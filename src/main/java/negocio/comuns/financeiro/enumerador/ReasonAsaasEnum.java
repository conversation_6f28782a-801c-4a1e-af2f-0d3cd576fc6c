package negocio.comuns.financeiro.enumerador;

/**
 * Created with Intel<PERSON><PERSON> IDEA.
 * User: <PERSON>
 * Date: 05/05/2023
 */
public enum ReasonAsaasEnum {

    NENHUMA(0, "ABSENCE_OF_PRINT"),
    ABSENT_CARD_FRAUD(1, "ABSENT_CARD_FRAUD"),
    CARD_ACTIVATED_PHONE_TRANSACTION(2, "CARD_ACTIVATED_PHONE_TRANSACTION"),
    CARD_FRAUD(3, "CARD_FRAUD"),
    CARD_RECOVERY_BULLETIN(4, "CARD_RECOVERY_BULLETIN"),
    COMMERCIAL_DISAGREEMENT(5, "COMMERCIAL_DISAGREEMENT"),
    COPY_NOT_RECEIVED(6, "COPY_NOT_RECEIVED"),
    CREDIT_OR_DEBIT_PRESENTATION_ERROR(7, "CREDIT_OR_DEBIT_PRESENTATION_ERROR"),
    DIFFERENT_PAY_METHOD(8, "DIFFERENT_PAY_METHOD"),
    FRAUD(9, "FRAUD"),
    INCORRECT_TRANSACTION_VALUE(10, "INCORRECT_TRANSACTION_VALUE"),
    INVALID_CURRENCY(11, "INVALID_CURRENCY"),
    INVALID_DATA(12, "INVALID_DATA"),
    LATE_PRESENTATION(13, "LATE_PRESENTATION"),
    LOCAL_REGULATORY_OR_LEGAL_DISPUTE(14, "LOCAL_REGULATORY_OR_LEGAL_DISPUTE"),
    MULTIPLE_ROCS(15, "MULTIPLE_ROCS"),
    ORIGINAL_CREDIT_TRANSACTION_NOT_ACCEPTED(16, "ORIGINAL_CREDIT_TRANSACTION_NOT_ACCEPTED"),
    OTHER_ABSENT_CARD_FRAUD(17, "OTHER_ABSENT_CARD_FRAUD"),
    PROCESS_ERROR(18, "PROCESS_ERROR"),
    RECEIVED_COPY_ILLEGIBLE_OR_INCOMPLETE(19, "RECEIVED_COPY_ILLEGIBLE_OR_INCOMPLETE"),
    RECURRENCE_CANCELED(20, "RECURRENCE_CANCELED"),
    REQUIRED_AUTHORIZATION_NOT_GRANTED(21, "REQUIRED_AUTHORIZATION_NOT_GRANTED"),
    RIGHT_OF_FULL_RECOURSE_FOR_FRAUD(22, "RIGHT_OF_FULL_RECOURSE_FOR_FRAUD"),
    SALE_CANCELED(23, "SALE_CANCELED"),
    SERVICE_DISAGREEMENT_OR_DEFECTIVE_PRODUCT(24, "SERVICE_DISAGREEMENT_OR_DEFECTIVE_PRODUCT"),
    SERVICE_NOT_RECEIVED(25, "SERVICE_NOT_RECEIVED"),
    SPLIT_SALE(26, "SPLIT_SALE"),
    TRANSFERS_OF_DIVERSE_RESPONSIBILITIES(27, "TRANSFERS_OF_DIVERSE_RESPONSIBILITIES"),
    UNQUALIFIED_CAR_RENTAL_DEBIT(28, "UNQUALIFIED_CAR_RENTAL_DEBIT"),
    USA_CARDHOLDER_DISPUTE(29, "USA_CARDHOLDER_DISPUTE"),
    VISA_FRAUD_MONITORING_PROGRAM(30, "VISA_FRAUD_MONITORING_PROGRAM"),
    WARNING_BULLETIN_FILE(31, "WARNING_BULLETIN_FILE"),
    ABSENCE_OF_PRINT(32, "ABSENCE_OF_PRINT"),
    ;

    private Integer codigo;
    private String descricao;

    ReasonAsaasEnum(Integer codigo, String descricao) {
        this.codigo = codigo;
        this.descricao = descricao;
    }

    public Integer getCodigo() {
        return codigo;
    }

    public String getDescricao() {
        return descricao;
    }

    public static ReasonAsaasEnum obterPorCodigo(Integer codigo) {
        for (ReasonAsaasEnum origem : ReasonAsaasEnum.values()) {
            if (origem.getCodigo().equals(codigo)) {
                return origem;
            }
        }
        return NENHUMA;
    }
}

/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package negocio.comuns.financeiro;

import annotations.arquitetura.ChaveEstrangeira;
import annotations.arquitetura.Lista;
import annotations.arquitetura.NaoControlarLogAlteracao;
import br.com.pactosolucoes.comuns.util.Formatador;
import br.com.pactosolucoes.comuns.util.StringUtilities;
import br.com.pactosolucoes.integracao.pactopay.dto.RemessaDTO;
import br.com.pactosolucoes.integracao.pactopay.dto.TransacaoDTO;
import br.com.pactosolucoes.oamd.controle.basico.DAO;
import negocio.comuns.arquitetura.SuperVO;
import negocio.comuns.arquitetura.UsuarioVO;
import negocio.comuns.basico.ConfiguracaoSistemaVO;
import negocio.comuns.basico.EmpresaVO;
import negocio.comuns.financeiro.enumerador.NomenclaturaArquivoEnum;
import negocio.comuns.financeiro.enumerador.SituacaoRemessaEnum;
import negocio.comuns.financeiro.enumerador.TipoCobrancaEnum;
import negocio.comuns.financeiro.enumerador.TipoConvenioCobrancaEnum;
import negocio.comuns.financeiro.enumerador.TipoRemessaEnum;
import negocio.comuns.utilitarias.Calendario;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisValidacao;
import negocio.facade.jdbc.basico.ConfiguracaoSistema;
import negocio.facade.jdbc.utilitarias.Conexao;
import org.jboleto.JBoleto;
import servicos.impl.apf.APF;
import servicos.impl.dcc.base.DCCAttEnum;
import servicos.impl.dcc.base.RegistroRemessa;
import servicos.impl.dcc.base.TipoRegistroEnum;

import java.math.BigDecimal;
import java.sql.Connection;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 *
 * <AUTHOR>
 */
public class RemessaVO extends SuperVO {

    private TipoRemessaEnum tipo = TipoRemessaEnum.DESCONHECIDO;
    private Date dataRegistro = Calendario.hoje();

    /**
     * Criado para mandar o histórico da data de registro, pois durante o processo de agrupar remessas houve a necessidade de alterar a data de registro.
     * para não perder a referencia da data original foi criado esse atributo "dataRegistroOriginal".
     * by Luiz Felipe 15/04/2020
     */
    private Date dataRegistroOriginal;

    private Date dataInicio;
    private Date dataFim;
    @NaoControlarLogAlteracao
    private List<TransacaoVO> listaTransacoes = new ArrayList<TransacaoVO>();
    @Lista
    private List<RemessaItemVO> listaItens = new ArrayList<RemessaItemVO>();
    private List<BoletoVO> listaItensBoleto;
    private StringBuilder head = new StringBuilder();
    private StringBuilder detail = new StringBuilder();
    private StringBuilder trailer = new StringBuilder();
    private StringBuilder retorno = new StringBuilder();
    @NaoControlarLogAlteracao
    private RegistroRemessa headerRemessa = new RegistroRemessa(TipoRegistroEnum.HEADER);
    @NaoControlarLogAlteracao
    private List<RegistroRemessa> detailsRemessa = new ArrayList<RegistroRemessa>();
    @NaoControlarLogAlteracao
    private RegistroRemessa trailerRemessa = new RegistroRemessa(TipoRegistroEnum.TRAILER);
    @NaoControlarLogAlteracao
    private RegistroRemessa headerRetorno = new RegistroRemessa(TipoRegistroEnum.HEADER);
    @NaoControlarLogAlteracao
    private List<RegistroRemessa> detailsRetorno = new ArrayList<RegistroRemessa>();
    @NaoControlarLogAlteracao
    private RegistroRemessa trailerRetorno = new RegistroRemessa(TipoRegistroEnum.TRAILER);
    private int empresa;
    @ChaveEstrangeira
    private ConvenioCobrancaVO convenioCobranca;
    private String identificador = "";
    private ConfiguracaoSistemaVO configuracaoSistema = new ConfiguracaoSistemaVO();
    private String nomeArquivo = "";
    @NaoControlarLogAlteracao
    private String resultadoProcessamentoRetorno;
    private SituacaoRemessaEnum situacaoRemessa = SituacaoRemessaEnum.NENHUMA;
    @ChaveEstrangeira
    private UsuarioVO usuario;
    @NaoControlarLogAlteracao
    private Double valorBruto = 0.0;
    @NaoControlarLogAlteracao
    private Double valorAceito = 0.0;
    @NaoControlarLogAlteracao
    private int qtdAceito = 0;
    @NaoControlarLogAlteracao
    private Double valorLiquido = 0.0;
    @NaoControlarLogAlteracao
    private Integer qtdRegistros = 0;
    @NaoControlarLogAlteracao
    private Date dataPrevistaCredito = null;
    private Map<String, String> props = new HashMap<String, String>();


    @NaoControlarLogAlteracao
    private Map<String, String> headMap = new HashMap<>();
    
    //PRO LAYOUT DO ITAU
    private StringBuilder headerArquivo = new StringBuilder();
    private StringBuilder trailerArquivo = new StringBuilder();
    @NaoControlarLogAlteracao
    private RegistroRemessa headerRemessaArquivo = new RegistroRemessa(TipoRegistroEnum.HEADER);
    @NaoControlarLogAlteracao
    private RegistroRemessa trailerRemessaArquivo = new RegistroRemessa(TipoRegistroEnum.TRAILER);
    @NaoControlarLogAlteracao
    private String nomeArquivoDownload;
    private boolean cancelamento = false;
    @Lista
    private List<RemessaCancelamentoItemVO> listaItensCancelamento = new ArrayList<RemessaCancelamentoItemVO>();

    private Date dataFechamento;
    @ChaveEstrangeira
    private UsuarioVO usuarioFechamento;
    private EmpresaVO empresaVO;
    private boolean arquivoUnico = false;
    private Integer sequencialArquivo;
    private Integer qtdItemDebito = 0;
    private Date dataEnvio;
    @NaoControlarLogAlteracao
    private List<ExtratoDiarioItemVO> listaExtratoDiarioItem;
    @NaoControlarLogAlteracao
    private String nomeArquivoEnvio;
    private boolean novoFormato = false;
    private String idPactoPay;
    @NaoControlarLogAlteracao
    private List<String> errosGeracao;

    public RemessaVO() {
    }

    public boolean isGetnet(){
        try {
            return this.getTipo().equals(TipoRemessaEnum.GET_NET);
        } catch (Exception e) {
            return false;
        }
    }

    public boolean isCielo(){
        try {
            return this.getTipo().equals(TipoRemessaEnum.EDI_CIELO);
        } catch (Exception e) {
            return false;
        }
    }

    public boolean getDCO(){
        try {
            return this.getTipo().equals(TipoRemessaEnum.BRADESCO_DCO) || this.getTipo().equals(TipoRemessaEnum.BB_DCO)
                    || this.getTipo().equals(TipoRemessaEnum.CAIXA_DCO) || this.getTipo().equals(TipoRemessaEnum.HSBC_DCO)
                    || this.getTipo().equals(TipoRemessaEnum.ITAU_DCO) || this.getTipo().equals(TipoRemessaEnum.SANTANDER_DCO)
                    || this.getTipo().equals(TipoRemessaEnum.CAIXA_SICOV_DCO);
        } catch (Exception e) {
            return false;
        }
    }
    public boolean isDCCBin(){
        try {
            return this.getTipo().equals(TipoRemessaEnum.DCC_BIN);
        } catch (Exception e) {
            return false;
        }
    }

    public void gerarIdentificador(Connection con) throws Exception {
        ConfiguracaoSistema configuracaoSistema = new ConfiguracaoSistema(con);
        boolean padraoTivit = configuracaoSistema.isNomeArquivoRemessaPadraoTivit();

        int id = (this.getTipo() == TipoRemessaEnum.BRADESCO_DCO || this.getTipo() == TipoRemessaEnum.ITAU_DCO)
                ? Conexao.obterUltimoCodigoGeradoTabela(con, "Remessa") + 1
                : (this.getTipo() == TipoRemessaEnum.GET_NET) ? (isCancelamento() ? getConvenioCobranca().getSequencialArquivoCancelamento() : getConvenioCobranca().getSequencialDoArquivo()) : this.empresa;
        if (isBoleto()) {
            if (isBoletoBradesco()) { //evitar duplicidade no nome do arquivo para bancos que são multiempresas
                int identificador = Conexao.obterUltimoCodigoGeradoTabela(con, "Remessa") + 1;
                id = Integer.valueOf(StringUtilities.formatarCampoForcandoZerosAEsquerda(identificador, 2)); //usar os 2 últimos dígitos do código da remessa, completar com zero a esquerda se necessário
            } else {
                id = getConvenioCobranca().getSequencialDoArquivo() % 100;
            }
        }
        this.identificador = this.tipo.getIdent(id, StringUtilities.formatarCampoForcandoZerosAEsquerda(getConvenioCobranca().getNumeroContrato(), 10), null);
        this.nomeArquivo = this.tipo.getNomeArquivo(id, StringUtilities.formatarCampoForcandoZerosAEsquerda(getConvenioCobranca().getNumeroContrato(), 10), null);

        if (this.getTipo().isDCO() && padraoTivit) {
            this.nomeArquivo = String.format("REM%s%s%s",
                    StringUtilities.formatarCampoForcandoZerosAEsquerda(getConvenioCobranca().getBanco().getCodigoBanco(), 3),
                    Calendario.getData("ddMMyyyy"),
                    StringUtilities.formatarCampoForcandoZerosAEsquerda(getSequencialArquivo(), 4));
        }

        if (isBoleto() && getConvenioCobranca().getBanco().getCodigoBanco().equals(JBoleto.SICREDI)) {
            //pegar mes, dia e colocar no arquivo
            int mes = Uteis.getMesData(getDataRegistro());
            int dia = Uteis.getDiaMesData(getDataRegistro());

            String mesUsar = "";
            if (mes < 10) {
                mesUsar += mes;
            } else if (mes == 10) {
                mesUsar = "O";
            } else if (mes == 11) {
                mesUsar = "N";
            } else if (mes == 12) {
                mesUsar = "D";
            }

            this.identificador = getConvenioCobranca().getNumeroContrato() + StringUtilities.formatarCampoForcandoZerosAEsquerda((id % 100), 2);
            this.nomeArquivo = StringUtilities.formatarCampoForcandoZerosAEsquerda(getConvenioCobranca().getNumeroContrato(),5) + mesUsar + StringUtilities.formatarCampoForcandoZerosAEsquerda(dia, 2);
        }

        if (isCancelamento()) {
            this.identificador = this.tipo.getIdentCancelamento(id, StringUtilities.formatarCampoForcandoZerosAEsquerda(getConvenioCobranca().getNumeroContrato(), 10));
            this.nomeArquivo = this.tipo.getNomeArquivoCancelamento(id, StringUtilities.formatarCampoForcandoZerosAEsquerda(getConvenioCobranca().getNumeroContrato(), 10));
        }

        if (!isCancelamento() && getConvenioCobranca().getTipo().equals(TipoConvenioCobrancaEnum.DCO_CAIXA) && !getConvenioCobranca().getNomenclaturaArquivo().equals(NomenclaturaArquivoEnum.NENHUM)) {

            String key = "";
            try {
                key = DAO.resolveKeyFromConnection(con);
            } catch (Exception ignored){}

            this.nomeArquivo = NomenclaturaArquivoEnum.obterNomeArquivo(this, key, getConvenioCobranca().getNomenclaturaArquivo());
        }
    }

    public void gerarIdentificador(Connection con, Integer sequencialArquivo) throws Exception {
        int id = (this.getTipo() == TipoRemessaEnum.BRADESCO_DCO || this.getTipo() == TipoRemessaEnum.ITAU_DCO)
                ? Conexao.obterUltimoCodigoGeradoTabela(con, "Remessa") + 1
                : (this.getTipo() == TipoRemessaEnum.GET_NET) ? (isCancelamento() ? getConvenioCobranca().getSequencialArquivoCancelamento() : sequencialArquivo) : this.empresa;
        if (isBoleto()) {
            id = sequencialArquivo % 100;
        }
        this.identificador = this.tipo.getIdent(id, StringUtilities.formatarCampoForcandoZerosAEsquerda(getConvenioCobranca().getNumeroContrato(), 10), null);
        this.nomeArquivo = this.tipo.getNomeArquivo(id, StringUtilities.formatarCampoForcandoZerosAEsquerda(getConvenioCobranca().getNumeroContrato(), 10), null);

        if (isBoleto() && getConvenioCobranca().getBanco().getCodigoBanco().equals(JBoleto.SICREDI)) {
            //pegar mes, dia e colocar no arquivo
            int mes = Uteis.getMesData(getDataRegistro());
            int dia = Uteis.getDiaMesData(getDataRegistro());

            String mesUsar = "";
            if (mes < 10) {
                mesUsar += mes;
            } else if (mes == 10) {
                mesUsar = "O";
            } else if (mes == 11) {
                mesUsar = "N";
            } else if (mes == 12) {
                mesUsar = "D";
            }

            this.identificador = getConvenioCobranca().getNumeroContrato() + StringUtilities.formatarCampoForcandoZerosAEsquerda((id % 100), 2);
            this.nomeArquivo = getConvenioCobranca().getNumeroContrato() + mesUsar + StringUtilities.formatarCampoForcandoZerosAEsquerda(dia, 2);
        }

        if (isCancelamento()) {
            this.identificador = this.tipo.getIdentCancelamento(id, StringUtilities.formatarCampoForcandoZerosAEsquerda(getConvenioCobranca().getNumeroContrato(), 10));
            this.nomeArquivo = this.tipo.getNomeArquivoCancelamento(id, StringUtilities.formatarCampoForcandoZerosAEsquerda(getConvenioCobranca().getNumeroContrato(), 10));
        }
    }
    

    public Date getDataRegistro() {
        return dataRegistro;
    }

    public void setDataRegistro(Date dataRegistro) {
        this.dataRegistro = dataRegistro;
    }

    public TipoRemessaEnum getTipo() {
        return tipo;
    }

    public void setTipo(TipoRemessaEnum tipo) {
        this.tipo = tipo;
    }

    public List<TransacaoVO> getListaTransacoes() {
        return listaTransacoes;
    }

    public void setListaTransacoes(List<TransacaoVO> listaTransacoes) {
        this.listaTransacoes = listaTransacoes;
    }

    public Date getDataFim() {
        return dataFim;
    }

    public void setDataFim(Date dataFim) {
        this.dataFim = dataFim;
    }

    public Date getDataInicio() {
        return dataInicio;
    }

    public void setDataInicio(Date dataInicio) {
        this.dataInicio = dataInicio;
    }

    public String getDataRegistro_Apresentar() {
        return Uteis.getData(dataRegistro);
    }

    public String getDataRegistroApresentar() {
        if (getDataRegistro() == null) {
            return "";
        } else {
            return Calendario.getDataAplicandoFormatacao(getDataRegistro(), "dd/MM/yyyy HH:mm:ss");
        }
    }

    public StringBuilder getDetail() {
        return detail;
    }

    public void setDetail(StringBuilder detail) {
        this.detail = detail;
    }

    public StringBuilder getHead() {
        return head;
    }

    public void setHead(StringBuilder head) {
        this.head = head;
    }

    public StringBuilder getRetorno() {
        return retorno;
    }

    public void setRetorno(StringBuilder retorno) {
        this.retorno = retorno;
    }

    public StringBuilder getTrailer() {
        return trailer;
    }

    public void setTrailer(StringBuilder trailer) {
        this.trailer = trailer;
    }

    public List<RegistroRemessa> getDetailsRemessa() {
        return detailsRemessa;
    }

    public void setDetailsRemessa(List<RegistroRemessa> detailsRemessa) {
        this.detailsRemessa = detailsRemessa;
    }

    public RegistroRemessa getHeaderRemessa() {
        return headerRemessa;
    }

    public void setHeaderRemessa(RegistroRemessa headerRemessa) {
        this.headerRemessa = headerRemessa;
    }

    public RegistroRemessa getTrailerRemessa() {
        return trailerRemessa;
    }

    public void setTrailerRemessa(RegistroRemessa trailerRemessa) {
        this.trailerRemessa = trailerRemessa;
    }

    public int getEmpresa() {
        return empresa;
    }

    public void setEmpresa(int empresa) {
        this.empresa = empresa;
    }

    public String getIdentificador() {
        return identificador;
    }

    public void setIdentificador(String identificador) {
        this.identificador = identificador;
    }

    public ConvenioCobrancaVO getConvenioCobranca() {
        if (convenioCobranca == null) {
            convenioCobranca = new ConvenioCobrancaVO();
        }
        return convenioCobranca;
    }

    public void setConvenioCobranca(ConvenioCobrancaVO convenioCobranca) {
        this.convenioCobranca = convenioCobranca;
    }

    public ConfiguracaoSistemaVO getConfiguracaoSistema() {
        return configuracaoSistema;
    }

    public void setConfiguracaoSistema(ConfiguracaoSistemaVO configuracaoSistema) {
        this.configuracaoSistema = configuracaoSistema;
    }

    public List<RemessaItemVO> getListaItens() {
        return listaItens;
    }

    public void setListaItens(List<RemessaItemVO> listaItens) {
        this.listaItens = listaItens;
    }

    public List<RegistroRemessa> getDetailsRetorno() {
        return detailsRetorno;
    }

    public void setDetailsRetorno(List<RegistroRemessa> detailsRetorno) {
        this.detailsRetorno = detailsRetorno;
    }

    public RegistroRemessa getHeaderRetorno() {
        return headerRetorno;
    }

    public void setHeaderRetorno(RegistroRemessa headerRetorno) {
        this.headerRetorno = headerRetorno;
    }

    public RegistroRemessa getTrailerRetorno() {
        return trailerRetorno;
    }

    public void setTrailerRetorno(RegistroRemessa trailerRetorno) {
        this.trailerRetorno = trailerRetorno;
    }

    public String getResultadoProcessamentoRetorno() {
        return resultadoProcessamentoRetorno;
    }

    public void setResultadoProcessamentoRetorno(String resultadoProcessamentoRetorno) {
        this.resultadoProcessamentoRetorno = resultadoProcessamentoRetorno;
    }

    public SituacaoRemessaEnum getSituacaoRemessa() {
        return situacaoRemessa;
    }

    public void setSituacaoRemessa(SituacaoRemessaEnum situacaoRemessa) {
        this.situacaoRemessa = situacaoRemessa;
    }

    public UsuarioVO getUsuario() {
        if (usuario == null) {
            usuario = new UsuarioVO();
        }
        return usuario;
    }

    public void setUsuario(UsuarioVO usuario) {
        this.usuario = usuario;
    }

    public Date getDataPrevistaCredito() {
        return dataPrevistaCredito;
    }

    public void setDataPrevistaCredito(Date dataPrevistaCredito) {
        this.dataPrevistaCredito = dataPrevistaCredito;
    }

    public Double getValorAceito() {
        return valorAceito;
    }

    public void setValorAceito(Double valorAceito) {
        this.valorAceito = valorAceito;
    }

    public Double getValorBruto() {
        return valorBruto;
    }

    public void setValorBruto(Double valorBruto) {
        this.valorBruto = valorBruto;
    }

    public Double getValorLiquido() {
        return valorLiquido;
    }

    public void setValorLiquido(Double valorLiquido) {
        this.valorLiquido = valorLiquido;
    }

    public Integer getQtdRegistrosTela() {
        if (this.getConvenioCobranca().getTipo().equals(TipoConvenioCobrancaEnum.DCO_CAIXA)) {
            return this.getQtdItemDebito();
        } else {
            return this.getQtdRegistros();
        }
    }

    public Integer getQtdRegistros() {
        return qtdRegistros;
    }

    public void setQtdRegistros(Integer qtdRegistros) {
        this.qtdRegistros = qtdRegistros;
    }

    public String getValorBruto_Apresentar() {
        return Formatador.formatarValorMonetarioSemMoeda(valorBruto);
    }

    public String getValorAceito_Apresentar() {
        return Formatador.formatarValorMonetarioSemMoeda(valorAceito);
    }

    public String getValorLiquido_Apresentar() {
        return Formatador.formatarValorMonetarioSemMoeda(valorLiquido);
    }

    public boolean isTemRetorno() {
        return this.getRetorno().length() > 0;
    }

    public int getQtdAceito() {
        return qtdAceito;
    }

    public void setQtdAceito(int qtdAceito) {
        this.qtdAceito = qtdAceito;
    }

    public Map<String, String> getProps() {
        return props;
    }

    public void setProps(Map<String, String> props) {
        this.props = props;
    }

    public String getUsuarioRetorno() {
        return this.getProps().get(DCCAttEnum.NomeUsuarioRetorno.name());
    }

    public String getUsuarioRetornoTitle() {
        try {
            StringBuilder title = new StringBuilder();
            if (!UteisValidacao.emptyString(this.getUsuarioRetorno())) {
                title.append("Usuario: <b>").append(this.getUsuarioRetorno()).append("</b><br/>");
            }
            if (this.getDataRetorno() != null) {
                title.append("Data de Retorno: <b>").append(Uteis.getDataComHHMM(this.getDataRetorno())).append("</b><br/>");
            }
            return title.toString();
        } catch (Exception ex) {
            return "";
        }
    }

    public Date getDataRetorno() {
        String d = this.getProps().get(DCCAttEnum.DataHoraRetorno.name());
        if (d != null && !d.isEmpty()) {
            return new Date(Long.valueOf(d));
        } else {
            return null;
        }
    }

    @Override
    public String toString() {
        return this.identificador;
    }

    public static void validarDados(RemessaVO obj) throws Exception {

        if (obj.tipo == TipoRemessaEnum.DESCONHECIDO) {
            throw new Exception("A REMESSA precisa possuir um TipoRemessaEnum");
        }
        if (obj.getTipo().equals(TipoRemessaEnum.APROVA_FACIL) && obj.getListaTransacoes().isEmpty()) {
            throw new Exception("A REMESSA precisa possuir uma transação ou mais relacionada");
        }
        if (obj.getTipo().equals(TipoRemessaEnum.EDI_CIELO) && obj.getListaItens().isEmpty() && !obj.isCancelamento()) {
            throw new Exception("A REMESSA precisa possuir uma parcela ou mais relacionada");
        }

        if (obj.getTipo().equals(TipoRemessaEnum.EDI_CIELO) && obj.getListaItensCancelamento().isEmpty() && obj.isCancelamento()) {
            throw new Exception("A REMESSA precisa possuir um item ou mais relacionado");
        }

        if (obj.getTipo().equals(TipoRemessaEnum.EDI_CIELO) && obj.getConvenioCobranca().getCodigo() == 0) {
            throw new Exception("A REMESSA precisa possuir um Convênio de Cobrança relacionado.");
        }
        if (obj.getTipo().equals(TipoRemessaEnum.EDI_CIELO) && obj.getEmpresa() == 0) {
            throw new Exception("A REMESSA precisa possuir uma Empresa relacionada.");
        }

    }
    public void setSequencialArquivo(Integer sequencialArquivo) {
        this.sequencialArquivo = sequencialArquivo;
    }

    public String getSequencialArquivo() {
        String numero = this.getProps().get(DCCAttEnum.NumeroResumoOperacoes.name());
        return numero != null && !numero.isEmpty() ? numero : this.getConvenioCobranca().getSequencialDoArquivo().toString();
    }

    public String getSequencialArquivoExibicao(){
        String numero = null;
        if(getTipo() != null && getTipo().equals(TipoRemessaEnum.DCC_BIN)){
            numero = getCodigo().toString();
        }else{
            if(getTipo() != null && getTipo().equals(TipoRemessaEnum.GET_NET)){
                numero = getPropriedadePropsEHead(DCCAttEnum.SequencialRegistro.name());
                if(!UteisValidacao.emptyString(numero)){
                    numero = Integer.valueOf(numero).toString();
                }
            }else{
                numero = getPropriedadePropsEHead(DCCAttEnum.NumeroResumoOperacoes.name());
                if(UteisValidacao.emptyString(numero) &&
                        getTipo() != null && getTipo().equals(TipoRemessaEnum.EDI_CIELO)){
                    numero = getPropriedadePropsEHeadCielo(DCCAttEnum.NumeroResumoOperacoes.name());
                }
                if(UteisValidacao.emptyString(numero)){
                    numero = getPropriedadePropsEHead(DCCAttEnum.SequencialRegistro.name());
                    if(!UteisValidacao.emptyString(numero)){
                        numero = Integer.valueOf(numero).toString();
                    }
                }else{
                    numero = Integer.valueOf(numero).toString();
                }
            }
        }
        return numero;
    }

    private String getPropriedadePropsEHead(String propriedade){
        String prop = this.getProps().get(propriedade);
        if(UteisValidacao.emptyString(prop) && this.head != null){
            prop = this.headerRemessa.getValue(propriedade);
        }
        return prop;
    }

    private String getPropriedadePropsEHeadCielo(String propriedade){
        String prop = this.getProps().get(propriedade);
        if(UteisValidacao.emptyString(prop) && this.head != null){
            prop = this.getHeadMap().get(propriedade);
        }
        return prop;
    }

    public boolean isAguardandoRetorno() {
        return this.situacaoRemessa == SituacaoRemessaEnum.GERADA || this.situacaoRemessa == SituacaoRemessaEnum.REMESSA_ENVIADA;
    }

    public StringBuilder getHeaderArquivo() {
        if(headerArquivo == null){
            headerArquivo = new StringBuilder();
        }
        return headerArquivo;
    }

    public void setHeaderArquivo(StringBuilder headerArquivo) {
        this.headerArquivo = headerArquivo;
    }

    public StringBuilder getTrailerArquivo() {
        if(trailerArquivo == null){
            trailerArquivo = new StringBuilder();
        }
        return trailerArquivo;
    }

    public void setTrailerArquivo(StringBuilder trailerArquivo) {
        this.trailerArquivo = trailerArquivo;
    }

    public RegistroRemessa getHeaderRemessaArquivo() {
        return headerRemessaArquivo;
    }

    public void setHeaderRemessaArquivo(RegistroRemessa headerRemessaArquivo) {
        this.headerRemessaArquivo = headerRemessaArquivo;
    }

    public RegistroRemessa getTrailerRemessaArquivo() {
        return trailerRemessaArquivo;
    }

    public void setTrailerRemessaArquivo(RegistroRemessa trailerRemessaArquivo) {
        this.trailerRemessaArquivo = trailerRemessaArquivo;
    }

    public String getNomeArquivo() {
        if (nomeArquivo == null) {
            nomeArquivo = "";
        }
        return nomeArquivo;
    }

    public void setNomeArquivo(String nomearquivo) {
        this.nomeArquivo = nomearquivo;
    }

    public String getNomeArquivoDownload() {
        if (nomeArquivoDownload == null) {
            processarNomeArquivoDownload();
        }
        return nomeArquivoDownload;
    }

    public void setNomeArquivoDownload(String nomeArquivoDownload) {
        this.nomeArquivoDownload = nomeArquivoDownload;
    }

    public void processarNomeArquivoDownload() {
        if (!isCancelamento() &&
                getConvenioCobranca().getTipo().equals(TipoConvenioCobrancaEnum.DCO_CAIXA) &&
                !getConvenioCobranca().getNomenclaturaArquivo().equals(NomenclaturaArquivoEnum.NENHUM)) {

            setNomeArquivoDownload(getNomeArquivo() + getConvenioCobranca().getExtensaoArquivoRemessa());


        } else if (getConvenioCobranca().getTipo().equals(TipoConvenioCobrancaEnum.DCO_CAIXA)) {
            String numeroResumoOperacoes = "";
            RegistroRemessa headerRemessa = getHeaderRemessa();
            for (ObjetoGenerico objH : headerRemessa.getAtributos()) {
                if (objH.getAtributo().equals(DCCAttEnum.NumeroResumoOperacoes.name())) {
                    numeroResumoOperacoes = StringUtilities.formatarCampo(new BigDecimal(objH.getValor()), 6);
                }
            }
            numeroResumoOperacoes = UteisValidacao.emptyString(numeroResumoOperacoes)
                    ? StringUtilities.formatarCampo(new BigDecimal(getConvenioCobranca().getSequencialDoArquivo()), 6)
                    : numeroResumoOperacoes;

            setNomeArquivoDownload("ACC." + StringUtilities.formatarCampoData(getDataRegistro(), "ddMMyyyy")
                    + "." + StringUtilities.formatarCampoEmBranco(getConvenioCobranca().getNumeroContrato(), 6)
                    + "." + StringUtilities.formatarCampoEmBranco(numeroResumoOperacoes, 6)
                    + getConvenioCobranca().getExtensaoArquivoRemessa());
        }else if (getConvenioCobranca().getTipo().equals(TipoConvenioCobrancaEnum.DCC_GETNET)) {
            setNomeArquivoDownload(getNomeArquivo() + getConvenioCobranca().getExtensaoArquivoRemessa()+".gpg");
        }else {
            setNomeArquivoDownload(getNomeArquivo() + getConvenioCobranca().getExtensaoArquivoRemessa());
        }
    }

    public Integer getQtdItensPrimeiraTentativa() {
        int qtdItens = 0;
        for (RemessaItemVO item : getListaItens()) {
            if (item.getNrTentativaParcela() == 1) {
                qtdItens++;
            }
        }
        return qtdItens;
    }

    private double getValorItensPrimeiraTentativa() {
        double valorItens = 0;
        for (RemessaItemVO item : getListaItens()) {
            if (item.getNrTentativaParcela() == 1) {
                valorItens += item.getValorItemRemessa();
            }
        }
        return valorItens;
    }

    public String getValorItensPrimeiraTentativa_Apresentar() {
        double valorItens = getValorItensPrimeiraTentativa();
        return Formatador.formatarValorMonetarioSemMoeda(valorItens);
    }

    public Integer getQtdItensAceitosPrimeiraTentativa() {
        int qtdItens = 0;
        for (RemessaItemVO item : getListaItens()) {
            if (item.getNrTentativaParcela() == 1 && item.getMovPagamento().getCodigo() != 0) {
                qtdItens++;
            }
        }
        return qtdItens;
    }

    private double getValorItensAceitosPrimeiraTentativa() {
        double valorItens = 0;
        for (RemessaItemVO item : getListaItens()) {
            if (item.getNrTentativaParcela() == 1 && item.getMovPagamento().getCodigo() != 0) {
                valorItens += item.getValorItemRemessa();
            }
        }
        return valorItens;
    }

    public String getValorItensAceitosPrimeiraTentativa_Apresentar() {
        double valorItens = getValorItensAceitosPrimeiraTentativa();
        return Formatador.formatarValorMonetarioSemMoeda(valorItens);
    }

    public Integer getQtdItensNaoAceitosPrimeiraTentativa() {
        return getQtdItensPrimeiraTentativa() - getQtdItensAceitosPrimeiraTentativa();
    }

    public String getValorItensNaoAceitosPrimeiraTentativa_Apresentar() {
        double valorItens = getValorItensPrimeiraTentativa() - getValorItensAceitosPrimeiraTentativa();
        return Formatador.formatarValorMonetarioSemMoeda(valorItens);
    }

    public String getEficienciaPrimeiraTentativa() {
        double qtdAceito = (double) getQtdItensAceitosPrimeiraTentativa();
        double qtdTotal = (double) getQtdItensPrimeiraTentativa();
        if (qtdTotal > 0) {
            double percentual = (qtdAceito / qtdTotal) * 100;
            return Formatador.formatarValorMonetarioSemMoeda(percentual);
        } else {
            return Formatador.formatarValorMonetarioSemMoeda(0.0);
        }
    }

    public Integer getQtdItensRepescagem() {
        int qtdItens = 0;
        for (RemessaItemVO item : getListaItens()) {
            if (item.getNrTentativaParcela() != 1) {
                qtdItens++;
            }
        }
        return qtdItens;
    }

    private double getValorItensRepescagem() {
        double valorItens = 0;
        for (RemessaItemVO item : getListaItens()) {
            if (item.getNrTentativaParcela() != 1) {
                valorItens += item.getValorItemRemessa();
            }
        }
        return valorItens;
    }

    public String getValorItensRepescagem_Apresentar() {
        double valorItens = getValorItensRepescagem();
        return Formatador.formatarValorMonetarioSemMoeda(valorItens);
    }

    public Integer getQtdItensAceitosRepescagem() {
        int qtdItens = 0;
        for (RemessaItemVO item : getListaItens()) {
            if (item.getNrTentativaParcela() != 1 && item.getMovPagamento().getCodigo() != 0) {
                qtdItens++;
            }
        }
        return qtdItens;
    }

    private double getValorItensAceitosRepescagem() {
        double valorItens = 0;
        for (RemessaItemVO item : getListaItens()) {
            if (item.getNrTentativaParcela() != 1 && item.getMovPagamento().getCodigo() != 0) {
                valorItens += item.getValorItemRemessa();
            }
        }
        return valorItens;
    }

    public String getValorItensAceitosRepescagem_Apresentar() {
        double valorItens = getValorItensAceitosRepescagem();
        return Formatador.formatarValorMonetarioSemMoeda(valorItens);
    }

    public Integer getQtdItensNaoAceitosRepescagem() {
        return getQtdItensRepescagem() - getQtdItensAceitosRepescagem();
    }

    public String getValorItensNaoAceitosRepescagem_Apresentar() {
        double valorItens = getValorItensRepescagem() - getValorItensAceitosRepescagem();
        return Formatador.formatarValorMonetarioSemMoeda(valorItens);
    }

    public String getEficienciaRepescagem() {
        double qtdAceito = (double) getQtdItensAceitosRepescagem();
        double qtdTotal = (double) getQtdItensRepescagem();
        if (qtdTotal > 0) {
            double percentual = (qtdAceito / qtdTotal) * 100;
            return Formatador.formatarValorMonetarioSemMoeda(percentual);
        } else {
            return Formatador.formatarValorMonetarioSemMoeda(0.0);
        }
    }

    public boolean isBoleto(){
        try {
            return this.getTipo() != null && this.getTipo().equals(TipoRemessaEnum.ITAU_BOLETO)
                    || this.getTipo().equals(TipoRemessaEnum.DAYCOVAL_BOLETO)
                    || this.getTipo().equals(TipoRemessaEnum.BOLETO)
                    || this.getTipo().equals(TipoRemessaEnum.ITAU_CNAB400);
        } catch (Exception e) {
            return false;
        }
    }

    public boolean isCancelamento() {
        return cancelamento;
    }

    public void setCancelamento(boolean cancelamento) {
        this.cancelamento = cancelamento;
    }

    public String getClassRemessa() {
        return (isCancelamento()) ? "remessa-cancelamento" : "remessa-padrao";
    }

    public List<RemessaCancelamentoItemVO> getListaItensCancelamento() {
        return listaItensCancelamento;
    }

    public void setListaItensCancelamento(List<RemessaCancelamentoItemVO> listaItensCancelamento) {
        this.listaItensCancelamento = listaItensCancelamento;
    }

    public Date getDataFechamento() {
        return dataFechamento;
}

    public void setDataFechamento(Date dataFechamento) {
        this.dataFechamento = dataFechamento;
    }

    public UsuarioVO getUsuarioFechamento() {
        if (usuarioFechamento == null) {
            usuarioFechamento = new UsuarioVO();
        }
        return usuarioFechamento;
    }

    public void setUsuarioFechamento(UsuarioVO usuarioFechamento) {
        this.usuarioFechamento = usuarioFechamento;
    }

    public String getDescricaoCodigoRetornoRemessa() {
        if (TipoRemessaEnum.DCC_BIN.equals(getTipo())) {
            return this.getProps().get(DCCAttEnum.CodigoRetorno.name());
        }
        return "";
    }

    public EmpresaVO getEmpresaVO() {
        if (empresaVO == null) {
            empresaVO = new EmpresaVO();
        }
        return empresaVO;
    }

    public void setEmpresaVO(EmpresaVO empresaVO) {
        this.empresaVO = empresaVO;
    }

    public boolean isArquivoUnico() {
        return arquivoUnico;
    }

    public void setArquivoUnico(boolean arquivoUnico) {
        this.arquivoUnico = arquivoUnico;
    }

    public String getEmpresa_Apresentar() {
        if (isArquivoUnico()) {
            return "ARQUIVO ÚNICO";
        } else {
            return getEmpresaVO().getNome();
        }
    }

    public Integer getQtdItemDebito() {
        return qtdItemDebito;
    }

    public void setQtdItemDebito(Integer qtdItemDebito) {
        this.qtdItemDebito = qtdItemDebito;
    }

    public String getDataRegistroTitle() {
        StringBuilder title = new StringBuilder();
        title.append("Data de Geração: <b>").append(getDataRegistroApresentar()).append("</b>");
        if (getDataRegistroOriginal() != null && !Calendario.igualComHora(getDataRegistro(), getDataRegistroOriginal())) {
            title.append("<br/>Data de Geração: <b>").append(getDataRegistroOriginalApresentar()).append("</b> - Original");
        }
        if (!UteisValidacao.emptyString(getDataEnvioApresentar())) {
            title.append("<br/>Data de Envio: <b>").append(getDataEnvioApresentar()).append("</b>");
        }
        if (!UteisValidacao.emptyString(getIdPactoPay())) {
            title.append("<br/>Id PactoPay: <b>").append(getIdPactoPay()).append("</b>");
        }
        return title.toString();
    }

    public String getInformacoesTitle() {
        try {
            StringBuilder title = new StringBuilder();
            if (!UteisValidacao.emptyString(this.getEmpresaVO().getNome())) {
                title.append("Empresa: <b>").append(this.getEmpresaVO().getNome()).append("</b><br/>");
            }
            if (!UteisValidacao.emptyString(this.getConvenioCobranca().getDescricao())) {
                title.append("Convênio: <b>").append(this.getConvenioCobranca().getDescricao()).append("</b> (Código ").append(this.getConvenioCobranca().getCodigo()).append(")<br/>");
            }
            if (this.getSituacaoRemessa() != null) {
                title.append("Situação: <b>").append(this.getSituacaoRemessa().getDescricao()).append("</b><br/>");
            }
            if (this.getDataRetorno() != null) {
                title.append("Data de Retorno: <b>").append(Uteis.getDataComHHMM(this.getDataRetorno())).append("</b><br/>");
            }
            return title.toString();
        } catch (Exception ex) {
            return "";
        }
    }

    public String getDataEnvioApresentar() {
        if (getDataEnvio() == null) {
            return "";
        } else {
            return Calendario.getDataAplicandoFormatacao(getDataEnvio(), "dd/MM/yyyy HH:mm:ss");
        }
    }

    public Date getDataEnvio() {
        return dataEnvio;
    }

    public void setDataEnvio(Date dataEnvio) {
        this.dataEnvio = dataEnvio;
    }

    public String getDataRegistroOriginalApresentar() {
        if (getDataRegistroOriginal() == null) {
            return "";
        } else {
            return Calendario.getDataAplicandoFormatacao(getDataRegistroOriginal(), "dd/MM/yyyy HH:mm:ss");
        }
    }

    public Date getDataRegistroOriginal() {
        return dataRegistroOriginal;
    }

    public void setDataRegistroOriginal(Date dataRegistroOriginal) {
        this.dataRegistroOriginal = dataRegistroOriginal;
    }

    public List<ExtratoDiarioItemVO> getListaExtratoDiarioItem() {
        if (listaExtratoDiarioItem == null) {
            listaExtratoDiarioItem = new ArrayList<>();
        }
        return listaExtratoDiarioItem;
    }

    public void setListaExtratoDiarioItem(List<ExtratoDiarioItemVO> listaExtratoDiarioItem) {
        this.listaExtratoDiarioItem = listaExtratoDiarioItem;
    }

    public String getNomeArquivoEnvio() {
        if (nomeArquivoEnvio == null) {
            nomeArquivoEnvio = "";
        }
        return nomeArquivoEnvio;
    }

    public void setNomeArquivoEnvio(String nomeArquivoEnvio) {
        this.nomeArquivoEnvio = nomeArquivoEnvio;
    }

    public boolean isNovoFormato() {
        return novoFormato;
    }

    public void setNovoFormato(boolean novoFormato) {
        this.novoFormato = novoFormato;
    }

    public RemessaDTO toRemessaDTO(String chave) {
        RemessaDTO remessaDTO = new RemessaDTO();
        remessaDTO.setChave(chave);
        remessaDTO.setAsync(true);
        remessaDTO.setIdReferencia(this.getCodigo());
        remessaDTO.setSequencial(Integer.parseInt(this.getSequencialArquivoExibicao()));
        remessaDTO.setResponsavel(this.getUsuario().getNome());
        remessaDTO.setDataRegistro(Calendario.getDataAplicandoFormatacao(this.getDataRegistro(), "yyyyMMddHHmmss"));

        remessaDTO.setConvenio(this.getConvenioCobranca().toConvenioDTO());
        remessaDTO.setEmpresa(this.getEmpresaVO().toEmpresaDTO());

        remessaDTO.setTransacoes(new ArrayList<>());
        for (RemessaItemVO itemVO : this.getListaItens()) {
            remessaDTO.getTransacoes().add(itemVO.toTransacaoDTO(this.getConvenioCobranca()));
        }

        return remessaDTO;
    }

    public String getIdPactoPay() {
        if (idPactoPay == null) {
            idPactoPay = "";
        }
        return idPactoPay;
    }

    public void setIdPactoPay(String idPactoPay) {
        this.idPactoPay = idPactoPay;
    }

    public Map<String, String> getHeadMap() {
        return headMap;
    }

    public void setHeadMap(Map<String, String> headMap) {
        this.headMap = headMap;
    }

    public List<BoletoVO> getListaItensBoleto() {
        if (listaItensBoleto == null) {
            listaItensBoleto = new ArrayList<>();
        }
        return listaItensBoleto;
    }

    public void setListaItensBoleto(List<BoletoVO> listaItensBoleto) {
        this.listaItensBoleto = listaItensBoleto;
    }

    public List<String> getErrosGeracao() {
        if (errosGeracao == null) {
            errosGeracao = new ArrayList<>();
        }
        return errosGeracao;
    }

    public void setErrosGeracao(List<String> errosGeracao) {
        this.errosGeracao = errosGeracao;
    }

    public boolean isBoletoBradesco() {
        return getConvenioCobranca() != null && getConvenioCobranca().getBanco() != null &&
                !UteisValidacao.emptyNumber(getConvenioCobranca().getBanco().getCodigo()) && getConvenioCobranca().getBanco().getCodigoBanco().equals(JBoleto.BRADESCO);
    }
}

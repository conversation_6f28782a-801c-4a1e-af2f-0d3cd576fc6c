package negocio.comuns.financeiro;

import annotations.arquitetura.ChavePrimaria;
import negocio.comuns.arquitetura.SuperVO;
import negocio.comuns.utilitarias.UteisValidacao;

import java.util.Date;

/**
 * Created by <PERSON> on 08/11/2024.
 */

public class KobanaWebhookVO extends SuperVO {

    @ChavePrimaria
    protected Integer codigo;
    protected Date dataRegistro;
    protected String dados;
    protected String event_code;
    protected boolean processado;
    protected Date dataProcessamento;
    protected String uidLoteKobanaItem;
    protected Integer codLoteKobanaItem;
    protected String uidLoteKobana;
    protected Integer codLoteKobana;
    protected MovContaVO movContaVO;

    @Override
    public Integer getCodigo() {
        return codigo;
    }

    @Override
    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public Date getDataRegistro() {
        return dataRegistro;
    }

    public void setDataRegistro(Date dataRegistro) {
        this.dataRegistro = dataRegistro;
    }

    public String getDados() {
        return dados;
    }

    public void setDados(String dados) {
        this.dados = dados;
    }

    public String getEvent_code() {
        return event_code;
    }

    public void setEvent_code(String event_code) {
        this.event_code = event_code;
    }

    public boolean isProcessado() {
        return processado;
    }

    public void setProcessado(boolean processado) {
        this.processado = processado;
    }

    public Date getDataProcessamento() {
        return dataProcessamento;
    }

    public void setDataProcessamento(Date dataProcessamento) {
        this.dataProcessamento = dataProcessamento;
    }

    public String getUidLoteKobanaItem() {
        return uidLoteKobanaItem;
    }

    public void setUidLoteKobanaItem(String uidLoteKobanaItem) {
        this.uidLoteKobanaItem = uidLoteKobanaItem;
    }

    public Integer getCodLoteKobanaItem() {
        return codLoteKobanaItem;
    }

    public void setCodLoteKobanaItem(Integer codLoteKobanaItem) {
        this.codLoteKobanaItem = codLoteKobanaItem;
    }

    public String getUidLoteKobana() {
        return uidLoteKobana;
    }

    public void setUidLoteKobana(String uidLoteKobana) {
        this.uidLoteKobana = uidLoteKobana;
    }

    public Integer getCodLoteKobana() {
        return codLoteKobana;
    }

    public void setCodLoteKobana(Integer codLoteKobana) {
        this.codLoteKobana = codLoteKobana;
    }

    public MovContaVO getMovContaVO() {
        if (movContaVO == null) {
            movContaVO = new MovContaVO();
        }
        return movContaVO;
    }

    public void setMovContaVO(MovContaVO movContaVO) {
        this.movContaVO = movContaVO;
    }
}

package negocio.comuns.financeiro;

import br.com.pactosolucoes.comuns.util.Formatador;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisValidacao;
import org.json.JSONObject;

import java.io.Serializable;
import java.math.BigDecimal;

public class BIAcessoGymPassTO implements Serializable{

    private Integer dia;
    private Integer qtdAcessos;

    public BIAcessoGymPassTO(Integer dia, Integer qtdAcessos) {
        this.dia = dia;
        this.qtdAcessos = qtdAcessos;
    }

    public Integer getDia() {
        return dia;
    }

    public void setDia(Integer dia) {
        this.dia = dia;
    }

    public Integer getQtdAcessos() {
        return qtdAcessos;
    }

    public void setQtdAcessos(Integer qtdAcessos) {
        this.qtdAcessos = qtdAcessos;
    }
}

/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package negocio.comuns.financeiro;

import java.util.Date;
import negocio.comuns.arquitetura.SuperVO;
import negocio.comuns.arquitetura.UsuarioVO;
import negocio.comuns.basico.EmpresaVO;
import negocio.comuns.utilitarias.Calendario;
import negocio.comuns.utilitarias.ConsistirException;

/**
 *
 * <AUTHOR>
 */
public class BloqueioCaixaVO extends SuperVO {

    private UsuarioVO usuarioResponsavel;
    private EmpresaVO empresa;
    private Date dataBloqueio;
    private Date lancamento;
    private String userOamd;

    public UsuarioVO getUsuarioResponsavel() {
        return usuarioResponsavel;
    }

    public void setUsuarioResponsavel(UsuarioVO usuarioResponsavel) {
        this.usuarioResponsavel = usuarioResponsavel;
    }

    public EmpresaVO getEmpresa() {
        return empresa;
    }

    public void setEmpresa(EmpresaVO empresa) {
        this.empresa = empresa;
    }

    public Date getDataBloqueio() {
        return dataBloqueio;
    }

    public void setDataBloqueio(Date dataBloqueio) {
        this.dataBloqueio = dataBloqueio;
    }

    public Date getLancamento() {
        return lancamento;
    }

    public void setLancamento(Date lancamento) {
        this.lancamento = lancamento;
    }

    public static void validarDados(BloqueioCaixaVO obj) throws ConsistirException {
        if (obj.getUsuarioResponsavel().getCodigo() == null || obj.getUsuarioResponsavel().getCodigo() == 0) {
            throw new ConsistirException("O campo USUÁRIO RESPONSÁVEL deve ser informado.");
        }
        if (obj.getEmpresa().getCodigo() == null || obj.getEmpresa().getCodigo() == 0) {
            throw new ConsistirException("O campo EMPRESA deve ser informado.");
        }
        if (obj.getDataBloqueio() == null) {
            throw new ConsistirException("A data do bloqueio deve ser informada.");
        }
        if (obj.getLancamento() == null) {
            throw new ConsistirException("A data do lançamento deve ser informada.");
        }
        if(Calendario.maior(obj.getDataBloqueio(), Calendario.hoje())){
            throw new ConsistirException("A data do bloqueio não pode ser uma data futura.");
        }
    }

    public String getUserOamd() {
        return userOamd;
    }

    public void setUserOamd(String userOamd) {
        this.userOamd = userOamd;
    }
    
    

}

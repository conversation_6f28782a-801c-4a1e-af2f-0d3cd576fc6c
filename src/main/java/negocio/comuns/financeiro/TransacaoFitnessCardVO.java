package negocio.comuns.financeiro;

import negocio.comuns.financeiro.enumerador.OperadorasExternasAprovaFacilEnum;
import negocio.comuns.financeiro.enumerador.TipoTransacaoEnum;
import negocio.comuns.utilitarias.UteisValidacao;
import org.json.JSONObject;
import servicos.impl.apf.APF;
import servicos.impl.cieloecommerce.CieloECommerceRetornoEnum;

import java.util.logging.Level;
import java.util.logging.Logger;

/*
 * Created by <PERSON><PERSON>
 */
public class TransacaoFitnessCardVO extends TransacaoVO {

    public String getValorCodigoExterno() throws Exception {
        return getCodigoExterno();
    }

    public String getValorCartaoMascarado() throws Exception {
        JSONObject obj = new JSONObject(getParamsEnvio());
        return obj.getString("numCartao");
    }

    public String getValorUltimaTransacaoAprovada() throws Exception {
        return getValorAtributoResposta(APF.Transacao);
    }

    public String getResultadoRequisicao() throws Exception {
        JSONObject json = new JSONObject(getParamsResposta());
        return json.getString("return");
    }

    public String getAutorizacao() {
        try {
            JSONObject obj = new JSONObject(getParamsResposta());
            return obj.getString("codAuto");
        } catch (Exception ex) {
            return "";
        }
    }

    public String getPaymentId() {
        try {
            JSONObject obj = new JSONObject(getParamsResposta());
            return obj.getString("identificadorAuto");
        } catch (Exception ex) {
            return "";
        }
    }

    public String getBandeira() {
        return OperadorasExternasAprovaFacilEnum.COM_VC.getDescricao();
    }

    public String getCartaoMascarado() {
        try {
            return getValorCartaoMascarado();
        } catch (Exception e) {
            return "";
        }
    }

    public String getValorAtributoResposta(String nomeAtributo) throws Exception {
        String valor = "";
        if (nomeAtributo.equals(APF.Transacao)) {
            return getPaymentId();
        }
        return valor;
    }

    public String getValorAtributoCancelamento(String nomeAtributo) throws Exception {
        String valor = "";
        if (!UteisValidacao.emptyString(getResultadoCancelamento())) {
            try {
                if (APF.ResultSolicCancel.equalsIgnoreCase(nomeAtributo)) {
                    valor = new JSONObject(getResultadoCancelamento()).getJSONObject("last_transaction").getString("gateway_message");
                }
            } catch (Exception e) {
            }
        }
        return valor;
    }

    public String getCodErroExterno() {
        try {
            String codigoRetorno = getReturnCode();
            if (!codigoRetorno.equals("04")) { //somente se não for autorizado retorna o erro.
                return codigoRetorno;
            }
        } catch (Exception ex) {
        }
        return "0";
    }

    public String getReturnCode() {
        try {
            JSONObject json = new JSONObject(getParamsResposta());
            return json.getString("retorno");
        } catch (Exception ex) {
            return "";
        }
    }
}

package negocio.comuns.financeiro;

import java.math.BigDecimal;

class ItemFaturamentoTOCreditoAte6X extends ItemFaturamentoTO {
    private final BigDecimal taxa = new BigDecimal("1.8");

    @Override
    public BigDecimal getTaxaSTONE() {
        return taxa;
    }

    @Override
    public String getFormaPagamento() {
        return "CRÉDITO ATÉ 6X";
    }

    @Override
    public int getOrdem() {
        return 2;
    }
}

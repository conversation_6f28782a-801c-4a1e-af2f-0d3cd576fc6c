package negocio.comuns.financeiro;

import annotations.arquitetura.ChaveEstrangeira;
import annotations.arquitetura.NaoControlarLogAlteracao;
import br.com.pactosolucoes.comuns.util.Formatador;
import negocio.comuns.arquitetura.SuperVO;
import negocio.comuns.plano.enumerador.TipoProduto;
import negocio.comuns.utilitarias.Calendario;
import negocio.comuns.utilitarias.ConsistirException;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisValidacao;
import relatorio.negocio.jdbc.financeiro.ProdutoRatear;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * Reponsável por manter os dados da entidade Banco. Classe do tipo VO - Value Object
 * composta pelos atributos da entidade com visibilidade protegida e os métodos de acesso a estes atributos.
 * Classe utilizada para apresentar e manter em memória os dados desta entidade.
 *
 * @see SuperVO
 */
public class ChequeVO extends SuperVO {

    private Integer codigo;
    private Integer MovPagamento;
    private Integer marcador;
    private String numero;
    private String agencia;
    @ChaveEstrangeira
    private BancoVO banco;
    private String nomeBanco;
    private String conta;
    private String vistaOuPrazo;
    private String situacao;
    private Double valor;
    private String valorFormatado;
    private Double valorTotal;
    private String Composicao;
    private String composicaoNova;
    private Date dataCompensacao;
    private String cpf;
    private String cnpj;
    private Boolean chequeEscolhido;
    private Boolean chequeCompensar;
    private Boolean apresentarCheque;
    private Boolean pagaOutroContrato;
    private Date dataOriginal;
    @NaoControlarLogAlteracao
    private String cpfOuCnpj;
    @ChaveEstrangeira
    private LoteVO loteVO = new LoteVO();
    @ChaveEstrangeira
    private MovContaVO movConta;
    private String nomeNoCheque;
    private String obterTodosChequesComposicao;
    private boolean credito;
    @NaoControlarLogAlteracao
    private int pagaContaFinanceiro = 0;
    private Boolean cupomEmitido = false;
    private Boolean nfseEmitido = false;
    private boolean nfceEmitido = false;
    private String produtosPagos;
    private String avisoVinculos;
    @NaoControlarLogAlteracao
    private boolean selecionado = false;
    @NaoControlarLogAlteracao
    private List<ProdutoRatear> produtosRateio = new ArrayList<ProdutoRatear>();
    private NFSeEmitidaVO nfSeEmitidaChequeExcluido;
    private Date dataDevolucao;
    @NaoControlarLogAlteracao
    private Boolean marcadoDevolucao = false;

    /**
     * Construtor padrão da classe <code>Banco</code>.
     * Cria uma nova instância desta entidade, inicializando automaticamente seus atributos (Classe VO).
     */
    public ChequeVO() {
        super();
        inicializarDados();
    }

    /**
     * Operação responsável por validar os dados de um objeto da classe <code>BancoVO</code>.
     * Todos os tipos de consistência de dados são e devem ser implementadas neste método.
     * São validações típicas: verificação de campos obrigatórios, verificação de valores válidos para os atributos.
     *
     * @throws ConsistirException Se uma inconsistência for encontrada aumaticamente é gerada uma exceção descrevendo
     *                            o atributo e o erro ocorrido.
     */
    public static void validarDados(ChequeVO obj) throws ConsistirException {
        if (!obj.getValidarDados()) {
            return;
        }
        if (obj.getBanco().getCodigo() == 0) {
            throw new ConsistirException("O campo BANCO CHEQUE (Movimento do Pagamento) deve ser informado.");
        }
        if (obj.getAgencia().equals("")) {
            throw new ConsistirException("O campo AGÊNCIA CHEQUE (Movimento do Pagamento) deve ser informado.");
        }
        if (obj.getConta().equals("")) {
            throw new ConsistirException("O campo CONTA CHEQUE (Movimento do Pagamento) deve ser informado.");
        }
        if (obj.getNumero().equals("")) {
            throw new ConsistirException("O campo NÚMERO CHEQUE (Movimento do Pagamento) deve ser informado.");
        }
        if (obj.getValor() == 0.0) {
            throw new ConsistirException("O campo VALOR CHEQUE (Movimento do Pagamento) deve ser informado.");
        }
        if (obj.getDataCompensacao() == null) {
            throw new ConsistirException("O campo DATA COMPENSAÇÃO (Movimento do Pagamento) deve ser informado.");
        }
    }

    public ChequeVO getClone() throws Exception {
        ChequeVO clone = new ChequeVO();
        clone.setBanco((BancoVO) banco.getClone(true));
        clone.setCodigo(codigo);
        clone.setMovPagamento(MovPagamento);
        clone.setMarcador(marcador);
        clone.setNumero(numero);
        clone.setAgencia(agencia);
        clone.setNomeBanco(nomeBanco);
        clone.setConta(conta);
        clone.setVistaOuPrazo(vistaOuPrazo);
        clone.setSituacao(situacao);
        clone.setValor(valor);
        clone.setValorTotal(valorTotal);
        clone.setDataCompensacao(dataCompensacao);
        clone.setDataOriginal(dataOriginal);
        clone.setCpf(cpf);
        clone.setCnpj(cnpj);
        clone.setNomeNoCheque(nomeNoCheque);
        clone.setChequeCompensar(chequeCompensar);
        clone.setChequeEscolhido(chequeEscolhido);
        clone.setApresentarCheque(apresentarCheque);
        clone.setLoteVO(loteVO);
        clone.setMovConta(movConta);
        clone.setProdutosPagos(produtosPagos);
        clone.setComposicao(Composicao);
        clone.setComposicaoNova(composicaoNova);
        return clone;
    }

    /**
     * Operação reponsável por inicializar os atributos da classe.
     */
    public void inicializarDados() {
        setCodigo(0);
        setMovPagamento(0);
        setNumero("");
        setAgencia("");
        setBanco(new BancoVO());
        setNomeBanco("");
        setConta("");
        setSituacao("EA");
        setValor(0.0);
        setValorTotal(0.0);
        setMarcador(0);
        setChequeEscolhido(false);
        setApresentarCheque(false);
        setChequeCompensar(false);
        setPagaOutroContrato(false);
        setDataOriginal(null);
        setNomeNoCheque("");
        setComposicao("");
        setComposicaoNova("");
        setProdutosPagos("");
    }

    public Integer getMovPagamento() {
        return MovPagamento;
    }

    public void setMovPagamento(Integer MovPagamento) {
        this.MovPagamento = MovPagamento;
    }

    public Integer getCodigo() {
        return codigo;
    }

    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public String getAgencia() {
        if (agencia == null) {
            agencia = "";
        }
        return agencia;
    }

    public void setAgencia(String agencia) {
        this.agencia = agencia;
    }

    public BancoVO getBanco() {
        return banco;
    }

    public void setBanco(BancoVO banco) {
        this.banco = banco;
    }

    public String getConta() {
        if (conta == null) {
            conta = "";
        }
        return conta;
    }

    public void setConta(String conta) {
        this.conta = conta;
    }

    public String getNumero() {
        if (numero == null) {
            numero = "";
        }
        return numero;
    }

    public void setNumero(String numero) {
        this.numero = numero;
    }

    public Double getValor() {
        return valor;
    }

    public void setValor(Double valor) {
        this.valor = valor;
    }

    public Double getValorTotal() {
        if (valorTotal == null) {
            valorTotal = 0.0;
        }
        return valorTotal;
    }

    public void setValorTotal(Double valorTotal) {
        this.valorTotal = valorTotal;
    }

    public String getComposicao() {
        return Composicao;
    }

    public void setComposicao(String composicao) {
        Composicao = composicao;
    }

    public Integer getMarcador() {
        return marcador;
    }

    public void setMarcador(Integer marcador) {
        this.marcador = marcador;
    }

    public Date getDataCompensacao() {
        return dataCompensacao;
    }

    public void setDataCompensacao(Date dataCompensacao) {
        this.dataCompensacao = dataCompensacao;
    }

    public String getDataCompensacao_Apresentar() {
        return Uteis.getData(dataCompensacao);
    }
    
    public String getDataOriginalApresentar() {
        if(dataOriginal == null){
            return Uteis.getData(dataCompensacao);    
        }else{
            return Uteis.getData(dataOriginal);
        }
    }

    public String getNomeBanco() {
        if (nomeBanco == null) {
            nomeBanco = "";
        }
        return nomeBanco;
    }

    public void setNomeBanco(String nomeBanco) {
        this.nomeBanco = nomeBanco;
    }

    public String getVistaOuPrazo() {
        if (vistaOuPrazo == null) {
            vistaOuPrazo = "";
        }
        return vistaOuPrazo;
    }

    public void setVistaOuPrazo(String vistaOuPrazo) {
        this.vistaOuPrazo = vistaOuPrazo;
    }

    public String getVistaOuPrazo_Apresentar() {
        if (vistaOuPrazo == null) {
            vistaOuPrazo = "";
        }
        if (vistaOuPrazo.equals("AV")) {
            return "A Vista";
        }
        if (vistaOuPrazo.equals("PR")) {
            return "PRAZO";
        }
        return vistaOuPrazo;
    }

    public String getCnpj() {
        if (cnpj == null) {
            cnpj = "";
        }
        return cnpj;
    }

    public void setCnpj(String cnpj) {
        this.cnpj = cnpj;
    }

    public String getCpf() {
        if (cpf == null) {
            cpf = "";
        }
        return cpf;
    }

    public void setCpf(String cpf) {
        this.cpf = cpf;
    }

    public Boolean getChequeEscolhido() {
        return chequeEscolhido;
    }

    public void setChequeEscolhido(Boolean chequeEscolhido) {
        this.chequeEscolhido = chequeEscolhido;
    }

    public Boolean getApresentarCheque() {
        return apresentarCheque;
    }

    public void setApresentarCheque(Boolean apresentarCheque) {
        this.apresentarCheque = apresentarCheque;
    }

    public Boolean getChequeCompensar() {
        return chequeCompensar;
    }

    public void setChequeCompensar(Boolean chequeCompensar) {
        this.chequeCompensar = chequeCompensar;
    }

    public String getSituacao() {
        if (situacao == null) {
            situacao = "";
        }
        return situacao;
    }

    public void setSituacao(String situacao) {
        this.situacao = situacao;
    }

    public Date getDataOriginal() {
        return dataOriginal;
    }

    public void setDataOriginal(Date dataOriginal) {
        this.dataOriginal = dataOriginal;
    }

    public boolean getTemLote() {
        return getLoteVO().getCodigo() > 0;
    }

    public Boolean getUsadoPagarContaFinanceiro() {
        return pagaContaFinanceiro > 0;
    }

    @Override
    public boolean equals(Object obj) {
        if (obj instanceof ChequeVO) {
            ChequeVO aux = (ChequeVO) obj;
            return this.codigo.intValue() == aux.getCodigo().intValue();
        }
        return false;
    }

    public String getSituacao_Apresentar() {
        if (situacao == null) {
            situacao = "";
        }
        if (situacao.equals("CA")) {
            return "Cancelado";
        }
        if (situacao.equals("DV")) {
            return "Devolvido";
        }
        if (Calendario.menorOuIgual(getDataCompensacao(), Calendario.hoje())) {
            return "Compensado";
        }
        if (Calendario.maior(getDataCompensacao(), Calendario.hoje())) {
            return "Em aberto";
        }
        return situacao;
    }

    public List<ProdutoRatear> getProdutosRateio() {
        return produtosRateio;
    }

    public void setProdutosRateio(List<ProdutoRatear> produtosRateio) {
        this.produtosRateio = produtosRateio;
    }

    public MovContaVO getMovConta() {
        if (movConta == null) {
            movConta = new MovContaVO();
        }
        return movConta;
    }

    public void setMovConta(MovContaVO movConta) {
        this.movConta = movConta;
    }

    public LoteVO getLoteVO() {
        return loteVO;
    }

    public void setLoteVO(LoteVO loteVO) {
        this.loteVO = loteVO;
    }

    public String getCpfOuCnpj() {
        return cpfOuCnpj;
    }

    public void setCpfOuCnpj(String cpfOuCnpj) {
        this.cpfOuCnpj = cpfOuCnpj;
    }

    public String getNomeNoCheque() {
        if (nomeNoCheque == null) {
            nomeNoCheque = "";
        }
        return nomeNoCheque;
    }

    public void setNomeNoCheque(String nomeNoCheque) {
        this.nomeNoCheque = nomeNoCheque;
    }

    public String getObterTodosChequesComposicao() {
        this.obterTodosChequesComposicao = codigo.toString();
        if (getComposicao() != null && !getComposicao().equals("")) {
            obterTodosChequesComposicao += "," + Composicao;
        }
        return obterTodosChequesComposicao;
    }

    public void setObterTodosChequesComposicao(
            String obterTodosChequesComposicao) {
        this.obterTodosChequesComposicao = obterTodosChequesComposicao;
    }

    public boolean isCredito() {
        return credito;
    }

    public void setCredito(boolean credito) {
        this.credito = credito;
    }

    public int getPagaContaFinanceiro() {
        return pagaContaFinanceiro;
    }

    public void setPagaContaFinanceiro(int pagaContaFinanceiro) {
        this.pagaContaFinanceiro = pagaContaFinanceiro;
    }

    public Boolean getCupomEmitido() {
        return cupomEmitido;
    }

    public void setCupomEmitido(Boolean cupomEmitido) {
        this.cupomEmitido = cupomEmitido;
    }

    public Boolean getPagaOutroContrato() {
        return pagaOutroContrato;
    }

    public void setPagaOutroContrato(Boolean pagaoutroContrato) {
        this.pagaOutroContrato = pagaoutroContrato;
    }

    public ChequeTO toTO() {
        return new ChequeTO(codigo, Composicao, dataCompensacao, dataOriginal, valor, agencia, conta, numero, banco.getNome(), nomeNoCheque);
    }

    public Boolean getTemComposicao() {
        return !UteisValidacao.emptyString(Composicao);
    }

    public String getProdutosPagos() {
        return produtosPagos;
    }

    public void setProdutosPagos(String produtosPagos) {
        this.produtosPagos = produtosPagos;
    }

    public Boolean getNfseEmitido() {
        return nfseEmitido;
    }

    public void setNfseEmitido(Boolean nfseEmitido) {
        this.nfseEmitido = nfseEmitido;
    }

    public String getComposicaoNova() {
        return composicaoNova;
    }

    public void setComposicaoNova(String composicaoNova) {
        this.composicaoNova = composicaoNova;
    }

    public String getAvisoVinculos() {
        if (avisoVinculos == null) {
            return "";
        }
        return avisoVinculos;
    }

    public void setAvisoVinculos(String avisoVinculos) {
        this.avisoVinculos = avisoVinculos;
    }

    public boolean isSelecionado() {
        return selecionado;
    }

    public void setSelecionado(boolean selecionado) {
        this.selecionado = selecionado;
    }

    public NFSeEmitidaVO getNfSeEmitidaChequeExcluido() {
        return nfSeEmitidaChequeExcluido;
    }

    public void setNfSeEmitidaChequeExcluido(NFSeEmitidaVO nfSeEmitidaChequeExcluido) {
        this.nfSeEmitidaChequeExcluido = nfSeEmitidaChequeExcluido;
    }
    
    public boolean codigoPertenceAComposicao(Integer codigoCheque) {
        if(this.codigo !=  null && codigo.equals(codigoCheque)){
            return true;
        }
        if(this.Composicao == null || this.Composicao == ""){
             return false;
        }
        if((this.Composicao.contains(","+codigoCheque.toString()+",") || this.Composicao.startsWith(codigoCheque.toString()+",") || this.Composicao.endsWith(","+codigoCheque.toString()) || this.Composicao.equals(codigoCheque.toString()))){
            return true;
        }
        return false;
    }

    public Date getDataDevolucao() {
        return dataDevolucao;
    }

    public void setDataDevolucao(Date dataDevolucao) {
        this.dataDevolucao = dataDevolucao;
    }

    public Boolean getMarcadoDevolucao() {
        return marcadoDevolucao;
    }

    public void setMarcadoDevolucao(Boolean marcadoDevolucao) {
        this.marcadoDevolucao = marcadoDevolucao;
    }

    public List<ProdutoPago> getProdutoPagos(){
        List<ProdutoPago> pagos = new ArrayList<ProdutoPago>();
        if(null != getProdutosPagos()){
            String[] pps = getProdutosPagos().split("\\|");
            for (String pp : pps) {
                if(!pp.isEmpty()) {
                    String[] it = pp.split("\\,");

                    ProdutoPago p = new ProdutoPago();
                    p.setProduto(Integer.parseInt(it[0]));
                    p.setTipoProduto(TipoProduto.getTipoProdutoCodigo(it[1]));
                    p.setContrato(Integer.parseInt(it[2]));
                    p.setValor(Double.parseDouble(it[3]));

                    pagos.add(p);
                }
            }
        }

        return pagos;
    }


    public boolean isNfceEmitido() {
        return nfceEmitido;
    }

    public void setNfceEmitido(boolean nfceEmitido) {
        this.nfceEmitido = nfceEmitido;
    }

    public String getValorFormatado() {
        valorFormatado = Formatador.formatarMoeda(valor);
        return valorFormatado;
    }
}

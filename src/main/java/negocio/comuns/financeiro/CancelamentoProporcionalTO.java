package negocio.comuns.financeiro;

import br.com.pactosolucoes.comuns.util.Formatador;
import br.com.pactosolucoes.enumeradores.TipoParcelaCancelamento;
import negocio.comuns.basico.EmpresaVO;
import negocio.comuns.contrato.CancelamentoContratoVO;
import negocio.comuns.contrato.ContratoVO;
import negocio.comuns.utilitarias.Calendario;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisValidacao;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

public class CancelamentoProporcionalTO extends CancelamentoCalculoTO {

    private ContratoVO contratoVO;
    private Integer diasUtilizados;
    private Double valorAnuidade;
    private MovParcelaVO proximaParcela;
    private Date dataCancelamento;
    private Date dataLimiteAcesso;
    private Integer diasProRataAnuidade;
    private List<MovParcelaVO> listaParcelasPagar;
    private List<MovParcelaVO> listaParcelasVencidas;
    private boolean cobrarProximaParcela = false;
    private String cobrarAnuidade;
    private Date dataVencimentoAnuidade;
    private boolean gerarNovaParcela = false;
    private Double valorNovaParcela;
    private Date dataVencimentoNovaParcela;
    private boolean gerarNovaParcelaProdutos = false;
    private Double valorNovaParcelaProdutos;
    private Date dataVencimentoNovaParcelaProdutos;
    private boolean cobrarAnuidadeTotal = false;
    private boolean naoCobrarMulta = true;
    private Double porcentagemMulta;
    private Double valorMulta;
    private Double valorMultaOriginal;
    private Date dataVencimentoMulta;
    private boolean alterarValorMulta = false;
    private boolean usuarioAlterouValorMulta = false;
    private boolean cancelamentoAutomatico = false;
    private String informacoesDesfazer;

//    DEFINIÇÃO COBRAR ANUIDADE!!!!!
//    ("S"); //NAO
//    ("N"); //SIM

    public Integer getDiasUtilizados() {
        if (diasUtilizados == null) {
            diasUtilizados = 0;
        }
        return diasUtilizados;
    }

    public void setDiasUtilizados(Integer diasUtilizados) {
        this.diasUtilizados = diasUtilizados;
    }

    public String getValorAnuidade_Apresentar() {
        return Formatador.formatarValorMonetario(getValorAnuidade());
    }

    public Double getValorAnuidade() {
        if (valorAnuidade == null) {
            valorAnuidade = 0.0;
        }
        return valorAnuidade;
    }

    public void setValorAnuidade(Double valorAnuidade) {
        this.valorAnuidade = valorAnuidade;
    }

    public MovParcelaVO getProximaParcela() {
        if (proximaParcela == null) {
            proximaParcela = new MovParcelaVO();
        }
        return proximaParcela;
    }

    public void setProximaParcela(MovParcelaVO proximaParcela) {
        this.proximaParcela = proximaParcela;
    }

    public Date getDataCancelamento() {
        return dataCancelamento;
    }

    public void setDataCancelamento(Date dataCancelamento) {
        this.dataCancelamento = dataCancelamento;
    }

    public Integer getDiasProRataAnuidade() {
        if (diasProRataAnuidade == null) {
            diasProRataAnuidade = 0;
        }
        return diasProRataAnuidade;
    }

    public void setDiasProRataAnuidade(Integer diasProRataAnuidade) {
        this.diasProRataAnuidade = diasProRataAnuidade;
    }

    public List<MovParcelaVO> getListaParcelasPagar() {
        if (listaParcelasPagar == null) {
            listaParcelasPagar = new ArrayList<MovParcelaVO>();
        }
        return listaParcelasPagar;
    }

    public void setListaParcelasPagar(List<MovParcelaVO> listaParcelasPagar) {
        this.listaParcelasPagar = listaParcelasPagar;
    }

    public String getDataLimiteAcesso_Apresentar() {
        return Uteis.getData(Calendario.subtrairDias(getDataLimiteAcesso(), 1));
    }

    public Date getDataLimiteAcesso() {
        return dataLimiteAcesso;
    }

    public void setDataLimiteAcesso(Date dataLimiteAcesso) {
        this.dataLimiteAcesso = dataLimiteAcesso;
    }

    public boolean isCobrarProximaParcela() {
        return cobrarProximaParcela;
    }

    public void setCobrarProximaParcela(boolean cobrarProximaParcela) {
        this.cobrarProximaParcela = cobrarProximaParcela;
    }

    public String getCobrarAnuidade() {
        if (cobrarAnuidade == null) {
            cobrarAnuidade = "";
        }
        return cobrarAnuidade;
    }

    public void setCobrarAnuidade(String cobrarAnuidade) {
        this.cobrarAnuidade = cobrarAnuidade;
    }

    public boolean isGerarNovaParcela() {
        return gerarNovaParcela;
    }

    public void setGerarNovaParcela(boolean gerarNovaParcela) {
        this.gerarNovaParcela = gerarNovaParcela;
    }

    public Double getValorNovaParcela() {
        if (valorNovaParcela == null) {
            valorNovaParcela = 0.0;
        }
        return valorNovaParcela;
    }

    public void setValorNovaParcela(Double valorNovaParcela) {
        this.valorNovaParcela = valorNovaParcela;
    }

    public ContratoVO getContratoVO() {
        if (contratoVO == null) {
            contratoVO = new ContratoVO();
        }
        return contratoVO;
    }

    public void setContratoVO(ContratoVO contratoVO) {
        this.contratoVO = contratoVO;
    }

    public Date getDataVencimentoNovaParcela() {
        return dataVencimentoNovaParcela;
    }

    public void setDataVencimentoNovaParcela(Date dataVencimentoNovaParcela) {
        this.dataVencimentoNovaParcela = dataVencimentoNovaParcela;
    }

    public boolean isGerarNovaParcelaProdutos() {
        return gerarNovaParcelaProdutos;
    }

    public void setGerarNovaParcelaProdutos(boolean gerarNovaParcelaProdutos) {
        this.gerarNovaParcelaProdutos = gerarNovaParcelaProdutos;
    }

    public Double getValorNovaParcelaProdutos() {
        if (valorNovaParcelaProdutos == null){
            valorNovaParcelaProdutos = 0.0;
        }
        return valorNovaParcelaProdutos;
    }

    public void setValorNovaParcelaProdutos(Double valorNovaParcelaProdutos) {
        this.valorNovaParcelaProdutos = valorNovaParcelaProdutos;
    }

    public Date getDataVencimentoNovaParcelaProdutos() {
        return dataVencimentoNovaParcelaProdutos;
    }

    public void setDataVencimentoNovaParcelaProdutos(Date dataVencimentoNovaParcelaProdutos) {
        this.dataVencimentoNovaParcelaProdutos = dataVencimentoNovaParcelaProdutos;
    }

    public Date getDataVencimentoAnuidade() {
        return dataVencimentoAnuidade;
    }

    public void setDataVencimentoAnuidade(Date dataVencimentoAnuidade) {
        this.dataVencimentoAnuidade = dataVencimentoAnuidade;
    }

    public String getDataVencimentoAnuidade_Apresentar() {
        return Uteis.getData(getDataVencimentoAnuidade());
    }

    public String getInformacoes() {
        return "";
    }

    public String getDescricaoCalculo(CancelamentoContratoVO cancelamentoContrato) {
        StringBuilder msg = new StringBuilder();
        msg.append("SOLICITAÇÃO DE CANCELAMENTO:\n");
        msg.append("- Data da solicitação de cancelamento: ").append(Uteis.getData(getDataCancelamento())).append(".");
        if (getDataLimiteAcesso() != null) {
            msg.append("- Acesso até o dia ").append(getDataLimiteAcesso_Apresentar()).append(", continue desfrutando de nossos serviços.\n\n");
        }

        msg.append("PRÓXIMAS COBRANÇAS:\n" +
                "Serão efetuadas as cobranças futuras do pró-rata da taxa de manutenção anual, " +
                "última mensalidade e eventuais débitos pendentes até a data do efetivo cancelamento, " +
                "conforme previsto no termo de matrícula.\n\n");

        if (!UteisValidacao.emptyList(getListaParcelasVencidas()) && mostrarDescricaoParcelaVencidaPorTipoParcelaCancelamento(cancelamentoContrato)) {
            msg.append("PARCELAS VENCIDAS:\n");
            for (MovParcelaVO movParcelaVO : getListaParcelasVencidas()) {
                if (!parcelaCancelada(movParcelaVO, cancelamentoContrato)) {
                    msg.append("Parcela: ").append(movParcelaVO.getDescricao()).append(" - Dt. Vencimento: ").append(Uteis.getData(movParcelaVO.getDataVencimento())).append(" - Valor ").append(Formatador.formatarValorMonetario(movParcelaVO.getValorParcela())).append(".\n");
                }
            }
            msg.append("\n");
        }

        if (isGerarNovaParcela()) {
            msg.append("NOVA PARCELA A SER GERADA:\n");
            msg.append("Valor: ").append(Formatador.formatarValorMonetario(getValorNovaParcela())).append(".\n");
            msg.append("Data Vencimento: ").append(Uteis.getData(getDataVencimentoNovaParcela())).append(".\n\n");
        }

        if (isGerarNovaParcelaProdutos()) {
            msg.append("NOVA PARCELA A SER GERADA PARA PRODUTOS:\n");
            msg.append("Valor: ").append(Formatador.formatarValorMonetario(getValorNovaParcelaProdutos())).append(".\n");
            msg.append("Data Vencimento: ").append(Uteis.getData(getDataVencimentoNovaParcelaProdutos())).append(".\n\n");
        }

        if (isCobrarProximaParcela() && !UteisValidacao.emptyList(getListaParcelasPagar())) {
            msg.append("MENSALIDADE:\n");
            msg.append("Cobrar próxima parcela: ").append(getListaParcelasPagar().get(0).getDescricao()).append(".\n");
            msg.append("Valor: ").append(getListaParcelasPagar().get(0).getValorParcela_Apresentar()).append(".\n");
            msg.append("Data Vencimento: ").append(Uteis.getData(getListaParcelasPagar().get(0).getDataVencimento())).append(".\n\n");
        }

        if (getCobrarAnuidade().equals("S")) {
            if (isCobrarAnuidadeTotal()) {
                msg.append("ANUIDADE:\n");
                msg.append(getValorAnuidade_Apresentar()).append(" - Referente a anuidade, a ser cobrada no dia ").append(Uteis.getData(getDataVencimentoAnuidade())).append(".\n\n");
            } else {
                msg.append("PRÓ-RATA DE ANUIDADE:\n");
                msg.append(getValorAnuidade_Apresentar()).append(" - Referente à ").append(getDiasProRataAnuidade()).append(" dias de pró-rata referente a anuidade, a ser cobrada no dia ").append(Uteis.getData(getDataVencimentoAnuidade())).append(".\n\n");
            }
        }
        if (!isNaoCobrarMulta() && getValorMulta() > 0.0) {
            msg.append("MULTA:\n");
            msg.append("Multa de ").append(getValorMulta_Apresentar()).append(", referente à ").append(getPorcentagemMulta()).append("% sobre o valor restante do contrato, a ser cobrada no dia ").append(getDataVencimentoMulta_Apresentar()).append(".\n\n");
        }
        if (isNaoCobrarMulta() && getValorMulta() > 0.0) {
            msg.append("MULTA:\n");
            msg.append("Multa de ").append(getValorMulta_Apresentar()).append(", referente à ").append(getPorcentagemMulta()).append("% sobre o valor restante do contrato, NÃO SERÁ COBRADA.\n\n");
        }
        return msg.toString();
    }

    public boolean mostrarDescricaoParcelaVencidaPorTipoParcelaCancelamento(CancelamentoContratoVO cancelamento) {
        EmpresaVO empresaVO = contratoVO.getEmpresa();
        int contemParcelaVencidaNaoCancelada = 0;
        for (MovParcelaVO movParcelaVencida : getListaParcelasVencidas()) {
            for (MovParcelaVO movParcelaCancelamento: cancelamento.getListaParcelas()) {
                if (movParcelaVencida.getCodigo().equals(movParcelaCancelamento.getCodigo()) && !movParcelaCancelamento.getSituacao().equals("CA")) {
                    contemParcelaVencidaNaoCancelada++;
                }
            }
        }
        if (empresaVO != null && empresaVO.getTipoParcelaCancelamento().equals(TipoParcelaCancelamento.MAIOR_IGUAL_MES_ATUAL.getSigla())
                && UteisValidacao.emptyNumber(contemParcelaVencidaNaoCancelada)) {
            return false;
        }
        return true;
    }

    private boolean parcelaCancelada (MovParcelaVO movParcelaVO, CancelamentoContratoVO cancelamento) {
        for (MovParcelaVO movParcelaCancelamento: cancelamento.getListaParcelas()) {
            if (movParcelaCancelamento.getCodigo().equals(movParcelaVO.getCodigo())) {
                return movParcelaCancelamento.getSituacao().equals("CA");
            }
        }
        return false;
    }

    public boolean isCobrarAnuidadeTotal() {
        return cobrarAnuidadeTotal;
    }

    public void setCobrarAnuidadeTotal(boolean cobrarAnuidadeTotal) {
        this.cobrarAnuidadeTotal = cobrarAnuidadeTotal;
    }

    public Double getValorMulta() {
        if (valorMulta == null) {
            valorMulta = 0.0;
        }
        return valorMulta;
    }

    public void setValorMulta(Double valorMulta) {
        this.valorMulta = valorMulta;
    }

    public Date getDataVencimentoMulta() {
        return dataVencimentoMulta;
    }

    public void setDataVencimentoMulta(Date dataVencimentoMulta) {
        this.dataVencimentoMulta = dataVencimentoMulta;
    }

    public Double getPorcentagemMulta() {
        if (porcentagemMulta == null) {
            porcentagemMulta = 0.0;
        }
        return porcentagemMulta;
    }

    public void setPorcentagemMulta(Double porcentagemMulta) {
        this.porcentagemMulta = porcentagemMulta;
    }

    public String getValorMulta_Apresentar() {
        return Formatador.formatarValorMonetario(getValorMulta());
    }

    public String getDataVencimentoMulta_Apresentar() {
        return Uteis.getData(getDataVencimentoMulta());
    }

    public boolean isAlterarValorMulta() {
        return alterarValorMulta;
    }

    public void setAlterarValorMulta(boolean alterarValorMulta) {
        this.alterarValorMulta = alterarValorMulta;
    }

    public boolean isNaoCobrarMulta() {
        return naoCobrarMulta;
    }

    public void setNaoCobrarMulta(boolean naoCobrarMulta) {
        this.naoCobrarMulta = naoCobrarMulta;
    }

    public boolean isUsuarioAlterouValorMulta() {
        return usuarioAlterouValorMulta;
    }

    public void setUsuarioAlterouValorMulta(boolean usuarioAlterouValorMulta) {
        this.usuarioAlterouValorMulta = usuarioAlterouValorMulta;
    }

    public Double getValorMultaOriginal() {
        if (valorMultaOriginal == null) {
            valorMultaOriginal = 0.0;
        }
        return valorMultaOriginal;
    }

    public void setValorMultaOriginal(Double valorMultaOriginal) {
        this.valorMultaOriginal = valorMultaOriginal;
    }

    public List<MovParcelaVO> getListaParcelasVencidas() {
        if (listaParcelasVencidas == null) {
            listaParcelasVencidas = new ArrayList<MovParcelaVO>();
        }
        return listaParcelasVencidas;
    }

    public void setListaParcelasVencidas(List<MovParcelaVO> listaParcelasVencidas) {
        this.listaParcelasVencidas = listaParcelasVencidas;
    }

    public boolean isCancelamentoAutomatico() {
        return cancelamentoAutomatico;
    }

    public void setCancelamentoAutomatico(boolean cancelamentoAutomatico) {
        this.cancelamentoAutomatico = cancelamentoAutomatico;
    }

    public String getInformacoesDesfazer() {
        if (informacoesDesfazer == null) {
            informacoesDesfazer = "";
        }
        return informacoesDesfazer;
    }

    public void setInformacoesDesfazer(String informacoesDesfazer) {
        this.informacoesDesfazer = informacoesDesfazer;
    }
}

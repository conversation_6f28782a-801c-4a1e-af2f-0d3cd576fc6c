package negocio.comuns.financeiro;

import negocio.comuns.arquitetura.SuperTO;

/**
 * <AUTHOR>
 */
public class ObjetoTreeTO extends SuperTO {
    private static final long serialVersionUID = 1721781714106903731L;
    private String codigoAgrupador;
    private String nome;
    private int codigoEntidade = 0;
    private boolean selecionado = false;

    public boolean equals(Object obj) {
        if (obj == null)
            return false;
        if (!(obj instanceof ObjetoTreeTO))
            return false;
        return (((ObjetoTreeTO) obj).getCodigoAgrupador().equals(this.getCodigoAgrupador()));
    }

    public Long getCodigoNode() {
        String codigo = this.getCodigoAgrupador();
        codigo = codigo.replace(".", "");
        return Long.parseLong(codigo.trim());
    }

    /**
     * @return the nome
     */
    public String getNome() {
        return nome;
    }

    /**
     * @param nome the nome to set
     */
    public void setNome(String nome) {
        this.nome = nome;
    }

    /**
     * @return the codigoAgrupador
     */
    public String getCodigoAgrupador() {
        return codigoAgrupador;
    }

    /**
     * @param codigoAgrupador the codigoAgrupador to set
     */
    public void setCodigoAgrupador(String codigoAgrupador) {
        this.codigoAgrupador = codigoAgrupador;
    }

    /**
     * @return the codigoEntidade
     */
    public int getCodigoEntidade() {
        return codigoEntidade;
    }

    /**
     * @param codigoEntidade the codigoEntidade to set
     */
    public void setCodigoEntidade(int codigoEntidade) {
        this.codigoEntidade = codigoEntidade;
    }

    /**
     * @return the selecionado
     */
    public boolean getSelecionado() {
        return selecionado;
    }

    /**
     * @param selecionado the selecionado to set
     */
    public void setSelecionado(boolean selecionado) {
        this.selecionado = selecionado;
    }

}

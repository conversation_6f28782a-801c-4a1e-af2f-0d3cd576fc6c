/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package negocio.comuns.feed;

import br.com.pactosolucoes.comuns.json.FeedGestaoJSON;
import java.util.Date;
import negocio.comuns.arquitetura.SuperVO;

/**
 *
 * <AUTHOR>
 */
public class FeedGestaoVO extends SuperVO{
    private String nome;
    private String mensagem;
    private DicaEnum dica;
    private CondicaoEnum condicao;
    private Double valorCondicao;
    private Date vigenciaDe;
    private Date vigenciaAte;
    private Integer periodicidade;
    private Integer diaMes;
    private Integer empresa;
    private PerfilUsuarioEnum perfil;
    private Integer codigoOAMD;
    private Integer codigoHistorico;
    
    private boolean primeira = false;
    private boolean ultima = false;
    private int index = 0;
    private Date dataVista;
    private Date dataProcessada;

    private IndicadorEnum indicadorGrupo;
    
    private boolean liked = false;
    private boolean disliked = false;
    

    public FeedGestaoVO(){
    }
    
    public FeedGestaoVO(FeedGestaoJSON json){
        nome = json.getNome();
        mensagem = json.getMensagem();
        dica = json.getDica() == null ? null : DicaEnum.getFromOrdinal(json.getDica());
        condicao = json.getCondicao() == null ? null : CondicaoEnum.getFromOrdinal(json.getCondicao());
        perfil = json.getPerfil() == null ? null : PerfilUsuarioEnum.getFromOrdinal(json.getPerfil());
        valorCondicao = json.getValorCondicao();
        vigenciaAte = json.getVigenciaAte();
        vigenciaDe = json.getVigenciaDe();
        periodicidade = json.getPeriodicidade();
        diaMes = json.getDiaMes();
        codigoOAMD = json.getCodigo();
        indicadorGrupo = json.getIndicadorGrupo() == null ? null : IndicadorEnum.getFromOrdinal(json.getIndicadorGrupo());
        
    }

    public Integer getCodigoOAMD() {
        return codigoOAMD;
    }

    public void setCodigoOAMD(Integer codigoOAMD) {
        this.codigoOAMD = codigoOAMD;
    }
    
    public String getNome() {
        return nome;
    }

    public void setNome(String nome) {
        this.nome = nome;
    }

    public String getMensagem() {
        return mensagem;
    }

    public void setMensagem(String mensagem) {
        this.mensagem = mensagem;
    }

    public DicaEnum getDica() {
        return dica;
    }

    public void setDica(DicaEnum indicador) {
        this.dica = indicador;
    }

    public CondicaoEnum getCondicao() {
        return condicao;
    }

    public void setCondicao(CondicaoEnum condicao) {
        this.condicao = condicao;
    }

    public Double getValorCondicao() {
        return valorCondicao;
    }

    public void setValorCondicao(Double valorCondicao) {
        this.valorCondicao = valorCondicao;
    }

    public Date getVigenciaDe() {
        return vigenciaDe;
    }

    public void setVigenciaDe(Date vigenciaDe) {
        this.vigenciaDe = vigenciaDe;
    }

    public Date getVigenciaAte() {
        return vigenciaAte;
    }

    public void setVigenciaAte(Date vigenciaAte) {
        this.vigenciaAte = vigenciaAte;
    }

    public Integer getPeriodicidade() {
        return periodicidade;
    }

    public void setPeriodicidade(Integer periodicidade) {
        this.periodicidade = periodicidade;
    }

    public Integer getDiaMes() {
        return diaMes;
    }

    public void setDiaMes(Integer diaMes) {
        this.diaMes = diaMes;
    }

    public PerfilUsuarioEnum getPerfil() {
        return perfil;
    }

    public void setPerfil(PerfilUsuarioEnum perfil) {
        this.perfil = perfil;
    }

    public Integer getEmpresa() {
        return empresa;
    }

    public void setEmpresa(Integer empresa) {
        this.empresa = empresa;
    }

    public boolean isPrimeira() {
        return primeira;
    }

    public void setPrimeira(boolean primeira) {
        this.primeira = primeira;
    }

    public boolean isUltima() {
        return ultima;
    }

    public void setUltima(boolean ultima) {
        this.ultima = ultima;
    }

    public int getIndex() {
        return index;
    }

    public void setIndex(int index) {
        this.index = index;
    }

    public Date getDataVista() {
        return dataVista;
    }

    public void setDataVista(Date dataVista) {
        this.dataVista = dataVista;
    }

    public Integer getCodigoHistorico() {
        return codigoHistorico;
    }

    public void setCodigoHistorico(Integer codigoHistorico) {
        this.codigoHistorico = codigoHistorico;
    }

    public IndicadorEnum getIndicadorGrupo() {
        return indicadorGrupo;
    }

    public void setIndicadorGrupo(IndicadorEnum indicadorGrupo) {
        this.indicadorGrupo = indicadorGrupo;
    }

    public boolean isLiked() {
        return liked;
    }

    public void setLiked(boolean liked) {
        this.liked = liked;
    }

    public boolean isDisliked() {
        return disliked;
    }

    public void setDisliked(boolean disliked) {
        this.disliked = disliked;
    }

    public Date getDataProcessada() {
        return dataProcessada;
    }

    public void setDataProcessada(Date dataProcessada) {
        this.dataProcessada = dataProcessada;
    }
}

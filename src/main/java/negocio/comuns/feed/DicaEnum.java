/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package negocio.comuns.feed;

/**
 *
 * <AUTHOR>
 */
public enum DicaEnum {
    ICV("<valor>"),
    CRM_VENDAS("<valor>"),
    PLANOS_POR_DURACAO("<valor>"),
    PREVISAO_RENOVACAO_CONTRATOS_ATIVOS("<valor>"),
    PREVISAO_RENOVACAO_CORRENTE("<valor>"),
    CONTATOS_GRUPO_RISCO("<valor>"),
    SALDO_ALUNOS(),
    CLIENTES_PARCELA_ATRASO("<valor>"),
    PARCELAS_DCC_VENCIDAS_NAO_RECEBIDAS("<valor>", "<dinheiro>"),
    NAO_USA_RECORRENCIA(),
    ICV_CONSULTOR("<consultores>"),
    PRODUTOS_ESTOQUE_MINIMO("<valor>"),
    TELA_BI();
    
    
    private String[] tags; 
    
    private DicaEnum(String ... tags){
        
        this.tags = tags;
    }

    public String[] getTags() {
        return tags;
    }

    public void setTags(String[] tags) {
        this.tags = tags;
    }
    

    public String getNome(){
        return name();
    }
    
   
    public static DicaEnum getFromOrdinal(int ord){
        for(DicaEnum ce : values()){
            if(ord == ce.ordinal()){
                return ce;
            }
        }
        return null;
    }
    
    
    
    
}

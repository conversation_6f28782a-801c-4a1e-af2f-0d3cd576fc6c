/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package negocio.comuns.feed;

import java.util.Date;
import negocio.comuns.arquitetura.SuperVO;

/**
 *
 * <AUTHOR>
 */
public class FeedGestaoHistoricoVO extends SuperVO{
    
    private Integer codigoFeedOAMD;
    private Date dia;
    private boolean foiVisto = false;
    

    public FeedGestaoHistoricoVO(Integer codigoFeedOAMD, Date dia) {
        this.codigoFeedOAMD = codigoFeedOAMD;
        this.dia = dia;
    }

    public FeedGestaoHistoricoVO() {
    }

    public Integer getCodigoFeedOAMD() {
        return codigoFeedOAMD;
    }

    public void setCodigoFeedOAMD(Integer codigoFeedOAMD) {
        this.codigoFeedOAMD = codigoFeedOAMD;
    }

    public Date getDia() {
        return dia;
    }

    public void setDia(Date dia) {
        this.dia = dia;
    }

    public boolean isFoiVisto() {
        return foiVisto;
    }

    public void setFoiVisto(boolean foiVisto) {
        this.foiVisto = foiVisto;
    }

    
}

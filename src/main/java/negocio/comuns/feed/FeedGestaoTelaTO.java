/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package negocio.comuns.feed;

import negocio.comuns.arquitetura.SuperTO;

/**
 *
 * <AUTHOR>
 */
public class FeedGestaoTelaTO extends SuperTO{
    
    private boolean primeira = false;
    private boolean ultima = false;
    private FeedGestaoVO feed1 = null;
    private FeedGestaoVO feed2 = null;
    private int index = 0;
    private IndicadorEnum indicador = null;

    public boolean isPrimeira() {
        return primeira;
    }

    public void setPrimeira(boolean primeira) {
        this.primeira = primeira;
    }

    public boolean isUltima() {
        return ultima;
    }

    public void setUltima(boolean ultima) {
        this.ultima = ultima;
    }

    public FeedGestaoVO getFeed1() {
        return feed1;
    }

    public void setFeed1(FeedGestaoVO feed1) {
        this.feed1 = feed1;
    }

    public FeedGestaoVO getFeed2() {
        return feed2;
    }

    public void setFeed2(FeedGestaoVO feed2) {
        this.feed2 = feed2;
    }

    public int getIndex() {
        return index;
    }

    public void setIndex(int index) {
        this.index = index;
    }

    public IndicadorEnum getIndicador() {
        return indicador;
    }

    public void setIndicador(IndicadorEnum indicador) {
        this.indicador = indicador;
    }
    
    
}

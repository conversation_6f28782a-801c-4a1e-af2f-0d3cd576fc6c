/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package negocio.comuns.feed;

/**
 *
 * <AUTHOR>
 */
public enum CondicaoEnum {
    MAIOR,
    MENOR,
    IGUAL;
    
    public String getNome(){
        return name();
    }
    
    public static CondicaoEnum getFromOrdinal(int o){
        for(CondicaoEnum ind : values()){
            if(ind.ordinal() == o){
                return ind;
            }
        }
        return null;
    }
}

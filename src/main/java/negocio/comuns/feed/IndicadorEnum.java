/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package negocio.comuns.feed;

/**
 *
 * <AUTHOR>
 */
public enum IndicadorEnum {
    FINANCEIRO(0,TipoIndicadorEnum.GERAL, "carinha-financeiro.png", "#4E8E3A", "FINANCEIROS", "financeiro.png", "Saúde Financeira", "Financeiro"),
    VENDAS(1,TipoIndicadorEnum.GERAL, "carinha-vendas.png", "#A3BD31", "de VENDAS", "vendas.png", "Indicadores de Vendas", "Vendas"),
    CRM(2,TipoIndicadorEnum.GERAL, "carinha-crm.png", "#E5BB18", "de CRM", "crm.png", "Gestão de Relacionamento", "CRM"),
    RETENCAO(3,TipoIndicadorEnum.GERAL, "carinha-retencao.png", "#CB6018", "de RETENÇÃO", "retencao.png", "Manutenção de Clientes", "Retenção"),
    TREINO(4,TipoIndicadorEnum.GERAL, "carinha-treino.png", "#A01027","de TREINO", "treino.png", "Qualidade técnica", "Treino"),
    TELA_BI(5,TipoIndicadorEnum.GERAL, "", "","", "", "", "Tela BI");
    
    private Integer id;
    private TipoIndicadorEnum tipo;
    private String image;
    private String cor;
    private String texto;
    private String icone;
    private String hint;
    private String descricao;
    
    public static IndicadorEnum getFromOrdinal(int o){
        for(IndicadorEnum ind : values()){
            if(ind.ordinal() == o){
                return ind;
            }
        }
        return null;
    }
    public static IndicadorEnum getFromId(int o){
        for(IndicadorEnum ind : values()){
            if(ind.getId() == o){
                return ind;
            }
        }
        return null;
    }
    private IndicadorEnum(Integer id,TipoIndicadorEnum tipo, String image, String cor, String texto, 
            String icone, String hint,
            String descricao){
        this.id = id;
        this.tipo = tipo;
        this.image = image;
        this.cor = cor;
        this.texto = texto;
        this.icone = icone;
        this.hint = hint;
        this.descricao = descricao;
    }

    public String getIcone() {
        return icone;
    }

    public void setIcone(String icone) {
        this.icone = icone;
    }

    
    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }
    
    public String getImage() {
        return image;
    }

    public void setImage(String image) {
        this.image = image;
    }

    public String getCor() {
        return cor;
    }

    public void setCor(String cor) {
        this.cor = cor;
    }
    

    public String getNome(){
        return name();
    }
    
    public TipoIndicadorEnum getTipo() {
        return tipo;
    }

    public void setTipo(TipoIndicadorEnum tipo) {
        this.tipo = tipo;
    }

    public String getTexto() {
        return texto;
    }

    public void setTexto(String texto) {
        this.texto = texto;
    }

    public String getHint() {
        return hint;
    }

    public void setHint(String hint) {
        this.hint = hint;
    }

    public String getDescricao() {
        return descricao;
    }

    public void setDescricao(String descricao) {
        this.descricao = descricao;
    }
    
    
}

/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package negocio.comuns.feed;

import servicos.impl.maxiPago.AdquirenteMaxiPagoEnum;

import javax.faces.model.SelectItem;
import java.util.ArrayList;
import java.util.List;

/**
 *
 * <AUTHOR>
 */
public enum PerfilUsuarioEnum {
    TODOS(0,"Todos"),
    ADMINISTRADOR(1,"Administrador"),
    CONSULTOR(2,"Consultor"),
    GERENTE(3,"Gerente"),
    PROFESSOR(4,"Professor");

    private int id;
    private String nome;

    private  PerfilUsuarioEnum(Integer id, String nome){
        this.id = id;
        this.nome = nome;
    }

    public String getNome(){
        return nome;
    }

    public int getId() {
        return id;
    }

    public static PerfilUsuarioEnum getFromOrdinal(int o){
        for(PerfilUsuarioEnum ind : values()){
            if(ind.ordinal() == o){
                return ind;
            }
        }
        return null;
    }

    public static List getSelectListPerfilUsuarioEnum() {
        List temp = new ArrayList<PerfilUsuarioEnum>();
        for (PerfilUsuarioEnum tipo : PerfilUsuarioEnum.values()) {
            temp.add(new SelectItem(tipo, tipo.getNome()));
        }
        return temp;
    }
}

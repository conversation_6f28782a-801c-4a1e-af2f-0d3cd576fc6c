/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package negocio.comuns.feed;

import java.util.Date;
import negocio.comuns.arquitetura.SuperVO;

/**
 *
 * <AUTHOR>
 */
public class FeedGestaoLidaVO extends SuperVO{
    
    private Integer codigoHistorico;
    private Integer usuario;
    private Date dataLida;
    private boolean liked = false;
    private boolean disliked = false;

    public FeedGestaoLidaVO(Integer codigo, Integer codigoFeedOamd, Integer usuario, Date dataVista) {
        this.codigo = codigo;
        this.codigoHistorico = codigoFeedOamd;
        this.usuario = usuario;
        this.dataLida = dataVista;
    }

    public FeedGestaoLidaVO() {
    }

    public Integer getCodigoHistorico() {
        return codigoHistorico;
    }

    public void setCodigoHistorico(Integer codigoFeedOamd) {
        this.codigoHistorico = codigoFeedOamd;
    }

    public Integer getUsuario() {
        return usuario;
    }

    public void setUsuario(Integer usuario) {
        this.usuario = usuario;
    }

    public Date getDataLida() {
        return dataLida;
    }

    public void setDataLida(Date dataVista) {
        this.dataLida = dataVista;
    }

    public boolean isLiked() {
        return liked;
    }

    public void setLiked(boolean liked) {
        this.liked = liked;
    }

    public boolean isDisliked() {
        return disliked;
    }

    public void setDisliked(boolean disliked) {
        this.disliked = disliked;
    }
    
}

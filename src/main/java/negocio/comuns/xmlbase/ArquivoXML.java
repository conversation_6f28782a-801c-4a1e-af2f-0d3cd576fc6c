/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */

package negocio.comuns.xmlbase;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
    import java.util.Map;

/**
 *
 * <AUTHOR>
 * data:28/11/10
 * Objetivo da classe: Criar arquivos no formato XML
 */
public class ArquivoXML {

    Map<String, CampoXML> mapaCamposXML;
    List<StringBuilder> rows;

    public ArquivoXML(){
       rows =  new ArrayList<StringBuilder>();
       mapaCamposXML= new HashMap<String, CampoXML>();
    }

    public List<StringBuilder> getRows() {
        return rows;
    }

    public void setRows(List<StringBuilder> rows) {
        this.rows = rows;
    }

    public Map<String, CampoXML> getMapaCamposXML() {
        return mapaCamposXML;
    }

    public void setMapaCamposXML(Map<String, CampoXML> mapaCamposXML) {
        this.mapaCamposXML = mapaCamposXML;
    }

}


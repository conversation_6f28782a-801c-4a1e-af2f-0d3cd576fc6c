/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package negocio.comuns.xmlbase;

import java.util.Date;
import java.util.List;

/**
 *
 * <AUTHOR>
 * data:28/11/10
 * Objetivo da Classe: Sevir de base para criação de outras classes, que precisam
 * gerar arquivos XML.
 */
public abstract  class  BaseXML {


    public abstract ArquivoXML adicionarDadosNoArquivo(List lista, boolean comAutorizacaoCobranca);

    public ArquivoXML arquivoXML;


    public BaseXML(){
       arquivoXML = new ArquivoXML();
    }

    public String criarArquivoXMLDataPacket(List lista){
        if (lista == null || lista.isEmpty()){
            return "";
        }
        arquivoXML = adicionarDadosNoArquivo(lista, false);
        return  retornarArquivoXMLDataPacket(arquivoXML);
    }

    public String criarArquivoXMLDataPacket(List lista, boolean comAutorizacaoCobranca){
    	if (lista == null || lista.isEmpty()){
    		return "";
    	}
        arquivoXML = adicionarDadosNoArquivo(lista, comAutorizacaoCobranca);
        return  retornarArquivoXMLDataPacket(arquivoXML);
    }

    public void addArquivoXML(String nomeCampo,
            String tamanhoCampo,
            Object tipoCampo,
            String valorCampo,
            int indiceDaLista,
            ArquivoXML arquivoXML) {
        CampoXML camposXML = arquivoXML.getMapaCamposXML().get(nomeCampo);
        // Criar os campos do XML
        if (camposXML == null) {
            camposXML = new CampoXML();
            camposXML.setNome(nomeCampo);
            camposXML.setTamanho(tamanhoCampo);
            camposXML.setTipo(tipoCampo);
            arquivoXML.getMapaCamposXML().put(nomeCampo, camposXML);
        }
        // Criar os Rows do XML
        StringBuilder linha;
        if (indiceDaLista < arquivoXML.getRows().size()){
           linha = arquivoXML.getRows().get(indiceDaLista);
        } else{
          linha = new StringBuilder();
          arquivoXML.getRows().add(linha);
        }
        linha.append(" ");
        linha.append(nomeCampo);
        linha.append("=\"");
        linha.append(valorCampo);
        linha.append("\"");
    }

    public String retornarArquivoXMLDataPacket(ArquivoXML arquivoXML) {
        StringBuilder textoXML = new StringBuilder();
        // Início do xml
        textoXML.append("<?xml version=\"1.0\" standalone=\"yes\"?> ");
        textoXML.append("<DATAPACKET Version=\"2.0\">");
        textoXML.append("<METADATA>");
        textoXML.append("<FIELDS>");
        // Início criação do METADATA
        //camposXML.getTipo().equals(String.class)

        for (String key : arquivoXML.getMapaCamposXML().keySet()) {
            CampoXML camposXML = arquivoXML.getMapaCamposXML().get(key);

            if (camposXML != null) {
                if (camposXML.getTipo().equals(Integer.class)) {
                    textoXML.append("<FIELD attrname=\"");
                    textoXML.append(camposXML.nome);
                    textoXML.append("\" fieldtype=\"i4\"/>");
                } else if (camposXML.getTipo().equals(String.class)) {
                    textoXML.append("<FIELD attrname=\"");
                    textoXML.append(camposXML.nome);
                    textoXML.append("\" fieldtype=\"string\" WIDTH=\"");
                    textoXML.append(camposXML.tamanho);
                    textoXML.append("\"/>");
                } else if (camposXML.getTipo().equals(Date.class)) {
                    textoXML.append("<FIELD attrname=\"");
                    textoXML.append(camposXML.nome);
                    textoXML.append("\" fieldtype=\"dateTime\"/>");
                }
            }
        }
        if ((arquivoXML.getMapaCamposXML() == null) || (arquivoXML.getMapaCamposXML().size() <= 0)){
          // Retornar o METADATA padrão.
          textoXML.append("<FIELD attrname=\"");
          textoXML.append("ResultadoConsulta");
          textoXML.append("\" fieldtype=\"string\" WIDTH=\"10\"/>");
        }
        // Fim criação do METADATA
        textoXML.append("</FIELDS>");
        textoXML.append("</METADATA>");
        textoXML.append("<ROWDATA>");
        // Início criação do ROWDATA
        for(StringBuilder linha : arquivoXML.getRows()){
            textoXML.append("<ROW RowState=\"4\"");
            textoXML.append(linha);
            textoXML.append("/>");
        }
        // Fim criação do ROWDATA
        textoXML.append("</ROWDATA>");
        textoXML.append("</DATAPACKET>");
        return textoXML.toString();

    }
}

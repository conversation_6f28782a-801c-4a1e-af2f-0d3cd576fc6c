package negocio.comuns.gestao;

import negocio.comuns.arquitetura.SuperTO;

/**
 * Created by <PERSON> on 17/11/2016.
 */
public class GestaoICVTO extends SuperTO {
    private Integer qtdMatriculaMes;
    private Integer qtdRematriculaMes;
    private Integer qtdQuestionarioMes;
    private Integer qtdMatriculaDia;
    private Integer qtdRematriculaDia;
    private Integer qtdQuestionarioDia;
    private Double totalICV;
    private Integer qtdBVSessaoMes;
    private Integer qtdBVSessaoDia;
    private Integer qtdBVSessaoPrimeiraCompraMes;
    private Integer qtdBVSessaoPrimeiraCompraDia;
    private Integer qtdBVSessaoRetornoCompraMes;
    private Integer qtdBVSessaoRetornoCompraDia;
    private Double totalICVS;

    public Integer getQtdMatriculaMes() {
        return qtdMatriculaMes;
    }

    public void setQtdMatriculaMes(Integer qtdMatriculaMes) {
        this.qtdMatriculaMes = qtdMatriculaMes;
    }

    public Integer getQtdRematriculaMes() {
        return qtdRematriculaMes;
    }

    public void setQtdRematriculaMes(Integer qtdRematriculaMes) {
        this.qtdRematriculaMes = qtdRematriculaMes;
    }

    public Integer getQtdQuestionarioMes() {
        return qtdQuestionarioMes;
    }

    public void setQtdQuestionarioMes(Integer qtdQuestionarioMes) {
        this.qtdQuestionarioMes = qtdQuestionarioMes;
    }

    public Integer getQtdMatriculaDia() {
        return qtdMatriculaDia;
    }

    public void setQtdMatriculaDia(Integer qtdMatriculaDia) {
        this.qtdMatriculaDia = qtdMatriculaDia;
    }

    public Integer getQtdRematriculaDia() {
        return qtdRematriculaDia;
    }

    public void setQtdRematriculaDia(Integer qtdRematriculaDia) {
        this.qtdRematriculaDia = qtdRematriculaDia;
    }

    public Integer getQtdQuestionarioDia() {
        return qtdQuestionarioDia;
    }

    public void setQtdQuestionarioDia(Integer qtdQuestionarioDia) {
        this.qtdQuestionarioDia = qtdQuestionarioDia;
    }

    public Double getTotalICV() {
        return totalICV;
    }

    public void setTotalICV(Double totalICV) {
        this.totalICV = totalICV;
    }

    public Integer getQtdBVSessaoMes() {
        return qtdBVSessaoMes;
    }

    public void setQtdBVSessaoMes(Integer qtdBVSessaoMes) {
        this.qtdBVSessaoMes = qtdBVSessaoMes;
    }

    public Integer getQtdBVSessaoDia() {
        return qtdBVSessaoDia;
    }

    public void setQtdBVSessaoDia(Integer qtdBVSessaoDia) {
        this.qtdBVSessaoDia = qtdBVSessaoDia;
    }

    public Integer getQtdBVSessaoPrimeiraCompraMes() {
        return qtdBVSessaoPrimeiraCompraMes;
    }

    public void setQtdBVSessaoPrimeiraCompraMes(Integer qtdBVSessaoPrimeiraCompraMes) {
        this.qtdBVSessaoPrimeiraCompraMes = qtdBVSessaoPrimeiraCompraMes;
    }

    public Integer getQtdBVSessaoPrimeiraCompraDia() {
        return qtdBVSessaoPrimeiraCompraDia;
    }

    public void setQtdBVSessaoPrimeiraCompraDia(Integer qtdBVSessaoPrimeiraCompraDia) {
        this.qtdBVSessaoPrimeiraCompraDia = qtdBVSessaoPrimeiraCompraDia;
    }

    public Integer getQtdBVSessaoRetornoCompraMes() {
        return qtdBVSessaoRetornoCompraMes;
    }

    public void setQtdBVSessaoRetornoCompraMes(Integer qtdBVSessaoRetornoCompraMes) {
        this.qtdBVSessaoRetornoCompraMes = qtdBVSessaoRetornoCompraMes;
    }

    public Integer getQtdBVSessaoRetornoCompraDia() {
        return qtdBVSessaoRetornoCompraDia;
    }

    public void setQtdBVSessaoRetornoCompraDia(Integer qtdBVSessaoRetornoCompraDia) {
        this.qtdBVSessaoRetornoCompraDia = qtdBVSessaoRetornoCompraDia;
    }

    public Double getTotalICVS() {
        return totalICVS;
    }

    public void setTotalICVS(Double totalICVS) {
        this.totalICVS = totalICVS;
    }
}

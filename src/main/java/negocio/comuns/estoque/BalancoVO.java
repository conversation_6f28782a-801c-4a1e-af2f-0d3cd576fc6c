/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package negocio.comuns.estoque;

import annotations.arquitetura.CampoCalendario;
import annotations.arquitetura.ChavePrimaria;
import br.com.pactosolucoes.comuns.util.Formatador;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashSet;
import java.util.List;
import java.util.Set;
import negocio.comuns.arquitetura.SuperVO;
import negocio.comuns.arquitetura.UsuarioVO;
import negocio.comuns.basico.EmpresaVO;
import negocio.comuns.utilitarias.ConsistirException;
import negocio.comuns.utilitarias.Uteis;

/**
 *
 * <AUTHOR>
 */
public class BalancoVO extends SuperVO {

    @ChavePrimaria
    protected Integer codigo;
    protected EmpresaVO empresa = new EmpresaVO();
    protected Boolean cancelado;
    @CampoCalendario
    protected Date dataCadastro;
    protected UsuarioVO usuarioCadastro = new UsuarioVO();
    protected Date dataCancelamento;
    protected UsuarioVO usuarioCancelamento = new UsuarioVO();
    protected String observacoes = "";
    protected Set<BalancoItensVO> itens = new HashSet<BalancoItensVO>();
    private String situacao_Apresentar = "";
    private String descricao;

    public BalancoVO() {
        super();
        setCodigo(new Integer(0));
    }

    public String getEmpresa_Apresentar() {
        return getEmpresa().getNome();
    }

    public String getSituacao_Apresentar() {
        if (cancelado == false) {
            return ("Ativo");
        }
        if (cancelado == true) {
            return ("Cancelado");
        }
        return situacao_Apresentar;
    }

    public static void validarDados(BalancoVO obj) throws ConsistirException {
        if ((obj.getEmpresa() == null) || (obj.getEmpresa().getCodigo().intValue() <= 0)) {
            throw new ConsistirException("O campo Empresa deve ser informado.");
        }
        if (obj.getDataCadastro() == null) {
            throw new ConsistirException("O campo Data Balanço deve ser informado.");
        }

    }

    public List<BalancoItensVO> getItensList() {
        return new ArrayList<BalancoItensVO>(this.getItens());
    }

    public boolean getPermiteAlterarItens() {
        return ((this.codigo == null) || (this.codigo.longValue() == 0));
    }

    public Integer getCodigo() {
        return (codigo);
    }

    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public boolean getPermiteCancelarBalanco() {
        return (this.codigo != null)
                && (this.codigo > 0)
                && (this.cancelado != null)
                && (!this.cancelado);
    }

    public Boolean getCancelado() {
        return cancelado;
    }

    public void setCancelado(Boolean cancelado) {
        this.cancelado = cancelado;
    }

    public Date getDataCadastro() {
        return dataCadastro;
    }
    public String getDataCadastro_Apresentar() {
        return Uteis.getDataComHora(dataCadastro);
    }

    public void setDataCadastro(Date dataCadastro) {
        this.dataCadastro = dataCadastro;
    }

    public EmpresaVO getEmpresa() {
        return empresa;
    }

    public void setEmpresa(EmpresaVO empresa) {
        this.empresa = empresa;
    }

    public Set<BalancoItensVO> getItens() {
        return itens;
    }

    public void setItens(Set<BalancoItensVO> itens) {
        this.itens = itens;
    }

    public UsuarioVO getUsuarioCadastro() {
        return usuarioCadastro;
    }

    public void setUsuarioCadastro(UsuarioVO usuarioCadastro) {
        this.usuarioCadastro = usuarioCadastro;
    }

    public Date getDataCancelamento() {
        return dataCancelamento;
    }

    public void setDataCancelamento(Date dataCancelamento) {
        this.dataCancelamento = dataCancelamento;
    }

    public UsuarioVO getUsuarioCancelamento() {
        return usuarioCancelamento;
    }

    public void setUsuarioCancelamento(UsuarioVO usuarioCancelamento) {
        this.usuarioCancelamento = usuarioCancelamento;
    }

    public String getObservacoes() {
        return observacoes;
    }

    public void setObservacoes(String observacoes) {
        this.observacoes = observacoes;
    }

    public void setSituacao_Apresentar(String situacao_Apresentar) {
        this.situacao_Apresentar = situacao_Apresentar;
    }

    public String getDescricao() {
        return descricao;
    }

    public void setDescricao(String descricao) {
        this.descricao = descricao;
    }
}

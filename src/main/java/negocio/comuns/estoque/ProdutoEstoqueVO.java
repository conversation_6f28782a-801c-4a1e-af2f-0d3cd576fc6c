/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package negocio.comuns.estoque;

import annotations.arquitetura.ChaveEstrangeira;
import annotations.arquitetura.ChavePrimaria;
import negocio.comuns.arquitetura.SuperVO;
import negocio.comuns.basico.EmpresaVO;
import negocio.comuns.plano.ConfiguracaoProdutoEmpresaVO;
import negocio.comuns.plano.ProdutoVO;
import negocio.comuns.utilitarias.ConsistirException;

import java.util.Date;

/**
 *
 * <AUTHOR>
 */
public class ProdutoEstoqueVO extends SuperVO {

    @ChavePrimaria
    protected Integer codigo;
    @ChaveEstrangeira
    protected EmpresaVO empresa = new EmpresaVO();
    @ChaveEstrangeira
    protected ProdutoVO produto = new ProdutoVO();
    protected Integer estoque;
    protected Integer estoqueMinimo;
    protected String situacao;
    // Atributo criado para auxiliar o relatório
    protected Double valorTotalEstoque;
    private String situacao_Apresentar = "";
    private Date dataConfiguracaoEstoque;
    private Double valorUnitario;
    private Integer pontos;

    public ProdutoEstoqueVO() {
        super();
    }

    public String getSituacao_Apresentar() {
        if (situacao.equals("A")) {
            return ("Ativo");
        }
        if (situacao.equals("C")) {
            return ("Cancelado");
        }
        return situacao_Apresentar;
    }

    public int getCodigo_Apresentar() {
        return getProduto().getCodigo();
    }

    public String getDescricao_Apresentar() {
        return getProduto().getDescricao();
    }

    public String getEmpresa_Apresentar() {
        return getEmpresa().getNome();
    }

    public static void validarDados(ProdutoEstoqueVO obj) throws ConsistirException {
        if ((obj.getEmpresa() == null) || (obj.getEmpresa().getCodigo().intValue() <= 0)) {
            throw new ConsistirException("O campo Empresa deve ser informado.");
        }
        if ((obj.getProduto() == null) || (obj.getProduto().getCodigo().intValue() <= 0)) {
            throw new ConsistirException("O campo Produto deve ser informado.");
        }
    }

    public EmpresaVO getEmpresa() {
        return empresa;
    }

    public void setEmpresa(EmpresaVO empresa) {
        this.empresa = empresa;
    }

    public Integer getEstoque() {
        return estoque;
    }

    public void setEstoque(Integer estoque) {
        this.estoque = estoque;
    }

    public Integer getEstoqueMinimo() {
        return estoqueMinimo;
    }

    public void setEstoqueMinimo(Integer estoqueMinimo) {
        this.estoqueMinimo = estoqueMinimo;
    }

    public ProdutoVO getProduto() {
        return produto;
    }

    public void setProduto(ProdutoVO produto) {
        this.produto = produto;
    }

    @Override
    public Integer getCodigo() {
        return (codigo);
    }

    @Override
    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public boolean getPermiteCancelar() {
        return (this.codigo != null)
                && (this.codigo > 0)
                && (this.situacao.equals("A"));

    }

    public String getSituacao() {
        return situacao;
    }

    public void setSituacao(String situacao) {
        this.situacao = situacao;
    }

    public Double getValorTotalEstoque() {
        if (this.estoque == null) {
            return 0d;
        } else {
            return this.estoque * this.getValorUnitario();
        }

    }

    public Double getValorUnitario() {
        if(valorUnitario==null){
            valorUnitario = getProduto().getValorFinal();
            for(ConfiguracaoProdutoEmpresaVO cfe : getProduto().getConfiguracoesEmpresa()){
                if(cfe.getEmpresa().getCodigo().equals(getEmpresa().getCodigo())){
                    valorUnitario = cfe.getValor();
                }
            }
         }
        return valorUnitario;
    }

    public void setValorUnitario(Double valorUnitario) {
        this.valorUnitario = valorUnitario;
    }

    public String getOrdernarCategoriaProduto(){
        return getProduto().getCategoriaProduto().getDescricao();
    }

    public int hashCode() {
        return 1 * 17;
    }

    public boolean equals(Object obj) {
        if ((obj == null) || (!(obj instanceof ProdutoEstoqueVO))) {
            return false;
        }
        return ((ProdutoEstoqueVO) obj).getProduto().getCodigo().equals(this.getProduto().getCodigo())
                && ((ProdutoEstoqueVO) obj).getEmpresa().getCodigo().equals(this.getEmpresa().getCodigo());
    }

    public void setSituacao_Apresentar(String situacao_Apresentar) {
        this.situacao_Apresentar = situacao_Apresentar;
    }

    public Date getDataConfiguracaoEstoque() {
        return dataConfiguracaoEstoque;
    }

    public void setDataConfiguracaoEstoque(Date dataConfiguracaoEstoque) {
        this.dataConfiguracaoEstoque = dataConfiguracaoEstoque;
    }

    public Integer getPontos() {
        if (pontos == null || pontos < 0){
            pontos = 0;
        }
        return pontos;
    }

    public void setPontos(Integer pontos) {
        this.pontos = pontos;
    }
}

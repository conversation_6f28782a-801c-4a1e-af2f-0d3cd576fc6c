/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package negocio.comuns.estoque;

import annotations.arquitetura.ChaveEstrangeira;
import annotations.arquitetura.ChavePrimaria;
import br.com.pactosolucoes.comuns.util.Formatador;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashSet;
import java.util.List;
import java.util.Set;
import negocio.comuns.arquitetura.SuperVO;
import negocio.comuns.arquitetura.UsuarioVO;
import negocio.comuns.basico.EmpresaVO;
import negocio.comuns.basico.FornecedorVO;
import negocio.comuns.financeiro.MovContaVO;
import negocio.comuns.utilitarias.ConsistirException;
import negocio.comuns.utilitarias.Uteis;

/**
 *
 * <AUTHOR>
 */
public class CompraVO extends SuperVO {

    @ChavePrimaria
    protected Integer codigo;
    protected EmpresaVO empresa = new EmpresaVO();
    @ChaveEstrangeira
    protected FornecedorVO fornecedor = new FornecedorVO();
    protected Boolean cancelada;
    protected String numeroNF;
    protected String contato;
    protected String telefoneContato;
    protected Date dataEmissao;
    protected Date dataCadastro;
    protected UsuarioVO usuarioCadastro = new UsuarioVO();
    protected Date dataCancelamento;
    protected UsuarioVO usuarioCancelamento = new UsuarioVO();
    protected Double valorTotal;
    protected String observacoes;
    protected Set<CompraItensVO> itens = new HashSet<CompraItensVO>();
    private String situacao_Apresentar = "";
    private Integer quantidade;
    private Integer pontos;
    private MovContaVO movConta;
    private String descricaoFinanceiro;
    private Boolean autorizada;
    private String documento;
    private String documentoExtensao;
    private String importacaoObs;
    private String produtoComConfigsGenericasJson;

    private String idPedidoNuvemshop; //Esse campo é usado para validar se um pedido da nuvemshop já foi processado - caso haja um id aqui, ele impede que o msm pedido seja processado 2x
    private Integer nrParcelasContaPagar = 1;
    private List<DocumentoCompraVO> documentosCompra = new ArrayList<>();
    private List<ContaFinanceiroCompraVO> compraContasLancarFinanceiro;
    private Integer solicitacaoCompra;

    public CompraVO() {
        super();
        setCodigo(new Integer(0));
    }

    public String getSituacao_Apresentar() {
        if (cancelada == false) {
            return ("Ativa");
        }
        if (cancelada == true) {
            return ("Cancelada");
        }
        return situacao_Apresentar;
    }

    public String getEmpresa_Apresentar() {
        return getEmpresa().getNome();
    }

    public String getFornecedor_Apresentar() {
        return getFornecedor().getPessoa().getNome();
    }

    public static void validarDados(CompraVO obj) throws ConsistirException {
        if ((obj.getEmpresa() == null) || (obj.getEmpresa().getCodigo().intValue() <= 0)) {
            throw new ConsistirException("O campo Empresa deve ser informado.");
        }
        if ((obj.getFornecedor() == null) || (obj.getFornecedor().getCodigo().intValue() <= 0)) {
            throw new ConsistirException("O campo Fornecedor deve ser informado.");
        }
    }

    public Boolean getCancelada() {
        return cancelada;
    }

    public void setCancelada(Boolean cancelada) {
        this.cancelada = cancelada;
    }

    public String getContato() {
        return contato;
    }

    public void setContato(String contato) {
        this.contato = contato;
    }

    public Date getDataCadastro() {
        return dataCadastro;
    }
    public String getDataCadastro_Apresentar(){
        if(this.dataCadastro == null){
    		return "";
    	}
        return Uteis.getDataComHora(this.dataCadastro);
    }
    
    public void setDataCadastro_Apresentar(String data) throws Exception {
        try {
            this.setDataCadastro(Uteis.getDateTime(Uteis.getDate(data.substring(0, 10)), Integer.parseInt(data.substring(13, 15)),
                    Integer.parseInt(data.substring(16, 18)), Integer.parseInt(data.substring(19, 21))));
        } catch (Exception e) {
            try {
                this.setDataCadastro(Uteis.getDateTime(Uteis.getDate(data.substring(0, 10)), 0,
                        0, 0));
            }catch (Exception ex){
                this.setDataCadastro(null);
            }

        }
    }

    public void setDataCadastro(Date dataCadastro) {
        this.dataCadastro = dataCadastro;
    }

    public Date getDataCancelamento() {
        return dataCancelamento;
    }

    public void setDataCancelamento(Date dataCancelamento) {
        this.dataCancelamento = dataCancelamento;
    }

    public Date getDataEmissao() {
        return dataEmissao;
    }

    public void setDataEmissao(Date dataEmissao) {
        this.dataEmissao = dataEmissao;
    }

    public EmpresaVO getEmpresa() {
        return empresa;
    }

    public void setEmpresa(EmpresaVO empresa) {
        this.empresa = empresa;
    }

    public FornecedorVO getFornecedor() {
        return fornecedor;
    }

    public void setFornecedor(FornecedorVO fornecedor) {
        this.fornecedor = fornecedor;
    }

    public String getNumeroNF() {
        return numeroNF;
    }

    public void setNumeroNF(String numeroNF) {
        this.numeroNF = numeroNF;
    }

    public String getTelefoneContato() {
        return telefoneContato;
    }

    public void setTelefoneContato(String telefoneContato) {
        this.telefoneContato = telefoneContato;
    }

    public UsuarioVO getUsuarioCadastro() {
        return usuarioCadastro;
    }

    public void setUsuarioCadastro(UsuarioVO usuarioCadastro) {
        this.usuarioCadastro = usuarioCadastro;
    }

    public UsuarioVO getUsuarioCancelamento() {
        return usuarioCancelamento;
    }

    public void setUsuarioCancelamento(UsuarioVO usuarioCancelamento) {
        this.usuarioCancelamento = usuarioCancelamento;
    }

    public Double getValorTotal() {
        if ((this.getItens() != null) && (this.getItens().size() > 0)) {
            this.valorTotal = 0d;
            for (CompraItensVO obj : this.getItens()) {
                this.valorTotal += obj.getTotal();
            }
        }
        return valorTotal;
    }
    public String getValorTotal_Apresentar() {
        if ((this.getItens() != null) && (this.getItens().size() > 0)) {
            this.valorTotal = 0d;
            for (CompraItensVO obj : this.getItens()) {
                this.valorTotal += obj.getTotal();
            }
        }
        return Formatador.formatarValorMonetario(valorTotal);
    }
    public void setValorTotal(Double valorTotal) {
        this.valorTotal = valorTotal;
    }

    public Set<CompraItensVO> getItens() {
        return itens;
    }

    public void setItens(Set<CompraItensVO> itens) {
        this.itens = itens;
    }

    public List<CompraItensVO> getItensList() {
        return new ArrayList<CompraItensVO>(this.getItens());
    }

    public String getObservacoes() {
        return observacoes;
    }

    public void setObservacoes(String observacoes) {
        this.observacoes = observacoes;
    }

    public boolean getPermiteAlterarItens() {
        return (this.novoObj) || ((this.codigo == null) || (this.codigo.longValue() == 0));
    }

    public Integer getCodigo() {
        return (codigo);
    }

    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public boolean getPermiteCancelarCompra() {
        return (this.codigo != null)
                && (this.codigo > 0)
                && (this.cancelada != null)
                && (!this.cancelada);
    }

    public void setSituacao_Apresentar(String situacao_Apresentar) {
        this.situacao_Apresentar = situacao_Apresentar;
    }

    public Integer getQuantidade() {
        return quantidade;
    }

    public void setQuantidade(Integer quantidade) {
        this.quantidade = quantidade;
    }

    public Integer getPontos() {
        return pontos;
    }

    public void setPontos(Integer pontos) {
        this.pontos = pontos;
    }

    public MovContaVO getMovConta() {
        return movConta;
    }

    public void setMovConta(MovContaVO movConta) {
        this.movConta = movConta;
    }

    public String getDescricaoFinanceiro() {
        return descricaoFinanceiro;
    }

    public void setDescricaoFinanceiro(String descricaoFinanceiro) {
        this.descricaoFinanceiro = descricaoFinanceiro;
    }

    public Boolean getAutorizada() {
        return autorizada;
    }

    public void setAutorizada(Boolean autorizada) {
        this.autorizada = autorizada;
    }

    public String getIdPedidoNuvemshop() {
        return idPedidoNuvemshop;
    }

    public void setIdPedidoNuvemshop(String idPedidoNuvemshop) {
        this.idPedidoNuvemshop = idPedidoNuvemshop;
    }

    public Integer getNrParcelasContaPagar() {
        return nrParcelasContaPagar;
    }

    public void setNrParcelasContaPagar(Integer nrParcelasContaPagar) {
        this.nrParcelasContaPagar = nrParcelasContaPagar;
    }

    public String getDocumento() {
        return documento;
    }

    public void setDocumento(String documento) {
        this.documento = documento;
    }

    public String getDocumentoExtensao() {
        return documentoExtensao;
    }

    public void setDocumentoExtensao(String documentoExtensao) {
        this.documentoExtensao = documentoExtensao;
    }

    public boolean isImportacaoXMLNFE() {
        return this.getImportacaoObs().equalsIgnoreCase("XML_NFE");
    }

    public String getImportacaoObs() {
        if (importacaoObs == null) {
            importacaoObs = "";
        }
        return importacaoObs;
    }

    public void setImportacaoObs(String importacaoObs) {
        this.importacaoObs = importacaoObs;
    }

    public String getProdutoComConfigsGenericasJson() {
        return produtoComConfigsGenericasJson;
    }

    public void setProdutoComConfigsGenericasJson(String produtoComConfigsGenericasJson) {
        this.produtoComConfigsGenericasJson = produtoComConfigsGenericasJson;
    }

    public List<DocumentoCompraVO> getDocumentosCompra() {
        return documentosCompra;
    }

    public void setDocumentosCompra(List<DocumentoCompraVO> documentosCompra) {
        this.documentosCompra = documentosCompra;
    }

    public List<ContaFinanceiroCompraVO> getCompraContasLancarFinanceiro() {
        return compraContasLancarFinanceiro;
    }

    public void setCompraContasLancarFinanceiro(List<ContaFinanceiroCompraVO> compraContasLancarFinanceiro) {
        this.compraContasLancarFinanceiro = compraContasLancarFinanceiro;
    }

    public Integer getSolicitacaoCompra() {
        return solicitacaoCompra;
    }

    public void setSolicitacaoCompra(Integer solicitacaoCompra) {
        this.solicitacaoCompra = solicitacaoCompra;
    }
}

/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */

package negocio.comuns.estoque;

import annotations.arquitetura.ChaveEstrangeira;
import annotations.arquitetura.ChavePrimaria;
import negocio.comuns.arquitetura.SuperVO;
import negocio.comuns.plano.ProdutoVO;
import negocio.comuns.utilitarias.ConsistirException;

/**
 *
 * <AUTHOR>
 */
public class BalancoItensVO extends SuperVO {

    @ChavePrimaria
    protected Integer codigo;
    @ChaveEstrangeira
    protected BalancoVO balanco = new BalancoVO();
    @ChaveEstrangeira
    protected ProdutoVO produto = new ProdutoVO();
    protected Integer qtdeBalanco;
    protected Integer qtdeEstoqueAnterior;


    public BalancoItensVO(){
        super();
    }
     public String getProdutoBalanco(){
        return getProduto().getDescricao();
    }

    public static void validarDados(BalancoItensVO obj) throws ConsistirException {
        if ((obj.getBalanco() == null) || (obj.getBalanco().getCodigo() <= 0)) {
            throw new ConsistirException("O campo Balanco deve ser informado.");
        }
        if ((obj.getProduto()== null) || (obj.getProduto().getCodigo() <= 0)) {
            throw new ConsistirException("O campo Produto deve ser informado.");
        }
        if ((obj.getQtdeBalanco()== null) || (obj.getQtdeBalanco() < 0)) {
            throw new ConsistirException("O campo Quantidade Balanço deve ser informado.");
        }

    }

    public static void validarDadosTelaInclusao(BalancoItensVO obj) throws ConsistirException {
        if ((obj.getProduto()== null) || (obj.getProduto().getCodigo() <= 0)) {
            throw new ConsistirException("O campo Produto deve ser informado.");
        }
        if ((obj.getQtdeBalanco()== null) || (obj.getQtdeBalanco() < 0)) {
            throw new ConsistirException("O campo Quantidade Balanço deve ser informado.");
        }
        if ((obj.getBalanco().getEmpresa() == null) ||
           (obj.getBalanco().getEmpresa().getCodigo() == null) ||
           (obj.getBalanco().getEmpresa().getCodigo() <= 0)){
            throw new ConsistirException("Antes de incluir os produtos é necessário informar a empresa do balanço.");
        }

    }

    public BalancoVO getBalanco() {
        return balanco;
    }

    public void setBalanco(BalancoVO balanco) {
        this.balanco = balanco;
    }

    public ProdutoVO getProduto() {
        return produto;
    }

    public void setProduto(ProdutoVO produto) {
        this.produto = produto;
    }

    public Integer getQtdeBalanco() {
        return qtdeBalanco;
    }

    public void setQtdeBalanco(Integer qtdeBalanco) {
        this.qtdeBalanco = qtdeBalanco;
    }

    public Integer getQtdeEstoqueAnterior() {
        return qtdeEstoqueAnterior;
    }

    public void setQtdeEstoqueAnterior(Integer qtdeEstoqueAnterior) {
        this.qtdeEstoqueAnterior = qtdeEstoqueAnterior;
    }


    public Integer getCodigo() {
        return (codigo);
    }

    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public int hashCode(){
        return 1*17;
    }

    public boolean equals(Object obj){
        if ((obj == null) || (!(obj instanceof BalancoItensVO)))
            return false;
        return ((BalancoItensVO)obj).getProduto().getCodigo().equals(this.getProduto().getCodigo());
    }

    public Integer getDiferencaBalanco(){
        return this.qtdeBalanco - this.qtdeEstoqueAnterior;
    }

}

package negocio.comuns.estoque;

import annotations.arquitetura.ChavePrimaria;
import negocio.comuns.arquitetura.SuperVO;

public class DocumentoCompraVO extends SuperVO {

    @ChavePrimaria
    protected Integer codigo;
    protected CompraVO compra = new CompraVO();
    protected String chaveArquivo;
    protected String identificadorArquivo;
    protected String extensaoArquivo;

    public DocumentoCompraVO() {
    }

    public DocumentoCompraVO(String chaveArquivo, String identificadorArquivo, String extensaoArquivo) {
        this.chaveArquivo = chaveArquivo;
        this.identificadorArquivo = identificadorArquivo;
        this.extensaoArquivo = extensaoArquivo;
    }

    @Override
    public Integer getCodigo() {
        return codigo;
    }

    @Override
    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public CompraVO getCompra() {
        return compra;
    }

    public void setCompra(CompraVO compra) {
        this.compra = compra;
    }

    public String getChaveArquivo() {
        return chaveArquivo;
    }

    public void setChaveArquivo(String chaveArquivo) {
        this.chaveArquivo = chaveArquivo;
    }

    public String getExtensaoArquivo() {
        return extensaoArquivo;
    }

    public void setExtensaoArquivo(String extensaoArquivo) {
        this.extensaoArquivo = extensaoArquivo;
    }

    public String getIdentificadorArquivo() {
        return identificadorArquivo;
    }

    public void setIdentificadorArquivo(String identificadorArquivo) {
        this.identificadorArquivo = identificadorArquivo;
    }
}

/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */

package negocio.comuns.estoque;

import annotations.arquitetura.ChavePrimaria;
import br.com.pactosolucoes.comuns.util.Formatador;
import negocio.comuns.arquitetura.SuperVO;
import negocio.comuns.plano.ProdutoVO;
import negocio.comuns.utilitarias.ConsistirException;
import negocio.comuns.utilitarias.UteisValidacao;

import java.util.Date;

/**
 *
 * <AUTHOR>
 */
public class CompraItensVO extends SuperVO {

    @ChavePrimaria
    protected Integer codigo;
    protected CompraVO compra = new CompraVO();
    protected ProdutoVO produto = new ProdutoVO();
    protected Date dataUltimaOperacao;
    protected Date dataSaidaControle;
    protected Integer quantidade;
    protected Double valorUnitario;
    protected Double desconto;
    protected Double total;
    private Integer pontos;
    private Integer quantidadeAutorizar;


    public CompraItensVO(){
        super();
    }

    public CompraItensVO(CompraVO compra, ProdutoVO produto, Date dataUltimaOperacao, Date dataSaidaControle, Integer quantidade, Double valorUnitario, Double desconto, Double total, Integer pontos, Integer quantidadeAutorizar) {
        this.compra = compra;
        this.produto = produto;
        this.dataUltimaOperacao = dataUltimaOperacao;
        this.dataSaidaControle = dataSaidaControle;
        this.quantidade = quantidade;
        this.valorUnitario = valorUnitario;
        this.desconto = desconto;
        this.total = total;
        this.pontos = pontos;
        this.quantidadeAutorizar = quantidadeAutorizar;
    }

    public static void validarDados(CompraItensVO obj) throws ConsistirException {
        if ((obj.getCompra() == null) || (obj.getCompra().getCodigo() <= 0)) {
            throw new ConsistirException("O campo Compra deve ser informado.");
        }
        if ((obj.getProduto()== null) || (obj.getProduto().getCodigo() <= 0)) {
            throw new ConsistirException("O campo Produto deve ser informado.");
        }
        if (((obj.getQuantidade()== null) || (obj.getQuantidade().doubleValue() <= 0)) && UteisValidacao.emptyNumber(obj.getQuantidadeAutorizar())) {
            throw new ConsistirException("O campo Quantidade deve ser informado.");
        }
        if ((obj.getValorUnitario()== null) || (obj.getValorUnitario().doubleValue() <= 0)) {
            throw new ConsistirException("O campo Valor Unitário deve ser informado.");
        }

    }

    public static void validarDadosTelaInclusao(CompraItensVO obj) throws ConsistirException {
        if ((obj.getProduto()== null) || (obj.getProduto().getCodigo() <= 0)) {
            throw new ConsistirException("O campo Produto deve ser informado.");
        }
        if (((obj.getQuantidade()== null) || (obj.getQuantidade().doubleValue() <= 0)) && UteisValidacao.emptyNumber(obj.getQuantidadeAutorizar())) {
            throw new ConsistirException("O campo Quantidade deve ser informado.");
        }
        if ((obj.getValorUnitario()== null) || (obj.getValorUnitario().doubleValue() <= 0)) {
            throw new ConsistirException("O campo Valor Unitário deve ser informado.");
        }

        if ((obj.getCompra().getEmpresa() == null) ||
           (obj.getCompra().getEmpresa().getCodigo() == null) ||
           (obj.getCompra().getEmpresa().getCodigo() <= 0)){
            throw new ConsistirException("Antes de incluir os produtos é necessário informar a empresa da compra.");
        }
        if ((obj.getDesconto()) > (obj.getQuantidade() * obj.getValorUnitario())){
          throw new ConsistirException("O valor de desconto, deve ser menor ou igual ao total do produto.");
        }

    }

    public CompraItensVO clone() throws CloneNotSupportedException {
        CompraItensVO compraItensVO = new CompraItensVO();
        compraItensVO.setCompra(this.compra);
        compraItensVO.setProduto(this.produto);
        compraItensVO.setValorUnitario(this.valorUnitario);
        compraItensVO.setDesconto(this.desconto);
        compraItensVO.setValorUnitario(this.valorUnitario);
        compraItensVO.setPontos(this.pontos);
        compraItensVO.setDataUltimaOperacao(this.dataUltimaOperacao);
        compraItensVO.setDataSaidaControle(this.dataSaidaControle);
        return compraItensVO;
    }


    public CompraVO getCompra() {
        return compra;
    }

    public void setCompra(CompraVO compra) {
        this.compra = compra;
    }

    public Double getDesconto() {
        return desconto;
    }

    public void setDesconto(Double desconto) {
        this.desconto = desconto;
    }

    public ProdutoVO getProduto() {
        return produto;
    }

    public void setProduto(ProdutoVO produto) {
        this.produto = produto;
    }

    public Integer getQuantidade() {
        return quantidade;
    }

    public void setQuantidade(Integer quantidade) {
        this.quantidade = quantidade;
    }

    public Double getTotal() {
        return (((UteisValidacao.emptyNumber(this.quantidade) ? this.quantidadeAutorizar : this.quantidade) * this.valorUnitario) - this.desconto);
    }

    public void setTotal(Double total) {
        this.total = total;
    }

    public Double getValorUnitario() {
        return valorUnitario;
    }

    public void setValorUnitario(Double valorUnitario) {
        this.valorUnitario = valorUnitario;
    }

    public String getValorUnitario_Apresentar() {
        return Formatador.formatarValorMonetarioSemMoeda(getValorUnitario());
    }

    public String getDesconto_Apresentar() {
        return Formatador.formatarValorMonetarioSemMoeda(getDesconto());
    }

    public String getTotal_Apresentar() {
        return Formatador.formatarValorMonetarioSemMoeda(getTotal());
    }

    public Integer getCodigo() {
        return (codigo);
    }

    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public int hashCode(){
        return 1*17;
    }


    public boolean equals(Object obj){
        if ((obj == null) || (!(obj instanceof CompraItensVO)))
            return false;
        return (((CompraItensVO)obj).getProduto().getCodigo().equals(this.getProduto().getCodigo()) &&
                UteisValidacao.emptyString(((CompraItensVO)obj).getProduto().getDescricao())) ||
                (((CompraItensVO)obj).getProduto().getCodigo().equals(this.getProduto().getCodigo()) &&
                        ((CompraItensVO)obj).getProduto().getDescricao().equalsIgnoreCase(this.getProduto().getDescricao()))
                ;
    }

    public Date getDataUltimaOperacao() {
        return dataUltimaOperacao;
    }

    public void setDataUltimaOperacao(Date dataUltimaOperacao) {
        this.dataUltimaOperacao = dataUltimaOperacao;
    }

    public Date getDataSaidaControle() {
        return dataSaidaControle;
    }

    public void setDataSaidaControle(Date dataSaidaControle) {
        this.dataSaidaControle = dataSaidaControle;
    }

    public Integer getPontos() {
        return pontos;
    }

    public void setPontos(Integer pontos) {
        this.pontos = pontos;
    }

    public Integer getQuantidadeAutorizar() {
        return quantidadeAutorizar;
    }

    public void setQuantidadeAutorizar(Integer quantidadeAutorizar) {
        this.quantidadeAutorizar = quantidadeAutorizar;
    }
}

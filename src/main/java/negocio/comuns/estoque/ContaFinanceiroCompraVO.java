package negocio.comuns.estoque;

import annotations.arquitetura.ChavePrimaria;
import br.com.pactosolucoes.comuns.util.Formatador;
import negocio.comuns.arquitetura.SuperVO;
import negocio.comuns.utilitarias.Calendario;

import java.util.Date;

public class ContaFinanceiroCompraVO extends SuperVO {

    @ChavePrimaria
    private Integer codigo;
    protected CompraVO compra = new CompraVO();
    private Integer nrParcela;
    private Double valorParcela;
    private Date dataVencimento;
    private String codigoBarras;

    @Override
    public Integer getCodigo() {
        return codigo;
    }

    @Override
    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public CompraVO getCompra() {
        return compra;
    }

    public void setCompra(CompraVO compra) {
        this.compra = compra;
    }

    public Integer getNrParcela() {
        return nrParcela;
    }

    public void setNrParcela(Integer nrParcela) {
        this.nrParcela = nrParcela;
    }

    public Double getValorParcela() {
        return valorParcela;
    }

    public void setValorParcela(Double valorParcela) {
        this.valorParcela = valorParcela;
    }

    public Date getDataVencimento() {
        return dataVencimento;
    }

    public void setDataVencimento(Date dataVencimento) {
        this.dataVencimento = dataVencimento;
    }

    public String getDataVencimento_Apresentar() {
        if (dataVencimento != null) {
            return Calendario.getDataAplicandoFormatacao(dataVencimento, "dd/MM/yyyy");
        }
        return "";
    }

    public String getValorParcela_Apresentar(){
        if(valorParcela != null){
            return Formatador.formatarValorMonetario(valorParcela);
        }
        return "";
    }

    public String getCodigoBarras() {
        return codigoBarras;
    }

    public void setCodigoBarras(String codigoBarras) {
        this.codigoBarras = codigoBarras;
    }
}

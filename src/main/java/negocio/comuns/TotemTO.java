package negocio.comuns;

import org.json.JSONException;
import org.json.JSONObject;
import negocio.comuns.basico.enumerador.ConfigTotemEnum;
import negocio.comuns.utilitarias.Calendario;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisValidacao;
import servicos.propriedades.PropsService;

import java.util.HashMap;
import java.util.Map;

public class TotemTO {

    private String totem = "";
    private String key = "";
    private Integer empresa;
    private Map<ConfigTotemEnum, ConfiguracaoEmpresaTotemVO> configs;

    public String getTotem() {
        return totem;
    }

    public TotemTO clone(){
        TotemTO t = new TotemTO();
        t.setTotem(this.totem);
        t.setConfigs(new HashMap<ConfigTotemEnum, ConfiguracaoEmpresaTotemVO>());
        for(ConfigTotemEnum cfg : ConfigTotemEnum.values()){
            ConfiguracaoEmpresaTotemVO totemVO = this.getConfigs().get(cfg);
            t.getConfigs().put(cfg, totemVO);
        }
        return t;
    }

    public String getLinkAuto(){
        String urlZWAuto = PropsService.getPropertyValue(PropsService.urlZWAuto);
        if(UteisValidacao.emptyString(urlZWAuto)){
            return "";
        }
        JSONObject p = new JSONObject();
        try {
            p.put("totem", Uteis.retirarAcentuacaoRegex(getTotem()).replaceAll(" ", "_"));
            p.put("chave", getKey());
            p.put("codigoEmpresa", getEmpresa());
            p.put("api", PropsService.getPropertyValue(PropsService.urlZWAPI));
            p.put("momento", Uteis.getDataAplicandoFormatacao(Calendario.hoje(), "MMddyyHHmm"));
        } catch (Exception e) {
            e.printStackTrace();
        }
        return urlZWAuto +"/index.html?p="+Uteis.encriptar(p.toString(), "k3y2w4ut0");
    }


    public void setTotem(String totem) {
        this.totem = totem;
    }

    public Map<ConfigTotemEnum, ConfiguracaoEmpresaTotemVO> getConfigs() {
        if(configs == null){
            configs = new HashMap<ConfigTotemEnum, ConfiguracaoEmpresaTotemVO>();
            for(ConfigTotemEnum cfg : ConfigTotemEnum.values()){
                ConfiguracaoEmpresaTotemVO totemVO = configs.get(cfg);
                if(totemVO == null){
                    ConfiguracaoEmpresaTotemVO cfgvo = new ConfiguracaoEmpresaTotemVO();
                    cfgvo.setValor("");
                    cfgvo.setConfigTotem(cfg);
                    configs.put(cfg, cfgvo);
                }
            }
        }
        return configs;
    }

    public void setConfigs(Map<ConfigTotemEnum, ConfiguracaoEmpresaTotemVO> configs) {
        this.configs = configs;
    }

    public String getKey() {
        return key;
    }

    public void setKey(String key) {
        this.key = key;
    }

    public Integer getEmpresa() {
        return empresa;
    }

    public void setEmpresa(Integer empresa) {
        this.empresa = empresa;
    }
}

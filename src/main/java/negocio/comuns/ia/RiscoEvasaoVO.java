package negocio.comuns.ia;

import annotations.arquitetura.ChavePrimaria;
import negocio.comuns.arquitetura.SuperVO;

import java.util.Date;

public class RiscoEvasaoVO extends SuperVO {

    @ChavePrimaria
    private Integer codigo;
    private Integer cliente;
    private Date dataPredicao;
    private Date dataPredicao30Dias;
    private Float chanceSair30Dias;

    public RiscoEvasaoVO() {
    }

    public RiscoEvasaoVO(RiscoEvasaoTO riscoEvasaoTO) {
        this.setCodigo(riscoEvasaoTO.getCodigo());
        this.setCliente(riscoEvasaoTO.getCliente());
        this.setDataPredicao(riscoEvasaoTO.getDataPredicao());
        this.setDataPredicao30Dias(riscoEvasaoTO.getDataPredicao30Dias());
        this.setChanceSair30Dias(riscoEvasaoTO.getChanceSair30Dias());
    }

    @Override
    public Integer getCodigo() {
        if (codigo == null) {
            codigo = 0;
        }
        return codigo;
    }

    @Override
    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public Integer getCliente() {
        return cliente;
    }

    public void setCliente(Integer cliente) {
        this.cliente = cliente;
    }

    public Date getDataPredicao() {
        return dataPredicao;
    }

    public void setDataPredicao(Date dataPredicao) {
        this.dataPredicao = dataPredicao;
    }

    public Date getDataPredicao30Dias() {
        return dataPredicao30Dias;
    }

    public void setDataPredicao30Dias(Date dataPredicao30Dias) {
        this.dataPredicao30Dias = dataPredicao30Dias;
    }

    public Float getChanceSair30Dias() {
        return chanceSair30Dias;
    }

    public void setChanceSair30Dias(Float chanceSair30Dias) {
        this.chanceSair30Dias = chanceSair30Dias;
    }
}
package negocio.comuns.ia;

import com.fasterxml.jackson.annotation.JsonIgnore;
import negocio.comuns.arquitetura.SuperTO;
import negocio.comuns.basico.ClienteVO;

import java.util.Date;

public class RiscoEvasaoTO extends SuperTO {

    private String chave;
    @JsonIgnore
    private ClienteVO clienteVO;

    private Integer codigo;
    private Integer cliente;
    private Date dataPredicao;
    private Date dataPredicao30Dias;
    private Float chanceSair30Dias;

    @JsonIgnore
    public ClienteVO getClienteVO() {
        if (clienteVO == null) {
            clienteVO = new ClienteVO();
        }
        return clienteVO;
    }

    @JsonIgnore
    public void setClienteVO(ClienteVO clienteVO) {
        this.clienteVO = clienteVO;
    }

    public String getChave() {
        return chave;
    }

    public void setChave(String chave) {
        this.chave = chave;
    }

    public Integer getCodigo() {
        return codigo;
    }

    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public Integer getCliente() {
        return cliente;
    }

    public void setCliente(Integer cliente) {
        this.cliente = cliente;
    }

    public Date getDataPredicao() {
        return dataPredicao;
    }

    public void setDataPredicao(Date dataPredicao) {
        this.dataPredicao = dataPredicao;
    }

    public Date getDataPredicao30Dias() {
        return dataPredicao30Dias;
    }

    public void setDataPredicao30Dias(Date dataPredicao30Dias) {
        this.dataPredicao30Dias = dataPredicao30Dias;
    }

    public Float getChanceSair30Dias() {
        return chanceSair30Dias;
    }

    public void setChanceSair30Dias(Float chanceSair30Dias) {
        this.chanceSair30Dias = chanceSair30Dias;
    }
}
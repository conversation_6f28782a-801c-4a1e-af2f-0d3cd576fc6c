package negocio.comuns.nfe;

import negocio.comuns.arquitetura.SuperTO;

public class NFeProdutoImprimirTO extends SuperTO {

    private String codigoProduto;
    private String descricao;
    private String ncm;
    private String cst;
    private String cfop;
    private String unidade;
    private String quantidade;
    private String valorUnitario;
    private String valorTotal;
    private String baseICMS;
    private String valorICMS;
    private String valorIPI;
    private String aliquotaICMS;
    private String aliquotaIPI;

    public String getCodigoProduto() {
        if (codigoProduto == null) {
            codigoProduto = "";
        }
        return codigoProduto;
    }

    public void setCodigoProduto(String codigoProduto) {
        this.codigoProduto = codigoProduto;
    }

    public String getDescricao() {
        if (descricao == null) {
            descricao = "";
        }
        return descricao;
    }

    public void setDescricao(String descricao) {
        this.descricao = descricao;
    }

    public String getNcm() {
        if (ncm == null) {
            ncm = "";
        }
        return ncm;
    }

    public void setNcm(String ncm) {
        this.ncm = ncm;
    }

    public String getCfop() {
        if (cfop == null) {
            cfop = "";
        }
        return cfop;
    }

    public void setCfop(String cfop) {
        this.cfop = cfop;
    }

    public String getUnidade() {
        if (unidade == null) {
            unidade = "";
        }
        return unidade;
    }

    public void setUnidade(String unidade) {
        this.unidade = unidade;
    }

    public String getQuantidade() {
        if (quantidade == null) {
            quantidade = "";
        }
        return quantidade;
    }

    public void setQuantidade(String quantidade) {
        this.quantidade = quantidade;
    }

    public String getValorUnitario() {
        if (valorUnitario == null) {
            valorUnitario = "";
        }
        return valorUnitario;
    }

    public void setValorUnitario(String valorUnitario) {
        this.valorUnitario = valorUnitario;
    }

    public String getValorTotal() {
        if (valorTotal == null) {
            valorTotal = "";
        }
        return valorTotal;
    }

    public void setValorTotal(String valorTotal) {
        this.valorTotal = valorTotal;
    }

    public String getBaseICMS() {
        if (baseICMS == null) {
            baseICMS = "";
        }
        return baseICMS;
    }

    public void setBaseICMS(String baseICMS) {
        this.baseICMS = baseICMS;
    }

    public String getValorICMS() {
        if (valorICMS == null) {
            valorICMS = "";
        }
        return valorICMS;
    }

    public void setValorICMS(String valorICMS) {
        this.valorICMS = valorICMS;
    }

    public String getValorIPI() {
        if (valorIPI == null) {
            valorIPI = "";
        }
        return valorIPI;
    }

    public void setValorIPI(String valorIPI) {
        this.valorIPI = valorIPI;
    }

    public String getAliquotaICMS() {
        if (aliquotaICMS == null) {
            aliquotaICMS = "";
        }
        return aliquotaICMS;
    }

    public void setAliquotaICMS(String aliquotaICMS) {
        this.aliquotaICMS = aliquotaICMS;
    }

    public String getAliquotaIPI() {
        if (aliquotaIPI == null) {
            aliquotaIPI = "";
        }
        return aliquotaIPI;
    }

    public void setAliquotaIPI(String aliquotaIPI) {
        this.aliquotaIPI = aliquotaIPI;
    }

    public String getCst() {
        if (cst == null) {
            cst = "";
        }
        return cst;
    }

    public void setCst(String cst) {
        this.cst = cst;
    }
}

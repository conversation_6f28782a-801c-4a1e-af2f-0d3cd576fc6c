package negocio.comuns.nfe;

import negocio.comuns.arquitetura.SuperTO;


public class NFCeProdutoImprimirTO extends SuperTO {

    private String codigo;
    private String descricao;
    private String quantidade;
    private String unidade;
    private String valorUnitario;
    private String valorDesconto;
    private String valorAcrescimo;
    private String valorTotal;
    private String cofins;

    public String getCodigo() {
        if (codigo == null) {
            codigo = "";
        }
        return codigo;
    }

    public void setCodigo(String codigo) {
        this.codigo = codigo;
    }

    public String getDescricao() {
        if (descricao == null) {
            descricao = "";
        }
        return descricao;
    }

    public void setDescricao(String descricao) {
        this.descricao = descricao;
    }

    public String getQuantidade() {
        if (quantidade == null) {
            quantidade = "";
        }
        return quantidade;
    }

    public void setQuantidade(String quantidade) {
        this.quantidade = quantidade;
    }

    public String getUnidade() {
        if (unidade == null) {
            unidade = "";
        }
        return unidade;
    }

    public void setUnidade(String unidade) {
        this.unidade = unidade;
    }

    public String getValorUnitario() {
        if (valorUnitario == null) {
            valorUnitario = "";
        }
        return valorUnitario;
    }

    public void setValorUnitario(String valorUnitario) {
        this.valorUnitario = valorUnitario;
    }

    public String getValorDesconto() {
        if (valorDesconto == null) {
            valorDesconto = "";
        }
        return valorDesconto;
    }

    public void setValorDesconto(String valorDesconto) {
        this.valorDesconto = valorDesconto;
    }

    public String getValorAcrescimo() {
        if (valorAcrescimo == null) {
            valorAcrescimo = "";
        }
        return valorAcrescimo;
    }

    public void setValorAcrescimo(String valorAcrescimo) {
        this.valorAcrescimo = valorAcrescimo;
    }

    public String getValorTotal() {
        if (valorTotal == null) {
            valorTotal = "";
        }
        return valorTotal;
    }

    public void setValorTotal(String valorTotal) {
        this.valorTotal = valorTotal;
    }

    public void setCofins(String cofins) {
        this.cofins = cofins;
    }

    public String getCofins() {
        return cofins;
    }
}

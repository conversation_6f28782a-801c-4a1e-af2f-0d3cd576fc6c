package negocio.comuns.nfe;

import annotations.arquitetura.ChaveEstrangeira;
import annotations.arquitetura.ChavePrimaria;
import negocio.comuns.arquitetura.SuperVO;

import java.util.Date;

public class OperacaoNotaVO extends SuperVO {

    @ChavePrimaria
    private Integer id_OperacaoRPS;
    @ChaveEstrangeira
    private Integer id_RPS;
    @ChaveEstrangeira
    private Integer id_Usuario;
    private String nomeDoUsuario;
    private Date dataHora;
    private String operacao;
    private String descricao;
    private boolean finalizado = false;
    private String observacao;

    public OperacaoNotaVO() {
        super();
    }

    public Integer getId_OperacaoRPS() {
        return id_OperacaoRPS;
    }

    public void setId_OperacaoRPS(Integer id_OperacaoRPS) {
        this.id_OperacaoRPS = id_OperacaoRPS;
    }

    public Integer getId_RPS() {
        return id_RPS;
    }

    public void setId_RPS(Integer id_RPS) {
        this.id_RPS = id_RPS;
    }

    public Integer getId_Usuario() {
        return id_Usuario;
    }

    public void setId_Usuario(Integer id_Usuario) {
        this.id_Usuario = id_Usuario;
    }

    public String getNomeDoUsuario() {
        return nomeDoUsuario;
    }

    public void setNomeDoUsuario(String nomeDoUsuario) {
        this.nomeDoUsuario = nomeDoUsuario;
    }

    public Date getDataHora() {
        return dataHora;
    }

    public void setDataHora(Date dataHora) {
        this.dataHora = dataHora;
    }

    public String getOperacao() {
        return operacao;
    }

    public void setOperacao(String operacao) {
        this.operacao = operacao;
    }

    public String getDescricao() {
        return descricao;
    }

    public void setDescricao(String descricao) {
        this.descricao = descricao;
    }

    public boolean isFinalizado() {
        return finalizado;
    }

    public String getConcluido() {
        return (isFinalizado()) ? "Sim" : "Não";
    }

    public void setFinalizado(boolean finalizado) {
        this.finalizado = finalizado;
    }

    public String getObservacao() {
        if (observacao == null) {
            observacao = "";
        }
        return observacao;
    }

    public void setObservacao(String observacao) {
        this.observacao = observacao;
    }
}

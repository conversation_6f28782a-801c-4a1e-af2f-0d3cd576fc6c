package negocio.comuns.nfe;

import annotations.arquitetura.ChaveEstrangeira;
import annotations.arquitetura.ChavePrimaria;
import annotations.arquitetura.NaoControlarLogAlteracao;
import br.com.pactosolucoes.comuns.util.Formatador;
import org.json.JSONException;
import org.json.JSONObject;
import negocio.comuns.arquitetura.SuperVO;
import negocio.comuns.financeiro.NotasFiscaisTO;
import negocio.comuns.nfe.enumerador.StatusNotaEnum;
import negocio.comuns.utilitarias.Calendario;
import negocio.comuns.utilitarias.ConsistirException;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisValidacao;
import servicos.propriedades.PropsService;

import java.util.Calendar;
import java.util.Date;

public class NotaFiscalDeServicoVO extends SuperVO {

    @ChavePrimaria
    private Integer idRPS;
    private Integer idLote;
    @ChaveEstrangeira
    private LoteNFeVO lote;
    private String status;
    private Date dataEmissao;
    private String naturezaOperacao;
    private String serieRPS;
    private Integer numeroRPS;
    private String numeroNota;
    private Date dataProcessamento;
    private String inscricaoMunicipalCons;
    private String cpfCnpjCons = "";
    private String razaoSocialCons;
    private String nomeAluno;
    private String tipoLogradouroCons;
    private String logradouroCons;
    private String numeroEnderecoCons;
    private String complementoEnderecoCons;
    private String tipoBairroCons;
    private String bairroCons;
    private String telefoneCons;
    @ChaveEstrangeira
    private MunicipioNFeVO cidadeCons;
    private String cepCons;
    private String emailCons;
    private String emailConsParaEnvio;
    private String codAtividade;
    private String itemListaServico;
    private String codigoCnae;
    private String codigoTributacaoMunicipio;
    private Double aliquotaAtividade;
    private String tipoRecolhimento;
    @ChaveEstrangeira
    private MunicipioNFeVO cidadePrest;
    private String tributacao;
    private Double valorServicos;
    private Double valorDeducoes;
    private Double valorTotal;
    private Double valorLiquido;
    private Double valorPIS;
    private Double valorCOFINS;
    private Double valorINSS;
    private Double valorIR;
    private Double valorIRRFR;
    private Double valorCSLL;
    private Double aliquotaPIS;
    private Double aliquotaCOFINS;
    private Double aliquotaINSS;
    private Double aliquotaIR;
    private Double aliquotaCSLL;
    private Integer ISSRetido;
    private Double outrasRetencoes;
    private Double baseCalculo;
    private Double valorISSRetido;
    private Double descontoIncondicionado;
    private Double descontoCondicionado;
    private String descricao;
    private String justificativaParaCancelar = "";
    private String motivo = "";
    private boolean emailEnviado = false;
    @NaoControlarLogAlteracao
    private boolean marcado = false;
    private String codigoVerificacao = "";
    private String inscricaoEstadual;
    private String cfdf;
    private String observacao;
    private Integer exigibilidadeISS;
    @NaoControlarLogAlteracao
    private Date dataCancelamento;
    private Integer idReenvio;
    private boolean excluido = false;
    private Date dataEmissaoReenvio;
    private boolean existeDadosImpressao = false;
    private String idReferencia;
    private boolean alterouAliquotaSimples = false;

    public NotaFiscalDeServicoVO() {
        super();
    }

    public static void validarDados(NotaFiscalDeServicoVO obj) throws ConsistirException {
        if (!obj.getValidarDados()) {
            return;
        }

        if (obj.getNumeroNota() == null) {
            throw new ConsistirException("O campo Número da Nota (RPS) deve ser informado.");
        }
    }

    public void atualizarDadosReenvio(JSONObject objAtualizar) {
        //Dados referente a cliente
        String telefone = objAtualizar.optString("telefone");
        if (!UteisValidacao.emptyString(telefone)) {
            setTelefoneCons(telefone);
        }

        String complemento = objAtualizar.optString("complemento");
        if (!UteisValidacao.emptyString(complemento)) {
            setComplementoEnderecoCons(complemento);
        }

        String endereco = objAtualizar.optString("endereco");
        if (!UteisValidacao.emptyString(endereco)) {
            setLogradouroCons(endereco);
        }

        String numero = objAtualizar.optString("numero");
        if (!UteisValidacao.emptyString(numero)) {
            setNumeroEnderecoCons(numero);
        }

        String bairro = objAtualizar.optString("bairro");
        if (!UteisValidacao.emptyString(bairro)) {
            setBairroCons(bairro);
        }

        String cpf = objAtualizar.optString("cpf");
        if (!UteisValidacao.emptyString(cpf)) {
            setCpfCnpjCons(cpf);
        }

        String nome = objAtualizar.optString("nome");
        if (!UteisValidacao.emptyString(nome)) {
            setNomeAluno(nome);
        }

        String email = objAtualizar.optString("email");
        if (!UteisValidacao.emptyString(email)) {
            setEmailCons(email);
        }

        String cep = objAtualizar.optString("cep");
        if (!UteisValidacao.emptyString(cep)) {
            setCepCons(cep);
        }

        //Dados referente a empresa
        try {
            String aliquotaPIS = objAtualizar.getString("aliquotaPIS");
            if (!UteisValidacao.emptyString(aliquotaPIS)) {
                setAliquotaPIS(Double.parseDouble(aliquotaPIS));
                setValorPIS((getAliquotaPIS() / 100) * getValorServicos());
            }
        } catch (JSONException e) {
            e.printStackTrace();
        } catch (NumberFormatException e) {
            e.printStackTrace();
        }

        try {
            String aliquotaCOFINS = objAtualizar.getString("aliquotaCOFINS");
            if (!UteisValidacao.emptyString(aliquotaCOFINS)) {
                setAliquotaCOFINS(Double.parseDouble(aliquotaCOFINS));
                setValorCOFINS((getAliquotaCOFINS() / 100) * getValorServicos());
            }
        } catch (JSONException e) {
            e.printStackTrace();
        } catch (NumberFormatException e) {
            e.printStackTrace();
        }

        try {
            String aliquotaIRRF = objAtualizar.getString("aliquotaIRRF");
            if (!UteisValidacao.emptyString(aliquotaIRRF)) {
                setAliquotaIR(Double.parseDouble(aliquotaIRRF));
                setValorIR((getAliquotaIR() / 100) * getValorServicos());
                setValorIRRFR((getAliquotaIR() / 100) * getValorServicos());
            }
        } catch (JSONException e) {
            e.printStackTrace();
        } catch (NumberFormatException e) {
            e.printStackTrace();
        }

        String codigoListaServico = objAtualizar.optString("codigoListaServico");
        if (!UteisValidacao.emptyString(codigoListaServico)){
            setItemListaServico(codigoListaServico);
        }

        String codigoTributacaoMunicipio = objAtualizar.optString("codigoTributacaoMunicipio");
        if (!UteisValidacao.emptyString(codigoTributacaoMunicipio)){
            setCodigoTributacaoMunicipio(codigoTributacaoMunicipio);
        }

        String codigoCNAE = objAtualizar.optString("codigoCNAE");
        if (!UteisValidacao.emptyString(codigoCNAE)){
            setCodigoCnae(codigoCNAE);
        }

        String exigibilidadeISS = objAtualizar.optString("exigibilidadeISS");
        if (!UteisValidacao.emptyString(exigibilidadeISS)){
            setExigibilidadeISS(Integer.parseInt(exigibilidadeISS));
        }
    }

    public Integer getIdRPS() {
        return idRPS;
    }

    public void setIdRPS(Integer idRPS) {
        this.idRPS = idRPS;
    }

    public LoteNFeVO getLote() {
        if (lote == null) {
            lote = new LoteNFeVO();
        }
        return lote;
    }

    public void setLote(LoteNFeVO lote) {
        this.lote = lote;
    }

    public String getStatus() {
        if (idReenvio != null && idReenvio != 0) {
            return status + " - ID_Lote " + idReenvio;
        }
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public Date getDataEmissao() {
        return dataEmissao;
    }

    public void setDataEmissao(Date dataEmissao) {
        this.dataEmissao = dataEmissao;
    }

    public String getNaturezaOperacao() {
        return naturezaOperacao;
    }

    public void setNaturezaOperacao(String naturezaOperacao) {
        this.naturezaOperacao = naturezaOperacao;
    }

    public String getSerieRPS() {
        return serieRPS;
    }

    public void setSerieRPS(String serieRPS) {
        this.serieRPS = serieRPS;
    }

    public Integer getNumeroRPS() {
        return numeroRPS;
    }

    public void setNumeroRPS(Integer numeroRPS) {
        this.numeroRPS = numeroRPS;
    }

    public String getNumeroNota() {
        return numeroNota;
    }

    public void setNumeroNota(String numeroNota) {
        this.numeroNota = numeroNota;
    }

    public Date getDataProcessamento() {
        return dataProcessamento;
    }

    public void setDataProcessamento(Date dataProcessamento) {
        this.dataProcessamento = dataProcessamento;
    }

    public String getInscricaoMunicipalCons() {
        return inscricaoMunicipalCons;
    }

    public void setInscricaoMunicipalCons(String inscricaoMunicipalCons) {
        this.inscricaoMunicipalCons = inscricaoMunicipalCons;
    }

    public String getCpfCnpjCons() {
        return cpfCnpjCons;
    }

    public void setCpfCnpjCons(String cpfCnpjCons) {
        this.cpfCnpjCons = cpfCnpjCons;
    }

    public String getCpfCnpjFormatado() {
        if (cpfCnpjCons.length() == 14) {
            return Formatador.formatarString("##.###.###/####-##", cpfCnpjCons);
        } else {
            return Formatador.formatarString("###.###.###-##", cpfCnpjCons);
        }
    }

    public String getRazaoSocialCons() {
        return razaoSocialCons;
    }

    public void setRazaoSocialCons(String razaoSocialCons) {
        this.razaoSocialCons = razaoSocialCons;
    }

    public String getTipoLogradouroCons() {
        return tipoLogradouroCons;
    }

    public void setTipoLogradouroCons(String tipoLogradouroCons) {
        this.tipoLogradouroCons = tipoLogradouroCons;
    }

    public String getLogradouroCons() {
        return logradouroCons;
    }

    public void setLogradouroCons(String logradouroCons) {
        this.logradouroCons = logradouroCons;
    }

    public String getNumeroEnderecoCons() {
        return numeroEnderecoCons;
    }

    public void setNumeroEnderecoCons(String numeroEnderecoCons) {
        this.numeroEnderecoCons = numeroEnderecoCons;
    }

    public String getComplementoEnderecoCons() {
        return complementoEnderecoCons;
    }

    public void setComplementoEnderecoCons(String complementoEnderecoCons) {
        this.complementoEnderecoCons = complementoEnderecoCons;
    }

    public String getTipoBairroCons() {
        return tipoBairroCons;
    }

    public void setTipoBairroCons(String tipoBairroCons) {
        this.tipoBairroCons = tipoBairroCons;
    }

    public String getBairroCons() {
        return bairroCons;
    }

    public void setBairroCons(String bairroCons) {
        this.bairroCons = bairroCons;
    }

    public MunicipioNFeVO getCidadeCons() {
        return cidadeCons;
    }

    public void setCidadeCons(MunicipioNFeVO cidadeCons) {
        this.cidadeCons = cidadeCons;
    }

    public String getCepCons() {
        return cepCons;
    }

    public void setCepCons(String cepCons) {
        this.cepCons = cepCons;
    }

    public String getEmailCons() {
        return emailCons;
    }

    public void setEmailCons(String emailCons) {
        this.emailCons = emailCons;
    }

    public String getCodAtividade() {
        return codAtividade;
    }

    public void setCodAtividade(String codAtividade) {
        this.codAtividade = codAtividade;
    }

    public String getItemListaServico() {
        return itemListaServico;
    }

    public void setItemListaServico(String itemListaServico) {
        this.itemListaServico = itemListaServico;
    }

    public String getCodigoCnae() {
        return codigoCnae;
    }

    public void setCodigoCnae(String codigoCnae) {
        this.codigoCnae = codigoCnae;
    }

    public String getCodigoTributacaoMunicipio() {
        return codigoTributacaoMunicipio;
    }

    public void setCodigoTributacaoMunicipio(String codigoTributacaoMunicipio) {
        this.codigoTributacaoMunicipio = codigoTributacaoMunicipio;
    }

    public Double getAliquotaAtividade() {
        return aliquotaAtividade;
    }

    public void setAliquotaAtividade(Double aliquotaAtividade) {
        this.aliquotaAtividade = aliquotaAtividade;
    }

    public String getTipoRecolhimento() {
        return tipoRecolhimento;
    }

    public void setTipoRecolhimento(String tipoRecolhimento) {
        this.tipoRecolhimento = tipoRecolhimento;
    }

    public MunicipioNFeVO getCidadePrest() {
        if (cidadePrest == null) {
            cidadePrest = new MunicipioNFeVO();
        }
        return cidadePrest;
    }

    public void setCidadePrest(MunicipioNFeVO cidadePrest) {
        this.cidadePrest = cidadePrest;
    }

    public String getTributacao() {
        return tributacao;
    }

    public void setTributacao(String tributacao) {
        this.tributacao = tributacao;
    }

    public Double getValorServicos() {
        return valorServicos;
    }

    public void setValorServicos(Double valorServicos) {
        this.valorServicos = valorServicos;
    }

    public Double getValorDeducoes() {
        return valorDeducoes;
    }

    public void setValorDeducoes(Double valorDeducoes) {
        this.valorDeducoes = valorDeducoes;
    }

    public Double getValorTotal() {
        return valorTotal;
    }

    public void setValorTotal(Double valorTotal) {
        this.valorTotal = valorTotal;
    }

    public Double getValorLiquido() {
        return valorLiquido;
    }

    public void setValorLiquido(Double valorLiquido) {
        this.valorLiquido = valorLiquido;
    }

    public Double getValorPIS() {
        return valorPIS;
    }

    public void setValorPIS(Double valorPIS) {
        this.valorPIS = valorPIS;
    }

    public Double getValorCOFINS() {
        return valorCOFINS;
    }

    public void setValorCOFINS(Double valorCOFINS) {
        this.valorCOFINS = valorCOFINS;
    }

    public Double getValorINSS() {
        return valorINSS;
    }

    public void setValorINSS(Double valorINSS) {
        this.valorINSS = valorINSS;
    }

    public Double getValorIR() {
        return valorIR;
    }

    public void setValorIR(Double valorIR) {
        this.valorIR = valorIR;
    }

    public Double getValorIRRFR() {
        return valorIRRFR;
    }

    public void setValorIRRFR(Double valorIRRFR) {
        this.valorIRRFR = valorIRRFR;
    }

    public Double getValorCSLL() {
        return valorCSLL;
    }

    public void setValorCSLL(Double valorCSLL) {
        this.valorCSLL = valorCSLL;
    }

    public Double getAliquotaPIS() {
        return aliquotaPIS;
    }

    public void setAliquotaPIS(Double aliquotaPIS) {
        this.aliquotaPIS = aliquotaPIS;
    }

    public Double getAliquotaCOFINS() {
        return aliquotaCOFINS;
    }

    public void setAliquotaCOFINS(Double aliquotaCOFINS) {
        this.aliquotaCOFINS = aliquotaCOFINS;
    }

    public Double getAliquotaINSS() {
        return aliquotaINSS;
    }

    public void setAliquotaINSS(Double aliquotaINSS) {
        this.aliquotaINSS = aliquotaINSS;
    }

    public Double getAliquotaIR() {
        return aliquotaIR;
    }

    public void setAliquotaIR(Double aliquotaIR) {
        this.aliquotaIR = aliquotaIR;
    }

    public Double getAliquotaCSLL() {
        return aliquotaCSLL;
    }

    public void setAliquotaCSLL(Double aliquotaCSLL) {
        this.aliquotaCSLL = aliquotaCSLL;
    }

    public Integer getISSRetido() {
        return ISSRetido;
    }

    public void setISSRetido(Integer ISSRetido) {
        this.ISSRetido = ISSRetido;
    }

    public Double getOutrasRetencoes() {
        return outrasRetencoes;
    }

    public void setOutrasRetencoes(Double outrasRetencoes) {
        this.outrasRetencoes = outrasRetencoes;
    }

    public Double getBaseCalculo() {
        return baseCalculo;
    }

    public void setBaseCalculo(Double baseCalculo) {
        this.baseCalculo = baseCalculo;
    }

    public Double getValorISSRetido() {
        return valorISSRetido;
    }

    public void setValorISSRetido(Double valorISSRetido) {
        this.valorISSRetido = valorISSRetido;
    }

    public Double getDescontoIncondicionado() {
        return descontoIncondicionado;
    }

    public void setDescontoIncondicionado(Double descontoIncondicionado) {
        this.descontoIncondicionado = descontoIncondicionado;
    }

    public Double getDescontoCondicionado() {
        return descontoCondicionado;
    }

    public void setDescontoCondicionado(Double descontoCondicionado) {
        this.descontoCondicionado = descontoCondicionado;
    }

    public String getDescricao() {
        return descricao;
    }

    public void setDescricao(String descricao) {
        this.descricao = descricao;
    }

    public String getJustificativaParaCancelar() {
        return justificativaParaCancelar;
    }

    public void setJustificativaParaCancelar(String justificativaParaCancelar) {
        this.justificativaParaCancelar = justificativaParaCancelar;
    }

    public boolean getNotaEstaCancelada() {
        return status.equals(StatusNotaEnum.CANCELADO.getDescricao());
    }

    public boolean getNotaEstaAutorizada() {
        return status.equals("Autorizado");
    }

    public boolean getNotaNaoAutorizada() {
        return status.equals("Não autorizado");
    }

    public String getClasseDoStatus() {
        if (status.equals("Cancelando")) {
            return "orange";
        } else if (status.equals("Cancelado")) {
            return "red";
        } else if (status.equals("Autorizado")) {
            return "green";
        } else if (status.equals("Enviando")) {
            return "gray";
        } else if (status.equals("Processando")) {
            return "blue";
        } else {
            return "";
        }
    }

    public boolean isEmailEnviado() {
        return emailEnviado;
    }

    public void setEmailEnviado(boolean emailEnviado) {
        this.emailEnviado = emailEnviado;
    }

    public String getEmailFoiEnviado() {
        return (isEmailEnviado()) ? "Sim" : "Não";
    }

    public String getMotivo() {
        return motivo;
    }

    public void setMotivo(String motivo) {
        this.motivo = motivo;
    }

    public boolean isMarcado() {
        return marcado;
    }

    public void setMarcado(boolean marcado) {
        this.marcado = marcado;
    }

    public String getCodigoVerificacao() {
        return codigoVerificacao;
    }

    public void setCodigoVerificacao(String codigoVerificacao) {
        this.codigoVerificacao = codigoVerificacao;
    }

    public boolean getPodeCancelarNota() throws Exception {
        try {
            if (getLote().getEmpresaNFeVO().getMunicipio().getHorasCancelamento() == 0) {
                return false;
            }
            if (getLote().getEmpresaNFeVO().getMunicipio().isCancelarAteFinalMes()) {
                String mesAnoAtual = Uteis.getDataMesAnoConcatenado(Calendario.hoje());
                String mesAnoNotaProcessada = Uteis.getDataMesAnoConcatenado(getDataProcessamento());
                return mesAnoNotaProcessada.equals(mesAnoAtual);
            } else {
                Date dataLimite = Uteis.somarCampoData(getDataProcessamento(), Calendar.HOUR, getLote().getEmpresaNFeVO().getMunicipio().getHorasCancelamento());
                return Calendario.maiorComHora(dataLimite, Calendario.hoje());
            }
        } catch (Exception e) {
            return false;
        }
    }

    public Boolean getCnpj() {
        if (!getCpfCnpjCons().isEmpty()){
            if (getCpfCnpjCons().length() > 11){
                return true;
            }
        }
        return false;
    }

    public String getTelefoneCons() {
        if (telefoneCons == null) {
            telefoneCons = "";
        }
        return telefoneCons;
    }

    public void setTelefoneCons(String telefoneCons) {
        this.telefoneCons = telefoneCons;
    }

    public String getEmailConsParaEnvio() {
        if (emailConsParaEnvio == null) {
            emailConsParaEnvio = "";
        }
        return emailConsParaEnvio;
    }

    public void setEmailConsParaEnvio(String emailConsParaEnvio) {
        this.emailConsParaEnvio = emailConsParaEnvio;
    }

    public String getInscricaoEstadual() {
        if (inscricaoEstadual == null) {
            inscricaoEstadual = "";
        }
        return inscricaoEstadual;
    }

    public void setInscricaoEstadual(String inscricaoEstadual) {
        this.inscricaoEstadual = inscricaoEstadual;
    }

    public String getCfdf() {
        if (cfdf == null) {
            cfdf = "";
        }
        return cfdf;
    }

    public void setCfdf(String cfdf) {
        this.cfdf = cfdf;
    }

    public String getObservacao() {
        if (observacao == null) {
            observacao = "";
        }
        return observacao;
    }

    public void setObservacao(String observacao) {
        this.observacao = observacao;
    }

    public Integer getExigibilidadeISS() {
        return exigibilidadeISS;
    }

    public void setExigibilidadeISS(Integer exigibilidadeISS) {
        this.exigibilidadeISS = exigibilidadeISS;
    }

    public JSONObject toJson() throws JSONException {
        JSONObject ret = new JSONObject();
        ret.put("aliquotaAtividade", aliquotaAtividade);
        ret.put("baseCalculo", baseCalculo);
        ret.put("codAtividade", codAtividade == null ? "" : codAtividade);
        ret.put("dataEmissao", dataEmissao);
        ret.put("dataProcessamento", dataProcessamento);
        ret.put("descricao", descricao);
        ret.put("itemListaServico", itemListaServico);
        ret.put("numeroNota", numeroNota);
        ret.put("valorLiquido", valorLiquido);
        ret.put("serieRPS", serieRPS);
        ret.put("valorISSRetido", valorISSRetido);
        ret.put("valorTotal", valorTotal);
        ret.put("codigoVerificacao", codigoVerificacao);
        ret.put("observacao", observacao);
        ret.put("lote", getLote().getId_lote());
        return ret;
    }

    public void fromJson(JSONObject obj) throws Exception {
        this.aliquotaAtividade = obj.getDouble("aliquotaAtividade");
        this.baseCalculo = obj.getDouble("baseCalculo");
        this.codAtividade = obj.getString("codAtividade");
        this.dataEmissao = Uteis.getDate(obj.getString("dataEmissao"), "yyyy-MM-dd HH:mm:ss");
        this.dataProcessamento = Uteis.getDate(obj.getString("dataProcessamento"), "yyyy-MM-dd HH:mm:ss");
        this.descricao = obj.getString("descricao");
        this.itemListaServico = obj.getString("itemListaServico");
        this.numeroNota = obj.getString("numeroNota");
        this.valorLiquido = obj.getDouble("valorLiquido");
        this.valorLiquido = obj.getDouble("valorLiquido");
        this.serieRPS = obj.getString("serieRPS");
        this.valorISSRetido = obj.getDouble("valorISSRetido");
        this.valorTotal = obj.getDouble("valorTotal");
        this.codigoVerificacao = obj.getString("codigoVerificacao");
        this.observacao = obj.getString("observacao");
        this.setLote(new LoteNFeVO());
        this.getLote().setId_lote(obj.getInt("lote"));
    }

    public Date getDataCancelamento() {
        return dataCancelamento;
    }

    public void setDataCancelamento(Date dataCancelamento) {
        this.dataCancelamento = dataCancelamento;
    }

    public Integer getIdReenvio() {
        if (idReenvio == null) {
            idReenvio = 0;
        }
        return idReenvio;
    }

    public void setIdReenvio(Integer idReenvio) {
        this.idReenvio = idReenvio;
    }

    public boolean isExcluido() {
        return excluido;
    }

    public void setExcluido(boolean excluido) {
        this.excluido = excluido;
    }

    public String getNomeAluno() {
        if (nomeAluno == null) {
            nomeAluno = "";
        }
        return nomeAluno;
    }

    public void setNomeAluno(String nomeAluno) {
        this.nomeAluno = nomeAluno;
    }

    public boolean getApresentarNomeAluno() {
        return (!UteisValidacao.emptyString(getNomeAluno()) && !getRazaoSocialCons().equals(getNomeAluno()));
    }

    public Date getDataEmissaoReenvio() {
        if (dataEmissaoReenvio == null) {
            dataEmissaoReenvio = Calendario.hoje();
        }
        return dataEmissaoReenvio;
    }

    public void setDataEmissaoReenvio(Date dataEmissaoReenvio) {
        this.dataEmissaoReenvio = dataEmissaoReenvio;
    }

    public NotasFiscaisTO toTO() {
        return new NotasFiscaisTO(this);
    }

    public boolean getNotaNFe() {
        //SABER SE É NFE
        return getCidadePrest().getTipoLayout().equals(3);
    }

    public String getLinkDownloadPDF(){
        try {
            return PropsService.getPropertyValue(PropsService.urlModuloNFSe) + "/nota?rps=" + getIdRPS();
        } catch (Exception ex) {
            return "";
        }
    }

    public Integer getIdLote() {
        if (idLote == null) {
            idLote = 0;
        }
        return idLote;
    }

    public void setIdLote(Integer idLote) {
        this.idLote = idLote;
    }

    public boolean isExisteDadosImpressao() {
        return existeDadosImpressao;
    }

    public void setExisteDadosImpressao(boolean existeDadosImpressao) {
        this.existeDadosImpressao = existeDadosImpressao;
    }

    public String getIdReferencia() {
        if (idReferencia == null) {
            idReferencia = "";
        }
        return idReferencia;
    }

    public void setIdReferencia(String idReferencia) {
        this.idReferencia = idReferencia;
    }

    public void setAlterouAliquotaSimples(boolean alterouAliquotaSimples) {
        this.alterouAliquotaSimples = alterouAliquotaSimples;
    }

    public boolean getAlterouAliquotaSimples() {
        return alterouAliquotaSimples;
    }
}

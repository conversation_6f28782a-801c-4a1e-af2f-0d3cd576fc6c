package negocio.comuns.nfe.enumerador;

/*
 * Created by <PERSON><PERSON> on 29/03/2017.
 */
public enum TipoNotaEnum {

    NFSE(0, "NFS-e"),
    NFCE(1, "NFC-e") ;

    private Integer codigo;
    private String descricao;

    private TipoNotaEnum(Integer codigo, String descricao) {
        this.codigo = codigo;
        this.descricao = descricao;
    }

    public static TipoNotaEnum getTipo(int consulta) {
        for (TipoNotaEnum origem : TipoNotaEnum.values()) {
            if (origem.getCodigo() == consulta) {
                return origem;
            }
        }
        return NFSE;
    }

    public Integer getCodigo() {
        return codigo;
    }

    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public String getDescricao() {
        return descricao;
    }

    public void setDescricao(String descricao) {
        this.descricao = descricao;
    }
}

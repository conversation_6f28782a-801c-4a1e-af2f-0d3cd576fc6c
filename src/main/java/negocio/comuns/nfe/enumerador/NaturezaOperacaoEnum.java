package negocio.comuns.nfe.enumerador;

/**
 * Created by <PERSON><PERSON>
 */
public enum NaturezaOperacaoEnum {

    Cod1(1, "Tributação no Município"),
    Cod2(2, "Tributação Fora Município"),
    Cod3(3, "<PERSON><PERSON><PERSON>"),
    Cod4(4, "<PERSON><PERSON><PERSON>"),
    Cod5(5, "Suspensa Decisão Judicial"),
    Cod6(6, "Suspensa Procedimento Administrativo"),
    Cod7(7, "Não Incidencia"),
    Cod51(51, "Não Incidencia"),
    Cod52(52, "Tributação no Município Sem ISS52"),
    Cod58(58, "Tributação no Município Sem ISS52"),
    Cod59(59, "Simples Nacional 59"),
    Cod61(61, "noTributacaoNoMunicipio61"),
    Cod62(62, "noTributacaoNoMunicipioSemISS62"),
    Cod63(63, "noTributacaoForaMunicipio63"),
    Cod64(64, "noTributacaoForaMunicipioSemISS64"),
    Cod68(68, "noNaoTributa68"),
    Cod69(69, "noSimplesNacional69"),
    Cod78(78, "noNaoTributa78"),
    Cod50(50, "no50"),
    Cod53(53, "no53"),
    Cod57(57, "no57"),
    Cod60(60, "no60"),
    Cod67(67, "no67"),
    Cod70(70, "no67"),
    Cod79(79, "no79"),
    Cod101(101, "noISSDevidoItajai"),
    Cod111(111, "noISSDevidoOutroMunicipio"),
    Cod121(121, "noISSFixo"),
    Cod201(201, "noISSRetido"),
    Cod301(301, "noOperacaoImune"),
    Cod501(501, "noISSDevidoItajaiSN"),
    Cod511(511, "noISSDevidoOutroMunicipioSN"),
    Cod541(541, "noMEISN"),
    Cod551(551, "noEscritorioContabilSN"),
    Cod601(601, "noISSRetidoSN"),
    Cod701(701, "noOperacaoImuneSN");

    private Integer codigo;
    private String descricao;

    private NaturezaOperacaoEnum(Integer codigo, String descricao) {
        this.codigo = codigo;
        this.descricao = descricao;
    }

    public static NaturezaOperacaoEnum getTipo(int consulta) {
        for (NaturezaOperacaoEnum origem : NaturezaOperacaoEnum.values()) {
            if (origem.getCodigo() == consulta) {
                return origem;
            }
        }
        return null;
    }

    public Integer getCodigo() {
        return codigo;
    }

    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public String getDescricao() {
        return descricao;
    }

    public void setDescricao(String descricao) {
        this.descricao = descricao;
    }
}

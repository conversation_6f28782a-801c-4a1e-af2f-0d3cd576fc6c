package negocio.comuns.nfe.enumerador;

import negocio.comuns.utilitarias.EnumNaoEncontradaException;

import static negocio.comuns.utilitarias.CampoRecuperavelEnum.DESCRICAO;

/*
 * Created by <PERSON><PERSON> on 26/03/2018.
 */
public enum StatusNFCeEnum {

    NENHUM          (0, "Nenhum"),
    ENVIANDO        (1, "Enviando"),
    AUTORIZADO      (2, "Autorizado"),
    NAO_AUTORIZADO  (3, "Não autorizado"),
    CANCELANDO      (4, "Cancelando"),
    CANCELADO       (5, "Cancelado"),
    REENVIADO       (6, "Reenviado");


    private Integer codigo;
    private String descricao;

    private StatusNFCeEnum(Integer codigo, String descricao) {
        this.codigo = codigo;
        this.descricao = descricao;
    }

    public static StatusNFCeEnum getStatus(int codigo) {
        for (StatusNFCeEnum origem : StatusNFCeEnum.values()) {
            if (origem.getCodigo() == codigo) {
                return origem;
            }
        }
        return null;
    }

    public static StatusNFCeEnum getEnumByDescricao(String descricao) {
        for (StatusNFCeEnum status : StatusNFCeEnum.values()) {
            if (status.getDescricao().toUpperCase().equals(descricao.toUpperCase())) {
                return status;
            }
        }

        return null;
    }

    public static StatusNFCeEnum getEnumByDescricaoThrowsException(String descricao) throws EnumNaoEncontradaException {
        StatusNFCeEnum enumByDescricao = getEnumByDescricao(descricao);

        if (enumByDescricao != null) {
            return enumByDescricao;
        }

        throw new EnumNaoEncontradaException(StatusNFCeEnum.class, DESCRICAO, descricao);
    }

    public Integer getCodigo() {
        return codigo;
    }

    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public String getDescricao() {
        return descricao;
    }

    public void setDescricao(String descricao) {
        this.descricao = descricao;
    }
}

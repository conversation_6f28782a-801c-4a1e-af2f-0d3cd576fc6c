package negocio.comuns.nfe.enumerador;

/*
 * Created by <PERSON><PERSON> on 26/02/2018.
 */
public enum ResultadoEnvioNFSeEnum {

    OK(0, "rtOK", ""),
    ERROINESPERADO(1, "rtErroInesperado", ""),
    LOTENAOINFORMADO(2, "rtLoteNaoInformado", ""),
    CIDADENAOHOMOLOGADA(3, "rtCidadeNaoHomologada", ""),
    LOTECPFCNPJINVALIDO(4, "rtLoteCPFCNPJInvalido", ""),
    LOTEEMPRESANAOENCONTRADA(5, "rtLoteEmpresaNaoEncontrada", ""),
    LOTESEMRPS(6, "rtLoteSemRPS", ""),
    LOTEVALORZERADO(7, "rtLoteValorZerado", ""),
    CIDADENAOENCONTRADA(8, "rtCidadeNaoEncontrada", ""),
    RPSINVALIDO(9, "rtRPSInvalido", ""),
    USUARIOINVALIDO(10, "rtUsuarioInvalido", ""),
    SENHAINVALIDA(11, "rtSenhaInvalida", ""),
    USUARIOSEMPERMISSAO(12, "rtUsuarioSemPermissao", ""),
    CERTIFICADOEXPIRADO(13, "rtCertificadoExpirado", ""),
    IDNFCEINVALIDO(14, "rtIdNFCeInvalido", "");

    private Integer codigo;
    private String descricao;
    private String mensagem;


    private ResultadoEnvioNFSeEnum(Integer codigo, String descricao, String mensagem) {
        this.codigo = codigo;
        this.descricao = descricao;
        this.mensagem = mensagem;
    }

    public static ResultadoEnvioNFSeEnum getTipo(int consulta) {
        for (ResultadoEnvioNFSeEnum origem : ResultadoEnvioNFSeEnum.values()) {
            if (origem.getCodigo() == consulta) {
                return origem;
            }
        }
        return ERROINESPERADO;
    }

    public Integer getCodigo() {
        return codigo;
    }

    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public String getDescricao() {
        return descricao;
    }

    public void setDescricao(String descricao) {
        this.descricao = descricao;
    }

    public String getMensagem() {
        return mensagem;
    }

    public void setMensagem(String mensagem) {
        this.mensagem = mensagem;
    }
}

package negocio.comuns.nfe.enumerador;

/*
 * Created by <PERSON><PERSON> on 20/06/2017.
 */
public enum StatusNotaEnum {

    NENHUM          (0, "", false),
    ENVIANDO        (1, "Enviando", false),
    PROCESSANDO     (2, "Processando", false),
    AUTORIZADO      (3, "Autorizado", false),
    NAO_AUTORIZADO  (4, "Não autorizado", true),
    CANCELANDO      (5, "Cancelando", false),
    CANCELADO       (6, "Cancelado", true),
    REENVIADO       (7, "Reenviado", false);


    private Integer codigo;
    private String descricao;
    private boolean podeEstornar;

    private StatusNotaEnum(Integer codigo, String descricao, boolean podeEstornar) {
        this.codigo = codigo;
        this.descricao = descricao;
        this.podeEstornar = podeEstornar;
    }

    public static StatusNotaEnum getStatus(int codigo) {
        for (StatusNotaEnum origem : StatusNotaEnum.values()) {
            if (origem.getCodigo() == codigo) {
                return origem;
            }
        }
        return null;
    }

    public Integer getCodigo() {
        return codigo;
    }

    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public String getDescricao() {
        return descricao;
    }

    public void setDescricao(String descricao) {
        this.descricao = descricao;
    }

    public boolean isPodeEstornar() {
        return podeEstornar;
    }

    public void setPodeEstornar(boolean podeEstornar) {
        this.podeEstornar = podeEstornar;
    }
}

package negocio.comuns.nfe.enumerador;

import negocio.comuns.utilitarias.EnumNaoEncontradaException;

import static negocio.comuns.utilitarias.CampoRecuperavelEnum.NOME_ENUM;

/**
 * Repositório de Modelos de Documento Fiscal disponíveis no Sistema.
 *
 * <AUTHOR>
 * @since 06/12/2018
 */
public enum ModeloDocumentoFiscal {

    NFCe(65, "Nota Fiscal do Consumidor Eletrônica"),
    NFSe(null, "Nota Fiscal de Serviços Eletrônica");

    private final Integer numeroModelo;
    private final String descricao;

    ModeloDocumentoFiscal(Integer numeroModelo, String descricao) {
        this.numeroModelo = numeroModelo;
        this.descricao = descricao;
    }

    public static ModeloDocumentoFiscal getEnumByNameThrowsException(String descricao) throws EnumNaoEncontradaException {
        for (ModeloDocumentoFiscal modelo : ModeloDocumentoFiscal.values()) {
            if (modelo.name().equals(descricao)) {
                return modelo;
            }
        }

        throw new EnumNaoEncontradaException(ModeloDocumentoFiscal.class, NOME_ENUM, descricao);
    }

    public Integer getNumeroModelo() {
        return numeroModelo;
    }

    public String getDescricao() {
        return descricao;
    }
}

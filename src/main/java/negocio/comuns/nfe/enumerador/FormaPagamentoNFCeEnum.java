package negocio.comuns.nfe.enumerador;

/*
 * Created by <PERSON><PERSON> on 28/06/2018.
 */
public enum FormaPagamentoNFCeEnum {

    NENHUM              (999, ""),
    DINHEIRO            (0, "Dinheiro"),
    CHEQUE              (1, "Cheque"),
    CREDITO             (2, "Cartão de Crédito"),
    DEBITO              (3, "Cartão de Débito"),
    CREDITO_NA_LOJA     (4, "Crédito na Loja (tipo bonus de promoção)"),
    VALE_ALIMENTACAO    (5, "Vale alimentação"),
    VALE_REFEICAO       (6, "Vale refeição"),
    VALE_PRESENTE       (7, "Vale presente"),
    VALE_COMBUSTIVEL    (8, "Vale combustível"),
    DUPLICATA           (9, "Duplica<PERSON>"),
    BOLETO              (10, "Boleto");


    private Integer codigo;
    private String descricao;

    private FormaPagamentoNFCeEnum(Integer codigo, String descricao) {
        this.codigo = codigo;
        this.descricao = descricao;
    }

    public static FormaPagamentoNFCeEnum obterPorCodigo(int codigo) {
        for (FormaPagamentoNFCeEnum origem : FormaPagamentoNFCeEnum.values()) {
            if (origem.getCodigo() == codigo) {
                return origem;
            }
        }
        return NENHUM;
    }

    public Integer getCodigo() {
        return codigo;
    }

    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public String getDescricao() {
        return descricao;
    }

    public void setDescricao(String descricao) {
        this.descricao = descricao;
    }
}

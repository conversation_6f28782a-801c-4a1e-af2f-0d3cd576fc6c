package negocio.comuns.nfe;

import annotations.arquitetura.ChaveEstrangeira;
import annotations.arquitetura.ChavePrimaria;
import negocio.comuns.arquitetura.SuperVO;

import java.util.Date;

public class NumerosInutilizarVO extends SuperVO {

    private Integer id_NumerosInutilizar;
    private Integer id_Empresa;
    private Integer serie;
    private Integer numInicial;
    private Integer numFinal;
    private boolean finalizado = false;
    private String retorno;

    public NumerosInutilizarVO() {
        super();
    }


    public Integer getId_NumerosInutilizar() {
        if (id_NumerosInutilizar == null) {
            id_NumerosInutilizar = 0;
        }
        return id_NumerosInutilizar;
    }

    public void setId_NumerosInutilizar(Integer id_NumerosInutilizar) {
        this.id_NumerosInutilizar = id_NumerosInutilizar;
    }

    public Integer getId_Empresa() {
        if (id_Empresa == null) {
            id_Empresa = 0;
        }
        return id_Empresa;
    }

    public void setId_Empresa(Integer id_Empresa) {
        this.id_Empresa = id_Empresa;
    }

    public Integer getSerie() {
        if (serie == null) {
            serie = 0;
        }
        return serie;
    }

    public void setSerie(Integer serie) {
        this.serie = serie;
    }

    public Integer getNumInicial() {
        if (numInicial == null) {
            numInicial = 0;
        }
        return numInicial;
    }

    public void setNumInicial(Integer numInicial) {
        this.numInicial = numInicial;
    }

    public Integer getNumFinal() {
        if (numFinal == null) {
            numFinal = 0;
        }
        return numFinal;
    }

    public void setNumFinal(Integer numFinal) {
        this.numFinal = numFinal;
    }

    public boolean isFinalizado() {
        return finalizado;
    }

    public void setFinalizado(boolean finalizado) {
        this.finalizado = finalizado;
    }

    public String getRetorno() {
        if (retorno == null) {
            retorno = "";
        }
        return retorno;
    }

    public void setRetorno(String retorno) {
        this.retorno = retorno;
    }

    public String getFinalizadoApresentar() {
        if(isFinalizado()) {
            return "SIM";
        } else {
            return "NÃO";
        }
    }
}

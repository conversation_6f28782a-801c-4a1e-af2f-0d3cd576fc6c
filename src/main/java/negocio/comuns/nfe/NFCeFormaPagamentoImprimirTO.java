package negocio.comuns.nfe;

import negocio.comuns.arquitetura.SuperTO;

import java.io.InputStream;


public class NFCeFormaPagamentoImprimirTO extends SuperTO {

    private String codigo;
    private String descricao;
    private String valor;

    public String getCodigo() {
        if (codigo == null) {
            codigo = "";
        }
        return codigo;
    }

    public void setCodigo(String codigo) {
        this.codigo = codigo;
    }

    public String getDescricao() {
        if (descricao == null) {
            descricao = "";
        }
        return descricao;
    }

    public void setDescricao(String descricao) {
        this.descricao = descricao;
    }

    public String getValor() {
        if (valor == null) {
            valor = "";
        }
        return valor;
    }

    public void setValor(String valor) {
        this.valor = valor;
    }
}

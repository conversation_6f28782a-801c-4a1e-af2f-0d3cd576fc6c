package negocio.comuns.nfe;

import annotations.arquitetura.ChavePrimaria;
import negocio.comuns.arquitetura.SuperVO;
import negocio.comuns.utilitarias.ConsistirException;

public class MunicipioNFeVO extends SuperVO {

    @ChavePrimaria
    private Integer id_Municipio = 0;
    private String nome = "";
    private String uf = "";
    private Integer codSIAFI = 0;
    private Integer codIBGE = 0;
    private Integer tipoLayout = 0;
    private Integer horasCancelamento;
    //PERMITE CANCELAR ATÉ O FIM DO MES EM QUE A NOTA FOI PROCESSADA
    private boolean cancelarAteFinalMes = false;
    private boolean requerCertificado = false;

    public MunicipioNFeVO() {
        super();
    }

    public static void validarDados(MunicipioNFeVO obj) throws ConsistirException {
        if (!obj.getValidarDados()) {
            return;
        }
        if (obj.getNome().equals("")) {
            throw new ConsistirException("O campo Nome (Município) deve ser informado.");
        }
        if (obj.getUf().equals("")) {
            throw new ConsistirException("O campo UF (Município) deve ser informado.");
        }
        if (obj.getCodIBGE() == null || obj.getCodIBGE() == 0) {
            throw new ConsistirException("O campo Código IBGE (Município) deve ser informado.");
        }

        if (obj.getCodSIAFI() == null || obj.getCodSIAFI() == 0) {
            throw new ConsistirException("O campo Código SIAFI (Município) deve ser informado.");
        }
    }

    public Integer getId_Municipio() {
        return id_Municipio;
    }

    public void setId_Municipio(Integer id_Municipio) {
        this.id_Municipio = id_Municipio;
    }

    public String getNome() {
        return nome;
    }

    public void setNome(String nome) {
        this.nome = nome;
    }

    public String getUf() {
        if (uf == null) {
            uf = "";
        }
        return uf;
    }

    public void setUf(String uf) {
        this.uf = uf;
    }

    public Integer getCodIBGE() {
        return codIBGE;
    }

    public void setCodIBGE(Integer codIBGE) {
        this.codIBGE = codIBGE;
    }

    public Integer getCodSIAFI() {
        return codSIAFI;
    }

    public void setCodSIAFI(Integer codSIAFI) {
        this.codSIAFI = codSIAFI;
    }

    public Integer getTipoLayout() {
        if (tipoLayout == null) {
            tipoLayout = 0;
        }
        return tipoLayout;
    }

    public void setTipoLayout(Integer tipoLayout) {
        this.tipoLayout = tipoLayout;
    }

    public Integer getHorasCancelamento() {
        if (horasCancelamento == null) {
            horasCancelamento = 0;
        }
        return horasCancelamento;
    }

    public void setHorasCancelamento(Integer horasCancelamento) {
        this.horasCancelamento = horasCancelamento;
    }

    public boolean isCancelarAteFinalMes() {
        return cancelarAteFinalMes;
    }

    public void setCancelarAteFinalMes(boolean cancelarAteFinalMes) {
        this.cancelarAteFinalMes = cancelarAteFinalMes;
    }

    public boolean isRequerCertificado() {
        return requerCertificado;
    }

    public void setRequerCertificado(boolean requerCertificado) {
        this.requerCertificado = requerCertificado;
    }
}

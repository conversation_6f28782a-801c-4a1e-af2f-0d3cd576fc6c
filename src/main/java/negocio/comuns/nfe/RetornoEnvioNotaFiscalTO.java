package negocio.comuns.nfe;

import negocio.comuns.arquitetura.SuperTO;
import negocio.comuns.nfe.enumerador.ResultadoEnvioNFSeEnum;

public class RetornoEnvioNotaFiscalTO extends SuperTO {

    private Integer idLote;
    private String mensagem;
    private ResultadoEnvioNFSeEnum resultadoEnvio;

    public Integer getIdLote() {
        if (idLote == null) {
            idLote = 0;
        }
        return idLote;
    }

    public void setIdLote(Integer idLote) {
        this.idLote = idLote;
    }

    public String getMensagem() {
        if (mensagem == null) {
            mensagem = "";
        }
        return mensagem;
    }

    public void setMensagem(String mensagem) {
        this.mensagem = mensagem;
    }

    public ResultadoEnvioNFSeEnum getResultadoEnvio() {
        if (resultadoEnvio == null) {
            resultadoEnvio = ResultadoEnvioNFSeEnum.ERROINESPERADO;
        }
        return resultadoEnvio;
    }

    public void setResultadoEnvio(ResultadoEnvioNFSeEnum resultadoEnvio) {
        this.resultadoEnvio = resultadoEnvio;
    }
}

package negocio.comuns.nfe;

import negocio.comuns.nfe.enumerador.TipoNotaEnum;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisValidacao;

import java.util.Date;
import negocio.comuns.arquitetura.SuperTO;

public class FiltroNFeTO extends SuperTO {

    private static final long serialVersionUID = 4748608939626667260L;
    private String numeroDaNota;
    private String cpf_cnpj = "";
    private String razaoSocial = "";
    private String cidadeDoCliente = "";
    private String cidadeDoServico = "";
    private Date dataEmissao_inicio = new Date();
    private Date dataEmissao_fim = new Date();
    private Date dataProcessamento_inicio;
    private Date dataProcessamento_fim;
    private String status = "";
    private Integer idEmpresa;
    private String codigoVerificacao = "";
    private Long numeroRps = null;
    private String idRps;
    private boolean somenteExcluidas = false;
    private Integer tipoNota;
    private String id_NFCe;
    private String numeroEnvioNFCe;
    private String id_Lote;

    public FiltroNFeTO() {
    }

    public FiltroNFeTO(int idEmpresa) {
        this.idEmpresa = idEmpresa;
    }

    public String getNumeroDaNota() {
        if(numeroDaNota == null) {
            numeroDaNota = "";
        }
        return numeroDaNota;
    }

    public void setNumeroDaNota(String numeroDaNota) {
        this.numeroDaNota = numeroDaNota;
    }

    public String getCpf_cnpj() {
        return cpf_cnpj;
    }

    public void setCpf_cnpj(String cpf_cnpj) {
        this.cpf_cnpj = cpf_cnpj;
    }

    public String getRazaoSocial() {
        return razaoSocial;
    }

    public void setRazaoSocial(String razaoSocial) {
        this.razaoSocial = razaoSocial;
    }

    public String getCidadeDoCliente() {
        return cidadeDoCliente;
    }

    public void setCidadeDoCliente(String cidadeDoCliente) {
        this.cidadeDoCliente = cidadeDoCliente;
    }

    public String getCidadeDoServico() {
        return cidadeDoServico;
    }

    public void setCidadeDoServico(String cidadeDoServico) {
        this.cidadeDoServico = cidadeDoServico;
    }

    public Date getDataEmissao_inicio() {
        return dataEmissao_inicio;
    }

    public void setDataEmissao_inicio(Date dataEmissao_inicio) {
        this.dataEmissao_inicio = dataEmissao_inicio;
    }

    public Date getDataEmissao_fim() {
        return dataEmissao_fim;
    }

    public void setDataEmissao_fim(Date dataEmissao_fim) {
        this.dataEmissao_fim = dataEmissao_fim;
    }

    public Date getDataProcessamento_inicio() {
        return dataProcessamento_inicio;
    }

    public void setDataProcessamento_inicio(Date dataProcessamento_inicio) {
        this.dataProcessamento_inicio = dataProcessamento_inicio;
    }

    public Date getDataProcessamento_fim() {
        return dataProcessamento_fim;
    }

    public void setDataProcessamento_fim(Date dataProcessamento_fim) {
        this.dataProcessamento_fim = dataProcessamento_fim;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public Integer getIdEmpresa() {
        return idEmpresa;
    }

    public void setIdEmpresa(Integer idEmpresa) {
        this.idEmpresa = idEmpresa;
    }

    public String getCodigoVerificacao() {
        return codigoVerificacao;
    }

    public void setCodigoVerificacao(String codigoVerificacao) {
        this.codigoVerificacao = codigoVerificacao;
    }

    public Long getNumeroRps() {
        return numeroRps;
    }

    public void setNumeroRps(Long numeroRps) {
        this.numeroRps = numeroRps;
    }

    public String obtenhaCondicoes() throws Exception {
        StringBuilder condicoes = new StringBuilder();

        if (somenteExcluidas) {
            condicoes.append(" AND r.excluido = 1 ");
        } else {
            condicoes.append(" AND r.excluido = 0 ");
        }

        if (!UteisValidacao.emptyString(getId_Lote())) {
            condicoes.append(" AND r.id_lote in (").append(getId_Lote()).append(")");
        }

        if (!UteisValidacao.emptyString(getNumeroDaNota())) {
            condicoes.append(" AND r.NumeroNota = '").append(getNumeroDaNota()).append("'");
        }

        if (numeroRps != null && numeroRps != 0) {
            condicoes.append(" AND r.numeroRPS = '").append(numeroRps).append("'");
        }

        if (!cpf_cnpj.equals("")) {
            condicoes.append(" AND r.CPFCNPJCons LIKE '").append(Uteis.removerMascara(cpf_cnpj)).append("'");
        }

        if (!UteisValidacao.emptyString(razaoSocial)) {
            condicoes.append(" AND (r.RazaoSocialCons LIKE '%").append(razaoSocial).append("%' OR r.nomealuno LIKE '%").append(razaoSocial).append("%') ");
        }

        if (!codigoVerificacao.equals("")) {
            condicoes.append(" AND r.CodigoVerificacao LIKE '%").append(codigoVerificacao).append("%'");
        }

        if (!UteisValidacao.emptyString(status)) {
            condicoes.append(" AND r.Status LIKE '").append(status).append("'");
        }

        if (dataEmissao_inicio != null) {
            condicoes.append(" AND r.DataEmissao >= '").append(Uteis.getDataJDBC(dataEmissao_inicio)).append(" 00:00:00'");
        }

        if (dataEmissao_fim != null) {
            condicoes.append(" AND r.DataEmissao <= '").append(Uteis.getDataJDBC(dataEmissao_fim)).append(" 23:59:59'");
        }

        if (dataProcessamento_inicio != null) {
            condicoes.append(" AND r.DataProcessamento >= '").append(Uteis.getDataJDBC(dataProcessamento_inicio)).append(" 00:00:00'");
        }

        if (dataProcessamento_fim != null) {
            condicoes.append(" AND r.DataProcessamento <= '").append(Uteis.getDataJDBC(dataProcessamento_fim)).append(" 23:59:59'");
        }
        return condicoes.toString();
    }

    public boolean isSomenteExcluidas() {
        return somenteExcluidas;
    }

    public void setSomenteExcluidas(boolean somenteExcluidas) {
        this.somenteExcluidas = somenteExcluidas;
    }

    public String obtenhaCondicoesNFCe() throws Exception {
        StringBuilder condicoes = new StringBuilder();

        if (!UteisValidacao.emptyString(getId_NFCe())) {
            condicoes.append(" AND n.Id_NFCe = '").append(id_NFCe).append("'");
        }

        if (!UteisValidacao.emptyString(getNumeroEnvioNFCe())) {
            condicoes.append(" AND n.NumeroEnvio = '").append(numeroEnvioNFCe).append("'");
        }

        if (!UteisValidacao.emptyString(getCpf_cnpj())) {
            condicoes.append(" AND n.destcpfcnpj = '").append(Uteis.removerMascara(cpf_cnpj)).append("'");
        }

        if (!UteisValidacao.emptyString(getRazaoSocial())) {
            condicoes.append(" AND (n.destnome LIKE '%").append(razaoSocial).append("%' OR n.nomealuno LIKE '%").append(razaoSocial).append("%') ");
        }

        if (dataEmissao_inicio != null) {
            condicoes.append(" AND n.DataHoraEmissao >= '").append(Uteis.getDataJDBC(dataEmissao_inicio)).append(" 00:00:00'");
        }

        if (dataEmissao_fim != null) {
            condicoes.append(" AND n.DataHoraEmissao <= '").append(Uteis.getDataJDBC(dataEmissao_fim)).append(" 23:59:59'");
        }

        if (!UteisValidacao.emptyString(getNumeroDaNota().trim())) {
            condicoes.append(" AND n.numeroenvio LIKE '").append(getNumeroDaNota().trim()).append("' ");
        }

        if (!UteisValidacao.emptyString(codigoVerificacao)) {
            condicoes.append(" AND r.chave LIKE '%").append(codigoVerificacao).append("%' ");
        }
        return condicoes.toString();
    }

    public String getId_NFCe() {
        if (id_NFCe == null) {
            id_NFCe = "";
        }
        return id_NFCe;
    }

    public void setId_NFCe(String id_NFCe) {
        this.id_NFCe = id_NFCe;
    }

    public String getNumeroEnvioNFCe() {
        if (numeroEnvioNFCe == null) {
            numeroEnvioNFCe = "";
        }
        return numeroEnvioNFCe;
    }

    public void setNumeroEnvioNFCe(String numeroEnvioNFCe) {
        this.numeroEnvioNFCe = numeroEnvioNFCe;
    }

    public Integer getTipoNota() {
        if (tipoNota == null) {
            tipoNota = TipoNotaEnum.NFSE.getCodigo();
        }
        return tipoNota;
    }

    public void setTipoNota(Integer tipoNota) {
        this.tipoNota = tipoNota;
    }

    public String getIdRps() {
        if (idRps == null) {
            idRps = "";
        }
        return idRps;
    }

    public void setIdRps(String idRps) {
        this.idRps = idRps;
    }

    public String getId_Lote() {
        if (id_Lote == null) {
            id_Lote = "";
        }
        return id_Lote;
    }

    public void setId_Lote(String id_Lote) {
        this.id_Lote = id_Lote;
    }
}

package negocio.comuns.nfe;

import annotations.arquitetura.ChaveEstrangeira;
import annotations.arquitetura.ChavePrimaria;

/*
 * Created by <PERSON><PERSON> on 29/03/2017.
 */
public class ItemNFCeVO {

    @ChavePrimaria
    private Integer id_ItemNFCe;
    @ChaveEstrangeira
    private NotaFiscalConsumidorNFCeVO notaFiscalConsumidorNFCeVO;
    private Double valorUnitario;
    private String descUnidade;
    private Integer quantidade;
    private String descricao;
    private String ncm;
    private String cfop;
    private Double baseCalculoICMS;
    private Double aliquotaICMS;
    private Double valorICMS;
    private Double baseCalculoPIS;
    private Double aliquotaPIS;
    private Double valorPIS;
    private Double baseCalculoCOFINS;
    private Double aliquotaCOFINS;
    private Double valorCOFINS;
    private Double baseCalculoISSQN;
    private Double aliquotaISSQN;
    private Double valorISSQN;
    private String codigoMunicipioISSQN;
    private String codigoListaServicoISSQN;
    private String informacaoAdicional;


    public Integer getId_ItemNFCe() {
        if (id_ItemNFCe == null) {
            id_ItemNFCe = 0;
        }
        return id_ItemNFCe;
    }

    public void setId_ItemNFCe(Integer id_ItemNFCe) {
        this.id_ItemNFCe = id_ItemNFCe;
    }

    public NotaFiscalConsumidorNFCeVO getNotaFiscalConsumidorNFCeVO() {
        if (notaFiscalConsumidorNFCeVO == null) {
            notaFiscalConsumidorNFCeVO = new NotaFiscalConsumidorNFCeVO();
        }
        return notaFiscalConsumidorNFCeVO;
    }

    public void setNotaFiscalConsumidorNFCeVO(NotaFiscalConsumidorNFCeVO notaFiscalConsumidorNFCeVO) {
        this.notaFiscalConsumidorNFCeVO = notaFiscalConsumidorNFCeVO;
    }

    public Double getValorUnitario() {
        if (valorUnitario == null) {
            valorUnitario = 0.0;
        }
        return valorUnitario;
    }

    public void setValorUnitario(Double valorUnitario) {
        this.valorUnitario = valorUnitario;
    }

    public String getDescUnidade() {
        if (descUnidade == null) {
            descUnidade = "";
        }
        return descUnidade;
    }

    public void setDescUnidade(String descUnidade) {
        this.descUnidade = descUnidade;
    }

    public Integer getQuantidade() {
        if (quantidade == null) {
            quantidade = 0;
        }
        return quantidade;
    }

    public void setQuantidade(Integer quantidade) {
        this.quantidade = quantidade;
    }

    public String getDescricao() {
        if (descricao == null) {
            descricao = "";
        }
        return descricao;
    }

    public void setDescricao(String descricao) {
        this.descricao = descricao;
    }

    public String getNcm() {
        if (ncm == null) {
            ncm = "";
        }
        return ncm;
    }

    public void setNcm(String ncm) {
        this.ncm = ncm;
    }

    public String getCfop() {
        if (cfop == null) {
            cfop = "";
        }
        return cfop;
    }

    public void setCfop(String cfop) {
        this.cfop = cfop;
    }

    public Double getBaseCalculoICMS() {
        if (baseCalculoICMS == null) {
            baseCalculoICMS = 0.0;
        }
        return baseCalculoICMS;
    }

    public void setBaseCalculoICMS(Double baseCalculoICMS) {
        this.baseCalculoICMS = baseCalculoICMS;
    }

    public Double getAliquotaICMS() {
        if (aliquotaICMS == null) {
            aliquotaICMS = 0.0;
        }
        return aliquotaICMS;
    }

    public void setAliquotaICMS(Double aliquotaICMS) {
        this.aliquotaICMS = aliquotaICMS;
    }

    public Double getValorICMS() {
        if (valorICMS == null) {
            valorICMS = 0.0;
        }
        return valorICMS;
    }

    public void setValorICMS(Double valorICMS) {
        this.valorICMS = valorICMS;
    }

    public Double getBaseCalculoPIS() {
        if (baseCalculoPIS == null) {
            baseCalculoPIS = 0.0;
        }
        return baseCalculoPIS;
    }

    public void setBaseCalculoPIS(Double baseCalculoPIS) {
        this.baseCalculoPIS = baseCalculoPIS;
    }

    public Double getAliquotaPIS() {
        if (aliquotaPIS == null) {
            aliquotaPIS = 0.0;
        }
        return aliquotaPIS;

    }

    public void setAliquotaPIS(Double aliquotaPIS) {
        this.aliquotaPIS = aliquotaPIS;
    }

    public Double getValorPIS() {
        if (valorPIS == null) {
            valorPIS = 0.0;
        }
        return valorPIS;
    }

    public void setValorPIS(Double valorPIS) {
        this.valorPIS = valorPIS;
    }

    public Double getBaseCalculoCOFINS() {
        if (baseCalculoCOFINS == null) {
            baseCalculoCOFINS = 0.0;
        }
        return baseCalculoCOFINS;
    }

    public void setBaseCalculoCOFINS(Double baseCalculoCOFINS) {
        this.baseCalculoCOFINS = baseCalculoCOFINS;
    }

    public Double getAliquotaCOFINS() {
        if (aliquotaCOFINS == null) {
            aliquotaCOFINS = 0.0;
        }
        return aliquotaCOFINS;
    }

    public void setAliquotaCOFINS(Double aliquotaCOFINS) {
        this.aliquotaCOFINS = aliquotaCOFINS;
    }

    public Double getValorCOFINS() {
        if (valorCOFINS == null) {
            valorCOFINS = 0.0;
        }
        return valorCOFINS;
    }

    public void setValorCOFINS(Double valorCOFINS) {
        this.valorCOFINS = valorCOFINS;
    }

    public Double getBaseCalculoISSQN() {
        if (baseCalculoISSQN == null) {
            baseCalculoISSQN = 0.0;
        }
        return baseCalculoISSQN;
    }

    public void setBaseCalculoISSQN(Double baseCalculoISSQN) {
        this.baseCalculoISSQN = baseCalculoISSQN;
    }

    public Double getAliquotaISSQN() {
        if (aliquotaISSQN == null) {
            aliquotaISSQN = 0.0;
        }
        return aliquotaISSQN;
    }

    public void setAliquotaISSQN(Double aliquotaISSQN) {
        this.aliquotaISSQN = aliquotaISSQN;
    }

    public Double getValorISSQN() {
        if (valorISSQN == null) {
            valorISSQN = 0.0;
        }
        return valorISSQN;
    }

    public void setValorISSQN(Double valorISSQN) {
        this.valorISSQN = valorISSQN;
    }

    public String getCodigoMunicipioISSQN() {
        if (codigoMunicipioISSQN == null) {
            codigoMunicipioISSQN = "";
        }
        return codigoMunicipioISSQN;
    }

    public void setCodigoMunicipioISSQN(String codigoMunicipioISSQN) {
        this.codigoMunicipioISSQN = codigoMunicipioISSQN;
    }

    public String getCodigoListaServicoISSQN() {
        if (codigoListaServicoISSQN == null) {
            codigoListaServicoISSQN = "";
        }
        return codigoListaServicoISSQN;
    }

    public void setCodigoListaServicoISSQN(String codigoListaServicoISSQN) {
        this.codigoListaServicoISSQN = codigoListaServicoISSQN;
    }

    public String getInformacaoAdicional() {
        if (informacaoAdicional == null) {
            informacaoAdicional = "";
        }
        return informacaoAdicional;
    }

    public void setInformacaoAdicional(String informacaoAdicional) {
        this.informacaoAdicional = informacaoAdicional;
    }

    public Double getValorTotal() {
        return getValorUnitario() * getQuantidade();
    }
}

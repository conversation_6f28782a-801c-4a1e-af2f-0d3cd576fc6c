package negocio.comuns.nfe;

import annotations.arquitetura.ChaveEstrangeira;
import annotations.arquitetura.ChavePrimaria;
import negocio.comuns.arquitetura.SuperVO;

import java.util.Date;

public class LogOperacaoNotaVO extends SuperVO {

    @ChavePrimaria
    private Integer Id_LogOperacaoRPS;
    @ChaveEstrangeira
    private Integer Id_OperacaoRPS;
    private Date dataHora;
    private String resultado;
    private NotaFiscalDeServicoVO notaFiscalVO;

    public LogOperacaoNotaVO() {
        super();
    }


    public Integer getId_LogOperacaoRPS() {
        return Id_LogOperacaoRPS;
    }

    public void setId_LogOperacaoRPS(Integer id_LogOperacaoRPS) {
        Id_LogOperacaoRPS = id_LogOperacaoRPS;
    }

    public Integer getId_OperacaoRPS() {
        return Id_OperacaoRPS;
    }

    public void setId_OperacaoRPS(Integer id_OperacaoRPS) {
        Id_OperacaoRPS = id_OperacaoRPS;
    }

    public Date getDataHora() {
        return dataHora;
    }

    public void setDataHora(Date dataHora) {
        this.dataHora = dataHora;
    }

    public String getResultado() {
        if (resultado == null) {
            resultado= "";
        }
        return resultado;
    }

    public void setResultado(String resultado) {
        this.resultado = resultado;
    }

    public NotaFiscalDeServicoVO getNotaFiscalVO() {
        return notaFiscalVO;
    }

    public void setNotaFiscalVO(NotaFiscalDeServicoVO notaFiscalVO) {
        this.notaFiscalVO = notaFiscalVO;
    }
}

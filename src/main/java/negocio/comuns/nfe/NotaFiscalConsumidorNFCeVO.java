package negocio.comuns.nfe;

import annotations.arquitetura.ChaveEstrangeira;
import annotations.arquitetura.ChavePrimaria;
import negocio.comuns.arquitetura.SuperVO;
import negocio.comuns.financeiro.NotasFiscaisTO;
import negocio.comuns.nfe.enumerador.StatusNFCeEnum;
import negocio.comuns.utilitarias.Calendario;
import negocio.comuns.utilitarias.Uteis;
import servicos.propriedades.PropsService;

import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.List;

/*
 * Created by <PERSON><PERSON> on 29/03/2017.
 */
public class NotaFiscalConsumidorNFCeVO extends SuperVO {

    @ChavePrimaria
    private Integer id_NFCe;
    private String numeroEnvio;
    @ChaveEstrangeira
    private EmpresaNFeVO empresaNFe;
    private String destCPFCNPJ;
    private Date dataHoraEmissao;
    private String destNome;
    private String destFone;
    private String destCEP;
    private String destLogradouro;
    private String destNumero;
    private String destComplemento;
    private String destBairro;
    @ChaveEstrangeira
    private MunicipioNFeVO destCidade;
    private String complemento;
    private String cnae;
    private List<ItemNFCeVO> itensNFCe;
    private List<FormaPagamentoNFCeVO> formasPagamentoNFCe;
    private Double valorTotal;
    private boolean envioFinalizado = false;
    private String resultadoEnvio;
    private String status;
    private String motivo;
    private boolean marcado = false;
    private String chave;
    private String justificativaParaCancelar;
    private boolean notaCancelando = false;
    private boolean notaCancelada = false;
    private String resultado;
    private Integer qtdTentativaInutilizado;
    private boolean inutilizado = false;
    private Integer idReenvio;
    private Date dataEmissaoReenvio;


    public Integer getId_NFCe() {
        if (id_NFCe == null) {
            id_NFCe = 0;
        }
        return id_NFCe;
    }

    public void setId_NFCe(Integer id_NFCe) {
        this.id_NFCe = id_NFCe;
    }

    public String getNumeroEnvio() {
        if (numeroEnvio == null) {
            numeroEnvio = "";
        }
        return numeroEnvio;
    }

    public void setNumeroEnvio(String numeroEnvio) {
        this.numeroEnvio = numeroEnvio;
    }

    public EmpresaNFeVO getEmpresaNFe() {
        if (empresaNFe == null) {
            empresaNFe = new EmpresaNFeVO();
        }
        return empresaNFe;
    }

    public void setEmpresaNFe(EmpresaNFeVO empresaNFe) {
        this.empresaNFe = empresaNFe;
    }

    public String getDestCPFCNPJ() {
        if (destCPFCNPJ == null) {
            destCPFCNPJ = "";
        }
        return destCPFCNPJ;
    }

    public void setDestCPFCNPJ(String destCPFCNPJ) {
        this.destCPFCNPJ = destCPFCNPJ;
    }

    public Date getDataHoraEmissao() {
        return dataHoraEmissao;
    }

    public void setDataHoraEmissao(Date dataHoraEmissao) {
        this.dataHoraEmissao = dataHoraEmissao;
    }

    public String getDestNome() {
        if (destNome == null) {
            destNome = "";
        }
        return destNome;
    }

    public void setDestNome(String destNome) {
        this.destNome = destNome;
    }

    public String getDestFone() {
        if (destFone == null) {
            destFone = "";
        }
        return destFone;
    }

    public void setDestFone(String destFone) {
        this.destFone = destFone;
    }

    public String getDestCEP() {
        if (destCEP == null) {
            destCEP = "";
        }
        return destCEP;
    }

    public void setDestCEP(String destCEP) {
        this.destCEP = destCEP;
    }

    public String getDestLogradouro() {
        if (destLogradouro == null) {
            destLogradouro = "";
        }
        return destLogradouro;
    }

    public void setDestLogradouro(String destLogradouro) {
        this.destLogradouro = destLogradouro;
    }

    public String getDestNumero() {
        if (destNumero == null) {
            destNumero = "";
        }
        return destNumero;
    }

    public void setDestNumero(String destNumero) {
        this.destNumero = destNumero;
    }

    public String getDestComplemento() {
        if (destComplemento == null) {
            destComplemento = "";
        }
        return destComplemento;
    }

    public void setDestComplemento(String destComplemento) {
        this.destComplemento = destComplemento;
    }

    public String getDestBairro() {
        if (destBairro == null) {
            destBairro = "";
        }
        return destBairro;
    }

    public void setDestBairro(String destBairro) {
        this.destBairro = destBairro;
    }

    public MunicipioNFeVO getDestCidade() {
        if (destCidade == null) {
            destCidade = new MunicipioNFeVO();
        }
        return destCidade;
    }

    public void setDestCidade(MunicipioNFeVO destCidade) {
        this.destCidade = destCidade;
    }

    public String getComplemento() {
        if (complemento == null) {
            complemento = "";
        }
        return complemento;
    }

    public void setComplemento(String complemento) {
        this.complemento = complemento;
    }

    public String getCnae() {
        if (cnae == null) {
            cnae = "";
        }
        return cnae;
    }

    public void setCnae(String cnae) {
        this.cnae = cnae;
    }

    public List<ItemNFCeVO> getItensNFCe() {
        if (itensNFCe == null) {
            itensNFCe = new ArrayList<ItemNFCeVO>();
        }
        return itensNFCe;
    }

    public void setItensNFCe(List<ItemNFCeVO> itensNFCe) {
        this.itensNFCe = itensNFCe;
    }

    public List<FormaPagamentoNFCeVO> getFormasPagamentoNFCe() {
        if (formasPagamentoNFCe == null) {
            formasPagamentoNFCe = new ArrayList<FormaPagamentoNFCeVO>();
        }
        return formasPagamentoNFCe;
    }

    public void setFormasPagamentoNFCe(List<FormaPagamentoNFCeVO> formasPagamentoNFCe) {
        this.formasPagamentoNFCe = formasPagamentoNFCe;
    }

    public Double getValorTotal() {
        if (valorTotal == null) {
            valorTotal = 0.0;
        }
        return valorTotal;
    }

    public void setValorTotal(Double valorTotal) {
        this.valorTotal = valorTotal;
    }

    public boolean isEnvioFinalizado() {
        return envioFinalizado;
    }

    public void setEnvioFinalizado(boolean envioFinalizado) {
        this.envioFinalizado = envioFinalizado;
    }

    public String getResultadoEnvio() {
        if (resultadoEnvio == null) {
            resultadoEnvio = "";
        }
        return resultadoEnvio;
    }

    public void setResultadoEnvio(String resultadoEnvio) {
        this.resultadoEnvio = resultadoEnvio;
    }

    public String getStatus() {
        if (status == null) {
            status = "";
        }
        return status;
    }

    public StatusNFCeEnum getStatusEnum() {
        return StatusNFCeEnum.getEnumByDescricao(getStatus());
    }

    public String getStatusApresentar() {
        if (getStatus().equals(StatusNFCeEnum.REENVIADO.getDescricao())) {
            return getStatus() + " - NFCe " + getIdReenvio();
        }
        return getStatus();
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public String getMotivo() {
        if (motivo == null) {
            motivo = "";
        }
        return motivo;
    }

    public void setMotivo(String motivo) {
        this.motivo = motivo;
    }

    public String getClasseDoStatus() {
        if (status.equals("Cancelando")) {
            return "orange";
        } else if (status.equals("Cancelado")) {
            return "red";
        } else if (status.equals("Autorizado")) {
            return "green";
        } else if (status.equals("Enviando")) {
            return "gray";
        } else if (status.equals("Processando")) {
            return "blue";
        } else {
            return "";
        }
    }

    public boolean getNotaNaoAutorizada() {
        return status.equals("Não autorizado")
                || status.equals("Reenviado");
    }

    public boolean getNotaEstaAutorizada() {
        return status.equals("Autorizado");
    }

    public boolean getNotaEstaCancelada() {
        return status.equals(StatusNFCeEnum.CANCELADO.getDescricao());
    }

    public boolean isMarcado() {
        return marcado;
    }

    public void setMarcado(boolean marcado) {
        this.marcado = marcado;
    }

    public String getChave() {
        if (chave == null) {
            chave = "";
        }
        return chave;
    }

    public void setChave(String chave) {
        this.chave = chave;
    }

    public String getJustificativaParaCancelar() {
        if (justificativaParaCancelar == null) {
            justificativaParaCancelar = "";
        }
        return justificativaParaCancelar;
    }

    public void setJustificativaParaCancelar(String justificativaParaCancelar) {
        this.justificativaParaCancelar = justificativaParaCancelar;
    }

    public boolean isNotaCancelando() {
        return notaCancelando;
    }

    public void setNotaCancelando(boolean notaCancelando) {
        this.notaCancelando = notaCancelando;
    }

    public String getResultado() {
        if (resultado == null) {
            resultado = "";
        }
        return resultado;
    }

    public void setResultado(String resultado) {
        this.resultado = resultado;
    }

    public Integer getQtdTentativaInutilizado() {
        if (qtdTentativaInutilizado == null) {
            qtdTentativaInutilizado = 0;
        }
        return qtdTentativaInutilizado;
    }

    public void setQtdTentativaInutilizado(Integer qtdTentativaInutilizado) {
        this.qtdTentativaInutilizado = qtdTentativaInutilizado;
    }

    public boolean isInutilizado() {
        return inutilizado;
    }

    public void setInutilizado(boolean inutilizado) {
        this.inutilizado = inutilizado;
    }

    public boolean getPodeCancelarNota() throws Exception {
        try {
            Date dataLimite = Uteis.somarCampoData(getDataHoraEmissao(), Calendar.HOUR, 24);
            return Calendario.maiorComHora(dataLimite, Calendario.hoje());
        } catch (Exception e) {
            return false;
        }
    }
    public Date getDataEmissaoReenvio() {
        if (dataEmissaoReenvio == null) {
            dataEmissaoReenvio = Calendario.hoje();
        }
        return dataEmissaoReenvio;
    }

    public void setDataEmissaoReenvio(Date dataEmissaoReenvio) {
        this.dataEmissaoReenvio = dataEmissaoReenvio;
    }

    public Integer getIdReenvio() {
        if (idReenvio == null) {
            idReenvio = 0;
        }
        return idReenvio;
    }

    public void setIdReenvio(Integer idReenvio) {
        this.idReenvio = idReenvio;
    }

    public NotasFiscaisTO toTO() {
        return new NotasFiscaisTO(this);
    }

    public boolean isNotaCancelada() {
        return notaCancelada;
    }

    public void setNotaCancelada(boolean notaCancelada) {
        this.notaCancelada = notaCancelada;
    }

    public String getLinkDownloadPDF(){
        try {
            return PropsService.getPropertyValue(PropsService.urlModuloNFSe) + "/nfce?nfce=" + getId_NFCe();
        } catch (Exception ex) {
            return "";
        }
    }
}

package negocio.comuns.nfe;

import negocio.comuns.notaFiscal.StatusEnotasEnum;
import org.json.JSONException;
import org.json.JSONObject;
import negocio.comuns.arquitetura.SuperTO;
import negocio.comuns.nfe.enumerador.ModeloDocumentoFiscal;
import negocio.comuns.nfe.enumerador.StatusNFCeEnum;
import negocio.comuns.utilitarias.Calendario;
import negocio.comuns.utilitarias.ProcessadorTemplateThymeleafSingletonImpl;
import negocio.comuns.utilitarias.Uteis;
import org.apache.commons.lang.StringUtils;

import java.util.Date;
import java.util.HashMap;
import java.util.Iterator;
import java.util.Map;

import static negocio.comuns.nfe.enumerador.ModeloDocumentoFiscal.NFCe;
import static negocio.comuns.nfe.enumerador.ModeloDocumentoFiscal.NFSe;
import static negocio.comuns.nfe.enumerador.StatusNFCeEnum.CANCELADO;
import static negocio.comuns.nfe.enumerador.StatusNFCeEnum.NAO_AUTORIZADO;
import static negocio.comuns.utilitarias.Uteis.getDataAplicandoFormatacao;

/**
 * POJO responsável por representar um simples relatório a respeito de um documento fiscal.
 *
 * <AUTHOR> Cattany
 * @since 06/12/2018
 */
public class DocumentoFiscalRelatorioPeriodoTO extends SuperTO {

    private static final String DATE_FORMAT_FROM_JSON = "yyyy-mm-dd hh:mm:ss";
    private static final String DATE_FORMAT_APRESENTAR_RELATORIO = "dd/MM/yyyy HH:mm:ss";

    /**
     * Limite inferior para uso da geração do relatório.
     */
    private Date periodoLimiteInferior;
    /**
     * Limite superior para uso da geração do relatório.
     */
    private Date periodoLimiteSuperior;
    /**
     * Map para uso de alguma contagem, mapeado por status do documento.
     */
    private Map<String, Integer> mapQuantidadePorStatus = new HashMap<String, Integer>();

    public DocumentoFiscalRelatorioPeriodoTO() {
    }

    public DocumentoFiscalRelatorioPeriodoTO(Date periodoLimiteInferior, Date periodoLimiteSuperior, Map<String, Integer> mapQuantidadePorStatus) {
        this.periodoLimiteInferior = periodoLimiteInferior;
        this.periodoLimiteSuperior = periodoLimiteSuperior;
        this.mapQuantidadePorStatus = mapQuantidadePorStatus;
    }

    public Date getPeriodoLimiteInferior() {
        return periodoLimiteInferior;
    }

    public void setPeriodoLimiteInferior(Date periodoLimiteInferior) {
        this.periodoLimiteInferior = periodoLimiteInferior;
    }

    public Date getPeriodoLimiteSuperior() {
        return periodoLimiteSuperior;
    }

    public void setPeriodoLimiteSuperior(Date periodoLimiteSuperior) {
        this.periodoLimiteSuperior = periodoLimiteSuperior;
    }

    public Map<String, Integer> getMapQuantidadePorStatus() {
        return mapQuantidadePorStatus;
    }

    public void setMapQuantidadePorStatus(Map<String, Integer> mapQuantidadePorStatus) {
        this.mapQuantidadePorStatus = mapQuantidadePorStatus;
    }

    public static DocumentoFiscalRelatorioPeriodoTO getFromJSON(String json) throws Exception {
        if (StringUtils.isNotBlank(json)) {
            DocumentoFiscalRelatorioPeriodoTO obj = new DocumentoFiscalRelatorioPeriodoTO();

            JSONObject jsonObject = new JSONObject(json);
            String periodoLimiteInferiorValue = null;
            try {
                periodoLimiteInferiorValue = jsonObject.getString("periodoLimiteInferior");
            } catch (JSONException e) {
                e.printStackTrace();
            }
            String periodoLimiteSuperiorValue = null;
            try {
                periodoLimiteSuperiorValue = jsonObject.getString("periodoLimiteSuperior");
            } catch (JSONException e) {
                e.printStackTrace();
            }
            String map = null;
            try {
                map = jsonObject.getString("mapQuantidadePorStatus");
            } catch (JSONException e) {
                e.printStackTrace();
            }
            Map<String, Integer> mapQuantidadePorStatus = new HashMap<String, Integer>();
            JSONObject objMap = new JSONObject(map);
            for (Iterator it = objMap.keys(); it.hasNext(); ) {
                Object key = it.next();
                String keyString = key.toString();
                StatusNFCeEnum statusEncontrado = StatusNFCeEnum.valueOf(keyString);
                Integer qtd = mapQuantidadePorStatus.get(statusEncontrado);
                if (qtd == null) {
                    qtd = 0;
                    mapQuantidadePorStatus.put(statusEncontrado.getDescricao(), qtd);
                }
                qtd += objMap.getInt(keyString);
                mapQuantidadePorStatus.put(statusEncontrado.getDescricao(), qtd);
            }

            obj.setPeriodoLimiteInferior(Calendario.getDate(DATE_FORMAT_FROM_JSON, periodoLimiteInferiorValue));
            obj.setPeriodoLimiteSuperior(Calendario.getDate(DATE_FORMAT_FROM_JSON, periodoLimiteSuperiorValue));
            obj.setMapQuantidadePorStatus(mapQuantidadePorStatus);

            return obj;
        }

        return null;
    }

    public static Map<ModeloDocumentoFiscal, DocumentoFiscalRelatorioPeriodoTO> getMapByModeloFromJSON(JSONObject jsonObject) throws Exception {
        Map<ModeloDocumentoFiscal, DocumentoFiscalRelatorioPeriodoTO> mapRelatorioPorModelo = new HashMap<ModeloDocumentoFiscal, DocumentoFiscalRelatorioPeriodoTO>();

        for (ModeloDocumentoFiscal modeloDocumentoFiscal : ModeloDocumentoFiscal.values()) {
            String relatorioJson = null;
            try {
                relatorioJson = jsonObject.getString(modeloDocumentoFiscal.name());
            } catch (JSONException e) {
                e.printStackTrace();
            }

            DocumentoFiscalRelatorioPeriodoTO relatorioTO = getFromJSON(relatorioJson);

            if (relatorioTO != null) {
                mapRelatorioPorModelo.put(modeloDocumentoFiscal, relatorioTO);
            }
        }

        return mapRelatorioPorModelo;
    }

    public static String mapByModeloDocumentoFiscalToEmailHTML(Map<ModeloDocumentoFiscal, DocumentoFiscalRelatorioPeriodoTO> mapRelatorioPorModelo,
                                                               String chaveModuloNFe,
                                                               String nomeFantasiaEmpresa,
                                                               boolean eNotas) {
        return mapByModeloDocumentoFiscalHTML(mapRelatorioPorModelo, chaveModuloNFe, nomeFantasiaEmpresa, "template_nao_autorizadas_ou_canceladas", eNotas);
    }

    public static String mapByModeloDocumentoFiscalToSocialMailingHTML(Map<ModeloDocumentoFiscal, DocumentoFiscalRelatorioPeriodoTO> mapRelatorioPorModelo,
                                                                       String chaveModuloNFe,
                                                                       String nomeFantasiaEmpresa, boolean eNotas) {
        return mapByModeloDocumentoFiscalHTML(mapRelatorioPorModelo, chaveModuloNFe, nomeFantasiaEmpresa, "template_nao_autorizadas_ou_canceladas_socialmailing", eNotas);
    }

    private static String mapByModeloDocumentoFiscalHTML(Map<ModeloDocumentoFiscal, DocumentoFiscalRelatorioPeriodoTO> mapRelatorioPorModelo,
                                                         String chaveModuloNFe,
                                                         String nomeFantasiaEmpresa,
                                                         String fileName, boolean eNotas) {
        Map<String, Object> mapContextVariables = new HashMap<String, Object>();
        mapContextVariables.put("chaveModuloNFe", chaveModuloNFe);
        mapContextVariables.put("nomeFantasiaEmpresa", nomeFantasiaEmpresa);
        mapContextVariables.put("eNotas", eNotas);

        for (Map.Entry<ModeloDocumentoFiscal, DocumentoFiscalRelatorioPeriodoTO> relatorioPorModeloEntryKey : mapRelatorioPorModelo.entrySet()) {
            String periodoLimiteInferiorString = getDataAplicandoFormatacao(relatorioPorModeloEntryKey.getValue().getPeriodoLimiteInferior(), DATE_FORMAT_APRESENTAR_RELATORIO);
            String periodoLimiteSuperiorString = getDataAplicandoFormatacao(relatorioPorModeloEntryKey.getValue().getPeriodoLimiteSuperior(), DATE_FORMAT_APRESENTAR_RELATORIO);

            mapContextVariables.put("periodoLimiteInferiorString", periodoLimiteInferiorString);
            mapContextVariables.put("periodoLimiteSuperiorString", periodoLimiteSuperiorString);

            Uteis.logar(String.format(
                    "Mapa do relatório de %s (\"relatorioNotasNaoAutorizadasOuCanceladasDesdeOntem\" - %s) -> %s",
                    relatorioPorModeloEntryKey.getKey(),
                    Uteis.getDataComHora(Calendario.hoje()),
                    relatorioPorModeloEntryKey.getValue().getMapQuantidadePorStatus().toString())
            );

            if (eNotas) {
                preencheQuantidadesRelatorioPorModeloENotas(mapContextVariables, relatorioPorModeloEntryKey);
            } else {
                preencheQuantidadesRelatorioPorModelo(mapContextVariables, relatorioPorModeloEntryKey);
            }
        }

        mapContextVariables.put("dataHoraRelatorio", getDataAplicandoFormatacao(Calendario.hoje(), DATE_FORMAT_APRESENTAR_RELATORIO));

        Uteis.logar(String.format(
                "Contexto com as variáveis do relatório (\"relatorioNotasNaoAutorizadasOuCanceladasDesdeOntem\" - %s) -> %s",
                Uteis.getDataComHora(Calendario.hoje()),
                mapContextVariables.toString())
        );

        return ProcessadorTemplateThymeleafSingletonImpl.getInstance().processarTemplateHTML(
                "relatorios/notas/" + fileName,
                ProcessadorTemplateThymeleafSingletonImpl.getInstance().returnContextWithVariables(mapContextVariables)
        );
    }

    private static void preencheQuantidadesRelatorioPorModelo(Map<String, Object> mapContextVariables,
                                                              Map.Entry<ModeloDocumentoFiscal, DocumentoFiscalRelatorioPeriodoTO> entryKey) {
        if (NFCe.equals(entryKey.getKey())) {
            for (Map.Entry<String, Integer> innerEntryKey : entryKey.getValue().getMapQuantidadePorStatus().entrySet()) {
                if (CANCELADO.getDescricao().toUpperCase().equals(innerEntryKey.getKey().toUpperCase())) {
                    mapContextVariables.put("quantidadeNFCeCancelada", innerEntryKey.getValue());
                } else if (NAO_AUTORIZADO.getDescricao().toUpperCase().equals(innerEntryKey.getKey().toUpperCase())) {
                    mapContextVariables.put("quantidadeNFCeNaoAutorizada", innerEntryKey.getValue());
                }
            }
        } else if (NFSe.equals(entryKey.getKey())) {
            Map<String, Integer> mapQuantidadePorStatus = entryKey.getValue().getMapQuantidadePorStatus();
            for (String status : mapQuantidadePorStatus.keySet()) {
                if (CANCELADO.getDescricao().toUpperCase().equals(status.toUpperCase())) {
                    mapContextVariables.put("quantidadeNFSeCancelada", entryKey.getValue().getMapQuantidadePorStatus().get(status));
                } else if (NAO_AUTORIZADO.getDescricao().toUpperCase().equals(status.toUpperCase())) {
                    mapContextVariables.put("quantidadeNFSeNaoAutorizada", entryKey.getValue().getMapQuantidadePorStatus().get(status));
                }
            }
        }
    }

    private static void preencheQuantidadesRelatorioPorModeloENotas(Map<String, Object> mapContextVariables,
                                                                    Map.Entry<ModeloDocumentoFiscal, DocumentoFiscalRelatorioPeriodoTO> entryKey) {
        if (NFCe.equals(entryKey.getKey())) {
            for (Map.Entry<String, Integer> innerEntryKey : entryKey.getValue().getMapQuantidadePorStatus().entrySet()) {
                if (StatusEnotasEnum.CANCELADA.getDescricaoEnotas().toUpperCase().equals(innerEntryKey.getKey().toUpperCase())) {
                    mapContextVariables.put("quantidadeNFCeCancelada", innerEntryKey.getValue());
                } else if (StatusEnotasEnum.NEGADA.getDescricaoEnotas().toUpperCase().equals(innerEntryKey.getKey().toUpperCase())) {
                    mapContextVariables.put("quantidadeNFCeNaoAutorizada", innerEntryKey.getValue());
                }
            }
        } else if (NFSe.equals(entryKey.getKey())) {
            Map<String, Integer> mapQuantidadePorStatus = entryKey.getValue().getMapQuantidadePorStatus();
            for (String status : mapQuantidadePorStatus.keySet()) {
                if (StatusEnotasEnum.CANCELADA.getDescricaoEnotas().toUpperCase().equals(status.toUpperCase())) {
                    mapContextVariables.put("quantidadeNFSeCancelada", entryKey.getValue().getMapQuantidadePorStatus().get(status));
                } else if (StatusEnotasEnum.NEGADA.getDescricaoEnotas().toUpperCase().equals(status.toUpperCase())) {
                    mapContextVariables.put("quantidadeNFSeNaoAutorizada", entryKey.getValue().getMapQuantidadePorStatus().get(status));
                }
            }
        }
    }


    /**
     * <h2>MÉTODO UTILITÁRIO - NÃO FAÇA MERGE DESTE MÉTODO - Usado para testes, para não depender do acesso ao módulo de Notas</h2>
     *
     * @return o html do relatório na estrutura para ser processado pelo Thymeleaf.
     * @deprecated
     */
    public static Map<ModeloDocumentoFiscal, DocumentoFiscalRelatorioPeriodoTO> _getMapRelatorioPorModeloFakeForTests(Integer quantidadeGerarNFCeCancelada,
                                                                                                                      Integer quantidadeGerarNFCeNaoAutorizada,
                                                                                                                      Integer quantidadeGerarNFSeCancelada,
                                                                                                                      Integer quantidadeGerarNFSeNaoAutorizada) {
        Map<ModeloDocumentoFiscal, DocumentoFiscalRelatorioPeriodoTO> mapParent = new HashMap<ModeloDocumentoFiscal, DocumentoFiscalRelatorioPeriodoTO>();

        Map<String, Integer> mapNFCe = new HashMap<>();
        mapNFCe.put(StatusNFCeEnum.CANCELADO.getDescricao(), quantidadeGerarNFCeCancelada);
        mapNFCe.put(StatusNFCeEnum.NAO_AUTORIZADO.getDescricao(), quantidadeGerarNFCeNaoAutorizada);
        DocumentoFiscalRelatorioPeriodoTO relatorioNFCe = new DocumentoFiscalRelatorioPeriodoTO(Uteis.obterDataAnterior(new Date(), 1), Calendario.getDataComHoraZerada(new Date()), mapNFCe);
        mapParent.put(ModeloDocumentoFiscal.NFCe, relatorioNFCe);

        Map<String, Integer> mapNFSe = new HashMap<>();
        mapNFSe.put(StatusNFCeEnum.CANCELADO.getDescricao(), quantidadeGerarNFSeCancelada);
        mapNFSe.put(StatusNFCeEnum.NAO_AUTORIZADO.getDescricao(), quantidadeGerarNFSeNaoAutorizada);
        DocumentoFiscalRelatorioPeriodoTO relatorioNFSe = new DocumentoFiscalRelatorioPeriodoTO(Uteis.obterDataAnterior(new Date(), 1), Calendario.getDataComHoraZerada(new Date()), mapNFSe);
        mapParent.put(ModeloDocumentoFiscal.NFSe, relatorioNFSe);

        return mapParent;
    }

}

package negocio.comuns.nfe;

import br.com.pactosolucoes.comuns.util.Formatador;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import negocio.comuns.arquitetura.SuperTO;
import negocio.comuns.utilitarias.Uteis;

import java.io.InputStream;

@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
public class NFSeImprimirTO extends SuperTO {

    //INFORMACOES PRESTADOR
    private Integer idEmpresaNFSe;
    private String razaoSocialPrestador;
    private String cnpjPrestador;
    private String inscricaoMunicipalPrestador;
    private String inscricaoEstadualPrestador;
    private String enderecoPrestador;
    private String complementoPrestador;
    private String municipioPrestador;
    private String ufPrestador;
    private String telefonePrestador;
    private String emailPrestador;
    private InputStream logomarcaPrestador;

    //INFORMACOES TOMADOR
    private String razaoSocialTomador;
    private String cnpjTomador;
    private String inscricaoMunicipalTomador;
    private String inscricaoEstadualTomador;
    private String enderecoTomador;
    private String complementoTomador;
    private String municipioTomador;
    private String ufTomador;
    private String telefoneTomador;
    private String emailTomador;

    //INFORMACOES DA NFSE
    private InputStream logomarcaPrefeitura;
    private String cidadePrestacao;
    private String numeroNota;
    private String dataServico;
    private String dataHoraEmissao;
    private String codigoAutorizacao;
    private String dataCompetencia;
    private String numeroRPS;
    private String municipioPrestacao;
    private String servicosDescricao;
    private String codigoServico;
    private String valorPIS;
    private String valorCOFINS;
    private String valorIR;
    private String valorINSS;
    private String valorCSLL;
    private String valorServicos;
    private String descontoIncondicionado;
    private String descontoCondicionado;
    private String retencoesFederais;
    private String outrasRetencoes;
    private String issRetidoValor;
    private String valorLiquido;
    private String naturezaOperacao;
    private String regimeEspecial;
    private String simplesNacional;
    private String incentivadorCultural;
    private String deducoesPermitidas;
    private String descontoIncondicionadoMunicipio;
    private String baseCalculo;
    private String aliquota;
    private String reterISS;
    private String valorISS;
    private String valorTotalNota;
    private String outrasInformacoes;
    private String id_RPS;

    private byte logomarcaPrestadorByte[];


    public String getRazaoSocialPrestador() {
        if (razaoSocialPrestador == null) {
            razaoSocialPrestador = "";
        }
        return razaoSocialPrestador;
    }

    public void setRazaoSocialPrestador(String razaoSocialPrestador) {
        this.razaoSocialPrestador = razaoSocialPrestador;
    }

    public String getCnpjPrestador() {
        if (cnpjPrestador == null) {
            cnpjPrestador = "";
        }
        return cnpjPrestador;
    }

    public void setCnpjPrestador(String cnpjPrestador) {
        this.cnpjPrestador = cnpjPrestador;
    }

    public String getInscricaoMunicipalPrestador() {
        if (inscricaoMunicipalPrestador == null) {
            inscricaoMunicipalPrestador = "";
        }
        return inscricaoMunicipalPrestador;
    }

    public void setInscricaoMunicipalPrestador(String inscricaoMunicipalPrestador) {
        this.inscricaoMunicipalPrestador = inscricaoMunicipalPrestador;
    }

    public String getInscricaoEstadualPrestador() {
        if (inscricaoEstadualPrestador == null) {
            inscricaoEstadualPrestador = "";
        }
        return inscricaoEstadualPrestador;
    }

    public void setInscricaoEstadualPrestador(String inscricaoEstadualPrestador) {
        this.inscricaoEstadualPrestador = inscricaoEstadualPrestador;
    }

    public String getEnderecoPrestador() {
        if (enderecoPrestador == null) {
            enderecoPrestador = "";
        }
        return enderecoPrestador;
    }

    public void setEnderecoPrestador(String enderecoPrestador) {
        this.enderecoPrestador = enderecoPrestador;
    }

    public String getComplementoPrestador() {
        if (complementoPrestador == null) {
            complementoPrestador = "";
        }
        return complementoPrestador;
    }

    public void setComplementoPrestador(String complementoPrestador) {
        this.complementoPrestador = complementoPrestador;
    }

    public String getMunicipioPrestador() {
        if (municipioPrestador == null) {
            municipioPrestador = "";
        }
        return municipioPrestador;
    }

    public void setMunicipioPrestador(String municipioPrestador) {
        this.municipioPrestador = municipioPrestador;
    }

    public String getUfPrestador() {
        if (ufPrestador == null) {
            ufPrestador = "";
        }
        return ufPrestador;
    }

    public void setUfPrestador(String ufPrestador) {
        this.ufPrestador = ufPrestador;
    }

    public String getTelefonePrestador() {
        if (telefonePrestador == null) {
            telefonePrestador = "";
        }
        return telefonePrestador;
    }

    public void setTelefonePrestador(String telefonePrestador) {
        this.telefonePrestador = telefonePrestador;
    }

    public String getEmailPrestador() {
        if (emailPrestador == null) {
            emailPrestador = "";
        }
        return emailPrestador;
    }

    public void setEmailPrestador(String emailPrestador) {
        this.emailPrestador = emailPrestador;
    }

    public String getRazaoSocialTomador() {
        if (razaoSocialTomador == null) {
            razaoSocialTomador = "";
        }
        return razaoSocialTomador;
    }

    public void setRazaoSocialTomador(String razaoSocialTomador) {
        this.razaoSocialTomador = razaoSocialTomador;
    }

    public String getCnpjTomador() {
        if (cnpjTomador == null) {
            cnpjTomador = "";
        }
        return cnpjTomador;
    }

    public void setCnpjTomador(String cnpjTomador) {
        this.cnpjTomador = cnpjTomador;
    }

    public String getInscricaoMunicipalTomador() {
        if (inscricaoMunicipalTomador == null) {
            inscricaoMunicipalTomador = "";
        }
        return inscricaoMunicipalTomador;
    }

    public void setInscricaoMunicipalTomador(String inscricaoMunicipalTomador) {
        this.inscricaoMunicipalTomador = inscricaoMunicipalTomador;
    }

    public String getInscricaoEstadualTomador() {
        if (inscricaoEstadualTomador == null) {
            inscricaoEstadualTomador = "";
        }
        return inscricaoEstadualTomador;
    }

    public void setInscricaoEstadualTomador(String inscricaoEstadualTomador) {
        this.inscricaoEstadualTomador = inscricaoEstadualTomador;
    }

    public String getEnderecoTomador() {
        if (enderecoTomador == null) {
            enderecoTomador = "";
        }
        return enderecoTomador;
    }

    public void setEnderecoTomador(String enderecoTomador) {
        this.enderecoTomador = enderecoTomador;
    }

    public String getComplementoTomador() {
        if (complementoTomador == null) {
            complementoTomador = "";
        }
        return complementoTomador;
    }

    public void setComplementoTomador(String complementoTomador) {
        this.complementoTomador = complementoTomador;
    }

    public String getMunicipioTomador() {
        if (municipioTomador == null) {
            municipioTomador = "";
        }
        return municipioTomador;
    }

    public void setMunicipioTomador(String municipioTomador) {
        this.municipioTomador = municipioTomador;
    }

    public String getUfTomador() {
        if (ufTomador == null) {
            ufTomador = "";
        }
        return ufTomador;
    }

    public void setUfTomador(String ufTomador) {
        this.ufTomador = ufTomador;
    }

    public String getTelefoneTomador() {
        if (telefoneTomador == null) {
            telefoneTomador = "";
        }
        return telefoneTomador;
    }

    public void setTelefoneTomador(String telefoneTomador) {
        this.telefoneTomador = telefoneTomador;
    }

    public String getEmailTomador() {
        if (emailTomador == null) {
            emailTomador = "";
        }
        return emailTomador;
    }

    public void setEmailTomador(String emailTomador) {
        this.emailTomador = emailTomador;
    }

    public String getCidadePrestacao() {
        if (cidadePrestacao == null) {
            cidadePrestacao = "";
        }
        return Uteis.obterNomeComApenasPrimeiraLetraMaiuscula(cidadePrestacao);
    }

    public void setCidadePrestacao(String cidadePrestacao) {
        this.cidadePrestacao = cidadePrestacao;
    }

    public String getNumeroNota() {
        if (numeroNota == null) {
            numeroNota = "";
        }
        return numeroNota;
    }

    public void setNumeroNota(String numeroNota) {
        this.numeroNota = numeroNota;
    }

    public String getDataServico() {
        if (dataServico == null) {
            dataServico = "";
        }
        return dataServico;
    }

    public void setDataServico(String dataServico) {
        this.dataServico = dataServico;
    }

    public String getDataHoraEmissao() {
        if (dataHoraEmissao == null) {
            dataHoraEmissao = "";
        }
        return dataHoraEmissao;
    }

    public void setDataHoraEmissao(String dataHoraEmissao) {
        this.dataHoraEmissao = dataHoraEmissao;
    }

    public String getCodigoAutorizacao() {
        if (codigoAutorizacao == null) {
            codigoAutorizacao = "";
        }
        return codigoAutorizacao;
    }

    public void setCodigoAutorizacao(String codigoAutorizacao) {
        this.codigoAutorizacao = codigoAutorizacao;
    }

    public String getDataCompetencia() {
        if (dataCompetencia == null) {
            dataCompetencia = "";
        }
        return dataCompetencia;
    }

    public void setDataCompetencia(String dataCompetencia) {
        this.dataCompetencia = dataCompetencia;
    }

    public String getNumeroRPS() {
        if (numeroRPS == null) {
            numeroRPS = "";
        }
        return numeroRPS;
    }

    public void setNumeroRPS(String numeroRPS) {
        this.numeroRPS = numeroRPS;
    }

    public String getMunicipioPrestacao() {
        if (municipioPrestacao == null) {
            municipioPrestacao = "";
        }
        return municipioPrestacao;
    }

    public void setMunicipioPrestacao(String municipioPrestacao) {
        this.municipioPrestacao = municipioPrestacao;
    }

    public String getServicosDescricao() {
        if (servicosDescricao == null) {
            servicosDescricao = "";
        }
        return servicosDescricao;
    }

    public void setServicosDescricao(String servicosDescricao) {
        this.servicosDescricao = servicosDescricao;
    }

    public String getCodigoServico() {
        if (codigoServico == null) {
            codigoServico = "";
        }
        return codigoServico;
    }

    public void setCodigoServico(String codigoServico) {
        this.codigoServico = codigoServico;
    }

    public String getValorPIS() {
        if (valorPIS == null) {
            valorPIS = "";
        }
        return valorPIS;
    }

    public void setValorPIS(String valorPIS) {
        this.valorPIS = valorPIS;
    }

    public String getValorCOFINS() {
        if (valorCOFINS == null) {
            valorCOFINS = "";
        }
        return valorCOFINS;
    }

    public void setValorCOFINS(String valorCOFINS) {
        this.valorCOFINS = valorCOFINS;
    }

    public String getValorIR() {
        if (valorIR == null) {
            valorIR = "";
        }
        return valorIR;
    }

    public void setValorIR(String valorIR) {
        this.valorIR = valorIR;
    }

    public String getValorINSS() {
        if (valorINSS == null) {
            valorINSS = "";
        }
        return valorINSS;
    }

    public void setValorINSS(String valorINSS) {
        this.valorINSS = valorINSS;
    }

    public String getValorCSLL() {
        if (valorCSLL == null) {
            valorCSLL = "";
        }
        return valorCSLL;
    }

    public void setValorCSLL(String valorCSLL) {
        this.valorCSLL = valorCSLL;
    }

    public String getValorServicos() {
        if (valorServicos == null) {
            valorServicos = "";
        }
        return valorServicos;
    }

    public void setValorServicos(String valorServicos) {
        this.valorServicos = valorServicos;
    }

    public String getDescontoIncondicionado() {
        if (descontoIncondicionado == null) {
            descontoIncondicionado = "";
        }
        return descontoIncondicionado;
    }

    public void setDescontoIncondicionado(String descontoIncondicionado) {
        this.descontoIncondicionado = descontoIncondicionado;
    }

    public String getDescontoCondicionado() {
        if (descontoCondicionado == null) {
            descontoCondicionado = "";
        }
        return descontoCondicionado;
    }

    public void setDescontoCondicionado(String descontoCondicionado) {
        this.descontoCondicionado = descontoCondicionado;
    }

    public String getRetencoesFederais() {
        if (retencoesFederais == null) {
            retencoesFederais = Formatador.formatarValorMonetario(0.0);
        }
        return retencoesFederais;
    }

    public void setRetencoesFederais(String retencoesFederais) {
        this.retencoesFederais = retencoesFederais;
    }

    public String getOutrasRetencoes() {
        if (outrasRetencoes == null) {
            outrasRetencoes = "";
        }
        return outrasRetencoes;
    }

    public void setOutrasRetencoes(String outrasRetencoes) {
        this.outrasRetencoes = outrasRetencoes;
    }

    public String getIssRetidoValor() {
        if (issRetidoValor == null) {
            issRetidoValor = "";
        }
        return issRetidoValor;
    }

    public void setIssRetidoValor(String issRetidoValor) {
        this.issRetidoValor = issRetidoValor;
    }

    public String getValorLiquido() {
        if (valorLiquido == null) {
            valorLiquido = "";
        }
        return valorLiquido;
    }

    public void setValorLiquido(String valorLiquido) {
        this.valorLiquido = valorLiquido;
    }

    public String getNaturezaOperacao() {
        if (naturezaOperacao == null) {
            naturezaOperacao = "";
        }
        return naturezaOperacao;
    }

    public void setNaturezaOperacao(String naturezaOperacao) {
        this.naturezaOperacao = naturezaOperacao;
    }

    public String getRegimeEspecial() {
        if (regimeEspecial == null) {
            regimeEspecial = "";
        }
        return regimeEspecial;
    }

    public void setRegimeEspecial(String regimeEspecial) {
        this.regimeEspecial = regimeEspecial;
    }

    public String getSimplesNacional() {
        if (simplesNacional == null) {
            simplesNacional = "";
        }
        return simplesNacional;
    }

    public void setSimplesNacional(String simplesNacional) {
        this.simplesNacional = simplesNacional;
    }

    public String getIncentivadorCultural() {
        if (incentivadorCultural == null) {
            incentivadorCultural = "";
        }
        return incentivadorCultural;
    }

    public void setIncentivadorCultural(String incentivadorCultural) {
        this.incentivadorCultural = incentivadorCultural;
    }

    public String getDeducoesPermitidas() {
        if (deducoesPermitidas == null) {
            deducoesPermitidas = Formatador.formatarValorMonetario(0.0);
            ;
        }
        return deducoesPermitidas;
    }

    public void setDeducoesPermitidas(String deducoesPermitidas) {
        this.deducoesPermitidas = deducoesPermitidas;
    }

    public String getDescontoIncondicionadoMunicipio() {
        if (descontoIncondicionadoMunicipio == null) {
            descontoIncondicionadoMunicipio = Formatador.formatarValorMonetario(0.0);
            ;
        }
        return descontoIncondicionadoMunicipio;
    }

    public void setDescontoIncondicionadoMunicipio(String descontoIncondicionadoMunicipio) {
        this.descontoIncondicionadoMunicipio = descontoIncondicionadoMunicipio;
    }

    public String getBaseCalculo() {
        if (baseCalculo == null) {
            baseCalculo = Formatador.formatarValorMonetario(0.0);
            ;
        }
        return baseCalculo;
    }

    public void setBaseCalculo(String baseCalculo) {
        this.baseCalculo = baseCalculo;
    }

    public String getAliquota() {
        if (aliquota == null) {
            aliquota = "";
        }
        return aliquota;
    }

    public void setAliquota(String aliquota) {
        this.aliquota = aliquota;
    }

    public String getReterISS() {
        if (reterISS == null) {
            reterISS = "";
        }
        return reterISS;
    }

    public void setReterISS(String reterISS) {
        this.reterISS = reterISS;
    }

    public String getValorISS() {
        if (valorISS == null) {
            valorISS = "";
        }
        return valorISS;
    }

    public void setValorISS(String valorISS) {
        this.valorISS = valorISS;
    }

    public String getValorTotalNota() {
        if (valorTotalNota == null) {
            valorTotalNota = "";
        }
        return valorTotalNota;
    }

    public void setValorTotalNota(String valorTotalNota) {
        this.valorTotalNota = valorTotalNota;
    }

    public String getOutrasInformacoes() {
        if (outrasInformacoes == null) {
            outrasInformacoes = "";
        }
        return outrasInformacoes;
    }

    public void setOutrasInformacoes(String outrasInformacoes) {
        this.outrasInformacoes = outrasInformacoes;
    }

    public InputStream getLogomarcaPrefeitura() {
        return logomarcaPrefeitura;
    }

    public void setLogomarcaPrefeitura(InputStream logomarcaPrefeitura) {
        this.logomarcaPrefeitura = logomarcaPrefeitura;
    }

    public String getId_RPS() {
        if (id_RPS == null) {
            id_RPS = "";
        }
        return id_RPS;
    }

    public void setId_RPS(String id_RPS) {
        this.id_RPS = id_RPS;
    }

    public InputStream getLogomarcaPrestador() {
        return logomarcaPrestador;
    }

    public void setLogomarcaPrestador(InputStream logomarcaPrestador) {
        this.logomarcaPrestador = logomarcaPrestador;
    }

    public byte[] getLogomarcaPrestadorByte() {
        if (logomarcaPrestadorByte == null) {
            logomarcaPrestadorByte = new byte[0];
        }
        return logomarcaPrestadorByte;
    }

    public void setLogomarcaPrestadorByte(byte[] logomarcaPrestadorByte) {
        this.logomarcaPrestadorByte = logomarcaPrestadorByte;
    }

    public Integer getIdEmpresaNFSe() {
        if (idEmpresaNFSe == null) {
            idEmpresaNFSe = 0;
        }
        return idEmpresaNFSe;
    }

    public void setIdEmpresaNFSe(Integer idEmpresaNFSe) {
        this.idEmpresaNFSe = idEmpresaNFSe;
    }
}

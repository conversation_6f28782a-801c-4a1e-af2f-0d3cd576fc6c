package negocio.comuns.nfe;

import negocio.comuns.arquitetura.SuperVO;
import negocio.comuns.utilitarias.ConsistirException;

public class ItemRPSNFeVO extends SuperVO {

    private Integer id_itemRPS;
    private Integer ordem;
    private Double valorUnitario;
    private Integer quantidade;
    private String descricao;
    private String tributavel;

    public ItemRPSNFeVO() {
        super();
    }

    public static void validarDados(ItemRPSNFeVO obj) throws ConsistirException {
        if (!obj.getValidarDados()) {
            return;
        }
        if (obj.getOrdem() == null) {
            throw new ConsistirException("O campo Ordem (Item do RPS) deve ser informado.");
        }
    }


    public Integer getId_itemRPS() {
        return id_itemRPS;
    }

    public void setId_itemRPS(Integer id_itemRPS) {
        this.id_itemRPS = id_itemRPS;
    }

    public Integer getOrdem() {
        return ordem;
    }

    public void setOrdem(Integer ordem) {
        this.ordem = ordem;
    }

    public Double getValorUnitario() {
        return valorUnitario;
    }

    public void setValorUnitario(Double valorUnitario) {
        this.valorUnitario = valorUnitario;
    }

    public Integer getQuantidade() {
        return quantidade;
    }

    public void setQuantidade(Integer quantidade) {
        this.quantidade = quantidade;
    }

    public String getDescricao() {
        return descricao;
    }

    public void setDescricao(String descricao) {
        this.descricao = descricao;
    }

    public String getTributavel() {
        return tributavel;
    }

    public void setTributavel(String tributavel) {
        this.tributavel = tributavel;
    }
}

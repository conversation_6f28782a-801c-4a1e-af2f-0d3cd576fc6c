package negocio.comuns.nfe;

import annotations.arquitetura.ChaveEstrangeira;
import annotations.arquitetura.ChavePrimaria;
import negocio.comuns.basico.EmpresaVO;
import negocio.comuns.utilitarias.ConsistirException;
import negocio.comuns.utilitarias.Uteis;
import org.apache.commons.lang.StringUtils;

import java.io.File;
import java.util.Date;

public class EmpresaNFeVO extends EmpresaVO {

    @ChavePrimaria
    private Integer id_Empresa = 0;
    @ChaveEstrangeira
    private MunicipioNFeVO municipio = new MunicipioNFeVO();
    private String cpf_cnpj = "";
    private String inscricaoMunicipal = "";
    private String razaoSocial = "";
    private String nomeFantasia = "";
    private boolean ativa = true;
    private Integer ddd_telefone = null;
    private String telefone = "";
    private String senhaCertificado = "";
    private String senhaCertificadoDescripto = "";
    private Integer CRT = 1;
    private boolean optateSimplesNacional = false;
    private boolean incentivadorCultural = false;
    private boolean imprimirImposto = false;
    private boolean enviarEmail = false;
    private String chave = "";
    private File certificado;
    private String ccm = "";
    private String usuarioInscricaoMunicipal = "";
    private String senhaInscricaoMunicipal = "";
    private String senhaInscricaoMunicipalDescripto = "";
    private String inscricaoEstadual = "";
    private String logradouro = "";
    private String bairro = "";
    private String CEP = "";
    private Integer numEndereco = 0;
    private String CFOP = "";
    private String complemento = "";
    private String email = "";
    private Double aliquotaSimples = 0.0;
    private Date dataVencimentoCertificado;
    private String idCsc;
    private String csc;
    private String informacaoFisco;
    private String CSTPis;
    private String CSTCofins;
    private boolean usarXMLPrefeitura = false;
    private boolean usarXMLManipulado = false;
    private boolean usarXMLManipuladoNFCe = false;
    private String serieNFCe;
    private String chaveZW;
    private Integer empresaZW;
    private boolean usaModuloNFCe = false;
    private boolean usaModuloNFSe = false;
    private String params = "";
    private String cnpjMatriz = "";
    private String razaoSocialMatriz = "";
    private String identificadorExecutavel = "";
    private boolean enviarApenasUmRPSPorVez = false;
    private boolean processoHomologacao = false;
    private boolean naoUsarDescricaoItemRPS = false;

    public EmpresaNFeVO() {
        super();
    }

    public static void validarDados(EmpresaNFeVO obj) throws ConsistirException {
        if (!obj.getValidarDados()) {
            return;
        }

        if (obj.getRazaoSocial().equals("")) {
            throw new ConsistirException("O campo Razão Social (Empresa) deve ser informado.");
        }

        if (obj.getCpf_cnpj().equals("")) {
            throw new ConsistirException("O campo CPF/CNPJ (Empresa) deve ser informado.");
        } else if (obj.getCpf_cnpj().length() != 11 && obj.getCpf_cnpj().length() != 14) {
            throw new ConsistirException("O campo CPF/CNPJ (Empresa) deve ter pelo menos 11 ou 14 dígitos.");
        }

//        if (obj.getInscricaoMunicipal().equals("")) {
//            throw new ConsistirException("O campo Inscrição Municipal (Empresa) deve ser informado.");
//        }
//
//        if (obj.getInscricaoMunicipal().length() > 20) {
//            throw new ConsistirException("O campo Inscrição Municipal (Empresa) deve ser menor que 20 dígitos.");
//        }

//        if (!obj.getInscricaoMunicipal().matches("\\d*")) {
//            throw new ConsistirException("O campo Inscrição Municipal (Empresa) deve conter apenas números.");
//        }

        if (obj.getDdd_telefone() == null) {
            throw new ConsistirException("O campo DDD do Telefone (Empresa) deve ser informado.");
        }

        if (obj.getTelefone().equals("")) {
            throw new ConsistirException("O campo Telefone (Empresa) deve ser informado.");
        }

        if (obj.getSenhaCertificado().equals("")) {
            throw new ConsistirException("O campo Senha do Certificado (Empresa) deve ser informado.");
        }

        if (obj.getMunicipio().getId_Municipio() == null || obj.getMunicipio().getId_Municipio() == 0) {
            throw new ConsistirException("O campo Município (Empresa) deve ser informado.");
        }

//        if (obj.isAtiva() == null) {
//            throw new ConsistirException("O campo Situação (Empresa) deve ser informado.");
//        }
    }

    public Integer getId_Empresa() {
        return id_Empresa;
    }

    public void setId_Empresa(Integer id_Empresa) {
        this.id_Empresa = id_Empresa;
    }

    public MunicipioNFeVO getMunicipio() {
        if (municipio == null) {
            municipio = new MunicipioNFeVO();
        }
        return municipio;
    }

    public void setMunicipio(MunicipioNFeVO municipio) {
        this.municipio = municipio;
    }

    public String getCpf_cnpj() {
        return cpf_cnpj;
    }

    public void setCpf_cnpj(String cpf_cnpj) {
        this.cpf_cnpj = cpf_cnpj;
    }

    public String getInscricaoMunicipal() {
        return inscricaoMunicipal;
    }

    public void setInscricaoMunicipal(String inscricaoMunicipal) {
        this.inscricaoMunicipal = inscricaoMunicipal;
    }

    public String getRazaoSocial() {
        return razaoSocial;
    }

    public void setRazaoSocial(String razaoSocial) {
        this.razaoSocial = razaoSocial;
    }

    public Integer getDdd_telefone() {
        return ddd_telefone;
    }

    public void setDdd_telefone(Integer ddd_telefone) {
        this.ddd_telefone = ddd_telefone;
    }

    public String getTelefone() {
        return telefone;
    }

    public void setTelefone(String telefone) {
        this.telefone = telefone;
    }

    public String getSenhaCertificado() {
        return senhaCertificado;
    }

    public void setSenhaCertificado(String senhaCertificado) {
        this.senhaCertificado = senhaCertificado;
    }

    public boolean isOptateSimplesNacional() {
        return optateSimplesNacional;
    }

    public void setOptateSimplesNacional(boolean optateSimplesNacional) {
        this.optateSimplesNacional = optateSimplesNacional;
    }

    public boolean isIncentivadorCultural() {
        return incentivadorCultural;
    }

    public void setIncentivadorCultural(boolean incentivadorCultural) {
        this.incentivadorCultural = incentivadorCultural;
    }

    public boolean isEnviarEmail() {
        return enviarEmail;
    }

    public void setEnviarEmail(boolean enviarEmail) {
        this.enviarEmail = enviarEmail;
    }

    public boolean isImprimirImposto() {
        return imprimirImposto;
    }

    public void setImprimirImposto(boolean imprimirImposto) {
        this.imprimirImposto = imprimirImposto;
    }

    public String getSenhaCertificadoDescripto() {
        if (senhaCertificadoDescripto == null) {
            senhaCertificadoDescripto = "";
        }
        return senhaCertificadoDescripto;
    }

    public void setSenhaCertificadoDescripto(String senhaCertificadoDescripto) {
        this.senhaCertificadoDescripto = senhaCertificadoDescripto;
    }

    public boolean manteveSenha() {
        if (this.getSenhaCertificado().length() < 4) {
            return this.getSenhaCertificadoDescripto().equals(this.getSenhaCertificado());
        }
        return this.getSenhaCertificadoDescripto().equals(Uteis.desencriptarNFe(this.getSenhaCertificado()));
    }

    public boolean manteveSenhaInscricaoMunicipal() {
        if (this.getSenhaInscricaoMunicipal().length() < 4) {
            return this.getSenhaInscricaoMunicipalDescripto().equals(this.getSenhaInscricaoMunicipal());
        }
        return this.getSenhaInscricaoMunicipalDescripto().equals(Uteis.desencriptarNFe(this.getSenhaInscricaoMunicipal()));
    }

    public String getChave() {
        return chave;
    }

    public void setChave(String chave) {
        this.chave = chave;
    }

    public File getCertificado() {
        return certificado;
    }

    public void setCertificado(File certificado) {
        this.certificado = certificado;
    }

    public boolean isAtiva() {
        return ativa;
    }

    public void setAtiva(boolean ativa)
    {
        this.ativa = ativa;
    }

    public String getCcm() {
        if (ccm == null) {
            return "";
        }
        return ccm;
    }

    public void setCcm(String ccm) {
        this.ccm = ccm;
    }

    public String getUsuarioInscricaoMunicipal() {
        if (usuarioInscricaoMunicipal == null) {
            usuarioInscricaoMunicipal = "";
        }
        return usuarioInscricaoMunicipal;
    }

    public void setUsuarioInscricaoMunicipal(String usuarioInscricaoMunicipal) {
        this.usuarioInscricaoMunicipal = usuarioInscricaoMunicipal;
    }

    public String getSenhaInscricaoMunicipal() {
        if (senhaInscricaoMunicipal == null) {
            senhaInscricaoMunicipal = "";
        }
        return senhaInscricaoMunicipal;
    }

    public void setSenhaInscricaoMunicipal(String senhaInscricaoMunicipal) {
        this.senhaInscricaoMunicipal = senhaInscricaoMunicipal;
    }

    public String getInscricaoEstadual() {
        return inscricaoEstadual;
    }

    public void setInscricaoEstadual(String inscricaoEstadual) {
        this.inscricaoEstadual = inscricaoEstadual;
    }

    public Double getAliquotaSimples() {
        return aliquotaSimples;
    }

    public void setAliquotaSimples(Double aliquotaSimples) {
        this.aliquotaSimples = aliquotaSimples;
    }

    public String getSenhaInscricaoMunicipalDescripto() {
        if (senhaInscricaoMunicipalDescripto == null) {
            senhaInscricaoMunicipalDescripto = "";
        }
        return senhaInscricaoMunicipalDescripto;
    }

    public void setSenhaInscricaoMunicipalDescripto(String senhaInscricaoMunicipalDescripto) {
        this.senhaInscricaoMunicipalDescripto = senhaInscricaoMunicipalDescripto;
    }

    public String getNomeFantasia() {
        return nomeFantasia;
    }

    public void setNomeFantasia(String nomeFantasia) {
        this.nomeFantasia = nomeFantasia;
    }

    public String getLogradouro() {
        return logradouro;
    }

    public void setLogradouro(String logradouro) {
        this.logradouro = logradouro;
    }

    public String getBairro() {
        return bairro;
    }

    public void setBairro(String bairro) {
        this.bairro = bairro;
    }

    public String getCFOP() {
        return CFOP;
    }

    public void setCFOP(String CFOP) {
        this.CFOP = CFOP;
    }

    @Override
    public String getCEP() {
        return CEP;
    }

    @Override
    public void setCEP(String CEP) {
        this.CEP = CEP;
    }

    public Integer getNumEndereco() {
        return numEndereco;
    }

    public void setNumEndereco(Integer numEndereco) {
        this.numEndereco = numEndereco;
    }

    @Override
    public String getComplemento() {
        return complemento;
    }

    @Override
    public void setComplemento(String complemento) {
        this.complemento = complemento;
    }

    @Override
    public String getEmail() {
        return email;
    }

    @Override
    public void setEmail(String email) {
        this.email = email;
    }

    public Integer getCRT() {
        return CRT;
    }

    public void setCRT(Integer CRT) {
        this.CRT = CRT;
    }

    public Date getDataVencimentoCertificado() {
        return dataVencimentoCertificado;
    }

    public void setDataVencimentoCertificado(Date dataVencimentoCertificado) {
        this.dataVencimentoCertificado = dataVencimentoCertificado;
    }

    public String getDataVencimentoCertificado_Apresentar() {
        if (getDataVencimentoCertificado() != null) {
            return Uteis.getData(getDataVencimentoCertificado());
        } else {
            return "Sem Certificado";
        }
    }

    public String getIdCsc() {
        if (idCsc == null) {
            idCsc = "";
        }
        return idCsc;
    }

    public void setIdCsc(String idCsc) {
        this.idCsc = idCsc;
    }

    public String getCsc() {
        if (csc == null) {
            csc = "";
        }
        return csc;
    }

    public void setCsc(String csc) {
        this.csc = csc;
    }

    public String getInformacaoFisco() {
        if (informacaoFisco == null) {
            informacaoFisco = "";
        }
        return informacaoFisco;
    }

    public void setInformacaoFisco(String informacaoFisco) {
        this.informacaoFisco = informacaoFisco;
    }

    public String getCSTPis() {
        if (CSTPis == null) {
            CSTPis = "";
        }
        return CSTPis;
    }

    public void setCSTPis(String CSTPis) {
        this.CSTPis = CSTPis;
    }

    public String getCSTCofins() {
        if (CSTCofins == null) {
            CSTCofins = "";
        }
        return CSTCofins;
    }

    public void setCSTCofins(String CSTCofins) {
        this.CSTCofins = CSTCofins;
    }

    public boolean isUsarXMLPrefeitura() {
        return usarXMLPrefeitura;
    }

    public void setUsarXMLPrefeitura(boolean usarXMLPrefeitura) {
        this.usarXMLPrefeitura = usarXMLPrefeitura;
    }

    public String getSerieNFCe() {
        if (serieNFCe == null) {
            serieNFCe = "";
        }
        return serieNFCe;
    }

    public void setSerieNFCe(String serieNFCe) {
        this.serieNFCe = serieNFCe;
    }

    public boolean isUsarXMLManipulado() {
        return usarXMLManipulado;
    }

    public void setUsarXMLManipulado(boolean usarXMLManipulado) {
        this.usarXMLManipulado = usarXMLManipulado;
    }

    public boolean isUsarXMLManipuladoNFCe() {
        return usarXMLManipuladoNFCe;
    }

    public void setUsarXMLManipuladoNFCe(boolean usarXMLManipuladoNFCe) {
        this.usarXMLManipuladoNFCe = usarXMLManipuladoNFCe;
    }

    public String getChaveZW() {
        if (chaveZW == null) {
            chaveZW = "";
        }
        return chaveZW;
    }

    public void setChaveZW(String chaveZW) {
        this.chaveZW = chaveZW;
    }

    public Integer getEmpresaZW() {
        if (empresaZW == null) {
            empresaZW = 0;
        }
        return empresaZW;
    }

    public void setEmpresaZW(Integer empresaZW) {
        this.empresaZW = empresaZW;
    }

    public boolean isUsaModuloNFCe() {
        return usaModuloNFCe;
    }

    public void setUsaModuloNFCe(boolean usaModuloNFCe) {
        this.usaModuloNFCe = usaModuloNFCe;
    }

    public String getParams() {
        if (params == null) {
            params = "";
        }
        return params;
    }

    public void setParams(String params) {
        this.params = params;
    }

    public String getCnpjMatriz() {
        if (cnpjMatriz == null) {
            cnpjMatriz = "";
        }
        return cnpjMatriz;
    }

    public void setCnpjMatriz(String cnpjMatriz) {
        this.cnpjMatriz = cnpjMatriz;
    }

    public String getRazaoSocialMatriz() {
        if (razaoSocialMatriz == null) {
            razaoSocialMatriz = "";
        }
        return razaoSocialMatriz;
    }

    public void setRazaoSocialMatriz(String razaoSocialMatriz) {
        this.razaoSocialMatriz = razaoSocialMatriz;
    }

    public String getIdentificadorExecutavel() {
        if (StringUtils.isBlank(identificadorExecutavel)) {
            identificadorExecutavel = "";
        }
        return identificadorExecutavel;
    }

    public void setIdentificadorExecutavel(String identificadorExecutavel) {
        this.identificadorExecutavel = identificadorExecutavel;
    }

    public boolean isUsaModuloNFSe() {
        return usaModuloNFSe;
    }

    public void setUsaModuloNFSe(boolean usaModuloNFSe) {
        this.usaModuloNFSe = usaModuloNFSe;
    }

    public boolean isEnviarApenasUmRPSPorVez() {
        return enviarApenasUmRPSPorVez;
    }

    public void setEnviarApenasUmRPSPorVez(boolean enviarApenasUmRPSPorVez) {
        this.enviarApenasUmRPSPorVez = enviarApenasUmRPSPorVez;
    }

    public boolean isProcessoHomologacao() {
        return processoHomologacao;
    }

    public void setProcessoHomologacao(boolean processoHomologacao) {
        this.processoHomologacao = processoHomologacao;
    }

    public boolean isNaoUsarDescricaoItemRPS() {
        return naoUsarDescricaoItemRPS;
    }

    public void setNaoUsarDescricaoItemRPS(boolean naoUsarDescricaoItemRPS) {
        this.naoUsarDescricaoItemRPS = naoUsarDescricaoItemRPS;
    }
}

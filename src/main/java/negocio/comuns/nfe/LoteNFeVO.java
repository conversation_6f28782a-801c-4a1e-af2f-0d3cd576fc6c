package negocio.comuns.nfe;

import negocio.comuns.arquitetura.SuperVO;
import negocio.comuns.utilitarias.ConsistirException;

import java.util.Date;

public class LoteNFeVO extends SuperVO {

    private Integer id_lote;
    private EmpresaNFeVO empresaNFeVO;
    private Date dataIni;
    private Date dataFim;
    private Integer qtdRPS;
    private Double valorTotalServico;
    private Double valorTotalDeducoes;
    private String urlConfirmacao;

    public LoteNFeVO() {
        super();
    }

    public static void validarDados(LoteNFeVO obj) throws ConsistirException {
        if (!obj.getValidarDados()) {
            return;
        }

        if (obj.getEmpresaNFeVO().getId_Empresa() == null) {
            throw new ConsistirException("O campo Empresa (Lote) deve ser informado.");
        }
    }

    public Integer getId_lote() {
        return id_lote;
    }

    public void setId_lote(Integer id_lote) {
        this.id_lote = id_lote;
    }

    public EmpresaNFeVO getEmpresaNFeVO() {
        if (empresaNFeVO == null) {
            empresaNFeVO = new EmpresaNFeVO();
        }
        return empresaNFeVO;
    }

    public void setEmpresaNFeVO(EmpresaNFeVO empresaNFeVO) {
        this.empresaNFeVO = empresaNFeVO;
    }

    public Date getDataIni() {
        return dataIni;
    }

    public void setDataIni(Date dataIni) {
        this.dataIni = dataIni;
    }

    public Date getDataFim() {
        return dataFim;
    }

    public void setDataFim(Date dataFim) {
        this.dataFim = dataFim;
    }

    public Integer getQtdRPS() {
        return qtdRPS;
    }

    public void setQtdRPS(Integer qtdRPS) {
        this.qtdRPS = qtdRPS;
    }

    public Double getValorTotalServico() {
        return valorTotalServico;
    }

    public void setValorTotalServico(Double valorTotalServico) {
        this.valorTotalServico = valorTotalServico;
    }

    public Double getValorTotalDeducoes() {
        return valorTotalDeducoes;
    }

    public void setValorTotalDeducoes(Double valorTotalDeducoes) {
        this.valorTotalDeducoes = valorTotalDeducoes;
    }

    public String getUrlConfirmacao() {
        return urlConfirmacao;
    }

    public void setUrlConfirmacao(String urlConfirmacao) {
        this.urlConfirmacao = urlConfirmacao;
    }
}

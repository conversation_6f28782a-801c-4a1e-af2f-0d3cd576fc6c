package negocio.comuns.nfe;

import negocio.comuns.arquitetura.SuperTO;

public class NFeFaturasImprimirTO extends SuperTO {

    private Integer ordem;
    private String numero;
    private String dataVencimento;
    private String valor;

    public Integer getOrdem() {
        if (ordem == null) {
            ordem = 0;
        }
        return ordem;
    }

    public void setOrdem(Integer ordem) {
        this.ordem = ordem;
    }

    public String getNumero() {
        if (numero == null) {
            numero = "";
        }
        return numero;
    }

    public void setNumero(String numero) {
        this.numero = numero;
    }

    public String getDataVencimento() {
        if (dataVencimento == null) {
            dataVencimento = "";
        }
        return dataVencimento;
    }

    public void setDataVencimento(String dataVencimento) {
        this.dataVencimento = dataVencimento;
    }

    public String getValor() {
        if (valor == null) {
            valor = "";
        }
        return valor;
    }

    public void setValor(String valor) {
        this.valor = valor;
    }
}

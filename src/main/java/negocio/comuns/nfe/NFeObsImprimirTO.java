package negocio.comuns.nfe;

import negocio.comuns.arquitetura.SuperTO;

public class NFeObsImprimirTO extends SuperTO {

    private String campo;
    private String texto;


    public String getCampo() {
        if (campo == null) {
            campo = "";
        }
        return campo;
    }

    public void setCampo(String campo) {
        this.campo = campo;
    }

    public String getTexto() {
        if (texto == null) {
            texto = "";
        }
        return texto;
    }

    public void setTexto(String texto) {
        this.texto = texto;
    }
}

package negocio.comuns.nfe;

import negocio.comuns.arquitetura.SuperTO;

import java.io.InputStream;


public class NotasImprimirTO extends SuperTO {

    private NFSeImprimirTO notaNFSe;
    private NFeImprimirTO notaNFe;

    public NFSeImprimirTO getNotaNFSe() {
        return notaNFSe;
    }

    public void setNotaNFSe(NFSeImprimirTO notaNFSe) {
        this.notaNFSe = notaNFSe;
    }

    public NFeImprimirTO getNotaNFe() {
        return notaNFe;
    }

    public void setNotaNFe(NFeImprimirTO notaNFe) {
        this.notaNFe = notaNFe;
    }

    public boolean getNFSe() {
        return getNotaNFSe() != null;
    }

    public String getNomeArquivo() {
        if (getNFSe()) {
            return getNotaNFSe().getId_RPS() + " - " + getNotaNFSe().getNumeroNota();
        } else {
            return getNotaNFe().getChaveAcesso().replaceAll(" ", "") + "-procNFe";
        }
    }

    public Integer getIdEmpresa() {
        if (getNFSe()) {
            return getNotaNFSe().getIdEmpresaNFSe();
        } else {
            return getNotaNFe().getIdEmpresaNFSe();
        }
    }

    public byte[] getLogomarcaPrestadorByte() {
        if (getNFSe()) {
            return getNotaNFSe().getLogomarcaPrestadorByte();
        } else {
            return getNotaNFe().getLogomarcaPrestadorByte();
        }
    }

    public void setLogomarcaPrestador(InputStream logomarcaPrestador) {
        if (getNFSe()) {
            getNotaNFSe().setLogomarcaPrestador(logomarcaPrestador);
        } else {
            getNotaNFe().setLogomarcaPrestador(logomarcaPrestador);
        }
    }

    public String getIdRPS() {
        if (getNFSe()) {
            return getNotaNFSe().getId_RPS();
        } else {
            return getNotaNFe().getId_RPS();
        }
    }
}

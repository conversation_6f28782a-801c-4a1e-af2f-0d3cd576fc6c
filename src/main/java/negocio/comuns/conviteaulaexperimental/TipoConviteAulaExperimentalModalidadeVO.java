package negocio.comuns.conviteaulaexperimental;

import negocio.comuns.arquitetura.SuperVO;
import negocio.comuns.plano.ModalidadeVO;

import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

/**
 * Created by ulisses on 08/01/2016.
 */
public class TipoConviteAulaExperimentalModalidadeVO extends SuperVO {


    private Integer codigo = 0;
    private TipoConviteAulaExperimentalVO tipoConviteAulaExperimentalVO;
    private ModalidadeVO modalidadeVO = new ModalidadeVO();
    private Set<TipoConviteAulaExperimentalModalidadeHorarioVO> listaHorario;
    private List<TipoConviteAulaExperimentalModalidadeHorarioVO> listaHorario_Apresentar;
    private List<TipoConviteAulaExperimentalModalidadeHorarioVO> listaHorarioAux;


    @Override
    public Integer getCodigo() {
        return codigo;
    }

    @Override
    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public TipoConviteAulaExperimentalVO getTipoConviteAulaExperimentalVO() {
        return tipoConviteAulaExperimentalVO;
    }

    public void setTipoConviteAulaExperimentalVO(TipoConviteAulaExperimentalVO tipoConviteAulaExperimentalVO) {
        this.tipoConviteAulaExperimentalVO = tipoConviteAulaExperimentalVO;
    }

    public ModalidadeVO getModalidadeVO() {
        return modalidadeVO;
    }

    public void setModalidadeVO(ModalidadeVO modalidadeVO) {
        this.modalidadeVO = modalidadeVO;
    }

    @Override
    public int hashCode(){
        return this.modalidadeVO.getCodigo().hashCode();
    }

    @Override
    public boolean equals(Object obj){
        if ((obj == null) || (!(obj instanceof TipoConviteAulaExperimentalModalidadeVO)))
            return false;
        return ((TipoConviteAulaExperimentalModalidadeVO)obj).getModalidadeVO().getCodigo().equals(this.modalidadeVO.getCodigo());
    }


    public Set<TipoConviteAulaExperimentalModalidadeHorarioVO> getListaHorario() {
        if (this.listaHorario == null)
            this.listaHorario = new HashSet<TipoConviteAulaExperimentalModalidadeHorarioVO>();
        return listaHorario;
    }

    public void setListaHorario(Set<TipoConviteAulaExperimentalModalidadeHorarioVO> listaHorario) {
        this.listaHorario = listaHorario;
    }

    public List<TipoConviteAulaExperimentalModalidadeHorarioVO> getListaHorario_Apresentar() {
        if (this.listaHorario_Apresentar == null)
            this.listaHorario_Apresentar = new ArrayList<TipoConviteAulaExperimentalModalidadeHorarioVO>();
        this.listaHorario_Apresentar.clear();
        this.listaHorario_Apresentar.addAll(getListaHorario());
        return listaHorario_Apresentar;
    }

    public List<TipoConviteAulaExperimentalModalidadeHorarioVO> getListaHorarioAux() {
        return listaHorarioAux;
    }

    public void setListaHorarioAux(List<TipoConviteAulaExperimentalModalidadeHorarioVO> listaHorarioAux) {
        this.listaHorarioAux = listaHorarioAux;
    }
}

package negocio.comuns.conviteaulaexperimental;

import annotations.arquitetura.NaoControlarLogAlteracao;
import negocio.comuns.arquitetura.SuperVO;
import negocio.comuns.arquitetura.UsuarioVO;
import negocio.comuns.basico.ClienteVO;
import negocio.comuns.basico.ColaboradorVO;
import negocio.comuns.crm.IndicadoVO;
import negocio.comuns.crm.PassivoVO;
import negocio.comuns.utilitarias.ConsistirException;
import negocio.comuns.utilitarias.UtilReflection;

import java.util.Date;
import java.util.List;
import negocio.comuns.basico.ReposicaoVO;
import negocio.comuns.utilitarias.Uteis;

/**
 * Created by ulisses on 08/01/2016.
 */
public class ConviteAulaExperimentalVO extends SuperVO {

    private Integer codigo = 0;
    private TipoConviteAulaExperimentalVO tipoConviteAulaExperimentalVO;
    private IndicadoVO indicadoConvidado;
    private PassivoVO passivoConvidado;
    private ClienteVO clienteConvidado;
    private UsuarioVO usuarioConvidou;
    private ClienteVO clienteConvidou;
    private ColaboradorVO colaboradorResponsavelConvite;
    private Date dataValidacaoConvidado;
    private Date dataLancamento;
    private ClienteVO clienteIndicadoOuPassivo;
    @NaoControlarLogAlteracao
    private String modalidades;
    @NaoControlarLogAlteracao
    private List<ReposicaoVO> reposicoes;


    @Override
    public Integer getCodigo() {
        return codigo;
    }

    @Override
    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public TipoConviteAulaExperimentalVO getTipoConviteAulaExperimentalVO() {
        return tipoConviteAulaExperimentalVO;
    }

    public void setTipoConviteAulaExperimentalVO(TipoConviteAulaExperimentalVO tipoConviteAulaExperimentalVO) {
        this.tipoConviteAulaExperimentalVO = tipoConviteAulaExperimentalVO;
    }

    public IndicadoVO getIndicadoConvidado() {
        return indicadoConvidado;
    }

    public void setIndicadoConvidado(IndicadoVO indicadoConvidado) {
        this.indicadoConvidado = indicadoConvidado;
    }

    public PassivoVO getPassivoConvidado() {
        return passivoConvidado;
    }

    public void setPassivoConvidado(PassivoVO passivoConvidado) {
        this.passivoConvidado = passivoConvidado;
    }

    public ClienteVO getClienteConvidado() {
        return clienteConvidado;
    }

    public void setClienteConvidado(ClienteVO clienteConvidado) {
        this.clienteConvidado = clienteConvidado;
    }

    public UsuarioVO getUsuarioConvidou() {
        return usuarioConvidou;
    }

    public void setUsuarioConvidou(UsuarioVO usuarioConvidou) {
        this.usuarioConvidou = usuarioConvidou;
    }

    public ClienteVO getClienteConvidou() {
        return clienteConvidou;
    }

    public void setClienteConvidou(ClienteVO clienteConvidou) {
        this.clienteConvidou = clienteConvidou;
    }

    public Date getDataValidacaoConvidado() {
        return dataValidacaoConvidado;
    }

    public void setDataValidacaoConvidado(Date dataValidacaoConvidado) {
        this.dataValidacaoConvidado = dataValidacaoConvidado;
    }

    public String getDataLancamentoApresentar() {
        return Uteis.getData(dataLancamento);
    }

    public Date getDataLancamento() {
        return dataLancamento;
    }

    public void setDataLancamento(Date dataLancamento) {
        this.dataLancamento = dataLancamento;
    }

    public ColaboradorVO getColaboradorResponsavelConvite() {
        return colaboradorResponsavelConvite;
    }

    public void setColaboradorResponsavelConvite(ColaboradorVO colaboradorResponsavelConvite) {
        this.colaboradorResponsavelConvite = colaboradorResponsavelConvite;
    }

    public static void validarDados(ConviteAulaExperimentalVO obj) throws Exception {
        if (!obj.getValidarDados()) {
            return;
        }
        if (UtilReflection.objetoMaiorQueZero(obj.tipoConviteAulaExperimentalVO, "getCodigo()")) {
            throw new ConsistirException("O campo TIPOCONVITE (ConviteAulaExperimental) deve ser informado.");
        }
        if (UtilReflection.objetoMaiorQueZero(obj.colaboradorResponsavelConvite, "getCodigo()")) {
            throw new ConsistirException("O campo COLABORADORRESPONSAVEL (ConviteAulaExperimental) deve ser informado.");
        }

    }

    public ClienteVO getClienteIndicadoOuPassivo() {
        return clienteIndicadoOuPassivo;
    }

    public void setClienteIndicadoOuPassivo(ClienteVO clienteIndicadoOuPassivo) {
        this.clienteIndicadoOuPassivo = clienteIndicadoOuPassivo;
    }

    public boolean conviteDeIndicado()throws Exception{
        return UtilReflection.objetoMaiorQueZero(this, "getIndicadoConvidado().getCodigo()");
    }
    public boolean conviteDePassivo()throws Exception{
        return UtilReflection.objetoMaiorQueZero(this, "getPassivoConvidado().getCodigo()");
    }
    public boolean conviteDeCliente()throws Exception{
        return UtilReflection.objetoMaiorQueZero(this, "getClienteConvidado().getCodigo()");
    }

    public String getModalidades() {
        return modalidades;
    }

    public void setModalidades(String modalidades) {
        this.modalidades = modalidades;
    }

    public List<ReposicaoVO> getReposicoes() {
        return reposicoes;
    }

    public void setReposicoes(List<ReposicaoVO> reposicoes) {
        this.reposicoes = reposicoes;
    }

    
}

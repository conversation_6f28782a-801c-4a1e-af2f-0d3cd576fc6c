package negocio.comuns.conviteaulaexperimental;

import br.com.pactosolucoes.ce.comuns.enumerador.DiaSemana;
import negocio.comuns.arquitetura.SuperVO;
import negocio.comuns.utilitarias.ConsistirException;

import java.util.ArrayList;
import java.util.List;

/**
 * Created by ulisses on 08/01/2016.
 */
public class TipoConviteAulaExperimentalModalidadeHorarioVO extends SuperVO {

    private Integer codigo = 0;
    private TipoConviteAulaExperimentalModalidadeVO tipoConviteAulaExperimentalModalidadeVO;
    private String diasSemana="";
    private String horaInicial = "";
    private String horaFinal = "";
    private List<DiaSemanaTO> listaDiaSemana;
    private String diasSemanaApresentarNaGrid;

    public TipoConviteAulaExperimentalModalidadeHorarioVO(){
        listaDiaSemana = new ArrayList<DiaSemanaTO>();
        for (DiaSemana obj: DiaSemana.values()){
            DiaSemanaTO dia = new DiaSemanaTO();
            dia.setDiaSemana(obj);
            this.listaDiaSemana.add(dia);
        }
    }

    public void selecionarDiasSemana(){

        if ((this.diasSemana != null) && (!this.diasSemana.equals(""))){
            String[] dias = getDiasSemana().split(",");
            for(String dia: dias){
                for (DiaSemanaTO diaSemanaTO: getListaDiaSemana()){
                    if (diaSemanaTO.getDiaSemana().getNumeral() == Integer.parseInt(dia)){
                        diaSemanaTO.setSelecionado(true);
                    }

                }
            }
            montarDescricaoDiaSemanaSelecionados();
        }
    }

    public void montarDescricaoDiaSemanaSelecionados(){
        StringBuilder descricaoSelecionados = new StringBuilder();
        for (DiaSemanaTO diaSemanaTO: getListaDiaSemana()){
            if (diaSemanaTO.isSelecionado()){
                if (descricaoSelecionados.length() <= 0)
                    descricaoSelecionados.append(diaSemanaTO.getDiaSemana().getDescricaoSimples());
                else
                    descricaoSelecionados.append(",").append(diaSemanaTO.getDiaSemana().getDescricaoSimples());
            }
        }
        this.diasSemanaApresentarNaGrid = descricaoSelecionados.toString();
    }

    public void montarDiasSemana(){
        StringBuilder dias = new StringBuilder();
        for (DiaSemanaTO diaSemanaTO: listaDiaSemana){
            if (diaSemanaTO.isSelecionado()){
                if (dias.length() ==0){
                    dias.append(diaSemanaTO.getDiaSemana().getNumeral());
                }else{
                    dias.append(",").append(diaSemanaTO.getDiaSemana().getNumeral());
                }
            }
        }
        this.diasSemana = dias.toString();
    }


    @Override
    public Integer getCodigo() {
        return codigo;
    }

    @Override
    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public TipoConviteAulaExperimentalModalidadeVO getTipoConviteAulaExperimentalModalidadeVO() {
        return tipoConviteAulaExperimentalModalidadeVO;
    }

    public void setTipoConviteAulaExperimentalModalidadeVO(TipoConviteAulaExperimentalModalidadeVO tipoConviteAulaExperimentalModalidadeVO) {
        this.tipoConviteAulaExperimentalModalidadeVO = tipoConviteAulaExperimentalModalidadeVO;
    }

    public String getDiasSemana() {
        return diasSemana;
    }

    public void setDiasSemana(String diasSemana) {
        this.diasSemana = diasSemana;
    }

    public String getHoraInicial() {
        return horaInicial;
    }

    public void setHoraInicial(String horaInicial) {
        this.horaInicial = horaInicial;
    }

    public String getHoraFinal() {
        return horaFinal;
    }

    public void setHoraFinal(String horaFinal) {
        this.horaFinal = horaFinal;
    }

    public static void validarDados(TipoConviteAulaExperimentalModalidadeHorarioVO obj)throws Exception{
        if ((obj.getHoraInicial().equals("")) ||
                (obj.getHoraInicial().equals(""))){
            throw new ConsistirException("O campo Intervalo de horas (TipoConviteAulaExperimentalModalidadeHorario) deve ser informado.");
        }
        String[] horaIni = obj.getHoraInicial().split(":");
        String[] horaFim = obj.getHoraFinal().split(":");
        if ( ((horaIni.length < 2) || (horaFim.length < 2)) ||
            ((horaIni[0] == null) || (horaIni[0].equals(""))) ||
            ((horaFim[0] == null) || (horaFim[0].equals(""))) ||
            ((horaIni[0].length() < 2) || (horaFim[0].length() < 2)) ||
            ((horaIni[1].length() < 2) || (horaFim[1].length() < 2)) ||
            ((Integer.parseInt(horaIni[0]) > 23) || (Integer.parseInt(horaFim[0]) > 23)) ||
            ((Integer.parseInt(horaIni[1]) > 59) || (Integer.parseInt(horaFim[1]) > 59)) ){
            throw new ConsistirException("O campo Intervalo de horas (TipoConviteAulaExperimentalModalidadeHorario) deve estar no formato correto de HH:MM(horas/minutos).");
        }

        if (Integer.parseInt(horaIni[0]) >= Integer.parseInt(horaFim[0])){
            throw new ConsistirException("A hora inicial deve ser menor que a hora final(TipoConviteAulaExperimentalModalidadeHorario)");
        }
    }


    @Override
    public int hashCode(){
        return 1;
    }

    @Override
    public boolean equals(Object obj){
        if ((obj == null) || (!(obj instanceof TipoConviteAulaExperimentalModalidadeHorarioVO)))
            return false;
        return ((TipoConviteAulaExperimentalModalidadeHorarioVO)obj).getHoraInicial().equals(this.horaInicial) &&
                ((TipoConviteAulaExperimentalModalidadeHorarioVO)obj).getHoraFinal().equals(this.horaFinal) &&
                ((TipoConviteAulaExperimentalModalidadeHorarioVO)obj).getDiasSemana().equals(this.diasSemana);
    }

    public List<DiaSemanaTO> getListaDiaSemana() {
        return listaDiaSemana;
    }

    public void setListaDiaSemana(List<DiaSemanaTO> listaDiaSemana) {
        this.listaDiaSemana = listaDiaSemana;
    }



    public String getDiasSemanaApresentarNaGrid() {
        return diasSemanaApresentarNaGrid;
    }

    public void setDiasSemanaApresentarNaGrid(String diasSemanaApresentarNaGrid) {
        this.diasSemanaApresentarNaGrid = diasSemanaApresentarNaGrid;
    }
}

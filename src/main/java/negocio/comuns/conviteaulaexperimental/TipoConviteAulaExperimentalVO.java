package negocio.comuns.conviteaulaexperimental;

import annotations.arquitetura.ChavePrimaria;
import negocio.comuns.arquitetura.SuperVO;
import negocio.comuns.basico.EmpresaVO;
import negocio.comuns.utilitarias.Calendario;
import negocio.comuns.utilitarias.ConsistirException;
import negocio.comuns.utilitarias.UtilReflection;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import negocio.comuns.utilitarias.Uteis;

/**
 * Created by ulisses on 08/01/2016.
 */
public class TipoConviteAulaExperimentalVO extends SuperVO {

    @ChavePrimaria
    private Integer codigo = 0;
    private String descricao;
    private Date vigenciaInicial;
    private Date vigenciaFinal;
    private boolean aulasAgendadasEmDiasSeguido = true;
    private Integer quantidadeAulaExperimental;
    private Integer quantidadeConviteAlunoPodeEnviar = 0;
    private boolean alunoPodeEnviarConvite = false;
    private boolean colaboradorPodeEnviarConvite = false;
    private EmpresaVO empresaVO;
    private Date dataLancamento;

    private List<TipoConviteAulaExperimentalModalidadeVO> listaModalidade;


    @Override
    public Integer getCodigo() {
        return codigo;
    }

    @Override
    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public String getDescricao() {
        return descricao;
    }

    public void setDescricao(String descricao) {
        this.descricao = descricao;
    }

    public Date getVigenciaInicial() {
        return vigenciaInicial;
    }

    public void setVigenciaInicial(Date vigenciaInicial) {
        this.vigenciaInicial = vigenciaInicial;
    }

    public Date getVigenciaFinal() {
        return vigenciaFinal;
    }

    public void setVigenciaFinal(Date vigenciaFinal) {
        this.vigenciaFinal = vigenciaFinal;
    }

    public boolean isAulasAgendadasEmDiasSeguido() {
        return aulasAgendadasEmDiasSeguido;
    }

    public void setAulasAgendadasEmDiasSeguido(boolean aulasAgendadasEmDiasSeguido) {
        this.aulasAgendadasEmDiasSeguido = aulasAgendadasEmDiasSeguido;
    }

    public Integer getQuantidadeAulaExperimental() {
        return quantidadeAulaExperimental;
    }

    public void setQuantidadeAulaExperimental(Integer quantidadeAulaExperimental) {
        this.quantidadeAulaExperimental = quantidadeAulaExperimental;
    }

    public Integer getQuantidadeConviteAlunoPodeEnviar() {
        return quantidadeConviteAlunoPodeEnviar;
    }

    public void setQuantidadeConviteAlunoPodeEnviar(Integer quantidadeConviteAlunoPodeEnviar) {
        this.quantidadeConviteAlunoPodeEnviar = quantidadeConviteAlunoPodeEnviar;
    }

    public boolean isAlunoPodeEnviarConvite() {
        return alunoPodeEnviarConvite;
    }

    public void setAlunoPodeEnviarConvite(boolean alunoPodeEnviarConvite) {
        this.alunoPodeEnviarConvite = alunoPodeEnviarConvite;
    }

    public boolean isColaboradorPodeEnviarConvite() {
        return colaboradorPodeEnviarConvite;
    }

    public void setColaboradorPodeEnviarConvite(boolean colaboradorPodeEnviarConvite) {
        this.colaboradorPodeEnviarConvite = colaboradorPodeEnviarConvite;
    }

    public EmpresaVO getEmpresaVO() {
        return empresaVO;
    }

    public void setEmpresaVO(EmpresaVO empresaVO) {
        this.empresaVO = empresaVO;
    }

    public Date getDataLancamento() {
        return dataLancamento;
    }

    public void setDataLancamento(Date dataLancamento) {
        this.dataLancamento = dataLancamento;
    }

    public String getVigenciaInicial_Apresentar(){
        return Calendario.getData(this.vigenciaInicial, "dd/MM/yyyy HH:mm:ss");
    }
    public String getVigenciaFinal_Apresentar(){
        return Calendario.getData(this.vigenciaFinal, "dd/MM/yyyy HH:mm:ss");
    }

    public String getVigenciaSemHoraApresentar(){
        return Uteis.getData(this.vigenciaFinal);
    }

    public String getEmpresa_Apresentar() {
        return this.empresaVO.getNome();

    }


    public static void validarDados(TipoConviteAulaExperimentalVO obj) throws Exception {
        if (!obj.getValidarDados()) {
            return;
        }
        if (!(UtilReflection.objetoMaiorQueZero(obj, "getEmpresaVO().getCodigo()"))){
            throw new ConsistirException("O campo Empresa (TipoConviteAulaExperimental) deve ser informado.");
        }
        if ((obj.getDescricao() == null) || (obj.getDescricao().equals(""))) {
            throw new ConsistirException("O campo Descrição (TipoConviteAulaExperimental) deve ser informado.");
        }

        if (obj.getVigenciaInicial() == null) {
            throw new ConsistirException("O campo Vigência De (TipoConviteAulaExperimental) deve ser informado.");
        }
        if (obj.getVigenciaFinal() == null) {
            throw new ConsistirException("O campo Vigência Até (TipoConviteAulaExperimental) deve ser informado.");
        }
        if (Calendario.maior(obj.getVigenciaInicial(), obj.getVigenciaFinal())) {
            throw new ConsistirException("O campo 'Vigência De' deve ser menor que o campo 'Vigência Até'.");
        }
        if (!(UtilReflection.objetoMaiorQueZero(obj, "getQuantidadeAulaExperimental()"))){
            throw new ConsistirException("O campo Quantidade aula experimental que o convidado pode fazer (TipoConviteAulaExperimental) deve ser informado.");
        }
        if ((!obj.isAlunoPodeEnviarConvite()) && (!obj.isColaboradorPodeEnviarConvite())){
            throw new ConsistirException("Um dos campos 'Aluno pode enviar convite' ou 'Colaborador pode enviar convite' (TipoConviteAulaExperimental) deve ser informado.");
        }
        if ((obj.isAlunoPodeEnviarConvite()) && (!UtilReflection.objetoMaiorQueZero(obj, "getQuantidadeConviteAlunoPodeEnviar()"))){
            throw new ConsistirException("O campo Quantidade máxima de convites o aluno pode enviar (TipoConviteAulaExperimental) deve ser informado.");
        }
    }

    public List<TipoConviteAulaExperimentalModalidadeVO> getListaModalidade() {
        if (this.listaModalidade == null)
            this.listaModalidade = new ArrayList<TipoConviteAulaExperimentalModalidadeVO>();
        return listaModalidade;
    }

    public void setListaModalidade(List<TipoConviteAulaExperimentalModalidadeVO> listaModalidade) {
        this.listaModalidade = listaModalidade;
    }
}

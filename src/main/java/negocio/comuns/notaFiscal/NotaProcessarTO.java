package negocio.comuns.notaFiscal;

import negocio.comuns.arquitetura.UsuarioVO;
import negocio.comuns.contrato.MovProdutoVO;
import negocio.comuns.financeiro.*;
import negocio.comuns.financeiro.enumerador.TipoRelatorioDF;
import negocio.comuns.utilitarias.UteisValidacao;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

public class NotaProcessarTO {

    private Integer codigo;
    private ChequeVO chequeVO;
    private MovProdutoVO movProdutoVO;
    private MovPagamentoVO movPagamentoVO;
    private CartaoCreditoVO cartaoCreditoVO;
    private ReciboPagamentoVO reciboPagamentoVO;
    private MovContaVO movContaVO;
    private boolean sucesso = false;
    private String retorno;
    private Date dataEmissaoPrevista;
    private SituacaoNotaFiscalEnum situacaoNotaFiscal;
    private List<NFSeEmitidaVO> listaNFSeEmitidaVO;
    private List<NotaFiscalConsumidorEletronicaVO> listaNotaFiscalConsumidorVO;
    private String numeroNotaManual;
    private UsuarioVO usuarioVO;
    private String chave;
    private Integer notaFiscalAnterior;
    private Integer notaFiscalNova;


    public NotaProcessarTO(String chave){
        //sempre adicionar a CHAVE pois é utilizado no ENOTAS!!! by Luiz Felipe
        this.chave = chave;
    }

    public NotaProcessarTO(ItemGestaoNotasTO itemIndividual, String numeroNotaManual,
                           TipoRelatorioDF tipoEmissao, String chave) {
        //sempre adicionar a CHAVE pois é utilizado no ENOTAS!!! by Luiz Felipe

        this.chave = chave;
        this.codigo = itemIndividual.getCodigo();
        this.dataEmissaoPrevista = itemIndividual.getDataReferenciaItem();
        this.numeroNotaManual = numeroNotaManual;
        this.movContaVO = itemIndividual.getMovContaVO();

//        objs.add(new SelectItem(TipoRelatorioDF.RECEITA.getCodigo(), "Por Receita"));
//        objs.add(new SelectItem(TipoRelatorioDF.COMPETENCIA.getCodigo(), "Por Competência"));
//        objs.add(new SelectItem(TipoRelatorioDF.FATURAMENTO_DE_CAIXA.getCodigo(), "Por Faturamento Recebido"));
//        PARA FATURAMENTO A DESCRIÇÃO É DIFERENTE --- POIS O FATURAMENTO DO NFSE É DIFERENTE DO ZILLYONWEB -- TICKET #3493
//        objs.add(new SelectItem(TipoRelatorioDF.FATURAMENTO.getCodigo(), "Por Faturamento Recebido Por Parcela"));

        if (tipoEmissao.equals(TipoRelatorioDF.COMPETENCIA) || tipoEmissao.equals(TipoRelatorioDF.FATURAMENTO) || tipoEmissao.equals(TipoRelatorioDF.COMPETENCIA_INDEPENDENTE_QUITACAO)) {

            this.movProdutoVO = itemIndividual.getMovProdutoVO();

        } else if (tipoEmissao.equals(TipoRelatorioDF.RECEITA)) {

            if (itemIndividual.getChequeVO() != null) {
                this.chequeVO = itemIndividual.getChequeVO();
            } else if (itemIndividual.getCartaoCreditoVO() != null) {
                this.cartaoCreditoVO = itemIndividual.getCartaoCreditoVO();
            } else if (itemIndividual.getMovPagamentoVO() != null) {
                this.movPagamentoVO = itemIndividual.getMovPagamentoVO();
            }

        } else if (tipoEmissao.equals(TipoRelatorioDF.FATURAMENTO_DE_CAIXA)) {

            this.reciboPagamentoVO = itemIndividual.getReciboPagamentoVO();

        }
    }

    public NotaProcessarTO(Integer notaFiscalReenviar, NFSeEmitidaVO nfSeEmitidaVO, TipoRelatorioDF tipoEmissao, String chave) {
        //sempre adicionar a CHAVE pois é utilizado no ENOTAS!!! by Luiz Felipe

        this.chave = chave;
        this.codigo = nfSeEmitidaVO.getCodigo();
        this.dataEmissaoPrevista = nfSeEmitidaVO.getDataReferencia();
        this.movContaVO = nfSeEmitidaVO.getMovConta();
        this.notaFiscalAnterior = notaFiscalReenviar;

//        objs.add(new SelectItem(TipoRelatorioDF.RECEITA.getCodigo(), "Por Receita"));
//        objs.add(new SelectItem(TipoRelatorioDF.COMPETENCIA.getCodigo(), "Por Competência"));
//        objs.add(new SelectItem(TipoRelatorioDF.FATURAMENTO_DE_CAIXA.getCodigo(), "Por Faturamento Recebido"));
//        PARA FATURAMENTO A DESCRIÇÃO É DIFERENTE --- POIS O FATURAMENTO DO NFSE É DIFERENTE DO ZILLYONWEB -- TICKET #3493
//        objs.add(new SelectItem(TipoRelatorioDF.FATURAMENTO.getCodigo(), "Por Faturamento Recebido Por Parcela"));

        if (tipoEmissao.equals(TipoRelatorioDF.COMPETENCIA) || tipoEmissao.equals(TipoRelatorioDF.FATURAMENTO)) {

            this.movProdutoVO = nfSeEmitidaVO.getMovProduto();

        } else if (tipoEmissao.equals(TipoRelatorioDF.RECEITA)) {

            if (nfSeEmitidaVO.getCheque() != null && !UteisValidacao.emptyNumber(nfSeEmitidaVO.getCheque().getCodigo())) {
                this.chequeVO = nfSeEmitidaVO.getCheque();
            } else if (nfSeEmitidaVO.getCartaoCredito() != null && !UteisValidacao.emptyNumber(nfSeEmitidaVO.getCartaoCredito().getCodigo())) {
                this.cartaoCreditoVO = nfSeEmitidaVO.getCartaoCredito();
            } else if (nfSeEmitidaVO.getMovPagamento() != null && !UteisValidacao.emptyNumber(nfSeEmitidaVO.getMovPagamento().getCodigo())) {
                this.movPagamentoVO = nfSeEmitidaVO.getMovPagamento();
            }

        } else if (tipoEmissao.equals(TipoRelatorioDF.FATURAMENTO_DE_CAIXA)) {

            this.reciboPagamentoVO = nfSeEmitidaVO.getRecibo();

        }

        if ((this.movProdutoVO == null || UteisValidacao.emptyNumber(this.movProdutoVO.getCodigo())) &&
                (this.chequeVO == null || UteisValidacao.emptyNumber(this.chequeVO.getCodigo())) &&
                (this.cartaoCreditoVO == null || UteisValidacao.emptyNumber(this.cartaoCreditoVO.getCodigo())) &&
                (this.movPagamentoVO == null || UteisValidacao.emptyNumber(this.movPagamentoVO.getCodigo())) &&
                (this.reciboPagamentoVO == null || UteisValidacao.emptyNumber(this.reciboPagamentoVO.getCodigo())) &&
                (this.movProdutoVO == null || UteisValidacao.emptyNumber(this.movProdutoVO.getCodigo()))) {

            this.reciboPagamentoVO = nfSeEmitidaVO.getRecibo();
            this.movProdutoVO = nfSeEmitidaVO.getMovProduto();
            if (nfSeEmitidaVO.getCheque() != null && !UteisValidacao.emptyNumber(nfSeEmitidaVO.getCheque().getCodigo())) {
                this.chequeVO = nfSeEmitidaVO.getCheque();
            } else if (nfSeEmitidaVO.getCartaoCredito() != null && !UteisValidacao.emptyNumber(nfSeEmitidaVO.getCartaoCredito().getCodigo())) {
                this.cartaoCreditoVO = nfSeEmitidaVO.getCartaoCredito();
            } else if (nfSeEmitidaVO.getMovPagamento() != null && !UteisValidacao.emptyNumber(nfSeEmitidaVO.getMovPagamento().getCodigo())) {
                this.movPagamentoVO = nfSeEmitidaVO.getMovPagamento();
            }
        }
    }

    public ChequeVO getChequeVO() {
        if (chequeVO == null) {
            chequeVO = new ChequeVO();
        }
        return chequeVO;
    }

    public void setChequeVO(ChequeVO chequeVO) {
        this.chequeVO = chequeVO;
    }

    public MovProdutoVO getMovProdutoVO() {
        if (movProdutoVO == null) {
            movProdutoVO = new MovProdutoVO();
        }
        return movProdutoVO;
    }

    public void setMovProdutoVO(MovProdutoVO movProdutoVO) {
        this.movProdutoVO = movProdutoVO;
    }

    public MovPagamentoVO getMovPagamentoVO() {
        if (movPagamentoVO == null) {
            movPagamentoVO = new MovPagamentoVO();
        }
        return movPagamentoVO;
    }

    public void setMovPagamentoVO(MovPagamentoVO movPagamentoVO) {
        this.movPagamentoVO = movPagamentoVO;
    }

    public CartaoCreditoVO getCartaoCreditoVO() {
        if (cartaoCreditoVO == null) {
            cartaoCreditoVO = new CartaoCreditoVO();
        }
        return cartaoCreditoVO;
    }

    public void setCartaoCreditoVO(CartaoCreditoVO cartaoCreditoVO) {
        this.cartaoCreditoVO = cartaoCreditoVO;
    }

    public ReciboPagamentoVO getReciboPagamentoVO() {
        if (reciboPagamentoVO == null) {
            reciboPagamentoVO = new ReciboPagamentoVO();
        }
        return reciboPagamentoVO;
    }

    public void setReciboPagamentoVO(ReciboPagamentoVO reciboPagamentoVO) {
        this.reciboPagamentoVO = reciboPagamentoVO;
    }

    public MovContaVO getMovContaVO() {
        if (movContaVO == null) {
            movContaVO = new MovContaVO();
        }
        return movContaVO;
    }

    public void setMovContaVO(MovContaVO movContaVO) {
        this.movContaVO = movContaVO;
    }

    public String getRetorno() {
        if (retorno == null) {
            retorno = "";
        }
        return retorno;
    }

    public void setRetorno(String retorno) {
        this.retorno = retorno;
    }

    public Date getDataEmissaoPrevista() {
        return dataEmissaoPrevista;
    }

    public void setDataEmissaoPrevista(Date dataEmissaoPrevista) {
        this.dataEmissaoPrevista = dataEmissaoPrevista;
    }

    public boolean isSucesso() {
        return sucesso;
    }

    public void setSucesso(boolean sucesso) {
        this.sucesso = sucesso;
    }

    public Integer getCodigo() {
        if (codigo == null) {
            codigo = 0;
        }
        return codigo;
    }

    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public List<NFSeEmitidaVO> getListaNFSeEmitidaVO() {
        if (listaNFSeEmitidaVO == null) {
            listaNFSeEmitidaVO = new ArrayList<NFSeEmitidaVO>();
        }
        return listaNFSeEmitidaVO;
    }

    public void setListaNFSeEmitidaVO(List<NFSeEmitidaVO> listaNFSeEmitidaVO) {
        this.listaNFSeEmitidaVO = listaNFSeEmitidaVO;
    }

    public String getNumeroNotaManual() {
        if (numeroNotaManual == null) {
            numeroNotaManual = "";
        }
        return numeroNotaManual;
    }

    public void setNumeroNotaManual(String numeroNotaManual) {
        this.numeroNotaManual = numeroNotaManual;
    }

    public List<NotaFiscalConsumidorEletronicaVO> getListaNotaFiscalConsumidorVO() {
        if (listaNotaFiscalConsumidorVO == null) {
            listaNotaFiscalConsumidorVO = new ArrayList<NotaFiscalConsumidorEletronicaVO>();
        }
        return listaNotaFiscalConsumidorVO;
    }

    public void setListaNotaFiscalConsumidorVO(List<NotaFiscalConsumidorEletronicaVO> listaNotaFiscalConsumidorVO) {
        this.listaNotaFiscalConsumidorVO = listaNotaFiscalConsumidorVO;
    }

    public SituacaoNotaFiscalEnum getSituacaoNotaFiscal() {
        if (situacaoNotaFiscal == null) {
            situacaoNotaFiscal = SituacaoNotaFiscalEnum.ERRO_GERACAO;
        }
        return situacaoNotaFiscal;
    }

    public void setSituacaoNotaFiscal(SituacaoNotaFiscalEnum situacaoNotaFiscal) {
        this.situacaoNotaFiscal = situacaoNotaFiscal;
    }

    public UsuarioVO getUsuarioVO() {
        if (usuarioVO == null) {
            usuarioVO = new UsuarioVO();
        }
        return usuarioVO;
    }

    public void setUsuarioVO(UsuarioVO usuarioVO) {
        this.usuarioVO = usuarioVO;
    }

    public String getChave() {
        if (chave == null) {
            chave = "";
        }
        return chave;
    }

    public void setChave(String chave) {
        this.chave = chave;
    }

    public Integer getNotaFiscalAnterior() {
        return notaFiscalAnterior;
    }

    public void setNotaFiscalAnterior(Integer notaFiscalAnterior) {
        this.notaFiscalAnterior = notaFiscalAnterior;
    }

    public Integer getNotaFiscalNova() {
        return notaFiscalNova;
    }

    public void setNotaFiscalNova(Integer notaFiscalNova) {
        this.notaFiscalNova = notaFiscalNova;
    }
}

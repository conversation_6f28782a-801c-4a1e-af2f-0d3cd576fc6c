package negocio.comuns.notaFiscal;

public enum OperacaoNotaFiscalEnum {

    NENHUM(0, ""),
    CANCELAR(1, "CANCELAR"),
    INUTILIZAR(2, "INUTILIZAR");

    private Integer codigo;
    private String descricao;

    private OperacaoNotaFiscalEnum(Integer codigo, String descricao) {
        this.codigo = codigo;
        this.descricao = descricao;
    }

    public Integer getCodigo() {
        return codigo;
    }

    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public String getDescricao() {
        return descricao;
    }

    public void setDescricao(String descricao) {
        this.descricao = descricao;
    }

    public static OperacaoNotaFiscalEnum obterPorCodigo(Integer codigo) {
        for (OperacaoNotaFiscalEnum obj : OperacaoNotaFiscalEnum.values()) {
            if (obj.getCodigo().equals(codigo)) {
                return obj;
            }
        }
        return NENHUM;
    }
}
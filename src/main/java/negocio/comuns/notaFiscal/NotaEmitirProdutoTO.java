package negocio.comuns.notaFiscal;

import negocio.comuns.arquitetura.SuperTO;
import negocio.comuns.contrato.MovProdutoVO;

public class NotaEmitirProdutoTO extends SuperTO {

    private MovProdutoVO movProdutoVO;
    private Double valorUnitario;
    private Double descontos;
    private Integer quantidade;
    private String descricao;
    private String unidadeMedida;




    public String getDescricao() {
        if (descricao == null) {
            descricao = "";
        }
        return descricao;
    }

    public void setDescricao(String descricao) {
        this.descricao = descricao;
    }

    public MovProdutoVO getMovProdutoVO() {
        if (movProdutoVO == null) {
            movProdutoVO = new MovProdutoVO();
        }
        return movProdutoVO;
    }

    public void setMovProdutoVO(MovProdutoVO movProdutoVO) {
        this.movProdutoVO = movProdutoVO;
    }

    public Integer getQuantidade() {
        if (quantidade == null) {
            quantidade = 0;
        }
        return quantidade;
    }

    public void setQuantidade(Integer quantidade) {
        this.quantidade = quantidade;
    }

    public Double getValorUnitario() {
        if (valorUnitario == null) {
            valorUnitario = 0.0;
        }
        return valorUnitario;
    }

    public void setValorUnitario(Double valorUnitario) {
        this.valorUnitario = valorUnitario;
    }

    public Double getDescontos() {
        if (descontos == null) {
            descontos = 0.0;
        }
        return descontos;
    }

    public void setDescontos(Double descontos) {
        this.descontos = descontos;
    }

    public String getUnidadeMedida() {
        if (unidadeMedida == null) {
            unidadeMedida = "";
        }
        return unidadeMedida;
    }

    public void setUnidadeMedida(String unidadeMedida) {
        this.unidadeMedida = unidadeMedida;
    }

}

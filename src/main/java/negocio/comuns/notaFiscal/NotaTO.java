package negocio.comuns.notaFiscal;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.databind.ObjectMapper;
import negocio.comuns.arquitetura.SuperTO;
import negocio.comuns.arquitetura.UsuarioVO;
import negocio.comuns.basico.ConfiguracaoNotaFiscalVO;
import negocio.comuns.basico.EmpresaVO;
import negocio.comuns.basico.PessoaVO;
import negocio.comuns.financeiro.NFSeEmitidaVO;
import negocio.comuns.financeiro.NotaFiscalConsumidorEletronicaVO;
import negocio.comuns.utilitarias.Uteis;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

public class NotaTO extends SuperTO {

    private String chave;
    @JsonIgnore
    private ConfiguracaoNotaFiscalVO configuracaoNotaFiscalVO;
    @JsonIgnore
    private EmpresaVO empresaVO;
    @JsonIgnore
    private UsuarioVO usuarioVO;
    @JsonIgnore
    private PessoaVO pessoaVO;
    private String nomePagador;
    @JsonIgnore
    private NotaFiscalConsumidorEletronicaVO notaFiscalConsumidorEletronicaVO;
    @JsonIgnore
    private NFSeEmitidaVO nfSeEmitidaVO;
    @JsonIgnore
    private NotaFiscalVO notaFiscalVO;
    private String numeroNotaManual;
    private Integer sequencialfamilia;

    //INFORMAÇÕES SOBRE A NOTA
    private TipoNotaFiscalEnum tipoNotaFiscal;
    private Date notaDtEmissao;
    private Date notaDtCompetencia;
    private Double notaValor;
    private Double notaAliquotaISS;
    private Double notaAliquotaPIS;
    private Double notaAliquotaCOFINS;
    private Double notaAliquotaIRRF;
    private Double notaValorISS;
    private Double notaValorPIS;
    private Double notaValorCOFINS;
    private Double notaValorIRRF;
    private Double notaValorCSLL;
    private Double notaPercentualAproximadoTributos;
    private Double notaValorAproximadoTributos;
    private String notaAmbienteEmissao;
    private String notaCFOP;
    private String notaCEST;
    private String notaCNAE;
    private String notaSerie;
    private String notaNumero;
    private String notaItemListaServico;
    private String notaCodigoTributacaoMunicipio;
    private String notaNaturezaOperacao;
    private String notaDescricao;
    private String notaObservacao;
    private boolean notaIssRetido = false;
    private Integer notaExigibilidadeISS;
    private String notaInformacaoFisco;
    private String notaIDReferencia;
    private boolean notaEnviarEmail = false;
    private List<NotaProdutoTO> notaProdutos;
    private List<NotaPagamentoTO> notaPagamentos;
    private String descricaoServicoMunicipio;


    //INFORMAÇÕES SOBRE O CLIENTE - TOMADOR DA NOTA
    private String cliNomeAluno; //nome do aluno somente para identificacao
    private String cliRazaoSocial; //nome que será emitida a nota
    private String cliCPFCNPJ; //CPF ou CNPJ do
    private String cliInscMunicipal;
    private String cliInscEstadual;
    private String cliCFDF;
    private String cliEmail;
    private String cliTelefone;
    private String cliEndLogradouro;
    private String cliEndNumero;
    private String cliEndBairro;
    private String cliEndComplemento;
    private String cliEndCEP;
    private String cliEndPais;
    private String cliEndCidade;
    private String cliEndCidadeIBGE;
    private String cliEndUFEstado;
    private String cliEndUFEstadoIBGE;


    //INFORMAÇÕES SOBRE A EMPRESA
    private String empRazaoSocial;
    private String empNomeFantasia;
    private String empCNPJ;
    private String empInscMunicipal;
    private String empInscEstadual;
    private String empEmail;
    private String empTelefone;
    private String empEndLogradouro;
    private String empEndNumero;
    private String empEndBairro;
    private String empEndComplemento;
    private String empEndCEP;
    private String empEndPais;
    private String empEndCidade;
    private String empEndCidadeIBGE;
    private String empEndUFEstado;
    private String empEndUFEstadoIBGE;

    private boolean emitirDuplicata = false;


    public TipoNotaFiscalEnum getTipoNotaFiscal() {
        return tipoNotaFiscal;
    }

    public void setTipoNotaFiscal(TipoNotaFiscalEnum tipoNotaFiscal) {
        this.tipoNotaFiscal = tipoNotaFiscal;
    }

    public Date getNotaDtEmissao() {
        return notaDtEmissao;
    }

    public void setNotaDtEmissao(Date notaDtEmissao) {
        this.notaDtEmissao = notaDtEmissao;
    }

    public Date getNotaDtCompetencia() {
        return notaDtCompetencia;
    }

    public void setNotaDtCompetencia(Date notaDtCompetencia) {
        this.notaDtCompetencia = notaDtCompetencia;
    }

    public Double getNotaValor() {
        if (notaValor == null) {
            notaValor = 0.0;
        }
        return notaValor;
    }

    public void setNotaValor(Double notaValor) {
        this.notaValor = notaValor;
    }

    public Double getNotaAliquotaISS() {
        if (notaAliquotaISS == null) {
            notaAliquotaISS = 0.0;
        }
        return notaAliquotaISS;
    }

    public void setNotaAliquotaISS(Double notaAliquotaISS) {
        this.notaAliquotaISS = notaAliquotaISS;
    }

    public Double getNotaAliquotaPIS() {
        if (notaAliquotaPIS == null) {
            notaAliquotaPIS = 0.0;
        }
        return notaAliquotaPIS;
    }

    public void setNotaAliquotaPIS(Double notaAliquotaPIS) {
        this.notaAliquotaPIS = notaAliquotaPIS;
    }

    public Double getNotaAliquotaCOFINS() {
        if (notaAliquotaCOFINS == null) {
            notaAliquotaCOFINS = 0.0;
        }
        return notaAliquotaCOFINS;
    }

    public void setNotaAliquotaCOFINS(Double notaAliquotaCOFINS) {
        this.notaAliquotaCOFINS = notaAliquotaCOFINS;
    }

    public Double getNotaValorISS() {
        if (notaValorISS == null) {
            notaValorISS = 0.0;
        }
        return notaValorISS;
    }

    public void setNotaValorISS(Double notaValorISS) {
        this.notaValorISS = notaValorISS;
    }

    public Double getNotaValorPIS() {
        if (notaValorPIS == null) {
            notaValorPIS = 0.0;
        }
        return notaValorPIS;
    }

    public void setNotaValorPIS(Double notaValorPIS) {
        this.notaValorPIS = notaValorPIS;
    }

    public Double getNotaValorCSLL() {
        if(notaValorCSLL == null) {
            notaValorCSLL = 0.0;
        }
        return notaValorCSLL;
    }

    public void setNotaValorCSLL(Double notaValorCSLL) {
        this.notaValorCSLL = notaValorCSLL;
    }

    public Double getNotaValorCOFINS() {
        if (notaValorCOFINS ==null) {
            notaValorCOFINS = 0.0;
        }
        return notaValorCOFINS;
    }

    public void setNotaValorCOFINS(Double notaValorCOFINS) {
        this.notaValorCOFINS = notaValorCOFINS;
    }

    public Double getNotaValorIRRF() {
        if (notaValorIRRF == null) {
            notaValorIRRF = 0.0;
        }
        return notaValorIRRF;
    }

    public void setNotaValorIRRF(Double notaValorIRRF) {
        this.notaValorIRRF = notaValorIRRF;
    }

    public String getNotaCFOP() {
        if (notaCFOP == null) {
            notaCFOP = "";
        }
        return notaCFOP;
    }

    public void setNotaCFOP(String notaCFOP) {
        this.notaCFOP = notaCFOP;
    }

    public String getNotaCEST() {
        if (notaCEST == null) {
            notaCEST = "";
        }
        return notaCEST;
    }

    public void setNotaCEST(String notaCEST) {
        this.notaCEST = notaCEST;
    }

    public String getNotaCNAE() {
        if (notaCNAE == null) {
            notaCNAE = "";
        }
        return notaCNAE;
    }

    public void setNotaCNAE(String notaCNAE) {
        this.notaCNAE = notaCNAE;
    }

    public String getNotaSerie() {
        if (notaSerie == null) {
            notaSerie = "";
        }
        return notaSerie;
    }

    public void setNotaSerie(String notaSerie) {
        this.notaSerie = notaSerie;
    }

    public String getNotaItemListaServico() {
        if (notaItemListaServico == null) {
            notaItemListaServico = "";
        }
        return notaItemListaServico;
    }

    public void setNotaItemListaServico(String notaItemListaServico) {
        this.notaItemListaServico = notaItemListaServico;
    }

    public String getNotaCodigoTributacaoMunicipio() {
        if (notaCodigoTributacaoMunicipio == null) {
            notaCodigoTributacaoMunicipio = "";
        }
        return notaCodigoTributacaoMunicipio;
    }

    public void setNotaCodigoTributacaoMunicipio(String notaCodigoTributacaoMunicipio) {
        this.notaCodigoTributacaoMunicipio = notaCodigoTributacaoMunicipio;
    }

    public String getNotaNaturezaOperacao() {
        if (notaNaturezaOperacao == null) {
            notaNaturezaOperacao = "";
        }
        return notaNaturezaOperacao;
    }

    public void setNotaNaturezaOperacao(String notaNaturezaOperacao) {
        this.notaNaturezaOperacao = notaNaturezaOperacao;
    }

    public String getNotaDescricao() {
        if (notaDescricao == null) {
            notaDescricao = "";
        }
        return notaDescricao;
    }

    public void setNotaDescricao(String notaDescricao) {
        this.notaDescricao = notaDescricao;
    }

    public String getNotaObservacao() {
        if (notaObservacao == null) {
            notaObservacao = "";
        }
        return notaObservacao;
    }

    public void setNotaObservacao(String notaObservacao) {
        this.notaObservacao = notaObservacao;
    }

    public boolean isNotaIssRetido() {
        return notaIssRetido;
    }

    public void setNotaIssRetido(boolean notaIssRetido) {
        this.notaIssRetido = notaIssRetido;
    }

    public Integer getNotaExigibilidadeISS() {
        if (notaExigibilidadeISS == null) {
            notaExigibilidadeISS = 0;
        }
        return notaExigibilidadeISS;
    }

    public void setNotaExigibilidadeISS(Integer notaExigibilidadeISS) {
        this.notaExigibilidadeISS = notaExigibilidadeISS;
    }

    public String getNotaInformacaoFisco() {
        if (notaInformacaoFisco == null) {
            notaInformacaoFisco = "";
        }
        return notaInformacaoFisco;
    }

    public void setNotaInformacaoFisco(String notaInformacaoFisco) {
        this.notaInformacaoFisco = notaInformacaoFisco;
    }

    public String getNotaIDReferencia() {
        if (notaIDReferencia == null) {
            notaIDReferencia = "";
        }
        return notaIDReferencia;
    }

    public void setNotaIDReferencia(String notaIDReferencia) {
        this.notaIDReferencia = notaIDReferencia;
    }

    public boolean isNotaEnviarEmail() {
        return notaEnviarEmail;
    }

    public void setNotaEnviarEmail(boolean notaEnviarEmail) {
        this.notaEnviarEmail = notaEnviarEmail;
    }

    public List<NotaProdutoTO> getNotaProdutos() {
        if (notaProdutos == null) {
            notaProdutos = new ArrayList<NotaProdutoTO>();
        }
        return notaProdutos;
    }

    public void setNotaProdutos(List<NotaProdutoTO> notaProdutos) {
        this.notaProdutos = notaProdutos;
    }

    public List<NotaPagamentoTO> getNotaPagamentos() {
        if (notaPagamentos == null) {
            notaPagamentos = new ArrayList<NotaPagamentoTO>();
        }
        return notaPagamentos;
    }

    public void setNotaPagamentos(List<NotaPagamentoTO> notaPagamentos) {
        this.notaPagamentos = notaPagamentos;
    }

    public String getCliNomeAluno() {
        if (cliNomeAluno == null) {
            cliNomeAluno = "";
        }
        return cliNomeAluno;
    }

    public void setCliNomeAluno(String cliNomeAluno) {
        this.cliNomeAluno = cliNomeAluno;
    }

    public String getCliRazaoSocial() {
        if (cliRazaoSocial == null) {
            cliRazaoSocial = "";
        }
        return cliRazaoSocial;
    }

    public void setCliRazaoSocial(String cliRazaoSocial) {
        this.cliRazaoSocial = cliRazaoSocial;
    }

    public String getCliCPFCNPJ() {
        if (cliCPFCNPJ == null) {
            cliCPFCNPJ = "";
        }
        return cliCPFCNPJ;
    }

    public void setCliCPFCNPJ(String cliCPFCNPJ) {
        this.cliCPFCNPJ = cliCPFCNPJ;
    }

    public String getCliInscMunicipal() {
        if (cliInscMunicipal == null) {
            cliInscMunicipal = "";
        }
        return cliInscMunicipal;
    }

    public void setCliInscMunicipal(String cliInscMunicipal) {
        this.cliInscMunicipal = cliInscMunicipal;
    }

    public String getCliInscEstadual() {
        if (cliInscEstadual == null) {
            cliInscEstadual = "";
        }
        return cliInscEstadual;
    }

    public void setCliInscEstadual(String cliInscEstadual) {
        this.cliInscEstadual = cliInscEstadual;
    }

    public String getCliCFDF() {
        return cliCFDF;
    }

    public void setCliCFDF(String cliCFDF) {
        this.cliCFDF = cliCFDF;
    }

    public String getCliEmail() {
        return cliEmail;
    }

    public void setCliEmail(String cliEmail) {
        this.cliEmail = cliEmail;
    }

    public String getCliTelefone() {
        return cliTelefone;
    }

    public void setCliTelefone(String cliTelefone) {
        this.cliTelefone = cliTelefone;
    }

    public String getCliEndLogradouro() {
        return cliEndLogradouro;
    }

    public void setCliEndLogradouro(String cliEndLogradouro) {
        this.cliEndLogradouro = cliEndLogradouro;
    }

    public String getCliEndNumero() {
        return cliEndNumero;
    }

    public void setCliEndNumero(String cliEndNumero) {
        this.cliEndNumero = cliEndNumero;
    }

    public String getCliEndBairro() {
        return cliEndBairro;
    }

    public void setCliEndBairro(String cliEndBairro) {
        this.cliEndBairro = cliEndBairro;
    }

    public String getCliEndComplemento() {
        return cliEndComplemento;
    }

    public void setCliEndComplemento(String cliEndComplemento) {
        this.cliEndComplemento = cliEndComplemento;
    }

    public String getCliEndCEP() {
        return cliEndCEP;
    }

    public void setCliEndCEP(String cliEndCEP) {
        this.cliEndCEP = cliEndCEP;
    }

    public String getCliEndCidade() {
        return cliEndCidade;
    }

    public void setCliEndCidade(String cliEndCidade) {
        this.cliEndCidade = cliEndCidade;
    }

    public String getCliEndCidadeIBGE() {
        return cliEndCidadeIBGE;
    }

    public void setCliEndCidadeIBGE(String cliEndCidadeIBGE) {
        this.cliEndCidadeIBGE = cliEndCidadeIBGE;
    }

    public String getCliEndUFEstado() {
        return cliEndUFEstado;
    }

    public void setCliEndUFEstado(String cliEndUFEstado) {
        this.cliEndUFEstado = cliEndUFEstado;
    }

    public String getCliEndUFEstadoIBGE() {
        return cliEndUFEstadoIBGE;
    }

    public void setCliEndUFEstadoIBGE(String cliEndUFEstadoIBGE) {
        this.cliEndUFEstadoIBGE = cliEndUFEstadoIBGE;
    }

    public String getEmpRazaoSocial() {
        return empRazaoSocial;
    }

    public void setEmpRazaoSocial(String empRazaoSocial) {
        this.empRazaoSocial = empRazaoSocial;
    }

    public String getEmpNomeFantasia() {
        return empNomeFantasia;
    }

    public void setEmpNomeFantasia(String empNomeFantasia) {
        this.empNomeFantasia = empNomeFantasia;
    }

    public String getEmpCNPJ() {
        return empCNPJ;
    }

    public void setEmpCNPJ(String empCNPJ) {
        this.empCNPJ = empCNPJ;
    }

    public String getEmpInscMunicipal() {
        return empInscMunicipal;
    }

    public void setEmpInscMunicipal(String empInscMunicipal) {
        this.empInscMunicipal = empInscMunicipal;
    }

    public String getEmpInscEstadual() {
        return empInscEstadual;
    }

    public void setEmpInscEstadual(String empInscEstadual) {
        this.empInscEstadual = empInscEstadual;
    }

    public String getEmpEmail() {
        return empEmail;
    }

    public void setEmpEmail(String empEmail) {
        this.empEmail = empEmail;
    }

    public String getEmpTelefone() {
        return empTelefone;
    }

    public void setEmpTelefone(String empTelefone) {
        this.empTelefone = empTelefone;
    }

    public String getEmpEndLogradouro() {
        return empEndLogradouro;
    }

    public void setEmpEndLogradouro(String empEndLogradouro) {
        this.empEndLogradouro = empEndLogradouro;
    }

    public String getEmpEndNumero() {
        return empEndNumero;
    }

    public void setEmpEndNumero(String empEndNumero) {
        this.empEndNumero = empEndNumero;
    }

    public String getEmpEndBairro() {
        return empEndBairro;
    }

    public void setEmpEndBairro(String empEndBairro) {
        this.empEndBairro = empEndBairro;
    }

    public String getEmpEndComplemento() {
        return empEndComplemento;
    }

    public void setEmpEndComplemento(String empEndComplemento) {
        this.empEndComplemento = empEndComplemento;
    }

    public String getEmpEndCEP() {
        return empEndCEP;
    }

    public void setEmpEndCEP(String empEndCEP) {
        this.empEndCEP = empEndCEP;
    }

    public String getEmpEndCidade() {
        return empEndCidade;
    }

    public void setEmpEndCidade(String empEndCidade) {
        this.empEndCidade = empEndCidade;
    }

    public String getEmpEndCidadeIBGE() {
        return empEndCidadeIBGE;
    }

    public void setEmpEndCidadeIBGE(String empEndCidadeIBGE) {
        this.empEndCidadeIBGE = empEndCidadeIBGE;
    }

    public String getEmpEndUFEstado() {
        return empEndUFEstado;
    }

    public void setEmpEndUFEstado(String empEndUFEstado) {
        this.empEndUFEstado = empEndUFEstado;
    }

    public String getEmpEndUFEstadoIBGE() {
        return empEndUFEstadoIBGE;
    }

    public void setEmpEndUFEstadoIBGE(String empEndUFEstadoIBGE) {
        this.empEndUFEstadoIBGE = empEndUFEstadoIBGE;
    }

    public Double getNotaAliquotaIRRF() {
        if (notaAliquotaIRRF == null) {
            notaAliquotaIRRF = 0.0;
        }
        return notaAliquotaIRRF;
    }

    public void setNotaAliquotaIRRF(Double notaAliquotaIRRF) {
        this.notaAliquotaIRRF = notaAliquotaIRRF;
    }

    public String getCliEndPais() {
        return cliEndPais;
    }

    public void setCliEndPais(String cliEndPais) {
        this.cliEndPais = cliEndPais;
    }

    public String getEmpEndPais() {
        return empEndPais;
    }

    public void setEmpEndPais(String empEndPais) {
        this.empEndPais = empEndPais;
    }

    @JsonIgnore
    public ConfiguracaoNotaFiscalVO getConfiguracaoNotaFiscalVO() {
        if (configuracaoNotaFiscalVO == null) {
            configuracaoNotaFiscalVO = new ConfiguracaoNotaFiscalVO();
        }
        return configuracaoNotaFiscalVO;
    }

    @JsonIgnore
    public void setConfiguracaoNotaFiscalVO(ConfiguracaoNotaFiscalVO configuracaoNotaFiscalVO) {
        this.configuracaoNotaFiscalVO = configuracaoNotaFiscalVO;
    }

    public String getNumeroNotaManual() {
        if (numeroNotaManual == null) {
            numeroNotaManual = "";
        }
        return numeroNotaManual;
    }

    public void setNumeroNotaManual(String numeroNotaManual) {
        this.numeroNotaManual = numeroNotaManual;
    }

    @JsonIgnore
    public NotaFiscalConsumidorEletronicaVO getNotaFiscalConsumidorEletronicaVO() {
        if (notaFiscalConsumidorEletronicaVO == null) {
            notaFiscalConsumidorEletronicaVO = new NotaFiscalConsumidorEletronicaVO();
        }
        return notaFiscalConsumidorEletronicaVO;
    }

    @JsonIgnore
    public void setNotaFiscalConsumidorEletronicaVO(NotaFiscalConsumidorEletronicaVO notaFiscalConsumidorEletronicaVO) {
        this.notaFiscalConsumidorEletronicaVO = notaFiscalConsumidorEletronicaVO;
    }

    @JsonIgnore
    public NFSeEmitidaVO getNfSeEmitidaVO() {
        if (nfSeEmitidaVO == null) {
            nfSeEmitidaVO = new NFSeEmitidaVO();
        }
        return nfSeEmitidaVO;
    }

    @JsonIgnore
    public void setNfSeEmitidaVO(NFSeEmitidaVO nfSeEmitidaVO) {
        this.nfSeEmitidaVO = nfSeEmitidaVO;
    }

    @JsonIgnore
    public EmpresaVO getEmpresaVO() {
        if (empresaVO == null) {
            empresaVO = new EmpresaVO();
        }
        return empresaVO;
    }

    @JsonIgnore
    public void setEmpresaVO(EmpresaVO empresaVO) {
        this.empresaVO = empresaVO;
    }

    @JsonIgnore
    public PessoaVO getPessoaVO() {
        if (pessoaVO == null) {
            pessoaVO = new PessoaVO();
        }
        return pessoaVO;
    }

    @JsonIgnore
    public void setPessoaVO(PessoaVO pessoaVO) {
        this.pessoaVO = pessoaVO;
    }

    //fisica ou jurídica
    public String getCliTipo() {
        String cpfCNPJ = Uteis.tirarCaracteres(this.getCliCPFCNPJ(), true);
        if (cpfCNPJ.length() > 11) {
            return "J";
        } else {
            return "F";
        }
    }

    public String getNotaNumero() {
        if (notaNumero == null) {
            notaNumero = "";
        }
        return notaNumero;
    }

    public void setNotaNumero(String notaNumero) {
        this.notaNumero = notaNumero;
    }

    public String getNomePagador() {
        if (nomePagador == null) {
            nomePagador = "";
        }
        return nomePagador;
    }

    public void setNomePagador(String nomePagador) {
        this.nomePagador = nomePagador;
    }

    public String getChave() {
        if (chave == null) {
            chave = "";
        }
        return chave;
    }

    public void setChave(String chave) {
        this.chave = chave;
    }

    @JsonIgnore
    public UsuarioVO getUsuarioVO() {
        if (usuarioVO == null) {
            usuarioVO = new UsuarioVO();
        }
        return usuarioVO;
    }

    @JsonIgnore
    public void setUsuarioVO(UsuarioVO usuarioVO) {
        this.usuarioVO = usuarioVO;
    }

    @Override
    public String toString() {
        try {
            ObjectMapper mapper = new ObjectMapper();
            return mapper.writeValueAsString(this);
        } catch (Exception e) {
            return "";
        }
    }

    public String getNotaAmbienteEmissao() {
        if (notaAmbienteEmissao == null) {
            notaAmbienteEmissao = "";
        }
        return notaAmbienteEmissao;
    }

    public void setNotaAmbienteEmissao(String notaAmbienteEmissao) {
        this.notaAmbienteEmissao = notaAmbienteEmissao;
    }

    @JsonIgnore
    public NotaFiscalVO getNotaFiscalVO() {
        if (notaFiscalVO == null) {
            notaFiscalVO = new NotaFiscalVO();
        }
        return notaFiscalVO;
    }

    @JsonIgnore
    public void setNotaFiscalVO(NotaFiscalVO notaFiscalVO) {
        this.notaFiscalVO = notaFiscalVO;
    }

    public Integer getSequencialfamilia() {
        return sequencialfamilia;
    }

    public void setSequencialfamilia(Integer sequencialfamilia) {
        this.sequencialfamilia = sequencialfamilia;
    }

    public boolean isEmitirDuplicata() {
        return emitirDuplicata;
    }

    public void setEmitirDuplicata(boolean emitirDuplicata) {
        this.emitirDuplicata = emitirDuplicata;
    }

    public Double getNotaPercentualAproximadoTributos() {
        if (notaPercentualAproximadoTributos == null) {
            notaPercentualAproximadoTributos = 0.0;
        }
        return notaPercentualAproximadoTributos;
    }

    public void setNotaPercentualAproximadoTributos(Double notaPercentualAproximadoTributos) {
        this.notaPercentualAproximadoTributos = notaPercentualAproximadoTributos;
    }

    public Double getNotaValorAproximadoTributos() {
        if (notaValorAproximadoTributos == null) {
            notaValorAproximadoTributos = 0.0;
        }
        return notaValorAproximadoTributos;
    }

    public void setNotaValorAproximadoTributos(Double notaValorAproximadoTributos) {
        this.notaValorAproximadoTributos = notaValorAproximadoTributos;
    }

    public String getDescricaoServicoMunicipio() {
        if(descricaoServicoMunicipio == null){
            descricaoServicoMunicipio = "";
        }
        return descricaoServicoMunicipio;
    }

    public void setDescricaoServicoMunicipio(String descricaoServicoMunicipio) {
        this.descricaoServicoMunicipio = descricaoServicoMunicipio;
    }
}

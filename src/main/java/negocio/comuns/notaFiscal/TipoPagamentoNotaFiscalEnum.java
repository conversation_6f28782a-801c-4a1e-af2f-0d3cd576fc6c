package negocio.comuns.notaFiscal;

import br.com.pactosolucoes.comuns.util.StringUtilities;

public enum TipoPagamentoNotaFiscalEnum {

    NENHUM(0, "",""),
    DINHEIRO(1, "AV","DINHEIRO"),
    CARTAO_CREDITO(2,"CA", "CARTÃO DE CRÉDITO"),
    CARTAO_DEBIDO(3, "CD", "CARTÃO DE DÉBITO"),
    CHEQUE(4, "CH", "CHEQUE"),
    BOLETO(5, "BB", "BOLETO"),
    CONTA_CORRENTE(6, "CC", "CONTA CORRENTE"),
    CONVENIO(7, "CO", "CONVÊNIO"),
    PAGAMENTO_DIGITAL(8, "PD", "PAGAMENTO DIGITAL"),
    LOTE(9, "LO", "LOTE"),
    ;

    private Integer codigo;
    private String sigla;
    private String descricao;

    private TipoPagamentoNotaFiscalEnum(Integer codigo, String sigla, String descricao) {
        this.codigo = codigo;
        this.sigla = sigla;
        this.descricao = descricao;
    }

    public Integer getCodigo() {
        return codigo;
    }

    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public String getSigla() {
        return sigla;
    }

    public void setSigla(String sigla) {
        this.sigla = sigla;
    }

    public String getDescricao() {
        return descricao;
    }

    public void setDescricao(String descricao) {
        this.descricao = descricao;
    }

    public String getDescricaoSemAcento() {
        return StringUtilities.doRemoverAcentos(getDescricao());
    }

    public static TipoPagamentoNotaFiscalEnum obterPorSigla(String sigla) {
        for (TipoPagamentoNotaFiscalEnum nota : TipoPagamentoNotaFiscalEnum.values()) {
            if (nota.getSigla().equals(sigla)) {
                return nota;
            }
        }
        return NENHUM;
    }
}

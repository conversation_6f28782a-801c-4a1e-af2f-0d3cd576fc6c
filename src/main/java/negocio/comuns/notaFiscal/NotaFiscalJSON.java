/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package negocio.comuns.notaFiscal;

import br.com.pactosolucoes.comuns.json.SuperJSON;

/**
 * <AUTHOR>
 */
public class NotaFiscalJSON extends SuperJSON {

    private Integer codigo;
    private Integer pessoa;
    private String tipoNota;
    private String statusNota;
    private String razaoSocial;
    private String cpfCnpj;
    private String numeroNota;
    private Double valor;
    private String urlPDF;
    private String urlXML;
    private String dataRegistro;
    private String dataEmissao;
    private String dataAutorizacao;

    public NotaFiscalJSON(NotaFiscalVO obj) {
        this.codigo = obj.getCodigo();
        this.pessoa = obj.getPessoaVO().getCodigo();
        this.tipoNota = obj.getTipo().getDescricao();
        this.statusNota = obj.getStatusNota();
        this.razaoSocial = obj.getRazaoSocial();
        this.cpfCnpj = obj.getCpfCnpjApresentar();
        this.numeroNota = obj.getNumeroNota();
        this.valor = obj.getValor();
        this.urlPDF = obj.getLinkPDF();
        this.urlXML = obj.getLinkXML();
        this.dataRegistro = obj.getDataRegistroApresentar();
        this.dataEmissao = obj.getDataEmissaoApresentar();
        this.dataAutorizacao = obj.getDataAutorizacaoApresentar();
    }

    public Integer getCodigo() {
        return codigo;
    }

    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public Integer getPessoa() {
        return pessoa;
    }

    public void setPessoa(Integer pessoa) {
        this.pessoa = pessoa;
    }

    public String getTipoNota() {
        return tipoNota;
    }

    public void setTipoNota(String tipoNota) {
        this.tipoNota = tipoNota;
    }

    public String getStatusNota() {
        return statusNota;
    }

    public void setStatusNota(String statusNota) {
        this.statusNota = statusNota;
    }

    public String getRazaoSocial() {
        return razaoSocial;
    }

    public void setRazaoSocial(String razaoSocial) {
        this.razaoSocial = razaoSocial;
    }

    public String getCpfCnpj() {
        return cpfCnpj;
    }

    public void setCpfCnpj(String cpfCnpj) {
        this.cpfCnpj = cpfCnpj;
    }

    public String getNumeroNota() {
        return numeroNota;
    }

    public void setNumeroNota(String numeroNota) {
        this.numeroNota = numeroNota;
    }

    public Double getValor() {
        return valor;
    }

    public void setValor(Double valor) {
        this.valor = valor;
    }

    public String getUrlPDF() {
        return urlPDF;
    }

    public void setUrlPDF(String urlPDF) {
        this.urlPDF = urlPDF;
    }

    public String getUrlXML() {
        return urlXML;
    }

    public void setUrlXML(String urlXML) {
        this.urlXML = urlXML;
    }

    public String getDataRegistro() {
        return dataRegistro;
    }

    public void setDataRegistro(String dataRegistro) {
        this.dataRegistro = dataRegistro;
    }

    public String getDataEmissao() {
        return dataEmissao;
    }

    public void setDataEmissao(String dataEmissao) {
        this.dataEmissao = dataEmissao;
    }

    public String getDataAutorizacao() {
        return dataAutorizacao;
    }

    public void setDataAutorizacao(String dataAutorizacao) {
        this.dataAutorizacao = dataAutorizacao;
    }
}

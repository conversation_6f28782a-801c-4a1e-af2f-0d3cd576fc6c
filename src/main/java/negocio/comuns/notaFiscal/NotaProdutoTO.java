package negocio.comuns.notaFiscal;

import br.com.pactosolucoes.comuns.util.Formatador;
import com.fasterxml.jackson.annotation.JsonIgnore;
import negocio.comuns.arquitetura.SuperTO;
import negocio.comuns.plano.ProdutoVO;

public class NotaProdutoTO extends SuperTO {

    private Double valorUnitario;
    private Double valorDesconto;
    private Double outrasDespesas;
    private Integer quantidade;
    private String descricao;
    private String ncm;
    private String cfop;
    private String cest;
    private String unidadeMedida;
    @JsonIgnore
    private ProdutoVO produtoVO;
    private boolean enviarPercentualImposto = true;
    private Double percentualFederal;
    private Double percentualEstadual;
    private Double percentualMunicipal;
    private boolean isentoPIS = false;
    private boolean isentoCOFINS = false;
    private boolean isentoICMS = false;


    public Double getValorUnitario() {
        if (valorUnitario == null) {
            valorUnitario = 0.0;
        }
        return valorUnitario;
    }

    public void setValorUnitario(Double valorUnitario) {
        this.valorUnitario = valorUnitario;
    }

    public Integer getQuantidade() {
        if (quantidade == null) {
            quantidade = 0;
        }
        return quantidade;
    }

    public void setQuantidade(Integer quantidade) {
        this.quantidade = quantidade;
    }

    public String getDescricao() {
        if (descricao == null) {
            descricao = "";
        }
        return descricao;
    }

    public void setDescricao(String descricao) {
        this.descricao = descricao;
    }

    public String getNcm() {
        if (ncm == null) {
            ncm = "";
        }
        return ncm;
    }

    public void setNcm(String ncm) {
        this.ncm = ncm;
    }

    public String getCfop() {
        if (cfop == null) {
            cfop = "";
        }
        return cfop;
    }

    public void setCfop(String cfop) {
        this.cfop = cfop;
    }

    public String getCest() {
        if (cest == null) {
            cest = "";
        }
        return cest;
    }

    public void setCest(String cest) {
        this.cest = cest;
    }

    public String getUnidadeMedida() {
        if (unidadeMedida == null) {
            unidadeMedida = "";
        }
        return unidadeMedida;
    }

    public void setUnidadeMedida(String unidadeMedida) {
        this.unidadeMedida = unidadeMedida;
    }

    @JsonIgnore
    public ProdutoVO getProdutoVO() {
        if (produtoVO == null) {
            produtoVO = new ProdutoVO();
        }
        return produtoVO;
    }

    @JsonIgnore
    public void setProdutoVO(ProdutoVO produtoVO) {
        this.produtoVO = produtoVO;
    }

    public Double getValorDesconto() {
        if (valorDesconto == null) {
            valorDesconto = 0.0;
        }
        return valorDesconto;
    }

    public void setValorDesconto(Double valorDesconto) {
        this.valorDesconto = valorDesconto;
    }

    public Double getOutrasDespesas() {
        if (outrasDespesas == null) {
            outrasDespesas = 0.0;
        }
        return outrasDespesas;
    }

    public void setOutrasDespesas(Double outrasDespesas) {
        this.outrasDespesas = outrasDespesas;
    }

    public String getValorDescontoApresentar() {
        return Formatador.formatarValorMonetario(getValorDesconto());
    }

    public String getValorUnitarioApresentar() {
        return Formatador.formatarValorMonetario(getValorUnitario());
    }

    public String getOutrasDespesasApresentar() {
        return Formatador.formatarValorMonetario(getOutrasDespesas());
    }

    public Double getTotal() {
        return (getValorUnitario() * getQuantidade()) - getValorDesconto() - getOutrasDespesas();
    }

    public String getTotalApresentar() {
        return Formatador.formatarValorMonetario(getTotal());
    }

    public Double getPercentualFederal() {
        if (percentualFederal == null) {
            percentualFederal = 0.0;
        }
        return percentualFederal;
    }

    public void setPercentualFederal(Double percentualFederal) {
        this.percentualFederal = percentualFederal;
    }

    public Double getPercentualEstadual() {
        if (percentualEstadual == null) {
            percentualEstadual = 0.0;
        }
        return percentualEstadual;
    }

    public void setPercentualEstadual(Double percentualEstadual) {
        this.percentualEstadual = percentualEstadual;
    }

    public Double getPercentualMunicipal() {
        if (percentualMunicipal == null) {
            percentualMunicipal = 0.0;
        }
        return percentualMunicipal;
    }

    public void setPercentualMunicipal(Double percentualMunicipal) {
        this.percentualMunicipal = percentualMunicipal;
    }

    public boolean isIsentoPIS() {
        return isentoPIS;
    }

    public void setIsentoPIS(boolean isentoPIS) {
        this.isentoPIS = isentoPIS;
    }

    public boolean isIsentoCOFINS() {
        return isentoCOFINS;
    }

    public void setIsentoCOFINS(boolean isentoCOFINS) {
        this.isentoCOFINS = isentoCOFINS;
    }

    public boolean isIsentoICMS() {
        return isentoICMS;
    }

    public void setIsentoICMS(boolean isentoICMS) {
        this.isentoICMS = isentoICMS;
    }

    public boolean isEnviarPercentualImposto() {
        return enviarPercentualImposto;
    }

    public void setEnviarPercentualImposto(boolean enviarPercentualImposto) {
        this.enviarPercentualImposto = enviarPercentualImposto;
    }
}

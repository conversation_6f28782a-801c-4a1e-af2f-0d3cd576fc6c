package negocio.comuns.notaFiscal;

import negocio.comuns.arquitetura.SuperTO;
import negocio.comuns.financeiro.FormaPagamentoVO;

import java.util.Date;


public class NotaEmitirFormaPagamentoTO extends SuperTO {

    private Double valor;
    private String siglaTipoFormaPagamento;
    private FormaPagamentoVO formaPagamento;
    private Date dataCompensacao;


    public Double getValor() {
        if (valor == null) {
            valor = 0.0;
        }
        return valor;
    }

    public void setValor(Double valor) {
        this.valor = valor;
    }

    public FormaPagamentoVO getFormaPagamento() {
        if (formaPagamento == null) {
            formaPagamento = new FormaPagamentoVO();
        }
        return formaPagamento;
    }

    public void setFormaPagamento(FormaPagamentoVO formaPagamento) {
        this.formaPagamento = formaPagamento;
    }

    public String getSiglaTipoFormaPagamento() {
        if (siglaTipoFormaPagamento == null) {
            siglaTipoFormaPagamento = "";
        }
        return siglaTipoFormaPagamento;
    }

    public void setSiglaTipoFormaPagamento(String siglaTipoFormaPagamento) {
        this.siglaTipoFormaPagamento = siglaTipoFormaPagamento;
    }

    public Date getDataCompensacao() {
        return dataCompensacao;
    }

    public void setDataCompensacao(Date dataCompensacao) {
        this.dataCompensacao = dataCompensacao;
    }
}

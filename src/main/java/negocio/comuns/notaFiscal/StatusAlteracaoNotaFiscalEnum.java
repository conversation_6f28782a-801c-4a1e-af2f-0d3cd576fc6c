package negocio.comuns.notaFiscal;

import negocio.comuns.utilitarias.Ordenacao;

import javax.faces.model.SelectItem;
import java.util.ArrayList;
import java.util.List;

public enum StatusAlteracaoNotaFiscalEnum {

    NEGADA(0, "Negada"),
    CANCELADA(1, "Cancelada");

    private Integer codigo;
    private String descricaoApresentar;


    StatusAlteracaoNotaFiscalEnum(Integer codigo, String descricaoApresentar) {
        this.codigo = codigo;
        this.descricaoApresentar = descricaoApresentar;
    }


    public Integer getCodigo() {
        return codigo;
    }



    public static List<SelectItem> getListaCombo() {
        List<SelectItem> lista = new ArrayList<>();
        for (StatusAlteracaoNotaFiscalEnum obj : values()) {
                lista.add(new SelectItem(obj.getDescricaoApresentar(), obj.getDescricaoApresentar().toUpperCase()));
        }
        Ordenacao.ordenarLista(lista, "label");
        return lista;
    }

    public String getDescricaoApresentar() {
        return descricaoApresentar;
    }


}

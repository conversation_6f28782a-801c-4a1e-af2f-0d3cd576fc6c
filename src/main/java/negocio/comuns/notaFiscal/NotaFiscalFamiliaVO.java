package negocio.comuns.notaFiscal;

import annotations.arquitetura.ChaveEstrangeira;
import annotations.arquitetura.ChavePrimaria;
import negocio.comuns.arquitetura.SuperVO;
import negocio.comuns.arquitetura.UsuarioVO;
import negocio.comuns.financeiro.NFSeEmitidaVO;

public class NotaFiscalFamiliaVO extends SuperVO {

    @ChavePrimaria
    private Integer codigo;
    @ChaveEstrangeira
    private NFSeEmitidaVO nfSeEmitidaVO;
    @ChaveEstrangeira
    private NotaFiscalVO notaFiscalVO;
    private Integer sequencialFamilia;

    public NotaFiscalFamiliaVO() {
    }

    @Override
    public Integer getCodigo() {
        if (codigo == null) {
            codigo = 0;
        }
        return codigo;
    }

    @Override
    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    @Override
    public UsuarioVO getUsuarioVO() {
        if (usuarioVO == null) {
            usuarioVO = new UsuarioVO();
        }
        return usuarioVO;
    }

    @Override
    public void setUsuarioVO(UsuarioVO usuarioVO) {
        this.usuarioVO = usuarioVO;
    }

    public NotaFiscalVO getNotaFiscalVO() {
        if (notaFiscalVO == null) {
            notaFiscalVO = new NotaFiscalVO();
        }
        return notaFiscalVO;
    }

    public void setNotaFiscalVO(NotaFiscalVO notaFiscalVO) {
        this.notaFiscalVO = notaFiscalVO;
    }

    public NFSeEmitidaVO getNfSeEmitidaVO() {
        if(nfSeEmitidaVO == null) {
            nfSeEmitidaVO = new NFSeEmitidaVO();
        }
        return nfSeEmitidaVO;
    }

    public void setNfSeEmitidaVO(NFSeEmitidaVO nfSeEmitidaVO) {
        this.nfSeEmitidaVO = nfSeEmitidaVO;
    }

    public Integer getSequencialFamilia() {
        return sequencialFamilia;
    }

    public void setSequencialFamilia(Integer sequencialFamilia) {
        this.sequencialFamilia = sequencialFamilia;
    }
}

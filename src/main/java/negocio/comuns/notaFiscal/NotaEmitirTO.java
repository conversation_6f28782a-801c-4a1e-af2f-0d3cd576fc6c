package negocio.comuns.notaFiscal;

import negocio.comuns.financeiro.MovPagamentoVO;
import negocio.comuns.arquitetura.UsuarioVO;
import org.json.JSONObject;
import negocio.comuns.arquitetura.SuperTO;
import negocio.comuns.basico.ConfiguracaoNotaFiscalVO;
import negocio.comuns.basico.EmpresaVO;
import negocio.comuns.basico.FornecedorVO;
import negocio.comuns.basico.PessoaVO;

import java.util.*;


public class NotaEmitirTO extends SuperTO {

    private ConfiguracaoNotaFiscalVO configuracaoNotaFiscalVO;
    private UsuarioVO usuarioVO;
    private EmpresaVO empresaVO;
    private PessoaVO pessoaVO;
    private String nomePagador;
    private FornecedorVO fornecedorVO;
    private NotaTO notaTO;
    private Date dataEmissao;
    private Date dataCompetencia;
    private String descricao;
    private String idReferencia;
    private String observacao;
    private boolean notaFamilia = false;
    private Integer sequencialFamilia;
    private String numeroNotaManual;
    private Map<String, NotaEmitirProdutoTO> produtos;
    private List<NotaEmitirFormaPagamentoTO> formasPagamento;
    private Double valorNota;
    private Set<Integer> listaMovPagamento;
    private String chave;


    public PessoaVO getPessoaVO() {
        if (pessoaVO == null) {
            pessoaVO = new PessoaVO();
        }
        return pessoaVO;
    }

    public void setPessoaVO(PessoaVO pessoaVO) {
        this.pessoaVO = pessoaVO;
    }

    public List<NotaEmitirFormaPagamentoTO> getFormasPagamento() {
        if (formasPagamento == null) {
            formasPagamento = new ArrayList<NotaEmitirFormaPagamentoTO>();
        }
        return formasPagamento;
    }

    public void setFormasPagamento(List<NotaEmitirFormaPagamentoTO> formasPagamento) {
        this.formasPagamento = formasPagamento;
    }

    public String getDescricao() {
        if (descricao == null) {
            descricao = "";
        }
        return descricao;
    }

    public void setDescricao(String descricao) {
        this.descricao = descricao;
    }

    public String getIdReferencia() {
        if (idReferencia == null) {
            idReferencia = "";
        }
        return idReferencia;
    }

    public void setIdReferencia(String idReferencia) {
        this.idReferencia = idReferencia;
    }

    public String getObservacao() {
        if (observacao == null) {
            observacao = "";
        }
        return observacao;
    }

    public void setObservacao(String observacao) {
        this.observacao = observacao;
    }

    public Map<String, NotaEmitirProdutoTO> getProdutos() {
        if (produtos == null) {
            produtos = new HashMap<String, NotaEmitirProdutoTO>();
        }
        return produtos;
    }

    public void setProdutos(Map<String, NotaEmitirProdutoTO> produtos) {
        this.produtos = produtos;
    }

    public FornecedorVO getFornecedorVO() {
        return fornecedorVO;
    }

    public void setFornecedorVO(FornecedorVO fornecedorVO) {
        this.fornecedorVO = fornecedorVO;
    }

    public Date getDataEmissao() {
        return dataEmissao;
    }

    public void setDataEmissao(Date dataEmissao) {
        this.dataEmissao = dataEmissao;
    }

    public Date getDataCompetencia() {
        return dataCompetencia;
    }

    public void setDataCompetencia(Date dataCompetencia) {
        this.dataCompetencia = dataCompetencia;
    }

    public NotaTO getNotaTO() {
        if (notaTO == null) {
            notaTO = new NotaTO();
        }
        return notaTO;
    }

    public void setNotaTO(NotaTO notaTO) {
        this.notaTO = notaTO;
    }

    public boolean isNotaFamilia() {
        return notaFamilia;
    }

    public void setNotaFamilia(boolean notaFamilia) {
        this.notaFamilia = notaFamilia;
    }

    public ConfiguracaoNotaFiscalVO getConfiguracaoNotaFiscalVO() {
        if (configuracaoNotaFiscalVO == null) {
            configuracaoNotaFiscalVO = new ConfiguracaoNotaFiscalVO();
        }
        return configuracaoNotaFiscalVO;
    }

    public void setConfiguracaoNotaFiscalVO(ConfiguracaoNotaFiscalVO configuracaoNotaFiscalVO) {
        this.configuracaoNotaFiscalVO = configuracaoNotaFiscalVO;
    }

    public String getNumeroNotaManual() {
        if (numeroNotaManual == null) {
            numeroNotaManual = "";
        }
        return numeroNotaManual;
    }

    public void setNumeroNotaManual(String numeroNotaManual) {
        this.numeroNotaManual = numeroNotaManual;
    }

    public EmpresaVO getEmpresaVO() {
        if (empresaVO == null) {
            empresaVO = new EmpresaVO();
        }
        return empresaVO;
    }

    public void setEmpresaVO(EmpresaVO empresaVO) {
        this.empresaVO = empresaVO;
    }

    public String getNomePagador() {
        if (nomePagador == null) {
            nomePagador = "";
        }
        return nomePagador;
    }

    public void setNomePagador(String nomePagador) {
        this.nomePagador = nomePagador;
    }

    public Double getValorNota() {
        if (valorNota == null) {
            valorNota = 0.0;
        }
        return valorNota;
    }

    public void setValorNota(Double valorNota) {
        this.valorNota = valorNota;
    }

    public Set<Integer> getListaMovPagamento() {
        if (listaMovPagamento == null) {
            listaMovPagamento = new HashSet<>();
        }
        return listaMovPagamento;
    }

    public void setListaMovPagamento(Set<Integer> listaMovPagamento) {
        this.listaMovPagamento = listaMovPagamento;
    }

    public String getChave() {
        if (chave == null) {
            chave = "";
        }
        return chave;
    }

    public void setChave(String chave) {
        this.chave = chave;
    }

    public UsuarioVO getUsuarioVO() {
        if (usuarioVO == null) {
            usuarioVO = new UsuarioVO();
        }
        return usuarioVO;
    }

    public void setUsuarioVO(UsuarioVO usuarioVO) {
        this.usuarioVO = usuarioVO;
    }

    public Integer getSequencialFamilia() {
        return sequencialFamilia;
    }

    public void setSequencialFamilia(Integer sequencialFamilia) {
        this.sequencialFamilia = sequencialFamilia;
    }
}

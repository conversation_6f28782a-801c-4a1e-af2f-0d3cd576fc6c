package negocio.comuns.notaFiscal;

import annotations.arquitetura.ChaveEstrangeira;
import annotations.arquitetura.ChavePrimaria;
import negocio.comuns.arquitetura.SuperVO;
import negocio.comuns.arquitetura.UsuarioVO;
import negocio.comuns.utilitarias.Calendario;
import negocio.comuns.utilitarias.Uteis;

import java.util.Date;

public class NotaFiscalHistoricoVO extends SuperVO {

    @ChavePrimaria
    private Integer codigo;
    private Date dataRegistro;
    private String descricao;
    private String observacao;
    @ChaveEstrangeira
    private UsuarioVO usuarioVO;
    @ChaveEstrangeira
    private NotaFiscalVO notaFiscalVO;

    public NotaFiscalHistoricoVO() {
    }

    public void realizarUpperCaseDados() {
        setDescricao(getDescricao().toUpperCase());
        setObservacao(getObservacao().toUpperCase());
    }

    @Override
    public Integer getCodigo() {
        if (codigo == null) {
            codigo = 0;
        }
        return codigo;
    }

    @Override
    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public Date getDataRegistro() {
        return dataRegistro;
    }

    public void setDataRegistro(Date dataRegistro) {
        this.dataRegistro = dataRegistro;
    }

    public String getDataRegistroApresentar() {
        return Uteis.getDataComHHMM(getDataRegistro());
    }

    @Override
    public UsuarioVO getUsuarioVO() {
        if (usuarioVO == null) {
            usuarioVO = new UsuarioVO();
        }
        return usuarioVO;
    }

    @Override
    public void setUsuarioVO(UsuarioVO usuarioVO) {
        this.usuarioVO = usuarioVO;
    }

    public NotaFiscalVO getNotaFiscalVO() {
        if (notaFiscalVO == null) {
            notaFiscalVO = new NotaFiscalVO();
        }
        return notaFiscalVO;
    }

    public void setNotaFiscalVO(NotaFiscalVO notaFiscalVO) {
        this.notaFiscalVO = notaFiscalVO;
    }

    public String getDescricaoApresentar() {
        StatusEnotasEnum statusEnotasEnum = StatusEnotasEnum.obterPorDescricaoEnotas(getDescricao());
        if (statusEnotasEnum.equals(StatusEnotasEnum.NENHUM)) {
            return getDescricao();
        } else {
            return statusEnotasEnum.getDescricaoApresentar();
        }
    }

    public String getDescricao() {
        if (descricao == null) {
            descricao = "";
        }
        return descricao;
    }

    public void setDescricao(String descricao) {
        this.descricao = descricao;
    }

    public String getObservacao() {
        if (observacao == null) {
            observacao = "";
        }
        return observacao;
    }

    public void setObservacao(String observacao) {
        this.observacao = observacao;
    }
}

package negocio.comuns.notaFiscal;

import annotations.arquitetura.ChaveEstrangeira;
import annotations.arquitetura.ChavePrimaria;
import annotations.arquitetura.NaoControlarLogAlteracao;
import br.com.pactosolucoes.comuns.util.Formatador;
import negocio.comuns.arquitetura.SuperVO;
import negocio.comuns.arquitetura.UsuarioVO;
import negocio.comuns.basico.ConfiguracaoNotaFiscalVO;
import negocio.comuns.basico.EmpresaVO;
import negocio.comuns.basico.PessoaVO;
import negocio.comuns.financeiro.NFSeEmitidaVO;
import negocio.comuns.financeiro.NotaFiscalConsumidorEletronicaVO;
import negocio.comuns.utilitarias.Calendario;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisValidacao;
import org.json.JSONArray;
import org.json.JSONException;
import org.json.JSONObject;

import java.text.SimpleDateFormat;
import java.util.*;

public class NotaFiscalVO extends SuperVO {

    @ChavePrimaria
    private Integer codigo;
    private Date dataRegistro;
    private Date dataEmissao;
    private Date dataAutorizacao;
    private String idEmpresaEnotas;
    private String idExterno;
    private String idReferencia;
    private String idPacto;
    private String jsonNota;
    private String jsonEnvio;
    private String jsonRetorno;
    private String jsonEnvioCancelamento;
    private String jsonRetornoCancelamento;
    private String jsonEnvioInutilizar;
    private String jsonRetornoInutilizar;
    private String statusNota;
    private String razaoSocial;
    private String cpfCnpj;
    private String nomeCliente;
    private String numeroNota;
    private String chaveAcesso;
    private TipoNotaFiscalEnum tipo;
    private Integer sequencialfamilia;
    private Double valor;
    private boolean excluido = false;
    @ChaveEstrangeira
    private PessoaVO pessoaVO;
    @ChaveEstrangeira
    private EmpresaVO empresaVO;
    @ChaveEstrangeira
    private UsuarioVO usuarioVO;
    @ChaveEstrangeira
    private NFSeEmitidaVO nfSeEmitidaVO;
    @ChaveEstrangeira
    private NotaFiscalConsumidorEletronicaVO notaFiscalConsumidorEletronicaVO;
    @ChaveEstrangeira
    private ConfiguracaoNotaFiscalVO configuracaoNotaFiscalVO;
    @NaoControlarLogAlteracao
    private boolean selecionado = false;
    private Integer notaFiscalAnterior;
    private Integer notaFiscalNova;
    @NaoControlarLogAlteracao
    private int matricula;
    @NaoControlarLogAlteracao
    private int contrato;
    private Boolean alteradoManualmente;

    public NotaFiscalVO() {
    }

    public NotaFiscalVO(NotaTO notaTO) {
        this.setConfiguracaoNotaFiscalVO(notaTO.getConfiguracaoNotaFiscalVO());
        this.setTipo(notaTO.getTipoNotaFiscal());
        this.setIdEmpresaEnotas(notaTO.getConfiguracaoNotaFiscalVO().getIdEnotas());
        this.setIdReferencia(notaTO.getNotaIDReferencia());
        this.setUsuarioVO(notaTO.getUsuarioVO());
        this.setEmpresaVO(notaTO.getEmpresaVO());
        this.setPessoaVO(notaTO.getPessoaVO());
        this.setRazaoSocial(notaTO.getCliRazaoSocial());
        this.setNomeCliente(notaTO.getCliNomeAluno());
        this.setCpfCnpj(notaTO.getCliCPFCNPJ());
        this.setStatusNota(StatusEnotasEnum.GERADA.getDescricaoEnotas());
        this.setNfSeEmitidaVO(notaTO.getNfSeEmitidaVO());
        this.setNotaFiscalConsumidorEletronicaVO(notaTO.getNotaFiscalConsumidorEletronicaVO());
        this.setJsonNota(notaTO.toString());
        this.setDataEmissao(notaTO.getNotaDtEmissao());
        this.setSequencialfamilia(notaTO.getSequencialfamilia());
        this.setValor(notaTO.getNotaValor());
    }

    @Override
    public Integer getCodigo() {
        if (codigo == null) {
            codigo = 0;
        }
        return codigo;
    }

    @Override
    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public Date getDataRegistro() {
        return dataRegistro;
    }

    public void setDataRegistro(Date dataRegistro) {
        this.dataRegistro = dataRegistro;
    }

    public String getIdExterno() {
        if (idExterno == null) {
            idExterno = "";
        }
        return idExterno;
    }

    public void setIdExterno(String idExterno) {
        this.idExterno = idExterno;
    }

    public EmpresaVO getEmpresaVO() {
        if (empresaVO == null) {
            empresaVO = new EmpresaVO();
        }
        return empresaVO;
    }

    public void setEmpresaVO(EmpresaVO empresaVO) {
        this.empresaVO = empresaVO;
    }

    public String getJsonEnvio() {
        if (jsonEnvio == null) {
            jsonEnvio = new JSONObject().toString();
        }
        return jsonEnvio;
    }

    public void setJsonEnvio(String jsonEnvio) {
        this.jsonEnvio = jsonEnvio;
    }

    public String getJsonRetorno() {
        if (jsonRetorno == null) {
            jsonRetorno = new JSONObject().toString();
        }
        return jsonRetorno;
    }

    public void setJsonRetorno(String jsonRetorno) {
        this.jsonRetorno = jsonRetorno;
    }

    @Override
    public UsuarioVO getUsuarioVO() {
        if (usuarioVO == null) {
            usuarioVO = new UsuarioVO();
        }
        return usuarioVO;
    }

    @Override
    public void setUsuarioVO(UsuarioVO usuarioVO) {
        this.usuarioVO = usuarioVO;
    }

    public String getIdReferencia() {
        if (idReferencia == null) {
            idReferencia = "";
        }
        return idReferencia;
    }

    public void setIdReferencia(String idReferencia) {
        this.idReferencia = idReferencia;
    }

    public String getJsonEnvioCancelamento() {
        if (jsonEnvioCancelamento == null) {
            jsonEnvioCancelamento = new JSONObject().toString();
        }
        return jsonEnvioCancelamento;
    }

    public void setJsonEnvioCancelamento(String jsonEnvioCancelamento) {
        this.jsonEnvioCancelamento = jsonEnvioCancelamento;
    }

    public String getJsonRetornoCancelamento() {
        if (jsonRetornoCancelamento == null) {
            jsonRetornoCancelamento = new JSONObject().toString();
        }
        return jsonRetornoCancelamento;
    }

    public void setJsonRetornoCancelamento(String jsonRetornoCancelamento) {
        this.jsonRetornoCancelamento = jsonRetornoCancelamento;
    }

    public String getJsonEnvioInutilizar() {
        if (jsonEnvioInutilizar == null) {
            jsonEnvioInutilizar = new JSONObject().toString();
        }
        return jsonEnvioInutilizar;
    }

    public void setJsonEnvioInutilizar(String jsonEnvioInutilizar) {
        this.jsonEnvioInutilizar = jsonEnvioInutilizar;
    }

    public String getJsonRetornoInutilizar() {
        if (jsonRetornoInutilizar == null) {
            jsonRetornoInutilizar = new JSONObject().toString();
        }
        return jsonRetornoInutilizar;
    }

    public void setJsonRetornoInutilizar(String jsonRetornoInutilizar) {
        this.jsonRetornoInutilizar = jsonRetornoInutilizar;
    }

    public boolean isSelecionado() {
        return selecionado;
    }

    public void setSelecionado(boolean selecionado) {
        this.selecionado = selecionado;
    }

    public String getRazaoSocial() {
        if (razaoSocial == null) {
            razaoSocial = "";
        }
        return razaoSocial;
    }

    public void setRazaoSocial(String razaoSocial) {
        this.razaoSocial = razaoSocial;
    }

    public boolean isExcluido() {
        return excluido;
    }

    public void setExcluido(boolean excluido) {
        this.excluido = excluido;
    }

    public String getExcluidoApresentar() {
        return isExcluido() ? "SIM" : "NÃO";
    }

    public String getIdEmpresaEnotas() {
        if (idEmpresaEnotas == null) {
            idEmpresaEnotas = "";
        }
        return idEmpresaEnotas;
    }

    public void setIdEmpresaEnotas(String idEmpresaEnotas) {
        this.idEmpresaEnotas = idEmpresaEnotas;
    }

    public String getAmbienteEmissao() {
        try {
            return new JSONObject(getJsonNota()).getString("notaAmbienteEmissao");
        } catch (Exception ex) {
            return "";
        }
    }

    public String getLinkXML() {
        try {
            JSONObject json = new JSONObject(getJsonRetorno());
            String linkXML = json.optString("nfeLinkXml");
            if (UteisValidacao.emptyString(linkXML)) {
                linkXML = json.optString("linkDownloadXML");
            }
            if (UteisValidacao.emptyString(linkXML)) {
                linkXML = json.optString("linkDownloadXml");
            }
            return linkXML;
        } catch (Exception ex) {
            return "";
        }
    }

    public String getLinkPDF() {
        try {
            JSONObject json = new JSONObject(getJsonRetorno());
            if (getTipo().equals(TipoNotaFiscalEnum.NFSE)) {
                String linkPDF = json.optString("nfeLinkPdf");
                if (UteisValidacao.emptyString(linkPDF)) {
                    linkPDF = json.optString("linkDownloadPDF");
                }
                return linkPDF;
            } else if (getTipo().equals(TipoNotaFiscalEnum.NFCE) || getTipo().equals(TipoNotaFiscalEnum.NFE)) {
                String linkPDF = json.optString("nfeLinkDanfe");
                if (UteisValidacao.emptyString(linkPDF)) {
                    linkPDF = json.optString("linkDanfe");
                }
                if (UteisValidacao.emptyString(linkPDF)) {
                    linkPDF = json.optString("linkDownloadPDF");
                }
                return linkPDF;
            }
            return "";
        } catch (Exception ex) {
            return "";
        }
    }

    public String getNumeroNota() {
        if (numeroNota == null) {
            numeroNota = "";
        }
        return numeroNota;
    }

    public void setNumeroNota(String numeroNota) {
        this.numeroNota = numeroNota;
    }

    public String getChaveAcesso() {
        if (chaveAcesso == null) {
            chaveAcesso = "";
        }
        return chaveAcesso;
    }

    public void setChaveAcesso(String chaveAcesso) {
        this.chaveAcesso = chaveAcesso;
    }

    public TipoNotaFiscalEnum getTipo() {
        if (tipo == null) {
            tipo = TipoNotaFiscalEnum.NFSE;
        }
        return tipo;
    }

    public void setTipo(TipoNotaFiscalEnum tipo) {
        this.tipo = tipo;
    }

    public ConfiguracaoNotaFiscalVO getConfiguracaoNotaFiscalVO() {
        if (configuracaoNotaFiscalVO == null) {
            configuracaoNotaFiscalVO = new ConfiguracaoNotaFiscalVO();
        }
        return configuracaoNotaFiscalVO;
    }

    public void setConfiguracaoNotaFiscalVO(ConfiguracaoNotaFiscalVO configuracaoNotaFiscalVO) {
        this.configuracaoNotaFiscalVO = configuracaoNotaFiscalVO;
    }

    public String getStatusNota() {
        if (statusNota == null) {
            statusNota = "";
        }
        return statusNota;
    }

    public void setStatusNota(String statusNota) {
        this.statusNota = statusNota;
    }

    public StatusEnotasEnum getStatusNotaEnum() {
        return StatusEnotasEnum.obterPorDescricaoEnotas(getStatusNota());
    }

    public String getStatusNotaApresentar() {
        try {
            return StatusEnotasEnum.obterPorDescricaoEnotas(getStatusNota()).getDescricaoApresentar();
        } catch (Exception ex) {
            return getStatusNota();
        }
    }

    public String getStatusNotaHint() {
        try {
            String hint = StatusEnotasEnum.obterPorDescricaoEnotas(getStatusNota()).getHint();
            if(getTipo().equals(TipoNotaFiscalEnum.NFCE)) {
                hint = StatusEnotasEnum.obterPorDescricaoEnotas(getStatusNota()).getHintNFC();
            }
            return hint + getMotivoStatus();
        } catch (Exception ex) {
            return "";
        }
    }

    public String getCpfCnpjApresentar() {
        return getCpfCnpjMascarado(false);
    }

    public String getCpfCnpjMascarado(boolean somenteNumeros) {
        return Uteis.formatarCpfCnpj(getCpfCnpj(), somenteNumeros);
    }

    public String getCpfCnpj() {
        if (cpfCnpj == null) {
            cpfCnpj = "";
        }
        return cpfCnpj;
    }

    public void setCpfCnpj(String cpfCnpj) {
        this.cpfCnpj = cpfCnpj;
    }

    public PessoaVO getPessoaVO() {
        if (pessoaVO == null) {
            pessoaVO = new PessoaVO();
        }
        return pessoaVO;
    }

    public void setPessoaVO(PessoaVO pessoaVO) {
        this.pessoaVO = pessoaVO;
    }

    public String getNomeCliente() {
        if (nomeCliente == null) {
            nomeCliente = "";
        }
        return nomeCliente;
    }

    public void setNomeCliente(String nomeCliente) {
        this.nomeCliente = nomeCliente;
    }

    private String findSerieAttribute(String serie, JSONObject json){
        if(serie == null || serie.isEmpty()){
            try{
                serie = json.optString("serie");
            }catch (Exception ignored){
                try{
                    serie = String.valueOf(json.getInt("serie"));
                }catch (Exception ignoredInt){}
            }
        }
        return serie;
    }

    public String getSerie() {
        JSONObject json = null;
        try {
            json = new JSONObject(getJsonRetorno());
            String serie = "";
            if (getTipo().equals(TipoNotaFiscalEnum.NFSE)) {
                String serieRPS = json.optString("nfeSerieRps");
                if (UteisValidacao.emptyString(serieRPS)) {
                    serieRPS = json.optString("serieRps");
                }
                serie = serieRPS;
            } else if (getTipo().equals(TipoNotaFiscalEnum.NFCE) || getTipo().equals(TipoNotaFiscalEnum.NFE)) {
                serie = json.getString("nfeSerie");
            }

            return findSerieAttribute(serie, json);
        } catch (Exception ex) {
            return findSerieAttribute(null, json);
        }
    }

    public String getRps() {
        try {
            JSONObject json = new JSONObject(getJsonRetorno());
            if (getTipo().equals(TipoNotaFiscalEnum.NFSE) || getTipo().equals(TipoNotaFiscalEnum.NFE)) {
                String nrRPS = json.optString("nfeNumeroRps");
                if (UteisValidacao.emptyString(nrRPS)) {
                    nrRPS = json.optString("numeroRps");
                }
                return nrRPS;
            } else if (getTipo().equals(TipoNotaFiscalEnum.NFCE)) {
                return json.getString("nfeNumero");
            }
            return "";
        } catch (Exception ex) {
            return "";
        }
    }

    public NotaFiscalConsumidorEletronicaVO getNotaFiscalConsumidorEletronicaVO() {
        if (notaFiscalConsumidorEletronicaVO == null) {
            notaFiscalConsumidorEletronicaVO = new NotaFiscalConsumidorEletronicaVO();
        }
        return notaFiscalConsumidorEletronicaVO;
    }

    public void setNotaFiscalConsumidorEletronicaVO(NotaFiscalConsumidorEletronicaVO notaFiscalConsumidorEletronicaVO) {
        this.notaFiscalConsumidorEletronicaVO = notaFiscalConsumidorEletronicaVO;
    }

    public NFSeEmitidaVO getNfSeEmitidaVO() {
        if (nfSeEmitidaVO == null) {
            nfSeEmitidaVO = new NFSeEmitidaVO();
        }
        return nfSeEmitidaVO;
    }

    public void setNfSeEmitidaVO(NFSeEmitidaVO nfSeEmitidaVO) {
        this.nfSeEmitidaVO = nfSeEmitidaVO;
    }

    public String getMotivoStatus() {
        try {

            StringBuilder erro = new StringBuilder();

            if (getStatusNotaEnum().equals(StatusEnotasEnum.ERRO)) {

                JSONArray lista;

                if (getJsonRetorno().contains("retorno_enotas")){
                    JSONObject jsonAux = new JSONObject(getJsonRetorno());
                     lista = new JSONArray(jsonAux.getString("retorno_enotas"));
                }
                else{
                    lista = new JSONArray(getJsonRetorno());
                }

                for (int e = 0; e < lista.length(); e++) {
                    JSONObject obj = lista.getJSONObject(e);
                    erro.append(obj.getString("codigo")).append(" - ").append(obj.getString("mensagem")).append(" <br/>");
                }

            } else if (getStatusNotaEnum().equals(StatusEnotasEnum.NEGADA)) {

                try {
                    JSONObject json = new JSONObject(getJsonRetorno());
                    if (!UteisValidacao.emptyString(json.optString("nfeMotivoStatus"))) {
                        erro.append(json.optString("nfeMotivoStatus").replaceAll("\r\n", "<br/>"));
                    }
                    if (!UteisValidacao.emptyString(json.optString("motivoStatus"))) {
                        erro.append(json.optString("motivoStatus").replaceAll("\r\n", "<br/>"));
                    }
                } catch (JSONException ignored) {
                }
            }

            if (erro.length() > 0) {
                return " <br/><br/> " + erro.toString();
            }

            return "";
        } catch (Exception ex) {
            return "";
        }
    }

    public JSONObject gerarJSONEnvioEnotas(NotaTO notaTO) {
        JSONObject jsonEnvio = new JSONObject();
        jsonEnvio.put("chave", notaTO.getChave());
        jsonEnvio.put("empresa", this.getEmpresaVO().getCodigo());
        jsonEnvio.put("tipoNotaFiscal", this.getTipo().getCodigo());
        jsonEnvio.put("codNotaFiscalZW", this.getCodigo());
        jsonEnvio.put("idEmpresaEnotas", this.getIdEmpresaEnotas());
        return jsonEnvio;
    }

    public JSONObject gerarJSONConsultaNota() {
        JSONObject jsonEnvio = new JSONObject();
        jsonEnvio.put("idEmpresaEnotas", this.getIdEmpresaEnotas());
        jsonEnvio.put("idExterno", this.getIdExterno());
        jsonEnvio.put("idReferencia", this.getIdPacto());
        jsonEnvio.put("tipoNotaFiscal", this.getTipo().getCodigo());
        return jsonEnvio;
    }

    public String getJsonNota() {
        if (jsonNota == null) {
            jsonNota = new JSONObject().toString();
        }
        return jsonNota;
    }

    public void setJsonNota(String jsonNota) {
        this.jsonNota = jsonNota;
    }

    public String getEmpresaRazaoSocial() {
        try {
            JSONObject json = new JSONObject(getJsonNota());
            return json.getString("empRazaoSocial");
        } catch (Exception ex) {
            return "";
        }
    }

    public String getEmpresaNomeFantasia() {
        try {
            JSONObject json = new JSONObject(getJsonNota());
            return json.getString("empNomeFantasia");
        } catch (Exception ex) {
            return "";
        }
    }

    public String getEmpresaCNPJ() {
        try {
            JSONObject json = new JSONObject(getJsonNota());
            return Uteis.formatarCpfCnpj(json.getString("empCNPJ"), false);
        } catch (Exception ex) {
            return "";
        }
    }

    public String getEmpresaTelefone() {
        try {
            JSONObject json = new JSONObject(getJsonNota());
            return json.getString("empTelefone");
        } catch (Exception ex) {
            return "";
        }
    }

    public String getClienteCidade() {
        try {
            JSONObject json = new JSONObject(getJsonNota());
            return json.getString("cliEndCidade");
        } catch (Exception ex) {
            return "";
        }
    }

    public String getClienteCidadeIBGE() {
        try {
            JSONObject json = new JSONObject(getJsonNota());
            return json.getString("cliEndCidadeIBGE");
        } catch (Exception ex) {
            return "";
        }
    }

    public String getClienteEstado() {
        try {
            JSONObject json = new JSONObject(getJsonNota());
            return json.getString("cliEndUFEstado");
        } catch (Exception ex) {
            return "";
        }
    }

    public String getClienteEstadoIBGE() {
        try {
            JSONObject json = new JSONObject(getJsonNota());
            return json.getString("cliEndUFEstadoIBGE");
        } catch (Exception ex) {
            return "";
        }
    }

    public String getClienteTelefone() {
        try {
            JSONObject json = new JSONObject(getJsonNota());
            return json.getString("cliTelefone");
        } catch (Exception ex) {
            return "";
        }
    }

    public String getClienteEndCEP() {
        try {
            JSONObject json = new JSONObject(getJsonNota());
            return json.getString("cliEndCEP");
        } catch (Exception ex) {
            return "";
        }
    }

    public String getClienteEmail() {
        try {
            JSONObject json = new JSONObject(getJsonNota());
            return json.getString("cliEmail");
        } catch (Exception ex) {
            return "";
        }
    }

    public String getClienteEndLogradouro() {
        try {
            JSONObject json = new JSONObject(getJsonNota());
            return json.getString("cliEndLogradouro");
        } catch (Exception ex) {
            return "";
        }
    }

    public String getClienteEndNumero() {
        try {
            JSONObject json = new JSONObject(getJsonNota());
            return json.getString("cliEndNumero");
        } catch (Exception ex) {
            return "";
        }
    }

    public String getClienteEndComplemento() {
        try {
            JSONObject json = new JSONObject(getJsonNota());
            return json.getString("cliEndComplemento");
        } catch (Exception ex) {
            return "";
        }
    }

    public String getClienteEndBairro() {
        try {
            JSONObject json = new JSONObject(getJsonNota());
            return json.getString("cliEndBairro");
        } catch (Exception ex) {
            return "";
        }
    }

    public String getClientePais() {
        try {
            JSONObject json = new JSONObject(getJsonNota());
            return json.getString("cliEndPais");
        } catch (Exception ex) {
            return "";
        }
    }

    public String getClienteInscMunicipal() {
        try {
            JSONObject json = new JSONObject(getJsonNota());
            return json.getString("cliInscMunicipal");
        } catch (Exception ex) {
            return "";
        }
    }

    public String getClienteInscEstadual() {
        try {
            JSONObject json = new JSONObject(getJsonNota());
            return json.getString("cliInscEstadual");
        } catch (Exception ex) {
            return "";
        }
    }

    public String getItemListaServico() {
        try {
            JSONObject json = new JSONObject(getJsonNota());
            return json.getString("notaItemListaServico");
        } catch (Exception ex) {
            return "";
        }
    }

    public String getCodigoTributacaoMunicipio() {
        try {
            JSONObject json = new JSONObject(getJsonNota());
            return json.getString("notaCodigoTributacaoMunicipio");
        } catch (Exception ex) {
            return "";
        }
    }

    public String getCodigoCNAE() {
        try {
            JSONObject json = new JSONObject(getJsonNota());
            return json.getString("notaCNAE");
        } catch (Exception ex) {
            return "";
        }
    }

    public String getAliquotaISS() {
        try {
            JSONObject json = new JSONObject(getJsonNota());
            Double aliquota = json.getDouble("notaAliquotaISS");
            return aliquota.toString();
        } catch (Exception ex) {
            return "";
        }
    }

    public String getNaturezaOperacao() {
        try {
            JSONObject json = new JSONObject(getJsonNota());
            return json.getString("notaNaturezaOperacao");
        } catch (Exception ex) {
            return "";
        }
    }

    public String getDescricao() {
        try {
            JSONObject json = new JSONObject(getJsonNota());
            return json.getString("notaDescricao");
        } catch (Exception ex) {
            return "";
        }
    }

    public String getObservacao() {
        try {
            JSONObject json = new JSONObject(getJsonNota());
            return json.getString("notaObservacao");
        } catch (Exception ex) {
            return "";
        }
    }

    public String getInformacaoFisco() {
        try {
            JSONObject json = new JSONObject(getJsonNota());
            return json.getString("notaInformacaoFisco");
        } catch (Exception ex) {
            return "";
        }
    }

    public String getEnviarEmail() {
        try {
            JSONObject json = new JSONObject(getJsonNota());
            return json.getBoolean("notaEnviarEmail") ? "SIM" : "NÃO";
        } catch (Exception ex) {
            return "";
        }
    }

    public List<NotaProdutoTO> getNotaProdutos() {
        try {
            List<NotaProdutoTO> retorno = new ArrayList<NotaProdutoTO>();

            JSONObject json = new JSONObject(getJsonNota());
            JSONArray jsonArray = json.getJSONArray("notaProdutos");

            for (int e = 0; e < jsonArray.length(); e++) {
                JSONObject obj = jsonArray.getJSONObject(e);
                try {
                    NotaProdutoTO novo = new NotaProdutoTO();
                    novo.setValorUnitario(obj.getDouble("valorUnitario"));
                    novo.setValorDesconto(obj.getDouble("valorDesconto"));
                    novo.setOutrasDespesas(obj.getDouble("outrasDespesas"));
                    novo.setQuantidade(obj.getInt("quantidade"));
                    novo.setDescricao(obj.getString("descricao"));
                    novo.setNcm(obj.getString("ncm"));
                    novo.setCfop(obj.getString("cfop"));
                    novo.setUnidadeMedida(obj.getString("unidadeMedida"));
                    retorno.add(novo);
                } catch (Exception ignored) {
                }
            }
            return retorno;
        } catch (Exception ex) {
            return new ArrayList<NotaProdutoTO>();
        }
    }

    public List<NotaPagamentoTO> getNotaPagamentos() {
        try {
            List<NotaPagamentoTO> retorno = new ArrayList<NotaPagamentoTO>();

            JSONObject json = new JSONObject(getJsonNota());
            JSONArray jsonArray = json.getJSONArray("notaPagamentos");

            for (int e = 0; e < jsonArray.length(); e++) {
                JSONObject obj = jsonArray.getJSONObject(e);
                try {
                    NotaPagamentoTO novo = new NotaPagamentoTO();
                    novo.setDescricaoFormaPagamento(obj.getString("descricaoFormaPagamento"));
                    novo.setSiglaFormaPagamento(obj.getString("siglaFormaPagamento"));
                    novo.setValor(obj.getDouble("valor"));
                    retorno.add(novo);
                } catch (Exception ignored) {
                }
            }
            return retorno;
        } catch (Exception ex) {
            return new ArrayList<NotaPagamentoTO>();
        }
    }

    public boolean isApresentaPDF() {
        return !UteisValidacao.emptyString(getLinkPDF());
    }

    public boolean isApresentaReenviar() {
        return UteisValidacao.emptyNumber(getSequencialfamilia()) &&
                (getTipo().equals(TipoNotaFiscalEnum.NFSE) || getTipo().equals(TipoNotaFiscalEnum.NFCE) || getTipo().equals(TipoNotaFiscalEnum.NFE)) &&
                !isExcluido() &&
                (getStatusNotaEnum().equals(StatusEnotasEnum.ERRO) || getStatusNotaEnum().equals(StatusEnotasEnum.NEGADA));
    }

    public boolean isApresentaXML() {
        return !UteisValidacao.emptyString(getLinkXML());
    }

    public boolean isPodeCancelar() {
        return getStatusNotaEnum().equals(StatusEnotasEnum.AUTORIZADA) || getStatusNotaEnum().equals(StatusEnotasEnum.CANCELAMENTONEGADO);
    }

    public boolean isPodeInutilizar() {
        return (getStatusNotaEnum().equals(StatusEnotasEnum.NEGADA) || getStatusNotaEnum().equals(StatusEnotasEnum.INUTILIZACAONEGADA)) &&
                (getTipo().equals(TipoNotaFiscalEnum.NFCE) || getTipo().equals(TipoNotaFiscalEnum.NFE));
    }

    public Date getDataAutorizacao() {
        return dataAutorizacao;
    }

    public void setDataAutorizacao(Date dataAutorizacao) {
        this.dataAutorizacao = dataAutorizacao;
    }

    public Date getDataAutorizacaoJSONRetorno() {
        try {
            JSONObject json = new JSONObject(getJsonRetorno());
            final SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss");
            sdf.setTimeZone(TimeZone.getTimeZone("UTC"));
            return sdf.parse(json.getString("nfeDataAutorizacao"));
        } catch (Exception ex) {
            return null;
        }
    }

    public Date getDataCompetenciaJSONRetorno() {
        try {
            JSONObject jsonRetorno = new JSONObject(getJsonRetorno());
            final SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
            sdf.setTimeZone(TimeZone.getTimeZone("UTC"));
            if (!UteisValidacao.emptyString(jsonRetorno.optString("dataCompetencia"))) {
                return sdf.parse(jsonRetorno.getString("dataCompetencia"));
            } else if (!UteisValidacao.emptyString(jsonRetorno.optString("dataCompetenciaRps"))) {
                return sdf.parse(jsonRetorno.getString("dataCompetenciaRps"));
            } else {
                //se não achar no retorno, pegar do envio
                JSONObject jsonEnvio = new JSONObject(getJsonEnvio());
                if (!UteisValidacao.emptyString(jsonEnvio.optJSONObject("jsonNota").optString("dataCompetencia"))) {
                    return sdf.parse(jsonEnvio.optJSONObject("jsonNota").optString("dataCompetencia"));
                }
            }
            return null;
        } catch (Exception ex) {
            return null;
        }
    }

    public Date getDataEmissao() {
        return dataEmissao;
    }

    public void setDataEmissao(Date dataEmissao) {
        this.dataEmissao = dataEmissao;
    }

    public String getHintNotaRetroativa() {
        try {
            if (Calendario.igual(getDataRegistro(), getDataEmissao())) {
                return "";
            }

            StringBuilder info = new StringBuilder();
            info.append("Envio retroativo: <br/><br/>");
            info.append("Dt. Registro: ").append(Uteis.getDataComHora(getDataRegistro())).append("<br/>");
            info.append("Dt. Emissão: ").append(Uteis.getDataComHora(getDataEmissao())).append("<br/>");
            if(!UteisValidacao.emptyString(getUsuarioVO().getNome())){
                info.append("Responsável pela emissão: ").append(getUsuarioVO().getNome());
            }
            return info.toString();
        } catch (Exception ex) {
            return "";
        }
    }

    public String getHintNomeAluno() {
        try {
            if (getNomeCliente().toUpperCase().equals(getRazaoSocial().toUpperCase())) {
                return "";
            }

            StringBuilder info = new StringBuilder();
            info.append("Nome Responsável: ").append(getRazaoSocial()).append("<br/>");
            info.append("Nome Aluno: ").append(getNomeCliente());
            return info.toString();
        } catch (Exception ex) {
            return "";
        }
    }

    public String getDataEmissaoApresentar() {
        if (getDataEmissao() == null) {
            return "";
        }
        return Calendario.getDataAplicandoFormatacao(getDataEmissao(), "dd/MM/yy - HH:mm:ss");
    }

    public String getDataAutorizacaoApresentar() {
        if (getDataAutorizacao() == null) {
            if (getDataAutorizacaoJSONRetorno() != null) {
                return Calendario.getDataAplicandoFormatacao(getDataAutorizacaoJSONRetorno(), "dd/MM/yy - HH:mm:ss");
            } else {
                return "";
            }
        }
        return Calendario.getDataAplicandoFormatacao(getDataAutorizacao(), "dd/MM/yy - HH:mm:ss");
    }

    public String getDataCompetenciaApresentar() {
        try {
            if (getDataCompetenciaJSONRetorno() != null) {
                return Calendario.getDataAplicandoFormatacao(getDataCompetenciaJSONRetorno(), "dd/MM/yy");
            }
        } catch (Exception ignored) {
        }
        return "";
    }

    public String getDataRegistroApresentar() {
        if (getDataRegistro() == null) {
            return "";
        }
        return Calendario.getDataAplicandoFormatacao(getDataRegistro(), "dd/MM/yy - HH:mm:ss");
    }

    public Integer getSequencialfamilia() {
        return sequencialfamilia;
    }

    public void setSequencialfamilia(Integer sequencialfamilia) {
        this.sequencialfamilia = sequencialfamilia;
    }

    public Double getValor() {
        if (valor == null) {
            valor = 0.0;
        }
        return valor;
    }

    public void setValor(Double valor) {
        this.valor = valor;
    }

    private Double getValorJSON() {
        try {
            return new JSONObject(getJsonNota()).getDouble("notaValor");
        } catch (Exception ex) {
            return 0.0;
        }
    }

    public String getValorApresentar() {
        try {
            Double valor = getValor();
            if (UteisValidacao.emptyNumber(valor)) {
                valor = getValorJSON();
            }
            return Formatador.formatarValorMonetario(valor);
        } catch (Exception ex) {
            return "";
        }
    }

    public Integer getNotaFiscalAnterior() {
        return notaFiscalAnterior;
    }

    public void setNotaFiscalAnterior(Integer notaFiscalAnterior) {
        this.notaFiscalAnterior = notaFiscalAnterior;
    }

    public Integer getNotaFiscalNova() {
        return notaFiscalNova;
    }

    public void setNotaFiscalNova(Integer notaFiscalNova) {
        this.notaFiscalNova = notaFiscalNova;
    }

    public boolean isPermiteAlterarDataReenvio() throws Exception {
        return (getTipo().equals(TipoNotaFiscalEnum.NFSE) && getEmpresaVO().isPermiteAlterarDataEmissaoNFSe()) || getTipo().equals(TipoNotaFiscalEnum.NFE);
    }

    public String getIdPacto() {
        if (idPacto == null) {
            idPacto = "";
        }
        return idPacto;
    }

    public void setIdPacto(String idPacto) {
        this.idPacto = idPacto;
    }

    public int getMatricula() {
        return matricula;
    }

    public void setMatricula(int matricula) {
        this.matricula = matricula;
    }

    public int getContrato() {
        return contrato;
    }

    public void setContrato(int contrato) {
        this.contrato = contrato;
    }

    public Boolean getAlteradoManualmente() {
        if(alteradoManualmente == null){
            alteradoManualmente = Boolean.FALSE;
        }
        return alteradoManualmente;
    }

    public void setAlteradoManualmente(Boolean alteradoManualmente) {
        this.alteradoManualmente = alteradoManualmente;
    }
}

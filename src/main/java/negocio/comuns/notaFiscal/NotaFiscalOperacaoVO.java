package negocio.comuns.notaFiscal;

import annotations.arquitetura.ChaveEstrangeira;
import annotations.arquitetura.ChavePrimaria;
import negocio.comuns.arquitetura.SuperVO;
import negocio.comuns.arquitetura.UsuarioVO;
import org.json.JSONObject;

import java.util.Date;

public class NotaFiscalOperacaoVO extends SuperVO {

    @ChavePrimaria
    private Integer codigo;
    private Date dataRegistro;
    private String idReferencia;
    private String idExterno;
    private OperacaoNotaFiscalEnum operacao;
    private String chave;
    private String idEmpresaEnotas;
    @ChaveEstrangeira
    private UsuarioVO usuarioVO;
    @ChaveEstrangeira
    private NotaFiscalVO notaFiscalVO;
    private StatusOperacaoNotaFiscalEnum status;
    private String jsonRetorno;
    private String statusRetorno;
    private Integer numeroInicial;
    private Integer numeroFinal;
    private String justificativa;
    private String ambienteEmissao;
    private String serie;
    private Integer tipoNota;

    public NotaFiscalOperacaoVO() {
    }

    @Override
    public Integer getCodigo() {
        if (codigo == null) {
            codigo = 0;
        }
        return codigo;
    }

    @Override
    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public Date getDataRegistro() {
        return dataRegistro;
    }

    public void setDataRegistro(Date dataRegistro) {
        this.dataRegistro = dataRegistro;
    }

    @Override
    public UsuarioVO getUsuarioVO() {
        if (usuarioVO == null) {
            usuarioVO = new UsuarioVO();
        }
        return usuarioVO;
    }

    @Override
    public void setUsuarioVO(UsuarioVO usuarioVO) {
        this.usuarioVO = usuarioVO;
    }

    public OperacaoNotaFiscalEnum getOperacao() {
        if (operacao == null) {
            operacao = OperacaoNotaFiscalEnum.NENHUM;
        }
        return operacao;
    }

    public void setOperacao(OperacaoNotaFiscalEnum operacao) {
        this.operacao = operacao;
    }

    public NotaFiscalVO getNotaFiscalVO() {
        if (notaFiscalVO == null) {
            notaFiscalVO = new NotaFiscalVO();
        }
        return notaFiscalVO;
    }

    public void setNotaFiscalVO(NotaFiscalVO notaFiscalVO) {
        this.notaFiscalVO = notaFiscalVO;
    }

    public String getChave() {
        if (chave == null) {
            chave = "";
        }
        return chave;
    }

    public void setChave(String chave) {
        this.chave = chave;
    }

    public JSONObject getJsonOperacao() {
        if (getOperacao().equals(OperacaoNotaFiscalEnum.CANCELAR)) {
            return new JSONObject();

        } else {
            //a forma como é generado o identificador é de extrema importancia para a identificação da chave
            //da academia e código da NotaFiscalOperacao
            //by Luiz Felipe

            JSONObject json = new JSONObject();
            json.put("id", this.getChave() + "_INU_" + this.getCodigo());
            json.put("ambienteEmissao", this.getAmbienteEmissao());
            json.put("serie", this.getSerie());
            json.put("numeroInicial", this.getNumeroInicial());
            json.put("numeroFinal", this.getNumeroFinal());
            json.put("justificativa", this.getJustificativa());
            return json;

        }
    }

    public JSONObject gerarJsonEnvio() {
        JSONObject jsonEnvio = new JSONObject();
        jsonEnvio.put("chave", this.getChave());
        jsonEnvio.put("codOperacaoZW", this.getCodigo());
        jsonEnvio.put("operacao", this.getOperacao().getCodigo());
        jsonEnvio.put("tipoNotaFiscal", this.getTipoNota());
        jsonEnvio.put("idEmpresaEnotas", this.getIdEmpresaEnotas());
        jsonEnvio.put("idExternoNota", this.getNotaFiscalVO().getIdExterno());
        jsonEnvio.put("jsonOperacao", this.getJsonOperacao().toString());
        return jsonEnvio;
    }

    public Integer getNumeroInicial() {
        return numeroInicial;
    }

    public void setNumeroInicial(Integer numeroInicial) {
        this.numeroInicial = numeroInicial;
    }

    public Integer getNumeroFinal() {
        return numeroFinal;
    }

    public void setNumeroFinal(Integer numeroFinal) {
        this.numeroFinal = numeroFinal;
    }

    public String getJustificativa() {
        if (justificativa == null) {
            justificativa = "";
        }
        return justificativa;
    }

    public void setJustificativa(String justificativa) {
        this.justificativa = justificativa;
    }

    public String getIdEmpresaEnotas() {
        if (idEmpresaEnotas == null) {
            idEmpresaEnotas = "";
        }
        return idEmpresaEnotas;
    }

    public void setIdEmpresaEnotas(String idEmpresaEnotas) {
        this.idEmpresaEnotas = idEmpresaEnotas;
    }

    public String getJsonRetorno() {
        if (jsonRetorno == null) {
            jsonRetorno = new JSONObject().toString();
        }
        return jsonRetorno;
    }

    public void setJsonRetorno(String jsonRetorno) {
        this.jsonRetorno = jsonRetorno;
    }

    public StatusOperacaoNotaFiscalEnum getStatus() {
        if (status == null) {
            status = StatusOperacaoNotaFiscalEnum.NENHUM;
        }
        return status;
    }

    public void setStatus(StatusOperacaoNotaFiscalEnum status) {
        this.status = status;
    }

    public String getStatusRetorno() {
        if (statusRetorno == null) {
            statusRetorno = "";
        }
        return statusRetorno;
    }

    public void setStatusRetorno(String statusRetorno) {
        this.statusRetorno = statusRetorno;
    }

    public String getAmbienteEmissao() {
        if(ambienteEmissao == null) {
            ambienteEmissao = "";
        }
        return ambienteEmissao;
    }

    public void setAmbienteEmissao(String ambienteEmissao) {
        this.ambienteEmissao = ambienteEmissao;
    }

    public String getSerie() {
        if(serie == null) {
            serie = "";
        }
        return serie;
    }

    public void setSerie(String serie) {
        this.serie = serie;
    }

    public Integer getTipoNota() {
        if(tipoNota == null) {
            tipoNota = 0;
        }
        return tipoNota;
    }

    public void setTipoNota(Integer tipoNota) {
        this.tipoNota = tipoNota;
    }

    public String getIdReferencia() {
        return idReferencia;
    }

    public void setIdReferencia(String idReferencia) {
        this.idReferencia = idReferencia;
    }

    public String getIdExterno() {
        return idExterno;
    }

    public void setIdExterno(String idExterno) {
        this.idExterno = idExterno;
    }
}

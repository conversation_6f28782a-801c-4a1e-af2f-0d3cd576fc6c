package negocio.comuns.notaFiscal;

public enum StatusOperacaoNotaFiscalEnum {

    NENHUM(0, "", ""),
    GERADA(1, "GERADA", "Solicitação aguardando para ser enviada para eNotas"),
    ENVIADA(2, "ENVIADA", "Aguardando retorno da solicitação"),
    PROCESSADA(3, "PROCESSADA", "Solicitação já retornada");

    private Integer codigo;
    private String descricao;
    private String hint;

    private StatusOperacaoNotaFiscalEnum(Integer codigo, String descricao, String hint) {
        this.codigo = codigo;
        this.descricao = descricao;
        this.hint = hint;
    }

    public Integer getCodigo() {
        return codigo;
    }

    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public String getDescricao() {
        return descricao;
    }

    public void setDescricao(String descricao) {
        this.descricao = descricao;
    }

    public static StatusOperacaoNotaFiscalEnum obterPorCodigo(Integer codigo) {
        for (StatusOperacaoNotaFiscalEnum obj : StatusOperacaoNotaFiscalEnum.values()) {
            if (obj.getCodigo().equals(codigo)) {
                return obj;
            }
        }
        return NENHUM;
    }

    public String getHint() {
        return hint;
    }

    public void setHint(String hint) {
        this.hint = hint;
    }
}
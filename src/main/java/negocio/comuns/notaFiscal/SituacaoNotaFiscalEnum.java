package negocio.comuns.notaFiscal;

public enum SituacaoNotaFiscalEnum {

    NENHUM(0, "", ""),// sem status
    GERADA(1, "Gerada", "Gerada pelo sistema aguardando envio."), //gerada pelo sistema - aguardando o envio para o emissor de notas fiscais
    ENVIADA(2, "Enviada", "Pedido enviado aguardando processamento."), //enviada para o emissor de notas fiscais
    PROCESSADA(3, "Processada", "Nota processada, verifique o status e motivo."), //recebida pelo emissor de notas fiscais
    ERRO_GERACAO(4, "Erro ao gerar nota fiscal", "Erro ao gerar nota fiscal verifique o retorno."), //erro tentar gerar a nota fiscal para enviar para o emissor de notas
    NAO_PROCESSADA(5, "Não processada", "Nota negada, confira o motivo."); //nota foi negada pelo emissor de notas fiscais

    private Integer codigo;
    private String descricao;
    private String hint;

    SituacaoNotaFiscalEnum(Integer codigo, String descricao, String hint) {
        this.codigo = codigo;
        this.descricao = descricao;
        this.hint = hint;
    }

    public static SituacaoNotaFiscalEnum obterPorCodigo(Integer codigo) {
        for (SituacaoNotaFiscalEnum obj : SituacaoNotaFiscalEnum.values()) {
            if (obj.getCodigo().equals(codigo))
                return obj;
        }
        return NENHUM;
    }

    public Integer getCodigo() {
        return codigo;
    }

    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public String getDescricao() {
        return descricao;
    }

    public void setDescricao(String descricao) {
        this.descricao = descricao;
    }

    public String getHint() {
        return hint;
    }

    public void setHint(String hint) {
        this.hint = hint;
    }
}

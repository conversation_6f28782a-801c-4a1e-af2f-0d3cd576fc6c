package negocio.comuns.notaFiscal;

import br.com.pactosolucoes.comuns.util.Formatador;
import negocio.comuns.arquitetura.SuperTO;
import negocio.comuns.utilitarias.Uteis;

public class TotalizadorNotaFiscalTO extends SuperTO {

    private String descricao;
    private Double valor;
    private Integer quantidade;
    private Double porcentagem;
    private String css;
    private String title;

    public Double getValor() {
        if (valor == null) {
            valor = 0.0;
        }
        return valor;
    }

    public void setValor(Double valor) {
        this.valor = valor;
    }

    public Integer getQuantidade() {
        if (quantidade == null) {
            quantidade = 0;
        }
        return quantidade;
    }

    public void setQuantidade(Integer quantidade) {
        this.quantidade = quantidade;
    }

    public String getValorApresentar() {
        return Formatador.formatarValorMonetario(getValor());
    }

    public String getCss() {
        if (css == null) {
            css = "";
        }
        return css;
    }

    public void setCss(String css) {
        this.css = css;
    }

    public Double getPorcentagem() {
        if (porcentagem == null) {
            porcentagem = 0.0;
        }
        return porcentagem;
    }

    public void setPorcentagem(Double porcentagem) {
        this.porcentagem = porcentagem;
    }

    public String getPorcentagemApresentar() {
        if (getPorcentagem() == null) {
            return "";
        }
        return Uteis.arrendondarForcando2CadasDecimaisComVirgula(getPorcentagem());
    }

    public void calcularPorcentagem(Double valorParaComparar) {
        if (valorParaComparar > 0.0) {
            setPorcentagem((this.getValor() / valorParaComparar) * 100);
        } else {
            setPorcentagem(0.0);
        }
    }

    public String getTitle() {
        if (title == null) {
            title = "";
        }
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public String getDescricao() {
        if (descricao == null) {
            descricao = "";
        }
        return descricao;
    }

    public void setDescricao(String descricao) {
        this.descricao = descricao;
    }
}
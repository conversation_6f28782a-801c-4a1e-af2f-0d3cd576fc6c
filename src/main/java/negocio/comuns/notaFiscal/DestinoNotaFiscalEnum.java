package negocio.comuns.notaFiscal;

public enum DestinoNotaFiscalEnum {

    MANUAL(0, "MANUAL"),
    ENOTAS(1, "ENOTAS"),
    DELPHI(2, "DELPHI");

    private Integer codigo;
    private String descricao;

    private DestinoNotaFiscalEnum(Integer codigo, String descricao) {
        this.codigo = codigo;
        this.descricao = descricao;
    }

    public Integer getCodigo() {
        return codigo;
    }

    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public String getDescricao() {
        return descricao;
    }

    public void setDescricao(String descricao) {
        this.descricao = descricao;
    }

    public static DestinoNotaFiscalEnum obterPorCodigo(Integer codigo) {
        for (DestinoNotaFiscalEnum nota : DestinoNotaFiscalEnum.values()) {
            if (nota.getCodigo().equals(codigo)) {
                return nota;
            }
        }
        return null;
    }
}

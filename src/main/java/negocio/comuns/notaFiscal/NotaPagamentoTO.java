package negocio.comuns.notaFiscal;

import br.com.pactosolucoes.comuns.util.StringUtilities;
import br.com.pactosolucoes.comuns.util.Formatador;
import negocio.comuns.arquitetura.SuperTO;

import java.util.Date;

public class NotaPagamentoTO extends SuperTO {

    private String descricaoFormaPagamento;
    private String siglaFormaPagamento;
    private Double valor;
    private Date dataVencimento;
    private Integer numero;


    public String getDescricaoFormaPagamento() {
        if (descricaoFormaPagamento == null) {
            descricaoFormaPagamento = "";
        }
        return descricaoFormaPagamento;
    }

    public void setDescricaoFormaPagamento(String descricaoFormaPagamento) {
        this.descricaoFormaPagamento = descricaoFormaPagamento;
    }

    public String getSiglaFormaPagamento() {
        if (siglaFormaPagamento == null) {
            siglaFormaPagamento = "";
        }
        return siglaFormaPagamento;
    }

    public void setSiglaFormaPagamento(String siglaFormaPagamento) {
        this.siglaFormaPagamento = siglaFormaPagamento;
    }

    public Double getValor() {
        if (valor == null) {
            valor = 0.0;
        }
        return valor;
    }

    public void setValor(Double valor) {
        this.valor = valor;
    }

    public String getSiglaDescricao() {
        return TipoPagamentoNotaFiscalEnum.obterPorSigla(getSiglaFormaPagamento()).getDescricao();
    }

    public String getValorApresentar() {
        return Formatador.formatarValorMonetario(getValor());
    }

    public Date getDataVencimento() {
        return dataVencimento;
    }

    public void setDataVencimento(Date dataVencimento) {
        this.dataVencimento = dataVencimento;
    }

    public Integer getNumero() {
        return numero;
    }

    public void setNumero(Integer numero) {
        this.numero = numero;
    }

    public String getNumeroFormatado() {
        return getNumero() == null ? "" : StringUtilities.formatarCampoForcandoZerosAEsquerda(getNumero().toString(), 3);
    }
}

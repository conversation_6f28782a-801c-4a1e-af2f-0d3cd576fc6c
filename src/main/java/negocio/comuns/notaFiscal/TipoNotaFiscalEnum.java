package negocio.comuns.notaFiscal;

import javax.faces.model.SelectItem;
import java.util.ArrayList;
import java.util.List;

public enum TipoNotaFiscalEnum {

    TODAS(999, "TODAS", ""),
    NFE(0, "NF-e", "Nota Fiscal Eletrônica"),
    NFSE(1, "NFS-e", "Nota Fiscal de Serviço Eletrônica"),
    NFCE(2, "NFC-e", "Nota Fiscal do Consumidor");

    private Integer codigo;
    private String descricao;
    private String descricaoEmail;

    private TipoNotaFiscalEnum(Integer codigo, String descricao, String descricaoEmail) {
        this.codigo = codigo;
        this.descricao = descricao;
        this.descricaoEmail = descricaoEmail;
    }

    public Integer getCodigo() {
        return codigo;
    }

    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public String getDescricao() {
        return descricao;
    }

    public void setDescricao(String descricao) {
        this.descricao = descricao;
    }

    public static TipoNotaFiscalEnum obterPorCodigo(Integer codigo) {
        for (TipoNotaFiscalEnum nota : TipoNotaFiscalEnum.values()) {
            if (nota.getCodigo().equals(codigo)) {
                return nota;
            }
        }
        return null;
    }

    public static List<SelectItem> getListaCombo(){
        List<SelectItem> lista = new ArrayList<SelectItem>();
        for(TipoNotaFiscalEnum obj : values()){
            lista.add(new SelectItem(obj.getCodigo(), obj.getDescricao()));
        }
        return lista;
    }

    public String getDescricaoEmail() {
        return descricaoEmail;
    }

    public void setDescricaoEmail(String descricaoEmail) {
        this.descricaoEmail = descricaoEmail;
    }
}

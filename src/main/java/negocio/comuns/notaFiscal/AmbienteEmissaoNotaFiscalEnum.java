package negocio.comuns.notaFiscal;

import br.com.pactosolucoes.comuns.util.StringUtilities;

public enum AmbienteEmissaoNotaFiscalEnum {

    HOMOLOGACAO(0, "HOMOLOGAÇÃO"),
    PRODUCAO(1, "PRODUÇÃO");

    private Integer codigo;
    private String descricao;

    private AmbienteEmissaoNotaFiscalEnum(Integer codigo, String descricao) {
        this.codigo = codigo;
        this.descricao = descricao;
    }

    public Integer getCodigo() {
        return codigo;
    }

    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public String getDescricao() {
        return descricao;
    }

    public String getDescricaoSemAcentuacao() {
        return StringUtilities.doRemoverAcentos(getDescricao());
    }

    public void setDescricao(String descricao) {
        this.descricao = descricao;
    }

    public static AmbienteEmissaoNotaFiscalEnum obterPorCodigo(Integer codigo) {
        for (AmbienteEmissaoNotaFiscalEnum amb : AmbienteEmissaoNotaFiscalEnum.values()) {
            if (amb.getCodigo().equals(codigo)) {
                return amb;
            }
        }
        return null;
    }
}
package negocio.comuns.arquitetura;

import annotations.arquitetura.ChavePrimaria;
import negocio.comuns.utilitarias.Calendario;

import java.util.Date;

public class LogApiVO extends SuperVO {

    @ChavePrimaria
    private Integer codigo;
    private String descricaoToken;
    private Date dataUso;
    private String ip;
    private String method;
    private String uri;
    private String params;

    public LogApiVO() {
        this.dataUso = Calendario.hoje();
    }

    @Override
    public Integer getCodigo() {
        return codigo;
    }

    @Override
    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public String getDescricaoToken() {
        return descricaoToken;
    }

    public void setDescricaoToken(String descricaoToken) {
        this.descricaoToken = descricaoToken;
    }

    public String getIp() {
        return ip;
    }

    public void setIp(String ip) {
        this.ip = ip;
    }

    public Date getDataUso() {
        return dataUso;
    }

    public void setDataUso(Date dataUso) {
        this.dataUso = dataUso;
    }

    public String getMethod() {
        return method;
    }

    public void setMethod(String method) {
        this.method = method;
    }

    public String getUri() {
        return uri;
    }

    public void setUri(String uri) {
        this.uri = uri;
    }

    public String getParams() {
        return params;
    }

    public void setParams(String params) {
        this.params = params;
    }
}

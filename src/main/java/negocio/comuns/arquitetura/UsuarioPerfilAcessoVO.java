/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package negocio.comuns.arquitetura;

import annotations.arquitetura.FKJson;
import negocio.comuns.basico.EmpresaVO;
import negocio.comuns.utilitarias.ConsistirException;

/**
 *
 * <AUTHOR>
 */
public class UsuarioPerfilAcessoVO extends SuperVO {

    protected Integer codigo;
    private Integer usuario;
    @FKJson
    private EmpresaVO empresa;
    @FKJson
    protected PerfilAcessoVO perfilAcesso;
    private Boolean verificado;

    /**
     * Construtor padrão da classe <code>EnderecoCobranca</code>.
     * Cria uma nova instância desta entidade, inicializando automaticamente seus atributos (Classe VO).
     */
    public UsuarioPerfilAcessoVO() {
        super();
        inicializarDados();
    }

    /**
     * Operação responsável por validar os dados de um objeto da classe <code>UsuarioPerfilAcessoVO</code>.
     * Todos os tipos de consistência de dados são e devem ser implementadas neste método.
     * São validações típicas: verificação de campos obrigatórios, verificação de valores válidos para os atributos.
     * @exception ConsistirExecption Se uma inconsistência for encontrada aumaticamente é gerada uma exceção descrevendo
     *                               o atributo e o erro ocorrido.
     */
    public static void validarDados(UsuarioPerfilAcessoVO obj) throws ConsistirException {
        if ((obj.getEmpresa() == null) || (obj.getEmpresa().getCodigo().intValue() == 0)) {
            throw new ConsistirException("O campo NOME da EMPRESA (Perfil Acesso Usuário) deve ser informado.");
        }
        if ((obj.getPerfilAcesso() == null) || (obj.getPerfilAcesso().getCodigo().intValue() == 0)) {
            throw new ConsistirException("O campo NOME do PERFIL ACESSO (Perfil Acesso Usuário) deve ser informado.");
        }
    }
    /**
     * Operação reponsável por inicializar os atributos da classe.
     */
    public void inicializarDados() {
        setCodigo(new Integer(0));
        setUsuario(new Integer(0));
        setPerfilAcesso(new PerfilAcessoVO());
    }

    public Integer getCodigo() {
        return (codigo);
    }

    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public PerfilAcessoVO getPerfilAcesso() {
        return perfilAcesso;
    }

    public void setPerfilAcesso(PerfilAcessoVO perfilAcesso) {
        this.perfilAcesso = perfilAcesso;
    }

    public EmpresaVO getEmpresa() {
        if (empresa == null) {
            empresa = new EmpresaVO();
        }
        return empresa;
    }

    public void setEmpresa(EmpresaVO empresa) {
        this.empresa = empresa;
    }

    public Integer getUsuario() {
        return usuario;
    }

    public void setUsuario(Integer usuario) {
        this.usuario = usuario;
    }

    public Boolean isVerificado() {
        if (verificado == null) {
            verificado = false;
        }
        return verificado;
    }

    public void setVerificado(Boolean verificado) {
        this.verificado = verificado;
    }

    @Override
    public int hashCode(){
        return this.empresa.getCodigo().hashCode();
    }

    @Override
    public boolean equals(Object obj){
        if ((obj == null) || (!(obj instanceof UsuarioPerfilAcessoVO))){
            return false;
        }
        return ((UsuarioPerfilAcessoVO) obj).getCodigo().equals(this.getCodigo());
    }


}

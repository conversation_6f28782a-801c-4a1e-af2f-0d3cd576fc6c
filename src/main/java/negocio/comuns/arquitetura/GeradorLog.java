/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package negocio.comuns.arquitetura;

import annotations.arquitetura.CampoCalendario;
import annotations.arquitetura.ChaveEstrangeira;
import annotations.arquitetura.ChavePrimaria;
import annotations.arquitetura.Lista;
import annotations.arquitetura.NaoControlarLogAlteracao;
import annotations.arquitetura.PassWord;
import br.com.pacto.priv.utils.Uteis;
import controle.arquitetura.security.LoginControle;
import negocio.comuns.utilitarias.Ordenacao;
import negocio.comuns.utilitarias.UteisValidacao;

import javax.faces.context.FacesContext;
import java.lang.reflect.Field;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 *
 * <AUTHOR>
 */
public class GeradorLog {

    protected List logDeAlteracoes;
    protected LogVO log;
    protected String nomeEntidade;
    protected String nomeEntidadeDescricao;
    protected String chavePrimaria;
    protected String responsavel;
    protected String chavePrimariaDoObjetoAnteriorChaveEstrangeira;
    protected String chavePrimariaDoObjetoAlteradoChaveEstrangeira;
    protected LoginControle loginControle;
    protected boolean novoObj;
    private UsuarioVO usuarioVO;

    public GeradorLog() {
        inicializarDados();
    }

    public void inicializarDados() {
        setLogDeAlteracoes(new ArrayList());
        setLog(new LogVO());
        setNomeEntidade("");
        setNomeEntidadeDescricao("");
        setChavePrimaria("");
        setResponsavel("");
        setChavePrimariaDoObjetoAlteradoChaveEstrangeira("");
        setChavePrimariaDoObjetoAnteriorChaveEstrangeira("");
        setUsuarioVO(null);
    }

    /**
     * Método responsável por fazer a comparação do objeto nas duas situações: Objeto Alterado e Objeto antes da alteração.
     * Apartir do valor do Field será gerado o LOG e nele em qual situação se encontra o Objeto.
     * Por exemplo: Se o field no objetoAlterado estiver com valor diferente do field no objetoAnterior.
     * Apartir dessa comparação será gerado um log com a operação ALTERAÇÃO.
     * Fields que utilizam annotation NaoControlarLogAlteracao e ChavePrimaria não são comparados.
     * Field com annotation NaoControlarLogAlteracao significa que para fields com essa annotation não serão gerados o LOG.
     * Field com annotation ChavePrimaria significa que para fields com essa annotation não serão gerados o LOG e tem a função
     * de informar qual Field é a ChavePrimaria do objeto.
     * A comparação dos Fields são feitos em 3 tipos, são eles:
     * Field com annotation ChaveEstrangeira
     * Field com annotation Lista
     * Field sem annotation
     * @param objetoAlterado Objeto após ser alterado.
     * @param objetoAnterior Objeto antes da alteração
     * @param nomeEntidadeMae Nome da entidade mãe. Esse variavel só é utilizada pro caso de Fields com annotation do tipo Lista.
     * @throws java.lang.ClassNotFoundException
     * @throws java.lang.IllegalArgumentException
     * @throws java.lang.IllegalAccessException
     */
    public void validarCamposDeclaradosComAnnotations(Object objetoAlterado, Object objetoAnterior, String nomeEntidadeMae, String nomeEntidadeMaeDescricao,
                                                      String chavePrimariaEntidadeSubordinada, boolean validarValorNull) throws ClassNotFoundException, IllegalArgumentException, IllegalAccessException, Exception {
        if (objetoAnterior == null) {
            return;
        }

        try {
            loginControle = (LoginControle) context().getExternalContext().getSessionMap().get("LoginControle");
        } catch (Exception ignored) {
        }

        adquirirNomeEntidadeChavePrimariaResponsavelAlteracao(objetoAlterado, chavePrimariaEntidadeSubordinada);


        Class superClasse = objetoAnterior.getClass().getSuperclass();
            if (objetoAlterado != null && (superClasse.getSimpleName().equals("SuperVO") || superClasse.getSimpleName().equals("SuperEmpresaVO"))) {
            novoObj = ((SuperVO) objetoAlterado).novoObj;
        }

        Field[] fieldsPrincipal = getFieldsPrincipais(objetoAlterado, objetoAnterior, nomeEntidadeMae, nomeEntidadeMaeDescricao, chavePrimariaEntidadeSubordinada, superClasse, validarValorNull);

        gerarLogAlteracoes(fieldsPrincipal, objetoAlterado, objetoAnterior, nomeEntidadeMae, nomeEntidadeMaeDescricao, chavePrimariaEntidadeSubordinada, validarValorNull);
    }

    private Field[] getFieldsPrincipais(Object objetoAlterado, Object objetoAnterior, String nomeEntidadeMae, String nomeEntidadeMaeDescricao,
                                        String chavePrimariaEntidadeSubordinada, Class superClasse, boolean validarValorNull) throws Exception {
        if (!superClasse.getSimpleName().equals("SuperVO")) {
            Field[] fieldsSuperClasse = superClasse.getDeclaredFields();
            gerarLogAlteracoes(fieldsSuperClasse, objetoAlterado, objetoAnterior, nomeEntidadeMae, nomeEntidadeMaeDescricao, chavePrimariaEntidadeSubordinada, validarValorNull);
            return objetoAnterior.getClass().getDeclaredFields();
        } else {
            return objetoAnterior.getClass().getDeclaredFields();
        }
    }

    public void validarLogVO(LogVO logVO) throws ClassNotFoundException, IllegalArgumentException, IllegalAccessException, Exception {
        if (logVO != null) {
            loginControle = (LoginControle) context().getExternalContext().getSessionMap().get("LoginControle");
            logVO.setResponsavelAlteracao(loginControle.getUsuarioLogado().getNome());
            logVO.setUserOAMD(loginControle.getUsuarioLogado().getUserOamd());
        }

        this.setLog(logVO);
    }

    @SuppressWarnings("unchecked")
    public void gerarLogAlteracoes(Field[] fields, Object objetoAlterado, Object objetoAnterior, String nomeEntidadeMae,
                                   String nomeEntidadeMaeDescricao, String chavePrimariaEntidadeSubordinada, boolean validarValorNull) throws IllegalArgumentException, IllegalAccessException, ClassNotFoundException, Exception {
        //percorre todos os campos do javabean
        for (Field field : fields) {
            //Verifica se existe marcao. No nosso caso é @NaoComparados, @ChavePrimaria e @Responsavel.
            //Não preciso comparar os campos ChavePrimaria e Responsavel porque os mesmos não serão inclusos no LOG
            //mais preciso dos campos para saber qual é a ChavePrimaria e o Responsavel pela alteração.

            if ((!field.isAnnotationPresent(NaoControlarLogAlteracao.class))
                    && (!field.isAnnotationPresent(ChavePrimaria.class))) {

//            	System.out.println("Campo Verificado: " + field.getName());            	

                //permite acesso aos dados do campo
                field.setAccessible(true);
                //verifica se o alterado é nulo, se for ele passa pro proximo field.
                if ((field.get(objetoAlterado)) == null && !validarValorNull) {
                    continue;
                }
                //comparação se o field do objeto principal é diferente do objeto secundario
                //caso seja será adicionado no LOG as informações do field.
                if (field.isAnnotationPresent(ChaveEstrangeira.class)) {
                    adquirirChavePrimariaDoObjetoDeChaveEstrangeira(objetoAlterado, objetoAnterior, nomeEntidadeMae, nomeEntidadeMaeDescricao, field);
                } else if (field.isAnnotationPresent(Lista.class)) {
                    adquirirAcoesRealizadasNaListaItems(objetoAlterado, objetoAnterior, field);
                } else {
//                	System.out.println("Campo Alt.: " + field.get(objetoAlterado) + " Campo Ant.: " + field.get(objetoAnterior));
                    if (((field.get(objetoAlterado) == null && field.get(objetoAnterior) != null) && validarValorNull) ||
                            (field.get(objetoAlterado) != null &&
                                    field.get(objetoAlterado).equals(field.get(objetoAnterior)) == false)) {

                        if (field.get(objetoAnterior) != null || !"".equals(field.get(objetoAlterado).toString())) {

                            String nomeCampo = "";
                            if (loginControle != null) {
                                nomeCampo = loginControle.getNomeEntidadeCamposLog("prt_" + getNomeEntidade() + "_" + field.getName());
                            }
                            getLog().setNomeEntidade(nomeEntidadeMae + getNomeEntidade());
                            getLog().setNomeEntidadeDescricao(nomeEntidadeMaeDescricao + getNomeEntidadeDescricao());
                            getLog().setChavePrimaria(getChavePrimaria());
                            getLog().setChavePrimariaEntidadeSubordinada(chavePrimariaEntidadeSubordinada);

                            if (!"".equals(nomeCampo)) {
                                getLog().setNomeCampo(nomeCampo);
                            } else {
                                getLog().setNomeCampo(field.getName());
                            }
                            if (field.get(objetoAnterior) == null) {
                                if(field.isAnnotationPresent(PassWord.class)){
                                    getLog().setValorCampoAnterior("Qtd digitos: 0");

                                }  else {
                                    getLog().setValorCampoAnterior("");
                                }
                            } else {
                                if(field.isAnnotationPresent(PassWord.class)){
                                    getLog().setValorCampoAnterior("Qtd digitos: "+field.get(objetoAnterior).toString().length());
                                } else {
                                    getLog().setValorCampoAnterior(field.get(objetoAnterior).toString());
                                }

                            }
                            if(field.isAnnotationPresent(CampoCalendario.class)) {
                                try {
                                    getLog().setValorCampoAnterior(Uteis.getDataComHHMM((Date) field.get(objetoAnterior)));
                                    getLog().setValorCampoAlterado(Uteis.getDataComHHMM((Date) field.get(objetoAlterado)));
                                }catch (Exception ex){
                                    getLog().setValorCampoAlterado(field.get(objetoAlterado).toString());
                                }
                            } else {

                                if (field.get(objetoAlterado) == null) {
                                    if(field.isAnnotationPresent(PassWord.class)){
                                        getLog().setValorCampoAlterado("Qtd digitos: 0");
                                    }  else {
                                        getLog().setValorCampoAlterado("");
                                    }
                                } else {
                                    if(field.isAnnotationPresent(PassWord.class)){
                                        getLog().setValorCampoAlterado("Qtd digitos: "+field.get(objetoAlterado).toString().length());
                                    }  else {
                                        getLog().setValorCampoAlterado(field.get(objetoAlterado).toString());
                                    }
                                }

                            }
                            getLog().setDataAlteracao(negocio.comuns.utilitarias.Calendario.hoje());

                            if (this.usuarioVO != null && !UteisValidacao.emptyNumber(this.usuarioVO.getCodigo())) {
                                getLog().setResponsavelAlteracao(this.usuarioVO.getNome());
                                getLog().setUserOAMD(this.usuarioVO.getUserOamd());
                            } else {
                                getLog().setResponsavelAlteracao(loginControle.getUsuarioLogado().getNome());
                                getLog().setUserOAMD(loginControle.getUsuarioLogado().getUserOamd());
                            }
                            if (novoObj) {
                                configOperacao("INCLUSÃO");
                            } else {
                                configOperacao("ALTERAÇÃO");
                            }
                            getLogDeAlteracoes().add(getLog());
                            setLog(new LogVO());
                        }
                    }
                }
            }
        }
    }

    /**
     * Autor: Pedro Y. Saito
     * Criado em 25/01/2011
     */
    private void configOperacao(String operacao) {
//		if (!"".equals(getLog().getNomeEntidade())){
//			getLog().setOperacao(operacao + " - " + getLog().getNomeEntidade());
//		} else {
        getLog().setOperacao(operacao);
//		}
    }

    /**
     * Método responsável por adquirir a Entidade, a Chave Primaria do Objeto e o Responsavel pela Alteração.
     * @param objetoSecundario 
     * @throws java.lang.IllegalArgumentException
     * @throws java.lang.IllegalAccessException
     */
    public void adquirirNomeEntidadeChavePrimariaResponsavelAlteracao(Object objetoSecundario, String chavePrimariaEntidadeSubordinada) throws Exception {
        Field[] fieldsSecundario = objetoSecundario.getClass().getDeclaredFields();

        String nomeEntidadeStr = objetoSecundario.getClass().getSimpleName();
        int tamanhoString = nomeEntidadeStr.length() - 2;
        setNomeEntidade(nomeEntidadeStr.substring(0, tamanhoString));
        if (loginControle != null) {
            nomeEntidadeStr = loginControle.getNomeEntidadeCamposLog("prt_" + getNomeEntidade() + "_tituloForm");
        }
        if (nomeEntidadeStr.equals("")) {
            nomeEntidadeStr = objetoSecundario.getClass().getSimpleName();
        }
        setNomeEntidadeDescricao(nomeEntidadeStr);
        //percorre todos os campos do javabean
        if (chavePrimariaEntidadeSubordinada.equals("")) {

            for (Field field : fieldsSecundario) {
                field.setAccessible(true);
                if (field.isAnnotationPresent(ChavePrimaria.class)) {
                    setChavePrimaria(field.get(objetoSecundario).toString());
                    break;
                }
            }
            if (getChavePrimaria().equals("")) {
                Field[] fieldsSecundarioClasseMae = objetoSecundario.getClass().getSuperclass().getDeclaredFields();
                for (Field field : fieldsSecundarioClasseMae) {
                    field.setAccessible(true);
                    if (field.isAnnotationPresent(ChavePrimaria.class)) {
                        setChavePrimaria(field.get(objetoSecundario).toString());
                        break;
                    }
                }
            }
        }
    }

    /**
     * Método reponsável por adquirir a Chave Primaria do objetoAlterado e objetoAnterior.
     * Com o valor da Chave Primaria sera comparado pra saber se o objeto foi ALTERADO.
     * @param objetoAlterado
     * @param objetoAnterior
     * @param field
     * @throws java.lang.IllegalArgumentException
     * @throws java.lang.IllegalAccessException
     */
    @SuppressWarnings("unchecked")
    public void adquirirChavePrimariaDoObjetoDeChaveEstrangeira(Object objetoAlterado, Object objetoAnterior, String nomeEntidadeMae, String nomeEntidadeMaeDescricao, Field field) throws IllegalArgumentException, IllegalAccessException, Exception {

        adquirirNomeEntidadeChavePrimariaResponsavelAlteracao(objetoAnterior, "CHAVEESTRANGEIRA");

        setChavePrimariaDoObjetoAnteriorChaveEstrangeira("");
        setChavePrimariaDoObjetoAlteradoChaveEstrangeira("");
        Object valorObjetoAlterado = field.get(objetoAlterado);
        Object valorObjetoAnterior = field.get(objetoAnterior);

        //O valor 1 é referente ao PRIMEIRO OBJETO.
        //O valor 2 é referente ao SEGUNDO OBJETO.
        obterChavePrimariaObjeto(valorObjetoAlterado, "1");
        if (valorObjetoAnterior != null) {
            obterChavePrimariaObjeto(valorObjetoAnterior, "2");
        }

        if (!getChavePrimariaDoObjetoAnteriorChaveEstrangeira().equals(getChavePrimariaDoObjetoAlteradoChaveEstrangeira())) {

            if (!"".equals(getChavePrimariaDoObjetoAnteriorChaveEstrangeira())) {
                String nomeCampo = "";
                if (loginControle != null) {
                    nomeCampo = loginControle.getNomeEntidadeCamposLog("prt_" + getNomeEntidade() + "_" + field.getName());
                }

                getLog().setNomeEntidade(nomeEntidadeMae + getNomeEntidade());
                getLog().setNomeEntidadeDescricao(nomeEntidadeMaeDescricao + getNomeEntidadeDescricao());
                getLog().setChavePrimaria(getChavePrimaria());
                getLog().setChavePrimariaEntidadeSubordinada("");
                if (!"".equals(nomeCampo)) {
                    getLog().setNomeCampo(nomeCampo);
                } else {
                    getLog().setNomeCampo(field.getName());
                }
                getLog().setValorCampoAnterior(getChavePrimariaDoObjetoAnteriorChaveEstrangeira());
                getLog().setValorCampoAlterado(getChavePrimariaDoObjetoAlteradoChaveEstrangeira());
                getLog().setDataAlteracao(negocio.comuns.utilitarias.Calendario.hoje());

                if (this.usuarioVO != null && !UteisValidacao.emptyNumber(this.usuarioVO.getCodigo())) {
                    getLog().setResponsavelAlteracao(this.usuarioVO.getNome());
                    getLog().setUserOAMD(this.usuarioVO.getUserOamd());
                } else {
                    getLog().setResponsavelAlteracao(loginControle.getUsuarioLogado().getNome());
                    getLog().setUserOAMD(loginControle.getUsuarioLogado().getUserOamd());
                }
                if (novoObj) {
                    configOperacao("INCLUSÃO");
                } else {
                    configOperacao("ALTERAÇÃO");
                }
                getLogDeAlteracoes().add(getLog());
                setLog(new LogVO());
            }

        }

    }

    /**
     * Método responsável por controlar e gerar LOGs das ações que foram realizadas na lista subordinada.
     * Ações: Inclusão, Alteração e Exclusão.
     * @param objetoAlterado
     * @param objetoAnterior
     * @param field
     * @throws java.lang.IllegalArgumentException
     * @throws java.lang.IllegalAccessException
     * @throws java.lang.ClassNotFoundException
     */
    @SuppressWarnings("unchecked")
    private void adquirirAcoesRealizadasNaListaItems(Object objetoAlterado, Object objetoAnterior, Field field) throws Exception {
        String nomeCampoLista = "".equals(field.getAnnotation(Lista.class).nome().trim()) ? "Campo(s)" : field.getAnnotation(Lista.class).nome();

        List listaAnterior = field.get(objetoAnterior) == null ? new ArrayList() : new ArrayList((ArrayList) field.get(objetoAnterior));
        List listaAlterado = field.get(objetoAlterado) == null ? new ArrayList() : new ArrayList((ArrayList) field.get(objetoAlterado));

        String nomeEntidadeMae = objetoAnterior.getClass().getSimpleName();
        int tamanhoString = nomeEntidadeMae.length() - 2;
        nomeEntidadeMae = nomeEntidadeMae.substring(0, tamanhoString) + " - ";
        String nomeEntidadeMaeDescricao = "";
        if (loginControle != null ) {
            nomeEntidadeMaeDescricao = loginControle.getNomeEntidadeCamposLog("prt_" + nomeEntidadeMae.substring(0, tamanhoString) + "_tituloForm") + " - ";
        }

        listaAnterior = Ordenacao.ordenarLista(listaAnterior, "codigo");
        listaAlterado = Ordenacao.ordenarLista(listaAlterado, "codigo");
        validarCamposListas(listaAnterior, listaAlterado, "EXCLUSÃO", nomeEntidadeMae, nomeEntidadeMaeDescricao, nomeCampoLista);
        validarCamposListas(listaAlterado, listaAnterior, "ALTERAÇÃO", nomeEntidadeMae, nomeEntidadeMaeDescricao, nomeCampoLista);
    }

    @SuppressWarnings("unchecked")
    private void validarCamposListas(List listaAlterado, List listaAnterior, String tipoOperacao,
                                     String nomeEntidadeMae, String nomeEntidadeMaeDescricao,
                                     String nomeCampoLista) throws  Exception {
        String nomeEntidadeLista = "";
        int numeroObjetosIguais = 0;

        for (Object aListaAlterado : listaAlterado) {
            numeroObjetosIguais = 0;
            Object valorPrimeiroObjeto;
            valorPrimeiroObjeto = aListaAlterado;

            nomeEntidadeLista = valorPrimeiroObjeto.getClass().getSimpleName();
            int tamanhoString = nomeEntidadeLista.length() - 2;
            nomeEntidadeLista = nomeEntidadeLista.substring(0, tamanhoString);
            String nomeEntidadeListaDescricao = "";
            if (loginControle != null) {
                nomeEntidadeListaDescricao = loginControle.getNomeEntidadeCamposLog("prt_" + nomeEntidadeLista + "_tituloForm");
            }

            nomeEntidadeLista = nomeEntidadeMae + nomeEntidadeLista;

            setChavePrimariaDoObjetoAlteradoChaveEstrangeira("");
            obterChavePrimariaObjeto(valorPrimeiroObjeto, "1");

            for (Object valorSegundoObjeto : listaAnterior) {
                setChavePrimariaDoObjetoAnteriorChaveEstrangeira("");
                obterChavePrimariaObjeto(valorSegundoObjeto, "2");
                if ((getChavePrimariaDoObjetoAnteriorChaveEstrangeira().equals(getChavePrimariaDoObjetoAlteradoChaveEstrangeira()))) {
                    if (tipoOperacao.equals("ALTERAÇÃO")) {
                        validarCamposDeclaradosComAnnotations(valorPrimeiroObjeto, valorSegundoObjeto, nomeEntidadeMae, nomeEntidadeMaeDescricao, getChavePrimariaDoObjetoAlteradoChaveEstrangeira(), false);
                        numeroObjetosIguais++;
                    } else {
                        numeroObjetosIguais++;
                    }
                }
            }
            if (numeroObjetosIguais == 0) {
                if (tipoOperacao.equals("ALTERAÇÃO")) {
                    adicionarListaLogDeAcordoComOperacao(valorPrimeiroObjeto, "INCLUSÃO", nomeEntidadeLista, nomeEntidadeListaDescricao, nomeCampoLista);
                } else {
                    adicionarListaLogDeAcordoComOperacao(valorPrimeiroObjeto, "EXCLUSÃO", nomeEntidadeLista, nomeEntidadeListaDescricao, nomeCampoLista);
                }
            }
        }
    }

    @SuppressWarnings("unchecked")
    public void adicionarListaLogDeAcordoComOperacao(Object valorPrimeiroObjeto, String tipoOperacao,
                                                     String nomeEntidadeLista, String nomeEntidadeListaDescricao,
                                                     String nomeCampoLista) throws Exception {
        getLog().setNomeEntidade(nomeEntidadeLista);
        getLog().setNomeEntidadeDescricao(nomeEntidadeLista + "/" + nomeEntidadeListaDescricao);
        getLog().setChavePrimaria(getChavePrimaria());
        getLog().setChavePrimariaEntidadeSubordinada(getChavePrimariaDoObjetoAlteradoChaveEstrangeira());
        getLog().setNomeCampo(nomeCampoLista);
        getLog().setValorCampoAnterior("");
        Field[] fields = valorPrimeiroObjeto.getClass().getDeclaredFields();
        for (Field field : fields) {
            field.setAccessible(true);
            int tamanhoStrig = nomeEntidadeLista.indexOf("-") + 2;
            String nome = nomeEntidadeLista.substring(tamanhoStrig, nomeEntidadeLista.length());
            String nomeCampo = "";
            if (loginControle != null) {
                nomeCampo = loginControle.getNomeEntidadeCamposLog("prt_" + nome + "_" + field.getName());
            }
            if ((field.isAnnotationPresent(NaoControlarLogAlteracao.class))
                    || (field.isAnnotationPresent(ChavePrimaria.class))
                    || (field.isAnnotationPresent(Lista.class))) {
                continue;
            }

            if (field.isAnnotationPresent(ChaveEstrangeira.class)) {

                if (getChavePrimariaDoObjetoAlteradoChaveEstrangeira() != null) {
                    setChavePrimariaDoObjetoAlteradoChaveEstrangeira("");
                    obterChavePrimariaObjeto(field.get(valorPrimeiroObjeto), "1");

                    if (!"".equals(getLog().getValorCampoAlterado())) {
                        getLog().setValorCampoAlterado(getLog().getValorCampoAlterado() + "\n");
                        analisarDescricaoExclusao(tipoOperacao, nomeCampo, field, valorPrimeiroObjeto);
                    }
                    getLog().setValorCampoAlterado(getLog().getValorCampoAlterado() + nomeCampo + " " + getChavePrimariaDoObjetoAlteradoChaveEstrangeira());
                    analisarDescricaoExclusao(tipoOperacao, nomeCampo, field, valorPrimeiroObjeto);
                }
            } else {
                if (field.get(valorPrimeiroObjeto) != null) {
                    if (!"".equals(getLog().getValorCampoAlterado())) {
                        getLog().setValorCampoAlterado(getLog().getValorCampoAlterado() + "\n");
                        analisarDescricaoExclusao(tipoOperacao, nomeCampo, field, valorPrimeiroObjeto);
                    }

                    getLog().setValorCampoAlterado(getLog().getValorCampoAlterado() + nomeCampo + " " + field.get(valorPrimeiroObjeto));
                    analisarDescricaoExclusao(tipoOperacao, nomeCampo, field, valorPrimeiroObjeto);
                }
            }
        }
        getLog().setDataAlteracao(negocio.comuns.utilitarias.Calendario.hoje());

        if (this.usuarioVO != null && !UteisValidacao.emptyNumber(this.usuarioVO.getCodigo())) {
            getLog().setResponsavelAlteracao(this.usuarioVO.getNome());
            getLog().setUserOAMD(this.usuarioVO.getUserOamd());
        } else {
            getLog().setResponsavelAlteracao(loginControle.getUsuarioLogado().getNome());
            getLog().setUserOAMD(loginControle.getUsuarioLogado().getUserOamd());
        }
        configOperacao(tipoOperacao);
        getLogDeAlteracoes().add(getLog());
        setLog(new LogVO());
    }

    /**
     * Permite que o valor anterior seja o objeto e o valor alterado seja vazio, pois é exclusão.
     * @param tipoOperacao
     * @param nomeCampo
     * @param field
     * @param valorPrimeiroObjeto
     * @throws IllegalArgumentException
     * @throws IllegalAccessException
     */
    public void analisarDescricaoExclusao(String tipoOperacao, String nomeCampo, Field field, Object valorPrimeiroObjeto) throws IllegalArgumentException, IllegalAccessException {
        if (tipoOperacao.equals("EXCLUSÃO")) {
            if (field.isAnnotationPresent(ChaveEstrangeira.class)) {
                getLog().setValorCampoAnterior(nomeCampo + " " + getChavePrimariaDoObjetoAlteradoChaveEstrangeira());
            } else {
                getLog().setValorCampoAnterior(nomeCampo + " " + field.get(valorPrimeiroObjeto));
            }
        }
    }

//    public void obterNomesCamposChaveEstrangeira(Field[] fields, Object valorPrimeiroObjeto) throws IllegalArgumentException, IllegalAccessException {
//        for (Field fieldChaveEstrangeira : fields) {
//            fieldChaveEstrangeira.setAccessible(true);
//            if (fieldChaveEstrangeira.isAnnotationPresent(ChavePrimaria.class)) {
//                int tamanhoString = valorPrimeiroObjeto.getClass().getSimpleName().length() - 2;
//                String nomeCampo = valorPrimeiroObjeto.getClass().getSimpleName().substring(0, tamanhoString);
//                getLog().setValorCampoAnterior(getLog().getValorCampoAnterior() + "Campo - " + nomeCampo + " : " + fieldChaveEstrangeira.get(valorPrimeiroObjeto) + "\n");
//                continue;
//            }
//            if (fieldChaveEstrangeira.isAnnotationPresent(ChaveEstrangeira.class)) {
//                Field[] fieldss = fieldChaveEstrangeira.get(valorPrimeiroObjeto).getClass().getDeclaredFields();
//                Object objetoChaveEstrangeira = fieldChaveEstrangeira.get(valorPrimeiroObjeto);
//                obterNomesCamposChaveEstrangeira(fieldss, objetoChaveEstrangeira);
//                continue;
//            }
//            if (fieldChaveEstrangeira.isAnnotationPresent(Lista.class)) {
//
//                continue;
//            }
//        }
//    }
    public void obterChavePrimariaObjeto(Object valorObjeto, String tipoObjeto) throws IllegalArgumentException, IllegalArgumentException, IllegalArgumentException, IllegalAccessException {
        Field[] fieldsBeanPrincipal = valorObjeto.getClass().getDeclaredFields();
        if (!valorObjeto.getClass().getSuperclass().getSimpleName().equals("SuperVO")) {
            fieldsBeanPrincipal = valorObjeto.getClass().getSuperclass().getDeclaredFields();
        }
        for (Field fieldPrincipal : fieldsBeanPrincipal) {
            fieldPrincipal.setAccessible(true);
            if (fieldPrincipal.isAnnotationPresent(ChavePrimaria.class)) {
                if ((fieldPrincipal.get(valorObjeto)) == null) {
                    break;
                }
                if (tipoObjeto.equals("1")) {
                    setChavePrimariaDoObjetoAlteradoChaveEstrangeira(fieldPrincipal.get(valorObjeto).toString());
                } else {
                    setChavePrimariaDoObjetoAnteriorChaveEstrangeira(fieldPrincipal.get(valorObjeto).toString());
                }
                break;
            }
        }

    }

    public void validarCadastrosExclusao(Object objetoExcluido) throws Exception {
        String nomeEntidadeLista = objetoExcluido.getClass().getSimpleName();
        String nomeEntidadeListaDescricao = "";
        try {
            loginControle = (LoginControle) context().getExternalContext().getSessionMap().get("LoginControle");
            nomeEntidadeListaDescricao = loginControle.getNomeEntidadeCamposLog("prt_" + nomeEntidadeLista + "_tituloForm");
        } catch (Exception ignored) {
        }
        adicionarListaLogDeAcordoComOperacao(objetoExcluido, "EXCLUSÃO", nomeEntidadeLista, nomeEntidadeListaDescricao, "Campo(s)");

//        adquirirNomeEntidadeChavePrimariaResponsavelAlteracao(objetoExcluido, "");
//
//        getLog().setNomeEntidade(getNomeEntidade());
//        getLog().setChavePrimaria(getChavePrimaria());
//        getLog().setChavePrimariaEntidadeSubordinada("");
//        getLog().setNomeCampo("");
//        getLog().setValorCampoAnterior("");
//        getLog().setValorCampoAlterado("");
//        getLog().setDataAlteracao(negocio.comuns.utilitarias.Calendario.hoje());
//        getLog().setResponsavelAlteracao(loginControle.getUsuarioLogado().getNome());
//        getLog().setOperacao("EXCLUSÃO");
//        getLogDeAlteracoes().add(getLog());
//        setLog(new LogVO());
    }

    protected FacesContext context() {
        return (FacesContext.getCurrentInstance());
    }

    /**
     * @return the logDeAlteracoes
     */
    public List getLogDeAlteracoes() {
        return logDeAlteracoes;
    }

    /**
     * @param logDeAlteracoes the logDeAlteracoes to set
     */
    public void setLogDeAlteracoes(List logDeAlteracoes) {
        this.logDeAlteracoes = logDeAlteracoes;
    }

    /**
     * @return the log
     */
    public LogVO getLog() {
        return log;
    }

    /**
     * @param log the log to set
     */
    public void setLog(LogVO log) {
        this.log = log;
    }

    /**
     * @return the nomeEntidade
     */
    public String getNomeEntidade() {
        if (nomeEntidade == null) {
            nomeEntidade = "";
        }
        return nomeEntidade;
    }

    /**
     * @param nomeEntidade the nomeEntidade to set
     */
    public void setNomeEntidade(String nomeEntidade) {
        this.nomeEntidade = nomeEntidade;
    }

    /**
     * @return the chavePrimaria
     */
    public String getChavePrimaria() {
        if (chavePrimaria == null) {
            chavePrimaria = "";
        }
        return chavePrimaria;
    }

    /**
     * @param chavePrimaria the chavePrimaria to set
     */
    public void setChavePrimaria(String chavePrimaria) {
        this.chavePrimaria = chavePrimaria;
    }

    /**
     * @return the responsavel
     */
    public String getResponsavel() {
        if (responsavel == null) {
            responsavel = "";
        }
        return responsavel;
    }

    /**
     * @param responsavel the responsavel to set
     */
    public void setResponsavel(String responsavel) {
        this.responsavel = responsavel;
    }

    /**
     * @return the chavePrimariaDoObjetoAnteriorChaveEstrangeira
     */
    public String getChavePrimariaDoObjetoAnteriorChaveEstrangeira() {
        if (chavePrimariaDoObjetoAnteriorChaveEstrangeira == null) {
            chavePrimariaDoObjetoAnteriorChaveEstrangeira = "";
        }
        return chavePrimariaDoObjetoAnteriorChaveEstrangeira;
    }

    /**
     * @param chavePrimariaDoObjetoAnteriorChaveEstrangeira the chavePrimariaDoObjetoAnteriorChaveEstrangeira to set
     */
    public void setChavePrimariaDoObjetoAnteriorChaveEstrangeira(String chavePrimariaDoObjetoAnteriorChaveEstrangeira) {
        this.chavePrimariaDoObjetoAnteriorChaveEstrangeira = chavePrimariaDoObjetoAnteriorChaveEstrangeira;
    }

    /**
     * @return the chavePrimariaDoObjetoAlteradoChaveEstrangeira
     */
    public String getChavePrimariaDoObjetoAlteradoChaveEstrangeira() {
        if (chavePrimariaDoObjetoAlteradoChaveEstrangeira == null) {
            chavePrimariaDoObjetoAlteradoChaveEstrangeira = "";
        }
        return chavePrimariaDoObjetoAlteradoChaveEstrangeira;
    }

    public void setNovoObj(boolean novoObj) {
        this.novoObj = novoObj;
    }

    /**
     * @param chavePrimariaDoObjetoAlteradoChaveEstrangeira the chavePrimariaDoObjetoAlteradoChaveEstrangeira to set
     */
    public void setChavePrimariaDoObjetoAlteradoChaveEstrangeira(String chavePrimariaDoObjetoAlteradoChaveEstrangeira) {
        this.chavePrimariaDoObjetoAlteradoChaveEstrangeira = chavePrimariaDoObjetoAlteradoChaveEstrangeira;
    }

    /**
     * @return the nomeEntidadeAuxiliar
     */
    public String getNomeEntidadeDescricao() {
        return nomeEntidadeDescricao;
    }

    public void setNomeEntidadeDescricao(String nomeEntidadeDescricao) {
        this.nomeEntidadeDescricao = nomeEntidadeDescricao;
    }

    public void setUsuarioVO(UsuarioVO usuarioVO) {
        this.usuarioVO = usuarioVO;
    }
//    private Integer identificarEmpresa() throws Exception {
//
//        if (loginControle.getEmpresaLogado().getCodigo().intValue() != 0) {
//            getLog().setNomeEmpresa(loginControle.getEmpresaLogado().getNome());
//            return loginControle.getEmpresaLogado().getCodigo().intValue();
//        } else {
//            getLog().setNomeEmpresa("Todas Empresas");
//            return 0;
//        }
//    }
//
//    private String identificarUsuario() throws Exception {
//        if (loginControle.getUsuarioLogado().getAdministrador()) {
//            return loginControle.getUsuarioLogado().getNome();
//        } else if (loginControle.getUsuarioLogado().getClienteVO().getCodigo().intValue() != 0) {
//            return loginControle.getUsuarioLogado().getNome() + " - Empresa: " + loginControle.getUsuarioLogado().getClienteVO().getEmpresa().getNome();
//        } else {
//            return loginControle.getUsuarioLogado().getNome() + " - Empresa: " + loginControle.getUsuarioLogado().getColaboradorVO().getEmpresa().getNome();
//        }
//    }
}

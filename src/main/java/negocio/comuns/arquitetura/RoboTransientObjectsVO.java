/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package negocio.comuns.arquitetura;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import negocio.comuns.basico.ClienteVO;
import negocio.comuns.contrato.ContratoVO;
import negocio.comuns.utilitarias.Calendario;
import negocio.comuns.utilitarias.Uteis;

/**
 *
 * <AUTHOR>
 */
public class RoboTransientObjectsVO extends SuperVO {

    private List<ClienteVO> listaClientes = new ArrayList();
    private List<ContratoVO> listaContratos = new ArrayList();
    private Date dia = Calendario.hoje();

    public List<ClienteVO> getListaClientes() {
        return listaClientes;
    }

    public void setListaClientes(List<ClienteVO> listaClientes) {
        this.listaClientes = listaClientes;
    }

    public List<ContratoVO> getListaContratos() {
        return listaContratos;
    }

    public void setListaContratos(List<ContratoVO> listaContratos) {
        this.listaContratos = listaContratos;
    }

    public Date getDia() {
        return dia;
    }

    public void setDia(Date dia) {
        this.dia = dia;
    }

    public void validar() throws Exception {
        if (listaClientes.isEmpty() && listaContratos.isEmpty()) {
            throw new Exception("É necessário informar alguma Matrícula de cliente ou algum Contrato separados por vírgula válidos, respectivamente.");
        }
        if (dia == null) {
            throw new Exception("Data do dia inválida!");
        }
    }

    public void atualizarListas() throws Exception {
        for (int i = 0; i < listaClientes.size(); i++) {
            ClienteVO cli = getFacade().getCliente().consultarPorChavePrimaria(listaClientes.get(i).getCodigo(),
                    Uteis.NIVELMONTARDADOS_SITUACAOCLIENTESINTETICODW);
            listaClientes.set(i, cli);

        }

        for (int i = 0; i < listaContratos.size(); i++) {
            ContratoVO cont = getFacade().getContrato().consultarPorChavePrimaria(listaContratos.get(i).getCodigo(),
                    Uteis.NIVELMONTARDADOS_ROBO);
            listaContratos.set(i, cont);
        }
    }
}

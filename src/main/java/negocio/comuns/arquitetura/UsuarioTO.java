/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package negocio.comuns.arquitetura;

import org.json.JSONObject;
import servicos.integracao.adm.beans.EmpresaWS;

import java.util.ArrayList;
import java.util.List;

/**
 *
 * <AUTHOR>
 */
public class UsuarioTO extends SuperTO {

    private String nome;
    private String userName;
    private Integer codigo;
    private Integer codigoPessoa;
    private boolean usuarioTreino;
    private List<EmpresaWS> empresas;
    private List<EmpresaWS> empresasPermissaoModuloNotas = new ArrayList<EmpresaWS>();
    private String urlTreino;
    private String mensagem;
    private boolean expirado = false;
    private boolean foraDoHorario = false;
    private String urlFotosNuvem;
    private String typeMidiasService;
    private String urlFoto;
    private boolean validarUserOamd = false;
    private EmpresaWS empresaDefault;
    private String diaExpiracaoAcesso;
    private boolean acessoTeste;
    private boolean administrador = false;
    private boolean permissaoAlterarRPS = false;
    private String usuarioMovelTreino;
    private String credecialTreino;
    private Integer colaboradorId;
    //Campos para relatório apenas;
    private String perfilAcesso;
    private String nomeEmpresa;
    private String tipoPerfilAcesso;

    public UsuarioTO() {
    }

    public UsuarioTO(String nome, String userName, Integer codigo, Integer codigoPessoa,
            boolean usuarioTreino, List<EmpresaWS> empresas, String urlTreino,
            String mensagem, String urlFotosNuvem, String typeMidiasService,
            String urlFoto) {
        this.nome = nome;
        this.userName = userName;
        this.codigo = codigo;
        this.codigoPessoa = codigoPessoa;
        this.usuarioTreino = usuarioTreino;
        this.empresas = empresas;
        this.urlTreino = urlTreino;
        this.mensagem = mensagem;
        this.urlFotosNuvem = urlFotosNuvem;
        this.typeMidiasService = typeMidiasService;
        this.urlFoto = urlFoto;
    }

    public String toJSON(){
        JSONObject json = new JSONObject();
        json.put("codigo", this.codigo);
        json.put("username", this.userName);
        json.put("colaboradorId", this.colaboradorId);
        json.put("nome", this.nome);
        json.put("usaTreino", this.usuarioTreino);
        json.put("administrador", this.administrador);
        return json.toString();
    }

    public String getNome() {
        return nome;
    }

    public void setNome(String nome) {
        this.nome = nome;
    }

    public String getUserName() {
        return userName;
    }

    public void setUserName(String userName) {
        this.userName = userName;
    }

    public Integer getCodigo() {
        return codigo;
    }

    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public Integer getCodigoPessoa() {
        return codigoPessoa;
    }

    public void setCodigoPessoa(Integer codigoPessoa) {
        this.codigoPessoa = codigoPessoa;
    }

    public boolean isUsuarioTreino() {
        return usuarioTreino;
    }

    public void setUsuarioTreino(boolean usuarioTreino) {
        this.usuarioTreino = usuarioTreino;
    }

    public List<EmpresaWS> getEmpresas() {
        if (empresas == null) {
            empresas = new ArrayList<EmpresaWS>();
        }
        return empresas;
    }

    public void setEmpresas(List<EmpresaWS> empresas) {
        this.empresas = empresas;
    }

    public String getUrlTreino() {
        return urlTreino;
    }

    public void setUrlTreino(String urlTreino) {
        this.urlTreino = urlTreino;
    }

    public String getMensagem() {
        return mensagem;
    }

    public void setMensagem(String mensagem) {
        this.mensagem = mensagem;
    }

    public boolean isExpirado() {
        return expirado;
    }

    public void setExpirado(boolean expirado) {
        this.expirado = expirado;
    }

    public boolean isForaDoHorario() {
        return foraDoHorario;
    }

    public void setForaDoHorario(boolean foraDoHorario) {
        this.foraDoHorario = foraDoHorario;
    }

    public String getUrlFotosNuvem() {
        return urlFotosNuvem;
    }

    public void setUrlFotosNuvem(String urlFotosNuvem) {
        this.urlFotosNuvem = urlFotosNuvem;
    }

    public String getTypeMidiasService() {
        return typeMidiasService;
    }

    public void setTypeMidiasService(String typeMidiasService) {
        this.typeMidiasService = typeMidiasService;
    }

    public String getUrlFoto() {
        return urlFoto;
    }

    public void setUrlFoto(String urlFoto) {
        this.urlFoto = urlFoto;
    }

    public boolean isValidarUserOamd() {
        return validarUserOamd;
    }

    public void setValidarUserOamd(boolean validarUserOamd) {
        this.validarUserOamd = validarUserOamd;
    }

    @Override
    public String toString() {
        return "UsuarioTO{" + "nome=" + nome + ", userName=" + userName + ", codigo=" + codigo + ", codigoPessoa=" + codigoPessoa + '}';
    }

    public EmpresaWS getEmpresaDefault() {
        return empresaDefault;
    }

    public void setEmpresaDefault(EmpresaWS empresaDefault) {
        this.empresaDefault = empresaDefault;
    }

    public String getDiaExpiracaoAcesso() {
        return diaExpiracaoAcesso;
    }

    public void setDiaExpiracaoAcesso(String diaExpiracaoAcesso) {
        this.diaExpiracaoAcesso = diaExpiracaoAcesso;
    }

    public boolean isAcessoTeste() {
        return acessoTeste;
    }

    public void setAcessoTeste(boolean acessoTeste) {
        this.acessoTeste = acessoTeste;
    }

    public boolean isAdministrador() {
        return administrador;
    }

    public void setAdministrador(boolean administrador) {
        this.administrador = administrador;
    }

    public boolean isPermissaoAlterarRPS() {
        return permissaoAlterarRPS;
    }

    public void setPermissaoAlterarRPS(boolean permissaoAlterarRPS) {
        this.permissaoAlterarRPS = permissaoAlterarRPS;
    }

    public List<EmpresaWS> getEmpresasPermissaoModuloNotas() {
        return empresasPermissaoModuloNotas;
    }

    public void setEmpresasPermissaoModuloNotas(List<EmpresaWS> empresasPermissaoModuloNotas) {
        this.empresasPermissaoModuloNotas = empresasPermissaoModuloNotas;
    }

    public String getUsuarioMovelTreino() {
        return usuarioMovelTreino;
    }

    public void setUsuarioMovelTreino(String usuarioMovelTreino) {
        this.usuarioMovelTreino = usuarioMovelTreino;
    }

    public String getCredecialTreino() {
        return credecialTreino;
    }

    public void setCredecialTreino(String credecialTreino) {
        this.credecialTreino = credecialTreino;
    }

    public Integer getColaboradorId() {
        return colaboradorId;
    }

    public void setColaboradorId(Integer colaboradorId) {
        this.colaboradorId = colaboradorId;
    }

    public String getPerfilAcesso() {
        return perfilAcesso;
    }

    public void setPerfilAcesso(String perfilAcesso) {
        this.perfilAcesso = perfilAcesso;
    }

    public String getNomeEmpresa() {
        return nomeEmpresa;
    }

    public void setNomeEmpresa(String nomeEmpresa) {
        this.nomeEmpresa = nomeEmpresa;
    }

    public String getTipoPerfilAcesso() {
        if (this.tipoPerfilAcesso == null) {
            this.tipoPerfilAcesso = "";
        }
        return this.tipoPerfilAcesso;
    }

    public void setTipoPerfilAcesso(final String tipoPerfilAcesso) {
        this.tipoPerfilAcesso = tipoPerfilAcesso;
    }
}

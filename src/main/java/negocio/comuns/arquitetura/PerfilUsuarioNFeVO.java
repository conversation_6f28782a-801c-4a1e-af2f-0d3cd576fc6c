package negocio.comuns.arquitetura;

import negocio.comuns.nfe.EmpresaNFeVO;
import negocio.comuns.utilitarias.ConsistirException;

public class PerfilUsuarioNFeVO extends PerfilAcessoVO {

    private String nomePerfilUsuario = "";
    private boolean permiteCRUD_Usuario = false;
    private boolean permiteIncluirEmpresa = false;
    private boolean permiteAlterarEmpresa = false;
    private boolean permiteAlterarBasicoEmpresa = false;
    private boolean permiteCancelarNota = false;
    private boolean permiteCRUD_Perfil = false;
    private EmpresaNFeVO empresa = new EmpresaNFeVO();
    private boolean permiteAlterarNumeroRPS = false;

    public PerfilUsuarioNFeVO() {
        super();
    }

    public static void validarDados(PerfilUsuarioNFeVO obj) throws ConsistirException {
        if (!obj.getValidarDados()) {
            return;
        }
        if (obj.getNomePerfilUsuario().equals("")) {
            throw new ConsistirException("O campo NOME (Perfil de Usuário) deve ser informado.");
        }

        if (obj.getEmpresa() == null
                || obj.getEmpresa().getId_Empresa() == null
                || obj.getEmpresa().getId_Empresa() == 0) {
            throw new ConsistirException("A empresa não está sendo informada.");
        }
    }

    public String getNomePerfilUsuario() {
        return nomePerfilUsuario;
    }

    public void setNomePerfilUsuario(String nomePerfilUsuario) {
        this.nomePerfilUsuario = nomePerfilUsuario;
    }

    public boolean isPermiteCRUD_Usuario() {
        return permiteCRUD_Usuario;
    }

    public void setPermiteCRUD_Usuario(boolean permiteCRUD_Usuario) {
        this.permiteCRUD_Usuario = permiteCRUD_Usuario;
    }

    public boolean isPermiteCancelarNota() {
        return permiteCancelarNota;
    }

    public void setPermiteCancelarNota(boolean permiteCancelarNota) {
        this.permiteCancelarNota = permiteCancelarNota;
    }

    public EmpresaNFeVO getEmpresa() {
        return empresa;
    }

    public void setEmpresa(EmpresaNFeVO empresa) {
        this.empresa = empresa;
    }

    public boolean isPermiteIncluirEmpresa() {
        return permiteIncluirEmpresa;
    }

    public void setPermiteIncluirEmpresa(boolean permiteIncluirEmpresa) {
        this.permiteIncluirEmpresa = permiteIncluirEmpresa;
    }

    public boolean isPermiteAlterarEmpresa() {
        return permiteAlterarEmpresa;
    }

    public void setPermiteAlterarEmpresa(boolean permiteAlterarEmpresa) {
        this.permiteAlterarEmpresa = permiteAlterarEmpresa;
    }

    public boolean isPermiteAlterarBasicoEmpresa() {
        return permiteAlterarBasicoEmpresa;
    }

    public void setPermiteAlterarBasicoEmpresa(boolean permiteAlterarBasicoEmpresa) {
        this.permiteAlterarBasicoEmpresa = permiteAlterarBasicoEmpresa;
    }

    public boolean isPermiteCRUD_Perfil() {
        return permiteCRUD_Perfil;
    }

    public void setPermiteCRUD_Perfil(boolean permiteCRUD_Perfil) {
        this.permiteCRUD_Perfil = permiteCRUD_Perfil;
    }

    public boolean isPermiteAlterarNumeroRPS() {
        return permiteAlterarNumeroRPS;
    }

    public void setPermiteAlterarNumeroRPS(boolean permiteAlterarNumeroRPS) {
        this.permiteAlterarNumeroRPS = permiteAlterarNumeroRPS;
    }
}
package negocio.comuns.arquitetura.response;

import negocio.comuns.arquitetura.PermissaoVO;

import java.util.Objects;

public class FuncionalidadesDTO {

    private String nome;
    private Boolean possuiFuncionalidade;
    private String referenciaFuncionalidade;

    public FuncionalidadesDTO(PermissaoVO permissao) {
        this.nome = permissao.getNomeEntidade().toLowerCase();
        this.possuiFuncionalidade = !permissao.getPermissoes_API().equals("");
        this.referenciaFuncionalidade = permissao.getTituloApresentacao().split("-")[0].trim().toLowerCase();
    }

    public String getNome() {
        return nome;
    }

    public void setNome(String nome) {
        this.nome = nome;
    }

    public Boolean getPossuiFuncionalidade() {
        return possuiFuncionalidade;
    }

    public void setPossuiFuncionalidade(Boolean possuiFuncionalidade) {
        this.possuiFuncionalidade = possuiFuncionalidade;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        FuncionalidadesDTO that = (FuncionalidadesDTO) o;
        return nome.equals(that.nome);
    }

    @Override
    public int hashCode() {
        return Objects.hash(nome);
    }

    public String getReferenciaFuncionalidade() {
        return referenciaFuncionalidade;
    }

    public void setReferenciaFuncionalidade(String referenciaFuncionalidade) {
        this.referenciaFuncionalidade = referenciaFuncionalidade;
    }
}

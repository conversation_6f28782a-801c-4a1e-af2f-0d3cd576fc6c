package negocio.comuns.arquitetura.response;

import com.fasterxml.jackson.annotation.JsonInclude;
import negocio.comuns.arquitetura.UsuarioVO;

import java.util.ArrayList;
import java.util.List;

/**
 * Created by <PERSON><PERSON> on 28/09/2018.
 */
@JsonInclude(JsonInclude.Include.NON_NULL)
public class UsuarioResponseTO {

    private Integer id;
    private String username;
    private String nome;
    private String imageUri;
    private List<String> perfis;
    private Boolean administrador;
    private Boolean pedirSenhaFuncionalidade;
    public Boolean acessoPactoApp;
    private String email;

    public UsuarioResponseTO(UsuarioVO usuario) {
        this.id = usuario.getCodigo();
        this.username = usuario.getUsername();
        this.nome = usuario.getColaboradorVO().getPessoa_Apresentar();
        this.imageUri = usuario.getColaboradorVO().getPessoa().getUrlFoto();
        this.administrador = usuario.getAdministrador();
        this.pedirSenhaFuncionalidade = usuario.isPedirSenhaFuncionalidade();
        this.perfis = new ArrayList<>();
        this.acessoPactoApp = usuario.getAcessoPactoApp();
        usuario.getUsuarioPerfilAcessoVOs().forEach(it -> perfis.add(it.getPerfilAcesso().getNome()));
        this.email = usuario.getEmail();
    }

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getUsername() {
        return username;
    }

    public void setUsername(String username) {
        this.username = username;
    }

    public String getNome() {
        return nome;
    }

    public void setNome(String nome) {
        this.nome = nome;
    }

    public String getImageUri() {
        return imageUri;
    }

    public void setImageUri(String imageUri) {
        this.imageUri = imageUri;
    }

    public List<String> getPerfis() {
        return perfis;
    }

    public void setPerfis(List<String> perfis) {
        this.perfis = perfis;
    }

    public Boolean getAdministrador() {
        return administrador;
    }

    public void setAdministrador(Boolean administrador) {
        this.administrador = administrador;
    }

    public Boolean getPedirSenhaFuncionalidade() {
        return pedirSenhaFuncionalidade;
    }

    public void setPedirSenhaFuncionalidade(Boolean pedirSenhaFuncionalidade) {
        this.pedirSenhaFuncionalidade = pedirSenhaFuncionalidade;
    }

    public Boolean getAcessoPactoApp() {
        return acessoPactoApp;
    }

    public void setAcessoPactoApp(Boolean acessoPactoApp) {
        this.acessoPactoApp = acessoPactoApp;
    }

    public String getEmail() {
        return email;
    }

    public void setEmail(String email) {
        this.email = email;
    }
}

package negocio.comuns.arquitetura.response;

import com.fasterxml.jackson.annotation.JsonInclude;
import negocio.comuns.arquitetura.PermissaoVO;

import java.util.ArrayList;
import java.util.Objects;

@JsonInclude(JsonInclude.Include.NON_NULL)
public class PermissoesDTO {

    private String recurso;
    private ArrayList<String> tipoPermissoes;
    private String referenciaRecurso;

    public PermissoesDTO(PermissaoVO p) {
        if (p.getNomeEntidade() != null) {
            this.recurso = p.getNomeEntidade().toLowerCase();
            this.referenciaRecurso = p.getTituloApresentacao().split("-")[0].trim().toLowerCase();
            if(this.referenciaRecurso.equals(p.getTituloApresentacao().trim().toLowerCase())){
                this.referenciaRecurso = p.getTituloApresentacao().split(" ")[0].trim().toLowerCase();
            }
        }
        this.tipoPermissoes = new ArrayList<>();
        this.tipoPermissoes.add(p.getPermissoes_API());
    }

    public String getRecurso() {
        return recurso;
    }

    public void setRecurso(String recurso) {
        this.recurso = recurso;
    }

    public ArrayList<String> getTipoPermissoes() {
        return tipoPermissoes;
    }

    public void setTipoPermissoes(ArrayList<String> tipoPermissoes) {
        this.tipoPermissoes = tipoPermissoes;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        PermissoesDTO that = (PermissoesDTO) o;
        return recurso.equals(that.recurso);
    }

    @Override
    public int hashCode() {
        return Objects.hash(recurso);
    }

    public String getReferenciaRecurso() {
        return referenciaRecurso;
    }

    public void setReferenciaRecurso(String referenciaRecurso) {
        this.referenciaRecurso = referenciaRecurso;
    }
}

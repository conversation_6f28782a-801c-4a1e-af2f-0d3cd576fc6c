package negocio.comuns.arquitetura.response;

import com.fasterxml.jackson.annotation.JsonInclude;
import negocio.comuns.arquitetura.PerfilAcessoVO;
import negocio.comuns.arquitetura.PermissaoVO;
import negocio.comuns.utilitarias.OpcaoPerfilAcesso;

import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

@JsonInclude(JsonInclude.Include.NON_NULL)
public class PerfilUsuarioDTO {

    private String nome;
    private String tipo;
    private List<PermissoesDTO> recursos = new ArrayList<>();
    private List<FuncionalidadesDTO> funcionalidades = new ArrayList<>();

    public PerfilUsuarioDTO(PerfilAcessoVO perfil) {
        this.nome = perfil.getNome();
        this.tipo = perfil.getTipo() != null ? perfil.getTipo().getNome() : "";

        Set<PermissoesDTO> recursoList = new HashSet<>();
        Set<FuncionalidadesDTO> funcionalidadeList = new HashSet<>();
        for (PermissaoVO p : perfil.getPermissaoVOs()) {
            if (p.getTipoPermissao().equals(OpcaoPerfilAcesso.TP_ENTIDADE)) {
                recursoList.add(new PermissoesDTO(p));
            } else {
                funcionalidadeList.add(new FuncionalidadesDTO(p));
            }
        }

        this.recursos.addAll(recursoList);
        this.funcionalidades.addAll(funcionalidadeList);

    }


    public String getNome() {
        return nome;
    }

    public void setNome(String nome) {
        this.nome = nome;
    }

    public String getTipo() {
        return tipo;
    }

    public void setTipo(String tipo) {
        this.tipo = tipo;
    }

    public List<PermissoesDTO> getRecursos() {
        return recursos;
    }

    public void setRecursos(List<PermissoesDTO> recursos) {
        this.recursos = recursos;
    }

    public List<FuncionalidadesDTO> getFuncionalidades() {
        return funcionalidades;
    }

    public void setFuncionalidades(List<FuncionalidadesDTO> funcionalidades) {
        this.funcionalidades = funcionalidades;
    }
}

package negocio.comuns.arquitetura;

import annotations.arquitetura.ChavePrimaria;
import servicos.discovery.RedeDTO;

import java.util.Date;

public class UsuarioRedeEmpresaVO extends SuperVO {

    @ChavePrimaria
    private Integer codigo;
    private Integer usuario;
    private String chaveOrigem;
    private String chaveDestino;
    private Integer empresaDestino;
    private Date datacadastro;
    private Date dataatualizacao;
    private String nomeUnidade;
    private String mensagemSituacao;
    private RedeDTO redeDTO;
    private Integer usuarioReplicado;
    private boolean selecionado;
    private UsuarioVO responsavelOperacao;

    public UsuarioRedeEmpresaVO(Integer usuario, String chaveOrigem, String chaveDestino, Integer empresaDestino) {
        this.usuario = usuario;
        this.chaveOrigem = chaveOrigem;
        this.chaveDestino = chaveDestino;
        this.empresaDestino = empresaDestino;
    }

    public UsuarioRedeEmpresaVO(String nomeUnidade, Integer usuario, String chaveOrigem, String chaveDestino, Integer empresaDestino) {
        this.nomeUnidade = nomeUnidade;
        this.usuario = usuario;
        this.chaveOrigem = chaveOrigem;
        this.chaveDestino = chaveDestino;
        this.empresaDestino = empresaDestino;
    }

    public UsuarioRedeEmpresaVO() {
        
    }

    @Override
    public Integer getCodigo() {
        return codigo;
    }

    @Override
    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public Integer getUsuario() {
        return usuario;
    }

    public void setUsuario(Integer usuario) {
        this.usuario = usuario;
    }

    public String getChaveOrigem() {
        return chaveOrigem;
    }

    public void setChaveOrigem(String chaveOrigem) {
        this.chaveOrigem = chaveOrigem;
    }

    public String getChaveDestino() {
        return chaveDestino;
    }

    public void setChaveDestino(String chaveDestino) {
        this.chaveDestino = chaveDestino;
    }

    public Date getDatacadastro() {
        return datacadastro;
    }

    public void setDatacadastro(Date datacadastro) {
        this.datacadastro = datacadastro;
    }

    public Boolean getDataAtualizacaoInformada() {
        return getDataatualizacao() != null;
    }
    public Boolean getExibirBotaoRetirarVinculo() {
        return getDataatualizacao() != null && this.mensagemSituacao.contains("REPLICADO EM");
    }
    public Date getDataatualizacao() {
        return dataatualizacao;
    }

    public void setDataatualizacao(Date dataatualizacao) {
        this.dataatualizacao = dataatualizacao;
    }

    public String getNomeUnidade() {
        return nomeUnidade;
    }

    public void setNomeUnidade(String nomeUnidade) {
        this.nomeUnidade = nomeUnidade;
    }

    public String getMensagemSituacao() {
        return mensagemSituacao;
    }

    public void setMensagemSituacao(String mensagemSituacao) {
        this.mensagemSituacao = mensagemSituacao;
    }

    public RedeDTO getRedeDTO() {
        return redeDTO;
    }

    public void setRedeDTO(RedeDTO redeDTO) {
        this.redeDTO = redeDTO;
    }

    public Integer getUsuarioReplicado() {
        return usuarioReplicado;
    }

    public void setUsuarioReplicado(Integer usuarioReplicado) {
        this.usuarioReplicado = usuarioReplicado;
    }

    public Integer getEmpresaDestino() {
        return empresaDestino;
    }

    public void setEmpresaDestino(Integer empresaDestino) {
        this.empresaDestino = empresaDestino;
    }

    public boolean isSelecionado() {
        return selecionado;
    }

    public void setSelecionado(boolean selecionado) {
        this.selecionado = selecionado;
    }

    public UsuarioVO getResponsavelOperacao() {
        return responsavelOperacao;
    }

    public void setResponsavelOperacao(UsuarioVO responsavelOperacao) {
        this.responsavelOperacao = responsavelOperacao;
    }
}

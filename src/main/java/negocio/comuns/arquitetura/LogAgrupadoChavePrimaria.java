/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package negocio.comuns.arquitetura;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import negocio.comuns.utilitarias.Uteis;

/**
 *
 * <AUTHOR>
 */
public class LogAgrupadoChavePrimaria extends SuperVO {

    protected Integer codigo;
    protected String nomeEntidade;
    protected String nomeEntidadeDescricao;
    protected String chavePrimaria;
    protected String chavePrimariaEntidadeSubordinada;
    protected Date dataAlteracao;
    protected String responsavelAlteracao;
    protected String operacao;
    protected Integer pessoa;
    private List<LogVO> listaLogsChavePrimaria;

    public LogAgrupadoChavePrimaria() {
        inicializarDados();
    }

    public void inicializarDados() {
        setNomeEntidade("");
        setNomeEntidadeDescricao("");
        setChavePrimaria("");
        setChavePrimariaEntidadeSubordinada("");
        setDataAlteracao(negocio.comuns.utilitarias.Calendario.hoje());
        setResponsavelAlteracao("");
        setOperacao("");
        setPessoa(new Integer(0));
        setListaLogsChavePrimaria(new ArrayList<LogVO>());
    }

    /**
     * @return the codigo
     */
    public Integer getCodigo() {
        return codigo;
    }

    /**
     * @param codigo the codigo to set
     */
    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    /**
     * @return the nomeEntidade
     */
    public String getNomeEntidade() {
        if (nomeEntidade == null) {
            nomeEntidade = "";
        }
        return nomeEntidade;
    }

    public String getNomeEntidade_Apresentar() {
        if (!getChavePrimariaEntidadeSubordinada().equals("")) {
            return "(" + getNomeEntidadeDescricao() + ")";
        } else {
            return getNomeEntidade();
        }
    }

    /**
     * @param nomeEntidade the nomeEntidade to set
     */
    public void setNomeEntidade(String nomeEntidade) {
        this.nomeEntidade = nomeEntidade;
    }

    /**
     * @return the chavePrimaria
     */
    public String getChavePrimaria() {
        if (chavePrimaria == null) {
            chavePrimaria = "";
        }
        return chavePrimaria;
    }

    /**
     * @param chavePrimaria the chavePrimaria to set
     */
    public void setChavePrimaria(String chavePrimaria) {
        this.chavePrimaria = chavePrimaria;
    }

    /**
     * @return the dataAlteracao
     */
    public Date getDataAlteracao() {
        return dataAlteracao;
    }

    public String getDataHoraAlteracao_Apresentar() {
        if (dataAlteracao != null) {
            return Uteis.getData(dataAlteracao) + " ás "
                    + Uteis.gethoraHHMMSSAjustado(dataAlteracao);
        } else {
            return "";
        }
    }

    public String getDataAlteracao_Apresentar() {
        if (dataAlteracao != null) {
            return Uteis.getData(dataAlteracao);
        } else {
            return "";
        }
    }

    public String getHoraAlteracao_Apresentar() {
        if (dataAlteracao != null) {
            return Uteis.gethoraHHMMSSAjustado(dataAlteracao);
        } else {
            return "";
        }
    }

    /**
     * @param dataAlteracao the dataAlteracao to set
     */
    public void setDataAlteracao(Date dataAlteracao) {
        this.dataAlteracao = dataAlteracao;
    }

    /**
     * @return the responsavelAlteracao
     */
    public String getResponsavelAlteracao() {
        if (responsavelAlteracao == null) {
            responsavelAlteracao = "";
        }
        return responsavelAlteracao;
    }

    /**
     * @param responsavelAlteracao the responsavelAlteracao to set
     */
    public void setResponsavelAlteracao(String responsavelAlteracao) {
        this.responsavelAlteracao = responsavelAlteracao;
    }

    /**
     * @return the operacao
     */
    public String getOperacao() {
        if (operacao == null) {
            operacao = "";
        }

        return operacao;
    }

    /**
     * Método usado para formatar a operação considerando que será incluída
     * ALTERAÇÃO, INCLUSÃO , EXCLUSÃO avaliando se existe algum deles
     * e considerando que aparecerá somente uma vez mesmo tendo mais de
     * um tipo de operação
     * Ex. : possui duas alterações e uma inclusão
     * Aparecerá no seguinte formato "ALTERAÇÃO, INCLUSÃO"
     * @param log
     * @param logAgrupado
     * @return
     */
    public void montarOperacao(LogVO log, LogAgrupadoChavePrimaria logAgrupado) {
        if (logAgrupado.getOperacao().isEmpty()) {
            if (log.getOperacao().contains("ALTERAÇÃO")) {
                logAgrupado.setOperacao("ALTERAÇÃO");
            }
            if (log.getOperacao().contains("EXCLUSÃO")) {
                logAgrupado.setOperacao("EXCLUSÃO");
            }
            if (log.getOperacao().contains("INCLUSÃO")) {
                logAgrupado.setOperacao("INCLUSÃO");
            }
        } else {
            if (!logAgrupado.getOperacao().contains("ALTERAÇÃO") && log.getOperacao().contains("ALTERAÇÃO")) {
                logAgrupado.setOperacao(logAgrupado.getOperacao() + ", ALTERAÇÃO");
            } else if (!logAgrupado.getOperacao().contains("EXCLUSÃO") && log.getOperacao().contains("EXCLUSÃO")) {
                logAgrupado.setOperacao(logAgrupado.getOperacao() + ", EXCLUSÃO");
            } else if (!logAgrupado.getOperacao().contains("INCLUSÃO") && log.getOperacao().contains("INCLUSÃO")) {
                logAgrupado.setOperacao(logAgrupado.getOperacao() + ", INCLUSÃO");
            }
        }
    }

    /**
     * @param operacao the operacao to set
     */
    public void setOperacao(String operacao) {
        this.operacao = operacao;
    }

    /**
     * @return the chavePrimariaEntidadeSubordinada
     */
    public String getChavePrimariaEntidadeSubordinada() {
        if (chavePrimariaEntidadeSubordinada == null) {
            chavePrimariaEntidadeSubordinada = "";
        }
        return chavePrimariaEntidadeSubordinada;
    }

    /**
     * @param chavePrimariaEntidadeSubordinada the chavePrimariaEntidadeSubordinada to set
     */
    public void setChavePrimariaEntidadeSubordinada(String chavePrimariaEntidadeSubordinada) {
        this.chavePrimariaEntidadeSubordinada = chavePrimariaEntidadeSubordinada;
    }

    /**
     * @return the nomeEntidadeDescricao
     */
    public String getNomeEntidadeDescricao() {
        return nomeEntidadeDescricao;
    }

    /**
     * @param nomeEntidadeDescricao the nomeEntidadeDescricao to set
     */
    public void setNomeEntidadeDescricao(String nomeEntidadeDescricao) {
        this.nomeEntidadeDescricao = nomeEntidadeDescricao;
    }

    /**
     * @return O campo pessoa.
     */
    public Integer getPessoa() {
        return this.pessoa;
    }

    /**
     * @param pessoa O novo valor de pessoa.
     */
    public void setPessoa(Integer pessoa) {
        this.pessoa = pessoa;
    }

    /**
     * @return the listaLogsChavePrimaria
     */
    public List<LogVO> getListaLogsChavePrimaria() {
        return listaLogsChavePrimaria;
    }

    /**
     * @param listaLogsChavePrimaria the listaLogsChavePrimaria to set
     */
    public void setListaLogsChavePrimaria(List<LogVO> listaLogsChavePrimaria) {
        this.listaLogsChavePrimaria = listaLogsChavePrimaria;
    }
}

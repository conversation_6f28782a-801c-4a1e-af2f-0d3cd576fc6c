package negocio.comuns.arquitetura;

import annotations.arquitetura.ChaveEstrangeira;
import annotations.arquitetura.Lista;
import annotations.arquitetura.NaoControlarLogAlteracao;
import java.io.Serializable;

import java.lang.reflect.Field;
import java.lang.reflect.Modifier;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.GregorianCalendar;
import java.util.Iterator;
import java.util.List;
import negocio.comuns.utilitarias.Uteis;
import negocio.facade.jdbc.arquitetura.FacadeManager;

/**
 * SuperClasse padrão para classes do tipo VO - Value Object (Design Pattern:
 * Transfer Object). Classe somente com atributos representando dados da
 * entidade de negócio correspondente. Implementa um controle simples para
 * indicar se um objeto é novo (ainda não gravado no banco de dados) ou, caso
 * contrário, o objeto em questão já existe no banco de dados. Permite mapear se
 * um objeto deve ser incluído ou alterado no BD.
 */
public class SuperVO extends FacadeManager implements Serializable {

    protected Integer codigo = 0;
    protected Boolean novoObj = true;
    protected Boolean validarDados = true;
    protected UsuarioVO usuarioVO;
    protected Object objetoVOAntesAlteracao = null;
    protected Boolean empresaInternacional;
    protected String[] identificadoresPessoais;

    /**
     * Creates a new instance of SuperVO
     */
    public SuperVO() {
    }

    /**
     * Responsável por registrar o objeto que está sendo alterado antes de sua
     * alteração ou exclusão. É necessário esse regitro para que possa ser
     * possivel comparar o objeto no seu estado antes da edição e depois da
     * edição para ver em qual situação o mesmo se encontra. Essa ação é
     * necessaria para geração do CONTROLE DE LOG.
     *
     * <AUTHOR> Cantarelli dos Santos
     *
     */
    public void registrarObjetoVOAntesDaAlteracao() {
        try {
            objetoVOAntesAlteracao = getClone(false);
        } catch (Exception e) {
            Uteis.logar(e, this.getClass());
            objetoVOAntesAlteracao = null;
        }
    }

    /**
     * Método responsável pela geração do Log (alteração) do Objeto referente.
     * Na classe GeradorLog será feito a validação para geração do Log.
     *
     * @return List Retorna uma lista com os objetos do tipo LogVO.
     * @throws java.lang.ClassNotFoundException
     * @throws java.lang.IllegalArgumentException
     * @throws java.lang.IllegalAccessException
     */
    public List gerarLogAlteracaoObjetoVO() throws Exception {
        return gerarLogAlteracaoObjetoVO(false);
    }

    public List gerarLogAlteracaoObjetoVO(boolean validarValorNull) throws Exception {
        return gerarLogAlteracaoObjetoVO(validarValorNull, null);
    }

    public List gerarLogAlteracaoObjetoVO(boolean validarValorNull, UsuarioVO usuarioVO) throws Exception {
        GeradorLog geradorLog = new GeradorLog();
        geradorLog.setUsuarioVO(usuarioVO);
        geradorLog.validarCamposDeclaradosComAnnotations(this, objetoVOAntesAlteracao, "", "", "", validarValorNull);
        return geradorLog.getLogDeAlteracoes();
    }

    /**
     * Método responsável pela geração do Log (alteração) do Objeto referente.
     * Na classe GeradorLog será feito a validação para geração do Log.
     *
     * @return List Retorna uma lista com os objetos do tipo LogVO.
     * @throws java.lang.ClassNotFoundException
     * @throws java.lang.IllegalArgumentException
     * @throws java.lang.IllegalAccessException
     */
    public List gerarLogExclusaoObjetoVO() throws Exception {
        return gerarLogExclusaoObjetoVO(null);
    }

    public List gerarLogExclusaoObjetoVO(UsuarioVO usuarioVO) throws Exception {
        GeradorLog geradorLog = new GeradorLog();
        geradorLog.setUsuarioVO(usuarioVO);
        geradorLog.validarCadastrosExclusao(this);
        return geradorLog.getLogDeAlteracoes();
    }

    public void atualizarObjetoNovo(List<? extends SuperVO> lista) {
        for (SuperVO superVO : lista) {
            superVO.setNovoObj(false);
        }
    }

    /**
     * Retorna um status indicando se o objeto é novo (ainda não foi gravado no
     * BD) ou não.
     *
     * @return boolean True indica que o objeto é novo e portanto ainda não foi
     * gravado no BD. False indica que o objeto já existe no BD e deve ser
     * alterado.
     */
    public Boolean isNovoObj() {
        return novoObj;
    }

    public Boolean getNovoObj() {
        return novoObj;
    }

    /**
     * Define um status indicando se o objeto é novo (ainda não foi gravado no
     * BD) ou não.
     *
     * @param novoObj True indica que o objeto é novo e portanto ainda não foi
     * gravado no BD. False indica que o objeto já existe no BD e deve ser
     * alterado.
     */
    public void setNovoObj(Boolean novoObj) {
        this.novoObj = novoObj;
    }

    public Object getObjetoVOAntesAlteracao() {
        return objetoVOAntesAlteracao;
    }

    public void setObjetoVOAntesAlteracao(Object objetoVOAntesAlteracao) {
        this.objetoVOAntesAlteracao = objetoVOAntesAlteracao;
    }

    public Boolean getEmpresaInternacional() {
        return empresaInternacional;
    }

    public void setEmpresaInternacional(Boolean empresaInternacional) {
        this.empresaInternacional = empresaInternacional;
    }

    public String[] getIdentificadoresPessoais() {
        return identificadoresPessoais;
    }

    public void setIdentificadoresPessoais(String[] identificadoresPessoais) {
        this.identificadoresPessoais = identificadoresPessoais;
    }

    /**
     * Método responsável por realizar o Clone do objeto antes que o mesmo seja
     * alterado. O clone de um objeto é realizado em três (3) etapas, são elas:
     * Os Fields que possuem annotation do tipo ChaveEstrangeira. Os Fields que
     * possuem annotation do tipo Lista. Os demais Fields que não contém os
     * annotations citados acima.
     *
     * <AUTHOR> Cantarelli dos Santos
     */
    public Object getClone(boolean semLog) throws IllegalArgumentException, IllegalAccessException, InstantiationException {
        Object objetoClonado = this;
        Object objeto = new Object();
        objeto = this.getClass().newInstance();
        if (!this.getClass().getSuperclass().getSimpleName().equals("SuperVO")) {
            Field[] fieldsPrincipalSuperClasse = this.getClass().getSuperclass().getDeclaredFields();
            objeto = gerarCloneObjeto(fieldsPrincipalSuperClasse, objeto, objetoClonado, semLog);
        }
        Field[] fieldsPrincipal = this.getClass().getDeclaredFields();
        objeto = gerarCloneObjeto(fieldsPrincipal, objeto, objetoClonado, semLog);

        return objeto;
    }

    public Object gerarCloneObjeto(Field[] fields, Object objeto, Object objetoClonado, boolean semLog) throws IllegalArgumentException, IllegalArgumentException, IllegalArgumentException, IllegalAccessException, InstantiationException {

        for (Field field : fields) {
            field.setAccessible(true);
            if (!semLog && field.isAnnotationPresent(NaoControlarLogAlteracao.class)) {
                continue;
            }
            if (objeto == null || objetoClonado == null
                    || Modifier.isStatic(field.getModifiers())) {
                continue;
            }
            if (field.isAnnotationPresent(ChaveEstrangeira.class)) {
                if (field.get(objetoClonado) == null) {
                    continue;
                }
                Object objetoChaveEstrangeira = field.get(objetoClonado).getClass().newInstance();
                Field[] fieldsChaveEstrangeira = field.get(objetoClonado).getClass().getDeclaredFields();
                Object objetoChaveEstrangeiraClonado = field.get(objetoClonado);

                if (!field.get(objetoClonado).getClass().getSuperclass().getSimpleName().equals("SuperVO")) {
                    fieldsChaveEstrangeira = field.get(objetoClonado).getClass().getSuperclass().getDeclaredFields();
                }
                objetoChaveEstrangeira = gerarCloneObjeto(fieldsChaveEstrangeira, objetoChaveEstrangeira, objetoChaveEstrangeiraClonado, semLog);
                // for (Field fieldChaveEstrangeira : fieldsChaveEstrangeira) {
                // fieldChaveEstrangeira.setAccessible(true);
                // fieldChaveEstrangeira.set(objetoChaveEstrangeira,
                // fieldChaveEstrangeira.get(field.get(this)));
                // }
                field.set(objeto, objetoChaveEstrangeira);
            } else if (field.isAnnotationPresent(Lista.class)) {
                List listaClone = new ArrayList();
                List listaOriginal = (ArrayList) field.get(objetoClonado);
                if (listaOriginal != null) {
                    Iterator i = listaOriginal.iterator();
                    while (i.hasNext()) {
                        SuperVO obj = (SuperVO) i.next();
                        Object objetoLista = obj.getClass().newInstance();
                        Field[] fieldsLista = obj.getClass().getDeclaredFields();
                        if (!obj.getClass().getSuperclass().getSimpleName().equals("SuperVO")) {
                            fieldsLista = obj.getClass().getSuperclass().getDeclaredFields();
                        }
                        // for (Field fieldLista : fieldsLista) {
                        // fieldLista.setAccessible(true);
                        // fieldLista.set(objetoLista, fieldLista.get(obj));
                        // }

                        gerarCloneObjeto(fieldsLista, objetoLista, obj, semLog);
                        listaClone.add(objetoLista);
                    }
                    field.set(objeto, listaClone);
                }
            } else {
                field.set(objeto, field.get(objetoClonado));
            }
        }

        return objeto;
    }

    /*
     * @deprecated em 31/01/2008 - Utializar rotina em Uteis
     */
    public static String removerMascara(String campo) {
        return Uteis.removerMascara(campo);
    }

    /*
     * @deprecated em 31/01/2008 - Utializar rotina em Uteis
     */
    public static String aplicarMascara(String dado, String mascara) {
        return Uteis.aplicarMascara(dado, mascara);
    }

    /**
     * Realiza a validação de um número de CPF.
     *
     * @param retorno True indica que o CPF é valido. False indica que o CPF é
     * inválido.
     */
    public static boolean verificaCPF(String cpf) {
        try {
            cpf = removerMascara(cpf);
            if (Uteis.digitosIguais(cpf)) {
                return false;
            }
            if (cpf.equals("")) {
                return false;
            }
            // WM -> precisamos dos testes
		/*if ((cpf.equals("00000000000")) || (cpf.equals("11111111111")) || (cpf.equals("22222222222")) || (cpf.equals("33333333333")) || (cpf.equals("44444444444")) || (cpf.equals("55555555555")) || (cpf.equals("66666666666")) || (cpf.equals("77777777777")) || (cpf.equals("88888888888")) || (cpf.equals("99999999999"))) {
         return false;
         }*/

            int count, Soma, x, y, CPF[] = new int[11];

            if ((cpf.length() != 11) && (cpf.length() != 0)) {
                return false;
            }

            Soma = 0;
            for (count = 0; count < 11; count++) {
                CPF[count] = 0;
            }

            char vetorChar[] = new char[11];
            String temp, CPFvalido = "";

            for (count = 0; count < 11; count++) {
                // Transformar String em vetor de caracteres
                vetorChar = cpf.toCharArray();
                // Transformar cada caractere em String
                temp = String.valueOf(vetorChar[count]);
                // Transformar String em inteiro e jogar no vetor
                CPF[count] = Integer.parseInt(temp);
            }
            // Método da árvore para obter o x
            for (count = 0; count < 9; count++) {
                // Pegar soma da permutação dos dígitos do CPF
                Soma += CPF[count] * (10 - count);
            }

            // se o resto da divisão der 0 ou 1, x terá dois dígitos
            if (Soma % 11 < 2) {
                x = 0;
            } // x não pode ter dois dígitos
            else {
                x = 11 - (Soma % 11);
            } // obtendo o penúltimo dígito do CPF

            CPF[9] = x;

            // Método da árvore para obter o y
            Soma = 0;
            for (count = 0; count < 10; count++) {
                // Pegar soma da permutação dos dígitos do CPF
                Soma += CPF[count] * (11 - count);
            }

            // se o resto da divisão der 0 ou 1, y terá dois dígitos
            if (Soma % 11 < 2) {
                y = 0;
            } // y não pode ter dois dígitos
            else {
                y = 11 - (Soma % 11);
            } // obtendo o último dígito do CPF

            CPF[10] = y;
            Soma = 0;

            // Verificando se o cpf informado é válido para retornar resultado ao
            // programa
            for (count = 0; count < 11; count++) {
                CPFvalido += String.valueOf(CPF[count]);
            }
            if (cpf.compareTo(CPFvalido) == 0) {
                return true;
            } else {
                return false;
            }
            // return true;
        }catch (Exception ex){
            return false;
        }
    }

    /**
     * Realiza a validação de um número de CNPJ.
     *
     * @param retorno True indica que o CNPJ é valido. False indica que o CNPJ é
     * inválido.
     */
    public static boolean validaCNPJ(String cnpj) {
        boolean ret = false;

        cnpj = removerMascara(cnpj);
        if(Uteis.digitosIguais(cnpj)){
            return false;
        }
        String base = "00000000000000";
        if (cnpj.length() <= 14) {
            if (cnpj.length() < 14) {
                cnpj = base.substring(0, 14 - cnpj.length()) + cnpj;
            }
            int soma = 0;
            int dig = 0;
            String cnpj_calc = cnpj.substring(0, 12);
            char[] chr_cnpj = cnpj.toCharArray();
            // Primeira parte
            for (int i = 0; i < 4; i++) {
                if (chr_cnpj[i] - 48 >= 0 && chr_cnpj[i] - 48 <= 9) {
                    soma += (chr_cnpj[i] - 48) * (6 - (i + 1));
                }
            }
            for (int i = 0; i < 8; i++) {
                if (chr_cnpj[i + 4] - 48 >= 0 && chr_cnpj[i + 4] - 48 <= 9) {
                    soma += (chr_cnpj[i + 4] - 48) * (10 - (i + 1));
                }
            }
            dig = 11 - (soma % 11);
            cnpj_calc += (dig == 10 || dig == 11) ? "0" : Integer.toString(dig);
            // Segunda parte
            soma = 0;
            for (int i = 0; i < 5; i++) {
                if (chr_cnpj[i] - 48 >= 0 && chr_cnpj[i] - 48 <= 9) {
                    soma += (chr_cnpj[i] - 48) * (7 - (i + 1));
                }
            }
            for (int i = 0; i < 8; i++) {
                if (chr_cnpj[i + 5] - 48 >= 0 && chr_cnpj[i + 5] - 48 <= 9) {
                    soma += (chr_cnpj[i + 5] - 48) * (10 - (i + 1));
                }
            }
            dig = 11 - (soma % 11);
            cnpj_calc += (dig == 10 || dig == 11) ? "0" : Integer.toString(dig);
            ret = cnpj.equals(cnpj_calc);

        }
        return ret;
    }

    public Boolean getValidarDados() {
        return validarDados;
    }

    public void setValidarDados(Boolean validarDados) {
        this.validarDados = validarDados;
    }

    public UsuarioVO getUsuarioVO() {
        return usuarioVO;
    }

    public void setUsuarioVO(UsuarioVO usuarioVO) {
        this.usuarioVO = usuarioVO;
    }

    public Integer getCodigo() {
        return codigo;
    }

    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public List getListaColunas() {
        return SuperVO.obterListaColunasVO(this.getClass());
    }

    public static List obterListaColunasVO(Class classe) {
        Field[] campos = classe.getDeclaredFields();
        List<ColunaVO> listaColuna = new ArrayList<ColunaVO>();

        for (int i = 0; i < campos.length; i++) {
            //inicializar lista de colunas da tabela dinâmica
            if ((campos[i].getType() instanceof Object)) {
                Field campo = campos[i];
                String prefixo = "get";
                if (campo.getType() == Boolean.class) {
                    prefixo = "is";
                }
                String sufixoGetter = campo.getName();
                if (campo.isAnnotationPresent(ChaveEstrangeira.class)) {
                    sufixoGetter += ".codigo";
                }
                ColunaVO colunaVO = new ColunaVO();
                colunaVO.setNomeGetter(prefixo + Uteis.firstLetterUpper(sufixoGetter));
                colunaVO.setHeader(campo.getName());
                colunaVO.setNomeAtributo(sufixoGetter);
                listaColuna.add(colunaVO);


            }
        }
        return listaColuna;
    }

    public String retornaNomeDiaSemanaData(Date data) {
        Calendar cal = new GregorianCalendar();
        cal.setTime(data);
        String nomeDiaSemanaAcesso = Uteis.retornaDescricaoDiaSemana(cal);
        return nomeDiaSemanaAcesso;
    }

    public static <T> void validarDados(T obj) throws Exception {
    }
}

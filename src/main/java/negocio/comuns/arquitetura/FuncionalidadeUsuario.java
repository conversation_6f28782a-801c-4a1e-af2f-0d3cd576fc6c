package negocio.comuns.arquitetura;

import controle.arquitetura.FuncionalidadeSistemaEnum;

import java.util.ArrayList;
import java.util.List;

public class FuncionalidadeUsuario extends SuperTO {

    private List<FuncionalidadeSistemaEnum> historico = new ArrayList<FuncionalidadeSistemaEnum>();
    private List<FuncionalidadeSistemaEnum> favorito = new ArrayList<FuncionalidadeSistemaEnum>();

    public List<FuncionalidadeSistemaEnum> getHistorico() {
        return historico;
    }

    public void setHistorico(List<FuncionalidadeSistemaEnum> historico) {
        this.historico = historico;
    }

    public List<FuncionalidadeSistemaEnum> getFavorito() {
        return favorito;
    }

    public void setFavorito(List<FuncionalidadeSistemaEnum> favorito) {
        this.favorito = favorito;
    }
}

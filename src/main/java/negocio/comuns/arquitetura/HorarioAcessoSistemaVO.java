/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package negocio.comuns.arquitetura;

import annotations.arquitetura.FKJson;
import annotations.arquitetura.NaoControlarLogAlteracao;
import br.com.pactosolucoes.ce.comuns.enumerador.DiaSemana;
import java.text.ParseException;
import negocio.comuns.utilitarias.ConsistirException;

/**
 *
 * <AUTHOR>
 */
public class HorarioAcessoSistemaVO extends SuperVO {

    private Integer codigo = 0;
    private DiaSemana diaSemana;
    private String horaInicial = "";
    private String horaFinal = "";
    @NaoControlarLogAlteracao
    @FKJson
    private UsuarioVO usuarioVO;
    private Boolean verificado;

    public HorarioAcessoSistemaVO() {
        inicializarDados();
    }

    public void realizarUpperCaseDados() {
    }

    public void inicializarDados() {
        setCodigo(0);
        setDiaSemana(DiaSemana.DOMINGO);
    }

    /**
     * Operação responsável por validar os dados de um objeto da classe <code>HorarioAcessoSistemaVO</code>.
     * Todos os tipos de consistência de dados são e devem ser implementadas neste método.
     * São validações típicas: verificação de campos obrigatórios, verificação de valores válidos para os atributos.
     * @exception ConsistirExecption Se uma inconsistência for encontrada aumaticamente é gerada uma exceção descrevendo
     *                               o atributo e o erro ocorrido.
     */
    public static void validarDados(HorarioAcessoSistemaVO obj) throws ConsistirException, ParseException {
        if (!obj.getValidarDados().booleanValue()) {
            return;
        }
        if (obj.getDiaSemana() == null || "".equals(obj.getDiaSemana().getDescricao())) {
            throw new ConsistirException("O campo DIA DA SEMANA(Horário de Acesso do Sistema) deve ser informado.");
        }
        if ("".equals(obj.getHoraInicial())) {
            throw new ConsistirException("O campo HORA INICIAL(Horário de Acesso do Sistema) deve ser informado.");
        }
        if ("".equals(obj.getHoraFinal())) {
            throw new ConsistirException("O campo HORA FINAL(Horário de Acesso do Sistema) deve ser informado.");
        }
    }

    @Override
    public boolean equals(Object obj) {
        if (obj == null) {
            return false;
        }
        if (getClass() != obj.getClass()) {
            return false;
        }
        final HorarioAcessoSistemaVO other = (HorarioAcessoSistemaVO) obj;
        if (this.codigo != other.codigo && (this.codigo == null || !this.codigo.equals(other.codigo))) {
            return false;
        }
        return true;
    }

    @Override
    public int hashCode() {
        int hash = 7;
        hash = 59 * hash + (this.codigo != null ? this.codigo.hashCode() : 0);
        return hash;
    }

   
    /**
     * @return the codigo
     */
    public Integer getCodigo() {
        return codigo;
    }

    /**
     * @param codigo the codigo to set
     */
    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    /**
     * @return the diaSemana
     */
    public DiaSemana getDiaSemana() {
        return diaSemana;
    }

    /**
     * @param diaSemana the diaSemana to set
     */
    public void setDiaSemana(DiaSemana diaSemana) {
        this.diaSemana = diaSemana;
    }

    /**
     * @return the horaInicial
     */
    public String getHoraInicial() {
        return horaInicial;
    }

    /**
     * @param horaInicial the horaInicial to set
     */
    public void setHoraInicial(String horaInicial) {
        this.horaInicial = horaInicial;
    }

    /**
     * @return the horaFinal
     */
    public String getHoraFinal() {
        return horaFinal;
    }

    /**
     * @param horaFinal the horaFinal to set
     */
    public void setHoraFinal(String horaFinal) {
        this.horaFinal = horaFinal;
    }

    /**
     * @return the perfilAcessoVO
     */
    public UsuarioVO getUsuarioVO() {
        return usuarioVO;
    }

    /**
     * @param perfilAcessoVO the perfilAcessoVO to set
     */
    public void setUsuarioVO(UsuarioVO usuarioVO) {
        this.usuarioVO = usuarioVO;
    }

    public Boolean isVerificado() {
        if (verificado == null) {
            verificado = false;
        }
        return verificado;
    }

    public void setVerificado(Boolean verificado) {
        this.verificado = verificado;
    }

    @Override
    public String toString() {
        return "HorarioAcessoSistemaVO{" + "codigo=" + codigo + ",\ndiaSemana=" + diaSemana.getDescricao() + ",\nhoraInicial=" + horaInicial + ",\nhoraFinal=" + horaFinal + ",\nusuarioVO=" + usuarioVO.getCodigo() + ",\nverificado=" + verificado + '}';
    }
}

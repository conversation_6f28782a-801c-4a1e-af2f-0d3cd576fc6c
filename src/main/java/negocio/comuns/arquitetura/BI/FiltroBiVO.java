package negocio.comuns.arquitetura.BI;

import br.com.pactosolucoes.comuns.json.SuperJSON;
import negocio.comuns.arquitetura.SuperVO;
import org.json.JSONObject;

public class FiltroBiVO extends SuperVO {

    private Integer codigo;
    private String nome;
    private Long dataGeracao;
    private String jsonDados;
    private String filtros;
    private String tokenFiltro;

    @Override
    public Integer getCodigo() {
        return codigo;
    }

    @Override
    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public String getNome() {
        return nome;
    }

    public void setNome(String nome) {
        this.nome = nome;
    }

    public Long getDataGeracao() {
        return dataGeracao;
    }

    public void setDataGeracao(Long dataGeracao) {
        this.dataGeracao = dataGeracao;
    }

    public String getJsonDados() {
        return jsonDados;
    }

    public void setJsonDados(String jsonDados) {
        this.jsonDados = jsonDados;
    }

    public String getFiltros() {
        return filtros;
    }

    public void setFiltros(String filtros) {
        this.filtros = filtros;
    }

    public String getTokenFiltro() {
        return tokenFiltro;
    }

    public void setTokenFiltro(String tokenFiltro) {
        this.tokenFiltro = tokenFiltro;
    }

    public JSONObject toJSON() {
        JSONObject json = new JSONObject();
        json.put("codigo", codigo);
        json.put("nome", nome);
        json.put("dataGeracao", dataGeracao);
        json.put("jsonDados", new JSONObject(jsonDados));
        json.put("filtros", new JSONObject(filtros));
        json.put("tokenFiltro", tokenFiltro);

        return json;
    }
}

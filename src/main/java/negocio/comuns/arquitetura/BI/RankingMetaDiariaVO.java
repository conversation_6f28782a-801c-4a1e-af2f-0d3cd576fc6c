package negocio.comuns.arquitetura.BI;

import negocio.comuns.arquitetura.SuperVO;
import org.json.JSONObject;

public class RankingMetaDiariaVO extends SuperVO {

    private Integer codigoColaborador;
    private String nomeColaborador;
    private Integer totalRealizado;
    private Integer totalPrevisto;
    private Double percentualRealizado;
    private Integer posicao;

    public Integer getCodigoColaborador() {
        return codigoColaborador;
    }

    public void setCodigoColaborador(Integer codigoColaborador) {
        this.codigoColaborador = codigoColaborador;
    }

    public String getNomeColaborador() {
        return nomeColaborador;
    }

    public void setNomeColaborador(String nomeColaborador) {
        this.nomeColaborador = nomeColaborador;
    }

    public Integer getTotalRealizado() {
        return totalRealizado;
    }

    public void setTotalRealizado(Integer totalRealizado) {
        this.totalRealizado = totalRealizado;
    }

    public Integer getTotalPrevisto() {
        return totalPrevisto;
    }

    public void setTotalPrevisto(Integer totalPrevisto) {
        this.totalPrevisto = totalPrevisto;
    }

    public Double getPercentualRealizado() {
        return percentualRealizado;
    }

    public void setPercentualRealizado(Double percentualRealizado) {
        this.percentualRealizado = percentualRealizado;
    }

    public Integer getPosicao() {
        return posicao;
    }

    public void setPosicao(Integer posicao) {
        this.posicao = posicao;
    }

    @Override
    public String toString() {
        return "codigoColaborador=" + codigoColaborador +
                ", nomeColaborador='" + nomeColaborador + '\'' +
                ", totalRealizado=" + totalRealizado +
                ", totalPrevisto=" + totalPrevisto +
                ", percentualRealizado=" + percentualRealizado +
                ", posicao=" + posicao;
    }
}

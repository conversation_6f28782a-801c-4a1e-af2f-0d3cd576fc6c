/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package negocio.comuns.arquitetura;

/**
 *
 * <AUTHOR>
 */
public class BannerVO extends SuperTO {
    private static final long serialVersionUID = 7428299537494579387L;

    private String urlImagem;
    private String urlLink;
    private String funcionalidade;

    public BannerVO() {
    }

    public BannerVO(String urlImagem) {
        this.urlImagem = urlImagem;
    }

    public BannerVO(final String urlImagem, final String urlLink) {
        this.urlImagem = urlImagem;
        this.urlLink = urlLink;
    }

    public String getUrlImagem() {
        return urlImagem;
    }

    public void setUrlImagem(String urlImagem) {
        this.urlImagem = urlImagem;
    }

    public String getUrlLink() {
        return urlLink;
    }

    public void setUrlLink(String urlLink) {
        this.urlLink = urlLink;
    }

    public String getFuncionalidade() {
        return funcionalidade;
    }

    public void setFuncionalidade(String funcionalidade) {
        this.funcionalidade = funcionalidade;
    }
}

/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package negocio.comuns.arquitetura;

import java.util.Date;

import negocio.comuns.basico.ClienteVO;
import negocio.comuns.basico.ColaboradorVO;
import negocio.comuns.basico.EmpresaVO;

/**
 * 
 * <AUTHOR>
 */
public class RiscoVO extends SuperVO {

	protected Date dia;
	protected Integer codigo;
	protected ClienteVO clienteVO;
	protected EmpresaVO empresaVO;
	protected ColaboradorVO colaboradorVO;
	protected Integer peso;
	protected String nomeCliente;
	protected String matriculaCliente;
	protected String foneCliente;
	protected UsuarioVO usuarioColaboradorVO;

	public RiscoVO() {
		super();
		inicializarDados();
	}

	public void inicializarDados() {
		//        setCodigo(new Integer(0));
		//        setPeso(new Integer(0));
		//        setNomeCliente("");
		//        setMatriculaCliente("");
		//        setFoneCliente("");
		//        setClienteVO(new ClienteVO());
		//        setEmpresaVO(new EmpresaVO());
		//        setColaboradorVO(new ColaboradorVO());

	}

	public void realizarUpperCaseDados() {
		setNomeCliente(getNomeCliente().toUpperCase());
		setMatriculaCliente(getMatriculaCliente().toUpperCase());
	}

	public ClienteVO getClienteVO() {
		if (clienteVO == null) {
			clienteVO = new ClienteVO();
		}
		return clienteVO;
	}

	public void setClienteVO(ClienteVO clienteVO) {
		this.clienteVO = clienteVO;
	}

	public Integer getCodigo() {
		if (codigo == null) {
			codigo = new Integer(0);
		}
		return codigo;
	}

	public void setCodigo(Integer codigo) {
		this.codigo = codigo;
	}

	public ColaboradorVO getColaboradorVO() {
		if (colaboradorVO == null) {
			colaboradorVO = new ColaboradorVO();
		}
		return colaboradorVO;
	}

	public void setColaboradorVO(ColaboradorVO colaboradorVO) {
		this.colaboradorVO = colaboradorVO;
	}

	public EmpresaVO getEmpresaVO() {
		if (empresaVO == null) {
			empresaVO = new EmpresaVO();
		}
		return empresaVO;
	}

	public void setEmpresaVO(EmpresaVO empresaVO) {
		this.empresaVO = empresaVO;
	}

	public String getMatriculaCliente() {
		if (matriculaCliente == null) {
			matriculaCliente = "";
		}
		return matriculaCliente;
	}

	public void setMatriculaCliente(String matriculaCliente) {
		this.matriculaCliente = matriculaCliente;
	}

	public String getNomeCliente() {
		if (nomeCliente == null) {
			nomeCliente = "";
		}
		return nomeCliente;
	}

	public void setNomeCliente(String nomeCliente) {
		this.nomeCliente = nomeCliente;
	}

	public String getFoneCliente() {
		if (foneCliente == null) {
			foneCliente = "";
		}
		return foneCliente;
	}

	public void setFoneCliente(String foneCliente) {
		this.foneCliente = foneCliente;
	}

	public Integer getPeso() {
		if (peso == null) {
			peso = new Integer(0);
		}
		return peso;
	}

	public void setPeso(Integer peso) {
		this.peso = peso;
	}

	public UsuarioVO getUsuarioColaboradorVO() {
		if (usuarioColaboradorVO == null) {
			usuarioColaboradorVO = new UsuarioVO();
		}
		return usuarioColaboradorVO;
	}

	public void setUsuarioColaboradorVO(UsuarioVO usuarioColaboradorVO) {
		this.usuarioColaboradorVO = usuarioColaboradorVO;
	}

	/**
	 * @return the dia
	 */
	public Date getDia() {
		if (dia == null) {
			dia = negocio.comuns.utilitarias.Calendario.hoje();
		}
		return dia;
	}

	/**
	 * @param dia the dia to set
	 */
	public void setDia(Date dia) {
		this.dia = dia;
	}
	
}

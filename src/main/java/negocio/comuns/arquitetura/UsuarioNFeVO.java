package negocio.comuns.arquitetura;

import annotations.arquitetura.ChaveEstrangeira;
import annotations.arquitetura.ChavePrimaria;
import negocio.comuns.utilitarias.ConsistirException;
import negocio.comuns.utilitarias.Uteis;

public class UsuarioNFeVO extends UsuarioVO {

    @ChavePrimaria
    private Integer id_Usuario = 0;
    @ChaveEstrangeira
    private PerfilUsuarioNFeVO perfilUsuarioNFe = new PerfilUsuarioNFeVO();
    private Integer idPerfil;
    private String nome;
    private String usuario;
    private String senha;
    private String senhaDescriptografada;
    private Boolean status = true;
    private boolean administrador = false;

    public UsuarioNFeVO() {
        super();
    }

    public static void validarDados(UsuarioNFeVO obj) throws ConsistirException {
        if (!obj.getValidarDados()) {
            return;
        }
        if (obj.getNome().equals("")) {
            throw new ConsistirException("O campo Nome (Usuário) deve ser informado.");
        }

        if (obj.getUsuario().equals("")) {
            throw new ConsistirException("O campo Usuário (Usuário) deve ser informado.");
        }

        if (obj.getSenha().equals("")) {
            throw new ConsistirException("O campo Senha (Usuário) deve ser informado.");
        }

        if (obj.getStatus() == null) {
            throw new ConsistirException("O campo Situação (Usuário) deve ser informado.");
        }

        if (obj.getPerfilUsuarioNFe().getCodigo() == null
                || obj.getPerfilUsuarioNFe().getCodigo() == 0) {
            throw new ConsistirException("O campo Perfil de Usuário (Usuário) deve ser informado.");
        }
    }

    public Integer getId_Usuario() {
        return id_Usuario;
    }

    public void setId_Usuario(Integer id_Usuario) {
        this.id_Usuario = id_Usuario;
    }

    public PerfilUsuarioNFeVO getPerfilUsuarioNFe() {
        return perfilUsuarioNFe;
    }

    public void setPerfilUsuarioNFe(PerfilUsuarioNFeVO perfilUsuarioNFe) {
        this.perfilUsuarioNFe = perfilUsuarioNFe;
    }

    public String getNome() {
        return nome;
    }

    public void setNome(String nome) {
        this.nome = nome;
    }

    public String getUsuario() {
        return usuario;
    }

    public void setUsuario(String usuario) {
        this.usuario = usuario;
    }

    public String getSenha() {
        return senha;
    }

    public void setSenha(String senha) {
        this.senha = senha;
    }

    public String getUsername() {
        return getUsuario();
    }

    public Integer getIdPerfil() {
        return idPerfil;
    }

    public void setIdPerfil(Integer idPerfil) {
        this.idPerfil = idPerfil;
    }

    public String getSenhaDescriptografada() {
        return senhaDescriptografada;
    }

    public void setSenhaDescriptografada(String senhaDescriptografada) {
        this.senhaDescriptografada = senhaDescriptografada;
    }

    public boolean manteveSenha() {
        if (this.getSenha().length() < 4) {
            return this.getSenhaDescriptografada().equals(this.getSenha());
        }
        return this.getSenhaDescriptografada().equals(Uteis.desencriptarNFe(this.getSenha()));
    }

    public Boolean getAdministrador() {
        return isAdministrador();
    }

    public boolean isAdministrador() {
        return administrador;
    }

    public void setAdministrador(boolean administrador) {
        this.administrador = administrador;
    }

    public Boolean getStatus() {
        return status;
    }

    public void setStatus(Boolean status) {
        this.status = status;
    }
}

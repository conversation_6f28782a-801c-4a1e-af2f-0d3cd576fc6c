/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package negocio.comuns.arquitetura;

import java.io.Serializable;
import java.util.Calendar;
import java.util.Date;
import java.util.List;
import negocio.comuns.utilitarias.Uteis;

/**
 *
 * <AUTHOR>
 */
public class DiaRoboVO implements Serializable {

    private String chave;
    private Date dia;

    public DiaRoboVO() {
    }

    public DiaRoboVO(Date data) {
        this.chave = Uteis.getDataAplicandoFormatacao(
                data, "ddMMyyyy");
        this.dia = data;
    }

    public Date getDia() {
        return dia;
    }

    public void setDia(Date dia) {
        this.dia = dia;
    }

    public void setChave(String chave) {
        this.chave = chave;
    }

    public String getChave() {
        return chave;
    }

    public static void adicionaUnicamente(Date dia, List<DiaRoboVO> lista) {
        if (lista.isEmpty()) {
            DiaRoboVO d = new DiaRoboVO(dia);
            lista.add(d);
        } else {
            boolean achou = false;
            for (DiaRoboVO diaRoboVO : lista) {
                String chaveDiaNovo = Uteis.getDataAplicandoFormatacao(
                        dia, "ddMMyyyy");
                if (diaRoboVO.getChave().equals(chaveDiaNovo)) {
                    achou = true;
                    break;
                }
            }
            if (!achou) {
                DiaRoboVO d = new DiaRoboVO(dia);
                lista.add(d);
            }
        }

    }
}

/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */

package negocio.comuns.arquitetura;

import org.richfaces.model.Ordering;

/**
 *
 * <AUTHOR>
 */
public class ColunaVO {

    private String header;

    private String nomeAtributo;

    private Ordering sortOrder = Ordering.UNSORTED;

    public void setNomeAtributo(String nomeAtributo) {
        this.nomeAtributo = nomeAtributo;
    }

    public void setNomeGetter(String nomeGetter) {
        this.nomeGetter = nomeGetter;
    }

    public void setNomeSetter(String nomeSetter) {
        this.nomeSetter = nomeSetter;
    }

    private String nomeGetter;

    public String getNomeAtributo() {
        return nomeAtributo;
    }

    public String getNomeGetter() {
        return nomeGetter;
    }

    public String getNomeSetter() {
        return nomeSetter;
    }

    private String nomeSetter;

    public void setHeader(String header) {
        this.header = header;
    }



    public String getHeader() {
        return header;
    }

    public void setSortOrder(Ordering sortOrder) {
        this.sortOrder = sortOrder;
    }

    public Ordering getSortOrder() {
        return sortOrder;
    }

    


}

/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package negocio.comuns.arquitetura;

/**
 *
 * <AUTHOR>
 */
public class ColumnType {

    private String name;
    private String columnType;

    public String getColumnType() {
        return columnType;
    }

    public void setColumnType(String columnType) {
        this.columnType = columnType;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public ColumnType(String name, String columnType) {
        this.name = name;
        this.columnType = columnType;

    }

    @Override
    public String toString(){
        return String.format("%s %s", new Object[]{name, columnType});
    }
}

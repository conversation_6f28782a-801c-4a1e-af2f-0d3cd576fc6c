package negocio.comuns.arquitetura;

import annotations.arquitetura.ChavePrimaria;
import negocio.comuns.utilitarias.Calendario;
import negocio.facade.jdbc.arquitetura.Usuario;
import negocio.comuns.utilitarias.Uteis;
import java.math.BigDecimal;
import java.sql.Timestamp;
import java.time.Instant;
import java.util.Date;

public class LogTotalPassVO extends SuperVO {

    @ChavePrimaria
    private Integer codigo;
    private Integer pessoa;
    private Timestamp dataregistro;
    private String uri;
    private String apikey;
    private String json;
    private String resposta;
    private Long tempoResposta;
    private String tipo;
    private Integer empresa;
    private String ip;
    private Integer usuario;
    protected String tipoAcesso;
    private String Origem;
    private String Status;

    private String respostaApi;

    @Override
    public Integer getCodigo() {
        return codigo;
    }

    @Override
    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public Integer getPessoa() {
        return pessoa;
    }

    public void setPessoa(Integer pessoa) {
        this.pessoa = pessoa;
    }

    public Timestamp getDataregistro() {
        return dataregistro;
    }

    public void setDataregistro(Timestamp dataregistro) {
        this.dataregistro = dataregistro;
    }

    public String getUri() {
        return uri;
    }

    public void setUri(String uri) {
        this.uri = uri;
    }

    public String getApikey() {
        return apikey;
    }

    public void setApikey(String apikey) {
        this.apikey = apikey;
    }

    public String getJson() {
        return json;
    }

    public void setJson(String json) {
        this.json = json;
    }

    public String getResposta() {
        return resposta;
    }

    public void setResposta(String resposta) {
        this.resposta = resposta;
    }

    public Long getTempoResposta() {
        return tempoResposta;
    }

    public void setTempoResposta(Long tempoResposta) {
        this.tempoResposta = tempoResposta;
    }

    public String getTipo() {
        return tipo;
    }

    public void setTipo(String tipo) {
        this.tipo = tipo;
    }

    public Integer getEmpresa() {
        return empresa;
    }

    public void setEmpresa(Integer empresa) {
        this.empresa = empresa;
    }

    public Integer getUsuario() {
        return usuario;
    }

    public void setUsuario(Integer usuario) {
        this.usuario = usuario;
    }

    public String getIp() {
        return ip;
    }

    public void setIp(String ip) {
        this.ip = ip;
    }

    public void setDataRegistroInstant(Instant instant) {
        this.dataregistro = Timestamp.from(instant);
    }

    public String getTipoAcesso() {
        return (tipoAcesso);
    }

    public String getDataRegistro_Apresentar() {
        return (Uteis.getDataComHora(dataregistro));
    }

    public String getHora_Apresentar() {
        return (Uteis.getDataComHora(dataregistro));
    }

    public boolean getPermiteAcesso() {
        return  (getTiposQuePermiteAcesso().indexOf(getTipoAcesso()) >= 0);
    }

    public static String getTiposQuePermiteAcesso() {
        StringBuilder sb = new StringBuilder();
        sb.append("'CA',");
        sb.append("'BO',");
        sb.append("'TO',");
        sb.append("'TD',");
        sb.append("'PL',");
        sb.append("'AA',");
        sb.append("'DI',");
        sb.append("'RT',");
        sb.append("'RR',");
        sb.append("'RA',");
        sb.append("'RC'");

        return sb.toString();
    }

    public String getOrigem() {
        return Origem;
    }

    public void setOrigem(String origem) {
        Origem = origem;
    }

    public String getStatus() {
        return Status;
    }

    public void setStatus(String status) {
        Status = status;
    }

    public String getRespostaApi() {
        return respostaApi;
    }

    public void setRespostaApi(String respostaApi) {
        this.respostaApi = respostaApi;
    }

}

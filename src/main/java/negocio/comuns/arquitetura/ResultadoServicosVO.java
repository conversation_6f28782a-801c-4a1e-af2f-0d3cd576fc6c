package negocio.comuns.arquitetura;

import org.json.JSONArray;
import org.json.JSONObject;
import negocio.comuns.basico.enumerador.ServicoEnum;
import negocio.comuns.utilitarias.Calendario;

import java.util.Date;

public class ResultadoServicosVO extends SuperVO {

    private Integer codigo;
    private ServicoEnum servico;
    private Integer empresa;
    private String descricao;
    private JSONArray resultado;
    private boolean finalizado = false;
    private Date dataHoraInicio;
    private Date dataHoraFim;

    public ResultadoServicosVO() {
    }

    public ResultadoServicosVO(ServicoEnum servicoEnum) {
        this.servico = servicoEnum;
        this.empresa = 0;
        this.descricao = servicoEnum.getDescricao();
        this.dataHoraInicio = Calendario.hoje();
        this.resultado = new JSONArray();
        this.finalizado  = false;
    }

    public ResultadoServicosVO(ServicoEnum servicoEnum , String descricao) {
        this.servico = servicoEnum;
        this.empresa = 0;
        this.descricao = descricao;
        this.dataHoraInicio = Calendario.hoje();
        this.resultado = new JSONArray();
        this.finalizado  = false;
    }

    public ResultadoServicosVO(ServicoEnum servicoEnum, Integer empresa) {
        this.servico = servicoEnum;
        this.empresa = empresa;
        this.descricao = "";
        this.dataHoraInicio = Calendario.hoje();
        this.resultado = new JSONArray();
        this.finalizado  = false;
    }

    public ResultadoServicosVO(ServicoEnum servicoEnum, String descricao, Integer empresa) {
        this.servico = servicoEnum;
        this.empresa = empresa;
        this.descricao = descricao;
        this.dataHoraInicio = Calendario.hoje();
        this.resultado = new JSONArray();
        this.finalizado  = false;
    }

    @Override
    public Integer getCodigo() {
        return codigo;
    }

    @Override
    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public ServicoEnum getServico() {
        return servico;
    }

    public void setServico(ServicoEnum servico) {
        this.servico = servico;
    }

    public boolean isFinalizado() {
        return finalizado;
    }

    public void setFinalizado(boolean finalizado) {
        this.finalizado = finalizado;
    }

    public Date getDataHoraInicio() {
        return dataHoraInicio;
    }

    public void setDataHoraInicio(Date dataHoraInicio) {
        this.dataHoraInicio = dataHoraInicio;
    }

    public Date getDataHoraFim() {
        return dataHoraFim;
    }

    public void setDataHoraFim(Date dataHoraFim) {
        this.dataHoraFim = dataHoraFim;
    }

    public JSONArray getResultado() {
        if (resultado == null) {
            resultado = new JSONArray();
        }
        return resultado;
    }

    public void setResultado(JSONArray resultado) {
        this.resultado = resultado;
    }

    public Integer getEmpresa() {
        if (empresa == null) {
            empresa = 0;
        }
        return empresa;
    }

    public void setEmpresa(Integer empresa) {
        this.empresa = empresa;
    }

    public String getDescricao() {
        if (descricao == null) {
            descricao = "";
        }
        return descricao;
    }

    public void setDescricao(String descricao) {
        this.descricao = descricao;
    }

    public String getMensagemErro() {
        String msg = "[DEBUG] " + Calendario.hoje() + " - " + "ERRO INCLUIR RESULTADO SERVICO: " + getServico().getDescricao().toUpperCase() + " || EMPRESA: " + getEmpresa();
        System.out.println(msg);
        return msg;
    }
}

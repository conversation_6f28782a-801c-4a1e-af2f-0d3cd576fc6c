package negocio.comuns.arquitetura;
import java.util.Iterator;
import java.util.List;
import java.util.ArrayList;
import java.util.Objects;

import annotations.arquitetura.ChavePrimaria;
import annotations.arquitetura.ListJson;
import annotations.arquitetura.Lista;
import annotations.arquitetura.NaoControlarLogAlteracao;
import negocio.comuns.feed.PerfilUsuarioEnum;
import negocio.comuns.utilitarias.*;
import org.apache.commons.lang.WordUtils;
import org.json.JSONArray;
import org.json.JSONObject;

/**
 * Reponsável por manter os dados da entidade PerfilAcesso. Classe do tipo VO - Value Object 
 * composta pelos atributos da entidade com visibilidade protegida e os métodos de acesso a estes atributos.
 * Classe utilizada para apresentar e manter em memória os dados desta entidade.
 * @see SuperVO
*/
public class PerfilAcessoVO extends SuperVO {
	@ChavePrimaria
    private Integer codigo;
    private String nome;
    private PerfilUsuarioEnum tipo;
    private Boolean administrador;
    private String nomeApresentarPermisao;
    /** Atributo responsável por manter os objetos da classe <code>Permissao</code>. */
    @Lista
    @ListJson(clazz = PermissaoVO.class)
    private List<PermissaoVO> permissaoVOs;

    @NaoControlarLogAlteracao
    protected String nomeCapitalizado;

    private Double porcetagemDescontoContrato = 0.0d;

    private boolean unificado = false;
	
    /**
     * Construtor padrão da classe <code>PerfilAcesso</code>.
     * Cria uma nova instância desta entidade, inicializando automaticamente seus atributos (Classe VO).
    */
    public PerfilAcessoVO() {
        super();
        inicializarDados();
    }
     
	
    /**
     * Operação responsável por validar os dados de um objeto da classe <code>PerfilAcessoVO</code>.
     * Todos os tipos de consistência de dados são e devem ser implementadas neste método.
     * São validações típicas: verificação de campos obrigatórios, verificação de valores válidos para os atributos.
     * @exception ConsistirException Se uma inconsistência for encontrada aumaticamente é gerada uma exceção descrevendo
     *                               o atributo e o erro ocorrido.
    */
    public static void validarDados(PerfilAcessoVO obj) throws ConsistirException {
        if (!obj.getValidarDados().booleanValue()) {
            return;
            }
        if (obj.getNome().equals("")) { 
            throw new ConsistirException("O campo NOME (PerfilAcesso) deve ser informado.");
        }
        if (obj.getPorcetagemDescontoContrato() > 100.0) {
            throw new ConsistirException("O campo Negociação de contratos na aba descontos (PerfilAcesso) deve ser informado.");
        }
    }
     
    /**
     * Operação reponsável por realizar o UpperCase dos atributos do tipo String.
    */
    public void realizarUpperCaseDados() {
        setNome( nome.toUpperCase() );
    }
     
    /**
     * Operação reponsável por inicializar os atributos da classe.
    */
    public void inicializarDados() {
        setCodigo( new Integer(0) );
        setNome( "" );
        setPermissaoVOs( new ArrayList() );
        setNomeApresentarPermisao("");
    }

    /**
     * Operação responsável por adicionar um novo objeto da classe <code>PermissaoVO</code>
     * ao List <code>permissaoVOs</code>. Utiliza o atributo padrão de consulta 
     * da classe <code>Permissao</code> - getNomeEntidade() - como identificador (key) do objeto no List.
     * @param obj    Objeto da classe <code>PermissaoVO</code> que será adiocionado ao Hashtable correspondente.
    */
    public void adicionarObjPermissaoVOs(PermissaoVO obj) throws Exception {
        PermissaoVO.validarDados(obj);
        int index = 0;
        Iterator i = getPermissaoVOs().iterator();
        while (i.hasNext()) {
            PermissaoVO objExistente = (PermissaoVO)i.next();
            if (objExistente.getNomeEntidade().equals(obj.getNomeEntidade())) {
                getPermissaoVOs().set( index , obj );
                return;
            }
            index++;
        }
        getPermissaoVOs().add( obj );
    }

    /**
     * Operação responsável por excluir um objeto da classe <code>PermissaoVO</code>
     * no List <code>permissaoVOs</code>. Utiliza o atributo padrão de consulta 
     * da classe <code>Permissao</code> - getNomeEntidade() - como identificador (key) do objeto no List.
     * @param nomeEntidade  Parâmetro para localizar e remover o objeto do List.
    */
    public void excluirObjPermissaoVOs(String nomeEntidade) throws Exception {
        int index = 0;
        Iterator i = getPermissaoVOs().iterator();
        while (i.hasNext()) {
            PermissaoVO objExistente = (PermissaoVO)i.next();
            if (objExistente.getNomeEntidade().equals(nomeEntidade)) {
                getPermissaoVOs().remove( index );
                return;
            }
            index++;
        }
    }

    /**
     * Operação responsável por consultar um objeto da classe <code>PermissaoVO</code>
     * no List <code>permissaoVOs</code>. Utiliza o atributo padrão de consulta 
     * da classe <code>Permissao</code> - getNomeEntidade() - como identificador (key) do objeto no List.
     * @param nomeEntidade  Parâmetro para localizar o objeto do List.
    */
    public PermissaoVO consultarObjPermissaoVO(String nomeEntidade) {
        Iterator i = getPermissaoVOs().iterator();
        while (i.hasNext()) {
            PermissaoVO objExistente = (PermissaoVO) i.next();
            if (objExistente.getNomeEntidade().equals(nomeEntidade)) {
                return objExistente;
            }
        }
        return null;
    }
	

    /** Retorna Atributo responsável por manter os objetos da classe <code>Permissao</code>. */
    public List<PermissaoVO> getPermissaoVOs() {
        return (permissaoVOs);
    }
     
    /** Define Atributo responsável por manter os objetos da classe <code>Permissao</code>. */
    public void setPermissaoVOs( List<PermissaoVO> permissaoVOs ) {
        this.permissaoVOs = permissaoVOs;
    }

    public String getNome() {
        if(nome == null){
        nome = "";
        }
        return (nome);
    }

    public String getNomeCapitalizado() {
        return nomeCapitalizado;
    }

    public void setNomeCapitalizado(String nomeCapitalizado) {
        this.nomeCapitalizado = nomeCapitalizado;
    }

    public String getNomePrimeiraLetraMaiuscula() {
        if(nomeCapitalizado == null || nomeCapitalizado.equals("")) {
            StringBuilder novoNomeCapitalizado = new StringBuilder();
            String aux = "";
            if (getNome().contains("*")) {
                for (String letra : getNome().toLowerCase().split("")) {
                    if ((letra.matches(".*[a-z].*")) && (aux.equals(" ") || aux.equals("") || !aux.matches(".*[a-z].*"))) {
                        letra = WordUtils.capitalize(letra);
                    }
                    novoNomeCapitalizado.append(letra);
                    aux = letra.toLowerCase();
                }
                setNomeCapitalizado(novoNomeCapitalizado.toString());
                return nomeCapitalizado;
            }
            setNomeCapitalizado(WordUtils.capitalize(getNome().toLowerCase()));
        }
        return nomeCapitalizado;
    }

    public void setNome( String nome ) {
        this.nome = nome;
    }

    public Integer getCodigo() {
        return (codigo);
    }
     
    public void setCodigo( Integer codigo ) {
        this.codigo = codigo;
    }

    public PerfilUsuarioEnum getTipo() {
        return tipo;
    }

    public void setTipo(PerfilUsuarioEnum tipo) {
        this.tipo = tipo;
    }

    public String getNomeApresentarPermisao() {
        return nomeApresentarPermisao;
    }

    public void setNomeApresentarPermisao(String nomeApresentarPermisao) {
        this.nomeApresentarPermisao = nomeApresentarPermisao;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        PerfilAcessoVO that = (PerfilAcessoVO) o;
        return Objects.equals(codigo, that.codigo) &&
                Objects.equals(nome, that.nome) &&
                Objects.equals(tipo, that.tipo) &&
                Objects.equals(administrador, that.administrador) &&
                Objects.equals(nomeApresentarPermisao, that.nomeApresentarPermisao) &&
                Objects.equals(permissaoVOs, that.permissaoVOs);
    }

    @Override
    public int hashCode() {
        return Objects.hash(codigo, nome, tipo, administrador, nomeApresentarPermisao, permissaoVOs);
    }

    public String getTipoPerfilFromOrdinal(){
        if (getTipo() == null){
            if (getNome() != null) {
                return Uteis.retirarAcentuacao(getNome());
            }else {
                return "";
            }
        }else {
            return getTipo().getNome();
        }
    }

    public JSONObject toJSON() {
        JSONObject json = new JSONObject();
        json.put("codigo", this.codigo);
        json.put("nome", this.nome);
        json.put("tipo", this.tipo);
        json.put("adminsitrador", this.administrador);

        return json;
    }

    private JSONArray permissaoVOsToJSON(List<PermissaoVO> permissaoVOs) {
        JSONArray permissoesJson = new JSONArray();
        for(PermissaoVO permissaoVO: permissaoVOs){
            permissoesJson.put(permissaoVO.toJSON());
        }

        return permissoesJson;
    }

    public Double getPorcetagemDescontoContrato() {
        return porcetagemDescontoContrato;
    }

    public void setPorcetagemDescontoContrato(Double porcetagemDescontoContrato) {
        this.porcetagemDescontoContrato = porcetagemDescontoContrato;
    }

    public boolean isUnificado() {
        return unificado;
    }

    public void setUnificado(boolean unificado) {
        this.unificado = unificado;
    }
}

package negocio.comuns.arquitetura;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;

@JsonIgnoreProperties(ignoreUnknown = true)
public class ClubeDeBeneficiosVO {

    private Integer codigo;
    private Boolean ativo;
    private String nome;
    private String link;

    public Integer getCodigo() {
        return codigo;
    }

    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public Boolean getAtivo() {
        return ativo;
    }

    public void setAtivo(Boolean ativo) {
        this.ativo = ativo;
    }

    public String getNome() {
        return nome;
    }

    public void setNome(String nome) {
        this.nome = nome;
    }

    public String getLink() {
        if (link == null) {
            link = "";
        }
        return link;
    }

    public void setLink(String link) {
        this.link = link;
    }
}

package negocio.comuns.arquitetura;

import negocio.comuns.utilitarias.Uteis;
import org.json.JSONObject;

import java.util.Date;

public class RecursoEmpresaTO extends SuperTO {

    private Date date;
    private String recurso;
    private Integer empresa;
    private String chave;
    private String modulos;
    private String usuario;
    private String nomeEmpresa;
    private String cidade;
    private String uf;
    private String pais;
    private Long tempoRequisicao;
    private int intervaloDiasPesquisados;
    public Date getDate() {
        return date;
    }

    public void setDate(Date date) {
        this.date = date;
    }

    public String getRecurso() {
        return recurso;
    }

    public void setRecurso(String recurso) {
        this.recurso = recurso;
    }

    public Integer getEmpresa() {
        return empresa;
    }

    public void setEmpresa(Integer empresa) {
        this.empresa = empresa;
    }

    public String getChave() {
        return chave;
    }

    public void setChave(String chave) {
        this.chave = chave;
    }

    public String getModulos() {
        return modulos;
    }

    public void setModulos(String modulos) {
        this.modulos = modulos;
    }

    public String getUsuario() {
        return usuario;
    }

    public void setUsuario(String usuario) {
        this.usuario = usuario;
    }

    public String getNomeEmpresa() {
        return nomeEmpresa;
    }

    public void setNomeEmpresa(String nomeEmpresa) {
        this.nomeEmpresa = nomeEmpresa;
    }

    public String getCidade() {
        return cidade;
    }

    public void setCidade(String cidade) {
        this.cidade = cidade;
    }

    public String getUf() {
        return uf;
    }

    public void setUf(String uf) {
        this.uf = uf;
    }

    public String getPais() {
        return pais;
    }

    public void setPais(String pais) {
        this.pais = pais;
    }

    public Long getTempoRequisicao() {
        return tempoRequisicao;
    }

    public void setTempoRequisicao(Long tempoRequisicao) {
        this.tempoRequisicao = tempoRequisicao;
    }

    public int getIntervaloDiasPesquisados() {
        return intervaloDiasPesquisados;
    }

    public void setIntervaloDiasPesquisados(int intervaloDiasPesquisados) {
        this.intervaloDiasPesquisados = intervaloDiasPesquisados;
    }

    public String toJSON() {
        JSONObject json = new JSONObject();
        json.put("data", Uteis.getDataComHora(date));
        json.put("recurso", recurso);
        json.put("empresa", empresa);
        json.put("chave", chave);
        json.put("modulos", modulos);
        json.put("usuario", usuario);
        json.put("nomeEmpresa", nomeEmpresa);
        json.put("cidade", cidade);
        json.put("uf", uf);
        json.put("pais", pais);
        json.put("tempoRequisicao", tempoRequisicao);
        json.put("intervaloDiasPesquisados", intervaloDiasPesquisados);
        return json.toString();
    }
}

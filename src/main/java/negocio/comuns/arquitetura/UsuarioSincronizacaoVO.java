package negocio.comuns.arquitetura;

import negocio.comuns.utilitarias.Uteis;

import java.util.Date;

public class UsuarioSincronizacaoVO extends SuperVO {

    private Integer codigo;
    private Date dataRegistro;
    private Integer usuario;
    private String operacao;
    private boolean sucesso = false;
    private String resposta;

    @Override
    public Integer getCodigo() {
        return codigo;
    }

    @Override
    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public String getDataRegistroApresentar() {
        return Uteis.getDataComHora(getDataRegistro());
    }

    public Date getDataRegistro() {
        return dataRegistro;
    }

    public void setDataRegistro(Date dataRegistro) {
        this.dataRegistro = dataRegistro;
    }

    public Integer getUsuario() {
        return usuario;
    }

    public void setUsuario(Integer usuario) {
        this.usuario = usuario;
    }

    public String getOperacao() {
        return operacao;
    }

    public void setOperacao(String operacao) {
        this.operacao = operacao;
    }

    public boolean isSucesso() {
        return sucesso;
    }

    public void setSucesso(boolean sucesso) {
        this.sucesso = sucesso;
    }

    public String getResposta() {
        return resposta;
    }

    public void setResposta(String resposta) {
        this.resposta = resposta;
    }
}

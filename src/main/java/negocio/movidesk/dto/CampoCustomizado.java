package negocio.movidesk.dto;

/**
 * <AUTHOR>
 * @since 19/02/19
 */
public abstract class Campo<PERSON>ustomizado {

    protected Integer customFieldId;
    protected Integer customFieldRuleId;
    protected Integer line;

    public CampoCustomizado(Integer customFieldId, Integer customFieldRuleId, Integer line) {
        this.customFieldId = customFieldId;
        this.customFieldRuleId = customFieldRuleId;
        this.line = line;
    }

    public Integer getCustomFieldId() {
        return customFieldId;
    }

    public void setCustomFieldId(Integer customFieldId) {
        this.customFieldId = customFieldId;
    }

    public Integer getCustomFieldRuleId() {
        return customFieldRuleId;
    }

    public void setCustomFieldRuleId(Integer customFieldRuleId) {
        this.customFieldRuleId = customFieldRuleId;
    }

    public Integer getLine() {
        return line;
    }

    public void setLine(Integer line) {
        this.line = line;
    }
}

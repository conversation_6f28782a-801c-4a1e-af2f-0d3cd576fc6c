package negocio.movidesk.dto;

/**
 * <AUTHOR>
 * @since 09/02/19
 */
public class EnderecoDTO {

    private String addressType;
    private String country;
    private String state;
    private String city;
    private String district;
    private String street;
    private String postalCode;

    public EnderecoDTO() {
        this.addressType = "Comercial";
        this.country = "BRASIL";
    }

    public EnderecoDTO estado(String sigla) {
        this.state = sigla;
        return this;
    }

    public EnderecoDTO cidade(String cidade) {
        this.city = cidade;
        return this;
    }

    public EnderecoDTO bairro(String bairro) {
        this.district = bairro;
        return this;
    }

    public EnderecoDTO rua(String rua) {
        this.street = rua;
        return this;
    }

    public EnderecoDTO cep(String cep) {
        this.postalCode = cep;
        return this;
    }

    public String getAddressType() {
        return addressType;
    }

    public void setAddressType(String addressType) {
        this.addressType = addressType;
    }

    public String getCountry() {
        return country;
    }

    public void setCountry(String country) {
        this.country = country;
    }

    public String getState() {
        return state;
    }

    public void setState(String state) {
        this.state = state;
    }

    public String getCity() {
        return city;
    }

    public void setCity(String city) {
        this.city = city;
    }

    public String getDistrict() {
        return district;
    }

    public void setDistrict(String district) {
        this.district = district;
    }

    public String getStreet() {
        return street;
    }

    public void setStreet(String street) {
        this.street = street;
    }

    public String getPostalCode() {
        return postalCode;
    }

    public void setPostalCode(String postalCode) {
        this.postalCode = postalCode;
    }
}

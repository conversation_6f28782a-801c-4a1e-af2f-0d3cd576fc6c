package negocio.movidesk.dto;

import org.apache.commons.lang3.StringUtils;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @since 09/02/19
 */
public class CarteiraCampoCustomizadoDTO extends CampoCustomizado {

    private List<ItemCustomizado> items;

    public CarteiraCampoCustomizadoDTO() {
        super(15564, 6999, 1);
        this.items = new ArrayList<ItemCustomizado>();
    }

    public class ItemCustomizado {
        private String personId;
        private String clientId;
        private String team;
        private String customFieldItem;
        private String storageFileGuid;

        private String fileName;

        public ItemCustomizado() {
            this.storageFileGuid = "";
        }

        public ItemCustomizado customFieldItem(String valor) {
            this.customFieldItem = valor;
            return this;
        }

        public String getCustomFieldItem() {
            return customFieldItem;
        }

        public void setCustomFieldItem(String customFieldItem) {
            this.customFieldItem = customFieldItem;
        }

        public String getPersonId() {
            return personId;
        }

        public void setPersonId(String personId) {
            this.personId = personId;
        }

        public String getClientId() {
            return clientId;
        }

        public void setClientId(String clientId) {
            this.clientId = clientId;
        }

        public String getTeam() {
            return team;
        }

        public void setTeam(String team) {
            this.team = team;
        }

        public String getStorageFileGuid() {
            return storageFileGuid;
        }

        public void setStorageFileGuid(String storageFileGuid) {
            this.storageFileGuid = storageFileGuid;
        }

        public String getFileName() {
            return fileName;
        }

        public void setFileName(String fileName) {
            this.fileName = fileName;
        }

    }

    public CarteiraCampoCustomizadoDTO carteiras(String carteirasComVirgula) {

        if (StringUtils.isBlank(carteirasComVirgula)) {
            return this;
        }

        final String[] carteiras = carteirasComVirgula.split(",");
        for (String carteira : carteiras) {
            items.add(new ItemCustomizado().customFieldItem(carteira.trim()));
        }

        return this;
    }

    public List<ItemCustomizado> getItems() {
        return items;
    }

    public void setItems(List<ItemCustomizado> items) {
        this.items = items;
    }
}

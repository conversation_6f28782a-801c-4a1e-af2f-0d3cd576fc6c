package negocio.movidesk.dto;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @since 09/02/19
 */
public class EmpresaMovideskDTO {

    private String id;
    private Integer empresaFinanceiro;
    private String corporateName;
    private String profileType;
    private String businessName;
    private String cpfCnpj;
    private String personType;
    private String isActive;
    private List<ChaveCampoCustomizadoDTO> customFieldValues;
    private List<EmailDTO> emails;
    private List<EnderecoDTO> addresses;
    private List<ContatoDTO> contacts;

    public EmpresaMovideskDTO() {
        this.profileType = "2";
        this.personType = "2";
        this.isActive = "true";

        this.customFieldValues = new ArrayList<ChaveCampoCustomizadoDTO>();
        this.emails = new ArrayList<EmailDTO>();
        this.addresses = new ArrayList<EnderecoDTO>();
        this.contacts = new ArrayList<ContatoDTO>();
    }

    public EmpresaMovideskDTO id(String id) {
        this.id = id;
        return this;
    }

    public EmpresaMovideskDTO razaoSocial(String razaoSocial) {
        this.corporateName = razaoSocial;
        return this;
    }

    public EmpresaMovideskDTO nomeFantasia(String nomeFantasia) {
        this.businessName = nomeFantasia;
        return this;
    }

    public EmpresaMovideskDTO cpfCnpj(String cpfCnpj) {
        this.cpfCnpj = cpfCnpj;
        return this;
    }

    public EmpresaMovideskDTO chave(String chave) {
        this.customFieldValues.add(new ChaveCampoCustomizadoDTO().chave(chave));
        return this;
    }

    public EmpresaMovideskDTO email(String email) {
        this.emails.add(new EmailDTO().email(email));
        return this;
    }

    public EmpresaMovideskDTO endereco(String siglaEstado, String cidade) {
        this.addresses.add(new EnderecoDTO().estado(siglaEstado).cidade(cidade));
        return this;
    }

    public EmpresaMovideskDTO telefone(String telefone) {
        this.contacts.add(new ContatoDTO().telefone(telefone));
        return this;
    }
    public EmpresaMovideskDTO empresaFinanceiro(Integer empresaFinanceiro){
        this.empresaFinanceiro = empresaFinanceiro;
        return this;
    }

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getCorporateName() {
        return corporateName;
    }

    public void setCorporateName(String corporateName) {
        this.corporateName = corporateName;
    }

    public String getProfileType() {
        return profileType;
    }

    public void setProfileType(String profileType) {
        this.profileType = profileType;
    }

    public String getBusinessName() {
        return businessName;
    }

    public void setBusinessName(String businessName) {
        this.businessName = businessName;
    }

    public String getCpfCnpj() {
        return cpfCnpj;
    }

    public void setCpfCnpj(String cpfCnpj) {
        this.cpfCnpj = cpfCnpj;
    }

    public String getPersonType() {
        return personType;
    }

    public void setPersonType(String personType) {
        this.personType = personType;
    }

    public String getIsActive() {
        return isActive;
    }

    public void setIsActive(String isActive) {
        this.isActive = isActive;
    }

    public List<ChaveCampoCustomizadoDTO> getCustomFieldValues() {
        return customFieldValues;
    }

    public void setCustomFieldValues(List<ChaveCampoCustomizadoDTO> customFieldValues) {
        this.customFieldValues = customFieldValues;
    }

    public List<EmailDTO> getEmails() {
        return emails;
    }

    public void setEmails(List<EmailDTO> emails) {
        this.emails = emails;
    }

    public List<EnderecoDTO> getAddresses() {
        return addresses;
    }

    public void setAddresses(List<EnderecoDTO> addresses) {
        this.addresses = addresses;
    }

    public List<ContatoDTO> getContacts() {
        return contacts;
    }

    public void setContacts(List<ContatoDTO> contacts) {
        this.contacts = contacts;
    }

    public Integer getEmpresaFinanceiro() {
        return empresaFinanceiro;
    }

    public void setEmpresaFinanceiro(Integer empresaFinanceiro) {
        this.empresaFinanceiro = empresaFinanceiro;
    }
}

package negocio.movidesk;

import com.google.gson.Gson;
import negocio.movidesk.dto.EmpresaMovideskDTO;
import negocio.movidesk.dto.MovideskResponseDTO;
import negocio.oamd.DiaExtraService;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClientBuilder;
import org.apache.http.util.EntityUtils;

import java.io.IOException;
import java.util.logging.Level;
import java.util.logging.Logger;

/**
 * <AUTHOR>
 * @since 22/02/19
 */
public class MovideskService {

    private static final Logger LOGGER = Logger.getLogger(DiaExtraService.class.getSimpleName());

    private static final String ACCEPT_HEADER = "Accept";
    private static final String HEADER_APPLICATION_JSON = "application/json";

    private static final String MOVIDESK_API_PATH = "https://api.movidesk.com/public/v1";
    private static final String MOVIDESK_API_PERSON_PATH = MOVIDESK_API_PATH + "/persons";
    private static final String MOVIDESK_API_TOKEN_PARAM = "token";

    private final Gson gson;

    private MovideskService() {
        gson = new Gson();
    }

    public static MovideskService getInstance() {
        return new MovideskService();
    }

    public String persistirEmpresa(final EmpresaMovideskDTO empresaMovideskDTO) {
        final CloseableHttpClient httpClient = HttpClientBuilder.create().build();
        try {

            final StringEntity body = new StringEntity(gson.toJson(empresaMovideskDTO));
            body.setContentType(HEADER_APPLICATION_JSON);

            final HttpPost post = new HttpPost(getPersonsApiUrl());
            post.setHeader(ACCEPT_HEADER, HEADER_APPLICATION_JSON);
            post.setEntity(body);

            final CloseableHttpResponse respostaEmpresaRegistrada = httpClient.execute(post);
            if (respostaEmpresaRegistrada.getStatusLine().getStatusCode() < 200
                    || respostaEmpresaRegistrada.getStatusLine().getStatusCode() > 299) {
                return null;
            }

            final MovideskResponseDTO movideskResponseDTO = verificarEmpresaPersistidaMovidesk(respostaEmpresaRegistrada);
            if (movideskResponseDTO == null) {
                return null;
            }

            return movideskResponseDTO.getId();

        } catch (Exception e) {
            LOGGER.log(Level.SEVERE, "Não foi possível ler a resposta do servidor", e);
            return null;
        } finally {
            closeClientSilent(httpClient);
        }
    }

    private MovideskResponseDTO verificarEmpresaPersistidaMovidesk(CloseableHttpResponse respostaEmpresaRegistrada) {
        try {
            return gson.fromJson(EntityUtils.toString(respostaEmpresaRegistrada.getEntity()), MovideskResponseDTO.class);
        } catch (Exception e) {
            LOGGER.log(Level.SEVERE, "Não foi possível registrar a empresa no Movidesk", e);
            return null;
        }
    }

    private void closeClientSilent(CloseableHttpClient httpClient) {
        if (httpClient != null) {
            try {
                httpClient.close();
            } catch (IOException ignored) {
            }
        }
    }

    private String getPersonsApiUrl() {
        return MOVIDESK_API_PERSON_PATH + "?" + MOVIDESK_API_TOKEN_PARAM + "0a8af88b-d27b-49dc-a2e8-ece5c2724847";
    }

}

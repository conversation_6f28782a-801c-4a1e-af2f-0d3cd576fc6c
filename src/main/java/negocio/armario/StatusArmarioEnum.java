/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package negocio.armario;

import br.com.pactosolucoes.comuns.util.JSFUtilities;
import java.util.List;
import javax.faces.model.SelectItem;

/**
 *
 * <AUTHOR>
 */
public enum StatusArmarioEnum {

    ABERTO(0, "Aberto"),
    FECHADO(1, "Fechado"),
    INATIVO(2, "Inativo");
    private Integer id;
    private String label;

    private StatusArmarioEnum(final Integer id, String label) {
        this.id = id;
        this.label = label;
    }

    public String getLabel() {
        return label;
    }

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public static StatusArmarioEnum obterPorOrdinal(int ord) {
        for (StatusArmarioEnum stats : StatusArmarioEnum.values()) {
            if (stats.ordinal() == ord) {
                return stats;
            }
        }
        return null;
    }

    public List<SelectItem> obterSelectItem() {
        return JSFUtilities.getListFromEnum(StatusArmarioEnum.class);
    }
}

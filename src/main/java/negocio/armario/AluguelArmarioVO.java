/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package negocio.armario;

import annotations.arquitetura.ChaveEstrangeira;
import annotations.arquitetura.NaoControlarLogAlteracao;
import br.com.pactosolucoes.comuns.util.Formatador;
import java.util.Date;
import negocio.comuns.arquitetura.SuperVO;
import negocio.comuns.arquitetura.UsuarioVO;
import negocio.comuns.basico.ClienteVO;
import negocio.comuns.contrato.MovProdutoVO;
import negocio.comuns.financeiro.VendaAvulsaVO;
import negocio.comuns.plano.ProdutoVO;
import negocio.comuns.utilitarias.Calendario;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisValidacao;

/**
 *
 * <AUTHOR>
 */
public class AluguelArmarioVO extends SuperVO {
    
    @ChaveEstrangeira
    private ClienteVO cliente;
    @ChaveEstrangeira
    private ArmarioVO armario;
    @ChaveEstrangeira
    private VendaAvulsaVO vendaAvulsa;
    @ChaveEstrangeira
    private MovProdutoVO movProduto;
    @NaoControlarLogAlteracao
    private ProdutoVO produto;
    private Double valor;
    @ChaveEstrangeira
    private UsuarioVO responsavelCadastro = new UsuarioVO();
    private Date dataCadastro;
    private Date fimOriginal;
    private Boolean contratoAssinado;
    private Boolean renovarAutomatico;
    private Date dataRenovacaoAutomatica;
    private Date dataInicio;
    private AluguelArmarioVO relacionamentoRenovacao;
    private Boolean chaveDevolvida = Boolean.FALSE;
    
    public String getInicioApresentar() {
        return (Uteis.getData(getMovProduto().getDataInicioVigencia()));
    }
   public String getDataInicioAluguelApresentar(){
       return (Uteis.getData(dataInicio));
   }
    public String getFimApresentar() {
        if(produto != null && !UteisValidacao.emptyNumber(produto.getCodigo())){
            return (Uteis.getData(getMovProduto().getDataFinalVigencia()));
        }
        return "";
    }
    
    public String getFinalVigenciaApresentar() {
        return (Uteis.getData(getMovProduto().getDataFinalVigencia()));
    }

    public String getValorApresentar() {
        return Formatador.formatarValorMonetario(getValor());
    }
    
    public ClienteVO getCliente() {
        if(cliente == null){
            cliente = new ClienteVO();
        }
        return cliente;
    }
    public String getContratoAssinadoApresentar(){
        return  contratoAssinado ? "fa-icon-check" : "fa-icon-check-empty";
    }

    public String getDataFimOriginalApresentar(){
        return Uteis.getData(getFimOriginal());
    }
    public void setCliente(ClienteVO cliente) {
        this.cliente = cliente;
    }

    public ArmarioVO getArmario() {
        if(armario == null){
            armario = new ArmarioVO();
        }
        return armario;
    }

    public void setArmario(ArmarioVO armario) {
        this.armario = armario;
    }

    public VendaAvulsaVO getVendaAvulsa() {
        if(vendaAvulsa == null){
            vendaAvulsa = new VendaAvulsaVO();
        }
        return vendaAvulsa;
    }

    public void setVendaAvulsa(VendaAvulsaVO vendaAvulsa) {
        this.vendaAvulsa = vendaAvulsa;
    }

    public MovProdutoVO getMovProduto() {
        if(movProduto == null){
            movProduto = new MovProdutoVO();
            movProduto.setDataInicioVigencia(Calendario.hoje());
        }
        return movProduto;
    }
    public AluguelArmarioVO getClone(){
        AluguelArmarioVO aluguelArmarioVO = new AluguelArmarioVO();
        aluguelArmarioVO.setVendaAvulsa(getVendaAvulsa());
        aluguelArmarioVO.setCliente(getCliente());
        aluguelArmarioVO.setDataCadastro(getDataCadastro());
        aluguelArmarioVO.setArmario(getArmario());
        aluguelArmarioVO.setProduto(getProduto());
        aluguelArmarioVO.setFimOriginal(getFimOriginal());
        aluguelArmarioVO.setResponsavelCadastro(getResponsavelCadastro());
        aluguelArmarioVO.setValor(getValor());
        aluguelArmarioVO.setDataInicio(getInicio());
        aluguelArmarioVO.setDataRenovacaoAutomatica(getDataRenovacaoAutomatica());
        aluguelArmarioVO.setRenovarAutomatico(getRenovarAutomatico());
        aluguelArmarioVO.setCodigo(getCodigo());
        aluguelArmarioVO.setContratoAssinado(getContratoAssinado());
        return aluguelArmarioVO;
    }

    public void setMovProduto(MovProdutoVO movProduto) {
        this.movProduto = movProduto;
    }

    public Double getValor() {
        return valor;
    }

    public void setValor(Double valor) {
        this.valor = valor;
    }

    public ProdutoVO getProduto() {
        if(produto == null){
            produto = new ProdutoVO();
        }
        return produto;
    }

    public void setProduto(ProdutoVO produto) {
        this.produto = produto;
    }

    public UsuarioVO getResponsavelCadastro() {
        if(responsavelCadastro == null){
            responsavelCadastro = new UsuarioVO();
        }
        return responsavelCadastro;
    }

    public void setResponsavelCadastro(UsuarioVO responsavelCadastro) {
        this.responsavelCadastro = responsavelCadastro;
    }

    public Date getDataCadastro() {
        return dataCadastro;
    }

    public void setDataCadastro(Date dataCadastro) {
        this.dataCadastro = dataCadastro;
    }
    
    public String getDataCadastroApresentar() {
        return (Uteis.getDataComHHMM(dataCadastro));
        }
    public String getDataCadastro_Apresentar() {
        return (Uteis.getData(dataCadastro));
    }
    public Date getFimOriginal() {
        return fimOriginal;
    }

    public void setFimOriginal(Date fimOriginal) {
        this.fimOriginal = fimOriginal;
    }
    
    public String getNomePessoa(){
        return getCliente().getPessoa().getNome();
    }
    
    public String getNomeProduto(){
        return getMovProduto().getProduto_Apresentar();
    }
    
    public Date getInicio(){
        return getMovProduto().getDataInicioVigencia();
    }
    
    public Date getFim(){
        return getMovProduto().getDataFinalVigencia();
    }

    public Boolean getContratoAssinado() {
        return contratoAssinado;
    }

    public Date getDataInicio() {
        return dataInicio;
    }

    public AluguelArmarioVO getRelacionamentoRenovacao() {
        if(relacionamentoRenovacao==null)
            relacionamentoRenovacao = new AluguelArmarioVO();
        return relacionamentoRenovacao;
    }

    public void setRelacionamentoRenovacao(AluguelArmarioVO relacionamentoRenovacao) {
        this.relacionamentoRenovacao = relacionamentoRenovacao;
    }

    public void setDataInicio(Date dataInicio) {
        this.dataInicio = dataInicio;
    }

    public void setContratoAssinado(Boolean contratoAssinado) {
        this.contratoAssinado = contratoAssinado;
    }

    public Boolean getRenovarAutomatico() {
        return renovarAutomatico;
    }

    public void setRenovarAutomatico(Boolean renovarAutomatico) {
        this.renovarAutomatico = renovarAutomatico;
    }

    public Date getDataRenovacaoAutomatica() {
        return dataRenovacaoAutomatica;
    }

    public void setDataRenovacaoAutomatica(Date dataRenovacaoAutomatica) {
        this.dataRenovacaoAutomatica = dataRenovacaoAutomatica;
    }

    public Boolean getChaveDevolvida() {
        return chaveDevolvida;
    }

    public void setChaveDevolvida(Boolean chaveDevolvida) {
        this.chaveDevolvida = chaveDevolvida;
    }
}

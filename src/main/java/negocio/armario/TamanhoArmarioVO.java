/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package negocio.armario;

import negocio.comuns.arquitetura.SuperVO;

/**
 *
 * <AUTHOR>
 */
public class TamanhoArmarioVO extends SuperVO {

    private String descricao;

    public TamanhoArmarioVO() {
    }

    public TamanhoArmarioVO(String descricao) {
        this.descricao = descricao;
    }
    
    public String getDescricao() {
        return descricao;
    }

    public void setDescricao(String descricao) {
        this.descricao = descricao;
    }

    public void realizarUpperCaseDados() {
        setDescricao(getDescricao().toUpperCase());
    }
    
    @Override
    public boolean equals(Object obj) {
        if ((obj == null) || (!(obj instanceof TamanhoArmarioVO))) {
            return false;
        }
        return ((TamanhoArmarioVO) obj).getCodigo().equals(this.getCodigo());
    }

    @Override
    public int hashCode() {
        return getCodigo().hashCode();
    }
}

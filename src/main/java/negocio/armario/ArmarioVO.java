/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package negocio.armario;

import annotations.arquitetura.ChaveEstrangeira;
import annotations.arquitetura.NaoControlarLogAlteracao;
import br.com.pactosolucoes.comuns.util.StringUtilities;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import negocio.comuns.arquitetura.SuperVO;
import negocio.comuns.arquitetura.UsuarioVO;
import negocio.comuns.basico.EmpresaVO;

/**
 *
 * <AUTHOR>
 */
public class ArmarioVO  extends SuperVO {
    
    private String descricao = "";
    @ChaveEstrangeira
    private TamanhoArmarioVO tamanhoArmario;
    private Integer numeracao;
    private GrupoArmarioEnum grupo;
    @ChaveEstrangeira
    private UsuarioVO responsavelCadastro = new UsuarioVO();
    @ChaveEstrangeira
    private EmpresaVO empresa = new EmpresaVO();
    private Date dataCadastro;
    private StatusArmarioEnum status;
    @NaoControlarLogAlteracao
    private AluguelArmarioVO aluguelAtual;
    @NaoControlarLogAlteracao
    private List<AluguelArmarioVO> historicoAluguel;
    
    public ArmarioVO() {
    }

    public boolean isInativo(){
        return status != null && status.equals(StatusArmarioEnum.INATIVO);
    }

    public boolean isFechado(){
        return status != null && status.equals(StatusArmarioEnum.FECHADO);
    }
    
    public boolean isAberto(){
        return status == null || status.equals(StatusArmarioEnum.ABERTO);
    }
    
    public ArmarioVO(TamanhoArmarioVO tamanhoArmario, Integer descricao, GrupoArmarioEnum grupo, 
            UsuarioVO responsavelCadastro, Integer empresa) {
        this.tamanhoArmario = tamanhoArmario;
        this.numeracao = descricao;
        this.descricao = StringUtilities.formatarCampoForcandoZerosAEsquerda(descricao.toString(), 3);
        this.grupo = grupo;
        this.responsavelCadastro = responsavelCadastro;
        this.empresa = new EmpresaVO();
        this.empresa.setCodigo(empresa);
    }
    public ArmarioVO getClone(){

        ArmarioVO armario = new ArmarioVO();
        armario.setCodigo(getCodigo());
        armario.setAluguelAtual(getAluguelAtual());
        armario.setResponsavelCadastro(getResponsavelCadastro());
        armario.setDataCadastro(getDataCadastro());
        armario.setDescricao(getDescricao());
        armario.setEmpresa(getEmpresa());
        armario.setGrupo(getGrupo());
        armario.setHistoricoAluguel(getHistoricoAluguel());
        armario.setNumeracao(getNumeracao());
        armario.setTamanhoArmario(getTamanhoArmario());
        armario.setStatus(getStatus());
        return armario;
    }
    public String getDescricao() {
        return descricao;
    }

    public void setDescricao(String descricao) {
        this.descricao = descricao;
    }

    public TamanhoArmarioVO getTamanhoArmario() {
        if(tamanhoArmario == null){
            tamanhoArmario = new TamanhoArmarioVO();
        }
        return tamanhoArmario;
    }

    public void setTamanhoArmario(TamanhoArmarioVO tamanhoArmario) {
        this.tamanhoArmario = tamanhoArmario;
    }

    public Integer getNumeracao() {
        return numeracao;
    }

    public void setNumeracao(Integer numeracao) {
        this.numeracao = numeracao;
    }

    public GrupoArmarioEnum getGrupo() {
        return grupo;
    }

    public void setGrupo(GrupoArmarioEnum grupo) {
        this.grupo = grupo;
    }

    public UsuarioVO getResponsavelCadastro() {
        return responsavelCadastro;
    }

    public void setResponsavelCadastro(UsuarioVO responsavelCadastro) {
        this.responsavelCadastro = responsavelCadastro;
    }

    public Date getDataCadastro() {
        return dataCadastro;
    }

    public void setDataCadastro(Date dataCadastro) {
        this.dataCadastro = dataCadastro;
    }
    
    public void realizarUpperCaseDados() {
        setDescricao(getDescricao().toUpperCase());
    }

    public StatusArmarioEnum getStatus() {
        return status;
    }

    public void setStatus(StatusArmarioEnum status) {
        this.status = status;
    }

    public EmpresaVO getEmpresa() {
        return empresa;
    }

    public void setEmpresa(EmpresaVO empresa) {
        this.empresa = empresa;
    }

    public AluguelArmarioVO getAluguelAtual() {
        if(aluguelAtual == null){
            aluguelAtual = new AluguelArmarioVO();
        }
        return aluguelAtual;
    }

    public void setAluguelAtual(AluguelArmarioVO aluguelAtual) {
        this.aluguelAtual = aluguelAtual;
    }

    public List<AluguelArmarioVO> getHistoricoAluguel() {
        if(historicoAluguel == null){
            historicoAluguel = new ArrayList<AluguelArmarioVO>();
        }
        return historicoAluguel;
    }

    public void setHistoricoAluguel(List<AluguelArmarioVO> historicoAluguel) {
        this.historicoAluguel = historicoAluguel;
    }
    
    public Long getDataFinalVigencia(){
        return getStatus() != null && getStatus().equals(StatusArmarioEnum.FECHADO)
                && getAluguelAtual() != null && getAluguelAtual().getMovProduto() != null 
                && getAluguelAtual().getMovProduto().getDataFinalVigencia() != null
                ? getAluguelAtual().getMovProduto().getDataFinalVigencia().getTime()
                : 0;
    }
    
}

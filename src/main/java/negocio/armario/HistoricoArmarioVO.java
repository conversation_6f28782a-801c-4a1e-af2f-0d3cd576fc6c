package negocio.armario;

import negocio.comuns.arquitetura.SuperVO;
import negocio.comuns.arquitetura.UsuarioVO;

import java.util.Date;

/**
 * Created by <PERSON> on 11/11/2015.
 */
public class HistoricoArmarioVO extends SuperVO {

    private int codigo;
    private AluguelArmarioVO aluguel;
    private String descricao;
    private Date dataOperacao;
    private OperacaoArmarioEnum operacao;
    private UsuarioVO reponsavel;

    @Override
    public Integer getCodigo() {
        return codigo;
    }

    public void setCodigo(int codigo) {
        this.codigo = codigo;
    }

    public AluguelArmarioVO getAluguel() {
        return aluguel;
    }

    public void setAluguel(AluguelArmarioVO aluguel) {
        this.aluguel = aluguel;
    }

    public String getDescricao() {
        return descricao;
    }
    public String getNomePessoa(){
        return getAluguel().getNomePessoa();
    }
    public String getNomeProduto(){
        return getAluguel().getNomeProduto();
    }
    public String getInicio(){
        return getAluguel().getInicioApresentar();
    }
    public String getFim(){
        return getAluguel().getFimApresentar();
    }
    public Date getFimOriginal(){
        return getAluguel().getFimOriginal();
    }
    public String getNomeResponsavel(){
        return getAluguel().getResponsavelCadastro().getNome();
    }
    public String getDataCadastro(){
        return getAluguel().getDataCadastro_Apresentar();
    }
    public void setDescricao(String descricao) {
        this.descricao = descricao;
    }

    public Date getDataOperacao() {
        return dataOperacao;
    }

    public void setDataOperacao(Date dataOperacao) {
        this.dataOperacao = dataOperacao;
    }

    public OperacaoArmarioEnum getOperacao() {
        return operacao;
    }

    public void setOperacao(OperacaoArmarioEnum operacao) {
        this.operacao = operacao;
    }

    public UsuarioVO getReponsavel() {
        return reponsavel;
    }

    public void setReponsavel(UsuarioVO reponsavel) {
        this.reponsavel = reponsavel;
    }
}

package negocio.armario;

import negocio.facade.jdbc.crm.AberturaMeta;

/**
 * Created by <PERSON> on 11/11/2015.
 */
public enum OperacaoArmarioEnum {

    LOCACAO(0,"Locação Armário"),
    ABERTURA(1,"Abertura Armário"),
    TROCA_ARMARIO(2,"Abertura Armário"),
    RENOVACAO(3,"Renovacao Armário");
    private int codigo;
    private String descricao;

    private OperacaoArmarioEnum(int codigo, String label){
        this.codigo = codigo;
        this.descricao = label;
    }
    public static OperacaoArmarioEnum obterPorCodigo(int codigo){

        for(OperacaoArmarioEnum item : OperacaoArmarioEnum.values()){
            if(item.getCodigo() == codigo){
                return item;
            }
        }
        return null;
    }
    public int getCodigo() {
        return codigo;
    }

    public void setCodigo(int codigo) {
        this.codigo = codigo;
    }

    public String getDescricao() {
        return descricao;
    }

    public void setDescricao(String descricao) {
        this.descricao = descricao;
    }
}

/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package negocio.armario;

import br.com.pactosolucoes.comuns.util.JSFUtilities;
import java.util.List;
import javax.faces.model.SelectItem;

/**
 *
 * <AUTHOR>
 */
public enum GrupoArmarioEnum {
    FEMININO("F", "Feminino"),
    MASCULINO("M", "Masculino"),
    UNISSEX("U", "Unissex");
    
    private String codigo;
    private String label;

    private GrupoArmarioEnum(String codigo, String label) {
        this.codigo = codigo;
        this.label = label;
    }

    public String getCodigo() {
        return codigo;
    }

    public void setCodigo(String codigo) {
        this.codigo = codigo;
    }

    public String getLabel() {
        return label;
    }

    public void setLabel(String label) {
        this.label = label;
    }
    
    public static GrupoArmarioEnum obterPorCodigo(String codigo){
        for(GrupoArmarioEnum grupo : GrupoArmarioEnum.values()){
            if(grupo.getCodigo().equals(codigo)){
                return grupo;
            }
        }
        return null;
    }
    
    public static List<SelectItem> obterSelectItem(){
        return JSFUtilities.getSelectItemListFromEnum(GrupoArmarioEnum.class, "label", false);
    }
    
    
}

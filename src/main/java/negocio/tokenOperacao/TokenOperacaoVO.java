package negocio.tokenOperacao;

import com.sun.xml.ws.api.tx.at.Transactional;
import negocio.comuns.arquitetura.UsuarioVO;
import negocio.comuns.utilitarias.ConsistirException;
import negocio.comuns.utilitarias.UteisValidacao;

import java.text.SimpleDateFormat;
import java.util.Date;

/**
 * Created with IntelliJ IDEA.
 * User: <PERSON>
 * Date: 14/11/2023
 */

public class TokenOperacaoVO {
    private Integer codigo;
    private Date dataRegistro;
    private int usuario;
    private String token;
    private boolean utilizado;
    @Transactional
    private UsuarioVO usuarioVO;

    public static void validarDados(TokenOperacaoVO obj) throws ConsistirException {
        if (UteisValidacao.emptyNumber(obj.getUsuario())) {
            throw new ConsistirException("Não foi informado o usuário.");
        }
        if (UteisValidacao.emptyString(obj.getToken())) {
            throw new ConsistirException("Não foi informado o token.");
        }
    }

    public Integer getCodigo() {
        return codigo;
    }

    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public Date getDataRegistro() {
        return dataRegistro;
    }

    public void setDataRegistro(Date dataRegistro) {
        this.dataRegistro = dataRegistro;
    }

    public int getUsuario() {
        return usuario;
    }

    public void setUsuario(int usuario) {
        this.usuario = usuario;
    }

    public String getToken() {
        return token;
    }

    public void setToken(String token) {
        this.token = token;
    }

    public boolean isUtilizado() {
        return utilizado;
    }

    public void setUtilizado(boolean utilizado) {
        this.utilizado = utilizado;
    }

    public UsuarioVO getUsuarioVO() {
        return usuarioVO;
    }

    public void setUsuarioVO(UsuarioVO usuarioVO) {
        this.usuarioVO = usuarioVO;
    }

    public String getData_Apresentar() {
        if (getDataRegistro() != null) {
            return (new SimpleDateFormat("dd/MM/yyyy HH:mm:ss")).format(this.dataRegistro);
        }
        return "";
    }
    public String getUsuario_Apresentar() {
        if (getUsuarioVO() != null && !UteisValidacao.emptyString(getUsuarioVO().getNome())) {
            return getUsuarioVO().getNome();
        }
        return "";
    }

    public String getUtilizado_Apresentar() {
        if (isUtilizado()) {
            return "SIM";
        }
        return "NÃO";
    }
}

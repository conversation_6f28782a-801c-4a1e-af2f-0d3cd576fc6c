package negocio.tokenOperacao;

import negocio.comuns.utilitarias.Calendario;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisValidacao;
import negocio.facade.jdbc.arquitetura.SuperEntidade;
import negocio.facade.jdbc.arquitetura.Usuario;
import negocio.interfaces.basico.TokenOperacaoInterfaceFacade;
import servicos.propriedades.PropsService;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * Created with IntelliJ IDEA.
 * User: <PERSON>
 * Date: 14/11/2023
 */
public class TokenOperacao extends SuperEntidade implements TokenOperacaoInterfaceFacade {

    public TokenOperacao() throws Exception {
    }

    public TokenOperacao(Connection con) throws Exception {
        super(con);
    }

    @Override
    public void incluir(TokenOperacaoVO obj) throws Exception {
        TokenOperacaoVO.validarDados(obj);

        String sql = "INSERT INTO tokenOperacao(dataRegistro, usuario, token)"
                + "VALUES (?,?,?)";

        try (PreparedStatement sqlInserir = con.prepareStatement(sql)) {

            sqlInserir.setTimestamp(1, Uteis.getDataJDBCTimestamp(obj.getDataRegistro()));
            sqlInserir.setInt(2, obj.getUsuario());
            sqlInserir.setString(3, obj.getToken());

            sqlInserir.execute();
        }
    }

    @Override
    public TokenOperacaoVO consultarUltimoGeradoPeloUsuario(int usuario) throws Exception {
        if (UteisValidacao.emptyNumber(usuario)) {
            throw new Exception("Não foi informado o usuario para consultar o token.");
        }

        String sql = "SELECT * from tokenoperacao t \n" +
                "WHERE usuario = " + usuario + " \n" +
                "AND utilizado = false \n" +
                "ORDER BY codigo desc limit 1";

        try (ResultSet rs = con.prepareStatement(sql).executeQuery()) {
            while (rs.next()) {
                Date data = Uteis.getDate(Uteis.getDataAplicandoFormatacao(rs.getTimestamp("dataregistro"), "dd/MM/yyyy HH:mm:ss"), "dd/MM/yyyy HH:mm:ss");
                Integer tempoSegundosExpiracaoToken = Integer.valueOf(PropsService.getPropertyValue(PropsService.tempoSegundosExpiracaoTokenOperacao));
                if (Calendario.diferencaEmSegundos(data, Calendario.hoje()) <= tempoSegundosExpiracaoToken) {
                    return montarDados(rs);
                }
            }
        }
        return null;
    }

    public TokenOperacaoVO consultarPeloToken(String token) throws Exception {
        try {
            if (UteisValidacao.emptyString(token)) {
                return null;
            }

            String sql = "SELECT * from tokenoperacao t \n" +
                    "WHERE token = '" + token + "' \n";

            try (ResultSet rs = con.prepareStatement(sql).executeQuery()) {
                while (rs.next()) {
                    return montarDados(rs);
                }
            }
            return null;
        } catch (Exception ignore) {
            return null;
        }
    }

    public void inutilizarToken(String token) throws Exception {
        try {
            if (UteisValidacao.emptyString(token)) {
                throw new Exception("Token não informado para inativar");
            }

            String update = "UPDATE tokenoperacao t \n" +
                    "SET utilizado = true \n" +
                    "WHERE token = '" + token + "' \n";

            try (PreparedStatement stm = con.prepareStatement(update)) {
                stm.execute();
            }
        } catch (Exception ex) {
            throw ex;
        }
    }

    @Override
    public List<TokenOperacaoVO> consultarPorPeriodo(Date dataInicial, Date dataFinal) throws Exception {
        String sql = "SELECT * " +
                "FROM tokenOperacao " +
                "WHERE dataRegistro::date between '" + Uteis.getDataFormatoBD(dataInicial) + "' and '" + Uteis.getDataFormatoBD(dataFinal) + "' " +
                "ORDER BY dataRegistro;";

        PreparedStatement statement = con.prepareStatement(sql);
        ResultSet resultSet = statement.executeQuery();
        List<TokenOperacaoVO> tokensOperacoresVOS = new ArrayList<>();
        while (resultSet.next()) {
            tokensOperacoresVOS.add(montarDados(resultSet));
        }
        return tokensOperacoresVOS;
    }

    private TokenOperacaoVO montarDados(ResultSet rs) throws Exception {
        TokenOperacaoVO tokenOperacaoVO = new TokenOperacaoVO();
        tokenOperacaoVO.setCodigo(rs.getInt("codigo"));
        tokenOperacaoVO.setUsuario(rs.getInt("usuario"));
        tokenOperacaoVO.setDataRegistro(rs.getTimestamp("dataRegistro"));
        tokenOperacaoVO.setToken(rs.getString("token"));
        tokenOperacaoVO.setUtilizado(rs.getBoolean("utilizado"));
        if (!UteisValidacao.emptyNumber(rs.getInt("usuario"))) {
            Usuario usuarioDAO;
            try {
                usuarioDAO = new Usuario(getCon());
                tokenOperacaoVO.setUsuarioVO(usuarioDAO.consultarPorCodigo(tokenOperacaoVO.getUsuario(), Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA));

            } catch (Exception ex) {

            } finally {
                usuarioDAO = null;
            }
        }
        return tokenOperacaoVO;
    }
}

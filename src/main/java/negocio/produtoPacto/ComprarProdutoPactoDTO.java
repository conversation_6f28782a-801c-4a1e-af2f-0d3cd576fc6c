package negocio.produtoPacto;

public class ComprarProdutoPactoDTO {

    //DADOS DO CLIENTE
    private boolean clienteNovo = false;
    private String responsavel;
    private String cpf;
    private String email;
    private String celular;
    private String senha;

    //DADOS DA EMPRESA
    private String chave;
    private Integer empresa;
    private Integer codFinanceiro;

    private String tokenSMS;
    private String nomeFantasia;
    private String razaoSocial;
    private String cnpj;


    //DADOS DA COMPRA
    private Integer idProdutoPacto;
    private Integer qtdParcelas;
    private Integer limiteUnico;
    private String sistemaOrigem;
    private String cupom;
    private Integer qtdAvulso;


    private String captcha;
    private String cep = "";

    public Integer getLimiteUnico() {
        return limiteUnico;
    }

    public void setLimiteUnico(Integer limiteUnico) {
        this.limiteUnico = limiteUnico;
    }

    public Integer getQtdAvulso() {
        return qtdAvulso;
    }

    public void setQtdAvulso(Integer qtdAvulso) {
        this.qtdAvulso = qtdAvulso;
    }

    public Integer getIdProdutoPacto() {
        if (idProdutoPacto == null) {
            idProdutoPacto = 0;
        }
        return idProdutoPacto;
    }

    public void setIdProdutoPacto(Integer idProdutoPacto) {
        this.idProdutoPacto = idProdutoPacto;
    }

    public Integer getQtdParcelas() {
        return qtdParcelas;
    }

    public void setQtdParcelas(Integer qtdParcelas) {
        this.qtdParcelas = qtdParcelas;
    }

    public String getSistemaOrigem() {
        if (sistemaOrigem == null) {
            sistemaOrigem = "";
        }
        return sistemaOrigem;
    }

    public void setSistemaOrigem(String sistemaOrigem) {
        this.sistemaOrigem = sistemaOrigem;
    }

    public String getCupom() {
        if (cupom == null) {
            cupom = "";
        }
        return cupom;
    }

    public void setCupom(String cupom) {
        this.cupom = cupom;
    }

    public String getCaptcha() {
        return captcha;
    }

    public void setCaptcha(String captcha) {
        this.captcha = captcha;
    }

    public String getChave() {
        if (chave == null) {
            chave = "";
        }
        return chave;
    }

    public void setChave(String chave) {
        this.chave = chave;
    }

    public Integer getEmpresa() {
        if (empresa == null) {
            empresa = 0;
        }
        return empresa;
    }

    public void setEmpresa(Integer empresa) {
        this.empresa = empresa;
    }

    public String getEmail() {
        if (email == null) {
            email = "";
        }
        return email;
    }

    public void setEmail(String email) {
        this.email = email;
    }

    public String getCelular() {
        if (celular == null) {
            celular = "";
        }
        return celular;
    }

    public void setCelular(String celular) {
        this.celular = celular;
    }

    public String getSenha() {
        if (senha == null) {
            senha = "";
        }
        return senha;
    }

    public void setSenha(String senha) {
        this.senha = senha;
    }

    public String getNomeFantasia() {
        if (nomeFantasia == null) {
            nomeFantasia = "";
        }
        return nomeFantasia;
    }

    public void setNomeFantasia(String nomeFantasia) {
        this.nomeFantasia = nomeFantasia;
    }

    public String getRazaoSocial() {
        if (razaoSocial == null) {
            razaoSocial = "";
        }
        return razaoSocial;
    }

    public void setRazaoSocial(String razaoSocial) {
        this.razaoSocial = razaoSocial;
    }

    public boolean isClienteNovo() {
        return clienteNovo;
    }

    public void setClienteNovo(boolean clienteNovo) {
        this.clienteNovo = clienteNovo;
    }

    public String getResponsavel() {
        if (responsavel == null) {
            responsavel = "";
        }
        return responsavel;
    }

    public void setResponsavel(String responsavel) {
        this.responsavel = responsavel;
    }

    public String getTokenSMS() {
        if (tokenSMS == null) {
            tokenSMS = "";
        }
        return tokenSMS;
    }

    public void setTokenSMS(String tokenSMS) {
        this.tokenSMS = tokenSMS;
    }

    public String getCnpj() {
        if (cnpj == null) {
            cnpj = "";
        }
        return cnpj;
    }

    public void setCnpj(String cnpj) {
        this.cnpj = cnpj;
    }

    public String getCpf() {
        if (cpf == null) {
            cpf = "";
        }
        return cpf;
    }

    public void setCpf(String cpf) {
        this.cpf = cpf;
    }

    public Integer getCodFinanceiro() {
        return codFinanceiro;
    }

    public void setCodFinanceiro(Integer codFinanceiro) {
        this.codFinanceiro = codFinanceiro;
    }

    public void setCep(String cep) {
        this.cep = cep;
    }

    public String getCep() {
        return cep;
    }
}

package negocio.oamd;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import negocio.comuns.arquitetura.SuperVO;
import org.json.JSONObject;

/**
 * Created by ulisses on 30/06/2016.
 */
@JsonIgnoreProperties(ignoreUnknown = true)
public class EmpresaFinanceiroVO extends SuperVO {

    private Integer codigo;
    private String nomeFantasia;
    private String chaveZW;
    private Integer empresazw;
    private Integer identificadorRemessa;
    private Integer codigoFinanceiro;
    private String tempoMedioAtendimento;

    public EmpresaFinanceiroVO() {
    }

    public EmpresaFinanceiroVO(Integer codigoFinanceiro) {
        this.codigoFinanceiro = codigoFinanceiro;
    }

    public String getNomeFantasia() {
        return nomeFantasia;
    }

    public void setNomeFantasia(String nomeFantasia) {
        this.nomeFantasia = nomeFantasia;
    }

    public String getChaveZW() {
        return chaveZW;
    }

    public void setChaveZW(String chaveZW) {
        this.chaveZW = chaveZW;
    }

    @Override
    public Integer getCodigo() {
        return codigo;
    }

    @Override
    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public Integer getIdentificadorRemessa() {
        return identificadorRemessa;
    }

    public void setIdentificadorRemessa(Integer identificadorRemessa) {
        this.identificadorRemessa = identificadorRemessa;
    }

    public Integer getCodigoFinanceiro() {
        return codigoFinanceiro;
    }

    public void setCodigoFinanceiro(Integer codigoFinanceiro) {
        this.codigoFinanceiro = codigoFinanceiro;
    }

    public String getTempoMedioAtendimento() {
        return tempoMedioAtendimento;
    }

    public void setTempoMedioAtendimento(String tempoMedioAtendimento) {
        this.tempoMedioAtendimento = tempoMedioAtendimento;
    }

    public Integer getEmpresazw() {
        return empresazw;
    }

    public void setEmpresazw(Integer empresazw) {
        this.empresazw = empresazw;
    }

    public String toJSON() {
        JSONObject json = new JSONObject();
        json.put("codigo", getCodigo());
        json.put("codigoFinanceiro", getCodigoFinanceiro());
        return json.toString();
    }
}

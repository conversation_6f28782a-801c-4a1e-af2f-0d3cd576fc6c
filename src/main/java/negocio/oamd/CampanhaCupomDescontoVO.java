package negocio.oamd;

import annotations.arquitetura.Lista;
import annotations.arquitetura.NaoControlarLogAlteracao;
import br.com.pactosolucoes.enumeradores.TipoDistribuicaoCupomDescontoEnum;
import br.com.pactosolucoes.enumeradores.TipoPremioCupomDescontoEnum;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import negocio.comuns.arquitetura.SuperVO;
import negocio.comuns.utilitarias.Calendario;
import negocio.comuns.utilitarias.ConsistirException;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UtilReflection;
import org.json.JSONArray;
import org.json.JSONException;
import org.json.JSONObject;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Random;

/**
 * Created by ulisses on 26/05/2016.
 */
@JsonIgnoreProperties(ignoreUnknown = true)
public class CampanhaCupomDescontoVO extends SuperVO {

    private Integer id;
    @NaoControlarLogAlteracao
    private RedeEmpresaVO redeEmpresaVO;
    @NaoControlarLogAlteracao
    @JsonProperty("empresaFinanceiro")
    private EmpresaFinanceiroVO empresaFinanceiroVO;
    private String descricaoCampanha;
    private Integer quantidadeCupomGerar;
    private Integer quantidadeCupomExtra;
    private Date  vigenciaInicial;
    private Date vigenciaFinal;
    private Integer tipoPremio;
    private Integer tipoDistribuicaoCupom;
    private String nomeProdutoPremioPortadorCupom;
    private Integer totalLote;
    private Integer seedAux;
    private String msgResultadoProcessamento;
    private String planosQueParticiparaoDaCampanha;
    @NaoControlarLogAlteracao
    private Boolean aplicarParaRede;
    @NaoControlarLogAlteracao
    private List<CampanhaCupomDescontoPremioPortadorVO> listaPremioPortador;
    @NaoControlarLogAlteracao
    private List<CampanhaCupomDescontoPremioPortadorVO> listaPremioPortadorAntesDeAlterar;
    @NaoControlarLogAlteracao
    private Integer totalCupomUtilizado; // atributo transient
    @NaoControlarLogAlteracao
    private List<CupomDescontoVO> listaCupom;

    public String toMinimalJSON() throws JSONException {
        JSONObject json = new JSONObject();
        json.put("id", this.id);
        return json.toString();
    }

    public String toJSON() throws JSONException {
        JSONObject json = new JSONObject();
        json.put("id", this.id);
        if (getAplicarParaRede()) {
            json.put("redeEmpresaVO", this.redeEmpresaVO != null ? new JSONObject(this.redeEmpresaVO.toJSON()) : null);
        } else {
            json.put("empresaFinanceiro", this.empresaFinanceiroVO != null ? new JSONObject(this.empresaFinanceiroVO.toJSON()) : null);
        }
        json.put("descricaoCampanha", this.descricaoCampanha);
        json.put("quantidadeCupomGerar", this.quantidadeCupomGerar);
        json.put("quantidadeCupomExtra", this.quantidadeCupomExtra);
        json.put("vigenciaInicial", this.vigenciaInicial != null ? this.vigenciaInicial.getTime(): null);
        json.put("vigenciaFinal", this.vigenciaFinal != null ? this.vigenciaFinal.getTime(): null);
        json.put("tipoPremio", this.tipoPremio);
        json.put("tipoDistribuicaoCupom", this.tipoDistribuicaoCupom);
        json.put("nomeProdutoPremioPortadorCupom", this.nomeProdutoPremioPortadorCupom);
        json.put("totalLote", this.totalLote);
        json.put("seedAux", this.seedAux);
        json.put("msgResultadoProcessamento", this.msgResultadoProcessamento);
        json.put("planosQueParticiparaoDaCampanha", this.planosQueParticiparaoDaCampanha);
        JSONArray arrayPremioPortador = new JSONArray();
        if(this.listaPremioPortador != null) {
            for (CampanhaCupomDescontoPremioPortadorVO ccdpp : this.listaPremioPortador) {
                arrayPremioPortador.put(new JSONObject(ccdpp.toJSON()));
            }
        }
        json.put("listaPremioPortador", arrayPremioPortador.length() == 0 ? null : arrayPremioPortador);
        JSONArray arrayPremioPortadorAntesDeAlterar = new JSONArray();
        if(this.listaPremioPortadorAntesDeAlterar != null) {
            for (CampanhaCupomDescontoPremioPortadorVO ccdpp : this.listaPremioPortadorAntesDeAlterar) {
                arrayPremioPortadorAntesDeAlterar.put(new JSONObject(ccdpp.toJSON()));
            }
        }
        json.put("listaPremioPortadorAntesDeAlterar", arrayPremioPortadorAntesDeAlterar.length() == 0 ? null : arrayPremioPortadorAntesDeAlterar);

        json.put("totalCupomUtilizado", this.totalCupomUtilizado);

        JSONArray arrayListaCupom = new JSONArray();
        if(this.listaCupom != null) {
            for (CupomDescontoVO cd : this.listaCupom) {
                arrayListaCupom.put(new JSONObject(cd.toJSON()));
            }
        }
        json.put("listaCupom", arrayListaCupom.length() == 0 ? null : arrayListaCupom);
        return json.toString();
    }

    public Integer getId() {
        if (id == null) {
            id = 0;
        }
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public RedeEmpresaVO getRedeEmpresaVO() {
        return redeEmpresaVO;
    }

    public void setRedeEmpresaVO(RedeEmpresaVO redeEmpresaVO) {
        this.redeEmpresaVO = redeEmpresaVO;
    }

    public String getDescricaoCampanha() {
        if (descricaoCampanha == null) {
            descricaoCampanha = "";
        }
        return descricaoCampanha;
    }

    public void setDescricaoCampanha(String descricaoCampanha) {
        this.descricaoCampanha = descricaoCampanha;
    }

    public Integer getQuantidadeCupomGerar() {
        if (quantidadeCupomGerar == null) {
            quantidadeCupomGerar = 0;
        }
        return quantidadeCupomGerar;
    }

    public void setQuantidadeCupomGerar(Integer quantidadeCupomGerar) {
        this.quantidadeCupomGerar = quantidadeCupomGerar;
    }

    public Integer getTipoPremio() {
        if (tipoPremio == null) {
            tipoPremio = 0;
        }
        return tipoPremio;
    }

    public void setTipoPremio(Integer tipoPremio) {
        this.tipoPremio = tipoPremio;
    }

    public Integer getTipoDistribuicaoCupom() {
        if (tipoDistribuicaoCupom == null) {
            tipoDistribuicaoCupom = 0;
        }
        return tipoDistribuicaoCupom;
    }

    public void setTipoDistribuicaoCupom(Integer tipoDistribuicaoCupom) {
        this.tipoDistribuicaoCupom = tipoDistribuicaoCupom;
    }

    public TipoDistribuicaoCupomDescontoEnum getTipoDistribuicaoCupomDesconto(){
        return TipoDistribuicaoCupomDescontoEnum.getTipo(this.getTipoDistribuicaoCupom());
    }

    public TipoPremioCupomDescontoEnum getTipoPremioCupom(){
        return TipoPremioCupomDescontoEnum.getTipo(this.getTipoPremio());
    }

    public static void validarDados(CampanhaCupomDescontoVO obj) throws Exception {
        if (!obj.getValidarDados()) {
            return;
        }
        if ((obj.getDescricaoCampanha() == null) || (obj.getDescricaoCampanha().trim().equals(""))) {
            throw new ConsistirException("O campo Descricao Campanha (CampanhaCupomDesconto) deve ser informado.");
        }
        if (obj.getVigenciaInicial() == null) {
            throw new ConsistirException("O campo Vigência De (CampanhaCupomDesconto) deve ser informado.");
        }
        if (obj.getVigenciaFinal() == null) {
            throw new ConsistirException("O campo Vigência Até (CampanhaCupomDesconto) deve ser informado.");
        }
        if (Calendario.maior(obj.getVigenciaInicial(), obj.getVigenciaFinal())) {
            throw new ConsistirException("O campo 'Vigência De' deve ser menor que o campo 'Vigência Até'.");
        }
        if ((obj.getTipoPremio() == null) || (obj.getTipoPremio() <= 0)){
            throw new ConsistirException("O campo Tipo Prêmio (CampanhaCupomDesconto) deve ser informado.");
        }
        if ((obj.getTipoDistribuicaoCupom() == null) || (obj.getTipoDistribuicaoCupom() <= 0)){
            throw new ConsistirException("O campo Tipo Distribuição Cupom (CampanhaCupomDesconto) deve ser informado.");
        }
        if ((obj.getListaPremioPortador() == null) || (obj.getListaPremioPortador().size() <= 0)){
            throw new ConsistirException("O campo Prêmios Ao Portador do Cupom (CampanhaCupomDesconto) deve ser informado.");
        }
    }

    public Integer gerarSeedAux(){
        Random generator = new Random();
        return generator.nextInt(32600);
    }


    public List<CupomDescontoVO> getListaCupomNomeFixo() {
        List<CupomDescontoVO> cuponsNomeFixo = new ArrayList<>();
        for(CupomDescontoVO cupom : getListaCupom()){
            if(cupom.isCupomNomeFixo()){
                cuponsNomeFixo.add(cupom);
            }
        }
        return cuponsNomeFixo;
    }

    public List<CupomDescontoVO> getListaCupomAleatorios() {
        List<CupomDescontoVO> cuponsAleatorios = new ArrayList<>();
        for(CupomDescontoVO cupom : getListaCupom()){
            if(!cupom.isCupomNomeFixo()){
                cuponsAleatorios.add(cupom);
            }
        }
        return cuponsAleatorios;
    }


    public Date getVigenciaInicial() {
        return vigenciaInicial;
    }

    public void setVigenciaInicial(Date vigenciaInicial) {
        this.vigenciaInicial = vigenciaInicial;
    }

    public Date getVigenciaFinal() {
        return vigenciaFinal;
    }

    public void setVigenciaFinal(Date vigenciaFinal) {
        this.vigenciaFinal = vigenciaFinal;
    }

    public String getVigenciaInicial_Apresentar(){
        return Calendario.getData(this.vigenciaInicial, "dd/MM/yyyy HH:mm:ss");
    }
    public String getVigenciaFinal_Apresentar(){
        return Calendario.getData(this.vigenciaFinal, "dd/MM/yyyy HH:mm:ss");
    }

    public Integer getQuantidadeCupomExtra() {
        if (quantidadeCupomExtra == null) {
            quantidadeCupomExtra = 0;
        }
        return quantidadeCupomExtra;
    }

    public void setQuantidadeCupomExtra(Integer quantidadeCupomExtra) {
        this.quantidadeCupomExtra = quantidadeCupomExtra;
    }

    public Integer getTotalLote() {
        if (totalLote == null) {
            totalLote = 0;
        }
        return totalLote;
    }

    public void setTotalLote(Integer totalLote) {
        this.totalLote = totalLote;
    }

    public List<CupomDescontoVO> getListaCupom() {
        if (listaCupom == null) {
            listaCupom = new ArrayList<CupomDescontoVO>();
        }
        return listaCupom;
    }

    public void setListaCupom(List<CupomDescontoVO> listaCupom) {
        this.listaCupom = listaCupom;
    }

    public Integer getSeedAux() {
        return seedAux;
    }

    public void setSeedAux(Integer seedAux) {
        this.seedAux = seedAux;
    }

    public String getNomeProdutoPremioPortadorCupom() {
        if (nomeProdutoPremioPortadorCupom == null) {
            nomeProdutoPremioPortadorCupom = "";
        }
        return nomeProdutoPremioPortadorCupom;
    }

    public void setNomeProdutoPremioPortadorCupom(String nomeProdutoPremioPortadorCupom) {
        this.nomeProdutoPremioPortadorCupom = nomeProdutoPremioPortadorCupom;
    }

    public Integer getTotalCupomUtilizado() {
        if (totalCupomUtilizado == null) {
            totalCupomUtilizado = 0;
        }
        return totalCupomUtilizado;
    }

    public void setTotalCupomUtilizado(Integer totalCupomUtilizado) {
        this.totalCupomUtilizado = totalCupomUtilizado;
    }

    public String getMsgResultadoProcessamento() {
        return msgResultadoProcessamento;
    }

    public void setMsgResultadoProcessamento(String msgResultadoProcessamento) {
        this.msgResultadoProcessamento = msgResultadoProcessamento;
    }

    public String getPlanosQueParticiparaoDaCampanha() {
        return planosQueParticiparaoDaCampanha;
}

    public void setPlanosQueParticiparaoDaCampanha(String planosQueParticiparaoDaCampanha) {
        this.planosQueParticiparaoDaCampanha = planosQueParticiparaoDaCampanha;
    }

    public List<CampanhaCupomDescontoPremioPortadorVO> getListaPremioPortador() {
        if (this.listaPremioPortador == null){
            this.listaPremioPortador = new ArrayList<CampanhaCupomDescontoPremioPortadorVO>();
        }
        return listaPremioPortador;
    }

    public void setListaPremioPortador(List<CampanhaCupomDescontoPremioPortadorVO> listaPremioPortador) {
        this.listaPremioPortador = listaPremioPortador;
    }

    public List<CampanhaCupomDescontoPremioPortadorVO> getListaPremioPortadorAntesDeAlterar() {
        return listaPremioPortadorAntesDeAlterar;
    }

    public void setListaPremioPortadorAntesDeAlterar(List<CampanhaCupomDescontoPremioPortadorVO> listaPremioPortadorAntesDeAlterar) {
        this.listaPremioPortadorAntesDeAlterar = listaPremioPortadorAntesDeAlterar;
    }

    public Boolean getAplicarParaRede() {
        if (aplicarParaRede == null) {
            aplicarParaRede = false;
        }
        return aplicarParaRede;
    }

    public void setAplicarParaRede(Boolean aplicarParaRede) {
        this.aplicarParaRede = aplicarParaRede;
    }

    public EmpresaFinanceiroVO getEmpresaFinanceiroVO() {
        if (empresaFinanceiroVO == null) {
            empresaFinanceiroVO = new EmpresaFinanceiroVO();
        }
        return empresaFinanceiroVO;
    }

    public void setEmpresaFinanceiroVO(EmpresaFinanceiroVO empresaFinanceiroVO) {
        this.empresaFinanceiroVO = empresaFinanceiroVO;
    }

    @Override
    public void registrarObjetoVOAntesDaAlteracao() {
        super.registrarObjetoVOAntesDaAlteracao();
        if(objetoVOAntesAlteracao != null) {
            ((CampanhaCupomDescontoVO)objetoVOAntesAlteracao).setListaPremioPortador(new ArrayList<>(getListaPremioPortador()));
        }
    }
}

package negocio.oamd;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;

@JsonIgnoreProperties(ignoreUnknown = true)
public class DetalheEmpresaTO {

    private String nivelAtendimento;
    private CustomerSuccessTO customerSuccess;

    public String getNivelAtendimento() {
        return nivelAtendimento;
    }

    public void setNivelAtendimento(String nivelAtendimento) {
        this.nivelAtendimento = nivelAtendimento;
    }

    public CustomerSuccessTO getCustomerSuccess() {
        return customerSuccess;
    }

    public void setCustomerSuccess(CustomerSuccessTO customerSuccess) {
        this.customerSuccess = customerSuccess;
    }
}

package negocio.oamd;

import com.google.gson.Gson;
import negocio.oamd.dto.DiaExtraDTO;
import negocio.oamd.dto.RespostaDTO;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClientBuilder;
import org.apache.http.util.EntityUtils;
import servicos.propriedades.PropsService;

import java.io.IOException;
import java.util.logging.Level;
import java.util.logging.Logger;

/**
 * <AUTHOR>
 * @since 20/02/19
 */
public class DiaExtraService {

    private static final Logger LOGGER = Logger.getLogger(DiaExtraService.class.getSimpleName());

    private static final String ACCEPT_HEADER = "Accept";
    private static final String HEADER_APPLICATION_JSON = "application/json";
    private static final String URL_REGISTRAR_DIA_EXTRA = "/prest/acesso/registrarDiaExtra";
    private static final String UTF_8 = "UTF-8";

    private DiaExtraService() {
    }

    public static DiaExtraService getInstance() {
        return new DiaExtraService();
    }

    public RespostaDTO registrarDiaExtra(final DiaExtraDTO diaExtraDTO) {
        final CloseableHttpClient httpClient = HttpClientBuilder.create().build();
        try {

            final StringEntity body = new StringEntity(new Gson().toJson(diaExtraDTO), UTF_8);
            body.setContentType(HEADER_APPLICATION_JSON);

            final HttpPost post = new HttpPost(PropsService.getPropertyValue(PropsService.urlOamd) + URL_REGISTRAR_DIA_EXTRA);
            post.setHeader(ACCEPT_HEADER, HEADER_APPLICATION_JSON);
            post.setEntity(body);

            return new Gson().fromJson(EntityUtils.toString(httpClient.execute(post).getEntity()), RespostaDTO.class);

        } catch (Exception e) {
            LOGGER.log(Level.SEVERE, "Não foi possível registrar o dia extra", e);
            return null;
        } finally {
            if (httpClient != null) {
                try {
                    httpClient.close();
                } catch (IOException ignored) {
                }
            }
        }
    }

}

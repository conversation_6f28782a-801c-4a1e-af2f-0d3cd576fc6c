package negocio.oamd.dto;

import org.json.JSONObject;

/**
 * <AUTHOR>
 * @since 25/09/2019
 */
public class BackupClienteDTO {
    private String hostBD;
    private String porta;
    private String userBD;
    private String passwordBD;
    private String nomeBD;
    private String modulos;

    public BackupClienteDTO(JSONObject json) {
        this.hostBD = json.optString("hostBD");
        this.porta = json.optString("porta");
        this.userBD = json.optString("userBD");
        this.passwordBD = json.optString("passwordBD");
        this.nomeBD = json.optString("nomeBD");
        this.modulos = json.optString("modulos");
    }

    public String getHostBD() {
        return hostBD;
    }

    public void setHostBD(String hostBD) {
        this.hostBD = hostBD;
    }

    public String getPorta() {
        return porta;
    }

    public void setPorta(String porta) {
        this.porta = porta;
    }

    public String getUserBD() {
        return userBD;
    }

    public void setUserBD(String userBD) {
        this.userBD = userBD;
    }

    public String getPasswordBD() {
        return passwordBD;
    }

    public void setPasswordBD(String passwordBD) {
        this.passwordBD = passwordBD;
    }

    public String getNomeBD() {
        return nomeBD;
    }

    public void setNomeBD(String nomeBD) {
        this.nomeBD = nomeBD;
    }

    public String getModulos() {
        return modulos;
    }

    public void setModulos(String modulos) {
        this.modulos = modulos;
    }
}

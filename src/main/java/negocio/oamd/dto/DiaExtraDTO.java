package negocio.oamd.dto;

/**
 * <AUTHOR>
 * @since 20/02/19
 */
public class DiaExtraDTO {

    private String chave;
    private Integer codigoEmpresa;
    private Integer codigoUsuario;
    private String nomeEmpresa;
    private String nomeUsuario;
    private Long dataDesbloqueio;
    private String mensagem;
    private String telefone;
    private int diasExtras;
    private String usaAppTreino;
    private String sabeDoCanalDoCliente;


    public String getChave() {
        return chave;
    }

    public void setChave(String chave) {
        this.chave = chave;
    }

    public Integer getCodigoEmpresa() {
        return codigoEmpresa;
    }

    public void setCodigoEmpresa(Integer codigoEmpresa) {
        this.codigoEmpresa = codigoEmpresa;
    }

    public Integer getCodigoUsuario() {
        return codigoUsuario;
    }

    public void setCodigoUsuario(Integer codigoUsuario) {
        this.codigoUsuario = codigoUsuario;
    }

    public String getNomeEmpresa() {
        return nomeEmpresa;
    }

    public void setNomeEmpresa(String nomeEmpresa) {
        this.nomeEmpresa = nomeEmpresa;
    }

    public String getNomeUsuario() {
        return nomeUsuario;
    }

    public void setNomeUsuario(String nomeUsuario) {
        this.nomeUsuario = nomeUsuario;
    }

    public Long getDataDesbloqueio() {
        return dataDesbloqueio;
    }

    public void setDataDesbloqueio(Long dataDesbloqueio) {
        this.dataDesbloqueio = dataDesbloqueio;
    }

    public String getMensagem() {
        return mensagem;
    }

    public void setMensagem(String mensagem) {
        this.mensagem = mensagem;
    }

    public String getTelefone() {
        return telefone;
    }

    public void setTelefone(String telefone) {
        this.telefone = telefone;
    }

    public int getDiasExtras() {
        return diasExtras;
    }

    public void setDiasExtras(int diasExtras) {
        this.diasExtras = diasExtras;
    }

    public String getUsaAppTreino() {
        return usaAppTreino;
    }

    public void setUsaAppTreino(String usaAppTreino) {
        this.usaAppTreino = usaAppTreino;
    }

    public String getSabeDoCanalDoCliente() {
        return sabeDoCanalDoCliente;
    }

    public void setSabeDoCanalDoCliente(String sabeDoCanalDoCliente) {
        this.sabeDoCanalDoCliente = sabeDoCanalDoCliente;
    }
}

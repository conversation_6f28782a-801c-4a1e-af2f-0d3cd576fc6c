package negocio.oamd;

import annotations.arquitetura.NaoControlarLogAlteracao;
import br.com.pactosolucoes.ce.comuns.to.PessoaTO;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import negocio.comuns.utilitarias.Calendario;
import negocio.comuns.utilitarias.ConsistirException;
import negocio.comuns.utilitarias.UtilReflection;
import org.json.JSONArray;
import org.json.JSONException;
import org.json.JSONObject;

import java.sql.Connection;
import java.util.Date;
import java.util.List;

@JsonIgnoreProperties(ignoreUnknown = true)
public class HistoricoUtilizacaoCupomDescontoVO {

    private Integer id;
    private Integer idCupom;

    @NaoControlarLogAlteracao
    private CampanhaCupomDescontoVO campanhaCupomDescontoVO;
    private String numeroCupom;
    private Date dataLancamento;
    private Integer lote;
    private Double valorPremioPortadorCupom;
    private Double valorPremioAluno;
    private String cpfAluno;
    private Integer codigoAluno;
    private Integer codigoClientePortadorCupom;
    private String chavePortadorCupom;
    private Integer empresaPortadorCupom;
    private String chaveAluno;
    private String nomePortadorCupom;
    private String nomeAluno;
    private Date dataPremioPortadorCupom;
    private Date dataPremioAluno;
    private String observacaoProcessamento;
    private Date dataProcessamento;

    private String nomeEmpresaAluno; // atributo transient
    private List<PessoaTO> listaPessoaTO;
    private Connection connection;
    private String msgValidacao = "";// atributo transient
    private String nomeProdutoPremioPortadorCupom = ""; // atributo transient
    private String nomeUnidadeAluno;// atributo transient
    private String nomeUnidadePortador;// atributo transient
    private Double valorPremioMensalidadePortadorCupom;
    private Double valorPremioProdutosPortadorCupom;
    private boolean cupomNomeFixo = false;
    private int qtdCuponsNomeFixo = 1;
    private int qtdUtilizadoCuponsNomeFixo = 0;
    private int contrato;
    private boolean contratoEstornado = false;

    public String toJSON() throws JSONException {
        JSONObject json = new JSONObject();
        json.put("id", this.id);
        json.put("idCupom", this.idCupom);
        json.put("campanhaCupomDescontoVO", this.campanhaCupomDescontoVO != null ? new JSONObject(this.campanhaCupomDescontoVO.toJSON()) : null);
        json.put("numeroCupom", this.numeroCupom);
        json.put("dataLancamento", this.dataLancamento != null ? this.dataLancamento.getTime() : null);
        json.put("lote", this.lote);
        json.put("valorPremioPortadorCupom", this.valorPremioPortadorCupom);
        json.put("valorPremioAluno", this.valorPremioAluno);
        json.put("cpfAluno", this.cpfAluno);
        json.put("codigoAluno", this.codigoAluno);
        json.put("codigoClientePortadorCupom", this.codigoClientePortadorCupom);
        json.put("chavePortadorCupom", this.chavePortadorCupom);
        json.put("empresaPortadorCupom", this.empresaPortadorCupom);
        json.put("chaveAluno", this.chaveAluno);
        json.put("nomePortadorCupom", this.nomePortadorCupom);
        json.put("nomeAluno", this.nomeAluno);
        json.put("dataPremioPortadorCupom", this.dataPremioPortadorCupom != null ? this.dataPremioPortadorCupom.getTime() : null);
        json.put("dataPremioAluno", this.dataPremioAluno != null ? this.dataPremioAluno.getTime() : null);
        json.put("observacaoProcessamento", this.observacaoProcessamento);
        json.put("dataProcessamento", this.dataProcessamento != null ? this.dataProcessamento.getTime() : null);
        json.put("nomeEmpresaAluno", this.nomeEmpresaAluno);

        JSONArray arrayListaPessoaTO = new JSONArray();
        if (this.listaPessoaTO != null) {
            for (PessoaTO pTO : this.listaPessoaTO) {
                arrayListaPessoaTO.put(new JSONObject(pTO.toJSON()));
            }
        }
        json.put("listaPessoaTO", arrayListaPessoaTO.length() == 0 ? null : arrayListaPessoaTO);

        json.put("msgValidacao", this.msgValidacao);
        json.put("nomeProdutoPremioPortadorCupom", this.nomeProdutoPremioPortadorCupom);
        json.put("nomeUnidadeAluno", this.nomeUnidadeAluno);
        json.put("nomeUnidadePortador", this.nomeUnidadePortador);
        json.put("valorPremioMensalidadePortadorCupom", this.valorPremioMensalidadePortadorCupom);
        json.put("valorPremioProdutosPortadorCupom", this.valorPremioProdutosPortadorCupom);
        json.put("cupomNomeFixo", this.cupomNomeFixo);
        json.put("qtdCuponsNomeFixo", this.qtdCuponsNomeFixo);
        json.put("qtdUtilizadoCuponsNomeFixo", this.qtdUtilizadoCuponsNomeFixo);
        json.put("contrato", this.contrato);
        json.put("contratoEstornado", this.contratoEstornado);
        return json.toString();
    }

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }


    public String getNumeroCupom() {
        return numeroCupom;
    }

    public void setNumeroCupom(String numeroCupom) {
        this.numeroCupom = numeroCupom;
    }

    public CampanhaCupomDescontoVO getCampanhaCupomDescontoVO() {
        return campanhaCupomDescontoVO;
    }

    public void setCampanhaCupomDescontoVO(CampanhaCupomDescontoVO campanhaCupomDescontoVO) {
        this.campanhaCupomDescontoVO = campanhaCupomDescontoVO;
    }

    public static void validarDados(negocio.oamd.CupomDescontoVO obj) throws Exception {
        if (!obj.getValidarDados()) {
            return;
        }
        if (!(UtilReflection.objetoMaiorQueZero(obj, "getCampanhaCupomDescontoVO().getId()"))) {
            throw new ConsistirException("O campo CampanhaCupom (CupomDesconto) deve ser informado.");
        }
        if ((obj.getNumeroCupom() == null) || (obj.getNumeroCupom().trim().equals(""))) {
            throw new ConsistirException("O campo Número Cupom (CupomDesconto) deve ser informado.");
        }
    }

    public Date getDataLancamento() {
        return dataLancamento;
    }


    public void setDataLancamento(Date dataLancamento) {
        this.dataLancamento = dataLancamento;
    }


    public String getDataLancamento_Apresentar() {
        return Calendario.getData(this.dataLancamento, "dd/MM/yyyy HH:mm:ss");
    }

    public String getDataPremioPortadorCupom_Apresentar() {
        if (this.dataPremioPortadorCupom != null)
            return Calendario.getData(this.dataPremioPortadorCupom, "dd/MM/yyyy HH:mm:ss");
        else
            return "";
    }

    public String getDataPremioAluno_Apresentar() {
        if (this.dataPremioAluno != null)
            return Calendario.getData(this.dataPremioAluno, "dd/MM/yyyy HH:mm:ss");
        else
            return "";
    }


    public Integer getLote() {
        return lote;
    }

    public void setLote(Integer lote) {
        this.lote = lote;
    }

    public Double getValorPremioPortadorCupom() {
        return valorPremioPortadorCupom;
    }

    public void setValorPremioPortadorCupom(Double valorPremioPortadorCupom) {
        this.valorPremioPortadorCupom = valorPremioPortadorCupom;
    }

    public Double getValorPremioAluno() {
        return valorPremioAluno;
    }

    public void setValorPremioAluno(Double valorPremioAluno) {
        this.valorPremioAluno = valorPremioAluno;
    }

    public String getCpfAluno() {
        return cpfAluno;
    }

    public void setCpfAluno(String cpfAluno) {
        this.cpfAluno = cpfAluno;
    }

    public Integer getCodigoAluno() {
        return codigoAluno;
    }

    public void setCodigoAluno(Integer codigoAluno) {
        this.codigoAluno = codigoAluno;
    }

    public Integer getCodigoClientePortadorCupom() {
        return codigoClientePortadorCupom;
    }

    public void setCodigoClientePortadorCupom(Integer codigoClientePortadorCupom) {
        this.codigoClientePortadorCupom = codigoClientePortadorCupom;
    }

    public String getChavePortadorCupom() {
        return chavePortadorCupom;
    }

    public void setChavePortadorCupom(String chavePortadorCupom) {
        this.chavePortadorCupom = chavePortadorCupom;
    }

    public String getChaveAluno() {
        return chaveAluno;
    }

    public void setChaveAluno(String chaveAluno) {
        this.chaveAluno = chaveAluno;
    }

    public String getNomePortadorCupom() {
        return nomePortadorCupom;
    }

    public void setNomePortadorCupom(String nomePortadorCupom) {
        this.nomePortadorCupom = nomePortadorCupom;
    }

    public String getNomeAluno() {
        return nomeAluno;
    }

    public void setNomeAluno(String nomeAluno) {
        this.nomeAluno = nomeAluno;
    }

    public Date getDataPremioPortadorCupom() {
        return dataPremioPortadorCupom;
    }

    public void setDataPremioPortadorCupom(Date dataPremioPortadorCupom) {
        this.dataPremioPortadorCupom = dataPremioPortadorCupom;
    }

    public Date getDataPremioAluno() {
        return dataPremioAluno;
    }

    public void setDataPremioAluno(Date dataPremioAluno) {
        this.dataPremioAluno = dataPremioAluno;
    }

    public String getNomeEmpresaAluno() {
        return nomeEmpresaAluno;
    }

    public void setNomeEmpresaAluno(String nomeEmpresaAluno) {
        this.nomeEmpresaAluno = nomeEmpresaAluno;
    }

    public List<PessoaTO> getListaPessoaTO() {
        return listaPessoaTO;
    }

    public void setListaPessoaTO(List<PessoaTO> listaPessoaTO) {
        this.listaPessoaTO = listaPessoaTO;
    }

    public Connection getConnection() {
        return connection;
    }

    public void setConnection(Connection connection) {
        this.connection = connection;
    }

    public String getObservacaoProcessamento() {
        return observacaoProcessamento;
    }

    public void setObservacaoProcessamento(String observacaoProcessamento) {
        this.observacaoProcessamento = observacaoProcessamento;
    }

    public Date getDataProcessamento() {
        return dataProcessamento;
    }

    public void setDataProcessamento(Date dataProcessamento) {
        this.dataProcessamento = dataProcessamento;
    }

    public String getMsgValidacao() {
        return msgValidacao;
    }

    public void setMsgValidacao(String msgValidacao) {
        this.msgValidacao = msgValidacao;
    }

    public String getNomeProdutoPremioPortadorCupom() {
        return nomeProdutoPremioPortadorCupom;
    }

    public void setNomeProdutoPremioPortadorCupom(String nomeProdutoPremioPortadorCupom) {
        this.nomeProdutoPremioPortadorCupom = nomeProdutoPremioPortadorCupom;
    }

    public String getNomeUnidadeAluno() {
        return nomeUnidadeAluno;
    }

    public void setNomeUnidadeAluno(String nomeUnidadeAluno) {
        this.nomeUnidadeAluno = nomeUnidadeAluno;
    }

    public String getNomeUnidadePortador() {
        return nomeUnidadePortador;
    }

    public void setNomeUnidadePortador(String nomeUnidadePortador) {
        this.nomeUnidadePortador = nomeUnidadePortador;
    }

    public Double getValorPremioMensalidadePortadorCupom() {
        return valorPremioMensalidadePortadorCupom;
    }

    public void setValorPremioMensalidadePortadorCupom(Double valorPremioMensalidadePortadorCupom) {
        this.valorPremioMensalidadePortadorCupom = valorPremioMensalidadePortadorCupom;
    }

    public Double getValorPremioProdutosPortadorCupom() {
        return valorPremioProdutosPortadorCupom;
    }

    public void setValorPremioProdutosPortadorCupom(Double valorPremioProdutosPortadorCupom) {
        this.valorPremioProdutosPortadorCupom = valorPremioProdutosPortadorCupom;
    }

    public Integer getEmpresaPortadorCupom() {
        if (empresaPortadorCupom == null) {
            empresaPortadorCupom = 0;
        }
        return empresaPortadorCupom;
    }

    public void setEmpresaPortadorCupom(Integer empresaPortadorCupom) {
        this.empresaPortadorCupom = empresaPortadorCupom;
    }

    public boolean isCupomNomeFixo() {
        return cupomNomeFixo;
    }

    public void setCupomNomeFixo(boolean cupomNomeFixo) {
        this.cupomNomeFixo = cupomNomeFixo;
    }

    public int getQtdCuponsNomeFixo() {
        return qtdCuponsNomeFixo;
    }

    public void setQtdCuponsNomeFixo(int qtdCuponsNomeFixo) {
        this.qtdCuponsNomeFixo = qtdCuponsNomeFixo;
    }

    public int getQtdUtilizadoCuponsNomeFixo() {
        return qtdUtilizadoCuponsNomeFixo;
    }

    public void setQtdUtilizadoCuponsNomeFixo(int qtdUtilizadoCuponsNomeFixo) {
        this.qtdUtilizadoCuponsNomeFixo = qtdUtilizadoCuponsNomeFixo;
    }

    public Integer getIdCupom() {
        return idCupom;
    }

    public void setIdCupom(Integer idCupom) {
        this.idCupom = idCupom;
    }

    public int getContrato() {
        return contrato;
    }

    public void setContrato(int contrato) {
        this.contrato = contrato;
    }

    public boolean isContratoEstornado() {
        return contratoEstornado;
    }

    public void setContratoEstornado(boolean contratoEstornado) {
        this.contratoEstornado = contratoEstornado;
    }

    public String getContratoEstornado_Apresentar(){
        if (contratoEstornado){
            return "Sim";
        }
        return "Não";
    }

}

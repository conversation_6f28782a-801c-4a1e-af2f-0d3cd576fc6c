package negocio.oamd;

import negocio.comuns.arquitetura.SuperVO;

/**
 * Created by ulisses on 19/06/2016.
 */
public class LoginSiteRedeEmpresaVO extends SuperVO {

    private Integer id;
    private String email;
    private Integer empresafinanceiro_codigo;
    private RedeEmpresaVO redeEmpresaVO;
    private String chaveZW; // atributo transient

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getEmail() {
        return email;
    }

    public void setEmail(String email) {
        this.email = email;
    }

    public Integer getEmpresafinanceiro_codigo() {
        return empresafinanceiro_codigo;
    }

    public void setEmpresafinanceiro_codigo(Integer empresafinanceiro_codigo) {
        this.empresafinanceiro_codigo = empresafinanceiro_codigo;
    }

    public RedeEmpresaVO getRedeEmpresaVO() {
        return redeEmpresaVO;
    }

    public void setRedeEmpresaVO(RedeEmpresaVO redeEmpresaVO) {
        this.redeEmpresaVO = redeEmpresaVO;
    }

    public String getChaveZW() {
        return chaveZW;
    }

    public void setChaveZW(String chaveZW) {
        this.chaveZW = chaveZW;
    }
}

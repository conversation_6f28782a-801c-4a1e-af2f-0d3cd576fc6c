package negocio.oamd;

import negocio.comuns.arquitetura.SuperVO;

import java.util.Date;

/**
 * Created by ulisses on 17/06/2016.
 */
public class TransferenciaUnidadeVO extends SuperVO {

    private Integer id;
    private String cpf;
    private String nomeAluno;
    private Date dataTransferencia;
    private Date dataAnuidade;
    private String chaveOrigem;
    private String unidadeOrigem;
    private Integer contratoOrigem;
    private String chaveDestino;
    private String unidadeDestino;
    private Integer contratoDestino;
    private RedeEmpresaVO redeEmpresaVO;
    private boolean incluirAnuidade = true;


    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getCpf() {
        return cpf;
    }

    public void setCpf(String cpf) {
        this.cpf = cpf;
    }

    public Date getDataTransferencia() {
        return dataTransferencia;
    }

    public void setDataTransferencia(Date dataTransferencia) {
        this.dataTransferencia = dataTransferencia;
    }

    public Date getDataAnuidade() {
        return dataAnuidade;
    }

    public void setDataAnuidade(Date dataAnuidade) {
        this.dataAnuidade = dataAnuidade;
    }

    public String getChaveOrigem() {
        return chaveOrigem;
    }

    public void setChaveOrigem(String chaveOrigem) {
        this.chaveOrigem = chaveOrigem;
    }

    public String getUnidadeOrigem() {
        return unidadeOrigem;
    }

    public void setUnidadeOrigem(String unidadeOrigem) {
        this.unidadeOrigem = unidadeOrigem;
    }

    public Integer getContratoOrigem() {
        return contratoOrigem;
    }

    public void setContratoOrigem(Integer contratoOrigem) {
        this.contratoOrigem = contratoOrigem;
    }

    public String getChaveDestino() {
        return chaveDestino;
    }

    public void setChaveDestino(String chaveDestino) {
        this.chaveDestino = chaveDestino;
    }

    public String getUnidadeDestino() {
        return unidadeDestino;
    }

    public void setUnidadeDestino(String unidadeDestino) {
        this.unidadeDestino = unidadeDestino;
    }

    public Integer getContratoDestino() {
        return contratoDestino;
    }

    public void setContratoDestino(Integer contratoDestino) {
        this.contratoDestino = contratoDestino;
    }

    public String getNomeAluno() {
        return nomeAluno;
    }

    public void setNomeAluno(String nomeAluno) {
        this.nomeAluno = nomeAluno;
    }

    public RedeEmpresaVO getRedeEmpresaVO() {
        return redeEmpresaVO;
    }

    public void setRedeEmpresaVO(RedeEmpresaVO redeEmpresaVO) {
        this.redeEmpresaVO = redeEmpresaVO;
    }

    public boolean isIncluirAnuidade() {
        return incluirAnuidade;
    }

    public void setIncluirAnuidade(boolean incluirAnuidade) {
        this.incluirAnuidade = incluirAnuidade;
    }
}

package negocio.oamd;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import negocio.comuns.arquitetura.SuperTO;
import org.json.JSONObject;

@JsonIgnoreProperties(ignoreUnknown = true)
public class CustomerSuccessTO extends SuperTO {

    private Integer codigo;
    private String nome;
    private String telefone;
    private String url;
    private boolean ativo;

    public String toJSON() {
        JSONObject json = new JSONObject();
        json.put("codigo", this.codigo);
        json.put("nome", this.nome);
        json.put("telefone", this.telefone);
        json.put("url", this.url);
        json.put("ativo", this.ativo);
        return json.toString();
    }

    public Integer getCodigo() {
        return codigo;
    }

    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public String getNome() {
        return nome;
    }

    public void setNome(String nome) {
        this.nome = nome;
    }

    public String getTelefone() {
        return telefone;
    }

    public void setTelefone(String telefone) {
        this.telefone = telefone;
    }

    public String getUrl() {
        return url;
    }

    public void setUrl(String url) {
        this.url = url;
    }

    public boolean isAtivo() {
        return ativo;
    }

    public void setAtivo(boolean ativo) {
        this.ativo = ativo;
    }
}

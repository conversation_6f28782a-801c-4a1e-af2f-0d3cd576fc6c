package negocio.oamd;

import br.com.pactosolucoes.oamd.bean.EmpresaTO;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import negocio.comuns.arquitetura.SuperVO;
import negocio.comuns.utilitarias.UtilReflection;
import org.json.JSONException;
import org.json.JSONObject;

import java.util.ArrayList;
import java.util.Comparator;
import java.util.Date;
import java.util.List;
import java.util.logging.Level;
import java.util.logging.Logger;

/**
 * Created by ulisses on 27/08/2017.
 */
@JsonIgnoreProperties(ignoreUnknown = true)
public class CampanhaCupomDescontoPremioPortadorVO extends SuperVO {

    private Integer id;
    private CampanhaCupomDescontoVO campanhaCupomDescontoVO;
    private String tipoPremio = "";
    private String descricaoPremio;
    private String descricaoPlano = "";
    private double valorDesconto = 0.0;
    private double percentualDesconto = 0.0;
    private Date datalancamento;

    public static final String TIPO_PREMIO_PRODUTO = "PRODUTO";
    public static final String TIPO_PREMIO_MENSALIDADE = "MENSALIDADE";
    public static final String TIPO_DESCONTO_VALOR = "VALOR";
    public static final String TIPO_DESCONTO_PERCENTUAL = "PERCENTUAL";

    public String toJSON() {
        JSONObject json = new JSONObject();
        json.put("id", this.id);
        json.put("campanhaCupomDescontoVO", this.campanhaCupomDescontoVO != null ? new JSONObject(this.campanhaCupomDescontoVO.toJSON()) : null);
        json.put("tipoPremio", this.tipoPremio);
        json.put("descricaoPremio", this.descricaoPremio);
        json.put("descricaoPlano", this.descricaoPlano);
        json.put("valorDesconto", this.valorDesconto);
        json.put("percentualDesconto", this.percentualDesconto);
        json.put("datalancamento", this.datalancamento != null ? this.datalancamento.getTime() : null);
        json.put("TIPO_PREMIO_PRODUTO", this.TIPO_PREMIO_PRODUTO);
        json.put("TIPO_PREMIO_MENSALIDADE", this.TIPO_PREMIO_MENSALIDADE);
        json.put("TIPO_DESCONTO_VALOR", this.TIPO_DESCONTO_VALOR);
        json.put("TIPO_DESCONTO_PERCENTUAL", this.TIPO_DESCONTO_PERCENTUAL);
        return json.toString();
    }

    public String toSITE(){
        JSONObject json = new JSONObject();
        json.put("descricaoPremio", this.descricaoPremio);
        json.put("tipoPremio", this.tipoPremio);
        json.put("percentualDesconto", this.percentualDesconto);
        json.put("valorDesconto", this.valorDesconto);
        json.put("descricaoPlano", this.descricaoPlano);
        return json.toString();
    }

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
        this.codigo = id;
    }

    public Integer getCodigo() {
        return codigo;
    }

    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public CampanhaCupomDescontoVO getCampanhaCupomDescontoVO() {
        if (campanhaCupomDescontoVO == null) {
            campanhaCupomDescontoVO = new CampanhaCupomDescontoVO();
        }
        return campanhaCupomDescontoVO;
    }

    public void setCampanhaCupomDescontoVO(CampanhaCupomDescontoVO campanhaCupomDescontoVO) {
        this.campanhaCupomDescontoVO = campanhaCupomDescontoVO;
    }

    public String getTipoPremio() {
        if (tipoPremio == null) {
            tipoPremio = "";
        }
        return tipoPremio;
    }

    public void setTipoPremio(String tipoPremio) {
        this.tipoPremio = tipoPremio;
    }

    public String getDescricaoPremio() {
        if (descricaoPremio == null) {
            descricaoPremio = "";
        }
        return descricaoPremio;
    }

    public void setDescricaoPremio(String descricaoPremio) {
        this.descricaoPremio = descricaoPremio;
    }

    public String getDescricaoPlano() {
        if (this.descricaoPlano == null){
            this.descricaoPlano = "";
        }
        return descricaoPlano;
    }

    public void setDescricaoPlano(String descricaoPlano) {
        this.descricaoPlano = descricaoPlano;
    }

    public double getValorDesconto() {
        return valorDesconto;
    }

    public void setValorDesconto(double valorDesconto) {
        this.valorDesconto = valorDesconto;
    }

    public double getPercentualDesconto() {
        return percentualDesconto;
    }

    public void setPercentualDesconto(double percentualDesconto) {
        this.percentualDesconto = percentualDesconto;
    }

    public Date getDatalancamento() {
        return datalancamento;
    }

    public void setDatalancamento(Date datalancamento) {
        this.datalancamento = datalancamento;
    }

    public String getLabelDescricaoTipoPremioPortador(){
        if (this.tipoPremio.equals(CampanhaCupomDescontoPremioPortadorVO.TIPO_PREMIO_PRODUTO)){
            return "Descrição do Produto:";
        }else if (this.tipoPremio.equals(CampanhaCupomDescontoPremioPortadorVO.TIPO_PREMIO_MENSALIDADE)){
            return "Descrição da Parcela:";
        }
        return "";
    }

    public double calcularDescontoPremio(double valorOriginal) {
        if (this.valorDesconto >0){
            if (valorDesconto > valorOriginal){
                return valorOriginal;
            }
            return valorDesconto;
        }else{
            return ((valorOriginal * this.percentualDesconto) /100);
        }
    }

    @Override
    public int hashCode(){
        try{
            if (UtilReflection.objetoMaiorQueZero(this.campanhaCupomDescontoVO, "getId()")){
                return this.campanhaCupomDescontoVO.getId().hashCode();
            }
        }catch (Exception e){
            // ignored.
        }
        return 0;
    }

    @Override
    public boolean equals(Object obj){
        if ((obj == null) || (!(obj instanceof CampanhaCupomDescontoPremioPortadorVO))){
            return false;
        }
        CampanhaCupomDescontoPremioPortadorVO objCom = (CampanhaCupomDescontoPremioPortadorVO) obj;
        if ((objCom.getId() != null) && (this.id != null)){
            return objCom.getId().equals(this.id);
        }
        return objCom.getTipoPremio().toUpperCase().equals(this.getTipoPremio().toUpperCase()) &&
               objCom.getDescricaoPremio().toUpperCase().equals(this.getDescricaoPremio().toUpperCase()) &&
                objCom.getDescricaoPlano().toUpperCase().equals(this.getDescricaoPlano().toUpperCase());
    }

    public static Comparator COMPARATOR_TIPO_PREMIO = new Comparator() {
        public int compare(Object o1, Object o2) {
            CampanhaCupomDescontoPremioPortadorVO p1 = (CampanhaCupomDescontoPremioPortadorVO) o1;
            CampanhaCupomDescontoPremioPortadorVO p2 = (CampanhaCupomDescontoPremioPortadorVO) o2;
            int resultado = p1.getTipoPremio().compareTo(p2.getTipoPremio());
            if (resultado == 0){
                resultado = p1.getDescricaoPremio().compareTo(p2.getDescricaoPremio());
            }
            return resultado;
        }
    };

    public static List<CampanhaCupomDescontoPremioPortadorVO> montarListaInclusao(List<CampanhaCupomDescontoPremioPortadorVO> listaAlterada)throws Exception {
        List<CampanhaCupomDescontoPremioPortadorVO> listaIncluir = new ArrayList<CampanhaCupomDescontoPremioPortadorVO>();
        for (CampanhaCupomDescontoPremioPortadorVO obj : listaAlterada) {
            if (obj.getId() == null) {
                listaIncluir.add(obj);
            }
        }
        return listaIncluir;
    }

    public static List<CampanhaCupomDescontoPremioPortadorVO> montarListaExclusao(List<CampanhaCupomDescontoPremioPortadorVO> listaAntesAlteracao, List<CampanhaCupomDescontoPremioPortadorVO> listaAlterada)throws Exception {
        List<CampanhaCupomDescontoPremioPortadorVO> listaExcluir = new ArrayList<CampanhaCupomDescontoPremioPortadorVO>();
        if (listaAntesAlteracao == null){
            return listaExcluir;
        }
        for (CampanhaCupomDescontoPremioPortadorVO obj : listaAntesAlteracao) {
            if (!listaAlterada.contains(obj)) {
                listaExcluir.add(obj);
            }
        }
        return listaExcluir;
    }

    public Integer getNumeroParcelaDescontoPortadorCupom(){
        try{
            if (this.tipoPremio.equals(TIPO_PREMIO_MENSALIDADE)){
                // considerar que o número da parcela são as duas últimas letras da descrição do premio.
                String nrParcela = this.descricaoPremio.substring(this.descricaoPremio.indexOf("PARCELA") + 7, this.descricaoPremio.length()).trim();
                return Integer.parseInt(nrParcela);
            }
        }catch (Exception e){
            // ignored
        }
        return null;
    }

    @Override
    public String toString() {
        try {
            JSONObject json = UtilReflection.toJSON(this);
            json.remove("campanhaCupomDescontoVO");
            return "CampanhaCupomDescontoPremioPortadorVO " + json.toString();
        } catch (JSONException ex) {
            Logger.getLogger(EmpresaTO.class.getName()).log(Level.SEVERE, null, ex);
        }
        return "";
    }


}

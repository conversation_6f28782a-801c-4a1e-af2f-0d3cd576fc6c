package negocio.intranet;


import br.com.pactosolucoes.comuns.json.SuperJSON;
import negocio.comuns.utilitarias.UteisValidacao;

import java.text.SimpleDateFormat;
import java.util.Comparator;
import java.util.Date;

/**
 * Created by ulisses on 04/07/2017.
 */
public class SolicitacaoAndamentoJSON extends SuperJSON {

    private Integer id;
    private String usuario;
    private String descricao;
    private String atividade;
    private String dataCadastro = "";
    private String grupoAtividade = "";
    private String urlFotoUsuario = "";
    private Date dataCadastroOriginal;
    private Integer idSolicitacao;
    private String ticket = "";


    public String getUsuario() {
        return usuario;
    }

    public void setUsuario(String usuario) {
        this.usuario = usuario;
    }

    public String getDescricao() {
        return descricao;
    }

    public void setDescricao(String descricao) {
        this.descricao = descricao;
    }

    public String getAtividade() {
        return atividade;
    }

    public void setAtividade(String atividade) {
        this.atividade = atividade;
    }

    public String getDataCadastro() {
        if (UteisValidacao.emptyString(getTicket())) {
            return dataCadastro;
        } else {
            return "#" + getTicket() + " - " + dataCadastro;
        }
    }

    public void setDataCadastro(String dataCadastro) {
        this.dataCadastro = dataCadastro;
    }

    public String getGrupoAtividade() {
        return grupoAtividade;
    }

    public void setGrupoAtividade(String grupoAtividade) {
        this.grupoAtividade = grupoAtividade;
    }

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getUrlFotoUsuario() {
        return urlFotoUsuario;
    }

    public void setUrlFotoUsuario(String urlFotoUsuario) {
        this.urlFotoUsuario = urlFotoUsuario;
    }

    public Integer getIdSolicitacao() {
        return idSolicitacao;
    }

    public void setIdSolicitacao(Integer idSolicitacao) {
        this.idSolicitacao = idSolicitacao;
    }

    public Date getDataCadastroOriginal() {
        try{
            if ((this.dataCadastro != null) || (!this.dataCadastro.trim().equals(""))){
                SimpleDateFormat sdf = new SimpleDateFormat("dd/MM/yyyy HH:mm:ss");
                dataCadastroOriginal = sdf.parse(this.dataCadastro);
            }
        }catch (Exception e){
            // ignored
        }
        return dataCadastroOriginal;
    }

    public void setDataCadastroOriginal(Date dataCadastroOriginal) {
        this.dataCadastroOriginal = dataCadastroOriginal;
    }

    public static Comparator COMPARATOR_ANDAMENTO_DATACADASTRO = new Comparator() {
        public int compare(Object o1, Object o2) {
            SolicitacaoAndamentoJSON p1 = (SolicitacaoAndamentoJSON) o1;
            SolicitacaoAndamentoJSON p2 = (SolicitacaoAndamentoJSON) o2;
            if ((p1.getDataCadastroOriginal() != null) && (p2.getDataCadastroOriginal() != null)){
                return p1.getDataCadastroOriginal().compareTo(p2.getDataCadastroOriginal());
            }else{
                return 0;
            }
        }
    };

    public String getTicket() {
        return ticket;
    }

    public void setTicket(String ticket) {
        this.ticket = ticket;
    }
}

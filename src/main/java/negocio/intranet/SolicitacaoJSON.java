package negocio.intranet;

import annotations.arquitetura.ListJson;
import br.com.pactosolucoes.comuns.json.SuperJSON;
import negocio.comuns.utilitarias.Calendario;
import negocio.comuns.utilitarias.Uteis;

import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.List;

/**
 * Created by ulisses on 04/07/2017.
 */
public class SolicitacaoJSON extends SuperJSON {

    public String codigo;
    public Integer idTarefa;
    public String processo;
    public String atividade;
    public String empresa;
    public String titulo;
    private String espera = "";
    private String atendente;
    private String solicitante;
    private String descricao;
    private String categoria;
    private String statusOrdem;
    private Integer id;
    private boolean atividadeAbertaPeloCliente = false;
    private String dataConclusaoComHora_Apresentar;
    private String ultimaDataConclusaoSolicitacao_apresentar;
    private String dataCadastroComHora_Apresentar;
    private boolean exibirAndamentos = false;
    private String tempoUtilAtendimento;

    @ListJson(clazz = SolicitacaoAndamentoJSON.class)
    private List<SolicitacaoAndamentoJSON> andamentos;

    private String origem;

    public static String STATUS_ORDEM_PENDENCIAS = "1,2";
    public static String STATUS_ORDEM_CONCLUIDA = "3";

    public String getCodigo() {
        return codigo;
    }

    public void setCodigo(String codigo) {
        this.codigo = codigo;
    }

    public Integer getIdTarefa() {
        return idTarefa;
    }

    public void setIdTarefa(Integer idTarefa) {
        this.idTarefa = idTarefa;
    }

    public String getProcesso() {
        return processo;
    }

    public void setProcesso(String processo) {
        this.processo = processo;
    }

    public String getAtividade() {
        return atividade;
    }

    public void setAtividade(String atividade) {
        this.atividade = atividade;
    }


    public String getEmpresa() {
        return empresa;
    }

    public void setEmpresa(String empresa) {
        this.empresa = empresa;
    }

    public String getTitulo() {
        return titulo;
    }

    public void setTitulo(String titulo) {
        this.titulo = titulo;
    }

    public String getEspera() {
        return espera;
    }

    public void setEspera(String espera) {
        this.espera = espera;
    }

    public String getAtendente() {
        return atendente;
    }

    public void setAtendente(String atendente) {
        this.atendente = atendente;
    }

    public String getSolicitante() {
        return solicitante;
    }

    public void setSolicitante(String solicitante) {
        this.solicitante = solicitante;
    }

    public String getDescricao() {
        return descricao;
    }

    public void setDescricao(String descricao) {
        this.descricao = descricao;
    }

    public String getCategoria() {
        return categoria;
    }

    public void setCategoria(String categoria) {
        this.categoria = categoria;
    }

    public String getStatusOrdem() {
        return statusOrdem;
    }

    public void setStatusOrdem(String statusOrdem) {
        this.statusOrdem = statusOrdem;
    }

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }


    public boolean isAtividadeAbertaPeloCliente() {
        return atividadeAbertaPeloCliente;
    }

    public void setAtividadeAbertaPeloCliente(boolean atividadeAbertaPeloCliente) {
        this.atividadeAbertaPeloCliente = atividadeAbertaPeloCliente;
    }

    public String getDataConclusaoComHora_Apresentar() {
        return dataConclusaoComHora_Apresentar;
    }

    public void setDataConclusaoComHora_Apresentar(String dataConclusaoComHora_Apresentar) {
        this.dataConclusaoComHora_Apresentar = dataConclusaoComHora_Apresentar;
    }

    public String getUltimaDataConclusaoSolicitacao_apresentar() {
        return ultimaDataConclusaoSolicitacao_apresentar;
    }

    public void setUltimaDataConclusaoSolicitacao_apresentar(String ultimaDataConclusaoSolicitacao_apresentar) {
        this.ultimaDataConclusaoSolicitacao_apresentar = ultimaDataConclusaoSolicitacao_apresentar;
    }

    public String getDataCadastroComHora_Apresentar() {
        return dataCadastroComHora_Apresentar;
    }

    public void setDataCadastroComHora_Apresentar(String dataCadastroComHora_Apresentar) {
        this.dataCadastroComHora_Apresentar = dataCadastroComHora_Apresentar;
    }

    public boolean isExibirAndamentos() {
        return exibirAndamentos;
    }

    public void setExibirAndamentos(boolean exibirAndamentos) {
        this.exibirAndamentos = exibirAndamentos;
    }

    public List<SolicitacaoAndamentoJSON> getAndamentos() {
        return andamentos;
    }

    public void setAndamentos(List<SolicitacaoAndamentoJSON> andamentos) {
        this.andamentos = andamentos;
    }

    public String getTempoUtilAtendimento() {
        return tempoUtilAtendimento;
    }

    public void setTempoUtilAtendimento(String tempoUtilAtendimento) {
        this.tempoUtilAtendimento = tempoUtilAtendimento;
    }


    public Date getDataCadastro() throws Exception{
        SimpleDateFormat sdf = new SimpleDateFormat("dd/MM/yyyy HH:mm:ss");
        if ((this.dataCadastroComHora_Apresentar != null) && (!this.dataCadastroComHora_Apresentar.equals(""))){
            return sdf.parse(this.dataCadastroComHora_Apresentar);
        }
        return null;
    }

    public Date getDataConclusao() throws Exception{
        SimpleDateFormat sdf = new SimpleDateFormat("dd/MM/yyyy HH:mm:ss");
        if ((this.dataConclusaoComHora_Apresentar != null) && (!this.dataConclusaoComHora_Apresentar.equals(""))){
            return sdf.parse(this.dataConclusaoComHora_Apresentar);
        }
        return null;
    }


    public String getTitleMostrarOcultarAndamentos(){
        if (this.exibirAndamentos){
            return "Clique para ocultar os andamentos da atividade";
        }else{
            return "Clique para exibir os andamentos da atividade";

        }
    }


    public boolean isMostrarTempoEsperaAtendimento(){
        if (this.codigo != null){
            return (!this.codigo.contains("/"));
        }
        return true;
    }


    public String getLabelDataAberturaSolicitacao(){
        StringBuilder msg = new StringBuilder();
        msg.append("<table>");
        msg.append("");
        msg.append("<tr> <td align=\"left\"> Data/Hora abertura da solicitação: </td> <td align=\"right\">").append(this.dataCadastroComHora_Apresentar).append("</td> </tr>");
        msg.append("<tr> <td align=\"left\"> Data/Hora atendimento: </td> <td align=\"right\">").append(this.dataConclusaoComHora_Apresentar).append("</td> </tr>");
        msg.append("<tr> <td align=\"left\"> Tempo espera atendimento: </td> <td align=\"right\">").append(getTempoUtilAtendimento()).append("</td> </tr>");
        msg.append("</table>");
        return  msg.toString();
    }

    public String getOrigem() {
        return origem;
    }

    public void setOrigem(String origem) {
        this.origem = origem;
    }
}

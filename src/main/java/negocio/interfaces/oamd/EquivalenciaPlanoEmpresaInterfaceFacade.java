package negocio.interfaces.oamd;

import negocio.comuns.plano.PlanoWS;
import negocio.interfaces.basico.SuperInterfaceOAMD;

import java.sql.Connection;

/**
 * Created by ulisses on 08/07/2016.
 */
public interface EquivalenciaPlanoEmpresaInterfaceFacade extends SuperInterfaceOAMD {

    PlanoWS consultarEquivalenciaPlanoUnidadeDestino(int codigoPlanoOrigem, int codigoEmpresaFinanceiroOrigem, String chaveDestino, Connection conDestino)throws Exception;

}

package negocio.interfaces.oamd;

import negocio.interfaces.basico.SuperInterfaceOAMD;
import negocio.oamd.TransferenciaUnidadeVO;

/**
 * Created by ulisses on 17/06/2016.
 */
public interface TransferenciaUnidadeInterfaceFacade extends SuperInterfaceOAMD {

    void incluir(TransferenciaUnidadeVO transferenciaUnidadeVO)throws Exception;
    void alterar(TransferenciaUnidadeVO transferenciaUnidadeVO)throws Exception;
    void gravarMensagemErro(Integer idTransferencia, String erro)throws Exception;
    TransferenciaUnidadeVO consultarUltimaTransferenciaUnidade(String chaveRede, String cpf)throws Exception;

}

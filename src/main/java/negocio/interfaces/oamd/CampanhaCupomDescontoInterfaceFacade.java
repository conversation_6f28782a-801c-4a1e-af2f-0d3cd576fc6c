package negocio.interfaces.oamd;

import negocio.comuns.arquitetura.UsuarioVO;
import negocio.facade.jdbc.oamd.CampanhaCupomDescontoJSON;
import negocio.interfaces.arquitetura.LogInterfaceFacade;
import negocio.oamd.CampanhaCupomDescontoVO;

import java.util.List;

/**
 * Created by ulisses on 27/05/2016.
 */
public interface CampanhaCupomDescontoInterfaceFacade {

    CampanhaCupomDescontoVO incluirOAMD(CampanhaCupomDescontoVO campanhaCupomDescontoVO, UsuarioVO usuarioVO, LogInterfaceFacade logFacade)throws Exception;

    CampanhaCupomDescontoVO alterarOAMD(CampanhaCupomDescontoVO campanhaCupomDescontoVO, UsuarioVO usuarioVO, LogInterfaceFacade logFacade)throws Exception;

    void excluirOAMD(CampanhaCupomDescontoVO campanhaCupomDescontoVO)throws Exception;

    CampanhaCupomDescontoVO consultarPorId(Integer id) throws Exception;

    List<CampanhaCupomDescontoVO> consultarParaImpressaoOAMD(String filtro, String ordem, String campoOrdenacao, String chaveRedeEmpresa) throws Exception;

    List<CampanhaCupomDescontoJSON> consultarCampanhaCupomDescontoJSONOAMD(String chaveRedeEmpresa, Integer idFavorecido, boolean somenteVigente)throws Exception;

    void gerarNovoLoteCupomDescontoOAMD(CampanhaCupomDescontoVO campanhaCupomDescontoVO, Integer quantidadeCupomGerar)throws Exception;
    boolean existeCampanhaCupomDescontoOAMD(Integer codigoFavorecido, boolean somenteVigente, String descricaoPlano) throws Exception;

    CampanhaCupomDescontoVO consultarCampanhaPorNumeroCupomOAMD(String numeroCupomDesconto)throws Exception;

}

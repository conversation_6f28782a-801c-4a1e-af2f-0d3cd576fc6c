package negocio.interfaces.oamd;

import negocio.comuns.arquitetura.UsuarioVO;
import negocio.interfaces.arquitetura.LogInterfaceFacade;
import negocio.oamd.CampanhaCupomDescontoPremioPortadorVO;
import negocio.oamd.CampanhaCupomDescontoVO;

import java.util.List;

/**
 * Created by ulisses on 27/08/2017.
 */
public interface CampanhaCupomDescontoPremioPortadorInterfaceFacade {

    List<CampanhaCupomDescontoPremioPortadorVO> consultarOAMD(Integer idCampanha, String descricaoPlano) throws Exception;

    void alterarOAMD(CampanhaCupomDescontoVO campanhaCupomDescontoVO, UsuarioVO usuarioVO, LogInterfaceFacade logFacade) throws Exception;

}

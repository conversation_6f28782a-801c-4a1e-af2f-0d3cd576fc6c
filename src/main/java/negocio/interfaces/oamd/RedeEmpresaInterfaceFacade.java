package negocio.interfaces.oamd;

import negocio.comuns.basico.ClienteValidacaoWS;
import negocio.comuns.basico.EmpresaVO;
import negocio.interfaces.basico.SuperInterfaceOAMD;
import negocio.oamd.EmpresaFinanceiroVO;
import negocio.oamd.RedeEmpresaVO;
import servicos.integracao.oamd.to.EmpresaFinanceiroOAMDTO;

import java.sql.Connection;
import java.util.List;
import java.util.Map;

/**
 * Created by ulisses on 27/05/2016.
 */
public interface RedeEmpresaInterfaceFacade extends SuperInterfaceOAMD {

    RedeEmpresaVO consultarPorChaveZW(String chaveZW) throws Exception;

    RedeEmpresaVO consultarPorChaveZWOAMD(String chaveZW) throws Exception;

    RedeEmpresaVO consultarPorEmpresaFinanceiro(Integer idEmpresaFinanceiro) throws Exception;

    Integer consultarIdEmpresaFinanceiro(Integer redeEmpresa_Id, String chave) throws Exception;

    RedeEmpresaVO consultarPorChaveRede(String chaveRede) throws Exception;

    RedeEmpresaVO consultarPorId(Integer id) throws Exception;

    Map<String, Connection> obterConexoesRedeEmpresa(String chaveZW) throws Exception;

    String consultarNomeEmpresaDaRede(String chaveZW) throws Exception;

    String consultarNomeEmpresaDaRedeOAMDPrincipal(String chaveZW) throws Exception;

    ClienteValidacaoWS consultarClienteNaRedeEmpresa(String chaveZW, String cpf) throws Exception;

    List<EmpresaFinanceiroVO> consultarUnidadesDaRede(RedeEmpresaVO redeEmpresaVO) throws Exception;

    EmpresaFinanceiroVO consultarEmpresaFinanceiro(Integer codigo) throws Exception;

    EmpresaFinanceiroVO consultarEmpresaFinanceiro(String chaveZW) throws Exception;

    List<EmpresaFinanceiroOAMDTO> consultarUnidadesDaRedeOAMD(RedeEmpresaVO redeEmpresaVO) throws Exception;

}

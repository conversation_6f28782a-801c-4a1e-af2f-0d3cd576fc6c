package negocio.interfaces.oamd;

import negocio.interfaces.basico.SuperInterfaceOAMD;
import negocio.oamd.LoginSiteRedeEmpresaVO;
import negocio.oamd.RedeEmpresaVO;

/**
 * Created by ulisses on 26/05/2016.
 */
public interface LoginSiteRedeEmpresaInterfaceFacade extends SuperInterfaceOAMD {

    void registrarLoginRedeEmpresa(String email, RedeEmpresaVO redeEmpresaVO, Integer idEmpresaFinanceiro, String cpf)throws Exception;
    LoginSiteRedeEmpresaVO consultar(String chaveRede, String email, boolean buscarPorCPF)throws Exception;
    LoginSiteRedeEmpresaVO consultarPorEmail(String chaveRede, String email)throws Exception;

}

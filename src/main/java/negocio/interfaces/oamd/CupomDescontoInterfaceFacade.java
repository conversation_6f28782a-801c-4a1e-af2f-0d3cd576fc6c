package negocio.interfaces.oamd;

import br.com.pactosolucoes.enumeradores.TipoConsultaCupomDescontoEnum;
import negocio.comuns.arquitetura.UsuarioVO;
import negocio.comuns.contrato.ContratoVO;
import negocio.oamd.CampanhaCupomDescontoVO;
import negocio.oamd.CupomDescontoVO;
import negocio.oamd.CupomDescontoWS;
import negocio.oamd.HistoricoUtilizacaoCupomDescontoVO;
import servicos.impl.oamd.OAMDService;

import java.sql.Connection;
import java.util.Date;
import java.util.List;

/**
 * Created by ulisses on 27/05/2016.
 */
public interface CupomDescontoInterfaceFacade  {

    List<CupomDescontoVO> incluirLoteCupomOAMD(CampanhaCupomDescontoVO campanhaCupomDescontoVO, Integer lote, Integer totalCupomGerar, String nomeCupomEspecifico) throws Exception;

    void gravarPremioAlunoOAMD(CupomDescontoVO cupomDescontoVO)throws Exception;

    void gravarObservacaoProcessamentoOAMD(CupomDescontoVO cupomDescontoVO)throws Exception;

    Integer consultarTotalCupomDescontoJaUtilizadoOAMD(Integer idCampanhaCupomDesconto)throws Exception;

    String concederPremioCupomDescontoAoAluno(OAMDService oamdService, CampanhaCupomDescontoVO campanhaCupomDescontoVO, List<CupomDescontoVO> listaCupomDesconto, Date dataBaseVencimentoMatricula, UsuarioVO usuarioVO, Integer codigoFavorecido)throws Exception;

    CupomDescontoVO consultarPorNumeroCupomOAMD(String numeroCupom, Integer codigoFavorecido)throws Exception;

    CupomDescontoVO validarCupomPortadorCupomOAMD(Integer codigoFavorecido, String numeroCupom, String nomePlano)throws Exception;

    void concederPremioCupomPortadorCupom(Connection conZillyon, String chaveZW, ContratoVO contratoVO, String numeroCupom, Integer codigoCliente, String nomeCliente, Double valorPremioProdutos)throws Exception;

    List<CupomDescontoWS> consultarCupomDescontoOAMD(String keyRede, String listaCupom) throws  Exception;

    List<HistoricoUtilizacaoCupomDescontoVO> consultarHistoricoUtilizacaoCuponsOAMD(CampanhaCupomDescontoVO campanhaCupomDescontoVO, String chaveZW, Integer empresaZW, TipoConsultaCupomDescontoEnum tipoConsultaCupomDescontoEnum , String numeroCupom)throws Exception;

    void cancelarUtilizacaoCupomDescontoOAMD(String numeroCupomDesconto, String chaveZW)throws Exception;

    void informarContratoEstornadoHistoricoUtilizacaoCupom(int codContrato, String chaveZW)throws Exception;
}

package negocio.interfaces.financeiro;

import negocio.comuns.financeiro.ConfiguracaoFinanceiroVO;
import negocio.interfaces.basico.SuperInterface;

import java.util.Date;

public interface ConfiguracaoFinanceiroInterfaceFacade extends SuperInterface {

	ConfiguracaoFinanceiroVO incluir() throws Exception;
	
	ConfiguracaoFinanceiroVO consultar() throws Exception;

	boolean consultarUsarMovimentacaoContas() throws Exception;

	boolean isAlterarDtPgtoZWAutomaticamenteConc() throws Exception;
	
	void alterar(ConfiguracaoFinanceiroVO confFinan) throws Exception;

	void estornarReciboCancelamento(Integer codigoContrato) throws Exception;

	void executarAjusteCompensacaoCheque(int nrDiasAdd, Date apartirDe) throws Exception;

	void habilitarDesabilitarFinanceiroAvancado(boolean status) throws Exception;

}

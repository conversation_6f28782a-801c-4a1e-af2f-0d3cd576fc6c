
package negocio.interfaces.financeiro;

import java.util.Date;
import negocio.comuns.financeiro.MetaFinanceiraConsultorVO;
import negocio.interfaces.basico.SuperInterface;

import java.util.List;

public interface MetaFinanceiraConsultorInterfaceFacade extends SuperInterface {

    public void incluir(MetaFinanceiraConsultorVO obj) throws Exception;

    public void incluirSemCommit(MetaFinanceiraConsultorVO obj) throws Exception;

    public void alterar(MetaFinanceiraConsultorVO obj) throws Exception;

    public void alterarSemCommit(MetaFinanceiraConsultorVO obj) throws Exception;

    public void excluir(MetaFinanceiraConsultorVO obj) throws Exception;

    public void excluirSemCommit(MetaFinanceiraConsultorVO obj) throws Exception;

    public void excluirPelaMeta(int meta) throws Exception;

    public MetaFinanceiraConsultorVO consultarPorChavePrimaria(int codigo, int nivelMontarDados) throws Exception;
    
    public List<MetaFinanceiraConsultorVO> consultarPorMetaFinanceiraEmpresa(int codigo, int nivelMontarDados) throws Exception;
    
    public MetaFinanceiraConsultorVO consultarPorColaboradorMetaDaEmpresa(int colaborador, int metaFinanceira) throws Exception;
    public boolean existeMetaColaboradorPorEmpresaData(int colaborador, int empresa, Date dataConsulta) throws Exception;

}
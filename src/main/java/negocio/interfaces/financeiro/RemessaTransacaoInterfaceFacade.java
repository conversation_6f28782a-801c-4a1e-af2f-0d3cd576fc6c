/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */

package negocio.interfaces.financeiro;

import negocio.comuns.financeiro.RemessaTransacaoVO;
import negocio.interfaces.basico.SuperInterface;

/**
 *
 * <AUTHOR>
 */
public interface RemessaTransacaoInterfaceFacade extends SuperInterface {

    void alterar(RemessaTransacaoVO obj) throws Exception;

    RemessaTransacaoVO consultarPorChavePrimaria(final int codigo) throws Exception;

    /**
     * Operação responsável por excluir no BD um objeto da classe <code>ContratoComposicaoVO</code>.
     * Sempre localiza o registro a ser excluído através da chave primária da entidade.
     * Primeiramente verifica a conexão com o banco de dados e a permissão do usuário
     * para realizar esta operacão na entidade.
     * Isto, através da operação <code>excluir</code> da superclasse.
     * @param obj    Objeto da classe <code>ContratoComposicaoVO</code> que será removido no banco de dados.
     * @exception Execption Caso haja problemas de conexão ou restrição de acesso.
     */
    void excluir(RemessaTransacaoVO obj) throws Exception;

    void incluir(RemessaTransacaoVO obj) throws Exception;

}

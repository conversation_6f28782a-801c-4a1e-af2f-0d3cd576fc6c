package negocio.interfaces.financeiro;

import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.List;

import negocio.comuns.financeiro.ComportamentoConta;
import negocio.comuns.financeiro.TipoContaVO;
import negocio.interfaces.basico.SuperInterface;

/**
 * Interface da DAO que representa a tabela finantipoconta
 * 
 * <AUTHOR>
 */
public interface TipoContaInterfaceFacade extends SuperInterface {
	
	public void alterar(TipoContaVO obj) throws Exception;

	public void excluir(TipoContaVO obj) throws Exception;

	public void incluir(TipoContaVO obj) throws Exception;

	public String consultarJSON() throws Exception;

	public List consultar(TipoContaVO filtro) throws Exception;

    public List consultarTodas(int nivelDados) throws Exception;

    public TipoContaVO consultarPorChavePrimaria(Integer codigoPrm, int nivelMontarDados) throws Exception;

    public ComportamentoConta consultarComportamento(Integer codigoTipo) throws SQLException;

	public List consultarParaImpressao(String filtro, String ordem, String campoOrdenacao, Integer empresa) throws SQLException;

	public TipoContaVO existeTipoConta(ComportamentoConta comportamento) throws Exception;
}
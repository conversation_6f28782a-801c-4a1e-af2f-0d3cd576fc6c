package negocio.interfaces.financeiro;

import cfin.wrapper.ParcelaEmAberto;
import cfin.wrapper.TResultadoBoleto;
import cfin.wrapper.TResultadoParcelaConsultada;
import cfin.wrapper.TResultadoParcelasEmAberto;
import negocio.interfaces.basico.SuperInterface;

import java.rmi.RemoteException;

/**
 * Created by glauco on 22/09/2014.
 */
public interface FinanceiroPactoInterfaceFacade  {

    public TResultadoBoleto obtenhaBoleto(Integer codigoParcela) throws Exception;

    public TResultadoParcelasEmAberto obterParcelasEmAberto(String chave, int codEmpresa) throws Exception;

    public TResultadoParcelaConsultada obterParcelasAcademia(String chave, int codEmpresa, int qtdeParcelasRetornar) throws Exception ;

    public TResultadoBoleto regerarParcela(int codParcela, boolean calcularCobrancasExtras) throws RemoteException;

}

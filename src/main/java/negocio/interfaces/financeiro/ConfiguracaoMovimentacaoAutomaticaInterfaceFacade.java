package negocio.interfaces.financeiro;

import negocio.comuns.financeiro.ConfiguracaoMovimentacaoAutomaticaVO;
import negocio.interfaces.basico.SuperInterface;

import java.util.List;

public interface ConfiguracaoMovimentacaoAutomaticaInterfaceFacade extends SuperInterface {


    List<ConfiguracaoMovimentacaoAutomaticaVO> obterConfiguracoes(Integer empresa) throws Exception;

    void incluir(ConfiguracaoMovimentacaoAutomaticaVO config) throws Exception;

    void alterar(ConfiguracaoMovimentacaoAutomaticaVO config) throws Exception;

    void remover(ConfiguracaoMovimentacaoAutomaticaVO config) throws Exception;

}

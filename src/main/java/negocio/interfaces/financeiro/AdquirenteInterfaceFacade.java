package negocio.interfaces.financeiro;

import negocio.comuns.financeiro.AdquirenteVO;
import negocio.comuns.financeiro.RemessaItemVO;
import negocio.comuns.financeiro.RemessaVO;
import negocio.comuns.financeiro.TransacaoVO;
import negocio.interfaces.basico.SuperInterface;

import java.sql.SQLException;
import java.util.List;

public interface AdquirenteInterfaceFacade extends SuperInterface {


    public AdquirenteVO consultarPorCodigo(Integer codigo) throws Exception;

    public void incluir(AdquirenteVO obj) throws Exception;

    public void alterar(AdquirenteVO obj) throws Exception ;

    public void excluir(AdquirenteVO obj) throws Exception;

    public List<AdquirenteVO> consultarTodos(boolean ativos) throws Exception;

    public List<AdquirenteVO> consultarTodosGeoidt(boolean ativos, boolean geoitd) throws Exception;

    public String consultarJSON(String situacao ) throws Exception;

    public List consultarParaImpressao(String filtro, String ordem, String campoOrdenacao, int i, String situacao) throws SQLException;

    AdquirenteVO consultarOuCriaSeNaoExistir(String nome);

    AdquirenteVO obterAdquirenteTransacao(TransacaoVO transacaoVO);

    AdquirenteVO obterAdquirenteRemessaItem(RemessaVO remessaVO);
}

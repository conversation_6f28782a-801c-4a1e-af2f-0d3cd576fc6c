package negocio.interfaces.financeiro;

import negocio.comuns.financeiro.ContaBancariaFornecedorVO;
import negocio.interfaces.basico.SuperInterface;

public interface ContaBancariaFornecedorInterfaceFacade extends SuperInterface {

    void incluir(ContaBancariaFornecedorVO obj) throws Exception;

    void alterar(ContaBancariaFornecedorVO obj) throws Exception;

    ContaBancariaFornecedorVO consultarPorPessoa(int codPessoa, int nivelMontarDados) throws Exception;

    void excluirPorCodPessoa(ContaBancariaFornecedorVO obj) throws Exception;

    void excluirEIncluirSemCommit(ContaBancariaFornecedorVO obj) throws Exception;

    Integer obterCodigoPorNumeroContaEAgencia(String account_number, String agency_number) throws Exception;
}

package negocio.interfaces.financeiro;

import negocio.comuns.financeiro.ContaCorrenteVO;
import negocio.interfaces.basico.SuperInterface;

import java.util.List;

/**
 * Interface reponsável por criar uma estrutura padrão de comunidação entre a camada de controle
 * e camada de negócio (em especial com a classe Façade). Com a utilização desta interface
 * é possível substituir tecnologias de uma camada da aplicação com mínimo de impacto nas demais.
 * Além de padronizar as funcionalidades que devem ser disponibilizadas pela camada de negócio, por intermédio
 * de sua classe Façade (responsável por persistir os dados das classes VO).
 */
public interface ContaCorrenteInterfaceFacade extends SuperInterface {


    public ContaCorrenteVO novo() throws Exception;

    public void incluir(ContaCorrenteVO obj) throws Exception;

    public void incluirSemCommit(ContaCorrenteVO obj) throws Exception ;

    public void alterar(ContaCorrenteVO obj) throws Exception;

    public void alterarSemCommit(ContaCorrenteVO obj) throws Exception ;

    public List<ContaCorrenteVO> consultarTodos(int nivelMontarDados)throws Exception;

    public void excluir(ContaCorrenteVO obj) throws Exception;

    public ContaCorrenteVO consultarPorChavePrimaria(Integer codigo, int nivelMontarDados) throws Exception;

    public List consultarPorCodigo(Integer valorConsulta, boolean controlarAcesso, int nivelMontarDados) throws Exception;

    public List consultarPorAgencia(String valorConsulta, boolean controlarAcesso, int nivelMontarDados) throws Exception;

    public List consultarPorAgenciaDV(String valorConsulta, boolean controlarAcesso, int nivelMontarDados) throws Exception;

    public List consultarPorContaCorrente(String valorConsulta, boolean controlarAcesso, int nivelMontarDados) throws Exception;

    public List consultarPorContaCorrenteDV(String valorConsulta, boolean controlarAcesso, int nivelMontarDados) throws Exception;

    public List consultarPorNomeBanco(String valorConsulta, int nivelMontarDados) throws Exception;

    public void setIdEntidade(String aIdEntidade);

    public ContaCorrenteVO criarOuConsultarSeExistePorContaCorrente(ContaCorrenteVO obj) throws Exception;

    public String consultarJSON() throws Exception;

    public List consultarParaImpressao(String filtro, String ordem, String campoOrdenacao, int i) throws Exception;

}

package negocio.interfaces.financeiro;

import negocio.comuns.financeiro.LogPJBankVO;
import negocio.interfaces.basico.SuperInterface;

import java.util.List;

/**
 * Interface reponsável por criar uma estrutura padrão de comunidação entre a camada de controle
 * e camada de negócio (em especial com a classe Façade). Com a utilização desta interface
 * é possível substituir tecnologias de uma camada da aplicação com mínimo de impacto nas demais.
 * Além de padronizar as funcionalidades que devem ser disponibilizadas pela camada de negócio, por intermédio
 * de sua classe Façade (responsável por persistir os dados das classes VO).
 */
public interface LogPJBankInterfaceFacade extends SuperInterface {

    public void incluir(LogPJBankVO obj) throws Exception;

    public List consultarTodos(int nivelMontarDados) throws Exception;




}

package negocio.interfaces.financeiro;

import negocio.comuns.contrato.ComissaoGeralConfiguracaoVO;
import negocio.comuns.financeiro.ComissaoMetaFinananceiraVO;
import negocio.interfaces.basico.SuperInterface;

import java.util.List;

/**
 * Created by ulisses on 28/09/2015.
 */
public interface ComissaoMetaFinanceiraInterfaceFacade extends SuperInterface {

    void incluirSemCommit(ComissaoGeralConfiguracaoVO comissaoGeralConfiguracaoVO) throws Exception;
    List<ComissaoMetaFinananceiraVO> consultar(ComissaoGeralConfiguracaoVO comissaoGeralConfiguracaoVO, int nivelMontarDados)throws Exception;
    ComissaoMetaFinananceiraVO consultar(Integer codigoComissaoGeralConfiguracao, Integer codigoMeta , int nivelMontarDados) throws Exception;
    void excluirSemCommmit(Integer codigoComissaoGeralConfiguracao)throws Exception;

}

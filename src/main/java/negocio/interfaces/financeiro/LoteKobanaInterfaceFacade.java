package negocio.interfaces.financeiro;

import br.com.pactosolucoes.enumeradores.TipoContaPagarLoteEnum;
import negocio.comuns.financeiro.LoteKobanaVO;
import negocio.comuns.financeiro.enumerador.RegistrationStatusKobanaEnum;
import negocio.comuns.financeiro.enumerador.StatusLoteKobanaEnum;
import negocio.interfaces.basico.SuperInterface;

import java.util.Date;
import java.util.List;

public interface LoteKobanaInterfaceFacade extends SuperInterface {

    LoteKobanaVO incluir(LoteKobanaVO obj) throws Exception;

    List<LoteKobanaVO> consultar(Date dataInicio, Date dataFim, Integer empresa, StatusLoteKobanaEnum statusEnum,
                                 RegistrationStatusKobanaEnum registrationStatusEnum, TipoContaPagarLoteEnum tipoContaPagarLoteEnum,
                                 Integer limit, Integer offset, int nivelMontarDados) throws Exception;

    void atualizarByCodigo(LoteKobanaVO obj) throws Exception;

    Integer obterCountConsultaLotes(Date dataInicio, Date dataFim, Integer empresa, StatusLoteKobanaEnum statusEnum,
                                    RegistrationStatusKobanaEnum registrationStatusEnum, TipoContaPagarLoteEnum tipoContaPagarLoteEnum,
                                    Integer limit, Integer offset) throws Exception;

    void alterar(LoteKobanaVO obj) throws Exception;

    LoteKobanaVO consultarPorCodigo(int cod, int nivelMontarDados) throws Exception;

    LoteKobanaVO consultarPorUId(String uid, int nivelMontarDados) throws Exception;
}

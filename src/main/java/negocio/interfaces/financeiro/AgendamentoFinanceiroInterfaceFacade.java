
package negocio.interfaces.financeiro;

import negocio.comuns.financeiro.AgendamentoFinanceiroVO;
import negocio.interfaces.basico.SuperInterface;

import java.util.Date;
import java.util.Map;
import negocio.comuns.basico.EmpresaVO;

public interface AgendamentoFinanceiroInterfaceFacade extends SuperInterface {

    public void alterar(AgendamentoFinanceiroVO obj) throws Exception;
    public void alterarSemCommit(AgendamentoFinanceiroVO obj) throws Exception;
    public void excluir(int codigo) throws Exception;
    public void excluirSemCommit(int codigo) throws Exception;
    public AgendamentoFinanceiroVO consultarPorChavePrimaria(int codigo, int nivelMontarDados) throws Exception;
    public AgendamentoFinanceiroVO consultarPorMovConta(Integer codigoMovConta, int nivelMontarDados) throws Exception;

    public Map<Integer, Date> atualizarDatasVencimentoFuturo(AgendamentoFinanceiroVO agendamento, EmpresaVO empresa) throws Exception;
}
/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package negocio.interfaces.financeiro;

import negocio.comuns.basico.EmpresaVO;
import negocio.comuns.financeiro.ConfiguracaoFinanceiroVO;
import negocio.comuns.financeiro.ConvenioCobrancaVO;
import negocio.comuns.financeiro.ExtratoDiarioItemVO;
import negocio.interfaces.basico.SuperInterface;

import java.sql.SQLException;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 *
 * <AUTHOR>
 */
public interface ExtratoDiarioItemInterfaceFacade extends SuperInterface{
    
    public void incluir(ExtratoDiarioItemVO obj) throws Exception;

    public void alterar(ExtratoDiarioItemVO obj) throws Exception;

    public List<ExtratoDiarioItemVO> consultarExtratoPorDia(Date inicio, Date fim) throws Exception;

    public List<ExtratoDiarioItemVO> consultarExtratoParaConciliacao(Date inicio, Date fim, String ro, String autorizacao, Integer tipo<PERSON>,
                                                                     Integer empresa, <PERSON>te<PERSON> convenio, Integer formaPagamento, Boolean apresentarPagamentosCancelados,
                                                                     Integer operadoraCartao, Integer adquirente, String nsu) throws Exception;

    public boolean arquivoProcessado(String arquivo) throws Exception;

    void processarListaExtratoDiario(List<ExtratoDiarioItemVO> listaExtratoDiarioItem, boolean reprocessar) throws Exception;

    void processarListaExtratoDiario(List<ExtratoDiarioItemVO> listaExtratoDiarioItem, boolean reprocessando, ConvenioCobrancaVO convenioCobranca) throws Exception;

    public void alterarDatasPagamento(ExtratoDiarioItemVO item) throws Exception;
    
    public void mesclarLancamentos(ExtratoDiarioItemVO itemZW, ExtratoDiarioItemVO itemED) throws Exception;

    public void simularExtratoDiario(Date inicio, Date fim) throws Exception;
    
    public Map<String, ExtratoDiarioItemVO> consultarMapaExtratoDiario(Date inicio, Date fim) throws Exception;
    
    public ExtratoDiarioItemVO consultarPorChavePrimaria(final Integer codigo) throws Exception;

    List<ExtratoDiarioItemVO> consultarExtratosRedeVenda(String ro) throws Exception;

    void estornarRecibo(ExtratoDiarioItemVO item) throws Exception;

    void preencherParcelasItem(ExtratoDiarioItemVO extratoDiarioItemVO) throws Exception;

    int consultarCodigoPor(String autorizacao, Date dataPrevisao, String nsu, Integer tipoConciliacao, Integer nrParcela, Integer empresa) throws  Exception;

    String consultarAutorizacaoPorNSU(String nsu) throws Exception;

    Integer consultarMovPagamento(String autorizacao, String nsu) throws  Exception;

    void incluirExtratoArquivo(String arquivo, String nomeArquivo, ConvenioCobrancaVO convenioCobrancaVO);

    void preencherMovPagamento(ExtratoDiarioItemVO obj) throws Exception;

    void preencherCartaoCredito(ExtratoDiarioItemVO obj, boolean isConciliarSemNumeroParcela) throws Exception;

    void preencherCartaoDebito(ExtratoDiarioItemVO obj) throws Exception;

    void verificarCreditoDebitoExtrato(ExtratoDiarioItemVO obj) throws Exception;

    Integer pessoaExtrato(ExtratoDiarioItemVO extratoDiarioItem) throws Exception;

    void incluirInfoCodPessoa(ExtratoDiarioItemVO extratoDiarioItemVO) throws SQLException;

    void criarContaPagarOuReceberCancelamentoStone(ConvenioCobrancaVO convenioCobrancaVO, ExtratoDiarioItemVO itemVO,
                                                   EmpresaVO empresaVO, ConfiguracaoFinanceiroVO configuracaoFinanceiroVO);

    void criarContaPagarOuReceberCancelamentoCielo(ConvenioCobrancaVO convenioCobrancaVO, ExtratoDiarioItemVO itemVO,
                                                   EmpresaVO empresaVO, ConfiguracaoFinanceiroVO configuracaoFinanceiroVO);

    double obterValorLiquidoParcelamentoVendaAgrupadosItem(Integer movPagamento, Date dataLancamentoItens) throws SQLException;

    double obterValorDescontadoTaxasParcelamentoVendaAgrupadosItem(Integer movPagamento, Date dataLancamentoItens) throws SQLException;
}

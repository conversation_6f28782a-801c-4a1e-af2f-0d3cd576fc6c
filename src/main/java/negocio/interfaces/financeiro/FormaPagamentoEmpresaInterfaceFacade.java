package negocio.interfaces.financeiro;

import negocio.comuns.financeiro.FormaPagamentoEmpresaVO;
import negocio.comuns.financeiro.FormaPagamentoVO;
import negocio.interfaces.basico.SuperInterface;

import java.util.Date;
import java.util.List;

public interface FormaPagamentoEmpresaInterfaceFacade extends SuperInterface {

    public void incluir(FormaPagamentoVO fp, List<FormaPagamentoEmpresaVO> empresas) throws Exception;

    public void incluir(FormaPagamentoEmpresaVO obj) throws Exception;

    public void incluirSemCommit(FormaPagamentoEmpresaVO obj) throws Exception ;

    public void alterarSemCommit(FormaPagamentoEmpresaVO obj) throws Exception;

    public void alterar(FormaPagamentoEmpresaVO obj) throws Exception ;

    public void excluir(FormaPagamentoEmpresaVO obj) throws Exception;

    public void excluirPorEmpresa(final Integer formaPagamento, final Integer empresa) throws Exception;

    public List<FormaPagamentoEmpresaVO> consultarPorForma(Integer forma) throws Exception;

    public Double descrobrirTaxaApropriada(int nrVezes, Date dataLancamento, Integer adquirente, Integer operadora,
                                           FormaPagamentoVO formaPagamentoVO, Integer empresa) throws Exception;
}

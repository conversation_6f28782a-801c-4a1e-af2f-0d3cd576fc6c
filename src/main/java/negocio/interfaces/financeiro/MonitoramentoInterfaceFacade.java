/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package negocio.interfaces.financeiro;

import negocio.comuns.financeiro.MonitoramentoVO;
import negocio.interfaces.basico.SuperInterface;

import java.util.Date;

/**
 * <AUTHOR>
 */
public interface MonitoramentoInterfaceFacade extends SuperInterface {

    MonitoramentoVO gerarDadosMonitoramento(final String chave, final Integer empresa, final String nomeEmpresa, Date dataReprocessar) throws Exception;

    void incluir(MonitoramentoVO monitoramentoVO) throws Exception;

    void deletar(final String chave, final Integer empresa, Date dataInicio, Date dataFinal) throws Exception;
}

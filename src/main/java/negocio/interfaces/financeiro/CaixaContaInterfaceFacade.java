/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */

package negocio.interfaces.financeiro;

import java.util.List;
import negocio.comuns.financeiro.CaixaContaVO;
import negocio.comuns.financeiro.CaixaVO;
import negocio.interfaces.basico.SuperInterface;

/**
 *
 * <AUTHOR>
 */
public interface CaixaContaInterfaceFacade extends SuperInterface{

    public void incluir(CaixaContaVO caixaContaVO) throws Exception;
    public void alterar(CaixaContaVO caixaContaVO) throws Exception;
    public void excluir(CaixaContaVO caixaContaVO)throws Exception;
    public List<CaixaContaVO> consultar(CaixaVO caixaVo, int nivelMontarDados)throws Exception;

}

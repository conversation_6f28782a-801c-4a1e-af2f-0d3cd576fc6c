package negocio.interfaces.financeiro;
import negocio.comuns.financeiro.LoteVO;
import negocio.comuns.financeiro.MovContaVO;
import negocio.interfaces.basico.SuperInterface;
import java.util.Date;
import java.util.List;
import negocio.comuns.financeiro.CartaoCreditoVO;
import negocio.comuns.financeiro.ChequeVO;

/**
 * Interface reponsável por criar uma estrutura padrão de comunidação entre a camada de controle
 * e camada de negócio (em especial com a classe Façade). Com a utilização desta interface 
 * é possível substituir tecnologias de uma camada da aplicação com mínimo de impacto nas demais.
 * Além de padronizar as funcionalidades que devem ser disponibilizadas pela camada de negócio, por intermédio
 * de sua classe Façade (responsável por persistir os dados das classes VO).
*/
public interface LoteInterfaceFacade extends SuperInterface {

    public void incluir(LoteVO obj) throws Exception;

    public void incluirSemCommit(LoteVO obj, boolean atualizarDataCompensacao) throws Exception;

    public void alterar(LoteVO obj, boolean alterarDataLancamento) throws Exception;

    public void alterarSemCommit(LoteVO obj, boolean alterarDataLancamento) throws Exception;

    public void alterarSomenteValor(LoteVO obj) throws Exception;

    public LoteVO consultarPorChavePrimaria(int codigo, int nivelMontarDados) throws Exception;

    public boolean verificarContaDevolucao(int codigolote) throws Exception;
    
    public List<LoteVO> consultarPorCodigoDescricaoPeriodoDL(int empresa, int codigo, String descricao, Date inicioD, Date fimD, 
    		Date inicioL, Date fimL,int codigoConta, boolean disponiveis, boolean avulso, int nivelMontarDados) throws Exception;

    public List<LoteVO> consultarPor(int empresa, int codigo, String descricao, Date inicioD, Date fimD,
                                     Date inicioL, Date fimL, int codigoConta, boolean disponiveis, boolean avulso, int nivelMontarDados) throws Exception;

    public void excluir(LoteVO obj) throws Exception;
    
    public void alterarMinimo(LoteVO obj, ChequeVO ch, boolean remover, boolean atualizarHistorico, boolean alterarValores) throws Exception;
    
    public void alterarMinimoSemCommit(LoteVO obj, ChequeVO ch, boolean remover, boolean atualizarHistorico, boolean alterarValores) throws Exception;

	void alterarMinimo(LoteVO obj, CartaoCreditoVO ca, boolean retirar, boolean alterarValores) throws Exception;
	
	void alterarMinimoSemCommit(LoteVO obj, CartaoCreditoVO ca, boolean retirar, boolean alterarValores) throws Exception;
	
	public void retirarRecebivelLotes(ChequeVO cheque, CartaoCreditoVO cartao, boolean alterarValores) throws Exception;
	
	public Integer consultarContaLote(int lote) throws Exception;
	
	public LoteVO consultarPorPagaMovConta(MovContaVO movcontaVO, int nivelMontarDados) throws Exception;
	
	public void retirarChequeDoLote(ChequeVO cheque, int loteNaoRetirar) throws Exception;
	
	public List<LoteVO> consultarRelacionamentosDoLote(LoteVO lote) throws Exception;
	
    public LoteVO consultarPorCartao(int codigo, int nivelMontarDados) throws Exception ;
    
    public LoteVO consultarPorCheque(int codigo, int nivelMontarDados) throws Exception ;
    
    public int consultarLoteAvulso(int cheque) throws Exception;

	void excluirSemCommit(LoteVO obj, boolean excluirMovConta, boolean limparRelacionamentoCheque) throws Exception;
	
	public void atualizarValorLote(Integer lote, boolean cheque) throws Exception;
	
	public List<LoteVO> consultarPorChequeLista(int codigo, int nivelMontarDados) throws Exception;
	
	public List<LoteVO> consultarPorCartaoLista(int codigo, int nivelMontarDados) throws Exception;
        
        public void atualizarMovConta(LoteVO obj, ChequeVO ch) throws Exception;
	
}

package negocio.interfaces.financeiro;

import br.com.pactosolucoes.autorizacaocobranca.modelo.TipoAutorizacaoCobrancaEnum;
import negocio.comuns.basico.EmpresaVO;
import negocio.comuns.financeiro.ConvenioCobrancaVO;
import negocio.comuns.financeiro.enumerador.*;
import negocio.interfaces.basico.SuperInterface;

import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * Interface reponsável por criar uma estrutura padrão de comunidação entre a camada de controle
 * e camada de negócio (em especial com a classe Façade). Com a utilização desta interface
 * é possível substituir tecnologias de uma camada da aplicação com mínimo de impacto nas demais.
 * Além de padronizar as funcionalidades que devem ser disponibilizadas pela camada de negócio, por intermédio
 * de sua classe Façade (responsável por persistir os dados das classes VO).
 */
public interface ConvenioCobrancaInterfaceFacade extends SuperInterface {

    ConvenioCobrancaVO novo() throws Exception;

    void incluir(ConvenioCobrancaVO obj) throws Exception;

    void alterar(ConvenioCobrancaVO obj) throws Exception;

    void excluir(ConvenioCobrancaVO obj) throws Exception;

    ConvenioCobrancaVO consultarPorCodigoSemInfoEmpresa(Integer codigoPrm, int nivelMontarDados) throws Exception;

    ConvenioCobrancaVO consultarPorCodigoEmpresa(Integer codigoPrm, Integer empresa, int nivelMontarDados) throws Exception;

    ConvenioCobrancaVO consultarPorBanco(Integer codBando) throws Exception;

    List<ConvenioCobrancaVO> consultarPorEmpresa(Integer valorConsulta, boolean controlarAcesso, int nivelMontarDados) throws Exception;

    List<ConvenioCobrancaVO> consultarPorEmpresa(Integer valorConsulta, SituacaoConvenioCobranca situacao, boolean controlarAcesso, Boolean somenteExtrato, int nivelMontarDados) throws Exception;

    List<ConvenioCobrancaVO> consultarPorEmpresaESituacao(Integer empresa, boolean controlarAcesso, int nivelMontarDados, final Integer[] tipos, SituacaoConvenioCobranca situacao) throws Exception;

    List<ConvenioCobrancaVO> consultarPorEmpresaESituacao(Integer empresa, boolean controlarAcesso, int nivelMontarDados, final Integer[] tipos, SituacaoConvenioCobranca situacao, boolean somenteExtrato) throws Exception;

    List<ConvenioCobrancaVO> consultarPorEmpresa(Integer valorConsulta, boolean controlarAcesso, int nivelMontarDados, Integer[] tipos) throws Exception;

    List consultarPorTipos(final TipoConvenioCobrancaEnum[] tipos, int empresa, boolean controlarAcesso, int nivelMontarDados) throws Exception;

    List consultarPorTiposESituacao(final TipoConvenioCobrancaEnum[] tipos, Integer empresa, SituacaoConvenioCobranca situacao, boolean controlarAcesso, int nivelMontarDados, Boolean somenteExtrato) throws Exception;

    List consultarPorTiposESituacao(final TipoConvenioCobrancaEnum[] tipos, Integer empresa, SituacaoConvenioCobranca situacao, boolean controlarAcesso, int nivelMontarDados, int codPessoa, Boolean somenteExtrato) throws Exception;

    List consultarPorTiposESituacaoEAmbiente(final TipoConvenioCobrancaEnum[] tipos, Integer empresa, SituacaoConvenioCobranca situacao,
                                             int nivelMontarDados, Boolean somenteExtrato, AmbienteEnum ambienteEnum) throws Exception;

    List consultarPjBankPorEmpresa( int empresa, SituacaoConvenioCobranca situacao, int nivelMontarDados) throws Exception;

    List consultarBBPorEmpresa( int empresa, SituacaoConvenioCobranca situacao, int nivelMontarDados) throws Exception;

    void setIdEntidade(String aIdEntidade);

    ConvenioCobrancaVO consultarPorChavePrimaria(Integer codigoPrm, int nivelMontarDados) throws Exception;

    ConvenioCobrancaVO criarOuConsultarSeExistePorDescricaoEEmpresa(ConvenioCobrancaVO obj, Integer empresa) throws Exception;

    void incrementarSequencialArquivo(ConvenioCobrancaVO convenio) throws Exception;

    void incrementarSequencialArquivoCancelamento(ConvenioCobrancaVO convenio) throws Exception;

    String consultarJSON(Integer empresa, String situacao, String sEcho, Integer tipoCobrancaEnum, String clausulaLike, String colunaOrdenar, String sentidoOrdenar) throws Exception;

    public ConvenioCobrancaVO consultarDadosPix(int codigo) throws Exception;

    public ConvenioCobrancaVO consultarConvenioPixPorCodigoEmpresa(int codigoEmpresa, TipoConvenioCobrancaEnum tipoConvenioCobrancaEnum) throws Exception;

    List consultarParaImpressao(String filtro, String ordem, String campoOrdenacao, int i) throws Exception;

    ConvenioCobrancaVO consultarPorMovParcela(Integer codigoMovparcela, Integer empresa) throws Exception;

    List<ConvenioCobrancaVO> consultarSimplesPorTipos(TipoConvenioCobrancaEnum ... tipos) throws Exception;

    Integer incrementarSequencialItem(Integer codigoConvenio) throws Exception;

    List<ConvenioCobrancaVO> consultarTodos(int nivelmontardadosDadosbasicos) throws  Exception;

    ConvenioCobrancaVO consultarPorTipoEBanco(TipoConvenioCobrancaEnum tipoConvenio, String codigoBancoConvenio) throws Exception;

    ConvenioCobrancaVO consultarPorTipoEBanco(TipoConvenioCobrancaEnum tipoConvenio, String codigoBancoConvenio, Integer codigoBanco) throws Exception;

    List<ConvenioCobrancaVO> consultarPorCodigoIn(String codigos, int nivelMontarDados) throws Exception;

    void alterarChaveAPIGetNet(ConvenioCobrancaVO convenioCobrancaVO) throws Exception;

    List<ConvenioCobrancaVO> consultarTodosGestaoRemessas(int nivelmontardados) throws Exception;

    List<ConvenioCobrancaVO> consultarTodosGeral(boolean comInformacaoEmpresa, Boolean somenteExtrato, int nivelmontardados) throws Exception;

    List<ConvenioCobrancaVO> consultarTodosPorSituacaoEmpresa(int codEmpresa, int situacao, int nivelmontardados) throws Exception;

    List<ConvenioCobrancaVO> consultarPorCodigo(int codigo, int nivelmontardadosTodos) throws Exception;

    List<ConvenioCobrancaVO> consultarPorTipoPix() throws Exception;

    List<ConvenioCobrancaVO> consultarPorTipoPixComEmpresa(EmpresaVO empresa) throws Exception;

    List<ConvenioCobrancaVO> consultarPorTipoConvenio(int tipoConvenio, int nivelmontardadosTodos) throws Exception;

    void ajustarConvenio(Integer codigoEmpresa, Integer codigoConvenioCobrancaAnterior,
                         TipoAutorizacaoCobrancaEnum tipoAutorizacaoConvenioCobrancaAnterior,
                         Integer codigoConvenioCobrancaNovo, List<OperadorasExternasAprovaFacilEnum> listOperadoras) throws Exception;

    List<ConvenioCobrancaVO> consultarPorEmpresaSituacaoTipoAutorizacao(Integer empresa, SituacaoConvenioCobranca situacao, TipoAutorizacaoCobrancaEnum tipoAutorizacaoEnum,
                                                                        Boolean somenteExtrato, int nivelMontarDados) throws Exception;

    List<ConvenioCobrancaVO> consultarConveniosTrocaDeEmpresa(Integer cliente, Integer empresaDestino);

    boolean existeConvenioCobrancaPorEmpresaSituacaoTipoCobranca(Integer empresa, TipoCobrancaEnum tipoCobrancaEnum,
                                                                 SituacaoConvenioCobranca situacao) throws Exception;

    boolean existeConvenioCobrancaPorEmpresaSituacaoTipo(Integer empresa, final Integer[] tipos, SituacaoConvenioCobranca situacao) throws Exception;

    List<ConvenioCobrancaVO> consultarPorTipoCobranca(TipoCobrancaEnum tipoCobrancaEnum, Integer empresa, SituacaoConvenioCobranca situacao,
                                                      Boolean somenteExtrato, int nivelMontarDados) throws Exception;

    List<ConvenioCobrancaVO> consultarPorTipoCobranca(TipoCobrancaEnum[] arrayTipoCobrancaEnum, Integer empresa, SituacaoConvenioCobranca situacao,
                                                      Boolean somenteExtrato, int nivelMontarDados) throws Exception;

    boolean somenteConvenioVindi() throws Exception;

    boolean existeConvenioOnline(Set<Integer> empresas, SituacaoConvenioCobranca situacao) throws Exception;

    List<String>  lerLogProcessamentoExtratos(ConvenioCobrancaVO convenioVO, int qtdMaximaLinhasApresentarExtrato);

    ConvenioCobrancaVO obterConvenioPadraoVerificacaoCartao(Integer empresa, int nivelMontarDados) throws Exception;

    TipoCredencialStoneEnum obterTipoCredenciamentoStoneByCodigoConvenio(int codigoConvenio) throws Exception;

    void existeConvenioPixMesmaChaveNaMesmaEmpresa(ConvenioCobrancaVO obj, boolean novoObj) throws Exception;

    Map<Integer, ConvenioCobrancaVO> obterMapaConvenios(Integer empresa, int nivelMontarDados) throws Exception;

    List<ConvenioCobrancaVO> consultarPorTipoECodEmpresa(TipoConvenioCobrancaEnum tipoConvenio, int codEmpresa, SituacaoConvenioCobranca situacaoConvenio, int nivelMontarDado) throws Exception;

    List<ConvenioCobrancaVO> obterListaConvenioCobrancaRetentativa(Integer empresa) throws Exception;

    List consultarPorDescricao(String valorConsulta, boolean controlarAcesso, int nivelMontarDados) throws Exception;

    void alterarStatusConciliacaoERede(ConvenioCobrancaVO convenioCobrancaVO) throws Exception;

    void alterarBloquearCobrancasAutomaticas(int codConvenio, boolean bloquearcobrancaautomatica) throws Exception;

    void incluirInformacoesAccessTokenPagBank(String accessToken, String authorizationCode, String refreshToken, int codConvenio) throws Exception;
}

package negocio.interfaces.financeiro;

import negocio.comuns.financeiro.TipoRemessaVO;
import negocio.interfaces.basico.SuperInterface;

import java.util.List;

/**
 * Interface reponsável por criar uma estrutura padrão de comunidação entre a camada de controle
 * e camada de negócio (em especial com a classe Façade). Com a utilização desta interface
 * é possível substituir tecnologias de uma camada da aplicação com mínimo de impacto nas demais.
 * Além de padronizar as funcionalidades que devem ser disponibilizadas pela camada de negócio, por intermédio
 * de sua classe Façade (responsável por persistir os dados das classes VO).
 */
public interface TipoRemessaInterfaceFacade extends SuperInterface {


    public TipoRemessaVO novo() throws Exception;

    public void incluir(TipoRemessaVO obj) throws Exception;

    public void alterar(TipoRemessaVO obj) throws Exception;

    public void excluir(TipoRemessaVO obj) throws Exception;

    public TipoRemessaVO consultarPorChavePrimaria(Integer codigo, int nivelMontarDados) throws Exception;

    public List consultarPorCodigo(Integer valorConsulta, boolean controlarAcesso, int nivelMontarDados) throws Exception;

    public List consultarPorDescricao(String valorConsulta, boolean controlarAcesso, int nivelMontarDados) throws Exception;

    public List consultarPorDescricaoTipoRetorno(String valorConsulta, int nivelMontarDados) throws Exception;

    public List consultarPorArquivoLayoutRemessa(String valorConsulta, boolean controlarAcesso, int nivelMontarDados) throws Exception;

    public List consultarPorTipoRemessa(String valorConsulta, boolean controlarAcesso, int nivelMontarDados) throws Exception;

    public void setIdEntidade(String aIdEntidade);

    public TipoRemessaVO criarOuConsultarSeExistePorDescricao(TipoRemessaVO obj) throws Exception;

    public String consultarJSON() throws Exception;

    public List consultarParaImpressao(String filtro, String ordem, String campoOrdenacao, int i) throws Exception;

}
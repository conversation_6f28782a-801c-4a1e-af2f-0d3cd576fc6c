/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */

package negocio.interfaces.financeiro;

import negocio.comuns.arquitetura.PaginadorDTO;
import negocio.comuns.arquitetura.UsuarioVO;
import negocio.comuns.financeiro.RemessaVO;
import negocio.comuns.financeiro.enumerador.SituacaoRemessaEnum;
import negocio.interfaces.basico.SuperInterface;

import java.sql.SQLException;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 */
public interface RemessaInterfaceFacade extends SuperInterface {

    void alterar(RemessaVO obj) throws Exception;

    RemessaVO consultarPorChavePrimaria(final int codigo) throws Exception;

    /**
     * Operação responsável por excluir no BD um objeto da classe <code>ContratoComposicaoVO</code>.
     * Sempre localiza o registro a ser excluído através da chave primária da entidade.
     * Primeiramente verifica a conexão com o banco de dados e a permissão do usuário
     * para realizar esta operacão na entidade.
     * Isto, através da operação <code>excluir</code> da superclasse.
     *
     * @param obj Objeto da classe <code>ContratoComposicaoVO</code> que será removido no banco de dados.
     * @throws Exception Caso haja problemas de conexão ou restrição de acesso.
     */
    void excluir(RemessaVO obj) throws Exception;

    void incluir(RemessaVO obj) throws Exception;

    public List<RemessaVO> consultar(Date dataInicio, Date dataFim, int codigoEmpresa, int convenio) throws Exception;

    List<RemessaVO> consultar(Date dataInicio, Date dataFim, List<Integer> empresas, List<Integer> convenios, List<SituacaoRemessaEnum> situacaoRemessaEnums,
                              boolean somenteRemessaCancelamento, PaginadorDTO paginadorDTO) throws Exception;

    List<RemessaVO> consultar(Date dataInicio, Date dataFim, List<Integer> empresas, List<Integer> convenios, List<SituacaoRemessaEnum> situacaoRemessaEnums,
                              boolean somenteRemessaCancelamento, PaginadorDTO paginadorDTO, Integer[] tiposRemessaEnum) throws Exception;

    void preencherTransacoes(RemessaVO remessaVO) throws Exception;

    public List<RemessaVO> consultarPorSituacao(SituacaoRemessaEnum situacao, int codigoEmpresa, int convenio) throws Exception;

    public RemessaVO consultarPorMovParcela(Integer codigoMovparcela) throws Exception;

    public boolean processarSituacaoRemessa(Integer codigoRemessa) throws Exception;

    public boolean processarSituacaoRemessaItauBoleto(Integer codigoRemessa) throws Exception;

    RemessaVO consultarRemessaAberta(int convenio, int empresa, UsuarioVO usuarioVO) throws Exception;

    void fecharRemessa(RemessaVO remessaVO) throws SQLException;

    Integer consultarOrdemDaycoval(Date dataPesquisar, Integer codConvenio) throws Exception;

    List<RemessaVO> consultarPorIdentificador(String identificador) throws Exception;

    Integer consultarTotalConvitePorIndicado( Integer empresa) throws Exception;

    void incluirRemessaHistoricoEnvio(RemessaVO remessaVO, UsuarioVO usuarioVO);

    RemessaVO obterUltimaRemessaPorSituacao(final Integer[] situacao, int codigoEmpresa, Boolean cancelamento, int convenio) throws Exception;

    List<RemessaVO> consultarPorSituacao(final Integer[] situacao, Integer empresa, Integer convenio, Date dataRegistroMinima) throws Exception;

    List<RemessaVO> consultarRemessasSemSitucao(Date dataRegistro) throws Exception;

    void excluirComLog(RemessaVO obj, UsuarioVO usuarioVO) throws Exception;

    void alterarSituacao(RemessaVO remessaVO) throws SQLException;

    boolean existeRemessaPendente(Integer convenioCobranca) throws Exception;

    void alterarIdPactoPay(RemessaVO remessaVO) throws SQLException;
}

package negocio.interfaces.financeiro;

import negocio.comuns.financeiro.MovContaTransactionPluggyVO;
import negocio.interfaces.basico.SuperInterface;

import java.util.List;

/**
 * Created by <PERSON> on 19/07/2023.
 */

public interface MovContaTransactionPluggyInterfaceFacade extends SuperInterface {

    void incluir(MovContaTransactionPluggyVO obj) throws Exception;

    List<MovContaTransactionPluggyVO> consultarPorIdTransaction(String idTransaction) throws Exception;

    void excluirByMovConta(int movconta) throws Exception;
}


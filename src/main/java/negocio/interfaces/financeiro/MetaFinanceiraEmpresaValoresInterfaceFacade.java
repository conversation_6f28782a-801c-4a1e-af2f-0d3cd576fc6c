
package negocio.interfaces.financeiro;

import negocio.comuns.financeiro.MetaFinanceiraEmpresaValoresVO;
import negocio.interfaces.basico.SuperInterface;

import java.util.List;

public interface MetaFinanceiraEmpresaValoresInterfaceFacade extends SuperInterface {

    public void incluir(MetaFinanceiraEmpresaValoresVO obj) throws Exception;

    public void incluirSemCommit(MetaFinanceiraEmpresaValoresVO obj) throws Exception;

    public void alterar(MetaFinanceiraEmpresaValoresVO obj) throws Exception;

    public void alterarSemCommit(MetaFinanceiraEmpresaValoresVO obj) throws Exception;

    public void excluir(MetaFinanceiraEmpresaValoresVO obj) throws Exception;

    public void excluirSemCommit(MetaFinanceiraEmpresaValoresVO obj) throws Exception;

    public void excluirPelaMeta(int meta) throws Exception;

    public MetaFinanceiraEmpresaValoresVO consultarPorChavePrimaria(int codigo, int nivelMontarDados) throws Exception;

    public List<MetaFinanceiraEmpresaValoresVO> consultarPorMetaFinanceiraEmpresa(int codigo, int nivelMontarDados) throws Exception;
    public List<MetaFinanceiraEmpresaValoresVO> consultar(int codigoEmpresa, int mes, int ano, int nivelMontarDados) throws Exception;
}
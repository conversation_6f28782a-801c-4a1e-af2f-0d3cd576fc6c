package negocio.interfaces.financeiro;

import br.com.pactosolucoes.enumeradores.EntidadeRateioEnum;
import negocio.comuns.financeiro.CategoriaRateioTO;
import negocio.comuns.financeiro.ModalidadeRateioTO;
import negocio.comuns.financeiro.RateioIntegracaoTO;
import negocio.interfaces.basico.SuperInterface;

import java.sql.SQLException;
import java.util.List;

public interface RateioIntegracaoInterfaceFacade extends SuperInterface {

    public void incluir(List<RateioIntegracaoTO> lista, Integer idEntidade, Integer tipo) throws SQLException, Exception;

    public void incluir(RateioIntegracaoTO rateioIntegracaoTO) throws Exception;

    public List<CategoriaRateioTO> consultarRateiosCategorias() throws SQLException, Exception;

    public List<RateioIntegracaoTO> consultarTodos(int nivelMontarDados)throws Exception;

    public void excluir(Integer entidade, Integer tipo) throws Exception;

    public List<RateioIntegracaoTO> consultar(EntidadeRateioEnum entidade, Integer valor) throws Exception;

    public List<ModalidadeRateioTO> consultar() throws SQLException, Exception;

    public List<Integer> obterModalidesComRateio() throws Exception;

    public List<Integer> obterProdutosComRateio() throws Exception;

    public List<RateioIntegracaoTO> consultarPlanoContaRateio(Integer planoconta) throws SQLException, Exception;

    public void excluirRateiosCentroCusto(Integer planoConta) throws Exception;
    
    public Boolean verificarExistenciaPlano(Integer codigoPlano) throws Exception;

    public List<RateioIntegracaoTO> obterRateiosDevolucaoDinheiro(String tipo) throws Exception;
}

package negocio.interfaces.financeiro;

import negocio.comuns.financeiro.FormaPagamentoPerfilAcessoVO;
import negocio.comuns.financeiro.FormaPagamentoVO;
import negocio.interfaces.basico.SuperInterface;

import java.util.List;

public interface FormaPagamentoPerfilAcessoInterfaceFacade extends SuperInterface {

    void incluir(FormaPagamentoVO fp, List<FormaPagamentoPerfilAcessoVO> empresas) throws Exception;

    void incluir(FormaPagamentoPerfilAcessoVO obj) throws Exception;

    void incluirSemCommit(FormaPagamentoPerfilAcessoVO obj) throws Exception;

    void alterarSemCommit(FormaPagamentoPerfilAcessoVO obj) throws Exception;

    void alterar(FormaPagamentoPerfilAcessoVO obj) throws Exception;

    void excluir(FormaPagamentoPerfilAcessoVO obj) throws Exception;

    List<FormaPagamentoPerfilAcessoVO> consultarPorForma(Integer forma) throws Exception;

    String obterFormaPagamentoPorPerfilAcesso(Integer codigoPerfilAcesso) throws Exception;
}

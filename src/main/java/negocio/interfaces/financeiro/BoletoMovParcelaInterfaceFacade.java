package negocio.interfaces.financeiro;

import negocio.comuns.financeiro.BoletoMovParcelaVO;
import negocio.interfaces.basico.SuperInterface;

import java.util.List;

/**
 * Created with IntelliJ IDEA.
 * User: <PERSON><PERSON>
 * Date: 07/12/2021
 */
public interface BoletoMovParcelaInterfaceFacade extends SuperInterface {

    void incluir(BoletoMovParcelaVO obj) throws Exception;

    void alterar(BoletoMovParcelaVO obj) throws Exception;

    void excluir(BoletoMovParcelaVO obj) throws Exception;

    BoletoMovParcelaVO consultarPorChavePrimaria(final int codigo, int nivelMontarDados) throws Exception;

    List<BoletoMovParcelaVO> consultarPorCodigoBoleto(final int codigoBoleto, int nivelMontarDados) throws Exception;

    List<BoletoMovParcelaVO> consultarPorCodigoContrato(final int codigoContrato, int nivelMontarDados) throws Exception;

    List<BoletoMovParcelaVO> consultarPorCodigoParcela(final int codigoMovParcela, int nivelMontarDados) throws Exception;

    void excluirPorBoleto(final int codBoleto) throws Exception;

    void excluirPorMovParcela(final int codMovParcela) throws Exception;

}

/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */

package negocio.interfaces.financeiro;

import java.sql.SQLException;
import java.util.List;
import java.util.Map;
import negocio.comuns.financeiro.TransacaoMovParcelaVO;
import negocio.interfaces.basico.SuperInterface;

/**
 *
 * <AUTHOR>
 */
public interface TransacaoMovParcelaInterfaceFacade extends SuperInterface {

    void alterar(TransacaoMovParcelaVO obj) throws Exception;

    TransacaoMovParcelaVO consultarPorChavePrimaria(final int codigo) throws Exception;

    List<TransacaoMovParcelaVO> cosultarPorCodigoContrato(final int codigoContrato) throws Exception;

    List<TransacaoMovParcelaVO> cosultarPorCodigoTransacao(final int codigoTransacao) throws Exception;

    List<TransacaoMovParcelaVO> cosultarPorCodigoParcela(final int codigoMovParcela) throws Exception;

    /**
     * Operação responsável por excluir no BD um objeto da classe <code>ContratoComposicaoVO</code>.
     * Sempre localiza o registro a ser excluído através da chave primária da entidade.
     * Primeiramente verifica a conexão com o banco de dados e a permissão do usuário
     * para realizar esta operacão na entidade.
     * Isto, através da operação <code>excluir</code> da superclasse.
     * @param obj    Objeto da classe <code>ContratoComposicaoVO</code> que será removido no banco de dados.
     * @exception Execption Caso haja problemas de conexão ou restrição de acesso.
     */
    void excluir(TransacaoMovParcelaVO obj) throws Exception;

    void excluir(Integer codigo) throws Exception;

    void incluir(TransacaoMovParcelaVO obj) throws Exception;

    void excluirPorTransacao(final int codTransacao) throws Exception;

    void excluirPorMovParcela(final int codMovParcela) throws Exception;

    Map<Integer, Integer> obterListaCodigosExcluirVinculadasATransacaoIncorretamente(int empresa) throws SQLException;

}


package negocio.interfaces.financeiro;

import negocio.comuns.financeiro.RelatorioOrcamentarioConfigVO;
import negocio.interfaces.basico.SuperInterface;

public interface RelatorioOrcamentarioConfigInterfaceFacade extends SuperInterface {

    public void incluir(RelatorioOrcamentarioConfigVO obj) throws Exception;
    public void incluirSemCommit(RelatorioOrcamentarioConfigVO obj) throws Exception;
    public void alterar(RelatorioOrcamentarioConfigVO obj) throws Exception;
    public void alterarSemCommit(RelatorioOrcamentarioConfigVO obj) throws Exception;
    public void excluir(RelatorioOrcamentarioConfigVO obj) throws Exception;
    public void excluirSemCommit(RelatorioOrcamentarioConfigVO obj) throws Exception;
    public String consultarJSON(Integer empresa) throws Exception;
    public RelatorioOrcamentarioConfigVO consultarPorChavePrimaria(Integer codigo) throws Exception;

}
/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */

package negocio.interfaces.financeiro;

import negocio.comuns.financeiro.*;
import negocio.interfaces.basico.SuperInterface;

import java.util.Date;
import java.util.List;

/*
 * <AUTHOR> 17/10/2017
 */
public interface MovParcelaTentativaConvenioInterfaceFacade extends SuperInterface {

    void alterar(MovParcelaTentativaConvenioVO obj) throws Exception;

    void excluir(MovParcelaTentativaConvenioVO obj) throws Exception;

    void incluir(MovParcelaTentativaConvenioVO obj) throws Exception;

    void incluirNrTentativaParcela(MovParcelaVO movParcelaVO, ConvenioCobrancaVO convenioCobrancaVO);

    MovParcelaTentativaConvenioVO consultarPorMovParcelaConvenioCobranca(MovParcelaVO movParcelaVO, ConvenioCobrancaVO convenioCobrancaVO, int nivelMontarDados) throws Exception;

    Integer contarNrTentativaParcelaConvenioCobranca(Integer convenioCobranca, Integer movParcela) throws Exception;

    void totalizadorPorConvenio(TotalizadorRemessaTO totalizador, Integer codigoEmpresa, Date dataInicio, Date dataFim, String convenios) throws Exception;

    String graficoTotalizadorPorConvenio(Integer codigoEmpresa, Date dataInicio, Date dataFim, List<Integer> convenios) throws Exception;

    void totalizadorBIResultadoDCC(TotalizadorRemessaTO totalizador, Integer codigoEmpresa,
                                          Date dataInicio, Date dataFim, List<Integer> convenios, String condicaoNrTentativaConv, String situacaoParcela, String somenteConvenio, String situacaoRemessa,
                                          boolean somenteMes, boolean somenteForaMes) throws Exception;
}

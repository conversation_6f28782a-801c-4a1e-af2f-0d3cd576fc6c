package negocio.interfaces.financeiro;

import java.sql.SQLException;
import java.util.Date;
import java.util.List;

import negocio.comuns.basico.EmpresaVO;
import negocio.comuns.financeiro.ChequeVO;
import negocio.comuns.financeiro.MovPagamentoVO;
import negocio.comuns.financeiro.RecebivelAvulsoTO;
import negocio.interfaces.basico.SuperInterface;

public interface RecebivelAvulsoInterfaceFacade extends SuperInterface {

	public void incluir(RecebivelAvulsoTO recebivel, Integer formaPagamento) throws Exception;
	
	public List<MovPagamentoVO> consultaParaGestaoRecebiveis(Date inicioCompensacao, Date fimCompensacao, 
			Date inicioFaturamento, Date fimFaturamento, EmpresaVO empresa, 
			String matricula, String nome,String nomeTerceiro,ChequeVO chequeVO,
			String codAutorizacao, String nrDocumento, Integer operadoraCartao, Boolean pesquisarComLote,String cpf) throws Exception;
	public List<RecebivelAvulsoTO> consultar() throws Exception;
	
	public void excluir(RecebivelAvulsoTO recebivel) throws Exception;

    public String consultarCartaoJSON() throws SQLException;

    public String consultarChequeJSON() throws SQLException;

    public RecebivelAvulsoTO consultarPorCodigo(boolean tipoCheque, int codigo) throws Exception;
}

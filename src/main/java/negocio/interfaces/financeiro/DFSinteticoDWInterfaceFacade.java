/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */

package negocio.interfaces.financeiro;

import java.util.Collection;
import java.util.Date;
import java.util.List;
import negocio.comuns.financeiro.DFSinteticoDWVO;
import negocio.comuns.financeiro.ReceitaSinteticoDWVO;
import negocio.interfaces.basico.SuperInterface;

/**
 *
 * <AUTHOR>
 */
public interface DFSinteticoDWInterfaceFacade extends SuperInterface  {

    public void incluir(Collection<DFSinteticoDWVO> lista, Integer empresa) throws Exception;

    public List<DFSinteticoDWVO> consultar(Integer empresa) throws Exception;

    public String consultarParaGrafico(Integer empresa, Date dataInicio, Date dataFinal) throws Exception;

    void gerarDadosSinteticoProcesso(Integer empresa, Date inicio, Date fim) throws Exception;

    public void gerarDadosSintetico(Integer empresa, boolean receita, Integer nrMeses) throws Exception;

    public ReceitaSinteticoDWVO consultarReceitaPorFormaPagamento(Integer empresa) throws Exception;

    public ReceitaSinteticoDWVO gerarReceitaPorFormaPagamento(Integer empresa, Date data) throws Exception;
}

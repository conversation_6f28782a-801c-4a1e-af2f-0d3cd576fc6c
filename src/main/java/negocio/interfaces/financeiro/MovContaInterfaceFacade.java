
package negocio.interfaces.financeiro;

import br.com.pactosolucoes.enumeradores.TipoOperacaoLancamento;
import br.com.pactosolucoes.estrutura.paginacao.ConfPaginacao;
import negocio.comuns.arquitetura.LogVO;
import negocio.comuns.arquitetura.UsuarioVO;
import negocio.comuns.basico.EmpresaVO;
import negocio.comuns.basico.PessoaVO;
import negocio.comuns.crm.GenericoTO;
import negocio.comuns.financeiro.AnexoMovContaVO;
import negocio.comuns.financeiro.CartaoCreditoTO;
import negocio.comuns.financeiro.ChequeTO;
import negocio.comuns.financeiro.ChequeVO;
import negocio.comuns.financeiro.ComportamentoConta;
import negocio.comuns.financeiro.ContaVO;
import negocio.comuns.financeiro.FiltroLancamentosTO;
import negocio.comuns.financeiro.HistoricoCartaoVO;
import negocio.comuns.financeiro.ItemRelatorioFechamentoCaixaTO;
import negocio.comuns.financeiro.LoteVO;
import negocio.comuns.financeiro.MovContaVO;
import negocio.comuns.financeiro.MovPagamentoVO;
import negocio.comuns.financeiro.PagamentoMovContaTO;
import negocio.comuns.financeiro.RelatorioFechamentoCaixaTO;
import negocio.comuns.financeiro.RelatorioFechamentoCaixaTO.TotaisRecebidos;
import negocio.interfaces.basico.SuperInterface;

import java.sql.Connection;
import java.sql.SQLException;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 *
 * <AUTHOR>
 */
public interface MovContaInterfaceFacade extends SuperInterface {

    List consultarTodas(int nivelDados) throws Exception;

    void validarDataBloqueio(MovContaVO movConta, boolean validarSomenteDataQuitacao) throws Exception;

    List consultarPaginado(FiltroLancamentosTO filtro, ConfPaginacao conf, Boolean marcarTodos, boolean visualizarMovExcluidas) throws Exception;

    void alterar(MovContaVO obj, int codigoCaixa, boolean validarFormaPagamento) throws Exception;

    void excluir(MovContaVO obj) throws Exception;

    boolean excluirLancamentoEParcelaGeradaAtravesDevolucaoCheque(MovContaVO obj) throws Exception;

    void excluirSemCommit(MovContaVO obj, boolean excluirLote, boolean limparRelacionamentoCheque) throws Exception;

    void incluir(MovContaVO obj, int codigoCaixa, boolean validarFormaPagamento, ComportamentoConta comportamento) throws Exception;

    MovContaVO incluir(MovContaVO obj) throws Exception;

    void excluirLista(List<MovContaVO> listaParaExcluir) throws Exception;

    Map<String, Number> consultarValorTotalLancamentos(FiltroLancamentosTO filtro) throws Exception;

    MovContaVO consultarPorCodigo(int codigoMovConta, int nivelMontarDados) throws Exception;

    MovContaVO consultarUltimoLancamentoPessoa(Integer codigoPessoa, Integer tipoLancamento, int nivelMontarDados) throws Exception;

    MovContaVO gravarQuitacao(MovContaVO movConta, int codigoCaixa, List<PagamentoMovContaTO> formasQuitacao, boolean parcial, boolean gravarCheque, boolean pagamentoConjunto, boolean pagamentoMenor, boolean pagamentoMaior) throws Exception ;

    void gravarQuitacao(MovContaVO movConta, int codigoCaixa, List<PagamentoMovContaTO> formasQuitacao, boolean conciliacao) throws Exception;

    List<MovContaVO> consultarLancamentosPeloAgendamento(int agendamento, Date inicio, Date fim,  int nivelMontarDados) throws Exception ;

    List<MovContaVO> consultarPorAgendamento(Integer codigoAgendamento, int nivelMontarDados) throws Exception ;

    List<MovContaVO> consultarPeloAgendamentoVencimentoNaoQuitadas(int agendamento, Date vencimento, int nivelMontarDados) throws Exception;

    int obterNrParcela(Integer codigo) throws Exception;

    List<MovContaVO> consultarPorConta(int codigoConta, int nivelMontarDados) throws Exception;

    void adicionarLote(Integer movConta, Integer lote) throws Exception;

    void excluirPorLote(int lote) throws  Exception;

    List<AnexoMovContaVO> anexos(Integer codigoMovConta) throws Exception;

    MovContaVO consultarPorLote(int lote, int nivelMontarDados) throws Exception;

    void salvarLoteAvulso(MovContaVO obj, LoteVO loteVO) throws Exception;

	void incluirSemCommit(MovContaVO movConta, int i, boolean b, ComportamentoConta comportamento)  throws Exception;

    void incluirSemCommit(MovContaVO obj, int codigoCaixa, boolean validarFormaPagamento, ComportamentoConta comportamento, boolean validarBloqueio) throws Exception;

    void alterarSomenteApresentarCaixa(MovContaVO movConta) throws SQLException;

    List<TotaisRecebidos> consultarRecebidos(Date inicio, Date fim, Integer empresa, Integer codigoCaixa, String contas) throws Exception;

    List<RelatorioFechamentoCaixaTO.TotaisFormaPagamento> totalizarPorFormasPagamento(Date inicio, Date fim, Integer empresa, Integer caixa, String contas) throws Exception;

    List<ItemRelatorioFechamentoCaixaTO> listarMovimentacoes(Date inicio, Date fim, Integer conta, Integer caixa, Integer empresa, String contas) throws Exception;

    List<MovContaVO> consultarMovContaRelacionadoPorLote(MovContaVO movConta) throws Exception;

    List<MovContaVO> consultarMovContaRelacionados(MovContaVO movConta) throws Exception;

    void excluir(MovContaVO obj, List<MovContaVO> relacionados) throws Exception;

    void salvarLogCaixaAlteracaoValor(MovContaVO obj,Double valorAnterior, ChequeVO cheque, LoteVO lote) throws Exception;

    void alterarSomenteValor(MovContaVO movConta, Double novoValor) throws Exception;

    void alterarSomenteDataQuitacaoPorLote(Date data, Integer lote, boolean dataLancamento) throws Exception;

    void gravarRetiradaRecebivelLote(Date data, List<ChequeTO> cheques,
			List<CartaoCreditoTO> cartoes,
			LoteVO lote, String observacao,
			UsuarioVO responsavel,
			int codigoCaixa) throws Exception;

    List<MovContaVO> verConjuntoPagamentosResumido(MovContaVO origem) throws Exception;

     void gravarRetiradaAutomaticaRecebivel(ContaVO conta, UsuarioVO responsavel,
                                                  String descricao, String observacao, Date data,
                                                  Integer codigoCaixa) throws Exception;

     List<GenericoTO> consultarGenerico(TipoOperacaoLancamento tipo, Date inicio, Date fim, Integer empresa) throws Exception;

     LogVO alterarCentroCustoPlanoContaTaxaCartaoBoleto(Integer planoConta, Integer centroCusto, UsuarioVO user, boolean boleto) throws Exception;

     void lancarSaidaDevolucaoCancelamento(PessoaVO pessoa, EmpresaVO empresa, UsuarioVO usuario,
             Double valor, Date dataDevolucao, String tipo, Integer movProduto) throws Exception;

     List<MovContaVO> consultarDevolucoes(Date faturamentoInicio, Date faturamentoFim,
            Date compensacaoInicio, Date compensacaoFim, Integer empresa, String nomePessoa, String nomeUsuario, String cpf,  String matricula) throws Exception;

     void deleteFromEstorno(Integer contrato, Connection con) throws Exception;

     MovContaVO consultarPorMovProdutoDevolucao(int codigoMovProduto, int nivelMontarDados) throws Exception;

     void gravarMovimentacaoFinanceiraPagamento(Integer movPagamento, Integer movConta) throws SQLException;

     List<HistoricoCartaoVO> consultarHistoricoCartaoDebito(MovPagamentoVO movPagamento) throws Exception;

    boolean validarMovContaUltimaParcela(Integer codigomovconta) throws Exception;

    void finalizarAgendamento(MovContaVO movconta, UsuarioVO usuario) throws Exception;

    Integer consultarMovContaLotePagou(Integer codigoLote)throws Exception;

    List<MovContaVO> consultarContasPagamentoComLote(Integer codigoLote, int nivelMontarDados)throws Exception;

    List<MovContaVO> consultarMovContaCompraEstoque(Integer compraEstoque, int nivelMontarDados)throws Exception;

    Date obterUltimoVencimentoMovContaAgendamento(Integer agendamento, Integer movContaExcluir) throws Exception;

    boolean isDepositoAVouCD(Integer codigomovconta) throws Exception;

    void alterarSomenteChaveArquivo(MovContaVO atestadoContratoVO) throws SQLException;

    List<MovContaVO> consultarMovContaComValoresDivergentesDoMovContaRateio() throws Exception;

    List<MovContaVO> consultarTelaCliente(Integer pessoa, int limit, int offset) throws Exception;

    Integer quantidadePorPessoaTelaCliente(Integer pessoa) throws Exception;

    List<MovContaVO> consultarNaoConciliadosENaoQuitados(Date inicio, Date fim, String descricao, TipoOperacaoLancamento tipo, Integer empresa, int nivelMontarDados) throws Exception;

    List<MovContaVO> consultarPorValorEVencimento(Date inicio, Date fim, Integer empresa, double valor, boolean contasPagar, int nivelMontarDados) throws Exception;

    MovContaVO consultarPorChavePrimaria(int codMovConta, int nivelMontarDados) throws Exception;

    void estornoSimplificado(MovContaVO obj) throws Exception, SQLException;

    void incluirOrigemProcessoCopiarContas(MovContaVO movContaVO) throws Exception;

    void alterarPayloadPix(String payload, int codMovConta) throws SQLException;

    void alterarCodigoBarras(String codigoBarras, int codMovConta) throws SQLException;

    void alterarCpfCnpjBeneficiario(String cpfOuCnpjBeneficiario, int codMovConta) throws SQLException;

    void alterarContaDeConsumo(boolean contaDeConsumo, int codMovConta) throws SQLException;

    void prenderOuLiberarMovContaEmLoteDePagamento(boolean presaEmLoteDePagamento, Integer loteDePagamento, Integer codMovConta) throws Exception;

    void gravarQuitacao(MovContaVO movConta, int codigoCaixa, boolean pagoOrigemWebhook, boolean presaEmLoteDePagamento) throws Exception;

    void gravarQuitacaoMultiplasContas(MovContaVO movConta, int codigoCaixa, List<PagamentoMovContaTO> formasQuitacao, boolean conciliacao) throws Exception;

    void alterarSemAtualizarValorOriginalAlterado(MovContaVO obj, int codigoCaixa, boolean validarFormaPagamento) throws Exception;

    int consultarQtdLancamentosPessoa(Integer codigoPessoa) throws Exception;

    List<MovContaVO> consultar(String sql, final int nivelMontarDados) throws SQLException, Exception;

    Integer retornarCodigoCompraEstoquePorMovConta(Integer movConta) throws Exception;

}


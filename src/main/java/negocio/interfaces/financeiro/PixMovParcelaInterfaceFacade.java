package negocio.interfaces.financeiro;

import negocio.comuns.financeiro.PixMovParcelaVO;
import negocio.comuns.financeiro.PixVO;
import negocio.interfaces.basico.SuperInterface;

import java.sql.SQLException;
import java.util.List;

public interface PixMovParcelaInterfaceFacade extends SuperInterface {

    void incluir(PixVO pixVO, List<PixMovParcelaVO> pixMovParcelas) throws Exception;

    void incluir(PixMovParcelaVO pixMovParcelaVO) throws Exception;

    void excluirPorContrato(Integer contrato) throws SQLException;

    void excluir(Integer movparcela, Integer pix) throws SQLException;

    void excluirPorPix(List<Integer> codigosPix) throws SQLException;

    void excluirPorParcela(Integer movparcela) throws SQLException;

    void excluirPorCodigo(Integer codigo) throws SQLException;
}

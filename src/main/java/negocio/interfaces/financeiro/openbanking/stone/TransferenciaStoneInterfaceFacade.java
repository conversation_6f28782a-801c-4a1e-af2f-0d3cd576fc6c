package negocio.interfaces.financeiro.openbanking.stone;


import negocio.comuns.financeiro.openbanking.stone.TransferenciaStoneVO;
import negocio.interfaces.basico.SuperInterface;

public interface TransferenciaStoneInterfaceFacade extends SuperInterface {

    void incluir(TransferenciaStoneVO obj) throws Exception;

    void alterar(TransferenciaStoneVO obj) throws Exception ;

    TransferenciaStoneVO montarTransferencia(String response, Integer movContaZW, Integer empresaZW,  Integer movContaDesc) throws Exception;

    TransferenciaStoneVO buscarTransferenciaStoneById(String id, Integer empresa);

    TransferenciaStoneVO buscarTransferenciaStoneByMovConta(Integer movConta, Integer empresa);
}

package negocio.interfaces.financeiro.openbanking.stone;

import negocio.comuns.financeiro.openbanking.stone.PagamentoStoneVO;
import negocio.interfaces.basico.SuperInterface;


public interface PagamentoStoneInterfaceFacade extends SuperInterface {

    void incluir(PagamentoStoneVO obj) throws Exception;

    void alterar(PagamentoStoneVO obj) throws Exception ;

    PagamentoStoneVO montarPagamento(String response, Integer movContaZW, Integer empresaZW) throws Exception;

    PagamentoStoneVO buscarPagamentoStoneById(String id, Integer empresa);

    PagamentoStoneVO buscarPagamentoStoneByMovConta(Integer movConta, Integer empresa);
}

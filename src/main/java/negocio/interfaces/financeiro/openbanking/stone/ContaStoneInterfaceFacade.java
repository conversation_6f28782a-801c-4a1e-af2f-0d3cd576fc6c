package negocio.interfaces.financeiro.openbanking.stone;

import negocio.comuns.financeiro.openbanking.stone.ContaStoneVO;
import negocio.interfaces.basico.SuperInterface;

public interface ContaStoneInterfaceFacade extends SuperInterface {

    void incluir(ContaStoneVO obj) throws Exception;

    void alterar(ContaStoneVO obj) throws Exception ;

    ContaStoneVO montarContaStoneInserirZwFin(String response, Integer empresaZW) throws Exception;

    String buscarAccountIdContaStone(Integer empresaZW);

    ContaStoneVO buscarContaStone(Integer empresa);
}

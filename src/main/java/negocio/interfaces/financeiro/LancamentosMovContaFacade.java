package negocio.interfaces.financeiro;

import negocio.comuns.financeiro.FiltroLancamentosTO;
import negocio.comuns.financeiro.MovContaVO;
import negocio.interfaces.basico.SuperInterface;

import java.sql.ResultSet;
import java.util.List;

/**
 * Facade usado no relatório de lançamentos de MovConta do ZW
 *
 * <AUTHOR>
 * @since 14/08/2018
 */
public interface LancamentosMovContaFacade extends SuperInterface {

    /**
     * Realiza uma consulta paginada no banco usando as configurações informadas e os filtros indicados
     *
     * @param tabelaResultado                 tabela de resultados que veio do banco
     * @param isAgruparPlanoContasCentroCusto caso o resultado deva ser agrupado pelos dados de plano de contas com centro de custo
     * @param isIntegracaoAlterData           a integração com a alter data está habilitada ou não?
     * @return A lista de {@link MovContaVO} encontrada
     * @throws Exception Caso ocorra algum problema na consulta
     */
    List<MovContaVO> consultar(ResultSet tabelaResultado, Boolean isAgruparPlanoContasCentroCusto,
                               Boolean isIntegracaoAlterData, Boolean marcarTodos) throws Exception;

    /**
     * Calcula o SELECT que será realizado para a consulta {@link LancamentosMovContaFacade#consultar(ResultSet, Boolean, Boolean)}
     *
     * @param filtros filtros para a consulta
     * @return A {@link String} com o select que será realizado no banco
     * @throws Exception Caso ocorra qualquer problema na construção da query
     * @see {@link LancamentosMovContaFacade#consultar(ResultSet, Boolean, Boolean)}
     */
    String getSQLSelect(FiltroLancamentosTO filtros) throws Exception;

    /**
     * Calcula o SELECT que será realizado para a consulta {@link LancamentosMovContaFacade#consultar(ResultSet, Boolean, Boolean)}
     * somando os resultados encontrados para retornar os valores das totalizações
     *
     * @param filtros             filtros para a consulta
     * @param resultadosAgrupados os resultados devem ser agrupados para exibir as totalizações?
     * @return A {@link String} com o select que será realizado no banco
     * @throws Exception Caso ocorra qualquer problema na construção da query
     * @see {@link LancamentosMovContaFacade#consultar(ResultSet, Boolean, Boolean)}
     */
    String getSQLSelect(FiltroLancamentosTO filtros, Boolean resultadosAgrupados) throws Exception;

}

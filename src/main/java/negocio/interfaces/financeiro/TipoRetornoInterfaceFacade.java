package negocio.interfaces.financeiro;

import negocio.comuns.financeiro.TipoRetornoVO;
import negocio.interfaces.basico.SuperInterface;

import java.util.List;

/**
 * Interface reponsável por criar uma estrutura padrão de comunidação entre a camada de controle
 * e camada de negócio (em especial com a classe Façade). Com a utilização desta interface
 * é possível substituir tecnologias de uma camada da aplicação com mínimo de impacto nas demais.
 * Além de padronizar as funcionalidades que devem ser disponibilizadas pela camada de negócio, por intermédio
 * de sua classe Façade (responsável por persistir os dados das classes VO).
 */
public interface TipoRetornoInterfaceFacade extends SuperInterface {


    public TipoRetornoVO novo() throws Exception;

    public void incluir(TipoRetornoVO obj) throws Exception;

    public void alterar(TipoRetornoVO obj) throws Exception;

    public void excluir(TipoRetornoVO obj) throws Exception;

    public TipoRetornoVO consultarPorChavePrimaria(Integer codigo, int nivelMontarDados) throws Exception;

    public List consultarPorCodigo(Integer valorConsulta, boolean controlarAcesso, int nivelMontarDados) throws Exception;

    public List consultarPorDescricao(String valorConsulta, boolean controlarAcesso, int nivelMontarDados) throws Exception;

    public List consultarPorArquivoLayoutRetorno(String valorConsulta, boolean controlarAcesso, int nivelMontarDados) throws Exception;

    public void setIdEntidade(String aIdEntidade);

    public TipoRetornoVO criarOuConsultarSeExistePorDescricao(TipoRetornoVO obj) throws Exception;

    public String consultarJSON() throws Exception;

    public List consultarParaImpressao(String filtro, String ordem, String campoOrdenacao, int i) throws Exception;

}
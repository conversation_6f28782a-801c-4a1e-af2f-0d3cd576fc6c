package negocio.interfaces.financeiro;

import br.com.pactosolucoes.enumeradores.TipoOperacaoLancamento;
import negocio.comuns.financeiro.*;
import negocio.interfaces.basico.SuperInterface;

import java.util.List;

public interface HistoricoChequeInterfaceFacade extends SuperInterface {

    void incluir(HistoricoChequeVO historico) throws Exception;

    void incluir(HistoricoChequeVO historico, boolean controlarTransacao) throws Exception;

    void incluirSemCommit(HistoricoChequeVO historico) throws Exception;

    List<HistoricoChequeVO> consultarPorChequeComposicao(String codigos) throws Exception;

    void mudarStatus(StatusCheque novStatus, String codigosComposicao) throws Exception;

    public void inicializarHistorico(ChequeVO cheque, int movConta, LoteVO lote, TipoOperacaoLancamento tipoOperacao) throws Exception;

    public void getContaLoteCheque(ChequeTO cheque, boolean datafimisnull) throws Exception;

    public void getCodigoLoteCheque(ChequeVO cheque, boolean datafimisnull) throws Exception;
    
    public boolean chequeJaSaiuLote(String codigosComposicao, Integer codigoLote) throws Exception;

    void excluirPorMovConta(Integer codigoMovConta)throws Exception;

    public ContaVO getContaCheque(Integer cheque, boolean datafimisnull) throws Exception;

}

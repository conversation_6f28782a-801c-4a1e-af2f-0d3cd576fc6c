package negocio.interfaces.financeiro;

import negocio.comuns.financeiro.BancoVO;
import negocio.interfaces.basico.SuperInterface;

import java.util.List;

/**
 * Interface reponsável por criar uma estrutura padrão de comunidação entre a camada de controle
 * e camada de negócio (em especial com a classe Façade). Com a utilização desta interface
 * é possível substituir tecnologias de uma camada da aplicação com mínimo de impacto nas demais.
 * Além de padronizar as funcionalidades que devem ser disponibilizadas pela camada de negócio, por intermédio
 * de sua classe Façade (responsável por persistir os dados das classes VO).
 */
public interface BancoInterfaceFacade extends SuperInterface {

    public BancoVO novo() throws Exception;

    public void incluir(BancoVO obj) throws Exception;

    public void alterar(BancoVO obj) throws Exception;

    public void alterarSemCommit(BancoVO obj) throws Exception ;

    public void excluir(BancoVO obj) throws Exception;

    public BancoVO consultarPorChavePrimaria(Integer codigo, int nivelMontarDados) throws Exception;

    List<BancoVO> consultarTodos(int nivelMontarDados) throws Exception;

    List<BancoVO> consultarTodosComISPB(int nivelMontarDados) throws Exception;

    List<BancoVO> consultarTodosComISPB(boolean somenteNumeros, String texto, int nivelMontarDados) throws Exception;

    public List consultarPorCodigo(Integer valorConsulta, boolean controlarAcesso, int nivelMontarDados) throws Exception;

    public List consultarPorCodigoBanco(Integer valorConsulta, boolean controlarAcesso, int nivelMontarDados) throws Exception;

    public List consultarPorNome(String valorConsulta, boolean controlarAcesso, int nivelMontarDados) throws Exception;

    public void setIdEntidade(String aIdEntidade);

    public BancoVO consultarCodigoBanco(Integer valorConsulta, boolean controlarAcesso, int nivelMontarDados) throws Exception;

    public BancoVO consultarCodigo(Integer valorConsulta, boolean controlarAcesso, int nivelMontarDados) throws Exception;

    public BancoVO criarOuConsultarSeExistePorNome(BancoVO obj) throws Exception;

    public String consultarJSON() throws Exception;

    public List consultarParaImpressao(String filtro, String ordem, String campoOrdenacao, int i) throws Exception;

}

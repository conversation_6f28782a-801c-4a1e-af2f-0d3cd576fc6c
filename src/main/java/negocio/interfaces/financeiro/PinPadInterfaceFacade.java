package negocio.interfaces.financeiro;

import negocio.comuns.financeiro.FormaPagamentoVO;
import negocio.comuns.financeiro.PinPadVO;
import negocio.comuns.financeiro.enumerador.OpcoesPinpadEnum;
import negocio.interfaces.basico.SuperInterface;

import java.sql.SQLException;
import java.util.List;

public interface PinPadInterfaceFacade extends SuperInterface {

    public PinPadVO novo() throws Exception;

    public void incluir(FormaPagamentoVO fp, List<PinPadVO> pinpads) throws Exception;

    public void incluir(PinPadVO obj) throws Exception;

    public void incluirSemCommit(PinPadVO obj) throws Exception ;

    public void alterar(PinPadVO obj) throws Exception;

    public void excluir(PinPadVO obj) throws Exception;

    public void excluirPorPinPad(Integer codigoPinPad) throws Exception;

    List<PinPadVO> consultarPorFormaEmpresa(Integer forma, Integer empresa, OpcoesPinpadEnum opcoesPinpadEnum) throws Exception;
    public List<PinPadVO> consultarPorForma(Integer forma) throws Exception;

    boolean existePinpad(OpcoesPinpadEnum opcoesPinpadEnum, Integer forma, Integer empresa) throws Exception;

    PinPadVO consultarPorCodigo(Integer pinPadSelecionado) throws Exception;

    PinPadVO consultarPorChavePrimaria(Integer codigo) throws Exception;

    PinPadVO obterPinPadTotem(Integer codigoFormaPagamento) throws Exception;

    void incluirHistorico(Integer pinPadPedido, String idExterno, String operacao, String dados) throws Exception;

    String consultarJSON(Integer empresa, String sEcho, String clausulaLike) throws Exception;

    List consultarParaImpressao(String filtro, String ordem, String campoOrdenacao, int i) throws SQLException;

    List<PinPadVO> consultarParaTotem() throws Exception;
}

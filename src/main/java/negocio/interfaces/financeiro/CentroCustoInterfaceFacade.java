package negocio.interfaces.financeiro;

import java.util.List;
import java.util.Map;

import relatorio.negocio.jdbc.financeiro.CentroCustosDRE;
import negocio.comuns.financeiro.CentroCustoTO;
import negocio.interfaces.basico.SuperInterface;

/**
 * Interface da DAO que representa a tabela financentrocustos
 * 
 * <AUTHOR>
 */
public interface CentroCustoInterfaceFacade extends SuperInterface {

    public void incluir(final CentroCustoTO obj) throws Exception;

    public void alterar(final CentroCustoTO obj) throws Exception;

    public void excluir(final CentroCustoTO obj) throws Exception;

    public void moverNos(CentroCustoTO centroDestino, CentroCustoTO centroOrigem)
            throws Exception;

    public String obterCodigoProximoFilho(String centroDest) throws Exception;

    public List<CentroCustoTO> consultar(String codigo, String nome, Integer codigoInterno) throws Exception;

    public void excluirTodosCentros() throws Exception;

    public List<CentroCustoTO> consultarTodos() throws Exception;

    public Boolean verificarExistenciaPlano(String centroCusto) throws Exception;

    public Map<Integer, String> obterCodigoCentroIrmaos(String codigoPai) throws Exception;

    public List<Integer> obterCodigoFilhos(String codigoPai, String exceto) throws Exception;

    public void atualizarCodigoCentroCustos(Integer codigoPlano, String codigoNovo, String codigoAntesAlteracao, List<Integer> codigosFilhos) throws Exception;

    public CentroCustoTO obter(String codigoCentro) throws Exception;

    public boolean consultarSeExisteCentroCustos() throws Exception;

    public CentroCustoTO obter(int pkey) throws Exception;

    public String consultarCodigoPaiContendoSomenteUmNumeroEAdicionaUm() throws Exception;
    
    public List<CentroCustosDRE> obterCentroCustos() throws Exception;

    public boolean verificarExisteRelacionamento();

    public boolean verificarExisteRelacionamento(Integer codigo);

    public void trocarCentroCustos(Integer codigoCentroAntigo, Integer codigoCentroNovo) throws Exception;

    CentroCustoTO consultarPorChavePrimaria(Integer codigo) throws Exception;
}

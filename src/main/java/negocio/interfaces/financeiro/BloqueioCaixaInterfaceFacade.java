/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package negocio.interfaces.financeiro;

import java.util.List;
import negocio.comuns.financeiro.BloqueioCaixaVO;
import negocio.interfaces.basico.SuperInterface;

/**
 *
 * <AUTHOR>
 */
public interface BloqueioCaixaInterfaceFacade extends SuperInterface {
    
    public void incluir(BloqueioCaixaVO bloqueioCaixaVo) throws Exception;
    public void alterar(BloqueioCaixaVO bloqueioCaixaVo) throws Exception;
    public void excluir(BloqueioCaixaVO bloqueioCaixaVo)throws Exception;
    public BloqueioCaixaVO consultarBloqueioAtual(Integer codigoEmpresa, int nivelMontarDados)throws Exception;
    public List<BloqueioCaixaVO> consultar(Integer codigoEmpresa, int nivelMontarDados)throws Exception;
    
}

/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */

package negocio.interfaces.financeiro;

import br.com.pactosolucoes.comuns.json.TicketMedioJSON;
import java.util.Date;
import java.util.List;
import negocio.comuns.basico.ConfiguracaoBIVO;
import negocio.interfaces.basico.SuperInterface;
import relatorio.negocio.comuns.basico.TicketMedioVO;
import relatorio.negocio.comuns.sad.RotatividadeSinteticoDWVO;

/**
 *
 * <AUTHOR>
 */
public interface TicketMedioInterfaceFacade extends SuperInterface {

    public Integer numeroContratosAtivosComBolsa(Date inicio, Date fim, Integer empresa) throws Exception;

    public Double caixaFaturamentoRecebido(Integer empresa, Date inicio, Date fim) throws Exception;
    
    public Double caixaCompetencia(Integer empresa, Date dataBase) throws Exception;

    public TicketMedioVO carregarBITicketMedio(Integer empresa, Date dataBase, RotatividadeSinteticoDWVO sintetico,
                                               boolean somenteProdutosNaCompetencia, boolean consultarPeloDRE) throws Exception;

    public String povoarJSON(TicketMedioVO ticket, Boolean competencia) throws Exception;

    public String caixaFaturamentoRecebidoJSON(Integer empresa, Date inicio, Date fim) throws Exception;

    public String caixaReceitaJSON(Integer empresa, Date inicio, Date fim) throws Exception;
    
    public TicketMedioVO montarTicketMedio(boolean atualizar, List<ConfiguracaoBIVO> cfgs, Integer empresa, Date data) throws Exception;
    
    public void gerarDadosSintetico(Integer empresa) throws Exception;
    
    public List<TicketMedioJSON> consultarParaGrafico(Integer empresa, boolean incluirBolsas, boolean usarMedia, Integer mes, Integer ano, Boolean consultarPeloDre) throws Exception;
    
}

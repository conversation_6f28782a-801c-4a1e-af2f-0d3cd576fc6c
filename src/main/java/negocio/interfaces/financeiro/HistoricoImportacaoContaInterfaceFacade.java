package negocio.interfaces.financeiro;

import negocio.comuns.basico.EmpresaVO;
import negocio.comuns.crm.GenericoTO;
import negocio.comuns.financeiro.ComportamentoConta;
import negocio.comuns.financeiro.ContaVO;
import negocio.comuns.financeiro.HistoricoImportacaoContaVO;
import negocio.interfaces.basico.SuperInterface;
import servicos.pactobank.dto.ExtratoMovimentoZWDTO;

import java.sql.SQLException;
import java.util.Date;
import java.util.List;

public interface HistoricoImportacaoContaInterfaceFacade extends SuperInterface {

    public void incluir(HistoricoImportacaoContaVO obj) throws Exception;

    public void alterar(HistoricoImportacaoContaVO obj) throws Exception;

    public void excluir(HistoricoImportacaoContaVO obj) throws Exception;

    public HistoricoImportacaoContaVO obterPorCodigo(int codigo) throws Exception;

    public List<HistoricoImportacaoContaVO> consultar(HistoricoImportacaoContaVO filtro) throws Exception;

    public String consultarJSON() throws Exception;

}

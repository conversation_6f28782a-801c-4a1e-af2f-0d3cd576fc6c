package negocio.interfaces.financeiro;

import negocio.comuns.financeiro.RetornoRemessaVO;
import negocio.interfaces.basico.SuperInterface;

import java.util.List;

/**
 * Created by GlaucoT on 30/06/2015
 */
public interface RetornoRemessaInterfaceFacade extends SuperInterface {

    void incluir(RetornoRemessaVO obj) throws Exception;

    void alterar(RetornoRemessaVO obj, boolean temArquivo) throws Exception;

    void alterarSomenteArquivo(RetornoRemessaVO obj) throws Exception;

    void excluir(RetornoRemessaVO obj) throws Exception;

    RetornoRemessaVO consultarPorNomeArquivo(String nomeArquivo) throws Exception;

    RetornoRemessaVO consultarPorCodigo(Integer codigoRetornoRemessa) throws Exception;

    List<RetornoRemessaVO> consultarTodos() throws Exception;
}


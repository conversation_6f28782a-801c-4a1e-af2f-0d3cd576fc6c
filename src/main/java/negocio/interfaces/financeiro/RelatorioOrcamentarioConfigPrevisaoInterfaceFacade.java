
package negocio.interfaces.financeiro;

import negocio.comuns.financeiro.RelatorioOrcamentarioValoresPrevisaoVO;
import negocio.interfaces.basico.SuperInterface;

public interface RelatorioOrcamentarioConfigPrevisaoInterfaceFacade extends SuperInterface {

    public void incluirSemCommit(RelatorioOrcamentarioValoresPrevisaoVO obj) throws Exception;
    public void alterarSemCommit(RelatorioOrcamentarioValoresPrevisaoVO obj) throws Exception;
    public void excluirSemCommit(Integer obj) throws Exception;

}
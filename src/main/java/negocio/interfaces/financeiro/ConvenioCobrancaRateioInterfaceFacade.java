package negocio.interfaces.financeiro;

import negocio.comuns.arquitetura.UsuarioVO;
import negocio.comuns.financeiro.ConvenioCobrancaRateioVO;
import negocio.comuns.financeiro.ConvenioCobrancaVO;
import negocio.interfaces.basico.SuperInterface;

import java.util.List;

/**
 * Created with IntelliJ IDEA.
 * User: <PERSON><PERSON>
 * Date: 15/05/2020
 */
public interface ConvenioCobrancaRateioInterfaceFacade extends SuperInterface {

    void gravar(ConvenioCobrancaVO obj, UsuarioVO usuarioVO) throws Exception;

    void incluir(ConvenioCobrancaRateioVO obj) throws Exception;

    void alterar(ConvenioCobrancaRateioVO obj) throws Exception;

    void excluir(ConvenioCobrancaRateioVO obj) throws Exception;

    List<ConvenioCobrancaRateioVO> consultarPorConvenioCobranca(Integer convenioCobranca, int nivelMontarDados) throws Exception;

}

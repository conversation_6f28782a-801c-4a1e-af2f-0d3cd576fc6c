/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */

package negocio.interfaces.financeiro;

import negocio.comuns.financeiro.DFSinteticoDWVO;
import negocio.comuns.financeiro.DRESinteticoDWVO;
import negocio.comuns.financeiro.ReceitaSinteticoDWVO;
import negocio.interfaces.basico.SuperInterface;

import java.util.Collection;
import java.util.Date;
import java.util.List;

/**
 *
 * <AUTHOR>
 */
public interface DRESinteticoDWInterfaceFacade extends SuperInterface  {

    public void incluir(Collection<DRESinteticoDWVO> lista, Integer empresa) throws Exception;

    public List<DRESinteticoDWVO> consultar(Integer empresa) throws Exception;

    public String consultarParaGrafico(Integer empresa, Date dataInicio, Date dataFinal) throws Exception;

    void gerarDadosSinteticoProcesso(Integer empresa, Date inicio, Date fim) throws Exception;

    public void gerarDadosSintetico(Integer empresa, boolean receita, Integer nrMeses) throws Exception;

    public ReceitaSinteticoDWVO consultarReceitaPorFormaPagamento(Integer empresa) throws Exception;

    public ReceitaSinteticoDWVO gerarReceitaPorFormaPagamento(Integer empresa, Date data) throws Exception;
}

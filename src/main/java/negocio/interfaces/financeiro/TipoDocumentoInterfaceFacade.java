
package negocio.interfaces.financeiro;

import java.sql.SQLException;
import java.util.List;
import negocio.comuns.financeiro.TipoDocumentoVO;
import negocio.interfaces.basico.SuperInterface;

/**
 * Interface da DAO que representa a tabela finantipodocumento
 * 
 * <AUTHOR>
 */
public interface TipoDocumentoInterfaceFacade extends SuperInterface {
	
	public void alterar(TipoDocumentoVO obj) throws Exception;

	public void excluir(TipoDocumentoVO obj) throws Exception;

	public void incluir(TipoDocumentoVO obj) throws Exception;

        public List<TipoDocumentoVO> consultar(TipoDocumentoVO filtro) throws Exception;

	public List consultarTodas(int nivelDados) throws Exception;

	public TipoDocumentoVO consultarPorCodigo(Integer codigo) throws Exception;

	public String consultarJSON() throws Exception;

	public List consultarParaImpressao(String filtro, String ordem, String campoOrdenacao, Integer empresa) throws SQLException;
}
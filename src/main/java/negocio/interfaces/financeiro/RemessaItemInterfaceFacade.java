/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */

package negocio.interfaces.financeiro;

import br.com.pactosolucoes.enumeradores.TotalizadorBIDCCEnum;
import negocio.comuns.arquitetura.PaginadorDTO;
import negocio.comuns.arquitetura.UsuarioVO;
import negocio.comuns.financeiro.*;
import negocio.comuns.financeiro.enumerador.TipoCobrancaEnum;
import negocio.interfaces.basico.SuperInterface;
import org.jboleto.JBoleto;

import java.sql.Connection;
import java.sql.SQLException;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 */
public interface RemessaItemInterfaceFacade extends SuperInterface {

    public void alterar(RemessaItemVO obj) throws Exception;

    public RemessaItemVO consultarPorChavePrimaria(final int codigo, final int nivel) throws Exception;

    public List<RemessaItemVO> consultarPorCodigoParcela(final int codigoMovParcela, final int nivel) throws Exception;

    public List<RemessaItemVO> consultarPorCodigoRemessa(final int codigoRemessa, final int nivel) throws Exception;

    Integer consultarQtdPorCodigoRemessa(final int codigoRemessa) throws Exception;

    Double consultarPorCodigoValorRemessaAceito(final RemessaVO remessaVO) throws Exception;

    Double consultarPorCodigoValorRemessa(final int codigoRemessa) throws Exception;

    boolean existeItensASerProcessados(final int codigoRemessa, boolean isDCO) throws Exception;
    /**
     * Operação responsável por excluir no BD um objeto da classe <code>ContratoComposicaoVO</code>.
     * Sempre localiza o registro a ser excluído através da chave primária da entidade.
     * Primeiramente verifica a conexão com o banco de dados e a permissão do usuário
     * para realizar esta operacão na entidade.
     * Isto, através da operação <code>excluir</code> da superclasse.
     *
     * @param obj Objeto da classe <code>ContratoComposicaoVO</code> que será removido no banco de dados.
     * @throws Exception Caso haja problemas de conexão ou restrição de acesso.
     */
    public void excluir(RemessaItemVO obj) throws Exception;

    public void incluir(RemessaItemVO obj) throws Exception;

    public List<RemessaItemVO> consultarPorRecibo(final int codRecibo, final int nivel) throws Exception;

    public List<RemessaItemVO> consultarPorCodigoPessoa(final int codigoPessoa, final int nivel) throws Exception;

    public List<RemessaItemVO> consultarTelaCliente(final int codigoPessoa, int limit, int offset, TipoCobrancaEnum[] arrayTiposCobrancaEnum, int nivel, int movParcela) throws Exception ;

    public Integer obterCountRemessaCliente(Integer pessoa, TipoCobrancaEnum[] arrayTiposCobrancaEnum) throws Exception;

    public List<MovParcelaVO> consultarParcelasEmAbertoRemessa(final int remessa) throws Exception;

    RemessaItemVO obterUltimoItemRemessaPorCodigoParcela(final int codigoMovParcela, int nivelMontarDados, boolean parcelaVencida) throws Exception;

    RemessaItemVO obterUltimoItemRemessaPorCodigoParcela(final int codigoMovParcela, int nivelMontarDados) throws Exception;

    public void preencherTotalizador(Integer codigoEmpresa, String colunas, boolean count, String[] colunasCount, String nrTentativas, String situacaoParcela, Date dataInicio, Date dataFim, List<Integer> convenios, String defaultRec, TotalizadorRemessaTO totalizador, String situacaoRemessa, boolean somenteMes, boolean somenteForaMes, String somenteConvenio) throws Exception;

    public List<RemessaItemVO> consultarItens(Integer codigoEmpresa, String colunas, boolean count, String[] colunasCount, String nrTentativas, String situacaoParcela, Date dataInicio, Date dataFim, List<Integer> convenios, String descricaoPagamento, String situacaoRemessa, boolean somenteMes, boolean somenteForaMes, String somenteConvenio) throws Exception;
    
    public List<RemessaItemVO> consultarPorParcelas(final String codigosParcelas, final int nivel) throws Exception;

    public List<RemessaItemVO> consultarPorParcelasConvenioCobranca(final String codigosParcelas, final int codigoConvenioCobranca, final int nivel) throws Exception;

    public boolean existeParcelaEmRemessaGeradaouAguardando(Integer movparcela) throws Exception;

    RemessaItemVO consultarPorParcelaPorCodigoRemessa(Integer codigoParcela, Integer codigoRemessa, int nivel) throws Exception;

    List<RemessaItemVO> consultarCodigos(String codRemessaItem, int nivelMontarDados) throws Exception;

    List<RemessaItemVO> consultarCodigosPorConvenio(String codRemessaItem, int convenioCobranca, int nivelMontarDados) throws Exception;

    String consultarMesesEmAberto(RemessaItemVO itemReimpressao) throws Exception;

    void gerarReciboItemRemessa(Integer codigoremessaitem, String codAutorizacao, Date dataCompensacao, UsuarioVO usuarioVO) throws Exception;

    RemessaItemVO consultarUltimoBoletoParcela(MovParcelaVO movParcelaVO) throws Exception;

    boolean remessaItemVinculadaVariasParcelas(RemessaItemVO remessaItemVO)throws Exception;

    String alterarProps(Integer codigoRemessaItem, String propriedadeAdicionar) throws  Exception;

    /**
     * Realiza a consulta dos {@link RemessaItemVO} a partir dos seus identificadores.
     * @param codRemessaItem
     * @param nivelmontardadosDadosbasicos
     * @return
     * @throws Exception
     */
    List<RemessaItemVO> consultarIdentificadores(String codIdentificadores, Integer codigoConvenio, int nivelmontardadosDados) throws  Exception;

    void totalizadorBIResultadoDCC(TotalizadorRemessaTO totalizador, Integer codigoEmpresa,
                                            Date dataInicio, Date dataFim,
                                            String nrTentativas, String situacaoParcela, String somenteConvenio, String situacaoRemessa,
                                            boolean somenteMes, boolean somenteForaMes, List<Integer> convenios, String nrTentativasFiltrar) throws Exception;

    void preencherTotalizadorBIResultadoDCC(TotalizadorBIDCCEnum totalizadorBIDCCEnum, TotalizadorRemessaTO totalizadorRemessaTO, Integer codigoEmpresa, Date dataInicio, Date dataFim, boolean incluirCancelados, boolean somenteMes, boolean somenteForaMes, List<Integer> convenios) throws Exception;

    RemessaItemVO consultarPorIdentificador(final int identificador, final int nivelMontarDados) throws Exception;

    void limparDadosRetorno(RemessaItemVO item) throws Exception;

    void estornarPagamentoBoleto(RemessaItemVO item, final String key, UsuarioVO usuarioVO) throws Exception;

    void totalizadorPorConvenio(TotalizadorRemessaTO totalizador, Integer codigoEmpresa, Date dataInicio, Date dataFim, Integer convenio) throws Exception;

    List<RemessaItemVO> consultarPorMovPagamento(final int codigoMovPAgamento, final int nivelMontarDados) throws Exception;

    void excluirPorCodigoRemessa(int codigoRemessa) throws SQLException;

    void marcarRemessaComoContabilizadaPacto(boolean contabilizada, RemessaVO remessa) throws Exception;

    void alterarJsonEstorno(RemessaItemVO obj) throws Exception;

    Integer consultarQtdParcelasItem(Integer remessaItem) throws Exception;

    List<RemessaItemVO> consultarPorCodigoRemessa(final int codigoRemessa, PaginadorDTO paginadorDTO, final int nivel) throws Exception;

    JBoleto obterJBoleto(RemessaItemVO remessaItem, Connection con) throws Exception;
}

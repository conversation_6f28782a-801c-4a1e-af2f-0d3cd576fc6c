package negocio.interfaces.financeiro;

import negocio.comuns.financeiro.LoteKobanaItemVO;
import negocio.interfaces.basico.SuperInterface;

import java.util.List;

public interface LoteKobanaItemInterfaceFacade extends SuperInterface {

    LoteKobanaItemVO incluir(LoteKobanaItemVO obj) throws Exception;

    void alterar(final LoteKobanaItemVO obj) throws Exception;

    void atualizarByCodigoBarrasAndCodLote(final LoteKobanaItemVO obj) throws Exception;

    List<LoteKobanaItemVO> consultarByLote(int codLote, int nivelMntarDados) throws Exception;

    LoteKobanaItemVO consultarByUID(String uid, int nivelMntarDados) throws Exception;
}

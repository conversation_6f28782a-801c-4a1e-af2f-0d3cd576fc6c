/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */

package negocio.interfaces.financeiro;

import br.com.pactosolucoes.estrutura.paginacao.ConfPaginacao;

import java.sql.SQLException;
import java.util.List;
import negocio.comuns.financeiro.CaixaMovContaVO;
import negocio.comuns.financeiro.CaixaVO;
import negocio.comuns.financeiro.MovContaVO;
import negocio.interfaces.basico.SuperInterface;

/**
 *
 * <AUTHOR>
 */
public interface CaixaMovContaInterfaceFacade extends SuperInterface {


    public void incluir(CaixaMovContaVO caixaMovContaVo) throws Exception;
    public void alterar(CaixaMovContaVO caixaMovContaVo) throws Exception;
    public void alterarDescricao(int movConta, String descricao) throws Exception;
    public void excluir(CaixaMovContaVO caixaMovContaVo)throws Exception;
    public List<CaixaMovContaVO> consultar(CaixaVO caixaVo, int nivelMontarDados)throws Exception;
    public List<CaixaMovContaVO> consultarPaginado(int codigoCaixa, int nivelMontarDados, ConfPaginacao confPaginacao) throws Exception;
    public void excluirPorMovConta(int movConta) throws Exception;
    public boolean movContaJaIncluido(int codigoMovConta) throws SQLException;
    public CaixaVO movContaCaixaAberto(Integer codigoMovConta) throws Exception;
    CaixaVO consultarPorMovConta(Integer codigoMovConta) throws Exception;
    public CaixaMovContaVO consultarPorMovConta(MovContaVO movConta) throws Exception;
    int validarCaixaAberto(MovContaVO movContaVO) throws Exception;

}

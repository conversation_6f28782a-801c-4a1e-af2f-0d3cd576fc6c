package negocio.interfaces.financeiro;

import negocio.comuns.financeiro.ConvenioCobrancaRateioItemVO;
import negocio.comuns.financeiro.ConvenioCobrancaRateioVO;
import negocio.interfaces.basico.SuperInterface;

import java.util.List;

/**
 * Created with IntelliJ IDEA.
 * User: <PERSON><PERSON>
 * Date: 15/05/2020
 */
public interface ConvenioCobrancaRateioItemInterfaceFacade extends SuperInterface {

    void incluir(ConvenioCobrancaRateioItemVO obj) throws Exception;

    void alterar(ConvenioCobrancaRateioItemVO obj) throws Exception;

    void excluir(ConvenioCobrancaRateioItemVO obj) throws Exception;

    void excluirPorRateio(ConvenioCobrancaRateioVO obj) throws Exception;

    List<ConvenioCobrancaRateioItemVO> consultarPorConvenioCobrancaRateio(Integer convenioCobrancaRateio) throws Exception;
}

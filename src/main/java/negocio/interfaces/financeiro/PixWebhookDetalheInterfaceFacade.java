package negocio.interfaces.financeiro;

import negocio.comuns.financeiro.PixWebhookDetalheVO;
import negocio.interfaces.basico.SuperInterface;


/**
 * Created by <PERSON> on 21/11/2023.
 */

public interface PixWebhookDetalheInterfaceFacade extends SuperInterface {

    void incluir(PixWebhookDetalheVO pixWebhookDetalheVO) throws Exception;

    PixWebhookDetalheVO consultar(int pix, String txId) throws  Exception;

    boolean pixJaFoiProcessado(int pix, String txId) throws  Exception;

}

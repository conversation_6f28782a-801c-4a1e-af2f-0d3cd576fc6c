package negocio.interfaces.financeiro;

import negocio.comuns.arquitetura.UsuarioVO;
import negocio.comuns.basico.ClienteVO;
import negocio.comuns.financeiro.MovParcelaVO;
import negocio.comuns.financeiro.RemessaItemMovParcelaVO;
import negocio.comuns.financeiro.RemessaItemVO;
import negocio.interfaces.basico.SuperInterface;

import java.sql.SQLException;
import java.util.List;

/**
 * Created by GlaucoT on 17/04/2015
 */
public interface RemessaItemMovParcelaInterfaceFacade extends SuperInterface {

    void incluir(RemessaItemMovParcelaVO obj) throws Exception;

    void incluirSemCommit(RemessaItemMovParcelaVO obj) throws Exception;

    void alterarMovParcela(RemessaItemMovParcelaVO obj) throws Exception;

    void alterarJsonEstorno(RemessaItemMovParcelaVO obj) throws Exception;

    List<RemessaItemMovParcelaVO> consultarPorRemessaItem(Integer codigoRemessaItem, int nivelMontarDados) throws Exception;

    public List<RemessaItemMovParcelaVO> consultarPorParcelaItem(Integer codigoParcela, int nivelMontarDados) throws Exception;

    List<RemessaItemMovParcelaVO> alterarRemessaItemMovParcela(RemessaItemVO item,  List<MovParcelaVO> parcela, List<MovParcelaVO> parcelasRenegociadas) throws Exception;

    void excluirPorCodigoRemessa(int codigoRemessa) throws SQLException;

    void removerParcelaBoleto(ClienteVO clienteVO, RemessaItemVO remessaItemVO, UsuarioVO usuarioVO) throws Exception;
}

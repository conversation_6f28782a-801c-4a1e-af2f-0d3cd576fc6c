/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */

package negocio.interfaces.financeiro;

import java.sql.SQLException;
import java.util.Date;
import java.util.List;
import negocio.comuns.financeiro.CaixaVO;
import negocio.comuns.financeiro.RelatorioFechamentoCaixaTO.CaixaAgrupar;
import negocio.interfaces.basico.SuperInterface;

/**
 *
 * <AUTHOR>
 */
public interface CaixaInterfaceFacade extends SuperInterface {

    public void incluir(CaixaVO caixaVo) throws Exception;
    public void alterar(CaixaVO caixaVo) throws Exception;
    public void excluir(CaixaVO caixaVo)throws Exception;
    public CaixaVO consultarCaixaEmAberto(int codigoUsuario, int codigoEmpresa, int nivelMontarDados)throws Exception;
    public CaixaVO consultarCaixa(int codigoCaixa, int nivelMontarDados)throws Exception;
    public List<CaixaVO> consultar(String sql, int nivelMontarDados)throws Exception;
    public List<CaixaVO> consultarHistoricoCaixa(int codigoEmpresa,
                                                 int codigoUsuario,
                                                 int codigoUsuarioLogado,
                                                 Date dataIniAbertura,
                                                 Date dataFimAbertura,
                                                 Date dataIniFechamento,
                                                 Date dataFimFechamento,
                                                 Date dataIniTrabalho,
                                                 Date dataFimTrabalho,
                                                 Integer codigoCaixa,
                                                 Boolean permissaoConsultarCaixa,
                                                 int nivelMontarDados)throws Exception;
    public void obterValoresResumidosCaixa(CaixaVO caixa) throws SQLException;
    public void obterValoresResumidosCaixa(CaixaVO caixa, boolean openBankingAtivado) throws SQLException;
    public List<CaixaAgrupar> consultarCaixasMovimentadas(Date inicio, Date fim, Integer empresa) throws Exception;
    public void reabrirCaixa(CaixaVO caixa) throws Exception;
    public CaixaVO consultarCaixaParaMovConta(Integer codigoMovConta, Integer nivelMontarDados) throws Exception;
    public List<CaixaVO> consultarCaixasEmAberto(int codigoUsuario, int codigoEmpresa, int nivelMontarDados)throws Exception;

    CaixaVO consultarCaixaPorMovConta(Integer codigoMovConta, Integer nivelMontarDados) throws Exception;
    CaixaVO abrirCaixa(CaixaVO obj) throws Exception;
}

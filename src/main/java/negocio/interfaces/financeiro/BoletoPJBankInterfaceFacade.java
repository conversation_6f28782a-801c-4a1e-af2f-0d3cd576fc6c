package negocio.interfaces.financeiro;

import negocio.comuns.arquitetura.UsuarioVO;
import negocio.comuns.basico.BoletoEmailTO;
import negocio.comuns.contrato.ContratoVO;
import negocio.comuns.financeiro.BoletoPJBankVO;
import negocio.comuns.financeiro.EstornoMovProdutoVO;
import negocio.comuns.financeiro.MovParcelaVO;
import negocio.interfaces.basico.SuperInterface;

import java.util.Date;
import java.util.List;

/**
 * Interface reponsável por criar uma estrutura padrão de comunidação entre a camada de controle
 * e camada de negócio (em especial com a classe Façade). Com a utilização desta interface
 * é possível substituir tecnologias de uma camada da aplicação com mínimo de impacto nas demais.
 * Além de padronizar as funcionalidades que devem ser disponibilizadas pela camada de negócio, por intermédio
 * de sua classe Façade (responsável por persistir os dados das classes VO).
 */
public interface BoletoPJBankInterfaceFacade extends SuperInterface {

    public BoletoPJBankVO novo() throws Exception;

    public void incluir(BoletoPJBankVO obj) throws Exception;

    List<BoletoPJBankVO> excluirBoletoPJBank(EstornoMovProdutoVO obj) throws Exception;

    List<BoletoPJBankVO> excluirBoletoPJBank(MovParcelaVO movParcelaVO, UsuarioVO usuarioResponsavel) throws Exception;

    List<BoletoPJBankVO> excluirBoletoPJBank(UsuarioVO usuarioEstornoVO, ContratoVO contratoVO) throws Exception;

    public BoletoPJBankVO consultarPorID(String idUnico, int nivelMontarDados) throws Exception;

    BoletoPJBankVO consultarPorChavePrimaria(Integer codigo, int nivelMontarDados) throws Exception;

    List<BoletoPJBankVO> consultarTodos(Integer pessoa, int nivelMontarDados) throws Exception;

    public void setIdEntidade(String aIdEntidade);

    List<BoletoPJBankVO> consultarPorMovParcela(Integer movParcela) throws Exception;

    BoletoPJBankVO gerarBoletoParcela(Integer parcela, Integer convenio, String chave, Integer diasVencidoCasoParcelaVencida,
                                      Date dataVencimentoFixo, Double multaValorFixo, Double jurosValorFixo) throws Exception;

    void processarWebhook(String json) throws Exception;

    String sincronizarBoleto(BoletoPJBankVO obj, String origem) throws Exception;

    void alterarReciboPagamento(BoletoPJBankVO obj) throws Exception;

    List<BoletoPJBankVO> consultarPorReciboPagamento(Integer reciboPagamento, int nivelMontarDados) throws Exception;

    void estornarReciboPagamento(Integer reciboPagamento) throws Exception;

    void estornarMovParcela(Integer movParcela) throws Exception;

    void alterarRegistroBancario(BoletoPJBankVO obj) throws Exception;

    void enviarEmailBoletoPjBank(String chave, Integer movParcela, String[] emailEnviar,
                                 boolean enviarLinkBoleto, boolean enviarBoletoEmAnexo) throws Exception;

    void enviarEmailBoletoPjBank(String chave, BoletoPJBankVO obj, String[] emailEnviar,
                                 boolean enviarLinkBoleto, boolean enviarBoletoEmAnexo) throws Exception;

    void enviarEmailBoletoLink(String chave, String link, Integer empresa,
                               Integer pessoa, String[] emailEnviar) throws Exception;

    void gerarHTMLModeloPadraoBoleto(String chave, BoletoEmailTO boletoEmailTO) throws Exception;

    List<BoletoPJBankVO> consultarBoletosStatusErrado() throws Exception;

    String cancelarBoleto(BoletoPJBankVO obj, UsuarioVO usuarioVO) throws Exception;

    List<BoletoPJBankVO> consultarBoletosSincronizar(Integer convenioCobranca) throws Exception;
}

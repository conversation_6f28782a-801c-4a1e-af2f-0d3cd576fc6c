
package negocio.interfaces.financeiro;

import negocio.comuns.financeiro.FluxoCaixaTO;
import negocio.comuns.financeiro.MovContaRateioVO;
import negocio.comuns.financeiro.MovContaVO;

import java.sql.SQLException;
import java.util.Date;
import java.util.List;
import negocio.interfaces.basico.SuperInterface;

/**
 *
 * <AUTHOR>
 */
public interface MovContaRateioInterfaceFacade extends SuperInterface {
    /* Consulta todos os tipos de ocnta
     *
     * @param nivelDados
     *            - não utilizado
     * @return - List
     * @throws Exception
     */

    public List consultarTodas(int nivelDados) throws Exception;

    /**
     * Consulta os tipos de contas restringindo aos valores passados pelo filtro
     *
     * @param filtro
     *            - ContaVO
     * @return List (ContasVO)
     * @throws Exception
     */
    public List<MovContaRateioVO> consultarPorMovConta(Integer movConta, int nivelMontarDados) throws Exception;

    /**
     * Altera o tipo de Conta
     *
     * @param obj
     *            - ContaVO
     * @throws Exception
     */

    public void alterarFormaPagtoValor(MovContaRateioVO obj) throws Exception;

    /**
     * Exclui o tipo de conta
     *
     * @param obj
     *            - ContaVO, somente o código precisa estar preenchido
     * @throws Exception
     */
    public void excluir(MovContaRateioVO obj) throws Exception;

    /**
     * Inclui o tipo de conta
     *
     * @param obj
     *            - ContaVO
     * @throws Exception
     */
    public void incluir(MovContaRateioVO obj, MovContaVO movContaVO, boolean validarFormaPagamento) throws Exception;

    void incluir(MovContaRateioVO obj, MovContaVO movContaVO) throws Exception;

    /**
     * Inclui uma lista de movcontasrateio para a movconta em questão
     * @param movConta
     * @param objetos
     * @throws Exception
     */
    public void incluirMovContasRateio(MovContaVO movConta, List objetos, boolean validarFormaPagamento) throws Exception;

    void incluirMovContasRateio(MovContaVO movConta, List rateios) throws Exception;


    /**
     * Altera movContasRateio
     * @param movConta
     * @param objetos
     * @throws Exception
     */
    public void alterarMovContasRateio(MovContaVO movConta, List<MovContaRateioVO> objetos, boolean validarFormaPagamento) throws Exception;
    
    /**
     * Exclui todas as movcontasrateio referentes ao movconta em questao
     * @param movContaRateio
     * @throws Exception
     */
    public void excluirMovContasRateio(Integer movConta) throws Exception;
    public List<MovContaRateioVO> consultarPorConta(int conta, Date inicio, Date fim) throws Exception;
    
    public Boolean verificarExistenciaPlano(Integer codigoPlano) throws Exception;

    List<FluxoCaixaTO> consultarFluxoCaixa(Date dataInicial, Date dataFinal, boolean previsto, Integer empresa) throws Exception;

    public void alterarValorMovContaRateioPeloCodigo(Integer codigo, Double valor) throws SQLException;

    void incluirOrigemProcessoCopiarContas(MovContaRateioVO obj) throws Exception;
}

package negocio.interfaces.financeiro;

import negocio.comuns.basico.enumerador.TipoTokenEnum;
import negocio.interfaces.basico.SuperInterface;
import servicos.pix.TokenVO;

import java.sql.SQLException;

public interface TokenInterfaceFacade extends SuperInterface {

    TokenVO incluir(TokenVO obj) throws Exception;

    TokenVO consultarAptoParaReutilizacao(int codConvenioCobranca, TipoTokenEnum tipoTokenEnum) throws Exception;

    void incrementarUtilizacao(int codigo) throws SQLException;

}

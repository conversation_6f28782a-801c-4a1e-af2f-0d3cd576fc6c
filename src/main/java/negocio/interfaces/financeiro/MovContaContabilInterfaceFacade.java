package negocio.interfaces.financeiro;

import negocio.comuns.financeiro.MovContaContabilVO;
import negocio.interfaces.basico.SuperInterface;

/**
 * Created by ulisses on 09/02/2017.
 */
public interface MovContaContabilInterfaceFacade extends SuperInterface {

    void incluir(MovContaContabilVO movContaContabilVO)throws Exception;
    void alterar(MovContaContabilVO movContaContabilVO)throws Exception;
    void excluir(Integer codigoMovConta)throws Exception;
    MovContaContabilVO consultar(Integer codigoMovConta)throws Exception;

}

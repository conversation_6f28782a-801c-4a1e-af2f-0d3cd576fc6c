
package negocio.interfaces.financeiro;

import negocio.comuns.financeiro.MetaFinanceiraEmpresaVO;
import negocio.interfaces.basico.SuperInterface;

import java.util.Date;
import java.util.List;

public interface MetaFinanceiraEmpresaInterfaceFacade extends SuperInterface {

    public void incluir(MetaFinanceiraEmpresaVO obj) throws Exception;

    public void incluirSemCommit(MetaFinanceiraEmpresaVO obj) throws Exception;

    public void alterar(MetaFinanceiraEmpresaVO obj) throws Exception;

    public void alterarSemCommit(MetaFinanceiraEmpresaVO obj) throws Exception;

    public void excluir(MetaFinanceiraEmpresaVO obj) throws Exception;

    public void excluirSemCommit(MetaFinanceiraEmpresaVO obj) throws Exception;

    public MetaFinanceiraEmpresaVO consultarPorChavePrimaria(int codigo, int nivelMontarDados) throws Exception;

    /**
     *
     * @param empresa           0 para consultar de todas as empresas
     * @param ano
     * @param mes
     * @param descricao
     * @param nivelMontarDados
     * @return
     * @throws Exception
     */
    List<MetaFinanceiraEmpresaVO> consultarPorEmpresaAnoMesDescricao(int empresa, int ano, int mes, String descricao, int nivelMontarDados) throws Exception;

    public List<MetaFinanceiraEmpresaVO> consultarPorEmpresaPeriodo(int empresa, Date periodoDe, Date periodoAte, String descricao, int nivelMontarDados) throws Exception;

    public String consultarJSON(Integer empresa) throws Exception;

    public List consultarParaImpressao(String filtro, String ordem, String campoOrdenacao, int i, String moeda) throws Exception;

}
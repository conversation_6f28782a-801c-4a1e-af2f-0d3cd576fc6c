package negocio.interfaces.financeiro;

import negocio.comuns.basico.ProdutoDevolverCancelamentoEmpresaVO;
import negocio.interfaces.basico.SuperInterface;

import java.util.List;

public interface ProdutoDevolverCancelamentoEmpresaInterfaceFacade extends SuperInterface {

    ProdutoDevolverCancelamentoEmpresaVO novo() throws Exception;

    void incluir(ProdutoDevolverCancelamentoEmpresaVO obj) throws Exception;

    void alterar(ProdutoDevolverCancelamentoEmpresaVO obj) throws Exception;

    void alterarProdutosDevolverCancelEmpresas(Integer empresa, List<ProdutoDevolverCancelamentoEmpresaVO> objetos) throws Exception;

    void excluir(ProdutoDevolverCancelamentoEmpresaVO obj) throws Exception;

    List consultarPorEmpresa(Integer empresa, int nivelMontarDados) throws Exception;

    List consultarPorCodigo(Integer valorConsulta, boolean controlarAcesso, int nivelMontarDados) throws Exception;

}

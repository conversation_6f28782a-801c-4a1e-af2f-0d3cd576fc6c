package negocio.interfaces.financeiro;

import negocio.comuns.arquitetura.UsuarioVO;
import negocio.comuns.basico.BoletoEmailTO;
import negocio.comuns.basico.EmpresaVO;
import negocio.comuns.basico.PessoaVO;
import negocio.comuns.contrato.ContratoVO;
import negocio.comuns.financeiro.*;
import negocio.comuns.financeiro.enumerador.OrigemCobrancaEnum;
import negocio.comuns.financeiro.enumerador.SituacaoBoletoEnum;
import negocio.comuns.financeiro.enumerador.TipoBoletoEnum;
import negocio.interfaces.basico.SuperInterface;

import java.util.Date;
import java.util.List;

/**
 * Created with IntelliJ IDEA.
 * User: <PERSON><PERSON>
 * Date: 07/12/2021
 */
public interface BoletoInterfaceFacade extends SuperInterface {

    void incluir(BoletoVO obj) throws Exception;

    void alterar(BoletoVO obj) throws Exception;

    void alterarMovPagamentoReciboPagamento(BoletoVO obj) throws Exception;

    void alterarSituacaoComLog(BoletoVO obj, SituacaoBoletoEnum situacaoBoletoEnum,
                               UsuarioVO usuarioVO, String operacao, String log) throws Exception;
    void alterarSituacao(BoletoVO obj, SituacaoBoletoEnum situacaoBoletoEnum) throws Exception;

    BoletoVO consultarPorChavePrimaria(final int codigo, int nivelMontarDados) throws Exception;

    BoletoVO consultarPorIdexternoTipo(String idExterno, TipoBoletoEnum tipoBoletoEnum, int nivelMontarDados) throws Exception;

    List<BoletoVO> consultarPorCodigoPessoa(Integer pessoa, int nivelMontarDados) throws Exception;

    Integer consultarTelaClienteQtd(Integer pessoa) throws Exception;

    List<BoletoVO> consultarTelaCliente(Integer pessoa, int limit, int offset) throws Exception;

    BoletoVO consultarChavePrimariaTelaCliente(Integer codigoBoleto) throws Exception;

    PessoaCPFTO obterDadosPessoaPagador(Integer empresa, PessoaVO pessoaVO, boolean validarNome, boolean validarCPF) throws Exception;

    List<BoletoVO> gerarBoletoPorParcela(PessoaVO pessoaVO, ConvenioCobrancaVO convenioCobrancaVO, List<MovParcelaVO> listaParcelas,
                                         UsuarioVO usuarioVO, OrigemCobrancaEnum origemCobrancaEnum, boolean cobrarMultaJuros, boolean registrarBoletoAgora) throws Exception;

    BoletoVO gerarBoleto(PessoaVO pessoaVO, ConvenioCobrancaVO convenioCobrancaVO, List<MovParcelaVO> listaParcelas,
                         Date dataVencimento, UsuarioVO usuarioVO, OrigemCobrancaEnum origemCobrancaEnum,
                         boolean cobrarMultaJuros, boolean registrarBoletoAgora) throws Exception;

    BoletoVO gerarBoleto(PessoaVO pessoaVO, ConvenioCobrancaVO convenioCobrancaVO, List<MovParcelaVO> listaParcelas,
                         Date dataVencimento, UsuarioVO usuarioVO, OrigemCobrancaEnum origemCobrancaEnum,
                         boolean cobrarMultaJuros, Double descontoValorFixo, Double descontoPercentual, boolean verificarBoletoExistente,
                         boolean registrarBoletoAgora) throws Exception;

    void cancelarBoleto(BoletoVO boletoVO, UsuarioVO usuarioVO, String origem) throws Exception;

    String sincronizarBoleto(BoletoVO boletoVO, UsuarioVO usuarioVO, String origem) throws Exception;

    void gerarHTMLModeloPadraoBoleto(String chave, BoletoEmailTO boletoEmailTO) throws Exception;

    void enviarEmailBoletoLink(String chave, String link, Integer empresa, Integer pessoa,
                               String[] emailEnviar) throws Exception;

    void enviarEmailBoleto(String chave, BoletoVO obj, String[] emailEnviar,
                           boolean enviarLinkBoleto, boolean enviarBoletoEmAnexo) throws Exception;

    void incluirBoletoHistorico(BoletoVO boleto, String operacao, String dados) throws Exception;

    boolean existeBoletoPendentePorMovParcela(Integer movParcela, boolean desconsiderarPagos) throws Exception;

    List<BoletoVO> obterBoletosPendentePorMovParcela(Integer movParcela, boolean desconsiderarPagos,
                                                     int nivelMontarDados) throws Exception;

    String obterDetalheBoletoPJBank(Integer boleto) throws Exception;

    List<BoletoVO> excluirBoletoContratoOrigemEstornoContrato(ContratoVO contratoVO, UsuarioVO usuarioVO) throws Exception;

    List<BoletoVO> excluirBoletoContratoOrigemCancelamentoContrato(ContratoVO contratoVO, UsuarioVO usuarioVO) throws Exception;

    void cancelarBoletos(List<BoletoVO> listaBoletosCancelar, UsuarioVO usuarioVO, String operacao) throws Exception;

    List<BoletoVO> consultarParaProcesso(String sql) throws Exception;

    List<BoletoVO> consultarPorDataRegistro(Date dataInicio, Date dataFim, ConvenioCobrancaVO convenioCobrancaVO, EmpresaVO empresaVO) throws Exception;

    List<BoletoVO> consultarPorCodigos(List<Integer> codigos, int nivelMontarDados) throws Exception;

    void incluirHistoricoWebHookBoleto(Integer conveniocobranca, Integer empresa, String retorno) throws Exception;

    String consultarBoletoWebHook(Integer conveniocobranca, Integer empresa) throws Exception;
}

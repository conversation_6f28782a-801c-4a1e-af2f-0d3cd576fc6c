package negocio.interfaces.plano;

import negocio.comuns.plano.TipoModalidadeVO;
import negocio.interfaces.basico.SuperInterface;

import java.util.List;
import java.util.Map;

/**
 * Interface reponsável por criar uma estrutura padrão de comunidação entre a camada de controle
 * e camada de negócio (em especial com a classe Façade). Com a utilização desta interface 
 * é possível substituir tecnologias de uma camada da aplicação com mínimo de impacto nas demais.
 * Além de padronizar as funcionalidades que devem ser disponibilizadas pela camada de negócio, por intermédio
 * de sua classe Façade (responsável por persistir os dados das classes VO).
 */
public interface TipoModalidadeInterfaceFacade extends SuperInterface {

    TipoModalidadeVO novo() throws Exception;

    void incluir(TipoModalidadeVO obj) throws Exception;

    void alterar(TipoModalidadeVO obj) throws Exception;

    void excluir(TipoModalidadeVO obj) throws Exception;

    TipoModalidadeVO consultarPorChavePrimaria(Integer codigo, int nivelMontarDados) throws Exception;

    TipoModalidadeVO consultarPorIdentificador(Integer codigoIdentificador, int nivelMontarDados) throws Exception;

    List consultarPorCodigo(Integer valorConsulta, boolean controlarAcesso, int nivelMontarDados) throws Exception;

    List<TipoModalidadeVO> consultarPorNome(String valorConsulta, boolean controlarAcesso, int nivelMontarDados) throws Exception;

    List consultarPorNomeTipoModalidadeComLimite(String valorConsulta, boolean controlarAcesso, int nivelMontarDados) throws Exception;

    public String consultarJSON(String codEmpresa) throws Exception;

    void setIdEntidade(String aIdEntidade);

    void incluirSemCommit(TipoModalidadeVO obj) throws Exception;

    Map<Integer, String> consultarTipoModalidadesSimplificado() throws Exception;

    public List consultarParaImpressao(String filtro, String ordem, String campoOrdenacao, Integer empresa) throws Exception;

    public List<TipoModalidadeVO> consultarPorNome(String nome) throws Exception;

}

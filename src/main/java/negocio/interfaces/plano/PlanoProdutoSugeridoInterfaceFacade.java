package negocio.interfaces.plano;

import negocio.comuns.plano.PlanoProdutoSugeridoVO;
import java.util.List;
import negocio.interfaces.basico.SuperInterface;

/**
 * Interface reponsável por criar uma estrutura padrão de comunidação entre a camada de controle
 * e camada de negócio (em especial com a classe Façade). Com a utilização desta interface 
 * é possível substituir tecnologias de uma camada da aplicação com mínimo de impacto nas demais.
 * Além de padronizar as funcionalidades que devem ser disponibilizadas pela camada de negócio, por intermédio
 * de sua classe Façade (responsável por persistir os dados das classes VO).
 */
public interface PlanoProdutoSugeridoInterfaceFacade extends SuperInterface {

    public PlanoProdutoSugeridoVO novo() throws Exception;

    public void incluir(PlanoProdutoSugeridoVO obj) throws Exception;

    public void alterar(PlanoProdutoSugeridoVO obj) throws Exception;

    public void excluir(PlanoProdutoSugeridoVO obj) throws Exception;

    public PlanoProdutoSugeridoVO consultarPorChavePrimaria(Integer codigo, int nivelMontarDados) throws Exception;

    public List consultarPorCodigo(Integer valorConsulta, boolean controlarAcesso, int nivelMontarDados) throws Exception;

    public List consultarPorNomePlano(String valorConsulta, int nivelMontarDados) throws Exception;

    public List consultarPorDescricaoProduto(String valorConsulta, int nivelMontarDados) throws Exception;

    public List<PlanoProdutoSugeridoVO> consultarPorProduto(Integer codigoProduto, int nivelMontarDados) throws Exception;

    public void setIdEntidade(String aIdEntidade);

    public List consultarPlanoProdutoSugeridos(Integer plano, boolean produtoAtivo, int nivelMontarDados) throws Exception;

    /**
     * Operação responsável por incluir objetos da <code>ProdutoSugeridoVO</code> no BD.
     * Garantindo o relacionamento com a entidade principal <code>plano.Modalidade</code> através do atributo de vínculo.
     * @param objetos List contendo os objetos a serem gravados no BD da classe.
     * @exception Exception  Erro de conexão com o BD ou restrição de acesso a esta operação.
     */
    public void incluirPlanoProdutoSugeridos(Integer planoPrm, List objetos) throws Exception;

    /**
     * Operação responsável por alterar todos os objetos da <code>ProdutoSugeridoVO</code> contidos em um Hashtable no BD.
     * Faz uso da operação <code>excluirProdutoSugeridos</code> e <code>incluirProdutoSugeridos</code> disponíveis na classe <code>ProdutoSugerido</code>.
     * @param objetos  List com os objetos a serem alterados ou incluídos no BD.
     * @exception Exception  Erro de conexão com o BD ou restrição de acesso a esta operação.
     */
    public void alterarPlanoProdutoSugeridos(Integer plano, List objetos) throws Exception;

    /**
     * Operação responsável por excluir todos os objetos da <code>ProdutoSugeridoVO</code> no BD.
     * Faz uso da operação <code>excluir</code> disponível na classe <code>ProdutoSugerido</code>.
     * @param <code>modalidade</code> campo chave para exclusão dos objetos no BD.
     * @exception Exception  Erro de conexão com o BD ou restrição de acesso a esta operação.
     */
    public void excluirPlanoProdutoSugeridos(Integer plano) throws Exception;

    public boolean vinculadoAoContrato(PlanoProdutoSugeridoVO pps) throws Exception;

}

package negocio.interfaces.plano;

import negocio.comuns.plano.NivelTurmaVO;
import negocio.interfaces.basico.SuperInterface;
import org.json.JSONObject;

import java.util.List;

/**
 * Interface reponsável por criar uma estrutura padrão de comunidação entre a camada de controle
 * e camada de negócio (em especial com a classe Façade). Com a utilização desta interface
 * é possível substituir tecnologias de uma camada da aplicação com mínimo de impacto nas demais.
 * Além de padronizar as funcionalidades que devem ser disponibilizadas pela camada de negócio, por intermédio
 * de sua classe Façade (responsável por persistir os dados das classes VO).
 */
public interface NivelTurmaInterfaceFacade extends SuperInterface {

    public NivelTurmaVO novo() throws Exception;

    public void incluir(NivelTurmaVO obj) throws Exception;

    public void alterarSemCommit(NivelTurmaVO obj) throws Exception;

    public void alterar(NivelTurmaVO obj) throws Exception;

    public void excluir(NivelTurmaVO obj) throws Exception;

    public NivelTurmaVO consultarPorChavePrimaria(Integer codigo, int nivelMontarDados) throws Exception;

    public List consultarPorCodigo(Integer valorConsulta, boolean controlarAcesso, int nivelMontarDados) throws Exception;

    public List consultarPorDescricao(String valorConsulta, boolean controlarAcesso, int nivelMontarDados) throws Exception;

    public void setIdEntidade(String aIdEntidade);

    public List<NivelTurmaVO> consultarPorTurma(Integer valorConsulta, boolean controlarAcesso, int nivelMontarDados) throws Exception;

    public String consultarJSON(Integer empresa) throws Exception;

    public List consultarParaImpressao(String filtro, String ordem, String campoOrdenacao, int i) throws Exception;

}
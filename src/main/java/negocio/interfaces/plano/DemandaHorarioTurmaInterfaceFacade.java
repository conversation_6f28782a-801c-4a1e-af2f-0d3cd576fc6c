package negocio.interfaces.plano;

import org.json.JSONArray;
import negocio.comuns.plano.AnaliticoDemandaHorarioTurmaTO;
import negocio.comuns.plano.SinteticoDemandaHorarioTurmaTO;
import negocio.comuns.plano.DemandaHorarioTurmaVO;
import negocio.interfaces.basico.SuperInterface;

import java.util.Date;
import java.util.List;

public interface DemandaHorarioTurmaInterfaceFacade extends SuperInterface {
    DemandaHorarioTurmaVO novo() throws Exception;
    void incluir(DemandaHorarioTurmaVO obj) throws Exception;
    void excluir(Integer cliente, Integer horarioturma, Date data) throws Exception;
    List<SinteticoDemandaHorarioTurmaTO> consultarSintetico(JSONArray filtroProfessores, JSONArray filtroModalidaes, JSONArray filtroAmbientes,
                                                            JSONArray filtroTurmas, JSONArray filtroHorarios, JSONArray filtroDiasDaSemana,
                                                            Date dataInicio, Date dataFim) throws Exception;

    List<AnaliticoDemandaHorarioTurmaTO> consultarAnalitico(JSONArray filtroProfessores, JSONArray filtroModalidaes, JSONArray filtroAmbientes,
                                                                                  JSONArray filtroTurmas, JSONArray filtroHorarios, JSONArray filtroDiasDaSemana,
                                                                                  Date dataInicio, Date dataFim) throws Exception;

    void excluir(Integer horarioturma) throws Exception;
}

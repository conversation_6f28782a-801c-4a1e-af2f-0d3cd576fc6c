package negocio.interfaces.plano;

import negocio.comuns.plano.ImpostoProdutoCfopVO;
import negocio.interfaces.basico.SuperInterface;

import java.util.Optional;

/**
 * Interface reponsável por criar uma estrutura padrão de comunidação entre a
 * camada de controle e camada de negócio (em especial com a classe Façade). Com
 * a utilização desta interface é possível substituir tecnologias de uma camada
 * da aplicação com mínimo de impacto nas demais. Além de padronizar as
 * funcionalidades que devem ser disponibilizadas pela camada de negócio, por
 * intermédio de sua classe Façade (responsável por persistir os dados das
 * classes VO).
 */
public interface ImpostoProdutoCfopInterfaceFacade extends SuperInterface {

    public ImpostoProdutoCfopVO novo() throws Exception;

    public void incluir(ImpostoProdutoCfopVO obj) throws Exception;

    public void alterar(ImpostoProdutoCfopVO obj) throws Exception;

    public void excluir(ImpostoProdutoCfopVO obj) throws Exception;

    public ImpostoProdutoCfopVO consultarPorChavePrimaria(Integer codigo, int nivelMontarDados) throws Exception;

    public Optional<ImpostoProdutoCfopVO> consultarPorCfopENcm(String cfop, String ncm) throws Exception;

    }

package negocio.interfaces.plano;

import negocio.comuns.plano.ConfiguracaoProdutoEmpresaVO;
import negocio.comuns.plano.ProdutoVO;
import negocio.interfaces.basico.SuperInterface;

import java.util.List;

public interface ConfiguracaoProdutoNotaFiscalInterfaceFacade extends SuperInterface {

    public void incluir(ConfiguracaoProdutoEmpresaVO obj) throws Exception;

    public void excluir(ConfiguracaoProdutoEmpresaVO obj) throws Exception;
    
    public void excluirPorProduto(ProdutoVO produto) throws Exception;
    
    public void alterarPorProduto(ProdutoVO produto) throws Exception;
    
    public List<ConfiguracaoProdutoEmpresaVO> consultarPorProduto(Integer produto) throws Exception;
    
    public ConfiguracaoProdutoEmpresaVO consultarPorProdutoEmpresa(Integer produto, Integer empresa) throws Exception;
    
    
}

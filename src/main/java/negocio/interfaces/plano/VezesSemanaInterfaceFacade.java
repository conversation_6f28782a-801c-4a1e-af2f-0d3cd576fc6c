package negocio.interfaces.plano;
import negocio.comuns.plano.VezesSemanaVO;
import java.util.List;
import negocio.interfaces.basico.SuperInterface;

/**
 * Interface reponsável por criar uma estrutura padrão de comunidação entre a camada de controle
 * e camada de negócio (em especial com a classe Façade). Com a utilização desta interface 
 * é possível substituir tecnologias de uma camada da aplicação com mínimo de impacto nas demais.
 * Além de padronizar as funcionalidades que devem ser disponibilizadas pela camada de negócio, por intermédio
 * de sua classe Façade (responsável por persistir os dados das classes VO).
*/
public interface VezesSemanaInterfaceFacade extends SuperInterface {
	

    public VezesSemanaVO novo() throws Exception;
    public void incluir(VezesSemanaVO obj) throws Exception;
    public void alterar(VezesSemanaVO obj) throws Exception;
    public void excluir(VezesSemanaVO obj) throws Exception;
    public VezesSemanaVO consultarPorChavePrimaria(Integer codigo, int nivelMontarDados) throws Exception;
    public List consultarPorCodigo(Integer valorConsulta, boolean controlarAcesso, int nivelMontarDados) throws Exception;
    public List consultarPorNrVezes(Integer valorConsulta, boolean controlarAcesso, int nivelMontarDados) throws Exception;
    public List consultarPorPercentualDesconto(Double valorConsulta, boolean controlarAcesso, int nivelMontarDados) throws Exception;
    public List consultarPorValorEspecifico(Double valorConsulta, boolean controlarAcesso, int nivelMontarDados) throws Exception;
    public void setIdEntidade(String aIdEntidade);
}
package negocio.interfaces.plano;

import java.util.List;

import negocio.comuns.plano.ConfiguracaoProdutoEmpresaPlanoVO;
import negocio.comuns.plano.ProdutoVO;
import negocio.interfaces.basico.SuperInterface;

public interface ConfiguracaoProdutoEmpresaPlanoInterfaceFacade extends SuperInterface {

    void incluir(ConfiguracaoProdutoEmpresaPlanoVO obj) throws Exception;

    void excluir(ConfiguracaoProdutoEmpresaPlanoVO obj) throws Exception;

    void excluirPorProduto(ProdutoVO produto) throws Exception;

    List<ConfiguracaoProdutoEmpresaPlanoVO> consultarPorProduto(Integer produto) throws Exception;

    ConfiguracaoProdutoEmpresaPlanoVO consultarPorProdutoEmpresaPlano(Integer produto, Integer empresa, Integer plano) throws Exception;
}

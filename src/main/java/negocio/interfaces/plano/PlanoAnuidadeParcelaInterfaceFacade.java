package negocio.interfaces.plano;

import negocio.comuns.plano.PlanoAnuidadeParcelaVO;
import negocio.comuns.plano.PlanoRecorrenciaVO;
import negocio.interfaces.basico.SuperInterface;

import java.util.List;

public interface PlanoAnuidadeParcelaInterfaceFacade extends SuperInterface {

    void incluirAlterar(PlanoRecorrenciaVO planoRecorrenciaVO) throws Exception;

    List<PlanoAnuidadeParcelaVO> consultarPorPlanoRecorrencia(Integer planoRecorrencia) throws Exception;

    List<PlanoAnuidadeParcelaVO> consultarPorPlano(Integer plano) throws Exception;

    PlanoAnuidadeParcelaVO consultarPorPlanoNumero(Integer plano, Integer numero) throws Exception;
}

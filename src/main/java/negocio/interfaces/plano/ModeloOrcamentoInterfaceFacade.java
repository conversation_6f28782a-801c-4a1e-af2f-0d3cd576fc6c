package negocio.interfaces.plano;

import negocio.comuns.plano.ModeloOrcamentoVO;
import negocio.interfaces.basico.SuperInterface;

import java.io.IOException;
import java.util.List;

public interface ModeloOrcamentoInterfaceFacade extends SuperInterface {

    public String consultarJSON() throws Exception;

    public ModeloOrcamentoVO consultarPorChavePrimaria(Integer codigo, int nivelMontarDados) throws Exception;

    public List<ModeloOrcamentoVO> consultarTodosAtivos() throws Exception;

    public void incluir(ModeloOrcamentoVO modeloOrcamento) throws Exception;

    public void alterar(ModeloOrcamentoVO modeloOrcamento) throws Exception;

    public void excluir(ModeloOrcamentoVO modeloOrcamento) throws Exception;

    public List consultarParaImpressao(String filtro, String ordem, String campoOrdenacao, int i) throws Exception;
}

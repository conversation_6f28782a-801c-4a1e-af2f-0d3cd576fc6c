/*
 * To change this template, choose Too<PERSON> | Templates
 * and open the template in the editor.
 */

package negocio.interfaces.plano;

import java.util.List;
import negocio.comuns.plano.HorarioDisponibilidadeVO;
import negocio.interfaces.basico.SuperInterface;

/**
 *
 * <AUTHOR>
 */
public interface HorarioDisponibilidadeInterfaceFacade extends SuperInterface {

    /**
     * Operação responsável por alterar no BD os dados de um objeto da classe <code>HorarioDisponibilidadeVO</code>.
     * Sempre utiliza a chave primária da classe como atributo para localização do registro a ser alterado.
     * Primeiramente valida os dados (<code>validarDados</code>) do objeto. Verifica a conexão com o banco de dados e a permissão do usuário
     * para realizar esta operacão na entidade.
     * Isto, através da operação <code>alterar</code> da superclasse.
     * @param obj    Objeto da classe <code>HorarioDisponibilidadeVO</code> que será alterada no banco de dados.
     * @exception Execption Caso haja problemas de conexão, restrição de acesso ou validação de dados.
     */
    void alterar(HorarioDisponibilidadeVO obj) throws Exception;

    /**
     * Operação responsável por alterar todos os objetos da <code>HorarioDisponibilidadeVO</code> contidos em um Hashtable no BD.
     * Faz uso da operação <code>excluirHorarioDisponibilidades</code> e <code>incluirHorarioDisponibilidades</code> disponíveis na classe <code>HorarioDisponibilidade</code>.
     * @param objetos  List com os objetos a serem alterados ou incluídos no BD.
     * @exception Exception  Erro de conexão com o BD ou restrição de acesso a esta operação.
     */
    void alterarHorarioDisponibilidades(Integer horario, List objetos) throws Exception;

    /**
     * Operação responsável por consultar todos os <code>HorarioDisponibilidadeVO</code> relacionados a um objeto da classe <code>plano.Horario</code>.
     * @param horario  Atributo de <code>plano.Horario</code> a ser utilizado para localizar os objetos da classe <code>HorarioDisponibilidadeVO</code>.
     * @return List  Contendo todos os objetos da classe <code>HorarioDisponibilidadeVO</code> resultantes da consulta.
     * @exception Exception  Erro de conexão com o BD ou restrição de acesso a esta operação.
     */
    List consultarHorarioDisponibilidades(Integer horario, int nivelMontarDados) throws Exception;

    /**
     * Operação responsável por localizar um objeto da classe <code>HorarioDisponibilidadeVO</code>
     * através de sua chave primária.
     * @exception Exception Caso haja problemas de conexão ou localização do objeto procurado.
     */
    HorarioDisponibilidadeVO consultarPorChavePrimaria(Integer codigoPrm, int nivelMontarDados) throws Exception;

    /**
     * Responsável por realizar uma consulta de <code>HorarioDisponibilidade</code> através do valor do atributo
     * <code>Integer codigo</code>. Retorna os objetos com valores iguais ou superiores ao parâmetro fornecido.
     * Faz uso da operação <code>montarDadosConsulta</code> que realiza o trabalho de prerarar o List resultante.
     * @param   controlarAcesso Indica se a aplicação deverá verificar se o usuário possui permissão para esta consulta ou não.
     * @return  List Contendo vários objetos da classe <code>HorarioDisponibilidadeVO</code> resultantes da consulta.
     * @exception Exception Caso haja problemas de conexão ou restrição de acesso.
     */
    List consultarPorCodigo(Integer valorConsulta, boolean controlarAcesso, int nivelMontarDados) throws Exception;

    /**
     * Responsável por realizar uma consulta de <code>HorarioDisponibilidade</code> através do valor do atributo
     * <code>descricao</code> da classe <code>Horario</code>
     * Faz uso da operação <code>montarDadosConsulta</code> que realiza o trabalho de prerarar o List resultante.
     * @return  List Contendo vários objetos da classe <code>HorarioDisponibilidadeVO</code> resultantes da consulta.
     * @exception Execption Caso haja problemas de conexão ou restrição de acesso.
     */
    List consultarPorDescricaoHorario(String valorConsulta, int nivelMontarDados) throws Exception;

    /**
     * Responsável por realizar uma consulta de <code>HorarioDisponibilidade</code> através do valor do atributo
     * <code>String identificador</code>. Retorna os objetos, com início do valor do atributo idêntico ao parâmetro fornecido.
     * Faz uso da operação <code>montarDadosConsulta</code> que realiza o trabalho de prerarar o List resultante.
     * @param   controlarAcesso Indica se a aplicação deverá verificar se o usuário possui permissão para esta consulta ou não.
     * @return  List Contendo vários objetos da classe <code>HorarioDisponibilidadeVO</code> resultantes da consulta.
     * @exception Exception Caso haja problemas de conexão ou restrição de acesso.
     */
    List consultarPorIdentificador(String valorConsulta, boolean controlarAcesso, int nivelMontarDados) throws Exception;

    /**
     * Operação responsável por excluir no BD um objeto da classe <code>HorarioDisponibilidadeVO</code>.
     * Sempre localiza o registro a ser excluído através da chave primária da entidade.
     * Primeiramente verifica a conexão com o banco de dados e a permissão do usuário
     * para realizar esta operacão na entidade.
     * Isto, através da operação <code>excluir</code> da superclasse.
     * @param obj    Objeto da classe <code>HorarioDisponibilidadeVO</code> que será removido no banco de dados.
     * @exception Execption Caso haja problemas de conexão ou restrição de acesso.
     */
    void excluir(HorarioDisponibilidadeVO obj) throws Exception;

    /**
     * Operação responsável por excluir todos os objetos da <code>HorarioDisponibilidadeVO</code> no BD.
     * Faz uso da operação <code>excluir</code> disponível na classe <code>HorarioDisponibilidade</code>.
     * @param <code>horario</code> campo chave para exclusão dos objetos no BD.
     * @exception Exception  Erro de conexão com o BD ou restrição de acesso a esta operação.
     */
    void excluirHorarioDisponibilidades(Integer horario) throws Exception;

    /**
     * Operação responsável por incluir no banco de dados um objeto da classe <code>HorarioDisponibilidadeVO</code>.
     * Primeiramente valida os dados (<code>validarDados</code>) do objeto. Verifica a conexão com o banco de dados e a permissão do usuário
     * para realizar esta operacão na entidade.
     * Isto, através da operação <code>incluir</code> da superclasse.
     * @param obj  Objeto da classe <code>HorarioDisponibilidadeVO</code> que será gravado no banco de dados.
     * @exception Exception Caso haja problemas de conexão, restrição de acesso ou validação de dados.
     */
    void incluir(HorarioDisponibilidadeVO obj) throws Exception;

    /**
     * Operação responsável por incluir objetos da <code>HorarioDisponibilidadeVO</code> no BD.
     * Garantindo o relacionamento com a entidade principal <code>plano.Horario</code> através do atributo de vínculo.
     * @param objetos List contendo os objetos a serem gravados no BD da classe.
     * @exception Exception  Erro de conexão com o BD ou restrição de acesso a esta operação.
     */
    void incluirHorarioDisponibilidades(Integer horarioPrm, List objetos) throws Exception;

    /**
     * Operação responsável por retornar um novo objeto da classe <code>HorarioDisponibilidadeVO</code>.
     */
    HorarioDisponibilidadeVO novo() throws Exception;

}

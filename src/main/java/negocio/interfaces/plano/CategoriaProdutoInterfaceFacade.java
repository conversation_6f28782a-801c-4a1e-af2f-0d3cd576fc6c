package negocio.interfaces.plano;

import negocio.comuns.financeiro.FormaPagamentoVO;
import negocio.comuns.plano.CategoriaProdutoVO;
import negocio.interfaces.basico.SuperInterface;

import java.sql.Connection;
import java.sql.SQLException;
import java.util.List;
import java.util.Map;

/**
 * Interface reponsável por criar uma estrutura padrão de comunidação entre a camada de controle
 * e camada de negócio (em especial com a classe Façade). Com a utilização desta interface
 * é possível substituir tecnologias de uma camada da aplicação com mínimo de impacto nas demais.
 * Além de padronizar as funcionalidades que devem ser disponibilizadas pela camada de negócio, por intermédio
 * de sua classe Façade (responsável por persistir os dados das classes VO).
 */
public interface CategoriaProdutoInterfaceFacade extends SuperInterface {

    public CategoriaProdutoVO novo() throws Exception;

    public void incluir(CategoriaProdutoVO obj) throws Exception;

    public void alterar(CategoriaProdutoVO obj) throws Exception;

    public void alterarSemCommit(CategoriaProdutoVO obj) throws Exception;

    public void excluir(CategoriaProdutoVO obj) throws Exception;

    public CategoriaProdutoVO consultarPorChavePrimaria(Integer codigo, int nivelMontarDados) throws Exception;

    public List consultarPorCodigo(Integer valorConsulta, boolean controlarAcesso, int nivelMontarDados) throws Exception;

    public List consultarPorDescricao(String valorConsulta, boolean controlarAcesso, int nivelMontarDados) throws Exception;

    public CategoriaProdutoVO consultarPorCodigoProduto(Integer valorConsulta) throws Exception;

    public void setIdEntidade(String aIdEntidade);
    public CategoriaProdutoVO consultarPorDescricao(String descricao, int nivelMontarDados)throws Exception;

    public void excluir(CategoriaProdutoVO obj, boolean centralEventos) throws Exception;

    public void incluir(CategoriaProdutoVO obj, boolean centralEventos) throws Exception;

    public void alterar(CategoriaProdutoVO obj, boolean centralEventos) throws Exception;

    public Map<Integer, String> consultarCategoriaSimplificado() throws Exception;

    public int consultarPorDescricao(String descricao) throws Exception;

    public List consultarCategoriaComControleEstoque(Integer codigoEmpresa, String descricaoCategoria, boolean somenteProdutoEstoqueAtivo, int nivelMontarDados) throws Exception;

    public String consultarJSON() throws Exception;

    public List consultarParaImpressao(String filtro, String ordem, String campoOrdenacao, int i) throws Exception;

    CategoriaProdutoVO obterCategoriaPadraoServico() throws Exception;

    List<CategoriaProdutoVO> consultarCategoriasParaVendasOnline() throws Exception;

    FormaPagamentoVO consultarFormaPagamentoCategoriaProduto(int movParcela) throws SQLException;
}

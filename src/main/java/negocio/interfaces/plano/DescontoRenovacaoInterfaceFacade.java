
package negocio.interfaces.plano;

import java.util.List;
import negocio.comuns.plano.DescontoRenovacaoVO;
import negocio.interfaces.basico.SuperInterface;

/**
 *
 * <AUTHOR>
 */
public interface DescontoRenovacaoInterfaceFacade extends SuperInterface {
    public void incluir(DescontoRenovacaoVO obj) throws Exception;
    public void incluirSemCommit(DescontoRenovacaoVO obj) throws Exception;
    public void alterar(DescontoRenovacaoVO obj) throws Exception;
    public void alterarSemCommit(DescontoRenovacaoVO obj) throws Exception;
    public void excluir(int desconto) throws Exception;
    public void excluirSemCommit(int desconto) throws Exception;

    public List<DescontoRenovacaoVO> consultarPorCodigo(int codigo, boolean controlarAcesso, int nivelMontarDados) throws Exception;
    public DescontoRenovacaoVO consultarPorChavePrimaria(int codigo, int nivelMontarDados) throws Exception;
    public List<DescontoRenovacaoVO> consultarPorDesconto(int desconto, boolean controlarAcesso, int nivelMontarDados) throws Exception;
}

package negocio.interfaces.plano;

import negocio.comuns.plano.CondicaoPagamentoVO;
import negocio.comuns.utilitarias.CondicaoPagamentoPlanoTO;
import negocio.interfaces.basico.SuperInterface;

import java.util.List;

/**
 * Interface reponsável por criar uma estrutura padrão de comunidação entre a camada de controle
 * e camada de negócio (em especial com a classe Façade). Com a utilização desta interface
 * é possível substituir tecnologias de uma camada da aplicação com mínimo de impacto nas demais.
 * Além de padronizar as funcionalidades que devem ser disponibilizadas pela camada de negócio, por intermédio
 * de sua classe Façade (responsável por persistir os dados das classes VO).
 */
public interface CondicaoPagamentoInterfaceFacade extends SuperInterface {

    public CondicaoPagamentoVO novo() throws Exception;

    public void incluir(CondicaoPagamentoVO obj) throws Exception;

    public void alterar(CondicaoPagamentoVO obj) throws Exception;

    public void alterarSemCommit(CondicaoPagamentoVO obj) throws Exception;

    public void excluir(CondicaoPagamentoVO obj) throws Exception;

    public CondicaoPagamentoVO consultarPorChavePrimaria(Integer codigo, int nivelMontarDados) throws Exception;

    public List consultarPorCodigo(Integer valorConsulta, boolean controlarAcesso, int nivelMontarDados) throws Exception;

    public List consultarPorCondicaoPagamentoDefault(Boolean valorConsulta, boolean controlarAcesso, int nivelMontarDados) throws Exception;

    public List consultarPorDescricao(String valorConsulta, boolean controlarAcesso, int nivelMontarDados) throws Exception;

    public CondicaoPagamentoVO consultarPorDescricao(String descricao, int nivelMontarDados)throws Exception;

    public List consultarPorNrParcelas(Integer valorConsulta, boolean controlarAcesso, int nivelMontarDados) throws Exception;
    
    public List consultarPorNrParcelasMenorOuIgual(Integer valorConsulta, boolean controlarAcesso, int nivelMontarDados) throws Exception;

    public List consultarPorPercentualValorEntrada(Double valorConsulta, boolean controlarAcesso, int nivelMontarDados) throws Exception;

    public void setIdEntidade(String aIdEntidade);

    public void incluirSemCommit(CondicaoPagamentoVO obj) throws Exception;

    public CondicaoPagamentoVO criarOuConsultarSeExistePorNome(int nrParcelas) throws Exception;

    public String consultarJSON() throws Exception;

    public List consultarParaImpressao(String filtro, String ordem, String campoOrdenacao, int i) throws Exception;

    public List<CondicaoPagamentoPlanoTO> consultarCondicaoPagamentoPlano(Integer codigoCondicaoPagamento, Integer codigoEmpresa)throws Exception;

}

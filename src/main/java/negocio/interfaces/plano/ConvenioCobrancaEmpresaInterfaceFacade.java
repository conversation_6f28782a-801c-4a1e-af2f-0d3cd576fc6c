package negocio.interfaces.plano;

import negocio.comuns.financeiro.ConvenioCobrancaVO;
import negocio.comuns.plano.ConvenioCobrancaEmpresaVO;
import negocio.interfaces.basico.SuperInterface;

import java.util.List;

public interface ConvenioCobrancaEmpresaInterfaceFacade extends SuperInterface {

    void incluir(ConvenioCobrancaEmpresaVO obj) throws Exception;

    void excluir(ConvenioCobrancaEmpresaVO obj) throws Exception;

    List<ConvenioCobrancaEmpresaVO> consultarPorConvenioCobranca(Integer convenioCobranca) throws Exception;

    ConvenioCobrancaEmpresaVO consultarPorConvenioCobrancaEmpresa(Integer convenioCobranca, Integer empresa) throws Exception;

    void excluirPorConvenioCobranca(ConvenioCobrancaVO convenioCobrancaVO) throws Exception;

    void alterarPorConvenioCobranca(ConvenioCobrancaVO convenioCobrancaVO) throws Exception;

    String obterEmpresasConvenio(Integer convenio);

    List<Integer> obterEmpresasConvenio(List<Integer> convenios);

    void incluirVinculoSeNaoExistir(Integer convenioCobranca, Integer empresa) throws Exception;

}

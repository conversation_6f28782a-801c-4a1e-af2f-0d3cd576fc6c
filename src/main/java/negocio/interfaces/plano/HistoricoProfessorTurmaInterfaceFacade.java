package negocio.interfaces.plano;

import negocio.comuns.plano.HistoricoProfessorTurmaVO;
import negocio.interfaces.basico.SuperInterface;

import java.util.List;

public interface HistoricoProfessorTurmaInterfaceFacade extends SuperInterface {

        public void incluir(HistoricoProfessorTurmaVO obj) throws Exception;

        public void alterar(HistoricoProfessorTurmaVO obj) throws Exception;

        public void excluir(HistoricoProfessorTurmaVO obj) throws Exception;

        public List<HistoricoProfessorTurmaVO> consultarTodos(int horarioTurma) throws Exception;

        public HistoricoProfessorTurmaVO consultarUltimo(int horarioTurma) throws Exception;
}

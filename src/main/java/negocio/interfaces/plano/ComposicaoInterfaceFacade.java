package negocio.interfaces.plano;

import negocio.comuns.plano.ComposicaoVO;
import negocio.interfaces.basico.SuperInterface;

import java.util.List;

/**
 * Interface reponsável por criar uma estrutura padrão de comunidação entre a camada de controle
 * e camada de negócio (em especial com a classe Façade). Com a utilização desta interface
 * é possível substituir tecnologias de uma camada da aplicação com mínimo de impacto nas demais.
 * Além de padronizar as funcionalidades que devem ser disponibilizadas pela camada de negócio, por intermédio
 * de sua classe Façade (responsável por persistir os dados das classes VO).
 */
public interface ComposicaoInterfaceFacade extends SuperInterface {


    public ComposicaoVO novo() throws Exception;

    public void incluir(ComposicaoVO obj) throws Exception;

    public void incluirSemCommit(ComposicaoVO obj, Boolean salvarComposicaoModalidade) throws Exception;

    public void incluirSemCommit(ComposicaoVO obj) throws Exception;

    public void alterar(ComposicaoVO obj) throws Exception;

    public void excluir(ComposicaoVO obj) throws Exception;

    public ComposicaoVO consultarPorChavePrimaria(Integer codigo, int nivelMontarDados) throws Exception;

    public List<ComposicaoVO> consultarTodos(int empresa, int nivelMontarDados) throws Exception;

    public List consultarPorCodigo(Integer valorConsulta, Integer empresa, boolean controlarAcesso, int nivelMontarDados) throws Exception;

    public ComposicaoVO consultarPorCodigo(Integer codigoPrm, Integer empresa, int nivelMontarDados) throws Exception;

    public List consultarPorDescricao(String valorConsulta, Integer empresa, boolean controlarAcesso, int nivelMontarDados) throws Exception;

    public List consultarPorComposicaoDefault(Boolean valorConsulta, Integer empresa, boolean controlarAcesso, int nivelMontarDados) throws Exception;

    public List consultarComposicaoPorModalidadeEmpresa(Integer modalidade, Integer empresa, boolean controlarAcesso, int nivelMontarDados) throws Exception;

    public void setIdEntidade(String aIdEntidade);

    public String consultarJSON(Integer empresa) throws Exception;

    public List consultarParaImpressao(String filtro, String ordem, String campoOrdenacao, Integer empresa) throws Exception;

}

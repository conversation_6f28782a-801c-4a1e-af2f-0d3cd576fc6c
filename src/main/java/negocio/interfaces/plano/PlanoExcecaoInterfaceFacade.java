/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package negocio.interfaces.plano;

import java.sql.Connection;
import java.sql.SQLException;
import java.util.List;

import negocio.comuns.plano.PlanoExcecaoVO;
import negocio.comuns.plano.PlanoVO;
import negocio.interfaces.basico.SuperInterface;

/**
 * <AUTHOR>
 */
public interface PlanoExcecaoInterfaceFacade extends SuperInterface {

    public PlanoExcecaoVO novo() throws Exception;

    public void incluir(PlanoExcecaoVO obj) throws Exception;

    public void alterar(PlanoExcecaoVO obj) throws Exception;

    public void excluir(PlanoExcecaoVO obj) throws Exception;

    public PlanoExcecaoVO consultarPorChavePrimaria(Integer codigo, int nivelMontarDados) throws Exception;

    public void setIdEntidade(String aIdEntidade);

    public List<PlanoExcecaoVO> consultar(String sql, final int nivelMontarDados, Connection con) throws Exception;

    public List<PlanoExcecaoVO> consultarPorPlano(final Integer plano) throws Exception;

    public void incluirOuAlterarPlanoExcecoes(PlanoVO plano) throws Exception;

}

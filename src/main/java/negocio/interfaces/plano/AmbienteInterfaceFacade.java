package negocio.interfaces.plano;

import negocio.comuns.plano.AmbienteVO;
import negocio.interfaces.basico.SuperInterface;

import java.util.List;
import java.util.Map;
import negocio.comuns.crm.GenericoTO;

/**
 * Interface reponsável por criar uma estrutura padrão de comunidação entre a camada de controle
 * e camada de negócio (em especial com a classe Façade). Com a utilização desta interface
 * é possível substituir tecnologias de uma camada da aplicação com mínimo de impacto nas demais.
 * Além de padronizar as funcionalidades que devem ser disponibilizadas pela camada de negócio, por intermédio
 * de sua classe Façade (responsável por persistir os dados das classes VO).
 */
public interface AmbienteInterfaceFacade extends SuperInterface {

    public AmbienteVO novo() throws Exception;

    public void incluir(AmbienteVO obj) throws Exception;

    public void alterar(AmbienteVO obj) throws Exception;

    public void excluir(AmbienteVO obj) throws Exception;

    public void excluir(AmbienteVO obj, boolean centralEventos) throws Exception;

    public AmbienteVO consultarPorChavePrimaria(Integer codigo, int nivelMontarDados) throws Exception;

    public List consultarPorCodigo(Integer valorConsulta, boolean controlarAcesso, int nivelMontarDados) throws Exception;

    public List consultarPorDescricao(String valorConsulta, boolean controlarAcesso, int nivelMontarDados) throws Exception;

    public List consultarTodosPorDescricao(String valorConsulta, boolean controlarAcesso, int nivelMontarDados) throws Exception;

    public List consultarPorDescricaoPorAtivoEstudio(String valorConsulta, boolean controlarAcesso, int nivelMontarDados) throws Exception;

    public void setIdEntidade(String aIdEntidade);

    public void incluirCE(AmbienteVO obj) throws Exception;

    public void alterarCE(AmbienteVO obj) throws Exception;

    public AmbienteVO obterAmbiente(Integer codigoAmbiente) throws Exception;

    public int consultarCodigoPorDescricao(String valorConsulta) throws Exception;

    public Map<Integer, String> consultarAmbientesCESimplificado() throws Exception;

    public List<AmbienteVO> consultarPorTurma(Integer valorConsulta, boolean controlarAcesso, int nivelMontarDados) throws Exception;

    public String consultarJSON(Integer empresa) throws Exception;

    public List consultarParaImpressao(String filtro, String ordem, String campoOrdenacao, int i) throws Exception;
    
    public List<AmbienteVO> consultarAmbientesSimplificado() throws Exception;

    boolean existeAmbienteComTerminal(String terminal) throws Exception;

}

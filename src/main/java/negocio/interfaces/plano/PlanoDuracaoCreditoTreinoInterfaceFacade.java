package negocio.interfaces.plano;

import negocio.comuns.contrato.ContratoDuracaoCreditoTreinoVO;
import negocio.comuns.plano.PlanoDuracaoCreditoTreinoVO;
import negocio.comuns.plano.PlanoDuracaoVO;
import negocio.interfaces.basico.SuperInterface;

import java.util.List;

/**
 * Created by ulisses on 10/11/2015.
 */
public interface PlanoDuracaoCreditoTreinoInterfaceFacade extends SuperInterface {

    void incluir(PlanoDuracaoCreditoTreinoVO planoDuracaoCreditoTreinoVO)throws Exception;
    void incluir(List<PlanoDuracaoCreditoTreinoVO> listaPlanoDuracaoCreditoTreinoVO)throws Exception;
    void excluir(PlanoDuracaoVO planoDuracaoVO)throws Exception;
    List<PlanoDuracaoCreditoTreinoVO> consultar(Integer codigoPlanoDuracao, Integer codigoTipoHorario, int nivelMontarDados)throws Exception;
    PlanoDuracaoCreditoTreinoVO consultar(Integer codigoPlanoDuracao, Integer codigoTipoHorario, Integer numeroVezesSemana, int nivelMontarDados)throws Exception;

    PlanoDuracaoCreditoTreinoVO consultarPorPlanoDuracao(Integer codigoPlanoDuracao, int nivelMontarDados)throws Exception;
    PlanoDuracaoCreditoTreinoVO consultarPorCodigo(Integer codigo, int nivelMontarDados)throws Exception;
    void consultarContratoDuracaoCreditoTreinoBaseCalculo(ContratoDuracaoCreditoTreinoVO contratoDuracaoCreditoTreinoVO, Integer codigoPlano) throws  Exception;
    void incluirSessao(PlanoDuracaoVO planoDuracaoVO) throws Exception;
    void alterarSessao(PlanoDuracaoVO planoDuracaoVO) throws Exception;
}

/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package negocio.interfaces.plano;

import java.util.List;

import negocio.comuns.financeiro.ConvenioCobrancaVO;
import negocio.comuns.plano.ConfiguracaoProdutoEmpresaVO;
import negocio.comuns.plano.ProdutoVO;
import negocio.interfaces.basico.SuperInterface;

/**
 *
 * <AUTHOR>
 */
public interface ConfiguracaoProdutoEmpresaInterfaceFacade extends SuperInterface {

    public void incluir(ConfiguracaoProdutoEmpresaVO obj) throws Exception;

    public void excluir(ConfiguracaoProdutoEmpresaVO obj) throws Exception;
    
    public void excluirPorProduto(ProdutoVO produto) throws Exception;
    
    public void alterarPorProduto(ProdutoVO produto) throws Exception;
    
    public List<ConfiguracaoProdutoEmpresaVO> consultarPorProduto(Integer produto) throws Exception;
    
    public ConfiguracaoProdutoEmpresaVO consultarPorProdutoEmpresa(Integer produto, Integer empresa) throws Exception;
    
    
}

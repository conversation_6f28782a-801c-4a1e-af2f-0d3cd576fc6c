package negocio.interfaces.plano;

import negocio.comuns.plano.PlanoModalidadeVezesSemanaVO;
import java.util.List;
import negocio.interfaces.basico.SuperInterface;

/**
 * Interface reponsável por criar uma estrutura padrão de comunidação entre a camada de controle
 * e camada de negócio (em especial com a classe Façade). Com a utilização desta interface 
 * é possível substituir tecnologias de uma camada da aplicação com mínimo de impacto nas demais.
 * Além de padronizar as funcionalidades que devem ser disponibilizadas pela camada de negócio, por intermédio
 * de sua classe Façade (responsável por persistir os dados das classes VO).
 */
public interface PlanoModalidadeVezesSemanaInterfaceFacade extends SuperInterface {

    public PlanoModalidadeVezesSemanaVO novo() throws Exception;

    public void incluir(PlanoModalidadeVezesSemanaVO obj) throws Exception;

    public void alterar(PlanoModalidadeVezesSemanaVO obj) throws Exception;

    public void excluir(PlanoModalidadeVezesSemanaVO obj) throws Exception;

    public PlanoModalidadeVezesSemanaVO consultarPorChavePrimaria(Integer codigo, int nivelMontarDados) throws Exception;

    public List consultarPorCodigo(Integer valorConsulta, boolean controlarAcesso, int nivelMontarDados) throws Exception;

    public List consultarPorNrVezesVezesSemana(Integer valorConsulta, int nivelMontarDados) throws Exception;

    public List consultarPorPlanoModalidade(Integer valorConsulta, boolean controlarAcesso, int nivelMontarDados) throws Exception;

    public void setIdEntidade(String aIdEntidade);

    /**
     * Operação responsável por excluir todos os objetos da <code>HorarioTurmaVO</code> no BD.
     * Faz uso da operação <code>excluir</code> disponível na classe <code>HorarioTurma</code>.
     * @param <code>turma</code> campo chave para exclusão dos objetos no BD.
     * @exception Exception  Erro de conexão com o BD ou restrição de acesso a esta operação.
     */
    public void excluirPlanoModalidadeVezesSemana(Integer planoModalidadePrm) throws Exception;

    /**
     * Operação responsável por incluir objetos da <code>HorarioTurmaVO</code> no BD.
     * Garantindo o relacionamento com a entidade principal <code>plano.Turma</code> através do atributo de vínculo.
     * @param objetos List contendo os objetos a serem gravados no BD da classe.
     * @exception Exception  Erro de conexão com o BD ou restrição de acesso a esta operação.
     */
    public void incluirPlanoModalidadeVezesSemana(Integer planoModalidadePrm, List objetos) throws Exception;

    /**
     * Operação responsável por alterar todos os objetos da <code>HorarioTurmaVO</code> contidos em um Hashtable no BD.
     * Faz uso da operação <code>excluirHorarioTurmas</code> e <code>incluirHorarioTurmas</code> disponíveis na classe <code>HorarioTurma</code>.
     * @param objetos  List com os objetos a serem alterados ou incluídos no BD.
     * @exception Exception  Erro de conexão com o BD ou restrição de acesso a esta operação.
     */
    public void alterarPlanoModalidadeVezesSemana(Integer planoModalidade, List objetos) throws Exception;

    /**
     * Operação responsável por consultar todos os <code>PlanoDuracaoVO</code> relacionados a um objeto da classe <code>plano.Plano</code>.
     * @param plano  Atributo de <code>plano.Plano</code> a ser utilizado para localizar os objetos da classe <code>PlanoDuracaoVO</code>.
     * @return List  Contendo todos os objetos da classe <code>PlanoDuracaoVO</code> resultantes da consulta.
     * @exception Exception  Erro de conexão com o BD ou restrição de acesso a esta operação.
     */
    public List consultarPlanoVezesSemanaVOs(Integer planoModalidade, int nivelMontarDados) throws Exception;

    public boolean consultarPorChavePrimaria(Integer codigo) throws Exception;
    public PlanoModalidadeVezesSemanaVO consultarPlanoModalidadePorCodigoModalidade(int modalidade) throws Exception;
    public PlanoModalidadeVezesSemanaVO consultarPlanoModalidadePorCodigoModalidadeECodigoPlano(int modalidade, int plano) throws Exception;

    public List<PlanoModalidadeVezesSemanaVO> consultarPorCodigoPlanoModalidade(int planomodalidade) throws Exception;

    public PlanoModalidadeVezesSemanaVO consultar(Integer codigoPlanoModalidade, Integer nrVezes, int nivelMontarDados) throws Exception;
}

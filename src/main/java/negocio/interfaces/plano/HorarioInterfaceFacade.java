package negocio.interfaces.plano;

import negocio.comuns.plano.HorarioVO;
import negocio.interfaces.basico.SuperInterface;

import java.util.List;

/**
 * Interface reponsável por criar uma estrutura padrão de comunidação entre a camada de controle
 * e camada de negócio (em especial com a classe Façade). Com a utilização desta interface
 * é possível substituir tecnologias de uma camada da aplicação com mínimo de impacto nas demais.
 * Além de padronizar as funcionalidades que devem ser disponibilizadas pela camada de negócio, por intermédio
 * de sua classe Façade (responsável por persistir os dados das classes VO).
 */
public interface HorarioInterfaceFacade extends SuperInterface {


    public HorarioVO novo() throws Exception;

    public void incluir(HorarioVO obj) throws Exception;

    public void alterar(HorarioVO obj) throws Exception;

    public void alterarSemCommit(HorarioVO obj) throws Exception;

    public void excluir(HorarioVO obj) throws Exception;

    public HorarioVO consultarPorChavePrimaria(Integer codigo, int nivelMontarDados) throws Exception;

    public HorarioVO consultarPorChavePrimaria(Integer codigoPrm, int nivelMontarDados, Boolean ativo) throws Exception;

    public List consultarPorCodigo(Integer valorConsulta, boolean controlarAcesso, int nivelMontarDados) throws Exception;

    public List<HorarioVO> consultarPorDescricao(String valorConsulta, boolean controlarAcesso, int nivelMontarDados) throws Exception;

    public List<HorarioVO> consultarPorDescricao(String valorConsulta, boolean controlarAcesso, int nivelMontarDados, Boolean ativo) throws Exception;

    public List consultarPorHorarioDefault(Boolean valorConsulta, boolean controlarAcesso, int nivelMontarDados) throws Exception;

    public List consultarPorPercentualDesconto(Double valorConsulta, boolean controlarAcesso, int nivelMontarDados) throws Exception;

    public List consultarPorValorEspecifico(Double valorConsulta, boolean controlarAcesso, int nivelMontarDados) throws Exception;

    public void setIdEntidade(String aIdEntidade);

    public void incluirSemCommit(HorarioVO obj) throws Exception;

    public HorarioVO consultarPorDescricao(String valorConsulta, int nivelMontarDados) throws Exception;

    public String consultarJSON(String situacao) throws Exception;

    public List consultarParaImpressao(String filtro, String ordem, String campoOrdenacao, int i) throws Exception;

    HorarioVO consultarPorCodigoContrato(Integer codigoContrato) throws Exception;

    List<HorarioVO> consultarTodosHorarios(boolean controlarAcesso, int nivelMontarDados) throws Exception;

    List<String> consultarTodosHorariosPorContrato() throws Exception;
}

package negocio.interfaces.plano;

import br.com.pactosolucoes.enumeradores.TipoTurmaEnum;
import negocio.comuns.plano.ModalidadeVO;
import negocio.interfaces.basico.SuperInterface;

import java.util.List;
import java.util.Map;

/**
 * Interface reponsável por criar uma estrutura padrão de comunidação entre a camada de controle
 * e camada de negócio (em especial com a classe Façade). Com a utilização desta interface 
 * é possível substituir tecnologias de uma camada da aplicação com mínimo de impacto nas demais.
 * Além de padronizar as funcionalidades que devem ser disponibilizadas pela camada de negócio, por intermédio
 * de sua classe Façade (responsável por persistir os dados das classes VO).
 */
public interface ModalidadeInterfaceFacade extends SuperInterface {

    public ModalidadeVO novo() throws Exception;

    public void incluir(ModalidadeVO obj) throws Exception;

    public void alterar(ModalidadeVO obj) throws Exception;

    public void excluir(ModalidadeVO obj) throws Exception;

    public ModalidadeVO consultarPorChavePrimaria(Integer codigo, int nivelMontarDados) throws Exception;

    public ModalidadeVO consultarPorTurma(int turma, int nivelMontarDados) throws Exception;

    public List consultarPorCodigo(Integer valorConsulta, Integer empresa, boolean controlarAcesso, int nivelMontarDados) throws Exception;
    
    public List consultarPorCodigoAtiva(Integer valorConsulta, Integer empresa, boolean controlarAcesso, int nivelMontarDados) throws Exception;

    public List consultarPorCodigoUtilizaTurma(Integer valorConsulta, Integer empresa, boolean controlarAcesso, boolean turma, int nivelMontarDados) throws Exception;

    public List<ModalidadeVO> consultarPorNome(String valorConsulta, Integer empresa, boolean controlarAcesso, int nivelMontarDados) throws Exception;

    public List consultarPorNomeUtilizaTurma(String valorConsulta, Integer empresa, boolean controlarAcesso, boolean turma, Boolean ativo, int nivelMontarDados) throws Exception;

    List consultarPorNomeUtilizaTurmaV2(String valorConsulta, Integer empresa, boolean controlarAcesso, boolean turma, Boolean ativo, int nivelMontarDados) throws Exception;

    public List consultarPorModalidadeDefault(Boolean valorConsulta, Integer empresa, boolean controlarAcesso, int nivelMontarDados) throws Exception;

    public List consultarPorUtilizaTurma(Boolean valorConsulta, boolean controlarAcesso, int nivelMontarDados) throws Exception;

    public void setIdEntidade(String aIdEntidade);

    public List consultarPorNomeModalidadeComLimite(String valorConsulta, boolean controlarAcesso, int nivelMontarDados) throws Exception;

    public ModalidadeVO consultarPorNomeModalidade(String valorConsulta, boolean controlarAcesso, int nivelMontarDados) throws Exception;

    public List<ModalidadeVO> consultarPorNomeModalidadeSemDesativado(String valorConsulta, Integer empresa, boolean controlarAcesso, int nivelMontarDados) throws Exception;

    public void incluirSemCommit(ModalidadeVO obj) throws Exception;

    public Map<Integer, String> consultarModalidadesSimplificado() throws Exception;

    public List<ModalidadeVO> consultarTodasModalidadesComLimite(Integer empresa, int limit) throws Exception;

    List<ModalidadeVO> consultarTodasModalidades(Integer empresa, Boolean ativo, Boolean utilizaTurma) throws Exception;

    public List<ModalidadeVO> consultarTodasModalidadesComLimite(Integer empresa, Boolean ativo, Boolean utilizaTurma) throws Exception;

    public List consultarPorNomeModalidadeComLimite(Integer empresa, String valorConsulta) throws Exception;

    public List consultarPorNomeModalidadeProfessorComLimite(String valorConsulta,int professor, boolean controlarAcesso, int nivelMontarDados) throws Exception;

    public String consultarJSON(Integer empresa, String sEcho, Integer offset, Integer limit, String clausulaLike, Integer colOrdenar, String dirOrdenar, String situacao) throws Exception;

    public List consultarParaImpressao(String filtro, String ordem, String campoOrdenacao, Integer empresa,String situacao) throws Exception;
    
    public Map<Integer, String> consultarEmpresasModalidadesSimplificado() throws Exception;

    public List<ModalidadeVO>consultarModalidadeAtivaComTurma(Integer codigoEmpresa, TipoTurmaEnum tipoTurmaEnum, int nivelMontarDados) throws Exception;
    
    public Map<Integer, String> consultarModalidadesSimplificado(Integer empresa) throws Exception;

    public Boolean modalidadeTemContratoVendido(Integer codigoModalidade) throws Exception;

    public List<ModalidadeVO> consultarSimples(Integer empresa) throws Exception;

    public void atualizarAlunosCrossfit(Integer codigoModalidade, Boolean crossfit, String chave);

    public Boolean consultarPorTipo(Integer tipo) throws Exception;

    List consultarPorCodigoTurmaEOuProfessor(int turmaSelecionada, int professorSelecionado, Integer codigoEmpresa, int nivelmontardadosDadosbasicos) throws Exception;

    /**
     * Consulta o código de todos as modalidades cadatrados no sistema.
     * @return
     * @throws Exception
     */
    public List<Integer> consultarTodosCodigos() throws  Exception;

    /**
     * Atualiza o nome da modalidade.
     * @param codigo
     * @param nome
     * @throws Exception
     */
    void alterarNomeModalidade(Integer codigo, String nome) throws  Exception;

    void excluirImagem(Integer codigo) throws Exception;

    ModalidadeVO consultarPorIdExterno(String idExterno, int nivelMontarDados) throws Exception;
}

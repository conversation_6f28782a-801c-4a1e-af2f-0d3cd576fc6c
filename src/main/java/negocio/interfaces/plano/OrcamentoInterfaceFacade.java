package negocio.interfaces.plano;

import negocio.comuns.plano.OrcamentoVO;
import negocio.interfaces.basico.SuperInterface;

import java.util.List;

public interface OrcamentoInterfaceFacade extends SuperInterface {

    public void incluir(OrcamentoVO orcamentoVO) throws Exception;

    public void alterar(OrcamentoVO orcamentoVO) throws Exception;

    public void excluir(OrcamentoVO orcamentoVO) throws Exception;

    public List<OrcamentoVO> consultarPorCodCliente(Integer codigo, int nivelMontarDados) throws Exception;

    public OrcamentoVO consultarPorChavePrimaria(Integer codigo, int nivelMontarDados) throws Exception;
}

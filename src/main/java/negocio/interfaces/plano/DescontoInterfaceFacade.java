package negocio.interfaces.plano;

import negocio.comuns.plano.DescontoVO;
import negocio.interfaces.basico.SuperInterface;

import java.util.List;

/**
 * Interface reponsável por criar uma estrutura padrão de comunidação entre a camada de controle
 * e camada de negócio (em especial com a classe Façade). Com a utilização desta interface
 * é possível substituir tecnologias de uma camada da aplicação com mínimo de impacto nas demais.
 * Além de padronizar as funcionalidades que devem ser disponibilizadas pela camada de negócio, por intermédio
 * de sua classe Façade (responsável por persistir os dados das classes VO).
 */
public interface DescontoInterfaceFacade extends SuperInterface {


    public DescontoVO novo() throws Exception;

    public void incluir(DescontoVO obj) throws Exception;

    public void alterar(DescontoVO obj) throws Exception;

    public void excluir(DescontoVO obj) throws Exception;

    public DescontoVO consultarPorChavePrimaria(Integer codigo, int nivelMontarDados) throws Exception;

    public List consultarPorCodigo(Integer valorConsulta, boolean controlarAcesso, int nivelMontarDados) throws Exception;

    public List consultarPorDescricao(String valorConsulta, boolean controlarAcesso, int nivelMontarDados) throws Exception;

    public List consultarPorValor(Double valorConsulta, boolean controlarAcesso, int nivelMontarDados) throws Exception;

    public List consultarPorTipoDesconto(String valorConsulta, boolean controlarAcesso, int nivelMontarDados) throws Exception;

    public List<DescontoVO> consultarPorTipoProduto(String valorConsulta, boolean controlarAcesso, int nivelMontarDados) throws Exception;

    public List consultarPorTipoProdutoPorEmpresa(String tipo, boolean controlarAcesso, int empresa) throws Exception;

    public void setIdEntidade(String aIdEntidade);

    public void incluir(DescontoVO obj, boolean centralEventos) throws Exception;

    public void alterar(DescontoVO obj, boolean centralEventos) throws Exception;

    public void alterarSemCommit(DescontoVO obj) throws Exception;

    public void excluir(DescontoVO obj, boolean centralEventos) throws Exception;

    public String consultarJSON() throws Exception;

    public List consultarParaImpressao(String filtro, String ordem, String campoOrdenacao, int i) throws Exception;

}
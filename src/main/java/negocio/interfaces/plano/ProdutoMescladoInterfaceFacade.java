package negocio.interfaces.plano;

import negocio.comuns.plano.ProdutoMescladoVO;
import negocio.comuns.plano.ProdutoVO;
import negocio.interfaces.basico.SuperInterface;

import java.util.List;

/**
 * Interface reponsável por criar uma estrutura padrão de comunidação entre a
 * camada de controle e camada de negócio (em especial com a classe Façade). Com
 * a utilização desta interface é possível substituir tecnologias de uma camada
 * da aplicação com mínimo de impacto nas demais. Além de padronizar as
 * funcionalidades que devem ser disponibilizadas pela camada de negócio, por
 * intermédio de sua classe Façade (responsável por persistir os dados das
 * classes VO).
 */
public interface ProdutoMescladoInterfaceFacade extends SuperInterface {

    public ProdutoMescladoVO novo() throws Exception;

    public void incluir(ProdutoMescladoVO obj) throws Exception;

    public void alterar(ProdutoMescladoVO obj) throws Exception;

    public void excluir(ProdutoMescladoVO obj) throws Exception;

    public ProdutoMescladoVO consultarPorChavePrimaria(Integer codigo, int nivelMontarDados) throws Exception;
    public List consultarPorProdutoDestino(Integer produtoDestino, int nivelMontarDados) throws Exception;

    void incluir (List<ProdutoMescladoVO> produtoMescladoVOS) throws Exception;

    void excluirTodosProdutosPelaDestino(Integer codigo) throws Exception;
}

package negocio.interfaces.arquitetura;

import negocio.comuns.arquitetura.UsuarioEmailVO;
import negocio.interfaces.basico.SuperInterface;

/**
 * Created by <PERSON> on 27/12/2016.
 */
public interface UsuarioEmailInterfaceFacade extends SuperInterface {

    public void validarDados(UsuarioEmailVO obj) throws Exception;

    public void incluir(UsuarioEmailVO obj) throws Exception;

    public void alterar(UsuarioEmailVO obj) throws Exception;

    public void gravar(UsuarioEmailVO obj) throws Exception;

    public void excluirSemCommit(UsuarioEmailVO obj) throws Exception;

    public void excluir(UsuarioEmailVO obj) throws Exception;

    public UsuarioEmailVO consultarPorEmail(String email) throws Exception ;

    public UsuarioEmailVO consultarPorUsuario(Integer usuario) throws Exception;

    public boolean existeEmail(UsuarioEmailVO UsuarioEmail) throws Exception;

    public void enviarEmailUsuarioApp(String key, String nome, String usuario, String senha) throws Exception;

}

package negocio.interfaces.arquitetura;

/**
 * Created by ulisses on 16/06/2016.
 */
public interface CachedManagerInterfaceFacade {
   void gravar(Object obj);
   void gravar(Object obj, int tempoExpirar);
   void gravar(Object obj, int tempoExpirar, String identificador, String key);

   <T> T ler(Class classe);
   <T> T ler(Class classe, String identificador);
   <T> T ler(Class classe, String identificador, String key);

   void remover(Class classe);
   void remover(Class classe, String identificador);
   void remover(Class classe, String identificador, String key);

}

package negocio.interfaces.arquitetura;

import br.com.pactosolucoes.enumeradores.BIEnum;
import br.com.pactosolucoes.enumeradores.TipoColaboradorEnum;
import br.com.pactosolucoes.enumeradores.TipoInfoMigracaoEnum;
import negocio.comuns.arquitetura.UsuarioSincronizacaoVO;
import negocio.comuns.arquitetura.UsuarioTO;
import negocio.comuns.arquitetura.UsuarioVO;
import negocio.comuns.basico.ColaboradorVO;
import negocio.comuns.basico.EmailVO;
import negocio.comuns.basico.PessoaVO;
import negocio.interfaces.basico.SuperInterface;
import relatorio.negocio.comuns.basico.ListaPaginadaTO;

import java.sql.SQLException;
import java.util.Date;
import java.util.List;

/**
 * Interface reponsável por criar uma estrutura padrão de comunidação entre a camada de controle
 * e camada de negócio (em especial com a classe Façade). Com a utilização desta interface
 * é possível substituir tecnologias de uma camada da aplicação com mínimo de impacto nas demais.
 * Além de padronizar as funcionalidades que devem ser disponibilizadas pela camada de negócio, por intermédio
 * de sua classe Façade (responsável por persistir os dados das classes VO).
 */
public interface UsuarioInterfaceFacade extends SuperInterface {

    public UsuarioVO novo() throws Exception;

    public void incluir(UsuarioVO obj) throws Exception;

    public void alterar(UsuarioVO obj) throws Exception;

    public void alterarSemPermissao(UsuarioVO obj) throws Exception;
//    public void alterarComSenha(UsuarioVO obj, UsuarioVO usuarioLogado) throws Exception;

    public void excluir(UsuarioVO obj) throws Exception;

    public UsuarioVO consultarPorChavePrimaria(Integer codigo, int nivelMontarDados) throws Exception;

    public List consultarPorCodigo(Integer valorConsulta, boolean controlarAcesso, int nivelMontarDados) throws Exception;

    public UsuarioVO consultarPorCodigo(Integer codigoPrm, int nivelMontarDados) throws Exception;

    boolean validarSenhaUsuarioLogado(Integer codigo, String senha) throws Exception;

    boolean validarSenhaUsuarioLogado(Integer codigo, String senha, String pin) throws Exception;

    void atualizarPin(Integer codigo, String pin) throws Exception;

    public List consultarPorNome(String valorConsulta, boolean controlarAcesso, int nivelMontarDados) throws Exception;

    List consultarPorNomeIdentico(String valorConsulta, int nivelMontarDados) throws Exception;

    public List consultarPorNomeAtivo(String valorConsulta, boolean controlarAcesso, int nivelMontarDados) throws Exception;

    public List consultarPorUsername(String valorConsulta, boolean controlarAcesso, int nivelMontarDados) throws Exception;

    public List consultarUsuariosSemAcessoPorUmMes() throws Exception;

    public List consultarPorNomePerfilAcesso(String valorConsulta, int nivelMontarDados) throws Exception;

    public UsuarioVO consultarPorCodigoColaborador(Integer codigoPrm, int nivelMontarDados) throws Exception;

    public void setIdEntidade(String aIdEntidade);

    public List consultarPorSituacaoColaborador(String valorConsulta, boolean controlarAcesso, int nivelMontarDados) throws Exception;

    public List<UsuarioVO> consultarUsuarioAberturaMeta(Integer empresa ,boolean controlarAcesso, int nivelMontarDados) throws Exception;

    public void alterarNomeUsuario(UsuarioVO obj) throws Exception;

    public void alterarShowModalInativar(UsuarioVO obj) throws Exception;

    public void alterarShowModalPlanos(UsuarioVO obj) throws Exception;

    public void registrarUltimoLoginAcessoAgora(UsuarioVO obj) throws Exception;

    public Integer consultarPorCodigoPessoaRetornandoCodigoUsuario(Integer codigoPessoa) throws Exception;

    public UsuarioVO consultarPorNomeUsuario(String valorConsulta, boolean controlarAcesso, int nivelMontarDados) throws Exception;

    public UsuarioVO consultarPorCodigoUsuario(Integer valorConsulta, boolean controlarAcesso, int nivelMontarDados) throws Exception;

    public List consultarPorCodigoUsuarioSomenteColaborador(Integer valorConsulta, boolean controlarAcesso, int nivelMontarDados) throws Exception;

    public List consultarPorNomeUsuarioSomenteColaborador(String valorConsulta, boolean controlarAcesso, int nivelMontarDados) throws Exception;

    public List<UsuarioVO> consultarTodosUsuarioComLimite(boolean controlarAcesso, Integer empresa, int nivelMontarDados) throws Exception;

    public List<UsuarioVO> consultarPorNomeUsuarioComLimite(String valorConsulta, Integer empresa, boolean controlarAcesso, int nivelMontarDados) throws Exception;

    public UsuarioVO criarOuConsultarSeExistePorNome(UsuarioVO obj) throws Exception;

    public void incluir(UsuarioVO obj, boolean centralEventos) throws Exception;

    public void alterar(UsuarioVO obj, boolean centralEventos, boolean somenteAlterarSuaPropriaSenh, boolean validarPermissao) throws Exception;


    public void excluir(UsuarioVO obj, boolean centralEventos) throws Exception;

    public void incluirSemCommit(UsuarioVO obj) throws Exception;

    void incluirSemCommit(UsuarioVO obj, boolean incluirColaborador) throws Exception;

    public List consultarTodosSemAdministrador(int nivelMontarDados) throws Exception;
    
    public List consultarTodosAtivosSemAdministrador(int nivelMontarDados) throws Exception;

    public UsuarioVO consultarPorCodigoPessoa(Integer codigoPessoa, int nivelMontarDados) throws Exception;

    public void alterarSenhaUsuario(UsuarioVO obj, boolean somenteAlterarSuaPropriaSenha) throws Exception;
    public void alterarSenhaUsuario(UsuarioVO obj, boolean somenteAlterarSuaPropriaSenha, String senhaCriptografada) throws Exception;

    void alterarUsernameSenha(UsuarioVO obj) throws Exception;

    public List<UsuarioVO> consultarPorAdministrador(int nivelMontarDados) throws Exception;

    public List<UsuarioVO> consultarPorAcessoEmpresa(Integer empresa, int nivelMontarDados) throws Exception;

    public String consultarSenhaPorCodigoUsuario(Integer valorConsulta) throws Exception;

    public boolean consultarPorUsernameEDiferenteDoUsuario(String username, int codigoUsuarioDiferente) throws Exception;

    public boolean consultarPorNomeUsuarioEDiferenteDoUsuario(String nome, int codigoUsuarioDiferente) throws Exception;

    public UsuarioVO consultarPorColaborador(Integer colaborador, int nivelMontarDados) throws Exception;

    public List<UsuarioVO> consultarPorNomeEDiferenteDoUsuarioSomenteColaborador(String nome, int codigoUsuarioDiferente, boolean controlarAcesso, int nivelMontarDados) throws Exception;

    public List<UsuarioVO> consultarNomeComFoto(String nome, List<UsuarioVO> usuariosNaoPesquisar) throws Exception;

    public String consultarJSON(Integer empresa, String situacao) throws Exception;

    public void marcarNaoAparecerMais(int usuario, String dica, boolean naoMostrarMais) throws Exception;

    public List consultarParaImpressao(String filtro, String ordem, String campoOrdenacao, Integer empresa, String situacao) throws Exception;
    
    public Integer obterCodigoPessoaUsuario(int colaborador) throws Exception;
    
    public Boolean consultarUsuarioAtivo(Integer usuarioZw) throws Exception;
    
    public void verificarHorariosAcessosPermitidos(UsuarioTO usuario) throws Exception;

    public List<UsuarioVO> consultarTodosUsuariosColaboradorPorEmpresa(int empresa,int nivelMontaDados) throws Exception;

    public List<UsuarioVO> consultarUsuariosComMeta(Integer empresa, Date diaMeta, boolean controlarAcesso, int nivelMontarDados) throws Exception;
    public UsuarioVO consultarColaboradorResponsavelPeloCliente(Integer codigoCliente, int nivelMontarDados) throws Exception;
    public UsuarioVO getUsuarioRecorrencia() throws Exception;

    List<UsuarioVO> consultarUsuarioDataNascimentoCPF(String dataNascimento, String cpf) throws Exception;

    StringBuilder gerarCorpoEmail(UsuarioVO usuarioVO, String mensagemSuperior, String mensagemInferior, String labelBotao, String urlBotao, boolean mostrarSenha) throws Exception;

    public UsuarioVO consultarPorColaboradorEmpresa(Integer colaborador,Integer empresa, int nivelMontarDados) throws Exception;

    /**
     * Consulta todos os {@link UsuarioVO} que são {@link negocio.comuns.basico.ColaboradorVO} ativos de uma determinada empresa e podem abrir meta de agendamento.
     * @param empresa {@link negocio.comuns.basico.EmpresaVO} para qual os colaboradores devem pertencer.
     * @param tipoColaborador Caso seja passado, o {@link UsuarioVO} deve possuir um {@link negocio.comuns.basico.ColaboradorVO} que possua um {@link TipoColaboradorEnum} definido.
     * @param nivelMontaDados Define o nivel de montagem dos dados do {@link UsuarioVO}
     * @return {@lint List} de {@link UsuarioVO} que satisfazem a consulta.
     * @throws Exception
     */
    public List<UsuarioVO> consultarTodosUsuariosColaboradorAtivosPorEmpresaEPassivelAbrirMetaAgendamento(Integer empresa, TipoColaboradorEnum tipoColaborador, Integer nivelMontaDados) throws Exception;

    public UsuarioVO consultarPorUsername(String username, int nivelMontarDados) throws Exception;

    public List<UsuarioVO> consultarListaUsuariosCRM(Integer empresa, boolean somenteAtivo, boolean somenteCodigos) throws Exception;

    /**
     * @param emailDestinatario a ser encontrado em um ou mais {@link UsuarioVO}s.
     *
     * @return lista de usuários que possui o <code>emailDestinatario</code> cadastrado em sua {@link PessoaVO}, na entidade {@link EmailVO}.
     */
    List<UsuarioVO> listarPorEmail(String emailDestinatario) throws Exception;

    boolean consultarSituacao(Integer codigoUsuario, Integer codigoEmpresa) throws SQLException;

    String enviarEmailSenha(String key, UsuarioVO usuarioVO);

    List<UsuarioVO> obterUsuariosPacto(int nivelMontarDados) throws Exception;

    void gravarHistoricoAcessoBI(Integer usuario, Integer empresa, BIEnum biEnum) throws SQLException;

    public String consultarEmailUsuarioEmail(Integer codigoUsuario) throws Exception;

    void adicionarUsuarioServicoDescobrir(String ctx, UsuarioVO usuarioVO);

    List<UsuarioSincronizacaoVO> consultarLogSincronizacao(Integer usuario, Integer limit) throws Exception;

    String consultarUsuarioGeral(UsuarioVO obj) throws SQLException;

    Boolean recursoHabilitado(TipoInfoMigracaoEnum recurso, Integer usuario, Integer empresa) throws Exception;

    Boolean recursoPadraoEmpresa(TipoInfoMigracaoEnum recurso, Integer empresa) throws Exception;

    String origemRecursoHabilitado(TipoInfoMigracaoEnum recurso, Integer usuario) throws Exception;

    void gravarRecurso(TipoInfoMigracaoEnum recurso, Integer usuario, String valor, UsuarioVO usuarioVO) throws Exception;

    void processarRecursoPadraoNovoUsuario(boolean controlarTransacao, boolean exception, UsuarioVO usuarioVO) throws Exception;
}

package negocio.interfaces.arquitetura;

import negocio.comuns.arquitetura.PerfilAcessoVO;
import negocio.comuns.arquitetura.UsuarioVO;
import negocio.comuns.basico.AvisoInternoDTO;
import negocio.comuns.feed.PerfilUsuarioEnum;
import negocio.interfaces.basico.SuperInterface;

import java.util.Date;
import java.util.List;

/**
 * Interface reponsável por criar uma estrutura padrão de comunidação entre a camada de controle
 * e camada de negócio (em especial com a classe Façade). Com a utilização desta interface
 * é possível substituir tecnologias de uma camada da aplicação com mínimo de impacto nas demais.
 * Além de padronizar as funcionalidades que devem ser disponibilizadas pela camada de negócio, por intermédio
 * de sua classe Façade (responsável por persistir os dados das classes VO).
 */
public interface PerfilAcessoInterfaceFacade extends SuperInterface {

    public PerfilAcessoVO novo() throws Exception;

    public void incluir(PerfilAcessoVO obj) throws Exception;

    public void alterar(PerfilAcessoVO obj) throws Exception;

    public void excluir(PerfilAcessoVO obj) throws Exception;

    public PerfilAcessoVO consultarPorTipo(PerfilUsuarioEnum tipo, boolean controlarAcesso, int nivelMontarDados) throws Exception;

    public PerfilAcessoVO consultarPorChavePrimaria(Integer codigo, int nivelMontarDados) throws Exception;

    public List consultarPorCodigo(Integer valorConsulta, boolean controlarAcesso, int nivelMontarDados) throws Exception;

    public List consultarPorNome(String valorConsulta, boolean controlarAcesso, int nivelMontarDados) throws Exception;

    public List consultarPorParteDoNome(String valorConsulta, boolean controlarAcesso, int nivelMontarDados) throws Exception;

    public void setIdEntidade(String aIdEntidade);

    public PerfilAcessoVO criarOuConsultarSeExistePorNome(PerfilAcessoVO obj) throws Exception;

    public void incluir(PerfilAcessoVO obj, boolean centralEventos) throws Exception;

    public void alterar(PerfilAcessoVO obj, boolean centralEventos) throws Exception;

    public void alterarSemCommit(PerfilAcessoVO obj) throws Exception;

    public void excluir(PerfilAcessoVO obj, boolean centralEventos) throws Exception;

    public void incluirSemCommit(PerfilAcessoVO obj) throws Exception;

    public int consultarPorNome(String valorConsulta) throws Exception;

    public String consultarJSON() throws Exception;

    public boolean temPerfilUnificado();

    public List consultarParaImpressao(String filtro, String ordem, String campoOrdenacao, int i) throws Exception;

    List consultarUsuariosDoPerfil(Integer codPerfil, String filtro, String ordem, String campoOrdenacao, String situacao) throws Exception;

    public String consultarUsuariosPerfilAcessoJSON(Integer perfilAcesso, String situacao, String sEcho, Integer offset, Integer limit, String clausulaLike, Integer colOrdenar, String dirOrdenar) throws Exception;
    
    public PerfilAcessoVO consultarPorUsuarioEmpresa(Integer usuario, Integer empresa, int nivelMontarDados) throws Exception;

    Double consultarMaximoDescontoPorUsuarioEmpresa(Integer usuario, Integer empresa) throws Exception;

    List<AvisoInternoDTO> avisosInternos(Integer empresa, Integer usuario, Boolean permissao);

    void inserirAvisoInterno(Integer empresa,
                             String aviso,
                             Integer autor,
                             Date limite,
                             List<PerfilAcessoVO> perfis,
                             List<UsuarioVO> usuarios) throws Exception;

    void deletarAvisoInterno(int codigo) throws Exception;

    void atualizarAvisoInterno(AvisoInternoDTO aviso,
                               List<PerfilAcessoVO> perfis,
                               List<UsuarioVO> usuarios) throws Exception;

    AvisoInternoDTO getAvisoInternoPorCodigo(int codigo) throws Exception;
}

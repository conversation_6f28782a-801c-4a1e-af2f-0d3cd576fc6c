package negocio.interfaces.arquitetura;

import negocio.comuns.arquitetura.UsuarioTelefoneVO;
import negocio.interfaces.basico.SuperInterface;

public interface UsuarioTelefoneInterfaceFacade extends SuperInterface {

    void validarDados(UsuarioTelefoneVO obj) throws Exception;

    void incluir(UsuarioTelefoneVO obj) throws Exception;

    void gravar(UsuarioTelefoneVO obj) throws Exception;

    void alterar(UsuarioTelefoneVO obj) throws Exception;

    void excluirSemCommit(UsuarioTelefoneVO obj) throws Exception;

    void excluir(UsuarioTelefoneVO obj) throws Exception;

    UsuarioTelefoneVO consultarPorUsuario(Integer usuario) throws Exception;

    boolean existeTelefone(UsuarioTelefoneVO usuarioTelefoneVO) throws Exception;
}

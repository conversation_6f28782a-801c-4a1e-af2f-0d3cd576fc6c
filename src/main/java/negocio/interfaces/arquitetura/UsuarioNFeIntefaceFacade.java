package negocio.interfaces.arquitetura;

import negocio.comuns.arquitetura.UsuarioNFeVO;
import negocio.interfaces.basico.SuperInterface;

import java.util.List;

public interface UsuarioNFeIntefaceFacade extends SuperInterface {

    public UsuarioNFeVO novo() throws Exception;

    public void incluir(UsuarioNFeVO obj) throws Exception;

    public void alterar(UsuarioNFeVO obj) throws Exception;

    public void excluir(UsuarioNFeVO obj) throws Exception;

    public UsuarioNFeVO consultarPorChavePrimaria(Integer codigo) throws Exception;

    public boolean existeUsernameNaEmpresa(String usuario, int idEmpresa, int idUsuario) throws Exception;

    public List<UsuarioNFeVO> consultarPorCodigo(int codigo) throws Exception;

    public List<UsuarioNFeVO> consultarPorCodigoEEmpresa(int codigo, int id_empresa) throws Exception;

    public List<UsuarioNFeVO> consultarPorNome(String nome) throws Exception;

    public List<UsuarioNFeVO> consultarPorNomeEEmpresa(String nome, int id_empresa) throws Exception;

    public List<UsuarioNFeVO> consultarPorUsername(String username) throws Exception;

    public List<UsuarioNFeVO> consultarPorUsernameEEmpresa(String username, int id_empresa) throws Exception;

    public List<UsuarioNFeVO> consultarPorNomeEmpresa(String nomeEmpresa, int id_empresa) throws Exception;

    public void criarUsuarioPacto(int idDoPerfilPacto) throws Exception;
}

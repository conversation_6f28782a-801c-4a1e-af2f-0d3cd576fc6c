package negocio.interfaces.arquitetura;

import negocio.comuns.arquitetura.LogTotalPassVO;
import negocio.interfaces.basico.SuperInterface;

import java.util.List;

public interface LogTotalPassInterfaceFacade extends SuperInterface {

    LogTotalPassVO incluir(LogTotalPassVO obj) throws Exception;
    List<LogTotalPassVO> consultarPorPessoaLogTotalPass(Integer pessoa) throws Exception;
    String consultarLogTotalPassJSON(Integer cliente, Integer nrPaginaUltimosAcessos) throws Exception;
}

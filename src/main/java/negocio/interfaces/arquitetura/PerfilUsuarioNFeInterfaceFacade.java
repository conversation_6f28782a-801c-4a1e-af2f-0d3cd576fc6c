package negocio.interfaces.arquitetura;

import negocio.comuns.arquitetura.PerfilUsuarioNFeVO;
import negocio.comuns.nfe.EmpresaNFeVO;
import negocio.interfaces.basico.SuperInterface;

import javax.faces.model.SelectItem;
import java.util.List;

public interface PerfilUsuarioNFeInterfaceFacade extends SuperInterface {

    public PerfilUsuarioNFeVO novo() throws Exception;

    public void incluir(PerfilUsuarioNFeVO obj) throws Exception;

    public void alterar(PerfilUsuarioNFeVO obj) throws Exception;

    public void excluir(PerfilUsuarioNFeVO obj) throws Exception;

    public PerfilUsuarioNFeVO consultarPorChavePrimaria(Integer codigo) throws Exception;

    public List<PerfilUsuarioNFeVO> consultarPorCodigo(int codigo) throws Exception;

    public List<PerfilUsuarioNFeVO> consultarPorCodigoEEmpresa(int codigo, int id_empresa) throws Exception;

    public List<PerfilUsuarioNFeVO> consultarPorNome(String nome) throws Exception;

    public List<PerfilUsuarioNFeVO> consultarPorNomeEEmpresa(String nome, int id_empresa) throws Exception;

    public List<PerfilUsuarioNFeVO> consultarPorNomeEmpresa(String nomeEmpresa, int id_empresa) throws Exception;

    public List<SelectItem> obtenhaPerfisDaEmpresa(EmpresaNFeVO empresa) throws Exception;

    public void criarPerfilAdministrador(int idEmpresa) throws Exception;

    public void criarPerfilConsultor(int idEmpresa) throws Exception;

    public int obtenhaIdDoPerfilAdministrador(int idDaEmpresa) throws Exception;

    public int obtenhaIdDoPerfilConsultor(int idDaEmpresa) throws Exception;

    public int obtenhaIdDoPerfil(String nomeDoPerfil, int idDaEmpresa) throws Exception;
}

package negocio.interfaces;

import negocio.comuns.contrato.OperacaoColetivaVO;
import negocio.facade.jdbc.contrato.OperacaoColetiva;
import negocio.interfaces.basico.SuperInterface;

import java.sql.SQLException;
import java.util.Date;
import java.util.List;

public interface OperacaoColetivaInterfaceFacade extends SuperInterface {
    public void incluir(OperacaoColetivaVO obj) throws Exception;

    public void incluirSemCommit(OperacaoColetivaVO obj) throws Exception;
    public void alterar(OperacaoColetivaVO obj) throws Exception;

    public void altetarSemCommit(OperacaoColetivaVO obj) throws Exception;

    public List<OperacaoColetivaVO> consultar(Integer empresa, Date datainicio, Boolean processado, int nivelMontarDados) throws Exception;
    public void excluir(OperacaoColetivaVO obj) throws Exception ;
    public String existeOperacaoConflitante(OperacaoColetivaVO operacaoColetivaVO) throws Exception ;

    String consultarJSON() throws Exception;
    OperacaoColetivaVO consultarPorChavePrimaria(Integer codigo, Integer nivelMontarDados) throws Exception ;
}

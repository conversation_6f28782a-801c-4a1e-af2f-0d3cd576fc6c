package negocio.interfaces.ia;

import negocio.comuns.ia.RiscoEvasaoVO;
import negocio.interfaces.basico.SuperInterface;

public interface RiscoEvasaoInterfaceFacade extends SuperInterface {

    void incluir(RiscoEvasaoVO obj) throws Exception;

    void incluirSemCommit(RiscoEvasaoVO obj) throws Exception;

    void alterar(RiscoEvasaoVO obj) throws Exception;

    void alterarSemCommit(RiscoEvasaoVO obj) throws Exception;

    void gravarSQL(String sql) throws Exception;
}
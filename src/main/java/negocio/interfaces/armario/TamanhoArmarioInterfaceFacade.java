/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package negocio.interfaces.armario;

import java.util.List;
import negocio.armario.TamanhoArmarioVO;
import negocio.interfaces.basico.SuperInterface;

/**
 *
 * <AUTHOR>
 */
public interface TamanhoArmarioInterfaceFacade  extends SuperInterface {
    
    public TamanhoArmarioVO novo() throws Exception;

    public void incluir(TamanhoArmarioVO obj) throws Exception;

    public void alterar(TamanhoArmarioVO obj) throws Exception;

    public void excluir(TamanhoArmarioVO obj) throws Exception;

    public TamanhoArmarioVO consultarPorChavePrimaria(Integer codigo) throws Exception;
    
    public String consultarJSON() throws Exception;

    public List consultarParaImpressao(String filtro, String ordem, String campoOrdenacao, int i) throws Exception;
    
    public List<TamanhoArmarioVO> consultarPorDescricao(String valorConsulta) throws Exception;
}

/*
 * To change this template, choose Too<PERSON> | Templates
 * and open the template in the editor.
 */
package negocio.interfaces.armario;

import java.sql.SQLException;
import java.util.Date;
import java.util.List;

import negocio.armario.*;
import negocio.comuns.arquitetura.LogVO;
import negocio.comuns.arquitetura.UsuarioVO;
import negocio.comuns.financeiro.VendaAvulsaVO;
import negocio.interfaces.basico.SuperInterface;
import relatorio.controle.basico.ArmarioRelTO;
import relatorio.controle.basico.FiltroArmarioTO;

/**
 *
 * <AUTHOR>
 */
public interface ArmarioInterfaceFacade extends SuperInterface {
    
    public ArmarioVO novo() throws Exception;

    public void incluir(ArmarioVO obj) throws Exception;

    public void alterar(ArmarioVO obj) throws Exception;

    public void excluir(ArmarioVO obj) throws Exception;

    public ArmarioVO consultarPorChavePrimaria(Integer codigo) throws Exception;

    public Boolean verificarDescricaoExistente(ArmarioVO armario) throws Exception;
    
    public List<ArmarioVO> consultar(FiltroArmarioTO filtro) throws Exception;
    
    public void abrirArmario(ArmarioVO armario) throws Exception;
    
    public void fecharArmario(ArmarioVO armario) throws Exception;
    
    public void gerarArmarios(Integer inicio, Integer fim, TamanhoArmarioVO tamanhoArmario, GrupoArmarioEnum grupo, 
            UsuarioVO responsavel, Integer empresa) throws Exception;
    
    public VendaAvulsaVO alugarArmario(ArmarioVO armario, AluguelArmarioVO aluguel, UsuarioVO responsavel, Date inicioAluguel,Date fimAluguel,
            Integer nrVezes, Date vencimentoPrimeiraParcela) throws Exception;

    public VendaAvulsaVO alugarArmarioProrata(ArmarioVO armario, AluguelArmarioVO aluguel,VendaAvulsaVO venda,
                                              Date inicioAluguel, Date fimAluguel, Integer nrVezes, Date vencimentoPrimeiraParcela,Date dia) throws Exception ;
    
    public void alterarAluguelArmario(AluguelArmarioVO aluguel, boolean abrir) throws Exception;

    public List<AluguelArmarioVO> consultarAluguelPorDataFim(Date dataFim) throws Exception;
    
    public void preencherHistoricoAluguel(ArmarioVO obj) throws Exception;
    
    public void inativarArmario(ArmarioVO armario) throws Exception;

    public int obterTotalArmarios(FiltroArmarioTO filtro) throws Exception;
    
    public void estornarAluguel(Integer codigoMovProduto, LogVO log) throws Exception;
    
    public Date verificarAlunoTemArmarioEmAberto(Integer codigoCliente) throws Exception;
    
    public String consultarJSON(String codArmario) throws Exception;

    public void aluguelRenovado(ArmarioVO armario) throws Exception;

    public void trocarArmarioAluguel(ArmarioVO armario) throws Exception ;

    public void gerarHistoricoOperacao(int codigoAluguel,int codigoArmario,String descricao ,OperacaoArmarioEnum operacao,int responsavel) throws Exception;

    public void gravarContratoAssinadoArmario(AluguelArmarioVO aluguel)throws  Exception;

    public List<AluguelArmarioVO> obterAluguelPorCliente(int codigoCliente,int empresa,Boolean habilitadoGestao) throws Exception;

    public  List<ArmarioRelTO> consultarArmariosRel(FiltroArmarioTO filtro) throws Exception;

    public List consultarParaImpressao(String filtro, String ordem, String campoOrdenacao, int i, String codArmario) throws SQLException;

    public int obterSumPorTamanho(int status, int empresa) throws Exception;

    public int obterSumPorStatus(int status, int empresa) throws Exception;

    public int obterSumPorGrupo(String grupo, int empresa) throws Exception;

    public void estornoArmario(int codigoVenda) throws Exception;
}

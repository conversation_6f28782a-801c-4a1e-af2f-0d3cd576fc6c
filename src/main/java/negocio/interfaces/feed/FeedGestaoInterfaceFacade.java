/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package negocio.interfaces.feed;

import java.util.Date;
import java.util.List;
import negocio.comuns.arquitetura.UsuarioVO;
import negocio.comuns.feed.DicaEnum;
import negocio.comuns.feed.FeedGestaoHistoricoVO;
import negocio.comuns.feed.FeedGestaoVO;
import negocio.comuns.feed.PerfilUsuarioEnum;
import negocio.interfaces.basico.SuperInterface;

/**
 *
 * <AUTHOR>
 */
public interface FeedGestaoInterfaceFacade extends SuperInterface{
    public void incluir(FeedGestaoVO obj, Integer empresa) throws Exception;
    
    public void incluirSemCommit(FeedGestaoVO obj, Integer empresa) throws Exception;
    
    public void deletarTodos(Integer empresa) throws Exception;
    
    public List<FeedGestaoVO> obterTodos(Integer empresa, UsuarioVO usuario, Integer perfil) throws Exception;
    
    public boolean usouRecorrenciaPeriodo(Date inicio, Date fim, Integer empresa) throws Exception;
    
    public void marcarLida(Integer codigoOAMD, Integer usuario) throws Exception;
    
    public Integer inserirHistorico(Integer codigoOAMD,Integer empresa, Date dia) throws Exception;
    
    public void marcarAvaliacao(Integer codigoHistorico, Integer usuario, boolean liked, boolean disliked) throws Exception;

    public Integer codigoFeedGestao(Integer codigoHistorico, Integer usuario) throws Exception;

    public boolean isFeedGestaoLida(Integer codigoHistorico, Integer usuario) throws Exception;
    
    public FeedGestaoHistoricoVO obterUltimaApresentacaoDaDica(Integer codigoOamd) throws Exception;
    
    public String gerarFeedBack(Date data) throws Exception;
    
    public void deletarPorTipo(Integer empresa, DicaEnum dica) throws Exception;
    
    public FeedGestaoVO consultarPorDica(DicaEnum dica, Integer empresa) throws Exception;
    
    public List<FeedGestaoVO> obterFeedsTelaBI(Integer empresa) throws Exception;
}

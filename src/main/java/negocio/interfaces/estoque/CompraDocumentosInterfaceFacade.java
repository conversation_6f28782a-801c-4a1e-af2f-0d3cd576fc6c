package negocio.interfaces.estoque;

import negocio.comuns.estoque.CompraVO;
import negocio.comuns.estoque.DocumentoCompraVO;
import negocio.interfaces.basico.SuperInterface;

import java.util.List;

public interface CompraDocumentosInterfaceFacade extends SuperInterface {

    void incluirListaDocumentosCompra(String chave, CompraVO compraVO) throws Exception;

    List<DocumentoCompraVO> consultarPorCompra(Integer codigoCompra) throws Exception;

    void excluirPorCompra(String chave, CompraVO compra) throws Exception;

}

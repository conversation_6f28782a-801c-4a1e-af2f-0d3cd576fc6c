/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */

package negocio.interfaces.estoque;

import java.util.List;
import java.util.Set;
import negocio.comuns.estoque.CompraItensVO;
import negocio.comuns.estoque.CompraVO;
import negocio.interfaces.basico.SuperInterface;

/**
 *
 * <AUTHOR>
 */
public interface CompraItensInterfaceFacade extends SuperInterface {

    public CompraItensVO novo() throws Exception;

    public void incluir(CompraItensVO obj) throws Exception;

    public void incluirListaCompraItens(CompraVO compraVO, Set<CompraItensVO> lista) throws Exception;

    public CompraItensVO consultarPorChavePrimaria(Integer codigo, int nivelMontarDados) throws Exception;

    public List<CompraItensVO> consultarPorCompra(Integer codigoCompra, int nivelMontarDados) throws Exception;

}

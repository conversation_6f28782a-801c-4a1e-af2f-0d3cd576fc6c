/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */

package negocio.interfaces.estoque;

import negocio.comuns.estoque.CompraVO;
import negocio.interfaces.basico.SuperInterface;

import java.sql.SQLException;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 */
public interface CompraInterfaceFacade extends SuperInterface {

    public CompraVO novo() throws Exception;

    public void incluir(CompraVO obj) throws Exception;

    public void alterar(CompraVO obj, boolean validarData, boolean autorizar) throws Exception;

    public void cancelar(CompraVO obj) throws Exception;

    public CompraVO consultarPorChavePrimaria(Integer codigo, int nivelMontarDados) throws Exception;

    public CompraVO consultarPorNumeroNF(String numeroNF, int nivelMontarDados) throws Exception;

    public List<CompraVO> consultar(Integer codigoEmpresa, Date dataIniCadastro, Date dataFimCadastro, Integer codigoFornecedor, String numeroNF, int nivelMontarDados) throws Exception;

    public String consultarJSON(Integer empresa) throws Exception;

    public String consultarUltimoProdutoConfigJson(Integer codigoEmpresa) throws Exception;

    public List consultarParaImpressao(String filtro, String ordem, String campoOrdenacao, Integer empresa) throws Exception;

    void alterarSomenteChaveArquivo(CompraVO obj) throws SQLException;

    boolean existeCompraPorNumeroNF(String numeroNF) throws Exception;

}

/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */

package negocio.interfaces.estoque;

import negocio.comuns.estoque.BalancoItensVO;
import negocio.comuns.estoque.BalancoVO;
import negocio.interfaces.basico.SuperInterface;

import java.util.List;
import java.util.Set;
import java.util.Date;

/**
 *
 * <AUTHOR>
 */
public interface BalancoItensInterfaceFacade extends SuperInterface {

    public BalancoItensVO novo() throws Exception;

    public void incluir(BalancoItensVO obj) throws Exception;

    public void incluirListaBalancoItens(BalancoVO balancoVO, Set<BalancoItensVO> lista) throws Exception;

    public BalancoItensVO consultarPorChavePrimaria(Integer codigo, int nivelMontarDados) throws Exception;

    public List<BalancoItensVO> consultarPorBalanco(Integer codigoBalanco, int nivelMontarDados) throws Exception;

    Date pesquisarBalancoComDataMaior(Date dataComparar, Integer codigoProduto, Integer codigoEmpresa)throws Exception;

}

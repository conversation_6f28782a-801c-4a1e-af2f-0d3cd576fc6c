package negocio.interfaces.estoque;

import negocio.comuns.estoque.CompraVO;
import negocio.comuns.estoque.ContaFinanceiroCompraVO;
import negocio.interfaces.basico.SuperInterface;

import java.util.List;

public interface ContaFinanceiroCompraInterfaceFacade extends SuperInterface {

    void incluirLista(List<ContaFinanceiroCompraVO> parcelasConta, CompraVO compraVO) throws Exception;

    List<ContaFinanceiroCompraVO> consultarPorCompra(Integer codigoCompra) throws Exception;

}

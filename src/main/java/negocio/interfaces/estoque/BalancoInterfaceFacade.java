/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */

package negocio.interfaces.estoque;

import negocio.comuns.estoque.BalancoVO;
import negocio.interfaces.basico.SuperInterface;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 */
public interface BalancoInterfaceFacade extends SuperInterface {

    public BalancoVO novo() throws Exception;

    public void incluir(BalancoVO obj) throws Exception;

    public void cancelar(BalancoVO obj) throws Exception;

    public BalancoVO consultarPorChavePrimaria(Integer codigo, int nivelMontarDados) throws Exception;

    public List<BalancoVO> consultar(Integer codigoEmpresa, Date dataIniCadastro, Date dataFimCadastro, int situacao, int nivelMontarDados) throws Exception;

    public BalancoVO consultarUltimoBalancoAtivo(Integer codigoEmpresa, int nivelMontarDados) throws Exception;

    public String consultarJSON(Integer empresa) throws Exception;

    public List consultarParaImpressao(String filtro, String ordem, String campoOrdenacao, Integer empresa) throws Exception;


    List<BalancoVO> consultarBalancoMesclado(List<Integer> idProdutosMesclados) throws Exception;
}

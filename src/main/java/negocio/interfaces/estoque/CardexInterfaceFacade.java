/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */

package negocio.interfaces.estoque;

import negocio.comuns.estoque.CardexVO;
import negocio.interfaces.basico.SuperInterface;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 */
public interface CardexInterfaceFacade extends SuperInterface {

    public List<CardexVO> consultarCardexAgrupadoPorDia(Integer codigoEmpresa, Integer codigoProduto, Date dataInicio, Date dataFim) throws Exception;

    public List<CardexVO> consultarCardexAnalitico(Integer codigoEmpresa, Integer codigoProduto, Date dataInicio, Date dataFim) throws Exception;

    public List<CardexVO> consultarDetalhesOperacao(Integer codigoEmpresa, Integer codigoProduto, CardexVO cardexVO) throws Exception;

    public String consultarJSON(Integer empresa) throws Exception;

}

/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */

package negocio.interfaces.estoque;

import br.com.pactosolucoes.controle.json.estoque.ListaEstoqueJSON;
import negocio.comuns.arquitetura.UsuarioVO;
import negocio.comuns.estoque.ProdutoEstoqueVO;
import negocio.interfaces.basico.SuperInterface;

import java.sql.ResultSet;
import java.util.Date;
import java.util.List;
import negocio.comuns.estoque.CompraItensVO;

/**
 * <AUTHOR>
 */
public interface ProdutoEstoqueInterfaceFacade extends SuperInterface {

    public ProdutoEstoqueVO novo() throws Exception;

    public void incluir(List<ProdutoEstoqueVO> listaProdutoEstoqueVO, UsuarioVO usuario) throws Exception;

    public void alterar(ProdutoEstoqueVO obj) throws Exception;

    public void alterarSituacao(ProdutoEstoqueVO obj, UsuarioVO usuario) throws Exception;

    public ProdutoEstoqueVO consultarPorChavePrimaria(Integer codigo, int nivelMontarDados) throws Exception;

    public ProdutoEstoqueVO consultarPorProduto(Integer codigoProduto, Integer codigoEmpresa, int nivelMontarDados) throws Exception;

    public ResultSet consultarProdutoEstoqueMinimo(int codigoEmpresa) throws Exception;

    public List<ProdutoEstoqueVO> consultar(Integer codigoEmpresa, Integer codigoProduto, Integer codigoCategoriaProduto, String situacao, int nivelMontarDados) throws Exception;

    public List<ProdutoEstoqueVO> consultarPosicaoEstoque(int tipoRelatorio, Integer codigoEmpresa, Integer codigoCategoria, int statusProduto, int valorImpresso, int ordenacao, int nivelMontarDados) throws Exception;

    public String consultarJSON(Integer empresa) throws Exception;

    public List consultarParaImpressao(String filtro, String ordem, String campoOrdenacao, Integer empresa) throws Exception;

    Date pesquisarProdutoEstoqueComDataMaior(Date dataComparar, Integer codigoProduto, Integer codigoEmpresa)throws Exception;
    
    public void pesquisarDatasAlteracoesProdutoEstoque(CompraItensVO item, Integer codigoEmpresa) throws Exception;

    ListaEstoqueJSON consultarEstoqueProdutosJSON(int codigoEmpresa) throws Exception;
}

/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package negocio.interfaces.acesso;

import negocio.comuns.acesso.LocalAcessoVO;
import negocio.interfaces.basico.SuperInterface;

import javax.ejb.Local;
import java.sql.Connection;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 */
public interface LocalAcessoInterfaceFacade extends SuperInterface {

    public LocalAcessoVO novo() throws Exception;

    public void incluir(LocalAcessoVO obj) throws Exception;

    public void alterar(LocalAcessoVO obj) throws Exception;

    public void excluir(LocalAcessoVO obj) throws Exception;

    public LocalAcessoVO consultarPorComputador(String nomeComputador, Integer empresa) throws Exception;

    public List consultarPorCodigo(Integer valorConsulta, int empresa, boolean controlarAcesso, int nivelMontarDados) throws Exception;

    public LocalAcessoVO consultarPorCodigo(Integer valorConsulta, int nivelMontarDados) throws Exception;

    public List consultarPorNome(String valorConsulta, boolean controlarAcesso, int nivelMontarDados) throws Exception;

    public void setIdEntidade(String idEntidade);

    public void incluirSemCommit(LocalAcessoVO obj) throws Exception;

    public String consultarJSON() throws Exception;
    
    public List<LocalAcessoVO> consultarPorEmpresa(Integer empresa,boolean integracao, int nivelMontarDados) throws Exception;
    
    public List<LocalAcessoVO> consultarPorEmpresa(Integer empresa, Connection con) throws Exception;

    public List consultarParaImpressao(String filtro, String ordem, String campoOrdenacao, Integer empresa) throws Exception;

    public void atualizarDadosOffLocalAcesso(LocalAcessoVO localAcesso) throws Exception;

    public void alterarDataBaseOffline(LocalAcessoVO localAcesso, Date dataBaseOffline) throws Exception;

    public void alterarDataDownloadBase(Integer codLocalAcesso, Date dataDownloadBase) throws Exception;

    public void alterarVersaoAcesso(Integer codLocalAcesso, String versao,String parametros) throws Exception;
    
    public boolean utilizaModoOffLine(Integer codLocalAcesso) throws Exception;
    
    public boolean temDadosOffLine(Integer codLocalAcesso) throws Exception;
    
    public Date ultimoDadosOffLineGerado(Integer codLocalAcesso) throws Exception;

    void alterarIpLocalAcesso(Integer codLocalAcesso, String versao) throws Exception;

    String obterIp(String categoria) throws Exception;

    public boolean consultarExisteNFC(String nfc, Integer codigo) throws Exception;
}

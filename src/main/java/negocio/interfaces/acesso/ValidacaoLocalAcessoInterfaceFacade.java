/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */

package negocio.interfaces.acesso;

import java.sql.SQLException;
import java.util.List;
import negocio.comuns.acesso.ValidacaoLocalAcessoVO;
import negocio.interfaces.basico.SuperInterface;

/**
 *
 * <AUTHOR>
 */
public interface ValidacaoLocalAcessoInterfaceFacade extends SuperInterface {

    List<ValidacaoLocalAcessoVO> consultar(final String condicao,
            final int nivelMontarDados) throws SQLException, Exception;
   public void alterarValidacoes(Integer coletorPrm, List<ValidacaoLocalAcessoVO> listaValidacoes) throws Exception;
   public void incluirValidacoes(Integer coletorPrm, List<ValidacaoLocalAcessoVO> listaValidacoes) throws Exception;
   public void excluirValidacoes(Integer coletorPrm) throws Exception;
   public List<ValidacaoLocalAcessoVO> consultarPorColetor(final Integer codigoColetor, final int nivelMontarDados) throws S<PERSON><PERSON>x<PERSON>, Exception;

}

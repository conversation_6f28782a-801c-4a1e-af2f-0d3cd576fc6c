package negocio.interfaces.acesso;

import negocio.comuns.acesso.IntegracaoMemberVO;
import negocio.interfaces.basico.SuperInterface;

import java.util.List;

public interface IntegracaoMemberInterfaceFacade extends SuperInterface {

    void incluir(IntegracaoMemberVO obj) throws Exception;

    void alterar(IntegracaoMemberVO obj) throws Exception;

    void excluir(IntegracaoMemberVO obj) throws Exception;

    List<IntegracaoMemberVO> consultarIntegracoesPorEmpresa(Integer codEmpresa, boolean memberFreePass) throws Exception;

    IntegracaoMemberVO consultarPorCodigo(Integer valorConsulta) throws Exception;

}

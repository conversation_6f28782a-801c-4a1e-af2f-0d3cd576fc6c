package negocio.interfaces.acesso;

import negocio.comuns.acesso.ServidorFacialVO;
import negocio.interfaces.basico.SuperInterface;

import java.sql.SQLException;
import java.util.List;

public interface ServidorFacialInterfaceFacade extends SuperInterface {

    void incluir(ServidorFacialVO obj) throws Exception;

    void alterar(ServidorFacialVO obj) throws Exception;

    void excluir(ServidorFacialVO obj) throws Exception;

    String consultarJSON() throws Exception;

    List consultarParaImpressao(String filtro, String ordem, String campoOrdenacao, Integer empresa) throws Exception;

    ServidorFacialVO consultarPorChavePrimaria(Integer codigoConsulta, int nivelMontarDados) throws Exception;

    ServidorFacialVO consultarPorNomePC(String nomePC) throws Exception;
}

/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package negocio.interfaces.acesso;

import java.sql.SQLException;
import java.util.Date;
import java.util.List;
import negocio.comuns.acesso.AcessoColaboradorVO;
import negocio.comuns.acesso.ColetorVO;
import negocio.comuns.acesso.LiberacaoAcessoVO;
import negocio.comuns.acesso.LocalAcessoVO;
import negocio.comuns.basico.ColaboradorVO;
import negocio.comuns.acesso.enumerador.DirecaoAcessoEnum;
import negocio.comuns.acesso.enumerador.MeioIdentificacaoEnum;
import negocio.interfaces.basico.SuperInterface;
import relatorio.negocio.comuns.basico.ItemRelatorioTO;

/**
 *
 * <AUTHOR>
 */
public interface AcessoColaboradorInterfaceFacade extends SuperInterface {

    public Integer registrarAcessoColaborador(Date dataAcesso, ColaboradorVO colaborador,
            DirecaoAcessoEnum direcao, LocalAcessoVO local, ColetorVO coletor, MeioIdentificacaoEnum meioIdentificacao) throws Exception;

    public Integer registrarAcessoColaborador(Date dataAcesso, ColaboradorVO colaborador,
                                              DirecaoAcessoEnum direcao, LocalAcessoVO local, ColetorVO coletor, MeioIdentificacaoEnum meioIdentificacao, LiberacaoAcessoVO liberacaoAcessoVO) throws Exception;

    public AcessoColaboradorVO consultarUltimoAcesso(ColaboradorVO colaborador, int nivelMontarDados) throws Exception;

    public List<AcessoColaboradorVO> consultarUltimos5Acessos(ColaboradorVO colaborador, int nivelMontarDados) throws Exception;

    public List<AcessoColaboradorVO> consultarTodosAcessos(ColaboradorVO colaborador, int nivelMontarDados) throws Exception;

    public void setIdEntidade(String idEntidade);

    public AcessoColaboradorVO consultarPorChavePrimaria(Integer codigoPrm, int nivelMontarDados) throws Exception;

    public AcessoColaboradorVO consultarPorCodigo(Integer codigoPrm, int nivelMontarDados) throws Exception;

    public int consultarTotalAcessosFiltros(int empresa, java.sql.Date dataInicial, java.sql.Date dataFinal, String horaInicial, String horaFinal) throws SQLException;
    
    public AcessoColaboradorVO consultarUltimoAcessoPorLocal(ColaboradorVO colaborador,Date data, Integer localAcesso, int nivelMontarDados) throws Exception;

    public List<ItemRelatorioTO> consultarUltimosAcessosRelatorio(Integer pessoa, String dataInicio, String dataTermino) throws Exception;
}

/*
 * To change this template, choose Too<PERSON> | Templates
 * and open the template in the editor.
 */
package negocio.interfaces.acesso;

import negocio.comuns.acesso.DadosAcessoOfflineVO;
import negocio.interfaces.basico.SuperInterface;

import java.util.List;

/**
 *
 * <AUTHOR>
 */
public interface DadosAcessoOfflineInterfaceFacade extends SuperInterface {

    public void incluir(DadosAcessoOfflineVO obj) throws Exception;

    public void excluirTudo(Integer codigoPessoa) throws Exception;
    
    public void excluirDadosLocalAcesso(Integer localAcesso) throws Exception;

    public void preencherDados(String key, Integer codigoPessoa) throws Exception;
    public void preencherDadosSemCommit(String key, Integer codigoPessoa) throws Exception;
    
    public void preencherDadosLocalAcesso(Integer localAcesso, Integer empresa) throws Exception;

    public List<DadosAcessoOfflineVO> consultarDados(Integer localAcesso) throws Exception;

    public List<DadosAcessoOfflineVO> consultarDadosPessoa(Integer localAcesso, Integer codigoPessoa) throws Exception;
}

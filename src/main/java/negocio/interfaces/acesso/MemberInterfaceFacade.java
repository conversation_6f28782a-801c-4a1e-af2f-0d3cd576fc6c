package negocio.interfaces.acesso;

import negocio.comuns.acesso.IntegracaoMemberVO;
import negocio.comuns.acesso.integracao.member.MemberDataJson;
import negocio.comuns.acesso.integracao.member.MemberVO;
import negocio.interfaces.basico.SuperInterface;

import java.util.Date;
import java.util.List;

public interface MemberInterfaceFacade extends SuperInterface {

    void incluir(MemberVO obj) throws Exception;

    void alterar(MemberVO obj) throws Exception;

    void excluir(MemberVO obj) throws Exception;

    MemberVO consultarPorIdMember(Integer idMember) throws Exception;

    List<MemberVO> consultarMembersParaSincronizar(IntegracaoMemberVO integracao) throws Exception;

    List<MemberDataJson> obterMembersImportar(Integer codigoIntegracaoMember, List<Integer> idsMembers, Date dataBaseSincronizacao) throws Exception;

    List<Integer> obterIdsMembersPorIntegracaoMember(Integer codigoIntegracaoMember) throws Exception;

    void setarSincronizado(Integer codigoMember, boolean sincronizado) throws Exception;
}

package negocio.interfaces.acesso;

import java.util.List;

import negocio.comuns.acesso.IntegracaoAcessoGrupoEmpresarialVO;
import negocio.interfaces.basico.SuperInterface;

public interface IntegracaoAcessoGrupoEmpresarialInterfaceFacade extends SuperInterface{

	public void incluir(IntegracaoAcessoGrupoEmpresarialVO obj) throws Exception;
	
	public void incluirSemCommit(IntegracaoAcessoGrupoEmpresarialVO obj) throws Exception;
	
	public void alterar(IntegracaoAcessoGrupoEmpresarialVO obj) throws Exception;
	
	public void alterarSemCommit(IntegracaoAcessoGrupoEmpresarialVO obj) throws Exception;
	
	public List<IntegracaoAcessoGrupoEmpresarialVO> consultar(String descricao, Integer empresa, Integer nivelMontarDados) throws Exception;

    public void excluir(IntegracaoAcessoGrupoEmpresarialVO obj) throws Exception;
        
    public String consultarJSON() throws Exception;

    public IntegracaoAcessoGrupoEmpresarialVO consultarPorCodigo(Integer codigo, Integer nivelMontarDados) throws Exception;

	public boolean obterPodeExcluir(int codigo) throws Exception;

    IntegracaoAcessoGrupoEmpresarialVO consultarPorCodigoChaveIdentificacao(String identificadorOutraEmpresa, Integer nivelMontarDados) throws Exception;

    public IntegracaoAcessoGrupoEmpresarialVO consultarPorChaveEEmpresa(String chave, Integer codEmpresaRemota, Integer codEmpresaLocal, Integer nivelMontarDados) throws Exception;
}

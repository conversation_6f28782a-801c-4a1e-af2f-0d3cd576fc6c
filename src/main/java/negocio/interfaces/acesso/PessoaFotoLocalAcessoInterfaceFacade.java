/*
 * To change this template, choose Too<PERSON> | Templates
 * and open the template in the editor.
 */

package negocio.interfaces.acesso;

import negocio.comuns.acesso.LocalAcessoVO;
import negocio.comuns.acesso.PessoaFotoLocalAcessoVO;
import negocio.comuns.basico.PessoaVO;

/**
 *
 * <AUTHOR>
 */
public interface PessoaFotoLocalAcessoInterfaceFacade {

    public void incluir(PessoaFotoLocalAcessoVO obj) throws Exception;

    public void incluir(LocalAcessoVO localAcesso, PessoaVO pessoa) throws Exception;

    public void incluir(Integer localAcesso, Integer pessoa) throws Exception;

    public void excluir(PessoaFotoLocalAcessoVO obj) throws Exception;

    public void excluir(int codigo) throws Exception;

    public void excluirFotoPessoaLocalAcesso(int pessoa) throws Exception;

    public void excluirFotoPessoaLocalAcesso(int localAcesso, int pessoa) throws Exception;

    public boolean fotoJaEnviadaLocalAcesso(Integer localAcesso, Integer pessoa) throws Exception;
    
}

/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package negocio.interfaces.acesso;

import negocio.comuns.acesso.ColetorVO;
import negocio.comuns.crm.GenericoTO;
import negocio.interfaces.basico.SuperInterface;

import java.sql.Connection;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 */
public interface ColetorInterfaceFacade extends SuperInterface {

    public ColetorVO novo() throws Exception;

    public void incluir(ColetorVO obj) throws Exception;

    public void alterar(ColetorVO obj) throws Exception;

    public void excluir(ColetorVO obj) throws Exception;

    public void setIdEntidade(String idEntidade);

    public ArrayList<ColetorVO> consultarColetores(Integer localAcesso, int nivelMontarDados) throws Exception;

    public ColetorVO consultarPorCodigo(Integer valorConsulta) throws Exception;

    public ColetorVO consultarPorNumeroTerminal(String numTerminal, int nivelMontarDados) throws Exception;

    public void alterarColetores(Integer localAcesso, List<ColetorVO> objetos) throws Exception;

    public void excluirColetores(Integer localAcesso, List<ColetorVO> listaColetores) throws Exception;

    public void incluirColetores(Integer localAcessoPrm, List objetos) throws Exception;

    public Map<Integer, ColetorVO> consultarColetores() throws Exception;

    public List<ColetorVO> consultarPorDescricao(String valorConsulta, int nivelMontarDados) throws Exception;

    public void incluirSemCommit(ColetorVO obj) throws Exception;

    public ArrayList<ColetorVO> consultarColetores(Integer localAcesso, Connection con) throws Exception;

    List<GenericoTO> consultarColetoresPorNumeroTerminal(String pref, int nivelMontarDados) throws Exception;

    List<ColetorVO> consultarPorEmpresa(Integer empresa) throws Exception;

    ColetorVO consultarPorNFC(String nfc) throws Exception;
}

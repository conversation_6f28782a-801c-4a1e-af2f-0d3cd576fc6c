package negocio.interfaces.acesso;

import negocio.comuns.acesso.CameraVO;
import negocio.interfaces.basico.SuperInterface;

import java.sql.SQLException;
import java.util.List;

public interface CameraInterfaceFacade extends SuperInterface {

    void incluir(CameraVO obj) throws Exception;

    void alterar(CameraVO obj) throws Exception;

    void excluir(CameraVO obj) throws Exception;

    CameraVO consultarPorChavePrimaria(Integer codigo) throws Exception;

    List<CameraVO> consultarCameras(Integer servidorFacial, int nivelMontarDados) throws Exception;

    void incluirCameras(Integer servidorFacial, List<CameraVO> cameras) throws Exception;

    void alterarCameras(Integer servidorFacial, List<CameraVO> cameras) throws Exception;
}

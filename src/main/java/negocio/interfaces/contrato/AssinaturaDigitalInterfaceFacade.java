package negocio.interfaces.contrato;

import negocio.interfaces.basico.SuperInterface;

import java.util.Date;


/**
 * Interface reponsável por criar uma estrutura padrão de comunidação entre a camada de controle
 * e camada de negócio (em especial com a classe Façade). Com a utilização desta interface 
 * é possível substituir tecnologias de uma camada da aplicação com mínimo de impacto nas demais.
 * Além de padronizar as funcionalidades que devem ser disponibilizadas pela camada de negócio, por intermédio
 * de sua classe Façade (responsável por persistir os dados das classes VO).
 */
public interface AssinaturaDigitalInterfaceFacade extends SuperInterface {


    public void atualizarIPAssinaturaContrato(Integer codigoContrato, String ip, Date dataAssinaturaContrato) throws Exception;

    public Boolean verificaSeContratoJaFoiAssinado(Integer codigoContrato) throws Exception;
}

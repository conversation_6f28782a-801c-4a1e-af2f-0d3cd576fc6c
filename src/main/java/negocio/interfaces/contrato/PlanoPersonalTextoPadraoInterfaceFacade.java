package negocio.interfaces.contrato;

import negocio.comuns.contrato.PlanoPersonalTextoPadraoVO;
import negocio.interfaces.basico.SuperInterface;

import java.util.List;

public interface PlanoPersonalTextoPadraoInterfaceFacade extends SuperInterface {

    public PlanoPersonalTextoPadraoVO novo() throws Exception;

    public void incluir(PlanoPersonalTextoPadraoVO obj) throws Exception;

    public void alterar(PlanoPersonalTextoPadraoVO obj) throws Exception;

    public void excluir(PlanoPersonalTextoPadraoVO obj) throws Exception;

    public PlanoPersonalTextoPadraoVO consultarPorChavePrimaria(Integer codigo, int nivelMontarDados) throws Exception;

    public List consultarPorCodigo(Integer valorConsulta, boolean controlarAcesso, int nivelMontarDados) throws Exception;

    public void setIdEntidade(String aIdEntidade);

    public PlanoPersonalTextoPadraoVO consultarPlanoPersonalTextoPadrao(Integer contrato, int nivelMontarDados) throws Exception;

    /**
     * Operação responsável por localizar um objeto da classe <code>ContratoTextoPadraoVO</code>
     * através de sua chave primária.
     * @exception Exception Caso haja problemas de conexão ou localização do objeto procurado.
     */
    public PlanoPersonalTextoPadraoVO consultarPorCodigoPlanoPersonal(Integer codigoPrm, int nivelMontarDados) throws Exception;

}

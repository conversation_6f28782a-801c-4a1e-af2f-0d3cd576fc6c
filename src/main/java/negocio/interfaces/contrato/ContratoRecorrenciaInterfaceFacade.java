/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package negocio.interfaces.contrato;

import br.com.pactosolucoes.estrutura.paginacao.ConfPaginacao;
import negocio.comuns.arquitetura.UsuarioVO;
import negocio.comuns.contrato.ContratoRecorrenciaVO;
import negocio.comuns.contrato.ContratoVO;
import negocio.comuns.financeiro.MovParcelaVO;
import negocio.comuns.plano.PlanoRecorrenciaVO;
import negocio.comuns.plano.PlanoVO;
import negocio.interfaces.basico.SuperInterface;
import relatorio.controle.basico.RecorrenciaClienteTO;

import java.sql.Connection;
import java.sql.ResultSet;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 */
public interface ContratoRecorrenciaInterfaceFacade extends SuperInterface {

    void alterar(ContratoRecorrenciaVO obj) throws Exception;

    ContratoRecorrenciaVO consultarPorContrato(int codigoContrato, int nivelMontarDados, Connection con) throws Exception;

    void excluir(ContratoRecorrenciaVO obj) throws Exception;

    void incluir(ContratoRecorrenciaVO obj) throws Exception;

    Integer contarAtivosRecorrentes(Integer codigoEmpresa, Date dataFim, List<Integer> consultores, List<Integer> convenios) throws Exception;

    Integer contarAdimplentesRecorrentes(Integer codEmpresa, Date date, List<Integer> consultores, List<Integer> convenios)throws Exception;

    Double somarAdimplentesRecorrentes(Integer codEmpresa, Date date, List<Integer> consultores, List<Integer> convenios)throws Exception;

    List<RecorrenciaClienteTO> consultarAdimplentesRecorrencia(Integer codigoEmpresa, Date dataFim, List<Integer> consultores, List<Integer> convenios, int nivelMontarDados) throws Exception;

    ContratoRecorrenciaVO incluirContratoRecorrencia(PlanoRecorrenciaVO recorrencia, ContratoVO contratoVO, int diaVencimentoRecorrencia) throws Exception;

    public ContratoRecorrenciaVO consultarPorContrato(int codigoContrato, int nivelMontarDados) throws Exception;

    public void gravarDataInutilizada(ContratoRecorrenciaVO contratoRecorrencia) throws Exception;

    public List<ContratoRecorrenciaVO> consultarContratosVencendo(Date dia, Integer carenciaRenovacao, Integer nrDiasRenovacaoAntecipadaAutomatica, Integer empresa, int nivelMontarDados, Connection con) throws Exception;

    public List<ContratoRecorrenciaVO> consultarContratosPrevistosAnuidade(Date dia,
            Integer empresa, int nivelMontarDados, Connection con) throws Exception;

    public List<RecorrenciaClienteTO> consultarContratosRecorrencia(Integer codigoEmpresa, Date dataFim, List<Integer> consultores, List<Integer> convenios, int nivelMontarDados) throws Exception;

    public Integer contarCanceladosAutomaticamente(Integer codigoEmpresa, Date dataFim, List<Integer> consultores, List<Integer> convenios) throws Exception;

    public List<RecorrenciaClienteTO> consultarCanceladosRecorrencia(Integer codigoEmpresa, Date dataFim, List<Integer> consultores, List<Integer> convenios, int nivelMontarDados) throws Exception;

    public Integer contarNaoRenovadosRecorrentes(Integer codigoEmpresa, Date dataFim, List<Integer> consultores, List<Integer> convenios) throws Exception;

    public List<RecorrenciaClienteTO> consultarNaoRenovadosRecorrentes(Integer codigoEmpresa, Date dataFim, List<Integer> consultores, List<Integer> convenios, int nivelMontarDados) throws Exception;

    public Integer contarParcelasRecorrenciaSemCartao(Integer codigoEmpresa, Date dataFim, List<Integer> consultores, List<Integer> convenios) throws Exception;

    public List<RecorrenciaClienteTO> consultarContratosRecorrenciaSemCartao(Integer codigoEmpresa, Date dataFim, List<Integer> consultores, List<Integer> convenios) throws Exception;

    public List<RecorrenciaClienteTO> consultarContratosRecorrenciaParcelaEmAbertoDCC(Integer codigoEmpresa, Date dataFim, List<Integer> convenios) throws Exception;

    public Integer contarPendenciaClienteMensagemCartaoVencido(Integer codigoEmpresa, Date dataFim, List<Integer> consultores) throws Exception;

    /**
     *
     * @param codigoEmpresa     0 para consultar de todas as empresas
     * @param dataBaseInicio
     * @param dataFim
     * @param consultores
     * @param paginacao
     * @return
     * @throws Exception
     */
    ResultSet consultarPendenciaClienteMensagemCartaoVencido(Integer codigoEmpresa, Date dataBaseInicio, Date dataFim, List<Integer> consultores,ConfPaginacao paginacao) throws Exception;

    public Integer contarPendenciaClienteCartaoAVencer(Integer codigoEmpresa, Date dataFim, List<Integer> consultores) throws Exception;

    /**
     *
     * @param codigoEmpresa     0 para consultar de todas as empresas
     * @param dataBaseInicio
     * @param dataFim
     * @param consultores
     * @param paginacao
     * @return
     * @throws Exception
     */
    ResultSet consultarPendenciaClienteCartaoAVencer(Integer codigoEmpresa, Date dataBaseInicio, Date dataFim, List<Integer> consultores,ConfPaginacao paginacao) throws Exception;

    public List<RecorrenciaClienteTO> consultarParcelasRecorrencia(Integer codigoEmpresa, String colunas, boolean count, String[] colunasCount, String nrTentativas,
            String situacaoParcela, Date dataInicio, Date dataFim, List<Integer> convenios,
            String descricaoPagamento, String situacaoRemessa, boolean somenteMes,
            boolean somenteForaMes) throws Exception;

    public Integer contarParcelasCanceladas(Integer codigoEmpresa, String colunas, boolean count, String[] colunasCount, String nrTentativas,
            String situacaoParcela, Date dataInicio, Date dataFim, List<Integer> convenios,
            String descricaoPagamento, String situacaoRemessa, boolean somenteMes,
            boolean somenteForaMes) throws Exception;

    public Integer contarParcelasAbertas(Integer codigoEmpresa, Date fim) throws Exception;

    public Double somarParcelasAbertas(Integer codigoEmpresa, Date fim) throws Exception;

    public List<RecorrenciaClienteTO> consultarParcelasAbertas(Integer codigoEmpresa, Date fim) throws Exception;

    public Integer contarOperacoesSuspeitas(Integer codigoEmpresa, String colunas, boolean count, String[] colunasCount) throws Exception;

    public List<RecorrenciaClienteTO> consultarOperacoesSuspeitas(Integer codigoEmpresa, String colunas, boolean count, String[] colunasCount) throws Exception;

    public ContratoRecorrenciaVO consultarPorContratoPegarDiaDebitoAnuidade(int codigoContrato) throws Exception;

    public Integer contarPendenciaClientesMesmoCartao(Integer codigoEmpresa, Date dataBaseInicio, Date dataFim, List<Integer> consultores,boolean somenteAtivos) throws Exception;

    public ResultSet consultarPendenciaClientesMesmoCartao(Integer codigoEmpresa, boolean count, Date dataBaseInicio, Date dataFim, String nomeCliente, List<Integer> consultores, Boolean somenteAtivos,ConfPaginacao paginacao) throws Exception;

    public List<ContratoRecorrenciaVO> consultarContratosRecorrenciaPorContrato(Date dia, Integer empresa, Integer carenciaRenovacao, int contrato, int nivelMontarDados, Connection con) throws Exception;

    public void alterarRenovacaoAutomaticaContratoRecorrencia(PlanoVO planoVO, boolean renovarAuto) throws Exception;

    public void alterarDiaVencimento(Integer dia, Integer contrato, String usuarioResponsavel) throws Exception;

    /**
     * Altera as datas de vencimento de todas as parcelas de mensalidade em aberto do contrato
     *
     * @param contrato
     * @param novoDiaVencimento
     * @param usuario
     * @return
     * @throws Exception
     */
    List<MovParcelaVO> alterarDataVencimentoParcelasMensalidade(ContratoVO contrato, int novoDiaVencimento, UsuarioVO usuario, Double valorProRata, int diferencaDiasNovoVencimento, boolean liberarValorProRata, boolean alterarMesProximaParcela) throws Exception;

    boolean cancelamentoProporcionalContrato(int codigoContrato) throws Exception;

    List<Integer> consultarIdContratosVencendoRecorrente(Date dia,Integer carenciaRenovacao, Integer nrDiasRenovacaoAntecipadaAutomatica, Integer empresa, Connection con) throws Exception;

}

package negocio.interfaces.contrato;

import br.com.pactosolucoes.enumeradores.OrigemSistemaEnum;
import negocio.comuns.arquitetura.UsuarioVO;
import negocio.comuns.basico.ClienteVO;
import negocio.comuns.basico.ColaboradorVO;
import negocio.comuns.basico.EmpresaVO;
import negocio.comuns.contrato.AtestadoContratoVO;
import negocio.comuns.contrato.BonusContratoVO;
import negocio.comuns.contrato.CancelamentoContratoVO;
import negocio.comuns.contrato.CarenciaContratoVO;
import negocio.comuns.contrato.ContratoModalidadeVO;
import negocio.comuns.contrato.ContratoOperacaoVO;
import negocio.comuns.contrato.ContratoVO;
import negocio.comuns.contrato.JustificativaOperacaoVO;
import negocio.comuns.financeiro.MovPagamentoVO;
import negocio.comuns.financeiro.MovParcelaVO;
import negocio.comuns.plano.HorarioTurmaVO;
import negocio.comuns.utilitarias.PeriodoMensal;
import negocio.interfaces.basico.SuperInterface;
import relatorio.negocio.comuns.basico.PendenciaResumoPessoaRelVO;

import java.sql.SQLException;
import java.util.Date;
import java.util.List;
import negocio.comuns.contrato.DadosContratoOperacaoWS;
import negocio.comuns.contrato.DadosRetornoOperacaoWS;

/**
 * Interface reponsável por criar uma estrutura padrão de comunidação entre a camada de controle
 * e camada de negócio (em especial com a classe Façade). Com a utilização desta interface
 * é possível substituir tecnologias de uma camada da aplicação com mínimo de impacto nas demais.
 * Além de padronizar as funcionalidades que devem ser disponibilizadas pela camada de negócio, por intermédio
 * de sua classe Façade (responsável por persistir os dados das classes VO).
 */
public interface ContratoOperacaoInterfaceFacade extends SuperInterface {

    public ContratoOperacaoVO novo() throws Exception;

    public void incluir(ContratoOperacaoVO obj) throws Exception;

    public void incluirSemCommit(ContratoOperacaoVO obj, Boolean controleAcesso) throws Exception;

    public void incluirOperacaoCancelamentoContratoTransferencia(CancelamentoContratoVO cancelamentoContratoVO, ContratoVO contrato, UsuarioVO usuarioVO) throws Exception;

    public void incluirOperacaoBonus(BonusContratoVO bonusContratoVO, boolean alterarVigenciaIndependenteDaSituacao, Date dataPrevistaRenovar, boolean controleTransacao, Date processarSintetico) throws Exception;

    public void incluirOperacaoAlterarHorario(ContratoOperacaoVO obj, ContratoVO contratoAntigo, ContratoVO contratoNovo, Double valor, boolean liberado) throws Exception;

    public void incluirOperacaoAtestado(AtestadoContratoVO atestadoContratoVO, UsuarioVO usuarioVO) throws Exception;

    public void incluirOperacaoRetornoAtestado(AtestadoContratoVO atestadoContratoVO, UsuarioVO usuarioLogadoVO) throws Exception;

    public void incluirOperacaoCarencia(CarenciaContratoVO carenciaContratoVO, UsuarioVO usuarioVO) throws Exception;

    public void incluirOperacaoRetornoCarencia(CarenciaContratoVO carenciaContratoVO) throws Exception;

    public void alterar(ContratoOperacaoVO obj) throws Exception;

    public void alterarSemCommit(ContratoOperacaoVO obj, Boolean controleAcesso) throws Exception;

    public void alterarSemCommit(ContratoOperacaoVO obj) throws Exception;

    public void excluir(ContratoOperacaoVO obj) throws Exception;

    public ContratoOperacaoVO consultarPorChavePrimaria(Integer codigo, int nivelMontarDados) throws Exception;

    public String consultarPorAssinaturaDigitalBiometria(Integer codigoContrato) throws Exception;

    public List consultarPorCodigo(Integer valorConsulta, boolean controlarAcesso, int nivelMontarDados) throws Exception;

    public List consultarPorContrato(Integer valorConsulta, boolean controlarAcesso, int nivelMontarDados) throws Exception;

    public List consultarPorTipoOperacao(String valorConsulta, boolean controlarAcesso, int nivelMontarDados) throws Exception;

    public List consultarPorTipoOperacaoCodigoContrato(String valorConsulta, Integer contrato, boolean controlarAcesso, int nivelMontarDados) throws Exception;

    public ContratoOperacaoVO consultarPorTipoOperacaoCodigoContrato(Integer contrato, String operacao, boolean controlarAcesso, int nivelMontarDados) throws Exception;

    public List consultarPorDataOperacao(Date prmIni, Date prmFim, boolean controlarAcesso, int nivelMontarDados) throws Exception;

    public List consultarPorResponsavel(Integer valorConsulta, boolean controlarAcesso, int nivelMontarDados) throws Exception;

    public void setIdEntidade(String aIdEntidade);

    /**
     * Operação responsável por obter o último valor gerado para uma chave primária.
     * É utilizada para obter o valor gerado pela SGBD para uma chave primária,
     * a apresentação do mesmo e a implementação de possíveis relacionamentos.
     */
    public ContratoOperacaoVO obterUltimaOperacaoContratoPorCodigoContratoTipoOperacao(Integer contrato, String tipoOperacao, int nivelMontarDados) throws Exception;

    public Boolean consultarPorDataEspecificaECodigoContratoETipoOperacao(Date data, Integer contrato, String tipoOperacao, boolean controlarAcesso, int nivelMontarDados) throws Exception;

    public Boolean verificarUltimaOperacaoCodigoContratoETipoOperacao(Date data, Integer contrato, String tipoOperacao, boolean controlarAcesso) throws Exception;

    boolean consultarCodigoContratoETipoOperacao(Integer contrato, String tipoOperacao, boolean controlarAcesso, int nivelMontarDados) throws Exception;

    public ContratoOperacaoVO consultarOperacaoContratoPorDataInicioCodigoContratoTipoOperacao(Date data, Integer contrato, String tipoOperacao, boolean controlarAcesso, int nivelMontarDados) throws Exception;

    public ContratoOperacaoVO consultarOperacaoContratoApenasInicioCodigoContratoTipoOperacao(Date data, Integer contrato, String tipoOperacao, boolean controlarAcesso, int nivelMontarDados) throws Exception;

    public ContratoOperacaoVO obterOperacaoContratoPorDataEspecifica(Date data, Integer contrato, int nivelMontarDados) throws Exception;

    public void excluirSemCommit(ContratoOperacaoVO obj) throws Exception;

    public boolean existeCarencia(Date data, Integer codigoContrato) throws Exception;

    public void incluirOperacaoManutencaoModalidade(List<ContratoModalidadeVO> listaModalidadeAdicionada, List<ContratoModalidadeVO> listaModalidadeExcluida, List<ContratoModalidadeVO> listaModalidadeAlterada, List<ContratoOperacaoVO> listaContratoOperacao, ContratoVO contratoNovo, ContratoVO contratoAntigo, Double valor, Double valorPagar, Boolean operacaoPaga, Boolean liberado, UsuarioVO resp, Integer diasRestantes, boolean alteracaoManual, List<HorarioTurmaVO> listaTodosHorarioTurma, List<MovParcelaVO> listapParcelaVOS) throws Exception;

    public void incluirOperacaoBonusSemCommit(BonusContratoVO bonusContratoVO) throws Exception;

    public boolean existeOperacaoRetroativoAEstaData(int codigoContrato, String tipoOperacao, Date dataBase) throws Exception;

    public boolean existeOperacaoParaEsteContrato(int codigoContrato, String tipoOperacao) throws Exception;

    public boolean existeOperacaoRetroativaParaEsteContrato(int codigoContrato, String tipoOperacao) throws Exception;

    public boolean existeOperacaoInterceptaEstaData(int codigoContrato, String tipoOperacao, Date dataBase) throws Exception;

    public boolean obterOperacaoContratoPorDataEspecifica(Date data, Integer contrato, String tipoOperacao) throws Exception;

    public int obterNumeroContratosCancelados(Date inicio, Date fim, List<ColaboradorVO> colaboradores, Integer codigoEmpresa) throws Exception;

    public List<ClienteVO> obterClientesCancelados(Date inicio, Date fim, List<ColaboradorVO> colaboradores, Integer codigoEmpresa) throws Exception;

    public int obterNumeroClientesComBonus(Date fim, List<ColaboradorVO> colaboradores, Integer codigoEmpresa) throws Exception;

    public List<PendenciaResumoPessoaRelVO> obterClientesComBonus(Date fim, List<ColaboradorVO> colaboradores, Integer codigoEmpresa) throws Exception;

    public int obterNumeroClientesComFreePass(Date data, List<ColaboradorVO> colaboradores, Integer codigoEmpresa) throws Exception;

    public List<PendenciaResumoPessoaRelVO> obterClientesComFreePass(Date data, List<ColaboradorVO> colaboradores, Integer codigoEmpresa) throws Exception;

    public ContratoVO estornarContratoOperacao(ContratoOperacaoVO obj, UsuarioVO responsavel) throws Exception;

    public String consultarClientesCanceladosJSON(Integer empresa, List<ColaboradorVO> colaboradores, Date inicio, Date fim) throws Exception;

    public List consultarParaImpressaoContratosCancelados(Integer empresa, List<ColaboradorVO> colaboradores, Date inicio, Date fim, String filtro, String ordem, String campoOrdenacao) throws Exception;

    public int contarOperacoesContratoRetroativas(int empresa, Date inicio, Date fim, List<ColaboradorVO> lista) throws Exception;

    public List<PendenciaResumoPessoaRelVO> consultarOperacoesContratoRetroativas(int empresa, Date inicio, Date fim, List<ColaboradorVO> lista) throws Exception;

    public ContratoOperacaoVO consultarOperacaoBonusRenovacaoAntecipadaCreditoTreino(ContratoVO contratoVO, int nivelMontarDados) throws Exception;

    public void lancarBonusReducaoCreditoTreino(ContratoVO contrato, Integer nrDias,
                                                UsuarioVO usuario, JustificativaOperacaoVO justificativaOperacaoVO, String observacao, Date dataInicio, Date dataTermino, boolean renovacaoAntecipada) throws Exception;

    boolean existeOperacaoPendenteDeRetorno(Integer contrato, Date verificar) throws Exception;

    public void atualizaDataFimDeProdutoComVigenciaDeContrato(Integer codigoContrato, Date vigenciaDe, Date vigenciaAjustada) throws Exception;

    public boolean existeOperacaoLancadaFuturaAEstaData(int codigoContrato, String tipos, Date dataAtual) throws Exception;

    public void retirarVinculoTWCancelamento(Integer pessoa, boolean removerVinculo) throws Exception;

    List<PendenciaResumoPessoaRelVO> obterClientesComGymPass(Date data, List<ColaboradorVO> colaboradores, Integer codigoEmpresa) throws Exception;

    int obterNumeroClientesComGymPass(Date data, List<ColaboradorVO> colaboradores, Integer codigoEmpresa) throws Exception;

    void alterarSomenteChaveArquivo(AtestadoContratoVO atestadoContratoVO) throws SQLException;

    List<PendenciaResumoPessoaRelVO> obterClientesComGymPassPorPeriodo(Date dataInicial, Date dataFinal, EmpresaVO codigoEmpresa) throws Exception;

    public DadosContratoOperacaoWS obterDadosOperacaoContrato(ContratoVO contrato, String tipoOperacao) throws Exception;

    public DadosRetornoOperacaoWS validarDadosOperacaoContrato(ContratoVO contrato, final String tipoOperacao, Date inicio, Date fim, final Integer produto, final Integer justificativa) throws Exception;

    public String gravarDadosOperacaoContrato(ContratoVO contrato, final String tipoOperacao, Date inicio, Date fim, final Integer produto, final Integer justificativa, final String obs, OrigemSistemaEnum origemSistemaEnum) throws Exception;

    public int obterNumeroContratosTipoBolsa(Date inicio, Date fim, List<ColaboradorVO> colaboradores, Integer codigoEmpresa) throws Exception;

    public List<PendenciaResumoPessoaRelVO> obterAlunosContratoTipoBolsa(Date inicio, Date fim,
                                                                         List<ColaboradorVO> colaboradores, Integer codigoEmpresa) throws Exception;

    public List<MovPagamentoVO> gerarmovimentocontacorrente(Double valorCredito,
                                                            CancelamentoContratoVO cancelamentoContratoVO, ContratoVO contrato, UsuarioVO usuario, boolean transferenciaDias) throws Exception;
    boolean existeOperacaoParaEstaData(int codigoCliente, String tipoOperacao, Date dataBase, int codigoContrato) throws Exception;

    ContratoOperacaoVO obterOperacaoParaEstaData(String tipoOperacao, Date dataBase, int codigoContrato) throws Exception;

    int obterDiasAfastamentoContratoPorPeriodo(Integer codigo, Date dataInicio, Date dataFinal) throws SQLException;

    List<PeriodoMensal> obterPeriodosParaAfastamentoColetivo(Integer codigo, Date hoje, Date datafinal) throws Exception;

    List consultarAfastamentoColetivoPorContrato(Integer codigoContrato, Date dataOperacao,  int nivelMontarDados) throws Exception;
}

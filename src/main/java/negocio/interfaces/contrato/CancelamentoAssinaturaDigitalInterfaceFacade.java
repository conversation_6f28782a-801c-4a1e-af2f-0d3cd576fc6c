package negocio.interfaces.contrato;

import negocio.comuns.contrato.CancelamentoAssinaturaDigitalVO;
import negocio.interfaces.basico.SuperInterface;

public interface CancelamentoAssinaturaDigitalInterfaceFacade extends SuperInterface {
    
    public void incluir(CancelamentoAssinaturaDigitalVO obj) throws Exception;
    
    public void excluir(CancelamentoAssinaturaDigitalVO obj) throws Exception;
    
    void alterar(CancelamentoAssinaturaDigitalVO obj) throws Exception;

    void excluirPorContrato(Integer contrato) throws Exception;

    CancelamentoAssinaturaDigitalVO consultarPorContratoCancelamento(Integer contrato) throws Exception;

    void excluirAssinaturaPeloCancelamentoContrato(Integer contrato) throws Exception;

    void excluirAssinaturaEletronicaCancelamento(Integer contrato) throws Exception;
}

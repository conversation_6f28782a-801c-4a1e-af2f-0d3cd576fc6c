package negocio.interfaces.contrato;
import java.util.Date;
import negocio.comuns.contrato.ConvenioVO;
import java.util.List;
import negocio.interfaces.basico.SuperInterface;

/**
 * Interface reponsável por criar uma estrutura padrão de comunidação entre a camada de controle
 * e camada de negócio (em especial com a classe Façade). Com a utilização desta interface 
 * é possível substituir tecnologias de uma camada da aplicação com mínimo de impacto nas demais.
 * Além de padronizar as funcionalidades que devem ser disponibilizadas pela camada de negócio, por intermédio
 * de sua classe Façade (responsável por persistir os dados das classes VO).
*/
public interface ConvenioInterfaceFacade extends SuperInterface {
	

    public ConvenioVO novo() throws Exception;
    public void incluir(ConvenioVO obj) throws Exception;
    public void alterar(ConvenioVO obj) throws Exception;
    public void excluir(ConvenioVO obj) throws Exception;
    public ConvenioVO consultarPorChavePrimaria(Integer codigo, int nivelMontarDados) throws Exception;
    public List consultarPorCodigo(Integer valorConsulta, boolean controlarAcesso, int nivelMontarDados) throws Exception;
    public List consultarPorDescricao(String valorConsulta, boolean controlarAcesso, int nivelMontarDados) throws Exception;
    public List consultarPorDataAssinatura(Date prmIni, Date prmFim, boolean controlarAcesso, int nivelMontarDados) throws Exception;
    public void setIdEntidade(String aIdEntidade);
}
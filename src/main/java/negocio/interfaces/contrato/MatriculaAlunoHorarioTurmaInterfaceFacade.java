package negocio.interfaces.contrato;

import br.com.pactosolucoes.agendatotal.json.AgendaTotalJSON;
import br.com.pactosolucoes.agendatotal.json.AgendadoJSON;
import br.com.pactosolucoes.mapaturmas.modelo.AlunoMapaTurmasTO;
import negocio.comuns.arquitetura.UsuarioVO;
import negocio.comuns.basico.AulaDesmarcadaVO;
import negocio.comuns.basico.ClienteVO;
import negocio.comuns.basico.EmpresaVO;
import negocio.comuns.basico.PessoaVO;
import negocio.comuns.contrato.ContratoVO;
import negocio.comuns.contrato.MatriculaAlunoHorarioTurmaVO;
import negocio.comuns.plano.ConsultarAlunosTurmaVO;
import negocio.comuns.plano.HorarioTurmaVO;
import negocio.comuns.plano.ModalidadeVO;
import negocio.comuns.plano.TurmaVO;
import negocio.interfaces.basico.SuperInterface;

import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Set;
import negocio.comuns.basico.AgendaTotalTO;

/**
 * 
 * <AUTHOR>
 */
public interface MatriculaAlunoHorarioTurmaInterfaceFacade extends SuperInterface {

    public void incluir(MatriculaAlunoHorarioTurmaVO obj) throws Exception;

    public void incluirSemComit(MatriculaAlunoHorarioTurmaVO obj) throws Exception;

    public void alterar(MatriculaAlunoHorarioTurmaVO obj) throws Exception;

    public void alterarVigenciaSemCommit(MatriculaAlunoHorarioTurmaVO obj) throws Exception;

    public void alterarSemCommit(MatriculaAlunoHorarioTurmaVO obj) throws Exception;

    public void alterarInicioFimMatricula(MatriculaAlunoHorarioTurmaVO obj) throws Exception;

    public void alterarDataFimMatricula(ContratoVO contratoVO, HorarioTurmaVO horarioTurma, Date dataFimMatriculaPesquisar, Date dataFimMatriculaAlterar)throws Exception;

    public void alterarInicioFimMatriculaSemCommit(MatriculaAlunoHorarioTurmaVO obj) throws Exception;

    public void excluir(MatriculaAlunoHorarioTurmaVO obj) throws Exception;

    public void excluirSemCommit(MatriculaAlunoHorarioTurmaVO obj) throws Exception;

    public MatriculaAlunoHorarioTurmaVO consultarPorChavePrimaria(int codigo, int nivelMontarDados) throws Exception;

    public List<MatriculaAlunoHorarioTurmaVO> consultarPorCodigo(int valorConsulta, boolean controlarAcesso, int nivelMontarDados) throws Exception;

    public List<MatriculaAlunoHorarioTurmaVO> consultarPorCodigo(int valorConsulta, Date dataInicial, Date dataFinal, boolean controlarAcesso, int nivelMontarDados) throws Exception;

    public List<MatriculaAlunoHorarioTurmaVO> consultarPorNomePessoa(String valorConsulta, int nivelMontarDados) throws Exception;

    public List<MatriculaAlunoHorarioTurmaVO> consultarPorNomePessoa(String valorConsulta, Date dataInicial, Date dataFinal, int nivelMontarDados) throws Exception;

    public List<MatriculaAlunoHorarioTurmaVO> consultarPorCodigoContrato(int valorConsulta, int nivelMontarDados) throws Exception;

    public List<MatriculaAlunoHorarioTurmaVO> consultarPorCodigoContrato(int valorConsulta, Date dataInicial, Date dataFinal, int nivelMontarDados, Boolean desconsiderarDataTurmas) throws Exception;
    
    public List<MatriculaAlunoHorarioTurmaVO> consultarPorCodigoContrato(int valorConsulta, int nivelMontarDados, Boolean somarTurmasRetiradasDoContrato,Integer codigoTurma) throws Exception;

    public List<MatriculaAlunoHorarioTurmaVO> consultarPorSituacaoContrato(String valorConsulta, int nivelMontarDados) throws Exception;

    public List<MatriculaAlunoHorarioTurmaVO> consultarPorSituacaoContrato(String valorConsulta, Date dataInicial, Date dataFinal, int nivelMontarDados) throws Exception;

    public List<MatriculaAlunoHorarioTurmaVO> consultarPorIdentificadorTurma(String valorConsulta, int nivelMontarDados) throws Exception;

    public List<MatriculaAlunoHorarioTurmaVO> consultarPorIdentificadorTurma(String valorConsulta, Date dataInicial, Date dataFinal, int nivelMontarDados) throws Exception;

    public MatriculaAlunoHorarioTurmaVO consultarPorEmpresaPessoaContratoHorarioTurma(int empresa, int pessoa, int contrato, int horarioturma, int nivelMontarDados) throws Exception;

    public MatriculaAlunoHorarioTurmaVO consultarPorEmpresaPessoaContratoHorarioTurma(int empresa, int pessoa, int contrato, int horarioturma, Date dataInicial, Date dataFinal, int nivelMontarDados) throws Exception;

    public List<MatriculaAlunoHorarioTurmaVO> consultarPorHorarioTurma(int valorConsulta, boolean controlarAcesso, int nivelMontarDados) throws Exception;

    public List<MatriculaAlunoHorarioTurmaVO> consultarPorHorarioTurma(int valorConsulta, Date dataInicial, Date dataFinal, boolean controlarAcesso, int nivelMontarDados) throws Exception;

    public List<MatriculaAlunoHorarioTurmaVO> consultarPorEmpresaContratoModalidade(int empresa, int contrato, int modalidade, int nivelMontarDados) throws Exception;

    public List<MatriculaAlunoHorarioTurmaVO> consultarPorEmpresaContratoModalidade(int empresa, int contrato, int modalidade, Date dataInicial, Date dataFinal, int nivelMontarDados) throws Exception;

    public List<ConsultarAlunosTurmaVO> consultarPorHorarioTurma(int horarioTurma, int nivelMontarDados) throws Exception;

    public List<ConsultarAlunosTurmaVO> consultarPorHorarioTurmaPeriodo(int horarioTurma,
                                                                        int nivelMontarDados, final Date dataInicio, final Date dataFim, boolean frenquecia, boolean consultarReposicoes) throws Exception;

    public List<MatriculaAlunoHorarioTurmaVO> consultarPorHorariosTurma(String horariosTurma, int nivelMontarDados, final Date inicio ,final Date fim) throws Exception;

    public List<MatriculaAlunoHorarioTurmaVO> consultarMatriculaAtiva(int contrato, Date vigenciaAtual) throws Exception;

    public MatriculaAlunoHorarioTurmaVO consultarMatriculaAtivaPorHorarioTurma(int contrato, int horario, Date vigenciaAtual, int nivelMontarDados) throws Exception;

    MatriculaAlunoHorarioTurmaVO consultarMatriculaAtivaPorHorarioTurma(final PessoaVO pessoaVO, final int horario, final Date vigenciaAtual, final int nivelMontarDados) throws Exception;

    public Long consultarPorHorarioTurmaPorPeriodoCount(int valorConsulta, Date dataInicial, Date dataFinal, boolean controlarAcesso) throws Exception;

    public Long consultarPorHorarioTurmaCount(int horarioTurma, boolean controlarAcesso) throws Exception;
    
    public Integer consultarPorHorarioTurmaACCount(int horarioTurma) throws Exception;

    public void matricularAlunos() throws Exception;

    public void preencherReposicoesFormatoMatriculaAlunoHorarioTurma(final int empresa,
            final int turma,
            final int colaborador, List<MatriculaAlunoHorarioTurmaVO> matriculas,
            final Date dataInicio, final Date dataFim,List<String> filtroHorarios, List<String> filtrodiasSemana) throws Exception;

    public void preencherReposicoesFormatoMatriculaAlunoHorarioTurma(
            final String horarios,
            List<MatriculaAlunoHorarioTurmaVO> matriculas,
            final Date dataInicio, final Date dataFim) throws Exception;

    public List<MatriculaAlunoHorarioTurmaVO> consultarPorContratos(Set<Integer> contratos) throws Exception;

    public void matricularAlunosQueNaoPossuemHistorico() throws Exception;
    
    public List<AlunoMapaTurmasTO> consultarPorHorarioTurmaPeriodoMapaTurmas(int horarioTurma,
            final Date dataInicio, final Date dataFim) throws Exception;

    public List<AlunoMapaTurmasTO> consultarReposicoesPorHorarioTurmaPeriodoMapaTurmas(int horarioTurma,
                                                                                       final Date dataInicio, final Date dataFim) throws Exception;

    boolean alunoDesmarcouAula(Integer cliente, int horarioTurma, final Date dataInicio, final Date dataFim) throws Exception;

    public boolean validarTemTurmaNoPeriodoOuNaoTemTurmaNenhuma(final Date inicio, final Date fim,
            final int contrato, final int modalidade) throws Exception;
    
    public List<MatriculaAlunoHorarioTurmaVO> consultarMatriculaAtivaPorPessoa(int pessoa, int empresa, Date dataPesquisa, int nivelMontarDados) throws Exception ;

    List<MatriculaAlunoHorarioTurmaVO> consultarMatriculaAtivaPorContrato(int contrato, int empresa, Date dataPesquisa, int nivelMontarDados) throws Exception ;
    
    public List<AgendadoJSON> consultarHorariosTurmaParaAgenda(Date inicio, Integer empresa) throws Exception;
    
    public String obterIdentificadorProximaAulaModalidade(Integer cliente, Integer contrato, Integer modalidade)throws Exception;
    
    public List<AgendaTotalJSON> consultarProximasAulasModalidadesDiferentes(Integer matricula, Map<Date, List<Integer>> mapaDesmarcados)throws Exception;
    
    public List<AgendaTotalJSON> consultarAulasProximos30dias(Integer matricula, Date data, Map<Date, List<Integer>> mapaDesmarcados, Integer saldo)throws Exception;
    
    public List<AgendaTotalJSON> consultarAulasDesmarcadasSemReposicao(Integer matricula, Date inicio, Date fim, Integer modalidade)throws Exception;
    
    public List<AgendaTotalJSON> consultarTurmasAluno(Integer matricula)throws Exception;
    
    public List<AgendaTotalJSON> consultarProximasAulasAulaCheia(Integer matricula)throws Exception;
    
    public List<AgendaTotalJSON> consultarAulasDia(Integer matricula, Date agora, 
            List<AgendadoJSON> reposicoesOrigemDia, Integer modalidade, Integer tipoModalidade) throws Exception;

    public Date pesquisarDataFimMatriculaTurmaCreditoTreino(Integer codigoContrato, Integer codigoHorarioTurma)throws Exception;

    public List<MatriculaAlunoHorarioTurmaVO> consultarMatriculaPorDataFim(int contrato, Date dataFim) throws Exception;

    public List<HorarioTurmaVO>consultarAulasPrevistas(Integer codigoContrato)throws Exception;
    
    public List<MatriculaAlunoHorarioTurmaVO> consultarMatriculasPorMaxDataFim(int contrato) throws Exception;
    
    public boolean existeMatriculaVigenteNoHorario(final Date dataverificar,
            final int contrato, final int horarioTurma) throws Exception;
     public List<AgendaTotalTO> consultarAulas(Integer matricula, Date data, Map<Date, List<Integer>> mapaDesmarcados,Integer contrato,boolean habilitarSomaDeAulaNaoVigente,List<MatriculaAlunoHorarioTurmaVO> listaMatriculaHorarioTurma)throws Exception;

    void inativarMatriculaAlunosHorarioTurmas() throws Exception;

    List<ConsultarAlunosTurmaVO> consultarPorHorarioTurmaPeriodoGestaoTurma(List<HorarioTurmaVO> listaHorarioTurma,
                                                                                   int nivelMontarDados, final Date dataInicio, final Date dataFim, boolean frequencia, boolean consultarReposicoes) throws Exception;

    String transferirAlunos(List<ConsultarAlunosTurmaVO> listaTransferir, Integer horarioTurmaDestino, UsuarioVO usuarioVO, boolean comitar) throws Exception;


    List<ClienteVO> contarEvasaoPorVariasModalidadeNoPeriodo(Date dataInicio, Date dataFim, String modalidades, EmpresaVO empresaVO, TurmaVO turma, Integer vezesSemana) throws Exception;

    List<ConsultarAlunosTurmaVO> contarEntradaPorVariasModalidadeNoPeriodo(Date dataInicio, Date dataFim, String modalidades, Integer codEmpresa, TurmaVO turma, Integer vezesSemana) throws Exception;

    List<ConsultarAlunosTurmaVO> contarPresencaPorVariasModalidadeNoPeriodo(Date dataInicio, Date dataFim, String modalidades, Integer codEmpresa, TurmaVO turma, Integer vezesSemana) throws Exception;

    List<ConsultarAlunosTurmaVO> contarPresencaAnoPorVariasModalidadeNoPeriodo(Date dataInicio, Date dataFim, String modalidades, Integer codEmpresa, TurmaVO turma, Integer vezesSemana) throws Exception;

}

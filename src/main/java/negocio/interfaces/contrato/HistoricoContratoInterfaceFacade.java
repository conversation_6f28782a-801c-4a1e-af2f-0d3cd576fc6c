/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */

package negocio.interfaces.contrato;

import negocio.comuns.contrato.ContratoVO;
import negocio.comuns.contrato.HistoricoContratoVO;
import negocio.interfaces.basico.SuperInterface;

import java.util.Date;
import java.util.List;

/**
 *
 * <AUTHOR>
 */
public interface HistoricoContratoInterfaceFacade extends SuperInterface{

    void alterar(HistoricoContratoVO obj) throws Exception;

    void alterar(HistoricoContratoVO obj, Boolean controleAcesso) throws Exception;

    /**
     * Operação responsável por alterar todos os objetos da <code>HistoricoContratoVO</code> contidos em um Hashtable no BD.
     * Faz uso da operação <code>excluirHistoricoContratos</code> e <code>incluirHistoricoContratos</code> disponíveis na classe <code>HistoricoContrato</code>.
     * @param objetos  List com os objetos a serem alterados ou incluídos no BD.
     * @exception Exception  Erro de conexão com o BD ou restrição de acesso a esta operação.
     */
    void alterarHistoricoContratos(Integer contrato, List objetos) throws Exception;

    /**
     * Operação responsável por alterar no BD os dados de um objeto da classe <code>HistoricoContratoVO</code>.
     * Sempre utiliza a chave primária da classe como atributo para localização do registro a ser alterado.
     * Primeiramente valida os dados (<code>validarDados</code>) do objeto. Verifica a conexão com o banco de dados e a permissão do usuário
     * para realizar esta operacão na entidade.
     * Isto, através da operação <code>alterar</code> da superclasse.
     * @param obj    Objeto da classe <code>HistoricoContratoVO</code> que será alterada no banco de dados.
     * @exception Execption Caso haja problemas de conexão, restrição de acesso ou validação de dados.
     */
    void alterarSemCommit(HistoricoContratoVO obj, Boolean controleAcesso) throws Exception;

    /**
     * Operação responsável por consultar todos os <code>HistoricoContratoVO</code> relacionados a um objeto da classe <code>contrato.Contrato</code>.
     * @param contrato  Atributo de <code>contrato.Contrato</code> a ser utilizado para localizar os objetos da classe <code>HistoricoContratoVO</code>.
     * @return List  Contendo todos os objetos da classe <code>HistoricoContratoVO</code> resultantes da consulta.
     * @exception Exception  Erro de conexão com o BD ou restrição de acesso a esta operação.
     */
    List consultarHistoricoContratos(Integer contrato, int nivelMontarDados) throws Exception;

    /**
     * Operação responsável por localizar um objeto da classe <code>HistoricoContratoVO</code>
     * através de sua chave primária.
     * @exception Exception Caso haja problemas de conexão ou localização do objeto procurado.
     */
    HistoricoContratoVO consultarPorChavePrimaria(Integer codigoPrm, int nivelMontarDados) throws Exception;

    /**
     * Responsável por realizar uma consulta de <code>HistoricoContrato</code> através do valor do atributo
     * <code>Integer codigo</code>. Retorna os objetos com valores iguais ou superiores ao parâmetro fornecido.
     * Faz uso da operação <code>montarDadosConsulta</code> que realiza o trabalho de prerarar o List resultante.
     * @param   controlarAcesso Indica se a aplicação deverá verificar se o usuário possui permissão para esta consulta ou não.
     * @return  List Contendo vários objetos da classe <code>HistoricoContratoVO</code> resultantes da consulta.
     * @exception Exception Caso haja problemas de conexão ou restrição de acesso.
     */
    List consultarPorCodigo(Integer valorConsulta, boolean controlarAcesso, int nivelMontarDados) throws Exception;

    /**
     * Responsável por realizar uma consulta de <code>ContratoOperacao</code> através do valor do atributo
     * <code>Integer contrato</code>. Retorna os objetos com valores iguais ou superiores ao parâmetro fornecido.
     * Faz uso da operação <code>montarDadosConsulta</code> que realiza o trabalho de prerarar o List resultante.
     * @param   controlarAcesso Indica se a aplicação deverá verificar se o usuário possui permissão para esta consulta ou não.
     * @return  List Contendo vários objetos da classe <code>ContratoOperacaoVO</code> resultantes da consulta.
     * @exception Exception Caso haja problemas de conexão ou restrição de acesso.
     */
    List<HistoricoContratoVO> consultarPorContrato(Integer valorConsulta, boolean controlarAcesso, int nivelMontarDados) throws Exception;

    /**
     * Responsável por realizar uma consulta de <code>ContratoOperacao</code> através do valor do atributo
     * <code>Integer contrato</code>. Retorna os objetos com valores iguais ou superiores ao parâmetro fornecido.
     * Faz uso da operação <code>montarDadosConsulta</code> que realiza o trabalho de prerarar o List resultante.
     * @param   controlarAcesso Indica se a aplicação deverá verificar se o usuário possui permissão para esta consulta ou não.
     * @return  List Contendo vários objetos da classe <code>ContratoOperacaoVO</code> resultantes da consulta.
     * @exception Exception Caso haja problemas de conexão ou restrição de acesso.
     */
    List consultarPorContratoDescricao(Integer valorConsulta, String descricao, int nivelMontarDados) throws Exception;

    /**
     * Responsável por realizar uma consulta de <code>HistoricoContrato</code> através do valor do atributo
     * <code>Date dataRegistro</code>. Retorna os objetos com valores pertecentes ao período informado por parâmetro.
     * Faz uso da operação <code>montarDadosConsulta</code> que realiza o trabalho de prerarar o List resultante.
     * @param   controlarAcesso Indica se a aplicação deverá verificar se o usuário possui permissão para esta consulta ou não.
     * @return  List Contendo vários objetos da classe <code>HistoricoContratoVO</code> resultantes da consulta.
     * @exception Exception Caso haja problemas de conexão ou restrição de acesso.
     */
    List consultarPorDataInicio(Date prmIni, Integer contrato, int nivelMontarDados) throws Exception;

    /**
     * Responsável por realizar uma consulta de <code>HistoricoContrato</code> através do valor do atributo
     * <code>Date dataRegistro</code>. Retorna os objetos com valores pertecentes ao período informado por parâmetro.
     * Faz uso da operação <code>montarDadosConsulta</code> que realiza o trabalho de prerarar o List resultante.
     * @param   controlarAcesso Indica se a aplicação deverá verificar se o usuário possui permissão para esta consulta ou não.
     * @return  List Contendo vários objetos da classe <code>HistoricoContratoVO</code> resultantes da consulta.
     * @exception Exception Caso haja problemas de conexão ou restrição de acesso.
     */
    List consultarPorDataRegistro(Date prmIni, Date prmFim, boolean controlarAcesso, int nivelMontarDados) throws Exception;

    /**
     * Responsável por realizar uma consulta de <code>HistoricoContrato</code> através do valor do atributo
     * <code>situacao</code> da classe <code>Colaborador</code>
     * Faz uso da operação <code>montarDadosConsulta</code> que realiza o trabalho de prerarar o List resultante.
     * @return  List Contendo vários objetos da classe <code>HistoricoContratoVO</code> resultantes da consulta.
     * @exception Execption Caso haja problemas de conexão ou restrição de acesso.
     */
    List consultarPorSituacaoColaborador(String valorConsulta, int nivelMontarDados) throws Exception;

    /**
     * Responsável por realizar uma consulta de <code>HistoricoContrato</code> através do valor do atributo
     * <code>situacao</code> da classe <code>Contrato</code>
     * Faz uso da operação <code>montarDadosConsulta</code> que realiza o trabalho de prerarar o List resultante.
     * @return  List Contendo vários objetos da classe <code>HistoricoContratoVO</code> resultantes da consulta.
     * @exception Execption Caso haja problemas de conexão ou restrição de acesso.
     */
    List consultarPorSituacaoContrato(String valorConsulta, int nivelMontarDados) throws Exception;

    /**
     * Responsável por realizar uma consulta de <code>HistoricoContrato</code> através do valor do atributo
     * <code>String situacaoRelativaHistorico</code>. Retorna os objetos, com início do valor do atributo idêntico ao parâmetro fornecido.
     * Faz uso da operação <code>montarDadosConsulta</code> que realiza o trabalho de prerarar o List resultante.
     * @param   controlarAcesso Indica se a aplicação deverá verificar se o usuário possui permissão para esta consulta ou não.
     * @return  List Contendo vários objetos da classe <code>HistoricoContratoVO</code> resultantes da consulta.
     * @exception Exception Caso haja problemas de conexão ou restrição de acesso.
     */
    List consultarPorSituacaoRelativaHistorico(String valorConsulta, boolean controlarAcesso, int nivelMontarDados) throws Exception;

    Integer consultarQuantidadeHistoricoContratoPorSituacao(Date dataInicio, Date dataFim, String situacao, Integer empresa) throws Exception;

    /**
     * Operação responsável por excluir no BD um objeto da classe <code>HistoricoContratoVO</code>.
     * Sempre localiza o registro a ser excluído através da chave primária da entidade.
     * Primeiramente verifica a conexão com o banco de dados e a permissão do usuário
     * para realizar esta operacão na entidade.
     * Isto, através da operação <code>excluir</code> da superclasse.
     * @param obj    Objeto da classe <code>HistoricoContratoVO</code> que será removido no banco de dados.
     * @exception Execption Caso haja problemas de conexão ou restrição de acesso.
     */
    void excluir(HistoricoContratoVO obj) throws Exception;

    /**
     * Operação responsável por excluir todos os objetos da <code>HistoricoContratoVO</code> no BD.
     * Faz uso da operação <code>excluir</code> disponível na classe <code>HistoricoContrato</code>.
     * @param <code>contrato</code> campo chave para exclusão dos objetos no BD.
     * @exception Exception  Erro de conexão com o BD ou restrição de acesso a esta operação.
     */
    void excluirHistoricoContratos(Integer contrato) throws Exception;

    void incluir(HistoricoContratoVO obj, Boolean controleAcesso) throws Exception;

    /**
     * Operação responsável por incluir objetos da <code>HistoricoContratoVO</code> no BD.
     * Garantindo o relacionamento com a entidade principal <code>contrato.Contrato</code> através do atributo de vínculo.
     * @param objetos List contendo os objetos a serem gravados no BD da classe.
     * @exception Exception  Erro de conexão com o BD ou restrição de acesso a esta operação.
     */
    void incluirHistoricoContratos(Integer contratoPrm, List objetos) throws Exception;

    /**
     * Operação responsável por incluir no banco de dados um objeto da classe <code>HistoricoContratoVO</code>.
     * Primeiramente valida os dados (<code>validarDados</code>) do objeto. Verifica a conexão com o banco de dados e a permissão do usuário
     * para realizar esta operacão na entidade.
     * Isto, através da operação <code>incluir</code> da superclasse.
     * @param obj  Objeto da classe <code>HistoricoContratoVO</code> que será gravado no banco de dados.
     * @exception Exception Caso haja problemas de conexão, restrição de acesso ou validação de dados.
     */
    void incluirSemCommit(HistoricoContratoVO obj, Boolean controleAcesso) throws Exception;

    /**
     * Operação responsável por retornar um novo objeto da classe <code>HistoricoContratoVO</code>.
     */
    HistoricoContratoVO novo() throws Exception;

    /**
     * Operação responsável por obter o último valor gerado para uma chave primária.
     * É utilizada para obter o valor gerado pela SGBD para uma chave primária,
     * a apresentação do mesmo e a implementação de possíveis relacionamentos.
     */
    HistoricoContratoVO obterHistoricoContratoLancadoFuturoPorCodigoContratoDataInicioDataFim(Integer contrato, Date data, int nivelMontarDados) throws Exception;

    /**
     * Operação responsável por obter o último valor gerado para uma chave primária.
     * É utilizada para obter o valor gerado pela SGBD para uma chave primária,
     * a apresentação do mesmo e a implementação de possíveis relacionamentos.
     */
    HistoricoContratoVO obterHistoricoContratoPorCodigoContratoDataInicioDataFim(Integer contrato, Date data, int nivelMontarDados) throws Exception;

    boolean obterHistoricoContratoPorCodigoContratoDecricao(Integer contrato, String descricao, int nivelMontarDados) throws Exception;

    void excluirHistoricosFuturosContrato (Integer contrato, Date data) throws Exception;

    Boolean obterHistoricoContratoPorCodigoContratoDecricaoDataInicio(Integer contrato, String descricao, Date data, int nivelMontarDados) throws Exception;

    Boolean obterHistoricoContratoPorCodigoContratoTipoHistorico(Integer contrato, String tipoHistorico) throws Exception;

    Boolean obterHistoricoContratoPorCodigoContratoTipoHistoricoDataInicioDataFim(Integer contrato, String tipoHistorico, Date data, int nivelMontarDados) throws Exception;

    /**
     * Operação responsável por obter o último valor gerado para uma chave primária.
     * É utilizada para obter o valor gerado pela SGBD para uma chave primária,
     * a apresentação do mesmo e a implementação de possíveis relacionamentos.
     */
    HistoricoContratoVO obterHistoricoContratoPorDataEspecifica(Integer contrato, Date data, int nivelMontarDados) throws Exception;
    List <HistoricoContratoVO> obterHistoricoContratoFuturo(Integer contrato, Date data, int nivelMontarDados) throws Exception;
    /**
     * Operação responsável por obter o último valor gerado para uma chave primária.
     * É utilizada para obter o valor gerado pela SGBD para uma chave primária,
     * a apresentação do mesmo e a implementação de possíveis relacionamentos.
     */
    HistoricoContratoVO obterHistoricoContratoPorDescricao(Integer contrato, String descricao, int nivelMontarDados) throws Exception;

    /**
     * Operação responsável por localizar um objeto da classe <code>HistoricoContratoVO</code>
     * através de sua chave primária.
     * @exception Exception Caso haja problemas de conexão ou localização do objeto procurado.
     */
    HistoricoContratoVO obterUltimaHistoricoContratoDoMes(Integer codigoPrm, Integer mes, Integer ano, int nivelMontarDados) throws Exception;

    /**
     * Operação responsável por obter o último valor gerado para uma chave primária.
     * É utilizada para obter o valor gerado pela SGBD para uma chave primária,
     * a apresentação do mesmo e a implementação de possíveis relacionamentos.
     */
    HistoricoContratoVO obterUltimoHistoricoContratoPorContrato(Integer contrato,
            Date dataBase, int nivelMontarDados) throws Exception;

    /**
     * Operação responsável por obter o último valor gerado para uma chave primária.
     * É utilizada para obter o valor gerado pela SGBD para uma chave primária,
     * a apresentação do mesmo e a implementação de possíveis relacionamentos.
     */
    HistoricoContratoVO obterUltimoHistoricoContratoPorContratoTipoHistorico(Integer contrato, String tipoHistorico, int nivelMontarDados) throws Exception;
    
    public HistoricoContratoVO obterUltimoHistoricoContratoPorContrato(Integer contrato, int nivelMontarDados) throws Exception;
    
    /**
     * Responsável por excluir histórico 'a vencer' de um contrato. Originalmente criado para o cadastro de trancamento. 
     * <AUTHOR> Alcides
     * 07/08/2012
     */
    public void excluirHistoricoAVencerContrato(Integer contrato) throws Exception;
    
    public List<HistoricoContratoVO> consultarPorContratoSimplificado(Integer valorConsulta) throws Exception;
    
    public Date obterDataInicioUltimoHistoricoOperacaoContrato(Integer contrato) throws Exception;
    public List<HistoricoContratoVO> obterHistoricosContratoPorDataEspecifica(Integer contrato, Date data, int nivelMontarDados) throws Exception;

    public List<Integer> obterListaTodosHistoricoContrato() throws Exception;

    public void incluirPrimeiroBaseadoSituacaoContrato(ContratoVO contratoVO, boolean podePossuirHistorico) throws Exception;
}

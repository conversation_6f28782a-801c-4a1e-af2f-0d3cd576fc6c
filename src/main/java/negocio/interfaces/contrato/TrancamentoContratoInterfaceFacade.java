/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */

package negocio.interfaces.contrato;

import negocio.comuns.arquitetura.UsuarioVO;
import negocio.comuns.contrato.TrancamentoContratoVO;
import negocio.interfaces.basico.SuperInterface;

import java.util.List;

/**
 *
 * <AUTHOR>
 */
public interface TrancamentoContratoInterfaceFacade extends SuperInterface {

    /**
 * Interface reponsável por criar uma estrutura padrão de comunidação entre a camada de controle
 * e camada de negócio (em especial com a classe Façade). Com a utilização desta interface
 * é possível substituir tecnologias de uma camada da aplicação com mínimo de impacto nas demais.
 * Além de padronizar as funcionalidades que devem ser disponibilizadas pela camada de negócio, por intermédio
 * de sua classe Façade (responsável por persistir os dados das classes VO).
*/

    public TrancamentoContratoVO novo() throws Exception;
    public void incluir(TrancamentoContratoVO obj, UsuarioVO usuarioVO) throws Exception;
    public void incluirRetorno(TrancamentoContratoVO obj) throws Exception;
    public void alterar(TrancamentoContratoVO obj) throws Exception;
    public void excluir(TrancamentoContratoVO obj) throws Exception;
    public TrancamentoContratoVO consultarPorChavePrimaria(Integer codigo, int nivelMontarDados) throws Exception;
    public List consultarPorCodigoContrato(Integer valorConsulta, boolean controlarAcesso, int nivelMontarDados) throws Exception;
    public List consultarPorCodigo(Integer valorConsulta, boolean controlarAcesso, int nivelMontarDados) throws Exception;
    public TrancamentoContratoVO obterUltimoDiaRetornoContrato(Integer contrato, int nivelMontarDados) throws Exception;
    public void setIdEntidade(String aIdEntidade);
}

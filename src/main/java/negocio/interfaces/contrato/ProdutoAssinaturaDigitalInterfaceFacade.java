package negocio.interfaces.contrato;

import negocio.comuns.contrato.ProdutoAssinaturaDigitalVO;
import negocio.comuns.financeiro.ProdutoTextoPadraoVO;
import negocio.interfaces.basico.SuperInterface;

import java.util.List;

public interface ProdutoAssinaturaDigitalInterfaceFacade extends SuperInterface {

    void incluir(ProdutoAssinaturaDigitalVO obj) throws Exception;

    void excluir(ProdutoAssinaturaDigitalVO obj) throws Exception;

    void alterar(ProdutoAssinaturaDigitalVO obj) throws Exception;

    List<ProdutoTextoPadraoVO> consultarContratosProdutos(boolean assinados, String filtro, Integer empresa, boolean todos) throws Exception;

    Integer countContratosProdutos(boolean assinados, String filtro, Integer empresa) throws Exception;

    ProdutoAssinaturaDigitalVO consultarPorContratoProduto(Integer contrato) throws Exception;

    void excluirAssinaturaProdutoContrato(Integer contrato) throws Exception;
}

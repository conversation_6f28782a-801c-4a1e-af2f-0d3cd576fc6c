package negocio.interfaces.contrato;

import negocio.comuns.basico.EmpresaVO;
import negocio.comuns.contrato.UtilizacaoAvaliacaoFisicaVO;
import negocio.interfaces.basico.SuperInterface;

import java.sql.SQLException;
import java.util.Date;
import java.util.List;

public interface UtilizacaoAvaliacaoFisicaInterfaceFacade extends SuperInterface {

    void gravar(UtilizacaoAvaliacaoFisicaVO utilizacao) throws Exception;

    void incluir(UtilizacaoAvaliacaoFisicaVO utilizacao) throws Exception;

    void alterar(UtilizacaoAvaliacaoFisicaVO utilizacao) throws SQLException;

    void excluirPorCodAvaliacaoFisica(Integer codAvaliacaoFisica) throws SQLException;

    UtilizacaoAvaliacaoFisicaVO findByCodAvaliacao(Integer codAvaliacao) throws SQLException;

    List<UtilizacaoAvaliacaoFisicaVO> findByMovproduto(Integer codMovproduto) throws SQLException;

    List<UtilizacaoAvaliacaoFisicaVO> findByDataPrimeiraUtilizacao(Date dataInicio, Date dataFim, EmpresaVO empresa) throws Exception;
}

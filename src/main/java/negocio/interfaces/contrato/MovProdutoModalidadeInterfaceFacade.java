package negocio.interfaces.contrato;

import java.sql.SQLException;
import java.util.Date;
import java.util.List;

import negocio.comuns.contrato.ContratoModalidadeVO;
import negocio.comuns.contrato.ContratoVO;
import negocio.comuns.contrato.MovProdutoModalidadeVO;
import negocio.comuns.contrato.MovProdutoVO;
import negocio.interfaces.basico.SuperInterface;

public interface MovProdutoModalidadeInterfaceFacade  extends SuperInterface {
	
	public void incluir(MovProdutoModalidadeVO obj) throws Exception;
	
	public void incluir(MovProdutoVO obj) throws Exception;

    public void alterar(MovProdutoModalidadeVO obj) throws Exception;

    public void excluirPorMovProduto(MovProdutoVO obj, List<ContratoModalidadeVO> modalidade) throws Exception;

    public MovProdutoModalidadeVO consultarPorChavePrimaria(Integer codigo, int nivelMontarDados) throws Exception;
    
    public List<MovProdutoModalidadeVO> consultarPorMovProduto(Integer movProduto, int nivelMontarDados) throws Exception;
    
    public void atualizarDataFimUsoModalidades(ContratoVO contrato, Date dataFim, List<ContratoModalidadeVO> modalidades) throws Exception;
    
    public void atualizarValorModalidadesNoProduto(Double totalDasModalidades, Double valorADividir, MovProdutoVO produto, List<ContratoModalidadeVO> modalidades) throws Exception;
    
    public void realizarManutencaoMovProdutoModalidade(List<ContratoModalidadeVO> modalidadesAdicionadas, List<ContratoModalidadeVO> modalidadesExcluidas,
			ContratoVO contratoNovo, ContratoVO contratoAntigo) throws Exception;

    public boolean validarExisteMovProdModalidade(int contrato) throws SQLException;

    public boolean selectMigradorMOVProdutoModalidade() throws SQLException;
}

package negocio.interfaces.contrato;

import org.json.JSONArray;
import negocio.comuns.basico.ArquivoVO;
import negocio.comuns.basico.PessoaVO;
import negocio.comuns.contrato.AtestadoVO;
import negocio.comuns.financeiro.VendaAvulsaVO;
import negocio.interfaces.basico.SuperInterface;

/**
 * Created by glauco on 04/06/2014.
 */
public interface AtestadoInterfaceFacade extends SuperInterface {


    public void incluir(AtestadoVO atestadoVO) throws Exception;

    public void alterar(AtestadoVO atestadoVO) throws Exception;

    public AtestadoVO consultarPorMovProduto(Integer codMovProduto, int nivelMontarDados) throws Exception;

    public AtestadoVO consultarUltimoAtestado(PessoaVO pessoa) throws Exception;

    public JSONArray consultarAtestadoTreino(String key,Integer codigoCliente) throws Exception;

    AtestadoVO incluirAtestado(VendaAvulsaVO vendaAvulsaVO, ArquivoVO arquivoVO, String observacao, boolean parqPositivo, Integer avaliacaoFisicaTW) throws Exception;
}

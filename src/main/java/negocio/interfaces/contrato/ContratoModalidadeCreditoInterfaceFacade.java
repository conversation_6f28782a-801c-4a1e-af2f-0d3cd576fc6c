/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */

package negocio.interfaces.contrato;

import negocio.comuns.contrato.ContratoModalidadeCreditoVO;
import negocio.comuns.contrato.ContratoModalidadeVO;
import negocio.interfaces.basico.SuperInterface;

import java.util.List;

public interface ContratoModalidadeCreditoInterfaceFacade extends SuperInterface {

    void incluir(ContratoModalidadeCreditoVO obj) throws Exception;

    void alterar(ContratoModalidadeCreditoVO obj) throws Exception;

    void excluir(ContratoModalidadeCreditoVO obj) throws Exception;

    ContratoModalidadeCreditoVO consultarPorChavePrimaria(Integer codigo, int nivelMontarDados) throws Exception;

    ContratoModalidadeCreditoVO consultarPorContratoModalidade(Integer contratoModalidade, int nivelMontarDados) throws Exception;

    void incluirContratoModalidadeCredito(ContratoModalidadeVO contratoModalidadeVO);

    List<ContratoModalidadeCreditoVO> consultarPorCodigoContrato(Integer contrato, int nivelMontarDados) throws Exception;

    boolean contratoVendaCreditoSessao(Integer contrato);
}

package negocio.interfaces.contrato;

import negocio.comuns.contrato.JustificativaOperacaoVO;
import negocio.interfaces.basico.SuperInterface;

import java.sql.ResultSet;
import java.util.List;

/**
 * Interface reponsável por criar uma estrutura padrão de comunidação entre a camada de controle
 * e camada de negócio (em especial com a classe Façade). Com a utilização desta interface
 * é possível substituir tecnologias de uma camada da aplicação com mínimo de impacto nas demais.
 * Além de padronizar as funcionalidades que devem ser disponibilizadas pela camada de negócio, por intermédio
 * de sua classe Façade (responsável por persistir os dados das classes VO).
 */
public interface JustificativaOperacaoInterfaceFacade extends SuperInterface {

    JustificativaOperacaoVO novo() throws Exception;

     void incluir(JustificativaOperacaoVO obj) throws Exception;

     void incluirSemPermissao(JustificativaOperacaoVO obj) throws Exception;

     void alterar(JustificativaOperacaoVO obj) throws Exception;

     void alterarSemCommit(JustificativaOperacaoVO obj) throws Exception;

     void excluir(JustificativaOperacaoVO obj) throws Exception;

     JustificativaOperacaoVO consultarPorChavePrimaria(Integer codigo, int nivelMontarDados) throws Exception;

     List<JustificativaOperacaoVO> consultarPorCodigo(Integer valorConsulta, Integer empresa, boolean controlarAcesso, int nivelMontarDados) throws Exception;

     List<JustificativaOperacaoVO> consultarPorTipoOperacao(String valorConsulta, Integer empresa, boolean controlarAcesso, int nivelMontarDados) throws Exception;

     List<JustificativaOperacaoVO> consultarPorNomeEmpresa(String valorConsulta, Integer empresa, int nivelMontarDados) throws Exception;

     /**
      * Obter justificativa padrão para cancelamento de contrato ao tranferir aluno entre empresa.
      * Veja mais detalhes em: https://app.assembla.com/spaces/plataforma-zw/tickets/12368-transfer%C3%AAncia-de-unidade-migrar-parcelas-aberto---just-fit---maurin/details
      *
      * @param empresa     código da emrpesa
      * @return
      * @throws Exception
      */
     JustificativaOperacaoVO obterJustificativaCancelamentoTransferenciaEmpresa(int empresa, boolean transferenciaDiasContrato) throws Exception;

     List<JustificativaOperacaoVO> consultarPorDescricao(String valorConsulta, Integer empresa, boolean controlarAcesso, int nivelMontarDados) throws Exception;

     void setIdEntidade(String aIdEntidade);

     JustificativaOperacaoVO criarOuConsultarSeExistePorNome(JustificativaOperacaoVO obj) throws Exception;

     String consultarJSON(Integer empresa) throws Exception;

     List<JustificativaOperacaoVO> consultarParaImpressao(String filtro, String ordem, String campoOrdenacao, Integer empresa) throws Exception;

     JustificativaOperacaoVO consultarOperacaoRenovacaoAntecipadaCreditoTreino(Integer codigoEmpresa)throws Exception;

     /**
      *
      * @param empresa    0 para consultar de todas as empresas
      * @return
      * @throws Exception
      */
     ResultSet consultarJustificativaContratoCanceladoDeOutraUnidade(Integer empresa) throws Exception;

    /**
     *
     * @param empresa   0 para consultar de todas as empresas
     * @return
     * @throws Exception
     */
     ResultSet consultarTotalJustificativaContratoCanceladoDeOutraUnidade(Integer empresa)  throws Exception;
     
     public JustificativaOperacaoVO consultarOperacaoCreditoTreino(Integer codigoEmpresa, String justificativa)throws Exception;

     public boolean isUtiliza(Integer empresa, Integer justificativaoperacao) throws Exception;

     JustificativaOperacaoVO obterJustificativaCancelamentoMudancaPlano(int codigoEmpresa) throws Exception;
}

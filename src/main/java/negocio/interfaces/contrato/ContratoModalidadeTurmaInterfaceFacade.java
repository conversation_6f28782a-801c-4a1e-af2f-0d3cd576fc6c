package negocio.interfaces.contrato;

import negocio.comuns.contrato.ContratoModalidadeTurmaVO;
import java.util.List;
import negocio.interfaces.basico.SuperInterface;

/**
 * Interface reponsável por criar uma estrutura padrão de comunidação entre a camada de controle
 * e camada de negócio (em especial com a classe Façade). Com a utilização desta interface 
 * é possível substituir tecnologias de uma camada da aplicação com mínimo de impacto nas demais.
 * Além de padronizar as funcionalidades que devem ser disponibilizadas pela camada de negócio, por intermédio
 * de sua classe Façade (responsável por persistir os dados das classes VO).
 */
public interface ContratoModalidadeTurmaInterfaceFacade extends SuperInterface {

    public ContratoModalidadeTurmaVO novo() throws Exception;

    public void alterar(ContratoModalidadeTurmaVO obj) throws Exception;

    public void excluir(ContratoModalidadeTurmaVO obj) throws Exception;

    public ContratoModalidadeTurmaVO consultarPorChavePrimaria(Integer codigo, int nivelMontarDados) throws Exception;

    public List consultarPorCodigo(Integer valorConsulta, boolean controlarAcesso, int nivelMontarDados) throws Exception;

    public List consultarPorContratoModalidade(Integer valorConsulta, boolean controlarAcesso, int nivelMontarDados) throws Exception;

    public List consultarPorCodigoContratoModalidadeTurma(Integer valorConsulta, int nivelMontarDados) throws Exception;

    public void setIdEntidade(String aIdEntidade);

    public void excluirContratoModalidadeTurma(Integer contratoModalidade) throws Exception;

    public void incluirContratoModalidadeTurma(ContratoModalidadeTurmaVO obj) throws Exception;
    /**
     * Operação responsável por alterar todos os objetos da <code>ContratoModalidadeVO</code> contidos em um Hashtable no BD.
     * Faz uso da operação <code>excluirContratoModalidades</code> e <code>incluirContratoModalidades</code> disponíveis na classe <code>ContratoModalidade</code>.
     * @param objetos  List com os objetos a serem alterados ou incluídos no BD.
     * @exception Exception  Erro de conexão com o BD ou restrição de acesso a esta operação.
     */
    public void alterarContratoModalidadeTurma(Integer contratoModalidade, List objetos) throws Exception;

    /**
     * Operação responsável por consultar todos os <code>ContratoModalidadeVO</code> relacionados a um objeto da classe <code>contrato.Contrato</code>.
     * @param contrato  Atributo de <code>contrato.Contrato</code> a ser utilizado para localizar os objetos da classe <code>ContratoModalidadeVO</code>.
     * @return List  Contendo todos os objetos da classe <code>ContratoModalidadeVO</code> resultantes da consulta.
     * @exception Exception  Erro de conexão com o BD ou restrição de acesso a esta operação.
     */
    public List consultarContratoModalidadeTurmas(Integer contratoModalidade, int nivelMontarDados) throws Exception;

    List<ContratoModalidadeTurmaVO> consultar(Integer codigoContrato, int nivelMontarDados)throws Exception;

    public Boolean existeContratoModalidadeTurma(Integer codigoTurma) throws Exception;
}

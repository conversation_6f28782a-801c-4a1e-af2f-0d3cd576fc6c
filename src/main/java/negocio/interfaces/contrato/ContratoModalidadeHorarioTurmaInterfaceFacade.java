/*
 * To change this template, choose Too<PERSON> | Templates
 * and open the template in the editor.
 */

package negocio.interfaces.contrato;

import java.util.List;
import negocio.comuns.contrato.ContratoModalidadeHorarioTurmaVO;
import negocio.comuns.plano.HorarioTurmaVO;
import negocio.interfaces.basico.SuperInterface;

/**
 *
 * <AUTHOR>
 */
public interface ContratoModalidadeHorarioTurmaInterfaceFacade extends SuperInterface {

    /**
     * Operação responsável por alterar no BD os dados de um objeto da classe <code>ContratoModalidadeHorarioTurmaVO</code>.
     * Sempre utiliza a chave primária da classe como atributo para localização do registro a ser alterado.
     * Primeiramente valida os dados (<code>validarDados</code>) do objeto. Verifica a conexão com o banco de dados e a permissão do usuário
     * para realizar esta operacão na entidade.
     * Isto, através da operação <code>alterar</code> da superclasse.
     * @param obj    Objeto da classe <code>ContratoModalidadeHorarioTurmaVO</code> que será alterada no banco de dados.
     * @exception Execption Caso haja problemas de conexão, restrição de acesso ou validação de dados.
     */
    void alterar(ContratoModalidadeHorarioTurmaVO obj) throws Exception;

    /**
     * Operação responsável por alterar todos os objetos da <code>ContratoModalidadeHorarioTurmaVO</code> contidos em um Hashtable no BD.
     * Faz uso da operação <code>excluirContratoModalidadeHorarioTurmas</code> e <code>incluirContratoModalidadeHorarioTurmas</code> disponíveis na classe <code>ContratoModalidadeHorarioTurma</code>.
     * @param objetos  List com os objetos a serem alterados ou incluídos no BD.
     * @exception Exception  Erro de conexão com o BD ou restrição de acesso a esta operação.
     */
    void alterarContratoModalidadeHorarioTurmas(Integer contratoModalidadeTurma, List objetos) throws Exception;

    /**
     * Operação responsável por consultar todos os <code>ContratoModalidadeHorarioTurmaVO</code> relacionados a um objeto da classe <code>contrato.ContratoModalidadeTurma</code>.
     * @param contratoModalidadeTurma  Atributo de <code>contrato.ContratoModalidadeTurma</code> a ser utilizado para localizar os objetos da classe <code>ContratoModalidadeHorarioTurmaVO</code>.
     * @return List  Contendo todos os objetos da classe <code>ContratoModalidadeHorarioTurmaVO</code> resultantes da consulta.
     * @exception Exception  Erro de conexão com o BD ou restrição de acesso a esta operação.
     */
    List consultarContratoModalidadeHorarioTurmas(Integer contratoModalidadeTurma, int nivelMontarDados) throws Exception;

    /**
     * Operação responsável por localizar um objeto da classe <code>ContratoModalidadeHorarioTurmaVO</code>
     * através de sua chave primária.
     * @exception Exception Caso haja problemas de conexão ou localização do objeto procurado.
     */
    ContratoModalidadeHorarioTurmaVO consultarPorChavePrimaria(Integer codigoPrm, int nivelMontarDados) throws Exception;

    /**
     * Responsável por realizar uma consulta de <code>ContratoModalidadeHorarioTurma</code> através do valor do atributo
     * <code>Integer codigo</code>. Retorna os objetos com valores iguais ou superiores ao parâmetro fornecido.
     * Faz uso da operação <code>montarDadosConsulta</code> que realiza o trabalho de prerarar o List resultante.
     * @param   controlarAcesso Indica se a aplicação deverá verificar se o usuário possui permissão para esta consulta ou não.
     * @return  List Contendo vários objetos da classe <code>ContratoModalidadeHorarioTurmaVO</code> resultantes da consulta.
     * @exception Exception Caso haja problemas de conexão ou restrição de acesso.
     */
    List consultarPorCodigo(Integer valorConsulta, boolean controlarAcesso, int nivelMontarDados) throws Exception;

    /**
     * Responsável por realizar uma consulta de <code>ContratoModalidadeHorarioTurma</code> através do valor do atributo
     * <code>codigo</code> da classe <code>ContratoModalidadeTurma</code>
     * Faz uso da operação <code>montarDadosConsulta</code> que realiza o trabalho de prerarar o List resultante.
     * @return  List Contendo vários objetos da classe <code>ContratoModalidadeHorarioTurmaVO</code> resultantes da consulta.
     * @exception Exception Caso haja problemas de conexão ou restrição de acesso.
     */
    List consultarPorCodigoContratoModalidadeTurma(Integer valorConsulta, int nivelMontarDados) throws Exception;

    /**
     * Responsável por realizar uma consulta de <code>ContratoModalidadeHorarioTurma</code> através do valor do atributo
     * <code>Integer horarioTurma</code>. Retorna os objetos com valores iguais ou superiores ao parâmetro fornecido.
     * Faz uso da operação <code>montarDadosConsulta</code> que realiza o trabalho de prerarar o List resultante.
     * @param   controlarAcesso Indica se a aplicação deverá verificar se o usuário possui permissão para esta consulta ou não.
     * @return  List Contendo vários objetos da classe <code>ContratoModalidadeHorarioTurmaVO</code> resultantes da consulta.
     * @exception Exception Caso haja problemas de conexão ou restrição de acesso.
     */
    List consultarPorHorarioTurma(Integer valorConsulta, boolean controlarAcesso, int nivelMontarDados) throws Exception;

    /**
     * Operação responsável por excluir no BD um objeto da classe <code>ContratoModalidadeHorarioTurmaVO</code>.
     * Sempre localiza o registro a ser excluído através da chave primária da entidade.
     * Primeiramente verifica a conexão com o banco de dados e a permissão do usuário
     * para realizar esta operacão na entidade.
     * Isto, através da operação <code>excluir</code> da superclasse.
     * @param obj    Objeto da classe <code>ContratoModalidadeHorarioTurmaVO</code> que será removido no banco de dados.
     * @exception Execption Caso haja problemas de conexão ou restrição de acesso.
     */
    void excluir(ContratoModalidadeHorarioTurmaVO obj) throws Exception;

    /**
     * Operação responsável por excluir todos os objetos da <code>ContratoModalidadeHorarioTurmaVO</code> no BD.
     * Faz uso da operação <code>excluir</code> disponível na classe <code>ContratoModalidadeHorarioTurma</code>.
     * @param <code>contratoModalidadeTurma</code> campo chave para exclusão dos objetos no BD.
     * @exception Exception  Erro de conexão com o BD ou restrição de acesso a esta operação.
     */
    void excluirContratoModalidadeHorarioTurmas(Integer contratoModalidadeTurma) throws Exception;

    /**
     * Operação responsável por incluir no banco de dados um objeto da classe <code>ContratoModalidadeHorarioTurmaVO</code>.
     * Primeiramente valida os dados (<code>validarDados</code>) do objeto. Verifica a conexão com o banco de dados e a permissão do usuário
     * para realizar esta operacão na entidade.
     * Isto, através da operação <code>incluir</code> da superclasse.
     * @param obj  Objeto da classe <code>ContratoModalidadeHorarioTurmaVO</code> que será gravado no banco de dados.
     * @exception Exception Caso haja problemas de conexão, restrição de acesso ou validação de dados.
     */
    void incluir(ContratoModalidadeHorarioTurmaVO obj) throws Exception;

    /**
     * Operação responsável por incluir objetos da <code>ContratoModalidadeHorarioTurmaVO</code> no BD.
     * Garantindo o relacionamento com a entidade principal <code>contrato.ContratoModalidadeTurma</code> através do atributo de vínculo.
     * @param objetos List contendo os objetos a serem gravados no BD da classe.
     * @exception Exception  Erro de conexão com o BD ou restrição de acesso a esta operação.
     */
    void incluirContratoModalidadeHorarioTurmas(Integer contratoModalidadeTurmaPrm, List objetos) throws Exception;

    /**
     * Operação responsável por retornar um novo objeto da classe <code>ContratoModalidadeHorarioTurmaVO</code>.
     */
    ContratoModalidadeHorarioTurmaVO novo() throws Exception;

    public List consultarPorContrato(int codigoContrato, int nivelMontarDados) throws Exception;
    boolean houveAlteracaoDeHorarioTurma(Integer codigoContrato, List<HorarioTurmaVO> listaHorarioTurma)throws Exception;

    boolean existeContratoModalidadeHorarioTurma(int codigoHorarioTurma) throws Exception;
}

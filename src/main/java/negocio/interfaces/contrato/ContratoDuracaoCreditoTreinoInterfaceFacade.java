package negocio.interfaces.contrato;

import java.util.Date;
import java.util.List;
import negocio.comuns.contrato.ContratoDuracaoCreditoTreinoVO;
import negocio.comuns.contrato.ContratoDuracaoVO;
import negocio.comuns.contrato.ContratoVO;
import negocio.interfaces.basico.SuperInterface;

/**
 * Created by ulisses on 13/11/2015.
 */
public interface ContratoDuracaoCreditoTreinoInterfaceFacade extends SuperInterface {

    void incluir(ContratoDuracaoCreditoTreinoVO contratoDuracaoCreditoTreinoVO)throws Exception;
    void excluir(Integer codigoContrato)throws Exception;
    void alterarVezesSemana(ContratoDuracaoCreditoTreinoVO contratoDuracaoCreditoTreinoVO, Integer numeroVezesSemana)throws Exception;
    ContratoDuracaoCreditoTreinoVO consultarPorContratoDuracao(Integer codigoContratoDuracao, int nivelMontarDados)throws Exception;
    ContratoDuracaoCreditoTreinoVO consultarPorContrato(Integer codigoContrato, int nivelMontarDados)throws Exception;
    ContratoDuracaoCreditoTreinoVO consultarPorCodigo(Integer codigo, int nivelMontarDados)throws Exception;
    ContratoDuracaoCreditoTreinoVO consultarDadosParaCancelamentoContrato(ContratoVO contratoVO, Integer codigoPlano, boolean retrocederValorMensalPlanoCancelamento)throws Exception;
    
    public List<ContratoVO> consultarCodigosContratosEncerrar(Date data) throws Exception;

    void processarCreditoDisponivelMensal(Date diaProcessamentoRobo)throws Exception;
    void processarCreditoDisponivelMensalZerarCreditoContratoInativo(Date diaProcessamentoRobo) throws Exception;
    void alterarDataUltimoCreditoMensal(ContratoDuracaoCreditoTreinoVO contratoDuracaoCreditoTreinoVO, Date data)throws Exception;

}

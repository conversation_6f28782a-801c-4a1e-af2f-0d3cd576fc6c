package negocio.interfaces.gestao;

import br.com.pactosolucoes.enumeradores.TipoContratoEnum;
import negocio.comuns.basico.enumerador.TipoBVEnum;
import negocio.comuns.gestao.GestaoICVTO;
import negocio.interfaces.basico.SuperInterface;

import java.util.Date;
import java.util.List;

/**
 * Created by <PERSON> on 17/11/2016.
 */
public interface GestaoICVInterfaceFacade  extends SuperInterface{

    public GestaoICVTO processarICV(Date dia, List<Integer> colaboradores , Integer empresa ,
                                    boolean filtroMatriculas, boolean filtroRematriculas, boolean filtroRetornos,
                                    boolean filtroEspontaneo, boolean filtroAgendado,
                                    List<TipoContratoEnum> tiposContrato, List<TipoBVEnum> tiposBV) throws Exception;
}
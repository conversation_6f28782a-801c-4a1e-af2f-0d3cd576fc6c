package negocio.interfaces.crm;

import br.com.pactosolucoes.estrutura.paginacao.ConfPaginacao;
import negocio.comuns.crm.MailingHistoricoVO;
import negocio.interfaces.basico.SuperInterface;

import java.util.Date;
import java.util.List;

public interface MailingHistoricoInterfaceFacade extends SuperInterface {

    public void incluir(MailingHistoricoVO obj) throws Exception;

    public void excluir(MailingHistoricoVO obj) throws Exception;

    public List<MailingHistoricoVO> consultar(Integer malaDireta) throws Exception;

    public List<MailingHistoricoVO> consultarPaginado(Integer malaDireta, ConfPaginacao confPaginacao) throws Exception;

    public int consultarSaldoPorEmpresa(Integer codEmpresa, Date dataConsulta) throws Exception;
}

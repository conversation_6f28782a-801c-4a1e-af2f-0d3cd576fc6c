package negocio.interfaces.crm;

import br.com.pactosolucoes.enumeradores.OcorrenciaEnum;
import br.com.pactosolucoes.enumeradores.TipoVigenciaConsultaEnum;
import br.com.pactosolucoes.estrutura.paginacao.ConfPaginacao;
import negocio.comuns.basico.ClienteVO;
import negocio.comuns.basico.EmpresaVO;
import negocio.comuns.crm.ConfiguracaoIntegracaoBotConversaVO;
import negocio.comuns.crm.ConfiguracaoIntegracaoGymbotProVO;
import negocio.comuns.crm.MalaDiretaVO;
import negocio.interfaces.basico.SuperInterface;

import java.sql.Connection;
import java.util.Date;
import java.util.List;
import negocio.comuns.crm.ConfigEventoMailingTO;

/**
 * Interface reponsável por criar uma estrutura padrão de comunidação entre a camada de controle
 * e camada de negócio (em especial com a classe Façade). Com a utilização desta interface
 * é possível substituir tecnologias de uma camada da aplicação com mínimo de impacto nas demais.
 * Além de padronizar as funcionalidades que devem ser disponibilizadas pela camada de negócio, por intermédio
 * de sua classe Façade (responsável por persistir os dados das classes VO).
 */
public interface MalaDiretaInterfaceFacade extends SuperInterface {

    MalaDiretaVO novo() throws Exception;

    void incluir(MalaDiretaVO obj) throws Exception;

    void incluirSemCommit(MalaDiretaVO obj) throws Exception;

    void alterar(MalaDiretaVO obj) throws Exception;

    void excluir(MalaDiretaVO obj) throws Exception;

    MalaDiretaVO consultarPorChavePrimaria(Integer codigo, int nivelMontarDados, boolean envio) throws Exception;

    List consultarPorCodigo(Integer valorConsulta, boolean controlarAcesso, int nivelMontarDados) throws Exception;

    void setIdEntidade(String aIdEntidade);

    void agendarEnvio(MalaDiretaVO mensagem, Date diaMeta, EmpresaVO empresa) throws Exception;

    void habilitarEnvio(MalaDiretaVO malaDiretaVO) throws Exception;

    void agendarEnvioEmailSemCommit(MalaDiretaVO mensagem, Boolean gerarHistorico, Date diaMeta, EmpresaVO empresa) throws Exception;

    /**
     * Responsável por realizar uma consulta de <code>malaDiretaVO</code>
     * através do valor do atributo <code>titulo</code> da classe
     * <code>ModeloMensagem</code> Faz uso da operação
     * <code>montarDadosConsulta</code> que realiza o trabalho de prerarar o
     * List resultante.
     *
     * @return List Contendo vários objetos da classe
     *         <code>malaDiretaVO</code> resultantes da consulta.
     * @throws Exception Caso haja problemas de conexão ou restrição de acesso.
     */
    List consultarPorTituloModeloMensagem(String valorConsulta, int nivelMontarDados) throws Exception;

    List consultarPorDataEnvio(Date prmIni, boolean controlarAcesso, int nivelMontarDados) throws Exception;

    /**
     * Responsável por realizar uma consulta de <code>malaDiretaVO</code>
     * através do valor do atributo <code>situacao</code> da classe
     * <code>Colaborador</code> Faz uso da operação
     * <code>montarDadosConsulta</code> que realiza o trabalho de prerarar o
     * List resultante.
     *
     * @return List Contendo vários objetos da classe
     *         <code>malaDiretaVO</code> resultantes da consulta.
     * @throws Exception Caso haja problemas de conexão ou restrição de acesso.
     */
    List consultarPorRemetente(String valorConsulta, int nivelMontarDados) throws Exception;

    /**
     * Responsável por realizar uma consulta de <code>malaDiretaVO</code>
     * através do valor do atributo <code>String titulo</code>. Retorna os
     * objetos, com início do valor do atributo idêntico ao parâmetro fornecido.
     * Faz uso da operação <code>montarDadosConsulta</code> que realiza o
     * trabalho de prerarar o List resultante.
     *
     * @param controlarAcesso Indica se a aplicação deverá verificar se o usuário possui
     *                        permissão para esta consulta ou não.
     * @return List Contendo vários objetos da classe
     *         <code>malaDiretaVO</code> resultantes da consulta.
     * @throws Exception Caso haja problemas de conexão ou restrição de acesso.
     */
    List consultarPorTitulo(String valorConsulta, boolean controlarAcesso, int nivelMontarDados) throws Exception;

    /**
     * Responsável por realizar uma consulta de <code>malaDiretaVO</code>
     * através do valor do atributo <code>String mensagem</code>. Retorna os
     * objetos, com início do valor do atributo idêntico ao parâmetro fornecido.
     * Faz uso da operação <code>montarDadosConsulta</code> que realiza o
     * trabalho de prerarar o List resultante.
     *
     * @param controlarAcesso Indica se a aplicação deverá verificar se o usuário possui
     *                        permissão para esta consulta ou não.
     * @return List Contendo vários objetos da classe
     *         <code>malaDiretaVO</code> resultantes da consulta.
     * @throws Exception Caso haja problemas de conexão ou restrição de acesso.
     */
    List consultarPorMensagem(String valorConsulta, boolean controlarAcesso, int nivelMontarDados) throws Exception;

    List<MalaDiretaVO> consultarPaginado(Integer codigo, ConfPaginacao confPaginacao,
                                         Date inicio, Date fim, String consultarDescricao,
                                         Date inicioCriacao, Date fimCriacao, String consultarRemetente,
                                         Integer consultarMeioEnvio, Integer codigoTipoAgendamento, TipoVigenciaConsultaEnum tipoVigencia, Integer codEmpresa,
                                         Integer nivelMontarDados) throws Exception;

    List<MalaDiretaVO> consultar(Integer codigo, Date inicio, Date fim, String consultarDescricao,
                                 Date inicioCriacao, Date fimCriacao, String consultarRemetente,
                                 Integer consultarMeioEnvio, Integer codigoTipoAgendamento,
                                 TipoVigenciaConsultaEnum tipoVigencia,
                                 Integer codEmpresa, Integer nivelMontarDados, int limite, int pagina, String campoOrdenar, String tipoOrdenacao) throws Exception;

    Integer totalizadorMalaDireta(Integer codigo, Date inicio, Date fim, String consultarDescricao,
                                  Date inicioCriacao, Date fimCriacao, String consultarRemetente,
                                  Integer consultarMeioEnvio, Integer codigoTipoAgendamento,
                                  TipoVigenciaConsultaEnum tipoVigencia,
                                  Integer codEmpresa) throws Exception;


    ConfigEventoMailingTO consultarConfigEvento(Integer malaDireta, boolean tela, ConfigEventoMailingTO cfg) throws Exception;

    void gravarCRMExtra(MalaDiretaVO mensagem) throws Exception;

    List<MalaDiretaVO> consultarExisteMetaCRMExtraDiaUsuario(Date dia, Integer codUsuario) throws Exception;

    String consultarJSONCRMExtra(Integer empresa) throws Exception;

    void alterarSituacaoExclusao(MalaDiretaVO malaDireta, boolean excluida) throws Exception;

    /**
     * Atualiza o status das Metas Extras do CRM que tem a data de vigência da meta vencida.
     * @throws Exception
     */
    void atualizaStatusCRMMetaExtra(Date dataRobo) throws Exception;

    /**
     * Consultar códigos de malas diretas por OcorrenciaEnum
     * @param ocorrencia
     * @return
     * @throws Exception
     */
    List<Integer> consultarCodigosPorOcorrencia(OcorrenciaEnum ocorrencia, Integer empresa) throws Exception;

    List<ConfiguracaoIntegracaoBotConversaVO> consultarFluxoEmpresa (Integer empresa) throws Exception;

    List<ConfiguracaoIntegracaoGymbotProVO> consultarFluxoEmpresaGymbotPro (Integer empresa) throws Exception;

    List<ConfiguracaoIntegracaoBotConversaVO> consultarFluxoEmpresaFase (Integer empresa, String fase) throws Exception;

    List<ConfiguracaoIntegracaoGymbotProVO> consultarFluxoEmpresaFaseGymbotPro (Integer empresa, String fase) throws Exception;

    boolean clienteValidoParaEnvio(OcorrenciaEnum ocorrencia, ClienteVO clienteVO, Integer malaDireta, Connection con) throws Exception;

    Integer countEmails(Date dia, boolean igual, boolean integracaoPacto);

    String findbyFluxoUrlTelaAluno(Integer idEmpresa,  Connection con) throws Exception;

    String findbyFluxoGymbotProTelaAluno(Integer idEmpresa,  Connection con) throws Exception;

    ConfiguracaoIntegracaoBotConversaVO buscarFluxoPeloCodigoEmpresa(Integer idEmpresa,  Connection con) throws Exception;

    ConfiguracaoIntegracaoGymbotProVO buscarFluxoGymbotProPeloCodigoEmpresa(Integer idEmpresa,  Connection con) throws Exception;

    ConfiguracaoIntegracaoGymbotProVO consultarFluxoGymbotPro (Integer codigo, Integer empresa) throws Exception;
}

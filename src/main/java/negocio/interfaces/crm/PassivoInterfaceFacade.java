package negocio.interfaces.crm;

import negocio.comuns.arquitetura.UsuarioVO;
import negocio.comuns.basico.EmpresaVO;
import negocio.comuns.crm.ConfiguracaoSistemaCRMVO;
import negocio.comuns.crm.HistoricoContatoVO;
import negocio.comuns.crm.PassivoVO;
import negocio.comuns.crm.TipoMetaCRMTO;
import negocio.comuns.plano.HorarioTurmaVO;
import negocio.interfaces.basico.SuperInterface;

import java.util.Date;
import java.util.List;

/**
 * Interface reponsável por criar uma estrutura padrão de comunidação entre a camada de controle
 * e camada de negócio (em especial com a classe Façade). Com a utilização desta interface 
 * é possível substituir tecnologias de uma camada da aplicação com mínimo de impacto nas demais.
 * Além de padronizar as funcionalidades que devem ser disponibilizadas pela camada de negócio, por intermédio
 * de sua classe Façade (responsável por persistir os dados das classes VO).
 */
public interface PassivoInterfaceFacade extends SuperInterface{

    public PassivoVO novo() throws Exception;

    public void incluir(PassivoVO obj, boolean validaRD) throws Exception;

    public void incluir(PassivoVO obj, boolean wordPress, boolean validaRD) throws Exception;
    public void incluir(PassivoVO obj, boolean wordPress, boolean validaRD, boolean bitrix) throws Exception;

    public void alterar(PassivoVO obj) throws Exception;

    public void alterar(PassivoVO obj, Boolean bitrix) throws Exception;

    public void excluir(PassivoVO obj) throws Exception;

    public void salvar(PassivoVO obj, HistoricoContatoVO hist, String agendaOuObjecao, Integer empresa, ConfiguracaoSistemaCRMVO configCRM, Integer codigoAula, TipoMetaCRMTO tipoMetaSelec, UsuarioVO usuarioLogado, List<HorarioTurmaVO> aulasAgenda) throws Exception;

    public PassivoVO consultarPorChavePrimaria(Integer codigo, int nivelMontarDados) throws Exception;

    public List consultarPorCodigo(Integer valorConsulta, boolean controlarAcesso, EmpresaVO empresa, int nivelMontarDados) throws Exception;

    public PassivoVO consultarPorCodigo(Integer codigoPassivo, int nivelMontarDados) throws Exception;

    public List consultarPorNome(String valorConsulta, boolean controlarAcesso,EmpresaVO empresa, int nivelMontarDados) throws Exception;

    public List consultarPorSituacaoColaborador(String valorConsulta,EmpresaVO empresa, int nivelMontarDados) throws Exception;

    public List consultarPorDia(Date prmIni, Date prmFim, boolean controlarAcesso,EmpresaVO empresa, int nivelMontarDados) throws Exception;

    public List<PassivoVO> consultarPorPeriodoResponsavel(Integer empresa, Date prmIni, Date prmFim, Integer usuario, boolean controlarAcesso, int nivelMontarDados) throws Exception;

    public void setIdEntidade(String aIdEntidade);

    public List consultarPorSituacaoResponsavelCadastro(String valorConsulta,EmpresaVO empresa, int nivelMontarDados) throws Exception;

    public List consultarPorNomePassivoComLimite(String valorConsulta, boolean controlarAcesso,EmpresaVO empresa, int nivelMontarDados) throws Exception;

    public PassivoVO consultarPorNomePassivo(String valorConsulta, boolean controlarAcesso,EmpresaVO empresa, int nivelMontarDados) throws Exception;

    public void executarExclusaoDependentesPassivo(PassivoVO obj) throws Exception;

    public void excluirPassivo(PassivoVO obj) throws Exception;

    public void incluirDependencias(PassivoVO obj, HistoricoContatoVO hist, String agendaOuObjecao, Integer empresa, ConfiguracaoSistemaCRMVO configCRM, Integer codigoAula, TipoMetaCRMTO tipoMetaSelec, UsuarioVO usuarioLogado, List<HorarioTurmaVO> aulasAgenda) throws Exception;

    public Integer consultarPorCodigoCliente(Integer valorConsulta, boolean controlarAcesso) throws Exception;

    public void alterarSomentePassivo(PassivoVO obj) throws Exception;

    public void executarAlteracaoPorCadastroCliente(Integer codigo, Integer cliente) throws Exception;

    public List consultarPorUsuarioResponsavelCadastro(String valorConsulta, EmpresaVO empresa, int nivelMontarDados) throws Exception;

    public String consultarJSON(Integer empresa,String sEcho,Integer offset, Integer limit, String clausulaLike, Integer colOrdenar, String dirOrdenar,Date dtInicio, Date dtFim, boolean apresentarConvertidos) throws Exception;

    public List consultarParaImpressao(Integer empresa,Date dataInicio,Date dataFim ,String filtro, String ordem, String campoOrdenacao) throws Exception;

    public Integer contarPassivoPorPeriodoResponsavelCadastro(List<UsuarioVO> listaUsuarioVO, Date dataInicioMeta, Date dataFimMeta, Integer empresa) throws Exception;

    public List consultarPassivoPorPeriodoResponsavelCadastro(List<UsuarioVO> listaUsuarioVO, Date dataInicioMeta, Date dataFimMeta, Integer empresa, int nivelMontarDados) throws Exception;

    Integer contarPassivoConvertidosPorPeriodoResponsavelCadastro(List<UsuarioVO> listaUsuarioVO, Date dataInicioMeta, Date dataFimMeta, Integer empresa) throws Exception;

    List<PassivoVO> consultarPassivoConvertidosPorPeriodoResponsavelCadastro(List<UsuarioVO> listaUsuarioVO, Date dataInicioMeta, Date dataFimMeta, Integer empresa, int nivelMontarDados) throws Exception;

    void executarAlteracaoContrato(Integer passivo, Integer contrato) throws Exception;

    List<PassivoVO> consultarPorContrato(Integer contrato, boolean controlarAcesso, int nivelMontarDados) throws Exception;
    
    PassivoVO consultarPorEmail(String email, Integer empresa, int nivelMontarDados)throws Exception;

    void alterarObjecaoPassivo(Integer objecao, Integer passivo) throws Exception;
    
}

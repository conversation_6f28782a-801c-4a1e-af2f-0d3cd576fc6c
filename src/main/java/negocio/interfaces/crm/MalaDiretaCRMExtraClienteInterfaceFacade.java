package negocio.interfaces.crm;

import controle.crm.MetaExtraImportadoDTO;
import negocio.comuns.arquitetura.UsuarioVO;
import negocio.comuns.basico.AmostraClienteTO;
import negocio.comuns.basico.ClienteVO;
import negocio.comuns.crm.MalaDiretaCRMExtraClienteVO;
import negocio.comuns.crm.MalaDiretaVO;
import negocio.interfaces.basico.SuperInterface;

import java.util.List;

/**
 * Created by <PERSON><PERSON> on 02/12/2015
 */
public interface MalaDiretaCRMExtraClienteInterfaceFacade extends SuperInterface {

    void incluir(MalaDiretaCRMExtraClienteVO obj) throws Exception;

    void alterar(MalaDiretaCRMExtraClienteVO obj) throws Exception;

    void alterarMalaDireta(List<MalaDiretaCRMExtraClienteVO> obj, MalaDiretaVO malaDiretaVO) throws Exception;

    void excluir(MalaDiretaCRMExtraClienteVO obj) throws Exception;

    void excluirPorMalaDireta(MalaDiretaVO obj) throws Exception;

    void incluirListaCliente(MalaDiretaVO malaDiretaVO, List<AmostraClienteTO> amostraClienteTOList) throws Exception;

    List<MalaDiretaCRMExtraClienteVO> incluirListaImportados(UsuarioVO usuario, Integer empresa, MalaDiretaVO malaDiretaVO, List<MetaExtraImportadoDTO> listaImportados) throws Exception;

    List<MalaDiretaCRMExtraClienteVO> consultarPorMalaDireta(Integer codMalaDireta, int nivelMontarDados, Integer empresa) throws Exception;

    List<MalaDiretaCRMExtraClienteVO> consultarPorMalaDiretaVinculoColaborador(Integer codMalaDireta,Integer codigoColaborador,String tipoVinculo, int nivelMontarDados, Integer empresa) throws Exception;
}

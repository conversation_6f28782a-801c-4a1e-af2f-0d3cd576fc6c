package negocio.interfaces.crm;

import br.com.pactosolucoes.enumeradores.FasesCRMEnum;
import negocio.comuns.basico.ConfiguracaoEmailFechamentoMetaVO;
import negocio.comuns.basico.EmpresaVO;
import negocio.comuns.crm.ConfiguracaoDiasMetasTO;
import negocio.comuns.crm.ConfiguracaoEmpresaBitrixVO;
import negocio.comuns.crm.ConfiguracaoSistemaCRMVO;
import negocio.interfaces.basico.SuperInterface;

import java.util.Date;
import java.util.List;

/**
 * Interface reponsável por criar uma estrutura padrão de comunidação entre a camada de controle
 * e camada de negócio (em especial com a classe Façade). Com a utilização desta interface 
 * é possível substituir tecnologias de uma camada da aplicação com mínimo de impacto nas demais.
 * Além de padronizar as funcionalidades que devem ser disponibilizadas pela camada de negócio, por intermédio
 * de sua classe Façade (responsável por persistir os dados das classes VO).
 */
public interface ConfiguracaoSistemaCRMInterfaceFacade extends SuperInterface {

    ConfiguracaoSistemaCRMVO novo() throws Exception;

    void incluir(ConfiguracaoSistemaCRMVO obj) throws Exception;

    void alterar(ConfiguracaoSistemaCRMVO obj) throws Exception;

    void alterarSemCommit(ConfiguracaoSistemaCRMVO obj) throws Exception;

    void excluir(ConfiguracaoSistemaCRMVO obj) throws Exception;

    ConfiguracaoSistemaCRMVO consultarPorChavePrimaria(Integer codigo, int nivelMontarDados) throws Exception;

    List<ConfiguracaoSistemaCRMVO> consultarTodas(int nivelMontarDados) throws Exception;

    List consultarPorCodigo(Integer valorConsulta, boolean controlarAcesso, int nivelMontarDados) throws Exception;

    void setIdEntidade(String aIdEntidade);

    ConfiguracaoSistemaCRMVO consultarConfiguracaoSistemaCRM(int nivelMontarDados) throws Exception;

    List<ConfiguracaoEmailFechamentoMetaVO> consultarEmailsEnviarFechamento(EmpresaVO empresaVO) throws Exception;

    void salvarEmailsFechamento(List<ConfiguracaoEmailFechamentoMetaVO> emails) throws Exception;

    Boolean verificarDiaAcademiaAberta(Date dia, ConfiguracaoSistemaCRMVO configCRM, EmpresaVO empresa) throws Exception;

    Date obterDataCalculadaDiasUteis(Date dataContrato, boolean anterior, int nrDias, EmpresaVO empresa) throws Exception;

    boolean consultarSeExisteConfiguracaoSistemaCRM() throws Exception;

    void incluirConfiguracoesEmail(ConfiguracaoSistemaCRMVO configuracaoSistemaCRMVO) throws Exception;

    void incluirSemCommit(ConfiguracaoSistemaCRMVO obj) throws Exception;

    void gravarDiasMetas(List<ConfiguracaoDiasMetasTO> dias, FasesCRMEnum fase) throws Exception;

    List<ConfiguracaoDiasMetasTO> consultarConfiguracaoDiasMetasPorFase(FasesCRMEnum fase) throws Exception;

     void alterarLimiteDiarioLimite(Integer limite) throws Exception;

    Integer obterLimiteDiario() throws Exception;

    void alterarLimiteMensal(Integer limite) throws Exception;

    ConfiguracaoSistemaCRMVO obterConfiguracaoEmailAutomatico(String emailDestino, String nomeEmpresaLogada,
                                                              ConfiguracaoSistemaCRMVO configuracaoSistemaCRMBase) throws Exception;

    boolean isGerarIndicacaoParaCadastroConvidadosVendasOnline() throws Exception;

    Integer obterNrDiasContarResultado() throws Exception;

    List<ConfiguracaoEmpresaBitrixVO> consultarConfiguracaoEmpresaBitrix24(String chave) throws Exception;
    
}

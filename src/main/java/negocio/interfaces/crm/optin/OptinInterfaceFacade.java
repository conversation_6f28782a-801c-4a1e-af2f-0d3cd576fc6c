package negocio.interfaces.crm.optin;

import negocio.comuns.basico.ClienteVO;
import negocio.comuns.basico.EmpresaVO;
import negocio.comuns.crm.optin.OptinVO;
import negocio.comuns.plano.TurmaVO;
import negocio.comuns.utilitarias.Calendario;
import negocio.comuns.utilitarias.ContratoModalidadeReferenceException;
import negocio.comuns.utilitarias.Uteis;
import negocio.interfaces.basico.SuperInterface;
import org.json.JSONArray;

import java.sql.Connection;
import java.sql.PreparedStatement;

public interface OptinInterfaceFacade extends SuperInterface {

    OptinVO novo() throws Exception;

    void incluir(OptinVO obj) throws Exception;

    void alterar(OptinVO obj) throws Exception;

    void alterar(OptinVO obj, <PERSON>olean sesc, String email) throws Exception;

    void alterarSemCommit(OptinVO obj) throws Exception;

    void excluir(OptinVO obj) throws Exception;

    JSONArray consultarTodas() throws Exception;

    public OptinVO consultarPorChavePrimaria(Integer codigo, int nivelMontarDados) throws Exception;

    public Integer existeRegistro(OptinVO obj) throws Exception;

    public void atualizarBounce(OptinVO obj) throws Exception;


    boolean precisaRealizarEnvio(OptinVO optinVO);

    void alterarDataEnvio(OptinVO obj) throws Exception ;

    void enviarEmailOptin(EmpresaVO empresaVO, ClienteVO clienteVO, String chave, boolean sistemaSesc) throws Exception;

    void renviarEmailOptin(EmpresaVO empresaVO, ClienteVO clienteVO, String chave) throws Exception;
}

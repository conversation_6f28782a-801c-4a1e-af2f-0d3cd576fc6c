package negocio.interfaces.crm;

import negocio.comuns.basico.EmpresaVO;
import negocio.comuns.crm.FecharMetaVO;
import negocio.comuns.crm.IndicacaoVO;
import negocio.comuns.crm.IndicadoVO;
import negocio.interfaces.basico.SuperInterface;

import java.sql.Connection;
import java.util.Date;
import java.util.List;

/**
 * Interface reponsável por criar uma estrutura padrão de comunidação entre a camada de controle
 * e camada de negócio (em especial com a classe Façade). Com a utilização desta interface
 * é possível substituir tecnologias de uma camada da aplicação com mínimo de impacto nas demais.
 * Além de padronizar as funcionalidades que devem ser disponibilizadas pela camada de negócio, por intermédio
 * de sua classe Façade (responsável por persistir os dados das classes VO).
 */
public interface IndicadoInterfaceFacade extends SuperInterface {

    public IndicadoVO novo() throws Exception;

    public void incluir(IndicadoVO obj) throws Exception;

    public void alterar(IndicadoVO obj) throws Exception;

    public void excluir(IndicadoVO obj) throws Exception;

    public IndicadoVO consultarPorChavePrimaria(Integer codigo, int nivelMontarDados) throws Exception;

    public IndicadoVO consultarPorCodigoCliente(Integer codigo, int nivelMontarDados) throws Exception;

    public List consultarPorCodigo(Integer valorConsulta, boolean controlarAcesso, EmpresaVO empresa, int nivelMontarDados) throws Exception;

    public IndicadoVO consultarPorCodigo(Integer codigo, int nivelMontarDados) throws Exception;

    public List consultarPorSituacaoCliente(String valorConsulta, EmpresaVO empresa,  int nivelMontarDados) throws Exception;

    public List consultarPorNomeIndicado(String valorConsulta,boolean controlarAcesso,  EmpresaVO empresa,  int nivelMontarDados) throws Exception;

    public List consultarPorDia(Date prmIni, Date prmFim, boolean controlarAcesso, EmpresaVO empresa,  int nivelMontarDados) throws Exception;

    public void setIdEntidade(String aIdEntidade);

    public Integer consultarPorCodigoCliente(Integer valorConsulta, boolean controlarAcesso) throws Exception;

    public void excluirIndicados(Integer grupoColaborador) throws Exception;

    /**
     * Operação responsável por alterar todos os objetos da <code>GrupoColaboradorParticipanteVO</code> contidos em um Hashtable no BD.
     * Faz uso da operação <code>excluirGrupoColaboradorParticipantes</code> e <code>incluirGrupoColaboradorParticipantes</code> disponíveis na classe <code>GrupoColaboradorParticipante</code>.
     * @param objetos  List com os objetos a serem alterados ou incluídos no BD.
     * @exception Exception  Erro de conexão com o BD ou restrição de acesso a esta operação.
     */
    public void alterarIndicados(IndicacaoVO indicacao, List objetos) throws Exception;

    public void validarFecharMeta(IndicacaoVO indicacao, List objetos) throws Exception;

    public void validarSePodeExcluirIndicado(Integer codigoIndicado, Integer codigoColaborador) throws Exception;

    public Boolean consultarIndicaoECliente(Integer indicado) throws Exception;

    public void incluirIndicados(IndicacaoVO indicacaoPrm, List objetos) throws Exception;

    public List consultarPorCodigoIndicacao(Integer valorConsulta, int nivelMontarDados) throws Exception;

    public List consultarListaIndicados(Integer indicacao, int nivelMontarDados) throws Exception;

    public void executarAlteracaoPorCadastroCliente(Integer codigo, Integer cliente) throws Exception;

    public void incluirIndicadosMeta(IndicacaoVO indicacaoPrm, List objetos, FecharMetaVO fecharMeta) throws Exception;

    IndicadoVO consultarPorEmail(Integer codigoEmpresa,String emailIndicado, int nivelMontarDados)throws Exception;
    IndicadoVO consultarPorIndicacao(Integer codigoIndicacao, int nivelMontarDados) throws Exception;

    public void incluirIndicadoMeta(IndicadoVO obj, FecharMetaVO fecharMeta) throws Exception;

    void alterarObjecaoIndicado(Integer objecao, Integer indicado) throws Exception;

    List consultarPorCpf(String valorConsulta, EmpresaVO empresa, int nivelMontarDados) throws Exception;

    List consultarIndicadoPorCpf(String cpf, int nivelMontarDados) throws Exception;
}

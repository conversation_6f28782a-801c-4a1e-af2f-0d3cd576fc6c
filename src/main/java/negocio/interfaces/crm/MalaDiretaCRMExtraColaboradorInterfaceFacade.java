package negocio.interfaces.crm;

import negocio.comuns.arquitetura.UsuarioVO;
import negocio.comuns.crm.MalaDiretaCRMExtraColaboradorVO;
import negocio.comuns.crm.MalaDiretaVO;
import negocio.interfaces.basico.SuperInterface;

import java.util.List;

/**
 * Created by <PERSON><PERSON> on 02/12/2015
 */
public interface MalaDiretaCRMExtraColaboradorInterfaceFacade extends SuperInterface {

    void incluir(MalaDiretaCRMExtraColaboradorVO obj) throws Exception;

    void alterar(MalaDiretaCRMExtraColaboradorVO obj) throws Exception;

    void excluir(MalaDiretaCRMExtraColaboradorVO obj) throws Exception;

    void excluirPorMalaDireta(MalaDiretaVO obj) throws Exception;

    void incluirListaColaborador(MalaDiretaVO malaDiretaVO, List<UsuarioVO> usuarioVOList) throws Exception;

    List<MalaDiretaCRMExtraColaboradorVO> consultarPorMalaDireta(Integer codMalaDireta, int nivelMontarDados) throws Exception;
}

package negocio.interfaces.crm;

import negocio.comuns.crm.GrupoColaboradorVO;

import java.util.Date;
import java.util.List;
import negocio.comuns.utilitarias.ConsistirException;
import negocio.interfaces.basico.SuperInterface;

/**
 * Interface reponsável por criar uma estrutura padrão de comunidação entre a camada de controle
 * e camada de negócio (em especial com a classe Façade). Com a utilização desta interface 
 * é possível substituir tecnologias de uma camada da aplicação com mínimo de impacto nas demais.
 * Além de padronizar as funcionalidades que devem ser disponibilizadas pela camada de negócio, por intermédio
 * de sua classe Façade (responsável por persistir os dados das classes VO).
 */
public interface GrupoColaboradorInterfaceFacade extends SuperInterface {

    public GrupoColaboradorVO novo() throws Exception;

    public void incluir(GrupoColaboradorVO obj) throws Exception;

    public void alterar(GrupoColaboradorVO obj) throws Exception;

    public void excluir(GrupoColaboradorVO obj) throws Exception;

    public GrupoColaboradorVO consultarPorChavePrimaria(Integer codigo, int nivelMontarDados, Integer empresa) throws Exception;

    public List consultarPorCodigo(Integer valorConsulta, boolean controlarAcesso, int nivelMontarDados, Integer empresa) throws Exception;

    public List consultarPorDescricaoGrupo(String valorConsulta, boolean controlarAcesso, int nivelMontarDados, Integer empresa) throws Exception;

    public List consultarPorResponsavelGrupo(String valorConsulta, boolean controlarAcesso, int nivelMontarDados, Integer empresa) throws Exception;

    public List consultarPorParticipanteGrupo(String valorConsulta, boolean controlarAcesso, int nivelMontarDados, Integer empresa) throws Exception;

    public List<GrupoColaboradorVO> consultarPorTipoGrupo(String valorConsulta, boolean controlarAcesso, int nivelMontarDados, Integer empresa) throws Exception;

    public List consultarPorPeriodoDataCadastroCliente(Date data, Integer empresa, boolean controlarAcesso, int nivelMontarDados) throws Exception;

    public Boolean consultarGerentePorCodigoUsuarioLogado(Integer valorConsulta) throws Exception;

    public void setIdEntidade(String aIdEntidade);
    
    public List consultarGrupoColaboradorComParticipantes(boolean controlarAcesso, int nivelMontarDados, Integer empresa) throws Exception;
    
    public List consultarGrupoColaboradorComParticipantesPorCodigoColaboradorPorTipoVisao(Integer valorConsulta, String tipoVisao, boolean controlarAcesso, int nivelMontarDados, Integer empresa) throws Exception;

    public List consultarGrupoColaboradorPorCodigoColaborador(Integer valorConsulta, boolean controlarAcesso, int nivelMontarDados, Integer empresa) throws Exception;
    
    public void executarValidacaoSeExisterParticipanteDiferenteDoTipoGrupo(GrupoColaboradorVO grupo) throws ConsistirException;

    /**
     * Responsável por realizar uma consulta de <code>GrupoColaborador</code> através do valor do atributo
     * <code>Integer codigo</code>. Retorna os objetos com valores iguais ou superiores ao parâmetro fornecido.
     * Faz uso da operação <code>montarDadosConsulta</code> que realiza o trabalho de prerarar o List resultante.
     * @param   controlarAcesso Indica se a aplicação deverá verificar se o usuário possui permissão para esta consulta ou não.
     * @return  List Contendo vários objetos da classe <code>GrupoColaboradorVO</code> resultantes da consulta.
     * @exception Exception Caso haja problemas de conexão ou restrição de acesso.
     */
    public List consultarPorOrganizadorCarteira(Integer empresa, boolean controlarAcesso, int nivelMontarDados) throws Exception;

    /**
     * Responsável por realizar uma consulta de <code>GrupoColaborador</code> através do valor do atributo
     * <code>Integer codigo</code>. Retorna os objetos com valores iguais ou superiores ao parâmetro fornecido.
     * Faz uso da operação <code>montarDadosConsulta</code> que realiza o trabalho de prerarar o List resultante.
     * @param   controlarAcesso Indica se a aplicação deverá verificar se o usuário possui permissão para esta consulta ou não.
     * @return  List Contendo vários objetos da classe <code>GrupoColaboradorVO</code> resultantes da consulta.
     * @exception Exception Caso haja problemas de conexão ou restrição de acesso.
     */
    public GrupoColaboradorVO consultarPorOrganizadorCarteiraColaboradorSemGrupo(Integer empresa, boolean controlarAcesso, int nivelMontarDados) throws Exception;

    public GrupoColaboradorVO consultarPorPeriodoDataCadastroClienteColaboradorSemGrupo(Date data, Integer empresa, boolean controlarAcesso, int nivelMontarDados) throws Exception;

    public Boolean consultarSeExiste() throws Exception ;

    public void incluirSemCommit(GrupoColaboradorVO obj) throws Exception;

    public List consultarPorCodigo(Integer valorConsulta,int nivelMontarDados, Integer empresa) throws Exception;

    public String consultarJSON(Integer empresa) throws Exception;

    public List<GrupoColaboradorVO> consultarGruposColaboradorComUsuario(Integer codigoUsuario, boolean controlarAcesso, int nivelMontarDados, Integer empresa) throws Exception;
}

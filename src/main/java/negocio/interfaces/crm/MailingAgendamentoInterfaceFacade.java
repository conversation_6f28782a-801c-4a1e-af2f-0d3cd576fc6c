package negocio.interfaces.crm;

import java.util.Date;
import java.util.List;
import negocio.comuns.basico.CategoriaVO;
import negocio.comuns.basico.ColaboradorVO;
import negocio.comuns.basico.SituacaoClienteTO;
import negocio.comuns.contrato.ContratoDuracaoVO;
import negocio.comuns.contrato.ConvenioVO;
import negocio.comuns.crm.ConfigEventoMailingTO;
import negocio.comuns.crm.GenericoTO;
import negocio.comuns.crm.MailingAgendamentoVO;
import negocio.comuns.crm.MalaDiretaVO;
import negocio.comuns.financeiro.ConvenioCobrancaVO;
import negocio.comuns.plano.ModalidadeVO;
import negocio.comuns.plano.PlanoVO;
import negocio.facade.jdbc.contrato.Convenio;
import negocio.interfaces.basico.SuperInterface;

public interface MailingAgendamentoInterfaceFacade extends SuperInterface {
	
	public void incluir(MailingAgendamentoVO agendamento, int maladireta) throws Exception;
	
	public void excluir(MailingAgendamentoVO agendamento) throws Exception;
	
	public MailingAgendamentoVO consultar(MalaDiretaVO malaDireta) throws Exception;
	
	public void montarSql(MalaDiretaVO malaDireta, Integer empresa,
						  List<CategoriaVO> categoriaVOs,
						  List<SituacaoClienteTO> situacaoClienteTOs,
						  List<ModalidadeVO> modalidadeVOs,
						  List<ColaboradorVO> consultoresVOs,
						  List<ColaboradorVO> professoresVOs,
						  List<PlanoVO> planoVOs,
						  List<ContratoDuracaoVO> contratoDuracaoVOs,
						  List<ConvenioCobrancaVO> convenioCobrancaVO,
						  Date inicioVencimento, Date fimVencimento,
						  Integer inicioDiasSemAparecer, Integer fimDiasSemAparecer, boolean dist) throws Exception;


        public List<GenericoTO> consultarSimples(String campoCodigo, String campoLabel, String tabela, String campoCondicao, String condicao) throws Exception;
}

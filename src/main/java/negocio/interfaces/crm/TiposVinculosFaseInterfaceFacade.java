package negocio.interfaces.crm;

import java.util.List;

import br.com.pactosolucoes.enumeradores.FasesCRMEnum;

import negocio.comuns.crm.TiposVinculosFaseVO;
import negocio.interfaces.basico.SuperInterface;



/**
 * <AUTHOR>
 *
 */
public interface TiposVinculosFaseInterfaceFacade extends SuperInterface {
	
	public void incluir(TiposVinculosFaseVO obj) throws Exception;
	
	public void incluir(List<TiposVinculosFaseVO> obj) throws Exception;

    public void excluir(TiposVinculosFaseVO obj) throws Exception;
    
    public void excluirTodos() throws Exception;

    public TiposVinculosFaseVO consultarPorFase(Integer fase) throws Exception;
    
    public List<TiposVinculosFaseVO> consultarTodos() throws Exception;
    
    public boolean verificarAberturaColaboradorFase(String tipoColaborador, FasesCRMEnum fase) throws Exception;
    
    public List<String> consultarTiposColaborador(Integer codigoColaborador) throws Exception;
    
    public boolean verificarUsarDivisao() throws Exception;
    
}

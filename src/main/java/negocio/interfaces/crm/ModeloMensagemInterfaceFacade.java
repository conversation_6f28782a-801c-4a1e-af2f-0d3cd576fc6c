package negocio.interfaces.crm;
import java.util.ArrayList;
import java.util.List;

import controle.crm.MsgBuildDTO;
import negocio.comuns.crm.MeioEnvio;

import negocio.comuns.crm.ModeloMensagemVO;
import negocio.interfaces.basico.SuperInterface;

/**
 * Interface reponsável por criar uma estrutura padrão de comunidação entre a camada de controle
 * e camada de negócio (em especial com a classe Façade). Com a utilização desta interface 
 * é possível substituir tecnologias de uma camada da aplicação com mínimo de impacto nas demais.
 * Além de padronizar as funcionalidades que devem ser disponibilizadas pela camada de negócio, por intermédio
 * de sua classe Façade (responsável por persistir os dados das classes VO).
*/
public interface ModeloMensagemInterfaceFacade extends SuperInterface {
	

    ModeloMensagemVO novo() throws Exception;

    void incluir(ModeloMensagemVO obj) throws Exception;

    void alterar(ModeloMensagemVO obj) throws Exception;

    void excluir(ModeloMensagemVO obj) throws Exception;

    ModeloMensagemVO consultarPorChavePrimaria(Integer codigo, int nivelMontarDados ) throws Exception;

    List consultarPorCodigo(Integer valorConsulta, boolean controlarAcesso, int nivelMontarDados ) throws Exception;

    List consultarPorTitulo(String valorConsulta, boolean controlarAcesso, int nivelMontarDados ) throws Exception;

    void setIdEntidade(String aIdEntidade);

    int obterNumeroTotalImagensModelo() throws Exception;

    List consultarPorCodigo(Integer valorConsulta,MeioEnvio meioEnvio, boolean controlarAcesso, int nivelMontarDados) throws Exception;

    List consultarPorTitulo(String valorConsulta,MeioEnvio meioEnvio, boolean controlarAcesso, int nivelMontarDados) throws Exception;

    boolean validaNomeArquivo(String nomeArquivo) throws Exception;

    void updateImagemModelo(ModeloMensagemVO obj);

    String consultarJSON() throws Exception;

    ModeloMensagemVO consultarPorTipo(String tipoMensagem, int nivelMontarDados) throws Exception;

    List<MsgBuildDTO> consultarMensagensBuilder(MeioEnvio meioEnvio) throws Exception;

    List<MsgBuildDTO> consultarMensagensWhatsAppBuilder( Integer empresaId, String key, MeioEnvio meioEnvio) throws Exception;

    List<MsgBuildDTO> consultarModelosAntigos() throws Exception;

    List<MsgBuildDTO> getTemplatesPacto(String chave);

    void desativar(Integer codigo) throws Exception;
}

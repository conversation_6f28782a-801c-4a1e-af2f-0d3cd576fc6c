package negocio.interfaces.crm;

import br.com.pactosolucoes.enumeradores.FasesCRMEnum;
import br.com.pactosolucoes.estrutura.paginacao.ConfPaginacao;
import negocio.comuns.arquitetura.UsuarioVO;
import negocio.comuns.basico.EmpresaVO;
import negocio.comuns.crm.*;
import negocio.interfaces.basico.SuperInterface;

import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Set;

public interface FecharMetaDetalhadoInterfaceFacade extends SuperInterface {

    /**
     * Operação responsável por retornar um novo objeto da classe
     * <code>FecharMetaDetalhadoVO</code>.
     */
    FecharMetaDetalhadoVO novo() throws Exception;

    /**
     * Operação responsável por incluir no banco de dados um objeto da classe
     * <code>FecharMetaDetalhadoVO</code>. Primeiramente valida os dados (
     * <code>validarDados</code>) do objeto. Verifica a conexão com o banco de
     * dados e a permissão do usuário para realizar esta operacão na entidade.
     * Isto, através da operação <code>incluir</code> da superclasse.
     *
     * @param obj Objeto da classe <code>FecharMetaDetalhadoVO</code> que será
     *            gravado no banco de dados.
     * @throws Exception Caso haja problemas de conexão, restrição de acesso ou
     *                   validação de dados.
     */
    void incluir(FecharMetaDetalhadoVO obj) throws Exception;

    void incluirSemCommit(FecharMetaDetalhadoVO obj) throws Exception;

    /**
     * Operação responsável por alterar no BD os dados de um objeto da classe
     * <code>FecharMetaDetalhadoVO</code>. Sempre utiliza a chave primária da
     * classe como atributo para localização do registro a ser alterado.
     * Primeiramente valida os dados (<code>validarDados</code>) do objeto.
     * Verifica a conexão com o banco de dados e a permissão do usuário para
     * realizar esta operacão na entidade. Isto, através da operação
     * <code>alterar</code> da superclasse.
     *
     * @param obj Objeto da classe <code>FecharMetaDetalhadoVO</code> que será
     *            alterada no banco de dados.
     * @throws Exception Caso haja problemas de conexão, restrição de acesso ou
     *                   validação de dados.
     */
    void alterar(FecharMetaDetalhadoVO obj) throws Exception;

    void alterarSomenteCamposHistoricoContato(Integer codHistoricoContato, Integer codigoFecharMetaDetalhado) throws Exception;

    void alterarSomenteCamposObteveSucessoAndContrato(Boolean obteveSucesso, Integer contrato, Integer codigoFecharMetaDetalhado, String observacao) throws Exception;

    /**
     * Operação responsável por excluir no BD um objeto da classe
     * <code>FecharMetaDetalhadoVO</code>. Sempre localiza o registro a ser
     * excluído através da chave primária da entidade. Primeiramente verifica a
     * conexão com o banco de dados e a permissão do usuário para realizar esta
     * operacão na entidade. Isto, através da operação <code>excluir</code> da
     * superclasse.
     *
     * @param obj              Objeto da classe <code>FecharMetaDetalhadoVO</code> que será
     *                         removido no banco de dados.
     * @param ignorarPermissao
     * @throws Exception Caso haja problemas de conexão ou restrição de acesso.
     */
    void excluir(FecharMetaDetalhadoVO obj, Boolean ignorarPermissao) throws Exception;

    void excluir(FecharMetaDetalhadoVO obj) throws Exception;

    FecharMetaDetalhadoVO consultarPorCodigoIndicadoCodigoColaboradorResponsavelDataIdentificador(Integer indicado, Integer codigoColaborador, Date data, String identificador, int nivelMontarDados) throws Exception;

    /**
     * Operação responsável por excluir no BD um objeto da classe
     * <code>FecharMetaDetalhadoVO</code>. Sempre localiza o registro a ser
     * excluído através da chave primária da entidade. Primeiramente verifica a
     * conexão com o banco de dados e a permissão do usuário para realizar esta
     * operacão na entidade. Isto, através da operação <code>excluir</code> da
     * superclasse.
     */
    void excluirPorCodigoPassivoCodigoColaboradorResponsavelDataIdentificador(Integer passivo, Integer codigoColaborador, Date data, String identificador) throws Exception;

    void excluirFecharMetaDetalhadoPorCodigoPassivo(Integer codPassivo) throws Exception;

    /**
     * Operação responsável por excluir no BD um objeto da classe
     * <code>FecharMetaDetalhadoVO</code>. Sempre localiza o registro a ser
     * excluído através da chave primária da entidade. Primeiramente verifica a
     * conexão com o banco de dados e a permissão do usuário para realizar esta
     * operacão na entidade. Isto, através da operação <code>excluir</code> da
     * superclasse.
     */
    void excluirPorCodigoClienteCodigoColaboradorResponsavelDataIdentificador(Integer cliente, Integer codigoColaborador, Date data, String identificador) throws Exception;

    /**
     * Responsável por realizar uma consulta de <code>FecharMetaDetalhado</code>
     * através do valor do atributo <code>situacao</code> da classe
     * <code>Cliente</code> Faz uso da operação <code>montarDadosConsulta</code>
     * que realiza o trabalho de prerarar o List resultante.
     *
     * @return List Contendo vários objetos da classe
     * <code>FecharMetaDetalhadoVO</code> resultantes da consulta.
     * @throws Exception Caso haja problemas de conexão ou restrição de acesso.
     */
    List consultarPorSituacaoCliente(String valorConsulta, int nivelMontarDados) throws Exception;

    /**
     * Responsável por realizar uma consulta de <code>FecharMetaDetalhado</code>
     * através do valor do atributo <code>codigo</code> da classe
     * <code>FecharMeta</code> Faz uso da operação
     * <code>montarDadosConsulta</code> que realiza o trabalho de prerarar o
     * List resultante.
     *
     * @return List Contendo vários objetos da classe
     * <code>FecharMetaDetalhadoVO</code> resultantes da consulta.
     * @throws Exception Caso haja problemas de conexão ou restrição de acesso.
     */
    List consultarPorCodigoFecharMeta(Integer valorConsulta, int nivelMontarDados) throws Exception;

    /**
     * Responsável por realizar uma consulta de <code>FecharMetaDetalhado</code>
     * através do valor do atributo <code>Integer codigo</code>. Retorna os
     * objetos com valores iguais ou superiores ao parâmetro fornecido. Faz uso
     * da operação <code>montarDadosConsulta</code> que realiza o trabalho de
     * prerarar o List resultante.
     *
     * @param controlarAcesso Indica se a aplicação deverá verificar se o usuário possui
     *                        permissão para esta consulta ou não.
     * @return List Contendo vários objetos da classe
     * <code>FecharMetaDetalhadoVO</code> resultantes da consulta.
     * @throws Exception Caso haja problemas de conexão ou restrição de acesso.
     */
    FecharMetaDetalhadoVO consultarPorCodigo(Integer valorConsulta, boolean controlarAcesso, int nivelMontarDados) throws Exception;

    /**
     * Operação responsável por excluir todos os objetos da
     * <code>FecharMetaDetalhadoVO</code> no BD. Faz uso da operação
     * <code>excluir</code> disponível na classe
     * <code>FecharMetaDetalhado</code>.
     *
     * @param fecharMeta campo chave para exclusão dos objetos no
     *                   BD.
     * @throws Exception Erro de conexão com o BD ou restrição de acesso a esta
     *                   operação.
     */
    void excluirFecharMetaDetalhados(Integer fecharMeta) throws Exception;

    /**
     * Operação responsável por incluir objetos da
     * <code>FecharMetaDetalhadoVO</code> no BD. Garantindo o relacionamento com
     * a entidade principal <code>CRM.FecharMeta</code> através do atributo de
     * vínculo.
     */
    void incluirFecharMetaDetalhados(Integer fecharMetaPrm, List objetos) throws Exception;

    /**
     * Operação responsável por consultar todos os
     * <code>FecharMetaDetalhadoVO</code> relacionados a um objeto da classe
     * <code>CRM.FecharMeta</code>.
     *
     * @param fecharMeta Atributo de <code>CRM.FecharMeta</code> a ser utilizado para
     *                   localizar os objetos da classe
     *                   <code>FecharMetaDetalhadoVO</code>.
     * @return List Contendo todos os objetos da classe
     * <code>FecharMetaDetalhadoVO</code> resultantes da consulta.
     * @throws Exception Erro de conexão com o BD ou restrição de acesso a esta
     *                   operação.
     */
    List<FecharMetaDetalhadoVO> consultarFecharMetaDetalhados(Integer fecharMeta, int nivelMontarDados) throws Exception;

    /**
     * Operação responsável por localizar um objeto da classe
     * <code>FecharMetaDetalhadoVO</code> através de sua chave primária.
     *
     * @throws Exception Caso haja problemas de conexão ou localização do objeto
     *                   procurado.
     */
    FecharMetaDetalhadoVO consultarPorChavePrimaria(Integer codigoPrm, int nivelMontarDados) throws Exception;

    FecharMetaDetalhadoVO consultarPorCodigoPassivo(Integer codigoPassivo, int nivelMontarDados) throws Exception;

    /**
     * Método responsavel por fazer a consulta de meta detalhado por mes, onde
     * lista todos os passivos daquele determinado mes.
     *
     * <AUTHOR>
     */
    String inicializarSqlFecharMetaDetalhadosPassivoPorMes(String identificador, AberturaMetaVO obj, int nivelMontarDados) throws Exception;

    /**
     * Método responsavel por fazer a consulta de meta detalhado por mes, onde
     * lista todos os passivos daquele determinado mes.
     *
     * <AUTHOR>
     */
    String inicializarSqlFecharMetaDetalhadosAgendadosPorMes(String identificador, AberturaMetaVO obj, int nivelMontarDados) throws Exception;

    /**
     * Método responsavel por fazer a consulta de meta detalhado por mes, onde
     * lista todos os clientes vinte Quatro Horas daquele determinado mes.
     *
     * <AUTHOR>
     */
    String inicializarSqlFecharMetaDetalhadosVinteQuatroHorasPorMes(String identificador, AberturaMetaVO obj, int nivelMontarDados) throws Exception;

    /**
     * Método responsavel por fazer a consulta de meta detalhado por mes, onde
     * lista todos os passivos daquele determinado mes.
     *
     * @throws Exception
     */
    String inicializarSqlFecharMetaDetalhadosIndicadoPorMes(String identificador, AberturaMetaVO obj, int nivelMontarDados) throws Exception;

    /**
     * Método responsavel por fazer a consulta do Historico da tela metaPassivoDetalhadaForm,
     * onde se pode consultar por nome e por duas datas(dataInicio, dataTermino), que retorna todas
     * metas detalhadas durante o periodo especificado.
     *
     * @param nome
     * @param dataInicio
     * @param dataTermino
     * @param nivelMontarDados
     * @return
     * @throws Exception
     * <AUTHOR>
     */
    String inicializarSqlPassivoHistorico(String identificador, String nome, Date dataInicio, Date dataTermino, AberturaMetaVO obj, int nivelMontarDados, Boolean isVenda) throws Exception;

    /**
     * Método responsavel por retornar um sql caso a meta seja de Agendados e
     * a aba seja Historico
     *
     * @param identificador
     * @param nome
     * @param dataInicio
     * @param dataTermino
     * @param obj
     * @param nivelMontarDados
     * @return slq
     * @throws Exception
     * <AUTHOR>
     */
    String inicializarSqlAgendadosHistorico(String identificador, String nome, Date dataInicio, Date dataTermino, AberturaMetaVO obj, int nivelMontarDados) throws Exception;

    /**
     * Método responsavel por fazer a consulta do Historico da tela metaIndicacaoDetalhadaForm,
     * onde se pode consultar por nome e por duas datas(dataInicio, dataTermino), que retorna todas
     * metas detalhadas durante o periodo especificado.
     *
     * @param nome
     * @param dataInicio
     * @param dataTermino
     * @param nivelMontarDados
     * @return
     * @throws Exception
     */
    String inicializarSqlIndicadoHistorico(String identificador, String nome, Date dataInicio, Date dataTermino, AberturaMetaVO obj, int nivelMontarDados) throws Exception;

    /**
     * Metodo responsavel por fazer a consulta da Meta PosVenda da aba Historico
     *
     * @param identificador
     * @param nome
     * @param dataInicio
     * @param dataTermino
     * @param obj
     * @param nivelMontarDados
     * @return
     * @throws Exception
     */
    List consultarFecharMetaDetalhadosHistorico(String identificador, String nome, Date dataInicio, Date dataTermino, AberturaMetaVO obj, int nivelMontarDados, Boolean isVenda) throws Exception;

    /**
     * Método que consulta fecharMetaDetalhada apos selecionar a meta no include_indicadorVendas e include_indicadorRetencao
     */
    List consultarFecharMetaDetalhados(Date dia, Integer codResponsavel, String sqlColaborador, String identificador, int nivelMontarDados, Integer empresa) throws Exception;

    /**
     * Metodo que consulta a lista de fecharMetaDetalhado do dia
     * utilizando a seguites regras validar quais sao os colaboradores que estao marcado na descricao do grupo
     * e qual tipo de meta vai ser consultada.
     */
    List consultarFecharMetaDetalhado(AberturaMetaVO obj, String identificador, Boolean isVenda, Integer empresa) throws Exception;

    /**
     * Método que retorna a qtde o codigo dos clientes 24 Horas para serem
     * gravados posteriormente.
     *
     * @param obj
     * @param dia
     * @param codColaborador
     * @param nivelMontarDados
     * @return
     * @throws Exception
     * <AUTHOR>
     */
    ResultSet consultarClientesVinteQuatroHorasBuscandoCodigo(FecharMetaVO obj, Date dia, Integer codColaborador, int nivelMontarDados, Integer empresa, String tipoVinculos, String codigosClientesObjecoes) throws Exception;

    ResultSet consultarClientesGymPassBuscandoCodigo(FecharMetaVO obj, Date dia, Integer codColaborador, int nivelMontarDados, Integer empresa, String tipoVinculos, String codigosClientesObjecoes) throws Exception;

    /**
     * Método que retorna a qtde o codigo dos Agendados para serem gravados
     * posteriormente.
     *
     * @param obj
     * @param dia
     * @param codColaborador
     * @param nivelMontarDados
     * @return
     * @throws Exception
     * <AUTHOR>
     */
    ResultSet consultarVisitantesRecorrentes(FecharMetaVO obj, Date dia, Integer codColaborador, int nivelMontarDados, Integer empresa, String tipoVinculos, String codigosClientesObjecoes) throws Exception;

    /**
     * Método que retorna a qtde o codigo dos Agendados para serem gravados
     * posteriormente.
     *
     * @param obj
     * @param dia
     * @param codColaborador
     * @param nivelMontarDados
     * @return
     * @throws Exception
     * <AUTHOR>
     */
    ResultSet consultarClientesAgendados(FecharMetaVO obj, Date dia, Integer codColaborador, int nivelMontarDados, Integer empresa, String tipoVinculos, String codigosClientesObjecoes) throws Exception;

    /**
     * Método que retorna o codigo dos clientes Renovados para serem gravados
     * posteriormente.
     *
     * @param obj
     * @param codColaborador
     * @param diasPrevisto
     * @param dia
     * @param nivelMontarDados
     * @return
     * @throws Exception
     * <AUTHOR>
     */
    ResultSet consultarClientesRenovacao(FecharMetaVO obj, Integer codColaborador, Integer diasPrevistoUmMes, Integer diasPrevistoMaiorUmMes, Date dia,
            int nivelMontarDados, Integer empresa, String tipoVinculos, String codigoClientesObjecoes, Integer saldoLimiteRenovar, Set<Integer> skipped, boolean entraContratoAutoRRenovavel) throws Exception;

    /**
     * Método que retorna o codigo das Perdas para serem gravados
     * posteriormente.
     *
     * @param obj
     * @param dia
     * @param nrDiasAposVencimentoContrato
     * @param codColaborador
     * @param nivelMontarDados
     * @return
     * @throws Exception
     * <AUTHOR>
     */
    ResultSet consultarPerdasClientes(FecharMetaVO obj, Date dia, Integer nrDiasAposVencimentoContrato, Integer codColaborador, int nivelMontarDados, Integer empresa, String tipoVinculos, String codigoClientesObjecoes) throws Exception;

    /**
     * Método que retorna o codigo das Risco para serem gravados posteriormente.
     *
     * <AUTHOR>
     */
    ResultSet consultarRiscoClientes(FecharMetaVO obj, Integer codColaborador, Integer nrRisco, Date meta, int nivelMontarDados, Integer empresa, String tipoVinculos, String codigosClientesObjecoes) throws Exception;

    ResultSet consultarCalculoMetaQtdeFaltasos(FecharMetaVO obj, Date dia, Integer codColaborador, Integer nrFalta, Integer nrFaltaConf, Integer nrDuracaoPlano, int nivelMontarDados, Integer empresa, String tipoVinculos, String codigoClientesObjecoes, Integer codCliente) throws Exception;

    /**
     * Método responsavel por consultar os clientes que realizaram uma matricula
     * seguindo as regras da Meta Pos Venda.
     *
     * @param nrDias
     * @param dia
     * @param obj
     * @param codigoConfiguracaoDiasPosVenda
     * @param codColaborador
     * @param nivelMontarDados
     * @return ResultSet
     * @throws Exception Caso haja prolemas de conexão, restrição de acesso ou validação de dados.
     * <AUTHOR>
     */
    ResultSet consultarClientesPosVendaBuscandoCodigoPassandoNrDeDias(Integer nrDias, Date dia, FecharMetaVO obj, Integer codigoConfiguracaoDiasPosVenda, Integer codColaborador, int nivelMontarDados, Integer empresa, boolean incluirContratosRenovados, String tipoVinculos, String codigosClientesObjecoes) throws Exception;

    /**
     * Método responsavel por consultar a meta detalhada pelo codigo do colaboradorResponsavel.
     *
     * @param valorConsulta
     * @param identificadorMeta
     * @param indicado
     * @param passivo
     * @param cliente
     * @param controlarAcesso
     * @param nivelMontarDados
     * @return FecharMetaDetalhadoVO
     * @throws Exception
     * <AUTHOR>
     */
    FecharMetaDetalhadoVO consultarPorCodigoColaboradorResponsavelIdentificadorMeta(Integer valorConsulta, String identificadorMeta, Integer indicado, Integer passivo, Integer cliente, boolean controlarAcesso, int nivelMontarDados) throws Exception;

    /**
     * Metodo responsavel por setar o codigo do cliente, passivo, indicação que
     * foi consultado.
     *
     * @param dadosSQL
     * @param obj
     * @param configuracaoDiasPosVenda
     * @param nivelMontarDados
     * @throws SQLException
     * <AUTHOR>
     */
    void montarFecharMetaDetalhado(ResultSet dadosSQL, FecharMetaVO obj, Integer configuracaoDiasPosVenda, int nivelMontarDados) throws Exception;
    void montarFecharMetaDetalhado(ResultSet dadosSQL, FecharMetaVO obj, Integer configuracaoDiasPosVenda, int nivelMontarDados, Set<Integer> skipped) throws Exception;

    /**
     * Método responsavel por verificar a aba selecionada e inicializar os dados para o
     * envio de e-mail.
     *
     * @param obj
     * @param malaDiretaVO
     * @param usuarioLogado
     * @throws Exception
     * <AUTHOR>
     */
    void inicializarDadosEnviarEmailColetivo(FecharMetaVO obj, MalaDiretaVO malaDiretaVO, UsuarioVO usuarioLogado) throws Exception;

    /**
     * Método resposanvel por consultar uma metaDetalhada pelo codigo do passivo.
     * Este metodo é executado quando exclui um Passivo.
     *
     * <AUTHOR>
     */
    List consultarFecharMetaDetalhadoPorCodigoPassivo(Integer codPassivo, int nivelMontarDados) throws Exception;

    /**
     * Método responsavel por chamar uma consulta que retorna uma lista de FecharMetaDetalhadoVO
     * nessa lista eu verifico se dentro de cada metaDetalhada se existe uma meta em aberto para ela,
     * se existir é excluido a metaDetalhada pelo codigo do Passivo.
     *
     * @param codPassivo
     * @throws Exception
     * <AUTHOR>
     */
    List executarExclusaoFecharMetaDetalhadoPorCodigoPassivo(Integer codPassivo) throws Exception;

    /**
     * Operação reponsável por definir um novo valor para o identificador desta
     * classe. Esta alteração deve ser possível, pois, uma mesma classe de
     * negócio pode ser utilizada com objetivos distintos. Assim ao se verificar
     * que Como o controle de acesso é realizado com base neste identificador,
     */
    void setIdEntidade(String idEntidade);

    FecharMetaDetalhadoVO consultarFecharMetaDetalhadoPorCodigoAgenda(Integer codAgenda, int nivelMontarDados) throws Exception;

    void excluirPorCodigoIndicadoCodigoColaboradorResponsavelDataIdentificador(Integer indicado, Integer codigoIndicacao, Integer codigoColaborador, String identificador) throws Exception;

    void excluirPorCodigoIndicadoCodigoColaboradorResponsavelDataIdentificador(Integer indicado, Integer codigoColaborador, Date data, String identificador) throws Exception;

    Boolean consultarSeAberturaMetaEstaAbertaPorCodigoIndicadoCodigoColaboradorResponsavelIdentificador(Integer indicado, Integer codigoColaborador, String identificador) throws Exception;

    void consultarClientesAniversariantesBuscandoCodigo(FecharMetaVO obj, Date data, Integer codColaborador, int nivelMontarDados, Integer empresa, String tipoVinculos, String codigoClientesObjecoes ) throws Exception;

    ResultSet consultarClientesPeloCodigoConsultorAndDataLancamentoContrato(Integer codigoColaborador, Date dataLancamento) throws Exception;

    Integer consultarPorCodigoPassivoComBaseEmHistoriContato(Integer codigoPassivo, boolean controlarAcesso) throws Exception;

    Integer consultarPorCodigoIndicadoComBaseEmHistoriContato(Integer codigoIndicado, boolean controlarAcesso) throws Exception;

    Integer consultarPorCodigoClienteComBaseEmHistoriContato(Integer codigoCliente, boolean controlarAcesso) throws Exception;

    List<FecharMetaDetalhadoVO> consultarPorContrato(int contrato, boolean controlarAcesso, int nivelMontarDados) throws Exception;

    List<FecharMetaDetalhadoVO> consultarPorCliente(int cliente, boolean controlarAcesso, int nivelMontarDados) throws Exception;

    List<FecharMetaDetalhadoVO> consultarPorClienteAberturaMeta(int cliente, AberturaMetaVO aberturaMetaVO, int nivelMontarDados) throws Exception;

    Map<String, Integer> contarOrigem(Integer valorConsulta) throws Exception;

    List<FecharMetaDetalhadoVO> consultarComparecimentos(Integer codigoFecharMeta, Integer nivelMontarDados) throws Exception;

    List<FecharMetaDetalhadoVO> consultarReagendados(Integer codigoFecharMeta, Integer nivelMontarDados) throws Exception;

    List<FecharMetaDetalhadoVO> consultarFechamentos(Integer codigoFecharMeta, Integer nivelMontarDados) throws Exception;

    void setarAtributoEspecificoFecharMetaDetalhados(List<FecharMetaDetalhadoVO> lista, String identificador) throws Exception;

    List<Map<String, Object>> consultarPorClienteDia(Integer cliente, Date dia, Integer nivelMontarDados) throws Exception;

    ResultSet consultarVencidos(FecharMetaVO obj, Integer codColaborador, int nivelMontarDados, Integer empresa, String tipoVinculos, String codigoClientesObjecoes) throws Exception;

    void baterMetaPorFase(Integer codigoCliente, Integer codigoPessoa, Date dia, String fase, Integer historicoContato, Integer passivo, Integer indicado) throws Exception;

    void baterMetaPorFaseConversao(Integer codigoCliente, Integer codigoPessoa, Date dia, String fase, Integer historicoContato, Integer passivo, Integer indicado) throws Exception;

    ResultSet consultarClientesConversaoAgendados(FecharMetaVO obj, Date inicio, Date fim, Integer codColaborador, int nivelMontarDados, Integer empresa) throws Exception;

    FecharMetaDetalhadoVO consultarConversaoAgendados(Integer cliente, Date inicio, Date fim) throws Exception;

    void consultarClientesLigacaoAgendados(FecharMetaVO obj, Date dia, Integer codColaborador, int nivelMontarDados, Integer empresa, String tipoVinculos, String codigoClientesObjecoes) throws Exception;

    void inicializarDadosEnviarSMSColetivo(FecharMetaVO obj, MalaDiretaVO malaDiretaVO, UsuarioVO usuarioLogado) throws Exception;

    FecharMetaDetalhadoVO consultarPorCodigoIndicado(Integer codigoIndicado, int nivelMontarDados) throws Exception;

    List<FecharMetaDetalhadoVO> consultarLigacoes(Integer codigoFecharMeta, Integer nivelMontarDados) throws Exception;

    List<FecharMetaDetalhadoVO> consultarLigacoes(String codigosFecharMeta, Integer nivelMontarDados) throws Exception;

    void consultarClientesSessoesFinais(FecharMetaVO obj, int sessoesFinais, Integer codColaborador, Integer empresa, String codigoClienteObjecoes) throws Exception;

    void consultarClientesSemAgendamento(Date data, FecharMetaVO obj, ConfiguracaoDiasMetasTO semAgendamento, Integer codColaborador, Integer empresa, String codigoClientesObjecoes) throws Exception;

    void consultarClientesExAlunos(Date data, FecharMetaVO obj, ConfiguracaoDiasMetasTO tempoExAluno, Integer codColaborador, Integer empresa, String tipoVinculos, String consultarClientesExAlunos) throws Exception;

    void consultarVisitantesAntigos(Date data, FecharMetaVO obj, ConfiguracaoDiasMetasTO tempoExAluno, Integer codColaborador, Integer empresa, String tipoVinculos, String codigosPessoasObjecoes) throws Exception;

    FecharMetaDetalhadoVO consultarPorFaseIntervaloDataClienteColaborador(Date inicio, Date fim, String identificadorFase, Integer codigoCliente) throws Exception;

    void processarMetasBatidasAnteriormente(List<FecharMetaVO> fecharMetaVos) throws Exception;

    void processarMetaBatidaAnteriormente(FecharMetaDetalhadoVO fecharMetaDetalhado, FasesCRMEnum fase) throws Exception;
    
    void alterarSomenteCamposHistoricoContato(Integer codigoCliente, Integer pessoa, Date dia, String fase, Integer hist) throws Exception;
    
    Double resultadoCRM(Date inicio, Date fim, String fases, Integer empresa) throws Exception;
    
    void alterarSomenteCamposHistoricoContatoObteveSucesso(Integer codHistoricoContato, Integer codigoFecharMetaDetalhado) throws Exception;

    /**
     * Realiza a consulta paginada dos registros de fechamento detalhado de meta
     *
     * @param listaFecharMetaVO     A lista de fechamentos de meta
     * @param metaAgendados         Houve agendamento de meta ?
     * @param metaAgendadosLigacoes Houve ligações de agendamento de meta ?
     * @param metaIndicadores       Houve indicadores de meta ?
     * @param empresa               Código da empresa
     * @param confPaginacao         Configurações de paginação
     * @return A lista paginada de objetos de fechamento detalhado de meta
     * @throws Exception Caso ocorra algum problema
     */
    List<FecharMetaDetalhadoVO> consultarMetaDetalhadaPaginado(List<FecharMetaVO> listaFecharMetaVO, boolean metaAgendados,
                                                               boolean metaAgendadosLigacoes, boolean metaIndicadores,
                                                               Integer empresa, ConfPaginacao confPaginacao) throws Exception;

    List<FecharMetaDetalhadoVO> consultarMetaDetalhada(List<FecharMetaVO> listaFecharMetaVO, boolean metaAgendados,  boolean metaAgendadosLigacoes, boolean metaIndicadores, Integer empresa) throws Exception;

    boolean isAgendadoLigacao(Integer codigoFecharMetaDetalhado) throws Exception;

    Integer contarIndicadores(List<FecharMetaVO> listaFecharMetaVO, boolean agendamentosLigacoes, boolean indicacoesSemContato, boolean biIndicadorLigacaoPendente) throws Exception;

    List<FecharMetaDetalhadoVO> consultarIndicadores(List<FecharMetaVO> listaFecharMetaVO, boolean agendamentosLigacoes, boolean indicacoesSemContato, boolean biIndicadorLigacaoPendente, int nivelMontarDados) throws Exception;

    void alterarSomenteCampoObteveSucesso(boolean obteveSucesso, Integer codigoFecharMetaDetalhado) throws Exception;

    void alterarSomenteCampoRepescagemFecharMetaDetalhado(Boolean obteveSucesso, Integer codigoFecharMetaDetalhado) throws Exception;

    String inicializarDadosEnviarSMSColetivoNovoCRM(TipoMetaCRMTO tipoMetaCRMTO, MalaDiretaVO malaDiretaVO, UsuarioVO usuarioLogado) throws Exception;

    String inicializarDadosEnviarEmailColetivoNovoCRM(TipoMetaCRMTO tipoMetaCRMTO, MalaDiretaVO malaDiretaVO, UsuarioVO usuarioLogado) throws Exception;

    List<FecharMetaDetalhadoVO> consultarMetaDetalhadaTodasFases(List<FecharMetaVO> listaFecharMetaVO, String filtro) throws Exception;

    void alterarSomenteTeveContato(Integer codigoFecharMetaDetalhado) throws Exception;

    boolean verificarDetalhadoMetaBatidaCRMExtra(Integer malaDireta, Integer codCliente) throws Exception;

    List<FecharMetaDetalhadoVO> consultarListaBICRM(EmpresaVO empresa, Date dataInicio, Date dataFinal, List<UsuarioVO> usuarioVOList, String identificadorMeta, boolean somenteObteveSucesso, boolean somenteRepescagem, boolean total, boolean metaAtingidaBIResultado, List<FecharMetaVO> fecharMetaCRMExtra) throws Exception;

    List<FecharMetaDetalhadoVO> consultarListaBICRM(EmpresaVO empresa, Date dataInicio, Date dataFinal, List<UsuarioVO> usuarioVOList, String identificadorMeta, boolean somenteObteveSucesso, boolean somenteRepescagem, boolean total, boolean metaAtingidaBIResultado, List<FecharMetaVO> fecharMetaCRMExtra, boolean metaIndividual, String codigosMetaExtraIndividual, boolean mostrarTodosClientesDaMeta, Integer quantidadeConsultorSelecionado, String codigosMetasExtraSomada, boolean metaExtra) throws Exception;
    
    List<FecharMetaDetalhadoVO> consultarListaBICRMEspecifico(Date dataInicio, Date dataFinal, List<UsuarioVO> usuarioVOList, String identificadorMeta, boolean somenteObteveSucesso, boolean somenteRepescagem, boolean total, boolean metaAtingidaBIResultado, List<FecharMetaVO> fecharMetaCRMExtra,String codigosMetas,boolean mostrarTodosClientesDaMeta,Integer quantidadeConsultorSelecionado) throws Exception;
    
    boolean existeMetaConfiguracaoDiasPosVenda(Integer codigoConfiguracao) throws Exception;

    boolean consultarSituacaoObteveSucesso(Integer codigoFecharMetaDetalhado) throws Exception;
    void excluirPorIndicado(Integer codigoIndicado)throws Exception;
    void deletarAlunoFilaEsperaTurmaCrm(Integer codigoFila)throws Exception;
    void excluirFecharMetaDetalhadoDoAgendamentoAulaExperimental(AgendaVO agendaVO, Integer codigoConvite)throws Exception;

    void excluirFecharMetaDetalhadoMalaDireta(MalaDiretaVO malaDireta) throws Exception;
    
    ResultSet consultarLeadsHoje(FecharMetaVO obj, Integer codColaborador, int nivelMontarDados, Integer empresa) throws Exception;
    ResultSet consultarLeadsAcumulado(FecharMetaVO obj, Integer codColaborador, int nivelMontarDados, Integer empresa) throws Exception;

    FecharMetaVO pegarMetaAulaExperimental(Integer codigoCliente, Date dataAula) throws Exception;
    void decrementarMeta(FecharMetaVO fecharMeta) throws Exception;
    
    List<FecharMetaDetalhadoVO> consultarFecharMetaDetalhadoPorCodigoAgenda(Integer codAgenda, String identificadorMeta, int nivelMontarDados) throws Exception;

    /**
     * Calcula a totalização de metas não realizadas para os indicadores
     *
     * @param listaFecharMetaVO A lista de fechamento de meta
     * @return A quantidade encontrada
     */
    int contarTotalizacaoMetaNaoRealizadaIndicadores(List<FecharMetaVO> listaFecharMetaVO) throws Exception;

    /**
     * Calcula a quantidade de metas realizadas
     *
     * @param listaFecharMetaVO       lista de fechamento de meta
     * @param metaAgendamento         deve contabilizar as metas agendadas?
     * @param metaAgendamentoLigacoes deve contabilizar as metas agendadas por ligação?
     * @param metaIndicacoes          deve contabilizar as metas de indicação
     * @param codigoEmpresa           código da empresa em questão
     * @return A quantidade encontrada
     * @throws SQLException
     */
    int contarTotalizacaoMetaRealizadaAgendamentoIndicacoesLigacoes(List<FecharMetaVO> listaFecharMetaVO,
                                                                    Boolean metaAgendamento, boolean metaAgendamentoLigacoes,
                                                                    Boolean metaIndicacoes, Integer codigoEmpresa, Integer codFecharMeta) throws SQLException;

    /**
     * Calcula a quantidade de metas não realizadas
     *
     * @param listaFecharMetaVO lista de fechamento
     * @param codigoEmpresa     código da empresa em questão
     * @return A quantidade encontrada
     * @throws SQLException
     */
    int contarTotalizacaoMetaNaoRealizadaAgendamentoIndicacoes(List<FecharMetaVO> listaFecharMetaVO, Integer codigoEmpresa) throws SQLException;

    String obterSqlCalculoMetaFaltosos(Date dia, Integer codColaborador, Integer nrFaltaConf, Integer nrDuracaoPlano, Integer empresa, String tipoVinculos, String codigoClientesObjecoes, Date dataInicioFaltas, Integer codCliente) throws Exception;


    void consultarUltimoAcessoGympass(Date data, FecharMetaVO obj, ConfiguracaoDiasMetasTO tempoGympass, Integer codColaborador, Integer empresa, String tipoVinculos, String consultarUltimoAcessoGympass) throws Exception;

    void consultarFilaEsperaTurmaCRM(Date data, FecharMetaVO obj, Integer codColaborador, Integer empresa, String tipoVinculos, String clientesObjecoes) throws Exception;

}

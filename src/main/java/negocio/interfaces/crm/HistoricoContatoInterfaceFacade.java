package negocio.interfaces.crm;

import br.com.pactosolucoes.estrutura.paginacao.ConfPaginacao;
import negocio.comuns.arquitetura.UsuarioVO;
import negocio.comuns.basico.*;
import negocio.comuns.crm.AberturaMetaVO;
import negocio.comuns.crm.AgendaVO;
import negocio.comuns.crm.FecharMetaDetalhadoVO;
import negocio.comuns.crm.HistoricoContatoVO;
import negocio.comuns.crm.MalaDiretaEnviadaVO;
import negocio.comuns.crm.MalaDiretaVO;
import negocio.interfaces.basico.SuperInterface;

import java.sql.SQLException;
import java.util.Date;
import java.util.List;

/**
 * Interface reponsável por criar uma estrutura padrão de comunidação entre a camada de controle
 * e camada de negócio (em especial com a classe Façade). Com a utilização desta interface
 * é possível substituir tecnologias de uma camada da aplicação com mínimo de impacto nas demais.
 * Além de padronizar as funcionalidades que devem ser disponibilizadas pela camada de negócio, por intermédio
 * de sua classe Façade (responsável por persistir os dados das classes VO).
 */
public interface HistoricoContatoInterfaceFacade extends SuperInterface {

    public HistoricoContatoVO novo() throws Exception;

    public void incluir(HistoricoContatoVO obj) throws Exception;

    public void alterar(HistoricoContatoVO obj) throws Exception;

    public void excluir(HistoricoContatoVO obj) throws Exception;

    public HistoricoContatoVO consultarPorChavePrimaria(Integer codigo, int nivelMontarDados) throws Exception;

    public List consultarPorCodigo(Integer valorConsulta, boolean controlarAcesso, int nivelMontarDados) throws Exception;

    public void setIdEntidade(String aIdEntidade);

    public void incluirHistoricoContatoAgenda(HistoricoContatoVO obj, AgendaVO agenda, String tipoResultado, Integer empresa) throws Exception;

    public void incluirSemCommit(HistoricoContatoVO obj) throws Exception;

    public void incluirSemCommitSemAtualizarSintetico(HistoricoContatoVO obj) throws Exception;

    public HistoricoContatoVO executarInsercaoHistoricoPorPassivoOrClienteOrIndicado(String fase, String tipoContato, String agendaOrObjecao, Integer passivo, Integer indicado, Integer cliente, HistoricoContatoVO hist, UsuarioVO colaboradoreResponsavel, UsuarioVO responsavelCadastro, Integer empresa) throws Exception;

    public void excluirHistoricoContatoPorCodigoPassivo(Integer codPassivo) throws Exception;

    public void excluirHistoricoContatoPorCodigoIndicado(Integer codIndicado) throws Exception;

    public HistoricoContatoVO consultarHistoricoContatoPorCodigoPassivo(Integer codPassivo, boolean controlarAcesso, int nivelMontarDados) throws Exception;

    public HistoricoContatoVO consultarHistoricoContatoPorCodigoIndicado(Integer codIndicado, boolean controlarAcesso, int nivelMontarDados) throws Exception;

    public Date obterDataUltimoContato(FecharMetaDetalhadoVO fecharDetalhado, boolean controlarAcesso) throws Exception;

    public void alterarHistoricoContatoAgenda(HistoricoContatoVO obj, AgendaVO agenda, String botaoApertado, Integer empresa) throws Exception;

    public void gravarEmail(MalaDiretaVO mensagem, HistoricoContatoVO hist) throws Exception;

    public void alterarSemCommit(HistoricoContatoVO obj) throws Exception;

    public void gravarHistoricoContato(String tipoResultado, HistoricoContatoVO obj, AgendaVO agenda, MalaDiretaVO malaDiretaVO, Integer empresa) throws Exception;

    public void gravarHistoricoContatoComColaborador(String tipoResultado, HistoricoContatoVO obj, AgendaVO agenda, MalaDiretaVO malaDiretaVO, Integer empresa, UsuarioVO usuarioVO) throws Exception;

    public List consultarPorFiltrosTelaConsulta(UsuarioVO colaboradorConsultar, Date dataInicio, Date dataTermino, List<String> fases, Boolean filtroOperacaoReagendamento, Boolean filtroOperacaoConfirmacao, Boolean filtroOperacaoComparecimento, Boolean filtroOperacaoPessoal, Boolean filtroOperacaoTelefone, Boolean filtroOperacaoLigacaoSemContato, Boolean filtroOperacaoEmail, Boolean filtroOperacaoSMS, boolean controlarAcesso, int nivelMontarDados, EmpresaVO empresa) throws Exception;

    public void inicializarDadosHistoricoContatoCliente(HistoricoContatoVO historicoContatoVO) throws Exception;

    public void inicializarDadosPassivoRealizacaoContato(HistoricoContatoVO historicoContatoVO, AgendaVO agenda, FecharMetaDetalhadoVO obj, UsuarioVO usuarioLogado, UsuarioVO colaboradorResponsavel, MalaDiretaVO malaDiretaVO, MalaDiretaEnviadaVO malaDiretaEnviadaVO, List<TelefoneVO> listatelfoneClientePotencial, Boolean reagendamento) throws Exception;

    public void inicializarDadosIndicadoRealizacaoContato(HistoricoContatoVO historicoContatoVO, AgendaVO agenda, FecharMetaDetalhadoVO obj, UsuarioVO usuarioLogado, UsuarioVO colaboradorResponsavel, MalaDiretaVO malaDiretaVO, MalaDiretaEnviadaVO malaDiretaEnviadaVO, List<TelefoneVO> listaTelefoneIndicado, Boolean reagendamento) throws Exception;

    public HistoricoContatoVO inicializarDadosPessoasHistoricoContatoRealizacaoContato(HistoricoContatoVO hist, AgendaVO agenda, UsuarioVO usuarioLogado, UsuarioVO colaboradorResponsavel, MalaDiretaVO malaDiretaVO, MalaDiretaEnviadaVO malaDiretaEnviadaVO, List listatelfoneClientePotencial, List listaTelefoneIndicado, Boolean reagendamento, AberturaMetaVO abertura) throws Exception;

    public HistoricoContatoVO inicializarDadosAgendadosRealizacaoContato(HistoricoContatoVO hist, AgendaVO agenda, FecharMetaDetalhadoVO obj, UsuarioVO usuarioLogado, UsuarioVO colaboradorResponsavel, MalaDiretaVO malaDiretaVO, MalaDiretaEnviadaVO malaDiretaEnviadaVO, List<TelefoneVO> listatelfoneClientePotencial, List listaTelefoneIndicado, Boolean reagendamento) throws Exception;

    public void inicializarDadosVinteQuatroHorasRealizacaoContato(HistoricoContatoVO historicoContatoVO, AgendaVO agenda, FecharMetaDetalhadoVO obj, UsuarioVO usuarioLogado, UsuarioVO colaboradorResponsavel, MalaDiretaVO malaDiretaVO, MalaDiretaEnviadaVO malaDiretaEnviadaVO, List<TelefoneVO> listatelfoneClientePotencial, List listaTelefoneIndicado, Boolean reagendamento) throws Exception;

    public void inicializarDadosVendasRealizacaoContato(HistoricoContatoVO historicoContatoVO, AgendaVO agenda, FecharMetaDetalhadoVO obj, UsuarioVO usuarioLogado, UsuarioVO colaboradorResponsavel, MalaDiretaVO malaDiretaVO, MalaDiretaEnviadaVO malaDiretaEnviadaVO, List<TelefoneVO> listatelfoneClientePotencial, List listaTelefoneIndicado, Boolean reagendamento) throws Exception;

    public void inicializarDadosFaturamentoRealizacaoContato(HistoricoContatoVO historicoContatoVO, AgendaVO agenda, FecharMetaDetalhadoVO obj, UsuarioVO usuarioLogado, UsuarioVO colaboradorResponsavel, MalaDiretaVO malaDiretaVO, MalaDiretaEnviadaVO malaDiretaEnviadaVO, List<TelefoneVO> listatelfoneClientePotencial, List listaTelefoneIndicado, Boolean reagendamento) throws Exception;

    public void inicializarDadosFaltasRealizacaoContato(HistoricoContatoVO historicoContatoVO, AgendaVO agenda, FecharMetaDetalhadoVO obj, UsuarioVO usuarioLogado, UsuarioVO colaboradorResponsavel, MalaDiretaVO malaDiretaVO, MalaDiretaEnviadaVO malaDiretaEnviadaVO, List<TelefoneVO> listatelfoneClientePotencial, List listaTelefoneIndicado, Boolean reagendamento) throws Exception;

    public void inicializarDadosPerdaRealizacaoContato(HistoricoContatoVO historicoContatoVO, AgendaVO agenda, FecharMetaDetalhadoVO obj, UsuarioVO usuarioLogado, UsuarioVO colaboradorResponsavel, MalaDiretaVO malaDiretaVO, MalaDiretaEnviadaVO malaDiretaEnviadaVO, List<TelefoneVO> listatelfoneClientePotencial, List listaTelefoneIndicado, Boolean reagendamento) throws Exception;

    public void inicializarDadosGrupoRiscoRealizacaoContato(HistoricoContatoVO historicoContatoVO, AgendaVO agenda, FecharMetaDetalhadoVO obj, UsuarioVO usuarioLogado, UsuarioVO colaboradorResponsavel, MalaDiretaVO malaDiretaVO, MalaDiretaEnviadaVO malaDiretaEnviadaVO, List<TelefoneVO> listatelfoneClientePotencial, List listaTelefoneIndicado, Boolean reagendamento) throws Exception;

    public void inicializarDadosPosVendaRealizacaoContato(HistoricoContatoVO historicoContatoVO, AgendaVO agenda, FecharMetaDetalhadoVO obj, UsuarioVO usuarioLogado, UsuarioVO colaboradorResponsavel, MalaDiretaVO malaDiretaVO, MalaDiretaEnviadaVO malaDiretaEnviadaVO, List<TelefoneVO> listatelfoneClientePotencial, List listaTelefoneIndicado, Boolean reagendamento) throws Exception;

    public void inicializarDadosRenovacaoRealizacaoContato(HistoricoContatoVO historicoContatoVO, AgendaVO agenda, FecharMetaDetalhadoVO obj, UsuarioVO usuarioLogado, UsuarioVO colaboradorResponsavel, MalaDiretaVO malaDiretaVO, MalaDiretaEnviadaVO malaDiretaEnviadaVO, List<TelefoneVO> listatelfoneClientePotencial, List listaTelefoneIndicado, Boolean reagendamento) throws Exception;

    public void inicializarDadosAniversarianteRealizacaoContato(HistoricoContatoVO historicoContatoVO, AgendaVO agenda, FecharMetaDetalhadoVO obj, UsuarioVO usuarioLogado, UsuarioVO colaboradorResponsavel, MalaDiretaVO malaDiretaVO, MalaDiretaEnviadaVO malaDiretaEnviadaVO, List<TelefoneVO> listatelfoneClientePotencial, List listaTelefoneIndicado, Boolean reagendamento) throws Exception;

    public List consultarHistoricoContatoPassivo(Integer codPassivo, boolean controlarAcesso, int nivelMontarDados) throws Exception;

    public List consultarHistoricoContatoIndicado(Integer codIndicado, boolean controlarAcesso, int nivelMontarDados) throws Exception;

    public List consultarHistoricoContatoCliente(Integer codCliente, boolean controlarAcesso, int nivelMontarDados) throws Exception;

    public Integer countHistoricoContatoClient(Integer codCliente, boolean controlarAcesso, int nivelMontarDados) throws Exception;

    public List<HistoricoContatoVO> consultarHistoricoContatoCliente(Integer codCliente, boolean controlarAcesso, int nivelMontarDados, Integer limit, Integer offset) throws Exception;

    public Boolean consultarHistoricoPorCodigoIndicado(Integer codIndicado, boolean controlarAcesso, int nivelMontarDados) throws Exception;

    public void preencherHistoricoContatoComDadosPassivo(HistoricoContatoVO historicoContatoVO) throws Exception;

    public void preencherHistoricoContatoComDadosIndicado(HistoricoContatoVO historicoContatoVO) throws Exception;

    public void preencherHistoricoContatoComDadosCliente(HistoricoContatoVO historicoContatoVO) throws Exception;

    public Integer executarGravacaoVindoGymbot(MalaDiretaVO mensagemVO, Integer passivo, Integer cliente, String observacao, Integer codigoFluxoGymBot) throws Exception;

    public Integer executarGravacaoVindoEmail(MalaDiretaVO mensagemVO, Integer passivo, Integer cliente, Integer indicado, Integer codigoNotificacao, boolean wagienvi) throws Exception;

    public HistoricoContatoVO executarAlteracaoHistoricoPorPorPassivoOrClienteOrIndicado(String agendaOuObjecao, String fase, String tipoOperacao, Integer passivo, Integer indicado, Integer cliente, HistoricoContatoVO hist, UsuarioVO colaboradoreResponsavel, UsuarioVO responsavelCadastro, Integer empresa) throws Exception;

    public HistoricoContatoVO consultarUltimoHistoricoPorCliente(Integer codigoCliente, int nivelMontarDados) throws Exception;

    HistoricoContatoVO consultarUltimoHistoricoPorClienteComDiaFase(Integer codigoCliente,  Date dia, String fase, int nivelMontarDados) throws Exception;

    public HistoricoContatoVO consultarPrimeiroAgendamentoAposDataPorCliente(Integer codCliente, Date dia, int nivelMontarDados) throws Exception;

    public void consultarTotalLigacaoEDiasUltAcesso(HistoricoContatoVO obj) throws Exception;

    public Date obterVencimentoUltimoContrato(Integer valorConsulta) throws Exception;

    public long consultarDiasUltimoAcesso(Integer valorConsulta) throws Exception;

    public List<HistoricoContatoVO> consultarLigacoesCliente(Integer codCliente, boolean controlarAcesso, int nivelMontarDados) throws Exception;

    public String consultarGrauSatisfacao(Integer codCliente) throws Exception;

    public void inicializarDadosVencidosRealizacaoContato(HistoricoContatoVO historicoContatoVO, AgendaVO agenda, FecharMetaDetalhadoVO obj, UsuarioVO usuarioLogado, UsuarioVO colaboradorResponsavel, MalaDiretaVO malaDiretaVO, MalaDiretaEnviadaVO malaDiretaEnviadaVO, List<TelefoneVO> listatelfoneClientePotencial, List listaTelefoneIndicado, Boolean reagendamento) throws Exception;

    public List consultarPaginado(HistoricoContatoFiltroVO filtro, ConfPaginacao confPaginacao) throws Exception;

    public String consultarFasePassivo(int passivo,boolean faseAtual) throws SQLException;

    public String consultarFaseIndicado(int indicado,boolean faseAtual) throws SQLException;

    public String consultarFaseCliente(int cliente,boolean faseAtual) throws SQLException;

    public void inicializarDadosRealizacaoContato(HistoricoContatoVO historicoContatoVO, AgendaVO agenda, FecharMetaDetalhadoVO obj, UsuarioVO usuarioLogado, UsuarioVO colaboradorResponsavel, MalaDiretaVO malaDiretaVO, MalaDiretaEnviadaVO malaDiretaEnviadaVO, List<TelefoneVO> listatelfoneClientePotencial, List listaTelefoneIndicado, Boolean reagendamento) throws Exception;

    public String consultarHistoricoContatoClienteObjecao(boolean cliente, boolean passivo, boolean indicado, String tipoGrupoObjecao) throws Exception;

    public String consultarHistoricoContatoPessoaObjecao() throws Exception;

    public void gravarResposta(Integer codigoNotf, String resposta) throws Exception;

    public List<HistoricoContatoVO> relatorioContatoApp(String filtroNomePessoa,String filtroResposta, String filtroMensagem, Date inicio, Date fim,
                                                        UsuarioVO responsavel, Integer malaDireta, Integer empresa) throws Exception;

    public List<HistoricoContatoVO> consultarHistoricoContatoVO(FecharMetaDetalhadoVO fecharMetaDetalhadoVO, boolean controlarAcesso, Integer limit, Integer offset) throws Exception;
    public List<HistoricoContatoVO> consultarHistoricoContatoVO(FecharMetaDetalhadoVO fecharMetaDetalhadoVO, boolean controlarAcesso) throws Exception;

    public Integer countHistoricoContatoVO(FecharMetaDetalhadoVO fecharMetaDetalhadoVO, boolean controlarAcesso) throws Exception;

    public void preencherHistoricoContato(HistoricoContatoVO historicoContatoVO) throws Exception;

    public HistoricoContatoVO executarInsercaoHistoricoIndicado(String agendaOuObjecao, HistoricoContatoVO hist) throws Exception;

    public Boolean existeHistoricoDataExpecifica(FecharMetaDetalhadoVO fecharMetaDetalhadoVO) throws Exception;
    public void excluirHistoricoContatoDeConvite(Integer codigoConvite, Integer codigoAgenda) throws Exception;

    void inserirHistoricoConfirmarAgendamento(AgendaVO agendaVO, String fase, boolean excluir);

    public List<HistoricoContatoVO> consultarTelaCliente(Integer codigoCliente, Integer limit)throws Exception;

    public Integer obterTotalContatosPeriodo(int codigoEmpresa,Date inicio,Date fim) throws Exception;

    void alterarTodasObjecoes(Integer antigaObjecao, Integer novaObjecao) throws Exception;

    public HistoricoContatoVO consultarPoCodigoNotificacao(Integer codigoNotificacao, int nivelMontarDados) throws Exception;

    void incluirHistoricoContatoConversaoCliente(ClienteVO clienteVO);

    void atualizarHistoricoCadastroCliente(ClienteVO obj) throws Exception;

    public  HistoricoContatoVO consultarHistoricoContatoDataPorCliente (Integer codCliente, Date dia, int nivelMontarDados) throws  Exception;
}

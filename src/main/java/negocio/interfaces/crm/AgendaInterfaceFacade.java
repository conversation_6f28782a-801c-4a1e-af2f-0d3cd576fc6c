package negocio.interfaces.crm;

import br.com.pactosolucoes.enumeradores.TipoAgendaEnum;
import br.com.pactosolucoes.enumeradores.TipoTurmaEnum;
import negocio.comuns.arquitetura.UsuarioVO;
import negocio.comuns.basico.ColaboradorVO;
import negocio.comuns.basico.VinculoVO;
import negocio.comuns.contrato.ContratoVO;
import negocio.comuns.crm.*;
import negocio.interfaces.basico.SuperInterface;

import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * Interface reponsável por criar uma estrutura padrão de comunidação entre a camada de controle
 * e camada de negócio (em especial com a classe Façade). Com a utilização desta interface 
 * é possível substituir tecnologias de uma camada da aplicação com mínimo de impacto nas demais.
 * Além de padronizar as funcionalidades que devem ser disponibilizadas pela camada de negócio, por intermédio
 * de sua classe Façade (responsável por persistir os dados das classes VO).
 */
public interface AgendaInterfaceFacade extends SuperInterface {

    public AgendaVO novo() throws Exception;

    public void incluir(AgendaVO obj) throws Exception;

    public void incluirSemValidacao(AgendaVO obj) throws Exception;

    public void alterar(AgendaVO obj) throws Exception;

    public void excluir(AgendaVO obj) throws Exception;

    public AgendaVO consultarPorChavePrimaria(Integer codigo, int nivelMontarDados) throws Exception;

    public AgendaVO consultarValidandoMeta(FecharMetaDetalhadoVO obj, int nivelMontarDados) throws Exception;

    public List consultarPorCodigo(Integer valorConsulta, boolean controlarAcesso, int nivelMontarDados) throws Exception;

    public List consultarUltimoPorCodigo(Integer codigo,  int nivelMontarDados) throws Exception;

    public List consultarPorNomePassivo(String valorConsulta, int nivelMontarDados) throws Exception;

    public List consultarPorNomeIndicado(String valorConsulta, int nivelMontarDados) throws Exception;

    public List consultarPorSituacaoColaborador(String valorConsulta, int nivelMontarDados) throws Exception;

    public List consultarPorSituacaoCliente(String valorConsulta, int nivelMontarDados) throws Exception;

    public List consultarPorDia(Date prmIni, Date prmFim, boolean controlarAcesso, int nivelMontarDados) throws Exception;

    public List consultarPorTipoAgendamento(String valorConsulta, boolean controlarAcesso, int nivelMontarDados) throws Exception;

    public List consultarPorModalidade(String valorConsulta, boolean controlarAcesso, int nivelMontarDados) throws Exception;

    public void setIdEntidade(String aIdEntidade);

    public List consultarPorSituacaoResponsavelCadastro(String valorConsulta, int nivelMontarDados) throws Exception;

    public void excluirAgendaPorCodigoPassivo(Integer codPassivo) throws Exception;

    public void excluirHistoricoContatoPorCodigoIndicado(Integer codIndicado) throws Exception;

    public void executarRegraNegocioParaFecharMetaInclusao(Date dia, AgendaVO agenda, Integer colaboradorResponsavel, Integer passivo, Integer indicado, Integer cliente, HistoricoContatoVO hist) throws Exception;

    public void executarRegraNegocioParaFecharMetaAlteracaoPorPassivo(Date dia, AgendaVO agenda, Integer colaboradorResponsavel, PassivoVO passivo, HistoricoContatoVO hist) throws Exception;

    public void preencherDadosAgenda(AgendaVO agenda, HistoricoContatoVO historicoContato, String tipoOperacao, Boolean atualizarMeta) throws Exception;

    public void preencherDadosAgenda(AgendaVO agenda, HistoricoContatoVO historicoContato, String tipoOperacao, Boolean atualizarMeta, UsuarioVO usuarioVO) throws Exception;

    public Integer consultarSeExisteIndicadoAgendado(Integer indicado, boolean controlarAcesso) throws Exception;

    public Integer consultarSeExistePassivoAgendado(Integer passivo, boolean controlarAcesso) throws Exception;

    public Integer consultarSeExisteClienteAgendadoVisitaOuAula(Integer cliente, boolean controlarAcesso) throws Exception;

    public void executarRegraNegocioParaFecharMetaAlteracao(Date dia, AgendaVO agenda, Integer colaboradorResponsavel, Integer passivo, Integer indicado, Integer cliente, HistoricoContatoVO hist) throws Exception;

    public void excluirSemCommit(Integer codigo) throws Exception;

    public void alterarAgendadosComparecido(AgendaVO obj, UsuarioVO usuario, boolean controlarAcesso) throws Exception;

    public void cancelarAgendadosComparecido(AgendaVO obj) throws Exception;

    public void executarSomaTotalizador(AgendaVO obj);

    public void executarSubtracaoTotalizador(AgendaVO obj);

    public Integer inicializarTotalComparecidos(List<AgendaVO> lista);

    public Integer inicializarTotalComparecidosMes(List<AgendaVO> lista);

    public List consultarAgendadosConfirmacaoComparecimentoHoje(Date dia, Integer empresa, int nivelMontarDados) throws Exception;

    public List consultarAgendadosConfirmacaoComparecimentoMes(Integer empresa, int nivelMontarDados) throws Exception;

    public Integer inicializarTotalComparecidosHistorico(List<AgendaVO> lista);

    public List consultarAgendadosConfirmacaoComparecimentoHistorico(Integer codEmpresa, String valorConsulta, Date dataInicio, Date dataTermino, int nivelMontarDados) throws Exception;

    public AgendaVO consultarPorCodigoCliente(Integer valorConsulta, Date dia, boolean controlarAcesso, int nivelMontarDados) throws Exception;

    public ResultSet consultarAgendadosParaHojePassivoAndIndicado(String valorConsultar, Date date, boolean controlarAcesso) throws Exception;

    public AgendaVO marcarComparecimentoPessoa(Integer codigoCliente, UsuarioVO usuario, boolean contralarAcesso) throws Exception;

    public AgendaVO verificaAgendamento(Date dataInicial, Date dataFinal, Integer codigoPessoa) throws Exception;

    public AgendaVO gerarAgendamentoContratoFechado(ContratoVO contrato) throws Exception;

    public AgendaVO consultarPorCodigoPassivo(Integer valorConsulta, boolean controlarAcesso, int nivelMontarDados) throws Exception;
    
    public RelatorioAgendamentoTO consultarAgendamentos(Date inicio, Date fim, Date iniciolancamento, Date fimlancamento, String tipoAgenda,
           UsuarioVO responsavel, Integer empresa, Integer nrDiasContarResultado, Integer modalidade,
           Boolean somenteConvertidos, Boolean somenteExecutadas,
                                                        Boolean aconteceram, Boolean acontecer, Boolean gymPass) throws Exception;

    public RelatorioAgendamentoTO consultarAgendamentos(Date inicio, Date fim, Date iniciolancamento, Date fimlancamento, String tipoAgenda,
                                                        UsuarioVO responsavel, Integer empresa, Integer nrDiasContarResultado, Integer modalidade,
                                                        Boolean somenteConvertidos, Boolean somenteExecutadas,
                                                        Boolean aconteceram, Boolean acontecer, Boolean gymPass, ColaboradorVO colaborador,
                                                        List<Integer> codigos) throws Exception;

    public void preencherDadosAgendaIndicado(AgendaVO agenda, HistoricoContatoVO historicoContato) throws Exception;

    public void alterarAgendadosComparecidoSemCommit(AgendaVO obj, UsuarioVO usuario, boolean controlarAcesso) throws Exception;
    List<AgendaVO> consultarPorConviteAulaExperimental(TipoTurmaEnum tipoTurmaEnum,Integer codigoConviteAulaExperimental, int nivelMontarDados)throws Exception;
    AgendaVO consultarPorConvite(Integer codigoConvite,Integer codigoModalidade, Date dataAgendamento, int nivelMontarDados)throws Exception;
    void excluirAgendaConviteAulaExperimental(Integer codigoConvite,Integer codigoModalidade, Date dataAgendamento)throws Exception;

    public void verificaExisteAgendamentoDiaHoraMinuto(HistoricoContatoVO historicoContatoVO, AgendaVO agendaVO) throws Exception;

    /**
     * Retorna a lista de {@link AgendaVO} para um determinado {@link negocio.comuns.basico.ColaboradorVO} que seja o <code>AgendaVO.colaboradorResponsavel</code>
     * @param codigoColaborador Codigo do Colaborador
     * @param dataAgendamento Informa para quais {@link br.com.pactosolucoes.estudio.modelo.AgendaVO} em que a <code>dataagendamento</code> e maior que o parâmetro informado.
     * @param nivelmontardados Nível de detalhamento dos dados.
     * @return
     */
    List<AgendaVO> consultarPorColaboradorResponsavel(Integer codigoColaborador, Date dataAgendamento, int nivelmontardados) throws  Exception;

    /**
     * Altera a propriedade <code>AgendaVO.colaboradorResponsavel</code> das agendas a partir da data informada.
     * @param codigoColaboradorResponsavelAntigo código do {@link UsuarioVO} do <code>AgendaVO.colaboradorResponsavel</code> antigo.
     * @param codigoColaboradorResponsavelNovo código do {@link UsuarioVO} do <code>AgendaVO.colaboradorResponsavel</code> antigo.
     * @param data Data a partir da qual a <code>AgendaVO.dataagendamento</code> que será consultada
     * @throws Exception
     */
    void alterarConsultorResponsavelAgenda(Integer codigoColaboradorResponsavelAntigo, Integer codigoColaboradorResponsavelNovo, Date data) throws  Exception;

    /**
     * Verifica se um determinado {@link negocio.comuns.basico.ClienteVO} possui um {@link AgendaVO} para o <code>codigoColaborador</code> cujo <code>AgendaVO.dataAgendamento</code> cuja seja maior ou igual a <code>dataAgendamento</code>
     * @param codigoColaborador código do {@link negocio.comuns.basico.ColaboradorVO} responsavel pelo agendamento.
     * @param codigoCliente código do {@link negocio.comuns.basico.ClienteVO} para quem foi feito o {@link AgendaVO}.
     * @param dataAgendamento Data a patir da qual a {@link AgendaVO} sera consultada.
     * @return true caso o {@link negocio.comuns.basico.ClienteVO} possua {@link AgendaVO} a partir da <code>dataAgendamento</code> informada.
     * @throws Exception
     */
    Boolean clientePossuiAgendamentosColaborador(Integer codigoColaborador, Integer codigoCliente, Date dataAgendamento) throws  Exception;

    /**
     * Altera a propriedade <code>AgendaVO.colaboradorResponsavel</code> das agendas a partir da data informada.
     * @param codigoColaboradorResponsavelAntigo código do {@link UsuarioVO} do <code>AgendaVO.colaboradorResponsavel</code> antigo.
     * @param codigoColaboradorResponsavelNovo código do {@link UsuarioVO} do <code>AgendaVO.colaboradorResponsavel</code> antigo.
     * @param codigoCliente codigo do <code>AgendaVO.cliente</code> para qual sera consultada a agenda.
     * @param data Data a partir da qual a <code>AgendaVO.dataagendamento</code> que será consultada
     * @throws Exception
     */
    void alterarConsultorResponsavelAgenda(Integer codigoColaboradorResponsavelAntigo, Integer codigoColaboradorResponsavelNovo, Integer codigoCliente, Date data) throws  Exception;

    /**
     * Realiza a consulta das codigos dos colaboradores reponsaveis que possuem agendamento a partir da lista de colaboradores informada.
     * @param codigosColaboradores Código dos colaboradores responsáveis pela {@link AgendaVO}
     * @param dataAgendamento Data a partir da qual as {@link AgendaVO} serão consultadas.
     * @param nivelmontardadosDados Nível de dados a ser montado.
     * @return {@link List} com os codigos dos colaboradores responsáveis pelo agendamento
     */
    List<Integer> consultarPorColaboradoresResponsaveis(List<Integer> codigosColaboradores, final Integer cliente, final Integer empresa, Date dataAgendamento, int nivelmontardadosDados) throws  Exception;

    /**
     * Retorna a lista dos códigos dos agendamentos que possuiem o colaborador responsavel o mesmo do vinculo e sua data de agendamento e maior ou igual <code>dataAgendamento</code>,
     * e seu cliente seja o mesmo do {@link VinculoVO}
     * @param vinculos {@link VinculoVO} para quais serão pesquisados os agendamentos.
     * @param dataAgendamento {@link Date} para qual a <code>AgendaVO.dataAgendamento >= dataAgendamento</code>
     * @return
     */
    List<Integer> consultarCodigoAgendaFuturasPorVinculos(List<VinculoVO> vinculos, Date dataAgendamento, Integer empresa) throws  Exception;

    /**
     * Realiza a alteração do <code>AgendaVO.colaboradorResponsavel</code> para os codigos das {@link AgendaVO} informadas.
     * @param agendasTransferir Codigos das {@link AgendaVO} que serão alteradas.
     * @param usuario {@link UsuarioVO} que sera o novo <code>AgendaVO.colaboradorResponsavel</code>
     */
    void alterarConsultorResponsavelAgenda(List<Integer> agendasTransferir, UsuarioVO usuario) throws Exception;
    
    public AgendaVO consultarPorReposicao(Integer codigoReposicao, int nivelMontarDados) throws Exception;
    
    public void alterarAgendamentoPassivoIndicadoParaCliente(Integer cliente, Integer passivo, Integer indicado) throws Exception;

    void gravarAgendamentoAulaColetivaExperimental(Date dia, String hora, Integer cliente,
                                                   Integer modalidade,
                                                   Integer empresa,
                                                   Integer produtoFreePass,
                                                   Integer usuario,
                                                   Integer alunohorarioturma,
                                                   TipoAgendaEnum tipoAgendaEnum) throws Exception;
}

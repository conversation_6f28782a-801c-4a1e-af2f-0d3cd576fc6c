/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package negocio.interfaces.crm;

import java.util.List;

import negocio.comuns.basico.EmpresaVO;
import negocio.comuns.crm.ConversaoLeadVO;
import negocio.interfaces.basico.SuperInterface;

/**
 * <AUTHOR>
 */
public interface ConversaoLeadInterfaceFacade extends SuperInterface {


    public void incluir(ConversaoLeadVO obj) throws Exception;

    public void alterar(ConversaoLeadVO obj) throws Exception;

    public void excluir(ConversaoLeadVO obj) throws Exception;

    public ConversaoLeadVO consultarPorChavePrimaria(Integer codigo, int nivelMontarDados) throws Exception;

    public List<ConversaoLeadVO> consultarPorNome(String valorConsulta, boolean controlarAcesso, EmpresaVO empresa, int nivelMontarDados) throws Exception;

    public List<ConversaoLeadVO> consultarPorPassivo(Integer codigoPassivo, int nivelMontarDados) throws Exception;

    public List<ConversaoLeadVO> consultarPorResponsavel(Integer codigoResponsavel, Integer empresa, Boolean tradada, int nivelMontarDados) throws Exception;

    public ConversaoLeadVO consultarUltimaConversao(Integer codigoCliente, Integer codigoPessoa, Integer codigoEmpresa, int nivelMontarDados) throws Exception;

    List<ConversaoLeadVO> consultarTodosAcao(String buzzlead) throws Exception;
}

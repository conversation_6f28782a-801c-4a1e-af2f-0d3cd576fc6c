package negocio.interfaces.crm;

import negocio.comuns.crm.QuarentenaVO;
import negocio.interfaces.basico.SuperInterface;

import java.util.Date;
import java.util.List;

/**
 * Interface reponsável por criar uma estrutura padrão de comunidação entre a camada de controle
 * e camada de negócio (em especial com a classe Façade). Com a utilização desta interface
 * é possível substituir tecnologias de uma camada da aplicação com mínimo de impacto nas demais.
 * Além de padronizar as funcionalidades que devem ser disponibilizadas pela camada de negócio, por intermédio
 * de sua classe Façade (responsável por persistir os dados das classes VO).
 */
public interface QuarentenaInterfaceFacade extends SuperInterface {

    void iniciar(QuarentenaVO obj) throws Exception;

    void iniciarRetroativa(QuarentenaVO obj) throws Exception;

    void encerrar(QuarentenaVO obj) throws Exception;

    QuarentenaVO consultarPorCodigo(int codigo, int nivelMontarDados) throws Exception;

    void alterarDadosGeradosNaParalisacao(Date dataInicio, Date dataFim) throws Exception;

    QuarentenaVO obterAtiva(int codEmpresa) throws Exception;

    QuarentenaVO obterUltimaQuarentenaEncerrada(int codEmpresa) throws Exception;

    List<QuarentenaVO> obterTodas(int codEmpresa) throws Exception;
}

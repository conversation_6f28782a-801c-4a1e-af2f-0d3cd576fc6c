package negocio.interfaces.crm;

import negocio.comuns.crm.FecharMetaVO;
import negocio.comuns.crm.HistoricoContatoVO;
import negocio.comuns.crm.IndicacaoVO;
import negocio.comuns.crm.IndicadoVO;
import negocio.interfaces.basico.SuperInterface;

import java.sql.SQLException;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * Interface reponsável por criar uma estrutura padrão de comunidação entre a camada de controle
 * e camada de negócio (em especial com a classe Façade). Com a utilização desta interface 
 * é possível substituir tecnologias de uma camada da aplicação com mínimo de impacto nas demais.
 * Além de padronizar as funcionalidades que devem ser disponibilizadas pela camada de negócio, por intermédio
 * de sua classe Façade (responsável por persistir os dados das classes VO).
 */
public interface IndicacaoInterfaceFacade extends SuperInterface {

    public IndicacaoVO novo() throws Exception;

    public void incluir(IndicacaoVO obj) throws Exception;

    public void alterar(IndicacaoVO obj) throws Exception;

    public void excluir(IndicacaoVO obj) throws Exception;

    public IndicacaoVO consultarPorChavePrimaria(Integer codigo, int nivelMontarDados) throws Exception;

    public List consultarPorCodigo(Integer valorConsulta, boolean controlarAcesso, int nivelMontarDados) throws Exception;

    public List consultarPorNomeResponsavelCadastro(String valorConsulta, int nivelMontarDados) throws Exception;

    public List consultarPorNomeColaboradorResponsavel(String valorConsulta, int nivelMontarDados) throws Exception;

    public List consultarPorNomeClienteQueIndicou(String valorConsulta, int nivelMontarDados) throws Exception;

    public List consultarPorNomeColaboradorQueIndicou(String valorConsulta, int nivelMontarDados) throws Exception;

    public List consultarPorClienteQueIndicou(Integer valorConsulta, boolean controlarAcesso, int nivelMontarDados) throws Exception;

    public List consultarPorDia(Date prmIni, Date prmFim, boolean controlarAcesso, int nivelMontarDados) throws Exception;

    public List<IndicacaoVO> consultarPorPeriodoColaboradorCliente(Date prmIni, Date prmFim, Integer colaborador, Integer cliente, boolean controlarAcesso, int nivelMontarDados) throws Exception;

    public List consultarPorNomeIndicado(String valorConsulta, boolean controlarAcesso, int nivelMontarDados) throws Exception;

    public List consultarPorSituacaoCliente(String valorConsulta, int nivelMontarDados) throws Exception;

    public List consultarPorNomeIndicador(String valorConsulta, int nivelMontarDados) throws Exception;

    public void setIdEntidade(String aIdEntidade);

    /**
     * Método que invoca os metodos exclui os dependentes de Indicação e indicacao
     * @param obj
     * @throws Exception
     * <AUTHOR>
     */
    public void excutarExclusaoDependentesIndicacao(IndicacaoVO obj) throws Exception;

    /**
     * Método responsavel por excluir FecharMetaVO e FecharMEtaDetalhadaVO quando é excluido uma Indicação
     * @param obj
     * @throws Exception
     * <AUTHOR>
     */
    public void executarExclusaoDependentes(IndicacaoVO obj) throws Exception;

    public List consultarPorColaboradorQueIndicou(Integer valorConsulta, boolean controlarAcesso, int nivelMontarDados) throws Exception;

    public String consultarJSON(Integer empresa,String sEcho,Integer offset, Integer limit, String clausulaLike, Integer colOrdenar, String dirOrdenar,Date dtInicio, Date dtFim) throws Exception;

    public IndicacaoVO consultarPorCodigoIndicado(Integer codigoIndicado, int nivelMontarDados) throws Exception;

    public IndicacaoVO consultarPorCodigoClienteIndicado(Integer codigoClienteIndicado, int nivelMontarDados) throws Exception;

    public void incluirIndicacaoMeta(IndicacaoVO obj, FecharMetaVO fecharMeta) throws Exception;

    public void incluirSomenteIndicacao(IndicacaoVO obj) throws Exception;

    public void incluirHistoricoContatoIndicado(IndicacaoVO indicacaoVO,  IndicadoVO indicadoVO, HistoricoContatoVO hist, String agendaOuObjecao) throws Exception;

    List consultarParaImpressao(String filtro, String ordem, String campoOrdenacao, Date dtInicio, Date dtFim, Integer empresa, String clausulaLike) throws Exception;

    Map<String, Number> countResultadosIndicacao(Integer empresa, Date dataInicio, Date dataFim) throws Exception;
}

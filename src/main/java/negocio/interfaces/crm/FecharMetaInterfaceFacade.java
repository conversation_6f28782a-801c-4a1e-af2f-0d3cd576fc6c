package negocio.interfaces.crm;

import br.com.pactosolucoes.enumeradores.FasesCRMEnum;
import negocio.comuns.arquitetura.UsuarioVO;
import negocio.comuns.basico.ColaboradorVO;
import negocio.comuns.contrato.ContratoVO;
import negocio.comuns.crm.*;
import negocio.facade.jdbc.crm.fecharMetaDetalhado.FecharMetaDetalhado;
import negocio.interfaces.basico.SuperInterface;
import relatorio.negocio.comuns.basico.ItemRelatorioTO;

import java.util.Date;
import java.util.List;

/**
 * Interface reponsável por criar uma estrutura padrão de comunidação entre a camada de controle
 * e camada de negócio (em especial com a classe Façade). Com a utilização desta interface
 * é possível substituir tecnologias de uma camada da aplicação com mínimo de impacto nas demais.
 * Além de padronizar as funcionalidades que devem ser disponibilizadas pela camada de negócio, por intermédio
 * de sua classe Façade (responsável por persistir os dados das classes VO).
 */
public interface FecharMetaInterfaceFacade extends SuperInterface {


    public FecharMetaVO novo() throws Exception;

    public void incluir(FecharMetaVO obj) throws Exception;

    public void incluir(FecharMetaVO obj,  FasesCRMEnum fase) throws Exception;

    public void alterar(FecharMetaVO obj) throws Exception;

    public void excluir(FecharMetaVO obj) throws Exception;

    public FecharMetaVO consultarPorChavePrimaria(Integer codigo, int nivelMontarDados) throws Exception;

    public List consultarPorCodigo(Integer valorConsulta, boolean controlarAcesso, int nivelMontarDados) throws Exception;

    public List consultarPorDataRegistro(Date prmIni, Date prmFim, boolean controlarAcesso, int nivelMontarDados) throws Exception;

    public FecharMetaVO consultarMetaPorDiaPorColaboradorResponsavel(String identificador, Date dia, Integer codigo, boolean controlarAcesso, int nivelMontarDados, Integer empresa) throws Exception;

    public FecharMetaVO consultarMetaPorDiaPorColaboradorResponsavel(String identificador, Date dia, String sql, boolean controlarAcesso, int nivelMontarDados, Integer empresa) throws Exception;

    public List consultarPorSituacaoColaborador(String valorConsulta, int nivelMontarDados) throws Exception;

    public List consultarPorIdentificadorMeta(String valorConsulta, boolean controlarAcesso, int nivelMontarDados) throws Exception;

    public List consultarFecharMetaRetencao(Integer fecharMeta, int nivelMontarDados) throws Exception;

    public List consultarFecharMetaVenda(Integer fecharMeta, int nivelMontarDados) throws Exception;

    public FecharMetaVO consultarPorIdentificadorMetaPorDiaPorColaborador(String valorConsulta, Date dia, Integer colaborador, boolean controlarAcesso, Integer empresa, int nivelMontarDados) throws Exception;

    List<String> consultarTipoColaborador(Integer colaborador) throws Exception;

    Boolean consultarColaboradorResFase(Integer fase, List<String> tipos) throws Exception;

    public void setIdEntidade(String aIdEntidade);

    public FecharMetaDetalhadoVO executarPersistenciaFechaMetaDetalhado(Date dia, String identificadorMeta, Integer colaboradorResponsavel, Integer passivo, Integer indicado, Integer cliente, Integer empresa, HistoricoContatoVO hist, Boolean atualizaMeta) throws Exception;

    public void executarExclusaoFecharMetaPassivo(List listaFecharMetaDetalhado) throws Exception;

    public Long executarTodosHistorico(FecharMetaVO fecharMeta, Long totalizadorSelecionadoHistorico);

    public Long executarTodosMes(FecharMetaVO fecharMeta, Long totalizadorSelecionadoMes);

    public void excluirSomenteMaeFecharMeta(FecharMetaVO obj) throws Exception;

    public void executarValidacaoAgendadoComprouContrato(ContratoVO contrato, AgendaVO agendaMarcadaPresenca) throws Exception;

    public void executarAtualizacaoMetaFaturamentoAndVenda(ContratoVO contrato) throws Exception;

    public void executarAtualizacaoMetaAtingidaPessoasComparecidas(FecharMetaDetalhadoVO fecharMetaDetalhadoVO, Integer contrato, Long quantidade, Boolean somar) throws Exception;

    public void executarAtualizacaoMetaAtingida(FecharMetaDetalhadoVO fecharMetaDetalhadoVO, Integer contrato, Long quantidade, Boolean somar,Boolean alterarFecharMeta) throws Exception;

    public void executarAtualizacaoMetaRepescagem(FecharMetaDetalhadoVO fecharMetaDetalhadoVO, Integer contrato, Long quantidade, Boolean somar) throws Exception;

    public void executarPersistenciaFechaMetaDetalhadoParaAgendamento(Date dia, AgendaVO agenda, Integer colaboradorResponsavel, Integer passivo, Integer indicado, Integer cliente, HistoricoContatoVO hist) throws Exception;

    public void executarPersistenciaFechaMetaDetalhadoParaAgendadosAmanhã(Date dia, AgendaVO agenda, Integer colaboradorResponsavel, Integer passivo, Integer indicado, Integer cliente, HistoricoContatoVO hist) throws Exception;

    public void executarAtualizacaoMetaRegraParaAgendamentoPorPassivo(FecharMetaDetalhadoVO fecharMetaDetalhadoVO, Long quantidade, Boolean somar) throws Exception;

    public FecharMetaVO consultarPorIdentificadorMetaPorCodigoIndicacaoPorColaborador(String valorConsulta, Integer codigoIndicacao, Integer colaborador, boolean controlarAcesso, int nivelMontarDados) throws Exception;

    public void executarAtualizacaoMetaAtingidaPorIndicado(FecharMetaVO fecharMetaVO, Long quantidade, Boolean somar) throws Exception;

    public void incluirFecharMetaVos(Integer aberturaMetaPrm, List objetos) throws Exception;

    public void incluirFecharMetaVos(Integer aberturaMetaPrm, List objetos, FasesCRMEnum fase) throws Exception;

    public void alterarFecharMetaVos(Integer aberturaMetaPrm, List objetos) throws Exception;

    public void executarPreencherMetaDetalhadoFaturamentoOrVenda(FecharMetaVO fecharMeta, Integer codigoColaborador, Date dia) throws Exception;

    public void alterarFecharMetaVosVindoAberturaMeta(Integer aberturaMetaPrm, List objetos) throws Exception;

    public void executarValidacaoQuandoGravaHistoricoContatoAlcancaMetaAtingida(Date dia, String identificadorMeta, Integer codigoFechaMetaDetalhado,
                                                                                Boolean metaEmAberta, FecharMetaDetalhado fecharMetaDetalhadoDAO) throws Exception;
    
    public void executarAtualizacaoRespescagemObjecao(Integer codigoFechaMetaDetalhado,
    						FecharMetaDetalhado fecharMetaDetalhadoDAO) throws Exception ;

    public void executarValidacaoQuandoGravaAgendaAlcancaMetaAtingida(Date dia, AgendaVO agenda,
                                                                      String identificadorMeta, Integer codigoFechaMetaDetalhado, Boolean incluir) throws Exception;

    public Long executarTodosFecharMetaDetalhados(FecharMetaVO fecharMeta, Long totalizadorSelecionadoHoje);

    public void inicializarDadosParaTelaMetaDetalhando(FecharMetaVO fecharMetaVO);

    public Long executarTotalizadorSelecionado(List<FecharMetaDetalhadoVO> lista, Long totalizadorSelecionado);

    public List<FecharMetaVO> consultarPorPeriodoColaboradoresResponsaveis(Date ini, Date fim, List<ColaboradorVO> colaboradores, boolean metaAberta, boolean controlarAcesso, Integer empresa, int nivelMontarDados) throws Exception;

    public double contarColaboradoresNasMetasPorPeriodoColaboradoresResponsaveis(Date ini, Date fim, List<ColaboradorVO> colaboradores, boolean metaAberta, boolean controlarAcesso, Integer empresa, int nivelMontarDados) throws Exception;

    public Integer obterCodigoFecharMetaColaboradorPorDia(Integer codigoColaborador, Date data, String identificador) throws Exception;

    public void alteraSemSubordinada(FecharMetaVO obj) throws Exception;
    public void alteraSemSubordinada(FecharMetaVO obj, boolean ignorarValidacao) throws Exception;

    List<MetaCRMTO> consultarMeta(Date dataInicioMeta, Date dataFimMeta, int nivelMontarDados, List<UsuarioVO> usuarioVOList, Integer empresa, boolean apenasCalculadas) throws Exception;

    List<MetaCRMTO> consultarMetaToServlet(Date dataInicioMeta, Date dataFimMeta, List<UsuarioVO> usuarioVOList, Integer empresa, boolean apenasCalculadas) throws Exception;

    public List<FecharMetaVO> consultarFecharMeta(int aberturaMeta, int nivelMontarDados, String... fases) throws Exception;
    
    public boolean metaAbertaFechamentaDetalhado(Integer fechametaDetalhado) throws Exception;

    public List consultarFecharMetaEstudio(Integer fecharMeta, int nivelMontarDados) throws Exception;

    public FecharMetaDetalhadoVO inserirFecharMetaDetalhadoIndicado(FecharMetaVO fecharMeta, Integer indicado) throws Exception;

    public Integer totalContatoSemBaterMeta(List<FecharMetaVO> fecharMetaVOList) throws Exception;

    boolean existeMetaCRMExtraGeradaUsuarioDia(Integer malaDireta, Integer usuario, Date dia, Integer empresa) throws Exception;

    void excluirMetaExtraGeradaComFalhaUDia(Integer malaDireta, Date dia) throws Exception;

    public String consultarGraficoResultadoBICRM(Date dataInicio, Date dataFim, List<UsuarioVO> usuarioVOList) throws Exception;

    public Double consultarTotalIndicadoBI(Date ini, Date fim, List<UsuarioVO> usuarioVOList, boolean metaAberta, boolean controlarAcesso, Integer empresa, int nivelMontarDados) throws Exception;
    public List<FecharMetaVO> consultarPorPeriodoColaboradoresResponsaveisBI(Date ini, Date fim, List<UsuarioVO> usuarioVOList, boolean metaAberta, boolean controlarAcesso, Integer empresa, int nivelMontarDados) throws Exception;

    public double contarColaboradoresNasMetasPorPeriodoColaboradoresResponsaveisBI(Date ini, Date fim, List<UsuarioVO> usuarioVOList, boolean metaAberta, boolean controlarAcesso, Integer empresa, int nivelMontarDados) throws Exception;

    String consultarGraficoResultadoBICRMLegenda(Date dataInicio, Date dataFim, List<UsuarioVO> usuarioVOList) throws Exception;

    List<FecharMetaVO> consultarBIDetalhado(Date dataInicio, Date dataFim, String identificadorMeta, List<UsuarioVO> usuariosSelecionados, Integer empresa) throws Exception;

    boolean metaCRMExtraUtilizada(Integer malaDireta) throws Exception;

    List<FecharMetaVO> consultarFecharMetaPorMalaDiretaMetaExtra(Integer malaDireta, int nivelMontarDados) throws Exception;

    List<ItemRelatorioTO> consultarImpressaoGraficoDesempenhoResultadoBICRM(Date dataInicio, Date dataFim, List<UsuarioVO> usuarioVOList) throws Exception;

    public Double obterMetaAlcancado(Date dataInicio, Date dataFim, int empresa) throws Exception;

    public Double obterSomaMeta(Date dataInicio, Date dataFim, int empresa) throws Exception;

    public void alterarFecharMetaMalaDireta(MalaDiretaVO malaDireta) throws Exception ;

    void excluirFecharMetaMalaDireta(MalaDiretaVO malaDireta) throws Exception;
    public void adicionarLeadMetaDoDia(ConversaoLeadVO conversaoLeadVO, Date data) throws Exception;
    public List consultarFecharMetaLeads(Integer fecharMeta, int nivelMontarDados) throws Exception;
    
    public void removerAgendamentoNasMetas(Integer agenda) throws Exception;

    public void removerMetasQueJaTiveramAgendamento(FecharMetaDetalhadoVO excluir) throws Exception;

    public Integer countFecharMetaExtra(Integer maladireta, Integer aberturameta) throws Exception;
}

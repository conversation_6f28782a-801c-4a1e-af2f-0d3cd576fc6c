package negocio.interfaces.vendas;

import negocio.facade.jdbc.vendas.VendasOnlineCampanhaIcvVO;
import negocio.interfaces.basico.SuperInterface;
import servicos.vendasonline.dto.VendaDTO;

import java.util.Date;

/**
 * Created by ulisses on 23/01/2023.
 */
public interface VendasOnlineCampanhaIcvInterfaceFacade extends SuperInterface {

    void incluir(VendasOnlineCampanhaIcvVO vendasOnlineCampanhaIcvVO)throws Exception;
    void alterarInfoPessoa(VendaDTO vendaDTO, Integer codigoVendasOnlineCampanhaIcv, Integer pessoa, Date dataConclusao)throws Exception;
    void alterarInfoVenda(Integer codigoVendasOnlineCampanhaIcv, Integer contrato, Integer vendaAvulsa, Integer pessoa)throws Exception;
    VendasOnlineCampanhaIcvVO consultar(Integer codigo)throws Exception;
}

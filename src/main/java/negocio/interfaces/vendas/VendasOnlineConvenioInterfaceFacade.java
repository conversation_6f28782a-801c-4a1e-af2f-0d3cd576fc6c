package negocio.interfaces.vendas;

import negocio.comuns.arquitetura.UsuarioVO;
import negocio.facade.jdbc.vendas.VendasConfigVO;
import negocio.facade.jdbc.vendas.VendasOnlineConvenioVO;
import negocio.interfaces.basico.SuperInterface;

import java.util.List;

/**
 * Created with IntelliJ IDEA.
 * User: <PERSON><PERSON>
 * Date: 23/06/2020
 */
public interface VendasOnlineConvenioInterfaceFacade extends SuperInterface {

    void gravar(VendasConfigVO vendasConfigVO, UsuarioVO usuarioVO, List<VendasOnlineConvenioVO> listaAtual, boolean formaPagamento) throws Exception;

    List<VendasOnlineConvenioVO> consultarPorEmpresa(Integer empresa, boolean somenteConvenioAtivo, int nivelMontarDados, boolean formaPagamento) throws Exception;

}

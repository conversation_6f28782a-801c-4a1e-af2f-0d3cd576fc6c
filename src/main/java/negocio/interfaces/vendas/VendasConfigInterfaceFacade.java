package negocio.interfaces.vendas;

import negocio.comuns.arquitetura.UsuarioVO;
import negocio.comuns.basico.FormaPagamentoPlanoProdutoTO;
import negocio.comuns.plano.ProdutoVO;
import negocio.facade.jdbc.vendas.*;
import negocio.interfaces.basico.SuperInterface;

import java.util.List;

public interface VendasConfigInterfaceFacade extends SuperInterface {

    void gravarConfig(VendasConfigVO vendasConfigVO) throws Exception;

    void gravarModalidadeCarrousel(ModalidadeCarrouselVendasOnlineVO modalidadeCarrousel) throws Exception;

    void gravarConfigModalidadeCarrossel(ConfigModalidadeCarrosselVendasOnlineVO configModalidadeCarrousel) throws Exception;

    void gravarCaptacaoLeads(CaptacaoLeadsVendasOnlineVO capLeads) throws Exception;

    void gravarContatoRodape(ContatoRodapeVendasOnlineVO contatoRodape) throws Exception;

    void gravarAgendaVendasOnline(AgendaVendasOnlineVO vo) throws  Exception;

    void gravarIntegracoesHotsite(VendasConfigVO configVO) throws Exception;

    void gravarAgendaVendasOnlineLinkVisitante(AgendaVendasOnlineVO vo, UsuarioVO us, Integer config, Integer empresa) throws  Exception;

    void gravarMenu(MenuVendasOnlineVO menuVendasOnline) throws Exception;

    void gravarMultiEmpresaConfig(MultiEmpresaConfigsVendasOnlineVO multiEmpresaConfigsVendasOnlineVO) throws Exception;

    void gravarPaginaInicial(PaginaInicialVendasOnlineVO pagInicialVendasOnline, Integer opcao) throws Exception;

    void gravarFotoFachada(FotoFachadaVendasOnlineVO fotoFachadaVendasOnlineVO) throws Exception;

    void gravarPlanoSiteVendasOnline(PlanosSiteVendasOnlineVO planoSite) throws Exception;

    VendasConfigVO config(Integer empresa) throws Exception;

    List<FormaPagamentoPlanoProdutoTO> obterVendasOnlineConvenioConfigs(Integer empresa, boolean formaPagamento) throws Exception;

    void gravarImagem(ImagensAcademiaVendasVO imagem) throws Exception;

    List<ModalidadeCarrouselVendasOnlineVO> consultarModalidadesCarrouselVendasOnline(Integer codConfigVendasOnline, Boolean banner) throws Exception;

    ConfigModalidadeCarrosselVendasOnlineVO consultarConfigModalidadesCarrosselVendasOnline(Integer codConfigVendasOnline) throws Exception;

    List<AulasVendasOnline> consultarAulasVendasOnlineAtivaPorEmpresa(Integer codigoEmpresa) throws Exception;

    List<PlanosSiteVendasOnlineVO> consultarPlanosSiteVendasOnline(Integer codConfigVendasOnliner) throws Exception;

    PaginaInicialVendasOnlineVO consultarPaginaInicialVendasOnline(Integer codConfigVendasOnline, Integer posicao) throws Exception;

   FotoFachadaVendasOnlineVO consultarFotoFachadaVendasOnline(Integer codConfigVendasOnline) throws Exception;

    CaptacaoLeadsVendasOnlineVO consultarCapLeadsVendasOnline(Integer codConfigVendasOnline) throws Exception;

    ContatoRodapeVendasOnlineVO consultarContatoRodapeVendasOnline(Integer codConfigVendasOnline) throws Exception;

    List<TurmaEHorarioVendasOnlineVO> consultarTurmaEHorarioVendasOnlinePorEmpresa(Integer codigoEmpresa) throws Exception;
    List<TurmaEHorarioVendasOnlineVO> consultarTurmaEHorarioVendasOnlineLinkVisitantePorEmpresa(Integer codigoEmpresa) throws Exception;

    List<AulasVendasOnline> consultarAulasVendasOnlinePorEmpresa(Integer codigoEmpresa) throws Exception;

    List<AulasVendasOnline> consultarAulasVendasOnlineLinkVisitantePorEmpresa(Integer codigoEmpresa, Integer config) throws Exception;

    List<ProdutoVO> consultarProdutosVendasOnlinePorTurma(Integer turma) throws Exception;

    void mudarStatusAulaVendasOnline(Integer codigo) throws Exception;

    boolean consultarDominioProprioHotsite(Integer empresa) throws Exception;

    void alterarDominioProprioHotsite(Integer empresa, boolean dominioproprio) throws Exception;

    void excluirAulaVendasOnline(VendasConfigVO vendasConfigVO, UsuarioVO usuarioVO, Integer codigo, boolean linkVisitante, Integer codigoTurma) throws Exception;

    TurmaEHorarioVendasOnlineVO consultarTurmaEHorarioVendasOnlinePorCodigoTurma(Integer codigoHorario) throws Exception;

    MultiEmpresaConfigsVendasOnlineVO consultarMultiEmpresaCongifsVendasOnline() throws Exception;

    MenuVendasOnlineVO consultarMenuVendasOnline(Integer codConfigVendasOnline) throws Exception;

    void gravarImagemModalidadeCarrousel(String fotoKeyModalidadeCarrousel, Integer codModalidadeCarrousel) throws Exception;

    List<ImagensAcademiaVendasVO> imagens(Integer empresa) throws Exception;

    void excluirImagem(ImagensAcademiaVendasVO imagem) throws Exception;

    void excluirModaCarrousel(Integer codModaCarrousel) throws Exception;

    void excluirPlanoSite(Integer codPlanoSite) throws Exception;

    String tagPagamento(String chave, Integer empresa, Integer codigoCliente, boolean cobrarParcelasEmAberto) throws Exception;

    String tagPagamentosms(String chave, Integer empresa, Integer codigoCliente, boolean cobrarParcelasEmAberto) throws Exception;
    List<ModalidadeCarrouselVendasOnlineVO> consultarModalidadesCarrouselVendasOnlinePorAgrupamento(Integer idAgrupamento, boolean banner) throws Exception;

    List<EmpresaHotsiteVO> consultarEmpresasHotsite() throws  Exception;

    List<EmpresaHotsiteVO> consultarEmpresasHotsite(Integer empresa) throws  Exception;

    void incluirAtualiza(Integer empresa) throws  Exception;

    Integer consultarCampanhaCupomDescontoIndicacoes(Integer empresa) throws Exception;

    Integer obterTipoAulasLinkVisitante(Integer configVendas) throws Exception;
}

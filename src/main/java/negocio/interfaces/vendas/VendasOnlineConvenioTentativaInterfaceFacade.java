package negocio.interfaces.vendas;

import negocio.comuns.arquitetura.UsuarioVO;
import negocio.facade.jdbc.vendas.VendasConfigVO;
import negocio.facade.jdbc.vendas.VendasOnlineConvenioTentativaVO;
import negocio.interfaces.basico.SuperInterface;

import java.util.List;

/**
 * Created with IntelliJ IDEA.
 * User: <PERSON><PERSON>
 * Date: 13/08/2020
 */
public interface VendasOnlineConvenioTentativaInterfaceFacade extends SuperInterface {

    void gravar(VendasConfigVO vendasConfigVO, UsuarioVO usuarioVO, List<VendasOnlineConvenioTentativaVO> listaAtual) throws Exception;

    List<VendasOnlineConvenioTentativaVO> consultarPorEmpresa(Integer empresa, boolean somenteConvenioAtivo, int nivelMontarDados) throws Exception;

}

package negocio.interfaces.pactoprint;


import negocio.interfaces.basico.SuperInterface;
import negocio.comuns.pactoprint.LocalImpressaoVO;

import java.sql.SQLException;
import java.util.List;

/**
 * date : 01/04/2015 14:26:27
 * autor: Ulisses
 */
public interface LocalImpressaoInterfaceFacade extends SuperInterface {


	public void incluir(LocalImpressaoVO localImpressaoVO) throws Exception;
	public void alterar(LocalImpressaoVO localImpressaoVO) throws Exception;
	public void excluir(LocalImpressaoVO localImpressaoVO) throws Exception;
	public List consultarParaImpressao(String filtro, String ordem, String campoOrdenacao, int i) throws SQLException;
	public LocalImpressaoVO consultarPorCodigo(Integer codigo, int nivelMontarDados) throws Exception;
	public LocalImpressaoVO consultarPorNomeComputador(String nomeComputador, int nivelMontarDados) throws Exception;
	public List<LocalImpressaoVO> consultarLocalImpressao(Integer codigoEmpresa, int nivelMontarDados) throws Exception;

    String consultarJSON() throws Exception;
}

package negocio.interfaces.notaFiscal;

import negocio.comuns.arquitetura.UsuarioVO;
import negocio.comuns.notaFiscal.NotaFiscalHistoricoVO;
import negocio.interfaces.basico.SuperInterface;

import java.util.List;

public interface NotaFiscalHistoricoInterfaceFacade extends SuperInterface {

    void incluir(NotaFiscalHistoricoVO obj) throws Exception;

    void incluirSemCommit(NotaFiscalHistoricoVO obj) throws Exception;

    void alterar(NotaFiscalHistoricoVO obj) throws Exception;

    void alterarSemCommit(NotaFiscalHistoricoVO obj) throws Exception;

    void excluir(NotaFiscalHistoricoVO obj) throws Exception;

    void excluirSemCommit(NotaFiscalHistoricoVO obj) throws Exception;

    void gerarHistoricoSemCommit(String status, String descricao, String idReferencia, Integer codNotaFiscal, UsuarioVO usuarioVO) throws Exception;

    List<NotaFiscalHistoricoVO> consultar(Integer codNotaFiscal, int nivelMontarDados) throws Exception;
}

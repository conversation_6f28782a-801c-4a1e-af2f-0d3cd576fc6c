package negocio.interfaces.notaFiscal;

import br.com.pactosolucoes.integracao.enotas.to.InutilizacaoNotaFiscalTO;
import br.com.pactosolucoes.integracao.enotas.to.NotaEnotasTO;
import negocio.comuns.arquitetura.UsuarioVO;
import negocio.comuns.basico.ConfiguracaoNotaFiscalVO;
import negocio.comuns.basico.EmpresaVO;
import negocio.comuns.contrato.MovProdutoVO;
import negocio.comuns.financeiro.*;
import negocio.comuns.financeiro.enumerador.TipoRelatorioDF;
import negocio.comuns.notaFiscal.*;
import negocio.interfaces.basico.SuperInterface;

import java.sql.SQLException;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Set;

public interface NotaFiscalInterfaceFacade extends SuperInterface {

    void incluir(NotaFiscalVO obj) throws Exception;

    void incluirSemCommit(NotaFiscalVO obj) throws Exception;

    void alterar(NotaFiscalVO obj) throws Exception;

    void alterarSemCommit(NotaFiscalVO obj) throws Exception;

    NotaFiscalVO consultarPorChavePrimaria(Integer codigo, int nivelMontarDados) throws Exception;

    NotaFiscalVO consultarPorChavePrimariaGestaoNotas(Integer codigo) throws Exception;

    NotaFiscalVO consultarPorNFSeEmitida(Integer nfseEmitida, int nivelMontarDados) throws Exception;

    NotaFiscalVO consultarPorNotaFiscalConsumidorEletronica(Integer notafiscalconsumidoreletronica, int nivelMontarDados) throws Exception;

    NotaFiscalVO consultarPorSequencialFamilia(Integer sequencialFamilia, int nivelMontarDados) throws Exception;

    void enviarNotasAguardando();

    void retentativaEnvioEnotasNFSe();

    void processarItensIndividual(String chave, TipoNotaFiscalEnum tipoNotaFiscal, UsuarioVO usuarioVO,
                                  List<ItemGestaoNotasTO> itensIndividual, Date dtEmissaoGestaoNotas,
                                  String numeroNotaManual, TipoRelatorioDF tipoEmissao) throws Exception;

    void incluirLogEmisao(String json, Integer empresa);

    NotaProcessarTO gerarNotaReciboPagamento(TipoNotaFiscalEnum tipoNotaFiscalEnum, ReciboPagamentoVO reciboPagamentoVO, UsuarioVO usuarioVO, String chave) throws Exception;

    NotaProcessarTO gerarNotaMovConta(TipoNotaFiscalEnum tipoNotaFiscalEnum, MovContaVO movContaVO, UsuarioVO usuarioVO, String chave) throws Exception;

    NotaProcessarTO gerarNotaMovConta(TipoNotaFiscalEnum tipoNotaFiscalEnum, MovContaVO movContaVO, UsuarioVO usuarioVO, String chave, Date dataEmissao) throws Exception;

    NotaProcessarTO gerarNotaCartaoCredito(TipoNotaFiscalEnum tipoNotaFiscalEnum, CartaoCreditoVO cartaoCreditoVO, UsuarioVO usuarioVO, String chave) throws Exception;

    NotaProcessarTO gerarNotaCheque(TipoNotaFiscalEnum tipoNotaFiscalEnum, ChequeVO chequeVO, UsuarioVO usuarioVO, String chave) throws Exception;

    NotaProcessarTO gerarNotaMovPagamento(TipoNotaFiscalEnum tipoNotaFiscalEnum, MovPagamentoVO movPagamentoVO, UsuarioVO usuarioVO, String chave) throws Exception;

    NotaProcessarTO gerarNotaMovProduto(TipoNotaFiscalEnum tipoNotaFiscalEnum, MovProdutoVO movProdutoVO, UsuarioVO usuarioVO, String chave) throws Exception;

    void incluirNotaFiscalWebHookHistorico(String retorno, String heareds) throws Exception;

    List<NotaFiscalVO> consultarNotasFiscais(FiltroNotaFiscalTO filtroNotaFiscalTO, Integer limit, Integer offset) throws Exception;

    List<NotaFiscalVO> consultarNotasFiscaisSincronizar(FiltroNotaFiscalTO filtroNotaFiscalTO) throws Exception;

    List<NotaFiscalVO> consultarNotasFiscais(FiltroNotaFiscalTO filtroNotaFiscalTO, Set<Integer> codigos, int nivelMontarDados) throws Exception;

    List<TotalizadorNotaFiscalTO> consultarNotasFiscaisTotalizador(FiltroNotaFiscalTO filtroNotaFiscalTO) throws Exception;

    Set<Integer> codigosNotas(FiltroNotaFiscalTO filtroNotaFiscalTO) throws SQLException;

    LayoutTelaNotaFiscalTO obterLayoutNotaFiscal(FiltroNotaFiscalTO filtroNotaFiscalTO) throws SQLException;

    Double consultarValorTotal(Integer empresa, Integer configNotaFiscal, StatusEnotasEnum statusEnotasEnum, Date dataInicial, Date dataFinal) throws Exception;

    Map<String, Integer> obterMapa(Integer empresa, String tipoNotaFiscal, Date dataRegistroInicial, Date dataRegistroFinal) throws Exception;

    Integer obterCodigoPorIdPacto(String idPacto) throws Exception;

    NotaEnotasTO consultarNotaEnotas(NotaFiscalVO obj) throws Exception;

    InutilizacaoNotaFiscalTO consultarInutilizacaoNotaEnotas(NotaFiscalVO notaFiscalVO) throws Exception;

    void atualizarDadosNotaFiscalEnotas(NotaFiscalVO obj, NotaEnotasTO notaEnotasTO, UsuarioVO usuarioVO) throws Exception;

    String enviarEmailNotaFiscal(String emailEnviar, NotaFiscalVO obj, UsuarioVO usuarioVO, String observacaoHistorico) throws Exception;

    void atualizarStatusNota(StatusEnotasEnum statusEnotasEnum, NotaFiscalVO obj) throws Exception;

    void atualizarStatusNotaNFSe(StatusEnotasEnum statusEnotasEnum, NFSeEmitidaVO obj) throws Exception;

    void atualizarStatusNotaNFCe(StatusEnotasEnum statusEnotasEnum, NotaFiscalConsumidorEletronicaVO obj) throws Exception;

    void processarItensFamilia(ConfiguracaoNotaFiscalVO configNotaFiscalVO,
                               List<ItemGestaoNotaFamiliaTO> notasEmitir, TipoRelatorioDF tipoRelatorioDF,
                               EmpresaVO empresaVO, UsuarioVO usuarioVO, Date dataEmissaoGestaoNotas,
                               String chave) throws Exception;

    void reenviarNotaNFSe(NotaFiscalVO notaFiscalReenviar, String chave,
                          Date dtEmissao, UsuarioVO usuarioVO) throws Exception;

    List<NotaFiscalVO> consultarNotas(Integer pessoa, Integer max, Integer index, String orderBY, int nivelMontarDados) throws Exception;

    Integer consultarNotasTotal(Integer pessoa) throws Exception;

    List<NotaFiscalVO> consultarNotasPorCodigos(String valorConsulta, int nivelMontarDados) throws Exception;

    String emitirNFCeRecibo(Integer recibo, Integer usuario, String key, String origemEmissaoNFCe) throws Exception;

    String emitirNFSeRecibo(Integer recibo, Integer usuario, String key) throws Exception;

    void atualizarStatusNotaManualmente(StatusEnotasEnum statusEnotasEnum, NotaFiscalVO notaFiscalVO,String motivoAlteracaoStatus) throws Exception;
}

package negocio.interfaces.notaFiscal;

import br.com.pactosolucoes.integracao.enotas.to.InutilizacaoNotaFiscalTO;
import negocio.comuns.arquitetura.UsuarioVO;
import negocio.comuns.notaFiscal.NotaFiscalOperacaoVO;
import negocio.comuns.notaFiscal.NotaFiscalVO;
import negocio.comuns.notaFiscal.OperacaoNotaFiscalEnum;
import negocio.interfaces.basico.SuperInterface;

import java.util.List;

public interface NotaFiscalOperacaoInterfaceFacade extends SuperInterface {

    void incluir(NotaFiscalOperacaoVO obj) throws Exception;

    void incluirSemCommit(NotaFiscalOperacaoVO obj) throws Exception;

    void alterar(NotaFiscalOperacaoVO obj) throws Exception;

    void alterarSemCommit(NotaFiscalOperacaoVO obj) throws Exception;

    void excluir(NotaFiscalOperacaoVO obj) throws Exception;

    void excluirSemCommit(NotaFiscalOperacaoVO obj) throws Exception;

    String realizarEnvioOperacoesServicoNota();

    void gerarSolicitacaoOperacao(String chave, String justificativa, NotaFiscalVO notaFiscalVO, UsuarioVO usuarioVO, OperacaoNotaFiscalEnum operacaoNotaFiscalEnum) throws Exception;

    void gerarSolicitacaoInutilizacaoPorFaixa(String chave, String idEmpresaEnotas, String justificativa, Integer numeroInicial,
                                              Integer numeroFinal, UsuarioVO usuarioVO, String ambienteEmissao, String serie, Integer tipoNota) throws Exception;

    void processarRetornoOperacao(String jsonRetorno, Integer codNotaFiscalOperacao) throws Exception;

    public NotaFiscalOperacaoVO consultarUltimaSoliciacaoInutilizacao(NotaFiscalVO notaFiscalVO) throws Exception;

    List<NotaFiscalOperacaoVO> obterInutilizacoesRealizadas(String idEmpresaEnotas) throws Exception;

    InutilizacaoNotaFiscalTO consultarInutilizacao(NotaFiscalOperacaoVO notaFiscalOperacaoVO) throws Exception;
}

package negocio.interfaces.notaFiscal;

import negocio.comuns.arquitetura.UsuarioVO;
import negocio.comuns.financeiro.NFSeEmitidaVO;
import negocio.comuns.notaFiscal.NotaFiscalFamiliaVO;
import negocio.comuns.notaFiscal.NotaFiscalVO;
import negocio.interfaces.basico.SuperInterface;

import java.sql.SQLException;
import java.util.List;

public interface NotaFiscalFamiliaInterfaceFacade extends SuperInterface {

    void incluir(NotaFiscalFamiliaVO obj) throws Exception;

    void incluirSemCommit(NotaFiscalFamiliaVO obj) throws Exception;

    void alterar(NotaFiscalFamiliaVO obj) throws Exception;

    void alterarSemCommit(NotaFiscalFamiliaVO obj) throws Exception;

    void excluir(NotaFiscalFamiliaVO obj) throws Exception;

    void excluirSemCommit(NotaFiscalFamiliaVO obj) throws Exception;

    void excluirComLogNotaFamilia(Integer codNotaFiscal, NFSeEmitidaVO obj, UsuarioVO usuarioVO) throws SQLException;

    NotaFiscalFamiliaVO consultarPorChavePrimaria(Integer codigo, int nivelMontarDados) throws Exception;

    List<NotaFiscalFamiliaVO> consultarPorNota(Integer codigo, int nivelMontarDados) throws Exception;

    List<NotaFiscalFamiliaVO> incluirLista(Integer sequencialFamilia, NotaFiscalVO notaFiscalVO, List<NFSeEmitidaVO> listaNFSeEmitida) throws Exception;

    String montarDescricaoExcluirNotaFiscal(List<NotaFiscalFamiliaVO> obj) throws Exception;
}

/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package negocio.interfaces.basico;

import java.util.List;
import negocio.comuns.basico.TurmaComissaoColaboradorVO;

/**
 *
 * <AUTHOR>
 */
public interface TurmaComissaoColaboradorInterfaceFacade extends SuperInterface {

    public void incluirTurmasComissao(Integer colaboradorPrm, List<TurmaComissaoColaboradorVO> objetos) throws Exception;

    public void incluir(TurmaComissaoColaboradorVO obj) throws Exception;

    public void alterar(TurmaComissaoColaboradorVO obj) throws Exception;

    public void excluir(TurmaComissaoColaboradorVO obj) throws Exception;

    public void excluirTurmaComissao(Integer colaborador) throws Exception;

    public void alterarTurmaComissao(Integer colaborador, List<TurmaComissaoColaboradorVO> objetos) throws Exception;

    public List consultarTurmaComissao(Integer colaborador, int nivelMontarDados) throws Exception;
}

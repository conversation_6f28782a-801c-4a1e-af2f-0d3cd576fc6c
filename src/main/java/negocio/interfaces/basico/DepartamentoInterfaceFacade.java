package negocio.interfaces.basico;

import negocio.comuns.basico.DepartamentoVO;

import java.sql.SQLException;
import java.util.List;

/**
 * Interface reponsável por criar uma estrutura padrão de comunidação entre a camada de controle
 * e camada de negócio (em especial com a classe Façade). Com a utilização desta interface
 * é possível substituir tecnologias de uma camada da aplicação com mínimo de impacto nas demais.
 * Além de padronizar as funcionalidades que devem ser disponibilizadas pela camada de negócio, por intermédio
 * de sua classe Façade (responsável por persistir os dados das classes VO).
 */
public interface DepartamentoInterfaceFacade extends SuperInterface {

    DepartamentoVO novo() throws Exception;

    void incluir(DepartamentoVO obj) throws Exception;

    void alterar(DepartamentoVO obj) throws Exception;

    void excluir(DepartamentoVO obj) throws Exception;

    DepartamentoVO consultarPorChavePrimaria(Integer codigo, int nivelMontarDados) throws Exception;

    void setIdEntidade(String aIdEntidade);

    String consultarJSON() throws Exception;

    List consultarParaImpressao(String filtro, String ordem, String campoOrdenacao, int i) throws SQLException;

    List<DepartamentoVO> consultarTodos(int nivelMontarDados) throws Exception;

    List<DepartamentoVO> consultarPorNome(String valorConsulta, int empresa, boolean controlarAcesso, int nivelMontarDados) throws Exception;
}
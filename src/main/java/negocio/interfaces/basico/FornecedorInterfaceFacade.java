package negocio.interfaces.basico;

import negocio.comuns.basico.FornecedorVO;
import negocio.comuns.basico.PessoaVO;

import java.sql.SQLException;
import java.util.List;

/**
 * <AUTHOR>
 */
public interface FornecedorInterfaceFacade {
    public void incluir(FornecedorVO fornecedor) throws Exception;

    public String consultarJSON(Integer empresa) throws Exception ;

    public void alterar(FornecedorVO fornecedor) throws Exception;

    public void excluir(FornecedorVO forn) throws Exception;

    public FornecedorVO obter(int codigo) throws Exception;

    public FornecedorVO consultarPorPessoa(PessoaVO pessoaVO) throws Exception;

    public List<FornecedorVO> consultarFornecedor(boolean somenteVigente) throws Exception;

    boolean existeCNPJNoBanco(Integer codigo, String cnpj, int idEmpresa) throws Exception;

    void alterarSomenteChaveArquivo(FornecedorVO obj) throws SQLException;

    public List<FornecedorVO> consultarTodosFornecedorPorEmpresa(int codigoEmpresa) throws Exception;

    public List consultarParaImpressao(String filtro, String ordem, String campoOrdenacao, Integer empresa) throws Exception;

    public List<FornecedorVO> consultarOrdenadoPelaDescricao() throws Exception;

    public FornecedorVO consultarPorCNPJExcetoCodigo(FornecedorVO fornecedor) throws Exception;

    public FornecedorVO consultarPorCNPJ(String cnpj) throws Exception;

    public List<FornecedorVO> consultarPorDescricao(String valorConsulta) throws Exception;

    boolean existeFornecedorNome(FornecedorVO fornecedorVO) throws Exception;

    boolean existeFornecedorCNPJ(FornecedorVO fornecedorVO) throws Exception;

    public void prepararConexao() throws Exception;

    FornecedorVO consultarPorCodigo(int codigo) throws Exception;
}

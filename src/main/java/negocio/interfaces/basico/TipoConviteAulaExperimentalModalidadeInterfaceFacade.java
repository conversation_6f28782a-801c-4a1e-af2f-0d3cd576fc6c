package negocio.interfaces.basico;

import negocio.comuns.conviteaulaexperimental.TipoConviteAulaExperimentalModalidadeVO;
import negocio.comuns.conviteaulaexperimental.TipoConviteAulaExperimentalVO;

import java.util.List;

/**
 * Created by ulisses on 14/01/2016.
 */
public interface TipoConviteAulaExperimentalModalidadeInterfaceFacade extends SuperInterface {

    void incluirSemCommit(List<TipoConviteAulaExperimentalModalidadeVO> listaConviteModalidade)throws Exception;
    void excluirSemComit(Integer tipoConviteAulaExperimental)throws Exception;
    List<TipoConviteAulaExperimentalModalidadeVO> consultar(TipoConviteAulaExperimentalVO tipoConviteAulaExperimentalVO, int nivelMontarDados)throws Exception;
}

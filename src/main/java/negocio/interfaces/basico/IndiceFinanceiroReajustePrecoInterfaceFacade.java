package negocio.interfaces.basico;

import br.com.pactosolucoes.enumeradores.TipoPlanoEnum;
import negocio.comuns.plano.IndiceFinanceiroReajustePrecoVO;

import java.util.List;

/**
 * Created by ulisses on 19/11/2016.
 */
public interface IndiceFinanceiroReajustePrecoInterfaceFacade extends SuperInterface{

    void incluir(IndiceFinanceiroReajustePrecoVO indiceFinanceiroReajustePrecoVO)throws Exception;
    void alterar(IndiceFinanceiroReajustePrecoVO indiceFinanceiroReajustePrecoVO)throws Exception;
    void excluir(IndiceFinanceiroReajustePrecoVO indiceFinanceiroReajustePrecoVO)throws Exception;
    IndiceFinanceiroReajustePrecoVO consultar(String mes, String ano, int nivelMontarDados)throws Exception;
    IndiceFinanceiroReajustePrecoVO consultarPorCodigo(Integer codigo, int nivelMontarDados)throws Exception;
    List consultarParaImpressao(String filtro, String ordem, String campoOrdenacao) throws Exception ;
    IndiceFinanceiroReajustePrecoVO consultarIndiceFinanceiroPeriodo(String mes, String ano, Boolean renovacaoAutoamtica, TipoPlanoEnum tipoPlanoEnum, boolean somentePercentualPositivo, int nivelMontarDados)throws Exception;
    String consultarJSON() throws Exception;
}

package negocio.interfaces.basico;

import br.com.pactosolucoes.conviteaulaexperimental.json.TipoConviteAulaExperimentalJSON;
import negocio.comuns.conviteaulaexperimental.TipoConviteAulaExperimentalVO;

import java.sql.SQLException;
import java.util.List;

/**
 * Created by ulisses on 08/01/2016.
 */
public interface TipoConviteAulaExperimentalInterfaceFacade extends SuperInterface {

    void incluir(TipoConviteAulaExperimentalVO tipoConviteAulaExperimentalVO)throws Exception;
    void alterar(TipoConviteAulaExperimentalVO tipoConviteAulaExperimentalVO)throws Exception;
    void excluir(TipoConviteAulaExperimentalVO tipoConviteAulaExperimentalVO)throws Exception;
    TipoConviteAulaExperimentalVO consultarPorCodigo(Integer codigo, int nivelMontarDados)throws Exception;
    String consultarJSON(Integer codEmpresa) throws Exception;
    List consultarParaImpressao(String filtro, String ordem, String campoOrdenacao, int i) throws SQLException;
    List<TipoConviteAulaExperimentalJSON> consultarTipoConvitesVigenteAlunoPodeEnviar(Integer codigoCliente, Integer codigoEmpresa)throws Exception;
    public List<TipoConviteAulaExperimentalVO> consultarTodos(Integer codigoEmpresa)throws Exception;

}

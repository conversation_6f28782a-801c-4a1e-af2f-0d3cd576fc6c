package negocio.interfaces.basico;

import br.com.pactosolucoes.integracao.enotas.to.InfoCidadeEnotasTO;
import br.com.pactosolucoes.integracao.enotas.to.InfoEmpresaEnotasTO;
import br.com.pactosolucoes.integracao.enotas.to.InfoServicoEnotasTO;
import org.json.JSONObject;
import negocio.comuns.basico.ConfiguracaoNotaFiscalVO;

import java.io.File;
import java.util.List;
import java.util.Map;

public interface ConfiguracaoNotaFiscalInterfaceFacade extends SuperInterface {

    void incluir(ConfiguracaoNotaFiscalVO obj) throws Exception;

    void alterar(ConfiguracaoNotaFiscalVO obj) throws Exception;

    void excluir(ConfiguracaoNotaFiscalVO obj) throws Exception;

    String sincronizarEnotas(String chave, boolean enviarConfiguracoesAmbiente, ConfiguracaoNotaFiscalVO obj);

    InfoEmpresaEnotasTO consultarSituacaoEmpresaEnotas(ConfiguracaoNotaFiscalVO obj) throws Exception;

    String enviarCertificadoEnotas(String chave, ConfiguracaoNotaFiscalVO obj) throws Exception;

    String enviarLogotipoEnotas(String chave, ConfiguracaoNotaFiscalVO obj) throws Exception;

    void obterInformacoesModuloNotas(String chave, ConfiguracaoNotaFiscalVO obj) throws Exception;

    InfoCidadeEnotasTO obterInformacoesCidade(ConfiguracaoNotaFiscalVO obj) throws Exception;

    List<InfoServicoEnotasTO> obterInformacoesServico(ConfiguracaoNotaFiscalVO obj) throws Exception;

    ConfiguracaoNotaFiscalVO consultarPorChavePrimaria(Integer codigo, int nivelMontarDados) throws Exception;

    String consultarJSON(Integer empresa, String situacao) throws Exception;

    Map<Integer, ConfiguracaoNotaFiscalVO> consultarTodosMapa(Integer empresa, Boolean ativo, int nivelMontarDados) throws Exception;

    List<ConfiguracaoNotaFiscalVO> consultarConfiguracaoNotaFiscal(Integer empresa, Integer[] tipoNotaFiscal, Boolean ativo, Boolean enotas, int nivelMontarDados) throws Exception;

    void alterarCertificado(ConfiguracaoNotaFiscalVO obj) throws Exception;

    void alterarLogotipo(ConfiguracaoNotaFiscalVO obj) throws Exception;

    String atualizarDadosModuloNFe(ConfiguracaoNotaFiscalVO obj) throws Exception;

    void obterDataCertificado(ConfiguracaoNotaFiscalVO obj);

    String obterLinkDownloadSetupSAT(ConfiguracaoNotaFiscalVO obj) throws Exception;

    String habilitarDesabilitarEmpresa(boolean isEnotas, String idEnotas) throws Exception;

    void desabilitarEmpresaInativaEnotas(Integer idEmpresa) throws Exception;
}

package negocio.interfaces.basico;

import negocio.comuns.basico.ProcessoImportacaoLogVO;

import java.util.List;

/**
 * Created by <PERSON><PERSON> on 20/12/2019.
 */
public interface ProcessoImportacaoLogInterfaceFacade extends SuperInterface {
    
    void setIdEntidade(String aIdEntidade);

    void incluir(ProcessoImportacaoLogVO obj) throws Exception;

    void excluir(ProcessoImportacaoLogVO obj) throws Exception;

    ProcessoImportacaoLogVO consultarPorChavePrimaria(Integer codigo, int nivelMontarDados) throws Exception;

    List<ProcessoImportacaoLogVO> consultarPorProcessoImportacao(Integer processoImportacao, Integer limit, Integer offSet, int nivelMontarDados) throws Exception;
}

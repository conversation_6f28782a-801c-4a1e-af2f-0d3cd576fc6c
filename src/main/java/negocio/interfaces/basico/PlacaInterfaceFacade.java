package negocio.interfaces.basico;

import negocio.comuns.basico.PlacaVO;

import java.util.List;

/**
 * Created by GlaucoT on 09/09/2015
 */
public interface PlacaInterfaceFacade extends SuperInterface {

    void incluir(PlacaVO obj) throws Exception;

    void alterar(PlacaVO obj) throws Exception;

    void excluir(PlacaVO obj) throws Exception;

    void alterarPlacas(Integer pessoa, List objetos) throws Exception;

    List<PlacaVO> consultarPlacas(Integer pessoa, int nivelMontarDados) throws Exception;
}

package negocio.interfaces.basico;

import importador.ImportacaoConfigTO;
import importador.json.*;
import negocio.comuns.acesso.IntegracaoMemberVO;
import negocio.comuns.acesso.integracao.member.enums.TipoOperacaoIntegracaoMembersEnum;
import negocio.comuns.basico.ConfigExcelImportacaoTO;
import negocio.comuns.basico.ProcessoImportacaoVO;
import negocio.comuns.basico.enumerador.TipoImportacaoEnum;

import java.util.List;

/**
 * Created by <PERSON><PERSON> on 20/12/2019.
 */
public interface ProcessoImportacaoInterfaceFacade extends SuperInterface {

    void setIdEntidade(String aIdEntidade);

    void incluir(ProcessoImportacaoVO obj) throws Exception;

    void alterar(ProcessoImportacaoVO obj) throws Exception;

    void excluir(ProcessoImportacaoVO obj) throws Exception;

    ProcessoImportacaoVO consultarPorChavePrimaria(Integer codigo, int nivelMontarDados) throws Exception;

    List<ProcessoImportacaoVO> consultarTodos(Integer limit, int nivelMontarDados) throws Exception;

    void atualizarStatus(ProcessoImportacaoVO obj) throws Exception;

    ProcessoImportacaoVO iniciarProcessoImportacaoVO(ImportacaoConfigTO configTO, Integer totalItens) throws Exception;

    void finalizarProcessoImportacaoVO(ProcessoImportacaoVO obj, ImportacaoConfigTO configTO) throws Exception;

    void enviarEmailImportacao(List<String> listaEmails, ProcessoImportacaoVO obj);

    void iniciarThreadClientesContratos(TipoImportacaoEnum tipoEnum, String chave,
                                        Integer usuario, List<String> emails,
                                        List<ClienteImportacaoJSON> clientesJSON,
                                        List<ContratoImportacaoJSON> contratosJSON,
                                        ConfigExcelImportacaoTO configExcelImportacaoTO) throws Exception;

    void iniciarThreadProdutos(TipoImportacaoEnum tipoEnum, String chave,
                               Integer usuario, List<String> emails,
                               List<ProdutoImportacaoJSON> produtosJSON,
                               ConfigExcelImportacaoTO configExcelImportacaoTO) throws Exception;

    void iniciarThreadContasFinanceiro(TipoImportacaoEnum tipoEnum, String chave,
                                       Integer usuario, List<String> emails,
                                       List<ContaImportacaoJSON> contasJSON,
                                       ConfigExcelImportacaoTO configExcelImportacaoTO) throws Exception;
    void iniciarThreadAlunoTurma(TipoImportacaoEnum tipoEnum, String chave,
                                       Integer usuario, List<String> emails,
                                       List<AlunoTurmaImportacaoJSON> alunoTurmaJson,
                                       ConfigExcelImportacaoTO configExcelImportacaoTO) throws Exception;
    void iniciarThreadTurma(TipoImportacaoEnum tipoEnum, String chave,
                                 Integer usuario, List<String> emails,
                                 List<TurmaImportacaoJSON> alunoTurmaJson,
                                 ConfigExcelImportacaoTO configExcelImportacaoTO) throws Exception;

    void iniciarThreadFornecedor(TipoImportacaoEnum tipoEnum, String chave,
                                 Integer usuario, List<String> emails,
                                 List<FornecedorImportacaoJSON> fornecedoresJSON,
                                 ConfigExcelImportacaoTO configExcelImportacaoTO) throws Exception;

    void iniciarThreadColaborador(TipoImportacaoEnum tipoEnum, String chave,
                                  Integer usuario, List<String> emails,
                                  List<ColaboradorImportacaoJSON> colaboradoresJSON,
                                  ConfigExcelImportacaoTO configExcelImportacaoTO) throws Exception;

    void iniciarThreadMembers(TipoImportacaoEnum tipoEnum, String chave,
                              Integer usuario, IntegracaoMemberVO integracaoMemberVO, String idsMembers, TipoOperacaoIntegracaoMembersEnum tipoOperacaoIntegracaoMembersEnum) throws Exception;

    void iniciarThreadParcelasPagamentos(TipoImportacaoEnum tipoEnum, String chave, Integer usuario,  List<String> emails, List<ParcelaPagamentoJSON> listaParcelaPagamentoJson, ConfigExcelImportacaoTO configExcelImportacaoTO) throws Exception;

    void iniciarThreadTreinoAtividades(TipoImportacaoEnum tipoEnum, String chave, Integer usuario,  List<String> emails, List<TreinoAtividadeJSON> listaTreinoAtividadesJson, ConfigExcelImportacaoTO configExcelImportacaoTO) throws Exception;

    void iniciarThreadTreinoProgramas(TipoImportacaoEnum tipoEnum, String chave, Integer usuario,  List<String> emails, List<ProgramaFichaJSON> listaTreinoProgramasJson, ConfigExcelImportacaoTO configExcelImportacaoTO) throws Exception;

    void iniciarThreadTreinoAtividadeFicha(TipoImportacaoEnum tipoEnum, String chave, Integer usuario,  List<String> emails, List<AtividadeFichaJSON> listaTreinoAtividadeFichaJson, ConfigExcelImportacaoTO configExcelImportacaoTO) throws Exception;

    ProcessoImportacaoVO consultarUltimoExecutado(int nivelMontarDados) throws Exception;

}

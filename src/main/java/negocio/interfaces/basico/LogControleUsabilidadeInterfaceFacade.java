package negocio.interfaces.basico;

import java.util.Date;
import negocio.comuns.basico.LogControleUsabilidadeVO;
import negocio.comuns.utilitarias.*;
import java.util.List;

/**
 * Interface reponsável por criar uma estrutura padrão de comunidação entre a camada de controle
 * e camada de negócio (em especial com a classe Façade). Com a utilização desta interface 
 * é possível substituir tecnologias de uma camada da aplicação com mínimo de impacto nas demais.
 * Além de padronizar as funcionalidades que devem ser disponibilizadas pela camada de negócio, por intermédio
 * de sua classe Façade (responsável por persistir os dados das classes VO).
 */
public interface LogControleUsabilidadeInterfaceFacade extends SuperInterface {

    public LogControleUsabilidadeVO novo() throws Exception;

    public void incluir(LogControleUsabilidadeVO obj) throws Exception;

    public void alterar(LogControleUsabilidadeVO obj) throws Exception;

    public void excluir(LogControleUsabilidadeVO obj) throws Exception;

    public LogControleUsabilidadeVO consultarPorChavePrimaria(Integer codigo, int nivelMontarDados) throws Exception;

    public List consultarPorCodigo(Integer valorConsulta, boolean controlarAcesso, int nivelMontarDados) throws Exception;

    public List consultarPorEntidade(String valorConsulta, boolean controlarAcesso, int nivelMontarDados) throws Exception;

    public List consultarPorMaquina(String valorConsulta, boolean controlarAcesso, int nivelMontarDados) throws Exception;

    public List consultarPorAcao(String valorConsulta, boolean controlarAcesso, int nivelMontarDados) throws Exception;

    public List consultarPorDataRegistro(Date prmIni, Date prmFim, boolean controlarAcesso, int nivelMontarDados) throws Exception;

    public List consultarPorUsuario(Integer valorConsulta, boolean controlarAcesso, int nivelMontarDados) throws Exception;

    public List consultarPorEmpresa(Integer valorConsulta, boolean controlarAcesso, int nivelMontarDados) throws Exception;

    public void setIdEntidade(String aIdEntidade);

    public void incluirSemCommit(LogControleUsabilidadeVO obj) throws Exception;

    public void incluirLog(LogControleUsabilidadeVO obj) throws Exception;

    public List consultarPorIntervaloDatasUsuario10UltimosAcessos(Integer valorConsulta, String dataInicio, String dataTermino, String acao, boolean controlarAcesso, int nivelMontarDados) throws Exception;

    public List consultarAcessosRelatorioPorPeriodo(Integer usuario, Integer colaborador, String dataInicio, String dataTermino, String acao) throws Exception;
}

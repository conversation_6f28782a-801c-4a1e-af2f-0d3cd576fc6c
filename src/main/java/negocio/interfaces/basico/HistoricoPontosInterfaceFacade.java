/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package negocio.interfaces.basico;

import negocio.comuns.basico.enumerador.TipoItemCampanhaEnum;
import org.json.JSONArray;
import java.util.Date;
import java.util.List;

import negocio.comuns.CampanhaDuracaoVO;
import negocio.comuns.basico.*;

/**
 *
 * <AUTHOR>
 */
public interface HistoricoPontosInterfaceFacade extends SuperInterface{

    HistoricoPontosVO consultarPorChavePrimaria(Integer codigo, int nivelMontarDados) throws Exception;

    public HistoricoPontosVO novo() throws Exception;
    
    public void incluir(HistoricoPontosVO historicoPontos) throws Exception;

    void incluirPontuacaoPorTipo(TipoItemCampanhaEnum tipoIndicacao,Integer chaveEstrangeira, String descricao, ClienteVO cliente, EmpresaVO empresa) throws Exception;

    public void alterar(HistoricoPontosVO historicoPontos) throws Exception;
    
    public List<HistoricoPontosVO> consultaTodosHistoricoPontos(Integer nivelMontarDados) throws Exception;
    
    public List<HistoricoPontosVO> consultaTotalPontosAlunos(String situacao, boolean pesquisaDetalhada, Date dataInicio, 
            Date dataFim, BrindeVO brinde, ClienteVO clientePesquisa, Integer empresa) throws Exception;
    
    public void alterarSemCommit(HistoricoPontosVO historicoPontos) throws Exception;
    
    public List<HistoricoPontosVO> consultarHistoricoPorCliente(Integer cliente,Integer nivelMontarDados) throws Exception;
    
    public Integer obterPontosTotalPorCliente(Integer cliente) throws Exception;

    Integer obterPontosTotalPorTipoItemCampanha(ItemCampanhaVO itemCampanha, Date dataInicial, Date dataFinal) throws Exception;

    public Integer obterPontosTotalPorCodigo(Integer codigo) throws Exception;

    public JSONArray consultaTotalPontosAlunosTreino(String situacao, boolean analitico, Integer cliente, String nomeCliente, Date dataInicio, Date dataFinal) throws Exception;
    
    public void excluir(Integer codigo) throws Exception;
    
    public void excluirSemCommit(Integer codigo) throws Exception;
    
    public HistoricoPontosVO obterHistoricoPorProduto(Integer cliente, Integer produto, Integer codigoVenda,Integer nivelMontarDado) throws Exception;
    
    public List<HistoricoPontosVO> obterHistoricoPorContrato(TipoItemCampanhaEnum tipoDePontos, Integer cliente, Integer contrato, Integer nivelMontarDado) throws Exception;

    Date existeAcessoPorClienteHorarioRobo(Integer cliente, Date dataRegistro) throws Exception;

    public void acresentarPontoPorAulaComfirmadaRobo(Date data) throws Exception;
    
    public void acresentarPontoPorAcessoRobo(Date data, List<EmpresaVO> listaEmpresa, String urlOamd, String key) throws Exception;

    void zerarPontuacaoRobo(PessoaVO pessoa)throws Exception;

    public JSONArray consultaTotalPontosAlunosApp(String matriculaCliente,boolean analitico) throws Exception;

    List<HistoricoPontosVO> consultarPontosXCategoria(ItemCampanhaVO item, Date dataInicial, Date dataFinal) throws Exception;

    List<HistoricoPontosVO> consultarPontosXBrinde(BrindeVO brinde, Date dataInicial, Date dataFinal) throws Exception;

    List<HistoricoPontosVO> consultarPontosXCampanha(CampanhaDuracaoVO brinde, Date dataInicial, Date dataFinal) throws Exception;

    public void excluirPorContrato(Integer codigoContrato) throws Exception;

}

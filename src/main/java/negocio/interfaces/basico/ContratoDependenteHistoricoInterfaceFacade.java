package negocio.interfaces.basico;

import negocio.comuns.basico.ContratoDependenteHistoricoVO;
import negocio.comuns.basico.ContratoDependenteVO;

import java.util.Optional;

public interface ContratoDependenteHistoricoInterfaceFacade extends SuperInterface {

    void incluirDependente(ContratoDependenteVO obj) throws Exception;

    void removerDependente(ContratoDependenteVO obj) throws Exception;

    Optional<ContratoDependenteHistoricoVO> consultarHistoricoEmAberto(ContratoDependenteVO contratoDependenteVO) throws Exception;

}

package negocio.interfaces.basico;

import negocio.comuns.basico.GrupoVO;
import negocio.comuns.basico.PessoaVO;

import java.util.List;

/**
 * Interface reponsável por criar uma estrutura padrão de comunidação entre a camada de controle
 * e camada de negócio (em especial com a classe Façade). Com a utilização desta interface
 * é possível substituir tecnologias de uma camada da aplicação com mínimo de impacto nas demais.
 * Além de padronizar as funcionalidades que devem ser disponibilizadas pela camada de negócio, por intermédio
 * de sua classe Façade (responsável por persistir os dados das classes VO).
 */
public interface GrupoInterfaceFacade extends SuperInterface {


    public GrupoVO novo() throws Exception;

    public void incluir(GrupoVO obj) throws Exception;

    public void alterar(GrupoVO obj) throws Exception;

    public void alterarSemCommit(GrupoVO obj) throws Exception;

    public void excluir(GrupoVO obj) throws Exception;

    public GrupoVO consultarPorChavePrimaria(Integer codigo, int nivelMontarDados) throws Exception;

    public List consultarPorCodigo(Integer valorConsulta, boolean controlarAcesso, int nivelMontarDados) throws Exception;

    public GrupoVO consultarPorCodigoExato(Integer valorConsulta, boolean controlarAcesso, int nivelMontarDados) throws Exception;

    public List consultarPorGrupoInativo(String valorConsulta, boolean controlarAcesso, int nivelMontarDados) throws Exception;

    public List consultarPorDescricao(String valorConsulta, boolean controlarAcesso, int nivelMontarDados) throws Exception;

    public List consultarPorTipoDesconto(String valorConsulta, boolean controlarAcesso, int nivelMontarDados) throws Exception;

    public void setIdEntidade(String aIdEntidade);

    public String consultarJSON() throws Exception;

    public List consultarParaImpressao(String filtro, String ordem, String campoOrdenacao, int i) throws Exception;

    public GrupoVO consultarMelhorGrupoPorVinculo(PessoaVO pessoaVO, int nivelMontarDados, double valorFinalContrato ) throws Exception;

    public GrupoVO consultarGrupoVinculoFamiliar(PessoaVO pessoaVO, int nivelMontarDados, double valorFinalContrato) throws Exception;

}

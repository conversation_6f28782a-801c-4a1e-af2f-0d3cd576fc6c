package negocio.interfaces.basico;

import negocio.comuns.basico.CadastroDinamicoVO;
import negocio.comuns.basico.enumerador.negocio.comuns.basico.enumerador.cadastrodinamico.CadastroDinamicoEnumInterface;

import java.util.List;

/**
 * Created by ulisses on 14/08/2015.
 */
public interface CadastroDinamicoInterfaceFacade extends  SuperInterface {

    void incluirSemCommit(CadastroDinamicoVO cadastroDinamicoVO) throws Exception;

    void excluir(CadastroDinamicoVO cadastroDinamicoVO) throws Exception;

    CadastroDinamicoVO consultar(String nomeTabela, int nivelMontarDados) throws Exception;
    CadastroDinamicoVO consultarPorCodigo(Integer codigo,  int nivelMontarDados) throws Exception;

    void incluirTabelaCadastroDinamico(String nomeTabela, List<CadastroDinamicoEnumInterface> listaCadastroDinamicoEnumInterface)throws Exception;
}

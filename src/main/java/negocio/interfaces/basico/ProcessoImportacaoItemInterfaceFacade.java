package negocio.interfaces.basico;

import negocio.comuns.basico.ProcessoImportacaoItemVO;

import java.util.List;

/**
 * Created by <PERSON><PERSON> on 20/12/2019.
 */
public interface ProcessoImportacaoItemInterfaceFacade extends SuperInterface {
    
    void setIdEntidade(String aIdEntidade);

    void incluir(ProcessoImportacaoItemVO obj) throws Exception;

    void alterar(ProcessoImportacaoItemVO obj) throws Exception;

    void excluir(ProcessoImportacaoItemVO obj) throws Exception;

    ProcessoImportacaoItemVO consultarPorChavePrimaria(Integer codigo, int nivelMontarDados) throws Exception;

    List<ProcessoImportacaoItemVO> consultarPorProcessoImportacao(Integer processoImportacao, Integer limit, Integer offSet, int nivelMontarDados) throws Exception;
}

package negocio.interfaces.basico;

import negocio.comuns.basico.GympassVO;

import java.sql.SQLException;
import java.util.Date;
import java.util.List;

/**
 * Interface reponsável por criar uma estrutura padrão de comunidação entre a camada de controle
 * e camada de negócio (em especial com a classe Façade). Com a utilização desta interface
 * é possível substituir tecnologias de uma camada da aplicação com mínimo de impacto nas demais.
 * Além de padronizar as funcionalidades que devem ser disponibilizadas pela camada de negócio, por intermédio
 * de sua classe Façade (responsável por persistir os dados das classes VO).
 */
public interface GympassInterfaceFacade extends SuperInterface {

    public GympassVO novo() throws Exception;

    public void incluir(GympassVO obj) throws Exception;

    public void alterar(GympassVO obj) throws Exception;

    public void excluir(GympassVO obj) throws Exception;

    public GympassVO buscarPorCodigo(int codigo, int nivelMontarDados) throws Exception;

    public GympassVO buscarPorTabelaAtiva(Date dataConsulta, int nivelMontarDados) throws Exception;

    public GympassVO buscarPorTabelaAtivaPeriodo(Date dataInicio, Date dataFinal, int nivelMontarDados) throws Exception;

    public Date buscarDataInicio() throws Exception;

    public List<GympassVO> consultar(int nivelMontarDados) throws Exception;

    public List consultarParaImpressao(String filtro, String ordem, String campoOrdenacao) throws SQLException;

    public void setIdEntidade(String aIdEntidade);

    public String consultarJSON() throws Exception;
}
package negocio.interfaces.basico;

import negocio.comuns.basico.EstadoVO;
import negocio.comuns.basico.PaisVO;

import java.util.List;

/**
 * Interface reponsável por criar uma estrutura padrão de comunidação entre a camada de controle
 * e camada de negócio (em especial com a classe Façade). Com a utilização desta interface 
 * é possível substituir tecnologias de uma camada da aplicação com mínimo de impacto nas demais.
 * Além de padronizar as funcionalidades que devem ser disponibilizadas pela camada de negócio, por intermédio
 * de sua classe Façade (responsável por persistir os dados das classes VO).
 */
public interface EstadoInterfaceFacade extends SuperInterface {

    public EstadoVO novo() throws Exception;

    public void incluir(EstadoVO obj) throws Exception;

    public void alterar(EstadoVO obj) throws Exception;

    public void excluir(EstadoVO obj) throws Exception;

    public List consultarPorNomePais(String valorConsulta, int nivelMontarDados) throws Exception;

    public EstadoVO consultarPorChavePrimaria(Integer codigoPrm, int nivelMontarDados) throws Exception;

    public List consultarEstados(Integer pais, int nivelMontarDados) throws Exception;

    public List consultarPorSigla(String valorConsulta, boolean controlarAcesso, int nivelMontarDados) throws Exception;

    public List consultarPorCodigo(Integer valorConsulta, boolean controlarAcesso, int nivelMontarDados) throws Exception;

    public List consultarPorDescricao(String valorConsulta, boolean controlarAcesso, int nivelMontarDados) throws Exception;

    public EstadoVO consultarPorSiglaDescricaoEPais(String valorConsulta, String descricaoEstado, Integer codigoPais, boolean controlarAcesso, int nivelMontarDados) throws Exception;

    public void setIdEntidade(String aIdEntidade);

    /**
     * Operação responsável por incluir objetos da <code>EstadoVO</code> no BD.
     * Garantindo o relacionamento com a entidade principal <code>basico.Pais</code> através do atributo de vínculo.
     * @param objetos List contendo os objetos a serem gravados no BD da classe.
     * @exception Exception  Erro de conexão com o BD ou restrição de acesso a esta operação.
     */
    public void incluirEstados(Integer paisPrm, List objetos) throws Exception;

    /**
     * Operação responsável por alterar todos os objetos da <code>EstadoVO</code> contidos em um Hashtable no BD.
     * Faz uso da operação <code>excluirEstados</code> e <code>incluirEstados</code> disponíveis na classe <code>Estado</code>.
     * @param objetos  List com os objetos a serem alterados ou incluídos no BD.
     * @exception Exception  Erro de conexão com o BD ou restrição de acesso a esta operação.
     */
    public void alterarEstados(Integer pais, List objetos) throws Exception;

    /**
     * Operação responsável por excluir todos os objetos da <code>EstadoVO</code> no BD.
     * Faz uso da operação <code>excluir</code> disponível na classe <code>Estado</code>.
     * @param <code>pais</code> campo chave para exclusão dos objetos no BD.
     * @exception Exception  Erro de conexão com o BD ou restrição de acesso a esta operação.
     */
    public void excluirEstados(Integer pais) throws Exception;

    public EstadoVO consultarPorSiglaUf(String valorConsulta, boolean controlarAcesso, int nivelMontarDados) throws Exception;

    EstadoVO consultarPorSiglaUf(String valorConsulta, PaisVO paisVO, int nivelMontarDados) throws Exception;

    public EstadoVO consultarPorCodigo(Integer codigoPrm, int nivelMontarDados) throws Exception;

    public EstadoVO consultarPorCodigoEmpresa(Integer codigoPrm, int nivelMontarDados) throws Exception ;

    void preencherCodigosIBGEEstado();
}

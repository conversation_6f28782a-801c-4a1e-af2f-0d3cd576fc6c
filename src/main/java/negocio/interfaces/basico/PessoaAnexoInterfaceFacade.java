package negocio.interfaces.basico;

import negocio.comuns.arquitetura.UsuarioVO;
import negocio.comuns.basico.ClienteVO;
import negocio.comuns.basico.PessoaAnexoVO;
import negocio.comuns.basico.PessoaVO;
import negocio.comuns.contrato.ContratoAssinaturaDigitalVO;
import negocio.comuns.contrato.ContratoVO;
import negocio.comuns.utilitarias.UteisValidacao;

import java.sql.ResultSet;
import java.util.Date;
import java.util.List;

public interface PessoaAnexoInterfaceFacade extends SuperInterface {
    public void incluir(PessoaAnexoVO obj) throws Exception;

    public void excluir(PessoaAnexoVO obj) throws Exception;
    public void excluirPorPessoa(Integer pessoa, String tipos) throws Exception;
    public String obterPorPessoaTipoAnexo(final Integer pessoa, String tipos) throws Exception;

    public void alterar(PessoaAnexoVO obj) throws Exception;
    public List<PessoaVO> consultarAlunosCartaoVacina(boolean cadastrados, String filtro, Integer empresa, boolean todos, String tiposAnexo) throws Exception;

    public Integer countAlunosCartaoVacina(boolean cadastrados, String filtro, Integer empresa, String tiposAnexo) throws Exception;
    public PessoaAnexoVO consultarPorPessoa(Integer Pessoa, String tipoAnexo) throws Exception ;
    public boolean existesAnexoPessoaTipoIdade(Integer pessoa, String tipoAnexo) throws Exception;
}

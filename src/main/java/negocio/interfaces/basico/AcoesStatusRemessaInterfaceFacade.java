/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */

package negocio.interfaces.basico;

import java.sql.ResultSet;
import java.util.List;
import java.util.Map;
import negocio.comuns.basico.AcoesStatusRemessaVO;
import negocio.comuns.basico.enumerador.AcoesRemessasEnum;

/**
 *
 * <AUTHOR>
 */
public interface AcoesStatusRemessaInterfaceFacade  extends SuperInterface{

    public void salvar(AcoesStatusRemessaVO acaoStatus) throws Exception;

    public void excluir(AcoesStatusRemessaVO acaoStatus) throws Exception;

    public List<AcoesStatusRemessaVO> consultarPorTodos() throws Exception;

    List<AcoesStatusRemessaVO> consultarPorAcao(AcoesRemessasEnum acao) throws Exception;

    public Map<String, Integer> consultarPorTodosMapa() throws Exception;

    public List<AcoesStatusRemessaVO> montarDadosConsulta(ResultSet dados) throws Exception;

    public AcoesStatusRemessaVO montarDados(ResultSet dados) throws Exception;
}

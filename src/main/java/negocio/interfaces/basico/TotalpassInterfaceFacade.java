package negocio.interfaces.basico;

import java.sql.SQLException;
import java.util.List;

public interface TotalpassInterfaceFacade extends SuperInterface {

    List consultarPorEmpresa(Integer codigo, int nivelMontarDados) throws Exception;

    Integer obterLimiteDeAulasPorDiaTotalpass(Integer codEmpresa) throws SQLException;

    Integer obterLimiteDeAcessosPorDiaTotalpass(Integer codEmpresa) throws SQLException;

    boolean possuiConfiguracaoTotalPassAtiva(Integer codEmpresa) throws Exception;

}
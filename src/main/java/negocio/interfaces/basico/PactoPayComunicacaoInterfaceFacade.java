package negocio.interfaces.basico;

import negocio.comuns.basico.PactoPayComunicacaoVO;

import java.util.Date;
import java.util.List;

public interface PactoPayComunicacaoInterfaceFacade extends SuperInterface {

    List<PactoPayComunicacaoVO> consultar(Date dataInicialRegistro, Date dataFinalRegistro,
                                          Date dataInicialExecucao, Date dataFinalExecucao,
                                          Date dataInicialClicou, Date dataFinalClicou,
                                          Date dataInicialLido, Date dataFinalLido,
                                          int nivelMontarDados) throws Exception;

}

package negocio.interfaces.basico;

import java.util.Date;
import java.util.List;
import negocio.comuns.basico.ParceiroFidelidadePontosVO;
import negocio.comuns.basico.enumerador.TipoPontoParceiroFidelidadeEnum;
import negocio.comuns.financeiro.MovPagamentoVO;


/**
 *
 * <AUTHOR>
 */
public interface ParceiroFidelidadePontosInterfaceFacade extends SuperInterface {

    ParceiroFidelidadePontosVO novo() throws Exception;

    void incluir(ParceiroFidelidadePontosVO obj) throws Exception;

    void alterar(ParceiroFidelidadePontosVO obj) throws Exception;

    void excluir(ParceiroFidelidadePontosVO obj) throws Exception;

    ParceiroFidelidadePontosVO consultarPorChavePrimaria(Integer codigo, int nivelMontarDados) throws Exception;

    List<ParceiroFidelidadePontosVO> consultarPorCodigo(Integer valorConsulta, boolean controlarAcesso, int nivelMontarDados) throws Exception;

    List<ParceiroFidelidadePontosVO> consultarPorHistorico(String valorConsulta, boolean controlarAcesso, int nivelMontarDados) throws Exception;

    void inserirPontos(ParceiroFidelidadePontosVO parceiroFidelidadePontosVO, String retornoAPIParceiro) throws Exception;

    void setIdEntidade(String aIdEntidade);

    List<ParceiroFidelidadePontosVO> consultarRelatorio(Date dataInicio, Date dataFinal, Integer pessoa) throws Exception;

    void alterarTipoPonto(TipoPontoParceiroFidelidadeEnum tipoPontoParceiroFidelidade, Integer codigo) throws Exception;

    List<ParceiroFidelidadePontosVO> consultarPorReciboPagamento(Integer reciboPagamento, int nivelMontarDados) throws Exception;

}
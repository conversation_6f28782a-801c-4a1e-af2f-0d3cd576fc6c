package negocio.interfaces.basico;
import negocio.comuns.basico.RespostaPerguntaVO;
import java.util.List;

/**
 * Interface reponsável por criar uma estrutura padrão de comunidação entre a camada de controle
 * e camada de negócio (em especial com a classe Façade). Com a utilização desta interface 
 * é possível substituir tecnologias de uma camada da aplicação com mínimo de impacto nas demais.
 * Além de padronizar as funcionalidades que devem ser disponibilizadas pela camada de negócio, por intermédio
 * de sua classe Façade (responsável por persistir os dados das classes VO).
*/
public interface RespostaPerguntaInterfaceFacade extends SuperInterface {
	

    public RespostaPerguntaVO novo() throws Exception;
    public void incluir(RespostaPerguntaVO obj) throws Exception;
    public void alterar(RespostaPerguntaVO obj) throws Exception;
    public void excluir(RespostaPerguntaVO obj) throws Exception;
    public RespostaPerguntaVO consultarPorChavePrimaria(Integer codigo, int nivelMontarDados) throws Exception;
    public List consultarPorCodigo(Integer valorConsulta, boolean controlarAcesso, int nivelMontarDados) throws Exception;
    public List consultarPorDescricaoRespota(String valorConsulta, boolean controlarAcesso, int nivelMontarDados) throws Exception;    
    public void setIdEntidade(String aIdEntidade);
    /**
     * Operação responsável por excluir todos os objetos da <code>RespostaPerguntaVO</code> no BD.
     * Faz uso da operação <code>excluir</code> disponível na classe <code>RespostaPergunta</code>.
     * @param <code>pergunta</code> campo chave para exclusão dos objetos no BD.
     * @exception Exception  Erro de conexão com o BD ou restrição de acesso a esta operação.
     */
    public void excluirRespostaPerguntas(Integer pergunta) throws Exception;
    /**
     * Operação responsável por alterar todos os objetos da <code>RespostaPerguntaVO</code> contidos em um Hashtable no BD.
     * Faz uso da operação <code>excluirRespostaPerguntas</code> e <code>incluirRespostaPerguntas</code> disponíveis na classe <code>RespostaPergunta</code>.
     * @param objetos  List com os objetos a serem alterados ou incluídos no BD.
     * @exception Exception  Erro de conexão com o BD ou restrição de acesso a esta operação.
     */
    public void alterarRespostaPerguntas(Integer pergunta, List<RespostaPerguntaVO>  objetos) throws Exception;

    /**
     * Operação responsável por incluir objetos da <code>RespostaPerguntaVO</code> no BD.
     * Garantindo o relacionamento com a entidade principal <code>basico.Pergunta</code> através do atributo de vínculo.
     * @param objetos List contendo os objetos a serem gravados no BD da classe.
     * @exception Exception  Erro de conexão com o BD ou restrição de acesso a esta operação.
     */
    public void incluirRespostaPerguntas(Integer perguntaPrm, List objetos) throws Exception;

    /**
     * Operação responsável por consultar todos os <code>RespostaPerguntaVO</code> relacionados a um objeto da classe <code>basico.Pergunta</code>.
     * @param pergunta  Atributo de <code>basico.Pergunta</code> a ser utilizado para localizar os objetos da classe <code>RespostaPerguntaVO</code>.
     * @return List  Contendo todos os objetos da classe <code>RespostaPerguntaVO</code> resultantes da consulta.
     * @exception Exception  Erro de conexão com o BD ou restrição de acesso a esta operação.
     */
    public List consultarRespostaPerguntas(Integer pergunta, int nivelMontarDados) throws Exception;
}
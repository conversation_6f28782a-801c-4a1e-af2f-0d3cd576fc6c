package negocio.interfaces.basico;

import org.json.JSONObject;
import negocio.comuns.basico.QuestionarioClienteVO;
import negocio.comuns.basico.QuestionarioVO;
import negocio.comuns.basico.enumerador.TipoServicoEnum;

import java.util.List;

/**
 * Interface reponsável por criar uma estrutura padrão de comunidação entre a camada de controle
 * e camada de negócio (em especial com a classe Façade). Com a utilização desta interface
 * é possível substituir tecnologias de uma camada da aplicação com mínimo de impacto nas demais.
 * Além de padronizar as funcionalidades que devem ser disponibilizadas pela camada de negócio, por intermédio
 * de sua classe Façade (responsável por persistir os dados das classes VO).
 */
public interface QuestionarioInterfaceFacade extends SuperInterface {

    public QuestionarioVO novo() throws Exception;

    public void incluir(QuestionarioVO obj) throws Exception;
    public void incluirSemCommit(QuestionarioVO obj) throws Exception;

    public void alterar(QuestionarioVO obj) throws Exception;

    public void excluir(QuestionarioVO obj) throws Exception;

    public QuestionarioVO consultarPorChavePrimaria(Integer codigo, int nivelMontarDados) throws Exception;

    public List consultarPorCodigo(Integer valorConsulta, boolean controlarAcesso, int nivelMontarDados) throws Exception;

    public QuestionarioVO consultarPorCodigoExato(Integer valorConsulta, boolean controlarAcesso, int nivelMontarDados) throws Exception;

    public List consultarPorDescricao(String valorConsulta, String tipoQuestionario, boolean controlarAcesso, int nivelMontarDados) throws Exception;

    public void setIdEntidade(String aIdEntidade);

    public void incluir(QuestionarioVO obj, boolean centralEventos) throws Exception;

    public void alterar(QuestionarioVO obj, boolean centralEventos) throws Exception;

    public void alterarSemCommit(QuestionarioVO obj) throws Exception ;

    public void excluir(QuestionarioVO obj, boolean centralEventos) throws Exception;

    public QuestionarioVO consultarPorDescricao(String valorConsulta, int nivelMontarDados) throws Exception;

    public String consultarJSON(boolean cadastroPesquisa) throws Exception;

    public List consultarParaImpressao(String filtro, String ordem, String campoOrdenacao, int i, boolean cadastroPesquisa) throws Exception;

    public QuestionarioVO obterQuestionario(Integer empresa, String t) throws Exception;

    public QuestionarioClienteVO responderQuestionario(Integer cliente, Integer questionario, JSONObject respostas, String tipo, boolean somenteValidar) throws Exception;

    void alterarFundoImagem(QuestionarioVO obj) throws Exception;

    List<QuestionarioVO> consultarQuestionariosPorTipo(TipoServicoEnum tipoServicoEnum, boolean somenteAtivos, int nivelMontarDados) throws Exception;
}

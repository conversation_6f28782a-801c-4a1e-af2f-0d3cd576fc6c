package negocio.interfaces.basico;

import negocio.comuns.basico.ColaboradorVO;
import negocio.comuns.basico.EmpresaVO;
import negocio.comuns.financeiro.ReciboClienteConsultorVO;
import negocio.comuns.financeiro.ReciboPagamentoVO;

import java.util.Date;

import negocio.comuns.contrato.ContratoVO;

/**
 * Created with IntelliJ IDEA.
 * User: glauco
 * Date: 15/02/13
 * Time: 13:56
 */
public interface ReciboClienteConsultorInterfaceFacade extends SuperInterface {

    public ReciboClienteConsultorVO novo() throws Exception;

    public void incluir(ReciboClienteConsultorVO obj) throws Exception;

    public void alterar(ReciboClienteConsultorVO obj) throws Exception;

    public void excluir(ReciboClienteConsultorVO obj) throws Exception;

    void excluirPorReciboPagamento(final Integer codRecibo) throws Exception;

    public void atualizarRecibosSemConsultor(ColaboradorVO consultor, EmpresaVO empresaVO) throws Exception;

    public void incluirComRecibo(ReciboPagamentoVO obj) throws Exception;
    
    public void alterarConsultorContrato(ContratoVO contratoVO, Integer cliente) throws Exception;

    public ColaboradorVO consultarPorReciboCliente(Integer empresa, Integer codRecibo, Integer codCliente) throws Exception;

    void processarReciboClienteConsultor(int codRecibo) throws Exception;

    public boolean excluirDados() throws Exception;

    public boolean selectMigradorPovoarReciboClienteConsultor() throws Exception;

    public boolean existeReciboVendaAvulsaConsumidorNaData(Integer codigoEmpresa, Date dataInicio, Date dataFim) throws Exception;

    public double consultarValoresReciboVendaAvulsaNaData(Integer codigoEmpresa, Date dataInicio, Date dataFim) throws Exception;
}

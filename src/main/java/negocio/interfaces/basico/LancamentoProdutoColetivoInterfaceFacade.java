/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */

package negocio.interfaces.basico;

import java.sql.SQLException;
import java.util.List;
import negocio.comuns.arquitetura.UsuarioVO;
import negocio.comuns.basico.AmostraClienteTO;
import negocio.comuns.basico.LancamentoProdutoColetivoVO;
import negocio.comuns.contrato.ContratoVO;

/**
 *
 * <AUTHOR>
 */
public interface LancamentoProdutoColetivoInterfaceFacade extends SuperInterface{

    public void incluir(LancamentoProdutoColetivoVO lancamento) throws Exception;

    public void excluir(LancamentoProdutoColetivoVO lancamento) throws Exception;

    public void alterar(LancamentoProdutoColetivoVO lancamento) throws Exception;

    public List<LancamentoProdutoColetivoVO> consultarTodos(Integer nivelMontarDados) throws Exception;

    public LancamentoProdutoColetivoVO consultarPorChavePrimaria(Integer codigo, Integer nivelMontarDados) throws Exception;

    public String consultarJSON(Integer empresa) throws Exception;

    public List<AmostraClienteTO> consultarAmostraLancamentoColetivo(LancamentoProdutoColetivoVO lancamento, boolean ignorarJaLancados) throws Exception;

    public void atualizarJaFoiLancado(LancamentoProdutoColetivoVO lancamento) throws Exception;

    public int lancarProdutos(LancamentoProdutoColetivoVO lancamento, boolean ignorarJaLancados) throws Exception;

    public List<AmostraClienteTO> consultarJaAlcancadosLancamentoColetivo(LancamentoProdutoColetivoVO lancamento) throws Exception;

    public List<LancamentoProdutoColetivoVO> consultarLancamentosEscopoContrato(ContratoVO contrato) throws Exception;

    public void estornarProdutosAbertosLancadosColetivamente(LancamentoProdutoColetivoVO lancamento, UsuarioVO usuario) throws Exception;
    
    public List consultarParaImpressao(String filtro, String ordem, String campoOrdenacao, int codigoEmpresa) throws SQLException;

}

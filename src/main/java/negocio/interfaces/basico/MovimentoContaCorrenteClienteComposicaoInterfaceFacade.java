package negocio.interfaces.basico;

import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.Date;
import java.util.List;

import negocio.comuns.basico.ColaboradorVO;
import negocio.comuns.basico.MovimentoContaCorrenteClienteComposicaoVO;
import negocio.comuns.basico.MovimentoContaCorrenteClienteFiltroVO;
import negocio.comuns.basico.MovimentoContaCorrenteClienteVO;
import br.com.pactosolucoes.estrutura.paginacao.ConfPaginacao;

public interface MovimentoContaCorrenteClienteComposicaoInterfaceFacade extends SuperInterface {

    public MovimentoContaCorrenteClienteComposicaoVO novo() throws Exception;

    public void incluir(MovimentoContaCorrenteClienteComposicaoVO obj) throws Exception;
    
    public void alterar(MovimentoContaCorrenteClienteComposicaoVO obj) throws Exception;

    public void alterarSemCommit(MovimentoContaCorrenteClienteComposicaoVO obj, boolean centralEventos) throws Exception;
    
    public void alterarComposicao(Integer novo, Integer atual) throws SQLException;

    public void excluir(MovimentoContaCorrenteClienteComposicaoVO obj) throws Exception;

    public MovimentoContaCorrenteClienteComposicaoVO consultarPorChavePrimaria(Integer codigo, int nivelMontarDados) throws Exception;

    public List <MovimentoContaCorrenteClienteComposicaoVO> consultarPorCodigoMovimento(Integer valorConsulta, boolean controlarAcesso, int nivelMontarDados) throws Exception;
    
    public MovimentoContaCorrenteClienteComposicaoVO  consultarPorCodigoMovPagamento(Integer valorConsulta, int nivelMontarDados) throws Exception;

    public void incluirSemCommit(MovimentoContaCorrenteClienteComposicaoVO obj,boolean centralEventos) throws Exception;

    public void incluirSemCommit(MovimentoContaCorrenteClienteComposicaoVO obj) throws Exception ;
    
    public void alterar(MovimentoContaCorrenteClienteComposicaoVO obj, boolean centralEventos) throws Exception;
    
    public void excluir(MovimentoContaCorrenteClienteComposicaoVO obj, boolean centralEventos) throws Exception;
    
    public void adicionarComposicao(MovimentoContaCorrenteClienteVO obj, boolean centralEventos) throws Exception;
    
	public void excluirComposicaoMovPagamento(Integer movpagamento) throws Exception;
	
	public void excluirComposicaoMovimento(Integer movimento) throws Exception;
}

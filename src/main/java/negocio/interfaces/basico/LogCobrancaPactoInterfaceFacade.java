package negocio.interfaces.basico;

import negocio.comuns.arquitetura.PaginadorDTO;
import negocio.comuns.basico.LogCobrancaPactoVO;

import java.util.Date;
import java.util.List;

/**
 * Created by <PERSON><PERSON> on 14/04/2016
 */
public interface LogCobrancaPactoInterfaceFacade extends SuperInterface {

    void incluir(LogCobrancaPactoVO obj) throws Exception;

    LogCobrancaPactoVO consultarChavePrimaria(Integer codigo, int nivelMontarDados) throws Exception;

    List<LogCobrancaPactoVO> consultarTodas(Integer empresa, PaginadorDTO paginadorDTO, int nivelMontarDados) throws Exception;

    List<LogCobrancaPactoVO> consultar(Integer empresa, Date dataCobrancaInicio, Date dataCobrancaFinal,
                                       Integer limit, PaginadorDTO paginadorDTO, int nivelMontarDados) throws Exception;
}

package negocio.interfaces.basico;

import negocio.comuns.basico.PaisVO;

import java.util.List;

/**
 * Interface reponsável por criar uma estrutura padrão de comunidação entre a camada de controle
 * e camada de negócio (em especial com a classe Façade). Com a utilização desta interface
 * é possível substituir tecnologias de uma camada da aplicação com mínimo de impacto nas demais.
 * Além de padronizar as funcionalidades que devem ser disponibilizadas pela camada de negócio, por intermédio
 * de sua classe Façade (responsável por persistir os dados das classes VO).
 */
public interface PaisInterfaceFacade extends SuperInterface {


    public PaisVO novo() throws Exception;

    public void incluir(PaisVO obj) throws Exception;

    public void alterar(PaisVO obj) throws Exception;

    public void excluir(PaisVO obj) throws Exception;

    public PaisVO consultarPorChavePrimaria(Integer codigo, int nivelMontarDados) throws Exception;

    public List consultarPorCodigo(Integer valorConsulta, boolean controlarAcesso, int nivelMontarDados) throws Exception;

    public PaisVO consultarPorCodigoExato(Integer valorConsulta, boolean controlarAcesso, int nivelMontarDados) throws Exception;

    public List consultarPorNome(String valorConsulta, boolean controlarAcesso, int nivelMontarDados) throws Exception;

    public List consultarEstadoPorPais(Integer valorConsulta, int nivelMontarDados) throws Exception;

    public PaisVO consultarPorNome(String valorConsulta, int nivelMontarDados) throws Exception;

    public void setIdEntidade(String aIdEntidade);

    public void incluir(PaisVO obj, boolean centralEventos) throws Exception;

    public void incluirSemCommit(PaisVO obj) throws Exception;

    public void alterar(PaisVO obj, boolean centralEventos) throws Exception;

    public void alterarSemCommit(PaisVO obj) throws Exception ;

    public void excluir(PaisVO obj, boolean centralEventos) throws Exception;

    public String consultarJSON() throws Exception;

    public List consultarParaImpressao(String filtro, String ordem, String campoOrdenacao, int i) throws Exception;

}
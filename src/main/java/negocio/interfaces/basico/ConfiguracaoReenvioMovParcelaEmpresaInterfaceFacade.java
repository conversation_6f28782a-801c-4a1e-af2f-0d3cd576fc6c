/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package negocio.interfaces.basico;

import negocio.comuns.arquitetura.UsuarioVO;
import negocio.comuns.basico.ConfiguracaoReenvioMovParcelaEmpresaVO;
import negocio.comuns.basico.EmpresaVO;
import negocio.comuns.financeiro.ConvenioCobrancaVO;
import negocio.comuns.financeiro.enumerador.SituacaoConvenioCobranca;

import java.util.List;

/*
 * <AUTHOR> 20/10/2017
 */
public interface ConfiguracaoReenvioMovParcelaEmpresaInterfaceFacade extends SuperInterface {

    void incluir(ConfiguracaoReenvioMovParcelaEmpresaVO obj) throws Exception;

    void alterar(ConfiguracaoReenvioMovParcelaEmpresaVO obj) throws Exception;

    void excluir(ConfiguracaoReenvioMovParcelaEmpresaVO obj) throws Exception;

    void excluirConfiguracaoReenvioMovParcelaEmpresa(Integer empresa) throws Exception;

    void alterarConfiguracaoReenvioMovParcelaEmpresa(Integer empresa, List<ConfiguracaoReenvioMovParcelaEmpresaVO> objetos) throws Exception;

    void excluirConfiguracaoReenvioMovParcelaEmpresaCodigosAtuais(final Integer codEmpresa, final String codigosAtuais) throws Exception;

    void incluirConfiguracaoReenvioMovParcelaEmpresa(Integer empresaPrm, List<ConfiguracaoReenvioMovParcelaEmpresaVO> objetos) throws Exception;

    List<ConfiguracaoReenvioMovParcelaEmpresaVO> consultarConfiguracaoReenvioMovParcelaEmpresa(Integer empresa, int nivelMontarDados) throws Exception;

    ConfiguracaoReenvioMovParcelaEmpresaVO consultarPorChavePrimaria(Integer codigo, int nivelMontarDados) throws Exception;

    List<ConvenioCobrancaVO> consultarConvenioReenvioAutomatico(Integer empresa, int nivelMontarDados, final Integer[] tipos, SituacaoConvenioCobranca situacao) throws Exception;

    boolean existeConfiguracaoReenvioMovParcelaEmpresa(Integer empresa, Integer convenioCobranca) throws Exception;

    void gravarLogConfiguracaoRetentativa(EmpresaVO empresaVO, UsuarioVO usuarioVO,
                                          List<ConfiguracaoReenvioMovParcelaEmpresaVO> listaAnterior,
                                          List<ConfiguracaoReenvioMovParcelaEmpresaVO> listaAtual) throws Exception;
}

package negocio.interfaces.basico;

import relatorio.negocio.comuns.sad.RenovacaoContratoSinteticoTO;

import java.util.Date;
import java.util.List;

/**
 * Façade responsável por carregar os dados do relatório sintético de renovação.
 *
 * <AUTHOR>
 * @since 06/08/2018
 */
public interface RenovacaoSinteticoFacade extends SuperInterface {

    /**
     * Carrega todos os dados para o relatório sintético de renovação de contratos
     *
     * @param dataInicial   Data inicial (filtro do relatório)
     * @param dataFinal     Data final (filtro do relatório)
     * @param codigoEmpresa Código da empresa vigente
     * @param isBolsa       O relatório deve incluir as bolsas
     * @param isTrancado    O relatório deve incluir os trancamentos
     * @param isCancelado   O relatório deve incluir os cancelamentos
     * @return O relatório sintético de renovação de contratos
     */
    List<RenovacaoContratoSinteticoTO> consultarRenovacaoContratosSinteticos(Date dataInicial, Date dataFinal,
                                                                             Integer codigoEmpresa, Boolean isBolsa,
                                                                             Boolean isTrancado, Boolean isCancelado) throws Exception;

}

package negocio.interfaces.basico;


import negocio.comuns.ConfiguracaoEmpresaTotemVO;
import negocio.comuns.TotemTO;
import negocio.comuns.basico.enumerador.ConfigTotemEnum;

import java.util.List;
import java.util.Map;

public interface ConfiguracaoEmpresaTotemInterfaceFacade extends SuperInterface {

    public ConfiguracaoEmpresaTotemVO novo() throws Exception;

    public void incluir(ConfiguracaoEmpresaTotemVO obj) throws Exception;

    public void excluir(Integer empresa) throws Exception;

    public List<TotemTO> obterConfigs(Integer empresa, String key) throws Exception;

    public void gravar(List<TotemTO> totems, Integer empresa) throws Exception;
}

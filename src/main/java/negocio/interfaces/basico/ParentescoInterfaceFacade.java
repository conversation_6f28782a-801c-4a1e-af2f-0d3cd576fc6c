package negocio.interfaces.basico;

import negocio.comuns.basico.ParentescoVO;

import java.util.List;

/**
 * Interface reponsável por criar uma estrutura padrão de comunidação entre a camada de controle
 * e camada de negócio (em especial com a classe Façade). Com a utilização desta interface
 * é possível substituir tecnologias de uma camada da aplicação com mínimo de impacto nas demais.
 * Além de padronizar as funcionalidades que devem ser disponibilizadas pela camada de negócio, por intermédio
 * de sua classe Façade (responsável por persistir os dados das classes VO).
 */
public interface ParentescoInterfaceFacade extends SuperInterface {


    public ParentescoVO novo() throws Exception;

    public void incluir(ParentescoVO obj) throws Exception;

    public void alterar(ParentescoVO obj) throws Exception;

    public void alterarSemCommit(ParentescoVO obj) throws Exception ;

    public void excluir(ParentescoVO obj) throws Exception;

    public ParentescoVO consultarPorChavePrimaria(Integer codigo, int nivelMontarDados) throws Exception;

    public List consultarPorCodigo(Integer valorConsulta, boolean controlarAcesso, int nivelMontarDados) throws Exception;

    public ParentescoVO consultarPorCodigoExato(Integer valorConsulta, boolean controlarAcesso, int nivelMontarDados) throws Exception;

    public List consultarPorDescricao(String valorConsulta, boolean controlarAcesso, int nivelMontarDados) throws Exception;

    public ParentescoVO obterParentescoDescricao(String valorConsulta, boolean controlarAcesso, int nivelMontarDados) throws Exception;

    public List consultarPorIdadeLimiteDependencia(Integer valorConsulta, boolean controlarAcesso, int nivelMontarDados) throws Exception;

    public ParentescoVO consultarPorIdadeLimiteDependenciaExata(Integer valorConsulta, boolean controlarAcesso, int nivelMontarDados) throws Exception;

    public void setIdEntidade(String aIdEntidade);

    public String consultarJSON() throws Exception;

    public List consultarParaImpressao(String filtro, String ordem, String campoOrdenacao, int i) throws Exception;

    ParentescoVO obterParentescoCriandoSeNaoExiste(String valorConsulta) throws Exception;
}
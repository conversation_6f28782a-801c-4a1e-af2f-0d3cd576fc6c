package negocio.interfaces.basico;

import negocio.comuns.utilitarias.ConfiguracaoSistemaUsuarioVO;

import java.util.List;
import negocio.comuns.basico.enumerador.ConfiguracaoUsuarioEnum;

/**
 * Created by <PERSON> on 27/02/2016.
 */

public interface ConfiguracaoSistemaUsuarioInterfaceFacade extends SuperInterface  {

    public void incluir(ConfiguracaoSistemaUsuarioVO obj) throws Exception;

    public ConfiguracaoSistemaUsuarioVO consultarUltimoPorCodigoUsuario(int codigoUsuario, ConfiguracaoUsuarioEnum tipo) throws Exception;

    public void alterar(ConfiguracaoSistemaUsuarioVO obj) throws Exception;

}

package negocio.interfaces.basico;

import br.com.pactosolucoes.comuns.json.DadosBITreinoJSON;
import br.com.pactosolucoes.enumeradores.TipoColaboradorEnum;
import negocio.comuns.arquitetura.UsuarioVO;
import negocio.comuns.basico.HistoricoVinculoVO;

import java.util.Date;
import java.util.List;

/**
 * Interface reponsável por criar uma estrutura padrão de comunidação entre a camada de controle
 * e camada de negócio (em especial com a classe Façade). Com a utilização desta interface
 * é possível substituir tecnologias de uma camada da aplicação com mínimo de impacto nas demais.
 * Além de padronizar as funcionalidades que devem ser disponibilizadas pela camada de negócio, por intermédio
 * de sua classe Façade (responsável por persistir os dados das classes VO).
 */
public interface HistoricoVinculoInterfaceFacade extends SuperInterface {


    public HistoricoVinculoVO novo() throws Exception;

    public void incluir(HistoricoVinculoVO obj) throws Exception;

    public void alterar(HistoricoVinculoVO obj) throws Exception;

    public void excluir(HistoricoVinculoVO obj) throws Exception;

    public void excluirSemCommit(HistoricoVinculoVO obj) throws Exception;

    public HistoricoVinculoVO consultarPorChavePrimaria(Integer codigo, int nivelMontarDados) throws Exception;

    public List<HistoricoVinculoVO> consultarPorCodigoClienteCodigoConsultor(Integer cliente, Integer consultor, int nivelMontarDados) throws Exception;

    public HistoricoVinculoVO consultarPorMaisRecenteClienteConsultor(Integer cliente, Integer consultor, int nivelMontarDados) throws Exception;

    public List<HistoricoVinculoVO> consultarUltimoHistoricoSaidaTransferencia(Date dataTransferencia, UsuarioVO respTransferencia, int nivelMontarDados) throws Exception;

    public List consultarPorCodigo(Integer valorConsulta, boolean controlarAcesso, int nivelMontarDados) throws Exception;

    public List consultarPorSituacaoCliente(String valorConsulta, int nivelMontarDados) throws Exception;

    public List consultarPorDataRegistro(Date prmIni, Date prmFim, boolean controlarAcesso, int nivelMontarDados) throws Exception;

    public List consultarPorTipoHistoricoVinculo(String valorConsulta, boolean controlarAcesso, int nivelMontarDados) throws Exception;

    public List consultarPorSituacaoColaborador(String valorConsulta, int nivelMontarDados) throws Exception;

    public void setIdEntidade(String aIdEntidade);

    public void incluirSemCommit(HistoricoVinculoVO obj) throws Exception;

    public void incluirSemCommit(HistoricoVinculoVO obj, boolean controlarLog) throws Exception;

    /**
     * Responsável por montar os dados de vários objetos, resultantes de uma consulta ao banco de dados (<code>ResultSet</code>).
     * Faz uso da operação <code>montarDados</code> que realiza o trabalho para um objeto por vez.
     *
     * @return List Contendo vários objetos da classe <code>HistoricoVinculoVO</code> resultantes da consulta.
     */
    public List<HistoricoVinculoVO> consultarPorCodigoClienteOrganizadorCarteira(Integer valorConsulta, boolean controlarAcesso, int nivelMontarDados) throws Exception;
    public List<HistoricoVinculoVO> consultarPorCodigoClientePeriodo(Date dataInicio, Date datafim, Integer valorConsulta, boolean controlarAcesso, int nivelMontarDados) throws Exception;

    public Boolean verificarVinculoNaData(Date data, Integer codigoCliente, Integer codigoColaborador) throws Exception;

    public Boolean verificarVinculoNaData(Date data, Integer codigoCliente, Integer codigoColaborador, String tipoColaborador) throws Exception;

    public List<HistoricoVinculoVO> consultarVinculoNaData(Date dataFinal, Integer codigoColaborador, int nivelMontarDados, TipoColaboradorEnum... tiposColaborador) throws Exception;

    public Boolean verificarVinculoNoPeriodo( Date data, Integer codigoCliente, Integer codigoColaborador) throws Exception;
    
    public void preencherDadosBITreino(DadosBITreinoJSON dados, Date fim, Integer professor) throws Exception;
    
    public List<Integer> obterTrocasCarteira(Integer empresa, Date inicio, Date fim, Integer professor) throws Exception;

    public void incluirHistoricoVinculoSaidaGestaoPersonal(Date dataFinal, Integer codigoColaborador, Integer codigoCliente, UsuarioVO usuarioVO) throws Exception;

}

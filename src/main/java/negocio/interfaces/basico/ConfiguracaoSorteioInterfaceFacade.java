package negocio.interfaces.basico;

import negocio.comuns.basico.ConfiguracaoSorteioVO;

/**
 * Created by GlaucoT on 16/02/2016
 */
public interface ConfiguracaoSorteioInterfaceFacade extends SuperInterface {

    ConfiguracaoSorteioVO obterConfiguracao(Integer codEmpresa) throws Exception;

    void incluir(ConfiguracaoSorteioVO config) throws Exception;

    void alterar(ConfiguracaoSorteioVO config) throws Exception;
}

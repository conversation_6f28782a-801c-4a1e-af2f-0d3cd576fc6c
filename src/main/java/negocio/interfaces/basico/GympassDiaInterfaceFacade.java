package negocio.interfaces.basico;

import negocio.comuns.basico.GympassDiaVO;

import java.util.List;

/**
 * Interface reponsável por criar uma estrutura padrão de comunidação entre a camada de controle
 * e camada de negócio (em especial com a classe Façade). Com a utilização desta interface
 * é possível substituir tecnologias de uma camada da aplicação com mínimo de impacto nas demais.
 * Além de padronizar as funcionalidades que devem ser disponibilizadas pela camada de negócio, por intermédio
 * de sua classe Façade (responsável por persistir os dados das classes VO).
 */
public interface GympassDiaInterfaceFacade extends SuperInterface {

    public GympassDiaVO novo() throws Exception;

    public void incluir(int codigo, GympassDiaVO obj) throws Exception;

    public void alterar(int codigo, GympassDiaVO obj) throws Exception;

    public List<GympassDiaVO> consultarPorCodigo(int codigo, int nivelMontarDados) throws Exception;
}
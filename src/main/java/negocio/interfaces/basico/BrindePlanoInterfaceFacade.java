package negocio.interfaces.basico;

import negocio.comuns.basico.BrindeVO;

import java.sql.SQLException;
import java.util.List;

public interface BrindePlanoInterfaceFacade extends SuperInterface {

    /**
     * Inclui na tabela brindeplano todos os planos contidos no atributo BrindeVO.codigoPlanos
     *
     * @param brinde
     * @throws SQLException
     */
    void incluir(BrindeVO brinde) throws SQLException;

    /**
     * Remove todos os registros da tabela brindeplano para este brinde
     * e adiciona os novos registro contidos no atributo BrindeVO.codigoPlanos
     *
     * @param brinde
     * @throws SQLException
     */
    void atualizar(Brinde<PERSON> brinde) throws SQLException;

    List<Integer> consultarCodigosPlanos(Integer codigoBrinde) throws SQLException;
}

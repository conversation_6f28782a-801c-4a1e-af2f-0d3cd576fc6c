package negocio.interfaces.basico;

import negocio.comuns.basico.CategoriaVO;
import negocio.comuns.financeiro.MovParcelaVO;

import java.sql.SQLException;
import java.util.List;

/**
 * Interface reponsável por criar uma estrutura padrão de comunidação entre a camada de controle
 * e camada de negócio (em especial com a classe Façade). Com a utilização desta interface
 * é possível substituir tecnologias de uma camada da aplicação com mínimo de impacto nas demais.
 * Além de padronizar as funcionalidades que devem ser disponibilizadas pela camada de negócio, por intermédio
 * de sua classe Façade (responsável por persistir os dados das classes VO).
 */
public interface CategoriaInterfaceFacade extends SuperInterface {


    public CategoriaVO novo() throws Exception;

    public void incluir(CategoriaVO obj) throws Exception;

    public void alterar(CategoriaVO obj) throws Exception;

    public void excluir(CategoriaVO obj) throws Exception;

    public CategoriaVO consultarPorChavePrimaria(Integer codigo, int nivelMontarDados) throws Exception;

    public List consultarPorCodigo(Integer valorConsulta, boolean controlarAcesso, int nivelMontarDados) throws Exception;

    public CategoriaVO consultarPorCodigo(Integer valorConsulta, int nivelMontarDados) throws Exception;

    public List<CategoriaVO> consultarPorNome(String valorConsulta, boolean controlarAcesso, int nivelMontarDados) throws Exception;

    public List consultarPorTipoCategoria(String valorConsulta, boolean controlarAcesso, int nivelMontarDados) throws Exception;

    public List consultarPorNrConvitePermitido(Integer valorConsulta, boolean controlarAcesso, int nivelMontarDados) throws Exception;

    public void setIdEntidade(String aIdEntidade);

    public void incluir(CategoriaVO obj, boolean centralEventos) throws Exception;

    public void alterar(CategoriaVO obj, boolean centralEventos) throws Exception;

    public void alterarSemCommit(CategoriaVO obj) throws Exception;

    public void excluir(CategoriaVO obj, boolean centralEventos) throws Exception;

    public String consultarJSON() throws Exception;

    public List consultarParaImpressao(String filtro, String ordem, String campoOrdenacao, int i) throws SQLException;
    
    public void montarCategoriasParcelas(List<MovParcelaVO> parcelas);

    CategoriaVO consultarPorNomeExterno(final String nomeExterno, int nivelMontarDados) throws Exception;

}

package negocio.interfaces.basico;

import negocio.comuns.basico.ColaboradorVO;
import negocio.comuns.basico.EstornoObservacaoVO;

import java.sql.Date;
import java.util.List;
import negocio.comuns.contrato.ContratoVO;

/**
 * Created with IntelliJ IDEA.
 * User: GlaucoT
 * Date: 03/12/13
 * Time: 16:10
 * To change this template use File | Settings | File Templates.
 */
public interface EstornoObservacaoInterfaceFacade extends SuperInterface {

    public void incluir(EstornoObservacaoVO obj) throws Exception;

    public void alterar(EstornoObservacaoVO obj) throws Exception;

    public void excluir(EstornoObservacaoVO obj) throws Exception;

    public List<EstornoObservacaoVO> consultar(Integer codCliente, int nivelMontarDados) throws Exception;

    public List<EstornoObservacaoVO> consultarPorDataEstorno(Date dataEstorno, int nivelMontarDados) throws Exception;

    public int contarPorNomeEntidadePorDataAlteracaoPorOperacao(Date dataAlteracaoInicial, Date dataAlteracaoFim, Integer codEmpresa, boolean buscarComAdministrador, boolean buscarComRecorrencia, List<ColaboradorVO> lista, int nivelMontarDados) throws Exception;

    public List<EstornoObservacaoVO> consultarPorNomeEntidadePorDataAlteracaoPorOperacao(Date dataAlteracaoInicial, Date dataAlteracaoFim, Integer codEmpresa, boolean buscarComAdministrado, boolean buscarComRecorrencia, List<ColaboradorVO> lista, int nivelMontarDados) throws Exception;
    
     public void gravarEstornoObservacao(ContratoVO contrato,final String justificativa) throws Exception;
}

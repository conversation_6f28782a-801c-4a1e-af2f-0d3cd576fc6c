package negocio.interfaces.basico;

import br.com.pactosolucoes.agendatotal.json.AgendamentoDesmarcadoJSON;
import negocio.comuns.basico.AulaDesmarcadaVO;
import negocio.comuns.basico.ReposicaoVO;
import negocio.comuns.plano.HorarioTurmaVO;

import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Set;

import negocio.comuns.arquitetura.UsuarioVO;
import negocio.comuns.contrato.ContratoVO;

public interface AulaDesmarcadaInterfaceFacade extends SuperInterface {

    public void incluir(final AulaDesmarcadaVO obj) throws Exception;
    public void incluirSemCommit(final AulaDesmarcadaVO obj) throws Exception;

    public void alterar(final AulaDesmarcadaVO obj) throws Exception;


    public List<AulaDesmarcadaVO> consultarPorCliente(int cliente, int empresa) throws Exception;

    public AulaDesmarcadaVO consultarAulaDesmarcadaPorDiaHorarioContrato(int cliente, int empresa, int contrato, int turma, int horarioTurma, Date dataOrigem,int nivelMontarDados,boolean permiteReposicao, boolean aindaNaoReposta) throws Exception;

    public AulaDesmarcadaVO consultarAulaDesmarcadaPorDiaHorarioContratoOrigemEReposicao(int cliente,
                                                                                        int empresa,
                                                                                        int contrato,
                                                                                        Integer contratoOrigem,
                                                                                        int turma,
                                                                                        int horarioTurma,
                                                                                        Date dataOrigem,
                                                                                        int nivelMontarDados) throws Exception;

    public AulaDesmarcadaVO consultar(Integer codigoContrato, Integer codigoHorarioTurma, Date dataOrigem, int nivelMontarDados)throws Exception;

    public void validarDados(AulaDesmarcadaVO obj) throws Exception;

    public Integer contarAulasDesmarcadas(int contrato, int empresa, int turma, int horarioTurma, Date dia, Date datainicio, Date datafim) throws Exception;

    Map<Date, Integer> contarAulasDesmarcadas(Integer horarioTurma, Date datainicio, Date datafim) throws Exception;

    public Integer consultarTotalAulasDesmarcadas(Integer codigoEmpresa, Integer codigoHorarioTurma, Date data, Set<Integer> codClientes) throws Exception;

    public List<AulaDesmarcadaVO> consultarListaAulasDesmarcadas(int contrato, int empresa, int turma, int nivelMontarDados) throws Exception;

    public void atualizarDataReposicao(final AulaDesmarcadaVO obj) throws Exception;

    public Integer nrAlunosReposicao(HorarioTurmaVO obj, final String periodo) throws Exception;

    public AulaDesmarcadaVO consultarAulaDesmarcadaPorDataReposicaoHorarioContrato(int cliente, int empresa, int contrato, int turma, int horarioTurma, Date dataReposicao,int nivelMontarDados) throws Exception;
    AulaDesmarcadaVO consultarPorCodigo(Integer codigo, int nivelMontarDados) throws Exception;
    AulaDesmarcadaVO consultarAulaDesmarcada(HorarioTurmaVO horarioTurmaOrigem, Date dataOrigem, Integer codigoContrato, int nivelMontarDados)throws Exception;
    
    public List<AgendamentoDesmarcadoJSON> consultarAgendamentosDesmarcados(Date inicio, Date fim, Integer empresa)throws Exception;
    
    public String obterIdentificadorAulaDesmarcada(Integer cliente, Integer contrato, Integer modalidade)throws Exception;
    
    public Map<Date, List<Integer>> consultarAgendamentosDesmarcados(Integer cliente)throws Exception;

    public AulaDesmarcadaVO consultarPorReposicao(Integer codigoReposicao,int nivelMontarDados)throws Exception;

    public void excluirAulaDesmarcadaPorRetornoAfastamentoAntecipado(Integer codigoContrato, Date dataRetorno, Date dataFimAfastamento)throws Exception;

    List<AulaDesmarcadaVO>consultarAulaDesmarcadaQueNaoPodeSerReposta(Integer codigoContrato, Date dataBase, int nivelMontarDados)throws Exception;

    void atualizaPermiteReporAulaDesmarcada(Integer codigoAulaDesmarcada, boolean permiteReporAula)throws Exception;

    boolean desmarcouAulaParaNaoPermitirReposicao(Integer codigoContrato, Integer codigoHorarioTurma, Date dataAula)throws Exception;

    boolean existeAulaDesmarcadaSemReporParaHorarioTurma(int codigoHorarioTurma)throws Exception;

    public void excluirAulasDesmarcadasFuturasSemReposicao(ContratoVO contratoVO, final Integer horarioTurma, Date dataBase,UsuarioVO usuario) throws Exception;

    public Integer contarAulasDesmarcadasPorPeriodo(int contrato, int horarioTurma, Date datainicio, Date datafim) throws Exception ;

    public Integer contarAulasDesmarcadasPorPeriodoSemReposicao(int contrato, int horarioTurma, Date datainicio, Date datafim) throws Exception ;

    public List<AulaDesmarcadaVO> consultarAulasDesmarcadasPassadasSemReposicao(Integer codigoContrato, int nivelMontarDados) throws Exception;

    public boolean existeAulaParaReposicao(ReposicaoVO reposicao) throws Exception;
    
    public List<AulaDesmarcadaVO> consultarListaAulasDesmarcadas(int contrato, int empresa, int nivelMontarDados) throws Exception;
    
    public void alterarContratoAulaEContratoAnterior(final AulaDesmarcadaVO obj) throws Exception;
    
    public Integer contarAulasDesmarcadasContratoPassado(int contrato, int empresa, int turma, int horarioTurma, Date dia,Date datainicio, Date datafim,int contratoAnterior) throws Exception;
    
    public List<AulaDesmarcadaVO> consultarListaAulasDesmarcadasContratoPassado(int contrato, int empresa, int contratoPassado, int nivelMontarDados) throws Exception;
    
    public Map<Date, List<Integer>> consultarAgendamentosDesmarcadosSemValidarData(Integer cliente,Integer contrato)throws Exception;

    public void voltarAulasDermarcadasEstorno(ContratoVO contrato) throws Exception;

    public List<AulaDesmarcadaVO> consultarAulaDesmarcadaPorHorarioTurmaEPeriodo(final Integer codigoHorarioTurma,
                                                                                 final String periodo,
                                                                                 int nivelMontarDados,
                                                                                 Integer codContrato,
                                                                                 String reposicoesId) throws Exception;

    public void atualizarContratoDasAulasDesmarcadas() throws Exception;
}

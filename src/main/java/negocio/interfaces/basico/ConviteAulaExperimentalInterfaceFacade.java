/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package negocio.interfaces.basico;

import java.util.Date;
import java.util.List;
import negocio.comuns.conviteaulaexperimental.ConviteAulaExperimentalBITO;
import negocio.comuns.conviteaulaexperimental.ConviteAulaExperimentalVO;

/**
 *
 * <AUTHOR>
 */
public interface ConviteAulaExperimentalInterfaceFacade extends SuperInterface {
    
    public List<ConviteAulaExperimentalVO> consultarConvitesRecebidos(Integer cliente) throws Exception;
    
    public ConviteAulaExperimentalBITO gerarBI(Date dataBase, Integer empresa) throws Exception;
    
    public List<ConviteAulaExperimentalBITO> obterListaBI(Date dataBase,Integer empresa) throws Exception;
    
    public List<ConviteAulaExperimentalBITO> consultarRelatorio(Date inicio, Date fim, Integer empresa, 
            String clienteConvidou, String usuarioConvidou, <PERSON> convidado, 
            <PERSON><PERSON>an todos, <PERSON><PERSON><PERSON> validou, <PERSON><PERSON><PERSON> age<PERSON>, <PERSON><PERSON><PERSON> compareceu, Integer tipo) throws Exception;
}

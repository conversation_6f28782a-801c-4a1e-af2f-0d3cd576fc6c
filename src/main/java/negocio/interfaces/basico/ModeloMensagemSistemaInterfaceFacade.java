package negocio.interfaces.basico;

import negocio.comuns.basico.ConfiguracaoSistemaVO;
import negocio.comuns.basico.ModeloMensagemSistemaVO;
import negocio.comuns.basico.enumerador.IdentificadorMensagemSistema;
import negocio.facade.jdbc.basico.ModeloMensagemSistema;

/**
 * Created by johny<PERSON> on 13/01/2017.
 */
public interface ModeloMensagemSistemaInterfaceFacade extends SuperInterface{

    /**
     * Realiza a inclusão de uma nova {@link ModeloMensagemSistemaVO}
     * @param obj
     * @throws Exception
     */
    void incluir(ModeloMensagemSistemaVO obj) throws Exception;

    /**
     * Realiza a consulta de uma {@link ModeloMensagemSistemaVO} a partir do atributo identificador.
     * @param identificador
     * @return
     * @throws Exception
     */
    ModeloMensagemSistemaVO consultarPorIdentificador(IdentificadorMensagemSistema identificador) throws  Exception;
}

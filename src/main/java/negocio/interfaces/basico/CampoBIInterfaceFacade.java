/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package negocio.interfaces.basico;

import br.com.pactosolucoes.enumeradores.BIEnum;
import br.com.pactosolucoes.enumeradores.CampoBIEnum;
import java.util.List;
import java.util.Map;
import negocio.comuns.basico.CampoBIVO;

/**
 *
 * <AUTHOR>
 */
public interface CampoBIInterfaceFacade {
    
    
    public List<CampoBIVO> consultarPorBI(BIEnum bi, Integer mes, Integer ano, Integer empresa) throws Exception;
    
    public void deletarPorBI(BIEnum bi, Integer mes, Integer ano, Integer empresa) throws Exception;
    
    public void incluir(CampoBIVO campoBI) throws Exception;
    
    public void incluirPorBI(List<CampoBIVO> campos) throws Exception;
    
    public Map<CampoBIEnum, CampoBIVO> obterMapaPorBI(BIEnum bi, Integer mes, Integer ano, Integer empresa) throws Exception;
}

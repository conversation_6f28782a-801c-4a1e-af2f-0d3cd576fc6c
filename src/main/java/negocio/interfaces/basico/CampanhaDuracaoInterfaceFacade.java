/*
 * To change this template, choose Too<PERSON> | Templates
 * and open the template in the editor.
 */
package negocio.interfaces.basico;

import java.util.Date;
import java.util.List;
import negocio.comuns.CampanhaDuracaoVO;
import negocio.comuns.basico.EmpresaVO;
import negocio.comuns.basico.ItemCampanhaVO;
import negocio.comuns.basico.enumerador.TipoItemCampanhaEnum;
import negocio.comuns.plano.PlanoDuracaoVO;
import negocio.comuns.plano.PlanoVO;
import negocio.comuns.plano.ProdutoVO;
import negocio.comuns.plano.TurmaVO;
import negocio.facade.jdbc.basico.CampanhaDuracao;
import negocio.facade.jdbc.basico.ItemCampanha;
import negocio.facade.jdbc.plano.PlanoDuracao;

/**
 *
 * <AUTHOR>
 */
public interface CampanhaDuracaoInterfaceFacade extends SuperInterface{
    
    public void incluir(CampanhaDuracaoVO obj) throws Exception;
    
    public void alterar(CampanhaDuracaoVO obj) throws Exception;
    
    public void excluir(CampanhaDuracaoVO obj) throws Exception;
    
    public List<CampanhaDuracaoVO> consultarTodos() throws Exception;
    
    public void alterarSemCommit(CampanhaDuracaoVO obj) throws Exception;
    
    CampanhaDuracaoVO consultarPorChavePrimaria(Integer codigo, EmpresaVO empresa) throws Exception;

    Integer multiplicadorPorCampanha(ItemCampanhaVO intemCampanha) throws  Exception;

    CampanhaDuracaoVO campanhaVigenteMultiplicador(Date dataRegistro, TipoItemCampanhaEnum tipoCategoria, Integer empresa) throws Exception;

    List<CampanhaDuracaoVO> consultarTotalizadorCampanhasAtivas(Date dataInicial, Date dataFinal, EmpresaVO empresa)throws  Exception;
}

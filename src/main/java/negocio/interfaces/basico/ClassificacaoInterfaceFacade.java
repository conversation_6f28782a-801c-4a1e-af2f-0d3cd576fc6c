package negocio.interfaces.basico;

import negocio.comuns.basico.ClassificacaoVO;

import java.util.List;

/**
 * Interface reponsável por criar uma estrutura padrão de comunidação entre a camada de controle
 * e camada de negócio (em especial com a classe Façade). Com a utilização desta interface
 * é possível substituir tecnologias de uma camada da aplicação com mínimo de impacto nas demais.
 * Além de padronizar as funcionalidades que devem ser disponibilizadas pela camada de negócio, por intermédio
 * de sua classe Façade (responsável por persistir os dados das classes VO).
 */
public interface ClassificacaoInterfaceFacade extends SuperInterface {

    public ClassificacaoVO novo() throws Exception;

    public void incluir(ClassificacaoVO obj) throws Exception;

    public void alterar(ClassificacaoVO obj) throws Exception;

    public void alterarSemCommit(ClassificacaoVO obj) throws Exception;

    public void excluir(ClassificacaoVO obj) throws Exception;

    public ClassificacaoVO consultarPorChavePrimaria(Integer codigo, int nivelMontarDados) throws Exception;

    public List consultarPorCodigo(Integer valorConsulta, boolean controlarAcesso, int nivelMontarDados) throws Exception;

    public ClassificacaoVO consultarPorCodigoExato(Integer valorConsulta, boolean controlarAcesso, int nivelMontarDados) throws Exception;

    public List consultarPorNome(String valorConsulta, boolean controlarAcesso, int nivelMontarDados) throws Exception;

    public void setIdEntidade(String aIdEntidade);

    public String consultarJSON() throws Exception;

    public List consultarParaImpressao(String filtro, String ordem, String campoOrdenacao, int i) throws Exception;
}
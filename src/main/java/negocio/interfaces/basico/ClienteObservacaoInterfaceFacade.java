package negocio.interfaces.basico;

import negocio.comuns.basico.ClienteObservacaoVO;

import java.util.List;

/**
 * Created with IntelliJ IDEA.
 * User: GlaucoT
 * Date: 03/12/13
 * Time: 16:10
 * To change this template use File | Settings | File Templates.
 */
public interface ClienteObservacaoInterfaceFacade extends SuperInterface {

    public void incluir(ClienteObservacaoVO obj) throws Exception;

    public void alterar(ClienteObservacaoVO obj) throws Exception;

    public void excluir(ClienteObservacaoVO obj) throws Exception;

    public List<ClienteObservacaoVO> consultar(Integer codCliente, int nivelMontarDados) throws Exception;

    public String consultarJSON(String codCliente) throws Exception;
    
    public ClienteObservacaoVO consultarChavePrimaria(Integer codigo,Integer nivelMontarDados)throws Exception;
    
    public Integer consultarQuantidadeListaObservacao(Integer codCliente) throws Exception;
    
    public List<ClienteObservacaoVO> consultarObservacaoPaginado(Integer codCliente, int nivelMontarDados,Integer limit,Integer offset)throws Exception;

}

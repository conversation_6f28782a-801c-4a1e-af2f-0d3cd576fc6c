package negocio.interfaces.basico;

import negocio.comuns.arquitetura.UsuarioVO;
import negocio.comuns.basico.ColaboradorVO;
import negocio.comuns.basico.VinculoVO;
import negocio.comuns.crm.ClienteOrganizadorCarteiraVO;
import negocio.comuns.crm.FiltroCarteiraTO;
import negocio.comuns.crm.GrupoColaboradorParticipanteVO;
import negocio.comuns.crm.GrupoColaboradorVO;
import negocio.comuns.utilitarias.ConsistirException;
import servicos.integracao.treino.dto.SinteticoMsDTO;

import java.util.Date;
import java.util.List;

/**
 * Interface reponsável por criar uma estrutura padrão de comunidação entre a camada de controle
 * e camada de negócio (em especial com a classe Façade). Com a utilização desta interface
 * é possível substituir tecnologias de uma camada da aplicação com mínimo de impacto nas demais.
 * Além de padronizar as funcionalidades que devem ser disponibilizadas pela camada de negócio, por intermédio
 * de sua classe Façade (responsável por persistir os dados das classes VO).
 */
public interface VinculoInterfaceFacade extends SuperInterface {

    public VinculoVO novo() throws Exception;

    public void incluir(VinculoVO obj, String origem, SinteticoMsDTO sinteticoSincronizar) throws Exception;

    public void alterar(VinculoVO obj, String origem, SinteticoMsDTO sinteticoSincronizar) throws Exception;

    public VinculoVO consultarPorChavePrimaria(Integer codigo, int nivelMontarDados) throws Exception;

    public void setIdEntidade(String aIdEntidade);

    /**
     * Operação responsável por alterar no BD os dados de um objeto da classe
     * <code>VinculoVO</code>. Sempre utiliza a chave primária da classe como
     * atributo para localização do registro a ser alterado. Primeiramente
     * valida os dados (<code>validarDados</code>) do objeto. Verifica a conexão
     * com o banco de dados e a permissão do usuário para realizar esta operacão
     * na entidade. Isto, através da operação <code>alterar</code> da
     * superclasse.
     *
     * @param listaGrupoColaborador
     * @throws Exception Caso haja problemas de conexão, restrição de acesso ou
     *                   validação de dados.
     */
    public void organizarCarteira(List<GrupoColaboradorVO> listaGrupoColaborador) throws Exception;

    public void incluirVinculoOrganizadorCarteira(ClienteOrganizadorCarteiraVO obj, String tipoVinculo) throws Exception;

    public void validarDadosVinculoVindoOrganizadorCarteira(ClienteOrganizadorCarteiraVO obj) throws ConsistirException;

    public void excluirVinculoOrganizadorCarteira(GrupoColaboradorParticipanteVO colaborador) throws Exception;

    /**
     * Operação responsável por excluir no BD um objeto da classe
     * <code>CidadeVO</code>. Sempre localiza o registro a ser excluído através
     * da chave primária da entidade. Primeiramente verifica a conexão com o
     * banco de dados e a permissão do usuário para realizar esta operacão na
     * entidade. Isto, através da operação <code>excluir</code> da superclasse.
     *
     * @param obj Objeto da classe <code>CidadeVO</code> que será removido no
     *            banco de dados.
     * @throws Exception Caso haja problemas de conexão ou restrição de acesso.
     */
    public void excluir(VinculoVO obj) throws Exception;

    /**
     * Operação responsável por excluir no BD um objeto da classe
     * <code>VinculoVO</code>. Sempre localiza o registro a ser excluído através
     * da chave primária da entidade. Primeiramente verifica a conexão com o
     * banco de dados e a permissão do usuário para realizar esta operacão na
     * entidade. Isto, através da operação <code>excluir</code> da superclasse.
     *
     * @param vinculo Objeto da classe <code>VinculoVO</code> que será removido no
     *                banco de dados.
     * @throws Exception Caso haja problemas de conexão ou restrição de acesso.
     */
    public void excluirVinculoPorCodigoColaboradorClienteTipoVinculo(int vinculo, int colaborador, int cliente, String tipoVinculo, String origem, UsuarioVO usuarioVO) throws Exception;

    /**
     * Responsável por realizar uma consulta de <code>Vinculo</code> através do
     * valor do atributo <code>situacao</code> da classe
     * <code>Colaborador</code> Faz uso da operação
     * <code>montarDadosConsulta</code> que realiza o trabalho de prerarar o
     * List resultante.
     *
     * @return List Contendo vários objetos da classe <code>VinculoVO</code>
     * resultantes da consulta.
     * @throws Exception Caso haja problemas de conexão ou restrição de acesso.
     */
    public List consultarPorSituacaoColaborador(String valorConsulta, int nivelMontarDados) throws Exception;

    /**
     * Responsável por realizar uma consulta de <code>Vinculo</code> através do
     * valor do atributo <code>situacao</code> da classe
     * <code>Colaborador</code> Faz uso da operação
     * <code>montarDadosConsulta</code> que realiza o trabalho de prerarar o
     * List resultante.
     *
     * @return List Contendo vários objetos da classe <code>VinculoVO</code>
     * resultantes da consulta.
     * @throws Exception Caso haja problemas de conexão ou restrição de acesso.
     */
    public List consultarPorCodigoCliente(Integer cliente, int nivelMontarDados, boolean controlarAcesso) throws Exception;

    /**
     * Responsável por realizar uma consulta de <code>Vinculo</code> através do
     * valor do atributo <code>situacao</code> da classe
     * <code>Colaborador</code> Faz uso da operação
     * <code>montarDadosConsulta</code> que realiza o trabalho de prerarar o
     * List resultante.
     *
     * @return List Contendo vários objetos da classe <code>VinculoVO</code>
     * resultantes da consulta.
     * @throws Exception Caso haja problemas de conexão ou restrição de acesso.
     */
    public List consultarPorCodigoClienteEmpresa(Integer cliente, Integer empresa, int nivelMontarDados) throws Exception;

    /**
     * Responsável por realizar uma consulta de <code>Vinculo</code> através do
     * valor do atributo <code>situacao</code> da classe
     * <code>Colaborador</code> Faz uso da operação
     * <code>montarDadosConsulta</code> que realiza o trabalho de prerarar o
     * List resultante.
     *
     * @return List Contendo vários objetos da classe <code>VinculoVO</code>
     * resultantes da consulta.
     * @throws Exception Caso haja problemas de conexão ou restrição de acesso.
     */
    public List consultarPorSituacaoClienteEmpresa(String valorConsulta, Integer empresa, int nivelMontarDados) throws Exception;

    /**
     * Responsável por realizar uma consulta de <code>Vinculo</code> através do
     * valor do atributo <code>situacao</code> da classe
     * <code>Colaborador</code> Faz uso da operação
     * <code>montarDadosConsulta</code> que realiza o trabalho de prerarar o
     * List resultante.
     *
     * @return List Contendo vários objetos da classe <code>VinculoVO</code>
     * resultantes da consulta.
     * @throws Exception Caso haja problemas de conexão ou restrição de acesso.
     */
    public List consultarPorEmpresa(Integer empresa, int nivelMontarDados) throws Exception;

    /**
     * Responsável por realizar uma consulta de <code>Vinculo</code> através do
     * valor do atributo <code>matricula</code> da classe <code>Cliente</code>
     * Faz uso da operação <code>montarDadosConsulta</code> que realiza o
     * trabalho de prerarar o List resultante.
     *
     * @return List Contendo vários objetos da classe <code>VinculoVO</code>
     * resultantes da consulta.
     * @throws Exception Caso haja problemas de conexão ou restrição de acesso.
     */
    public List consultarPorMatriculaCliente(String valorConsulta, int nivelMontarDados) throws Exception;

    /**
     * Responsável por realizar uma consulta de <code>Vinculo</code> através do
     * valor do atributo <code>Integer codigo</code>. Retorna os objetos com
     * valores iguais ou superiores ao parâmetro fornecido. Faz uso da operação
     * <code>montarDadosConsulta</code> que realiza o trabalho de prerarar o
     * List resultante.
     *
     * @param controlarAcesso Indica se a aplicação deverá verificar se o usuário possui
     *                        permissão para esta consulta ou não.
     * @return List Contendo vários objetos da classe <code>VinculoVO</code>
     * resultantes da consulta.
     * @throws Exception Caso haja problemas de conexão ou restrição de acesso.
     */
    public List consultarPorCodigo(Integer valorConsulta, boolean controlarAcesso, int nivelMontarDados) throws Exception;

    public List consultarPorCodigoGrupoColaboradorParticipante(Integer codigo, boolean controleAcesso, int nivelMontarDados) throws Exception;

    public List consultarPorCodigoGrupoColaboradorParticipanteTipoGrupo(Integer codigo, String tipoGrupo, String tipoVisao, boolean controleAcesso, int nivelMontarDados) throws Exception;

    public List consultarPorCodigoGrupoColaboradorParticipanteTipoGrupoPeriodo(Integer codigo, String tipoGrupo, String tipoVisao, Date data, boolean controleAcesso, int nivelMontarDados) throws Exception;

    public List consultarPorCodigoGrupoColaboradorParticipantePeriodo(Integer codigo, Date data, boolean controleAcesso, Integer empresa, int nivelMontarDados) throws Exception;

    public List consultarPorClienteSemTipoVinculoOrganizadorCarteira(String tipoVinculo, String campoConsultar, String valorConsultar, String situacao, String periodoMaisAcessado, boolean controleAcesso, Integer codEmpresa, Integer codigoColaborador, Date dataLimite, boolean ignoraMeses, int nivelMontarDados) throws Exception;

    /**
     * Operação responsável por excluir todos os objetos da
     * <code>VinculoVO</code> no BD. Faz uso da operação <code>excluir</code>
     * disponível na classe <code>Vinculo</code>.
     *
     * @param cliente campo chave para exclusão dos objetos no BD.
     * @throws Exception Erro de conexão com o BD ou restrição de acesso a esta
     *                   operação.
     */
    public void excluirVinculo(Integer cliente) throws Exception;

    /**
     * Operação responsável por alterar todos os objetos da
     * <code>VinculoVO</code> contidos em um Hashtable no BD. Faz uso da
     * operação <code>excluirVinculo</code> e <code>incluirVinculo</code>
     * disponíveis na classe <code>Vinculo</code>.
     *
     * @param objetos List com os objetos a serem alterados ou incluídos no BD.
     * @throws Exception Erro de conexão com o BD ou restrição de acesso a esta
     *                   operação.
     */
    public void alterarVinculo(Integer cliente, UsuarioVO usuarioVO, List objetos, String origem) throws Exception;

    public void validarHistoricoVinculoSaida(Integer cliente, UsuarioVO usuarioVO, List objetos, String origem) throws Exception;

    /**
     * Operação responsável por incluir objetos da <code>VinculoVO</code> no BD.
     * Garantindo o relacionamento com a entidade principal
     * <code>basico.Cliente</code> através do atributo de vínculo.
     *
     * @param objetos List contendo os objetos a serem gravados no BD da classe.
     * @throws Exception Erro de conexão com o BD ou restrição de acesso a esta
     *                   operação.
     */
    public void incluirVinculo(Integer clientePrm, List objetos, String origem) throws Exception;

    public void incluirVinculo(Integer clientePrm, List objetos, String origem, UsuarioVO usuarioVO) throws Exception;

    public void incluirVinculo(Integer clientePrm, List objetos, String origem, UsuarioVO usuarioVO, boolean controlarAcesso) throws Exception;

    /**
     * Operação responsável por consultar todos os <code>VinculoVO</code>
     * relacionados a um objeto da classe <code>basico.Cliente</code>.
     *
     * @param cliente Atributo de <code>basico.Cliente</code> a ser utilizado para
     *                localizar os objetos da classe <code>VinculoVO</code>.
     * @return List Contendo todos os objetos da classe <code>VinculoVO</code>
     * resultantes da consulta.
     * @throws Exception Erro de conexão com o BD ou restrição de acesso a esta
     *                   operação.
     */
    public List consultarVinculo(Integer cliente, int nivelMontarDados) throws Exception;

    /**
     * Operação responsável por consultar todos os <code>VinculoVO</code>
     * relacionados a um objeto da classe <code>basico.Cliente</code>.
     *
     * @param colaborador Atributo de <code>basico.Cliente</code> a ser utilizado para
     *                    localizar os objetos da classe <code>VinculoVO</code>.
     * @return List Contendo todos os objetos da classe <code>VinculoVO</code>
     * resultantes da consulta.
     * @throws Exception Erro de conexão com o BD ou restrição de acesso a esta
     *                   operação.
     */
    public List consultarVinculoPorCodigoColaborador(Integer colaborador, String tipoVinculo, int nivelMontarDados) throws Exception;

    /**
     * Operação responsável por consultar todos os <code>VinculoVO</code>
     * relacionados a um objeto da classe <code>basico.Cliente</code>.
     *
     * @param cliente Atributo de <code>basico.Cliente</code> a ser utilizado para
     *                localizar os objetos da classe <code>VinculoVO</code>.
     * @return List Contendo todos os objetos da classe <code>VinculoVO</code>
     * resultantes da consulta.
     * @throws Exception Erro de conexão com o BD ou restrição de acesso a esta
     *                   operação.
     */
    public VinculoVO consultarVinculoPorCodigoColaboradorClienteTipoVinculo(Integer colaborador, Integer cliente, String tipoVinculo, int nivelMontarDados) throws Exception;

    public List<VinculoVO> consultarPorClienteTipoVinculo(int cliente, String tipoVinculo, int nivelMontarDados) throws Exception;

    public void excluirVinculoPorCodigoColaboradorClienteTipoVinculo(int vinculo, int colaborador, int cliente, String tipoVinculo, Date dataRegistro, String origem, UsuarioVO usuarioVO) throws Exception;

    public VinculoVO consultarVinculoConsultorAtualCliente(Integer codigoCliente,int nivelMontarDados) throws Exception;

    public void incluir(VinculoVO obj, Date registro, String origem, boolean controlarLog, UsuarioVO usuarioVO, SinteticoMsDTO sinteticoSincronizar) throws Exception;

    public List consultarTodosVinculosCarteiraComLimite(Integer empresa) throws Exception;

    public List consultarPorNomeColaboradorVinculoCarteiraComLimite(Integer empresa, String valorConsulta) throws Exception;

    public int restaurarUltimoVinculoConsultor() throws Exception;

    public List<VinculoVO> consultarTodosVinculosPorTipoVinculo(String tipoVinculo, int nivelMontarDados) throws Exception;

    public List<VinculoVO> consultarPorFiltroCarteiraListaClientes(String clientes, FiltroCarteiraTO filtroCarteira, int nivelMontarDados) throws Exception;

    /**
     * @param quantidadeRegistroConsulta define a quantidade de registros deverão ser tragos na consulta. Caso informado <b>NULL</b>, haverá limite de quantidade.
     */
    List<VinculoVO> consultarPorFiltroCarteiraListaClientes(String clientes, FiltroCarteiraTO filtroCarteira, Integer quantidadeRegistroConsulta, int nivelMontarDados) throws Exception;

    public List<VinculoVO> consultarPorFiltroCarteiraListaClientesComum(String clientes, FiltroCarteiraTO filtroCarteira, Integer quantidadeRegistroConsulta, int nivelMontarDados) throws Exception;

    public void removerVinculos(List<VinculoVO> vinculos, String origem, UsuarioVO usuarioVO,boolean atualizarsintetico) throws Exception;

    public void removerVinculos(List<VinculoVO> vinculos, String origem, UsuarioVO usuarioVO,boolean atualizarsintetico, Date dataRegistro) throws Exception;

    public void incluirVinculos(List<VinculoVO> vinculos, String origem, UsuarioVO usuarioVO) throws Exception;

    public void transferirVinculos(List<VinculoVO> vinculos, ColaboradorVO colaborador, String origem, UsuarioVO usuarioVO, List<Integer> agendasTransferir) throws Exception;

    public void transferirVinculos(List<VinculoVO> vinculos, ColaboradorVO colaborador, String origem, UsuarioVO usuarioVO, List<Integer> agendasTransferir, final String tipoVinculo) throws Exception;

    public void transferirVinculosConsultor(List<VinculoVO> vinculos, ColaboradorVO colaborador, String origem, UsuarioVO usuarioVO, List<Integer> agendasTransferir) throws Exception;

    public void transferirVinculosTreino(List<VinculoVO> vinculos, ColaboradorVO colaborador, String origem, UsuarioVO usuarioVO, List<Integer> agendasTransferir) throws Exception;
    
    public boolean existeVinculoCodigoColaboradorTipoVinculo(Integer colaborador, String tipoVinculo) throws Exception;

    public void excluir(VinculoVO obj, String origem, boolean controlarLog) throws Exception;

    public void removerVinculosSemCommit(List<VinculoVO> vinculos, String origem, UsuarioVO usuarioVO, boolean atualizarsintetico, SinteticoMsDTO sinteticoSincronizar) throws Exception;
    
    public List<Integer> obterNumeroNovosNaCarteira(Integer empresa, Date inicio, Date fim, Integer professor, boolean novos) throws Exception;
    
    public List<VinculoVO> consultarVinculosDiferentesDeConsultorPorCliente(int cliente,int nivelMontarDados) throws Exception;

    public List<VinculoVO> consultarVinculosUsuarioTipoConsultor(Integer codigoUsuario, Integer codigoEmpresa ,int nivelMontarDados) throws Exception;
    public void alterarVinculoConsultor(Integer codigoEmpresa, UsuarioVO usuarioVO, ColaboradorVO novoColaborador)throws Exception;

    public List<String> existeVinculoCodigoColaborador(Integer colaborador) throws Exception;
    
    public VinculoVO consultarVinculoConsultorAtualColaborador(Integer codigoColaborador,int nivelMontarDados) throws Exception;

    public String consultarEmpresasVinculadas(Integer codigoColaborador) throws  Exception;

}

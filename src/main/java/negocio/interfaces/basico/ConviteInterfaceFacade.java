package negocio.interfaces.basico;

import negocio.comuns.arquitetura.UsuarioVO;
import negocio.comuns.basico.ClienteVO;
import negocio.comuns.basico.ConviteVO;
import negocio.comuns.financeiro.FreePassVO;

import java.sql.SQLException;
import java.util.Date;
import java.util.List;

public interface ConviteInterfaceFacade extends SuperInterface {

    void incluir(ConviteVO convite) throws Exception;

    List<ConviteVO> historico(Integer convidou) throws Exception;

    public Integer convitesDireito(Integer contrato) throws Exception;

    public Boolean jaConvidou(Integer cliente) throws Exception;

    public void lancarConvite(UsuarioVO logado, ClienteVO convidado, ClienteVO anfitriao) throws Exception;

    public void validarConvite(Integer cliente) throws Exception;

    public void lancarConviteValidando(UsuarioVO logado, ClienteVO convidado, ClienteVO anfitriao) throws Exception;

    public Integer totalizarMes(Integer convidou) throws Exception;

    public List<ConviteVO> relatorio(Date inicio, Date fim, Integer empresa) throws Exception;

    boolean validarSeConvidadoNoDia(ClienteVO convidado, Date hoje) throws SQLException;

    boolean existeConviteNoMes(ClienteVO convidado, ClienteVO anfitriao, Date hoje) throws SQLException;

    void excluir(FreePassVO freePassVO) throws SQLException;

    boolean faltaLancarAcessoConvidado(ClienteVO convidado) throws SQLException;

    void atualizarFaltaAcessoConvidado(ClienteVO convidado) throws SQLException;

}

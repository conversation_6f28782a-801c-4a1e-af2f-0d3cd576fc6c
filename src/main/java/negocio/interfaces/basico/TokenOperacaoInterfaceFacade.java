package negocio.interfaces.basico;

import negocio.tokenOperacao.TokenOperacaoVO;

import java.util.Date;
import java.util.List;

/**
 * Created with IntelliJ IDEA.
 * User: <PERSON>
 * Date: 14/11/2023
 */

public interface TokenOperacaoInterfaceFacade extends SuperInterface {

    void incluir(TokenOperacaoVO obj) throws Exception;

    TokenOperacaoVO consultarUltimoGeradoPeloUsuario(int usuario) throws Exception;

    TokenOperacaoVO consultarPeloToken(String token) throws Exception;

    void inutilizarToken(String token) throws Exception;

    List<TokenOperacaoVO> consultarPorPeriodo(Date dataInicial, Date dataFinal) throws Exception;

}

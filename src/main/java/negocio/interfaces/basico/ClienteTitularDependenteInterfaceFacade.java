package negocio.interfaces.basico;

import negocio.comuns.basico.ClienteTitularDependenteVO;
import negocio.comuns.basico.ClienteVO;
import negocio.comuns.basico.enumerador.TipoCategoriaClubeEnum;

import java.util.List;

/**
 * date : 28/01/2015 12:02:34
 * autor: U<PERSON><PERSON>
 */
public interface ClienteTitularDependenteInterfaceFacade extends SuperInterface {

    void incluir(ClienteTitularDependenteVO clienteTitularDependenteVO) throws Exception;

    List<ClienteTitularDependenteVO> consultarFamiliaPorCodigoCliente(Integer codigoCliente, int nivelMontarDados) throws Exception;

    ClienteTitularDependenteVO consultarPorCodigoCliente(Integer codigoCliente, int nivelMontarDados) throws Exception;

    ClienteTitularDependenteVO consultarPorCodigoPessoa(Integer codigoPessoa, int nivelMontarDados) throws Exception;

    ClienteTitularDependenteVO consultarPorCodigo(Integer codigo, int nivelMontarDados) throws Exception;

    ClienteTitularDependenteVO consultarPorCPF(Integer clienteTitular, String cpf, TipoCategoriaClubeEnum tipoCategoriaClube, int nivelMontarDados) throws Exception;

    ClienteVO consultarClienteTitular(ClienteVO clienteDependenteAssemelhado, int nivelMontarDados) throws Exception;

    void excluir(ClienteVO clienteVO) throws Exception;

    boolean clienteEhTitular(Integer codigoCliente) throws Exception;

    boolean pessoaEhTitular(Integer codigoPessoa) throws Exception;

    void excluirFamilia(ClienteVO clienteTitular) throws Exception;

    void alterarPagaSeparadoDependenteAssemelhado(ClienteTitularDependenteVO clienteTitularDependenteVO) throws Exception;



}

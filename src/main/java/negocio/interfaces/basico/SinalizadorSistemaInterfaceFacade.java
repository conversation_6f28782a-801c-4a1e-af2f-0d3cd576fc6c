/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package negocio.interfaces.basico;

import br.com.pactosolucoes.enumeradores.SinalizadorEnum;

/**
 *
 * <AUTHOR>
 */
public interface SinalizadorSistemaInterfaceFacade extends SuperInterface {
    public boolean consultarPorSinalizador(SinalizadorEnum sin) throws Exception;
    
    public void deletarPorBI(SinalizadorEnum sin) throws Exception;

    public void incluir(SinalizadorEnum sin, boolean value) throws Exception;
    
}

package negocio.interfaces.basico;

import negocio.comuns.arquitetura.UsuarioVO;
import negocio.comuns.basico.ClienteVO;
import negocio.comuns.basico.ContratoDependenteVO;
import negocio.comuns.contrato.AfastamentoContratoDependenteVO;
import negocio.comuns.contrato.ContratoVO;

import java.util.Date;
import java.util.List;
import java.util.Optional;

public interface ContratoDependenteInterfaceFacade extends SuperInterface {

    void incluir(ContratoVO obj) throws Exception;

    void alterar(ContratoDependenteVO obj) throws Exception;

    void finalizarDependencia(ContratoDependenteVO obj) throws Exception;

    void finalizarDependenciaTitularCancelado(ContratoDependenteVO obj) throws Exception;

    boolean existeDependenciaVigenteNoPeriodo(Integer codCliente, Date dataInicio, Date dataFinal) throws Exception;

    int quantidadeContratoDependente(Integer contrato, boolean somenteDisponiveis) throws Exception;

    Optional<ContratoDependenteVO> findBy<PERSON>odigo(Integer dependente) throws Exception;

    Optional<ContratoDependenteVO> findByDependente(Integer dependente) throws Exception;

    List<ContratoDependenteVO> findAllByContratoOrderByPosicaoDependente(Integer contrato) throws Exception;

    List<ContratoDependenteVO> findAllByContrato(Integer contrato) throws Exception;

    Optional<ContratoDependenteVO>  consultarProximoContratoDependenteDisponivelPorContrato(Integer codigoContrato) throws Exception;

    List<ContratoDependenteVO> findAllByCliente(ClienteVO clienteVO, Integer limit) throws Exception;

    List<ContratoDependenteVO> findAllVigentByCliente(Integer codCliente, Date data) throws Exception;

    void incluirAfastamento(AfastamentoContratoDependenteVO afastamentoVO) throws Exception;

    void excluirAfastamento(AfastamentoContratoDependenteVO afastamentoVO, UsuarioVO responsavelOperacao) throws Exception;

    List<ContratoDependenteVO> findAllByContratoOrderByDataFinal(ClienteVO clienteVO);

    void alterarVigenciaFinalContratoDependente(Integer contrato, Date dataFinal) throws Exception;

    void removerDependenteContrato(Integer codigo) throws Exception;
}

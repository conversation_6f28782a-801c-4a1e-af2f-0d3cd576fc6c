package negocio.interfaces.basico;

import negocio.comuns.arquitetura.UsuarioVO;
import negocio.comuns.basico.ColaboradorDocumentoRhVO;
import negocio.comuns.basico.ColaboradorVO;

import java.util.List;

/**
 * Created by ulisses on 14/11/2022.
 */
public interface ColaboradorDocumentoRhInterface extends SuperInterface {

    void incluir(ColaboradorDocumentoRhVO colaboradorDocumentoRhVO)throws Exception;
    List<ColaboradorDocumentoRhVO> consultar(ColaboradorVO colaboradorVO)throws Exception;
    void excluir(Integer codigo)throws Exception;
    Integer pesquisarIdentificarAnexo()throws Exception;

}

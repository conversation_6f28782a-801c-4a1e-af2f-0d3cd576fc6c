package negocio.interfaces.basico;

import negocio.comuns.basico.ColaboradorIndisponivelCrmVO;

import java.util.Date;
import java.util.List;

public interface ColaboradorIndisponivelCrmInterfaceFacade extends SuperInterface {

    public void validarDados(ColaboradorIndisponivelCrmVO obj) throws Exception;

    public void incluir(final ColaboradorIndisponivelCrmVO obj) throws Exception;

    public void incluirSemCommit(final ColaboradorIndisponivelCrmVO obj) throws Exception;

    public void alterar(final ColaboradorIndisponivelCrmVO obj) throws Exception;

    public void excluirSemCommit(final ColaboradorIndisponivelCrmVO obj) throws Exception;

    public void excluir(final ColaboradorIndisponivelCrmVO obj) throws Exception;

    public List<ColaboradorIndisponivelCrmVO> consultarPorColaboradorSuplente(int colaboradorSuplente, int empresa) throws Exception;

    public void incluirColaboradorIndisponivelCrm(List<ColaboradorIndisponivelCrmVO> lista) throws Exception;

    public void incluirColaboradorIndisponivelCrmSemCommit(List<ColaboradorIndisponivelCrmVO> lista) throws Exception;

    public List<ColaboradorIndisponivelCrmVO> consultarPorColaboradorIndispOuSupl(int colaborador, int empresa) throws Exception ;

    public List<ColaboradorIndisponivelCrmVO> consultarPorColaborador(int colaborador, int empresa) throws Exception;

    public List<ColaboradorIndisponivelCrmVO> consultarPorEmpresa(int empresa) throws Exception;

    public List<ColaboradorIndisponivelCrmVO> consultarPorColaboradorSuplentePorPeriodo(int colaboradorSuplente, int empresa, Date data) throws Exception;


    }
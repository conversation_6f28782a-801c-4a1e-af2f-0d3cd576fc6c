
package negocio.interfaces.basico;

import java.util.List;
import negocio.comuns.basico.TabelaParceiroFidelidadeItemVO;



/**
 *
 * <AUTHOR>
 */
public interface TabelaParceiroFidelidadeItemInterfaceFacade extends SuperInterface {

    TabelaParceiroFidelidadeItemVO novo() throws Exception;

    void incluir(TabelaParceiroFidelidadeItemVO obj) throws Exception;

    void alterar(TabelaParceiroFidelidadeItemVO obj) throws Exception;

    void excluir(TabelaParceiroFidelidadeItemVO obj) throws Exception;

    TabelaParceiroFidelidadeItemVO consultarPorChavePrimaria(Integer codigo, int nivelMontarDados) throws Exception;    

    List consultarPorTabelaParceiro(final Integer codigoParceiro, boolean controlarAcesso, int nivelMontarDados) throws Exception;

    void setIdEntidade(String aIdEntidade);

    TabelaParceiroFidelidadeItemVO consultarPorTabelaParceiroFidelidadeValor(Integer tabelaParceiroFidelidade, Double valorBase, int nivelMontarDados) throws Exception;

    Integer calcularValorPontos(Integer tabelaParceiroFidelidade, Double valorBase) throws Exception;
}
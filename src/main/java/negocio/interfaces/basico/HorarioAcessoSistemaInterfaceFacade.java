/*
 * To change this template, choose Too<PERSON> | Templates
 * and open the template in the editor.
 */
package negocio.interfaces.basico;

import java.util.List;
import negocio.comuns.arquitetura.HorarioAcessoSistemaVO;

/**
 *
 * <AUTHOR>
 */
public interface HorarioAcessoSistemaInterfaceFacade extends SuperInterface {

    public void incluir(HorarioAcessoSistemaVO obj) throws Exception;

    public List<HorarioAcessoSistemaVO> consultarPorDiaSemana(String diaSemana,
            Integer perfilAcesso, int nivelMontarDados) throws Exception;

    public void alterar(HorarioAcessoSistemaVO obj) throws Exception;

    public HorarioAcessoSistemaVO consultarVerificarConflitoHorarios(
            HorarioAcessoSistemaVO horarioAcessoSistemaVO, int nivelMontarDados) throws Exception;

    public List<HorarioAcessoSistemaVO> consultarHorarioAtualDentroIntervalosPermitidos(Integer usuario, String horaAtual, String diaSemana, int nivelMontarDados) throws Exception;

    public boolean consultarSeExisteHorarioAcessoSistema() throws Exception;

    public Integer consultarUltimoCodigo() throws Exception;

    public void incluirHorarioAcessoSistemaUsuario(Integer usuarioprm, List objetos) throws Exception;

    public void excluirUsuarioHorarioAcessoSistema(Integer usuario) throws Exception;

    public void excluir(Integer codigo) throws Exception;

    public void alterarHorarioAcessoSistemaUsuario(Integer usuario, List objetos) throws Exception;

    public List<HorarioAcessoSistemaVO> consultar(Integer usuario, int nivelMontarDados) throws Exception;
}

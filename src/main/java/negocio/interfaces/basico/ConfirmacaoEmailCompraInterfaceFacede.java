package negocio.interfaces.basico;

import negocio.comuns.basico.ClienteVO;
import negocio.comuns.basico.ConfirmacaoEmailCompraVO;

public interface ConfirmacaoEmailCompraInterfaceFacede extends SuperInterface {

     void incluirCadastroEmailConfirmacaoCompra(ClienteVO cliente, String token, int nrParcelasAdesao, String numeroCupomDesconto, int nrParcelasPagamento, int diaVencimento, int plano) throws Exception;

     ConfirmacaoEmailCompraVO consultarCadastroEmailConfirmacaoCompra(String token) throws Exception;



}

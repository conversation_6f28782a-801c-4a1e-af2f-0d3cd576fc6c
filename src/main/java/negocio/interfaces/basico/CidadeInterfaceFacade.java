package negocio.interfaces.basico;

import negocio.comuns.basico.CidadeVO;

import java.sql.ResultSet;
import java.util.List;

/**
 * Interface reponsável por criar uma estrutura padrão de comunidação entre a camada de controle
 * e camada de negócio (em especial com a classe Façade). Com a utilização desta interface
 * é possível substituir tecnologias de uma camada da aplicação com mínimo de impacto nas demais.
 * Além de padronizar as funcionalidades que devem ser disponibilizadas pela camada de negócio, por intermédio
 * de sua classe Façade (responsável por persistir os dados das classes VO).
 */
public interface CidadeInterfaceFacade extends SuperInterface {


    public CidadeVO novo() throws Exception;

    public void incluir(CidadeVO obj) throws Exception;

    public void alterar(CidadeVO obj) throws Exception;

    public void excluir(CidadeVO obj) throws Exception;

    public CidadeVO consultarPorChavePrimaria(Integer codigo, int nivelMontarDados) throws Exception;

    public List consultarPorCodigo(Integer valorConsulta, boolean controlarAcesso, int nivelMontarDados) throws Exception;

    public CidadeVO consultarPorCodigoExato(Integer valorConsulta, boolean controlarAcesso, int nivelMontarDados) throws Exception;

    public List consultarPorNome(String valorConsulta, boolean controlarAcesso, int nivelMontarDados) throws Exception;

    public List consultarPorCodigoPais(Integer valorConsulta, int nivelMontarDados) throws Exception;

    public List consultarPorCodigoEstado(Integer valorConsulta, int nivelMontarDados) throws Exception;

    public List consultarPorNomePais(String valorConsulta, int nivelMontarDados) throws Exception;

    public List consultarPorEstado(String valorConsulta, boolean controlarAcesso, int nivelMontarDados) throws Exception;

    public CidadeVO consultarPorNome(String valorConsulta, int nivelMontarDados) throws Exception;

    public CidadeVO consultaPorNomeEstadoCriaSeNaoExiste(String cidade, String uf) ;

    public void setIdEntidade(String aIdEntidade);

    public boolean consultarPorNomeEstadoPais(String cidade, int estado, int pais, int nivelMontarDados) throws Exception;

    public ResultSet consultar() throws Exception;

    public void incluir(CidadeVO obj, boolean centralEventos) throws Exception;

    public void alterar(CidadeVO obj, boolean centralEventos) throws Exception;

    public void excluir(CidadeVO obj, boolean centralEventos) throws Exception;

    public String consultarJSON() throws Exception;

    public List consultarParaImpressao(String filtro, String ordem, String campoOrdenacao, int i) throws Exception;

    CidadeVO consultarPorNomeCidadeSiglaEstado(String cidade, String siglaEstado) throws Exception;

    void preencherCodigosIBGECidade();

    List<CidadeVO> consultarCidadesHomologadas(int nivelMontarDados) throws Exception;
}
/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package negocio.interfaces.basico;

import java.sql.Connection;

/**
 *
 * <AUTHOR>
 */
public interface SuperInterface {

    public Integer obterValorChavePrimariaCodigo() throws Exception;
    public String getIdEntidade();
    public void prepararConexao() throws Exception;
    public Connection getCon();
    public void setCon(Connection c);
}

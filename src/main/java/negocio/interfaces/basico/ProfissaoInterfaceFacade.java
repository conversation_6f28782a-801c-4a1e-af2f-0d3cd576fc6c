package negocio.interfaces.basico;

import negocio.comuns.basico.ProfissaoVO;

import java.util.List;

/**
 * Interface reponsável por criar uma estrutura padrão de comunidação entre a camada de controle
 * e camada de negócio (em especial com a classe Façade). Com a utilização desta interface
 * é possível substituir tecnologias de uma camada da aplicação com mínimo de impacto nas demais.
 * Além de padronizar as funcionalidades que devem ser disponibilizadas pela camada de negócio, por intermédio
 * de sua classe Façade (responsável por persistir os dados das classes VO).
 */
public interface ProfissaoInterfaceFacade extends SuperInterface {


    public ProfissaoVO novo() throws Exception;

    public void incluir(ProfissaoVO obj) throws Exception;

    public void alterar(ProfissaoVO obj) throws Exception;

    public void excluir(ProfissaoVO obj) throws Exception;

    public ProfissaoVO consultarPorChavePrimaria(Integer codigo, int nivelMontarDados) throws Exception;

    public List consultarPorCodigo(Integer valorConsulta, boolean controlarAcesso, int nivelMontarDados) throws Exception;

    public ProfissaoVO consultarPorCodigoExato(Integer valorConsulta, boolean controlarAcesso, int nivelMontarDados) throws Exception;

    public List consultarPorDescricao(String valorConsulta, boolean controlarAcesso, int nivelMontarDados) throws Exception;

    public void setIdEntidade(String aIdEntidade);

    public void incluir(ProfissaoVO obj, boolean centralEventos) throws Exception;

    public void alterar(ProfissaoVO obj, boolean centralEventos) throws Exception;

    public void excluir(ProfissaoVO obj, boolean centralEventos) throws Exception;

    public int consultarCodigoPorDescricao(String valorConsulta) throws Exception;

    public String consultarJSON() throws Exception;

    public List consultarParaImpressao(String filtro, String ordem, String campoOrdenacao, int i) throws Exception;

}
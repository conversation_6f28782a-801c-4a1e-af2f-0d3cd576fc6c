/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package negocio.interfaces.basico;

import br.com.pactosolucoes.agendatotal.json.AgendaTotalJSON;
import br.com.pactosolucoes.agendatotal.json.AgendadoJSON;
import negocio.comuns.arquitetura.UsuarioVO;
import negocio.comuns.basico.PresencaVO;
import negocio.comuns.basico.ReposicaoVO;
import negocio.comuns.contrato.ContratoModalidadeVO;
import negocio.comuns.plano.HorarioTurmaVO;
import negocio.facade.jdbc.plano.HorarioTurma;

import java.util.Date;
import java.util.List;
import java.util.Set;

import negocio.comuns.contrato.ContratoVO;

/**
 *
 * <AUTHOR>
 */
public interface ReposicaoInterfaceFacade extends SuperInterface {

    ReposicaoVO consultarPorChavePrimaria(final int codigo, int nivelMontarDados) throws Exception;
    ReposicaoVO consultarPorCodigo(final int codigo, int nivelMontarDados) throws Exception;

    /**
     * Método inclui uma reposição esperando que esteja dentro de um bloco transacional, omitindo os métodos de 'commit' e 'rollback'
     * @param obj
     * @throws Exception
     */
    void incluir(ReposicaoVO obj, ReposicaoVO reposicaoOrigem) throws Exception;
    void incluirSemCommit(ReposicaoVO obj, ReposicaoVO reposicaoOrigem) throws Exception;

    List<ReposicaoVO> consulta(final String sql) throws Exception;

    void atualizarPresenca(int codigoReposicao, final Date dataPresenca) throws Exception;

    PresencaVO preencherPresencaEmFuncaoReposicao(int codigoPessoa, int codigoHorarioTurma, Date dataInicial, Date dataFinal) throws Exception;

    public int contarReposicoes(String contrato, String turma, String horarioturma, boolean pesquisarPorHorario, Date periodoInicial, Date periodoFinal, boolean presencas, boolean pesqHorarioOrigem) throws Exception;

    public int contarReposicoesSolicitadas(String contratos, String turma, String horarioTurma, boolean pesquisarPorHorario, Date dataLancamentoInicial, Date dataLancamentoFinal) throws Exception;

    public List<ReposicaoVO> consultarReposicoes(String contrato, String turma, String horarioturma, boolean pesquisarPorHorario, Date periodoInicial, Date periodoFinal, boolean presencas) throws Exception;

    public List<ReposicaoVO> consultarReposicoesSolicitadas(String contratos, String turma, String horarioTurma, boolean pesquisarPorHorario, Date dataLancamentoInicial, Date dataLancamentoFinal) throws Exception;

    public List<ReposicaoVO> consultarReposicoesPorHorarioTurmaOrigem(HorarioTurmaVO obj, final String periodo, int nivelMontarDados, Integer codContrato) throws Exception;

    public List<ReposicaoVO> consultarReposicoesPorHorarioTurma(final Integer codigoHorarioTurma, boolean alunosQueSairam, final String periodo, int nivelMontarDados, Integer codContrato) throws Exception;

    public void nrAlunosReposicao(HorarioTurmaVO obj, final String periodo) throws Exception;

    public void excluir(ReposicaoVO obj) throws Exception;

    public int contarReposicoesDoAluno(final int cliente, final HorarioTurmaVO obj,
            final String periodo, boolean presentes) throws Exception;

    public int contarDesmarcacoesDoAluno(final int cliente, final HorarioTurmaVO obj,
            final String periodo) throws Exception;

    public boolean existeReposicaoAlunoNaqueleDia(final int pessoa, final HorarioTurmaVO ht,
            final Date dia) throws Exception;

    public boolean existeDesmarcacaoAlunoNaqueleDia(final int pessoa, final HorarioTurmaVO ht,
            final Date dia) throws Exception;

    public boolean existemReposicoesPorHorarioTurmaOrigem(HorarioTurmaVO obj, final String periodo, Integer codContrato) throws Exception;

    boolean existemReposicoesParaHorarioTurma(HorarioTurmaVO obj, boolean verificarReposicoesFuturas) throws Exception;

    public Integer existemReposicoesPorContratoDataReposicaoPegarHorarioTurmaOrigem(final String periodo, final Integer codContrato) throws Exception;

    public ReposicaoVO consultarReposicao(int contrato, int horarioTurmaOrigem, int turmaOrigem, Date dataOrigem, int nivelMontarDados) throws Exception ;

    public void verificarReposicaoJaLancada(int contrato, int horarioTurma, int turma, Date data) throws Exception;

    public ReposicaoVO consultar(HorarioTurmaVO horarioTurmaOrigem, Date dataOrigem, Integer codigoContrato, int nivelMontarDados)throws Exception;
    public List<ReposicaoVO> consultar(Date dataReposicao, Integer codigoContrato, Integer codigoCliente, int nivelMontarDados)throws Exception;
    public List<ReposicaoVO> consultarReposicoesApartirDeUmaDataBase(Date dataBase, Integer codigoContrato, int nivelMontarDados)throws Exception;
    public List<ReposicaoVO> consultarReposicaoContratoCreditoTreino(Date dataBase)throws Exception;
    
    public List<AgendadoJSON> consultarReposicoesParaAgenda(Date inicio, Date fim, Integer empresa, Integer matricula, boolean usarDataOrigem, Integer modalidade, Integer tipomodalidade) throws Exception;
    
    public ReposicaoVO consultarReposicaoJaLancada(int codigoCliente, int horarioTurma, int turma, Date dataOrigem, Date dataReposicao) throws Exception;
    
    public void excluirSemValidarPermissao(ReposicaoVO obj) throws Exception;

    public List<ReposicaoVO>consultarAulaMarcadaComCreditoExtra(Integer codigoContrato, Date dataBase, int nivelMontarDados)throws Exception;
    
    public List<AgendaTotalJSON> consultarProximasReposicoes(Integer matricula, Date agora, boolean somenteMesmoDia, Integer modalidade, Integer tipoModalidade)throws Exception;
            
    public void excluirReposicaoDaAulaExperimental(Integer codigoHorarioTurma, Integer codigoConvite, Date dataAula) throws Exception;

    public Integer consultarTotalReposicao(Integer codigoHorarioTurma, Date dataReposicao, Set<Integer> codClientes)throws Exception;
    
    public void excluirReposicoesFuturasContratoHorarioTurma(Integer contrato,Integer horarioTurma, Date agora) throws Exception;
    public void excluirReposicoesFuturasContrato(Integer contrato, Date agora, ContratoVO contratoNovo) throws Exception;
    public ReposicaoVO consultaReposicaoClienteDiaTurma(Integer cliente,Integer horarioTurma, Date data ,int nivelMontarDados) throws Exception;

    void validarPermissaoExcluir(final ReposicaoVO obj) throws Exception;

    public StringBuilder validarConflitoComReposicoes(ContratoVO contratoVO, List<Integer> codigosHorarios) throws Exception;

    Boolean existeReposicaoTurma(Integer codigoTurma) throws Exception;

    void excluirAulasExperimentaisFuturas(Integer pessoa , Integer horarioTurma, Date dataInicio, UsuarioVO usuarioVO) throws Exception;
    Boolean existeAulaExperimentalNoDiaParaAlunoHorarioTurma(Integer pessoa , Integer horarioTurma, Date dataPesquisa) throws Exception;

    boolean existeAulaExperimentalNoDiaParaAlunoHorariosDoContrato(Integer codigo, List<ContratoModalidadeVO> contratoModalidadeVOs, Date vigenciaDe) throws Exception;

    void excluirSemValidarPermissao(ReposicaoVO reposicao, String descreverParcelasVencidasContratoDesmarcacao) throws Exception;

    Boolean removerMarcacoesFuturasParcelasVencidas(String chave);

    String enviarEmail(final ReposicaoVO repo, boolean exclusao) throws Exception;

    String enviarSMS(final ReposicaoVO repo, boolean exclusao) throws Exception;
}

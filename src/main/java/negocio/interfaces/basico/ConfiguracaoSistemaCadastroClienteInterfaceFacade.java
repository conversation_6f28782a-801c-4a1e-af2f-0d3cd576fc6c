package negocio.interfaces.basico;
import negocio.comuns.basico.ConfiguracaoSistemaCadastroClienteVO;
import negocio.comuns.basico.ConfiguracaoSistemaVO;

import java.util.List;

/**
 * Interface reponsável por criar uma estrutura padrão de comunidação entre a camada de controle
 * e camada de negócio (em especial com a classe Façade). Com a utilização desta interface
 * é possível substituir tecnologias de uma camada da aplicação com mínimo de impacto nas demais.
 * Além de padronizar as funcionalidades que devem ser disponibilizadas pela camada de negócio, por intermédio
 * de sua classe Façade (responsável por persistir os dados das classes VO).
*/
public interface ConfiguracaoSistemaCadastroClienteInterfaceFacade extends SuperInterface {

    public void alterar(ConfiguracaoSistemaVO obj) throws Exception;
    public void alterarConfiguracaoClienteSemCommit(ConfiguracaoSistemaVO obj) throws Exception;
    List<ConfiguracaoSistemaCadastroClienteVO> consultar(Boolean visitante) throws Exception;
    public List consultarTodos(int nivelMontarDados) throws Exception;
    public void setIdEntidade(String aIdEntidade);
    ConfiguracaoSistemaCadastroClienteVO consultarConfiguracaoCampoNome(String nome, boolean visitante) throws Exception;
    public void alterar(ConfiguracaoSistemaCadastroClienteVO obj) throws Exception;
}

/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package negocio.interfaces.basico;

import java.util.Date;
import java.util.List;

import negocio.comuns.CampanhaDuracaoVO;
import negocio.comuns.basico.EmpresaVO;
import negocio.comuns.basico.ItemCampanhaVO;
import negocio.facade.jdbc.basico.ItemCampanha;
import relatorio.negocio.comuns.basico.ListaPaginadaTO;

/**
 *
 * <AUTHOR>
 */
public interface ItemCampanhaInterfaceFacade extends SuperInterface{
    
    public void incluir(ItemCampanhaVO obj) throws Exception;
    
    public void alterar(ItemCampanhaVO obj) throws Exception;
    
    public void excluir(boolean permissao,ItemCampanhaVO obj) throws Exception;

    void excluirSemCommit(ItemCampanhaVO obj) throws Exception;

    public List<ItemCampanhaVO> listaItemCampanha(CampanhaDuracaoVO campanha, Integer nivelMontarDados)throws Exception;
    
    public void alterarSemCommit(ItemCampanhaVO obj) throws Exception;
    
    public List<ItemCampanhaVO> listaItemSemCampanha() throws Exception;
    
    public boolean validarItemJaCadastradoChaveEstrangeira(Integer codigo, Integer tipoitem, Integer empresa)throws Exception;

    boolean validarItemJaCadastradoCampanha(ItemCampanhaVO campanha)throws Exception;
    
    public ItemCampanhaVO consultarPorCampanhaChaveEstrangeira(Integer campanha, Integer chaveEstrangeria, int tipoitem, int nivelMontarDados) throws Exception;

    public Integer consultarPorCampanhaVigenteChaveEstrangeira(Integer tipoitem, Integer empresa) throws Exception;

    public ItemCampanhaVO consultarPorChaveEstrangeira(Integer empresa, Integer chaveEstrangeira, Integer tipoitem)throws Exception;

    ItemCampanhaVO consultarUnicosTipoXEmpresa(ItemCampanhaVO tipoItemCampanha)throws Exception;

    List<ItemCampanhaVO> listarItensPorTipo(Integer tipoItemCampanha, Integer empresa)throws Exception;

    List<ItemCampanhaVO> listaItemCampanhaVigentePontuadoPorTipoPaginada(Integer empresa, Integer tipoItemCampanha, Integer nivelMontarDados, ListaPaginadaTO listaPaginadaTO)throws Exception;

    void alterarPontos(Integer codigo, Integer pontos, Integer tipoitem) throws Exception;

    Boolean existeItemCampanha(Integer chaveEstrangeira, int tipoitem) throws Exception;

    void alterarItemCampanhaIndicacao(List<ItemCampanhaVO> itensCampanhaVO) throws Exception;

    List<ItemCampanhaVO> consultaTotalizadorBiPontoOrderDesc(Date dataInicial, Date dataFinal, EmpresaVO empresa) throws Exception;

    void alterarOuIncluir(ItemCampanhaVO itemCampanhaPlano)throws Exception;

    List<Integer> itensRemoverIndicaoEConvertida(EmpresaVO empresaSelecionada)throws Exception;

    List<ItemCampanhaVO> listaItemCampanhaVigentePontuadoPlano(Integer empresa, Integer tipoItemCampanha, int nivelmontardadosTodos, ListaPaginadaTO paginadorListaPlanosCP)throws Exception;
}

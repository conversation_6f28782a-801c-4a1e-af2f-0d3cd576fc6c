/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package negocio.interfaces.basico;

import java.util.List;
import negocio.comuns.financeiro.DFSinteticoDetalheVO;

/**
 *
 * <AUTHOR>
 */
public interface DFSinteticoDetalheInterfaceFacade extends SuperInterface{
    
    public void incluir(DFSinteticoDetalheVO detalhe) throws Exception;
    
    public void incluir(List<DFSinteticoDetalheVO> mapaDetalhes) throws Exception;
   
    public List<DFSinteticoDetalheVO> consultar(String mesAno, Integer nrMesesAnteriores, Integer empresa) throws Exception;
}

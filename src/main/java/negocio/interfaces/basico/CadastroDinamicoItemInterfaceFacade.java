package negocio.interfaces.basico;

import negocio.comuns.CadastroDinamicoItemVO;
import negocio.comuns.basico.CadastroDinamicoVO;
import negocio.comuns.basico.enumerador.negocio.comuns.basico.enumerador.cadastrodinamico.CadastroDinamicoEnumInterface;

import java.util.List;

/**
 * Created by ulisses on 14/08/2015.
 */
public interface CadastroDinamicoItemInterfaceFacade  extends  SuperInterface {

    void incluirSemCommit(Integer codigoCadastroDinamico, String nomeCampo, String labelCampo) throws Exception;

    void incluirSemCommit(CadastroDinamicoVO cadastroDinamicoVO, List<CadastroDinamicoEnumInterface> listaCadastroDinamicoEnumInterface) throws Exception;

    void excluir(List<CadastroDinamicoItemVO> listaCadastroDinamicoItem) throws Exception;

    void alterar(List<CadastroDinamicoItemVO> listaCadastroDinamicoItem) throws Exception;

    void excluirSemCommit(CadastroDinamicoItemVO cadastroDinamicoItemVO) throws Exception;

    List<CadastroDinamicoItemVO> consultar(String nomeTabela, int nivelMontarDados) throws Exception;

    List<String> consultarCamposMostrar(String nomeTabela, int nivelMontarDados) throws Exception;

    List<String> consultarCamposObrigatorio(String nomeTabela, int nivelMontarDados) throws Exception;

    CadastroDinamicoItemVO consultarPorCodigo(Integer codigo,  int nivelMontarDados) throws Exception;

    CadastroDinamicoItemVO consultarPorNome(Integer codigoCadastroDinamico, String nome,  int nivelMontarDados) throws Exception;
}

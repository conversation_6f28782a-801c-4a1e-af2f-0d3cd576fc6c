package negocio.interfaces.basico;

import java.util.List;
import negocio.comuns.basico.TabelaParceiroFidelidadeVO;

/**
 *
 * <AUTHOR>
 */
public interface TabelaParceiroFidelidadeInterfaceFacade extends SuperInterface {
    
    void setIdEntidade(String aIdEntidade);

    TabelaParceiroFidelidadeVO novo() throws Exception;

    void incluir(TabelaParceiroFidelidadeVO obj) throws Exception;

    void alterar(TabelaParceiroFidelidadeVO obj) throws Exception;

    void excluir(TabelaParceiroFidelidadeVO obj) throws Exception;

    TabelaParceiroFidelidadeVO consultarPorChavePrimaria(Integer codigo, int nivelMontarDados) throws Exception;

    List consultarPorCodigo(Integer valorConsulta, boolean controlarAcesso, int nivelMontarDados) throws Exception;

    List consultarPorNome(String valorConsulta, boolean controlarAcesso, int nivelMontarDados) throws Exception;
    
    TabelaParceiroFidelidadeVO obterDefaultRecorrencia(final Integer empresa) throws Exception;
    
    List consultarPorParceiro(final Integer codigoParceiro, boolean controlarAcesso, int nivelMontarDados) throws Exception;

}
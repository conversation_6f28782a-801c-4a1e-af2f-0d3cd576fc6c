package negocio.interfaces.basico;

import br.com.pactosolucoes.estrutura.paginacao.ConfPaginacao;
import negocio.comuns.arquitetura.UsuarioVO;
import negocio.comuns.basico.*;
import negocio.comuns.financeiro.ReciboDevolucaoVO;
import negocio.comuns.financeiro.VendaAvulsaVO;
import negocio.comuns.financeiro.enumerador.TipoOperacaoContaCorrenteEnum;

import java.sql.ResultSet;
import java.util.Date;
import java.util.List;
import negocio.comuns.contrato.ContratoVO;
import negocio.comuns.financeiro.MovPagamentoVO;

/**
 * Interface reponsável por criar uma estrutura padrão de comunidação entre a camada de controle
 * e camada de negócio (em especial com a classe Façade). Com a utilização desta interface
 * é possível substituir tecnologias de uma camada da aplicação com mínimo de impacto nas demais.
 * Além de padronizar as funcionalidades que devem ser disponibilizadas pela camada de negócio, por intermédio
 * de sua classe Façade (responsável por persistir os dados das classes VO).
 */
public interface MovimentoContaCorrenteClienteInterfaceFacade extends SuperInterface {

    public MovimentoContaCorrenteClienteVO novo() throws Exception;

    public void incluir(MovimentoContaCorrenteClienteVO obj) throws Exception;

    public void alterar(MovimentoContaCorrenteClienteVO obj) throws Exception;

    public void alterarSemCommit(MovimentoContaCorrenteClienteVO obj, boolean centralEventos) throws Exception;

    public void excluir(MovimentoContaCorrenteClienteVO obj) throws Exception;

    public MovimentoContaCorrenteClienteVO consultarPorChavePrimaria(Integer codigo, int nivelMontarDados) throws Exception;

    public List consultarPorCodigo(Integer valorConsulta, boolean controlarAcesso, int nivelMontarDados) throws Exception;

    public List consultarPorMatriculaCliente(String valorConsulta, int nivelMontarDados) throws Exception;

    public List consultarPorNomeCliente(String valorConsulta, int nivelMontarDados) throws Exception;

    public List consultarPorSaldoAnteior(Double valorConsulta, boolean controlarAcesso, int nivelMontarDados) throws Exception;

    public List consultarPorSaldoAtual(Double valorConsulta, boolean controlarAcesso, int nivelMontarDados) throws Exception;

    public List consultarPorDescricao(String valorConsulta, boolean controlarAcesso, int nivelMontarDados) throws Exception;

    public List consultarPorDataRegistro(Date prmIni, Date prmFim, boolean controlarAcesso, int nivelMontarDados) throws Exception;

    public MovimentoContaCorrenteClienteVO consultarPorCodigoPessoa(Integer valorConsulta, int nivelMontarDados) throws Exception;

    public List consultarPorCodigoPessoa(Integer valorConsulta, boolean controlarAcesso, int nivelMontarDados) throws Exception;

    public List consultarPorCodigoReciboPagamento(Integer valorConsulta, boolean controlarAcesso, int nivelMontarDados) throws Exception;

    public List consultarPorCodigoContratoOperacao(Integer valorConsulta, boolean controlarAcesso, int nivelMontarDados) throws Exception;

    public void setIdEntidade(String aIdEntidade);

    /**
     *
     * @param empresa           0 para consultar de todas as empresas
     * @param colaboradores
     * @param paginacao
     * @param dataBaseInicio
     * @return
     * @throws Exception
     */
    ResultSet consultarPendenciasClienteDevendoContaCorrente(int empresa, final String colaboradores,ConfPaginacao paginacao, Date dataBaseInicio) throws Exception;

    public ResultSet consultarPendenciasClienteCreditoContaCorrente(int empresa, final String colaboradores,ConfPaginacao paginacao) throws Exception;

    public ResultSet consultarPendenciasClienteCreditoContaCorrente(int empresa, final String colaboradores,ConfPaginacao paginacao, Date dataBaseInicio) throws Exception;

    public String consultarPendenciasClienteCreditoContaCorrenteJSON(int empresa, final String colaboradores, Date dataBaseInicio) throws Exception;

    public ResultSet contarPendenciasClienteDevendoContaCorrente(int empresa, final String colaboradores) throws Exception;

    /**
     *
     * @param empresa           0 para consultar de todas as empresas
     * @param colaboradores
     * @param dataBaseInicio
     * @return
     * @throws Exception
     */
    ResultSet contarPendenciasClienteDevendoContaCorrente(int empresa, final String colaboradores, Date dataBaseInicio) throws Exception;

    public ResultSet contarPendenciasClienteCreditoContaCorrente(int empresa, final String colaboradores, Date dataBaseInicio) throws Exception;

    public Double consultarSaldoAtual(Integer codPessoa) throws Exception;

    public List consultarPaginado(MovimentoContaCorrenteClienteFiltroVO filtro, ConfPaginacao confPaginacao) throws Exception;

    public void incluirSemCommit(MovimentoContaCorrenteClienteVO obj, boolean centralEventos) throws Exception;

    public void incluirSemCommit(MovimentoContaCorrenteClienteVO obj) throws Exception;

    public void alterar(MovimentoContaCorrenteClienteVO obj, boolean centralEventos) throws Exception;

    public void excluir(MovimentoContaCorrenteClienteVO obj, boolean centralEventos) throws Exception;

    public void adicionarmovimento(MovimentoContaCorrenteClienteVO obj) throws Exception;

    public void estornarCreditosMovpagamento(Integer movPagCodigo, UsuarioVO responsavel, Integer recibo) throws Exception;

    public void pagarProdutosDebito(MovimentoContaCorrenteClienteVO creditoContaCorrente, boolean b) throws NumberFormatException, Exception;

    public VendaAvulsaVO gerarProdutoPagamentoDebito(Double valor, ClienteVO cliente, TipoOperacaoContaCorrenteEnum tipo,
                                                     UsuarioVO responsavel) throws Exception;

    public VendaAvulsaVO gerarProdutoPagamentoCredito(Double valor, ClienteVO cliente, TipoOperacaoContaCorrenteEnum tipo,
                                                      UsuarioVO responsavel, String descricao) throws Exception;

    public VendaAvulsaVO gerarProdutoPagamentoCredito(Double valor, ClienteVO cliente, TipoOperacaoContaCorrenteEnum tipo, UsuarioVO responsavel,
                                                      String descricao, Date data, EmpresaVO empresaVO) throws Exception;

    public VendaAvulsaVO gerarProdutoPagamentoCredito(Double valor, ClienteVO cliente, ColaboradorVO colaboradorVO, TipoOperacaoContaCorrenteEnum tipo,
                                                      UsuarioVO responsavel, String descricao, Date data, Date dataVencimento, EmpresaVO empresaVO) throws Exception;

    public void gerarDebito(MovimentoContaCorrenteClienteVO movimento) throws Exception;

    public void incluirTransferenciaCredito(MovimentoContaCorrenteClienteVO depositante, MovimentoContaCorrenteClienteVO beneficiado, MovimentoContaCorrenteClienteVO atual) throws Exception;

    public void gerarParcelaCredito(MovimentoContaCorrenteClienteVO movcco) throws Exception;

    public void incluirParcelaParcialDebito(MovimentoContaCorrenteClienteVO movCCC, MovimentoContaCorrenteClienteVO atual) throws Exception;

    public String consultarJSON(String codPessoa, String moeda) throws Exception;

    public List consultarParaImpressao(String filtro, String ordem, String campoOrdenacao, int i, String codPessoa) throws Exception;

    ReciboDevolucaoVO ajustarSaldoContaCorrenteCliente(ClienteVO cliente, MovimentoContaCorrenteClienteVO ultimoMovimento,
                                                       Double valorDevolver, UsuarioVO usuario, Date dataRegistro, boolean devolverRestanteDinheiro,
                                                       Double novoValorConta, boolean lancarSaidaFinanceiro) throws Exception;

    public List<MovPagamentoVO> atualizarPagamentos(List<MovPagamentoVO> creditos, List<MovPagamentoVO> debitos, boolean pagarDebito) throws Exception;
    public MovimentoContaCorrenteClienteVO consultarPorMovPagamento(Integer movpagamento,int nivelMontarDados) throws Exception;
    public boolean ajustarDebitoAlteracaoManutencaoHorario(MovPagamentoVO pagamentoatual, MovPagamentoVO devolvido, String tipoProduto, ContratoVO contrato, UsuarioVO usuario) throws Exception;
}

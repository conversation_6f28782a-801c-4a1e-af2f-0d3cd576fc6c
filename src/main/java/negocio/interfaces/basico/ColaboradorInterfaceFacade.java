package negocio.interfaces.basico;

import br.com.pactosolucoes.enumeradores.TipoColaboradorEnum;
import br.com.pactosolucoes.estrutura.paginacao.ConfPaginacao;
import org.json.JSONArray;
import negocio.comuns.basico.ColaboradorVO;
import negocio.comuns.basico.TipoColaboradorVO;
import negocio.comuns.crm.GenericoTO;
import org.json.JSONObject;

import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.*;

/**
 * Interface reponsável por criar uma estrutura padrão de comunidação entre a camada de controle
 * e camada de negócio (em especial com a classe Façade). Com a utilização desta interface
 * é possível substituir tecnologias de uma camada da aplicação com mínimo de impacto nas demais.
 * Além de padronizar as funcionalidades que devem ser disponibilizadas pela camada de negócio, por intermédio
 * de sua classe Façade (responsável por persistir os dados das classes VO).
 */
public interface ColaboradorInterfaceFacade extends SuperInterface {

    public ColaboradorVO novo() throws Exception;

    public void incluir(ColaboradorVO obj) throws Exception;

    public void incluirVendaRapida(ColaboradorVO obj, boolean validarIncluir) throws Exception;

    public void incluirSemPessoa(ColaboradorVO obj) throws Exception;

    public void incluirApartirDaTelaUsuarioSistemaSemCommit(ColaboradorVO obj) throws Exception;

    public void alterar(ColaboradorVO obj) throws Exception;

    public void alterarVendaRapida(ColaboradorVO obj, boolean validarAlterar) throws Exception;

    public void alterarSemPermissao(ColaboradorVO obj) throws Exception;

    public void alterarSituacao(String situacao, Integer codigoColaborador) throws Exception;

    public void alterarSituacaoPorCodigoUsuario(String situacao, Integer codigoUsuario) throws Exception;

    public void excluir(ColaboradorVO obj) throws Exception;

    public void excluirSemPessoa(ColaboradorVO obj) throws Exception;

    public ColaboradorVO consultarPorChavePrimaria(Integer codigo, int nivelMontarDados) throws Exception;

    public List consultarPorDescricaoProfissao(String valorConsulta, Integer empresa, int nivelMontarDados) throws Exception;

    public List consultarPorDescricao(String valorConsulta, boolean controlarAcesso, int nivelMontarDados, TipoColaboradorEnum tipoColaborador) throws Exception;

    public List consultarPorCfp(String valorConsulta, Integer empresa, boolean controlarAcesso, int nivelMontarDados) throws Exception;

    public List consultarPorCodigo(Integer valorConsulta, Integer empresa, boolean controlarAcesso, int nivelMontarDados) throws Exception;

    public ColaboradorVO consultarPorCodigo(Integer valorConsulta, Integer empresa, int nivelMontarDados) throws Exception;

    public ColaboradorVO consultarColaboradorPorCodigo(Integer valorConsulta, Integer empresa, boolean controlarAcesso, int nivelMontarDados) throws Exception;

    public List<ColaboradorVO> consultarPorNomePessoa(String valorConsulta, Integer empresa, int nivelMontarDados) throws Exception;

    public List<ColaboradorVO> obterColaboradorPactoMetodoGestao(Integer empresa, int nivelMontarDados) throws Exception;

    public List consultarPorCodigoEmpresa(Integer valorConsulta, int nivelMontarDados) throws Exception;

    public List consultarPorNomeEmpresa(String valorConsulta, int nivelMontarDados) throws Exception;

    public List<ColaboradorVO> consultarPorNomeTipoColaborador(String valorConsulta, Integer empresa, Boolean apenasAtivos, Boolean ignorarTipos, int nivelMontarDados, TipoColaboradorEnum... tiposColaborador) throws Exception;

    public List<ColaboradorVO> consultarPorCpfTipoColaborador(String valorConsulta, Integer empresa, Boolean apenasAtivos, Boolean ignorarTipos, int nivelMontarDados, TipoColaboradorEnum... tiposColaborador) throws Exception;

    public List consultarPorSituacao(String valorConsulta, Integer empresa, boolean controlarAcesso, int nivelMontarDados) throws Exception;

    public List<ColaboradorVO> consultarPorTipoColaborador(TipoColaboradorEnum tipoColaborador, Integer empresa, boolean controlarAcesso, int nivelMontarDados) throws Exception;

    List<ColaboradorVO> consultarPorTipos(List<TipoColaboradorEnum> tipos, String situacao, Integer empresa, String where, String orderBy, int limit, int nivelMontarDados) throws Exception;

    public List<ColaboradorVO> consultarPorTipoColaboradorAtivo(TipoColaboradorEnum tipoColaborador, String situacao, Integer empresa, boolean controlarAcesso, int nivelMontarDados) throws Exception;

    public List consultarPorCodAcesso(String valorConsulta, Integer empresa, boolean controlarAcesso, int nivelMontarDados) throws Exception;

    public ColaboradorVO consultarPorCodAcesso(String valorConsulta, Integer empresa, int nivelMontarDados) throws Exception;

    public List consultarPorCodAlternativo(String valorConsulta, Integer empresa, boolean controlarAcesso, int nivelMontarDados) throws Exception;

    public ColaboradorVO consultarPorCodAlternativo(String valorConsulta, Integer empresa, int nivelMontarDados) throws Exception;

    public void registrarUltimoAcesso(int codigoColaborador, int codigoAcessoColaborador) throws Exception;

    public List consultarColaboradorResponsavelPorMatriculaRematricula(Date prmIni, Date prmFim, Integer empresa, boolean controlarAcesso, int nivelMontarDados) throws Exception;

    public void setIdEntidade(String aIdEntidade);

    public ColaboradorVO consultarPorNomeColaborador(String valorConsulta, Integer empresa, int nivelMontarDados) throws Exception;

    public ColaboradorVO consultarPorNomeColaboradorImportacao(String valorConsulta, Integer empresa, int nivelMontarDados) throws Exception;

    public List consultarPorNomeColaboradorComLimite(String valorConsulta, Integer empresa, boolean controlarAcesso, int nivelMontarDados) throws Exception;

    public ColaboradorVO consultarPorCodigoUsuario(Integer valorConsulta, Integer empresa, boolean controlarAcesso, int nivelMontarDados) throws Exception;

    public void incluirSemCommit(ColaboradorVO obj) throws Exception;

    void incluirSemCommit(ColaboradorVO obj, boolean incluirPessoa) throws Exception;

    public ColaboradorVO consultarColaboradorPorPessoaVinculada(Integer valorConsulta, int nivelMontarDados) throws Exception;

    public List consultarTodosColaboradorComLimite(Integer empresa, boolean controlarAcesso, int nivelMontarDados) throws Exception;

    public ColaboradorVO consultarPorCfp(String valorConsulta, int nivelMontarDados) throws Exception;

    public ColaboradorVO consultarExisteColaborador(String valorConsulta, Date data, int nivelMontarDados) throws Exception;

    public ColaboradorVO consultarPorCodigoPessoa(Integer codigoPrm, Integer empresa, int nivelMontarDados) throws Exception;

    public List consultarColaboradorPorClienteMensagem(Integer empresa, boolean controlarAcesso, int nivelMontarDados) throws Exception;

    public List<ColaboradorVO> consultarPorNomeOperador(String valorConsulta, Integer empresa, int nivelMontarDados) throws Exception;

    public void incluir(ColaboradorVO obj, boolean centralEventos, boolean validarPermissao) throws Exception;

    public void incluirSemPessoa(ColaboradorVO obj, boolean centralEventos) throws Exception;

    public void alterar(ColaboradorVO obj, boolean centralEventos,boolean validarPermissao) throws Exception;

    public void alterarSemCommit(ColaboradorVO obj) throws Exception ;

    public void excluir(ColaboradorVO obj, boolean centralEventos) throws Exception;

    public void excluirSemPessoa(ColaboradorVO obj, boolean centralEventos) throws Exception;

    public List consultarPorCodigoTurmaEOuModalidade(Integer turma, Integer modalidade, Integer empresa, int nivelMontarDados) throws Exception;

    public ColaboradorVO consultarPorCodigoIgualTipoColaborador(Integer valorConsulta, Integer empresa, TipoColaboradorEnum tipoColaborador, boolean controlarAcesso, int nivelMontarDados) throws Exception;

    public List<ColaboradorVO> consultarTodosColaboradoresPorTipoComLimite(TipoColaboradorEnum tipoColaborador, boolean controlarAcesso, int nivelMontarDados) throws Exception;

    public List<ColaboradorVO> consultarTodosColaboradoresPorTipoPorNomeComLimite(String nome, TipoColaboradorEnum tipoColaborador, boolean comPorcentagem, boolean controlarAcesso, int nivelMontarDados) throws Exception;

    public Double getPorcComissao(int aluno, int modalidade, int turma, int colaborador) throws Exception;
    public Double getValorComissao(int aluno, int modalidade, int turma, int colaborador) throws Exception;

    public ColaboradorVO consultarConsultorDoClienteNaData(int cliente, Date data) throws Exception;

    public List<ColaboradorVO> consultarPorTipoColaboradorAtivoNomePessoa(String nome,TipoColaboradorEnum tipoColaborador, String situacao, Integer empresa, boolean controlarAcesso, int nivelMontarDados) throws Exception;

    public List<ColaboradorVO> consultarPorTipoColaboradorAtivoNomePessoa(String nome,int colaboradorDesconsiderar,ListIterator<TipoColaboradorVO> tipoColaborador, String situacao, Integer empresa, boolean controlarAcesso, int nivelMontarDados) throws Exception;

    public List<ColaboradorVO> consultarPorTipoColaboradorAtivoCodigo(int codigo,ListIterator<TipoColaboradorVO> tipoColaborador, Integer empresa,Boolean controlarAcesso, int nivelMontarDados) throws Exception;

    public List<ColaboradorVO> consultarColaboradoresQuestionarios(Date prmIni, Date prmFim, Integer empresa, boolean controlarAcesso, int nivelMontarDados) throws Exception;

    public String obterNomePessoa(final int codColaborador) throws Exception;

    public List<ColaboradorVO> consultarPorNomeTipoVinculoPossivel(String nomeColaborador, TipoColaboradorEnum tipoColaborador, Integer codEmpresa, int nivelMontarDados) throws Exception;

    public String consultarJSON(Integer empresa, String sEcho, Integer offset, Integer limit, String clausulaLike, Integer colOrdenar, String dirOrdenar, String situacao,String tipoColaborador) throws Exception;

    public void validarCodAcessoAlternativo(final ColaboradorVO obj) throws Exception;

    public List consultarParaImpressao(String filtro, String ordem, String campoOrdenacao, Integer empresa, String situacao,String tipoColaborador) throws SQLException;

    public List<GenericoTO> consultarSimplesPorTipo(TipoColaboradorEnum tipo) throws Exception;

    public String consultarTokenGoogle(Integer codColaborador) throws Exception;

    /**
     *
     * @param empresa           0 para consultar de todas as empresas
     * @param paginacao
     * @param dataBaseInicio
     * @param dataBase
     * @return
     * @throws Exception
     */
    ResultSet consultarAniversarioColaborador(int empresa,ConfPaginacao paginacao,Date dataBaseInicio, Date dataBase) throws Exception;

    /**
     *
     * @param empresa           0 para consultar de todas as empresas
     * @param dataBaseInicio
     * @param dataBase
     * @return
     * @throws Exception
     */
    ResultSet contarAniversarioColaborador(int empresa,Date dataBaseInicio, Date dataBase) throws Exception;
    
    public void atualizarSaldoPersonal(Integer codigoColaborador, Integer saldo) throws Exception;
    
    public void atualizarEmAtendimento(Integer codigoColaborador, boolean emAtendimento) throws Exception;

    public ColaboradorVO criarOuConsultarSeExistePorNome(ColaboradorVO obj, boolean controlarTransacao) throws Exception;
    
    public byte[] obterFotoPersonal(Integer codigo) throws SQLException;

    public Boolean verificarColaboradorTemUsuario(int colaborador) throws Exception;
    
    public JSONArray obterProfessores(Integer empresa, boolean apenasAtivos) throws Exception;

    /**
     * Realiza a consulta do {@link ColaboradorVO} que esta vinculado a empresa.
     * A consulta se realiza trazendo o colaborador vinculado ao usuário, com base nesse colaborador traz o colaborador que esta associado a empresa que tenha o mesmo
     * pessoa_codigo do colaborador vinculado ao usuário.
     * @param codigoUsuario Codigo do {@link negocio.comuns.arquitetura.UsuarioVO}
     * @param codigoEmpresa Código da {@link negocio.comuns.basico.EmpresaVO}
     * @return O objeto {@link ColaboradorVO} com a propriedade codigo preenchida.
     */
    public ColaboradorVO consultarPorUsuarioEmpresaComBasePessoa(Integer codigoUsuario, Integer codigoEmpresa) throws Exception;
    public ColaboradorVO consultarPorCodigoPessoaSituacao(Integer codigoPrm, final String situacao, Integer empresa, int nivelMontarDados) throws Exception;
    public boolean existeColaboradorEmpresaPorCodigoPessoaSituacao(Integer codigoPrm, Integer empresa, Boolean ativo) throws Exception;
    public ColaboradorVO consultarConsultorDoContrato(int codigoContrato, int nivelMontarDados) throws Exception;
    public ColaboradorVO consultarConsultorDoReciboConsultor(int codigoRecibo, int nivelMontarDados) throws Exception;

    Map<Integer, List<String>> tiposDosColaboradores(Integer colaborador) throws Exception;

    public List<ColaboradorVO> consultarPorCodigoPessoa(Integer codigoPrm, int nivelMontarDados) throws Exception;

    public ColaboradorVO consultarPorCodigoPessoaObjetoSimples(Integer codigoPrm, int nivelMontarDados) throws Exception;

    JSONObject detalhesProfessor(Integer codigo) throws Exception;
    public void salvaCodigoVitioColaborador (Integer codigoCol, String codigoVitio) throws Exception;

    List<ColaboradorVO> consultarColaboradorEmailOuCPF(String email, String cpf, Integer empresa, Integer colaborador, int nivelMontarDados) throws Exception;

    JSONArray obterColaboradorEmailCpfDTO(List<ColaboradorVO> colaboradores);

    void alterarSincronizadoRedeEmpresa(Integer codColaborador) throws Exception;

    void removerSincronizadoRedeEmpresa(String codAcesso) throws Exception;

    void atualizarNovaPessoa(Integer codigoColaborador, Integer codigoPessoa) throws Exception;
}

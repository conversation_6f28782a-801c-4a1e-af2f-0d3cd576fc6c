package negocio.interfaces.basico;

import negocio.comuns.utilitarias.SolicitacaoVO;
import negocio.comuns.utilitarias.StatusSolicitacaoEnum;
import negocio.comuns.utilitarias.TipoSolicitacaoEnum;

import java.sql.SQLException;
import java.util.Date;
import java.util.List;

public interface SolicitacaoInterfaceFacade extends SuperInterface  {
    public void incluir(SolicitacaoVO obj) throws Exception ;

    public void alterar(SolicitacaoVO obj) throws Exception ;

    public List<SolicitacaoVO> consultarListaSolicitacoes(Integer codigoUsuario, Date dataInicio, Date dataFim, TipoSolicitacaoEnum tipo, StatusSolicitacaoEnum status, int nivelMontarDados, Integer empresa) throws Exception;

    public SolicitacaoVO consultarPorChavePrimaria(Integer codigo,int nivelMontarDados) throws  Exception;

    public SolicitacaoVO existeSolicitacaoComMesmoDados(SolicitacaoVO solicitacaoVO) throws  Exception;
}

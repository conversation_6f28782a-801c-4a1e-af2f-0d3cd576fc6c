package negocio.interfaces.basico;
import negocio.comuns.basico.TipoColaboradorVO;
import java.util.List;

/**
 * Interface reponsável por criar uma estrutura padrão de comunidação entre a camada de controle
 * e camada de negócio (em especial com a classe Façade). Com a utilização desta interface
 * é possível substituir tecnologias de uma camada da aplicação com mínimo de impacto nas demais.
 * Além de padronizar as funcionalidades que devem ser disponibilizadas pela camada de negócio, por intermédio
 * de sua classe Façade (responsável por persistir os dados das classes VO).
*/
public interface TipoColaboradorInterfaceFacade extends SuperInterface {


    public TipoColaboradorVO novo() throws Exception;
    public void incluir(TipoColaboradorVO obj) throws Exception;
    public void incluirSemPermissao(TipoColaboradorVO obj) throws Exception;
    public void alterar(TipoColaboradorVO obj) throws Exception;
    public void alterarSemPermissao(TipoColaboradorVO obj) throws Exception;
    public void excluir(TipoColaboradorVO obj) throws Exception;
    public TipoColaboradorVO consultarPorChavePrimaria(Integer codigo, int nivelMontarDados) throws Exception;
    public List consultarPorCodigo(Integer valorConsulta, boolean controlarAcesso, int nivelMontarDados) throws Exception;
    public List<TipoColaboradorVO> consultarPorCodigoColaborador(Integer valorConsulta, boolean controlarAcesso, int nivelMontarDados) throws Exception;
    public List consultarPorDescricao(String valorConsulta, boolean controlarAcesso, int nivelMontarDados) throws Exception;
    public void setIdEntidade(String aIdEntidade);
    public void incluirTipoColaboradors(Integer colaboradorPrm, List objetos, boolean validarPermissao) throws Exception;
    public void alterarTipoColaboradors(Integer colaborador, List objetos) throws Exception;
    public void excluirTipoColaborador(Integer colaborador) throws Exception;
    public List consultarTipoColaborador(Integer colaborador, int nivelMontarDados) throws Exception;
}

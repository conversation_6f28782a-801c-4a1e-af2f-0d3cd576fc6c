package negocio.interfaces.basico;

import negocio.comuns.arquitetura.UsuarioVO;
import negocio.comuns.basico.EmpresaVO;
import negocio.comuns.basico.enumerador.TipoNotificacaoUsuarioEnum;
import negocio.comuns.utilitarias.NotificacaoUsuarioVO;

import java.util.Date;
import java.util.List;

/**
 * Created by <PERSON><PERSON> on 26/01/2017.
 */
public interface NotificacaoUsuarioInterfaceFacade extends SuperInterface {

    void incluir(NotificacaoUsuarioVO obj) throws Exception;

    void alterar(NotificacaoUsuarioVO obj) throws Exception;

    void excluir(NotificacaoUsuarioVO obj) throws Exception;

    List<NotificacaoUsuarioVO> consultarPorUsuario(UsuarioVO usuario, int nivelMontarDados) throws Exception;

    NotificacaoUsuarioVO consultarPorUsuarioTipo(UsuarioVO usuario, TipoNotificacaoUsuarioEnum tipo, int nivelMontarDados) throws Exception;

    List<NotificacaoUsuarioVO> consultarPorUsuarioTipo(UsuarioVO usuario, List<TipoNotificacaoUsuarioEnum> listaTipos, int nivelMontarDados) throws Exception;

    NotificacaoUsuarioVO consultarNotificacao(UsuarioVO usuario, TipoNotificacaoUsuarioEnum tipoNotificacaoUsuarioEnum,
                                              Boolean apresentarhoje, Date dataReferencia, EmpresaVO empresaVO,
                                              int nivelMontarDados) throws Exception;

    String enviarNotificacaoUsuario(Integer codUsuario, String username, String mensagem, String link, Integer tipoNotificacao) throws Exception;

    void excluirTodasPorTipoUsuario(NotificacaoUsuarioVO obj, UsuarioVO usuario) throws Exception;
}

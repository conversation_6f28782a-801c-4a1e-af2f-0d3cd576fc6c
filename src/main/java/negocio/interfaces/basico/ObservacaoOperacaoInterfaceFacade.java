package negocio.interfaces.basico;

import negocio.comuns.basico.ColaboradorVO;
import negocio.comuns.basico.ObservacaoOperacaoVO;
import negocio.comuns.basico.enumerador.TipoObservacaoOperacaoEnum;

import java.util.Date;
import java.util.List;

/**
 * Created with IntelliJ IDEA.
 * User: GlaucoT
 * Date: 03/12/13
 * Time: 16:10
 * To change this template use File | Settings | File Templates.
 */
public interface ObservacaoOperacaoInterfaceFacade extends SuperInterface {

    public void incluir(ObservacaoOperacaoVO obj) throws Exception;

    public void alterar(ObservacaoOperacaoVO obj) throws Exception;

    public void excluir(ObservacaoOperacaoVO obj) throws Exception;

//    public List<ObservacaoOperacaoVO> consultar(Integer codCliente, int nivelMontarDados) throws Exception;

//    public List<ObservacaoOperacaoVO> consultarPorDataEstorno(Date dataEstorno, int nivelMontarDados) throws Exception;

    public int contarPorNomeEntidadePorDataAlteracaoPorOperacao(Date dataAlteracaoInicial, Date dataAlteracaoFim, Integer codEmpresa, boolean buscarComAdministrador, TipoObservacaoOperacaoEnum tipoObservacao,  List<ColaboradorVO> lista, int nivelMontarDados) throws Exception;

    public List<ObservacaoOperacaoVO> consultarPorNomeEntidadePorDataAlteracaoPorOperacao(Date dataAlteracaoInicial, Date dataAlteracaoFim, Integer codEmpresa, boolean buscarComAdministrador, TipoObservacaoOperacaoEnum tipoObservacao, List<ColaboradorVO> lista, int nivelMontarDados) throws Exception;
}

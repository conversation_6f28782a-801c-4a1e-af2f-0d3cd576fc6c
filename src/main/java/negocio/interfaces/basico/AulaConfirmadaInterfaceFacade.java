package negocio.interfaces.basico;


import br.com.pactosolucoes.agendatotal.json.AgendamentoConfirmadoJSON;
import negocio.facade.jdbc.basico.AulaConfirmadaVO;

import java.util.Date;
import java.util.List;

/**
 * Created by <PERSON> on 20/12/2016.
 */
public interface AulaConfirmadaInterfaceFacade extends SuperInterface{

    public void incluir(final AulaConfirmadaVO obj) throws Exception;

    public void incluirSemCommit(final AulaConfirmadaVO obj) throws Exception;

    public List<AulaConfirmadaVO> consultarAulaConfirmada(int cliente,int horarioTurma,Date dia,int nivelMontarDados) throws Exception;

    public List<AgendamentoConfirmadoJSON> consultarAgendamentosConfirmados(Date inicio, Date fim)throws Exception;

    public void excluir(final AulaConfirmadaVO obj) throws Exception;
    
    public List<AulaConfirmadaVO> consultarAulaConfirmadaPorData(Date dia) throws Exception;
}

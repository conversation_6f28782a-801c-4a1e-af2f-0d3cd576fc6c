/*
 * To change this template, choose Too<PERSON> | Templates
 * and open the template in the editor.
 */
package negocio.interfaces.basico;

import br.com.pactosolucoes.enumeradores.BIEnum;
import br.com.pactosolucoes.enumeradores.ConfiguracaoBIEnum;
import java.util.List;
import java.util.Map;
import negocio.comuns.basico.ConfiguracaoBIVO;

/**
 *
 * <AUTHOR>
 */
public interface ConfiguracaoBIInterfaceFacade extends SuperInterface {
    
    public List<ConfiguracaoBIVO> consultarPorBI(BIEnum bi, Integer empresa) throws Exception;
    
    public void deletarPorBI(BIEnum bi, Integer empresa) throws Exception;

    public void incluir(ConfiguracaoBIVO cfg) throws Exception;

    public void incluirPorBI(BIEnum BI, Integer empresa, List<ConfiguracaoBIVO> campos) throws Exception;
    
    public void incluirPorConfig(Integer empresa, ConfiguracaoBIEnum cfg, String valor) throws Exception;
    
    public String valorPorConfig(Integer empresa, ConfiguracaoBIEnum cfg) throws Exception;
    
    public Map<ConfiguracaoBIEnum, Boolean> mapaConfigs(Integer empresa, ConfiguracaoBIEnum ... cfgs) throws Exception;
}

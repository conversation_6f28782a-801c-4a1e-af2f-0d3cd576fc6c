package negocio.interfaces.basico;

import java.util.Date;
import java.util.List;
import negocio.comuns.basico.ParceiroFidelidadeVO;
import negocio.comuns.basico.enumerador.TipoParceiroEnum;

/**
 *
 * <AUTHOR>
 */
public interface ParceiroFidelidadeInterfaceFacade extends SuperInterface {

    ParceiroFidelidadeVO novo() throws Exception;

    void incluir(ParceiroFidelidadeVO obj) throws Exception;

    void alterar(ParceiroFidelidadeVO obj) throws Exception;

    void excluir(ParceiroFidelidadeVO obj) throws Exception;

    ParceiroFidelidadeVO consultarPorChavePrimaria(Integer codigo, int nivelMontarDados) throws Exception;

    List consultarPorEmpresa(Integer codigoEmpresa, boolean controlarAcesso, int nivelMontarDados) throws Exception;
    
    ParceiroFidelidadeVO consultarPorEmpresaETipo(final Integer codigoEmpresa, final TipoParceiroEnum tipo, int nivelMontarDados) throws Exception;

    ParceiroFidelidadeVO consultarPorTabelaParceiroFidelidade(Integer tabelaParceiroFidelidade, int nivelMontarDados) throws Exception;

    void atualizarToken(String token, Date dataExpiracaoToken, Integer codigo) throws Exception;

    void setIdEntidade(String aIdEntidade);
}
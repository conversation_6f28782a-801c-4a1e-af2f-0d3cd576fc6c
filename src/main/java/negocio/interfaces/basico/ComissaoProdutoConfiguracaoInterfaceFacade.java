package negocio.interfaces.basico;

import negocio.comuns.contrato.ComissaoProdutoConfiguracaoVO;
import negocio.comuns.plano.CategoriaProdutoVO;
import negocio.comuns.plano.ProdutoVO;

import java.util.List;

/**
 * Created by GlaucoT on 25/01/2017
 */
public interface ComissaoProdutoConfiguracaoInterfaceFacade extends SuperInterface {

    List<ComissaoProdutoConfiguracaoVO> consultarPorProduto(Integer codProduto) throws Exception;

    List<ComissaoProdutoConfiguracaoVO> consultarPorCategoriaProduto(Integer codCategoriaProduto) throws Exception;

    void atualizarPorProduto(ProdutoVO obj) throws Exception;

    void atualizarPorCategoriaProduto(CategoriaProdutoVO obj) throws Exception;
}

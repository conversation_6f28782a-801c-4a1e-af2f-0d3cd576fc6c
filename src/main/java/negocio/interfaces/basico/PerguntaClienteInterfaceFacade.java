package negocio.interfaces.basico;
import negocio.comuns.basico.PerguntaClienteVO;
import java.util.List;

/**
 * Interface reponsável por criar uma estrutura padrão de comunidação entre a camada de controle
 * e camada de negócio (em especial com a classe Façade). Com a utilização desta interface 
 * é possível substituir tecnologias de uma camada da aplicação com mínimo de impacto nas demais.
 * Além de padronizar as funcionalidades que devem ser disponibilizadas pela camada de negócio, por intermédio
 * de sua classe Façade (responsável por persistir os dados das classes VO).
*/
public interface PerguntaClienteInterfaceFacade extends SuperInterface {
	

    public PerguntaClienteVO novo() throws Exception;
    public void incluir(PerguntaClienteVO obj) throws Exception;
    public void incluirSemCommit(PerguntaClienteVO obj) throws Exception ;
    public void alterar(PerguntaClienteVO obj) throws Exception;
    public void alterarSemCommit(PerguntaClienteVO obj) throws Exception ;
    public void excluir(PerguntaClienteVO obj) throws Exception;
    public PerguntaClienteVO consultarPorChavePrimaria(Integer codigo, int nivelMontarDados) throws Exception;
    public List consultarPorCodigo(Integer valorConsulta, boolean controlarAcesso, int nivelMontarDados) throws Exception;
    public List consultarPorDescricao(String valorConsulta, boolean controlarAcesso, int nivelMontarDados) throws Exception;
    public List consultarPorTipoPergunta(String valorConsulta, boolean controlarAcesso, int nivelMontarDados) throws Exception;
    public void setIdEntidade(String aIdEntidade);
    public void incluirPerguntaCliente(PerguntaClienteVO obj, Boolean validarQuestionario, boolean controlarAcesso) throws Exception;
    public void alterarPeguntaCliente(PerguntaClienteVO obj, Boolean validarQuestionario) throws Exception;
    public void excluirPerguntaCliente(PerguntaClienteVO obj) throws Exception;
}
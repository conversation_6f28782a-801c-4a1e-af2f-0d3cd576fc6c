/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */

package negocio.interfaces.basico;

import java.util.List;
import negocio.comuns.basico.ModalidadeComissaoColaboradorVO;

/**
 * Interface reponsável por criar uma estrutura padrão de comunidação entre a camada de controle
 * e camada de negócio (em especial com a classe Façade). Com a utilização desta interface
 * é possível substituir tecnologias de uma camada da aplicação com mínimo de impacto nas demais.
 * Além de padronizar as funcionalidades que devem ser disponibilizadas pela camada de negócio, por intermédio
 * de sua classe Façade (responsável por persistir os dados das classes VO).
 * <AUTHOR>
 */
public interface ModalidadeComissaoColaboradorInterfaceFacade extends SuperInterface  {

    public void incluir(ModalidadeComissaoColaboradorVO obj) throws Exception;
    public void alterar(ModalidadeComissaoColaboradorVO obj) throws Exception;
    public void excluir(ModalidadeComissaoColaboradorVO obj) throws Exception;
    public void excluirModalidadeComissao(Integer colaborador) throws Exception;
    public void alterarModalidadeComissao(Integer colaborador, List objetos) throws Exception;
    public List consultarModalidadeComissao(Integer colaborador, int nivelMontarDados) throws Exception;
    public void incluirModalidadesComissao(Integer colaboradorPrm, List<ModalidadeComissaoColaboradorVO> objetos) throws Exception;
}

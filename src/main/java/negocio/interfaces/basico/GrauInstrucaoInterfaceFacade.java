package negocio.interfaces.basico;

import negocio.comuns.basico.GrauInstrucaoVO;

import java.util.List;

public interface GrauInstrucaoInterfaceFacade extends SuperInterface {

    public GrauInstrucaoVO novo() throws Exception;

    public void incluir(GrauInstrucaoVO obj) throws Exception;

    public void alterar(GrauInstrucaoVO obj) throws Exception;

    public void excluir(GrauInstrucaoVO obj) throws Exception;

    public GrauInstrucaoVO consultarPorChavePrimaria(Integer codigo, int nivelMontarDados) throws Exception;

    public List consultarPorCodigo(Integer valorConsulta, boolean controlarAcesso, int nivelMontarDados) throws Exception;

    public GrauInstrucaoVO consultarPorCodigoExato(Integer valorConsulta, boolean controlarAcesso, int nivelMontarDados) throws Exception;

    public List consultarPorDescricao(String valorConsulta, boolean controlarAcesso, int nivelMontarDados) throws Exception;

    public void setIdEntidade(String aIdEntidade);

    public void incluir(GrauInstrucaoVO obj, boolean centralEventos) throws Exception;

    public void alterar(GrauInstrucaoVO obj, boolean centralEventos) throws Exception;

    public void excluir(GrauInstrucaoVO obj, boolean centralEventos) throws Exception;

    public String consultarJSON() throws Exception;

    public List consultarParaImpressao(String filtro, String ordem, String campoOrdenacao, int i) throws Exception;
}

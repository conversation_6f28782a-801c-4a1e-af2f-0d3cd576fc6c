package negocio.interfaces.nfe;

import negocio.comuns.nfe.EmpresaNFeVO;
import negocio.interfaces.basico.SuperInterface;

import javax.faces.model.SelectItem;
import java.sql.SQLException;
import java.util.List;

public interface EmpresaNFeInterfaceFacade extends SuperInterface {

    public EmpresaNFeVO novo() throws Exception;

    public void incluir(EmpresaNFeVO obj) throws Exception;

    public void alterar(EmpresaNFeVO obj) throws Exception;

    public void excluir(EmpresaNFeVO obj) throws Exception;

    public EmpresaNFeVO consultarPorChavePrimaria(Integer codigo) throws Exception;

    public List<EmpresaNFeVO> consultarPorCodigo(int codigo,String situacaEmpresa) throws Exception;

    public List<EmpresaNFeVO> consultarPorRazaoSocial(String razaoSocial,String situacaEmpresa) throws Exception;

    public List<EmpresaNFeVO> consultarPorNomeFantasia(String nomeFantasia,String situacaEmpresa) throws Exception;

    public List<EmpresaNFeVO> consultarPorCidade(String cidade,String situacaEmpresa) throws Exception;

    public List<SelectItem> obtenhaEmpresasCadastradas() throws Exception;

    public void incluirCertificado(EmpresaNFeVO empresaNFeVO) throws Exception;

    public boolean temCerficadoCadastrado(EmpresaNFeVO empresaNFeVO) throws Exception;

    public void atualizarCertificado(EmpresaNFeVO empresaNFeVO) throws Exception;

    EmpresaNFeVO consultarPorChave(String chave) throws Exception;

    void atualizarParametros(EmpresaNFeVO empresaNFeVO, String params) throws SQLException;

}





package negocio.interfaces.nfe;

import org.json.JSONArray;
import org.json.JSONObject;
import negocio.comuns.basico.webservice.IntegracaoNFSeWS;
import negocio.comuns.nfe.*;
import negocio.comuns.nfe.enumerador.ModeloDocumentoFiscal;
import negocio.comuns.nfe.enumerador.TipoNotaEnum;
import negocio.interfaces.basico.SuperInterface;

import javax.servlet.http.HttpServletRequest;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Set;

public interface NotaFiscalDeServicoInterfaceFacade extends SuperInterface {

    public NotaFiscalDeServicoVO consultarPorChavePrimaria(Integer codigo, Integer nivelMontarDados) throws Exception;

    public List<NotaFiscalDeServicoVO> listarNotas(Integer empresaVO, FiltroNFeTO filtroTO, String ordenador) throws Exception;

    public void cancelarNota(NotaFiscalDeServicoVO obj, int idUsuario) throws Exception;

    public NotaFiscalDeServicoVO alterarStatusDaNota(NotaFiscalDeServicoVO nota, String status) throws Exception;

    public void enviarEmail(NotaFiscalDeServicoVO obj, int idUsuario, String observacao) throws Exception;

    public List<OperacaoNotaVO> historicoDaNota(NotaFiscalDeServicoVO obj) throws Exception;

    public byte[] obtenhaArquivoDaNota(NotaFiscalDeServicoVO obj, String tipoDoArquivo) throws Exception;

    public List<LogOperacaoNotaVO> logDaNota(NotaFiscalDeServicoVO obj) throws Exception;

    public byte[] obtenhaArquivoNota(String extensao, Integer codigoReferencia, Date dataReferencia, boolean NFCe, boolean xmlPrefeitura, boolean xmlManipulado, String caminho, boolean xmlCancelamento, boolean xmlManipuladoNFCe) throws Exception;

    void alterarStatusNotaFiscal(String status, String motivo, Integer id_rps) throws Exception;

    void alterarNotasParaExcluidas(String notas) throws Exception;

    void inserirLogNotasExcluidas(String notas) throws Exception;

    Integer existeNotaDiferenteDeNaoAutorizada(String listaLote) throws Exception;

    List<NotaFiscalConsumidorNFCeVO> listarNFCe(Integer empresaVO, FiltroNFeTO filtroTO, String ordenador) throws Exception;

    NotaFiscalConsumidorNFCeVO consultarPorChavePrimariaNFCe(Integer codigo, Integer nivelMontarDados) throws Exception;

    void cancelarNotaNFCe(NotaFiscalConsumidorNFCeVO obj, int idUsuario) throws Exception;

    public boolean empresaUsaXMLPrefeitura(Integer idRPS, Integer idEmpresa) throws Exception;

    boolean empresaUsaXMLManipulado(Integer idRPS, Integer idEmpresa) throws Exception;

    boolean empresaUsaXMLManipuladoNFCe(Integer idRPS, Integer idEmpresa) throws Exception;

    List<Integer> listaNotasGerarPDF() throws Exception;

    List<NumerosInutilizarVO> logInutilizarNFCE(NotaFiscalConsumidorNFCeVO obj) throws Exception;

    byte[] obtenhaXMLInutilizarNFCE(NotaFiscalConsumidorNFCeVO obj) throws Exception;

    List<NotaFiscalDeServicoVO> consultarNFSe(String listaLote) throws Exception;

    List<NotaFiscalConsumidorNFCeVO> consultarNFCe(String listaNFCe) throws Exception;

    JSONObject consultarDash(String chave) throws Exception;

    void inserirLogNotaReenviada(Date dataReenvio, String usuario, Integer id_Referencia, TipoNotaEnum tipoNotaEnum) throws Exception;

    JSONArray consultarGestaoDeNotas(String chave, String mesReferencia) throws Exception;

    List<NotasImprimirTO> consultarNotasParaGeracaoPDF(String codigosId_RPS) throws Exception;

    String consultarProximoCodigoTabelaCodigo(String identificador) throws Exception;

    void alterarProximoCodigoTabelaCodigo(String proximoCodigo, String identificador) throws Exception;

    List<NFCeImprimirTO> consultarNFCeParaGeracaoPDF(String codigosId_NFCe) throws Exception;

    void limparIdReferenciaParaReenvio(NotaFiscalDeServicoVO notaFiscalDeServicoVO) throws Exception;

    void inserirLogGenericoNotas(Integer Id_Lote, Integer id_NFCe, String descricao) throws Exception;

    List<NotaFiscalDeServicoVO> consultarPorId_Lote(String listaIdLote) throws Exception;

    boolean empresaNFE(Integer idEmpresa) throws Exception;

    List<NumerosInutilizarVO> logInutilizarNFE(Integer idEmpresa) throws Exception;

    /**
     * Veja em: {@link IntegracaoNFSeWS#consultarQuantidadeNotasNaoAutorizadasOuCanceladasDesdeOntem(String, String, String, List)}.
     */
    Map<ModeloDocumentoFiscal, DocumentoFiscalRelatorioPeriodoTO> consultarQuantidadeNotasNaoAutorizadasOuCanceladasDesdeOntem(String chaveNFSe,
                                                                                                                               String chaveZW,
                                                                                                                               String cpfCnpj,
                                                                                                                               Set<ModeloDocumentoFiscal> modelosPesquisa) throws Exception;

    NotaFiscalConsumidorNFCeVO consultarStatusGestaoNFCe(Integer codigo) throws Exception;

    String gerarPDFNotasNFSe(String codigosId_RPS, HttpServletRequest request) throws Exception;

    String gerarPDFNotasNFCe(String codigosId_NFCe, HttpServletRequest request) throws Exception;
}

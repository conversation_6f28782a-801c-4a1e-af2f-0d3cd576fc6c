package negocio.interfaces.nfe;

import negocio.comuns.nfe.ItemRPSNFeVO;
import negocio.comuns.nfe.NotaFiscalDeServicoVO;
import negocio.interfaces.basico.SuperInterface;

import java.util.List;

public interface ItemRPSNFeInterfaceFacade extends SuperInterface {

    public ItemRPSNFeVO novo() throws Exception;

    public void incluir(ItemRPSNFeVO obj) throws Exception;

    public void alterar(ItemRPSNFeVO obj) throws Exception;

    public void excluir(ItemRPSNFeVO obj) throws Exception;

    public ItemRPSNFeVO consultarPorChavePrimaria(Integer codigo) throws Exception;

    public List<ItemRPSNFeVO> listarItensDaNota(NotaFiscalDeServicoVO notaVO) throws Exception;
}

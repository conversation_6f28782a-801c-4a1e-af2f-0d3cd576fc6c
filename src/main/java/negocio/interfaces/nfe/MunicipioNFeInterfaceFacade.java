package negocio.interfaces.nfe;

import negocio.comuns.nfe.MunicipioNFeVO;
import negocio.interfaces.basico.SuperInterface;

import javax.faces.model.SelectItem;
import java.util.List;

public interface MunicipioNFeInterfaceFacade extends SuperInterface {

    public MunicipioNFeVO novo() throws Exception;

    public void incluir(MunicipioNFeVO obj) throws Exception;

    public void alterar(MunicipioNFeVO obj) throws Exception;

    public void excluir(MunicipioNFeVO obj) throws Exception;

    public MunicipioNFeVO consultarPorChavePrimaria(Integer codigo) throws Exception;

    public List<SelectItem> obtenhaMunicipiosHomologados() throws Exception;

    List<SelectItem> obtenhaMunicipios(String estadoUF) throws Exception;

}

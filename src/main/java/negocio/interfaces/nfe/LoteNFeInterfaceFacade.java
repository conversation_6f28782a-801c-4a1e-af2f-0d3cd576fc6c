package negocio.interfaces.nfe;

import org.json.JSONObject;
import negocio.comuns.basico.ConfiguracaoNotaFiscalVO;
import negocio.comuns.basico.EmpresaVO;
import negocio.comuns.financeiro.ItemGestaoNotaFamiliaTO;
import negocio.comuns.financeiro.enumerador.TipoRelatorioDF;
import negocio.comuns.nfe.LoteNFeVO;
import negocio.comuns.nfe.RetornoEnvioNotaFiscalTO;
import negocio.comuns.nfe.enumerador.StatusNotaEnum;
import negocio.interfaces.basico.SuperInterface;

import java.util.Date;
import java.util.List;

public interface LoteNFeInterfaceFacade extends SuperInterface {

    LoteNFeVO consultarPorChavePrimaria(Integer codigo) throws Exception;

    StatusNotaEnum retornarStatus(Integer id_rps);

    String pegarLinkNFSeDoIdExterno(List<Integer> idsReferencia);

    RetornoEnvioNotaFiscalTO reenviarRPS(JSONObject jsonObject, Integer id_rps);

    void gravarLotePorFamiliaAsync(ConfiguracaoNotaFiscalVO configNotaFiscalVO, List<ItemGestaoNotaFamiliaTO> notasEmitir,
                                   TipoRelatorioDF tipoRelatorioDF, EmpresaVO empresaVO, Date dataEmissaoGestaoNotas, String chave) throws Exception;

    RetornoEnvioNotaFiscalTO inutilizarNFe(JSONObject jsonObject);
}
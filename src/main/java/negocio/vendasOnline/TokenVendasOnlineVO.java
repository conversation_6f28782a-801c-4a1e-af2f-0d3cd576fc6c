package negocio.vendasOnline;

import negocio.comuns.utilitarias.ConsistirException;
import negocio.comuns.utilitarias.UteisValidacao;
import org.json.JSONObject;
import servicos.vendasonline.dto.DadosTokenDTO;

import java.util.Date;

/**
 * Created with IntelliJ IDEA.
 * User: <PERSON>
 * Date: 26/08/2022
 */

public class TokenVendasOnlineVO {
    private Integer codigo;
    private String token;
    private Date dataRegistro;
    private String dados;
    private Date dataUtilizado;

    public TokenVendasOnlineVO() {
    }

    public TokenVendasOnlineVO(Integer codigo, String token, Date dataRegistro, String dados) {
        this.codigo = codigo;
        this.token = token;
        this.dataRegistro = dataRegistro;
        this.dados = dados;
    }

    public static void validarDados(TokenVendasOnlineVO obj) throws ConsistirException {
        if (UteisValidacao.emptyString(obj.getDados())) {
            throw new ConsistirException("Não foi informado os dados para gravar o token");
        }
        if (UteisValidacao.emptyString(obj.getToken())) {
            throw new ConsistirException("Não foi informado o token");
        }
        if (obj.getDataRegistro() != null && UteisValidacao.emptyString(obj.getDataRegistro().toString())) {
            throw new ConsistirException("Não foi informado a data de criação");
        }
    }

    public JSONObject toJSON() {
        return new JSONObject(this);
    }

    public Integer getCodigo() {
        return codigo;
    }

    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public String getToken() {
        return token;
    }

    public void setToken(String token) {
        this.token = token;
    }

    public Date getDataRegistro() {
        return dataRegistro;
    }

    public void setDataRegistro(Date dataRegistro) {
        this.dataRegistro = dataRegistro;
    }

    public String getDados() {
        return dados;
    }

    public void setDados(String dados) {
        this.dados = dados;
    }

    public DadosTokenDTO getDadosTokenDTO() {
        return new DadosTokenDTO(new JSONObject(this.getDados()));
    }

    public Date getDataUtilizado() {
        return dataUtilizado;
    }

    public void setDataUtilizado(Date dataUtilizado) {
        this.dataUtilizado = dataUtilizado;
    }
}

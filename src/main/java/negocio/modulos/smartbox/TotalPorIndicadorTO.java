/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package negocio.modulos.smartbox;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.List;

import negocio.comuns.arquitetura.SuperTO;
import negocio.comuns.basico.ClienteVO;
import negocio.comuns.basico.ColaboradorVO;
import negocio.comuns.contrato.ContratoVO;
import negocio.comuns.utilitarias.Calendario;
import negocio.comuns.utilitarias.Uteis;
import negocio.facade.jdbc.arquitetura.FacadeManager;
import negocio.modulos.smartbox.enumerador.IndicadorSmartBoxEnum;
import negocio.modulos.smartbox.enumerador.TipoBoxEnum;
import org.jfree.data.general.SeriesDataset;
import org.jfree.data.xy.XYSeries;
import org.jfree.data.xy.XYSeriesCollection;

/**
 *
 * <AUTHOR>
 */
public class TotalPorIndicadorTO extends SuperTO {

    private IndicadorSmartBoxEnum indicador;
    private Integer totalLista = 0;
    private List<ContratoVO> listaContratos = new ArrayList<ContratoVO>();
    private List<ClienteVO> listaClientes = new ArrayList<ClienteVO>();
    private TipoBoxEnum tipoBox;
    private List<ColaboradorVO> colaboradoresSelecionados = new ArrayList<ColaboradorVO>();
    private transient byte[] chart = null;
    private int empresa = 0;
    private String memoriaCalculoGrafico = null;

    public IndicadorSmartBoxEnum getIndicador() {
        return indicador;
    }

    public void setIndicador(IndicadorSmartBoxEnum indicador) {
        this.indicador = indicador;
    }

    public List<ClienteVO> getListaClientes() {
        return listaClientes;
    }

    public void setListaClientes(List<ClienteVO> listaClientes) {
        this.listaClientes = listaClientes;
    }

    public List<ContratoVO> getListaContratos() {
        return listaContratos;
    }

    public void setListaContratos(List<ContratoVO> listaContratos) {
        this.listaContratos = listaContratos;
    }

    public Integer getTotalLista() {
        return totalLista;
    }

    public void setTotalLista(Integer totalLista) {
        this.totalLista = totalLista;
    }

    public TipoBoxEnum getTipoBox() {
        return tipoBox;
    }

    public void setTipoBox(TipoBoxEnum tipoBox) {
        this.tipoBox = tipoBox;
    }

    public List<ColaboradorVO> getColaboradoresSelecionados() {
        return colaboradoresSelecionados;
    }

    public void setColaboradoresSelecionados(List<ColaboradorVO> colaboradoresSelecionados) {
        this.colaboradoresSelecionados = colaboradoresSelecionados;
    }

    public byte[] getChart() {
        return chart;
    }

    public void setChart(byte[] chartImage) {
        this.chart = chartImage;
    }

    public int getEmpresa() {
        return empresa;
    }

    public void setEmpresa(int empresa) {
        this.empresa = empresa;
    }

    public String getMemoriaCalculoGrafico() {
        return memoriaCalculoGrafico;
    }

    public void setMemoriaCalculoGrafico(String memoriaCalculoPrevisaoRenovacao) {
        this.memoriaCalculoGrafico = memoriaCalculoPrevisaoRenovacao;
    }

    private int obterPrevistosRenovar(int empresa, Date dataInicio, Date dataFim, String condicaoColaborador) throws Exception {
        return FacadeManager.getFacade().getContrato().contarQtdeContratoPrevistoPeriodo(empresa,
                dataInicio, dataFim, condicaoColaborador, false,
                tipoBox.getCondicaoDuracaoParaContrato(), null);
    }

    private int obterRenovados(int empresa, Date dataInicio, Date dataFim, String condicaoColaborador) throws Exception {
        return FacadeManager.getFacade().getContrato().contarQtdeContratoPrevistoPeriodo(empresa, dataInicio,
                dataFim, condicaoColaborador, true,
                tipoBox.getCondicaoDuracaoParaContrato(), null);
    }

    private int obterRenovadosAgendados(int empresa, Date dataInicio, Date dataFim, String condicaoColaborador) throws Exception {
        return FacadeManager.getFacade().getContrato().contarQtdeContratoPrevistoPeriodo(empresa, dataInicio,
                dataFim, condicaoColaborador, true,
                tipoBox.getCondicaoDuracaoParaContrato(), " and c.origemcontrato = 2 ");
    }

    private int obterRenovadosEspontaneos(int empresa, Date dataInicio, Date dataFim, String condicaoColaborador) throws Exception {
        return FacadeManager.getFacade().getContrato().contarQtdeContratoPrevistoPeriodo(empresa, dataInicio,
                dataFim, condicaoColaborador, true,
                tipoBox.getCondicaoDuracaoParaContrato(), " and c.origemcontrato = 1 ");
    }

    private SeriesDataset createDataset(String condicaoColaboradores) throws Exception {

        class PeriodoDatas implements Serializable {

            private Date dataInicio;
            private Date dataFim;
            private int prev = 0;
            private int renov = 0;
            private int agend = 0;
            private int espont = 0;

            public PeriodoDatas(Date dataInicio, Date dataFim) {
                this.dataInicio = dataInicio;
                this.dataFim = dataFim;
            }

            public Date getDataFim() {
                return dataFim;
            }

            public void setDataFim(Date dataFim) {
                this.dataFim = dataFim;
            }

            public Date getDataInicio() {
                return dataInicio;
            }

            public void setDataInicio(Date dataInicio) {
                this.dataInicio = dataInicio;
            }

            public int getPrev() {
                return prev;
            }

            public void setPrev(int prev) {
                this.prev = prev;
            }

            public int getRenov() {
                return renov;
            }

            public void setRenov(int renov) {
                this.renov = renov;
            }

            public int getAgend() {
                return agend;
            }

            public void setAgend(int agend) {
                this.agend = agend;
            }

            public int getEspont() {
                return espont;
            }

            public void setEspont(int espont) {
                this.espont = espont;
            }
        }

        memoriaCalculoGrafico = "";

        switch (this.indicador) {
            case PREVISAO_RENOVACAO: {
                memoriaCalculoGrafico += getCabecalhoHintGrafico();

                List<PeriodoDatas> lista = new ArrayList<PeriodoDatas>();
                Date dataInicio = Calendario.hoje();
                int prev;
                int renov;
                int agendados;
                int espontaneos;

                //inicializa a lista de periodos semanais: 4 ultimas semanas
                for (int i = 1; i <= 4; i++) {
                    Date dataFim = (Date) dataInicio.clone();
                    dataInicio = Uteis.somarCampoData(dataInicio, Calendar.DAY_OF_MONTH, -7);
                    lista.add(new PeriodoDatas(dataInicio, dataFim));
                    dataInicio = Uteis.somarDias(dataInicio, -1);
                }
                //
                //calcula os previstos para renovar por semana
                final XYSeries seriesPrevistos = new XYSeries("Previstos");
                int semana = 1;
                for (PeriodoDatas periodoRenovacao : lista) {
                    prev = obterPrevistosRenovar(empresa,
                            periodoRenovacao.getDataInicio(),
                            periodoRenovacao.getDataFim(), condicaoColaboradores);
                    periodoRenovacao.setPrev(prev);
                    seriesPrevistos.add(semana, prev);
                    semana++;
                }
                //
                //calcula os renovados por semana
                final XYSeries seriesRenovados = new XYSeries("Renovados");
                semana = 1;
                for (PeriodoDatas periodoRenovacao : lista) {
                    renov = obterRenovados(empresa, periodoRenovacao.getDataInicio(),
                            periodoRenovacao.getDataFim(), condicaoColaboradores);
                    periodoRenovacao.setRenov(renov);
                    seriesRenovados.add(semana, renov);
                    semana++;
                }

                final XYSeries seriesAgendados = new XYSeries("Agendados");
                semana = 1;
                for (PeriodoDatas periodoRenovacao : lista) {
                    agendados = obterRenovadosAgendados(empresa, periodoRenovacao.getDataInicio(),
                            periodoRenovacao.getDataFim(), condicaoColaboradores);
                    periodoRenovacao.setAgend(agendados);
                    seriesAgendados.add(semana, agendados);
                    semana++;
                }

                final XYSeries seriesEspontaneos = new XYSeries("Espontâneos");
                semana = 1;
                for (PeriodoDatas periodoRenovacao : lista) {
                    espontaneos = obterRenovadosEspontaneos(empresa, periodoRenovacao.getDataInicio(),
                            periodoRenovacao.getDataFim(), condicaoColaboradores);
                    periodoRenovacao.setEspont(espontaneos);
                    seriesEspontaneos.add(semana, espontaneos);
                    semana++;
                }


                final XYSeriesCollection dataset = new XYSeriesCollection();
                dataset.addSeries(seriesPrevistos);
                dataset.addSeries(seriesRenovados);
                dataset.addSeries(seriesAgendados);
                dataset.addSeries(seriesEspontaneos);

                //
                semana = 1;
                for (PeriodoDatas periodoRenovacao : lista) {
                    memoriaCalculoGrafico += semana + ". De "
                            + Uteis.getData(periodoRenovacao.getDataInicio())
                            + " a " + Uteis.getData(periodoRenovacao.getDataFim())
                            + "     <p style='padding-left: 13px;'>  Prev: " + periodoRenovacao.getPrev() + " Renov:" + periodoRenovacao.getRenov()
                            + "         Agend: " + periodoRenovacao.getAgend()
                            + "         Espont: " + periodoRenovacao.getEspont()
                            + "</p><br/>";
                    semana++;


                }


                return dataset;
            }

            case PREVISAO_RENOVACAO_MESES_FUTUROS: {
                memoriaCalculoGrafico += getCabecalhoHintGrafico();

                List<PeriodoDatas> lista = new ArrayList<PeriodoDatas>();
                Date dataInicio = Uteis.obterPrimeiroDiaMes(Calendario.hoje());

                //inicializa a lista de periodos mensais: 5 proximos meses
                for (int i = 1; i <= 5; i++) {
                    dataInicio = Uteis.somarCampoData(dataInicio, Calendar.MONTH, 1);
                    Date dataFim = (Date) dataInicio.clone();
                    dataFim = Uteis.obterUltimoDiaMesUltimaHora(dataFim);
                    lista.add(new PeriodoDatas(dataInicio, dataFim));
                }

                final XYSeries seriesPrevistos = new XYSeries(this.indicador.getDescricao());

                int prev;
                for (PeriodoDatas periodoRenovacao : lista) {
                    prev = obterPrevistosRenovar(empresa,
                            periodoRenovacao.getDataInicio(),
                            periodoRenovacao.getDataFim(), condicaoColaboradores);
                    Calendar cal = (Calendar) Calendar.getInstance().clone();
                    cal.setTime(periodoRenovacao.getDataInicio());
                    periodoRenovacao.setPrev(prev);
                    seriesPrevistos.add(cal.get(Calendar.MONTH) + 1, prev);
                }

                for (PeriodoDatas periodoRenovacao : lista) {
                    memoriaCalculoGrafico += "<p>"
                            +Uteis.getMesReferencia(periodoRenovacao.getDataInicio()) + "/"
                            + Uteis.getAnoData(periodoRenovacao.getDataInicio())
                            + "    Prev: " + periodoRenovacao.getPrev()
                            + "</p><br/>";
                }

                final XYSeriesCollection dataset = new XYSeriesCollection();
                dataset.addSeries(seriesPrevistos);

                return dataset;

            }

        }
        return null;

    }

    private String getCabecalhoHintGrafico() {
        return String.format("<center><b>%s</b><br/><b>%s</b><br/></center>",
                this.getTipoBox().getNome(),
                this.getIndicador().getDescricao());
    }

    public void chartXY() throws Exception {
        SeriesDataset ds = createDataset(this.indicador.prepararCondicaoConsultorContrato(colaboradoresSelecionados));
        this.chart = this.indicador.createChart(ds);
    }
}

/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package negocio.modulos.smartbox;

import java.io.Serializable;
import java.sql.Connection;
import java.sql.ResultSet;

import negocio.comuns.utilitarias.Uteis;
import negocio.facade.jdbc.arquitetura.SuperFacadeJDBC;

/**
 *
 * <AUTHOR>
 */
public class SmartBoxThreadCalculator extends Thread implements Serializable {

    private transient Connection con;
    private boolean executandoCalculo = false;
    private TotalPorIndicadorTO objetoCalculado = null;
    private Integer empresa = 0;

    public SmartBoxThreadCalculator(Connection con, TotalPorIndicadorTO objetoCalculado, Integer empresa) {
        this.con = con;
        this.objetoCalculado = objetoCalculado;
        this.empresa = empresa;
    }

    public boolean isExecutandoCalculo() {
        return executandoCalculo;
    }

    public void setExecutandoCalculo(boolean executandoCalculo) {
        this.executandoCalculo = executandoCalculo;
    }

    @Override
    public void run() {
//            long l1 = new Date().getTime();
        try {
            executandoCalculo = true;
            objetoCalculado.getListaClientes().clear();
            objetoCalculado.getListaContratos().clear();
            objetoCalculado.setTotalLista(0);

            if (objetoCalculado != null) {
                try {
                    String sql = objetoCalculado.getIndicador().prepararSQLCount(
                            objetoCalculado.getTipoBox(),
                            this.empresa, objetoCalculado.getColaboradoresSelecionados());
                    if (!sql.isEmpty()) {
                        ResultSet rs = SuperFacadeJDBC.criarConsulta(sql, con);
                        if (rs.next()) {
                            int cont = rs.getInt("cont");
                            objetoCalculado.setTotalLista(cont);
                        }
                    }
                    if (objetoCalculado.getIndicador().getFormatoMidia().equals("grafico")) {
                        objetoCalculado.chartXY();
                    }


//                        long l2 = new Date().getTime();
//                        long l = l2 - l1;
//                        Uteis.logar(null, " Processou " + l + "ms " + this.getName());
                } catch (Exception ex) {
                    Uteis.logar(null, "Erro ao iniciar " + this.getName() + " -> " + ex.getMessage());
                }
            }
        } finally {
            executandoCalculo = false;
        }

    }
}

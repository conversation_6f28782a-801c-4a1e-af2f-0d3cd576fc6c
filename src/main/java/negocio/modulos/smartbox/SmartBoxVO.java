/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package negocio.modulos.smartbox;

import br.com.pactosolucoes.comuns.util.Formatador;
import java.util.Calendar;
import java.util.List;
import negocio.comuns.arquitetura.SuperVO;
import negocio.comuns.utilitarias.Calendario;
import negocio.comuns.utilitarias.Uteis;
import negocio.facade.jdbc.contrato.Contrato;
import negocio.modulos.smartbox.enumerador.TendenciaBoxEnum;
import negocio.modulos.smartbox.enumerador.TipoBoxEnum;

/**
 *
 * <AUTHOR>
 */
public class SmartBoxVO extends SuperVO {

    private TipoBoxEnum tipo = TipoBoxEnum.MENSAL;
    private List<TotalPorIndicadorTO> listaIndicadores;
    private Integer totalContratosAtivosDesteTipo = 0;
    private Integer totalContratosAtivosDesteTipoMesAnterior = 0;
    private Integer totalContratosAtivosTodosTipos = 0;
    private Integer totalContratosAtivosTodosTiposMesAnterior = 0;
    private TendenciaBoxEnum tendencia = TendenciaBoxEnum.NEUTRO;
    private String descricaoTendencia;

    public TipoBoxEnum getTipo() {
        return tipo;
    }

    public void setTipo(TipoBoxEnum tipo) {
        this.tipo = tipo;
    }

    public List<TotalPorIndicadorTO> getListaIndicadores() {
        return listaIndicadores;
    }

    public void setListaIndicadores(List<TotalPorIndicadorTO> listaIndicadores) {
        this.listaIndicadores = listaIndicadores;
    }

    public Integer getTotalContratosAtivosDesteTipo() {
        return totalContratosAtivosDesteTipo;
    }

    public void setTotalContratosAtivosDesteTipo(Integer totalContratosAtivosDesteTipo) {
        this.totalContratosAtivosDesteTipo = totalContratosAtivosDesteTipo;
    }

    public Integer getTotalContratosAtivosTodosTipos() {
        return totalContratosAtivosTodosTipos;
    }

    public void setTotalContratosAtivosTodosTipos(Integer totalContratosAtivosTodosTipos) {
        this.totalContratosAtivosTodosTipos = totalContratosAtivosTodosTipos;
    }

    public Integer getTotalContratosAtivosDesteTipoMesAnterior() {
        return totalContratosAtivosDesteTipoMesAnterior;
    }

    public void setTotalContratosAtivosDesteTipoMesAnterior(Integer totalContratosAtivosDesteTipoMesAnterior) {
        this.totalContratosAtivosDesteTipoMesAnterior = totalContratosAtivosDesteTipoMesAnterior;
    }

    public TendenciaBoxEnum getTendencia() {
        return tendencia;
    }

    public void setTendencia(TendenciaBoxEnum tendencia) {
        this.tendencia = tendencia;
    }

    public Integer getTotalContratosAtivosTodosTiposMesAnterior() {
        return totalContratosAtivosTodosTiposMesAnterior;
    }

    public void setTotalContratosAtivosTodosTiposMesAnterior(Integer totalContratosAtivosTodosTiposMesAnterior) {
        this.totalContratosAtivosTodosTiposMesAnterior = totalContratosAtivosTodosTiposMesAnterior;
    }

    public String getDescricaoTendencia() {
        return descricaoTendencia;
    }

    public void setDescricaoTendencia(String descricaoTendencia) {
        this.descricaoTendencia = descricaoTendencia;
    }

    public void limparValores() {
        for (TotalPorIndicadorTO totalPorIndicadorTO : listaIndicadores) {
            totalPorIndicadorTO.getListaClientes().clear();
            totalPorIndicadorTO.getListaContratos().clear();
            totalPorIndicadorTO.setTotalLista(0);
        }
    }

    public void consultarQuantidadeDestaCaixa(int empresa) throws Exception {
        String hoje = Uteis.getDataFormatoBD(Calendario.hoje());
        String mesAnterior = Uteis.getDataFormatoBD(Uteis.somarCampoData(
                Calendario.hoje(), Calendar.MONTH, -1));
        String condicaoEmpresa = empresa == 0 ? "" : " and c.empresa = "
                + String.valueOf(empresa) + " ";
        totalContratosAtivosDesteTipoMesAnterior = getFacade().getContrato().contar(
                String.format(Contrato.sqlAtivosContar,
                new Object[]{
                        (" and c.empresa = " + empresa),
                    mesAnterior,
                    mesAnterior,
                    mesAnterior,
                    mesAnterior,
                        mesAnterior,
                    mesAnterior
                })
                + condicaoEmpresa
                + this.getTipo().getCondicaoDuracaoParaContrato());
        //
        totalContratosAtivosDesteTipo = getFacade().getContrato().contar(
                String.format(Contrato.sqlAtivosContar,
                new Object[]{
                        (" and c.empresa = " + empresa),
                    hoje,
                    hoje,
                    hoje,
                    hoje,
                        hoje,
                    hoje
                })
                + condicaoEmpresa
                + this.getTipo().getCondicaoDuracaoParaContrato());

    }

    public Double getPorcentagemContratosAtivosDesteTipoEmRelacaoAoTodoMesAnteriorDouble() {
        if (totalContratosAtivosDesteTipoMesAnterior.intValue() > 0 && totalContratosAtivosTodosTiposMesAnterior.intValue() > 0) {
            double umaParte = totalContratosAtivosDesteTipoMesAnterior;
            double todo = totalContratosAtivosTodosTiposMesAnterior;
            return (umaParte / todo);
        } else {
            return 0.0;
        }
    }

    public Double getPorcentagemContratosAtivosDesteTipoEmRelacaoAoTodoDouble() {
        if (totalContratosAtivosDesteTipo.intValue() > 0 && totalContratosAtivosTodosTipos.intValue() > 0) {
            double umaParte = totalContratosAtivosDesteTipo;
            double todo = totalContratosAtivosTodosTipos;
            return (umaParte / todo);
        } else {
            return 0.0;
        }
    }

    public String getPorcentagemContratosAtivosDesteTipoEmRelacaoAoTodo() {

        return Formatador.formatarValorPercentual(
                this.getPorcentagemContratosAtivosDesteTipoEmRelacaoAoTodoDouble(), 2);

    }

    /**
     * Verifica se um numero esta perto de outro baseando-se numa tolerancia.
     * Caso a diferença entre eles seja maior do que a tolerancia sera considerado 'distante' -> false, senão 'proximo' -> true.
     * @param um
     * @param dois
     * @param toleranciaParaProximo
     * @return 
     */
    private boolean estaProximoComTolerancia(double um, double dois) {
        double tolerancia = 0.02;//2 pontos percentuais
        return (Math.abs(um - dois) <= tolerancia);
    }

    public TendenciaBoxEnum calcularTendencia() {
        double valorDesejado = (this.getTipo().getPorcentagemEsperada() / 100);
        double valorAnterior = (this.getPorcentagemContratosAtivosDesteTipoEmRelacaoAoTodoMesAnteriorDouble());
        double valorAtual = (this.getPorcentagemContratosAtivosDesteTipoEmRelacaoAoTodoDouble());
        Double valorDesejadoAbsoluto = 0.0;

        if (valorAtual != 0.0) {
            valorDesejadoAbsoluto = (totalContratosAtivosDesteTipo * this.getTipo().getPorcentagemEsperada()) / (valorAtual * 100);
        }

        descricaoTendencia = String.format("Valor anterior: %s (%s)<br/>Valor atual: %s (%s)<br/>Valor desejado: %s (%s)<br/>",
                new Object[]{
                    Formatador.formatarValorPercentual(valorAnterior, 2),
                    totalContratosAtivosDesteTipoMesAnterior,
                    Formatador.formatarValorPercentual(valorAtual, 2),
                    totalContratosAtivosDesteTipo,
                    Formatador.formatarValorPercentual(valorDesejado, 2),
                    valorDesejadoAbsoluto.intValue()
                });
        /**
         * Caso 1: Se Verde e para Baixo indica que: O valor está menor que o mês
         * anterior e que está mais próximo do valor desejado;
         */
        if (valorAtual < valorAnterior && estaProximoComTolerancia(valorDesejado, valorAtual)) {
            return TendenciaBoxEnum.POSITIVA_DECRESCENTE;
        }
        /**
         * Caso 2: Se Verde e para Cima indica que: O valor está maior que o mês
         * anterior e está mais próximo do valor desejado;
         */
        if (valorAtual > valorAnterior && estaProximoComTolerancia(valorDesejado, valorAtual)) {
            return TendenciaBoxEnum.POSITIVA_CRESCENTE;
        }

        /**
         * Caso 3: Se Verde e no Meio indica que: Não houve alteração em relação ao
         * mês anterior, porém, o valor está igual ao valor desejado.
         */
        if (valorAtual == valorAnterior && estaProximoComTolerancia(valorDesejado, valorAtual)) {
            return TendenciaBoxEnum.POSITIVA_NEUTRO;
        }
        /**
         * Caso 4: Se Vermelha e para Baixo: O valor está menor que o mês anterior e
         * que está mais distante do valor desejado;
         */
        if (valorAtual < valorAnterior && !estaProximoComTolerancia(valorDesejado, valorAtual)) {
            return TendenciaBoxEnum.NEGATIVA_DECRESCENTE;
        }
        /**
         * Caso 5: Se Vermelha e para Cima: O valor está maior que o mês anterior e que
         * está mais distante do valor desejado;
         */
        if (valorAtual > valorAnterior && !estaProximoComTolerancia(valorDesejado, valorAtual)) {
            return TendenciaBoxEnum.NEGATIVA_CRESCENTE;
        }
        /**
         * Caso 6: Se Amarela e no Meio indica que: Não houve alteração em relação ao
         * mês anterior, porém, o valor está diferente do valor desejado. Se trata do 'resto'.;
         */
        return TendenciaBoxEnum.POSITIVA_NEUTRO;
    }
}

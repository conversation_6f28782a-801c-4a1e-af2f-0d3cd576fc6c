/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package negocio.modulos.smartbox.enumerador;

import java.util.ArrayList;
import java.util.List;

/**
 *
 * <AUTHOR>
 */
public enum TipoBoxEnum {

    MENSAL(1, "Mensal", "images/box/mensal.png", 10.0),
    TRIMESTRAL(3, "Trimestral", "images/box/trimestral.png", 15.0),
    SEMESTRAL(6, "Semestral", "images/box/semestral.png", 30.0),
    ANUAL(12, "Anual", "images/box/anual.png", 30.0),
    OUTROS(-1, "OUTROS", "images/box/outros.png", 40.0);
    private Integer duracao;
    private String nome;
    private String imagem;
    private Double porcentagemEsperada;

    TipoBoxEnum(Integer duracao, String nome, String imagem, Double porcentagemEsperada) {
        this.duracao = duracao;
        this.nome = nome;
        this.imagem = imagem;
        this.porcentagemEsperada = porcentagemEsperada;
    }

    public Integer getDuracao() {
        return duracao;
    }

    public void setDuracao(Integer duracao) {
        this.duracao = duracao;
    }

    public String getNome() {
        return nome;
    }

    public void setNome(String nome) {
        this.nome = nome;
    }

    public String getImagem() {
        return imagem;
    }

    public void setImagem(String imagem) {
        this.imagem = imagem;
    }

    public Double getPorcentagemEsperada() {
        return porcentagemEsperada;
    }

    public void setPorcentagemEsperada(Double porcentagemEsperada) {
        this.porcentagemEsperada = porcentagemEsperada;
    }

    public static List getListTipoBox() {
        List temp = new ArrayList<TipoBoxEnum>();
        for (int i = 0; i < TipoBoxEnum.values().length; i++) {
            TipoBoxEnum obj = TipoBoxEnum.values()[i];
            temp.add(obj);
        }
        return temp;
    }

    private String prepararCondicaoDuracoes() {

        String codigosDuracoes = this.duracao.toString();

        if (duracao == -1) {//outros
            TipoBoxEnum[] tipos = this.values();
            codigosDuracoes = "";
            for (int i = 0; i < tipos.length; i++) {
                TipoBoxEnum tipoBoxEnum = tipos[i];
                codigosDuracoes += i + 1 == tipos.length
                        ? tipoBoxEnum.getDuracao() : tipoBoxEnum.getDuracao() + ",";
            }


        }
        return codigosDuracoes;
    }

    public String getCondicaoDuracaoParaContrato() {
        String codigosDuracoes = prepararCondicaoDuracoes();

        if (duracao == -1) {//outros
            return " and c.codigo in (select contrato from contratoduracao where numeromeses not in (" + codigosDuracoes + "))";
        } else {
            return " and c.codigo in (select contrato from contratoduracao where numeromeses = " + codigosDuracoes + ") ";
        }
    }

    public String getCondicaoDuracaoParaCliente() {
        String codigosDuracoes = prepararCondicaoDuracoes();

        if (duracao == -1) {//outros
            return " and cli.pessoa in (select pessoa from contrato c where c.codigo in (select contrato from contratoduracao where numeromeses not in (" + codigosDuracoes + "))) ";
        } else {
            return " and cli.pessoa in (select pessoa from contrato c where c.codigo in (select contrato from contratoduracao where numeromeses = " + codigosDuracoes + ")) ";
        }
    }

    public String getCondicaoDuracaoParaClienteAtivos() {
        String codigosDuracoes = prepararCondicaoDuracoes();

        if (duracao == -1) {//outros
            return " and cli.pessoa in (select pessoa from contrato c where c.situacao = 'AT' and c.codigo in (select contrato from contratoduracao where numeromeses not in (" + codigosDuracoes + "))) ";
        } else {
            return " and cli.pessoa in (select pessoa from contrato c where c.situacao = 'AT' and c.codigo in (select contrato from contratoduracao where numeromeses = " + codigosDuracoes + ")) ";
        }
    }
}

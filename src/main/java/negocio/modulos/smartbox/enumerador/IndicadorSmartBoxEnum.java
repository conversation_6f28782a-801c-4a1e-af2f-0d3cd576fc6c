/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package negocio.modulos.smartbox.enumerador;

import java.awt.Color;
import java.awt.image.BufferedImage;
import java.io.IOException;
import java.util.ArrayList;
import java.util.Date;
import java.util.Iterator;
import java.util.List;
import negocio.comuns.basico.ColaboradorVO;
import negocio.comuns.utilitarias.Calendario;
import negocio.comuns.utilitarias.Uteis;
import negocio.facade.jdbc.basico.Cliente;
import negocio.facade.jdbc.contrato.Contrato;
import org.jfree.chart.ChartFactory;
import org.jfree.chart.JFreeChart;
import org.jfree.chart.axis.NumberAxis;
import org.jfree.chart.block.BlockBorder;
import org.jfree.chart.encoders.EncoderUtil;
import org.jfree.chart.encoders.ImageFormat;
import org.jfree.chart.plot.PlotOrientation;
import org.jfree.chart.plot.XYPlot;
import org.jfree.chart.renderer.xy.XYBarRenderer;
import org.jfree.chart.renderer.xy.XYLineAndShapeRenderer;
import org.jfree.data.general.Dataset;
import org.jfree.data.general.SeriesDataset;
import org.jfree.data.xy.IntervalXYDataset;
import org.jfree.data.xy.XYDataset;
import org.jfree.ui.RectangleInsets;

/**
 *
 * <AUTHOR>
 */
public enum IndicadorSmartBoxEnum {

    CONTRATOS_VENCEM_MES_ATUAL(1, "Vencem no mês atual", Contrato.class, "images/btn_ContratosVencemMesAtual_VERDE.png", "totalizador", ""),
    CONTRATOS_VENCIDOS(2, "Contratos estão vencidos", Contrato.class, "images/btn_ContratosEstaoVencidos_VERDE.png", "totalizador", ""),
    CLIENTES_EM_RISCO(3, "Estão no Grupo de Risco", Cliente.class, "images/btn_EstaonoGrupoRisco_VERDE.png", "totalizador", ""),
    CLIENTES_SEM_CONTATO(4, "Clientes sem Contato", Cliente.class, "images/btn_ClientessemContato_VERDE.png", "totalizador", ""),
    CONTRATOS_PELA_METADE(5, "Contratos na metade", Contrato.class, "images/btn_ContratonaMetade_VERDE.png", "totalizador", ""),
    PREVISAO_RENOVACAO(6, "Histórico de Renovação", Dataset.class, "", "grafico", "Últimas 4 Semanas"),
    PREVISAO_RENOVACAO_MESES_FUTUROS(7, "Previsão de Renovação", Dataset.class, "", "grafico", "Próximos 5 meses");
    private Integer id;
    private String descricao;
    private Class entidade;
    private String imagem;
    private String formatoMidia;
    private String subTitulo;
    private Integer maxWidthToGraph = 0;
    public static Integer idSelecionada = 0;

    IndicadorSmartBoxEnum(Integer id, String descricao, Class entidade, String imagem, String formatoMidia, String subTitulo) {
        this.id = id;
        this.descricao = descricao;
        this.entidade = entidade;
        this.imagem = imagem;
        this.formatoMidia = formatoMidia;
        this.subTitulo = subTitulo;
    }

    public String getDescricao() {
        return descricao;
    }

    public void setDescricao(String descricao) {
        this.descricao = descricao;
    }

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public Class getEntidade() {
        return entidade;
    }

    public void setEntidade(Class entidade) {
        this.entidade = entidade;
    }

    public void setImagem(String imagem) {
        this.imagem = imagem;
    }

    public String getImagem() {
        String imagemMostrada = imagem;
        if (idSelecionada != this.getId()) {
            imagemMostrada = imagemMostrada.replaceAll("_VERDE", "");
        }
        return imagemMostrada;
    }

    public String getFormatoMidia() {
        return formatoMidia;
    }

    public void setFormatoMidia(String formatoMidia) {
        this.formatoMidia = formatoMidia;
    }

    public String getSubTitulo() {
        return subTitulo;
    }

    public void setSubTitulo(String subTitulo) {
        this.subTitulo = subTitulo;
    }

    public Integer getMaxWidthToGraph() {
        return maxWidthToGraph;
    }

    public void setMaxWidthToGraph(Integer maxWidthToGraph) {
        this.maxWidthToGraph = maxWidthToGraph;
    }

    public static List getListIndicadorSmartBox() {
        List temp = new ArrayList<IndicadorSmartBoxEnum>();
        for (int i = 0; i < IndicadorSmartBoxEnum.values().length; i++) {
            IndicadorSmartBoxEnum obj = IndicadorSmartBoxEnum.values()[i];
            temp.add(obj);
        }
        return temp;
    }

    public String prepararCondicaoConsultorContrato(List<ColaboradorVO> listaColaboradores) {
        String condicao = "";
        if (!listaColaboradores.isEmpty()) {

            String sIn = "";
            for (Iterator<ColaboradorVO> it = listaColaboradores.iterator(); it.hasNext();) {
                ColaboradorVO colaboradorVO = it.next();
                sIn += colaboradorVO.getCodigo();
                if (it.hasNext()) {
                    sIn += ",";
                }
            }
//            condicao = String.format(" c.consultor in (%s) ", new Object[]{sIn});
            condicao = String.format(" c.pessoa in "
                    + "(select pessoa from cliente where codigo in "
                    + "(select cliente from vinculo where colaborador in (%s))) ",
                    new Object[]{sIn});

        }
        return condicao;
    }

    private String prepararCondicaoVinculo(List<ColaboradorVO> listaColaboradores) {
        String condicao = "";
        if (!listaColaboradores.isEmpty()) {

            String sIn = "";
            for (Iterator<ColaboradorVO> it = listaColaboradores.iterator(); it.hasNext();) {
                ColaboradorVO colaboradorVO = it.next();
                sIn += colaboradorVO.getCodigo();
                if (it.hasNext()) {
                    sIn += ",";
                }
            }
            condicao = String.format(" cli.codigo in "
                    + "(select cliente from vinculo where colaborador in (%s)) ",
                    new Object[]{sIn});
        }
        return condicao;
    }

    private String getSqlConsulta(TipoBoxEnum tipoBox, String clausulaFrom,
            int empresa, List<ColaboradorVO> listaColaboradores)
            throws Exception {
        String sql = "";
        switch (this) {
            case CONTRATOS_VENCIDOS: {
                Date dataInicio = Uteis.obterPrimeiroDiaMes(Calendario.hoje());
                Date dataFim = Calendario.hoje();
                String condicaoColab = prepararCondicaoConsultorContrato(listaColaboradores);
                sql = String.format(Contrato.sqlVencidosFinalMes, new Object[]{
                            clausulaFrom,
                        (" and c.empresa = " + empresa),
                            Uteis.getDataFormatoBD(dataFim),
                            Uteis.getDataFormatoBD(dataInicio),
                            Uteis.getDataFormatoBD(dataFim),
                            Uteis.getDataFormatoBD(dataInicio),
                            Uteis.getDataFormatoBD(dataFim)})
                        + tipoBox.getCondicaoDuracaoParaContrato()
                        + (condicaoColab.isEmpty() ? "" : " and " + condicaoColab);
                break;                
            }
            case CONTRATOS_VENCEM_MES_ATUAL: {
                Date dataInicio = Uteis.obterPrimeiroDiaMes(Calendario.hoje());
                Date dataFim = Uteis.obterUltimoDiaMesUltimaHora(Calendario.hoje());
                String condicaoColab = prepararCondicaoConsultorContrato(listaColaboradores);
                sql = String.format(Contrato.sqlVencidosAteAoFinalMes, new Object[]{
                            clausulaFrom,
                            empresa,
                            Uteis.getDataFormatoBD(dataInicio),
                            Uteis.getDataFormatoBD(dataFim)})
                        + tipoBox.getCondicaoDuracaoParaContrato()
                        + (condicaoColab.isEmpty() ? "" : " and " + condicaoColab);
                break;
            }
            case CLIENTES_EM_RISCO: {
                String condicaoColab = prepararCondicaoVinculo(listaColaboradores);
                sql = String.format(Cliente.sqlClientesEmRisco, new Object[]{
                            clausulaFrom,
                            empresa})
                        + tipoBox.getCondicaoDuracaoParaClienteAtivos()
                        + (condicaoColab.isEmpty() ? "" : " and " + condicaoColab);
                break;
            }
            case CLIENTES_SEM_CONTATO: {
                String condicaoColab = prepararCondicaoVinculo(listaColaboradores);
                sql = String.format(Cliente.sqlClientesSemContato, new Object[]{
                            clausulaFrom,
                            empresa})
                        + tipoBox.getCondicaoDuracaoParaClienteAtivos()
                        + (condicaoColab.isEmpty() ? "" : " and " + condicaoColab);
                break;
            }
            case CONTRATOS_PELA_METADE: {
                Date dataFim = Uteis.obterUltimoDiaMes(Calendario.hoje());
                String condicaoColab = prepararCondicaoConsultorContrato(listaColaboradores);
                sql = String.format(Contrato.sqlContratosMetade, new Object[]{
                            clausulaFrom,
                            empresa,
                            Uteis.getDataFormatoBD(dataFim),
                            Uteis.getDataFormatoBD(dataFim),
                            Uteis.getDataFormatoBD(dataFim)})
                        + tipoBox.getCondicaoDuracaoParaContrato()
                        + (condicaoColab.isEmpty() ? "" : " and " + condicaoColab);
                break;
            }
        }
        return sql;

    }

    public String prepararSQLSelect(TipoBoxEnum tipoBox, int empresa,
            List<ColaboradorVO> listaColaboradores) throws Exception {
        return this.getSqlConsulta(tipoBox, "*", empresa, listaColaboradores);
    }

    public String prepararSQLCount(TipoBoxEnum tipoBox, int empresa,
            List<ColaboradorVO> listaColaboradores) throws Exception {
        return this.getSqlConsulta(tipoBox, "count(*) as cont", empresa, listaColaboradores);
    }

    public byte[] createChart(final SeriesDataset dataset) throws IOException {
        JFreeChart chart = null;
        switch (this) {
            case PREVISAO_RENOVACAO: {

                chart = ChartFactory.createXYLineChart(
                        null, // Titulo
                        this.getSubTitulo(), // x axis label
                        null, // y axis label
                        (XYDataset) dataset, //dados
                        PlotOrientation.VERTICAL,
                        true, // incluir legenda
                        true, // tooltips
                        false // urls
                        );

                chart.setBackgroundPaint(Color.white);

                final XYPlot plot = chart.getXYPlot();
                plot.setBackgroundPaint(Color.white);
                plot.setAxisOffset(new RectangleInsets(1.0, 1.0, 1.0, 1.0));


                final XYLineAndShapeRenderer renderer = new XYLineAndShapeRenderer();
                renderer.setSeriesPaint(0, new Color(95, 160, 215));
                renderer.setSeriesPaint(1, new Color(237, 124, 0));
                renderer.setSeriesPaint(2, new Color(87, 201, 88));
                renderer.setSeriesPaint(3, new Color(250, 222, 62));
                plot.setRenderer(renderer);

                // change the auto tick unit selection to integer units only...
                final NumberAxis rangeAxis = (NumberAxis) plot.getRangeAxis();
                rangeAxis.setStandardTickUnits(NumberAxis.createIntegerTickUnits());
                rangeAxis.setAutoRange(true);
                break;

            }

            case PREVISAO_RENOVACAO_MESES_FUTUROS: {

                chart = ChartFactory.createXYBarChart(
                        null, // Titulo
                        this.getSubTitulo(), // x axis label
                        false,
                        null, // y axis label
                        (IntervalXYDataset) dataset, //dados
                        PlotOrientation.VERTICAL,
                        true, // incluir legenda
                        true, // tooltips
                        false // urls
                        );

                chart.setBackgroundPaint(Color.white);

                final XYPlot plot = chart.getXYPlot();
                plot.setBackgroundPaint(Color.white);
                plot.setAxisOffset(new RectangleInsets(1.0, 1.0, 1.0, 1.0));
                final XYBarRenderer renderer = new XYBarRenderer();

                renderer.setMargin(0.2);

                renderer.setSeriesPaint(0, new Color(188, 239, 155));//RGB

                plot.setRenderer(renderer);

                final NumberAxis rangeAxis = (NumberAxis) plot.getRangeAxis();
                rangeAxis.setStandardTickUnits(NumberAxis.createIntegerTickUnits());
                rangeAxis.setAutoRange(true);
                break;
            }
        }


        chart.getLegend().setBorder(BlockBorder.NONE);


        return createChartByteArray(chart);
    }

    private byte[] createChartByteArray(JFreeChart chart) throws IOException {
        BufferedImage buf = chart.createBufferedImage(maxWidthToGraph.intValue() != 0 ? maxWidthToGraph : 176,
                184, BufferedImage.TYPE_INT_RGB, null);
        byte[] b = EncoderUtil.encode(buf, ImageFormat.PNG);
        buf.flush();
        return b;
    }
}

/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package negocio.modulos.smartbox.enumerador;

/**
 *
 * <AUTHOR>
 */
public enum TendenciaBoxEnum {
    
    POSITIVA_CRESCENTE(1, "images/setas/verde_cima.png"),
    POSITIVA_DECRESCENTE(2, "images/setas/verde_baixo.png"),
    POSITIVA_NEUTRO(3, "images/setas/verde_meio.png"),
    NEGATIVA_CRESCENTE(4, "images/setas/vermelho_cima.png"),
    NEGATIVA_DECRESCENTE(5, "images/setas/vermelho_baixo.png"),
    NEUTRO(6, "images/setas/amarelo_meio.png");

    private Integer id;
    private String imagem;
    

    TendenciaBoxEnum(Integer id, String imagem) {
        this.id = id;
        this.imagem = imagem;
    }

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getImagem() {
        return imagem;
    }

    public void setImagem(String imagem) {
        this.imagem = imagem;
    }
}

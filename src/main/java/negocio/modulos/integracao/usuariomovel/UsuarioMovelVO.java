/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package negocio.modulos.integracao.usuariomovel;

import annotations.arquitetura.ChaveEstrangeira;
import annotations.arquitetura.ChavePrimaria;
import annotations.arquitetura.FKJson;
import annotations.arquitetura.NaoControlarLogAlteracao;
import br.com.pactosolucoes.enumeradores.TipoColaboradorEnum;
import negocio.comuns.arquitetura.SuperVO;
import negocio.comuns.arquitetura.UsuarioEmailVO;
import negocio.comuns.basico.ClienteVO;
import negocio.comuns.basico.ColaboradorVO;
import negocio.comuns.basico.VinculoVO;
import negocio.comuns.utilitarias.ConsistirException;
import negocio.comuns.utilitarias.UteisValidacao;
import servicos.integracao.treino.StatusEnum;
import servicos.integracao.treino.TipoUsuarioEnum;
import servicos.integracao.treino.UsuarioZW;
import servicos.integracao.treino.dto.UsuarioZWDTO;

/**
 *
 * <AUTHOR>
 */
public class UsuarioMovelVO extends SuperVO {

    @ChavePrimaria
    private Integer codigo = 0;
    private String nome = "";
    @ChaveEstrangeira
    @FKJson
    private ColaboradorVO colaborador = new ColaboradorVO();
    @ChaveEstrangeira
    @FKJson
    private ClienteVO cliente = new ClienteVO();
    private String senha = "";
    @NaoControlarLogAlteracao
    private String senhaAnterior = "";
    private boolean ativo = true;
    private Integer usuarioZW = 0;
    private Integer empresa = 0;
    private String origem = "";
    private Integer indicado;
    @NaoControlarLogAlteracao
    private String key;
    private String cpf;

    private Boolean senhaEncriptada;
    @ChaveEstrangeira
    private UsuarioEmailVO usuarioEmailVO;
    @NaoControlarLogAlteracao
    private Boolean propagarExcessao = false;
    private Integer usuarioTW = 0;

    @NaoControlarLogAlteracao
    private Integer perfilTw;

    /**
     * Construtor padrão da classe <code>UsuarioMovel</code>.
     * Cria uma nova instância desta entidade, inicializando automaticamente seus atributos (Classe VO).
     */
    public UsuarioMovelVO() {
        super();
    }

    /**
     * Operação responsável por validar os dados de um objeto da classe <code>UsuarioMovelVO</code>.
     * Todos os tipos de consistência de dados são e devem ser implementadas neste método.
     * São validações típicas: verificação de campos obrigatórios, verificação de valores válidos para os atributos.
     * @exception ConsistirException Se uma inconsistência for encontrada aumaticamente é gerada uma exceção descrevendo
     *                               o atributo e o erro ocorrido.
     */
    public static void validarDados(UsuarioMovelVO obj,boolean validarSenha) throws ConsistirException {
        if (obj.getValidarDados()) {
            if (UteisValidacao.emptyString(obj.getNome())) {
                throw new ConsistirException("O campo NOME (UsuarioMovel) deve ser informado.");
            }
            if (UteisValidacao.emptyString(obj.getSenha()) && validarSenha) {
                throw new ConsistirException("O campo Senha (UsuarioMovel) deve ser informado.");
            }
            if (obj.getColaborador() == null && obj.getCliente() == null && obj.getIndicado() == null) {
                throw new ConsistirException("O campo Colaborador ou Cliente (UsuarioMovel) deve ser informado.");
            } else if (UteisValidacao.emptyNumber(obj.getColaborador().getCodigo())
                    && UteisValidacao.emptyNumber(obj.getCliente().getCodigo())
                    && UteisValidacao.emptyNumber(obj.getIndicado())) {
                throw new ConsistirException("O campo Colaborador ou Cliente (UsuarioMovel) deve ser informado.");
            }
        }
    }
    public static void validarDados(UsuarioMovelVO obj) throws ConsistirException {
        validarDados(obj,true);
    }
    /**
     * Operação reponsável por realizar o UpperCase dos atributos do tipo String.
     */
    public void realizarUpperCaseDados() {
        setNome(getNome().toLowerCase());
    }

    public String getNome() {
        return (nome);
    }

    public void setNome(String nome) {
        this.nome = nome;
    }

    @Override
    public Integer getCodigo() {
        return (codigo);
    }

    @Override
    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public ClienteVO getCliente() {
        return cliente;
    }

    public void setCliente(ClienteVO cliente) {
        this.cliente = cliente;
    }

    public ColaboradorVO getColaborador() {
        return colaborador;
    }

    public void setColaborador(ColaboradorVO colaborador) {
        this.colaborador = colaborador;
    }

    public String getSenha() {
        return senha;
    }

    public void setSenha(String senha) {
        this.senha = senha;
    }

    public String getSenhaAnterior() {
        return senhaAnterior;
    }

    public void setSenhaAnterior(String senhaAnterior) {
        this.senhaAnterior = senhaAnterior;
    }

    public boolean isAtivo() {
        return ativo;
    }

    public void setAtivo(boolean ativo) {
        this.ativo = ativo;
    }

    public Integer getUsuarioZW() {
        return usuarioZW;
    }

    public void setUsuarioZW(Integer usuarioZW) {
        this.usuarioZW = usuarioZW;
    }

    public Integer getEmpresa() {
        return empresa;
    }

    public void setEmpresa(Integer empresa) {
        this.empresa = empresa;
    }

    public UsuarioZW toUsuarioTreino() {
        UsuarioZW u = new UsuarioZW();
        u.setCliente(this.cliente.getCodigo() > 0 ? this.cliente.getSituacaoClienteSinteticoVO().toSinteticoZW(
                this.cliente.getPessoa().getTodosTelefones(),this.cliente.getPessoa().getTodosEmails()) : null);
        if (u.getCliente() != null) {
            for (VinculoVO vinculo : this.cliente.getVinculoVOs()) {
                if (vinculo.getTipoVinculo().equals(TipoColaboradorEnum.PROFESSOR_TREINO.getSigla())) {
                    u.getCliente().setCodigoColaboradorProfessor(vinculo.getColaborador().getCodigo());
                    break;
                }
            }
        }
        u.setIndicado(this.indicado);
        u.setCodigoExterno(this.codigo.toString());
        u.setNome(this.nome);
        u.setProfessor(this.colaborador.getCodigo() > 0 ? this.colaborador.toProfessorSintetico() : null);
        u.setSenha(this.senha);
        u.setStatus(this.ativo ? StatusEnum.ATIVO : StatusEnum.INATIVO);
        u.setUserName(this.nome);
        u.setTipo(u.getCliente() != null ? TipoUsuarioEnum.ALUNO
                : UteisValidacao.emptyNumber(this.indicado) ? TipoUsuarioEnum.PROFESSOR
                : TipoUsuarioEnum.CONVIDADO);
        if (this.colaborador.temTipoColaborador(TipoColaboradorEnum.COORDENADOR.getSigla())) {
            u.setTipo(TipoUsuarioEnum.COORDENADOR);
        } else if (this.colaborador.temTipoColaborador(TipoColaboradorEnum.CONSULTOR.getSigla())) {
            u.setTipo(TipoUsuarioEnum.CONSULTOR);
        }
        u.setUsuarioZW(this.usuarioZW);
        u.setEmpresaZW(this.empresa);
        u.setEmail(this.getUsuarioEmailVO().getEmail());
        u.setEmailVerificado(this.getUsuarioEmailVO().isVerificado());
        u.setPerfilTw(this.perfilTw);
        if (u.getCliente() != null) {
            u.setCpf(this.cliente.getPessoa().getCfp());
            u.setFotoKey(this.cliente.getPessoa().getFotoKey());
        } else if (u.getProfessor() != null) {
            u.setCpf(this.colaborador.getPessoa().getCfp());
            u.setFotoKey(this.colaborador.getPessoa().getFotoKey());
            u.getProfessor().setCodigoAcesso(this.colaborador.getCodAcesso());
        }
        return u;
    }

    public UsuarioZWDTO toUsuarioTreinoDTO() {
        UsuarioZWDTO u = new UsuarioZWDTO();
        u.setCliente(this.cliente.getCodigo() > 0 ? this.cliente.getSituacaoClienteSinteticoVO().toSinteticoZW(
                this.cliente.getPessoa().getTodosTelefones(),this.cliente.getPessoa().getTodosEmails()) : null);
        if (u.getCliente() != null) {
            for (VinculoVO vinculo : this.cliente.getVinculoVOs()) {
                if (vinculo.getTipoVinculo().equals(TipoColaboradorEnum.PROFESSOR_TREINO.getSigla())) {
                    u.getCliente().setCodigoColaboradorProfessor(vinculo.getColaborador().getCodigo());
                    break;
                }
            }
        }
        u.setIndicado(this.indicado);
        u.setCodigoExterno(this.codigo.toString());
        u.setNome(this.nome);
        u.setProfessor(this.colaborador.getCodigo() > 0 ? this.colaborador.toProfessorSintetico() : null);
        u.setSenha(this.senha);
        u.setStatus(this.ativo ? StatusEnum.ATIVO.name() : StatusEnum.INATIVO.name());
        u.setUserName(this.nome);
        u.setTipo(u.getCliente() != null ? TipoUsuarioEnum.ALUNO.name()
                : UteisValidacao.emptyNumber(this.indicado) ? TipoUsuarioEnum.PROFESSOR.name()
                : TipoUsuarioEnum.CONVIDADO.name());
        if (this.colaborador.temTipoColaborador(TipoColaboradorEnum.COORDENADOR.getSigla())) {
            u.setTipo(TipoUsuarioEnum.COORDENADOR.name());
        } else if (this.colaborador.temTipoColaborador(TipoColaboradorEnum.CONSULTOR.getSigla())) {
            u.setTipo(TipoUsuarioEnum.CONSULTOR.name());
        }
        u.setUsuarioZW(this.usuarioZW);
        u.setEmpresaZW(this.empresa);
        u.setEmail(this.getUsuarioEmailVO().getEmail());
        if (u.getCliente() != null) {
            u.setCpf(this.cliente.getPessoa().getCfp());
            u.setFotoKey(this.cliente.getPessoa().getFotoKey());
        } else if (u.getProfessor() != null) {
            u.setCpf(this.colaborador.getPessoa().getCfp());
            u.setFotoKey(this.colaborador.getPessoa().getFotoKey());
            u.getProfessor().setCodigoAcesso(this.colaborador.getCodAcesso());
        }
        return u;
    }

    public String getOrigem() {
        return origem;
    }

    public void setOrigem(String origem) {
        this.origem = origem;
    }

    public Boolean getSenhaEncriptada() {
        if (senhaEncriptada == null){
            senhaEncriptada = false;
        }
        return senhaEncriptada;
    }

    public void setSenhaEncriptada(Boolean senhaEncriptada) {
        this.senhaEncriptada = senhaEncriptada;
    }

    public Integer getIndicado() {
        return indicado;
    }

    public void setIndicado(Integer indicado) {
        this.indicado = indicado;
    }

    public String getKey() {
        return key;
    }

    public void setKey(String key) {
        this.key = key;
    }

    public String getCpf() {
        if (cpf == null) {
            cpf = "";
        }
        return cpf;
    }

    public void setCpf(String cpf) {
        this.cpf = cpf;
    }

    public UsuarioEmailVO getUsuarioEmailVO() {
        if(usuarioEmailVO == null){
            usuarioEmailVO = new UsuarioEmailVO();
        }
        return usuarioEmailVO;
    }

    public void setUsuarioEmailVO(UsuarioEmailVO usuarioEmailVO) {
        this.usuarioEmailVO = usuarioEmailVO;
    }

    public Boolean getPropagarExcessao() {
        return propagarExcessao;
    }

    public void setPropagarExcessao(Boolean propagarExcessao) {
        this.propagarExcessao = propagarExcessao;
    }

    public Integer getUsuarioTW() {
        return usuarioTW;
    }

    public void setUsuarioTW(Integer usuarioTW) {
        this.usuarioTW = usuarioTW;
    }

    public Integer getPerfilTw() {
        return perfilTw;
    }

    public void setPerfilTw(Integer perfilTw) {
        this.perfilTw = perfilTw;
    }
}

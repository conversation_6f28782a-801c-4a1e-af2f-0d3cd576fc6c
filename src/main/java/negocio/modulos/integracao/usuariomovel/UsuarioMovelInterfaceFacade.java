/*
 * To change this template, choose Too<PERSON> | Templates
 * and open the template in the editor.
 */
package negocio.modulos.integracao.usuariomovel;

import org.json.JSONArray;
import negocio.comuns.arquitetura.UsuarioVO;
import negocio.comuns.basico.ClienteVO;
import negocio.comuns.basico.ColaboradorVO;
import negocio.comuns.basico.EmpresaVO;
import negocio.comuns.crm.IndicadoVO;
import negocio.interfaces.basico.SuperInterface;

import java.util.List;

/**
 *
 * <AUTHOR>
 */
public interface UsuarioMovelInterfaceFacade extends SuperInterface {

    public UsuarioMovelVO novo() throws Exception;

    public void incluir(UsuarioMovelVO obj) throws Exception;

    public void alterar(UsuarioMovelVO obj) throws Exception;

    public void alterarSemSenha(UsuarioMovelVO obj) throws Exception;

    public void alterarSenha(Integer codigoUsuarioMovel, String senha, String lembreteSenha) throws Exception;

    public void alterarSenhaJaE<PERSON>riptada(Integer codigoUsuarioMovel, String senhaEncriptada) throws Exception;

    public void excluir(UsuarioMovelVO obj) throws Exception;

    public void excluir(ClienteVO clienteVO) throws Exception;

    public UsuarioMovelVO consultarPorChavePrimaria(Integer codigo, int nivelMontarDados) throws Exception;

    public List consultarPorCodigo(Integer valorConsulta, boolean controlarAcesso, int nivelMontarDados) throws Exception;

    public List consultarPorNome(String valorConsulta, boolean controlarAcesso, int nivelMontarDados) throws Exception;

    public void setIdEntidade(String aIdEntidade);

    public UsuarioMovelVO consultarPorColaborador(ColaboradorVO colaborador, int nivelMontarDados) throws Exception;

    public UsuarioMovelVO consultarPorCliente(ClienteVO cliente, int nivelMontarDados) throws Exception;
    public UsuarioMovelVO consultarPorCodigoCliente(Integer codigoCliente, int nivelMontarDados) throws Exception;

    public boolean consultarClienteTem(Integer codigoCliente) throws Exception;
    
    public boolean usuarioTreino(Integer usuario) throws Exception;
    
    public String obterUsuarioMesmoNome(String nome);

    public UsuarioMovelVO consultarPorUsuarioSenha(final String username, final String senha) throws Exception;

    public UsuarioMovelVO consultarPorCPFSenha(String cpf, final String senha) throws Exception;

    public UsuarioMovelVO consultarPorNome(final String nome, int nivelMontarDados) throws Exception;
      
    public List obterTodosColaboradores(boolean controlarAcesso, int nivelMontarDados) throws Exception;
    
    public Integer obterAlunosAtivosSemUsuarioMovel(final Integer empresa) throws Exception;
    
    public String obterCodsAlunosAtivosSemUsuarioMovel(final Integer empresa) throws Exception;
    
    public JSONArray obterListaAlunosAtivosForaTreino(final Integer empresa, String modalidadesFiltro) throws Exception;

    public JSONArray obterListaAlunosForaTreino(final Integer empresa) throws Exception;
    
    public JSONArray obterModalidadesAlunosAtivosForaTreino(final Integer empresa) throws Exception;
    
    public boolean temUsuarioMovel(Integer codigoCliente);
    
    public UsuarioMovelVO gerarUsuarioMovel(IndicadoVO indicado) throws Exception;

    public void gerarUsuarioMovelViaContextoZW(EmpresaVO empresaVO, ClienteVO clienteVO, String email, String nomePessoa, String cpf)throws Exception;

    public List<UsuarioMovelVO> consultarTodosPorOrigem(String origem, int nivelMontarDados)throws Exception;

    public UsuarioMovelVO consultarPorUsuario(UsuarioVO usuarioVO, int nivelMontarDados) throws Exception;

    public String gerarUsuarioMovelAluno(Integer codigoCliente, String key, String senha,  String username, String nomeApp,  String urlGooglePlay,  String urlItunes,final String chaveUrlMobile)throws Exception;

    public StringBuilder gerarCorpoEmailSenhaUsuarioMovel(
            final String key,
            final ClienteVO cliente, ColaboradorVO colaborador,
            final UsuarioMovelVO usuarioMovel,
            final String senha,
            final boolean novoAPP,
            final String nomeApp,
            final String urlGooglePlay,
            final String urlItunes, final String chaveUrlMobile) throws Exception;

    public void verificarEnvioAlunoParaTW(final String key, ClienteVO clienteVO) throws Exception;

    void verificarUsuarioMovel(final String key, ClienteVO clienteVO) throws Exception;

    void alterarUsuarioTW(UsuarioMovelVO usuarioMovelVO) throws Exception;

    Integer obterUsuarioTW(String key, Integer usuariozw, Integer empresazw);

    UsuarioMovelVO consultarPorUserName(String username) throws Exception;

    public UsuarioMovelVO consultarPorColaboradorDoUsuario(UsuarioVO usuarioVO, int nivelMontarDados) throws Exception;
}

/*
 * To change this template, choose Too<PERSON> | Templates
 * and open the template in the editor.
 */
package negocio.modulos.integracao.usuariomovel;

import br.com.pactosolucoes.comuns.util.FileUtilities;
import br.com.pactosolucoes.comuns.util.Formatador;
import br.com.pactosolucoes.comuns.util.JSFUtilities;
import br.com.pactosolucoes.enumeradores.OrigemSistemaEnum;
import negocio.comuns.arquitetura.UsuarioVO;
import negocio.comuns.basico.ClienteVO;
import negocio.comuns.basico.ColaboradorVO;
import negocio.comuns.basico.EmailVO;
import negocio.comuns.basico.EmpresaVO;
import negocio.comuns.crm.IndicadoVO;
import negocio.comuns.utilitarias.Calendario;
import negocio.comuns.utilitarias.ConsistirException;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisEmail;
import negocio.comuns.utilitarias.UteisValidacao;
import negocio.comuns.utilitarias.UtilReflection;
import negocio.facade.jdbc.arquitetura.SuperEntidade;
import negocio.facade.jdbc.arquitetura.UsuarioEmail;
import negocio.facade.jdbc.arquitetura.ZillyonWebFacade;
import negocio.facade.jdbc.basico.Cliente;
import negocio.facade.jdbc.basico.Colaborador;
import negocio.facade.jdbc.basico.Email;
import negocio.facade.jdbc.basico.Empresa;
import negocio.facade.jdbc.basico.Vinculo;
import negocio.facade.jdbc.basico.webservice.IntegracaoCadastros;
import negocio.oamd.EmpresaFinanceiroVO;
import org.json.JSONArray;
import org.json.JSONObject;
import relatorio.negocio.comuns.sad.SituacaoClienteSinteticoDWVO;
import relatorio.negocio.comuns.sad.SituacaoClienteSinteticoEnum;
import servicos.http.MetodoHttpEnum;
import servicos.http.RequestHttpService;
import servicos.http.RespostaHttpDTO;
import servicos.impl.oamd.OAMDService;
import servicos.integracao.TreinoWSConsumer;
import servicos.operacoes.midias.MidiaService;
import servicos.operacoes.midias.commons.MidiaEntidadeEnum;
import servicos.propriedades.PropsService;

import java.io.File;
import java.security.MessageDigest;
import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.Statement;
import java.text.DecimalFormat;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Random;
import java.util.UUID;
import java.util.logging.Level;
import java.util.logging.Logger;

/**
 *
 * <AUTHOR>
 */
/**
 * Classe de persistência que encapsula todas as operações de manipulação dos
 * dados da classe
 * <code>UsuarioMovelVO</code>. Responsável por implementar operações como
 * incluir, alterar, excluir e consultar pertinentes a classe
 * <code>UsuarioMovelVO</code>. Encapsula toda a interação com o banco de dados.
 *
 * @see UsuarioMovelVO
 * @see SuperEntidade
 */
public class UsuarioMovel extends SuperEntidade implements UsuarioMovelInterfaceFacade {

    public UsuarioMovel() throws Exception {
        super();
        setIdEntidade("Cliente");
    }

    public UsuarioMovel(Connection con) throws Exception {
        super(con);
        setIdEntidade("Cliente");
    }

    /**
     * Operação responsável por retornar um novo objeto da classe
     * <code>UsuarioMovelVO</code>.
     */
    @Override
    public UsuarioMovelVO novo() throws Exception {
        incluir(getIdEntidade());
        return new UsuarioMovelVO();
    }

    /**
     * Operação responsável por incluir no banco de dados um objeto da classe
     * <code>UsuarioMovelVO</code>. Primeiramente valida os dados (
     * <code>validarDados</code>) do objeto. Verifica a conexão com o banco de
     * dados e a permissão do usuário para realizar esta operacão na entidade.
     * Isto, através da operação
     * <code>incluir</code> da superclasse.
     *
     * @param obj Objeto da classe <code>UsuarioMovelVO</code> que será gravado
     *            no banco de dados.
     * @throws Exception Caso haja problemas de conexão, restrição de acesso
     *                   ou validação de dados.
     */
    @Override
    public void incluir(UsuarioMovelVO obj) throws Exception {
        UsuarioMovelVO.validarDados(obj);
        incluir(getIdEntidade());
        obj.realizarUpperCaseDados();
        String sql = "INSERT INTO UsuarioMovel (nome, senha, colaborador, cliente, ativo, usuariozw, empresa, origem, indicado, cpf, usuarioTW) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)";
        int i = 1;
        PreparedStatement ps = con.prepareStatement(sql);
        if (!obj.getSenhaEncriptada()) {
            final String senhaCripto = Uteis.encriptar(obj.getSenha());
            obj.setSenha(senhaCripto);
        }
        ps.setString(i++, obj.getNome());
        ps.setString(i++, obj.getSenha());
        ps = resolveFKNull(ps, i++, obj.getColaborador().getCodigo());
        ps = resolveFKNull(ps, i++, obj.getCliente().getCodigo());
        ps.setBoolean(i++, (obj.isAtivo()));
        ps = resolveFKNull(ps, i++, obj.getUsuarioZW());
        ps.setInt(i++, (obj.getEmpresa()));
        ps.setString(i++, obj.getOrigem());
        ps = resolveFKNull(ps, i++, obj.getIndicado());
        ps.setString(i++, obj.getCpf());
        ps = resolveFKNull(ps, i++, obj.getUsuarioTW());
        ps.execute();
        obj.setCodigo(obterValorChavePrimariaCodigo());
        obj.setNovoObj(false);
        obj.setSenhaAnterior(obj.getSenha());
    }

    /**
     * Operação responsável por alterar no BD os dados de um objeto da classe
     * <code>UsuarioMovelVO</code>. Sempre utiliza a chave primária da classe
     * como atributo para localização do registro a ser alterado. Primeiramente
     * valida os dados (
     * <code>validarDados</code>) do objeto. Verifica a conexão com o banco de
     * dados e a permissão do usuário para realizar esta operacão na entidade.
     * Isto, através da operação
     * <code>alterar</code> da superclasse.
     *
     * @param obj Objeto da classe <code>UsuarioMovelVO</code> que será alterada
     *            no banco de dados.
     * @throws Exception Caso haja problemas de conexão, restrição de acesso
     *                   ou validação de dados.
     */
    @Override
    public void alterar(UsuarioMovelVO obj) throws Exception {
        try {
            if (UteisValidacao.emptyString(obj.getSenha())) {
                obj.setSenha(obj.getSenhaAnterior());
            }
            UsuarioMovelVO.validarDados(obj);
            obj.realizarUpperCaseDados();

            if (!obj.getSenha().isEmpty()
                    && !MessageDigest.isEqual(
                    obj.getSenhaAnterior().getBytes(), obj.getSenha().getBytes())) {

                if (!obj.getSenhaEncriptada()) {
                    obj.setSenha(Uteis.encriptar(obj.getSenha()));
                }
                obj.setSenhaAnterior(obj.getSenha());
            }

            String sql = "UPDATE UsuarioMovel\n" +
                    "SET nome = ?, senha = ?, colaborador = ?, cliente = ?, ativo = ?, usuarioZW = ?, empresa = ?, origem = ?, indicado = ?, cpf = ?, usuariotw = ? \n" +
                    "WHERE codigo = ?";
            PreparedStatement ps = con.prepareStatement(sql);
            int i = 1;
            ps.setString(i++, obj.getNome());
            ps.setString(i++, obj.getSenha());
            ps = resolveFKNull(ps, i++, obj.getColaborador().getCodigo());
            ps = resolveFKNull(ps, i++, obj.getCliente().getCodigo());
            ps.setBoolean(i++, obj.isAtivo());
            ps = resolveFKNull(ps, i++, obj.getUsuarioZW());
            ps.setInt(i++, (obj.getEmpresa()));
            ps.setString(i++, obj.getOrigem());
            ps = resolveFKNull(ps, i++, obj.getIndicado());
            ps.setString(i++, obj.getCpf());
            ps = resolveFKNull(ps, i++, obj.getUsuarioTW());
            ps.setInt(i++, obj.getCodigo());

            ps.execute();
        } catch (Exception e) {
            throw e;
        }
    }

    public void alterarSemSenha(UsuarioMovelVO obj) throws Exception {
        try {
            if (obj.getSenha().isEmpty()) {
                obj.setSenha(obj.getSenhaAnterior());
            }
            UsuarioMovelVO.validarDados(obj, false);
            alterar(getIdEntidade());
            obj.realizarUpperCaseDados();

            String sql = "UPDATE UsuarioMovel\n" +
                    "SET nome = ?, colaborador = ?, cliente = ?, ativo = ?, usuarioZW = ?, empresa = ?, origem = ?, indicado = ?, cpf = ? \n" +
                    "WHERE codigo = ?";
            PreparedStatement ps = con.prepareStatement(sql);
            int i = 1;
            ps.setString(i++, obj.getNome());
            ps = resolveFKNull(ps, i++, obj.getColaborador().getCodigo());
            ps = resolveFKNull(ps, i++, obj.getCliente().getCodigo());
            ps.setBoolean(i++, obj.isAtivo());
            ps = resolveFKNull(ps, i++, obj.getUsuarioZW());
            ps.setInt(i++, (obj.getEmpresa()));
            ps.setString(i++, obj.getOrigem());
            ps = resolveFKNull(ps, i++, obj.getIndicado());
            ps.setString(i++, obj.getCpf());
            ps.setInt(i++, obj.getCodigo());

            ps.execute();
        } catch (Exception e) {
            throw e;
        }
    }

    public void gerarUsuarioMovelViaContextoZW(EmpresaVO empresaVO, ClienteVO clienteVO, String email, String nomePessoa, String cpf) throws Exception {
        if ((email == null) || (email.trim().equals(""))) {
            email = obterEmailCorrespondenciaPorPessoa(clienteVO.getPessoa().getCodigo());
            if ((email == null) || (email.trim().equals(""))) {
                return;
            }
        }
        OAMDService oamdService = new OAMDService();
        Logger.getLogger(ZillyonWebFacade.class.getName()).log(Level.SEVERE, "#### GERARUSUARIOMOVELVIACONTEXTOZW =" + " URL:" + oamdService.getConOAMD().getMetaData().getURL() + " HASHCODE:" + oamdService.getConOAMD().toString());
        if (oamdService.getConOAMD().isClosed()) {
            Logger.getLogger(ZillyonWebFacade.class.getName()).log(Level.SEVERE, "#### GERARUSUARIOMOVELVIACONTEXTOZW - CONEXAO DO OAMD FECHADA");
        }
        Logger.getLogger(ZillyonWebFacade.class.getName()).log(Level.SEVERE, "#### GERARUSUARIOMOVELVIACONTEXTOZW =" + " URL:" + oamdService.getConOAMD().getMetaData().getURL() + " HASHCODE:" + oamdService.getConOAMD().toString());
        EmpresaFinanceiroVO empresaFinanceiroVO = oamdService.consultarEmpresaFinanceiro((String) JSFUtilities.getFromSession("key"));
        Integer codigoEmpresaFinanceiro = (empresaFinanceiroVO != null) ? empresaFinanceiroVO.getCodigo() : null;
        if (codigoEmpresaFinanceiro != null) {
            Logger.getLogger(ZillyonWebFacade.class.getName()).log(Level.SEVERE, "#### GERARUSUARIOMOVELVIACONTEXTOZW - ACHOU codigoEmpresaFinanceiro");
        }
        Random generator = new Random();
        String senha = String.valueOf(generator.nextInt(90000000));
        if ((cpf != null) && (!cpf.equals(""))) {
            senha = Formatador.removerMascara(cpf);
        }
        IntegracaoCadastros integracaoCadastros = new IntegracaoCadastros(getCon());
        integracaoCadastros.incluirUsuarioMovel((String) JSFUtilities.getFromSession("key"),empresaVO, clienteVO, email, senha, nomePessoa, null, codigoEmpresaFinanceiro, null, true);
        integracaoCadastros = null;
        oamdService = null;
    }

    private String obterEmailCorrespondenciaPorPessoa(int pessoa) {
        try {
            String sql = "select email from email where emailcorrespondencia is true and pessoa =" + pessoa;
            try (ResultSet rs = criarConsulta(sql, con)) {
                if (rs.next()) {
                    return rs.getString("email");
                }
            }
        } catch (Exception e) {
        }
        return "";
    }

    public void alterarSenha(Integer codigoUsuarioMovel, String senha, String lembreteSenha) throws Exception {
        alterarSenha(codigoUsuarioMovel, senha, lembreteSenha, "");
    }
    public void alterarSenha(Integer codigoUsuarioMovel, String senha, String lembreteSenha, String senhaCriptograda) throws Exception {
        String sql = "update UsuarioMovel set senha = ? where codigo = ?";
        PreparedStatement pst = con.prepareStatement(sql);
        if (UteisValidacao.emptyString(senhaCriptograda)) {
            pst.setString(1, Uteis.encriptar(senha));
        } else {
            pst.setString(1, senhaCriptograda);
        }
        pst.setInt(2, codigoUsuarioMovel);
        pst.execute();
    }

    public void alterarSenhaJaEncriptada(Integer codigoUsuarioMovel, String senhaEncriptada) throws Exception {
        String sql = "update UsuarioMovel set senha = ? where codigo = ?";
        PreparedStatement pst = con.prepareStatement(sql);
        pst.setString(1, senhaEncriptada);
        pst.setInt(2, codigoUsuarioMovel);
        pst.execute();
    }


    /**
     * Operação responsável por excluir no BD um objeto da classe
     * <code>UsuarioMovelVO</code>. Sempre localiza o registro a ser excluído
     * através da chave primária da entidade. Primeiramente verifica a conexão
     * com o banco de dados e a permissão do usuário para realizar esta operacão
     * na entidade. Isto, através da operação
     * <code>excluir</code> da superclasse.
     *
     * @param obj Objeto da classe <code>UsuarioMovelVO</code> que será removido
     *            no banco de dados.
     * @throws Exception Caso haja problemas de conexão ou restrição de
     *                   acesso.
     */
    @Override
    public void excluir(UsuarioMovelVO obj) throws Exception {
        try {
            excluir(getIdEntidade());
            String sql = "DELETE FROM UsuarioMovel WHERE ((codigo = ?))";
            PreparedStatement ps = con.prepareStatement(sql);
            int i = 1;
            ps.setInt(i++, obj.getCodigo());
            ps.execute();
        } catch (Exception e) {
            throw e;
        }
    }

    public void excluir(ClienteVO clienteVO) throws Exception {
        String sql = "DELETE FROM UsuarioMovel WHERE cliente = ?";
        PreparedStatement ps = con.prepareStatement(sql);
        ps.setInt(1, clienteVO.getCodigo());
        ps.execute();
    }

    /**
     * Responsável por realizar uma consulta de
     * <code>UsuarioMovel</code> através do valor do atributo
     * <code>String nome</code>. Retorna os objetos, com início do valor do
     * atributo idêntico ao parâmetro fornecido. Faz uso da operação
     * <code>montarDadosConsulta</code> que realiza o trabalho de prerarar o
     * List resultante.
     *
     * @param controlarAcesso Indica se a aplicação deverá verificar se o
     *                        usuário possui permissão para esta consulta ou não.
     * @return List Contendo vários objetos da      * classe <code>UsuarioMovelVO</code> resultantes da consulta.
     * @throws Exception Caso haja problemas de conexão ou restrição de
     *                   acesso.
     */
    @Override
    public List consultarPorNome(String valorConsulta, boolean controlarAcesso, int nivelMontarDados) throws Exception {
        consultar(getIdEntidade(), controlarAcesso);
        String sqlStr = "SELECT * FROM UsuarioMovel WHERE upper( nome ) like('" + valorConsulta.toUpperCase() + "%') ORDER BY nome";
        try (Statement stm = con.createStatement()) {
            try (ResultSet tabelaResultado = stm.executeQuery(sqlStr)) {
                return (montarDadosConsulta(tabelaResultado, nivelMontarDados, con));
            }
        }
    }

    public List<UsuarioMovelVO> consultarTodosPorOrigem(String origem, int nivelMontarDados) throws Exception {
        StringBuilder sql = new StringBuilder();
        sql.append("select * from UsuarioMovel where upper(origem) = '").append(origem.toUpperCase()).append("'");
        try (Statement st = con.createStatement()) {
            try (ResultSet rs = st.executeQuery(sql.toString())) {
                return (montarDadosConsulta(rs, nivelMontarDados, con));
            }
        }
    }


    /**
     * Responsável por realizar uma consulta de
     * <code>UsuarioMovel</code> através do valor do atributo
     * <code>Integer codigo</code>. Retorna os objetos com valores iguais ou
     * superiores ao parâmetro fornecido. Faz uso da operação
     * <code>montarDadosConsulta</code> que realiza o trabalho de prerarar o
     * List resultante.
     *
     * @param controlarAcesso Indica se a aplicação deverá verificar se o
     *                        usuário possui permissão para esta consulta ou não.
     * @return List Contendo vários objetos da      * classe <code>UsuarioMovelVO</code> resultantes da consulta.
     * @throws Exception Caso haja problemas de conexão ou restrição de
     *                   acesso.
     */
    @Override
    public List consultarPorCodigo(Integer valorConsulta, boolean controlarAcesso, int nivelMontarDados) throws Exception {
        consultar(getIdEntidade(), controlarAcesso);
        String sqlStr = "SELECT * FROM UsuarioMovel WHERE codigo = " + valorConsulta + " ORDER BY codigo";
        try (Statement stm = con.createStatement()) {
            try (ResultSet tabelaResultado = stm.executeQuery(sqlStr)) {
                return (montarDadosConsulta(tabelaResultado, nivelMontarDados, con));
            }
        }
    }

    /**
     * Responsável por montar os dados de vários objetos, resultantes de uma
     * consulta ao banco de dados (
     * <code>ResultSet</code>). Faz uso da operação
     * <code>montarDados</code> que realiza o trabalho para um objeto por vez.
     *
     * @return List Contendo vários objetos da      * classe <code>UsuarioMovelVO</code> resultantes da consulta.
     */
    public static List montarDadosConsulta(ResultSet tabelaResultado, int nivelMontarDados, Connection con) throws Exception {
        List vetResultado = new ArrayList();
        while (tabelaResultado.next()) {
            UsuarioMovelVO obj = montarDados(tabelaResultado, nivelMontarDados, con);
            vetResultado.add(obj);
        }
        return vetResultado;
    }

    /**
     * Responsável por montar os dados resultantes de uma consulta ao banco de
     * dados (
     * <code>ResultSet</code>) em um objeto da classe
     * <code>UsuarioMovelVO</code>.
     *
     * @return O objeto da classe <code>UsuarioMovelVO</code> com os dados
     * devidamente montados.
     */
    public static UsuarioMovelVO montarDados(ResultSet dadosSQL, int nivelMontarDados, Connection con) throws Exception {
        UsuarioMovelVO obj = new UsuarioMovelVO();
        obj.setCodigo(dadosSQL.getInt("codigo"));
        obj.setNome(dadosSQL.getString("nome"));
        obj.setSenha(dadosSQL.getString("senha"));
        obj.setSenhaAnterior(obj.getSenha());
        obj.setAtivo(dadosSQL.getBoolean("ativo"));
        int colaborador = dadosSQL.getInt("colaborador");
        int cliente = dadosSQL.getInt("cliente");
        ClienteVO c = new ClienteVO();
        c.setCodigo(cliente);
        c.setUsuarioMovelVO(obj);
        obj.setCliente(c);
        ColaboradorVO colab = new ColaboradorVO();
        colab.setCodigo(colaborador);
        obj.setColaborador(colab);
        obj.setUsuarioZW(dadosSQL.getInt("usuarioZW"));
        obj.setEmpresa(dadosSQL.getInt("empresa"));
        obj.setOrigem(dadosSQL.getString("origem"));
        obj.setIndicado(dadosSQL.getInt("indicado"));
        obj.setCpf(dadosSQL.getString("cpf"));
        obj.setUsuarioTW(dadosSQL.getInt("usuarioTW"));
        obj.setNovoObj(false);
        montarDadosUsuarioEmail(obj, con);
        if (nivelMontarDados == Uteis.NIVELMONTARDADOS_DADOSBASICOS || nivelMontarDados == Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA) {
            if (colaborador != 0) {
                obj.setColaborador(new Colaborador(con).consultarPorChavePrimaria(colaborador, nivelMontarDados));
            }
            if (cliente != 0) {
                obj.setCliente(new Cliente(con).consultarPorChavePrimaria(cliente, nivelMontarDados));
                obj.getCliente().setUsuarioMovelVO(obj);
                obj.getCliente().setVinculoVOs(new Vinculo(con).consultarPorCodigoCliente(cliente, Uteis.NIVELMONTARDADOS_DADOSBASICOS, false));
            }
            return obj;
        }
        return obj;
    }

    public static void montarDadosUsuarioEmail(UsuarioMovelVO obj, Connection con) {
        try {
            UsuarioEmail usuarioEmailDao = new UsuarioEmail(con);
            obj.setUsuarioEmailVO(usuarioEmailDao.consultarPorUsuario(obj.getUsuarioZW()));
        } catch (Exception ex) {
        }
    }

    /**
     * Operação responsável por localizar um objeto da classe
     * <code>UsuarioMovelVO</code> através de sua chave primária.
     *
     * @throws Exception Caso haja problemas de conexão ou localização do
     *                   objeto procurado.
     */
    @Override
    public UsuarioMovelVO consultarPorChavePrimaria(Integer codigoPrm, int nivelMontarDados) throws Exception {
        consultar(getIdEntidade(), false);
        String sql = "SELECT * FROM UsuarioMovel WHERE codigo = ?";
        try (PreparedStatement ps = con.prepareStatement(sql)) {
            int i = 1;
            ps.setInt(i++, codigoPrm);
            try (ResultSet tabelaResultado = ps.executeQuery()) {
                if (!tabelaResultado.next()) {
                    throw new ConsistirException("Dados Não Encontrados ( UsuarioMovel ).");
                }
                return (montarDados(tabelaResultado, nivelMontarDados, con));
            }
        }
    }

    @Override
    public UsuarioMovelVO consultarPorCliente(ClienteVO cliente, int nivelMontarDados) throws Exception {
        String s = "select * from usuariomovel where cliente = " + cliente.getCodigo();
        try (ResultSet rs = criarConsulta(s, con)) {
            if (rs.next()) {
                return montarDados(rs, nivelMontarDados, con);
            } else {
                return new UsuarioMovelVO();
            }
        }
    }

    @Override
    public boolean consultarClienteTem(Integer codigoCliente) throws Exception {
        String s = "select codigo from usuariomovel where cliente = " + codigoCliente;
        try (ResultSet rs = criarConsulta(s, con)) {
            return rs.next();
        }
    }

    @Override
    public UsuarioMovelVO consultarPorColaborador(ColaboradorVO colaborador, int nivelMontarDados) throws Exception {
        String s = "select * from usuariomovel where colaborador = " + colaborador.getCodigo();
        try (ResultSet rs = criarConsulta(s, con)) {
            if (rs.next()) {
                return montarDados(rs, nivelMontarDados, con);
            } else {
                return new UsuarioMovelVO();
            }
        }
    }

    @Override
    public boolean usuarioTreino(Integer usuario) throws Exception {
        String s = "select * from usuariomovel where ativo is true and usuariozw = " + usuario;
        try (ResultSet rs = criarConsulta(s, con)) {
            return rs.next();
        }
    }

    public String obterUsuarioMesmoNome(String nome) {
        try {
            String sql = "select pes.nome,cli.matricula from usuariomovel um \n"
                    + " INNER JOIN cliente cli ON cli.codigo = um.cliente\n"
                    + " INNER JOIN pessoa pes ON pes.codigo = cli.pessoa\n"
                    + " WHERE um.nome = '" + nome + "'";
            try (ResultSet rs = criarConsulta(sql, con)) {
                if (rs.next()) {
                    return "Nome: " + rs.getString("nome") + ", Matrícula: " + rs.getString("matricula");
                }
            }
        } catch (Exception e) {
        }
        return "";
    }

    public UsuarioMovelVO consultarPorUsuarioSenha(final String username, final String senha) throws Exception {
        String s = "SELECT * FROM usuariomovel WHERE 1 = 1 AND ativo = TRUE AND nome = ? AND senha = ?;";
        try (PreparedStatement ps = con.prepareStatement(s)) {
            int i = 1;
            ps.setString(i++, username);
            ps.setString(i++, senha);
            try (ResultSet rs = ps.executeQuery()) {
                if (rs.next()) {
                    return montarDados(rs, Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA, con);
                } else {
                    return new UsuarioMovelVO();
                }
            }
        }
    }

    public UsuarioMovelVO consultarPorCPFSenha(final String cpf, final String senha) throws Exception {
        String s = "SELECT * FROM usuariomovel WHERE 1 = 1 AND ativo = TRUE AND cpf = ? AND senha = ?;";
        try (PreparedStatement ps = con.prepareStatement(s)) {
            int i = 1;
            ps.setString(i++, cpf);
            ps.setString(i++, senha);
            try (ResultSet rs = ps.executeQuery()) {
                if (rs.next()) {
                    return montarDados(rs, Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA, con);
                } else {
                    return new UsuarioMovelVO();
                }
            }
        }
    }

    public UsuarioMovelVO consultarPorNome(final String nome, int nivelMontarDados) throws Exception {
        String sql = "select * from usuariomovel where lower(nome) = lower(?)";
        try (PreparedStatement pst = con.prepareStatement(sql)) {
            pst.setString(1, nome);
            try (ResultSet rs = pst.executeQuery()) {
                if (rs.next()) {
                    return montarDados(rs, nivelMontarDados, con);
                }
            }
        }
        return null;

    }

    @Override
    public List obterTodosColaboradores(boolean controlarAcesso, int nivelMontarDados) throws Exception {
        consultar(getIdEntidade(), controlarAcesso);
        String sqlStr = "SELECT * FROM UsuarioMovel WHERE colaborador > 0 ORDER BY codigo";
        try (Statement stm = con.createStatement()) {
            try (ResultSet tabelaResultado = stm.executeQuery(sqlStr)) {
                return (montarDadosConsulta(tabelaResultado, nivelMontarDados, con));
            }
        }
    }

    public static String sqlAtivosNaoEstaoNoTreino(Integer empresa, boolean lista, boolean listaModalidades, String modalidadesFiltro, boolean cods) {
        StringBuilder sql = new StringBuilder("SELECT ");
        if (cods) {
            sql.append(" DISTINCT sw.codigocliente ");
        } else if (listaModalidades) {
            sql.append(" mod.nome as nomemodalidade, mod.codigo as codigomodalidade ");
        } else if (lista) {
            sql.append("sw.nomecliente as nome, sw.matricula, sw.codigopessoa, colp.nome as nomeprofessor, col.codigo as codigoprofessor, ");
            sql.append("ARRAY_TO_STRING(ARRAY(select nome from modalidade where codigo IN (select modalidade FROM contratomodalidade WHERE contrato = ");
            sql.append("sw.codigocontrato)), '|') as modalidades   ");
        } else {
            sql.append("COUNT(DISTINCT sw.codigocliente)");
        }
        sql.append(" FROM situacaoclientesinteticodw sw \n");
        sql.append(" INNER JOIN contrato con ON con.codigo = sw.codigocontrato \n");
        sql.append(" INNER JOIN contratomodalidade conmod ON conmod.contrato = con.codigo \n");
        sql.append(" INNER JOIN modalidade mod ON mod.codigo = conmod.modalidade AND mod.usatreino \n");
        if (lista) {
            sql.append(" LEFT JOIN vinculo vi ON vi.cliente = sw.codigocliente   AND vi.tipovinculo = 'TW' \n");
            sql.append(" LEFT JOIN colaborador col ON col.codigo = vi.colaborador \n");
            sql.append(" LEFT JOIN pessoa colp ON colp.codigo = col.pessoa \n");
        }
        sql.append(" LEFT JOIN usuariomovel u ON u.cliente = sw.codigocliente AND (u.origem = 'ZW' OR u.origem = 'TW') \n");
        sql.append(" WHERE sw.situacao = 'AT' AND u.codigo is null AND sw.empresacliente = ").append(empresa);

        if (modalidadesFiltro != null && !modalidadesFiltro.isEmpty()) {
            sql.append(" AND mod.codigo IN (").append(modalidadesFiltro).append(") ");
        }
        if (listaModalidades) {
            sql.append("\n GROUP BY mod.nome, mod.codigo ");
            sql.append(" ORDER BY mod.nome");
        }
        if (lista) {
            sql.append("\n GROUP BY sw.nomecliente, sw.matricula, sw.codigopessoa, colp.nome, col.codigo, sw.codigocontrato");
            sql.append(" ORDER BY sw.nomecliente");
        }
        return sql.toString();
    }

    public static String sqlClientesZW(Integer empresa) {
        StringBuilder sql = new StringBuilder("SELECT ");
        sql.append("sw.nomecliente as nome, sw.matricula, sw.codigopessoa, colp.nome as nomeprofessor, col.codigo as codigoprofessor, ");
        sql.append("ARRAY_TO_STRING(ARRAY(select nome from modalidade where codigo IN (select modalidade FROM contratomodalidade WHERE contrato = ");
        sql.append("sw.codigocontrato)), '|') as modalidades   ");
        sql.append(" FROM situacaoclientesinteticodw sw \n");
        sql.append(" LEFT JOIN contrato con ON con.codigo = sw.codigocontrato \n");
        sql.append(" LEFT JOIN contratomodalidade conmod ON conmod.contrato = con.codigo \n");
        sql.append(" LEFT JOIN modalidade mod ON mod.codigo = conmod.modalidade AND mod.usatreino \n");
        sql.append(" LEFT JOIN vinculo vi ON vi.cliente = sw.codigocliente   AND vi.tipovinculo = 'TW' \n");
        sql.append(" LEFT JOIN colaborador col ON col.codigo = vi.colaborador \n");
        sql.append(" LEFT JOIN pessoa colp ON colp.codigo = col.pessoa \n");
        sql.append(" WHERE sw.empresacliente = ").append(empresa);
        sql.append("\n GROUP BY sw.nomecliente, sw.matricula, sw.codigopessoa, colp.nome, col.codigo, sw.codigocontrato");
        sql.append(" ORDER BY sw.nomecliente");
        return sql.toString();
    }

    @Override
    public Integer obterAlunosAtivosSemUsuarioMovel(final Integer empresa) throws Exception {
        return contar(sqlAtivosNaoEstaoNoTreino(empresa, false, false, null, false), con);
    }


    @Override
    public String obterCodsAlunosAtivosSemUsuarioMovel(final Integer empresa) throws Exception {
        String result = "";
        try (ResultSet rs = criarConsulta(sqlAtivosNaoEstaoNoTreino(empresa, false, false, null, true), con)) {
            while (rs.next()) {
                result += "," + rs.getString("codigocliente");
            }
        }
        return result.replaceFirst(",", "");
    }

    @Override
    public JSONArray obterListaAlunosAtivosForaTreino(final Integer empresa, String modalidadesFiltro) throws Exception {
        return Vinculo.montarArray(criarConsulta(sqlAtivosNaoEstaoNoTreino(empresa, true, false, modalidadesFiltro, false), con));
    }

    @Override
    public JSONArray obterListaAlunosForaTreino(final Integer empresa) throws Exception {
        return Vinculo.montarArray(criarConsulta(sqlClientesZW(empresa), con));
    }

    @Override
    public JSONArray obterModalidadesAlunosAtivosForaTreino(final Integer empresa) throws Exception {
        JSONArray array;
        try (ResultSet rs = criarConsulta(sqlAtivosNaoEstaoNoTreino(empresa, false, true, null, false), con)) {
            array = new JSONArray();
            while (rs.next()) {
                JSONObject json = new JSONObject();
                json.put("codigoModalidade", rs.getInt("codigomodalidade"));
                json.put("nome", rs.getString("nomemodalidade"));
                array.put(json);
            }
        }
        return array;
    }

    @Override
    public boolean temUsuarioMovel(Integer codigoCliente) {
        try {
            return existe("SELECT codigo FROM UsuarioMovel WHERE cliente = " + codigoCliente, con);
        } catch (Exception e) {
            return false;
        }

    }

    @Override
    public UsuarioMovelVO gerarUsuarioMovel(IndicadoVO indicado) throws Exception {
        UsuarioMovelVO usuario = new UsuarioMovelVO();
        usuario.setIndicado(indicado.getCodigo());
        usuario.setEmpresa(indicado.getEmpresaVO().getCodigo());
        usuario.setNome(indicado.getEmail());
        Random generator = new Random(indicado.getCodigo());
        Integer senhaInt = generator.nextInt(900000);
        DecimalFormat df = new DecimalFormat("000000");
        String senha = df.format(senhaInt);
        usuario.setSenha(senha);
        usuario.setSenhaEncriptada(false);
        usuario.setOrigem(OrigemSistemaEnum.APP_TREINO.getDescricao());
        incluir(usuario);
        usuario.setSenhaAnterior(senha);
        return usuario;
    }

    public UsuarioMovelVO consultarPorUsuario(UsuarioVO usuarioVO, int nivelMontarDados) throws Exception {
        String s = "SELECT usuariomovel.* FROM usuariomovel WHERE colaborador IN (SELECT col2.codigo FROM usuario usu\n" +
                "LEFT JOIN colaborador col ON usu.colaborador = col.codigo\n" +
                "LEFT JOIN colaborador col2 ON col2.pessoa = col.pessoa\n" +
                "where usu.codigo = " + usuarioVO.getCodigo() + ");";
        try (ResultSet rs = criarConsulta(s, con)) {
            if (rs.next()) {
                return montarDados(rs, nivelMontarDados, con);
            } else {
                return new UsuarioMovelVO();
            }
        }
    }

    public UsuarioMovelVO consultarPorUserName(String username) throws Exception {
        String s = "SELECT * FROM usuariomovel WHERE 1 = 1 AND ativo = TRUE AND nome = ?;";
        try (PreparedStatement ps = con.prepareStatement(s)) {
            int i = 1;
            ps.setString(i++, username);
            try (ResultSet rs = ps.executeQuery()) {
                if (rs.next()) {
                    return montarDados(rs, Uteis.NIVELMONTARDADOS_DADOSBASICOS, con);
                } else {
                    return new UsuarioMovelVO();
                }
            }
        }
    }

    public String gerarUsuarioMovelAluno(Integer codigoCliente, String key, String senha, String username, String nomeApp, String urlGooglePlay, String urlItunes,final String chaveUrlMobile) throws Exception {
        Cliente clienteDao = new Cliente(con);
        Email emailDao = new Email(con);
        ClienteVO cliente = clienteDao.consultarPorCodigo(codigoCliente, false,
                Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA);
        /**
         * Como determinar o Email (username): 1. Se o usãrio informar um
         * novo e-mail, o sistema deve verificar se este já existe
         * cadastrado e incluir se não existe; 2. Se o usuário não informar
         * um e-mail, o sistema deve procurar o primeiro cadastrado e
         * utilizã-lo como 'username'; 3. Se o usuário informar que não
         * usarã o app mobile o sistema deve usar a matrãcula como
         * 'username'
         */
        boolean matriculaComoUserName = username != null && username.equals(cliente.getCodigoMatricula().toString());
        List<EmailVO> listaEmails = emailDao.consultarEmails(cliente.getPessoa().getCodigo(), false, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
        boolean jaExiste = false;
        for (EmailVO emailVO : listaEmails) {
            if (username != null && emailVO.getEmail().equalsIgnoreCase(username)) {
                jaExiste = true;
                break;
            }
        }
        if (username == null) {
            username = listaEmails.isEmpty() ? null : listaEmails.get(0).getEmail();
            jaExiste = username != null && !username.isEmpty();
        }
        if (username == null || username.isEmpty()) {
            throw new ConsistirException(String.format("Nenhum Email cadastrado/informado", new Object[]{username}));
        }

        username = username.toLowerCase();
        if (!matriculaComoUserName) {
            if (!UteisEmail.getValidEmail(username)) {
                throw new ConsistirException(String.format("Email %s inválido", new Object[]{username}));
            }
        }

        UsuarioMovelVO usuarioMovel = consultarPorCliente(
                cliente, Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA);

        //se o usuário movél já existir, o sistema não altera senha atravãs deste mãtodo
        //ã preciso usar os mãtodos de Alteração ("AlterarUsuarioMovel...")
        String senhaRandomica = senha;
        if (senha == null || senha.isEmpty()) {
            String uuid = UUID.randomUUID().toString();
            senhaRandomica = uuid.substring(0, uuid.indexOf("-"));
            if (senhaRandomica == null || senhaRandomica.isEmpty()) {
                senhaRandomica = cliente.getCodigoMatricula().toString();
            }
        }

        boolean senhaAlterada = true;

        if (usuarioMovel.getCodigo() > 0) {
            usuarioMovel.setNome(username);
            usuarioMovel.setSenha(senha);
            usuarioMovel.setAtivo(true);
            if (UteisValidacao.emptyString(senha)) {
                alterarSemSenha(usuarioMovel);
                senhaAlterada = false;
            } else {
                alterar(usuarioMovel);
            }
        } else {
            UsuarioMovelVO novoUsuario = new UsuarioMovelVO();
            novoUsuario.setAtivo(true);
            novoUsuario.setCliente(cliente);
            novoUsuario.setNome(username);
            novoUsuario.setEmpresa(cliente.getEmpresa().getCodigo());
            novoUsuario.setOrigem("TW");
            novoUsuario.setSenha(senhaRandomica);
            //Para caso gerado senha randomica a mesma seja atribuida ao corpo do e-mail enviado ao aluno.
            senha = senhaRandomica;
            incluir(novoUsuario);
            usuarioMovel = novoUsuario;
        }

        if (!jaExiste && !matriculaComoUserName) {
            EmailVO emailNovo = new EmailVO();
            emailNovo.setCliente(codigoCliente);
            emailNovo.setEmail(username);
            emailNovo.setEmailCorrespondencia(false);
            emailNovo.setPessoa(cliente.getPessoa().getCodigo());
            emailDao.incluir(emailNovo);
        }

        cliente.setAtualizarTreino(false);
        ZillyonWebFacade zwFacade = new ZillyonWebFacade(con);
        if (!matriculaComoUserName) {
            try {
               zwFacade.enviarEmail(new String[]{username}, "Informações de acesso ao Pacto Treino",
                        gerarCorpoEmailSenhaUsuarioMovel(key, cliente, null, usuarioMovel, senha, true, nomeApp, urlGooglePlay, urlItunes,chaveUrlMobile));
            } catch (Exception ex) {
                String msg = "Encontrado problema ao enviar e-mail para " + username + " verifique as configurações de envio : " + ex.getMessage();
                throw new Exception(msg);
            }
        }

        sincronizarTW(key, cliente, usuarioMovel);


        zwFacade = null;
        clienteDao = null;
        emailDao = null;

        return (!matriculaComoUserName && senhaAlterada)
                ? "Email enviado com sucesso para " + username
                : "Cadastro realizado com sucesso para matrícula " + username;

    }

    public StringBuilder gerarCorpoEmailSenhaUsuarioMovel(
            final String key,
            final ClienteVO cliente, ColaboradorVO colaborador,
            final UsuarioMovelVO usuarioMovel,
            final String senha,
            final boolean novoAPP,
            final String nomeApp,
            final String urlGooglePlay,
            final String urlItunes, final String chaveUrlMobile) throws Exception {
        Empresa empresaDao = new Empresa(con);

        final String expiracao = Uteis.getDataAplicandoFormatacao(Uteis.somarDias(Calendario.hoje(), 7), "yyyyMMdd");
        final String hash = Uteis.encriptar(String.format("%s|%s|%s|%s", new Object[]{key, usuarioMovel.getNome(),
                senha, expiracao}), chaveUrlMobile);


        Uteis.desencriptar(String.format("%s|%s|%s|%s", new Object[]{key, usuarioMovel.getNome(),
                senha, expiracao}), chaveUrlMobile);
        String urlAtivacaoIOS;
        if (novoAPP) {
            urlAtivacaoIOS = "appdoaluno://?token=" + hash;
        } else {
            urlAtivacaoIOS = "pactotreino://?token=" + hash;
        }
        final String urlAtivacaoAndroid = "http://www.pactotreino.com.br/?token=" + hash;
        if (key.equals("ad979b3ace1b577b2a87ebe8781b4992")
                || key.equals("78cfbd0442cf8d6bc06a8ba70e838355")
                || key.equals("c9ee582d4d58d2537ea914133746e883")) {

            String prefixoQRCode = "parkshopping";

            if (key.equals("78cfbd0442cf8d6bc06a8ba70e838355")) {
                prefixoQRCode = "asasul";
            } else if (key.equals("c9ee582d4d58d2537ea914133746e883")) {
                prefixoQRCode = "sudoeste";
            }
            EmpresaVO empresa = null;
            if (cliente != null) {
                empresa = empresaDao.consultarPorChavePrimaria(
                        cliente.getEmpresa().getCodigo(), Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA);
            } else {
                empresa = empresaDao.consultarPorChavePrimaria(
                        usuarioMovel.getEmpresa(), Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA);
            }
            empresaDao = null;

            File arq = new File(getClass().getResource("/br/com/pactosolucoes/comuns/util/resources/emailPactoTreinoUnique.txt").toURI());
            StringBuilder texto = FileUtilities.readContentFile(arq.getAbsolutePath());
            final String aux = texto.toString().
                    replaceAll("#URL_SITE", empresa.getSite()).
                    //                    replaceAll("#URL_LOGO", "http://app.pactosolucoes.com.br/oamd/imagens/" + key + ".jpg").
                            replaceAll("#CHAVE_EMPRESA", key).replaceAll("#EMAIL_USUARIO", usuarioMovel.getNome().toLowerCase()).
                            replaceAll("#SENHA", senha).replaceAll("#PREFIXO_EMPRESA", prefixoQRCode).
                            replaceAll("#HASH_IOS", urlAtivacaoIOS).
                            replaceAll("#HASH_ANDROID", urlAtivacaoAndroid);
            texto = null;
            return new StringBuilder(aux);
        } else {
            String[] imagens = new String[]{"baixar_androidP4CT0.png", "baixar_appleP4CT0.png", "entrar_androidP4CT0.png", "entrar_appleP4CT0.png", "topo_emailP4CT0.png", "setaP4CT0.png"};
            Map<String, File> mapaImagem = new HashMap<String, File>();
            for (String imagem : imagens) {
                File arqImg = new File(getClass().getResource("/br/com/pactosolucoes/comuns/util/resources/" + imagem).toURI());
                mapaImagem.put(imagem, arqImg);
            }
            UteisEmail.criarImagensEmailTreino(mapaImagem);
            File arq = new File(getClass().getResource("/br/com/pactosolucoes/comuns/util/resources/emailPactoTreinoDefaultNovo.txt").toURI());
            StringBuilder texto = FileUtilities.readContentFile(arq.getAbsolutePath());
            String nome = cliente != null ? Uteis.getPrimeiroNome(cliente.getPessoa().getNome()) : Uteis.getPrimeiroNome(colaborador.getPessoa().getPrimeiroNomeConcatenado());

            String URL_PLAY = PropsService.getPropertyValue(PropsService.urlTreinoGooglePlay);
            String URL_ITUNES = PropsService.getPropertyValue(PropsService.urlTreinoAppleStore);
            if (key.equals("f6346c926a547ad524cdccd5bfcceba0")) { //PRIMATAS
                URL_PLAY = "https://play.google.com/store/apps/details?id=com.pacto.appdoaluno";
                URL_ITUNES = "https://itunes.apple.com/br/app/primatas-crossfit/id1353143778";
            }
            final String genKey = MidiaService.getInstance().genKey(key, MidiaEntidadeEnum.FOTO_EMPRESA_RELATORIO, cliente.getEmpresa().getCodigo().toString());
            final String urlFotoEmpresa = Uteis.getPaintFotoDaNuvem(genKey);

            final String aux = texto.toString()
                    .replaceAll("#CHAVE_ACADEMIA ", key)
                    .replaceAll("#NEW_USER", nome)
                    .replaceAll("#USER_NAME", usuarioMovel.getNome().toLowerCase())
                    .replaceAll("#SENHA", UteisValidacao.emptyString(senha) ? "A senha não foi alterada" : senha)
                    .replaceAll("#URL_PLAY", (urlGooglePlay == null || urlGooglePlay.equalsIgnoreCase("")) ? URL_PLAY:urlGooglePlay)
                    .replaceAll("#URL_ITUNES", (urlItunes==null || urlItunes.equalsIgnoreCase("")) ? URL_ITUNES:urlItunes)
                    .replaceAll("#URL_IOS", urlAtivacaoIOS)
                    .replaceAll("#URL_ANDROID", urlAtivacaoAndroid)
                    .replaceAll("#NOME_APP", (nomeApp == null || nomeApp.equalsIgnoreCase("")) ? "ZW Treino":nomeApp)
                    .replaceAll("#URL_FOTO_EMPRESA", (urlFotoEmpresa == null) ?
                            "http://wiki.pactosolucoes.com.br/midias/email_app/topo_emailP4CTO_NOVO.png":urlFotoEmpresa);
            return new StringBuilder(aux);
        }

    }

    public void verificarUsuarioMovel(final String key, ClienteVO clienteVO) throws Exception {
        UsuarioMovelVO usuarioMovelVO = consultarPorCliente(clienteVO, Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA);
        if(UtilReflection.objetoMaiorQueZero(usuarioMovelVO,"getCodigo()")){
            TreinoWSConsumer.sincronizarUsuario(key, usuarioMovelVO.toUsuarioTreino());
        } else {
            gerarUsuarioMovelAluno(clienteVO.getCodigo(), key, "", clienteVO.getCodigoMatricula().toString(), null, null, null, null);
        }
    }

    public void verificarEnvioAlunoParaTW(final String key, ClienteVO clienteVO) throws Exception {
        UsuarioMovelVO usuarioMovelVO = consultarPorCliente(clienteVO, Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA);
        if(UtilReflection.objetoMaiorQueZero(usuarioMovelVO,"getCodigo()")){
            sincronizarTW(key,clienteVO,usuarioMovelVO);
        } else {
            gerarUsuarioMovelAluno(clienteVO.getCodigo(), key, "", clienteVO.getCodigoMatricula().toString(), null, null, null, null);
        }
    }

    public void sincronizarTW(String key, ClienteVO cliente, UsuarioMovelVO usuarioMovel) throws Exception {
        ZillyonWebFacade zwFacade = new ZillyonWebFacade(con);
        SituacaoClienteSinteticoDWVO swCliente = zwFacade.atualizarSintetico(
                cliente, Calendario.hoje(), SituacaoClienteSinteticoEnum.GRUPO_CLIENTE, true);
        usuarioMovel.getCliente().setSituacaoClienteSinteticoVO(swCliente);
        if (usuarioMovel.getCliente() != null && UteisValidacao.emptyList(usuarioMovel.getCliente().getVinculoVOs())) {
            Vinculo vinculo = new Vinculo(zwFacade.getCon());
            usuarioMovel.getCliente().setVinculoVOs(vinculo.consultarPorCodigoCliente(usuarioMovel.getCliente().getCodigo(), Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA, false));
        }
        TreinoWSConsumer.sincronizarUsuario(key, usuarioMovel.toUsuarioTreino());
    }

    private void sincronizarClientePesquisa(final String key, ClienteVO clienteVO) throws Exception {
        Cliente clienteDAO = new Cliente(con);
        JSONArray clientesJSON = clienteDAO.consultarDadosClienteConsultaTW(clienteVO.getCodigo());
        if(clientesJSON.length() > 0) {
            TreinoWSConsumer.sincronizarAlunoPesquisa(key, (JSONObject) clientesJSON.get(0));
        }
    }

    public void alterarUsuarioTW(UsuarioMovelVO usuarioMovelVO) throws Exception {
        String sql = "update UsuarioMovel set usuariotw = ? where codigo = ?";
        PreparedStatement pst = con.prepareStatement(sql);
        pst = resolveIntegerNull(pst, 1, usuarioMovelVO.getUsuarioTW());
        pst.setInt(2, usuarioMovelVO.getCodigo());
        pst.execute();
    }

    public Integer obterUsuarioTW(String key, Integer usuariozw, Integer empresazw) {
        try {
            UsuarioMovelVO usuarioMovelVO = consultarPorUsuarioEmpresa(usuariozw, empresazw, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
            if (usuarioMovelVO != null &&
                    !UteisValidacao.emptyNumber(usuarioMovelVO.getCodigo()) &&
                    !UteisValidacao.emptyNumber(usuarioMovelVO.getUsuarioTW())) {
                return usuarioMovelVO.getUsuarioTW();
            }

            RequestHttpService service = new RequestHttpService();
            String urlTreino = PropsService.getPropertyValue(key, PropsService.urlTreino);
            Map<String, String> params = new HashMap<>();
            if (!UteisValidacao.emptyNumber(empresazw)) {
                params.put("empresazw", empresazw.toString());
            }

            RespostaHttpDTO respostaHttpDTO = service.executeRequest(urlTreino + "/prest/usuario/" + key + "/find-by-usuariozw/" + usuariozw, null, params, null, MetodoHttpEnum.GET);
            JSONObject json = new JSONObject(respostaHttpDTO.getResponse());
            if (json.has("return")) {
                Integer codigoUsuarioTW = json.getJSONObject("return").getInt("codigo");
                if (usuarioMovelVO != null &&
                        !UteisValidacao.emptyNumber(usuarioMovelVO.getCodigo()) &&
                        !UteisValidacao.emptyNumber(codigoUsuarioTW)) {
                    usuarioMovelVO.setUsuarioTW(codigoUsuarioTW);
                    alterarUsuarioTW(usuarioMovelVO);
                }
                return codigoUsuarioTW;
            } else {
                throw new Exception(respostaHttpDTO.getResponse());
            }
        } catch (Exception ex) {
            ex.printStackTrace();
            return null;
        }
    }

    public UsuarioMovelVO consultarPorUsuarioEmpresa(Integer usuarioZw, Integer empresaZw, int nivelMontarDados) throws Exception {
        StringBuilder sql = new StringBuilder();
        sql.append("select \n");
        sql.append("um.* \n");
        sql.append("from usuariomovel um \n");
        sql.append("where um.usuariozw = ").append(usuarioZw).append(" \n");
        if (!UteisValidacao.emptyNumber(empresaZw)) {
            sql.append("and (um.empresa is null or um.empresa = ").append(empresaZw).append(") \n");
        }
        sql.append("order by um.empresa \n");
        try (ResultSet rs = criarConsulta(sql.toString(), con)) {
            if (rs.next()) {
                return montarDados(rs, nivelMontarDados, con);
            } else {
                return new UsuarioMovelVO();
            }
        }
    }

    @Override
    public UsuarioMovelVO consultarPorCodigoCliente(Integer codigoCliente, int nivelMontarDados) throws Exception {
        String s = "SELECT * FROM usuariomovel WHERE cliente = " + codigoCliente;
        try (ResultSet rs = criarConsulta(s, con)) {
            if (rs.next()) {
                return montarDados(rs, nivelMontarDados, con);
            } else {
                return new UsuarioMovelVO();
            }
        }
    }


    public UsuarioMovelVO consultarPorColaboradorDoUsuario(UsuarioVO usuarioVO, int nivelMontarDados) throws Exception {
        String s = "SELECT usuariomovel.* FROM usuariomovel WHERE colaborador = " + usuarioVO.getColaboradorVO().getCodigo() + ";";
        try (ResultSet rs = criarConsulta(s, con)) {
            if (rs.next()) {
                return montarDados(rs, nivelMontarDados, con);
            } else {
                return new UsuarioMovelVO();
            }
        }
    }

    public List<UsuarioMovelVO> consultarTodos(int nivelMontarDados) throws Exception {
        StringBuilder sql = new StringBuilder();
        sql.append("select * from UsuarioMovel");
        try (Statement st = con.createStatement()) {
            try (ResultSet rs = st.executeQuery(sql.toString())) {
                return (montarDadosConsulta(rs, nivelMontarDados, con));
            }
        }
    }

    public StringBuilder gerarCorpoEmailSenhaUsuarioMovel(
            final String key,
            final ClienteVO cliente, ColaboradorVO colaborador,
            final UsuarioMovelVO usuarioMovel,
            final String senha,
            final boolean novoAPP,
            final String urlAppEmail) throws Exception {

        Empresa empresaDAO;
        try {
            empresaDAO = new Empresa(this.con);


            String CHAVE_URL_MOBILE = "Tr3in0";

            final String expiracao = Uteis.getDataAplicandoFormatacao(Uteis.somarDias(Calendario.hoje(), 7), "yyyyMMdd");
            final String hash = Uteis.encriptar(String.format("%s|%s|%s|%s", key, usuarioMovel.getNome(),
                    senha, expiracao), CHAVE_URL_MOBILE);


            Uteis.desencriptar(String.format("%s|%s|%s|%s", key, usuarioMovel.getNome(),
                    senha, expiracao), CHAVE_URL_MOBILE);
            String urlAtivacaoIOS;
            String urlApp = "";
            if (novoAPP) {
                urlAtivacaoIOS = "appdoaluno://?token=" + hash;
            } else {
                urlAtivacaoIOS = "pactotreino://?token=" + hash;
            }
            final String urlAtivacaoAndroid = "http://www.pactotreino.com.br/?token=" + hash;
            if (key.equals("ad979b3ace1b577b2a87ebe8781b4992")
                    || key.equals("78cfbd0442cf8d6bc06a8ba70e838355")
                    || key.equals("c9ee582d4d58d2537ea914133746e883")) {

                String prefixoQRCode = "parkshopping";

                if (key.equals("78cfbd0442cf8d6bc06a8ba70e838355")) {
                    prefixoQRCode = "asasul";
                } else if (key.equals("c9ee582d4d58d2537ea914133746e883")) {
                    prefixoQRCode = "sudoeste";
                }

                EmpresaVO empresa = null;
                if (cliente != null && cliente.getCodigo() > 0) {
                    empresa = empresaDAO.consultarPorChavePrimaria(cliente.getEmpresa().getCodigo(), Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA);
                } else if (colaborador != null && colaborador.getCodigo() > 0) {
                    empresa = empresaDAO.consultarPorChavePrimaria(colaborador.getEmpresa().getCodigo(), Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA);
                } else {
                    empresa = empresaDAO.consultarPorChavePrimaria(usuarioMovel.getEmpresa(), Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA);
                }

                File arq = new File(getClass().getResource("/br/com/pactosolucoes/comuns/util/resources/emailPactoTreinoUnique.txt").toURI());
                StringBuilder texto = FileUtilities.readContentFile(arq.getAbsolutePath());
                final String aux = texto.toString().
                        replaceAll("#URL_SITE", empresa.getSite()).
                        //                    replaceAll("#URL_LOGO", "http://app.pactosolucoes.com.br/oamd/imagens/" + key + ".jpg").
                                replaceAll("#CHAVE_EMPRESA", key).replaceAll("#EMAIL_USUARIO", usuarioMovel.getNome().toLowerCase()).
                        replaceAll("#SENHA", senha).replaceAll("#PREFIXO_EMPRESA", prefixoQRCode).
                        replaceAll("#HASH_IOS", urlAtivacaoIOS).
                        replaceAll("#HASH_ANDROID", urlAtivacaoAndroid);
                texto = null;
                return new StringBuilder(aux);
            } else {
                String[] imagens = new String[]{"baixar_androidP4CT0.png", "baixar_appleP4CT0.png", "entrar_androidP4CT0.png", "entrar_appleP4CT0.png", "topo_emailP4CT0.png", "setaP4CT0.png"};
                Map<String, File> mapaImagem = new HashMap<>();
                for (String imagem : imagens) {
                    File arqImg = new File(getClass().getResource("/br/com/pactosolucoes/comuns/util/resources/" + imagem).toURI());
                    mapaImagem.put(imagem, arqImg);
                }
                UteisEmail.criarImagensEmailTreino(mapaImagem);

                boolean app_personalizado = false;
                String app_personalizado_nome = "", app_personalizado_url = "", app_url_email = "";

                RequestHttpService httpService = new RequestHttpService();
                String urlTreino = PropsService.getPropertyValue(key, PropsService.urlTreino);
                RespostaHttpDTO respostaHttpDTO = httpService.executeRequest(urlTreino + "/prest/config/" + key + "/manutencao", null, null, null, MetodoHttpEnum.GET);
                JSONObject json = new JSONObject(respostaHttpDTO.getResponse());
                if (json.has("return")) {
                    String returnValue = json.getString("return");
                    returnValue = returnValue.replaceAll("\\\\", "");
                    JSONObject nestedObj = new JSONObject(returnValue);
                    app_personalizado = nestedObj.getBoolean("aplicativo_personalizado");
                    app_personalizado_nome = nestedObj.getString("aplicativo_personalizado_nome");
                    app_personalizado_url = nestedObj.getString("aplicativo_personalizado_url");
                    app_url_email = nestedObj.getString("app_url_email");
                }

                String urlAppPlay = "";
                String urlAppApple = "";
                String modeloArquivo = "";
                String nomeApp = "";
                if (!UteisValidacao.emptyString(app_url_email)) {
                    switch (app_url_email) {

                        case "MINHA_ACADEMIA":
                            urlApp = "https://sistemspacto.app.link/minha-academia";
                            urlAppPlay = "https://play.google.com/store/apps/details?id=com.pacto.zwacademia&hl=pt_BR&gl=US";
                            urlAppApple = "https://apps.apple.com/br/app/minha-academia/id1388309741";
                            modeloArquivo = "emailPactoTreinoModeloMinhaAcademia.txt";
                            nomeApp = "Minha Academia";
                            break;

                        case "MEU_BOX":
                            urlApp = "https://sistempacto.app.link/meu-box";
                            urlAppPlay = "https://play.google.com/store/apps/details?id=com.pactosolucoes.meubox&hl=pt_BR&gl=US";
                            urlAppApple = "https://apps.apple.com/pt/app/meu-box/id1342274240";
                            modeloArquivo = "emailPactoTreinoDefaultNovoModeloMyBox.txt";
                            nomeApp = "Meu Box";
                            break;
                        default:
                            urlApp = "https://sistemapacto.app.link/apptreino";
                            urlAppPlay = "https://play.google.com/store/apps/details?id=com.pacto&hl=pt_BR&gl=US";
                            urlAppApple = "https://apps.apple.com/br/app/treino/id862662527";
                            modeloArquivo = "emailPactoTreinoDefaultNovoModelo.txt";
                            nomeApp = "App Treino";
                            break;
                    }
                } else if (app_personalizado) {
                    nomeApp = app_personalizado_nome;
                    urlAppPlay = "https://play.google.com/store/apps/details?id=com.pacto.zwacademia&hl=pt_BR&gl=US";
                    urlAppApple = "https://apps.apple.com/br/app/minha-academia/id1388309741";
                    modeloArquivo = "emailPactoTreinoAppPersonalizado.txt";
                }


                File arq = new File(getClass().getResource("/br/com/pactosolucoes/comuns/util/resources/" + modeloArquivo).toURI());
                StringBuilder texto = FileUtilities.readContentFile(arq.getAbsolutePath());
                String nome = (cliente != null && cliente.getCodigo() > 0) ? Uteis.getPrimeiroNome(cliente.getPessoa().getNome()) : Uteis.getPrimeiroNome(colaborador.getPessoa().getPrimeiroNomeConcatenado());

                EmpresaVO empresa = null;
                if (cliente != null && cliente.getCodigo() > 0) {
                    Integer codigoEmpresa = cliente.getEmpresa().getCodigo() != null && cliente.getEmpresa().getCodigo() > 0 ? cliente.getEmpresa().getCodigo() : usuarioMovel.getEmpresa();
                    empresa = empresaDAO.consultarPorChavePrimaria(
                            codigoEmpresa, Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA);
                } else if (colaborador != null && colaborador.getCodigo() > 0) {
                    empresa = empresaDAO.consultarPorChavePrimaria(
                            colaborador.getEmpresa().getCodigo(), Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA);
                } else {
                    empresa = empresaDAO.consultarPorChavePrimaria(
                            usuarioMovel.getEmpresa(), Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA);
                }

                final String genKey = MidiaService.getInstance().genKey(key, MidiaEntidadeEnum.FOTO_EMPRESA_RELATORIO, cliente.getEmpresa().getCodigo().toString());
                final String urlFotoEmpresa = Uteis.getPaintFotoDaNuvem(genKey);
                if (urlAppEmail.equals("TREINO") && usuarioMovel.getCliente() != null && UteisValidacao.emptyNumber(usuarioMovel.getCliente().getCodigo()) &&
                        usuarioMovel.getColaborador() != null && UteisValidacao.notEmptyNumber(usuarioMovel.getColaborador().getCodigo())) {
                    texto = new StringBuilder(texto.toString().replace("&#233; um aluno da #NOME_ACADEMIA e poder&#225; acessar seus treinos, agendar aulas e muito mais",
                            "est&#225; apto a prescrever treinos, agendar aulas e muito mais na #NOME_ACADEMIA"));
                }
                final String aux = texto.toString()
                        .replaceAll("#NOME_ACADEMIA", empresa.getNome())
                        .replaceAll("#ENDERECO_EMPRESA", empresa.getEndereco())
                        .replaceAll("#CIDADE_EMPRESA", (!UteisValidacao.emptyString(empresa.getCidade().getNome())) ? " - " + empresa.getCidade().getNome() : "")
                        .replaceAll("#ESTADO_EMPRESA", (!UteisValidacao.emptyString(empresa.getEstadoSigla())) ? " - " + empresa.getEstadoSigla() : "")
                        .replaceAll("#TELEFONE_EMPRESA", empresa.getPrimeiroTelefoneNaoNulo())
                        .replaceAll("#EMAIL_EMPRESA", empresa.getEmail())
                        .replaceAll("#NEW_USER", nome)
                        .replaceAll("#USER_NAME", usuarioMovel.getNome().toLowerCase())
                        .replaceAll("#SENHA", UteisValidacao.emptyString(senha) ? "A senha não foi alterada" : senha)
                        .replaceAll("#URL_APP", app_personalizado ? app_personalizado_url : urlApp)
                        .replaceAll("#URL_PLAY", urlAppPlay)
                        .replaceAll("#URL_MACA", urlAppApple)
                        .replaceAll("#APP_NOME", nomeApp)
                        .replaceAll("#URL_FOTO_EMPRESA", (urlFotoEmpresa == null) ?
                                "https://app.pactosolucoes.com.br/midias/email_app/novo_logo_pacto.png" : urlFotoEmpresa);
                return new StringBuilder(aux);
            }
        } finally {
            empresaDAO = null;
        }
    }

    public String consultarNomeUsuarioPorCodigoCliente(Integer codigoCliente, int nivelmontardadosDadosbasicos) throws Exception {
        String s = "SELECT nome FROM usuariomovel WHERE cliente = " + codigoCliente;
        try (ResultSet rs = criarConsulta(s, con)) {
            if (rs.next()) {
                return rs.getString("nome");
            }
        }
        return null;
    }
}

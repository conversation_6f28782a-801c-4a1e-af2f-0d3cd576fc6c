package negocio.facade.jdbc.plano;

import negocio.comuns.plano.ProdutoVO;
import java.util.Iterator;
import negocio.facade.jdbc.arquitetura.*;

import java.sql.Connection;
import java.sql.ResultSet;
import java.sql.PreparedStatement;
import java.sql.Statement;
import java.util.List;
import java.util.ArrayList;
import negocio.comuns.utilitarias.*;
import negocio.comuns.plano.PlanoProdutoSugeridoVO;
import negocio.comuns.plano.ProdutoSugeridoVO;
import negocio.interfaces.plano.PlanoProdutoSugeridoInterfaceFacade;

/**
 * Classe de persistência que encapsula todas as operações de manipulação dos dados da classe <code>ProdutoSugeridoVO</code>.
 * Responsável por implementar operações como incluir, alterar, excluir e consultar pertinentes a classe <code>ProdutoSugeridoVO</code>.
 * Encapsula toda a interação com o banco de dados.
 * @see ProdutoSugeridoVO
 * @see SuperEntidade
 * @see Modalidade
 */
public class PlanoProdutoSugerido extends SuperEntidade implements PlanoProdutoSugeridoInterfaceFacade {    

    public PlanoProdutoSugerido() throws Exception {
        super();
        setIdEntidade("Plano");
    }

    public PlanoProdutoSugerido(Connection con) throws Exception {
    	super(con);
        setIdEntidade("Plano");
    }

	/**
     * Operação responsável por retornar um novo objeto da classe <code>ProdutoSugeridoVO</code>.
     */
    public PlanoProdutoSugeridoVO novo() throws Exception {
        incluir(getIdEntidade());
        PlanoProdutoSugeridoVO obj = new PlanoProdutoSugeridoVO();
        return obj;
    }

    /**
     * Operação responsável por incluir no banco de dados um objeto da classe <code>ProdutoSugeridoVO</code>.
     * Primeiramente valida os dados (<code>validarDados</code>) do objeto. Verifica a conexão com o banco de dados e a permissão do usuário
     * para realizar esta operacão na entidade.
     * Isto, através da operação <code>incluir</code> da superclasse.
     * @param obj  Objeto da classe <code>ProdutoSugeridoVO</code> que será gravado no banco de dados.
     * @exception Exception Caso haja problemas de conexão, restrição de acesso ou validação de dados.
     */
    public void incluir(PlanoProdutoSugeridoVO obj) throws Exception {
        PlanoProdutoSugeridoVO.validarDados(obj);
        obj.realizarUpperCaseDados();
        String sql = "INSERT INTO PlanoProdutoSugerido( plano, produto, Obrigatorio, valorProduto ) VALUES ( ?, ?, ?, ? )";
        PreparedStatement sqlInserir = con.prepareStatement(sql);
        if (obj.getPlano().intValue() != 0) {
            sqlInserir.setInt(1, obj.getPlano().intValue());
        } else {
            sqlInserir.setNull(1, 0);
        }
        if (obj.getProduto().getCodigo().intValue() != 0) {
            sqlInserir.setInt(2, obj.getProduto().getCodigo().intValue());
        } else {
            sqlInserir.setNull(2, 0);
        }
        sqlInserir.setBoolean(3, obj.isObrigatorio().booleanValue());
        sqlInserir.setDouble(4, obj.getValorProduto());
        sqlInserir.execute();
        obj.setCodigo(obterValorChavePrimariaCodigo());
        obj.setNovoObj(new Boolean(false));
    }

    /**
     * Operação responsável por alterar no BD os dados de um objeto da classe <code>ProdutoSugeridoVO</code>.
     * Sempre utiliza a chave primária da classe como atributo para localização do registro a ser alterado.
     * Primeiramente valida os dados (<code>validarDados</code>) do objeto. Verifica a conexão com o banco de dados e a permissão do usuário
     * para realizar esta operacão na entidade.
     * Isto, através da operação <code>alterar</code> da superclasse.
     * @param obj    Objeto da classe <code>ProdutoSugeridoVO</code> que será alterada no banco de dados.
     * @exception Execption Caso haja problemas de conexão, restrição de acesso ou validação de dados.
     */
    public void alterar(PlanoProdutoSugeridoVO obj) throws Exception {
        PlanoProdutoSugeridoVO.validarDados(obj);
        alterar(getIdEntidade());
        obj.realizarUpperCaseDados();
        String sql = "UPDATE PlanoProdutoSugerido set plano=?, produto=?, Obrigatorio=?, valorProduto=?, ativoplano=? WHERE ((codigo = ?))";
        PreparedStatement sqlAlterar = con.prepareStatement(sql);
        if (obj.getPlano().intValue() != 0) {
            sqlAlterar.setInt(1, obj.getPlano().intValue());
        } else {
            sqlAlterar.setNull(1, 0);
        }
        if (obj.getProduto().getCodigo().intValue() != 0) {
            sqlAlterar.setInt(2, obj.getProduto().getCodigo().intValue());
        } else {
            sqlAlterar.setNull(2, 0);
        }
        sqlAlterar.setBoolean(3, obj.isObrigatorio().booleanValue());
        sqlAlterar.setDouble(4, obj.getValorProduto());
        sqlAlterar.setBoolean(5, obj.getAtivoPlano());
        sqlAlterar.setInt(6, obj.getCodigo().intValue());
        sqlAlterar.execute();
    }

    /**
     * Operação responsável por excluir no BD um objeto da classe <code>ProdutoSugeridoVO</code>.
     * Sempre localiza o registro a ser excluído através da chave primária da entidade.
     * Primeiramente verifica a conexão com o banco de dados e a permissão do usuário
     * para realizar esta operacão na entidade.
     * Isto, através da operação <code>excluir</code> da superclasse.
     * @param obj    Objeto da classe <code>ProdutoSugeridoVO</code> que será removido no banco de dados.
     * @exception Execption Caso haja problemas de conexão ou restrição de acesso.
     */
    public void excluir(PlanoProdutoSugeridoVO obj) throws Exception {
        excluir(getIdEntidade());
        String sql = "DELETE FROM PlanoProdutoSugerido WHERE ((codigo = ?))";
        PreparedStatement sqlExcluir = con.prepareStatement(sql);
        sqlExcluir.setInt(1, obj.getCodigo().intValue());
        sqlExcluir.execute();
    }

    /**
     * Responsável por realizar uma consulta de <code>ProdutoSugerido</code> através do valor do atributo 
     * <code>descricao</code> da classe <code>Produto</code>
     * Faz uso da operação <code>montarDadosConsulta</code> que realiza o trabalho de prerarar o List resultante.
     * @return  List Contendo vários objetos da classe <code>ProdutoSugeridoVO</code> resultantes da consulta.
     * @exception Execption Caso haja problemas de conexão ou restrição de acesso.
     */
    public List consultarPorDescricaoProduto(String valorConsulta, int nivelMontarDados) throws Exception {
        consultar(getIdEntidade(), true);
        String sqlStr = "SELECT PlanoProdutoSugerido.* FROM PlanoProdutoSugerido, Produto WHERE PlanoProdutoSugerido.produto = Produto.codigo and upper( Produto.descricao ) like('" + valorConsulta.toUpperCase() + "%') ORDER BY Produto.descricao";
        Statement stm = con.createStatement();
        ResultSet tabelaResultado = stm.executeQuery(sqlStr);
        return montarDadosConsulta(tabelaResultado, nivelMontarDados, this.con);
    }

    public List<PlanoProdutoSugeridoVO> consultarPorProduto(Integer codigoProduto, int nivelMontarDados) throws Exception {
        StringBuilder sql = new StringBuilder();
        sql.append("SELECT *\n");
        sql.append("FROM PlanoProdutoSugerido\n");
        sql.append("WHERE produto = ?\n");
        sql.append("ORDER BY codigo\n");
        PreparedStatement preparedStatement = con.prepareStatement(sql.toString());
        preparedStatement.setInt(1, codigoProduto);
        ResultSet tabelaResultado = preparedStatement.executeQuery();
        return montarDadosConsulta(tabelaResultado, nivelMontarDados, this.con);
    }

    /**
     * Responsável por realizar uma consulta de <code>ProdutoSugerido</code> através do valor do atributo 
     * <code>nome</code> da classe <code>Modalidade</code>
     * Faz uso da operação <code>montarDadosConsulta</code> que realiza o trabalho de prerarar o List resultante.
     * @return  List Contendo vários objetos da classe <code>ProdutoSugeridoVO</code> resultantes da consulta.
     * @exception Execption Caso haja problemas de conexão ou restrição de acesso.
     */
    public List consultarPorNomePlano(String valorConsulta, int nivelMontarDados) throws Exception {
        consultar(getIdEntidade(), true);
        String sqlStr = "SELECT PlanoProdutoSugerido.* FROM PlanoProdutoSugerido, Plano WHERE PlanoProdutoSugerido.plano = Plano.codigo and upper( Plano.descricao ) like('" + valorConsulta.toUpperCase() + "%') ORDER BY Plano.descricao";
        Statement stm = con.createStatement();
        ResultSet tabelaResultado = stm.executeQuery(sqlStr);
        return montarDadosConsulta(tabelaResultado, nivelMontarDados, this.con);
    }

    /**
     * Responsável por realizar uma consulta de <code>ProdutoSugerido</code> através do valor do atributo 
     * <code>Integer codigo</code>. Retorna os objetos com valores iguais ou superiores ao parâmetro fornecido.
     * Faz uso da operação <code>montarDadosConsulta</code> que realiza o trabalho de prerarar o List resultante.
     * @param   controlarAcesso Indica se a aplicação deverá verificar se o usuário possui permissão para esta consulta ou não.
     * @return  List Contendo vários objetos da classe <code>ProdutoSugeridoVO</code> resultantes da consulta.
     * @exception Exception Caso haja problemas de conexão ou restrição de acesso.
     */
    public List consultarPorCodigo(Integer valorConsulta, boolean controlarAcesso, int nivelMontarDados) throws Exception {
        consultar(getIdEntidade(), controlarAcesso);
        String sqlStr = "SELECT * FROM PlanoProdutoSugerido WHERE codigo >= " + valorConsulta.intValue() + " ORDER BY codigo";
        Statement stm = con.createStatement();
        ResultSet tabelaResultado = stm.executeQuery(sqlStr);
        return (montarDadosConsulta(tabelaResultado, nivelMontarDados, this.con));
    }

    /**
     * Responsável por montar os dados de vários objetos, resultantes de uma consulta ao banco de dados (<code>ResultSet</code>).
     * Faz uso da operação <code>montarDados</code> que realiza o trabalho para um objeto por vez.
     * @return  List Contendo vários objetos da classe <code>ProdutoSugeridoVO</code> resultantes da consulta.
     */
    public static List montarDadosConsulta(ResultSet tabelaResultado, int nivelMontarDados, Connection con) throws Exception {
        List vetResultado = new ArrayList();
        while (tabelaResultado.next()) {
            PlanoProdutoSugeridoVO obj = new PlanoProdutoSugeridoVO();
            obj = montarDados(tabelaResultado, nivelMontarDados, con);
            vetResultado.add(obj);
        }
        return vetResultado;
    }

    /**
     * Responsável por montar os dados resultantes de uma consulta ao banco de dados (<code>ResultSet</code>)
     * em um objeto da classe <code>ProdutoSugeridoVO</code>.
     * @return  O objeto da classe <code>ProdutoSugeridoVO</code> com os dados devidamente montados.
     */
    public static PlanoProdutoSugeridoVO montarDados(ResultSet dadosSQL, int nivelMontarDados, Connection con) throws Exception {
        PlanoProdutoSugeridoVO obj = new PlanoProdutoSugeridoVO();
        obj.setCodigo(new Integer(dadosSQL.getInt("codigo")));
        obj.setPlano(new Integer(dadosSQL.getInt("plano")));
        obj.getProduto().setCodigo(new Integer(dadosSQL.getInt("produto")));
        obj.setObrigatorio(new Boolean(dadosSQL.getBoolean("Obrigatorio")));
        obj.setValorProduto(new Double(dadosSQL.getDouble("valorProduto")));
        obj.setAtivoPlano(dadosSQL.getBoolean("ativoplano"));
        if (obj.getObrigatorio()) {
            obj.setProdutoSugeridoEscolhida(true);
        }
        obj.setNovoObj(new Boolean(false));
        if (nivelMontarDados == Uteis.NIVELMONTARDADOS_DADOSBASICOS) {
            return obj;
        }

        montarDadosProduto(obj, nivelMontarDados, con);
        return obj;
    }

    /**
     * Operação responsável por montar os dados de um objeto da classe <code>ProdutoVO</code> relacionado ao objeto <code>ProdutoSugeridoVO</code>.
     * Faz uso da chave primária da classe <code>ProdutoVO</code> para realizar a consulta.
     * @param obj  Objeto no qual será montado os dados consultados.
     */
    public static void montarDadosProduto(PlanoProdutoSugeridoVO obj, int nivelMontarDados, Connection con) throws Exception {
        if (obj.getProduto().getCodigo().intValue() == 0) {
            obj.setProduto(new ProdutoVO());
            return;
        }
        Produto produto = new Produto(con);
        obj.setProduto(produto.consultarPorChavePrimaria(obj.getProduto().getCodigo(), nivelMontarDados));
        produto = null;
    }

    /**
     * Operação responsável por excluir todos os objetos da <code>ProdutoSugeridoVO</code> no BD.
     * Faz uso da operação <code>excluir</code> disponível na classe <code>ProdutoSugerido</code>.
     * @param <code>modalidade</code> campo chave para exclusão dos objetos no BD.
     * @exception Exception  Erro de conexão com o BD ou restrição de acesso a esta operação.
     */
    public void excluirPlanoProdutoSugeridos(Integer plano) throws Exception {
        excluir(getIdEntidade());
        String sql = "DELETE FROM PlanoProdutoSugerido WHERE (plano = ?)";
        PreparedStatement sqlExcluir = con.prepareStatement(sql);
        sqlExcluir.setInt(1, plano.intValue());
        sqlExcluir.execute();
    }

    /**
     * Operação responsável por alterar todos os objetos da <code>ProdutoSugeridoVO</code> contidos em um Hashtable no BD.
     * Faz uso da operação <code>excluirProdutoSugeridos</code> e <code>incluirProdutoSugeridos</code> disponíveis na classe <code>ProdutoSugerido</code>.
     * @param objetos  List com os objetos a serem alterados ou incluídos no BD.
     * @exception Exception  Erro de conexão com o BD ou restrição de acesso a esta operação.
     */
    public void alterarPlanoProdutoSugeridos(Integer plano, List objetos) throws Exception {
        String str = "DELETE FROM PlanoProdutoSugerido WHERE plano = " + plano.intValue();
        Iterator i = objetos.iterator();
        while (i.hasNext()) {
            PlanoProdutoSugeridoVO objeto = (PlanoProdutoSugeridoVO) i.next();
            str += " AND codigo <> " + objeto.getCodigo().intValue();
        }
        PreparedStatement sqlExcluir = con.prepareStatement(str);
        sqlExcluir.execute();
        Iterator e = objetos.iterator();
        while (e.hasNext()) {
            PlanoProdutoSugeridoVO obj = (PlanoProdutoSugeridoVO) e.next();
            if (obj.getCodigo().equals(new Integer(0))) {
                obj.setPlano(plano);
                incluir(obj);
            } else {
                alterar(obj);
            }
        }
        //        excluirPlanoProdutoSugeridos( plano);
//        incluirPlanoProdutoSugeridos( plano, objetos );
    }

    /**
     * Operação responsável por incluir objetos da <code>ProdutoSugeridoVO</code> no BD.
     * Garantindo o relacionamento com a entidade principal <code>plano.Modalidade</code> através do atributo de vínculo.
     * @param objetos List contendo os objetos a serem gravados no BD da classe.
     * @exception Exception  Erro de conexão com o BD ou restrição de acesso a esta operação.
     */
    public void incluirPlanoProdutoSugeridos(Integer planoPrm, List objetos) throws Exception {
        Iterator e = objetos.iterator();
        while (e.hasNext()) {
            PlanoProdutoSugeridoVO obj = (PlanoProdutoSugeridoVO) e.next();
            obj.setPlano(planoPrm);
            incluir(obj);
        }
    }

    /**
     * Operação responsável por consultar todos os <code>ProdutoSugeridoVO</code> relacionados a um objeto da classe <code>plano.Modalidade</code>.
     * @param modalidade  Atributo de <code>plano.Modalidade</code> a ser utilizado para localizar os objetos da classe <code>ProdutoSugeridoVO</code>.
     * @param produtoAtivoNoPlano Atributo de <code>plano.PlanoProdutoSugerido</code> utilizado para trazer os produtos que estão ativos ou não no plano.
     *                            Caso nulo o filtro é ignorado.
     * @return List  Contendo todos os objetos da classe <code>ProdutoSugeridoVO</code> resultantes da consulta.
     * @exception Exception  Erro de conexão com o BD ou restrição de acesso a esta operação.
     */
    public List consultarPlanoProdutoSugeridos(Integer plano, boolean produtoAtivo, int nivelMontarDados) throws Exception {
        consultar(getIdEntidade());
        List objetos = new ArrayList();
        StringBuilder sql = new StringBuilder();
        sql.append("SELECT * FROM PlanoProdutoSugerido WHERE plano = " + plano.toString());
        if (produtoAtivo) {
            sql.append(" AND produto NOT IN (SELECT codigo FROM produto WHERE desativado = TRUE)");
        }

        PreparedStatement sqlConsulta = con.prepareStatement(sql.toString());
        ResultSet resultado = sqlConsulta.executeQuery();
        while (resultado.next()) {
            PlanoProdutoSugeridoVO novoObj = new PlanoProdutoSugeridoVO();
            novoObj = PlanoProdutoSugerido.montarDados(resultado, nivelMontarDados, this.con);
            objetos.add(novoObj);
        }
        return objetos;
    }

    /**
     * Operação responsável por localizar um objeto da classe <code>ProdutoSugeridoVO</code>
     * através de sua chave primária. 
     * @exception Exception Caso haja problemas de conexão ou localização do objeto procurado.
     */
    public PlanoProdutoSugeridoVO consultarPorChavePrimaria(Integer codigoPrm, int nivelMontarDados) throws Exception {
        consultar(getIdEntidade(), false);
        String sql = "SELECT * FROM PlanoProdutoSugerido WHERE codigo = ?";
        PreparedStatement sqlConsultar = con.prepareStatement(sql);
        sqlConsultar.setInt(1, codigoPrm.intValue());
        ResultSet tabelaResultado = sqlConsultar.executeQuery();
        if (!tabelaResultado.next()) {
            throw new ConsistirException("Dados Não Encontrados ( ProdutoSugerido ).");
        }
        return (montarDados(tabelaResultado, nivelMontarDados, this.con));
    }

    public boolean vinculadoAoContrato(PlanoProdutoSugeridoVO pps) throws Exception {
        StringBuilder sql = new StringBuilder();
        sql.append(" SELECT CPPS.codigo ");
        sql.append(" FROM contratoplanoprodutosugerido CPPS ");
        sql.append(" WHERE CPPS.planoprodutosugerido = ? ");
        sql.append(" LIMIT 1 ");

        PreparedStatement ps = con.prepareStatement(sql.toString());
        ps.setInt(1, pps.getCodigo().intValue());
        return ps.executeQuery().next();
    }

}

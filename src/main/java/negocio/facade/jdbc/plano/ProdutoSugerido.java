package negocio.facade.jdbc.plano;

import negocio.comuns.plano.ProdutoVO;
import java.util.Iterator;
import negocio.comuns.plano.ProdutoSugeridoVO;
import negocio.facade.jdbc.arquitetura.*;

import java.sql.Connection;
import java.sql.ResultSet;
import java.sql.PreparedStatement;
import java.sql.Statement;
import java.util.List;
import java.util.ArrayList;
import negocio.comuns.utilitarias.*;
import negocio.interfaces.plano.ProdutoSugeridoInterfaceFacade;

/**
 * Classe de persistência que encapsula todas as operações de manipulação dos dados da classe <code>ProdutoSugeridoVO</code>.
 * Responsável por implementar operações como incluir, alterar, excluir e consultar pertinentes a classe <code>ProdutoSugeridoVO</code>.
 * Encapsula toda a interação com o banco de dados.
 * @see ProdutoSugeridoVO
 * @see SuperEntidade
 * @see Modalidade
 */
public class ProdutoSugerido extends SuperEntidade implements ProdutoSugeridoInterfaceFacade {    

    public ProdutoSugerido() throws Exception {
        super();
        setIdEntidade("Modalidade");
    }

    public ProdutoSugerido(Connection con) throws Exception {
        super(con);
        setIdEntidade("Modalidade");
    
	}

	/**
     * Operação responsável por retornar um novo objeto da classe <code>ProdutoSugeridoVO</code>.
     */
    public ProdutoSugeridoVO novo() throws Exception {
        incluir(getIdEntidade());
        return new ProdutoSugeridoVO();
    }

    /**
     * Operação responsável por incluir no banco de dados um objeto da classe <code>ProdutoSugeridoVO</code>.
     * Primeiramente valida os dados (<code>validarDados</code>) do objeto. Verifica a conexão com o banco de dados e a permissão do usuário
     * para realizar esta operacão na entidade.
     * Isto, através da operação <code>incluir</code> da superclasse.
     * @param obj  Objeto da classe <code>ProdutoSugeridoVO</code> que será gravado no banco de dados.
     * @exception Exception Caso haja problemas de conexão, restrição de acesso ou validação de dados.
     */
    public void incluir(ProdutoSugeridoVO obj) throws Exception {
        ProdutoSugeridoVO.validarDados(obj);
        incluir(getIdEntidade());
        obj.realizarUpperCaseDados();
        String sql = "INSERT INTO ProdutoSugerido( modalidade, produto, Obrigatorio ) VALUES ( ?, ?, ? )";
        try (PreparedStatement sqlInserir = con.prepareStatement(sql)) {
            if (obj.getModalidade() != 0) {
                sqlInserir.setInt(1, obj.getModalidade());
            } else {
                sqlInserir.setNull(1, 0);
            }
            if (obj.getProduto().getCodigo() != 0) {
                sqlInserir.setInt(2, obj.getProduto().getCodigo());
            } else {
                sqlInserir.setNull(2, 0);
            }
            sqlInserir.setBoolean(3, obj.isObrigatorio());
            sqlInserir.execute();
        }
        obj.setCodigo(obterValorChavePrimariaCodigo());
        obj.setNovoObj(false);
    }

    /**
     * Operação responsável por alterar no BD os dados de um objeto da classe <code>ProdutoSugeridoVO</code>.
     * Sempre utiliza a chave primária da classe como atributo para localização do registro a ser alterado.
     * Primeiramente valida os dados (<code>validarDados</code>) do objeto. Verifica a conexão com o banco de dados e a permissão do usuário
     * para realizar esta operacão na entidade.
     * Isto, através da operação <code>alterar</code> da superclasse.
     * @param obj    Objeto da classe <code>ProdutoSugeridoVO</code> que será alterada no banco de dados.
     * @exception Exception Caso haja problemas de conexão, restrição de acesso ou validação de dados.
     */
    public void alterar(ProdutoSugeridoVO obj) throws Exception {
        ProdutoSugeridoVO.validarDados(obj);
        alterar(getIdEntidade());
        obj.realizarUpperCaseDados();
        String sql = "UPDATE ProdutoSugerido set modalidade=?, produto=?, Obrigatorio=? WHERE ((codigo = ?))";
        try (PreparedStatement sqlAlterar = con.prepareStatement(sql)) {
            if (obj.getModalidade() != 0) {
                sqlAlterar.setInt(1, obj.getModalidade());
            } else {
                sqlAlterar.setNull(1, 0);
            }
            if (obj.getProduto().getCodigo() != 0) {
                sqlAlterar.setInt(2, obj.getProduto().getCodigo());
            } else {
                sqlAlterar.setNull(2, 0);
            }
            sqlAlterar.setBoolean(3, obj.isObrigatorio());
            sqlAlterar.setInt(4, obj.getCodigo());
            sqlAlterar.execute();
        }
    }

    /**
     * Operação responsável por excluir no BD um objeto da classe <code>ProdutoSugeridoVO</code>.
     * Sempre localiza o registro a ser excluído através da chave primária da entidade.
     * Primeiramente verifica a conexão com o banco de dados e a permissão do usuário
     * para realizar esta operacão na entidade.
     * Isto, através da operação <code>excluir</code> da superclasse.
     * @param obj    Objeto da classe <code>ProdutoSugeridoVO</code> que será removido no banco de dados.
     * @exception Exception Caso haja problemas de conexão ou restrição de acesso.
     */
    public void excluir(ProdutoSugeridoVO obj) throws Exception {
        excluir(getIdEntidade());
        String sql = "DELETE FROM ProdutoSugerido WHERE ((codigo = ?))";
        try (PreparedStatement sqlExcluir = con.prepareStatement(sql)) {
            sqlExcluir.setInt(1, obj.getCodigo());
            sqlExcluir.execute();
        }
    }

    /**
     * Responsável por realizar uma consulta de <code>ProdutoSugerido</code> através do valor do atributo 
     * <code>descricao</code> da classe <code>Produto</code>
     * Faz uso da operação <code>montarDadosConsulta</code> que realiza o trabalho de prerarar o List resultante.
     * @return  List Contendo vários objetos da classe <code>ProdutoSugeridoVO</code> resultantes da consulta.
     * @exception Exception Caso haja problemas de conexão ou restrição de acesso.
     */
    public List consultarPorDescricaoProduto(String valorConsulta, int nivelMontarDados) throws Exception {
        consultar(getIdEntidade(), true);
        String sqlStr = "SELECT ProdutoSugerido.* FROM ProdutoSugerido, Produto WHERE ProdutoSugerido.produto = Produto.codigo and upper( Produto.descricao ) like('" + valorConsulta.toUpperCase() + "%') ORDER BY Produto.descricao";
        try (Statement stm = con.createStatement()) {
            try (ResultSet tabelaResultado = stm.executeQuery(sqlStr)) {
                return montarDadosConsulta(tabelaResultado, nivelMontarDados, this.con);
            }
        }
    }

    /**
     * Responsável por realizar uma consulta de <code>ProdutoSugerido</code> através do valor do atributo 
     * <code>nome</code> da classe <code>Modalidade</code>
     * Faz uso da operação <code>montarDadosConsulta</code> que realiza o trabalho de prerarar o List resultante.
     * @return  List Contendo vários objetos da classe <code>ProdutoSugeridoVO</code> resultantes da consulta.
     * @exception Exception Caso haja problemas de conexão ou restrição de acesso.
     */
    public List consultarPorNomeModalidade(String valorConsulta, int nivelMontarDados) throws Exception {
        consultar(getIdEntidade(), true);
        String sqlStr = "SELECT ProdutoSugerido.* FROM ProdutoSugerido, Modalidade WHERE ProdutoSugerido.modalidade = Modalidade.codigo and upper( Modalidade.nome ) like('" + valorConsulta.toUpperCase() + "%') ORDER BY Modalidade.nome";
        try (Statement stm = con.createStatement()) {
            try (ResultSet tabelaResultado = stm.executeQuery(sqlStr)) {
                return montarDadosConsulta(tabelaResultado, nivelMontarDados, this.con);
            }
        }
    }

    /**
     * Responsável por realizar uma consulta de <code>ProdutoSugerido</code> através do valor do atributo 
     * <code>Integer codigo</code>. Retorna os objetos com valores iguais ou superiores ao parâmetro fornecido.
     * Faz uso da operação <code>montarDadosConsulta</code> que realiza o trabalho de prerarar o List resultante.
     * @param   controlarAcesso Indica se a aplicação deverá verificar se o usuário possui permissão para esta consulta ou não.
     * @return  List Contendo vários objetos da classe <code>ProdutoSugeridoVO</code> resultantes da consulta.
     * @exception Exception Caso haja problemas de conexão ou restrição de acesso.
     */
    public List consultarPorCodigo(Integer valorConsulta, boolean controlarAcesso, int nivelMontarDados) throws Exception {
        consultar(getIdEntidade(), controlarAcesso);
        String sqlStr = "SELECT * FROM ProdutoSugerido WHERE codigo >= " + valorConsulta + " ORDER BY codigo";
        try (Statement stm = con.createStatement()) {
            try (ResultSet tabelaResultado = stm.executeQuery(sqlStr)) {
                return (montarDadosConsulta(tabelaResultado, nivelMontarDados, this.con));
            }
        }
    }

    /**
     * Responsável por montar os dados de vários objetos, resultantes de uma consulta ao banco de dados (<code>ResultSet</code>).
     * Faz uso da operação <code>montarDados</code> que realiza o trabalho para um objeto por vez.
     * @return  List Contendo vários objetos da classe <code>ProdutoSugeridoVO</code> resultantes da consulta.
     */
    public static List montarDadosConsulta(ResultSet tabelaResultado, int nivelMontarDados, Connection con) throws Exception {
        List vetResultado = new ArrayList();
        while (tabelaResultado.next()) {
            ProdutoSugeridoVO obj = montarDados(tabelaResultado, nivelMontarDados, con);
            vetResultado.add(obj);
        }
        return vetResultado;
    }

    /**
     * Responsável por montar os dados resultantes de uma consulta ao banco de dados (<code>ResultSet</code>)
     * em um objeto da classe <code>ProdutoSugeridoVO</code>.
     * @return  O objeto da classe <code>ProdutoSugeridoVO</code> com os dados devidamente montados.
     */
    public static ProdutoSugeridoVO montarDados(ResultSet dadosSQL, int nivelMontarDados, Connection con) throws Exception {
        ProdutoSugeridoVO obj = new ProdutoSugeridoVO();
        obj.setCodigo(new Integer(dadosSQL.getInt("codigo")));
        obj.setModalidade(dadosSQL.getInt("modalidade"));
        obj.getProduto().setCodigo(new Integer(dadosSQL.getInt("produto")));
        obj.setObrigatorio(dadosSQL.getBoolean("Obrigatorio"));
        obj.setNovoObj(false);
        if (obj.getObrigatorio()) {
            obj.setProdutoSugeridoEscolhida(true);
        }

        if (nivelMontarDados == Uteis.NIVELMONTARDADOS_DADOSBASICOS) {
            return obj;
        }
        montarDadosProduto(obj, nivelMontarDados, con);

        return obj;
    }

    /**
     * Operação responsável por montar os dados de um objeto da classe <code>ProdutoVO</code> relacionado ao objeto <code>ProdutoSugeridoVO</code>.
     * Faz uso da chave primária da classe <code>ProdutoVO</code> para realizar a consulta.
     * @param obj  Objeto no qual será montado os dados consultados.
     */
    public static void montarDadosProduto(ProdutoSugeridoVO obj, int nivelMontarDados, Connection con) throws Exception {
        if (obj.getProduto().getCodigo() == 0) {
            obj.setProduto(new ProdutoVO());
            return;
        }
        Produto produto = new Produto(con);
        obj.setProduto(produto.consultarPorChavePrimaria(obj.getProduto().getCodigo(), nivelMontarDados));
        produto = null;
    }

    /**
     * Operação responsável por excluir todos os objetos da <code>ProdutoSugeridoVO</code> no BD.
     * Faz uso da operação <code>excluir</code> disponível na classe <code>ProdutoSugerido</code>.
     * @param modalidade campo chave para exclusão dos objetos no BD.
     * @exception Exception  Erro de conexão com o BD ou restrição de acesso a esta operação.
     */
    public void excluirProdutoSugeridos(Integer modalidade) throws Exception {
        excluir(getIdEntidade());
        String sql = "DELETE FROM ProdutoSugerido WHERE (modalidade = ?)";
        try (PreparedStatement sqlExcluir = con.prepareStatement(sql)) {
            sqlExcluir.setInt(1, modalidade);
            sqlExcluir.execute();
        }
    }

    /**
     * Operação responsável por alterar todos os objetos da <code>ProdutoSugeridoVO</code> contidos em um Hashtable no BD.
     * Faz uso da operação <code>excluirProdutoSugeridos</code> e <code>incluirProdutoSugeridos</code> disponíveis na classe <code>ProdutoSugerido</code>.
     * @param objetos  List com os objetos a serem alterados ou incluídos no BD.
     * @exception Exception  Erro de conexão com o BD ou restrição de acesso a esta operação.
     */
    public void alterarProdutoSugeridos(Integer modalidade, List objetos) throws Exception {
        String str = "DELETE FROM produtoSugerido WHERE modalidade = " + modalidade;
        Iterator i = objetos.iterator();
        while (i.hasNext()) {
            ProdutoSugeridoVO objeto = (ProdutoSugeridoVO) i.next();
            str += " AND codigo <> " + objeto.getCodigo();
        }
        try (PreparedStatement sqlExcluir = con.prepareStatement(str)) {
            sqlExcluir.execute();
        }
        Iterator e = objetos.iterator();
        while (e.hasNext()) {
            ProdutoSugeridoVO obj = (ProdutoSugeridoVO) e.next();
            if (obj.getCodigo().equals(new Integer(0))) {
                obj.setModalidade(modalidade);
                incluir(obj);
            } else {
                alterar(obj);
            }
        }

//        excluirProdutoSugeridos( modalidade );
//        incluirProdutoSugeridos( modalidade, objetos );
    }

    /**
     * Operação responsável por incluir objetos da <code>ProdutoSugeridoVO</code> no BD.
     * Garantindo o relacionamento com a entidade principal <code>plano.Modalidade</code> através do atributo de vínculo.
     * @param objetos List contendo os objetos a serem gravados no BD da classe.
     * @exception Exception  Erro de conexão com o BD ou restrição de acesso a esta operação.
     */
    public void incluirProdutoSugeridos(Integer modalidadePrm, List objetos) throws Exception {
        Iterator e = objetos.iterator();
        while (e.hasNext()) {
            ProdutoSugeridoVO obj = (ProdutoSugeridoVO) e.next();
            obj.setModalidade(modalidadePrm);
            incluir(obj);
        }
    }

    /**
     * Operação responsável por consultar todos os <code>ProdutoSugeridoVO</code> relacionados a um objeto da classe <code>plano.Modalidade</code>.
     * @param modalidade  Atributo de <code>plano.Modalidade</code> a ser utilizado para localizar os objetos da classe <code>ProdutoSugeridoVO</code>.
     * @return List  Contendo todos os objetos da classe <code>ProdutoSugeridoVO</code> resultantes da consulta.
     * @exception Exception  Erro de conexão com o BD ou restrição de acesso a esta operação.
     */
    public List consultarProdutoSugeridos(Integer modalidade, int nivelMontarDados) throws Exception {
        consultar(getIdEntidade());
        List objetos = new ArrayList();
        String sql = "SELECT * FROM ProdutoSugerido WHERE modalidade = ?";
        try (PreparedStatement sqlConsulta = con.prepareStatement(sql)) {
            sqlConsulta.setInt(1, modalidade);
            try (ResultSet resultado = sqlConsulta.executeQuery()) {
                while (resultado.next()) {
                    ProdutoSugeridoVO novoObj = ProdutoSugerido.montarDados(resultado, nivelMontarDados, this.con);
                    objetos.add(novoObj);
                }
            }
        }
        return objetos;
    }

    /**
     * Operação responsável por localizar um objeto da classe <code>ProdutoSugeridoVO</code>
     * através de sua chave primária. 
     * @exception Exception Caso haja problemas de conexão ou localização do objeto procurado.
     */
    public ProdutoSugeridoVO consultarPorChavePrimaria(Integer codigoPrm, int nivelMontarDados) throws Exception {
        consultar(getIdEntidade(), false);
        String sql = "SELECT * FROM ProdutoSugerido WHERE codigo = ?";
        try (PreparedStatement sqlConsultar = con.prepareStatement(sql)) {
            sqlConsultar.setInt(1, codigoPrm);
            try (ResultSet tabelaResultado = sqlConsultar.executeQuery()) {
                if (!tabelaResultado.next()) {
                    throw new ConsistirException("Dados Não Encontrados ( ProdutoSugerido ).");
                }
                return (montarDados(tabelaResultado, nivelMontarDados, this.con));
            }
        }
    }
}

package negocio.facade.jdbc.plano;

import java.sql.SQLException;
import java.util.*;

import br.com.pactosolucoes.comuns.util.Formatador;
import negocio.comuns.plano.ComposicaoModalidadeVO;
import negocio.comuns.plano.PlanoComposicaoVO;
import negocio.comuns.plano.PlanoVO;
import negocio.facade.jdbc.utilitarias.Conexao;
import negocio.interfaces.plano.*;
import negocio.comuns.plano.ComposicaoVO;
import negocio.facade.jdbc.arquitetura.*;
import negocio.facade.jdbc.basico.Empresa;

import java.sql.Connection;
import java.sql.ResultSet;
import java.sql.PreparedStatement;
import java.sql.Statement;

import negocio.comuns.utilitarias.*;
import negocio.comuns.basico.EmpresaVO;

/**
 * Classe de persistência que encapsula todas as operações de manipulação dos
 * dados da classe <code>ComposicaoVO</code>. Responsável por implementar
 * operações como incluir, alterar, excluir e consultar pertinentes a classe
 * <code>ComposicaoVO</code>. Encapsula toda a interação com o banco de dados.
 *
 * @see ComposicaoVO
 * @see SuperEntidade
 */
public class Composicao extends SuperEntidade implements ComposicaoInterfaceFacade {

    private Hashtable composicaoModalidades;

    public Composicao() throws Exception {
        super();
        setComposicaoModalidades(new Hashtable());
    }

    public Composicao(Connection con) throws Exception {
        super(con);
        setComposicaoModalidades(new Hashtable());
    }

    /**
     * Operação responsável por retornar um novo objeto da classe
     * <code>ComposicaoVO</code>.
     */
    public ComposicaoVO novo() throws Exception {
        incluir(getIdEntidade());
        ComposicaoVO obj = new ComposicaoVO();
        return obj;
    }

    /**
     * Operação responsável por incluir no banco de dados um objeto da classe
     * <code>ComposicaoVO</code>. Primeiramente valida os dados
     * (<code>validarDados</code>) do objeto. Verifica a conexão com o banco de
     * dados e a permissão do usuário para realizar esta operacão na entidade.
     * Isto, através da operação <code>incluir</code> da superclasse.
     *
     * @param obj Objeto da classe <code>ComposicaoVO</code> que será gravado no
     * banco de dados.
     * @exception Exception Caso haja problemas de conexão, restrição de acesso
     * ou validação de dados.
     */
    public void incluir(ComposicaoVO obj) throws Exception {
        try {
            con.setAutoCommit(false);
            incluirSemCommit(obj, Boolean.TRUE);
            con.commit();
        } catch (Exception e) {
            con.rollback();
            throw e;
        } finally {
            con.setAutoCommit(true);
        }
    }

    public void incluirSemCommit(ComposicaoVO obj) throws Exception {
        incluirSemCommit(obj, true);
    }

    public void incluirSemCommit(ComposicaoVO obj, Boolean salvarCompModalidade) throws Exception {
        try {
            if (salvarCompModalidade) {
                ComposicaoVO.validarDados(obj);
            }
            obj.realizarUpperCaseDados();
            String sql = "INSERT INTO Composicao( descricao, precoComposicao, composicaoAdicional, composicaoDefault, "
                    + "empresa, modalidadesespecificas, qtdeModalidades ) VALUES ( ?, ?, ?, ?, ?, ?, ? )";
            PreparedStatement sqlInserir = con.prepareStatement(sql);
            sqlInserir.setString(1, obj.getDescricao());
            sqlInserir.setDouble(2, obj.getPrecoComposicao().doubleValue());
            sqlInserir.setBoolean(3, obj.isComposicaoAdicional().booleanValue());
            sqlInserir.setBoolean(4, obj.isComposicaoDefault().booleanValue());
            sqlInserir.setInt(5, obj.getEmpresa().getCodigo().intValue());
            sqlInserir.setBoolean(6, obj.isModalidadesEspecificas());
            sqlInserir.setInt(7, obj.getQtdeModalidades());
            sqlInserir.execute();
            obj.setCodigo(obterValorChavePrimariaCodigo());
            if (salvarCompModalidade) {
                ComposicaoModalidade cmDao = new ComposicaoModalidade(con);
                cmDao.incluirComposicaoModalidades(obj.getCodigo(), obj.getComposicaoModalidadeVOs(), obj.isModalidadesEspecificas());
                cmDao = null;
            }
            obj.setNovoObj(false);
        } catch (Exception e) {
            obj.setNovoObj(true);
            throw e;
        }
    }

    /**
     * Operação responsável por alterar no BD os dados de um objeto da classe
     * <code>ComposicaoVO</code>. Sempre utiliza a chave primária da classe como
     * atributo para localização do registro a ser alterado. Primeiramente
     * valida os dados (<code>validarDados</code>) do objeto. Verifica a conexão
     * com o banco de dados e a permissão do usuário para realizar esta operacão
     * na entidade. Isto, através da operação <code>alterar</code> da
     * superclasse.
     *
     * @param obj Objeto da classe <code>ComposicaoVO</code> que será alterada
     * no banco de dados.
     * @exception Execption Caso haja problemas de conexão, restrição de acesso
     * ou validação de dados.
     */
    public void alterar(ComposicaoVO obj) throws Exception {
        try {
            con.setAutoCommit(false);
            ComposicaoVO.validarDados(obj);
            alterar(getIdEntidade());
            obj.realizarUpperCaseDados();
            consultarPlanoQueUtilizaEssaComposicao(obj);
            String sql = "UPDATE Composicao set descricao=?, precoComposicao=?, composicaoAdicional=?, composicaoDefault=?, "
                + "empresa=?, modalidadesespecificas=?, qtdeModalidades=? WHERE codigo = ?";
            PreparedStatement sqlAlterar = con.prepareStatement(sql);
            sqlAlterar.setString(1, obj.getDescricao());
            sqlAlterar.setDouble(2, obj.getPrecoComposicao().doubleValue());
            sqlAlterar.setBoolean(3, obj.isComposicaoAdicional().booleanValue());
            sqlAlterar.setBoolean(4, obj.isComposicaoDefault().booleanValue());
            sqlAlterar.setInt(5, obj.getEmpresa().getCodigo().intValue());
        sqlAlterar.setBoolean(6, obj.isModalidadesEspecificas());
            sqlAlterar.setInt(7, obj.getQtdeModalidades());
            sqlAlterar.setInt(8, obj.getCodigo().intValue());
            sqlAlterar.execute();
        getFacade().getComposicaoModalidade().alterarComposicaoModalidades(obj.getCodigo(), obj.getComposicaoModalidadeVOs(), obj.isModalidadesEspecificas());
            con.commit();
        } catch (Exception e) {
            con.rollback();
            con.setAutoCommit(true);
            throw e;
        } finally {
            con.setAutoCommit(true);
        }
    }

    /**
     * Operação responsável por excluir no BD um objeto da classe
     * <code>ComposicaoVO</code>. Sempre localiza o registro a ser excluído
     * através da chave primária da entidade. Primeiramente verifica a conexão
     * com o banco de dados e a permissão do usuário para realizar esta operacão
     * na entidade. Isto, através da operação <code>excluir</code> da
     * superclasse.
     *
     * @param obj Objeto da classe <code>ComposicaoVO</code> que será removido
     * no banco de dados.
     * @exception Execption Caso haja problemas de conexão ou restrição de
     * acesso.
     */
    public void excluir(ComposicaoVO obj) throws Exception {
        try {
            con.setAutoCommit(false);
            excluir(getIdEntidade());
            String sql = "DELETE FROM Composicao WHERE ((codigo = ?))";
            PreparedStatement sqlExcluir = con.prepareStatement(sql);
            sqlExcluir.setInt(1, obj.getCodigo().intValue());
            sqlExcluir.execute();
            getFacade().getComposicaoModalidade().excluirComposicaoModalidades(obj.getCodigo());
            con.commit();
        } catch (Exception e) {
            con.rollback();
            con.setAutoCommit(true);
            throw e;
        } finally {
            con.setAutoCommit(true);
        }
    }

    public void consultarPlanoQueUtilizaEssaComposicao(ComposicaoVO obj) throws Exception {
        try {
            List lista = new ArrayList();
            lista = getFacade().getPlano().consultarPorComposicao(obj.getCodigo().intValue(), true, Uteis.NIVELMONTARDADOS_DADOSENTIDADESUBORDINADAS);
            if (!lista.isEmpty()) {
                obj.processarPlanoQueUtilizaEssaComposicao(lista);
            }
        } catch (Exception e) {
            throw e;
        }
    }

    /**
     * Responsável por realizar uma consulta de <code>Composicao</code> através
     * do valor do atributo <code>String descricao</code>. Retorna os objetos,
     * com início do valor do atributo idêntico ao parâmetro fornecido. Faz uso
     * da operação <code>montarDadosConsulta</code> que realiza o trabalho de
     * prerarar o List resultante.
     *
     * @param controlarAcesso Indica se a aplicação deverá verificar se o
     * usuário possui permissão para esta consulta ou não.
     * @return List Contendo vários objetos da classe <code>ComposicaoVO</code>
     * resultantes da consulta.
     * @exception Exception Caso haja problemas de conexão ou restrição de
     * acesso.
     */
    public List consultarComposicaoPorModalidadeEmpresa(Integer modalidade, Integer empresa, boolean controlarAcesso, int nivelMontarDados) throws Exception {
        consultar(getIdEntidade(), controlarAcesso);
        String sqlStr = "SELECT composicao.* from composicao "
                + "inner join composicaoModalidade on composicaoModalidade.composicao = composicao.codigo and composicaoModalidade.modalidade = " + modalidade.intValue()
                + " where empresa = " + empresa.intValue();
        Statement stm = con.createStatement();
        ResultSet tabelaResultado = stm.executeQuery(sqlStr);
        return (montarDadosConsulta(tabelaResultado, nivelMontarDados, this.con));
    }

    /**
     * Responsável por realizar uma consulta de <code>Composicao</code> através
     * do valor do atributo <code>String descricao</code>. Retorna os objetos,
     * com início do valor do atributo idêntico ao parâmetro fornecido. Faz uso
     * da operação <code>montarDadosConsulta</code> que realiza o trabalho de
     * prerarar o List resultante.
     *
     * @param controlarAcesso Indica se a aplicação deverá verificar se o
     * usuário possui permissão para esta consulta ou não.
     * @return List Contendo vários objetos da classe <code>ComposicaoVO</code>
     * resultantes da consulta.
     * @exception Exception Caso haja problemas de conexão ou restrição de
     * acesso.
     */
    public List consultarPorDescricao(String valorConsulta, Integer empresa, boolean controlarAcesso, int nivelMontarDados) throws Exception {
        consultar(getIdEntidade(), controlarAcesso);
        String sqlStr = "";
        if (empresa == 0) {
            sqlStr = "SELECT * FROM Composicao WHERE upper( descricao ) like('" + valorConsulta.toUpperCase() + "%') ORDER BY descricao";
        } else {
            sqlStr = "SELECT * FROM Composicao WHERE upper( descricao ) like('" + valorConsulta.toUpperCase() + "%') AND empresa = " + empresa.intValue() + " ORDER BY descricao";
        }

        Statement stm = con.createStatement();
        ResultSet tabelaResultado = stm.executeQuery(sqlStr);
        return (montarDadosConsulta(tabelaResultado, nivelMontarDados, this.con));
    }

    public List consultarPorComposicaoDefault(Boolean valorConsulta, Integer empresa, boolean controlarAcesso, int nivelMontarDados) throws Exception {
        consultar(getIdEntidade(), controlarAcesso);
        String sqlStr = "";
        if (empresa == 0) {
            sqlStr = "SELECT * FROM Composicao WHERE ComposicaoDefault = " + valorConsulta + " ORDER BY composicaoDefault";
        } else {
            sqlStr = "SELECT * FROM Composicao WHERE ComposicaoDefault = " + valorConsulta + " and empresa = " + empresa.intValue() + " ORDER BY composicaoDefault";
        }

        Statement stm = con.createStatement();
        ResultSet tabelaResultado = stm.executeQuery(sqlStr);
        return (montarDadosConsulta(tabelaResultado, nivelMontarDados, this.con));
    }

    /**
     * Responsável por realizar uma consulta de <code>Composicao</code> através
     * do valor do atributo <code>Integer codigo</code>. Retorna os objetos com
     * valores iguais ou superiores ao parâmetro fornecido. Faz uso da operação
     * <code>montarDadosConsulta</code> que realiza o trabalho de prerarar o
     * List resultante.
     *
     * @param controlarAcesso Indica se a aplicação deverá verificar se o
     * usuário possui permissão para esta consulta ou não.
     * @return List Contendo vários objetos da classe <code>ComposicaoVO</code>
     * resultantes da consulta.
     * @exception Exception Caso haja problemas de conexão ou restrição de
     * acesso.
     */
    public List consultarPorCodigo(Integer valorConsulta, Integer empresa, boolean controlarAcesso, int nivelMontarDados) throws Exception {
        consultar(getIdEntidade(), controlarAcesso);
        String sqlStr = "";
        if (empresa == 0) {
            sqlStr = "SELECT * FROM Composicao WHERE codigo >= " + valorConsulta.intValue() + " ORDER BY codigo";
        } else {
            sqlStr = "SELECT * FROM Composicao WHERE codigo >= " + valorConsulta.intValue() + " and empresa =" + empresa.intValue() + " ORDER BY codigo";
        }
        Statement stm = con.createStatement();
        ResultSet tabelaResultado = stm.executeQuery(sqlStr);
        return (montarDadosConsulta(tabelaResultado, nivelMontarDados, this.con));
    }

    /**
     * Operação responsável por localizar um objeto da classe
     * <code>ComposicaoVO</code> através de sua chave primária.
     *
     * @exception Exception Caso haja problemas de conexão ou localização do
     * objeto procurado.
     */
    public ComposicaoVO consultarPorCodigo(Integer codigoPrm, Integer empresa, int nivelMontarDados) throws Exception {
        consultar(getIdEntidade(), false);
        String sqlStr = "";
        if (empresa == 0) {
            sqlStr = "SELECT * FROM Composicao WHERE codigo >= " + codigoPrm.intValue() + " ORDER BY codigo";
        } else {
            sqlStr = "SELECT * FROM Composicao WHERE codigo >= " + codigoPrm.intValue() + " and empresa =" + empresa.intValue() + " ORDER BY codigo";
        }

        // String sql = "SELECT * FROM Composicao WHERE codigo = ? and empresa = ?";
        PreparedStatement sqlConsultar = con.prepareStatement(sqlStr);
        ResultSet tabelaResultado = sqlConsultar.executeQuery();
        if (!tabelaResultado.next()) {
            throw new ConsistirException("Dados Não Encontrados ( Composicao ).");
        }
        return (montarDados(tabelaResultado, nivelMontarDados, this.con));
    }

    /**
     * Responsável por montar os dados de vários objetos, resultantes de uma
     * consulta ao banco de dados (<code>ResultSet</code>). Faz uso da operação
     * <code>montarDados</code> que realiza o trabalho para um objeto por vez.
     *
     * @return List Contendo vários objetos da classe <code>ComposicaoVO</code>
     * resultantes da consulta.
     */
    public static List montarDadosConsulta(ResultSet tabelaResultado, int nivelMontarDados, Connection con) throws Exception {
        List vetResultado = new ArrayList();
        while (tabelaResultado.next()) {
            ComposicaoVO obj = montarDados(tabelaResultado, nivelMontarDados, con);
            vetResultado.add(obj);
        }
        return vetResultado;
    }

    /**
     * Responsável por montar os dados resultantes de uma consulta ao banco de
     * dados (<code>ResultSet</code>) em um objeto da classe
     * <code>ComposicaoVO</code>.
     *
     * @return O objeto da classe <code>ComposicaoVO</code> com os dados
     * devidamente montados.
     */
    public static ComposicaoVO montarDados(ResultSet dadosSQL, int nivelMontarDados, Connection con) throws Exception {
        ComposicaoVO obj = new ComposicaoVO();
        obj.setCodigo(dadosSQL.getInt("codigo"));
        obj.getEmpresa().setCodigo(dadosSQL.getInt("empresa"));
        obj.setDescricao(dadosSQL.getString("descricao"));
        obj.setPrecoComposicao(dadosSQL.getDouble("precoComposicao"));
        obj.setComposicaoAdicional(dadosSQL.getBoolean("composicaoAdicional"));
        obj.setComposicaoDefault(dadosSQL.getBoolean("composicaoDefault"));
        obj.setModalidadesEspecificas(dadosSQL.getBoolean("modalidadesespecificas"));
        obj.setQtdeModalidades(dadosSQL.getInt("qtdeModalidades"));
        obj.setNovoObj(false);
        if (nivelMontarDados == Uteis.NIVELMONTARDADOS_MINIMOS) {
            return obj;
        }
        if (nivelMontarDados == Uteis.NIVELMONTARDADOS_DADOSBASICOS) {
            return obj;
        }
        ComposicaoModalidade composicaoModalidade = new ComposicaoModalidade(con);
        obj.setComposicaoModalidadeVOs(composicaoModalidade.consultarComposicaoModalidades(obj.getCodigo(), nivelMontarDados));
        composicaoModalidade = null;
        if (nivelMontarDados == Uteis.NIVELMONTARDADOS_DADOSENTIDADESUBORDINADAS) {
            return obj;
        }
        montarDadosEmpresa(obj, Uteis.NIVELMONTARDADOS_DADOSBASICOS, con);
        return obj;
    }

    /**
     * Operação responsável por montar os dados de um objeto da classe
     * <code>EmpresaVO</code> relacionado ao objeto <code>PlanoVO</code>. Faz
     * uso da chave primária da classe <code>EmpresaVO</code> para realizar a
     * consulta.
     *
     * @param obj Objeto no qual será montado os dados consultados.
     */
    public static void montarDadosEmpresa(ComposicaoVO obj, int nivelMontarDados, Connection con) throws Exception {
        if (obj.getEmpresa().getCodigo().intValue() == 0) {
            obj.setEmpresa(new EmpresaVO());
            return;
        }
        Empresa empresa = new Empresa(con);
        obj.setEmpresa(empresa.consultarPorChavePrimaria(obj.getEmpresa().getCodigo(), nivelMontarDados));
        empresa = null;
    }

    /**
     * Operação responsável por adicionar um objeto da
     * <code>ComposicaoModalidadeVO</code> no Hashtable
     * <code>ComposicaoModalidades</code>. Neste Hashtable são mantidos todos os
     * objetos de ComposicaoModalidade de uma determinada Composicao.
     *
     * @param obj Objeto a ser adicionado no Hashtable.
     */
    public void adicionarObjComposicaoModalidades(ComposicaoModalidadeVO obj) throws Exception {
        getComposicaoModalidades().put(obj.getModalidade().getCodigo() + "", obj);
        //adicionarObjSubordinadoOC
    }

    /**
     * Operação responsável por remover um objeto da classe
     * <code>ComposicaoModalidadeVO</code> do Hashtable
     * <code>ComposicaoModalidades</code>. Neste Hashtable são mantidos todos os
     * objetos de ComposicaoModalidade de uma determinada Composicao.
     *
     * @param Modalidade Atributo da classe <code>ComposicaoModalidadeVO</code>
     * utilizado como apelido (key) no Hashtable.
     */
    public void excluirObjComposicaoModalidades(Integer Modalidade) throws Exception {
        getComposicaoModalidades().remove(Modalidade + "");
        //excluirObjSubordinadoOC
    }

    /**
     * Operação responsável por localizar um objeto da classe
     * <code>ComposicaoVO</code> através de sua chave primária.
     *
     * @exception Exception Caso haja problemas de conexão ou localização do
     * objeto procurado.
     */
    public ComposicaoVO consultarPorChavePrimaria(Integer codigoPrm, int nivelMontarDados) throws Exception {
        consultar(getIdEntidade(), false);
        String sql = "SELECT * FROM Composicao WHERE codigo = ? ";
        PreparedStatement sqlConsultar = con.prepareStatement(sql);
        sqlConsultar.setInt(1, codigoPrm.intValue());
        ResultSet tabelaResultado = sqlConsultar.executeQuery();
        if (!tabelaResultado.next()) {
            throw new ConsistirException("Dados Não Encontrados ( Composicao ).");
        }
        return (montarDados(tabelaResultado, nivelMontarDados, this.con));
    }

    public List<ComposicaoVO> consultarTodos(int empresa, int nivelMontarDados) throws Exception {
        String sql = "SELECT * FROM Composicao WHERE empresa = ? ";
        PreparedStatement sqlConsultar = con.prepareStatement(sql);
        sqlConsultar.setInt(1, empresa);
        ResultSet tabelaResultado = sqlConsultar.executeQuery();
        return (montarDadosConsulta(tabelaResultado, nivelMontarDados, this.con));
    }

    public Hashtable getComposicaoModalidades() {
        return (composicaoModalidades);
    }

    public void setComposicaoModalidades(Hashtable composicaoModalidades) {
        this.composicaoModalidades = composicaoModalidades;
    }

    public String consultarJSON(Integer empresa) throws Exception {
        ResultSet rs = getRS(empresa);

        StringBuilder json = new StringBuilder();
        json.append("{\"aaData\":[");
        boolean dados = false;
        while (rs.next()) {
            dados = true;
            json.append("[\"").append(rs.getString("codigo")).append("\",");
            json.append("\"").append(Uteis.normalizarStringJSON(rs.getString("descricao"))).append("\",");
            json.append("\"").append(Uteis.normalizarStringJSON(rs.getString("nome"))).append("\",");
            json.append("\"").append(getMoeda(empresa)).append(" ").append(Formatador.formatarValorMonetarioSemMoeda(rs.getDouble("precocomposicao"))).append("\"],");
        }
        if (dados) {
            json.deleteCharAt(json.toString().length() - 1);
        }
        json.append("]}");
        return json.toString();
    }

    private String getMoeda(Integer empresa) throws SQLException {
        String moeda = "R$";
        StringBuilder sql = new StringBuilder();
        sql.append("select moeda from empresa where codigo = " + empresa);
        PreparedStatement sqlConsultar = con.prepareStatement(sql.toString());
        ResultSet rs = sqlConsultar.executeQuery();
        while (rs.next())
            moeda = rs.getString("moeda");
        return moeda;
    }

    private ResultSet getRS(Integer empresa) throws SQLException {
        StringBuilder sql = new StringBuilder("SELECT c.codigo, em.nome, descricao, precocomposicao FROM composicao c ");
        sql.append(" LEFT JOIN empresa em ON em.codigo = c.empresa \n");
        if (empresa != 0) {
            sql.append("  WHERE empresa = ? ");
        }
        sql.append("  ORDER BY descricao");
        PreparedStatement sqlConsultar = con.prepareStatement(sql.toString());
        if (empresa != 0) {
            sqlConsultar.setInt(1, empresa);
        }
        ResultSet rs = sqlConsultar.executeQuery();
        return rs;
    }

    public List consultarParaImpressao(String filtro, String ordem, String campoOrdenacao, Integer empresa) throws SQLException {

        ResultSet rs = getRS(empresa);
        List lista = new ArrayList();

        while (rs.next()) {

            ComposicaoVO comp = new ComposicaoVO();
            String geral = rs.getString("codigo") + rs.getString("nome") + rs.getString("descricao") + rs.getString("precocomposicao");
            if (geral.toLowerCase().contains(filtro.toLowerCase())) {
                comp.setCodigo(rs.getInt("codigo"));
                comp.setDescricao(rs.getString("descricao"));
                comp.getEmpresa().setNome(rs.getString("nome"));
                comp.setPrecoComposicao(rs.getDouble("precocomposicao"));
                lista.add(comp);
            }
        }
        if (campoOrdenacao.equals("Código")) {
            Ordenacao.ordenarLista(lista, "codigo");
        } else if (campoOrdenacao.equals("Descrição")) {
            Ordenacao.ordenarLista(lista, "descricao");
        } else if (campoOrdenacao.equals("Empresa")) {
            Ordenacao.ordenarLista(lista, "empresa_Apresentar");
        } else if (campoOrdenacao.equals("Valor")) {
            Ordenacao.ordenarLista(lista, "precoComposicao_Apresentar");
        }
        if (ordem.contains("desc")) {
            Collections.reverse(lista);
        }
        return lista;

    }
}

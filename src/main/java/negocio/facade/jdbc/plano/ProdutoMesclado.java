package negocio.facade.jdbc.plano;

import negocio.comuns.plano.ProdutoMescladoVO;
import negocio.comuns.plano.ProdutoVO;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisValidacao;
import negocio.facade.jdbc.arquitetura.SuperEntidade;
import negocio.facade.jdbc.basico.ComissaoProdutoConfiguracao;
import negocio.interfaces.plano.ProdutoMescladoInterfaceFacade;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.Statement;
import java.sql.Timestamp;
import java.util.ArrayList;
import java.util.List;

import static negocio.comuns.plano.enumerador.TipoProduto.CREDITO_PERSONAL;

/**
 * Classe de persist?ncia que encapsula todas as opera??es de manipula??o dos
 * dados da classe
 * <code>ProdutoMescladoVO</code>. Respons?vel por implementar opera??es como incluir,
 * alterar, excluir e consultar pertinentes a classe
 * <code>ProdutoMescladoVO</code>. Encapsula toda a intera??o com o banco de dados.
 *
 * @see ProdutoMescladoVO
 * @see SuperEntidade
 */
public class ProdutoMesclado extends SuperEntidade implements ProdutoMescladoInterfaceFacade {

    private static final int CATEGORIA_PRODUTO_SERVICOS = 5;

    public ProdutoMesclado() throws Exception {
        super();
    }

    public ProdutoMesclado(Connection con) throws Exception {
        super(con);
    }


    @Override
    public ProdutoMescladoVO novo() throws Exception {
        return null;
    }
    @Override
    public void incluir(ProdutoMescladoVO obj) throws Exception {
        String sql = "INSERT INTO public.PRODUTOMESCLADO (produtodestino, produtoorigem, dataexecucao, responsavel) " +
                "VALUES (?, ?, ?, ?)";
        try (PreparedStatement sqlInserir = con.prepareStatement(sql)) {
            sqlInserir.setInt(1, obj.getProdutoDestino().getCodigo());
            sqlInserir.setInt(2, obj.getProdutoOrigem().getCodigo());
            sqlInserir.setTimestamp(3, Uteis.getDataJDBCTimestamp(obj.getDataExecucao()));
            sqlInserir.setString(4, obj.getResponsavel());
            sqlInserir.executeUpdate();
        }
    }

    @Override
    public void alterar(ProdutoMescladoVO obj) throws Exception {

    }

    @Override
    public void excluir(ProdutoMescladoVO obj) throws Exception {

    }

    @Override
    public ProdutoMescladoVO consultarPorChavePrimaria(Integer codigo, int nivelMontarDados) throws Exception {
        return null;
    }

    public List consultarPorProdutoDestino(Integer produtoDestino, int nivelMontarDados) throws Exception {
        String sqlStr = "SELECT * FROM PUBLIC.ProdutoMesclado WHERE produtoDestino ="+produtoDestino+"  ORDER BY codigo";
        try (Statement stm = con.createStatement()) {
            try (ResultSet tabelaResultado = stm.executeQuery(sqlStr)) {
                return (montarDadosConsulta(tabelaResultado, nivelMontarDados, this.con));
            }
        }
    }



    @Override
    public void incluir(List<ProdutoMescladoVO> produtoMescladoVOS) throws Exception {
        for (ProdutoMescladoVO produtoMescladoVO : produtoMescladoVOS) {
            incluir(produtoMescladoVO);
        }

    }

    @Override
    public void excluirTodosProdutosPelaDestino(Integer codigo) throws Exception {
        try {
            con.setAutoCommit(false);
            String sql = "DELETE FROM public.produtomesclado WHERE ((produtodestino = ?))";
            try (PreparedStatement sqlExcluir = con.prepareStatement(sql)) {
                sqlExcluir.setInt(1, codigo.intValue());
                sqlExcluir.execute();
            }
            con.commit();
        } catch (Exception e) {
            con.rollback();
            con.setAutoCommit(true);
            throw e;
        } finally {
            con.setAutoCommit(true);
        }

    }

    public static List<ProdutoMescladoVO> montarDadosConsulta(ResultSet tabelaResultado, int nivelMontarDados, Connection con) throws Exception {
        List<ProdutoMescladoVO> vetResultado = new ArrayList<ProdutoMescladoVO>();
        while (tabelaResultado.next()) {
            ProdutoMescladoVO obj = montarDados(tabelaResultado, nivelMontarDados, con);
            vetResultado.add(obj);
        }
        return vetResultado;
    }
    private static ProdutoVO montarProduto(Integer idProduto, Connection con) throws Exception {
        Produto produto = new Produto(con);
        return  produto.consultarPorChavePrimaria(idProduto, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
    }

    public static ProdutoMescladoVO montarDados(ResultSet dadosSQL, int nivelMontarDados, Connection con) throws Exception {
        ProdutoMescladoVO obj = new ProdutoMescladoVO();
        if(nivelMontarDados <0){
            return null;
        }

        obj.setCodigo(dadosSQL.getInt("codigo"));
        obj.setProdutoOrigem(montarProduto(dadosSQL.getInt("produtoOrigem"),con));
        obj.setProdutoDestino(montarProduto(dadosSQL.getInt("produtoDestino"),con));
        obj.setDataExecucao(dadosSQL.getDate("dataExecucao"));
        obj.setResponsavel(dadosSQL.getString("responsavel"));
        return obj;
    }
}

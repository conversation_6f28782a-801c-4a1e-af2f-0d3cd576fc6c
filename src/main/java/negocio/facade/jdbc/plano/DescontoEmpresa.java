package negocio.facade.jdbc.plano;

import negocio.comuns.basico.EmpresaVO;
import negocio.comuns.plano.DescontoEmpresaVO;
import negocio.comuns.plano.DescontoVO;
import negocio.facade.jdbc.arquitetura.SuperEntidade;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.List;

public class DescontoEmpresa extends SuperEntidade {

    public DescontoEmpresa() throws Exception {
        super();
    }

    public DescontoEmpresa(Connection conexao) throws Exception {
        super(conexao);
    }

    public List<DescontoEmpresaVO> consultarTodas(Integer descontoCodigo) throws SQLException {
        String sql = "SELECT " +
                "CASE WHEN de.codigo IS NULL THEN false ELSE true end AS utilizaDesconto, " +
                "de.codigo, " +
                "de.desconto, " +
                "empresa.codigo empresa_codigo, " +
                "empresa.nome empresa_nome, " +
                "cidade.nome as empresa_cidadeNome, " +
                "estado.sigla as empresa_estadoSigla, " +
                "empresa.setor empresa_setor " +
                "FROM empresa " +
                "LEFT JOIN cidade ON cidade.codigo = empresa.cidade "+
                "LEFT JOIN ESTADO ON estado.codigo = cidade.estado "+
                "LEFT JOIN descontoempresa de on empresa.codigo = de.empresa AND de.desconto = "+descontoCodigo;

        PreparedStatement ps = con.prepareStatement(sql);
        ResultSet resultado = ps.executeQuery();
        List<DescontoEmpresaVO> empresas = new ArrayList<DescontoEmpresaVO>();

        while(resultado.next()){
            empresas.add(montarDados(resultado));
        }

        return empresas;
    }

    public void atualizarEmpresas(DescontoVO desconto) throws SQLException {
        String sqlDelete = "DELETE FROM descontoempresa WHERE desconto = "+desconto.getCodigo();
        PreparedStatement ps = con.prepareStatement(sqlDelete);
        ps.execute();

        incluirEmpresas(desconto);
    }

    public void incluirEmpresas(DescontoVO desconto) throws SQLException {
        String values = "";
        for (DescontoEmpresaVO descontoEmpresa: desconto.getEmpresas()) {
            if(descontoEmpresa.isUtilizaDesconto()){
                values += "("+descontoEmpresa.getEmpresa()+", "+desconto.getCodigo()+"), ";
            }
        }
        if(values.length() > 0){
            values = values.substring(0, values.length() - 2);
            String sqlInsert = "INSERT INTO descontoempresa (empresa, desconto) VALUES "+values;
            PreparedStatement ps = con.prepareStatement(sqlInsert);
            ps.execute();
        }
    }

    private DescontoEmpresaVO montarDados(ResultSet rs) throws SQLException {
        DescontoEmpresaVO descontoEmpresaVO = new DescontoEmpresaVO();
        descontoEmpresaVO.setCodigo(rs.getInt("codigo"));
        descontoEmpresaVO.setEmpresa(rs.getInt("empresa_codigo"));
        descontoEmpresaVO.setDesconto(rs.getInt("desconto"));
        descontoEmpresaVO.setUtilizaDesconto(rs.getBoolean("utilizaDesconto"));

        EmpresaVO empresaVO = new EmpresaVO();
        empresaVO.setCodigo(rs.getInt("empresa_codigo"));
        empresaVO.setNome(rs.getString("empresa_nome"));
        empresaVO.setCidadeNome(rs.getString("empresa_cidadeNome"));
        empresaVO.setEstadoSigla(rs.getString("empresa_estadoSigla"));
        empresaVO.setSetor(rs.getString("empresa_setor"));
        descontoEmpresaVO.setEmpresaVO(empresaVO);

        return descontoEmpresaVO;
    }

    public void montarDados(DescontoVO desconto) throws SQLException {
        String sql = "SELECT * FROM descontoempresa WHERE desconto = "+desconto.getCodigo();
        PreparedStatement ps =  con.prepareStatement(sql);
        ResultSet result = ps.executeQuery();

        List<DescontoEmpresaVO> empresas = new ArrayList<DescontoEmpresaVO>();
        while (result.next()){
            DescontoEmpresaVO de = new DescontoEmpresaVO();
            de.setDesconto(result.getInt("desconto"));
            de.setEmpresa(result.getInt("empresa"));
            de.setCodigo(result.getInt("codigo"));
            empresas.add(de);
        }

        desconto.setEmpresas(empresas);
    }
}

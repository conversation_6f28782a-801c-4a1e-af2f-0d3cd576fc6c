package negocio.facade.jdbc.plano;

import br.com.pactosolucoes.enumeradores.TipoHorarioCreditoTreinoEnum;
import negocio.comuns.contrato.ContratoDuracaoCreditoTreinoVO;
import negocio.comuns.plano.PlanoDuracaoCreditoTreinoVO;
import negocio.comuns.plano.PlanoDuracaoVO;
import negocio.comuns.utilitarias.ConsistirException;
import negocio.comuns.utilitarias.UteisValidacao;
import negocio.comuns.utilitarias.UtilReflection;
import negocio.facade.jdbc.arquitetura.SuperEntidade;
import negocio.facade.jdbc.arquitetura.SuperFacadeJDBC;
import negocio.interfaces.plano.PlanoDuracaoCreditoTreinoInterfaceFacade;

import java.sql.*;
import java.util.ArrayList;
import java.util.List;

/**
 * Created by ulisses on 10/11/2015.
 */
public class PlanoDuracaoCreditoTreino extends SuperEntidade implements PlanoDuracaoCreditoTreinoInterfaceFacade{

    public PlanoDuracaoCreditoTreino() throws Exception {
        super();
    }

    public PlanoDuracaoCreditoTreino(Connection conexao) throws Exception {
        super(conexao);
    }
    public void incluir(List<PlanoDuracaoCreditoTreinoVO> listaPlanoDuracaoCreditoTreinoVO)throws Exception{
        if (listaPlanoDuracaoCreditoTreinoVO != null){
            for (PlanoDuracaoCreditoTreinoVO obj: listaPlanoDuracaoCreditoTreinoVO){
                this.incluir(obj);
            }
        }
    }

    public void incluir(PlanoDuracaoCreditoTreinoVO planoDuracaoCreditoTreinoVO)throws Exception{
        planoDuracaoCreditoTreinoVO.validarDados();
        if (!UtilReflection.objetoMaiorQueZero(planoDuracaoCreditoTreinoVO, "getPlanoDuracaoVO().getCodigo()")) {
            throw new ConsistirException("O campo PlanoDuracao (PlanoDuracaoCreditoTreino) deve ser informado.");
        }

        String sql = "insert into planoDuracaoCreditoTreino(planoDuracao,tipoHorarioCreditoTreino,numeroVezesSemana,quantidadeCreditoCompra,valorUnitario,quantidadeCreditoMensal) values(?,?,?,?,?,?)";
        PreparedStatement pst = con.prepareStatement(sql);
        pst.setInt(1, planoDuracaoCreditoTreinoVO.getPlanoDuracaoVO().getCodigo());
        pst.setInt(2, planoDuracaoCreditoTreinoVO.getTipoHorarioCreditoTreino());
        pst.setInt(3, planoDuracaoCreditoTreinoVO.getNumeroVezesSemana());
        pst.setInt(4, planoDuracaoCreditoTreinoVO.getQuantidadeCreditoCompra());
        pst.setDouble(5, planoDuracaoCreditoTreinoVO.getValorUnitario());
        if (UteisValidacao.emptyNumber(planoDuracaoCreditoTreinoVO.getQuantidadeCreditoMensal())) {
            pst.setNull(6, Types.INTEGER);
        } else {
            pst.setInt(6, planoDuracaoCreditoTreinoVO.getQuantidadeCreditoMensal());
        }

        pst.execute();

    }

  /*  public void consultarContratoDuracaoCreditoTreinoBaseCalculo(ContratoDuracaoCreditoTreinoVO contratoDuracaoCreditoTreinoVO, Integer qtdeCreditoUtilizado) throws  Exception{
        // Pegar o valor unitário mais próximo em relação à quantidade de créditos utilizado.

        StringBuilder sql = new StringBuilder();
        sql.append(" select pdc.*  \n");
        sql.append("from planoDuracaoCreditoTreino pdc \n");
        sql.append("inner join planoDuracao pd on pd.codigo = pdc.planoDuracao \n");
        sql.append("where tipoHorarioCreditoTreino = ").append(contratoDuracaoCreditoTreinoVO.getTipoHorarioCreditoTreinoEnum().getCodigo()).append(" \n");
        sql.append("and pd.numeroMeses = ").append(contratoDuracaoCreditoTreinoVO.getContratoDuracaoVO().getNumeroMeses()).append(" \n");
        sql.append("order by quantidadeCreditoCompra ");
        PreparedStatement pst  = con.prepareStatement(sql.toString());
        ResultSet rs = pst.executeQuery();
        double valorUnitario = 0;
        ContratoDuracaoCreditoTreinoVO contratoDuracaoBaseCalculo = new ContratoDuracaoCreditoTreinoVO();
        while (rs.next()){
            if (rs.getInt("quantidadeCreditoCompra") >= qtdeCreditoUtilizado){
                valorUnitario = rs.getDouble("valorUnitario");
                contratoDuracaoCreditoTreinoVO.setValorUnitario(valorUnitario);
                contratoDuracaoBaseCalculo.setQuantidadeCreditoCompra(rs.getInt("quantidadeCreditoCompra"));
                contratoDuracaoBaseCalculo.setValorUnitario(valorUnitario);
                contratoDuracaoBaseCalculo.setNumeroVezesSemana(rs.getInt("numeroVezesSemana"));
                contratoDuracaoBaseCalculo.setTipoHorarioCreditoTreinoEnum(TipoHorarioCreditoTreinoEnum.getTipo(rs.getInt("tipoHorarioCreditoTreino")));
                contratoDuracaoCreditoTreinoVO.setContratoDuracaoCreditoTreinoBaseCalculo(contratoDuracaoBaseCalculo);
                break;
            }
        }

    }*/
    public void consultarContratoDuracaoCreditoTreinoBaseCalculo(ContratoDuracaoCreditoTreinoVO contratoDuracaoCreditoTreinoVO, Integer codigoPlano) throws  Exception{
        //Ao cancelar o contrato antes do término, o cliente paga o maior valor unitário do crédito.

        StringBuilder sql = new StringBuilder();
        sql.append(" select * \n");
        sql.append("from planoDuracaoCreditoTreino pdc \n");
        sql.append("inner join planoDuracao pd on pd.codigo = pdc.planoDuracao \n");
        sql.append("where  pdc.tipoHorarioCreditoTreino =").append(contratoDuracaoCreditoTreinoVO.getTipoHorarioCreditoTreinoEnum().getCodigo()).append(" \n ");
        sql.append(" and pd.plano = ").append(codigoPlano).append(" \n ");
        sql.append("order by valorUnitario desc limit 1 ");

        PreparedStatement pst  = con.prepareStatement(sql.toString());
        ResultSet rs = pst.executeQuery();
        ContratoDuracaoCreditoTreinoVO contratoDuracaoBaseCalculo = new ContratoDuracaoCreditoTreinoVO();
        if (rs.next()){
            double valorUnitario = 0;
            valorUnitario = rs.getDouble("valorUnitario");
            contratoDuracaoCreditoTreinoVO.setValorUnitario(valorUnitario);
            contratoDuracaoBaseCalculo.setQuantidadeCreditoCompra(rs.getInt("quantidadeCreditoCompra"));
            contratoDuracaoBaseCalculo.setValorUnitario(valorUnitario);
            contratoDuracaoBaseCalculo.setNumeroVezesSemana(rs.getInt("numeroVezesSemana"));
            contratoDuracaoBaseCalculo.setTipoHorarioCreditoTreinoEnum(TipoHorarioCreditoTreinoEnum.getTipo(rs.getInt("tipoHorarioCreditoTreino")));
            contratoDuracaoCreditoTreinoVO.setContratoDuracaoCreditoTreinoBaseCalculo(contratoDuracaoBaseCalculo);
        }

    }


    public PlanoDuracaoCreditoTreinoVO consultar(Integer codigoPlanoDuracao, Integer codigoTipoHorario, Integer numeroVezesSemana, int nivelMontarDados)throws Exception{
        StringBuilder sql = new StringBuilder();
        sql.append("select * \n");
        sql.append("from planoDuracaoCreditoTreino \n");
        sql.append("where  planoDuracao = ").append(codigoPlanoDuracao).append(" \n");
        sql.append("and tipoHorarioCreditoTreino = ").append(codigoTipoHorario).append(" \n");
        sql.append("and numeroVezesSemana = ").append(numeroVezesSemana);
        PreparedStatement pst = con.prepareStatement(sql.toString());
        ResultSet rs = pst.executeQuery();
        if (rs.next()){
            return montarDados(rs,nivelMontarDados);
        }
        return null;

    }

    public void excluir(PlanoDuracaoVO planoDuracaoVO)throws Exception{
        String sql = "delete from planoDuracaoCreditoTreino where planoDuracao = ?";
        PreparedStatement pst = con.prepareStatement(sql);
        pst.setInt(1, planoDuracaoVO.getCodigo());
        pst.execute();
    }

    public List<PlanoDuracaoCreditoTreinoVO> consultar(Integer codigoPlanoDuracao, Integer codigoTipoHorario, int nivelMontarDados)throws Exception{
        StringBuilder sql = new StringBuilder();
        sql.append("select * \n");
        sql.append("from planoDuracaoCreditoTreino \n");
        sql.append("where  planoDuracao = ").append(codigoPlanoDuracao).append(" \n");
        if ((codigoTipoHorario != null) && (codigoTipoHorario > 0)){
            sql.append("and tipoHorarioCreditoTreino = ").append(codigoTipoHorario);
        }
        sql.append(" order by quantidadeCreditoCompra, numeroVezesSemana");
        PreparedStatement pst = con.prepareStatement(sql.toString());
        ResultSet rs = pst.executeQuery();
        return montarDadosConsulta(rs, nivelMontarDados);
    }

    private PlanoDuracaoCreditoTreinoVO montarDadosBasico(ResultSet rs)throws Exception{
        PlanoDuracaoCreditoTreinoVO planoDuracaoCreditoTreinoVO = new PlanoDuracaoCreditoTreinoVO();
        planoDuracaoCreditoTreinoVO.setCodigo(rs.getInt("codigo"));
        planoDuracaoCreditoTreinoVO.setPlanoDuracaoVO(new PlanoDuracaoVO());
        planoDuracaoCreditoTreinoVO.getPlanoDuracaoVO().setCodigo(rs.getInt("planoDuracao"));
        planoDuracaoCreditoTreinoVO.setTipoHorarioCreditoTreino(rs.getInt("tipoHorarioCreditoTreino"));
        planoDuracaoCreditoTreinoVO.setNumeroVezesSemana(rs.getInt("numeroVezesSemana"));
        planoDuracaoCreditoTreinoVO.setQuantidadeCreditoCompra(rs.getInt("quantidadeCreditoCompra"));
        planoDuracaoCreditoTreinoVO.setValorUnitario(rs.getDouble("valorUnitario"));
        planoDuracaoCreditoTreinoVO.setQuantidadeCreditoMensal(rs.getInt("quantidadeCreditoMensal"));
        return planoDuracaoCreditoTreinoVO;
    }

    private PlanoDuracaoCreditoTreinoVO montarDados(ResultSet rs,  int nivelMontarDados)throws Exception{
        PlanoDuracaoCreditoTreinoVO planoDuracaoCreditoTreinoVO = montarDadosBasico(rs);
        return planoDuracaoCreditoTreinoVO;
    }

    private List<PlanoDuracaoCreditoTreinoVO> montarDadosConsulta(ResultSet rs, int nivelMontarDados) throws Exception {
        List<PlanoDuracaoCreditoTreinoVO> lista = new ArrayList<PlanoDuracaoCreditoTreinoVO>();
        while (rs.next()) {
            PlanoDuracaoCreditoTreinoVO obj = montarDadosBasico(rs);
            lista.add(obj);
        }
        return lista;
    }

    public PlanoDuracaoCreditoTreinoVO consultarPorPlanoDuracao(Integer codigoPlanoDuracao, int nivelMontarDados)throws Exception{
        String sql = "select * from planoDuracaoCreditoTreino where planoduracao = ?";
        PreparedStatement pst = con.prepareStatement(sql);
        pst.setInt(1, codigoPlanoDuracao);
        ResultSet rs = pst.executeQuery();
        if(rs.next()){
            return montarDados(rs, nivelMontarDados);
        }
        return null;
    }

    public PlanoDuracaoCreditoTreinoVO consultarPorCodigo(Integer codigo, int nivelMontarDados)throws Exception{
        String sql = "select * from planoDuracaoCreditoTreino where codigo = ?";
        PreparedStatement pst = con.prepareStatement(sql);
        pst.setInt(1, codigo);
        ResultSet rs = pst.executeQuery();
        rs.next();
        return montarDados(rs, nivelMontarDados);
    }

    public void incluirSessao(PlanoDuracaoVO planoDuracaoVO) throws Exception {
        if (isCreditoSessao(planoDuracaoVO)) {
            PlanoDuracaoCreditoTreinoVO novo = new PlanoDuracaoCreditoTreinoVO();
            novo.setPlanoDuracaoVO(planoDuracaoVO);
            novo.setTipoHorarioCreditoTreino(TipoHorarioCreditoTreinoEnum.HORARIO_TURMA.getCodigo());
            novo.setNumeroVezesSemana(1);
            novo.setQuantidadeCreditoCompra(1);
            novo.setQuantidadeCreditoMensal(0);
            novo.setValorUnitario(1);
            this.incluir(novo);
        }
    }

    public void alterarSessao(PlanoDuracaoVO planoDuracaoVO) throws Exception {
        if (isCreditoSessao(planoDuracaoVO)) {
            this.excluir(planoDuracaoVO);
            this.incluirSessao(planoDuracaoVO);
        }
    }

    private boolean isCreditoSessao(PlanoDuracaoVO planoDuracaoVO) {
        try {
            if (UteisValidacao.emptyNumber(planoDuracaoVO.getPlano())) {
                return false;
            } else {
                String sql = "select creditosessao from plano where codigo = " + planoDuracaoVO.getPlano();
                Statement stm = con.createStatement();
                ResultSet tabelaResultado = stm.executeQuery(sql);
                tabelaResultado.next();
                return tabelaResultado.getBoolean("creditosessao");
            }
        } catch (Exception ex) {
            return false;
        }
    }

    public PlanoDuracaoCreditoTreinoVO consultarPorPlanoDuracao(Integer codigoPlano,Integer numeroMeses, Integer codigoTipoHorario, Integer numeroVezesSemana, Integer qteCreditos, int nivelMontarDados)throws Exception{
        StringBuilder sql = new StringBuilder();
        sql.append("select * \n");
        sql.append("from planoDuracaoCreditoTreino pdct inner join planoduracao pd on pd.codigo = pdct.planoduracao \n");
        sql.append("where  pd.plano = ").append(codigoPlano).append(" \n");
        sql.append("and pd.numeromeses = ").append(numeroMeses).append(" \n");
        sql.append("and tipoHorarioCreditoTreino = ").append(codigoTipoHorario).append(" \n");
        sql.append("and numeroVezesSemana = ").append(numeroVezesSemana);
        sql.append("and quantidadecreditocompra = ").append(qteCreditos);
        try(PreparedStatement pst = con.prepareStatement(sql.toString())) {
            try (ResultSet rs = pst.executeQuery()) {
                if (rs.next()) {
                    return montarDados(rs, nivelMontarDados);
                }
            }
        }
        return null;

    }
}

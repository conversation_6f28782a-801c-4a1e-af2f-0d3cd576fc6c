package negocio.facade.jdbc.plano;

import java.sql.Connection;
import java.util.Iterator;
import negocio.comuns.plano.CondicaoPagamentoParcelaVO;
import negocio.facade.jdbc.arquitetura.*;
import java.sql.ResultSet;
import java.sql.PreparedStatement;
import java.sql.Statement;
import java.util.List;
import java.util.ArrayList;
import negocio.comuns.utilitarias.*;

/**
 * Classe de persistência que encapsula todas as operações de manipulação dos dados da classe <code>CondicaoPagamentoParcelaVO</code>.
 * Responsável por implementar operações como incluir, alterar, excluir e consultar pertinentes a classe <code>CondicaoPagamentoParcelaVO</code>.
 * Encapsula toda a interação com o banco de dados.
 * @see CondicaoPagamentoParcelaVO
 * @see SuperEntidade
 * @see CondicaoPagamento
 */
public class CondicaoPagamentoParcela extends SuperEntidade {    

    public CondicaoPagamentoParcela() throws Exception {
        super();
        setIdEntidade("CondicaoPagamento");
    }
    public CondicaoPagamentoParcela(Connection con) throws Exception {
        super(con);
        setIdEntidade("CondicaoPagamento");
    }

    /**
     * Operação responsável por retornar um novo objeto da classe <code>CondicaoPagamentoParcelaVO</code>.
     */
    public CondicaoPagamentoParcelaVO novo() throws Exception {
        incluir(getIdEntidade());
        CondicaoPagamentoParcelaVO obj = new CondicaoPagamentoParcelaVO();
        return obj;
    }

    /**
     * Operação responsável por incluir no banco de dados um objeto da classe <code>CondicaoPagamentoParcelaVO</code>.
     * Primeiramente valida os dados (<code>validarDados</code>) do objeto. Verifica a conexão com o banco de dados e a permissão do usuário
     * para realizar esta operacão na entidade.
     * Isto, através da operação <code>incluir</code> da superclasse.
     * @param obj  Objeto da classe <code>CondicaoPagamentoParcelaVO</code> que será gravado no banco de dados.
     * @exception Exception Caso haja problemas de conexão, restrição de acesso ou validação de dados.
     */
    public void incluir(CondicaoPagamentoParcelaVO obj) throws Exception {
        CondicaoPagamentoParcelaVO.validarDados(obj);
        //  CondicaoPagamentoParcela.incluir(getIdEntidade());
        obj.realizarUpperCaseDados();
        String sql = "INSERT INTO CondicaoPagamentoParcela( condicaoPagamento, nrDiasParcela, nrParcela ) VALUES ( ?, ?, ? )";
        PreparedStatement sqlInserir = con.prepareStatement(sql);
        if (obj.getCondicaoPagamento().intValue() != 0) {
            sqlInserir.setInt(1, obj.getCondicaoPagamento().intValue());
        } else {
            sqlInserir.setNull(1, 0);
        }
        //       sqlInserir.setDouble( 2, obj.getPercentualParcela().doubleValue() );
        sqlInserir.setInt(2, obj.getNrDiasParcela().intValue());
        sqlInserir.setInt(3, obj.getNrParcela().intValue());
        sqlInserir.execute();
        obj.setCodigo(obterValorChavePrimariaCodigo());
        obj.setNovoObj(false);
    }

    /**
     * Operação responsável por alterar no BD os dados de um objeto da classe <code>CondicaoPagamentoParcelaVO</code>.
     * Sempre utiliza a chave primária da classe como atributo para localização do registro a ser alterado.
     * Primeiramente valida os dados (<code>validarDados</code>) do objeto. Verifica a conexão com o banco de dados e a permissão do usuário
     * para realizar esta operacão na entidade.
     * Isto, através da operação <code>alterar</code> da superclasse.
     * @param obj    Objeto da classe <code>CondicaoPagamentoParcelaVO</code> que será alterada no banco de dados.
     * @exception Execption Caso haja problemas de conexão, restrição de acesso ou validação de dados.
     */
    public void alterar(CondicaoPagamentoParcelaVO obj) throws Exception {
        CondicaoPagamentoParcelaVO.validarDados(obj);
        alterar(getIdEntidade());
        obj.realizarUpperCaseDados();
        String sql = "UPDATE CondicaoPagamentoParcela set condicaoPagamento=?,  nrParcelas=?, nrDiasParcelas=? WHERE ((codigo = ?))";
        PreparedStatement sqlAlterar = con.prepareStatement(sql);
        if (obj.getCondicaoPagamento().intValue() != 0) {
            sqlAlterar.setInt(1, obj.getCondicaoPagamento().intValue());
        } else {
            sqlAlterar.setNull(1, 0);
        }
        //      sqlAlterar.setDouble( 2, obj.getPercentualParcela().doubleValue() );
        sqlAlterar.setInt(2, obj.getNrParcela().intValue());
        sqlAlterar.setInt(3, obj.getNrDiasParcela().intValue());
        sqlAlterar.setInt(4, obj.getCodigo().intValue());
        sqlAlterar.execute();
    }

    /**
     * Operação responsável por excluir no BD um objeto da classe <code>CondicaoPagamentoParcelaVO</code>.
     * Sempre localiza o registro a ser excluído através da chave primária da entidade.
     * Primeiramente verifica a conexão com o banco de dados e a permissão do usuário
     * para realizar esta operacão na entidade.
     * Isto, através da operação <code>excluir</code> da superclasse.
     * @param obj    Objeto da classe <code>CondicaoPagamentoParcelaVO</code> que será removido no banco de dados.
     * @exception Execption Caso haja problemas de conexão ou restrição de acesso.
     */
    public void excluir(CondicaoPagamentoParcelaVO obj) throws Exception {
        excluir(getIdEntidade());
        String sql = "DELETE FROM CondicaoPagamentoParcela WHERE ((codigo = ?))";
        PreparedStatement sqlExcluir = con.prepareStatement(sql);
        sqlExcluir.setInt(1, obj.getCodigo().intValue());
        sqlExcluir.execute();
    }

    /**
     * Responsável por realizar uma consulta de <code>CondicaoPagamentoParcela</code> através do valor do atributo 
     * <code>Double percentualParcela</code>. Retorna os objetos com valores iguais ou superiores ao parâmetro fornecido.
     * Faz uso da operação <code>montarDadosConsulta</code> que realiza o trabalho de prerarar o List resultante.
     * @param   controlarAcesso Indica se a aplicação deverá verificar se o usuário possui permissão para esta consulta ou não.
     * @return  List Contendo vários objetos da classe <code>CondicaoPagamentoParcelaVO</code> resultantes da consulta.
     * @exception Exception Caso haja problemas de conexão ou restrição de acesso.
     */
    public List consultarPorPercentualParcela(Double valorConsulta, boolean controlarAcesso, int nivelMontarDados) throws Exception {
        consultar(getIdEntidade(), controlarAcesso);
        String sqlStr = "SELECT * FROM CondicaoPagamentoParcela WHERE percentualParcela >= " + valorConsulta.doubleValue() + " ORDER BY percentualParcela";
        Statement stm = con.createStatement();
        ResultSet tabelaResultado = stm.executeQuery(sqlStr);
        return (montarDadosConsulta(tabelaResultado, nivelMontarDados));
    }

    /**
     * Responsável por realizar uma consulta de <code>CondicaoPagamentoParcela</code> através do valor do atributo 
     * <code>descricao</code> da classe <code>CondicaoPagamento</code>
     * Faz uso da operação <code>montarDadosConsulta</code> que realiza o trabalho de prerarar o List resultante.
     * @return  List Contendo vários objetos da classe <code>CondicaoPagamentoParcelaVO</code> resultantes da consulta.
     * @exception Execption Caso haja problemas de conexão ou restrição de acesso.
     */
    public List consultarPorDescricaoCondicaoPagamento(String valorConsulta, int nivelMontarDados) throws Exception {
        consultar(getIdEntidade(), true);
        String sqlStr = "SELECT CondicaoPagamentoParcela.* FROM CondicaoPagamentoParcela, CondicaoPagamento WHERE CondicaoPagamentoParcela.condicaoPagamento = CondicaoPagamento.codigo and upper( CondicaoPagamento.descricao ) like('" + valorConsulta.toUpperCase() + "%') ORDER BY CondicaoPagamento.descricao";
        Statement stm = con.createStatement();
        ResultSet tabelaResultado = stm.executeQuery(sqlStr);
        return montarDadosConsulta(tabelaResultado, nivelMontarDados);
    }

    /**
     * Responsável por realizar uma consulta de <code>CondicaoPagamentoParcela</code> através do valor do atributo 
     * <code>Integer codigo</code>. Retorna os objetos com valores iguais ou superiores ao parâmetro fornecido.
     * Faz uso da operação <code>montarDadosConsulta</code> que realiza o trabalho de prerarar o List resultante.
     * @param   controlarAcesso Indica se a aplicação deverá verificar se o usuário possui permissão para esta consulta ou não.
     * @return  List Contendo vários objetos da classe <code>CondicaoPagamentoParcelaVO</code> resultantes da consulta.
     * @exception Exception Caso haja problemas de conexão ou restrição de acesso.
     */
    public List consultarPorCodigo(Integer valorConsulta, boolean controlarAcesso, int nivelMontarDados) throws Exception {
        consultar(getIdEntidade(), controlarAcesso);
        String sqlStr = "SELECT * FROM CondicaoPagamentoParcela WHERE codigo >= " + valorConsulta.intValue() + " ORDER BY codigo";
        Statement stm = con.createStatement();
        ResultSet tabelaResultado = stm.executeQuery(sqlStr);
        return (montarDadosConsulta(tabelaResultado, nivelMontarDados));
    }

    /**
     * Responsável por montar os dados de vários objetos, resultantes de uma consulta ao banco de dados (<code>ResultSet</code>).
     * Faz uso da operação <code>montarDados</code> que realiza o trabalho para um objeto por vez.
     * @return  List Contendo vários objetos da classe <code>CondicaoPagamentoParcelaVO</code> resultantes da consulta.
     */
    public static List montarDadosConsulta(ResultSet tabelaResultado, int nivelMontarDados) throws Exception {
        List vetResultado = new ArrayList();
        while (tabelaResultado.next()) {
            CondicaoPagamentoParcelaVO obj = new CondicaoPagamentoParcelaVO();
            obj = montarDados(tabelaResultado, nivelMontarDados);
            vetResultado.add(obj);
        }
        return vetResultado;
    }

    /**
     * Responsável por montar os dados resultantes de uma consulta ao banco de dados (<code>ResultSet</code>)
     * em um objeto da classe <code>CondicaoPagamentoParcelaVO</code>.
     * @return  O objeto da classe <code>CondicaoPagamentoParcelaVO</code> com os dados devidamente montados.
     */
    public static CondicaoPagamentoParcelaVO montarDados(ResultSet dadosSQL, int nivelMontarDados) throws Exception {
        CondicaoPagamentoParcelaVO obj = new CondicaoPagamentoParcelaVO();
        obj.setCodigo(new Integer(dadosSQL.getInt("codigo")));
        obj.setCondicaoPagamento(new Integer(dadosSQL.getInt("condicaoPagamento")));
//        obj.setPercentualParcela( new Double( dadosSQL.getDouble("percentualParcela")));
        obj.setNrParcela(new Integer(dadosSQL.getInt("nrParcela")));
        obj.setNrDiasParcela(new Integer(dadosSQL.getInt("nrDiasParcela")));
        obj.setNovoObj(false);
        if (nivelMontarDados == Uteis.NIVELMONTARDADOS_DADOSBASICOS) {
            return obj;
        }

        return obj;
    }

    /**
     * Operação responsável por excluir todos os objetos da <code>CondicaoPagamentoParcelaVO</code> no BD.
     * Faz uso da operação <code>excluir</code> disponível na classe <code>CondicaoPagamentoParcela</code>.
     * @param <code>condicaoPagamento</code> campo chave para exclusão dos objetos no BD.
     * @exception Exception  Erro de conexão com o BD ou restrição de acesso a esta operação.
     */
    public void excluirCondicaoPagamentoParcelas(Integer condicaoPagamento) throws Exception {
        excluir(getIdEntidade());
        String sql = "DELETE FROM CondicaoPagamentoParcela WHERE (condicaoPagamento = ?)";
        PreparedStatement sqlExcluir = con.prepareStatement(sql);
        sqlExcluir.setInt(1, condicaoPagamento.intValue());
        sqlExcluir.execute();
    }

    /**
     * Operação responsável por alterar todos os objetos da <code>CondicaoPagamentoParcelaVO</code> contidos em um Hashtable no BD.
     * Faz uso da operação <code>excluirCondicaoPagamentoParcelas</code> e <code>incluirCondicaoPagamentoParcelas</code> disponíveis na classe <code>CondicaoPagamentoParcela</code>.
     * @param objetos  List com os objetos a serem alterados ou incluídos no BD.
     * @exception Exception  Erro de conexão com o BD ou restrição de acesso a esta operação.
     */
    public void alterarCondicaoPagamentoParcelas(Integer condicaoPagamento, List objetos) throws Exception {
        excluirCondicaoPagamentoParcelas(condicaoPagamento);
        incluirCondicaoPagamentoParcelas(condicaoPagamento, objetos);
    }

    /**
     * Operação responsável por incluir objetos da <code>CondicaoPagamentoParcelaVO</code> no BD.
     * Garantindo o relacionamento com a entidade principal <code>plano.CondicaoPagamento</code> através do atributo de vínculo.
     * @param objetos List contendo os objetos a serem gravados no BD da classe.
     * @exception Exception  Erro de conexão com o BD ou restrição de acesso a esta operação.
     */
    public void incluirCondicaoPagamentoParcelas(Integer condicaoPagamentoPrm, List objetos) throws Exception {
        Iterator e = objetos.iterator();
        while (e.hasNext()) {
            CondicaoPagamentoParcelaVO obj = (CondicaoPagamentoParcelaVO) e.next();
            obj.setCondicaoPagamento(condicaoPagamentoPrm);
            incluir(obj);
        }
    }

    /**
     * Operação responsável por consultar todos os <code>CondicaoPagamentoParcelaVO</code> relacionados a um objeto da classe <code>plano.CondicaoPagamento</code>.
     * @param condicaoPagamento  Atributo de <code>plano.CondicaoPagamento</code> a ser utilizado para localizar os objetos da classe <code>CondicaoPagamentoParcelaVO</code>.
     * @return List  Contendo todos os objetos da classe <code>CondicaoPagamentoParcelaVO</code> resultantes da consulta.
     * @exception Exception  Erro de conexão com o BD ou restrição de acesso a esta operação.
     */
    public List consultarCondicaoPagamentoParcelas(Integer condicaoPagamento, int nivelMontarDados) throws Exception {
        consultar(getIdEntidade());
        List objetos = new ArrayList();
        String sql = "SELECT * FROM CondicaoPagamentoParcela WHERE condicaoPagamento = ?";
        PreparedStatement sqlConsulta = con.prepareStatement(sql);
        sqlConsulta.setInt(1, condicaoPagamento.intValue());
        ResultSet resultado = sqlConsulta.executeQuery();
        while (resultado.next()) {
            CondicaoPagamentoParcelaVO novoObj = CondicaoPagamentoParcela.montarDados(resultado, nivelMontarDados);
            objetos.add(novoObj);
        }
        return objetos;
    }

    /**
     * Operação responsável por localizar um objeto da classe <code>CondicaoPagamentoParcelaVO</code>
     * através de sua chave primária. 
     * @exception Exception Caso haja problemas de conexão ou localização do objeto procurado.
     */
    public CondicaoPagamentoParcelaVO consultarPorChavePrimaria(Integer codigoPrm, int nivelMontarDados) throws Exception {
        consultar(getIdEntidade(), false);
        String sql = "SELECT * FROM CondicaoPagamentoParcela WHERE codigo = ?";
        PreparedStatement sqlConsultar = con.prepareStatement(sql);
        sqlConsultar.setInt(1, codigoPrm.intValue());
        ResultSet tabelaResultado = sqlConsultar.executeQuery();
        if (!tabelaResultado.next()) {
            throw new ConsistirException("Dados Não Encontrados ( CondicaoPagamentoParcela ).");
        }
        return (montarDados(tabelaResultado, nivelMontarDados));
    }    
}

/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package negocio.facade.jdbc.plano;

import br.com.pactosolucoes.estudio.dao.Pacote;
import br.com.pactosolucoes.estudio.interfaces.PacoteInterfaceFacade;
import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.util.ArrayList;
import java.util.List;
import negocio.comuns.plano.PlanoExcecaoVO;
import negocio.comuns.plano.PlanoVO;
import negocio.comuns.utilitarias.ConsistirException;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisValidacao;
import negocio.facade.jdbc.arquitetura.SuperEntidade;
import negocio.facade.jdbc.arquitetura.SuperFacadeJDBC;
import negocio.facade.jdbc.basico.Categoria;
import negocio.interfaces.basico.CategoriaInterfaceFacade;
import negocio.interfaces.plano.ComposicaoInterfaceFacade;
import negocio.interfaces.plano.HorarioInterfaceFacade;
import negocio.interfaces.plano.ModalidadeInterfaceFacade;
import negocio.interfaces.plano.PlanoExcecaoInterfaceFacade;
import negocio.interfaces.plano.PlanoInterfaceFacade;

/**
 *
 * <AUTHOR>
 */
/**
 * Classe de persistência que encapsula todas as operações de manipulação dos
 * dados da classe
 * <code>PlanoExcecaoVO</code>. Responsável por implementar operações como
 * incluir, alterar, excluir e consultar pertinentes a classe
 * <code>PlanoExcecaoVO</code>. Encapsula toda a interação com o banco de dados.
 *
 * @see PlanoExcecaoVO
 * @see SuperEntidade
 */
public class PlanoExcecao extends SuperEntidade implements PlanoExcecaoInterfaceFacade {

    public PlanoExcecao() throws Exception {
        super();
        setIdEntidade("Plano");
    }

    public PlanoExcecao(Connection con) throws Exception {
        super(con);
        setIdEntidade("Plano");
    }

    /**
     * Operação responsável por retornar um novo objeto da classe
     * <code>PlanoExcecaoVO</code>.
     */
    @Override
    public PlanoExcecaoVO novo() throws Exception {
        incluir(getIdEntidade());
        return new PlanoExcecaoVO();
    }

    /**
     * Operação responsável por incluir no banco de dados um objeto da classe
     * <code>PlanoExcecaoVO</code>. Primeiramente valida os dados (
     * <code>validarDados</code>) do objeto. Verifica a conexão com o banco de
     * dados e a permissão do usuário para realizar esta operacão na entidade.
     * Isto, através da operação
     * <code>incluir</code> da superclasse.
     *
     * @param obj Objeto da classe <code>PlanoExcecaoVO</code> que será gravado
     * no banco de dados.
     * @exception Exception Caso haja problemas de conexão, restrição de acesso
     * ou validação de dados.
     */
    @Override
    public void incluir(PlanoExcecaoVO obj) throws Exception {
        incluir(getIdEntidade());
        String sql = "INSERT INTO PlanoExcecao( plano,pacote,modalidade,vezessemana,duracao,horario,valor ) VALUES ( ?,?,?,?,?,?,? )";
        int i = 1;
        try (PreparedStatement ps = con.prepareStatement(sql)) {
            resolveFKNull(ps, i++, obj.getPlano().getCodigo());
            resolveFKNull(ps, i++, obj.getPacote().getCodigo());
            resolveFKNull(ps, i++, obj.getModalidade().getCodigo());
            ps.setInt(i++, obj.getVezesSemana());
            ps.setInt(i++, obj.getDuracao());
            resolveFKNull(ps, i++, obj.getHorario().getCodigo());
            ps.setDouble(i++, obj.getValor());
            ps.execute();
        }
        obj.setCodigo(obterValorChavePrimariaCodigo());
        obj.setNovoObj(false);
    }

    /**
     * Operação responsável por alterar no BD os dados de um objeto da classe
     * <code>PlanoExcecaoVO</code>. Sempre utiliza a chave primária da classe
     * como atributo para localização do registro a ser alterado. Primeiramente
     * valida os dados (
     * <code>validarDados</code>) do objeto. Verifica a conexão com o banco de
     * dados e a permissão do usuário para realizar esta operacão na entidade.
     * Isto, através da operação
     * <code>alterar</code> da superclasse.
     *
     * @param obj Objeto da classe <code>PlanoExcecaoVO</code> que será alterada
     * no banco de dados.
     * @exception Exception Caso haja problemas de conexão, restrição de acesso
     * ou validação de dados.
     */
    @Override
    public void alterar(PlanoExcecaoVO obj) throws Exception {
        try {
            PlanoExcecaoVO.validarDados(obj);
            alterar(getIdEntidade());
            String sql = "UPDATE PlanoExcecao set plano=?,pacote=?,modalidade=?,vezessemana=?,duracao=?,horario=?,valor=?  WHERE ((codigo = ?))";
            try (PreparedStatement ps = con.prepareStatement(sql)) {
                int i = 1;
                resolveFKNull(ps, i++, obj.getPlano().getCodigo());
                resolveFKNull(ps, i++, obj.getPacote().getCodigo());
                resolveFKNull(ps, i++, obj.getModalidade().getCodigo());
                ps.setInt(i++, obj.getVezesSemana());
                ps.setInt(i++, obj.getDuracao());
                resolveFKNull(ps, i++, obj.getHorario().getCodigo());
                ps.setDouble(i++, obj.getValor());
                ps.setInt(i++, obj.getCodigo());
                ps.execute();
            }
        } catch (Exception e) {
            throw e;
        }
    }

    /**
     * Operação responsável por excluir no BD um objeto da classe
     * <code>PlanoExcecaoVO</code>. Sempre localiza o registro a ser excluído
     * através da chave primária da entidade. Primeiramente verifica a conexão
     * com o banco de dados e a permissão do usuário para realizar esta operacão
     * na entidade. Isto, através da operação
     * <code>excluir</code> da superclasse.
     *
     * @param obj Objeto da classe <code>PlanoExcecaoVO</code> que será removido
     * no banco de dados.
     * @exception Exception Caso haja problemas de conexão ou restrição de
     * acesso.
     */
    @Override
    public void excluir(PlanoExcecaoVO obj) throws Exception {
        try {
            excluir(getIdEntidade());
            String sql = "DELETE FROM PlanoExcecao WHERE ((codigo = ?))";
            try (PreparedStatement ps = con.prepareStatement(sql)) {
                int i = 1;
                ps.setInt(i++, obj.getCodigo());
                ps.execute();
            }
        } catch (Exception e) {
            throw e;
        }
    }

    /**
     * Responsável por montar os dados de vários objetos, resultantes de uma
     * consulta ao banco de dados (
     * <code>ResultSet</code>). Faz uso da operação
     * <code>montarDados</code> que realiza o trabalho para um objeto por vez.
     *
     * @return List Contendo vários objetos da classe
     * <code>PlanoExcecaoVO</code> resultantes da consulta.
     */
    public static List<PlanoExcecaoVO> montarDadosConsulta(ResultSet tabelaResultado, int nivelMontarDados, Connection con) throws Exception {
        List<PlanoExcecaoVO> vetResultado = new ArrayList<PlanoExcecaoVO>();
        while (tabelaResultado.next()) {
            PlanoExcecaoVO obj = montarDados(tabelaResultado, nivelMontarDados, con);
            vetResultado.add(obj);
        }
        return vetResultado;
    }

    /**
     * Responsável por montar os dados resultantes de uma consulta ao banco de
     * dados (
     * <code>ResultSet</code>) em um objeto da classe
     * <code>PlanoExcecaoVO</code>.
     *
     * @return O objeto da classe <code>PlanoExcecaoVO</code> com os dados
     * devidamente montados.
     */
    public static PlanoExcecaoVO montarDados(ResultSet dadosSQL, int nivelMontarDados, Connection con) throws Exception {
        PlanoExcecaoVO obj = new PlanoExcecaoVO();
        obj.setCodigo(dadosSQL.getInt("codigo"));
        obj.setDuracao(dadosSQL.getInt("duracao"));
        obj.setValor(dadosSQL.getDouble("valor"));
        obj.setVezesSemana(dadosSQL.getInt("vezesSemana"));
        try {
            if (!UteisValidacao.emptyNumber(dadosSQL.getInt("horario"))) {
                HorarioInterfaceFacade horarioDao = new Horario(con);
                obj.setHorario(horarioDao.consultarPorChavePrimaria(dadosSQL.getInt("horario"), Uteis.NIVELMONTARDADOS_MINIMOS));
            }
            if (!UteisValidacao.emptyNumber(dadosSQL.getInt("modalidade"))) {
                ModalidadeInterfaceFacade modalidadeDao = new Modalidade(con);
                obj.setModalidade(modalidadeDao.consultarPorChavePrimaria(dadosSQL.getInt("modalidade"), Uteis.NIVELMONTARDADOS_DADOSBASICOS));
            }
            if (!UteisValidacao.emptyNumber(dadosSQL.getInt("categoria"))) {
                CategoriaInterfaceFacade categoriaDao = new Categoria(con);
                obj.setCategoria(categoriaDao.consultarPorChavePrimaria(dadosSQL.getInt("categoria"), Uteis.NIVELMONTARDADOS_DADOSBASICOS));
            }
            if (!UteisValidacao.emptyNumber(dadosSQL.getInt("pacote"))) {
                ComposicaoInterfaceFacade composicaoDao = new Composicao(con);
                obj.setPacote(composicaoDao.consultarPorChavePrimaria(dadosSQL.getInt("pacote"), Uteis.NIVELMONTARDADOS_MINIMOS));
            }
            if (!UteisValidacao.emptyNumber(dadosSQL.getInt("plano"))) {
                PlanoInterfaceFacade planoDao = new Plano(con);
                obj.setPlano(planoDao.consultarPorChavePrimaria(dadosSQL.getInt("plano"), Uteis.NIVELMONTARDADOS_MINIMOS));
            }
        } catch (Exception e) {
            Uteis.logar(e, PlanoExcecao.class);
        }
        obj.setNovoObj(false);
        if (nivelMontarDados == Uteis.NIVELMONTARDADOS_DADOSBASICOS) {
            return obj;
        }
        return obj;
    }

    /**
     * Operação responsável por localizar um objeto da classe
     * <code>PlanoExcecaoVO</code> através de sua chave primária.
     *
     * @exception Exception Caso haja problemas de conexão ou localização do
     * objeto procurado.
     */
    @Override
    public PlanoExcecaoVO consultarPorChavePrimaria(Integer codigoPrm, int nivelMontarDados) throws Exception {
        consultar(getIdEntidade(), false);
        String sql = "SELECT * FROM PlanoExcecao WHERE codigo = ?";
        try (PreparedStatement ps = con.prepareStatement(sql)) {
            int i = 1;
            ps.setInt(i++, codigoPrm);
            try (ResultSet tabelaResultado = ps.executeQuery()) {
                if (!tabelaResultado.next()) {
                    throw new ConsistirException("Dados Não Encontrados ( PlanoExcecao ).");
                }
                return (montarDados(tabelaResultado, nivelMontarDados, con));
            }
        }
    }

    @Override
    public List<PlanoExcecaoVO> consultar(String sql, final int nivelMontarDados, Connection con) throws Exception {
        try (ResultSet tabelaResultado = SuperFacadeJDBC.criarConsulta(sql, con)) {
            return (PlanoExcecao.montarDadosConsulta(tabelaResultado, nivelMontarDados, con));
        }
    }

    @Override
    public List<PlanoExcecaoVO> consultarPorPlano(final Integer plano) throws Exception {
        return consultar("select * from planoexcecao where plano = " + plano, Uteis.NIVELMONTARDADOS_DADOSBASICOS, this.con);
    }

    @Override
    public void incluirOuAlterarPlanoExcecoes(PlanoVO plano) throws Exception {
        List<PlanoExcecaoVO> atuais = plano.getPlanoExcecaoVOs();
        boolean excluirTudo = atuais.isEmpty();
        for (PlanoExcecaoVO exc : atuais) {
            exc.setPlano(plano);
            if (UteisValidacao.emptyNumber(exc.getCodigo())) {
                incluir(exc);
            } else {
                alterar(exc);
            }
        }
        final String aExcluir = Uteis.splitFromList("codigo", atuais, false);
        if (!UteisValidacao.emptyString(aExcluir)) {
            SuperFacadeJDBC.executarConsultaUpdate("delete from planoexcecao where plano = "
                    + plano.getCodigo() + " and codigo not in (" + aExcluir + ")", con);
        }
        if (excluirTudo) {
            SuperFacadeJDBC.executarConsultaUpdate("delete from planoexcecao where plano = "
                    + plano.getCodigo(), con);
        }
    }
}

package negocio.facade.jdbc.plano;

import negocio.comuns.basico.CategoriaVO;
import negocio.comuns.plano.PlanoCategoriaVO;
import negocio.comuns.utilitarias.Uteis;
import negocio.facade.jdbc.arquitetura.SuperEntidade;
import negocio.facade.jdbc.basico.Categoria;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.util.ArrayList;
import java.util.Iterator;
import java.util.List;

public class PlanoCategoria extends SuperEntidade {

    public PlanoCategoria() throws Exception {
        super();
        setIdEntidade("Plano");
    }

    public PlanoCategoria(Connection con) throws Exception {
        super(con);
        setIdEntidade("Plano");
    }

    public PlanoCategoriaVO novo() throws Exception {
        incluir(getIdEntidade());
        PlanoCategoriaVO obj = new PlanoCategoriaVO();
        return obj;
    }

    public void incluir(PlanoCategoriaVO obj) throws Exception {
        PlanoCategoriaVO.validarDados(obj);
        String sql = "INSERT INTO planocategoria ( plano, categoria ) VALUES ( ?, ? )";
        PreparedStatement sqlInserir = con.prepareStatement(sql);
        if (obj.getPlano() != 0) {
            sqlInserir.setInt(1, obj.getPlano());
        } else {
            sqlInserir.setNull(1, 0);
        }
        if (obj.getCategoria().getCodigo() != 0) {
            sqlInserir.setInt(2, obj.getCategoria().getCodigo());
        } else {
            sqlInserir.setNull(2, 0);
        }
        sqlInserir.execute();
        obj.setCodigo(obterValorChavePrimariaCodigo());
        obj.setNovoObj(false);
    }

    public void alterar(PlanoCategoriaVO obj) throws Exception {
        PlanoCategoriaVO.validarDados(obj);
        alterar(getIdEntidade());
        String sql = "UPDATE planocategoria set plano = ?, categoria = ? WHERE codigo = ?";
        PreparedStatement sqlAlterar = con.prepareStatement(sql);
        if (obj.getPlano() != 0) {
            sqlAlterar.setInt(1, obj.getPlano());
        } else {
            sqlAlterar.setNull(1, 0);
        }
        if (obj.getCategoria().getCodigo() != 0) {
            sqlAlterar.setInt(2, obj.getCategoria().getCodigo());
        } else {
            sqlAlterar.setNull(2, 0);
        }
        sqlAlterar.setInt(3, obj.getCodigo());
        sqlAlterar.execute();
    }

    public void alterarPorPlano(Integer plano, List objetos) throws Exception {
        String str = "DELETE FROM planocategoria WHERE plano = " + plano;
        Iterator i = objetos.iterator();
        while (i.hasNext()) {
            PlanoCategoriaVO objeto = (PlanoCategoriaVO) i.next();
            str += " AND codigo <> " + objeto.getCodigo();
        }
        PreparedStatement sqlExcluir = con.prepareStatement(str);
        sqlExcluir.execute();
        Iterator e = objetos.iterator();
        while (e.hasNext()) {
            PlanoCategoriaVO obj = (PlanoCategoriaVO) e.next();
            if (obj.getCodigo().equals(0)) {
                obj.setPlano(plano);
                incluir(obj);
            } else {
                alterar(obj);
            }
        }
    }

    public List<PlanoCategoriaVO> consultarPorPlano(Integer plano, int nivelMontarDados) throws Exception {
        consultar(getIdEntidade());
        List<PlanoCategoriaVO> objetos = new ArrayList();
        String sql = "SELECT * FROM planocategoria WHERE plano = ?";
        PreparedStatement sqlConsulta = con.prepareStatement(sql);
        sqlConsulta.setInt(1, plano.intValue());
        ResultSet resultado = sqlConsulta.executeQuery();
        while (resultado.next()) {
            PlanoCategoriaVO novoObj = new PlanoCategoriaVO();
            novoObj = PlanoCategoria.montarDados(resultado, nivelMontarDados, this.con);
            objetos.add(novoObj);
        }
        return objetos;
    }

    public static PlanoCategoriaVO montarDados(ResultSet dadosSQL, int nivelMontarDados, Connection con) throws Exception {
        PlanoCategoriaVO obj = new PlanoCategoriaVO();
        obj.setCodigo(dadosSQL.getInt("codigo"));
        obj.setPlano(dadosSQL.getInt("plano"));
        obj.getCategoria().setCodigo(dadosSQL.getInt("categoria"));
        obj.setNovoObj(Boolean.FALSE);
        if(nivelMontarDados == Uteis.NIVELMONTARDADOS_DADOSBASICOS) {
            return obj;
        }

        montarDadosCategoria(obj, Uteis.NIVELMONTARDADOS_TODOS, con);
        return obj;
    }

    public static void montarDadosCategoria(PlanoCategoriaVO obj, int nivelMontarDados, Connection con) throws Exception {
        if (obj.getCategoria().getCodigo() == 0) {
            obj.setCategoria(new CategoriaVO());
            return;
        }
        Categoria categoria = new Categoria(con);
        obj.setCategoria(categoria.consultarPorChavePrimaria(obj.getCategoria().getCodigo(), nivelMontarDados));
        categoria = null;
    }

    public void excluirPorPlano(Integer plano) throws Exception {
        excluir(getIdEntidade());
        String sql = "DELETE FROM planocategoria WHERE plano = ?";
        PreparedStatement sqlExcluir = con.prepareStatement(sql);
        sqlExcluir.setInt(1, plano);
        sqlExcluir.execute();
    }

    public void incluirPlanosCategorias(Integer planoPrm, List objetos) throws Exception {
        Iterator e = objetos.iterator();
        while (e.hasNext()) {
            PlanoCategoriaVO obj = (PlanoCategoriaVO) e.next();
            obj.setPlano(planoPrm);
            incluir(obj);
        }
    }

}

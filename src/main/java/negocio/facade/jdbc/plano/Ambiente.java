package negocio.facade.jdbc.plano;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import br.com.pactosolucoes.ce.comuns.enumerador.EnumTipoAmbiente;
import br.com.pactosolucoes.ce.comuns.to.TipoAmbienteTO;
import br.com.pactosolucoes.comuns.util.Declaracao;
import br.com.pactosolucoes.estudio.util.Validador;
import java.sql.*;
import java.util.Collections;
import negocio.comuns.crm.GenericoTO;

import negocio.comuns.plano.AmbienteVO;
import negocio.comuns.plano.TurmaVO;
import negocio.comuns.utilitarias.ConsistirException;
import negocio.comuns.utilitarias.Ordenacao;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisValidacao;
import negocio.facade.jdbc.arquitetura.SuperEntidade;
import negocio.interfaces.plano.AmbienteInterfaceFacade;

import org.json.JSONArray;
import org.json.JSONObject;

import servicos.integracao.mgb.impl.MgbServiceImpl;

/**
 * Classe de persistência que encapsula todas as operações de manipulação dos
 * dados da classe
 * <code>AmbienteVO</code>. Responsável por implementar operações como incluir,
 * alterar, excluir e consultar pertinentes a classe
 * <code>AmbienteVO</code>. Encapsula toda a interação com o banco de dados.
 *
 * @see AmbienteVO
 * @see SuperEntidade
 */
public class Ambiente extends SuperEntidade implements AmbienteInterfaceFacade {

    public Ambiente() throws Exception {
        super();
    }

    public Ambiente(Connection con) throws Exception {
        super(con);
    }

    /**
     * Operação responsável por retornar um novo objeto da classe
     * <code>AmbienteVO</code>.
     */
    public AmbienteVO novo() throws Exception {
        incluir(getIdEntidade());
        AmbienteVO obj = new AmbienteVO();
        return obj;
    }

    /**
     * Operação responsável por incluir no banco de dados um objeto da classe
     * <code>AmbienteVO</code>. Primeiramente valida os dados (
     * <code>validarDados</code>) do objeto. Verifica a conexão com o banco de
     * dados e a permissão do usuário para realizar esta operacão na entidade.
     * Isto, através da operação
     * <code>incluir</code> da superclasse.
     *
     * @param obj Objeto da classe
     * <code>AmbienteVO</code> que será gravado no banco de dados.
     * @exception Exception Caso haja problemas de conexão, restrição de acesso
     * ou validação de dados.
     */
    public void incluir(AmbienteVO obj) throws Exception {
        try {
            AmbienteVO.validarDados(obj);
            incluir(getIdEntidade());
            obj.realizarUpperCaseDados();
            String sql = "INSERT INTO Ambiente( descricao,capacidade, tipomodulo, situacao, codigopiscinamgb ) VALUES ( ?, ?,?,?, ? )";
            try (PreparedStatement sqlInserir = con.prepareStatement(sql)) {
                sqlInserir.setString(1, obj.getDescricao());
                if (Validador.isValidaInteger(obj.getCapacidade())) {
                    sqlInserir.setInt(2, obj.getCapacidade());
                } else {
                    sqlInserir.setNull(2, Types.INTEGER);
                }
                sqlInserir.setString(3, obj.getTipoModulo());
                sqlInserir.setInt(4, obj.getSituacaoAmbiente());
                sqlInserir.setString(5, obj.getCodigoPiscinaMgb());
                sqlInserir.execute();
            }
            obj.setCodigo(obterValorChavePrimariaCodigo());
            obj.setNovoObj(new Boolean(false));
        } catch (Exception e) {
            throw e;
        }
    }

    /**
     * Método que realiza as mesmas funções do método incluir dessa classe, mas
     * será acessado pelo Central de Eventos
     *
     * @param obj
     * @throws Exception
     */
    public void incluirCE(AmbienteVO obj) throws Exception {
        try {
            con.setAutoCommit(false);
            AmbienteVO.validarDadosCE(obj);
//            incluirObj(getIdEntidade());
            obj.realizarUpperCaseDados();
            String sql = "INSERT INTO Ambiente( descricao, tipoambiente, capacidademaximaconvidados, situacao, tipomodulo ) VALUES ( ?,?,?,?,? )";
            try (PreparedStatement sqlInserir = con.prepareStatement(sql)) {
                sqlInserir.setString(1, obj.getDescricao());
                sqlInserir.setInt(2, obj.getTipoAmbiente());
                sqlInserir.setInt(3, obj.getCapacidadeMaximaConvidados());
                sqlInserir.setInt(4, obj.getSituacaoAmbiente());
                sqlInserir.setString(5, "CE");
                sqlInserir.execute();
            }
            obj.setCodigo(obterValorChavePrimariaCodigo());
            obj.setNovoObj(new Boolean(false));
            con.commit();
        } catch (Exception e) {
            con.rollback();
            con.setAutoCommit(true);
            throw e;
        } finally {
            con.setAutoCommit(true);
        }
    }

    /**
     * Operação responsável por alterar no BD os dados de um objeto da classe
     * <code>AmbienteVO</code>. Sempre utiliza a chave primária da classe como
     * atributo para localização do registro a ser alterado. Primeiramente
     * valida os dados (
     * <code>validarDados</code>) do objeto. Verifica a conexão com o banco de
     * dados e a permissão do usuário para realizar esta operacão na entidade.
     * Isto, através da operação
     * <code>alterar</code> da superclasse.
     *
     * @param obj Objeto da classe
     * <code>AmbienteVO</code> que será alterada no banco de dados.
     * @exception Exception Caso haja problemas de conexão, restrição de acesso
     * ou validação de dados.
     */
    public void alterar(AmbienteVO obj) throws Exception {
        try {
            con.setAutoCommit(false);
            AmbienteVO.validarDados(obj);
            alterar(getIdEntidade());
            obj.realizarUpperCaseDados();
            String sql = "UPDATE Ambiente set descricao=?, capacidade = ?, tipomodulo =?, situacao =?, coletor =?, codigopiscinamgb = ? WHERE ((codigo = ?))";
            try (PreparedStatement sqlAlterar = con.prepareStatement(sql)) {
                sqlAlterar.setString(1, obj.getDescricao());

                if (Validador.isValidaInteger(obj.getCapacidade())) {
                    sqlAlterar.setInt(2, obj.getCapacidade());
                } else {
                    sqlAlterar.setNull(2, Types.INTEGER);
                }
                sqlAlterar.setString(3, obj.getTipoModulo());
                sqlAlterar.setInt(4, obj.getSituacaoAmbiente());
                resolveIntegerNull(sqlAlterar, 5, obj.getColetor());
                sqlAlterar.setString(6, obj.getCodigoPiscinaMgb());
                sqlAlterar.setInt(7, obj.getCodigo().intValue());
                sqlAlterar.execute();
            }
            con.commit();
        } catch (Exception e) {
            con.rollback();
            con.setAutoCommit(true);
            throw e;
        } finally {
            con.setAutoCommit(true);
        }
    }

    /**
     * Operação responsável por excluir no BD um objeto da classe
     * <code>AmbienteVO</code>. Sempre localiza o registro a ser excluído
     * através da chave primária da entidade. Primeiramente verifica a conexão
     * com o banco de dados e a permissão do usuário para realizar esta operacão
     * na entidade. Isto, através da operação
     * <code>excluir</code> da superclasse.
     *
     * @param obj Objeto da classe
     * <code>AmbienteVO</code> que será removido no banco de dados.
     * @exception Exception Caso haja problemas de conexão ou restrição de
     * acesso.
     */
    public void excluir(AmbienteVO obj) throws Exception {
        excluir(obj, false);
    }

    /**
     * Responsável por excluir um ambiente, recebendo um boolean que indica se o
     * método irá utlizar o controle de permissoes do CE
     *
     * <AUTHOR> 22/03/2011
     * @param obj
     * @param centralEventos
     * @throws Exception
     */
    public void excluir(AmbienteVO obj, boolean centralEventos) throws Exception {
        try {
            con.setAutoCommit(false);
            if (centralEventos) {
//                excluirObj(getIdEntidade());   	
            } else {
                excluir(getIdEntidade());
            }
            String sql = "DELETE FROM Ambiente WHERE ((codigo = ?))";
            try (PreparedStatement sqlExcluir = con.prepareStatement(sql)) {
                sqlExcluir.setInt(1, obj.getCodigo().intValue());
                sqlExcluir.execute();
            }
            con.commit();
        } catch (Exception e) {
            con.rollback();
            con.setAutoCommit(true);
            throw e;
        } finally {
            con.setAutoCommit(true);
        }
    }

    /**
     * Responsável por realizar uma consulta de
     * <code>Ambiente</code> através do valor do atributo
     * <code>String descricao</code>. Retorna os objetos, com início do valor do
     * atributo idêntico ao parâmetro fornecido. Faz uso da operação
     * <code>montarDadosConsulta</code> que realiza o trabalho de prerarar o
     * List resultante.
     *
     * @param controlarAcesso Indica se a aplicação deverá verificar se o
     * usuário possui permissão para esta consulta ou não.
     * @return List Contendo vários objetos da classe
     * <code>AmbienteVO</code> resultantes da consulta.
     * @exception Exception Caso haja problemas de conexão ou restrição de
     * acesso.
     */
    public List consultarPorDescricao(String valorConsulta, boolean controlarAcesso, int nivelMontarDados) throws Exception {
        consultar(getIdEntidade(), controlarAcesso);
        String sqlStr = "SELECT * FROM Ambiente WHERE upper( descricao ) like('" + valorConsulta.toUpperCase() + "%')  ORDER BY descricao";
        try (Statement stm = con.createStatement()) {
            try (ResultSet tabelaResultado = stm.executeQuery(sqlStr)) {
                return (montarDadosConsulta(tabelaResultado, nivelMontarDados));
            }
        }
    }

    public int consultarCodigoPorDescricao(String valorConsulta) throws Exception {
        String sqlStr = "SELECT codigo FROM Ambiente WHERE upper( descricao ) like('" + valorConsulta.toUpperCase() + "') ORDER BY descricao";
        try (Statement stm = con.createStatement()) {
            try (ResultSet tabelaResultado = stm.executeQuery(sqlStr)) {
                if (tabelaResultado.next()) {
                    return tabelaResultado.getInt("codigo");
                }
            }
        }
        return 0;
    }

    /**
     * Metodo que consulta todos os ambientes ignorando a sua situacao atual
     *
     * Autor: Pedro Y. Saito Criado em 31/03/2011
     */
    public List consultarTodosPorDescricao(String valorConsulta, boolean controlarAcesso, int nivelMontarDados) throws Exception {
        consultar(getIdEntidade(), controlarAcesso);
        String sqlStr = "SELECT * FROM Ambiente WHERE upper( descricao ) like('" + valorConsulta.toUpperCase() + "%') ORDER BY descricao";
        try (Statement stm = con.createStatement()) {
            try (ResultSet tabelaResultado = stm.executeQuery(sqlStr)) {
                return (montarDadosConsulta(tabelaResultado, nivelMontarDados));
            }
        }
    }

 /**
     * Metodo que consulta todos os ambientes ignorando a sua situacao atual
     *
     * Autor: Pedro Y. Saito Criado em 31/03/2011
     */
    public List consultarPorDescricaoPorAtivoEstudio(String valorConsulta, boolean controlarAcesso, int nivelMontarDados) throws Exception {
        consultar(getIdEntidade(), controlarAcesso);
        String sqlStr = "SELECT * FROM Ambiente WHERE upper( descricao ) like('" + valorConsulta.toUpperCase() + "%') and situacao = 1 and tipoModulo = 'SS' ORDER BY descricao";
        try (Statement stm = con.createStatement()) {
            try (ResultSet tabelaResultado = stm.executeQuery(sqlStr)) {
                return (montarDadosConsulta(tabelaResultado, nivelMontarDados));
            }
        }
    }

    /**
     * Responsável por realizar uma consulta de
     * <code>Ambiente</code> através do valor do atributo
     * <code>Integer codigoTipo</code>. Retorna os objetos que possuem tipo de
     * ambiente igual ao do parametro passado. Faz uso da operação
     * <code>montarDadosConsulta</code> que realiza o trabalho de prerarar o
     * List resultante.
     *
     * @param tipo
     * @param nivelMontarDados
     * @return
     * @throws Exception
     */
    public List<AmbienteVO> consultarPorTipo(TipoAmbienteTO tipo, int nivelMontarDados) throws Exception {
        String sqlStr = "SELECT * FROM Ambiente WHERE tipoambiente = " + tipo.getCodigo();
        try (Statement stm = con.createStatement()) {
            try (ResultSet tabelaResultado = stm.executeQuery(sqlStr)) {
                return (montarDadosConsulta(tabelaResultado, nivelMontarDados));
            }
        }
    }

    /**
     * Responsável por realizar uma consulta de
     * <code>Ambiente</code> através do valor do atributo
     * <code>Integer codigo</code>. Retorna os objetos com valores iguais ou
     * superiores ao parâmetro fornecido. Faz uso da operação
     * <code>montarDadosConsulta</code> que realiza o trabalho de prerarar o
     * List resultante.
     *
     * @param controlarAcesso Indica se a aplicação deverá verificar se o
     * usuário possui permissão para esta consulta ou não.
     * @return List Contendo vários objetos da classe
     * <code>AmbienteVO</code> resultantes da consulta.
     * @exception Exception Caso haja problemas de conexão ou restrição de
     * acesso.
     */
    public List<AmbienteVO> consultarPorCodigo(Integer valorConsulta, boolean controlarAcesso, int nivelMontarDados) throws Exception {
        consultar(getIdEntidade(), controlarAcesso);
        String sqlStr = "SELECT * FROM Ambiente WHERE codigo >= " + valorConsulta.intValue() + " ORDER BY codigo";
        try (Statement stm = con.createStatement()) {
            try (ResultSet tabelaResultado = stm.executeQuery(sqlStr)) {
                return (montarDadosConsulta(tabelaResultado, nivelMontarDados));
            }
        }
    }

    /**
     * Responsável por montar os dados de vários objetos, resultantes de uma
     * consulta ao banco de dados (
     * <code>ResultSet</code>). Faz uso da operação
     * <code>montarDados</code> que realiza o trabalho para um objeto por vez.
     *
     * @return List Contendo vários objetos da classe
     * <code>AmbienteVO</code> resultantes da consulta.
     */
    public static List<AmbienteVO> montarDadosConsulta(ResultSet tabelaResultado, int nivelMontarDados) throws Exception {
        List vetResultado = new ArrayList();
        while (tabelaResultado.next()) {
            AmbienteVO obj = new AmbienteVO();
            obj = montarDados(tabelaResultado, nivelMontarDados);
            vetResultado.add(obj);
        }
        return vetResultado;
    }

    /**
     * Responsável por montar os dados resultantes de uma consulta ao banco de
     * dados (
     * <code>ResultSet</code>) em um objeto da classe
     * <code>AmbienteVO</code>.
     *
     * @return O objeto da classe
     * <code>AmbienteVO</code> com os dados devidamente montados.
     */
    public static AmbienteVO montarDados(ResultSet dadosSQL, int nivelMontarDados) throws Exception {
        AmbienteVO obj = new AmbienteVO();
        obj.setCodigo(dadosSQL.getInt("codigo"));
        obj.setDescricao(dadosSQL.getString("descricao"));
        obj.setCapacidade(dadosSQL.getInt("capacidade"));
        obj.setSituacaoAmbiente(dadosSQL.getInt("situacao"));
        obj.setTipoModulo(dadosSQL.getString("tipomodulo"));
        try {
            obj.setColetor(dadosSQL.getInt("coletor"));
            obj.setCapacidadeMaximaConvidados(dadosSQL.getInt("capacidademaximaconvidados"));
            obj.setTipoAmbiente(dadosSQL.getInt("tipoambiente"));
            obj.setSituacaoAmbiente(dadosSQL.getInt("situacao"));
            obj.setCodigoPiscinaMgb(dadosSQL.getString("codigopiscinamgb"));
        } catch (Exception ignored) {

        }
        obj.setNovoObj(false);
        return obj;
    }

    /**
     * Operação responsável por localizar um objeto da classe
     * <code>AmbienteVO</code> através de sua chave primária.
     *
     * @exception Exception Caso haja problemas de conexão ou localização do
     * objeto procurado.
     */
    public AmbienteVO consultarPorChavePrimaria(Integer codigoPrm, int nivelMontarDados) throws Exception {
        consultar(getIdEntidade(), false);
        AmbienteVO eCache = (AmbienteVO) obterFromCache(codigoPrm);
        if (eCache != null){
            return eCache;
        }
        String sql = "SELECT * FROM Ambiente WHERE codigo = ?";
        try (PreparedStatement sqlConsultar = con.prepareStatement(sql)) {
            sqlConsultar.setInt(1, codigoPrm.intValue());
            try (ResultSet tabelaResultado = sqlConsultar.executeQuery()) {
                if (!tabelaResultado.next()) {
                    throw new ConsistirException("Dados Não Encontrados ( Ambiente ).");
                }
                eCache = (montarDados(tabelaResultado, nivelMontarDados));
            }
        }
        putToCache(eCache);
        return eCache;
    }

    /**
     * Método responsável pelas mesmas ações do método alterar(), mas será
     * acessado pelo Central de Eventos
     *
     * @param obj
     * @throws Exception
     */
    public void alterarCE(AmbienteVO obj) throws Exception {
        try {
            con.setAutoCommit(false);
            AmbienteVO.validarDadosCE(obj);
//            alterarObj(getIdEntidade());
            obj.realizarUpperCaseDados();
            String sql = "UPDATE Ambiente set descricao=?,tipoambiente = ?, capacidademaximaconvidados = ?, situacao=?, tipomodulo =? WHERE ((codigo = ?))";
            try (PreparedStatement sqlAlterar = con.prepareStatement(sql)) {
                sqlAlterar.setString(1, obj.getDescricao());
                sqlAlterar.setInt(2, obj.getTipoAmbiente());
                sqlAlterar.setInt(3, obj.getCapacidadeMaximaConvidados());
                sqlAlterar.setInt(4, obj.getSituacaoAmbiente());
                sqlAlterar.setString(5, "CE");
                sqlAlterar.setInt(6, obj.getCodigo().intValue());
                sqlAlterar.execute();
            }
            con.commit();
        } catch (Exception e) {
            con.rollback();
            con.setAutoCommit(true);
            throw e;
        } finally {
            con.setAutoCommit(true);
        }
    }

    /**
     * Método responsável por consultar o tipo de um determinado ambiente
     *
     * @param codigoAmbiente o valor do código do ambiente
     * @return
     * @throws SQLException
     */
    public EnumTipoAmbiente consultarTipoAmbiente(Integer codigoAmbiente) throws SQLException {
        //montar string com a sql da consulta
        String sqlStr = "SELECT TIPOAMBIENTE FROM Ambiente WHERE codigo = " + codigoAmbiente.intValue();
        //executar a consulta
        try (Statement stm = con.createStatement()) {
            //guardar resultado num resultset
            try (ResultSet resultado = stm.executeQuery(sqlStr)) {
                //retornar o tipo de ambiente correspondente
                resultado.next();
                return EnumTipoAmbiente.getTipoAmbiente(resultado.getInt("TIPOAMBIENTE"));
            }
        }


    }

    /**
     * Método responsável por consultar o ambiente de uma negociacao associada a
     * um evento
     *
     * @param codigoEvento
     * <AUTHOR>
     * @return ambiente da negociacao associada ao evento
     * @throws Exception
     */
    public AmbienteVO obterAmbNegociacaoEvento(Integer codigoEvento) throws Exception {
        StringBuilder sql = new StringBuilder();
        //selecionar o ambiente
        sql.append("SELECT A.descricao, A.codigo, A.tipoambiente, A.capacidademaximaconvidados, A.situacao, A.capacidade, A.tipoModulo FROM ambiente A ");
        //relacionado a negociacao
        sql.append("INNER JOIN negociacaoeventoperfileventoambiente N ON N.ambiente = A.codigo ");
        //da negociacao relacionada ao evento
        sql.append("INNER JOIN negociacaoevento NE ON NE.codigo = N.negociacaoevento ");
        // do evento de codigo ?
        sql.append("INNER JOIN eventointeresse E ON E.codigo = NE.eventointeresse ");
        sql.append("WHERE E.codigo = ? ");
        Declaracao dc = new Declaracao(sql.toString(), con);
        dc.setInt(1, codigoEvento);
        try (ResultSet sqlDados = dc.executeQuery()) {
            if (sqlDados.next()) {
                return montarDados(sqlDados, Uteis.NIVELMONTARDADOS_TODOS);
            } else {
                return new AmbienteVO();
            }
        }
    }

    @SuppressWarnings("unchecked")
    public List<AmbienteVO> consultarAmbNegociacaoEvento(Integer codigoEvento) throws Exception {
        final List vetResultado = new ArrayList();

        StringBuilder sql = new StringBuilder();
        //selecionar o ambiente
        sql.append("SELECT A.descricao, A.codigo, A.tipoambiente, A.capacidademaximaconvidados, A.situacao, A.capacidade, A.tipoModulo FROM ambiente A ");
        //relacionado a negociacao
        sql.append("INNER JOIN negociacaoeventoperfileventoambiente N ON N.ambiente = A.codigo ");
        //da negociacao relacionada ao evento
        sql.append("INNER JOIN negociacaoevento NE ON NE.codigo = N.negociacaoevento ");
        // do evento de codigo ?
        sql.append("INNER JOIN eventointeresse E ON E.codigo = NE.eventointeresse ");
        sql.append("WHERE E.codigo = ? ");
        Declaracao dc = new Declaracao(sql.toString(), con);
        dc.setInt(1, codigoEvento);
        try (ResultSet sqlDados = dc.executeQuery()) {
            while (sqlDados.next()) {
                final Object obj = montarDados(sqlDados, Uteis.NIVELMONTARDADOS_TODOS);
                vetResultado.add(obj);
            }
        }

        return vetResultado;
    }

    /**
     * @param codigoAmbiente
     * @return AmbienteVO
     * @throws Exception
     */
    public AmbienteVO obterAmbiente(Integer codigoAmbiente) throws Exception {
        StringBuilder sql = new StringBuilder();
        sql.append("SELECT descricao, codigo, tipoambiente, capacidademaximaconvidados, situacao, capacidade, tipomodulo FROM ambiente");
        sql.append(" where codigo = ?");
        Declaracao dc = new Declaracao(sql.toString(), con);
        dc.setInt(1, codigoAmbiente);
        try (ResultSet sqlDados = dc.executeQuery()) {
            if (sqlDados.next()) {
                return montarDados(sqlDados, Uteis.NIVELMONTARDADOS_TODOS);
            } else {
                return new AmbienteVO();
            }
        }
    }
    
    /**
	 * Responsável por consultar todos os registros de ambientes do central de eventos de forma simplificada, montando num mapa.
	 * <AUTHOR>
	 * 12/03/2013
	 */
	public Map<Integer, String> consultarAmbientesCESimplificado() throws Exception{
		Map<Integer, String> mapResult = new HashMap<Integer, String>();
		String sql = "SELECT descricao, codigo FROM ambiente WHERE tipomodulo like 'CE'";
        try (ResultSet consulta = criarConsulta(sql, con)) {
            while (consulta.next()) {
                mapResult.put(consulta.getInt("codigo"), consulta.getString("descricao"));
            }
        }
        return mapResult;
	}
	
    public List<AmbienteVO> consultarAmbientesSimplificado() throws Exception {
        List<AmbienteVO> ambientes = new ArrayList<AmbienteVO>();
        String sql = "SELECT descricao, codigo, capacidade FROM ambiente ";
        try (ResultSet consulta = Modalidade.criarConsulta(sql, con)) {
            while (consulta.next()) {
                AmbienteVO amb = new AmbienteVO();
                amb.setCodigo(consulta.getInt("codigo"));
                amb.setDescricao(consulta.getString("descricao"));
                amb.setCapacidade(consulta.getInt("capacidade"));
                ambientes.add(amb);
            }
        }
        return ambientes;
    }

    public List<AmbienteVO> consultarPorTurma(Integer valorConsulta, boolean controlarAcesso, int nivelMontarDados) throws Exception {
        consultar(getIdEntidade(), controlarAcesso);
        String sqlStr = "select distinct a.* from ambiente a inner join horarioturma ht on ht.ambiente = a.codigo and ht.turma = "+valorConsulta+" order by a.descricao ";
        try (Statement stm = con.createStatement()) {
            try (ResultSet tabelaResultado = stm.executeQuery(sqlStr)) {
                return (montarDadosConsulta(tabelaResultado, nivelMontarDados));
            }
        }
    }

    public String consultarJSON(Integer empresa) throws Exception {
        Boolean mgb;
        MgbServiceImpl mgbService = new MgbServiceImpl(getCon());
        Map<String, String> objsMgb = new HashMap<>();
        try {
            mgb = mgbService.integradoMgb(empresa);
            if (mgb) {
                JSONObject jsonObject = mgbService.consultarPiscinas(empresa);
                JSONArray lista = new JSONObject(jsonObject.get("data").toString()).getJSONArray("items");
                for (int e = 0; e < lista.length(); e++) {
                    JSONObject obj = lista.getJSONObject(e);
                    objsMgb.put(obj.getString("publicId"), obj.getString("name"));
                }
            }
        } catch (Exception e) {
            mgb = false;
        }


        StringBuilder json = new StringBuilder();
        json.append("{\"aaData\":[");
        boolean dados = false;
        try (ResultSet rs = getPS().executeQuery()) {
            while (rs.next()) {
                dados = true;
                json.append("[\"").append(rs.getString("codigo")).append("\",");
                json.append("\"").append(Uteis.normalizarStringJSON(rs.getString("descricao").trim())).append("\",");
                if (mgb) {
                    try {
                        String piscinaMgb = UteisValidacao.emptyString(rs.getString("codigopiscinamgb"))
                                || objsMgb.get(rs.getString("codigopiscinamgb")) == null ? "-" : objsMgb.get(rs.getString("codigopiscinamgb"));
                        json.append("\"").append(Uteis.normalizarStringJSON(piscinaMgb.trim())).append("\",");
                    } catch (Exception e) { }
                }
                if (rs.getBoolean("situacao")) {
                    json.append("\"").append("Ativo").append("\"],");
                } else {
                    json.append("\"").append("Inativo").append("\"],");
                }
                //json.append("\"").append(rs.getString("tipoambiente")).append("\"],");
            }
        }
        if (dados) {
            json.deleteCharAt(json.toString().length() - 1);
        }
        json.append("]}");
        return json.toString();
    }

    private PreparedStatement getPS() throws SQLException {
        String sql = "SELECT amb.codigo, amb.descricao, situacao, ta.descricao AS tipoambiente, amb.codigopiscinamgb \n" + "FROM ambiente amb \n" + "  LEFT JOIN tipoambiente ta ON amb.tipoambiente = ta.codigo\n" + "  ORDER BY amb.descricao";
        return con.prepareStatement(sql);
    }
    public List consultarParaImpressao(String filtro, String ordem, String campoOrdenacao, int i) throws SQLException {
        List lista = new ArrayList();
        try (ResultSet rs = getPS().executeQuery()) {
            while (rs.next()) {
                AmbienteVO amb = new AmbienteVO();
                String geral = rs.getString("codigo") + rs.getString("descricao") + rs.getString("situacao");
                if (geral.toLowerCase().contains(filtro.toLowerCase())) {
                    amb.setCodigo(rs.getInt("codigo"));
                    amb.setDescricao(rs.getString("descricao"));
                    amb.setSelecionado(rs.getBoolean("situacao"));
                    lista.add(amb);
                }
            }
        }
        if (campoOrdenacao.equals("Código")) {
            Ordenacao.ordenarLista(lista, "codigo");
        } else if (campoOrdenacao.equals("Descrição")) {
            Ordenacao.ordenarLista(lista, "descricao");
        } else if (campoOrdenacao.equals("Situação")) {
            Ordenacao.ordenarLista(lista, "situacao");
        }
        if (ordem.contains("desc")) {
            Collections.reverse(lista);
        }
        return lista;

    }

    public boolean existeAmbienteComTerminal(String terminal) throws Exception {
	    if (UteisValidacao.emptyString(terminal))
	        return false;
        return existe("SELECT amb.codigo FROM ambiente amb\n" +
                "INNER JOIN coletor col ON amb.coletor = col.codigo\n" +
                "WHERE col.numeroterminal = '" + terminal + "' AND amb.situacao = 1", this.con);
    }
}

package negocio.facade.jdbc.plano;

import negocio.comuns.plano.ModeloOrcamentoVO;
import negocio.comuns.plano.OrcamentoVO;
import negocio.comuns.utilitarias.ConsistirException;
import negocio.comuns.utilitarias.Uteis;
import negocio.facade.jdbc.arquitetura.SuperEntidade;
import negocio.interfaces.plano.OrcamentoInterfaceFacade;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.util.ArrayList;
import java.util.List;

public class Orcamento extends SuperEntidade implements OrcamentoInterfaceFacade {

    public Orcamento() throws Exception {
        super();
        setIdEntidade("Orcamento");
    }

    public Orcamento(Connection con) throws Exception {
        super(con);
    }

    private static final String sqlInsert = "INSERT INTO orcamento("
            + " cliente, nomeProspecto, consultor, paraquem, modeloorcamento, situacao, anotacao, idade, data, periodo, tipoturma ) "
            + "VALUES ( ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ? )";
    private static final String sqlUpdate = "UPDATE orcamento set cliente=?, "
            + "nomeProspecto=?, consultor=?, paraquem=?, modeloorcamento=?, situacao=?, anotacao=?, "
            + "idade=?, data=?, periodo=?, tipoturma=? "
            + "WHERE ((codigo = ?))";


    public void incluir(OrcamentoVO obj) throws Exception {
        try {
            con.setAutoCommit(false);
            PreparedStatement sqlInserir = prepararIncluir(obj);
            sqlInserir.execute();
            obj.setCodigo(obterValorChavePrimariaCodigo());
            obj.setNovoObj(new Boolean(false));
            con.commit();
        } catch (Exception e) {
            obj.setNovoObj(new Boolean(true));
            con.rollback();
            con.setAutoCommit(true);
            throw e;
        } finally {
            con.setAutoCommit(true);
        }
    }

    public void incluirSemCommit(OrcamentoVO obj) throws Exception {
        try {
            PreparedStatement sqlInserir = prepararIncluir(obj);
            sqlInserir.execute();
            obj.setCodigo(obterValorChavePrimariaCodigo());
            obj.setNovoObj(false);
        } catch (Exception e) {
            obj.setNovoObj(true);
            throw e;
        }
    }

    public void alterar(OrcamentoVO obj) throws Exception {
        try {
            con.setAutoCommit(false);
            alterarSemCommit(obj);
            con.commit();
        } catch (Exception e) {
            con.rollback();
            con.setAutoCommit(true);
            throw e;
        } finally {
            con.setAutoCommit(true);
        }
    }

    public void alterarSemCommit(OrcamentoVO obj) throws Exception{
        PreparedStatement sqlAlterar = prepararAlterar(obj);
        sqlAlterar.execute();
    }

    public void excluir(OrcamentoVO obj) throws Exception {
        try {
            con.setAutoCommit(false);
            excluir(getIdEntidade());
            String sql = "DELETE FROM orcamento WHERE ((codigo = ?))";
            PreparedStatement sqlExcluir = con.prepareStatement(sql);
            sqlExcluir.setInt(1, obj.getCodigo().intValue());
            sqlExcluir.execute();
            con.commit();
        } catch (Exception e) {
            con.rollback();
            con.setAutoCommit(true);
            throw e;
        } finally {
            con.setAutoCommit(true);
        }
    }

    private PreparedStatement prepararIncluir(OrcamentoVO obj) throws Exception {

        PreparedStatement sqlInserir = con.prepareStatement(sqlInsert);
        sqlInserir.setInt(1, obj.getCliente());
        sqlInserir.setString(2, obj.getNomeProspecto());
        sqlInserir.setInt(3, obj.getConsultor());
        sqlInserir.setInt(4, obj.getParaQuem());
        sqlInserir.setInt(5, obj.getModeloOrcamento());
        sqlInserir.setInt(6, obj.getSituacao());
        sqlInserir.setString(7, obj.getAnotacao());
        sqlInserir.setInt(8, obj.getIdade());
        sqlInserir.setDate(9, Uteis.getDataJDBC(obj.getData()));
        sqlInserir.setInt(10, obj.getPeriodo());
        sqlInserir.setInt(11, obj.getTipoTurma());

        return sqlInserir;
    }

    private PreparedStatement prepararAlterar(OrcamentoVO obj) throws Exception {

        PreparedStatement sqlAlterar = con.prepareStatement(sqlUpdate);

        sqlAlterar.setInt(1, obj.getCliente());
        sqlAlterar.setString(2, obj.getNomeProspecto());
        sqlAlterar.setInt(3, obj.getConsultor());
        sqlAlterar.setInt(4, obj.getParaQuem());
        sqlAlterar.setInt(5, obj.getModeloOrcamento());
        sqlAlterar.setInt(6, obj.getSituacao());
        sqlAlterar.setString(7, obj.getAnotacao());
        sqlAlterar.setInt(8, obj.getIdade());
        sqlAlterar.setDate(9, Uteis.getDataJDBC(obj.getData()));
        sqlAlterar.setInt(10, obj.getPeriodo());
        sqlAlterar.setInt(11, obj.getTipoTurma());

        sqlAlterar.setInt(12, obj.getCodigo().intValue());
        return sqlAlterar;
    }

    public OrcamentoVO consultarPorChavePrimaria(Integer codigoPrm, int nivelMontarDados) throws Exception {
        consultar(getIdEntidade(), false);
        String sql = "SELECT codigo, cliente, nomeProspecto, consultor, paraquem, modeloorcamento, situacao, anotacao, idade, data, periodo, tipoturma FROM orcamento WHERE codigo = ?";
        PreparedStatement sqlConsultar = con.prepareStatement(sql);
        sqlConsultar.setInt(1, codigoPrm);
        ResultSet tabelaResultado = sqlConsultar.executeQuery();
        if (!tabelaResultado.next()) {
            throw new ConsistirException("Dados Não Encontrados ( Orcamento ).");
        }
        return (montarDados(tabelaResultado, nivelMontarDados, this.con));
    }

    public List<OrcamentoVO> consultarPorCodCliente(Integer codigoPrm, int nivelMontarDados) throws Exception {
        List<OrcamentoVO> orcamentos = new ArrayList<>();
        consultar(getIdEntidade(), false);
        String sql = "SELECT codigo, cliente, nomeProspecto, consultor, paraquem, modeloorcamento, situacao, anotacao, idade, data, periodo, tipoturma FROM orcamento WHERE cliente = ?";
        PreparedStatement sqlConsultar = con.prepareStatement(sql);
        sqlConsultar.setInt(1, codigoPrm);
        ResultSet tabelaResultado = sqlConsultar.executeQuery();
        while(tabelaResultado.next()){
            orcamentos.add((montarDados(tabelaResultado, nivelMontarDados, this.con)));
        }
        return orcamentos;
    }

    public static OrcamentoVO montarDados(ResultSet dadosSQL, int nivelMontarDados, Connection con) throws Exception {
        OrcamentoVO obj = new OrcamentoVO();
        obj.setCodigo(dadosSQL.getInt("codigo"));
        obj.setCliente(dadosSQL.getInt("cliente"));
        obj.setNomeProspecto(dadosSQL.getString("nomeProspecto"));
        obj.setConsultor(dadosSQL.getInt("consultor"));
        obj.setParaQuem(dadosSQL.getInt("paraquem"));
        obj.setModeloOrcamento(dadosSQL.getInt("modeloorcamento"));
        obj.setSituacao(dadosSQL.getInt("situacao"));
        obj.setAnotacao(dadosSQL.getString("anotacao"));
        obj.setIdade(dadosSQL.getInt("idade"));
        obj.setData(dadosSQL.getDate("data"));
        obj.setPeriodo(dadosSQL.getInt("periodo"));
        obj.setTipoTurma(dadosSQL.getInt("tipoturma"));
        obj.setNovoObj(false);
        if (nivelMontarDados == Uteis.NIVELMONTARDADOS_DADOSBASICOS) {
            return obj;
        }

        return obj;
    }

}

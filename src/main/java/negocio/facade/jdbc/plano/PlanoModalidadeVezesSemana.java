package negocio.facade.jdbc.plano;

import negocio.interfaces.plano.*;
import negocio.comuns.plano.PlanoModalidadeVezesSemanaVO;
import negocio.facade.jdbc.arquitetura.*;

import java.sql.Connection;
import java.sql.ResultSet;
import java.sql.PreparedStatement;
import java.sql.Statement;
import java.util.List;
import java.util.ArrayList;
import java.util.Iterator;
import negocio.comuns.utilitarias.*;

/**
 * Classe de persistência que encapsula todas as operações de manipulação dos
 * dados da classe
 * <code>PlanoModalidadeVezesSemanaVO</code>. Responsável por implementar
 * operações como incluir, alterar, excluir e consultar pertinentes a classe
 * <code>PlanoModalidadeVezesSemanaVO</code>. Encapsula toda a interação com o
 * banco de dados.
 *
 * @see PlanoModalidadeVezesSemanaVO
 * @see SuperEntidade
 */
public class PlanoModalidadeVezesSemana extends SuperEntidade implements PlanoModalidadeVezesSemanaInterfaceFacade {

    public PlanoModalidadeVezesSemana() throws Exception {
        super();
        setIdEntidade("Plano");
    }

    public PlanoModalidadeVezesSemana(Connection con) throws Exception {
        super(con);
        setIdEntidade("Plano");
    }

    /**
     * Operação responsável por retornar um novo objeto da classe
     * <code>PlanoModalidadeVezesSemanaVO</code>.
     */
    public PlanoModalidadeVezesSemanaVO novo() throws Exception {
        incluir(getIdEntidade());
        PlanoModalidadeVezesSemanaVO obj = new PlanoModalidadeVezesSemanaVO();
        return obj;
    }

    /**
     * Operação responsável por incluir no banco de dados um objeto da classe
     * <code>PlanoModalidadeVezesSemanaVO</code>. Primeiramente valida os dados
     * (
     * <code>validarDados</code>) do objeto. Verifica a conexão com o banco de
     * dados e a permissão do usuário para realizar esta operacão na entidade.
     * Isto, através da operação
     * <code>incluir</code> da superclasse.
     *
     * @param obj Objeto da classe <code>PlanoModalidadeVezesSemanaVO</code> que
     * será gravado no banco de dados.
     * @exception Exception Caso haja problemas de conexão, restrição de acesso
     * ou validação de dados.
     */
    public void incluir(PlanoModalidadeVezesSemanaVO obj) throws Exception {
        PlanoModalidadeVezesSemanaVO.validarDados(obj, false);
        //PlanoModalidadeVezesSemana.incluir(getIdEntidade());
        obj.realizarUpperCaseDados();
        String sql = "INSERT INTO PlanoModalidadeVezesSemana( nrVezes, planoModalidade, percentualDesconto, "
                + "valorEspecifico,  tipoValor, tipoOperacao, referencia) "
                + "VALUES ( ?, ?, ?, ?, ?, ?, ? )";
        PreparedStatement ps = con.prepareStatement(sql);
        int i = 1;
        ps.setInt(i++, obj.getNrVezes());
        ps.setInt(i++, obj.getPlanoModalidade());
        ps.setDouble(i++, obj.getPercentualDesconto());
        ps.setDouble(i++, obj.getValorEspecifico());
        ps.setString(i++, obj.getTipoValor());
        ps.setString(i++, obj.getTipoOperacao());
        ps.setBoolean(i++, obj.isReferencia());
        ps.execute();
        obj.setCodigo(obterValorChavePrimariaCodigo());
        obj.setNovoObj(false);
    }

    /**
     * Operação responsável por alterar no BD os dados de um objeto da classe
     * <code>PlanoModalidadeVezesSemanaVO</code>. Sempre utiliza a chave
     * primária da classe como atributo para localização do registro a ser
     * alterado. Primeiramente valida os dados (
     * <code>validarDados</code>) do objeto. Verifica a conexão com o banco de
     * dados e a permissão do usuário para realizar esta operacão na entidade.
     * Isto, através da operação
     * <code>alterar</code> da superclasse.
     *
     * @param obj Objeto da classe <code>PlanoModalidadeVezesSemanaVO</code> que
     * será alterada no banco de dados.
     * @exception Execption Caso haja problemas de conexão, restrição de acesso
     * ou validação de dados.
     */
    public void alterar(PlanoModalidadeVezesSemanaVO obj) throws Exception {
        PlanoModalidadeVezesSemanaVO.validarDados(obj, false);
        alterar(getIdEntidade());
        obj.realizarUpperCaseDados();
        String sql = "UPDATE PlanoModalidadeVezesSemana set nrVezes=?, planoModalidade=?, percentualDesconto=?, "
                + "valorEspecifico=?, tipoValor=?, tipoOperacao=?, referencia=? "
                + "WHERE ((codigo = ?))";
        PreparedStatement ps = con.prepareStatement(sql);
        int i = 1;
        ps.setInt(i++, obj.getNrVezes());
        ps.setInt(i++, obj.getPlanoModalidade());
        ps.setDouble(i++, obj.getPercentualDesconto());
        ps.setDouble(i++, obj.getValorEspecifico());
        ps.setString(i++, obj.getTipoValor());
        ps.setString(i++, obj.getTipoOperacao());
        ps.setBoolean(i++, obj.isReferencia());
        ps.setInt(i++, obj.getCodigo());
        ps.execute();
    }

    /**
     * Operação responsável por excluir no BD um objeto da classe
     * <code>PlanoModalidadeVezesSemanaVO</code>. Sempre localiza o registro a
     * ser excluído através da chave primária da entidade. Primeiramente
     * verifica a conexão com o banco de dados e a permissão do usuário para
     * realizar esta operacão na entidade. Isto, através da operação
     * <code>excluir</code> da superclasse.
     *
     * @param obj Objeto da classe <code>PlanoModalidadeVezesSemanaVO</code> que
     * será removido no banco de dados.
     * @exception Execption Caso haja problemas de conexão ou restrição de
     * acesso.
     */
    public void excluir(PlanoModalidadeVezesSemanaVO obj) throws Exception {
        excluir(getIdEntidade());
        String sql = "DELETE FROM PlanoModalidadeVezesSemana WHERE ((codigo = ?))";
        PreparedStatement sqlExcluir = con.prepareStatement(sql);
        sqlExcluir.setInt(1, obj.getCodigo().intValue());
        sqlExcluir.execute();
    }

    /**
     * Responsável por realizar uma consulta de
     * <code>PlanoModalidadeVezesSemana</code> através do valor do atributo
     * <code>Integer planoModalidade</code>. Retorna os objetos com valores
     * iguais ou superiores ao parâmetro fornecido. Faz uso da operação
     * <code>montarDadosConsulta</code> que realiza o trabalho de prerarar o
     * List resultante.
     *
     * @param controlarAcesso Indica se a aplicação deverá verificar se o
     * usuário possui permissão para esta consulta ou não.
     * @return List Contendo vários objetos da      * classe <code>PlanoModalidadeVezesSemanaVO</code> resultantes da
     * consulta.
     * @exception Exception Caso haja problemas de conexão ou restrição de
     * acesso.
     */
    public List consultarPorPlanoModalidade(Integer valorConsulta, boolean controlarAcesso, int nivelMontarDados) throws Exception {
        consultar(getIdEntidade(), controlarAcesso);
        String sqlStr = "SELECT * FROM PlanoModalidadeVezesSemana WHERE planoModalidade >= " + valorConsulta.intValue() + " ORDER BY planoModalidade";
        Statement stm = con.createStatement();
        ResultSet tabelaResultado = stm.executeQuery(sqlStr);
        return (montarDadosConsulta(tabelaResultado, nivelMontarDados));
    }

    public PlanoModalidadeVezesSemanaVO consultar(Integer codigoPlanoModalidade, Integer nrVezes, int nivelMontarDados) throws Exception {
        StringBuilder sql = new StringBuilder();
        sql.append("select * \n");
        sql.append("from PlanoModalidadeVezesSemana \n");
        sql.append("where planoModalidade = ").append(codigoPlanoModalidade).append(" \n");
        sql.append("and nrVezes = ").append(nrVezes);
        Statement stm = con.createStatement();
        ResultSet rs = stm.executeQuery(sql.toString());
        if (rs.next()){
            return (montarDados(rs, nivelMontarDados));
        }
        return null;
    }

    /**
     * Responsável por realizar uma consulta de
     * <code>PlanoModalidadeVezesSemana</code> através do valor do atributo
     * <code>nrVezes</code> da classe
     * <code>VezesSemana</code> Faz uso da operação
     * <code>montarDadosConsulta</code> que realiza o trabalho de prerarar o
     * List resultante.
     *
     * @return List Contendo vários objetos da      * classe <code>PlanoModalidadeVezesSemanaVO</code> resultantes da
     * consulta.
     * @exception Exception Caso haja problemas de conexão ou restrição de
     * acesso.
     */
    public List consultarPorNrVezesVezesSemana(Integer valorConsulta, int nivelMontarDados) throws Exception {
        consultar(getIdEntidade(), true);
        String sqlStr = "SELECT PlanoModalidadeVezesSemana.* FROM PlanoModalidadeVezesSemana, VezesSemana WHERE PlanoModalidadeVezesSemana.vezesSemana = VezesSemana.codigo and VezesSemana.nrVezes >= " + valorConsulta.intValue() + " ORDER BY VezesSemana.nrVezes";
        Statement stm = con.createStatement();
        ResultSet tabelaResultado = stm.executeQuery(sqlStr);
        return montarDadosConsulta(tabelaResultado, nivelMontarDados);
    }

    /**
     * Operação responsável por localizar um objeto da classe
     * <code>PlanoModalidadeVezesSemanaVO</code> através de sua chave primária.
     *
     * @exception Exception Caso haja problemas de conexão ou localização do
     * objeto procurado.
     */
    public PlanoModalidadeVezesSemanaVO consultarPorNrVezeSemana(Integer valorConsulta, int nivelMontarDados) throws Exception {
        consultar(getIdEntidade(), false);
        String sql = "SELECT PlanoModalidadeVezesSemana.* FROM PlanoModalidadeVezesSemana WHERE PlanoModalidadeVezesSemana.nrVezes = ?";
        PreparedStatement sqlConsultar = con.prepareStatement(sql);
        sqlConsultar.setInt(1, valorConsulta);
        ResultSet tabelaResultado = sqlConsultar.executeQuery();
        if (!tabelaResultado.next()) {
            throw new ConsistirException("Quantidade de VEZES POR SEMANA INDISPONIVEL para esse PLANO.");
        }
        return (montarDados(tabelaResultado, nivelMontarDados));
    }

    /**
     * Responsável por realizar uma consulta de
     * <code>PlanoModalidadeVezesSemana</code> através do valor do atributo
     * <code>Integer codigo</code>. Retorna os objetos com valores iguais ou
     * superiores ao parâmetro fornecido. Faz uso da operação
     * <code>montarDadosConsulta</code> que realiza o trabalho de prerarar o
     * List resultante.
     *
     * @param controlarAcesso Indica se a aplicação deverá verificar se o
     * usuário possui permissão para esta consulta ou não.
     * @return List Contendo vários objetos da      * classe <code>PlanoModalidadeVezesSemanaVO</code> resultantes da
     * consulta.
     * @exception Exception Caso haja problemas de conexão ou restrição de
     * acesso.
     */
    public List consultarPorCodigo(Integer valorConsulta, boolean controlarAcesso, int nivelMontarDados) throws Exception {
        consultar(getIdEntidade(), controlarAcesso);
        String sqlStr = "SELECT * FROM PlanoModalidadeVezesSemana WHERE codigo >= " + valorConsulta.intValue() + " ORDER BY codigo";
        Statement stm = con.createStatement();
        ResultSet tabelaResultado = stm.executeQuery(sqlStr);
        return (montarDadosConsulta(tabelaResultado, nivelMontarDados));
    }

    /**
     * Responsável por montar os dados de vários objetos, resultantes de uma
     * consulta ao banco de dados (
     * <code>ResultSet</code>). Faz uso da operação
     * <code>montarDados</code> que realiza o trabalho para um objeto por vez.
     *
     * @return List Contendo vários objetos da      * classe <code>PlanoModalidadeVezesSemanaVO</code> resultantes da
     * consulta.
     */
    public static List montarDadosConsulta(ResultSet tabelaResultado, int nivelMontarDados) throws Exception {
        List vetResultado = new ArrayList();
        while (tabelaResultado.next()) {
            PlanoModalidadeVezesSemanaVO obj = montarDados(tabelaResultado, nivelMontarDados);
            vetResultado.add(obj);
        }
        return vetResultado;
    }

    /**
     * Responsável por montar os dados resultantes de uma consulta ao banco de
     * dados (
     * <code>ResultSet</code>) em um objeto da classe
     * <code>PlanoModalidadeVezesSemanaVO</code>.
     *
     * @return O objeto da classe <code>PlanoModalidadeVezesSemanaVO</code> com
     * os dados devidamente montados.
     */
    public static PlanoModalidadeVezesSemanaVO montarDados(ResultSet dadosSQL, int nivelMontarDados) throws Exception {
        PlanoModalidadeVezesSemanaVO obj = new PlanoModalidadeVezesSemanaVO();
        obj.setCodigo(dadosSQL.getInt("codigo"));
        obj.setNrVezes(dadosSQL.getInt("nrVezes"));
        obj.setPlanoModalidade(dadosSQL.getInt("planoModalidade"));
        obj.setTipoValor(dadosSQL.getString("tipoValor"));
        obj.setTipoOperacao(dadosSQL.getString("tipoOperacao"));
        obj.setPercentualDesconto(dadosSQL.getDouble("percentualDesconto"));
        obj.setValorEspecifico(dadosSQL.getDouble("valorEspecifico"));
        obj.setReferencia(dadosSQL.getBoolean("referencia"));
        obj.setNovoObj(false);
        if (nivelMontarDados == Uteis.NIVELMONTARDADOS_DADOSBASICOS) {
            return obj;
        }
        return obj;
    }

    /**
     * Operação responsável por excluir todos os objetos da
     * <code>HorarioTurmaVO</code> no BD. Faz uso da operação
     * <code>excluir</code> disponível na classe
     * <code>HorarioTurma</code>.
     *
     * @param <code>turma</code> campo chave para exclusão dos objetos no BD.
     * @exception Exception Erro de conexão com o BD ou restrição de acesso a
     * esta operação.
     */
    public void excluirPlanoModalidadeVezesSemana(Integer planoModalidadePrm) throws Exception {
        excluir(getIdEntidade());
        String sql = "DELETE FROM PlanoModalidadeVezesSemana WHERE ( planoModalidade= ?)";
        PreparedStatement sqlExcluir = con.prepareStatement(sql);
        sqlExcluir.setInt(1, planoModalidadePrm.intValue());
        sqlExcluir.execute();
    }

    /**
     * Operação responsável por alterar todos os objetos da
     * <code>HorarioTurmaVO</code> contidos em um Hashtable no BD. Faz uso da
     * operação
     * <code>excluirHorarioTurmas</code> e
     * <code>incluirHorarioTurmas</code> disponíveis na classe
     * <code>HorarioTurma</code>.
     *
     * @param objetos List com os objetos a serem alterados ou incluídos no BD.
     * @exception Exception Erro de conexão com o BD ou restrição de acesso a
     * esta operação.
     */
    public void alterarPlanoModalidadeVezesSemana(Integer planoModalidade, List objetos) throws Exception {
        String str = "DELETE FROM PlanoModalidadeVezesSemana WHERE planoModalidade = " + planoModalidade.intValue();
        Iterator i = objetos.iterator();
        while (i.hasNext()) {
            PlanoModalidadeVezesSemanaVO objeto = (PlanoModalidadeVezesSemanaVO) i.next();
            str += " AND codigo <> " + objeto.getCodigo();
        }
        PreparedStatement sqlExcluir = con.prepareStatement(str);
        sqlExcluir.execute();
        Iterator e = objetos.iterator();
        while (e.hasNext()) {
            PlanoModalidadeVezesSemanaVO obj = (PlanoModalidadeVezesSemanaVO) e.next();
            if (obj.getTipoOperacao().equals("EX") && objetos.size() == 1) {
                obj.setReferencia(true);
            }
            if (obj.getCodigo().equals(0)) {
                obj.setPlanoModalidade(planoModalidade);
                incluir(obj);
            } else {
                alterar(obj);
            }
        }
//        excluirPlanoModalidadeVezesSemana(planoModalidade);
//        incluirPlanoModalidadeVezesSemana(planoModalidade, objetos);
    }

    /**
     * Operação responsável por incluir objetos da
     * <code>HorarioTurmaVO</code> no BD. Garantindo o relacionamento com a
     * entidade principal
     * <code>plano.Turma</code> através do atributo de vínculo.
     *
     * @param objetos List contendo os objetos a serem gravados no BD da classe.
     * @exception Exception Erro de conexão com o BD ou restrição de acesso a
     * esta operação.
     */
    public void incluirPlanoModalidadeVezesSemana(Integer planoModalidadePrm, List objetos) throws Exception {
        Iterator e = objetos.iterator();
        while (e.hasNext()) {
            PlanoModalidadeVezesSemanaVO obj = (PlanoModalidadeVezesSemanaVO) e.next();
            obj.setPlanoModalidade(planoModalidadePrm);
            incluir(obj);
        }
    }

    /**
     * Operação responsável por consultar todos os
     * <code>PlanoDuracaoVO</code> relacionados a um objeto da classe
     * <code>plano.Plano</code>.
     *
     * @param plano Atributo de <code>plano.Plano</code> a ser utilizado para
     * localizar os objetos da classe <code>PlanoDuracaoVO</code>.
     * @return List Contendo todos os objetos da      * classe <code>PlanoDuracaoVO</code> resultantes da consulta.
     * @exception Exception Erro de conexão com o BD ou restrição de acesso a
     * esta operação.
     */
    public List consultarPlanoVezesSemanaVOs(Integer planoModalidade, int nivelMontarDados) throws Exception {
        consultar(getIdEntidade());
        List objetos = new ArrayList();
        String sql = "SELECT * FROM PlanoModalidadeVezesSemana WHERE planoModalidade = ? order by nrVezes";
        PreparedStatement sqlConsulta = con.prepareStatement(sql);
        sqlConsulta.setInt(1, planoModalidade.intValue());
        ResultSet resultado = sqlConsulta.executeQuery();
        while (resultado.next()) {
            PlanoModalidadeVezesSemanaVO novoObj = new PlanoModalidadeVezesSemanaVO();
            novoObj = PlanoModalidadeVezesSemana.montarDados(resultado, nivelMontarDados);
            objetos.add(novoObj);
        }
        return objetos;
    }

    /**
     * Operação responsável por localizar um objeto da classe
     * <code>PlanoModalidadeVezesSemanaVO</code> através de sua chave primária.
     *
     * @exception Exception Caso haja problemas de conexão ou localização do
     * objeto procurado.
     */
    public PlanoModalidadeVezesSemanaVO consultarPorChavePrimaria(Integer codigoPrm, int nivelMontarDados) throws Exception {
        consultar(getIdEntidade(), false);
        String sql = "SELECT * FROM PlanoModalidadeVezesSemana WHERE codigo = ?";
        PreparedStatement sqlConsultar = con.prepareStatement(sql);
        sqlConsultar.setInt(1, codigoPrm.intValue());
        ResultSet tabelaResultado = sqlConsultar.executeQuery();
        if (!tabelaResultado.next()) {
            throw new ConsistirException("Dados Não Encontrados ( PlanoModalidadeVezesSemana ).");
        }
        return (montarDados(tabelaResultado, nivelMontarDados));
    }

    /**
     * Método usado para saber se a chave primária passada como parâmetro existe
     *
     * @param codigo
     * @return
     * @throws Exception
     */
    public boolean consultarPorChavePrimaria(Integer codigo) throws Exception {
        consultar(getIdEntidade(), false);
        String sql = "SELECT * FROM PlanoModalidadeVezesSemana WHERE codigo = ?";
        PreparedStatement sqlConsultar = con.prepareStatement(sql);
        sqlConsultar.setInt(1, codigo.intValue());
        ResultSet tabelaResultado = sqlConsultar.executeQuery();
        if (!tabelaResultado.next()) {
            return false;
        }
        return true;
    }

    public PlanoModalidadeVezesSemanaVO consultarPlanoModalidadePorCodigoModalidade(int modalidade) throws Exception {
        consultar(getIdEntidade(), false);
        String sql = "select * from planomodalidadevezessemana  where planomodalidade = (select codigo from planomodalidade where modalidade = ?)";
        PreparedStatement sqlConsultar = con.prepareStatement(sql);
        sqlConsultar.setInt(1, modalidade);
        ResultSet tabelaResultado = sqlConsultar.executeQuery();
        if (!tabelaResultado.next()) {
            throw new ConsistirException("Dados Não Encontrados ( PlanoModalidadeVezesSemana ).");
        }
        return (montarDados(tabelaResultado, Uteis.NIVELMONTARDADOS_DADOSBASICOS));
    }

    public PlanoModalidadeVezesSemanaVO consultarPlanoModalidadePorCodigoModalidadeECodigoPlano(int modalidade, int plano) throws Exception {
        consultar(getIdEntidade(), false);
        String sql = "select * from planomodalidadevezessemana  where planomodalidade = (select codigo from planomodalidade where modalidade = ? and plano = ?)";
        PreparedStatement sqlConsultar = con.prepareStatement(sql);
        sqlConsultar.setInt(1, modalidade);
        sqlConsultar.setInt(2, plano);
        ResultSet tabelaResultado = sqlConsultar.executeQuery();
        if (!tabelaResultado.next()) {
            throw new ConsistirException("Dados Não Encontrados ( PlanoModalidadeVezesSemana ).");
        }
        return (montarDados(tabelaResultado, Uteis.NIVELMONTARDADOS_TODOS));
    }

    @Override
    public List<PlanoModalidadeVezesSemanaVO> consultarPorCodigoPlanoModalidade(int planomodalidade) throws Exception {
        List<PlanoModalidadeVezesSemanaVO> objetos = new ArrayList<PlanoModalidadeVezesSemanaVO>();
        PreparedStatement sqlConsultar = con.prepareStatement("select * from planomodalidadevezessemana "
                + "where planomodalidade = ?");
        sqlConsultar.setInt(1, planomodalidade);
        ResultSet resultado = sqlConsultar.executeQuery();
        while (resultado.next()) {
            objetos.add(montarDados(resultado, Uteis.NIVELMONTARDADOS_DADOSBASICOS));
        }
        return objetos;
    }
}

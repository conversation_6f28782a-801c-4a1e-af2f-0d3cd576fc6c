package negocio.facade.jdbc.plano;

import negocio.comuns.contrato.AditivoVO;
import negocio.facade.jdbc.arquitetura.SuperEntidade;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;

public class Aditivo extends SuperEntidade {

    public Aditivo(Connection conexao) throws Exception {
        super(conexao);
    }

    public Aditivo() throws Exception {
        super();
    }

    public AditivoVO buscarPorCodigo(Integer codigo) throws SQLException {
        String sql = "SELECT * FROM Aditivo WHERE codigo = ?";

        try (PreparedStatement preparedStatement = con.prepareStatement(sql)) {
            preparedStatement.setInt(1, codigo);

            try (ResultSet resultSet = preparedStatement.executeQuery()) {
                if (resultSet.next()) {
                    return montarDados(resultSet);
                }
            }
        }
        return null;
    }

    public AditivoVO montarDados(ResultSet resultSet) throws SQLException {
        final AditivoVO aditivoVO = new AditivoVO();
        aditivoVO.setCodigo(resultSet.getInt("codigo"));
        aditivoVO.setNome(resultSet.getString("nome"));
        aditivoVO.setDescricao(resultSet.getString("descricao"));

        return aditivoVO;
    }

    public void excluirAssinaturaPorContrato(Integer contrato) throws Exception {
        String sql = "DELETE FROM contratoassinaturadigital WHERE ((contratoaditivo = ?))";
        try (PreparedStatement sqlExcluir = con.prepareStatement(sql)) {
            sqlExcluir.setInt(1, contrato);
            sqlExcluir.execute();
        }
    }
}

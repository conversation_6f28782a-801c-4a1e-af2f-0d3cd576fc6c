package negocio.facade.jdbc.plano;

import br.com.pactosolucoes.comuns.util.Formatador;
import br.com.pactosolucoes.comuns.util.JSFUtilities;
import br.com.pactosolucoes.enumeradores.TipoTurmaEnum;
import negocio.comuns.plano.ModalidadeVO;
import negocio.comuns.utilitarias.ConsistirException;
import negocio.comuns.utilitarias.Ordenacao;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisValidacao;
import negocio.facade.jdbc.arquitetura.SuperEntidade;
import negocio.facade.jdbc.arquitetura.SuperFacadeJDBC;
import negocio.interfaces.plano.ModalidadeInterfaceFacade;
import org.json.JSONArray;
import org.json.JSONObject;
import relatorio.negocio.jdbc.sad.SituacaoClienteSinteticoDW;
import servicos.integracao.TreinoWSConsumer;
import servicos.operacoes.midias.MidiaService;
import servicos.operacoes.midias.commons.MidiaEntidadeEnum;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.sql.Statement;
import java.sql.Types;
import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * Classe de persistência que encapsula todas as operações de manipulação dos dados da classe <code>ModalidadeVO</code>.
 * Responsável por implementar operações como incluir, alterar, excluir e consultar pertinentes a classe <code>ModalidadeVO</code>.
 * Encapsula toda a interação com o banco de dados.
 * @see ModalidadeVO
 * @see SuperEntidade
 */
public class Modalidade extends SuperEntidade implements ModalidadeInterfaceFacade {

    public Modalidade() throws Exception {
        super();
    }

    public Modalidade(Connection con) throws Exception {
        super(con);
    }

    /**
     * Operação responsável por retornar um novo objeto da classe <code>ModalidadeVO</code>.
     */
    public ModalidadeVO novo() throws Exception {
        incluir(getIdEntidade());
        return new ModalidadeVO();
    }

    /**
     * Operação responsável por incluir no banco de dados um objeto da classe <code>ModalidadeVO</code>.
     * Primeiramente valida os dados (<code>validarDados</code>) do objeto. Verifica a conexão com o banco de dados e a permissão do usuário
     * para realizar esta operacão na entidade.
     * Isto, através da operação <code>incluir</code> da superclasse.
     * @param obj  Objeto da classe <code>ModalidadeVO</code> que será gravado no banco de dados.
     * @exception Exception Caso haja problemas de conexão, restrição de acesso ou validação de dados.
     */
    public void incluir(ModalidadeVO obj) throws Exception {
        try {
            con.setAutoCommit(false);
            ModalidadeVO.validarDados(obj);
            //   Modalidade.incluir(getIdEntidade());
            obj.realizarUpperCaseDados();
            String sql = "INSERT INTO Modalidade( nome, ativo, valorMensal, utilizarTurma, utilizarProduto, "
                    + "modalidadeDefault,nrVezes, usatreino, crossfit, fotokey, tipo, frequenciaspossiveis, vagasdisponiveis) VALUES ( ?, ?,?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)";
            try (PreparedStatement sqlInserir = con.prepareStatement(sql)) {
                sqlInserir.setString(1, obj.getNome());
                sqlInserir.setBoolean(2, obj.isAtivo());
                sqlInserir.setDouble(3, obj.getValorMensal());
                sqlInserir.setBoolean(4, obj.isUtilizarTurma());
                sqlInserir.setBoolean(5, obj.getUtilizarProduto());
                sqlInserir.setBoolean(6, obj.getModalidadeDefault());
                sqlInserir.setInt(7, obj.getNrVezes());
                sqlInserir.setBoolean(8, obj.getUsaTreino());
                sqlInserir.setBoolean(9, obj.getCrossfit());
                sqlInserir.setString(10, obj.getFotokey());
                if (obj.getTipo() != null) {
                    sqlInserir.setInt(11, obj.getTipo());
                } else {
                    sqlInserir.setNull(11, Types.INTEGER);
                }
                sqlInserir.setInt(12, obj.getFrequenciasPossiveis());
                sqlInserir.setInt(13, obj.getVagasDisponiveis());
                sqlInserir.execute();
            }
            obj.setCodigo(obterValorChavePrimariaCodigo());
            getFacade().getProdutoSugerido().incluirProdutoSugeridos(obj.getCodigo(), obj.getProdutoSugeridoVOs());
            getFacade().getModalidadeEmpresa().incluirModalidadeEmpresas(obj.getCodigo(), obj.getModalidadeEmpresaVOs());

            obj.setNovoObj(false);
            con.commit();
        } catch (Exception e) {
            obj.setNovoObj(true);
            con.rollback();
            con.setAutoCommit(true);
            throw e;
        } finally {
            con.setAutoCommit(true);
        }
    }

    public void incluirSemCommit(ModalidadeVO obj) throws Exception {
        try {
            ModalidadeVO.validarDados(obj);
            //   Modalidade.incluir(getIdEntidade());
            obj.realizarUpperCaseDados();
            String sql = "INSERT INTO Modalidade( nome, ativo, valorMensal, utilizarTurma, utilizarProduto, "
                    + "modalidadeDefault,nrVezes, usatreino, crossfit, tipo, frequenciaspossiveis, vagasdisponiveis) VALUES ( ?, ?,?, ?, ?, ?, ?, ?, ?, ?, ?, ?)";
            try (PreparedStatement sqlInserir = con.prepareStatement(sql)) {
                sqlInserir.setString(1, obj.getNome());
                sqlInserir.setBoolean(2, obj.isAtivo());
                sqlInserir.setDouble(3, obj.getValorMensal());
                sqlInserir.setBoolean(4, obj.isUtilizarTurma());
                sqlInserir.setBoolean(5, obj.getUtilizarProduto());
                sqlInserir.setBoolean(6, obj.getModalidadeDefault());
                sqlInserir.setInt(7, obj.getNrVezes());
                sqlInserir.setBoolean(8, obj.getUsaTreino());
                sqlInserir.setBoolean(9, obj.getCrossfit());
                if (obj.getTipo() != null) {
                    sqlInserir.setInt(10, obj.getTipo());
                } else {
                    sqlInserir.setNull(10, Types.INTEGER);
                }
                sqlInserir.setInt(11, obj.getFrequenciasPossiveis());
                sqlInserir.setInt(12, obj.getVagasDisponiveis());
                sqlInserir.execute();
            }
            obj.setCodigo(obterValorChavePrimariaCodigo());
            getFacade().getProdutoSugerido().incluirProdutoSugeridos(obj.getCodigo(), obj.getProdutoSugeridoVOs());
            getFacade().getModalidadeEmpresa().incluirModalidadeEmpresas(obj.getCodigo(), obj.getModalidadeEmpresaVOs());

            obj.setNovoObj(false);
        } catch (Exception e) {
            obj.setNovoObj(true);
            throw e;
        }
    }

    /**
     * Operação responsável por alterar no BD os dados de um objeto da classe <code>ModalidadeVO</code>.
     * Sempre utiliza a chave primária da classe como atributo para localização do registro a ser alterado.
     * Primeiramente valida os dados (<code>validarDados</code>) do objeto. Verifica a conexão com o banco de dados e a permissão do usuário
     * para realizar esta operacão na entidade.
     * Isto, através da operação <code>alterar</code> da superclasse.
     * @param obj    Objeto da classe <code>ModalidadeVO</code> que será alterada no banco de dados.
     * @exception Exception Caso haja problemas de conexão, restrição de acesso ou validação de dados.
     */
    public void alterar(ModalidadeVO obj) throws Exception {
        try {
            con.setAutoCommit(false);
            ModalidadeVO.validarDados(obj);
            alterar(getIdEntidade());
            obj.realizarUpperCaseDados();
            String sql = "UPDATE Modalidade set nome=?, ativo=?, valorMensal=?, utilizarTurma=?, utilizarProduto=? , "
                    + "modalidadeDefault=? , nrVezes=?, usatreino = ?, crossfit = ?, fotokey = ?, tipo = ?, " +
                    "frequenciaspossiveis = ?, vagasdisponiveis = ? WHERE codigo = ?";
            try (PreparedStatement sqlAlterar = con.prepareStatement(sql)) {
                sqlAlterar.setString(1, obj.getNome());
                sqlAlterar.setBoolean(2, obj.isAtivo());
                sqlAlterar.setDouble(3, obj.getValorMensal());
                sqlAlterar.setBoolean(4, obj.isUtilizarTurma());
                sqlAlterar.setBoolean(5, obj.getUtilizarProduto());
                sqlAlterar.setBoolean(6, obj.getModalidadeDefault());
                sqlAlterar.setInt(7, obj.getNrVezes());
                sqlAlterar.setBoolean(8, obj.getUsaTreino());
                sqlAlterar.setBoolean(9, obj.getCrossfit());
                sqlAlterar.setString(10, obj.getFotokey());
                if (obj.getTipo() != null) {
                    sqlAlterar.setInt(11, obj.getTipo());
                } else {
                    sqlAlterar.setNull(11, Types.INTEGER);
                }
                sqlAlterar.setInt(12, obj.getFrequenciasPossiveis());
                sqlAlterar.setInt(13, obj.getVagasDisponiveis());
                sqlAlterar.setInt(14, obj.getCodigo());
                sqlAlterar.execute();
            }

            ProdutoSugerido produtoSugeridoDAO = new ProdutoSugerido(con);
            ModalidadeEmpresa modalidadeEmpresaDAO = new ModalidadeEmpresa(con);

            produtoSugeridoDAO.alterarProdutoSugeridos(obj.getCodigo(), obj.getProdutoSugeridoVOs());
            modalidadeEmpresaDAO.alterarModalidadeEmpresas(obj.getCodigo(), obj.getModalidadeEmpresaVOs());

            produtoSugeridoDAO = null;
            modalidadeEmpresaDAO = null;

            con.commit();

        } catch (Exception e) {
            con.rollback();
            con.setAutoCommit(true);
            throw e;
        } finally {
            con.setAutoCommit(true);
        }
    }

    /**
     * Operação responsável por excluir no BD um objeto da classe <code>ModalidadeVO</code>.
     * Sempre localiza o registro a ser excluído através da chave primária da entidade.
     * Primeiramente verifica a conexão com o banco de dados e a permissão do usuário
     * para realizar esta operacão na entidade.
     * Isto, através da operação <code>excluir</code> da superclasse.
     * @param obj    Objeto da classe <code>ModalidadeVO</code> que será removido no banco de dados.
     * @exception Exception Caso haja problemas de conexão ou restrição de acesso.
     */
    public void excluir(ModalidadeVO obj) throws Exception {
        try {
            con.setAutoCommit(false);
            excluir(getIdEntidade());
            String sql = "DELETE FROM Modalidade WHERE ((codigo = ?))";
            try (PreparedStatement sqlExcluir = con.prepareStatement(sql)) {
                sqlExcluir.setInt(1, obj.getCodigo());
                sqlExcluir.execute();
            }
            getFacade().getProdutoSugerido().excluirProdutoSugeridos(obj.getCodigo());
            getFacade().getModalidadeEmpresa().excluirModalidadeEmpresas(obj.getCodigo());

            con.commit();
        } catch (Exception e) {
            con.rollback();
            con.setAutoCommit(true);
            throw e;
        } finally {
            con.setAutoCommit(true);
        }
    }

    /**
     * Responsável por realizar uma consulta de <code>Modalidade</code> através do valor do atributo
     * <code>String nome</code>. Retorna os objetos, com início do valor do atributo idêntico ao parâmetro fornecido.
     * Faz uso da operação <code>montarDadosConsulta</code> que realiza o trabalho de prerarar o List resultante.
     * @param   controlarAcesso Indica se a aplicação deverá verificar se o usuário possui permissão para esta consulta ou não.
     * @return  List Contendo vários objetos da classe <code>ModalidadeVO</code> resultantes da consulta.
     * @exception Exception Caso haja problemas de conexão ou restrição de acesso.
     */
    public List<ModalidadeVO> consultarPorNome(String valorConsulta, Integer empresa, boolean controlarAcesso, int nivelMontarDados) throws Exception {
        consultar(getIdEntidade(), controlarAcesso);
        String sqlStr = "";
        if (empresa == 0) {
            sqlStr += "SELECT Modalidade.* FROM Modalidade WHERE upper( Modalidade.nome ) like('" + valorConsulta.toUpperCase() + "%') ORDER BY Modalidade.nome";
        } else {
            sqlStr += "SELECT Modalidade.* FROM Modalidade , ModalidadeEmpresa WHERE upper( Modalidade.nome ) like('" + valorConsulta.toUpperCase() + "%')  and modalidadeempresa.modalidade = modalidade.codigo "
                    + "and modalidadeempresa.empresa =" + empresa + "   ORDER BY Modalidade.nome";

        }
        try (Statement stm = con.createStatement()) {
            try (ResultSet tabelaResultado = stm.executeQuery(sqlStr)) {
                return (montarDadosConsulta(tabelaResultado, nivelMontarDados, this.con));
            }
        }
    }
    public List<ModalidadeVO> consultarPorNomeModalidadeSemDesativado(String valorConsulta, Integer empresa, boolean controlarAcesso, int nivelMontarDados) throws Exception {
        consultar(getIdEntidade(), controlarAcesso);
        String sqlStr = "";
        if (empresa == 0) {
            sqlStr += "SELECT Modalidade.* FROM Modalidade WHERE upper( Modalidade.nome ) like('%" + valorConsulta.toUpperCase() + "%') and ativo = true ORDER BY Modalidade.nome";
        } else {
            sqlStr += "SELECT Modalidade.* FROM Modalidade , ModalidadeEmpresa WHERE upper( Modalidade.nome ) like('%" + valorConsulta.toUpperCase() + "%')  and modalidadeempresa.modalidade = modalidade.codigo "
                    + "and modalidadeempresa.empresa =" + empresa + " and ativo = true  ORDER BY Modalidade.nome";

        }
        try (Statement stm = con.createStatement()) {
            try (ResultSet tabelaResultado = stm.executeQuery(sqlStr)) {
                return (montarDadosConsulta(tabelaResultado, nivelMontarDados, this.con));
            }
        }
    }
    public ModalidadeVO consultarPorNomeModalidade(String valorConsulta, boolean controlarAcesso, int nivelMontarDados) throws Exception {
        consultar(getIdEntidade(), controlarAcesso);
        String sqlStr = "SELECT Modalidade.* FROM Modalidade WHERE upper(Modalidade.nome) like('" + valorConsulta.toUpperCase() + "%') ORDER BY modalidade.nome ";
        try (Statement stm = con.createStatement()) {
            try (ResultSet tabelaResultado = stm.executeQuery(sqlStr)) {
                if (!tabelaResultado.next()) {
                    return new ModalidadeVO();
                }
                return (montarDados(tabelaResultado, nivelMontarDados, this.con));
            }
        }
    }

    public ModalidadeVO consultarPorNomeModalidadeAtivaComValor(String valorConsulta, boolean controlarAcesso, int nivelMontarDados) throws Exception {
        consultar(getIdEntidade(), controlarAcesso);
        String sqlStr = "SELECT Modalidade.* FROM Modalidade WHERE upper(Modalidade.nome) like('" + valorConsulta.toUpperCase() + "%') and ativo  and valormensal > 0.0 ORDER BY modalidade.nome ";
        try (Statement stm = con.createStatement()) {
            try (ResultSet tabelaResultado = stm.executeQuery(sqlStr)) {
                if (!tabelaResultado.next()) {
                    return new ModalidadeVO();
                }
                return (montarDados(tabelaResultado, nivelMontarDados, this.con));
            }
        }
    }

    public List consultarPorNomeModalidadeComLimite(String valorConsulta, boolean controlarAcesso, int nivelMontarDados) throws Exception {
        consultar(getIdEntidade(), controlarAcesso);
        String sqlStr = "SELECT Modalidade.* FROM Modalidade WHERE upper(Modalidade.nome) like('" + valorConsulta.toUpperCase() + "%') ORDER BY modalidade.nome limit 20";
        try (Statement stm = con.createStatement()) {
            try (ResultSet tabelaResultado = stm.executeQuery(sqlStr)) {
                return (montarDadosConsulta(tabelaResultado, nivelMontarDados, this.con));
            }
        }
    }


    public List consultarPorNomeModalidadeProfessorComLimite(String valorConsulta,int professor, boolean controlarAcesso, int nivelMontarDados) throws Exception {
        consultar(getIdEntidade(), controlarAcesso);
        StringBuilder sqlStr = new StringBuilder();
        sqlStr.append("SELECT distinct (modalidade.codigo),modalidade.nome from turma ");
        sqlStr.append("inner join modalidade on modalidade.codigo = turma.modalidade ");
        sqlStr.append("inner join horarioturma on horarioturma.turma= turma.codigo ");
        sqlStr.append("where horarioturma.professor =  ").append(professor);
        sqlStr.append(" and modalidade.nome ilike('").append(valorConsulta.toUpperCase()).append("%') ORDER BY modalidade.nome limit 30");
        return montarDadosSimplificados(sqlStr);
    }

    public List consultarPorNomeUtilizaTurma(String valorConsulta, Integer empresa, boolean controlarAcesso, boolean turma, Boolean ativo, int nivelMontarDados) throws Exception {
        consultar(getIdEntidade(), controlarAcesso);
        StringBuilder sql = new StringBuilder("SELECT \n");
        sql.append("  Modalidade.*\n");
        sql.append("FROM Modalidade, ModalidadeEmpresa\n");
        sql.append("WHERE modalidadeempresa.modalidade = modalidade.codigo\n");
        sql.append("      AND upper(modalidade.nome) LIKE ('").append(valorConsulta.toUpperCase()).append("%')\n");
        sql.append("      AND Modalidade.utilizarturma = ").append(turma).append("\n");
        sql.append("      AND modalidadeempresa.empresa = ").append(empresa).append("\n");
        if (ativo != null) {
            sql.append("      AND Modalidade.ativo = ").append((ativo)).append("\n");
        }
        sql.append("ORDER BY modalidade.nome;\n");

        try (Statement stm = con.createStatement()) {
            try (ResultSet tabelaResultado = stm.executeQuery(sql.toString())) {
                return (montarDadosConsulta(tabelaResultado, nivelMontarDados, this.con));
            }
        }
    }

    public List consultarPorNomeUtilizaTurmaV2(String valorConsulta, Integer empresa, boolean controlarAcesso, boolean turma, Boolean ativo, int nivelMontarDados) throws Exception {
        StringBuilder sql = new StringBuilder("SELECT *\n");
        sql.append("FROM Modalidade m\n");
        if (!UteisValidacao.emptyNumber(empresa)) {
            sql.append("INNER JOIN ModalidadeEmpresa me on me.modalidade = m.codigo\n");
        }
        sql.append("WHERE upper(m.nome) LIKE ('").append(valorConsulta.toUpperCase()).append("%')\n");
        sql.append("      AND m.utilizarturma = ").append(turma).append("\n");
        if (!UteisValidacao.emptyNumber(empresa)) {
            sql.append("      AND me.empresa = ").append(empresa).append("\n");
        }
        if (ativo != null) {
            sql.append("      AND m.ativo = ").append((ativo)).append("\n");
        }
        sql.append("ORDER BY m.nome;\n");

        try (Statement stm = con.createStatement()) {
            try (ResultSet tabelaResultado = stm.executeQuery(sql.toString())) {
                return (montarDadosConsulta(tabelaResultado, nivelMontarDados, this.con));
            }
        }
    }

    public List consultarPorModalidadeDefault(Boolean valorConsulta, Integer empresa, boolean controlarAcesso, int nivelMontarDados) throws Exception {
        consultar(getIdEntidade(), controlarAcesso);
        String sqlStr = "";
        if (empresa == 0) {
            sqlStr += "SELECT Modalidade.* FROM Modalidade WHERE Modalidade.modalidadeDefault = " + valorConsulta + " ORDER BY ModalidadeDefault";
        } else {
            sqlStr += "SELECT Modalidade.* FROM Modalidade, ModalidadeEmpresa WHERE Modalidade.modalidadeDefault = " + valorConsulta + " and modalidadeempresa.modalidade = modalidade.codigo "
                    + "and modalidadeempresa.empresa =" + empresa + " ORDER BY ModalidadeDefault";
        }

        try (Statement stm = con.createStatement()) {
            try (ResultSet tabelaResultado = stm.executeQuery(sqlStr)) {
                return (montarDadosConsulta(tabelaResultado, nivelMontarDados, this.con));
            }
        }
    }

    public List consultarPorUtilizaTurma(Boolean valorConsulta, boolean controlarAcesso, int nivelMontarDados) throws Exception {
        consultar(getIdEntidade(), controlarAcesso);
        String sqlStr = "SELECT Modalidade.* FROM Modalidade WHERE Modalidade.utilizarturma = " + valorConsulta + " ORDER BY utilizarturma";
        try (Statement stm = con.createStatement()) {
            try (ResultSet tabelaResultado = stm.executeQuery(sqlStr)) {
                return (montarDadosConsulta(tabelaResultado, nivelMontarDados, this.con));
            }
        }
    }

    /**
     * Responsável por realizar uma consulta de <code>Modalidade</code> através do valor do atributo
     * <code>Integer codigo</code>. Retorna os objetos com valores iguais ou superiores ao parâmetro fornecido.
     * Faz uso da operação <code>montarDadosConsulta</code> que realiza o trabalho de prerarar o List resultante.
     * @param   controlarAcesso Indica se a aplicação deverá verificar se o usuário possui permissão para esta consulta ou não.
     * @return  List Contendo vários objetos da classe <code>ModalidadeVO</code> resultantes da consulta.
     * @exception Exception Caso haja problemas de conexão ou restrição de acesso.
     */
    public List consultarPorCodigo(Integer valorConsulta, Integer empresa, boolean controlarAcesso, int nivelMontarDados) throws Exception {
        consultar(getIdEntidade(), controlarAcesso);
        String sqlStr = "";
        if (empresa == 0) {
            sqlStr += "SELECT Modalidade.* FROM Modalidade WHERE Modalidade.codigo >= " + valorConsulta + " ORDER BY Modalidade.codigo";
        } else {
            sqlStr += "SELECT Modalidade.* FROM Modalidade, ModalidadeEmpresa WHERE Modalidade.codigo >= " + valorConsulta + " and modalidadeempresa.modalidade = modalidade.codigo "
                    + "and modalidadeempresa.empresa =" + empresa + "  ORDER BY Modalidade.codigo";
        }
        try (Statement stm = con.createStatement()) {
            try (ResultSet tabelaResultado = stm.executeQuery(sqlStr)) {
                return (montarDadosConsulta(tabelaResultado, nivelMontarDados, this.con));
            }
        }
    }

    public List consultarPorCodigoAtiva(Integer valorConsulta, Integer empresa, boolean controlarAcesso, int nivelMontarDados) throws Exception {
        consultar(getIdEntidade(), controlarAcesso);
        String sqlStr = "";
        if (empresa == 0) {
            sqlStr += "SELECT Modalidade.* FROM Modalidade WHERE Modalidade.codigo >= " + valorConsulta + " and  modalidade.ativo='t' ORDER BY Modalidade.codigo";
        } else {
            sqlStr += "SELECT Modalidade.* FROM Modalidade, ModalidadeEmpresa WHERE Modalidade.codigo >= " + valorConsulta + " and modalidadeempresa.modalidade = modalidade.codigo "
                    + "and modalidadeempresa.empresa =" + empresa + " and  modalidade.ativo='t' ORDER BY Modalidade.codigo";
        }
        try (Statement stm = con.createStatement()) {
            try (ResultSet tabelaResultado = stm.executeQuery(sqlStr)) {
                return (montarDadosConsulta(tabelaResultado, nivelMontarDados, this.con));
            }
        }
    }

    public List consultarPorCodigoUtilizaTurma(Integer valorConsulta, Integer empresa, boolean controlarAcesso, boolean turma, int nivelMontarDados) throws Exception {
        consultar(getIdEntidade(), controlarAcesso);
        String sqlStr = "SELECT Modalidade.* FROM Modalidade, ModalidadeEmpresa WHERE modalidade.codigo >= " + valorConsulta + "  and  Modalidade.utilizarturma = " + turma + " "
                + "and modalidadeempresa.modalidade = modalidade.codigo and modalidadeempresa.empresa =" + empresa + "  ORDER BY modalidade.codigo";
        try (Statement stm = con.createStatement()) {
            try (ResultSet tabelaResultado = stm.executeQuery(sqlStr)) {
                return (montarDadosConsulta(tabelaResultado, nivelMontarDados, this.con));
            }
        }
    }

    /**
     * Responsável por montar os dados de vários objetos, resultantes de uma consulta ao banco de dados (<code>ResultSet</code>).
     * Faz uso da operação <code>montarDados</code> que realiza o trabalho para um objeto por vez.
     * @return  List Contendo vários objetos da classe <code>ModalidadeVO</code> resultantes da consulta.
     */
    public static List<ModalidadeVO> montarDadosConsulta(ResultSet tabelaResultado, int nivelMontarDados, Connection con) throws Exception {
        List<ModalidadeVO> vetResultado = new ArrayList<>();
        while (tabelaResultado.next()) {
            ModalidadeVO obj = montarDados(tabelaResultado, nivelMontarDados, con);
            vetResultado.add(obj);
        }
        return vetResultado;
    }

    private static ModalidadeVO montarDadosBasicoGestao(ResultSet dadosSQL, int nivelMontarDados) throws Exception {
        ModalidadeVO obj = new ModalidadeVO();
        obj.setCodigo(dadosSQL.getInt("codigo"));
        obj.setNome(dadosSQL.getString("nome"));
        obj.setNovoObj(false);
        return obj;
    }

    /**
     * Responsável por montar os dados resultantes de uma consulta ao banco de dados (<code>ResultSet</code>)
     * em um objeto da classe <code>ModalidadeVO</code>.
     * @return  O objeto da classe <code>ModalidadeVO</code> com os dados devidamente montados.
     */
    public static ModalidadeVO montarDados(ResultSet dadosSQL, int nivelMontarDados, Connection con) throws Exception {
        ModalidadeVO obj = new ModalidadeVO();
        if (nivelMontarDados == Uteis.NIVELMONTARDADOS_TELACONSULTA) {
            ModalidadeEmpresa modalidadeEmpresa = new ModalidadeEmpresa(con);
            obj.setCodigo(dadosSQL.getInt("codigo"));
            obj.setNome(dadosSQL.getString("nome"));
            obj.setValorMensal(dadosSQL.getDouble("valorMensal"));
            obj.setModalidadeEmpresaVOs(modalidadeEmpresa.consultarModalidadeEmpresas(obj.getCodigo(), nivelMontarDados));
            modalidadeEmpresa = null;
            return obj;
        }
        if (nivelMontarDados == Uteis.NIVELMONTARDADOS_MINIMOS) {
            obj.setCodigo(dadosSQL.getInt("codigo"));
            obj.setNome(dadosSQL.getString("nome"));
            return obj;
        }

        obj.setCodigo(dadosSQL.getInt("codigo"));
        obj.setNrVezes(dadosSQL.getInt("nrVezes"));
        obj.setNome(dadosSQL.getString("nome"));
        obj.setAtivo(dadosSQL.getBoolean("ativo"));
        obj.setValorMensal(dadosSQL.getDouble("valorMensal"));
        obj.setUtilizarTurma(dadosSQL.getBoolean("utilizarTurma"));
        obj.setUtilizarProduto(dadosSQL.getBoolean("utilizarProduto"));
        obj.setModalidadeDefault(dadosSQL.getBoolean("modalidadeDefault"));
        obj.setFotokey(dadosSQL.getString("fotokey"));
        obj.setTipo(dadosSQL.getInt("tipo"));
        obj.setFrequenciasPossiveis(dadosSQL.getInt("frequenciaspossiveis"));
        obj.setVagasDisponiveis(dadosSQL.getInt("vagasdisponiveis"));
        try {
            obj.setUsaTreino(dadosSQL.getBoolean("usatreino"));
        } catch (Exception ignored) {
        }
        try {
            obj.setCrossfit(dadosSQL.getBoolean("crossfit"));
        } catch (Exception ignored) {
        }
        obj.setNovoObj(false);
        if (nivelMontarDados == Uteis.NIVELMONTARDADOS_DADOSBASICOS) {
            return obj;
        }
        ProdutoSugerido produtoSugerido = new ProdutoSugerido(con);
        ModalidadeEmpresa modalidadeEmpresa = new ModalidadeEmpresa(con);
        obj.setProdutoSugeridoVOs(produtoSugerido.consultarProdutoSugeridos(obj.getCodigo(), nivelMontarDados));
        obj.setModalidadeEmpresaVOs(modalidadeEmpresa.consultarModalidadeEmpresas(obj.getCodigo(), nivelMontarDados));
        modalidadeEmpresa = null;
        produtoSugerido = null;

        if (nivelMontarDados == Uteis.NIVELMONTARDADOS_DADOSENTIDADESUBORDINADAS) {
            return obj;
        }

        return obj;
    }

    /**
     * Operação responsável por localizar um objeto da classe <code>ModalidadeVO</code>
     * através de sua chave primária.
     * @exception Exception Caso haja problemas de conexão ou localização do objeto procurado.
     */
    public ModalidadeVO consultarPorChavePrimaria(Integer codigoPrm, int nivelMontarDados) throws Exception {
        consultar(getIdEntidade(), false);
        String sql = "SELECT * FROM Modalidade WHERE codigo = ?";
        try (PreparedStatement sqlConsultar = con.prepareStatement(sql)) {
            sqlConsultar.setInt(1, codigoPrm);
            try (ResultSet tabelaResultado = sqlConsultar.executeQuery()) {
                if (!tabelaResultado.next()) {
                    throw new ConsistirException("Dados Não Encontrados ( Modalidade ).");
                }
                return (montarDados(tabelaResultado, nivelMontarDados, this.con));
            }
        }
    }

    public ModalidadeVO consultarPorTurma(int turma, int nivelMontarDados) throws Exception {
        consultar(getIdEntidade(), false);
        String sql = "SELECT * FROM Modalidade "
                + "INNER JOIN turma ON modalidade.codigo = turma.modalidade AND turma.codigo = ?";
        try (PreparedStatement sqlConsultar = con.prepareStatement(sql)) {
            sqlConsultar.setInt(1, turma);
            try (ResultSet tabelaResultado = sqlConsultar.executeQuery()) {
                if (!tabelaResultado.next()) {
                    throw new ConsistirException("Dados Não Encontrados ( Modalidade ).");
                }
                return (montarDados(tabelaResultado, nivelMontarDados, this.con));
            }
        }
    }

    /**
     * Responsável por consultar todos os registros de modalidades de forma simplificada, montando num mapa.
     * <AUTHOR>
     * 11/08/2011
     */
    public Map<Integer, String> consultarModalidadesSimplificado() throws Exception{
        Map<Integer, String> mapResult = new HashMap<Integer, String>();
        String sql = "SELECT nome, codigo FROM modalidade";
        ResultSet consulta = Modalidade.criarConsulta(sql, con);
        while(consulta.next()){
            mapResult.put(consulta.getInt("codigo"), consulta.getString("nome"));
        }
        return mapResult;
    }

    /**
     * Consulta de suggestionBox que retorna o nome do colaborador que possui vinculo na carteira
     * e o codigo do colaborador para posterior consulta de clientes vinculados
     * pesquisa por empresa
     */
    public List<ModalidadeVO> consultarTodasModalidadesComLimite(Integer empresa, int limit) throws Exception {
        StringBuilder sql = new StringBuilder();
        if (empresa == 0) {
            sql.append("select codigo, nome from modalidade order by nome ");
        } else {
            sql.append("select mod.codigo, mod.nome from modalidade as mod ");
            sql.append("inner join modalidadeempresa as modEmp on modEmp.modalidade= mod.codigo ");
            sql.append("where modEmp.empresa=").append(empresa);
            sql.append(" ORDER BY mod.nome ");
        }
        if (limit > 0) {
            sql.append("\nlimit ").append(limit);
        }
        return montarDadosSimplificados(sql);
    }


    public List<ModalidadeVO> consultarTodasModalidades(Integer empresa, Boolean ativo, Boolean utilizaTurma) throws Exception {
        return consultarModalidades(empresa, ativo, 0, utilizaTurma);
    }

    public List<ModalidadeVO> consultarTodasModalidadesComLimite(Integer empresa, Boolean ativo, Boolean utilizaTurma) throws Exception {
        return consultarModalidades(empresa, ativo, 50, utilizaTurma);
    }

    private List<ModalidadeVO> consultarModalidades(Integer empresa, Boolean ativo, Integer limit, Boolean utilizaTurma) throws Exception {
        StringBuilder sql = new StringBuilder();
        sql.append(" select mod.codigo, mod.nome from modalidade mod \n");
        if (empresa > 0) {
            sql.append("inner join modalidadeempresa as modEmp on modEmp.modalidade = mod.codigo and modEmp.empresa =").append(empresa).append("\n");
        }
        sql.append("WHERE 1 = 1\n");
        if (ativo != null) {
            sql.append("and ativo = ").append(ativo).append("\n");
        }
        if (utilizaTurma != null) {
            sql.append("and utilizarturma = ").append(utilizaTurma).append("\n");
        }

        sql.append(" ORDER BY mod.nome \n");

        if (!UteisValidacao.emptyNumber(limit)) {
            sql.append(" limit ").append(limit);
        }

        return montarDadosSimplificados(sql);
    }

    private List<ModalidadeVO> montarDadosSimplificados(StringBuilder sql) throws SQLException {
        List<ModalidadeVO> listaModalidades;
        try (Statement stm = con.createStatement()) {
            try (ResultSet tabelaResultado = stm.executeQuery(sql.toString())) {
                listaModalidades = new ArrayList<>();
                while (tabelaResultado.next()) {
                    ModalidadeVO modalidadeVO = new ModalidadeVO();
                    modalidadeVO.setCodigo(tabelaResultado.getInt("codigo"));
                    modalidadeVO.setNome(tabelaResultado.getString("nome"));
                    listaModalidades.add(modalidadeVO);
                }
            }
        }
        return listaModalidades;
    }

    /**
     * Método usado em SuggestionBox na tela de mailing
     * pesquisando por nome das modalidades com limite de 50
     */
    public List consultarPorNomeModalidadeComLimite(Integer empresa, String valorConsulta) throws Exception {
        StringBuilder sql = new StringBuilder();
        if (empresa == 0) {
            sql.append("select codigo, nome from modalidade where nome ilike '").append(valorConsulta).append("%' order by nome limit 50 ");
        } else {
            sql.append("select mod.codigo, mod.nome from modalidade as mod ");
            sql.append("inner join modalidadeempresa as modEmp on modEmp.modalidade= mod.codigo ");
            sql.append("where modEmp.empresa=").append(empresa);
            sql.append(" and mod.nome ilike '").append(valorConsulta).append("%'");
            sql.append(" ORDER BY mod.nome limit 50");
        }
        return montarDadosSimplificados(sql);
    }

    public String consultarJSON(Integer empresa, String sEcho, Integer offset, Integer limit, String clausulaLike, Integer colOrdenar, String dirOrdenar, String situacao) throws Exception {
        String sqlCount = "SELECT count(codigo)+1 FROM modalidade mod";

        ResultSet rs = getRS(empresa, offset, limit, colOrdenar, dirOrdenar, situacao, false, clausulaLike);

        StringBuilder sqlContarFiltrados = new StringBuilder("SELECT count(mod.codigo)\n");
        sqlContarFiltrados.append("FROM modalidade mod   \n");
        sqlContarFiltrados.append(" left join modalidadeempresa me on me.modalidade = mod.codigo  \n");
        if (!UteisValidacao.emptyNumber(empresa)) {
            sqlContarFiltrados.append("where me.empresa = ").append(empresa);
        }
        if (empresa == 0) {
            if (situacao.equals("AT")) {
                sqlContarFiltrados.append(" WHERE mod.ativo = true \n");
            } else if (situacao.equals("NA")) {
                sqlContarFiltrados.append(" WHERE mod.ativo = false \n");
            }
        } else {
            if (situacao.equals("AT")) {
                sqlContarFiltrados.append(" AND mod.ativo = true \n");
            } else if (situacao.equals("NA")) {
                sqlContarFiltrados.append(" AND mod.ativo = false \n");
            }
        }

        if (!UteisValidacao.emptyString(clausulaLike)) {
            sqlContarFiltrados.append(" AND (");
            sqlContarFiltrados.append("lower(mod.codigo::VARCHAR) ~ '").append(clausulaLike).append("' OR\n");
            sqlContarFiltrados.append("lower(mod.nome) ~ '").append(clausulaLike).append("' \n");
            sqlContarFiltrados.append("OR lower((SELECT string_agg(empresa.nome, ', ') \n" +
                    "FROM modalidadeempresa \n" +
                    " INNER JOIN empresa ON empresa.codigo = modalidadeempresa.empresa \n" +
                    " WHERE modalidadeempresa.modalidade = mod.codigo)) ~ '").append(clausulaLike).append("')\n");

            if (clausulaLike.toLowerCase().equals("[aà-æ]t[iì-ï]v[oò-ö]") && situacao.equals("AT")) {
                sqlContarFiltrados.append(" or mod.ativo = true");
            } else if (clausulaLike.toLowerCase().equals("[iì-ï][nñ][aà-æ]t[iì-ï]v[oò-ö]") && situacao.equals("NA")) {
                sqlContarFiltrados.append(" or mod.ativo = false");
            }
        }

        JSONArray aaData = new JSONArray();
        while (rs.next()) {
            JSONArray itemAaData = new JSONArray();
            itemAaData.put(rs.getString("codigo"));
            itemAaData.put(rs.getString("nome"));
            itemAaData.put(rs.getBoolean("ativo") ? "Ativo" :"Inativo");
            itemAaData.put(rs.getString("empresa"));
            itemAaData.put(rs.getString("nrvezes"));
            itemAaData.put(getMoeda(empresa) + " " + Formatador.formatarValorMonetarioSemMoeda(rs.getDouble("valormensal")));
            aaData.put(itemAaData);
        }

        JSONObject objJson = new JSONObject();
        objJson.put("iTotalRecords", contar(sqlCount, getCon()));
        objJson.put("iTotalDisplayRecords", contar(sqlContarFiltrados.toString(), getCon()));
        objJson.put("sEcho", sEcho);
        objJson.put("aaData", aaData);

        return objJson.toString();
    }

    private String getMoeda(Integer empresa) throws SQLException {
        String moeda = "R$";
        PreparedStatement sqlConsultar = con.prepareStatement("select moeda from empresa where codigo = " + empresa);
        ResultSet rs = sqlConsultar.executeQuery();
        while (rs.next())
            moeda = rs.getString("moeda");
        return moeda;
    }

    private ResultSet getRS(Integer empresa, Integer offset, Integer limit, Integer colOrdenar, String dirOrdenar, String situacao, Boolean exportar,String clausulaLike) throws SQLException {
        StringBuilder sql = new StringBuilder();
        sql.append("SELECT \n");
        sql.append("mod.codigo, \n");
        sql.append("mod.nome, \n");
        sql.append("ativo, \n");
        sql.append("nrvezes, \n");
        sql.append("valormensal, \n");
        sql.append("tipo, \n");
        sql.append("(SELECT string_agg(empresa.nome, ', ') \n");
        sql.append("FROM modalidadeempresa \n");
        sql.append(" INNER JOIN empresa ON empresa.codigo = modalidadeempresa.empresa \n");
        sql.append(" WHERE modalidadeempresa.modalidade = mod.codigo) AS empresa \n");
        sql.append("FROM modalidade mod  \n");
        if (!UteisValidacao.emptyNumber(empresa)) {
            sql.append("left join modalidadeempresa me on me.modalidade = mod.codigo \n");
            sql.append("where me.empresa = ").append(empresa);
            if (situacao.equals("AT")) {
                sql.append(" AND mod.ativo = true \n");
            }else if (situacao.equals("NA")) {
                sql.append(" AND mod.ativo = false \n");
            }
        }else if (empresa == 0){
            if (situacao.equals("AT")) {
                sql.append(" Where mod.ativo = true \n");
            }else if (situacao.equals("NA")) {
                sql.append(" Where mod.ativo = false \n");
            }
        }else {

            sql.append("Where 1=1  \n");
        }
        if (!UteisValidacao.emptyString(clausulaLike)) {
            sql.append(" AND (");
            sql.append("lower(mod.codigo::VARCHAR) ~ '").append(clausulaLike).append("' OR\n");
            sql.append("lower(mod.nome) ~ '").append(clausulaLike).append("' \n");
            sql.append("OR lower((SELECT string_agg(empresa.nome, ', ') \n" +
                    "FROM modalidadeempresa \n" +
                    " INNER JOIN empresa ON empresa.codigo = modalidadeempresa.empresa \n" +
                    " WHERE modalidadeempresa.modalidade = mod.codigo)) ~ '").append(clausulaLike).append("')\n");

            if(clausulaLike.toLowerCase().equals("[aà-æ]t[iì-ï]v[oò-ö]") && situacao.equals("AT")){
                sql.append(" or mod.ativo = true");
            }else if (clausulaLike.toLowerCase().equals("[iì-ï][nñ][aà-æ]t[iì-ï]v[oò-ö]") && situacao.equals("NA")){
                sql.append(" or mod.ativo = false");
            }
        }

        if (!exportar){
            sql.append("  ORDER BY ").append(colOrdenar + 1).append(" ").append(dirOrdenar).append("\n");
            if (limit > 0) {
                sql.append(" LIMIT ").append(limit).append("\n");
            }
            sql.append(" OFFSET ").append(offset).append("\n");

        }
        PreparedStatement sqlConsultar = con.prepareStatement(sql.toString());
        return sqlConsultar.executeQuery();
    }
    public List consultarParaImpressao(String filtro, String ordem, String campoOrdenacao, Integer empresa,String situacao) throws SQLException {

        ResultSet rs = getRS(empresa, 1, 0, 1, campoOrdenacao, situacao,true,"");
        List<ModalidadeVO> lista = new ArrayList<>();

        while (rs.next()) {

            ModalidadeVO mod = new ModalidadeVO();
            String geral = rs.getString("codigo") + rs.getString("nome") + rs.getString("ativo") + rs.getString("empresa") + rs.getString("nrvezes") + rs.getString("valormensal");
            if (geral.toLowerCase().contains(filtro.toLowerCase())) {
                mod.setCodigo(rs.getInt("codigo"));
                mod.setNome(rs.getString("nome"));
                mod.setAtivo(rs.getBoolean("ativo"));
                mod.setEmpresa(rs.getString("empresa"));
                mod.setNrVezes(rs.getInt("nrvezes"));
                mod.setValorMensal(rs.getDouble("valormensal"));
                mod.setTipo(rs.getInt("tipo"));
                lista.add(mod);
            }
        }
        switch (campoOrdenacao) {
            case "Código":
                Ordenacao.ordenarLista(lista, "codigo");
                break;
            case "Descrição":
                Ordenacao.ordenarLista(lista, "nome");
                break;
            case "Situação":
                Ordenacao.ordenarLista(lista, "situacao");
                break;
            case "Empresa(s)":
                Ordenacao.ordenarLista(lista, "empresa");
                break;
            case "Nr de Vezes":
                Ordenacao.ordenarLista(lista, "nrVezes");
                break;
            case "Valor":
                Ordenacao.ordenarLista(lista, "valorMensal");
                break;
            case "Tipo":
                Ordenacao.ordenarLista(lista, "tipo");
                break;
        }
        if (ordem.contains("desc")) {
            Collections.reverse(lista);
        }
        return lista;

    }

    public List<ModalidadeVO>consultarModalidadeAtivaComTurma(Integer codigoEmpresa, TipoTurmaEnum tipoTurmaEnum, int nivelMontarDados) throws Exception{
        StringBuilder sql = new StringBuilder();
        sql.append("select distinct mod.*  \n");
        sql.append("from modalidade mod \n");
        sql.append("inner join modalidadeEmpresa modEmp on modEmp.modalidade = mod.codigo \n");
        sql.append("inner join turma t on t.modalidade = mod.codigo \n");
        sql.append("where ativo = true and modEmp.empresa = ? \n");
        if (tipoTurmaEnum != TipoTurmaEnum.TODOS){
            sql.append("and t.aulacoletiva = ").append( (tipoTurmaEnum == TipoTurmaEnum.AULA_CHEIA) ? "true": "false ");
        }
        sql.append(" order by mod.nome ");
        try (PreparedStatement pst = con.prepareStatement(sql.toString())) {
            pst.setInt(1, codigoEmpresa);
            try (ResultSet rs = pst.executeQuery()) {
                return montarDadosConsulta(rs, nivelMontarDados, con);
            }
        }

    }

    public List<ModalidadeVO> consultarModalidadeAtivaSemTurma(Integer codigoEmpresa, int nivelMontarDados) throws Exception {
        StringBuilder sql = new StringBuilder();
        sql.append("select distinct m.*  \n");
        sql.append("from modalidade m \n");
        sql.append("inner join modalidadeEmpresa me on me.modalidade = m.codigo \n");
        sql.append("where m.ativo = true \n");
        sql.append("and m.utilizarturma is false \n");
        sql.append("and me.empresa = ? \n");
        sql.append("order by m.nome ");
        try (PreparedStatement pst = con.prepareStatement(sql.toString())) {
            pst.setInt(1, codigoEmpresa);
            try (ResultSet rs = pst.executeQuery()) {
                return montarDadosConsulta(rs, nivelMontarDados, con);
            }
        }
    }

    public Map<Integer, String> consultarEmpresasModalidadesSimplificado() throws Exception{
        Map<Integer, String> mapa = new HashMap<>();
        ResultSet rs = criarConsulta("select modalidade, nome from modalidadeempresa  \n" +
                "inner join empresa ON empresa.codigo = modalidadeempresa.empresa", con);
        while (rs.next()) {
            String empresas = mapa.get(rs.getInt("modalidade"));
            if (UteisValidacao.emptyString(empresas)) {
                empresas = rs.getString("nome");
                mapa.put(rs.getInt("modalidade"), empresas);
            } else {
                empresas += "," + rs.getString("nome");
            }
        }
        return mapa;
    }

    @Override
    public Map<Integer, String> consultarModalidadesSimplificado(Integer empresa) throws Exception {
        Map<Integer, String> mapResult = new HashMap<>();
        String sql;
        sql = "SELECT modalidade.nome, modalidade.codigo FROM modalidade \n"
                + "LEFT JOIN modalidadeempresa ON modalidade.codigo = modalidadeempresa.modalidade\n"
                + "WHERE modalidadeempresa.empresa = null OR modalidadeempresa.empresa = "
                + empresa + "\n"
                + " AND modalidade.ativo is true"
                + " ORDER BY modalidade.nome";
        ResultSet consulta = Modalidade.criarConsulta(sql, con);
        while (consulta.next()) {
            mapResult.put(consulta.getInt("codigo"), consulta.getString("nome"));
        }
        return mapResult;
    }

    public Boolean modalidadeTemContratoVendido(Integer codigoModalidade) throws Exception{
        String sql = "SELECT EXISTS(SELECT codigo FROM contratomodalidade WHERE modalidade = "+
                codigoModalidade+") AS existe";
        try (ResultSet rs = con.prepareStatement(sql).executeQuery()) {
            return rs.next() && rs.getBoolean("existe");
        }
    }

    public Boolean consultarPorTipo(Integer tipo) throws Exception {
        String sql = "SELECT EXISTS(SELECT tipo FROM modalidade WHERE tipo = " + tipo + ") AS existe";
        try (ResultSet rs = con.prepareStatement(sql).executeQuery()) {
            return rs.next() && rs.getBoolean("existe");
        }

    }

    @Override
    public List consultarPorCodigoTurmaEOuProfessor(int turmaSelecionada, int professorSelecionado, Integer codigoEmpresa, int nivelmontardadosDadosbasicos) throws Exception {
        String sqlStr = "SELECT DISTINCT m.codigo, m.nome FROM modalidade as m  \n" +
                "inner join turma on m.codigo = turma.modalidade  \n" +
                "inner join horarioturma on turma.codigo = horarioturma.turma   \n" +
                "INNER JOIN colaborador on colaborador.codigo = horarioturma.professor  " +
                " where 1=1 ";

        if (turmaSelecionada != 0) {
            sqlStr += " and turma.codigo = " + turmaSelecionada;
        }

        if(professorSelecionado != 0){
            sqlStr += " and colaborador.codigo = " + professorSelecionado;
        }

        sqlStr += " and m.utilizarTurma is true";

        List<ModalidadeVO> vetResultado;
        try (Statement stm = con.createStatement()) {
            try (ResultSet tabelaResultado = stm.executeQuery(sqlStr)) {
                vetResultado = new ArrayList<>();
                while (tabelaResultado.next()) {
                    ModalidadeVO obj = montarDadosBasicoGestao(tabelaResultado, nivelmontardadosDadosbasicos);
                    vetResultado.add(obj);
                }
            }
        }
        return vetResultado;
    }

    public void atualizarAlunosCrossfit(Integer codigoModalidade, Boolean crossfit, String chave) {
        try {
            String sql = "SELECT con.codigo as contrato, cli.codigomatricula, cli.codigo, cli.pessoa FROM contrato con\n"
                    + "INNER JOIN cliente cli ON con.pessoa = cli.pessoa\n"
                    + "WHERE con.situacao = 'AT' and  con.codigo IN (SELECT contrato FROM contratomodalidade WHERE modalidade = "
                    + codigoModalidade + ")"
                    + "UNION ALL " +
                    "SELECT con.codigo AS contrato, cli.codigomatricula, cli.codigo, cli.pessoa " +
                    "FROM contratodependente cd " +
                    "JOIN cliente cli ON cli.codigo = cd.cliente " +
                    "JOIN contratomodalidade cm ON cm.contrato = cd.contrato " +
                    "JOIN contrato con ON con.codigo = cd.contrato " +
                    "WHERE con.situacao = 'AT' " +
                    "AND cm.modalidade = " + codigoModalidade;

            try (ResultSet rs = con.prepareStatement(sql).executeQuery()) {
                while (rs.next()) {
                    try {
                        atualizarInformacoesCrossfitSintetico(rs, chave, crossfit);
                        TreinoWSConsumer.atualizarCrossfit(chave, rs.getInt("codigomatricula"), crossfit);
                    } catch (Exception e) {
                        Uteis.logar(e, Modalidade.class);
                    }
                }
            }
        } catch (Exception e) {
            Uteis.logar(e, Modalidade.class);
        }

    }

    private void atualizarInformacoesCrossfitSintetico(ResultSet rs, String chave, boolean crossfit) throws Exception {
        if (JSFUtilities.isJSFContext()) {
            getFacade().getSituacaoClienteSinteticoDW().atualizarInformacoesCrossfit(null, false, rs.getInt("pessoa"));
            TreinoWSConsumer.atualizarCrossfit(chave, rs.getInt("codigomatricula"), crossfit);
        } else {
            SituacaoClienteSinteticoDW situacaoClienteSinteticoDW = null;
            try {
                situacaoClienteSinteticoDW = new SituacaoClienteSinteticoDW(con);
                situacaoClienteSinteticoDW.atualizarInformacoesCrossfit(null, false, rs.getInt("pessoa"));
            } finally {
                situacaoClienteSinteticoDW = null;
            }
        }
    }

    public byte[] obterFoto(final String chave, final Integer codigo) throws Exception {

        byte[] foto = null;
        if (!UteisValidacao.emptyNumber(codigo)) {
            foto = MidiaService.getInstance().downloadObjectAsByteArray(chave,
                    MidiaEntidadeEnum.FOTO_MODADLIDADE, codigo.toString(), null);
        }
        if (foto == null) {
            foto = MidiaService.getInstance().downloadObjectAsByteArray(chave,
                    MidiaEntidadeEnum.FOTO_MODADLIDADE, null, null);
        }
        return foto;

    }

    public List<ModalidadeVO> consultarSimples(Integer empresa) throws Exception{
        StringBuilder sql = new StringBuilder();
        sql.append(" SELECT m.codigo, m.nome, utilizarTurma, nrVezes  from modalidade m");
        sql.append(" LEFT JOIN modalidadeempresa me on me.modalidade = m.codigo");
        sql.append(" WHERE ativo");
        if(UteisValidacao.notEmptyNumber(empresa)){
            sql.append(" AND (me.codigo is null or me.empresa = ").append(empresa).append(")");
        }
        sql.append(" GROUP BY m.nome, m.codigo");
        sql.append(" ORDER BY m.nome");
        ResultSet rs = SuperFacadeJDBC.criarConsulta(sql.toString(), con);
        List<ModalidadeVO> modalidades = new ArrayList<>();
        while(rs.next()){
            ModalidadeVO modalidade = new ModalidadeVO();
            modalidade.setCodigo(rs.getInt("codigo"));
            modalidade.setNome(rs.getString("nome"));
            modalidade.setUtilizarTurma(rs.getBoolean("utilizarTurma"));
            modalidade.setNrVezes(rs.getInt("nrVezes"));
            modalidades.add(modalidade);
        }
        return modalidades;
    }

    @Override
    public void alterarNomeModalidade(Integer codigo, String nome) throws Exception {
        String sql = "UPDATE modalidade SET nome = ? WHERE codigo = ?";
        PreparedStatement ps = con.prepareStatement(sql);
        ps.setString(1, nome);
        ps.setInt(2, codigo);
        ps.executeUpdate();
    }

    @Override
    public List<Integer> consultarTodosCodigos() throws Exception {
        String sql = "SELECT codigo FROM modalidade ORDER BY ativo DESC";
        List<Integer> codigos = new ArrayList<Integer>();
        ResultSet rs = con.prepareStatement(sql).executeQuery();
        while(rs.next()){
            codigos.add(rs.getInt("codigo"));
        }
        return codigos;
    }


    public JSONArray todasModalidades(Integer empresa, Integer tamanho, Integer pagina) throws Exception{
        StringBuilder sql = new StringBuilder();
        sql.append(" select codigo, nome from modalidade ");
        sql.append(" where ativo ");
        sql.append(" order by nome ");

        if(!UteisValidacao.emptyNumber(tamanho)){
            sql.append(" limit ").append(tamanho);
        }

        if(!UteisValidacao.emptyNumber(pagina)){
            sql.append(" offset ").append(pagina);
        }

        JSONArray modalidades = new JSONArray();

        ResultSet rs = criarConsulta(sql.toString(), con);
        while(rs.next()){
            JSONObject modalidade = new JSONObject();
            modalidade.put("id", rs.getInt("codigo"));
            modalidade.put("nome", rs.getString("nome"));
            modalidades.put(modalidade);
        }
        return modalidades;

    }

    @Override
    public void excluirImagem(Integer codigo) throws Exception {
        String sql = "UPDATE modalidade SET fotokey = null WHERE codigo = ?";
        PreparedStatement ps = con.prepareStatement(sql);
        ps.setInt(1, codigo);
        ps.executeUpdate();
    }

    @Override
    public ModalidadeVO consultarPorIdExterno(String idExterno, int nivelMontarDados) throws Exception {
        String sql = "SELECT * FROM modalidade WHERE idexterno = ?";
        try (PreparedStatement pstm = this.con.prepareStatement(sql)) {
            pstm.setString(1, idExterno);
            try (ResultSet rs = pstm.executeQuery()) {
                if (rs.next()) {
                    return montarDados(rs, nivelMontarDados, this.con);
                }
            }
        }
        return null;
    }
}

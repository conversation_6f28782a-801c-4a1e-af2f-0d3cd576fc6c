
package negocio.facade.jdbc.plano;

import java.sql.Connection;
import java.sql.Statement;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.util.ArrayList;
import java.util.List;
import negocio.comuns.plano.DescontoRenovacaoVO;
import negocio.comuns.plano.enumerador.TipoDesconto;
import negocio.comuns.plano.enumerador.TipoIntervalo;
import negocio.comuns.utilitarias.Uteis;
import negocio.facade.jdbc.arquitetura.SuperEntidade;
import negocio.interfaces.plano.DescontoRenovacaoInterfaceFacade;

/**
 *
 * <AUTHOR>
 */
public class DescontoRenovacao extends SuperEntidade implements DescontoRenovacaoInterfaceFacade {
    
    public DescontoRenovacao()  throws Exception {
        super();
    }

    public DescontoRenovacao(Connection con) throws Exception {
		super(con);
	}

	@Override
    public void incluir(DescontoRenovacaoVO obj) throws Exception {
        try {
            con.setAutoCommit(false);
            incluir(getIdEntidade());
            incluirSemCommit(obj);
            con.commit();
        } catch (Exception e) {
            con.rollback();
            throw e;
        } finally {
            con.setAutoCommit(true);
        }
    }

    @Override
    public void incluirSemCommit(DescontoRenovacaoVO obj) throws Exception {
        obj.validarDados();
        String sql = "INSERT INTO descontorenovacao ( desconto, tipointervalo, "
                   + "intervalode, intervaloate, tipodesconto, "
                   + "valor, tipojustificativa) VALUES ( ?, ?, ?, ?, ?, ?, ? )";
        PreparedStatement sqlInserir = con.prepareStatement(sql);
        sqlInserir.setInt(1, obj.getDesconto());
        sqlInserir.setString(2, obj.getTipoIntervalo().name());
        sqlInserir.setInt(3, obj.getIntervaloDe());
        sqlInserir.setInt(4, obj.getIntervaloAte());
        sqlInserir.setString(5, obj.getTipoDesconto().name());
        sqlInserir.setDouble(6, obj.getValor());
        sqlInserir.setInt(7, obj.getJustificativaBonus().getCodigo());
        sqlInserir.execute();
        obj.setCodigo(obterValorChavePrimariaCodigo());
        obj.setNovoObj(false);
    }

    @Override
    public void alterar(DescontoRenovacaoVO obj) throws Exception {
        try {
            con.setAutoCommit(false);
            alterar(getIdEntidade());
            alterarSemCommit(obj);
            con.commit();
        } catch (Exception e) {
            con.rollback();
            throw e;
        } finally {
            con.setAutoCommit(true);
        }
    }

    @Override
    public void alterarSemCommit(DescontoRenovacaoVO obj) throws Exception {
        obj.validarDados();
        String sql = "UPDATE descontorenovacao SET desconto = ?, tipointervalo = ?, "
                   + "intervalode = ?, intervaloate = ?, tipodesconto = ?, "
                   + "valor = ?, tipojustificativa = ? WHERE codigo = ?";
        PreparedStatement sqlAlterar = con.prepareStatement(sql);
        sqlAlterar.setInt(1, obj.getDesconto());
        sqlAlterar.setString(2, obj.getTipoIntervalo().name());
        sqlAlterar.setInt(3, obj.getIntervaloDe());
        sqlAlterar.setInt(4, obj.getIntervaloAte());
        sqlAlterar.setString(5, obj.getTipoDesconto().name());
        sqlAlterar.setDouble(6, obj.getValor());
        sqlAlterar.setInt(7, obj.getJustificativaBonus().getCodigo());
        sqlAlterar.execute();
    }

    @Override
    public void excluir(int desconto) throws Exception {
        try {
            con.setAutoCommit(false);
            excluir(getIdEntidade());
            excluirSemCommit(desconto);
            con.commit();
        } catch (Exception e) {
            con.rollback();
            throw e;
        } finally {
            con.setAutoCommit(true);
        }
    }

    @Override
    public void excluirSemCommit(int desconto) throws Exception {
        String sql = "DELETE FROM descontorenovacao WHERE desconto = ?";
        PreparedStatement sqlExcluir = con.prepareStatement(sql);
        sqlExcluir.setInt(1, desconto);
        sqlExcluir.execute();
    }

    @Override
    public List<DescontoRenovacaoVO> consultarPorCodigo(int codigo, boolean controlarAcesso, int nivelMontarDados) throws Exception {
        consultar(getIdEntidade(), controlarAcesso);
        String sqlStr = "SELECT * FROM descontorenovacao WHERE codigo >= ?";
        PreparedStatement stm = con.prepareStatement(sqlStr);
        stm.setInt(1, codigo);
        ResultSet tabelaResultado = stm.executeQuery(sqlStr);
        return montarDadosConsulta(tabelaResultado, nivelMontarDados);
    }

    @Override
    public DescontoRenovacaoVO consultarPorChavePrimaria(int codigo, int nivelMontarDados) throws Exception {
        consultar(getIdEntidade(), false);
        String sql = "SELECT * FROM descontorenovacao WHERE codigo = ?";
        PreparedStatement sqlConsultar = con.prepareStatement(sql);
        sqlConsultar.setInt(1, codigo);
        ResultSet tabelaResultado = sqlConsultar.executeQuery();
        if (tabelaResultado.next())
            return montarDados(tabelaResultado, nivelMontarDados);
        else
            return new DescontoRenovacaoVO();
    }

    @Override
    public List<DescontoRenovacaoVO> consultarPorDesconto(int desconto, boolean controlarAcesso, int nivelMontarDados) throws Exception {
        consultar(getIdEntidade(), controlarAcesso);
        String sqlStr = "SELECT * FROM descontorenovacao WHERE desconto = "+desconto;
        Statement stm = con.createStatement();
        ResultSet tabelaResultado = stm.executeQuery(sqlStr);
        return montarDadosConsulta(tabelaResultado, nivelMontarDados);
    }

    public List<DescontoRenovacaoVO> montarDadosConsulta(ResultSet tabelaResultado, int nivelMontarDados) throws Exception {
        List<DescontoRenovacaoVO> vetResultado = new ArrayList();
        while (tabelaResultado.next()) {
            DescontoRenovacaoVO obj = new DescontoRenovacaoVO();
            obj = montarDados(tabelaResultado, nivelMontarDados);
            vetResultado.add(obj);
        }
        return vetResultado;
    }

    public DescontoRenovacaoVO montarDados(ResultSet dadosSQL, int nivelMontarDados) throws Exception {
        DescontoRenovacaoVO obj = new DescontoRenovacaoVO();
        obj.setCodigo(dadosSQL.getInt("codigo"));
        obj.setDesconto(dadosSQL.getInt("desconto"));
        obj.setTipoIntervalo(TipoIntervalo.getTipoIntervalo(dadosSQL.getString("tipointervalo")));
        obj.setIntervaloDe(dadosSQL.getInt("intervalode"));
        obj.setIntervaloAte(dadosSQL.getInt("intervaloate"));
        obj.setTipoDesconto(TipoDesconto.getTipoDesconto(dadosSQL.getString("tipoDesconto")));
        obj.setValor(dadosSQL.getDouble("valor"));
        if(dadosSQL.getInt("tipojustificativa") != 0)
            obj.setJustificativaBonus(getFacade().getJustificativaOperacao().consultarPorChavePrimaria(dadosSQL.getInt("tipojustificativa"), Uteis.NIVELMONTARDADOS_DADOSBASICOS));
        obj.setNovoObj(false);
        if (nivelMontarDados == Uteis.NIVELMONTARDADOS_DADOSBASICOS) {
            return obj;
        }
        return obj;
    }
}

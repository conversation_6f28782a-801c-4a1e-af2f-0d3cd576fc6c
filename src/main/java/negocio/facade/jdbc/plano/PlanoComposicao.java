package negocio.facade.jdbc.plano;

import negocio.comuns.plano.ComposicaoVO;
import java.util.Iterator;
import negocio.comuns.plano.PlanoComposicaoVO;
import negocio.facade.jdbc.arquitetura.*;

import java.sql.Connection;
import java.sql.ResultSet;
import java.sql.PreparedStatement;
import java.sql.Statement;
import java.util.List;
import java.util.ArrayList;
import negocio.comuns.utilitarias.*;

/**
 * Classe de persistência que encapsula todas as operações de manipulação dos dados da classe <code>PlanoComposicaoVO</code>.
 * Responsável por implementar operações como incluir, alterar, excluir e consultar pertinentes a classe <code>PlanoComposicaoVO</code>.
 * Encapsula toda a interação com o banco de dados.
 * @see PlanoComposicaoVO
 * @see SuperEntidade
 * @see Plano
 */
public class PlanoComposicao extends SuperEntidade {    

    public PlanoComposicao() throws Exception {
        super();
        setIdEntidade("Plano");
    }

    public PlanoComposicao(Connection con) throws Exception {
    	super(con);
        setIdEntidade("Plano");
	}

	/**
     * Operação responsável por retornar um novo objeto da classe <code>PlanoComposicaoVO</code>.
     */
    public PlanoComposicaoVO novo() throws Exception {
        incluir(getIdEntidade());
        PlanoComposicaoVO obj = new PlanoComposicaoVO();
        return obj;
    }

    /**
     * Operação responsável por incluir no banco de dados um objeto da classe <code>PlanoComposicaoVO</code>.
     * Primeiramente valida os dados (<code>validarDados</code>) do objeto. Verifica a conexão com o banco de dados e a permissão do usuário
     * para realizar esta operacão na entidade.
     * Isto, através da operação <code>incluir</code> da superclasse.
     * @param obj  Objeto da classe <code>PlanoComposicaoVO</code> que será gravado no banco de dados.
     * @exception Exception Caso haja problemas de conexão, restrição de acesso ou validação de dados.
     */
    public void incluir(PlanoComposicaoVO obj) throws Exception {
        PlanoComposicaoVO.validarDados(obj);
        // PlanoComposicao.incluir(getIdEntidade());
        obj.realizarUpperCaseDados();
        String sql = "INSERT INTO PlanoComposicao( plano, composicao ) VALUES ( ?, ? )";
        PreparedStatement sqlInserir = con.prepareStatement(sql);
        if (obj.getPlano().intValue() != 0) {
            sqlInserir.setInt(1, obj.getPlano().intValue());
        } else {
            sqlInserir.setNull(1, 0);
        }
        if (obj.getComposicao().getCodigo().intValue() != 0) {
            sqlInserir.setInt(2, obj.getComposicao().getCodigo().intValue());
        } else {
            sqlInserir.setNull(2, 0);
        }
        sqlInserir.execute();
        obj.setCodigo(obterValorChavePrimariaCodigo());
        obj.setNovoObj(new Boolean(false));
    }

    /**
     * Operação responsável por alterar no BD os dados de um objeto da classe <code>PlanoComposicaoVO</code>.
     * Sempre utiliza a chave primária da classe como atributo para localização do registro a ser alterado.
     * Primeiramente valida os dados (<code>validarDados</code>) do objeto. Verifica a conexão com o banco de dados e a permissão do usuário
     * para realizar esta operacão na entidade.
     * Isto, através da operação <code>alterar</code> da superclasse.
     * @param obj    Objeto da classe <code>PlanoComposicaoVO</code> que será alterada no banco de dados.
     * @exception Execption Caso haja problemas de conexão, restrição de acesso ou validação de dados.
     */
    public void alterar(PlanoComposicaoVO obj) throws Exception {
        PlanoComposicaoVO.validarDados(obj);
        alterar(getIdEntidade());
        obj.realizarUpperCaseDados();
        String sql = "UPDATE PlanoComposicao set plano=?, composicao=? WHERE ((codigo = ?))";
        PreparedStatement sqlAlterar = con.prepareStatement(sql);
        if (obj.getPlano().intValue() != 0) {
            sqlAlterar.setInt(1, obj.getPlano().intValue());
        } else {
            sqlAlterar.setNull(1, 0);
        }
        if (obj.getComposicao().getCodigo().intValue() != 0) {
            sqlAlterar.setInt(2, obj.getComposicao().getCodigo().intValue());
        } else {
            sqlAlterar.setNull(2, 0);
        }
        sqlAlterar.setInt(3, obj.getCodigo().intValue());
        sqlAlterar.execute();
    }

    /**
     * Operação responsável por excluir no BD um objeto da classe <code>PlanoComposicaoVO</code>.
     * Sempre localiza o registro a ser excluído através da chave primária da entidade.
     * Primeiramente verifica a conexão com o banco de dados e a permissão do usuário
     * para realizar esta operacão na entidade.
     * Isto, através da operação <code>excluir</code> da superclasse.
     * @param obj    Objeto da classe <code>PlanoComposicaoVO</code> que será removido no banco de dados.
     * @exception Execption Caso haja problemas de conexão ou restrição de acesso.
     */
    public void excluir(PlanoComposicaoVO obj) throws Exception {
        excluir(getIdEntidade());
        String sql = "DELETE FROM PlanoComposicao WHERE ((codigo = ?))";
        PreparedStatement sqlExcluir = con.prepareStatement(sql);
        sqlExcluir.setInt(1, obj.getCodigo().intValue());
        sqlExcluir.execute();
    }

    /**
     * Responsável por realizar uma consulta de <code>PlanoComposicao</code> através do valor do atributo 
     * <code>descricao</code> da classe <code>Composicao</code>
     * Faz uso da operação <code>montarDadosConsulta</code> que realiza o trabalho de prerarar o List resultante.
     * @return  List Contendo vários objetos da classe <code>PlanoComposicaoVO</code> resultantes da consulta.
     * @exception Execption Caso haja problemas de conexão ou restrição de acesso.
     */
    public List consultarPorDescricaoComposicao(String valorConsulta, int nivelMontarDados) throws Exception {
        consultar(getIdEntidade(), true);
        String sqlStr = "SELECT PlanoComposicao.* FROM PlanoComposicao, Composicao WHERE PlanoComposicao.composicao = Composicao.codigo and upper( Composicao.descricao ) like('" + valorConsulta.toUpperCase() + "%') ORDER BY Composicao.descricao";
        Statement stm = con.createStatement();
        ResultSet tabelaResultado = stm.executeQuery(sqlStr);
        return montarDadosConsulta(tabelaResultado, nivelMontarDados, this.con);
    }

    /**
     * Responsável por realizar uma consulta de <code>PlanoComposicao</code> através do valor do atributo 
     * <code>descricao</code> da classe <code>Plano</code>
     * Faz uso da operação <code>montarDadosConsulta</code> que realiza o trabalho de prerarar o List resultante.
     * @return  List Contendo vários objetos da classe <code>PlanoComposicaoVO</code> resultantes da consulta.
     * @exception Execption Caso haja problemas de conexão ou restrição de acesso.
     */
    public List consultarPorDescricaoPlano(String valorConsulta, int nivelMontarDados) throws Exception {
        consultar(getIdEntidade(), true);
        String sqlStr = "SELECT PlanoComposicao.* FROM PlanoComposicao, Plano WHERE PlanoComposicao.plano = Plano.codigo and upper( Plano.descricao ) like('" + valorConsulta.toUpperCase() + "%') ORDER BY Plano.descricao";
        Statement stm = con.createStatement();
        ResultSet tabelaResultado = stm.executeQuery(sqlStr);
        return montarDadosConsulta(tabelaResultado, nivelMontarDados, this.con);
    }

    /**
     * Responsável por realizar uma consulta de <code>PlanoComposicao</code> através do valor do atributo 
     * <code>Integer codigo</code>. Retorna os objetos com valores iguais ou superiores ao parâmetro fornecido.
     * Faz uso da operação <code>montarDadosConsulta</code> que realiza o trabalho de prerarar o List resultante.
     * @param   controlarAcesso Indica se a aplicação deverá verificar se o usuário possui permissão para esta consulta ou não.
     * @return  List Contendo vários objetos da classe <code>PlanoComposicaoVO</code> resultantes da consulta.
     * @exception Exception Caso haja problemas de conexão ou restrição de acesso.
     */
    public List consultarPorCodigo(Integer valorConsulta, boolean controlarAcesso, int nivelMontarDados) throws Exception {
        consultar(getIdEntidade(), controlarAcesso);
        String sqlStr = "SELECT * FROM PlanoComposicao WHERE codigo >= " + valorConsulta.intValue() + " ORDER BY codigo";
        Statement stm = con.createStatement();
        ResultSet tabelaResultado = stm.executeQuery(sqlStr);
        return (montarDadosConsulta(tabelaResultado, nivelMontarDados, this.con));
    }

    /**
     * Responsável por montar os dados de vários objetos, resultantes de uma consulta ao banco de dados (<code>ResultSet</code>).
     * Faz uso da operação <code>montarDados</code> que realiza o trabalho para um objeto por vez.
     * @return  List Contendo vários objetos da classe <code>PlanoComposicaoVO</code> resultantes da consulta.
     */
    public static List montarDadosConsulta(ResultSet tabelaResultado, int nivelMontarDados, Connection con) throws Exception {
        List vetResultado = new ArrayList();
        while (tabelaResultado.next()) {
            PlanoComposicaoVO obj = new PlanoComposicaoVO();
            obj = montarDados(tabelaResultado, nivelMontarDados, con);
            vetResultado.add(obj);
        }
        return vetResultado;
    }

    /**
     * Responsável por montar os dados resultantes de uma consulta ao banco de dados (<code>ResultSet</code>)
     * em um objeto da classe <code>PlanoComposicaoVO</code>.
     * @return  O objeto da classe <code>PlanoComposicaoVO</code> com os dados devidamente montados.
     */
    public static PlanoComposicaoVO montarDados(ResultSet dadosSQL, int nivelMontarDados, Connection con) throws Exception {
        PlanoComposicaoVO obj = new PlanoComposicaoVO();
        obj.setCodigo(new Integer(dadosSQL.getInt("codigo")));
        obj.setPlano(new Integer(dadosSQL.getInt("plano")));
        obj.getComposicao().setCodigo(new Integer(dadosSQL.getInt("composicao")));
        obj.setNovoObj(new Boolean(false));
        if (nivelMontarDados == Uteis.NIVELMONTARDADOS_DADOSBASICOS) {
            return obj;
        }

        montarDadosComposicao(obj, Uteis.NIVELMONTARDADOS_TODOS, con);
        return obj;
    }

    /**
     * Operação responsável por montar os dados de um objeto da classe <code>ComposicaoVO</code> relacionado ao objeto <code>PlanoComposicaoVO</code>.
     * Faz uso da chave primária da classe <code>ComposicaoVO</code> para realizar a consulta.
     * @param obj  Objeto no qual será montado os dados consultados.
     */
    public static void montarDadosComposicao(PlanoComposicaoVO obj, int nivelMontarDados, Connection con) throws Exception {
        if (obj.getComposicao().getCodigo().intValue() == 0) {
            obj.setComposicao(new ComposicaoVO());
            return;
        }
        Composicao composicao = new Composicao(con);
        obj.setComposicao(composicao.consultarPorChavePrimaria(obj.getComposicao().getCodigo(), nivelMontarDados));
        composicao = null;
    }

    /**
     * Operação responsável por excluir todos os objetos da <code>PlanoComposicaoVO</code> no BD.
     * Faz uso da operação <code>excluir</code> disponível na classe <code>PlanoComposicao</code>.
     * @param <code>plano</code> campo chave para exclusão dos objetos no BD.
     * @exception Exception  Erro de conexão com o BD ou restrição de acesso a esta operação.
     */
    public void excluirPlanoComposicaos(Integer plano) throws Exception {
        excluir(getIdEntidade());
        String sql = "DELETE FROM PlanoComposicao WHERE (plano = ?)";
        PreparedStatement sqlExcluir = con.prepareStatement(sql);
        sqlExcluir.setInt(1, plano.intValue());
        sqlExcluir.execute();
    }

    /**
     * Operação responsável por alterar todos os objetos da <code>PlanoComposicaoVO</code> contidos em um Hashtable no BD.
     * Faz uso da operação <code>excluirPlanoComposicaos</code> e <code>incluirPlanoComposicaos</code> disponíveis na classe <code>PlanoComposicao</code>.
     * @param objetos  List com os objetos a serem alterados ou incluídos no BD.
     * @exception Exception  Erro de conexão com o BD ou restrição de acesso a esta operação.
     */
    public void alterarPlanoComposicaos(Integer plano, List objetos) throws Exception {
        String str = "DELETE FROM PlanoComposicao WHERE plano = " + plano.intValue();
        Iterator i = objetos.iterator();
        while (i.hasNext()) {
            PlanoComposicaoVO objeto = (PlanoComposicaoVO) i.next();
            str += " AND codigo <> " + objeto.getCodigo().intValue();
        }
        PreparedStatement sqlExcluir = con.prepareStatement(str);
        sqlExcluir.execute();
        Iterator e = objetos.iterator();
        while (e.hasNext()) {
            PlanoComposicaoVO obj = (PlanoComposicaoVO) e.next();
            if (obj.getCodigo().equals(new Integer(0))) {
                obj.setPlano(plano);
                incluir(obj);
            } else {
                alterar(obj);
            }
        }

        //        excluirPlanoComposicaos( plano );
//        incluirPlanoComposicaos( plano, objetos );
    }

    /**
     * Operação responsável por incluir objetos da <code>PlanoComposicaoVO</code> no BD.
     * Garantindo o relacionamento com a entidade principal <code>plano.Plano</code> através do atributo de vínculo.
     * @param objetos List contendo os objetos a serem gravados no BD da classe.
     * @exception Exception  Erro de conexão com o BD ou restrição de acesso a esta operação.
     */
    public void incluirPlanoComposicaos(Integer planoPrm, List objetos) throws Exception {
        Iterator e = objetos.iterator();
        while (e.hasNext()) {
            PlanoComposicaoVO obj = (PlanoComposicaoVO) e.next();
            obj.setPlano(planoPrm);
            incluir(obj);
        }
    }

    /**
     * Operação responsável por consultar todos os <code>PlanoComposicaoVO</code> relacionados a um objeto da classe <code>plano.Plano</code>.
     * @param plano  Atributo de <code>plano.Plano</code> a ser utilizado para localizar os objetos da classe <code>PlanoComposicaoVO</code>.
     * @return List  Contendo todos os objetos da classe <code>PlanoComposicaoVO</code> resultantes da consulta.
     * @exception Exception  Erro de conexão com o BD ou restrição de acesso a esta operação.
     */
    public List consultarPlanoComposicaos(Integer plano, int nivelMontarDados) throws Exception {
        consultar(getIdEntidade());
        List objetos = new ArrayList();
        String sql = "SELECT * FROM PlanoComposicao WHERE plano = ?";
        PreparedStatement sqlConsulta = con.prepareStatement(sql);
        sqlConsulta.setInt(1, plano.intValue());
        ResultSet resultado = sqlConsulta.executeQuery();
        while (resultado.next()) {
            PlanoComposicaoVO novoObj = new PlanoComposicaoVO();
            novoObj = PlanoComposicao.montarDados(resultado, nivelMontarDados, this.con);
            objetos.add(novoObj);
        }
        return objetos;
    }

    /**
     * Operação responsável por localizar um objeto da classe <code>PlanoComposicaoVO</code>
     * através de sua chave primária. 
     * @exception Exception Caso haja problemas de conexão ou localização do objeto procurado.
     */
    public PlanoComposicaoVO consultarPorChavePrimaria(Integer codigoPrm, int nivelMontarDados) throws Exception {
        consultar(getIdEntidade(), false);
        String sql = "SELECT * FROM PlanoComposicao WHERE codigo = ?";
        PreparedStatement sqlConsultar = con.prepareStatement(sql);
        sqlConsultar.setInt(1, codigoPrm.intValue());
        ResultSet tabelaResultado = sqlConsultar.executeQuery();
        if (!tabelaResultado.next()) {
            throw new ConsistirException("Dados Não Encontrados ( PlanoComposicao ).");
        }
        return (montarDados(tabelaResultado, nivelMontarDados, this.con));
    }
}

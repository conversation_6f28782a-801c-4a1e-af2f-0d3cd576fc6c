package negocio.facade.jdbc.plano;

import java.util.Iterator;
import negocio.comuns.plano.PlanoModalidadeVO;
import negocio.facade.jdbc.arquitetura.*;

import java.sql.Connection;
import java.sql.ResultSet;
import java.sql.PreparedStatement;
import java.sql.Statement;
import java.util.List;
import java.util.ArrayList;
import negocio.comuns.plano.PlanoTextoPadraoTagVO;
import negocio.comuns.utilitarias.*;

/**
 * Classe de persistência que encapsula todas as operações de manipulação dos dados da classe <code>PlanoModalidadeVO</code>.
 * Responsável por implementar operações como incluir, alterar, excluir e consultar pertinentes a classe <code>PlanoModalidadeVO</code>.
 * Encapsula toda a interação com o banco de dados.
 * @see PlanoModalidadeVO
 * @see SuperEntidade
 * @see Plano
 */
public class PlanoTextoPadraoTag extends SuperEntidade {    

    public PlanoTextoPadraoTag() throws Exception {
        super();
        setIdEntidade("Plano");
    }

    public PlanoTextoPadraoTag(Connection con) throws Exception {
    	super(con);
        setIdEntidade("Plano");
	}

	/**
     * Operação responsável por retornar um novo objeto da classe <code>PlanoModalidadeVO</code>.
     */
    public PlanoTextoPadraoTagVO novo() throws Exception {
        incluir(getIdEntidade());
        PlanoTextoPadraoTagVO obj = new PlanoTextoPadraoTagVO();
        return obj;
    }

    /**
     * Operação responsável por incluir no banco de dados um objeto da classe <code>PlanoModalidadeVO</code>.
     * Primeiramente valida os dados (<code>validarDados</code>) do objeto. Verifica a conexão com o banco de dados e a permissão do usuário
     * para realizar esta operacão na entidade.
     * Isto, através da operação <code>incluir</code> da superclasse.
     * @param obj  Objeto da classe <code>PlanoModalidadeVO</code> que será gravado no banco de dados.
     * @exception Exception Caso haja problemas de conexão, restrição de acesso ou validação de dados.
     */
    public void incluir(PlanoTextoPadraoTagVO obj) throws Exception {
        //  PlanoTextoPadraoTag.incluir(getIdEntidade());
        String sql = "INSERT INTO PlanoTextoPadraoTag( tag,planotextopadrao) VALUES ( ?, ? )";
        PreparedStatement sqlInserir = con.prepareStatement(sql);
        sqlInserir.setString(1, obj.getTag());
        sqlInserir.setInt(2, obj.getPlanoTextoPadrao().intValue());
        sqlInserir.execute();
        obj.setCodigo(obterValorChavePrimariaCodigo());
        obj.setNovoObj(new Boolean(false));
    }

    /**
     * Operação responsável por alterar no BD os dados de um objeto da classe <code>PlanoModalidadeVO</code>.
     * Sempre utiliza a chave primária da classe como atributo para localização do registro a ser alterado.
     * Primeiramente valida os dados (<code>validarDados</code>) do objeto. Verifica a conexão com o banco de dados e a permissão do usuário
     * para realizar esta operacão na entidade.
     * Isto, através da operação <code>alterar</code> da superclasse.
     * @param obj    Objeto da classe <code>PlanoModalidadeVO</code> que será alterada no banco de dados.
     * @exception Execption Caso haja problemas de conexão, restrição de acesso ou validação de dados.
     */
    public void alterar(PlanoTextoPadraoTagVO obj) throws Exception {
        alterar(getIdEntidade());
        String sql = "UPDATE PlanoTextoPadraoTag set tag=?, planotextopadrao=? WHERE ((codigo = ?))";
        PreparedStatement sqlAlterar = con.prepareStatement(sql);
        sqlAlterar.setString(1, obj.getTag());
        sqlAlterar.setInt(2, obj.getPlanoTextoPadrao().intValue());
        sqlAlterar.setInt(3, obj.getCodigo().intValue());
        sqlAlterar.execute();
    }

    /**
     * Operação responsável por excluir no BD um objeto da classe <code>PlanoModalidadeVO</code>.
     * Sempre localiza o registro a ser excluído através da chave primária da entidade.
     * Primeiramente verifica a conexão com o banco de dados e a permissão do usuário
     * para realizar esta operacão na entidade.
     * Isto, através da operação <code>excluir</code> da superclasse.
     * @param obj    Objeto da classe <code>PlanoModalidadeVO</code> que será removido no banco de dados.
     * @exception Execption Caso haja problemas de conexão ou restrição de acesso.
     */
    public void excluir(PlanoTextoPadraoTagVO obj) throws Exception {
        excluir(getIdEntidade());
        String sql = "DELETE FROM PlanoTextoPadraoTag WHERE ((codigo = ?))";
        PreparedStatement sqlExcluir = con.prepareStatement(sql);
        sqlExcluir.setInt(1, obj.getCodigo().intValue());
        sqlExcluir.execute();
    }

    /**
     * Responsável por realizar uma consulta de <code>PlanoModalidade</code> através do valor do atributo 
     * <code>Integer codigo</code>. Retorna os objetos com valores iguais ou superiores ao parâmetro fornecido.
     * Faz uso da operação <code>montarDadosConsulta</code> que realiza o trabalho de prerarar o List resultante.
     * @param   controlarAcesso Indica se a aplicação deverá verificar se o usuário possui permissão para esta consulta ou não.
     * @return  List Contendo vários objetos da classe <code>PlanoModalidadeVO</code> resultantes da consulta.
     * @exception Exception Caso haja problemas de conexão ou restrição de acesso.
     */
    public List consultarPorCodigo(Integer valorConsulta, boolean controlarAcesso, int nivelMontarDados) throws Exception {
        consultar(getIdEntidade(), controlarAcesso);
        String sqlStr = "SELECT * FROM PlanoTextoPadraoTag WHERE codigo >= " + valorConsulta.intValue() + " ORDER BY codigo";
        Statement stm = con.createStatement();
        ResultSet tabelaResultado = stm.executeQuery(sqlStr);
        return (montarDadosConsulta(tabelaResultado, nivelMontarDados));
    }

    /**
     * Responsável por montar os dados de vários objetos, resultantes de uma consulta ao banco de dados (<code>ResultSet</code>).
     * Faz uso da operação <code>montarDados</code> que realiza o trabalho para um objeto por vez.
     * @return  List Contendo vários objetos da classe <code>PlanoModalidadeVO</code> resultantes da consulta.
     */
    public static List montarDadosConsulta(ResultSet tabelaResultado, int nivelMontarDados) throws Exception {
        List vetResultado = new ArrayList();
        while (tabelaResultado.next()) {
            PlanoTextoPadraoTagVO obj = new PlanoTextoPadraoTagVO();
            obj = montarDados(tabelaResultado, nivelMontarDados);
            vetResultado.add(obj);
        }
        return vetResultado;
    }

    /**
     * Responsável por montar os dados resultantes de uma consulta ao banco de dados (<code>ResultSet</code>)
     * em um objeto da classe <code>PlanoModalidadeVO</code>.
     * @return  O objeto da classe <code>PlanoModalidadeVO</code> com os dados devidamente montados.
     */
    public static PlanoTextoPadraoTagVO montarDados(ResultSet dadosSQL, int nivelMontarDados) throws Exception {
        PlanoTextoPadraoTagVO obj = new PlanoTextoPadraoTagVO();
        obj.setCodigo(new Integer(dadosSQL.getInt("codigo")));
        obj.setTag(dadosSQL.getString("tag"));
        obj.setPlanoTextoPadrao(new Integer(dadosSQL.getInt("planoTextoPadrao")));
        obj.setNovoObj(new Boolean(false));
        return obj;
    }

    /**
     * Operação responsável por excluir todos os objetos da <code>PlanoModalidadeVO</code> no BD.
     * Faz uso da operação <code>excluir</code> disponível na classe <code>PlanoModalidade</code>.
     * @param <code>plano</code> campo chave para exclusão dos objetos no BD.
     * @exception Exception  Erro de conexão com o BD ou restrição de acesso a esta operação.
     */
    public void excluirPlanoTextoPadraoTag(Integer plano) throws Exception {
        excluir(getIdEntidade());
        String sql = "DELETE FROM PlanoTextoPadraoTag WHERE (planoTextoPadrao = ?)";
        PreparedStatement sqlExcluir = con.prepareStatement(sql);
        sqlExcluir.setInt(1, plano.intValue());
        sqlExcluir.execute();
    }

    /**
     * Operação responsável por alterar todos os objetos da <code>PlanoModalidadeVO</code> contidos em um Hashtable no BD.
     * Faz uso da operação <code>excluirPlanoModalidades</code> e <code>incluirPlanoModalidades</code> disponíveis na classe <code>PlanoModalidade</code>.
     * @param objetos  List com os objetos a serem alterados ou incluídos no BD.
     * @exception Exception  Erro de conexão com o BD ou restrição de acesso a esta operação.
     */
    public void alterarPlanoTextoPadraoTag(Integer planoTextoPadrao, List objetos) throws Exception {
//        Iterator i = objetos.iterator();
//        while (i.hasNext()) {
//            PlanoTextoPadraoTagVO obj = (PlanoTextoPadraoTagVO) i.next();            
//            obj.setPlanoTextoPadrao(planoTextoPadrao);
//            alterar(obj);
//        }
        excluirPlanoTextoPadraoTag(planoTextoPadrao);
        incluirPlanoTextoPadraoTag(planoTextoPadrao, objetos);
    }

    /**
     * Operação responsável por incluir objetos da <code>PlanoModalidadeVO</code> no BD.
     * Garantindo o relacionamento com a entidade principal <code>plano.Plano</code> através do atributo de vínculo.
     * @param objetos List contendo os objetos a serem gravados no BD da classe.
     * @exception Exception  Erro de conexão com o BD ou restrição de acesso a esta operação.
     */
    public void incluirPlanoTextoPadraoTag(Integer planoPrm, List objetos) throws Exception {
        Iterator e = objetos.iterator();
        while (e.hasNext()) {
            PlanoTextoPadraoTagVO obj = (PlanoTextoPadraoTagVO) e.next();
            obj.setPlanoTextoPadrao(planoPrm);
            incluir(obj);
        }
    }

    /**
     * Operação responsável por consultar todos os <code>PlanoModalidadeVO</code> relacionados a um objeto da classe <code>plano.Plano</code>.
     * @param plano  Atributo de <code>plano.Plano</code> a ser utilizado para localizar os objetos da classe <code>PlanoModalidadeVO</code>.
     * @return List  Contendo todos os objetos da classe <code>PlanoModalidadeVO</code> resultantes da consulta.
     * @exception Exception  Erro de conexão com o BD ou restrição de acesso a esta operação.
     */
    public List consultarPlanoTextoPadraoTag(Integer planoTextoPadrao, int nivelMontarDados) throws Exception {
        consultar(getIdEntidade());
        List objetos = new ArrayList();
        String sql = "SELECT * FROM PlanoTextoPadraoTag WHERE planoTextoPadrao = ?";
        PreparedStatement sqlConsulta = con.prepareStatement(sql);
        sqlConsulta.setInt(1, planoTextoPadrao.intValue());
        ResultSet resultado = sqlConsulta.executeQuery();
        while (resultado.next()) {
            PlanoTextoPadraoTagVO novoObj = new PlanoTextoPadraoTagVO();
            novoObj = PlanoTextoPadraoTag.montarDados(resultado, nivelMontarDados);
            objetos.add(novoObj);
        }
        return objetos;
    }

    /**
     * Operação responsável por localizar um objeto da classe <code>PlanoModalidadeVO</code>
     * através de sua chave primária. 
     * @exception Exception Caso haja problemas de conexão ou localização do objeto procurado.
     */
    public PlanoTextoPadraoTagVO consultarPorChavePrimaria(Integer codigoPrm, int nivelMontarDados) throws Exception {
        consultar(getIdEntidade(), false);
        String sql = "SELECT * FROM PlanoTextoPadraoTag WHERE codigo = ?";
        PreparedStatement sqlConsultar = con.prepareStatement(sql);
        sqlConsultar.setInt(1, codigoPrm.intValue());
        ResultSet tabelaResultado = sqlConsultar.executeQuery();
        if (!tabelaResultado.next()) {
            throw new ConsistirException("Dados Não Encontrados ( PlanoModalidade ).");
        }
        return (montarDados(tabelaResultado, nivelMontarDados));
    }
}

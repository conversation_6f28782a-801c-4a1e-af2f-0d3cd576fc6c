package negocio.facade.jdbc.plano;

import negocio.comuns.plano.ProdutoRedeEmpresaVO;
import negocio.comuns.utilitarias.Uteis;
import negocio.facade.jdbc.arquitetura.SuperEntidade;

import java.sql.*;

public class ProdutoRedeEmpresa extends SuperEntidade {

    public ProdutoRedeEmpresa(Connection conexao) throws Exception {
        super(conexao);
    }

    public ProdutoRedeEmpresa() throws Exception {
        super();
    }

    public void inserir(ProdutoRedeEmpresaVO produtoRedeEmpresaVO) throws SQLException {
        String sql = "INSERT INTO ProdutoRedeEmpresa " +
                "(produto, chave, datacadastro, produtoReplicado, nomeUnidade, mensagemSituacao, codigoEmpresaDestino) " +
                "VALUES (?,?,?,?,?,?,?)";

        try (PreparedStatement preparedStatement = con.prepareStatement(sql)) {
            int i = 1;
            preparedStatement.setInt(i++, produtoRedeEmpresaVO.getProduto());
            preparedStatement.setString(i++, produtoRedeEmpresaVO.getChave());
            preparedStatement.setTimestamp(i++, Uteis.getTimestamp());
            if (produtoRedeEmpresaVO.getProdutoReplicado() != null) {
                preparedStatement.setInt(i++, produtoRedeEmpresaVO.getProdutoReplicado());
            } else {
                preparedStatement.setNull(i++, Types.INTEGER);
            }
            preparedStatement.setString(i++, produtoRedeEmpresaVO.getNomeUnidade());
            preparedStatement.setString(i++, produtoRedeEmpresaVO.getMensagemSituacao());
            preparedStatement.setInt(i++, produtoRedeEmpresaVO.getCodigoEmpresaDestino());
            preparedStatement.execute();
        }
    }

    public void alterarMensagemSituacao(Integer produto, String chave, String mensagemSituacao, Integer codigoEmpresaDestino) throws SQLException {
        String sql = "UPDATE ProdutoRedeEmpresa SET " +
                "mensagemSituacao = ? " +
                "WHERE produto = ? AND chave = ? AND codigoEmpresaDestino = ?";

        try (PreparedStatement preparedStatement = con.prepareStatement(sql)) {
            int i = 1;
            preparedStatement.setString(i++, mensagemSituacao);
            preparedStatement.setInt(i++, produto);
            preparedStatement.setString(i++, chave);
            preparedStatement.setInt(i++, codigoEmpresaDestino);
            preparedStatement.execute();
        }
    }

    public void alterarDataAtualizacao(Integer produto, String chave, Integer codigoEmpresaDestino) throws SQLException {
        String sql = "UPDATE ProdutoRedeEmpresa SET " +
                "dataatualizacao = ? " +
                "WHERE produto = ? AND chave = ? AND codigoEmpresaDestino = ?";

        try (PreparedStatement preparedStatement = con.prepareStatement(sql)) {
            int i = 1;
            preparedStatement.setTimestamp(i++, Uteis.getTimestamp());
            preparedStatement.setInt(i++, produto);
            preparedStatement.setString(i++, chave);
            preparedStatement.setInt(i++, codigoEmpresaDestino);
            preparedStatement.execute();
        }
    }

    public void alterarDataAtualizacao(Integer produto, String chave, Integer novoProduto, String mensagemSituacao, Integer codigoEmpresaDestino) throws SQLException {
        String sql = "UPDATE ProdutoRedeEmpresa SET " +
                "dataatualizacao = ?, produtoReplicado = ?, mensagemSituacao = ? " +
                "WHERE produto = ? AND chave = ? AND codigoEmpresaDestino = ?";

        try (PreparedStatement preparedStatement = con.prepareStatement(sql)) {
            int i = 1;
            preparedStatement.setTimestamp(i++, Uteis.getTimestamp());
            preparedStatement.setInt(i++, novoProduto);
            preparedStatement.setString(i++, mensagemSituacao);
            preparedStatement.setInt(i++, produto);
            preparedStatement.setString(i++, chave);
            preparedStatement.setInt(i++, codigoEmpresaDestino);
            preparedStatement.execute();
        }
    }

    public void limparDataAtualizacao(Integer produto, String chaveDestino, Integer codigoEmpresaDestino) throws SQLException {
        String sql = "UPDATE ProdutoRedeEmpresa set " +
                "dataatualizacao = ? " +
                "WHERE produto = ? AND chave = ? AND codigoEmpresaDestino = ?";

        try (PreparedStatement preparedStatement = con.prepareStatement(sql)) {
            int i = 1;
            preparedStatement.setTimestamp(i++, null);
            preparedStatement.setInt(i++, produto);
            preparedStatement.setString(i++, chaveDestino);
            preparedStatement.setInt(i++, codigoEmpresaDestino);
            preparedStatement.execute();
        }
    }


    public ProdutoRedeEmpresaVO consultarPorChaveProdutoECodigoEmpresaDestino(String chave, Integer produto, Integer codigoEmpresaDestino) throws SQLException {
        String sql = "SELECT * FROM ProdutoRedeEmpresa " +
                "WHERE chave = ? AND produto = ? AND codigoEmpresaDestino = ?";

        try (PreparedStatement preparedStatement = con.prepareStatement(sql)) {
            int i = 1;
            preparedStatement.setString(i++, chave);
            preparedStatement.setInt(i++, produto);
            preparedStatement.setInt(i++, codigoEmpresaDestino);
            try (ResultSet resultSet = preparedStatement.executeQuery()) {
                if (resultSet.next()) {
                    return montarDados(resultSet);
                }
            }
        }
        return null;
    }

    public ProdutoRedeEmpresaVO montarDados(ResultSet resultSet) throws SQLException {
        ProdutoRedeEmpresaVO produtoRedeEmpresaVO = new ProdutoRedeEmpresaVO();
        produtoRedeEmpresaVO.setChave(resultSet.getString("chave"));
        produtoRedeEmpresaVO.setCodigo(resultSet.getInt("codigo"));
        produtoRedeEmpresaVO.setProduto(resultSet.getInt("produto"));
        produtoRedeEmpresaVO.setDataAtualizacao(resultSet.getTimestamp("dataatualizacao"));
        produtoRedeEmpresaVO.setProdutoReplicado(resultSet.getInt("produtoReplicado"));
        produtoRedeEmpresaVO.setNomeUnidade(resultSet.getString("nomeUnidade"));
        produtoRedeEmpresaVO.setMensagemSituacao(resultSet.getString("mensagemSituacao"));
        produtoRedeEmpresaVO.setCodigoEmpresaDestino(resultSet.getInt("codigoEmpresaDestino"));

        return produtoRedeEmpresaVO;
    }
}

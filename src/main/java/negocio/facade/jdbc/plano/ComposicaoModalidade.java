package negocio.facade.jdbc.plano;

import negocio.comuns.plano.ComposicaoModalidadeVO;
import negocio.comuns.plano.ModalidadeVO;
import negocio.comuns.utilitarias.ConsistirException;
import negocio.comuns.utilitarias.Uteis;
import negocio.facade.jdbc.arquitetura.SuperEntidade;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.Statement;
import java.util.ArrayList;
import java.util.List;
import negocio.comuns.utilitarias.ColecaoUtils;
import negocio.comuns.utilitarias.UteisValidacao;
import org.apache.commons.collections.Predicate;

/**
 * Classe de persistência que encapsula todas as operações de manipulação dos
 * dados da classe <code>ComposicaoModalidadeVO</code>. Responsável por
 * implementar operações como incluir, alterar, excluir e consultar pertinentes
 * a classe <code>ComposicaoModalidadeVO</code>. Encapsula toda a interação com
 * o banco de dados.
 *
 * @see ComposicaoModalidadeVO
 * @see SuperEntidade
 * @see Composicao
 */
public class ComposicaoModalidade extends SuperEntidade {

    public ComposicaoModalidade() throws Exception {
        super();
        setIdEntidade("Composicao");
    }

    public ComposicaoModalidade(Connection con) throws Exception {
        super(con);
        setIdEntidade("Composicao");

    }

    /**
     * Responsável por montar os dados de vários objetos, resultantes de uma
     * consulta ao banco de dados ( <code>ResultSet</code>). Faz uso da operação
     * <code>montarDados</code> que realiza o trabalho para um objeto por vez.
     *
     * @return List Contendo vários objetos da * classe
     * <code>ComposicaoModalidadeVO</code> resultantes da consulta.
     */
    public static List<ComposicaoModalidadeVO> montarDadosConsulta(ResultSet tabelaResultado, int nivelMontarDados, Connection con) throws Exception {
        List<ComposicaoModalidadeVO> vetResultado = new ArrayList<ComposicaoModalidadeVO>();
        while (tabelaResultado.next()) {
            ComposicaoModalidadeVO obj = montarDados(tabelaResultado, nivelMontarDados, con);
            vetResultado.add(obj);
        }
        return vetResultado;
    }

    /**
     * Responsável por montar os dados resultantes de uma consulta ao banco de
     * dados ( <code>ResultSet</code>) em um objeto da classe
     * <code>ComposicaoModalidadeVO</code>.
     *
     * @return O objeto da classe <code>ComposicaoModalidadeVO</code> com os
     * dados devidamente montados.
     */
    public static ComposicaoModalidadeVO montarDados(ResultSet dadosSQL, int nivelMontarDados, Connection con) throws Exception {
        ComposicaoModalidadeVO obj = new ComposicaoModalidadeVO();
        obj.setCodigo(dadosSQL.getInt("codigo"));
        obj.getModalidade().setCodigo(dadosSQL.getInt("modalidade"));
        obj.setComposicao(dadosSQL.getInt("composicao"));
        obj.setPrecoModalidade(dadosSQL.getDouble("precoModalidade"));
        obj.setValorMensalComposicao(dadosSQL.getDouble("valorMensalComposicao"));
        obj.setNrVezes(dadosSQL.getInt("nrvezes"));
        obj.setNovoObj(false);
        if (nivelMontarDados == Uteis.NIVELMONTARDADOS_DADOSBASICOS) {
            return obj;
        }

        montarDadosModalidade(obj, nivelMontarDados, con);
        return obj;
    }

    /**
     * Operação responsável por montar os dados de um objeto da classe
     * <code>ModalidadeVO</code> relacionado ao objeto
     * <code>ComposicaoModalidadeVO</code>. Faz uso da chave primária da classe
     * <code>ModalidadeVO</code> para realizar a consulta.
     *
     * @param obj Objeto no qual será montado os dados consultados.
     */
    public static void montarDadosModalidade(ComposicaoModalidadeVO obj, int nivelMontarDados, Connection con) throws Exception {
        if (obj.getModalidade().getCodigo() == 0) {
            obj.setModalidade(new ModalidadeVO());
            return;
        }
        Modalidade modalidade = new Modalidade(con);
        obj.setModalidade(modalidade.consultarPorChavePrimaria(obj.getModalidade().getCodigo(), nivelMontarDados));
        modalidade = null;
    }

    /**
     * Operação responsável por retornar um novo objeto da classe
     * <code>ComposicaoModalidadeVO</code>.
     */
    public ComposicaoModalidadeVO novo() throws Exception {
        incluir(getIdEntidade());
        return new ComposicaoModalidadeVO();
    }

    /**
     * Operação responsável por incluir no banco de dados um objeto da classe
     * <code>ComposicaoModalidadeVO</code>. Primeiramente valida os dados (
     * <code>validarDados</code>) do objeto. Verifica a conexão com o banco de
     * dados e a permissão do usuário para realizar esta operacão na entidade.
     * Isto, através da operação <code>incluir</code> da superclasse.
     *
     * @param obj Objeto da classe <code>ComposicaoModalidadeVO</code> que será
     * gravado no banco de dados.
     * @throws Exception Caso haja problemas de conexão, restrição de acesso ou
     * validação de dados.
     */
    public void incluir(ComposicaoModalidadeVO obj, boolean modalidadesEscolhidas) throws Exception {
        ComposicaoModalidadeVO.validarDados(obj, modalidadesEscolhidas);
        incluir(getIdEntidade());
        obj.realizarUpperCaseDados();
        String sql = "INSERT INTO ComposicaoModalidade( modalidade, composicao, precoModalidade, valorMensalComposicao, nrvezes) VALUES ( ?, ?, ?, ?, ?)";
        PreparedStatement sqlInserir = con.prepareStatement(sql);
        int i = 1;
        if (obj.getModalidade().getCodigo() != 0) {
            sqlInserir.setInt(i++, obj.getModalidade().getCodigo());
        } else {
            sqlInserir.setNull(i++, 0);
        }
        if (obj.getComposicao() != 0) {
            sqlInserir.setInt(i++, obj.getComposicao());
        } else {
            sqlInserir.setNull(i++, 0);
        }
        sqlInserir.setDouble(i++, obj.getPrecoModalidade());
        sqlInserir.setDouble(i++, obj.getValorMensalComposicao());
        sqlInserir.setInt(i++, obj.getNrVezes());
        sqlInserir.execute();
        obj.setCodigo(obterValorChavePrimariaCodigo());
        obj.setNovoObj(false);
    }

    /**
     * Operação responsável por alterar no BD os dados de um objeto da classe
     * <code>ComposicaoModalidadeVO</code>. Sempre utiliza a chave primária da
     * classe como atributo para localização do registro a ser alterado.
     * Primeiramente valida os dados ( <code>validarDados</code>) do objeto.
     * Verifica a conexão com o banco de dados e a permissão do usuário para
     * realizar esta operacão na entidade. Isto, através da operação
     * <code>alterar</code> da superclasse.
     *
     * @param obj Objeto da classe <code>ComposicaoModalidadeVO</code> que será
     * alterada no banco de dados.
     * @throws Exception Caso haja problemas de conexão, restrição de acesso ou
     * validação de dados.
     */
    public void alterar(ComposicaoModalidadeVO obj, boolean modalidadesEscolhidas) throws Exception {
        ComposicaoModalidadeVO.validarDados(obj, modalidadesEscolhidas);
        alterar(getIdEntidade());
        obj.realizarUpperCaseDados();
        String sql = "UPDATE ComposicaoModalidade SET modalidade=?, composicao=?, precoModalidade=?, valorMensalComposicao=?, nrvezes=? WHERE codigo = ?";
        PreparedStatement sqlAlterar = con.prepareStatement(sql);
        int i = 1;
        if (obj.getModalidade().getCodigo() != 0) {
            sqlAlterar.setInt(i++, obj.getModalidade().getCodigo());
        } else {
            sqlAlterar.setNull(i++, 0);
        }
        if (obj.getComposicao() != 0) {
            sqlAlterar.setInt(i++, obj.getComposicao());
        } else {
            sqlAlterar.setNull(i++, 0);
        }
        sqlAlterar.setDouble(i++, obj.getPrecoModalidade());
        sqlAlterar.setDouble(i++, obj.getValorMensalComposicao());
        sqlAlterar.setInt(i++, obj.getNrVezes());
        sqlAlterar.setInt(i++, obj.getCodigo());
        sqlAlterar.execute();
    }

    /**
     * Operação responsável por excluir no BD um objeto da classe
     * <code>ComposicaoModalidadeVO</code>. Sempre localiza o registro a ser
     * excluído através da chave primária da entidade. Primeiramente verifica a
     * conexão com o banco de dados e a permissão do usuário para realizar esta
     * operacão na entidade. Isto, através da operação <code>excluir</code> da
     * superclasse.
     *
     * @param obj Objeto da classe <code>ComposicaoModalidadeVO</code> que será
     * removido no banco de dados.
     * @throws Exception Caso haja problemas de conexão ou restrição de acesso.
     */
    public void excluir(ComposicaoModalidadeVO obj) throws Exception {
        excluir(getIdEntidade());
        String sql = "DELETE FROM ComposicaoModalidade WHERE ((codigo = ?))";
        PreparedStatement sqlExcluir = con.prepareStatement(sql);
        sqlExcluir.setInt(1, obj.getCodigo());
        sqlExcluir.execute();
    }

    /**
     * Responsável por realizar uma consulta de
     * <code>ComposicaoModalidade</code> através do valor do atributo
     * <code>descricao</code> da classe <code>Composicao</code> Faz uso da
     * operação <code>montarDadosConsulta</code> que realiza o trabalho de
     * prerarar o List resultante.
     *
     * @return List Contendo vários objetos da * classe
     * <code>ComposicaoModalidadeVO</code> resultantes da consulta.
     * @throws Exception Caso haja problemas de conexão ou restrição de acesso.
     */
    public List consultarPorDescricaoComposicao(String valorConsulta, int nivelMontarDados) throws Exception {
        consultar(getIdEntidade(), true);
        String sqlStr = "SELECT ComposicaoModalidade.* FROM ComposicaoModalidade, Composicao WHERE ComposicaoModalidade.composicao = Composicao.codigo and upper( Composicao.descricao ) like('" + valorConsulta.toUpperCase() + "%') ORDER BY Composicao.descricao";
        Statement stm = con.createStatement();
        ResultSet tabelaResultado = stm.executeQuery(sqlStr);
        return montarDadosConsulta(tabelaResultado, nivelMontarDados, this.con);
    }

    public boolean consultarPorCodigoModalidadeComposicao(int modalidade, int composicao) throws Exception {
        consultar(getIdEntidade(), true);
        String sqlStr = "SELECT ComposicaoModalidade.* FROM ComposicaoModalidade, Composicao "
                + "WHERE ComposicaoModalidade.composicao = Composicao.codigo "
                + "and composicao.codigo = " + composicao + " "
                + "and composicaoModalidade.modalidade = " + modalidade + " ORDER BY Composicao.descricao";
        Statement stm = con.createStatement();
        ResultSet tabelaResultado = stm.executeQuery(sqlStr);
        return tabelaResultado.next();
    }

    /**
     * Responsável por realizar uma consulta de
     * <code>ComposicaoModalidade</code> através do valor do atributo
     * <code>nome</code> da classe <code>Modalidade</code> Faz uso da operação
     * <code>montarDadosConsulta</code> que realiza o trabalho de prerarar o
     * List resultante.
     *
     * @return List Contendo vários objetos da * classe
     * <code>ComposicaoModalidadeVO</code> resultantes da consulta.
     * @throws Exception Caso haja problemas de conexão ou restrição de acesso.
     */
    public List consultarPorNomeModalidade(String valorConsulta, int nivelMontarDados) throws Exception {
        consultar(getIdEntidade(), true);
        String sqlStr = "SELECT ComposicaoModalidade.* FROM ComposicaoModalidade, Modalidade WHERE ComposicaoModalidade.modalidade = Modalidade.codigo and upper( Modalidade.nome ) like('" + valorConsulta.toUpperCase() + "%') ORDER BY Modalidade.nome";
        Statement stm = con.createStatement();
        ResultSet tabelaResultado = stm.executeQuery(sqlStr);
        return montarDadosConsulta(tabelaResultado, nivelMontarDados, this.con);
    }

    /**
     * Responsável por realizar uma consulta de
     * <code>ComposicaoModalidade</code> através do valor do atributo
     * <code>Integer codigo</code>. Retorna os objetos com valores iguais ou
     * superiores ao parâmetro fornecido. Faz uso da operação
     * <code>montarDadosConsulta</code> que realiza o trabalho de prerarar o
     * List resultante.
     *
     * @param controlarAcesso Indica se a aplicação deverá verificar se o
     * usuário possui permissão para esta consulta ou não.
     * @return List Contendo vários objetos da * classe
     * <code>ComposicaoModalidadeVO</code> resultantes da consulta.
     * @throws Exception Caso haja problemas de conexão ou restrição de acesso.
     */
    public List consultarPorCodigo(Integer valorConsulta, boolean controlarAcesso, int nivelMontarDados) throws Exception {
        consultar(getIdEntidade(), controlarAcesso);
        String sqlStr = "SELECT * FROM ComposicaoModalidade WHERE codigo >= " + valorConsulta + " ORDER BY codigo";
        Statement stm = con.createStatement();
        ResultSet tabelaResultado = stm.executeQuery(sqlStr);
        return (montarDadosConsulta(tabelaResultado, nivelMontarDados, this.con));
    }

    /**
     * Operação responsável por excluir todos os objetos da
     * <code>ComposicaoModalidadeVO</code> no BD. Faz uso da operação
     * <code>excluir</code> disponível na classe
     * <code>ComposicaoModalidade</code>.
     *
     * @param composicao campo chave para exclusão dos objetos no BD.
     * @throws Exception Erro de conexão com o BD ou restrição de acesso a esta
     * operação.
     */
    public void excluirComposicaoModalidades(Integer composicao) throws Exception {
        excluir(getIdEntidade());
        String sql = "DELETE FROM ComposicaoModalidade WHERE (composicao = ?)";
        PreparedStatement sqlExcluir = con.prepareStatement(sql);
        sqlExcluir.setInt(1, composicao);
        sqlExcluir.execute();
    }

    /**
     * Operação responsável por alterar todos os objetos da
     * <code>ComposicaoModalidadeVO</code> contidos em um Hashtable no BD. Faz
     * uso da operação <code>excluirComposicaoModalidades</code> e
     * <code>incluirComposicaoModalidades</code> disponíveis na classe
     * <code>ComposicaoModalidade</code>.
     *
     * @param objetos List com os objetos a serem alterados ou incluídos no BD.
     * @throws Exception Erro de conexão com o BD ou restrição de acesso a esta
     * operação.
     */
    public void alterarComposicaoModalidades(Integer composicao, List<ComposicaoModalidadeVO> objetos, boolean modalidadesEscolhidas) throws Exception {

        String codigoComposicoes = "";
        for (ComposicaoModalidadeVO modalidade : objetos) {
            final int codModalidade = modalidade.getModalidade().getCodigo();
            if (!ColecaoUtils.exists(objetos, new Predicate() {
                @Override
                public boolean evaluate(Object o) {
                    ComposicaoModalidadeVO existente = (ComposicaoModalidadeVO) o;
                    return existente.getModalidade().getCodigo().equals(codModalidade)
                            && UteisValidacao.emptyNumber(existente.getNrVezes());
                }
            })) {
                throw new ConsistirException(
                        String.format("Para a modalidade %s não foi definido nenhum registro como Referência. "
                                + "Faça isso deixando o campo \"Vezes por Semana no Pacote\" desta modalidade vazio", modalidade.getModalidade().getNome()));
            }
            if (UteisValidacao.emptyNumber(modalidade.getCodigo())) {
                modalidade.setComposicao(composicao);
                incluir(modalidade,modalidadesEscolhidas);
            } else {
                alterar(modalidade, modalidadesEscolhidas);
            }
            codigoComposicoes += "," + modalidade.getCodigo();
        }
        
        excluirComposicaoModalidadesEdicao(composicao, codigoComposicoes.replaceFirst(",", ""));
    }

    /**
     * Operação responsável por incluir objetos da
     * <code>ComposicaoModalidadeVO</code> no BD. Garantindo o relacionamento
     * com a entidade principal <code>plano.Composicao</code> através do
     * atributo de vínculo.
     *
     * @param objetos List contendo os objetos a serem gravados no BD da classe.
     * @throws Exception Erro de conexão com o BD ou restrição de acesso a esta
     * operação.
     */
    public void incluirComposicaoModalidades(Integer composicaoPrm, List objetos, boolean modalidadesEscolhidas) throws Exception {
        for (Object objeto : objetos) {
            ComposicaoModalidadeVO obj = (ComposicaoModalidadeVO) objeto;
            final int codModalidade = obj.getModalidade().getCodigo();
            if (!ColecaoUtils.exists(objetos, new Predicate() {
                @Override
                public boolean evaluate(Object o) {
                    ComposicaoModalidadeVO existente = (ComposicaoModalidadeVO) o;
                    return existente.getModalidade().getCodigo().equals(codModalidade)
                            && UteisValidacao.emptyNumber(existente.getNrVezes());
                }
            })) {
                throw new ConsistirException(
                        String.format("Para a modalidade %s não foi definido nenhum registro como Referência. "
                                + "Faça isso deixando o campo \"Vezes por Semana no Pacote\" desta modalidade vazio", obj.getModalidade().getNome()));
            }
            obj.setComposicao(composicaoPrm);
            incluir(obj, modalidadesEscolhidas);
        }
    }

    /**
     * Operação responsável por consultar todos os
     * <code>ComposicaoModalidadeVO</code> relacionados a um objeto da classe
     * <code>plano.Composicao</code>.
     *
     * @param composicao Atributo de <code>plano.Composicao</code> a ser
     * utilizado para localizar os objetos da * classe
     * <code>ComposicaoModalidadeVO</code>.
     * @return List Contendo todos os objetos da * classe
     * <code>ComposicaoModalidadeVO</code> resultantes da consulta.
     * @throws Exception Erro de conexão com o BD ou restrição de acesso a esta
     * operação.
     */
    public List<ComposicaoModalidadeVO> consultarComposicaoModalidades(Integer composicao, int nivelMontarDados) throws Exception {
        consultar(getIdEntidade());
        List<ComposicaoModalidadeVO> objetos = new ArrayList<ComposicaoModalidadeVO>();
        String sql = "SELECT * FROM ComposicaoModalidade WHERE composicao = ?";
        PreparedStatement sqlConsulta = con.prepareStatement(sql);
        sqlConsulta.setInt(1, composicao);
        ResultSet resultado = sqlConsulta.executeQuery();
        while (resultado.next()) {
            ComposicaoModalidadeVO novoObj = ComposicaoModalidade.montarDados(resultado, nivelMontarDados, this.con);
            objetos.add(novoObj);
        }
        return objetos;
    }

    /**
     * Operação responsável por localizar um objeto da classe
     * <code>ComposicaoModalidadeVO</code> através de sua chave primária.
     *
     * @throws Exception Caso haja problemas de conexão ou localização do objeto
     * procurado.
     */
    public ComposicaoModalidadeVO consultarPorChavePrimaria(Integer codigoPrm, int nivelMontarDados) throws Exception {
        consultar(getIdEntidade(), false);
        String sql = "SELECT * FROM ComposicaoModalidade WHERE codigo = ?";
        PreparedStatement sqlConsultar = con.prepareStatement(sql);
        sqlConsultar.setInt(1, codigoPrm);
        ResultSet tabelaResultado = sqlConsultar.executeQuery();
        if (!tabelaResultado.next()) {
            throw new ConsistirException("Dados Não Encontrados ( ComposicaoModalidade ).");
        }
        return (montarDados(tabelaResultado, nivelMontarDados, this.con));
    }
    
    public void excluirComposicaoModalidadesEdicao(final Integer composicao, final String codigosAtuais) throws Exception {
        excluir(getIdEntidade());
        String sql = "DELETE FROM ComposicaoModalidade WHERE (composicao = ?)";
        if(!UteisValidacao.emptyString(codigosAtuais)){        
                sql += " and codigo not in ("+ codigosAtuais + ")";
        }
        PreparedStatement sqlExcluir = con.prepareStatement(sql);
        sqlExcluir.setInt(1, composicao);
        sqlExcluir.execute();
    }
}

package negocio.facade.jdbc.plano;

import java.sql.SQLException;
import negocio.interfaces.plano.*;
import negocio.comuns.plano.DescontoVO;
import negocio.facade.jdbc.arquitetura.*;

import java.sql.Connection;
import java.sql.ResultSet;
import java.sql.PreparedStatement;
import java.sql.Statement;
import java.util.List;
import java.util.ArrayList;
import java.util.Collections;
import negocio.comuns.plano.DescontoRenovacaoVO;
import negocio.comuns.plano.enumerador.TipoDesconto;
import negocio.comuns.utilitarias.*;

/**
 * Classe de persistência que encapsula todas as operações de manipulação dos dados da classe <code>DescontoVO</code>.
 * Responsável por implementar operações como incluir, alterar, excluir e consultar pertinentes a classe <code>DescontoVO</code>.
 * Encapsula toda a interação com o banco de dados.
 * @see DescontoVO
 * @see SuperEntidade
 */
public class Desconto extends SuperEntidade implements DescontoInterfaceFacade {

    public Desconto() throws Exception {
        super();
    }

    public Desconto(Connection con) throws Exception {
        super(con);
    }

    /**
     * Operação responsável por retornar um novo objeto da classe <code>DescontoVO</code>.
     */
    public DescontoVO novo() throws Exception {
        incluir(getIdEntidade());
        DescontoVO obj = new DescontoVO();
        return obj;
    }

    /**
     * Operação responsável por incluir no banco de dados um objeto da classe <code>DescontoVO</code>.
     * Primeiramente valida os dados (<code>validarDados</code>) do objeto. Verifica a conexão com o banco de dados e a permissão do usuário
     * para realizar esta operacão na entidade.
     * Isto, através da operação <code>incluir</code> da superclasse.
     * @param obj  Objeto da classe <code>DescontoVO</code> que será gravado no banco de dados.
     * @exception Exception Caso haja problemas de conexão, restrição de acesso ou validação de dados.
     */
    public void incluir(DescontoVO obj, boolean centralEventos) throws Exception {
        try {
            DescontoVO.validarDados(obj);
            if (centralEventos) {
//            	super.incluirObj(this.getIdEntidade());
            } else {
                incluir(this.getIdEntidade());
            }
            obj.realizarUpperCaseDados();
            String sql = "INSERT INTO Desconto( descricao, valor, tipoDesconto, tipoProduto, ativo, aplicarEmpresas ) VALUES ( ?, ?, ?, ?, ?, ? )";
            PreparedStatement sqlInserir = con.prepareStatement(sql);
            sqlInserir.setString(1, obj.getDescricao());
            sqlInserir.setDouble(2, obj.getValor().doubleValue());
            sqlInserir.setString(3, obj.getTipoDesconto().name());
            sqlInserir.setString(4, obj.getTipoProduto());
            sqlInserir.setBoolean(5, obj.getAtivo());
            sqlInserir.setBoolean(6, obj.isAplicarEmpresas());
            sqlInserir.execute();
            obj.setCodigo(obterValorChavePrimariaCodigo());
            incluirIntervalos(obj);
            getFacade().getDescontoEmpresa().incluirEmpresas(obj);
            obj.setNovoObj(false);
        } catch (Exception e) {
            obj.setNovoObj(true);
            throw e;
        }
    }

    public void incluirIntervalos(DescontoVO desconto) throws Exception {
        for (DescontoRenovacaoVO intervalo : desconto.getListaIntervalos()) {
            intervalo.setDesconto(desconto.getCodigo());
            getFacade().getDescontoRenovacao().incluirSemCommit(intervalo);
        }
    }

    /**
     * Operação responsável por alterar no BD os dados de um objeto da classe <code>DescontoVO</code>.
     * Sempre utiliza a chave primária da classe como atributo para localização do registro a ser alterado.
     * Primeiramente valida os dados (<code>validarDados</code>) do objeto. Verifica a conexão com o banco de dados e a permissão do usuário
     * para realizar esta operacão na entidade.
     * Isto, através da operação <code>alterar</code> da superclasse.
     * @param obj    Objeto da classe <code>DescontoVO</code> que será alterada no banco de dados.
     * @exception \Execption Caso haja problemas de conexão, restrição de acesso ou validação de dados.
     */
    public void alterar(DescontoVO obj, boolean centralEventos) throws Exception {
        try {
            con.setAutoCommit(false);
            alterarSemCommit(obj);
            con.commit();
        } catch (Exception e) {
            con.rollback();
            throw e;
        } finally {
            con.setAutoCommit(true);
            }
    }

    public void alterarSemCommit(DescontoVO obj) throws Exception {
        DescontoVO.validarDados(obj);
            obj.realizarUpperCaseDados();
            String sql = "UPDATE Desconto set descricao=?, valor=?, tipoDesconto=?, tipoProduto=?, ativo=?, aplicarEmpresas=? WHERE ((codigo = ?))";
            PreparedStatement sqlAlterar = con.prepareStatement(sql);
            sqlAlterar.setString(1, obj.getDescricao());
            sqlAlterar.setDouble(2, obj.getValor().doubleValue());
            sqlAlterar.setString(3, obj.getTipoDesconto().name());
            sqlAlterar.setString(4, obj.getTipoProduto());
            sqlAlterar.setBoolean(5, obj.getAtivo());
            sqlAlterar.setBoolean(6, obj.isAplicarEmpresas());
            sqlAlterar.setInt(7, obj.getCodigo().intValue());
            sqlAlterar.execute();
            alterarIntervalos(obj);
            getFacade().getDescontoEmpresa().atualizarEmpresas(obj);
        }

    public void alterarIntervalos(DescontoVO desconto) throws Exception {
        // exclui os intervalos existentes
        getFacade().getDescontoRenovacao().excluirSemCommit(desconto.getCodigo());
        // adiciona os novos intervalos
        for (DescontoRenovacaoVO intervalo : desconto.getListaIntervalos()) {
            intervalo.setDesconto(desconto.getCodigo());
            getFacade().getDescontoRenovacao().incluirSemCommit(intervalo);
        }
    }

    /**
     * Operação responsável por excluir no BD um objeto da classe <code>DescontoVO</code>.
     * Sempre localiza o registro a ser excluído através da chave primária da entidade.
     * Primeiramente verifica a conexão com o banco de dados e a permissão do usuário
     * para realizar esta operacão na entidade.
     * Isto, através da operação <code>excluir</code> da superclasse.
     * @param obj    Objeto da classe <code>DescontoVO</code> que será removido no banco de dados.
     * @exception \Execption Caso haja problemas de conexão ou restrição de acesso.
     */
    public void excluir(DescontoVO obj, boolean centralEventos) throws Exception {
        try {
            con.setAutoCommit(false);
            if (centralEventos) {
//            	super.excluirObj(getIdEntidade());
            } else {
                excluir(getIdEntidade());
            }
            String sql = "DELETE FROM Desconto WHERE ((codigo = ?))";
            PreparedStatement sqlExcluir = con.prepareStatement(sql);
            sqlExcluir.setInt(1, obj.getCodigo().intValue());
            sqlExcluir.execute();
            con.commit();
        } catch (Exception e) {
            con.rollback();
            throw e;
        } finally {
            con.setAutoCommit(true);
        }
    }

    /**
     * Responsável por realizar uma consulta de <code>Desconto</code> através do valor do atributo
     * <code>String tipoProduto</code>. Retorna os objetos, com início do valor do atributo idêntico ao parâmetro fornecido.
     * Faz uso da operação <code>montarDadosConsulta</code> que realiza o trabalho de prerarar o List resultante.
     * @param   controlarAcesso Indica se a aplicação deverá verificar se o usuário possui permissão para esta consulta ou não.
     * @return  List Contendo vários objetos da classe <code>DescontoVO</code> resultantes da consulta.
     * @exception Exception Caso haja problemas de conexão ou restrição de acesso.
     */
    public List consultarPorTipoProduto(String valorConsulta, boolean controlarAcesso, int nivelMontarDados) throws Exception {
        consultar(getIdEntidade(), controlarAcesso);
        String sqlStr = "SELECT * FROM Desconto WHERE upper( tipoProduto ) like('" + valorConsulta.toUpperCase() + "%') AND ativo = TRUE ORDER BY tipoProduto";
        Statement stm = con.createStatement();
        ResultSet tabelaResultado = stm.executeQuery(sqlStr);
        return (montarDadosConsulta(tabelaResultado, nivelMontarDados, this.con));
    }

    public List consultarPorTipoProdutoPorEmpresa(String tipo, boolean controlarAcesso, int empresa) throws Exception {
        String sql = "SELECT distinct d.* " +
                "FROM Desconto d " +
                "LEFT JOIN descontoempresa de on de.desconto = d.codigo " +
                "WHERE upper( d.tipoProduto ) like('" + tipo.toUpperCase() + "%') AND " +
                "d.ativo = TRUE " +
                "AND ( de.empresa = "+empresa+" OR d.aplicarempresas IS FALSE OR d.aplicarempresas IS NULL) " +
                "ORDER BY d.tipoProduto";

        Statement stm = con.createStatement();
        ResultSet tabelaResultado = stm.executeQuery(sql);
        return (montarDadosConsulta(tabelaResultado, Uteis.NIVELMONTARDADOS_DADOSBASICOS, this.con));
    }

    /**
     * Responsável por realizar uma consulta de <code>Desconto</code> através do valor do atributo
     * <code>String tipoDesconto</code>. Retorna os objetos, com início do valor do atributo idêntico ao parâmetro fornecido.
     * Faz uso da operação <code>montarDadosConsulta</code> que realiza o trabalho de prerarar o List resultante.
     * @param   controlarAcesso Indica se a aplicação deverá verificar se o usuário possui permissão para esta consulta ou não.
     * @return  List Contendo vários objetos da classe <code>DescontoVO</code> resultantes da consulta.
     * @exception Exception Caso haja problemas de conexão ou restrição de acesso.
     */
    public List consultarPorTipoDesconto(String valorConsulta, boolean controlarAcesso, int nivelMontarDados) throws Exception {
        consultar(getIdEntidade(), controlarAcesso);
        String sqlStr = "SELECT * FROM Desconto WHERE upper( tipoDesconto ) like('" + valorConsulta.toUpperCase() + "%') AND ativo = TRUE ORDER BY tipoDesconto";
        Statement stm = con.createStatement();
        ResultSet tabelaResultado = stm.executeQuery(sqlStr);
        return (montarDadosConsulta(tabelaResultado, nivelMontarDados, this.con));
    }

    /**
     * Responsável por realizar uma consulta de <code>Desconto</code> através do valor do atributo
     * <code>Double valor</code>. Retorna os objetos com valores iguais ou superiores ao parâmetro fornecido.
     * Faz uso da operação <code>montarDadosConsulta</code> que realiza o trabalho de prerarar o List resultante.
     * @param   controlarAcesso Indica se a aplicação deverá verificar se o usuário possui permissão para esta consulta ou não.
     * @return  List Contendo vários objetos da classe <code>DescontoVO</code> resultantes da consulta.
     * @exception Exception Caso haja problemas de conexão ou restrição de acesso.
     */
    public List consultarPorValor(Double valorConsulta, boolean controlarAcesso, int nivelMontarDados) throws Exception {
        consultar(getIdEntidade(), controlarAcesso);
        String sqlStr = "SELECT * FROM Desconto WHERE valor >= " + valorConsulta.doubleValue() + " AND ativo = TRUE ORDER BY valor";
        Statement stm = con.createStatement();
        ResultSet tabelaResultado = stm.executeQuery(sqlStr);
        return (montarDadosConsulta(tabelaResultado, nivelMontarDados, this.con));
    }

    /**
     * Responsável por realizar uma consulta de <code>Desconto</code> através do valor do atributo
     * <code>String descricao</code>. Retorna os objetos, com início do valor do atributo idêntico ao parâmetro fornecido.
     * Faz uso da operação <code>montarDadosConsulta</code> que realiza o trabalho de prerarar o List resultante.
     * @param   controlarAcesso Indica se a aplicação deverá verificar se o usuário possui permissão para esta consulta ou não.
     * @return  List Contendo vários objetos da classe <code>DescontoVO</code> resultantes da consulta.
     * @exception Exception Caso haja problemas de conexão ou restrição de acesso.
     */
    public List consultarPorDescricao(String valorConsulta, boolean controlarAcesso, int nivelMontarDados) throws Exception {
        consultar(getIdEntidade(), controlarAcesso);
        String sqlStr = "SELECT * FROM Desconto WHERE upper( descricao ) like('" + valorConsulta.toUpperCase() + "%') AND ativo = TRUE ORDER BY descricao";
        Statement stm = con.createStatement();
        ResultSet tabelaResultado = stm.executeQuery(sqlStr);
        return (montarDadosConsulta(tabelaResultado, nivelMontarDados, this.con));
    }

    /**
     * Responsável por realizar uma consulta de <code>Desconto</code> através do valor do atributo
     * <code>Integer codigo</code>. Retorna os objetos com valores iguais ou superiores ao parâmetro fornecido.
     * Faz uso da operação <code>montarDadosConsulta</code> que realiza o trabalho de prerarar o List resultante.
     * @param   controlarAcesso Indica se a aplicação deverá verificar se o usuário possui permissão para esta consulta ou não.
     * @return  List Contendo vários objetos da classe <code>DescontoVO</code> resultantes da consulta.
     * @exception Exception Caso haja problemas de conexão ou restrição de acesso.
     */
    public List consultarPorCodigo(Integer valorConsulta, boolean controlarAcesso, int nivelMontarDados) throws Exception {
        consultar(getIdEntidade(), controlarAcesso);
        String sqlStr = "SELECT * FROM Desconto WHERE codigo >= " + valorConsulta.intValue() + " AND ativo = TRUE ORDER BY codigo";
        Statement stm = con.createStatement();
        ResultSet tabelaResultado = stm.executeQuery(sqlStr);
        return (montarDadosConsulta(tabelaResultado, nivelMontarDados, this.con));
    }

    /**
     * Responsável por montar os dados de vários objetos, resultantes de uma consulta ao banco de dados (<code>ResultSet</code>).
     * Faz uso da operação <code>montarDados</code> que realiza o trabalho para um objeto por vez.
     * @return  List Contendo vários objetos da classe <code>DescontoVO</code> resultantes da consulta.
     */
    public static List montarDadosConsulta(ResultSet tabelaResultado, int nivelMontarDados, Connection con) throws Exception {
        List vetResultado = new ArrayList();
        while (tabelaResultado.next()) {
            DescontoVO obj = new DescontoVO();
            obj = montarDados(tabelaResultado, nivelMontarDados, con);
            vetResultado.add(obj);
        }
        return vetResultado;
    }

    /**
     * Responsável por montar os dados resultantes de uma consulta ao banco de dados (<code>ResultSet</code>)
     * em um objeto da classe <code>DescontoVO</code>.
     * @return  O objeto da classe <code>DescontoVO</code> com os dados devidamente montados.
     */
    public static DescontoVO montarDados(ResultSet dadosSQL, int nivelMontarDados, Connection con) throws Exception {
        DescontoVO obj = new DescontoVO();
        obj.setCodigo(new Integer(dadosSQL.getInt("codigo")));
        obj.setDescricao(dadosSQL.getString("descricao"));
        obj.setValor(new Double(dadosSQL.getDouble("valor")));
        obj.setTipoDesconto(TipoDesconto.getTipoDesconto(dadosSQL.getString("tipoDesconto")));
        obj.setTipoProduto(dadosSQL.getString("tipoProduto"));
        obj.setAtivo(dadosSQL.getBoolean("ativo"));
        obj.setAplicarEmpresas(dadosSQL.getBoolean("aplicarEmpresas"));

        if(nivelMontarDados == Uteis.NIVELMONTARDADOS_EMPRESAS){
            getFacade().getDescontoEmpresa().montarDados(obj);
        }

        if (obj.getTipoProduto().equals("DR")) {
            obj.setListaIntervalos(montarDadosDescontoRenovacao(obj.getCodigo(), con));
        }
        obj.setNovoObj(false);
        if (nivelMontarDados == Uteis.NIVELMONTARDADOS_DADOSBASICOS) {
            return obj;
        }
        return obj;
    }

    public static List<DescontoRenovacaoVO> montarDadosDescontoRenovacao(int desconto, Connection con) throws Exception {
        DescontoRenovacao descontoRenovacao = new DescontoRenovacao(con);
        List<DescontoRenovacaoVO> consultarPorDesconto = descontoRenovacao.consultarPorDesconto(desconto, false, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
        descontoRenovacao = null;
        return consultarPorDesconto;
    }

    /**
     * Operação responsável por localizar um objeto da classe <code>DescontoVO</code>
     * através de sua chave primária.
     * @exception Exception Caso haja problemas de conexão ou localização do objeto procurado.
     */
    public DescontoVO consultarPorChavePrimaria(Integer codigoPrm, int nivelMontarDados) throws Exception {
        consultar(getIdEntidade(), false);
        String sql = "SELECT * FROM Desconto WHERE codigo = ?";
        PreparedStatement sqlConsultar = con.prepareStatement(sql);
        sqlConsultar.setInt(1, codigoPrm.intValue());
        ResultSet tabelaResultado = sqlConsultar.executeQuery();
        if (!tabelaResultado.next()) {
            throw new ConsistirException("Dados Não Encontrados ( Desconto ).");
        }
        return (montarDados(tabelaResultado, nivelMontarDados, this.con));
    }

    @Override
    public void alterar(DescontoVO obj) throws Exception {
        this.alterar(obj, false);
    }

    @Override
    public void excluir(DescontoVO obj) throws Exception {
        this.excluir(obj, false);

    }

    @Override
    public void incluir(DescontoVO obj) throws Exception {
        this.incluir(obj, false);

    }

    public String consultarJSON() throws Exception {
        ResultSet rs = getRS();

        StringBuilder json = new StringBuilder();
        DescontoVO desconto = new DescontoVO();
        json.append("{\"aaData\":[");
        boolean dados = false;
        while (rs.next()) {
            dados = true;
            json.append("[\"").append(rs.getString("codigo")).append("\",");
            json.append("\"").append(Uteis.normalizarStringJSON(rs.getString("descricao"))).append("\",");
            desconto.setTipoProduto(rs.getString("tipoproduto"));
            json.append("\"").append(desconto.getTipoProduto_Apresentar()).append("\",");
            desconto.setTipoDesconto(TipoDesconto.getTipoDesconto(rs.getString("tipodesconto")));
            json.append("\"").append(desconto.getTipoDesconto_Apresentar()).append("\",");
            desconto.setValor(rs.getDouble("valor"));
            json.append("\"").append(desconto.getValor_Apresentar()).append("\",");
            desconto.setAtivo(rs.getBoolean("ativo"));
            json.append("\"").append(desconto.getSituacao_Apresentar()).append("\"],");
        }
        if (dados) {
            json.deleteCharAt(json.toString().length() - 1);
        }
        json.append("]}");
        return json.toString();
    }

    private ResultSet getRS() throws SQLException {
        String sql = "SELECT codigo, descricao, tipodesconto, tipoproduto, valor, ativo FROM desconto ORDER BY descricao";
        PreparedStatement sqlConsultar = con.prepareStatement(sql);
        ResultSet rs = sqlConsultar.executeQuery();
        return rs;
    }

    public List consultarParaImpressao(String filtro, String ordem, String campoOrdenacao, int i) throws SQLException {

        ResultSet rs = getRS();
        List lista = new ArrayList();

        while (rs.next()) {

            DescontoVO desconto = new DescontoVO();
            String geral = rs.getString("codigo") + rs.getString("descricao") + rs.getString("tipoproduto") + rs.getString("tipodesconto") + rs.getString("valor") + rs.getBoolean("ativo");
            if (geral.toLowerCase().contains(filtro.toLowerCase())) {
                desconto.setCodigo(rs.getInt("codigo"));
                desconto.setDescricao(rs.getString("descricao"));
                desconto.setTipoProduto(rs.getString("tipoproduto"));
                desconto.setTipoDesconto(TipoDesconto.getTipoDesconto(rs.getString("tipodesconto")));
                desconto.setValor(rs.getDouble("valor"));
                desconto.setAtivo(rs.getBoolean("ativo"));
                lista.add(desconto);
            }
        }
        if (campoOrdenacao.equals("Código")) {
            Ordenacao.ordenarLista(lista, "codigo");
        } else if (campoOrdenacao.equals("Descrição")) {
            Ordenacao.ordenarLista(lista, "descricao");
        } else if (campoOrdenacao.equals("Tipo de Produto")) {
            Ordenacao.ordenarLista(lista, "tipoProduto_Apresentar");
        } else if (campoOrdenacao.equals("Tipo")) {
            Ordenacao.ordenarLista(lista, "tipoDesconto");
        } else if (campoOrdenacao.equals("Valor")) {
            Ordenacao.ordenarLista(lista, "valor_Apresentar");
        } else if (campoOrdenacao.equals("Ativo")) {
            Ordenacao.ordenarLista(lista, "situacao_Apresentar");
        }

        if (ordem.contains("desc")) {
            Collections.reverse(lista);
        }
        return lista;

    }
}

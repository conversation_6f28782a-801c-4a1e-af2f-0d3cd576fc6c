package negocio.facade.jdbc.plano;

import negocio.comuns.basico.ColaboradorVO;
import negocio.comuns.plano.HistoricoProfessorTurmaVO;
import negocio.comuns.plano.HorarioTurmaVO;
import negocio.comuns.utilitarias.Uteis;
import negocio.facade.jdbc.arquitetura.SuperEntidade;
import negocio.interfaces.plano.HistoricoProfessorTurmaInterfaceFacade;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.util.ArrayList;
import java.util.List;

public class HistoricoProfessorTurma extends SuperEntidade implements HistoricoProfessorTurmaInterfaceFacade {

    public HistoricoProfessorTurma() throws Exception {
        super();
        setIdEntidade("HistoricoProfessorTurma");
    }

    public HistoricoProfessorTurma(Connection con) throws Exception {
        super(con);
        setIdEntidade("HistoricoProfessorTurma");
    }

    @Override
    public void incluir(HistoricoProfessorTurmaVO obj) throws Exception {
        String sql = "INSERT INTO historicoprofessorturma(professor, horarioturma, lancamento, inicio, fim) "
                + "VALUES ( ?, ?, ?, ?, ?)";
        int i = 0;
        PreparedStatement sqlInserir = con.prepareStatement(sql);
        sqlInserir.setInt(++i, obj.getProfessor().getCodigo());
        sqlInserir.setInt(++i, obj.getHorarioTurma().getCodigo());
        sqlInserir.setTimestamp(++i, Uteis.getDataJDBCTimestamp(obj.getLancamento()));
        sqlInserir.setDate(++i, Uteis.getDataJDBC(obj.getInicio()));
        sqlInserir.setDate(++i, Uteis.getDataJDBC(obj.getFim()));
        sqlInserir.execute();
    }

    @Override
    public void alterar(HistoricoProfessorTurmaVO obj) throws Exception {
        String sql = "UPDATE historicoprofessorturma SET professor = ?, " +
                "horarioturma = ?, lancamento = ?, inicio = ?, fim = ? "
                + " WHERE codigo = ?";
        int i = 0;
        PreparedStatement sqlInserir = con.prepareStatement(sql);
        sqlInserir.setInt(++i, obj.getProfessor().getCodigo());
        sqlInserir.setInt(++i, obj.getHorarioTurma().getCodigo());
        sqlInserir.setTimestamp(++i, Uteis.getDataJDBCTimestamp(obj.getLancamento()));
        sqlInserir.setTimestamp(++i, Uteis.getDataJDBCTimestamp(obj.getInicio()));
        sqlInserir.setTimestamp(++i, Uteis.getDataJDBCTimestamp(obj.getFim()));
        sqlInserir.setInt(++i, obj.getCodigo());
        sqlInserir.execute();
    }

    @Override
    public void excluir(HistoricoProfessorTurmaVO obj) throws Exception {
            String sql = "DELETE FROM historicoprofessorturma WHERE codigo = ?";
            PreparedStatement sqlExcluir = con.prepareStatement(sql);
            sqlExcluir.setInt(1, obj.getCodigo());
            sqlExcluir.execute();
    }

    @Override
    public List<HistoricoProfessorTurmaVO> consultarTodos(int horarioTurma) throws Exception {
        ResultSet rs = criarConsulta("SELECT * FROM historicoprofessorturma WHERE horarioturma = " + horarioTurma +
                " order by fim desc ",
                con);
        return montarDadosLista(rs, con);
    }

    public HistoricoProfessorTurmaVO consultarUltimo(int horarioTurma) throws Exception{
        ResultSet rs = criarConsulta("SELECT * FROM historicoprofessorturma WHERE fim is null and horarioturma = "
                + horarioTurma
                + " order by codigo desc", con);
        return rs.next() ? montarDados(rs, con) : null;
    }

    public static HistoricoProfessorTurmaVO montarDados(ResultSet rs, Connection con) throws Exception{
        HistoricoProfessorTurmaVO historico = new HistoricoProfessorTurmaVO();
        historico.setCodigo(rs.getInt("codigo"));
        historico.setLancamento(rs.getTimestamp("lancamento"));
        historico.setInicio(rs.getTimestamp("inicio"));
        historico.setFim(rs.getTimestamp("fim"));
        historico.setProfessor(new ColaboradorVO());
        historico.getProfessor().setCodigo(rs.getInt("professor"));
        historico.setHorarioTurma(new HorarioTurmaVO());
        historico.getHorarioTurma().setCodigo(rs.getInt("horarioturma"));
        return historico;
    }

    public static List<HistoricoProfessorTurmaVO> montarDadosLista(ResultSet rs, Connection con) throws Exception{
        List<HistoricoProfessorTurmaVO> lista = new ArrayList<HistoricoProfessorTurmaVO>();
        while(rs.next()){
            lista.add(montarDados(rs, con));
        }
        return lista;
    }
}

package negocio.facade.jdbc.plano;

import br.com.pactosolucoes.enumeradores.TipoHorarioCreditoTreinoEnum;
import negocio.comuns.plano.HorarioDisponibilidadeVO;
import negocio.comuns.plano.HorarioVO;
import negocio.comuns.utilitarias.ConsistirException;
import negocio.comuns.utilitarias.Ordenacao;
import negocio.comuns.utilitarias.Uteis;
import negocio.facade.jdbc.arquitetura.SuperEntidade;
import negocio.interfaces.plano.HorarioInterfaceFacade;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.sql.Statement;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Hashtable;
import java.util.List;

/**
 * Classe de persistência que encapsula todas as operações de manipulação dos dados da classe <code>HorarioVO</code>.
 * Responsável por implementar operações como incluir, alterar, excluir e consultar pertinentes a classe <code>HorarioVO</code>.
 * Encapsula toda a interação com o banco de dados.
 *
 * @see HorarioVO
 * @see SuperEntidade
 */
public class Horario extends SuperEntidade implements HorarioInterfaceFacade {

    private Hashtable horarioDisponibilidades;

    public Horario() throws Exception {
        super();
        setHorarioDisponibilidades(new Hashtable());
    }

    public Horario(Connection conexao) throws Exception {
        super(conexao);
        setHorarioDisponibilidades(new Hashtable());
    }

    /**
     * Operação responsável por retornar um novo objeto da classe <code>HorarioVO</code>.
     */
    public HorarioVO novo() throws Exception {
        incluir(getIdEntidade());
        HorarioVO obj = new HorarioVO();
        return obj;
    }

    /**
     * Operação responsável por incluir no banco de dados um objeto da classe <code>HorarioVO</code>.
     * Primeiramente valida os dados (<code>validarDados</code>) do objeto. Verifica a conexão com o banco de dados e a permissão do usuário
     * para realizar esta operacão na entidade.
     * Isto, através da operação <code>incluir</code> da superclasse.
     *
     * @param obj Objeto da classe <code>HorarioVO</code> que será gravado no banco de dados.
     * @throws Exception Caso haja problemas de conexão, restrição de acesso ou validação de dados.
     */
    public void incluir(HorarioVO obj) throws Exception {
        try {
            con.setAutoCommit(false);
            HorarioVO.validarDados(obj);
            HorarioVO.verificarHorario(obj);
            //  Horario.incluir(getIdEntidade());
            obj.realizarUpperCaseDados();
            String sql = "INSERT INTO Horario( descricao, livre, horarioDefault, domingo, segunda, terca, quarta, quinta, sexta, sabado, correspondencia_zd, ativo ) VALUES ( ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)";
            try (PreparedStatement sqlInserir = con.prepareStatement(sql)) {
                sqlInserir.setString(1, obj.getDescricao());
                sqlInserir.setBoolean(2, obj.isLivre().booleanValue());
                sqlInserir.setBoolean(3, obj.isHorarioDefault().booleanValue());
                sqlInserir.setBoolean(4, obj.getDomingo().booleanValue());
                sqlInserir.setBoolean(5, obj.getSegunda().booleanValue());
                sqlInserir.setBoolean(6, obj.getTerca().booleanValue());
                sqlInserir.setBoolean(7, obj.getQuarta().booleanValue());
                sqlInserir.setBoolean(8, obj.getQuinta().booleanValue());
                sqlInserir.setBoolean(9, obj.getSexta().booleanValue());
                sqlInserir.setBoolean(10, obj.getSabado().booleanValue());
                sqlInserir.setString(11, obj.getCorrespondenciaZD());
                sqlInserir.setBoolean(12, obj.getAtivo());
                sqlInserir.execute();
            }
            obj.setCodigo(obterValorChavePrimariaCodigo());
            obj.setNovoObj(new Boolean(false));

            if (obj.getLivre()) {
                getFacade().getHorarioDisponibilidade().excluirHorarioDisponibilidades(obj.getCodigo());
            } else {
                getFacade().getHorarioDisponibilidade().incluirHorarioDisponibilidades(obj.getCodigo(), obj.getHorarioDisponibilidadeVOs());
            }
            con.commit();
        } catch (Exception e) {
            con.rollback();
            con.setAutoCommit(true);
            throw e;
        } finally {
            con.setAutoCommit(true);
        }
    }

    public void incluirSemCommit(HorarioVO obj) throws Exception {
        try {
            HorarioVO.validarDados(obj);
            HorarioVO.verificarHorario(obj);
            //  Horario.incluir(getIdEntidade());
            obj.realizarUpperCaseDados();
            String sql = "INSERT INTO Horario( descricao, livre, horarioDefault, domingo, segunda, terca, quarta, quinta, sexta, sabado, correspondencia_zd, ativo ) VALUES ( ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)";
            try (PreparedStatement sqlInserir = con.prepareStatement(sql)) {
                sqlInserir.setString(1, obj.getDescricao());
                sqlInserir.setBoolean(2, obj.isLivre().booleanValue());
                sqlInserir.setBoolean(3, obj.isHorarioDefault().booleanValue());
                sqlInserir.setBoolean(4, obj.getDomingo().booleanValue());
                sqlInserir.setBoolean(5, obj.getSegunda().booleanValue());
                sqlInserir.setBoolean(6, obj.getTerca().booleanValue());
                sqlInserir.setBoolean(7, obj.getQuarta().booleanValue());
                sqlInserir.setBoolean(8, obj.getQuinta().booleanValue());
                sqlInserir.setBoolean(9, obj.getSexta().booleanValue());
                sqlInserir.setBoolean(10, obj.getSabado().booleanValue());
                sqlInserir.setString(11, obj.getCorrespondenciaZD());
                sqlInserir.setBoolean(12, obj.getAtivo());
                sqlInserir.execute();
            }
            obj.setCodigo(obterValorChavePrimariaCodigo());
            obj.setNovoObj(new Boolean(false));

            if (obj.getLivre()) {
                getFacade().getHorarioDisponibilidade().excluirHorarioDisponibilidades(obj.getCodigo());
            } else {
                getFacade().getHorarioDisponibilidade().incluirHorarioDisponibilidades(obj.getCodigo(), obj.getHorarioDisponibilidadeVOs());
            }
        } catch (Exception e) {
            throw e;
        }
    }

    /**
     * Operação responsável por alterar no BD os dados de um objeto da classe <code>HorarioVO</code>.
     * Sempre utiliza a chave primária da classe como atributo para localização do registro a ser alterado.
     * Primeiramente valida os dados (<code>validarDados</code>) do objeto. Verifica a conexão com o banco de dados e a permissão do usuário
     * para realizar esta operacão na entidade.
     * Isto, através da operação <code>alterar</code> da superclasse.
     *
     * @param obj Objeto da classe <code>HorarioVO</code> que será alterada no banco de dados.
     * @throws Execption Caso haja problemas de conexão, restrição de acesso ou validação de dados.
     */
    public void alterar(HorarioVO obj) throws Exception {
        try {
            con.setAutoCommit(false);
            alterarSemCommit(obj);
            con.commit();
        } catch (Exception e) {
            con.rollback();
            con.setAutoCommit(true);
            throw e;
        } finally {
            con.setAutoCommit(true);
        }
    }

    public void alterarSemCommit(HorarioVO obj) throws Exception {
        HorarioVO.validarDados(obj);
        HorarioVO.verificarHorario(obj);
        alterar(getIdEntidade());
        obj.realizarUpperCaseDados();
        String sql = "UPDATE Horario set descricao=?, livre=?, horarioDefault=?, domingo=?, segunda=?, terca=?, quarta=?, quinta=?, sexta=?, sabado=?, correspondencia_zd = ?, ativo = ? WHERE ((codigo = ?))";
        try (PreparedStatement sqlAlterar = con.prepareStatement(sql)) {
            sqlAlterar.setString(1, obj.getDescricao());
            sqlAlterar.setBoolean(2, obj.isLivre().booleanValue());
            sqlAlterar.setBoolean(3, obj.isHorarioDefault().booleanValue());
            sqlAlterar.setBoolean(4, obj.getDomingo().booleanValue());
            sqlAlterar.setBoolean(5, obj.getSegunda().booleanValue());
            sqlAlterar.setBoolean(6, obj.getTerca().booleanValue());
            sqlAlterar.setBoolean(7, obj.getQuarta().booleanValue());
            sqlAlterar.setBoolean(8, obj.getQuinta().booleanValue());
            sqlAlterar.setBoolean(9, obj.getSexta().booleanValue());
            sqlAlterar.setBoolean(10, obj.getSabado().booleanValue());
            sqlAlterar.setString(11, obj.getCorrespondenciaZD());
            sqlAlterar.setBoolean(12, obj.getAtivo());
            sqlAlterar.setInt(13, obj.getCodigo().intValue());
            sqlAlterar.execute();
        }
        if (obj.getLivre()) {
            getFacade().getHorarioDisponibilidade().excluirHorarioDisponibilidades(obj.getCodigo());
        } else {
            getFacade().getHorarioDisponibilidade().alterarHorarioDisponibilidades(obj.getCodigo(), obj.getHorarioDisponibilidadeVOs());
        }
    }

    /**
     * Operação responsável por excluir no BD um objeto da classe <code>HorarioVO</code>.
     * Sempre localiza o registro a ser excluído através da chave primária da entidade.
     * Primeiramente verifica a conexão com o banco de dados e a permissão do usuário
     * para realizar esta operacão na entidade.
     * Isto, através da operação <code>excluir</code> da superclasse.
     *
     * @param obj Objeto da classe <code>HorarioVO</code> que será removido no banco de dados.
     * @throws Execption Caso haja problemas de conexão ou restrição de acesso.
     */
    public void excluir(HorarioVO obj) throws Exception {
        try {
            con.setAutoCommit(false);
            excluir(getIdEntidade());
            String sql = "DELETE FROM Horario WHERE ((codigo = ?))";
            try (PreparedStatement sqlExcluir = con.prepareStatement(sql)) {
                sqlExcluir.setInt(1, obj.getCodigo().intValue());
                sqlExcluir.execute();
            }
            getFacade().getHorarioDisponibilidade().excluirHorarioDisponibilidades(obj.getCodigo());
            con.commit();
        } catch (Exception e) {
            con.rollback();
            con.setAutoCommit(true);
            throw e;
        } finally {
            con.setAutoCommit(true);
        }
    }

    /**
     * Responsável por realizar uma consulta de <code>Horario</code> através do valor do atributo
     * <code>Double percentualDesconto</code>. Retorna os objetos com valores iguais ou superiores ao parâmetro fornecido.
     * Faz uso da operação <code>montarDadosConsulta</code> que realiza o trabalho de prerarar o List resultante.
     *
     * @param controlarAcesso Indica se a aplicação deverá verificar se o usuário possui permissão para esta consulta ou não.
     * @return List Contendo vários objetos da classe <code>HorarioVO</code> resultantes da consulta.
     * @throws Exception Caso haja problemas de conexão ou restrição de acesso.
     */
    public List consultarPorPercentualDesconto(Double valorConsulta, boolean controlarAcesso, int nivelMontarDados) throws Exception {
        consultar(getIdEntidade(), controlarAcesso);
        String sqlStr = "SELECT * FROM Horario WHERE percentualDesconto >= " + valorConsulta.doubleValue() + " ORDER BY percentualDesconto";
        try (Statement stm = con.createStatement()) {
            try (ResultSet tabelaResultado = stm.executeQuery(sqlStr)) {
                return (montarDadosConsulta(tabelaResultado, nivelMontarDados, con));
            }
        }
    }

    public List consultarPorValorEspecifico(Double valorConsulta, boolean controlarAcesso, int nivelMontarDados) throws Exception {
        consultar(getIdEntidade(), controlarAcesso);
        String sqlStr = "SELECT * FROM Horario WHERE valorEspecifico >= " + valorConsulta.doubleValue() + " ORDER BY valorEspecifico";
        try (Statement stm = con.createStatement()) {
            try (ResultSet tabelaResultado = stm.executeQuery(sqlStr)) {
                return (montarDadosConsulta(tabelaResultado, nivelMontarDados, con));
            }
        }
    }

    /**
     * Responsável por realizar uma consulta de <code>Horario</code> através do valor do atributo
     * <code>String descricao</code>. Retorna os objetos, com início do valor do atributo idêntico ao parâmetro fornecido.
     * Faz uso da operação <code>montarDadosConsulta</code> que realiza o trabalho de prerarar o List resultante.
     *
     * @param controlarAcesso Indica se a aplicação deverá verificar se o usuário possui permissão para esta consulta ou não.
     * @return List Contendo vários objetos da classe <code>HorarioVO</code> resultantes da consulta.
     * @throws Exception Caso haja problemas de conexão ou restrição de acesso.
     */
    public List<HorarioVO> consultarPorDescricao(String valorConsulta, boolean controlarAcesso, int nivelMontarDados) throws Exception {
        return consultarPorDescricao(valorConsulta, controlarAcesso, nivelMontarDados, null);
    }

    public List<HorarioVO> consultarPorDescricao(String valorConsulta, boolean controlarAcesso, int nivelMontarDados, Boolean ativo) throws Exception {
        consultar(getIdEntidade(), controlarAcesso);
        StringBuilder sqlStr = new StringBuilder("SELECT * FROM Horario WHERE upper( descricao ) like('").append(valorConsulta.toUpperCase()).append("%')");
        if (ativo != null) {
            sqlStr.append(" AND ativo = ").append(ativo);
        }
        sqlStr.append(" ORDER BY descricao");
        try (Statement stm = con.createStatement()) {
            try (ResultSet tabelaResultado = stm.executeQuery(sqlStr.toString())) {
                return (montarDadosConsulta(tabelaResultado, nivelMontarDados, con));
            }
        }
    }

    public HorarioVO consultarPorDescricao(String valorConsulta, int nivelMontarDados) throws Exception {
        String sqlStr = "SELECT * FROM Horario WHERE upper( descricao ) like('" + valorConsulta.toUpperCase() + "') ORDER BY descricao";
        try (Statement stm = con.createStatement()) {
            try (ResultSet tabelaResultado = stm.executeQuery(sqlStr)) {
                if (!tabelaResultado.next()) {
                    return new HorarioVO();
                }
                return montarDados(tabelaResultado, nivelMontarDados, con);
            }
        }
    }

    public List consultarPorHorarioDefault(Boolean valorConsulta, boolean controlarAcesso, int nivelMontarDados) throws Exception {
        consultar(getIdEntidade(), controlarAcesso);
        String sqlStr = "SELECT * FROM Horario WHERE Horario.horarioDefault = " + valorConsulta + " ORDER BY descricao";
        try (Statement stm = con.createStatement()) {
            try (ResultSet tabelaResultado = stm.executeQuery(sqlStr)) {
                return (montarDadosConsulta(tabelaResultado, nivelMontarDados, con));
            }
        }
    }

    /**
     * Responsável por realizar uma consulta de <code>Horario</code> através do valor do atributo
     * <code>Integer codigo</code>. Retorna os objetos com valores iguais ou superiores ao parâmetro fornecido.
     * Faz uso da operação <code>montarDadosConsulta</code> que realiza o trabalho de prerarar o List resultante.
     *
     * @param controlarAcesso Indica se a aplicação deverá verificar se o usuário possui permissão para esta consulta ou não.
     * @return List Contendo vários objetos da classe <code>HorarioVO</code> resultantes da consulta.
     * @throws Exception Caso haja problemas de conexão ou restrição de acesso.
     */
    public List consultarPorCodigo(Integer valorConsulta, boolean controlarAcesso, int nivelMontarDados) throws Exception {
        consultar(getIdEntidade(), controlarAcesso);
        String sqlStr = "SELECT * FROM Horario WHERE codigo >= " + valorConsulta.intValue() + " ORDER BY codigo";
        try (Statement stm = con.createStatement()) {
            try (ResultSet tabelaResultado = stm.executeQuery(sqlStr)) {
                return (montarDadosConsulta(tabelaResultado, nivelMontarDados, con));
            }
        }
    }

    /**
     * Responsável por montar os dados de vários objetos, resultantes de uma consulta ao banco de dados (<code>ResultSet</code>).
     * Faz uso da operação <code>montarDados</code> que realiza o trabalho para um objeto por vez.
     *
     * @return List Contendo vários objetos da classe <code>HorarioVO</code> resultantes da consulta.
     */
    public static List<HorarioVO> montarDadosConsulta(ResultSet tabelaResultado, int nivelMontarDados, Connection con) throws Exception {
        List<HorarioVO> vetResultado = new ArrayList<HorarioVO>();
        while (tabelaResultado.next()) {
            HorarioVO obj = montarDados(tabelaResultado, nivelMontarDados, con);
            vetResultado.add(obj);
        }
        return vetResultado;
    }

    public static HorarioVO montarDadosBasico(ResultSet dadosSQL) throws Exception {
        HorarioVO obj = new HorarioVO();
        obj.setNovoObj(false);
        obj.setCodigo(new Integer(dadosSQL.getInt("codigo")));
        obj.setDescricao(dadosSQL.getString("descricao"));
        obj.setLivre(dadosSQL.getBoolean("livre"));
        obj.setDomingo(dadosSQL.getBoolean("domingo"));
        obj.setSegunda(dadosSQL.getBoolean("segunda"));
        obj.setTerca(dadosSQL.getBoolean("terca"));
        obj.setQuarta(dadosSQL.getBoolean("quarta"));
        obj.setQuinta(dadosSQL.getBoolean("quinta"));
        obj.setSexta(dadosSQL.getBoolean("sexta"));
        obj.setSabado(dadosSQL.getBoolean("sabado"));
        obj.setHorarioDefault(dadosSQL.getBoolean("horarioDefault"));
        obj.setAtivo(dadosSQL.getBoolean("ativo"));
        obj.setCorrespondenciaZD(dadosSQL.getString("correspondencia_zd"));
        return obj;

    }

    /**
     * Responsável por montar os dados resultantes de uma consulta ao banco de dados (<code>ResultSet</code>)
     * em um objeto da classe <code>HorarioVO</code>.
     *
     * @return O objeto da classe <code>HorarioVO</code> com os dados devidamente montados.
     */
    public static HorarioVO montarDados(ResultSet dadosSQL, int nivelMontarDados, Connection con) throws Exception {
        HorarioVO obj = montarDadosBasico(dadosSQL);

        if (nivelMontarDados == Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA || nivelMontarDados == Uteis.NIVELMONTARDADOS_MINIMOS) {
            return obj;
        }

        if (nivelMontarDados == Uteis.NIVELMONTARDADOS_DADOSBASICOS) {
            return obj;
        }
        HorarioDisponibilidade horarioDisponibilidadeDao = new HorarioDisponibilidade(con);
        obj.setHorarioDisponibilidadeVOs(horarioDisponibilidadeDao.consultarHorarioDisponibilidades(obj.getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS));
        if (nivelMontarDados == Uteis.NIVELMONTARDADOS_DADOSENTIDADESUBORDINADAS) {
            return obj;
        }

        return obj;
    }

    /**
     * Operação responsável por adicionar um objeto da <code>HorarioDisponibilidadeVO</code> no Hashtable <code>HorarioDisponibilidades</code>.
     * Neste Hashtable são mantidos todos os objetos de HorarioDisponibilidade de uma determinada Horario.
     *
     * @param obj Objeto a ser adicionado no Hashtable.
     */
    public void adicionarObjHorarioDisponibilidades(HorarioDisponibilidadeVO obj) throws Exception {
        getHorarioDisponibilidades().put(obj.getIdentificador() + "", obj);
        //adicionarObjSubordinadoOC
    }

    /**
     * Operação responsável por remover um objeto da classe <code>HorarioDisponibilidadeVO</code> do Hashtable <code>HorarioDisponibilidades</code>.
     * Neste Hashtable são mantidos todos os objetos de HorarioDisponibilidade de uma determinada Horario.
     *
     * @param Identificador Atributo da classe <code>HorarioDisponibilidadeVO</code> utilizado como apelido (key) no Hashtable.
     */
    public void excluirObjHorarioDisponibilidades(String Identificador) throws Exception {
        getHorarioDisponibilidades().remove(Identificador + "");
        //excluirObjSubordinadoOC
    }

    /**
     * Operação responsável por localizar um objeto da classe <code>HorarioVO</code>
     * através de sua chave primária.
     *
     * @throws Exception Caso haja problemas de conexão ou localização do objeto procurado.
     */
    public HorarioVO consultarPorChavePrimaria(Integer codigoPrm, int nivelMontarDados) throws Exception {
        return consultarPorChavePrimaria(codigoPrm, nivelMontarDados, null);
    }

    public HorarioVO consultarPorChavePrimaria(Integer codigoPrm, int nivelMontarDados, Boolean ativo) throws Exception {
        consultar(getIdEntidade(), false);
        StringBuilder sql = new StringBuilder("SELECT * FROM Horario WHERE codigo = ?");
        if (ativo != null) {
            sql.append(" AND ativo = ?");
        }
        try (PreparedStatement sqlConsultar = con.prepareStatement(sql.toString())) {
            sqlConsultar.setInt(1, codigoPrm.intValue());
            if (ativo != null) {
                sqlConsultar.setBoolean(2, ativo);
            }
            try (ResultSet tabelaResultado = sqlConsultar.executeQuery()) {
                if (!tabelaResultado.next()) {
                    throw new ConsistirException("Dados Não Encontrados ( Horario ).");
                }
                return (montarDados(tabelaResultado, nivelMontarDados, con));
            }
        }
    }

    public Hashtable getHorarioDisponibilidades() {
        return (horarioDisponibilidades);
    }

    public void setHorarioDisponibilidades(Hashtable horarioDisponibilidades) {
        this.horarioDisponibilidades = horarioDisponibilidades;
    }

    public String consultarJSON(String situacao) throws Exception {

        StringBuilder json;
        boolean dados;
        try (ResultSet rs = getPS(situacao).executeQuery()) {
            json = new StringBuilder();
            json.append("{\"aaData\":[");
            dados = false;
            while (rs.next()) {
                dados = true;
                json.append("[\"").append(rs.getString("codigo")).append("\",");
                json.append("\"").append(Uteis.normalizarStringJSON(rs.getString("descricao"))).append("\",");
                json.append("\"").append(rs.getBoolean("ativo") ? "ATIVO" : "INATIVO").append("\"],");
            }
        }
        if (dados) {
            json.deleteCharAt(json.toString().length() - 1);
        }
        json.append("]}");
        return json.toString();
    }

    private PreparedStatement getPS() throws SQLException {
        return getPS(null);
    }

    private PreparedStatement getPS(String situacao) throws SQLException {
        StringBuilder sql = new StringBuilder("SELECT codigo, descricao, ativo FROM horario");
        if (situacao != null && !situacao.equals("TD")) {
            sql.append(" WHERE ativo = ");
            if (situacao.equals("AT")) {
                sql.append("true");
            } else if (situacao.equals("NA")) {
                sql.append("false");
            }
        }
        return getCon().prepareStatement(sql.toString());
    }

    public List consultarParaImpressao(String filtro, String ordem, String campoOrdenacao, int i) throws SQLException {

        List lista;
        try (ResultSet rs = getPS().executeQuery()) {
            lista = new ArrayList();

            while (rs.next()) {

                HorarioVO hr = new HorarioVO();
                String geral = rs.getString("codigo") + rs.getString("descricao");
                if (geral.toLowerCase().contains(filtro.toLowerCase())) {
                    hr.setCodigo(rs.getInt("codigo"));
                    hr.setDescricao(rs.getString("descricao"));
                    hr.setAtivo(rs.getBoolean("ativo"));
                    lista.add(hr);
                }
            }
        }
        if (campoOrdenacao.equals("Código")) {
            Ordenacao.ordenarLista(lista, "codigo");
        } else if (campoOrdenacao.equals("Descrição")) {
            Ordenacao.ordenarLista(lista, "descricao");
        }
        if (ordem.contains("desc")) {
            Collections.reverse(lista);
        }
        return lista;

    }

    @Override
    public HorarioVO consultarPorCodigoContrato(Integer codigoContrato) throws Exception {
        consultar(getIdEntidade(), false);
        String sql = "select * from horario where codigo in (select horario from contratohorario where contrato=?)";
        try (PreparedStatement sqlConsultar = con.prepareStatement(sql)) {
            sqlConsultar.setInt(1, codigoContrato);
            try (ResultSet tabelaResultado = sqlConsultar.executeQuery()) {
                if (!tabelaResultado.next()) {
                    throw new ConsistirException("Dados Não Encontrados ( Horario ).");
                }

                return (montarDados(tabelaResultado, Uteis.NIVELMONTARDADOS_DADOSBASICOS, con));
            }
        }
    }

    public List<HorarioVO> consultarTodosHorarios(boolean controlarAcesso, int nivelMontarDados) throws Exception {
        consultar(getIdEntidade(), controlarAcesso);
        String sqlStr = "SELECT * FROM Horario";
        try (Statement stm = con.createStatement()) {
            try (ResultSet tabelaResultado = stm.executeQuery(sqlStr)) {
                return (montarDadosConsulta(tabelaResultado, nivelMontarDados, con));
            }
        }
    }

    public List<String> consultarTodosHorariosPorContrato() throws Exception {
        StringBuilder sql = new StringBuilder();
        sql.append("SELECT DISTINCT (case\twhen con.vendacreditotreino and cdt.tipohorario = 2 THEN '"+ TipoHorarioCreditoTreinoEnum.LIVRE_OBRIGATORIO_MARCAR_AULA.getDescricao()+"' ");
        sql.append(" else hr.descricao end) horarios \n");
        sql.append("FROM contrato con \n");
        sql.append("LEFT JOIN contratoduracao cd ON con.codigo = cd.contrato \n");
        sql.append("LEFT JOIN contratoduracaocreditotreino cdt ON cd.codigo = cdt.contratoduracao \n");
        sql.append("LEFT JOIN contratohorario ch ON ch.contrato = con.codigo \n");
        sql.append("LEFT JOIN horario hr ON hr.codigo = ch.horario \n");
        sql.append("ORDER BY horarios \n");
        List<String> listaHorarios = new ArrayList<String>();
        try (Statement stm = con.createStatement()) {
            try (ResultSet tabelaResultado = stm.executeQuery(sql.toString())) {
                while (tabelaResultado.next()) {
                    listaHorarios.add(tabelaResultado.getString("horarios"));
                }
            }
        }
        return listaHorarios;
    }
}

package negocio.facade.jdbc.plano;

import negocio.comuns.plano.TipoModalidadeVO;
import negocio.comuns.utilitarias.ConsistirException;
import negocio.comuns.utilitarias.Ordenacao;
import negocio.comuns.utilitarias.Uteis;
import negocio.facade.jdbc.arquitetura.SuperEntidade;
import negocio.interfaces.plano.TipoModalidadeInterfaceFacade;

import java.sql.*;
import java.util.*;

/**
 * Classe de persistência que encapsula todas as operações de manipulação dos dados da classe <code>TipoModalidadeVO</code>.
 * Responsável por implementar operações como incluir, alterar, excluir e consultar pertinentes a classe <code>TipoModalidadeVO</code>.
 * Encapsula toda a interação com o banco de dados.
 * @see TipoModalidadeVO
 * @see SuperEntidade
 */
public class TipoModalidade extends SuperEntidade implements TipoModalidadeInterfaceFacade {

    public TipoModalidade() throws Exception {
        super();
    }

    public TipoModalidade(Connection con) throws Exception {
        super(con);        
    }

	/**
     * Operação responsável por retornar um novo objeto da classe <code>TipoModalidadeVO</code>.
     */
    public TipoModalidadeVO novo() throws Exception {
        incluir(getIdEntidade());
        TipoModalidadeVO obj = new TipoModalidadeVO();
        return obj;
    }

    /**
     * Operação responsável por incluir no banco de dados um objeto da classe <code>TipoModalidadeVO</code>.
     * Primeiramente valida os dados (<code>validarDadosIncluir</code>) do objeto. Verifica a conexão com o banco de dados e a permissão do usuário
     * para realizar esta operacão na entidade.
     * Isto, através da operação <code>incluir</code> da superclasse.
     * @param obj  Objeto da classe <code>TipoModalidadeVO</code> que será gravado no banco de dados.
     * @exception Exception Caso haja problemas de conexão, restrição de acesso ou validação de dados.
     */
    public void incluir(TipoModalidadeVO obj) throws Exception {
        try {
            con.setAutoCommit(false);
            TipoModalidadeVO.validarDadosIncluir(obj);
            incluir(getIdEntidade());
            obj.realizarUpperCaseDados();
            String sql = "INSERT INTO TipoModalidade( nome, identificador) VALUES ( ?, ?)";
            PreparedStatement sqlInserir = con.prepareStatement(sql);
            sqlInserir.setString(1, obj.getNome());
            sqlInserir.setInt(2, obj.getIdentificador().intValue());
            sqlInserir.execute();

            obj.setCodigo(obterValorChavePrimariaCodigo());
            obj.setNovoObj(Boolean.FALSE);
            con.commit();
        } catch (Exception e) {
            obj.setNovoObj(Boolean.TRUE);
            con.rollback();
            con.setAutoCommit(true);
            throw e;
        } finally {
            con.setAutoCommit(true);
        }
    }

    public void incluirSemCommit(TipoModalidadeVO obj) throws Exception {
        try {
            TipoModalidadeVO.validarDadosIncluir(obj);
            obj.realizarUpperCaseDados();
            String sql = "INSERT INTO TipoModalidade( nome, identificador) VALUES ( ?, ?)";
            PreparedStatement sqlInserir = con.prepareStatement(sql);
            sqlInserir.setString(1, obj.getNome());
            sqlInserir.setInt(2, obj.getIdentificador().intValue());
            sqlInserir.execute();

            obj.setCodigo(obterValorChavePrimariaCodigo());
            obj.setNovoObj(Boolean.FALSE);
        } catch (Exception e) {
            obj.setNovoObj(Boolean.TRUE);
            throw e;
        }
    }

    /**
     * Operação responsável por alterar no BD os dados de um objeto da classe <code>TipoModalidadeVO</code>.
     * Sempre utiliza a chave primária da classe como atributo para localização do registro a ser alterado.
     * Primeiramente valida os dados (<code>validarDadosAlterar</code>) do objeto. Verifica a conexão com o
     * banco de dados e a permissão do usuário para realizar esta operacão na entidade.
     * Isto, através da operação <code>alterar</code> da superclasse.
     * @param obj    Objeto da classe <code>TipoModalidadeVO</code> que será alterada no banco de dados.
     * @exception Exception Caso haja problemas de conexão, restrição de acesso ou validação de dados.
     */
    public void alterar(TipoModalidadeVO obj) throws Exception {
        try {
            con.setAutoCommit(false);
            TipoModalidadeVO.validarDadosAlterar(obj);
            alterar(getIdEntidade());
            obj.realizarUpperCaseDados();
            String sql = "UPDATE TipoModalidade set nome = ?, identificador = ? WHERE ((codigo = ?))";
            PreparedStatement sqlAlterar = con.prepareStatement(sql);
            sqlAlterar.setString(1, obj.getNome());
            sqlAlterar.setInt(2, obj.getIdentificador().intValue());
            sqlAlterar.setInt(3, obj.getCodigo());
            sqlAlterar.execute();

            con.commit();

        } catch (Exception e) {
            con.rollback();
            con.setAutoCommit(true);
            throw e;
        } finally {
            con.setAutoCommit(true);
        }
    }

    /**
     * Operação responsável por excluir no BD um objeto da classe <code>TipoModalidadeVO</code>.
     * Sempre localiza o registro a ser excluído através da chave primária da entidade.
     * Primeiramente verifica a conexão com o banco de dados e a permissão do usuário
     * para realizar esta operacão na entidade.
     * Isto, através da operação <code>excluir</code> da superclasse.
     * @param obj    Objeto da classe <code>TipoModalidadeVO</code> que será removido no banco de dados.
     * @exception Exception Caso haja problemas de conexão ou restrição de acesso.
     */
    public void excluir(TipoModalidadeVO obj) throws Exception {
        try {
            con.setAutoCommit(false);
            excluir(getIdEntidade());
            String sql = "DELETE FROM TipoModalidade WHERE ((codigo = ?))";
            PreparedStatement sqlExcluir = con.prepareStatement(sql);
            sqlExcluir.setInt(1, obj.getCodigo().intValue());
            sqlExcluir.execute();

            con.commit();
        } catch (Exception e) {
            con.rollback();
            con.setAutoCommit(true);
            throw e;
        } finally {
            con.setAutoCommit(true);
        }
    }

    /**
     * Responsável por realizar uma consulta de <code>TipoModalidade</code> através do valor do atributo
     * <code>Integer codigo</code>. Retorna os objetos com valores iguais ou superiores ao parâmetro fornecido.
     * Faz uso da operação <code>montarDadosConsulta</code> que realiza o trabalho de prerarar o List resultante.
     * @param   controlarAcesso Indica se a aplicação deverá verificar se o usuário possui permissão para esta consulta ou não.
     * @return  List Contendo vários objetos da classe <code>TipoModalidadeVO</code> resultantes da consulta.
     * @exception Exception Caso haja problemas de conexão ou restrição de acesso.
     */
    public List consultarPorCodigo(Integer valorConsulta, boolean controlarAcesso, int nivelMontarDados) throws Exception {
        consultar(getIdEntidade(), controlarAcesso);
        String sqlStr = "";
        sqlStr += "SELECT codigo.* FROM TipoModalidade WHERE TipoModalidade.codigo >= " + valorConsulta.intValue() + " ORDER BY TipoModalidade.codigo";
        Statement stm = con.createStatement();
        ResultSet tabelaResultado = stm.executeQuery(sqlStr);
        return (montarDadosConsulta(tabelaResultado, nivelMontarDados, this.con));
    }

    /**
     * Responsável por realizar uma consulta de <code>TipoModalidade</code> através do valor do atributo
     * <code>String nome</code>. Retorna os objetos, com início do valor do atributo idêntico ao parâmetro fornecido.
     * Faz uso da operação <code>montarDadosConsulta</code> que realiza o trabalho de prerarar o List resultante.
     * @param   controlarAcesso Indica se a aplicação deverá verificar se o usuário possui permissão para esta consulta ou não.
     * @return  List Contendo vários objetos da classe <code>TipoModalidadeVO</code> resultantes da consulta.
     * @exception Exception Caso haja problemas de conexão ou restrição de acesso.
     */
    public List<TipoModalidadeVO> consultarPorNome(String valorConsulta, boolean controlarAcesso, int nivelMontarDados) throws Exception {
        consultar(getIdEntidade(), controlarAcesso);
        String sqlStr = "";
        sqlStr += "SELECT nome,* FROM TipoModalidade WHERE upper( TipoModalidade.nome ) like('" + valorConsulta.toUpperCase() + "%') ORDER BY TipoModalidade.nome";
        Statement stm = con.createStatement();
        ResultSet tabelaResultado = stm.executeQuery(sqlStr);
        return (montarDadosConsulta(tabelaResultado, nivelMontarDados, this.con));
    }

    public List consultarPorNomeTipoModalidadeComLimite(String valorConsulta, boolean controlarAcesso, int nivelMontarDados) throws Exception {
        consultar(getIdEntidade(), controlarAcesso);
        String sqlStr = "";
        sqlStr += "SELECT nome,* FROM tipomodalidade WHERE upper(TipoModalidade.nome) like('" + valorConsulta.toUpperCase() + "%') ORDER BY tipomodalidade.nome limit 20";
        Statement stm = con.createStatement();
        ResultSet tabelaResultado = stm.executeQuery(sqlStr);
        return (montarDadosConsulta(tabelaResultado, nivelMontarDados, this.con));
    }
    
    /**
     * Responsável por montar os dados de vários objetos, resultantes de uma consulta ao banco de dados (<code>ResultSet</code>).
     * Faz uso da operação <code>montarDados</code> que realiza o trabalho para um objeto por vez.
     * @return  List Contendo vários objetos da classe <code>TipoModalidadeVO</code> resultantes da consulta.
     */
    public static List<TipoModalidadeVO> montarDadosConsulta(ResultSet tabelaResultado, int nivelMontarDados, Connection con) throws Exception {
        List<TipoModalidadeVO> vetResultado = new ArrayList<TipoModalidadeVO>();
        while (tabelaResultado.next()) {
            TipoModalidadeVO obj = montarDados(tabelaResultado, nivelMontarDados, con);
            vetResultado.add(obj);
        }
        return vetResultado;
    }

    /**
     * Responsável por montar os dados resultantes de uma consulta ao banco de dados (<code>ResultSet</code>)
     * em um objeto da classe <code>TipoModalidadeVO</code>.
     * @return  O objeto da classe <code>TipoModalidadeVO</code> com os dados devidamente montados.
     */
    public static TipoModalidadeVO montarDados(ResultSet dadosSQL, int nivelMontarDados, Connection con) throws Exception {
        TipoModalidadeVO obj = new TipoModalidadeVO();
        if (nivelMontarDados == Uteis.NIVELMONTARDADOS_TELACONSULTA) {
            return obj;
        }
        if (nivelMontarDados == Uteis.NIVELMONTARDADOS_MINIMOS) {
            return obj;
        }
        obj.setCodigo(dadosSQL.getInt("codigo"));
        obj.setNome(dadosSQL.getString("nome"));
        obj.setIdentificador(dadosSQL.getInt("identificador"));
        obj.setNovoObj(false);

        if (nivelMontarDados == Uteis.NIVELMONTARDADOS_DADOSBASICOS) {
            return obj;
        }
        if (nivelMontarDados == Uteis.NIVELMONTARDADOS_DADOSENTIDADESUBORDINADAS) {
            return obj;
        }
        return obj;
    }

    /**
     * Operação responsável por localizar um objeto da classe <code>TipoModalidadeVO</code>
     * através de sua chave primária. 
     * @exception Exception Caso haja problemas de conexão ou localização do objeto procurado.
     */
    public TipoModalidadeVO consultarPorChavePrimaria(Integer codigoPrm, int nivelMontarDados) throws Exception {
        consultar(getIdEntidade(), false);
        String sql = "SELECT * FROM TipoModalidade WHERE codigo = ?";
        PreparedStatement sqlConsultar = con.prepareStatement(sql);
        sqlConsultar.setInt(1, codigoPrm);
        ResultSet tabelaResultado = sqlConsultar.executeQuery();
        if (!tabelaResultado.next()) {
            throw new ConsistirException("Dados Não Encontrados ( TipoModalidade ).");
        }
        return (montarDados(tabelaResultado, nivelMontarDados, this.con));
    }

    /**
     * Operação responsável por localizar um objeto da classe <code>TipoModalidadeVO</code>
     * através de seu identificador.
     * @exception Exception Caso haja problemas de conexão ou localização do objeto procurado.
     */
    public TipoModalidadeVO consultarPorIdentificador(Integer codigoIdentificador, int nivelMontarDados) throws Exception {
        consultar(getIdEntidade(), false);
        String sql = "SELECT * FROM TipoModalidade WHERE identificador = ?";
        PreparedStatement sqlConsultar = con.prepareStatement(sql);
        sqlConsultar.setInt(1, codigoIdentificador);
        ResultSet tabelaResultado = sqlConsultar.executeQuery();
        if (!tabelaResultado.next()) {
            throw new ConsistirException("Dados Não Encontrados ( TipoModalidade ).");
        }
        return (montarDados(tabelaResultado, nivelMontarDados, this.con));
    }

    /**
	 * Responsável por consultar todos os registros de modalidades de forma simplificada, montando num mapa.
	 * <AUTHOR> Cristian
	 * 11/08/2011
	 */
	public Map<Integer, String> consultarTipoModalidadesSimplificado() throws Exception{
		Map<Integer, String> mapResult = new HashMap<Integer, String>();
		String sql = "SELECT nome, codigo FROM TipoModalidade";
		ResultSet consulta = TipoModalidade.criarConsulta(sql, con);
		while(consulta.next()){
			mapResult.put(consulta.getInt("codigo"), consulta.getString("nome"));
		}
		return mapResult;
	}

	public String consultarJSON(String codEmpresa) throws Exception {
        ResultSet rs = getRS(Integer.parseInt(codEmpresa));

        StringBuilder json = new StringBuilder();
        json.append("{\"aaData\":[");
        boolean dados = false;
        while (rs.next()) {
            dados = true;
            json.append("[\"").append(rs.getString("codigo")).append("\",");
            json.append("\"").append(Uteis.normalizarStringJSON(rs.getString("nome"))).append("\",");
            json.append("\"").append(Uteis.normalizarStringJSON(rs.getString("identificador"))).append("\"],");

        }
        if(dados) {
            json.deleteCharAt(json.toString().length() - 1);
        }
        json.append("]}");
        return json.toString();
    }

    private ResultSet getRS(Integer empresa) throws SQLException {
        String sql = "SELECT codigo, nome, identificador FROM tipomodalidade";
        PreparedStatement sqlConsultar = con.prepareStatement(sql);
        ResultSet rs = sqlConsultar.executeQuery();
        return rs;
    }

    public List consultarParaImpressao(String filtro, String ordem, String campoOrdenacao, Integer empresa) throws SQLException {

        ResultSet rs = getRS(empresa);
        List lista = new ArrayList();

        while (rs.next()) {

            TipoModalidadeVO mod = new TipoModalidadeVO();
            String geral = rs.getString("codigo") + rs.getString("nome") + rs.getString("identificador");
            if (geral.toLowerCase().contains(filtro.toLowerCase())) {
                mod.setCodigo(rs.getInt("codigo"));
                mod.setNome(rs.getString("nome"));
                mod.setIdentificador(rs.getInt("identificador"));
                lista.add(mod);
            }
        }
        if (campoOrdenacao.equals("Código")) {
            Ordenacao.ordenarLista(lista, "codigo");
        } else if (campoOrdenacao.equals("Descrição")) {
            Ordenacao.ordenarLista(lista, "nome");
        } else if (campoOrdenacao.equals("Identificador")) {
            Ordenacao.ordenarLista(lista, "identificador");
        }
        if (ordem.contains("desc")) {
            Collections.reverse(lista);
        }
        return lista;
    }

    @Override
    public List consultarPorNome(String valorConsulta) throws Exception {
        String sqlStr = "SELECT * FROM TipoModalidade WHERE upper(nome) like('" + valorConsulta.toUpperCase() + "%') ORDER BY nome";
        Statement stm = con.createStatement();
        ResultSet tabelaResultado = stm.executeQuery(sqlStr);

        return (montarDadosConsulta(tabelaResultado));
    }

    public static List<TipoModalidadeVO> montarDadosConsulta(ResultSet tabelaResultado) throws Exception {
        List<TipoModalidadeVO> vetResultado = new ArrayList<TipoModalidadeVO>();
        while (tabelaResultado.next()) {
            TipoModalidadeVO obj = montarDados(tabelaResultado);
            vetResultado.add(obj);
        }
        return vetResultado;
    }
    public static TipoModalidadeVO montarDados(ResultSet dadosSQL) throws Exception {
        TipoModalidadeVO obj = new TipoModalidadeVO();
        obj.setCodigo(dadosSQL.getInt("codigo"));
        obj.setNome(dadosSQL.getString("nome"));
        obj.setIdentificador(dadosSQL.getInt("identificador"));
        obj.setNovoObj(false);
        return obj;
    }

}

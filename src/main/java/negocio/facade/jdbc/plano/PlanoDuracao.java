package negocio.facade.jdbc.plano;

import negocio.comuns.plano.PlanoDuracaoCreditoTreinoVO;
import negocio.comuns.plano.PlanoDuracaoVO;
import negocio.comuns.plano.PlanoVO;
import negocio.comuns.utilitarias.ConsistirException;
import negocio.comuns.utilitarias.Uteis;
import negocio.facade.jdbc.arquitetura.SuperEntidade;
import negocio.interfaces.plano.PlanoDuracaoCreditoTreinoInterfaceFacade;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.Statement;
import java.util.ArrayList;
import java.util.Iterator;
import java.util.List;

/**
 * Classe de persistência que encapsula todas as operações de manipulação dos dados da classe <code>PlanoDuracaoVO</code>.
 * Responsável por implementar operações como incluir, alterar, excluir e consultar pertinentes a classe <code>PlanoDuracaoVO</code>.
 * Encapsula toda a interação com o banco de dados.
 * @see PlanoDuracaoVO
 * @see SuperEntidade
 * @see Plano
 */
public class PlanoDuracao extends SuperEntidade {    

    public PlanoDuracao() throws Exception {
        super();
        setIdEntidade("Plano");
    }

    public PlanoDuracao(Connection con) throws Exception {
    	super(con);
        setIdEntidade("Plano");
	}

	/**
     * Operação responsável por retornar um novo objeto da classe <code>PlanoDuracaoVO</code>.
     */
    public PlanoDuracaoVO novo() throws Exception {
        incluir(getIdEntidade());
        PlanoDuracaoVO obj = new PlanoDuracaoVO();
        return obj;
    }

    /**
     * Operação responsável por incluir no banco de dados um objeto da classe <code>PlanoDuracaoVO</code>.
     * Primeiramente valida os dados (<code>validarDados</code>) do objeto. Verifica a conexão com o banco de dados e a permissão do usuário
     * para realizar esta operacão na entidade.
     * Isto, através da operação <code>incluir</code> da superclasse.
     * @param obj  Objeto da classe <code>PlanoDuracaoVO</code> que será gravado no banco de dados.
     * @exception Exception Caso haja problemas de conexão, restrição de acesso ou validação de dados.
     */
    public void incluir(PlanoDuracaoVO obj) throws Exception {
        PlanoDuracaoVO.validarDados(obj);
        //  PlanoDuracao.incluir(getIdEntidade());
        obj.realizarUpperCaseDados();
        String sql = "INSERT INTO PlanoDuracao( numeroMeses , plano, nrMaximoParcelasCondPagamento,"
                + " percentualDesconto, valorEspecifico,  tipoValor, tipoOperacao, valorDesejado,"
                + " valorDesejadoMensal, valorDesejadoParcela, carencia, quantidadeDiasExtra,pontos, situacao) VALUES ( ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)";
        PreparedStatement sqlInserir = con.prepareStatement(sql);
//        if (obj.getDuracao().getCodigo().intValue() != 0) {
//            sqlInserir.setInt(1, obj.getDuracao().getCodigo().intValue());
//        } else {
//            sqlInserir.setNull(1, 0);
//        }
        sqlInserir.setInt(1, obj.getNumeroMeses().intValue());
        if (obj.getPlano().intValue() != 0) {
            sqlInserir.setInt(2, obj.getPlano().intValue());
        } else {
            sqlInserir.setNull(2, 0);
        }
        sqlInserir.setInt(3, obj.getNrMaximoParcelasCondPagamento().intValue());
        sqlInserir.setDouble(4, obj.getPercentualDesconto().doubleValue());
        sqlInserir.setDouble(5, obj.getValorEspecifico().doubleValue());
        sqlInserir.setString(6, obj.getTipoValor());
        sqlInserir.setString(7, obj.getTipoOperacao());
        sqlInserir.setDouble(8, obj.getValorDesejado());
        sqlInserir.setDouble(9, obj.getValorDesejadoMensal());
        sqlInserir.setDouble(10, obj.getValorDesejadoParcela());
        sqlInserir.setInt(11, obj.getCarencia().intValue());
        sqlInserir.setInt(12, obj.getQuantidadeDiasExtra());
        sqlInserir.setInt(13, obj.getPontos());
        sqlInserir.setBoolean(14, obj.getSituacao());
        sqlInserir.execute();
        obj.setCodigo(obterValorChavePrimariaCodigo());
        obj.setNovoObj(new Boolean(false));
        PlanoCondicaoPagamento planoCondicaoPagamentoDAO = new PlanoCondicaoPagamento(con);
        PlanoDuracaoCreditoTreino planoDuracaoCreditoTreinoDAO = new PlanoDuracaoCreditoTreino(con);

        planoCondicaoPagamentoDAO.incluirPlanoCondicaoPagamentos(obj.getCodigo(), obj.getPlanoCondicaoPagamentoVOs());
        if ((obj.getListaPlanoDuracaoCreditoTreino() != null) && (obj.getListaPlanoDuracaoCreditoTreino().size() > 0)){
            for (PlanoDuracaoCreditoTreinoVO planoDuracaoCreditoTreinoVO: obj.getListaPlanoDuracaoCreditoTreino()){
                planoDuracaoCreditoTreinoVO.setPlanoDuracaoVO(obj);
            }
            planoDuracaoCreditoTreinoDAO.incluir(obj.getListaPlanoDuracaoCreditoTreino());
        }
        planoDuracaoCreditoTreinoDAO.incluirSessao(obj);

        planoCondicaoPagamentoDAO = null;
        planoDuracaoCreditoTreinoDAO = null;
    }

    /**
     * Operação responsável por alterar no BD os dados de um objeto da classe <code>PlanoDuracaoVO</code>.
     * Sempre utiliza a chave primária da classe como atributo para localização do registro a ser alterado.
     * Primeiramente valida os dados (<code>validarDados</code>) do objeto. Verifica a conexão com o banco de dados e a permissão do usuário
     * para realizar esta operacão na entidade.
     * Isto, através da operação <code>alterar</code> da superclasse.
     * @param obj    Objeto da classe <code>PlanoDuracaoVO</code> que será alterada no banco de dados.
     * @exception Exception Caso haja problemas de conexão, restrição de acesso ou validação de dados.
     */
    public void alterar(PlanoDuracaoVO obj) throws Exception {
        PlanoDuracaoVO.validarDados(obj);
        obj.realizarUpperCaseDados();
        String sql = "UPDATE PlanoDuracao set numeroMeses=?, plano=?, nrMaximoParcelasCondPagamento=?,percentualDesconto=?, valorEspecifico=?, tipoValor=?, tipoOperacao=?, valorDesejado=?, valorDesejadoMensal = ?, valorDesejadoParcela = ?, carencia = ?, quantidadeDiasExtra = ?, pontos = ?, situacao = ? WHERE ((codigo = ?))";
        PreparedStatement sqlAlterar = con.prepareStatement(sql);
//        if (obj.getDuracao().getCodigo().intValue() != 0) {
//            sqlAlterar.setInt(1, obj.getDuracao().getCodigo().intValue());
//        } else {
//            sqlAlterar.setNull(1, 0);
//        }
        sqlAlterar.setInt(1, obj.getNumeroMeses().intValue());
        if (obj.getPlano().intValue() != 0) {
            sqlAlterar.setInt(2, obj.getPlano().intValue());
        } else {
            sqlAlterar.setNull(2, 0);
        }
        sqlAlterar.setInt(3, obj.getNrMaximoParcelasCondPagamento().intValue());
        sqlAlterar.setDouble(4, obj.getPercentualDesconto().doubleValue());
        sqlAlterar.setDouble(5, obj.getValorEspecifico().doubleValue());
        sqlAlterar.setString(6, obj.getTipoValor());
        sqlAlterar.setString(7, obj.getTipoOperacao());
        sqlAlterar.setDouble(8, obj.getValorDesejado());
        sqlAlterar.setDouble(9, obj.getValorDesejadoMensal());
        sqlAlterar.setDouble(10, obj.getValorDesejadoParcela());
        sqlAlterar.setInt(11, obj.getCarencia().intValue());
        sqlAlterar.setInt(12, obj.getQuantidadeDiasExtra());
        sqlAlterar.setInt(13, obj.getPontos());
        sqlAlterar.setBoolean(14, obj.getSituacao());
        sqlAlterar.setInt(15, obj.getCodigo().intValue());
        sqlAlterar.execute();
        PlanoCondicaoPagamento planoCondicaoPagamentoDAO = new PlanoCondicaoPagamento(con);
        PlanoDuracaoCreditoTreino planoDuracaoCreditoTreinoDAO = new PlanoDuracaoCreditoTreino(con);

        planoCondicaoPagamentoDAO.alterarPlanoCondicaoPagamentos(obj.getCodigo(), obj.getPlanoCondicaoPagamentoVOs());
        if ((obj.getListaPlanoDuracaoCreditoTreino() != null) && (obj.getListaPlanoDuracaoCreditoTreino().size() > 0)){
            planoDuracaoCreditoTreinoDAO.excluir(obj);
            planoDuracaoCreditoTreinoDAO.incluir(obj.getListaPlanoDuracaoCreditoTreino());
        }
        planoDuracaoCreditoTreinoDAO.alterarSessao(obj);

        planoCondicaoPagamentoDAO = null;
        planoDuracaoCreditoTreinoDAO = null;
    }

    /**
     * Operação responsável por excluir no BD um objeto da classe <code>PlanoDuracaoVO</code>.
     * Sempre localiza o registro a ser excluído através da chave primária da entidade.
     * Primeiramente verifica a conexão com o banco de dados e a permissão do usuário
     * para realizar esta operacão na entidade.
     * Isto, através da operação <code>excluir</code> da superclasse.
     * @param obj    Objeto da classe <code>PlanoDuracaoVO</code> que será removido no banco de dados.
     * @exception Exception Caso haja problemas de conexão ou restrição de acesso.
     */
    public void excluir(PlanoDuracaoVO obj) throws Exception {
        excluir(getIdEntidade());
        String sqlExcluirCredito = "delete from planoDuracaoCreditoTreino where planoDuracao = ? ";
        PreparedStatement pst = con.prepareStatement(sqlExcluirCredito);
        pst.setInt(1, obj.getCodigo());
        pst.execute();

        String sql = "DELETE FROM PlanoDuracao WHERE ((codigo = ?))";
        PreparedStatement sqlExcluir = con.prepareStatement(sql);
        sqlExcluir.setInt(1, obj.getCodigo().intValue());
        sqlExcluir.execute();
    }

    /**
     * Responsável por realizar uma consulta de <code>PlanoDuracao</code> através do valor do atributo 
     * <code>Integer nrMaximoParcelasCondPagamento</code>. Retorna os objetos com valores iguais ou superiores ao parâmetro fornecido.
     * Faz uso da operação <code>montarDadosConsulta</code> que realiza o trabalho de prerarar o List resultante.
     * @param   controlarAcesso Indica se a aplicação deverá verificar se o usuário possui permissão para esta consulta ou não.
     * @return  List Contendo vários objetos da classe <code>PlanoDuracaoVO</code> resultantes da consulta.
     * @exception Exception Caso haja problemas de conexão ou restrição de acesso.
     */
    public List consultarPorNrMaximoParcelasCondPagamento(Integer valorConsulta, boolean controlarAcesso, int nivelMontarDados) throws Exception {
        consultar(getIdEntidade(), controlarAcesso);
        String sqlStr = "SELECT * FROM PlanoDuracao WHERE nrMaximoParcelasCondPagamento >= " + valorConsulta.intValue() + " ORDER BY nrMaximoParcelasCondPagamento";
        Statement stm = con.createStatement();
        ResultSet tabelaResultado = stm.executeQuery(sqlStr);
        return (montarDadosConsulta(tabelaResultado, nivelMontarDados, this.con));
    }

    /**
     * Responsável por realizar uma consulta de <code>PlanoDuracao</code> através do valor do atributo 
     * <code>descricao</code> da classe <code>Plano</code>
     * Faz uso da operação <code>montarDadosConsulta</code> que realiza o trabalho de prerarar o List resultante.
     * @return  List Contendo vários objetos da classe <code>PlanoDuracaoVO</code> resultantes da consulta.
     * @exception Exception Caso haja problemas de conexão ou restrição de acesso.
     */
    public List consultarPorDescricaoPlano(String valorConsulta, int nivelMontarDados) throws Exception {
        consultar(getIdEntidade(), true);
        String sqlStr = "SELECT PlanoDuracao.* FROM PlanoDuracao, Plano WHERE PlanoDuracao.plano = Plano.codigo and upper( Plano.descricao ) like('" + valorConsulta.toUpperCase() + "%') ORDER BY Plano.descricao";
        Statement stm = con.createStatement();
        ResultSet tabelaResultado = stm.executeQuery(sqlStr);
        return montarDadosConsulta(tabelaResultado, nivelMontarDados, this.con);
    }

    /**
     * Responsável por realizar uma consulta de <code>PlanoDuracao</code> através do valor do atributo 
     * <code>numeroMeses</code> da classe <code>Duracao</code>
     * Faz uso da operação <code>montarDadosConsulta</code> que realiza o trabalho de prerarar o List resultante.
     * @return  List Contendo vários objetos da classe <code>PlanoDuracaoVO</code> resultantes da consulta.
     * @exception Exception Caso haja problemas de conexão ou restrição de acesso.
     */
    public List consultarPorNumeroMesesDuracao(Integer valorConsulta, int nivelMontarDados) throws Exception {
        consultar(getIdEntidade(), true);
        String sqlStr = "SELECT PlanoDuracao.* FROM PlanoDuracao, Duracao WHERE PlanoDuracao.duracao = Duracao.codigo and Duracao.numeroMeses >= " + valorConsulta.intValue() + " ORDER BY Duracao.numeroMeses";
        Statement stm = con.createStatement();
        ResultSet tabelaResultado = stm.executeQuery(sqlStr);
        return montarDadosConsulta(tabelaResultado, nivelMontarDados, this.con);
    }

    /**
     * Responsável por realizar uma consulta de <code>PlanoDuracao</code> através do valor do atributo 
     * <code>Integer codigo</code>. Retorna os objetos com valores iguais ou superiores ao parâmetro fornecido.
     * Faz uso da operação <code>montarDadosConsulta</code> que realiza o trabalho de prerarar o List resultante.
     * @param   controlarAcesso Indica se a aplicação deverá verificar se o usuário possui permissão para esta consulta ou não.
     * @return  List Contendo vários objetos da classe <code>PlanoDuracaoVO</code> resultantes da consulta.
     * @exception Exception Caso haja problemas de conexão ou restrição de acesso.
     */
    public List consultarPorCodigo(Integer valorConsulta, boolean controlarAcesso, int nivelMontarDados) throws Exception {
        consultar(getIdEntidade(), controlarAcesso);
        String sqlStr = "SELECT * FROM PlanoDuracao WHERE codigo >= " + valorConsulta.intValue() + " ORDER BY codigo";
        Statement stm = con.createStatement();
        ResultSet tabelaResultado = stm.executeQuery(sqlStr);
        return (montarDadosConsulta(tabelaResultado, nivelMontarDados, this.con));
    }

    /**
     * Responsável por montar os dados de vários objetos, resultantes de uma consulta ao banco de dados (<code>ResultSet</code>).
     * Faz uso da operação <code>montarDados</code> que realiza o trabalho para um objeto por vez.
     * @return  List Contendo vários objetos da classe <code>PlanoDuracaoVO</code> resultantes da consulta.
     */
    public List consultarPorCarencia(Integer valorConsulta, boolean controlarAcesso, int nivelMontarDados) throws Exception {
        consultar(getIdEntidade(), controlarAcesso);
        String sqlStr = "SELECT * FROM PlanoDuracao WHERE carencia >= " + valorConsulta.intValue() + " ORDER BY carencia";
        Statement stm = con.createStatement();
        ResultSet tabelaResultado = stm.executeQuery(sqlStr);
        return (montarDadosConsulta(tabelaResultado, nivelMontarDados, this.con));
    }

    public static List montarDadosConsulta(ResultSet tabelaResultado, int nivelMontarDados, Connection con) throws Exception {
        List vetResultado = new ArrayList();
        while (tabelaResultado.next()) {
            PlanoDuracaoVO obj = new PlanoDuracaoVO();
            obj = montarDados(tabelaResultado, nivelMontarDados, con);
            vetResultado.add(obj);
        }
        return vetResultado;
    }

    /**
     * Responsável por montar os dados resultantes de uma consulta ao banco de dados (<code>ResultSet</code>)
     * em um objeto da classe <code>PlanoDuracaoVO</code>.
     * @return  O objeto da classe <code>PlanoDuracaoVO</code> com os dados devidamente montados.
     */
    public static PlanoDuracaoVO montarDados(ResultSet dadosSQL, int nivelMontarDados, Connection con) throws Exception {
        PlanoDuracaoVO obj = new PlanoDuracaoVO();
        obj.setCodigo(new Integer(dadosSQL.getInt("codigo")));
        obj.setNumeroMeses(new Integer(dadosSQL.getInt("numeroMeses")));
        obj.setPlano(new Integer(dadosSQL.getInt("plano")));
        obj.setNrMaximoParcelasCondPagamento(new Integer(dadosSQL.getInt("nrMaximoParcelasCondPagamento")));
        obj.setTipoValor(dadosSQL.getString("tipoValor"));
        obj.setTipoOperacao(dadosSQL.getString("tipoOperacao"));
        obj.setPercentualDesconto(new Double(dadosSQL.getDouble("percentualDesconto")));
        obj.setValorEspecifico(new Double(dadosSQL.getDouble("valorEspecifico")));
        obj.setValorDesejado(new Double(dadosSQL.getDouble("valorDesejado")));
        obj.setValorDesejadoMensal(new Double(dadosSQL.getDouble("valorDesejadoMensal")));
        obj.setValorDesejadoParcela(new Double(dadosSQL.getDouble("valorDesejadoParcela")));
        obj.setValorMaximoDescontoPlano(new Double(dadosSQL.getDouble("valorMaximoDescontoPlano")));
        obj.setCarencia(new Integer(dadosSQL.getInt("carencia")));
        obj.setQuantidadeDiasExtra(dadosSQL.getInt("quantidadeDiasExtra"));
        obj.setPontos(dadosSQL.getInt("pontos"));
        obj.setSituacao(dadosSQL.getBoolean("situacao"));
        obj.setNovoObj(new Boolean(false));

        if (nivelMontarDados == Uteis.NIVELMONTARDADOS_DADOSBASICOS) {
            return obj;
        }
        PlanoCondicaoPagamento planoCondicaoPagamento = new PlanoCondicaoPagamento(con);
        PlanoDuracaoCreditoTreinoInterfaceFacade planoDuracaoCreditoTreino = new PlanoDuracaoCreditoTreino(con);
        obj.setPlanoCondicaoPagamentoVOs(planoCondicaoPagamento.consultarPlanoCondicaoPagamentos(obj.getCodigo(), Uteis.NIVELMONTARDADOS_TODOS));
        obj.setListaPlanoDuracaoCreditoTreino(planoDuracaoCreditoTreino.consultar(obj.getCodigo(), null, Uteis.NIVELMONTARDADOS_DADOSBASICOS));
        for (PlanoDuracaoCreditoTreinoVO planoDuracaoCreditoTreinoVO: obj.getListaPlanoDuracaoCreditoTreino()){
            planoDuracaoCreditoTreinoVO.setPlanoDuracaoVO(obj);
        }
        planoCondicaoPagamento = null;
        planoDuracaoCreditoTreino = null;

        //      montarDadosDuracao(obj, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
        return obj;
    }

//    /**
//     * Operação responsável por montar os dados de um objeto da classe <code>DuracaoVO</code> relacionado ao objeto <code>PlanoDuracaoVO</code>.
//     * Faz uso da chave primária da classe <code>DuracaoVO</code> para realizar a consulta.
//     * @param obj  Objeto no qual será montado os dados consultados.
//     */
//    public static void montarDadosDuracao(PlanoDuracaoVO obj, int nivelMontarDados) throws Exception {
//        if (obj.getDuracao().getCodigo().intValue() == 0) {
//            obj.setDuracao(new DuracaoVO());
//            return;
//        }
//        obj.setDuracao(new Duracao().consultarPorChavePrimaria(obj.getDuracao().getCodigo(), nivelMontarDados));
//    }
    /**
     * Operação responsável por excluir todos os objetos da <code>PlanoDuracaoVO</code> no BD.
     * Faz uso da operação <code>excluir</code> disponível na classe <code>PlanoDuracao</code>.
     * @param <code>plano</code> campo chave para exclusão dos objetos no BD.
     * @exception Exception  Erro de conexão com o BD ou restrição de acesso a esta operação.
     */
    public void excluirPlanoDuracaos(Integer plano) throws Exception {
        excluir(getIdEntidade());
        String sql = "DELETE FROM PlanoDuracao WHERE (plano = ?)";
        PreparedStatement sqlExcluir = con.prepareStatement(sql);
        sqlExcluir.setInt(1, plano.intValue());
        sqlExcluir.execute();
    }

    /**
     * Operação responsável por alterar todos os objetos da <code>PlanoDuracaoVO</code> contidos em um Hashtable no BD.
     * Faz uso da operação <code>excluirPlanoDuracaos</code> e <code>incluirPlanoDuracaos</code> disponíveis na classe <code>PlanoDuracao</code>.
     * @param objetos  List com os objetos a serem alterados ou incluídos no BD.
     * @exception Exception  Erro de conexão com o BD ou restrição de acesso a esta operação.
     */
    public void alterarPlanoDuracaos(PlanoVO planoVO, List objetos) throws Exception {

        String str = "DELETE FROM planoDuracao  ";
        StringBuilder where = new StringBuilder(" WHERE plano = ").append(planoVO.getCodigo().intValue());
        Iterator i = objetos.iterator();
        while (i.hasNext()) {
            PlanoDuracaoVO objeto = (PlanoDuracaoVO) i.next();
            where.append(" AND codigo <> ").append(objeto.getCodigo().intValue());
        }
        StringBuilder sqlExcluirCredito = new StringBuilder();
        sqlExcluirCredito.append("delete from planoDuracaoCreditoTreino  \n");
        sqlExcluirCredito.append("where planoDuracao in(")
                                                  .append("select codigo from planoDuracao")
                                                  .append(where.toString())
                                                  .append(")");
        PreparedStatement pst = con.prepareStatement(sqlExcluirCredito.toString());
        pst.execute();

        PreparedStatement sqlExcluir = con.prepareStatement(str + where.toString());
        sqlExcluir.execute();
        Iterator e = objetos.iterator();
        while (e.hasNext()) {
            PlanoDuracaoVO obj = (PlanoDuracaoVO) e.next();
            if (obj.getCodigo().equals(new Integer(0))) {
                obj.setPlano(planoVO.getCodigo());
                incluir(obj);
            } else {
                alterar(obj);
            }
        }

        //        excluirPlanoDuracaos(plano);

        //        incluirPlanoDuracaos(plano, objetos);
    }

    /**
     * Operação responsável por incluir objetos da <code>PlanoDuracaoVO</code> no BD.
     * Garantindo o relacionamento com a entidade principal <code>plano.Plano</code> através do atributo de vínculo.
     * @param objetos List contendo os objetos a serem gravados no BD da classe.
     * @exception Exception  Erro de conexão com o BD ou restrição de acesso a esta operação.
     */
    public void incluirPlanoDuracaos(Integer planoPrm, List objetos) throws Exception {
        Iterator e = objetos.iterator();
        while (e.hasNext()) {
            PlanoDuracaoVO obj = (PlanoDuracaoVO) e.next();
            obj.setPlano(planoPrm);
            incluir(obj);
        }
    }

    /**
     * Operação responsável por consultar todos os <code>PlanoDuracaoVO</code> relacionados a um objeto da classe <code>plano.Plano</code>.
     * @param plano  Atributo de <code>plano.Plano</code> a ser utilizado para localizar os objetos da classe <code>PlanoDuracaoVO</code>.
     * @return List  Contendo todos os objetos da classe <code>PlanoDuracaoVO</code> resultantes da consulta.
     * @exception Exception  Erro de conexão com o BD ou restrição de acesso a esta operação.
     */
    public List consultarPlanoDuracaos(Integer plano, boolean apenasAtivas, int nivelMontarDados) throws Exception {
        consultar(getIdEntidade());
        List objetos = new ArrayList();
        String sql = "SELECT * FROM PlanoDuracao WHERE plano = ?";
        if(apenasAtivas){
            sql += " and situacao = true";
        }
        PreparedStatement sqlConsulta = con.prepareStatement(sql);
        sqlConsulta.setInt(1, plano.intValue());
        ResultSet resultado = sqlConsulta.executeQuery();
        while (resultado.next()) {
            PlanoDuracaoVO novoObj = new PlanoDuracaoVO();
            novoObj = PlanoDuracao.montarDados(resultado, nivelMontarDados, this.con);
            objetos.add(novoObj);
        }
        return objetos;
    }

    /**
     * Operação responsável por localizar um objeto da classe <code>PlanoDuracaoVO</code>
     * através de sua chave primária. 
     * @exception Exception Caso haja problemas de conexão ou localização do objeto procurado.
     */
    public PlanoDuracaoVO consultarPorChavePrimaria(Integer codigoPrm, int nivelMontarDados) throws Exception {
        consultar(getIdEntidade(), false);
        String sql = "SELECT * FROM PlanoDuracao WHERE codigo = ?";
        PreparedStatement sqlConsultar = con.prepareStatement(sql);
        sqlConsultar.setInt(1, codigoPrm.intValue());
        ResultSet tabelaResultado = sqlConsultar.executeQuery();
        if (!tabelaResultado.next()) {
            throw new ConsistirException("Dados Não Encontrados ( PlanoDuracao ).");
        }
        return (montarDados(tabelaResultado, nivelMontarDados, this.con));
    }

    public PlanoDuracaoVO consultarPorPlanoRecorrencia(Integer plano, int nivelMontarDados) throws Exception {
        consultar(getIdEntidade(), false);
        String sql = "SELECT * FROM PlanoDuracao WHERE plano = ?";
        PreparedStatement sqlConsultar = con.prepareStatement(sql);
        sqlConsultar.setInt(1, plano.intValue());
        ResultSet tabelaResultado = sqlConsultar.executeQuery();
        if (!tabelaResultado.next()) {
            throw new ConsistirException("Dados Não Encontrados ( PlanoDuracao ).");
        }
        return (montarDados(tabelaResultado, nivelMontarDados, this.con));
    }
   
    /*
     * 
     */
    public boolean consultarPorChavePrimaria(Integer codigo) throws Exception {
        consultar(getIdEntidade(), false);
        String sql = "SELECT * FROM PlanoDuracao WHERE codigo = ?";
        PreparedStatement sqlConsultar = con.prepareStatement(sql);
        sqlConsultar.setInt(1, codigo.intValue());
        ResultSet tabelaResultado = sqlConsultar.executeQuery();
        if (!tabelaResultado.next()) {
            return false;
        }
        return true;
    }
    
    public PlanoDuracaoVO consultarPorNumeroMesesPlano(Integer nrMeses, Integer plano, int nivelMontarDados) throws Exception {
        consultar(getIdEntidade(), true);
        String sqlStr = "SELECT PlanoDuracao.* FROM PlanoDuracao WHERE numeroMeses = " + nrMeses 
                + " AND plano = "+plano;
        Statement stm = con.createStatement();
        ResultSet tabelaResultado = stm.executeQuery(sqlStr);
        if(tabelaResultado.next()){
            return montarDados(tabelaResultado, nivelMontarDados, this.con);
        }
        return null;
    }

    public List<Integer>consultarDuracaoMesesVigente(Integer codigoEmpresa, Integer codigoPlano)throws Exception{
        List<Integer> lista = new ArrayList<Integer>();
        StringBuilder sql = new StringBuilder();
        sql.append("select distinct pd.numeroMeses \n");
        sql.append("from plano p \n");
        sql.append("inner join planoDuracao pd on pd.plano = p.codigo \n");
        sql.append("where p.empresa = ").append(codigoEmpresa);
        if ((codigoPlano != null) && (codigoPlano >0)){
            sql.append(" and p.codigo = ").append(codigoPlano).append(" \n");
        }
        sql.append(" order by pd.numeroMeses ");
        Statement st = con.createStatement();
        ResultSet rs = st.executeQuery(sql.toString());
        while (rs.next()){
          lista.add(rs.getInt("numeroMeses"));
        }
        return lista;
    }

    public Integer obterPlanoDuracaoMesesMaximo() throws Exception {
        Statement st = con.createStatement();
        ResultSet rs = st.executeQuery("select max(numeromeses) as meses from planoduracao");
        while (rs.next()){
            return rs.getInt("meses");
        }
        return 0;
    }

}

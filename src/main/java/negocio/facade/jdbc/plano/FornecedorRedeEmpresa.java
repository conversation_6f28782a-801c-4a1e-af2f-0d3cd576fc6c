package negocio.facade.jdbc.plano;

import negocio.comuns.plano.FornecedorRedeEmpresaVO;
import negocio.comuns.utilitarias.Uteis;
import negocio.facade.jdbc.arquitetura.SuperEntidade;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.sql.Types;

public class FornecedorRedeEmpresa extends SuperEntidade {

    public FornecedorRedeEmpresa(Connection conexao) throws Exception {
        super(conexao);
    }

    public FornecedorRedeEmpresa() throws Exception {
        super();
    }

    public void inserir(FornecedorRedeEmpresaVO fornecedorRedeEmpresaVO) throws SQLException {
        String sql = "INSERT INTO FornecedorRedeEmpresa " +
                "(fornecedor, chave, dataCadastro, fornecedorReplicado, nomeUnidade, mensagemSituacao, codigoEmpresaDestino) " +
                "VALUES (?,?,?,?,?,?,?)";

        try (PreparedStatement preparedStatement = con.prepareStatement(sql)) {
            int i = 1;
            preparedStatement.setInt(i++, fornecedorRedeEmpresaVO.getFornecedor());
            preparedStatement.setString(i++, fornecedorRedeEmpresaVO.getChave());
            preparedStatement.setTimestamp(i++, Uteis.getTimestamp());
            if (fornecedorRedeEmpresaVO.getFornecedorReplicado() != null) {
                preparedStatement.setInt(i++, fornecedorRedeEmpresaVO.getFornecedorReplicado());
            } else {
                preparedStatement.setNull(i++, Types.INTEGER);
            }
            preparedStatement.setString(i++, fornecedorRedeEmpresaVO.getNomeUnidade());
            preparedStatement.setString(i++, fornecedorRedeEmpresaVO.getMensagemSituacao());
            preparedStatement.setInt(i++, fornecedorRedeEmpresaVO.getCodigoEmpresaDestino());
            preparedStatement.execute();
        }
    }

    public void alterarMensagemSituacao(Integer fornecedor, String chave, String mensagemSituacao, Integer codigoEmpresaDestino) throws SQLException {
        String sql = "UPDATE FornecedorRedeEmpresa SET " +
                "mensagemSituacao = ? " +
                "WHERE fornecedor = ? AND chave = ? AND codigoEmpresaDestino = ?";

        try (PreparedStatement preparedStatement = con.prepareStatement(sql)) {
            int i = 1;
            preparedStatement.setString(i++, mensagemSituacao);
            preparedStatement.setInt(i++, fornecedor);
            preparedStatement.setString(i++, chave);
            preparedStatement.setInt(i++, codigoEmpresaDestino);
            preparedStatement.execute();
        }
    }

    public void alterarDataAtualizacao(Integer fornecedor, String chave, Integer codigoEmpresaDestino) throws SQLException {
        String sql = "UPDATE FornecedorRedeEmpresa SET " +
                "dataatualizacao = ? " +
                "WHERE fornecedor = ? AND chave = ? AND codigoEmpresaDestino = ?";

        try (PreparedStatement preparedStatement = con.prepareStatement(sql)) {
            int i = 1;
            preparedStatement.setTimestamp(i++, Uteis.getTimestamp());
            preparedStatement.setInt(i++, fornecedor);
            preparedStatement.setString(i++, chave);
            preparedStatement.setInt(i++, codigoEmpresaDestino);
            preparedStatement.execute();
        }
    }

    public void alterarDataAtualizacao(Integer fornecedor, String chave, Integer novoFornecedor, String mensagemSituacao, Integer codigoEmpresaDestino) throws SQLException {
        String sql = "UPDATE FornecedorRedeEmpresa SET " +
                "dataatualizacao = ?, fornecedorReplicado = ?, mensagemSituacao = ? " +
                "WHERE fornecedor = ? AND chave = ? AND codigoEmpresaDestino = ?";

        try (PreparedStatement preparedStatement = con.prepareStatement(sql)) {
            int i = 1;
            preparedStatement.setTimestamp(i++, Uteis.getTimestamp());
            preparedStatement.setInt(i++, novoFornecedor);
            preparedStatement.setString(i++, mensagemSituacao);
            preparedStatement.setInt(i++, fornecedor);
            preparedStatement.setString(i++, chave);
            preparedStatement.setInt(i++, codigoEmpresaDestino);
            preparedStatement.execute();
        }
    }

    public void limparDataAtualizacao(Integer fornecedor, String chaveDestino, Integer codigoEmpresaDestino) throws SQLException {
        String sql = "UPDATE FornecedorRedeEmpresa set " +
                "dataatualizacao = ? " +
                "WHERE fornecedor = ? AND chave = ? AND codigoEmpresaDestino = ?";

        try (PreparedStatement preparedStatement = con.prepareStatement(sql)) {
            int i = 1;
            preparedStatement.setTimestamp(i++, null);
            preparedStatement.setInt(i++, fornecedor);
            preparedStatement.setString(i++, chaveDestino);
            preparedStatement.setInt(i++, codigoEmpresaDestino);
            preparedStatement.execute();
        }
    }


    public FornecedorRedeEmpresaVO consultarPorChaveFornecedorECodigoEmpresaDestino(String chave, Integer fornecedor, Integer codigoEmpresaDestino) throws SQLException {
        String sql = "SELECT * FROM FornecedorRedeEmpresa " +
                "WHERE chave = ? AND fornecedor = ? AND codigoEmpresaDestino = ?";

        try (PreparedStatement preparedStatement = con.prepareStatement(sql)) {
            int i = 1;
            preparedStatement.setString(i++, chave);
            preparedStatement.setInt(i++, fornecedor);
            preparedStatement.setInt(i++, codigoEmpresaDestino);
            try (ResultSet resultSet = preparedStatement.executeQuery()) {
                if (resultSet.next()) {
                    return montarDados(resultSet);
                }
            }
        }
        return null;
    }

    public FornecedorRedeEmpresaVO montarDados(ResultSet resultSet) throws SQLException {
        FornecedorRedeEmpresaVO fornecedorRedeEmpresaVO = new FornecedorRedeEmpresaVO();
        fornecedorRedeEmpresaVO.setChave(resultSet.getString("chave"));
        fornecedorRedeEmpresaVO.setCodigo(resultSet.getInt("codigo"));
        fornecedorRedeEmpresaVO.setFornecedor(resultSet.getInt("fornecedor"));
        fornecedorRedeEmpresaVO.setDataAtualizacao(resultSet.getTimestamp("dataatualizacao"));
        fornecedorRedeEmpresaVO.setFornecedorReplicado(resultSet.getInt("fornecedorReplicado"));
        fornecedorRedeEmpresaVO.setNomeUnidade(resultSet.getString("nomeUnidade"));
        fornecedorRedeEmpresaVO.setMensagemSituacao(resultSet.getString("mensagemSituacao"));
        fornecedorRedeEmpresaVO.setCodigoEmpresaDestino(resultSet.getInt("codigoEmpresaDestino"));

        return fornecedorRedeEmpresaVO;
    }
}

package negocio.facade.jdbc.plano;

import negocio.comuns.basico.EmpresaVO;
import negocio.comuns.plano.ConfiguracaoProdutoEmpresaVO;
import negocio.comuns.plano.ProdutoVO;
import negocio.facade.jdbc.arquitetura.SuperEntidade;
import negocio.interfaces.plano.ConfiguracaoProdutoNotaFiscalInterfaceFacade;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.util.ArrayList;
import java.util.List;

public class ConfiguracaoProdutoNotaFiscal extends SuperEntidade implements ConfiguracaoProdutoNotaFiscalInterfaceFacade {

    public ConfiguracaoProdutoNotaFiscal() throws Exception {
        super();
    }

    public ConfiguracaoProdutoNotaFiscal(Connection con) throws Exception {
        super(con);
    }

    @Override
    public void incluir(ConfiguracaoProdutoEmpresaVO obj) throws Exception {
        ConfiguracaoProdutoEmpresaVO.validarDados(obj);
        PreparedStatement stm = con.prepareStatement("INSERT INTO configuracaoprodutoempresa("
                + "produto, empresa, valor) "
                + "VALUES (?, ?, ?)");
        int i = 1;
        stm.setInt(i++, obj.getProduto());
        stm.setInt(i++, obj.getEmpresa().getCodigo());
        stm.setDouble(i++, obj.getValor());
        stm.execute();
        obj.setCodigo(obterValorChavePrimariaCodigo());
        obj.setNovoObj(false);
    }

    @Override
    public void excluir(ConfiguracaoProdutoEmpresaVO obj) throws Exception {
        executarConsulta("DELETE FROM configuracaoprodutoempresa WHERE codigo = " + obj.getCodigo(), con);
    }

    @Override
    public List<ConfiguracaoProdutoEmpresaVO> consultarPorProduto(Integer produto) throws Exception {
        ResultSet rs = criarConsulta("SELECT e.nome as nomeempresa, cfpe.* FROM configuracaoprodutoempresa cfpe"
                + " INNER JOIN empresa e ON e.codigo = cfpe.empresa"
                + " WHERE produto = "+produto,con);
        return montarDadosConulta(rs);
    }

    @Override
    public ConfiguracaoProdutoEmpresaVO consultarPorProdutoEmpresa(Integer produto, Integer empresa) throws Exception {
        ResultSet rs = criarConsulta("SELECT * FROM configuracaoprodutoempresa WHERE produto = "+produto+" and empresa = "+empresa,con);
        return rs.next() ? montarDados(rs) : null;
    }

    @Override
    public void excluirPorProduto(ProdutoVO produto) throws Exception {
        executarConsulta("DELETE FROM configuracaoprodutoempresa WHERE produto = " + produto.getCodigo(), con);
    }

    @Override
    public void alterarPorProduto(ProdutoVO produto) throws Exception {
        excluirPorProduto(produto);
        for (ConfiguracaoProdutoEmpresaVO cpe : produto.getConfiguracoesEmpresa()) {
            cpe.setProduto(produto.getCodigo());
            incluir(cpe);
        }
    }

    public List<ConfiguracaoProdutoEmpresaVO> montarDadosConulta(ResultSet rs) throws Exception{
        List<ConfiguracaoProdutoEmpresaVO> lista = new ArrayList<ConfiguracaoProdutoEmpresaVO>();
        while(rs.next()){
            lista.add(montarDados(rs));
        }
        return lista;
    }

    public ConfiguracaoProdutoEmpresaVO montarDados(ResultSet rs) throws Exception{
        ConfiguracaoProdutoEmpresaVO obj = new ConfiguracaoProdutoEmpresaVO();
        obj.setCodigo(rs.getInt("codigo"));
        obj.setProduto(rs.getInt("produto"));
        obj.setEmpresa(new EmpresaVO());
        obj.getEmpresa().setCodigo(rs.getInt("empresa"));
        try {
            obj.getEmpresa().setNome(rs.getString("nomeempresa"));
        } catch (Exception e) {
        }
        obj.setValor(rs.getDouble("valor"));
        return obj;
    }
}


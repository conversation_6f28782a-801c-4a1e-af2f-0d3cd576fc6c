package negocio.facade.jdbc.plano;

import negocio.comuns.plano.ConfiguracaoProdutoEmpresaPlanoVO;

import negocio.comuns.plano.ProdutoVO;
import negocio.facade.jdbc.arquitetura.SuperEntidade;
import negocio.interfaces.plano.ConfiguracaoProdutoEmpresaPlanoInterfaceFacade;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.util.ArrayList;
import java.util.List;

public class ConfiguracaoProdutoEmpresaPlano extends SuperEntidade implements ConfiguracaoProdutoEmpresaPlanoInterfaceFacade {

    public ConfiguracaoProdutoEmpresaPlano() throws Exception {
        super();
    }

    public ConfiguracaoProdutoEmpresaPlano(Connection con) throws Exception {
        super(con);
    }

    @Override
    public void incluir(ConfiguracaoProdutoEmpresaPlanoVO obj) throws Exception {
        ConfiguracaoProdutoEmpresaPlanoVO.validarDados(obj);
        try (PreparedStatement stm = con.prepareStatement("INSERT INTO configuracaoprodutoempresaplano(" +
                "produto, empresa, plano, valor) VALUES (?, ?, ?, ?)")) {
            int i = 1;
            stm.setInt(i++, obj.getProduto());
            stm.setInt(i++, obj.getEmpresa().getCodigo());
            stm.setInt(i++, obj.getPlano().getCodigo());
            stm.setDouble(i++, obj.getValor());
            stm.execute();
        }
        obj.setCodigo(obterValorChavePrimariaCodigo());
        obj.setNovoObj(false);
    }

    @Override
    public void excluir(ConfiguracaoProdutoEmpresaPlanoVO obj) throws Exception {
        try (PreparedStatement stm = con.prepareStatement("DELETE FROM configuracaoprodutoempresaplano WHERE codigo = ?")) {
             stm.setInt(1, obj.getCodigo());
             stm.execute();
        }
    }


    @Override
    public void excluirPorProduto(ProdutoVO produto) throws Exception {
        try (PreparedStatement stm = con.prepareStatement("DELETE FROM configuracaoprodutoempresaplano WHERE produto = ?")) {
            stm.setInt(1, produto.getCodigo());
            stm.execute();
        }
    }


    public void alterarPorProduto(ProdutoVO produto) throws Exception {
        excluirPorProduto(produto);
        for (ConfiguracaoProdutoEmpresaPlanoVO cpep : produto.getConfiguracoesValorPorPlano()) {
            cpep.setProduto(produto.getCodigo());
            incluir(cpep);
        }
    }

    @Override
    public List<ConfiguracaoProdutoEmpresaPlanoVO> consultarPorProduto(Integer produto) throws Exception {
        try (ResultSet rs = criarConsulta(
                "SELECT e.nome AS nomeempresa, p.descricao AS descricaoplano, cfpe.* " +
                        "FROM configuracaoprodutoempresaplano cfpe " +
                        "INNER JOIN empresa e ON e.codigo = cfpe.empresa " +
                        "INNER JOIN plano p ON p.codigo = cfpe.plano " +
                        "WHERE cfpe.produto = " + produto, con)) {
            return montarDadosConsulta(rs);
        }
    }


    @Override
    public ConfiguracaoProdutoEmpresaPlanoVO consultarPorProdutoEmpresaPlano(Integer produto, Integer empresa, Integer plano) throws Exception {
        try (ResultSet rs = criarConsulta("SELECT * FROM configuracaoprodutoempresaplano WHERE produto = " + produto +
                " AND empresa = " + empresa + " AND plano = " + plano, con)) {
            return rs.next() ? montarDados(rs) : null;
        }
    }

    public List<ConfiguracaoProdutoEmpresaPlanoVO> montarDadosConsulta(ResultSet rs) throws Exception {
        List<ConfiguracaoProdutoEmpresaPlanoVO> lista = new ArrayList<>();
        while (rs.next()) {
            lista.add(montarDados(rs));
        }
        return lista;
    }

    public ConfiguracaoProdutoEmpresaPlanoVO montarDados(ResultSet rs) throws Exception {
        ConfiguracaoProdutoEmpresaPlanoVO obj = new ConfiguracaoProdutoEmpresaPlanoVO();
        obj.setCodigo(rs.getInt("codigo"));
        obj.setProduto(rs.getInt("produto"));
        obj.getEmpresa().setCodigo(rs.getInt("empresa"));
        try {
            obj.getEmpresa().setNome(rs.getString("nomeempresa"));
        } catch (Exception e) {
        }
        obj.getPlano().setCodigo(rs.getInt("plano"));
        try {
            obj.getPlano().setDescricao(rs.getString("descricaoplano"));
        } catch (Exception e) {
        }
        obj.setValor(rs.getDouble("valor"));
        return obj;
    }
}

package negocio.facade.jdbc.plano;


import negocio.comuns.plano.ModeloOrcamentoVO;
import negocio.comuns.utilitarias.ConsistirException;
import negocio.comuns.utilitarias.Ordenacao;
import negocio.comuns.utilitarias.Uteis;
import negocio.facade.jdbc.arquitetura.SuperEntidade;
import negocio.interfaces.plano.ModeloOrcamentoInterfaceFacade;


import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

public class ModeloOrcamento extends SuperEntidade implements ModeloOrcamentoInterfaceFacade {

    private static final String sqlInsert = "INSERT INTO modeloorcamento("
            + " descricao, dataDefinicao, responsavelDefinicao, texto,situacao, imagemLogo, modalidade, pacote ) "
            + "VALUES ( ?, ?, ?, ?, ?, ?, ?, ? )";
    private static final String sqlUpdate = "UPDATE modeloorcamento set descricao=?, "
            + "dataDefinicao=?, responsavelDefinicao=?, texto=?,situacao=?, imagemLogo=?, modalidade=?, pacote=? "
            + "WHERE ((codigo = ?))";


    public ModeloOrcamento() throws Exception {
        super();
        setIdEntidade("ModeloOrcamento");
    }

    public ModeloOrcamento(Connection con) throws Exception {
        super(con);
    }

    @Override
    public String consultarJSON() throws Exception {
        ResultSet rs = getRS();
        StringBuilder json = new StringBuilder();
        json.append("{\"aaData\":[");
        boolean dados = false;
        while (rs.next()) {
            dados = true;
            ResultSet rs1 = buscarModalidade(rs.getInt("modalidade"));
            json.append("[\"").append(rs.getString("codigo")).append("\",");
            json.append("\"").append(Uteis.normalizarStringJSON(rs.getString("descricao").trim())).append("\",");
            json.append("\"").append(Uteis.normalizarStringJSON(rs.getString("responsaveldefinicao").trim())).append("\",");
            while (rs1.next()) {
                json.append("\"").append(Uteis.normalizarStringJSON(rs1.getString("nome"))).append("\",");
            }
            if (rs.getInt("modalidade") == 0) {
                ResultSet rs2 = buscarPacote(rs.getInt("pacote"));
                while (rs2.next()) {
                    json.append("\"").append(Uteis.normalizarStringJSON(rs2.getString("descricao"))).append("\",");
                }
            }
            json.append("\"").append(rs.getDate("datadefinicao")).append("\"],");
        }
        if (dados) {
            json.deleteCharAt(json.toString().length() - 1);
        }
        json.append("]}");
        return json.toString();
    }

    private ResultSet buscarModalidade(int codModalidade) throws SQLException {
        String sql = "SELECT * FROM modalidade where codigo = "+codModalidade;
        PreparedStatement sqlConsultar = con.prepareStatement(sql);
        ResultSet rs = sqlConsultar.executeQuery();
        return rs;
    }

    private ResultSet buscarPacote(int codPacote) throws SQLException {
        String sql = "SELECT * FROM composicao where codigo = "+codPacote;
        PreparedStatement sqlConsultar = con.prepareStatement(sql);
        ResultSet rs = sqlConsultar.executeQuery();
        return rs;
    }

    private ResultSet getRS() throws SQLException {
        String sql = "SELECT\n" + "  ptp.codigo, descricao, u.nome AS responsaveldefinicao, modalidade, pacote, datadefinicao\n" + "FROM modeloorcamento ptp\n" + "  LEFT JOIN usuario u ON ptp.responsaveldefinicao = u.codigo" + "  ORDER BY descricao";
        PreparedStatement sqlConsultar = con.prepareStatement(sql);
        ResultSet rs = sqlConsultar.executeQuery();
        return rs;
    }

    public ModeloOrcamentoVO consultarPorChavePrimaria(Integer codigoPrm, int nivelMontarDados) throws Exception {
        consultar(getIdEntidade(), false);
        String sql = "SELECT texto, situacao, responsaveldefinicao, imagemlogo, modalidade, pacote, datadefinicao, descricao, codigo  FROM modeloorcamento WHERE codigo = ?";
        PreparedStatement sqlConsultar = con.prepareStatement(sql);
        sqlConsultar.setInt(1, codigoPrm);
        ResultSet tabelaResultado = sqlConsultar.executeQuery();
        if (!tabelaResultado.next()) {
            throw new ConsistirException("Dados Não Encontrados ( ModeloOrcamento ).");
        }
        return (montarDados(tabelaResultado, nivelMontarDados, this.con));
    }

    public List<ModeloOrcamentoVO> consultarTodosAtivos() throws Exception {
        List<ModeloOrcamentoVO> modelosOrcamento = new ArrayList<>();
        consultar(getIdEntidade(), false);
        String sql = "SELECT *  FROM modeloorcamento WHERE situacao = 'AT'";
        PreparedStatement sqlConsultar = con.prepareStatement(sql);
        ResultSet tabelaResultado = sqlConsultar.executeQuery();
        while(tabelaResultado.next()){
            modelosOrcamento.add(montarDados(tabelaResultado, Uteis.NIVELMONTARDADOS_TODOS, this.con));
        }
        return modelosOrcamento;
    }

    public static ModeloOrcamentoVO montarDados(ResultSet dadosSQL, int nivelMontarDados, Connection con) throws Exception {
        ModeloOrcamentoVO obj = new ModeloOrcamentoVO();
        obj.setCodigo(dadosSQL.getInt("codigo"));
        obj.setDescricao(dadosSQL.getString("descricao"));
        obj.setSituacao(dadosSQL.getString("situacao"));
        obj.setDataDefinicao(dadosSQL.getDate("dataDefinicao"));
        obj.getResponsavelDefinicao().setCodigo(dadosSQL.getInt("responsavelDefinicao"));
        obj.setTexto(dadosSQL.getString("texto"));
        obj.setImagemModelo(dadosSQL.getBytes("imagemlogo"));
        obj.setCodModalidade(dadosSQL.getInt("modalidade"));
        obj.setCodPacote(dadosSQL.getInt("pacote"));
        obj.setNovoObj(false);
        if (nivelMontarDados == Uteis.NIVELMONTARDADOS_DADOSBASICOS) {
            return obj;
        }

        return obj;
    }

    /**
     * Método que procura comentário em HTML <!-- -->
     * considerando espaços ou letras ou números ou símbolos especiais
     * <AUTHOR>
     * @param texto
     * @return o texto sem os comentários
     */
    public String apagarComentariosHTML(String texto) {
        texto = texto.replaceAll("(?s)<!--.*?-->", "");
        return texto;
    }

    private PreparedStatement prepararIncluir(ModeloOrcamentoVO obj) throws Exception {
        ModeloOrcamentoVO.validarDados(obj);
        incluir(getIdEntidade());
        obj.realizarUpperCaseDados();

        PreparedStatement sqlInserir = con.prepareStatement(sqlInsert);
        sqlInserir.setString(1, obj.getDescricao());
        sqlInserir.setDate(2, Uteis.getDataJDBC(obj.getDataDefinicao()));
        if (obj.getResponsavelDefinicao().getCodigo() != 0) {
            sqlInserir.setInt(3, obj.getResponsavelDefinicao().getCodigo());
        } else {
            sqlInserir.setNull(3, 0);
        }
        obj.setTexto(apagarComentariosHTML(obj.getTexto()));
        sqlInserir.setString(4, obj.getTexto());
        sqlInserir.setString(5, obj.getSituacao());
        sqlInserir.setBytes(6, obj.getImagemModelo());
        if (obj.getCodModalidade() != 0) {
            sqlInserir.setInt(7, obj.getCodModalidade());
        } else {
            sqlInserir.setNull(7, 0);
        }
        if (obj.getCodPacote() != 0) {
            sqlInserir.setInt(8, obj.getCodPacote());
        } else {
            sqlInserir.setNull(8, 0);
        }

        return sqlInserir;
    }

    private PreparedStatement prepararAlterar(ModeloOrcamentoVO obj)
            throws Exception {
        ModeloOrcamentoVO.validarDados(obj);
        alterar(getIdEntidade());
        obj.realizarUpperCaseDados();

        PreparedStatement sqlAlterar = con.prepareStatement(sqlUpdate);
        sqlAlterar.setString(1, obj.getDescricao());
        sqlAlterar.setDate(2, Uteis.getDataJDBC(obj.getDataDefinicao()));
        if (obj.getResponsavelDefinicao().getCodigo().intValue() != 0) {
            sqlAlterar.setInt(3, obj.getResponsavelDefinicao().getCodigo().intValue());
        } else {
            sqlAlterar.setNull(3, 0);
        }
        obj.setTexto(apagarComentariosHTML(obj.getTexto()));
        sqlAlterar.setString(4, obj.getTexto());
        sqlAlterar.setString(5, obj.getSituacao());
        sqlAlterar.setBytes(6, obj.getImagemModelo());
        if (obj.getCodModalidade() != 0) {
            sqlAlterar.setInt(7, obj.getCodModalidade());
        } else {
            sqlAlterar.setNull(7, 0);
        }
        if (obj.getCodPacote() != 0) {
            sqlAlterar.setInt(8, obj.getCodPacote());
        } else {
            sqlAlterar.setNull(8, 0);
        }
        sqlAlterar.setInt(9, obj.getCodigo().intValue());
        return sqlAlterar;
    }

    /**
     * Operação responsável por incluir no banco de dados um objeto da classe <code>PlanoTextoPadraoVO</code>.
     * Primeiramente valida os dados (<code>validarDados</code>) do objeto. Verifica a conexão com o banco de dados e a permissão do usuário
     * para realizar esta operacão na entidade.
     * Isto, através da operação <code>incluir</code> da superclasse.
     * @param obj  Objeto da classe <code>PlanoTextoPadraoVO</code> que será gravado no banco de dados.
     * @exception Exception Caso haja problemas de conexão, restrição de acesso ou validação de dados.
     */
    public void incluir(ModeloOrcamentoVO obj) throws Exception {
        try {
            con.setAutoCommit(false);
            PreparedStatement sqlInserir = prepararIncluir(obj);
            sqlInserir.execute();
            obj.setCodigo(obterValorChavePrimariaCodigo());
            //obj.setListaTagUtilizado(obterTagsAlteradas(obj.getTexto(), obj.getCodigo(), obj.getTipoContrato()));
            //getFacade().getPlanoTextoPadraoTag().incluirPlanoTextoPadraoTag(obj.getCodigo(), obj.getListaTagUtilizado());
            obj.setNovoObj(new Boolean(false));
            con.commit();
        } catch (Exception e) {
            obj.setNovoObj(new Boolean(true));
            con.rollback();
            con.setAutoCommit(true);
            throw e;
        } finally {
            con.setAutoCommit(true);
        }
    }

    /**
     * Operação responsável por incluir no banco de dados um objeto da classe <code>PlanoTextoPadraoVO</code>.
     * Primeiramente valida os dados (<code>validarDados</code>) do objeto. Verifica a conexão com o banco de dados e a permissão do usuário
     * para realizar esta operacão na entidade.
     * Isto, através da operação <code>incluir</code> da superclasse.
     * @param obj  Objeto da classe <code>PlanoTextoPadraoVO</code> que será gravado no banco de dados.
     * @exception Exception Caso haja problemas de conexão, restrição de acesso ou validação de dados.
     */
    public void incluirSemCommit(ModeloOrcamentoVO obj) throws Exception {
        try {
            PreparedStatement sqlInserir = prepararIncluir(obj);
            sqlInserir.execute();
            obj.setCodigo(obterValorChavePrimariaCodigo());
            //obj.setListaTagUtilizado(obterTagsAlteradas(obj.getTexto(), obj.getCodigo(), obj.getTipoContrato()));
            //getFacade().getPlanoTextoPadraoTag().incluirPlanoTextoPadraoTag(obj.getCodigo(), obj.getListaTagUtilizado());
            obj.setNovoObj(false);
        } catch (Exception e) {
            obj.setNovoObj(true);
            throw e;
        }
    }

    /**
     * Operação responsável por alterar no BD os dados de um objeto da classe <code>PlanoTextoPadraoVO</code>.
     * Sempre utiliza a chave primária da classe como atributo para localização do registro a ser alterado.
     * Primeiramente valida os dados (<code>validarDados</code>) do objeto. Verifica a conexão com o banco de dados e a permissão do usuário
     * para realizar esta operacão na entidade.
     * Isto, através da operação <code>alterar</code> da superclasse.
     * @param obj    Objeto da classe <code>PlanoTextoPadraoVO</code> que será alterada no banco de dados.
     * @exception Exception Caso haja problemas de conexão, restrição de acesso ou validação de dados.
     */
    public void alterar(ModeloOrcamentoVO obj) throws Exception {
        try {
            con.setAutoCommit(false);
            alterarSemCommit(obj);
            con.commit();
        } catch (Exception e) {
            con.rollback();
            con.setAutoCommit(true);
            throw e;
        } finally {
            con.setAutoCommit(true);
        }
    }

    public void alterarSemCommit(ModeloOrcamentoVO obj) throws Exception{
        PreparedStatement sqlAlterar = prepararAlterar(obj);
        sqlAlterar.execute();
        //obj.setListaTagUtilizado(obterTagsAlteradas(obj.getTexto(), obj.getCodigo(), obj.getTipoContrato()));
        //getFacade().getPlanoTextoPadraoTag().alterarPlanoTextoPadraoTag(obj.getCodigo(), obj.getListaTagUtilizado());
    }

    /**
     * Operação responsável por excluir no BD um objeto da classe <code>PlanoTextoPadraoVO</code>.
     * Sempre localiza o registro a ser excluído através da chave primária da entidade.
     * Primeiramente verifica a conexão com o banco de dados e a permissão do usuário
     * para realizar esta operacão na entidade.
     * Isto, através da operação <code>excluir</code> da superclasse.
     * @param obj    Objeto da classe <code>PlanoTextoPadraoVO</code> que será removido no banco de dados.
     * @exception Exception Caso haja problemas de conexão ou restrição de acesso.
     */
    public void excluir(ModeloOrcamentoVO obj) throws Exception {
        try {
            con.setAutoCommit(false);
            excluir(getIdEntidade());
            String sql = "DELETE FROM modeloorcamento WHERE ((codigo = ?))";
            PreparedStatement sqlExcluir = con.prepareStatement(sql);
            sqlExcluir.setInt(1, obj.getCodigo().intValue());
            sqlExcluir.execute();
            //getFacade().getPlanoTextoPadraoTag().excluirPlanoTextoPadraoTag(obj.getCodigo());
            con.commit();
        } catch (Exception e) {
            con.rollback();
            con.setAutoCommit(true);
            throw e;
        } finally {
            con.setAutoCommit(true);
        }
    }

    @Override
    public List consultarParaImpressao(String filtro, String ordem, String campoOrdenacao, int i) throws SQLException {

        ResultSet rs = getRS();
        List lista = new ArrayList();

        while (rs.next()) {

            ModeloOrcamentoVO texto = new ModeloOrcamentoVO();
            String geral = rs.getString("codigo") + rs.getString("descricao") + rs.getString("responsaveldefinicao") + rs.getString("datadefinicao");
            if (geral.toLowerCase().contains(filtro.toLowerCase())) {
                texto.setCodigo(rs.getInt("codigo"));
                texto.setDescricao(rs.getString("descricao"));
                texto.getResponsavelDefinicao().setNome(rs.getString("responsaveldefinicao"));
                texto.setDataDefinicao(rs.getDate("datadefinicao"));
                lista.add(texto);
            }
        }
        if (campoOrdenacao.equals("Código")) {
            Ordenacao.ordenarLista(lista, "codigo");
        } else if (campoOrdenacao.equals("Descrição")) {
            Ordenacao.ordenarLista(lista, "descricao");
        } else if (campoOrdenacao.equals("Responsável")) {
            Ordenacao.ordenarLista(lista, "responsavel_Apresentar");
        } else if (campoOrdenacao.equals("Definição")) {
            Ordenacao.ordenarLista(lista, "dataDefinicao");
        }
        if (ordem.contains("desc")) {
            Collections.reverse(lista);
        }
        return lista;

    }
}

package negocio.facade.jdbc.plano;

import negocio.comuns.plano.PlanoAnuidadeParcelaVO;
import negocio.comuns.plano.PlanoRecorrenciaVO;
import negocio.facade.jdbc.arquitetura.SuperEntidade;
import negocio.interfaces.plano.PlanoAnuidadeParcelaInterfaceFacade;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.List;

public class PlanoAnuidadeParcela extends SuperEntidade implements PlanoAnuidadeParcelaInterfaceFacade {

    public PlanoAnuidadeParcela() throws Exception {
        super();
    }

    public PlanoAnuidadeParcela(Connection conexao) throws Exception {
        super(conexao);
    }

    public void incluirAlterar(PlanoRecorrenciaVO planoRecorrenciaVO) throws Exception {
        excluirTodas(planoRecorrenciaVO.getCodigo());
        if (planoRecorrenciaVO.isParcelarAnuidade()) {
            for (PlanoAnuidadeParcelaVO obj : planoRecorrenciaVO.getParcelasAnuidade()) {
                obj.setPlanoRecorrencia(planoRecorrenciaVO.getCodigo());
                incluir(obj);
            }
        }
    }

    private void incluir(PlanoAnuidadeParcelaVO obj) throws Exception {
        String sql = "INSERT INTO PlanoAnuidadeParcela(planoRecorrencia, numero, valor, parcela) VALUES (?, ?, ?, ?)";
        PreparedStatement sqlInserir = con.prepareStatement(sql);
        int i = 0;
        sqlInserir.setInt(++i, obj.getPlanoRecorrencia());
        sqlInserir.setInt(++i, obj.getNumero());
        sqlInserir.setDouble(++i, obj.getValor());
        sqlInserir.setInt(++i, obj.getParcela());

        sqlInserir.execute();
        obj.setCodigo(obterValorChavePrimariaCodigo());
        obj.setNovoObj(false);
    }

    private void excluirTodas(Integer codigoPlanoRecorrencia) throws SQLException {
        String sql = "DELETE FROM PlanoAnuidadeParcela WHERE planoRecorrencia = "+codigoPlanoRecorrencia;
        PreparedStatement ps = con.prepareStatement(sql);
        ps.execute();
    }

    public List<PlanoAnuidadeParcelaVO> montarDadosConsulta(ResultSet rs) throws Exception {
        List<PlanoAnuidadeParcelaVO> vetResultado = new ArrayList<PlanoAnuidadeParcelaVO>();
        while (rs.next()) {
            vetResultado.add(montarDados(rs));
        }
        return vetResultado;
    }

    public PlanoAnuidadeParcelaVO montarDados(ResultSet rs) throws SQLException {
        PlanoAnuidadeParcelaVO obj = new PlanoAnuidadeParcelaVO();
        obj.setCodigo(rs.getInt("codigo"));
        obj.setPlanoRecorrencia(rs.getInt("planoRecorrencia"));
        obj.setNumero(rs.getInt("numero"));
        obj.setValor(rs.getDouble("valor"));
        obj.setParcela(rs.getInt("parcela"));
        return obj;
    }

    public List<PlanoAnuidadeParcelaVO> consultarPorPlanoRecorrencia(Integer planoRecorrencia) throws Exception {
        String sql = "SELECT * from PlanoAnuidadeParcela WHERE planorecorrencia = "+ planoRecorrencia + " order by numero";
        PreparedStatement ps = con.prepareStatement(sql);
        ResultSet rs = ps.executeQuery();
        return montarDadosConsulta(rs);
    }

    public List<PlanoAnuidadeParcelaVO> consultarPorPlano(Integer plano) throws Exception {
        String sql = "SELECT \n" +
                "pa.* \n" +
                "from PlanoAnuidadeParcela pa \n" +
                "inner join planorecorrencia pr on pr.codigo = pa.planorecorrencia \n" +
                "where pr.plano = "+ plano + " order by numero";
        PreparedStatement ps = con.prepareStatement(sql);
        ResultSet rs = ps.executeQuery();
        return montarDadosConsulta(rs);
    }

    public PlanoAnuidadeParcelaVO consultarPorPlanoNumero(Integer plano, Integer numero) throws Exception {
        String sql = "SELECT \n" +
                "pa.* \n" +
                "from PlanoAnuidadeParcela pa \n" +
                "inner join planorecorrencia pr on pr.codigo = pa.planorecorrencia \n" +
                "where pr.plano = "+ plano + " AND pa.numero = "+numero+" order by numero";
        PreparedStatement ps = con.prepareStatement(sql);
        ResultSet rs = ps.executeQuery();
        if (!rs.next()) {
            return new PlanoAnuidadeParcelaVO();
        }
        return montarDados(rs);
    }
}

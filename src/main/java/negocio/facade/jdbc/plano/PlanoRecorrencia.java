package negocio.facade.jdbc.plano;

import negocio.comuns.arquitetura.UsuarioVO;
import negocio.comuns.basico.EmpresaVO;
import negocio.comuns.basico.PessoaVO;
import negocio.comuns.contrato.ContratoVO;
import negocio.comuns.contrato.MovProdutoVO;
import negocio.comuns.financeiro.MovParcelaVO;
import negocio.comuns.financeiro.MovProdutoParcelaVO;
import negocio.comuns.plano.CondicaoPagamentoVO;
import negocio.comuns.plano.PlanoAnuidadeParcelaVO;
import negocio.comuns.plano.PlanoCondicaoPagamentoVO;
import negocio.comuns.plano.PlanoDuracaoVO;
import negocio.comuns.plano.PlanoEmpresaVO;
import negocio.comuns.plano.PlanoProdutoSugeridoVO;
import negocio.comuns.plano.PlanoRecorrenciaParcelaVO;
import negocio.comuns.plano.PlanoRecorrenciaParcelaWS;
import negocio.comuns.plano.PlanoRecorrenciaVO;
import negocio.comuns.plano.PlanoVO;
import negocio.comuns.plano.ProdutoVO;
import negocio.comuns.plano.enumerador.TipoProduto;
import negocio.comuns.utilitarias.Calendario;
import negocio.comuns.utilitarias.ConsistirException;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisValidacao;
import negocio.facade.jdbc.arquitetura.SuperEntidade;
import negocio.facade.jdbc.basico.PlanoTipoVO;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.sql.Statement;
import java.sql.Types;
import java.util.ArrayList;
import java.util.Date;
import java.util.Iterator;
import java.util.List;

/**
 * Classe de persistência que encapsula todas as operações de manipulação dos dados da classe <code>PlanoRecorrenciaVO</code>.
 * Responsável por implementar operações como incluir, alterar, excluir e consultar pertinentes a classe <code>PlanoRecorrenciaVO</code>.
 * Encapsula toda a interação com o banco de dados.
 * @see PlanoRecorrenciaVO
 * @see SuperEntidade
 * @see Plano
 */
public class PlanoRecorrencia extends SuperEntidade {

    public PlanoRecorrencia() throws Exception {
        super();
        setIdEntidade("Plano");
    }

    public PlanoRecorrencia(Connection conexao) throws Exception {
    	super(conexao);
        setIdEntidade("Plano");
	}

	/**
     * Operação responsável por retornar um novo objeto da classe <code>PlanoRecorrenciaVO</code>.
     */
    public PlanoRecorrenciaVO novo() throws Exception {
        incluir(getIdEntidade());
        return new PlanoRecorrenciaVO();
    }

    /**
     * Operação responsável por incluir no banco de dados um objeto da classe <code>PlanoRecorrenciaVO</code>.
     * Primeiramente valida os dados (<code>validarDados</code>) do objeto. Verifica a conexão com o banco de dados e a permissão do usuário
     * para realizar esta operacão na entidade.
     * Isto, através da operação <code>incluir</code> da superclasse.
     * @param obj  Objeto da classe <code>PlanoRecorrenciaVO</code> que será gravado no banco de dados.
     * @exception Exception Caso haja problemas de conexão, restrição de acesso ou validação de dados.
     */
    public void incluir(PlanoRecorrenciaVO obj) throws Exception {
        PlanoRecorrenciaVO.validarDados(obj);
        String sql = "INSERT INTO PlanoRecorrencia( taxaAdesao , valorAnuidade, diaanuidade,"
                + "  mesanuidade, duracaoplano, qtddiasvenccancelauto,"
                + " renovavelautomaticamente, plano, valormensal,naoRenovarParcelaVencida,"
                + " naoCobrarAnuidadeProporcional, anuidadeNaParcela, parcelaAnuidade, cancelamentoProporcional, qtdDiasCobrarProximaParcela, qtdDiasCobrarAnuidadeTotal, "
                + " gerarParcelasValorDiferente, gerarParcelasValorDiferenteRenovacao, parcelarAnuidade, cancelamentoProporcionalSomenteRenovacao)"
                + "VALUES ( ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ? ,?, ?, ?, ?, ?, ?, ?,?)";
        try (PreparedStatement sqlInserir = con.prepareStatement(sql)) {
            int i = 1;
            sqlInserir.setDouble(i++, obj.getTaxaAdesao());
            sqlInserir.setDouble(i++, obj.getValorAnuidade());
            if (obj.getDiaAnuidade() != null) {
                sqlInserir.setInt(i++, obj.getDiaAnuidade());
            } else {
                sqlInserir.setInt(i++, 0);
            }
            if (obj.getMesAnuidade() != null) {
                sqlInserir.setInt(i++, obj.getMesAnuidade());
            } else {
                sqlInserir.setInt(i++, 0);
            }
            sqlInserir.setInt(i++, obj.getDuracaoPlano());
            sqlInserir.setInt(i++, obj.getQtdDiasAposVencimentoCancelamentoAutomatico());
            sqlInserir.setBoolean(i++, obj.getRenovavelAutomaticamente());
            if (obj.getPlano() != 0) {
                sqlInserir.setInt(i++, obj.getPlano());
            } else {
                sqlInserir.setNull(i++, Types.NULL);
            }
            sqlInserir.setDouble(i++, obj.getValorMensal());
            sqlInserir.setBoolean(i++, obj.getNaoRenovarParcelaVencida());
            sqlInserir.setBoolean(i++, obj.getNaoCobrarAnuidadeProporcional());
            sqlInserir.setBoolean(i++, obj.isAnuidadeNaParcela());
            sqlInserir.setInt(i++, obj.getParcelaAnuidade());
            sqlInserir.setBoolean(i++, obj.isCancelamentoProporcional());
            sqlInserir.setInt(i++, obj.getQtdDiasCobrarProximaParcela());
            sqlInserir.setInt(i++, obj.getQtdDiasCobrarAnuidadeTotal());
            sqlInserir.setBoolean(i++, obj.isGerarParcelasValorDiferente());
            sqlInserir.setBoolean(i++, obj.isGerarParcelasValorDiferenteRenovacao());
            sqlInserir.setBoolean(i++, obj.isParcelarAnuidade());
            sqlInserir.setBoolean(i++, obj.isCancelamentoProporcionalSomenteRenovacao());
            sqlInserir.execute();
        }
        obj.setCodigo(obterValorChavePrimariaCodigo());
        obj.setNovoObj(false);

        alterarOuIncluirPlanoDuracaoRecorrente(obj);
        alterarOuIncluirPlanoProdutosSugeridosRecorrencia(obj);
        if(obj.isGerarParcelasValorDiferente()){
            PlanoRecorrenciaParcela planoRecorrenciaParcelaDAO = new PlanoRecorrenciaParcela(con);
            planoRecorrenciaParcelaDAO.incluir(obj);
            planoRecorrenciaParcelaDAO = null;
        }
        alterarOuIncluirPlanoAnuidadeParcela(obj);
    }

    private void alterarOuIncluirPlanoAnuidadeParcela(PlanoRecorrenciaVO obj) throws Exception {
        PlanoAnuidadeParcela planoAnuidadeParcelaDAO = new PlanoAnuidadeParcela(con);
        planoAnuidadeParcelaDAO.incluirAlterar(obj);
        planoAnuidadeParcelaDAO = null;
    }

    private void alterarOuIncluirPlanoProdutosSugeridosRecorrencia(PlanoRecorrenciaVO obj) throws Exception{
        Plano planoDao = new Plano(con);
        PlanoProdutoSugerido planoProdutoSugeridoDAO = new PlanoProdutoSugerido(con);
        PlanoVO plano = planoDao.consultarPorChavePrimaria(obj.getPlano(), Uteis.NIVELMONTARDADOS_DADOSBASICOS);
        if (plano != null){
            List<PlanoProdutoSugeridoVO> lista = planoProdutoSugeridoDAO.consultarPlanoProdutoSugeridos(obj.getPlano(), true, Uteis.NIVELMONTARDADOS_TODOS);
            //excluir produtos sugeridos da recorrência para adicionar conforme configurado
            boolean deveIncluirAnuidade = true;
            boolean deveIncluirAdesao = true;
            for (PlanoProdutoSugeridoVO planoProdutoSugeridoVO : lista) {
                if (planoProdutoSugeridoVO.getProduto().getTipoProduto().equals("TD")){//adesão recorrência
                    deveIncluirAdesao = false;
                    planoProdutoSugeridoVO.setValorProduto(Uteis.arredondarForcando2CasasDecimais(obj.getTaxaAdesao()));
                    planoProdutoSugeridoDAO.alterar(planoProdutoSugeridoVO);
                }else if (planoProdutoSugeridoVO.getProduto().getTipoProduto().equals("TA")){//anuidade recorrência
                    deveIncluirAnuidade = false;
                    planoProdutoSugeridoVO.setValorProduto(Uteis.arredondarForcando2CasasDecimais(obj.getValorAnuidade()));
                    planoProdutoSugeridoDAO.alterar(planoProdutoSugeridoVO);
                }

            }
            if (obj.getValorAnuidade() != 0 && deveIncluirAnuidade){
                Produto produtoDAO = new Produto(con);
                ProdutoVO produto = produtoDAO.criarOuConsultarExisteProdutoPorTipo("ANUIDADE PLANO RECORRENTE", "TA", 0.0);
                produtoDAO = null;

                PlanoProdutoSugeridoVO novoPlanoProduto = new PlanoProdutoSugeridoVO();
                novoPlanoProduto.setObrigatorio(true);
                novoPlanoProduto.setPlano(obj.getPlano());
                novoPlanoProduto.setProduto(produto);
                novoPlanoProduto.setValorProduto(obj.getValorAnuidade());
                planoProdutoSugeridoDAO.incluir(novoPlanoProduto);

            }
            if (obj.getTaxaAdesao() != 0 && deveIncluirAdesao){
                Produto produtoDAO = new Produto(con);
                ProdutoVO produto = produtoDAO.criarOuConsultarExisteProdutoPorTipo("ADESÃO PLANO RECORRENTE", "TD", 0.0);
                produtoDAO = null;

                PlanoProdutoSugeridoVO novoPlanoProduto = new PlanoProdutoSugeridoVO();
                novoPlanoProduto.setObrigatorio(true);
                novoPlanoProduto.setPlano(obj.getPlano());
                novoPlanoProduto.setProduto(produto);
                novoPlanoProduto.setValorProduto(obj.getTaxaAdesao());
                planoProdutoSugeridoDAO.incluir(novoPlanoProduto);
            }
        }
    }

    private void alterarOuIncluirPlanoDuracaoRecorrente(PlanoRecorrenciaVO obj) throws Exception {
        PlanoDuracao planoDuracaoDAO = new PlanoDuracao(con);
        CondicaoPagamento condicaoPagamentoDAO = new CondicaoPagamento(con);
        PlanoCondicaoPagamento planoCondicaoPagamentoDAO = new PlanoCondicaoPagamento(con);

        //incluir/alterar duração padrão de acordo com a fidelidade do plano
        if (obj.getDuracaoPlano() != 0) {
            List<PlanoDuracaoVO> lista = planoDuracaoDAO.consultarPlanoDuracaos(obj.getPlano(),false, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
            
            //verificar se deve atualizar a duraçaõ que já existe, ou criar uma única duração
            //para o plano de recorrência
            PlanoDuracaoVO planoDuracao = new PlanoDuracaoVO();
            if (obj.getPlanoDuracao() != null){
                //Ao incluir o plano, foi necessário já ter o objeto "planoduracao" para o usuário informar as férias.
                planoDuracao = obj.getPlanoDuracao();
            }
            if (!lista.isEmpty()) {
                planoDuracao = lista.get(0);
            }
            planoDuracao = criarPlanoDuracao(obj,planoDuracao);

            if (planoDuracao.getCodigo() == 0) {
                planoDuracaoDAO.incluir(planoDuracao);
            } else {
                planoDuracaoDAO.alterar(planoDuracao);
            }

            //incluir CONDICAO DE PAGAMENTO
            CondicaoPagamentoVO condicao = condicaoPagamentoDAO.
                    criarOuConsultarSeExistePorNome(obj.getDuracaoPlano());

            PlanoCondicaoPagamentoVO planoCondPagamento = new PlanoCondicaoPagamentoVO();
            List<PlanoCondicaoPagamentoVO> listaCondicoes = planoCondicaoPagamentoDAO.consultarPlanoCondicaoPagamentos(planoDuracao.getCodigo(), Uteis.NIVELMONTARDADOS_TODOS);

            if (!listaCondicoes.isEmpty()){
                planoCondPagamento = listaCondicoes.get(0);
            }
            planoCondPagamento.setCondicaoPagamento(condicao);
            planoCondPagamento.setPlanoDuracao(planoDuracao.getCodigo());
            planoCondPagamento.setQtdParcela(0);

            if (planoCondPagamento.getCodigo() == 0){
                planoCondicaoPagamentoDAO.incluir(planoCondPagamento);
            }else{
                planoCondicaoPagamentoDAO.alterar(planoCondPagamento);
            }
        }

        planoDuracaoDAO = null;
        condicaoPagamentoDAO = null;
        planoCondicaoPagamentoDAO = null;
    }

    public static PlanoDuracaoVO criarPlanoDuracao(PlanoRecorrenciaVO obj,PlanoDuracaoVO planoDuracao){
        planoDuracao.setNrMaximoParcelasCondPagamento(obj.getDuracaoPlano());
        planoDuracao.setPlano(obj.getPlano());
        planoDuracao.setValorDesejadoMensal(obj.getValorMensal());
        planoDuracao.setValorDesejado(Uteis.arredondarForcando2CasasDecimais(obj.getValorMensal() * obj.getDuracaoPlano()));
        planoDuracao.setValorDesejadoParcela(obj.getValorMensal());
        planoDuracao.setNumeroMeses(obj.getDuracaoPlano());
        planoDuracao.setTipoValor("VE");
        planoDuracao.setTipoOperacao("RE");
        planoDuracao.setDuracaoPlanoRecorrencia(true);
        return planoDuracao;
    }

    /**
     * Operação responsável por alterar no BD os dados de um objeto da classe <code>PlanoRecorrenciaVO</code>.
     * Sempre utiliza a chave primária da classe como atributo para localização do registro a ser alterado.
     * Primeiramente valida os dados (<code>validarDados</code>) do objeto. Verifica a conexão com o banco de dados e a permissão do usuário
     * para realizar esta operacão na entidade.
     * Isto, através da operação <code>alterar</code> da superclasse.
     * @param obj    Objeto da classe <code>PlanoRecorrenciaVO</code> que será alterada no banco de dados.
     * @exception Exception Caso haja problemas de conexão, restrição de acesso ou validação de dados.
     */
    public void alterar(PlanoRecorrenciaVO obj) throws Exception {
        PlanoRecorrenciaVO.validarDados(obj);
        alterar(getIdEntidade());
        String sql = "UPDATE PlanoRecorrencia set taxaAdesao=?, valorAnuidade=?,diaanuidade=?, "
                + "mesanuidade=?, duracaoplano=?,"
                + "qtddiasvenccancelauto = ?, renovavelautomaticamente = ?, plano = ?, valormensal = ?, naoRenovarParcelaVencida=?, "
                + "naoCobrarAnuidadeProporcional = ?, anuidadeNaParcela = ?, parcelaAnuidade = ?, cancelamentoProporcional = ?, qtdDiasCobrarProximaParcela = ?, qtdDiasCobrarAnuidadeTotal = ?, "
                + "gerarParcelasValorDiferente = ?, gerarParcelasValorDiferenteRenovacao = ?, parcelarAnuidade = ?, cancelamentoProporcionalSomenteRenovacao = ? "
                + "WHERE ((codigo = ?))";
        try (PreparedStatement sqlAlterar = con.prepareStatement(sql)) {
            int i = 1;
            sqlAlterar.setDouble(i++, obj.getTaxaAdesao());
            sqlAlterar.setDouble(i++, obj.getValorAnuidade());
            if (obj.getDiaAnuidade() != null) {
                sqlAlterar.setInt(i++, obj.getDiaAnuidade());
            } else {
                sqlAlterar.setInt(i++, 0);
            }
            if (obj.getMesAnuidade() != null) {
                sqlAlterar.setInt(i++, obj.getMesAnuidade());
            } else {
                sqlAlterar.setInt(i++, 0);
            }
            sqlAlterar.setInt(i++, obj.getDuracaoPlano());
            sqlAlterar.setInt(i++, obj.getQtdDiasAposVencimentoCancelamentoAutomatico());
            sqlAlterar.setBoolean(i++, obj.getRenovavelAutomaticamente());
            if (obj.getPlano() != 0) {
                sqlAlterar.setInt(i++, obj.getPlano());
            } else {
                sqlAlterar.setNull(i++, Types.NULL);
            }
            sqlAlterar.setDouble(i++, obj.getValorMensal());
            sqlAlterar.setBoolean(i++, obj.getNaoRenovarParcelaVencida());
            sqlAlterar.setBoolean(i++, obj.getNaoCobrarAnuidadeProporcional());
            sqlAlterar.setBoolean(i++, obj.isAnuidadeNaParcela());
            sqlAlterar.setInt(i++, obj.getParcelaAnuidade());
            sqlAlterar.setBoolean(i++, obj.isCancelamentoProporcional());
            sqlAlterar.setInt(i++, obj.getQtdDiasCobrarProximaParcela());
            sqlAlterar.setInt(i++, obj.getQtdDiasCobrarAnuidadeTotal());
            sqlAlterar.setBoolean(i++, obj.isGerarParcelasValorDiferente());
            sqlAlterar.setBoolean(i++, obj.isGerarParcelasValorDiferente() ? obj.isGerarParcelasValorDiferenteRenovacao() : false);
            sqlAlterar.setBoolean(i++, obj.isParcelarAnuidade());
            sqlAlterar.setBoolean(i++, obj.isCancelamentoProporcionalSomenteRenovacao());
            sqlAlterar.setInt(i++, obj.getCodigo());
            sqlAlterar.execute();
        }

        alterarOuIncluirPlanoDuracaoRecorrente(obj);
        alterarOuIncluirPlanoProdutosSugeridosRecorrencia(obj);
        PlanoRecorrenciaParcela planoRecorrenciaParcelaDAO = new PlanoRecorrenciaParcela(con);
        if(obj.isGerarParcelasValorDiferente()){
            planoRecorrenciaParcelaDAO.alterar(obj);
        }else{
            planoRecorrenciaParcelaDAO.excluirPorPlanoRecorrencia(obj.getCodigo());
        }
        alterarOuIncluirPlanoAnuidadeParcela(obj);
        planoRecorrenciaParcelaDAO = null;
    }

    /**
     * Operação responsável por excluir no BD um objeto da classe <code>PlanoRecorrenciaVO</code>.
     * Sempre localiza o registro a ser excluído através da chave primária da entidade.
     * Primeiramente verifica a conexão com o banco de dados e a permissão do usuário
     * para realizar esta operacão na entidade.
     * Isto, através da operação <code>excluir</code> da superclasse.
     * @param obj    Objeto da classe <code>PlanoRecorrenciaVO</code> que será removido no banco de dados.
     * @exception Exception Caso haja problemas de conexão ou restrição de acesso.
     */
    public void excluir(PlanoRecorrenciaVO obj) throws Exception {
        excluir(getIdEntidade());
        String sql = "DELETE FROM PlanoRecorrencia WHERE ((codigo = ?))";
        try (PreparedStatement sqlExcluir = con.prepareStatement(sql)) {
            sqlExcluir.setInt(1, obj.getCodigo());
            sqlExcluir.execute();
        }
    }

    /**
     * Responsável por realizar uma consulta de <code>PlanoRecorrencia</code> através do valor do atributo
     * <code>Integer codigo</code>. Retorna os objetos com valores iguais ou superiores ao parâmetro fornecido.
     * Faz uso da operação <code>montarDadosConsulta</code> que realiza o trabalho de prerarar o List resultante.
     * @param   controlarAcesso Indica se a aplicação deverá verificar se o usuário possui permissão para esta consulta ou não.
     * @return  List Contendo vários objetos da classe <code>PlanoRecorrenciaVO</code> resultantes da consulta.
     * @exception Exception Caso haja problemas de conexão ou restrição de acesso.
     */
    public PlanoRecorrenciaVO consultarPorCodigo(Integer valorConsulta, boolean controlarAcesso, int nivelMontarDados) throws Exception {
        consultar(getIdEntidade(), controlarAcesso);
        String sqlStr = "SELECT * FROM PlanoRecorrencia WHERE codigo >= " + valorConsulta + " ORDER BY codigo";
        try (Statement stm = con.createStatement()) {
            try (ResultSet tabelaResultado = stm.executeQuery(sqlStr)) {
                if (!tabelaResultado.next()) {
                    throw new ConsistirException("Dados Não Encontrados ( PlanoRecorrencia ).");
                }
                return (montarDados(tabelaResultado, nivelMontarDados));
            }
        }
    }

    /**
     * Responsável por montar os dados resultantes de uma consulta ao banco de dados (<code>ResultSet</code>)
     * em um objeto da classe <code>PlanoRecorrenciaVO</code>.
     * @return  O objeto da classe <code>PlanoRecorrenciaVO</code> com os dados devidamente montados.
     */
    public PlanoRecorrenciaVO montarDados(ResultSet dadosSQL, int nivelMontarDados) throws Exception {
        PlanoRecorrenciaVO obj = new PlanoRecorrenciaVO();
        obj.setCodigo(dadosSQL.getInt("codigo"));
        obj.setTaxaAdesao(dadosSQL.getDouble("taxaadesao"));
        obj.setPlano(dadosSQL.getInt("plano"));        
        obj.setValorAnuidade(dadosSQL.getDouble("valoranuidade"));
        obj.setDiaAnuidade(dadosSQL.getInt("diaanuidade"));
        obj.setMesAnuidade(dadosSQL.getInt("mesanuidade"));
        obj.setDuracaoPlano(dadosSQL.getInt("duracaoplano"));
        obj.setQtdDiasAposVencimentoCancelamentoAutomatico(dadosSQL.getInt("qtddiasvenccancelauto"));
        obj.setRenovavelAutomaticamente(dadosSQL.getBoolean("renovavelautomaticamente"));
        obj.setNaoRenovarParcelaVencida(dadosSQL.getBoolean("naoRenovarParcelaVencida"));
        obj.setValorMensal(dadosSQL.getDouble("valormensal"));
        obj.setNaoCobrarAnuidadeProporcional(dadosSQL.getBoolean("naoCobrarAnuidadeProporcional"));
        obj.setAnuidadeNaParcela(dadosSQL.getBoolean("anuidadeNaParcela"));
        obj.setParcelaAnuidade(dadosSQL.getInt("parcelaAnuidade"));
        obj.setCancelamentoProporcional(dadosSQL.getBoolean("cancelamentoProporcional"));
        obj.setQtdDiasCobrarProximaParcela(dadosSQL.getInt("qtdDiasCobrarProximaParcela"));
        obj.setQtdDiasCobrarAnuidadeTotal(dadosSQL.getInt("qtdDiasCobrarAnuidadeTotal"));
        obj.setGerarParcelasValorDiferente(dadosSQL.getBoolean("gerarParcelasValorDiferente"));
        obj.setGerarParcelasValorDiferenteRenovacao(dadosSQL.getBoolean("gerarParcelasValorDiferenteRenovacao"));
        obj.setParcelarAnuidade(dadosSQL.getBoolean("parcelarAnuidade"));
        obj.setCancelamentoProporcionalSomenteRenovacao(dadosSQL.getBoolean("cancelamentoProporcionalSomenteRenovacao"));
        obj.setNovoObj(false);

        montarDadosPlanoAnuidadeParcela(obj);

        if (nivelMontarDados == Uteis.NIVELMONTARDADOS_DADOSBASICOS) {
            return obj;
        }
        return obj;
    }

    private void montarDadosPlanoAnuidadeParcela(PlanoRecorrenciaVO obj) throws Exception {
        if (obj.isParcelarAnuidade()) {
            PlanoAnuidadeParcela planoAnuidadeParcelaDAO = new PlanoAnuidadeParcela(this.con);
            obj.setParcelasAnuidade(planoAnuidadeParcelaDAO.consultarPorPlanoRecorrencia(obj.getCodigo()));
            planoAnuidadeParcelaDAO = null;
        } else {
            obj.setParcelasAnuidade(new ArrayList<PlanoAnuidadeParcelaVO>());
        }
    }

    /**
     * Operação responsável por excluir todos os objetos da <code>PlanoRecorrenciaVO</code> no BD.
     * Faz uso da operação <code>excluir</code> disponível na classe <code>PlanoDuracao</code>.
     * @param plano campo chave para exclusão dos objetos no BD.
     * @exception Exception  Erro de conexão com o BD ou restrição de acesso a esta operação.
     */
    public void excluirPlanoRecorrencia(Integer plano) throws Exception {
        excluir(getIdEntidade());
        String sql = "DELETE FROM PlanoRecorrencia WHERE (plano = ?)";
        try (PreparedStatement sqlExcluir = con.prepareStatement(sql)) {
            sqlExcluir.setInt(1, plano);
            sqlExcluir.execute();
        }
    }

    /**
     * Operação responsável por alterar todos os objetos da <code>PlanoRecorrenciaVO</code> contidos em um Hashtable no BD.
     * Faz uso da operação <code>excluirPlanoRecorrencia</code> e <code>incluirPlanoDuracaos</code> disponíveis na classe <code>PlanoRecorrencia</code>.
     * @param objetos  List com os objetos a serem alterados ou incluídos no BD.
     * @exception Exception  Erro de conexão com o BD ou restrição de acesso a esta operação.
     */
    public void alterarPlanoRecorrencia(Integer plano, List objetos) throws Exception {
        String str = "DELETE FROM PlanoRecorrencia WHERE plano = " + plano;
        Iterator i = objetos.iterator();
        while (i.hasNext()) {
            PlanoRecorrenciaVO objeto = (PlanoRecorrenciaVO) i.next();
            str += " AND codigo <> " + objeto.getCodigo();
        }
        try (PreparedStatement sqlExcluir = con.prepareStatement(str)) {
            sqlExcluir.execute();
        }
        Iterator e = objetos.iterator();
        while (e.hasNext()) {
            PlanoRecorrenciaVO obj = (PlanoRecorrenciaVO) e.next();
            if (obj.getCodigo().equals(0)) {
                obj.setPlano(plano);
                incluir(obj);
            } else {
                alterar(obj);
            }
        }
    }

    /**
     * Operação responsável por consultar todos os <code>PlanoRecorrenciaVO</code> relacionados a um objeto da classe <code>plano.Plano</code>.
     * @param plano  Atributo de <code>plano.Plano</code> a ser utilizado para localizar os objetos da classe <code>PlanoRecorrenciaVO</code>.
     * @return List  Contendo todos os objetos da classe <code>PlanoRecorrenciaVO</code> resultantes da consulta.
     * @exception Exception  Erro de conexão com o BD ou restrição de acesso a esta operação.
     */
    public PlanoRecorrenciaVO consultarPlanoRecorrencia(Integer plano, int nivelMontarDados) throws Exception {
        consultar(getIdEntidade());
        String sql = "SELECT * FROM PlanoRecorrencia WHERE plano = ?";
        try (PreparedStatement sqlConsulta = con.prepareStatement(sql)) {
            sqlConsulta.setInt(1, plano);
            try (ResultSet resultado = sqlConsulta.executeQuery()) {
                if (!resultado.next()) {
                    return new PlanoRecorrenciaVO();
                }
                return montarDados(resultado, nivelMontarDados);
            }
        }
    }

    /**
     * Operação responsável por localizar um objeto da classe <code>PlanoRecorrenciaVO</code>
     * através de sua chave primária.
     * @exception Exception Caso haja problemas de conexão ou localização do objeto procurado.
     */
    public PlanoRecorrenciaVO consultarPorChavePrimaria(Integer codigoPrm, int nivelMontarDados) throws Exception {
        consultar(getIdEntidade(), false);
        String sql = "SELECT * FROM PlanoRecorrencia WHERE codigo = ?";
        try (PreparedStatement sqlConsultar = con.prepareStatement(sql)) {
            sqlConsultar.setInt(1, codigoPrm);
            try (ResultSet tabelaResultado = sqlConsultar.executeQuery()) {
                if (!tabelaResultado.next()) {
                    throw new ConsistirException("Dados Não Encontrados ( PlanoRecorrencia ).");
                }
                return (montarDados(tabelaResultado, nivelMontarDados));
            }
        }
    }


    public Boolean consultarPorPlanoNaoRenovarParcelaVencida(int plano, Connection con) throws Exception {
        String sql = "SELECT naorenovarparcelavencida FROM PlanoRecorrencia WHERE plano = ?";
        try (PreparedStatement sqlConsultar = con.prepareStatement(sql)) {
            sqlConsultar.setInt(1, plano);
            try (ResultSet tabelaResultado = sqlConsultar.executeQuery()) {
                return tabelaResultado.next() && tabelaResultado.getBoolean("naorenovarparcelavencida");
            }
        }
    }



    /**
     * Método usado para verificar se existe determinada chave primária
     * da tabela planorecorrencia
     * @param codigo
     * @return
     * @throws Exception
     */
    public boolean consultarPorChavePrimaria(Integer codigo) throws Exception {
        consultar(getIdEntidade(), false);
        String sql = "SELECT * FROM PlanoRecorrencia WHERE codigo = ?";
        try (PreparedStatement sqlConsultar = con.prepareStatement(sql)) {
            sqlConsultar.setInt(1, codigo);
            try (ResultSet tabelaResultado = sqlConsultar.executeQuery()) {
                return tabelaResultado.next();
            }
        }
    }

    public boolean consultarPlanoRenovacaoAutomatica(int codigo) throws Exception {
        String sql = "SELECT renovavelautomaticamente FROM PlanoRecorrencia WHERE codigo = ?";
        try (PreparedStatement sqlConsultar = con.prepareStatement(sql)) {
            sqlConsultar.setInt(1, codigo);
            try (ResultSet tabelaResultado = sqlConsultar.executeQuery()) {
                return tabelaResultado.next() && tabelaResultado.getBoolean("renovavelautomaticamente");
            }
        }
    }

    /**
     * Consulta a quantidade e valor total de parcelas de um plano recorrente com valores diferentes
     * @param codigoPlanoRecorrencia
     *
     * @throws SQLException
     */
    public PlanoRecorrenciaVO consultarTotalParcelasValorDiferente(int codigoPlanoRecorrencia) throws SQLException {
        String sql = "SELECT SUM(valor) as valorTotalParcelas, COUNT(codigo) as totalParcelas\n" +
                "FROM planorecorrenciaparcela "+
                "WHERE planorecorrencia = "+codigoPlanoRecorrencia;

        PlanoRecorrenciaVO planoRecorrenciaVO;
        try (PreparedStatement ps = con.prepareStatement(sql)) {
            try (ResultSet rs = ps.executeQuery()) {
                rs.next();

                planoRecorrenciaVO = new PlanoRecorrenciaVO();
                planoRecorrenciaVO.setValorTotalParcelasValorDirefente(rs.getDouble("valorTotalParcelas"));
                planoRecorrenciaVO.setQuantidadeParcelasValorDiferente(rs.getInt("totalParcelas"));
            }
        }

        return planoRecorrenciaVO;
    }

    public List<PlanoRecorrenciaParcelaVO> calcularParcelasRecorrenciaMensalidade(PlanoRecorrenciaVO planoRecorrencia) throws Exception {
        return calcularParcelasRecorrenciaMensalidade(planoRecorrencia, false);
    }

    public List<PlanoRecorrenciaParcelaVO> calcularParcelasRecorrenciaMensalidade(PlanoRecorrenciaVO planoRecorrencia, boolean isRenovacao) throws Exception {
        List<PlanoRecorrenciaParcelaVO> parcelas = new ArrayList<PlanoRecorrenciaParcelaVO>();

        int i = 1;
        Double valorParcela;
        while (i <= planoRecorrencia.getDuracaoPlano()){
            valorParcela = getFacade().getPlanorecorrenciaParcela().consultarValorPorNumeroParcela(i, planoRecorrencia.getCodigo());
            valorParcela = (valorParcela == null || (isRenovacao && !planoRecorrencia.isGerarParcelasValorDiferenteRenovacao()))
                    ? planoRecorrencia.getValorMensal() : valorParcela;
            parcelas.add(new PlanoRecorrenciaParcelaVO(i, valorParcela));
            i++;
        }

        return parcelas;
    }

    public List<PlanoRecorrenciaParcelaWS> calcularParcelasRecorrenciaWS(PlanoRecorrenciaVO planoRecorrencia) throws Exception{
        return calcularParcelasRecorrenciaWS(planoRecorrencia, false);
    }

    public List<PlanoRecorrenciaParcelaWS> calcularParcelasRecorrenciaWS(PlanoRecorrenciaVO planoRecorrencia, boolean isRenovacao) throws Exception {
        List<PlanoRecorrenciaParcelaVO> parcelas = calcularParcelasRecorrenciaMensalidade(planoRecorrencia, isRenovacao);
        List<PlanoRecorrenciaParcelaWS> parcelasWS = new ArrayList<PlanoRecorrenciaParcelaWS>();

        for (PlanoRecorrenciaParcelaVO parcela: parcelas) {
            parcelasWS.add(new PlanoRecorrenciaParcelaWS(parcela));
        }

        return parcelasWS;
    }

    public  List<MovParcelaVO> calcularParcelas(PlanoVO plano,
                                                int diaVencimentoParcelas,
                                                int numeroVezesParcelarAdesao,
                                                PessoaVO pessoa,
                                                EmpresaVO empresa,
                                                UsuarioVO usuario,
                                                String prefixoDescricaoParcelas,
                                                String prefixoDescricaoProdutos) throws Exception {

        PlanoRecorrenciaVO planoRecorrencia = plano.getPlanoRecorrencia();
        List<MovParcelaVO> movParcelas = new ArrayList<MovParcelaVO>();
        List<PlanoRecorrenciaParcelaVO> parcelasRecorrenciaMensalidade = calcularParcelasRecorrenciaMensalidade(planoRecorrencia);
        Date dataVencimento = Calendario.setDiaMes(new Date(), diaVencimentoParcelas);

        if(diaVencimentoParcelas < Calendario.getDiaMes() ){
            dataVencimento = Calendario.somarMeses(dataVencimento, 1);
        }

        // Pro rata
        if(Calendario.diferencaEmDias(Calendario.hoje(), dataVencimento) > empresa.getToleranciaProrata()){

            MovParcelaVO parcelaProRata = getFacade().getMovParcela().calcularParcelaProRata(
                    Calendario.hoje(),
                    dataVencimento,
                    plano.getPlanoRecorrencia().getValorMensal(),
                    usuario,
                    empresa,
                    pessoa,
                    null
                    );

            movParcelas.add(parcelaProRata);
        }

        // Taxa adesão
        if(plano.getPlanoRecorrencia().getTaxaAdesao() != null && plano.getPlanoRecorrencia().getTaxaAdesao() > 0.0){
            List<MovParcelaVO> parcelasAdesao = calcularParcelasAdesao(plano, dataVencimento, numeroVezesParcelarAdesao, pessoa, empresa, usuario);
            movParcelas.addAll(parcelasAdesao);
        }

        // Mensalidades
        for (PlanoRecorrenciaParcelaVO parcelaRecorrencia: parcelasRecorrenciaMensalidade) {

            MovProdutoVO movProduto = new MovProdutoVO();
            movProduto.setSituacao("EA");
            movProduto.setAnoReferencia(Calendario.getAno(dataVencimento));
            movProduto.setMesReferencia(Calendario.getData(dataVencimento, "MM/yyyy"));
            movProduto.setProduto(getFacade().getProduto().obterProdutoPadraoPersonal());
            movProduto.setContrato(null);
            movProduto.setQuitado(true);
            movProduto.setPessoa(pessoa);
            movProduto.setEmpresa(empresa);
            movProduto.setQuantidade(1);
            movProduto.setPrecoUnitario(parcelaRecorrencia.getValor());
            movProduto.setValorDesconto(parcelaRecorrencia.getValorDesconto());
            movProduto.setDataLancamento(new Date());
            movProduto.setTotalFinal(parcelaRecorrencia.getValorFinal());
            movProduto.setResponsavelLancamento(usuario);
            movProduto.setDataInicioVigencia(dataVencimento);
            movProduto.setDataFinalVigencia(Calendario.somarMeses(dataVencimento, 1));
            String codigoOperacaoFinanceira =  getFacade()
                    .getPlanoTipo()
                    .consultarCodigoOperacaoFinanceiraParaParcelas(plano.getPlanoTipo(), TipoProduto.TAXA_PERSONAL.getCodigo());
            movProduto.setDescricao(prefixoDescricaoProdutos+" - "+movProduto.getMesReferencia());
            movProduto.setCodigoOperacaoFinanceira(codigoOperacaoFinanceira);
            movProduto.setNumeroParcela(parcelaRecorrencia.getNumero());

            MovParcelaVO movParcela = new MovParcelaVO();
            movParcela.setDataVencimento(dataVencimento);
            movParcela.setPessoa(pessoa);
            movParcela.setEmpresa(empresa);
            movParcela.setDataRegistro(new Date());
            movParcela.setSituacao("EA");
            movParcela.setValorParcela(parcelaRecorrencia.getValorFinal());
            movParcela.setResponsavel(usuario);
            movParcela.setDescricao(prefixoDescricaoParcelas+" - PARCELA "+parcelaRecorrencia.getNumero());
            movParcela.getContrato().setCodigo(0);

            ArrayList<MovProdutoParcelaVO> movProdutosParcelas = new ArrayList<MovProdutoParcelaVO>();
            MovProdutoParcelaVO movProdutoParcela = new MovProdutoParcelaVO(movProduto, movParcela);
            movProdutoParcela.setMovProdutoVO(movProduto);
            movProdutoParcela.setMovParcelaVO(movParcela);
            movProdutoParcela.setValorPago(movParcela.getValorParcela());
            movProdutosParcelas.add(movProdutoParcela);
            movParcela.setMovProdutoParcelaVOs(movProdutosParcelas);

            movParcelas.add(movParcela);

            dataVencimento = Calendario.somarMeses(dataVencimento, 1);
        }

        // Anuidade
        if(plano.getPlanoRecorrencia().getValorAnuidade() != null && plano.getPlanoRecorrencia().getValorAnuidade() > 0.0){
            List<MovParcelaVO> parcelasAnuidade = calcularParcelasAnuidade(plano, movParcelas, pessoa, empresa, usuario);
            movParcelas.addAll(parcelasAnuidade);
        }

        return movParcelas;
    }

    private List<MovParcelaVO> calcularParcelasAnuidade(PlanoVO plano,
                                                        List<MovParcelaVO> parcelasMensalidade,
                                                        PessoaVO pessoa,
                                                        EmpresaVO empresa,
                                                        UsuarioVO usuario) throws Exception {

        List<MovParcelaVO> parcelas = new ArrayList<MovParcelaVO>();

        Double valorAnuidade = plano.getPlanoRecorrencia().getValorAnuidade();
        PlanoEmpresaVO planoEmpresaVO = plano.obterPlanoEmpresa(empresa.getCodigo());
        if (planoEmpresaVO != null && !UteisValidacao.emptyNumber(planoEmpresaVO.getValorAnuidade())) {
            valorAnuidade = planoEmpresaVO.getValorAnuidade();
        }


        Date dataVencimento = new Date();
        if(plano.getPlanoRecorrencia().isAnuidadeNaParcela()){
            for (MovParcelaVO parcela: parcelasMensalidade) {
                if(parcela.getNumeroParcela() == plano.getPlanoRecorrencia().getParcelaAnuidade()){
                    dataVencimento = parcela.getDataVencimento();
                }
            }

        }else{
            Calendario.setDiaMes(new Date(), plano.getPlanoRecorrencia().getDiaAnuidade());
            dataVencimento = Calendario.setMesData(dataVencimento, plano.getPlanoRecorrencia().getMesAnuidade() - 1);

            if(Calendario.menorDataAtual(dataVencimento)){
                dataVencimento = Calendario.somarAnos(dataVencimento, 1);
            }
        }

        MovProdutoVO movProduto = new MovProdutoVO();
        movProduto.setSituacao("EA");
        movProduto.setAnoReferencia(Calendario.getAno(dataVencimento));
        movProduto.setMesReferencia(Calendario.getData(dataVencimento, "MM/yyyy"));
        movProduto.setProduto(getFacade().getProduto().obterProdutoPadraoAnuidadePlanoRecorrente());
        movProduto.setPessoa(pessoa);
        movProduto.setEmpresa(empresa);
        movProduto.setQuantidade(1);
        movProduto.setPrecoUnitario(valorAnuidade);
        movProduto.setDataLancamento(new Date());
        movProduto.setTotalFinal(valorAnuidade);
        movProduto.setResponsavelLancamento(usuario);
        movProduto.setDataInicioVigencia(null);
        movProduto.setDataFinalVigencia(null);
        String codigoOperacaoFinanceira =  getFacade()
                .getPlanoTipo()
                .consultarCodigoOperacaoFinanceiraParaParcelas(plano.getPlanoTipo(), TipoProduto.TAXA_DE_ANUIDADE_PLANO_RECORRENCIA.getCodigo());
        movProduto.setDescricao("TAXA DE ANUIDADE PLANO RECORRÊNCIA - "+movProduto.getMesReferencia());
        movProduto.setCodigoOperacaoFinanceira(codigoOperacaoFinanceira);

        MovParcelaVO movParcela = new MovParcelaVO();
        movParcela.setDataVencimento(dataVencimento);
        movParcela.setPessoa(pessoa);
        movParcela.setEmpresa(empresa);
        movParcela.setDataRegistro(new Date());
        movParcela.setSituacao("EA");
        movParcela.setValorParcela(valorAnuidade);
        movParcela.setResponsavel(usuario);
        movParcela.setDescricao("ANUIDADE PLANO RECORRENTE - "+movProduto.getAnoReferencia());

        ArrayList<MovProdutoParcelaVO> movProdutosParcelas = new ArrayList<MovProdutoParcelaVO>();
        MovProdutoParcelaVO movProdutoParcela = new MovProdutoParcelaVO(movProduto, movParcela);
        movProdutoParcela.setMovProdutoVO(movProduto);
        movProdutoParcela.setMovParcelaVO(movParcela);
        movProdutosParcelas.add(movProdutoParcela);
        movParcela.setMovProdutoParcelaVOs(movProdutosParcelas);

        parcelas.add(movParcela);

        return parcelas;
    }

    public MovParcelaVO criarParcelaAdesao(Double valor,
                                           int numeroParcela,
                                           Date dataVencimento,
                                           PessoaVO pessoa,
                                           EmpresaVO empresa,
                                           UsuarioVO usuario,
                                           PlanoTipoVO planoTipo) throws Exception {

        MovProdutoVO movProduto = new MovProdutoVO();
        movProduto.setSituacao("EA");
        movProduto.setAnoReferencia(Calendario.getAno(dataVencimento));
        movProduto.setMesReferencia(Calendario.getData(dataVencimento, "MM/yyyy"));
        movProduto.setProduto(getFacade().getProduto().obterProdutoPadraoAdesaoPlanoRecorrente());
        movProduto.setContrato(null);
        movProduto.setPessoa(pessoa);
        movProduto.setEmpresa(empresa);
        movProduto.setQuantidade(1);
        movProduto.setPrecoUnitario(valor);
        movProduto.setDataLancamento(new Date());
        movProduto.setTotalFinal(valor);
        movProduto.setResponsavelLancamento(usuario);
        movProduto.setDataInicioVigencia(null);
        movProduto.setDataFinalVigencia(null);
        String codigoOperacaoFinanceira =  getFacade()
                .getPlanoTipo()
                .consultarCodigoOperacaoFinanceiraParaParcelas(planoTipo, TipoProduto.TAXA_DE_ADESAO_PLANO_RECORRENCIA.getCodigo());
        movProduto.setDescricao("ADESÃO PLANO RECORRENTE");
        movProduto.setCodigoOperacaoFinanceira(codigoOperacaoFinanceira);

        MovParcelaVO movParcela = new MovParcelaVO();
        movParcela.setDataVencimento(dataVencimento);
        movParcela.setPessoa(pessoa);
        movParcela.setEmpresa(empresa);
        movParcela.setDataRegistro(new Date());
        movParcela.setSituacao("EA");
        movParcela.setValorParcela(valor);
        movParcela.setResponsavel(usuario);
        movParcela.setDescricao("ADESÃO PARCELA "+Integer.toString(numeroParcela));

        ArrayList<MovProdutoParcelaVO> movProdutosParcelas = new ArrayList<MovProdutoParcelaVO>();
        MovProdutoParcelaVO movProdutoParcela = new MovProdutoParcelaVO(movProduto, movParcela);
        movProdutoParcela.setMovProdutoVO(movProduto);
        movProdutoParcela.setMovParcelaVO(movParcela);
        movProdutosParcelas.add(movProdutoParcela);
        movParcela.setMovProdutoParcelaVOs(movProdutosParcelas);

        return movParcela;
    }

    public List<MovParcelaVO> calcularParcelasAdesao(PlanoVO plano,
                                                     Date dataVencimento,
                                                     int numeroVezesParcelarAdesao,
                                                     PessoaVO pessoa,
                                                     EmpresaVO empresa,
                                                     UsuarioVO usuario) throws Exception {
        List<MovParcelaVO> parcelas = new ArrayList<>();

        Double taxaAdesao = plano.getPlanoRecorrencia().getTaxaAdesao();
        PlanoEmpresaVO planoEmpresaVO = plano.obterPlanoEmpresa(empresa.getCodigo());
        if (planoEmpresaVO != null && !UteisValidacao.emptyNumber(planoEmpresaVO.getTaxaAdesao())) {
            taxaAdesao = planoEmpresaVO.getTaxaAdesao();
        }

        if (plano.getCobrarAdesaoSeparada() != null && plano.getCobrarAdesaoSeparada()) {
            int numeroParcela = 1;
            Double valorParcela = taxaAdesao / numeroVezesParcelarAdesao;

            while (numeroParcela <= numeroVezesParcelarAdesao) {
                parcelas.add(criarParcelaAdesao(valorParcela,
                        numeroParcela,
                        dataVencimento,
                        pessoa,
                        empresa,
                        usuario,
                        plano.getPlanoTipo()));

                dataVencimento = Calendario.somarMeses(dataVencimento, 1);
                numeroParcela++;
            }
        } else {
            parcelas.add(criarParcelaAdesao(taxaAdesao,
                    1,
                    dataVencimento,
                    pessoa,
                    empresa,
                    usuario,
                    plano.getPlanoTipo()));
        }

        return parcelas;
    }

    public List<MovParcelaVO> alterarDataVencimentoParcelasMensalidade(ContratoVO contrato, int novoDiaVencimento, UsuarioVO usuario) throws Exception {

        Date dataVencimento = Calendario.somarDias(Calendario.hoje(), 1);
        List<MovParcelaVO> parcelasEmAberto = getFacade()
                .getMovParcela()
                .consultarMensalidadesEmAbertoPorContrato(contrato.getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS, dataVencimento);

        if(parcelasEmAberto.size() > 0) {
            MovParcelaVO proximaParcela = parcelasEmAberto.get(0);

            int diaVecimentoParcelaAtual = proximaParcela.getDiaVencimento();
            int diferencaDiasNovoVencimento = novoDiaVencimento - diaVecimentoParcelaAtual;

            Date novaDataVencimentoParcela = Calendario.setDiaMes(proximaParcela.getDataVencimento(), novoDiaVencimento);

            if (diferencaDiasNovoVencimento > 0) {

                MovParcelaVO parcelaProRata =  getFacade().getMovParcela().calcularParcelaProRata(
                        proximaParcela.getDataVencimento(),
                        novaDataVencimentoParcela,
                        contrato.getContratoRecorrenciaVO().getValorMensal(),
                        usuario,
                        contrato.getEmpresa(),
                        contrato.getCliente().getPessoa(),
                        contrato);

                getFacade().getMovParcela().incluirComProdutosSemCommit(parcelaProRata);
            }

            for (MovParcelaVO parcela: parcelasEmAberto) {
                parcela.setDataVencimento(Calendario.somarDias(parcela.getDataVencimento(), diferencaDiasNovoVencimento));
            }

            List<MovParcelaVO> parcelasSemAlteracaoVencimento = getFacade()
                    .getMovParcela()
                    .consultarMensalidadesEmAbertoPorContrato(contrato.getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS, dataVencimento);
            getFacade().getMovParcela().alterarVencimentoListaParcelas(parcelasEmAberto, parcelasSemAlteracaoVencimento, false, true, usuario.getNome(),"Metodo não ultilizado", false);
            getFacade().getContratoRecorrencia().alterarDiaVencimento(novoDiaVencimento, contrato.getCodigo(), usuario.getNome());
        }

        return parcelasEmAberto;
    }
}

package negocio.facade.jdbc.plano;

import negocio.comuns.basico.EmpresaVO;
import negocio.comuns.contrato.ContratoVO;
import negocio.comuns.financeiro.ControleTaxaPersonalVO;
import negocio.comuns.plano.PlanoEmpresaVO;
import negocio.comuns.plano.PlanoVO;
import negocio.comuns.utilitarias.UteisValidacao;
import negocio.facade.jdbc.arquitetura.SuperEntidade;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.sql.Types;
import java.util.ArrayList;
import java.util.List;

public class PlanoEmpresa extends SuperEntidade {

    public PlanoEmpresa() throws Exception {
    }

    public PlanoEmpresa(Connection conexao) throws Exception {
        super(conexao);
    }

    public List<PlanoEmpresaVO> consultarTodas(Integer plano) throws SQLException {
        String sql = "SELECT " +
                "CASE WHEN pe.codigo IS NULL THEN false ELSE true end AS controlaAcesso, " +
                "pe.codigo, " +
                "pe.empresa, " +
                "empresa.codigo empresa_codigo, " +
                "empresa.nome empresa_nome, " +
                "cidade.nome as empresa_cidadeNome, " +
                "estado.sigla as empresa_estadoSigla, " +
                "empresa.setor empresa_setor, " +
                "acesso, " +
                "venda,\n" +
                "taxaadesao,\n" +
                "valoranuidade,\n" +
                "valormensal,\n" +
                "modelocontrato,\n" +
                "percentualmultacancelamento\n" +
                "FROM empresa " +
                "LEFT JOIN cidade ON cidade.codigo = empresa.cidade " +
                "LEFT JOIN ESTADO ON estado.codigo = cidade.estado " +
                "LEFT JOIN planoempresa pe on empresa.codigo = pe.empresa AND pe.plano = " + plano;

        PreparedStatement ps = con.prepareStatement(sql);
        ResultSet resultado = ps.executeQuery();
        List<PlanoEmpresaVO> empresas = new ArrayList<>();

        while (resultado.next()) {
            empresas.add(montarDados(resultado));
        }

        return empresas;
    }

    private PlanoEmpresaVO montarDados(ResultSet rs) throws SQLException {
        PlanoEmpresaVO pe = new PlanoEmpresaVO();
        pe.setCodigo(rs.getInt("codigo"));
        pe.setAcesso(rs.getBoolean("acesso"));
        pe.setVenda(rs.getBoolean("venda"));
        pe.setTaxaAdesao(rs.getDouble("taxaadesao"));
        pe.setValorAnuidade(rs.getDouble("valoranuidade"));
        pe.setValorMensal(rs.getDouble("valormensal"));
        pe.getModeloContrato().setCodigo(rs.getInt("modelocontrato"));
        pe.setPercentualMultaCancelamento(rs.getDouble("percentualmultacancelamento"));

        EmpresaVO empresa = new EmpresaVO();
        empresa.setCodigo(rs.getInt("empresa_codigo"));
        empresa.setNome(rs.getString("empresa_nome"));
        empresa.setCidadeNome(rs.getString("empresa_cidadeNome"));
        empresa.setEstadoSigla(rs.getString("empresa_estadoSigla"));
        empresa.setSetor(rs.getString("empresa_setor"));
        pe.setEmpresa(empresa);

        return pe;
    }

    public void atualizarEmpresasPlano(PlanoVO plano) throws Exception {
        excluirEmpresasPlano(plano);
        incluirEmpresasPlano(plano);
    }

    public void excluirEmpresasPlano(PlanoVO plano) throws SQLException {
        String sqlDelete = "DELETE FROM planoempresa WHERE plano = " + plano.getCodigo();
        PreparedStatement ps = con.prepareStatement(sqlDelete);
        ps.execute();
    }

    private void incluirPlanoEmpresa(PlanoVO plano, PlanoEmpresaVO obj) throws Exception {
        String sqlInsert = "INSERT INTO planoempresa (empresa, plano, acesso, venda, taxaadesao, valoranuidade, valormensal, modelocontrato, percentualmultacancelamento)\n" +
                "VALUES ( ?, ?, ?, ?, ?, ?, ?, ?, ? )";
        try (PreparedStatement sqlInserir = con.prepareStatement(sqlInsert)) {
            int i = 0;
            sqlInserir.setInt(++i, obj.getEmpresa().getCodigo());
            sqlInserir.setInt(++i, plano.getCodigo());
            if (!plano.isPermitirAcessoSomenteNaEmpresaVendeuContrato()) {
                sqlInserir.setBoolean(++i, obj.isAcesso());
                sqlInserir.setBoolean(++i, obj.isVenda());
            } else {
                if (obj.isVenda()) {
                    sqlInserir.setBoolean(++i, true);
                    sqlInserir.setBoolean(++i, true);
                } else {
                    sqlInserir.setBoolean(++i, false);
                    sqlInserir.setBoolean(++i, false);
                }
            }

            if (UteisValidacao.emptyNumber(obj.getTaxaAdesao())) {
                sqlInserir.setNull(++i, Types.DOUBLE);
            } else {
                sqlInserir.setDouble(++i, obj.getTaxaAdesao());
            }

            if (UteisValidacao.emptyNumber(obj.getValorAnuidade())) {
                sqlInserir.setNull(++i, Types.DOUBLE);
            } else {
                sqlInserir.setDouble(++i, obj.getValorAnuidade());
            }

            if (UteisValidacao.emptyNumber(obj.getValorMensal())) {
                sqlInserir.setNull(++i, Types.DOUBLE);
            } else {
                sqlInserir.setDouble(++i, obj.getValorMensal());
            }

            if (UteisValidacao.emptyNumber(obj.getModeloContrato().getCodigo())) {
                sqlInserir.setNull(++i, Types.INTEGER);
            } else {
                sqlInserir.setDouble(++i, obj.getModeloContrato().getCodigo());
            }

            if (UteisValidacao.emptyNumber(obj.getPercentualMultaCancelamento())) {
                sqlInserir.setNull(++i, Types.DOUBLE);
            } else {
                sqlInserir.setDouble(++i, obj.getPercentualMultaCancelamento());
            }

            sqlInserir.execute();
        }
        obj.setCodigo(obterValorChavePrimariaCodigo());
        obj.setNovoObj(false);
    }

    public void incluirEmpresasPlano(PlanoVO plano) throws Exception {
        for (PlanoEmpresaVO planoEmpresa : plano.getEmpresas()) {
            incluirPlanoEmpresa(plano, planoEmpresa);
        }
    }

    /**
     * @param contratos lista de contratos
     * @param empresa   codigo da empresa
     * @return true = O contrato permite acesso a empresa
     * @throws SQLException
     * @todo Precisa ser testado com varios contratos em empresas diferentes.
     * <p>
     * Verifica se alguns dos contratos permitem acesso do aluno a empresa
     */
    public boolean permiteAcessosEmpresa(List<ContratoVO> contratos, Integer empresa) throws SQLException {

        if (contratos.size() > 0) {
            String codigoPlanos = "";
            List<Integer> contratosPermitemAcessoEmpresa = new ArrayList<>();
            for (ContratoVO contrato : contratos) {
                codigoPlanos += contrato.getPlano().getCodigo() + ",";

                if (contrato.getPlano().isPermitirAcessoSomenteNaEmpresaVendeuContrato() &&
                        empresa == contrato.getEmpresa().getCodigo()) {
                    contratosPermitemAcessoEmpresa.add(contrato.getCodigo());
                }

                if (!contrato.getPlano().isPermitirAcessoSomenteNaEmpresaVendeuContrato()) {
                    contratosPermitemAcessoEmpresa.add(contrato.getCodigo());
                }
            }

            if (contratosPermitemAcessoEmpresa.size() == 0) {
                return false;
            }

            codigoPlanos = codigoPlanos.substring(0, codigoPlanos.length() - 1);

            String sqlTodasEmpresaDosPlanos = "SELECT count(*) total_empresas " +
                    "FROM planoempresa " +
                    "WHERE plano IN (" + codigoPlanos + ") " +
                    "   AND acesso IS TRUE";

            PreparedStatement ps = con.prepareStatement(sqlTodasEmpresaDosPlanos);
            ResultSet rs = ps.executeQuery();
            rs.next();

            if (rs.getInt("total_empresas") == 0) {
                // Se não foi selecionada empresa nos planos, o acesso é permitido em todas as empresas
                // Veja: https://app.assembla.com/spaces/plataforma-zw/tickets/12410/details?comment=1486473164
                return true;
            }

            String sqlExisteEmpresaNosPlanos = "SELECT empresa " +
                    "FROM planoempresa " +
                    "WHERE plano IN (" + codigoPlanos + ") " +
                    "   AND empresa = " + empresa +
                    "   AND acesso IS TRUE";


            ps = con.prepareStatement(sqlExisteEmpresaNosPlanos);
            rs = ps.executeQuery();
            // Se a empresa esta selecionada no plano, é permitido
            return rs.next();
        }

        return false;
    }

    public boolean permiteAcessosEmpresaPlanoPersonal(List<ControleTaxaPersonalVO> taxasPersonal, Integer empresa) throws SQLException {

        if (taxasPersonal.size() > 0) {
            String codigoPlanos = "";
            List<Integer> contratosPermitemAcessoEmpresa = new ArrayList<>();
            for (ControleTaxaPersonalVO controleTaxaPersonalVO : taxasPersonal) {
                codigoPlanos += controleTaxaPersonalVO.getPlano().getCodigo() + ",";

                if (controleTaxaPersonalVO.getPlano().isPermitirAcessoSomenteNaEmpresaVendeuContrato() &&
                        empresa == controleTaxaPersonalVO.getEmpresa().getCodigo()) {
                    contratosPermitemAcessoEmpresa.add(controleTaxaPersonalVO.getCodigo());
                }

                if (!controleTaxaPersonalVO.getPlano().isPermitirAcessoSomenteNaEmpresaVendeuContrato()) {
                    contratosPermitemAcessoEmpresa.add(controleTaxaPersonalVO.getCodigo());
                }
            }

            if (contratosPermitemAcessoEmpresa.size() == 0) {
                return false;
            }

            codigoPlanos = codigoPlanos.substring(0, codigoPlanos.length() - 1);

            String sqlTodasEmpresaDosPlanos = "SELECT count(*) total_empresas " +
                    "FROM planoempresa " +
                    "WHERE plano IN (" + codigoPlanos + ") " +
                    "   AND acesso IS TRUE";

            PreparedStatement ps = con.prepareStatement(sqlTodasEmpresaDosPlanos);
            ResultSet rs = ps.executeQuery();
            rs.next();

            if (rs.getInt("total_empresas") == 0) {
                // Se não foi selecionada empresa nos planos, o acesso é permitido em todas as empresas
                // Veja: https://app.assembla.com/spaces/plataforma-zw/tickets/12410/details?comment=1486473164
                return true;
            }

            String sqlExisteEmpresaNosPlanos = "SELECT empresa " +
                    "FROM planoempresa " +
                    "WHERE plano IN (" + codigoPlanos + ") " +
                    "   AND empresa = " + empresa +
                    "   AND acesso IS TRUE";


            ps = con.prepareStatement(sqlExisteEmpresaNosPlanos);
            rs = ps.executeQuery();
            // Se a empresa esta selecionada no plano, é permitido
            return rs.next();
        }

        return false;
    }

    public boolean permiteVendaEmpresa(int empresa, int plano) throws SQLException {
        String sql = "SELECT empresa FROM PlanoEmpresa " +
                "WHERE empresa = " + empresa +
                "   AND plano = " + plano +
                "   AND venda IS TRUE";

        PreparedStatement statement = con.prepareStatement(sql);
        ResultSet result = statement.executeQuery();
        return result.next();
    }
}

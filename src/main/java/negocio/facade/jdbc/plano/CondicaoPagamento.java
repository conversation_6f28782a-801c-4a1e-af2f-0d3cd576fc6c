package negocio.facade.jdbc.plano;

import negocio.comuns.financeiro.enumerador.TipoConvenioCobrancaEnum;
import negocio.comuns.plano.CondicaoPagamentoParcelaVO;
import negocio.comuns.plano.CondicaoPagamentoVO;
import negocio.comuns.plano.PlanoVO;
import negocio.comuns.utilitarias.CondicaoPagamentoPlanoTO;
import negocio.comuns.utilitarias.ConsistirException;
import negocio.comuns.utilitarias.Ordenacao;
import negocio.comuns.utilitarias.Uteis;
import negocio.facade.jdbc.arquitetura.SuperEntidade;
import negocio.interfaces.plano.CondicaoPagamentoInterfaceFacade;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.sql.Statement;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Date;
import java.util.Hashtable;
import java.util.List;

/**
 * Classe de persistência que encapsula todas as operações de manipulação dos dados da classe <code>CondicaoPagamentoVO</code>.
 * Responsável por implementar operações como incluir, alterar, excluir e consultar pertinentes a classe <code>CondicaoPagamentoVO</code>.
 * Encapsula toda a interação com o banco de dados.
 * @see CondicaoPagamentoVO
 * @see SuperEntidade
 */
public class CondicaoPagamento extends SuperEntidade implements CondicaoPagamentoInterfaceFacade {

    private Hashtable condicaoPagamentoParcelas;

    public CondicaoPagamento() throws Exception {
        super();
        setCondicaoPagamentoParcelas(new Hashtable());
    }

    public CondicaoPagamento(Connection con) throws Exception {
        super(con);
        setCondicaoPagamentoParcelas(new Hashtable());
    }

    /**
     * Operação responsável por retornar um novo objeto da classe <code>CondicaoPagamentoVO</code>.
     */
    public CondicaoPagamentoVO novo() throws Exception {
        incluir(getIdEntidade());
        CondicaoPagamentoVO obj = new CondicaoPagamentoVO();
        return obj;
    }

    PreparedStatement prepararIncluir(CondicaoPagamentoVO obj) throws Exception {
        CondicaoPagamentoVO.validarDados(obj);
        incluir(getIdEntidade());
        obj.realizarUpperCaseDados();
        String sql = "INSERT INTO CondicaoPagamento( descricao, nrParcelas, "
                + "entrada, percentualValorEntrada,condicaoPagamentoDefault,intervaloEntreParcela,tipoConvenioCobranca, recebimentoprepago, recorrencia ) VALUES ( ?, ?, ?, ?, ?, ?, ?, ?, ? )";
        PreparedStatement ps = con.prepareStatement(sql);
        ps.setString(1, obj.getDescricao());
        ps.setInt(2, obj.getNrParcelas().intValue());
        ps.setBoolean(3, obj.isEntrada().booleanValue());
        ps.setDouble(4, obj.getPercentualValorEntrada().doubleValue());
        ps.setBoolean(5, obj.getCondicaoPagamentoDefault().booleanValue());
        ps.setInt(6, obj.getIntervaloEntreParcela());
        ps.setInt(7, obj.getTipoConvenioCobranca().getCodigo());
        ps.setBoolean(8, obj.getRecebimentoPrePago());
        ps.setBoolean(9, obj.isRecorrencia());

        return ps;
    }

    public CondicaoPagamentoVO consultarPorDescricao(String descricao, int nivelMontarDados)throws Exception{
        String sql = "select * from CondicaoPagamento where upper(descricao) = ?";
        try (PreparedStatement pst = con.prepareStatement(sql.toString())) {
            pst.setString(1, descricao.toUpperCase());
            try (ResultSet rs = pst.executeQuery()) {
                if (rs.next()) {
                    return montarDados(rs, nivelMontarDados, con);
                }
            }
        }
        return null;

    }

    /**
     * Operação responsável por incluir no banco de dados um objeto da classe <code>CondicaoPagamentoVO</code>.
     * Primeiramente valida os dados (<code>validarDados</code>) do objeto. Verifica a conexão com o banco de dados e a permissão do usuário
     * para realizar esta operacão na entidade.
     * Isto, através da operação <code>incluir</code> da superclasse.
     * @param obj  Objeto da classe <code>CondicaoPagamentoVO</code> que será gravado no banco de dados.
     * @exception Exception Caso haja problemas de conexão, restrição de acesso ou validação de dados.
     */
    @Override
    public void incluir(CondicaoPagamentoVO obj) throws Exception {
        try {
            con.setAutoCommit(false);
            try (PreparedStatement ps = prepararIncluir(obj)) {
                ps.execute();
            }
            obj.setCodigo(obterValorChavePrimariaCodigo());
            getFacade().getCondicaoPagamentoParcela().incluirCondicaoPagamentoParcelas(obj.getCodigo(), obj.getCondicaoPagamentoParcelaVOs());
            getFacade().getPlanoCondicaoPagamento().incluirPlanoCondicaoPagamento(obj.getListaCondicaoPagamentoPlano());
            con.commit();
            obj.setNovoObj(false);
        } catch (Exception e) {
            con.rollback();
            throw e;
        } finally {
            con.setAutoCommit(true);
        }
    }

    @Override
    public void incluirSemCommit(CondicaoPagamentoVO obj) throws Exception {
        try {
            try (PreparedStatement ps = prepararIncluir(obj)) {
                ps.execute();
            }
            obj.setCodigo(obterValorChavePrimariaCodigo());
            getFacade().getCondicaoPagamentoParcela().incluirCondicaoPagamentoParcelas(obj.getCodigo(), obj.getCondicaoPagamentoParcelaVOs());
            obj.setNovoObj(false);
        } catch (Exception e) {
            throw e;
        }
    }

    /**
     * Operação responsável por alterar no BD os dados de um objeto da classe <code>CondicaoPagamentoVO</code>.
     * Sempre utiliza a chave primária da classe como atributo para localização do registro a ser alterado.
     * Primeiramente valida os dados (<code>validarDados</code>) do objeto. Verifica a conexão com o banco de dados e a permissão do usuário
     * para realizar esta operacão na entidade.
     * Isto, através da operação <code>alterar</code> da superclasse.
     * @param obj    Objeto da classe <code>CondicaoPagamentoVO</code> que será alterada no banco de dados.
     * @exception Exception Caso haja problemas de conexão, restrição de acesso ou validação de dados.
     */
    public void alterar(CondicaoPagamentoVO obj) throws Exception {
        try {
            con.setAutoCommit(false);
            alterarSemCommit(obj);
            con.commit();
        } catch (Exception e) {
            con.rollback();
            con.setAutoCommit(true);
            throw e;
        } finally {
            con.setAutoCommit(true);
        }
    }

    public void alterarSemCommit(CondicaoPagamentoVO obj) throws Exception {
            CondicaoPagamentoVO.validarDados(obj);
            alterar(getIdEntidade());
            obj.realizarUpperCaseDados();
            String sql = "UPDATE CondicaoPagamento set descricao=?, nrParcelas=?, "
                    + "entrada=?, percentualValorEntrada=?, condicaoPagamentoDefault=?, "
                    + "intervaloEntreParcela=?, tipoConvenioCobranca=?, recebimentoprepago=?, recorrencia=? WHERE ((codigo = ?))";
        try (PreparedStatement ps = con.prepareStatement(sql)) {
            ps.setString(1, obj.getDescricao());
            ps.setInt(2, obj.getNrParcelas().intValue());
            ps.setBoolean(3, obj.isEntrada().booleanValue());
            ps.setDouble(4, obj.getPercentualValorEntrada().doubleValue());
            ps.setBoolean(5, obj.getCondicaoPagamentoDefault().booleanValue());
            ps.setInt(6, obj.getIntervaloEntreParcela());
            ps.setInt(7, obj.getTipoConvenioCobranca().getCodigo());
            ps.setBoolean(8, obj.getRecebimentoPrePago());
            ps.setBoolean(9, obj.isRecorrencia());
            ps.setInt(10, obj.getCodigo().intValue());
            ps.execute();
        }
        getFacade().getCondicaoPagamentoParcela().alterarCondicaoPagamentoParcelas(obj.getCodigo(), obj.getCondicaoPagamentoParcelaVOs());
        getFacade().getPlanoCondicaoPagamento().atualizarParcelasPlanoCondicaoPagamento(obj);
    }

    /**
     * Operação responsável por excluir no BD um objeto da classe <code>CondicaoPagamentoVO</code>.
     * Sempre localiza o registro a ser excluído através da chave primária da entidade.
     * Primeiramente verifica a conexão com o banco de dados e a permissão do usuário
     * para realizar esta operacão na entidade.
     * Isto, através da operação <code>excluir</code> da superclasse.
     * @param obj    Objeto da classe <code>CondicaoPagamentoVO</code> que será removido no banco de dados.
     * @exception Exception Caso haja problemas de conexão ou restrição de acesso.
     */
    public void excluir(CondicaoPagamentoVO obj) throws Exception {
        try {
            con.setAutoCommit(false);
            excluir(getIdEntidade());
            String sql = "DELETE FROM CondicaoPagamento WHERE ((codigo = ?))";
            try (PreparedStatement sqlExcluir = con.prepareStatement(sql)) {
                sqlExcluir.setInt(1, obj.getCodigo().intValue());
                sqlExcluir.execute();
            }
            getFacade().getCondicaoPagamentoParcela().excluirCondicaoPagamentoParcelas(obj.getCodigo());
            con.commit();
        } catch (Exception e) {
            con.rollback();
            con.setAutoCommit(true);
            throw e;
        } finally {
            con.setAutoCommit(true);
        }
    }

    /**
     * Responsável por realizar uma consulta de <code>CondicaoPagamento</code> através do valor do atributo 
     * <code>Double percentualValorEntrada</code>. Retorna os objetos com valores iguais ou superiores ao parâmetro fornecido.
     * Faz uso da operação <code>montarDadosConsulta</code> que realiza o trabalho de prerarar o List resultante.
     * @param   controlarAcesso Indica se a aplicação deverá verificar se o usuário possui permissão para esta consulta ou não.
     * @return  List Contendo vários objetos da classe <code>CondicaoPagamentoVO</code> resultantes da consulta.
     * @exception Exception Caso haja problemas de conexão ou restrição de acesso.
     */
    public List consultarPorPercentualValorEntrada(Double valorConsulta, boolean controlarAcesso, int nivelMontarDados) throws Exception {
        consultar(getIdEntidade(), controlarAcesso);
        String sqlStr = "SELECT * FROM CondicaoPagamento WHERE percentualValorEntrada >= " + valorConsulta.doubleValue() + " ORDER BY nrParcelas";
        try (Statement stm = con.createStatement()) {
            try (ResultSet tabelaResultado = stm.executeQuery(sqlStr)) {
                return (montarDadosConsulta(tabelaResultado, nivelMontarDados, con));
            }
        }
    }

    /**
     * Responsável por realizar uma consulta de <code>CondicaoPagamento</code> através do valor do atributo 
     * <code>Integer nrParcelas</code>. Retorna os objetos com valores iguais ou superiores ao parâmetro fornecido.
     * Faz uso da operação <code>montarDadosConsulta</code> que realiza o trabalho de prerarar o List resultante.
     * @param   controlarAcesso Indica se a aplicação deverá verificar se o usuário possui permissão para esta consulta ou não.
     * @return  List Contendo vários objetos da classe <code>CondicaoPagamentoVO</code> resultantes da consulta.
     * @exception Exception Caso haja problemas de conexão ou restrição de acesso.
     */
    public List consultarPorNrParcelas(Integer valorConsulta, boolean controlarAcesso, int nivelMontarDados) throws Exception {
        consultar(getIdEntidade(), controlarAcesso);
        String sqlStr = "SELECT * FROM CondicaoPagamento WHERE nrParcelas >= " + valorConsulta.intValue() + " ORDER BY nrParcelas";
        try (Statement stm = con.createStatement()) {
            try (ResultSet tabelaResultado = stm.executeQuery(sqlStr)) {
                return (montarDadosConsulta(tabelaResultado, nivelMontarDados, con));
            }
        }
    }
    
    /**
     * Responsável por realizar uma consulta de <code>CondicaoPagamento</code> através do valor do atributo 
     * <code>Integer nrParcelas</code>. Retorna os objetos com valores iguais ou inferiores ao parâmetro fornecido.
     * Faz uso da operação <code>montarDadosConsulta</code> que realiza o trabalho de prerarar o List resultante.
     * @param   controlarAcesso Indica se a aplicação deverá verificar se o usuário possui permissão para esta consulta ou não.
     * @return  List Contendo vários objetos da classe <code>CondicaoPagamentoVO</code> resultantes da consulta.
     * @exception Exception Caso haja problemas de conexão ou restrição de acesso.
     */
    public List consultarPorNrParcelasMenorOuIgual(Integer valorConsulta, boolean controlarAcesso, int nivelMontarDados) throws Exception {
        consultar(getIdEntidade(), controlarAcesso);
        String sqlStr = "SELECT * FROM CondicaoPagamento WHERE nrParcelas <= " + valorConsulta.intValue() + " ORDER BY nrParcelas";
        try (Statement stm = con.createStatement()) {
            try (ResultSet tabelaResultado = stm.executeQuery(sqlStr)) {
                return (montarDadosConsulta(tabelaResultado, nivelMontarDados, con));
            }
        }
    }

    /**
     * Responsável por realizar uma consulta de <code>CondicaoPagamento</code> através do valor do atributo 
     * <code>String descricao</code>. Retorna os objetos, com início do valor do atributo idêntico ao parâmetro fornecido.
     * Faz uso da operação <code>montarDadosConsulta</code> que realiza o trabalho de prerarar o List resultante.
     * @param   controlarAcesso Indica se a aplicação deverá verificar se o usuário possui permissão para esta consulta ou não.
     * @return  List Contendo vários objetos da classe <code>CondicaoPagamentoVO</code> resultantes da consulta.
     * @exception Exception Caso haja problemas de conexão ou restrição de acesso.
     */
    public List consultarPorDescricao(String valorConsulta, boolean controlarAcesso, int nivelMontarDados) throws Exception {
        consultar(getIdEntidade(), controlarAcesso);
        String sqlStr = "SELECT * FROM CondicaoPagamento WHERE upper( descricao ) like('" + valorConsulta.toUpperCase() + "%') ORDER BY nrParcelas";
        try (Statement stm = con.createStatement()) {
            try (ResultSet tabelaResultado = stm.executeQuery(sqlStr)) {
                return (montarDadosConsulta(tabelaResultado, nivelMontarDados, con));
            }
        }
    }

    public List consultarPorCondicaoPagamentoDefault(Boolean valorConsulta, boolean controlarAcesso, int nivelMontarDados) throws Exception {
        consultar(getIdEntidade(), controlarAcesso);
        String sqlStr = "SELECT * FROM CondicaoPagamento WHERE CondicaoPagamento.CondicaoPagamentoDefault = " + valorConsulta + " ORDER BY nrParcelas";
        try (Statement stm = con.createStatement()) {
            try (ResultSet tabelaResultado = stm.executeQuery(sqlStr)) {
                return (montarDadosConsulta(tabelaResultado, nivelMontarDados, con));
            }
        }
    }

    /**
     * Responsável por realizar uma consulta de <code>CondicaoPagamento</code> através do valor do atributo 
     * <code>Integer codigo</code>. Retorna os objetos com valores iguais ou superiores ao parâmetro fornecido.
     * Faz uso da operação <code>montarDadosConsulta</code> que realiza o trabalho de prerarar o List resultante.
     * @param   controlarAcesso Indica se a aplicação deverá verificar se o usuário possui permissão para esta consulta ou não.
     * @return  List Contendo vários objetos da classe <code>CondicaoPagamentoVO</code> resultantes da consulta.
     * @exception Exception Caso haja problemas de conexão ou restrição de acesso.
     */
    public List consultarPorCodigo(Integer valorConsulta, boolean controlarAcesso, int nivelMontarDados) throws Exception {
        consultar(getIdEntidade(), controlarAcesso);
        String sqlStr = "SELECT * FROM CondicaoPagamento WHERE codigo >= " + valorConsulta.intValue() + " ORDER BY nrParcelas";
        try (Statement stm = con.createStatement()) {
            try (ResultSet tabelaResultado = stm.executeQuery(sqlStr)) {
                return (montarDadosConsulta(tabelaResultado, nivelMontarDados, con));
            }
        }
    }

    /**
     * Responsável por montar os dados de vários objetos, resultantes de uma consulta ao banco de dados (<code>ResultSet</code>).
     * Faz uso da operação <code>montarDados</code> que realiza o trabalho para um objeto por vez.
     * @return  List Contendo vários objetos da classe <code>CondicaoPagamentoVO</code> resultantes da consulta.
     */
    public static List montarDadosConsulta(ResultSet tabelaResultado, int nivelMontarDados, Connection con) throws Exception {
        List vetResultado = new ArrayList();
        while (tabelaResultado.next()) {
            CondicaoPagamentoVO obj = montarDados(tabelaResultado, nivelMontarDados, con);
            vetResultado.add(obj);
        }
        return vetResultado;
    }

    /**
     * Responsável por montar os dados resultantes de uma consulta ao banco de dados (<code>ResultSet</code>)
     * em um objeto da classe <code>CondicaoPagamentoVO</code>.
     * @return  O objeto da classe <code>CondicaoPagamentoVO</code> com os dados devidamente montados.
     */
    public static CondicaoPagamentoVO montarDados(ResultSet dadosSQL, int nivelMontarDados, Connection con) throws Exception {
        CondicaoPagamentoVO obj = new CondicaoPagamentoVO();
        obj.setCodigo(new Integer(dadosSQL.getInt("codigo")));
        obj.setDescricao(dadosSQL.getString("descricao"));
        obj.setNrParcelas(dadosSQL.getInt("nrParcelas"));
        obj.setIntervaloEntreParcela(dadosSQL.getInt("intervaloEntreParcela"));
        obj.setEntrada(dadosSQL.getBoolean("entrada"));
        obj.setCondicaoPagamentoDefault(dadosSQL.getBoolean("condicaoPagamentoDefault"));
        obj.setPercentualValorEntrada(dadosSQL.getDouble("percentualValorEntrada"));
        obj.setTipoConvenioCobranca(TipoConvenioCobrancaEnum.valueOf(dadosSQL.getInt("tipoConvenioCobranca")));
        obj.setRecebimentoPrePago(dadosSQL.getBoolean("recebimentoprepago"));
        obj.setRecorrencia(dadosSQL.getBoolean("recorrencia"));
        obj.setNovoObj(false);
        if (nivelMontarDados == Uteis.NIVELMONTARDADOS_DADOSBASICOS) {
            return obj;
        }
        CondicaoPagamentoParcela condPagamentoParcelaDao = new CondicaoPagamentoParcela(con);
        obj.setCondicaoPagamentoParcelaVOs(condPagamentoParcelaDao.consultarCondicaoPagamentoParcelas(obj.getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS));
        if (nivelMontarDados == Uteis.NIVELMONTARDADOS_DADOSENTIDADESUBORDINADAS) {
            return obj;
        }

        return obj;
    }

    /**
     * Operação responsável por adicionar um objeto da <code>CondicaoPagamentoParcelaVO</code> no Hashtable <code>CondicaoPagamentoParcelas</code>.
     * Neste Hashtable são mantidos todos os objetos de CondicaoPagamentoParcela de uma determinada CondicaoPagamento.
     * @param obj  Objeto a ser adicionado no Hashtable.
     */
    public void adicionarObjCondicaoPagamentoParcelas(CondicaoPagamentoParcelaVO obj) throws Exception {
        getCondicaoPagamentoParcelas().put(obj.getNrParcela() + "", obj);
        //adicionarObjSubordinadoOC
    }

    /**
     * Operação responsável por remover um objeto da classe <code>CondicaoPagamentoParcelaVO</code> do Hashtable <code>CondicaoPagamentoParcelas</code>.
     * Neste Hashtable são mantidos todos os objetos de CondicaoPagamentoParcela de uma determinada CondicaoPagamento.
     * @param DataVencimento Atributo da classe <code>CondicaoPagamentoParcelaVO</code> utilizado como apelido (key) no Hashtable.
     */
    public void excluirObjCondicaoPagamentoParcelas(Date DataVencimento) throws Exception {
        getCondicaoPagamentoParcelas().remove(DataVencimento + "");
        //excluirObjSubordinadoOC
    }

    /**
     * Operação responsável por localizar um objeto da classe <code>CondicaoPagamentoVO</code>
     * através de sua chave primária. 
     * @exception Exception Caso haja problemas de conexão ou localização do objeto procurado.
     */
    public CondicaoPagamentoVO consultarPorChavePrimaria(Integer codigoPrm, int nivelMontarDados) throws Exception {
        consultar(getIdEntidade(), false);
        String sql = "SELECT * FROM CondicaoPagamento WHERE codigo = ?";
        try (PreparedStatement sqlConsultar = con.prepareStatement(sql)) {
            sqlConsultar.setInt(1, codigoPrm.intValue());
            try (ResultSet tabelaResultado = sqlConsultar.executeQuery()) {
                if (!tabelaResultado.next()) {
                    throw new ConsistirException("Dados Não Encontrados ( CondicaoPagamento ).");
                }
                return (montarDados(tabelaResultado, nivelMontarDados, con));
            }
        }
    }

    public Hashtable getCondicaoPagamentoParcelas() {
        return (condicaoPagamentoParcelas);
    }

    public void setCondicaoPagamentoParcelas(Hashtable condicaoPagamentoParcelas) {
        this.condicaoPagamentoParcelas = condicaoPagamentoParcelas;
    }

    public CondicaoPagamentoVO criarOuConsultarSeExistePorNome(int nrParcelas) throws Exception {
        CondicaoPagamentoVO condicao = new CondicaoPagamentoVO();
        String plural = nrParcelas == 1 ? " VEZ " : " VEZES ";
        condicao.setDescricao("EM " + nrParcelas + plural + "- CARTAO RECORRENCIA");
        condicao.setIntervaloEntreParcela(30);
        condicao.setEntrada(true);
        condicao.setNrParcelas(nrParcelas);
        List<CondicaoPagamentoVO> lista = this.consultarPorDescricao(
                condicao.getDescricao(), false, Uteis.NIVELMONTARDADOS_TODOS);
        if (lista.isEmpty()) {
            this.incluir(condicao);
            return condicao;
        } else {
            return lista.get(0);
        }
    }

    public String consultarJSON() throws Exception {
        StringBuilder json;
        boolean dados;
        try (PreparedStatement ps = getRS()) {
            try (ResultSet rs = ps.executeQuery()) {

                json = new StringBuilder();
                json.append("{\"aaData\":[");
                dados = false;
                while (rs.next()) {
                    dados = true;
                    json.append("[\"").append(rs.getString("codigo")).append("\",");
                    json.append("\"").append(Uteis.normalizarStringJSON(rs.getString("descricao"))).append("\",");
                    json.append("\"").append(Uteis.normalizarStringJSON(rs.getString("conveniocobranca"))).append("\",");
                    json.append("\"").append(rs.getString("nrparcelas")).append("\",");
                    json.append("\"").append(rs.getString("percentualvalorentrada")).append("\"],");
                }
            }
        }
        if (dados) {
            json.deleteCharAt(json.toString().length() - 1);
        }
        json.append("]}");
        return json.toString();
    }

    private PreparedStatement getRS() throws SQLException {
        String sql = "SELECT cp.codigo, cp.descricao, cc.descricao AS conveniocobranca, nrparcelas, percentualvalorentrada \n" + "FROM condicaopagamento cp LEFT JOIN conveniocobranca cc ON cp.tipoconveniocobranca = cc.tipoconvenio \n" + "  ORDER BY cp.descricao";
        return con.prepareStatement(sql);
    }


    public List<CondicaoPagamentoPlanoTO> consultarCondicaoPagamentoPlano(Integer codigoCondicaoPagamento, Integer codigoEmpresa)throws Exception{
        StringBuilder sql = new StringBuilder();
        sql.append("select cp.codigo as codigoCondPag, cp.descricao as descricaoCondPag,  \n");
        sql.append("p.codigo as codigoPlano, p.descricao as descricaoPlano, pd.numeroMeses as duracaoPlano \n");
        sql.append("from condicaoPagamento cp \n");
        sql.append("inner join planocondicaopagamento pcp on pcp.condicaoPagamento = cp.codigo \n");
        sql.append("inner join planoDuracao pd on pd.codigo = pcp.planoDuracao \n");
        sql.append("inner join plano p on p.codigo = pd.plano \n");
        sql.append("where cp.codigo = ").append(codigoCondicaoPagamento).append(" and p.empresa = ").append(codigoEmpresa).append(" \n");
        sql.append("and p.ingressoAte >= current_date \n");
        sql.append("order by p.descricao, pd.numeroMeses");
        List<CondicaoPagamentoPlanoTO> lista;
        try (Statement st = con.createStatement()) {
            try (ResultSet rs = st.executeQuery(sql.toString())) {
                lista = new ArrayList<CondicaoPagamentoPlanoTO>();
                while (rs.next()) {
                    CondicaoPagamentoPlanoTO obj = new CondicaoPagamentoPlanoTO();
                    obj.setCondicaoPagamentoVO(new CondicaoPagamentoVO());
                    obj.getCondicaoPagamentoVO().setCodigo(rs.getInt("codigoCondPag"));
                    obj.getCondicaoPagamentoVO().setDescricao(rs.getString("descricaoCondPag"));
                    obj.setPlanoVO(new PlanoVO());
                    obj.getPlanoVO().setCodigo(rs.getInt("codigoPlano"));
                    obj.getPlanoVO().setDescricao(rs.getString("descricaoPlano"));
                    obj.setDuracaoPlano(rs.getInt("duracaoPlano"));
                    lista.add(obj);
                }
            }
        }
        return lista;
    }

    public List consultarParaImpressao(String filtro, String ordem, String campoOrdenacao, int i) throws SQLException {

        List lista;
        try (PreparedStatement ps = getRS()) {
            try (ResultSet rs = ps.executeQuery()) {
                lista = new ArrayList();

                while (rs.next()) {

                    CondicaoPagamentoVO cp = new CondicaoPagamentoVO();
                    String geral = rs.getString("codigo") + rs.getString("descricao") + rs.getString("conveniocobranca") + rs.getString("nrparcelas") + rs.getString("percentualvalorentrada");
                    if (geral.toLowerCase().contains(filtro.toLowerCase())) {
                        cp.setCodigo(rs.getInt("codigo"));
                        cp.setDescricao(rs.getString("descricao"));
                        cp.setTipoconvenio(rs.getString("conveniocobranca"));
                        cp.setNrParcelas(rs.getInt("nrparcelas"));
                        cp.setPercentualValorEntrada(rs.getDouble("percentualvalorentrada"));
                        lista.add(cp);
                    }
                }
            }
        }
        if (campoOrdenacao.equals("Código")) {
            Ordenacao.ordenarLista(lista, "codigo");
        } else if (campoOrdenacao.equals("Descrição")) {
            Ordenacao.ordenarLista(lista, "descricao");
        } else if (campoOrdenacao.equals("Convênio Cobrança")) {
            Ordenacao.ordenarLista(lista, "convenio_Apresentar");
        } else if (campoOrdenacao.equals("Nr. de Parcelas")) {
            Ordenacao.ordenarLista(lista, "nrParcelas");
        } else if (campoOrdenacao.equals("Percentual de Entrada")) {
            Ordenacao.ordenarLista(lista, "percentualValorEntrada");
        }
        if (ordem.contains("desc")) {
            Collections.reverse(lista);
        }
        return lista;

    }
}

package negocio.facade.jdbc.plano;

import negocio.comuns.plano.CondicaoPagamentoVO;

import java.io.StringBufferInputStream;
import java.util.Iterator;
import negocio.comuns.plano.PlanoCondicaoPagamentoVO;
import negocio.comuns.plano.PlanoDuracaoVO;
import negocio.facade.jdbc.arquitetura.*;

import java.sql.Connection;
import java.sql.ResultSet;
import java.sql.PreparedStatement;
import java.sql.Statement;
import java.util.List;
import java.util.ArrayList;
import negocio.comuns.utilitarias.*;

/**
 * Classe de persistência que encapsula todas as operações de manipulação dos dados da classe <code>PlanoCondicaoPagamentoVO</code>.
 * Responsável por implementar operações como incluir, alterar, excluir e consultar pertinentes a classe <code>PlanoCondicaoPagamentoVO</code>.
 * Encapsula toda a interação com o banco de dados.
 * @see PlanoCondicaoPagamentoVO
 * @see SuperEntidade
 * @see Plano
 */
public class PlanoCondicaoPagamento extends SuperEntidade {   

    public PlanoCondicaoPagamento() throws Exception {
        super();
        setIdEntidade("Plano");
    }

    public PlanoCondicaoPagamento(Connection con) throws Exception {
		super(con);
        setIdEntidade("Plano");
    }

	/**
     * Operação responsável por retornar um novo objeto da classe <code>PlanoCondicaoPagamentoVO</code>.
     */
    public PlanoCondicaoPagamentoVO novo() throws Exception {
        incluir(getIdEntidade());
        PlanoCondicaoPagamentoVO obj = new PlanoCondicaoPagamentoVO();
        return obj;
    }

    /**
     * Operação responsável por incluir no banco de dados um objeto da classe <code>PlanoCondicaoPagamentoVO</code>.
     * Primeiramente valida os dados (<code>validarDados</code>) do objeto. Verifica a conexão com o banco de dados e a permissão do usuário
     * para realizar esta operacão na entidade.
     * Isto, através da operação <code>incluir</code> da superclasse.
     * @param obj  Objeto da classe <code>PlanoCondicaoPagamentoVO</code> que será gravado no banco de dados.
     * @exception Exception Caso haja problemas de conexão, restrição de acesso ou validação de dados.
     */
    public void incluir(PlanoCondicaoPagamentoVO obj) throws Exception {
        PlanoCondicaoPagamentoVO.validarDados(obj);
        //  PlanoCondicaoPagamento.incluir(getIdEntidade());
        obj.realizarUpperCaseDados();
        String sql = "INSERT INTO PlanoCondicaoPagamento( planoDuracao, condicaoPagamento, percentualDesconto, tipooperacao, tipovalor, valorespecifico, qtdParcela ) VALUES ( ?, ?, ?, ?, ?, ?, ? )";
        PreparedStatement sqlInserir = con.prepareStatement(sql);
        if (obj.getPlanoDuracao().intValue() != 0) {
            sqlInserir.setInt(1, obj.getPlanoDuracao().intValue());
        } else {
            sqlInserir.setNull(1, 0);
        }
        if (obj.getCondicaoPagamento().getCodigo().intValue() != 0) {
            sqlInserir.setInt(2, obj.getCondicaoPagamento().getCodigo().intValue());
        } else {
            sqlInserir.setNull(2, 0);
        }
        sqlInserir.setDouble(3, obj.getPercentualDesconto().doubleValue());
        sqlInserir.setString(4, obj.getTipoOperacao());
        sqlInserir.setString(5, obj.getTipoValor());
        sqlInserir.setDouble(6, obj.getValorEspecifico().doubleValue());
        sqlInserir.setInt(7, obj.getQtdParcela());

        sqlInserir.execute();
        obj.setCodigo(obterValorChavePrimariaCodigo());
        obj.setNovoObj(new Boolean(false));
    }

    /**
     * Operação responsável por alterar no BD os dados de um objeto da classe <code>PlanoCondicaoPagamentoVO</code>.
     * Sempre utiliza a chave primária da classe como atributo para localização do registro a ser alterado.
     * Primeiramente valida os dados (<code>validarDados</code>) do objeto. Verifica a conexão com o banco de dados e a permissão do usuário
     * para realizar esta operacão na entidade.
     * Isto, através da operação <code>alterar</code> da superclasse.
     * @param obj    Objeto da classe <code>PlanoCondicaoPagamentoVO</code> que será alterada no banco de dados.
     * @exception Execption Caso haja problemas de conexão, restrição de acesso ou validação de dados.
     */
    public void alterar(PlanoCondicaoPagamentoVO obj) throws Exception {
        PlanoCondicaoPagamentoVO.validarDados(obj);
        obj.realizarUpperCaseDados();
        String sql = "UPDATE PlanoCondicaoPagamento set planoDuracao=?, condicaoPagamento=?, percentualDesconto=?, tipooperacao=?, tipovalor=?, valorespecifico=?, qtdParcela=? WHERE ((codigo = ?))";
        PreparedStatement sqlAlterar = con.prepareStatement(sql);
        if (obj.getPlanoDuracao().intValue() != 0) {
            sqlAlterar.setInt(1, obj.getPlanoDuracao().intValue());
        } else {
            sqlAlterar.setNull(1, 0);
        }
        if (obj.getCondicaoPagamento().getCodigo().intValue() != 0) {
            sqlAlterar.setInt(2, obj.getCondicaoPagamento().getCodigo().intValue());
        } else {
            sqlAlterar.setNull(2, 0);
        }
        sqlAlterar.setDouble(3, obj.getPercentualDesconto().doubleValue());
        sqlAlterar.setString(4, obj.getTipoOperacao());
        sqlAlterar.setString(5, obj.getTipoValor());
        sqlAlterar.setDouble(6, obj.getValorEspecifico().doubleValue());
        sqlAlterar.setInt(7, obj.getQtdParcela());
        sqlAlterar.setInt(8, obj.getCodigo().intValue());
        sqlAlterar.execute();
    }

    /**
     * Operação responsável por excluir no BD um objeto da classe <code>PlanoCondicaoPagamentoVO</code>.
     * Sempre localiza o registro a ser excluído através da chave primária da entidade.
     * Primeiramente verifica a conexão com o banco de dados e a permissão do usuário
     * para realizar esta operacão na entidade.
     * Isto, através da operação <code>excluir</code> da superclasse.
     * @param obj    Objeto da classe <code>PlanoCondicaoPagamentoVO</code> que será removido no banco de dados.
     * @exception Execption Caso haja problemas de conexão ou restrição de acesso.
     */
    public void excluir(PlanoCondicaoPagamentoVO obj) throws Exception {
        excluir(getIdEntidade());
        String sql = "DELETE FROM PlanoCondicaoPagamento WHERE ((codigo = ?))";
        PreparedStatement sqlExcluir = con.prepareStatement(sql);
        sqlExcluir.setInt(1, obj.getCodigo().intValue());
        sqlExcluir.execute();
    }

    /**
     * Responsável por realizar uma consulta de <code>PlanoCondicaoPagamento</code> através do valor do atributo 
     * <code>descricao</code> da classe <code>CondicaoPagamento</code>
     * Faz uso da operação <code>montarDadosConsulta</code> que realiza o trabalho de prerarar o List resultante.
     * @return  List Contendo vários objetos da classe <code>PlanoCondicaoPagamentoVO</code> resultantes da consulta.
     * @exception Execption Caso haja problemas de conexão ou restrição de acesso.
     */
    public List consultarPorDescricaoCondicaoPagamento(String valorConsulta, int nivelMontarDados) throws Exception {
        consultar(getIdEntidade(), true);
        String sqlStr = "SELECT PlanoCondicaoPagamento.* FROM PlanoCondicaoPagamento, CondicaoPagamento WHERE PlanoCondicaoPagamento.condicaoPagamento = CondicaoPagamento.codigo and upper( CondicaoPagamento.descricao ) like('" + valorConsulta.toUpperCase() + "%') ORDER BY CondicaoPagamento.descricao";
        Statement stm = con.createStatement();
        ResultSet tabelaResultado = stm.executeQuery(sqlStr);
        return montarDadosConsulta(tabelaResultado, nivelMontarDados, this.con);
    }

    public PlanoCondicaoPagamentoVO consultarPorCodigoCondicaoPagamento(Integer codigoCondicao, Integer planoDuracao, int nivelMontarDados) throws Exception {
        consultar(getIdEntidade(), true);
        String sqlStr = "SELECT * FROM PlanoCondicaoPagamento WHERE condicaoPagamento = " + codigoCondicao + " and planoDuracao = " + planoDuracao;
        Statement stm = con.createStatement();
        ResultSet tabelaResultado = stm.executeQuery(sqlStr);
        if (tabelaResultado.next()) {
            return montarDados(tabelaResultado, nivelMontarDados, this.con);
        }
        return null;
    }

    /**
     * Responsável por realizar uma consulta de <code>PlanoCondicaoPagamento</code> através do valor do atributo 
     * <code>descricao</code> da classe <code>Plano</code>
     * Faz uso da operação <code>montarDadosConsulta</code> que realiza o trabalho de prerarar o List resultante.
     * @return  List Contendo vários objetos da classe <code>PlanoCondicaoPagamentoVO</code> resultantes da consulta.
     * @exception Execption Caso haja problemas de conexão ou restrição de acesso.
     */
    public List consultarPorDescricaoPlano(String valorConsulta, int codEmpresa, int nivelMontarDados) throws Exception {
        consultar(getIdEntidade(), true);
        StringBuilder sql = new StringBuilder();
        sql.append("SELECT PlanoCondicaoPagamento.* FROM PlanoCondicaoPagamento, PlanoDuracao, Plano ");
        sql.append(" WHERE PlanoCondicaoPagamento.planoDuracao = PlanoDuracao.codigo ");
        sql.append(" AND Plano.empresa = ").append(codEmpresa);
        sql.append(" AND PlanoDuracao.plano = Plano.codigo ");
        sql.append(" AND UPPER(Plano.descricao) = UPPER('").append(valorConsulta).append("')");
        sql.append(" ORDER BY Plano.descricao ");
        Statement stm = con.createStatement();
        ResultSet tabelaResultado = stm.executeQuery(sql.toString());
        return montarDadosConsulta(tabelaResultado, nivelMontarDados, this.con);
    }

    /**
     * Responsável por realizar uma consulta de <code>PlanoCondicaoPagamento</code> através do valor do atributo 
     * <code>Integer codigo</code>. Retorna os objetos com valores iguais ou superiores ao parâmetro fornecido.
     * Faz uso da operação <code>montarDadosConsulta</code> que realiza o trabalho de prerarar o List resultante.
     * @param   controlarAcesso Indica se a aplicação deverá verificar se o usuário possui permissão para esta consulta ou não.
     * @return  List Contendo vários objetos da classe <code>PlanoCondicaoPagamentoVO</code> resultantes da consulta.
     * @exception Exception Caso haja problemas de conexão ou restrição de acesso.
     */
    public List consultarPorCodigo(Integer valorConsulta, boolean controlarAcesso, int nivelMontarDados) throws Exception {
        consultar(getIdEntidade(), controlarAcesso);
        String sqlStr = "SELECT * FROM PlanoCondicaoPagamento WHERE codigo >= " + valorConsulta.intValue() + " ORDER BY codigo";
        Statement stm = con.createStatement();
        ResultSet tabelaResultado = stm.executeQuery(sqlStr);
        return (montarDadosConsulta(tabelaResultado, nivelMontarDados, this.con));
    }

    /**
     * Responsável por montar os dados de vários objetos, resultantes de uma consulta ao banco de dados (<code>ResultSet</code>).
     * Faz uso da operação <code>montarDados</code> que realiza o trabalho para um objeto por vez.
     * @return  List Contendo vários objetos da classe <code>PlanoCondicaoPagamentoVO</code> resultantes da consulta.
     */
    public static List montarDadosConsulta(ResultSet tabelaResultado, int nivelMontarDados, Connection con) throws Exception {
        List vetResultado = new ArrayList();
        while (tabelaResultado.next()) {
            PlanoCondicaoPagamentoVO obj = new PlanoCondicaoPagamentoVO();
            obj = montarDados(tabelaResultado, nivelMontarDados, con);
            vetResultado.add(obj);
        }
        return vetResultado;
    }

    /**
     * Responsável por montar os dados resultantes de uma consulta ao banco de dados (<code>ResultSet</code>)
     * em um objeto da classe <code>PlanoCondicaoPagamentoVO</code>.
     * @return  O objeto da classe <code>PlanoCondicaoPagamentoVO</code> com os dados devidamente montados.
     */
    public static PlanoCondicaoPagamentoVO montarDados(ResultSet dadosSQL, int nivelMontarDados, Connection con) throws Exception {
        PlanoCondicaoPagamentoVO obj = new PlanoCondicaoPagamentoVO();
        obj.setCodigo(new Integer(dadosSQL.getInt("codigo")));
        obj.setPlanoDuracao(new Integer(dadosSQL.getInt("planoDuracao")));
        obj.getCondicaoPagamento().setCodigo(new Integer(dadosSQL.getInt("condicaoPagamento")));
        obj.setTipoValor(dadosSQL.getString("tipoValor"));
        obj.setTipoOperacao(dadosSQL.getString("tipoOperacao"));
        obj.setPercentualDesconto(new Double(dadosSQL.getDouble("percentualDesconto")));
        obj.setValorEspecifico(new Double(dadosSQL.getDouble("valorEspecifico")));
        obj.setQtdParcela(new Integer(dadosSQL.getInt("qtdParcela")));
        obj.setNovoObj(new Boolean(false));
        if (nivelMontarDados == Uteis.NIVELMONTARDADOS_DADOSBASICOS) {
            return obj;
        }

        montarDadosCondicaoPagamento(obj, Uteis.NIVELMONTARDADOS_DADOSBASICOS, con);
        return obj;
    }

    /**
     * Operação responsável por montar os dados de um objeto da classe <code>CondicaoPagamentoVO</code> relacionado ao objeto <code>PlanoCondicaoPagamentoVO</code>.
     * Faz uso da chave primária da classe <code>CondicaoPagamentoVO</code> para realizar a consulta.
     * @param obj  Objeto no qual será montado os dados consultados.
     */
    public static void montarDadosCondicaoPagamento(PlanoCondicaoPagamentoVO obj, int nivelMontarDados, Connection con) throws Exception {
        if (obj.getCondicaoPagamento().getCodigo().intValue() == 0) {
            obj.setCondicaoPagamento(new CondicaoPagamentoVO());
            return;
        }
        CondicaoPagamento condicaoPagamento = new CondicaoPagamento(con);
        obj.setCondicaoPagamento(condicaoPagamento.consultarPorChavePrimaria(obj.getCondicaoPagamento().getCodigo(), nivelMontarDados));
        condicaoPagamento = null;
    }

    /**
     * Operação responsável por excluir todos os objetos da <code>PlanoCondicaoPagamentoVO</code> no BD.
     * Faz uso da operação <code>excluir</code> disponível na classe <code>PlanoCondicaoPagamento</code>.
     * @param <code>plano</code> campo chave para exclusão dos objetos no BD.
     * @exception Exception  Erro de conexão com o BD ou restrição de acesso a esta operação.
     */
    public void excluirPlanoCondicaoPagamentos(Integer plano) throws Exception {
        excluir(getIdEntidade());
        String sql = "DELETE FROM PlanoCondicaoPagamento WHERE (planoDuracao = ?)";
        PreparedStatement sqlExcluir = con.prepareStatement(sql);
        sqlExcluir.setInt(1, plano.intValue());
        sqlExcluir.execute();
    }

    /**
     * Operação responsável por alterar todos os objetos da <code>PlanoCondicaoPagamentoVO</code> contidos em um Hashtable no BD.
     * Faz uso da operação <code>excluirPlanoCondicaoPagamentos</code> e <code>incluirPlanoCondicaoPagamentos</code> disponíveis na classe <code>PlanoCondicaoPagamento</code>.
     * @param objetos  List com os objetos a serem alterados ou incluídos no BD.
     * @exception Exception  Erro de conexão com o BD ou restrição de acesso a esta operação.
     */
    public void alterarPlanoCondicaoPagamentos(Integer plano, List objetos) throws Exception {
        String str = "DELETE FROM PlanoCondicaoPagamento WHERE planoDuracao = " + plano.intValue();
        Iterator i = objetos.iterator();
        while (i.hasNext()) {
            PlanoCondicaoPagamentoVO objeto = (PlanoCondicaoPagamentoVO) i.next();
            str += " AND codigo <> " + objeto.getCodigo().intValue();
        }
        PreparedStatement sqlExcluir = con.prepareStatement(str);
        sqlExcluir.execute();
        Iterator e = objetos.iterator();
        while (e.hasNext()) {
            PlanoCondicaoPagamentoVO obj = (PlanoCondicaoPagamentoVO) e.next();
            if (obj.getCodigo().equals(new Integer(0))) {
                obj.setPlanoDuracao(plano);
                incluir(obj);
            } else {
                alterar(obj);
            }
        }

        //        excluirPlanoCondicaoPagamentos(plano);
//        incluirPlanoCondicaoPagamentos(plano, objetos);
    }

    /**
     * Operação responsável por incluir objetos da <code>PlanoCondicaoPagamentoVO</code> no BD.
     * Garantindo o relacionamento com a entidade principal <code>plano.Plano</code> através do atributo de vínculo.
     * @param objetos List contendo os objetos a serem gravados no BD da classe.
     * @exception Exception  Erro de conexão com o BD ou restrição de acesso a esta operação.
     */
    public void incluirPlanoCondicaoPagamentos(Integer planoPrm, List objetos) throws Exception {
        Iterator e = objetos.iterator();
        while (e.hasNext()) {
            PlanoCondicaoPagamentoVO obj = (PlanoCondicaoPagamentoVO) e.next();
            obj.setPlanoDuracao(planoPrm);
            incluir(obj);
        }
    }

    /**
     * Operação responsável por consultar todos os <code>PlanoCondicaoPagamentoVO</code> relacionados a um objeto da classe <code>plano.Plano</code>.
     * @param plano  Atributo de <code>plano.Plano</code> a ser utilizado para localizar os objetos da classe <code>PlanoCondicaoPagamentoVO</code>.
     * @return List  Contendo todos os objetos da classe <code>PlanoCondicaoPagamentoVO</code> resultantes da consulta.
     * @exception Exception  Erro de conexão com o BD ou restrição de acesso a esta operação.
     */
    public List consultarPlanoCondicaoPagamentos(Integer plano, int nivelMontarDados) throws Exception {
        consultar(getIdEntidade());
        List objetos = new ArrayList();
        String sql = "SELECT * FROM PlanoCondicaoPagamento WHERE planoDuracao = ?";
        PreparedStatement sqlConsulta = con.prepareStatement(sql);
        sqlConsulta.setInt(1, plano.intValue());
        ResultSet resultado = sqlConsulta.executeQuery();
        while (resultado.next()) {
            PlanoCondicaoPagamentoVO novoObj = new PlanoCondicaoPagamentoVO();
            novoObj = PlanoCondicaoPagamento.montarDados(resultado, nivelMontarDados, this.con);
            objetos.add(novoObj);
        }
        return objetos;
    }

    public List consultaPlanoCondicaoPagamentosNumeroParcelas(Integer plano, Integer numeroParcelas, int nivelMontarDados) throws Exception {
        consultar(getIdEntidade());
        List objetos = new ArrayList();
        String sql = "SELECT * FROM PlanoCondicaoPagamento WHERE planoDuracao = ? AND qtdparcela = ?";
        PreparedStatement sqlConsulta = con.prepareStatement(sql);
        sqlConsulta.setInt(1, plano.intValue());
        sqlConsulta.setInt(2, numeroParcelas.intValue());
        ResultSet resultado = sqlConsulta.executeQuery();
        if(!resultado.isBeforeFirst()){
            sql = "SELECT p.* FROM PlanoCondicaoPagamento p INNER JOIN condicaopagamento c ON c.codigo = p.condicaopagamento WHERE c.descricao like '%VISTA%'";
            sqlConsulta = con.prepareStatement(sql);
            resultado = sqlConsulta.executeQuery();
        }
        while (resultado.next()) {
            PlanoCondicaoPagamentoVO novoObj = new PlanoCondicaoPagamentoVO();
            novoObj = PlanoCondicaoPagamento.montarDados(resultado, nivelMontarDados, this.con);
            objetos.add(novoObj);
        }
        return objetos;
    }

    /**
     * Operação responsável por localizar um objeto da classe <code>PlanoCondicaoPagamentoVO</code>
     * através de sua chave primária. 
     * @exception Exception Caso haja problemas de conexão ou localização do objeto procurado.
     */
    public PlanoCondicaoPagamentoVO consultarPorChavePrimaria(Integer codigoPrm, int nivelMontarDados) throws Exception {
        consultar(getIdEntidade(), false);
        String sql = "SELECT * FROM PlanoCondicaoPagamento WHERE codigo = ?";
        PreparedStatement sqlConsultar = con.prepareStatement(sql);
        sqlConsultar.setInt(1, codigoPrm.intValue());
        ResultSet tabelaResultado = sqlConsultar.executeQuery();
        if (!tabelaResultado.next()) {
            throw new ConsistirException("Dados Não Encontrados ( PlanoCondicaoPagamento ).");
        }
        return (montarDados(tabelaResultado, nivelMontarDados, this.con));
    }

    public PlanoCondicaoPagamentoVO consultarPorPlanoDuracaoRecorrencia(Integer planoduracao, int nivelMontarDados) throws Exception {
        consultar(getIdEntidade(), false);
        String sql = "SELECT * FROM PlanoCondicaoPagamento  WHERE planoduracao = ?";
        PreparedStatement sqlConsultar = con.prepareStatement(sql);
        sqlConsultar.setInt(1, planoduracao.intValue());
        ResultSet tabelaResultado = sqlConsultar.executeQuery();
        if (!tabelaResultado.next()) {
            throw new ConsistirException("Dados Não Encontrados ( PlanoCondicaoPagamento ).");
        }
        return (montarDados(tabelaResultado, nivelMontarDados, this.con));
    }
    
    public PlanoCondicaoPagamentoVO consultarPorPlanoDuracaoCondicao(Integer planoduracao, Integer condicao, int nivelMontarDados) throws Exception {
        consultar(getIdEntidade(), false);
        String sql = "SELECT * FROM PlanoCondicaoPagamento  WHERE planoduracao = ? AND condicaopagamento = ?";
        PreparedStatement sqlConsultar = con.prepareStatement(sql);
        sqlConsultar.setInt(1, planoduracao);
        sqlConsultar.setInt(2, condicao);
        ResultSet tabelaResultado = sqlConsultar.executeQuery();
        if (!tabelaResultado.next()) {
            throw new ConsistirException("Dados Não Encontrados ( PlanoCondicaoPagamento ).");
        }
        return (montarDados(tabelaResultado, nivelMontarDados, this.con));
    }

    public void incluirPlanoCondicaoPagamento(List<CondicaoPagamentoPlanoTO> lista)throws Exception{
        for (CondicaoPagamentoPlanoTO obj: lista){
            PlanoCondicaoPagamentoVO planoCondicaoPagamentoVO = new PlanoCondicaoPagamentoVO();
            planoCondicaoPagamentoVO.setCondicaoPagamento(obj.getCondicaoPagamentoVO());
            PlanoDuracaoVO planoDuracaoVO = getFacade().getPlanoDuracao().consultarPorNumeroMesesPlano(obj.getDuracaoPlano(), obj.getPlanoVO().getCodigo(),Uteis.NIVELMONTARDADOS_DADOSBASICOS);
            if (planoDuracaoVO == null) {
                continue;
            }
            planoCondicaoPagamentoVO.setPlanoDuracao(planoDuracaoVO.getCodigo());
            planoCondicaoPagamentoVO.setQtdParcela(obj.getCondicaoPagamentoVO().getNrParcelas());
            incluir(planoCondicaoPagamentoVO);
        }
    }

    public void excluirPlanoCondicaoPagamento(CondicaoPagamentoVO condicaoPagamentoVO)throws Exception{
        StringBuilder sql = new StringBuilder();
        sql.append("delete from PlanoCondicaoPagamento where condicaopagamento = ").append(condicaoPagamentoVO.getCodigo());
        Statement st = con.createStatement();
        st.execute(sql.toString());
    }

    public List<String> alterarPlanoCondicaoPagamento(CondicaoPagamentoVO condicaoPagamentoVO)throws Exception{
        List<String> condicoesNaoExcluidas = new ArrayList<String>();
        List<CondicaoPagamentoPlanoTO> listaIncluir = CondicaoPagamentoPlanoTO.consultarListaIncluir(condicaoPagamentoVO.getListaCondicaoPagamentoPlanoAntesDeAlterar(),condicaoPagamentoVO.getListaCondicaoPagamentoPlano());
        List<CondicaoPagamentoPlanoTO> listaExcluir = CondicaoPagamentoPlanoTO.consultarListaExcluir(condicaoPagamentoVO.getListaCondicaoPagamentoPlanoAntesDeAlterar(),condicaoPagamentoVO.getListaCondicaoPagamentoPlano());
        incluirPlanoCondicaoPagamento(listaIncluir);
        StringBuilder sqlExcluir = new StringBuilder();
        sqlExcluir.append("delete from PlanoCondicaoPagamento \n");
        sqlExcluir.append("where condicaoPagamento = ? and planoDuracao = ? and qtdParcela = ?");
        PreparedStatement pst = con.prepareStatement(sqlExcluir.toString());
        for (CondicaoPagamentoPlanoTO obj: listaExcluir){
            PlanoDuracaoVO planoDuracaoVO = getFacade().getPlanoDuracao().consultarPorNumeroMesesPlano(obj.getDuracaoPlano(), obj.getPlanoVO().getCodigo(),Uteis.NIVELMONTARDADOS_DADOSBASICOS);
            List<PlanoCondicaoPagamento> listaCondicoes = consultarPlanoCondicaoPagamentos(planoDuracaoVO.getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS);
            if(listaCondicoes.size() < 2){
                condicoesNaoExcluidas.add("duração "+planoDuracaoVO.getDescricaoDuracao()+" do plano "+obj.getPlanoVO().getDescricao());
                continue;
            }
            pst.setInt(1, condicaoPagamentoVO.getCodigo());
            pst.setInt(2, planoDuracaoVO.getCodigo());
            pst.setInt(3, condicaoPagamentoVO.getNrParcelas());
            pst.execute();
        }
        return condicoesNaoExcluidas;
    }

    public void atualizarParcelasPlanoCondicaoPagamento(CondicaoPagamentoVO condicaoPagamentoVO) throws Exception {
        String sql = "UPDATE PlanoCondicaoPagamento set qtdParcela=? "
                + "WHERE condicaopagamento = ?";

        try (PreparedStatement ps = con.prepareStatement(sql)) {
            ps.setInt(1, condicaoPagamentoVO.getNrParcelas().intValue());
            ps.setInt(2, condicaoPagamentoVO.getCodigo().intValue());
            ps.execute();
        }
    }
}

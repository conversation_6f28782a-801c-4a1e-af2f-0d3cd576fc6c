package negocio.facade.jdbc.plano;

import java.sql.Connection;
import negocio.interfaces.plano.HorarioDisponibilidadeInterfaceFacade;
import java.util.Iterator;
import negocio.comuns.plano.HorarioDisponibilidadeVO;
import negocio.facade.jdbc.arquitetura.*;
import java.sql.ResultSet;
import java.sql.PreparedStatement;
import java.sql.Statement;
import java.util.List;
import java.util.ArrayList;
import negocio.comuns.utilitarias.*;

/**
 * Classe de persistência que encapsula todas as operações de manipulação dos dados da classe <code>HorarioDisponibilidadeVO</code>.
 * Responsável por implementar operações como incluir, alterar, excluir e consultar pertinentes a classe <code>HorarioDisponibilidadeVO</code>.
 * Encapsula toda a interação com o banco de dados.
 * @see HorarioDisponibilidadeVO
 * @see SuperEntidade
 * @see Horario
 */
public class HorarioDisponibilidade extends SuperEntidade implements HorarioDisponibilidadeInterfaceFacade {

    public HorarioDisponibilidade() throws Exception {
        super();
        setIdEntidade("Horario");
    }

    public HorarioDisponibilidade(Connection conexao) throws Exception {
        super(conexao);
        setIdEntidade("Horario");
    }

    /**
     * Operação responsável por retornar um novo objeto da classe <code>HorarioDisponibilidadeVO</code>.
     */
    public HorarioDisponibilidadeVO novo() throws Exception {
        incluir(getIdEntidade());
        HorarioDisponibilidadeVO obj = new HorarioDisponibilidadeVO();
        return obj;
    }

    /**
     * Operação responsável por incluir no banco de dados um objeto da classe <code>HorarioDisponibilidadeVO</code>.
     * Primeiramente valida os dados (<code>validarDados</code>) do objeto. Verifica a conexão com o banco de dados e a permissão do usuário
     * para realizar esta operacão na entidade.
     * Isto, através da operação <code>incluir</code> da superclasse.
     * @param obj  Objeto da classe <code>HorarioDisponibilidadeVO</code> que será gravado no banco de dados.
     * @exception Exception Caso haja problemas de conexão, restrição de acesso ou validação de dados.
     */
    public void incluir(HorarioDisponibilidadeVO obj) throws Exception {
        HorarioDisponibilidadeVO.validarDados(obj);
        obj.realizarUpperCaseDados();
        String sql = "INSERT INTO HorarioDisponibilidade( identificador, horario, hora0000, hora0030, hora0100, hora0130, hora0200, hora0230, hora0300, hora0330, hora0400, hora0430, hora0500, hora0530, hora0600, hora0630, hora0700, hora0730, hora0800, hora0830, hora0900, hora0930, hora1000, hora1030, hora1100, hora1130, hora1200, hora1230, hora1300, hora1330, hora1400, hora1430, hora1500, hora1530, hora1600, hora1630, hora1700, hora1730, hora1800, hora1830, hora1900, hora1930, hora2000, hora2030, hora2100, hora2130, hora2200, hora2230, hora2300, hora2330,desenharTodos,matutino,vespertino,noturno ) VALUES ( ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ? )";
        PreparedStatement sqlInserir = con.prepareStatement(sql);
        sqlInserir.setString(1, obj.getIdentificador());
        if (obj.getHorario().intValue() != 0) {
            sqlInserir.setInt(2, obj.getHorario().intValue());
        } else {
            sqlInserir.setNull(2, 0);
        }
        sqlInserir.setBoolean(3, obj.isHora0000().booleanValue());
        sqlInserir.setBoolean(4, obj.isHora0030().booleanValue());
        sqlInserir.setBoolean(5, obj.isHora0100().booleanValue());
        sqlInserir.setBoolean(6, obj.isHora0130().booleanValue());
        sqlInserir.setBoolean(7, obj.isHora0200().booleanValue());
        sqlInserir.setBoolean(8, obj.isHora0230().booleanValue());
        sqlInserir.setBoolean(9, obj.isHora0300().booleanValue());
        sqlInserir.setBoolean(10, obj.isHora0330().booleanValue());
        sqlInserir.setBoolean(11, obj.isHora0400().booleanValue());
        sqlInserir.setBoolean(12, obj.isHora0430().booleanValue());
        sqlInserir.setBoolean(13, obj.isHora0500().booleanValue());
        sqlInserir.setBoolean(14, obj.isHora0530().booleanValue());
        sqlInserir.setBoolean(15, obj.isHora0600().booleanValue());
        sqlInserir.setBoolean(16, obj.isHora0630().booleanValue());
        sqlInserir.setBoolean(17, obj.isHora0700().booleanValue());
        sqlInserir.setBoolean(18, obj.isHora0730().booleanValue());
        sqlInserir.setBoolean(19, obj.isHora0800().booleanValue());
        sqlInserir.setBoolean(20, obj.isHora0830().booleanValue());
        sqlInserir.setBoolean(21, obj.isHora0900().booleanValue());
        sqlInserir.setBoolean(22, obj.isHora0930().booleanValue());
        sqlInserir.setBoolean(23, obj.isHora1000().booleanValue());
        sqlInserir.setBoolean(24, obj.isHora1030().booleanValue());
        sqlInserir.setBoolean(25, obj.isHora1100().booleanValue());
        sqlInserir.setBoolean(26, obj.isHora1130().booleanValue());
        sqlInserir.setBoolean(27, obj.isHora1200().booleanValue());
        sqlInserir.setBoolean(28, obj.isHora1230().booleanValue());
        sqlInserir.setBoolean(29, obj.isHora1300().booleanValue());
        sqlInserir.setBoolean(30, obj.isHora1330().booleanValue());
        sqlInserir.setBoolean(31, obj.isHora1400().booleanValue());
        sqlInserir.setBoolean(32, obj.isHora1430().booleanValue());
        sqlInserir.setBoolean(33, obj.isHora1500().booleanValue());
        sqlInserir.setBoolean(34, obj.isHora1530().booleanValue());
        sqlInserir.setBoolean(35, obj.isHora1600().booleanValue());
        sqlInserir.setBoolean(36, obj.isHora1630().booleanValue());
        sqlInserir.setBoolean(37, obj.isHora1700().booleanValue());
        sqlInserir.setBoolean(38, obj.isHora1730().booleanValue());
        sqlInserir.setBoolean(39, obj.isHora1800().booleanValue());
        sqlInserir.setBoolean(40, obj.isHora1830().booleanValue());
        sqlInserir.setBoolean(41, obj.isHora1900().booleanValue());
        sqlInserir.setBoolean(42, obj.isHora1930().booleanValue());
        sqlInserir.setBoolean(43, obj.isHora2000().booleanValue());
        sqlInserir.setBoolean(44, obj.isHora2030().booleanValue());
        sqlInserir.setBoolean(45, obj.isHora2100().booleanValue());
        sqlInserir.setBoolean(46, obj.isHora2130().booleanValue());
        sqlInserir.setBoolean(47, obj.isHora2200().booleanValue());
        sqlInserir.setBoolean(48, obj.isHora2230().booleanValue());
        sqlInserir.setBoolean(49, obj.isHora2300().booleanValue());
        sqlInserir.setBoolean(50, obj.isHora2330().booleanValue());
        sqlInserir.setBoolean(51, obj.getDesenharTodos());
        sqlInserir.setBoolean(52, obj.getMatutino());
        sqlInserir.setBoolean(53, obj.getVespertino());
        sqlInserir.setBoolean(54, obj.getNoturno());
        sqlInserir.execute();
        obj.setCodigo(obterValorChavePrimariaCodigo());
        obj.setNovoObj(new Boolean(false));
    }

    /**
     * Operação responsável por alterar no BD os dados de um objeto da classe <code>HorarioDisponibilidadeVO</code>.
     * Sempre utiliza a chave primária da classe como atributo para localização do registro a ser alterado.
     * Primeiramente valida os dados (<code>validarDados</code>) do objeto. Verifica a conexão com o banco de dados e a permissão do usuário
     * para realizar esta operacão na entidade.
     * Isto, através da operação <code>alterar</code> da superclasse.
     * @param obj    Objeto da classe <code>HorarioDisponibilidadeVO</code> que será alterada no banco de dados.
     * @exception Execption Caso haja problemas de conexão, restrição de acesso ou validação de dados.
     */
    public void alterar(HorarioDisponibilidadeVO obj) throws Exception {
        HorarioDisponibilidadeVO.validarDados(obj);
        alterar(getIdEntidade());
        obj.realizarUpperCaseDados();
        String sql = "UPDATE HorarioDisponibilidade set identificador=?, horario=?, hora0000=?, hora0030=?, hora0100=?, hora0130=?, hora0200=?, hora0230=?, hora0300=?, hora0330=?, hora0400=?, hora0430=?, hora0500=?, hora0530=?, hora0600=?, hora0630=?, hora0700=?, hora0730=?, hora0800=?, hora0830=?, hora0900=?, hora0930=?, hora1000=?, hora1030=?, hora1100=?, hora1130=?, hora1200=?, hora1230=?, hora1300=?, hora1330=?, hora1400=?, hora1430=?, hora1500=?, hora1530=?, hora1600=?, hora1630=?, hora1700=?, hora1730=?, hora1800=?, hora1830=?, hora1900=?, hora1930=?, hora2000=?, hora2030=?, hora2100=?, hora2130=?, hora2200=?, hora2230=?, hora2300=?, hora2330=? , desenharTodos=?, matutino=?, vespertino=?, noturno=? WHERE ((codigo = ?))";
        PreparedStatement sqlAlterar = con.prepareStatement(sql);
        sqlAlterar.setString(1, obj.getIdentificador());
        if (obj.getHorario().intValue() != 0) {
            sqlAlterar.setInt(2, obj.getHorario().intValue());
        } else {
            sqlAlterar.setNull(2, 0);
        }
        sqlAlterar.setBoolean(3, obj.isHora0000().booleanValue());
        sqlAlterar.setBoolean(4, obj.isHora0030().booleanValue());
        sqlAlterar.setBoolean(5, obj.isHora0100().booleanValue());
        sqlAlterar.setBoolean(6, obj.isHora0130().booleanValue());
        sqlAlterar.setBoolean(7, obj.isHora0200().booleanValue());
        sqlAlterar.setBoolean(8, obj.isHora0230().booleanValue());
        sqlAlterar.setBoolean(9, obj.isHora0300().booleanValue());
        sqlAlterar.setBoolean(10, obj.isHora0330().booleanValue());
        sqlAlterar.setBoolean(11, obj.isHora0400().booleanValue());
        sqlAlterar.setBoolean(12, obj.isHora0430().booleanValue());
        sqlAlterar.setBoolean(13, obj.isHora0500().booleanValue());
        sqlAlterar.setBoolean(14, obj.isHora0530().booleanValue());
        sqlAlterar.setBoolean(15, obj.isHora0600().booleanValue());
        sqlAlterar.setBoolean(16, obj.isHora0630().booleanValue());
        sqlAlterar.setBoolean(17, obj.isHora0700().booleanValue());
        sqlAlterar.setBoolean(18, obj.isHora0730().booleanValue());
        sqlAlterar.setBoolean(19, obj.isHora0800().booleanValue());
        sqlAlterar.setBoolean(20, obj.isHora0830().booleanValue());
        sqlAlterar.setBoolean(21, obj.isHora0900().booleanValue());
        sqlAlterar.setBoolean(22, obj.isHora0930().booleanValue());
        sqlAlterar.setBoolean(23, obj.isHora1000().booleanValue());
        sqlAlterar.setBoolean(24, obj.isHora1030().booleanValue());
        sqlAlterar.setBoolean(25, obj.isHora1100().booleanValue());
        sqlAlterar.setBoolean(26, obj.isHora1130().booleanValue());
        sqlAlterar.setBoolean(27, obj.isHora1200().booleanValue());
        sqlAlterar.setBoolean(28, obj.isHora1230().booleanValue());
        sqlAlterar.setBoolean(29, obj.isHora1300().booleanValue());
        sqlAlterar.setBoolean(30, obj.isHora1330().booleanValue());
        sqlAlterar.setBoolean(31, obj.isHora1400().booleanValue());
        sqlAlterar.setBoolean(32, obj.isHora1430().booleanValue());
        sqlAlterar.setBoolean(33, obj.isHora1500().booleanValue());
        sqlAlterar.setBoolean(34, obj.isHora1530().booleanValue());
        sqlAlterar.setBoolean(35, obj.isHora1600().booleanValue());
        sqlAlterar.setBoolean(36, obj.isHora1630().booleanValue());
        sqlAlterar.setBoolean(37, obj.isHora1700().booleanValue());
        sqlAlterar.setBoolean(38, obj.isHora1730().booleanValue());
        sqlAlterar.setBoolean(39, obj.isHora1800().booleanValue());
        sqlAlterar.setBoolean(40, obj.isHora1830().booleanValue());
        sqlAlterar.setBoolean(41, obj.isHora1900().booleanValue());
        sqlAlterar.setBoolean(42, obj.isHora1930().booleanValue());
        sqlAlterar.setBoolean(43, obj.isHora2000().booleanValue());
        sqlAlterar.setBoolean(44, obj.isHora2030().booleanValue());
        sqlAlterar.setBoolean(45, obj.isHora2100().booleanValue());
        sqlAlterar.setBoolean(46, obj.isHora2130().booleanValue());
        sqlAlterar.setBoolean(47, obj.isHora2200().booleanValue());
        sqlAlterar.setBoolean(48, obj.isHora2230().booleanValue());
        sqlAlterar.setBoolean(49, obj.isHora2300().booleanValue());
        sqlAlterar.setBoolean(50, obj.isHora2330().booleanValue());
        sqlAlterar.setBoolean(51, obj.getDesenharTodos());
        sqlAlterar.setBoolean(52, obj.getMatutino());
        sqlAlterar.setBoolean(53, obj.getVespertino());
        sqlAlterar.setBoolean(54, obj.getNoturno());
        sqlAlterar.setInt(55, obj.getCodigo().intValue());
        sqlAlterar.execute();
    }

    /**
     * Operação responsável por excluir no BD um objeto da classe <code>HorarioDisponibilidadeVO</code>.
     * Sempre localiza o registro a ser excluído através da chave primária da entidade.
     * Primeiramente verifica a conexão com o banco de dados e a permissão do usuário
     * para realizar esta operacão na entidade.
     * Isto, através da operação <code>excluir</code> da superclasse.
     * @param obj    Objeto da classe <code>HorarioDisponibilidadeVO</code> que será removido no banco de dados.
     * @exception Execption Caso haja problemas de conexão ou restrição de acesso.
     */
    public void excluir(HorarioDisponibilidadeVO obj) throws Exception {
        excluir(getIdEntidade());
        String sql = "DELETE FROM HorarioDisponibilidade WHERE ((codigo = ?))";
        PreparedStatement sqlExcluir = con.prepareStatement(sql);
        sqlExcluir.setInt(1, obj.getCodigo().intValue());
        sqlExcluir.execute();
    }

    /**
     * Responsável por realizar uma consulta de <code>HorarioDisponibilidade</code> através do valor do atributo 
     * <code>descricao</code> da classe <code>Horario</code>
     * Faz uso da operação <code>montarDadosConsulta</code> que realiza o trabalho de prerarar o List resultante.
     * @return  List Contendo vários objetos da classe <code>HorarioDisponibilidadeVO</code> resultantes da consulta.
     * @exception Execption Caso haja problemas de conexão ou restrição de acesso.
     */
    public List consultarPorDescricaoHorario(String valorConsulta, int nivelMontarDados) throws Exception {
        consultar(getIdEntidade(), true);
        String sqlStr = "SELECT HorarioDisponibilidade.* FROM HorarioDisponibilidade, Horario WHERE HorarioDisponibilidade.horario = Horario.codigo and upper( Horario.descricao ) like('" + valorConsulta.toUpperCase() + "%') ORDER BY Horario.descricao";
        Statement stm = con.createStatement();
        ResultSet tabelaResultado = stm.executeQuery(sqlStr);
        return montarDadosConsulta(tabelaResultado, nivelMontarDados);
    }

    /**
     * Responsável por realizar uma consulta de <code>HorarioDisponibilidade</code> através do valor do atributo 
     * <code>String identificador</code>. Retorna os objetos, com início do valor do atributo idêntico ao parâmetro fornecido.
     * Faz uso da operação <code>montarDadosConsulta</code> que realiza o trabalho de prerarar o List resultante.
     * @param   controlarAcesso Indica se a aplicação deverá verificar se o usuário possui permissão para esta consulta ou não.
     * @return  List Contendo vários objetos da classe <code>HorarioDisponibilidadeVO</code> resultantes da consulta.
     * @exception Exception Caso haja problemas de conexão ou restrição de acesso.
     */
    public List consultarPorIdentificador(String valorConsulta, boolean controlarAcesso, int nivelMontarDados) throws Exception {
        consultar(getIdEntidade(), controlarAcesso);
        String sqlStr = "SELECT * FROM HorarioDisponibilidade WHERE upper( identificador ) like('" + valorConsulta.toUpperCase() + "%') ORDER BY identificador";
        Statement stm = con.createStatement();
        ResultSet tabelaResultado = stm.executeQuery(sqlStr);
        return (montarDadosConsulta(tabelaResultado, nivelMontarDados));
    }

    /**
     * Responsável por realizar uma consulta de <code>HorarioDisponibilidade</code> através do valor do atributo 
     * <code>Integer codigo</code>. Retorna os objetos com valores iguais ou superiores ao parâmetro fornecido.
     * Faz uso da operação <code>montarDadosConsulta</code> que realiza o trabalho de prerarar o List resultante.
     * @param   controlarAcesso Indica se a aplicação deverá verificar se o usuário possui permissão para esta consulta ou não.
     * @return  List Contendo vários objetos da classe <code>HorarioDisponibilidadeVO</code> resultantes da consulta.
     * @exception Exception Caso haja problemas de conexão ou restrição de acesso.
     */
    public List consultarPorCodigo(Integer valorConsulta, boolean controlarAcesso, int nivelMontarDados) throws Exception {
        consultar(getIdEntidade(), controlarAcesso);
        String sqlStr = "SELECT * FROM HorarioDisponibilidade WHERE codigo >= " + valorConsulta.intValue() + " ORDER BY codigo";
        Statement stm = con.createStatement();
        ResultSet tabelaResultado = stm.executeQuery(sqlStr);
        return (montarDadosConsulta(tabelaResultado, nivelMontarDados));
    }

    /**
     * Responsável por montar os dados de vários objetos, resultantes de uma consulta ao banco de dados (<code>ResultSet</code>).
     * Faz uso da operação <code>montarDados</code> que realiza o trabalho para um objeto por vez.
     * @return  List Contendo vários objetos da classe <code>HorarioDisponibilidadeVO</code> resultantes da consulta.
     */
    public static List montarDadosConsulta(ResultSet tabelaResultado, int nivelMontarDados) throws Exception {
        List vetResultado = new ArrayList();
        while (tabelaResultado.next()) {
            HorarioDisponibilidadeVO obj = new HorarioDisponibilidadeVO();
            obj = montarDados(tabelaResultado, nivelMontarDados);
            vetResultado.add(obj);
        }
        return vetResultado;
    }

    private static HorarioDisponibilidadeVO montarDadosBasico(ResultSet dadosSQL) throws Exception {
        HorarioDisponibilidadeVO obj = new HorarioDisponibilidadeVO();
        obj.setNovoObj(false);
        obj.setCodigo(new Integer(dadosSQL.getInt("codigo")));
        obj.setIdentificador(dadosSQL.getString("identificador"));
        obj.setHorario(new Integer(dadosSQL.getInt("horario")));
        obj.setHora0000(new Boolean(dadosSQL.getBoolean("hora0000")));
        obj.setHora0030(new Boolean(dadosSQL.getBoolean("hora0030")));
        obj.setHora0100(new Boolean(dadosSQL.getBoolean("hora0100")));
        obj.setHora0130(new Boolean(dadosSQL.getBoolean("hora0130")));
        obj.setHora0200(new Boolean(dadosSQL.getBoolean("hora0200")));
        obj.setHora0230(new Boolean(dadosSQL.getBoolean("hora0230")));
        obj.setHora0300(new Boolean(dadosSQL.getBoolean("hora0300")));
        obj.setHora0330(new Boolean(dadosSQL.getBoolean("hora0330")));
        obj.setHora0400(new Boolean(dadosSQL.getBoolean("hora0400")));
        obj.setHora0430(new Boolean(dadosSQL.getBoolean("hora0430")));
        obj.setHora0500(new Boolean(dadosSQL.getBoolean("hora0500")));
        obj.setHora0530(new Boolean(dadosSQL.getBoolean("hora0530")));
        obj.setHora0600(new Boolean(dadosSQL.getBoolean("hora0600")));
        obj.setHora0630(new Boolean(dadosSQL.getBoolean("hora0630")));
        obj.setHora0700(new Boolean(dadosSQL.getBoolean("hora0700")));
        obj.setHora0730(new Boolean(dadosSQL.getBoolean("hora0730")));
        obj.setHora0800(new Boolean(dadosSQL.getBoolean("hora0800")));
        obj.setHora0830(new Boolean(dadosSQL.getBoolean("hora0830")));
        obj.setHora0900(new Boolean(dadosSQL.getBoolean("hora0900")));
        obj.setHora0930(new Boolean(dadosSQL.getBoolean("hora0930")));
        obj.setHora1000(new Boolean(dadosSQL.getBoolean("hora1000")));
        obj.setHora1030(new Boolean(dadosSQL.getBoolean("hora1030")));
        obj.setHora1100(new Boolean(dadosSQL.getBoolean("hora1100")));
        obj.setHora1130(new Boolean(dadosSQL.getBoolean("hora1130")));
        obj.setHora1200(new Boolean(dadosSQL.getBoolean("hora1200")));
        obj.setHora1230(new Boolean(dadosSQL.getBoolean("hora1230")));
        obj.setHora1300(new Boolean(dadosSQL.getBoolean("hora1300")));
        obj.setHora1330(new Boolean(dadosSQL.getBoolean("hora1330")));
        obj.setHora1400(new Boolean(dadosSQL.getBoolean("hora1400")));
        obj.setHora1430(new Boolean(dadosSQL.getBoolean("hora1430")));
        obj.setHora1500(new Boolean(dadosSQL.getBoolean("hora1500")));
        obj.setHora1530(new Boolean(dadosSQL.getBoolean("hora1530")));
        obj.setHora1600(new Boolean(dadosSQL.getBoolean("hora1600")));
        obj.setHora1630(new Boolean(dadosSQL.getBoolean("hora1630")));
        obj.setHora1700(new Boolean(dadosSQL.getBoolean("hora1700")));
        obj.setHora1730(new Boolean(dadosSQL.getBoolean("hora1730")));
        obj.setHora1800(new Boolean(dadosSQL.getBoolean("hora1800")));
        obj.setHora1830(new Boolean(dadosSQL.getBoolean("hora1830")));
        obj.setHora1900(new Boolean(dadosSQL.getBoolean("hora1900")));
        obj.setHora1930(new Boolean(dadosSQL.getBoolean("hora1930")));
        obj.setHora2000(new Boolean(dadosSQL.getBoolean("hora2000")));
        obj.setHora2030(new Boolean(dadosSQL.getBoolean("hora2030")));
        obj.setHora2100(new Boolean(dadosSQL.getBoolean("hora2100")));
        obj.setHora2130(new Boolean(dadosSQL.getBoolean("hora2130")));
        obj.setHora2200(new Boolean(dadosSQL.getBoolean("hora2200")));
        obj.setHora2230(new Boolean(dadosSQL.getBoolean("hora2230")));
        obj.setHora2300(new Boolean(dadosSQL.getBoolean("hora2300")));
        obj.setHora2330(new Boolean(dadosSQL.getBoolean("hora2330")));
        obj.setDesenharTodos(new Boolean(dadosSQL.getBoolean("desenharTodos")));
        obj.setMatutino(new Boolean(dadosSQL.getBoolean("matutino")));
        obj.setVespertino(new Boolean(dadosSQL.getBoolean("vespertino")));
        obj.setNoturno(new Boolean(dadosSQL.getBoolean("noturno")));

        return obj;
    }

    /**
     * Responsável por montar os dados resultantes de uma consulta ao banco de dados (<code>ResultSet</code>)
     * em um objeto da classe <code>HorarioDisponibilidadeVO</code>.
     * @return  O objeto da classe <code>HorarioDisponibilidadeVO</code> com os dados devidamente montados.
     */
    public static HorarioDisponibilidadeVO montarDados(ResultSet dadosSQL, int nivelMontarDados) throws Exception {

        HorarioDisponibilidadeVO obj = montarDadosBasico(dadosSQL);

        if (nivelMontarDados == Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA)
            return obj;

        if (nivelMontarDados == Uteis.NIVELMONTARDADOS_DADOSBASICOS) {
            return obj;
        }
        return obj;
    }

    /**
     * Operação responsável por excluir todos os objetos da <code>HorarioDisponibilidadeVO</code> no BD.
     * Faz uso da operação <code>excluir</code> disponível na classe <code>HorarioDisponibilidade</code>.
     * @param <code>horario</code> campo chave para exclusão dos objetos no BD.
     * @exception Exception  Erro de conexão com o BD ou restrição de acesso a esta operação.
     */
    public void excluirHorarioDisponibilidades(Integer horario) throws Exception {
        excluir(getIdEntidade());
        String sql = "DELETE FROM HorarioDisponibilidade WHERE (horario = ?)";
        PreparedStatement sqlExcluir = con.prepareStatement(sql);
        sqlExcluir.setInt(1, horario.intValue());
        sqlExcluir.execute();
    }

    /**
     * Operação responsável por alterar todos os objetos da <code>HorarioDisponibilidadeVO</code> contidos em um Hashtable no BD.
     * Faz uso da operação <code>excluirHorarioDisponibilidades</code> e <code>incluirHorarioDisponibilidades</code> disponíveis na classe <code>HorarioDisponibilidade</code>.
     * @param objetos  List com os objetos a serem alterados ou incluídos no BD.
     * @exception Exception  Erro de conexão com o BD ou restrição de acesso a esta operação.
     */
    public void alterarHorarioDisponibilidades(Integer horario, List objetos) throws Exception {
        excluirHorarioDisponibilidades(horario);
        incluirHorarioDisponibilidades(horario, objetos);
    }

    /**
     * Operação responsável por incluir objetos da <code>HorarioDisponibilidadeVO</code> no BD.
     * Garantindo o relacionamento com a entidade principal <code>plano.Horario</code> através do atributo de vínculo.
     * @param objetos List contendo os objetos a serem gravados no BD da classe.
     * @exception Exception  Erro de conexão com o BD ou restrição de acesso a esta operação.
     */
    public void incluirHorarioDisponibilidades(Integer horarioPrm, List objetos) throws Exception {
        incluir(getIdEntidade());
        Iterator e = objetos.iterator();
        while (e.hasNext()) {
            HorarioDisponibilidadeVO obj = (HorarioDisponibilidadeVO) e.next();
            obj.setHorario(horarioPrm);
            incluir(obj);
        }
    }

    /**
     * Operação responsável por consultar todos os <code>HorarioDisponibilidadeVO</code> relacionados a um objeto da classe <code>plano.Horario</code>.
     * @param horario  Atributo de <code>plano.Horario</code> a ser utilizado para localizar os objetos da classe <code>HorarioDisponibilidadeVO</code>.
     * @return List  Contendo todos os objetos da classe <code>HorarioDisponibilidadeVO</code> resultantes da consulta.
     * @exception Exception  Erro de conexão com o BD ou restrição de acesso a esta operação.
     */
    public List consultarHorarioDisponibilidades(Integer horario, int nivelMontarDados) throws Exception {
        consultar(getIdEntidade());
        List objetos = new ArrayList();
        String sql = "SELECT * FROM HorarioDisponibilidade WHERE horario = ?";
        PreparedStatement sqlConsulta = con.prepareStatement(sql);
        sqlConsulta.setInt(1, horario.intValue());
        ResultSet resultado = sqlConsulta.executeQuery();
        while (resultado.next()) {
            HorarioDisponibilidadeVO novoObj = new HorarioDisponibilidadeVO();
            novoObj = HorarioDisponibilidade.montarDados(resultado, nivelMontarDados);
            objetos.add(novoObj);
        }
        Ordenacao.ordenarLista(objetos, "ordenar");
        return objetos;
    }

    /**
     * Operação responsável por localizar um objeto da classe <code>HorarioDisponibilidadeVO</code>
     * através de sua chave primária. 
     * @exception Exception Caso haja problemas de conexão ou localização do objeto procurado.
     */
    public HorarioDisponibilidadeVO consultarPorChavePrimaria(Integer codigoPrm, int nivelMontarDados) throws Exception {
        consultar(getIdEntidade(), false);
        String sql = "SELECT * FROM HorarioDisponibilidade WHERE codigo = ?";
        PreparedStatement sqlConsultar = con.prepareStatement(sql);
        sqlConsultar.setInt(1, codigoPrm.intValue());
        ResultSet tabelaResultado = sqlConsultar.executeQuery();
        if (!tabelaResultado.next()) {
            throw new ConsistirException("Dados Não Encontrados ( HorarioDisponibilidade ).");
        }
        return (montarDados(tabelaResultado, nivelMontarDados));
    }
}

package negocio.facade.jdbc.plano;

import negocio.comuns.plano.PlanoRedeEmpresaVO;
import negocio.comuns.utilitarias.Uteis;
import negocio.facade.jdbc.arquitetura.SuperEntidade;

import java.sql.*;

public class PlanoRedeEmpresa extends SuperEntidade {

    public PlanoRedeEmpresa(Connection conexao) throws Exception {
        super(conexao);
    }

    public PlanoRedeEmpresa() throws Exception {
        super();
    }

    public void inserir(PlanoRedeEmpresaVO planoRedeEmpresaVO) throws SQLException {
        String sql = "INSERT INTO PlanoRedeEmpresa " +
                "(plano,chave,datacadastro,planoReplicado, nomeUnidade, mensagemSituacao, codigoEmpresaDestino)" +
                "VALUES" +
                "(?,?,?,?,?,?,?)";

        try (PreparedStatement preparedStatement = con.prepareStatement(sql)) {
            int i = 1;
            preparedStatement.setInt(i++, planoRedeEmpresaVO.getPlano());
            preparedStatement.setString(i++, planoRedeEmpresaVO.getChave());
            preparedStatement.setTimestamp(i++, Uteis.getTimestamp());
            if (planoRedeEmpresaVO.getPlanoReplicado() != null) {
                preparedStatement.setInt(i++, planoRedeEmpresaVO.getPlanoReplicado());
            } else {
                preparedStatement.setNull(i++, 0);
            }
            preparedStatement.setString(i++, planoRedeEmpresaVO.getNomeUnidade());
            preparedStatement.setString(i++, planoRedeEmpresaVO.getMensagemSituacao());
            preparedStatement.setInt(i++, planoRedeEmpresaVO.getCodigoEmpresaDestino());
            preparedStatement.execute();
        }
    }

    public void alterarMensagemSituacao(Integer plano, String chave, String mensagemSituacao, Integer codigoEmpresaDestino) throws SQLException {
        String sql = "UPDATE PlanoRedeEmpresa set " +
                "mensagemSituacao = ? " +
                "WHERE plano = ? AND chave = ? AND codigoEmpresaDestino = ?";

        try (PreparedStatement preparedStatement = con.prepareStatement(sql)) {
            int i = 1;
            preparedStatement.setString(i++, mensagemSituacao);
            preparedStatement.setInt(i++, plano);
            preparedStatement.setString(i++, chave);
            preparedStatement.setInt(i++, codigoEmpresaDestino);
            preparedStatement.execute();
        }
    }

    public void limparDataAtualizacao(Integer plano, String chaveDestino, Integer codigoEmpresaDestino) throws SQLException {
        String sql = "UPDATE PlanoRedeEmpresa set " +
                "dataatualizacao = ? " +
                "WHERE plano = ? AND chave = ? AND codigoEmpresaDestino = ?";

        try (PreparedStatement preparedStatement = con.prepareStatement(sql)) {
            int i = 1;
            preparedStatement.setTimestamp(i++, null);
            preparedStatement.setInt(i++, plano);
            preparedStatement.setString(i++, chaveDestino);
            preparedStatement.setInt(i++, codigoEmpresaDestino);
            preparedStatement.execute();
        }
    }

    public void alterarDataAtualizacao(Integer plano, String chave, Integer codigoEmpresaDestino) throws SQLException {
        String sql = "UPDATE PlanoRedeEmpresa set " +
                "dataatualizacao = ? " +
                "WHERE plano = ? AND chave = ? AND codigoEmpresaDestino = ?";

        try (PreparedStatement preparedStatement = con.prepareStatement(sql)) {
            int i = 1;
            preparedStatement.setTimestamp(i++, Uteis.getTimestamp());
            preparedStatement.setInt(i++, plano);
            preparedStatement.setString(i++, chave);
            preparedStatement.setInt(i++, codigoEmpresaDestino);
            preparedStatement.execute();
        }
    }

    public void alterarDataAtualizacao(Integer plano, String chave, Integer novoPlano, String mensagemSituacao, Integer codigoEmpresaDestino) throws SQLException {
        String sql = "UPDATE PlanoRedeEmpresa set " +
                "dataatualizacao = ?, planoReplicado = ?, mensagemSituacao = ? " +
                "WHERE plano = ? AND chave = ? AND codigoEmpresaDestino = ?";

        try (PreparedStatement preparedStatement = con.prepareStatement(sql)) {
            int i = 1;
            preparedStatement.setTimestamp(i++, Uteis.getTimestamp());
            preparedStatement.setInt(i++, novoPlano);
            preparedStatement.setString(i++, mensagemSituacao);
            preparedStatement.setInt(i++, plano);
            preparedStatement.setString(i++, chave);
            preparedStatement.setInt(i++, codigoEmpresaDestino);
            preparedStatement.execute();
        }
    }

    public PlanoRedeEmpresaVO consultarPorChavePlano(String chave, Integer plano, Integer codigoEmpresaDestino) throws SQLException {
        String sql = "SELECT * FROM PlanoRedeEmpresa " +
                "WHERE chave = ? AND plano = ? and codigoEmpresaDestino = ?";

        try (PreparedStatement preparedStatement = con.prepareStatement(sql)) {
            int i = 1;
            preparedStatement.setString(i++, chave);
            preparedStatement.setInt(i++, plano);
            preparedStatement.setInt(i++, codigoEmpresaDestino);
            try (ResultSet resultSet = preparedStatement.executeQuery()) {
                if (resultSet.next()) {
                    return montarDados(resultSet);
                }
            }
        }
        return null;
    }

    public PlanoRedeEmpresaVO montarDados(ResultSet resultSet) throws SQLException {
        PlanoRedeEmpresaVO planoRedeEmpresaVO = new PlanoRedeEmpresaVO();
        planoRedeEmpresaVO.setChave(resultSet.getString("chave"));
        planoRedeEmpresaVO.setCodigo(resultSet.getInt("codigo"));
        planoRedeEmpresaVO.setDatacadastro(resultSet.getDate("datacadastro"));
        planoRedeEmpresaVO.setPlano(resultSet.getInt("plano"));
        planoRedeEmpresaVO.setDataatualizacao(resultSet.getDate("dataatualizacao"));
        planoRedeEmpresaVO.setPlanoReplicado(resultSet.getInt("planoreplicado"));
        planoRedeEmpresaVO.setNomeUnidade(resultSet.getString("nomeUnidade"));
        planoRedeEmpresaVO.setMensagemSituacao(resultSet.getString("mensagemsituacao"));
        planoRedeEmpresaVO.setCodigoEmpresaDestino(resultSet.getInt("codigoEmpresaDestino"));

        return planoRedeEmpresaVO;
    }
}

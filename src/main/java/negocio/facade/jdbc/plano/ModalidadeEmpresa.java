package negocio.facade.jdbc.plano;

import negocio.comuns.basico.EmpresaVO;
import negocio.comuns.plano.ModalidadeEmpresaVO;
import negocio.comuns.utilitarias.ConsistirException;
import negocio.comuns.utilitarias.Uteis;
import negocio.facade.jdbc.arquitetura.SuperEntidade;
import negocio.facade.jdbc.basico.Empresa;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.Statement;
import java.util.ArrayList;
import java.util.Iterator;
import java.util.List;

/**
 * Classe de persistência que encapsula todas as operações de manipulação dos dados da classe <code>ModalidadeEmpresaVO</code>.
 * Responsável por implementar operações como incluir, alterar, excluir e consultar pertinentes a classe <code>ModalidadeEmpresaVO</code>.
 * Encapsula toda a interação com o banco de dados.
 * @see ModalidadeEmpresaVO
 * @see SuperEntidade
 * @see Modalidade
 */
public class ModalidadeEmpresa extends SuperEntidade {    

    public ModalidadeEmpresa() throws Exception {
        super();
        setIdEntidade("Modalidade");
    }

    public ModalidadeEmpresa(Connection con) throws Exception {
    	super(con);
        setIdEntidade("Modalidade");
	}

	/**
     * Operação responsável por retornar um novo objeto da classe <code>ModalidadeEmpresaVO</code>.
     */
    public ModalidadeEmpresaVO novo() throws Exception {
        incluir(getIdEntidade());
        return new ModalidadeEmpresaVO();
    }

    /**
     * Operação responsável por incluir no banco de dados um objeto da classe <code>ModalidadeEmpresaVO</code>.
     * Primeiramente valida os dados (<code>validarDados</code>) do objeto. Verifica a conexão com o banco de dados e a permissão do usuário
     * para realizar esta operacão na entidade.
     * Isto, através da operação <code>incluir</code> da superclasse.
     * @param obj  Objeto da classe <code>ModalidadeEmpresaVO</code> que será gravado no banco de dados.
     * @exception Exception Caso haja problemas de conexão, restrição de acesso ou validação de dados.
     */
    public void incluir(ModalidadeEmpresaVO obj) throws Exception {
        ModalidadeEmpresaVO.validarDados(obj);
        incluir(getIdEntidade());
        obj.realizarUpperCaseDados();
        String sql = "INSERT INTO ModalidadeEmpresa( modalidade, empresa ) VALUES ( ?, ? )";
        try (PreparedStatement sqlInserir = con.prepareStatement(sql)) {
            if (obj.getModalidade() != 0) {
                sqlInserir.setInt(1, obj.getModalidade());
            } else {
                sqlInserir.setNull(1, 0);
            }
            if (obj.getEmpresa().getCodigo() != 0) {
                sqlInserir.setInt(2, obj.getEmpresa().getCodigo());
            } else {
                sqlInserir.setNull(2, 0);
            }
            sqlInserir.execute();
        }
        obj.setCodigo(obterValorChavePrimariaCodigo());
        obj.setNovoObj(false);
    }

    /**
     * Operação responsável por alterar no BD os dados de um objeto da classe <code>ModalidadeEmpresaVO</code>.
     * Sempre utiliza a chave primária da classe como atributo para localização do registro a ser alterado.
     * Primeiramente valida os dados (<code>validarDados</code>) do objeto. Verifica a conexão com o banco de dados e a permissão do usuário
     * para realizar esta operacão na entidade.
     * Isto, através da operação <code>alterar</code> da superclasse.
     * @param obj    Objeto da classe <code>ModalidadeEmpresaVO</code> que será alterada no banco de dados.
     * @exception Exception Caso haja problemas de conexão, restrição de acesso ou validação de dados.
     */
    public void alterar(ModalidadeEmpresaVO obj) throws Exception {
        ModalidadeEmpresaVO.validarDados(obj);
        alterar(getIdEntidade());
        obj.realizarUpperCaseDados();
        String sql = "UPDATE ModalidadeEmpresa set modalidade=?, empresa=? WHERE ((codigo = ?))";
        try (PreparedStatement sqlAlterar = con.prepareStatement(sql)) {
            if (obj.getModalidade() != 0) {
                sqlAlterar.setInt(1, obj.getModalidade());
            } else {
                sqlAlterar.setNull(1, 0);
            }
            if (obj.getEmpresa().getCodigo() != 0) {
                sqlAlterar.setInt(2, obj.getEmpresa().getCodigo());
            } else {
                sqlAlterar.setNull(2, 0);
            }
            sqlAlterar.setInt(3, obj.getCodigo());
            sqlAlterar.execute();
        }
    }

    /**
     * Operação responsável por excluir no BD um objeto da classe <code>ModalidadeEmpresaVO</code>.
     * Sempre localiza o registro a ser excluído através da chave primária da entidade.
     * Primeiramente verifica a conexão com o banco de dados e a permissão do usuário
     * para realizar esta operacão na entidade.
     * Isto, através da operação <code>excluir</code> da superclasse.
     * @param obj    Objeto da classe <code>ModalidadeEmpresaVO</code> que será removido no banco de dados.
     * @exception Exception Caso haja problemas de conexão ou restrição de acesso.
     */
    public void excluir(ModalidadeEmpresaVO obj) throws Exception {
        excluir(getIdEntidade());
        String sql = "DELETE FROM ModalidadeEmpresa WHERE ((codigo = ?))";
        try (PreparedStatement sqlExcluir = con.prepareStatement(sql)) {
            sqlExcluir.setInt(1, obj.getCodigo());
            sqlExcluir.execute();
        }
    }

    /**
     * Responsável por realizar uma consulta de <code>ModalidadeEmpresa</code> através do valor do atributo 
     * <code>nome</code> da classe <code>Empresa</code>
     * Faz uso da operação <code>montarDadosConsulta</code> que realiza o trabalho de prerarar o List resultante.
     * @return  List Contendo vários objetos da classe <code>ModalidadeEmpresaVO</code> resultantes da consulta.
     * @exception Exception Caso haja problemas de conexão ou restrição de acesso.
     */
    public List consultarPorNomeEmpresa(String valorConsulta, int nivelMontarDados) throws Exception {
        consultar(getIdEntidade(), true);
        String sqlStr = "SELECT ModalidadeEmpresa.* FROM ModalidadeEmpresa, Empresa WHERE ModalidadeEmpresa.empresa = Empresa.codigo and upper( Empresa.nome ) like('" + valorConsulta.toUpperCase() + "%') ORDER BY Empresa.nome";
        try (Statement stm = con.createStatement()) {
            try (ResultSet tabelaResultado = stm.executeQuery(sqlStr)) {
                return montarDadosConsulta(tabelaResultado, nivelMontarDados, this.con);
            }
        }
    }

    public List<ModalidadeEmpresaVO> consultarTodos(int nivelMontarDados)throws Exception{
        try (Statement st = con.createStatement()) {
            try (ResultSet rs = st.executeQuery("select * from ModalidadeEmpresa")) {
                return montarDadosConsulta(rs, nivelMontarDados, this.con);
            }
        }
    }


    /**
     * Responsável por realizar uma consulta de <code>ModalidadeEmpresa</code> através do valor do atributo 
     * <code>nome</code> da classe <code>Modalidade</code>
     * Faz uso da operação <code>montarDadosConsulta</code> que realiza o trabalho de prerarar o List resultante.
     * @return  List Contendo vários objetos da classe <code>ModalidadeEmpresaVO</code> resultantes da consulta.
     * @exception Exception Caso haja problemas de conexão ou restrição de acesso.
     */
    public List consultarPorNomeModalidade(String valorConsulta, int nivelMontarDados) throws Exception {
        consultar(getIdEntidade(), true);
        String sqlStr = "SELECT ModalidadeEmpresa.* FROM ModalidadeEmpresa, Modalidade WHERE ModalidadeEmpresa.modalidade = Modalidade.codigo and upper( Modalidade.nome ) like('" + valorConsulta.toUpperCase() + "%') ORDER BY Modalidade.nome";
        try (Statement stm = con.createStatement()) {
            try (ResultSet tabelaResultado = stm.executeQuery(sqlStr)) {
                return montarDadosConsulta(tabelaResultado, nivelMontarDados, this.con);
            }
        }
    }

    /**
     * Responsável por realizar uma consulta de <code>ModalidadeEmpresa</code> através do valor do atributo 
     * <code>Integer codigo</code>. Retorna os objetos com valores iguais ou superiores ao parâmetro fornecido.
     * Faz uso da operação <code>montarDadosConsulta</code> que realiza o trabalho de prerarar o List resultante.
     * @param   controlarAcesso Indica se a aplicação deverá verificar se o usuário possui permissão para esta consulta ou não.
     * @return  List Contendo vários objetos da classe <code>ModalidadeEmpresaVO</code> resultantes da consulta.
     * @exception Exception Caso haja problemas de conexão ou restrição de acesso.
     */
    public List consultarPorCodigo(Integer valorConsulta, boolean controlarAcesso, int nivelMontarDados) throws Exception {
        consultar(getIdEntidade(), controlarAcesso);
        String sqlStr = "SELECT * FROM ModalidadeEmpresa WHERE codigo >= " + valorConsulta + " ORDER BY codigo";
        try (Statement stm = con.createStatement()) {
            try (ResultSet tabelaResultado = stm.executeQuery(sqlStr)) {
                return (montarDadosConsulta(tabelaResultado, nivelMontarDados, this.con));
            }
        }
    }

    /**
     * Responsável por montar os dados de vários objetos, resultantes de uma consulta ao banco de dados (<code>ResultSet</code>).
     * Faz uso da operação <code>montarDados</code> que realiza o trabalho para um objeto por vez.
     * @return  List Contendo vários objetos da classe <code>ModalidadeEmpresaVO</code> resultantes da consulta.
     */
    public static List montarDadosConsulta(ResultSet tabelaResultado, int nivelMontarDados, Connection con) throws Exception {
        List vetResultado = new ArrayList();
        while (tabelaResultado.next()) {
            ModalidadeEmpresaVO obj = montarDados(tabelaResultado, nivelMontarDados, con);
            vetResultado.add(obj);
        }
        return vetResultado;
    }

    /**
     * Responsável por montar os dados resultantes de uma consulta ao banco de dados (<code>ResultSet</code>)
     * em um objeto da classe <code>ModalidadeEmpresaVO</code>.
     * @return  O objeto da classe <code>ModalidadeEmpresaVO</code> com os dados devidamente montados.
     */
    public static ModalidadeEmpresaVO montarDados(ResultSet dadosSQL, int nivelMontarDados, Connection con) throws Exception {
        ModalidadeEmpresaVO obj = new ModalidadeEmpresaVO();
        obj.setCodigo(new Integer(dadosSQL.getInt("codigo")));
        obj.setModalidade(dadosSQL.getInt("modalidade"));
        obj.getEmpresa().setCodigo(new Integer(dadosSQL.getInt("empresa")));
        obj.setNovoObj(false);
        if (nivelMontarDados == Uteis.NIVELMONTARDADOS_DADOSBASICOS) {
            return obj;
        }
        montarDadosEmpresa(obj, Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA, con);
        return obj;
    }

    /**
     * Operação responsável por montar os dados de um objeto da classe <code>EmpresaVO</code> relacionado ao objeto <code>ModalidadeEmpresaVO</code>.
     * Faz uso da chave primária da classe <code>EmpresaVO</code> para realizar a consulta.
     * @param obj  Objeto no qual será montado os dados consultados.
     */
    public static void montarDadosEmpresa(ModalidadeEmpresaVO obj, int nivelMontarDados, Connection con) throws Exception {
        if (obj.getEmpresa().getCodigo().intValue() == 0) {
            obj.setEmpresa(new EmpresaVO());
            return;
        }
        Empresa empresa = new Empresa(con);
        obj.setEmpresa(empresa.consultarPorChavePrimaria(obj.getEmpresa().getCodigo(), nivelMontarDados));
        empresa = null;
    }

    /**
     * Operação responsável por excluir todos os objetos da <code>ModalidadeEmpresaVO</code> no BD.
     * Faz uso da operação <code>excluir</code> disponível na classe <code>ModalidadeEmpresa</code>.
     * @param modalidade campo chave para exclusão dos objetos no BD.
     * @exception Exception  Erro de conexão com o BD ou restrição de acesso a esta operação.
     */
    public void excluirModalidadeEmpresas(Integer modalidade) throws Exception {
        excluir(getIdEntidade());
        String sql = "DELETE FROM ModalidadeEmpresa WHERE (modalidade = ?)";
        try (PreparedStatement sqlExcluir = con.prepareStatement(sql)) {
            sqlExcluir.setInt(1, modalidade);
            sqlExcluir.execute();
        }
    }

    /**
     * Operação responsável por alterar todos os objetos da <code>ModalidadeEmpresaVO</code> contidos em um Hashtable no BD.
     * Faz uso da operação <code>excluirModalidadeEmpresas</code> e <code>incluirModalidadeEmpresas</code> disponíveis na classe <code>ModalidadeEmpresa</code>.
     * @param objetos  List com os objetos a serem alterados ou incluídos no BD.
     * @exception Exception  Erro de conexão com o BD ou restrição de acesso a esta operação.
     */
    public void alterarModalidadeEmpresas(Integer modalidade, List objetos) throws Exception {
        String str = "DELETE FROM modalidadeEmpresa WHERE modalidade = " + modalidade;
        try (PreparedStatement sqlExcluir = con.prepareStatement(str)) {
            sqlExcluir.execute();
        }
        Iterator e = objetos.iterator();
        while (e.hasNext()) {
            ModalidadeEmpresaVO obj = (ModalidadeEmpresaVO) e.next();
            obj.setModalidade(modalidade);
            incluir(obj);
        }
    }

    /**
     * Operação responsável por incluir objetos da <code>ModalidadeEmpresaVO</code> no BD.
     * Garantindo o relacionamento com a entidade principal <code>plano.Modalidade</code> através do atributo de vínculo.
     * @param objetos List contendo os objetos a serem gravados no BD da classe.
     * @exception Exception  Erro de conexão com o BD ou restrição de acesso a esta operação.
     */
    public void incluirModalidadeEmpresas(Integer modalidadePrm, List objetos) throws Exception {
        Iterator e = objetos.iterator();
        while (e.hasNext()) {
            ModalidadeEmpresaVO obj = (ModalidadeEmpresaVO) e.next();
            obj.setModalidade(modalidadePrm);
            incluir(obj);
        }
    }

    /**
     * Operação responsável por consultar todos os <code>ModalidadeEmpresaVO</code> relacionados a um objeto da classe <code>plano.Modalidade</code>.
     * @param modalidade  Atributo de <code>plano.Modalidade</code> a ser utilizado para localizar os objetos da classe <code>ModalidadeEmpresaVO</code>.
     * @return List  Contendo todos os objetos da classe <code>ModalidadeEmpresaVO</code> resultantes da consulta.
     * @exception Exception  Erro de conexão com o BD ou restrição de acesso a esta operação.
     */
    public List consultarModalidadeEmpresas(Integer modalidade, int nivelMontarDados) throws Exception {
        consultar(getIdEntidade());
        List objetos = new ArrayList();
        String sql = "SELECT  ModalidadeEmpresa.*, Empresa.nome, Empresa.codigo FROM ModalidadeEmpresa , Empresa WHERE modalidade = ? and ModalidadeEmpresa.empresa =  empresa.codigo ORDER BY Empresa.nome";
        try (PreparedStatement sqlConsulta = con.prepareStatement(sql)) {
            sqlConsulta.setInt(1, modalidade);
            try (ResultSet resultado = sqlConsulta.executeQuery()) {
                while (resultado.next()) {
                    ModalidadeEmpresaVO novoObj = ModalidadeEmpresa.montarDados(resultado, nivelMontarDados, this.con);
                    objetos.add(novoObj);
                }
            }
        }
        return objetos;
    }

    /**
     * Operação responsável por localizar um objeto da classe <code>ModalidadeEmpresaVO</code>
     * através de sua chave primária. 
     * @exception Exception Caso haja problemas de conexão ou localização do objeto procurado.
     */
    public ModalidadeEmpresaVO consultarPorChavePrimaria(Integer codigoPrm, int nivelMontarDados) throws Exception {
        consultar(getIdEntidade(), false);
        String sql = "SELECT * FROM ModalidadeEmpresa WHERE codigo = ?";
        try (PreparedStatement sqlConsultar = con.prepareStatement(sql)) {
            sqlConsultar.setInt(1, codigoPrm);
            try (ResultSet tabelaResultado = sqlConsultar.executeQuery()) {
                if (!tabelaResultado.next()) {
                    throw new ConsistirException("Dados Não Encontrados ( ModalidadeEmpresa ).");
                }
                return (montarDados(tabelaResultado, nivelMontarDados, this.con));
            }
        }
    }

    public List<Integer> listaEmpresasModalidade(Integer modalidade) throws Exception {
        consultar(getIdEntidade());
        List<Integer> objetos = new ArrayList();
        String sql = "SELECT  m.empresa FROM ModalidadeEmpresa m WHERE modalidade = ? order by codigo";
        try (PreparedStatement sqlConsulta = con.prepareStatement(sql)) {
            sqlConsulta.setInt(1, modalidade);
            try (ResultSet resultado = sqlConsulta.executeQuery()) {
                while (resultado.next()) {
                    Integer empresa = resultado.getInt("empresa");
                    objetos.add(empresa);
                }
            }
        }
        return objetos;
    }

}

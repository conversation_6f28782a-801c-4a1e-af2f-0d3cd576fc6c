package negocio.facade.jdbc.plano;

import negocio.comuns.plano.PlanoRecorrenciaParcelaVO;
import negocio.comuns.plano.PlanoRecorrenciaVO;
import negocio.facade.jdbc.arquitetura.SuperEntidade;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.List;

public class PlanoRecorrenciaParcela extends SuperEntidade {

    public PlanoRecorrenciaParcela() throws Exception {
        super();
        setIdEntidade("planoRecorrenciaParcela");
    }

    public PlanoRecorrenciaParcela(Connection conexao) throws Exception {
        super(conexao);
        setIdEntidade("planoRecorrenciaParcela");
    }

    public void incluir(PlanoRecorrenciaVO planoRecorrencia) throws SQLException {
        String values = "";
        for (PlanoRecorrenciaParcelaVO parcela: planoRecorrencia.getParcelas()) {
            values += "("+planoRecorrencia.getCodigo()+" ,"+parcela.getNumero()+", "+parcela.getValor()+"), ";
        }
        if(values.length() > 0){
            values = values.substring(0, values.length() - 2);
            String sqlInsert = "INSERT INTO planorecorrenciaparcela (planorecorrencia, numero, valor) VALUES "+values;
            PreparedStatement ps = con.prepareStatement(sqlInsert);
            ps.execute();
        }
    }

    public void alterar(PlanoRecorrenciaVO planoRecorrencia) throws SQLException {
        excluirPorPlanoRecorrencia(planoRecorrencia.getCodigo());
        incluir(planoRecorrencia);
    }

    public void excluirPorPlanoRecorrencia(int codigoPlanoRecorrencia) throws SQLException {
        String sql = "DELETE FROM planorecorrenciaparcela " +
                "WHERE planorecorrencia = "+codigoPlanoRecorrencia;
        PreparedStatement ps = con.prepareStatement(sql);
        ps.execute();
    }

    public void montarDados(PlanoRecorrenciaVO planoRecorrencia) throws SQLException {
        String sql = "SELECT * FROM planorecorrenciaparcela " +
                "WHERE planorecorrencia = "+planoRecorrencia.getCodigo();

        PreparedStatement ps = con.prepareStatement(sql);
        ResultSet rs = ps.executeQuery();
        while (rs.next()){
            planoRecorrencia
                    .getParcelas()
                    .add(new PlanoRecorrenciaParcelaVO(
                            rs.getInt("numero"),
                            rs.getDouble("valor")
                    ));
        }
    }

    public List<PlanoRecorrenciaParcelaVO> montarDados(ResultSet rs) throws SQLException {
        List<PlanoRecorrenciaParcelaVO> parcelas = new ArrayList<PlanoRecorrenciaParcelaVO>();

        while (rs.next()){
            parcelas.add(new PlanoRecorrenciaParcelaVO(
                            rs.getInt("numero"),
                            rs.getDouble("valor")
                    ));
        }

        return parcelas;
    }

    public Double consultarValorPorNumeroParcela(int numeroParcela, Integer codigoPlanoRecorrencia) throws SQLException {
        String sql = "SELECT * FROM planorecorrenciaparcela " +
                "WHERE planorecorrencia = "+codigoPlanoRecorrencia +
                "   AND numero = "+numeroParcela;
        PreparedStatement ps = con.prepareStatement(sql);
        ResultSet rs = ps.executeQuery();
        if(rs.next()){
            return rs.getDouble("valor");
        }else{
            return null;
        }
    }

    public List<PlanoRecorrenciaParcelaVO> consultar(int codigoPlanoRecorrencia) throws SQLException {
        String sql = "SELECT * from planorecorrenciaparcela "+
                "WHERE planorecorrencia = "+codigoPlanoRecorrencia;

        PreparedStatement ps = con.prepareStatement(sql);
        ResultSet rs = ps.executeQuery();

        return montarDados(rs);
    }
}

package negocio.facade.jdbc.plano;

import negocio.comuns.plano.CategoriaProdutoVO;
import negocio.comuns.financeiro.FormaPagamentoVO;
import negocio.comuns.utilitarias.ConsistirException;
import negocio.comuns.plano.ProdutoVO;
import negocio.comuns.utilitarias.Ordenacao;
import negocio.comuns.utilitarias.Uteis;
import negocio.facade.jdbc.arquitetura.SuperEntidade;
import negocio.facade.jdbc.basico.ComissaoProdutoConfiguracao;
import negocio.interfaces.plano.CategoriaProdutoInterfaceFacade;
import negocio.facade.jdbc.financeiro.FormaPagamento;
import org.json.JSONArray;
import org.eclipse.jdt.core.IField;
import org.json.JSONObject;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.sql.Statement;
import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * Classe de persistência que encapsula todas as operações de manipulação dos dados da classe <code>CategoriaProdutoVO</code>.
 * Responsável por implementar operações como incluir, alterar, excluir e consultar pertinentes a classe <code>CategoriaProdutoVO</code>.
 * Encapsula toda a interação com o banco de dados.
 *
 * @see CategoriaProdutoVO
 * @see SuperEntidade
 */
public class CategoriaProduto extends SuperEntidade implements CategoriaProdutoInterfaceFacade {

    public CategoriaProduto() throws Exception {
        super();
    }

    public CategoriaProduto(Connection con) throws Exception {
        super(con);
    }

    /**
     * Responsável por montar os dados de vários objetos, resultantes de uma consulta ao banco de dados (<code>ResultSet</code>).
     * Faz uso da operação <code>montarDados</code> que realiza o trabalho para um objeto por vez.
     *
     * @return List Contendo vários objetos da classe <code>CategoriaProdutoVO</code> resultantes da consulta.
     */
    public static List<CategoriaProdutoVO> montarDadosConsulta(ResultSet tabelaResultado, int nivelMontarDados, Connection con) throws Exception {
        List<CategoriaProdutoVO> vetResultado = new ArrayList<>();
        while (tabelaResultado.next()) {
            CategoriaProdutoVO obj = montarDados(tabelaResultado, nivelMontarDados, con);
            vetResultado.add(obj);
        }
        return vetResultado;
    }

    /**
     * Responsável por montar os dados resultantes de uma consulta ao banco de dados (<code>ResultSet</code>)
     * em um objeto da classe <code>CategoriaProdutoVO</code>.
     *
     * @return O objeto da classe <code>CategoriaProdutoVO</code> com os dados devidamente montados.
     */
    public static CategoriaProdutoVO montarDados(ResultSet dadosSQL, int nivelMontarDados, Connection con) throws Exception {
        CategoriaProdutoVO obj = new CategoriaProdutoVO();
        obj.setCodigo(dadosSQL.getInt("codigo"));
        obj.setDescricao(dadosSQL.getString("descricao"));
        obj.setBloquearAcessoSeProdutoAberto(dadosSQL.getBoolean("bloquearAcessoSeProdutoAberto"));
        obj.setAvaliacaoFisica(dadosSQL.getBoolean("avaliacaofisica"));
        obj.setNovoObj(false);
        obj.getFormaPagamento().setCodigo(dadosSQL.getInt("formapagamento"));
        montarFormaPagamento(obj,nivelMontarDados,con);

        if (nivelMontarDados == Uteis.NIVELMONTARDADOS_DADOSBASICOS) {
            return obj;
        }
        montarDadosComissao(obj, con);
        return obj;
    }

    private static void montarDadosComissao(CategoriaProdutoVO obj, Connection con) throws Exception {
        ComissaoProdutoConfiguracao comissaoProdutoConfiguracao = new ComissaoProdutoConfiguracao(con);
        obj.setComissaoCategoriaProdutos(comissaoProdutoConfiguracao.consultarPorCategoriaProduto(obj.getCodigo()));
    }

    /**
     * Operação responsável por retornar um novo objeto da classe <code>CategoriaProdutoVO</code>.
     */
    public CategoriaProdutoVO novo() throws Exception {
        incluir(getIdEntidade());
        return new CategoriaProdutoVO();
    }

    /**
     * Operação responsável por incluir no banco de dados um objeto da classe <code>CategoriaProdutoVO</code>.
     * Primeiramente valida os dados (<code>validarDados</code>) do objeto. Verifica a conexão com o banco de dados e a permissão do usuário
     * para realizar esta operacão na entidade.
     * Isto, através da operação <code>incluir</code> da superclasse.
     *
     * @param obj Objeto da classe <code>CategoriaProdutoVO</code> que será gravado no banco de dados.
     * @throws Exception Caso haja problemas de conexão, restrição de acesso ou validação de dados.
     */
    public void incluir(CategoriaProdutoVO obj, boolean centralEventos) throws Exception {
        CategoriaProdutoVO.validarDados(obj);
        if (!centralEventos) {
            incluir(getIdEntidade());
        }
        obj.realizarUpperCaseDados();
        String sql = "INSERT INTO CategoriaProduto(descricao, bloquearAcessoSeProdutoAberto, avaliacaofisica, formaPagamento) VALUES (?, ?, ?,?)";
        try (PreparedStatement sqlInserir = con.prepareStatement(sql)) {
            int i = 0;
            sqlInserir.setString(++i, obj.getDescricao());
            sqlInserir.setBoolean(++i, obj.isBloquearAcessoSeProdutoAberto());
            sqlInserir.setBoolean(++i, obj.getAvaliacaoFisica());
            if (obj.getFormaPagamento().getCodigo() != 0){
                sqlInserir.setInt(++i, obj.getFormaPagamento().getCodigo());
            }else {
                sqlInserir.setNull(++i, 0);
            }

            sqlInserir.execute();
        }
        obj.setCodigo(obterValorChavePrimariaCodigo());
        obj.setNovoObj(false);
    }

    /* (non-Javadoc)
     * @see negocio.interfaces.plano.CategoriaProdutoInterfaceFacade#incluir(negocio.comuns.plano.CategoriaProdutoVO)
     */
    public void incluir(CategoriaProdutoVO obj) throws Exception {
        incluir(obj, false);
    }
    /* (non-Javadoc)
     * @see negocio.interfaces.plano.CategoriaProdutoInterfaceFacade#excluir(negocio.comuns.plano.CategoriaProdutoVO)
     */

    /**
     * Operação responsável por alterar no BD os dados de um objeto da classe <code>CategoriaProdutoVO</code>.
     * Sempre utiliza a chave primária da classe como atributo para localização do registro a ser alterado.
     * Primeiramente valida os dados (<code>validarDados</code>) do objeto. Verifica a conexão com o banco de dados e a permissão do usuário
     * para realizar esta operacão na entidade.
     * Isto, através da operação <code>alterar</code> da superclasse.
     *
     * @param obj Objeto da classe <code>CategoriaProdutoVO</code> que será alterada no banco de dados.
     * @throws Exception Caso haja problemas de conexão, restrição de acesso ou validação de dados.
     */
    public void alterar(CategoriaProdutoVO obj, boolean centralEventos) throws Exception {
        try {
            con.setAutoCommit(false);
            alterarSemCommit(obj);
            con.commit();
        } catch (Exception e) {
            con.rollback();
            con.setAutoCommit(true);
            throw e;
        } finally {
            con.setAutoCommit(true);
        }
    }

    public void alterarSemCommit(CategoriaProdutoVO obj) throws Exception {
        CategoriaProdutoVO.validarDados(obj);
        alterar(getIdEntidade());
        obj.realizarUpperCaseDados();
        String sql = "UPDATE CategoriaProduto set descricao = ?, bloquearAcessoSeProdutoAberto = ?, avaliacaofisica = ?, formapagamento = ? WHERE codigo = ?";
        try (PreparedStatement sqlAlterar = con.prepareStatement(sql)) {
            int i = 0;
            sqlAlterar.setString(++i, obj.getDescricao());
            sqlAlterar.setBoolean(++i, obj.isBloquearAcessoSeProdutoAberto());
            sqlAlterar.setBoolean(++i, obj.getAvaliacaoFisica());
            if (obj.getFormaPagamento().getCodigo() != 0){
                sqlAlterar.setInt(++i, obj.getFormaPagamento().getCodigo());
            }else {
                sqlAlterar.setNull(++i, 0);
            }
            sqlAlterar.setInt(++i, obj.getCodigo());
            sqlAlterar.execute();
        }

        getFacade().getComissaoProdutoConfiguracao().atualizarPorCategoriaProduto(obj);
    }

    /**
     * Operação responsável por excluir no BD um objeto da classe <code>CategoriaProdutoVO</code>.
     * Sempre localiza o registro a ser excluído através da chave primária da entidade.
     * Primeiramente verifica a conexão com o banco de dados e a permissão do usuário
     * para realizar esta operacão na entidade.
     * Isto, através da operação <code>excluir</code> da superclasse.
     *
     * @param obj Objeto da classe <code>CategoriaProdutoVO</code> que será removido no banco de dados.
     * @throws Exception Caso haja problemas de conexão ou restrição de acesso.
     */
    public void excluir(CategoriaProdutoVO obj, boolean centralEventos) throws Exception {
        try {
            con.setAutoCommit(false);
            if (!centralEventos) {
                excluir(getIdEntidade());
            }
            String sql = "DELETE FROM CategoriaProduto WHERE codigo = ?";
            try (PreparedStatement sqlExcluir = con.prepareStatement(sql)) {
                sqlExcluir.setInt(1, obj.getCodigo());
                sqlExcluir.execute();
            }
            con.commit();
        } catch (Exception e) {
            con.rollback();
            con.setAutoCommit(true);
            throw e;
        } finally {
            con.setAutoCommit(true);
        }
    }

    public void excluir(CategoriaProdutoVO obj) throws Exception {
        this.excluir(obj, false);
    }

    /**
     * Responsável por realizar uma consulta de <code>CategoriaProduto</code> através do valor do atributo
     * <code>String descricao</code>. Retorna os objetos, com início do valor do atributo idêntico ao parâmetro fornecido.
     * Faz uso da operação <code>montarDadosConsulta</code> que realiza o trabalho de prerarar o List resultante.
     *
     * @param controlarAcesso Indica se a aplicação deverá verificar se o usuário possui permissão para esta consulta ou não.
     * @return List Contendo vários objetos da classe <code>CategoriaProdutoVO</code> resultantes da consulta.
     * @throws Exception Caso haja problemas de conexão ou restrição de acesso.
     */
    public List consultarPorDescricao(String valorConsulta, boolean controlarAcesso, int nivelMontarDados) throws Exception {
        consultar(getIdEntidade(), controlarAcesso);
        String sqlStr = "SELECT * FROM CategoriaProduto WHERE upper( descricao ) like('" + valorConsulta.toUpperCase() + "%') ORDER BY descricao";
        Statement stm = con.createStatement();
        try (ResultSet tabelaResultado = stm.executeQuery(sqlStr)) {
            return (montarDadosConsulta(tabelaResultado, nivelMontarDados, con));
        }
    }

    public CategoriaProdutoVO consultarPorCodigoProduto(Integer codProduto) throws Exception {
        String sqlStr = "SELECT cat.* FROM CATEGORIAPRODUTO cat \n" +
                "    inner join produto p on p.categoriaproduto = cat.codigo\n" +
                "\t WHERE p.codigo = " + codProduto;
        CategoriaProdutoVO categoriaProduto;
        try (Statement stm = con.createStatement()) {
            try (ResultSet rs = stm.executeQuery(sqlStr)) {
                categoriaProduto = new CategoriaProdutoVO();
                if (rs.next()) {
                    categoriaProduto.setCodigo(rs.getInt("codigo"));
                    categoriaProduto.setDescricao(rs.getString("descricao"));
                    categoriaProduto.setAvaliacaoFisica(rs.getBoolean("avaliacaofisica"));
                    categoriaProduto.getFormaPagamento().setCodigo(rs.getInt("formaPagamento"));
                }
            }
        }
        return categoriaProduto;
    }

    /**
     * Responsável por realizar uma consulta de <code>CategoriaProduto</code> através do valor do atributo
     * <code>String descricao</code>. Retorna os objetos, com início do valor do atributo idêntico ao parâmetro fornecido.
     * Faz uso da operação <code>montarDadosConsulta</code> que realiza o trabalho de prerarar o List resultante.
     *
     * @return List Contendo vários objetos da classe <code>CategoriaProdutoVO</code> resultantes da consulta.
     * @throws Exception Caso haja problemas de conexão ou restrição de acesso.
     */
    public int consultarPorDescricao(String descricao) throws Exception {
        String sqlStr = "SELECT codigo FROM CategoriaProduto WHERE upper( descricao ) like('" + descricao.toUpperCase() + "') ORDER BY descricao";
        try (Statement stm = con.createStatement()) {
            try (ResultSet tabelaResultado = stm.executeQuery(sqlStr)) {
                if (!tabelaResultado.next()) {
                    throw new Exception("Categoria de Produto de descrição " + descricao + " não encontrada.");
                }
                return tabelaResultado.getInt("codigo");
            }
        }
    }

    /**
     * Responsável por realizar uma consulta de <code>CategoriaProduto</code> através do valor do atributo
     * <code>Integer codigo</code>. Retorna os objetos com valores iguais ou superiores ao parâmetro fornecido.
     * Faz uso da operação <code>montarDadosConsulta</code> que realiza o trabalho de prerarar o List resultante.
     *
     * @param controlarAcesso Indica se a aplicação deverá verificar se o usuário possui permissão para esta consulta ou não.
     * @return List Contendo vários objetos da classe <code>CategoriaProdutoVO</code> resultantes da consulta.
     * @throws Exception Caso haja problemas de conexão ou restrição de acesso.
     */
    public List consultarPorCodigo(Integer valorConsulta, boolean controlarAcesso, int nivelMontarDados) throws Exception {
        consultar(getIdEntidade(), controlarAcesso);
        String sqlStr = "SELECT * FROM CategoriaProduto WHERE codigo >= " + valorConsulta.intValue() + " ORDER BY codigo";
        try (Statement stm = con.createStatement()) {
            try (ResultSet tabelaResultado = stm.executeQuery(sqlStr)) {
                return (montarDadosConsulta(tabelaResultado, nivelMontarDados, con));
            }
        }
    }

    /**
     * Operação responsável por localizar um objeto da classe <code>CategoriaProdutoVO</code>
     * através de sua chave primária.
     *
     * @throws Exception Caso haja problemas de conexão ou localização do objeto procurado.
     */
    public CategoriaProdutoVO consultarPorChavePrimaria(Integer codigoPrm, int nivelMontarDados) throws Exception {
        consultar(getIdEntidade(), false);
        String sql = "SELECT * FROM CategoriaProduto WHERE codigo = ?";
        try (PreparedStatement sqlConsultar = con.prepareStatement(sql)) {
            sqlConsultar.setInt(1, codigoPrm);
            try (ResultSet tabelaResultado = sqlConsultar.executeQuery()) {
                if (!tabelaResultado.next()) {
                    throw new ConsistirException("Dados Não Encontrados ( CategoriaProduto ).");
                }
                return (montarDados(tabelaResultado, nivelMontarDados, con));
            }
        }
    }

    public CategoriaProdutoVO criarCategoriaOuConsultarSeExistePorDescricao(
            String descricao) throws Exception {

        List lista = consultarPorDescricao(descricao, false, Uteis.NIVELMONTARDADOS_MINIMOS);
        if (lista.isEmpty()) {
            CategoriaProdutoVO categoria = new CategoriaProdutoVO();
            categoria.setDescricao(descricao);
            incluir(categoria);
            return categoria;
        } else {
            return (CategoriaProdutoVO) lista.get(0);
        }
    }

    @Override
    public void alterar(CategoriaProdutoVO obj) throws Exception {
        this.alterar(obj, false);

    }

    /**
     * Responsável por consultar todos os registros de categoria de produto de forma simplificada, montando num mapa.
     *
     * <AUTHOR>
     * 11/08/2011
     */
    public Map<Integer, String> consultarCategoriaSimplificado() throws Exception {
        Map<Integer, String> mapResult = new HashMap<Integer, String>();
        String sql = "SELECT descricao, codigo FROM categoriaproduto";
        try (ResultSet consulta = criarConsulta(sql, con)) {
            while (consulta.next()) {
                mapResult.put(consulta.getInt("codigo"), consulta.getString("descricao"));
            }
        }
        return mapResult;
    }

    public List consultarCategoriaComControleEstoque(Integer codigoEmpresa, String descricaoCategoria, boolean somenteProdutoEstoqueAtivo, int nivelMontarDados) throws Exception {
        StringBuilder sql = new StringBuilder();
        sql.append(" SELECT distinct cat.codigo, cat.descricao, cat.bloquearAcessoSeProdutoAberto  ");
        sql.append(" FROM CategoriaProduto cat    ");
        sql.append(" inner join produto prod on prod.categoriaProduto = cat.codigo ");
        sql.append(" inner join produtoEstoque pe on pe.produto = prod.codigo ");
        sql.append(" WHERE upper( cat.descricao ) like('").append(descricaoCategoria.toUpperCase()).append("%') ");
        if (somenteProdutoEstoqueAtivo) {
            sql.append(" and (pe.situacao = 'A') ");
            sql.append(" and prod.desativado = false");
        }
        sql.append(" and pe.empresa=").append(codigoEmpresa);
        sql.append(" ORDER BY cat.descricao");
        List lista;
        try (Statement stm = con.createStatement()) {
            try (ResultSet tabelaResultado = stm.executeQuery(sql.toString())) {
                lista = new ArrayList();
                while (tabelaResultado.next()) {
                    CategoriaProdutoVO obj = new CategoriaProdutoVO();
                    obj.setCodigo(tabelaResultado.getInt("codigo"));
                    obj.setDescricao(tabelaResultado.getString("descricao"));
                    obj.setBloquearAcessoSeProdutoAberto(tabelaResultado.getBoolean("bloquearAcessoSeProdutoAberto"));
                    lista.add(obj);
                }
            }
        }
        return lista;

    }

    public String consultarJSON() throws Exception {
        JSONArray aaData = new JSONArray();
        try (ResultSet rs = getPS().executeQuery()) {
            while (rs.next()) {
                JSONArray item = new JSONArray();
                item.put(rs.getString("codigo"));
                item.put(rs.getString("descricao"));
                aaData.put(item);
            }
        }
        JSONObject objReturn = new JSONObject();
        objReturn.put("aaData", aaData);
        return objReturn.toString();
    }

    public CategoriaProdutoVO consultarPorDescricao(String descricao, int nivelMontarDados) throws Exception {
        String sql = "select * from categoriaProduto where upper(descricao) = ?";
        try (PreparedStatement pst = con.prepareStatement(sql.toString())) {
            pst.setString(1, descricao.toUpperCase());
            try (ResultSet rs = pst.executeQuery()) {
                if (rs.next()) {
                    return montarDados(rs, nivelMontarDados, con);
                }
            }
        }
        return null;

    }

    private PreparedStatement getPS() throws SQLException {
        String sql = "SELECT codigo, descricao, bloquearAcessoSeProdutoAberto FROM categoriaproduto ORDER BY descricao";
        return con.prepareStatement(sql);
    }

    public List consultarParaImpressao(String filtro, String ordem, String campoOrdenacao, int i) throws SQLException {
        List lista;
        try (ResultSet rs = getPS().executeQuery()) {
            lista = new ArrayList();
            while (rs.next()) {
                CategoriaProdutoVO cp = new CategoriaProdutoVO();
                String geral = rs.getString("codigo") + rs.getString("descricao");
                if (geral.toLowerCase().contains(filtro.toLowerCase())) {
                    cp.setCodigo(rs.getInt("codigo"));
                    cp.setDescricao(rs.getString("descricao"));
                    lista.add(cp);
                }
            }
        }
        if (campoOrdenacao.equals("Código")) {
            Ordenacao.ordenarLista(lista, "codigo");
        } else if (campoOrdenacao.equals("Descrição")) {
            Ordenacao.ordenarLista(lista, "descricao");
        }
        if (ordem.contains("desc")) {
            Collections.reverse(lista);
        }
        return lista;

    }

    public CategoriaProdutoVO obterCategoriaPadraoServico() throws Exception {
        String sql = "SELECT * FROM categoriaproduto " +
                "WHERE UPPER(descricao) = 'SERVIÇO' " +
                "LIMIT 1";
        try (Statement st = con.createStatement()) {
            try (ResultSet rs = st.executeQuery(sql)) {
                if (rs.next()) {
                    return montarDados(rs, Uteis.NIVELMONTARDADOS_DADOSBASICOS, con);
                } else {
                    CategoriaProdutoVO categoria = new CategoriaProdutoVO();
                    categoria.setDescricao("SERVIÇO");
                    incluir(categoria);

                    return categoria;
                }
            }
        }
    }

    public List<CategoriaProdutoVO> consultarCategoriasParaVendasOnline() throws Exception {
        StringBuilder sql = new StringBuilder();
        sql.append("SELECT  \n");
        sql.append("distinct ca.* \n");
        sql.append("FROM produto p  \n");
        sql.append("INNER JOIN categoriaproduto ca ON ca.codigo = p.categoriaproduto \n");
        sql.append("WHERE p.apresentarVendasOnline  \n");
        sql.append("AND p.desativado = false  \n");
        sql.append("order by ca.descricao \n");
        try (Statement stm = con.createStatement()) {
            try (ResultSet rs = stm.executeQuery(sql.toString())) {
                return montarDadosConsulta(rs, Uteis.NIVELMONTARDADOS_DADOSBASICOS, con);
            }
        }
    }
    private static void montarFormaPagamento(CategoriaProdutoVO obj, int nivelMontarDados,Connection con) throws Exception {

        if (obj.getFormaPagamento().getCodigo().intValue() == 0) {
            obj.setFormaPagamento(new FormaPagamentoVO());
            return;
        }
        FormaPagamento formaPagamento = new FormaPagamento(con);
        obj.setFormaPagamento(formaPagamento.consultarPorChavePrimaria(obj.getFormaPagamento().getCodigo(), nivelMontarDados));
        formaPagamento = null;
    }

    public FormaPagamentoVO consultarFormaPagamentoCategoriaProduto(int movParcela) throws SQLException {

        FormaPagamentoVO formaPagamentoVO = new FormaPagamentoVO();
        StringBuilder sql = new StringBuilder("");
        sql.append("select c.formapagamento as formapagamento From categoriaproduto c \n");
        sql.append(" inner join produto p on p.categoriaproduto  = c.codigo \n");
        sql.append(" inner join movproduto mpo on mpo.produto = p.codigo \n");
        sql.append(" left join movprodutoparcela mpp on mpp.movproduto = mpo.codigo \n");
        sql.append(" where mpp.movparcela = '").append(movParcela).append("' \n");
        try (Statement stm = con.createStatement()) {
            try (ResultSet rs = stm.executeQuery(sql.toString())) {
                while (rs.next()){
                    formaPagamentoVO = getFacade().getFormaPagamento().consultarPorChavePrimaria(rs.getInt("formapagamento"),Uteis.NIVELMONTARDADOS_DADOSBASICOS);
                }
            } catch (Exception e) {
                e.printStackTrace();
            }
        }

        return formaPagamentoVO;
    }
}



package negocio.facade.jdbc.plano;

import negocio.comuns.basico.EmpresaVO;
import negocio.comuns.financeiro.ConvenioCobrancaVO;
import negocio.comuns.plano.ConvenioCobrancaEmpresaVO;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisValidacao;
import negocio.facade.jdbc.arquitetura.SuperEntidade;
import negocio.interfaces.plano.ConvenioCobrancaEmpresaInterfaceFacade;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class ConvenioCobrancaEmpresa extends SuperEntidade implements ConvenioCobrancaEmpresaInterfaceFacade {

    public ConvenioCobrancaEmpresa() throws Exception {
        super();
    }

    public ConvenioCobrancaEmpresa(Connection con) throws Exception {
        super(con);
    }

    @Override
    public void incluir(ConvenioCobrancaEmpresaVO obj) throws Exception {
        ConvenioCobrancaEmpresaVO.validarDados(obj);
        PreparedStatement stm = con.prepareStatement("INSERT INTO ConvenioCobrancaEmpresa(convenioCobranca, empresa, siglaPagarMe) VALUES (?, ?, ?)");
        int i = 0;
        stm.setInt(++i, obj.getConvenioCobranca().getCodigo());
        stm.setInt(++i, obj.getEmpresa().getCodigo());
        stm.setString(++i, obj.getSiglaPagarMe());
        stm.execute();
        obj.setCodigo(obterValorChavePrimariaCodigo());
        obj.setNovoObj(false);
    }

    @Override
    public void excluir(ConvenioCobrancaEmpresaVO obj) throws Exception {
        executarConsulta("DELETE FROM ConvenioCobrancaEmpresa WHERE codigo = " + obj.getCodigo(), con);
    }

    @Override
    public List<ConvenioCobrancaEmpresaVO> consultarPorConvenioCobranca(Integer convenioCobranca) throws Exception {
        ResultSet rs = criarConsulta("SELECT e.nome as nomeempresa, cce.* FROM ConvenioCobrancaEmpresa cce"
                + " INNER JOIN empresa e ON e.codigo = cce.empresa"
                + " WHERE conveniocobranca = " + convenioCobranca, con);
        return montarDadosConulta(rs);
    }

    @Override
    public ConvenioCobrancaEmpresaVO consultarPorConvenioCobrancaEmpresa(Integer convenioCobranca, Integer empresa) throws Exception {
        ResultSet rs = criarConsulta("SELECT * FROM ConvenioCobrancaEmpresa WHERE convenioCobranca = " + convenioCobranca + " and empresa = " + empresa, con);
        return rs.next() ? montarDados(rs) : null;
    }

    @Override
    public void excluirPorConvenioCobranca(ConvenioCobrancaVO convenioCobrancaVO) throws Exception {
        executarConsulta("DELETE FROM ConvenioCobrancaEmpresa WHERE convenioCobranca = " + convenioCobrancaVO.getCodigo(), con);
    }

    @Override
    public void alterarPorConvenioCobranca(ConvenioCobrancaVO convenioCobrancaVO) throws Exception {
        excluirPorConvenioCobranca(convenioCobrancaVO);
        for (ConvenioCobrancaEmpresaVO cpe : convenioCobrancaVO.getConfiguracoesEmpresa()) {
            cpe.setConvenioCobranca(convenioCobrancaVO);
            incluir(cpe);
        }
    }

    private List<ConvenioCobrancaEmpresaVO> montarDadosConulta(ResultSet rs) throws Exception {
        List<ConvenioCobrancaEmpresaVO> lista = new ArrayList<ConvenioCobrancaEmpresaVO>();
        while (rs.next()) {
            lista.add(montarDados(rs));
        }
        return lista;
    }

    public ConvenioCobrancaEmpresaVO montarDados(ResultSet rs) throws Exception {
        ConvenioCobrancaEmpresaVO obj = new ConvenioCobrancaEmpresaVO();
        obj.setCodigo(rs.getInt("codigo"));
        obj.setConvenioCobranca(new ConvenioCobrancaVO());
        obj.getConvenioCobranca().setCodigo(rs.getInt("convenioCobranca"));
        obj.setEmpresa(new EmpresaVO());
        obj.getEmpresa().setCodigo(rs.getInt("empresa"));
        obj.setSiglaPagarMe(rs.getString("siglaPagarMe"));
        try {
            obj.getEmpresa().setNome(rs.getString("nomeempresa"));
        } catch (Exception ignored) {
        }
        return obj;
    }

    public String obterEmpresasConvenio(Integer convenio) {
        if (UteisValidacao.emptyNumber(convenio)) {
            return "";
        }

        List<Integer> empresas = new ArrayList<Integer>();
        try {
            List<ConvenioCobrancaEmpresaVO> lista = consultarPorConvenioCobranca(convenio);
            for (ConvenioCobrancaEmpresaVO conv : lista) {
                empresas.add(conv.getEmpresa().getCodigo());
            }
        } catch (Exception ex) {
            empresas = new ArrayList<Integer>();
        }
        return Uteis.montarListaIN(empresas);
    }

    public List<Integer> obterEmpresasConvenio(List<Integer> convenios) {
        if (UteisValidacao.emptyList(convenios)) {
            return new ArrayList<Integer>();
        }

        List<Integer> empresas = new ArrayList<Integer>();
        try {
            Map<Integer, Integer> map = new HashMap<Integer, Integer>();
            for (Integer convenio : convenios) {
                ConvenioCobrancaEmpresa convenioCobrancaEmpresaDAO = new ConvenioCobrancaEmpresa(con);
                List<ConvenioCobrancaEmpresaVO> lista = convenioCobrancaEmpresaDAO.consultarPorConvenioCobranca(convenio);
                for (ConvenioCobrancaEmpresaVO conv : lista) {
                    map.put(conv.getEmpresa().getCodigo(), conv.getEmpresa().getCodigo());
                }
            }
            empresas.addAll(map.values());
        } catch (Exception ex) {
            empresas = new ArrayList<Integer>();
        }
        return empresas;
    }

    public void incluirVinculoSeNaoExistir(Integer convenioCobranca, Integer empresa) throws Exception {
        ConvenioCobrancaEmpresaVO confg = consultarPorConvenioCobrancaEmpresa(convenioCobranca, empresa);
        if (confg == null) {
            ConvenioCobrancaEmpresaVO nova = new ConvenioCobrancaEmpresaVO();
            nova.getEmpresa().setCodigo(empresa);
            nova.getConvenioCobranca().setCodigo(convenioCobranca);
            incluir(nova);
        }
    }

}

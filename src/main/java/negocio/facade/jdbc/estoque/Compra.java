/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */

package negocio.facade.jdbc.estoque;

import br.com.pactosolucoes.comuns.util.Formatador;
import negocio.comuns.arquitetura.UsuarioVO;
import negocio.comuns.basico.EmpresaVO;
import negocio.comuns.basico.FornecedorVO;
import negocio.comuns.estoque.CompraItensVO;
import negocio.comuns.estoque.CompraVO;
import negocio.comuns.financeiro.MovContaVO;
import negocio.comuns.utilitarias.*;
import negocio.facade.jdbc.arquitetura.SuperEntidade;
import negocio.facade.jdbc.arquitetura.Usuario;
import negocio.facade.jdbc.basico.Empresa;
import negocio.facade.jdbc.basico.Fornecedor;
import negocio.facade.jdbc.financeiro.ConfiguracaoFinanceiro;
import negocio.facade.jdbc.financeiro.MovConta;
import negocio.interfaces.estoque.CompraInterfaceFacade;
import org.apache.commons.lang.StringUtils;

import java.sql.*;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Date;
import java.util.HashSet;
import java.util.List;

/**
 * <AUTHOR>
 */
public class Compra extends SuperEntidade implements CompraInterfaceFacade {

    public Compra() throws Exception {
        super();
    }

    public Compra(Connection connection) throws Exception {
        super(connection);
    }

    /**
     * Responsável por montar os dados de vários objetos, resultantes de uma consulta ao banco de dados (<code>ResultSet</code>).
     * Faz uso da operação <code>montarDados</code> que realiza o trabalho para um objeto por vez.
     *
     * @return List Contendo vários objetos da classe <code>CompraVO</code> resultantes da consulta.
     */
    public static List montarDadosConsulta(ResultSet tabelaResultado, int nivelMontarDados, Connection con) throws Exception {
        List vetResultado = new ArrayList();
        while (tabelaResultado.next()) {
            CompraVO obj = new CompraVO();
            obj = montarDados(tabelaResultado, nivelMontarDados, con);
            vetResultado.add(obj);
        }
        return vetResultado;
    }

    public static CompraVO montarDadosBasico(ResultSet dadosSQL) throws Exception {
        CompraVO obj = new CompraVO();
        obj.setCodigo(dadosSQL.getInt("codigo"));
        obj.setContato(dadosSQL.getString("contato"));
        obj.setTelefoneContato(dadosSQL.getString("telefoneContato"));
        obj.setNumeroNF(dadosSQL.getString("numeroNF"));
        obj.setDataCadastro(dadosSQL.getTimestamp("dataCadastro"));
        obj.setDataCancelamento(dadosSQL.getTimestamp("dataCancelamento"));
        obj.setDataEmissao(dadosSQL.getDate("dataEmissao"));
        obj.setCancelada(dadosSQL.getBoolean("cancelada"));
        obj.setValorTotal(dadosSQL.getDouble("valorTotal"));
        obj.getEmpresa().setCodigo(dadosSQL.getInt("empresa"));
        obj.getFornecedor().setCodigo(dadosSQL.getInt("fornecedor"));
        obj.getUsuarioCadastro().setCodigo(dadosSQL.getInt("usuarioCadastro"));
        obj.getUsuarioCancelamento().setCodigo(dadosSQL.getInt("usuarioCancelamento"));
        obj.setObservacoes(dadosSQL.getString("observacoes"));
        obj.setPontos(dadosSQL.getInt("pontos"));
        try {
            obj.setSolicitacaoCompra(dadosSQL.getInt("solicitacaoCompra"));
        }catch (Exception ignore){}
        try {
            obj.setDescricaoFinanceiro(dadosSQL.getString("descricaoFinanceiro"));
        }catch (Exception ignore){}
        try {
            obj.setAutorizada(dadosSQL.getString("autorizada") == null ? null : dadosSQL.getBoolean("autorizada"));
        }catch (Exception ignore){}
        try {
            obj.setIdPedidoNuvemshop(dadosSQL.getString("idpedidonuvemshop") == null ? null : dadosSQL.getString("idpedidonuvemshop"));
        }catch (Exception ignore){}
        try {
            obj.setNrParcelasContaPagar(dadosSQL.getInt("nrParcelasContaPagar"));
        }catch (Exception ignore){}
        try {
            obj.setDocumento(dadosSQL.getString("documento"));
            obj.setDocumentoExtensao(dadosSQL.getString("documentoExtensao"));
        }catch (Exception ignore){}
        try {
            obj.setImportacaoObs(dadosSQL.getString("importacaoObs"));
        }catch (Exception ignore){}
        return obj;

    }

    private static void montarDadosFKs(CompraVO obj, Connection con) throws Exception {
        montarDadosEmpresa(obj, Uteis.NIVELMONTARDADOS_DADOSBASICOS, con);
        montarDadosFornecedor(obj, Uteis.NIVELMONTARDADOS_DADOSBASICOS, con);
        montarDadosUsuarioCadastro(obj, Uteis.NIVELMONTARDADOS_DADOSBASICOS, con);
        montarDadosUsuarioCancelamento(obj, Uteis.NIVELMONTARDADOS_DADOSBASICOS, con);
    }

    /**
     * Responsável por montar os dados resultantes de uma consulta ao banco de dados (<code>ResultSet</code>)
     * em um objeto da classe <code>CompraVO</code>.
     *
     * @return O objeto da classe <code>CompraVO</code> com os dados devidamente montados.
     */
    public static CompraVO montarDados(ResultSet dadosSQL, int nivelMontarDados, Connection con) throws Exception {
        CompraVO obj = montarDadosBasico(dadosSQL);
        obj.setNovoObj(false);
        if (nivelMontarDados == Uteis.NIVELMONTARDADOS_DADOSBASICOS) {
            return obj;
        }
        if (nivelMontarDados == Uteis.NIVELMONTARDADOS_TELACONSULTA) {
            montarDadosFKs(obj, con);
            return obj;
        }
        if (nivelMontarDados == Uteis.NIVELMONTARDADOS_TODOS) {
            montarDadosFKs(obj, con);
            obj.setItens(new HashSet<CompraItensVO>(getFacade().getCompraItens().consultarPorCompra(obj.getCodigo(), nivelMontarDados)));
            for (CompraItensVO compraItensVO : obj.getItens()) {
                getFacade().getProdutoEstoque().pesquisarDatasAlteracoesProdutoEstoque(compraItensVO, obj.getEmpresa().getCodigo());
                compraItensVO.setCompra(obj);
            }
            obj.setDocumentosCompra(getFacade().getCompraDocumentos().consultarPorCompra(obj.getCodigo()));
            obj.setCompraContasLancarFinanceiro(getFacade().getContaFinanceiroCompra().consultarPorCompra(obj.getCodigo()));
            return obj;
        }
        return obj;
    }

    public static void montarDadosEmpresa(CompraVO obj, int nivelMontarDados, Connection con) throws Exception {
        if ((obj.getEmpresa() == null) || (obj.getEmpresa().getCodigo().intValue() == 0)) {
            obj.setEmpresa(new EmpresaVO());
            return;
        }
        Empresa empresa = new Empresa(con);
        obj.setEmpresa(empresa.consultarPorChavePrimaria(obj.getEmpresa().getCodigo(), nivelMontarDados));
        empresa = null;
    }

    public static void montarDadosUsuarioCadastro(CompraVO obj, int nivelMontarDados, Connection con) throws Exception {
        if ((obj.getUsuarioCadastro() == null) || (obj.getUsuarioCadastro().getCodigo().intValue() == 0)) {
            obj.setUsuarioCadastro(new UsuarioVO());
            return;
        }
        Usuario usuario = new Usuario(con);
        obj.setUsuarioCadastro(usuario.consultarPorChavePrimaria(obj.getUsuarioCadastro().getCodigo(), nivelMontarDados));
        usuario = null;
    }

    public static void montarDadosUsuarioCancelamento(CompraVO obj, int nivelMontarDados, Connection con) throws Exception {
        if ((obj.getUsuarioCancelamento() == null) || (obj.getUsuarioCancelamento().getCodigo().intValue() == 0)) {
            obj.setUsuarioCancelamento(new UsuarioVO());
            return;
        }
        Usuario usuario = new Usuario(con);
        obj.setUsuarioCancelamento(usuario.consultarPorChavePrimaria(obj.getUsuarioCancelamento().getCodigo(), nivelMontarDados));
        usuario = null;
    }

    public static void montarDadosFornecedor(CompraVO obj, int nivelMontarDados, Connection con) throws Exception {
        if ((obj.getFornecedor() == null) || (obj.getFornecedor().getCodigo().intValue() == 0)) {
            obj.setFornecedor(new FornecedorVO());
            return;
        }
        Fornecedor fornecedor = new Fornecedor();
        obj.setFornecedor(fornecedor.obter(obj.getFornecedor().getCodigo()));
        fornecedor = null;
    }

    /**
     * Operação responsável por retornar um novo objeto da classe <code>CompraVO</code>.
     */
    public CompraVO novo() throws Exception {
        setIdEntidade("Compra");
        //incluir(getIdEntidade());
        CompraVO obj = new CompraVO();
        return obj;
    }

    public CompraVO consultarPorChavePrimaria(Integer codigoPrm, int nivelMontarDados) throws Exception {
        consultar(getIdEntidade(), false);
        String sql = " SELECT * FROM compra WHERE codigo = ?";
        PreparedStatement sqlConsultar = con.prepareStatement(sql);
        sqlConsultar.setInt(1, codigoPrm.intValue());
        ResultSet tabelaResultado = sqlConsultar.executeQuery();
        if (!tabelaResultado.next()) {
            throw new ConsistirException("Dados Não Encontrados ( Compra ).");
        }
        return (montarDados(tabelaResultado, nivelMontarDados, this.con));
    }

    public CompraVO consultarPorIdPedidoNuvemshop(String id, int nivelMontarDados) throws Exception {
        String sql = " SELECT * FROM compra WHERE idpedidonuvemshop = ?";
        PreparedStatement sqlConsultar = con.prepareStatement(sql);
        sqlConsultar.setString(1, id);
        ResultSet tabelaResultado = sqlConsultar.executeQuery();
        if (!tabelaResultado.next()) {
            return null;
        }
        return (montarDados(tabelaResultado, nivelMontarDados, this.con));
    }

    public List<CompraVO> consultar(Integer codigoEmpresa, Date dataIniCadastro, Date dataFimCadastro, Integer codigoFornecedor, String numeroNF, int nivelMontarDados) throws Exception {
        consultar(getIdEntidade(), false);
        StringBuilder sql = new StringBuilder();
        sql.append("select * from compra where (0 = 0)");
        if ((dataIniCadastro != null) && (dataFimCadastro != null)) {
            sql.append(" AND datacadastro BETWEEN '").append(Uteis.getDataJDBC(dataIniCadastro))
                    .append(" 00:00:00' ").append("AND '").append(Uteis.getDataJDBC(dataFimCadastro))
                    .append(" 23:59:59'");
        }
        if ((codigoFornecedor != null) && (codigoFornecedor.intValue() > 0)) {
            sql.append(" AND fornecedor = ").append(codigoFornecedor.intValue());
        }
        if (!numeroNF.equals("")) {
            sql.append(" and numeroNF = '").append(numeroNF).append("'");
        }
                if ((codigoEmpresa != null) && (codigoEmpresa.intValue() > 0)) {
            sql.append(" AND empresa = ").append(codigoEmpresa.intValue());
        }

        Statement stm = con.createStatement();
        ResultSet tabelaResultado = stm.executeQuery(sql.toString());
        return (montarDadosConsulta(tabelaResultado, nivelMontarDados, this.con));

    }

    public String consultarUltimoProdutoConfigJson(Integer codigoEmpresa) throws Exception {
        String sql = "SELECT ultimoprodutoconfigjson FROM compra WHERE empresa = ? ORDER BY codigo DESC LIMIT 1";

        try (PreparedStatement ps = con.prepareStatement(sql)) {
            ps.setInt(1, codigoEmpresa);

            try (ResultSet rs = ps.executeQuery()) {
                if (rs.next()) {
                    return rs.getString("ultimoprodutoconfigjson");
                }
            }
        }
        return null;
    }

    public CompraVO consultarPorNumeroNF(String numeroNF, int nivelMontarDados) throws Exception {
        if (numeroNF == null || numeroNF.isEmpty()) {
            return null;
        }

        String sql = " SELECT * FROM compra WHERE numeronf = ? AND cancelada = false";
        PreparedStatement sqlConsultar = con.prepareStatement(sql);
        sqlConsultar.setString(1, numeroNF);
        ResultSet tabelaResultado = sqlConsultar.executeQuery();
        if (!tabelaResultado.next()) {
            return null;
        }
        return (montarDados(tabelaResultado, nivelMontarDados, this.con));
    }

    public boolean existeCompraPorNumeroNF(String numeroNF) throws Exception {
        if (numeroNF == null || numeroNF.isEmpty()) {
            return false;
        }

        String sql = "SELECT EXISTS (SELECT 1 FROM compra WHERE numeroNF = ? AND cancelada = false) AS existe";

        try (PreparedStatement ps = con.prepareStatement(sql)) {
            ps.setString(1, numeroNF);

            try (ResultSet rs = ps.executeQuery()) {
                if (rs.next()) {
                    return rs.getBoolean("existe");
                }
            }
        }
        return false;
    }

    /**
     * Operação responsável por incluir no banco de dados um objeto da classe <code>CompraVO</code>.
     * Primeiramente valida os dados (<code>validarDados</code>) do objeto. Verifica a conexão com o banco de dados e a permissão do usuário
     * para realizar esta operacão na entidade.
     * Isto, através da operação <code>incluir</code> da superclasse.
     *
     * @param obj Objeto da classe <code>CompraVO</code> que será gravado no banco de dados.
     * @throws Exception Caso haja problemas de conexão, restrição de acesso ou validação de dados.
     */
    public void incluir(CompraVO obj) throws Exception {
        try {
            con.setAutoCommit(false);
            CompraVO.validarDados(obj);
            if (obj.getItens().size() <= 0) {
                throw new ConsistirException("É necessário informar ao menos um produto para a compra.");
            }
            String sql = "INSERT INTO Compra( empresa, fornecedor, numeroNF, contato, telefoneContato, dataEmissao," +
                    "dataCadastro, usuarioCadastro, observacoes, valorTotal, pontos, descricaoFinanceiro," +
                    " idPedidoNuvemshop, nrParcelasContaPagar, importacaoobs, ultimoprodutoconfigjson, solicitacaoCompra)" +
                    " VALUES ( ?, ?,?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)";

            PreparedStatement sqlInserir = con.prepareStatement(sql);
            sqlInserir.setInt(1, obj.getEmpresa().getCodigo());
            sqlInserir.setInt(2, obj.getFornecedor().getCodigo());
            sqlInserir.setString(3, obj.getNumeroNF());
            sqlInserir.setString(4, obj.getContato());
            sqlInserir.setString(5, obj.getTelefoneContato());
            if (obj.getDataEmissao() == null)
                sqlInserir.setNull(6, java.sql.Types.NULL);
            else
                sqlInserir.setDate(6, Uteis.getDataJDBC(obj.getDataEmissao()));
            sqlInserir.setTimestamp(7, Uteis.getDataJDBCTimestamp(obj.getDataCadastro()));
            sqlInserir.setInt(8, obj.getUsuarioCadastro().getCodigo());
            sqlInserir.setString(9, obj.getObservacoes());
            sqlInserir.setDouble(10, obj.getValorTotal());
            sqlInserir.setInt(11, obj.getPontos());
            sqlInserir.setString(12, StringUtils.isNotBlank(obj.getDescricaoFinanceiro()) ? obj.getDescricaoFinanceiro() : "");
            sqlInserir.setString(13, StringUtils.isNotBlank(obj.getIdPedidoNuvemshop()) ? obj.getIdPedidoNuvemshop() : "");
            sqlInserir.setInt(14, obj.getNrParcelasContaPagar());
            sqlInserir.setString(15, obj.getImportacaoObs());
            sqlInserir.setString(16, obj.getProdutoComConfigsGenericasJson());
            resolveIntegerNull(sqlInserir, 17, obj.getSolicitacaoCompra());
            sqlInserir.execute();
            obj.setCodigo(obterValorChavePrimariaCodigo());

            CompraItens compraItensDao = new CompraItens(getCon());
            compraItensDao.incluirListaCompraItens(obj, obj.getItens());
            compraItensDao = null;

            if (!UteisValidacao.emptyList(obj.getCompraContasLancarFinanceiro())) {
                ContaFinanceiroCompra contaFinanceiroCompraDAO = new ContaFinanceiroCompra(con);
                contaFinanceiroCompraDAO.incluirLista(obj.getCompraContasLancarFinanceiro(), obj);
                contaFinanceiroCompraDAO = null;
            }

            obj.setNovoObj(new Boolean(false));
            con.commit();
        } catch (Exception e) {
            obj.setNovoObj(new Boolean(true));
            con.rollback();
            con.setAutoCommit(true);
            throw e;
        } finally {
            con.setAutoCommit(true);
        }
    }

    private void validarRegraControleEstoque(CompraVO compraVO) throws Exception {
        // Verificar se a data da compra é menor que a data do último balanço do produto.
        for (CompraItensVO compraItensVO: compraVO.getItens()){
            Date dataBalanco = getFacade().getBalancoItens().pesquisarBalancoComDataMaior(compraItensVO.getCompra().getDataCadastro(), compraItensVO.getProduto().getCodigo(), compraItensVO.getCompra().getEmpresa().getCodigo());
            SimpleDateFormat sdf = new SimpleDateFormat("dd/MM/yyyy HH:mm:ss");
            if (dataBalanco != null){
                throw new ConsistirException("Operação não permitida, a data da compra é menor que a data do último balanço. " +
                        "Existe um balanço na data '" + sdf.format(dataBalanco) + "' para o produto '" + compraItensVO.getProduto().getDescricao() + "'.");
            }
        }

    }

    public void alterar(CompraVO obj, boolean validarData, boolean autorizar) throws Exception {
        try {
            if(validarData){
                validarRegraControleEstoque(obj);
            }
            con.setAutoCommit(false);
            CompraVO.validarDados(obj);
            String sql = "UPDATE Compra SET empresa = ?, fornecedor = ?, numeroNF = ?, contato = ?, telefoneContato = ?," +
                    " dataEmissao = ?, observacoes =?,dataCadastro = ?,pontos = ?, descricaoFinanceiro = ?, autorizada = ?," +
                    " idPedidoNuvemshop = ?, nrParcelasContaPagar = ?, solicitacaoCompra = ? WHERE codigo = ? ";

            PreparedStatement sqlAlterar = con.prepareStatement(sql);
            sqlAlterar.setInt(1, obj.getEmpresa().getCodigo());
            sqlAlterar.setInt(2, obj.getFornecedor().getCodigo());
            sqlAlterar.setString(3, obj.getNumeroNF());
            sqlAlterar.setString(4, obj.getContato());
            sqlAlterar.setString(5, obj.getTelefoneContato());
            sqlAlterar.setDate(6, Uteis.getDataJDBC(obj.getDataEmissao()));
            sqlAlterar.setString(7, obj.getObservacoes());
            sqlAlterar.setTimestamp(8, Uteis.getDataJDBCTimestamp(obj.getDataCadastro()));
            sqlAlterar.setInt(9, obj.getPontos());
            sqlAlterar.setString(10, StringUtils.isNotBlank(obj.getDescricaoFinanceiro()) ? obj.getDescricaoFinanceiro() : "");
            if (obj.getAutorizada() == null) {
                sqlAlterar.setNull(11, Types.NULL);
            } else {
                sqlAlterar.setBoolean(11, obj.getAutorizada());
            }
            sqlAlterar.setString(12, obj.getIdPedidoNuvemshop());
            sqlAlterar.setInt(13, obj.getNrParcelasContaPagar());
            resolveIntegerNull(sqlAlterar, 14, obj.getSolicitacaoCompra());
            sqlAlterar.setInt(15, obj.getCodigo());

            sqlAlterar.execute();
            //obj.setCodigo(obterValorChavePrimariaCodigo());

            if(autorizar) {
                CompraItens compraItensDao = new CompraItens(getCon());
                compraItensDao.incluirListaCompraItens(obj, obj.getItens());
                compraItensDao = null;
            }

            con.commit();
        } catch (Exception e) {
            con.rollback();
            throw e;
        } finally {
            con.setAutoCommit(true);
        }
    }

    public void cancelar(CompraVO obj) throws Exception {
        try {
            con.setAutoCommit(false);
            CompraVO.validarDados(obj);
            String sql = "UPDATE Compra SET cancelada = ?, usuarioCancelamento =?, dataCancelamento = ? WHERE codigo = ? ";

            PreparedStatement sqlAlterar = con.prepareStatement(sql);
            sqlAlterar.setBoolean(1, true);
            sqlAlterar.setInt(2, obj.getUsuarioCancelamento().getCodigo());
            sqlAlterar.setTimestamp(3, Uteis.getDataHoraJDBC(Calendario.getInstance().getTime(), Uteis.getHoraAtual()));
            sqlAlterar.setInt(4, obj.getCodigo());
            sqlAlterar.execute();

            MovConta movContaDAO = new MovConta(con);
            List<MovContaVO> movConta = movContaDAO.consultarMovContaCompraEstoque(obj.getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS);
            MovConta finalMovContaDAO = movContaDAO;
            if (movConta != null && !movConta.isEmpty()) {
                movConta.forEach(m -> {
                    if (m.getCodigo() != 0) {
                        try {
                            finalMovContaDAO.excluir(m);
                        } catch (Exception e) {
                            throw new RuntimeException(e);
                        }
                    }
                });
                movContaDAO = null;
            } else {
                con.commit();
            }
        } catch (Exception e) {
            obj.setNovoObj(new Boolean(true));
            con.rollback();
            con.setAutoCommit(true);
            throw e;
        } finally {
            con.setAutoCommit(true);
        }
    }

    public String consultarJSON(Integer empresa) throws Exception {
        ResultSet rs = getRS(empresa);

        StringBuilder json = new StringBuilder();
        json.append("{\"aaData\":[");
        boolean dados = false;
        ConfiguracaoFinanceiro configFinanDAO = new ConfiguracaoFinanceiro(con);
        boolean ordemCompra = configFinanDAO.consultar().isOrdemCompraEstoque();
        while (rs.next()) {
            dados = true;
            json.append("[\"").append(rs.getString("codigo")).append("\",");
            json.append("\"").append(rs.getString("fornecedor").trim().replaceAll("\\\\", "\\\\\\\\").replaceAll("\"", "\\\\\"")).append("\",");
            json.append("\"").append(rs.getDate("datacadastro")).append("\",");
            if(rs.getBoolean("cancelada")) {
                json.append("\"").append("Cancelada").append("\",");
            } else {
                json.append("\"").append("Ativa").append("\",");
            }

            if ((((ordemCompra && rs.getInt("quantidade") == 0) || rs.getInt("quantidadeAutorizada") > 0) && rs.getString("autorizada") == null) ||
                    (rs.getString("autorizada") != null && !rs.getBoolean("autorizada"))) {
                json.append("\"").append(rs.getString("quantidadeAutorizada")).append("\",");
            }
            if(((!ordemCompra || rs.getInt("quantidade") > 0) && rs.getInt("quantidadeAutorizada") == 0) || (rs.getString("autorizada") != null && rs.getBoolean("autorizada"))){
                json.append("\"").append(rs.getString("quantidade")).append("\",");
            }


            json.append("\"").append(Uteis.normalizarStringJSON(rs.getString("empresa"))).append("\",");
            json.append("\"").append(Formatador.formatarValorMonetario(rs.getDouble("valortotal"))).append("\",");
            if (ordemCompra) {
                json.append("\"").append(Uteis.normalizarStringJSON(rs.getString("autorizada") == null ?
                        "-" : ((rs.getBoolean("autorizada")) ? "AUTORIZADA" : "NEGADA"))).append("\",");
            }
            json.append("\"").append(rs.getInt("pontos")).append("\"],");
        }
        configFinanDAO = null;
        if(dados) {
            json.deleteCharAt(json.toString().length() - 1);
        }
        json.append("]}");
        return json.toString();
    }

    private ResultSet getRS(Integer empresa) throws SQLException {

        StringBuilder sql = new StringBuilder("SELECT cp.codigo, pe.nome AS fornecedor, \n");
        sql.append("cp.datacadastro, cp.cancelada, (SELECT sum(ci.quantidade) FROM compraitens ci WHERE ci.compra = cp.codigo) AS quantidade, (SELECT sum(ci.quantidadeAutorizada) FROM compraitens ci WHERE ci.compra = cp.codigo) AS quantidadeAutorizada, emp.nome AS empresa,\n");
        sql.append("  (SELECT SUM(prod) as valorTotal FROM (SELECT  distinct(ci.produto),ci.total AS prod FROM compraitens ci WHERE ci.compra = cp.codigo ) as v)  AS valorTotal, pontos, autorizada \n");
        sql.append("FROM compra cp\n");
        sql.append("  LEFT JOIN empresa emp ON cp.empresa = emp.codigo\n");
        sql.append("  LEFT JOIN fornecedor fc ON cp.fornecedor = fc.codigo\n");
        sql.append("  INNER JOIN pessoa pe ON fc.pessoa = pe.codigo");
        if (empresa != 0) {
            sql.append("  WHERE cp.empresa = ?");
        }
        sql.append("   ORDER BY cp.datacadastro DESC");
        PreparedStatement sqlConsultar = con.prepareStatement(sql.toString());
        if (empresa != 0) {
            sqlConsultar.setInt(1, empresa);
        }
        ResultSet rs = sqlConsultar.executeQuery();
        return rs;
    }
         public List consultarParaImpressao(String filtro, String ordem, String campoOrdenacao, Integer empresa) throws SQLException {

            ResultSet rs = getRS(empresa);
            List lista = new ArrayList();

            while (rs.next()) {

                CompraVO compra = new CompraVO();
                String geral = rs.getString("codigo") + rs.getString("fornecedor") + rs.getString("datacadastro") + rs.getString("cancelada") + rs.getString("empresa") + rs.getString("valortotal");
                if (geral.toLowerCase().contains(filtro.toLowerCase())) {
                    compra.setCodigo(rs.getInt("codigo"));
                    compra.getFornecedor().getPessoa().setNome(rs.getString("fornecedor"));
                    compra.setDataCadastro(rs.getDate("datacadastro"));
                    compra.setCancelada(rs.getBoolean("cancelada"));
                    compra.setQuantidade(rs.getInt("quantidade"));
                    compra.getEmpresa().setNome(rs.getString("empresa"));
                    compra.setValorTotal(rs.getDouble("valortotal"));
                    compra.setPontos(rs.getInt("pontos"));
                    lista.add(compra);
                }
            }
            if (campoOrdenacao.equals("Código")) {
                Ordenacao.ordenarLista(lista, "codigo");
            } else if (campoOrdenacao.equals("Fornecedor")) {
                Ordenacao.ordenarLista(lista, "fornecedor_Apresentar");
            } else if (campoOrdenacao.equals("Data da Compra")) {
                Ordenacao.ordenarLista(lista, "dataCadastro");
            } else if (campoOrdenacao.equals("Situação")) {
                Ordenacao.ordenarLista(lista, "situacao_Apresentar");
            } else if (campoOrdenacao.equals("Quantidade")) {
                Ordenacao.ordenarLista(lista, "quantidade");
            } else if (campoOrdenacao.equals("Empresa")) {
                Ordenacao.ordenarLista(lista, "empresa_Apresentar");
            } else if (campoOrdenacao.equals("Valor")) {
                Ordenacao.ordenarLista(lista, "valorTotal_Apresentar");
            } else if (campoOrdenacao.equals("pontos")) {
                Ordenacao.ordenarLista(lista, "pontos");
            }
            if (ordem.contains("desc")) {
                Collections.reverse(lista);
            }
            return lista;

        }

    public void alterarSomenteChaveArquivo(CompraVO obj) throws SQLException {
        String sql = "UPDATE compra SET documento = ?, documentoExtensao = ? WHERE codigo = ?";
        try (PreparedStatement sqlAlterar = con.prepareStatement(sql)) {
            int i = 0;
            sqlAlterar.setString(++i, obj.getDocumento());
            sqlAlterar.setString(++i, obj.getDocumentoExtensao());
            sqlAlterar.setInt(++i, obj.getCodigo());
            sqlAlterar.execute();
        }
    }
}

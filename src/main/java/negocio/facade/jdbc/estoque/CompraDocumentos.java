package negocio.facade.jdbc.estoque;

import negocio.comuns.estoque.CompraItensVO;
import negocio.comuns.estoque.CompraVO;
import negocio.comuns.estoque.DocumentoCompraVO;
import negocio.facade.jdbc.arquitetura.SuperEntidade;
import negocio.interfaces.estoque.CompraDocumentosInterfaceFacade;
import servicos.operacoes.midias.MidiaService;
import servicos.operacoes.midias.commons.MidiaEntidadeEnum;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.util.ArrayList;
import java.util.List;

public class CompraDocumentos extends SuperEntidade implements CompraDocumentosInterfaceFacade {


    public CompraDocumentos() throws Exception {
        super();
        setIdEntidade("CompraDocumentos");
    }

    public CompraDocumentos(Connection con) throws Exception {
        super(con);
        setIdEntidade("CompraDocumentos");

    }
    public void excluirPorCompra(String chave, CompraVO compra) throws Exception {
        try {
            List<DocumentoCompraVO> documentos = consultarPorCompra(compra.getCodigo());
            if (documentos != null) {
                for (DocumentoCompraVO doc : documentos) {
                    MidiaService.getInstance().deleteObject(chave, MidiaEntidadeEnum.ANEXO_DOCUMENTO_COMPRA, doc.getIdentificadorArquivo());
                }
                String sql = "DELETE FROM DocumentoCompra WHERE compra = " + compra.getCodigo();
                PreparedStatement sqlExcluir = con.prepareStatement(sql);
                sqlExcluir.execute();
            }
        } catch (Exception e) {
            throw e;
        }
    }

    public void incluir(DocumentoCompraVO obj) throws Exception {
        CompraItensVO.validarDados(obj);
        String sql = "INSERT INTO DocumentoCompra( compra, identificadorArquivo, chaveArquivo, extensaoArquivo ) VALUES ( ?, ?, ?, ?)";

        PreparedStatement sqlInserir = con.prepareStatement(sql);
        sqlInserir.setInt(1, obj.getCompra().getCodigo());
        sqlInserir.setString(2, obj.getIdentificadorArquivo());
        sqlInserir.setString(3, obj.getChaveArquivo());
        sqlInserir.setString(4, obj.getExtensaoArquivo());

        sqlInserir.execute();
    }

    public void incluirListaDocumentosCompra(String chave, CompraVO compraVO) throws Exception {
        excluirPorCompra(chave, compraVO);
        for (DocumentoCompraVO doucmento: compraVO.getDocumentosCompra()){
            doucmento.setCompra(compraVO);
            incluir(doucmento);
        }
    }

    public List<DocumentoCompraVO> consultarPorCompra(Integer codigoCompra) throws Exception {
        List<DocumentoCompraVO> documentos = null;
        StringBuilder sql = new StringBuilder(" select * from documentocompra where compra = ").append(codigoCompra);
        PreparedStatement pst = con.prepareStatement(sql.toString());
        ResultSet tabelaResultado = pst.executeQuery();
        while (tabelaResultado.next()) {
            if (documentos == null) {
                documentos = new ArrayList<>();
            }
            DocumentoCompraVO doc = new DocumentoCompraVO();
            CompraVO compra = new CompraVO();
            compra.setCodigo(tabelaResultado.getInt("compra"));
            doc.setCompra(compra);
            doc.setChaveArquivo(tabelaResultado.getString("chaveArquivo"));
            doc.setIdentificadorArquivo(tabelaResultado.getString("identificadorArquivo"));
            doc.setExtensaoArquivo(tabelaResultado.getString("extensaoArquivo"));
            documentos.add(doc);
        }
        return documentos;
    }
}

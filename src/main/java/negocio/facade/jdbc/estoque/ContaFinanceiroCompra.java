package negocio.facade.jdbc.estoque;

import negocio.comuns.estoque.CompraItensVO;
import negocio.comuns.estoque.CompraVO;
import negocio.comuns.estoque.ContaFinanceiroCompraVO;
import negocio.comuns.utilitarias.Uteis;
import negocio.facade.jdbc.arquitetura.SuperEntidade;
import negocio.interfaces.estoque.ContaFinanceiroCompraInterfaceFacade;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.util.ArrayList;
import java.util.List;

public class ContaFinanceiroCompra extends SuperEntidade implements ContaFinanceiroCompraInterfaceFacade {


    public ContaFinanceiroCompra() throws Exception {
        super();
        setIdEntidade("ContaFinanceiroCompra");
    }

    public ContaFinanceiroCompra(Connection con) throws Exception {
        super(con);
        setIdEntidade("ContaFinanceiroCompra");

    }
    public void incluir(ContaFinanceiroCompraVO obj) throws Exception {
        CompraItensVO.validarDados(obj);
        String sql = "INSERT INTO parcelasCompraContaFinanceiro( compra, nrParcela, valorParcela, dataVencimento, codigoBarras ) VALUES ( ?, ?, ?, ?, ?)";

        PreparedStatement sqlInserir = con.prepareStatement(sql);
        sqlInserir.setInt(1, obj.getCompra().getCodigo());
        sqlInserir.setInt(2, obj.getNrParcela());
        sqlInserir.setDouble(3, obj.getValorParcela());
        sqlInserir.setDate(4, Uteis.getDataJDBC(obj.getDataVencimento()));
        sqlInserir.setString(5, obj.getCodigoBarras());

        sqlInserir.execute();
    }

    public void incluirLista(List<ContaFinanceiroCompraVO> parcelasConta, CompraVO compra) throws Exception {
        for (ContaFinanceiroCompraVO contaParc: parcelasConta){
            contaParc.setCompra(compra);
            incluir(contaParc);
        }
    }

    public List<ContaFinanceiroCompraVO> consultarPorCompra(Integer codigoCompra) throws Exception {
        List<ContaFinanceiroCompraVO> parcelasConta = null;
        StringBuilder sql = new StringBuilder(" select * from parcelasCompraContaFinanceiro where compra = ").append(codigoCompra);
        PreparedStatement pst = con.prepareStatement(sql.toString());
        ResultSet tabelaResultado = pst.executeQuery();
        while (tabelaResultado.next()) {
            if (parcelasConta == null) {
                parcelasConta = new ArrayList<>();
            }
            ContaFinanceiroCompraVO parc = new ContaFinanceiroCompraVO();
            CompraVO compra = new CompraVO();
            compra.setCodigo(tabelaResultado.getInt("compra"));
            parc.setCompra(compra);
            parc.setNrParcela(tabelaResultado.getInt("nrParcela"));
            parc.setValorParcela(tabelaResultado.getDouble("valorParcela"));
            parc.setDataVencimento(tabelaResultado.getDate("dataVencimento"));
            parc.setCodigoBarras(tabelaResultado.getString("codigoBarras"));
            parcelasConta.add(parc);
        }
        return parcelasConta;
    }
}

/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package negocio.facade.jdbc.estoque;

import java.sql.SQLException;
import negocio.comuns.arquitetura.UsuarioVO;
import negocio.comuns.basico.EmpresaVO;
import negocio.comuns.estoque.BalancoItensVO;
import negocio.comuns.estoque.BalancoVO;
import negocio.comuns.utilitarias.Calendario;
import negocio.comuns.utilitarias.ConsistirException;
import negocio.comuns.utilitarias.Uteis;
import negocio.facade.jdbc.arquitetura.SuperEntidade;
import negocio.facade.jdbc.arquitetura.Usuario;
import negocio.facade.jdbc.basico.Empresa;
import negocio.interfaces.estoque.BalancoInterfaceFacade;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.Statement;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Date;
import java.util.HashSet;
import java.util.List;
import java.util.stream.Collectors;

import negocio.comuns.utilitarias.Ordenacao;

/**
 * <AUTHOR>
 */
public class Balanco extends SuperEntidade implements BalancoInterfaceFacade {

    public Balanco() throws Exception {
        super();
    }

    public Balanco(Connection connection) throws Exception {
        super(connection);
    }

    /**
     * Responsável por montar os dados de vários objetos, resultantes de uma consulta ao banco de dados (<code>ResultSet</code>).
     * Faz uso da operação <code>montarDados</code> que realiza o trabalho para um objeto por vez.
     *
     * @return List Contendo vários objetos da classe <code>BalancoVO</code> resultantes da consulta.
     */
    public static List montarDadosConsulta(ResultSet tabelaResultado, int nivelMontarDados, Connection con) throws Exception {
        List vetResultado = new ArrayList();
        while (tabelaResultado.next()) {
            BalancoVO obj = new BalancoVO();
            obj = montarDados(tabelaResultado, nivelMontarDados, con);
            vetResultado.add(obj);
        }
        return vetResultado;
    }

    public static BalancoVO montarDadosBasico(ResultSet dadosSQL) throws Exception {
        BalancoVO obj = new BalancoVO();
        obj.setCodigo(dadosSQL.getInt("codigo"));
        obj.setDataCadastro(dadosSQL.getTimestamp("dataCadastro"));
        obj.setDataCancelamento(dadosSQL.getTimestamp("dataCancelamento"));
        obj.setCancelado(dadosSQL.getBoolean("cancelado"));
        obj.getEmpresa().setCodigo(dadosSQL.getInt("empresa"));
        obj.getUsuarioCadastro().setCodigo(dadosSQL.getInt("usuarioCadastro"));
        obj.setObservacoes(dadosSQL.getString("observacoes"));
        obj.getUsuarioCancelamento().setCodigo(dadosSQL.getInt("usuarioCancelamento"));
        obj.setDescricao(dadosSQL.getString("descricao"));
        return obj;
    }

    private static void montarDadosFKs(BalancoVO obj, Connection con) throws Exception {
        montarDadosEmpresa(obj, Uteis.NIVELMONTARDADOS_DADOSBASICOS, con);
        montarDadosUsuarioCadastro(obj, Uteis.NIVELMONTARDADOS_DADOSBASICOS, con);
        montarDadosUsuarioCancelamento(obj, Uteis.NIVELMONTARDADOS_DADOSBASICOS, con);
    }

    /**
     * Responsável por montar os dados resultantes de uma consulta ao banco de dados (<code>ResultSet</code>)
     * em um objeto da classe <code>BalancoVO</code>.
     *
     * @return O objeto da classe <code>BalancoVO</code> com os dados devidamente montados.
     */
    public static BalancoVO montarDados(ResultSet dadosSQL, int nivelMontarDados, Connection con) throws Exception {
        BalancoVO obj = montarDadosBasico(dadosSQL);
        obj.setNovoObj(false);
        if (nivelMontarDados == Uteis.NIVELMONTARDADOS_DADOSBASICOS) {
            return obj;
        }
        if (nivelMontarDados == Uteis.NIVELMONTARDADOS_TELACONSULTA) {
            montarDadosFKs(obj, con);
            return obj;
        }
        if (nivelMontarDados == Uteis.NIVELMONTARDADOS_TODOS) {
            montarDadosFKs(obj, con);
            obj.setItens(new HashSet<BalancoItensVO>(getFacade().getBalancoItens().consultarPorBalanco(obj.getCodigo(), nivelMontarDados)));
            for (BalancoItensVO balancoItensVO : obj.getItens()) {
                balancoItensVO.setBalanco(obj);
            }
            return obj;
        }
        return obj;
    }

    public static void montarDadosEmpresa(BalancoVO obj, int nivelMontarDados, Connection con) throws Exception {
        if ((obj.getEmpresa() == null) || (obj.getEmpresa().getCodigo().intValue() == 0)) {
            obj.setEmpresa(new EmpresaVO());
            return;
        }
        Empresa empresa = new Empresa(con);
        obj.setEmpresa(empresa.consultarPorChavePrimaria(obj.getEmpresa().getCodigo(), nivelMontarDados));
        empresa = null;
    }

    public static void montarDadosUsuarioCadastro(BalancoVO obj, int nivelMontarDados, Connection con) throws Exception {
        if ((obj.getUsuarioCadastro() == null) || (obj.getUsuarioCadastro().getCodigo().intValue() == 0)) {
            obj.setUsuarioCadastro(new UsuarioVO());
            return;
        }
        Usuario usuario = new Usuario(con);
        obj.setUsuarioCadastro(usuario.consultarPorChavePrimaria(obj.getUsuarioCadastro().getCodigo(), nivelMontarDados));
        usuario = null;
    }

    public static void montarDadosUsuarioCancelamento(BalancoVO obj, int nivelMontarDados, Connection con) throws Exception {
        if ((obj.getUsuarioCancelamento() == null) || (obj.getUsuarioCancelamento().getCodigo().intValue() == 0)) {
            obj.setUsuarioCancelamento(new UsuarioVO());
            return;
        }
        Usuario usuario = new Usuario(con);
        obj.setUsuarioCancelamento(usuario.consultarPorChavePrimaria(obj.getUsuarioCancelamento().getCodigo(), nivelMontarDados));
        usuario = null;
    }

    /**
     * Operação responsável por retornar um novo objeto da classe <code>BalancoVO</code>.
     */
    public BalancoVO novo() throws Exception {
        setIdEntidade("Balanco");
        //incluir(getIdEntidade());
        BalancoVO obj = new BalancoVO();
        return obj;
    }

    public BalancoVO consultarPorChavePrimaria(Integer codigoPrm, int nivelMontarDados) throws Exception {
        consultar(getIdEntidade(), false);
        String sql = " SELECT * FROM Balanco WHERE codigo = ?";
        PreparedStatement sqlConsultar = con.prepareStatement(sql);
        sqlConsultar.setInt(1, codigoPrm.intValue());
        ResultSet tabelaResultado = sqlConsultar.executeQuery();
        if (!tabelaResultado.next()) {
            throw new ConsistirException("Dados Não Encontrados ( Balanco ).");
        }
        return (montarDados(tabelaResultado, nivelMontarDados, this.con));
    }

    public List<BalancoVO> consultar(Integer codigoEmpresa, Date dataIniCadastro, Date dataFimCadastro, int situacao, int nivelMontarDados) throws Exception {
        consultar(getIdEntidade(), false);
        StringBuilder sql = new StringBuilder();
        sql.append("select * from Balanco where (0 = 0)");
        if ((dataIniCadastro != null) && (dataFimCadastro != null)) {
            sql.append(" AND datacadastro BETWEEN '").append(Uteis.getDataJDBC(dataIniCadastro)).append(" 00:00:00' ").append("AND '").append(Uteis.getDataJDBC(dataFimCadastro)).append(" 23:59:59'");
        }
        if (situacao >= 0) {
            sql.append(" AND cancelado = ").append(situacao == 0 ? "false" : "true");
        }
        if ((codigoEmpresa != null) && (codigoEmpresa.intValue() > 0)) {
            sql.append(" and empresa = ").append(codigoEmpresa);
        }
        Statement stm = con.createStatement();
        ResultSet tabelaResultado = stm.executeQuery(sql.toString());
        return (montarDadosConsulta(tabelaResultado, nivelMontarDados, this.con));

    }

    /**
     * Operação responsável por incluir no banco de dados um objeto da classe <code>BalancoVO</code>.
     * Primeiramente valida os dados (<code>validarDados</code>) do objeto. Verifica a conexão com o banco de dados e a permissão do usuário
     * para realizar esta operacão na entidade.
     * Isto, através da operação <code>incluir</code> da superclasse.
     *
     * @param obj Objeto da classe <code>BalancoVO</code> que será gravado no banco de dados.
     * @throws Exception Caso haja problemas de conexão, restrição de acesso ou validação de dados.
     */
    public void incluir(BalancoVO obj) throws Exception {
        try {
            con.setAutoCommit(false);
            BalancoVO.validarDados(obj);
            if (obj.getItens().size() <= 0) {
                throw new ConsistirException("É necessário informar ao menos um produto para o balanço .");
            }
            String sql = "INSERT INTO Balanco( empresa, dataCadastro, usuarioCadastro, observacoes,descricao ) VALUES ( ?, ?, ?, ?,? )";

            PreparedStatement sqlInserir = con.prepareStatement(sql);
            sqlInserir.setInt(1, obj.getEmpresa().getCodigo());
            sqlInserir.setTimestamp(2, Uteis.getDataHoraJDBC(Calendario.getInstance().getTime(), Uteis.getHoraAtual()));
            sqlInserir.setInt(3, obj.getUsuarioCadastro().getCodigo());
            sqlInserir.setString(4, obj.getObservacoes());
            sqlInserir.setString(5, obj.getDescricao());
            sqlInserir.execute();
            obj.setCodigo(obterValorChavePrimariaCodigo());

            getFacade().getBalancoItens().incluirListaBalancoItens(obj, obj.getItens());

            obj.setNovoObj(new Boolean(false));
            con.commit();
        } catch (Exception e) {
            obj.setNovoObj(new Boolean(true));
            con.rollback();
            con.setAutoCommit(true);
            throw e;
        } finally {
            con.setAutoCommit(true);
        }
    }

    public void cancelar(BalancoVO obj) throws Exception {
        try {
            con.setAutoCommit(false);
            BalancoVO.validarDados(obj);
            String sql = "UPDATE Balanco SET cancelado = ?, usuarioCancelamento =? , dataCancelamento = ? WHERE codigo = ? ";

            PreparedStatement sqlAlterar = con.prepareStatement(sql);
            sqlAlterar.setBoolean(1, true);
            sqlAlterar.setInt(2, obj.getUsuarioCancelamento().getCodigo());
            sqlAlterar.setTimestamp(3, Uteis.getDataHoraJDBC(Calendario.getInstance().getTime(), Uteis.getHoraAtual()));
            sqlAlterar.setInt(4, obj.getCodigo());
            sqlAlterar.execute();
            con.commit();
        } catch (Exception e) {
            obj.setNovoObj(new Boolean(true));
            con.rollback();
            con.setAutoCommit(true);
            throw e;
        } finally {
            con.setAutoCommit(true);
        }
    }

    public BalancoVO consultarUltimoBalancoAtivo(Integer codigoEmpresa, int nivelMontarDados) throws Exception {
        String sql = "SELECT * FROM balanco WHERE codigo = (SELECT max(codigo) FROM balanco WHERE cancelado = false AND empresa = ?)";
        PreparedStatement sqlConsultar = con.prepareStatement(sql);
        sqlConsultar.setInt(1, codigoEmpresa);
        ResultSet tabelaResultado = sqlConsultar.executeQuery();
        if (!tabelaResultado.next()) {
            return null;
        }
        return (montarDados(tabelaResultado, nivelMontarDados, this.con));

    }

    public String consultarJSON(Integer empresa) throws Exception {
        ResultSet rs = getRS(empresa);

        StringBuilder json = new StringBuilder();
        json.append("{\"aaData\":[");
        boolean dados = false;
        while (rs.next()) {
            dados = true;
            json.append("[\"").append(rs.getString("codigo")).append("\",");
            json.append("\"").append(rs.getDate("datacadastro")).append("\",");
            if (rs.getBoolean("cancelado")) {
                json.append("\"").append("Cancelado").append("\",");
            } else {
                json.append("\"").append("Ativo").append("\",");
            }
            json.append("\"").append(Uteis.normalizarStringJSON(rs.getString("empresa"))).append("\",");
            json.append("\"").append(Uteis.normalizarStringJSON(rs.getString("descricao"))).append("\"],");
        }
        if (dados) {
            json.deleteCharAt(json.toString().length() - 1);
        }
        json.append("]}");
        return json.toString();
    }

    private ResultSet getRS(Integer empresa) throws SQLException {
        StringBuilder sql = new StringBuilder("SELECT ba.codigo, ba.datacadastro, \n"
                + "ba.cancelado AS cancelado, emp.nome AS empresa, ba.descricao \n"
                + "FROM balanco ba\n"
                + "  LEFT JOIN empresa emp ON ba.empresa = emp.codigo");
        if (empresa != 0) {
            sql.append(" WHERE ba.empresa = ?");
        }
        sql.append("  ORDER BY ba.codigo DESC");
        PreparedStatement sqlConsultar = con.prepareStatement(sql.toString());
        if (empresa != 0) {
            sqlConsultar.setInt(1, empresa);
        }
        ResultSet rs = sqlConsultar.executeQuery();
        return rs;
    }

    public List consultarParaImpressao(String filtro, String ordem, String campoOrdenacao, Integer empresa) throws SQLException {

        ResultSet rs = getRS(empresa);
        List lista = new ArrayList();

        while (rs.next()) {

            BalancoVO balanco = new BalancoVO();
            String geral = rs.getString("codigo") + rs.getString("datacadastro") + rs.getString("cancelado") + rs.getString("empresa");
            if (geral.toLowerCase().contains(filtro.toLowerCase())) {
                balanco.setCodigo(rs.getInt("codigo"));
                balanco.setDataCadastro(rs.getTimestamp("datacadastro"));
                balanco.setCancelado(rs.getBoolean("cancelado"));
                balanco.getEmpresa().setNome(rs.getString("empresa"));
                balanco.setDescricao(rs.getString("descricao"));
                lista.add(balanco);
            }
        }
        if (campoOrdenacao.equals("Código")) {
            Ordenacao.ordenarLista(lista, "codigo");
        } else if (campoOrdenacao.equals("Data")) {
            Ordenacao.ordenarLista(lista, "dataCadastro");
        } else if (campoOrdenacao.equals("Situação")) {
            Ordenacao.ordenarLista(lista, "situacao_Apresentar");
        } else if (campoOrdenacao.equals("Empresa")) {
            Ordenacao.ordenarLista(lista, "empresa_Apresentar");
        }
        if (ordem.contains("desc")) {
            Collections.reverse(lista);
        }
        return lista;

    }

    @Override
    public List<BalancoVO> consultarBalancoMesclado(List<Integer> idProdutosMesclados) throws Exception {
        List<BalancoVO> listaBalancos = new ArrayList<>();

        if (idProdutosMesclados == null || idProdutosMesclados.isEmpty()) {
            return listaBalancos;
        }

        StringBuilder sql = new StringBuilder();
        sql.append("SELECT DISTINCT b.* FROM balanco b ")
                .append("INNER JOIN balancoitens b2 ON b.codigo = b2.codigo ")
                .append("WHERE b.observacoes LIKE 'Mesclado%' ")
                .append("AND b2.produto IN (");


        sql.append(idProdutosMesclados.stream().map(p -> "?").collect(Collectors.joining(","))).append(")");

        try (PreparedStatement sqlConsultar = con.prepareStatement(sql.toString())) {
            for (int i = 0; i < idProdutosMesclados.size(); i++) {
                sqlConsultar.setInt(i + 1, idProdutosMesclados.get(i));
            }

            ResultSet tabelaResultado = sqlConsultar.executeQuery();
            while (tabelaResultado.next()) {
                listaBalancos.add(montarDados(tabelaResultado, 1, this.con));
            }
        }

        return listaBalancos;
    }

}

/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */

package negocio.facade.jdbc.estoque;

import negocio.comuns.estoque.CompraItensVO;
import negocio.comuns.estoque.CompraVO;
import negocio.comuns.utilitarias.ConsistirException;
import negocio.comuns.utilitarias.UteisValidacao;
import negocio.facade.jdbc.arquitetura.SuperEntidade;
import negocio.interfaces.estoque.CompraItensInterfaceFacade;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Set;

/**
 *
 * <AUTHOR>
 */
public class CompraItens extends SuperEntidade implements CompraItensInterfaceFacade {


    public CompraItens() throws Exception {
        super();
        setIdEntidade("CompraItens");
    }

    public CompraItens(Connection con) throws Exception {
        super(con);
        setIdEntidade("CompraItens");

    }
    public CompraItensVO consultarPorChavePrimaria(Integer codigoPrm, int nivelMontarDados) throws Exception {
        consultar(getIdEntidade(), false);
        String sql = "SELECT * FROM CompraItens WHERE codigo = ?";
        PreparedStatement sqlConsultar = con.prepareStatement(sql);
        sqlConsultar.setInt(1, codigoPrm.intValue());
        ResultSet tabelaResultado = sqlConsultar.executeQuery();
        if (!tabelaResultado.next()) {
            throw new ConsistirException("Dados Não Encontrados ( CompraItens ).");
        }
        return (montarDados(tabelaResultado, nivelMontarDados));
    }

    public List<CompraItensVO> consultarPorCompra(Integer codigoCompra, int nivelMontarDados) throws Exception {
        StringBuilder sql = new StringBuilder(getSqlPadrao()).append(" where ci.compra = ").append(codigoCompra);
        sql.append(" order by  ci.codigo desc, p.descricao");
        PreparedStatement pst = con.prepareStatement(sql.toString());
        //pst.setInt(1, codigoCompra.intValue());
        ResultSet tabelaResultado = pst.executeQuery();
        return (montarDadosConsulta(tabelaResultado, nivelMontarDados));
    }

    /**
     * MontarDados para varios objetos
     * @param tabelaResultado
     * @param nivelMontarDados
     * @return
     * @throws Exception
     */
    public static List montarDadosConsulta(ResultSet tabelaResultado,
            int nivelMontarDados) throws Exception {
        List vetResultado = new ArrayList();
        while (tabelaResultado.next()) {
            CompraItensVO obj = new CompraItensVO();
            obj = montarDados(tabelaResultado, nivelMontarDados);
            vetResultado.add(obj);
        }
        return vetResultado;
    }

    /**
     * Responsável por montar os dados resultantes de uma consulta ao banco de dados (<code>ResultSet</code>)
     * em um objeto da classe <code>CompraVO</code>.
     * @return  O objeto da classe <code>CompraVO</code> com os dados devidamente montados.
     */
    public static CompraItensVO montarDados(ResultSet dadosSQL, int nivelMontarDados) throws Exception {
        CompraItensVO obj = new CompraItensVO();
        obj.setCodigo(dadosSQL.getInt("codigoCompraItens"));
        obj.getCompra().setCodigo(dadosSQL.getInt("codigoCompra"));
        obj.getProduto().setCodigo(dadosSQL.getInt("codigoProduto"));
        obj.getProduto().setDescricao(dadosSQL.getString("descricaoProduto"));
        obj.setQuantidade(dadosSQL.getInt("Quantidade"));
        obj.setValorUnitario(dadosSQL.getDouble("valorUnitario"));
        obj.setDesconto(dadosSQL.getDouble("desconto"));
        obj.setTotal(dadosSQL.getDouble("total"));
        obj.setPontos(dadosSQL.getInt("pontos"));
        try {
            obj.setQuantidadeAutorizar(dadosSQL.getInt("quantidadeAutorizada"));
        }catch (Exception ignore){}

        obj.setNovoObj(false);
        return obj;
    }


    private String getSqlPadrao(){
        StringBuilder sql = new StringBuilder();
        sql.append("select ");
        sql.append("ci.codigo as codigoCompraItens, ");
        sql.append("c.codigo as codigoCompra, ");
        sql.append("p.codigo as codigoProduto, ");
        sql.append("p.descricao descricaoProduto, ");
        sql.append("ci.quantidade, ");
        sql.append("ci.valorUnitario, ");
        sql.append("ci.desconto, ");
        sql.append("ci.total, ");
        sql.append("ci.quantidadeAutorizada, ");
        sql.append("(ci.quantidade * p.qtdepontos ) as pontos ");
        sql.append("from compraItens ci ");
        sql.append("inner join compra c on c.codigo = ci.compra ");
        sql.append("inner join produto p on p.codigo = ci.produto ");
        return sql.toString();


    }
	/**
     * Operação responsável por retornar um novo objeto da classe <code>CompraItensVO</code>.
     */
    public CompraItensVO novo() throws Exception {
        //incluir(getIdEntidade());
        CompraItensVO obj = new CompraItensVO();
        return obj;
    }

    private void validarRegraControleEstoque(CompraItensVO compraItensVO) throws Exception {
        // Verificar se a data da compra é menor que a data do último balanço do produto.
        Date dataBalanco = getFacade().getBalancoItens().pesquisarBalancoComDataMaior(compraItensVO.getCompra().getDataCadastro(), compraItensVO.getProduto().getCodigo(), compraItensVO.getCompra().getEmpresa().getCodigo());
        SimpleDateFormat sdf = new SimpleDateFormat("dd/MM/yyyy HH:mm:ss");
        if (dataBalanco != null){
            throw new ConsistirException("Operação não permitida, a data da compra é menor que a data do último balanço. " +
                    "Existe um balanço na data '" + sdf.format(dataBalanco) + "' para o produto '" + compraItensVO.getProduto().getDescricao() + "'.");
        }

    }
    /**
     * Operação responsável por incluir no banco de dados um objeto da classe <code>CompraItensVO</code>.
     * Primeiramente valida os dados (<code>validarDados</code>) do objeto. Verifica a conexão com o banco de dados e a permissão do usuário
     * para realizar esta operacão na entidade.
     * Isto, através da operação <code>incluir</code> da superclasse.
     * @param obj  Objeto da classe <code>CompraItensVO</code> que será gravado no banco de dados.
     * @exception Exception Caso haja problemas de conexão, restrição de acesso ou validação de dados.
     */
    public void incluir(CompraItensVO obj) throws Exception {
        validarRegraControleEstoque(obj);
        CompraItensVO.validarDados(obj);
        //incluir(getIdEntidade());
        String sql = "INSERT INTO CompraItens( compra, produto, quantidade, valorUnitario, desconto, total,pontos, quantidadeAutorizada ) VALUES ( ?, ?, ?, ?, ?, ?,?,?)";

        PreparedStatement sqlInserir = con.prepareStatement(sql);
        sqlInserir.setInt(1, obj.getCompra().getCodigo());
        sqlInserir.setInt(2, obj.getProduto().getCodigo());
        sqlInserir.setInt(3, obj.getQuantidade());
        sqlInserir.setDouble(4, obj.getValorUnitario());
        sqlInserir.setDouble(5, obj.getDesconto());
        sqlInserir.setDouble(6, obj.getTotal());
        sqlInserir.setInt(7, obj.getPontos());
        if (UteisValidacao.emptyNumber(obj.getQuantidadeAutorizar())) {
            sqlInserir.setNull(8, 0);
        } else {
            sqlInserir.setInt(8, obj.getQuantidadeAutorizar());
        }

        sqlInserir.execute();
        obj.setCodigo(obterValorChavePrimariaCodigo());
        obj.setNovoObj(new Boolean(false));
    }



    public void incluirListaCompraItens(CompraVO compraVO, Set<CompraItensVO> lista) throws Exception {
        for (CompraItensVO obj: lista){
            obj.setCompra(compraVO);
            incluir(obj);
        }
    }


}

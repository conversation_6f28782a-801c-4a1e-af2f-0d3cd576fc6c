/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */

package negocio.facade.jdbc.estoque;

import negocio.comuns.estoque.CardexVO;
import negocio.comuns.utilitarias.Calendario;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisValidacao;
import negocio.facade.jdbc.arquitetura.SuperEntidade;
import negocio.interfaces.estoque.CardexInterfaceFacade;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.GregorianCalendar;
import java.util.List;

/**
 * <AUTHOR>
 */
public class Cardex extends SuperEntidade implements CardexInterfaceFacade {

    public Cardex() throws Exception {
        super();
    }

    public Cardex(Connection connection) throws Exception {
        super(connection);
    }

    private Date retornarDataInicial(Integer codigoEmpresa, Integer codigoProduto, Date dataInicio) throws Exception {
       /*
        * Se a data inicial informada para gerar o cardex for menor que a data em que
        * o produto foi adicionado ao controle de estoque, então considerar a data
        * em que o produto foi adicionado ao controle de estoque como data inicial
        * para gerar o relatório.
        */
        StringBuilder sql = new StringBuilder();
        sql.append(" select min(sit.dataCadastro) as dataCadastro \n ");
        sql.append(" from produtoEstoque_alteracaosit sit \n ");
        sql.append(" inner join produtoEstoque pe on pe.codigo = sit.produtoEstoque \n ");
        sql.append(" where (pe.produto = ").append(codigoProduto).append(") \n");
        sql.append(" and (pe.empresa = ").append(codigoEmpresa).append(") \n");
        sql.append(" and (sit.situacao = 'A') \n");
        PreparedStatement sqlConsultar = con.prepareStatement(sql.toString());
        ResultSet result = sqlConsultar.executeQuery();
        if (result.next()) {
            //return dataInicio.before(result.getDate("dataCadastro")) ? result.getDate("dataCadastro") : dataInicio;
            if (dataInicio.before(result.getDate("dataCadastro")) ){
                // consultar a menor data do perido informado na tela na tabela de compra.. se existir data,
                // então considerar a mesma como sendo data inicial.. caso contrario retornar a data de Configuração do Produto
                Date dataConfiguracao = result.getDate("dataCadastro");
                sql = new StringBuilder();
                sql.append("Select c.dataCadastro from Compra c\n");
                sql.append("INNER JOIN  CompraItens ci ON  ci.compra = c.codigo\n");
                sql.append("WHERE c.dataCadastro");
                sql.append(" BETWEEN '").append(dataInicio).append("'");
                sql.append(" AND '").append(dataConfiguracao).append("'\n");
                sql.append(" AND ci.produto = ").append(codigoProduto);
                sql.append(" ORDER BY c.dataCadastro ASC");
                sqlConsultar = con.prepareStatement(sql.toString());
                result = sqlConsultar.executeQuery();
                if(result.next()){
                    return result.getDate("dataCadastro");
                }else{
                 return dataConfiguracao;
                }
            }else{
                return dataInicio;
            }
        }
        return null;

    }

    private void pesquisarUltimoBalancoParaCalcularSaldoAnterior(Integer codigoEmpresa, Integer codigoProduto, Date dataInicio, Date dataProdutoAdicionadoControleEstoque, CardexVO ultimoBalanco) throws Exception {
        StringBuilder sql = new StringBuilder();
        Calendar dataMaxBalanco = Calendario.getInstance();
        {
            // O último balanço tem que ser menor que a data inicial informada para gerar o Cardex,
            dataMaxBalanco.setTime(dataInicio);
            dataMaxBalanco.add(Calendar.DAY_OF_MONTH, -1);
        }

        sql.append("   select max(b.dataCadastro) as dataBalanco, max(b.codigo) codigoBalanco \n");
        sql.append("from balanco b \n");
        sql.append("inner join balancoItens bi on bi.balanco = b.codigo  \n");
        sql.append("where \n");
        //sql.append("  b.dataCadastro <= '").append(Uteis.getDataJDBC(dataInicio)).append(" 23:59:59'  \n");
        sql.append("  b.dataCadastro <= '").append(Uteis.getDataJDBC(dataMaxBalanco.getTime())).append(" 23:59:59'  \n");
        if (dataInicio.after(dataProdutoAdicionadoControleEstoque))
            sql.append(" and b.dataCadastro > '").append(Uteis.getDataJDBC(dataProdutoAdicionadoControleEstoque)).append("'  \n");
        else
            sql.append(" and b.dataCadastro > '").append(Uteis.getDataJDBC(dataProdutoAdicionadoControleEstoque)).append(" 23:59:59'  \n");

        sql.append(" and (bi.produto = ").append(codigoProduto).append(") \n");
        sql.append(" and (b.empresa = ").append(codigoEmpresa).append(") \n");
        PreparedStatement sqlConsultar = con.prepareStatement(sql.toString());
        ResultSet result = sqlConsultar.executeQuery();
        if (result.next()) {
            ultimoBalanco.setData(result.getTimestamp("dataBalanco"));
            ultimoBalanco.setCodigo(result.getInt("codigoBalanco"));
        }
    }

    private Date retornarDataProdutoFoiAdicionadoAoControleEstoque(Integer codigoEmpresa, Integer codigoProduto) throws Exception {
        // pegar a data da  1² vez em que o produto foi adicionado ao controle de estoque
        StringBuilder sql2 = new StringBuilder();
        sql2.append(" select min(sit.dataCadastro) as data  \n");
        sql2.append(" from produtoEstoque_alteracaoSit sit \n");
        sql2.append(" inner join produtoEstoque pe on pe.codigo = sit.produtoEstoque \n");
        sql2.append(" where (produto = ").append(codigoProduto).append(") \n");
        sql2.append(" and (empresa = ").append(codigoEmpresa).append(") \n");
        sql2.append(" and (sit.situacao = 'A') \n");
        PreparedStatement sqlConsultar2 = con.prepareStatement(sql2.toString());
        ResultSet result2 = sqlConsultar2.executeQuery();
        if (result2.next()) {
            return result2.getDate("data");
        }
        return null;
    }

    private Integer retornarUltimoBalancoProduto(Integer codigoEmpresa, Integer codigoProduto, Date dataInicio, CardexVO ultimoBalanco, Date dataProdutoAdicionadoControleEstoque) throws Exception {
        pesquisarUltimoBalancoParaCalcularSaldoAnterior(codigoEmpresa, codigoProduto, dataInicio, dataProdutoAdicionadoControleEstoque, ultimoBalanco);
        if (ultimoBalanco.getData() != null) {
            StringBuilder sql = new StringBuilder();
            sql.append(" select bi.qtdeBalanco \n");
            sql.append("from balanco b \n");
            sql.append("inner join balancoItens bi on bi.balanco = b.codigo  \n");
            sql.append("where b.codigo = ").append(ultimoBalanco.getCodigo()).append(" \n");
            sql.append(" and b.empresa = ").append(codigoEmpresa).append(" \n");
            sql.append(" and bi.produto = ").append(codigoProduto).append(" \n");
            PreparedStatement sqlConsultar = con.prepareStatement(sql.toString());
            ResultSet result = sqlConsultar.executeQuery();
            if (result.next()) {
                return result.getInt("qtdeBalanco");
            }
        }
        return 0;
    }

    private Integer consultarSaldoAnterior(Integer codigoEmpresa, Integer codigoProduto, Date dataInicioRelatorioCardex, Date dataProdutoAdicionadoControleEstoque) throws Exception {
       /*
        * Regra para calcular o saldo anterior à data inicial informada para gerar o Cardex:
        *  (Quantidade do último balanço ativo antes da data inicial) +
        *    (Soma das movimentações lançadas após o último balanço e antes da data inicial informada
        *    para gerar o Cardex)
        */
        StringBuilder sql = new StringBuilder();
        CardexVO ultimoBalanco = new CardexVO();
        Integer qtdeUltimoBalanco = retornarUltimoBalancoProduto(codigoEmpresa, codigoProduto, dataInicioRelatorioCardex, ultimoBalanco, dataProdutoAdicionadoControleEstoque);
        Calendar dataFimRel = Calendario.getInstance();
        dataFimRel.setTime(dataInicioRelatorioCardex);
        dataFimRel.add(Calendar.DAY_OF_MONTH, -1);
        Date dataIniSaldo;
        {
           /*
            * 1 - Pegar a data do último balanco ativo antes da data inicial informada para gerar o cardex.
            * 2 - Se não houver balanço, então pegar a data em que o produto foi adicionado ao controle de estoque.
            */
            if (ultimoBalanco.getData() != null) {
                Calendar cal = new GregorianCalendar();  
      
                cal.setTime(ultimoBalanco.getData());  
  
                cal.add(Calendar.MILLISECOND, 1);  
  
                dataIniSaldo = cal.getTime();
            } else {
                dataIniSaldo = dataProdutoAdicionadoControleEstoque;
            }
        }

        sql.append(" select sum(totalSaida) +  sum(totalEntrada) as saldo ");
        sql.append(" from (");
        sql.append(retornarSQLCardex(codigoEmpresa, codigoProduto, true, dataIniSaldo, dataFimRel.getTime(), ultimoBalanco));
        sql.append(" )sqlFin ");
        PreparedStatement sqlConsultar = con.prepareStatement(sql.toString());
        ResultSet result = sqlConsultar.executeQuery();
        if (result.next()) {
            return result.getInt("saldo") + qtdeUltimoBalanco;
        }
        return 0;
    }

    private String retornarSQLCardex(Integer codigoEmpresa, Integer codigoProduto, boolean sintetico, Date dataInicio, Date dataFim, CardexVO balancoDesconsiderar) throws Exception {
        dataInicio = retornarDataInicial(codigoEmpresa, codigoProduto, dataInicio);
        StringBuilder sql = new StringBuilder();
        sql.append(" select * from( \n");
        sql.append(getSqlProdutoEstoque(codigoEmpresa, codigoProduto, sintetico, dataInicio, dataFim));
        sql.append(" union all \n");
        sql.append(sintetico ? getSqlCompra(codigoEmpresa, codigoProduto, dataInicio, dataFim) : getSqlCompraAnalitico(codigoEmpresa, codigoProduto, sintetico, dataInicio, dataFim));
        sql.append(" union all \n");
        sql.append(sintetico ? getSqlBalanco(codigoEmpresa, codigoProduto, dataInicio, dataFim, balancoDesconsiderar) : getSqlBalancoAnalitico(codigoEmpresa, codigoProduto, sintetico, dataInicio, dataFim, balancoDesconsiderar));
        sql.append(" union all \n");
        sql.append(sintetico ? getSqlVenda(codigoEmpresa, codigoProduto, dataInicio, dataFim) : getSqlVendaAnalitico(codigoEmpresa, codigoProduto, sintetico, dataInicio, dataFim));
        sql.append(" )sqlFinal \n");
        sql.append(" order by data,operacao \n ");
        return sql.toString();
    }
    private List<CardexVO> montarDadosConsulta(ResultSet result, Integer codigoEmpresa, Integer codigoProduto, Date dataInicio, Date dataFim, boolean sintetico) throws Exception {
        List<CardexVO> lista = new ArrayList<CardexVO>();
        while (result.next()) {
            CardexVO obj = new CardexVO();
            obj.setData(result.getTimestamp("data"));
            if (!sintetico) {
                obj.setDataAux(result.getDate("dataAux"));
                obj.setNomeAux(result.getString("nomeAux"));
            }
            obj.setOperacao(CardexVO.OperacaoCardex.getOperacaoCardex(result.getInt("operacao")));

            if ((obj.getOperacao() != CardexVO.OperacaoCardex.INCLUIDO_CONTROLE_ESTOQUE) &&
                    (obj.getOperacao() != CardexVO.OperacaoCardex.REMOVIDO_CONTROLE_ESTOQUE)) {
                obj.setTotalEntrada(result.getInt("totalEntrada"));
                obj.setTotalSaida(result.getInt("totalSaida"));

            }
            lista.add(obj);
        }
        if (lista.size() == 1) {
            /* Se não foi encontrado movimentações para o período informado,
             * então retornar somente a data em que o produto foi adicionado ao controle de estoque.
             */
            return lista;
        }
        Date dataProdutoAdicionadoControleEstoque = retornarDataProdutoFoiAdicionadoAoControleEstoque(codigoEmpresa, codigoProduto);
        // calcular o saldo atual do produto
        boolean saldoIniJaUtilizado = false;
        Integer saldoAnt = 0;
        Integer saldoAnteriorAoPeriodo = consultarSaldoAnterior(codigoEmpresa, codigoProduto, dataInicio, dataProdutoAdicionadoControleEstoque);
        CardexVO operacaoAnterior = null;
        boolean primeiroRegistroInclusao = false;
        while (!primeiroRegistroInclusao){
            if(lista.get(0).getOperacao() !=  CardexVO.OperacaoCardex.INCLUIDO_CONTROLE_ESTOQUE){
                lista.remove(lista.get(0));
            } else {
                primeiroRegistroInclusao = true;
            }
        }
        for (CardexVO obj : lista) {
            if ((obj.getOperacao() != CardexVO.OperacaoCardex.INCLUIDO_CONTROLE_ESTOQUE) &&
                    (obj.getOperacao() != CardexVO.OperacaoCardex.REMOVIDO_CONTROLE_ESTOQUE)) {
                if ((operacaoAnterior.getOperacao() == CardexVO.OperacaoCardex.INCLUIDO_CONTROLE_ESTOQUE) || (operacaoAnterior.getOperacao() == CardexVO.OperacaoCardex.REMOVIDO_CONTROLE_ESTOQUE)){
                    if (operacaoAnteriorEhIncluidoRemovidoEstoque(operacaoAnterior,obj,codigoEmpresa,codigoProduto)){
                        saldoAnteriorAoPeriodo = 0;
                        saldoAnt = 0;
                    }
                }
                if (!saldoIniJaUtilizado) {
                    obj.setSaldoAnterior(saldoAnteriorAoPeriodo);
                    obj.setSaldoAtual(obj.getSaldoAnterior() + (obj.getTotalEntrada() + obj.getTotalSaida()));
                    saldoIniJaUtilizado = true;
                } else {
                    obj.setSaldoAnterior(saldoAnt);
                    obj.setSaldoAtual(obj.getSaldoAnterior() + (obj.getTotalEntrada() + obj.getTotalSaida()));
                }
                saldoAnt = obj.getSaldoAtual();
            }
            operacaoAnterior = obj;
        }
        return lista;
    }

    private boolean operacaoAnteriorEhIncluidoRemovidoEstoque(CardexVO operacaoIncluidoRemovidoEstoque, CardexVO operacaoAtual, Integer codigoEmpresa, Integer codigoProduto)throws Exception{
        StringBuilder sql = new StringBuilder();
        CardexVO balancoDesconsiderar = new CardexVO();
        balancoDesconsiderar.setCodigo(0);
        sql.append(retornarSQLCardex(codigoEmpresa, codigoProduto, true, operacaoIncluidoRemovidoEstoque.getData(), operacaoAtual.getData(), balancoDesconsiderar));
        PreparedStatement sqlConsultar = con.prepareStatement(sql.toString());
        ResultSet result = sqlConsultar.executeQuery();
        List<CardexVO> lista = new ArrayList<>();
        while (result.next()){
            CardexVO obj = new CardexVO();
            obj.setData(result.getTimestamp("data"));
            obj.setOperacao(CardexVO.OperacaoCardex.getOperacaoCardex(result.getInt("operacao")));
            if ((obj.getOperacao() == operacaoIncluidoRemovidoEstoque.getOperacao()) && (operacaoIncluidoRemovidoEstoque.getData().equals(obj.getData()))) {
                // descartar o registro passado como parâmetro "operacaoIncluidoRemovidoEstoque"
                continue;
            }
            if ((obj.getOperacao() == operacaoAtual.getOperacao()) && (operacaoAtual.getData().equals(obj.getData()))) {
                // descartar o registro passado como parâmetro "operacaoAtual"
                continue;
            }
            lista.add(obj);
        }
        if (UteisValidacao.emptyList(lista)){
            return true;
        }
        CardexVO ultimoRegistro = lista.get(lista.size()-1);
        return ((ultimoRegistro.getOperacao() == CardexVO.OperacaoCardex.INCLUIDO_CONTROLE_ESTOQUE) || (ultimoRegistro.getOperacao() == CardexVO.OperacaoCardex.REMOVIDO_CONTROLE_ESTOQUE));
    }

    private List<CardexVO> consultarCardex(boolean sintetico, Integer codigoEmpresa, Integer codigoProduto, Date dataInicio, Date dataFim) throws Exception {
        StringBuilder sql = new StringBuilder();
        CardexVO balancoDesconsiderar = new CardexVO();
        balancoDesconsiderar.setCodigo(0);
        sql.append(retornarSQLCardex(codigoEmpresa, codigoProduto, sintetico, dataInicio, dataFim, balancoDesconsiderar));
        PreparedStatement sqlConsultar = con.prepareStatement(sql.toString());
        ResultSet result = sqlConsultar.executeQuery();
        return montarDadosConsulta(result, codigoEmpresa, codigoProduto, dataInicio, dataFim, sintetico);
    }

    public List<CardexVO> consultarCardexAnalitico(Integer codigoEmpresa, Integer codigoProduto, Date dataInicio, Date dataFim) throws Exception {
        return consultarCardex(false, codigoEmpresa, codigoProduto, dataInicio, dataFim);
    }

    public List<CardexVO> consultarCardexAgrupadoPorDia(Integer codigoEmpresa, Integer codigoProduto, Date dataInicio, Date dataFim) throws Exception {
        return consultarCardex(true, codigoEmpresa, codigoProduto, dataInicio, dataFim);
    }

    private String getSqlVendaAnalitico(Integer codigoEmpresa, Integer codigoProduto, boolean sintetico, Date dataInicio, Date dataFim) throws Exception {
        StringBuilder sql = new StringBuilder();
        sql.append(" -- script venda  \n");
        sql.append(" select dataLancamento as data,  \n");
        sql.append(CardexVO.OperacaoCardex.VENDA.getCodigo());
        sql.append("        as operacao,          \n");
        sql.append("  (quantidade * -1) as totalSaida,    \n");
        sql.append("  0 as totalEntrada,   \n");
        sql.append("  cast (null as date) as dataAux,   \n");
        sql.append("  coalesce(p.nome, 'CONSUMIDOR') as nomeAux          \n");
        sql.append("  from movProduto mov   \n");
        sql.append("  left join pessoa p on p.codigo = mov.pessoa   \n");
        sql.append(" where  \n");
        sql.append("  dataLancamento BETWEEN '").append(Uteis.getDataJDBCTimestamp(dataInicio))
                .append(" ' ").append("AND '").append(Uteis.getDataJDBC(dataFim)).append(" 23:59:59'  \n");
        sql.append(" and (produto = ").append(codigoProduto).append(") \n");
        sql.append(" and (empresa = ").append(codigoEmpresa).append(") \n");
        sql.append(" and mov.situacao <> 'CA' \n");

        sql.append(" union all    \n");

        sql.append(" -- script cancelamento venda  \n");
        sql.append(" select  dataExclusao as data, \n");
        sql.append(CardexVO.OperacaoCardex.VENDA_CANCELADA.getCodigo());
        sql.append("        as operacao,          \n");
        sql.append("   0 as totalSaida,    \n");
        sql.append("  quantidade as totalEntrada,   \n");
        sql.append("   e.dataVenda as dataAux,   \n");
        sql.append("  p.nome as nomeAux             \n");
        sql.append("  from exclusaoMovProdutoEstoque e    \n");
        sql.append("  left join pessoa p on p.codigo = e.pessoa    \n");
        sql.append(" where  \n");
        sql.append("  dataExclusao BETWEEN '").append(Uteis.getDataJDBCTimestamp(dataInicio))
                .append(" ' ").append("AND '").append(Uteis.getDataJDBC(dataFim)).append(" 23:59:59'  \n");
        sql.append(" and (produto = ").append(codigoProduto).append(") \n");
        sql.append(" and (empresa = ").append(codigoEmpresa).append(") \n");

        sql.append(" union all    \n");

        sql.append(" -- script venda  cancelada após balanço\n");
        sql.append(" select  datavenda as data, \n");
        sql.append(CardexVO.OperacaoCardex.VENDA_CANCELADA_BALANCO.getCodigo());
        sql.append("        as operacao,          \n");
        sql.append("   quantidade as totalSaida,    \n");
        sql.append("  0 as totalEntrada,   \n");
        sql.append("   e.dataExclusao as dataAux,   \n");
        sql.append("  p.nome as nomeAux             \n");
        sql.append("  from exclusaoMovProdutoEstoque e    \n");
        sql.append("  left join pessoa p on p.codigo = e.pessoa    \n");
        sql.append(" where  \n");
        sql.append("  datavenda BETWEEN '").append(Uteis.getDataJDBCTimestamp(dataInicio))
                .append(" ' ").append("AND '").append(Uteis.getDataJDBC(dataFim)).append(" 23:59:59'  \n");
        sql.append(" and (produto = ").append(codigoProduto).append(") \n");
        sql.append(" and (empresa = ").append(codigoEmpresa).append(") \n");

        return sql.toString();

    }

    private String getSqlVenda(Integer codigoEmpresa, Integer codigoProduto, Date dataInicio, Date dataFim) throws Exception {
        StringBuilder sql = new StringBuilder();
        sql.append(" -- script venda \n");
        sql.append(" select  cast (sql.dataLancamento as timestamp) as data, \n");
        sql.append(CardexVO.OperacaoCardex.VENDA.getCodigo());
        sql.append("        as operacao,          \n");
        sql.append(" sum(sql.quantidade) * -1 as totalSaida, \n");
        sql.append("        0 as totalEntrada \n");
        sql.append(" from (select dataLancamento, pessoa, empresa, produto, coalesce(coalesce(vendaavulsa,contrato),0), quantidade  \n");
        sql.append("       from movproduto \n" );
        sql.append("       where  \n");
        sql.append("       dataLancamento BETWEEN '").append(Uteis.getDataJDBCTimestamp(dataInicio))
                .append(" ' ").append("AND '").append(Uteis.getDataJDBC(dataFim)).append(" 23:59:59'  \n");
        sql.append("      and (produto = ").append(codigoProduto).append(") \n");
        sql.append("      and (empresa = ").append(codigoEmpresa).append(") \n");
        sql.append("      and coalesce(movprodutooriginal,0) = 0\n");
        sql.append("      group by 1,2,3,4,5,6)sql \n");
        //sql.append(" and movProduto.situacao <> 'CA'\n");
        sql.append(" group by cast (dataLancamento as timestamp) \n");
        sql.append("  \n");
        sql.append("  \n");
        sql.append(" union all \n");
        sql.append("  \n");
        sql.append(" -- script cancelamento venda \n");
        sql.append(" select cast (dataExclusao as timestamp) as data, \n");
        sql.append(CardexVO.OperacaoCardex.VENDA_CANCELADA.getCodigo());
        sql.append("         as operacao,          \n");
        sql.append("        0 as totalSaida, \n");
        sql.append(" sum(quantidade) as totalEntrada      \n");
        sql.append(" from exclusaoMovProdutoEstoque \n");
        sql.append(" where   \n");
        sql.append("  dataExclusao BETWEEN '").append(Uteis.getDataJDBCTimestamp(dataInicio))
                .append(" ' ").append("AND '").append(Uteis.getDataJDBC(dataFim)).append(" 23:59:59'  \n");
        sql.append(" and (produto = ").append(codigoProduto).append(") \n");
        sql.append(" and (empresa = ").append(codigoEmpresa).append(") \n");
        sql.append(" group by cast (dataExclusao as timestamp) \n");
        sql.append(" union all    \n");

        sql.append(" -- script venda  cancelada após balanço\n");
        sql.append(" select   cast (datavenda as timestamp), \n");
        sql.append(CardexVO.OperacaoCardex.VENDA_CANCELADA_BALANCO.getCodigo());
        sql.append("        as operacao,          \n");
        sql.append("   sum(quantidade) * -1 as totalSaida,    \n");
        sql.append("  0 as totalEntrada   \n");
        sql.append("  from exclusaoMovProdutoEstoque e    \n");
        sql.append("  left join pessoa p on p.codigo = e.pessoa    \n");
        sql.append(" where  \n");
        sql.append("  datavenda BETWEEN '").append(Uteis.getDataJDBCTimestamp(dataInicio))
                .append(" ' ").append("AND '").append(Uteis.getDataJDBC(dataFim)).append(" 23:59:59'  \n");
        sql.append(" and (produto = ").append(codigoProduto).append(") \n");
        sql.append(" and (empresa = ").append(codigoEmpresa).append(") \n");
        sql.append(" group by cast (datavenda as timestamp) \n");
        return sql.toString();
    }


    private String getSqlBalancoAnalitico(Integer codigoEmpresa, Integer codigoProduto, boolean sintetico, Date dataInicio, Date dataFim, CardexVO balancoDesconsiderar) throws Exception {
        StringBuilder sql = new StringBuilder();
        sql.append(" -- script para Balanço \n");
        sql.append(" select  b.dataCadastro as data, \n");
        sql.append(CardexVO.OperacaoCardex.BALANCO.getCodigo());
        sql.append("        as operacao,          \n");
        sql.append(" case when ((bi.qtdeBalanco - qtdeEstoqueAnterior) < 0) then  (bi.qtdeBalanco - qtdeEstoqueAnterior)  end as totalSaida,  \n");
        sql.append(" case when ((bi.qtdeBalanco - qtdeEstoqueAnterior) > 0) then  (bi.qtdeBalanco - qtdeEstoqueAnterior)  end as totalEntrada,  \n");
        sql.append(" cast (null as date) as dataAux,  \n");
        sql.append(" ''  as nomeAux  \n");
        sql.append(" from balancoItens bi   \n");
        sql.append(" inner join balanco b on b.codigo = bi.balanco   \n");
        sql.append(" where  \n");
        sql.append("  b.dataCadastro BETWEEN '").append(Uteis.getDataJDBCTimestamp(dataInicio))
                .append(" ' ").append("AND '").append(Uteis.getDataJDBC(dataFim)).append(" 23:59:59'  \n");
        sql.append(" and (bi.produto = ").append(codigoProduto).append(") \n");
        sql.append(" and (b.empresa = ").append(codigoEmpresa).append(") \n");
        sql.append(" and  b.codigo <> ").append(balancoDesconsiderar.getCodigo()).append(" \n");

        sql.append(" union all  \n");

        sql.append(" -- script para balanço cancelado  \n");
        sql.append(" select  b.dataCancelamento as data,  \n");
        sql.append(CardexVO.OperacaoCardex.BALANCO_CANCELADO.getCodigo());
        sql.append("        as operacao,          \n");
        sql.append("  case when ((bi.qtdeBalanco - qtdeEstoqueAnterior) > 0) then  ((bi.qtdeBalanco - qtdeEstoqueAnterior)*-1)  end as totalSaida,  \n");
        sql.append("  case when ((bi.qtdeBalanco - qtdeEstoqueAnterior) < 0) then  ((bi.qtdeBalanco - qtdeEstoqueAnterior)*-1)  end as totalEntrada,  \n");
        sql.append("  cast (null as date) as dataAux,  \n");
        sql.append("  ''  as nomeAux  \n");
        sql.append("  from balancoItens bi   \n");
        sql.append("  inner join balanco b on b.codigo = bi.balanco   \n");
        sql.append(" where  \n");
        sql.append("  b.dataCancelamento BETWEEN '").append(Uteis.getDataJDBCTimestamp(dataInicio))
                .append(" ' ").append("AND '").append(Uteis.getDataJDBC(dataFim)).append(" 23:59:59'  \n");
        sql.append(" and (bi.produto = ").append(codigoProduto).append(") \n");
        sql.append(" and (b.empresa = ").append(codigoEmpresa).append(") \n");

        return sql.toString();
    }

    private String getSqlBalanco(Integer codigoEmpresa, Integer codigoProduto, Date dataInicio, Date dataFim, CardexVO balancoDesconsiderar) throws Exception {
        StringBuilder sql = new StringBuilder();
        sql.append(" -- script para Balanço \n");
        sql.append(" select sql2.data, \n");
        sql.append(CardexVO.OperacaoCardex.BALANCO.getCodigo());
        sql.append("        as operacao,          \n");
        sql.append("        sum(totalSaida) as totalSaida, \n");
        sql.append("        sum(totalEntrada) as totalEntrada \n");
        sql.append(" from(        \n");
        sql.append(" select sql.data,  \n");
        sql.append(" 	case  \n");
        sql.append(" 	 when tipo = 'Saida' then sum(total) \n");
        sql.append(" 	end as totalSaida, \n");
        sql.append(" 	case  \n");
        sql.append("          when tipo = 'Entrada' then sum(total) \n");
        sql.append(" 	end as totalEntrada \n");
        sql.append(" from( \n");
        sql.append(" select  cast (b.dataCadastro  as timestamp) as data, \n");
        sql.append("        cast ('Entrada' as varchar(50)) as tipo, \n");
        sql.append("        sum((bi.qtdeBalanco - qtdeEstoqueAnterior)) total \n");
        sql.append(" from balancoItens bi \n");
        sql.append(" inner join balanco b on b.codigo = bi.balanco \n");
        sql.append(" where  b.codigo <> ").append(balancoDesconsiderar.getCodigo()).append("\n");
        sql.append(" and b.dataCadastro BETWEEN '").append(Uteis.getDataJDBCTimestamp(dataInicio))
                .append(" ' ").append("AND '").append(Uteis.getDataJDBC(dataFim)).append(" 23:59:59'  \n");
        sql.append(" and (bi.produto = ").append(codigoProduto).append(") \n");
        sql.append(" and (b.empresa = ").append(codigoEmpresa).append(") \n");
        sql.append("         and ((bi.qtdeBalanco - qtdeEstoqueAnterior) > 0) \n");
        sql.append(" group by cast ('Entrada' as varchar(50)), cast (b.dataCadastro  as timestamp) \n");
        sql.append("  \n");
        sql.append(" union all \n");
        sql.append("  \n");
        sql.append(" select cast (b.dataCadastro  as timestamp) as data, \n");
        sql.append("        cast ('Saida' as varchar(50)) as tipo, \n");
        sql.append("        sum((bi.qtdeBalanco - qtdeEstoqueAnterior)) total \n");
        sql.append(" from balancoItens bi \n");
        sql.append(" inner join balanco b on b.codigo = bi.balanco \n");
        sql.append(" where 	  \n");
        sql.append("  b.dataCadastro BETWEEN '").append(Uteis.getDataJDBCTimestamp(dataInicio))
                .append(" ' ").append("AND '").append(Uteis.getDataJDBC(dataFim)).append(" 23:59:59'  \n");
        sql.append(" and (bi.produto = ").append(codigoProduto).append(") \n");
        sql.append(" and (b.empresa = ").append(codigoEmpresa).append(") \n");
        sql.append("         and ((bi.qtdeBalanco - qtdeEstoqueAnterior) < 0) \n");
        sql.append(" group by cast ('Saida' as varchar(50)),  cast (b.dataCadastro  as timestamp) \n");
        sql.append("  \n");
        sql.append("  \n");
        sql.append(" )sql \n");
        sql.append(" group by sql.data, sql.tipo \n");
        sql.append(" )sql2 \n");
        sql.append(" group by sql2.data \n");
        sql.append("  \n");
        sql.append(" union all \n");
        sql.append(" -- script para balanço cancelado \n");
        sql.append(" select sql2.data, \n");
        sql.append(CardexVO.OperacaoCardex.BALANCO_CANCELADO.getCodigo());
        sql.append("        as operacao,          \n");
        sql.append("        sum(totalSaida) as totalSaida, \n");
        sql.append("        sum(totalEntrada) as totalEntrada \n");
        sql.append(" from(        \n");
        sql.append(" select sql.data,  \n");
        sql.append(" 	case  \n");
        sql.append(" 	 when tipo = 'Saida' then sum(total) \n");
        sql.append(" 	end as totalSaida, \n");
        sql.append(" 	case  \n");
        sql.append("          when tipo = 'Entrada' then sum(total) \n");
        sql.append(" 	end as totalEntrada \n");
        sql.append(" from( \n");
        sql.append(" select cast (b.dataCancelamento  as timestamp) as data, \n");
        sql.append("        cast ('Saida' as varchar(50)) as tipo, \n");
        sql.append("        sum((bi.qtdeBalanco - qtdeEstoqueAnterior)) * -1 total \n");
        sql.append(" from balancoItens bi \n");
        sql.append(" inner join balanco b on b.codigo = bi.balanco \n");
        sql.append(" where   \n");
        sql.append("  b.dataCancelamento BETWEEN '").append(Uteis.getDataJDBCTimestamp(dataInicio))
                .append(" ' ").append("AND '").append(Uteis.getDataJDBC(dataFim)).append(" 23:59:59'  \n");
        sql.append(" and (bi.produto = ").append(codigoProduto).append(") \n");
        sql.append(" and (b.empresa = ").append(codigoEmpresa).append(") \n");
        sql.append("         and ((bi.qtdeBalanco - qtdeEstoqueAnterior) > 0) \n");
        sql.append(" group by cast ('Saida' as varchar(50)),  cast (b.dataCancelamento  as timestamp) \n");
        sql.append("  \n");
        sql.append(" union all \n");
        sql.append("  \n");
        sql.append(" select  cast (b.dataCancelamento  as timestamp) as data, \n");
        sql.append("        cast ('Entrada' as varchar(50)) as tipo, \n");
        sql.append("        sum((bi.qtdeBalanco - qtdeEstoqueAnterior)) * -1 total \n");
        sql.append(" from balancoItens bi \n");
        sql.append(" inner join balanco b on b.codigo = bi.balanco \n");
        sql.append(" where     \n");
        sql.append("  b.dataCancelamento BETWEEN '").append(Uteis.getDataJDBCTimestamp(dataInicio))
                .append(" ' ").append("AND '").append(Uteis.getDataJDBC(dataFim)).append(" 23:59:59'  \n");
        sql.append(" and (bi.produto = ").append(codigoProduto).append(") \n");
        sql.append(" and (b.empresa = ").append(codigoEmpresa).append(") \n");
        sql.append("         and ((bi.qtdeBalanco - qtdeEstoqueAnterior) < 0) \n");
        sql.append(" group by cast ('Entrada' as varchar(50)), cast (b.dataCancelamento  as timestamp) \n");
        sql.append("  \n");
        sql.append("  \n");
        sql.append(" )sql \n");
        sql.append(" group by sql.data, sql.tipo \n");
        sql.append(" )sql2 \n");
        sql.append(" group by sql2.data \n");

        return sql.toString();
    }

    private String getSqlCompraAnalitico(Integer codigoEmpresa, Integer codigoProduto, boolean sintetico, Date dataInicio, Date dataFim) throws Exception {
        StringBuilder sql = new StringBuilder();
        sql.append(" select  c.dataCadastro as data,  \n");
        sql.append(CardexVO.OperacaoCardex.COMPRA.getCodigo());
        sql.append("        as operacao,          \n");
        sql.append(" 0 as totalSaida,  \n");
        sql.append(" quantidade as totalEntrada, \n");
        sql.append(" cast (null as date) as dataAux, \n");
        sql.append(" p.nome as nomeAux  \n");
        sql.append(" from compraItens ci  \n");
        sql.append(" inner join compra c on c.codigo = ci.compra  \n");
        sql.append(" inner join fornecedor f on f.codigo = c.fornecedor \n");
        sql.append(" left join pessoa p on p.codigo = f.pessoa \n");
        sql.append(" where  \n");
        sql.append("  c.dataCadastro BETWEEN '").append(Uteis.getDataJDBCTimestamp(dataInicio))
                .append(" ' ").append("AND '").append(Uteis.getDataJDBC(dataFim)).append(" 23:59:59'  \n");
        sql.append(" and (ci.produto = ").append(codigoProduto).append(") \n");
        sql.append(" and (c.empresa = ").append(codigoEmpresa).append(") \n");

        sql.append(" union all  \n");

        sql.append(" select  c.dataCancelamento as data,  \n");
        sql.append(CardexVO.OperacaoCardex.COMPRA_CANCELADA.getCodigo());
        sql.append("        as operacao,          \n");
        sql.append(" (quantidade * -1) as totalSaida,     \n");
        sql.append(" 0 as totalEntrada,    \n");
        sql.append(" cast (null as date) as dataAux,     \n");
        sql.append(" p.nome as nomeAux     \n");
        sql.append(" from compraItens ci      \n");
        sql.append(" inner join compra c on c.codigo = ci.compra     \n");
        sql.append(" inner join fornecedor f on f.codigo = c.fornecedor       \n");
        sql.append(" left join pessoa p on p.codigo = f.pessoa    \n");
        sql.append(" where  \n");
        sql.append("  c.dataCancelamento BETWEEN '").append(Uteis.getDataJDBCTimestamp(dataInicio))
                .append(" ' ").append("AND '").append(Uteis.getDataJDBC(dataFim)).append(" 23:59:59'  \n");
        sql.append(" and (ci.produto = ").append(codigoProduto).append(") \n");
        sql.append(" and (c.empresa = ").append(codigoEmpresa).append(") \n");

        return sql.toString();

    }

    private String getSqlCompra(Integer codigoEmpresa, Integer codigoProduto, Date dataInicio, Date dataFim) throws Exception {
        StringBuilder sql = new StringBuilder();
        sql.append(" select data, operacao, sum(totalSaida) as totalSaida, sum(totalEntrada) as totalEntrada \n");
        sql.append(" from( \n");
        sql.append(" select  cast (c.dataCadastro as timestamp) as data, \n");
        sql.append(CardexVO.OperacaoCardex.COMPRA.getCodigo());
        sql.append("        as operacao,          \n");
        sql.append("        0 as totalSaida, \n");
        sql.append("        quantidade as totalEntrada \n");
        sql.append(" from compraItens ci \n");
        sql.append(" inner join compra c on c.codigo = ci.compra \n");
        sql.append(" where  \n");
        sql.append("  c.dataCadastro BETWEEN '").append(Uteis.getDataJDBCTimestamp(dataInicio))
                .append(" ' ").append("AND '").append(Uteis.getDataJDBC(dataFim)).append(" 23:59:59'  \n");
        sql.append(" and (ci.produto = ").append(codigoProduto).append(") \n");
        sql.append(" and (c.empresa = ").append(codigoEmpresa).append(") \n");
        sql.append(" )sql \n");
        sql.append(" group by data, operacao \n");
        sql.append("  \n");
        sql.append(" union all \n");
        sql.append("  \n");
        sql.append(" select data, operacao, sum(totalSaida) * -1 as totalSaida, sum(totalEntrada) as totalEntrada \n");
        sql.append(" from( \n");
        sql.append(" select cast (c.dataCancelamento as timestamp) as data, \n");
        sql.append(CardexVO.OperacaoCardex.COMPRA_CANCELADA.getCodigo());
        sql.append("        as operacao,          \n");
        sql.append("        quantidade as totalSaida, \n");
        sql.append("        0 as totalEntrada \n");
        sql.append(" from compraItens ci \n");
        sql.append(" inner join compra c on c.codigo = ci.compra \n");
        sql.append(" where  \n");
        sql.append("  c.dataCancelamento BETWEEN '").append(Uteis.getDataJDBCTimestamp(dataInicio))
                .append(" ' ").append("AND '").append(Uteis.getDataJDBC(dataFim)).append(" 23:59:59'  \n");
        sql.append(" and (ci.produto = ").append(codigoProduto).append(") \n");
        sql.append(" and (c.empresa = ").append(codigoEmpresa).append(") \n");
        sql.append(" )sql \n");
        sql.append(" group by data, operacao \n");
        return sql.toString();
    }

    private String getSqlProdutoEstoque_1_vez_Adicionado(Integer codigoEmpresa, Integer codigoProduto, boolean sintetico, Date dataInicio, Date dataFim) throws Exception {
        StringBuilder sql = new StringBuilder();
        // retornar a 1² vez em que o produto foi adicionado ao controle de estoque
        if (sintetico)
            sql.append(" select cast (min(sit.dataCadastro) as timestamp) as data,  \n");
        else
            sql.append(" select min(sit.dataCadastro) as data,  \n");
        sql.append(CardexVO.OperacaoCardex.INCLUIDO_CONTROLE_ESTOQUE.getCodigo());
        sql.append("       as operacao,          \n");
        sql.append("        0 as totalSaida, \n");
        sql.append("        0 as totalEntrada \n");
        sql.append(" from produtoEstoque_alteracaoSit sit \n");
        sql.append(" inner join produtoEstoque pe on pe.codigo = sit.produtoEstoque \n");
        sql.append(" where (produto = ").append(codigoProduto).append(") \n");
        sql.append(" and (empresa = ").append(codigoEmpresa).append(") \n");
        sql.append(" and (sit.situacao = 'A') \n");
        return sql.toString();
    }

    private String getSqlProdutoEstoque(Integer codigoEmpresa, Integer codigoProduto, boolean sintetico, Date dataInicio, Date dataFim) throws Exception {
        StringBuilder sql = new StringBuilder();
        sql.append(" select data, operacao, sum(totalSaida) as totalSaida, sum(totalEntrada) as totalEntrada \n");
        if (!sintetico) {
            sql.append(" ,cast (null as date) as dataAux, ' ' as nomeAux \n");
        }
        sql.append(" from (");
        // adicionar a 1² vez em que o produto foi adicionado ao controle de estoque
        sql.append(getSqlProdutoEstoque_1_vez_Adicionado(codigoEmpresa, codigoProduto, sintetico, dataInicio, dataFim));
        sql.append(" union all ");
        sql.append(" select data, operacao, sum(totalSaida) as totalSaida, sum(totalEntrada) as totalEntrada \n");
        sql.append(" from( \n");
        sql.append(" select ").append(sintetico ? " cast (sit.dataCadastro as timestamp) as data, \n" : " sit.dataCadastro as data, \n");
        sql.append(CardexVO.OperacaoCardex.INCLUIDO_CONTROLE_ESTOQUE.getCodigo());
        sql.append("       as operacao,          \n");
        sql.append("        0 as totalSaida, \n");
        sql.append("        0 as totalEntrada \n");
        sql.append(" from produtoEstoque_alteracaoSit sit \n");
        sql.append(" inner join produtoEstoque pe on pe.codigo = sit.produtoEstoque \n");
        sql.append(" where  \n");
        sql.append("  sit.dataCadastro BETWEEN '").append(Uteis.getDataJDBCTimestamp(dataInicio))
                .append(" ' ").append("AND '").append(Uteis.getDataJDBC(dataFim)).append(" 23:59:59'  \n");
        sql.append(" and (produto = ").append(codigoProduto).append(") \n");
        sql.append(" and (empresa = ").append(codigoEmpresa).append(") \n");
        sql.append("         and (sit.situacao = 'A') \n");
        sql.append(" )sql \n");
        sql.append(" group by data, operacao \n");
        sql.append("  \n");
        sql.append(" union all \n");
        sql.append("  \n");
        sql.append(" select data, operacao, sum(totalSaida) as totalSaida, sum(totalEntrada) as totalEntrada \n");
        sql.append(" from( \n");
        sql.append(" select ").append(sintetico ? " cast (sit.dataCadastro as timestamp) as data, \n" : " sit.dataCadastro  as data, \n");
        sql.append(CardexVO.OperacaoCardex.REMOVIDO_CONTROLE_ESTOQUE.getCodigo());
        sql.append("         as operacao,          \n");
        sql.append("        0 as totalSaida, \n");
        sql.append("        0 as totalEntrada \n");
        sql.append(" from produtoEstoque_alteracaoSit sit \n");
        sql.append(" inner join produtoEstoque pe on pe.codigo = sit.produtoEstoque \n");
        sql.append(" where  \n");
        sql.append("  sit.dataCadastro BETWEEN '").append(Uteis.getDataJDBCTimestamp(dataInicio))
                .append(" ' ").append("AND '").append(Uteis.getDataJDBC(dataFim)).append(" 23:59:59'  \n");
        sql.append(" and (produto = ").append(codigoProduto).append(") \n");
        sql.append(" and (empresa = ").append(codigoEmpresa).append(") \n");
        sql.append("         and (sit.situacao = 'C') \n");
        sql.append(" )sql         \n");
        sql.append(" group by data, operacao \n");
        sql.append(") sql group by data, operacao \n");
        return sql.toString();
    }

    private String retornarSqlDetalhesBalancoAtivo(Integer codigoEmpresa, Integer codigoProduto, CardexVO cardexVO) throws Exception {
        StringBuilder sql = new StringBuilder();
        sql.append(" select b.codigo,  b.dataCadastro as data, (bi.qtdeBalanco - bi.qtdeEstoqueAnterior) total, '' as nome \n");
        sql.append(" from balanco b \n");
        sql.append(" inner join balancoItens bi on bi.balanco = b.codigo \n");
        sql.append(" where  \n");
        sql.append("  b.dataCadastro BETWEEN '").append(Uteis.getDataJDBCTimestamp(cardexVO.getData()))
                .append("' ").append("AND '").append(Uteis.getDataJDBC(cardexVO.getData())).append(" 23:59:59'  \n");
        sql.append(" and (bi.produto = ").append(codigoProduto).append(") \n");
        sql.append(" and (b.empresa = ").append(codigoEmpresa).append(") \n");
        return sql.toString();
    }

    private String retornarSqlDetalhesBalancoCancelado(Integer codigoEmpresa, Integer codigoProduto, CardexVO cardexVO) throws Exception {
        StringBuilder sql = new StringBuilder();
        sql.append(" select b.codigo,  b.dataCancelamento as data, ((bi.qtdeBalanco - bi.qtdeEstoqueAnterior) *-1) total, '' as nome \n");
        sql.append(" from balanco b \n");
        sql.append(" inner join balancoItens bi on bi.balanco = b.codigo \n");
        sql.append(" where  \n");
        sql.append("  b.dataCancelamento BETWEEN '").append(Uteis.getDataJDBCTimestamp(cardexVO.getData()))
                .append("' ").append("AND '").append(Uteis.getDataJDBC(cardexVO.getData())).append(" 23:59:59'  \n");
        sql.append(" and (bi.produto = ").append(codigoProduto).append(") \n");
        sql.append(" and (b.empresa = ").append(codigoEmpresa).append(") \n");
        return sql.toString();
    }

    private String retornarSqlDetalhesCompraAtiva(Integer codigoEmpresa, Integer codigoProduto, CardexVO cardexVO) throws Exception {
        StringBuilder sql = new StringBuilder();
        sql.append(" select c.dataCadastro as data, c.codigo, ci.quantidade as total, p.nome \n");
        sql.append(" from compra c \n");
        sql.append(" inner join fornecedor f on f.codigo = c.fornecedor \n");
        sql.append(" left join pessoa p on p.codigo = f.pessoa      \n");
        sql.append(" inner join compraItens ci on ci.compra = c.codigo \n");
        sql.append(" where  \n");
        sql.append("  c.dataCadastro BETWEEN '").append(Uteis.getDataJDBCTimestamp(cardexVO.getData()))
                .append("' ").append("AND '").append(Uteis.getDataJDBC(cardexVO.getData())).append(" 23:59:59'  \n");
        sql.append(" and (ci.produto = ").append(codigoProduto).append(") \n");
        sql.append(" and (c.empresa = ").append(codigoEmpresa).append(") \n");
        return sql.toString();
    }

    private String retornarSqlDetalhesCompraCancelada(Integer codigoEmpresa, Integer codigoProduto, CardexVO cardexVO) throws Exception {
        StringBuilder sql = new StringBuilder();
        sql.append(" select c.dataCancelamento as data, c.codigo, (ci.quantidade *-1) as total, p.nome \n");
        sql.append(" from compra c \n");
        sql.append(" inner join fornecedor f on f.codigo = c.fornecedor \n");
        sql.append(" left join pessoa p on p.codigo = f.pessoa      \n");
        sql.append(" inner join compraItens ci on ci.compra = c.codigo \n");
        sql.append(" where  \n");
        sql.append("  c.dataCancelamento BETWEEN '").append(Uteis.getDataJDBCTimestamp(cardexVO.getData()))
                .append("' ").append("AND '").append(Uteis.getDataJDBC(cardexVO.getData())).append(" 23:59:59'  \n");
        sql.append(" and (ci.produto = ").append(codigoProduto).append(") \n");
        sql.append(" and (c.empresa = ").append(codigoEmpresa).append(") \n");
        return sql.toString();
    }

    private String retornarSqlDetalhesVendaAtiva(Integer codigoEmpresa, Integer codigoProduto, CardexVO cardexVO) throws Exception {
        StringBuilder sql = new StringBuilder();
        sql.append(" select p.codigo as codigoPessoa, mov.dataLancamento as data, mov.codigo, (mov.quantidade * -1) total,  p.nome, v.nomecomprador, v.codigo as codigovenda,  v.tipocomprador\n");
        sql.append(" from movProduto mov \n");
        sql.append(" left join pessoa p on p.codigo = mov.pessoa \n");
        sql.append(" left join vendaavulsa v on v.codigo = mov.vendaavulsa\n");
        sql.append(" where  \n");
        sql.append(" mov.dataLancamento = '").append(Uteis.getDataJDBCTimestamp(cardexVO.getData())).append("'\n");
        sql.append(" and (mov.produto = ").append(codigoProduto).append(") \n");
        sql.append(" and (mov.empresa = ").append(codigoEmpresa).append(") \n");
        return sql.toString();
    }

    private String retornarSqlDetalhesVendaCancelada(Integer codigoEmpresa, Integer codigoProduto, CardexVO cardexVO) throws Exception {
        StringBuilder sql = new StringBuilder();
        sql.append(" select p.codigo as codigoPessoa,  mov.dataExclusao as data, mov.codigo, (mov.quantidade) total,  p.nome, mov.dataVenda as dataAux \n");
        sql.append(" from exclusaoMovProdutoEstoque mov \n");
        sql.append(" left join pessoa p on p.codigo = mov.pessoa \n");
        sql.append(" where  \n");
        sql.append("  mov.dataExclusao BETWEEN '").append(Uteis.getDataJDBCTimestamp(cardexVO.getData()))
                .append("' ").append("AND '").append(Uteis.getDataJDBC(cardexVO.getData())).append(" 23:59:59'  \n");
        sql.append(" and (mov.produto = ").append(codigoProduto).append(") \n");
        sql.append(" and (mov.empresa = ").append(codigoEmpresa).append(") \n");
        return sql.toString();
    }


    private String retornarSqlDetalhesVendaCanceladaBalanco(Integer codigoEmpresa, Integer codigoProduto, CardexVO cardexVO) throws Exception {
        StringBuilder sql = new StringBuilder();
        sql.append(" select p.codigo as codigoPessoa,  mov.dataVenda as data, mov.codigo, (mov.quantidade * -1) total,  p.nome, mov.dataExclusao as dataAux \n");
        sql.append(" from exclusaoMovProdutoEstoque mov \n");
        sql.append(" left join pessoa p on p.codigo = mov.pessoa \n");
        sql.append(" where  \n");
        sql.append("  mov.dataVenda BETWEEN '").append(Uteis.getDataJDBCTimestamp(cardexVO.getData()))
                .append("' ").append("AND '").append(Uteis.getDataJDBC(cardexVO.getData())).append(" 23:59:59'  \n");
        sql.append(" and (mov.produto = ").append(codigoProduto).append(") \n");
        sql.append(" and (mov.empresa = ").append(codigoEmpresa).append(") \n");
        return sql.toString();
    }

    public List<CardexVO> consultarDetalhesOperacao(Integer codigoEmpresa, Integer codigoProduto, CardexVO cardexVO) throws Exception {
        List<CardexVO> lista = new ArrayList<CardexVO>();
        String sql = "";
        if (cardexVO.getOperacao() == CardexVO.OperacaoCardex.BALANCO)
            sql = retornarSqlDetalhesBalancoAtivo(codigoEmpresa, codigoProduto, cardexVO);
        else if (cardexVO.getOperacao() == CardexVO.OperacaoCardex.BALANCO_CANCELADO)
            sql = retornarSqlDetalhesBalancoCancelado(codigoEmpresa, codigoProduto, cardexVO);
        else if (cardexVO.getOperacao() == CardexVO.OperacaoCardex.COMPRA)
            sql = retornarSqlDetalhesCompraAtiva(codigoEmpresa, codigoProduto, cardexVO);
        else if (cardexVO.getOperacao() == CardexVO.OperacaoCardex.COMPRA_CANCELADA)
            sql = retornarSqlDetalhesCompraCancelada(codigoEmpresa, codigoProduto, cardexVO);
        else if (cardexVO.getOperacao() == CardexVO.OperacaoCardex.VENDA)
            sql = retornarSqlDetalhesVendaAtiva(codigoEmpresa, codigoProduto, cardexVO);
        else if (cardexVO.getOperacao() == CardexVO.OperacaoCardex.VENDA_CANCELADA)
            sql = retornarSqlDetalhesVendaCancelada(codigoEmpresa, codigoProduto, cardexVO);
        else if (cardexVO.getOperacao() == CardexVO.OperacaoCardex.VENDA_CANCELADA_BALANCO)
            sql = retornarSqlDetalhesVendaCanceladaBalanco(codigoEmpresa, codigoProduto, cardexVO);

        PreparedStatement sqlConsultar = con.prepareStatement(sql);
        ResultSet result = sqlConsultar.executeQuery();
        while (result.next()) {
            CardexVO obj = new CardexVO();
            obj.setOperacao(cardexVO.getOperacao());
            obj.setCodigo(result.getInt("codigo"));
            obj.setData(result.getTimestamp("data"));
            obj.setNomeAux(result.getString("nome"));
            if (cardexVO.getOperacao() == CardexVO.OperacaoCardex.VENDA_CANCELADA
            || cardexVO.getOperacao() == CardexVO.OperacaoCardex.VENDA_CANCELADA_BALANCO) {
                obj.setDataAux(result.getTimestamp("dataAux"));
            }

            if (cardexVO.getOperacao() == CardexVO.OperacaoCardex.VENDA_CANCELADA
                    || cardexVO.getOperacao() == CardexVO.OperacaoCardex.VENDA_CANCELADA_BALANCO) {
                obj.setCodigoPessoa(result.getInt("codigoPessoa"));
                obj.setNomeAux("VENDA CONSUMIDOR");
            }

            if (cardexVO.getOperacao() == CardexVO.OperacaoCardex.VENDA) {
                obj.setCodigoPessoa(result.getInt("codigoPessoa"));
                String tipoConsumidor = result.getString("tipocomprador");
                if (!UteisValidacao.emptyString(obj.getNomeAux()) && !"CN".equals(tipoConsumidor)) {
                    if ("CO".equals(tipoConsumidor)) {
                        obj.setNomeAux("COLABORADOR - " + result.getString("nomecomprador"));
                    } else {
                        obj.setNomeAux("CLIENTE - " + result.getString("nomecomprador"));
                    }
                    obj.setVendaavulsa(result.getInt("codigovenda"));
                } else {
                    obj.setNomeAux("VENDA CONSUMIDOR");
                }
            }

            if (result.getInt("total") > 0)
                obj.setTotalEntrada(result.getInt("total"));
            else
                obj.setTotalSaida(result.getInt("total"));

            lista.add(obj);
        }
        return lista;
    }

    public String consultarJSON(Integer empresa) throws Exception {
        String sql = "SELECT \n" +
                "  cli.codigo, p.nome, p.datanasc, situacao, matricula, cat.nome AS categoria\n" +
                "FROM cliente cli \n" +
                "  INNER JOIN pessoa p ON cli.pessoa = p.codigo LEFT JOIN categoria cat ON cli.categoria = cat.codigo;";

        PreparedStatement sqlConsultar = con.prepareStatement(sql);

        ResultSet rs = sqlConsultar.executeQuery();

        StringBuilder json = new StringBuilder();
        json.append("{\"aaData\":[");
        boolean dados = false;
        while (rs.next()) {
            dados = true;
            json.append("[\"").append(rs.getString("codigo")).append("\",");
            json.append("\"").append(rs.getString("nome")).append("\",");
            json.append("\"").append(rs.getDate("datanasc").getTime()).append("\",");
            json.append("\"").append(rs.getString("situacao")).append("\",");
            json.append("\"").append(rs.getString("matricula")).append("\",");
            json.append("\"").append(rs.getString("categoria")).append("\"],");
        }
        if(dados) {
            json.deleteCharAt(json.toString().length() - 1);
        }
        json.append("]}");
        return json.toString();
    }
}

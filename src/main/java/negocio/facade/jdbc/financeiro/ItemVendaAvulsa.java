package negocio.facade.jdbc.financeiro;

import br.com.pactosolucoes.estudio.dao.Pacote;
import br.com.pactosolucoes.estudio.modelo.PacoteVO;
import negocio.comuns.estoque.ProdutoEstoqueVO;
import negocio.comuns.financeiro.ItemVendaAvulsaVO;
import negocio.comuns.plano.DescontoVO;
import negocio.comuns.plano.ProdutoVO;
import negocio.comuns.utilitarias.Calendario;
import negocio.comuns.utilitarias.ConsistirException;
import negocio.comuns.utilitarias.Uteis;
import negocio.facade.jdbc.arquitetura.SuperEntidade;
import negocio.facade.jdbc.estoque.BalancoItens;
import negocio.facade.jdbc.estoque.ProdutoEstoque;
import negocio.facade.jdbc.plano.Desconto;
import negocio.facade.jdbc.plano.Produto;

import java.sql.*;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.Iterator;
import java.util.List;

/**
 * Classe de persistência que encapsula todas as operações de manipulação dos dados da classe <code>ItemVendaAvulsaVO</code>.
 * Responsável por implementar operações como incluir, alterar, excluir e consultar pertinentes a classe <code>ItemVendaAvulsaVO</code>.
 * Encapsula toda a interação com o banco de dados.
 * @see ItemVendaAvulsaVO
 * @see SuperEntidade
 * @see VendaAvulsa
 */
public class ItemVendaAvulsa extends SuperEntidade {

    public ItemVendaAvulsa() throws Exception {
        super();
        setIdEntidade("VendaAvulsa");
    }

    public ItemVendaAvulsa(Connection con) throws Exception {
        super(con);
        setIdEntidade("VendaAvulsa");
    }

    /**
     * Operação responsável por retornar um novo objeto da classe <code>ItemVendaAvulsaVO</code>.
     */
    public ItemVendaAvulsaVO novo() throws Exception {
        incluir(getIdEntidade());
        ItemVendaAvulsaVO obj = new ItemVendaAvulsaVO();
        return obj;
    }

    /**
     * Operação responsável por incluir no banco de dados um objeto da classe <code>ItemVendaAvulsaVO</code>.
     * Primeiramente valida os dados (<code>validarDados</code>) do objeto. Verifica a conexão com o banco de dados e a permissão do usuário
     * para realizar esta operacão na entidade.
     * Isto, através da operação <code>incluir</code> da superclasse.
     * @param obj  Objeto da classe <code>ItemVendaAvulsaVO</code> que será gravado no banco de dados.
     * @exception Exception Caso haja problemas de conexão, restrição de acesso ou validação de dados.
     */
    public void incluir(ItemVendaAvulsaVO obj) throws Exception {
        ItemVendaAvulsaVO.validarDados(obj);
        incluir(getIdEntidade());
        obj.realizarUpperCaseDados();
        String sql = "INSERT INTO ItemVendaAvulsa( vendaAvulsa, produto, quantidade, valorParcial, tabelaDesconto, descontomanual, "
                + "valordescontomanual, responsaveldesconto, pacote) VALUES (?, ?, ?, ?, ?, ?,?,?,?)";
        try (PreparedStatement sqlInserir = con.prepareStatement(sql)) {
            if (obj.getVendaAvulsa().intValue() != 0) {
                sqlInserir.setInt(1, obj.getVendaAvulsa().intValue());
            } else {
                sqlInserir.setNull(1, 0);
            }
            if (obj.getProduto().getCodigo().intValue() != 0) {
                sqlInserir.setInt(2, obj.getProduto().getCodigo().intValue());
            } else {
                sqlInserir.setNull(2, 0);
            }
            sqlInserir.setInt(3, obj.getQuantidade().intValue());
            sqlInserir.setDouble(4, obj.getValorParcial().doubleValue());
            if (obj.getTabelaDesconto().getCodigo().intValue() != 0) {
                sqlInserir.setInt(5, obj.getTabelaDesconto().getCodigo().intValue());
            } else {
                sqlInserir.setNull(5, 0);
            }
            sqlInserir.setBoolean(6, obj.getDescontoManual());
            if (obj.getDescontoManual()) {
                sqlInserir.setDouble(7, obj.getValorDescontoManual());
                sqlInserir.setInt(8, obj.getResponsavelAutorizacaoDesconto().getCodigo());
            } else {
                sqlInserir.setNull(7, 0);
                sqlInserir.setNull(8, 0);
            }
            if (obj.getPacoteVO().getCodigo() != null && obj.getPacoteVO().getCodigo() != 0) {
                sqlInserir.setInt(9, obj.getPacoteVO().getCodigo());
            } else {
                sqlInserir.setNull(9, 0);
            }
            sqlInserir.execute();
        }
        obj.setCodigo(obterValorChavePrimariaCodigo());
        obj.setNovoObj(new Boolean(false));
    }

    /**
     * Operação responsável por alterar no BD os dados de um objeto da classe <code>ItemVendaAvulsaVO</code>.
     * Sempre utiliza a chave primária da classe como atributo para localização do registro a ser alterado.
     * Primeiramente valida os dados (<code>validarDados</code>) do objeto. Verifica a conexão com o banco de dados e a permissão do usuário
     * para realizar esta operacão na entidade.
     * Isto, através da operação <code>alterar</code> da superclasse.
     * @param obj    Objeto da classe <code>ItemVendaAvulsaVO</code> que será alterada no banco de dados.
     * @exception Execption Caso haja problemas de conexão, restrição de acesso ou validação de dados.
     */
    public void alterar(ItemVendaAvulsaVO obj) throws Exception {
        ItemVendaAvulsaVO.validarDados(obj);
        alterar(getIdEntidade());
        obj.realizarUpperCaseDados();
        String sql = "UPDATE ItemVendaAvulsa set vendaAvulsa=?, produto=?, quantidade=?, valorParcial=?, "
                + "tabelaDesconto=?, descontomanual = ?, valordescontomanual = ?, responsaveldesconto = ?, pacote = ? WHERE ((codigo = ?))";
        try (PreparedStatement sqlAlterar = con.prepareStatement(sql)) {
            if (obj.getVendaAvulsa().intValue() != 0) {
                sqlAlterar.setInt(1, obj.getVendaAvulsa().intValue());
            } else {
                sqlAlterar.setNull(1, 0);
            }
            if (obj.getProduto().getCodigo().intValue() != 0) {
                sqlAlterar.setInt(2, obj.getProduto().getCodigo().intValue());
            } else {
                sqlAlterar.setNull(2, 0);
            }
            sqlAlterar.setInt(3, obj.getQuantidade().intValue());
            sqlAlterar.setDouble(4, obj.getValorParcial().doubleValue());
            if (obj.getTabelaDesconto().getCodigo().intValue() != 0) {
                sqlAlterar.setInt(5, obj.getTabelaDesconto().getCodigo().intValue());
            } else {
                sqlAlterar.setNull(5, 0);
            }
            sqlAlterar.setBoolean(6, obj.getDescontoManual());
            if (obj.getDescontoManual()) {
                sqlAlterar.setDouble(7, obj.getValorDescontoManual());
                sqlAlterar.setInt(8, obj.getResponsavelAutorizacaoDesconto().getCodigo());
            } else {
                sqlAlterar.setNull(7, 0);
                sqlAlterar.setNull(8, 0);
            }
            if (obj.getPacoteVO().getCodigo() != null && obj.getPacoteVO().getCodigo() != 0) {
                sqlAlterar.setInt(9, obj.getPacoteVO().getCodigo());
            } else {
                sqlAlterar.setNull(9, 0);
            }
            sqlAlterar.setInt(10, obj.getCodigo().intValue());
            sqlAlterar.execute();
        }
    }

    /**
     * Operação responsável por excluir no BD um objeto da classe <code>ItemVendaAvulsaVO</code>.
     * Sempre localiza o registro a ser excluído através da chave primária da entidade.
     * Primeiramente verifica a conexão com o banco de dados e a permissão do usuário
     * para realizar esta operacão na entidade.
     * Isto, através da operação <code>excluir</code> da superclasse.
     * @param obj    Objeto da classe <code>ItemVendaAvulsaVO</code> que será removido no banco de dados.
     * @exception Execption Caso haja problemas de conexão ou restrição de acesso.
     */
    public void excluir(ItemVendaAvulsaVO obj) throws Exception {
        excluir(getIdEntidade());
        String sql = "DELETE FROM ItemVendaAvulsa WHERE ((codigo = ?))";
        try (PreparedStatement sqlExcluir = con.prepareStatement(sql)) {
            sqlExcluir.setInt(1, obj.getCodigo().intValue());
            sqlExcluir.execute();
        }
    }

    /**
     * Responsável por realizar uma consulta de <code>ItemVendaAvulsa</code> através do valor do atributo 
     * <code>descricao</code> da classe <code>Produto</code>
     * Faz uso da operação <code>montarDadosConsulta</code> que realiza o trabalho de prerarar o List resultante.
     * @return  List Contendo vários objetos da classe <code>ItemVendaAvulsaVO</code> resultantes da consulta.
     * @exception Execption Caso haja problemas de conexão ou restrição de acesso.
     */
    public List consultarPorDescricaoProduto(String valorConsulta, int nivelMontarDados) throws Exception {
        consultar(getIdEntidade(), true);
        String sqlStr = "SELECT ItemVendaAvulsa.* FROM ItemVendaAvulsa, Produto WHERE ItemVendaAvulsa.produto = Produto.codigo and upper( Produto.descricao ) like('" + valorConsulta.toUpperCase() + "%') ORDER BY Produto.descricao";
        try (Statement stm = con.createStatement()) {
            try (ResultSet tabelaResultado = stm.executeQuery(sqlStr)) {
                return montarDadosConsulta(tabelaResultado, nivelMontarDados);
            }
        }
    }

    /**
     * Responsável por realizar uma consulta de <code>ItemVendaAvulsa</code> através do valor do atributo 
     * <code>nomeComprador</code> da classe <code>VendaAvulsa</code>
     * Faz uso da operação <code>montarDadosConsulta</code> que realiza o trabalho de prerarar o List resultante.
     * @return  List Contendo vários objetos da classe <code>ItemVendaAvulsaVO</code> resultantes da consulta.
     * @exception Execption Caso haja problemas de conexão ou restrição de acesso.
     */
    public List consultarPorNomeCompradorVendaAvulsa(String valorConsulta, int nivelMontarDados) throws Exception {
        consultar(getIdEntidade(), true);
        String sqlStr = "SELECT ItemVendaAvulsa.* FROM ItemVendaAvulsa, VendaAvulsa WHERE ItemVendaAvulsa.vendaAvulsa = VendaAvulsa.codigo and upper( VendaAvulsa.nomeComprador ) like('" + valorConsulta.toUpperCase() + "%') ORDER BY VendaAvulsa.nomeComprador";
        try (Statement stm = con.createStatement()) {
            try (ResultSet tabelaResultado = stm.executeQuery(sqlStr)) {
                return montarDadosConsulta(tabelaResultado, nivelMontarDados);
            }
        }
    }

    /**
     * Responsável por realizar uma consulta de <code>ItemVendaAvulsa</code> através do valor do atributo 
     * <code>Integer codigo</code>. Retorna os objetos com valores iguais ou superiores ao parâmetro fornecido.
     * Faz uso da operação <code>montarDadosConsulta</code> que realiza o trabalho de prerarar o List resultante.
     * @param   controlarAcesso Indica se a aplicação deverá verificar se o usuário possui permissão para esta consulta ou não.
     * @return  List Contendo vários objetos da classe <code>ItemVendaAvulsaVO</code> resultantes da consulta.
     * @exception Exception Caso haja problemas de conexão ou restrição de acesso.
     */
    public List consultarPorCodigo(Integer valorConsulta, boolean controlarAcesso, int nivelMontarDados) throws Exception {
        consultar(getIdEntidade(), controlarAcesso);
        String sqlStr = "SELECT * FROM ItemVendaAvulsa WHERE codigo >= " + valorConsulta.intValue() + " ORDER BY codigo";
        try (Statement stm = con.createStatement()) {
            try (ResultSet tabelaResultado = stm.executeQuery(sqlStr)) {
                return (montarDadosConsulta(tabelaResultado, nivelMontarDados));
            }
        }
    }

    /**
     * Responsável por montar os dados de vários objetos, resultantes de uma consulta ao banco de dados (<code>ResultSet</code>).
     * Faz uso da operação <code>montarDados</code> que realiza o trabalho para um objeto por vez.
     * @return  List Contendo vários objetos da classe <code>ItemVendaAvulsaVO</code> resultantes da consulta.
     */
    public List montarDadosConsulta(ResultSet tabelaResultado, int nivelMontarDados) throws Exception {
        List vetResultado = new ArrayList();
        while (tabelaResultado.next()) {
            ItemVendaAvulsaVO obj = new ItemVendaAvulsaVO();
            obj = montarDados(tabelaResultado, nivelMontarDados);
            vetResultado.add(obj);
        }
        tabelaResultado = null;
        return vetResultado;
    }

    public boolean consultarPorPacote(int pacote) throws Exception {
        String sqlStr = "SELECT exists (select codigo FROM itemvendaavulsa "
                + " WHERE pacote = "+pacote+") as existe";
        try (Statement stm = con.createStatement()) {
            try (ResultSet tabelaResultado = stm.executeQuery(sqlStr)) {
                tabelaResultado.next();
                return tabelaResultado.getBoolean("existe");
            }
        }
    }
    
    /**
     * Responsável por montar os dados resultantes de uma consulta ao banco de dados (<code>ResultSet</code>)
     * em um objeto da classe <code>ItemVendaAvulsaVO</code>.
     * @return  O objeto da classe <code>ItemVendaAvulsaVO</code> com os dados devidamente montados.
     */
    public ItemVendaAvulsaVO montarDados(ResultSet dadosSQL, int nivelMontarDados) throws Exception {
        ItemVendaAvulsaVO obj = new ItemVendaAvulsaVO();
        obj.setCodigo(new Integer(dadosSQL.getInt("codigo")));

        obj.setVendaAvulsa(new Integer(dadosSQL.getInt("vendaAvulsa")));
        obj.getProduto().setCodigo(new Integer(dadosSQL.getInt("produto")));
        obj.setQuantidade(new Integer(dadosSQL.getInt("quantidade")));
        obj.setValorParcial(new Double(dadosSQL.getDouble("valorParcial")));
        obj.getTabelaDesconto().setCodigo(new Integer(dadosSQL.getInt("tabelaDesconto")));
        obj.getPacoteVO().setCodigo(new Integer(dadosSQL.getInt("pacote")));
        try {
            obj.setPacotePersonal(dadosSQL.getInt("pacotepersonal"));
        } catch (SQLException e) {}
        obj.setNovoObj(new Boolean(false));
        if (nivelMontarDados == Uteis.NIVELMONTARDADOS_DADOSBASICOS) {
            return obj;
        }
        montarDadosProduto(obj, nivelMontarDados);
        montarDadosPacote(obj, nivelMontarDados);
        try {
            obj.setDescontoManual(dadosSQL.getBoolean("descontoManual"));
            obj.setValorDescontoManual(dadosSQL.getDouble("valorDescontoManual"));
            montarDadosDesconto(obj, nivelMontarDados);
        }catch (Exception e){}
        return obj;
    }

    /**
     * Operação responsável por montar os dados de um objeto da classe <code>ProdutoVO</code> relacionado ao objeto <code>ItemVendaAvulsaVO</code>.
     * Faz uso da chave primária da classe <code>ProdutoVO</code> para realizar a consulta.
     * @param obj  Objeto no qual será montado os dados consultados.
     */
    public void montarDadosProduto(ItemVendaAvulsaVO obj, int nivelMontarDados) throws Exception {
        if (obj.getProduto().getCodigo() == 0) {
            obj.setProduto(new ProdutoVO());
            return;
        }
        Produto produtoDAO = new Produto(this.con);
        obj.setProduto(produtoDAO.consultarPorChavePrimaria(obj.getProduto().getCodigo(), nivelMontarDados));
        produtoDAO = null;
    }

     public void montarDadosPacote(ItemVendaAvulsaVO obj, int nivelMontarDados) throws Exception {
        if (obj.getPacoteVO().getCodigo() == 0) {
            obj.setPacoteVO(new PacoteVO());
            return;
        }
         Pacote pacoteDAO = new Pacote(this.con);
        obj.setPacoteVO(pacoteDAO.consultarPorCodigo(obj.getPacoteVO().getCodigo()));
         pacoteDAO = null;
    }

     public void montarDadosDesconto(ItemVendaAvulsaVO obj, int nivelMontarDados) throws Exception {
        if (obj.getTabelaDesconto() == null || obj.getTabelaDesconto().getCodigo() == 0) {
            obj.setTabelaDesconto(new DescontoVO());
            return;
        }
        Desconto descontoDAO = new Desconto(this.con);
        obj.setTabelaDesconto(descontoDAO.consultarPorChavePrimaria(obj.getTabelaDesconto().getCodigo(), nivelMontarDados));
         descontoDAO = null;
    }

    /**
     * Operação responsável por excluir todos os objetos da <code>ItemVendaAvulsaVO</code> no BD.
     * Faz uso da operação <code>excluir</code> disponível na classe <code>ItemVendaAvulsa</code>.
     * @param <code>vendaAvulsa</code> campo chave para exclusão dos objetos no BD.
     * @exception Exception  Erro de conexão com o BD ou restrição de acesso a esta operação.
     */
    public void excluirItemVendaAvulsas(Integer vendaAvulsa) throws Exception {
        excluirItemVendaAvulsas(vendaAvulsa, true);
    }

    public void excluirItemVendaAvulsas(Integer vendaAvulsa, boolean verificarAcesso) throws Exception {
        if(verificarAcesso) {
            excluir(getIdEntidade());
        }
        String sql = "DELETE FROM ItemVendaAvulsa WHERE (vendaAvulsa = ?)";
        try (PreparedStatement sqlExcluir = con.prepareStatement(sql)) {
            sqlExcluir.setInt(1, vendaAvulsa.intValue());
            sqlExcluir.execute();
        }
    }

    /**
     * Operação responsável por alterar todos os objetos da <code>ItemVendaAvulsaVO</code> contidos em um Hashtable no BD.
     * Faz uso da operação <code>excluirItemVendaAvulsas</code> e <code>incluirItemVendaAvulsas</code> disponíveis na classe <code>ItemVendaAvulsa</code>.
     * @param objetos  List com os objetos a serem alterados ou incluídos no BD.
     * @exception Exception  Erro de conexão com o BD ou restrição de acesso a esta operação.
     */
    public void alterarItemVendaAvulsas(Integer vendaAvulsa, List objetos) throws Exception {
        String str = "DELETE FROM ItemVendaAvulsa WHERE vendaAvulsa = " + vendaAvulsa;
        Iterator i = objetos.iterator();
        while (i.hasNext()) {
            ItemVendaAvulsaVO objeto = (ItemVendaAvulsaVO) i.next();
            str += " AND codigo <> " + objeto.getCodigo().intValue();
        }
        try (PreparedStatement sqlExcluir = con.prepareStatement(str)) {
            sqlExcluir.execute();
        }
        Iterator e = objetos.iterator();
        while (e.hasNext()) {
            ItemVendaAvulsaVO objeto = (ItemVendaAvulsaVO) e.next();
            if (objeto.getCodigo().equals(0)) {
                incluir(objeto);
            } else {
                alterar(objeto);
            }
        }
    }

    /**
     * Operação responsável por incluir objetos da <code>ItemVendaAvulsaVO</code> no BD.
     * Garantindo o relacionamento com a entidade principal <code>financeiro.VendaAvulsa</code> através do atributo de vínculo.
     * @param objetos List contendo os objetos a serem gravados no BD da classe.
     * @exception Exception  Erro de conexão com o BD ou restrição de acesso a esta operação.
     */
    public void incluirItemVendaAvulsas(Integer vendaAvulsaPrm, List objetos) throws Exception {
        Iterator e = objetos.iterator();
        while (e.hasNext()) {
            ItemVendaAvulsaVO obj = (ItemVendaAvulsaVO) e.next();
            obj.setVendaAvulsa(vendaAvulsaPrm);
            validarRegraControleEstoque(obj);
            incluir(obj);
        }
    }

    private void validarRegraControleEstoque(ItemVendaAvulsaVO itemVendaAvulsaVO) throws Exception {
        ProdutoEstoque produtoEstoqueDAO = new ProdutoEstoque(con);
        BalancoItens balancoItensDAO = new BalancoItens(con);
        ProdutoEstoqueVO produtoEstoqueVO = produtoEstoqueDAO.consultarPorProduto(itemVendaAvulsaVO.getProduto().getCodigo(), itemVendaAvulsaVO.getVendaAvulsaVO().getEmpresa().getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS);
        if ((produtoEstoqueVO != null) && (produtoEstoqueVO.getSituacao().equals("A"))){
            // Verificar se a data da venda é menor que data do último balanço do produto.
            Date dataBalanco = balancoItensDAO.pesquisarBalancoComDataMaior(itemVendaAvulsaVO.getVendaAvulsaVO().getDataRegistro(), itemVendaAvulsaVO.getProduto().getCodigo(), itemVendaAvulsaVO.getVendaAvulsaVO().getEmpresa().getCodigo());
            SimpleDateFormat sdf = new SimpleDateFormat("dd/MM/yyyy HH:mm:ss");
            if (dataBalanco != null){
                throw new ConsistirException("Operação não permitida, a data da venda é menor que a data do último balanço. " +
                        "Existe um balanço na data '" + sdf.format(dataBalanco) + "' para o produto '" + itemVendaAvulsaVO.getProduto().getDescricao() + "'.");
            }
            // verificar se a data da venda é menor que a data de configuração do produto no estoque.
            Date dataProdutoEstoque = produtoEstoqueDAO.pesquisarProdutoEstoqueComDataMaior(itemVendaAvulsaVO.getVendaAvulsaVO().getDataRegistro(), itemVendaAvulsaVO.getProduto().getCodigo(), itemVendaAvulsaVO.getVendaAvulsaVO().getEmpresa().getCodigo());
            if (dataProdutoEstoque != null){
                throw new ConsistirException("Operação não permitida, a data da venda é menor que a data de configuração do produto no estoque. " +
                        "Data de configuração do produto ' " + itemVendaAvulsaVO.getProduto().getDescricao() + "' no estoque: '" + sdf.format(dataProdutoEstoque) + "'");
            }
        }

        produtoEstoqueDAO = null;
        balancoItensDAO = null;
    }

    /**
     * Operação responsável por consultar todos os <code>ItemVendaAvulsaVO</code> relacionados a um objeto da classe <code>financeiro.VendaAvulsa</code>.
     * @param vendaAvulsa  Atributo de <code>financeiro.VendaAvulsa</code> a ser utilizado para localizar os objetos da classe <code>ItemVendaAvulsaVO</code>.
     * @return List  Contendo todos os objetos da classe <code>ItemVendaAvulsaVO</code> resultantes da consulta.
     * @exception Exception  Erro de conexão com o BD ou restrição de acesso a esta operação.
     */
    public List consultarItemVendaAvulsas(Integer vendaAvulsa, int nivelMontarDados) throws Exception {
        consultar(getIdEntidade());
        List objetos = new ArrayList();
        String sql = "SELECT * FROM ItemVendaAvulsa WHERE vendaAvulsa = ?";
        try (PreparedStatement sqlConsulta = con.prepareStatement(sql)) {
            sqlConsulta.setInt(1, vendaAvulsa.intValue());
            try (ResultSet resultado = sqlConsulta.executeQuery()) {
                while (resultado.next()) {
                    ItemVendaAvulsaVO novoObj = new ItemVendaAvulsaVO();
                    novoObj = montarDados(resultado, nivelMontarDados);
                    objetos.add(novoObj);
                }
            }
        }
        return objetos;
    }

    /**
     * Operação responsável por localizar um objeto da classe <code>ItemVendaAvulsaVO</code>
     * através de sua chave primária. 
     * @exception Exception Caso haja problemas de conexão ou localização do objeto procurado.
     */
    public ItemVendaAvulsaVO consultarPorChavePrimaria(Integer codigoPrm, int nivelMontarDados) throws Exception {
        consultar(getIdEntidade(), false);
        String sql = "SELECT * FROM ItemVendaAvulsa WHERE codigo = ?";
        try (PreparedStatement sqlConsultar = con.prepareStatement(sql)) {
            sqlConsultar.setInt(1, codigoPrm.intValue());
            try (ResultSet tabelaResultado = sqlConsultar.executeQuery()) {
                if (!tabelaResultado.next()) {
                    throw new ConsistirException("Dados Não Encontrados ( ItemVendaAvulsa ).");
                }
                return (montarDados(tabelaResultado, nivelMontarDados));
            }
        }
    }

    public ItemVendaAvulsaVO consultarItemVendaAvulsaPorMovProduto(Integer movproduto, Integer produto, int nivelMontarDados) throws Exception {
        consultar(getIdEntidade(), true);
        StringBuilder sqlStr = new StringBuilder("SELECT item.* FROM MovParcela ");
        sqlStr.append("inner join movprodutoparcela mvp on mvp.movparcela = MovParcela.codigo ");
        sqlStr.append("inner join movproduto mov on mov.codigo = mvp.movproduto ");
        sqlStr.append("inner join vendaavulsa venda on venda.codigo = movparcela.vendaavulsa ");
        sqlStr.append("inner join itemvendaavulsa item on item.vendaavulsa = venda.codigo ");
        sqlStr.append("where mov.codigo = " + movproduto + " and item.produto = " + produto);
        try (PreparedStatement sqlConsultar = con.prepareStatement(sqlStr.toString())) {
            try (ResultSet tabelaResultado = sqlConsultar.executeQuery()) {
                if (!tabelaResultado.next()) {
                    throw new ConsistirException("Dados Não Encontrados ( ItemVendaAvulsa ).");
                }
                return (montarDados(tabelaResultado, nivelMontarDados));
            }
        }
    }
}

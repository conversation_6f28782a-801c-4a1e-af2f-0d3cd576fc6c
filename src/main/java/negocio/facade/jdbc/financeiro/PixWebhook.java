package negocio.facade.jdbc.financeiro;

import negocio.comuns.financeiro.ConvenioCobrancaVO;
import negocio.comuns.financeiro.PixWebhookVO;
import negocio.comuns.utilitarias.Calendario;
import negocio.comuns.utilitarias.Uteis;
import negocio.facade.jdbc.arquitetura.SuperEntidade;
import negocio.interfaces.financeiro.PixWebhookInterfaceFacade;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.List;


/**
 * Created by <PERSON> on 21/11/2023.
 */

public class PixWebhook extends SuperEntidade implements PixWebhookInterfaceFacade {

    public PixWebhook() throws Exception {
        super();
    }

    public PixWebhook(Connection connection) throws Exception {
        super(connection);
    }

    public void incluir(PixWebhookVO obj) throws Exception {
        String sql = "INSERT INTO pixwebhook(dataregistro, tipoConvenio, dados, pixWebhookOamd) VALUES (?,?,?,?) RETURNING codigo";
        try (PreparedStatement pst = con.prepareStatement(sql)) {
            int i = 0;
            pst.setTimestamp(++i, Uteis.getDataJDBCTimestamp(Calendario.hoje()));
            pst.setInt(++i, obj.getTipoConveio());
            pst.setString(++i, obj.getDados());
            pst.setInt(++i, obj.getCodigoPixWebhookOamd());

            ResultSet rsCodigo = pst.executeQuery();
            rsCodigo.next();
            obj.setCodigo(rsCodigo.getInt("codigo"));
            obj.setNovoObj(false);
        }
    }

    public List<PixWebhookVO> consultarPorTipoConvenio(ConvenioCobrancaVO convenioCobrancaVO) throws Exception {
        String sql = "SELECT * from pixWebhook where tipoConvenio = " + convenioCobrancaVO.getTipo().getCodigo() + "order by dataRegistro desc";

        List<PixWebhookVO> lista = new ArrayList<>();
        try (ResultSet rs = criarConsulta(sql, con)) {
            while (rs.next()) {
                lista.add(montarDados(rs));
            }
        }
        return lista;
    }

    public PixWebhookVO montarDados(ResultSet rs) throws SQLException {
        PixWebhookVO obj = new PixWebhookVO();
        obj.setCodigo(rs.getInt("codigo"));
        obj.setDataRegistro(rs.getTimestamp("dataRegistro"));
        obj.setDados(rs.getString("dados"));
        obj.setTipoConveio(rs.getInt("tipoConvenio"));
        return obj;
    }
}


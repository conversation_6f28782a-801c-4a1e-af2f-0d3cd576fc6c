/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package negocio.facade.jdbc.financeiro;

import java.sql.Connection;
import negocio.comuns.financeiro.PlanoContaTO;
import negocio.comuns.financeiro.enumerador.TipoES;
import negocio.comuns.financeiro.enumerador.TipoEquivalenciaDRE;
import negocio.facade.jdbc.arquitetura.SuperFacadeJDBC;

/**
 *
 * <AUTHOR>
 */
public class PlanoContasSugeridoServico {

    private PlanoConta planoContas;
    private Connection con;

    public PlanoContasSugeridoServico(Connection con){
        this.con = con;
    }

    public void povoar() {
        try {
            planoContas = new PlanoConta(con);
            gravarPlanoConta("001.003", "Receitas Não Operacionais", TipoES.getTipoPadrao(1), TipoEquivalenciaDRE.getTipoEquivalenciaDRE(1));
            gravarPlanoConta("001", "<PERSON><PERSON><PERSON>", TipoES.getTipoPadrao(1), TipoEquivalenciaDRE.getTipoEquivalenciaDRE(1));
            gravarPlanoConta("001.003.001", "Exames Médicos", TipoES.getTipoPadrao(1), TipoEquivalenciaDRE.getTipoEquivalenciaDRE(1));
            gravarPlanoConta("001.001.002", "Matrícula", TipoES.getTipoPadrao(1), TipoEquivalenciaDRE.getTipoEquivalenciaDRE(1));
            gravarPlanoConta("001.001", "Serviços", TipoES.getTipoPadrao(1), TipoEquivalenciaDRE.getTipoEquivalenciaDRE(1));
            gravarPlanoConta("001.001.003", "Avaliação Física", TipoES.getTipoPadrao(1), TipoEquivalenciaDRE.getTipoEquivalenciaDRE(1));
            gravarPlanoConta("001.001.004", "Taxa Personal", TipoES.getTipoPadrao(1), TipoEquivalenciaDRE.getTipoEquivalenciaDRE(1));
            gravarPlanoConta("001.003.002", "Locação Lanchonete", TipoES.getTipoPadrao(1), TipoEquivalenciaDRE.getTipoEquivalenciaDRE(1));
            gravarPlanoConta("001.003.003", "Locação Estética", TipoES.getTipoPadrao(1), TipoEquivalenciaDRE.getTipoEquivalenciaDRE(1));
            gravarPlanoConta("001.003.004", "Locação Loja", TipoES.getTipoPadrao(1), TipoEquivalenciaDRE.getTipoEquivalenciaDRE(1));
            gravarPlanoConta("002.002", "Despesas Fixas", TipoES.getTipoPadrao(2), TipoEquivalenciaDRE.getTipoEquivalenciaDRE(3));
            gravarPlanoConta("002.002.002", "Água e Esgoto", TipoES.getTipoPadrao(2), TipoEquivalenciaDRE.getTipoEquivalenciaDRE(3));
            gravarPlanoConta("002.003", "Despesas Variáveis", TipoES.getTipoPadrao(2), TipoEquivalenciaDRE.getTipoEquivalenciaDRE(3));
            gravarPlanoConta("002.003.001", "Máquinas e Equipamentos", TipoES.getTipoPadrao(2), TipoEquivalenciaDRE.getTipoEquivalenciaDRE(3));
            gravarPlanoConta("002", "Despesas", TipoES.getTipoPadrao(2), TipoEquivalenciaDRE.getTipoEquivalenciaDRE(3));
            gravarPlanoConta("002.002.001", "Energia Elétrica", TipoES.getTipoPadrao(2), TipoEquivalenciaDRE.getTipoEquivalenciaDRE(3));
            gravarPlanoConta("002.002.003", "Gás", TipoES.getTipoPadrao(2), TipoEquivalenciaDRE.getTipoEquivalenciaDRE(3));
            gravarPlanoConta("002.002.004", "Telefone", TipoES.getTipoPadrao(2), TipoEquivalenciaDRE.getTipoEquivalenciaDRE(3));
            gravarPlanoConta("002.002.007", "Honorários Advocatícios", TipoES.getTipoPadrao(2), TipoEquivalenciaDRE.getTipoEquivalenciaDRE(3));
            gravarPlanoConta("002.002.008", "Contabilidade", TipoES.getTipoPadrao(2), TipoEquivalenciaDRE.getTipoEquivalenciaDRE(3));
            gravarPlanoConta("002.002.009", "Assinaturas", TipoES.getTipoPadrao(2), TipoEquivalenciaDRE.getTipoEquivalenciaDRE(3));
            gravarPlanoConta("002.006", "Materiais de Consumo", TipoES.getTipoPadrao(2), TipoEquivalenciaDRE.getTipoEquivalenciaDRE(3));
            gravarPlanoConta("002.006.001", "Material de Escritório", TipoES.getTipoPadrao(2), TipoEquivalenciaDRE.getTipoEquivalenciaDRE(3));
            gravarPlanoConta("002.007", "Propaganda e Markaeting", TipoES.getTipoPadrao(2), TipoEquivalenciaDRE.getTipoEquivalenciaDRE(3));
            gravarPlanoConta("002.003.002", "Edifícios Dependencias", TipoES.getTipoPadrao(2), TipoEquivalenciaDRE.getTipoEquivalenciaDRE(3));
            gravarPlanoConta("002.003.003", "Móveis e Utensílios", TipoES.getTipoPadrao(2), TipoEquivalenciaDRE.getTipoEquivalenciaDRE(3));
            gravarPlanoConta("002.002.006", "Aulas sistematizadas", TipoES.getTipoPadrao(2), TipoEquivalenciaDRE.getTipoEquivalenciaDRE(3));
            gravarPlanoConta("002.009.010", "INSS", TipoES.getTipoPadrao(2), TipoEquivalenciaDRE.getTipoEquivalenciaDRE(3));
            gravarPlanoConta("003.001", "Apórtes Sócios", TipoES.getTipoPadrao(1), TipoEquivalenciaDRE.getTipoEquivalenciaDRE(1));
            gravarPlanoConta("002.009.015", " ECAD ", TipoES.getTipoPadrao(2), TipoEquivalenciaDRE.getTipoEquivalenciaDRE(3));
            gravarPlanoConta("002.002.011", " Seguros ", TipoES.getTipoPadrao(2), TipoEquivalenciaDRE.getTipoEquivalenciaDRE(3));
            gravarPlanoConta("002.009.001", "PIS", TipoES.getTipoPadrao(2), TipoEquivalenciaDRE.getTipoEquivalenciaDRE(3));
            gravarPlanoConta("002.009.002", " COFINS  ", TipoES.getTipoPadrao(2), TipoEquivalenciaDRE.getTipoEquivalenciaDRE(3));
            gravarPlanoConta("002.009", "Impostos e Taxas", TipoES.getTipoPadrao(2), TipoEquivalenciaDRE.getTipoEquivalenciaDRE(3));
            gravarPlanoConta("002.009.003", " ISS", TipoES.getTipoPadrao(2), TipoEquivalenciaDRE.getTipoEquivalenciaDRE(3));
            gravarPlanoConta("002.009.004", " TFE e TFA ", TipoES.getTipoPadrao(2), TipoEquivalenciaDRE.getTipoEquivalenciaDRE(3));
            gravarPlanoConta("002.009.005", " Contribuição Sindical ", TipoES.getTipoPadrao(2), TipoEquivalenciaDRE.getTipoEquivalenciaDRE(3));
            gravarPlanoConta("002.009.006", " IRRF", TipoES.getTipoPadrao(2), TipoEquivalenciaDRE.getTipoEquivalenciaDRE(3));
            gravarPlanoConta("002.009.007", "CREFI ", TipoES.getTipoPadrao(2), TipoEquivalenciaDRE.getTipoEquivalenciaDRE(3));
            gravarPlanoConta("002.001.001", "Salários", TipoES.getTipoPadrao(2), TipoEquivalenciaDRE.getTipoEquivalenciaDRE(3));
            gravarPlanoConta("002.001.002", "Férias", TipoES.getTipoPadrao(2), TipoEquivalenciaDRE.getTipoEquivalenciaDRE(3));
            gravarPlanoConta("002.001.003", "13º Salários", TipoES.getTipoPadrao(2), TipoEquivalenciaDRE.getTipoEquivalenciaDRE(3));
            gravarPlanoConta("002.009.012", "FGTS", TipoES.getTipoPadrao(2), TipoEquivalenciaDRE.getTipoEquivalenciaDRE(3));
            gravarPlanoConta("002.001.004", "Comissões", TipoES.getTipoPadrao(2), TipoEquivalenciaDRE.getTipoEquivalenciaDRE(3));
            gravarPlanoConta("002.009.013", "Rescisões", TipoES.getTipoPadrao(2), TipoEquivalenciaDRE.getTipoEquivalenciaDRE(3));
            gravarPlanoConta("002.001.005", "Benefícios", TipoES.getTipoPadrao(2), TipoEquivalenciaDRE.getTipoEquivalenciaDRE(3));
            gravarPlanoConta("002.001.006", "Uniformes", TipoES.getTipoPadrao(2), TipoEquivalenciaDRE.getTipoEquivalenciaDRE(3));
            gravarPlanoConta("002.001", "Folhas e Encargos", TipoES.getTipoPadrao(2), TipoEquivalenciaDRE.getTipoEquivalenciaDRE(3));
            gravarPlanoConta("002.009.008", "CSLL", TipoES.getTipoPadrao(2), TipoEquivalenciaDRE.getTipoEquivalenciaDRE(3));
            gravarPlanoConta("002.002.005", "Internet", TipoES.getTipoPadrao(2), TipoEquivalenciaDRE.getTipoEquivalenciaDRE(3));
            gravarPlanoConta("002.009.009", "IRPJ ", TipoES.getTipoPadrao(2), TipoEquivalenciaDRE.getTipoEquivalenciaDRE(3));
            gravarPlanoConta("002.003.004", "Avaliação Física", TipoES.getTipoPadrao(2), TipoEquivalenciaDRE.getTipoEquivalenciaDRE(3));
            gravarPlanoConta("002.009.011", "DARF", TipoES.getTipoPadrao(2), TipoEquivalenciaDRE.getTipoEquivalenciaDRE(3));
            gravarPlanoConta("002.002.010", "Segurança", TipoES.getTipoPadrao(2), TipoEquivalenciaDRE.getTipoEquivalenciaDRE(3));
            gravarPlanoConta("002.010", "Despesas Bancárias", TipoES.getTipoPadrao(2), TipoEquivalenciaDRE.getTipoEquivalenciaDRE(3));
            gravarPlanoConta("002.003.005", "Informática", TipoES.getTipoPadrao(2), TipoEquivalenciaDRE.getTipoEquivalenciaDRE(3));
            gravarPlanoConta("002.006.002", "Material de Limpeza", TipoES.getTipoPadrao(2), TipoEquivalenciaDRE.getTipoEquivalenciaDRE(3));
            gravarPlanoConta("002.006.003", "Impressos", TipoES.getTipoPadrao(2), TipoEquivalenciaDRE.getTipoEquivalenciaDRE(3));
            gravarPlanoConta("002.007.001", "Criação", TipoES.getTipoPadrao(2), TipoEquivalenciaDRE.getTipoEquivalenciaDRE(3));
            gravarPlanoConta("002.007.002", "Material Gráfico", TipoES.getTipoPadrao(2), TipoEquivalenciaDRE.getTipoEquivalenciaDRE(3));
            gravarPlanoConta("002.007.003", "Assessoria Imprensa", TipoES.getTipoPadrao(2), TipoEquivalenciaDRE.getTipoEquivalenciaDRE(3));
            gravarPlanoConta("002.010.001", "Tarifas Bancárias ", TipoES.getTipoPadrao(2), TipoEquivalenciaDRE.getTipoEquivalenciaDRE(3));
            gravarPlanoConta("002.009.014", " CREF ", TipoES.getTipoPadrao(2), TipoEquivalenciaDRE.getTipoEquivalenciaDRE(3));
            gravarPlanoConta("002.011", "Investimentos", TipoES.getTipoPadrao(2), TipoEquivalenciaDRE.getTipoEquivalenciaDRE(3));
            gravarPlanoConta("002.011.001", " Edifícios e Instalações ", TipoES.getTipoPadrao(2), TipoEquivalenciaDRE.getTipoEquivalenciaDRE(3));
            gravarPlanoConta("002.011.002", " Máquinas e Equipamentos ", TipoES.getTipoPadrao(2), TipoEquivalenciaDRE.getTipoEquivalenciaDRE(3));
            gravarPlanoConta("002.011.003", " Móveis e Utensílios ", TipoES.getTipoPadrao(2), TipoEquivalenciaDRE.getTipoEquivalenciaDRE(3));
            gravarPlanoConta("002.011.004", " Treinamentos ", TipoES.getTipoPadrao(2), TipoEquivalenciaDRE.getTipoEquivalenciaDRE(3));
            gravarPlanoConta("002.011.005", " Consultoria Empresarial ", TipoES.getTipoPadrao(2), TipoEquivalenciaDRE.getTipoEquivalenciaDRE(3));
            gravarPlanoConta("002.002.012", " Aluguel ", TipoES.getTipoPadrao(2), TipoEquivalenciaDRE.getTipoEquivalenciaDRE(3));
            gravarPlanoConta("002.009.016", "IPTU ", TipoES.getTipoPadrao(2), TipoEquivalenciaDRE.getTipoEquivalenciaDRE(3));
            gravarPlanoConta("004.001", "Retiradas de sócios", TipoES.getTipoPadrao(2), TipoEquivalenciaDRE.getTipoEquivalenciaDRE(2));
            gravarPlanoConta("001.001.001", "Planos", TipoES.getTipoPadrao(1), TipoEquivalenciaDRE.getTipoEquivalenciaDRE(1));
            gravarPlanoConta("002.007.004", " Eventos ", TipoES.getTipoPadrao(2), TipoEquivalenciaDRE.getTipoEquivalenciaDRE(3));
            gravarPlanoConta("003", "Entradas (Outras Entradas)", TipoES.getTipoPadrao(1), TipoEquivalenciaDRE.getTipoEquivalenciaDRE(1));
            gravarPlanoConta("004", "Saídas (Outras Saídas)", TipoES.getTipoPadrao(2), TipoEquivalenciaDRE.getTipoEquivalenciaDRE(2));
            gravarPlanoConta("002.003.006", "Cancelamento (Devoluções)", TipoES.getTipoPadrao(2), TipoEquivalenciaDRE.getTipoEquivalenciaDRE(3));
            gravarPlanoConta("002.010.002", "Taxa Operadora de cartão", TipoES.getTipoPadrao(2), TipoEquivalenciaDRE.getTipoEquivalenciaDRE(3));
            gravarPlanoConta("002.010.003", "Taxa Boleto", TipoES.getTipoPadrao(2), TipoEquivalenciaDRE.getTipoEquivalenciaDRE(3));
            gravarPlanoConta("001.003.005", "Quitação de Cancelamento", TipoES.getTipoPadrao(1), TipoEquivalenciaDRE.getTipoEquivalenciaDRE(1));
            gravarPlanoConta("001.001.005", "Rematrícula", TipoES.getTipoPadrao(1), TipoEquivalenciaDRE.getTipoEquivalenciaDRE(1));
            gravarPlanoConta("003.002", "Rendimento de investimentos", TipoES.getTipoPadrao(1), TipoEquivalenciaDRE.getTipoEquivalenciaDRE(1));
            gravarPlanoConta("003.003", "Patrocínio", TipoES.getTipoPadrao(1), TipoEquivalenciaDRE.getTipoEquivalenciaDRE(1));
            gravarPlanoConta("004.002", "Investimentos", TipoES.getTipoPadrao(2), TipoEquivalenciaDRE.getTipoEquivalenciaDRE(2));
            gravarPlanoConta("004.003", "Fundo de reserva", TipoES.getTipoPadrao(2), TipoEquivalenciaDRE.getTipoEquivalenciaDRE(2));
        } catch (Exception e) {
            e.fillInStackTrace();
        }
    }

    public void gravarPlanoConta(String codigoPlano, String descricao, TipoES tipoES, TipoEquivalenciaDRE equivalenciaDRE) throws Exception {
        if(planoContas == null){
            planoContas = new PlanoConta(this.con);
        }
        PlanoContaTO planoConta = new PlanoContaTO();
        planoConta.setCodigoPlano(codigoPlano);
        planoConta.setDescricao(descricao);
        planoConta.setTipoPadrao(tipoES);
        planoConta.setEquivalenciaDRE(equivalenciaDRE);
        planoContas.incluirSemCommit(planoConta);
    }
}

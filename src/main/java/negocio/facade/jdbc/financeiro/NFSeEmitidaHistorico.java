package negocio.facade.jdbc.financeiro;

import negocio.comuns.financeiro.ItemGestaoNotasTO;
import negocio.comuns.financeiro.NFSeEmitidaHistoricoVO;
import negocio.comuns.financeiro.NFSeEmitidaVO;
import negocio.comuns.notaFiscal.SituacaoNotaFiscalEnum;
import negocio.comuns.utilitarias.Calendario;
import negocio.comuns.utilitarias.Uteis;
import negocio.facade.jdbc.arquitetura.SuperEntidade;
import negocio.interfaces.financeiro.NFSeEmitidaHistoricoInterfaceFacade;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.sql.Statement;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR> <PERSON>
 */
public class NFSeEmitidaHistorico extends SuperEntidade implements NFSeEmitidaHistoricoInterfaceFacade {

    public NFSeEmitidaHistorico() throws Exception {
    }

    public NFSeEmitidaHistorico(Connection conexao) throws Exception {
        super(conexao);
    }

    public void incluir(NFSeEmitidaHistoricoVO obj) throws Exception {
        String sql = "INSERT INTO NFSeEmitidaHistorico(nfseemitida, recibopagamento, cartaocredito, cheque, movpagamento, movproduto, rps, " +
                "contrato, movconta, nrNotaManual, idReferencia, valor, dataEnvio, pessoa, jsonEnviar, dataRegistro, dataEmissao, " +
                "dataReferencia, empresa, notaFamilia, configuracaonotafiscal, SituacaoNotaFiscal, sequencialFamilia) " +
                "VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)";
        PreparedStatement sqlInserir = con.prepareStatement(sql);
        int i = 0;
        sqlInserir.setInt(++i, obj.getNfseEmitida());
        resolveIntegerNull(sqlInserir, ++i, obj.getReciboPagamento());
        resolveIntegerNull(sqlInserir, ++i, obj.getCartaoCredito());
        resolveIntegerNull(sqlInserir, ++i, obj.getCheque());
        resolveIntegerNull(sqlInserir, ++i, obj.getMovPagamento());
        resolveIntegerNull(sqlInserir, ++i, obj.getMovProduto());
        resolveIntegerNull(sqlInserir, ++i, obj.getRps());
        resolveIntegerNull(sqlInserir, ++i, obj.getContrato());
        resolveIntegerNull(sqlInserir, ++i, obj.getMovConta());
        sqlInserir.setString(++i, obj.getNrNotaManual());
        sqlInserir.setString(++i, obj.getIdReferencia());
        sqlInserir.setDouble(++i, obj.getValor());
        sqlInserir.setTimestamp(++i, Uteis.getDataJDBCTimestamp(obj.getDataEnvio()));
        resolveIntegerNull(sqlInserir, ++i, obj.getPessoa());
        sqlInserir.setString(++i, obj.getJsonEnviar());
        sqlInserir.setTimestamp(++i, Uteis.getDataJDBCTimestamp(obj.getDataRegistro()));
        sqlInserir.setTimestamp(++i, Uteis.getDataJDBCTimestamp(obj.getDataEmissao()));
        sqlInserir.setTimestamp(++i, Uteis.getDataJDBCTimestamp(obj.getDataReferencia()));
        sqlInserir.setInt(++i, obj.getEmpresa());
        sqlInserir.setBoolean(++i, obj.isNotaFamilia());
        resolveIntegerNull(sqlInserir, ++i, obj.getConfiguracaoNotaFiscalVO().getCodigo());
        sqlInserir.setInt(++i, obj.getSituacaoNotaFiscal().getCodigo());
        resolveIntegerNull(sqlInserir, ++i, obj.getSequencialFamilia());

        sqlInserir.execute();
        obj.setCodigo(obterValorChavePrimariaCodigo());
        obj.setNovoObj(false);
    }

    public static NFSeEmitidaHistoricoVO montarDados(ResultSet dadosSQL, int nivelMontarDados, Connection con) throws Exception {
        NFSeEmitidaHistoricoVO obj = new NFSeEmitidaHistoricoVO();
        obj.setCodigo(dadosSQL.getInt("codigo"));
        obj.setReciboPagamento(dadosSQL.getInt("recibopagamento"));
        obj.setCheque(dadosSQL.getInt("cheque"));
        obj.setCartaoCredito(dadosSQL.getInt("cartaocredito"));
        obj.setMovPagamento(dadosSQL.getInt("movpagamento"));
        obj.setMovProduto(dadosSQL.getInt("movproduto"));
        obj.setMovConta(dadosSQL.getInt("movconta"));
        obj.setContrato(dadosSQL.getInt("contrato"));
        obj.setRps(dadosSQL.getInt("rps"));
        obj.setNrNotaManual(dadosSQL.getString("nrNotaManual"));
        obj.setIdReferencia(dadosSQL.getString("IdReferencia"));
        obj.setValor(dadosSQL.getDouble("valor"));
        obj.setDataEnvio(dadosSQL.getTimestamp("dataEnvio"));
        obj.setPessoa(dadosSQL.getInt("pessoa"));
        obj.setJsonEnviar(dadosSQL.getString("jsonEnviar"));
        obj.setDataRegistro(dadosSQL.getTimestamp("dataRegistro"));
        obj.setDataEmissao(dadosSQL.getTimestamp("dataEmissao"));
        obj.setDataReferencia(dadosSQL.getTimestamp("dataReferencia"));
        obj.setEmpresa(dadosSQL.getInt("empresa"));
        obj.setNotaFamilia(dadosSQL.getBoolean("notaFamilia"));
        obj.getConfiguracaoNotaFiscalVO().setCodigo(dadosSQL.getInt("configuracaonotafiscal"));
        obj.setSituacaoNotaFiscal(SituacaoNotaFiscalEnum.obterPorCodigo(dadosSQL.getInt("SituacaoNotaFiscal")));
        obj.setSequencialFamilia(dadosSQL.getInt("sequencialFamilia"));
        obj.setNovoObj(false);

        if (nivelMontarDados == Uteis.NIVELMONTARDADOS_DADOSBASICOS) {
            return obj;
        }

        return obj;
    }

    public List<NFSeEmitidaHistoricoVO> montarDadosConsulta(ResultSet tabelaResultado, int nivelMontarDados, Connection con) throws Exception {
        List<NFSeEmitidaHistoricoVO> notas = new ArrayList<NFSeEmitidaHistoricoVO>();
        while (tabelaResultado.next()) {
            NFSeEmitidaHistoricoVO nota = montarDados(tabelaResultado, nivelMontarDados, con);
            notas.add(nota);
        }
        return notas;
    }

    public void atualizarSituacaoIdRps(NFSeEmitidaVO obj) throws SQLException {
        String sql = "UPDATE nfseemitidahistorico SET SituacaoNotaFiscal = ?, rps = ? WHERE nfseemitida = ?";
        PreparedStatement sqlUpdate = con.prepareStatement(sql);
        sqlUpdate.setInt(1, obj.getSituacaoNotaFiscal().getCodigo());
        sqlUpdate.setInt(2, obj.getIdRps());
        sqlUpdate.setInt(3, obj.getCodigo());
        sqlUpdate.execute();
    }

    public void atualizarSituacaoIdRpsNotaFamilia(SituacaoNotaFiscalEnum situacaoNotaFiscal, Integer rps, String idNotaFamilia) throws Exception {
        String sql = "UPDATE nfseemitidahistorico SET SituacaoNotaFiscal = ?, rps = ? WHERE notafamilia = true and idreferencia = ?";
        PreparedStatement sqlInserir = con.prepareStatement(sql);
        sqlInserir.setInt(1, situacaoNotaFiscal.getCodigo());
        sqlInserir.setInt(2, rps);
        sqlInserir.setString(3, idNotaFamilia);
        sqlInserir.execute();
    }

    public void atualizarNrNotaManual(NFSeEmitidaVO obj) throws SQLException {
        String sql = "UPDATE nfseemitidahistorico SET nrNotaManual = ? WHERE nfseemitida = ?";
        PreparedStatement sqlUpdate = con.prepareStatement(sql);
        sqlUpdate.setString(1, obj.getNrNotaManual());
        sqlUpdate.setInt(2, obj.getCodigo());
        sqlUpdate.execute();
    }

    public void atualizarDataReferenciaJsonEnviar(Date dataReferencia, String jsonEnviar, Integer nfseemitida) throws Exception {
        String sql = "UPDATE nfseemitidahistorico SET dataReferencia = ?,  jsonEnviar  = ? WHERE nfseemitida = ?";
        PreparedStatement sqlInserir = con.prepareStatement(sql);
        sqlInserir.setTimestamp(1, Uteis.getDataJDBCTimestamp(dataReferencia));
        sqlInserir.setString(2, jsonEnviar);
        sqlInserir.setInt(3, nfseemitida);
        sqlInserir.execute();
    }

    public void atualizarJsonEnviarIdReferencia(NFSeEmitidaVO nfSeEmitidaVO) throws Exception {
        String sql = "UPDATE nfseemitidahistorico SET jsonEnviar  = ?, idReferencia  = ? WHERE nfseemitida = ?";
        PreparedStatement sqlInserir = con.prepareStatement(sql);
        sqlInserir.setString(1, nfSeEmitidaVO.getJsonEnviar());
        sqlInserir.setString(2, nfSeEmitidaVO.getIdReferencia());
        sqlInserir.setInt(3, nfSeEmitidaVO.getCodigo());
        sqlInserir.execute();
    }

    public void atualizarSituacao(SituacaoNotaFiscalEnum situacaoNotaFiscal, Integer nfseemitida) throws Exception {
        String sql = "UPDATE nfseemitidahistorico SET situacaoNotaFiscal  = ? WHERE nfseemitida = ?";
        PreparedStatement sqlInserir = con.prepareStatement(sql);
        sqlInserir.setInt(1, situacaoNotaFiscal.getCodigo());
        sqlInserir.setInt(2, nfseemitida);
        sqlInserir.execute();
    }

    public void atualizarSituacaoPorSequencialFamilia(SituacaoNotaFiscalEnum situacaoNotaFiscal, Integer nfseemitida) throws Exception {
        String sql = "UPDATE nfseemitidahistorico SET situacaoNotaFiscal  = ? WHERE sequencialFamilia = ?";
        PreparedStatement sqlInserir = con.prepareStatement(sql);
        sqlInserir.setInt(1, situacaoNotaFiscal.getCodigo());
        sqlInserir.setInt(2, nfseemitida);
        sqlInserir.execute();
    }

    public void atualizarDataEnvioSituacao(Date dataEnvio, SituacaoNotaFiscalEnum situacaoNotaFiscal, String nfseemitida) throws Exception {
        String sql = "UPDATE nfseemitidahistorico SET dataenvio = ?, situacaoNotaFiscal  = ? WHERE nfseemitida in ("+nfseemitida+")";
        PreparedStatement sqlInserir = con.prepareStatement(sql);
        sqlInserir.setTimestamp(1, Uteis.getDataJDBCTimestamp(dataEnvio));
        sqlInserir.setInt(2, situacaoNotaFiscal.getCodigo());
        sqlInserir.execute();
    }

    public void atualizarCartao(Integer codAntigo, Integer codNovo) throws SQLException {
        String sql = "UPDATE nfseemitidahistorico SET cartaocredito  = ? WHERE cartaocredito= ?";
        PreparedStatement sqlUpdate = con.prepareStatement(sql);
        sqlUpdate.setInt(1, codNovo);
        sqlUpdate.setInt(2, codAntigo);
        sqlUpdate.execute();
    }

    public void atualizarCheque(Integer codAntigo, Integer codNovo) throws SQLException {
        String sql = "UPDATE nfseemitidahistorico SET cheque  = ? WHERE cheque= ?";
        PreparedStatement sqlUpdate = con.prepareStatement(sql);
        sqlUpdate.setInt(1, codNovo);
        sqlUpdate.setInt(2, codAntigo);
        sqlUpdate.execute();
    }

    public void atualizarMovPagamento(Integer codAntigo, Integer codNovo) throws SQLException {
        String sql = "UPDATE nfseemitidahistorico SET movpagamento = ? WHERE movpagamento = ?";
        PreparedStatement sqlUpdate = con.prepareStatement(sql);
        sqlUpdate.setInt(1, codNovo);
        sqlUpdate.setInt(2, codAntigo);
        sqlUpdate.execute();
    }

    public List<NFSeEmitidaHistoricoVO> consultarPorEmpresaDataReferencia(Integer empresa, Date dataInicio, Date dataFinal) throws Exception {
        String sql = "SELECT * FROM nfseemitidahistorico WHERE empresa = "+empresa+" AND datareferencia::date BETWEEN '"+Uteis.getData(dataInicio)+"' AND '"+Uteis.getData(dataFinal)+"'";
        Statement stm = con.createStatement();
        ResultSet tabelaResultado = stm.executeQuery(sql);
        List<NFSeEmitidaHistoricoVO> notasEmitidas = new ArrayList<NFSeEmitidaHistoricoVO>();
        while (tabelaResultado.next()) {
            notasEmitidas.add(montarDados(tabelaResultado, Uteis.NIVELMONTARDADOS_DADOSBASICOS, con));
        }
        return notasEmitidas;
    }

    public List<ItemGestaoNotasTO> consultarNotasExcluidasApresentar(Integer empresa, Date dataInicio, Date dataFinal) throws Exception {
        List<ItemGestaoNotasTO> lista = new ArrayList<>();
        try (Statement stm = con.createStatement();
             ResultSet rs = stm.executeQuery(sqlNotasExcluidas(empresa, dataInicio, dataFinal))) {
            while (rs.next()) {
                ItemGestaoNotasTO item = new ItemGestaoNotasTO();
                item.setMatricula(rs.getString("matricula"));
                item.setNome(rs.getString("nome"));
                item.setValor(rs.getDouble("valor"));
                item.setDataEmissao(rs.getDate("dataemissao"));
                item.setRps(rs.getInt("rps"));
                lista.add(item);
            }
        }
        return lista;
    }

    public boolean existeNotasExcluidas(Integer empresa, Date dataInicio, Date dataFinal) throws Exception {
        Statement stm = con.createStatement();
        String sql = sqlNotasExcluidas(empresa, dataInicio, dataFinal);
        ResultSet tabelaResultado = stm.executeQuery("select exists(" + sql + ")");
        return tabelaResultado.next() && tabelaResultado.getBoolean(1);
    }

    private String sqlNotasExcluidas(Integer empresa, Date dataInicio, Date dataFinal) {

        String dataInicioFormatada = Calendario.getDataAplicandoFormatacao(Calendario.getDataComHoraZerada(dataInicio),"dd/MM/yyyy HH:mm:ss");
        String dataFinalFormatada = Calendario.getDataAplicandoFormatacao(Calendario.getDataComUltimaHora(dataFinal),"dd/MM/yyyy HH:mm:ss");

        StringBuffer sql = new StringBuffer();
        sql.append("select \n");
        sql.append("    cl.matricula, \n");
        sql.append("    p.nome,  \n");
        sql.append("    sql.valor, \n");
        sql.append("    sql.dataemissao, \n");
        sql.append("    sql.rps  \n");
        sql.append("from ( \n");
        sql.append("    select  \n");
        sql.append("        nh.pessoa,  \n");
        sql.append("        nh.valor,  \n");
        sql.append("        nh.dataemissao,  \n");
        sql.append("        nh.rps  \n");
        sql.append("    from nfseemitidahistorico nh  \n");
        sql.append("    where nh.empresa = ").append(empresa).append(" \n");
        sql.append("    and nh.datareferencia between '")
                .append(dataInicioFormatada)
                .append("' and '")
                .append(dataFinalFormatada).append("' \n");
        sql.append("      and not exists ( \n");
        sql.append("          select 1 from nfseemitida n where n.codigo = nh.nfseemitida \n");
        sql.append("      ) \n");
        sql.append("    UNION \n");
        sql.append("    select  \n");
        sql.append("        nh.pessoa,  \n");
        sql.append("        nh.valor,  \n");
        sql.append("        nh.dataemissao,  \n");
        sql.append("        nh.rps  \n");
        sql.append("    from nfseemitidahistorico nh  \n");
        sql.append("    inner join movproduto m on m.codigo = nh.movproduto \n");
        sql.append("    where nh.empresa = ").append(empresa).append(" \n");
        sql.append("      and m.empresa = ").append(empresa).append(" \n");
        sql.append("      and m.situacao = 'CA' \n");
        sql.append("and nh.datareferencia between '")
                .append(dataInicioFormatada)
                .append("' and '")
                .append(dataFinalFormatada).append("' \n");
        sql.append(") as sql \n");
        sql.append("left join pessoa p on p.codigo = sql.pessoa \n");
        sql.append("left join cliente cl on cl.pessoa = p.codigo \n");
        return sql.toString();
    }

    public void excluirPorNFSeEmitida(NFSeEmitidaVO nfSeEmitidaVO) throws SQLException {
        Statement stm = con.createStatement();
        String sql = "DELETE FROM nfseemitidahistorico\n" +
                "WHERE nfseemitida = " + nfSeEmitidaVO.getCodigo() + ";";
        stm.execute(sql);
    }
}

package negocio.facade.jdbc.financeiro;

import br.com.pactosolucoes.comuns.util.Formatador;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

import br.com.pactosolucoes.enumeradores.TipoOperacaoLancamento;
import java.sql.Connection;
import negocio.comuns.basico.EmpresaVO;
import negocio.comuns.financeiro.ChequeVO;
import negocio.comuns.financeiro.FormaPagamentoVO;
import negocio.comuns.financeiro.MovContaRateioVO;
import negocio.comuns.financeiro.MovContaVO;
import negocio.comuns.financeiro.MovPagamentoVO;
import negocio.comuns.financeiro.RecebivelAvulsoTO;
import negocio.comuns.financeiro.enumerador.TipoES;
import negocio.comuns.utilitarias.Calendario;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisValidacao;
import negocio.facade.jdbc.arquitetura.SuperEntidade;
import negocio.interfaces.financeiro.RecebivelAvulsoInterfaceFacade;

public class RecebivelAvulso extends SuperEntidade implements RecebivelAvulsoInterfaceFacade {

    public RecebivelAvulso() throws Exception {
        super();
    }

    public RecebivelAvulso(Connection con) throws Exception {
        super(con);
    }

    public void incluir(RecebivelAvulsoTO recebivel, Integer formaPagamento) throws Exception {

        try {
            if(recebivel.getTipoCheque()){
                recebivel.setMsgErro(getFacade().getCheque().existeJaExisteChequeComMesmosDados(recebivel.getCheque(), false));
                if(!UteisValidacao.emptyString(recebivel.getMsgErro())){
                    throw new Exception("Dados Existentes");
                }
            }
            con.setAutoCommit(false);
            MovContaVO movConta = new MovContaVO();
            movConta.setUsuarioVO(recebivel.getResponsavel());
            movConta.setPessoaVO(recebivel.getCliente().getPessoa());
            movConta.setEmpresaVO(recebivel.getEmpresa());
            movConta.setTipoOperacaoLancamento(TipoOperacaoLancamento.RECEBIVEL_AVULSO);
            movConta.setCodigo(recebivel.getMovConta());
            movConta.setDataCompetencia(recebivel.getFaturamento());
            movConta.setDataLancamento(Calendario.hoje());
            movConta.setDataVencimento(recebivel.getFaturamento());
            movConta.setDataQuitacao(recebivel.getCompensacao());
            movConta.setValor(recebivel.getValor());

            FormaPagamentoVO fp = new FormaPagamentoVO();
            fp.setCodigo(formaPagamento);
            if (recebivel.getTipoCheque()) {
                recebivel.getCheque().setValor(recebivel.getValor());
                recebivel.getCheque().setValorTotal(recebivel.getValor());
                recebivel.getCheque().setDataCompensacao(recebivel.getCompensacao());
                movConta.setDescricao("Cheque Avulso nr. : " + recebivel.getCheque().getNumero());
            } else {
                movConta.setDescricao("Cartão de Crédito Avulso  "
                        + (UteisValidacao.emptyString(recebivel.getCartaoCredito().getNrDocumento()) ? ": " + recebivel.getCliente().getPessoa()
                        : ("nr. Doc.: " + recebivel.getCartaoCredito().getNrDocumento())));
                recebivel.getCartaoCredito().setValor(recebivel.getValor());
                recebivel.getCartaoCredito().setValorTotal(recebivel.getValor());
                recebivel.getCartaoCredito().setDataCompensacao(recebivel.getCompensacao());
                //PAY-1022 - Tratativa para remover espaços em branco do texto evitando erros
                if(!UteisValidacao.emptyString(recebivel.getCodAutorizacao())) {
                    recebivel.setCodAutorizacao(recebivel.getCodAutorizacao().trim());
                }
                movConta.setAutorizacaoCartao(recebivel.getCodAutorizacao());
            }

            MovContaRateioVO mcr = new MovContaRateioVO();
            mcr.setFormaPagamentoVO(fp);
            mcr.setPlanoContaVO(recebivel.getPlanoContas());
            mcr.setCentroCustoVO(recebivel.getCentroCustos());
            mcr.setTipoES(TipoES.ENTRADA);
            mcr.setDescricao(movConta.getDescricao());
            mcr.setValor(movConta.getValor());

            movConta.getMovContaRateios().add(mcr);

            if (movConta.getCodigo() == 0) {
                getFacade().getFinanceiro().getMovConta().incluirSemCommit(movConta, 0, false, null);
            } else {
                getFacade().getFinanceiro().getMovConta().alterar(movConta, 0, false);
            }


            if (recebivel.getTipoCheque()) {
                recebivel.getCheque().setVistaOuPrazo(recebivel.getCompensacao().compareTo(Uteis.somarDias(recebivel.getFaturamento(), movConta.getEmpresaVO().getNrDiasChequeAVista())) == 1 ? "PR" : "AV");
                recebivel.getCheque().setMovConta(movConta);
                if (recebivel.getCheque().getCodigo() == 0) {
                    getFacade().getCheque().incluir(recebivel.getCheque());
                } else {
                    getFacade().getCheque().alterar(recebivel.getCheque());
                }

            } else {
                recebivel.getCartaoCredito().setMovConta(movConta);
                recebivel.getCartaoCredito().setValorTotal(recebivel.getCartaoCredito().getValor());
                if (recebivel.getCartaoCredito().getCodigo() == 0) {
                    getFacade().getCartaoCredito().incluir(recebivel.getCartaoCredito());
                } else {
                    getFacade().getCartaoCredito().alterar(recebivel.getCartaoCredito());
                }


            }
            con.commit();
        } catch (Exception e) {
            con.rollback();
            throw e;
        } finally {
            con.setAutoCommit(true);
        }

    }

    public void excluir(RecebivelAvulsoTO recebivel) throws Exception {

        try {
            con.setAutoCommit(false);
            if (recebivel.getCheque().getCodigo() == 0) {
                getFacade().getCheque().excluir(recebivel.getCheque());
            } else {
                getFacade().getCartaoCredito().excluir(recebivel.getCartaoCredito());
            }
            MovContaVO mc = new MovContaVO();
            mc.setCodigo(recebivel.getMovConta());
            getFacade().getFinanceiro().getMovConta().excluir(mc);
        } catch (Exception e) {
            con.rollback();
            throw e;
        } finally {
            con.setAutoCommit(true);
        }
    }

    public List<RecebivelAvulsoTO> consultar() throws Exception {
        List<RecebivelAvulsoTO> lista = new ArrayList<RecebivelAvulsoTO>();
        ResultSet query = getRS(true, true, null);
        while (query.next()) {
            lista.add(montarDados(query));
        }
        return lista;
    }

    private ResultSet getRS(boolean cheque, boolean cartao, Integer codigo) throws SQLException {
        StringBuilder sql = new StringBuilder();
        sql.append(" select banco.nome as bnome, oc.descricao ocdescricao, p.nome as nomepessoa,  c.vistaouprazo ch_vistaouprazo, ");
        sql.append("c.datacompesancao ch_datacompesancao,  c.valor ch_valor,  c.agencia ch_agencia, \n");
        sql.append(" c.conta ch_conta, c.numero ch_numero,  c.movpagamento ch_movpagamento,  c.codigo ch_codigo,  c.cpf ch_cpf,  c.cnpj ch_cnpj,  \n");
        sql.append(" c.situacao ch_situacao,  c.dataoriginal ch_dataoriginal,  c.banco ch_banco, c.movconta ch_movconta,  c.nomenocheque ch_nomenocheque, \n");
        sql.append(" ca.codigo cc_codigo, ca.valor cc_valor, ca.dataoriginal cc_dataoriginal, ca.operadoracartao cc_operadoracartao, ca.situacao cc_situacao, \n");
        sql.append(" ca.movconta cc_movconta, ca.nomenocartao cc_nomenocartao, ca.datacompesancao cc_datacompesancao,ca.nrparcela as cc_nrparcela, banco.codigobanco, ");
        sql.append(" ca.nrdocumento cc_nrdocumento, m.*, mcr.formapagamento, mcr.planoconta, mcr.centrocusto, mcr.movconta, ");
        sql.append(" pl.codigo as pl_codigo, pl.nome as pl_nome, ce.codigo as ce_codigo, ce.nome as ce_nome, u.nome responsavel from movconta m \n ");
        sql.append(" INNER JOIN pessoa p on  p.codigo = m.pessoa \n");
        sql.append(" INNER JOIN movcontarateio mcr on  mcr.movconta = m.codigo \n");
        sql.append(" INNER JOIN usuario u on  u.codigo = m.usuario \n");
        sql.append(" LEFT JOIN planoconta pl on mcr.planoconta = pl.codigo \n");
        sql.append(" LEFT JOIN centrocusto ce on ce.codigo = mcr.centrocusto \n");
        sql.append(" LEFT JOIN cheque c on m.codigo = c.movconta \n");
        sql.append(" LEFT JOIN cartaocredito ca on m.codigo = ca.movconta \n");
        sql.append(" LEFT JOIN banco on banco.codigo = c.banco \n");
        sql.append(" LEFT JOIN operadoracartao oc on oc.codigo = ca.operadoracartao \n");
        sql.append(" WHERE m.tipooperacao = " + TipoOperacaoLancamento.RECEBIVEL_AVULSO.getCodigo());
        if (cheque && !cartao) {
            sql.append(UteisValidacao.emptyNumber(codigo) ? " AND c.codigo is not null " : " AND c.codigo = " + codigo);
        }
        if (cartao && !cheque) {
            sql.append(UteisValidacao.emptyNumber(codigo) ? " AND ca.codigo is not null " : " AND ca.codigo = " + codigo);
        }

        sql.append(" order by m.datalancamento desc");
        PreparedStatement stm = con.prepareStatement(sql.toString());
        return stm.executeQuery();
    }

    public RecebivelAvulsoTO consultarPorCodigo(boolean tipoCheque, int codigo) throws Exception {
        ResultSet rS = getRS(tipoCheque, !tipoCheque, codigo);
        if (rS.next()) {
            return montarDados(rS);
        } else {
            return new RecebivelAvulsoTO();
        }

    }

    private RecebivelAvulsoTO montarDados(ResultSet query) throws Exception {
        RecebivelAvulsoTO novo = new RecebivelAvulsoTO();
        if (!UteisValidacao.emptyNumber(query.getInt("ch_codigo"))) {
            novo.setCheque(new ChequeVO());
            novo.getCheque().setAgencia(query.getString("ch_agencia"));
            novo.getCheque().setNomeNoCheque(query.getString("ch_nomenocheque"));
            novo.getCheque().setCnpj(query.getString("ch_cnpj"));
            novo.getCheque().setCpf(query.getString("ch_cpf"));
            novo.getCheque().getBanco().setCodigo(query.getInt("ch_banco"));
            novo.getCheque().setCodigo(query.getInt("ch_codigo"));
            novo.getCheque().setDataCompensacao(query.getDate("ch_datacompesancao"));
            novo.getCheque().setDataOriginal(query.getDate("ch_dataoriginal"));
            novo.getCheque().setValor(query.getDouble("ch_valor"));
            novo.getCheque().setVistaOuPrazo(query.getString("ch_vistaouprazo"));
            novo.getCheque().setConta(query.getString("ch_conta"));
            novo.getCheque().getMovConta().setCodigo(query.getInt("ch_movconta"));
            novo.getCheque().setSituacao(query.getString("ch_situacao"));
            novo.getCheque().setNumero(query.getString("ch_numero"));
            novo.getCheque().getBanco().setNome(query.getString("bnome"));
            novo.getCheque().getBanco().setCodigoBanco(query.getInt("codigobanco"));
            novo.setTipoCheque(true);
        } else {
            novo.setTipoCheque(false);
            novo.getCartaoCredito().getMovConta().setCodigo(query.getInt("cc_movconta"));
            novo.getCartaoCredito().setCodigo(query.getInt("cc_codigo"));
            novo.getCartaoCredito().setValor(query.getDouble("cc_valor"));
            novo.getCartaoCredito().getOperadora().setCodigo(query.getInt("cc_operadoracartao"));
            novo.getCartaoCredito().setDataCompensacao(query.getDate("cc_datacompesancao"));
            novo.getCartaoCredito().setDataOriginal(query.getDate("cc_dataoriginal"));
            novo.getCartaoCredito().setNomeNoCartao(query.getString("cc_nomenocartao"));
            novo.getCartaoCredito().setNrDocumento(query.getString("cc_nrdocumento"));
            novo.getCartaoCredito().setSituacao(query.getString("cc_situacao"));
            novo.getCartaoCredito().getOperadora().setDescricao(query.getString("ocdescricao"));
            novo.getCartaoCredito().setNrParcela(query.getInt("cc_nrparcela"));
        }
        novo.getEmpresa().setCodigo(query.getInt("empresa"));
        novo.getCliente().getPessoa().setCodigo(query.getInt("pessoa"));
        novo.getCliente().getPessoa().setNome(query.getString("nomepessoa"));
        novo.setFaturamento(query.getDate("datacompetencia"));
        novo.setLancamento(query.getDate("datalancamento"));
        novo.setCompensacao(query.getDate("dataquitacao"));
        novo.setCodAutorizacao(query.getString("autorizacaocartao"));
        novo.getResponsavel().setCodigo(query.getInt("usuario"));
        novo.getResponsavel().setNome(query.getString("responsavel"));
        novo.setValor(query.getDouble("valor"));
        novo.setMovConta(query.getInt("movconta"));
        novo.getCentroCustos().setCodigo(query.getInt("ce_codigo"));
        novo.getPlanoContas().setCodigo(query.getInt("pl_codigo"));
        novo.getPlanoContas().setDescricao(query.getString("pl_nome"));
        novo.getCentroCustos().setDescricao(query.getString("ce_nome"));


        return novo;

    }

    public String consultarCartaoJSON() throws SQLException {
        ResultSet query = getRS(false, true, null);
        StringBuilder json = new StringBuilder();
        json.append("{\"aaData\":[");
        boolean dados = false;
        while (query.next()) {
            dados = true;
            json.append("[\"").append(query.getString("cc_codigo")).append("\",");
            json.append("\"").append(query.getString("nomepessoa")).append("\",");
            json.append("\"").append(query.getDate("datalancamento")).append("\",");
            json.append("\"").append(query.getDate("datacompetencia")).append("\",");
            json.append("\"").append(query.getDate("cc_datacompesancao")).append("\",");

            //PAY-1022 - Tratamento para evitar erros no parse do JSON por espaços de tabulação na String
            String autorizacaoCartao = query.getString("autorizacaocartao");
            if(!UteisValidacao.emptyString(autorizacaoCartao)) {
                autorizacaoCartao = autorizacaoCartao.trim();
            }else {
                autorizacaoCartao = "";
            }

            json.append("\"").append(autorizacaoCartao).append("\",");
            json.append("\"").append(query.getString("cc_nrdocumento")).append("\",");
            json.append("\"").append(query.getString("cc_operadoracartao")).append("\",");
            json.append("\"").append(Formatador.formatarValorMonetario(query.getDouble("cc_valor"))).append("\",");
            json.append("\"").append(query.getString("cc_nomenocartao")).append("\"],");
        }
        if (dados) {
            json.deleteCharAt(json.toString().length() - 1);
        }
        json.append("]}");
        return json.toString();

    }

    public String consultarChequeJSON() throws SQLException {
        ResultSet query = getRS(true, false, null);
        StringBuilder json = new StringBuilder();
        json.append("{\"aaData\":[");
        boolean dados = false;
        while (query.next()) {
            dados = true;
            json.append("[\"").append(query.getString("ch_codigo")).append("\",");
            json.append("\"").append(query.getString("nomepessoa")).append("\",");
            json.append("\"").append(query.getDate("datalancamento")).append("\",");
            json.append("\"").append(query.getDate("datacompetencia")).append("\",");
            json.append("\"").append(query.getDate("cc_datacompesancao")).append("\",");
            json.append("\"").append(query.getString("bnome")).append("\",");
            json.append("\"").append(query.getString("ch_agencia")).append("\",");
            json.append("\"").append(query.getString("ch_conta")).append("\",");
            json.append("\"").append(query.getString("ch_numero")).append("\",");
            json.append("\"").append(Formatador.formatarValorMonetario(query.getDouble("ch_valor"))).append("\",");
            json.append("\"").append(query.getString("ch_nomenocheque")).append("\"],");
        }
        if (dados) {
            json.deleteCharAt(json.toString().length() - 1);
        }
        json.append("]}");
        return json.toString();

    }

    public List<MovPagamentoVO> consultaParaGestaoRecebiveis(Date inicioCompensacao, Date fimCompensacao,
            Date inicioFaturamento, Date fimFaturamento, EmpresaVO empresa,
            String matricula, String nome, String nomeTerceiro, ChequeVO chequeVO,
            String codAutorizacao, String nrDocumento, Integer operadoraCartao, Boolean pesquisarComLote,
            String cpf) throws Exception {

        List<MovPagamentoVO> lista = new ArrayList<MovPagamentoVO>();
        String sql = "select  p.codigo as pessoa, p.nome as nomepessoa,p.cfp as cpfpessoa, mc.autorizacaocartao, cc.nrdocumento, cli.matricula, "
                + " mc.codigo as movconta, fp.descricao, fp.tipoformapagamento, fp.codigo as forma, mcr.valor from movconta  mc inner join movcontarateio mcr "
                + " on mcr.movconta = mc.codigo "
                + " inner join formapagamento fp on fp.codigo = mcr.formapagamento "
                + " inner join pessoa p on p.codigo = mc.pessoa "
                + " left join cliente cli on cli.pessoa = p.codigo "
                + " left join cheque ch on ch.movconta = mc.codigo "
                + " left join cartaocredito cc on cc.movconta = mc.codigo "
                + " WHERE tipooperacao = " + TipoOperacaoLancamento.RECEBIVEL_AVULSO.getCodigo();
        if (!UteisValidacao.emptyNumber(empresa.getCodigo())) {
            sql = sql + " and mc.empresa = " + empresa.getCodigo();
        }
        if (!UteisValidacao.emptyNumber(operadoraCartao)) {
            sql = sql + " and cc.operadoracartao = " + operadoraCartao;
        }
        if (!UteisValidacao.emptyString(matricula)) {
            try {
                sql = sql + " and cli.codigomatricula = " + Integer.valueOf(matricula);
            } catch (Exception e) {
            }
        }
        if (!UteisValidacao.emptyString(cpf)) {
            sql = sql + " and p.cfp like '" + cpf + "%' ";
        }
        if (!UteisValidacao.emptyString(nome)) {
            sql = sql + " and p.nome like '" + nome.toUpperCase() + "%' ";
        }
        if (!UteisValidacao.emptyString(nomeTerceiro)) {
            sql = sql + " and ch.nomenocheque like '" + nomeTerceiro.toUpperCase() + "%' ";
        }
        if (!UteisValidacao.emptyString(nomeTerceiro)) {
            sql = sql + " and ch.nomenocheque like '" + nomeTerceiro.toUpperCase() + "%' ";
        }
        if (!UteisValidacao.emptyString(codAutorizacao)) {
            sql = sql + " and mc.autorizacaocartao like '%" + codAutorizacao + "' ";
        }
        if (!UteisValidacao.emptyString(nrDocumento)) {
            sql = sql + " and cc.nrdocumento like '" + nrDocumento + "' ";
        }
        if (chequeVO != null) {
            if (!UteisValidacao.emptyString(chequeVO.getAgencia())) {
                sql = sql + " and ch.agencia ilike '" + chequeVO.getAgencia() + "%'";
            }
            if (!UteisValidacao.emptyString(chequeVO.getConta())) {
                sql = sql + " and ch.conta ilike '" + chequeVO.getConta() + "%'";
            }
            if (!UteisValidacao.emptyString(chequeVO.getNumero())) {
                sql = sql + " and ch.numero = '" + chequeVO.getNumero() + "'";
            }
            if (chequeVO.getBanco() != null && !UteisValidacao.emptyNumber(chequeVO.getBanco().getCodigo().intValue())) {
                sql = sql + " and ch.banco = " + chequeVO.getBanco().getCodigo().intValue();
            }
        }
        if (inicioCompensacao != null) {
            sql = sql + " and ((ch.datacompesancao >= '" + Uteis.getDataJDBC(inicioCompensacao)
                    + "') and (ch.datacompesancao <= '" + Uteis.getDataHoraJDBC(fimCompensacao, "23:59:59") + "') "
                    + "or (cc.datacompesancao >= '" + Uteis.getDataJDBC(inicioCompensacao)
                    + "') and (cc.datacompesancao <= '" + Uteis.getDataHoraJDBC(fimCompensacao, "23:59:59") + "'))";
        } else if (inicioFaturamento != null) {
            sql = sql + " and ((mc.dataCompetencia >= '" + Uteis.getDataJDBC(inicioFaturamento)
                    + "') and (mc.dataCompetencia <= '" + Uteis.getDataHoraJDBC(fimFaturamento, "23:59:59") + "'))";
        }
        //se pesquisar considerando somente com lotes ou sem lotes
        if (pesquisarComLote != null) {
            if (pesquisarComLote) {
                sql = sql + " and (EXISTS (SELECT * FROM chequecartaolote WHERE cheque = ch.codigo) or EXISTS (SELECT * FROM chequecartaolote where cartao = cc.codigo))";
            } else {
                sql = sql + " and (NOT EXISTS (SELECT * FROM chequecartaolote WHERE cheque = ch.codigo) AND NOT EXISTS(SELECT * FROM chequecartaolote where cartao = cc.codigo))";
            }
        }

        ResultSet resultSet = criarConsulta(sql, con);
        while (resultSet.next()) {
            MovPagamentoVO pagamentoFake = new MovPagamentoVO();
            pagamentoFake.setPagamentoAvulso(true);
            pagamentoFake.getFormaPagamento().setDescricao(resultSet.getString("descricao"));
            pagamentoFake.getFormaPagamento().setTipoFormaPagamento(resultSet.getString("tipoformapagamento"));
            pagamentoFake.getFormaPagamento().setCodigo(resultSet.getInt("forma"));
            pagamentoFake.setCodigo(resultSet.getInt("movconta"));
            pagamentoFake.setValorTotal(resultSet.getDouble("valor"));
            pagamentoFake.setValor(resultSet.getDouble("valor"));
            pagamentoFake.getPessoa().setNome(resultSet.getString("nomepessoa"));
            pagamentoFake.getPessoa().setCodigo(resultSet.getInt("pessoa"));
            pagamentoFake.setCpfPagador(resultSet.getString("cpfpessoa"));
            pagamentoFake.setNomePagador(resultSet.getString("nomepessoa"));
            pagamentoFake.setAutorizacaoCartao(resultSet.getString("autorizacaocartao"));
            pagamentoFake.setNsu(resultSet.getString("nrdocumento"));
            pagamentoFake.setMatriculaPagador(resultSet.getString("matricula"));
            lista.add(pagamentoFake);
        }
        return lista;
    }
}

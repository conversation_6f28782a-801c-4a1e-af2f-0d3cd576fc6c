package negocio.facade.jdbc.financeiro;

import negocio.comuns.financeiro.TaxaCartaoVO;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisValidacao;
import negocio.facade.jdbc.arquitetura.SuperEntidade;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * Created by glauco on 14/10/2014.
 */
public class TaxaCartao extends SuperEntidade {
    public TaxaCartao() throws Exception {
    }

    public TaxaCartao(Connection conexao) throws Exception {
        super(conexao);
    }

    public static TaxaCartaoVO montarDados(ResultSet dadosSQL, int nivelMontarDados, Connection con) throws Exception {
        TaxaCartaoVO obj = new TaxaCartaoVO();
        obj.setCodigo(dadosSQL.getInt("codigo"));
        obj.setTaxa(dadosSQL.getDouble("taxa"));
        obj.getFormaPagamentoVO().setCodigo(dadosSQL.getInt("formapagamento"));
        obj.setNrmeses(dadosSQL.getInt("nrmeses"));
        try {
            obj.setVigenciaInicial(dadosSQL.getDate("vigenciaInicial"));
            obj.setVigenciaFinal(dadosSQL.getDate("vigenciaFinal"));
        } catch (Exception ignored) {
        }
        try {
            obj.getAdquirenteVO().setCodigo(dadosSQL.getInt("adquirente"));
            obj.getBandeira().setCodigo(dadosSQL.getInt("bandeira"));
            if(nivelMontarDados == Uteis.NIVELMONTARDADOS_DADOSBASICOS){
                try {
                    OperadoraCartao operadoraCartao = new OperadoraCartao(con);
                    obj.setBandeira(operadoraCartao.consultarPorChavePrimaria(obj.getBandeira().getCodigo(), Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA));
                }catch (Exception e){
                //ignore
                }
                try {
                    Adquirente adquirente = new Adquirente(con);
                    obj.setAdquirenteVO(adquirente.consultarPorCodigo(obj.getAdquirenteVO().getCodigo()));
                }catch (Exception e){
                //ignore
                }
            }
        } catch (Exception ignored) {
            //ignore
        }
        try{
            obj.setCompensacaoPorTaxa(dadosSQL.getBoolean("compensacaoPorTaxa"));
            obj.setNrDiasCompensacaoPorTaxa(dadosSQL.getInt("nrDiasCompensacaoPorTaxa"));
        } catch (Exception ignored) {
            //ignore
        }
        try {
            obj.setProdutoVendaSesi(dadosSQL.getString("produtoVendaSesi"));
            obj.setTipodocumentoSesi(dadosSQL.getString("tipodocumentoSesi"));
        } catch (Exception ignored) {
            //ignore
        }
        return obj;
    }

    public static List<TaxaCartaoVO> montarDadosConsulta(ResultSet tabelaResultado, int nivelMontarDados, Connection con) throws Exception {
        List<TaxaCartaoVO> vetResultado = new ArrayList<TaxaCartaoVO>();
        while (tabelaResultado.next()) {
            TaxaCartaoVO obj = montarDados(tabelaResultado, nivelMontarDados, con);
            vetResultado.add(obj);
        }
        return vetResultado;
    }

    public void incluir(TaxaCartaoVO obj) throws Exception {
        String sql = "INSERT INTO taxacartao (formapagamento, nrmeses, taxa, vigenciaInicial, vigenciaFinal, bandeira, formapagamentoempresa, " +
                " adquirente, compensacaoPorTaxa, nrDiasCompensacaoPorTaxa,tipodocumentoSesi,produtoVendaSesi )" +
                "VALUES (?,?,?,?,?,?,?,?,?,?,?,?)";
        int i = 0;
        try (PreparedStatement sqlInserir = con.prepareStatement(sql)) {
            sqlInserir.setInt(++i, obj.getFormaPagamentoVO().getCodigo());
            sqlInserir.setInt(++i, obj.getNrmeses());
            sqlInserir.setDouble(++i, obj.getTaxa());
            sqlInserir.setDate(++i, Uteis.getDataJDBC(obj.getVigenciaInicial()));
            sqlInserir.setDate(++i, Uteis.getDataJDBC(obj.getVigenciaFinal()));

            if (UteisValidacao.emptyNumber(obj.getBandeira().getCodigo())) {
                sqlInserir.setNull(++i, 0);
            } else {
                sqlInserir.setInt(++i, obj.getBandeira().getCodigo());
            }
            if (UteisValidacao.emptyNumber(obj.getFormaPagamentoEmpresaVO().getCodigo())) {
                sqlInserir.setNull(++i, 0);
            } else {
                sqlInserir.setInt(++i, obj.getFormaPagamentoEmpresaVO().getCodigo());
            }
            if (UteisValidacao.emptyNumber(obj.getAdquirenteVO().getCodigo())) {
                sqlInserir.setNull(++i, 0);
            } else {
                sqlInserir.setInt(++i, obj.getAdquirenteVO().getCodigo());
            }
            sqlInserir.setBoolean(++i, obj.isCompensacaoPorTaxa());
            sqlInserir.setInt(++i, obj.getNrDiasCompensacaoPorTaxa());
            sqlInserir.setString(++i, obj.getTipodocumentoSesi());
            sqlInserir.setString(++i, obj.getProdutoVendaSesi());

            sqlInserir.execute();
        }
        obj.setCodigo(obterValorChavePrimariaCodigo());
        obj.setNovoObj(false);
    }

    public void alterar(TaxaCartaoVO obj) throws Exception {
        String sql = "UPDATE taxacartao set formapagamento = ?, nrmeses = ?, taxa = ?, vigenciaInicial = ?, " +
                "vigenciaFinal = ?," +
                "bandeira = ?," +
                "formapagamentoempresa = ?," +
                "adquirente = ?," +
                "compensacaoPorTaxa = ?," +
                "nrDiasCompensacaoPorTaxa = ?,tipodocumentoSesi = ? ,produtoVendaSesi = ? " +
                " WHERE codigo = ?";
        int i = 0;
        try (PreparedStatement sqlAlterar = con.prepareStatement(sql)) {
            sqlAlterar.setInt(++i, obj.getFormaPagamentoVO().getCodigo());
            sqlAlterar.setInt(++i, obj.getNrmeses());
            sqlAlterar.setDouble(++i, obj.getTaxa());
            sqlAlterar.setDate(++i, Uteis.getDataJDBC(obj.getVigenciaInicial()));
            sqlAlterar.setDate(++i, Uteis.getDataJDBC(obj.getVigenciaFinal()));

            if (UteisValidacao.emptyNumber(obj.getBandeira().getCodigo())) {
                sqlAlterar.setNull(++i, 0);
            } else {
                sqlAlterar.setInt(++i, obj.getBandeira().getCodigo());
            }
            if (UteisValidacao.emptyNumber(obj.getFormaPagamentoEmpresaVO().getCodigo())) {
                sqlAlterar.setNull(++i, 0);
            } else {
                sqlAlterar.setInt(++i, obj.getFormaPagamentoEmpresaVO().getCodigo());
            }
            if (UteisValidacao.emptyNumber(obj.getAdquirenteVO().getCodigo())) {
                sqlAlterar.setNull(++i, 0);
            } else {
                sqlAlterar.setInt(++i, obj.getAdquirenteVO().getCodigo());
            }
            sqlAlterar.setBoolean(++i, obj.isCompensacaoPorTaxa());
            sqlAlterar.setInt(++i, obj.getNrDiasCompensacaoPorTaxa());
            sqlAlterar.setString(++i, obj.getTipodocumentoSesi());
            sqlAlterar.setString(++i, obj.getProdutoVendaSesi());


            sqlAlterar.setInt(++i, obj.getCodigo());
            sqlAlterar.execute();
        }
        obj.setNovoObj(false);
    }

    public void excluir(TaxaCartaoVO obj) throws Exception {
        String sql = "DELETE FROM taxacartao WHERE codigo = ?";
        try (PreparedStatement sqlExcluir = con.prepareStatement(sql)) {
            sqlExcluir.setInt(1, obj.getCodigo());
            sqlExcluir.execute();
        }
    }

    public void excluirPorFormaPagamento(Integer codFormaPagamento) throws Exception {
        String sql = "DELETE FROM taxacartao WHERE formapagamento = ?";
        try (PreparedStatement sqlExcluir = con.prepareStatement(sql)) {
            sqlExcluir.setInt(1, codFormaPagamento);
            sqlExcluir.execute();
        }
    }
    
    public void excluirPorFormaPagamentoCodigosAtuais(final Integer codFormaPagamento,final String codigosAtuais) throws Exception {
        String sql = "DELETE FROM taxacartao WHERE formapagamento = ?";
        if(!UteisValidacao.emptyString(codigosAtuais)){
            sql += " and codigo not in ("+codigosAtuais+") ";
        }
        sql += " and formapagamentoempresa is null ";
        try (PreparedStatement sqlExcluir = con.prepareStatement(sql)) {
            sqlExcluir.setInt(1, codFormaPagamento);
            sqlExcluir.execute();
        }
    }

    public List<TaxaCartaoVO> consultarPorFormaPagamento(int formaPagamento, Integer formaEmpresa, int nivelMontarDados) throws Exception {
        String sql = "SELECT * FROM taxacartao ";
        sql += " WHERE formapagamento = ? ";
        if(UteisValidacao.emptyNumber(formaEmpresa)){
            sql += " AND formaPagamentoEmpresa IS NULL ";
        }else{
            sql += " AND formaPagamentoEmpresa = ? ";
        }
        sql += " ORDER BY nrmeses";
        try (PreparedStatement sqlConsultar = con.prepareStatement(sql)) {
            sqlConsultar.setInt(1, formaPagamento);

            if (!UteisValidacao.emptyNumber(formaEmpresa)) {
                sqlConsultar.setInt(2, formaEmpresa);
            }

            try (ResultSet tabelaResultado = sqlConsultar.executeQuery()) {
                return montarDadosConsulta(tabelaResultado, nivelMontarDados, this.con);
            }
        }
    }
    public List<TaxaCartaoVO> consultarPorFormaPagamentoEmpresa(int fpe, Date dataLancamento , Integer formaEmpresa, int nivelMontarDados) throws Exception {
        String sql = "SELECT * FROM taxacartao ";
        sql += " WHERE formapagamentoempresa = ? ";
        sql += " and coalesce(vigenciafinal,?) >= ? ";
        try (PreparedStatement sqlConsultar = con.prepareStatement(sql)) {
            sqlConsultar.setInt(1, fpe);
            sqlConsultar.setDate(2, new java.sql.Date(dataLancamento.getTime()));
            sqlConsultar.setDate(3, new java.sql.Date(dataLancamento.getTime()));
            try (ResultSet tabelaResultado = sqlConsultar.executeQuery()) {
                return montarDadosConsulta(tabelaResultado, nivelMontarDados, this.con);
            }
        }
    }

    public List<TaxaCartaoVO> obterPorBandeiraEFormaPagamentoEParcelamento(int bandeira, int formaPagamento, int nrMeses, int adquirente, java.sql.Date dataHoje, int nivelMontarDados) throws Exception {
        String sql = "SELECT * FROM taxacartao ";
        sql += " WHERE bandeira = ? ";
        sql += " AND formaPagamento = ? ";
        sql += " AND nrMeses = ? ";
        sql += " AND adquirente = ? ";
        sql += " AND vigenciafinal >= ? ";
        sql += " AND vigenciainicial <= ? ";
        try (PreparedStatement sqlConsultar = con.prepareStatement(sql)) {
            sqlConsultar.setInt(1, bandeira);
            sqlConsultar.setInt(2, formaPagamento);
            sqlConsultar.setInt(3, nrMeses);
            sqlConsultar.setInt(4, adquirente);
            sqlConsultar.setDate(5, dataHoje);
            sqlConsultar.setDate(6, dataHoje);
            try (ResultSet tabelaResultado = sqlConsultar.executeQuery()) {
                return montarDadosConsulta(tabelaResultado, nivelMontarDados, this.con);
            }
        }
    }
}

package negocio.facade.jdbc.financeiro;

import negocio.comuns.financeiro.MovParcelaVO;
import negocio.comuns.financeiro.PagamentoMovParcelaVO;
import negocio.comuns.utilitarias.ConsistirException;
import negocio.comuns.utilitarias.Uteis;
import negocio.facade.jdbc.arquitetura.SuperEntidade;
import negocio.facade.jdbc.arquitetura.SuperFacadeJDBC;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.Statement;
import java.util.ArrayList;
import java.util.Iterator;
import java.util.List;

/**
 * Classe de persistência que encapsula todas as operações de manipulação dos dados da classe <code>PagamentoMovParcelaVO</code>.
 * Responsável por implementar operações como incluir, alterar, excluir e consultar pertinentes a classe <code>PagamentoMovParcelaVO</code>.
 * Encapsula toda a interação com o banco de dados.
 * @see PagamentoMovParcelaVO
 * @see SuperEntidade
 * @see MovPagamento
 */
public class PagamentoMovParcela extends SuperEntidade {    

    public PagamentoMovParcela() throws Exception {
        super();
        setIdEntidade("MovPagamento");
    }
    public PagamentoMovParcela(Connection con) throws Exception {
        super(con);
        setIdEntidade("MovPagamento");
    }

    /**
     * Operação responsável por retornar um novo objeto da classe <code>PagamentoMovParcelaVO</code>.
     */
    public PagamentoMovParcelaVO novo() throws Exception {
        incluir(getIdEntidade());
        PagamentoMovParcelaVO obj = new PagamentoMovParcelaVO();
        return obj;
    }

    /**
     * Operação responsável por incluir no banco de dados um objeto da classe <code>PagamentoMovParcelaVO</code>.
     * Primeiramente valida os dados (<code>validarDados</code>) do objeto. Verifica a conexão com o banco de dados e a permissão do usuário
     * para realizar esta operacão na entidade.
     * Isto, através da operação <code>incluir</code> da superclasse.
     * @param obj  Objeto da classe <code>PagamentoMovParcelaVO</code> que será gravado no banco de dados.
     * @exception Exception Caso haja problemas de conexão, restrição de acesso ou validação de dados.
     */
    public void incluir(PagamentoMovParcelaVO obj) throws Exception {
        PagamentoMovParcelaVO.validarDadosComConexao(obj, getCon());
        //incluir(getIdEntidade());
        obj.realizarUpperCaseDados();
        String sql = "INSERT INTO PagamentoMovParcela( movPagamento, movParcela, valorPago, reciboPagamento ) VALUES ( ?, ?, ?, ? )";
        try (PreparedStatement sqlInserir = con.prepareStatement(sql)) {
            if (obj.getMovPagamento() != 0) {
                sqlInserir.setInt(1, obj.getMovPagamento());
            } else {
                sqlInserir.setNull(1, 0);
            }
            if (obj.getMovParcela().getCodigo() != 0) {
                sqlInserir.setInt(2, obj.getMovParcela().getCodigo());
            } else {
                sqlInserir.setNull(2, 0);
            }
            sqlInserir.setDouble(3, obj.getValorPago());
            if (obj.getReciboPagamento().getCodigo() != 0) {
                sqlInserir.setInt(4, obj.getReciboPagamento().getCodigo());
            } else {
                sqlInserir.setNull(4, 0);
            }
            sqlInserir.execute();
        }
        obj.setCodigo(obterValorChavePrimariaCodigo());
        obj.setNovoObj(false);
        if (obj.getMovParcela().getSituacao().equals("PG")) {
            MovParcela movParcela = new MovParcela(con);
            movParcela.alterarSemCommit(obj.getMovParcela(), obj.getReciboPagamento());
            movParcela = null;
        }
    }

    /**
     * Operação responsável por alterar no BD os dados de um objeto da classe <code>PagamentoMovParcelaVO</code>.
     * Sempre utiliza a chave primária da classe como atributo para localização do registro a ser alterado.
     * Primeiramente valida os dados (<code>validarDados</code>) do objeto. Verifica a conexão com o banco de dados e a permissão do usuário
     * para realizar esta operacão na entidade.
     * Isto, através da operação <code>alterar</code> da superclasse.
     * @param obj    Objeto da classe <code>PagamentoMovParcelaVO</code> que será alterada no banco de dados.
     * @exception Exception Caso haja problemas de conexão, restrição de acesso ou validação de dados.
     */
    public void alterar(PagamentoMovParcelaVO obj) throws Exception {
        try {
            PagamentoMovParcelaVO.validarDadosComConexao(obj, getCon());
            alterar(getIdEntidade());
            obj.realizarUpperCaseDados();
            String sql = "UPDATE PagamentoMovParcela set movPagamento=?, movParcela=?, valorPago=?, reciboPagamento= ? WHERE ((codigo = ?))";
            try (PreparedStatement sqlAlterar = con.prepareStatement(sql)) {
                if (obj.getMovPagamento().intValue() != 0) {
                    sqlAlterar.setInt(1, obj.getMovPagamento().intValue());
                } else {
                    sqlAlterar.setNull(1, 0);
                }
                if (obj.getMovParcela().getCodigo().intValue() != 0) {
                    sqlAlterar.setInt(2, obj.getMovParcela().getCodigo().intValue());
                } else {
                    sqlAlterar.setNull(2, 0);
                }
                sqlAlterar.setDouble(3, obj.getValorPago());
                if (obj.getReciboPagamento().getCodigo().intValue() != 0) {
                    sqlAlterar.setInt(4, obj.getReciboPagamento().getCodigo().intValue());
                } else {
                    sqlAlterar.setNull(4, 0);
                }
                sqlAlterar.setInt(5, obj.getCodigo().intValue());
                sqlAlterar.execute();
            }
        } catch (Exception e) {
            throw e;
        }
    }

    /**
     * Operação responsável por excluir no BD um objeto da classe <code>PagamentoMovParcelaVO</code>.
     * Sempre localiza o registro a ser excluído através da chave primária da entidade.
     * Primeiramente verifica a conexão com o banco de dados e a permissão do usuário
     * para realizar esta operacão na entidade.
     * Isto, através da operação <code>excluir</code> da superclasse.
     * @param obj    Objeto da classe <code>PagamentoMovParcelaVO</code> que será removido no banco de dados.
     * @exception Exception Caso haja problemas de conexão ou restrição de acesso.
     */
    public void excluir(PagamentoMovParcelaVO obj) throws Exception {
        excluir(getIdEntidade());
        String sql = "DELETE FROM PagamentoMovParcela WHERE ((codigo = ?))";
        try (PreparedStatement sqlExcluir = con.prepareStatement(sql)) {
            sqlExcluir.setInt(1, obj.getCodigo().intValue());
            sqlExcluir.execute();
        }
    }

    /**
     * Responsável por realizar uma consulta de <code>PagamentoMovParcela</code> através do valor do atributo 
     * <code>codigo</code> da classe <code>MovParcela</code>
     * Faz uso da operação <code>montarDadosConsulta</code> que realiza o trabalho de prerarar o List resultante.
     * @return  List Contendo vários objetos da classe <code>PagamentoMovParcelaVO</code> resultantes da consulta.
     * @exception Exception Caso haja problemas de conexão ou restrição de acesso.
     */
    public List consultarPorReciboPagamneto(Integer valorConsulta, int nivelMontarDados) throws Exception {
        consultar(getIdEntidade(), true);
        String sqlStr = "SELECT PagamentoMovParcela.* FROM PagamentoMovParcela WHERE PagamentoMovParcela.reciboPagamento = " + valorConsulta.intValue() + " ORDER BY PagamentoMovParcela.reciboPagamento ";
        try (Statement stm = con.createStatement()) {
            try (ResultSet tabelaResultado = stm.executeQuery(sqlStr)) {
                return montarDadosConsulta(tabelaResultado, nivelMontarDados, con);
            }
        }
    }

    /**
     * Responsável por realizar uma consulta de <code>PagamentoMovParcela</code> através do valor do atributo 
     * <code>codigo</code> da classe <code>MovParcela</code>
     * Faz uso da operação <code>montarDadosConsulta</code> que realiza o trabalho de prerarar o List resultante.
     * @return  List Contendo vários objetos da classe <code>PagamentoMovParcelaVO</code> resultantes da consulta.
     * @exception Exception Caso haja problemas de conexão ou restrição de acesso.
     */
    public List consultarPorCodigoMovParcela(Integer valorConsulta, int nivelMontarDados) throws Exception {
        consultar(getIdEntidade(), true);
        String sqlStr = "SELECT PagamentoMovParcela.* FROM PagamentoMovParcela, MovParcela WHERE PagamentoMovParcela.movParcela = MovParcela.codigo and MovParcela.codigo = " + valorConsulta.intValue() + " ORDER BY MovParcela.codigo";
        try (Statement stm = con.createStatement()) {
            try (ResultSet tabelaResultado = stm.executeQuery(sqlStr)) {
                return montarDadosConsulta(tabelaResultado, nivelMontarDados, con);
            }
        }
    }

    public Double valorChequesDevolvidosPagamento(Integer codigo) throws Exception{
        try (ResultSet rs = criarConsulta("select SUM(valor) as total from cheque where movpagamento = " + codigo + " and situacao = 'DV' ", con)) {
            if (rs.next()) {
                return rs.getDouble("total");
            }
        }
        return 0.0;

    }

    /**
     * Responsável por realizar uma consulta de <code>PagamentoMovParcela</code> através do valor do atributo 
     * <code>codigo</code> da classe <code>MovPagamento</code>
     * Faz uso da operação <code>montarDadosConsulta</code> que realiza o trabalho de prerarar o List resultante.
     * @return  List Contendo vários objetos da classe <code>PagamentoMovParcelaVO</code> resultantes da consulta.
     * @exception Exception Caso haja problemas de conexão ou restrição de acesso.
     */
    public List consultarPorCodigoMovPagamento(Integer valorConsulta, int nivelMontarDados) throws Exception {
        consultar(getIdEntidade(), true);
        String sqlStr = "SELECT PagamentoMovParcela.* FROM PagamentoMovParcela, MovPagamento WHERE PagamentoMovParcela.movPagamento = MovPagamento.codigo and MovPagamento.codigo >= " + valorConsulta.intValue() + " ORDER BY MovPagamento.codigo";
        try (Statement stm = con.createStatement()) {
            try (ResultSet tabelaResultado = stm.executeQuery(sqlStr)) {
                return montarDadosConsulta(tabelaResultado, nivelMontarDados, con);
            }
        }
    }


    /**
     * Responsável por realizar uma consulta de <code>PagamentoMovParcela</code> através do valor do atributo 
     * <code>Integer codigo</code>. Retorna os objetos com valores iguais ou superiores ao parâmetro fornecido.
     * Faz uso da operação <code>montarDadosConsulta</code> que realiza o trabalho de prerarar o List resultante.
     * @param   controlarAcesso Indica se a aplicação deverá verificar se o usuário possui permissão para esta consulta ou não.
     * @return  List Contendo vários objetos da classe <code>PagamentoMovParcelaVO</code> resultantes da consulta.
     * @exception Exception Caso haja problemas de conexão ou restrição de acesso.
     */
    public List consultarPorCodigo(Integer valorConsulta, boolean controlarAcesso, int nivelMontarDados) throws Exception {
        consultar(getIdEntidade(), controlarAcesso);
        String sqlStr = "SELECT * FROM PagamentoMovParcela WHERE codigo >= " + valorConsulta.intValue() + " ORDER BY codigo";
        try (Statement stm = con.createStatement()) {
            try (ResultSet tabelaResultado = stm.executeQuery(sqlStr)) {
                return (montarDadosConsulta(tabelaResultado, nivelMontarDados, con));
            }
        }
    }

    /**
     * Responsável por montar os dados de vários objetos, resultantes de uma consulta ao banco de dados (<code>ResultSet</code>).
     * Faz uso da operação <code>montarDados</code> que realiza o trabalho para um objeto por vez.
     * @return  List Contendo vários objetos da classe <code>PagamentoMovParcelaVO</code> resultantes da consulta.
     */
    public static List montarDadosConsulta(ResultSet tabelaResultado, int nivelMontarDados, Connection con) throws Exception {
        List vetResultado = new ArrayList();
        while (tabelaResultado.next()) {
            PagamentoMovParcelaVO obj = montarDados(tabelaResultado, nivelMontarDados, con);
            vetResultado.add(obj);
        }
        return vetResultado;
    }

    /**
     * Responsável por montar os dados resultantes de uma consulta ao banco de dados (<code>ResultSet</code>)
     * em um objeto da classe <code>PagamentoMovParcelaVO</code>.
     * @return  O objeto da classe <code>PagamentoMovParcelaVO</code> com os dados devidamente montados.
     */
    public static PagamentoMovParcelaVO montarDados(ResultSet dadosSQL, int nivelMontarDados, Connection con) throws Exception {
        PagamentoMovParcelaVO obj = new PagamentoMovParcelaVO();
        obj.setCodigo(new Integer(dadosSQL.getInt("codigo")));
        obj.setValorPago(dadosSQL.getDouble("valorPago"));
        obj.setMovPagamento(dadosSQL.getInt("movPagamento"));
        obj.getMovParcela().setCodigo(new Integer(dadosSQL.getInt("movParcela")));
        obj.getReciboPagamento().setCodigo(new Integer(dadosSQL.getInt("reciboPagamento")));
        obj.setNovoObj(false);
        if (nivelMontarDados == Uteis.NIVELMONTARDADOS_DADOSBASICOS) {
            return obj;
        }
        if (nivelMontarDados == Uteis.NIVELMONTARDADOS_MOVPAGAMENTOSINTETICO) {
            return obj;
        }
        montarDadosMovParcela(obj, Uteis.NIVELMONTARDADOS_DADOSBASICOS, con);
        return obj;
    }

    /**
     * Operação responsável por montar os dados de um objeto da classe <code>MovParcelaVO</code> relacionado ao objeto <code>PagamentoMovParcelaVO</code>.
     * Faz uso da chave primária da classe <code>MovParcelaVO</code> para realizar a consulta.
     * @param obj  Objeto no qual será montado os dados consultados.
     * @param con
     */
    public static void montarDadosMovParcela(PagamentoMovParcelaVO obj, int nivelMontarDados, Connection con) throws Exception {
        if (obj.getMovParcela().getCodigo() == 0) {
            obj.setMovParcela(new MovParcelaVO());
            return;
        }
        MovParcela movParcela = new MovParcela(con);
        obj.setMovParcela(movParcela.consultarPorChavePrimaria(obj.getMovParcela().getCodigo(), nivelMontarDados));
        movParcela = null;
    }

    /**
     * Operação responsável por excluir todos os objetos da <code>PagamentoMovParcelaVO</code> no BD.
     * Faz uso da operação <code>excluir</code> disponível na classe <code>PagamentoMovParcela</code>.
     * @param movPagamento campo chave para exclusão dos objetos no BD.
     * @exception Exception  Erro de conexão com o BD ou restrição de acesso a esta operação.
     */
    public void excluirPagamentoMovParcelas(Integer movPagamento) throws Exception {
        String sql = "DELETE FROM PagamentoMovParcela WHERE (movPagamento = ?)";
        try (PreparedStatement sqlExcluir = con.prepareStatement(sql)) {
            sqlExcluir.setInt(1, movPagamento);
            sqlExcluir.execute();
        }
    }

    /**
     * Operação responsável por alterar todos os objetos da <code>PagamentoMovParcelaVO</code> contidos em um Hashtable no BD.
     * Faz uso da operação <code>excluirPagamentoMovParcelas</code> e <code>incluirPagamentoMovParcelas</code> disponíveis na classe <code>PagamentoMovParcela</code>.
     * @param objetos  List com os objetos a serem alterados ou incluídos no BD.
     * @param recibo 
     * @exception Exception  Erro de conexão com o BD ou restrição de acesso a esta operação.
     */
    public void alterarPagamentoMovParcelas(Integer movPagamento, List objetos, Integer recibo) throws Exception {
        try {
        excluirPagamentoMovParcelas(movPagamento);
        incluirPagamentoMovParcelas(movPagamento, objetos, recibo);
        } catch (Exception e) {
            throw e;
        }

        //        excluirPagamentoMovParcelas(movPagamento);
//        incluirPagamentoMovParcelas(movPagamento, objetos);
    }

    /**
     * Operação responsável por incluir objetos da <code>PagamentoMovParcelaVO</code> no BD.
     * Garantindo o relacionamento com a entidade principal <code>financeiro.MovPagamento</code> através do atributo de vínculo.
     * @param objetos List contendo os objetos a serem gravados no BD da classe.
     * @exception Exception  Erro de conexão com o BD ou restrição de acesso a esta operação.
     */
    public void incluirPagamentoMovParcelas(Integer movPagamentoPrm, List objetos, Integer codigoReciboPagamento) throws Exception {
        try {
            Iterator e = objetos.iterator();
            while (e.hasNext()) {
                PagamentoMovParcelaVO obj = (PagamentoMovParcelaVO) e.next();
                obj.setMovPagamento(movPagamentoPrm);
                obj.getReciboPagamento().setCodigo(codigoReciboPagamento);
                incluir(obj);
            }
        } catch (Exception e) {
            throw e;
        }

    }

    /**
     * Operação responsável por consultar todos os <code>PagamentoMovParcelaVO</code> relacionados a um objeto da classe <code>financeiro.MovPagamento</code>.
     * @param movPagamento  Atributo de <code>financeiro.MovPagamento</code> a ser utilizado para localizar os objetos da classe <code>PagamentoMovParcelaVO</code>.
     * @return List  Contendo todos os objetos da classe <code>PagamentoMovParcelaVO</code> resultantes da consulta.
     * @exception Exception  Erro de conexão com o BD ou restrição de acesso a esta operação.
     */
    public List consultarPagamentoMovParcelas(Integer movPagamento, int nivelMontarDados) throws Exception {
        consultar(getIdEntidade());
        List<PagamentoMovParcelaVO> objetos = new ArrayList<>();
        String sql = "SELECT * FROM PagamentoMovParcela WHERE movPagamento = ?";
        try (PreparedStatement sqlConsulta = con.prepareStatement(sql)) {
            sqlConsulta.setInt(1, movPagamento);
            try (ResultSet resultado = sqlConsulta.executeQuery()) {
                while (resultado.next()) {
                    PagamentoMovParcelaVO novoObj = montarDados(resultado, nivelMontarDados, con);
                    objetos.add(novoObj);
                }
            }
        }
        return objetos;
    }

    /**
     * Operação responsável por localizar um objeto da classe <code>PagamentoMovParcelaVO</code>
     * através de sua chave primária. 
     * @exception Exception Caso haja problemas de conexão ou localização do objeto procurado.
     */
    public PagamentoMovParcelaVO consultarPorChavePrimaria(Integer codigoPrm, int nivelMontarDados) throws Exception {
        consultar(getIdEntidade(), false);
        String sql = "SELECT * FROM PagamentoMovParcela WHERE codigo = ?";
        try (PreparedStatement sqlConsultar = con.prepareStatement(sql)) {
            sqlConsultar.setInt(1, codigoPrm.intValue());
            try (ResultSet tabelaResultado = sqlConsultar.executeQuery()) {
                if (!tabelaResultado.next()) {
                    throw new ConsistirException("Dados Não Encontrados ( PagamentoMovParcela ).");
                }
                return (montarDados(tabelaResultado, nivelMontarDados, con));
            }
        }
    } 
    
    /**
     * Operacao que pesquisa o valor que um pagamento paga de um contrato
     * 
     */
    public Double consultarValorPagoContratoPagamento(Integer contrato, Integer movPagamento) throws Exception {
        consultar(getIdEntidade(), true);
        String sqlStr = "SELECT sum(valorpago) as valor FROM PagamentoMovParcela WHERE movparcela in (select codigo from movparcela where contrato = " + contrato.intValue() +
        ") and movpagamento = " + movPagamento.intValue();
        try (Statement stm = con.createStatement()) {
            try (ResultSet tabelaResultado = stm.executeQuery(sqlStr)) {
                tabelaResultado.next();
                return tabelaResultado.getDouble("valor");
            }
        }
    }

    /**
     * Remover os vinculos das PagamentoMovParcela caso não tenha parcela e valor pago seja zerado
     */
    public static void corrigirPagamentosSemMovParcelas(Connection con) throws Exception {
        SuperFacadeJDBC.executarUpdateExecutarProcessos("DELETE FROM PagamentoMovParcela WHERE codigo in " +
                "(SELECT codigo FROM PagamentoMovParcela WHERE valorpago = 0 and not exists(select codigo from movparcela where codigo = PagamentoMovParcela.movparcela))", con);
    }

    public Boolean existeReciboPagoPorPix(Integer codigoMovParcela) throws Exception {
        String sqlStr = "SELECT EXISTS ( SELECT p.recibopagamento FROM pix p INNER JOIN pixmovparcela pmi ON pmi.pix = p.codigo INNER JOIN pagamentomovparcela pmp ON pmp.movparcela = pmi.movparcela \n";
        sqlStr = sqlStr + "WHERE p.recibopagamento IS NOT NULL AND pmp.movparcela = " + codigoMovParcela + " ) AS pago;";
        try (Statement stm = con.createStatement()) {
            try (ResultSet tabelaResultado = stm.executeQuery(sqlStr)) {
                tabelaResultado.next();
                return tabelaResultado.getBoolean("pago");
            }
        }
    }

}

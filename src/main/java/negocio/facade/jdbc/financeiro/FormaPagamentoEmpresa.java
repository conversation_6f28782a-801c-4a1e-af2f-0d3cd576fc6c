package negocio.facade.jdbc.financeiro;

import negocio.comuns.financeiro.FormaPagamentoEmpresaVO;
import negocio.comuns.financeiro.FormaPagamentoVO;
import negocio.comuns.financeiro.TaxaCartaoVO;
import negocio.comuns.financeiro.enumerador.TipoFormaPagto;
import negocio.comuns.utilitarias.Calendario;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisValidacao;
import negocio.facade.jdbc.arquitetura.SuperEntidade;
import negocio.interfaces.financeiro.FormaPagamentoEmpresaInterfaceFacade;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.Types;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Date;
import java.util.List;

public class FormaPagamentoEmpresa  extends SuperEntidade implements FormaPagamentoEmpresaInterfaceFacade {

    public FormaPagamentoEmpresa() throws Exception {
        super();
    }

    public FormaPagamentoEmpresa(Connection con) throws Exception {
        super(con);
    }

    public void incluir(FormaPagamentoVO fp, List<FormaPagamentoEmpresaVO> empresas) throws Exception{
        for(FormaPagamentoEmpresaVO fpe : empresas){
            fpe.setFormaPagamento(fp);
            if(UteisValidacao.emptyNumber(fpe.getCodigo())){
                incluirSemCommit(fpe);
            }else{
                alterarSemCommit(fpe);
            }
        }
    }

    public void incluir(FormaPagamentoEmpresaVO obj) throws Exception{
        try {
            con.setAutoCommit(false);
            incluirSemCommit(obj);
            con.commit();
        } catch (Exception e) {
            con.rollback();
            con.setAutoCommit(true);
            throw e;
        } finally {
            con.setAutoCommit(true);
        }
    }

    public void incluirSemCommit(FormaPagamentoEmpresaVO obj) throws Exception{
        incluir("FormaPagamento");
        String insert = "INSERT INTO formapagamentoempresa (empresa, formapagamento, contadestino) values (?,?, ?)";
        PreparedStatement insertStm = con.prepareStatement(insert);
        insertStm.setInt(1, obj.getEmpresa().getCodigo());
        insertStm.setInt(2, obj.getFormaPagamento().getCodigo());
        if(UteisValidacao.emptyNumber(obj.getContaDestino().getCodigo())){
            insertStm.setNull(3, Types.NULL);
        }else{
            insertStm.setInt(3, obj.getContaDestino().getCodigo());
        }
        insertStm.execute();
        obj.setCodigo(obterValorChavePrimariaCodigo());
    }

    public void alterarSemCommit(FormaPagamentoEmpresaVO obj) throws Exception{
        alterar("FormaPagamento");
        String update = "UPDATE formapagamentoempresa SET empresa = ? , formapagamento = ?, contadestino = ? WHERE codigo = ?";
        PreparedStatement updateStm = con.prepareStatement(update);
        updateStm.setInt(1, obj.getEmpresa().getCodigo());
        updateStm.setInt(2, obj.getFormaPagamento().getCodigo());
        if(UteisValidacao.emptyNumber(obj.getContaDestino().getCodigo())){
            updateStm.setNull(3, Types.NULL);
        }else{
            updateStm.setInt(3, obj.getContaDestino().getCodigo());
        }
        updateStm.setInt(4, obj.getCodigo());
        updateStm.execute();

    }

    public void alterar(FormaPagamentoEmpresaVO obj) throws Exception{
        try {
            con.setAutoCommit(false);
            alterarSemCommit(obj);
            con.commit();
        } catch (Exception e) {
            con.rollback();
            con.setAutoCommit(true);
            throw e;
        } finally {
            con.setAutoCommit(true);
        }
    }

    public void excluir(FormaPagamentoEmpresaVO obj) throws Exception{
        String sql = "DELETE FROM FormaPagamentoEmpresa WHERE codigo = ?";
        PreparedStatement sqlExcluir = con.prepareStatement(sql);
        sqlExcluir.setInt(1, obj.getCodigo());
        sqlExcluir.execute();
    }

    public void excluirPorEmpresa(final Integer formaPagamento, final Integer empresa) throws Exception{
        String sql = "DELETE FROM FormaPagamentoEmpresa WHERE empresa = ? and formapagamento = ? ";
        PreparedStatement sqlExcluir = con.prepareStatement(sql);
        sqlExcluir.setInt(1, empresa);
        sqlExcluir.setInt(2, formaPagamento);
        sqlExcluir.execute();
    }

    public List<FormaPagamentoEmpresaVO> consultarPorForma(Integer forma) throws Exception{
        List<FormaPagamentoEmpresaVO> formasEmpresa = new ArrayList<FormaPagamentoEmpresaVO>();
        ResultSet rs = criarConsulta("SELECT * FROM formapagamentoempresa WHERE formapagamento = " + forma, con);
        while(rs.next()){
            formasEmpresa.add(montarDados(rs));
        }
        return formasEmpresa;
    }

    public FormaPagamentoEmpresaVO montarDados(ResultSet rs) throws Exception{
        FormaPagamentoEmpresaVO obj = new FormaPagamentoEmpresaVO();
        obj.setCodigo(rs.getInt("codigo"));
        ResultSet rsEmpresa = criarConsulta("SELECT codigo, nome FROM empresa WHERE codigo = " + rs.getInt("empresa"), con);
        if(rsEmpresa.next()){
            obj.getEmpresa().setNome(rsEmpresa.getString("nome"));
            obj.getEmpresa().setCodigo(rsEmpresa.getInt("codigo"));
        }
        ResultSet rsForma = criarConsulta("SELECT codigo, descricao FROM formapagamento WHERE codigo = " + rs.getInt("formapagamento"), con);
        if(rsForma.next()){
            obj.getFormaPagamento().setDescricao(rsForma.getString("descricao"));
            obj.getFormaPagamento().setCodigo(rsForma.getInt("codigo"));

        }

        try {
            ContaCorrente contaCorrenteDAO = new ContaCorrente(con);
            obj.setContaDestino(contaCorrenteDAO.consultarPorChavePrimaria(rs.getInt("contadestino"), Uteis.NIVELMONTARDADOS_TELACONSULTA));
            contaCorrenteDAO = null;
        } catch (Exception ignored) {}

        return obj;
    }

    public Double descrobrirTaxaApropriada(int nrVezes, Date dataLancamento, Integer adquirente, Integer operadora,
                                           FormaPagamentoVO formaPagamentoVO, Integer empresa) throws Exception {
        return descrobrirTaxaApropriada(nrVezes, dataLancamento, adquirente, operadora, formaPagamentoVO, empresa, false);
    }
    public Double descrobrirTaxaApropriada(int nrVezes, Date dataLancamento, Integer adquirente, Integer operadora,
                                           FormaPagamentoVO formaPagamentoVO, Integer empresa, boolean antiloop) throws Exception {

        adquirente = adquirente == null ? 0 : adquirente;
        operadora = operadora == null ? 0 : operadora;

        if(UteisValidacao.emptyList(formaPagamentoVO.getFormasEmpresas())){
            formaPagamentoVO.setFormasEmpresas(consultarPorForma(formaPagamentoVO.getCodigo()));
        }

        Double taxa = 0.0;
        TaxaCartao taxaCartao = new TaxaCartao(con);
        List<TaxaCartaoVO> taxas = taxaCartao.consultarPorFormaPagamento(formaPagamentoVO.getCodigo(),null,Uteis.NIVELMONTARDADOS_DADOSBASICOS);
        if (!UteisValidacao.emptyList(taxas)){
            for(TaxaCartaoVO ta : taxas){
                if(ta.getVigenciaInicial() == null ||
                        Calendario.maior(ta.getVigenciaInicial(), dataLancamento) ||
                        (ta.getVigenciaFinal() != null && Calendario.menor(ta.getVigenciaFinal(), dataLancamento))){
                    //fora da vigencia
                    continue;
                }
                if(ta.getAdquirenteVO().getCodigo().equals(adquirente)
                        && ta.getBandeira().getCodigo().equals(operadora)
                        && (ta.getNrmeses() == nrVezes || formaPagamentoVO.getTipoFormaPagamento().equals(TipoFormaPagto.CARTAODEBITO.getSigla()))){
                    taxa = ta.getTaxa();
                }
            }
        }

        for(FormaPagamentoEmpresaVO fpe : formaPagamentoVO.getFormasEmpresas()){

            if(!fpe.getEmpresa().getCodigo().equals(empresa)){
                continue;
            }

            if(UteisValidacao.emptyList(fpe.getTaxas())){
                fpe.setTaxas(getFacade().getTaxaCartao().consultarPorFormaPagamentoEmpresa(fpe.getCodigo(), dataLancamento,
                        fpe.getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS));
            }

            for(TaxaCartaoVO ta : fpe.getTaxas()){
                if(ta.getVigenciaInicial() == null ||
                        Calendario.maior(ta.getVigenciaInicial(), dataLancamento) ||
                        (ta.getVigenciaFinal() != null && Calendario.menor(ta.getVigenciaFinal(), dataLancamento))){
                    //fora da vigencia
                    continue;
                }
                if(ta.getAdquirenteVO().getCodigo().equals(adquirente)
                        && ta.getBandeira().getCodigo().equals(operadora)
                        && (ta.getNrmeses() == nrVezes || formaPagamentoVO.getTipoFormaPagamento().equals(TipoFormaPagto.CARTAODEBITO.getSigla()))){
                    taxa = ta.getTaxa();
                }
            }
        }


        if(UteisValidacao.emptyNumber(taxa) && !antiloop && !UteisValidacao.emptyNumber(adquirente) && UteisValidacao.emptyNumber(operadora)){
            taxa = descrobrirTaxaApropriada(nrVezes, dataLancamento, adquirente, 0,
                    formaPagamentoVO, empresa, true);
        }

        if(UteisValidacao.emptyNumber(taxa) && !antiloop && UteisValidacao.emptyNumber(adquirente) && !UteisValidacao.emptyNumber(operadora)){
            taxa = descrobrirTaxaApropriada(nrVezes, dataLancamento, 0, operadora,
                    formaPagamentoVO, empresa, true);
        }


        if(UteisValidacao.emptyNumber(taxa)){
            taxa = formaPagamentoVO.getTaxaCartao(nrVezes, dataLancamento, adquirente, operadora);
        }

        if(UteisValidacao.emptyNumber(taxa) && !antiloop){
            taxa = descrobrirTaxaApropriada(nrVezes, dataLancamento, 0, 0,
                    formaPagamentoVO, empresa, true);
        }

        if(UteisValidacao.emptyNumber(taxa)){
            taxa = formaPagamentoVO.getTaxaCartao(nrVezes, dataLancamento, 0, 0);
        }

        if (UteisValidacao.emptyNumber((taxa)) && formaPagamentoVO.getTipoFormaPagamento().equals(TipoFormaPagto.PIX.getSigla())){
            taxa= getFacade().getFormaPagamento().consultarPorTipoFormaPagamento( TipoFormaPagto.PIX.getSigla(),  Uteis.NIVELMONTARDADOS_DADOSBASICOS).getTaxaPix();
        }
        return taxa;
    }

}

/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */

package negocio.facade.jdbc.financeiro;

import br.com.pactosolucoes.enumeradores.TipoOperacaoLancamento;
import br.com.pactosolucoes.estrutura.paginacao.ConfPaginacao;
import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.List;
import negocio.comuns.financeiro.CaixaMovContaVO;
import negocio.comuns.financeiro.CaixaVO;
import negocio.comuns.financeiro.MovContaVO;
import negocio.comuns.utilitarias.Calendario;
import negocio.comuns.utilitarias.Uteis;
import negocio.facade.jdbc.arquitetura.SuperEntidade;
import negocio.interfaces.financeiro.CaixaMovContaInterfaceFacade;

/**
 *
 * <AUTHOR>
 */
public class CaixaMovConta extends SuperEntidade implements CaixaMovContaInterfaceFacade {

    public CaixaMovConta() throws Exception {
        super();
    }

    public CaixaMovConta(Connection con) throws Exception {
        super(con);
    }

    public void incluir(CaixaMovContaVO obj) throws Exception {
        StringBuilder sb = new StringBuilder();
        sb.append("insert into caixaMovConta(caixa, movConta, descricao, valor, dataMovimento) ");
        sb.append("values(?,?,?,?,?)");
        PreparedStatement pst = this.con.prepareStatement(sb.toString());
        pst.setInt(1, obj.getCaixaVo().getCodigo());
        pst.setInt(2, obj.getMovContaVo().getCodigo());
        pst.setString(3, obj.getDescricao());
        pst.setDouble(4, obj.getValor());
        pst.setTimestamp(5, Uteis.getDataJDBCTimestamp(Calendario.hoje()));
        pst.execute();
        obj.setCodigo(obterValorChavePrimariaCodigo());
   }

    public void incluirOrigemProcessoCopiarContas(CaixaMovContaVO obj) throws Exception {
        StringBuilder sb = new StringBuilder();
        sb.append("insert into caixaMovConta(codigo, caixa, movConta, descricao, valor, dataMovimento) ");
        sb.append("values(?,?,?,?,?,?)");
        PreparedStatement pst = this.con.prepareStatement(sb.toString());
        pst.setInt(1, obj.getCodigo());
        pst.setInt(2, obj.getCaixaVo().getCodigo());
        pst.setInt(3, obj.getMovContaVo().getCodigo());
        pst.setString(4, obj.getDescricao());
        pst.setDouble(5, obj.getValor());
        pst.setTimestamp(6, Uteis.getDataJDBCTimestamp(Calendario.hoje()));
        pst.execute();
    }

    public void excluir(CaixaMovContaVO obj) throws Exception {
        StringBuilder sb = new StringBuilder();
        sb.append("delete from caixaMovConta where codigo = ").append(obj.getCodigo());
        PreparedStatement pst = this.con.prepareStatement(sb.toString());
        pst.execute();
   }
    


    public void alterar(CaixaMovContaVO obj) throws Exception {
        StringBuilder sb = new StringBuilder();
        sb.append("update caixaMovConta set movConta=?, descricao =?, valor=? where codigo =? ");
        PreparedStatement pst = this.con.prepareStatement(sb.toString());
        pst.setInt(1, obj.getMovContaVo().getCodigo());
        pst.setString(2, obj.getDescricao());
        pst.setDouble(3, obj.getValor());
        pst.setInt(4, obj.getCodigo());
        pst.execute();
   }

   public void alterarDescricao(int movConta, String descricao) throws Exception {
        StringBuilder sb = new StringBuilder();
        sb.append("update caixaMovConta set descricao =? where movconta =? ");
        PreparedStatement pst = this.con.prepareStatement(sb.toString());
        pst.setString(1, descricao);
        pst.setInt(2, movConta);
        pst.execute();
    }


   private CaixaMovContaVO montarDadosBasico(ResultSet dados)throws Exception{
       CaixaMovContaVO obj = new CaixaMovContaVO();
       obj.setCodigo(dados.getInt("codigo"));
       obj.getCaixaVo().setCodigo(dados.getInt("caixa"));
       obj.getMovContaVo().setCodigo(dados.getInt("movConta"));
       obj.setDescricao(dados.getString("descricao"));
       obj.setValor(dados.getDouble("valor"));
       obj.setDataMovimento(dados.getTimestamp("dataMovimento"));

       return obj;

   }

   private CaixaMovContaVO montarDados(ResultSet dados, int nivelMontarDados)throws Exception{
      CaixaMovContaVO obj = montarDadosBasico(dados);

      if (nivelMontarDados == Uteis.NIVELMONTARDADOS_TODOS){
          obj.getMovContaVo().setTipoOperacaoLancamento(TipoOperacaoLancamento.getTipoOperacaoLancamento(dados.getInt("tipoOperacao")));
          obj.getMovContaVo().getPessoaVO().setNome(dados.getString("nomePessoa"));
          obj.getMovContaVo().getContaVO().setDescricao(dados.getString("nomeConta"));
          obj.getMovContaVo().setApresentarNoCaixa(dados.getBoolean("apresentarnocaixa"));
          obj.getMovContaVo().setDataLancamento(dados.getTimestamp("dataLancamento"));
          obj.getMovContaVo().setDataQuitacao(dados.getTimestamp("dataquitacao"));
          try {
                  obj.getMovContaVo().getEmpresaVO().setCodigo(dados.getInt("empresa"));
          }catch (Exception ignore){}
      }
       return obj;

   }

    private String retornarSelect(int nivelMontarDados)throws Exception{
        StringBuilder sql = new StringBuilder();
        sql.append("select cmc.codigo, cmc.caixa, cmc.movConta,");
        sql.append(" cmc.descricao, cmc.valor, cmc.dataMovimento ");
        if (nivelMontarDados == Uteis.NIVELMONTARDADOS_TODOS){
          sql.append(" ,p.nome as nomePessoa, ct.descricao as nomeConta, mc.empresa, mc.datalancamento, mc.dataquitacao, mc.tipoOperacao, mc.apresentarnocaixa ");
        }
        sql.append(" FROM caixaMovConta cmc ");
        if (nivelMontarDados == Uteis.NIVELMONTARDADOS_TODOS){
           sql.append(" inner join movConta mc on mc.codigo = cmc.movConta ");
           sql.append(" inner join pessoa p on p.codigo = mc.pessoa ");
           sql.append(" inner join conta ct on ct.codigo = mc.conta ");
        }
        return sql.toString();

    }

    public List<CaixaMovContaVO> consultar(CaixaVO caixaVo, int nivelMontarDados)throws Exception{
       StringBuilder sb = new StringBuilder();
       sb.append(retornarSelect(nivelMontarDados)).append(" where caixa =").append(caixaVo.getCodigo());
       PreparedStatement pst = this.con.prepareStatement(sb.toString());
       ResultSet resultDados = pst.executeQuery();
       return montarDadosConsulta(resultDados, nivelMontarDados);
    }

    public CaixaMovContaVO consultarPorMovConta(MovContaVO movConta) throws Exception{
    	ResultSet query = con.prepareStatement("SELECT * FROM caixamovconta where movconta = "+movConta.getCodigo()).executeQuery();
    	if(query.next()){
    		return montarDados(query, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
    	}else{
    		return new CaixaMovContaVO();
    	}
    	
    }
    
    private List<CaixaMovContaVO> montarDadosConsulta(ResultSet resultDados, int nivelMontarDados)throws Exception{
        List<CaixaMovContaVO> lista = new ArrayList<CaixaMovContaVO>();
         while (resultDados.next()){
           lista.add(montarDados(resultDados, nivelMontarDados));
         }
        return lista;
    }

    public List<CaixaMovContaVO> consultarPaginado(int codigoCaixa, int nivelMontarDados, ConfPaginacao confPaginacao) throws Exception{
        //1- CONFIGURANDO A NAVEGACAO DA PAGINACAO
        confPaginacao.configurarNavegacao();
        StringBuffer sql = new StringBuffer();
        sql.append(retornarSelect(nivelMontarDados));
        sql.append(" WHERE caixa =").append(codigoCaixa);
        sql.append(" ORDER BY dataMovimento DESC ");
        //2 - CONFIGURA A INICIALIZACAO DA PAGINACAO PARA ESTA DAO.
        confPaginacao.iniciarPaginacao(this);
        //3 - ADICIONA PAGINACAO NA CONSULTA
        confPaginacao.addPaginacao(sql);
        //4 - REALIZA A CONSULTA COM PAGINACAO
        ResultSet resultDados = confPaginacao.consultaPaginada();
        return montarDadosConsulta(resultDados, nivelMontarDados);
        
    }

	@Override
	public void excluirPorMovConta(int movConta) throws Exception {
	   StringBuilder sb = new StringBuilder();
	   sb.append("delete from caixaMovConta where movconta = ").append(movConta);
	   PreparedStatement pst = this.con.prepareStatement(sb.toString());
	   pst.execute();
	}
	
	public boolean movContaJaIncluido(int codigoMovConta) throws SQLException{
		String sql = "select * from caixaMovConta where movconta = "+codigoMovConta;
		PreparedStatement pst = this.con.prepareStatement(sql);
		ResultSet resultSet = pst.executeQuery();
		return resultSet.next();
	}
	
	public CaixaVO movContaCaixaAberto(Integer codigoMovConta) throws Exception{
		CaixaVO caixa = new CaixaVO();
		StringBuilder sql = new StringBuilder();
		sql.append("SELECT c.datafechamento, c.codigo FROM caixa c inner join caixamovconta cmc ");
		sql.append("ON c.codigo = cmc.caixa ");
		sql.append("AND cmc.movconta = "+codigoMovConta);
		ResultSet resultSet = criarConsulta(sql.toString(), con);
		if(resultSet.next()){
			caixa.setCodigo(resultSet.getInt("codigo"));
			caixa.setDataFechamento(resultSet.getDate("datafechamento"));
		}
		return caixa;
	}

    public CaixaVO consultarPorMovConta(Integer codigoMovConta) throws Exception{
        CaixaVO caixa = new CaixaVO();
        StringBuilder sql = new StringBuilder();
        sql.append("SELECT * FROM caixa c inner join caixamovconta cmc ");
        sql.append("ON c.codigo = cmc.caixa ");
        sql.append("AND cmc.movconta = " + codigoMovConta);
        ResultSet resultSet = criarConsulta(sql.toString(), con);
        if(resultSet.next()){
            caixa.setCodigo(resultSet.getInt("codigo"));
            caixa.setDataFechamento(resultSet.getDate("datafechamento"));
        }
        return caixa;
    }

    public int validarCaixaAberto(MovContaVO movContaVO) throws Exception {
        try {
            final StringBuilder sqlCaixa =
                    new StringBuilder(" select cmc.caixa                                        ")
                            .append("   from caixamovconta cmc                                  ")
                            .append("       inner join movconta mc on mc.codigo = cmc.movconta  ")
                            .append("       inner join caixa c on c.codigo = cmc.caixa          ")
                            .append("   where mc.codigo = ").append(movContaVO.getCodigo())
                            .append("       and c.datafechamento is not null                    ");

            final ResultSet resultSet = criarConsulta(sqlCaixa.toString(), con);
            if (resultSet.next()) {
                return resultSet.getInt("caixa");
            }

        } catch (Exception ex) {
        }
        return 0;
    }
}

package negocio.facade.jdbc.financeiro;

import br.com.pactosolucoes.comuns.util.FileUtilities;
import br.com.pactosolucoes.pjbank.WebhookPJBankJSON;
import controle.arquitetura.SuperControle;
import negocio.comuns.arquitetura.UsuarioVO;
import negocio.comuns.basico.BoletoEmailTO;
import negocio.comuns.basico.ClienteVO;
import negocio.comuns.basico.ColaboradorVO;
import negocio.comuns.basico.EmpresaVO;
import negocio.comuns.basico.EnderecoVO;
import negocio.comuns.basico.MovimentoContaCorrenteClienteVO;
import negocio.comuns.basico.PessoaVO;
import negocio.comuns.contrato.ContratoVO;
import negocio.comuns.crm.ConfiguracaoSistemaCRMVO;
import negocio.comuns.financeiro.BoletoPJBankVO;
import negocio.comuns.financeiro.BoletoVO;
import negocio.comuns.financeiro.ConvenioCobrancaVO;
import negocio.comuns.financeiro.EstornoMovProdutoVO;
import negocio.comuns.financeiro.FormaPagamentoVO;
import negocio.comuns.financeiro.LogPJBankVO;
import negocio.comuns.financeiro.MovPagamentoVO;
import negocio.comuns.financeiro.MovParcelaVO;
import negocio.comuns.financeiro.ReciboPagamentoVO;
import negocio.comuns.financeiro.VendaAvulsaVO;
import negocio.comuns.financeiro.enumerador.TipoBoletoEnum;
import negocio.comuns.utilitarias.Calendario;
import negocio.comuns.utilitarias.ConsistirException;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisEmail;
import negocio.comuns.utilitarias.UteisValidacao;
import negocio.facade.jdbc.arquitetura.SuperEntidade;
import negocio.facade.jdbc.arquitetura.SuperFacadeJDBC;
import negocio.facade.jdbc.arquitetura.Usuario;
import negocio.facade.jdbc.arquitetura.ZillyonWebFacade;
import negocio.facade.jdbc.basico.Cliente;
import negocio.facade.jdbc.basico.Colaborador;
import negocio.facade.jdbc.basico.Empresa;
import negocio.facade.jdbc.basico.LogPJBank;
import negocio.facade.jdbc.basico.MovimentoContaCorrenteCliente;
import negocio.facade.jdbc.basico.Pessoa;
import negocio.facade.jdbc.crm.Feriado;
import negocio.interfaces.financeiro.BoletoPJBankInterfaceFacade;
import org.json.JSONArray;
import org.json.JSONObject;
import relatorio.negocio.comuns.financeiro.DescontoBoletoTO;
import servicos.impl.boleto.BoletoOnlineService;
import servicos.integracao.pjbank.bean.Endereco;
import servicos.integracao.pjbank.beanRecebimento.BoletoRecebimento;
import servicos.integracao.pjbank.recebimento.BoletosManager;
import servicos.interfaces.BoletoOnlineServiceInterface;
import servicos.operacoes.midias.MidiaService;
import servicos.operacoes.midias.commons.MidiaEntidadeEnum;
import servicos.propriedades.PropsService;
import servicos.util.ExecuteRequestHttpService;

import java.io.File;
import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.sql.Statement;
import java.sql.Types;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

public class BoletoPJBank extends SuperEntidade implements BoletoPJBankInterfaceFacade {

    public BoletoPJBank() throws Exception {
        super();
    }

    public BoletoPJBank(Connection con) throws Exception {
        super(con);
    }

    /**
     * Operação responsável por retornar um novo objeto da classe <code>BoletoPJBankVO</code>.
     */
    public BoletoPJBankVO novo() throws Exception {
        incluir(getIdEntidade());
        return new BoletoPJBankVO();
    }

    public void incluir(BoletoPJBankVO obj) throws Exception {
        try {
            // BoletoPJBankVO.validarDados(obj); TODO criar um validador para os dados
            String sql = "INSERT INTO boletospjbank(movparcela, conveniocobranca, dataregistro, datavencimento, datacredito, valor, juros, multa, desconto, pedidonumero," +
                    "webhook, banco, tokenfacilitador, linkgrupo, idunico, idunicooriginal, linkboleto, linhadigitavel, valorpago, valorliquido, valortarifa," +
                    "pagamentoduplicado, nossonumero, nossonumerooriginal, registrosistemabancario, registroregeicaomotivo, pedidoNumeroPJBank, pessoa, chavePJBank, " +
                    "credencialPJBank, dataatualizacao, diaDoMesDescontoBoletoPagAntecipado, porcentagemDescontoBoletoPagAntecipado, multaValorFixo, jurosValorFixo) " +
                    "VALUES (?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)";
            try (PreparedStatement sqlInserir = con.prepareStatement(sql, Statement.RETURN_GENERATED_KEYS)) {
                int i = 0;
                sqlInserir.setInt(++i, obj.getMovParcelaVO().getCodigo());
                sqlInserir.setInt(++i, obj.getConvenioCobrancaVO().getCodigo());
                sqlInserir.setTimestamp(++i, Uteis.getDataJDBCTimestamp(obj.getDataRegistro()));
                sqlInserir.setDate(++i, Uteis.getDataJDBC(obj.getDataVencimento()));
                sqlInserir.setDate(++i, Uteis.getDataJDBC(obj.getDataCredito()));
                sqlInserir.setFloat(++i, obj.getValor());
                sqlInserir.setFloat(++i, obj.getJuros());
                sqlInserir.setFloat(++i, obj.getMulta());
                sqlInserir.setFloat(++i, obj.getDesconto());
                sqlInserir.setInt(++i, obj.getPedidoNumero());
                sqlInserir.setString(++i, obj.getWebhook());
                sqlInserir.setString(++i, obj.getBanco());
                sqlInserir.setString(++i, obj.getTokenFacilitador());
                sqlInserir.setString(++i, obj.getLinkGrupo());
                sqlInserir.setString(++i, obj.getIdUnico());
                sqlInserir.setString(++i, obj.getIdUnicoOriginal());
                sqlInserir.setString(++i, obj.getLinkBoleto());
                sqlInserir.setString(++i, obj.getLinhaDigitavel());
                sqlInserir.setFloat(++i, obj.getValorPago());
                sqlInserir.setFloat(++i, obj.getValorLiquido());
                sqlInserir.setFloat(++i, obj.getValorTarifa());
                sqlInserir.setBoolean(++i, obj.isPagamentoDuplicado());
                sqlInserir.setString(++i, obj.getNossoNumero());
                sqlInserir.setString(++i, obj.getNossoNumeroOriginal());
                sqlInserir.setString(++i, obj.getRegistroSistemaBancario());
                sqlInserir.setString(++i, obj.getRegistroRegeicaoMotivo());
                sqlInserir.setString(++i, obj.getPedidoNumeroPJBank());

                if (!UteisValidacao.emptyNumber(obj.getPessoaVO().getCodigo())) {
                    sqlInserir.setInt(++i, obj.getPessoaVO().getCodigo());
                } else {
                    sqlInserir.setNull(++i, 0);
                }

                sqlInserir.setString(++i, obj.getChavePJBank());
                sqlInserir.setString(++i, obj.getCredencialPJBank());
                sqlInserir.setTimestamp(++i, Uteis.getDataJDBCTimestamp(obj.getDataRegistro()));
                sqlInserir.setInt(++i, obj.getDiaDoMesDescontoBoletoPagAntecipado());
                sqlInserir.setDouble(++i, obj.getPorcentagemDescontoBoletoPagAntecipado());
                sqlInserir.setDouble(++i, obj.getMultaValorFixo());
                sqlInserir.setDouble(++i, obj.getJurosValorFixo());

                sqlInserir.execute();
                if (sqlInserir.getGeneratedKeys().next())
                    obj.setCodigo(sqlInserir.getGeneratedKeys().getInt("codigo"));
            }
            obj.setNovoObj(false);
        } catch (Exception e) {
            obj.setNovoObj(true);
            throw e;
        }
    }

    public List<BoletoPJBankVO> excluirBoletoPJBank(EstornoMovProdutoVO estornoMovProdutoVO) throws Exception {
        List<BoletoPJBankVO> listaBoletosCancelar = new ArrayList<>();
        try {
            StringBuilder sql = new StringBuilder();
            sql.append("select  \n");
            sql.append("distinct(b.codigo) as codigo \n");
            sql.append("from boletospjbank b \n");
            sql.append("inner join movparcela mp on mp.codigo = b.movparcela \n");
            sql.append("inner join movprodutoparcela mpp on mpp.movparcela = mp.codigo \n");
            sql.append("where mpp.movproduto = ? ");
            try (PreparedStatement ps = con.prepareStatement(sql.toString())) {
                ps.setInt(1, estornoMovProdutoVO.getMovProdutoVO().getCodigo());
                try (ResultSet rs = ps.executeQuery()) {
                    while (rs.next()) {
                        Integer boleto = rs.getInt("codigo");

                        String operacao = ("Estorno MovProduto " + estornoMovProdutoVO.getMovProdutoVO().getCodigo());
                        JSONObject jsonEstorno = gerarBaseJSONEstorno(operacao, estornoMovProdutoVO.getResponsavelEstorno());
                        BoletoPJBankVO boletoPJBankVO = consultarPorChavePrimaria(boleto, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
                        alterarSomenteRegistroBancario("Estornado", boleto);
                        jsonEstorno.put("msgCancelarBoleto", "");
                        removerMovParcelaAtualizarJsonEstorno(operacao, jsonEstorno.toString(), boleto);
                        listaBoletosCancelar.add(boletoPJBankVO);
                    }
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
            throw e;
        }
        return listaBoletosCancelar;
    }

    public List<BoletoPJBankVO> excluirBoletoPJBank(MovParcelaVO movParcelaVO, UsuarioVO usuarioResponsavel) throws Exception {
        List<BoletoPJBankVO> listaBoletosCancelar = new ArrayList<>();
        try {
            StringBuilder sql = new StringBuilder();
            sql.append("select  \n");
            sql.append("distinct(b.codigo) as codigo \n");
            sql.append("from boletospjbank b \n");
            sql.append("where b.movparcela = ? ");
            try (PreparedStatement ps = con.prepareStatement(sql.toString())) {
                ps.setInt(1, movParcelaVO.getCodigo());
                try (ResultSet rs = ps.executeQuery()) {
                    while (rs.next()) {
                        Integer boleto = rs.getInt("codigo");

                        String operacao = ("Estorno MovParcela " + movParcelaVO.getCodigo());
                        JSONObject jsonEstorno = gerarBaseJSONEstorno(operacao, usuarioResponsavel);
                        BoletoPJBankVO boletoPJBankVO = consultarPorChavePrimaria(boleto, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
                        alterarSomenteRegistroBancario("Estornado", boleto);
                        jsonEstorno.put("msgCancelarBoleto", "");
                        removerMovParcelaAtualizarJsonEstorno(operacao, jsonEstorno.toString(), boleto);
                        listaBoletosCancelar.add(boletoPJBankVO);
                    }
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
            throw e;
        }
        return listaBoletosCancelar;
    }


    public List<BoletoPJBankVO> excluirBoletoPJBank(UsuarioVO usuarioEstornoVO, ContratoVO contratoVO) throws Exception {
        List<BoletoPJBankVO> listaBoletosCancelar = new ArrayList<>();
        try {
            StringBuilder sql = new StringBuilder();
            sql.append("select  \n");
            sql.append("distinct(b.codigo) as codigo \n");
            sql.append("from boletospjbank b \n");
            sql.append("inner join movparcela mp on mp.codigo = b.movparcela \n");
            sql.append("where mp.contrato = ? ");
            try (PreparedStatement ps = con.prepareStatement(sql.toString())) {
                ps.setInt(1, contratoVO.getCodigo());
                try (ResultSet rs = ps.executeQuery()) {
                    while (rs.next()) {
                        Integer boleto = rs.getInt("codigo");

                        String operacao = ("Estorno Contrato " + contratoVO.getCodigo());
                        JSONObject jsonEstorno = gerarBaseJSONEstorno(operacao, usuarioEstornoVO);
                        BoletoPJBankVO boletoPJBankVO = consultarPorChavePrimaria(boleto, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
                        alterarSomenteRegistroBancario("Estornado", boleto);
                        jsonEstorno.put("msgCancelarBoleto", "");
                        removerMovParcelaAtualizarJsonEstorno(operacao, jsonEstorno.toString(), boleto);
                        listaBoletosCancelar.add(boletoPJBankVO);
                    }
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
            throw e;
        }
        return listaBoletosCancelar;
    }

    private JSONObject gerarBaseJSONEstorno(String operacao, UsuarioVO usuarioVO) {
        JSONObject jsonEstorno = new JSONObject();
        jsonEstorno.put("operacao", operacao);
        jsonEstorno.put("data", Calendario.getDataAplicandoFormatacao(Calendario.hoje(), "dd/MM/yyyy HH:mm:ss"));
        if (usuarioVO != null) {
            jsonEstorno.put("usuario", usuarioVO.getNome());
        }
        return jsonEstorno;
    }

    public void removerMovParcelaAtualizarJsonEstorno(String operacao, String jsonEstorno, Integer codigo) throws SQLException {
        try {
            String sql2 = "update boletospjbank set movparcela = null, jsonEstorno = ?, dataatualizacao = ? where codigo = ?;";
            try (PreparedStatement ps = con.prepareStatement(sql2)) {
                int i = 0;
                ps.setString(++i, jsonEstorno);
                ps.setTimestamp(++i, Uteis.getDataJDBCTimestamp(Calendario.hoje()));
                ps.setInt(++i, codigo);
                ps.execute();
            }
            registrarLogPJBank(operacao, codigo);
        } catch (Exception ex) {
            ex.printStackTrace();
            throw ex;
        }
    }

    public List<BoletoPJBankVO> consultarTodos(Integer pessoa, int nivelMontarDados) throws Exception {
        StringBuilder sql = new StringBuilder();
        sql.append("select \n");
        sql.append("b.*, \n");
        sql.append("m.descricao \n");
        sql.append("from boletospjbank b \n");
        sql.append("left join movparcela m on b.movparcela = m.codigo \n");
        sql.append("where b.codigo in ( \n");
        sql.append("select \n");
        sql.append("b1.codigo \n");
        sql.append("from boletospjbank b1 \n");
        sql.append("where b1.pessoa = ").append(pessoa).append(" \n");
        sql.append("union \n");
        sql.append("select \n");
        sql.append("b2.codigo \n");
        sql.append("from boletospjbank b2 \n");
        sql.append("inner join movparcela m2 on m2.codigo = b2.movparcela \n");
        sql.append("where m2.pessoa = ").append(pessoa).append(" \n");
        sql.append(") \n");
        sql.append("order by b.codigo desc \n");
        try (Statement stm = con.createStatement()) {
            try (ResultSet tabelaResultado = stm.executeQuery(sql.toString())) {
                return montarDadosConsulta(tabelaResultado, nivelMontarDados);
            }
        }
    }

    @Override
    public List<BoletoPJBankVO> consultarPorMovParcela(Integer movParcela) throws Exception {
        StringBuilder query = new StringBuilder();
        query.append("select b.* from boletospjbank b \n");
        query.append("where b.movparcela = ").append(movParcela).append(" \n");
        query.append("order by b.codigo desc\n");
        //não alterar pois os locais onde chamam dependem da ordem dos boletos.
        try (Statement stm = con.createStatement()) {
            try (ResultSet tabelaResultado = stm.executeQuery(query.toString())) {
                return (montarDadosConsulta(tabelaResultado, Uteis.NIVELMONTARDADOS_DADOSBASICOS));
            }
        }
    }

    public boolean existeBoletoNaoCanceladoPorMovParcela(Integer movParcela) throws Exception {
        StringBuilder query = new StringBuilder();
        query.append("select exists(select b.codigo from boletospjbank b \n");
        query.append("where b.movparcela = ").append(movParcela).append(" \n");
        query.append("and upper(registrosistemabancario) not in ('CANCELADO','BAIXADO')) as existe");
        try (Statement stm = con.createStatement()) {
            try (ResultSet rs = stm.executeQuery(query.toString())) {
                if (rs.next()) {
                    return rs.getBoolean(1);
                }
                return false;
            }
        }
    }

    public List<BoletoPJBankVO> montarDadosConsulta(ResultSet tabelaResultado, int nivelMontarDados) throws Exception {
        List<BoletoPJBankVO> vetResultado = new ArrayList();
        while (tabelaResultado.next()) {
            BoletoPJBankVO obj = montarDados(tabelaResultado, nivelMontarDados);
            vetResultado.add(obj);
        }
        return vetResultado;
    }

    public BoletoPJBankVO montarDados(ResultSet dadosSQL, int nivelMontarDados) throws Exception {
        BoletoPJBankVO obj = new BoletoPJBankVO();

        obj.setCodigo(dadosSQL.getInt("codigo"));
        obj.getMovParcelaVO().setCodigo(dadosSQL.getInt("movparcela"));

        try {
            obj.getMovParcelaVO().setDescricao(dadosSQL.getString("descricao"));
        } catch (Exception ignored) {
        }

        obj.getConvenioCobrancaVO().setCodigo(dadosSQL.getInt("conveniocobranca"));
        obj.setDataRegistro(dadosSQL.getTimestamp("dataregistro"));
        obj.setDataVencimento(dadosSQL.getDate("datavencimento"));
        obj.setDataCredito(dadosSQL.getDate("datacredito"));
        obj.setValor(dadosSQL.getFloat("valor"));
        obj.setJuros(dadosSQL.getFloat("juros"));
        obj.setMulta(dadosSQL.getFloat("multa"));
        obj.setDesconto(dadosSQL.getFloat("desconto"));
        obj.setPedidoNumero(dadosSQL.getInt("pedidonumero"));
        obj.setWebhook(dadosSQL.getString("webhook"));
        obj.setBanco(dadosSQL.getString("banco"));
        obj.setTokenFacilitador(dadosSQL.getString("tokenfacilitador"));
        obj.setLinkGrupo(dadosSQL.getString("linkgrupo"));
        obj.setIdUnico(dadosSQL.getString("idunico"));
        obj.setIdUnicoOriginal(dadosSQL.getString("idunicooriginal"));
        obj.setLinkBoleto(dadosSQL.getString("linkboleto"));
        obj.setLinhaDigitavel(dadosSQL.getString("linhadigitavel"));
        obj.setValorPago(dadosSQL.getFloat("valorpago"));
        obj.setValorLiquido(dadosSQL.getFloat("valorliquido"));
        obj.setValorTarifa(dadosSQL.getFloat("valortarifa"));
        obj.setPagamentoDuplicado(dadosSQL.getBoolean("pagamentoduplicado"));
        obj.setNossoNumero(dadosSQL.getString("nossonumero"));
        obj.setNossoNumeroOriginal(dadosSQL.getString("nossonumerooriginal"));
        obj.setRegistroSistemaBancario(dadosSQL.getString("registrosistemabancario"));
        obj.setRegistroRegeicaoMotivo(dadosSQL.getString("registroregeicaomotivo"));
        obj.setDatapagamento(dadosSQL.getDate("datapagamento"));
        obj.getReciboPagamentoVO().setCodigo(dadosSQL.getInt("recibopagamento"));
        obj.setPedidoNumeroPJBank(dadosSQL.getString("pedidoNumeroPJBank"));
        obj.getPessoaVO().setCodigo(dadosSQL.getInt("pessoa"));
        obj.setJsonEstorno(dadosSQL.getString("jsonestorno"));
        obj.getMovPagamentoVO().setCodigo(dadosSQL.getInt("movPagamento"));
        obj.setDataAtualizacao(dadosSQL.getTimestamp("dataatualizacao"));
        obj.setDiaDoMesDescontoBoletoPagAntecipado(dadosSQL.getInt("diaDoMesDescontoBoletoPagAntecipado"));
        obj.setPorcentagemDescontoBoletoPagAntecipado(dadosSQL.getDouble("porcentagemDescontoBoletoPagAntecipado"));
        obj.setMultaValorFixo(dadosSQL.getDouble("multaValorFixo"));
        obj.setJurosValorFixo(dadosSQL.getDouble("jurosValorFixo"));

        obj.setNovoObj(false);

        if (nivelMontarDados == Uteis.NIVELMONTARDADOS_DADOSBASICOS) {
            return obj;
        }

        if (nivelMontarDados == Uteis.NIVELMONTARDADOS_DADOSENTIDADESPRINCIPAIS) {
            if (!UteisValidacao.emptyNumber(obj.getConvenioCobrancaVO().getCodigo())) {
                ConvenioCobranca convDAO = new ConvenioCobranca(con);
                obj.setConvenioCobrancaVO(convDAO.consultarPorChavePrimaria(obj.getConvenioCobrancaVO().getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS));
                convDAO = null;
            }
            return obj;
        }

        return obj;
    }

    /**
     * Operação responsável por localizar um objeto da classe <code>BoletoPJBankVO</code>
     * através de seu IdUnico.
     *
     * @throws Exception Caso haja problemas de conexão ou localização do objeto procurado.
     */
    public BoletoPJBankVO consultarPorID(String idUnico, int nivelMontarDados) throws Exception {
        String sql = "select m.descricao, b.* from boletospjbank b left join movparcela m on b.movparcela = m.codigo WHERE idunico = ?";
        try (PreparedStatement sqlConsultar = con.prepareStatement(sql)) {
            sqlConsultar.setString(1, idUnico);
            try (ResultSet rs = sqlConsultar.executeQuery()) {
                if (rs.next()) {
                    return montarDados(rs, nivelMontarDados);
                } else {
                    throw new ConsistirException("Dados Não Encontrados ( BoletosPJBank ).");
                }
            }
        }
    }

    public BoletoPJBankVO consultarPorChavePrimaria(Integer codigo, int nivelMontarDados) throws Exception {
        String sql = "select m.descricao, b.* from boletospjbank b left join movparcela m on b.movparcela = m.codigo WHERE b.codigo = ?";
        try (PreparedStatement sqlConsultar = con.prepareStatement(sql)) {
            sqlConsultar.setInt(1, codigo);
            try (ResultSet rs = sqlConsultar.executeQuery()) {
                if (rs.next()) {
                    return montarDados(rs, nivelMontarDados);
                } else {
                    throw new ConsistirException("Dados Não Encontrados ( BoletosPJBank ).");
                }
            }
        }
    }

    public BoletoPJBankVO gerarBoletoParcela(Integer parcela, Integer convenio, String chave, Integer diasVencidoCasoParcelaVencida,
                                             Date dataVencimentoFixo, Double multaValorFixo, Double jurosValorFixo) throws Exception {
        ConvenioCobranca convenioCobrancaDAO = null;
        MovParcela movParcelaDAO = null;
        Empresa empresaDAO = null;
        Cliente clienteDAO = null;
        Pessoa pessoaDAO = null;
        try {
            convenioCobrancaDAO = new ConvenioCobranca(con);
            movParcelaDAO = new MovParcela(con);
            empresaDAO = new Empresa(con);
            clienteDAO = new Cliente(con);
            pessoaDAO = new Pessoa(con);

            ConvenioCobrancaVO cobrancaVO = convenioCobrancaDAO.consultarPorChavePrimaria(convenio, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
            MovParcelaVO movParcelaVO = movParcelaDAO.consultarPorChavePrimaria(parcela, Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA);
            movParcelaVO.setPessoa(pessoaDAO.consultarPorChavePrimaria(movParcelaVO.getPessoa().getCodigo(), Uteis.NIVELMONTARDADOS_CONSULTA_DADOS_BOLETO));
            EmpresaVO empresaVO = empresaDAO.consultarPorChavePrimaria(movParcelaVO.getEmpresa().getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS);

            servicos.integracao.pjbank.bean.Cliente clientePJBank = new servicos.integracao.pjbank.bean.Cliente();

            String cpf = "";
            String nome = "";

            if (empresaVO.isUtilizarNomeResponsavelNoBoleto() &&
                    movParcelaVO.getPessoa().getIdade() < 18) {

                PessoaVO pessoaResponsavel = clienteDAO.obterPessoaResponsavelCliente(null, movParcelaVO.getPessoa().getCodigo(), Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA);

                if (pessoaResponsavel != null && !UteisValidacao.emptyNumber(pessoaResponsavel.getCodigo())) {
                    nome = pessoaResponsavel.getNome();
                    cpf = pessoaResponsavel.getCfp().replaceAll("[^0-9]", "");
                } else if (!movParcelaVO.getPessoa().getCpfMae().equals("")) {
                    nome = movParcelaVO.getPessoa().getNomeMae();
                    cpf = movParcelaVO.getPessoa().getCpfMae().replaceAll("[^0-9]", "");
                } else if (!movParcelaVO.getPessoa().getCpfPai().equals("")) {
                    nome = movParcelaVO.getPessoa().getNomePai();
                    cpf = movParcelaVO.getPessoa().getCpfPai().replaceAll("[^0-9]", "");
                } else {
                    throw new Exception("Responsável pelo cliente não cadastrado.");
                }

                if (UteisValidacao.emptyString(nome) ||
                        UteisValidacao.emptyString(cpf.replaceAll("[^0-9]", ""))) {
                    throw new Exception("Marcado configuração de \"Utilizar nome responsável no boleto\", nome ou CPF do responsável não está preenchido.");
                }

            } else {
                nome = movParcelaVO.getPessoa_Apresentar();
                cpf = movParcelaVO.getPessoa().getCfp().replaceAll("[^0-9]", "");

                if (UteisValidacao.emptyString(nome)) {
                    throw new Exception("Nome não informado");
                }

                if (UteisValidacao.emptyString(cpf.replaceAll("[^0-9]", ""))) {
                    throw new Exception("CPF não cadastrado");
                }
            }

            if (UteisValidacao.emptyString(nome)) {
                throw new Exception("Nome não informado");
            }

            if (UteisValidacao.emptyString(cpf.replaceAll("[^0-9]", ""))) {
                throw new Exception("CPF não cadastrado");
            }

            clientePJBank.setNome(nome);
            clientePJBank.setCpfCnpj(cpf.replaceAll("[^0-9]", ""));


            Endereco enderecoPJBank = new Endereco();
            List<EnderecoVO> lstEnderecoVO = new ArrayList<EnderecoVO>();
            negocio.facade.jdbc.basico.Endereco enderecoDAO = new negocio.facade.jdbc.basico.Endereco(con);
            lstEnderecoVO = enderecoDAO.consultarEnderecos(movParcelaVO.getPessoa().getCodigo(), false, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
            enderecoDAO = null;

            for (EnderecoVO enderecos : lstEnderecoVO) {
                enderecoPJBank.setLogradouro(enderecos.getEndereco());
                enderecoPJBank.setNumero(enderecos.getNumero());
                enderecoPJBank.setComplemento(enderecos.getComplemento());
                enderecoPJBank.setBairro(enderecos.getBairro());
                enderecoPJBank.setCidade(movParcelaVO.getPessoa().getCidade_Apresentar());
                enderecoPJBank.setEstado(movParcelaVO.getPessoa().getEstadoVO().getSigla());
                enderecoPJBank.setCep(enderecos.getCep().replaceAll("[^0-9]", ""));
                break;
            }

            clientePJBank.setEndereco(enderecoPJBank);
            String credencial = cobrancaVO.getCredencialPJBank();
            String chavePJBank = cobrancaVO.getChavePJBank();

            BoletoRecebimento boletoRecebimentoPJBank = new BoletoRecebimento();
            boletoRecebimentoPJBank.setCliente(clientePJBank);

            //somar o valor de juros e multa fixo.
            if (!UteisValidacao.emptyNumber(multaValorFixo) || !UteisValidacao.emptyNumber(jurosValorFixo)) {
                boletoRecebimentoPJBank.setValor(movParcelaVO.getValorParcela() + multaValorFixo + jurosValorFixo);
            } else {
                boletoRecebimentoPJBank.setValor(movParcelaVO.getValorParcela());
            }

            if (empresaVO.getCobrarAutomaticamenteMultaJuros()) {
                boletoRecebimentoPJBank.setJurosFixo(empresaVO.isUtilizarJurosValorAbsoluto() ? 1 : 0);
                boletoRecebimentoPJBank.setMultaFixo(empresaVO.isUtilizarMultaValorAbsoluto() ? 1 : 0);
                boletoRecebimentoPJBank.setJuros(Uteis.arredondarForcando2CasasDecimais(empresaVO.getJurosCobrancaAutomatica() * 30));
                boletoRecebimentoPJBank.setMulta(Uteis.arredondarForcando2CasasDecimais(empresaVO.getMultaCobrancaAutomatica()));
            } else {
                boletoRecebimentoPJBank.setJuros(0);
                boletoRecebimentoPJBank.setMulta(0);
            }

            Date dataVencimento = movParcelaVO.getDataVencimento();
            if (dataVencimentoFixo != null) {
                dataVencimento = dataVencimentoFixo;
            } else if (UteisValidacao.dataMenorDataAtualSemHora(dataVencimento) &&
                    !UteisValidacao.emptyNumber(diasVencidoCasoParcelaVencida)) {
                //caso seja informado um valor então deve ser gerado a data de vencimento a partir da data atual.
                //by Luiz Felipe 05/04/2021
                dataVencimento = Calendario.somarDias(Calendario.hoje(), diasVencidoCasoParcelaVencida);
            }

            if (UteisValidacao.dataMenorDataAtualSemHora(dataVencimento)) {
                throw new Exception("A data de vencimento do boleto não pode ser menor que a data atual.");
            }
            boletoRecebimentoPJBank.setVencimento(dataVencimento);


            //logomarca empresa
            try {
                final String genKey = MidiaService.getInstance().genKey(chave, MidiaEntidadeEnum.FOTO_EMPRESA, empresaVO.getCodigo().toString());
                String urlFotoLogo = Uteis.getPaintFotoDaNuvem(genKey);
                boletoRecebimentoPJBank.setLogoUrl(urlFotoLogo);
            } catch (Exception ignored) {
            }


            try {
                if (cobrancaVO.getInstrucoesBoleto().toUpperCase().contains("TAG_MATRICULA")) {
                    ClienteVO clienteVO = clienteDAO.consultarPorCodigoPessoa(movParcelaVO.getPessoa().getCodigo(),empresaVO.getCodigo(),Uteis.NIVELMONTARDADOS_MINIMOS);
                    if (!UteisValidacao.emptyNumber(clienteVO.getCodigo())) {
                        cobrancaVO.setInstrucoesBoleto(processarInstrucaoMatricula(cobrancaVO.getInstrucoesBoleto(), clienteVO.getMatricula()));
                    }
                }
            } catch (Exception ex) {
                ex.printStackTrace();
            }

            boletoRecebimentoPJBank.setInstrucao_adicional(cobrancaVO.getInstrucoesBoleto());
            boletoRecebimentoPJBank.setPedidoNumero(gerarNumeroAleatorio());

            //desconto boleto
            List<MovParcelaVO> listaParcela = new ArrayList<>();
            listaParcela.add(movParcelaVO);
            DescontoBoletoTO desconto = clienteDAO.descobrirDescontoBoletoParaCliente(listaParcela);
            if (desconto != null) {
                Integer diaDesconto = 0;
                Integer diaVencimentoBoleto = Uteis.obterDiaData(boletoRecebimentoPJBank.getVencimento());
                if (diaVencimentoBoleto >= desconto.getDiaMaximoPagamentoDesconto()) {
                    diaDesconto = new Long(Uteis.nrDiasEntreDatas(Calendario.setDiaMes(boletoRecebimentoPJBank.getVencimento(), desconto.getDiaMaximoPagamentoDesconto()), boletoRecebimentoPJBank.getVencimento())).intValue();
                } else {
                    //pegar o dia do mês anterior
                    diaDesconto = new Long(Uteis.nrDiasEntreDatas(Calendario.setDiaMes(Uteis.somarMeses(boletoRecebimentoPJBank.getVencimento(), -1), desconto.getDiaMaximoPagamentoDesconto()), boletoRecebimentoPJBank.getVencimento())).intValue();
                }
                boletoRecebimentoPJBank.setDiaDesconto(diaDesconto);

                //calcular desconto
                boletoRecebimentoPJBank.setDesconto((boletoRecebimentoPJBank.getValor() * desconto.getPorcentagemDesconto()) / 100);
            } else {
                boletoRecebimentoPJBank.setDesconto(0.0);
            }

            String urlZWAPI = PropsService.getPropertyValue(PropsService.urlZWAPI);
            boletoRecebimentoPJBank.setWebhook(urlZWAPI + "/prest/pjbank/" + chave + "/novaTransacao");
            boletoRecebimentoPJBank.setNumParcela(movParcelaVO.getCodigo());

            BoletosManager boletosManager = new BoletosManager(credencial, chavePJBank, cobrancaVO);
            BoletoRecebimento boletoRecebimento = new BoletoRecebimento();
            boletoRecebimento = boletosManager.create(boletoRecebimentoPJBank, this);
            BoletoPJBankVO boletoPJBankVO = new BoletoPJBankVO(boletoRecebimento, cobrancaVO, movParcelaVO.getPessoa().getCodigo(), desconto, multaValorFixo, jurosValorFixo);
            incluir(boletoPJBankVO);
            return boletoPJBankVO;
        } catch (Exception ex) {
            ex.printStackTrace();
            throw new Exception("PJBank: " + ex.getMessage());
        } finally {
            convenioCobrancaDAO = null;
            movParcelaDAO = null;
            empresaDAO = null;
            clienteDAO = null;
            pessoaDAO = null;
        }
    }

    private String processarInstrucaoMatricula(String instrucao, String matriculaCliente) throws Exception {
        if (instrucao.contains("<matricula>")) {
            int inicioMatricula = instrucao.indexOf("<matricula>");
            int finalMatricula = instrucao.indexOf("</matricula>");
            String aux = instrucao.substring(0, inicioMatricula);
            aux = aux + instrucao.substring((inicioMatricula + 11), finalMatricula);
            aux = aux.replaceAll("TAG_MATRICULA", matriculaCliente != null ? matriculaCliente : "");
            aux += instrucao.substring(finalMatricula + 12);
            instrucao = aux;
        }
        return instrucao;
    }

    public Integer gerarNumeroAleatorio() {
        SimpleDateFormat sdf = new SimpleDateFormat("ddHHmmss");
        Double vlrRandom = 0.0;
        vlrRandom = Math.random() * Double.valueOf(sdf.format(Calendario.hoje()));
        return vlrRandom.intValue();
    }

    public void registrarLogPJBank(JSONObject json, Integer codigoBoleto) {
        registrarLogPJBank(json.toString(), codigoBoleto);
    }

    public void registrarLogPJBank(String mgs, Integer codigoBoleto) {
        LogPJBank logPJBankDAO = null;
        try {
            logPJBankDAO = new LogPJBank(con);
            LogPJBankVO logPJBankVO = new LogPJBankVO();
            logPJBankVO.setDescricao(mgs);
            logPJBankVO.setBoletoPJBank(codigoBoleto);
            logPJBankVO.setDataRegistroLog(Calendario.hoje());
            logPJBankDAO.incluir(logPJBankVO);
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            logPJBankDAO = null;
        }
    }

    public void processarWebhook(String json) throws Exception {
        try {
            incluirHistoricoRetornoPJBank(null, "processarWebhook", json);
            processarBoletoPJBank(json, "WebHook");
        } catch (Exception ex) {
            ex.printStackTrace();
            throw ex;
        }
    }

    public String processarBoletoPJBank(String json, String origem) throws Exception {
        Boleto boletoDAO;
        try {
            WebhookPJBankJSON webhookPJBankJSON = new WebhookPJBankJSON(new JSONObject(json));
            if (UteisValidacao.emptyString(webhookPJBankJSON.getId_unico())) {
                throw new Exception("Registro não encontrado");
            }
            boletoDAO = new Boleto(con);

            BoletoVO boletoVO = boletoDAO.consultarPorIdexternoTipo(webhookPJBankJSON.getId_unico(), TipoBoletoEnum.PJ_BANK, Uteis.NIVELMONTARDADOS_DADOSBASICOS);

            //incluir histórico
            boletoDAO.incluirBoletoHistorico(boletoVO, "processarBoletoPJBank-" + origem, json);

            return boletoDAO.processarWebhook(TipoBoletoEnum.PJ_BANK, boletoVO.getCodigo(), boletoVO.getConvenioCobrancaVO().getCodigo(), json, null);
        } catch (Exception ex) {
            ex.printStackTrace();
            throw ex;
        } finally {
            boletoDAO = null;
        }
    }

    public String processarBoletoPJBank_ANTIGO(String json, String origem) throws Exception {
        BoletoPJBankVO obj = null;
        Usuario usuarioDAO = null;
        try {
            WebhookPJBankJSON webhookPJBankJSON = new WebhookPJBankJSON(new JSONObject(json));
            if (UteisValidacao.emptyString(webhookPJBankJSON.getId_unico())) {
                throw new Exception("Registro não encontrado");
            }
            usuarioDAO = new Usuario(con);

            obj = consultarPorID(webhookPJBankJSON.getId_unico(), Uteis.NIVELMONTARDADOS_DADOSBASICOS);

            //incluir histórico
            incluirHistoricoRetornoPJBank(obj.getCodigo(), "processarBoletoPJBank", new JSONObject(webhookPJBankJSON).toString());


            //em caso de webhook de cancelamento
            if (webhookPJBankJSON.getTipo().equalsIgnoreCase("cancelamento_boleto")) {
                UsuarioVO usuarioVO = usuarioDAO.getUsuarioRecorrencia();
                JSONObject jsonEstorno = gerarBaseJSONEstorno(origem + "-Cancelamento", usuarioVO);
                removerMovParcelaAtualizarJsonEstorno(("Cancelar Boleto " + obj.getCodigo() + " - " + origem), jsonEstorno.toString(), obj.getCodigo());
                alterarSomenteRegistroBancario("Cancelado", obj.getCodigo());
                return "Boleto foi cancelado";
            }

            try {
                obj.setRegistroSistemaBancario(webhookPJBankJSON.getRegistro_sistema_bancario());
                obj.setRegistroRegeicaoMotivo(webhookPJBankJSON.getRegistro_rejeicao_motivo());
                obj.setValorPago(webhookPJBankJSON.getValor_pago_Float());
                obj.setDatapagamento(webhookPJBankJSON.getData_pagamento_Date());
                obj.setDataCredito(webhookPJBankJSON.getData_credito_Date());
                obj.setValorLiquido(webhookPJBankJSON.getValor_liquido_Float());
                obj.setValorTarifa(webhookPJBankJSON.getValor_tarifa_Float());
            } catch (Exception ex) {
                ex.printStackTrace();
            }

            BoletoPJBankVO objAnterior = (BoletoPJBankVO) obj.getClone(true);

            if (webhookPJBankJSON.getValor_pago_Float() > 0.0) {
                if (!UteisValidacao.emptyNumber(obj.getReciboPagamentoVO().getCodigo()) ||
                        !UteisValidacao.emptyNumber(obj.getMovPagamentoVO().getCodigo())) {
                    registrarLogPJBank(" BOLETO JÁ ESTÁ PAGO | " + origem, obj.getCodigo());
                } else {
                    //gerar recibo pagamento
                    gerarPagamentoBoleto(webhookPJBankJSON, obj);
                }
            }

            registrarLogPJBank(webhookPJBankJSON.getRegistro_sistema_bancario(), obj.getCodigo());

            //atualizar dados do boleto
            alterarRegistroBancario(obj);

            if (UteisValidacao.emptyNumber(objAnterior.getReciboPagamentoVO().getCodigo()) &&
                    !UteisValidacao.emptyNumber(obj.getReciboPagamentoVO().getCodigo())) {
                if (obj.isGerouCreditoContaCorrente()) {
                    return "Foi gerado crédito na conta corrente do cliente.";
                } else {
                    return "Recibo gerado com sucesso.";
                }
            } else {
                return "Boleto sincronizado.";
            }
        } catch (Exception ex) {
            ex.printStackTrace();
            if (obj != null) {
                registrarLogPJBank(ex.getMessage(), obj.getCodigo());
            }
            throw ex;
        } finally {
            usuarioDAO = null;
        }
    }

    private void gerarPagamentoBoleto(WebhookPJBankJSON webhookPJBankJSON, BoletoPJBankVO obj) throws Exception {
        Empresa empresaDAO = null;
        Usuario usuarioDAO = null;
        Cliente clienteDAO = null;
        Colaborador colaboradorDAO = null;
        MovParcela movParcelaDAO = null;
        MovPagamento movPagamentoDAO = null;
        FormaPagamento formaPagamentoDAO = null;
        MovimentoContaCorrenteCliente movimentoDAO = null;
        try {
            empresaDAO = new Empresa(this.con);
            usuarioDAO = new Usuario(this.con);
            clienteDAO = new Cliente(this.con);
            colaboradorDAO = new Colaborador(this.con);
            movParcelaDAO = new MovParcela(this.con);
            movPagamentoDAO = new MovPagamento(this.con);
            formaPagamentoDAO = new FormaPagamento(this.con);
            movimentoDAO = new MovimentoContaCorrenteCliente(this.con);

            MovParcelaVO movParcelaVO = movParcelaDAO.consultarPorCodigo(obj.getMovParcelaVO().getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS);
            if (movParcelaVO == null) {
                throw new Exception("Parcela não encontrada");
            }

            Double valorPago = Uteis.arredondarForcando2CasasDecimais(webhookPJBankJSON.getValor_pago_Float().doubleValue());

            if (valorPago > 0.0 &&
                    UteisValidacao.emptyNumber(obj.getReciboPagamentoVO().getCodigo()) &&
                    UteisValidacao.emptyNumber(obj.getMovPagamentoVO().getCodigo())) {

                Uteis.logar(null, "## INCLUIR PAGAMENTO PJBank | Parcela " + movParcelaVO.getCodigo() + " | IdUnico " + webhookPJBankJSON.getId_unico() + " | Cod. Boleto " + obj.getCodigo());

                UsuarioVO usuarioVO = usuarioDAO.getUsuarioRecorrencia();

                List<MovPagamentoVO> listaPagamento = new ArrayList<>();
                List<MovParcelaVO> listaParcelas = new ArrayList<>();

                FormaPagamentoVO formaPagamentoVO = formaPagamentoDAO.consultarFormaPorConvenio(obj.getConvenioCobrancaVO());
                if (UteisValidacao.emptyNumber(formaPagamentoVO.getCodigo())) {
                    formaPagamentoVO = new FormaPagamentoVO();
                    formaPagamentoVO.setDescricao("BOLETO BANCÁRIO");
                    formaPagamentoVO.setTipoFormaPagamento("BB");
                    formaPagamentoVO = formaPagamentoDAO.criarOuConsultarSeExistePorFlag(formaPagamentoVO);
                }

                Date dataPagamento = webhookPJBankJSON.getData_pagamento_Date();
                Date dataCredito = webhookPJBankJSON.getData_credito_Date();

                MovPagamentoVO movPagamentoVO = new MovPagamentoVO();
                movPagamentoVO.setMovPagamentoEscolhida(true);
                movPagamentoVO.setOpcaoPagamentoBoleto(true);
                movPagamentoVO.setCredito(false);
                movPagamentoVO.setPessoa(movParcelaVO.getPessoa());
                movPagamentoVO.setFormaPagamento(formaPagamentoVO);
                movPagamentoVO.setValor(valorPago);
                movPagamentoVO.setValorTotal(valorPago);
                movPagamentoVO.setNomePagador(movParcelaVO.getPessoa().getNome());
                movPagamentoVO.setResponsavelPagamento(usuarioVO);
                movPagamentoVO.setEmpresa(movParcelaVO.getEmpresa());
                movPagamentoVO.setConvenio(obj.getConvenioCobrancaVO());

                //ajustado conforme informado pelo Marcos André
                movPagamentoVO.setDataLancamento(Calendario.hoje());
                movPagamentoVO.setDataQuitacao(dataPagamento);
                movPagamentoVO.setDataPagamento(dataCredito);

                listaPagamento.add(movPagamentoVO);


                //se a parcela não estiver em aberto então gerar depósito na conta
                //gerar pagamento conta corrente
                if (!movParcelaVO.getSituacao().equalsIgnoreCase("EA")) {

                    StringBuilder sqlExiste = new StringBuilder();
                    sqlExiste.append("select \n");
                    sqlExiste.append("mp.codigo \n");
                    sqlExiste.append("from movpagamento mp \n");
                    sqlExiste.append("inner join pagamentomovparcela pag on pag.movpagamento = mp.codigo \n");
                    sqlExiste.append("where mp.observacao ilike '%gerado crédito pois a parcela ").append(movParcelaVO.getCodigo()).append(" - %' \n");
                    sqlExiste.append("limit 1 \n");
                    boolean existe = SuperFacadeJDBC.existe(sqlExiste.toString(), con);

                    if (existe) {
                        throw new Exception("Já existe um crédito em conta corrente referente à parcela " + movParcelaVO.getCodigo());
                    }

                    EmpresaVO empresaVO;
                    ClienteVO clienteVO = clienteDAO.consultarPorCodigoPessoa(movParcelaVO.getPessoa().getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS);
                    ColaboradorVO colaboradorVO = null;
                    if (clienteVO == null || UteisValidacao.emptyNumber(clienteVO.getCodigo())) {
                        clienteVO = null;
                        colaboradorVO = colaboradorDAO.consultarPorCodigoPessoa(movParcelaVO.getPessoa().getCodigo(), movParcelaVO.getEmpresa().getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS);
                        empresaVO = colaboradorVO.getEmpresa();
                    } else {
                        empresaVO = clienteVO.getEmpresa();
                    }

                    String descLancCC = MovimentoContaCorrenteClienteVO.DESCRICAO_DEPOSITO_CONTA_ALUNO_BOLETO_PARCELA_NAO_ABERTO;
                    VendaAvulsaVO vendaAvulsaVO = movimentoDAO.gerarProdutoPagamentoCredito(valorPago, clienteVO, colaboradorVO, null, usuarioVO, descLancCC, dataPagamento, dataPagamento, empresaVO);

                    MovParcelaVO movParcela = movParcelaDAO.consultarPorCodigoVendaAvulsa(vendaAvulsaVO.getCodigo(), "EA", false, Uteis.NIVELMONTARDADOS_MINIMOS);
                    movPagamentoVO.setObservacao("Foi gerado crédito pois a parcela " + movParcelaVO.getCodigo() + " - " + movParcelaVO.getDescricao() + " estava com a situação: " + movParcelaVO.getSituacao_Apresentar());
                    obj.setGerouCreditoContaCorrente(true);
                    listaParcelas.add(movParcela);

                } else {

                    listaParcelas.add(movParcelaVO);
                    // Estamos tendo problemas onde cria as parcelas de Multa e Juros e as mesmas não são apagadas em caso de erro.
                    // Mas não temos como saber a origem, pois a criação vem de vários locais diferentes.
                    // Por isso a inclusão desses logs
                    if (!UteisValidacao.emptyList(listaParcelas)) {
                        String codigosMovParcelas = listaParcelas.stream()
                                .map(p -> String.valueOf(p.getCodigo()))
                                .collect(Collectors.joining(","));
                        Uteis.logarDebug("BoletoPjbank - gerarPagamentoBoleto - Identificar multa e juros criada e nao excluida apos pagamento. - Parcelas: " + codigosMovParcelas);
                    }

                    //esse valor já está incluso no valor do boleto porem ainda não foi criado a parcela
                    //criar a parcela e adicionar na lista de parcelas
                    if (!UteisValidacao.emptyNumber(obj.getMultaValorFixo()) || !UteisValidacao.emptyNumber(obj.getJurosValorFixo())) {
                        MovParcelaVO movParcelaMultaJurosVO = movParcelaDAO.criarParcelaMultaJuros(movParcelaVO, obj.getMultaValorFixo(), obj.getJurosValorFixo(), usuarioVO);
                        listaParcelas.add(movParcelaMultaJurosVO);
                    }

                    //se pagou mais do que era a parcela e a data de pagamento é posterior a data de vencimento então é multa e juros
                    if (Calendario.maior(dataPagamento, obj.getDataVencimento()) &&
                            Uteis.arredondarForcando2CasasDecimais(valorPago) > Uteis.arredondarForcando2CasasDecimais(obj.getValor())) {
                        EmpresaVO empresaVO = empresaDAO.consultarPorChavePrimaria(movParcelaVO.getEmpresa().getCodigo(), Uteis.NIVELMONTARDADOS_GESTAOREMESSA);
                        List<MovParcelaVO> movParcelasCalcu = new ArrayList<>();
                        movParcelasCalcu.add(movParcelaVO);
                        Double valorMultaJuros = movParcelaDAO.montarMultaJurosParcelaVencida(empresaVO, movParcelasCalcu, obj.getDataVencimento(), false, 1.0, null);
                        if (Uteis.arredondarForcando2CasasDecimais(valorMultaJuros) == Uteis.arredondarForcando2CasasDecimais(valorPago - obj.getValor())) {
                            MovParcelaVO movParcelaMultaJurosVO = movParcelaDAO.criarParcelaMultaJuros(movParcelaVO, movParcelaVO.getValorMulta(), movParcelaVO.getValorJuros(), usuarioVO);
                            listaParcelas.add(movParcelaMultaJurosVO);
                        } else {
                            MovParcelaVO movParcelaMultaJurosVO = movParcelaDAO.criarParcelaMultaJuros(movParcelaVO, Uteis.arredondarForcando2CasasDecimais(valorPago - obj.getValor()), 0.0, usuarioVO);
                            listaParcelas.add(movParcelaMultaJurosVO);
                        }

                    } else if (Uteis.arredondarForcando2CasasDecimais(valorPago) > Uteis.arredondarForcando2CasasDecimais(movParcelaVO.getValorParcela()) &&
                            (!UteisValidacao.emptyNumber(obj.getMulta()) || !UteisValidacao.emptyNumber(obj.getJuros()))) {
                        Double dif = (valorPago - (obj.getMulta() + obj.getJuros() + movParcelaVO.getValorParcela()));
                        if (UteisValidacao.emptyNumber(dif)) {
                            MovParcelaVO movParcelaMultaJurosVO = movParcelaDAO.criarParcelaMultaJuros(movParcelaVO, Double.valueOf(obj.getMulta()), Double.valueOf(obj.getJuros()), usuarioVO);
                            listaParcelas.add(movParcelaMultaJurosVO);
                        } else {
                            Double multa = (valorPago - movParcelaVO.getValorParcela());
                            MovParcelaVO movParcelaMultaJurosVO = movParcelaDAO.criarParcelaMultaJuros(movParcelaVO, multa, 0.0, usuarioVO);
                            listaParcelas.add(movParcelaMultaJurosVO);
                        }
                    } else if (deveGerarDesconto(obj, dataPagamento, valorPago, movParcelaVO.getEmpresa().getCodigo(), movParcelaVO)) {
                        gerarDescontoRemessa(obj, listaParcelas, usuarioVO);
                    }
                }

                ReciboPagamentoVO reciboPagamentoVO = movPagamentoDAO.incluirListaPagamento(listaPagamento, listaParcelas, null, movParcelaVO.getContrato(), false, 0.0);
                obj.setReciboPagamentoVO(reciboPagamentoVO);
                try {
                    obj.setMovPagamentoVO(reciboPagamentoVO.getPagamentosDesteRecibo().get(0));
                } catch (Exception ex) {
                    ex.printStackTrace();
                }
                alterarReciboPagamento(obj);

                try {
                    if (reciboPagamentoVO.getEmpresa().isNotificarWebhook()) {
                        ClienteVO clienteVO = clienteDAO.consultarPorCodigoPessoa(reciboPagamentoVO.getPessoaPagador().getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS);
                        ZillyonWebFacade zwFacade = new ZillyonWebFacade(this.getCon());
                        zwFacade.notificarPagamento(clienteVO, reciboPagamentoVO);
                        zwFacade = null;
                    }
                } catch (Exception ex) {
                    ex.printStackTrace();
                }

            }
        } catch (Exception ex) {
            ex.printStackTrace();
            throw ex;
        } finally {
            empresaDAO = null;
            usuarioDAO = null;
            clienteDAO = null;
            colaboradorDAO = null;
            movParcelaDAO = null;
            movPagamentoDAO = null;
            formaPagamentoDAO = null;
            movimentoDAO = null;
        }
    }

    private Boolean deveGerarDesconto(BoletoPJBankVO boletoPJBankVO, Date dataOcorrencia, Double valorPago, Integer empresa, MovParcelaVO movParcelaVO) throws Exception {
        Feriado feriadoDAO = null;
        Empresa empresaDAO = null;
        try {
            if (((movParcelaVO.getValorParcela() - valorPago) > 1) && //validar se a parcela já não foi renegociada e já está com o valor correto
                    boletoPJBankVO.possuiDesconto() &&
                    valorPago < boletoPJBankVO.getValor() &&
                    (boletoPJBankVO.getValor() - valorPago > 1)) {
                feriadoDAO = new Feriado(this.con);
                empresaDAO = new Empresa(this.con);
                boolean darDesconto = true;
                String dataMaximaDescontoAux = boletoPJBankVO.getDiaDoMesDescontoBoletoPagAntecipado().toString() + "/" + (Calendario.getData(boletoPJBankVO.getDataVencimento(), "MM/yyyy"));
                Date dataMaximaDesconto = Calendario.getDate("dd/MM/yyyy", dataMaximaDescontoAux);
                dataMaximaDesconto = Calendario.fimDoDia(dataMaximaDesconto);

                Calendar cal = Calendario.getInstance();
                cal.setTime(dataMaximaDesconto);
                if (cal.get(Calendar.DAY_OF_WEEK) == Calendar.SATURDAY || cal.get(Calendar.DAY_OF_WEEK) == Calendar.SUNDAY) {
                    int diasSomar = (cal.get(Calendar.DAY_OF_WEEK) == Calendar.SATURDAY) ? 2 : 1;
                    dataMaximaDesconto = Uteis.somarDias(cal.getTime(), diasSomar);
                }
                EmpresaVO empresaVO = empresaDAO.consultarPorChavePrimaria(empresa, Uteis.NIVELMONTARDADOS_TODOS);
                List<Date> dataLimiteFeriado = feriadoDAO.consultarDiasFeriados(dataMaximaDesconto, dataMaximaDesconto, empresaVO);
                if (!dataLimiteFeriado.isEmpty()) {
                    dataMaximaDesconto = Uteis.somarDias(dataMaximaDesconto, 1);
                }
                if (Calendario.maior(dataOcorrencia, dataMaximaDesconto)) {
                    darDesconto = false;
                }
                return darDesconto;
            }
        } finally {
            feriadoDAO = null;
            empresaDAO = null;
        }
        return false;
    }

    private void gerarDescontoRemessa(BoletoPJBankVO boletoPJBankVO, List<MovParcelaVO> listaParcelas, UsuarioVO usuarioVO) throws Exception {
        MovParcela movParcelaDAO = null;
        try {
            movParcelaDAO = new MovParcela(this.con);

            MovParcelaVO movParcelaVO = listaParcelas.get(0);

            Double valorDesconto = ((boletoPJBankVO.getValor() * boletoPJBankVO.getPorcentagemDescontoBoletoPagAntecipado()) / 100);
            List<MovParcelaVO> parcelasRenegociar = new ArrayList<>();
            parcelasRenegociar.add(movParcelaVO);
            // parcela desconto
            MovParcelaVO parcelaRenegociar = new MovParcelaVO();
            parcelaRenegociar.setDescricao("DESCONTOS");
            parcelaRenegociar.setValorParcela(Uteis.arredondarForcando2CasasDecimais(valorDesconto));
            parcelaRenegociar.setDataVencimento(Calendario.hoje());
            parcelasRenegociar.add(parcelaRenegociar);

            MovParcelaVO parcelaDesconto = new MovParcelaVO();
            parcelaDesconto.setDescricao("");
            parcelaDesconto.setValorParcela(valorDesconto);
            parcelaDesconto.setDataVencimento(Calendario.hoje());

            // Parcelas Renegociadas
            List<MovParcelaVO> parcelasRenegociadas = new ArrayList<>();
            MovParcelaVO novaParcela = (MovParcelaVO) movParcelaVO.getClone(true);
            novaParcela.setDescricao("PARCELA RENEGOCIADA");
            novaParcela.setValorParcela(Uteis.arredondarForcando2CasasDecimais(movParcelaVO.getValorParcela() - valorDesconto));
            novaParcela.setDataRegistro(Calendario.hoje());
            parcelasRenegociadas.add(novaParcela);
            parcelaDesconto.setDescricao("");

            movParcelaDAO.renegociarParcelas(parcelasRenegociar, parcelasRenegociadas, parcelaDesconto, new MovParcelaVO(), "DE", false, null, null, 0.0, false, usuarioVO, true, false, true, null, null);
            List<MovParcelaVO> parcelasNovas = new ArrayList<>();
            parcelasNovas.addAll(parcelasRenegociadas);

            alterarMovParcela(boletoPJBankVO, parcelasRenegociadas.get(0), "Alteração de MovParcela do boleto devido desconto");
            boletoPJBankVO.setMovParcelaVO(parcelasRenegociadas.get(0));

            listaParcelas.clear();
            listaParcelas.addAll(parcelasNovas);
        } finally {
            movParcelaDAO = null;
        }
    }

    public void incluirHistoricoRetornoPJBank(Integer boletoPJBank, String metodo, String webhook) throws Exception {
        try {
            String sql = "INSERT INTO pjbankhistoricoretorno(data, boletospjbank, metodo, webhook) VALUES (?,?,?,?)";
            try (PreparedStatement pst = con.prepareStatement(sql)) {
                int i = 0;
                pst.setTimestamp(++i, Uteis.getDataJDBCTimestamp(Calendario.hoje()));
                if (UteisValidacao.emptyNumber(boletoPJBank)) {
                    pst.setNull(++i, Types.NULL);
                } else {
                    pst.setInt(++i, boletoPJBank);
                }
                pst.setString(++i, metodo);
                pst.setString(++i, webhook);
                pst.execute();
            }
        } catch (Exception ex) {
            ex.printStackTrace();
            throw ex;
        }
    }

    public String sincronizarBoleto(BoletoPJBankVO obj, String origem) throws Exception {
        try {
            ConvenioCobranca convenioCobrancaDAO = new ConvenioCobranca(con);
            ConvenioCobrancaVO cobrancaVO = convenioCobrancaDAO.consultarPorChavePrimaria(obj.getConvenioCobrancaVO().getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS);
            convenioCobrancaDAO = null;

            String credencial = cobrancaVO.getCredencialPJBank();
            String chavePJBank = cobrancaVO.getChavePJBank();

            BoletosManager boletosManager = new BoletosManager(credencial, chavePJBank, cobrancaVO);
            String response = boletosManager.get(obj);
            incluirHistoricoRetornoPJBank(obj.getCodigo(), origem, response);

            JSONArray array = null;
            try {
                array = new JSONArray(response);
            } catch (Exception ex) {
                ex.printStackTrace();
            }

            if (array == null) {
                return "Não foi possível sincronizar, tente novamente mais tarde. " + response;
            } else if (array.length() == 0) {
                throw new Exception("Não foi encontrado boleto com o Id Único: " + obj.getIdUnico());
            } else if (array.length() == 1) {
                return processarBoletoPJBank(array.getJSONObject(0).toString(), origem);
            } else {
                throw new Exception("Erro ao sincronizar boleto: " + response);
            }
        } catch (Exception ex) {
            ex.printStackTrace();
            throw ex;
        }
    }

    private void alterarMovParcela(BoletoPJBankVO obj, MovParcelaVO movParcelaNova, String descricaoOperacao) throws Exception {
        String sql = "UPDATE boletospjbank SET movparcela = ? WHERE codigo = ?";
        try (PreparedStatement ps = con.prepareStatement(sql)) {
            int i = 0;
            ps.setInt(++i, movParcelaNova.getCodigo());
            ps.setInt(++i, obj.getCodigo());
            ps.execute();
        }
        JSONObject jsonLog = new JSONObject();
        jsonLog.put("descricao", descricaoOperacao);
        jsonLog.put("movparcela_anterior", obj.getMovParcelaVO().getCodigo());
        jsonLog.put("movparcela_nova", movParcelaNova.getCodigo());
        registrarLogPJBank(jsonLog, obj.getCodigo());
    }

    public void alterarReciboPagamento(BoletoPJBankVO obj) throws Exception {
        String sql = "UPDATE boletospjbank SET recibopagamento = ?, movpagamento = ?, dataatualizacao = ?, movparcela = ? WHERE codigo = ?";
        try (PreparedStatement ps = con.prepareStatement(sql)) {
            int i = 0;
            if (UteisValidacao.emptyNumber(obj.getReciboPagamentoVO().getCodigo())) {
                ps.setNull(++i, Types.NULL);
            } else {
                ps.setInt(++i, obj.getReciboPagamentoVO().getCodigo());
            }
            if (UteisValidacao.emptyNumber(obj.getMovPagamentoVO().getCodigo())) {
                ps.setNull(++i, Types.NULL);
            } else {
                ps.setInt(++i, obj.getMovPagamentoVO().getCodigo());
            }
            ps.setTimestamp(++i, Uteis.getDataJDBCTimestamp(Calendario.hoje()));
            if (UteisValidacao.emptyNumber(obj.getMovParcelaVO().getCodigo())) {
                ps.setNull(++i, Types.NULL);
            } else {
                ps.setInt(++i, obj.getMovParcelaVO().getCodigo());
            }
            ps.setInt(++i, obj.getCodigo());
            ps.execute();
        }
    }

    public List<BoletoPJBankVO> consultarPorReciboPagamento(Integer reciboPagamento, int nivelMontarDados) throws Exception {
        StringBuilder query = new StringBuilder();
        query.append("select b.* from boletospjbank b \n");
        query.append("where b.reciboPagamento = ").append(reciboPagamento).append(" \n");
        try (Statement stm = con.createStatement()) {
            try (ResultSet tabelaResultado = stm.executeQuery(query.toString())) {
                return montarDadosConsulta(tabelaResultado, nivelMontarDados);
            }
        }
    }

    public void estornarReciboPagamento(Integer reciboPagamento) throws Exception {
        if (UteisValidacao.emptyNumber(reciboPagamento)) {
            return;
        }

        List<BoletoPJBankVO> lista = consultarPorReciboPagamento(reciboPagamento, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
        for (BoletoPJBankVO obj : lista) {
            obj.setReciboPagamentoVO(null);
            obj.setMovPagamentoVO(null);
            alterarReciboPagamento(obj);
            registrarLogPJBank("Recibo estornado", obj.getCodigo());
        }
    }

    public void estornarMovParcela(Integer movParcela) throws Exception {
        if (UteisValidacao.emptyNumber(movParcela)) {
            return;
        }
        List<BoletoPJBankVO> lista = consultarPorMovParcela(movParcela);
        for (BoletoPJBankVO boleto : lista) {
            boleto.setReciboPagamentoVO(null);
            boleto.setMovPagamentoVO(null);
            boleto.setMovParcelaVO(null);
            alterarReciboPagamento(boleto);
            registrarLogPJBank("MovParcela estornada " + boleto.getMovParcelaVO().getCodigo(), boleto.getCodigo());
        }
    }

    public void alterarRegistroBancario(BoletoPJBankVO obj) throws Exception {
        String sql = "UPDATE boletospjbank set registrosistemabancario = ?, registroregeicaomotivo = ?, valorpago = ?, valorliquido = ?, " +
                "valortarifa = ?, datacredito = ?, datapagamento = ?, dataatualizacao = ?  WHERE codigo = ?";
        try (PreparedStatement ps = con.prepareStatement(sql)) {
            int i = 0;
            ps.setString(++i, obj.getRegistroSistemaBancario());
            ps.setString(++i, obj.getRegistroRegeicaoMotivo());
            ps.setDouble(++i, obj.getValorPago());
            ps.setDouble(++i, obj.getValorLiquido());
            ps.setDouble(++i, obj.getValorTarifa());
            ps.setDate(++i, Uteis.getDataJDBC(obj.getDataCredito()));
            ps.setDate(++i, Uteis.getDataJDBC(obj.getDatapagamento()));
            ps.setTimestamp(++i, Uteis.getDataJDBCTimestamp(Calendario.hoje()));
            ps.setInt(++i, obj.getCodigo());
            ps.execute();
        }
    }

    private void alterarSomenteRegistroBancario(String registrosistemabancario, Integer codigo) throws Exception {
        String sql = "UPDATE boletospjbank set registrosistemabancario = ?, dataatualizacao = ? WHERE codigo = ?";
        try (PreparedStatement ps = con.prepareStatement(sql)) {
            int i = 0;
            ps.setString(++i, registrosistemabancario);
            ps.setTimestamp(++i, Uteis.getDataJDBCTimestamp(Calendario.hoje()));
            ps.setInt(++i, codigo);
            ps.execute();
        }
    }

    public void enviarEmailBoletoPjBank(String chave, Integer movParcela, String[] emailEnviar,
                                        boolean enviarLinkBoleto, boolean enviarBoletoEmAnexo) throws Exception {

        List<BoletoPJBankVO> listaBoletos = consultarPorMovParcela(movParcela);
        if (UteisValidacao.emptyList(listaBoletos)) {
            throw new Exception("Não foi encontrado Boleto PJBank para a parcela " + movParcela);
        }
        BoletoPJBankVO obj = listaBoletos.get(0);
        enviarEmailBoletoPjBank(chave, obj, emailEnviar, enviarLinkBoleto, enviarBoletoEmAnexo);
    }

    public void enviarEmailBoletoPjBank(String chave, BoletoPJBankVO obj, String[] emailEnviar,
                                        boolean enviarLinkBoleto, boolean enviarBoletoEmAnexo) throws Exception {

        ConfiguracaoSistemaCRMVO configuracaoSistemaCRMVO = obterConfiguracaoSistemaCRMVO();

        UteisEmail uteisEmail = new UteisEmail();
        uteisEmail.novo("BOLETO", configuracaoSistemaCRMVO);

        BoletoEmailTO boletoEmailTO = new BoletoEmailTO();

        MovParcela movParcelaDAO = new MovParcela(this.con);
        MovParcelaVO movParcelaVO = movParcelaDAO.consultarPorChavePrimaria(obj.getMovParcelaVO().getCodigo(), Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA);
        movParcelaDAO = null;

        Empresa empresaDAO = new Empresa(this.con);
        boletoEmailTO.setEmpresaVO(empresaDAO.consultarPorChavePrimaria(movParcelaVO.getEmpresa().getCodigo(), Uteis.NIVELMONTARDADOS_GESTAOREMESSA));
        empresaDAO = null;

        Pessoa pessoaDAO = new Pessoa(this.con);
        boletoEmailTO.setPessoaVO(pessoaDAO.consultarPorChavePrimaria(movParcelaVO.getPessoa().getCodigo(), Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA));
        pessoaDAO = null;


        boletoEmailTO.setLinhaDigitavel(obj.getLinhaDigitavel());
        boletoEmailTO.setValor(new Double(obj.getValor()));
        boletoEmailTO.setDataVencimento(obj.getDataVencimento());

        if (enviarLinkBoleto) {
            boletoEmailTO.setLinkBoleto(obj.getLinkBoleto());
        }

        if (enviarBoletoEmAnexo) {
            boletoEmailTO.setNomeArquivoBoleto("Boleto-" + obj.getIdUnico() + ".pdf");

            File file = new File(PropsService.getPropertyValue(PropsService.diretorioArquivos) + File.separator + "boleto_email_temp" + File.separator + chave + "-" + boletoEmailTO.getNomeArquivoBoleto());
            byte[] arquivo = ExecuteRequestHttpService.obterByteFromUrl(obj.getLinkBoleto(), null, null);
            FileUtilities.saveToFile(arquivo, file.getPath());
            boletoEmailTO.setUrlFileArquivoBoleto(file.getAbsolutePath());

            uteisEmail.addAnexo(boletoEmailTO.getNomeArquivoBoleto(), file);
        }

        gerarHTMLModeloPadraoBoleto(chave, boletoEmailTO);

        uteisEmail.enviarEmailN(emailEnviar, boletoEmailTO.getHtmlEmail(), "Boleto gerado: " + boletoEmailTO.getEmpresaVO().getNome(), boletoEmailTO.getEmpresaVO().getNome());
    }

    public void gerarHTMLModeloPadraoBoleto(String chave, BoletoEmailTO boletoEmailTO) throws Exception {
        File arq = new File(getClass().getResource("/br/com/pactosolucoes/comuns/util/resources/emailBoletoOnline.txt").toURI());
        StringBuilder texto = FileUtilities.readContentFile(arq.getAbsolutePath(), "UTF-8");

        String empresaNome = boletoEmailTO.getEmpresaVO().getNome().toUpperCase();
        String empresaUrlLogo = boletoEmailTO.getUrlLogoEmpresa(chave);
        String empresaEndereco = boletoEmailTO.getEmpresaVO().getEndereco();
        String empresaTelefone = boletoEmailTO.getEmpresaVO().getTelComercial1();
        String empresaEmail = boletoEmailTO.getEmpresaVO().getEmail();
        String mesReferencia = boletoEmailTO.getMesReferencia();
        String dataVencimento = boletoEmailTO.getDataVencimentoApresentar();
        String valorBoleto = boletoEmailTO.getValorApresentar();
        String nomeAluno = boletoEmailTO.getPessoaVO().getNome();
        String linhaDigitavel = boletoEmailTO.getLinhaDigitavel();
        String linkBoleto = boletoEmailTO.getLinkBoleto();

        boolean enviarBoletoEmAnexo = !UteisValidacao.emptyString(boletoEmailTO.getUrlFileArquivoBoleto());
        boolean enviarLinkBoleto = !UteisValidacao.emptyString(linkBoleto);

        String aux = texto.toString()
                .replaceAll("#ACADEMIA_NOME#", Uteis.trocarAcentuacaoPorAcentuacaoHTML(empresaNome))
                .replaceAll("#ACADEMIA_URL_LOGO#", empresaUrlLogo)
                .replaceAll("#ACADEMIA_ENDERECO#", Uteis.trocarAcentuacaoPorAcentuacaoHTML(empresaEndereco))
                .replaceAll("#ACADEMIA_TELEFONE#", empresaTelefone)
                .replaceAll("#ACADEMIA_EMAIL#", Uteis.trocarAcentuacaoPorAcentuacaoHTML(empresaEmail))
                .replaceAll("#NOME_ALUNO#", Uteis.trocarAcentuacaoPorAcentuacaoHTML(nomeAluno))
                .replaceAll("#MES_REFERENCIA#", Uteis.trocarAcentuacaoPorAcentuacaoHTML(mesReferencia))
                .replaceAll("#DATA_VENCIMENTO#", dataVencimento)
                .replaceAll("#LINHA_DIGITAVEL#", linhaDigitavel)
                .replaceAll("#VALOR_BOLETO#", valorBoleto)
                .replaceAll("#LINK_BOLETO#", linkBoleto)
                .replaceAll("#LINK#", enviarLinkBoleto ? "block" : "none")
                .replaceAll("#ANEXO#", enviarBoletoEmAnexo ? "block" : "none");
        boletoEmailTO.setHtmlEmail(aux);
    }

    public void enviarEmailBoletoLink(String chave, String link, Integer empresa, Integer pessoa,
                                      String[] emailEnviar) throws Exception {

        ConfiguracaoSistemaCRMVO configuracaoSistemaCRMVO = obterConfiguracaoSistemaCRMVO();

        UteisEmail uteisEmail = new UteisEmail();
        uteisEmail.novo("BOLETO", configuracaoSistemaCRMVO);

        BoletoEmailTO boletoEmailTO = new BoletoEmailTO();

        Empresa empresaDAO = new Empresa(this.con);
        boletoEmailTO.setEmpresaVO(empresaDAO.consultarPorChavePrimaria(empresa, Uteis.NIVELMONTARDADOS_GESTAOREMESSA));
        empresaDAO = null;

        Pessoa pessoaDAO = new Pessoa(this.con);
        boletoEmailTO.setPessoaVO(pessoaDAO.consultarPorChavePrimaria(pessoa, Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA));
        pessoaDAO = null;

        boletoEmailTO.setLinkBoleto(link);

        gerarHTMLModeloPadraoBoletoLinkVariosBoletos(chave, boletoEmailTO);

        uteisEmail.enviarEmailN(emailEnviar, boletoEmailTO.getHtmlEmail(), "Boleto gerado: " + boletoEmailTO.getEmpresaVO().getNome(), boletoEmailTO.getEmpresaVO().getNome());
    }

    private ConfiguracaoSistemaCRMVO obterConfiguracaoSistemaCRMVO() throws Exception {
//        ConfiguracaoSistemaCRM configuracaoSistemaCRMDAO = new ConfiguracaoSistemaCRM(con);
//        ConfiguracaoSistemaCRMVO configuracaoSistemaCRMVO = configuracaoSistemaCRMDAO.consultarConfiguracaoSistemaCRM(Uteis.NIVELMONTARDADOS_DADOSBASICOS);
//        configuracaoSistemaCRMDAO = null;
//
//        if (UteisValidacao.emptyString(configuracaoSistemaCRMVO.getMailServer())) {
//            throw new Exception("Não foi possível enviar o e-mail. Verifique as configurações de e-mail no CRM!");
//        }
//        return configuracaoSistemaCRMVO;
        return SuperControle.getConfiguracaoSMTPNoReply();
    }

    public void gerarHTMLModeloPadraoBoletoLinkVariosBoletos(String chave, BoletoEmailTO boletoEmailTO) throws Exception {

        File arq = new File(getClass().getResource("/br/com/pactosolucoes/comuns/util/resources/emailBoletoOnlineSomenteLink.txt").toURI());

        StringBuilder texto = FileUtilities.readContentFile(arq.getAbsolutePath());

        String empresaNome = boletoEmailTO.getEmpresaVO().getNome().toUpperCase();
        String empresaUrlLogo = boletoEmailTO.getUrlLogoEmpresa(chave);
        String empresaEndereco = boletoEmailTO.getEmpresaVO().getEndereco();
        String empresaTelefone = boletoEmailTO.getEmpresaVO().getTelComercial1();
        String empresaEmail = boletoEmailTO.getEmpresaVO().getEmail();
        String nomeAluno = boletoEmailTO.getPessoaVO().getNome();
        String linkBoleto = boletoEmailTO.getLinkBoleto();

        String aux = texto.toString()
                .replaceAll("#ACADEMIA_NOME#", Uteis.trocarAcentuacaoPorAcentuacaoHTML(empresaNome))
                .replaceAll("#ACADEMIA_URL_LOGO#", empresaUrlLogo)
                .replaceAll("#ACADEMIA_ENDERECO#", Uteis.trocarAcentuacaoPorAcentuacaoHTML(empresaEndereco))
                .replaceAll("#ACADEMIA_TELEFONE#", empresaTelefone)
                .replaceAll("#ACADEMIA_EMAIL#", Uteis.trocarAcentuacaoPorAcentuacaoHTML(empresaEmail))
                .replaceAll("#NOME_ALUNO#", Uteis.trocarAcentuacaoPorAcentuacaoHTML(nomeAluno))
                .replaceAll("#LINK_BOLETO#", linkBoleto);
        boletoEmailTO.setHtmlEmail(aux);
    }

    public List<BoletoPJBankVO> consultarBoletosStatusErrado() throws Exception {
        String sql = "SELECT b.*, m.descricao \n" +
                "   FROM boletospjbank b \n" +
                "   INNER JOIN movparcela m on b.movparcela = m.codigo \n" +
                "   INNER JOIN pessoa p on p.codigo = m.pessoa \n" +
                "WHERE ((b.recibopagamento > 0 AND b.registrosistemabancario = 'confirmado' AND b.valorpago <= 0) " +
                "OR (coalesce(b.recibopagamento, 0) = 0 and valorpago <= 0 and b.datavencimento < current_date) \n" +
                "OR b.registrosistemabancario ilike '%registro%') \n" +
                " order by p.nome ";
        List<BoletoPJBankVO> lista = new ArrayList<>();
        try (Statement stm = con.createStatement()) {
            try (ResultSet tabelaResultado = stm.executeQuery(sql)) {
                while (tabelaResultado.next()) {
                    BoletoPJBankVO obj = montarDados(tabelaResultado, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
                    if (!UteisValidacao.emptyNumber(obj.getPessoaVO().getCodigo())) {
                        Pessoa pessoaDAO = new Pessoa(con);
                        obj.setPessoaVO(pessoaDAO.consultarPorChavePrimaria(obj.getPessoaVO().getCodigo(), Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA));
                        pessoaDAO = null;
                    }
                    lista.add(obj);
                }
            }
        }
        return lista;
    }

    public String cancelarBoleto(BoletoPJBankVO obj, UsuarioVO usuarioVO) throws Exception {
        try {

            if (!obj.isPodeCancelar()) {
                throw new Exception("Boleto não pode ser cancelado");
            }

            ConvenioCobranca convenioCobrancaDAO = new ConvenioCobranca(con);
            ConvenioCobrancaVO cobrancaVO = convenioCobrancaDAO.consultarPorChavePrimaria(obj.getConvenioCobrancaVO().getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS);
            convenioCobrancaDAO = null;

            String credencial = cobrancaVO.getCredencialPJBank();
            String chavePJBank = cobrancaVO.getChavePJBank();

            BoletosManager boletosManager = new BoletosManager(credencial, chavePJBank, cobrancaVO);
            //verificar se o boleto não foi pago
            validarSeBoletoEstaPago(obj, boletosManager);

            String response = boletosManager.cancelar(obj);
            incluirHistoricoRetornoPJBank(obj.getCodigo(), "cancelarBoleto", response);

            JSONObject json = null;
            try {
                json = new JSONObject(response);
            } catch (Exception ex) {
                ex.printStackTrace();
            }

            if (json != null && json.optString("status").equalsIgnoreCase("200")) {
                String operacao = ("Cancelamento de Boleto - " + obj.getCodigo());
                JSONObject jsonEstorno = gerarBaseJSONEstorno(operacao, usuarioVO);
                removerMovParcelaAtualizarJsonEstorno(("Cancelar Boleto " + obj.getCodigo()), jsonEstorno.toString(), obj.getCodigo());
                alterarSomenteRegistroBancario("Cancelado", obj.getCodigo());
                return "Boleto " + obj.getIdUnico() + " cancelado com sucesso!";
            } else {
                throw new Exception("Não foi possível cancelar, tente novamente mais tarde. " + response);
            }
        } catch (Exception ex) {
            ex.printStackTrace();
            throw ex;
        }
    }

    private void validarSeBoletoEstaPago(BoletoPJBankVO obj, BoletosManager boletosManager) throws Exception {
        //consultar dados do boleto
        String response = boletosManager.get(obj);
        JSONObject json = new JSONArray(response).getJSONObject(0);
        if (!UteisValidacao.emptyString(json.optString("data_pagamento"))) {
            throw new Exception("Boleto não pode ser cancelado pois o seu status está como PAGO");
        }
    }

    public List<BoletoPJBankVO> consultarBoletosSincronizar(Integer convenioCobranca) throws Exception {
        StringBuilder sql = new StringBuilder();
        sql.append("SELECT b.*, m.descricao \n");
        sql.append("FROM boletospjbank b \n");
        sql.append("INNER JOIN movparcela m on b.movparcela = m.codigo \n");
        sql.append("WHERE (b.registrosistemabancario ilike '%registro%' or b.registrosistemabancario ilike '%enviado%' or b.registrosistemabancario ilike '%pendente%') \n");
        if (!UteisValidacao.emptyNumber(convenioCobranca)) {
            sql.append("AND b.conveniocobranca = ").append(convenioCobranca).append(" \n");
        }
        sql.append("order by b.codigo \n");
        try (Statement stm = con.createStatement()) {
            try (ResultSet tabelaResultado = stm.executeQuery(sql.toString())) {
                return montarDadosConsulta(tabelaResultado, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
            }
        }
    }

    public void cancelarBoletos(List<BoletoPJBankVO> listaBoletosCancelar, UsuarioVO usuarioVO, String operacao) {
        if (!UteisValidacao.emptyList(listaBoletosCancelar)) {
            for (BoletoPJBankVO boletoPJBankVO : listaBoletosCancelar) {
                try {
                    cancelarBoleto(boletoPJBankVO, usuarioVO);
                } catch (Exception ex) {
                    ex.printStackTrace();
                    registrarLogPJBank(operacao + " | " + ex.getMessage(), boletoPJBankVO.getCodigo());
                }
            }
        }
    }
}

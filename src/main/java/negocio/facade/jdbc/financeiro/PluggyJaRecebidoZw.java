package negocio.facade.jdbc.financeiro;

import negocio.comuns.financeiro.PluggyJaRecebidoZwVO;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisValidacao;
import negocio.facade.jdbc.arquitetura.SuperEntidade;
import negocio.interfaces.financeiro.PluggyJaRecebidoZWFacade;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;

public class PluggyJaRecebidoZw extends SuperEntidade implements PluggyJaRecebidoZWFacade {

    public PluggyJaRecebidoZw() throws Exception {
        super();
    }

    public PluggyJaRecebidoZw(Connection con) throws Exception {
        super(con);
    }

    private static void validarDados(PluggyJaRecebidoZwVO obj) throws Exception {
        if (UteisValidacao.emptyNumber(obj.getMovContaTransactionPluggyVO().getCodigo())) {
            throw new Exception("movContaTransactionPluggyVO não pode ser vazio ou nulo");
        }
    }

    public PluggyJaRecebidoZwVO incluir(PluggyJaRecebidoZwVO obj) throws Exception {
        try {
            validarDados(obj);
            String sql = "INSERT INTO pluggyjarecebidozw(descricao, valor, dataVencimento, dataoperacao, movContaTransactionPluggy ) VALUES (?, ?, ?, ?, ?) RETURNING codigo";
            try (PreparedStatement sqlInserir = con.prepareStatement(sql)) {
                sqlInserir.setString(1, obj.getDescricao());
                sqlInserir.setDouble(2, obj.getValor());
                sqlInserir.setTimestamp(3, Uteis.getDataJDBCTimestamp(obj.getDataVencimento()));
                sqlInserir.setTimestamp(4, Uteis.getDataJDBCTimestamp(obj.getDataOperacao()));
                sqlInserir.setInt(5, obj.getMovContaTransactionPluggyVO().getCodigo());

                ResultSet rs = sqlInserir.executeQuery();
                if (rs.next()) {
                    obj.setCodigo(rs.getInt("codigo"));
                }
                return obj;
            }
        } catch (Exception e) {
            throw e;
        }
    }

    public PluggyJaRecebidoZwVO consultarPorChavePrimaria(int codigo) throws Exception {
        String sql = "SELECT * FROM PluggyJaRecebidoZw WHERE codigo = " + codigo;
        try (PreparedStatement ps = con.prepareStatement(sql)) {
            try (ResultSet rs = ps.executeQuery()) {
                while (rs.next()) {
                    return montarDados(rs);
                }
            }
        }
        return null;
    }

    public PluggyJaRecebidoZwVO montarDados(ResultSet dadosSQL) throws Exception {
        PluggyJaRecebidoZwVO obj = new PluggyJaRecebidoZwVO();
        obj.setCodigo(dadosSQL.getInt("codigo"));
        obj.setDescricao(dadosSQL.getString("descricao"));
        obj.setValor(dadosSQL.getDouble("valor"));
        obj.setDataVencimento(dadosSQL.getTimestamp("datavencimento"));
        obj.setDataOperacao(dadosSQL.getTimestamp("dataoperacao"));

        return obj;
    }

    public void excluirByMovContaTransactionPluggy(int movContaTransactionPluggy) throws Exception {
        try {
            executarConsulta("DELETE from pluggyJaRecebidoZw where movContaTransactionPluggy = " + movContaTransactionPluggy, con);
        } catch (Exception e) {
            throw e;
        }
    }
}

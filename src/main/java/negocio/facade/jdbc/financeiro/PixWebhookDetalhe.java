package negocio.facade.jdbc.financeiro;

import negocio.comuns.financeiro.PixWebhookDetalheVO;
import negocio.comuns.utilitarias.Calendario;
import negocio.comuns.utilitarias.Uteis;
import negocio.facade.jdbc.arquitetura.SuperEntidade;
import negocio.interfaces.financeiro.PixWebhookDetalheInterfaceFacade;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.Types;


/**
 * Created by <PERSON> on 21/11/2023.
 */

public class PixWebhookDetalhe extends SuperEntidade implements PixWebhookDetalheInterfaceFacade {

    public PixWebhookDetalhe() throws Exception {
        super();
    }

    public PixWebhookDetalhe(Connection connection) throws Exception {
        super(connection);
    }

    public void incluir(PixWebhookDetalheVO pixWebhookDetalheVO) throws Exception {
        String sql = "INSERT INTO pixWebhookDetalhe(pixwebhook, txid, pix, dataRegistro, processado, dataProcessamento) VALUES (?,?,?,?,?,?)";
        try (PreparedStatement pst = con.prepareStatement(sql)) {
            pst.setInt(1, pixWebhookDetalheVO.getPixWebhook());
            pst.setString(2, pixWebhookDetalheVO.getTxid());
            pst.setInt(3, pixWebhookDetalheVO.getPix());
            pst.setTimestamp(4, Uteis.getDataJDBCTimestamp(Calendario.hoje()));
            pst.setBoolean(5, pixWebhookDetalheVO.isProcessado());
            if (pixWebhookDetalheVO.getDataProcessamento() != null) {
                pst.setTimestamp(6, Uteis.getDataJDBCTimestamp(pixWebhookDetalheVO.getDataProcessamento()));
            } else {
                pst.setNull(6, Types.NULL);
            }
            pst.execute();
        }
    }

    public PixWebhookDetalheVO consultar(int pix, String txId) throws Exception {
        String sql = "SELECT * FROM pixWebhookDetalhe WHERE pix = " + pix + " AND txid = '" + txId + "' ORDER BY codigo desc LIMIT 1";
        ResultSet rs = con.prepareStatement(sql).executeQuery();
        while (rs.next()) {
            PixWebhookDetalheVO pixWebhookDetalheVO = new PixWebhookDetalheVO();
            pixWebhookDetalheVO.setCodigo(rs.getInt("codigo"));
            pixWebhookDetalheVO.setPixWebhook(rs.getInt("pixwebhook"));
            pixWebhookDetalheVO.setTxid(rs.getString("txid"));
            pixWebhookDetalheVO.setPix(rs.getInt("pix"));
            pixWebhookDetalheVO.setDataRegistro(rs.getTimestamp("dataRegistro"));
            pixWebhookDetalheVO.setProcessado(rs.getBoolean("processado"));
            pixWebhookDetalheVO.setDataProcessamento(rs.getTimestamp("dataProcessamento"));
            return pixWebhookDetalheVO;
        }
        return null;
    }

    public boolean pixJaFoiProcessado(int pix, String txId) throws Exception {
        String sql = "SELECT * FROM pixWebhookDetalhe WHERE pix = " + pix + " AND txid = '" + txId + "' ORDER BY codigo desc LIMIT 1";
        ResultSet rs = con.prepareStatement(sql).executeQuery();
        if (rs.next()) {
            return rs.getBoolean("processado");
        }
        return false;
    }

    public void colocarComoJaProcessado(int codPixWebhook, int pix, String txId) throws Exception {

        StringBuilder sql = new StringBuilder();
        sql.append("UPDATE pixWebhookDetalhe SET processado = TRUE, dataProcessamento = ? WHERE txid = ? AND pix = ? AND pixwebhook = ?");

        try (PreparedStatement pst = con.prepareStatement(sql.toString())) {
            pst.setTimestamp(1, Uteis.getDataJDBCTimestamp(Calendario.hoje()));
            pst.setString(2, txId);
            pst.setInt(3, pix);
            pst.setInt(4, codPixWebhook);
            pst.execute();
        }

    }

}


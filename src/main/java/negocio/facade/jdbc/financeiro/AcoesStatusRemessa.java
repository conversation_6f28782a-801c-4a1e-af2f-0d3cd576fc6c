/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */

package negocio.facade.jdbc.financeiro;

import negocio.comuns.basico.AcoesStatusRemessaVO;
import negocio.comuns.basico.enumerador.AcoesRemessasEnum;
import negocio.comuns.utilitarias.UteisValidacao;
import negocio.facade.jdbc.arquitetura.SuperEntidade;
import negocio.interfaces.basico.AcoesStatusRemessaInterfaceFacade;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 *
 * <AUTHOR>
 */
public class AcoesStatusRemessa extends SuperEntidade implements AcoesStatusRemessaInterfaceFacade{

    public AcoesStatusRemessa() throws Exception {
        super();
    }

    public AcoesStatusRemessa(Connection con) throws Exception {
        super(con);
    }

    @Override
    public void salvar(AcoesStatusRemessaVO acaoStatus) throws Exception {
        String sql = "";
        if (jaExiste(acaoStatus)) {
            sql = "UPDATE acoesstatusremessa set acao = ?, reagendarAutomaticamente = ?,\n"
                    + "qtdTentativasParaReagendamentoAutomatico = ?, qtdDiasParaReagendamentoAutomatico = ? WHERE codigoStatus Like ? ";
        } else {
            sql = "INSERT INTO acoesstatusremessa (acao, reagendarAutomaticamente, qtdTentativasParaReagendamentoAutomatico, qtdDiasParaReagendamentoAutomatico, codigoStatus) VALUES (?, ?, ?, ?, ?);";
        }
        if(!UteisValidacao.emptyNumber(acaoStatus.getCodigo()) && mudouStatus(acaoStatus)){
            excluir(acaoStatus);
        }
        PreparedStatement stm = con.prepareStatement(sql);
        int i = 1;
        stm.setInt(i++,acaoStatus.getAcao().getCodigo());
        if (!acaoStatus.getAcao().getReenvio()) {
            stm.setBoolean(i++, false);
            stm.setInt(i++, 0);
            stm.setInt(i++, 0);
        } else {
            stm.setBoolean(i++, acaoStatus.getReagendarAutomaticamente());
            stm.setInt(i++, acaoStatus.getQtdTentativasParaReagendamentoAutomatico());
            stm.setInt(i++, acaoStatus.getQtdDiasParaReagendamentoAutomatico());
        }

        stm.setString(i++,acaoStatus.getCodigoStatus());
        stm.execute();
    }
    public boolean jaExiste(AcoesStatusRemessaVO acaoStatus) throws Exception{
        String sql = "SELECT * FROM acoesstatusremessa where codigostatus Like '"+acaoStatus.getCodigoStatus()+"'";
        return criarConsulta(sql, con).next();
    }

    public boolean mudouStatus(AcoesStatusRemessaVO acaoStatus) throws Exception {
        String sql = "SELECT codigostatus FROM acoesstatusremessa where codigo = " + acaoStatus.getCodigo();
        ResultSet consulta = criarConsulta(sql, con);
        return consulta.next() && !acaoStatus.getCodigoStatus().equals(consulta.getString("codigostatus"));
    }


    @Override
    public void excluir(AcoesStatusRemessaVO acaoStatus) throws Exception {
        executarConsulta("DELETE FROM acoesstatusremessa where codigo = "+acaoStatus.getCodigo(), con);
    }

    @Override
    public List<AcoesStatusRemessaVO> consultarPorTodos() throws Exception {
        return montarDadosConsulta(criarConsulta("SELECT * FROM acoesstatusremessa order by codigostatus", con));
    }

    public List<AcoesStatusRemessaVO> consultarPorAcao(AcoesRemessasEnum acao) throws Exception {
        if (acao == null) {
            return new ArrayList<AcoesStatusRemessaVO>();
        }
        return montarDadosConsulta(criarConsulta("SELECT * FROM acoesstatusremessa WHERE acao = " + acao.getCodigo() + " order by codigostatus", con));
    }

    @Override
    public Map<String, Integer> consultarPorTodosMapa() throws Exception {
        List<AcoesStatusRemessaVO> acoes = consultarPorTodos();
        Map<String, Integer> mapa = new HashMap<String, Integer>();
        for(AcoesStatusRemessaVO acao : acoes){
            mapa.put(acao.getCodigoStatus(), acao.getCodigoAcao());
        }
        return mapa;
    }

    @Override
    public List<AcoesStatusRemessaVO> montarDadosConsulta(ResultSet dados) throws Exception{
        List<AcoesStatusRemessaVO> lista = new ArrayList<AcoesStatusRemessaVO>();
        while(dados.next()){
            lista.add(montarDados(dados));
        }
        return lista;
    }

    @Override
    public AcoesStatusRemessaVO montarDados(ResultSet dados) throws Exception{
        AcoesStatusRemessaVO acao = new AcoesStatusRemessaVO();
        acao.setCodigoStatus(dados.getString("codigoStatus"));
        acao.setCodigo(dados.getInt("codigo"));
        acao.setAcao(AcoesRemessasEnum.getFromId(dados.getInt("acao")));
        acao.setReagendarAutomaticamente(dados.getBoolean("reagendarAutomaticamente"));
        acao.setQtdTentativasParaReagendamentoAutomatico(dados.getInt("qtdTentativasParaReagendamentoAutomatico"));
        acao.setQtdDiasParaReagendamentoAutomatico(dados.getInt("qtdDiasParaReagendamentoAutomatico"));
        return acao;
    }


}

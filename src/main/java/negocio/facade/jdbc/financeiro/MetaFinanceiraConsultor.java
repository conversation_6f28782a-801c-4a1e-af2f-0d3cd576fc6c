
package negocio.facade.jdbc.financeiro;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Calendar;
import negocio.comuns.basico.ColaboradorVO;
import negocio.comuns.financeiro.MetaFinanceiraConsultorVO;
import negocio.comuns.utilitarias.Calendario;
import negocio.comuns.utilitarias.Uteis;
import negocio.facade.jdbc.arquitetura.SuperEntidade;
import negocio.facade.jdbc.basico.Colaborador;
import negocio.interfaces.financeiro.MetaFinanceiraConsultorInterfaceFacade;

/**
 *
 * <AUTHOR>
 */
public class MetaFinanceiraConsultor extends SuperEntidade implements MetaFinanceiraConsultorInterfaceFacade {

    public MetaFinanceiraConsultor() throws Exception {
        super();
    }

    public MetaFinanceiraConsultor(Connection conexao) throws Exception {
        super(conexao);
    }

    @Override
    public void incluir(MetaFinanceiraConsultorVO obj) throws Exception {
        try {
            con.setAutoCommit(false);
            incluirSemCommit(obj);
            con.commit();
        } catch (Exception e) {
            con.rollback();
            throw e;
        } finally {
            con.setAutoCommit(true);
        }
    }

    @Override
    public void incluirSemCommit(MetaFinanceiraConsultorVO obj) throws Exception {
        obj.validarDados();
        PreparedStatement sql = con.prepareStatement("INSERT INTO MetaFinanceiraConsultor "
                + "(metaFinanceiraEmpresa, colaborador, percentagem) VALUES (?, ?, ?)");
        sql.setInt(1, obj.getMetaFinanceiraEmpresa());
        sql.setInt(2, obj.getColaborador().getCodigo());
        sql.setDouble(3, obj.getPercentagem());
        sql.execute();
        obj.setCodigo(obterValorChavePrimariaCodigo());
        obj.setNovoObj(false);
    }

    @Override
    public void alterar(MetaFinanceiraConsultorVO obj) throws Exception {
        try {
            con.setAutoCommit(false);
            alterarSemCommit(obj);
            con.commit();
        } catch (Exception e) {
            con.rollback();
            throw e;
        } finally {
            con.setAutoCommit(true);
        }
    }

    @Override
    public void alterarSemCommit(MetaFinanceiraConsultorVO obj) throws Exception {
        obj.validarDados();
        PreparedStatement sqlAlterar = con.prepareStatement("UPDATE MetaFinanceiraConsultor SET "
                + "metaFinanceiraEmpresa=?, colaborador=?, percentagem=? WHERE codigo = ?");
        sqlAlterar.setInt(1, obj.getMetaFinanceiraEmpresa());
        sqlAlterar.setInt(2, obj.getColaborador().getCodigo());
        sqlAlterar.setDouble(3, obj.getPercentagem());
        sqlAlterar.setInt(4, obj.getCodigo());
        sqlAlterar.execute();
    }

    @Override
    public void excluir(MetaFinanceiraConsultorVO obj) throws Exception {
        try {
            con.setAutoCommit(false);
            excluirSemCommit(obj);
            con.commit();
        } catch (Exception e) {
            con.rollback();
            throw e;
        } finally {
            con.setAutoCommit(true);
        }
    }

    @Override
    public void excluirSemCommit(MetaFinanceiraConsultorVO obj) throws Exception {
        PreparedStatement sqlExcluir = con.prepareStatement("DELETE FROM MetaFinanceiraConsultor WHERE codigo = ?");
        sqlExcluir.setInt(1, obj.getCodigo());
        sqlExcluir.execute();
    }

    @Override
    public void excluirPelaMeta(int meta) throws Exception {
        PreparedStatement sqlExcluir = con.prepareStatement("DELETE FROM MetaFinanceiraConsultor WHERE metaFinanceiraEmpresa = ?");
        sqlExcluir.setInt(1, meta);
        sqlExcluir.execute();
    }

    @Override
    public MetaFinanceiraConsultorVO consultarPorChavePrimaria(int codigo, int nivelMontarDados) throws Exception {
        StringBuilder str = new StringBuilder();
        str.append("SELECT * FROM MetaFinanceiraConsultor WHERE codigo = ").append(codigo);
        PreparedStatement stm = con.prepareStatement(str.toString());
        ResultSet tabelaResultado = stm.executeQuery();
        if(tabelaResultado.next())
            return montarDados(tabelaResultado, nivelMontarDados, this.con);
        else
            return new MetaFinanceiraConsultorVO();
    }

    @Override
    public List<MetaFinanceiraConsultorVO> consultarPorMetaFinanceiraEmpresa(int codigo, int nivelMontarDados) throws Exception {
        StringBuilder str = new StringBuilder();
        str.append("SELECT * FROM MetaFinanceiraConsultor WHERE metaFinanceiraEmpresa = ").append(codigo);
        PreparedStatement stm = con.prepareStatement(str.toString());
        ResultSet tabelaResultado = stm.executeQuery();
        return montarDadosConsulta(tabelaResultado, nivelMontarDados, this.con);
    }
    
    public MetaFinanceiraConsultorVO consultarPorColaboradorMetaDaEmpresa(int colaborador, int metaFinanceira) throws Exception {
        StringBuilder str = new StringBuilder();
		str.append("SELECT * FROM metafinanceiraconsultor WHERE colaborador = "+colaborador+" AND metafinanceiraempresa = "+metaFinanceira);
		
		PreparedStatement stm = con.prepareStatement(str.toString());
        ResultSet tabelaResultado = stm.executeQuery();
        if(tabelaResultado.next())
        	return montarDadosBasico(tabelaResultado);
        	return new MetaFinanceiraConsultorVO();
    }

    public List<MetaFinanceiraConsultorVO> montarDadosConsulta(ResultSet tabelaResultado, int nivelMontarDados, Connection con) throws Exception {
        List<MetaFinanceiraConsultorVO> vetResultado = new ArrayList<MetaFinanceiraConsultorVO>();
        while (tabelaResultado.next())
            vetResultado.add(montarDados(tabelaResultado, nivelMontarDados, con));
        return vetResultado;
    }

    public MetaFinanceiraConsultorVO montarDados(ResultSet dadosSQL, int nivelMontarDados, Connection con) throws Exception {
        MetaFinanceiraConsultorVO obj = montarDadosBasico(dadosSQL);
        if (nivelMontarDados == Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA) {
            return obj;
        }
        montarColaborador(obj, con);
        return obj;
    }

    private MetaFinanceiraConsultorVO montarDadosBasico(ResultSet dadosSQL) throws Exception {
        MetaFinanceiraConsultorVO obj = new MetaFinanceiraConsultorVO();
        obj.setNovoObj(false);
        obj.setCodigo(dadosSQL.getInt("codigo"));
        obj.setColaborador(new ColaboradorVO());
        obj.getColaborador().setCodigo(dadosSQL.getInt("colaborador"));
        obj.setPercentagem(dadosSQL.getDouble("percentagem"));
        obj.setMetaFinanceiraEmpresa(dadosSQL.getInt("metaFinanceiraEmpresa"));
        return obj;
    }
    
    public void montarColaborador(MetaFinanceiraConsultorVO obj, Connection con) throws Exception {
        Colaborador colaborador = new Colaborador(con);
        obj.setColaborador(colaborador.consultarPorChavePrimaria(obj.getColaborador().getCodigo().intValue(),
                Uteis.NIVELMONTARDADOS_MINIMOS));
    }
    
    public boolean existeMetaColaboradorPorEmpresaData(int colaborador, int empresa, Date dataConsulta) throws Exception{
        Calendar dataCalendar = Calendario.getInstance();
        dataCalendar.setTime(dataConsulta);
        String sql = "select * from metafinanceiraconsultor col inner join metafinanceiraempresa meta on meta.codigo = col.metafinanceiraempresa where col.colaborador = "+colaborador+" and meta.empresa = "+empresa+" and meta.mes = "+ (dataCalendar.get(Calendar.MONTH)+1) +" and meta.ano = "+dataCalendar.get(Calendar.YEAR);
        return existe(sql, con);
    }
}

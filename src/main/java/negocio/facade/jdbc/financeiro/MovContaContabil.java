package negocio.facade.jdbc.financeiro;

import negocio.comuns.financeiro.ContaContabilVO;
import negocio.comuns.financeiro.MovContaContabilVO;
import negocio.comuns.financeiro.MovContaVO;
import negocio.comuns.utilitarias.UtilReflection;
import negocio.facade.jdbc.arquitetura.SuperEntidade;
import negocio.interfaces.financeiro.MovContaContabilInterfaceFacade;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.Statement;
import java.sql.Types;

/**
 * Created by ulisses on 09/02/2017.
 */
public class MovContaContabil extends SuperEntidade implements MovContaContabilInterfaceFacade {

    private static StringBuilder SQL_PADRAO = new StringBuilder()
                    .append("select mc.*, \n")
                    .append("      contaCreditoValor.descricao as descricaoContaCreditoValor, contaCreditoValor.codigoIntegracao as codigoIntegracaoContaCreditoValor,  \n")
                    .append("      contaDebitoValor.descricao as descricaoContaDebitoValor, contaDebitoValor.codigoIntegracao as codigoIntegracaoContaDebitoValor, \n")
                    .append("      contaCreditoMulta.descricao as descricaoContaCreditoMulta, contaCreditoMulta.codigoIntegracao as codigoIntegracaoContaCreditoMulta, \n")
                    .append("      contaDebitoMulta.descricao as descricaoContaDebitoMulta, contaDebitoMulta.codigoIntegracao as codigoIntegracaoContaDebitoMulta, \n")
                    .append("      contaCreditoJuro.descricao as descricaoContaCreditoJuro, contaCreditoJuro.codigoIntegracao as codigoIntegracaoContaCreditoJuro, \n")
                    .append("      contaDebitoJuro.descricao as descricaoContaDebitoJuro, contaDebitoJuro.codigoIntegracao as codigoIntegracaoContaDebitoJuro \n")
                    .append("from movcontacontabil mc \n")
                    .append("left join contaContabil contaCreditoValor on contaCreditoValor.codigo = mc.contaContabilCreditoValor \n")
                    .append("left join contaContabil contaDebitoValor on contaDebitoValor.codigo = mc.contaContabilDebitoValor \n")
                    .append("left join contaContabil contaCreditoMulta on contaCreditoMulta.codigo = mc.contaContabilCreditoMulta \n")
                    .append("left join contaContabil contaDebitoMulta on contaDebitoMulta.codigo = mc.contaContabilDebitoMulta \n")
                    .append("left join contaContabil contaCreditoJuro on contaCreditoJuro.codigo = mc.contaContabilCreditoJuro \n")
                    .append("left join contaContabil contaDebitoJuro on contaDebitoJuro.codigo = mc.contaContabilDebitoJuro \n");

    public MovContaContabil() throws Exception {
        super();
    }

    public MovContaContabil(Connection con) throws Exception {
        super(con);
    }

    public void incluir(MovContaContabilVO movContaContabilVO)throws Exception{
        if (movContaContabilVO.getMovContaVO() != null && movContaContabilVO.getMovContaVO().isIncluirMovContaContabil()){
            MovContaContabilVO.validarDados(movContaContabilVO);
            StringBuilder sql = new StringBuilder();
            sql.append("insert into movContaContabil (movConta,historicoContabil,complementoHistoricoContabil, \n");
            sql.append("contaContabilCreditoValor,contaContabilDebitoValor,valorMulta,valorJuro, \n");
            sql.append("contaContabilCreditoMulta,contaContabilDebitoMulta,contaContabilCreditoJuro,contaContabilDebitoJuro) \n");
            sql.append("values(?,?,?,?,?,?,?,?,?,?,?)");
            PreparedStatement pst = con.prepareStatement(sql.toString());
            int i = 1;
            pst.setInt(i++, movContaContabilVO.getMovContaVO().getCodigo());
            if (UtilReflection.objetoMaiorQueZero(movContaContabilVO, "getHistoricoContabil()")){
                pst.setInt(i++, movContaContabilVO.getHistoricoContabil());
            }else{
                pst.setNull(i++, Types.NULL);
            }
            if ((movContaContabilVO.getComplementoHistoricoContabil() != null) && (!movContaContabilVO.getComplementoHistoricoContabil().trim().equals(""))){
                pst.setString(i++, movContaContabilVO.getComplementoHistoricoContabil());
            }else{
                pst.setNull(i++, Types.NULL);
            }
            if (UtilReflection.objetoMaiorQueZero(movContaContabilVO, "getContaContabilCreditoValor().getCodigo()")){
                pst.setInt(i++, movContaContabilVO.getContaContabilCreditoValor().getCodigo());
            }else{
                pst.setNull(i++, Types.NULL);
            }
            if (UtilReflection.objetoMaiorQueZero(movContaContabilVO, "getContaContabilDebitoValor().getCodigo()")){
                pst.setInt(i++, movContaContabilVO.getContaContabilDebitoValor().getCodigo());
            }else{
                pst.setNull(i++, Types.NULL);
            }
            if ((movContaContabilVO.getValorMulta() != null) && (movContaContabilVO.getValorMulta() >0)){
                pst.setDouble(i++, movContaContabilVO.getValorMulta());
            }else{
                pst.setNull(i++, Types.NULL);
            }
            if ((movContaContabilVO.getValorJuro() != null) && (movContaContabilVO.getValorJuro() >0)){
                pst.setDouble(i++, movContaContabilVO.getValorJuro());
            }else{
                pst.setNull(i++, Types.NULL);
            }
            if (UtilReflection.objetoMaiorQueZero(movContaContabilVO, "getContaContabilCreditoMulta().getCodigo()")){
                pst.setInt(i++, movContaContabilVO.getContaContabilCreditoMulta().getCodigo());
            }else{
                pst.setNull(i++, Types.NULL);
            }
            if (UtilReflection.objetoMaiorQueZero(movContaContabilVO, "getContaContabilDebitoMulta().getCodigo()")){
                pst.setInt(i++, movContaContabilVO.getContaContabilDebitoMulta().getCodigo());
            }else{
                pst.setNull(i++, Types.NULL);
            }
            if (UtilReflection.objetoMaiorQueZero(movContaContabilVO, "getContaContabilCreditoJuro().getCodigo()")){
                pst.setInt(i++, movContaContabilVO.getContaContabilCreditoJuro().getCodigo());
            }else{
                pst.setNull(i++, Types.NULL);
            }
            if (UtilReflection.objetoMaiorQueZero(movContaContabilVO, "getContaContabilDebitoJuro().getCodigo()")){
                pst.setInt(i++, movContaContabilVO.getContaContabilDebitoJuro().getCodigo());
            }else{
                pst.setNull(i++, Types.NULL);
            }
            pst.execute();
            movContaContabilVO.setCodigo(obterValorChavePrimariaCodigo());
        }
    }

    public void alterar(MovContaContabilVO movContaContabilVO)throws Exception{
        if (movContaContabilVO.getMovContaVO().isIncluirMovContaContabil()){
            MovContaContabilVO.validarDados(movContaContabilVO);
            StringBuilder sql = new StringBuilder();
            sql.append("update movContaContabil set historicoContabil=?, complementoHistoricoContabil=?, \n");
            sql.append("contaContabilCreditoValor=?, contaContabilDebitoValor=?, valorMulta=?, valorJuro=?, \n");
            sql.append("contaContabilCreditoMulta=?, contaContabilDebitoMulta=?, contaContabilCreditoJuro=?, contaContabilDebitoJuro=? \n");
            sql.append("where codigo = ?");
            PreparedStatement pst = con.prepareStatement(sql.toString());
            int i = 1;

            if (UtilReflection.objetoMaiorQueZero(movContaContabilVO, "getHistoricoContabil()")){
                pst.setInt(i++, movContaContabilVO.getHistoricoContabil());
            }else{
                pst.setNull(i++, Types.NULL);
            }
            if ((movContaContabilVO.getComplementoHistoricoContabil() != null) && (!movContaContabilVO.getComplementoHistoricoContabil().trim().equals(""))){
                pst.setString(i++, movContaContabilVO.getComplementoHistoricoContabil());
            }else{
                pst.setNull(i++, Types.NULL);
            }

            if (UtilReflection.objetoMaiorQueZero(movContaContabilVO, "getContaContabilCreditoValor().getCodigo()")){
                pst.setInt(i++, movContaContabilVO.getContaContabilCreditoValor().getCodigo());
            }else{
                pst.setNull(i++, Types.NULL);
            }
            if (UtilReflection.objetoMaiorQueZero(movContaContabilVO, "getContaContabilDebitoValor().getCodigo()")){
                pst.setInt(i++, movContaContabilVO.getContaContabilDebitoValor().getCodigo());
            }else{
                pst.setNull(i++, Types.NULL);
            }
            if (movContaContabilVO.getMovContaVO().getDataQuitacao() == null){
                pst.setNull(i++, Types.NULL);
                pst.setNull(i++, Types.NULL);
                pst.setNull(i++, Types.NULL);
                pst.setNull(i++, Types.NULL);
                pst.setNull(i++, Types.NULL);
                pst.setNull(i++, Types.NULL);
            }else{
                if ((movContaContabilVO.getValorMulta() != null) && (movContaContabilVO.getValorMulta() >0)){
                    pst.setDouble(i++, movContaContabilVO.getValorMulta());
                }else{
                    pst.setNull(i++, Types.NULL);
                }
                if ((movContaContabilVO.getValorJuro() != null) && (movContaContabilVO.getValorJuro() >0)){
                    pst.setDouble(i++, movContaContabilVO.getValorJuro());
                }else{
                    pst.setNull(i++, Types.NULL);
                }
                if (UtilReflection.objetoMaiorQueZero(movContaContabilVO, "getContaContabilCreditoMulta().getCodigo()")){
                    pst.setInt(i++, movContaContabilVO.getContaContabilCreditoMulta().getCodigo());
                }else{
                    pst.setNull(i++, Types.NULL);
                }
                if (UtilReflection.objetoMaiorQueZero(movContaContabilVO, "getContaContabilDebitoMulta().getCodigo()")){
                    pst.setInt(i++, movContaContabilVO.getContaContabilDebitoMulta().getCodigo());
                }else{
                    pst.setNull(i++, Types.NULL);
                }
                if (UtilReflection.objetoMaiorQueZero(movContaContabilVO, "getContaContabilCreditoJuro().getCodigo()")){
                    pst.setInt(i++, movContaContabilVO.getContaContabilCreditoJuro().getCodigo());
                }else{
                    pst.setNull(i++, Types.NULL);
                }
                if (UtilReflection.objetoMaiorQueZero(movContaContabilVO, "getContaContabilDebitoJuro().getCodigo()")){
                    pst.setInt(i++, movContaContabilVO.getContaContabilDebitoJuro().getCodigo());
                }else{
                    pst.setNull(i++, Types.NULL);
                }
            }
            pst.setInt(i++, movContaContabilVO.getCodigo());
            pst.execute();
        }
    }

    public void excluir(Integer codigoMovConta)throws Exception{
        StringBuilder sql = new StringBuilder();
        sql.append("delete from movContaContabil where movConta = ").append(codigoMovConta);
        Statement st = con.createStatement();
        st.execute(sql.toString());
    }

    public MovContaContabilVO consultar(Integer codigoMovConta)throws Exception{
        StringBuilder sql = new StringBuilder();
        sql.append(SQL_PADRAO.toString()).append(" \n where movConta = ").append(codigoMovConta);
        Statement st = con.createStatement();
        ResultSet rs = st.executeQuery(sql.toString());
        if (rs.next()){
            return montarDados(rs);
        }
        return new MovContaContabilVO();
    }

    public MovContaContabilVO montarDados(ResultSet rs)throws Exception{
        MovContaContabilVO obj = montarDadosBasico(rs);

        return obj;
    }

    public MovContaContabilVO montarDadosBasico(ResultSet rs)throws Exception{
        MovContaContabilVO obj = new MovContaContabilVO();
        obj.setCodigo(rs.getInt("codigo"));
        obj.setMovContaVO(new MovContaVO());
        obj.getMovContaVO().setCodigo(rs.getInt("movConta"));
        obj.setHistoricoContabil(rs.getInt("historicoContabil"));
        obj.setComplementoHistoricoContabil(rs.getString("complementoHistoricoContabil"));
        obj.setContaContabilCreditoValor(new ContaContabilVO());
        obj.getContaContabilCreditoValor().setCodigo(rs.getInt("contaContabilCreditoValor"));
        obj.setContaContabilDebitoValor(new ContaContabilVO());
        obj.getContaContabilDebitoValor().setCodigo(rs.getInt("contaContabilDebitoValor"));
        obj.setValorMulta(rs.getDouble("valorMulta"));
        obj.setContaContabilCreditoMulta(new ContaContabilVO());
        obj.getContaContabilCreditoMulta().setCodigo(rs.getInt("contaContabilCreditoMulta"));
        obj.setContaContabilDebitoMulta(new ContaContabilVO());
        obj.getContaContabilDebitoMulta().setCodigo(rs.getInt("contaContabilDebitoMulta"));
        obj.setValorJuro(rs.getDouble("valorJuro"));
        obj.setContaContabilCreditoJuro(new ContaContabilVO());
        obj.getContaContabilCreditoJuro().setCodigo(rs.getInt("contaContabilCreditoJuro"));
        obj.setContaContabilDebitoJuro(new ContaContabilVO());
        obj.getContaContabilDebitoJuro().setCodigo(rs.getInt("contaContabilDebitoJuro"));

        // valor
        obj.getContaContabilCreditoValor().setDescricao(rs.getString("descricaoContaCreditoValor"));
        obj.getContaContabilCreditoValor().setCodigoIntegracao(rs.getInt("codigoIntegracaoContaCreditoValor"));
        obj.getContaContabilDebitoValor().setDescricao(rs.getString("descricaoContaDebitoValor"));
        obj.getContaContabilDebitoValor().setCodigoIntegracao(rs.getInt("codigoIntegracaoContaDebitoValor"));
        // multa
        obj.getContaContabilCreditoMulta().setDescricao(rs.getString("descricaoContaCreditoMulta"));
        obj.getContaContabilCreditoMulta().setCodigoIntegracao(rs.getInt("codigoIntegracaoContaCreditoMulta"));
        obj.getContaContabilDebitoMulta().setDescricao(rs.getString("descricaoContaDebitoMulta"));
        obj.getContaContabilDebitoMulta().setCodigoIntegracao(rs.getInt("codigoIntegracaoContaDebitoMulta"));
        // juro
        obj.getContaContabilCreditoJuro().setDescricao(rs.getString("descricaoContaCreditoJuro"));
        obj.getContaContabilCreditoJuro().setCodigoIntegracao(rs.getInt("codigoIntegracaoContaCreditoJuro"));
        obj.getContaContabilDebitoJuro().setDescricao(rs.getString("descricaoContaDebitoJuro"));
        obj.getContaContabilDebitoJuro().setCodigoIntegracao(rs.getInt("codigoIntegracaoContaDebitoJuro"));

        return obj;
    }


}

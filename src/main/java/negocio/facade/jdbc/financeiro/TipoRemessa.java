package negocio.facade.jdbc.financeiro;

import negocio.comuns.financeiro.TipoRemessaVO;
import negocio.comuns.financeiro.TipoRetornoVO;
import negocio.comuns.financeiro.enumerador.ArquivoLayoutRemessaEnum;
import negocio.comuns.utilitarias.ConsistirException;
import negocio.comuns.utilitarias.Ordenacao;
import negocio.comuns.utilitarias.Uteis;
import negocio.facade.jdbc.arquitetura.SuperEntidade;
import negocio.interfaces.financeiro.TipoRemessaInterfaceFacade;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.sql.Statement;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

/**
 * Classe de persistência que encapsula todas as operações de manipulação dos dados da classe <code>TipoRemessaVO</code>.
 * Responsável por implementar operações como incluir, alterar, excluir e consultar pertinentes a classe <code>TipoRemessaVO</code>.
 * Encapsula toda a interação com o banco de dados.
 * @see TipoRemessaVO
 * @see SuperEntidade
 */
public class TipoRemessa extends SuperEntidade implements TipoRemessaInterfaceFacade {

    protected static String idEntidade;

    public TipoRemessa() throws Exception {
        super();
        setIdEntidade("TipoRemessa");
    }

    public TipoRemessa(Connection con) throws Exception {
        super(con);
        setIdEntidade("TipoRemessa");
    }

    /**
     * Operação responsável por retornar um novo objeto da classe <code>TipoRemessaVO</code>.
     */
    public TipoRemessaVO novo() throws Exception {
        incluir(getIdEntidade());
        return new TipoRemessaVO();
    }

    /**
     * Operação responsável por incluir no banco de dados um objeto da classe <code>TipoRemessaVO</code>.
     * Primeiramente valida os dados (<code>validarDados</code>) do objeto. Verifica a conexão com o banco de dados e a permissão do usuário
     * para realizar esta operacão na entidade.
     * Isto, através da operação <code>incluir</code> da superclasse.
     * @param obj  Objeto da classe <code>TipoRemessaVO</code> que será gravado no banco de dados.
     * @exception Exception Caso haja problemas de conexão, restrição de acesso ou validação de dados.
     */
    public void incluir(TipoRemessaVO obj) throws Exception {
            TipoRemessaVO.validarDados(obj);
            incluir(getIdEntidade());
            obj.realizarUpperCaseDados();
            String sql = "INSERT INTO TipoRemessa( descricao, tipoRetorno, arquivoLayoutRemessa, tipoRemessa ) VALUES ( ?, ?, ?, ? )";
        try (PreparedStatement sqlInserir = con.prepareStatement(sql)) {
            sqlInserir.setString(1, obj.getDescricao());
            if (obj.getTipoRetorno().getCodigo() != 0) {
                sqlInserir.setInt(2, obj.getTipoRetorno().getCodigo());
            } else {
                sqlInserir.setNull(2, 0);
            }
            sqlInserir.setString(3, obj.getArquivoLayoutRemessa().getId());
            sqlInserir.setString(4, obj.getTipoRemessa());
            sqlInserir.execute();
        }
        obj.setCodigo(obterValorChavePrimariaCodigo());
            obj.setNovoObj(false);
        }

    /**
     * Operação responsável por alterar no BD os dados de um objeto da classe <code>TipoRemessaVO</code>.
     * Sempre utiliza a chave primária da classe como atributo para localização do registro a ser alterado.
     * Primeiramente valida os dados (<code>validarDados</code>) do objeto. Verifica a conexão com o banco de dados e a permissão do usuário
     * para realizar esta operacão na entidade.
     * Isto, através da operação <code>alterar</code> da superclasse.
     * @param obj    Objeto da classe <code>TipoRemessaVO</code> que será alterada no banco de dados.
     * @exception Exception Caso haja problemas de conexão, restrição de acesso ou validação de dados.
     */
    public void alterar(TipoRemessaVO obj) throws Exception {
            TipoRemessaVO.validarDados(obj);
            alterar(getIdEntidade());
            obj.realizarUpperCaseDados();
            String sql = "UPDATE TipoRemessa set descricao = ?, tipoRetorno = ?, arquivoLayoutRemessa = ?, tipoRemessa = ? WHERE codigo = ?";
        try (PreparedStatement sqlAlterar = con.prepareStatement(sql)) {
            sqlAlterar.setString(1, obj.getDescricao());
            if (obj.getTipoRetorno().getCodigo() != 0) {
                sqlAlterar.setInt(2, obj.getTipoRetorno().getCodigo());
            } else {
                sqlAlterar.setNull(2, 0);
            }
            sqlAlterar.setString(3, obj.getArquivoLayoutRemessa().getId());
            sqlAlterar.setString(4, obj.getTipoRemessa());
            sqlAlterar.setInt(5, obj.getCodigo());
            sqlAlterar.execute();
        }
        obj.setNovoObj(false);
        }

    /**
     * Operação responsável por excluir no BD um objeto da classe <code>TipoRemessaVO</code>.
     * Sempre localiza o registro a ser excluído através da chave primária da entidade.
     * Primeiramente verifica a conexão com o banco de dados e a permissão do usuário
     * para realizar esta operacão na entidade.
     * Isto, através da operação <code>excluir</code> da superclasse.
     * @param obj    Objeto da classe <code>TipoRemessaVO</code> que será removido no banco de dados.
     * @exception Exception Caso haja problemas de conexão ou restrição de acesso.
     */
    public void excluir(TipoRemessaVO obj) throws Exception {
        try {
            con.setAutoCommit(false);
            excluir(getIdEntidade());
            String sql = "DELETE FROM TipoRemessa WHERE codigo = ?";
            try (PreparedStatement sqlExcluir = con.prepareStatement(sql)) {
                sqlExcluir.setInt(1, obj.getCodigo());
                sqlExcluir.execute();
            }
            con.commit();
        } catch (Exception e) {
            con.rollback();
            con.setAutoCommit(true);
            throw e;
        } finally {
            con.setAutoCommit(true);
        }
    }

    /**
     * Responsável por realizar uma consulta de <code>TipoRemessa</code> através do valor do atributo 
     * <code>String tipoRemessa</code>. Retorna os objetos, com início do valor do atributo idêntico ao parâmetro fornecido.
     * Faz uso da operação <code>montarDadosConsulta</code> que realiza o trabalho de prerarar o List resultante.
     * @param   controlarAcesso Indica se a aplicação deverá verificar se o usuário possui permissão para esta consulta ou não.
     * @return  List Contendo vários objetos da classe <code>TipoRemessaVO</code> resultantes da consulta.
     * @exception Exception Caso haja problemas de conexão ou restrição de acesso.
     */
    public List consultarPorTipoRemessa(String valorConsulta, boolean controlarAcesso, int nivelMontarDados) throws Exception {
        consultar(getIdEntidade(), controlarAcesso);
        String sqlStr = "SELECT * FROM TipoRemessa WHERE upper( tipoRemessa ) like('" + valorConsulta.toUpperCase() + "%') ORDER BY tipoRemessa";
        try (Statement stm = con.createStatement()) {
            try (ResultSet tabelaResultado = stm.executeQuery(sqlStr)) {
                return (montarDadosConsulta(tabelaResultado, nivelMontarDados, con));
            }
        }
    }

    /**
     * Responsável por realizar uma consulta de <code>TipoRemessa</code> através do valor do atributo 
     * <code>String arquivoLayoutRemessa</code>. Retorna os objetos, com início do valor do atributo idêntico ao parâmetro fornecido.
     * Faz uso da operação <code>montarDadosConsulta</code> que realiza o trabalho de prerarar o List resultante.
     * @param   controlarAcesso Indica se a aplicação deverá verificar se o usuário possui permissão para esta consulta ou não.
     * @return  List Contendo vários objetos da classe <code>TipoRemessaVO</code> resultantes da consulta.
     * @exception Exception Caso haja problemas de conexão ou restrição de acesso.
     */
    public List consultarPorArquivoLayoutRemessa(String valorConsulta, boolean controlarAcesso, int nivelMontarDados) throws Exception {
        consultar(getIdEntidade(), controlarAcesso);
        String sqlStr = "SELECT * FROM TipoRemessa WHERE upper( arquivoLayoutRemessa ) like('" + valorConsulta.toUpperCase() + "%') ORDER BY arquivoLayoutRemessa";
        try (Statement stm = con.createStatement()) {
            try (ResultSet tabelaResultado = stm.executeQuery(sqlStr)) {
                return (montarDadosConsulta(tabelaResultado, nivelMontarDados, con));
            }
        }
    }

    /**
     * Responsável por realizar uma consulta de <code>TipoRemessa</code> através do valor do atributo 
     * <code>descricao</code> da classe <code>TipoRetorno</code>
     * Faz uso da operação <code>montarDadosConsulta</code> que realiza o trabalho de prerarar o List resultante.
     * @return  List Contendo vários objetos da classe <code>TipoRemessaVO</code> resultantes da consulta.
     * @exception Exception Caso haja problemas de conexão ou restrição de acesso.
     */
    public List consultarPorDescricaoTipoRetorno(String valorConsulta, int nivelMontarDados) throws Exception {
        consultar(getIdEntidade(), true);
        String sqlStr = "SELECT TipoRemessa.* FROM TipoRemessa, TipoRetorno WHERE TipoRemessa.tipoRetorno = TipoRetorno.codigo and upper( TipoRetorno.descricao ) like('" + valorConsulta.toUpperCase() + "%') ORDER BY TipoRetorno.descricao";
        try (Statement stm = con.createStatement()) {
            try (ResultSet tabelaResultado = stm.executeQuery(sqlStr)) {
                return montarDadosConsulta(tabelaResultado, nivelMontarDados, con);
            }
        }
    }

    /**
     * Responsável por realizar uma consulta de <code>TipoRemessa</code> através do valor do atributo 
     * <code>String descricao</code>. Retorna os objetos, com início do valor do atributo idêntico ao parâmetro fornecido.
     * Faz uso da operação <code>montarDadosConsulta</code> que realiza o trabalho de prerarar o List resultante.
     * @param   controlarAcesso Indica se a aplicação deverá verificar se o usuário possui permissão para esta consulta ou não.
     * @return  List Contendo vários objetos da classe <code>TipoRemessaVO</code> resultantes da consulta.
     * @exception Exception Caso haja problemas de conexão ou restrição de acesso.
     */
    public List consultarPorDescricao(String valorConsulta, boolean controlarAcesso, int nivelMontarDados) throws Exception {
        consultar(getIdEntidade(), controlarAcesso);
        String sqlStr = "SELECT * FROM TipoRemessa WHERE upper( descricao ) like('" + valorConsulta.toUpperCase() + "%') ORDER BY descricao";
        try (Statement stm = con.createStatement()) {
            try (ResultSet tabelaResultado = stm.executeQuery(sqlStr)) {
                return (montarDadosConsulta(tabelaResultado, nivelMontarDados, con));
            }
        }
    }

    /**
     * Responsável por realizar uma consulta de <code>TipoRemessa</code> através do valor do atributo 
     * <code>Integer codigo</code>. Retorna os objetos com valores iguais ou superiores ao parâmetro fornecido.
     * Faz uso da operação <code>montarDadosConsulta</code> que realiza o trabalho de prerarar o List resultante.
     * @param   controlarAcesso Indica se a aplicação deverá verificar se o usuário possui permissão para esta consulta ou não.
     * @return  List Contendo vários objetos da classe <code>TipoRemessaVO</code> resultantes da consulta.
     * @exception Exception Caso haja problemas de conexão ou restrição de acesso.
     */
    public List consultarPorCodigo(Integer valorConsulta, boolean controlarAcesso, int nivelMontarDados) throws Exception {
        consultar(getIdEntidade(), controlarAcesso);
        String sqlStr = "SELECT * FROM TipoRemessa WHERE codigo >= " + valorConsulta + " ORDER BY codigo";
        try (Statement stm = con.createStatement()) {
            try (ResultSet tabelaResultado = stm.executeQuery(sqlStr)) {
                return (montarDadosConsulta(tabelaResultado, nivelMontarDados, con));
            }
        }
    }

    /**
     * Responsável por montar os dados de vários objetos, resultantes de uma consulta ao banco de dados (<code>ResultSet</code>).
     * Faz uso da operação <code>montarDados</code> que realiza o trabalho para um objeto por vez.
     * @return  List Contendo vários objetos da classe <code>TipoRemessaVO</code> resultantes da consulta.
     */
    public static List montarDadosConsulta(ResultSet tabelaResultado, int nivelMontarDados, Connection con) throws Exception {
        List<TipoRemessaVO> vetResultado = new ArrayList<TipoRemessaVO>();
        while (tabelaResultado.next()) {
            TipoRemessaVO obj = montarDados(tabelaResultado, nivelMontarDados, con);
            vetResultado.add(obj);
        }
        return vetResultado;
    }

    /**
     * Responsável por montar os dados resultantes de uma consulta ao banco de dados (<code>ResultSet</code>)
     * em um objeto da classe <code>TipoRemessaVO</code>.
     * @return  O objeto da classe <code>TipoRemessaVO</code> com os dados devidamente montados.
     */
    public static TipoRemessaVO montarDados(ResultSet dadosSQL, int nivelMontarDados, Connection con) throws Exception {
        TipoRemessaVO obj = new TipoRemessaVO();
        obj.setCodigo(dadosSQL.getInt("codigo"));
        obj.setDescricao(dadosSQL.getString("descricao"));
        obj.getTipoRetorno().setCodigo(dadosSQL.getInt("tipoRetorno"));
        obj.setArquivoLayoutRemessa(ArquivoLayoutRemessaEnum.getFromId(dadosSQL.getString("arquivoLayoutRemessa")));
        obj.setTipoRemessa(dadosSQL.getString("tipoRemessa"));
        obj.setNovoObj(false);
        if (nivelMontarDados == Uteis.NIVELMONTARDADOS_DADOSBASICOS) {
            return obj;
        }

        montarDadosTipoRetorno(obj, nivelMontarDados, con);
        return obj;
    }

    /**
     * Operação responsável por montar os dados de um objeto da classe <code>TipoRetornoVO</code> relacionado ao objeto <code>TipoRemessaVO</code>.
     * Faz uso da chave primária da classe <code>TipoRetornoVO</code> para realizar a consulta.
     * @param obj  Objeto no qual será montado os dados consultados.
     */
    public static void montarDadosTipoRetorno(TipoRemessaVO obj, int nivelMontarDados, Connection con) throws Exception {
        if (obj.getTipoRetorno().getCodigo() == 0) {
            obj.setTipoRetorno(new TipoRetornoVO());
            return;
        }
        TipoRetorno tipoRetornoDAO = new TipoRetorno(con);
        obj.setTipoRetorno(tipoRetornoDAO.consultarPorChavePrimaria(obj.getTipoRetorno().getCodigo(), nivelMontarDados));
        tipoRetornoDAO = null;
    }

    /**
     * Operação responsável por localizar um objeto da classe <code>TipoRemessaVO</code>
     * através de sua chave primária. 
     * @exception Exception Caso haja problemas de conexão ou localização do objeto procurado.
     */
    public TipoRemessaVO consultarPorChavePrimaria(Integer codigoPrm, int nivelMontarDados) throws Exception {
        consultar(getIdEntidade(), false);

        TipoRemessaVO eCache = (TipoRemessaVO) obterFromCache(codigoPrm);
        if (eCache != null){
            return eCache;
        }

        String sql = "SELECT * FROM TipoRemessa WHERE codigo = ?";
        try (PreparedStatement sqlConsultar = con.prepareStatement(sql)) {
            sqlConsultar.setInt(1, codigoPrm);
            try (ResultSet tabelaResultado = sqlConsultar.executeQuery()) {
                if (!tabelaResultado.next()) {
                    throw new ConsistirException("Dados Não Encontrados ( TipoRemessa ).");
                }
                eCache = (montarDados(tabelaResultado, nivelMontarDados, con));
            }
        }
        putToCache(eCache);
        return eCache;
    }

    public TipoRemessaVO criarOuConsultarSeExistePorDescricao(TipoRemessaVO obj) throws Exception {
        String sql = "SELECT * FROM TipoRemessa WHERE descricao = ?";
        try (PreparedStatement sqlConsultar = con.prepareStatement(sql)) {
            sqlConsultar.setString(1, obj.getDescricao());
            try (ResultSet tabelaResultado = sqlConsultar.executeQuery()) {
                if (!tabelaResultado.next()) {
                    incluir(obj);
                    return obj;
                } else {
                    return (TipoRemessa.montarDados(tabelaResultado, Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA, con));
                }
            }
        }
    }

    public String consultarJSON() throws Exception {
        StringBuilder json;
        boolean dados;
        try (ResultSet rs = getPS().executeQuery()) {
            json = new StringBuilder();
            json.append("{\"aaData\":[");
            dados = false;
            while (rs.next()) {
                dados = true;
                ArquivoLayoutRemessaEnum layout = ArquivoLayoutRemessaEnum.getFromId(rs.getString("arquivoLayoutRemessa"));

                json.append("[\"").append(rs.getString("codigo")).append("\",");
                json.append("\"").append(rs.getString("descricao")).append("\",");
                json.append("\"").append(layout.getDescricao()).append("\",");
                json.append("\"").append(rs.getString("tiporemessa")).append("\"],");
            }
        }
        if (dados) {
            json.deleteCharAt(json.toString().length() - 1);
        }
        json.append("]}");
        return json.toString();
    }

    private PreparedStatement getPS() throws SQLException {
        String sql = "SELECT codigo, descricao, arquivolayoutremessa, tiporemessa FROM tiporemessa ORDER BY descricao";
        return con.prepareStatement(sql);
    }

    public List consultarParaImpressao(String filtro, String ordem, String campoOrdenacao, int i) throws SQLException {

        List lista;
        try (ResultSet rs = getPS().executeQuery()) {
            lista = new ArrayList();
            while (rs.next()) {
                TipoRemessaVO tRemessa = new TipoRemessaVO();
                String geral = rs.getString("codigo") + rs.getString("descricao") + rs.getString("arquivolayoutremessa") + rs.getString("tiporemessa");
                if (geral.toLowerCase().contains(filtro.toLowerCase())) {
                    tRemessa.setCodigo(rs.getInt("codigo"));
                    tRemessa.setDescricao(rs.getString("descricao"));
                    tRemessa.setArquivoLayoutRemessa(ArquivoLayoutRemessaEnum.getFromId(rs.getString("arquivoLayoutRemessa")));
                    tRemessa.setTipoRemessa(rs.getString("tiporemessa"));
                    lista.add(tRemessa);
                }
            }
        }
        if (campoOrdenacao.equals("Código")) {
            Ordenacao.ordenarLista(lista, "codigo");
        } else if (campoOrdenacao.equals("Descrição")) {
            Ordenacao.ordenarLista(lista, "descricao");
        } else if (campoOrdenacao.equals("Arquivo de Layout")) {
            Ordenacao.ordenarLista(lista, "arquivoLayoutRemessa");
        } else if (campoOrdenacao.equals("Tipo")) {
            Ordenacao.ordenarLista(lista, "tipoRemessa");
        }
        if (ordem.contains("desc")) {
            Collections.reverse(lista);
        }
        return lista;

    }
}

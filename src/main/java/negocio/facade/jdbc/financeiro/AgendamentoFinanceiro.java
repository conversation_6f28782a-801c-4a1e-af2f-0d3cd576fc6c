
package negocio.facade.jdbc.financeiro;

import negocio.comuns.financeiro.AgendamentoFinanceiroVO;
import negocio.comuns.financeiro.enumerador.FrequenciaAgendamento;
import negocio.comuns.financeiro.enumerador.LayoutDescricao;
import negocio.comuns.utilitarias.Calendario;
import negocio.comuns.utilitarias.Uteis;
import negocio.facade.jdbc.arquitetura.SuperEntidade;
import negocio.facade.jdbc.crm.Feriado;
import negocio.interfaces.financeiro.AgendamentoFinanceiroInterfaceFacade;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.Statement;
import java.sql.Types;
import java.util.*;
import negocio.comuns.basico.EmpresaVO;
import negocio.facade.jdbc.basico.Empresa;

/**
 *
 * <AUTHOR>
 */
public class AgendamentoFinanceiro extends SuperEntidade implements AgendamentoFinanceiroInterfaceFacade {
    public static final String SQL_INSERIR = "INSERT INTO agendamentofinanceiro(descricao, frequencia, layoutdescricao, proximovencimento, vencimentoultimaparcela, "
                                           + "parcelaini, parcelafim, qtdeParcelasGerar, qtdeDiasNovaGeracao, diavencimento, datalancamento, usaParcelasFixas, gerarapenasdiasuteis) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)";
    public static final String SQL_ALTERAR = "UPDATE agendamentofinanceiro SET descricao=?, frequencia=?, layoutdescricao=?, proximovencimento=?, vencimentoultimaparcela=?, "
                                           + "parcelaini=?, parcelafim=?, qtdeParcelasGerar=?, qtdeDiasNovaGeracao=?, diavencimento=?, datalancamento=?, usaParcelasFixas =?, gerarapenasdiasuteis=? WHERE codigo=?";
    public static final String SQL_EXCLUIR = "DELETE FROM agendamentofinanceiro WHERE codigo = ?";
    public static final String SQL_CONSULTAR_CHAVE_PRIMARIA = "SELECT * FROM agendamentofinanceiro WHERE codigo = ?";

    public AgendamentoFinanceiro() throws Exception {
        
    }

    public AgendamentoFinanceiro(Connection con) throws Exception {
        super(con);
    }

    @Override
    public void alterar(AgendamentoFinanceiroVO obj) throws Exception {
        try {
            con.setAutoCommit(false);
            obj.validarDados();
            alterarSemCommit(obj);
            con.commit();
        } catch (Exception e) {
            con.rollback();
            throw e;
        } finally {
            con.setAutoCommit(true);
        }
    }

    @Override
    public void alterarSemCommit(AgendamentoFinanceiroVO obj) throws Exception {
        PreparedStatement sqlAlterar = con.prepareStatement(SQL_ALTERAR);
        sqlAlterar.setString(1, obj.getDescricao());
        sqlAlterar.setInt(2, obj.getFrequencia().getCodigo());
        sqlAlterar.setInt(3, obj.getLayoutDescricao().getCodigo());
        sqlAlterar.setDate(4, Uteis.getDataJDBC(obj.getProximoVencimento()));
        sqlAlterar.setDate(5, Uteis.getDataJDBC(obj.getVencimentoUltimaParcela()));
        if (obj.getParcelaInicial() <= 0){
            sqlAlterar.setNull(6, Types.NULL);
        }else{
            sqlAlterar.setInt(6, obj.getParcelaInicial());
        }
        if (obj.getParcelaFinal() <= 0){
            sqlAlterar.setNull(7, Types.NULL);
        }else{
            sqlAlterar.setInt(7, obj.getParcelaFinal());
        }
        sqlAlterar.setInt(8, obj.getQtdeParcelasGerar());
        sqlAlterar.setInt(9, obj.getQtdeDiasNovaGeracao());
        sqlAlterar.setInt(10, obj.getDiaVencimento());
        
        sqlAlterar.setDate(11, Uteis.getDataJDBC(obj.getDataLancamento()));
        sqlAlterar.setBoolean(12, obj.isUsaParcelasFixas());

        sqlAlterar.setBoolean(13, obj.getGerarApenasDiasUteis());

        sqlAlterar.setInt(14, obj.getCodigo());
        sqlAlterar.execute();
    }

    @Override
    public void excluir(int codigo) throws Exception {
        try {
            con.setAutoCommit(false);
            excluirSemCommit(codigo);
            con.commit();
        } catch (Exception e) {
            con.rollback();
            throw e;
        } finally {
            con.setAutoCommit(true);
        }
    }

    @Override
    public void excluirSemCommit(int codigo) throws Exception {
        PreparedStatement sqlExcluir = con.prepareStatement(SQL_EXCLUIR);
        sqlExcluir.setInt(1, codigo);
        sqlExcluir.execute();
    }

    @Override
    public AgendamentoFinanceiroVO consultarPorChavePrimaria(int codigo, int nivelMontarDados) throws Exception {
        PreparedStatement sqlConsultar = con.prepareStatement(SQL_CONSULTAR_CHAVE_PRIMARIA);
        sqlConsultar.setInt(1, codigo);
        ResultSet tabelaResultado = sqlConsultar.executeQuery();
        if(tabelaResultado.next())
            return montarDados(tabelaResultado, nivelMontarDados, this.con);
        else
            return new AgendamentoFinanceiroVO();
    }

    public AgendamentoFinanceiroVO consultarPorMovConta(Integer codigoMovConta, int nivelMontarDados) throws Exception{
        StringBuilder sql = new StringBuilder();
        sql.append("select ag.* \n");
        sql.append("from agendamentofinanceiro ag \n");
        sql.append("inner join movConta mov on mov.agendamentofinanceiro = ag.codigo \n");
        sql.append("where mov.codigo = ").append(codigoMovConta);
        Statement st = con.createStatement();
        ResultSet rs = st.executeQuery(sql.toString());
        if (rs.next()){
            return montarDados(rs,nivelMontarDados,con);
        }
        return null;
    }

    public List<AgendamentoFinanceiroVO> montarDadosConsulta(ResultSet tabelaResultado, int nivelMontarDados, Connection con) throws Exception {
        List<AgendamentoFinanceiroVO> resultado = new ArrayList<AgendamentoFinanceiroVO>();
        while (tabelaResultado.next()) {
            resultado.add(montarDados(tabelaResultado, nivelMontarDados, con));
        }
        return resultado;
    }

    public AgendamentoFinanceiroVO montarDados(ResultSet resultado, int nivelMontarDados, Connection con) throws Exception {
        AgendamentoFinanceiroVO obj = montarDadosBasicos(resultado);
        if (nivelMontarDados == Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA)
            return obj;
        return obj;
    }

    public AgendamentoFinanceiroVO montarDadosBasicos(ResultSet resultado) throws Exception {
        AgendamentoFinanceiroVO obj = new AgendamentoFinanceiroVO();
        obj.setNovoObj(false);
        obj.setCodigo(resultado.getInt("codigo"));
        obj.setDescricao(resultado.getString("descricao"));
        obj.setFrequencia(FrequenciaAgendamento.getFrequencia(resultado.getInt("frequencia")));
        obj.setLayoutDescricao(LayoutDescricao.getLayoutDescricao(resultado.getInt("layoutdescricao")));
        obj.setProximoVencimento(resultado.getDate("proximovencimento"));
        obj.setVencimentoUltimaParcela(resultado.getDate("vencimentoultimaparcela"));
        obj.setParcelaInicial(resultado.getInt("parcelaini"));
        obj.setParcelaFinal(resultado.getInt("parcelafim"));
        obj.setQtdeParcelasGerar(resultado.getInt("qtdeParcelasGerar"));
        obj.setQtdeDiasNovaGeracao(resultado.getInt("qtdeDiasNovaGeracao"));
        obj.setDiaVencimento(resultado.getInt("diavencimento"));
        
        obj.setUsaParcelasFixas(resultado.getBoolean("usaParcelasFixas"));
        obj.setDataLancamento(resultado.getDate("datalancamento"));
        obj.setGerarApenasDiasUteis(resultado.getBoolean("gerarapenasdiasuteis"));

        obj.setNovoObj(false);
        return obj;
    }

    public Map<Integer, Date> atualizarDatasVencimentoFuturo(AgendamentoFinanceiroVO agendamento, EmpresaVO empresa) throws Exception {
        Map<Integer, Date> datas = new HashMap<Integer, Date>();
        if(empresa == null){
            ResultSet rs = criarConsulta("select empresa from movconta where  agendamentofinanceiro = " + agendamento.getCodigo() + " limit 1", con);
            if(rs.next()){
                Empresa empresaDao = new Empresa(con);
                empresa = empresaDao.consultarPorChavePrimaria(rs.getInt("empresa"), Uteis.NIVELMONTARDADOS_GESTAOREMESSA);
            } else {
                return datas; // não existe movconta
            }
        }
        String sql = "select codigo, datavencimento from movconta where datavencimento > '" + Uteis.getDataAplicandoFormatacao(Calendario.hoje(), "yyyy-MM-dd")+
                "' and agendamentofinanceiro = " + agendamento.getCodigo() + " order by datavencimento ";
        ResultSet rs = criarConsulta(sql, con);
        Feriado feriadoDAo = new Feriado(this.con);
        
        while (rs.next()) {
            int codigo = rs.getInt("codigo");
            Date vencimento = rs.getDate("datavencimento");
            int diaVencimento = agendamento.getDiaVencimento();

            Calendar novaData = Calendario.getInstance();
            novaData.setTime(vencimento);
            novaData.set(Calendar.DAY_OF_MONTH, 1);
            if (diaVencimento > (novaData.getActualMaximum(Calendar.DAY_OF_MONTH))) {
                diaVencimento = novaData.getActualMaximum(Calendar.DAY_OF_MONTH);
            }
            novaData.set(Calendar.DAY_OF_MONTH, diaVencimento);
            Date novoVencimento = novaData.getTime();
            if(agendamento.getGerarApenasDiasUteis()){
                novoVencimento = feriadoDAo.obterProximoDiaUtil(novoVencimento, empresa);
            }


            PreparedStatement stm = con.prepareStatement("update movconta set datavencimento = ? where codigo = ?");
            stm.setDate(1, Uteis.getDataJDBC(novoVencimento));
            stm.setInt(2, codigo);

            stm.execute();


            datas.put(codigo, new Date(novoVencimento.getTime()));
        }
        feriadoDAo = null;

        return datas;
    }


}

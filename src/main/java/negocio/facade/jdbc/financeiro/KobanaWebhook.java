package negocio.facade.jdbc.financeiro;

import negocio.comuns.financeiro.KobanaWebhookVO;
import negocio.comuns.financeiro.MovContaVO;
import negocio.comuns.utilitarias.Calendario;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisValidacao;
import negocio.facade.jdbc.arquitetura.SuperEntidade;
import negocio.interfaces.financeiro.PixWebhookInterfaceFacade;

import java.sql.*;


/**
 * Created by <PERSON> on 08/11/2024.
 */

public class KobanaWebhook extends SuperEntidade implements PixWebhookInterfaceFacade {

    public KobanaWebhook() throws Exception {
        super();
    }

    public KobanaWebhook(Connection connection) throws Exception {
        super(connection);
    }

    public KobanaWebhookVO incluir(KobanaWebhookVO obj) throws Exception {
        String sql = "INSERT INTO kobanaWebhook(dataregistro, dados, event_code, processado, dataProcessamento, uidLoteKobanaItem," +
                " codLoteKobanaItem, uidLoteKobana, codLoteKobana, movConta) " +
                " VALUES (?,?,?,?,?,?,?,?,?,?) RETURNING codigo";
        try (PreparedStatement pst = con.prepareStatement(sql)) {
            int i = 0;
            pst.setTimestamp(++i, Uteis.getDataJDBCTimestamp(Calendario.hoje()));
            pst.setString(++i, obj.getDados());
            pst.setString(++i, obj.getEvent_code());
            pst.setBoolean(++i, obj.isProcessado());
            pst.setNull(++i, Types.NULL);
            if (UteisValidacao.emptyString(obj.getUidLoteKobanaItem())) {
                pst.setNull(++i, Types.NULL);
            } else {
                pst.setString(++i, obj.getUidLoteKobanaItem());
            }
            if (UteisValidacao.emptyNumber(obj.getCodLoteKobanaItem())) {
                pst.setNull(++i, Types.NULL);
            } else {
                pst.setInt(++i, obj.getCodLoteKobanaItem());
            }
            if (UteisValidacao.emptyString(obj.getUidLoteKobana())) {
                pst.setNull(++i, Types.NULL);
            } else {
                pst.setString(++i, obj.getUidLoteKobana());
            }
            if (UteisValidacao.emptyNumber(obj.getCodLoteKobana())) {
                pst.setNull(++i, Types.NULL);
            } else {
                pst.setInt(++i, obj.getCodLoteKobana());
            }
            if (obj.getMovContaVO() != null && UteisValidacao.emptyNumber(obj.getMovContaVO().getCodigo())) {
                pst.setNull(++i, Types.NULL);
            } else {
                pst.setInt(++i, obj.getMovContaVO().getCodigo());
            }

            ResultSet rsCodigo = pst.executeQuery();
            rsCodigo.next();
            obj.setCodigo(rsCodigo.getInt("codigo"));
            obj.setNovoObj(false);
            return obj;
        }
    }

    public void alterar(KobanaWebhookVO obj) throws Exception {
        String sql = "UPDATE kobanaWebhook \n"
                + " set processado = ?, dataProcessamento = ? \n "
                + " WHERE codigo = ? ";

        try (PreparedStatement sqlAlterar = con.prepareStatement(sql)) {
            sqlAlterar.setBoolean(1, obj.isProcessado());
            sqlAlterar.setTimestamp(2, Uteis.getDataJDBCTimestamp(obj.getDataProcessamento()));
            sqlAlterar.setInt(3, obj.getCodigo());
            sqlAlterar.execute();
        }
    }

    public KobanaWebhookVO consultarPorMovConta(Integer movConta) throws Exception {
        //procurar se já tem o id processado com sucesso
        String sql = "select * from kobanaWebhook where movConta = " + movConta + " order by dataRegistro desc";
        try (ResultSet rs = criarConsulta(sql, con)) {
            while (rs.next()) {
                return montarDados(rs);
            }
        }
        return null;
    }

    public KobanaWebhookVO montarDados(ResultSet rs) throws SQLException {
        KobanaWebhookVO obj = new KobanaWebhookVO();
        obj.setCodigo(rs.getInt("codigo"));
        obj.setDataRegistro(rs.getTimestamp("dataRegistro"));
        obj.setDados(rs.getString("dados"));
        obj.setEvent_code(rs.getString("event_code"));
        obj.setProcessado(rs.getBoolean("processado"));
        obj.setUidLoteKobanaItem(rs.getString("uidLoteKobanaItem"));
        obj.setCodLoteKobanaItem(rs.getInt("codLoteKobanaItem"));
        obj.setUidLoteKobanaItem(rs.getString("uidLoteKobana"));
        obj.setCodLoteKobanaItem(rs.getInt("codLoteKobana"));

        MovContaVO movContaVO = new MovContaVO();
        movContaVO.setCodigo(rs.getInt("movConta"));
        obj.setMovContaVO(movContaVO);

        return obj;
    }
}


package negocio.facade.jdbc.financeiro;

import br.com.pactosolucoes.enumeradores.TipoContaPagarLoteEnum;
import negocio.comuns.financeiro.LoteKobanaVO;
import negocio.comuns.financeiro.enumerador.RegistrationStatusKobanaEnum;
import negocio.comuns.financeiro.enumerador.StatusLoteKobanaEnum;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisValidacao;
import negocio.facade.jdbc.arquitetura.SuperEntidade;
import negocio.interfaces.financeiro.LoteKobanaInterfaceFacade;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.Types;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

public class LoteKobana extends SuperEntidade implements LoteKobanaInterfaceFacade {

    public LoteKobana() throws Exception {
        super();
    }

    public LoteKobana(Connection con) throws Exception {
        super(con);
    }


    public LoteKobanaVO incluir(LoteKobanaVO lote) throws Exception {
        try {
            String sql = "INSERT INTO LoteKobana(uid, status, registration_status, financial_account_uid, created_at, updated_at, empresa," +
                    " paramsEnvio, paramsRetorno, valor, tipoContaPagarLote) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING codigo";
            try (PreparedStatement sqlInserir = con.prepareStatement(sql)) {
                sqlInserir.setString(1, lote.getUid());
                if (lote.getStatus() == null) {
                    sqlInserir.setNull(2, Types.NULL);
                } else {
                    sqlInserir.setInt(2, lote.getStatus().getCodigo());
                }
                if (lote.getRegistration_status() == null) {
                    sqlInserir.setNull(3, Types.NULL);
                } else {
                    sqlInserir.setInt(3, lote.getRegistration_status().getCodigo());
                }
                sqlInserir.setString(4, lote.getFinancial_account_uid());
                sqlInserir.setTimestamp(5, Uteis.getDataJDBCTimestamp(lote.getCreated_at()));
                sqlInserir.setTimestamp(6, Uteis.getDataJDBCTimestamp(lote.getUpdated_at()));
                sqlInserir.setInt(7, lote.getEmpresa());
                sqlInserir.setString(8, lote.getParamsEnvio());
                sqlInserir.setString(9, lote.getParamsRetorno());
                sqlInserir.setDouble(10, lote.getValor());
                sqlInserir.setInt(11, lote.getTipoContaPagarLoteEnum().getCodigo());

                ResultSet rsNovo = sqlInserir.executeQuery();
                rsNovo.next();
                lote.setCodigo(rsNovo.getInt(1));
                return lote;
            }
        } catch (Exception e) {
            throw e;
        }
    }

    public void atualizarByUid(final LoteKobanaVO obj) throws Exception {
        String sql = "UPDATE loteKobana "
                + "set status = ?, registration_status = ?, "
                + "created_at = ?, updated_at = ?, paramsenvio = ?, "
                + "paramsretorno = ? WHERE uid = ? ";

        try (PreparedStatement sqlAlterar = con.prepareStatement(sql)) {
            if (obj.getStatus() == null) {
                sqlAlterar.setNull(1, Types.NULL);
            } else {
                sqlAlterar.setInt(1, obj.getStatus().getCodigo());
            }
            if (obj.getRegistration_status() == null) {
                sqlAlterar.setNull(2, Types.NULL);
            } else {
                sqlAlterar.setInt(2, obj.getRegistration_status().getCodigo());
            }
            sqlAlterar.setTimestamp(3, Uteis.getDataJDBCTimestamp(obj.getCreated_at()));
            sqlAlterar.setTimestamp(4, Uteis.getDataJDBCTimestamp(obj.getUpdated_at()));
            sqlAlterar.setString(5, obj.getParamsEnvio());
            sqlAlterar.setString(6, obj.getParamsRetorno());
            sqlAlterar.setString(7, obj.getUid());
            sqlAlterar.execute();
        }
    }

    public void atualizarByCodigo(final LoteKobanaVO obj) throws Exception {
        String sql = "UPDATE loteKobana "
                + "set uid = ?, status = ?, registration_status = ?, "
                + "created_at = ?, updated_at = ?, paramsenvio = ?, "
                + "paramsretorno = ? WHERE codigo = ? ";

        try (PreparedStatement sqlAlterar = con.prepareStatement(sql)) {
            sqlAlterar.setString(1, obj.getUid());
            if (obj.getStatus() == null) {
                sqlAlterar.setNull(2, Types.NULL);
            } else {
                sqlAlterar.setInt(2, obj.getStatus().getCodigo());
            }
            if (obj.getRegistration_status() == null) {
                sqlAlterar.setNull(3, Types.NULL);
            } else {
                sqlAlterar.setInt(3, obj.getRegistration_status().getCodigo());
            }
            sqlAlterar.setTimestamp(4, Uteis.getDataJDBCTimestamp(obj.getCreated_at()));
            sqlAlterar.setTimestamp(5, Uteis.getDataJDBCTimestamp(obj.getUpdated_at()));
            sqlAlterar.setString(6, obj.getParamsEnvio());
            sqlAlterar.setString(7, obj.getParamsRetorno());
            sqlAlterar.setInt(8, obj.getCodigo());
            sqlAlterar.execute();
        }
    }

    public void alterar(LoteKobanaVO obj) throws Exception {
        String sql = "UPDATE loteKobana "
                + "set registration_status = ?, "
                + "updated_at = ? WHERE codigo = ? ";

        try (PreparedStatement sqlAlterar = con.prepareStatement(sql)) {
            if (obj.getRegistration_status() == null) {
                sqlAlterar.setNull(1, Types.NULL);
            } else {
                sqlAlterar.setInt(1, obj.getRegistration_status().getCodigo());
            }
            sqlAlterar.setTimestamp(2, Uteis.getDataJDBCTimestamp(obj.getUpdated_at()));
            sqlAlterar.setInt(3, obj.getCodigo());
            sqlAlterar.execute();
        }
    }

    public void alterarStatus(StatusLoteKobanaEnum statusLoteKobanaEnum, int codLote) throws Exception {
        String sql = "UPDATE loteKobana "
                + "set status = ? "
                + "WHERE codigo = ? ";

        try (PreparedStatement sqlAlterar = con.prepareStatement(sql)) {
            sqlAlterar.setInt(1, statusLoteKobanaEnum.getCodigo());
            sqlAlterar.setInt(2, codLote);
            sqlAlterar.execute();
        }
    }

    public List<LoteKobanaVO> consultar(Date dataInicio, Date dataFim, Integer empresa, StatusLoteKobanaEnum statusEnum,
                                        RegistrationStatusKobanaEnum registrationStatusEnum, TipoContaPagarLoteEnum tipoContaPagarLoteEnum,
                                        Integer limit, Integer offset, int nivelMontarDados) throws Exception {

        String sql = montarSQLConsultaLotes(dataInicio, dataFim, empresa, statusEnum, registrationStatusEnum, tipoContaPagarLoteEnum, limit, offset, false);

        List<LoteKobanaVO> listaLotes = new ArrayList<>();
        try (PreparedStatement stm = con.prepareStatement(sql.toString())) {
            try (ResultSet rs = stm.executeQuery()) {
                while (rs.next()) {
                    listaLotes.add(montarDadosConsulta(rs, nivelMontarDados));
                }
            }
        }
        return listaLotes;
    }

    @Override
    public Integer obterCountConsultaLotes(Date dataInicio, Date dataFim, Integer empresa, StatusLoteKobanaEnum statusEnum,
                                           RegistrationStatusKobanaEnum registrationStatusEnum, TipoContaPagarLoteEnum tipoContaPagarLoteEnum,
                                           Integer limit, Integer offset) throws Exception {
        String sql = montarSQLConsultaLotes(dataInicio, dataFim, empresa, statusEnum, registrationStatusEnum, tipoContaPagarLoteEnum, limit, offset, true);
        try (PreparedStatement stm = con.prepareStatement(sql)) {
            try (ResultSet rs = stm.executeQuery()) {
                if (rs.next()) {
                    return rs.getInt("total");
                }
            }
        }
        return 0;
    }

    public String montarSQLConsultaLotes(Date dataInicio, Date dataFim, Integer empresa, StatusLoteKobanaEnum statusEnum,
                                         RegistrationStatusKobanaEnum registrationStatusEnum, TipoContaPagarLoteEnum tipoContaPagarLoteEnum,
                                         Integer limit, Integer offset, boolean count) throws Exception {
        StringBuilder sql = new StringBuilder();
        if (dataInicio == null) {
            throw new Exception("Data Inicio é obrigatória");
        }
        if (dataFim == null) {
            throw new Exception("Data Inicio é obrigatória");
        }

        if (count) {
            sql.append("SELECT count(*) as total\n");
        } else {
            sql.append("SELECT *\n");
        }
        sql.append("FROM loteKobana lote \n");

        sql.append(" where ").append("created_at::date between '").append(Uteis.getDataFormatoBD(dataInicio)).append("' and '").append(Uteis.getDataFormatoBD(dataFim)).append("' \n");

        if (!UteisValidacao.emptyNumber(empresa)) {
            sql.append(" and empresa = ").append(empresa).append("\n");
        }

        if (statusEnum != null) {
            sql.append(" and status = ").append(statusEnum.getCodigo()).append("\n");
        }

        if (statusEnum != null) {
            sql.append(" and registration_status = ").append(registrationStatusEnum.getCodigo()).append("\n");
        }

        if (tipoContaPagarLoteEnum != null) {
            sql.append(" and tipoContaPagarLote = ").append(tipoContaPagarLoteEnum.getCodigo()).append("\n");
        }
        if (!count) {
            sql.append(" order by lote.created_at desc \n");
        }
        if (!UteisValidacao.emptyNumber(limit)) {
            sql.append(" limit ").append(limit).append(" offset ").append(offset);
        }
        return sql.toString();
    }

    public LoteKobanaVO consultarPorCodigo(int cod, int nivelMontarDados) throws Exception {
        StringBuilder sql = new StringBuilder();
        sql.append("SELECT *\n");
        sql.append("FROM loteKobana lote \n");
        sql.append(" where lote.codigo = ").append(cod).append("\n");

        try (PreparedStatement stm = con.prepareStatement(sql.toString())) {
            try (ResultSet rs = stm.executeQuery()) {
                if (rs.next()) {
                    return montarDadosConsulta(rs, nivelMontarDados);
                }
            }
        }
        return null;
    }

    public LoteKobanaVO consultarPorUId(String uid, int nivelMontarDados) throws Exception {
        StringBuilder sql = new StringBuilder();
        sql.append("SELECT *\n");
        sql.append("FROM loteKobana lote \n");
        sql.append(" where lote.uid = '").append(uid).append("'\n");

        try (PreparedStatement stm = con.prepareStatement(sql.toString())) {
            try (ResultSet rs = stm.executeQuery()) {
                if (rs.next()) {
                    return montarDadosConsulta(rs, nivelMontarDados);
                }
            }
        }
        return null;
    }

    private LoteKobanaVO montarDadosConsulta(ResultSet rs, int nivelMontarDados) throws Exception {
        LoteKobanaVO obj = new LoteKobanaVO();
        obj.setCodigo(rs.getInt("codigo"));
        obj.setUid(rs.getString("uid"));
        obj.setStatus(StatusLoteKobanaEnum.obterPorCodigo(rs.getInt("status")));
        obj.setRegistration_status(RegistrationStatusKobanaEnum.obterPorCodigo(rs.getInt("registration_status")));
        obj.setFinancial_account_uid(rs.getString("financial_account_uid"));
        obj.setCreated_at(rs.getTimestamp("created_at"));
        obj.setUpdated_at(rs.getTimestamp("updated_at"));
        obj.setEmpresa(rs.getInt("empresa"));
        obj.setParamsEnvio(rs.getString("paramsEnvio"));
        obj.setParamsRetorno(rs.getString("paramsRetorno"));
        obj.setValor(rs.getDouble("valor"));
        obj.setTipoContaPagarLoteEnum(TipoContaPagarLoteEnum.obterPorCodigo(rs.getInt("tipoContaPagarLote")));

        if (nivelMontarDados == Uteis.NIVELMONTARDADOS_TODOS) {
            LoteKobanaItem loteKobanaItemDAO = new LoteKobanaItem(con);
            try {
                obj.setListaLoteKobanaItemVO(loteKobanaItemDAO.consultarByLote(obj.getCodigo(), Uteis.NIVELMONTARDADOS_TODOS));
            } catch (Exception ex) {
                throw ex;
            } finally {
                loteKobanaItemDAO = null;
            }
        }

        return obj;
    }
}

package negocio.facade.jdbc.financeiro;

import br.com.pactosolucoes.agendatotal.json.AgendaTotalJSON;
import br.com.pactosolucoes.bi.dto.DadosAulaExperimentalDTO;
import br.com.pactosolucoes.bi.dto.FiltroDTO;
import br.com.pactosolucoes.bi.service.impl.BiMSServiceImpl;
import br.com.pactosolucoes.enumeradores.BIEnum;
import br.com.pactosolucoes.turmas.servico.impl.TurmasServiceImpl;
import br.com.pactosolucoes.turmas.servico.intf.TurmasServiceInterface;
import controle.basico.LtvControle;
import negocio.comuns.basico.EmpresaVO;
import negocio.comuns.financeiro.MonitoramentoHistoricoMesVO;
import negocio.comuns.financeiro.MonitoramentoVO;
import negocio.comuns.utilitarias.Calendario;
import negocio.comuns.utilitarias.Uteis;
import negocio.facade.jdbc.arquitetura.SuperEntidade;
import negocio.facade.jdbc.arquitetura.SuperFacadeJDBC;
import negocio.facade.jdbc.crm.ConfiguracaoSistemaCRM;
import negocio.interfaces.financeiro.MonitoramentoInterfaceFacade;
import org.json.JSONObject;
import relatorio.controle.financeiro.FaturamentoSinteticoControleRel;
import relatorio.controle.sad.BITicketMedioControle;
import relatorio.negocio.comuns.basico.TicketMedioVO;

import java.math.BigDecimal;
import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;

public class Monitoramento extends SuperEntidade implements MonitoramentoInterfaceFacade {

    public Monitoramento() throws Exception {
        super();
    }

    public Monitoramento(Connection con) throws Exception {
        super(con);
    }

    public void incluir(MonitoramentoVO monitoramentoVO) throws Exception {
        String sqlInsert = "INSERT INTO monitoramento\n" +
                "(alunosativosnarede, alunosativospagantes, aulasexperimentaismarcadas, aulasmarcadas, aulasconvertidas, chaveecodigo, checkins,\n" +
                "churn, desafiadosdomes, dia, faturamentomensalidade, faturamentomerchan, faturamentonutricao, faturamentototal,\n" +
                "ltvaluno, nomeunidade, planosanuais, planosmensais, planossemestrais, planostrimestrais, ticketmedio)\n" +
                "VALUES (?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?);";

        try (PreparedStatement ps = con.prepareStatement(sqlInsert)) {
            int i = 0;
            ps.setLong(++i, monitoramentoVO.getAlunosAtivosNaRede());
            ps.setLong(++i, monitoramentoVO.getAlunosAtivosPagantes());
            ps.setLong(++i, monitoramentoVO.getAulasExperimentaisMarcadas());
            ps.setLong(++i, monitoramentoVO.getAulasMarcadas());
            ps.setBigDecimal(++i, monitoramentoVO.getAulasConvertidas());
            ps.setString(++i, monitoramentoVO.getChaveECodigo());
            ps.setLong(++i, monitoramentoVO.getCheckins());
            ps.setBigDecimal(++i, monitoramentoVO.getChurn());
            ps.setLong(++i, monitoramentoVO.getDesafiadosDoMes());
            ps.setDate(++i, Uteis.getDataJDBC(monitoramentoVO.getDia()));
            ps.setBigDecimal(++i, monitoramentoVO.getFaturamentoMensalidade());
            ps.setBigDecimal(++i, monitoramentoVO.getFaturamentoMerchan());
            ps.setBigDecimal(++i, monitoramentoVO.getFaturamentoNutricao());
            ps.setBigDecimal(++i, monitoramentoVO.getFaturamentoTotal());
            ps.setBigDecimal(++i, monitoramentoVO.getLtvAluno());
            ps.setString(++i, monitoramentoVO.getNomeUnidade());
            ps.setLong(++i, monitoramentoVO.getPlanosAnuais());
            ps.setLong(++i, monitoramentoVO.getPlanosMensais());
            ps.setLong(++i, monitoramentoVO.getPlanosSemestrais());
            ps.setLong(++i, monitoramentoVO.getPlanosTrimestrais());
            ps.setBigDecimal(++i, monitoramentoVO.getTicketMedio());

            ps.execute();
            monitoramentoVO.setCodigo(obterValorChavePrimariaCodigo());
            MonitoramentoHistoricoMes monitoramentoHistoricoMes = new MonitoramentoHistoricoMes(getCon());
            monitoramentoHistoricoMes.incluirMonitoramentoHistoricoMes(monitoramentoVO.getCodigo(), monitoramentoVO.getMonitoramentoHistoricoMesProduto());
            monitoramentoHistoricoMes = null;
        }
    }

    private HashMap<Long, Long> getPlanosMensais(Integer empresa, Date dataProcessamento) throws Exception {
        long contratosAte2Meses = 0;
        long contratosAte6Meses = 0;
        long contratosAte12Meses = 0;
        long contratosAcima12Meses = 0;

        String query = "select count(c.codigo) quantidade, cd.numeromeses numeromeses from contrato c\n" +
                "inner join contratoduracao cd on cd.contrato = c.codigo\n" +
                "where c.empresa = " + empresa + "\n" +
                "AND c.vigenciade <= '" + Uteis.getDataJDBC(dataProcessamento) + "' \n" +
                "AND c.vigenciaAteAjustada >= '" + Uteis.getDataJDBC(dataProcessamento) + "'\n" +
                "group by cd.numeromeses\n" +
                "order by cd.numeromeses";

        try (ResultSet rs = SuperFacadeJDBC.criarConsulta(query, con)) {
            while (rs.next()) {
                long numeroMeses = rs.getLong("numeromeses");
                if (numeroMeses < 3) {
                    contratosAte2Meses += rs.getLong("quantidade");
                } else if (numeroMeses < 6) {
                    contratosAte6Meses += rs.getLong("quantidade");
                } else if (numeroMeses < 12) {
                    contratosAte12Meses += rs.getLong("quantidade");
                } else {
                    contratosAcima12Meses += rs.getLong("quantidade");
                }
            }
        }

        HashMap<Long, Long> map = new HashMap<>();
        map.put(1L, contratosAte2Meses);
        map.put(3L, contratosAte6Meses);
        map.put(6L, contratosAte12Meses);
        map.put(12L, contratosAcima12Meses);
        return map;
    }

    private long getAlunosAtivosNaRede(final Integer empresa) throws Exception {
        String sql = "SELECT count(*) as quantidade FROM cliente c " +
                "INNER JOIN SituacaoClienteSinteticoDW s ON s.codigoCliente = c.codigo " +
                "   and (c.situacao = s.situacao) and (s.situacao =  'AT' or s.situacaoContrato = 'AT') " +
                "WHERE c.empresa = " + empresa;

        try (ResultSet rs = SuperFacadeJDBC.criarConsulta(sql, con)) {
            if (rs.next()) {
                return rs.getLong("quantidade");
            }
        }
        return 0;

    }

    private List<MonitoramentoHistoricoMesVO> getAlunosAtivosNaRedeHistorico(final Integer empresa, final Date dataProcessamento) throws Exception {
        try {
            int indexMes = 1;
            List<MonitoramentoHistoricoMesVO> lista = new ArrayList<>();
            while (indexMes <= Uteis.getMesData(dataProcessamento)) {
                Date dataFimMes = Uteis.obterUltimoDiaMes(Uteis.getDate(1, indexMes, Uteis.getAnoData(dataProcessamento)));
                MonitoramentoHistoricoMesVO obj = new MonitoramentoHistoricoMesVO();
                obj.setAno(Uteis.getAnoData(dataProcessamento));
                obj.setMes(indexMes);
                obj.setQuantidade(Math.toIntExact(getAlunosAtivosNaRede(empresa)));
                obj.setDia(dataProcessamento);
                obj.setTotal(0.0);
                obj.setIndicador("alunosAtivosNaRede");
                lista.add(obj);
                indexMes++;
            }
            return lista;
        } catch (Exception e) {
            return new ArrayList<>();
        }
    }

    private List<MonitoramentoHistoricoMesVO> getAlunosAtivosPagantesHistorico(final Integer empresa, final Date dataProcessamento) throws Exception {
        try {
            int indexMes = 0;
            List<MonitoramentoHistoricoMesVO> lista = new ArrayList<>();
            while (indexMes < Uteis.getMesData(dataProcessamento)) {
                Date dataFimMes = Uteis.obterUltimoDiaMes(Uteis.getDate(1, indexMes, Uteis.getAnoData(dataProcessamento)));
                MonitoramentoHistoricoMesVO obj = new MonitoramentoHistoricoMesVO();
                obj.setAno(Uteis.getAnoData(dataProcessamento));
                obj.setMes(indexMes);
                obj.setQuantidade(Math.toIntExact(getAlunosAtivosPagantes(empresa, dataFimMes)));
                obj.setDia(dataProcessamento);
                obj.setTotal(0.0);
                obj.setIndicador("alunosAtivosPagantes");
                lista.add(obj);
                indexMes++;
            }
            return lista;
        } catch (Exception e) {
            return new ArrayList<>();
        }
    }

    private long getAlunosAtivosPagantes(final Integer empresa, final Date dataProcessamento) throws Exception {
        String sql = "SELECT count(DISTINCT c.pessoa) as quantidade FROM contrato c\n" +
                "INNER JOIN movParcela ON movParcela.contrato = c.codigo \n" +
                "WHERE c.vigenciade <= '" + Uteis.getDataJDBC(dataProcessamento) + "'\n" +
                "AND c.vigenciaateajustada >= '" + Uteis.getDataJDBC(dataProcessamento) + "'\n" +
                "AND movParcela.dataVencimento <= '" + Uteis.getDataJDBC(dataProcessamento) + "'\n" +
                "AND movParcela.situacao = 'PG'\n" +
                "AND c.empresa = " + empresa;

        try (ResultSet rs = SuperFacadeJDBC.criarConsulta(sql, con)) {
            if (rs.next()) {
                return rs.getLong("quantidade");
            }
        }
        return 0;
    }

    private DadosAulaExperimentalDTO getDadosAulaExperimentalDTO(final String chave, final Integer empresa, final Date dataProcessamento) throws Exception {
        Date inicio = Calendario.getDataComHoraZerada(Uteis.obterPrimeiroDiaMes(dataProcessamento));
        Date fim = Uteis.obterUltimoDiaMesUltimaHora(dataProcessamento);

        ConfiguracaoSistemaCRM configuracaoSistemaCRM = new ConfiguracaoSistemaCRM(getCon());
        Integer nrDiasContarResultado = configuracaoSistemaCRM.obterNrDiasContarResultado();
        configuracaoSistemaCRM = null;

        FiltroDTO filtroDTO = new FiltroDTO();
        filtroDTO.setNome(BIEnum.AULA_EXPERIMENTAL.name());
        JSONObject filtros = new JSONObject();
        filtros.put("inicio", inicio.getTime());
        filtros.put("fim", fim.getTime());
        filtros.put("responsavel", 0);
        filtros.put("empresa", empresa);
        filtros.put("listas", false);
        filtros.put("nrDiasContarResultado", nrDiasContarResultado);
        filtroDTO.setFiltros(filtros.toString());

        filtroDTO = new BiMSServiceImpl().obterBI(chave, BIEnum.AULA_EXPERIMENTAL, filtroDTO, true);
        return new DadosAulaExperimentalDTO(new JSONObject(filtroDTO.getJsonDados()));
    }

    private long getAulasMarcadas(final Integer empresa, final Date dataProcessamento) throws Exception {
        TurmasServiceInterface turmasService = new TurmasServiceImpl(con);
        List<AgendaTotalJSON> agendamentos = turmasService.consultarParaAgenda(
                Calendario.getDataComHoraZerada(Uteis.obterPrimeiroDiaMes(dataProcessamento)),
                Calendario.getDataComHora(dataProcessamento, "23:59:59"),
                null, new ArrayList<>(), empresa,
                false);
        int qtd = 0;
        for (AgendaTotalJSON agendaTotalJSON : agendamentos) {
            qtd += agendaTotalJSON.getNrVagasPreenchidas();
        }
        return qtd;
    }

    private List<MonitoramentoHistoricoMesVO> getAulasMarcadasHistorico(final Integer empresa, final Date dataProcessamento) throws Exception {
        try {
            int indexMes = 0;
            List<MonitoramentoHistoricoMesVO> lista = new ArrayList<>();
            while (indexMes < Uteis.getMesData(dataProcessamento)) {
                Date dataFimMes = Uteis.obterUltimoDiaMes(Uteis.getDate(1, indexMes, Uteis.getAnoData(dataProcessamento)));
                MonitoramentoHistoricoMesVO obj = new MonitoramentoHistoricoMesVO();
                obj.setAno(Uteis.getAnoData(dataProcessamento));
                obj.setMes(indexMes);
                obj.setQuantidade(Math.toIntExact(getAulasMarcadas(empresa, dataFimMes)));
                obj.setDia(dataProcessamento);
                obj.setTotal(0.0);
                obj.setIndicador("aulasExperimentais");
                lista.add(obj);
                indexMes++;
            }
            return lista;
        } catch (Exception e) {
            return new ArrayList<>();
        }
    }

    private List<MonitoramentoHistoricoMesVO> getHistoricoAulasExperimentais(final String chave, final Integer empresa, final Date dataProcessamento, List<MonitoramentoHistoricoMesVO> lista) throws Exception {
        try {
            ConfiguracaoSistemaCRM configuracaoSistemaCRM = new ConfiguracaoSistemaCRM(getCon());
            Integer nrDiasContarResultado = configuracaoSistemaCRM.obterNrDiasContarResultado();
            configuracaoSistemaCRM = null;

            int indexMes = 0;
            List<MonitoramentoHistoricoMesVO> listaAulasMarcadas = new ArrayList<>();
            List<MonitoramentoHistoricoMesVO> listaAulasConvertidas = new ArrayList<>();
            while (indexMes < Uteis.getMesData(dataProcessamento)) {
                Date dataConsulta = Uteis.getDate(1, indexMes, Uteis.getAnoData(dataProcessamento));
                Date inicio = Calendario.getDataComHoraZerada(Uteis.obterPrimeiroDiaMes(dataConsulta));
                Date fim = Uteis.obterUltimoDiaMesUltimaHora(dataConsulta);

                FiltroDTO filtroDTO = new FiltroDTO();
                filtroDTO.setNome(BIEnum.AULA_EXPERIMENTAL.name());
                JSONObject filtros = new JSONObject();
                filtros.put("inicio", inicio.getTime());
                filtros.put("fim", fim.getTime());
                filtros.put("responsavel", 0);
                filtros.put("empresa", empresa);
                filtros.put("listas", false);
                filtros.put("nrDiasContarResultado", nrDiasContarResultado);
                filtroDTO.setFiltros(filtros.toString());

                filtroDTO = new BiMSServiceImpl().obterBI(chave, BIEnum.AULA_EXPERIMENTAL, filtroDTO, true);
                DadosAulaExperimentalDTO dados =  new DadosAulaExperimentalDTO(new JSONObject(filtroDTO.getJsonDados()));

                {
                    MonitoramentoHistoricoMesVO objAulasMarcadas = new MonitoramentoHistoricoMesVO();
                    objAulasMarcadas.setAno(Uteis.getAnoData(dataProcessamento));
                    objAulasMarcadas.setMes(indexMes);
                    objAulasMarcadas.setQuantidade(dados.getAulasAgendadas());
                    objAulasMarcadas.setDia(dataProcessamento);
                    objAulasMarcadas.setTotal(0.0);
                    objAulasMarcadas.setIndicador("aulasExperimentaisMarcadas");
                    listaAulasMarcadas.add(objAulasMarcadas);
                }

                {
                    MonitoramentoHistoricoMesVO objAulasConvertidas = new MonitoramentoHistoricoMesVO();
                    objAulasConvertidas.setAno(Uteis.getAnoData(dataProcessamento));
                    objAulasConvertidas.setMes(indexMes);
                    objAulasConvertidas.setQuantidade(0);
                    objAulasConvertidas.setDia(dataProcessamento);
                    objAulasConvertidas.setTotal(dados.getIndiceConversaoAulas());
                    objAulasConvertidas.setIndicador("aulasConvertidas");
                    listaAulasConvertidas.add(objAulasConvertidas);
                }

                indexMes++;
            }

            lista.addAll(listaAulasMarcadas);
            lista.addAll(listaAulasConvertidas);
            return lista;
        } catch (Exception e) {
            return new ArrayList<>();
        }
    }

    private List<MonitoramentoHistoricoMesVO> getAulasDesafiadosHistorico(final Integer empresa, final Date dataProcessamento) throws Exception {
        try {
            int indexMes = 0;
            List<MonitoramentoHistoricoMesVO> lista = new ArrayList<>();
            while (indexMes < Uteis.getMesData(dataProcessamento)) {
                Date dataFimMes = Uteis.obterUltimoDiaMes(Uteis.getDate(1, indexMes, Uteis.getAnoData(dataProcessamento)));
                MonitoramentoHistoricoMesVO obj = new MonitoramentoHistoricoMesVO();
                obj.setAno(Uteis.getAnoData(dataProcessamento));
                obj.setMes(indexMes);
                obj.setQuantidade(Math.toIntExact(getDesafiados(empresa, dataFimMes)));
                obj.setDia(dataProcessamento);
                obj.setTotal(0.0);
                obj.setIndicador("desafiadosDoMes");
                lista.add(obj);
                indexMes++;
            }
            return lista;
        } catch (Exception e) {
            return new ArrayList<>();
        }
    }

    private long getDesafiados(final Integer empresa, final Date dataProcessamento) throws Exception {
        String sql = "SELECT count(*) as quantidade \n" +
                "from movproduto mp \n" +
                "INNER JOIN produto prod ON mp.produto = prod.codigo AND tipoproduto = 'DS'\n" +
                "WHERE empresa = " + empresa + "\n" +
                "and datalancamento::date = '" + Uteis.getDataJDBC(dataProcessamento) + "'";

        try (ResultSet rs = SuperFacadeJDBC.criarConsulta(sql, con)) {
            if (rs.next()) {
                return rs.getLong("quantidade");
            }
        }

        return 0;
    }

    private double getTicketMedio(final Integer empresa, final Date dataProcessamento, String key) {
        try {
            EmpresaVO empresaVO = new EmpresaVO();
            empresaVO.setCodigo(empresa);

            BITicketMedioControle ticketMedio = new BITicketMedioControle();

            ticketMedio.setDataBaseFiltro(Calendario.getDataComHoraZerada(dataProcessamento));
            ticketMedio.atualizarData(empresaVO, key);
            TicketMedioVO ticket = ticketMedio.getTicket();
            if (ticket != null &&
                    ticket.getTicketReceita() != null) {
                return Uteis.arredondarForcando2CasasDecimais(ticket.getTicketReceita());
            }
            return 0.0;
        } catch (Exception e) {
            return 0.0;
        }
    }

    private List<MonitoramentoHistoricoMesVO> getTicketMedioHistorico(final Integer empresa, final Date dataProcessamento, String key) throws Exception {
        try {
            int indexMes = 0;
            List<MonitoramentoHistoricoMesVO> lista = new ArrayList<>();
            while (indexMes < Uteis.getMesData(dataProcessamento)) {
                Date dataFimMes = Uteis.obterUltimoDiaMes(Uteis.getDate(1, indexMes, Uteis.getAnoData(dataProcessamento)));
                MonitoramentoHistoricoMesVO obj = new MonitoramentoHistoricoMesVO();
                obj.setAno(Uteis.getAnoData(dataProcessamento));
                obj.setMes(indexMes);
                obj.setQuantidade(0);
                obj.setDia(dataProcessamento);
                obj.setTotal(getTicketMedio(empresa, dataFimMes, key));
                obj.setIndicador("ticketMedio");
                lista.add(obj);
                indexMes++;
            }
            return lista;
        } catch (Exception e) {
            return new ArrayList<>();
        }
    }

    private List<MonitoramentoHistoricoMesVO> getLTVHistorico(final Integer empresa, final Date dataProcessamento, String key) throws Exception {
        try {
            int indexMes = 0;
            List<MonitoramentoHistoricoMesVO> lista = new ArrayList<>();
            while (indexMes < Uteis.getMesData(dataProcessamento)) {
                Date dataFimMes = Uteis.obterUltimoDiaMes(Uteis.getDate(1, indexMes, Uteis.getAnoData(dataProcessamento)));
                MonitoramentoHistoricoMesVO obj = new MonitoramentoHistoricoMesVO();
                obj.setAno(Uteis.getAnoData(dataProcessamento));
                obj.setMes(indexMes);
                obj.setQuantidade(0);
                obj.setDia(dataProcessamento);
                obj.setTotal(getLTV(empresa, dataFimMes, key));
                obj.setIndicador("ltvAluno");
                lista.add(obj);
                indexMes++;
            }
            return lista;
        } catch (Exception e) {
            return new ArrayList<>();
        }
    }

    private List<MonitoramentoHistoricoMesVO> getChurnHistorico(final Integer empresa, final Date dataProcessamento, String key) throws Exception {
        try {
            int indexMes = 0;
            List<MonitoramentoHistoricoMesVO> lista = new ArrayList<>();
            while (indexMes < Uteis.getMesData(dataProcessamento)) {
                Date dataFimMes = Uteis.obterUltimoDiaMes(Uteis.getDate(1, indexMes, Uteis.getAnoData(dataProcessamento)));
                MonitoramentoHistoricoMesVO obj = new MonitoramentoHistoricoMesVO();
                obj.setAno(Uteis.getAnoData(dataProcessamento));
                obj.setMes(indexMes);
                obj.setQuantidade(0);
                obj.setDia(dataProcessamento);
                obj.setTotal(getChurn(empresa, dataFimMes, key));
                obj.setIndicador("churn");
                lista.add(obj);
                indexMes++;
            }
            return lista;
        } catch (Exception e) {
            return new ArrayList<>();
        }
    }

    private double getChurn(final Integer empresa, final Date dataProcessamento, String key) {
        try {
            EmpresaVO empresaVO = new EmpresaVO();
            empresaVO.setCodigo(empresa);

            LtvControle ltv = new LtvControle();
            ltv.setEmpresaConsulta(empresa);

            ltv.setDataBaseFiltro(Calendario.getDataComHoraZerada(dataProcessamento));
            ltv.recalcularMetricas(key);
            return ltv.getChurn();
        } catch (Exception e) {
            return 0;
        }
    }


    private double getLTV(final Integer empresa, final Date dataProcessamento, String key) {
        try {
            EmpresaVO empresaVO = new EmpresaVO();
            empresaVO.setCodigo(empresa);

            LtvControle ltv = new LtvControle();
            ltv.setEmpresaConsulta(empresa);

            ltv.setDataBaseFiltro(Calendario.getDataComHoraZerada(dataProcessamento));
            ltv.recalcularMetricas(key);
            return ltv.getLtv();
        } catch (Exception e) {
            return 0;
        }
    }

    private long getCheckins(final Integer empresa, final Date dataProcessamento) throws Exception {
        String sql = "select count(*) quantidade from acessocliente a\n" +
                "inner join cliente c on c.codigo = a.cliente\n" +
                "where dthrentrada::date = '" + Uteis.getDataJDBC(dataProcessamento) + "' \n" +
                "and c.empresa= " + empresa;

        try (ResultSet rs = SuperFacadeJDBC.criarConsulta(sql, con)) {
            if (rs.next()) {
                return rs.getLong("quantidade");
            }
        }

        return 0;
    }

    private List<MonitoramentoHistoricoMesVO> getCheckinsHistorico(final Integer empresa, final Date dataProcessamento) throws Exception {
        try {
            int indexMes = 0;
            List<MonitoramentoHistoricoMesVO> lista = new ArrayList<>();
            while (indexMes < Uteis.getMesData(dataProcessamento)) {
                Date dataFimMes = Uteis.obterUltimoDiaMes(Uteis.getDate(1, indexMes, Uteis.getAnoData(dataProcessamento)));
                MonitoramentoHistoricoMesVO obj = new MonitoramentoHistoricoMesVO();
                obj.setAno(Uteis.getAnoData(dataProcessamento));
                obj.setMes(indexMes);
                obj.setQuantidade(Math.toIntExact(getCheckins(empresa, dataFimMes)));
                obj.setDia(dataProcessamento);
                obj.setTotal(0.0);
                obj.setIndicador("checkins");
                lista.add(obj);
                indexMes++;
            }
            return lista;
        } catch (Exception e) {
            return new ArrayList<>();
        }
    }

    private double getFaturamentoTotal(final Integer empresa, final Date dataProcessamento) {
        try {
            EmpresaVO empresaVO = new EmpresaVO();
            empresaVO.setCodigo(empresa);

            FaturamentoSinteticoControleRel faturamento = new FaturamentoSinteticoControleRel();
            faturamento.setEmpresa(empresaVO);

            faturamento.getFaturamentoSinteticoRel().setDataInicio(Calendario.getDataComHoraZerada(dataProcessamento));
            faturamento.getFaturamentoSinteticoRel().setDataTermino(Calendario.getDataComUltimaHora(dataProcessamento));
            faturamento.setAgrupamento("");
            faturamento.setMesReferenciaPlano(false);
            double valorCalculado = 0.0;
            faturamento.setSomenteFaturamentoRecebido(false);
            faturamento.imprimir(false, empresa);
            if (!faturamento.getResumo().getListaProduto().isEmpty()
                    && !faturamento.getResumo().getListaProduto().get(0).getListaProdutoXMes().isEmpty()) {
                valorCalculado = Uteis.arredondarForcando2CasasDecimais(faturamento.getResumo().getListaProduto().get(0).getListaProdutoXMes().get(0).getValor());
            }
            return valorCalculado;
        } catch (Exception e) {
            return 0;
        }
    }

    private List<MonitoramentoHistoricoMesVO> getFaturamentoTotalHistorico(final Integer empresa, final Date dataProcessamento) throws Exception {
        try {
            int indexMes = 0;
            List<MonitoramentoHistoricoMesVO> lista = new ArrayList<>();
            while (indexMes < Uteis.getMesData(dataProcessamento)) {
                Date dataFimMes = Uteis.obterUltimoDiaMes(Uteis.getDate(1, indexMes, Uteis.getAnoData(dataProcessamento)));
                MonitoramentoHistoricoMesVO obj = new MonitoramentoHistoricoMesVO();
                obj.setAno(Uteis.getAnoData(dataProcessamento));
                obj.setMes(indexMes);
                obj.setQuantidade(0);
                obj.setDia(dataProcessamento);
                obj.setTotal(getFaturamentoTotal(empresa, dataFimMes));
                obj.setIndicador("faturamentoTotal");
                lista.add(obj);
                indexMes++;
            }
            return lista;
        } catch (Exception e) {
            return new ArrayList<>();
        }
    }

    private double getFaturamentoMensalidades(final Integer empresa, final Date dataProcessamento) {
        try {
            EmpresaVO empresaVO = new EmpresaVO();
            empresaVO.setCodigo(empresa);

            FaturamentoSinteticoControleRel faturamento = new FaturamentoSinteticoControleRel();
            faturamento.setEmpresa(empresaVO);

            faturamento.getFaturamentoSinteticoRel().setDataInicio(Calendario.getDataComHoraZerada(dataProcessamento));
            faturamento.getFaturamentoSinteticoRel().setDataTermino(Calendario.getDataComUltimaHora(dataProcessamento));
            faturamento.setAgrupamento("nomeDuracao");
            faturamento.setMesReferenciaPlano(true);
            faturamento.getMapaTipoProduto().put("PM", true);

            double valorCalculado = 0.0;
            faturamento.setSomenteFaturamentoRecebido(false);
            faturamento.imprimir(false, empresa);
            if (!faturamento.getResumo().getListaProduto().isEmpty()
                    && !faturamento.getResumo().getListaProduto().get(0).getListaProdutoXMes().isEmpty()) {
                valorCalculado = Uteis.arredondarForcando2CasasDecimais(faturamento.getResumo().getListaProduto().get(0).getListaProdutoXMes().get(0).getValor());
            }
            return valorCalculado;
        } catch (Exception e) {
            return 0;
        }
    }

    private List<MonitoramentoHistoricoMesVO> getFaturamentoMensalidadesHistorico(final Integer empresa, final Date dataProcessamento) throws Exception {
        try {
            int indexMes = 0;
            List<MonitoramentoHistoricoMesVO> lista = new ArrayList<>();
            while (indexMes < Uteis.getMesData(dataProcessamento)) {
                Date dataFimMes = Uteis.obterUltimoDiaMes(Uteis.getDate(1, indexMes, Uteis.getAnoData(dataProcessamento)));
                MonitoramentoHistoricoMesVO obj = new MonitoramentoHistoricoMesVO();
                obj.setAno(Uteis.getAnoData(dataProcessamento));
                obj.setMes(indexMes);
                obj.setQuantidade(0);
                obj.setDia(dataProcessamento);
                obj.setTotal(getFaturamentoMensalidades(empresa, dataFimMes));
                obj.setIndicador("faturamentoMensalidades");
                lista.add(obj);
                indexMes++;
            }
            return lista;
        } catch (Exception e) {
            return new ArrayList<>();
        }
    }

    private List<MonitoramentoHistoricoMesVO> getFaturamentoNutricaoHistorico(final Integer empresa, final Date dataProcessamento) throws Exception {
        try {
            EmpresaVO empresaVO = new EmpresaVO();
            empresaVO.setCodigo(empresa);

            BITicketMedioControle ticketMedio = new BITicketMedioControle();
            int indexMes = 0;
            List<MonitoramentoHistoricoMesVO> lista = new ArrayList<>();
            while (indexMes < Uteis.getMesData(dataProcessamento)) {
                Date dataFimMes = Uteis.obterUltimoDiaMes(Uteis.getDate(1, indexMes, Uteis.getAnoData(dataProcessamento)));
                MonitoramentoHistoricoMesVO obj = new MonitoramentoHistoricoMesVO();
                obj.setAno(Uteis.getAnoData(dataProcessamento));
                obj.setMes(indexMes);
                obj.setQuantidade(0);
                obj.setDia(dataProcessamento);
                obj.setTotal(getFaturamentoNutricao(empresa, dataFimMes));
                obj.setIndicador("faturamentoNutricao");
                lista.add(obj);
                indexMes++;
            }
            return lista;
        } catch (Exception e) {
            return new ArrayList<>();
        }
    }

    private List<MonitoramentoHistoricoMesVO> getFaturamentoMerchanHistorico(final Integer empresa, final Date dataProcessamento) throws Exception {
        try {
            EmpresaVO empresaVO = new EmpresaVO();
            empresaVO.setCodigo(empresa);

            BITicketMedioControle ticketMedio = new BITicketMedioControle();
            int indexMes = 0;
            List<MonitoramentoHistoricoMesVO> lista = new ArrayList<>();
            while (indexMes < Uteis.getMesData(dataProcessamento)) {
                Date dataFimMes = Uteis.obterUltimoDiaMes(Uteis.getDate(1, indexMes, Uteis.getAnoData(dataProcessamento)));
                MonitoramentoHistoricoMesVO obj = new MonitoramentoHistoricoMesVO();
                obj.setAno(Uteis.getAnoData(dataProcessamento));
                obj.setMes(indexMes);
                obj.setQuantidade(0);
                obj.setDia(dataProcessamento);
                obj.setTotal(getFaturamentoMerchan(empresa, dataFimMes));
                obj.setIndicador("faturamentoMerchan");
                lista.add(obj);
                indexMes++;
            }
            return lista;
        } catch (Exception e) {
            return new ArrayList<>();
        }
    }

    private double getFaturamentoNutricao(final Integer empresa, final Date dataProcessamento) {
        try {
            String query = "SELECT sum(mprod.totalfinal) as total, count(mprod.codigo) as quantidade FROM movproduto mprod\n" +
                    "INNER JOIN produto prod ON mprod.produto = prod.codigo\n" +
                    "INNER JOIN categoriaproduto cprod ON prod.categoriaproduto = cprod.codigo\n" +
                    "WHERE cprod.descricao LIKE '%BEM ESTAR%'\n" +
                    "AND mprod.situacao <> 'CA'\n" +
                    "AND mprod.datalancamento::date = '" + Uteis.getDataJDBC(dataProcessamento) + "'\n" +
                    "AND mprod.empresa = " + empresa;
            try (ResultSet rs = SuperFacadeJDBC.criarConsulta(query, con)) {
                if (rs.next()) {
                    return rs.getLong("quantidade") > 0 ? rs.getDouble("total") : 0;
                }
            }
        } catch (Exception e) {
            return 0;
        }
        return 0;
    }

    private double getFaturamentoMerchan(final Integer empresa, final Date dataProcessamento) {
        try {
            String query = "SELECT sum(mprod.totalfinal) as total, count(mprod.codigo) as quantidade FROM movproduto mprod\n" +
                    "INNER JOIN produto prod ON mprod.produto = prod.codigo\n" +
                    "INNER JOIN categoriaproduto cprod ON prod.categoriaproduto = cprod.codigo\n" +
                    "WHERE cprod.descricao = 'BOX - LOJA'\n" +
                    "AND mprod.situacao <> 'CA'\n" +
                    "AND mprod.datalancamento::date = '" + Uteis.getDataJDBC(dataProcessamento) + "'\n" +
                    "AND mprod.empresa = " + empresa;
            try (ResultSet rs = SuperFacadeJDBC.criarConsulta(query, con)) {
                if (rs.next()) {
                    return rs.getLong("quantidade") > 0 ? rs.getDouble("total") : 0;
                }
            }
        } catch (Exception e) {
            return 0;
        }
        return 0;
    }

    private List<MonitoramentoHistoricoMesVO> getMonitoramentoHistoricoMesProduto(final Integer empresa, final Date dataProcessamento) {
        try {
            MonitoramentoHistoricoMes monitoramentoHistoricoMes = new MonitoramentoHistoricoMes(getCon());
            List<MonitoramentoHistoricoMesVO> monitoramentoHistoricoMesVOS = monitoramentoHistoricoMes.montarMonitoramentoHistoricoMesProduto(empresa, dataProcessamento);
            monitoramentoHistoricoMes = null;
            return monitoramentoHistoricoMesVOS;
        } catch (Exception e) {
            return new ArrayList<>();
        }
    }

    public MonitoramentoVO gerarDadosMonitoramento(final String chave, final Integer empresa, final String nomeEmpresa, final Date dataGerar) throws Exception {
        MonitoramentoVO monitoramentoVO = new MonitoramentoVO();
        try {
            HashMap<Long, Long> map = getPlanosMensais(empresa, dataGerar);
            monitoramentoVO.setMonitoramentoHistoricoMesProduto(new ArrayList<>());
            monitoramentoVO.setNomeUnidade(nomeEmpresa);
            monitoramentoVO.setDia(dataGerar);
            monitoramentoVO.setChaveECodigo(chave + "-" + empresa);
            monitoramentoVO.setAlunosAtivosNaRede(getAlunosAtivosNaRede(empresa));
            monitoramentoVO.getMonitoramentoHistoricoMesProduto().addAll(getAlunosAtivosNaRedeHistorico(empresa, dataGerar));
            monitoramentoVO.setAlunosAtivosPagantes(getAlunosAtivosPagantes(empresa, dataGerar));
            monitoramentoVO.getMonitoramentoHistoricoMesProduto().addAll(getAlunosAtivosPagantesHistorico(empresa, dataGerar));

            monitoramentoVO.setAulasMarcadas(getAulasMarcadas(empresa, dataGerar));
            monitoramentoVO.getMonitoramentoHistoricoMesProduto().addAll(getAulasMarcadasHistorico(empresa, dataGerar));

            DadosAulaExperimentalDTO dadosAulaExperimentalDTO = getDadosAulaExperimentalDTO(chave, empresa, dataGerar);
            monitoramentoVO.setAulasExperimentaisMarcadas(dadosAulaExperimentalDTO.getAulasAgendadas().longValue());
            monitoramentoVO.setAulasConvertidas(BigDecimal.valueOf(dadosAulaExperimentalDTO.getIndiceConversaoAulas()));
            getHistoricoAulasExperimentais(chave, empresa, dataGerar, monitoramentoVO.getMonitoramentoHistoricoMesProduto());

            monitoramentoVO.setDesafiadosDoMes(getDesafiados(empresa, dataGerar));
            monitoramentoVO.getMonitoramentoHistoricoMesProduto().addAll(getAulasDesafiadosHistorico(empresa, dataGerar));
            monitoramentoVO.setTicketMedio(BigDecimal.valueOf(getTicketMedio(empresa, dataGerar, chave)));
            monitoramentoVO.getMonitoramentoHistoricoMesProduto().addAll(getTicketMedioHistorico(empresa, dataGerar, chave));
            monitoramentoVO.setLtvAluno(BigDecimal.valueOf(getLTV(empresa, dataGerar, chave)));
            monitoramentoVO.getMonitoramentoHistoricoMesProduto().addAll(getLTVHistorico(empresa, dataGerar, chave));
            monitoramentoVO.setChurn(BigDecimal.valueOf(getChurn(empresa, dataGerar, chave)));
            monitoramentoVO.getMonitoramentoHistoricoMesProduto().addAll(getChurnHistorico(empresa, dataGerar, chave));
            monitoramentoVO.setPlanosMensais(map.get(1L));
            monitoramentoVO.setPlanosTrimestrais(map.get(3L));
            monitoramentoVO.setPlanosSemestrais(map.get(6L));
            monitoramentoVO.setPlanosAnuais(map.get(12L));
            monitoramentoVO.setCheckins(getCheckins(empresa, dataGerar));
            monitoramentoVO.getMonitoramentoHistoricoMesProduto().addAll(getCheckinsHistorico(empresa, dataGerar));
            monitoramentoVO.setFaturamentoTotal(BigDecimal.valueOf(getFaturamentoTotal(empresa, dataGerar)));
            monitoramentoVO.getMonitoramentoHistoricoMesProduto().addAll(getFaturamentoTotalHistorico(empresa, dataGerar));
            monitoramentoVO.setFaturamentoMensalidade(BigDecimal.valueOf(getFaturamentoMensalidades(empresa, dataGerar)));
            monitoramentoVO.getMonitoramentoHistoricoMesProduto().addAll(getFaturamentoMensalidadesHistorico(empresa, dataGerar));
            monitoramentoVO.setFaturamentoNutricao(BigDecimal.valueOf(getFaturamentoNutricao(empresa, dataGerar)));
            monitoramentoVO.getMonitoramentoHistoricoMesProduto().addAll(getFaturamentoNutricaoHistorico(empresa, dataGerar));
            monitoramentoVO.setFaturamentoMerchan(BigDecimal.valueOf(getFaturamentoMerchan(empresa, dataGerar)));
            monitoramentoVO.getMonitoramentoHistoricoMesProduto().addAll(getFaturamentoMerchanHistorico(empresa, dataGerar));
            monitoramentoVO.getMonitoramentoHistoricoMesProduto().addAll(getMonitoramentoHistoricoMesProduto(empresa, dataGerar));

        } catch (Exception e) {
            Uteis.logar(e, Monitoramento.class);
            throw e;
        }

        return monitoramentoVO;
    }

    public void deletar(final String chave, final Integer empresa, Date dataInicio, Date dataFinal) throws Exception {
        MonitoramentoHistoricoMes monitoramentoHistoricoMes = new MonitoramentoHistoricoMes(getCon());
        monitoramentoHistoricoMes.excluirMonitoramentoHistoricoMesPorChave(chave, empresa, dataInicio, dataFinal, false);
        StringBuilder sql = new StringBuilder();
        sql.append("DELETE FROM monitoramento\n");
        sql.append("WHERE chaveecodigo = ?\n");
        sql.append("AND dia::date >= ?\n");
        sql.append("AND dia::date <= ?\n");

        try (PreparedStatement ps = con.prepareStatement(sql.toString())) {
            int i = 0;
            ps.setString(++i, chave + "-" + empresa);
            ps.setDate(++i, Uteis.getDataJDBC(dataInicio));
            ps.setDate(++i, Uteis.getDataJDBC(dataFinal));
            ps.execute();
        }

        monitoramentoHistoricoMes = null;
    }
}

package negocio.facade.jdbc.financeiro;

import negocio.comuns.financeiro.ConvenioCobrancaVO;
import negocio.comuns.financeiro.RemessaCancelamentoItemVO;
import negocio.comuns.financeiro.RemessaItemVO;
import negocio.comuns.financeiro.RemessaVO;
import negocio.comuns.utilitarias.Uteis;
import negocio.facade.jdbc.arquitetura.SuperEntidade;
import negocio.interfaces.financeiro.RemessaCancelamentoItemInterfaceFacade;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * Created by GlaucoT on 08/04/2016
 */
public class RemessaCancelamentoItem extends SuperEntidade implements RemessaCancelamentoItemInterfaceFacade {


    public RemessaCancelamentoItem() throws Exception {
    }

    public RemessaCancelamentoItem(Connection conexao) throws Exception {
        super(conexao);
    }

    public static List<RemessaCancelamentoItemVO> montarDadosConsulta(ResultSet tabelaResultado, Integer nivelMontarDados, Connection con) throws Exception {
        List<RemessaCancelamentoItemVO> resultado = new ArrayList<RemessaCancelamentoItemVO>();
        while (tabelaResultado.next()) {
            RemessaCancelamentoItemVO obj = RemessaCancelamentoItem.montarDados(tabelaResultado, nivelMontarDados, con);
            resultado.add(obj);
        }

        return resultado;
    }

    public static RemessaCancelamentoItemVO montarDados(ResultSet dadosSQL, int nivelMontarDados, Connection con) throws Exception {
        RemessaCancelamentoItemVO obj = new RemessaCancelamentoItemVO();

        obj.setCodigo(dadosSQL.getInt("codigo"));
        obj.getRemessa().setCodigo(dadosSQL.getInt("remessa"));
        obj.getItemRemessaCancelar().setCodigo(dadosSQL.getInt("remessaitem"));
        obj.setProps(Uteis.obterMapFromString(dadosSQL.getString("props")));

        if (nivelMontarDados == Uteis.NIVELMONTARDADOS_MINIMOS) {
            return obj;
        }
        if (nivelMontarDados == Uteis.NIVELMONTARDADOS_DADOSBASICOS) {
            montarDadosRemessaItem(obj, Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA, con);
            montarDadosRemessa(obj, Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA, con);
            return obj;
        }
        if (nivelMontarDados == Uteis.NIVELMONTARDADOS_TODOS) {
            montarDadosRemessaItem(obj, Uteis.NIVELMONTARDADOS_TODOS, con);
            montarDadosRemessa(obj, Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA, con);
            return obj;
        }
        return obj;
    }

    private static void montarDadosRemessa(RemessaCancelamentoItemVO obj, int nivelMontarDados, Connection con) throws Exception {
        if (obj.getRemessa().getCodigo() == 0) {
            obj.setRemessa(new RemessaVO());
            return;
        }
        Remessa remessaDAO = new Remessa(con);
        RemessaVO remessaCancelamento = remessaDAO.consultarPorChavePrimaria(obj.getRemessa().getCodigo());
        obj.setRemessa(remessaCancelamento);
        remessaDAO = null;
    }

    private static void montarDadosRemessaItem(RemessaCancelamentoItemVO obj, int nivelMontarDados, Connection con) throws Exception {
        if (obj.getItemRemessaCancelar().getCodigo() == 0) {
            obj.setItemRemessaCancelar(new RemessaItemVO());
            return;
        }
        RemessaItem remessaItemDAO = new RemessaItem(con);
        RemessaItemVO itemCancelar = remessaItemDAO.consultarPorChavePrimaria(obj.getItemRemessaCancelar().getCodigo(), nivelMontarDados);
        obj.setItemRemessaCancelar(itemCancelar);
        remessaItemDAO = null;
    }

    @Override
    public void incluir(RemessaCancelamentoItemVO obj) throws Exception {
        String sql = "INSERT INTO remessacancelamentoitem(remessa, remessaitem, props) VALUES (?, ?, ?)";
        try (PreparedStatement sqlInserir = con.prepareStatement(sql)){
            int i = 0;
            sqlInserir.setInt(++i, obj.getRemessa().getCodigo());
            sqlInserir.setInt(++i, obj.getItemRemessaCancelar().getCodigo());
            sqlInserir.setString(++i, obj.getProps().toString());
            sqlInserir.execute();
        }
        obj.setCodigo(obterValorChavePrimariaCodigo());
        obj.setNovoObj(false);
    }

    @Override
    public void alterar(RemessaCancelamentoItemVO obj) throws Exception {
        String sql = "UPDATE remessacancelamentoitem SET remessa = ?, remessaitem = ?, props = ? WHERE codigo = ?";
        PreparedStatement sqlAlterar = con.prepareStatement(sql);
        int i = 0;
        sqlAlterar.setInt(++i, obj.getRemessa().getCodigo());
        sqlAlterar.setInt(++i, obj.getItemRemessaCancelar().getCodigo());
        sqlAlterar.setString(++i, obj.getProps().toString());
        sqlAlterar.setInt(++i, obj.getCodigo());
        sqlAlterar.execute();
        obj.setNovoObj(false);
    }

    @Override
    public void excluir(RemessaCancelamentoItemVO obj) throws Exception {
        String sql = "DELETE FROM remessacancelamentoitem WHERE codigo = ?";
        PreparedStatement sqlExcluir = con.prepareStatement(sql);
        int i = 0;
        sqlExcluir.setInt(++i, obj.getCodigo());
        sqlExcluir.execute();
    }

    @Override
    public List<RemessaCancelamentoItemVO> consultarItensAutorizados(ConvenioCobrancaVO convenio, Date dataInicio, Date dataFim) throws Exception {
        List<RemessaCancelamentoItemVO> itensAutorizados = new ArrayList<RemessaCancelamentoItemVO>();

        StringBuilder sql = new StringBuilder();
        StringBuilder sql2 = new StringBuilder();
        String joinMovParcela1 = "INNER JOIN movparcela mpar ON ri.movparcela = mpar.codigo";
        String joinMovParcela2 = "INNER JOIN remessaitemmovparcela rimov ON rimov.remessaitem = ri.codigo \n" +
                "  INNER JOIN movparcela mpar ON rimov.movparcela = mpar.codigo";

        sql.append("SELECT DISTINCT\n");
        sql.append("  r.dataregistro AS registroRemessa,\n");
        sql.append("  r.codigo       AS remessa,\n");
        sql.append("  ri.codigo      AS remessaitem,\n");
        sql.append("  ri.props,\n");
        sql.append("  mpar.codigo    AS movparcela,\n");
        sql.append("  mpar.valorparcela,\n");
        sql.append("  mpar.descricao,\n");
        sql.append("  p.nome,\n");
        sql.append("  mpag.codigo    AS movpagamento\n");
        sql.append("FROM remessaitem ri\n");
        sql.append("  INNER JOIN movpagamento mpag ON ri.movpagamento = mpag.codigo\n");
        sql.append("  INNER JOIN remessa r ON ri.remessa = r.codigo\n");
        sql.append("  INNER JOIN movparcela mpar ON ri.movparcela = mpar.codigo\n");
        sql.append("  INNER JOIN pessoa p ON mpar.pessoa = p.codigo\n");
        sql.append("  LEFT JOIN remessacancelamentoitem rci ON ri.codigo = rci.remessaitem\n");
        sql.append("  LEFT JOIN remessa rc ON rc.codigo = rci.remessa\n");
        sql.append("WHERE mpag.datalancamento >= ? AND mpag.datalancamento <= ?\n");
        if (convenio.getCodigo() > 0) {
            sql.append("      AND r.conveniocobranca = ?");
        }
        sql.append("      AND r.situacaoremessa = 2\n");
        sql.append("      AND (rc.situacaoremessa IS NULL OR rc.situacaoremessa = 2)\n");

        sql2.append(sql.toString().replace(joinMovParcela1,joinMovParcela2));
        sql.append("union \n").append(sql2);

        PreparedStatement ps = con.prepareStatement(sql.toString());
        int i = 0;
        ps.setTimestamp(++i, Uteis.getDataHoraJDBC(dataInicio, "00:00:00"));
        ps.setTimestamp(++i, Uteis.getDataHoraJDBC(dataFim, "23:59:59"));
        if (convenio.getCodigo() > 0) {
            ps.setInt(++i, convenio.getCodigo());
        }
        ps.setTimestamp(++i, Uteis.getDataHoraJDBC(dataInicio, "00:00:00"));
        ps.setTimestamp(++i, Uteis.getDataHoraJDBC(dataFim, "23:59:59"));
        if (convenio.getCodigo() > 0) {
            ps.setInt(++i, convenio.getCodigo());
        }

        ResultSet tabelaResultado = ps.executeQuery();
        while (tabelaResultado.next()) {
            RemessaCancelamentoItemVO itemCancelamento = new RemessaCancelamentoItemVO();

            RemessaVO remessa = new RemessaVO();
            remessa.setDataRegistro(tabelaResultado.getDate("registroremessa"));
            remessa.setCodigo(tabelaResultado.getInt("remessa"));

            RemessaItemVO itemOriginal = new RemessaItemVO();
            itemOriginal.setProps(Uteis.obterMapFromString(tabelaResultado.getString("props")));
            itemOriginal.getMovParcela().setCodigo(tabelaResultado.getInt("movparcela"));
            itemOriginal.getMovParcela().setValorParcela(tabelaResultado.getDouble("valorparcela"));
            itemOriginal.getMovParcela().setDescricao(tabelaResultado.getString("descricao"));
            itemOriginal.getMovParcela().getPessoa().setNome(tabelaResultado.getString("nome"));
            itemOriginal.getMovPagamento().setCodigo(tabelaResultado.getInt("movpagamento"));
            itemOriginal.setCodigo(tabelaResultado.getInt("remessaitem"));
            itemOriginal.setRemessa(remessa);

            itemCancelamento.setItemRemessaCancelar(itemOriginal);

            itensAutorizados.add(itemCancelamento);
        }

        return itensAutorizados;
    }

    @Override
    public List<RemessaCancelamentoItemVO> consultarPorCodigoRemessa(Integer codRemessa, int nivelMontarDados) throws Exception {
        StringBuilder sql = new StringBuilder();

        sql.append("SELECT\n");
        sql.append("  *\n");
        sql.append("FROM remessacancelamentoitem rci\n");
        sql.append("WHERE rci.remessa = ?\n");

        PreparedStatement ps = con.prepareStatement(sql.toString());
        int i = 0;
        ps.setInt(++i, codRemessa);

        ResultSet tabelaResultado = ps.executeQuery();
        return RemessaCancelamentoItem.montarDadosConsulta(tabelaResultado, nivelMontarDados, con);
    }

    @Override
    public List<RemessaCancelamentoItemVO> consultarPorCodigoRemessaItem(Integer codRemessaItem, int nivelMontarDados) throws Exception {
        StringBuilder sql = new StringBuilder();

        sql.append("SELECT\n");
        sql.append("  *\n");
        sql.append("FROM remessacancelamentoitem rci\n");
        sql.append("WHERE rci.remessaitem = ?\n");

        PreparedStatement ps = con.prepareStatement(sql.toString());
        int i = 0;
        ps.setInt(++i, codRemessaItem);

        ResultSet tabelaResultado = ps.executeQuery();
        return RemessaCancelamentoItem.montarDadosConsulta(tabelaResultado, nivelMontarDados, con);
    }

    public void excluirPorCodigoRemessa(int codigoRemessa) throws SQLException {
        String sql = "DELETE FROM remessacancelamentoitem WHERE remessa = "+codigoRemessa;
        try (PreparedStatement ps = con.prepareStatement(sql)) {
            ps.execute();
        }
    }
}

package negocio.facade.jdbc.financeiro;

import br.com.pactosolucoes.comuns.util.Formatador;
import controle.arquitetura.threads.ThreadDemonstrativoFinanceiro;
import negocio.comuns.arquitetura.UsuarioVO;
import negocio.comuns.basico.*;
import negocio.comuns.contrato.MovProdutoVO;
import negocio.comuns.financeiro.*;
import negocio.comuns.financeiro.enumerador.TipoFormaPagto;
import negocio.comuns.financeiro.enumerador.TipoRelatorioDF;
import negocio.comuns.notaFiscal.NotaFiscalVO;
import negocio.comuns.notaFiscal.SituacaoNotaFiscalEnum;
import negocio.comuns.notaFiscal.TipoNotaFiscalEnum;
import negocio.comuns.plano.PlanoVO;
import negocio.comuns.plano.enumerador.TipoProduto;
import negocio.comuns.utilitarias.Calendario;
import negocio.comuns.utilitarias.Ordenacao;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisValidacao;
import negocio.facade.jdbc.acesso.Coletor;
import negocio.facade.jdbc.acesso.LocalAcesso;
import negocio.facade.jdbc.arquitetura.PerfilAcesso;
import negocio.facade.jdbc.arquitetura.SuperEntidade;
import negocio.facade.jdbc.arquitetura.Usuario;
import negocio.facade.jdbc.basico.*;
import negocio.facade.jdbc.contrato.Contrato;
import negocio.facade.jdbc.contrato.ConvenioDesconto;
import negocio.facade.jdbc.contrato.MovProduto;
import negocio.facade.jdbc.notaFiscal.NotaFiscal;
import negocio.facade.jdbc.plano.Plano;
import negocio.facade.jdbc.plano.Produto;
import negocio.facade.jdbc.utilitarias.CacheControl;
import negocio.interfaces.financeiro.GestaoNotasInterfaceFacade;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.util.*;

public class GestaoNotas extends SuperEntidade implements GestaoNotasInterfaceFacade {

    public GestaoNotas() throws Exception {
    }

    public GestaoNotas(Connection conexao) throws Exception {
        super(conexao);
    }

    public List<ItemGestaoNotasTO> consultarFaturamentoCaixa(Integer codEmpresa, Date dataIni, Date dataFim, Integer codPessoa,
                                                             boolean familia, boolean processoAutomatico, boolean consultaNFCe,
                                                             List<PlanoVO> planosFiltrar) throws Exception {
        ClienteTitularDependenteVO clienteTitularDependenteVO = null;
        if ((codPessoa != null) && (codPessoa >0) && (familia)) {
            ClienteTitularDependente clienteTitularDependenteDAO = new ClienteTitularDependente(con);
            clienteTitularDependenteVO = clienteTitularDependenteDAO.consultarPorCodigoPessoa(codPessoa, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
            clienteTitularDependenteDAO = null;
        }
        List<ItemGestaoNotasTO> lista = new ArrayList<ItemGestaoNotasTO>();
        StringBuilder sql = ThreadDemonstrativoFinanceiro.consultaFaturamentoRecebido(codEmpresa, dataIni, dataFim, null, null, false, null, true,
                null, null, codPessoa, familia, clienteTitularDependenteVO,null,null, planosFiltrar);
        obterItensFaturamentoRecebido(lista, sql, codEmpresa, familia, processoAutomatico, consultaNFCe);

        lista = processarItensPorRecibo(lista);
        return lista;
    }

    private List<ItemGestaoNotasTO> processarItensPorRecibo(List<ItemGestaoNotasTO> lista) throws Exception {
        Map<Integer, ItemGestaoNotasTO> mapaRecibos = new HashMap<Integer, ItemGestaoNotasTO>();

        for (ItemGestaoNotasTO item : lista) {
            ItemGestaoNotasTO itemAtualizar = mapaRecibos.get(item.getReciboPagamentoVO().getCodigo());
            if (itemAtualizar == null) {
                mapaRecibos.put(item.getReciboPagamentoVO().getCodigo(), item);
            } else {
                //Quando a nota já foi emitida, no método obterItensFaturamentoRecebido é setado o valor emitido(total) para cada um dos pagamentos.
                //Portanto, não se deve unir o valor dos pagamentos aqui, somente atualizar valoresEFormas e produtosPagos.
                if(!itemAtualizar.getNfseemitida()){
                    itemAtualizar.setValor(itemAtualizar.getValor() + item.getValor());
                }else if(itemAtualizar.getNfseemitida() && itemAtualizar.getNotaFiscalVO() != null){
                    itemAtualizar.setValor(itemAtualizar.getNotaFiscalVO().getValor());
                }else{
                    itemAtualizar.setValor(itemAtualizar.getValorEmitido());
                }
                itemAtualizar.getFormasPagamento().addAll(item.getFormasPagamento());
                for (Map.Entry<Integer, Double> entrySet :item.getValoresEFormas().entrySet()) {
                    if (itemAtualizar.getValoresEFormas().containsKey(entrySet.getKey())){
                        Double valorAtualizado = itemAtualizar.getValoresEFormas().get(entrySet.getKey()) + entrySet.getValue();
                        itemAtualizar.getValoresEFormas().put(entrySet.getKey(), valorAtualizado);
                    } else{
                        itemAtualizar.getValoresEFormas().put(entrySet.getKey(),  entrySet.getValue());
                    }
                }
                itemAtualizar.setProdutosPagos(itemAtualizar.getProdutosPagos() + item.getProdutosPagos());
            }
        }

        List<ItemGestaoNotasTO> novaLista = new ArrayList<ItemGestaoNotasTO>();
        novaLista.addAll(mapaRecibos.values());


        //Caso a nota seja emitida de outra forma, mas tenha o mesmo valor do total do recibo, tratar como
        //uma nota comum, para possibilitar o reenvio e desvinculação pelo gestão de notas
        for(ItemGestaoNotasTO item : novaLista){
            if(item.isEmitidoDeOutraForma()){
                if(Objects.equals(Uteis.arredondarForcando2CasasDecimais(item.getValor()), item.getValorEmitido())){
                    if (!UteisValidacao.emptyNumber(item.getCodNFSeEmitida())) {
                        NotaFiscal notaFiscalDAO = new NotaFiscal(con);
                        item.setNotaFiscalVO(notaFiscalDAO.consultarPorNFSeEmitida(item.getCodNFSeEmitida(), Uteis.NIVELMONTARDADOS_DADOSBASICOS));
                        item.setCodNotaFiscal(item.getNotaFiscalVO().getCodigo());
                        notaFiscalDAO = null;
                    }
                }
            }
        }

        return novaLista;
    }

    private List<ItemGestaoNotasTO> consultarCompetencia(Integer codEmpresa, int ano, String mes, Integer codPessoa,
                                                         boolean familia,Boolean mostrarPorDiaACompetencia, String mesReferenciafinal,
                                                         List<PlanoVO> planosFiltrar, Integer formaPagamento, boolean processoAutomatico,
                                                         TipoRelatorioDF tipoRelatorio) throws Exception {
        long a = System.currentTimeMillis();
        StringBuilder sql = consultaCompetenciaFaturamento("mpag.*", ano, mes, null, null, codEmpresa, codPessoa, familia,
                mostrarPorDiaACompetencia, mesReferenciafinal, planosFiltrar, false, formaPagamento, processoAutomatico, tipoRelatorio);
        List<MovPagamentoVO> pagamentos = obterPagamentosDaCompetenciaFaturamento(sql);
        long b = System.currentTimeMillis();
        Uteis.logarDebug("consultarCompetencia | Pagamentos: " + (b - a) + " milissegundos - " + pagamentos.size() + " itens.");

        a = System.currentTimeMillis();
        sql = consultaCompetenciaFaturamento("mov.*", ano, mes, null, null, codEmpresa, codPessoa, familia,
                mostrarPorDiaACompetencia, mesReferenciafinal, planosFiltrar, false, formaPagamento, processoAutomatico, tipoRelatorio);
        List<MovProdutoVO> produtos = obterProdutosDaCompetenciaFaturamento(sql, familia);
        b = System.currentTimeMillis();
        Uteis.logarDebug("consultarCompetencia | Produtos: " + (b - a) + " milissegundos - " + produtos.size() + " itens.");

        return processarProdutosPagosFaturamentoCompetencia(pagamentos, produtos, codEmpresa, true, familia);
    }

    private List<ItemGestaoNotasTO> consultarFaturamento(Integer codEmpresa, Date dataIni, Date dataFim, Integer codPessoa,
                                                         boolean familia, List<PlanoVO> planosFiltrar, Integer formaPagamento,
                                                         boolean processoAutomatico, TipoRelatorioDF tipoRelatorio) throws Exception {
        StringBuilder sql;
        long a = System.currentTimeMillis();
        sql = consultaCompetenciaFaturamento("mpag.*", 0, null, dataIni, dataFim, codEmpresa, codPessoa, familia,
                null,null, planosFiltrar, false, formaPagamento, processoAutomatico, tipoRelatorio);
        List<MovPagamentoVO> pagamentos = obterPagamentosDaCompetenciaFaturamento(sql);
        long b = System.currentTimeMillis();
        System.out.println("Pagamentos: " + (b - a));

        a = System.currentTimeMillis();
        sql = consultaCompetenciaFaturamento("mov.*", 0, null, dataIni, dataFim, codEmpresa, codPessoa, familia,
                null,null, planosFiltrar, false, formaPagamento, processoAutomatico, tipoRelatorio);
        List<MovProdutoVO> produtos = obterProdutosDaCompetenciaFaturamento(sql, familia);
        b = System.currentTimeMillis();
        System.out.println("Produtos: " + (b - a));

        return processarProdutosPagosFaturamentoCompetencia(pagamentos, produtos, codEmpresa, false, familia);
    }

    private List<ItemGestaoNotasTO> consultarCompetenciaIndependenteQuitacao(Integer codEmpresa, int ano, String mes, Integer codPessoa, boolean familia,
                                                                             Boolean mostrarPorDiaACompetencia, String mesReferenciafinal, List<PlanoVO> planosFiltrar,
                                                                             Integer formaPagamento, boolean processoAutomatico, TipoRelatorioDF tipoRelatorio) throws Exception {
        long a = System.currentTimeMillis();
        StringBuilder sql = consultaCompetenciaFaturamento("mpag.*", ano, mes, null, null, codEmpresa, codPessoa, familia, mostrarPorDiaACompetencia,
                mesReferenciafinal, planosFiltrar, false, formaPagamento, processoAutomatico, tipoRelatorio);
        List<MovPagamentoVO> pagamentos = obterPagamentosDaCompetenciaFaturamento(sql);
        long b = System.currentTimeMillis();
        System.out.println("Pagamentos: " + (b - a) + "ms - " + pagamentos.size() + " itens.");

        a = System.currentTimeMillis();
        sql = consultaCompetenciaFaturamento("mov.*", ano, mes, null, null, codEmpresa, codPessoa, familia, mostrarPorDiaACompetencia,
                mesReferenciafinal, planosFiltrar, true, formaPagamento, processoAutomatico, tipoRelatorio);
        List<MovProdutoVO> produtos = obterProdutosDaCompetenciaFaturamento(sql, familia);
        b = System.currentTimeMillis();
        System.out.println("Produtos: " + (b - a) + "ms - " + produtos.size() + " itens.");

        return processarProdutosCompetenciaIndependenteQuitacao(pagamentos, produtos, codEmpresa, true, familia);
    }

    private List<ItemGestaoNotasTO> processarProdutosPagosFaturamentoCompetencia(List<MovPagamentoVO> pagamentos, List<MovProdutoVO> produtos,
                                                                                 Integer codEmpresa, boolean emissaoCompetencia, boolean familia)  throws Exception {

        Empresa empresaDAO = new Empresa(con);
        EmpresaVO empresaVO = empresaDAO.consultarPorChavePrimaria(codEmpresa, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
        empresaDAO = null;

        boolean usarNomeResponsavelNota;
        ConfiguracaoSistema confDAO = new ConfiguracaoSistema(con);
        usarNomeResponsavelNota = confDAO.usarNomeResponsavelNota();
        confDAO = null;

        boolean empresaEmiteValorTotalCompetencia = empresaVO.isEmiteValorTotalCompetencia();
        String tiposProdutoEmissaoNFSeEmpresa = empresaVO.getTipoProdutoEmissaoNFSe();


        long a = System.currentTimeMillis();
        long totalMillesConsultarPorCodigoPessoa = 0;
        long totalMillesNfseemitida = 0;
        List<ItemGestaoNotasTO> itens = new ArrayList<>();

        Map<Integer, Boolean> mapaContratosParcialmenteEnviados = new HashMap<>();
        Map<Integer, ClienteVO> mapaClientes = new HashMap<>();
        Map<Integer, Boolean> mapaNotasEmitidas = new HashMap<>();
        Map<Integer, MovProdutoVO> mapaMovProduto = new HashMap<>();

        for (MovProdutoVO prod : produtos) {
            if (prod.getSituacao().equals("PG") || prod.getValorParcialmentePago() > 0.0) { //SOMENTE PRODUTOS PAGOS
                mapaMovProduto.put(prod.getCodigo(), prod);
            }
        }

        for (MovPagamentoVO pagamento : pagamentos) {
            Map<Integer, Double> mapaProdutos = new HashMap<Integer, Double>();

            String[] prods = new String[0];
            if (pagamento.getProdutosPagos() != null) {
                prods = pagamento.getProdutosPagos().split("\\|");
            }

            for (String infoProdutoTemp : prods) {
                if (!infoProdutoTemp.isEmpty()) {

                    String[] infoProduto = infoProdutoTemp.split(",");
                    Integer codigoProduto = Integer.parseInt(infoProduto[0]);
                    if (tiposProdutoEmissaoNFSeEmpresa.contains(infoProduto[1])) {
                        if (codigoProduto > 0) {
                            Double valorPagoProduto = Double.parseDouble(infoProduto[3]);
                            if (mapaProdutos.containsKey(codigoProduto)) {
                                Double valorContrato = mapaProdutos.get(codigoProduto) + valorPagoProduto;
                                mapaProdutos.put(codigoProduto, valorContrato);
                            } else {
                                mapaProdutos.put(codigoProduto, valorPagoProduto);
                            }
                        }
                    }
                }
            }

            long millsInicio;
            long millsFim;

            iteracaoProdutos:
            for (Integer codProduto : mapaProdutos.keySet()) {
                MovProdutoVO prod = mapaMovProduto.get(codProduto);
                if (prod != null && prod.getCodigo() != 0) {
                    MovProdutoVO movProdutoVO = new MovProdutoVO();
                    movProdutoVO.setCodigo(codProduto);
                    movProdutoVO.setDescricao(prod.getDescricao());
                    movProdutoVO.setEmpresa(prod.getEmpresa());
                    movProdutoVO.setPessoa(prod.getPessoa());
                    movProdutoVO.setProduto(prod.getProduto());
                    movProdutoVO.setDataLancamento(prod.getDataLancamento());
                    movProdutoVO.setContrato(prod.getContrato());
                    movProdutoVO.setMovPagamento(pagamento);

                    //2500ms;
                    millsInicio = System.currentTimeMillis();

                    ClienteVO clienteVO;

                    if(prod.getPessoa().isEmitirNotaNomeAluno()){
                        clienteVO = mapaClientes.get(prod.getPessoa().getCodigo());
                        if (clienteVO == null) {
                            clienteVO = getFacade().getCliente().consultarPorCodigoPessoa(prod.getPessoa().getCodigo(), Uteis.NIVELMONTARDADOS_GESTAO_NOTAS);
                            mapaClientes.put(prod.getPessoa().getCodigo(), clienteVO);
                        }
                    } else {
                        clienteVO = mapaClientes.get(pagamento.getPessoa().getCodigo());
                        if (clienteVO == null) {
                            clienteVO = getFacade().getCliente().consultarPorCodigoPessoa(pagamento.getPessoa().getCodigo(), Uteis.NIVELMONTARDADOS_GESTAO_NOTAS);
                            mapaClientes.put(pagamento.getPessoa().getCodigo(), clienteVO);
                        }
                    }
                    millsFim = System.currentTimeMillis();
                    totalMillesConsultarPorCodigoPessoa += (millsFim - millsInicio);

                    ItemGestaoNotasTO itemGestaoNotasTO = new ItemGestaoNotasTO();
                    itemGestaoNotasTO.setNome(clienteVO.getNome_Apresentar());
                    itemGestaoNotasTO.setPessoaVO(prod.getPessoa());
                    itemGestaoNotasTO.getFormasPagamento().add(pagamento.getFormaPagamento().getCodigo());

                    if (empresaEmiteValorTotalCompetencia || (prod.getTotalFinal() < mapaProdutos.get(codProduto))) {
                        itemGestaoNotasTO.setValor(prod.getTotalFinal());
                    } else {
                        itemGestaoNotasTO.setValor(mapaProdutos.get(codProduto));
                    }
                    itemGestaoNotasTO.setMovProdutoVO(movProdutoVO);
                    itemGestaoNotasTO.setReciboPagamentoVO(pagamento.getReciboPagamento());
                    itemGestaoNotasTO.setCpf(clienteVO.getPessoa().getCfp());
                    itemGestaoNotasTO.setCodCliente(clienteVO.getCodigo());
                    itemGestaoNotasTO.setMatricula(clienteVO.getMatricula());
                    itemGestaoNotasTO.getValoresEFormas().put(pagamento.getFormaPagamento().getCodigo(),itemGestaoNotasTO.getValor());
                    itemGestaoNotasTO.setClienteTitular(prod.getClienteTitular());

                    Boolean contratoParcialmenteEnviado = mapaContratosParcialmenteEnviados.get(prod.getContrato().getCodigo());
                    if (contratoParcialmenteEnviado == null) {
                        contratoParcialmenteEnviado = getFacade().getNFSeEmitida().contratoParcialmenteEnviado(prod.getContrato().getCodigo());
                        mapaContratosParcialmenteEnviados.put(prod.getContrato().getCodigo(), contratoParcialmenteEnviado);
                    }
                    itemGestaoNotasTO.setParcialmenteemitida(contratoParcialmenteEnviado);

                    millsInicio = System.currentTimeMillis();
                    NFSeEmitidaVO notaEmitidaVO = new NFSeEmitidaVO();
                    Boolean nfSeEmitida = mapaNotasEmitidas.get(prod.getCodigo());
                    if (nfSeEmitida == null) {
                        notaEmitidaVO = getFacade().getNFSeEmitida().consultaPorProduto(prod.getCodigo());
                        if (notaEmitidaVO == null) {
                            notaEmitidaVO = getFacade().getNFSeEmitida().consultaNFSePorReciboPagamentoEProdCartaoChequeEmitido(prod.getCodigo());
                            if(notaEmitidaVO != null) {
                                itemGestaoNotasTO.setEmitidoDeOutraForma(true);
                                nfSeEmitida = true;
                            } else {
                                nfSeEmitida = false;
                            }
                        } else {
                            nfSeEmitida = true;
                        }
                        mapaNotasEmitidas.put(prod.getCodigo(), nfSeEmitida);
                    }
                    itemGestaoNotasTO.setNfseemitida(nfSeEmitida);

                    if (emissaoCompetencia) {
                        try {
                            Date referencia = Uteis.getDate(prod.getMesReferencia(), "MM/yyyy");
                            itemGestaoNotasTO.setDataReferenciaItem(referencia);
                        } catch (Exception e) {
                            itemGestaoNotasTO.setDataReferenciaItem(null);
                        }
                    } else {
                        try {
                            itemGestaoNotasTO.setDataReferenciaItem(movProdutoVO.getDataLancamento());
                        } catch (Exception e) {
                            itemGestaoNotasTO.setDataReferenciaItem(null);
                        }
                    }

                    PessoaVO pessoaVO = new PessoaVO();
                    boolean podeUtilizarNomeResponsavel = false;

                    if(clienteVO != null && clienteVO.getPessoa() != null){
                        pessoaVO = clienteVO.getPessoa();
                        Integer idadeCliente = Uteis.calcularIdadePessoa(Calendario.hoje(), pessoaVO.getDataNasc());
                        podeUtilizarNomeResponsavel = usarNomeResponsavelNota && !pessoaVO.isEmitirNotaNomeAluno() && idadeCliente < 18  && idadeCliente != 0;
                    }

                    if (itemGestaoNotasTO.getNfseemitida()) {
                        if (notaEmitidaVO != null) {
                            itemGestaoNotasTO.setRps(notaEmitidaVO.getIdRps());
                            itemGestaoNotasTO.setSituacaoNotaFiscal(notaEmitidaVO.getSituacaoNotaFiscal());
                            itemGestaoNotasTO.setValorEmitido(notaEmitidaVO.getValor());
                            //só seta o valor da total da nota se não for aba família e a nota também não for família
                            if (!familia && !notaEmitidaVO.isNotaFamilia()) {
                                itemGestaoNotasTO.setValor(notaEmitidaVO.getValor());
                            }
                            itemGestaoNotasTO.setDataEmissao(notaEmitidaVO.getDataEmissao());
                            itemGestaoNotasTO.setNrNotaManual(notaEmitidaVO.getNrNotaManual());

                            if(podeUtilizarNomeResponsavel && !UteisValidacao.emptyString(itemGestaoNotasTO.getNotaFiscalVO().getRazaoSocial()) && itemGestaoNotasTO.getNotaFiscalVO().getNomeCliente() != itemGestaoNotasTO.getNotaFiscalVO().getRazaoSocial()) {
                                itemGestaoNotasTO.setNomeResponsavel(itemGestaoNotasTO.getNotaFiscalVO().getRazaoSocial());
                            }
                        }

                        if (notaEmitidaVO != null && familia) {
                            NotaFiscal notaFiscalDAO = new NotaFiscal(con);
                            NotaFiscalVO notaFiscalVO = notaFiscalDAO.consultarPorNFSeEmitida(notaEmitidaVO.getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS);
                            notaFiscalDAO = null;
                            itemGestaoNotasTO.setSequencialFamilia(notaFiscalVO.getSequencialfamilia());
                            itemGestaoNotasTO.setNotaFiscalVO(notaFiscalVO);
                            itemGestaoNotasTO.setCodNotaFiscal(notaFiscalVO.getCodigo());
                        } else if (notaEmitidaVO != null) {
                            NotaFiscal notaFiscalDAO = new NotaFiscal(con);
                            NotaFiscalVO notaFiscalVO = notaFiscalDAO.consultarPorNFSeEmitida(notaEmitidaVO.getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS);
                            notaFiscalDAO = null;
                            itemGestaoNotasTO.setNotaFiscalVO(notaFiscalVO);
                            itemGestaoNotasTO.setCodNotaFiscal(notaFiscalVO.getCodigo());
                            itemGestaoNotasTO.setCodNFSeEmitida(notaEmitidaVO.getCodigo());
                        }
                    } else if(podeUtilizarNomeResponsavel){
                        if (clienteVO.getPessoaResponsavel() != null && !UteisValidacao.emptyString(clienteVO.getPessoaResponsavel().getNome()) && !clienteVO.getPessoaResponsavel().getCodigo().equals(0)) {
                            itemGestaoNotasTO.setNomeResponsavel(clienteVO.getPessoaResponsavel().getNome());
                        } else if (pessoaVO.isEmitirNotaNomeMae() && !UteisValidacao.emptyString(pessoaVO.getNomeMae())){
                            itemGestaoNotasTO.setNomeResponsavel(pessoaVO.getNomeMae());
                        } else if (!pessoaVO.isEmitirNotaNomeMae() && !UteisValidacao.emptyString(pessoaVO.getNomePai())){
                            itemGestaoNotasTO.setNomeResponsavel(pessoaVO.getNomePai());
                        }
                    }

                    millsFim = System.currentTimeMillis();
                    totalMillesNfseemitida += (millsFim - millsInicio);

                    if (empresaEmiteValorTotalCompetencia) {
                        for (ItemGestaoNotasTO item : itens) {
                            if (item.getNome().equals(itemGestaoNotasTO.getNome())
                                    && item.getMovProdutoVO().getCodigo().equals(itemGestaoNotasTO.getMovProdutoVO().getCodigo())) {
                                continue iteracaoProdutos;
                            }
                        }
                    }
                    itens.add(itemGestaoNotasTO);
                }
            }
        }
        long b = System.currentTimeMillis();
        Uteis.logar(null, "GestaoNotas.processarProdutosPagosFaturamentoCompetencia | consultarPorCodigoPessoa | "+totalMillesConsultarPorCodigoPessoa+ " milissegundos");
        Uteis.logar(null, "GestaoNotas.processarProdutosPagosFaturamentoCompetencia | Nfseemitida | "+totalMillesNfseemitida+ " milissegundos");
        Uteis.logar(null, "Processamento: " + (b - a) + "ms - " + itens.size() + " itens.");
        return itens;
    }

    private List<ItemGestaoNotasTO> processarProdutosCompetenciaIndependenteQuitacao(List<MovPagamentoVO> pagamentos, List<MovProdutoVO> listaProdutos,
                                                                                 Integer codEmpresa, boolean emissaoCompetencia, boolean familia)  throws Exception {

        Empresa empresaDAO = new Empresa(con);
        EmpresaVO empresaVO = empresaDAO.consultarPorChavePrimaria(codEmpresa, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
        empresaDAO = null;

        boolean empresaEmiteValorTotalCompetencia = empresaVO.isEmiteValorTotalCompetencia();

        boolean usarNomeResponsavelNota;
        ConfiguracaoSistema confDAO = new ConfiguracaoSistema(con);
        usarNomeResponsavelNota = confDAO.usarNomeResponsavelNota();
        confDAO = null;

        long a = System.currentTimeMillis();
        List<ItemGestaoNotasTO> itens = new ArrayList<ItemGestaoNotasTO>();

        Map<Integer, Boolean> mapaContratosParcialmenteEnviados = new HashMap<Integer, Boolean>();
        Map<Integer, ClienteVO> mapaClientes = new HashMap<Integer, ClienteVO>();
        Map<Integer, Boolean> mapaNotasEmitidas = new HashMap<Integer, Boolean>();
        Map<Integer, MovPagamentoVO> mapaMovPagamento = new HashMap<Integer, MovPagamentoVO>();
        Map<Integer, Double> mapaProdutos = new HashMap<Integer, Double>();
        List<MovProdutoVO> produtos = new ArrayList<MovProdutoVO>();

        for(MovProdutoVO prod : listaProdutos) {
            String tipos = empresaVO.getTipoProdutoEmissaoNFSe();
            if ((tipos != null) && (!(tipos.trim().equals("")))){
                String[]arrayTipos = tipos.split("\\|");
                for (String tp: arrayTipos){
                    if(prod.getProduto().getTipoProduto().equals(tp)) {
                        produtos.add(prod);
                        break;
                    }
                }
            }
        }

        for (MovPagamentoVO pagamento : pagamentos) {
            String[] prods = new String[0];
            if (pagamento.getProdutosPagos() != null) {
                prods = pagamento.getProdutosPagos().split("\\|");
            }

            for (String infoProdutoTemp : prods) {
                if (!infoProdutoTemp.isEmpty()) {
                    String[] infoProduto = infoProdutoTemp.split(",");
                    Integer codigoProduto = Integer.parseInt(infoProduto[0]);
                    if (empresaVO.getTipoProdutoEmissaoNFSe().contains(infoProduto[1])) {
                        if (codigoProduto > 0) {
                            Double valorPagoProduto = Double.parseDouble(infoProduto[3]);
                            if (mapaProdutos.containsKey(codigoProduto)) {
                                Double valorContrato = mapaProdutos.get(codigoProduto) + valorPagoProduto;
                                mapaProdutos.put(codigoProduto, valorContrato);
                            } else {
                                mapaProdutos.put(codigoProduto, valorPagoProduto);
                            }
                            mapaMovPagamento.put(codigoProduto, pagamento);
                        }
                    }
                }
            }
        }

        for (MovProdutoVO prod : produtos) {
            MovPagamentoVO pagamento = mapaMovPagamento.get(prod.getCodigo());
            if(prod != null && prod.getCodigo() != 0) {
                MovProdutoVO movProdutoVO = new MovProdutoVO();
                movProdutoVO.setMovPagamento(pagamento);
                movProdutoVO.setCodigo(prod.getCodigo());
                movProdutoVO.setDescricao(prod.getDescricao());
                movProdutoVO.setEmpresa(prod.getEmpresa());
                movProdutoVO.setPessoa(prod.getPessoa());
                movProdutoVO.setProduto(prod.getProduto());
                movProdutoVO.setDataLancamento(prod.getDataLancamento());
                movProdutoVO.setContrato(prod.getContrato());

                ClienteVO clienteVO = mapaClientes.get(prod.getPessoa().getCodigo());
                if (clienteVO == null) {
                    clienteVO = getFacade().getCliente().consultarPorCodigoPessoa(prod.getPessoa().getCodigo(), Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA);
                    mapaClientes.put(prod.getPessoa().getCodigo(), clienteVO);
                }

                ItemGestaoNotasTO itemGestaoNotasTO = new ItemGestaoNotasTO();
                itemGestaoNotasTO.setNome(clienteVO.getNome_Apresentar());

                if(!empresaEmiteValorTotalCompetencia && pagamento != null) {
                    if (prod.getTotalFinal() < mapaProdutos.get(prod.getCodigo())) {
                        itemGestaoNotasTO.setValor(prod.getTotalFinal());
                    } else {
                        itemGestaoNotasTO.setValor(mapaProdutos.get(prod.getCodigo()));
                    }
                } else {
                    itemGestaoNotasTO.setValor(prod.getTotalFinal());
                }

                itemGestaoNotasTO.setMovProdutoVO(movProdutoVO);
                itemGestaoNotasTO.setCpf(clienteVO.getPessoa().getCfp());
                itemGestaoNotasTO.setCodCliente(clienteVO.getCodigo());
                itemGestaoNotasTO.setMatricula(clienteVO.getMatricula());

                if(!empresaEmiteValorTotalCompetencia && pagamento != null) {
                    itemGestaoNotasTO.getFormasPagamento().add(pagamento.getFormaPagamento().getCodigo());
                    itemGestaoNotasTO.setReciboPagamentoVO(pagamento.getReciboPagamento());
                    itemGestaoNotasTO.getValoresEFormas().put(pagamento.getFormaPagamento().getCodigo(),itemGestaoNotasTO.getValor());
                }

                itemGestaoNotasTO.setClienteTitular(prod.getClienteTitular());

                Boolean contratoParcialmenteEnviado = mapaContratosParcialmenteEnviados.get(prod.getContrato().getCodigo());
                if (contratoParcialmenteEnviado == null) {
                    contratoParcialmenteEnviado = getFacade().getNFSeEmitida().contratoParcialmenteEnviado(prod.getContrato().getCodigo());
                    mapaContratosParcialmenteEnviados.put(prod.getContrato().getCodigo(), contratoParcialmenteEnviado);
                }
                itemGestaoNotasTO.setParcialmenteemitida(contratoParcialmenteEnviado);

                Boolean nfSeEmitidaVO = mapaNotasEmitidas.get(prod.getCodigo());
                if (nfSeEmitidaVO == null) {
                    nfSeEmitidaVO = getFacade().getNFSeEmitida().existeNFSePorProduto(prod.getCodigo());
                    if (!nfSeEmitidaVO) {
                        nfSeEmitidaVO = getFacade().getNFSeEmitida().existeNFSePorReciboPagamentoProduto(prod.getCodigo());
                    }
                    if (!nfSeEmitidaVO) {
                        nfSeEmitidaVO = getFacade().getNFSeEmitida().existeNFSePorProdutoCartaoChequeDinheiro(prod.getCodigo());
                    }
                    mapaNotasEmitidas.put(prod.getCodigo(), nfSeEmitidaVO);
                }
                itemGestaoNotasTO.setNfseemitida(nfSeEmitidaVO);

                if (emissaoCompetencia) {
                    try {
                        Date referencia = Uteis.getDate(prod.getMesReferencia(), "MM/yyyy");
                        itemGestaoNotasTO.setDataReferenciaItem(referencia);
                    } catch (Exception e) {
                        itemGestaoNotasTO.setDataReferenciaItem(null);
                    }
                } else {
                    try {
                        itemGestaoNotasTO.setDataReferenciaItem(movProdutoVO.getDataLancamento());
                    } catch (Exception e) {
                        itemGestaoNotasTO.setDataReferenciaItem(null);
                    }
                }

                PessoaVO pessoaVO = new PessoaVO();
                boolean podeUtilizarNomeResponsavel = false;

                if (clienteVO != null && clienteVO.getPessoa() != null){
                    pessoaVO = clienteVO.getPessoa();
                    Integer idadeCliente = Uteis.calcularIdadePessoa(Calendario.hoje(), pessoaVO.getDataNasc());
                    podeUtilizarNomeResponsavel = usarNomeResponsavelNota && !pessoaVO.isEmitirNotaNomeAluno() && idadeCliente < 18  && idadeCliente != 0;
                }

                if (itemGestaoNotasTO.getNfseemitida()) {
                    NFSeEmitidaVO nota = getFacade().getNFSeEmitida().consultaPorProduto(prod.getCodigo());
                    if (nota == null) {
                        nota = getFacade().getNFSeEmitida().consultaNFSePorReciboPagamentoProduto(prod.getCodigo());
                        if (nota != null) {
                            itemGestaoNotasTO.setEmitidoDeOutraForma(true);
                        }
                    }
                    if (nota == null) {
                        nota = getFacade().getNFSeEmitida().consultaPorProdutoComCartaoChequeEmitido(prod.getCodigo());
                        if (nota != null) {
                            itemGestaoNotasTO.setEmitidoDeOutraForma(true);
                        }
                    }
                    if (nota != null) {
                        itemGestaoNotasTO.setRps(nota.getIdRps());
                        itemGestaoNotasTO.setSituacaoNotaFiscal(nota.getSituacaoNotaFiscal());
                        itemGestaoNotasTO.setValorEmitido(nota.getValor());
                        if (!familia) {
                            itemGestaoNotasTO.setValor(nota.getValor());
                        }
                        itemGestaoNotasTO.setDataEmissao(nota.getDataEmissao());
                        itemGestaoNotasTO.setNrNotaManual(nota.getNrNotaManual());

                        if(podeUtilizarNomeResponsavel && !UteisValidacao.emptyString(itemGestaoNotasTO.getNotaFiscalVO().getRazaoSocial()) && itemGestaoNotasTO.getNotaFiscalVO().getNomeCliente() != itemGestaoNotasTO.getNotaFiscalVO().getRazaoSocial()) {
                            itemGestaoNotasTO.setNomeResponsavel(itemGestaoNotasTO.getNotaFiscalVO().getRazaoSocial());
                        }

                    }

                    if (nota != null && familia) {
                        NotaFiscal notaFiscalDAO = new NotaFiscal(con);
                        NotaFiscalVO notaFiscalVO = notaFiscalDAO.consultarPorNFSeEmitida(nota.getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS);
                        notaFiscalDAO = null;
                        itemGestaoNotasTO.setSequencialFamilia(notaFiscalVO.getSequencialfamilia());
                        itemGestaoNotasTO.setNotaFiscalVO(notaFiscalVO);
                        itemGestaoNotasTO.setCodNotaFiscal(notaFiscalVO.getCodigo());
                    } else if (nota != null) {
                        NotaFiscal notaFiscalDAO = new NotaFiscal(con);
                        NotaFiscalVO notaFiscalVO = notaFiscalDAO.consultarPorNFSeEmitida(nota.getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS);
                        notaFiscalDAO = null;
                        itemGestaoNotasTO.setNotaFiscalVO(notaFiscalVO);
                        itemGestaoNotasTO.setCodNotaFiscal(notaFiscalVO.getCodigo());
                        itemGestaoNotasTO.setCodNFSeEmitida(nota.getCodigo());
                    }
                } else if(podeUtilizarNomeResponsavel){
                    if (clienteVO.getPessoaResponsavel() != null && !UteisValidacao.emptyString(clienteVO.getPessoaResponsavel().getNome()) && !clienteVO.getPessoaResponsavel().getCodigo().equals(0)) {
                        itemGestaoNotasTO.setNomeResponsavel(clienteVO.getPessoaResponsavel().getNome());
                    } else if (pessoaVO.isEmitirNotaNomeMae() && !UteisValidacao.emptyString(pessoaVO.getNomeMae())){
                        itemGestaoNotasTO.setNomeResponsavel(pessoaVO.getNomeMae());
                    } else if (!pessoaVO.isEmitirNotaNomeMae() && !UteisValidacao.emptyString(pessoaVO.getNomePai())){
                        itemGestaoNotasTO.setNomeResponsavel(pessoaVO.getNomePai());
                    }
                }

                itens.add(itemGestaoNotasTO);
            }
        }

        long b = System.currentTimeMillis();
        System.out.println("Processamento: " + (b - a) + "ms - " + itens.size() + " itens.");
        return itens;
    }

    private List<ItemGestaoNotasTO> consultarReceita(Integer codEmpresa, Date dataIni, Date dataFim, Integer codPessoa, boolean familia, List<PlanoVO> planosFiltrar) throws Exception {

        Empresa empresaDAO = new Empresa(con);
        EmpresaVO empresaVO = empresaDAO.consultarPorChavePrimaria(codEmpresa, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
        empresaDAO = null;

        List<ItemGestaoNotasTO> lista = new ArrayList<ItemGestaoNotasTO>();
        ClienteTitularDependenteVO clienteTitularDependenteVO = null;
        if ((codPessoa != null) && (codPessoa >0) && (familia)) {
            clienteTitularDependenteVO = getFacade().getClienteTitularDependente().consultarPorCodigoPessoa(codPessoa, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
        }
        StringBuilder sql = ThreadDemonstrativoFinanceiro.consultaPagamentoEmCartaoCredito(codEmpresa, dataIni, dataFim, false, null, true,
                codPessoa, familia, clienteTitularDependenteVO, null, empresaVO.isUsarDataOriginalCompensacaoNFSe(), planosFiltrar, "", null, false);
        obterItensReceita(empresaVO, lista, sql, TipoFormaPagto.CARTAOCREDITO, familia);

        sql = ThreadDemonstrativoFinanceiro.consultaPagamentosEmCheque(codEmpresa, dataIni, dataFim, false, null, true, codPessoa, familia,
                clienteTitularDependenteVO, null,null, empresaVO.isUsarDataOriginalCompensacaoNFSe(), planosFiltrar, "", null, false);
        obterItensReceita(empresaVO, lista, sql, TipoFormaPagto.CHEQUE, familia);

        sql = ThreadDemonstrativoFinanceiro.consultaPagamentosDiferentesDeChequeECartaoCredito(codEmpresa, dataIni, dataFim, false, null, true, codPessoa, familia, clienteTitularDependenteVO, null, planosFiltrar, null);
        obterItensReceita(empresaVO, lista, sql, TipoFormaPagto.AVISTA, familia);
        Ordenacao.ordenarLista(lista, "nome");
        return lista;
    }

    private List<MovProdutoVO> obterProdutosDaCompetenciaFaturamento(StringBuilder sql, boolean familia) throws Exception {
        try (PreparedStatement ps = getCon().prepareStatement(sql.toString())) {
            ResultSet rs = ps.executeQuery();
            List<MovProdutoVO> lista = new ArrayList<>();
            while (rs.next()) {
                MovProdutoVO obj = MovProduto.montarDados(rs, Uteis.NIVELMONTARDADOS_DADOSBASICOS, con);
                if (familia) {
                    obj.setClienteTitular(rs.getInt("clienteTitular"));
                }
                lista.add(obj);
            }
            return lista;
        }
    }

    private List<MovPagamentoVO> obterPagamentosDaCompetenciaFaturamento(StringBuilder sql) throws Exception {
        PreparedStatement ps = getCon().prepareStatement(sql.toString());
        ResultSet rs = ps.executeQuery();
        return MovPagamento.montarDadosConsulta(rs, Uteis.NIVELMONTARDADOS_DADOSBASICOS, con, false);
    }

    private void obterItensFaturamentoRecebido(List<ItemGestaoNotasTO> lista, StringBuilder sql, Integer codEmpresa, Boolean familia,
                                               boolean processoAutomatico, boolean consultaNFCe) throws Exception {

        Empresa empresaDAO = new Empresa(con);
        EmpresaVO empresaVO = empresaDAO.consultarPorChavePrimaria(codEmpresa, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
        empresaDAO = null;

        ConfiguracaoNotaFiscal configuracaoNotaFiscalDAO = new ConfiguracaoNotaFiscal(con);
        ConfiguracaoNotaFiscalVO configuracaoNotaFiscalVO = configuracaoNotaFiscalDAO.consultarPorChavePrimaria(empresaVO.getConfiguracaoNotaFiscalNFCe().getCodigo(), Uteis.NIVELMONTARDADOS_MINIMOS);
        configuracaoNotaFiscalDAO = null;

        Usuario usuarioDAO = new Usuario(con);
        UsuarioVO usuarioRecorrencia = usuarioDAO.getUsuarioRecorrencia();
        usuarioDAO = null;

        boolean usarNomeResponsavelNota;
        ConfiguracaoSistema confDAO = new ConfiguracaoSistema(con);
        usarNomeResponsavelNota = confDAO.usarNomeResponsavelNota();
        confDAO = null;

        boolean emitirNotaSomenteRecorrencia;
        boolean gerarNFSeContaCorrente;
        String tipoProdutoEmissao;

        if (consultaNFCe) {
            emitirNotaSomenteRecorrencia = empresaVO.isEmitirNFCeSomenteRecorrencia();
            gerarNFSeContaCorrente = false;
            familia = false;
            tipoProdutoEmissao = empresaVO.getTipoProdutoEmissaoNFCe();

            if (UteisValidacao.emptyString(empresaVO.getTipoProdutoEmissaoNFCe())) {
                StringBuilder tipos = new StringBuilder();
                for (TipoProduto tipoProduto : TipoProduto.values()) {
                    if (tipos.toString().equals("")) {
                        tipos.append(tipoProduto.getCodigo());
                    } else {
                        tipos.append("|").append(tipoProduto.getCodigo());
                    }
                }
                tipoProdutoEmissao = tipos.toString();
            }

        } else {

            emitirNotaSomenteRecorrencia = empresaVO.isEmitirNotaSomenteRecorrencia();
            gerarNFSeContaCorrente = empresaVO.isGerarNFSeContaCorrente();
            tipoProdutoEmissao = empresaVO.getTipoProdutoEmissaoNFSe();

        }

        Date d1 = Calendario.hoje();
        PreparedStatement ps = getCon().prepareStatement(sql.toString());
        ResultSet rs = ps.executeQuery();
        Date d2 = Calendario.hoje();
//        Uteis.logar(null, "Tempo obterItensFaturamentoRecebido SQL: " + (d2.getTime() - d1.getTime()));

        Date d1Cons = Calendario.hoje();
        while (rs.next()) {
            ItemGestaoNotasTO item = new ItemGestaoNotasTO();
            item.setNome(rs.getString("nomepagador"));
            item.setValor(rs.getDouble("valor"));
            item.setRps(rs.getInt("rps"));
            item.setDataReferenciaItem(rs.getTimestamp("dataReferenciaItem"));
            item.setSituacaoNotaFiscal(SituacaoNotaFiscalEnum.obterPorCodigo(rs.getInt("situacaonfse")));
            item.setValorEmitido(rs.getDouble("valornfse"));
            item.setDataEmissao(rs.getTimestamp("dataEmissaonfse"));
            item.setProdutosPagos(rs.getString("produtospagos"));
            item.setNrNotaManual(rs.getString("nrnotamanual"));
            item.setCodCliente(rs.getInt("codcliente"));
            item.setCodColaborador(rs.getInt("codcolaborador"));

            if (consultaNFCe) {
                //Possibilita encontrar os status de emissão de NFe, e também das NFC-e já emitidas,
                // caso a empresa antes emitisse NFC-e pelo gestão de NFC-e e depois passou a emitir NFe
                item.setCodNFCeEmitida(rs.getInt("codNFCe"));
                item.setCodNotaFiscal(rs.getInt("codigonotafiscalNFCE"));
                item.setNfseemitida((rs.getString("codNFCe") != null));
                if (configuracaoNotaFiscalVO.getTipoNotaFiscal().equals(TipoNotaFiscalEnum.NFE) &&
                        (UteisValidacao.emptyNumber(item.getCodNFCeEmitida()) && UteisValidacao.emptyNumber(item.getCodNotaFiscal()))){
                    item.setCodNFSeEmitida(rs.getInt("nfse"));
                    item.setCodNotaFiscal(rs.getInt("codigonotafiscal"));
                    item.setNfseemitida((rs.getString("nfse") != null));
                }
            } else {
                item.setCodNFSeEmitida(rs.getInt("nfse"));
                item.setCodNotaFiscal(rs.getInt("codigonotafiscal"));
                item.setNfseemitida((rs.getString("nfse") != null));
            }

            ClienteVO clienteVO = new ClienteVO();
            PessoaVO pessoaVO = new PessoaVO();
            boolean podeUtilizarNomeResponsavel = false;

            if(!UteisValidacao.emptyNumber(item.getCodCliente())){
                clienteVO = getFacade().getCliente().consultarPorChavePrimaria(item.getCodCliente(), Uteis.NIVELMONTARDADOS_DADOSENTIDADESUBORDINADAS);
                if (clienteVO != null && clienteVO.getPessoa() != null){
                    pessoaVO = clienteVO.getPessoa();
                    Integer idadeCliente = Uteis.calcularIdadePessoa(Calendario.hoje(), pessoaVO.getDataNasc());
                    podeUtilizarNomeResponsavel = usarNomeResponsavelNota && !pessoaVO.isEmitirNotaNomeAluno() && idadeCliente < 18  && idadeCliente != 0;
                }
            }

            if (!UteisValidacao.emptyNumber(item.getCodNotaFiscal())) {
                NotaFiscal notaFiscalDAO = new NotaFiscal(con);
                item.setNotaFiscalVO(notaFiscalDAO.consultarPorChavePrimaria(item.getCodNotaFiscal(), Uteis.NIVELMONTARDADOS_DADOSBASICOS));
                notaFiscalDAO = null;

                if(podeUtilizarNomeResponsavel && !UteisValidacao.emptyString(item.getNotaFiscalVO().getRazaoSocial()) && item.getNotaFiscalVO().getNomeCliente() != item.getNotaFiscalVO().getRazaoSocial()) {
                    item.setNomeResponsavel(item.getNotaFiscalVO().getRazaoSocial());
                }
            } else if(podeUtilizarNomeResponsavel){
                if (clienteVO.getPessoaResponsavel() != null && !UteisValidacao.emptyString(clienteVO.getPessoaResponsavel().getNome()) && !clienteVO.getPessoaResponsavel().getCodigo().equals(0)) {
                    item.setNomeResponsavel(clienteVO.getPessoaResponsavel().getNome());
                } else if (pessoaVO.isEmitirNotaNomeMae() && !UteisValidacao.emptyString(pessoaVO.getNomeMae())){
                    item.setNomeResponsavel(pessoaVO.getNomeMae());
                } else if (!pessoaVO.isEmitirNotaNomeMae() && !UteisValidacao.emptyString(pessoaVO.getNomePai())){
                    item.setNomeResponsavel(pessoaVO.getNomePai());
                }
            }

            item.setCodUsuarioResponsavelPagamento(rs.getInt("codresponsavelpagamento"));
            if (processoAutomatico && emitirNotaSomenteRecorrencia && !usuarioRecorrencia.getCodigo().equals(item.getCodUsuarioResponsavelPagamento())) {
                continue;
            }

            //TICKET #5867
            boolean existeChequeEmitido = rs.getBoolean("existeChequeEmitido");
            boolean existeCartaoEmitido = rs.getBoolean("existeCartaoEmitido");
            boolean existeDinheiroEmitido = rs.getBoolean("existeDinheiroEmitido");

            boolean nfseEmitidaEncontrada = false;
            if (existeChequeEmitido || existeCartaoEmitido || existeDinheiroEmitido) {
                item.setNfseemitida(true);
                item.setEmitidoDeOutraForma(true);

                Integer codigoMovPagamento = rs.getInt("codigoMovPagamento");
                NFSeEmitida nfSeEmitidaDAO = new NFSeEmitida(con);
                NFSeEmitidaVO nfSeEmitidaVO = nfSeEmitidaDAO.consultaPorMovPagamentoComCartaoChequeEmitido(codigoMovPagamento);
                nfSeEmitidaDAO = null;
                if (nfSeEmitidaVO != null) {
                    item.setCodNFSeEmitida(nfSeEmitidaVO.getCodigo());
                    item.setValor(nfSeEmitidaVO.getValor());
                    item.setRps(nfSeEmitidaVO.getIdRps());
                    item.setSituacaoNotaFiscal(nfSeEmitidaVO.getSituacaoNotaFiscal());
                    item.setValorEmitido(nfSeEmitidaVO.getValor());
                    item.setDataEmissao(nfSeEmitidaVO.getDataEmissao());
                    item.setNrNotaManual(nfSeEmitidaVO.getNrNotaManual());
                }
            } else {
                String[] produtos = new String[0];
                if (item.getProdutosPagos() != null) {
                    produtos = item.getProdutosPagos().split("\\|");
                }
                for (String infoProdutoTemp : produtos) {
                    if (!infoProdutoTemp.isEmpty()) {
                        String[] infoProduto = infoProdutoTemp.split(",");
                        if (tipoProdutoEmissao.contains(infoProduto[1])) {
                            NFSeEmitidaVO nfSeEmitidaVO = getFacade().getNFSeEmitida().consultaPorProduto(Integer.parseInt(infoProduto[0]));
                            if (nfSeEmitidaVO != null && !UteisValidacao.emptyNumber(nfSeEmitidaVO.getCodigo())) {
                                item.setValor(nfSeEmitidaVO.getValor());
                                item.setNfseemitida(true);
                                item.setCodNFSeEmitida(nfSeEmitidaVO.getCodigo());
                                item.setEmitidoDeOutraForma(true);
                                item.setRps(nfSeEmitidaVO.getIdRps());
                                item.setSituacaoNotaFiscal(nfSeEmitidaVO.getSituacaoNotaFiscal());
                                item.setValorEmitido(nfSeEmitidaVO.getValor());
                                item.setDataEmissao(nfSeEmitidaVO.getDataEmissao());
                                item.setNrNotaManual(nfSeEmitidaVO.getNrNotaManual());
                                nfseEmitidaEncontrada = true;
                                break;
                            }
                        }
                    }
                }
                if(!nfseEmitidaEncontrada && item.getNfseemitida() && !UteisValidacao.emptyString(rs.getString("nfse"))) {
                    NFSeEmitidaVO nfSeEmitidaVO = getFacade().getNFSeEmitida().consultarPorChavePrimaria(Integer.valueOf(rs.getString("nfse")));
                    if (nfSeEmitidaVO != null && !UteisValidacao.emptyNumber(nfSeEmitidaVO.getCodigo())) {
                        item.setValor(nfSeEmitidaVO.getValor());
                        item.setCodNFSeEmitida(nfSeEmitidaVO.getCodigo());
                        item.setRps(nfSeEmitidaVO.getIdRps());
                        item.setSituacaoNotaFiscal(nfSeEmitidaVO.getSituacaoNotaFiscal());
                        item.setValorEmitido(nfSeEmitidaVO.getValor());
                        item.setDataEmissao(nfSeEmitidaVO.getDataEmissao());
                        item.setNrNotaManual(nfSeEmitidaVO.getNrNotaManual());
                    }
                }
            }



            if(item.getCodCliente() == null || item.getCodCliente() == 0) {
                item.setCpf(rs.getString("cpfcolaborador"));
                item.setMatricula("COLABORADOR");
            } else if(podeUtilizarNomeResponsavel && clienteVO.getPessoaResponsavel() != null && !UteisValidacao.emptyString(clienteVO.getPessoaResponsavel().getCfp()) && !UteisValidacao.emptyNumber(clienteVO.getPessoaResponsavel().getCodigo())){
                    item.setCpf(clienteVO.getPessoaResponsavel().getCfp());
            } else {
                item.setCpf(rs.getString("cpf"));
                item.setMatricula(rs.getString("matriculacliente"));
            }
            if (item.getCodColaborador() == 0 && (item.getCodCliente() == 0||item.getCodCliente() == null)){
                item.setMatricula("CONSUMIDOR");
            }
            if(familia){
                item.setClienteTitular(rs.getInt("clienteTitular"));

                item.setSequencialFamilia(rs.getInt("sequencialfamilia"));
                if (!UteisValidacao.emptyNumber(item.getSequencialFamilia())) {
                    NotaFiscal notaFiscalDAO = new NotaFiscal(con);
                    item.setNotaFiscalVO(notaFiscalDAO.consultarPorSequencialFamilia(item.getSequencialFamilia(), Uteis.NIVELMONTARDADOS_DADOSBASICOS));
                    item.setCodNotaFiscal(item.getNotaFiscalVO().getCodigo());
                    notaFiscalDAO = null;
                }
            }
            item.getFormasPagamento().add(rs.getInt("codformapagamento"));
            item.setParcialmenteemitida(rs.getBoolean("emitidaantes"));
            ReciboPagamentoVO reciboPagamentoVO = new ReciboPagamentoVO();
            reciboPagamentoVO.setCodigo(rs.getInt("recibopagamento"));

            // INICIO
            // removido de NFSE demora e não identificado nenhum local que precisa das informações alem do código do recibo
            if (consultaNFCe) {
                ReciboPagamento reciboPagamentoDAO = new ReciboPagamento(con);
                reciboPagamentoVO = reciboPagamentoDAO.consultarPorChavePrimaria(reciboPagamentoVO.getCodigo(),Uteis.NIVELMONTARDADOS_TELACONSULTAESPECIAL);
                reciboPagamentoDAO = null;
            }
            // by Luiz Felipe
            // FIM

            item.setReciboPagamentoVO(reciboPagamentoVO);

            boolean processarProduto  = item.processarProdutosPagos(tipoProdutoEmissao, gerarNFSeContaCorrente, empresaVO);

            if (consultaNFCe && !configuracaoNotaFiscalVO.getTipoNotaFiscal().equals(TipoNotaFiscalEnum.NFE)) {
                item.setId_NFCe(rs.getInt("id_nfce"));
                item.setNfseemitida(rs.getBoolean("nfceEmitidaAntes"));
                item.setParcialmenteemitida(false);
                if (!UteisValidacao.emptyNumber(item.getCodNFCeEmitida())) {
                    NotaFiscalConsumidorEletronica notaDAO = new NotaFiscalConsumidorEletronica(con);
                    NotaFiscalConsumidorEletronicaVO eletronicaVO = notaDAO.consultarPorChavePrimaria(item.getCodNFCeEmitida(), Uteis.NIVELMONTARDADOS_DADOSBASICOS);
                    notaDAO = null;
                    if (!UteisValidacao.emptyNumber(eletronicaVO.getCodigo())) {
                        processarProduto = true;
                        item.setValor(eletronicaVO.getValorTotal());
                    }
                }
            }

            if(processarProduto) {
                item.getValoresEFormas().put(rs.getInt("codformapagamento"), rs.getDouble("valor"));
                lista.add(item);
            }
        }

        Date d2Cons = Calendario.hoje();
//        Uteis.logar(null, "Tempo obterItensFaturamentoRecebido Montar: " + (d2Cons.getTime() - d1Cons.getTime()));
    }

    private void obterItensFaturamentoRecebidoFamilia(List<ItemGestaoNotasTO> lista, StringBuilder sql, String tipoProdutoEmissaoNFSe, boolean gerarNFSeContaCorrente) throws Exception {
        PreparedStatement ps = getCon().prepareStatement(sql.toString());

        ResultSet rs = ps.executeQuery();
        while (rs.next()) {
            ItemGestaoNotasTO item = new ItemGestaoNotasTO();
            item.setNome(rs.getString("nomepagador"));
            item.setValor(rs.getDouble("valor"));
            item.setNfseemitida((rs.getString("nfse") != null));
            item.setProdutosPagos(rs.getString("produtospagos"));
            item.setCodCliente(rs.getInt("codcliente"));

            if(item.getCodCliente() == null || item.getCodCliente() == 0) {
                item.setCpf(rs.getString("cpfcolaborador"));
                item.setMatricula("COLABORADOR");
                item.setCodColaborador(rs.getInt("codcolaborador"));
            } else {
                item.setCpf(rs.getString("cpf"));
                item.setMatricula(rs.getString("matriculacliente"));
            }


            item.getFormasPagamento().add(rs.getInt("codformapagamento"));
            item.setParcialmenteemitida(rs.getBoolean("emitidaantes"));
            ReciboPagamentoVO reciboPagamentoVO = new ReciboPagamentoVO();
            reciboPagamentoVO.setCodigo(rs.getInt("recibopagamento"));
            item.setReciboPagamentoVO(reciboPagamentoVO);

            item.processarProdutosPagos(tipoProdutoEmissaoNFSe, gerarNFSeContaCorrente, null);
            item.getValoresEFormas().put(rs.getInt("codformapagamento"), item.getValor());
            lista.add(item);
        }

    }

    private void obterItensReceita(EmpresaVO empresaVO, List<ItemGestaoNotasTO> lista, StringBuilder sql,
                                   TipoFormaPagto tipoFormaPagto, boolean familia) throws Exception {

        String tipoProdutoEmissao = empresaVO.getTipoProdutoEmissaoNFSe();
        boolean gerarNFSeContaCorrente = empresaVO.isGerarNFSeContaCorrente();

        CartaoCredito cartaoCredito = new CartaoCredito(getCon());
        Cheque cheque = new Cheque(getCon());
        MovPagamento movPagamento = new MovPagamento(getCon());

        boolean usarNomeResponsavelNota;
        ConfiguracaoSistema confDAO = new ConfiguracaoSistema(con);
        usarNomeResponsavelNota = confDAO.usarNomeResponsavelNota();
        confDAO = null;

        final UsuarioVO usuarioVOIdZero = new UsuarioVO();

        PreparedStatement ps = getCon().prepareStatement(sql.toString());
        ResultSet rs = ps.executeQuery();
        while (rs.next()) {
            ItemGestaoNotasTO item = new ItemGestaoNotasTO();
            item.setNome(rs.getString("nomepagador"));
            item.setNfseemitida((rs.getString("nfse") != null));
            item.setRps(rs.getInt("rps"));
            item.setDataReferenciaItem(rs.getTimestamp("dataReferenciaItem"));
            item.setSituacaoNotaFiscal(SituacaoNotaFiscalEnum.obterPorCodigo(rs.getInt("situacaonfse")));
            item.setValorEmitido(rs.getDouble("valornfse"));
            item.setDataEmissao(rs.getTimestamp("dataemissaonfse"));
            item.setNrNotaManual(rs.getString("nrnotamanual"));
            item.setProdutosPagos(rs.getString("produtospagos"));
            item.getFormasPagamento().add(rs.getInt("codformapagamento"));
            item.setCodCliente(rs.getInt("codcliente"));
            item.setParcialmenteemitida(rs.getBoolean("emitidaantes"));
            item.setNomeResponsavelEmissaoNota(rs.getString("nome_usuario_logado"));

            item.setCodNFSeEmitida(rs.getInt("nfse"));
            item.setCodNotaFiscal(rs.getInt("codigonotafiscal"));
            item.setValor(!UteisValidacao.emptyNumber(item.getCodNFSeEmitida()) ? rs.getDouble("valornfse") : rs.getDouble("valor"));

            ClienteVO clienteVO = new ClienteVO();
            PessoaVO pessoaVO = new PessoaVO();
            boolean podeUtilizarNomeResponsavel = false;

            if(!UteisValidacao.emptyNumber(item.getCodCliente())){
                clienteVO = getFacade().getCliente().consultarPorChavePrimaria(item.getCodCliente(), Uteis.NIVELMONTARDADOS_DADOSENTIDADESUBORDINADAS);
                if (clienteVO != null && clienteVO.getPessoa() != null){
                    pessoaVO = clienteVO.getPessoa();
                    Integer idadeCliente = Uteis.calcularIdadePessoa(Calendario.hoje(), pessoaVO.getDataNasc());
                    podeUtilizarNomeResponsavel = usarNomeResponsavelNota && !pessoaVO.isEmitirNotaNomeAluno() && idadeCliente < 18  && idadeCliente != 0;
                }
            }


            if (!UteisValidacao.emptyNumber(item.getCodNotaFiscal())) {
                NotaFiscal notaFiscalDAO = new NotaFiscal(con);
                item.setNotaFiscalVO(notaFiscalDAO.consultarPorChavePrimaria(item.getCodNotaFiscal(), Uteis.NIVELMONTARDADOS_ADMINISTRATIVORUNNER_PROCESSARNOTAS));
                notaFiscalDAO = null;

                if(podeUtilizarNomeResponsavel && !UteisValidacao.emptyString(item.getNotaFiscalVO().getRazaoSocial()) && item.getNotaFiscalVO().getNomeCliente() != item.getNotaFiscalVO().getRazaoSocial()) {
                    item.setNomeResponsavel(item.getNotaFiscalVO().getRazaoSocial());
                }
            } else if(podeUtilizarNomeResponsavel){
                if (clienteVO.getPessoaResponsavel() != null && !UteisValidacao.emptyString(clienteVO.getPessoaResponsavel().getNome()) && !clienteVO.getPessoaResponsavel().getCodigo().equals(0)) {
                    item.setNomeResponsavel(clienteVO.getPessoaResponsavel().getNome());
                } else if (pessoaVO.isEmitirNotaNomeMae() && !UteisValidacao.emptyString(pessoaVO.getNomeMae())){
                    item.setNomeResponsavel(pessoaVO.getNomeMae());
                } else if (!pessoaVO.isEmitirNotaNomeMae() && !UteisValidacao.emptyString(pessoaVO.getNomePai())){
                    item.setNomeResponsavel(pessoaVO.getNomePai());
                }
            }

            if(item.getCodCliente() == null || item.getCodCliente() == 0) {
                item.setCpf(rs.getString("cpfcolaborador"));
                item.setMatricula("COLABORADOR");
                item.setCodColaborador(rs.getInt("codcolaborador"));
            } else {
                item.setCpf(rs.getString("cpf"));
                item.setMatricula(rs.getString("matriculacliente"));
            }

            if (familia){
                item.setClienteTitular(rs.getInt("clienteTitular"));

                item.setSequencialFamilia(rs.getInt("sequencialfamilia"));
                if (!UteisValidacao.emptyNumber(item.getSequencialFamilia())) {
                    NotaFiscal notaFiscalDAO = new NotaFiscal(con);
                    item.setNotaFiscalVO(notaFiscalDAO.consultarPorSequencialFamilia(item.getSequencialFamilia(), Uteis.NIVELMONTARDADOS_DADOSBASICOS));
                    item.setCodNotaFiscal(item.getNotaFiscalVO().getCodigo());
                    notaFiscalDAO = null;
                }
            }

            MovPagamentoVO movPagamentoPesquisar = new MovPagamentoVO();
            if (tipoFormaPagto.equals(TipoFormaPagto.AVISTA)) {
                MovPagamentoVO movPagamentoVO = movPagamento.consultarPorChavePrimaria(rs.getInt("codigoMovPagamento"), Uteis.NIVELMONTARDADOS_DADOSBASICOS);
                item.setMovPagamentoVO(movPagamentoVO);
                movPagamentoPesquisar = movPagamentoVO;
            } else if (tipoFormaPagto.equals(TipoFormaPagto.CARTAOCREDITO)) {
                CartaoCreditoVO cartaoCreditoVO = cartaoCredito.consultarPorChavePrimaria(rs.getInt("codigoCartaoCredito"), Uteis.NIVELMONTARDADOS_ADMINISTRATIVORUNNER_PROCESSARNOTAS);

                montarDadosUsuarioResponsavelPagamento(cartaoCreditoVO.getMovpagamento(), con);
                montarDadosDaEmpresaNoMovPagamento(cartaoCreditoVO.getMovpagamento(), con);
                montarDadosUsuarioMovConta(cartaoCreditoVO.getMovConta(), con, usuarioVOIdZero);

                item.setCartaoCreditoVO(cartaoCreditoVO);
                movPagamentoPesquisar = cartaoCreditoVO.getMovpagamento();
            } else if (tipoFormaPagto.equals(TipoFormaPagto.CHEQUE)) {
                ChequeVO chequeVO = cheque.consultarPorChavePrimaria(rs.getInt("codigoCheque"), Uteis.NIVELMONTARDADOS_TODOS);
                item.setChequeVO(chequeVO);
                movPagamentoPesquisar.setCodigo(chequeVO.getMovPagamento());
            }

//            if (gerarNFSeContaCorrente && (movPagamentoPesquisar != null && movPagamentoPesquisar.getCodigo() !=  null)) {
//                MovPagamentoVO movPagamentoOrigem = movPagamento.consultarPorChavePrimaria(movPagamentoPesquisar.getCodigo(), Uteis.NIVELMONTARDADOS_TELACONSULTA);
//                boolean existe = movPagamento.existeNotaEmitidaMovPagamentoOrigem(movPagamentoOrigem.getMovPagamentoOrigemCredito());
//                if (!item.getNfseemitida() && existe) {
//                    item.setNfseemitida(existe);
//                }
//            }

            boolean processarProduto = false;

            if (item.getNfseemitida() && !familia) {
                item.setValor(rs.getDouble("valornfse"));
                processarProduto = true;
            } else {
                processarProduto = item.processarProdutosPagos(tipoProdutoEmissao, gerarNFSeContaCorrente, null);
            }

            if (!familia) {
                String[] produtos = new String[0];
                Double valorTotalProdutosPagos = 0.0;
                if (item.getProdutosPagos() != null) {
                    produtos = item.getProdutosPagos().split("\\|");
                }
                for (String infoProdutoTemp : produtos) {
                    if (!infoProdutoTemp.isEmpty()) {
                        String[] infoProduto = infoProdutoTemp.split(",");
                        if (tipoProdutoEmissao.contains(infoProduto[1])) {
                            Double valorPagoProduto = Double.valueOf(infoProduto[3]);
                            NFSeEmitidaVO nfSeEmitidaVO = getFacade().getNFSeEmitida().consultaPorProduto(Integer.parseInt(infoProduto[0]));
                            if (nfSeEmitidaVO != null && !UteisValidacao.emptyNumber(nfSeEmitidaVO.getCodigo())) {
                                item.setNfseemitida(true);
                                item.setEmitidoDeOutraForma(true);
                                item.setRps(nfSeEmitidaVO.getIdRps());
                                valorTotalProdutosPagos = nfSeEmitidaVO.getValor();
                                break;
                            }
                            valorTotalProdutosPagos = (valorTotalProdutosPagos + valorPagoProduto);
                        }
                    }
                }
                if(UteisValidacao.emptyNumber(item.getCodNFSeEmitida())){
                    item.setValor(valorTotalProdutosPagos);
                }
            }

            item.getValoresEFormas().put(rs.getInt("codformapagamento"), item.getValor());
            if (processarProduto && (!UteisValidacao.emptyNumber(item.getCodCliente()) || !UteisValidacao.emptyNumber(item.getCodColaborador()))) {
                lista.add(item);
            }
        }
    }

    private void montarDadosUsuarioResponsavelPagamento (MovPagamentoVO obj, Connection con) throws Exception {
        Usuario usuarioDao = new Usuario(con);
        try {
            obj.setResponsavelPagamento(usuarioDao.consultarPorChavePrimaria(obj.getResponsavelPagamento().getCodigo(), Uteis.NIVELMONTARDADOS_TODOS));
        } finally {
            usuarioDao = null;
        }
    }

    private void montarDadosDaEmpresaNoMovPagamento (MovPagamentoVO obj, Connection con) throws Exception {
        Empresa empresaDao = new Empresa(con);
        try {
            obj.setEmpresa(empresaDao.consultarPorChavePrimaria(obj.getEmpresa().getCodigo(), Uteis.NIVELMONTARDADOS_TODOS));
        } finally {
            empresaDao = null;
        }
    }

    private void montarDadosUsuarioMovConta (MovContaVO obj, Connection con, UsuarioVO usuarioVOIdZero) throws Exception {
        if (obj.getUsuarioVO() != null && UteisValidacao.emptyNumber(obj.getUsuarioVO().getCodigo())) {
            obj.setUsuarioVO(usuarioVOIdZero);
            return;
        } else if (obj.getUsuarioVO() == null) {
            obj.setUsuarioVO(new UsuarioVO());
            return;
        }
        Usuario usuarioDao = new Usuario(con);
        try {
            obj.setUsuarioVO(usuarioDao.consultarPorChavePrimaria(obj.getUsuarioVO().getCodigo(), Uteis.NIVELMONTARDADOS_TODOS));
        } finally {
            usuarioDao = null;
        }
    }

    private List<ItemGestaoNotasTO> processarItens(List<ItemGestaoNotasTO> lista, Integer codFormaPagamento, TipoRelatorioDF tipoRelatorio) throws Exception {
        List<ItemGestaoNotasTO> listaSaida = new ArrayList<ItemGestaoNotasTO>();

        for (ItemGestaoNotasTO item : lista) {
            boolean valorMaiorQueZero = item.getValor() > 0.0;

            boolean codFormaPagamentoValido = (codFormaPagamento == 0);
            for (int codFP : item.getFormasPagamento()) {
                codFormaPagamentoValido = codFormaPagamentoValido || codFP == codFormaPagamento;
            }

            boolean estaPago = false;
            switch (tipoRelatorio) {
                case COMPETENCIA:
                case FATURAMENTO:
                    if (item.getReciboPagamentoVO() != null && item.getReciboPagamentoVO().getCodigo() != null && item.getReciboPagamentoVO().getCodigo() != 0) {
                        estaPago = true;
                    }
                    break;
                case FATURAMENTO_DE_CAIXA:
                    estaPago = true;
                    break;
                case RECEITA:
                    estaPago = true;
                    break;
                case COMPETENCIA_INDEPENDENTE_QUITACAO:
                    estaPago = true;
                    break;
            }

            if (valorMaiorQueZero && codFormaPagamentoValido && estaPago) {
                listaSaida.add(item);
            }
        }
        return listaSaida;
    }


    private StringBuilder consultaCompetenciaFaturamento(String colunas, int anoReferencia, String mesAnoReferencia, Date dataIni, Date dataFim,
                                                         int empresa, Integer codPessoa, boolean familia, Boolean mostrarPorDiaACompetencia,
                                                         String mesAnoReferenciaFinal, List<PlanoVO> planosFiltrar, boolean protudosEmAberto,
                                                         Integer formaPagamento, boolean processoAutomatico, TipoRelatorioDF tipoRelatorio) throws Exception {

        String codPlanos = obterListaCodigosPlanos(planosFiltrar);

        StringBuilder sql = new StringBuilder();
        sql.append("SELECT\n");
        sql.append("  DISTINCT\n ").append(colunas).append("\n");
        if (familia){
            sql.append(" ,familia.clienteTitular \n");
        }

        if(protudosEmAberto) {
            sql.append("FROM movprodutoparcela mpp \n");
            sql.append("  INNER JOIN movparcela mp on mpp.movparcela = mp.codigo\n");
        } else {
            sql.append("FROM movpagamento mpag\n");
            sql.append("  INNER JOIN pagamentomovparcela pmp ON mpag.codigo = pmp.movpagamento\n");
            sql.append("  INNER JOIN movprodutoparcela mpp ON pmp.movparcela = mpp.movparcela\n");
        }
        sql.append("  INNER JOIN movproduto mov ON mpp.movproduto = mov.codigo\n");
        sql.append("  INNER JOIN produto prod ON mov.produto = prod.codigo\n");
        sql.append("  INNER JOIN pessoa pe ON pe.codigo = mov.pessoa\n");


        if (!UteisValidacao.emptyString(codPlanos)) {
            sql.append(" INNER JOIN contrato con ON con.codigo = mov.contrato and con.plano in (").append(codPlanos).append(") \n");
        }

        if (familia){
            sql.append("INNER JOIN cliente cli on cli.pessoa = pe.codigo \n");
            sql.append(" INNER JOIN clientetitulardependente familia ON familia.cliente = cli.codigo \n");
        }
        sql.append("WHERE prod.tipoProduto NOT IN (").append(MovProduto.PRODUTOS_IGNORADOS).append(")\n");
        if ((codPessoa != null)  && (codPessoa > 0)) {

            if (familia) {
                ClienteTitularDependenteVO clienteTitularDependenteVO = getFacade().getClienteTitularDependente().consultarPorCodigoPessoa(codPessoa, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
                if (clienteTitularDependenteVO.getCodigo().equals(clienteTitularDependenteVO.getClienteTitular().getCodigo())){
                    // A pessoa é um titular
                    sql.append(" AND familia.clienteTitular = ").append(clienteTitularDependenteVO.getCodigo());
                }else{
                    // A pessoa é um dependente
                    sql.append(" AND familia.cliente = ").append(clienteTitularDependenteVO.getClienteVO().getCodigo());
                }
            }else{
                sql.append(" and pe.codigo = ").append(codPessoa).append("\n");
            }
        }
        sql.append("AND (mov.TotalFinal > 0)\n");
        sql.append("AND mov.movpagamentocc IS NULL\n"); // isso para não considerar produtos de pagamento de Débito em conta

        if (!protudosEmAberto &&
                !UteisValidacao.emptyNumber(formaPagamento)) {
            sql.append("AND mpag.formapagamento = ").append(formaPagamento).append(" \n");
        }

        if (tipoRelatorio != null &&
                tipoRelatorio.equals(TipoRelatorioDF.COMPETENCIA) &&
                processoAutomatico) {
            sql.append("AND not exists(select codigo from nfseemitida where movproduto = mov.codigo) \n");
        }

        if (dataIni == null && dataFim == null) {
            if (mostrarPorDiaACompetencia != null) {
                if (mostrarPorDiaACompetencia) {
                    String diaInial = mesAnoReferencia.substring(0,2);
                    String mesAno = mesAnoReferencia.substring(3);
                    Integer diaComparar = Integer.parseInt(diaInial);
                    String diaFinal = mesAnoReferenciaFinal.substring(0,2);
                    Integer diaFinalComparar = Integer.parseInt(diaFinal);
                    sql.append("AND mov.anoReferencia = ").append(anoReferencia).append("\n");
                    sql.append("AND mov.mesReferencia = '").append(mesAno).append("'\n");
                    Integer ultimoDiaMes = Calendario.ultimoDiaMes(Calendario.getDate("dd/MM/yyy", mesAnoReferenciaFinal));
                    if (diaComparar >= 28) {
                        sql.append("AND EXTRACT(DAY FROM(mov.datalancamento)) >= '").append(28).append("'\n");
                    }else if (diaFinalComparar.equals(ultimoDiaMes)){
                        sql.append("AND EXTRACT(DAY FROM(mov.datalancamento)) >= '").append(diaInial).append("'\n");
                        sql.append("AND EXTRACT(DAY FROM(mov.datalancamento)) <= '").append(31).append("'\n");
                    } else {
                        sql.append("AND EXTRACT(DAY FROM(mov.datalancamento)) >= '").append(diaInial).append("'\n");
                        sql.append("AND EXTRACT(DAY FROM(mov.datalancamento)) <= '").append(diaFinal).append("'\n");
                    }
                } else {
                    sql.append("AND mov.anoReferencia = ").append(anoReferencia).append("\n");
                    sql.append("AND mov.mesReferencia = '").append(mesAnoReferencia).append("'\n");
                }
            }else{
                sql.append("AND mov.anoReferencia = ").append(anoReferencia).append("\n");
                sql.append("AND mov.mesReferencia = '").append(mesAnoReferencia).append("'\n");
            }

        } else {
            sql.append("AND mpag.datalancamento >= '").append(Uteis.getDateTime(dataIni, 0, 0, 0)).append("'\n");
            sql.append("AND mpag.datalancamento <= '").append(Uteis.getDateTime(dataFim, 23, 59, 59)).append("'\n");
        }
        sql.append("AND mov.empresa = ").append(empresa).append("\n");
        sql.append("AND mov.situacao != 'CA'\n");
        return sql;
    }

    @Override
    public List<ItemGestaoNotasTO> obterDadosNFCe(
            TipoRelatorioDF tipoRelatorio, Integer codEmpresa, Integer formaPagamento,
            Date dataIniLanc, Date dataFimLanc, Integer codPessoa, boolean processoAutomatico, List<PlanoVO> planosFiltrar) throws Exception {
        return obterDadosGeral(tipoRelatorio, codEmpresa, formaPagamento, dataIniLanc, dataFimLanc, codPessoa, false, false, processoAutomatico, true, planosFiltrar);
    }

    @Override
    public List<ItemGestaoNotasTO> obterDados(
            TipoRelatorioDF tipoRelatorio, Integer codEmpresa, Integer formaPagamento,
            Date dataIniLanc, Date dataFimLanc, Integer codPessoa, boolean familia, boolean mostrarPorDiaACompetencia, boolean processoAutomatico, List<PlanoVO> planosFiltrar) throws Exception {
        return obterDadosGeral(tipoRelatorio, codEmpresa, formaPagamento, dataIniLanc, dataFimLanc, codPessoa, familia, mostrarPorDiaACompetencia, processoAutomatico, false, planosFiltrar);
    }

    private List<ItemGestaoNotasTO> obterDadosGeral(
            TipoRelatorioDF tipoRelatorio, Integer codEmpresa, Integer formaPagamento,
            Date dataIniLanc, Date dataFimLanc, Integer codPessoa, boolean familia, boolean mostrarPorDiaACompetencia,
            boolean processoAutomatico, boolean consultaNFCe, List<PlanoVO> planosFiltrar) throws Exception {

        List<ItemGestaoNotasTO> obj = new ArrayList<ItemGestaoNotasTO>();
        try {
            CacheControl.reuseReference();
            CacheControl.toggleCache(Empresa.class, true); // possui
            CacheControl.toggleCache(ConfiguracaoNotaFiscal.class, true); // possui
            CacheControl.toggleCache(FormaPagamento.class, true); // possui
            CacheControl.toggleCache(Pessoa.class, true); // possui
            CacheControl.toggleCache(Banco.class, true); // possui
            CacheControl.toggleCache(MovPagamento.class, true); // possui
            CacheControl.toggleCache(OperadoraCartao.class, true); // posusi
            CacheControl.toggleCache(ConvenioCobranca.class, true); // possui
            CacheControl.toggleCache(Usuario.class, true); // possui
            CacheControl.toggleCache(Cidade.class, true); // possui
            CacheControl.toggleCache(Estado.class, true); // possui
            CacheControl.toggleCache(Pais.class, true); // possui
            CacheControl.toggleCache(Colaborador.class, true); // possui
            CacheControl.toggleCache(PerfilAcesso.class, true); // possui
            CacheControl.toggleCache(Profissao.class, true); // possui
            CacheControl.toggleCache(Produto.class, true); // possui
            CacheControl.toggleCache(ConvenioDesconto.class, true); // possui
            CacheControl.toggleCache(Contrato.class, true); // possui
            CacheControl.toggleCache(Plano.class, true); // possui
            CacheControl.toggleCache(GrauInstrucao.class, true);// possui
            CacheControl.toggleCache(LocalAcesso.class, true);
            CacheControl.toggleCache(Coletor.class, true);

            String competenciaInicial = "";
            String competenciaFinal = "";
            Integer ano;
            switch (tipoRelatorio) {
                case COMPETENCIA:
                    if (mostrarPorDiaACompetencia) {
                        if (processoAutomatico) {
                            //buscar no automatico sempre o primeiro dia e ultimo
                            competenciaInicial = Formatador.formatarData(Uteis.obterPrimeiroDiaMes(dataIniLanc), "dd/MM/yyyy");
                            competenciaFinal = Formatador.formatarData(Uteis.obterUltimoDiaMes(dataFimLanc), "dd/MM/yyyy");
                        } else {
                            competenciaInicial = Formatador.formatarData(dataIniLanc, "dd/MM/yyyy");
                            competenciaFinal = Formatador.formatarData(dataFimLanc, "dd/MM/yyyy");
                        }
                    } else {
                        competenciaInicial = Formatador.formatarData(dataIniLanc, "MM/yyyy");
                    }
                    ano = Calendario.getInstance(dataIniLanc).get(Calendar.YEAR);
                    obj = consultarCompetencia(codEmpresa, ano, competenciaInicial, codPessoa, familia, mostrarPorDiaACompetencia,
                            competenciaFinal, planosFiltrar, formaPagamento, processoAutomatico, tipoRelatorio);
                    break;
                case FATURAMENTO:
                    obj = consultarFaturamento(codEmpresa, dataIniLanc, dataFimLanc, codPessoa, familia, planosFiltrar,
                            formaPagamento, processoAutomatico, tipoRelatorio);
                    break;
                case FATURAMENTO_DE_CAIXA:
                    obj = consultarFaturamentoCaixa(codEmpresa, dataIniLanc, dataFimLanc, codPessoa, familia, processoAutomatico, consultaNFCe, planosFiltrar);
                    break;
                case RECEITA:
                    obj = consultarReceita(codEmpresa, dataIniLanc, dataFimLanc, codPessoa, familia, planosFiltrar);
                    break;
                case COMPETENCIA_INDEPENDENTE_QUITACAO:
                    if (mostrarPorDiaACompetencia) {
                        if (processoAutomatico) {
                            //buscar no automatico sempre o primeiro dia e ultimo
                            competenciaInicial = Formatador.formatarData(Uteis.obterPrimeiroDiaMes(dataIniLanc), "dd/MM/yyyy");
                            competenciaFinal = Formatador.formatarData(Uteis.obterUltimoDiaMes(dataFimLanc), "dd/MM/yyyy");
                        } else {
                            competenciaInicial = Formatador.formatarData(dataIniLanc, "dd/MM/yyyy");
                            competenciaFinal = Formatador.formatarData(dataFimLanc, "dd/MM/yyyy");
                        }
                    } else {
                        competenciaInicial = Formatador.formatarData(dataIniLanc, "MM/yyyy");
                    }
                    ano = Calendario.getInstance(dataIniLanc).get(Calendar.YEAR);
                    obj = consultarCompetenciaIndependenteQuitacao(codEmpresa, ano, competenciaInicial, codPessoa, familia, mostrarPorDiaACompetencia,
                            competenciaFinal, planosFiltrar, formaPagamento, processoAutomatico, tipoRelatorio);
                    break;
            }
            //Verifica a forma de pagamento;
            obj = processarItens(obj, formaPagamento, tipoRelatorio);
        } finally {
            CacheControl.clear();
        }
        return obj;
    }

    private String obterListaCodigosPlanos(List<PlanoVO> planosFiltrar) {
        String codigoPlanos = "";
        if (!UteisValidacao.emptyList(planosFiltrar)) {
            codigoPlanos = Uteis.retornarCodigos(planosFiltrar);
        }
        return codigoPlanos;
    }
}

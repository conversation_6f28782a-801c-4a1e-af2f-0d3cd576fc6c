package negocio.facade.jdbc.financeiro;

import negocio.comuns.financeiro.FinancialAccountKobanaVO;
import negocio.comuns.utilitarias.Uteis;
import negocio.facade.jdbc.arquitetura.SuperEntidade;
import negocio.interfaces.financeiro.FinancialAccountKobanaInterfaceFacade;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.util.ArrayList;
import java.util.List;

public class FinancialAccountKobana extends SuperEntidade implements FinancialAccountKobanaInterfaceFacade {

    public FinancialAccountKobana() throws Exception {
        super();
    }

    public FinancialAccountKobana(Connection con) throws Exception {
        super(con);
    }

    public static FinancialAccountKobanaVO montarDados(ResultSet dadosSQL) throws Exception {
        FinancialAccountKobanaVO obj = new FinancialAccountKobanaVO();
        obj.setCodigo(dadosSQL.getInt("codigo"));
        obj.setEmpresa(dadosSQL.getInt("empresa"));
        obj.setUid(dadosSQL.getString("uid"));
        obj.setFinancial_provider_slug(dadosSQL.getString("financial_provider_slug"));
        obj.setCodIntegracaoKobana(dadosSQL.getInt("codIntegracaoKobana"));
        obj.setAtivo(dadosSQL.getBoolean("ativo"));
        obj.setCreated_at(dadosSQL.getTimestamp("created_at"));

        return obj;
    }

    public void incluir(FinancialAccountKobanaVO obj) throws Exception {
        try {
            String sql = "INSERT INTO FinancialAccountKobana(empresa, uid, created_At, ativo, financial_provider_slug, codIntegracaoKobana) VALUES (?, ?, ?, ?, ?, ?)";
            try (PreparedStatement sqlInserir = con.prepareStatement(sql)) {
                sqlInserir.setInt(1, obj.getEmpresa());
                sqlInserir.setString(2, obj.getUid());
                sqlInserir.setTimestamp(3, Uteis.getDataJDBCTimestamp(obj.getCreated_at()));
                sqlInserir.setBoolean(4, obj.isAtivo());
                sqlInserir.setString(5, obj.getFinancial_provider_slug());
                sqlInserir.setInt(6, obj.getCodIntegracaoKobana());
                sqlInserir.execute();
            }
        } catch (Exception e) {
            throw e;
        }
    }

    public List<FinancialAccountKobanaVO> consultarByCodIntegracaoKobana(int codIntegracaoKobana) throws Exception {
        String sql = "SELECT * FROM FinancialAccountKobana WHERE codIntegracaoKobana = " + codIntegracaoKobana;
        List<FinancialAccountKobanaVO> lista = new ArrayList<>();
        try (PreparedStatement ps = con.prepareStatement(sql)) {
            try (ResultSet rs = ps.executeQuery()) {
                while (rs.next()) {
                    lista.add(montarDados(rs));
                }
            }
        }
        return lista;
    }

}

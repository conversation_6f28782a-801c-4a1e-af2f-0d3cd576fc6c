package negocio.facade.jdbc.financeiro;

import br.com.pactosolucoes.comuns.json.JSONMapper;
import br.com.pactosolucoes.enumeradores.BancoOpenBankEnum;
import negocio.comuns.basico.EmpresaVO;
import negocio.comuns.crm.GenericoTO;
import negocio.comuns.financeiro.ComportamentoConta;
import negocio.comuns.financeiro.ContaVO;
import negocio.comuns.financeiro.TipoContaVO;
import negocio.comuns.financeiro.enumerador.TipoES;
import negocio.comuns.utilitarias.Calendario;
import negocio.comuns.utilitarias.ConsistirException;
import negocio.comuns.utilitarias.Ordenacao;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisValidacao;
import negocio.facade.jdbc.arquitetura.SuperEntidade;
import negocio.interfaces.financeiro.ContaInterfaceFacade;
import org.json.JSONArray;
import org.json.JSONObject;
import servicos.pactobank.dto.ExtratoMovimentoZWDTO;
import servicos.pactobank.dto.SaldoZWDTO;
import servicos.propriedades.PropsService;
import servicos.util.ExecuteRequestHttpService;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class Conta extends SuperEntidade implements ContaInterfaceFacade {

    private static String sqlInsert = "insert into conta (empresa, tipoconta, "
            + "descricao, banco, numero, \"numerodv\", agencia, \"agenciadv\", "
            + "ativa, mostrarnobi, observacao, descricaocurta, mostrardredemonstrativo, bancoopenbank)"
            + "values (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)";
    private static String sqlUpdate = "UPDATE conta SET empresa=?, tipoconta=?, "
            + "descricao=?, banco=?, observacao=?, numero=?, numerodv=?, "
            + "agencia=?, agenciadv=?, ativa=?, mostrarnobi=?, descricaocurta = ?, mostrardredemonstrativo = ?, bancoopenbank = ? "
            + " WHERE  codigo=?";

    public Conta() throws Exception {
        super();
        setIdEntidade("Contrato");
    }

    public Conta(Connection con) throws Exception {
        super(con);
        setIdEntidade("Contrato");
    }

    @Override
    public List<ContaVO> consultar(ContaVO filtro, boolean somenteAtivos, int nivelMontarDados, String chaveZW) throws Exception {
        StringBuilder sql = new StringBuilder();
        sql.append("SELECT conta.codigo, empresa, tipoconta, conta.descricao, ");
        sql.append("banco, numero, \"numerodv\", agencia, \"agenciadv\", ");
        sql.append("tipoconta.descricao as descricaotipoconta, banco.nome as nomebanco, banco.codigobanco, ");
        sql.append("ativa, mostrarnobi, observacao, descricaocurta, conta.mostrardredemonstrativo, bancoopenbank ");
        sql.append("FROM conta ");
        sql.append("left join tipoconta on tipoconta.codigo = conta.tipoconta ");
        sql.append("left join banco on banco.codigo = conta.banco ");

        boolean flag = false;
        if (!UteisValidacao.emptyNumber(filtro.getEmpresa().getCodigo())) {
            sql.append("where empresa = ? ");
            flag = true;
        }
        if (!UteisValidacao.emptyNumber(filtro.getBanco().getCodigo())) {
            sql.append(flag ? " and " : " where ").append(" banco = ? ");
            flag = true;
        }
        if (!UteisValidacao.emptyNumber(filtro.getTipoConta().getCodigo())) {
            sql.append(flag ? " and " : " where ").append(" tipoconta = ? ");
            flag = true;
        }
        if (somenteAtivos) {
            sql.append(flag ? " and ativa is true " : " where  ativa is true ");
        }

        sql.append(" ORDER BY conta.descricao ");


        try (PreparedStatement pst = con.prepareStatement(sql.toString())) {
            int i = 1;
            if (!UteisValidacao.emptyNumber(filtro.getEmpresa().getCodigo())) {
                pst.setInt(i++, filtro.getEmpresa().getCodigo());
            }
            if (!UteisValidacao.emptyNumber(filtro.getBanco().getCodigo())) {
                pst.setInt(i++, filtro.getBanco().getCodigo());
            }
            if (!UteisValidacao.emptyNumber(filtro.getTipoConta().getCodigo())) {
                pst.setInt(i++, filtro.getTipoConta().getCodigo());
            }
            try (ResultSet tabelaResultado = pst.executeQuery()) {
                return montarDadosConsulta(tabelaResultado, nivelMontarDados, chaveZW);
            }
        }
    }

    public List<ContaVO> consultarContasCaixaAberto(int empresa, int caixaAberto, boolean somenteAtivos) throws Exception {
        StringBuilder sql = new StringBuilder();
        sql.append("SELECT conta.codigo, empresa, tipoconta, conta.descricao, ");
        sql.append("banco, numero, \"numerodv\", agencia, \"agenciadv\", ");
        sql.append("tipoconta.descricao as descricaotipoconta, banco.nome as nomebanco, ");
        sql.append("banco.codigobanco, ativa, mostrarnobi, observacao, descricaocurta ");
        sql.append("FROM conta ");
        sql.append("left join tipoconta on tipoconta.codigo = conta.tipoconta ");
        sql.append("left join banco on banco.codigo = conta.banco ");
        if (!UteisValidacao.emptyNumber( caixaAberto)) {
            sql.append(" inner join  caixaconta  on conta.codigo = caixaconta.conta");
        }
        StringBuilder sqlWhere = new StringBuilder();
        boolean addAnd = false;
        if (!UteisValidacao.emptyNumber(empresa)) {
            sqlWhere.append(" empresa = ? ");
            addAnd = true;
        }
        if (!UteisValidacao.emptyNumber( caixaAberto)) {
            sqlWhere.append((addAnd ? " and " : "") +  " caixaconta.caixa = ? ");
            addAnd = true;
        }
        if (somenteAtivos) {
            sqlWhere.append((addAnd ? " and " : "") +  "  ativa is true ");
            addAnd = true;
        }
        if(sqlWhere.length() > 0){
            sql.append(" where ").append(sqlWhere.toString());
        }

        sql.append(" ORDER BY conta.descricao ");

        try (PreparedStatement pst = con.prepareStatement(sql.toString())) {
            int i = 1;
            if (!UteisValidacao.emptyNumber(empresa)) {
                pst.setInt(i++, empresa);
            }
            if (!UteisValidacao.emptyNumber(caixaAberto)) {
                pst.setInt(i++, caixaAberto);
            }

            try (ResultSet tabelaResultado = pst.executeQuery()) {
                return (montarDadosConsulta(tabelaResultado,
                        Uteis.NIVELMONTARDADOS_TELACONSULTA, ""));
            }
        }
    }

    public static List<ContaVO> montarDadosConsulta(ResultSet tabelaResultado,
            int nivelMontarDados, String chaveZW) throws Exception {
        List<ContaVO> vetResultado = new ArrayList<ContaVO>();
        while (tabelaResultado.next()) {
            ContaVO obj = new ContaVO();
            obj = montarDados(tabelaResultado, nivelMontarDados, chaveZW);
            vetResultado.add(obj);
        }
        return vetResultado;
    }
    private PreparedStatement getPS() throws SQLException {
        StringBuilder sql = new StringBuilder();
        sql.append(" SELECT c.codigo,c.descricao,tc.descricao as tipoConta,b.nome as banco\n");
        sql.append(" FROM Conta c\n");
        sql.append(" LEFT JOIN Banco b ON  b.codigo = c.banco\n");
        sql.append(" LEFT JOIN TipoConta tc ON  tc.codigo = c.tipoconta\n");
        sql.append(" WHERE tc.comportamento <> "+ComportamentoConta.OPENBANK.getCodigo()); // Tipo openbank apenas inserido pela integração openbank.
        return con.prepareStatement(sql.toString());
    }
    public List consultarParaImpressao(String filtro, String ordem, String campoOrdenacao, int i) throws SQLException {
        List lista;
        try (ResultSet rs = getPS().executeQuery()) {
            lista = new ArrayList();
            while (rs.next()) {
                ContaVO contaVO = new ContaVO();
                String geral = rs.getString("codigo") + rs.getString("descricao") + rs.getString("tipoConta") + rs.getString("banco");
                if (geral.toLowerCase().contains(filtro.toLowerCase())) {
                    contaVO.setCodigo(rs.getInt("codigo"));
                    contaVO.setDescricao(rs.getString("descricao"));
                    contaVO.getBanco().setNome(rs.getString("banco"));
                    contaVO.getTipoConta().setDescricao(rs.getString("tipoConta"));
                    lista.add(contaVO);
                }
            }
        }
        if (campoOrdenacao.equals("Código")) {
            Ordenacao.ordenarLista(lista, "codigo");
        } else if (campoOrdenacao.equals("Descrição da Conta")) {
            Ordenacao.ordenarLista(lista, "descricao");
        } else if (campoOrdenacao.equals("Tipo de Conta")) {
            Ordenacao.ordenarLista(lista, "tipoConta_Apresentar");
        } else if (campoOrdenacao.trim().equals("Banco")) {
        Ordenacao.ordenarLista(lista, "banco_Apresentar");
        }

        if (ordem.contains("desc")) {
            Collections.reverse(lista);
        }
        return lista;

    }
    public String consultarJSON() throws Exception {
        StringBuilder json;
        boolean dados;

        try (ResultSet rs = getPS().executeQuery()) {
            json = new StringBuilder();
            json.append("{\"aaData\":[");
            dados = false;
            while (rs.next()) {
                dados = true;
                json.append("[\"").append(rs.getString("codigo")).append("\",");
                json.append("\"").append(Uteis.normalizarStringJSON(rs.getString("descricao"))).append("\",");
                json.append("\"").append(Uteis.normalizarStringJSON(rs.getString("tipoConta"))).append("\",");
                json.append("\"").append(Uteis.normalizarStringJSON(rs.getString("banco"))).append("\"],");
            }
        }
        if (dados) {
            json.deleteCharAt(json.toString().length() - 1);
        }
        json.append("]}");
        return json.toString();
    }
    public static ContaVO montarDados(ResultSet dadosSQL, int nivelMontarDados, String chaveZW)
            throws Exception {

        ContaVO obj = new ContaVO();
        obj.setNovoObj(false);
        obj.setCodigo(dadosSQL.getInt("codigo"));
        obj.getBanco().setCodigo(dadosSQL.getInt("banco"));
        obj.getTipoConta().setCodigo(dadosSQL.getInt("tipoconta"));
        obj.getEmpresa().setCodigo(dadosSQL.getInt("empresa"));
        obj.setDescricao(dadosSQL.getString("descricao"));
        try {
            obj.setDescricaoCurta(dadosSQL.getString("descricaocurta"));
        } catch (Exception ignored) {

        }

        obj.setNumero(dadosSQL.getString("numero"));
        obj.setNumeroDV(dadosSQL.getString("numerodv"));
        obj.setAgencia(dadosSQL.getString("agencia"));
        obj.setAgenciaDV(dadosSQL.getString("agenciadv"));
        obj.setAtiva(dadosSQL.getBoolean("ativa"));
        obj.setMostrarBi(dadosSQL.getBoolean("mostrarnobi"));
        obj.setObservacao(dadosSQL.getString("observacao"));
        try {
            obj.setMostrarDREDemonstrativo(dadosSQL.getBoolean("mostrardredemonstrativo"));
        }catch(Exception ignore){
        }

        try {
            obj.setBancoOpenBankEnum(BancoOpenBankEnum.getFromCodigo(dadosSQL.getInt("bancoopenbank")));
        }catch(Exception ignore){
        }
        if  (Uteis.NIVELMONTARDADOS_DADOSBASICOS == nivelMontarDados) {
            return obj;
        }

        if  (Uteis.NIVELMONTARDADOS_DADOSENTIDADESPRINCIPAIS == nivelMontarDados) {
            obj.getBanco().setNome(dadosSQL.getString("nomebanco"));
            obj.getTipoConta().setDescricao(dadosSQL.getString("descricaotipoconta"));
            return obj;

        }
        if (nivelMontarDados == Uteis.NIVELMONTARDADOS_TELACONSULTA) {
            obj.getBanco().setNome(dadosSQL.getString("nomebanco"));
            try {
                obj.getBanco().setCodigoBanco(dadosSQL.getInt("codigobanco"));
            }catch (Exception ignore){}
            obj.getTipoConta().setDescricao(dadosSQL.getString("descricaotipoconta"));
            if (obj.isContaIntegracaoOpenBank() && !UteisValidacao.emptyString(chaveZW)) {
                obj.setSaldoAtual(buscarSaldoContaStoneOpenBank(chaveZW, obj.getEmpresa().getCodigo()));
            }else{
                obj.setSaldoAtual(getFacade().getFinanceiro().getConta().saldoAte(obj.getCodigo(), Calendario.hoje()));
            }
        }

        if (nivelMontarDados == Uteis.NIVELMONTARDADOS_TODOS) {
            obj.setTipoConta(getFacade().getFinanceiro().getTipoConta().consultarPorChavePrimaria(obj.getTipoConta().getCodigo(), Uteis.NIVELMONTARDADOS_TODOS));
            obj.setSaldoAtual(getFacade().getFinanceiro().getConta().saldoAte(obj.getCodigo(), Calendario.hoje()));
        }
        return obj;
    }

    @Override
    public void incluir(ContaVO obj) throws Exception {
        try (PreparedStatement sqlInserir = prepararIncluir(obj)) {
            sqlInserir.execute();
        }
        obj.setCodigo(obterValorChavePrimariaCodigo());
            obj.setNovoObj(false);
        }

    @Override
    public void alterar(ContaVO obj) throws Exception {
        try {
            con.setAutoCommit(false);
            try (PreparedStatement sqlAlterar = prepararAlterar(obj)) {
                sqlAlterar.execute();
            }
            obj.setNovoObj(false);
            con.commit();
        } catch (Exception e) {
            con.rollback();
            throw e;
        } finally {
            con.setAutoCommit(true);
        }
    }

    @Override
    public void excluir(ContaVO obj) throws Exception {
        try {
            con.setAutoCommit(false);
            excluir(getIdEntidade());
            String sql = "DELETE FROM conta WHERE codigo = ?";
            try (PreparedStatement sqlExcluir = con.prepareStatement(sql)) {
                sqlExcluir.setInt(1, obj.getCodigo());
                sqlExcluir.execute();
            }
            con.commit();
        } catch (Exception e) {
            con.rollback();
            throw e;
        } finally {
            con.setAutoCommit(true);
        }
    }

    @Override
    public ContaVO consultarPorChavePrimaria(Integer codigoPrm, int nivelMontarDados) throws Exception {
        consultar(getIdEntidade(), false);
        String sql = "SELECT * FROM conta WHERE codigo = ?";
        try (PreparedStatement sqlConsultar = con.prepareStatement(sql)) {
            sqlConsultar.setInt(1, codigoPrm.intValue());
            try (ResultSet tabelaResultado = sqlConsultar.executeQuery()) {
                if (!tabelaResultado.next()) {
                    throw new ConsistirException("Dados Não Encontrados ( Conta ).");
                }
                return (montarDados(tabelaResultado, nivelMontarDados, ""));
            }
        }
    }

    @Override
    public ContaVO consultarPorDescricao(String descricao, int nivelMontarDados) throws Exception {
        consultar(getIdEntidade(), false);
        String sql = "SELECT * FROM conta WHERE descricao = ?";
        try (PreparedStatement sqlConsultar = con.prepareStatement(sql)) {
            sqlConsultar.setString(1, descricao);
            try (ResultSet tabelaResultado = sqlConsultar.executeQuery()) {
                if (!tabelaResultado.next()) {
                    Uteis.logarDebug( "# Dados Não Encontrados ( Conta )");
                    return null;
                }else{
                    return (montarDados(tabelaResultado, nivelMontarDados, ""));
                }
            }
        }
    }

    @Override
    public List<ContaVO> consultarTiposContas(String tipos, int nivelMontarDados) throws Exception {
        consultar(getIdEntidade(), false);
        StringBuilder sql = new StringBuilder();
        sql.append("SELECT * FROM conta");
        if (!tipos.trim().isEmpty()) {
            sql.append("WHERE tipoconta in (?)");
        }
        try (PreparedStatement sqlConsultar = con.prepareStatement(sql.toString())) {
            if (!tipos.trim().isEmpty()) {
                sqlConsultar.setString(1, tipos);
            }
            try (ResultSet tabelaResultado = sqlConsultar.executeQuery()) {
                return montarDadosConsulta(tabelaResultado, nivelMontarDados, "");
            }
        }
    }

    private PreparedStatement prepararIncluir(ContaVO obj) throws Exception {
        ContaVO.validarDados(obj);
        incluir(getIdEntidade());
        PreparedStatement sqlInserir = con.prepareStatement(sqlInsert);
        int i = 0;
        sqlInserir.setInt(++i, obj.getEmpresa().getCodigo());
        sqlInserir.setInt(++i, obj.getTipoConta().getCodigo());
        sqlInserir.setString(++i, obj.getDescricao());
        if (obj.getBanco() != null && obj.getBanco().getCodigo().intValue() != 0) {
            sqlInserir.setInt(++i, obj.getBanco().getCodigo().intValue());
        } else {
            sqlInserir.setNull(++i, 0);
        }
        if (!UteisValidacao.emptyString(obj.getNumero())) {
            sqlInserir.setString(++i, obj.getNumero());
        } else {
            sqlInserir.setNull(++i, 0);
        }
        if (!UteisValidacao.emptyString(obj.getNumeroDV())) {
            sqlInserir.setString(++i, obj.getNumeroDV());
        } else {
            sqlInserir.setNull(++i, 0);
        }
        if (!UteisValidacao.emptyString(obj.getAgencia())) {
            sqlInserir.setString(++i, obj.getAgencia());
        } else {
            sqlInserir.setNull(++i, 0);
        }
        if (!UteisValidacao.emptyString(obj.getAgenciaDV())) {
            sqlInserir.setString(++i, obj.getAgenciaDV());
        } else {
            sqlInserir.setNull(++i, 0);
        }
        sqlInserir.setBoolean(++i, obj.getAtiva());
        sqlInserir.setBoolean(++i, obj.getMostrarBi());
        sqlInserir.setString(++i, obj.getObservacao());
        sqlInserir.setString(++i, obj.getDescricaoCurta());
        sqlInserir.setBoolean(++i, obj.isMostrarDREDemonstrativo());
        if (obj.getBancoOpenBankEnum() != null && !UteisValidacao.emptyNumber(obj.getBancoOpenBankEnum().getCodigo())) {
            sqlInserir.setInt(++i, obj.getBancoOpenBankEnum().getCodigo());
        } else {
            sqlInserir.setNull(++i, 0);
        }
        return sqlInserir;
    }

    private PreparedStatement prepararAlterar(ContaVO obj) throws ConsistirException, Exception {
        ContaVO.validarDados(obj);
        alterar(getIdEntidade());
        PreparedStatement sqlAlterar = con.prepareStatement(sqlUpdate);
        int i = 0;
        sqlAlterar.setInt(++i, obj.getEmpresa().getCodigo());
        sqlAlterar.setInt(++i, obj.getTipoConta().getCodigo());
        sqlAlterar.setString(++i, obj.getDescricao());
        if (obj.getBanco().getCodigo().intValue() != 0) {
            sqlAlterar.setInt(++i, obj.getBanco().getCodigo().intValue());
        } else {
            sqlAlterar.setNull(++i, 0);
        }
        sqlAlterar.setString(++i, obj.getObservacao());
        if (!UteisValidacao.emptyString(obj.getNumero())) {
            sqlAlterar.setString(++i, obj.getNumero());
        } else {
            sqlAlterar.setNull(++i, 0);
        }
        if (!UteisValidacao.emptyString(obj.getNumeroDV())) {
            sqlAlterar.setString(++i, obj.getNumeroDV());
        } else {
            sqlAlterar.setNull(++i, 0);
        }
        if (!UteisValidacao.emptyString(obj.getAgencia())) {
            sqlAlterar.setString(++i, obj.getAgencia());
        } else {
            sqlAlterar.setNull(++i, 0);
        }
        if (!UteisValidacao.emptyString(obj.getAgenciaDV())) {
            sqlAlterar.setString(++i, obj.getAgenciaDV());
        } else {
            sqlAlterar.setNull(++i, 0);
        }
        sqlAlterar.setBoolean(++i, obj.getAtiva());
        sqlAlterar.setBoolean(++i, obj.getMostrarBi());
        sqlAlterar.setString(++i, obj.getDescricaoCurta());
        if(!obj.getAtiva()) {
            sqlAlterar.setBoolean(++i, obj.isMostrarDREDemonstrativo());
        }
        else {
            sqlAlterar.setBoolean(++i, true);
        }
        if (obj.getBancoOpenBankEnum() != null && !UteisValidacao.emptyNumber(obj.getBancoOpenBankEnum().getCodigo())) {
            sqlAlterar.setInt(++i, obj.getBancoOpenBankEnum().getCodigo());
        } else {
            sqlAlterar.setNull(++i, 0);
        }
        sqlAlterar.setInt(++i, obj.getCodigo().intValue());
        return sqlAlterar;
    }

    public List<ContaVO> consultarContasNaoVinculadasCaixaEmAberto(Integer empresa, int nivelMontarDados) throws Exception {
        StringBuilder sql = new StringBuilder();
        sql.append("select * from conta ");
        sql.append("where codigo not in(select cc.conta ");
        sql.append("                    from caixaConta cc ");
        sql.append("                    inner join caixa cx on cx.codigo = cc.caixa");
        sql.append("                    where cx.datafechamento is null) ");
        if (!UteisValidacao.emptyNumber(empresa)) {
            sql.append("AND empresa = " + empresa);
        }
        try (PreparedStatement pst = this.con.prepareStatement(sql.toString())) {
            try (ResultSet dados = pst.executeQuery()) {
                return montarDadosConsulta(dados, nivelMontarDados, "");
            }
        }
    }

    public List<ContaVO> consultarContasParaCaixa(Integer empresa, int nivelMontarDados) throws Exception {
        StringBuilder sql = new StringBuilder();
        sql.append("select * from conta where ativa = true");
        if (!UteisValidacao.emptyNumber(empresa)) {
            sql.append(" and empresa = ").append(empresa);
        }
        try (PreparedStatement pst = this.con.prepareStatement(sql.toString())) {
            try (ResultSet dados = pst.executeQuery()) {
                return montarDadosConsulta(dados, nivelMontarDados, "");
            }
        }
    }

    public Double saldoAte(int codigoConta, Date ate) throws Exception {
            return saldoAte(codigoConta, ate, false, null, null);
    }
     public Double saldoAte(Integer codigoConta, Date ate, boolean previsto, Integer empresa, String contas) throws Exception {
        StringBuilder sql = new StringBuilder();
        sql.append("SELECT coalesce((SELECT sum(cast(movcontarateio.valor as double precision)) from movcontarateio ");
        sql.append("INNER JOIN movconta mc ON movconta = mc.codigo AND tipoes = ").append(TipoES.ENTRADA.getCodigo());
        sql.append(" AND ");
        if(empresa != null){
            sql.append(" mc.empresa = ").append(empresa);
            sql.append(" AND ");
        }
        sql.append(previsto ? "datavencimento" : "dataquitacao");
        sql.append(" <= '" + Uteis.getDataJDBC(ate) + "  23:59:59' WHERE mc.conta");

        if(!UteisValidacao.emptyString(contas)){
            sql.append(" in (").append(contas).append(") ");
        }else if(codigoConta == null){
            sql.append(" is not null");
        }else{
            sql.append(" = ".concat(codigoConta.toString()));
        }

        sql.append(" ),0.0) - ");
        sql.append("coalesce((SELECT sum(cast(movcontarateio.valor as double precision)) from movcontarateio INNER JOIN movconta mc ");
        sql.append("ON movconta = mc.codigo AND tipoes = ").append(TipoES.SAIDA.getCodigo());
        sql.append(" AND ");
        if(empresa != null){
            sql.append(" mc.empresa = ").append(empresa);
            sql.append(" AND ");
        }
        sql.append(previsto ? "datavencimento" : "dataquitacao");
        sql.append(" <= '");
        sql.append(Uteis.getDataJDBC(ate));
        sql.append("  23:59:59' WHERE mc.conta ");

        if(!UteisValidacao.emptyString(contas)){
            sql.append(" in (").append(contas).append(") ");
        }else if(codigoConta == null){
            sql.append(" is not null");
        }else{
            sql.append(" = ".concat(codigoConta.toString()));
        }

        sql.append("),0.0) AS saldo");

         try (PreparedStatement stm = con.prepareStatement(sql.toString())) {
             try (ResultSet saldo = stm.executeQuery()) {
                 saldo.next();
                 return saldo.getDouble(1);
             }
         }

     }
     
    public ContaVO consultarPorCheque(int codigoCheque) throws Exception {
        String sql = " SELECT c.* FROM conta c "
                + " INNER JOIN movconta mc ON mc.conta = c.codigo "
                + " INNER JOIN historicocheque hc ON hc.movconta = mc.codigo "
                + " WHERE hc.cheque = " + codigoCheque
                + " AND hc.datafim is null";
        try (ResultSet consulta = criarConsulta(sql, con)) {
            if (consulta.next()) {
                return montarDados(consulta, Uteis.NIVELMONTARDADOS_DADOSBASICOS, "");
            }
        }
        return null;
    }

    public ContaVO consultarPorLote(int codigoLote) throws Exception {
        String sql = " SELECT c.* FROM conta c "
                + " INNER JOIN movconta mc ON mc.conta = c.codigo "
                + " WHERE mc.lote = " + codigoLote;
        try (ResultSet consulta = criarConsulta(sql, con)) {
            if (consulta.next()) {
                return montarDados(consulta, Uteis.NIVELMONTARDADOS_DADOSBASICOS, "");
            }
        }
        return null;
    }

    public ComportamentoConta getComportamento(int codigoConta) throws Exception {
        try (ResultSet criarConsulta = criarConsulta("SELECT t.comportamento FROM conta c inner join tipoconta t on t.codigo = c.tipoconta and c.codigo = " + codigoConta, con)) {
            if (criarConsulta.next()) {
                return ComportamentoConta.getComportamentoConta(criarConsulta.getInt("comportamento"));
            } else {
                return null;
            }
        }
    }

    public List<ContaVO> consultarContasMovimentadas(Date inicio, Date fim, Integer empresa, String contas) throws Exception {
        StringBuilder sql = new StringBuilder();
        sql.append(" SELECT c.codigo, c.descricao, sum(mcr.valor) as valor, mcr.tipoes FROM conta c \n");
        sql.append(" LEFT JOIN movconta mc ON mc.conta = c.codigo \n");
        sql.append(" LEFT JOIN movcontarateio mcr ON mcr.movconta = mc.codigo \n");
        sql.append(" WHERE (mc.dataquitacao >= '" + Uteis.getDataJDBC(inicio) + " 00:00:00' "
                + "AND mc.dataquitacao <= '" + Uteis.getDataJDBC(fim) + " 23:59:59') AND mostrarnobi is true ");
        if (!UteisValidacao.emptyNumber(empresa)) {
            sql.append(" and c.empresa = " + empresa);
        }
        if (!UteisValidacao.emptyString(contas)) {
            sql.append(" and c.codigo IN (" + contas + ") ");
        }
        sql.append(" GROUP BY c.codigo, c.descricao, mcr.tipoes ");
        Map<Integer, ContaVO> mapa;
        try (ResultSet consulta = criarConsulta(sql.toString(), con)) {
            mapa = new HashMap<Integer, ContaVO>();

            while (consulta.next()) {
                ContaVO conta = mapa.get(consulta.getInt("codigo"));
                TipoES tipo = TipoES.getTipoPadrao(consulta.getInt("tipoes"));
                if (tipo != null) {
                    if (conta == null) {
                        conta = new ContaVO();
                        conta.setCodigo(consulta.getInt("codigo"));
                        conta.setDescricao(consulta.getString("descricao"));
                        conta.setEntrada(tipo.equals(TipoES.ENTRADA) ? consulta.getDouble("valor") : 0.0);
                        conta.setSaida(tipo.equals(TipoES.SAIDA) ? consulta.getDouble("valor") : 0.0);
                        conta.setSaldoInicial(saldoAte(conta.getCodigo(), Uteis.somarDias(inicio, -1)));
                        conta.setSaldoAtual(saldoAte(conta.getCodigo(), fim));
                        mapa.put(consulta.getInt("codigo"), conta);
                    } else {
                        conta.setEntrada(tipo.equals(TipoES.ENTRADA) ? consulta.getDouble("valor") : conta.getEntrada());
                        conta.setSaida(tipo.equals(TipoES.SAIDA) ? consulta.getDouble("valor") : conta.getSaida());
                    }
                }
            }
        }
        List<ContaVO> contasL = new ArrayList<ContaVO>();
        contasL.addAll(mapa.values());
        Ordenacao.ordenarLista(contasL, "descricao");
        return contasL;
    }

    public ContaVO consultarOuCriarContaBancoPadrao(EmpresaVO empresa) throws Exception {
        try (ResultSet consulta = criarConsulta("SELECT * FROM conta WHERE descricao LIKE 'CONTA BANCO PADRAO' and empresa = " + empresa.getCodigo(), con)) {
            if (consulta.next()) {
                return montarDados(consulta, Uteis.NIVELMONTARDADOS_TODOS, "");
            } else {
                ContaVO conta = new ContaVO();
                conta.setDescricao("CONTA BANCO PADRAO");
                conta.setEmpresa(empresa);
                try (ResultSet consultaTipo = criarConsulta("SELECT * FROM tipoconta WHERE comportamento  = " + ComportamentoConta.BANCO.getCodigo(), con)) {
                    if (consultaTipo.next()) {
                        conta.setTipoConta(TipoConta.montarDados(consultaTipo, Uteis.NIVELMONTARDADOS_TODOS, con));
                    } else {
                        TipoContaVO tipoconta = new TipoContaVO();
                        tipoconta.setDescricao("BANCO");
                        tipoconta.setComportamento(ComportamentoConta.BANCO);
                        getFacade().getFinanceiro().getTipoConta().incluir(tipoconta);
                        conta.setTipoConta(tipoconta);
                        conta.setEmpresa(empresa);
                    }
                }
                incluir(conta);
                return conta;
            }
        }
    }

    @Override
    public List<ContaVO> consultarContasBI(Integer empresa) throws Exception {
        StringBuilder sql = new StringBuilder();
        sql.append("SELECT * FROM conta where mostrarnobi is true \n");

        if (!UteisValidacao.emptyNumber(empresa)) {
            sql.append(" and empresa = ").append(empresa);
        }
        sql.append(" ORDER BY descricao");

        try (PreparedStatement stm = con.prepareStatement(sql.toString())) {
            try (ResultSet rs = stm.executeQuery()) {
                return montarDadosConsulta(rs, Uteis.NIVELMONTARDADOS_DADOSBASICOS, "");
            }
        }

    }

    public List<GenericoTO> consultarGenerico(Date data, Integer empresa) throws Exception {
        List<GenericoTO> lista = new ArrayList<GenericoTO>();
        StringBuilder sql = new StringBuilder();
        sql.append("SELECT conta.codigo, conta.descricao, tipoconta.descricao as tipo FROM conta \n")
                .append(" inner join tipoconta on conta.tipoconta = tipoconta.codigo \n")
                .append(" where mostrarnobi is true \n");

        if (!UteisValidacao.emptyNumber(empresa)) {
            sql.append(" and empresa = ").append(empresa);
        }
        sql.append(" ORDER BY descricao");

        try (PreparedStatement stm = con.prepareStatement(sql.toString())) {
            try (ResultSet rs = stm.executeQuery()) {
                while (rs.next()) {
                    Double saldo = getFacade().getFinanceiro().getConta().saldoAte(rs.getInt("codigo"), data);
                    lista.add(new GenericoTO(rs.getString("descricao"), rs.getString("tipo"),
                            rs.getInt("codigo"), saldo));
                }
            }
        }
        return lista;

    }

    public List<ContaVO> consultarContasPorMovContas(String movContas) throws Exception {
        StringBuilder sql = new StringBuilder();
        sql.append("select * from conta\n");
        sql.append("where codigo IN (SELECT conta FROM movconta WHERE codigo IN (");
        sql.append(movContas).append("))");

        try (PreparedStatement stm = con.prepareStatement(sql.toString())) {
            try (ResultSet rs = stm.executeQuery()) {
                return montarDadosConsulta(rs, Uteis.NIVELMONTARDADOS_DADOSBASICOS, "");
            }
        }

    }

    @Override
    public List<ContaVO> consultarContasSimples(Integer empresa, Boolean somenteAtivas) throws Exception {
        StringBuilder sql = new StringBuilder()
                .append(" SELECT c.codigo, c.descricao, descricaocurta, t.codigo as tipo, t.descricao as tipodesc FROM conta c\n")
                .append(" inner join tipoconta t on t.codigo = c.tipoconta \n")
                .append("WHERE 1 = 1\n");

        if (!UteisValidacao.emptyNumber(empresa)) {
            sql.append("AND empresa = ").append(empresa).append("\n");
        }
        sql.append("AND mostrardredemonstrativo is true \n");
        if (somenteAtivas) {
            sql.append("AND ativa is true\n");
        }
        sql.append(" ORDER BY descricao");

        List<ContaVO> contas;
        try (PreparedStatement stm = con.prepareStatement(sql.toString())) {
            contas = new ArrayList<ContaVO>();
            try (ResultSet rs = stm.executeQuery()) {
                while (rs.next()) {
                    ContaVO conta = new ContaVO();
                    conta.setCodigo(rs.getInt("codigo"));
                    conta.setDescricao(UteisValidacao.emptyString(rs.getString("descricaocurta")) ?
                            rs.getString("descricao") : rs.getString("descricaocurta"));
                    conta.setTipoConta(new TipoContaVO());
                    conta.getTipoConta().setCodigo(rs.getInt("tipo"));
                    conta.getTipoConta().setDescricao(rs.getString("tipodesc"));
                    contas.add(conta);
                }
            }
        }
        return contas;

    }

    public boolean existeConta(ComportamentoConta comportamento) throws Exception{
        ResultSet consulta = criarConsulta("select c.codigo from conta c inner join tipoconta tc on tc.codigo = c.tipoconta where tc.comportamento = "+comportamento.getCodigo(), con);
        if(consulta.next()){
            return true;
        }
        return false;
    }

    public static Double buscarSaldoContaStoneOpenBank(String chaveZW, Integer empresaZW) {
        try {
            String url = Uteis.getUrlDiscovery("openBankingMs") + "/stonebank/saldo";
            HashMap<String, String> headers = new HashMap<>();
            headers.put("chaveZW", chaveZW);
            headers.put("account_id", getFacade().getContaStone().buscarAccountIdContaStone(empresaZW));
            headers.put("producao", PropsService.getPropertyValue(PropsService.stoneOpenBankProducao));
            String response = ExecuteRequestHttpService.executeRequestCupomDesconto(url, "", headers);
            SaldoZWDTO saldoStone = JSONMapper.getObject(new JSONObject(new JSONObject(response).getString("content")), SaldoZWDTO.class);
            return Uteis.arredondarForcando2CasasDecimais((float) saldoStone.getBalance() / 100);
        } catch (Exception ignore) {
            // Retorna saldo zerado sem impactar.
        }
        return 0.0;
    }

    public List<ExtratoMovimentoZWDTO> consultarExtratoContaStoneOpenBank(String chaveZW, Integer empresaZW, Date inicio, Date fim, int limit, int pageAnterior, int pagePosterior) {
        try {
            String url = Uteis.getUrlDiscovery("openBankingMs") + "/stonebank/extrato";
            HashMap<String, String> headers = new HashMap<>();
            headers.put("chaveZW", chaveZW);
            headers.put("account_id", getFacade().getContaStone().buscarAccountIdContaStone(empresaZW));
            headers.put("producao", PropsService.getPropertyValue(PropsService.stoneOpenBankProducao));

            JSONObject body = new JSONObject();
            body.put("limit", String.valueOf(limit));
            body.put("before", String.valueOf(pageAnterior));
            body.put("after", String.valueOf(pagePosterior));
            body.put("start_datetime", Calendario.getDataAplicandoFormatacao(inicio, "yyyy-MM-dd"));
            body.put("end_datetime", Calendario.getDataAplicandoFormatacao(fim, "yyyy-MM-dd"));
            String response = ExecuteRequestHttpService.executeRequestCupomDesconto(url, body.toString(), headers);
            return montarExtratoStoneOpenBanking(response);
        }catch (Exception ignore){
            // Retorna extrato vazio sem impactar.
        }
        return new ArrayList<>();
    }

    private List<ExtratoMovimentoZWDTO> montarExtratoStoneOpenBanking(String response) throws Exception {
        List<ExtratoMovimentoZWDTO> extratos = new ArrayList<>();
        JSONObject resp = new JSONObject(new JSONObject(response).getString("content"));
        JSONArray array = resp.getJSONArray("data");
        for(int i = 0; i < array.length(); i++){
            ExtratoMovimentoZWDTO extrato = JSONMapper.getObject(array.getJSONObject(i), ExtratoMovimentoZWDTO.class);
            extratos.add(extrato);
        }
        return extratos;
    }

    public void alterarSituacaoPorCodigoContrato(Integer codigoContrato) throws Exception {
        String sql = "update contrato set situacao = 'CA', vigenciaateajustada = '2020-07-08' where codigo = ?";
        try (PreparedStatement sqlAlterar = con.prepareStatement(sql)) {
            sqlAlterar.setInt(1, codigoContrato);
            sqlAlterar.execute();
        }
    }

}

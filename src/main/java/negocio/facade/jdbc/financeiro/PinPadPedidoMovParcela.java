package negocio.facade.jdbc.financeiro;

import negocio.comuns.financeiro.PinPadPedidoMovParcelaVO;
import negocio.facade.jdbc.arquitetura.SuperEntidade;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;

public class PinPadPedidoMovParcela extends SuperEntidade {

    public PinPadPedidoMovParcela() throws Exception {
        super();
    }

    public PinPadPedidoMovParcela(Connection con) throws Exception {
        super(con);
    }

    public void incluir(PinPadPedidoMovParcelaVO obj) throws Exception {
        String sql = "INSERT INTO PinPadPedidoMovParcela(pinpadpedido, movparcela) "
                + "VALUES (?,?)";
        try (PreparedStatement pst = con.prepareStatement(sql)) {
            int i = 0;
            pst.setInt(++i, obj.getPinpadpedido());
            pst.setInt(++i, obj.getMovparcela());

            ResultSet rsCodigo = pst.executeQuery();
            rsCodigo.next();
            obj.setCodigo(rsCodigo.getInt(1));
            obj.setNovoObj(false);
        }
    }

}

package negocio.facade.jdbc.financeiro;

import br.com.pactosolucoes.ce.comuns.enumerador.Mes;
import br.com.pactosolucoes.comuns.util.Formatador;
import negocio.comuns.basico.EmpresaVO;
import negocio.comuns.financeiro.MetaFinanceiraConsultorVO;
import negocio.comuns.financeiro.MetaFinanceiraEmpresaVO;
import negocio.comuns.financeiro.MetaFinanceiraEmpresaValoresVO;
import negocio.comuns.utilitarias.ConsistirException;
import negocio.comuns.utilitarias.Ordenacao;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisValidacao;
import negocio.facade.jdbc.arquitetura.SuperEntidade;
import negocio.facade.jdbc.basico.Empresa;
import negocio.interfaces.financeiro.MetaFinanceiraEmpresaInterfaceFacade;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 *
 * <AUTHOR>
 */
public class MetaFinanceiraEmpresa extends SuperEntidade implements MetaFinanceiraEmpresaInterfaceFacade {

    public MetaFinanceiraEmpresa() throws Exception {
        super();
    }

    public MetaFinanceiraEmpresa(Connection conexao) throws Exception {
        super(conexao);
    }

    private Boolean existeMetaMesAnoEmpresa(MetaFinanceiraEmpresaVO obj) throws SQLException {
        String sql = " SELECT * FROM metafinanceiraempresa WHERE empresa = ? AND mes = ? AND ano = ? " + (UteisValidacao.emptyNumber(obj.getCodigo()) ? "" : " AND codigo != "+obj.getCodigo());
        PreparedStatement stm = con.prepareStatement(sql);
        int i = 1;
        stm.setInt(i++, obj.getEmpresa().getCodigo());
        stm.setInt(i++, obj.getMes().getCodigo());
        stm.setInt(i++, obj.getAno());

        ResultSet resultSet = stm.executeQuery();
        return resultSet.next();
    }

    @Override
    public void incluir(MetaFinanceiraEmpresaVO obj) throws Exception {
        try {
            con.setAutoCommit(false);
            incluirSemCommit(obj);
            con.commit();
        } catch (Exception e) {
            obj.setNovoObj(true);
            con.rollback();
            throw e;
        } finally {
            con.setAutoCommit(true);
        }
    }

    @Override
    public void incluirSemCommit(MetaFinanceiraEmpresaVO obj) throws Exception {
        obj.validarDados();
        if (existeMetaMesAnoEmpresa(obj)) {
            throw new ConsistirException("Já existe uma meta para esta empresa no mês informado. ");
        }
        obj.realizarUpperCaseDados();
        PreparedStatement sql = con.prepareStatement("INSERT INTO metaFinanceiraEmpresa "
                + "(empresa, mes, ano, descricao, receitaveloc, faturamentoveloc, despesaveloc) VALUES (?, ?, ?, ?, ?, ?, ?)");
        sql.setInt(1, obj.getEmpresa().getCodigo());
        sql.setInt(2, obj.getMes().getCodigo());
        sql.setInt(3, obj.getAno());
        sql.setString(4, obj.getDescricao());
        sql.setDouble(5, obj.getReceitaVeloc());
        sql.setDouble(6, obj.getFaturamentoVeloc());
        sql.setDouble(7, obj.getDespesaVeloc());
        sql.execute();
        obj.setCodigo(obterValorChavePrimariaCodigo());
        obj.setNovoObj(false);
        incluirValores(obj);
        incluirConsultores(obj);
    }

    private void incluirValores(MetaFinanceiraEmpresaVO obj) throws Exception {
        for (MetaFinanceiraEmpresaValoresVO mf : obj.getValores()) {
            mf.validarDados();
            if (mf.isEmpty()) {
                continue;
            }
            if (mf.getValor() > 0){
                mf.setMetaFinanceiraEmpresa(obj.getCodigo());
                getFacade().getMetaFinanceiraEmpresaValores().incluirSemCommit(mf);
            }
        }
    }

    private void incluirConsultores(MetaFinanceiraEmpresaVO obj) throws Exception {
        for (MetaFinanceiraConsultorVO mf : obj.getConsultores()) {
            if (mf.getCodigo().intValue() < 0) {
                continue;
            }
            mf.setMetaFinanceiraEmpresa(obj.getCodigo());
            getFacade().getMetaFinanceiraConsultor().incluirSemCommit(mf);
        }
    }

    @Override
    public void alterar(MetaFinanceiraEmpresaVO obj) throws Exception {
        try {
            con.setAutoCommit(false);
            alterarSemCommit(obj);
            con.commit();
        } catch (Exception e) {
            con.rollback();
            throw e;
        } finally {
            con.setAutoCommit(true);
        }
    }

    @Override
    public void alterarSemCommit(MetaFinanceiraEmpresaVO obj) throws Exception {
        obj.validarDados();
        if (existeMetaMesAnoEmpresa(obj)) {
            throw new ConsistirException("Já existe uma meta para esta empresa no mês informado. ");
        }
        obj.realizarUpperCaseDados();
        PreparedStatement sqlAlterar = con.prepareStatement("UPDATE MetaFinanceiraEmpresa SET "
                + "empresa=?, mes=?, ano=?, descricao=?, receitaveloc = ?, faturamentoveloc = ? , despesaveloc = ? WHERE codigo = ?");
        sqlAlterar.setInt(1, obj.getEmpresa().getCodigo());
        sqlAlterar.setInt(2, obj.getMes().getCodigo());
        sqlAlterar.setInt(3, obj.getAno());
        sqlAlterar.setString(4, obj.getDescricao());
        sqlAlterar.setDouble(5, obj.getReceitaVeloc());
        sqlAlterar.setDouble(6, obj.getFaturamentoVeloc());
        sqlAlterar.setDouble(7, obj.getDespesaVeloc());
        sqlAlterar.setInt(8, obj.getCodigo());
        
        sqlAlterar.execute();
        alterarValores(obj);
        alterarConsultores(obj);
    }

    private void alterarValores(MetaFinanceiraEmpresaVO obj) throws Exception {
        getFacade().getMetaFinanceiraEmpresaValores().excluirPelaMeta(obj.getCodigo());
        incluirValores(obj);
    }

    private void alterarConsultores(MetaFinanceiraEmpresaVO obj) throws Exception {
        getFacade().getMetaFinanceiraConsultor().excluirPelaMeta(obj.getCodigo());
        incluirConsultores(obj);
    }

    @Override
    public void excluir(MetaFinanceiraEmpresaVO obj) throws Exception {
        try {
            con.setAutoCommit(false);
            excluirSemCommit(obj);
            con.commit();
        } catch (Exception e) {
            con.rollback();
            throw e;
        } finally {
            con.setAutoCommit(true);
        }
    }

    @Override
    public void excluirSemCommit(MetaFinanceiraEmpresaVO obj) throws Exception {
        getFacade().getMetaFinanceiraEmpresaValores().excluirPelaMeta(obj.getCodigo());
        getFacade().getMetaFinanceiraConsultor().excluirPelaMeta(obj.getCodigo());
        PreparedStatement sqlExcluir = con.prepareStatement("DELETE FROM MetaFinanceiraEmpresa WHERE codigo = ?");
        sqlExcluir.setInt(1, obj.getCodigo());
        sqlExcluir.execute();
    }

    @Override
    public MetaFinanceiraEmpresaVO consultarPorChavePrimaria(int codigo, int nivelMontarDados) throws Exception {
        StringBuilder str = new StringBuilder();
        str.append("SELECT * FROM MetaFinanceiraEmpresa WHERE codigo = ").append(codigo);
        PreparedStatement stm = con.prepareStatement(str.toString());
        ResultSet tabelaResultado = stm.executeQuery();
        if (tabelaResultado.next()) {
            return montarDados(tabelaResultado, nivelMontarDados, this.con);
        } else {
            return new MetaFinanceiraEmpresaVO();
        }
    }

    @Override
    public List<MetaFinanceiraEmpresaVO> consultarPorEmpresaAnoMesDescricao(int empresa, int ano, int mes, String descricao, int nivelMontarDados) throws Exception {
        StringBuilder str = new StringBuilder();
        str.append("SELECT * FROM MetaFinanceiraEmpresa ");

        if(empresa != 0){
            str.append("WHERE empresa = ").append(empresa);
        }

        if (ano > 0) {
            if(str.indexOf("WHERE") > 0){
                str.append(" and ano = ").append(ano);
            }else{
                str.append(" WHERE ano = ").append(ano);
            }
        }
        if (mes > 0) {
            if(str.indexOf("WHERE") > 0){
                str.append(" and mes = ").append(mes);
            }else{
                str.append(" WHERE mes = ").append(mes);
            }
        }
        if (!descricao.trim().isEmpty()) {
            if(str.indexOf("WHERE") > 0){
                str.append(" and descricao LIKE '%").append(descricao).append("%'");
            }else{
                str.append(" WHERE descricao LIKE '%").append(descricao).append("%'");
            }
        }
        str.append(" order by ano, mes ");
        PreparedStatement stm = con.prepareStatement(str.toString());
        ResultSet tabelaResultado = stm.executeQuery();
        return montarDadosConsulta(tabelaResultado, nivelMontarDados, con);
    }

    @Override
    public List<MetaFinanceiraEmpresaVO> consultarPorEmpresaPeriodo(int empresa, Date periodoDe, Date periodoAte, String descricao, int nivelMontarDados) throws Exception {
        List<MetaFinanceiraEmpresaVO> result = new ArrayList<MetaFinanceiraEmpresaVO>();
        List<Map<String, Integer>> params = prepararListaParams(periodoDe, periodoAte);
        for (Map<String, Integer> mapa : params) {
            StringBuilder str = new StringBuilder();
            str.append("SELECT * FROM MetaFinanceiraEmpresa WHERE (0 =0) ");
            if (empresa > 0) {
                str.append(" and (empresa = ").append(empresa).append(") ");
            }
            if ((periodoDe != null) && (periodoAte != null)) {
                str.append(" and ano = ").append(mapa.get("ano"));
                str.append(" and mes >= ").append(mapa.get("de"));
                str.append(" and mes <= ").append(mapa.get("ate"));
            }
            if ((descricao != null) && (!descricao.trim().equals(""))) {
                str.append(" and upper(descricao) LIKE '%").append(descricao.toUpperCase()).append("%'");
            }
            str.append(" order by ano, mes ");
            PreparedStatement stm = con.prepareStatement(str.toString());
            ResultSet tabelaResultado = stm.executeQuery();
            result.addAll(montarDadosConsulta(tabelaResultado, nivelMontarDados, con));
        }

        return result;
    }

    private List<Map<String, Integer>> prepararListaParams(Date periodoDe, Date periodoAte) {
        List<Map<String, Integer>> params = new ArrayList<Map<String, Integer>>();

        if (Uteis.getAnoData(periodoDe) == Uteis.getAnoData(periodoAte)) {
            Map<String, Integer> mapa = new HashMap<String, Integer>();
            mapa.put("de", Uteis.getMesData(periodoDe));
            mapa.put("ate", Uteis.getMesData(periodoAte));
            mapa.put("ano", Uteis.getAnoData(periodoDe));
            params.add(mapa);
        } else {
            int anoInicio = Uteis.getAnoData(periodoDe) + 1;
            Map<String, Integer> mapa = new HashMap<String, Integer>();
            mapa.put("de", Uteis.getMesData(periodoDe));
            mapa.put("ate", 12);
            mapa.put("ano", Uteis.getAnoData(periodoDe));
            params.add(mapa);

            while (anoInicio < Uteis.getAnoData(periodoAte)) {
                mapa = new HashMap<String, Integer>();
                mapa.put("de", 1);
                mapa.put("ate", 12);
                mapa.put("ano", anoInicio);
                params.add(mapa);
                anoInicio++;
            }

            mapa = new HashMap<String, Integer>();
            mapa.put("de", 1);
            mapa.put("ate", Uteis.getMesData(periodoAte));
            mapa.put("ano", Uteis.getAnoData(periodoAte));
            params.add(mapa);
        }

        return params;
    }

    public List<MetaFinanceiraEmpresaVO> montarDadosConsulta(ResultSet tabelaResultado, int nivelMontarDados, Connection con) throws Exception {
        List<MetaFinanceiraEmpresaVO> vetResultado = new ArrayList<MetaFinanceiraEmpresaVO>();
        while (tabelaResultado.next()) {
            vetResultado.add(montarDados(tabelaResultado, nivelMontarDados, con));
        }
        return vetResultado;
    }

    public MetaFinanceiraEmpresaVO montarDados(ResultSet dadosSQL, int nivelMontarDados, Connection con) throws Exception {
        MetaFinanceiraEmpresaVO obj = montarDadosBasico(dadosSQL);
        if (nivelMontarDados == Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA) {
            return obj;
        }

        montarListaValores(obj, con);
        montarListaConsultores(obj, con);
        if(nivelMontarDados == Uteis.NIVELMONTARDADOS_RESULTADOS_BI){
            return obj;
        }
        montarEmpresa(obj, con);
        return obj;
    }

    private MetaFinanceiraEmpresaVO montarDadosBasico(ResultSet dadosSQL) throws Exception {
        MetaFinanceiraEmpresaVO obj = new MetaFinanceiraEmpresaVO();
        obj.setNovoObj(false);
        obj.setCodigo(dadosSQL.getInt("codigo"));
        obj.setEmpresa(new EmpresaVO());
        obj.getEmpresa().setCodigo(dadosSQL.getInt("empresa"));
        obj.setMes(Mes.getMesPeloCodigo(dadosSQL.getInt("mes")));
        obj.setAno(dadosSQL.getInt("ano"));
        obj.setDescricao(dadosSQL.getString("descricao"));
        obj.setReceitaVeloc(dadosSQL.getDouble("receitaveloc"));
        obj.setFaturamentoVeloc(dadosSQL.getDouble("faturamentoveloc"));
        obj.setDespesaVeloc(dadosSQL.getDouble("despesaveloc"));
        return obj;
    }

    public void montarEmpresa(MetaFinanceiraEmpresaVO obj, Connection con) throws Exception {
        Empresa empresa = new Empresa(con);
        obj.setEmpresa(empresa.consultarPorChavePrimaria(
                obj.getEmpresa().getCodigo(),
                Uteis.NIVELMONTARDADOS_MINIMOS));
    }

    public void montarListaValores(MetaFinanceiraEmpresaVO obj, Connection con) throws Exception {
        MetaFinanceiraEmpresaValores metaValor = new MetaFinanceiraEmpresaValores(con);
        obj.setValores(metaValor.consultarPorMetaFinanceiraEmpresa(
                obj.getCodigo().intValue(),
                Uteis.NIVELMONTARDADOS_DADOSBASICOS));
        Ordenacao.ordenarLista(obj.getValores(), "valor");
        obj.backUpValores();
    }

    public void montarListaConsultores(MetaFinanceiraEmpresaVO obj, Connection con) throws Exception {
        MetaFinanceiraConsultor metaConsultor = new MetaFinanceiraConsultor(con);
        obj.setConsultores(metaConsultor.consultarPorMetaFinanceiraEmpresa(
                obj.getCodigo().intValue(),
                Uteis.NIVELMONTARDADOS_MINIMOS));
    }

    public String consultarJSON(Integer empresa) throws Exception {
        ResultSet rs = getRS(empresa);

        StringBuilder json = new StringBuilder();
        json.append("{\"aaData\":[");
        boolean dados = false;
        while (rs.next()) {
            dados = true;
            json.append("[\"").append(rs.getString("codigo")).append("\",");
            json.append("\"").append(Uteis.normalizarStringJSON(rs.getString("nomeEmpresa"))).append("\",");
            json.append("\"").append(rs.getString("ano")).append("/");
            if (rs.getString("mes").length() < 2) {
                json.append("0");
            }
            json.append(rs.getString("mes")).append("\",");
            json.append("\"").append(Uteis.normalizarStringJSON(rs.getString("descricao"))).append("\",");
            json.append(montarValores(rs.getString("valores"), rs.getString("moeda")));
            json.append("],");
        }
        if (dados) {
            json.deleteCharAt(json.toString().length() - 1);
        }
        json.append("]}");
        return json.toString();
    }

    private String montarValores(String retornoBanco, String moeda) {
        String temp = retornoBanco.replace("{", "").replace("}", "");
        String[] valores = temp.split(",");
        int i = 0;
        StringBuilder retorno = new StringBuilder("");
        int qtdValoresParaCriar = 5 - valores.length;
        if (!temp.contains(",")) {
            qtdValoresParaCriar = 5;
        }
        for (String valor : valores) {
            if (!UteisValidacao.emptyString(valor)) {
                retorno.append("\"").append(moeda + " ").append(Formatador.formatarValorMonetarioSemMoeda(Double.parseDouble(valor))).append("\",");
            }
        }
        while (i < qtdValoresParaCriar) {
            retorno.append("\"-\",");
            i++;
        }
        retorno.deleteCharAt(retorno.toString().length() - 1);
        temp = null;
        valores = null;
        return retorno.toString();
    }

    private ResultSet getRS(Integer empresa) throws SQLException {
        StringBuilder sql = new StringBuilder("SELECT\n"
                + "  DISTINCT mfe.codigo, em.nome as nomeEmpresa, mfe.empresa, mfe.ano, mfe.mes, mfe.descricao, em.moeda,\n"
                + "  (array(SELECT\n"
                + "           mfev1.valor\n"
                + "         FROM metafinanceiraempresavalores mfev1\n"
                + "         WHERE metafinanceiraempresa = mfe.codigo\n"
                + "         ORDER BY mfev1.codigo ASC)) as valores\n"
                + "FROM metafinanceiraempresa mfe\n"
                + " LEFT JOIN empresa em ON em.codigo = mfe.empresa \n"
                + "WHERE 1 = 1\n");
        if (empresa != 0) {
            sql.append("      AND empresa = ").append(empresa).append(";");
        }
        PreparedStatement sqlConsultar = con.prepareStatement(sql.toString());
        return sqlConsultar.executeQuery();
    }

    public List consultarParaImpressao(String filtro, String ordem, String campoOrdenacao, int empresa, String moeda) throws SQLException {

        ResultSet rs = getRS(empresa);
        List lista = new ArrayList();

        while (rs.next()) {

            MetaFinanceiraEmpresaVO metaFinanceira = new MetaFinanceiraEmpresaVO();
            String geral = rs.getString("codigo") + rs.getString("nomeEmpresa") + rs.getString("ano") + rs.getString("mes") + rs.getString("descricao");
            if (geral.toLowerCase().contains(filtro.toLowerCase())) {
                metaFinanceira.setCodigo(rs.getInt("codigo"));
                metaFinanceira.getEmpresa().setNome(rs.getString("nomeEmpresa"));
                metaFinanceira.getEmpresa().setMoeda(moeda);

                metaFinanceira.setValores(new ArrayList<MetaFinanceiraEmpresaValoresVO>());
                String temp = rs.getString("valores").replace("{", "").replace("}", "");
                String[] valores = temp.split(",");
                for (String valor : valores) {
                    MetaFinanceiraEmpresaValoresVO mfv = new MetaFinanceiraEmpresaValoresVO();
                    mfv.setValor(Double.valueOf(valor));
                    metaFinanceira.getValores().add(mfv);
                }

                metaFinanceira.setAno(rs.getInt("ano"));
                metaFinanceira.setCodigoMes(rs.getInt("mes"));
                metaFinanceira.setDescricao(rs.getString("descricao"));
                lista.add(metaFinanceira);
            }
        }
        if (campoOrdenacao.equals("Código")) {
            Ordenacao.ordenarLista(lista, "codigo");
        } else if (campoOrdenacao.equals("Empresa")) {
            Ordenacao.ordenarLista(lista, "empresa_Apresentar");
        } else if (campoOrdenacao.equals("Ano/Mês")) {
            Ordenacao.ordenarLista(lista, "anoMes");
        } else if (campoOrdenacao.equals("Descrição")) {
            Ordenacao.ordenarLista(lista, "descricao");
        } else if (campoOrdenacao.equals("Meta 1")) {
            Ordenacao.ordenarLista(lista, "valor0");
        } else if (campoOrdenacao.equals("Meta 2")) {
            Ordenacao.ordenarLista(lista, "valor1");
        } else if (campoOrdenacao.equals("Meta 3")) {
            Ordenacao.ordenarLista(lista, "valor2");
        } else if (campoOrdenacao.equals("Meta 4")) {
            Ordenacao.ordenarLista(lista, "valor3");
        } else if (campoOrdenacao.equals("Meta 5")) {
            Ordenacao.ordenarLista(lista, "valor4");
        }
        if (ordem.contains("desc")) {
            Collections.reverse(lista);
        }
        return lista;

    }
}

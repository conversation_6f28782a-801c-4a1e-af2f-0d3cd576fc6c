package negocio.facade.jdbc.financeiro;

import negocio.comuns.financeiro.MovParcelaCupomDescontoVO;
import negocio.comuns.financeiro.MovParcelaVO;
import negocio.comuns.utilitarias.Calendario;
import negocio.comuns.utilitarias.Uteis;
import negocio.facade.jdbc.arquitetura.SuperEntidade;
import negocio.interfaces.financeiro.MovParcelaCupomDescontoInterfaceFacade;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.Statement;

/**
 * Created by ulisses on 05/06/2016.
 */
public class MovParcelaCupomDesconto extends SuperEntidade implements MovParcelaCupomDescontoInterfaceFacade {

    public MovParcelaCupomDesconto() throws Exception {
        super();
    }

    public MovParcelaCupomDesconto(Connection conexao) throws Exception {
        super(conexao);
    }

    public void incluir(MovParcelaCupomDescontoVO movParcelaCupomDescontoVO)throws Exception{
        String sql = "insert into movParcelaCupomDesconto (movparcela, cupomdesconto, responsaveldesconto, valordesconto,datalancamento,descontoContratoNovo) values(?,?,?,?,?,?)";
        PreparedStatement pst = con.prepareStatement(sql);
        pst.setInt(1, movParcelaCupomDescontoVO.getMovParcelaVO().getCodigo());
        pst.setString(2, movParcelaCupomDescontoVO.getCupomDesconto());
        pst.setString(3, movParcelaCupomDescontoVO.getResponsavelDesconto());
        pst.setDouble(4, movParcelaCupomDescontoVO.getValorDesconto());
        pst.setTimestamp(5, Uteis.getDataJDBCTimestamp(Calendario.hoje()));
        pst.setBoolean(6, movParcelaCupomDescontoVO.isDescontoContratoNovo());
        pst.execute();
    }

    public void excluirPorCodigoContrato(Integer codigoContrato)throws Exception{
       StringBuilder sql = new StringBuilder();
        sql.append("delete from movParcelaCupomDesconto where movParcela in(select codigo from movParcela where contrato  = ").append(codigoContrato).append(")");
        Statement st = con.createStatement();
        st.execute(sql.toString());
    }

    private MovParcelaCupomDescontoVO montarDadosBasico(ResultSet rs)throws Exception{
        MovParcelaCupomDescontoVO obj = new MovParcelaCupomDescontoVO();
        obj.setCodigo(rs.getInt("codigo"));
        obj.setMovParcelaVO(new MovParcelaVO());
        obj.getMovParcelaVO().setCodigo(rs.getInt("movParcela"));
        obj.setCupomDesconto(rs.getString("cupomDesconto"));
        obj.setResponsavelDesconto(rs.getString("responsavelDesconto"));
        obj.setValorDesconto(rs.getDouble("valorDesconto"));
        obj.setDataLancamento(rs.getTimestamp("dataLancamento"));
        obj.setDescontoContratoNovo(rs.getBoolean("descontoContratoNovo"));
        return obj;
    }

    public MovParcelaCupomDescontoVO consultarPorMovProduto(Integer codigoMovProduto, int nivelMontarDados)throws Exception{
        StringBuilder sql = new StringBuilder();
        sql.append("select movParcCup.*  \n");
        sql.append("from movParcelaCupomDesconto movParcCup \n");
        sql.append("inner join movParcela parc on parc.codigo = movParcCup.movParcela \n");
        sql.append("inner join movProdutoParcela movProdParc on movProdParc.movParcela = parc.codigo \n");
        sql.append("inner join movProduto mov on mov.codigo = movProdParc.movProduto \n");
        sql.append("where mov.codigo = ").append(codigoMovProduto);
        Statement st = con.createStatement();
        ResultSet rs = st.executeQuery(sql.toString());
        if (rs.next()){
            return montarDadosBasico(rs);
        }
        return null;
    }
}

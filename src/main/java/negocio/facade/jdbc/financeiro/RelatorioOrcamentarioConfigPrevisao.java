package negocio.facade.jdbc.financeiro;

import negocio.comuns.financeiro.PlanoContaTO;
import negocio.comuns.financeiro.RelatorioOrcamentarioValoresPrevisaoVO;
import negocio.facade.jdbc.arquitetura.SuperEntidade;
import negocio.interfaces.financeiro.RelatorioOrcamentarioConfigPrevisaoInterfaceFacade;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;

public class RelatorioOrcamentarioConfigPrevisao extends SuperEntidade implements RelatorioOrcamentarioConfigPrevisaoInterfaceFacade {

    public RelatorioOrcamentarioConfigPrevisao() throws Exception {
        super();
    }

    public RelatorioOrcamentarioConfigPrevisao(Connection conexao) throws Exception {
        super(conexao);
    }

    @Override
    public void incluirSemCommit(RelatorioOrcamentarioValoresPrevisaoVO obj) throws Exception {
        PreparedStatement sql = con.prepareStatement("INSERT INTO previsaoplanocontasrelatorioorcamentario "
                + "(relatorioorcamentario, planoconta, previsao) VALUES (?, ?, ?)");
        sql.setInt(1, obj.getRelatorioOrcamentarioConfigVO());
        sql.setInt(2, obj.getPlanoContaTO().getCodigo());
        sql.setDouble(3, obj.getValor());
        sql.execute();
        obj.setNovoObj(false);
    }

    @Override
    public void alterarSemCommit(RelatorioOrcamentarioValoresPrevisaoVO obj) throws Exception {
        PreparedStatement sqlAlterar = con.prepareStatement("UPDATE previsaoplanocontasrelatorioorcamentario SET "
                + "relatorioorcamentario=?, planoconta=?, previsao=? WHERE codigo = ?");
        sqlAlterar.setInt(1, obj.getRelatorioOrcamentarioConfigVO());
        sqlAlterar.setInt(2, obj.getPlanoContaTO().getCodigo());
        sqlAlterar.setDouble(3, obj.getValor());
        sqlAlterar.setInt(4, obj.getCodigo());
        
        sqlAlterar.execute();
    }

    @Override
    public void excluirSemCommit(Integer idRelatorioOrcamentarioConfig) throws Exception {
        PreparedStatement sqlExcluir = con.prepareStatement("DELETE FROM previsaoplanocontasrelatorioorcamentario WHERE relatorioorcamentario = ?;");
        sqlExcluir.setInt(1, idRelatorioOrcamentarioConfig);
        sqlExcluir.execute();
    }

    public RelatorioOrcamentarioValoresPrevisaoVO consultarIdrelatorioOrcamentarioConfigIdPlanoConta(Integer idCodigoRelOrc, PlanoContaTO planoConta) throws Exception {
        StringBuilder str = new StringBuilder();
        str.append("SELECT * FROM previsaoplanocontasrelatorioorcamentario WHERE relatorioorcamentario = " + idCodigoRelOrc + " AND planoconta = " + planoConta.getCodigo() + " ;");
        PreparedStatement stm = con.prepareStatement(str.toString());
        ResultSet tabelaResultadoPrevisao = stm.executeQuery();
        return montarDadosConsulta(tabelaResultadoPrevisao, planoConta);
    }

    public Double consultarValorPrevisaoPorIdRelatorioOrcamentarioConfigEIdPlanoConta(Integer idCodigoRelOrc, Integer idPlanoConta) throws Exception {
        StringBuilder str = new StringBuilder();
        str.append("SELECT previsao FROM previsaoplanocontasrelatorioorcamentario WHERE relatorioorcamentario = " + idCodigoRelOrc + " AND planoconta = " + idPlanoConta + " ;");
        PreparedStatement stm = con.prepareStatement(str.toString());
        ResultSet resultSet = stm.executeQuery();
        if(resultSet.next()) {
            return resultSet.getDouble("previsao");
        }
        return 0.0;
    }

    public RelatorioOrcamentarioValoresPrevisaoVO montarDadosConsulta(ResultSet tabelaResultadoPrevisao, PlanoContaTO planoConta) throws Exception {
        RelatorioOrcamentarioValoresPrevisaoVO vetResultado = new RelatorioOrcamentarioValoresPrevisaoVO();
        vetResultado.setPlanoContaTO(planoConta);
        if (tabelaResultadoPrevisao.next()) {
            vetResultado.setCodigo(tabelaResultadoPrevisao.getInt("codigo"));
            vetResultado.setValor(tabelaResultadoPrevisao.getDouble("previsao"));
        } else {
            vetResultado.setCodigo(0);
            vetResultado.setValor(0.0);
        }
        return vetResultado;
    }

}

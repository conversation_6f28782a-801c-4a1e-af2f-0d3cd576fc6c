package negocio.facade.jdbc.financeiro;


import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.sql.Statement;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

import br.com.pactosolucoes.enumeradores.TipoOperacaoLancamento;
import java.util.Calendar;

import negocio.comuns.financeiro.*;
import negocio.comuns.utilitarias.Calendario;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisValidacao;
import negocio.facade.jdbc.arquitetura.SuperEntidade;
import negocio.interfaces.financeiro.HistoricoChequeInterfaceFacade;

public class HistoricoCheque extends SuperEntidade implements HistoricoChequeInterfaceFacade {

	public HistoricoCheque() throws Exception {
		super();
	}
    public HistoricoCheque(Connection con) throws Exception {
		super(con);
	}

	public void incluir(HistoricoChequeVO obj) throws Exception {
		incluir(obj, true);
	}

	public void incluir(HistoricoChequeVO obj,  boolean controlarTransacao) throws Exception {
		try {
			if (controlarTransacao) {
				con.setAutoCommit(false);
			}
			incluirSemCommit(obj);
			if (controlarTransacao) {
				con.commit();
			}
		} catch (Exception e) {
			obj.setNovoObj(true);
			if (controlarTransacao) {
				con.rollback();
			}
			throw e;
		} finally {
			if (controlarTransacao) {
				con.setAutoCommit(true);
			}
		}
	}

	@Override
    public void incluirSemCommit(HistoricoChequeVO obj) throws Exception {
		finalizarHistorico(obj, obj.getDataInicio());

		//esta cláusula vai ficar somente até eu realizar o projeto que mudará a retirada de cheques em lotes
		if(!obj.getStatus().equals(StatusCheque.RETIRAR_LOTES)){
			StringBuilder sql = new StringBuilder();
			sql.append("INSERT INTO historicocheque(cheque, status, lote, datainicio, movconta, dataoriginal) ");
			sql.append("VALUES ( ?, ?, ?, ?, ?, ?);");

			PreparedStatement sqlInserir = con.prepareStatement(sql.toString());
			int i = 1;
			sqlInserir.setInt(i++, obj.getCheque().getCodigo());
			sqlInserir.setInt(i++, obj.getStatus().getCodigo());

			resolveFKNull(sqlInserir, i++, obj.getLote().getCodigo());
			sqlInserir.setTimestamp(i++, Uteis.getDataJDBCTimestamp(obj.getDataInicio()));

			resolveFKNull(sqlInserir, i++, obj.getMovConta().getCodigo());
			resolveDateNull(sqlInserir, i++, obj.getCheque().getDataOriginal());

			sqlInserir.execute();
			obj.setCodigo(obterValorChavePrimariaCodigo());
			obj.setNovoObj(false);
		}
    }
    
    @Override
    public List<HistoricoChequeVO> consultarPorChequeComposicao(String codigos) throws Exception{
        ResultSet dados = con.prepareStatement("SELECT hc.*,l.descricao as desclote, c.*, mc.tipooperacao, p.nome,  mc.codigo as codigomovconta  FROM historicocheque hc" +
        		" LEFT JOIN movconta mc ON mc.codigo = hc.movconta "+
        		" LEFT JOIN conta c ON c.codigo = mc.conta "+
        		" LEFT JOIN lote l ON hc.lote = l.codigo "+
        		" LEFT JOIN pessoa p ON p.codigo = mc.pessoa "+
        		" WHERE cheque in ( "+codigos+") ORDER BY hc.codigo desc").executeQuery();
        return montarDadosConsulta(dados, Uteis.NIVELMONTARDADOS_TELACONSULTA);
    }
    

    public static List<HistoricoChequeVO> montarDadosConsulta(ResultSet dados, int nivelMontarDados) throws Exception{
    	List<HistoricoChequeVO> vetResultado = new ArrayList<HistoricoChequeVO>();
        while (dados.next()) {
        	HistoricoChequeVO obj = new HistoricoChequeVO();
            obj = montarDados(dados, nivelMontarDados);
            vetResultado.add(obj);
        }

        return vetResultado;
    }
    
    public void finalizarHistorico(HistoricoChequeVO histCheque, Date data) throws Exception{
    	ResultSet dados = con.prepareStatement("SELECT * FROM historicocheque WHERE cheque in ("+histCheque.getCheque().getObterTodosChequesComposicao()
    										   +") AND datafim is null ORDER BY datainicio DESC limit 1").executeQuery();
    	if(dados.next()){
    		HistoricoChequeVO hist = montarDados(dados, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
    		PreparedStatement stm = con.prepareStatement("UPDATE historicocheque SET datafim = ? WHERE codigo = ? ");
    		stm.setTimestamp(1, Uteis.getDataJDBCTimestamp(Uteis.somarCampoData(data, Calendar.SECOND, -1)));
    		stm.setInt(2, hist.getCodigo());
    		stm.execute();
    	}
    }
    
    public void mudarStatus(StatusCheque novoStatus, String codigosComposicao) throws Exception{
    	ResultSet dados = con.prepareStatement("SELECT * FROM historicocheque WHERE cheque in ("+codigosComposicao
				   +") ORDER BY codigo DESC limit 1").executeQuery();
		if (dados.next()) {
			HistoricoChequeVO hist = montarDados(dados, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
			PreparedStatement stm = con.prepareStatement("UPDATE historicocheque SET status = ? WHERE codigo = ? ");
			stm.setInt(1, novoStatus.getCodigo());
			stm.setInt(2, hist.getCodigo());
			stm.execute();
		}
    	
    }
    
    public static HistoricoChequeVO montarDados(ResultSet dados, int nivelMontarDados) throws Exception{
    	HistoricoChequeVO historico = new HistoricoChequeVO();
    	historico.getCheque().setCodigo(dados.getInt("cheque"));
    	historico.getLote().setCodigo(dados.getInt("lote"));
    	
    	historico.setDataInicio(dados.getTimestamp("datainicio"));
    	historico.setDataFim(dados.getTimestamp("datafim"));
    	historico.setCodigo(dados.getInt("codigo"));
    	historico.setStatus(StatusCheque.getStatusCheque(dados.getInt("status")));
    	historico.getMovConta().setCodigo(dados.getInt("movconta"));
    	
    	
    	
    	if(nivelMontarDados == Uteis.NIVELMONTARDADOS_TELACONSULTA){
    		historico.getMovConta().setContaVO(Conta.montarDados(dados, Uteis.NIVELMONTARDADOS_DADOSBASICOS, ""));
    		historico.getLote().setDescricao(UteisValidacao.emptyString(dados.getString("desclote")) ? String.valueOf(dados.getInt("lote")) : dados.getString("desclote"));
    		historico.getMovConta().setTipoOperacaoLancamento(TipoOperacaoLancamento.getTipoOperacaoLancamento(dados.getInt("tipooperacao")));
    		historico.getMovConta().getPessoaVO().setNome(dados.getString("nome"));
    		historico.getMovConta().setCodigo(UteisValidacao.emptyNumber(dados.getInt("codigomovconta")) ? 0 : dados.getInt("codigomovconta"));
    	}
    	return historico;
    }
    
    public void inicializarHistorico(ChequeVO cheque, int movConta, LoteVO lote, TipoOperacaoLancamento tipoOperacao) throws Exception{
    	HistoricoChequeVO histCheque = new HistoricoChequeVO();
    	histCheque.setCheque(cheque);
    	histCheque.setStatus(
				tipoOperacao.equals(TipoOperacaoLancamento.DEVOLUCAO_CHEQUE) ? StatusCheque.DEVOLVIDO :
						(tipoOperacao.equals(TipoOperacaoLancamento.CUSTODIA) ? StatusCheque.CUSTODIA : StatusCheque.DEPOSITADO));
    	histCheque.setDataInicio(Calendario.hoje());
    	histCheque.getMovConta().setCodigo(movConta);
    	histCheque.setLote(lote);
    	
    	incluir(histCheque);
    	if(tipoOperacao.equals(TipoOperacaoLancamento.DEVOLUCAO_CHEQUE)){
    		executarConsulta("UPDATE cheque set situacao = 'DV' where codigo in ("+cheque.getObterTodosChequesComposicao()+")", con);
		}
    	
    }
    
	public void excluirPorMovConta(Integer codigoMovConta)throws Exception{
		StringBuilder sql = new StringBuilder();
		sql.append("delete from historicocheque where movConta =").append(codigoMovConta);
		Statement st = con.createStatement();
		st.execute(sql.toString());
	}
    
    public void getContaLoteCheque(ChequeTO cheque, boolean datafimisnull) throws Exception{
    	StringBuilder sql = sqlLote(cheque.getObterTodosChequesComposicao(), datafimisnull);

    	ResultSet resultSet = criarConsulta(sql.toString(), con);
    	if(resultSet.next()){
    		cheque.setContaContido(resultSet.getString("contac"));	
    		cheque.setNumeroLote(resultSet.getInt("nrlote"));
    		cheque.setPagaMovConta(resultSet.getInt("pagamovconta"));
    		cheque.setCodigoContaContido(resultSet.getInt("codigoconta"));
            cheque.setDataFim(resultSet.getDate("datafim"));
    	}
    }

    public ContaVO getContaCheque(Integer cheque, boolean datafimisnull) throws Exception{
    	StringBuilder sql = sqlLote(cheque.toString(), datafimisnull);

    	ResultSet resultSet = criarConsulta(sql.toString(), con);
    	if(resultSet.next()){
			return getFacade().getFinanceiro().getConta().consultarPorChavePrimaria(resultSet.getInt("codigoconta"), Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA);
    	}
    	return null;
    }
    
    public void getCodigoLoteCheque(ChequeVO cheque, boolean datafimisnull) throws Exception{
    	
    	StringBuilder sql = sqlLote(cheque.getObterTodosChequesComposicao(), datafimisnull);

    	ResultSet resultSet = criarConsulta(sql.toString(), con);
    	Lote loteDAO = new Lote(con);
    	if(resultSet.next()){
            int loteAvulso = loteDAO.consultarLoteAvulso(cheque.getCodigo());
            if (resultSet.getDate("datafim") == null || loteAvulso != 0) {
    		    cheque.getLoteVO().setCodigo(resultSet.getInt("nrlote"));
            }
    	}
    }
	/**
	 * Responsável por 
	 * <AUTHOR> Alcides
	 * 23/03/2013
	 */
	private StringBuilder sqlLote(String codigosComposicao, boolean datafimisnull) {
		StringBuilder sql = new StringBuilder("SELECT c.codigo as codigoconta, c.descricao as contac, lote.codigo as nrlote, coalesce(lote.pagamovconta, mcLote.codigo) as pagamovconta  , hc.datafim FROM historicocheque hc ")
    	.append(" INNER JOIN movconta mc ON mc.codigo = hc.movconta ")
	    .append(" LEFT JOIN movconta mcLote ON mcLote.lotePagouConta = hc.lote ")
    	.append(" INNER JOIN lote ON hc.lote = lote.codigo ")
    	.append(" INNER JOIN conta c ON c.codigo = mc.conta ")
    	.append(" WHERE hc.cheque in ("+codigosComposicao+")")
    	.append(" AND not lote.avulso ");
        if(datafimisnull){
            sql.append(" AND hc.datafim is null ");
        }
        sql.append("ORDER BY hc.datainicio DESC limit 1");
		return sql;
	}
    
    public void mudarStatusPorLote(StatusCheque novoStatus, int lote) throws Exception{
    	executarConsulta("UPDATE historicocheque SET status = "+novoStatus.getCodigo()+" WHERE lote = "+lote, con);
    }    

    public boolean chequeJaSaiuLote(String codigosComposicao, Integer codigoLote) throws Exception{
        ResultSet set = criarConsulta("SELECT codigo FROM historicocheque WHERE cheque in ("+codigosComposicao
                + ") AND lote = "+codigoLote+" AND datafim is not null", con);
        return set.next();
    }
  
}

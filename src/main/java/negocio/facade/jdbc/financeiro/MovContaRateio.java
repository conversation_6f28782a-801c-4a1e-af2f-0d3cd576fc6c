
package negocio.facade.jdbc.financeiro;

import br.com.pactosolucoes.enumeradores.TipoOperacaoLancamento;
import negocio.comuns.financeiro.CentroCustoTO;
import negocio.comuns.financeiro.FluxoCaixaTO;
import negocio.comuns.financeiro.FormaPagamentoVO;
import negocio.comuns.financeiro.MovContaRateioVO;
import negocio.comuns.financeiro.MovContaVO;
import negocio.comuns.financeiro.PlanoContaTO;
import negocio.comuns.financeiro.TipoDocumentoVO;
import negocio.comuns.financeiro.enumerador.TipoES;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisValidacao;
import negocio.facade.jdbc.arquitetura.SuperEntidade;
import negocio.facade.jdbc.arquitetura.SuperFacadeJDBC;
import negocio.interfaces.financeiro.MovContaRateioInterfaceFacade;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.sql.Statement;
import java.util.ArrayList;
import java.util.Date;
import java.util.Iterator;
import java.util.List;

public class MovContaRateio extends SuperEntidade implements MovContaRateioInterfaceFacade {

    public MovContaRateio() throws Exception {
        super();
    }
    
    public MovContaRateio(Connection con) throws Exception {
        super(con);
    }

    /**
     * Incluir novo movContaRateioVO
     * @param obj
     * @throws Exception
     */

    @Override
    public void incluir(MovContaRateioVO obj, MovContaVO movContaVO) throws Exception {
        incluir(obj, movContaVO, false);
    }

    @Override
    public void incluir(MovContaRateioVO obj, MovContaVO movContaVO, boolean validarFormaPagamento) throws Exception {

        verificacarObjetoIncorretoAntesDeGravar(obj, movContaVO);

        //a validação será falsa para todos os campos
        //pois a validação é feita somente quando há mais de um movContaRateioVO
        MovContaRateioVO.validarDados(obj, false, validarFormaPagamento);
        //incluir(getIdEntidade());
        StringBuilder sql = new StringBuilder();
        sql.append("insert into movcontarateio (movConta , ");
        sql.append("planoConta , ");
        sql.append("centroCusto , ");
        sql.append("tipoDocumento , formaPagamento , ");
        sql.append("tipoes, descricao , valor, numerodocumento) ");
        sql.append("values (?,?,?,?,?,?,?,?,?)");
        try (PreparedStatement sqlInserir = con.prepareStatement(sql.toString())) {
            int i = 1;
            sqlInserir.setInt(i++, obj.getMovContaVO());
            if (obj.getPlanoContaVO().getCodigo() != null && obj.getPlanoContaVO().getCodigo() != 0) {
                sqlInserir.setInt(i++, obj.getPlanoContaVO().getCodigo());
            } else {
                sqlInserir.setNull(i++, 0);
            }
            if (obj.getCentroCustoVO().getCodigo() != null && obj.getCentroCustoVO().getCodigo() != 0) {
                sqlInserir.setInt(i++, obj.getCentroCustoVO().getCodigo());
            } else {
                sqlInserir.setNull(i++, 0);
            }
            if (obj.getTipoDocumentoVO().getCodigo() != null && obj.getTipoDocumentoVO().getCodigo() != 0) {
                sqlInserir.setInt(i++, obj.getTipoDocumentoVO().getCodigo());
            } else {
                sqlInserir.setNull(i++, 0);
            }
            if (obj.getFormaPagamentoVO().getCodigo() != null && obj.getFormaPagamentoVO().getCodigo() != 0)
                sqlInserir.setInt(i++, obj.getFormaPagamentoVO().getCodigo());
            else
                sqlInserir.setNull(i++, 0);
            sqlInserir.setInt(i++, obj.getTipoES().getCodigo());
            sqlInserir.setString(i++, obj.getDescricao());
            sqlInserir.setDouble(i++, obj.getValor());
            sqlInserir.setString(i++, obj.getNumeroDocumento());
            sqlInserir.execute();
        }
        obj.setCodigo(obterValorChavePrimariaCodigo());
        obj.getCodigoLog();
        obj.setNovoObj(false);
    }

    @Override
    public void incluirOrigemProcessoCopiarContas(MovContaRateioVO obj) throws Exception {
        StringBuilder sql = new StringBuilder();
        sql.append("insert into movcontarateio (codigo, movConta , ");
        sql.append("planoConta , ");
        sql.append("centroCusto , ");
        sql.append("tipoDocumento , formaPagamento , ");
        sql.append("tipoes, descricao , valor, numerodocumento) ");
        sql.append("values (?,?,?,?,?,?,?,?,?,?)");
        try (PreparedStatement sqlInserir = con.prepareStatement(sql.toString())) {
            int i = 1;
            sqlInserir.setInt(i++, obj.getCodigo());
            sqlInserir.setInt(i++, obj.getMovContaVO());
            if (obj.getPlanoContaVO().getCodigo() != null && obj.getPlanoContaVO().getCodigo() != 0) {
                sqlInserir.setInt(i++, obj.getPlanoContaVO().getCodigo());
            } else {
                sqlInserir.setNull(i++, 0);
            }
            if (obj.getCentroCustoVO().getCodigo() != null && obj.getCentroCustoVO().getCodigo() != 0) {
                sqlInserir.setInt(i++, obj.getCentroCustoVO().getCodigo());
            } else {
                sqlInserir.setNull(i++, 0);
            }
            if (obj.getTipoDocumentoVO().getCodigo() != null && obj.getTipoDocumentoVO().getCodigo() != 0) {
                sqlInserir.setInt(i++, obj.getTipoDocumentoVO().getCodigo());
            } else {
                sqlInserir.setNull(i++, 0);
            }
            if (obj.getFormaPagamentoVO().getCodigo() != null && obj.getFormaPagamentoVO().getCodigo() != 0) {
                sqlInserir.setInt(i++, obj.getFormaPagamentoVO().getCodigo());
            } else {
                sqlInserir.setNull(i++, 0);
            }
            sqlInserir.setInt(i++, obj.getTipoES().getCodigo());
            sqlInserir.setString(i++, obj.getDescricao());
            sqlInserir.setDouble(i++, obj.getValor());
            sqlInserir.setString(i++, obj.getNumeroDocumento());
            sqlInserir.execute();
        }
    }

    public void verificacarObjetoIncorretoAntesDeGravar(MovContaRateioVO movContaRateioVO, MovContaVO movConta) {
        boolean deposito = movConta != null && movConta.getTipoOperacaoLancamento() != null &&
                (movConta.getTipoOperacaoLancamento().equals(TipoOperacaoLancamento.DEPOSITO));
        boolean transferencia = movConta != null && movConta.getTipoOperacaoLancamento() != null &&
                (movConta.getTipoOperacaoLancamento().equals(TipoOperacaoLancamento.TRANSFERENCIA));
        boolean pagamento = movConta != null && movConta.getTipoOperacaoLancamento() != null &&
                (movConta.getTipoOperacaoLancamento().equals(TipoOperacaoLancamento.PAGAMENTO));
        boolean recebimento = movConta != null && movConta.getTipoOperacaoLancamento() != null &&
                (movConta.getTipoOperacaoLancamento().equals(TipoOperacaoLancamento.RECEBIMENTO));

        if (deposito || recebimento) {
            verificarDivergenciaNoRateioParaEntradas(movContaRateioVO);
        } else if (transferencia) {
            verificarDivergenciaNoRateioParaTransferencias(movConta, movContaRateioVO);
        } else if (pagamento) {
            verificarDivergenciaNoRateioParaSaidas(movContaRateioVO);
        }
    }

    /**
     * Altera o movContaRateioVO
     * @param obj
     * @throws Exception
     */
    public void alterar(MovContaRateioVO obj) throws Exception {
        try {
            //a validação será falsa para todos os campos
            //pois a validação é feita somente quando há mais de um movContaRateioVO
            MovContaRateioVO.validarDados(obj, false, true);
            //alterar(getIdEntidade());

            StringBuilder sql = new StringBuilder();
            sql.append("UPDATE movcontarateio SET ");
            sql.append("movconta = ? ,");
            sql.append("planoconta = ?, ");
            sql.append("centrocusto = ?, ");
            sql.append("tipodocumento = ?, ");
            sql.append("formapagamento = ?, ");
            sql.append("tipoes = ?, ");
            sql.append("descricao = ?, ");
            sql.append("valor = ? ");
            sql.append("numerodocumento = ? ");
            sql.append("WHERE  codigo = ?");
            try (PreparedStatement sqlAlterar = con.prepareStatement(sql.toString())) {
                int i = 1;
                sqlAlterar.setInt(i++, obj.getMovContaVO());
                if (obj.getPlanoContaVO().getCodigo() != null && obj.getPlanoContaVO().getCodigo() != 0) {
                    sqlAlterar.setInt(i++, obj.getPlanoContaVO().getCodigo());
                } else {
                    sqlAlterar.setNull(i++, 0);
                }
                if (obj.getCentroCustoVO().getCodigo() != null && obj.getCentroCustoVO().getCodigo() != 0) {
                    sqlAlterar.setInt(i++, obj.getCentroCustoVO().getCodigo());
                } else {
                    sqlAlterar.setNull(i++, 0);
                }
                if (obj.getTipoDocumentoVO().getCodigo() != null && obj.getTipoDocumentoVO().getCodigo() != 0) {
                    sqlAlterar.setInt(i++, obj.getTipoDocumentoVO().getCodigo());
                } else {
                    sqlAlterar.setNull(i++, 0);
                }
                sqlAlterar.setInt(i++, obj.getFormaPagamentoVO().getCodigo());
                sqlAlterar.setInt(i++, obj.getTipoES().getCodigo());
                sqlAlterar.setString(i++, obj.getDescricao());
                sqlAlterar.setDouble(i++, obj.getValor());
                sqlAlterar.setInt(i++, obj.getCodigo());
                sqlAlterar.setString(i++, obj.getNumeroDocumento());
                sqlAlterar.execute();
            }
        } catch (Exception e) {            
            throw e;
        }
    }

    public void alterarFormaPagtoValor(MovContaRateioVO obj) throws Exception{
        String sql = "update movContaRateio set formaPagamento = ?, valor = ? where codigo = ?";
        try (PreparedStatement pst = this.con.prepareStatement(sql)) {
            pst.setInt(1, obj.getFormaPagamentoVO().getCodigo());
            pst.setDouble(2, obj.getValor());
            pst.setInt(3, obj.getCodigo());
            pst.execute();
        }
    }

    /**
     * Exclui a movContaRateioVO
     * @param obj
     * @throws Exception
     */
    @Override
    public void excluir(MovContaRateioVO obj) throws Exception {
        try {
            con.setAutoCommit(false);
            //excluir(getIdEntidade());
            String sql = "DELETE FROM movcontarateio WHERE codigo = ?";
            try (PreparedStatement sqlExcluir = con.prepareStatement(sql)) {
                sqlExcluir.setInt(1, obj.getCodigo());
                sqlExcluir.execute();
            }
            con.commit();
        } catch (Exception e) {
            con.rollback();
            throw e;
        } finally {
            con.setAutoCommit(true);
        }
    }

    /**
     * Operação responsável por incluir objetos da <code>MovContaRateioVO</code> no BD.
     * Garantindo o relacionamento com a entidade principal <code>MovContaVO</code> através do atributo de vínculo.
     * @param objetos List contendo os objetos a serem gravados no BD da classe.
     * @exception Exception  Erro de conexão com o BD ou restrição de acesso a esta operação.
     */
    @Override
    public void incluirMovContasRateio(MovContaVO movConta, List objetos, boolean validarFormaPagamento) throws Exception {
        Iterator e = objetos.iterator();

        while (e.hasNext()) {
            MovContaRateioVO obj = (MovContaRateioVO) e.next();
            obj.setMovContaVO(movConta.getCodigo().intValue());
            incluir(obj, movConta, validarFormaPagamento);
        }
    }

    public void incluirMovContasRateio(MovContaVO movConta, List rateios) throws Exception {
        Iterator e = rateios.iterator();

        while (e.hasNext()) {
            MovContaRateioVO obj = (MovContaRateioVO) e.next();
            obj.setMovContaVO(movConta.getCodigo().intValue());
            incluir(obj, movConta);
        }
    }

    private static void verificarDivergenciaNoRateioParaEntradas(MovContaRateioVO obj) {
        //as vezes acontece de vir um pagamento (movconta) de ENTRADA e gravar no banco o movcontaRateio de saída.
        //Como não conseguimos simular o problema, então tratar antes de incluir no banco para não deixar gravar lançamentos movconta de Entrada com movcontarateio de Saída.
        boolean movContaRateioDeSaida = obj.getTipoES() != null && obj.getTipoES().equals(TipoES.SAIDA);
        if (movContaRateioDeSaida || obj.getTipoES() == null) {
            Uteis.logarDebug("verificarDivergenciaNoRateioParaEntradas");
            obj.setTipoES(TipoES.ENTRADA);
        }
    }

    private static void verificarDivergenciaNoRateioParaSaidas(MovContaRateioVO obj) {
        //as vezes acontece de vir um pagamento (movconta) de SAÍDA e gravar no banco o movcontaRateio de Entrada.
        //Como não conseguimos simular o problema, então tratar antes de incluir no banco para não deixar gravar lançamentos movconta de Saída com movcontarateio de Entrada.
        boolean movContaRateioDeEntrada = obj.getTipoES() != null && obj.getTipoES().equals(TipoES.ENTRADA);
        if (movContaRateioDeEntrada || obj.getTipoES() == null) {
            Uteis.logarDebug("verificarDivergenciaNoRateioParaSaidas - " + movContaRateioDeEntrada);
            obj.setTipoES(TipoES.SAIDA);
        }
    }

    private static void verificarDivergenciaNoRateioParaTransferencias(MovContaVO movContaVO, MovContaRateioVO movContaRateio) {
        //as vezes acontece de vir um pagamento (movconta) de ENTRADA e gravar no banco o movcontaRateio de saída.
        //Como não conseguimos simular o problema, então tratar antes de incluir no banco para não deixar gravar lançamentos movconta de Entrada com movcontarateio de Saída ou vice-versa.

        //as variáveis estão redundantes abaixo para facilitar a leitura em caso de manutenção

        boolean movContaDeSaida = movContaVO.getDescricao().contains("Saída de valor - CONTA DESTINO") || movContaVO.getDescricao().contains("Pagamento de parcela de cheque devolvido");
        boolean movContaRateioDeEntrada = movContaRateio.getTipoES() != null && movContaRateio.getTipoES().equals(TipoES.ENTRADA);

        if ((!movContaDeSaida && movContaRateio.getTipoES() == null) || (!movContaDeSaida && !movContaRateioDeEntrada)) {
            Uteis.logarDebug("verificarDivergenciaNoRateioParaTransferencias 1 - " + movContaDeSaida + " " + movContaRateioDeEntrada);
            movContaRateio.setTipoES(TipoES.ENTRADA);
            return;
        }

        if ((movContaDeSaida && movContaRateioDeEntrada) ||
                (movContaDeSaida && movContaRateio.getTipoES() == null)) {
            Uteis.logarDebug("verificarDivergenciaNoRateioParaTransferencias 2 - " + movContaDeSaida + " " + movContaRateioDeEntrada);
            movContaRateio.setTipoES(TipoES.SAIDA);
        }
    }

    /**
     * Operação responsável por alterar todos os objetos da <code>MovContaRateioVO</code> contidos em um Hashtable no BD.
     * @param objetos  List com os objetos a serem alterados ou incluídos no BD.
     * @exception Exception  Erro de conexão com o BD ou restrição de acesso a esta operação.
     */
    @Override
    public void alterarMovContasRateio(MovContaVO movConta, List<MovContaRateioVO> objetos, boolean validarFormaPagamento) throws Exception {
        String str = "DELETE FROM movContaRateio WHERE movConta = " + movConta.getCodigo();
        try (PreparedStatement sqlExcluir = con.prepareStatement(str)) {
            sqlExcluir.execute();
        }
        Iterator e = objetos.iterator();
        while (e.hasNext()) {
            MovContaRateioVO obj = (MovContaRateioVO) e.next();
            obj.setMovContaVO(movConta.getCodigo());
            obj.getCodigoLog();
            incluir(obj, movConta, validarFormaPagamento);
        }
    }

    /**
     * Operação responsável por excluir todos os objetos da <code>MovContaRateioVO</code> no BD.
     * Faz uso da operação <code>excluir</code> disponível na classe <code>MovContaRateio</code>.
     * @param movConta campo chave para exclusão dos objetos no BD.
     * @exception Exception  Erro de conexão com o BD ou restrição de acesso a esta operação.
     */
    @Override
    public void excluirMovContasRateio(Integer movConta) throws Exception {
        //excluir(getIdEntidade());
        String sql = "DELETE FROM movContaRateio WHERE movConta = ?";
        try (PreparedStatement sqlExcluir = con.prepareStatement(sql)) {
            sqlExcluir.setInt(1, movConta.intValue());
            sqlExcluir.execute();
        }
    }

    /**
     * Consulta MovConta e MovContaRateio para preencher a edição de registro
     * escolhido de consulta de Lançamentos Financeiros
     * @param movConta
     * @param nivelMontarDados
     * @return
     * @throws Exception
     */
    @Override
    public List<MovContaRateioVO> consultarPorMovConta(Integer movConta, int nivelMontarDados) throws Exception {
        StringBuilder sql = new StringBuilder();
        sql.append("SELECT movcontarateio.*, planoconta.codigo as codigoPlanoConta, "
                + "planoconta.nome as nomePlanoConta , "
                + "planoConta.codigoPlanoContas as codigoPlanoContas ,"
                + "planoConta.tipoes as tipoPlanoConta, "
                + " centrocusto.codigo as codigoCentroCusto, "
                + "centrocusto.codigoCentroCustos as codigoCentroCustos, "
                + "centrocusto.nome as nomeCentroCusto, "
                + "formapagamento.descricao as descricaoFormaPagamento, "
                + "formapagamento.tipoformapagamento as tipoFormaPagamento, "
                + "tipodocumento.descricao as descricaoTipoDocumento");
        sql.append(" from movcontarateio ");
        sql.append(" left join centrocusto on movcontarateio.centrocusto = centrocusto.codigo ");
        sql.append(" left join planoconta on movcontarateio.planoconta = planoconta.codigo ");
        sql.append(" left join formapagamento on formapagamento.codigo = movcontarateio.formapagamento ");
        sql.append(" left join tipodocumento on tipodocumento.codigo = movcontarateio.tipodocumento ");
        sql.append("where movconta = ?");
        try (PreparedStatement pst = con.prepareStatement(sql.toString())) {
            pst.setInt(1, movConta.intValue());
            try (ResultSet tabelaResultado = pst.executeQuery()) {
                return (montarDadosConsulta(tabelaResultado, nivelMontarDados, con));
            }
        }
    }

    /**
     *
     * @param nivelMontarDados
     * @return
     * @throws Exception
     */
    @Override
    public List consultarTodas(int nivelMontarDados) throws Exception {
        String sqlStr = "SELECT * FROM movcontarateio";
        try (Statement stm = con.createStatement()) {
            try (ResultSet tabelaResultado = stm.executeQuery(sqlStr)) {
                return (montarDadosConsulta(tabelaResultado, nivelMontarDados, con));
            }
        }
    }

    /**
     * MontarDados para varios objetos
     * @param tabelaResultado
     * @param nivelMontarDados
     * @return
     * @throws Exception
     */
    public static List montarDadosConsulta(ResultSet tabelaResultado,
            int nivelMontarDados, Connection con) throws Exception {
        List vetResultado = new ArrayList();
        while (tabelaResultado.next()) {
            MovContaRateioVO obj = new MovContaRateioVO();
            obj = montarDados(tabelaResultado, nivelMontarDados, con);
            vetResultado.add(obj);
        }
        return vetResultado;
    }

    public static MovContaRateioVO montarDados(ResultSet dadosSQL,
            int nivelMontarDados, Connection con) throws Exception {
        MovContaRateioVO obj = new MovContaRateioVO();
        obj.setCodigo(new Integer(dadosSQL.getInt("codigo")));
        obj.setCodigoLog(new Integer(dadosSQL.getInt("codigo")));
        obj.setMovContaVO(dadosSQL.getInt("movconta"));
        obj.setPlanoContaVO(new PlanoContaTO());
        obj.getPlanoContaVO().setCodigo(dadosSQL.getInt("planoconta"));
        obj.setCentroCustoVO(new CentroCustoTO());
        obj.getCentroCustoVO().setCodigo(dadosSQL.getInt("centrocusto"));
        obj.setTipoDocumentoVO(new TipoDocumentoVO());
        obj.getTipoDocumentoVO().setCodigo(dadosSQL.getInt("tipodocumento"));
        obj.setFormaPagamentoVO(new FormaPagamentoVO());
        obj.getFormaPagamentoVO().setCodigo(dadosSQL.getInt("formapagamento"));
        obj.getTipoES();
		obj.setTipoES(TipoES.getTipoPadrao(dadosSQL.getInt("tipoes")));
        obj.setDescricao(dadosSQL.getString("descricao"));
        obj.setValor(dadosSQL.getDouble("valor"));
        obj.setNumeroDocumento(dadosSQL.getString("numerodocumento"));
        obj.setNovoObj(false);
        if (nivelMontarDados == Uteis.NIVELMONTARDADOS_DADOSENTIDADESPRINCIPAIS) {
            obj.setPlanoContaVO(montarDadosPlanoConta(dadosSQL, Uteis.NIVELMONTARDADOS_DADOSBASICOS, con));
            obj.setCentroCustoVO(montarDadosCentroCusto(dadosSQL, Uteis.NIVELMONTARDADOS_DADOSBASICOS));
            try{
            	obj.getFormaPagamentoVO().setDescricao(dadosSQL.getString("descricaoFormaPagamento"));
                obj.getTipoDocumentoVO().setDescricao(dadosSQL.getString("descricaoTipoDocumento"));
                obj.getFormaPagamentoVO().setTipoFormaPagamento(dadosSQL.getString("tipoFormaPagamento"));
            }catch (Exception e) {
            	obj.getFormaPagamentoVO().setDescricao("");
                obj.getTipoDocumentoVO().setDescricao("");
			}
            obj.getFormaPagamentoVO().setDescricao(dadosSQL.getString("descricaoFormaPagamento"));
            obj.getTipoDocumentoVO().setDescricao(dadosSQL.getString("descricaoTipoDocumento"));
        }
        if(nivelMontarDados == Uteis.NIVELMONTARDADOS_DADOSBASICOS){
        	obj.setPlanoContaVO(montarDadosPlanoConta(dadosSQL, Uteis.NIVELMONTARDADOS_DADOSBASICOS,con));
            obj.setCentroCustoVO(montarDadosCentroCusto(dadosSQL, Uteis.NIVELMONTARDADOS_DADOSBASICOS));
        	obj.setMovConta(getFacade().getFinanceiro().getMovConta().consultarPorCodigo(obj.getMovContaVO(), Uteis.NIVELMONTARDADOS_DADOSENTIDADESPRINCIPAIS));
        	obj.setFormaPagamentoVO(getFacade().getFormaPagamento().consultarPorChavePrimaria(obj.getFormaPagamentoVO().getCodigo(), nivelMontarDados));
        }
        if(nivelMontarDados == Uteis.NIVELMONTARDADOS_MINIMOS){
        	obj.setMovConta(getFacade().getFinanceiro().getMovConta().consultarPorCodigo(obj.getMovContaVO(), Uteis.NIVELMONTARDADOS_DADOSBASICOS));
        	obj.setFormaPagamentoVO(getFacade().getFormaPagamento().consultarPorChavePrimaria(obj.getFormaPagamentoVO().getCodigo(), nivelMontarDados));
        }
        return obj;
    }

    public static PlanoContaTO montarDadosPlanoConta(ResultSet dadosSQL,
            int nivelMontarDados, Connection con) throws SQLException, Exception {
        PlanoContaTO obj = new PlanoContaTO();
        if (nivelMontarDados == Uteis.NIVELMONTARDADOS_DADOSBASICOS) {
            obj.setCodigo(dadosSQL.getInt("codigoPlanoConta"));
            obj.setCodigoPlano(dadosSQL.getString("codigoPlanoContas"));
            obj.setTipoPadrao(obj.getTipoPadrao().getTipoPadrao(dadosSQL.getInt("tipoPlanoConta")));
            obj.setDescricao(dadosSQL.getString("nomePlanoConta"));
            try (ResultSet haFilhos = SuperFacadeJDBC.criarConsulta("select codigo from planoconta where codigoplanocontas like '"
                    + obj.getCodigoPlano() + ".%'", con)) {
                obj.setLeaf(!haFilhos.next());
            }
        }
        return obj;
    }

    public static CentroCustoTO montarDadosCentroCusto(ResultSet dadosSQL,
            int nivelMontarDados) throws SQLException {
        CentroCustoTO obj = new CentroCustoTO();
        if (nivelMontarDados == Uteis.NIVELMONTARDADOS_DADOSBASICOS) {
            obj.setCodigo(dadosSQL.getInt("codigoCentroCusto"));
            obj.setCodigoCentro(dadosSQL.getString("codigoCentroCustos"));
            obj.setDescricao(dadosSQL.getString("nomeCentroCusto"));
        }
        return obj;
    }
    
    public List<MovContaRateioVO> consultarPorConta(int conta, Date inicio, Date fim) throws Exception{
    	StringBuilder sql = new StringBuilder();
		sql.append("SELECT mcr.* FROM movcontarateio mcr ");
		sql.append("INNER JOIN movconta mc ON mcr.movconta = mc.codigo ");
		sql.append("WHERE mc.conta = ? ");
		sql.append("AND mc.dataquitacao BETWEEN ? AND ? ");
		sql.append("ORDER BY mc.dataquitacao");
        try (PreparedStatement stm = con.prepareStatement(sql.toString())) {
            stm.setInt(1, conta);
            stm.setTimestamp(2, Uteis.getDataHoraJDBC(inicio, "00:00:00"));
            stm.setTimestamp(3, Uteis.getDataHoraJDBC(fim, "23:59:59"));
            return montarDadosConsulta(stm.executeQuery(), Uteis.NIVELMONTARDADOS_MINIMOS, con);
        }
    }
    
    public Boolean verificarExistenciaPlano(Integer codigoPlano) throws Exception{
    	return MovContaRateio.criarConsulta("SELECT * FROM movcontarateio WHERE planoconta = "+codigoPlano, con).next();
    }


    public List<FluxoCaixaTO> consultarFluxoCaixa(Date dataInicial, Date dataFinal, boolean previsto, Integer empresa) throws Exception {
        List<FluxoCaixaTO> retorno = new ArrayList<FluxoCaixaTO>();
        StringBuilder sql = new StringBuilder();
        sql.append("select \n");
        if (previsto) {
            sql.append("mc.datavencimento::date as dia, \n");
        } else {
            sql.append("mc.dataquitacao::date as dia, \n");
        }
        sql.append("mc.codigo, \n");
        sql.append("mc.descricao, \n");
        sql.append("mcr.planoconta, \n");
        sql.append("mcr.tipoes, \n");
        sql.append("sum(mcr.valor) as total \n");
        sql.append("from movcontarateio mcr \n");
        sql.append("inner join movconta mc on mc.codigo = mcr.movconta \n");
        sql.append("where 1 = 1 \n");
        sql.append("and mc.empresa = ? \n");
        sql.append("and mc.tipooperacao in (");
        sql.append(TipoOperacaoLancamento.PAGAMENTO.getCodigo()).append(",");
        sql.append(TipoOperacaoLancamento.RECEBIMENTO.getCodigo()).append(",");
        sql.append(TipoOperacaoLancamento.AJUSTESALDO.getCodigo()).append(") \n");
        if (previsto) {
            sql.append("and mc.datavencimento between ? and ? \n");
        } else {
            sql.append("and mc.dataquitacao between ? and ? \n");
        }
        sql.append("group by 1,2,3,4,5 \n");
        sql.append("order by 1 ");
        try (PreparedStatement stm = con.prepareStatement(sql.toString())) {
            stm.setInt(1, empresa);
            stm.setTimestamp(2, Uteis.getDataHoraJDBC(dataInicial, "00:00:00"));
            stm.setTimestamp(3, Uteis.getDataHoraJDBC(dataFinal, "23:59:59"));
            try (ResultSet rs = stm.executeQuery()) {
                while (rs.next()) {
                    FluxoCaixaTO obj = new FluxoCaixaTO();
                    obj.setDia(rs.getDate("dia"));
                    obj.setCodigoMovConta(rs.getInt("codigo"));
                    obj.setDescricao(rs.getString("descricao"));
                    obj.setTipoES(TipoES.getTipoPadrao(rs.getInt("tipoes")));
                    Integer codPlanoConta = rs.getInt("planoconta");
                    if (!UteisValidacao.emptyNumber(codPlanoConta)) {
                        obj.setPlanoConta(new PlanoContaTO());
                        obj.getPlanoConta().setCodigo(codPlanoConta);
                    }
                    if (obj.getTipoES().equals(TipoES.ENTRADA)) {
                        obj.setEntradas(rs.getDouble("total"));
                    } else if (obj.getTipoES().equals(TipoES.SAIDA)) {
                        obj.setSaidas(-1 * rs.getDouble("total"));
                    }

                    retorno.add(obj);
                }
            }
        }
        return retorno;
    }


    public void alterarValorMovContaRateioPeloCodigo(Integer codigo, Double valor) throws SQLException {
        String sql = "UPDATE movcontarateio SET valor=? WHERE codigo=?";

        PreparedStatement ps= con.prepareStatement(sql);
        ps.setDouble(1, valor);
        ps.setInt(2, codigo);
        ps.execute();

    }

    public void alterarValorMovContaRateio(MovContaRateioVO movContaRateioVO) throws SQLException {
        String sql = "UPDATE movcontarateio SET valor = ?, formaPagamento = null WHERE codigo = ?";
        PreparedStatement ps = con.prepareStatement(sql);
        ps.setDouble(1, movContaRateioVO.getValor());
        ps.setInt(2, movContaRateioVO.getCodigo());
        ps.execute();

    }
}

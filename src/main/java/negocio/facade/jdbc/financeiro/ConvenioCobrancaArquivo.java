package negocio.facade.jdbc.financeiro;

import negocio.comuns.financeiro.ConvenioCobrancaArquivoVO;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisValidacao;
import negocio.facade.jdbc.arquitetura.SuperEntidade;
import negocio.interfaces.financeiro.ConvenioCobrancaArquivoInterfaceFacade;
import servicos.propriedades.PropsService;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.Types;
import java.util.ArrayList;
import java.util.List;

/**
 * Created with IntelliJ IDEA.
 * User: <PERSON>
 * Date: 14/07/2022
 */

public class ConvenioCobrancaArquivo extends SuperEntidade implements ConvenioCobrancaArquivoInterfaceFacade {

    public ConvenioCobrancaArquivo() throws Exception {
        super();
    }

    public ConvenioCobrancaArquivo(Connection conexao) throws Exception {
        super(conexao);
    }

    @Override
    public void incluir(ConvenioCobrancaArquivoVO obj) throws Exception {
        ConvenioCobrancaArquivoVO.validarDados(obj);
        String sql = "INSERT INTO ConvenioCobrancaArquivo( convenioCobranca, nomeArquivoOriginal, arquivo, "
                + "dataUpload, senha, usuario) "
                + "VALUES (?, ?, ?, ?, ?, ?)";
        try (PreparedStatement sqlInserir = con.prepareStatement(sql)) {
            int i = 0;
            sqlInserir.setInt(++i, obj.getConvenioCobranca());
            sqlInserir.setString(++i, obj.getNomeArquivoOriginal());
            sqlInserir.setBytes(++i, obj.getArquivo());
            sqlInserir.setTimestamp(++i, Uteis.getDataJDBCTimestamp(obj.getDataUpload()));
            if (!UteisValidacao.emptyString(obj.getSenha())) {
                String senhaCrypt = Uteis.encriptar(obj.getSenha(), PropsService.getPropertyValue(PropsService.chaveCriptCertPrivado));
                sqlInserir.setString(++i, senhaCrypt);
                obj.setSenha(senhaCrypt);
            } else {
                sqlInserir.setNull(++i, Types.NULL);
            }
            sqlInserir.setInt(++i, obj.getUsuario());
            sqlInserir.execute();
        }
        obj.setCodigo(obterValorChavePrimariaCodigo());
        obj.setNovoObj(false);
    }

    @Override
    public void excluir(int codConvenioCobranca) throws Exception {
        String sql = "DELETE FROM conveniocobrancaarquivo WHERE conveniocobranca = ?";
        try (PreparedStatement ps = con.prepareStatement(sql)) {
            ps.setInt(1, codConvenioCobranca);
            ps.execute();
        }
    }

    @Override
    public void excluirPorChavePrimaria(int codigo) throws Exception {
        String sql = "DELETE FROM conveniocobrancaarquivo WHERE codigo = ?";
        try (PreparedStatement ps = con.prepareStatement(sql)) {
            ps.setInt(1, codigo);
            ps.execute();
        }
    }


    @Override
    public ConvenioCobrancaArquivoVO consultarPorConvenioCobranca(int convenioCobranca, int nivelMontarDados) throws Exception {
        String sql = "SELECT * FROM conveniocobrancaarquivo WHERE convenioCobranca = " + convenioCobranca;
        try (PreparedStatement ps = con.prepareStatement(sql)) {
            try (ResultSet rs = ps.executeQuery()) {
                if (rs.next()) {
                    return montarDados(rs);
                }
                return null;
            }
        }
    }

    @Override
    public List<ConvenioCobrancaArquivoVO> consultarListaPorConvenioCobranca(int convenioCobranca, boolean montarArquivo) throws Exception {
        String sql = "SELECT * FROM conveniocobrancaarquivo WHERE convenioCobranca = " + convenioCobranca;
        List<ConvenioCobrancaArquivoVO> lista = new ArrayList<>();
        try (PreparedStatement ps = con.prepareStatement(sql)) {
            try (ResultSet rs = ps.executeQuery()) {
                while(rs.next()) {
                    if (montarArquivo) {
                        lista.add(montarDadosComArquivo(rs));
                    } else {
                        lista.add(montarDados(rs));
                    }
                }
            }
            return lista;
        }
    }

    @Override
    public byte[] consultarArquivoBytePorChavePrimaria(int codigo) throws Exception {
        String sql = "SELECT arquivo FROM conveniocobrancaarquivo WHERE codigo = " + codigo;
        byte[] arquivo = null;
        try (PreparedStatement ps = con.prepareStatement(sql)) {
            try (ResultSet rs = ps.executeQuery()) {
                while(rs.next()) {
                    return rs.getBytes("arquivo");
                }
            }
            return arquivo;
        }
    }

    public static ConvenioCobrancaArquivoVO montarDados(ResultSet dadosSQL) throws Exception {
        //TODO não colocar para montar o arquivo em bytes aqui para evitar estouro de memória (coluna 'arquivo' da tabela conveniocobrancaarquivo)
        ConvenioCobrancaArquivoVO obj = new ConvenioCobrancaArquivoVO();
        obj.setCodigo(dadosSQL.getInt("codigo"));
        obj.setConvenioCobranca(dadosSQL.getInt("conveniocobranca"));
        obj.setNomeArquivoOriginal(dadosSQL.getString("nomearquivooriginal"));
        obj.setDataUpload(dadosSQL.getTimestamp("dataupload"));
        obj.setDataUploadString(Uteis.getDataComHHMM(dadosSQL.getTimestamp("dataupload")));
        obj.setUsuario(dadosSQL.getInt("usuario"));
        obj.setSenha(dadosSQL.getString("senha"));
        return obj;
    }

    public static ConvenioCobrancaArquivoVO montarDadosComArquivo(ResultSet dadosSQL) throws Exception {
        //TODO muito cuidado onde vai chamar esse método, pois como ele monta o arquivo bytes, pode dar estouro de memória.
        ConvenioCobrancaArquivoVO obj = new ConvenioCobrancaArquivoVO();
        obj.setCodigo(dadosSQL.getInt("codigo"));
        obj.setArquivo(dadosSQL.getBytes("arquivo"));
        obj.setConvenioCobranca(dadosSQL.getInt("conveniocobranca"));
        obj.setNomeArquivoOriginal(dadosSQL.getString("nomearquivooriginal"));
        obj.setDataUpload(dadosSQL.getTimestamp("dataupload"));
        obj.setDataUploadString(Uteis.getDataComHHMM(dadosSQL.getTimestamp("dataupload")));
        obj.setUsuario(dadosSQL.getInt("usuario"));
        obj.setSenha(dadosSQL.getString("senha"));
        return obj;
    }

}

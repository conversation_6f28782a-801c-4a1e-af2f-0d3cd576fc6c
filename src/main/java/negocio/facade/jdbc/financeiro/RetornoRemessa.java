/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package negocio.facade.jdbc.financeiro;

import negocio.comuns.financeiro.RetornoRemessaVO;
import negocio.comuns.utilitarias.Uteis;
import negocio.facade.jdbc.arquitetura.SuperEntidade;
import negocio.facade.jdbc.arquitetura.Usuario;
import negocio.interfaces.financeiro.RetornoRemessaInterfaceFacade;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.Statement;
import java.sql.Types;
import java.util.ArrayList;
import java.util.List;

public class RetornoRemessa extends SuperEntidade implements RetornoRemessaInterfaceFacade {

    public RetornoRemessa() throws Exception {
    }

    public RetornoRemessa(Connection con) throws Exception {
        super(con);
    }

    public static List<RetornoRemessaVO> montarDadosConsulta(ResultSet tabelaResultado, Connection c) throws Exception {
        List<RetornoRemessaVO> vetResultado = new ArrayList<RetornoRemessaVO>();
        while (tabelaResultado.next()) {
            RetornoRemessaVO obj = montarDados(tabelaResultado, c);
            vetResultado.add(obj);
        }
        return vetResultado;
    }

    private static RetornoRemessaVO montarDados(ResultSet ds, Connection c) throws Exception {

        RetornoRemessaVO obj = new RetornoRemessaVO();

        obj.setNovoObj(false);
        obj.setCodigo(ds.getInt("codigo"));
        obj.setNomeArquivo(ds.getString("nomeArquivo"));
        obj.setDataPrevistaCredito(ds.getDate("dataprevistacredito"));
        obj.setQuantidadeDeItens(ds.getInt("quantidadeitens"));
        obj.setValorTotalDoArquivo(ds.getDouble("valortotalarquivo"));
        obj.setQuantidadeSucesso(ds.getInt("quantidadesucesso"));
        obj.setValorSucesso(ds.getDouble("valorsucesso"));
        obj.setQuantidadeErro(ds.getInt("quantidadeerro"));
        obj.setValorErro(ds.getDouble("valorerro"));
        obj.setDataProcessamento(ds.getDate("dataprocessamento"));
        obj.setDataUltimoProcessamento(ds.getDate("dataultimoprocessamento"));
        obj.setEmpresaSolicitante(ds.getString("empresasolicitante"));
        obj.setUserSolicitante(ds.getString("usersolicitante"));

        montarDadosUsuario(ds.getInt("usuario"), obj, c);

        return obj;
    }


    private static void montarDadosUsuario(int codigo, RetornoRemessaVO obj, Connection c) throws Exception {
        Usuario usuarioFacade = new Usuario(c);
        if (codigo != 0) {
            obj.setUsuarioVO(usuarioFacade.consultarPorChavePrimaria(codigo, Uteis.NIVELMONTARDADOS_MINIMOS));
        }
    }

    @Override
    public void incluir(RetornoRemessaVO obj) throws Exception {
        try {
            con.setAutoCommit(false);
            String sql = "INSERT INTO retornoremessa(nomeArquivo, dataprevistacredito, quantidadeitens, valortotalarquivo,\n" +
                    "quantidadesucesso, valorsucesso, quantidadeerro, valorerro, dataprocessamento, dataultimoprocessamento,\n" +
                    "arquivoretorno, usuario, empresasolicitante, usersolicitante) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)";

            PreparedStatement ps = con.prepareStatement(sql);
            int i = 0;
            ps.setString(++i, obj.getNomeArquivo());
            if (obj.getDataPrevistaCredito() != null) {
                ps.setTimestamp(++i, Uteis.getDataJDBCTimestamp(obj.getDataPrevistaCredito()));
            } else {
                ps.setNull(++i, Types.TIMESTAMP);
            }
            ps.setInt(++i, obj.getQuantidadeDeItens());
            ps.setDouble(++i, obj.getValorTotalDoArquivo());
            ps.setInt(++i, obj.getQuantidadeSucesso());
            ps.setDouble(++i, obj.getValorSucesso());
            ps.setInt(++i, obj.getQuantidadeErro());
            ps.setDouble(++i, obj.getValorErro());
            if (obj.getDataProcessamento() != null) {
                ps.setTimestamp(++i, Uteis.getDataJDBCTimestamp(obj.getDataProcessamento()));
            } else {
                ps.setNull(++i, Types.TIMESTAMP);
            }
            if (obj.getDataUltimoProcessamento() != null) {
                ps.setTimestamp(++i, Uteis.getDataJDBCTimestamp(obj.getDataUltimoProcessamento()));
            } else {
                ps.setNull(++i, Types.TIMESTAMP);
            }
            ps.setString(++i, obj.getArquivoRetorno());
            ps.setInt(++i, obj.getUsuarioVO().getCodigo());
            ps.setString(++i, obj.getEmpresaSolicitante());
            ps.setString(++i, obj.getUserSolicitante());
            ps.execute();

            obj.setCodigo(obterValorChavePrimariaCodigo());
            obj.setNovoObj(false);
            con.commit();
        } catch (Exception e) {
            con.rollback();
            throw e;
        } finally {
            con.setAutoCommit(true);
        }

    }

    @Override
    public void alterar(RetornoRemessaVO obj, boolean temArquivo) throws Exception {
        StringBuilder sql = new StringBuilder("UPDATE retornoremessa "
                + "SET nomeArquivo = ?, dataprevistacredito = ?, quantidadeitens = ?, valortotalarquivo = ?,\n" +
                "quantidadesucesso = ?, valorsucesso = ?, quantidadeerro = ?, valorerro = ?, dataprocessamento = ?,\n" +
                "dataultimoprocessamento = ?, usuario = ?\n");
        if (temArquivo) {
            sql.append(", arquivoretorno = ?\n");
        }
        sql.append(", empresasolicitante = ?, usersolicitante = ? \n");
        sql.append("WHERE codigo = ?");
        //metodo alterar pode não sobrepor a coluna DETAIL, pois ela não é montada na consulta
        PreparedStatement ps = con.prepareStatement(sql.toString());
        int i = 0;
        ps.setString(++i, obj.getNomeArquivo());
        ps.setTimestamp(++i, Uteis.getDataJDBCTimestamp(obj.getDataPrevistaCredito()));
        ps.setInt(++i, obj.getQuantidadeDeItens());
        ps.setDouble(++i, obj.getValorTotalDoArquivo());
        ps.setInt(++i, obj.getQuantidadeSucesso());
        ps.setDouble(++i, obj.getValorSucesso());
        ps.setInt(++i, obj.getQuantidadeErro());
        ps.setDouble(++i, obj.getValorErro());
        ps.setTimestamp(++i, Uteis.getDataJDBCTimestamp(obj.getDataProcessamento()));
        ps.setTimestamp(++i, Uteis.getDataJDBCTimestamp(obj.getDataUltimoProcessamento()));
        ps.setInt(++i, obj.getUsuarioVO().getCodigo());
        if (temArquivo) {
            ps.setString(++i, obj.getArquivoRetorno());
        }
        ps.setString(++i, obj.getEmpresaSolicitante());
        ps.setString(++i, obj.getUserSolicitante());
        ps.setInt(++i, obj.getCodigo());

        ps.execute();
    }

    @Override
    public void alterarSomenteArquivo(RetornoRemessaVO obj) throws Exception {
        PreparedStatement ps = con.prepareStatement("UPDATE retornoremessa SET arquivoretorno = ?\n" + "WHERE codigo = ?");
        int i = 0;
        ps.setString(++i, obj.getArquivoRetorno());
        ps.setInt(++i, obj.getCodigo());

        ps.execute();
    }

    @Override
    public void excluir(RetornoRemessaVO obj) throws Exception {
        String sql = "DELETE FROM Remessa WHERE codigo = ?";
        PreparedStatement sqlExcluir = con.prepareStatement(sql);
        sqlExcluir.setInt(1, obj.getCodigo());
        sqlExcluir.execute();
    }

    public RetornoRemessaVO consultarPorNomeArquivo(String nomeArquivo) throws Exception {
        String sqlStr = "SELECT\n" +
                "  codigo,\n" +
                "  nomeArquivo,\n" +
                "  dataprevistacredito,\n" +
                "  quantidadeitens,\n" +
                "  valortotalarquivo,\n" +
                "  quantidadesucesso,\n" +
                "  valorsucesso,\n" +
                "  quantidadeerro,\n" +
                "  valorerro,\n" +
                "  dataprocessamento,\n" +
                "  dataultimoprocessamento,\n" +
                "  usuario,\n" +
                " usersolicitante,\n" +
                " empresasolicitante \n" +
                "FROM retornoremessa\n" +
                "WHERE nomearquivo like('" + nomeArquivo + "%')  ORDER BY codigo DESC LIMIT 1";
        Statement stm = con.createStatement();
        ResultSet tabelaResultado = stm.executeQuery(sqlStr);
        if (tabelaResultado.next()) {
            return montarDados(tabelaResultado, this.con);
        } else {
            return null;
        }
    }

    public RetornoRemessaVO consultarPorCodigo(Integer codigoRetornoRemessa) throws Exception {
        String sqlStr = "SELECT\n" +
                "  codigo,\n" +
                "  nomeArquivo,\n" +
                "  dataprevistacredito,\n" +
                "  quantidadeitens,\n" +
                "  valortotalarquivo,\n" +
                "  quantidadesucesso,\n" +
                "  valorsucesso,\n" +
                "  quantidadeerro,\n" +
                "  valorerro,\n" +
                "  dataprocessamento,\n" +
                "  dataultimoprocessamento,\n" +
                "  usuario,\n" +
                "  usersolicitante,\n" +
                "  empresasolicitante \n" +
                "FROM retornoremessa\n" +
                "WHERE codigo = " + codigoRetornoRemessa + ";";
        Statement stm = con.createStatement();
        ResultSet tabelaResultado = stm.executeQuery(sqlStr);
        if (tabelaResultado.next()) {
            return montarDados(tabelaResultado, this.con);
        } else {
            return null;
        }
    }

    public RetornoRemessaVO obterArquivoRetorno(RetornoRemessaVO obj) throws Exception {
        String sqlStr = "SELECT\n" +
                "  arquivoretorno\n" +
                "FROM retornoremessa\n" +
                "WHERE codigo = " + obj.getCodigo() + ";";
        Statement stm = con.createStatement();
        ResultSet tabelaResultado = stm.executeQuery(sqlStr);
        if (tabelaResultado.next()) {
            obj.setArquivoRetorno(tabelaResultado.getString("arquivoretorno"));
        }
        return obj;
    }

    public List<RetornoRemessaVO> consultarTodos() throws Exception {
        String sqlStr = "SELECT\n" +
                "  codigo,\n" +
                "  nomeArquivo,\n" +
                "  dataprevistacredito,\n" +
                "  quantidadeitens,\n" +
                "  valortotalarquivo,\n" +
                "  quantidadesucesso,\n" +
                "  valorsucesso,\n" +
                "  quantidadeerro,\n" +
                "  valorerro,\n" +
                "  dataprocessamento,\n" +
                "  dataultimoprocessamento,\n" +
                "  usuario,\n" +
                "  usersolicitante,\n" +
                "  empresasolicitante,\n" +
                "  arquivoretorno\n" +
                "FROM retornoremessa\n" +
                "  ORDER BY codigo DESC";
        Statement stm = con.createStatement();
        ResultSet tabelaResultado = stm.executeQuery(sqlStr);
        List<RetornoRemessaVO> retornos = new ArrayList<RetornoRemessaVO>();
        while (tabelaResultado.next()) {
            RetornoRemessaVO retornoRemessaVO = montarDados(tabelaResultado, this.con);
            retornoRemessaVO.setArquivoRetorno(tabelaResultado.getString("arquivoretorno"));
            retornos.add(retornoRemessaVO);
        }
        return retornos;
    }
}

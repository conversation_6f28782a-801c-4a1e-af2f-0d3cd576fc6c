package negocio.facade.jdbc.financeiro;

import negocio.comuns.financeiro.FormaPagamentoPerfilAcessoVO;
import negocio.comuns.financeiro.FormaPagamentoVO;
import negocio.comuns.utilitarias.UteisValidacao;
import negocio.facade.jdbc.arquitetura.SuperEntidade;
import negocio.interfaces.financeiro.FormaPagamentoPerfilAcessoInterfaceFacade;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.util.ArrayList;
import java.util.List;

public class FormaPagamentoPerfilAcesso extends SuperEntidade implements FormaPagamentoPerfilAcessoInterfaceFacade {

    public FormaPagamentoPerfilAcesso() throws Exception {
        super();
    }

    public FormaPagamentoPerfilAcesso(Connection con) throws Exception {
        super(con);
    }

    public void incluir(FormaPagamentoVO fp, List<FormaPagamentoPerfilAcessoVO> perfis) throws Exception {
        for (FormaPagamentoPerfilAcessoVO fpe : perfis) {
            fpe.setFormaPagamento(fp);
            if (UteisValidacao.emptyNumber(fpe.getCodigo())) {
                incluirSemCommit(fpe);
            } else {
                alterarSemCommit(fpe);
            }
        }
    }

    public void incluir(FormaPagamentoPerfilAcessoVO obj) throws Exception {
        try {
            con.setAutoCommit(false);
            incluirSemCommit(obj);
            con.commit();
        } catch (Exception e) {
            con.rollback();
            con.setAutoCommit(true);
            throw e;
        } finally {
            con.setAutoCommit(true);
        }
    }

    public void incluirSemCommit(FormaPagamentoPerfilAcessoVO obj) throws Exception {
        String insert = "INSERT INTO formapagamentoperfilacesso(formapagamento, perfilacesso) values (?, ?)";
        PreparedStatement insertStm = con.prepareStatement(insert);
        insertStm.setInt(1, obj.getFormaPagamento().getCodigo());
        insertStm.setInt(2, obj.getPerfilAcessoVO().getCodigo());
        insertStm.execute();
        obj.setCodigo(obterValorChavePrimariaCodigo());
    }

    public void alterarSemCommit(FormaPagamentoPerfilAcessoVO obj) throws Exception {
        String update = "UPDATE formapagamentoperfilacesso SET formapagamento = ? , perfilacesso = ? WHERE codigo = ?";
        PreparedStatement updateStm = con.prepareStatement(update);
        updateStm.setInt(1, obj.getFormaPagamento().getCodigo());
        updateStm.setInt(2, obj.getPerfilAcessoVO().getCodigo());
        updateStm.setInt(3, obj.getCodigo());
        updateStm.execute();
    }

    public void alterar(FormaPagamentoPerfilAcessoVO obj) throws Exception {
        try {
            con.setAutoCommit(false);
            alterarSemCommit(obj);
            con.commit();
        } catch (Exception e) {
            con.rollback();
            con.setAutoCommit(true);
            throw e;
        } finally {
            con.setAutoCommit(true);
        }
    }

    public void excluir(FormaPagamentoPerfilAcessoVO obj) throws Exception {
        String sql = "DELETE FROM formapagamentoperfilacesso WHERE codigo = ?";
        PreparedStatement sqlExcluir = con.prepareStatement(sql);
        sqlExcluir.setInt(1, obj.getCodigo());
        sqlExcluir.execute();
    }

    public List<FormaPagamentoPerfilAcessoVO> consultarPorForma(Integer forma) throws Exception {
        List<FormaPagamentoPerfilAcessoVO> perfis = new ArrayList<FormaPagamentoPerfilAcessoVO>();
        ResultSet rs = criarConsulta("SELECT * FROM formapagamentoperfilacesso WHERE formapagamento = " + forma, con);
        while (rs.next()) {
            perfis.add(montarDados(rs));
        }
        return perfis;
    }

    public String obterFormaPagamentoPorPerfilAcesso(Integer codigoPerfilAcesso) throws Exception {
        List<FormaPagamentoPerfilAcessoVO> perfis = new ArrayList<FormaPagamentoPerfilAcessoVO>();
        ResultSet rs = criarConsulta("select string_agg(fp.descricao, ' | ') as descricoes\n" +
                "from formapagamentoperfilacesso fpp\n" +
                "inner join formapagamento fp on fp.codigo = fpp.formapagamento\n" +
                "where perfilacesso = " + codigoPerfilAcesso, con);
        if (rs.next()) {
            return rs.getString("descricoes");
        }
        return null;
    }

    public FormaPagamentoPerfilAcessoVO montarDados(ResultSet rs) throws Exception {
        FormaPagamentoPerfilAcessoVO obj = new FormaPagamentoPerfilAcessoVO();
        obj.setCodigo(rs.getInt("codigo"));
        ResultSet rsPerfilAcesso = criarConsulta("SELECT codigo, nome FROM perfilacesso WHERE codigo = " + rs.getInt("perfilacesso"), con);
        if (rsPerfilAcesso.next()) {
            obj.getPerfilAcessoVO().setCodigo(rsPerfilAcesso.getInt("codigo"));
            obj.getPerfilAcessoVO().setNome(rsPerfilAcesso.getString("nome"));
        }
        ResultSet rsForma = criarConsulta("SELECT codigo, descricao FROM formapagamento WHERE codigo = " + rs.getInt("formapagamento"), con);
        if (rsForma.next()) {
            obj.getFormaPagamento().setDescricao(rsForma.getString("descricao"));
            obj.getFormaPagamento().setCodigo(rsForma.getInt("codigo"));
        }
        return obj;
    }
}


package negocio.facade.jdbc.financeiro;

import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.sql.Statement;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import negocio.comuns.utilitarias.UteisValidacao;
import negocio.facade.jdbc.arquitetura.FacadeManager;
import negocio.facade.jdbc.arquitetura.SuperFacadeJDBC;
import negocio.interfaces.financeiro.CentroCustoInterfaceFacade;
import br.com.pactosolucoes.comuns.util.Declaracao;
import java.sql.Connection;

import relatorio.negocio.jdbc.financeiro.CentroCustosDRE;
import negocio.comuns.financeiro.CentroCustoTO;
import negocio.facade.jdbc.arquitetura.SuperEntidade;

public class CentroCusto extends SuperEntidade implements CentroCustoInterfaceFacade {

    public CentroCusto() throws Exception {
        super();
    }

    public CentroCusto(Connection con) throws Exception {
        super(con);
    }

    @Override
    public void incluir(final CentroCustoTO obj) throws Exception {
        // Cria a string sql
        CentroCustoTO.validarDados(obj);
        StringBuilder sql = new StringBuilder();
        sql.append("INSERT INTO centrocusto( " + " codigocentrocustos, nome) "
                + "VALUES (?, ?)");
        // Prepara a conexão
        Declaracao dc = new Declaracao(sql.toString(), FacadeManager.getFacade().getRisco().getCon());
        int i = 0;
        dc.setString(++i, obj.getCodigoCentro());
        dc.setString(++i, obj.getDescricao());
        // Executa a consulta
        dc.execute();
        obj.setCodigo(obterValorChavePrimariaCodigo());
        obj.setNovoObj(false);
    }

    @Override
    public void alterar(final CentroCustoTO obj) throws Exception {
        // Cria a String que irá conter o codigo sql
        CentroCustoTO.validarDados(obj);
        String sql = "UPDATE centrocusto SET codigocentrocustos=?, nome=? "
                + "WHERE codigo=?";
        // Prepara a consulta
        Declaracao dc = new Declaracao(sql.toString(), FacadeManager.getFacade().getRisco().getCon());
        int i = 0;
        dc.setString(++i, obj.getCodigoCentro());
        dc.setString(++i, obj.getDescricao());
        dc.setInt(++i, obj.getCodigo());
        // Executa a consulta
        dc.execute();
    }

    @Override
    public void excluir(final CentroCustoTO obj) throws Exception {
        // super.excluirObj(this.getIdEntidade());
        Declaracao dc = new Declaracao(
                "DELETE FROM centrocusto WHERE CODIGO = ?", FacadeManager.getFacade().getRisco().getCon());
        dc.setInt(1, obj.getCodigo());
        dc.execute();

    }

    @Override
    public void moverNos(CentroCustoTO centroDestino, CentroCustoTO centroOrigem)
            throws Exception {
        // CONSULTA OS FILHOS DO PLANO DE ORIGEM
        List<CentroCustoTO> filhos;
        try (ResultSet rsFilhos = SuperFacadeJDBC.criarConsulta("select codigo, codigocentrocustos, nome from centrocusto where codigo < 100 and codigocentrocustos like '"
                + centroOrigem.getCodigoCentro()
                + "%' order by codigocentrocustos", con)) {

            filhos = new ArrayList<CentroCustoTO>();
            while (rsFilhos.next()) {
                CentroCustoTO obj = new CentroCustoTO();
                obj.setCodigo(rsFilhos.getInt("codigo"));
                obj.setCodigoCentro(rsFilhos.getString("codigocentrocustos"));
                obj.setDescricao(rsFilhos.getString("nome"));
                filhos.add(obj);
            }
        }

        // SELECIONA O MAIOR NÓ DOS FILHOS DO PLANO DE DESTINO
        String centroDest = centroDestino.getCodigoCentro();
        String codigoProximo = obterCodigoProximoFilho(centroDest);

        // atualiza o nó de origem
        StringBuilder sql = new StringBuilder();
        sql.append("UPDATE centrocusto ");
        sql.append(" SET codigocentrocustos = ? ");
        sql.append(" WHERE  codigo = ?");

        String novoCodigo = centroDest + "." + codigoProximo;

        for (CentroCustoTO obj : filhos) {
            String cdgTemp = obj.getCodigoCentro().replaceAll(
                    centroOrigem.getCodigoCentro(), novoCodigo);
            PreparedStatement query = SuperFacadeJDBC.criarQuery(sql.toString(), con);
            query.setString(1, cdgTemp);
            query.setInt(2, obj.getCodigo());
            query.execute();
        }
    }

    @Override
    public String obterCodigoProximoFilho(String centroDest) throws Exception {
        String sql = "";
        if (!UteisValidacao.emptyString(centroDest)) {
            sql = "SELECT MAX(SPLIT_PART(codigocentrocustos, '" + centroDest + ".', 2)) as codigocentrocustos FROM centrocusto "
                    + "where codigocentrocustos like '" + centroDest + ".%' "
                    + "and SPLIT_PART(codigocentrocustos, '" + centroDest + ".', 2) NOT SIMILAR TO '%(.)%'";
        }
        CentroCustoTO ultimoFilho;
        try (ResultSet rsMaiorFilho = SuperFacadeJDBC.criarConsulta(sql, con)) {

            ultimoFilho = new CentroCustoTO();
            if (rsMaiorFilho.next()) {
                CentroCustoTO obj = new CentroCustoTO();
                obj.setCodigoCentro(rsMaiorFilho.getString("codigocentrocustos"));
                ultimoFilho = obj;
            }
        }
        if (UteisValidacao.emptyString(ultimoFilho.getCodigoCentro())) {
            String ret = centroDest;
            if (!ret.isEmpty()) {
                ret += ".";
            }
            ret += "001";
            return ret;
        } else {
            // parte do codigo do ultimo filho (numero apos virgula)
            String ultiFilho = ultimoFilho.getCodigoCentro();
            //numero do ultimo filho + 1
            Integer temp = Integer.parseInt(ultiFilho) + 1;
            //pega o numero antes do ponto e soma com o numero apos a virgula gerado
            String novoCodigoFilho = centroDest;
            novoCodigoFilho += ".";
            if (String.valueOf(temp).length() == 1) {
                novoCodigoFilho += "00";
            } else if (String.valueOf(temp).length() == 2) {
                novoCodigoFilho += "0";
            }
            novoCodigoFilho += String.valueOf(temp);
            if (ultiFilho.equals("999")) {
                throw new Exception(
                        "Este centro de custos está cheio, não é permitido cadastrar novos filhos.");
            }
            return novoCodigoFilho;
        }
    }

    private boolean encontrouCentroPorCodigoInterno(Integer codigoInterno)throws Exception{
        if ((codigoInterno != null) && (codigoInterno > 0)){
            StringBuilder sql = new StringBuilder();
            sql.append("select * \n");
            sql.append("from centrocusto \n");
            sql.append("where codigo = ").append(codigoInterno).append(" \n");
            Statement st = con.createStatement();
            try (ResultSet rs = st.executeQuery(sql.toString())) {
                return rs.next();
            }
        }
        return false;
    }


    @Override
    public List<CentroCustoTO> consultar(String codigocentrocustos, String descricao, Integer codigoInterno) throws Exception {
        String sql = "select codigo, codigocentrocustos, nome from centrocusto ";

        boolean achouPlanoPorCodigoInterno =  encontrouCentroPorCodigoInterno(codigoInterno);
        if (achouPlanoPorCodigoInterno){
            sql += " where codigo =" + codigoInterno;
        }else{
            if (!UteisValidacao.emptyString(codigocentrocustos)) {
                sql += " where codigocentrocustos like '" + codigocentrocustos + "%' ";
            }
            if (!UteisValidacao.emptyString(descricao)) {
                if (!sql.contains("where")) {
                    sql += " where upper(nome) like '%" + descricao.toUpperCase() + "%' ";
                } else {
                    sql += " and upper(nome) like '%" + descricao.toUpperCase() + "%' ";
                }
            }
        }
        sql += "order by codigocentrocustos";
        List<CentroCustoTO> vetResultado;
        try (ResultSet rsFilhos = SuperFacadeJDBC.criarConsulta(sql, con)) {

            vetResultado = new ArrayList<CentroCustoTO>();
            // Lista que irá guardar os codigos para a tree view
            int[] codigos = new int[10];
            codigos[0] = 1;

            while (rsFilhos.next()) {
                CentroCustoTO obj = new CentroCustoTO();
                obj.setCodigo(rsFilhos.getInt("codigo"));
                String codigoCentro = UteisValidacao.removerZeros(rsFilhos.getString("codigocentrocustos"));
                obj.setCodigoTrv(UteisValidacao.rearanjar(codigos, codigoCentro));
                obj.setCodigoCentro(rsFilhos.getString("codigocentrocustos"));
                obj.setDescricao(rsFilhos.getString("nome"));
                //verifica se possui registros filhos
                //esse controle é necessário para permitir ou não a exclusão do plano
                try (ResultSet haFilhos = SuperFacadeJDBC.criarConsulta("select codigo from centrocusto where codigocentrocustos like '"
                        + obj.getCodigoCentro() + ".%'", con)) {
                    obj.setLeaf(!haFilhos.next());
                }
                vetResultado.add(obj);
            }
        }
        return vetResultado;
    }

    @Override
    public boolean consultarSeExisteCentroCustos() throws Exception {
        String sql = "select exists (select codigo from centrocusto limit 1 ) as existe";
        PreparedStatement stm = con.prepareStatement(sql);
        try (ResultSet tabelaResultado = stm.executeQuery()) {
            tabelaResultado.next();
            return tabelaResultado.getBoolean("existe");
        }
    }

    @Override
    public CentroCustoTO obter(int pkey) throws Exception {
        List<CentroCustoTO> vetResultado;
        CentroCustoTO obj;
        try (ResultSet rsFilhos = SuperFacadeJDBC.criarConsulta("select codigo, codigocentrocustos, nome from centrocusto where codigo = "
                + pkey, con)) {
            vetResultado = new ArrayList<CentroCustoTO>();
            // Lista que irá guardar os codigos para a tree view
            obj = null;
            if (rsFilhos.next()) {
                obj = new CentroCustoTO();
                obj.setCodigo(rsFilhos.getInt("codigo"));
                obj.setCodigoCentro(rsFilhos.getString("codigocentrocustos"));
                obj.setDescricao(rsFilhos.getString("nome"));
                vetResultado.add(obj);
            }
        }
        if (vetResultado.size() < 1) {
            throw new Exception(
                    "Não foi encontrado nenhum centro de custos cadastrado");
        }
        //verifica se possui registros filhos
        //esse controle é necessário para permitir ou não a exclusão do centro
        try (ResultSet haFilhos = SuperFacadeJDBC.criarConsulta("select codigo from centrocusto where codigocentrocustos like '"
                + obj.getCodigoCentro() + ".%'", con)) {
            obj.setLeaf(!haFilhos.next());
        }
        return obj;
    }

    @Override
    public CentroCustoTO obter(String codigoCentro) throws Exception {
        CentroCustoTO obj;
        try (ResultSet rsFilhos = SuperFacadeJDBC.criarConsulta("select codigo, codigocentrocustos, nome from centrocusto where codigocentrocustos like '" + codigoCentro + "'", con)) {
            obj = null;
            if (rsFilhos.next()) {
                obj = new CentroCustoTO();
                obj.setCodigo(rsFilhos.getInt("codigo"));
                obj.setDescricao(rsFilhos.getString("nome"));
                obj.setCodigoCentro(codigoCentro);
            }
        }
        if (obj == null) {
            throw new Exception(
                    "Não foi encontrado nenhum centro de custos cadastrado");
        }
        //verifica se possui registros filhos
        //esse controle é necessário para permitir ou não a exclusão do centro
        try (ResultSet haFilhos = SuperFacadeJDBC.criarConsulta("select codigo from centrocusto where codigocentrocustos like '"
                + obj.getCodigoCentro() + ".%'", con)) {
            obj.setLeaf(!haFilhos.next());
        }
        return obj;
    }

    @Override
    public void excluirTodosCentros() throws Exception {
        Declaracao dc = new Declaracao(
                "DELETE FROM centrocusto ", FacadeManager.getFacade().getRisco().getCon());
        dc.execute();
    }

    @Override
    public String consultarCodigoPaiContendoSomenteUmNumeroEAdicionaUm() throws Exception {
        String sql = "select max(codigocentrocustos) as codigocentrocustos from centrocusto  where CHAR_LENGTH(codigocentrocustos) = 3";
        Integer maiorCodigoPai;
        try (ResultSet rsFilhos = SuperFacadeJDBC.criarConsulta(sql, con)) {
            maiorCodigoPai = 0;
            if (rsFilhos.next()) {
                maiorCodigoPai = rsFilhos.getInt("codigocentrocustos");
            }
        }
        maiorCodigoPai = maiorCodigoPai + 1;
        if (String.valueOf(maiorCodigoPai).length() < 3) {
            if (String.valueOf(maiorCodigoPai).length() == 1) {
                return "00" + maiorCodigoPai;
            } else if (String.valueOf(maiorCodigoPai).length() == 2) {
                return "0" + maiorCodigoPai;
            }
        } else {
            throw new Exception("Não é possível cadastrar uma numeração maior que 999");
        }
        return "" + maiorCodigoPai;
    }

    @Override
    public List<CentroCustoTO> consultarTodos() throws Exception {

        List<CentroCustoTO> listaCentroCusto;
        try (ResultSet resultDados = SuperFacadeJDBC.criarConsulta("select * from centrocusto order by codigocentrocustos ", con)) {
            listaCentroCusto = new ArrayList<CentroCustoTO>();
            while (resultDados.next()) {
                listaCentroCusto.add(montarDados(resultDados));
            }
        }
        return listaCentroCusto;
    }

    private CentroCustoTO montarDados(ResultSet resultDados) throws Exception {
        CentroCustoTO obj = new CentroCustoTO();
        obj.setCodigo(resultDados.getInt("codigo"));
        obj.setCodigoCentro(resultDados.getString("codigocentrocustos"));
        obj.setDescricao(resultDados.getString("nome"));
        return obj;
    }

    /**
     * <AUTHOR>
     * 23/11/2011
     */
    @Override
    public Boolean verificarExistenciaPlano(String centroCusto) throws Exception{
    	return CentroCusto.criarConsulta("SELECT * FROM centrocusto WHERE codigocentrocustos LIKE '"+centroCusto+"'", con).next();
    }
    
    /**
     * Responsável por obter Codigo dos Filhos do centro de código informado como parametro
     * <AUTHOR>
     * 23/11/2011
     */
    @Override
    public Map<Integer, String> obterCodigoCentroIrmaos(String codigoPai) throws Exception{
    	Map<Integer, String> codigosFilhos = new HashMap<Integer, String>();
    	if(!UteisValidacao.emptyString(codigoPai)){
    		codigoPai = codigoPai + ".";
    	}
        try (ResultSet rs = CentroCusto.criarConsulta("SELECT codigo, codigocentrocustos FROM centrocusto WHERE codigocentrocustos LIKE '" + codigoPai + "___'", con)) {
            while (rs.next()) {
                codigosFilhos.put(rs.getInt("codigo"), rs.getString("codigocentrocustos"));
            }
        }
        return codigosFilhos;
    }
    
    /**
     * <AUTHOR>
     * 23/11/2011
     */
    @Override
    public List<Integer> obterCodigoFilhos(String codigoPai, String exceto) throws Exception {
		List<Integer> codigosFilhos = new ArrayList<Integer>();
    	if(!UteisValidacao.emptyString(codigoPai)){
    		codigoPai = codigoPai + ".";
    	}
    	StringBuilder sql = new StringBuilder();
    	sql.append("SELECT codigo FROM centrocusto WHERE codigocentrocustos LIKE '"+codigoPai+"%' ");
    	if(!UteisValidacao.emptyString(exceto)){
    		sql.append("AND codigocentrocustos NOT LIKE '"+exceto+"%'");
    	}
        try (ResultSet rs = CentroCusto.criarConsulta(sql.toString(), con)) {
            while (rs.next()) {
                codigosFilhos.add(rs.getInt("codigo"));
            }
        }
        return codigosFilhos;
	}
    
    /**
     * Responsável por atualizar o Codigo do centro custos
     * <AUTHOR>
     * 22/11/2011
     */
    @Override
    public void atualizarCodigoCentroCustos(Integer codigoPlano, String codigoNovo,String codigoAntesAlteracao,List<Integer> codigosFilhos) throws Exception{
    	StringBuilder sql = new StringBuilder();
    	sql.append("UPDATE centrocusto SET codigocentrocustos = '"+codigoNovo+"' WHERE codigo = "+codigoPlano+";\n");
    	for(Integer codigo : codigosFilhos){
    		sql.append("UPDATE centrocusto SET codigocentrocustos = '");
        	sql.append(codigoNovo+"'||SUBSTRING (codigocentrocustos ,"+(codigoAntesAlteracao.length()+1)+", LENGTH(codigocentrocustos) )  WHERE codigo = "+codigo+";\n");
        }
    	PlanoConta.executarConsulta(sql.toString(), con);
    }
    
    public List<CentroCustosDRE> obterCentroCustos() throws Exception{
        List<CentroCustosDRE> centros;
        try (ResultSet consulta = criarConsulta("select codigocentrocustos, nome, codigo from centrocusto order by codigocentrocustos asc", con)) {
            centros = new ArrayList<CentroCustosDRE>();
            while (consulta.next()) {
                CentroCustosDRE centro = new CentroCustosDRE();
                centro.setCodigoCentro(consulta.getString("codigocentrocustos"));
                centro.setNomeCentro(consulta.getString("nome"));
                centro.setCodigo(consulta.getInt("codigo"));
                centros.add(centro);
            }
        }
        return centros;
    }

    public boolean verificarExisteRelacionamento(){
        try {
            return existe("SELECT * FROM movcontarateio WHERE centrocusto IS NOT NULL", getCon())
                    || existe("select * from rateiointegracao WHERE centrocusto IS NOT NULL", getCon())
                    || existe("select * from configuracaofinanceiro  where centrocustostaxa IS NOT NULL", getCon());
        } catch (Exception e) {
            return true;
        }
    }

    public boolean verificarExisteRelacionamento(Integer codigo){
        try {
            return existe("SELECT * FROM movcontarateio WHERE centrocusto ="+codigo, getCon())
                    || existe("select * from rateiointegracao WHERE centrocusto ="+codigo, getCon())
                    || existe("select * from configuracaofinanceiro  where centrocustostaxa ="+codigo, getCon());
        } catch (Exception e) {
            return true;
        }
    }

    public void trocarCentroCustos(Integer codigoCentroAntigo, Integer codigoCentroNovo) throws Exception{
    	executarConsulta(" UPDATE movcontarateio SET centrocusto = "+codigoCentroNovo+" where centrocusto = "+codigoCentroAntigo, con);
    	executarConsulta(" UPDATE rateiointegracao SET centrocusto = "+codigoCentroNovo+" where centrocusto = "+codigoCentroAntigo, con);
        executarConsulta(" UPDATE configuracaofinanceiro SET centrocustostaxa = "+codigoCentroNovo+" where centrocustostaxa = "+codigoCentroAntigo, con);
    }
    @Override
    public CentroCustoTO consultarPorChavePrimaria(Integer codigo) throws Exception{
        try (ResultSet resultDados = SuperFacadeJDBC.criarConsulta("select * from centrocusto where codigo = " + codigo, con)) {
            if (resultDados.next()) {
                return montarDados(resultDados);
            }
        }
        return new CentroCustoTO();
    }
}

package negocio.facade.jdbc.financeiro;

import negocio.comuns.financeiro.HistoricoIntegracaoKobanaVO;
import negocio.comuns.utilitarias.Calendario;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisValidacao;
import negocio.facade.jdbc.arquitetura.SuperEntidade;
import negocio.interfaces.financeiro.HistoricoIntegracaoKobanaInterfaceFacade;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.Types;

public class HistoricoIntegracaoKobana extends SuperEntidade implements HistoricoIntegracaoKobanaInterfaceFacade {

    public HistoricoIntegracaoKobana() throws Exception {
        super();
    }

    public HistoricoIntegracaoKobana(Connection con) throws Exception {
        super(con);
    }

    public void incluir(HistoricoIntegracaoKobanaVO obj) throws Exception {
        try {
            String sql = "INSERT INTO HistoricoIntegracaoKobana(empresa, metodo, lote, paramsEnvio, paramsRetorno, dataRegistro, sucesso, lote) VALUES (?, ?, ?, ?, ?, ?)";
            try (PreparedStatement sqlInserir = con.prepareStatement(sql)) {
                sqlInserir.setInt(1, obj.getEmpresa());
                sqlInserir.setString(2, obj.getMetodo());
                if (UteisValidacao.emptyNumber(obj.getLote())) {
                    sqlInserir.setNull(3, Types.NULL);
                } else {
                    sqlInserir.setInt(3, obj.getLote());
                }
                sqlInserir.setString(4, obj.getParamsEnvio());
                sqlInserir.setString(5, obj.getParamsRetorno());
                sqlInserir.setTimestamp(6, Uteis.getDataJDBCTimestamp(Calendario.hoje()));
                sqlInserir.setBoolean(7, obj.isSucesso());
                sqlInserir.setBoolean(8, obj.isSucesso());
                sqlInserir.execute();
            }
        } catch (Exception e) {
            throw e;
        }
    }
}

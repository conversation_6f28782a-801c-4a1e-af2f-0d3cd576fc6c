package negocio.facade.jdbc.financeiro;

import br.com.pactosolucoes.bi.dto.DadosMetaFinanceiraDTO;
import br.com.pactosolucoes.bi.dto.FiltroDTO;
import br.com.pactosolucoes.comuns.json.JSONMapper;
import br.com.pactosolucoes.enumeradores.BIEnum;
import negocio.comuns.arquitetura.UsuarioVO;
import negocio.comuns.basico.ClienteVO;
import negocio.comuns.basico.ColaboradorVO;
import negocio.comuns.financeiro.DetalhamentoDiarioMetaFinanceiroVO;
import negocio.comuns.financeiro.FormaPagamentoVO;
import negocio.comuns.financeiro.ReciboClienteConsultorVO;
import negocio.comuns.financeiro.ReciboPagamentoVO;
import negocio.comuns.financeiro.enumerador.TipoFormaPagto;
import negocio.comuns.plano.enumerador.TipoProduto;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisValidacao;
import negocio.facade.jdbc.arquitetura.SuperEntidade;
import org.json.JSONArray;
import org.json.JSONObject;

import java.sql.Connection;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class MetaFinanceiroBI extends SuperEntidade {

    public static final String sqlCompetenciaZW = "SELECT\n" +
            "  %s\n" +
            "FROM public.movProduto mov\n" +
            "  INNER JOIN public.produto prod\n" +
            "    ON prod.codigo = mov.produto\n" +
            "  INNER JOIN public.usuario u\n" +
            "    ON mov.responsavellancamento = u.codigo\n" +
            "  INNER JOIN public.pessoa p\n" +
            "    ON mov.pessoa = p.codigo\n" +
            "  INNER JOIN public.cliente cli\n" +
            "    ON p.codigo = cli.pessoa\n" +
            "  INNER JOIN public.vinculo v\n" +
            "    ON (v.cliente = cli.codigo AND v.tipovinculo = 'CO')\n" +
            "  INNER JOIN public.colaborador col\n" +
            "    ON v.colaborador = col.codigo\n" +
            "  INNER JOIN public.pessoa pescol\n" +
            "    ON col.pessoa = pescol.codigo\n" +
            "WHERE prod.tipoProduto NOT IN ('DE', 'DR', 'DV')\n" +
            "      AND (mov.TotalFinal > 0)\n" +
            "      AND mov.mesReferencia = '%s'\n" +
            "      AND mov.empresa = %s";
    public static final String sqlReceitaZW = "	SELECT %s FROM cartaocredito cc\n" +
            " INNER JOIN movpagamento mp ON mp.codigo = cc.movpagamento AND mp.empresa = %s" +
            " INNER JOIN usuario u ON u.codigo = mp.responsavelpagamento " +
            " LEFT JOIN recibopagamento r ON r.codigo = mp.recibopagamento " +
            " WHERE datacompesancao BETWEEN '%s 00:00:00' AND '%s 23:59:59' %s\n" +
            " UNION ALL SELECT %s FROM cheque ch \n" +
            " INNER JOIN movpagamento mp ON mp.codigo = ch.movpagamento  AND mp.empresa = %s" +
            " INNER JOIN usuario u ON u.codigo = mp.responsavelpagamento " +
            " LEFT JOIN recibopagamento r ON r.codigo = mp.recibopagamento " +
            " WHERE datacompesancao BETWEEN '%s 00:00:00' AND '%s 23:59:59' %s\n" +
            " UNION ALL	SELECT %s FROM movpagamento mp \n" +
            " INNER JOIN formapagamento fp ON fp.codigo = mp.formapagamento \n" +
            " INNER JOIN usuario u ON u.codigo = mp.responsavelpagamento " +
            " LEFT JOIN recibopagamento r ON r.codigo = mp.recibopagamento " +
            " WHERE (fp.tipoformapagamento NOT IN ('CA','CH')) \n" +
            " AND datapagamento BETWEEN '%s 00:00:00' AND '%s 23:59:59'" +
            " AND mp.empresa = %s %s";
    public static final String sqlFaturamentoZW = "SELECT\n" +
            "  %s \n" +
            "FROM public.movProduto \n" +
            "  INNER JOIN public.usuario u\n" +
            "    ON movProduto.responsavellancamento = u.codigo\n" +
            "  INNER JOIN public.pessoa pes\n" +
            "    ON movProduto.pessoa = pes.codigo\n" +
            "  LEFT JOIN public.produto prod\n" +
            "    ON movProduto.produto = prod.codigo\n" +
            "  INNER JOIN public.pessoa p\n" +
            "    ON movProduto.pessoa = p.codigo\n" +
            "  JOIN public.cliente cli\n" +
            "    ON cli.pessoa = pes.codigo\n" +
            "  JOIN public.vinculo v\n" +
            "    ON (v.cliente = cli.codigo AND v.tipovinculo = 'CO')\n" +
            "  JOIN public.colaborador col\n" +
            "    ON col.codigo = v.colaborador\n" +
            "  INNER JOIN public.pessoa pescol\n" +
            "    ON col.pessoa = pescol.codigo\n" +
            "WHERE movProduto.datalancamento >= '%s'\n" +
            "      AND movProduto.datalancamento <= '%s'\n" +
            "      AND movProduto.empresa = %s\n" +
            "      AND prod.tipoProduto NOT IN ('DE', 'DR', 'DV')\n" +
            "      AND (movProduto.TotalFinal > 0)\n";

    public MetaFinanceiroBI(Connection conexao) throws Exception {
        super(conexao);
    }

    public MetaFinanceiroBI() throws Exception {
        super();
    }

    private FiltroDTO getFiltroDTO(Date inicio, Date fim, List<ColaboradorVO> listColaboradores, List<UsuarioVO> listResponsaveis, Integer empresa,
                                   boolean rematricula, boolean renovacao, boolean matricula, boolean consultorAtual, boolean faturamentoRecebidoPorDia,
                                   boolean faturamentoRecebidoSimplificado, boolean incluirFaturamentoPeriodo, boolean faturamentoRecebidoPeriodo, boolean considerarProdutos, boolean metaFinanceiraPorFaturamento) {
        FiltroDTO filtroDTO = new FiltroDTO();
        filtroDTO.setNome(BIEnum.METAS_FINANCEIRAS.name());
        JSONObject filtros = new JSONObject();
        filtros.put("empresa", empresa);
        JSONArray colaboradores = new JSONArray();
        if (listColaboradores != null) {
            for (ColaboradorVO co : listColaboradores) {
                colaboradores.put(co.getCodigo());
            }
        }
        filtros.put("colaboradores", colaboradores);
        JSONArray responsaveis = new JSONArray();
        if (listResponsaveis != null) {
            for (UsuarioVO us : listResponsaveis) {
                responsaveis.put(us.getCodigo());
            }
        }
        filtros.put("responsaveis", responsaveis);
        filtros.put("rematricula", rematricula);
        filtros.put("renovacao", renovacao);
        filtros.put("matricula", matricula);
        filtros.put("consultorAtual", consultorAtual);
        filtros.put("dataInicial", inicio != null ? inicio.getTime() : null);
        filtros.put("dataFinal", fim != null ? fim.getTime() : null);
        filtros.put("tipoFormaPagamentoSigla", TipoFormaPagto.CREDITOCONTACORRENTE.getSigla());
        filtros.put("tipoProduto", TipoProduto.CHEQUE_DEVOLVIDO.getCodigo());
        filtros.put("faturamentoRecebidoPorDia", faturamentoRecebidoPorDia);
        filtros.put("faturamentoRecebidoSimplificado", faturamentoRecebidoSimplificado);
        filtros.put("incluirFaturamentoPeriodo", incluirFaturamentoPeriodo);
        filtros.put("faturamentoRecebidoPeriodo", faturamentoRecebidoPeriodo);
        filtros.put("considerarProdutos", considerarProdutos);
        filtros.put("metaFinanceiraPorFaturamento", metaFinanceiraPorFaturamento);
        filtroDTO.setFiltros(filtros.toString());
        return filtroDTO;
    }

    public Double obterFaturamentoPorPeriodo(String key, Date inicio, Date fim, List<ColaboradorVO> colaboradores, List<UsuarioVO> responsaveis, Integer empresa,
                                             boolean rematricula, boolean renovacao, boolean matricula, boolean consultorAtual, boolean atualizar) throws Exception {
        FiltroDTO filtroDTO = getFacade().getBiMSService().obterBI(key, BIEnum.METAS_FINANCEIRAS,
                getFiltroDTO(inicio, fim, colaboradores, responsaveis, empresa, rematricula, renovacao, matricula, consultorAtual, false, false, true, true, true, false), true);
        DadosMetaFinanceiraDTO dto = JSONMapper.getObject(new JSONObject(filtroDTO.getJsonDados()), DadosMetaFinanceiraDTO.class);
        return dto.getTotalFaturamentoPeriodo();
    }

    public Double obterFaturamentoRecebido(String key, Date inicio, Date fim, List<ColaboradorVO> colaboradores, List<UsuarioVO> responsaveis, Integer empresa,
                                           boolean rematricula, boolean renovacao, boolean matricula, boolean consultorAtual, boolean atualizar, boolean metaFinanceiraPorFaturamento) throws Exception {
        FiltroDTO filtroDTO = getFacade().getBiMSService().obterBI(key, BIEnum.METAS_FINANCEIRAS,
                getFiltroDTO(inicio, fim, colaboradores, responsaveis, empresa, rematricula, renovacao, matricula, consultorAtual, false, false, false, false, false, metaFinanceiraPorFaturamento), atualizar);
        DadosMetaFinanceiraDTO dto = JSONMapper.getObject(new JSONObject(filtroDTO.getJsonDados()), DadosMetaFinanceiraDTO.class);
        return (dto.getFaturamentoRecebido() - dto.getFaturamentoDevolvido()) < 0.0 ? 0.0 : (dto.getFaturamentoRecebido() - dto.getFaturamentoDevolvido());
    }

    /**
     * Joao Alcides
     * 05/03/2012
     */
    public Double obterFaturamento(Date inicio, Date fim, List<ColaboradorVO> colaboradores, Integer empresa) throws Exception {

        String codigosColaboradores = getCodigosColaboradores(colaboradores);
        if (!codigosColaboradores.isEmpty()) {
            codigosColaboradores = " AND v.colaborador IN (" + codigosColaboradores + ")";
        }
        String sql = String.format(sqlFaturamentoZW, "SUM(movproduto.totalfinal) AS total",
                Uteis.getDataJDBC(inicio),
                Uteis.getDataJDBC(fim),
                empresa);
        if (!codigosColaboradores.isEmpty()) {
            sql += codigosColaboradores;
        }

        ResultSet consulta = criarConsulta(sql, con);
        consulta.next();
        return consulta.getDouble("total");
    }

    /**
     * Joao Alcides
     * 05/03/2012
     */
    public Map<Object, Double> obterFaturamentoPorDia(Date inicio, Date fim, List<ColaboradorVO> colaboradores, Integer empresa) throws Exception {
        Map<Object, Double> faturamentoDia = new HashMap<Object, Double>();
        String codigosColaboradores = getCodigosColaboradores(colaboradores);
        if (!codigosColaboradores.isEmpty()) {
            codigosColaboradores = " AND v.colaborador IN (" + codigosColaboradores + ")";
        }
        String sql = String.format(sqlFaturamentoZW, "SUM(movproduto.totalfinal) AS valor, datalancamento ",
                Uteis.getDataJDBC(inicio),
                Uteis.getDataJDBC(fim),
                empresa);
        if (!codigosColaboradores.isEmpty()) {
            sql += codigosColaboradores;
        }
        sql += "GROUP BY datalancamento";
        ResultSet consulta = criarConsulta(sql, con);
        while (consulta.next()) {
            faturamentoDia.put(consulta.getDate("datalancamento"), consulta.getDouble("valor"));
        }
        return faturamentoDia;
    }

    /**
     * Joao Alcides
     * 05/03/2012
     */
    public Double obterReceita(Date inicio, Date fim, List<ColaboradorVO> colaboradores, Integer empresa) throws Exception {

        String codigosColaboradores = getCodigosColaboradores(colaboradores);
        if (!codigosColaboradores.isEmpty()) {
            codigosColaboradores = " AND u.colaborador IN (" + codigosColaboradores + ")";
        }
        String sql = String.format(sqlReceitaZW, new Object[]{
                "SUM(cc.valor) AS valor",
                empresa,
                Uteis.getDataJDBC(inicio),
                Uteis.getDataJDBC(fim),
                codigosColaboradores,
                "SUM(ch.valor) AS valor",
                empresa,
                Uteis.getDataJDBC(inicio),
                Uteis.getDataJDBC(fim),
                codigosColaboradores,
                "SUM(valor) AS valor",
                Uteis.getDataJDBC(inicio),
                Uteis.getDataJDBC(fim),
                empresa,
                codigosColaboradores});
        sql = "SELECT SUM(valor) AS total FROM (" + sql + ") AS consulta";
        ResultSet consulta = criarConsulta(sql, con);
        consulta.next();
        return consulta.getDouble("total");

    }

    /**
     * Joao Alcides
     * 05/03/2012
     */
    public Map<Object, Double> obterReceitaPorDia(Date inicio, Date fim, List<ColaboradorVO> colaboradores, Integer empresa) throws Exception {
        Map<Object, Double> receitaDia = new HashMap<Object, Double>();
        String codigosColaboradores = getCodigosColaboradores(colaboradores);
        if (!codigosColaboradores.isEmpty()) {
            codigosColaboradores = " AND u.colaborador IN (" + codigosColaboradores + ")";
        }
        String sql = String.format(sqlReceitaZW, new Object[]{
                "CAST(datacompesancao AS DATE) AS data, SUM(cc.valor) AS valor",
                empresa,
                Uteis.getDataJDBC(inicio),
                Uteis.getDataJDBC(fim),
                codigosColaboradores + "GROUP BY CAST(datacompesancao AS DATE)",
                "CAST(datacompesancao AS DATE) AS data, SUM(ch.valor) AS valor",
                empresa,
                Uteis.getDataJDBC(inicio),
                Uteis.getDataJDBC(fim),
                codigosColaboradores + "GROUP BY CAST(datacompesancao AS DATE)",
                "CAST(datapagamento AS DATE) AS data, SUM(valor) AS valor",
                Uteis.getDataJDBC(inicio),
                Uteis.getDataJDBC(fim),
                empresa,
                codigosColaboradores + "GROUP BY CAST(datapagamento AS DATE)"});
        sql = "SELECT SUM(valor) AS valor, data FROM (" + sql + ") AS consulta GROUP BY data ORDER BY data";
        ResultSet consulta = criarConsulta(sql, con);
        while (consulta.next()) {
            receitaDia.put(consulta.getDate("data"), consulta.getDouble("valor"));
        }
        return receitaDia;

    }

    /**
     * Joao Alcides
     * 05/03/2012
     */
    public Double obterCompetencia(Date inicio, Date fim, String mesReferencia, List<ColaboradorVO> colaboradores, Integer empresa) throws Exception {
        String sql = String.format(sqlCompetenciaZW, "SUM(mov.totalfinal) AS total",
                mesReferencia,
                empresa.toString());
        String codigosColaboradores = getCodigosColaboradores(colaboradores);
        if (!codigosColaboradores.isEmpty()) {
            sql += " AND v.colaborador IN (" + codigosColaboradores + ")";
        }
        ResultSet consulta = criarConsulta(sql, con);
        consulta.next();
        return consulta.getDouble("total");

    }

    /**
     * Joao Alcides
     * 05/03/2012
     */
    public Map<Object, Double> obterCompetenciaPorDia(Date inicio, Date fim, String mesReferencia, List<ColaboradorVO> colaboradores, Integer empresa) throws Exception {
        Map<Object, Double> competenciaDia = new HashMap<Object, Double>();
        String sql = String.format(sqlCompetenciaZW, "SUM(mov.totalfinal) AS total, DATE_PART('day', datalancamento) AS data ",
                mesReferencia,
                empresa.toString());
        String codigosColaboradores = getCodigosColaboradores(colaboradores);
        if (!codigosColaboradores.isEmpty()) {
            sql += " AND v.colaborador IN (" + codigosColaboradores + ")";
        }
        sql += " GROUP BY DATE_PART('day', datalancamento) \n";
        ResultSet consulta = criarConsulta(sql.toString(), con);
        while (consulta.next()) {
            competenciaDia.put(consulta.getInt("data"), consulta.getDouble("total"));
        }
        return competenciaDia;
    }

    /**
     * Joao Alcides
     * 29/02/2012
     */
    public Map<Object, Double> obterFaturamentoRecebidoPorDia(String key, Date inicio, Date fim, List<ColaboradorVO> colaboradores,List<UsuarioVO> reponsaveis,
                                                              Integer empresa, boolean rematricula, boolean renovacao,
                                                              boolean matricula, boolean consultorAtual, boolean atualizar, boolean metaFinanceiraPorFaturamento) throws Exception {
        FiltroDTO filtroDTO = getFacade().getBiMSService().obterBI(key, BIEnum.METAS_FINANCEIRAS,
                getFiltroDTO(inicio, fim, colaboradores, reponsaveis, empresa, rematricula, renovacao, matricula, consultorAtual, true, false, false, false, false, metaFinanceiraPorFaturamento), atualizar);
        DadosMetaFinanceiraDTO dto = JSONMapper.getObject(new JSONObject(filtroDTO.getJsonDados()), DadosMetaFinanceiraDTO.class);
        return dto.getFaturamentoRecebidoPorDia();
    }


    /**
     * Joao Alcides
     * 05/03/2012
     */
    private String getCodigosColaboradores(List<ColaboradorVO> colaboradores) {
        String codigos = "";
        for (ColaboradorVO colaborador : colaboradores) {
            codigos += "," + colaborador.getCodigo();
        }
        return codigos.replaceFirst(",", "");
    }
    private String getUsuariosResponsaveis(List<UsuarioVO> colaboradores) {
        String codigos = "";
        for (UsuarioVO colaborador : colaboradores) {
            codigos += "," + colaborador.getCodigo();
        }
        return codigos.replaceFirst(",", "");
    }
    public List<ReciboClienteConsultorVO> obterListaSimplificadaFaturamentoRecebido(String key, Date inicio, Date fim, List<ColaboradorVO> colaboradores,List<UsuarioVO> usuariosResponsaveis, Integer empresa,
                                                                                    boolean rematricula, boolean renovacao, boolean matricula, boolean consultarAtual, boolean atualizar, boolean metaFinanceiraPorFaturamento) throws Exception {
        FiltroDTO filtroDTO = getFacade().getBiMSService().obterBI(key, BIEnum.METAS_FINANCEIRAS,
                getFiltroDTO(inicio, fim, colaboradores, usuariosResponsaveis, empresa, rematricula, renovacao, matricula, consultarAtual, false, true, false, false, false, metaFinanceiraPorFaturamento), atualizar);
        DadosMetaFinanceiraDTO dto = JSONMapper.getObject(new JSONObject(filtroDTO.getJsonDados()), DadosMetaFinanceiraDTO.class);
        return montarDadosConsultaFaturamentoRecebido(new JSONArray(dto.getFaturamentoRecebidoSimplificado()));
    }

    /**
     * Joao Alcides
     * 05/03/2012
     */
    public List<DetalhamentoDiarioMetaFinanceiroVO> obterListaSimplificadaFaturamento(Date inicio, Date fim, List<ColaboradorVO> colaboradores, Integer empresa) throws Exception {
        String codigosColaboradores = getCodigosColaboradores(colaboradores);
        if (!codigosColaboradores.isEmpty()) {
            codigosColaboradores = " AND v.colaborador IN (" + codigosColaboradores + ")";
        }
        String sql = String.format(sqlFaturamentoZW, "p.nome AS pessoa, movProduto.contrato, movProduto.datalancamento, pescol.nome AS user, SUM(movproduto.totalfinal) AS totalfinal ",
                Uteis.getDataJDBC(inicio),
                Uteis.getDataJDBC(fim),
                empresa);
        if (!codigosColaboradores.isEmpty()) {
            sql += codigosColaboradores;
        }
        sql += "GROUP BY p.nome, movProduto.contrato, movProduto.datalancamento, pescol.nome";
        ResultSet consulta = criarConsulta(sql, con);

        return montarDadosConsultaOrigemMovProduto(consulta);

    }

    /**
     * Joao Alcides
     * 05/03/2012
     */
    public List<DetalhamentoDiarioMetaFinanceiroVO> obterListaSimplificadaCompetencia(Integer inicio, Integer fim, String mesReferencia, List<ColaboradorVO> colaboradores, Integer empresa) throws Exception {
        String sql = String.format(sqlCompetenciaZW, "p.nome AS pessoa, mov.contrato, mov.datalancamento, pescol.nome AS user, SUM(mov.totalfinal) AS totalfinal ",
                mesReferencia,
                empresa.toString());
        String codigosColaboradores = getCodigosColaboradores(colaboradores);
        if (!codigosColaboradores.isEmpty()) {
            sql += " AND v.colaborador IN (" + codigosColaboradores + ")";
        }
        sql += " AND DATE_PART('day', datalancamento) BETWEEN " + inicio + " AND " + fim;
        sql += " GROUP BY p.nome, mov.contrato, mov.datalancamento, pescol.nome";
        ResultSet consulta = criarConsulta(sql, con);
        return montarDadosConsultaOrigemMovProduto(consulta);

    }

    /**
     * Joao Alcides
     * 05/03/2012
     */
    public List<DetalhamentoDiarioMetaFinanceiroVO> obterListaSimplificadaReceita(Date inicio, Date fim, List<ColaboradorVO> colaboradores, Integer empresa) throws Exception {
        String codigosColaboradores = getCodigosColaboradores(colaboradores);
        if (!codigosColaboradores.isEmpty()) {
            codigosColaboradores = " AND u.colaborador IN (" + codigosColaboradores + ")";
        }
        String sql = String.format(sqlReceitaZW, new Object[]{
                "cc.datacompesancao AS data, cc.valor AS valor, r.nomepessoapagador, r.contrato, u.nome ",
                empresa,
                Uteis.getDataJDBC(inicio),
                Uteis.getDataJDBC(fim),
                codigosColaboradores,
                "ch.datacompesancao AS data, ch.valor AS valor, r.nomepessoapagador, r.contrato, u.nome ",
                empresa,
                Uteis.getDataJDBC(inicio),
                Uteis.getDataJDBC(fim),
                codigosColaboradores,
                "mp.datapagamento AS data, mp.valor AS valor, r.nomepessoapagador, r.contrato, u.nome ",
                Uteis.getDataJDBC(inicio),
                Uteis.getDataJDBC(fim),
                empresa,
                codigosColaboradores});
        ResultSet consulta = criarConsulta(sql, con);
        return montarDadosConsultaReceita(consulta);

    }

    private List<DetalhamentoDiarioMetaFinanceiroVO> montarDadosConsultaOrigemMovProduto(ResultSet consulta) throws SQLException {
        List<DetalhamentoDiarioMetaFinanceiroVO> listaDia = new ArrayList<DetalhamentoDiarioMetaFinanceiroVO>();
        while (consulta.next()) {
            DetalhamentoDiarioMetaFinanceiroVO itemDia = new DetalhamentoDiarioMetaFinanceiroVO();
            itemDia.setDia(consulta.getDate("datalancamento"));
            itemDia.setCliente(consulta.getString("pessoa"));
            itemDia.setContrato(consulta.getInt("contrato"));
            itemDia.setValor(consulta.getDouble("totalfinal"));
            itemDia.setResponsavel(consulta.getString("user"));
            listaDia.add(itemDia);
        }
        return listaDia;
    }

    private List<DetalhamentoDiarioMetaFinanceiroVO> montarDadosConsultaReceita(ResultSet consulta) throws SQLException {
        List<DetalhamentoDiarioMetaFinanceiroVO> listaDia = new ArrayList<DetalhamentoDiarioMetaFinanceiroVO>();
        while (consulta.next()) {
            DetalhamentoDiarioMetaFinanceiroVO itemDia = new DetalhamentoDiarioMetaFinanceiroVO();
            itemDia.setDia(consulta.getDate("data"));
            itemDia.setCliente(consulta.getString("nomepessoapagador"));
            itemDia.setContrato(consulta.getInt("contrato"));
            itemDia.setValor(consulta.getDouble("valor"));
            itemDia.setResponsavel(consulta.getString("nome"));
            listaDia.add(itemDia);
        }
        return listaDia;
    }

    private List<ReciboClienteConsultorVO> montarDadosConsultaFaturamentoRecebido(JSONArray jsonList) throws Exception {
        List<ReciboClienteConsultorVO> lista = new ArrayList<>();
        List<Integer> listaDevolucoes = new ArrayList<>();
        for (int i = 0; i < jsonList.length(); i++) {
            JSONObject dados = jsonList.getJSONObject(i);
            ReciboClienteConsultorVO reciboclienteconsultorVO = new ReciboClienteConsultorVO();
            reciboclienteconsultorVO.setCodigo(dados.getInt("codigo"));
            reciboclienteconsultorVO.setValor(dados.getDouble("valor"));

            //Montando dados do Recibo
            ReciboPagamentoVO reciboPagamentoVO = ReciboPagamento.montarDados(dados, Uteis.NIVELMONTARDADOS_TELACONSULTA, con);
            reciboPagamentoVO.setUsuarioVO(new UsuarioVO());
            reciboPagamentoVO.getUsuarioVO().setNome(dados.getString("nome"));
            reciboclienteconsultorVO.setRecibo(reciboPagamentoVO);

            //Montando dados do Consultor
            if (dados.getInt("consultor") != 0) {
                ColaboradorVO colaboradorVO = getFacade().getColaborador().consultarPorChavePrimaria(dados.getInt("consultor"), Uteis.NIVELMONTARDADOS_TELACONSULTA);
                reciboclienteconsultorVO.setConsultor(colaboradorVO);
            } else {
                reciboclienteconsultorVO.setConsultor(new ColaboradorVO());
            }
            //Montando dados do Responsavel Venda
            if (dados.getInt("responsavellancamento") != 0 || !UteisValidacao.emptyString(dados.getString("nomeresponsavel"))) {
                UsuarioVO usuarioVO = new UsuarioVO();
                usuarioVO.setNome(dados.getString("nomeresponsavel"));
                reciboclienteconsultorVO.setResponsavelPagamento(usuarioVO);
            } else {
                reciboclienteconsultorVO.setResponsavelPagamento(new UsuarioVO());
            }

            //Montando dados do Cliente
            if (dados.getInt("cliente") != 0) {
                ClienteVO clienteVO = getFacade().getCliente().consultarPorChavePrimaria(dados.getInt("cliente"), Uteis.NIVELMONTARDADOS_TELACONSULTA);
                reciboclienteconsultorVO.setCliente(clienteVO);
            } else {
                reciboclienteconsultorVO.setCliente(new ClienteVO());
                reciboclienteconsultorVO.getCliente().getPessoa().setNome(dados.getString("nomepessoapagador"));
            }

            //Montando dados da Forma de Pagamento
            FormaPagamentoVO formaPagamento = getFacade().getFormaPagamento().consultarPorChavePrimaria(dados.getInt("formapagamento"), Uteis.NIVELMONTARDADOS_TELACONSULTA);
            reciboclienteconsultorVO.setFormaPagamento((formaPagamento == null ? new FormaPagamentoVO() : formaPagamento));

            lista.add(reciboclienteconsultorVO);
            if(dados.getDouble("devolucoes") > 0.0 && !(listaDevolucoes.contains(reciboPagamentoVO.getCodigo()))){
                FormaPagamentoVO fpdev = new FormaPagamentoVO();
                fpdev.setDescricao("Cheque devolvido");
                lista.add(new ReciboClienteConsultorVO(reciboclienteconsultorVO, -1*Uteis.arredondarForcando2CasasDecimais(dados.getDouble("devolucoes")),fpdev));
                listaDevolucoes.add(reciboPagamentoVO.getCodigo());
            }
        }
        return lista;
    }
}

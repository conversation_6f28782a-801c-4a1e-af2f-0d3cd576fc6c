package negocio.facade.jdbc.financeiro;

import br.com.pactosolucoes.estudio.dao.Pacote;
import br.com.pactosolucoes.estudio.modelo.PacoteVO;
import negocio.comuns.estoque.ProdutoEstoqueVO;
import negocio.comuns.financeiro.MonitoramentoHistoricoMesVO;
import negocio.comuns.plano.ProdutoVO;
import negocio.comuns.utilitarias.ConsistirException;
import negocio.comuns.utilitarias.Uteis;
import negocio.facade.jdbc.arquitetura.SuperEntidade;
import negocio.facade.jdbc.estoque.BalancoItens;
import negocio.facade.jdbc.estoque.ProdutoEstoque;
import negocio.facade.jdbc.plano.Produto;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.Statement;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.Iterator;
import java.util.List;

/**
 *
 * @see negocio.comuns.financeiro.MonitoramentoHistoricoMesVO
 * @see SuperEntidade
 * @see Monitoramento
 */
public class MonitoramentoHistoricoMes extends SuperEntidade {

    public MonitoramentoHistoricoMes() throws Exception {
        super();
        setIdEntidade("MonitoramentoHistoricoMes");
    }

    public MonitoramentoHistoricoMes(Connection con) throws Exception {
        super(con);
        setIdEntidade("Monitoramento");
    }

    /**
     * Opera??o respons?vel por retornar um novo objeto da classe <code>MonitoramentoHistoricoMesVO</code>.
     */
    public MonitoramentoHistoricoMesVO novo() throws Exception {
        incluir(getIdEntidade());
        MonitoramentoHistoricoMesVO obj = new MonitoramentoHistoricoMesVO();
        return obj;
    }

    private void validarDados(MonitoramentoHistoricoMesVO obj) throws ConsistirException {
        if(obj.getMonitoramento().equals(0)){
            throw new ConsistirException("O campo MONITORAMENTO n?o foi informado");
        }
        if(obj.getDia() == null){
            throw new ConsistirException("O campo DIA n?o foi informado");
        }
        if(obj.getAno().equals(0)){
            throw new ConsistirException("O campo ANO n?o foi informado");
        }
        if(obj.getMes().equals(0)){
            throw new ConsistirException("O campo M?S n?o foi informado");
        }
    }

    /**
     * Opera??o respons?vel por incluir no banco de dados um objeto da classe <code>MonitoramentoHistoricoMesVO</code>.
     * Primeiramente valida os dados (<code>validarDados</code>) do objeto. Verifica a conex?o com o banco de dados e a permiss?o do usu?rio
     * para realizar esta operac?o na entidade.
     * Isto, atrav?s da opera??o <code>incluir</code> da superclasse.
     * @param obj  Objeto da classe <code>MonitoramentoHistoricoMesVO</code> que ser? gravado no banco de dados.
     * @exception Exception Caso haja problemas de conex?o, restri??o de acesso ou valida??o de dados.
     */
    public void incluir(MonitoramentoHistoricoMesVO obj) throws Exception {
        MonitoramentoHistoricoMesVO.validarDados(obj);
        incluir(getIdEntidade());
        String sql = "INSERT INTO MonitoramentoHistoricoMes( monitoramento, dia, ano, mes, quantidade, total, indicador) VALUES (?,?,?,?,?,?,?)";
        try (PreparedStatement sqlInserir = con.prepareStatement(sql)) {
            int cont = 1;
            sqlInserir.setInt(cont++, obj.getMonitoramento());
            sqlInserir.setDate(cont++, Uteis.getDataJDBC(obj.getDia()));
            sqlInserir.setInt(cont++, obj.getAno());
            sqlInserir.setInt(cont++, obj.getMes());
            sqlInserir.setInt(cont++, obj.getQuantidade());
            sqlInserir.setDouble(cont++, obj.getTotal());
            sqlInserir.setString(cont++, obj.getIndicador());

            sqlInserir.execute();
        }
        obj.setCodigo(obterValorChavePrimariaCodigo());
        obj.setNovoObj(new Boolean(false));
    }

    /**
     * Opera??o respons?vel por alterar no BD os dados de um objeto da classe <code>MonitoramentoHistoricoMesVO</code>.
     * Sempre utiliza a chave prim?ria da classe como atributo para localiza??o do registro a ser alterado.
     * Primeiramente valida os dados (<code>validarDados</code>) do objeto. Verifica a conex?o com o banco de dados e a permiss?o do usu?rio
     * para realizar esta operac?o na entidade.
     * Isto, atrav?s da opera??o <code>alterar</code> da superclasse.
     * @param obj    Objeto da classe <code>MonitoramentoHistoricoMesVO</code> que ser? alterada no banco de dados.
     * @exception Execption Caso haja problemas de conex?o, restri??o de acesso ou valida??o de dados.
     */
    public void alterar(MonitoramentoHistoricoMesVO obj) throws Exception {
        MonitoramentoHistoricoMesVO.validarDados(obj);
        alterar(getIdEntidade());
        String sql = "UPDATE MonitoramentoHistoricoMes set monitoramento=?, dia=?, ano=?, mes=?, quantidade=?, total=?, indicador=? WHERE ((codigo = ?))";
        try (PreparedStatement sqlAlterar = con.prepareStatement(sql)) {
            int cont = 1;
            sqlAlterar.setInt(cont++, obj.getMonitoramento());
            sqlAlterar.setDate(cont++, Uteis.getDataJDBC(obj.getDia()));
            sqlAlterar.setInt(cont++, obj.getAno());
            sqlAlterar.setInt(cont++, obj.getMes());
            sqlAlterar.setInt(cont++, obj.getQuantidade());
            sqlAlterar.setDouble(cont++, obj.getTotal());
            sqlAlterar.setString(cont++, obj.getIndicador());
            sqlAlterar.setInt(cont++, obj.getCodigo().intValue());
            sqlAlterar.execute();
        }
    }

    /**
     * Opera??o respons?vel por excluir no BD um objeto da classe <code>MonitoramentoHistoricoMesVO</code>.
     * Sempre localiza o registro a ser exclu?do atrav?s da chave prim?ria da entidade.
     * Primeiramente verifica a conex?o com o banco de dados e a permiss?o do usu?rio
     * para realizar esta operac?o na entidade.
     * Isto, atrav?s da opera??o <code>excluir</code> da superclasse.
     * @param obj    Objeto da classe <code>MonitoramentoHistoricoMesVO</code> que ser? removido no banco de dados.
     * @exception Execption Caso haja problemas de conex?o ou restri??o de acesso.
     */
    public void excluir(MonitoramentoHistoricoMesVO obj) throws Exception {
        excluir(getIdEntidade());
        String sql = "DELETE FROM MonitoramentoHistoricoMes WHERE ((codigo = ?))";
        try (PreparedStatement sqlExcluir = con.prepareStatement(sql)) {
            sqlExcluir.setInt(1, obj.getCodigo().intValue());
            sqlExcluir.execute();
        }
    }

    public List montarMonitoramentoHistoricoMesProduto(Integer empresa, Date dataProcessamento) throws Exception {
        consultar(getIdEntidade(), true);
        String query = "SELECT 0 AS codigo, 0 AS monitoramento, prod.tipoProduto AS indicador, \n" +
                "EXTRACT (MONTH FROM mprod.datalancamento) AS mes, \n" +
                "EXTRACT (YEAR FROM mprod.datalancamento) AS ano, \n" +
                "COUNT(mprod.codigo) AS quantidade, \n" +
                "SUM(mprod.totalfinal) as total, \n" +
                "DATE('" + Uteis.getDataJDBC(dataProcessamento) + "') AS dia \n" +
                "FROM movproduto mprod\n" +
                "INNER JOIN produto prod ON mprod.produto = prod.codigo\n" +
                "WHERE mprod.situacao <> 'CA'\n" +
                "AND mprod.quitado\n" +
                "AND EXTRACT (YEAR FROM mprod.datalancamento) = " + Uteis.getAnoData(dataProcessamento) + "\n" +
                "AND mprod.empresa = " + empresa + "\n" +
                "GROUP BY EXTRACT (YEAR FROM mprod.datalancamento), EXTRACT (MONTH FROM mprod.datalancamento), prod.tipoProduto\n" +
                "ORDER BY EXTRACT (YEAR FROM mprod.datalancamento), EXTRACT (MONTH FROM mprod.datalancamento), prod.tipoProduto\n";
        try (Statement stm = con.createStatement()) {
            try (ResultSet tabelaResultado = stm.executeQuery(query)) {
                return montarDadosConsulta(tabelaResultado, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
            }
        }
    }
    public List consultarPorMonitoramentoMesAno(Integer monitoramento, Integer mes, Integer ano, int nivelMontarDados) throws Exception {
        consultar(getIdEntidade(), true);
        String sqlStr = "SELECT * FROM MonitoramentoHistoricoMes\n" +
                "WHERE ano = " + ano + "\n" +
                "AND mes = " + ano + "\n" +
                "AND monitoramento = " + monitoramento + "\n" +
                "ORDER BY ano, mes, indicador";
        try (Statement stm = con.createStatement()) {
            try (ResultSet tabelaResultado = stm.executeQuery(sqlStr)) {
                return montarDadosConsulta(tabelaResultado, nivelMontarDados);
            }
        }
    }

    public List consultarPorMonitoramentoMesAnoTipoProduto(Integer monitoramento, Integer mes, Integer ano, String indicador, int nivelMontarDados) throws Exception {
        consultar(getIdEntidade(), true);
        String sqlStr = "SELECT * FROM MonitoramentoHistoricoMes\n" +
                "WHERE ano = " + ano + "\n" +
                "AND mes = " + ano + "\n" +
                "AND monitoramento = " + monitoramento + "\n" +
                "AND indicador = '" + indicador + "'\n" +
                "ORDER BY ano, mes, tipoProduto";
        try (Statement stm = con.createStatement()) {
            try (ResultSet tabelaResultado = stm.executeQuery(sqlStr)) {
                return montarDadosConsulta(tabelaResultado, nivelMontarDados);
            }
        }
    }


    public List montarDadosConsulta(ResultSet tabelaResultado, int nivelMontarDados) throws Exception {
        List vetResultado = new ArrayList();
        while (tabelaResultado.next()) {
            MonitoramentoHistoricoMesVO obj = new MonitoramentoHistoricoMesVO();
            obj = montarDados(tabelaResultado, nivelMontarDados);
            vetResultado.add(obj);
        }
        tabelaResultado = null;
        return vetResultado;
    }

    public MonitoramentoHistoricoMesVO montarDados(ResultSet dadosSQL, int nivelMontarDados) throws Exception {
        MonitoramentoHistoricoMesVO obj = new MonitoramentoHistoricoMesVO();
        obj.setCodigo(dadosSQL.getInt("codigo"));
        obj.setMonitoramento(dadosSQL.getInt("monitoramento"));
        obj.setAno(dadosSQL.getInt("ano"));
        obj.setMes(dadosSQL.getInt("mes"));
        obj.setDia(dadosSQL.getDate("dia"));
        obj.setQuantidade(dadosSQL.getInt("quantidade"));
        obj.setTotal(dadosSQL.getDouble("total"));
        obj.setIndicador(dadosSQL.getString("indicador"));
        obj.setNovoObj(false);
        return obj;
    }


    public void excluirMonitoramentoHistoricoMesPorMonitoramento(Integer monitoramento) throws Exception {
        excluirMonitoramentoHistoricoMesPorMonitoramento(monitoramento, true);
    }

    public void excluirMonitoramentoHistoricoMesPorMonitoramento(Integer monitoramento, boolean verificarAcesso) throws Exception {
        if(verificarAcesso) {
            excluir(getIdEntidade());
        }
        String sql = "DELETE FROM MonitoramentoHistoricoMes WHERE (monitoramento = ?)";
        try (PreparedStatement sqlExcluir = con.prepareStatement(sql)) {
            sqlExcluir.setInt(1, monitoramento.intValue());
            sqlExcluir.execute();
        }
    }
    public void excluirMonitoramentoHistoricoMesPorChave(String chave, Integer empresa, Date dataInicio, Date dataFinal, boolean verificarAcesso) throws Exception {
        if(verificarAcesso) {
            excluir(getIdEntidade());
        }
        String sql = "DELETE FROM MonitoramentoHistoricoMes WHERE monitoramento IN\n" +
                "(SELECT codigo FROM monitoramento\n" +
                "WHERE monitoramento.chaveecodigo = ?\n" +
                "AND monitoramento.dia::date >= ?" +
                "AND monitoramento.dia::date <= ?)";
        try (PreparedStatement sqlExcluir = con.prepareStatement(sql)) {
            int i = 0;
            sqlExcluir.setString(++i, chave + "-" + empresa);
            sqlExcluir.setDate(++i, Uteis.getDataJDBC(dataInicio));
            sqlExcluir.setDate(++i, Uteis.getDataJDBC(dataFinal));
            sqlExcluir.execute();
        }
    }

    public void alterarMonitoramentoHistoricoMes(Integer monitoramento, List objetos) throws Exception {
        String str = "DELETE FROM MonitoramentoHistoricoMes WHERE monitoramento = " + monitoramento;
        Iterator i = objetos.iterator();
        while (i.hasNext()) {
            MonitoramentoHistoricoMesVO objeto = (MonitoramentoHistoricoMesVO) i.next();
            str += " AND codigo <> " + objeto.getCodigo().intValue();
        }
        try (PreparedStatement sqlExcluir = con.prepareStatement(str)) {
            sqlExcluir.execute();
        }
        Iterator e = objetos.iterator();
        while (e.hasNext()) {
            MonitoramentoHistoricoMesVO objeto = (MonitoramentoHistoricoMesVO) e.next();
            if (objeto.getCodigo().equals(0)) {
                incluir(objeto);
            } else {
                alterar(objeto);
            }
        }
    }

    public void incluirMonitoramentoHistoricoMes(Integer monitoramentoPrm, List objetos) throws Exception {
        Iterator e = objetos.iterator();
        while (e.hasNext()) {
            MonitoramentoHistoricoMesVO obj = (MonitoramentoHistoricoMesVO) e.next();
            obj.setMonitoramento(monitoramentoPrm);
            incluir(obj);
        }
    }

    public List consultarMonitoramentoHistoricoMesPorMonitoramento(Integer monitoramento, int nivelMontarDados) throws Exception {
        consultar(getIdEntidade());
        List objetos = new ArrayList();
        String sql = "SELECT * FROM MonitoramentoHistoricoMes WHERE monitoramento = ?";
        try (PreparedStatement sqlConsulta = con.prepareStatement(sql)) {
            sqlConsulta.setInt(1, monitoramento.intValue());
            try (ResultSet resultado = sqlConsulta.executeQuery()) {
                while (resultado.next()) {
                    MonitoramentoHistoricoMesVO novoObj = new MonitoramentoHistoricoMesVO();
                    novoObj = montarDados(resultado, nivelMontarDados);
                    objetos.add(novoObj);
                }
            }
        }
        return objetos;
    }

    public MonitoramentoHistoricoMesVO consultarPorChavePrimaria(Integer codigoPrm, int nivelMontarDados) throws Exception {
        consultar(getIdEntidade(), false);
        String sql = "SELECT * FROM MonitoramentoHistoricoMes WHERE codigo = ?";
        try (PreparedStatement sqlConsultar = con.prepareStatement(sql)) {
            sqlConsultar.setInt(1, codigoPrm.intValue());
            try (ResultSet tabelaResultado = sqlConsultar.executeQuery()) {
                if (!tabelaResultado.next()) {
                    throw new ConsistirException("Dados N?o Encontrados ( MonitoramentoHistoricoMes ).");
                }
                return (montarDados(tabelaResultado, nivelMontarDados));
            }
        }
    }

}

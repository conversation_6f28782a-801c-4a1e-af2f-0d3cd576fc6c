package negocio.facade.jdbc.financeiro;

import negocio.comuns.financeiro.ContaBancariaFornecedorVO;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisValidacao;
import negocio.facade.jdbc.arquitetura.SuperEntidade;
import negocio.facade.jdbc.basico.Pessoa;
import negocio.interfaces.financeiro.ContaBancariaFornecedorInterfaceFacade;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.Statement;

public class ContaBancariaFornecedor extends SuperEntidade implements ContaBancariaFornecedorInterfaceFacade {

    public ContaBancariaFornecedor() throws Exception {
        super();
    }

    public ContaBancariaFornecedor(Connection con) throws Exception {
        super(con);
    }

    public void incluir(ContaBancariaFornecedorVO obj) throws Exception {
        ContaBancariaFornecedorVO.validarDados(obj);
        String sql = "INSERT INTO ContaBancariaFornecedor( agency_number, agency_digit, account_number, account_digit, "
                + "pessoa, banco, cpfOuCnpj ) VALUES ( ?, ?, ?, ?, ?, ?, ?)";
        PreparedStatement sqlInserir = con.prepareStatement(sql);
        sqlInserir.setString(1, obj.getAgency_number().trim());
        sqlInserir.setString(2, obj.getAgency_digit().trim());
        sqlInserir.setString(3, obj.getAccount_number().trim());
        sqlInserir.setString(4, obj.getAccount_digit().trim());
        sqlInserir.setInt(5, obj.getPessoaVO().getCodigo());
        sqlInserir.setInt(6, obj.getBancoVO().getCodigo());
        sqlInserir.setString(7, obj.getCpfOuCnpj().trim());
        sqlInserir.execute();
        obj.setCodigo(obterValorChavePrimariaCodigo());
    }

    public void alterar(ContaBancariaFornecedorVO obj) throws Exception {
        ContaBancariaFornecedorVO.validarDados(obj);
        String update = "update ContaBancariaFornecedor set agency_number = ?, " +
                "agency_digit = ?, account_number = ?, " +
                "account_digit = ?, pessoa = ?, banco = ?, cpfOuCnpj = ? where codigo = ?";
        PreparedStatement ps = con.prepareStatement(update);
        ps.setString(1, obj.getAgency_number());
        ps.setString(2, obj.getAgency_digit());
        ps.setString(3, obj.getAccount_number());
        ps.setString(4, obj.getAccount_digit());
        ps.setInt(5, obj.getPessoaVO().getCodigo());
        ps.setInt(6, obj.getBancoVO().getCodigo());
        ps.setString(7, obj.getCpfOuCnpj().trim());

        ps.setInt(8, obj.getCodigo());
        ps.executeUpdate();
    }

    public void excluirEIncluirSemCommit(ContaBancariaFornecedorVO obj) throws Exception {
        ContaBancariaFornecedorVO.validarDados(obj);
        try {
            con.setAutoCommit(false);
            excluirPorCodPessoa(obj);
            incluir(obj);
            con.commit();
        } catch (Exception e) {
            con.rollback();
            con.setAutoCommit(true);
            throw e;
        } finally {
            con.setAutoCommit(true);
        }
    }

    public void excluirPorCodigo(int codigo) throws Exception {
        String sql = "DELETE FROM ContaBancariaFornecedor WHERE codigo = ?";
        PreparedStatement sqlExcluir = con.prepareStatement(sql);
        sqlExcluir.setInt(1, codigo);
        sqlExcluir.execute();
    }

    public void excluirPorCodPessoa(ContaBancariaFornecedorVO obj) throws Exception {
        if (obj.getPessoaVO() == null || UteisValidacao.emptyNumber(obj.getPessoaVO().getCodigo())) {
            throw new Exception("Informe a pessoa para excluir a conta bancária do fornecedor.");
        }
        String sql = "DELETE FROM ContaBancariaFornecedor WHERE pessoa = ?";
        PreparedStatement sqlExcluir = con.prepareStatement(sql);
        sqlExcluir.setInt(1, obj.getPessoaVO().getCodigo());
        sqlExcluir.execute();
    }

    public ContaBancariaFornecedorVO consultarPorPessoa(int codPessoa, int nivelMontarDados) throws Exception {
        String sqlStr = "SELECT * FROM ContaBancariaFornecedor WHERE pessoa = " + codPessoa;
        Statement stm = con.createStatement();
        ResultSet tabelaResultado = stm.executeQuery(sqlStr);
        if (tabelaResultado.next()) {
            return montarDados(tabelaResultado, nivelMontarDados, this.con);
        }
        return null;
    }

    public ContaBancariaFornecedorVO consultarPorCodigo(int codigo, int nivelMontarDados) throws Exception {
        String sqlStr = "SELECT * FROM ContaBancariaFornecedor WHERE codigo = " + codigo;
        Statement stm = con.createStatement();
        ResultSet tabelaResultado = stm.executeQuery(sqlStr);
        if (tabelaResultado.next()) {
            return montarDados(tabelaResultado, nivelMontarDados, this.con);
        }
        return null;
    }

    public Integer obterCodigoPorNumeroContaEAgencia(String account_number, String agency_number) throws Exception {
        String sqlStr = "SELECT codigo FROM ContaBancariaFornecedor WHERE account_number = '" + account_number + "' AND agency_number = '" + agency_number + "'";
        Statement stm = con.createStatement();
        ResultSet tabelaResultado = stm.executeQuery(sqlStr);
        if (tabelaResultado.next()) {
            return tabelaResultado.getInt("codigo");
        }
        return null;
    }

    public ContaBancariaFornecedorVO montarDados(ResultSet dadosSQL, int nivelMontarDados, Connection con) throws Exception {
        ContaBancariaFornecedorVO obj = new ContaBancariaFornecedorVO();
        obj.setCodigo(dadosSQL.getInt("codigo"));
        if (nivelMontarDados == Uteis.NIVELMONTARDADOS_MINIMOS) {
            return obj;
        }
        obj.setAgency_number(dadosSQL.getString("agency_number"));
        obj.setAgency_digit(dadosSQL.getString("agency_digit"));
        obj.setAccount_number(dadosSQL.getString("account_number"));
        obj.setAccount_digit(dadosSQL.getString("account_digit"));
        obj.setCpfOuCnpj(dadosSQL.getString("cpfOuCnpj"));
        if (nivelMontarDados == Uteis.NIVELMONTARDADOS_TODOS) {
            Pessoa pessoaDAO;
            Banco bancoDAO;
            try {
                pessoaDAO = new Pessoa(con);
                bancoDAO = new Banco(con);
                obj.setPessoaVO(pessoaDAO.consultarPorCodigo(dadosSQL.getInt("pessoa"), Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA));
                obj.setBancoVO(bancoDAO.consultarCodigo(dadosSQL.getInt("banco"), false, Uteis.NIVELMONTARDADOS_DADOSBASICOS));
            } catch (Exception e) {
                e.printStackTrace();
            } finally {
                pessoaDAO = null;
                bancoDAO = null;
            }
        }
        return obj;
    }

}

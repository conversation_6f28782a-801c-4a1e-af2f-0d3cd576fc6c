package negocio.facade.jdbc.financeiro;

import cfin.BeanFin;
import cfin.wrapper.TFavorecido;
import cfin.wrapper.TResultadoBoleto;
import cfin.wrapper.TResultadoParcelaConsultada;
import cfin.wrapper.TResultadoParcelasEmAberto;
import negocio.comuns.utilitarias.Calendario;
import negocio.comuns.utilitarias.Uteis;
import negocio.interfaces.financeiro.FinanceiroPactoInterfaceFacade;
import ws.TEfeitoParcelaAgendamento;
import ws.TResultadoTransacaoWS;

import java.io.Serializable;
import java.rmi.RemoteException;
import java.sql.Connection;

public class FinanceiroPacto implements FinanceiroPactoInterfaceFacade, Serializable {

    BeanFin bean;

    public FinanceiroPacto() {
        bean = new BeanFin(Uteis.getUrlFinanceiroPacto());
    }

    public FinanceiroPacto(Connection conexao) {
        bean = new BeanFin(Uteis.getUrlFinanceiroPacto());
    }

    public TResultadoBoleto obtenhaBoleto(Integer codigoParcela) throws Exception {
        return bean.getBoletoPorPacela(codigoParcela);
    }

    public TResultadoParcelasEmAberto obterParcelasEmAberto(String chave, int codEmpresa) throws Exception {
        return bean.getListaParcelasEmAberto(chave, codEmpresa, Calendario.getInstance());
    }

    public TResultadoParcelaConsultada obterParcelasAcademia(String chave, int codEmpresa, int qtdeParcelasRetornar) throws Exception {
        return bean.getListaParcelasAcademia(chave, codEmpresa, Calendario.getInstance(), qtdeParcelasRetornar);
    }

    public TResultadoBoleto regerarParcela(int codParcela, boolean calcularCobrancasExtras) throws RemoteException {
        return bean.reimprimirBoletoPorPacela(codParcela, calcularCobrancasExtras);
    }


    public TResultadoTransacaoWS lancarAgendamento(String chave, Integer codigoEmpresa, String descricao, double valorTotal, Integer numParcelas, String dataParcela, TEfeitoParcelaAgendamento efeito) throws RemoteException {
        return bean.lancarAgendamento(chave, codigoEmpresa, descricao, valorTotal, numParcelas, dataParcela, efeito);
    }

    public TFavorecido consultarFavorecido(String cpf_cnpj) throws RemoteException {
        return bean.consultarFavorecido(cpf_cnpj);
    }
}

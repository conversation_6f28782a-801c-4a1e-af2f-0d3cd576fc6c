/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package negocio.facade.jdbc.financeiro;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.util.ArrayList;
import java.util.List;
import negocio.comuns.arquitetura.UsuarioVO;
import negocio.comuns.basico.EmpresaVO;
import negocio.comuns.financeiro.BloqueioCaixaVO;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisValidacao;
import negocio.facade.jdbc.arquitetura.SuperEntidade;
import negocio.facade.jdbc.arquitetura.Usuario;
import negocio.facade.jdbc.basico.Empresa;
import negocio.interfaces.arquitetura.UsuarioInterfaceFacade;
import negocio.interfaces.basico.EmpresaInterfaceFacade;
import negocio.interfaces.financeiro.BloqueioCaixaInterfaceFacade;

/**
 *
 * <AUTHOR>
 */
public class BloqueioCaixa extends SuperEntidade implements BloqueioCaixaInterfaceFacade{
    public BloqueioCaixa() throws Exception {
        super();
    }

    public BloqueioCaixa(Connection con) throws Exception {
        super(con);
    }

    @Override
    public void incluir(BloqueioCaixaVO bloqueioCaixaVo) throws Exception {
        StringBuilder sb = new StringBuilder();
        BloqueioCaixaVO.validarDados(bloqueioCaixaVo);
        sb.append("insert into bloqueiocaixa(usuarioresponsavel, empresa, databloqueio, lancamento, useroamd) ");
        sb.append("values(?,?,?,?,?)");
        PreparedStatement pst = this.con.prepareStatement(sb.toString());
        pst.setInt(1, bloqueioCaixaVo.getUsuarioResponsavel().getCodigo());
        pst.setInt(2, bloqueioCaixaVo.getEmpresa().getCodigo());
        pst.setDate(3, Uteis.getDataJDBC(bloqueioCaixaVo.getDataBloqueio()));
        pst.setTimestamp(4, Uteis.getDataJDBCTimestamp(bloqueioCaixaVo.getLancamento()));
        pst.setString(5, bloqueioCaixaVo.getUserOamd());
        pst.execute();
    }

    @Override
    public void alterar(BloqueioCaixaVO bloqueioCaixaVo) throws Exception {
        StringBuilder sb = new StringBuilder();
        BloqueioCaixaVO.validarDados(bloqueioCaixaVo);
        sb.append("update bloqueiocaixa set usuarioresponsavel=?, empresa = ?, databloqueio = ? where codigo =? ");
        PreparedStatement pst = this.con.prepareStatement(sb.toString());
        pst.setInt(1, bloqueioCaixaVo.getUsuarioResponsavel().getCodigo());
        pst.setInt(2, bloqueioCaixaVo.getEmpresa().getCodigo());
        pst.setDate(3, Uteis.getDataJDBC(bloqueioCaixaVo.getDataBloqueio()));
        pst.setInt(4, bloqueioCaixaVo.getCodigo());
        
        pst.execute();
    }

    @Override
    public void excluir(BloqueioCaixaVO bloqueioCaixaVo) throws Exception {
        StringBuilder sb = new StringBuilder();
        sb.append("delete from bloqueiocaixa where codigo =? ");
        PreparedStatement pst = this.con.prepareStatement(sb.toString());
        pst.setInt(1, bloqueioCaixaVo.getCodigo());
        pst.execute();
    }

    @Override
    public BloqueioCaixaVO consultarBloqueioAtual(Integer codigoEmpresa, int nivelMontarDados) throws Exception {
        StringBuilder sb = new StringBuilder();
        sb.append(" select * from bloqueiocaixa ");
        if(!UteisValidacao.emptyNumber(codigoEmpresa)){
            sb.append(" WHERE empresa = ? ");
        }
        sb.append(" ORDER BY lancamento DESC LIMIT 1");
        PreparedStatement pst = this.con.prepareStatement(sb.toString());
        if(!UteisValidacao.emptyNumber(codigoEmpresa)){
            pst.setInt(1, codigoEmpresa);
        }
        ResultSet rs = pst.executeQuery();
        return rs.next() ? montarDados(rs, nivelMontarDados, con) : null;
    }
    
    public BloqueioCaixaVO montarDados(ResultSet dados, int nivelMontarDados, Connection con)throws Exception{
       BloqueioCaixaVO obj = new BloqueioCaixaVO();
       obj.setCodigo(dados.getInt("codigo"));
       obj.setUserOamd(dados.getString("useroamd"));
       obj.setUsuarioResponsavel(new UsuarioVO());
       obj.setEmpresa(new EmpresaVO());
       obj.getUsuarioResponsavel().setCodigo(dados.getInt("usuarioresponsavel"));
       obj.getEmpresa().setCodigo(dados.getInt("empresa"));
       obj.setLancamento(dados.getTimestamp("lancamento"));
       obj.setDataBloqueio(dados.getDate("databloqueio"));
       if (nivelMontarDados == Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA){
           return obj;
       }
       EmpresaInterfaceFacade edao = new Empresa(con);
       obj.setEmpresa(edao.consultarPorChavePrimaria(obj.getEmpresa().getCodigo(), Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA));
       UsuarioInterfaceFacade udao = new Usuario(con);
       obj.setUsuarioResponsavel(udao.consultarPorChavePrimaria(obj.getUsuarioResponsavel().getCodigo(), Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA));
       return obj;
    }
    
    private List<BloqueioCaixaVO> montarDadosConsulta(ResultSet resultDados, int nivelMontarDados, Connection con)throws Exception{
        List<BloqueioCaixaVO> lista = new ArrayList<BloqueioCaixaVO>();
        while (resultDados.next()){
          lista.add(montarDados(resultDados, nivelMontarDados, con));
        }
        return lista;
    }


    @Override
    public List<BloqueioCaixaVO> consultar(Integer codigoEmpresa, int nivelMontarDados) throws Exception {
        StringBuilder sb = new StringBuilder();
        sb.append(" select * from bloqueiocaixa ");
        if(!UteisValidacao.emptyNumber(codigoEmpresa)){
            sb.append(" WHERE empresa = ? ");
        }
        sb.append(" ORDER BY lancamento DESC");
        PreparedStatement pst = this.con.prepareStatement(sb.toString());
        if(!UteisValidacao.emptyNumber(codigoEmpresa)){
            pst.setInt(1, codigoEmpresa);
        }
        ResultSet rs = pst.executeQuery();
        return montarDadosConsulta(rs, nivelMontarDados, con);
    }

}

package negocio.facade.jdbc.financeiro;

import br.com.pactosolucoes.integracao.conciliadora.EstornoConciliadoraTO;
import negocio.comuns.utilitarias.Calendario;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisValidacao;
import negocio.facade.jdbc.arquitetura.SuperEntidade;
import negocio.facade.jdbc.arquitetura.SuperFacadeJDBC;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

public class ConciliadoraEstorno extends SuperEntidade {

    public ConciliadoraEstorno(Connection conexao) throws Exception {
        super(conexao);
    }

    public void estornarConciliadora(Integer recibo) {
        try {
            if (UteisValidacao.emptyNumber(recibo)) {
                return;
            }

            StringBuilder sql = new StringBuilder();
            sql.append("select  \n");
            sql.append("mp.codigo as movpagamento, \n");
            sql.append("mp.recibopagamento, \n");
            sql.append("e.codigo as empresa, \n");
            sql.append("mp.dataLancamento, \n");
            sql.append("e.empresaConciliadora, \n");
            sql.append("e.senhaConciliadora \n");
            sql.append("From movpagamento mp \n");
            sql.append("inner join empresa e on e.codigo = mp.empresa \n");
            sql.append("where mp.enviadoconciliadora = true  \n");
            sql.append("and mp.recibopagamento = ").append(recibo);
            ResultSet rs = SuperFacadeJDBC.criarConsulta(sql.toString(), con);
            while (rs.next()) {
                try {
                    Integer movpagamento = rs.getInt("movpagamento");
                    Integer recibopagamento = rs.getInt("recibopagamento");
                    Integer empresa = rs.getInt("empresa");
                    String empresaConciliadora = rs.getString("empresaConciliadora");
                    String senhaConciliadora = rs.getString("senhaConciliadora");
                    Date dataLancamento = rs.getTimestamp("dataLancamento");

                    if (!UteisValidacao.emptyNumber(movpagamento)) {
                        String insert = "INSERT INTO conciliadoraestorno(dataregistro, movpagamento, recibopagamento, empresa, dataLancamento, empresaConciliadora, senhaConciliadora, processado) " +
                                "VALUES (?, ?, ?, ?, ?, ?, ?, ?);";
                        try (PreparedStatement ps = con.prepareStatement(insert)) {
                            int i = 0;
                            ps.setTimestamp(++i, Uteis.getDataJDBCTimestamp(Calendario.hoje()));
                            ps.setInt(++i, movpagamento);
                            ps.setInt(++i, recibopagamento);
                            ps.setInt(++i, empresa);
                            ps.setTimestamp(++i, Uteis.getDataJDBCTimestamp(dataLancamento));
                            ps.setString(++i, empresaConciliadora);
                            ps.setString(++i, senhaConciliadora);
                            ps.setBoolean(++i, false);
                            ps.execute();
                        }
                    }
                } catch (Exception ignored) {
                }
            }
        } catch (Exception ex) {
            ex.printStackTrace();
        }
    }

    public List<EstornoConciliadoraTO> consultarEstornosConciliacao(Integer reciboPagamento) {
        List<EstornoConciliadoraTO> lista = new ArrayList<>();
        try {
            StringBuilder sql = new StringBuilder();
            sql.append("select \n");
            sql.append("* \n");
            sql.append("From conciliadoraestorno \n");
            sql.append("where processado = false \n");
            if (!UteisValidacao.emptyNumber(reciboPagamento)) {
                sql.append("and reciboPagamento =  ").append(reciboPagamento).append(" \n");
            }

            ResultSet rs = SuperFacadeJDBC.criarConsulta(sql.toString(), con);
            while (rs.next()) {
                try {
                    Integer codigo = rs.getInt("codigo");
                    Integer movpagamento = rs.getInt("movpagamento");
                    Integer empresa = rs.getInt("empresa");
                    String empresaConciliadora = rs.getString("empresaConciliadora");
                    String senhaConciliadora = rs.getString("senhaConciliadora");
                    Date dataLancamento = rs.getTimestamp("dataLancamento");

                    EstornoConciliadoraTO novo = new EstornoConciliadoraTO();
                    novo.setCodigo(codigo);
                    novo.setMovPagamento(movpagamento);
                    novo.setEmpresa(empresa);
                    novo.setDataLancamento(dataLancamento);
                    novo.setEmpresaConciliadora(empresaConciliadora);
                    novo.setSenhaConciliadora(senhaConciliadora);
                    lista.add(novo);
                } catch (Exception ignored) {
                }
            }
        } catch (Exception ex) {
            ex.printStackTrace();
        }
        return lista;
    }

    public void marcarEstornarConciliadoraProcessado(boolean processado, Integer codigo) {
        try {
            String sql = "UPDATE conciliadoraestorno set processado = ? WHERE codigo = ?";
            try (PreparedStatement ps = con.prepareStatement(sql)) {
                ps.setBoolean(1, processado);
                ps.setInt(2, codigo);
                ps.executeUpdate();
            }
        } catch (Exception ignored) {

        }
    }

}

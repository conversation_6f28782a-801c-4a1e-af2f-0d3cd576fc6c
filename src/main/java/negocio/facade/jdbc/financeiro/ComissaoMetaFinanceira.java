package negocio.facade.jdbc.financeiro;

import negocio.comuns.basico.EmpresaVO;
import negocio.comuns.contrato.ComissaoGeralConfiguracaoVO;
import negocio.comuns.financeiro.ComissaoMetaFinananceiraVO;
import negocio.comuns.financeiro.MetaFinanceiraEmpresaVO;
import negocio.comuns.utilitarias.Calendario;
import negocio.comuns.utilitarias.ConsistirException;
import negocio.comuns.utilitarias.Uteis;
import negocio.facade.jdbc.arquitetura.SuperEntidade;
import negocio.interfaces.financeiro.ComissaoMetaFinanceiraInterfaceFacade;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.List;

/**
 * Created by ulisses on 28/09/2015.
 */
public class ComissaoMetaFinanceira extends SuperEntidade implements ComissaoMetaFinanceiraInterfaceFacade {

    public ComissaoMetaFinanceira() throws Exception {
        super();
    }

    public ComissaoMetaFinanceira(Connection con) throws Exception {
        super(con);
    }


    public void incluirSemCommit(ComissaoGeralConfiguracaoVO comissaoGeralConfiguracaoVO) throws Exception{
        EmpresaVO empresaVO = getFacade().getEmpresa().consultarPorCodigo(comissaoGeralConfiguracaoVO.getEmpresa().getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS);
        if (empresaVO.isPagarComissaoSeAtingirMetaFinanceira()){
            Calendar vigenciaInicial = Calendario.getInstance();
            Calendar vigenciaFinal = Calendario.getInstance();
            vigenciaInicial.setTime(comissaoGeralConfiguracaoVO.getVigenciaInicio());
            vigenciaFinal.setTime(comissaoGeralConfiguracaoVO.getVigenciaFinal());
            vigenciaInicial.set(Calendar.DAY_OF_MONTH,1);
            while (vigenciaInicial.compareTo(vigenciaFinal) <=0){
                int mes =  (vigenciaInicial.get(Calendar.MONTH)) + 1;
                int ano = vigenciaInicial.get(Calendar.YEAR);
                List<MetaFinanceiraEmpresaVO> listaMeta = getFacade().getMetaFinanceiraEmpresa().consultarPorEmpresaAnoMesDescricao(empresaVO.getCodigo(),ano,mes,"", Uteis.NIVELMONTARDADOS_DADOSBASICOS);
                if (listaMeta.isEmpty()){
                    throw new ConsistirException("Operação não permitida. Não foi encontrada nenhuma meta financeira cadastrada para o mês '" + mes + "/" + ano);
                }
                vigenciaInicial.add(Calendar.MONTH, 1);
            }

            String sqlDel = "delete from ComissaoMetaFinananceira where comissaogeralconfiguracao = ?";
            PreparedStatement pstDel = con.prepareStatement(sqlDel);
            pstDel.setInt(1, comissaoGeralConfiguracaoVO.getCodigo());
            pstDel.execute();

            StringBuilder sql = new StringBuilder();
            sql.append("insert into ComissaoMetaFinananceira (comissaogeralconfiguracao, codigoMeta,valorespontaneo,valoragendado,porcentagemespontaneo,porcentagemagendado)");
            sql.append(" values(?,?,?,?,?,?)");
            PreparedStatement pst = con.prepareStatement(sql.toString());
            for (ComissaoMetaFinananceiraVO comissaoMetaFinananceiraVO: comissaoGeralConfiguracaoVO.getListaComissaoMeta()){
                pst.setInt(1, comissaoGeralConfiguracaoVO.getCodigo());
                pst.setInt(2, comissaoMetaFinananceiraVO.getCodigoMeta());
                pst.setDouble(3, comissaoMetaFinananceiraVO.getValorEspontaneo());
                pst.setDouble(4, comissaoMetaFinananceiraVO.getValorAgendado());
                pst.setDouble(5, comissaoMetaFinananceiraVO.getPorcentagemEspontaneo());
                pst.setDouble(6, comissaoMetaFinananceiraVO.getPorcentagemAgendado());
                pst.execute();
            }

        }
    }

    public void excluirSemCommmit(Integer codigoComissaoGeralConfiguracao)throws Exception{
        String sql = "DELETE FROM ComissaoMetaFinananceira WHERE comissaogeralconfiguracao = ?";
        PreparedStatement sqlExcluir = con.prepareStatement(sql);
        sqlExcluir.setInt(1, codigoComissaoGeralConfiguracao);
        sqlExcluir.execute();

    }

    public ComissaoMetaFinananceiraVO consultar(Integer codigoComissaoGeralConfiguracao, Integer codigoMeta , int nivelMontarDados) throws Exception{
        StringBuilder sql = new StringBuilder();
        sql.append("select * \n");
        sql.append("from ComissaoMetaFinananceira \n");
        sql.append("where comissaogeralconfiguracao = ").append(codigoComissaoGeralConfiguracao).append(" \n");
        sql.append("and codigoMeta = ").append(codigoMeta);
        PreparedStatement pst = con.prepareStatement(sql.toString());
        ResultSet rs = pst.executeQuery();
        if (rs.next()){
            return montarDados(rs,nivelMontarDados);
        }
        return null;
    }

    public List<ComissaoMetaFinananceiraVO> consultar(ComissaoGeralConfiguracaoVO comissaoGeralConfiguracaoVO, int nivelMontarDados)throws Exception{
        StringBuilder sql = new StringBuilder();
        sql.append("select * from ComissaoMetaFinananceira where comissaogeralconfiguracao = ").append(comissaoGeralConfiguracaoVO.getCodigo());
        PreparedStatement pst = con.prepareStatement(sql.toString());
        ResultSet rs = pst.executeQuery();
        return montarDadosConsulta(rs, nivelMontarDados);
    }

    private List<ComissaoMetaFinananceiraVO> montarDadosConsulta(ResultSet rs, int nivelMontarDados)throws Exception{
        List<ComissaoMetaFinananceiraVO> lista = new ArrayList<ComissaoMetaFinananceiraVO>();
        while (rs.next()){
            lista.add(montarDados(rs, nivelMontarDados));
        }
        return lista;
    }

    private ComissaoMetaFinananceiraVO montarDados(ResultSet rs, int nivelMontarDados)throws Exception{
        ComissaoMetaFinananceiraVO comissaoMetaFinananceiraVO = new ComissaoMetaFinananceiraVO();
        comissaoMetaFinananceiraVO.setCodigo(rs.getInt("codigo"));
        ComissaoGeralConfiguracaoVO comissaoGeralConfiguracaoVO = new ComissaoGeralConfiguracaoVO();
        comissaoGeralConfiguracaoVO.setCodigo(rs.getInt("comissaogeralconfiguracao"));
        comissaoMetaFinananceiraVO.setComissaoGeralConfiguracaoVO(comissaoGeralConfiguracaoVO);
        comissaoMetaFinananceiraVO.setCodigoMeta(rs.getInt("codigoMeta"));
        comissaoMetaFinananceiraVO.setValorEspontaneo(rs.getDouble("valorEspontaneo"));
        comissaoMetaFinananceiraVO.setValorAgendado(rs.getDouble("valorAgendado"));
        comissaoMetaFinananceiraVO.setPorcentagemEspontaneo(rs.getDouble("porcentagemEspontaneo"));
        comissaoMetaFinananceiraVO.setPorcentagemAgendado(rs.getDouble("porcentagemAgendado"));
        return comissaoMetaFinananceiraVO;
    }

}

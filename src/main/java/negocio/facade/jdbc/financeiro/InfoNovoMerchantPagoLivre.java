package negocio.facade.jdbc.financeiro;

import negocio.comuns.financeiro.InfoNovoMerchantPagoLivreVO;
import negocio.comuns.utilitarias.Uteis;
import negocio.facade.jdbc.arquitetura.SuperEntidade;

import java.sql.PreparedStatement;

public class InfoNovoMerchantPagoLivre extends SuperEntidade {

    public InfoNovoMerchantPagoLivre() throws Exception {
    }

    public void incluir(InfoNovoMerchantPagoLivreVO obj) throws Exception {
        try {
            String sql = "INSERT INTO InfoNovoMerchantPagoLivre( dataRegistro, paramsEnvio, paramsresposta, tipoConvenioCobranca) "
                    + "VALUES (?, ?, ?, ?)";
            try (PreparedStatement sqlInserir = con.prepareStatement(sql)) {
                int i = 1;
                sqlInserir.setTimestamp(i++, Uteis.getDataJDBCTimestamp(obj.getDataRegistro()));
                sqlInserir.setString(i++, obj.getParamsEnvio());
                sqlInserir.setString(i++, obj.getParamsResposta());
                sqlInserir.setInt(i++, obj.getTipoConvenioCobranca());
                sqlInserir.execute();
            }
        } catch (Exception e) {
            obj.setNovoObj(true);
            throw e;
        }
    }
}

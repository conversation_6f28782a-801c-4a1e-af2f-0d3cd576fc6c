/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */

package negocio.facade.jdbc.financeiro;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.util.ArrayList;
import java.util.List;
import negocio.comuns.financeiro.CaixaContaVO;
import negocio.comuns.financeiro.CaixaVO;
import negocio.comuns.utilitarias.Uteis;
import negocio.facade.jdbc.arquitetura.SuperEntidade;
import negocio.interfaces.financeiro.CaixaContaInterfaceFacade;

/**
 *
 * <AUTHOR>
 */
public class CaixaConta extends SuperEntidade implements CaixaContaInterfaceFacade{

    public CaixaConta() throws Exception {
        super();
    }

    public CaixaConta(Connection con) throws Exception {
        super(con);
    }

    public void incluir(CaixaContaVO obj) throws Exception {
        StringBuilder sb = new StringBuilder();
        sb.append("insert into caixaConta(caixa, conta, saldoInicial, saldoFinal) ");
        sb.append("values(?,?,?,?)");
        PreparedStatement pst = this.con.prepareStatement(sb.toString());
        pst.setInt(1, obj.getCaixaVo().getCodigo());
        pst.setInt(2, obj.getContaVo().getCodigo());
        pst.setDouble(3, obj.getSaldoInicial());
        pst.setDouble(4, obj.getSaldoFinal());
        pst.execute();
        obj.setCodigo(obterValorChavePrimariaCodigo());
   }

    public void excluir(CaixaContaVO obj) throws Exception {
        StringBuilder sb = new StringBuilder();
        sb.append("delete from caixaConta where codigo = ").append(obj.getCodigo());
        PreparedStatement pst = this.con.prepareStatement(sb.toString());
        pst.execute();
   }

    public void alterar(CaixaContaVO obj) throws Exception {
        StringBuilder sb = new StringBuilder();
        sb.append("update caixaConta set conta=?, saldoInicial =?, saldoFinal=? where codigo =? ");
        PreparedStatement pst = this.con.prepareStatement(sb.toString());
        pst.setInt(1, obj.getContaVo().getCodigo());
        pst.setDouble(2, obj.getSaldoInicial());
        pst.setDouble(3, obj.getSaldoFinal());
        pst.setInt(4, obj.getCodigo());
        pst.execute();
   }

  private CaixaContaVO montarDadosBasico(ResultSet dados)throws Exception{
      CaixaContaVO obj = new CaixaContaVO() ;
      obj.setCodigo(dados.getInt("codigo"));
      obj.getCaixaVo().setCodigo(dados.getInt("caixa"));
      obj.getContaVo().setCodigo(dados.getInt("conta"));
      obj.setSaldoInicial(dados.getDouble("saldoInicial"));
      obj.setSaldoFinal(dados.getDouble("saldoFinal"));
      obj.getContaVo().setDescricao(dados.getString("descricao"));
      obj.setSaida(dados.getDouble("saida"));
      obj.setEntrada(dados.getDouble("entrada"));
      return obj;
  }

   private CaixaContaVO montarDados(ResultSet dados, int nivelMontarDados)throws Exception{
      CaixaContaVO obj = montarDadosBasico(dados);
      if (nivelMontarDados == Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA){
          return obj;
      }

      return obj;
   }

   public List<CaixaContaVO> consultar(CaixaVO caixaVo, int nivelMontarDados)throws Exception{
       List<CaixaContaVO> lista = new ArrayList<CaixaContaVO>();
       StringBuilder sb = new StringBuilder();
       sb.append("select cc.*, ct.descricao from caixaConta cc ");
       sb.append("inner join conta ct on ct.codigo = cc.conta ");
       sb.append("where cc.caixa = ").append(caixaVo.getCodigo());
       PreparedStatement pst = this.con.prepareStatement(sb.toString());
       ResultSet resultDados = pst.executeQuery();
       while (resultDados.next()){
          lista.add(montarDados(resultDados, nivelMontarDados));
       }
       return lista;
   }
}

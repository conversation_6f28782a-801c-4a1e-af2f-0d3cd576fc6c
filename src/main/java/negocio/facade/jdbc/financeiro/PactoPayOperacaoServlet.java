package negocio.facade.jdbc.financeiro;


import br.com.pactosolucoes.integracao.pactopay.OperacaoEnum;
import br.com.pactosolucoes.integracao.pactopay.SituacaoTransacaoPactoPayEnum;
import br.com.pactosolucoes.integracao.pactopay.dto.ResultadoDTO;
import br.com.pactosolucoes.integracao.pactopay.dto.TransacaoResultadoDTO;
import negocio.comuns.arquitetura.UsuarioVO;
import negocio.comuns.contrato.ContratoVO;
import negocio.comuns.financeiro.FormaPagamentoVO;
import negocio.comuns.financeiro.MovPagamentoVO;
import negocio.comuns.financeiro.MovParcelaVO;
import negocio.comuns.financeiro.OperadoraCartaoVO;
import negocio.comuns.financeiro.ReciboPagamentoVO;
import negocio.comuns.financeiro.RemessaItemVO;
import negocio.comuns.financeiro.RemessaVO;
import negocio.comuns.financeiro.TransacaoVO;
import negocio.comuns.financeiro.enumerador.OperadorasExternasAprovaFacilEnum;
import negocio.comuns.financeiro.enumerador.SituacaoRemessaEnum;
import negocio.comuns.financeiro.enumerador.SituacaoTransacaoEnum;
import negocio.comuns.financeiro.enumerador.TipoCobrancaEnum;
import negocio.comuns.financeiro.enumerador.TipoConvenioCobrancaEnum;
import negocio.comuns.utilitarias.Calendario;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisValidacao;
import negocio.facade.jdbc.arquitetura.SuperEntidade;
import negocio.facade.jdbc.arquitetura.Usuario;
import org.json.JSONObject;
import servicos.impl.apf.APF;
import servicos.impl.dcc.base.DCCAttEnum;
import servicos.impl.dcc.base.RemessaService;
import servicos.impl.pactoPay.PactoPayTransacaoService;
import servicos.integracao.impl.parceirofidelidade.base.ParceiroFidelidadeZW;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.Statement;
import java.sql.Types;
import java.util.ArrayList;
import java.util.List;

public class PactoPayOperacaoServlet extends SuperEntidade {

    private Integer codigoPactoPayWebhook;

    public PactoPayOperacaoServlet(Connection con, String body) throws Exception {
        super(con);
        incluirPactoPayWebhook(body);
    }

    public Object processarWebhook(ResultadoDTO resultadoDTO) throws Exception {
        Object objRetorno = null;
        try {

            TipoConvenioCobrancaEnum tipoConvenioCobrancaEnum = TipoConvenioCobrancaEnum.valueOf(resultadoDTO.getConvenio().getTipo());

            if (tipoConvenioCobrancaEnum.getTipoCobranca().equals(TipoCobrancaEnum.ONLINE)) {
                return processarOnline(resultadoDTO);
            } else if (tipoConvenioCobrancaEnum.getTipoCobranca().equals(TipoCobrancaEnum.EDI_DCC)) {
                return processarEDI(resultadoDTO);
            } else {
                throw new Exception("Tipo Convênio não localizado");
            }
        } catch (Exception ex) {
            ex.printStackTrace();
            //coloca o objRetorno como a mensagem para que seja salvo no banco a resposta;
            objRetorno = ex.getMessage();
            throw ex;
        } finally {
            atualizarPactoPayWebhook(resultadoDTO, null, null, objRetorno);
        }
    }

    private Object processarOnline(ResultadoDTO resultadoDTO) throws Exception {
        Transacao transacaoDAO = null;
        TransacaoVO transacaoVO = null;
        Object objRetorno = null;
        try {
            transacaoDAO = new Transacao(con);

            transacaoVO = transacaoDAO.consultarPorChavePrimaria(resultadoDTO.getTransacaoResultado().getIdReferencia());
            objRetorno = processarTransacao(transacaoVO, resultadoDTO, resultadoDTO.getTransacaoResultado());

            return "ok";
        } catch (Exception ex) {
            ex.printStackTrace();
            //coloca o objRetorno como a mensagem para que seja salvo no banco a resposta;
            objRetorno = ex.getMessage();
            throw ex;
        } finally {
            atualizarPactoPayWebhook(resultadoDTO, transacaoVO, null, objRetorno);
            transacaoDAO = null;
        }
    }

    private Object processarEDI(ResultadoDTO resultadoDTO) throws Exception {
        RemessaItem remessaItemDAO = null;
        try {
            remessaItemDAO = new RemessaItem(con);

            for (TransacaoResultadoDTO transacaoResultadoDTO : resultadoDTO.getRemessaResultado()) {
                TransacaoVO transacaoVO = null;
                RemessaItemVO remessaItemVO = null;
                Object objRetorno = null;
                try {
                    remessaItemVO = remessaItemDAO.consultarPorChavePrimaria(transacaoResultadoDTO.getIdReferencia(), Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA);
                    objRetorno = processarRemessaItem(remessaItemVO, transacaoResultadoDTO);
                } catch (Exception ex) {
                    ex.printStackTrace();
                    //coloca o objRetorno como a mensagem para que seja salvo no banco a resposta;
                    objRetorno = ex.getMessage();
                } finally {
                    atualizarPactoPayWebhook(resultadoDTO, transacaoVO, remessaItemVO, objRetorno);
                }
            }

            processarSituacaoRemessa(resultadoDTO);

            return "ok";
        } catch (Exception ex) {
            ex.printStackTrace();
            throw ex;
        } finally {
            remessaItemDAO = null;
        }
    }

    private void incluirPactoPayWebhook(String body) {
        try {
            String sql = "INSERT INTO pactopaywebhook(dataRegistro, dados) VALUES (?,?);";
            try (PreparedStatement sqlInserir = this.con.prepareStatement(sql, Statement.RETURN_GENERATED_KEYS)) {
                int i = 0;
                sqlInserir.setTimestamp(++i, Uteis.getDataJDBCTimestamp(Calendario.hoje()));
                sqlInserir.setString(++i, body);
                sqlInserir.execute();

                ResultSet rs = sqlInserir.getGeneratedKeys();
                if (rs.next()) {
                    this.codigoPactoPayWebhook = rs.getInt("codigo");
                }
            }
        } catch (Exception ex) {
            ex.printStackTrace();
        }
    }

    private void processarSituacaoRemessa(ResultadoDTO resultadoDTO) throws Exception {
        Remessa remessaDAO = null;
        Usuario usuarioDAO = null;
        try {
            remessaDAO = new Remessa(con);
            usuarioDAO = new Usuario(con);

            RemessaVO remessaVO = remessaDAO.consultarPorChavePrimaria(resultadoDTO.getRemessa().getIdReferencia());
            SituacaoRemessaEnum situacaoRemessaEnumAnterior = remessaVO.getSituacaoRemessa();

            boolean remessaProcessada = remessaDAO.processarSituacaoRemessa(remessaVO.getCodigo());

            if (remessaProcessada && !situacaoRemessaEnumAnterior.equals(SituacaoRemessaEnum.RETORNO_PROCESSADO)) {
                UsuarioVO usuarioVO = usuarioDAO.getUsuarioRecorrencia();
                remessaVO.getProps().put(DCCAttEnum.CodUsuarioRetorno.name(), usuarioVO.getCodigo().toString());
                remessaVO.getProps().put(DCCAttEnum.NomeUsuarioRetorno.name(), usuarioVO.getNome());
                remessaVO.getProps().put(DCCAttEnum.DataHoraRetorno.name(), Long.toString(Calendario.hoje().getTime()));
                if (!UteisValidacao.emptyString(remessaVO.getHeaderRetorno().get(DCCAttEnum.CodigoRetorno.name()))) {
                    remessaVO.getProps().put(DCCAttEnum.CodigoRetorno.name(), remessaVO.getHeaderRetorno().getValue(DCCAttEnum.CodigoRetorno.name()));
                }
                remessaVO.setSituacaoRemessa(SituacaoRemessaEnum.RETORNO_PROCESSADO);
                remessaDAO.alterar(remessaVO);
            }
        } catch (Exception ex) {
            ex.printStackTrace();
            throw ex;
        } finally {
            remessaDAO = null;
            usuarioDAO = null;
        }
    }

    private void atualizarPactoPayWebhook(ResultadoDTO resultadoDTO, TransacaoVO transacaoVO,
                                          RemessaItemVO remessaItemVO, Object objRetorno) {
        try {
            if (UteisValidacao.emptyNumber(this.codigoPactoPayWebhook)) {
                Uteis.logarDebug("Codigo PactoPayWebhook vazio! PactoPay Servlet");
                return;
            }

            StringBuilder sql = new StringBuilder();
            sql.append("UPDATE pactopaywebhook SET \n");
            sql.append("empresa = ?, conveniocobranca = ?, transacao = ?, \n");
            sql.append("remessa = ?, remessaitem = ?, operacao = ?, resposta = ? \n");
            sql.append("WHERE codigo = ?;");
            try (PreparedStatement sqlUpdate = this.con.prepareStatement(sql.toString())) {
                int i = 0;
                //empresa
                if (UteisValidacao.emptyNumber(resultadoDTO.getEmpresa().getIdReferencia())) {
                    sqlUpdate.setNull(++i, Types.NULL);
                } else {
                    sqlUpdate.setInt(++i, resultadoDTO.getEmpresa().getIdReferencia());
                }

                //conveniocobranca
                if (UteisValidacao.emptyNumber(resultadoDTO.getConvenio().getIdReferencia())) {
                    sqlUpdate.setNull(++i, Types.NULL);
                } else {
                    sqlUpdate.setInt(++i, resultadoDTO.getConvenio().getIdReferencia());
                }

                //transacao
                if (transacaoVO == null || UteisValidacao.emptyNumber(transacaoVO.getCodigo())) {
                    sqlUpdate.setNull(++i, Types.NULL);
                } else {
                    sqlUpdate.setInt(++i, transacaoVO.getCodigo());
                }

                //remessa remessaitem
                if (remessaItemVO == null || UteisValidacao.emptyNumber(remessaItemVO.getCodigo())) {
                    //remessa
                    sqlUpdate.setNull(++i, Types.NULL);
                    //remessaitem
                    sqlUpdate.setNull(++i, Types.NULL);
                } else {
                    //remessa
                    sqlUpdate.setInt(++i, remessaItemVO.getRemessa().getCodigo());
                    //remessaitem
                    sqlUpdate.setInt(++i, remessaItemVO.getCodigo());
                }

                //operacao
                if (UteisValidacao.emptyNumber(resultadoDTO.getOperacao())) {
                    sqlUpdate.setNull(++i, Types.NULL);
                } else {
                    sqlUpdate.setInt(++i, resultadoDTO.getOperacao());
                }

                String resposta = "";
                try {
                    try {
                        resposta = (String) objRetorno;
                    } catch (Exception ignored) {
                    }
                    if (UteisValidacao.emptyString(resposta)) {
                        resposta = new JSONObject(objRetorno).toString();
                    }
                } catch (Exception ex) {
                    ex.printStackTrace();
                }
                //resposta webhook
                sqlUpdate.setString(++i, resposta);

                //codigo PactoPayWebhook
                sqlUpdate.setInt(++i, this.codigoPactoPayWebhook);

                sqlUpdate.execute();
            }
        } catch (
                Exception ex) {
            ex.printStackTrace();
        }

    }

    private Object processarRemessaItem(RemessaItemVO remessaItemVO, TransacaoResultadoDTO transacaoResultadoDTO) throws Exception {
        RemessaItem remessaItemDAO = null;
        Remessa remessaDAO = null;
        RemessaService remessaService = null;
        Object objRetorno = null;
        try {
            remessaDAO = new Remessa(this.con);
            remessaItemDAO = new RemessaItem(this.con);
            remessaService = new RemessaService(this.con);

            SituacaoTransacaoPactoPayEnum situacaoTransacaoPactoPayEnum = SituacaoTransacaoPactoPayEnum.obterPorCodigo(transacaoResultadoDTO.getSituacao());
            if (situacaoTransacaoPactoPayEnum == null) {
                throw new Exception("Situação da transação não encontrada");
            }

            //salvar o status
            if (!UteisValidacao.emptyString(transacaoResultadoDTO.getCodigoRetorno())) {
                remessaItemVO.put(DCCAttEnum.StatusVenda.name(), transacaoResultadoDTO.getCodigoRetorno());
            }
            //autorizacao
            if (!UteisValidacao.emptyString(transacaoResultadoDTO.getAutorizacao())) {
                remessaItemVO.put(DCCAttEnum.CodigoAutorizacao.name(), transacaoResultadoDTO.getAutorizacao());
            }
            //autorizacao
            if (!UteisValidacao.emptyString(transacaoResultadoDTO.getNsu())) {
                remessaItemVO.put(DCCAttEnum.NSU.name(), transacaoResultadoDTO.getNsu());
            }

            //incluir pagamento
            if (situacaoTransacaoPactoPayEnum.equals(SituacaoTransacaoPactoPayEnum.CONCLUIDA_COM_SUCESSO) &&
                    remessaItemVO.getMovPagamento() != null &&
                    UteisValidacao.emptyNumber(remessaItemVO.getMovPagamento().getCodigo())) {
                remessaService.incluirPagamentoItem(remessaItemVO);
                objRetorno = "RemessaItem | incluirPagamento | Recibo " + remessaItemVO.getReciboPagamentoVO().getCodigo();
            } else {
                objRetorno = "ok";
            }

            remessaItemDAO.alterar(remessaItemVO);

            if (!UteisValidacao.emptyString(transacaoResultadoDTO.getCodigoRetorno())) {
                remessaService.processosPosRetornoRemessaItem(remessaItemVO);
            }

            return objRetorno;
        } catch (Exception ex) {
            ex.printStackTrace();
            throw ex;
        } finally {
            remessaDAO = null;
            remessaItemDAO = null;
            remessaService = null;
        }
    }

    private Object processarTransacao(TransacaoVO transacaoVO, ResultadoDTO resultadoDTO, TransacaoResultadoDTO transacaoResultadoDTO) throws Exception {
        PactoPayTransacaoService pactoPayTransacaoService = null;
        try {
            pactoPayTransacaoService = new PactoPayTransacaoService(this.con, resultadoDTO.getEmpresa().getIdReferencia(), resultadoDTO.getConvenio().getIdReferencia());
            if (resultadoDTO.getOperacao().equals(OperacaoEnum.RETORNO_TRANSACAO_PAGAR.getCodigo()) ||
                    resultadoDTO.getOperacao().equals(OperacaoEnum.RETORNO_TRANSACAO_CONSULTAR.getCodigo())) {
                pactoPayTransacaoService.processarRetorno(transacaoVO, new JSONObject(resultadoDTO).toString());
            } else if (resultadoDTO.getOperacao().equals(OperacaoEnum.RETORNO_TRANSACAO_CANCELAR.getCodigo())) {
                pactoPayTransacaoService.processarRetornoCancelamento(transacaoVO, new JSONObject(resultadoDTO).toString());
            }

            if (transacaoVO.getSituacao().equals(SituacaoTransacaoEnum.CONCLUIDA_COM_SUCESSO) &&
                    UteisValidacao.emptyNumber(transacaoVO.getReciboPagamento())) {
                incluirPagamento(transacaoVO);
                return "Transacao | incluirPagamento | Recibo " + transacaoVO.getReciboPagamento();
            } else if (transacaoVO.getSituacao().equals(SituacaoTransacaoEnum.CANCELADA) &&
                    !UteisValidacao.emptyNumber(transacaoVO.getReciboPagamento())) {
                Integer codigoRecibo = transacaoVO.getReciboPagamento();
                estornarRecibo(transacaoVO, pactoPayTransacaoService);
                return "Transacao | estornarRecibo | Recibo " + codigoRecibo + " estornado";
            } else {
                throw new Exception("Nenhuma operação realizada");
            }
        } catch (Exception ex) {
            ex.printStackTrace();
            throw ex;
        } finally {
            pactoPayTransacaoService = null;
        }
    }

    private void estornarRecibo(TransacaoVO transacaoVO, PactoPayTransacaoService pactoPayTransacaoService) throws Exception {

        Transacao transacaoDAO = null;
        try {
            transacaoDAO = new Transacao(this.con);

            if (transacaoVO.getSituacao().equals(SituacaoTransacaoEnum.CANCELADA) &&
                    !UteisValidacao.emptyNumber(transacaoVO.getReciboPagamento())) {
                pactoPayTransacaoService.estornarRecibo(transacaoVO, false);
                if (transacaoVO.getSituacao().equals(SituacaoTransacaoEnum.ESTORNADA)) {
                    transacaoVO.setSituacao(SituacaoTransacaoEnum.CANCELADA);
                }
                transacaoDAO.alterar(transacaoVO);
            }
        } catch (Exception ex) {
            ex.printStackTrace();
            throw ex;
        } finally {
            transacaoDAO = null;
        }
    }

    private void incluirPagamento(TransacaoVO transacaoVO) throws Exception {
        Transacao transacaoDAO = null;
        ConvenioCobranca convenioCobrancaDAO = null;
        MovPagamento movPagamentoDAO = null;
        FormaPagamento formaPagamentoDAO = null;
        Adquirente adquirenteDAO = null;
        try {
            transacaoDAO = new Transacao(this.con);
            convenioCobrancaDAO = new ConvenioCobranca(this.con);
            movPagamentoDAO = new MovPagamento(this.con);
            formaPagamentoDAO = new FormaPagamento(this.con);
            adquirenteDAO = new Adquirente(this.con);

            MovPagamentoVO movPagamentoVO = new MovPagamentoVO();
            movPagamentoVO.setMovPagamentoEscolhida(true);
            movPagamentoVO.setOpcaoPagamentoCartaoCredito(true);

            boolean temFormaPagamento = false;
            List<FormaPagamentoVO> formasPgtConvenio = formaPagamentoDAO.consultarFormaPagamentoComConvenioCobranca(true, false, Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA);
            for (FormaPagamentoVO form : formasPgtConvenio) {
                if (form.getConvenioCobrancaVO() != null && form.getConvenioCobrancaVO().getCodigo().equals(transacaoVO.getConvenioCobrancaVO().getCodigo())) {
                    movPagamentoVO.setFormaPagamento(form);
                    temFormaPagamento = true;
                    break;
                }
            }
            if (!temFormaPagamento) {
                movPagamentoVO.setFormaPagamento(formaPagamentoDAO.obterFormaPagamentoCartaoRecorrente());
            }

            try {
                movPagamentoVO.setAdquirenteVO(adquirenteDAO.obterAdquirenteTransacao(transacaoVO));
            } catch (Exception ex) {
                ex.printStackTrace();
            }

            movPagamentoVO.setValor(transacaoVO.getValor());
            movPagamentoVO.setValorTotal(transacaoVO.getValor());
            movPagamentoVO.setPessoa(transacaoVO.getPessoaPagador());
            movPagamentoVO.setNomePagador(transacaoVO.getPessoaPagador().getNome());
            if (!UteisValidacao.emptyNumber(transacaoVO.getNumeroParcelas())) {
                movPagamentoVO.setNrParcelaCartaoCredito(transacaoVO.getNumeroParcelas());
            } else {
                movPagamentoVO.setNrParcelaCartaoCredito(1);
            }
            movPagamentoVO.setOperadoraCartaoVO(obterOperadoraCartao(transacaoVO));
            movPagamentoVO.setResponsavelPagamento(transacaoVO.getUsuarioResponsavel());
            movPagamentoVO.setNsu(transacaoVO.getNSU());
            movPagamentoVO.setAutorizacaoCartao(transacaoVO.getAutorizacao());
            movPagamentoVO.setEmpresa(transacaoVO.getEmpresaVO());
            movPagamentoVO.setConvenio(transacaoVO.getConvenioCobrancaVO());

            if (!UteisValidacao.emptyNumber(transacaoVO.getConvenioCobrancaVO().getCodigo())) {
                transacaoVO.setConvenioCobrancaVO(convenioCobrancaDAO.consultarPorChavePrimaria(transacaoVO.getConvenioCobrancaVO().getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS));

                OperadorasExternasAprovaFacilEnum operadorasExternasAprovaFacilEnum = OperadorasExternasAprovaFacilEnum.obterPorDescricao(transacaoVO.getBandeira());
                if (operadorasExternasAprovaFacilEnum != null) {
                    movPagamentoVO.setOperadoraCartaoVO(obterOperadoraCartao(transacaoVO));
                }
            }

            transacaoVO.setListaParcelas(transacaoDAO.obterParcelasDaTransacao(transacaoVO));

            prepararMovPagamentoAcumuloAutomatico(movPagamentoVO, transacaoVO.getListaParcelas());

            List<MovPagamentoVO> listaPagamento = new ArrayList<MovPagamentoVO>();
            listaPagamento.add(movPagamentoVO);
            ReciboPagamentoVO reciboObj = movPagamentoDAO.incluirListaPagamento(
                    listaPagamento,
                    transacaoVO.getListaParcelas(),
                    null,
                    obterContratoParcelas(transacaoVO),
                    false, 0.0);

            transacaoVO.setReciboPagamento(reciboObj.getCodigo());
            transacaoVO.setMovPagamento(reciboObj.getPagamentosDesteRecibo().get(0).getCodigo());

            if (UteisValidacao.emptyString(transacaoVO.getCodigoExterno())) {
                transacaoVO.setCodigoExterno(transacaoVO.getValorAtributoResposta(APF.Transacao));
            }

            transacaoDAO.alterar(transacaoVO);
        } catch (Exception ex) {
            ex.printStackTrace();
            throw ex;
        } finally {
            transacaoDAO = null;
            convenioCobrancaDAO = null;
            movPagamentoDAO = null;
            formaPagamentoDAO = null;
            adquirenteDAO = null;
        }
    }

    private OperadoraCartaoVO obterOperadoraCartao(TransacaoVO transacaoVO) {
        OperadoraCartao operadoraDAO;
        try {
            operadoraDAO = new OperadoraCartao(this.con);

            OperadorasExternasAprovaFacilEnum operadoraEnum = OperadorasExternasAprovaFacilEnum.obterPorDescricao(transacaoVO.getBandeira());
            if (operadoraEnum == null) {
                throw new Exception("Bandeira não identificada.");
            }
            return operadoraDAO.consultarOuCriaPorCodigoIntegracao(transacaoVO.getConvenioCobrancaVO().getTipo(), transacaoVO.getNumeroParcelas(), operadoraEnum);
        } catch (Exception ex) {
            ex.printStackTrace();
        } finally {
            operadoraDAO = null;
        }
        return new OperadoraCartaoVO();
    }

    private void prepararMovPagamentoAcumuloAutomatico(MovPagamentoVO movPagamentoVO, List<MovParcelaVO> listaMovParcelas) {
        ParceiroFidelidadeZW parceiroFidelidadeZW = null;
        try {
            parceiroFidelidadeZW = new ParceiroFidelidadeZW(this.con);
            for (MovParcelaVO movParcelaVO : listaMovParcelas) {
                parceiroFidelidadeZW.prepararMovPagamentoAcumuloAutomatico(movPagamentoVO, movParcelaVO);
            }
        } catch (Exception ex) {
            Uteis.logar(null, "prepararMovPagamentoAcumuloAutomatico ERRO: " + ex.getMessage());
        } finally {
            parceiroFidelidadeZW = null;
        }
    }

    private ContratoVO obterContratoParcelas(TransacaoVO transacaoVO) {
        //verificar se todas as parcelas são do mesmo contrato;
        Integer contrato = transacaoVO.getListaParcelas().get(0).getContrato().getCodigo();
        for (MovParcelaVO movParcelaVO : transacaoVO.getListaParcelas()) {
            if (!contrato.equals(movParcelaVO.getContrato().getCodigo())) {
                return null;
            }
        }
        ContratoVO contratoVO = new ContratoVO();
        contratoVO.setCodigo(contrato);
        return contratoVO;
    }
}

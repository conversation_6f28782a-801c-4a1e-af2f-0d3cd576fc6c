package negocio.facade.jdbc.financeiro;

import negocio.comuns.financeiro.BancoVO;
import negocio.comuns.financeiro.ContaCorrenteVO;
import negocio.comuns.utilitarias.ConsistirException;
import negocio.comuns.utilitarias.Ordenacao;
import negocio.comuns.utilitarias.Uteis;
import negocio.facade.jdbc.arquitetura.SuperEntidade;
import negocio.interfaces.financeiro.ContaCorrenteInterfaceFacade;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.sql.Statement;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

/**
 * Classe de persistência que encapsula todas as operações de manipulação dos dados da classe <code>ContaCorrenteVO</code>.
 * Responsável por implementar operações como incluir, alterar, excluir e consultar pertinentes a classe <code>ContaCorrenteVO</code>.
 * Encapsula toda a interação com o banco de dados.
 * @see ContaCorrenteVO
 * @see SuperEntidade
 */
public class ContaCorrente extends SuperEntidade implements ContaCorrenteInterfaceFacade {

    public ContaCorrente() throws Exception {
        super();
    }

    public ContaCorrente(Connection con) throws Exception {
        super(con);
    }

    /**
     * Operação responsável por retornar um novo objeto da classe <code>ContaCorrenteVO</code>.
     */
    public ContaCorrenteVO novo() throws Exception {
        incluir(getIdEntidade());
        ContaCorrenteVO obj = new ContaCorrenteVO();
        return obj;
    }

    /**
     * Operação responsável por incluir no banco de dados um objeto da classe <code>ContaCorrenteVO</code>.
     * Primeiramente valida os dados (<code>validarDados</code>) do objeto. Verifica a conexão com o banco de dados e a permissão do usuário
     * para realizar esta operacão na entidade.
     * Isto, através da operação <code>incluir</code> da superclasse.
     * @param obj  Objeto da classe <code>ContaCorrenteVO</code> que será gravado no banco de dados.
     * @exception Exception Caso haja problemas de conexão, restrição de acesso ou validação de dados.
     */
    public void incluir(ContaCorrenteVO obj) throws Exception {
        try {
            con.setAutoCommit(false);
            incluirSemCommit(obj);
            con.commit();
        } catch (Exception e) {
            obj.setNovoObj(new Boolean(true));
            con.rollback();
            con.setAutoCommit(true);
            throw e;
        } finally {
            con.setAutoCommit(true);
        }
    }

    public void incluirSemCommit(ContaCorrenteVO obj) throws Exception {
            ContaCorrenteVO.validarDados(obj);
            ContaCorrenteVO.removerEspacosEmBranco(obj);
            incluir(getIdEntidade());
            String sql = "INSERT INTO ContaCorrente( agencia, agenciaDV, contaCorrente, contaCorrenteDV, "
                    + "banco, codigooperacao ) VALUES ( ?, ?, ?, ?, ?, ?)";
            PreparedStatement sqlInserir = con.prepareStatement(sql);
            sqlInserir.setString(1, obj.getAgencia().trim());
            sqlInserir.setString(2, obj.getAgenciaDV().trim());
            sqlInserir.setString(3, obj.getContaCorrente().trim());
            sqlInserir.setString(4, obj.getContaCorrenteDV().trim());
            if (obj.getBanco().getCodigo() != 0) {
                sqlInserir.setInt(5, obj.getBanco().getCodigo());
            } else {
                sqlInserir.setNull(5, 0);
            }
            sqlInserir.setString(6, obj.getCodigoOperacao());
            sqlInserir.execute();
            obj.setCodigo(obterValorChavePrimariaCodigo());
            obj.setNovoObj(Boolean.FALSE);
        }

    /**
     * Operação responsável por alterar no BD os dados de um objeto da classe <code>ContaCorrenteVO</code>.
     * Sempre utiliza a chave primária da classe como atributo para localização do registro a ser alterado.
     * Primeiramente valida os dados (<code>validarDados</code>) do objeto. Verifica a conexão com o banco de dados e a permissão do usuário
     * para realizar esta operacão na entidade.
     * Isto, através da operação <code>alterar</code> da superclasse.
     * @param obj    Objeto da classe <code>ContaCorrenteVO</code> que será alterada no banco de dados.
     * @exception Exception Caso haja problemas de conexão, restrição de acesso ou validação de dados.
     */
    public void alterar(ContaCorrenteVO obj) throws Exception {
        try {
            con.setAutoCommit(false);
            alterarSemCommit(obj);
            con.commit();
        } catch (Exception e) {
            con.rollback();
            con.setAutoCommit(true);
            throw e;
        } finally {
            con.setAutoCommit(true);
        }
    }

    public void alterarSemCommit(ContaCorrenteVO obj) throws Exception {
            ContaCorrenteVO.validarDados(obj);
            ContaCorrenteVO.removerEspacosEmBranco(obj);
            alterar(getIdEntidade());
            String sql = "UPDATE ContaCorrente set agencia=?, agenciaDV=?, contaCorrente=?, "
                    + "contaCorrenteDV=?, banco=?, codigoOperacao = ? WHERE ((codigo = ?))";
            PreparedStatement sqlAlterar = con.prepareStatement(sql);
            sqlAlterar.setString(1, obj.getAgencia());
            sqlAlterar.setString(2, obj.getAgenciaDV());
            sqlAlterar.setString(3, obj.getContaCorrente());
            sqlAlterar.setString(4, obj.getContaCorrenteDV());
            if (obj.getBanco().getCodigo() != 0) {
                sqlAlterar.setInt(5, obj.getBanco().getCodigo());
            } else {
                sqlAlterar.setNull(5, 0);
            }
            sqlAlterar.setString(6, obj.getCodigoOperacao());
            sqlAlterar.setInt(7, obj.getCodigo().intValue());
            sqlAlterar.execute();
        }

    public List<ContaCorrenteVO> consultarTodos(int nivelMontarDados)throws Exception{
        Statement st = con.createStatement();
        ResultSet rs = st.executeQuery("select * from ContaCorrente ");
        return montarDadosConsulta(rs, nivelMontarDados, con);
    }

    /**
     * Operação responsável por excluir no BD um objeto da classe <code>ContaCorrenteVO</code>.
     * Sempre localiza o registro a ser excluído através da chave primária da entidade.
     * Primeiramente verifica a conexão com o banco de dados e a permissão do usuário
     * para realizar esta operacão na entidade.
     * Isto, através da operação <code>excluir</code> da superclasse.
     * @param obj    Objeto da classe <code>ContaCorrenteVO</code> que será removido no banco de dados.
     * @exception Exception Caso haja problemas de conexão ou restrição de acesso.
     */
    public void excluir(ContaCorrenteVO obj) throws Exception {
        try {
            con.setAutoCommit(false);
            excluir(getIdEntidade());
            String sql = "DELETE FROM ContaCorrente WHERE ((codigo = ?))";
            PreparedStatement sqlExcluir = con.prepareStatement(sql);
            sqlExcluir.setInt(1, obj.getCodigo().intValue());
            sqlExcluir.execute();
            con.commit();
        } catch (Exception e) {
            con.rollback();
            con.setAutoCommit(true);
            throw e;
        } finally {
            con.setAutoCommit(true);
        }
    }

    /**
     * Responsável por realizar uma consulta de <code>ContaCorrente</code> através do valor do atributo 
     * <code>nome</code> da classe <code>Banco</code>
     * Faz uso da operação <code>montarDadosConsulta</code> que realiza o trabalho de prerarar o List resultante.
     * @return  List Contendo vários objetos da classe <code>ContaCorrenteVO</code> resultantes da consulta.
     * @exception Exception Caso haja problemas de conexão ou restrição de acesso.
     */
    public List consultarPorNomeBanco(String valorConsulta, int nivelMontarDados) throws Exception {
        consultar(getIdEntidade(), true);
        String sqlStr = "SELECT ContaCorrente.* FROM ContaCorrente, Banco WHERE ContaCorrente.banco = Banco.codigo and upper( Banco.nome ) like('" + valorConsulta.toUpperCase() + "%') ORDER BY Banco.nome";
        Statement stm = con.createStatement();
        ResultSet tabelaResultado = stm.executeQuery(sqlStr);
        return montarDadosConsulta(tabelaResultado, nivelMontarDados, this.con);
    }

    /**
     * Responsável por realizar uma consulta de <code>ContaCorrente</code> através do valor do atributo 
     * <code>String contaCorrenteDV</code>. Retorna os objetos, com início do valor do atributo idêntico ao parâmetro fornecido.
     * Faz uso da operação <code>montarDadosConsulta</code> que realiza o trabalho de prerarar o List resultante.
     * @param   controlarAcesso Indica se a aplicação deverá verificar se o usuário possui permissão para esta consulta ou não.
     * @return  List Contendo vários objetos da classe <code>ContaCorrenteVO</code> resultantes da consulta.
     * @exception Exception Caso haja problemas de conexão ou restrição de acesso.
     */
    public List consultarPorContaCorrenteDV(String valorConsulta, boolean controlarAcesso, int nivelMontarDados) throws Exception {
        consultar(getIdEntidade(), controlarAcesso);
        String sqlStr = "SELECT * FROM ContaCorrente WHERE upper( contaCorrenteDV ) like('" + valorConsulta.toUpperCase() + "%') ORDER BY contaCorrenteDV";
        Statement stm = con.createStatement();
        ResultSet tabelaResultado = stm.executeQuery(sqlStr);
        return (montarDadosConsulta(tabelaResultado, nivelMontarDados, this.con));
    }

    /**
     * Responsável por realizar uma consulta de <code>ContaCorrente</code> através do valor do atributo 
     * <code>String contaCorrente</code>. Retorna os objetos, com início do valor do atributo idêntico ao parâmetro fornecido.
     * Faz uso da operação <code>montarDadosConsulta</code> que realiza o trabalho de prerarar o List resultante.
     * @param   controlarAcesso Indica se a aplicação deverá verificar se o usuário possui permissão para esta consulta ou não.
     * @return  List Contendo vários objetos da classe <code>ContaCorrenteVO</code> resultantes da consulta.
     * @exception Exception Caso haja problemas de conexão ou restrição de acesso.
     */
    public List consultarPorContaCorrente(String valorConsulta, boolean controlarAcesso, int nivelMontarDados) throws Exception {
        consultar(getIdEntidade(), controlarAcesso);
        String sqlStr = "SELECT * FROM ContaCorrente WHERE upper( contaCorrente ) like('" + valorConsulta.toUpperCase() + "%') ORDER BY contaCorrente";
        Statement stm = con.createStatement();
        ResultSet tabelaResultado = stm.executeQuery(sqlStr);
        return (montarDadosConsulta(tabelaResultado, nivelMontarDados, this.con));
    }

    /**
     * Responsável por realizar uma consulta de <code>ContaCorrente</code> através do valor do atributo 
     * <code>String agenciaDV</code>. Retorna os objetos, com início do valor do atributo idêntico ao parâmetro fornecido.
     * Faz uso da operação <code>montarDadosConsulta</code> que realiza o trabalho de prerarar o List resultante.
     * @param   controlarAcesso Indica se a aplicação deverá verificar se o usuário possui permissão para esta consulta ou não.
     * @return  List Contendo vários objetos da classe <code>ContaCorrenteVO</code> resultantes da consulta.
     * @exception Exception Caso haja problemas de conexão ou restrição de acesso.
     */
    public List consultarPorAgenciaDV(String valorConsulta, boolean controlarAcesso, int nivelMontarDados) throws Exception {
        consultar(getIdEntidade(), controlarAcesso);
        String sqlStr = "SELECT * FROM ContaCorrente WHERE upper( agenciaDV ) like('" + valorConsulta.toUpperCase() + "%') ORDER BY agenciaDV";
        Statement stm = con.createStatement();
        ResultSet tabelaResultado = stm.executeQuery(sqlStr);
        return (montarDadosConsulta(tabelaResultado, nivelMontarDados, this.con));
    }

    /**
     * Responsável por realizar uma consulta de <code>ContaCorrente</code> através do valor do atributo 
     * <code>String agencia</code>. Retorna os objetos, com início do valor do atributo idêntico ao parâmetro fornecido.
     * Faz uso da operação <code>montarDadosConsulta</code> que realiza o trabalho de prerarar o List resultante.
     * @param   controlarAcesso Indica se a aplicação deverá verificar se o usuário possui permissão para esta consulta ou não.
     * @return  List Contendo vários objetos da classe <code>ContaCorrenteVO</code> resultantes da consulta.
     * @exception Exception Caso haja problemas de conexão ou restrição de acesso.
     */
    public List consultarPorAgencia(String valorConsulta, boolean controlarAcesso, int nivelMontarDados) throws Exception {
        consultar(getIdEntidade(), controlarAcesso);
        String sqlStr = "SELECT * FROM ContaCorrente WHERE upper( agencia ) like('" + valorConsulta.toUpperCase() + "%') ORDER BY agencia";
        Statement stm = con.createStatement();
        ResultSet tabelaResultado = stm.executeQuery(sqlStr);
        return (montarDadosConsulta(tabelaResultado, nivelMontarDados, this.con));
    }

    public ContaCorrenteVO criarOuConsultarSeExistePorContaCorrente(ContaCorrenteVO obj) throws Exception {
        String sql = "SELECT * FROM ContaCorrente WHERE contacorrente = ?";
        PreparedStatement sqlConsultar = con.prepareStatement(sql);
        sqlConsultar.setString(1, obj.getContaCorrente());
        ResultSet tabelaResultado = sqlConsultar.executeQuery();
        if (!tabelaResultado.next()) {
            incluir(obj);
            return obj;
        } else {
            return (ContaCorrente.montarDados(tabelaResultado, Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA, con));
        }
    }

    /**
     * Responsável por realizar uma consulta de <code>ContaCorrente</code> através do valor do atributo 
     * <code>Integer codigo</code>. Retorna os objetos com valores iguais ou superiores ao parâmetro fornecido.
     * Faz uso da operação <code>montarDadosConsulta</code> que realiza o trabalho de prerarar o List resultante.
     * @param   controlarAcesso Indica se a aplicação deverá verificar se o usuário possui permissão para esta consulta ou não.
     * @return  List Contendo vários objetos da classe <code>ContaCorrenteVO</code> resultantes da consulta.
     * @exception Exception Caso haja problemas de conexão ou restrição de acesso.
     */
    public List consultarPorCodigo(Integer valorConsulta, boolean controlarAcesso, int nivelMontarDados) throws Exception {
        consultar(getIdEntidade(), controlarAcesso);
        String sqlStr = "SELECT * FROM ContaCorrente WHERE codigo >= " + valorConsulta.intValue() + " ORDER BY codigo";
        Statement stm = con.createStatement();
        ResultSet tabelaResultado = stm.executeQuery(sqlStr);
        return (montarDadosConsulta(tabelaResultado, nivelMontarDados, this.con));
    }

    /**
     * Responsável por montar os dados de vários objetos, resultantes de uma consulta ao banco de dados (<code>ResultSet</code>).
     * Faz uso da operação <code>montarDados</code> que realiza o trabalho para um objeto por vez.
     * @return  List Contendo vários objetos da classe <code>ContaCorrenteVO</code> resultantes da consulta.
     */
    public static List montarDadosConsulta(ResultSet tabelaResultado, int nivelMontarDados, Connection con) throws Exception {
        List vetResultado = new ArrayList();
        while (tabelaResultado.next()) {
            ContaCorrenteVO obj = new ContaCorrenteVO();
            obj = montarDados(tabelaResultado, nivelMontarDados, con);
            vetResultado.add(obj);
        }
        return vetResultado;
    }

    /**
     * Responsável por montar os dados resultantes de uma consulta ao banco de dados (<code>ResultSet</code>)
     * em um objeto da classe <code>ContaCorrenteVO</code>.
     * @return  O objeto da classe <code>ContaCorrenteVO</code> com os dados devidamente montados.
     */
    public static ContaCorrenteVO montarDados(ResultSet dadosSQL, int nivelMontarDados, Connection con) throws Exception {
        ContaCorrenteVO obj = new ContaCorrenteVO();
        obj.setCodigo(new Integer(dadosSQL.getInt("codigo")));
        obj.setAgencia(dadosSQL.getString("agencia"));
        obj.setAgenciaDV(dadosSQL.getString("agenciaDV"));
        obj.setContaCorrente(dadosSQL.getString("contaCorrente"));
        obj.setContaCorrenteDV(dadosSQL.getString("contaCorrenteDV"));
        obj.getBanco().setCodigo(new Integer(dadosSQL.getInt("banco")));
        try {
            obj.setCodigoOperacao(dadosSQL.getString("codigoOperacao"));
        } catch (Exception e) {
        }
        obj.setNovoObj(new Boolean(false));
        if (nivelMontarDados == Uteis.NIVELMONTARDADOS_DADOSBASICOS) {
            return obj;
        }

        montarDadosBanco(obj, nivelMontarDados, con);
        return obj;
    }

    /**
     * Operação responsável por montar os dados de um objeto da classe <code>BancoVO</code> relacionado ao objeto <code>ContaCorrenteVO</code>.
     * Faz uso da chave primária da classe <code>BancoVO</code> para realizar a consulta.
     * @param obj  Objeto no qual será montado os dados consultados.
     */
    public static void montarDadosBanco(ContaCorrenteVO obj, int nivelMontarDados, Connection con) throws Exception {
        if (obj.getBanco().getCodigo().intValue() == 0) {
            obj.setBanco(new BancoVO());
            return;
        }
        Banco banco = new Banco(con);
        obj.setBanco(banco.consultarPorChavePrimaria(obj.getBanco().getCodigo(), nivelMontarDados));
        banco = null;
    }

    /**
     * Operação responsável por localizar um objeto da classe <code>ContaCorrenteVO</code>
     * através de sua chave primária. 
     * @exception Exception Caso haja problemas de conexão ou localização do objeto procurado.
     */
    public ContaCorrenteVO consultarPorChavePrimaria(Integer codigoPrm, int nivelMontarDados) throws Exception {
        consultar(getIdEntidade(), false);
        String sql = "SELECT * FROM ContaCorrente WHERE codigo = ?";
        PreparedStatement sqlConsultar = con.prepareStatement(sql);
        sqlConsultar.setInt(1, codigoPrm.intValue());
        ResultSet tabelaResultado = sqlConsultar.executeQuery();
        if (!tabelaResultado.next()) {
            throw new ConsistirException("Dados Não Encontrados ( ContaCorrente ).");
        }
        return (montarDados(tabelaResultado, nivelMontarDados, this.con));
    }

    public String consultarJSON() throws Exception {

        ResultSet rs = getRS();

        StringBuilder json = new StringBuilder();
        json.append("{\"aaData\":[");
        boolean dados = false;
        while (rs.next()) {
            dados = true;
            json.append("[\"").append(rs.getString("codigo")).append("\",");
            json.append("\"").append(rs.getString("agencia")).append("\",");
            json.append("\"").append(rs.getString("agenciadv")).append("\",");
            json.append("\"").append(rs.getString("contacorrente")).append("\",");
            json.append("\"").append(rs.getString("contacorrentedv")).append("\",");
            json.append("\"").append(rs.getString("banco")).append("\"],");
        }
        if (dados) {
            json.deleteCharAt(json.toString().length() - 1);
        }
        json.append("]}");
        return json.toString();
    }

    private ResultSet getRS() throws SQLException {
        String sql = "SELECT\n" + "  cc.codigo, banco.nome AS banco, contacorrente, contacorrentedv,agencia, agenciadv\n" + "FROM contacorrente cc\n" + "  LEFT JOIN banco ON cc.banco = banco.codigo;";
        PreparedStatement sqlConsultar = con.prepareStatement(sql);
        ResultSet rs = sqlConsultar.executeQuery();
        return rs;
    }

    public List consultarParaImpressao(String filtro, String ordem, String campoOrdenacao, int i) throws SQLException {

        ResultSet rs = getRS();
        List lista = new ArrayList();

        while (rs.next()) {

            ContaCorrenteVO cCorrente = new ContaCorrenteVO();
            String geral = rs.getString("codigo") + rs.getString("banco") + rs.getString("contacorrente") + rs.getString("contacorrentedv") + rs.getString("agencia") + rs.getString("agenciadv");
            if (geral.toLowerCase().contains(filtro.toLowerCase())) {
                cCorrente.setCodigo(rs.getInt("codigo"));
                cCorrente.getBanco().setNome(rs.getString("banco"));
                cCorrente.setContaCorrente(rs.getString("contacorrente"));
                cCorrente.setContaCorrenteDV(rs.getString("contacorrentedv"));
                cCorrente.setAgencia(rs.getString("agencia"));
                cCorrente.setAgenciaDV(rs.getString("agenciadv"));
                lista.add(cCorrente);
            }
        }
        if (campoOrdenacao.equals("Código")) {
            Ordenacao.ordenarLista(lista, "codigo");
        } else if (campoOrdenacao.equals("Agência")) {
            Ordenacao.ordenarLista(lista, "agencia");
        } else if (campoOrdenacao.equals("Dígito Verificador da Agência")) {
            Ordenacao.ordenarLista(lista, "agenciaDV");
        } else if (campoOrdenacao.equals("Conta Corrente")) {
            Ordenacao.ordenarLista(lista, "contaCorrente");
        } else if (campoOrdenacao.equals("Dígito Verificador da Conta Corrente")) {
            Ordenacao.ordenarLista(lista, "contaCorrenteDV");
        } else if (campoOrdenacao.equals("Banco")) {
            Ordenacao.ordenarLista(lista, "banco_Apresentar");
        }
        if (ordem.contains("desc")) {
            Collections.reverse(lista);
        }
        return lista;
    }
}

package negocio.facade.jdbc.financeiro;

import br.com.pactosolucoes.comuns.to.CartaoCreditoTO;
import br.com.pactosolucoes.comuns.to.TotalizadorRetentativaTO;
import negocio.comuns.financeiro.CartaoTentativaVO;
import negocio.comuns.financeiro.enumerador.CodigoRetornoPactoEnum;
import negocio.comuns.financeiro.enumerador.OperacaoRetornoCobrancaEnum;
import negocio.comuns.financeiro.enumerador.SituacaoTransacaoEnum;
import negocio.comuns.financeiro.enumerador.TipoConvenioCobrancaEnum;
import negocio.comuns.utilitarias.Calendario;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisValidacao;
import negocio.facade.jdbc.arquitetura.SuperEntidade;
import servicos.impl.apf.APF;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;

/**
 * Created with IntelliJ IDEA.
 * User: <PERSON><PERSON>
 * Date: 01/05/2020
 */
public class CartaoTentativa extends SuperEntidade {

    public CartaoTentativa() throws Exception {
        super();
    }

    public CartaoTentativa(Connection con) throws Exception {
        super(con);
    }

    public void incluir(CartaoTentativaVO obj) throws Exception {
        String sql = "INSERT INTO cartaoTentativa(data, cartao, codigoRetorno, convenioCobranca, tipoConvenioCobranca, codigoRetornoPacto, " +
                "transacaoPresencial, usuario, transacao, remessaitem, operacaoretornocobranca) VALUES (?,?,?,?,?,?,?,?,?,?,?)";
        try (PreparedStatement pst = con.prepareStatement(sql)) {
            int i = 0;
            if (obj.getData() == null) {
                obj.setData(Calendario.hoje());
            }
            pst.setTimestamp(++i, Uteis.getDataJDBCTimestamp(obj.getData()));
            pst.setString(++i, obj.getCartao());
            pst.setString(++i, obj.getCodigoRetorno());
            resolveIntegerNull(pst, ++i, obj.getConvenioCobrancaVO().getCodigo());
            pst.setInt(++i, obj.getTipoConvenioCobranca().getCodigo());
            pst.setString(++i, CartaoTentativaVO.obterCodigoRetornoPacto(obj.getTransacaoVO(), obj.getTipoConvenioCobranca(), obj.getCodigoRetorno()).getCodigo());
            pst.setBoolean(++i, obj.isTransacaoPresencial());
            resolveIntegerNull(pst, ++i, obj.getUsuario());
            resolveIntegerNull(pst, ++i, obj.getTransacao());
            resolveIntegerNull(pst, ++i, obj.getRemessaItem());
            pst.setInt(++i, obj.getOperacaoRetornoCobranca().getCodigo());
            pst.execute();
        }
        obj.setCodigo(obterValorChavePrimariaCodigo());
        obj.setNovoObj(false);
    }

    public CartaoTentativaVO montarDados(ResultSet dadosSQL) throws Exception {
        CartaoTentativaVO obj = new CartaoTentativaVO();
        obj.setNovoObj(false);
        obj.setCodigo(dadosSQL.getInt("codigo"));
        obj.setData(dadosSQL.getTimestamp("data"));
        obj.setCartao(dadosSQL.getString("cartao"));
        obj.setCodigoRetorno(dadosSQL.getString("codigoRetorno"));
        obj.getConvenioCobrancaVO().setCodigo(dadosSQL.getInt("convenioCobranca"));
        obj.setTipoConvenioCobranca(TipoConvenioCobrancaEnum.valueOf(dadosSQL.getInt("tipoConvenioCobranca")));
        obj.setCodigoRetornoPacto(CodigoRetornoPactoEnum.consultarPorCodigo(dadosSQL.getString("codigoRetornoPacto")));
        obj.setTransacaoPresencial(dadosSQL.getBoolean("transacaoPresencial"));
        obj.setUsuario(dadosSQL.getInt("usuario"));
        obj.setTransacao(dadosSQL.getInt("transacao"));
        obj.setRemessaItem(dadosSQL.getInt("remessaitem"));
        obj.setOperacaoRetornoCobranca(OperacaoRetornoCobrancaEnum.obterPorCodigo(dadosSQL.getInt("operacaoRetornoCobranca")));
        return obj;
    }

    public CartaoTentativaVO obterUltimaTentativa(String cartao, boolean semRetornoPacto, Integer idConvenio) throws Exception {
        //consulta por data pois tem um processo que povoa.. e pode alterar a ordem se for buscado por código...
        StringBuilder sql = new StringBuilder();
        sql.append("select * from cartaoTentativa \n");
        sql.append("where cartao = '").append(APF.getCartaoMascarado(cartao)).append("' \n");
        if (semRetornoPacto) {
            sql.append("and codigoRetorno not ilike 'PAC%' \n");
        }
        if (!UteisValidacao.emptyNumber(idConvenio)) {
            sql.append("and conveniocobranca = ").append(idConvenio).append(" \n");
        }
        sql.append("order by data desc limit 1;");
        try (PreparedStatement stm = con.prepareStatement(sql.toString())) {
            try (ResultSet rs = stm.executeQuery()) {
                if (rs.next()) {
                    return montarDados(rs);
                } else {
                    return null;
                }
            }
        }
    }

    public CartaoTentativaVO obterUltimaTentativaCartao(String cartao, boolean semRetornoPacto, Integer idConvenio) throws Exception {
        //consulta por data pois tem um processo que povoa.. e pode alterar a ordem se for buscado por código...

        String cartaoMascarado1Forma = cartao.replaceAll("(\\d{6})\\d{6}(\\d{4})", "$1******$2"); //123456******3456
        String cartaoMascarado2Forma = cartao.replaceAll("\\d(?=\\d{4})", "*"); //************3456
        String cartaoMascarado3Forma = cartao.replaceAll("(?<=\\d)\\d(?=\\d{4})", "*"); //1**********3456

        StringBuilder sql = new StringBuilder();
        sql.append("select * from cartaoTentativa \n");
        sql.append("WHERE ( \n");
        sql.append("cartao LIKE '").append(cartaoMascarado1Forma).append("' \n");
        sql.append("OR cartao LIKE '").append(cartaoMascarado2Forma).append("' \n");
        sql.append("OR cartao LIKE '").append(cartaoMascarado3Forma).append("' \n");
        sql.append(") \n");
        if (semRetornoPacto) {
            sql.append("and codigoRetorno not ilike 'PAC%' \n");
        }
        if (!UteisValidacao.emptyNumber(idConvenio)) {
            sql.append("and conveniocobranca = ").append(idConvenio).append(" \n");
        }
        sql.append("order by data desc limit 1;");
        try (PreparedStatement stm = con.prepareStatement(sql.toString())) {
            try (ResultSet rs = stm.executeQuery()) {
                if (rs.next()) {
                    return montarDados(rs);
                } else {
                    return null;
                }
            }
        }
    }

    public boolean existeTentativaAprovada(String cartao) throws Exception {
        StringBuilder sql = new StringBuilder();
        sql.append("SELECT COUNT(*) AS cobrancasAprovadas \n");
        sql.append("FROM ( \n");
        sql.append(" SELECT t.situacao, ct.* FROM cartaoTentativa AS ct \n");
        sql.append(" INNER JOIN transacao t ON t.codigo = ct.transacao \n");
        sql.append(" WHERE ct.cartao = '").append(APF.getCartaoMascarado(cartao)).append("' \n");
        sql.append(" AND ct.codigoRetorno NOT ILIKE 'PAC%' \n");
        sql.append(" ORDER BY data DESC LIMIT 5 \n");
        sql.append(") AS consultaOriginal \n");
        sql.append("WHERE consultaOriginal.situacao in (").append(SituacaoTransacaoEnum.APROVADA.getId()).append(", ").append(SituacaoTransacaoEnum.CONCLUIDA_COM_SUCESSO.getId()).append(");");

        try (PreparedStatement stm = con.prepareStatement(sql.toString())) {
            try (ResultSet rs = stm.executeQuery()) {
                if (rs.next()) {
                    int quantidadeCobrancasAprovadas = rs.getInt("cobrancasAprovadas");
                    if (quantidadeCobrancasAprovadas > 0) {
                        return true;
                    } else {
                        return false;
                    }
                } else {
                    return false;
                }
            }
        }
    }

    public boolean existeVerificacaoPosteriorUltimaTentaviva(String cartao) throws Exception {
        StringBuilder sql = new StringBuilder();
        sql.append("SELECT EXISTS ( \n");
        sql.append(" SELECT t.* FROM transacao t \n");
        sql.append(" WHERE t.transacaoverificarcartao \n");
        sql.append(" AND t.situacao = ").append(SituacaoTransacaoEnum.CANCELADA.getId()).append(" \n");
        sql.append(" AND t.outrasinformacoes ILIKE '%").append(APF.getCartaoMascarado(cartao)).append("%' \n");
        sql.append(" AND t.dataprocessamento >= ( \n");
        sql.append("  SELECT c.data FROM cartaotentativa c \n");
        sql.append("  WHERE c.cartao = '").append(APF.getCartaoMascarado(cartao)).append("' \n");
        sql.append("  AND c.codigoRetorno NOT ILIKE 'PAC%' \n");
        sql.append("  ORDER BY c.data DESC LIMIT 1 \n");
        sql.append(" ) ORDER BY t.codigo DESC LIMIT 1 \n");
        sql.append(");");

        try (PreparedStatement stm = con.prepareStatement(sql.toString())) {
            try (ResultSet rs = stm.executeQuery()) {
                if (rs.next()) {
                    if (rs.getBoolean("exists")) {
                        return true;
                    } else {
                        return false;
                    }
                } else {
                    return false;
                }
            }
        }
    }

    public int obterQtdTentativasUltDias(String cartaoMascarado, int qtdDias) throws Exception {
        //consulta por data pois tem um processo que povoa.. e pode alterar a ordem se for buscado por código...
        StringBuilder sql = new StringBuilder();
        sql.append("select count(*) as total from cartaoTentativa \n");
        sql.append("where cartao = '").append(cartaoMascarado).append("' \n");
        sql.append("and codigoRetorno not ilike 'PAC%' \n"); //considerar somente o que foi na adquirente mesmo
        sql.append("and data::date >= '").append(Uteis.getData(Uteis.voltarDias(Calendario.hoje(), qtdDias), "yyyy-MM-dd'T'HH:mm:ss")).append("' \n"); //considerar somente o que foi na adquirente mesmo
        try (PreparedStatement stm = con.prepareStatement(sql.toString())) {
            try (ResultSet rs = stm.executeQuery()) {
                if (rs.next()) {
                    return rs.getInt("total");
                }
            }
        }
        return 0;
    }


    public TotalizadorRetentativaTO buscarTotalizadoresUlt24HorasEUlt30Dias(CartaoCreditoTO cartaoTO) throws Exception {
        String cartaoMascarado1Forma = cartaoTO.getNumero().replaceAll("(\\d{6})\\d{6}(\\d{4})", "$1******$2"); //123456******3456
        String cartaoMascarado2Forma = cartaoTO.getNumero().replaceAll("\\d(?=\\d{4})", "*"); //************3456
        String cartaoMascarado3Forma = cartaoTO.getNumero().replaceAll("(?<=\\d)\\d(?=\\d{4})", "*"); //1**********3456

        StringBuilder sql = new StringBuilder();
        sql.append("SELECT \n");
        sql.append("COUNT(*) FILTER (WHERE data >= (NOW() - INTERVAL '24 HOURS')) AS totalTentativasUlt24HorasMesmoCartao, \n");
        sql.append("COUNT(*) FILTER (WHERE data >= (NOW() - INTERVAL '30 DAYS')) AS totalTentativasUlt30DiasMesmoCartao \n");
        sql.append("FROM cartaotentativa ct \n");
        sql.append("INNER JOIN transacao t on t.codigo = ct.transacao \n");
        sql.append("WHERE ( \n");
        sql.append("cartao LIKE '").append(cartaoMascarado1Forma).append("' \n");
        sql.append("OR cartao LIKE '").append(cartaoMascarado2Forma).append("' \n");
        sql.append("OR cartao LIKE '").append(cartaoMascarado3Forma).append("' \n");
        sql.append(") \n");
        sql.append("AND ct.codigoRetorno NOT ILIKE 'PAC%' \n");
        sql.append("AND t.situacao NOT IN (").append(SituacaoTransacaoEnum.CONCLUIDA_COM_SUCESSO.getId()).append(",").append(SituacaoTransacaoEnum.CANCELADA.getId())
                .append(",").append(SituacaoTransacaoEnum.ESTORNADA.getId()).append(") \n");
        sql.append("AND ct.transacao IS NOT NULL AND transacao <> 0 \n");
        sql.append("GROUP BY cartao");

        try (PreparedStatement sqlConsultar = con.prepareStatement(sql.toString())) {
            try (ResultSet tabelaResultado = sqlConsultar.executeQuery()) {
                if (tabelaResultado.next()) {
                    TotalizadorRetentativaTO totalizadorRetentativaTO = new TotalizadorRetentativaTO();
                    totalizadorRetentativaTO.setTotalTentativasUlt24HorasMesmoCartao(tabelaResultado.getInt("totalTentativasUlt24HorasMesmoCartao"));
                    totalizadorRetentativaTO.setTotalTentativasUlt30DiasMesmoCartao(tabelaResultado.getInt("totalTentativasUlt30DiasMesmoCartao"));
                    return totalizadorRetentativaTO;
                }
                return null;
            }
        }
    }

    public TotalizadorRetentativaTO buscarTotalizadoresUlt24HorasEUlt30DiasEMesmoValor(CartaoCreditoTO cartaoTO, double valorCobranca) throws Exception {
        String cartaoMascarado1Forma = cartaoTO.getNumero().replaceAll("(\\d{6})\\d{6}(\\d{4})", "$1******$2"); //123456******3456
        String cartaoMascarado2Forma = cartaoTO.getNumero().replaceAll("\\d(?=\\d{4})", "*"); //************3456
        String cartaoMascarado3Forma = cartaoTO.getNumero().replaceAll("(?<=\\d)\\d(?=\\d{4})", "*"); //1**********3456

        StringBuilder sql = new StringBuilder();
        sql.append("SELECT \n");
        sql.append("COUNT(*) FILTER (WHERE data >= (NOW() - INTERVAL '24 HOURS')) AS totalTentativasUlt24HorasMesmoCartao, \n");
        sql.append("COUNT(*) FILTER (WHERE data >= (NOW() - INTERVAL '30 DAYS')) AS totalTentativasUlt30DiasMesmoCartao \n");
        sql.append("FROM cartaotentativa ct \n");
        sql.append("INNER JOIN transacao t on t.codigo = ct.transacao \n");
        sql.append("WHERE ( \n");
        sql.append("cartao LIKE '").append(cartaoMascarado1Forma).append("' \n");
        sql.append("OR cartao LIKE '").append(cartaoMascarado2Forma).append("' \n");
        sql.append("OR cartao LIKE '").append(cartaoMascarado3Forma).append("' \n");
        sql.append(") \n");
        sql.append("AND ct.codigoRetorno NOT ILIKE 'PAC%' \n");
        sql.append("AND t.situacao NOT IN (").append(SituacaoTransacaoEnum.CONCLUIDA_COM_SUCESSO.getId()).append(",").append(SituacaoTransacaoEnum.CANCELADA.getId())
                .append(",").append(SituacaoTransacaoEnum.ESTORNADA.getId()).append(") \n");
        sql.append("AND t.valor = ").append(valorCobranca).append(" \n");
        sql.append("AND ct.transacao IS NOT NULL AND transacao <> 0");
        sql.append("GROUP BY cartao");

        try (PreparedStatement sqlConsultar = con.prepareStatement(sql.toString())) {
            try (ResultSet tabelaResultado = sqlConsultar.executeQuery()) {
                if (tabelaResultado.next()) {
                    TotalizadorRetentativaTO totalizadorRetentativaTO = new TotalizadorRetentativaTO();
                    totalizadorRetentativaTO.setTotalTentativasUlt30DiasMesmoCartao(tabelaResultado.getInt("totalTentativasUlt30DiasMesmoCartao"));
                    return totalizadorRetentativaTO;
                }
                return null;
            }
        }
    }

    public TotalizadorRetentativaTO buscarTotalizadoresMesVigenteEMesmoValor(CartaoCreditoTO cartaoTO, double valorCobranca) throws Exception {
        String cartaoMascarado1Forma = cartaoTO.getNumero().replaceAll("(\\d{6})\\d{6}(\\d{4})", "$1******$2"); //123456******3456
        String cartaoMascarado2Forma = cartaoTO.getNumero().replaceAll("\\d(?=\\d{4})", "*"); //************3456
        String cartaoMascarado3Forma = cartaoTO.getNumero().replaceAll("(?<=\\d)\\d(?=\\d{4})", "*"); //1**********3456

        StringBuilder sql = new StringBuilder();
        sql.append("SELECT \n");
        sql.append("COUNT(*) FILTER (WHERE data >= DATE_TRUNC('month', NOW())) AS totalTentativasMesVigenteMesmoCartao \n");
        sql.append("FROM cartaotentativa ct \n");
        sql.append("INNER JOIN transacao t on t.codigo = ct.transacao \n");
        sql.append("WHERE ( \n");
        sql.append("cartao LIKE '").append(cartaoMascarado1Forma).append("' \n");
        sql.append("OR cartao LIKE '").append(cartaoMascarado2Forma).append("' \n");
        sql.append("OR cartao LIKE '").append(cartaoMascarado3Forma).append("' \n");
        sql.append(") \n");
        sql.append("AND ct.codigoRetorno NOT ILIKE 'PAC%' \n");
        sql.append("AND t.situacao NOT IN (").append(SituacaoTransacaoEnum.CONCLUIDA_COM_SUCESSO.getId()).append(",").append(SituacaoTransacaoEnum.CANCELADA.getId())
                .append(",").append(SituacaoTransacaoEnum.ESTORNADA.getId()).append(") \n");
        sql.append("AND t.valor = ").append(valorCobranca).append(" \n");
        sql.append("AND ct.transacao IS NOT NULL AND transacao <> 0");
        sql.append("GROUP BY cartao");

        try (PreparedStatement sqlConsultar = con.prepareStatement(sql.toString())) {
            try (ResultSet tabelaResultado = sqlConsultar.executeQuery()) {
                if (tabelaResultado.next()) {
                    TotalizadorRetentativaTO totalizadorRetentativaTO = new TotalizadorRetentativaTO();
                    totalizadorRetentativaTO.setTotalTentativasMesVigenteMesmoCartao(tabelaResultado.getInt("totalTentativasMesVigenteMesmoCartao"));
                    return totalizadorRetentativaTO;
                }
                return null;
            }
        }
    }
}

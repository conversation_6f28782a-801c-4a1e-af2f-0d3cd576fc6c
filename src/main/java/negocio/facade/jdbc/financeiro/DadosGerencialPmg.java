/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package negocio.facade.jdbc.financeiro;

import br.com.pactosolucoes.enumeradores.BIEnum;
import br.com.pactosolucoes.enumeradores.ConfiguracaoBIEnum;
import br.com.pactosolucoes.enumeradores.FasesCRMEnum;
import br.com.pactosolucoes.enumeradores.SituacaoClienteEnum;
import br.com.pactosolucoes.enumeradores.TipoContratoEnum;
import br.com.pactosolucoes.oamd.controle.basico.DAO;
import controle.basico.ConsultaClienteControle;
import controle.basico.LtvControle;
import controle.crm.BusinessIntelligenceCRMControle;
import controle.financeiro.GestaoRecebiveisControle;
import negocio.comuns.basico.ClienteVO;
import negocio.comuns.basico.ColaboradorVO;
import negocio.comuns.basico.ConfiguracaoBIVO;
import negocio.comuns.basico.EmpresaVO;
import negocio.comuns.basico.enumerador.TipoBVEnum;
import negocio.comuns.crm.FecharMetaVO;
import negocio.comuns.financeiro.DadosGerencialPmgVO;
import negocio.comuns.financeiro.MetaFinanceiraEmpresaVO;
import negocio.comuns.financeiro.MonitoramentoVO;
import negocio.comuns.financeiro.enumerador.IdentificadorDadosGerencialEnum;
import negocio.comuns.financeiro.enumerador.IndicadoresDadosGerencialEnum;
import negocio.comuns.financeiro.enumerador.TipoFonteDadosDF;
import negocio.comuns.financeiro.enumerador.TipoRelatorioDF;
import negocio.comuns.utilitarias.Calendario;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisValidacao;
import negocio.facade.jdbc.arquitetura.FacadeManager;
import negocio.facade.jdbc.arquitetura.SuperEntidade;
import negocio.facade.jdbc.arquitetura.SuperFacadeJDBC;
import negocio.facade.jdbc.basico.ConfiguracaoBI;
import negocio.facade.jdbc.contrato.Contrato;
import negocio.facade.jdbc.utilitarias.Conexao;
import negocio.interfaces.financeiro.DadosGerencialPmgInterfaceFacade;
import org.json.JSONObject;
import relatorio.controle.financeiro.FaturamentoSinteticoControleRel;
import relatorio.controle.sad.RotatividadeAnaliticoDWControle;
import relatorio.negocio.comuns.basico.TicketMedioVO;
import relatorio.negocio.comuns.financeiro.IndiceConversaoVendaVO;
import relatorio.negocio.comuns.sad.RotatividadeAnaliticoDWVO;
import relatorio.negocio.jdbc.financeiro.DemonstrativoFinanceiroJSON;
import relatorio.negocio.jdbc.financeiro.RelatorioDRE;
import relatorio.negocio.jdbc.financeiro.TicketMedio;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.logging.Level;
import java.util.logging.Logger;

/**
 *
 * <AUTHOR>
 */
public class DadosGerencialPmg extends SuperEntidade implements DadosGerencialPmgInterfaceFacade {

    private List<ColaboradorVO> listaColaborador = new ArrayList<>();

    public static final String sqlContratoFaturados = "select %s from contrato where datalancamento between '%s' and '%s'";
    public static final String sqlContratoFaturadosContar = String.format(sqlContratoFaturados, new Object[]{
        "count(*)",
        "%s",
        "%s"
    });

    public static final String sqlContratoFaturadosMensal = "select %s from contrato con inner join contratoduracao cd on con.codigo = cd.contrato where cd.numeromeses = 1 and con.datalancamento between '%s' and '%s' and empresa = %s";
    public static final String sqlContratoFaturadosMensalContar = String.format(sqlContratoFaturadosMensal, new Object[]{
        "count(*)",
        "%s",
        "%s",
        "%s"
    });

    public static final String sqlContratoFaturadosLongaDuracao = "select %s from contrato con inner join contratoduracao cd on con.codigo = cd.contrato where cd.numeromeses > 1 and con.datalancamento between %s and %s and empresa = %s";
    public static final String sqlContratoFaturadosLongaDuracaoContar = String.format(sqlContratoFaturadosLongaDuracao, new Object[]{
        "count(*)",
        "%s",
        "%s",
        "%s"
    });
    public static final String sqlChequesCompensados = "SELECT %s FROM cheque  ch inner join movpagamento mov on mov.codigo = ch.movpagamento WHERE  ch.situacao <> 'CA' AND ch.datacompesancao BETWEEN '%s' AND '%s' AND mov.empresa = %s";
    public static final String sqlChequesCompensadosSomar  = String.format(sqlChequesCompensados , new Object[]{
        "sum(ch.valor) as valor",
        "%s",
        "%s",
        "%s"
    });
    public static final String sqlCartoesCompensados = "SELECT %s FROM cartaocredito ca inner join movpagamento mov on mov.codigo = ca.movpagamento WHERE  ca.situacao <> 'CA' AND ca.datacompesancao BETWEEN '%s' AND '%s' AND mov.empresa = %s";
    public static final String sqlCartoesCompensadosSomar  = String.format(sqlCartoesCompensados , new Object[]{
        "sum(ca.valor) as valor",
        "%s",
        "%s",
        "%s"
    });
    public static final String sqlMovpagamentosCompensados = "SELECT %s FROM movpagamento  mov INNER JOIN formapagamento fp ON fp.codigo = mov.formapagamento WHERE fp.tipoformapagamento NOT IN ('CA','CC', 'CH') AND datapagamento BETWEEN '%s' AND '%s' AND mov.empresa = %s";
    public static final String sqlMovpagamentosCompensadosSomar  = String.format(sqlMovpagamentosCompensados , new Object[]{
        "sum(mov.valor) as valor",
        "%s",
        "%s",
        "%s"
    });
    
    public static final String sqlDespesas = "SELECT %s FROM movconta  WHERE tipooperacao = 1 and  dataquitacao  BETWEEN '%s' and '%s' and empresa = %s";
    public static final String sqlDespesasSomar  = String.format(sqlDespesas , new Object[]{
        "sum(valor :: numeric) as valor",
        "%s",
        "%s",
        "%s"
    });

    public DadosGerencialPmg() throws Exception {
        super();
    }

    public DadosGerencialPmg(Connection conexao) throws Exception {
        super(conexao);
    }

    private void incluir(DadosGerencialPmgVO obj) throws Exception {
        if(!Calendario.igual(obj.getDatapesquisainicio(),obj.getDatapesquisafim())) {
            excluirDadosPeriodo(obj);
        }
        incluirSemCommit(obj);
    }
    private Double obterValorPeriodoIdentificador(String identificador,int codigoEmpresa,Date inicio,Date fim) throws Exception{
        StringBuilder sql = new StringBuilder();
        sql.append("Select valor from dadosgerencialpmg dpmg\n");
        sql.append(" WHERE dpmg.empresa = ? and dpmg.datapesquisainicio::DATE = ? and dpmg.datapesquisafim::DATE = ? and dpmg.identificador = ?");
        PreparedStatement ps = con.prepareStatement(sql.toString());
        int i = 1;
        ps.setInt(i++,codigoEmpresa);
        ps.setDate(i++,Uteis.getDataJDBC(inicio));
        ps.setDate(i++,Uteis.getDataJDBC(fim));
        ps.setString(i++,identificador);
        ResultSet rs = ps.executeQuery();
        if(rs.next()){
            return rs.getDouble("valor");
        } else {
            return 0.0;
        }

    }

    @Override
    public void incluirSemCommit(DadosGerencialPmgVO obj) throws Exception {
        String sql = "INSERT INTO dadosgerencialpmg(chave, nomeEmpresa, empresa,identificador, "
                + "indicador, periodicidade, datageracao, datapesquisainicio, datapesquisafim, valor)"
                + " VALUES ( ?, ?, ?, ?, ?, ?, ?, ?, ?, ? )";
        PreparedStatement sqlInserir = getCon().prepareStatement(sql);
        sqlInserir.setString(1, obj.getChave());
        sqlInserir.setString(2, obj.getNomeEmpresa());
        sqlInserir.setInt(3, obj.getEmpresa());
        sqlInserir.setString(4, obj.getIdentificador());
        sqlInserir.setString(5, obj.getIndicador());
        sqlInserir.setString(6, obj.getPeriodicidade());
        sqlInserir.setTimestamp(7, Uteis.getDataJDBCTimestamp(obj.getDatageracao()));
        sqlInserir.setDate(8, Uteis.getDataJDBC(obj.getDatapesquisainicio()));
        sqlInserir.setDate(9, Uteis.getDataJDBC(obj.getDatapesquisafim()));
        sqlInserir.setDouble(10, obj.getValor());
        sqlInserir.execute();
    }

    @Override
    public void alterar(DadosGerencialPmgVO obj) throws Exception {
        throw new Exception("ainda nao implementado");
    }

    @Override
    public void excluir(Integer codigo) throws Exception {
        throw new Exception("ainda nao implementado");
    }

    @Override
    public List<DadosGerencialPmgVO> consultarDados(Date inicioPeriodo, Date finalPeriodo, String identificador, String tipo, Integer empresa) throws Exception {
        throw new Exception("ainda nao implementado");
    }

    public boolean existeDadosPeriodo(DadosGerencialPmgVO obj) throws SQLException {
        String sql = "SELECT EXISTS(SELECT * FROM dadosgerencialpmg WHERE identificador ='" + obj.getIdentificador() + "' and  empresa = " + obj.getEmpresa() + " and datapesquisainicio ='" + obj.getDatapesquisainicio() + "' and periodicidade = '" + obj.getPeriodicidade() + "')";
        PreparedStatement stm = con.prepareStatement(sql);
        ResultSet tabelaResultado = stm.executeQuery();
        tabelaResultado.next();
        return tabelaResultado.getBoolean(1);
    }

    public boolean existeDadosPeriodoIndicador(DadosGerencialPmgVO obj) throws SQLException {
        String sql = "SELECT EXISTS(SELECT * FROM dadosgerencialpmg WHERE indicador ='" + obj.getIndicador() + "' and  empresa = " + obj.getEmpresa() + " and datapesquisainicio ='" + obj.getDatapesquisainicio() + "' and datapesquisainicio != datapesquisafim  and periodicidade = '" + obj.getPeriodicidade() + "')";
        PreparedStatement stm = con.prepareStatement(sql);
        ResultSet tabelaResultado = stm.executeQuery();
        tabelaResultado.next();
        return tabelaResultado.getBoolean(1);
    }

    private boolean existeDadoIndicador(DadosGerencialPmgVO obj) throws SQLException {
        String sql = "SELECT EXISTS(SELECT * FROM dadosgerencialpmg WHERE indicador ='" + obj.getIndicador() + "' and  empresa = " + obj.getEmpresa() + " and datapesquisainicio ='" + obj.getDatapesquisainicio() + "' and datapesquisainicio != datapesquisafim and periodicidade = '" + obj.getPeriodicidade() + "' "+
                "and datapesquisafim = '" + obj.getDatapesquisafim() + "')";
        PreparedStatement stm = con.prepareStatement(sql);
        ResultSet tabelaResultado = stm.executeQuery();
        tabelaResultado.next();
        return tabelaResultado.getBoolean(1);
    }

    public void excluirDadosPeriodo(DadosGerencialPmgVO obj) throws Exception {
        String sql = "delete FROM dadosgerencialpmg WHERE identificador = ? and  empresa = ? and datapesquisainicio::DATE = ? and datapesquisafim::DATE = ? and periodicidade = ?";
        PreparedStatement sqlExcluir = con.prepareStatement(sql);
        sqlExcluir.setString(1, obj.getIdentificador());
        sqlExcluir.setInt(2, obj.getEmpresa());
        sqlExcluir.setDate(3, Uteis.getDataJDBC(obj.getDatapesquisainicio()));
        sqlExcluir.setDate(4, Uteis.getDataJDBC(obj.getDatapesquisafim()));
        sqlExcluir.setString(5, obj.getPeriodicidade());
        sqlExcluir.execute();
    }

    public void excluirDadosPeriodoIndicador(DadosGerencialPmgVO obj) throws Exception {
        String sql = "delete FROM dadosgerencialpmg WHERE indicador = ? and  empresa = ? and datapesquisainicio = ? and datapesquisafim = ? and periodicidade = ?";
        PreparedStatement sqlExcluir = con.prepareStatement(sql);
        sqlExcluir.setString(1, obj.getIndicador());
        sqlExcluir.setInt(2, obj.getEmpresa());
        sqlExcluir.setDate(3, Uteis.getDataJDBC(obj.getDatapesquisainicio()));
        sqlExcluir.setDate(4, Uteis.getDataJDBC(obj.getDatapesquisafim()));
        sqlExcluir.setString(5, obj.getPeriodicidade());
        sqlExcluir.execute();
    }
     public void excluirDadosOutraChave(String chave) throws Exception {
        String sql = "delete from dadosgerencialpmg  where chave  not like  ?";
        PreparedStatement sqlExcluir = con.prepareStatement(sql);
        sqlExcluir.setString(1, chave);
        sqlExcluir.execute();
    }

    private DadosGerencialPmgVO obterUltimoDadoGerado(DadosGerencialPmgVO obj) throws SQLException {
        DadosGerencialPmgVO dadoGerencial = null;
        String sql = "SELECT * FROM dadosgerencialpmg\n" +
                "WHERE indicador ='" + obj.getIndicador() + "'\n" +
                "AND empresa = " + obj.getEmpresa() + "\n" +
                "AND datapesquisafim = '"+Uteis.getDataFormatoBD(obj.getDatapesquisafim())+"'\n"+
                "AND periodicidade = '"+obj.getPeriodicidade()+"'\n"+
                "ORDER BY codigo DESC\n" +
                "LIMIT 1";
        PreparedStatement stm = con.prepareStatement(sql);
        ResultSet rs = stm.executeQuery();
        if(rs.next()) {
            dadoGerencial = new DadosGerencialPmgVO();
            dadoGerencial.setCodigo(rs.getInt("codigo"));
            dadoGerencial.setChave(rs.getString("chave"));
            dadoGerencial.setNomeEmpresa(rs.getString("nomeempresa"));
            dadoGerencial.setEmpresa(rs.getInt("empresa"));
            dadoGerencial.setIndicador(rs.getString("indicador"));
            dadoGerencial.setIdentificador(rs.getString("identificador"));
            dadoGerencial.setPeriodicidade(rs.getString("periodicidade"));
            dadoGerencial.setDatageracao(rs.getTimestamp("datageracao"));
            dadoGerencial.setDatapesquisainicio(rs.getTimestamp("datapesquisainicio"));
            dadoGerencial.setDatapesquisafim(rs.getTimestamp("datapesquisafim"));
            dadoGerencial.setValor(rs.getDouble("valor"));
        }
        return dadoGerencial;
    }

    public void gerarDadosPMG(Integer empresa, final String chave, final java.util.Date data, final List<IndicadoresDadosGerencialEnum> indicadores) throws Exception {
        excluirDadosOutraChave(chave);

        StringBuilder sb = new StringBuilder("SELECT codigo, nome FROM empresa where ativa \n");
        if (!UteisValidacao.emptyNumber(empresa)) {
            sb.append("and codigo = ").append(empresa);
        }

        ResultSet rs = criarConsulta(sb.toString(), getCon());
        while (rs.next()) {
            Date d1 = Calendario.hoje();
            Uteis.logarDebug("Iniciando Geração de dados PMG (Mensal) da empresa " + rs.getString("nome"));
            gerarIndicadoresMensais(rs.getInt("codigo"), rs.getString("nome"), chave, data, indicadores);
            Date d2 = Calendario.hoje();
            Uteis.logarDebug(String.format("Terminando Geração de dados PMG (Mensal) da empresa %s (%s ms)", rs.getString("nome"), (d2.getTime() - d1.getTime())));
        }
    }

    public void gerarDadosDiaPMG(Integer empresa, final String chave, final Date dia, final List<IndicadoresDadosGerencialEnum> indicadores) throws Exception {
        excluirDadosOutraChave(chave);

        StringBuilder sb = new StringBuilder("SELECT codigo, nome FROM empresa where ativa \n");
        if (!UteisValidacao.emptyNumber(empresa)) {
            sb.append("and codigo = ").append(empresa);
        }

        ResultSet rs = criarConsulta(sb.toString(), getCon());
        while (rs.next()) {
            Date d1 = Calendario.hoje();
            Uteis.logarDebug("Iniciando Geração de dados PMG (Diário) da empresa " + rs.getString("nome"));
            gerarIndicadoresDiarios(rs.getInt("codigo"), rs.getString("nome"), chave, dia, indicadores);
            Date d2 = Calendario.hoje();
            Uteis.logarDebug(String.format("Terminando Geração de dados PMG (Diário) da empresa %s (%s ms)", rs.getString("nome"), (d2.getTime() - d1.getTime())));
        }
    }

    public String obterValorVendasPeriodo(Integer empresa, final String chave, final Date dataConsultar, final String tipoDado) throws Exception {
        JSONObject jsonVendas = new JSONObject();
        MetaFinanceiroBI metaDao = new MetaFinanceiroBI(con);
        Double valorCalculado;
        if (UteisValidacao.emptyString(tipoDado) || "VENDAS_MES_DIA".equals(tipoDado)) {
            //Vendas Hoje
            valorCalculado = metaDao.obterFaturamentoPorPeriodo(chave, Calendario.getDataComHoraZerada(dataConsultar),
                    Calendario.getDataComHora(dataConsultar, "23:59:59"),
                    new ArrayList<>(), new ArrayList<>(), empresa, true, true, true, false, false);
            jsonVendas.put("vendasDia", valorCalculado);
        }

        if (UteisValidacao.emptyString(tipoDado) || "VENDAS_MES_MES".equals(tipoDado)) {
            //Vendas Mês Atual
            valorCalculado = metaDao.obterFaturamentoPorPeriodo(chave, Calendario.getDataComHoraZerada(Uteis.obterPrimeiroDiaMes(dataConsultar)),
                    Calendario.getDataComHora(dataConsultar, "23:59:59"),
                    new ArrayList<>(), new ArrayList<>(), empresa, true, true, true, false, false);
            jsonVendas.put("vendasMes", valorCalculado);
        }

        Date mesPassado = Uteis.somarMeses(dataConsultar, -1);
        if (UteisValidacao.emptyString(tipoDado) || "VENDAS_MES_DIA_PASSADO".equals(tipoDado)) {
            //Vendas Acumulado até dia Passado
            valorCalculado = metaDao.obterFaturamentoPorPeriodo(chave, Uteis.obterPrimeiroDiaMesPrimeiraHora(mesPassado), Calendario.getDataComHora(mesPassado, "23:59:59"),
                    new ArrayList<>(), new ArrayList<>(), empresa, true, true, true, false, false);
            jsonVendas.put("vendasDiaPassado", valorCalculado);
        }

        if (UteisValidacao.emptyString(tipoDado) || "VENDAS_MES_MES_PASSADO".equals(tipoDado)) {
            //Vendas Mês Passado
            valorCalculado = metaDao.obterFaturamentoPorPeriodo(chave, Uteis.obterPrimeiroDiaMesPrimeiraHora(mesPassado), Uteis.obterUltimoDiaMesUltimaHora(mesPassado),
                    new ArrayList<>(), new ArrayList<>(), empresa, true, true, true, false, false);
            jsonVendas.put("vendasMesPassado", valorCalculado);
        }
        return jsonVendas.toString();
    }

    public String obterValorVendasDia(Integer empresa, final String chave, final Date dataConsultar, final String tipoDado) throws Exception {
        JSONObject jsonVendas = new JSONObject();
        MetaFinanceiroBI metaDao = new MetaFinanceiroBI(con);
        Double valorCalculado;
        if (UteisValidacao.emptyString(tipoDado) || "VENDAS_MES_DIA".equals(tipoDado)) {
            //Vendas Hoje
            valorCalculado = metaDao.obterFaturamentoRecebido(chave, Calendario.getDataComHoraZerada(dataConsultar),
                    Calendario.getDataComHora(dataConsultar, "23:59:59"),
                    new ArrayList<>(), new ArrayList<>(), empresa, true, true, true, false, false, false);
//            valorCalculado = obterValorVendas(Calendario.getDataComHoraZerada(dataConsultar), Calendario.getDataComHora(dataConsultar, "23:59:59"), empresa);
            jsonVendas.put("vendasDia", valorCalculado);
        }

        if (UteisValidacao.emptyString(tipoDado) || "VENDAS_MES_MES".equals(tipoDado)) {
            //Vendas Mês Atual
            valorCalculado = metaDao.obterFaturamentoRecebido(chave, Calendario.getDataComHoraZerada(Uteis.obterPrimeiroDiaMes(dataConsultar)),
                    Calendario.getDataComHora(dataConsultar, "23:59:59"),
                    new ArrayList<>(), new ArrayList<>(), empresa, true, true, true, false, false, false);
//            valorCalculado = obterValorVendas(Calendario.getDataComHoraZerada(Uteis.obterPrimeiroDiaMes(dataConsultar)), Calendario.getDataComHora(dataConsultar, "23:59:59"), empresa);
            jsonVendas.put("vendasMes", valorCalculado);
        }

        Date mesPassado = Uteis.somarMeses(dataConsultar, -1);
        if (UteisValidacao.emptyString(tipoDado) || "VENDAS_MES_DIA_PASSADO".equals(tipoDado)) {
            //Vendas Acumulado até dia Passado
            valorCalculado = metaDao.obterFaturamentoRecebido(chave, Uteis.obterPrimeiroDiaMesPrimeiraHora(mesPassado), Calendario.getDataComHora(mesPassado, "23:59:59"),
                    new ArrayList<>(), new ArrayList<>(), empresa, true, true, true, false, false, false);
//            valorCalculado = obterValorVendas(Uteis.obterPrimeiroDiaMesPrimeiraHora(mesPassado), Calendario.getDataComHora(mesPassado, "23:59:59"), empresa);
            jsonVendas.put("vendasDiaPassado", valorCalculado);
        }

        if (UteisValidacao.emptyString(tipoDado) || "VENDAS_MES_MES_PASSADO".equals(tipoDado)) {
            //Vendas Mês Passado
            valorCalculado = metaDao.obterFaturamentoRecebido(chave, Uteis.obterPrimeiroDiaMesPrimeiraHora(mesPassado), Uteis.obterUltimoDiaMesUltimaHora(mesPassado),
                    new ArrayList<>(), new ArrayList<>(), empresa, true, true, true, false, false, false);
//            valorCalculado = obterValorVendas(Uteis.obterPrimeiroDiaMesPrimeiraHora(mesPassado), Uteis.obterUltimoDiaMesUltimaHora(mesPassado), empresa);
            jsonVendas.put("vendasMesPassado", valorCalculado);
        }
        return jsonVendas.toString();
    }

    public Double obterValorVendas(Date dataInicio, Date dataTermino, Integer codigoEmpresa) throws Exception {
        String sql = "SELECT sum(valor) as valorConsulta FROM movpagamento mpag\n" +
                "INNER JOIN formapagamento fp ON mpag.formapagamento = fp.codigo\n" +
                "WHERE 1 = 1 \n" +
                "AND fp.tipoformapagamento <> 'CC'\n" +
                "AND mpag.empresa = ?\n" +
                "AND datalancamento >= ?\n" +
                "AND datalancamento <= ?;";

        PreparedStatement ps = getCon().prepareStatement(sql);
        int i = 1;
        ps.setInt(i++, codigoEmpresa);
        ps.setTimestamp(i++, Uteis.getDataJDBCTimestamp(dataInicio));
        ps.setTimestamp(i++, Uteis.getDataJDBCTimestamp(dataTermino));
        ResultSet rs = ps.executeQuery();
        return (rs.next()) ? rs.getDouble("valorConsulta") : 0.0;
    }

    private Map<String, IdentificadorDadosGerencialEnum> mapaIndicadoresCRM(){
        Map<String, IdentificadorDadosGerencialEnum> mapaIndicadorCRM = new HashMap<>();
        mapaIndicadorCRM.put(FasesCRMEnum.CONVERSAO_AGENDADOS.name()+"meta", IdentificadorDadosGerencialEnum.META_CRM_CONVERSAO_AGENDADOS);
        mapaIndicadorCRM.put(FasesCRMEnum.CONVERSAO_AGENDADOS.name()+"resultado", IdentificadorDadosGerencialEnum.RESULTADO_CRM_CONVERSAO_AGENDADOS);
        
        mapaIndicadorCRM.put(FasesCRMEnum.CONVERSAO_DESISTENTES.name()+"meta", IdentificadorDadosGerencialEnum.META_CRM_CONVERSAO_DESISTENTES);
        mapaIndicadorCRM.put(FasesCRMEnum.CONVERSAO_DESISTENTES.name()+"resultado", IdentificadorDadosGerencialEnum.RESULTADO_CRM_CONVERSAO_DESISTENTES);
        
        mapaIndicadorCRM.put(FasesCRMEnum.CONVERSAO_EX_ALUNOS.name()+"meta", IdentificadorDadosGerencialEnum.META_CRM_CONVERSAO_EX_ALUNOS);
        mapaIndicadorCRM.put(FasesCRMEnum.CONVERSAO_EX_ALUNOS.name()+"resultado", IdentificadorDadosGerencialEnum.RESULTADO_CRM_CONVERSAO_EX_ALUNOS);
        
        mapaIndicadorCRM.put(FasesCRMEnum.CONVERSAO_INDICADOS.name()+"meta", IdentificadorDadosGerencialEnum.META_CRM_CONVERSAO_INDICADOS);
        mapaIndicadorCRM.put(FasesCRMEnum.CONVERSAO_INDICADOS.name()+"resultado", IdentificadorDadosGerencialEnum.RESULTADO_CRM_CONVERSAO_INDICADOS);
        
        mapaIndicadorCRM.put(FasesCRMEnum.CONVERSAO_PASSIVO.name()+"meta", IdentificadorDadosGerencialEnum.META_CRM_CONVERSAO_RECEPTIVO);
        mapaIndicadorCRM.put(FasesCRMEnum.CONVERSAO_PASSIVO.name()+"resultado", IdentificadorDadosGerencialEnum.RESULTADO_CRM_CONVERSAO_RECEPTIVO);
        
        mapaIndicadorCRM.put(FasesCRMEnum.CONVERSAO_VISITANTES_ANTIGOS.name()+"meta", IdentificadorDadosGerencialEnum.META_CRM_CONVERSAO_VISITANTES_ANTIGOS);
        mapaIndicadorCRM.put(FasesCRMEnum.CONVERSAO_VISITANTES_ANTIGOS.name()+"resultado", IdentificadorDadosGerencialEnum.RESULTADO_CRM_CONVERSAO_VISITANTES_ANTIGOS);
        
        return mapaIndicadorCRM;
    }

    private void gerarIndicadoresMensais(Integer empresa, String nomeEmpresa, String chave, java.util.Date data, List<IndicadoresDadosGerencialEnum> indicadores) {
        List<IndicadoresDadosGerencialEnum> lista = new ArrayList<>();
        if (!UteisValidacao.emptyList(indicadores)) {
            lista = new ArrayList<>(indicadores);
        } else {
            lista.add(IndicadoresDadosGerencialEnum.CRM);
            lista.add(IndicadoresDadosGerencialEnum.FINANCEIROS);
            lista.add(IndicadoresDadosGerencialEnum.INDICE_CONVERSAO);
            lista.add(IndicadoresDadosGerencialEnum.INDICE_RENOVACAO);
            lista.add(IndicadoresDadosGerencialEnum.MOVIMENTACAO_CONTRATO);
            lista.add(IndicadoresDadosGerencialEnum.PENDENCIAS);
            lista.add(IndicadoresDadosGerencialEnum.QUANTIDADE_PLANOS);
            lista.add(IndicadoresDadosGerencialEnum.LTV);
            lista.add(IndicadoresDadosGerencialEnum.ADM);
            lista.add(IndicadoresDadosGerencialEnum.TICKET_MEDIO);
        }
        List<DadosGerenciaisPmgCallable> callableTasks = new ArrayList<>();
        for (IndicadoresDadosGerencialEnum indicador : lista) {
            callableTasks.add(new DadosGerenciaisPmgCallable(con, empresa, nomeEmpresa, chave, data, indicador, "MS"));
        }
        executarCallableDadosPMG(callableTasks);
    }

    private void gerarIndicadoresSemanais(Integer empresa, String nomeEmpresa, String chave, java.util.Date data) throws Exception {
        List<DadosGerenciaisPmgCallable> callableTasks = new ArrayList<>();
        callableTasks.add(new DadosGerenciaisPmgCallable(con, empresa, nomeEmpresa, chave, data, IndicadoresDadosGerencialEnum.INDICE_CONVERSAO, "SM"));
        callableTasks.add(new DadosGerenciaisPmgCallable(con, empresa, nomeEmpresa, chave, data, IndicadoresDadosGerencialEnum.INDICE_RENOVACAO, "SM"));
        executarCallableDadosPMG(callableTasks);
    }

    private void gerarIndicadoresDiarios(Integer empresa, String nomeEmpresa, String chave, Date data, List<IndicadoresDadosGerencialEnum> indicadores) {
        List<IndicadoresDadosGerencialEnum> indicadoresDadosGerenciaisProcessar = new ArrayList<>();
        if (!UteisValidacao.emptyList(indicadores)) {
            indicadoresDadosGerenciaisProcessar = new ArrayList<>(indicadores);
        } else {
            indicadoresDadosGerenciaisProcessar.add(IndicadoresDadosGerencialEnum.MOVIMENTACAO_CONTRATO);
            indicadoresDadosGerenciaisProcessar.add(IndicadoresDadosGerencialEnum.INDICE_CONVERSAO);
            indicadoresDadosGerenciaisProcessar.add(IndicadoresDadosGerencialEnum.CRM);
            indicadoresDadosGerenciaisProcessar.add(IndicadoresDadosGerencialEnum.ADM);
            indicadoresDadosGerenciaisProcessar.add(IndicadoresDadosGerencialEnum.FINANCEIROS);
        }
        List<DadosGerenciaisPmgCallable> callableTasks = new ArrayList<>();
        for (IndicadoresDadosGerencialEnum indicador : indicadoresDadosGerenciaisProcessar) {
            callableTasks.add(new DadosGerenciaisPmgCallable(con, empresa, nomeEmpresa, chave, data, indicador, "DI"));
        }
        executarCallableDadosPMG(callableTasks);
    }

    public void gerarDadosCargaIndicadoresDiariosRenovacao(String chave) throws Exception {
        ResultSet rs = criarConsulta("SELECT codigo,nome FROM empresa where ativa", con);
        while (rs.next()) {
            Date d1 = negocio.comuns.utilitarias.Calendario.hoje();
            Uteis.logarDebug("Iniciando Geração de dados pmg da empresa " + rs.getString("nome"));

            DadosGerencialPmgVO obj = new DadosGerencialPmgVO();
            obj.setEmpresa(rs.getInt("codigo"));
            obj.setNomeEmpresa(rs.getString("nome"));
            obj.setChave(chave);
            obj.setDatageracao(Calendario.hoje());
            obj.setIndicador(IndicadoresDadosGerencialEnum.INDICE_RENOVACAO.getSigla());
            obj.setPeriodicidade("MS");
            Date dataAnterior = Uteis.somarDias(d1, -1);

            obj.setDatapesquisainicio(Calendario.getDataComHoraZerada(Uteis.somarDias(dataAnterior,-1)));
            obj.setDatapesquisafim(Calendario.getDataComHoraZerada(Uteis.somarDias(dataAnterior,-1)));

            gerarDadosRenovacaoUltimos60Dias(obj);
        }
    }

    public void gerarDadosIndicador(Integer empresa, String nomeEmpresa, String chave, java.util.Date data,
                                     IndicadoresDadosGerencialEnum indicador, String frequencia) throws Exception {
        Conexao.guardarConexaoForJ2SE(con);
        Date d1 = negocio.comuns.utilitarias.Calendario.hoje();
        DadosGerencialPmgVO obj = new DadosGerencialPmgVO();
        obj.setEmpresa(empresa);
        obj.setNomeEmpresa(nomeEmpresa);
        obj.setChave(chave);
        obj.setDatageracao(Calendario.hoje());
        obj.setIndicador(indicador.getSigla());
        obj.setPeriodicidade(frequencia);
        switch (frequencia) {
            case "MS": {
                Date dataAnterior = Uteis.somarDias(data, -1);
                //Gera não exista gera a primeira carga de cada indicador diário
                obj.setDatapesquisainicio(Calendario.getDataComHoraZerada(Uteis.somarDias(dataAnterior, -1)));
                obj.setDatapesquisafim(Calendario.getDataComHoraZerada(Uteis.somarDias(dataAnterior, -1)));
                if (!existeDadoIndicador(obj)) {
                    if (obj.getIndicador().equals(IndicadoresDadosGerencialEnum.FINANCEIROS.getSigla())) {
                        gerarDadosFaturamentoRecebidoUltimos60Dias(obj);
                    }
                }

                obj.setDatapesquisainicio(Calendario.getDataComHoraZerada(Uteis.obterPrimeiroDiaMes(dataAnterior)));
                obj.setDatapesquisafim(Uteis.obterUltimoDiaMesUltimaHora(dataAnterior));
                if (Calendario.maiorOuIgual(obj.getDatapesquisafim(), data) && !obj.getIndicador().equals(IndicadoresDadosGerencialEnum.INDICE_RENOVACAO.getSigla())) {
                    obj.setDatapesquisafim(Calendario.getDataComUltimaHora(dataAnterior));
                }
                excluirDadosPeriodoIndicador(obj);
                calcularDadosIndicador(obj);
                for (int i = 1; i < 30; i++) {
                    dataAnterior = Uteis.somarDias(obj.getDatapesquisainicio(), -1);
                    obj.setDatapesquisainicio(Calendario.getDataComHoraZerada(Uteis.obterPrimeiroDiaMes(dataAnterior)));
                    obj.setDatapesquisafim(Uteis.obterUltimoDiaMesUltimaHora(dataAnterior));
                    if (!existeDadosPeriodoIndicador(obj)) {
                        calcularDadosIndicador(obj);
                    } else {
                        break;
                    }
                }
                break;
            }
            case "SM": {
                Date dataAnterior = Uteis.somarDias(data, -1);
                obj.setDatapesquisainicio(Calendario.getDataComHoraZerada(Uteis.obterPrimeiroEUltimoDiaSemana(true, dataAnterior)));
                obj.setDatapesquisafim(Uteis.obterUltimoDiaSemanaUltimaHora(dataAnterior));
                if (Calendario.maior(obj.getDatapesquisafim(), data) && !obj.getIndicador().equals(IndicadoresDadosGerencialEnum.INDICE_RENOVACAO.getSigla())) {
                    obj.setDatapesquisafim(Uteis.getDataComUltimaHora(dataAnterior));
                }
                excluirDadosPeriodoIndicador(obj);
                calcularDadosIndicador(obj);
                for (int i = 1; i < 15; i++) {
                    dataAnterior = Uteis.somarDias(obj.getDatapesquisainicio(), -1);
                    obj.setDatapesquisainicio(Calendario.getDataComHoraZerada(Uteis.obterPrimeiroEUltimoDiaSemana(true, dataAnterior)));
                    obj.setDatapesquisafim(Uteis.obterUltimoDiaSemanaUltimaHora(dataAnterior));
                    if (!existeDadosPeriodo(obj)) {
                        calcularDadosIndicador(obj);
                    } else {
                        break;
                    }
                }
                break;
            }
            case "DI": {
                boolean permiteGerar = true;
                obj.setDatapesquisainicio(Calendario.getDataComHoraZerada(data));
                obj.setDatapesquisafim(Calendario.getDataComUltimaHora(data));
                DadosGerencialPmgVO ultimoGerado = obterUltimoDadoGerado(obj);
                if (ultimoGerado != null) {
                    Date dataProxGeracao = Uteis.somarCampoData(ultimoGerado.getDatageracao(), Calendar.MINUTE, 30);
                    permiteGerar = Calendario.maiorComHora(Calendario.hoje(), dataProxGeracao);
                }
                if (permiteGerar) {
                    //Forçar para salvar o Mensal;
//                    obj.setPeriodicidade("MS");

                    //Gera não exista gera a primeira carga de cada indicador diário
                    excluirDadosPeriodoIndicador(obj);
                    calcularDadosIndicador(obj);
                }

                break;
            }
        }
        Date d2 = negocio.comuns.utilitarias.Calendario.hoje();
        Uteis.logarDebug(String.format("Dados do PMG %s foram gerados (%s) (%s ms)", indicador.getDescricao(),frequencia, (d2.getTime() - d1.getTime())));
    }

    private void calcularDadosIndicador(DadosGerencialPmgVO obj) {
        if (obj.getIndicador().equals(IndicadoresDadosGerencialEnum.MOVIMENTACAO_CONTRATO.getSigla())) {
            try {
                gravarDadosMovimentacaoContrato(obj);
//                USADO PARA GERAR OS DADOS DO Mês PASSADO, TEMPORÁRIO
                if(obj.getPeriodicidade().equals("MS")) {
                    gerarAtivosMesPassado(obj);
                }
            } catch (Exception ex) {
                ex.printStackTrace();
                Logger.getLogger(DadosGerencialPmg.class.getName()).log(Level.SEVERE, null, ex);
            }

        } else if (obj.getIndicador().equals(IndicadoresDadosGerencialEnum.INDICE_CONVERSAO.getSigla())) {
            try {
                gravarDadosIndiceConversao(obj);
            } catch (Exception ex) {
                Logger.getLogger(DadosGerencialPmg.class.getName()).log(Level.SEVERE, null, ex);
            }
        } else if (obj.getIndicador().equals(IndicadoresDadosGerencialEnum.INDICE_RENOVACAO.getSigla())) {
            try {
                gravarDadosIndiceRenovacao(obj);
            } catch (Exception ex) {
                Logger.getLogger(DadosGerencialPmg.class.getName()).log(Level.SEVERE, null, ex);
            }
        } else if (obj.getIndicador().equals(IndicadoresDadosGerencialEnum.QUANTIDADE_PLANOS.getSigla())) {
            try {
                gravarDadosQuantidadePlanos(obj);
            } catch (Exception ex) {
                Logger.getLogger(DadosGerencialPmg.class.getName()).log(Level.SEVERE, null, ex);
            }

        } else if (obj.getIndicador().equals(IndicadoresDadosGerencialEnum.PENDENCIAS.getSigla())) {
            try {
                gravarDadosPendencias(obj);
            } catch (Exception ex) {
                Logger.getLogger(DadosGerencialPmg.class.getName()).log(Level.SEVERE, null, ex);
            }

        } else if (obj.getIndicador().equals(IndicadoresDadosGerencialEnum.FINANCEIROS.getSigla())) {
            try {
                if (obj.getPeriodicidade().equals("MS")) {
                    gravarDadosFinanceiros(obj);
                }
            } catch (Exception ex) {
                Logger.getLogger(DadosGerencialPmg.class.getName()).log(Level.SEVERE, null, ex);
            }
            try {
                gravarDadosFinanceirosRecebiveis(obj);
            } catch (Exception ex) {
                Logger.getLogger(DadosGerencialPmg.class.getName()).log(Level.SEVERE, null, ex);
            }
        } else if (obj.getIndicador().equals(IndicadoresDadosGerencialEnum.CRM.getSigla())) {
            try {
                String key = DAO.resolveKeyFromConnection(Conexao.getConexaoForJ2SE());
                gerarDadosCRM(key, obj);
            } catch (Exception ex) {
                Logger.getLogger(DadosGerencialPmg.class.getName()).log(Level.SEVERE, null, ex);
            }
        } else if (obj.getIndicador().equals(IndicadoresDadosGerencialEnum.LTV.getSigla())) {
            try {
                gravarDadosltv(obj);
            } catch (Exception ex) {
                Logger.getLogger(DadosGerencialPmg.class.getName()).log(Level.SEVERE, null, ex);
            }
        } else if (obj.getIndicador().equals(IndicadoresDadosGerencialEnum.ADM.getSigla())) {
            try {
                gravarDadosAdm(obj);
            } catch (Exception ex) {
                Logger.getLogger(DadosGerencialPmg.class.getName()).log(Level.SEVERE, null, ex);
            }
        } else if (obj.getIndicador().equals(IndicadoresDadosGerencialEnum.TICKET_MEDIO.getSigla())) {
            try {
                gravarDadosTicketMedio(obj);
            } catch (Exception ex) {
                Logger.getLogger(DadosGerencialPmg.class.getName()).log(Level.SEVERE, null, ex);
            }
        }
    }

    private void gravarDadosTicketMedio(DadosGerencialPmgVO obj) throws Exception {
        EmpresaVO empresa = new EmpresaVO();
        empresa.setCodigo(obj.getEmpresa());
        TicketMedio ticketDao = new TicketMedio(con);

        ConfiguracaoBI configuracaoBI = new ConfiguracaoBI(con);
        List<ConfiguracaoBIVO> configuracoes = configuracaoBI.consultarPorBI(BIEnum.TICKET_MEDIO, empresa.getCodigo());
        configuracoes.removeIf(c -> c.getConfiguracao().equals(ConfiguracaoBIEnum.INCLUIR_PRODUTOS_RECEITA));

        TicketMedioVO ticketMedioVO = ticketDao.montarTicketMedio(true, configuracoes, empresa.getCodigo(), obj.getDatapesquisafim());

        obj.setIdentificador(IdentificadorDadosGerencialEnum.TICKET_MEDIO_ATIVOS.getSigla());
        obj.setValor(ticketMedioVO.getAtivos().doubleValue());
        incluir(obj);

        obj.setIdentificador(IdentificadorDadosGerencialEnum.TICKET_MEDIO_MEDIA_ATIVOS.getSigla());
        obj.setValor(ticketMedioVO.getMediaAtivosVencidos().doubleValue());
        incluir(obj);

        obj.setIdentificador(IdentificadorDadosGerencialEnum.TICKET_MEDIO_BOLTAS.getSigla());
        obj.setValor(ticketMedioVO.getBolsas().doubleValue());
        incluir(obj);

        obj.setIdentificador(IdentificadorDadosGerencialEnum.TICKET_MEDIO_DEPENDENTES.getSigla());
        obj.setValor(ticketMedioVO.getDependentesFimMes().doubleValue());
        incluir(obj);

        obj.setIdentificador(IdentificadorDadosGerencialEnum.TICKET_MEDIO_PAGANTES.getSigla());
        obj.setValor(ticketMedioVO.getPagantes().doubleValue());
        incluir(obj);

        obj.setIdentificador(IdentificadorDadosGerencialEnum.TICKET_MEDIO_RECEITA.getSigla());
        obj.setValor(ticketMedioVO.getCaixaReceita());
        incluir(obj);

        obj.setIdentificador(IdentificadorDadosGerencialEnum.TICKET_MEDIO_POR_RECEITA.getSigla());
        obj.setValor(ticketMedioVO.getTicketReceita());
        incluir(obj);

        obj.setIdentificador(IdentificadorDadosGerencialEnum.TICKET_MEDIO_COMPETENCIA.getSigla());
        obj.setValor(ticketMedioVO.getCaixaCompetencia());
        incluir(obj);

        obj.setIdentificador(IdentificadorDadosGerencialEnum.TICKET_MEDIO_POR_COMPETENCIA.getSigla());
        obj.setValor(ticketMedioVO.getTicketCompetencia());
        incluir(obj);

        obj.setIdentificador(IdentificadorDadosGerencialEnum.TICKET_MEDIO_DESPESAS.getSigla());
        obj.setValor(ticketMedioVO.getDespesaTotalMes());
        incluir(obj);

        obj.setIdentificador(IdentificadorDadosGerencialEnum.TICKET_MEDIO_DESPESAS_CONTRATO.getSigla());
        obj.setValor(ticketMedioVO.getDespesaPorAluno());
        incluir(obj);




    }

    private void gerarAtivosMesPassado(DadosGerencialPmgVO obj) throws Exception{
        obj.setIdentificador(IdentificadorDadosGerencialEnum.ATIVOS_FIM_MES.getSigla());
        if(!existeDadosPeriodo(obj)){
            Date inicioOrigem = obj.getDatapesquisainicio();
            Date fimOrigem = obj.getDatapesquisafim();
            Date inicio  = Uteis.somarMeses(inicioOrigem, -1);
            Date fim  = Uteis.obterUltimoDiaMes(Uteis.somarMeses(fimOrigem, -1));
            obj.setDatapesquisainicio(inicio);
            obj.setDatapesquisafim(fim);
            gravarDadosMovimentacaoContrato(obj);
            obj.setDatapesquisafim(inicioOrigem);
            obj.setDatapesquisafim(fimOrigem);
        }
    }
    private void gravarDadosMovimentacaoContrato(DadosGerencialPmgVO obj) throws Exception {
        SimpleDateFormat sdf = new SimpleDateFormat("dd/MM/yyyy");

        java.util.Date fim;
        fim = sdf.parse(Uteis.getData(obj.getDatapesquisafim()));

        int mes = Uteis.getMesData(fim);
        int ano = Uteis.getAnoData(fim);

        RotatividadeAnaliticoDWControle control = new RotatividadeAnaliticoDWControle();
        RotatividadeAnaliticoDWVO rotVO = new RotatividadeAnaliticoDWVO();
        EmpresaVO empresa = FacadeManager.getFacade().getEmpresa().consultarPorChavePrimaria(obj.getEmpresa(), Uteis.NIVELMONTARDADOS_DADOSBASICOS);
        rotVO.setEmpresa(empresa);

        rotVO.setAno(ano);
        rotVO.setMes(mes);
        rotVO.setDia(fim);
        control.setRotatividadeAnaliticoDWVO(rotVO);
        control.setEmpresaVO(rotVO.getEmpresa());
        control.setDataBaseFiltro(fim);
        control.montarRelatorioRotatividadeTelaInicialMS(true, obj.getChave());

        obj.setIdentificador(IdentificadorDadosGerencialEnum.TOTAL_INICIO.getSigla());
        obj.setValor(control.getRotatividadeSinteticoDWVO().getQtdVigentesMesAnterior().doubleValue());
        incluir(obj);

        obj.setIdentificador(IdentificadorDadosGerencialEnum.ATIVOS_INICIO_MES.getSigla());
        obj.setValor(control.getRotatividadeSinteticoDWVO().getQtdeVigenteMesAtual().doubleValue());
        incluir(obj);

        obj.setIdentificador(IdentificadorDadosGerencialEnum.ATIVOS_FIM_MES.getSigla());
        obj.setValor(control.getRotatividadeSinteticoDWVO().getQtdeFinalMesAtual().doubleValue());
        incluir(obj);

        obj.setIdentificador(IdentificadorDadosGerencialEnum.SALDO_MES.getSigla());
        obj.setValor(control.getRotatividadeAnaliticoDWVO().getQtdSaldo().doubleValue());
        incluir(obj);

        obj.setIdentificador(IdentificadorDadosGerencialEnum.VENCIDOS_FINAL.getSigla());
        obj.setValor(control.getRotatividadeAnaliticoDWVO().getQtdVencidoMes().doubleValue());
        incluir(obj);
        
        obj.setIdentificador(IdentificadorDadosGerencialEnum.MATRICULADOS.getSigla());
        if(obj.getPeriodicidade().equals("DI")){
            obj.setValor(control.getRotatividadeAnaliticoDWVO().getQtdMatriculadoHoje().doubleValue());
        } else {
            obj.setValor(control.getRotatividadeAnaliticoDWVO().getQtdMatriculado().doubleValue());
        }
        incluir(obj);
        
        obj.setIdentificador(IdentificadorDadosGerencialEnum.REMATRICULADOS.getSigla());
        if(obj.getPeriodicidade().equals("DI")) {
            obj.setValor(control.getRotatividadeAnaliticoDWVO().getQtdRematriculadoHoje().doubleValue());
        } else {
            obj.setValor(control.getRotatividadeAnaliticoDWVO().getQtdRematriculado().doubleValue());
        }
        incluir(obj);

        obj.setIdentificador(IdentificadorDadosGerencialEnum.TRANCADOS.getSigla());
        if(obj.getPeriodicidade().equals("DI")) {
             obj.setValor(control.getRotatividadeAnaliticoDWVO().getQtdTrancamentoHoje().doubleValue());
        } else {
            obj.setValor(control.getRotatividadeAnaliticoDWVO().getQtdTrancamento().doubleValue());
        }
        incluir(obj);


        if(obj.getPeriodicidade().equals("DI")) {//provisorio até usar todos os diarios
            obj.setIdentificador(IdentificadorDadosGerencialEnum.DESISTENTES_HOJE.getSigla());
            obj.setValor(control.getRotatividadeAnaliticoDWVO().getQtdDesistenteHoje().doubleValue());
            incluir(obj);
            obj.setIdentificador(IdentificadorDadosGerencialEnum.CANCELADOS_HOJE.getSigla());
            obj.setValor(control.getRotatividadeAnaliticoDWVO().getQtdCanceladoHoje().doubleValue());
            incluir(obj);
        }

        obj.setIdentificador(IdentificadorDadosGerencialEnum.DESISTENTES.getSigla());
        if(obj.getPeriodicidade().equals("DI")) {
            obj.setValor(control.getRotatividadeAnaliticoDWVO().getQtdDesistenteHoje().doubleValue());
        } else {
            obj.setValor(control.getRotatividadeAnaliticoDWVO().getQtdDesistente().doubleValue());
        }
        incluir(obj);



        obj.setIdentificador(IdentificadorDadosGerencialEnum.CANCELADOS.getSigla());
        if(obj.getPeriodicidade().equals("DI")) {
            obj.setValor(control.getRotatividadeAnaliticoDWVO().getQtdCanceladoHoje().doubleValue());
        } else {
            obj.setValor(control.getRotatividadeAnaliticoDWVO().getQtdCancelado().doubleValue());
        }
        incluir(obj);

        obj.setIdentificador(IdentificadorDadosGerencialEnum.RE_TRANCADOS.getSigla());
        if(obj.getPeriodicidade().equals("DI")) {
            obj.setValor(control.getRotatividadeAnaliticoDWVO().getQtdRetornoTrancamentoHoje().doubleValue());
        } else {
            obj.setValor(control.getRotatividadeAnaliticoDWVO().getQtdRetornoTrancamento().doubleValue());
        }
        incluir(obj);

        obj.setIdentificador(IdentificadorDadosGerencialEnum.CONTRATO_TRANSFERIDO.getSigla());
        if(obj.getPeriodicidade().equals("DI")) {
            obj.setValor(control.getRotatividadeAnaliticoDWVO().getQtdContratoTransferidoHoje().doubleValue());
        } else {
            obj.setValor(control.getRotatividadeAnaliticoDWVO().getQtdContratoTransferido().doubleValue());
        }
        incluir(obj);


        obj.setIdentificador(IdentificadorDadosGerencialEnum.TOTAL_FINAL.getSigla());
        obj.setValor(control.getRotatividadeSinteticoDWVO().getQtdTotal().doubleValue());
        incluir(obj);
        
        obj.setIdentificador(IdentificadorDadosGerencialEnum.PERC_MATRICULAS.getSigla());
        if(control.getRotatividadeAnaliticoDWVO().getQtdMatriculado() > 0 && control.getRotatividadeSinteticoDWVO().getQtdVigentesMesAnterior() > 0){
            obj.setValor(Uteis.arredondarForcando2CasasDecimais((control.getRotatividadeAnaliticoDWVO().getQtdMatriculado().doubleValue() * 100) /control.getRotatividadeSinteticoDWVO().getQtdVigentesMesAnterior().doubleValue()));
        } else {
            obj.setValor(0.0);
        }
        incluir(obj);
        
        obj.setIdentificador(IdentificadorDadosGerencialEnum.PERC_REMATRICULAS.getSigla());
        if(control.getRotatividadeAnaliticoDWVO().getQtdRematriculado() > 0 && control.getRotatividadeSinteticoDWVO().getQtdVigentesMesAnterior() > 0){
            obj.setValor(Uteis.arredondarForcando2CasasDecimais((control.getRotatividadeAnaliticoDWVO().getQtdRematriculado().doubleValue() * 100) /control.getRotatividadeSinteticoDWVO().getQtdVigentesMesAnterior().doubleValue()));
        } else {
            obj.setValor(0.0);
        }
        incluir(obj);
        
        obj.setIdentificador(IdentificadorDadosGerencialEnum.PERC_MATRI_REM.getSigla());
        if((control.getRotatividadeAnaliticoDWVO().getQtdRematriculado() + control.getRotatividadeAnaliticoDWVO().getQtdMatriculado()) > 0 && control.getRotatividadeSinteticoDWVO().getQtdVigentesMesAnterior() > 0){
            obj.setValor(Uteis.arredondarForcando2CasasDecimais(((control.getRotatividadeAnaliticoDWVO().getQtdRematriculado().doubleValue() + control.getRotatividadeAnaliticoDWVO().getQtdMatriculado().doubleValue()) * 100) /control.getRotatividadeSinteticoDWVO().getQtdVigentesMesAnterior().doubleValue()));
        } else {
            obj.setValor(0.0);
        }
        incluir(obj);

        obj.setIdentificador(IdentificadorDadosGerencialEnum.NOVOS_CONTRATOS.getSigla());
        int novosCliente = 0;
        if(obj.getPeriodicidade().equals("DI")) {
            novosCliente = control.getRotatividadeAnaliticoDWVO().getQtdMatriculadoHoje() + control.getRotatividadeAnaliticoDWVO().getQtdRematriculadoHoje();
        } else {
            novosCliente = control.getRotatividadeAnaliticoDWVO().getQtdMatriculado() + control.getRotatividadeAnaliticoDWVO().getQtdRematriculado();
        }
        if (novosCliente > 0) {
            obj.setValor((double) novosCliente);
        } else {
            obj.setValor(0.0);
        }
        incluir(obj);

        obj.setIdentificador(IdentificadorDadosGerencialEnum.DEPENDENTE_INICIO_MES.getSigla());
        obj.setValor(control.getRotatividadeSinteticoDWVO().getQtdDependentesMesAtual().doubleValue());
        incluir(obj);

        obj.setIdentificador(IdentificadorDadosGerencialEnum.DEPENDENTE_FIM_MES.getSigla());
        obj.setValor(control.getRotatividadeSinteticoDWVO().getQtdDependentesFinalMesAtual().doubleValue());
        incluir(obj);

        obj.setIdentificador(IdentificadorDadosGerencialEnum.ATIVO_DEPENDENTE_INICIO_MES.getSigla());
        obj.setValor(control.getRotatividadeSinteticoDWVO().getQtdeVigenteMesAtual().doubleValue() + control.getRotatividadeSinteticoDWVO().getQtdDependentesMesAtual().doubleValue());
        incluir(obj);

        obj.setIdentificador(IdentificadorDadosGerencialEnum.ATIVO_DEPENDENTE_FIM_MES.getSigla());
        obj.setValor(control.getRotatividadeSinteticoDWVO().getQtdeFinalMesAtual().doubleValue() + control.getRotatividadeSinteticoDWVO().getQtdDependentesFinalMesAtual().doubleValue());
        incluir(obj);

        obj.setIdentificador(IdentificadorDadosGerencialEnum.CLIENTES_DEPENDENTE_ATIVO_INICIO_MES.getSigla());
        obj.setValor(control.getRotatividadeSinteticoDWVO().getQtdClienteAtivosDependentesMesAtual().doubleValue());
        incluir(obj);

        obj.setIdentificador(IdentificadorDadosGerencialEnum.CLIENTES_DEPENDENTE_ATIVO_FIM_MES.getSigla());
        obj.setValor(control.getRotatividadeSinteticoDWVO().getQtdClienteAtivosDependentesFinalMesAtual().doubleValue());
        incluir(obj);

        obj.setIdentificador(IdentificadorDadosGerencialEnum.BOLSISTAS.getSigla());
        obj.setValor(control.getRotatividadeSinteticoDWVO().getQtdBolsistasFinalMesAtual().doubleValue());
        incluir(obj);
    }

    private void gravarDadosIndiceConversao(DadosGerencialPmgVO obj) throws Exception {
        List<TipoBVEnum> listaTiposBV = new ArrayList<>();
        listaTiposBV.add(TipoBVEnum.MA);
        listaTiposBV.add(TipoBVEnum.RE);
        listaTiposBV.add(TipoBVEnum.RT);

        List<TipoContratoEnum> listaTipoContratos = new ArrayList<>();
        listaTipoContratos.add(TipoContratoEnum.AGENDADO);
        listaTipoContratos.add(TipoContratoEnum.ESPONTANEO);

        obj.setIdentificador(IdentificadorDadosGerencialEnum.BV_TOTAL.getSigla());
        IndiceConversaoVendaVO indiceConversaoVendaVO= new IndiceConversaoVendaVO();
        indiceConversaoVendaVO.setMapaQuestionarios(
                getFacade().getQuestionarioCliente().consultaMapaQuestionarioPorDataEmpresaPorColaborador(
                        obj.getDatapesquisainicio(), obj.getDatapesquisafim(), "", obj.getEmpresa(), listaTiposBV, false));
        indiceConversaoVendaVO.setMapaGeralMatriculas(getFacade().getCliente().consultarClientesMapaICV(obj.getDatapesquisainicio(), obj.getDatapesquisafim(),
                        obj.getEmpresa(),  "MA", "", listaTipoContratos, listaTiposBV, false, false));
        indiceConversaoVendaVO.setMapaGeralRematriculas(getFacade().getCliente().consultarClientesMapaICV(obj.getDatapesquisainicio(), obj.getDatapesquisafim(),
                        obj.getEmpresa(),  "RE", "", listaTipoContratos, listaTiposBV, false, false));
        indiceConversaoVendaVO.processarIndicadoresMapasMes();
        Integer bvtotal = indiceConversaoVendaVO.getQtdQuestionarioMes();
        obj.setValor(bvtotal.doubleValue());
        incluir(obj);

        obj.setIdentificador(IdentificadorDadosGerencialEnum.MATRICULAS.getSigla());
        Integer matriculas = indiceConversaoVendaVO.getQtdMatriculaMes();
        obj.setValor(matriculas.doubleValue());
        incluir(obj);

        obj.setIdentificador(IdentificadorDadosGerencialEnum.REMATRICULAS.getSigla());
        Integer rematriculas = indiceConversaoVendaVO.getQtdRematriculaMes();
        obj.setValor(rematriculas.doubleValue());
        incluir(obj);

        obj.setIdentificador(IdentificadorDadosGerencialEnum.ICV_TOTAL.getSigla());
        if (!bvtotal.equals(0)) {
            obj.setValor(Uteis.arredondarForcando2CasasDecimais(((rematriculas + matriculas) / bvtotal.doubleValue()) * 100));
        } else {
            obj.setValor(0.00);
        }
        incluir(obj);

        listaTiposBV = new ArrayList<>();
        listaTiposBV.add(TipoBVEnum.MA);
        indiceConversaoVendaVO.limparCampos();
        indiceConversaoVendaVO.setMapaQuestionarios(getFacade().getQuestionarioCliente().consultaMapaQuestionarioPorDataEmpresaPorColaborador(obj.getDatapesquisainicio(), obj.getDatapesquisafim(), "", obj.getEmpresa(), listaTiposBV, false));
        indiceConversaoVendaVO.setMapaGeralMatriculas(getFacade().getCliente().consultarClientesMapaICV(obj.getDatapesquisainicio(), obj.getDatapesquisafim(),
                        obj.getEmpresa(),  "MA", "", listaTipoContratos, listaTiposBV, false, false));
        indiceConversaoVendaVO.setMapaGeralRematriculas(getFacade().getCliente().consultarClientesMapaICV(obj.getDatapesquisainicio(), obj.getDatapesquisafim(),
                        obj.getEmpresa(),  "RE", "", listaTipoContratos, listaTiposBV, false, false));
        indiceConversaoVendaVO.processarIndicadoresMapasMes();

        obj.setIdentificador(IdentificadorDadosGerencialEnum.BV_MATRICULAS.getSigla());
        Integer bvMatricula = indiceConversaoVendaVO.getQtdQuestionarioMes();
        obj.setValor(bvMatricula.doubleValue());
        incluir(obj);
        
        
        obj.setIdentificador(IdentificadorDadosGerencialEnum.ICV_MATRICULAS.getSigla());
        if (!bvMatricula.equals(0)) {
            matriculas = indiceConversaoVendaVO.getQtdMatriculaMes();
            obj.setValor(Uteis.arredondarForcando2CasasDecimais((matriculas / bvMatricula.doubleValue()) * 100));
        } else {
            obj.setValor(0.00);
        }
        incluir(obj);

        listaTiposBV = new ArrayList<>();
        listaTiposBV.add(TipoBVEnum.RE);
        indiceConversaoVendaVO.limparCampos();
        indiceConversaoVendaVO.setMapaQuestionarios(getFacade().getQuestionarioCliente().consultaMapaQuestionarioPorDataEmpresaPorColaborador(obj.getDatapesquisainicio(), obj.getDatapesquisafim(), "", obj.getEmpresa(), listaTiposBV, false));
        indiceConversaoVendaVO.setMapaGeralMatriculas(getFacade().getCliente().consultarClientesMapaICV(obj.getDatapesquisainicio(), obj.getDatapesquisafim(),
                        obj.getEmpresa(),  "MA", "", listaTipoContratos, listaTiposBV, false, false));
        indiceConversaoVendaVO.setMapaGeralRematriculas(getFacade().getCliente().consultarClientesMapaICV(obj.getDatapesquisainicio(), obj.getDatapesquisafim(),
                        obj.getEmpresa(),  "RE", "", listaTipoContratos, listaTiposBV, false, false));
        indiceConversaoVendaVO.processarIndicadoresMapasMes();

        obj.setIdentificador(IdentificadorDadosGerencialEnum.BV_REMATRICULAS.getSigla());
        Integer bvRematricula = indiceConversaoVendaVO.getQtdQuestionarioMes();
        obj.setValor(bvRematricula.doubleValue());
        incluir(obj);

        

        obj.setIdentificador(IdentificadorDadosGerencialEnum.ICV_REMATRICULAS.getSigla());
        if (!bvRematricula.equals(0)) {
            rematriculas = indiceConversaoVendaVO.getQtdRematriculaMes();
            obj.setValor(Uteis.arredondarForcando2CasasDecimais((rematriculas / bvRematricula.doubleValue()) * 100));
        } else {
            obj.setValor(0.00);
        }
        incluir(obj);
        
        listaTiposBV = new ArrayList<>();
        listaTiposBV.add(TipoBVEnum.RT);
        indiceConversaoVendaVO.limparCampos();
        indiceConversaoVendaVO.setMapaQuestionarios(getFacade().getQuestionarioCliente().consultaMapaQuestionarioPorDataEmpresaPorColaborador(obj.getDatapesquisainicio(), obj.getDatapesquisafim(), "", obj.getEmpresa(), listaTiposBV, false));
        indiceConversaoVendaVO.setMapaGeralMatriculas(getFacade().getCliente().consultarClientesMapaICV(obj.getDatapesquisainicio(), obj.getDatapesquisafim(),
                        obj.getEmpresa(),  "MA", "", listaTipoContratos, listaTiposBV, false, false));
        indiceConversaoVendaVO.setMapaGeralRematriculas(getFacade().getCliente().consultarClientesMapaICV(obj.getDatapesquisainicio(), obj.getDatapesquisafim(),
                        obj.getEmpresa(),  "RE", "", listaTipoContratos, listaTiposBV, false, false));
        indiceConversaoVendaVO.processarIndicadoresMapasMes();
        
        obj.setIdentificador(IdentificadorDadosGerencialEnum.BV_RETORNOS.getSigla());
        Integer bvRetorno = indiceConversaoVendaVO.getQtdQuestionarioMes();
        obj.setValor(bvRetorno.doubleValue());
        incluir(obj);
        
        obj.setIdentificador(IdentificadorDadosGerencialEnum.RETORNO_MATRICULAS.getSigla());
        matriculas = indiceConversaoVendaVO.getQtdMatriculaMes();
        obj.setValor(matriculas.doubleValue());
        incluir(obj);

        obj.setIdentificador(IdentificadorDadosGerencialEnum.RETORNO_REMATRICULAS.getSigla());
        rematriculas = indiceConversaoVendaVO.getQtdRematriculaMes();
        obj.setValor(rematriculas.doubleValue());
        incluir(obj);
        
        obj.setIdentificador(IdentificadorDadosGerencialEnum.ICV_RETORNOS.getSigla());
        if (!bvRetorno.equals(0)) {
            obj.setValor(Uteis.arredondarForcando2CasasDecimais(((rematriculas + matriculas) / bvRetorno.doubleValue()) * 100));
        } else {
            obj.setValor(0.00);
        }
        incluir(obj);
        
        if(obj.getPeriodicidade().equals("MS")) {
            listaTiposBV = new ArrayList<>();
            listaTiposBV.add(TipoBVEnum.MA);
            listaTiposBV.add(TipoBVEnum.RE);
            listaTiposBV.add(TipoBVEnum.RT);
            obj.setIdentificador(IdentificadorDadosGerencialEnum.BV_IDEAL.getSigla());
            Date dataAnterior = Uteis.somarDias(obj.getDatapesquisainicio(), -1);
            Date datainicio = Uteis.obterPrimeiroDiaMes(dataAnterior);
            Date datafim = Uteis.obterUltimoDiaMesUltimaHora(datainicio);
            indiceConversaoVendaVO.limparCampos();
            indiceConversaoVendaVO.setMapaQuestionarios(getFacade().getQuestionarioCliente().consultaMapaQuestionarioPorDataEmpresaPorColaborador(datainicio, datafim, "", obj.getEmpresa(), listaTiposBV, false));
            indiceConversaoVendaVO.setMapaGeralMatriculas(getFacade().getCliente().consultarClientesMapaICV(datainicio, datafim,
                        obj.getEmpresa(),  "MA", "", listaTipoContratos, listaTiposBV, false, false));
            indiceConversaoVendaVO.setMapaGeralRematriculas(getFacade().getCliente().consultarClientesMapaICV(datainicio, datafim,
                        obj.getEmpresa(),  "RE", "", listaTipoContratos, listaTiposBV, false, false));
            indiceConversaoVendaVO.processarIndicadoresMapasMes();
            
            bvtotal = indiceConversaoVendaVO.getQtdQuestionarioMes();
            matriculas = indiceConversaoVendaVO.getQtdMatriculaMes();
            rematriculas = indiceConversaoVendaVO.getQtdRematriculaMes();
        
            Integer previsao = getFacade().getContrato().contarQtdeContratoPrevistoPeriodo(obj.getEmpresa(), obj.getDatapesquisainicio(), obj.getDatapesquisafim(), getListaColaborador(), false, null, null);
            Integer renovacaoRealizada = getFacade().getContrato().contarQtdeContratoPrevistoPeriodo(obj.getEmpresa(), obj.getDatapesquisainicio(), obj.getDatapesquisafim(), getListaColaborador(), true, null, null);
        
            Integer naoRenovados = previsao - renovacaoRealizada;
            if (!bvtotal.equals(0)) {
                Double indice = Uteis.arredondarForcando2CasasDecimais((rematriculas + matriculas / bvtotal.doubleValue()) * 100);
                if(indice > 0.0){
                    obj.setValor(Uteis.arredondarForcando2CasasDecimais((naoRenovados * 100)/ indice));
                } else {
                     obj.setValor(0.0);
                }
            } else {
                obj.setValor(0.0);
            }
            incluir(obj);
            
        }
    }
    private void gerarDadosRenovacaoUltimos60Dias(DadosGerencialPmgVO obj) throws Exception{
        Date inicioOriginal = obj.getDatapesquisainicio();
        Date fimOriginal = obj.getDatapesquisainicio();
        for(int e = 1; e < 70 ; e ++){
            obj.setDatapesquisafim(Uteis.obterUltimoDiaMes(Uteis.somarDias(fimOriginal, - (e))));
            obj.setDatapesquisainicio(Uteis.obterPrimeiroDiaMes(Uteis.somarDias(fimOriginal, - (e))));
            gravarDadosIndiceRenovacao(obj,Uteis.somarDias(fimOriginal, - (e)));
            incluir(obj);
        }
    }
    private void gravarDadosIndiceRenovacao(DadosGerencialPmgVO obj) throws Exception{
        gravarDadosIndiceRenovacao(obj,null);
    }
    private void gravarDadosIndiceRenovacao(DadosGerencialPmgVO obj,Date diaReferencia) throws Exception {
        List<ClienteVO> listaAuxiliar;
        //Caso contenha dia referencia será consultado a renovação pelo dia do Mês
        Date inicioPesquisa = obj.getDatapesquisainicio();
        Date fimPesquisa = obj.getDatapesquisafim();
        Boolean realizadoMes = true;

        Integer renovacaoMesPassado =  0;

        if(diaReferencia != null){
            obj.setDatapesquisainicio(diaReferencia);
            obj.setDatapesquisafim(diaReferencia);
            realizadoMes = false;
            if(diaReferencia.equals(Calendario.getDataComHoraZerada(Uteis.obterPrimeiroDiaMes(diaReferencia)))){
                renovacaoMesPassado = getFacade().getContrato().contarQtdeContratoPrevistoPeriodo(obj.getEmpresa(), inicioPesquisa,fimPesquisa,diaReferencia, getListaColaborador(), true, null, null,true);
            }

        } else {
            diaReferencia = fimPesquisa;
        }


        obj.setIdentificador(IdentificadorDadosGerencialEnum.RENOVACAO_PREVISAO.getSigla());
        Integer previsao = getFacade().getContrato().contarQtdeContratoPrevistoPeriodo(obj.getEmpresa(), inicioPesquisa,fimPesquisa,diaReferencia, getListaColaborador(), false, null, null,false);
        obj.setValor(previsao.doubleValue());
        incluir(obj);

        obj.setIdentificador(IdentificadorDadosGerencialEnum.RENOVACAO_REALIZADA.getSigla());
        Integer renovacaoRealizada = getFacade().getContrato().contarQtdeContratoPrevistoPeriodo(obj.getEmpresa(), inicioPesquisa,fimPesquisa,diaReferencia, getListaColaborador(), true, null, null,realizadoMes);
        renovacaoRealizada += renovacaoMesPassado;
        obj.setValor(renovacaoRealizada.doubleValue());
        incluir(obj);

        obj.setIdentificador(IdentificadorDadosGerencialEnum.RENOVACAO_NAOREALIZADA.getSigla());
        Integer renovacaoNaoRealizada = previsao - renovacaoRealizada;
        obj.setValor(renovacaoNaoRealizada.doubleValue());
        incluir(obj);
        
        obj.setIdentificador(IdentificadorDadosGerencialEnum.RISCO_ALTO.getSigla());
        Integer risco = getFacade().getRisco().consultarNumeroRiscoAlto(obj.getEmpresa());
        obj.setValor(risco.doubleValue());
        incluir(obj);

        obj.setIdentificador(IdentificadorDadosGerencialEnum.RENOVACAO_INDICE.getSigla());
        if(!previsao.equals(0)){
            obj.setValor(Uteis.arredondarForcando2CasasDecimais((renovacaoRealizada.doubleValue() * 100) / previsao.doubleValue()));
        } else {
            obj.setValor(0.0);
        }
        incluir(obj);

        obj.setIdentificador(IdentificadorDadosGerencialEnum.RENOVACAO_CORRESP.getSigla());
        Integer contratosAtivos = getFacade().getContrato().contar(
                String.format(Contrato.sqlAtivosContar,
                        new Object[]{
                                (" and c.empresa = " + obj.getEmpresa()),
                            obj.getDatapesquisainicio(),
                            obj.getDatapesquisainicio(),
                            obj.getDatapesquisainicio(),
                            obj.getDatapesquisainicio(), obj.getDatapesquisainicio(),
                            obj.getDatapesquisainicio()
                        }));
        if(contratosAtivos > 0){
            obj.setValor(Uteis.arredondarForcando2CasasDecimais((previsao.doubleValue() * 100) / contratosAtivos));
        } else {
            obj.setValor(0.0);
        }
        incluir(obj);
    }

    public void gerarDadosCRM(String chave, DadosGerencialPmgVO obj) throws Exception {
        BusinessIntelligenceCRMControle biCRM = new BusinessIntelligenceCRMControle();
        biCRM.totalizarBIsVendasFidelizacaoStudioCRMExtra(obj.getDatapesquisainicio(), obj.getDatapesquisafim(),obj.getEmpresa(), new ArrayList<>());
        Map<String, IdentificadorDadosGerencialEnum> mapaIndicadoresCRM = mapaIndicadoresCRM();
        for(FecharMetaVO ft : biCRM.getTotaisResultado()){
            FasesCRMEnum fase = FasesCRMEnum.getFasePorSigla(ft.getIdentificadorMeta());
            if (fase == null) {
                continue;
            }
            IdentificadorDadosGerencialEnum meta = mapaIndicadoresCRM.get(fase.name() + "meta");
            IdentificadorDadosGerencialEnum resultado = mapaIndicadoresCRM.get(fase.name() + "resultado");
            if (meta == null || resultado == null) {
                continue;
            }
            obj.setIdentificador(meta.getSigla());
            obj.setValor(Uteis.arredondarForcando2CasasDecimais(ft.getMeta()));
            incluir(obj);

            obj.setIdentificador(resultado.getSigla());
            obj.setValor(Uteis.arredondarForcando2CasasDecimais(ft.getMetaAtingida()));
            incluir(obj);
        }
         gerarNumeroContatosCRM(obj);
    }
    private void gerarNumeroContatosCRM(final DadosGerencialPmgVO obj) throws Exception{
        Date dataInicioOriginal = obj.getDatapesquisainicio();
        Date dataFimOriginal = obj.getDatapesquisafim();

        obj.setIdentificador(IdentificadorDadosGerencialEnum.META_CONTATOS_CRM.getSigla());
        Double countContatoCliente  = getFacade().getFecharMeta().obterSomaMeta(obj.getDatapesquisainicio(),obj.getDatapesquisafim(),obj.getEmpresa());
        obj.setValor(countContatoCliente);
        incluir(obj);

        obj.setIdentificador(IdentificadorDadosGerencialEnum.CONTATOS_CRM.getSigla());
        countContatoCliente  = getFacade().getFecharMeta().obterMetaAlcancado(obj.getDatapesquisainicio(),obj.getDatapesquisafim(),obj.getEmpresa());
        obj.setValor(countContatoCliente);
        incluir(obj);

        Date inicio  = Uteis.somarMeses(obj.getDatapesquisainicio(),-1);
        Date fim = Uteis.obterUltimoDiaMes(inicio);
        obj.setIdentificador(IdentificadorDadosGerencialEnum.CONTATOS_CRM.getSigla());
        obj.setDatapesquisainicio(inicio);
        obj.setDatapesquisafim(fim);
        countContatoCliente  = getFacade().getFecharMeta().obterMetaAlcancado(inicio,fim,obj.getEmpresa());
        obj.setValor(countContatoCliente);
        incluir(obj);

        obj.setDatapesquisainicio(dataInicioOriginal);
        obj.setDatapesquisafim(dataFimOriginal);
    }

    private void gravarDadosQuantidadePlanos(DadosGerencialPmgVO obj) throws Exception {
        obj.setIdentificador(IdentificadorDadosGerencialEnum.PLANOS_VENDIDOS.getSigla());
        Double faturados = contar(
                String.format(sqlContratoFaturadosContar,
                        new Object[]{
                            Uteis.getDataJDBCTimestamp(obj.getDatapesquisainicio()),
                            Uteis.getDataJDBCTimestamp(obj.getDatapesquisafim()),
                            obj.getEmpresa()
                        }), con).doubleValue();
        obj.setValor(faturados);
        incluir(obj);

        Double mensais = 0.0;
        Double percMensal = 0.0;
        Double percLonga = 0.0;
        if (faturados > 0) {
            mensais = contar(
                    String.format(sqlContratoFaturadosMensalContar,
                            new Object[]{
                                Uteis.getDataJDBCTimestamp(obj.getDatapesquisainicio()),
                                Uteis.getDataJDBCTimestamp(obj.getDatapesquisafim()),
                                obj.getEmpresa()
                            }), con).doubleValue();
            if (mensais > 0) {
                percMensal = Uteis.arredondarForcando2CasasDecimais((mensais * 100) / faturados);
            }
            percLonga = Uteis.arredondarForcando2CasasDecimais(100.0 - percMensal);
        }

        obj.setIdentificador(IdentificadorDadosGerencialEnum.PLANOS_MENSAIS.getSigla());
        obj.setValor(percMensal);
        incluir(obj);

        obj.setIdentificador(IdentificadorDadosGerencialEnum.PLANOS_LONGADURACAO.getSigla());
        obj.setValor(percLonga);
        incluir(obj);
    }

    private void gravarDadosPendencias(DadosGerencialPmgVO obj) throws Exception {
        Integer quantidade = 0;
        Double total = 0.0;

        obj.setIdentificador(IdentificadorDadosGerencialEnum.CLIENTES_PARCATRASO.getSigla());
        ResultSet rs = getFacade().getMovParcela().contarPendenciaParcelaEmAbertoAtraso(obj.getEmpresa(), obj.getDatapesquisafim(), "");
        if (rs.next()) {
            quantidade = rs.getInt("qtd");
            total = rs.getDouble("total");
        }
        obj.setValor(quantidade.doubleValue());
        incluir(obj);

        obj.setIdentificador(IdentificadorDadosGerencialEnum.VALOR_PARCATRASO.getSigla());
        obj.setValor(total);
        incluir(obj);

        obj.setIdentificador(IdentificadorDadosGerencialEnum.CLIENTES_PARCEMABERTO.getSigla());
        quantidade = 0;
        total = 0.0;
        rs = getFacade().getMovParcela().contarPendenciaParcelaEmAbertoAPagar(obj.getEmpresa(),Calendario.hoje(), "",false,false,true, null);
        if (rs.next()) {
            quantidade = rs.getInt("qtd");
            total = rs.getDouble("total");
        }
        obj.setValor(quantidade.doubleValue());
        incluir(obj);

        obj.setIdentificador(IdentificadorDadosGerencialEnum.VALOR_PARCEMABERTO.getSigla());
        obj.setValor(total);
        incluir(obj);

        obj.setIdentificador(IdentificadorDadosGerencialEnum.CLIENTES_DEBITOCC.getSigla());
        quantidade = 0;
        rs = getFacade().getMovimentoContaCorrenteCliente().contarPendenciasClienteDevendoContaCorrente(obj.getEmpresa(), "");
        if (rs.next()) {
            quantidade = rs.getInt("qtd");
        }
        obj.setValor(quantidade.doubleValue());
        incluir(obj);
    }

    private void gerarDadosFaturamentoRecebidoUltimos60Dias(DadosGerencialPmgVO obj) throws Exception{
        Date inicioOriginal = obj.getDatapesquisainicio();
        Date fimOriginal = obj.getDatapesquisafim();
        for(int e = 1; e < 60 ; e ++){
            obj.setDatapesquisafim(Uteis.somarDias(fimOriginal, - (e)));
            obj.setDatapesquisainicio(Uteis.somarDias(fimOriginal, - (e)));
            Double valorCalculado = 0.0;
            EmpresaVO empresa = new EmpresaVO();
            empresa.setCodigo(obj.getEmpresa());
            FaturamentoSinteticoControleRel faturamento = new FaturamentoSinteticoControleRel();
            faturamento.setEmpresa(empresa);
            faturamento.getFaturamentoSinteticoRel().setDataInicio(obj.getDatapesquisainicio());
            faturamento.getFaturamentoSinteticoRel().setDataTermino(obj.getDatapesquisafim());
            faturamento.setAgrupamento("");
            faturamento.setMesReferenciaPlano(false);
            obj.setIdentificador(IdentificadorDadosGerencialEnum.FATURAMENTO_RECEBIDO.getSigla());
            valorCalculado =  0.0;
            faturamento.setSomenteFaturamentoRecebido(true);
            faturamento.imprimir(false, obj.getEmpresa());
            if (!faturamento.getResumo().getListaProduto().isEmpty()
                    && !faturamento.getResumo().getListaProduto().get(0).getListaProdutoXMes().isEmpty()) {
                valorCalculado = Uteis.arredondarForcando2CasasDecimais(faturamento.getResumo().getListaProduto().get(0).getListaProdutoXMes().get(0).getValor());
            }
            obj.setValor(valorCalculado);
            incluir(obj);
        }
        obj.setDatapesquisainicio(inicioOriginal);
        obj.setDatapesquisafim(fimOriginal);
    }

    private void gravarDadosFinanceiros(DadosGerencialPmgVO obj) throws Exception {
        Double valorCalculado = 0.0;
        obj.setIdentificador(IdentificadorDadosGerencialEnum.FATURAMENTO_META.getSigla());
        Calendar dataCalendar = Calendario.getInstance();
        dataCalendar.setTime(obj.getDatapesquisainicio());
        List<MetaFinanceiraEmpresaVO> listaMetas = getFacade().getMetaFinanceiraEmpresa().consultarPorEmpresaAnoMesDescricao(obj.getEmpresa(),
                dataCalendar.get(Calendar.YEAR), (dataCalendar.get(Calendar.MONTH) + 1), "", Uteis.NIVELMONTARDADOS_TODOS);
        if (!listaMetas.isEmpty()) {
            boolean todosVazios = true;
            for (MetaFinanceiraEmpresaVO mfeVO : listaMetas) {
                todosVazios = todosVazios && mfeVO.getValores().isEmpty();
            }
            if (!todosVazios) {
                //obtenho a primeira posição da lista porque com os filtros usados não irá existir mais de um resultado para a consulta
                valorCalculado = listaMetas.get(0).getValores().get(0).getValor();
            }
        }
        obj.setValor(valorCalculado);
        incluir(obj);
        
        EmpresaVO empresa = new EmpresaVO();
        empresa.setCodigo(obj.getEmpresa());
        FaturamentoSinteticoControleRel faturamento = new FaturamentoSinteticoControleRel();
        faturamento.setEmpresa(empresa);
        faturamento.getFaturamentoSinteticoRel().setDataInicio(obj.getDatapesquisainicio());
        faturamento.getFaturamentoSinteticoRel().setDataTermino(obj.getDatapesquisafim());
        faturamento.setAgrupamento("");
        faturamento.setMesReferenciaPlano(false);
       

        obj.setIdentificador(IdentificadorDadosGerencialEnum.FATURAMENTO_ATINGIDO.getSigla());
        valorCalculado =  0.0;
        faturamento.setSomenteFaturamentoRecebido(false);
        faturamento.imprimir(false, obj.getEmpresa());
        if (!faturamento.getResumo().getListaProduto().isEmpty()
                    && !faturamento.getResumo().getListaProduto().get(0).getListaProdutoXMes().isEmpty()) {
            valorCalculado = Uteis.arredondarForcando2CasasDecimais(faturamento.getResumo().getListaProduto().get(0).getListaProdutoXMes().get(0).getValor());
         }
        obj.setValor(valorCalculado);
        incluir(obj);

        obj.setIdentificador(IdentificadorDadosGerencialEnum.FATURAMENTO_RECEBIDO.getSigla());
        valorCalculado =  0.0;
        faturamento.setSomenteFaturamentoRecebido(true);
        faturamento.imprimir();
        if (!faturamento.getResumo().getListaProduto().isEmpty()
                    && !faturamento.getResumo().getListaProduto().get(0).getListaProdutoXMes().isEmpty()) {
            valorCalculado = Uteis.arredondarForcando2CasasDecimais(faturamento.getResumo().getListaProduto().get(0).getListaProdutoXMes().get(0).getValor());
         }
        obj.setValor(valorCalculado);
        incluir(obj);

        TicketMedio ticketDao = new TicketMedio(con);

        ConfiguracaoBI configuracaoBI = new ConfiguracaoBI(con);
        List<ConfiguracaoBIVO> configuracoes = configuracaoBI.consultarPorBI(BIEnum.TICKET_MEDIO, empresa.getCodigo());
        configuracoes.removeIf(c -> c.getConfiguracao().equals(ConfiguracaoBIEnum.INCLUIR_PRODUTOS_RECEITA));
        
        TicketMedioVO ticket = ticketDao.montarTicketMedio(true, configuracoes, empresa.getCodigo(), obj.getDatapesquisafim());
        
        obj.setIdentificador(IdentificadorDadosGerencialEnum.RECEITA_PERIODO.getSigla());
        obj.setValor(ticket.getCaixaReceita());
        incluir(obj);
        
        obj.setIdentificador(IdentificadorDadosGerencialEnum.RECEITA_ALUNO.getSigla());
        Integer alunos = ((ticket.getAtivosVencidosInicioMes() + ticket.getAtivosVencidosFimMes()) / 2) - ticket.getBolsas();
        if(alunos > 0) {
            obj.setValor(Uteis.arredondarForcando2CasasDecimais(ticket.getCaixaReceita() / alunos));
        }else {
            obj.setValor(Uteis.arredondarForcando2CasasDecimais(ticket.getCaixaReceita()));
        }    
        incluir(obj);
        
        obj.setIdentificador(IdentificadorDadosGerencialEnum.DESPESAS_TOTAIS.getSigla());
        ResultSet rs; 
        rs = SuperFacadeJDBC.criarConsulta(String.format(sqlDespesasSomar,
                        new Object[]{
                            Uteis.getDataJDBCTimestamp(obj.getDatapesquisainicio()),
                            Uteis.getDataJDBCTimestamp(Uteis.obterUltimoDiaMesUltimaHora(obj.getDatapesquisafim())),
                            obj.getEmpresa()
                        }), con);
        rs.next();
        Double despesas = rs.getDouble("valor");
        obj.setValor(despesas);
        incluir(obj);
        
        obj.setIdentificador(IdentificadorDadosGerencialEnum.DESPESAS_ALUNO.getSigla());
        if(alunos > 0) {
            obj.setValor(Uteis.arredondarForcando2CasasDecimais(despesas / alunos));
        } else  {
            obj.setValor(despesas);
        }
        incluir(obj);

        //---------------DRE
        String key = DAO.resolveKeyFromConnection(Conexao.getConexaoForJ2SE());
        if(UteisValidacao.emptyString(key)){
            key = DAO.resolveKeyFromConnection(con);
        }
        gerarDadosDRE(key,obj,TipoRelatorioDF.COMPETENCIA,IdentificadorDadosGerencialEnum.DRE_COMPETENCIA);
        gerarDadosDRE(key,obj,TipoRelatorioDF.COMPETENCIA_QUITADA,IdentificadorDadosGerencialEnum.DRE_COMPETENCIA_QUITADA);
        gerarDadosDRE(key,obj,TipoRelatorioDF.COMPETENCIA_NAO_QUITADA,IdentificadorDadosGerencialEnum.DRE_COMPETENCIA_NAO_QUITADA);
        gerarDadosDRE(key,obj,TipoRelatorioDF.RECEITA,IdentificadorDadosGerencialEnum.DRE_RECEITA);
        gerarDadosDRE(key,obj,TipoRelatorioDF.FATURAMENTO, IdentificadorDadosGerencialEnum.DRE_FATURAMENTO);
        gerarDadosDRE(key,obj,TipoRelatorioDF.FATURAMENTO_DE_CAIXA,IdentificadorDadosGerencialEnum.DRE_FATURAMENTO_DE_CAIXA);
        //---------------Novas consutlas Monitoramento Game Of Results
        if(!empresaJaFoiInseridaHoje(obj))
            gerarDadosMonitoramentoGameOfResults(obj);
    }

    public void gerarDadosDRE(String chave,DadosGerencialPmgVO obj,TipoRelatorioDF tipoRelatorioDF,IdentificadorDadosGerencialEnum identificador) throws Exception{
        RelatorioDRE relatorioDRE = new RelatorioDRE();
        List<Integer> listaFiltroCentroCusto = new ArrayList<>();
        Connection con = new DAO().obterConexaoEspecifica(chave);
        DemonstrativoFinanceiroJSON resultadoQuitada = relatorioDRE.obterResultadoJSON(relatorioDRE.gerarDREFechandoConexao(con,tipoRelatorioDF, Calendario.getInstance(obj.getDatapesquisainicio()),
                Calendario.getInstance(obj.getDatapesquisafim()), obj.getEmpresa(), listaFiltroCentroCusto,true, TipoFonteDadosDF.TODAS, false, false));
        obj.setIdentificador(identificador.getSigla());
        obj.setValor(Uteis.arredondarForcando2CasasDecimais(resultadoQuitada.getResultadoExercicio()));
        incluir(obj);
    }


    public Integer contar(String sql) throws SQLException, Exception {
        ResultSet tabelaResultado = SuperFacadeJDBC.criarConsulta(sql, con);
        if (tabelaResultado.next()) {
            return tabelaResultado.getInt(1);
        } else {
            return 0;
        }

    }

    private boolean empresaJaFoiInseridaHoje(DadosGerencialPmgVO obj) {
        try {
            StringBuilder query = new StringBuilder("select * from monitoramento where dia::date = (now() - interval '1 day')::date and chaveecodigo = '");
            query.append(obj.getChave()).append("-").append(obj.getEmpresa()).append("'");
            return SuperFacadeJDBC.criarConsulta(query.toString(), con).next();
        } catch (Exception e) {
            return false;
        }
    }

    private void gerarDadosMonitoramentoGameOfResults(DadosGerencialPmgVO dadosGerencialPmgVO) {
        try {
            Date dataDados = Calendario.somarDias(dadosGerencialPmgVO.getDatageracao(), -1);
            Monitoramento monitoramentoDao = new Monitoramento(con);
            MonitoramentoVO monitoramentoVO = monitoramentoDao.gerarDadosMonitoramento(dadosGerencialPmgVO.getChave(), dadosGerencialPmgVO.getEmpresa(), dadosGerencialPmgVO.getNomeEmpresa(), dataDados);
            monitoramentoDao.incluir(monitoramentoVO);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    public List<ColaboradorVO> getListaColaborador() {
        return listaColaborador;
    }

    public void setListaColaborador(List<ColaboradorVO> listaColaborador) {
        this.listaColaborador = listaColaborador;
    }


    public static void main(String... args) {
        try {
            String chave = args.length > 0 ? args[0] : "greco";
            Connection c = new DAO().obterConexaoEspecifica(chave);
//          Connection c = DriverManager.getConnection("*****************************************************************", "zillyonweb", "pactodb");
            Integer empresa = (args.length > 1 ? Integer.parseInt(args[1]) : 0);
            DadosGerencialPmg pmg = new DadosGerencialPmg(c);
            Conexao.guardarConexaoForJ2SE(c);
            pmg.gerarDadosPMG(empresa, chave,Calendario.hoje(), null);
//            pmg.gerarDadosPMG(empresa, chave,Uteis.somarDias(Calendario.hoje(), -1));
//            pmg.gerarDadosCargaIndicadoresDiariosRenovacao(chave);
        } catch (Exception ex) {
            Logger.getLogger(DadosGerencialPmg.class.getName()).log(Level.SEVERE, null, ex);
        }
    }


    private void gravarDadosltv(DadosGerencialPmgVO obj) throws Exception {
        LtvControle control = new LtvControle();
        EmpresaVO empresa = FacadeManager.getFacade().getEmpresa().consultarPorChavePrimaria(obj.getEmpresa(), Uteis.NIVELMONTARDADOS_DADOSBASICOS);
        control.setEmpresaFiltro(empresa);
        control.setInicio(Uteis.obterPrimeiroDiaMes(obj.getDatapesquisainicio()));
        control.setFim(Uteis.obterUltimoDiaMes(obj.getDatapesquisafim()));
        JSONObject jsonObject = control.buscarDadosBIMS(true, obj.getChave(), false, true);

        obj.setIdentificador(IdentificadorDadosGerencialEnum.LTV_CAC.getSigla());
        obj.setValor(jsonObject.getDouble("cac"));
        incluir(obj);

        obj.setIdentificador(IdentificadorDadosGerencialEnum.LTV_LTV.getSigla());
        obj.setValor(jsonObject.getDouble("ltv"));
        incluir(obj);

        obj.setIdentificador(IdentificadorDadosGerencialEnum.LTV_CHURN.getSigla());
        obj.setValor(jsonObject.getDouble("churn"));
        incluir(obj);

        obj.setIdentificador(IdentificadorDadosGerencialEnum.LTV_TEMPO_MEDIO.getSigla());
        obj.setValor((double) jsonObject.getInt("tempoDuracaoMedioDosContratos"));
        incluir(obj);
    }

    private void gravarDadosAdm(DadosGerencialPmgVO obj) throws Exception {
        ConsultaClienteControle control = new ConsultaClienteControle();
        EmpresaVO empresaVO = FacadeManager.getFacade().getEmpresa().consultarPorChavePrimaria(obj.getEmpresa(), Uteis.NIVELMONTARDADOS_DADOSBASICOS);
        control.setConsultaSituacao(SituacaoClienteEnum.ATIVO.getCodigo());
        control.setEmpresa(empresaVO);
        control.setConsultaEmpresa(empresaVO.getCodigo());
        control.getConfPaginacao().setItensPorPagina(1);
        control.consultarPaginado();

        obj.setIdentificador(IdentificadorDadosGerencialEnum.CLIENTES_ATIVOS.getSigla());
        obj.setValor((double) control.getConfPaginacao().getNumeroTotalItens());
        incluir(obj);
    }

    private void gravarDadosFinanceirosRecebiveis(DadosGerencialPmgVO obj) throws Exception {
        GestaoRecebiveisControle control = new GestaoRecebiveisControle();
        EmpresaVO empresaVO = FacadeManager.getFacade().getEmpresa().consultarPorChavePrimaria(obj.getEmpresa(), Uteis.NIVELMONTARDADOS_DADOSBASICOS);
        control.setEmpresa(empresaVO);
        control.setEmpresaRel(empresaVO);

        //faturamento
        control.limparPeriodoC();
        control.setDataInicialLancamento(obj.getDatapesquisainicio());
        control.setDataFinalLancamento(obj.getDatapesquisafim());
        control.consultarRecebiveis();

        obj.setIdentificador(IdentificadorDadosGerencialEnum.RECEBIVEIS_FATURAMENTO_ESPECIE.getSigla());
        obj.setValor(control.getTotalEspecie());
        incluir(obj);

        obj.setIdentificador(IdentificadorDadosGerencialEnum.RECEBIVEIS_FATURAMENTO_BOLETO.getSigla());
        obj.setValor(control.getTotalBoleto());
        incluir(obj);

        obj.setIdentificador(IdentificadorDadosGerencialEnum.RECEBIVEIS_FATURAMENTO_DEVOLUCAO.getSigla());
        obj.setValor(control.getTotalDevolucoes());
        incluir(obj);

        obj.setIdentificador(IdentificadorDadosGerencialEnum.RECEBIVEIS_FATURAMENTO_CONTA_CORRENTE.getSigla());
        obj.setValor(control.getTotalNaoFisicamente());
        incluir(obj);

        obj.setIdentificador(IdentificadorDadosGerencialEnum.RECEBIVEIS_FATURAMENTO_TOTAL.getSigla());
        obj.setValor(control.getTotalTotal());
        incluir(obj);


        //receita
        control.limparPeriodoL();
        control.setDataInicialCompensacao(obj.getDatapesquisainicio());
        control.setDataFinalCompensacao(obj.getDatapesquisafim());
        control.consultarRecebiveis();

        obj.setIdentificador(IdentificadorDadosGerencialEnum.RECEBIVEIS_RECEITA_ESPECIE.getSigla());
        obj.setValor(control.getTotalEspecie());
        incluir(obj);

        obj.setIdentificador(IdentificadorDadosGerencialEnum.RECEBIVEIS_RECEITA_BOLETO.getSigla());
        obj.setValor(control.getTotalBoleto());
        incluir(obj);

        obj.setIdentificador(IdentificadorDadosGerencialEnum.RECEBIVEIS_RECEITA_DEVOLUCAO.getSigla());
        obj.setValor(control.getTotalDevolucoes());
        incluir(obj);

        obj.setIdentificador(IdentificadorDadosGerencialEnum.RECEBIVEIS_RECEITA_CONTA_CORRENTE.getSigla());
        obj.setValor(control.getTotalNaoFisicamente());
        incluir(obj);

        obj.setIdentificador(IdentificadorDadosGerencialEnum.RECEBIVEIS_RECEITA_TOTAL.getSigla());
        obj.setValor(control.getTotalTotal());
        incluir(obj);

    }

    private void executarCallableDadosPMG(List<DadosGerenciaisPmgCallable> callableTasks){
        try {
            ExecutorService executorService = Executors.newFixedThreadPool(callableTasks.size());
            executorService.invokeAll(callableTasks);
            executorService.shutdown();
            executorService = null;
        } catch (Exception ex) {
            ex.printStackTrace();
            Uteis.logarDebug("ERRO: Dados do PMG  | " + ex.getMessage());
        }
    }

}

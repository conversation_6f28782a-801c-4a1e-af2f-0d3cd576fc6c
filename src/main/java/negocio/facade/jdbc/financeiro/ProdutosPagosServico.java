/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */

package negocio.facade.jdbc.financeiro;

import br.com.pactosolucoes.atualizadb.processo.CorrigirMovParcela;
import br.com.pactosolucoes.comuns.util.JSFUtilities;
import br.com.pactosolucoes.oamd.controle.basico.DAO;
import java.sql.Connection;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;

import controle.basico.ConfiguracaoSistemaControle;
import negocio.comuns.financeiro.MovPagamentoVO;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisValidacao;
import negocio.facade.jdbc.arquitetura.SuperFacadeJDBC;
import negocio.facade.jdbc.utilitarias.Conexao;
import org.eclipse.collections.impl.map.mutable.primitive.DoubleObjectHashMap;

/**
 *
 * <AUTHOR>
 */
public class ProdutosPagosServico {

    private static MovPagamentoVO obterDadosPagamento(Connection con, Integer codigoPagamento, String tipoForma, Map<Integer, MovPagamentoVO> mapaPagamentos) throws SQLException {

        if(!mapaPagamentos.containsKey(codigoPagamento)){
            MovPagamentoVO novo = new MovPagamentoVO();
            novo.setCodigo(codigoPagamento);
            String consultaValores = "";

            if(tipoForma.equals("CA") ){
                consultaValores = "select valor,0 as valortotal, situacao from cartaocredito where movpagamento = "+codigoPagamento;
            } else if (tipoForma.equals("CH")){
                consultaValores = "select valor,0 as valortotal, situacao from cheque where movpagamento = "+codigoPagamento;
            } else {
                consultaValores = "select valor,valortotal, 'NAORECEBIVEL' as situacao from movpagamento where codigo = "+codigoPagamento;
            }
            try (ResultSet valores = con.prepareStatement(consultaValores).executeQuery()) {
                while (valores.next()) {
                    if (valores.getString("situacao").equals("CA")) {
                        novo.setValorPPCancelado(Uteis.arredondarForcando2CasasDecimais(novo.getValorPPCancelado() + valores.getDouble("valor")));
                    } else if (valores.getString("situacao").equals("EA")) {
                        novo.setValorPP(Uteis.arredondarForcando2CasasDecimais(novo.getValorPP() + valores.getDouble("valor")));
                    } else {
                        novo.setValorPPCancelado(Uteis.arredondarForcando2CasasDecimais(novo.getValorPPCancelado() + valores.getDouble("valortotal")));
                        novo.setValorPP(Uteis.arredondarForcando2CasasDecimais(novo.getValorPP() + valores.getDouble("valor")));
                    }
                }
            }
            mapaPagamentos.put(codigoPagamento, novo);

        }

        return mapaPagamentos.get(codigoPagamento);
    }

    public static class Parcela{
        int pagamentoParcela;
        int parcela;
        List<ProdutoParcela> produtos = new ArrayList<>();
        List<ProdutoParcela> produtosCanceladosPagamentoCancelados = new ArrayList<>();
        List<ProdutoParcela> produtosCanceladosPagamentoNaoCancelados= new ArrayList<>();

        public Parcela(int pmp, int parc){
            pagamentoParcela = pmp;
            parcela = parc;
        }
    }

    public static class ProdutoParcela{
        int produto;
        String tipo;
        double valorPago;
        double valorUsado = 0.0;
        int parcela;
        int contrato;
        String situacao;
        boolean pagamentoDebito= false;

        public ProdutoParcela(int prod, double pago, int parc, int cont, String t,String sit, boolean pgDebito){
            valorPago = pago;
            valorUsado = pago;
            produto = prod;
            parcela = parc;
            contrato = cont;
            tipo = t;
            situacao = sit;
            pagamentoDebito = pgDebito;
        }
    }

    public static void setarProdutosPagos(Connection con, Integer codigoRecibo){
        try {
            List<ProdutoParcela> produtosParcela = new ArrayList<>();
            Map<Integer, List<Integer>> mapaProdParcelas = new HashMap<>();
            List<ProdutoParcela> produtosParcelaCancelados = new ArrayList<>();
            ResultSet produtos = con.prepareStatement("SELECT mpp.movparcela, mpp.movproduto, mpp.valorpago, (p.tipoproduto = 'PM') AS produtoplano,p.tipoproduto, mp.contrato,mp.situacao, mp.movpagamentocc  FROM movprodutoparcela mpp "+
                    " INNER JOIN movproduto mp ON mpp.movproduto = mp.codigo "+
                    " INNER JOIN produto p ON p.codigo = mp.produto"+
                    " where recibopagamento  = " + codigoRecibo + " order by produtoplano, movparcela ,movproduto ").executeQuery();
            while(produtos.next()){
                if(produtos.getString("situacao").equals("CA") || produtos.getString("tipoproduto").equals("CC") || !UteisValidacao.emptyString(produtos.getString("movpagamentocc"))){
                    produtosParcelaCancelados.add(new ProdutoParcela(produtos.getInt("movproduto"),
                            produtos.getDouble("valorpago"), produtos.getInt("movparcela"), produtos.getInt("contrato"), produtos.getString("tipoproduto"), produtos.getString("situacao"), !UteisValidacao.emptyString(produtos.getString("movpagamentocc"))));
                } else {
                    produtosParcela.add(new ProdutoParcela(produtos.getInt("movproduto"),
                            produtos.getDouble("valorpago"), produtos.getInt("movparcela"), produtos.getInt("contrato"), produtos.getString("tipoproduto"), produtos.getString("situacao"), !UteisValidacao.emptyString(produtos.getString("movpagamentocc"))));
                }
                if(!produtos.getString("tipoproduto").equals("PM")){
                    if(!mapaProdParcelas.containsKey(produtos.getInt("movproduto"))){
                        mapaProdParcelas.put(produtos.getInt("movproduto"),new ArrayList<>());
                    }
                    if (!mapaProdParcelas.get(produtos.getInt("movproduto")).contains(produtos.getInt("movparcela"))) {
                        mapaProdParcelas.get(produtos.getInt("movproduto")).add(produtos.getInt("movparcela"));
                    }
                }
            }
            produtosParcela.addAll(produtosParcelaCancelados);

            Map<Integer, List<Parcela>> mapa = new HashMap<>();
            Map<Integer, MovPagamentoVO> mapaPagamentos = new HashMap<>();
            //obter as parcelas
            ResultSet rs = con.prepareStatement(" SELECT pmp.codigo, pmp.valorpago, pmp.movparcela, mp.descricao, mp.contrato, pmp.movpagamento,fp.tipoformapagamento "
                    + " from pagamentomovparcela pmp "
                    + " INNER JOIN movparcela mp ON pmp.movparcela = mp.codigo "
                    + " inner join movpagamento mov on mov.codigo = pmp.movpagamento "
                    + " inner join formapagamento fp on mov.formapagamento = fp.codigo "
                    + " WHERE pmp.recibopagamento = " + codigoRecibo + " order by pmp.codigo ").executeQuery();
            while (rs.next()) {
                Parcela parcela = new Parcela(rs.getInt("codigo"), rs.getInt("movparcela"));
                double valorPagoMPP = Uteis.arredondarForcando2CasasDecimais(rs.getDouble("valorpago"));
                MovPagamentoVO pagamento = obterDadosPagamento(con, rs.getInt("movpagamento"), rs.getString("tipoformapagamento"),mapaPagamentos);

                for(ProdutoParcela proPa : produtosParcela){
                    if(proPa.parcela == parcela.parcela && Uteis.arredondarForcando2CasasDecimais(proPa.valorUsado) > 0.0){
                        Double valorNaParcela = 0.0;
                        Double valorRetirar =0.0;
                        Double valorProdutoCanceladoPagamentoNao = 0.0;
                        if((proPa.situacao.equals("CA") || proPa.tipo.equals("CC") || proPa.pagamentoDebito)){
                            if(pagamento.getValorPPCancelado() >=  Uteis.arredondarForcando2CasasDecimais(proPa.valorUsado)){
                                valorRetirar = Uteis.arredondarForcando2CasasDecimais(proPa.valorUsado);
                            } else {
                                valorProdutoCanceladoPagamentoNao = Uteis.arredondarForcando2CasasDecimais(proPa.valorUsado - pagamento.getValorPPCancelado());
                                valorRetirar = pagamento.getValorPPCancelado();

                            }
                        } else  if(!proPa.situacao.equals("CA") && pagamento.getValorPP() > 0.0){
                            if(pagamento.getValorPP() >=  Uteis.arredondarForcando2CasasDecimais(proPa.valorUsado)){
                                valorRetirar = Uteis.arredondarForcando2CasasDecimais(proPa.valorUsado);
                            } else {
                                valorRetirar = pagamento.getValorPP();
                            }
                        }
                        if(valorRetirar > 0.0){
                            valorPagoMPP = adicionarProdutoParcela(valorRetirar, parcela, proPa, rs.getInt("movparcela"), pagamento, valorPagoMPP , false);
                        }
                        if(valorProdutoCanceladoPagamentoNao > 0.0){
                            valorPagoMPP = adicionarProdutoParcela(valorProdutoCanceladoPagamentoNao, parcela, proPa, rs.getInt("movparcela"), pagamento, valorPagoMPP , true);
                        }

                    }
                    if(valorPagoMPP <= 0.0 ){
                        break;
                    }
                }
                List<Parcela> parcelasPagamento = mapa.get(rs.getInt("movpagamento"));
                if(parcelasPagamento == null){
                    parcelasPagamento = new ArrayList<>();
                    mapa.put(rs.getInt("movpagamento"), parcelasPagamento);
                }
                parcelasPagamento.add(parcela);
            }
            Set<Integer> keySet = mapa.keySet();
            for(Integer key : keySet){
                List<Parcela> parcelas = mapa.get(key);
                List<ProdutoParcela> produtosPagamento  = new ArrayList<>();
                List<ProdutoParcela> produtosCanceladosPagamentoCancelados  = new ArrayList<>();
                List<ProdutoParcela> produtosCanceladosPagamentoNaoCancelados  = new ArrayList<>();
                String produtosPagos = "";
                String produtosPagosPagamentoNaoCancelados = "";
                for(Parcela parc : parcelas){
                    for(ProdutoParcela prodPa : parc.produtos){
                        produtosPagamento.add(prodPa);
                        produtosPagos += "|"+prodPa.produto+","+prodPa.tipo+","+prodPa.contrato+","+Uteis.arredondarForcando2CasasDecimais(prodPa.valorPago);
                    }
                    for(ProdutoParcela prodPa : parc.produtosCanceladosPagamentoCancelados){
                        produtosCanceladosPagamentoCancelados.add(prodPa);
                        produtosPagos += "|"+prodPa.produto+","+prodPa.tipo+","+prodPa.contrato+","+Uteis.arredondarForcando2CasasDecimais(prodPa.valorPago);
                    }
                    for(ProdutoParcela prodPa : parc.produtosCanceladosPagamentoNaoCancelados){
                        produtosCanceladosPagamentoNaoCancelados.add(prodPa);
                        produtosPagosPagamentoNaoCancelados += "|"+prodPa.produto+","+prodPa.tipo+","+prodPa.contrato+","+Uteis.arredondarForcando2CasasDecimais(prodPa.valorPago);
                    }
                }
                con.prepareStatement("UPDATE movpagamento SET produtospagos = '"+produtosPagos+"', produtosPagoscancelados = '"+ produtosPagosPagamentoNaoCancelados +"' WHERE codigo = "+key).execute();

                //cartoes
                ResultSet rsCartoes = con.prepareStatement("SELECT * FROM cartaocredito WHERE movpagamento = " + key+" ORDER BY datacompesancao").executeQuery();
                Integer nrCartoes = SuperFacadeJDBC.contar("SELECT COUNT(*) FROM cartaocredito WHERE movpagamento = " + key, con);

                while(rsCartoes.next()){
                    Integer codigo = rsCartoes.getInt("codigo");
                    Double valor = rsCartoes.getDouble("valor");
                    String produtosPagosCartao = "";
                    String produtosPagosCartaoCancelados = "";
                    if(rsCartoes.getString("situacao").equals("CA")){
                        for(ProdutoParcela prodPa : produtosCanceladosPagamentoCancelados){
                            Double valorUsadoProduto = (prodPa.tipo.equals("PM") || mapaProdParcelas.get(prodPa.produto).size() > 1 ) ? prodPa.valorUsado : (prodPa.valorPago/nrCartoes);

                            if(Uteis.arredondarForcando2CasasDecimais(valor) > 0.0 && Uteis.arredondarForcando2CasasDecimais(prodPa.valorUsado) > 0.0){

                                if(Uteis.arredondarForcando2CasasDecimais(valor) >= Uteis.arredondarForcando2CasasDecimais(valorUsadoProduto)){
                                    produtosPagosCartao +="|"+prodPa.produto+","+prodPa.tipo+","+prodPa.contrato+","+Uteis.arredondarForcando2CasasDecimais(valorUsadoProduto);
                                    valor = Uteis.arredondarForcando2CasasDecimais(valor) - Uteis.arredondarForcando2CasasDecimais(valorUsadoProduto);
                                    prodPa.valorUsado = prodPa.tipo.equals("PM") ? 0.0 : prodPa.valorUsado;
                                }else{
                                    produtosPagosCartao +="|"+prodPa.produto+","+prodPa.tipo+","+prodPa.contrato+","+Uteis.arredondarForcando2CasasDecimais(valor);
                                    prodPa.valorUsado = Uteis.arredondarForcando2CasasDecimais(prodPa.valorUsado) - Uteis.arredondarForcando2CasasDecimais(valor);
                                    valor = 0.0;
                                    break;
                                }
                            }
                        }

                    } else{
                        for(ProdutoParcela prodPa : produtosPagamento){
                            Double valorUsadoProduto = (prodPa.tipo.equals("PM") || mapaProdParcelas.get(prodPa.produto).size() > 1 ) ? prodPa.valorUsado : (prodPa.valorPago/nrCartoes);

                            if(Uteis.arredondarForcando2CasasDecimais(valor) > 0.0 && Uteis.arredondarForcando2CasasDecimais(prodPa.valorUsado) > 0.0){

                                if(Uteis.arredondarForcando2CasasDecimais(valor) >= Uteis.arredondarForcando2CasasDecimais(valorUsadoProduto)){
                                    produtosPagosCartao +="|"+prodPa.produto+","+prodPa.tipo+","+prodPa.contrato+","+Uteis.arredondarForcando2CasasDecimais(valorUsadoProduto);
                                    valor = Uteis.arredondarForcando2CasasDecimais(valor) - Uteis.arredondarForcando2CasasDecimais(valorUsadoProduto);
                                    prodPa.valorUsado = (prodPa.tipo.equals("PM") || mapaProdParcelas.get(prodPa.produto).size() > 1 ) ? 0.0 : prodPa.valorUsado;
                                }else{
                                    produtosPagosCartao +="|"+prodPa.produto+","+prodPa.tipo+","+prodPa.contrato+","+Uteis.arredondarForcando2CasasDecimais(valor);
                                    prodPa.valorUsado = Uteis.arredondarForcando2CasasDecimais(prodPa.valorUsado) - Uteis.arredondarForcando2CasasDecimais(valor);
                                    valor = 0.0;
                                    break;
                                }
                            }
                        }
                        for(ProdutoParcela prodPa : produtosCanceladosPagamentoNaoCancelados){
                            Double valorUsadoProduto = (prodPa.tipo.equals("PM") || mapaProdParcelas.get(prodPa.produto).size() > 1 ) ? prodPa.valorUsado : (prodPa.valorPago/nrCartoes);

                            if(Uteis.arredondarForcando2CasasDecimais(valor) > 0.0 && Uteis.arredondarForcando2CasasDecimais(prodPa.valorUsado) > 0.0){

                                if(Uteis.arredondarForcando2CasasDecimais(valor) >= Uteis.arredondarForcando2CasasDecimais(valorUsadoProduto)){
                                    produtosPagosCartaoCancelados +="|"+prodPa.produto+","+prodPa.tipo+","+prodPa.contrato+","+Uteis.arredondarForcando2CasasDecimais(valorUsadoProduto);
                                    valor = Uteis.arredondarForcando2CasasDecimais(valor) - Uteis.arredondarForcando2CasasDecimais(valorUsadoProduto);
                                    prodPa.valorUsado = (prodPa.tipo.equals("PM") || mapaProdParcelas.get(prodPa.produto).size() > 1 ) ? 0.0 : prodPa.valorUsado;
                                }else{
                                    produtosPagosCartaoCancelados +="|"+prodPa.produto+","+prodPa.tipo+","+prodPa.contrato+","+Uteis.arredondarForcando2CasasDecimais(valor);
                                    prodPa.valorUsado = Uteis.arredondarForcando2CasasDecimais(prodPa.valorUsado) - Uteis.arredondarForcando2CasasDecimais(valor);
                                    valor = 0.0;
                                    break;
                                }
                            }
                        }
                    }
                    con.prepareStatement("UPDATE cartaocredito SET produtospagos = '"+produtosPagosCartao+"', produtospagoscancelados = '"+produtosPagosCartaoCancelados+"' WHERE codigo = "+codigo).execute();
                }

                //cheques
                ResultSet rsCheques = con.prepareStatement("SELECT * FROM cheque WHERE movpagamento = " + key+" ORDER BY datacompesancao").executeQuery();
                while(rsCheques.next()){
                    Integer codigo = rsCheques.getInt("codigo");
                    Double valor = rsCheques.getDouble("valor");
                    String produtosPagosCheque = "";
                    String produtosPagosChequeCancelados = "";
                    if(rsCheques.getString("situacao").equals("CA")){
                        for(ProdutoParcela prodPa : produtosCanceladosPagamentoCancelados){
                            if(Uteis.arredondarForcando2CasasDecimais(valor) > 0.0 && Uteis.arredondarForcando2CasasDecimais(prodPa.valorUsado) > 0.0){

                                if(Uteis.arredondarForcando2CasasDecimais(valor) >= Uteis.arredondarForcando2CasasDecimais(prodPa.valorUsado)){
                                    produtosPagosCheque +="|"+prodPa.produto+","+prodPa.tipo+","+prodPa.contrato+","+Uteis.arredondarForcando2CasasDecimais(prodPa.valorUsado);
                                    valor = Uteis.arredondarForcando2CasasDecimais(valor) - Uteis.arredondarForcando2CasasDecimais(prodPa.valorUsado);
                                    prodPa.valorUsado = 0.0;
                                }else{
                                    produtosPagosCheque +="|"+prodPa.produto+","+prodPa.tipo+","+prodPa.contrato+","+Uteis.arredondarForcando2CasasDecimais(valor);
                                    prodPa.valorUsado = Uteis.arredondarForcando2CasasDecimais(prodPa.valorUsado) - Uteis.arredondarForcando2CasasDecimais(valor);
                                    break;
                                }
                            }
                        }
                    }else{
                        for(ProdutoParcela prodPa : produtosPagamento){
                            if(Uteis.arredondarForcando2CasasDecimais(valor) > 0.0 && Uteis.arredondarForcando2CasasDecimais(prodPa.valorUsado) > 0.0){

                                if (Uteis.arredondarForcando2CasasDecimais(valor) >= Uteis.arredondarForcando2CasasDecimais(prodPa.valorUsado)) {
                                    produtosPagosCheque += "|" + prodPa.produto + "," + prodPa.tipo + "," + prodPa.contrato + "," + Uteis.arredondarForcando2CasasDecimais(prodPa.valorUsado);
                                    valor = Uteis.arredondarForcando2CasasDecimais(valor) - Uteis.arredondarForcando2CasasDecimais(prodPa.valorUsado);
                                    prodPa.valorUsado = 0.0;
                                } else {
                                    produtosPagosCheque += "|" + prodPa.produto + "," + prodPa.tipo + "," + prodPa.contrato + "," + Uteis.arredondarForcando2CasasDecimais(valor);
                                    prodPa.valorUsado = Uteis.arredondarForcando2CasasDecimais(prodPa.valorUsado) - Uteis.arredondarForcando2CasasDecimais(valor);
                                    break;
                                }
                            }
                        }

                        for(ProdutoParcela prodPa : produtosCanceladosPagamentoNaoCancelados){
                            if(Uteis.arredondarForcando2CasasDecimais(valor) > 0.0 && Uteis.arredondarForcando2CasasDecimais(prodPa.valorUsado) > 0.0){

                                if (Uteis.arredondarForcando2CasasDecimais(valor) >= Uteis.arredondarForcando2CasasDecimais(prodPa.valorUsado)) {
                                    produtosPagosChequeCancelados += "|" + prodPa.produto + "," + prodPa.tipo + "," + prodPa.contrato + "," + Uteis.arredondarForcando2CasasDecimais(prodPa.valorUsado);
                                    valor = Uteis.arredondarForcando2CasasDecimais(valor) - Uteis.arredondarForcando2CasasDecimais(prodPa.valorUsado);
                                    prodPa.valorUsado = 0.0;
                                } else {
                                    produtosPagosChequeCancelados += "|" + prodPa.produto + "," + prodPa.tipo + "," + prodPa.contrato + "," + Uteis.arredondarForcando2CasasDecimais(valor);
                                    prodPa.valorUsado = Uteis.arredondarForcando2CasasDecimais(prodPa.valorUsado) - Uteis.arredondarForcando2CasasDecimais(valor);
                                    break;
                                }
                            }
                        }
                    }
                    con.prepareStatement("UPDATE cheque SET produtospagos = '"+produtosPagosCheque+"', produtospagoscancelados = '"+produtosPagosChequeCancelados+"' WHERE codigo = "+codigo).execute();
                }

            }



        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    private static Double adicionarProdutoParcela(Double valorRetirar, Parcela parcela, ProdutoParcela proPa, Integer movparcela,  MovPagamentoVO pagamento, Double valorPagoMpp, boolean produtoCanceladoPagamentoNaoCancelado) {
        Double valorNaParcela = 0.0;
        if(Uteis.arredondarForcando2CasasDecimais(valorRetirar) > Uteis.arredondarForcando2CasasDecimais(valorPagoMpp)){
            valorRetirar = valorPagoMpp;
        }
        if(valorRetirar >= Uteis.arredondarForcando2CasasDecimais(proPa.valorUsado)){
            valorNaParcela = proPa.valorUsado;
            valorPagoMpp = Uteis.arredondarForcando2CasasDecimais(valorPagoMpp - proPa.valorUsado);
            proPa.valorUsado = 0.0;
        }else{
            valorNaParcela = valorRetirar;
            proPa.valorUsado = Uteis.arredondarForcando2CasasDecimais(proPa.valorUsado - valorRetirar);
            valorPagoMpp = Uteis.arredondarForcando2CasasDecimais(valorPagoMpp - valorRetirar);
        }
        ProdutoParcela produtoDoPagamentoDaParcela = new ProdutoParcela(proPa.produto, valorNaParcela,
                movparcela, proPa.contrato, proPa.tipo, proPa.situacao,proPa.pagamentoDebito);
        if (produtoCanceladoPagamentoNaoCancelado) {
            parcela.produtosCanceladosPagamentoNaoCancelados.add(produtoDoPagamentoDaParcela);
            pagamento.setValorPP(Uteis.arredondarForcando2CasasDecimais(pagamento.getValorPP() - valorNaParcela));
        } else  if(proPa.situacao.equals("CA") || proPa.pagamentoDebito || proPa.tipo.equals("CC")){
            parcela.produtosCanceladosPagamentoCancelados.add(produtoDoPagamentoDaParcela);
            pagamento.setValorPPCancelado(Uteis.arredondarForcando2CasasDecimais(pagamento.getValorPPCancelado() - valorNaParcela));
        } else {
            parcela.produtos.add(produtoDoPagamentoDaParcela);
            pagamento.setValorPP(Uteis.arredondarForcando2CasasDecimais(pagamento.getValorPP() - valorNaParcela));
        }
        return valorPagoMpp;
    }

    public static void main(String... args) {
        try {
            Connection c = new DAO().obterConexaoEspecifica(args.length > 0 ? args[0] : "d485e7096869560c388b07b8dc5a006d");
//            Connection c = DriverManager.getConnection("****************************************************"
//                    + "", "postgres", "pactodb");
//            Connection c = DriverManager.getConnection("*********************************************************************","postgres", "pactodb");
            Conexao.guardarConexaoForJ2SE(c);
            ProdutosPagosServico produtos = new ProdutosPagosServico();
//            produtos.atualizarTodosProdutosPagos(c);
            produtos.setarProdutosPagos(c,19131);
//            contratosViculosZoados(c);

        } catch (Exception ex) {
            ex.printStackTrace();
        }
    }
    public static void contratosViculosZoados(Connection con) throws Exception{
        try {
            con.setAutoCommit(false);
            MovProdutoParcela movProdutoParcela = new MovProdutoParcela(con);

            String sql = "select  movPag.pessoa, movPag.nomepagador, movPag.empresa as codigoempresa,\n" +
                    " movPag.produtospagos, movPag.codigo CodigoMovPagamento,movPag.valortotal valor,\n" +
                    " (select sum((regexp_split_to_array(col,E','))[4]::numeric)  from (\n" +
                    " select  regexp_split_to_table(movpag.produtospagos , E'\\\\|') as col) as coluna1 where trim(col ) <>'') calculoString,\n" +
                    " movPag.recibopagamento,movParc.contrato \n" +
                    " from MovPagamento movPag\n" +
                    " inner join pagamentomovparcela pgMvt on pgMvt.movpagamento =movPag.codigo \n" +
                    " inner join movparcela movParc on pgMvt.movparcela = movparc.codigo \n" +
                    " where (movPag.valor > 0) and movPag.recibopagamento is not null and \n" +
                    " movpag.valor::numeric  <> (\n" +
                    " select sum((regexp_split_to_array(col,E','))[4]::numeric)  from (\n" +
                    " select  regexp_split_to_table(movpag.produtospagos , E'\\\\|') as col\n" +
                    " ) as coluna1 where trim(col ) <>'' )\n" +
                    " group by movPag.pessoa,movPag.nomepagador,movPag.empresa,movPag.codigo,\n" +
                    " movPag.valortotal,movPag.recibopagamento,movPag.produtospagos, movParc.contrato ";
            List<Integer> codigosRecibos = new ArrayList<>();
            try(ResultSet resultSet = con.prepareStatement(sql).executeQuery()) {
                while (resultSet.next()) {
                    movProdutoParcela.excluirMovProdutoParcelasRecibo(resultSet.getInt("contrato"));
                    codigosRecibos.add(resultSet.getInt("recibopagamento"));
                }
            }
            if(codigosRecibos.isEmpty()){
                throw new Exception("Não Há registros para serem corrigidos!");
            }
            new CorrigirMovParcela().executarProcesso(false);
            codigosRecibos.forEach(codigosRecibo -> setarProdutosPagos(con, codigosRecibo));
            con.commit();
        }catch (Exception e){
            con.rollback();
        }finally {
            con.setAutoCommit(true);
        }
    }

    public static void atualizarTodosProdutosPagos(Connection con) throws Exception{
        Uteis.logarDebug("Iniciando processo: produtos pagos serviço");

        Integer total = SuperFacadeJDBC.contar("SELECT COUNT(codigo) FROM recibopagamento", con);
        Integer passo = 0;
        ConfiguracaoSistemaControle config = (ConfiguracaoSistemaControle) JSFUtilities.getFromSession("ConfiguracaoSistemaControle");
        if (!JSFUtilities.isJSFContext()) {
            config = new ConfiguracaoSistemaControle();
            config.setRodandoProdutosPagosServico(true);
        }

        try (ResultSet query = con.prepareStatement("SELECT codigo FROM recibopagamento").executeQuery()) {
            while (query.next()) {
                if (config.isRodandoProdutosPagosServico() || config.isRodandoExecutarProcessos()) {
                    setarProdutosPagos(con, query.getInt("codigo"));

                    String log = String.format("%d/%d - Recibo %d atualizado", ++passo, total, query.getInt("codigo"));
                    Uteis.logarDebug(log);
                    config.setInformacaoProdutosPagosServico(log);

                    //EXECUÇÃO DE TODOS OS PROCESSOS
                    config.setInformacaoExecutarProcessos(log);
                } else {
                    return;
                }
            }
        }
        config.setInformacaoProdutosPagosServico("Ajustes concluídos");
        config.setRodandoProdutosPagosServico(false);
    }

}

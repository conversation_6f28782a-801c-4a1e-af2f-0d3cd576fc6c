
package negocio.facade.jdbc.financeiro;

import negocio.comuns.financeiro.ComportamentoConta;
import negocio.comuns.financeiro.ContaVO;
import negocio.comuns.financeiro.TipoContaVO;
import negocio.comuns.utilitarias.ConsistirException;
import negocio.comuns.utilitarias.Ordenacao;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisValidacao;
import negocio.facade.jdbc.arquitetura.SuperEntidade;
import negocio.interfaces.financeiro.TipoContaInterfaceFacade;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.sql.Statement;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

public class TipoConta extends SuperEntidade implements TipoContaInterfaceFacade {

    public TipoConta() throws Exception {
        super();
    }

    public TipoConta(Connection con) throws Exception {
        super(con);
    }

    @Override
    public void alterar(TipoContaVO obj) throws Exception {
        try {
            con.setAutoCommit(false);
            TipoContaVO.validarDados(obj);
            
            validarExisteComportamento(obj.getComportamento(), obj.getCodigo(), con);
            alterar(getIdEntidade());

            StringBuilder sql = new StringBuilder();
            sql.append("UPDATE tipoconta ");
            sql.append("SET descricao = ?, ");
            sql.append("comportamento = ? ");
            sql.append("WHERE  codigo = ?");
            PreparedStatement sqlAlterar = con.prepareStatement(sql.toString());
            int i = 1;
            sqlAlterar.setString(i++, obj.getDescricao());
            if(UteisValidacao.emptyNumber(obj.getCodigoComportamento())){
            	sqlAlterar.setNull(i++, 0);
            }else{
            	sqlAlterar.setInt(i++, obj.getCodigoComportamento());	
            }
            
            
            sqlAlterar.setInt(i++, obj.getCodigo());
            sqlAlterar.execute();
            con.commit();
        } catch (Exception e) {
            con.rollback();
            throw e;
        } finally {
            con.setAutoCommit(true);
        }
    }

    @Override
    public void excluir(TipoContaVO obj) throws Exception {
        try {
            con.setAutoCommit(false);
            excluir(getIdEntidade());
            String sql = "DELETE FROM tipoconta WHERE codigo = ?";
            PreparedStatement sqlExcluir = con.prepareStatement(sql);
            sqlExcluir.setInt(1, obj.getCodigo());
            sqlExcluir.execute();
            con.commit();
        } catch (Exception e) {
            con.rollback();
            throw e;
        } finally {
            con.setAutoCommit(true);
        }
    }

    @Override
    public void incluir(TipoContaVO obj) throws Exception {
            TipoContaVO.validarDados(obj);
            validarExisteComportamento(obj.getComportamento(), obj.getCodigo(), con);
            incluir(getIdEntidade());
            StringBuilder sql = new StringBuilder();
            sql.append("insert into tipoconta (descricao, comportamento) ");
            sql.append("values (?, ?)");
            PreparedStatement sqlInserir = con.prepareStatement(sql.toString());
            int i = 0;
            sqlInserir.setString(++i, obj.getDescricao());
            if(UteisValidacao.emptyNumber(obj.getCodigoComportamento())){
            	sqlInserir.setNull(++i, 0);
            }else{
            	sqlInserir.setInt(++i, obj.getCodigoComportamento());	
            }
            sqlInserir.execute();
            obj.setCodigo(obterValorChavePrimariaCodigo());
            obj.setNovoObj(false);
        }

    @Override
    public List<TipoContaVO> consultar(TipoContaVO filtro) throws Exception {
        StringBuilder sql = new StringBuilder();
        sql.append("SELECT codigo, descricao, comportamento ");
        sql.append("FROM tipoconta ");
        sql.append("WHERE descricao ilike('%" + filtro.getDescricao().toUpperCase() + "%') ");
        PreparedStatement pst = con.prepareStatement(sql.toString());
        ResultSet tabelaResultado = pst.executeQuery();
        return (montarDadosConsulta(tabelaResultado,
                Uteis.NIVELMONTARDADOS_DADOSBASICOS, con));
    }

    @Override
    public List<TipoContaVO> consultarTodas(int nivelMontarDados) throws Exception {
        String sqlStr = "SELECT codigo, descricao, comportamento FROM tipoconta";
        Statement stm = con.createStatement();
        ResultSet tabelaResultado = stm.executeQuery(sqlStr);
        return (montarDadosConsulta(tabelaResultado, nivelMontarDados, con));
    }

    public static List<TipoContaVO> montarDadosConsulta(ResultSet tabelaResultado,
            int nivelMontarDados, Connection con) throws Exception {
        List vetResultado = new ArrayList();
        while (tabelaResultado.next()) {
            TipoContaVO obj = new TipoContaVO();
            obj = montarDados(tabelaResultado, nivelMontarDados, con);
            vetResultado.add(obj);
        }
        return vetResultado;
    }

    public static void validarExisteComportamento(ComportamentoConta comportamento, Integer tipo, Connection con) throws Exception{
    	if(comportamento == null){
    		return;
    	}
    	ResultSet consulta = criarConsulta("SELECT * FROM tipoconta WHERE comportamento = "+comportamento.getCodigo()+" and codigo <> "+tipo, con);
    	if(consulta.next()){
    		throw new ConsistirException("Já existe um Tipo de Conta com este comportamento");
    	}
    }

    public TipoContaVO existeTipoConta(ComportamentoConta comportamento) throws Exception{
        ResultSet consulta = criarConsulta("SELECT * FROM tipoconta WHERE comportamento = "+comportamento.getCodigo(), con);
        while(consulta.next()){
            return montarDados(consulta, Uteis.NIVELMONTARDADOS_DADOSBASICOS, con);
        }
        return new TipoContaVO();
    }
    
    public static TipoContaVO montarDados(ResultSet dadosSQL,
            int nivelMontarDados, Connection con) throws Exception {
        TipoContaVO obj = new TipoContaVO();
        obj.setCodigo(new Integer(dadosSQL.getInt("codigo")));
        obj.setDescricao(dadosSQL.getString("descricao"));
        obj.setCodigoComportamento(dadosSQL.getInt("comportamento"));
        obj.setNovoObj(false);
        if(nivelMontarDados == Uteis.NIVELMONTARDADOS_TODOS){
        	Conta conta = new Conta(con);
        	ContaVO filtro = new ContaVO();
        	filtro.setTipoConta(obj);
        	obj.setContas(conta.consultar(filtro, false, Uteis.NIVELMONTARDADOS_TELACONSULTA, ""));
        }
        return obj;
    }
    
    @Override
    public TipoContaVO consultarPorChavePrimaria(Integer codigoPrm, int nivelMontarDados) throws Exception {
        consultar(getIdEntidade(), false);
        String sql = "SELECT * FROM tipoconta WHERE codigo = ?";
        PreparedStatement sqlConsultar = con.prepareStatement(sql);
        sqlConsultar.setInt(1, codigoPrm.intValue());
        ResultSet tabelaResultado = sqlConsultar.executeQuery();
        if (!tabelaResultado.next()) {
            throw new ConsistirException("Dados Não Encontrados ( Conta ).");
        }
        return (montarDados(tabelaResultado, nivelMontarDados, con));
    }
    
    public ComportamentoConta consultarComportamento(Integer codigoConta) throws SQLException{
    	String sql = "SELECT comportamento FROM tipoconta " +
    				" INNER JOIN conta on conta.tipoconta = tipoconta.codigo "+
    				" WHERE conta.codigo = ?";
        PreparedStatement sqlConsultar = con.prepareStatement(sql);
        sqlConsultar.setInt(1, codigoConta);
        ResultSet tabelaResultado = sqlConsultar.executeQuery();
        if(tabelaResultado.next()){
        	return ComportamentoConta.getComportamentoConta(tabelaResultado.getInt("comportamento"));
        }else{
        	return null;
        }
    }
    private ResultSet getRS() throws SQLException {
        StringBuilder sql = new StringBuilder();
        sql.append(" Select codigo,descricao,comportamento\n");
        sql.append(" FROM TipoConta where comportamento <> "+ComportamentoConta.OPENBANK.getCodigo()); // Tipo openbank apenas inserido pela integração openbank.
        PreparedStatement sqlConsultar = con.prepareStatement(sql.toString());
        ResultSet rs = sqlConsultar.executeQuery();
        return rs;
    }

    public String consultarJSON() throws Exception {
        ResultSet rs = getRS();
        StringBuilder json = new StringBuilder();
        json.append("{\"aaData\":[");
        boolean dados = false;
        while (rs.next()) {
            dados = true;
            json.append("[\"").append(rs.getString("codigo")).append("\",");
            json.append("\"").append(rs.getString("descricao").trim()).append("\",");
            json.append("\"").append(ComportamentoConta.getComportamentoConta(rs.getInt("comportamento"))).append("\"],");
        }
        if (dados) {
            json.deleteCharAt(json.toString().length() - 1);
        }
        json.append("]}");
        return json.toString();
    }
    public List consultarParaImpressao(String filtro, String ordem, String campoOrdenacao, Integer empresa) throws SQLException {
        ResultSet rs = getRS();
        List<TipoContaVO> lista = new ArrayList<TipoContaVO>();
        while (rs.next()) {

            TipoContaVO tipoContaVO = new TipoContaVO();
            String geral = rs.getString("codigo") + rs.getString("descricao") + ComportamentoConta.getComportamentoConta(rs.getInt("comportamento")).getDescricao();
            if (geral.toLowerCase().contains(filtro.toLowerCase())) {
                tipoContaVO.setCodigo(rs.getInt("codigo"));
                tipoContaVO.setDescricao(rs.getString("descricao"));
                tipoContaVO.setCodigoComportamento(rs.getInt("comportamento"));
                tipoContaVO.setComportamento(ComportamentoConta.getComportamentoConta(tipoContaVO.getCodigoComportamento()));
                lista.add(tipoContaVO);
            }
        }
        if (campoOrdenacao.equals("Código")) {
            Ordenacao.ordenarLista(lista, "codigo");
        } else if (campoOrdenacao.equals("Tipo de Conta")) {
            Ordenacao.ordenarLista(lista, "descricao");
        } else if (campoOrdenacao.equals("Comportamento")) {
            Ordenacao.ordenarLista(lista, "comportamentoApresentar");
        }
        if (ordem.contains("desc")) {
            Collections.reverse(lista);
        }
        return lista;

    }
}

/*
 * To change this template, choose Too<PERSON> | Templates
 * and open the template in the editor.
 */

package negocio.facade.jdbc.financeiro;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

import br.com.pactosolucoes.enumeradores.TipoOperacaoLancamento;
import controle.financeiro.CaixaControle;
import negocio.comuns.financeiro.BloqueioCaixaVO;
import negocio.comuns.financeiro.CaixaContaVO;
import negocio.comuns.financeiro.CaixaVO;
import negocio.comuns.financeiro.RelatorioFechamentoCaixaTO;
import negocio.comuns.financeiro.RelatorioFechamentoCaixaTO.CaixaAgrupar;
import negocio.comuns.financeiro.enumerador.TipoES;
import negocio.comuns.utilitarias.Calendario;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisValidacao;
import negocio.facade.jdbc.arquitetura.SuperEntidade;
import negocio.interfaces.financeiro.BloqueioCaixaInterfaceFacade;
import negocio.interfaces.financeiro.CaixaInterfaceFacade;

/**
 *
 * <AUTHOR>
 */
public class Caixa extends SuperEntidade implements CaixaInterfaceFacade {

    public Caixa() throws Exception {
        super();
    }

    public Caixa(Connection con) throws Exception {
        super(con);
    }

    public void incluir(CaixaVO caixaVo) throws Exception {
        incluir(caixaVo, true);
    }

    public void incluir(CaixaVO caixaVo, boolean validarBloqueio) throws Exception {
        StringBuilder sb = new StringBuilder();
        if (validarBloqueio) {
            validarDataBloqueio(caixaVo);
        }
        sb.append("insert into caixa(usuario, empresa, dataAbertura, datatrabalho) ");
        sb.append("values(?,?,?,?)");
        PreparedStatement pst = this.con.prepareStatement(sb.toString());
        pst.setInt(1, caixaVo.getUsuarioVo().getCodigo());
        pst.setInt(2, caixaVo.getEmpresaVo().getCodigo());
        pst.setTimestamp(3, Uteis.getDataJDBCTimestamp(caixaVo.getDataAbertura()));
        pst.setDate(4, Uteis.getDataJDBC(caixaVo.getDataTrabalho()));
        pst.execute();
        caixaVo.setCodigo(obterValorChavePrimariaCodigo());
        // Incluir as contas do caixa.
        for (CaixaContaVO obj: caixaVo.getListaCaixaConta()){
          obj.getCaixaVo().setCodigo(caixaVo.getCodigo());
          getFacade().getFinanceiro().getCaixaConta().incluir(obj);
        }
    }

    public CaixaVO abrirCaixa(CaixaVO obj) throws Exception {
        StringBuilder sb = new StringBuilder();
        sb.append("insert into caixa(usuario, empresa, dataAbertura, datatrabalho) ");
        sb.append("values(?,?,?,?) RETURNING codigo");
        PreparedStatement pst = this.con.prepareStatement(sb.toString());
        pst.setInt(1, obj.getUsuarioVo().getCodigo());
        pst.setInt(2, obj.getEmpresaVo().getCodigo());
        pst.setTimestamp(3, Uteis.getDataJDBCTimestamp(obj.getDataAbertura()));
        pst.setDate(4, Uteis.getDataJDBC(obj.getDataTrabalho()));

        ResultSet rsCodigo = pst.executeQuery();
        rsCodigo.next();
        obj.setCodigo(rsCodigo.getInt(1));
        obj.setCodigo(obterValorChavePrimariaCodigo());

//        // Incluir as contas do caixa.
//        for (CaixaContaVO obj: obj.getListaCaixaConta()){
//            obj.getCaixaVo().setCodigo(obj.getCodigo());
//            getFacade().getFinanceiro().getCaixaConta().incluir(obj);
//        }
        return obj;
    }

    public void alterar(CaixaVO caixaVo) throws Exception {
        StringBuilder sb = new StringBuilder();
        sb.append("update caixa set dataFechamento=?, responsavelfechamento = ?, datatrabalho = ? where codigo =? ");
        PreparedStatement pst = this.con.prepareStatement(sb.toString());
        pst.setTimestamp(1, Uteis.getDataJDBCTimestamp(caixaVo.getDataFechamento()));
        pst.setInt(2, caixaVo.getResponsavelFechamento().getCodigo());
        pst.setDate(3, Uteis.getDataJDBC(caixaVo.getDataTrabalho()));
        pst.setInt(4, caixaVo.getCodigo());
        
        pst.execute();
        // Alterar as contas do caixa.
        for (CaixaContaVO obj: caixaVo.getListaCaixaConta()){
          obj.getCaixaVo().setCodigo(caixaVo.getCodigo());
          obj.setSaldoFinal(getFacade().getFinanceiro().getConta().saldoAte(obj.getContaVo().getCodigo(), Calendario.hoje()));
          getFacade().getFinanceiro().getCaixaConta().alterar(obj);
        }

    }

    public void excluir(CaixaVO caixaVo) throws Exception {
        // excluir as contas do caixa.
        for (CaixaContaVO obj: caixaVo.getListaCaixaConta()){
          getFacade().getFinanceiro().getCaixaConta().excluir(obj);
        }

        StringBuilder sb = new StringBuilder();
        sb.append("delete from caixa  where codigo =? ");
        PreparedStatement pst = this.con.prepareStatement(sb.toString());
        pst.setInt(1, caixaVo.getCodigo());
        pst.execute();
    }

    private CaixaVO montarDadosEspecifico(ResultSet dados)throws Exception{
        CaixaVO obj = new CaixaVO();
        obj.setCodigo(dados.getInt("codigo"));
        obj.getUsuarioVo().setCodigo(dados.getInt("usuario"));
        obj.getResponsavelFechamento().setCodigo(dados.getInt("responsavelfechamento"));
        obj.getEmpresaVo().setCodigo(dados.getInt("empresa"));
        obj.setDataAbertura(dados.getTimestamp("dataAbertura"));
        obj.setDataFechamento(dados.getTimestamp("dataFechamento"));
        obj.setDataTrabalho(dados.getDate("datatrabalho"));
        return obj;
    }

    private CaixaVO montarDadosBasico(ResultSet dados)throws Exception{
       CaixaVO obj = new CaixaVO();
       obj.setCodigo(dados.getInt("codigoCaixa"));
       obj.getUsuarioVo().setCodigo(dados.getInt("codigoUsuario"));
       obj.getResponsavelFechamento().setCodigo(dados.getInt("responsavelfechamento"));
       obj.getEmpresaVo().setCodigo(dados.getInt("codigoEmpresa"));
       obj.setDataAbertura(dados.getTimestamp("dataAbertura"));
       obj.setDataFechamento(dados.getTimestamp("dataFechamento"));
       obj.setDataTrabalho(dados.getDate("datatrabalho"));
       return obj;
    }

    public CaixaVO montarDados(ResultSet dados, int nivelMontarDados)throws Exception{
       CaixaVO obj = montarDadosBasico(dados);
       
       if (nivelMontarDados == Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA){
           return obj;
       }
       if (nivelMontarDados == Uteis.NIVELMONTARDADOS_TODOS) {
           obj.setListaCaixaConta(getFacade().getFinanceiro().getCaixaConta().consultar(obj, nivelMontarDados));
       }
       if ((nivelMontarDados == Uteis.NIVELMONTARDADOS_TODOS) ||
           (nivelMontarDados == Uteis.NIVELMONTARDADOS_DADOSENTIDADESPRINCIPAIS)){
           obj.getEmpresaVo().setNome(dados.getString("nomeEmpresa"));
           obj.getEmpresaVo().setRazaoSocial(dados.getString("razaoSocial"));
           obj.getUsuarioVo().setNome(dados.getString("nomeUsuario"));
           obj.getUsuarioVo().setUsername(dados.getString("userName"));
           if(!UteisValidacao.emptyNumber(dados.getInt("responsavelfechamento")))
        	   obj.setResponsavelFechamento(getFacade().getUsuario().consultarPorChavePrimaria(dados.getInt("responsavelfechamento"), Uteis.NIVELMONTARDADOS_DADOSENTIDADESPRINCIPAIS));
       }
       
       return obj;
    }


    private String retornarSelect(int nivelMontarDados)throws Exception{
        StringBuilder sb = new StringBuilder();
        sb.append("select cx.codigo as codigoCaixa, cx.responsavelfechamento, cx.dataAbertura, cx.dataFechamento, cx.datatrabalho, cx.Usuario as codigoUsuario, cx.Empresa as codigoEmpresa ");
        if ((nivelMontarDados == Uteis.NIVELMONTARDADOS_DADOSENTIDADESPRINCIPAIS) ||
                (nivelMontarDados == Uteis.NIVELMONTARDADOS_TODOS))
            sb.append(", usu.nome as nomeUsuario, usu.userName, emp.nome as nomeEmpresa, emp.razaoSocial ");
        sb.append("from caixa cx ");
        if ((nivelMontarDados == Uteis.NIVELMONTARDADOS_DADOSENTIDADESPRINCIPAIS)||
            (nivelMontarDados == Uteis.NIVELMONTARDADOS_TODOS)){
            sb.append("inner join usuario usu on usu.codigo = cx.usuario ");
            sb.append("inner join empresa emp on emp.codigo = cx.empresa ");
        }
        return sb.toString();
    }


    private List<CaixaVO> montarDadosConsulta(ResultSet resultDados, int nivelMontarDados)throws Exception{
        List<CaixaVO> lista = new ArrayList<CaixaVO>();
        while (resultDados.next()){
          lista.add(montarDados(resultDados, nivelMontarDados));
        }
        return lista;
    }

    public CaixaVO consultarCaixaEmAberto(int codigoUsuario, int codigoEmpresa, int nivelMontarDados)throws Exception{
        CaixaVO obj = new CaixaVO();
        StringBuilder sb = new StringBuilder();
        sb.append(retornarSelect(nivelMontarDados));
        sb.append(" where dataFechamento is null and cx.usuario = ").append(codigoUsuario);
        if (codigoEmpresa >0)
          sb.append(" and cx.empresa = ").append(codigoEmpresa);
        sb.append(" ORDER BY cx.codigo DESC ");
        PreparedStatement pst = this.con.prepareStatement(sb.toString());
        ResultSet resultDados = pst.executeQuery();
        if (resultDados.next()){
            obj = montarDados(resultDados, nivelMontarDados);
        }
        
        return obj;
    }
    
    
    public List<CaixaVO> consultar(String sql, int nivelMontarDados)throws Exception{
        if (sql.isEmpty())
           return null;
        PreparedStatement pst = this.con.prepareStatement(sql);
        ResultSet dados = pst.executeQuery();
        return montarDadosConsulta(dados, nivelMontarDados);
  }

  public CaixaVO consultarCaixa(int codigoCaixa, int nivelMontarDados)throws Exception{
     CaixaVO obj = null;
     StringBuilder sql = new StringBuilder();
     sql.append(retornarSelect(nivelMontarDados));
     sql.append(" where cx.codigo = ").append(codigoCaixa);
     PreparedStatement pst = this.con.prepareStatement(sql.toString());
     ResultSet dados = pst.executeQuery();
     if (dados.next())
         obj = montarDados(dados, nivelMontarDados);

     return obj;

  }

    public List<CaixaVO> consultarHistoricoCaixa(int codigoEmpresa,
                                                 int codigoUsuario,
                                                 int codigoUsuarioLogado,
                                                 Date dataIniAbertura,
                                                 Date dataFimAbertura,
                                                 Date dataIniFechamento,
                                                 Date dataFimFechamento,
                                                 Date dataIniTrabalho,
                                                 Date dataFimTrabalho,
                                                 Integer codigoCaixa,
                                                 Boolean permissaoConsultarCaixa,
                                                 int nivelMontarDados)throws Exception{

        StringBuilder sql = new StringBuilder();
        sql.append(retornarSelect(nivelMontarDados));
        sql.append(" where (0 = 0) ");

        if(!permissaoConsultarCaixa){
           sql.append(" and usu.codigo = ").append(codigoUsuarioLogado);
        }

        if (codigoEmpresa > 0)
          sql.append(" and cx.empresa = ").append(codigoEmpresa);
        if (codigoUsuario >0)
          sql.append(" and cx.usuario = ").append(codigoUsuario);
        if (dataIniAbertura != null){
            sql.append(" and cx.dataAbertura between ? and ? ");
        }
        if (dataIniFechamento != null){
            sql.append(" and cx.dataFechamento between ? and ? ");
        }
        if (dataIniTrabalho != null && dataFimTrabalho!=null){
            sql.append(" and cx.dataTrabalho between ? and ? ");
        }
        if(!UteisValidacao.emptyNumber(codigoCaixa)){
        	sql.append(" and cx.codigo = ? ");
        }
        
        sql.append(" order by cx.codigo desc");
        PreparedStatement pst = this.con.prepareStatement(sql.toString());
        int i = 1;
        if ((dataIniAbertura != null) && (dataIniFechamento != null)){
            pst.setTimestamp(i++, Uteis.getDataHoraJDBC(dataIniAbertura, "00:00:00"));
            pst.setTimestamp(i++, Uteis.getDataHoraJDBC(dataFimAbertura, "23:59:59"));
            pst.setTimestamp(i++, Uteis.getDataHoraJDBC(dataIniFechamento, "00:00:00"));
            pst.setTimestamp(i++, Uteis.getDataHoraJDBC(dataFimFechamento, "23:59:59"));
        }else if (dataIniAbertura != null){
            pst.setTimestamp(i++, Uteis.getDataHoraJDBC(dataIniAbertura, "00:00:00"));
            pst.setTimestamp(i++, Uteis.getDataHoraJDBC(dataFimAbertura, "23:59:59"));
        }else if (dataIniFechamento != null){
            pst.setTimestamp(i++, Uteis.getDataHoraJDBC(dataIniFechamento, "00:00:00"));
            pst.setTimestamp(i++, Uteis.getDataHoraJDBC(dataFimFechamento, "23:59:59"));
        }
        if(dataIniTrabalho!=null && dataFimTrabalho!=null){
            pst.setTimestamp(i++, Uteis.getDataHoraJDBC(dataIniTrabalho, "00:00:00"));
            pst.setTimestamp(i++, Uteis.getDataHoraJDBC(dataFimTrabalho, "23:59:59"));
        }
        if(!UteisValidacao.emptyNumber(codigoCaixa)){
        	pst.setInt(i++, codigoCaixa);
        }
        ResultSet dados = pst.executeQuery();
        return montarDadosConsulta(dados, nivelMontarDados);

    }

    public void obterValoresResumidosCaixa(CaixaVO caixa) throws SQLException{
        obterValoresResumidosCaixa(caixa, false);
    }
    
    public void obterValoresResumidosCaixa(CaixaVO caixa, boolean openBankingAtivado) throws SQLException{
    	if (caixa == null) {
            return;
        }

        caixa.setTotalEntrada(Uteis.arredondarForcando2CasasDecimais(obterValoresResumidosCaixa(caixa, TipoES.ENTRADA, openBankingAtivado)));
        caixa.setTotalSaida(Uteis.arredondarForcando2CasasDecimais(obterValoresResumidosCaixa(caixa, TipoES.SAIDA, openBankingAtivado)));
        caixa.setTotalTransferencia(Uteis.arredondarForcando2CasasDecimais(obterValoresResumidosCaixaTransferencia(caixa, openBankingAtivado)));
    }
    
    private Double obterValoresResumidosCaixa(CaixaVO caixa, TipoES tipo, boolean openBankingAtivado) throws SQLException{
        StringBuilder sql = new StringBuilder();
        sql.append("SELECT SUM(MCR.valor) as valor FROM movcontarateio mcr ");
        sql.append("INNER JOIN movconta mc ON mcr.movconta = mc.codigo ");
        if(openBankingAtivado){
            sql.append("LEFT JOIN pagamentostone p2 on p2.movconta = mc.codigo ");
            sql.append("LEFT JOIN transferenciastone t2 on t2.movconta = mc.codigo ");
        }
        sql.append("WHERE mcr.movconta IN (SELECT movconta FROM caixamovconta WHERE caixa = ?) ");
        sql.append("AND tipoes = ? AND mc.pessoa IS NOT NULL ");
        if(openBankingAtivado){
            sql.append("AND NOT mc.descricao = 'Transferência online - ZillyonWeb FIN | Pacto Software Gestão' ");
            sql.append("AND (p2.eventidwebhook is null or ((select status from retornostone where eventid = p2.eventidwebhook) = 'FINISHED')) ");
            sql.append("AND (t2.eventidwebhook is null or ((select status from retornostone where eventid = t2.eventidwebhook) = 'FINISHED')) ");
        }
        try (PreparedStatement ps = con.prepareStatement(sql.toString())) {
            int i = 1;
            ps.setInt(i++, caixa.getCodigo());
            ps.setInt(i++, tipo.getCodigo());
            try (ResultSet rs = ps.executeQuery()) {
                rs.next();
                return rs.getDouble("valor");
            }
        }

    }
    
    private Double obterValoresResumidosCaixaTransferencia(CaixaVO caixa, boolean openBankingAtivado) throws SQLException{
        StringBuilder sql = new StringBuilder();
        sql.append("SELECT sum(mcr.valor) as valor FROM movcontarateio mcr ");
        sql.append("INNER JOIN movconta mc ON mc.codigo = mcr.movconta ");
        sql.append("INNER JOIN caixamovconta c ON c.movconta = mc.codigo ");
        if(openBankingAtivado){
            sql.append("LEFT JOIN pagamentostone p2 on p2.movconta = mc.codigo ");
            sql.append("LEFT JOIN transferenciastone t2 on t2.movconta = mc.codigo ");
        }
        sql.append("WHERE mc.tipooperacao = ").append(TipoOperacaoLancamento.TRANSFERENCIA.getCodigo()).append(" ");
        sql.append("AND c.caixa = ").append(caixa.getCodigo()).append(" ");
        sql.append("AND mcr.tipoes = ").append(TipoES.SAIDA.getCodigo()).append(" ");
        if(openBankingAtivado){
            sql.append("AND (p2.eventidwebhook is null or ((select status from retornostone where eventid = p2.eventidwebhook) = 'FINISHED')) ");
            sql.append("AND (t2.eventidwebhook is null or ((select status from retornostone where eventid = t2.eventidwebhook) = 'FINISHED')) ");
        }
        try (PreparedStatement ps = con.prepareStatement(sql.toString())) {
            try (ResultSet rs = ps.executeQuery()) {
                rs.next();
                return rs.getDouble("valor");
            }
        }

    }
    
    public List<CaixaAgrupar> consultarCaixasMovimentadas(Date inicio, Date fim, Integer empresa) throws Exception{
    	StringBuilder sql = new StringBuilder();
    	sql.append(" SELECT c.codigo as caixa, u.nome FROM caixa c \n");
    	sql.append(" INNER JOIN caixamovconta cmc ON  cmc.caixa = c.codigo \n");
    	sql.append(" INNER JOIN movconta mc ON  mc.codigo = cmc.movconta \n");
    	sql.append(" INNER JOIN conta co ON  co.codigo = mc.conta \n");
    	sql.append(" INNER JOIN usuario u ON  c.usuario = u.codigo \n");
    	sql.append(" WHERE mc.dataquitacao >= '"+Uteis.getDataJDBC(inicio)+" 00:00:00' AND mc.dataquitacao <= '"+Uteis.getDataJDBC(fim)+"  23:59:59' \n");
    	if(!UteisValidacao.emptyNumber(empresa)){
    		sql.append(" AND co.empresa = "+empresa);
    	}
    	sql.append(" GROUP BY c.codigo, u.nome ");

        List<CaixaAgrupar> lista;
        try (ResultSet consulta = criarConsulta(sql.toString(), con)) {
            RelatorioFechamentoCaixaTO rfc = new RelatorioFechamentoCaixaTO();
            lista = new ArrayList<CaixaAgrupar>();
            while (consulta.next()) {
                CaixaAgrupar cx = rfc.new CaixaAgrupar();
                cx.setCodigoCaixa(consulta.getInt("caixa"));
                cx.setNomeCaixa(consulta.getString("nome"));
                lista.add(cx);
            }
        }
        return lista;
    }

    public void reabrirCaixa(CaixaVO caixa) throws Exception{
    	con.prepareStatement(" UPDATE caixa SET datafechamento = null where codigo = "+caixa.getCodigo()).execute();
    	caixa.setDataFechamento(null);
    	caixa.setDataReabertura(Calendario.hoje());
    }
    
	public CaixaVO consultarCaixaParaMovConta(Integer codigoMovConta, Integer nivelMontarDados) throws Exception{
		StringBuilder sql = new StringBuilder();
		sql.append("SELECT c.* FROM caixa c inner join caixamovconta cmc ");
		sql.append("ON c.codigo = cmc.caixa ");
		sql.append("AND cmc.movconta = "+codigoMovConta);
        try (ResultSet resultSet = criarConsulta(sql.toString(), con)) {
            if (resultSet.next())
                if (nivelMontarDados == null) {
                    return new CaixaVO(resultSet.getInt("codigo"));
                } else
                    return montarDados(resultSet, nivelMontarDados);
            else
                return new CaixaVO();
        }
    }

    public CaixaVO consultarCaixaPorMovConta(Integer codigoMovConta, Integer nivelMontarDados) throws Exception {
        StringBuilder sql = new StringBuilder();
        sql.append("SELECT c.* FROM caixa c inner join caixamovconta cmc ");
        sql.append("ON c.codigo = cmc.caixa ");
        sql.append("AND cmc.movconta = "+codigoMovConta);
        try (ResultSet resultSet = criarConsulta(sql.toString(), con)) {
            if (resultSet.next())
                if (nivelMontarDados == null) {
                    return new CaixaVO(resultSet.getInt("codigo"));
                } else
                    return montarDadosEspecifico(resultSet);
            else
                return new CaixaVO();
        }
    }
	
    public List<CaixaVO> consultarCaixasEmAberto(int codigoUsuario, int codigoEmpresa, int nivelMontarDados)throws Exception{
        StringBuilder sb = new StringBuilder();
        sb.append(retornarSelect(nivelMontarDados));
        sb.append(" where dataFechamento is null and cx.usuario = ").append(codigoUsuario);
        if (codigoEmpresa >0)
          sb.append(" and cx.empresa = ").append(codigoEmpresa);
        sb.append(" ORDER BY cx.codigo DESC ");
        try (PreparedStatement pst = this.con.prepareStatement(sb.toString())) {
            try (ResultSet resultDados = pst.executeQuery()) {
                return montarDadosConsulta(resultDados, nivelMontarDados);
            }
        }
    }
    
    public void validarDataBloqueio(CaixaVO caixa) throws Exception{
        BloqueioCaixaInterfaceFacade bcDao = new BloqueioCaixa(con);
        BloqueioCaixaVO bloqueio = bcDao.consultarBloqueioAtual(caixa.getEmpresaVo().getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS);
        if(bloqueio != null){
            if(caixa.getDataTrabalho() != null && Calendario.menor(caixa.getDataTrabalho(), bloqueio.getDataBloqueio())){
                throw new Exception("Data de trabalho não pode ser menor que a data de bloqueio ("
                        +Uteis.getData(bloqueio.getDataBloqueio())+" lançada por "+
                        bloqueio.getUsuarioResponsavel().getNome()+")."
                );
            }
        }
    }
    
}

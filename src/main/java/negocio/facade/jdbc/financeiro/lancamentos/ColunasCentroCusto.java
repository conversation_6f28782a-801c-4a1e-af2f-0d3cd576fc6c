package negocio.facade.jdbc.financeiro.lancamentos;

import negocio.facade.jdbc.arquitetura.consulta.ColunaPrefixavel;

/**
 * <AUTHOR>
 * @since 14/08/2018
 */
enum ColunasCentroCusto implements ColunaPrefixavel {

    CODIGO("codigo"),
    NOME("nome"),
    CODIGO_CENTRO_CUSTOS("codigoCentroCustos");

    public static final String NOME_CENTRO_CUSTO = "nomeCentroCusto";

    private static final String PREFIXO = "centroCusto";
    private String label;

    ColunasCentroCusto(String label) {
        this.label = label;
    }

    public static String getPrefixo() {
        return PREFIXO;
    }

    @Override
    public String getLabel() {
        return label;
    }

    @Override
    public String getLabelComPrefixo() {
        return PREFIXO + "." + label;
    }

}

package negocio.facade.jdbc.financeiro.lancamentos;

import negocio.facade.jdbc.arquitetura.consulta.ColunaPrefixavel;

/**
 * <AUTHOR>
 * @since 15/08/2018
 */
enum ColunasCartao implements ColunaPrefixavel {

    CODIGO_CARTAO("codigocartao"),
    DATA_COMPENSACAO("datacompensacao"),
    DATA_LANCAMENTO("datalancamento"),
    VALOR("valor"),
    OPERADORA("operadora"),
    NOME("nome"),
    AUTORIZACAO("autorizacao"),
    MOV_CONTA("movconta");

    private static final String PREFIXO = "cartao";
    private String label;

    ColunasCartao(String label) {
        this.label = label;
    }

    public static String getPrefixo() {
        return PREFIXO;
    }

    @Override
    public String getLabel() {
        return label;
    }

    @Override
    public String getLabelComPrefixo() {
        return PREFIXO + "." + label;
    }

}

package negocio.facade.jdbc.financeiro.lancamentos;

import negocio.facade.jdbc.arquitetura.consulta.ColunaPrefixavel;

/**
 * <AUTHOR>
 * @since 14/08/2018
 */
public enum ColunasConta implements ColunaPrefixavel {

    CODIGO("codigo"),
    DESCRICAO("descricao");

    private static final String PREFIXO = "conta";
    private String label;

    ColunasConta(String label) {
        this.label = label;
    }

    public static String getPrefixo() {
        return PREFIXO;
    }

    @Override
    public String getLabel() {
        return label;
    }

    @Override
    public String getLabelComPrefixo() {
        return PREFIXO + "." + label;
    }
}

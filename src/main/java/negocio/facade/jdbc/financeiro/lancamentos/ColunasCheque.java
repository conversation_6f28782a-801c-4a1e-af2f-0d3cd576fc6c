package negocio.facade.jdbc.financeiro.lancamentos;

import negocio.facade.jdbc.arquitetura.consulta.ColunaPrefixavel;

/**
 * <AUTHOR>
 * @since 15/08/2018
 */
enum ColunasCheque implements ColunaPrefixavel {

    MOV_CONTA("movconta"),
    BANCO("banco"),
    AGENCIA("agencia"),
    CONTA("conta"),
    NUMERO("numero"),
    CODIGO_CHEQUE("codigocheque"),
    DATA_COMPENSACAO("datacompensacao"),
    DATA_LANCAMENTO("datalancamento"),
    VALOR("valor"),
    NOME("nome");

    private static final String PREFIXO = "cheque";
    private String label;

    ColunasCheque(String label) {
        this.label = label;
    }

    public static String getPrefixo() {
        return PREFIXO;
    }

    @Override
    public String getLabel() {
        return label;
    }

    @Override
    public String getLabelComPrefixo() {
        return PREFIXO + "." + label;
    }

}

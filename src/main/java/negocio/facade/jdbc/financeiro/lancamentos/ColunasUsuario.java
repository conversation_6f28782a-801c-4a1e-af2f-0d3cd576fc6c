package negocio.facade.jdbc.financeiro.lancamentos;

import negocio.facade.jdbc.arquitetura.consulta.ColunaPrefixavel;

/**
 * <AUTHOR>
 * @since 15/08/2018
 */
enum ColunasUsuario implements ColunaPrefixavel {

    CODIGO("codigo"),
    NOME("nome"),
    COLABORADOR("colaborador"),
    USERNAME("username"),
    SENHA("senha"),
    TIPO_USUARIO("tipoUsuario"),
    ADMINISTRADOR("administrador"),
    CLIENTE("cliente"),
    DATA_ALTERACAO_SENHA("dataAlteracaoSenha"),
    PERMITE_ALTERAR_PROPRIA_SENHA("permiteAlterarPropriaSenha"),
    SERVICE_USUARIO("serviceUsuario"),
    SERVICE_SENHA("serviceSenha"),
    PEDIR_SENHA_FUNCIONALIDADE("pedirSenhaFuncionalidade");

    private static final String PREFIXO = "usuario";
    private String label;

    ColunasUsuario(String label) {
        this.label = label;
    }

    public static String getPrefixo() {
        return PREFIXO;
    }

    @Override
    public String getLabel() {
        return label;
    }

    @Override
    public String getLabelComPrefixo() {
        return PREFIXO + "." + label;
    }

}

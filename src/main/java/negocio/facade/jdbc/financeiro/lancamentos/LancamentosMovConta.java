package negocio.facade.jdbc.financeiro.lancamentos;

import br.com.pactosolucoes.enumeradores.TipoOperacaoLancamento;
import negocio.comuns.acesso.AcessoColaboradorVO;
import negocio.comuns.arquitetura.UsuarioVO;
import negocio.comuns.basico.ColaboradorVO;
import negocio.comuns.basico.EmpresaVO;
import negocio.comuns.basico.PessoaVO;
import negocio.comuns.basico.enumerador.TipoPessoa;
import negocio.comuns.financeiro.CartaoCreditoTO;
import negocio.comuns.financeiro.ChequeTO;
import negocio.comuns.financeiro.FiltroLancamentosTO;
import negocio.comuns.financeiro.LoteVO;
import negocio.comuns.financeiro.MovContaVO;
import negocio.comuns.utilitarias.Calendario;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisValidacao;
import negocio.facade.jdbc.arquitetura.consulta.ColunaPrefixavel;
import negocio.facade.jdbc.arquitetura.consulta.ConsultaFacade;
import negocio.facade.jdbc.basico.TipoColaborador;
import negocio.interfaces.financeiro.LancamentosMovContaFacade;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang.StringUtils;

import java.sql.Connection;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.sql.Statement;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;

import static negocio.facade.jdbc.arquitetura.consulta.ConsultaUtil.fecharResultSetQuietly;
import static negocio.facade.jdbc.arquitetura.consulta.ConsultaUtil.fecharStatementQuietly;
import static negocio.facade.jdbc.arquitetura.consulta.ConsultaUtil.getColunaPrefixada;
import static negocio.facade.jdbc.arquitetura.consulta.ConsultaUtil.getColunasPrefixadas;

/**
 * <AUTHOR> Karlus
 * @since 14/08/2018
 */
public class LancamentosMovConta extends ConsultaFacade implements LancamentosMovContaFacade {

    private static final String SOMA = "soma";
    private static final String TOTAL = "total";
    private static final String TIPO = "tipo";

    /**
     * @throws Exception Caso haja algum problema na preparação da conexão com o banco
     */
    public LancamentosMovConta() throws Exception {
        super();
    }

    public LancamentosMovConta(Connection conexao) throws Exception {
        super(conexao);
    }

    @Override
    public List<MovContaVO> consultar(ResultSet tabelaResultado, Boolean isAgruparPorPlanoContasCentroCusto, Boolean isIntegracaoAlterData, Boolean marcarTodos) throws Exception {
        List<MovContaVO> movContas = new ArrayList<MovContaVO>();
        Map<Integer, MovContaVO> movContasRetiradaRecebivelAgrupadosCodigo = new HashMap<Integer, MovContaVO>();

        while (tabelaResultado.next()) {
            final MovContaVO movConta = montarMovConta(tabelaResultado, isAgruparPorPlanoContasCentroCusto, isIntegracaoAlterData);
            if (!movConta.isExcluido()){
                if (movConta.getTipoOperacaoLancamento().equals(TipoOperacaoLancamento.PAGAMENTO)
                        && movConta.getDataQuitacao() == null) {
                    movConta.setLancamentoSelecionado(marcarTodos);
                }
            }
            if (movConta.getTipoOperacaoLancamento().equals(TipoOperacaoLancamento.RETIRADA_RECEBIVEL_LOTE)) {
                movContasRetiradaRecebivelAgrupadosCodigo.put(movConta.getCodigo(), movConta);
            }

            movContas.add(movConta);
        }

        for (ChequeCartaoVO chequeCartaoVO : montarChequesCartoes(movContasRetiradaRecebivelAgrupadosCodigo.keySet())) {
            MovContaVO movContaVO = movContasRetiradaRecebivelAgrupadosCodigo.get(chequeCartaoVO.getMovContaID());
            if (chequeCartaoVO.getCheque() != null) {
                movContaVO.getChequesRetirados().add(chequeCartaoVO.getCheque());
            } else if (chequeCartaoVO.getCartao() != null) {
                movContaVO.getCartoesRetirados().add(chequeCartaoVO.getCartao());
            }
        }

        return movContas;
    }

    @Override
    public String getSQLSelect(FiltroLancamentosTO filtros) throws Exception {
        return getSQLSelect(filtros, false);
    }

    @Override
    public String getSQLSelect(FiltroLancamentosTO filtros, Boolean agruparParaTotalizacao) throws Exception {
        StringBuilder sqlSelect = new StringBuilder(SELECT);

        if (agruparParaTotalizacao) {
            sqlSelect.append(SUM).append("(\"").append(ColunasMovConta.VALOR.getLabelComPrefixo()).append("\")").append(AS).append(SOMA).append(",")
                    .append(contar()).append(AS).append(TOTAL).append(",")
                    .append("\"").append(ColunasMovConta.TIPO_OPERACAO.getLabelComPrefixo()).append("\"").append(AS).append(TIPO)
                    .append(" FROM (")
                    .append(SELECT).append(DISTINCT).append("(").append(ColunasMovConta.CODIGO.getLabelComPrefixo()).append("),")
                    .append(getColunasPrefixadas(ColunasMovConta.values()));
        } else {
            if (filtros.isAgruparPorPlanoDeContasCentroCusto()) {
                sqlSelect.append(ColunasMovConta.CODIGO.getLabelComPrefixo()).append(",");
            } else {
                sqlSelect.append(DISTINCT).append("(").append(ColunasMovConta.CODIGO.getLabelComPrefixo()).append("),");
            }

            sqlSelect.append(getColunasPrefixadas(ColunasMovConta.values())).append(",")
                    .append(getColunasPrefixadas(ColunasUsuario.values())).append(",")
                    .append(getColunasPrefixadas(ColunasLote.values())).append(",")
                    .append(getColunasPrefixadas(ColunasColaborador.values())).append(",")
                    .append(getColunasPrefixadas(ColunasColaboradorPessoa.values())).append(",")
                    .append(getColunaPrefixada(ColunasEmpresa.NOME)).append(",")
                    .append(getColunaPrefixada(ColunasPessoa.NOME)).append(",")
                    .append(getColunaPrefixada(ColunasConta.DESCRICAO)).append(",")
                    .append("nfseemitida.rps as \"nfseemitida.rps\"").append(",")
                    .append(getColunaPrefixada(ColunasCaixa.CODIGO));

            if (filtros.isAgruparPorPlanoDeContasCentroCusto()) {
                sqlSelect
                        .append(",")
                        .append(concatenarColunas(ColunasPlanoConta.CODIGO_PLANO_CONTAS, ColunasPlanoConta.NOME, ColunasPlanoConta.NOME_PLANO_CONTA)).append(",")
                        .append(concatenarColunas(ColunasCentroCusto.CODIGO_CENTRO_CUSTOS, ColunasCentroCusto.NOME, ColunasCentroCusto.NOME_CENTRO_CUSTO)).append(",")
                        .append(getColunaPrefixada(ColunasMovContaRateio.VALOR)).append(",")
                        .append(getColunaPrefixada(ColunasMovContaRateio.DESCRICAO));
            }
        }

        sqlSelect.append(" FROM movconta ").append(ColunasMovConta.getPrefixo())
                .append(leftJoin("conta", ColunasConta.getPrefixo(), ColunasConta.CODIGO, ColunasMovConta.CONTA));

        // planos de contas selecionados e centro de custos
        sqlSelect.append(innerJoin("movcontarateio", ColunasMovContaRateio.getPrefixo(), ColunasMovContaRateio.MOV_CONTA, ColunasMovConta.CODIGO));
        if (StringUtils.isNotBlank(filtros.getCodigosPlanoContas())) {
            sqlSelect.append(andIn(ColunasMovContaRateio.PLANO_CONTA, filtros.getCodigosPlanoContas()));
        }
        if (StringUtils.isNotBlank(filtros.getCodigosCentroCustos())) {
            sqlSelect.append(andIn(ColunasMovContaRateio.CENTRO_CUSTO, filtros.getCodigosCentroCustos()));
        }

        if (filtros.isAgruparPorPlanoDeContasCentroCusto() && !agruparParaTotalizacao) {
            sqlSelect
                    .append(leftJoin("planoConta", ColunasPlanoConta.getPrefixo(), ColunasPlanoConta.CODIGO, ColunasMovContaRateio.PLANO_CONTA))
                    .append(leftJoin("centroCusto", ColunasCentroCusto.getPrefixo(), ColunasCentroCusto.CODIGO, ColunasMovContaRateio.CENTRO_CUSTO));
        }

        sqlSelect.append(innerJoin("empresa", ColunasEmpresa.getPrefixo(), ColunasEmpresa.CODIGO, ColunasMovConta.EMPRESA));
        if (filtros.getAgendamentosAndamento()) {
            sqlSelect
                    .append(innerJoin("agendamentofinanceiro", ColunasAgendamentoFinanceiro.getPrefixo(), ColunasAgendamentoFinanceiro.CODIGO, ColunasMovConta.AGENDAMENTO_FINANCEIRO))
                    .append(andIsNull(ColunasAgendamentoFinanceiro.VENCIMENTO_ULTIMA_PARCELA))
                    .append(AND).append(" (").append(isNull(ColunasAgendamentoFinanceiro.PARCELA_INI)).append(OR).append(igual(ColunasAgendamentoFinanceiro.PARCELA_INI, 0)).append(")")

                    .append(getUltimosLancamentosAgendadosLeftJoin());
        }

        sqlSelect.append(leftJoin("pessoa", ColunasPessoa.getPrefixo(), ColunasPessoa.CODIGO, ColunasMovConta.PESSOA));

        // filtro de pesquisa por cheque...
        boolean contemCodigoBanco = filtros.getChequeVO().getBanco().getCodigo() != null && filtros.getChequeVO().getBanco().getCodigo() != 0;
        if (contemCodigoBanco
                || StringUtils.isNotBlank(filtros.getChequeVO().getAgencia())
                || StringUtils.isNotBlank(filtros.getChequeVO().getConta())
                || StringUtils.isNotBlank(filtros.getChequeVO().getNumero())) {

            sqlSelect.append(innerJoin("cheque", ColunasCheque.getPrefixo(), ColunasCheque.MOV_CONTA, ColunasMovConta.CODIGO));
        }

        sqlSelect.append(leftJoin("lote", ColunasLote.getPrefixo(), ColunasLote.CODIGO, ColunasMovConta.LOTE)).append(" \n");

        //Tem um problema que foi identificado que acontece desde 2019, onde uma mesma MovConta  quitada em dois caixas com datas distintas.
        //Por isso, o mesmo  exibindo na tela de Conta a Pagar de forma Duplicada, uma para cada Caixa.
        //Como no encontramos a causa Raiz, resolvemos tratar na consulta, por entender que não faz sentido uma conta em dois caixa.
        sqlSelect.append("LEFT JOIN ( \n");
        sqlSelect.append("    SELECT caixamovconta1.movconta, MIN(caixamovconta1.codigo) AS caixamovconta_codigo \n");
        sqlSelect.append("    FROM caixamovconta caixamovconta1 \n");
        sqlSelect.append("    GROUP BY caixamovconta1.movconta \n");
        sqlSelect.append(") AS maisantigo_caixamovconta ON maisantigo_caixamovconta.movconta = movConta.codigo \n");

        sqlSelect.append("LEFT JOIN caixa caixa ON caixa.codigo = maisantigo_caixamovconta.caixamovconta_codigo \n");

        sqlSelect.append(innerJoin("usuario", ColunasUsuario.getPrefixo(), ColunasUsuario.CODIGO, ColunasMovConta.USUARIO))
                .append(leftJoin("colaborador", ColunasColaborador.getPrefixo(), ColunasColaborador.CODIGO, ColunasUsuario.COLABORADOR))
                .append(leftJoin("pessoa", ColunasColaboradorPessoa.getPrefixo(), ColunasColaboradorPessoa.CODIGO, ColunasColaborador.PESSOA))
                .append("\nleft join nfseemitida nfseemitida on\n" +
                        "\tmovconta.codigo = nfseemitida.movconta")

                .append(getClausulaWhere(filtros));

        if (agruparParaTotalizacao) {
            sqlSelect.append(")").append(AS).append("totalizacao")
                    .append(GROUP_BY).append("\"").append(ColunasMovConta.TIPO_OPERACAO.getLabelComPrefixo()).append("\"");
        } else {
            sqlSelect.append(ordenacao(filtros.isAgruparPorPlanoDeContasCentroCusto()));
        }

        return sqlSelect.toString();
    }

    private String ordenacao(boolean agruparPorPlanoDeContasCentroCusto) {
        return agruparPorPlanoDeContasCentroCusto ?
                ORDER_BY + ColunasPlanoConta.NOME_PLANO_CONTA :
                ORDER_BY + ColunasMovConta.DATA_VENCIMENTO.getLabelComPrefixo() + DESC;
    }

    private List<ChequeCartaoVO> montarChequesCartoes(Set<Integer> movContaIDs) throws SQLException {
        if (movContaIDs == null || movContaIDs.size() == 0) {
            return new ArrayList<ChequeCartaoVO>();
        }

        final List<ChequeCartaoVO> chequesCartoesVO = new ArrayList<ChequeCartaoVO>(movContaIDs.size());
        final String SQL_CHEQUES_CARTOES_RETIRADOS = getSQLSelectChequesCartoes(movContaIDs);
        System.out.println(SQL_CHEQUES_CARTOES_RETIRADOS);

        Statement statement = null;
        ResultSet tabelaResultados = null;

        try {
            statement = con.createStatement();
            tabelaResultados = statement.executeQuery(SQL_CHEQUES_CARTOES_RETIRADOS);

            while (tabelaResultados.next()) {
                ChequeCartaoVO chequeCartaoVO = new ChequeCartaoVO();
                ChequeTO cheque = montarCheque(tabelaResultados);
                CartaoCreditoTO cartaoCredito = montarCartaoCredito(tabelaResultados);

                if (cheque != null) {
                    chequeCartaoVO.setMovContaID(cheque.getMovConta());
                    chequeCartaoVO.setCheque(cheque);
                } else if (cartaoCredito != null) {
                    chequeCartaoVO.setMovContaID(cartaoCredito.getMovConta());
                    chequeCartaoVO.setCartao(cartaoCredito);
                }

                chequesCartoesVO.add(chequeCartaoVO);
            }

        } finally {
            fecharResultSetQuietly(tabelaResultados);
            fecharStatementQuietly(statement);
        }

        return chequesCartoesVO;
    }

    private ChequeTO montarCheque(ResultSet tabelaResultado) throws SQLException {
        Integer codigoCheque;
        try {
            codigoCheque = tabelaResultado.getInt(ColunasCheque.CODIGO_CHEQUE.getLabelComPrefixo());
            if (codigoCheque == 0) {
                return null;
            }
        } catch (Exception e) {
            return null;
        }

        ChequeTO cheque = new ChequeTO();
        cheque.setCodigo(codigoCheque);
        cheque.setAgencia(tabelaResultado.getString(ColunasCheque.AGENCIA.getLabelComPrefixo()));
        cheque.setDataCompensacao(tabelaResultado.getDate(ColunasCheque.DATA_COMPENSACAO.getLabelComPrefixo()));
        cheque.setDataLancamento(tabelaResultado.getDate(ColunasCheque.DATA_LANCAMENTO.getLabelComPrefixo()));
        cheque.setValor(tabelaResultado.getDouble(ColunasCheque.VALOR.getLabelComPrefixo()));
        cheque.setConta(tabelaResultado.getString(ColunasCheque.CONTA.getLabelComPrefixo()));
        cheque.setNumero(tabelaResultado.getString(ColunasCheque.NUMERO.getLabelComPrefixo()));
        cheque.setNomePagador(tabelaResultado.getString(ColunasCheque.NOME.getLabelComPrefixo()));
        cheque.setNumeroBanco(tabelaResultado.getString(ColunasCheque.BANCO.getLabelComPrefixo()));
        cheque.setMovConta(tabelaResultado.getInt(ColunasCheque.MOV_CONTA.getLabelComPrefixo()));
        cheque.setRemovido(true);
        return cheque;
    }

    private CartaoCreditoTO montarCartaoCredito(ResultSet tabelaResultado) throws SQLException {
        Integer codigoCartao;
        try {
            codigoCartao = tabelaResultado.getInt(ColunasCartao.CODIGO_CARTAO.getLabelComPrefixo());
            if (codigoCartao == 0) {
                return null;
            }
        } catch (Exception e) {
            return null;
        }

        CartaoCreditoTO cartao = new CartaoCreditoTO();
        cartao.setCodigo(codigoCartao);
        cartao.setDataCompensacao(tabelaResultado.getDate(ColunasCartao.DATA_COMPENSACAO.getLabelComPrefixo()));
        cartao.setDataLancamento(tabelaResultado.getDate(ColunasCartao.DATA_LANCAMENTO.getLabelComPrefixo()));
        cartao.setValor(tabelaResultado.getDouble(ColunasCartao.VALOR.getLabelComPrefixo()));
        cartao.setOperadora(tabelaResultado.getString(ColunasCartao.OPERADORA.getLabelComPrefixo()));
        cartao.setNomePagador(tabelaResultado.getString(ColunasCartao.NOME.getLabelComPrefixo()));
        cartao.setAutorizacao(tabelaResultado.getString(ColunasCartao.AUTORIZACAO.getLabelComPrefixo()));
        cartao.setMovConta(tabelaResultado.getInt(ColunasCartao.MOV_CONTA.getLabelComPrefixo()));
        cartao.setRemovido(true);
        return cartao;
    }

    private String getSQLSelectChequesCartoes(Set<Integer> movContaIDs) {
        return new StringBuilder(SELECT)
                .append(getColunasPrefixadas(ColunasCheque.values())).append(",")
                .append(getColunasPrefixadas(ColunasCartao.values()))

                .append(" FROM chequeretiradolote ").append(ColunasCheque.getPrefixo())

                .append(fullOuterJoin("cartaoretiradolote", ColunasCartao.getPrefixo(), ColunasCartao.MOV_CONTA, ColunasCheque.MOV_CONTA))

                .append(WHERE)
                .append(in(ColunasCheque.MOV_CONTA, movContaIDs))
                .append(OR)
                .append(in(ColunasCartao.MOV_CONTA, movContaIDs))

                .toString();
    }

    private String getClausulaWhere(FiltroLancamentosTO filtros) throws Exception {
        final StringBuilder SQL_WHERE = new StringBuilder(WHERE).append(INIT_WHERE);

        //descricao
        if (StringUtils.isNotBlank(filtros.getDescricao())) {
            SQL_WHERE.append(andLikeInsensitiveBothSides(ColunasMovConta.DESCRICAO, filtros.getDescricao()));
        }

        if (StringUtils.isNotBlank(filtros.getNumeroDocumento())) {
            SQL_WHERE.append(andLikeInsensitiveBothSides(ColunasMovConta.NUMERO_DOCUMENTO, filtros.getNumeroDocumento()));
        }

        if (filtros.getAgendamentosAndamento()) {
            SQL_WHERE.append(andIsNull(ColunasAgendamentoFinanceiro.CODIGO));
        }

        if (filtros.getSomenteContasMobile()) {
            SQL_WHERE.append(andIsBoolean(ColunasMovConta.APP, true));
        }

        if (!UteisValidacao.emptyNumber(filtros.getCodigo())) {
            SQL_WHERE.append(andIgual(ColunasMovConta.CODIGO, filtros.getCodigo()));
        }

        if (!UteisValidacao.emptyNumber(filtros.getTipoLancamento())) {
            if (filtros.getTipoLancamento().equals(TipoOperacaoLancamento.FLUXO_CAIXA.getCodigo())) {
                SQL_WHERE.append(andIn(
                        ColunasMovConta.TIPO_OPERACAO,
                        Arrays.asList(TipoOperacaoLancamento.RECEBIMENTO.getCodigo(), TipoOperacaoLancamento.PAGAMENTO.getCodigo())));
            } else {
                SQL_WHERE.append(andIgual(ColunasMovConta.TIPO_OPERACAO, filtros.getTipoLancamento()));
            }
        }
        if (filtros.getPagamentosConjunto()) {
            SQL_WHERE.append(andIsNotNull(ColunasMovConta.CONJUNTO_PAGAMENTO));
        }

        //empresas selecionadas
        String empresas = filtros.getCodigosEmpresa();
        if (!empresas.isEmpty()) {
            SQL_WHERE.append(andIn(ColunasMovConta.EMPRESA, empresas));
        }

        //tipos documentos selecionados
        String doc = filtros.getCodigosTipoDocumento();
        if (!doc.isEmpty()) {
            SQL_WHERE.append(andIn(ColunasMovContaRateio.TIPO_DOCUMENTO, doc));
        }

        //contas selecionadas
        String conta = filtros.getCodigosConta();
        if (!conta.isEmpty()) {
            SQL_WHERE.append(andIn(ColunasMovConta.CONTA, conta));
        }

        //formas de pagamento selecionadas
        String formaPgto = filtros.getCodigosFormaPgto();
        if (!formaPgto.isEmpty()) {
            SQL_WHERE.append(andIn(ColunasMovContaRateio.FORMA_PAGAMENTO, formaPgto));
        }

        //favorecido
        if (CollectionUtils.isNotEmpty(filtros.getFavorecidosCodigos())) {
            SQL_WHERE.append(andIn(ColunasMovConta.PESSOA, filtros.getFavorecidosCodigos()));
        }

        //valores máximo e minimo
        if (!UteisValidacao.emptyNumber(filtros.getValorMaximo())) {
            SQL_WHERE.append(andEntre(ColunasMovConta.VALOR, filtros.getValorMinimo(), filtros.getValorMaximo()));
        }

        //data lancamento
        if (filtros.getInicioLancamento() != null && filtros.getFinalLancamento() != null) {
            SQL_WHERE.append(andEntre(
                    ColunasMovConta.DATA_LANCAMENTO,
                    "'" + Uteis.getDataJDBC(filtros.getInicioLancamento()) + " 00:00:00'",
                    "'" + Uteis.getDataJDBC(filtros.getFinalLancamento()) + " 23:59:59'"));
        }

        //data quitacao
        if (filtros.getInicioQuitacao() != null && filtros.getFinalQuitacao() != null) {
            SQL_WHERE.append(andEntre(
                    ColunasMovConta.DATA_QUITACAO,
                    "'" + Uteis.getDataJDBC(filtros.getInicioQuitacao()) + " 00:00:00'",
                    "'" + Uteis.getDataJDBC(filtros.getFinalQuitacao()) + " 23:59:59'"));
        }

        //data competencia
        if (filtros.getInicioCompetencia() != null && filtros.getFinalCompetencia() != null) {
            SQL_WHERE.append(andEntre(
                    ColunasMovConta.DATA_COMPETENCIA,
                    "'" + Uteis.getDataJDBC(filtros.getInicioCompetencia()) + " 00:00:00'",
                    "'" + Uteis.getDataJDBC(filtros.getFinalCompetencia()) + " 23:59:59'"));
        }

        //data vencimento
        if ((filtros.getTipoPeriodo() != null && filtros.getTipoPeriodo().equals(2)) || (filtros.getTipoPeriodo() != null && filtros.getTipoPeriodo().equals(4))) {
            SQL_WHERE.append(andEntre(
                    ColunasMovConta.DATA_VENCIMENTO,
                    "'" + Uteis.getDataJDBC(Uteis.obterPrimeiroEUltimoDiaSemana(true, Calendario.hoje())) + " 00:00:00'",
                    "'" + Uteis.getDataJDBC(Uteis.obterPrimeiroEUltimoDiaSemana(false,Calendario.hoje())) + " 23:59:59'"));
        }else if (filtros.getInicioVencimento() != null && filtros.getFinalVencimento() != null){
            SQL_WHERE.append(andEntre(
                    ColunasMovConta.DATA_VENCIMENTO,
                    "'" + Uteis.getDataJDBC(filtros.getInicioVencimento()) + " 00:00:00'",
                    "'" + Uteis.getDataJDBC(filtros.getFinalVencimento()) + " 23:59:59'"));
        }

        //data ultima alteração(inclusão, alteração e exclusão)
        if (filtros.getDataInicioUltimaAlteracao() != null && filtros.getDataFimUltimaAlteracao() != null) {
            SQL_WHERE.append(andEntre(
                    ColunasMovConta.DATA_ULTIMA_ALTERACAO,
                    "'" + Uteis.getDataJDBC(filtros.getDataInicioUltimaAlteracao()) + " 00:00:00'",
                    "'" + Uteis.getDataJDBC(filtros.getDataFimUltimaAlteracao()) + " 23:59:59'"));
        }

        if (filtros.getApenasNaoQuitados() && !filtros.getApenasQuitados()) {
            SQL_WHERE.append(andIsNull(ColunasMovConta.DATA_QUITACAO));
        }

        if (!filtros.getApenasNaoQuitados() && filtros.getApenasQuitados()) {
            SQL_WHERE.append(andIsNotNull(ColunasMovConta.DATA_QUITACAO));
        }

        //filtro de pagamento por cheque parametro 5: BANCO
        if (filtros.getChequeVO().getBanco().getCodigo() != null && filtros.getChequeVO().getBanco().getCodigo() != 0) {
            SQL_WHERE.append(andIgual(ColunasCheque.BANCO, filtros.getChequeVO().getBanco().getCodigo()));
        }

        //filtro de pagamento por cheque parametro 5: AGENCIA
        if (StringUtils.isNotBlank(filtros.getChequeVO().getAgencia())) {
            SQL_WHERE.append(andIgual(ColunasCheque.AGENCIA, "'" + filtros.getChequeVO().getAgencia() + "'"));
        }

        //filtro de pagamento por cheque parametro 5: NUMERO CONTA
        if (StringUtils.isNotBlank(filtros.getChequeVO().getConta())) {
            SQL_WHERE.append(andIgual(ColunasCheque.CONTA, "'" + filtros.getChequeVO().getConta() + "'"));
        }

        //filtro de pagamento por cheque parametro 5: NUMERO CHEQUE
        if (StringUtils.isNotBlank(filtros.getChequeVO().getNumero())) {
            SQL_WHERE.append(andIgual(ColunasCheque.CONTA, "'" + filtros.getChequeVO().getNumero() + "'"));
        }

        return SQL_WHERE.toString();
    }

    private PessoaVO montarColaboradorPessoaVO(ResultSet tabelaResultados) throws Exception {
        PessoaVO pessoaVO = new PessoaVO();
        pessoaVO.setNovoObj(false);
        pessoaVO.setCodigo(tabelaResultados.getInt(ColunasColaboradorPessoa.CODIGO.getLabelComPrefixo()));
        pessoaVO.setNome(tabelaResultados.getString(ColunasColaboradorPessoa.NOME.getLabelComPrefixo()));
        pessoaVO.getProfissao().setCodigo(tabelaResultados.getInt(ColunasColaboradorPessoa.PROFISSAO.getLabelComPrefixo()));
        pessoaVO.setDataCadastro(tabelaResultados.getDate(ColunasColaboradorPessoa.DATA_CADASTRO.getLabelComPrefixo()));
        pessoaVO.setDataNasc(tabelaResultados.getDate(ColunasColaboradorPessoa.DATA_NASC.getLabelComPrefixo()));
        pessoaVO.setNomePai(tabelaResultados.getString(ColunasColaboradorPessoa.NOME_PAI.getLabelComPrefixo()));
        pessoaVO.setNomeMae(tabelaResultados.getString(ColunasColaboradorPessoa.NOME_MAE.getLabelComPrefixo()));
        pessoaVO.setCfp(tabelaResultados.getString(ColunasColaboradorPessoa.CFP.getLabelComPrefixo()));
        pessoaVO.setLiberaSenhaAcesso(tabelaResultados.getBoolean(ColunasColaboradorPessoa.LIBERA_SENHA_ACESSO.getLabelComPrefixo()));
        pessoaVO.setRg(tabelaResultados.getString(ColunasColaboradorPessoa.RG.getLabelComPrefixo()));
        pessoaVO.setRgOrgao(tabelaResultados.getString(ColunasColaboradorPessoa.RG_ORGAO.getLabelComPrefixo()));
        pessoaVO.setRgUf(tabelaResultados.getString(ColunasColaboradorPessoa.RG_UF.getLabelComPrefixo()));
        pessoaVO.getCidade().setCodigo(tabelaResultados.getInt(ColunasColaboradorPessoa.CIDADE.getLabelComPrefixo()));
        pessoaVO.getEstadoVO().setCodigo(tabelaResultados.getInt(ColunasColaboradorPessoa.ESTADO.getLabelComPrefixo()));
        pessoaVO.getPais().setCodigo(tabelaResultados.getInt(ColunasColaboradorPessoa.PAIS.getLabelComPrefixo()));
        pessoaVO.setEstadoCivil(tabelaResultados.getString(ColunasColaboradorPessoa.ESTADO_CIVIL.getLabelComPrefixo()));
        pessoaVO.setNacionalidade(tabelaResultados.getString(ColunasColaboradorPessoa.NACIONALIDADE.getLabelComPrefixo()));
        pessoaVO.setNaturalidade(tabelaResultados.getString(ColunasColaboradorPessoa.NATURALIDADE.getLabelComPrefixo()));
        pessoaVO.setSexo(tabelaResultados.getString(ColunasColaboradorPessoa.SEXO.getLabelComPrefixo()));
        pessoaVO.setWebPage(tabelaResultados.getString(ColunasColaboradorPessoa.WEB_PAGE.getLabelComPrefixo()));
        pessoaVO.getGrauInstrucao().setCodigo(tabelaResultados.getInt(ColunasColaboradorPessoa.GRAU_INSTRUCAO.getLabelComPrefixo()));
        pessoaVO.setCategoriaPessoa(TipoPessoa.obterPorCodigo(tabelaResultados.getInt(ColunasColaboradorPessoa.TIPO_PESSOA.getLabelComPrefixo())));
        pessoaVO.setFotoKey(tabelaResultados.getString(ColunasColaboradorPessoa.FOTO_KEY.getLabelComPrefixo()));
        pessoaVO.setIdVindi(tabelaResultados.getInt(ColunasColaboradorPessoa.ID_VINDI.getLabelComPrefixo()));
        pessoaVO.setDataAlteracaoVindi(tabelaResultados.getDate(ColunasColaboradorPessoa.DATA_ALTERACAO_VINDI.getLabelComPrefixo()));
        pessoaVO.setIdMaxiPago(tabelaResultados.getString(ColunasColaboradorPessoa.ID_MAXI_PAGO.getLabelComPrefixo()));
        pessoaVO.setDataAlteracaoMaxiPago(tabelaResultados.getDate(ColunasColaboradorPessoa.DATA_ALTERACAO_MAXI_PAGO.getLabelComPrefixo()));

        try {
            pessoaVO.setFoto(tabelaResultados.getBytes(ColunasColaboradorPessoa.FOTO.getLabelComPrefixo()));
            pessoaVO.setCategoriaPessoa(TipoPessoa.obterPorCodigo(tabelaResultados.getInt(ColunasColaboradorPessoa.TIPO_PESSOA.getLabelComPrefixo())));
            pessoaVO.setCnpj(tabelaResultados.getString(ColunasColaboradorPessoa.CNPJ.getLabelComPrefixo()));
            pessoaVO.setInscEstadual(tabelaResultados.getString(ColunasColaboradorPessoa.INSC_ESTADUAL.getLabelComPrefixo()));
            pessoaVO.setInscMunicipal(tabelaResultados.getString(ColunasColaboradorPessoa.INSC_MUNICIPAL.getLabelComPrefixo()));
            pessoaVO.setCfdf(tabelaResultados.getString(ColunasColaboradorPessoa.CFDF.getLabelComPrefixo()));
            pessoaVO.setEmitirNotaNomeMae(tabelaResultados.getBoolean(ColunasColaboradorPessoa.EMITIR_NOTA_NOME_MAE.getLabelComPrefixo()));
        } catch (Exception ignored) {
        }

        return pessoaVO;
    }

    private UsuarioVO montarUsuarioVO(ResultSet tabelaResultado) throws Exception {
        UsuarioVO usuarioVO = new UsuarioVO();
        usuarioVO.setNovoObj(false);
        usuarioVO.setCodigo(tabelaResultado.getInt(ColunasUsuario.CODIGO.getLabelComPrefixo()));
        usuarioVO.setNome(tabelaResultado.getString(ColunasUsuario.NOME.getLabelComPrefixo()));
        usuarioVO.getColaboradorVO().setCodigo(tabelaResultado.getInt(ColunasUsuario.COLABORADOR.getLabelComPrefixo()));
        usuarioVO.setUsername(tabelaResultado.getString(ColunasUsuario.USERNAME.getLabelComPrefixo()));
        usuarioVO.setSenha(tabelaResultado.getString(ColunasUsuario.SENHA.getLabelComPrefixo()));
        usuarioVO.setSenhaConfirmar(usuarioVO.getSenha());
        usuarioVO.setTipoUsuario(tabelaResultado.getString(ColunasUsuario.TIPO_USUARIO.getLabelComPrefixo()));
        usuarioVO.setAdministrador(tabelaResultado.getBoolean(ColunasUsuario.ADMINISTRADOR.getLabelComPrefixo()));
        usuarioVO.getClienteVO().setCodigo(tabelaResultado.getInt(ColunasUsuario.CLIENTE.getLabelComPrefixo()));

        try {
            //estes campos podem ainda não existir no momento do login do usuário, por isso mascara-se as exceções de 'coluna não encontrada neste ResultSet'
            usuarioVO.setDataUltimaAlteracaoSenha(tabelaResultado.getTimestamp(ColunasUsuario.DATA_ALTERACAO_SENHA.getLabelComPrefixo()));
            usuarioVO.setPermiteAlterarPropriaSenha(tabelaResultado.getBoolean(ColunasUsuario.PERMITE_ALTERAR_PROPRIA_SENHA.getLabelComPrefixo()));
            usuarioVO.setServiceUsuario(tabelaResultado.getString(ColunasUsuario.SERVICE_USUARIO.getLabelComPrefixo()));
            usuarioVO.setServiceSenha(tabelaResultado.getString(ColunasUsuario.SERVICE_SENHA.getLabelComPrefixo()));
            usuarioVO.setPedirSenhaFuncionalidade(tabelaResultado.getBoolean(ColunasUsuario.PEDIR_SENHA_FUNCIONALIDADE.getLabelComPrefixo()));
        } catch (Exception e) {
            //dataalteracaosenha nao existira na primeira vez rodada
        }

        return usuarioVO;
    }

    private ColaboradorVO montarColaboradorVO(ResultSet tabelaResultados) throws Exception {
        ColaboradorVO colaboradorVO = new ColaboradorVO();
        colaboradorVO.setNovoObj(false);
        colaboradorVO.setCodigo(tabelaResultados.getInt(ColunasColaborador.CODIGO.getLabelComPrefixo()));
        colaboradorVO.getPessoa().setCodigo(tabelaResultados.getInt(ColunasColaborador.PESSOA.getLabelComPrefixo()));
        colaboradorVO.getEmpresa().setCodigo(tabelaResultados.getInt(ColunasColaborador.EMPRESA.getLabelComPrefixo()));
        colaboradorVO.setCodAcesso(tabelaResultados.getString(ColunasColaborador.COD_ACESSO.getLabelComPrefixo()));
        colaboradorVO.setSituacao(tabelaResultados.getString(ColunasColaborador.SITUACAO.getLabelComPrefixo()));
        colaboradorVO.setCodAcessoAlternativo(tabelaResultados.getString(ColunasColaborador.COD_ACESSO_ALTERNATIVO.getLabelComPrefixo()));
        colaboradorVO.setFuncionario(tabelaResultados.getBoolean(ColunasColaborador.FUNCIONARIO.getLabelComPrefixo()));
        colaboradorVO.setUaColaborador(new AcessoColaboradorVO());
        colaboradorVO.getUaColaborador().setCodigo(tabelaResultados.getInt(ColunasColaborador.UA_CODIGO.getLabelComPrefixo()));
        colaboradorVO.setDiaVencimento(tabelaResultados.getInt(ColunasColaborador.DIA_VENCIMENTO.getLabelComPrefixo()));
        colaboradorVO.getProdutoDefault().setCodigo(tabelaResultados.getInt(ColunasColaborador.PRODUTO_DEFAULT.getLabelComPrefixo()));
        colaboradorVO.setPorcComissao(tabelaResultados.getDouble(ColunasColaborador.PORC_COMISSAO.getLabelComPrefixo()));
        colaboradorVO.setCorAgendaProfissional(tabelaResultados.getString(ColunasColaborador.COR_AGENDA_PROFISSIONAL.getLabelComPrefixo()));
        colaboradorVO.setCref(tabelaResultados.getString(ColunasColaborador.CREF.getLabelComPrefixo()));
        try {
            colaboradorVO.setTokenGoogle(tabelaResultados.getString(ColunasColaborador.TOKEN_GOOGLE.getLabelComPrefixo()));
//            colaboradorVO.setTipoColaborador(tabelaResultados.getString(ColunasColaborador.TIPO_COLABORADOR.getLabelComPrefixo()));
            colaboradorVO.setUsoCreditosPersonal(tabelaResultados.getInt(ColunasColaborador.USO_CREDITOS_PERSONAL.getLabelComPrefixo()));
            colaboradorVO.setSaldoCreditoPersonal(tabelaResultados.getInt(ColunasColaborador.SALDO_CREDITO_PERSONAL.getLabelComPrefixo()));
            colaboradorVO.setEmAtendimentoPersonal(tabelaResultados.getBoolean(ColunasColaborador.EM_ATENDIMENTO_PERSONAL.getLabelComPrefixo()));
            colaboradorVO.setTempoEntreAcessos(tabelaResultados.getInt(ColunasColaborador.TEMPO_ENTRE_ACESSOS.getLabelComPrefixo()));
            colaboradorVO.getDepartamentoVO().setCodigo(tabelaResultados.getInt(ColunasColaborador.DEPARTAMENTO.getLabelComPrefixo()));
        } catch (Exception ignored) {
        }

        colaboradorVO.setColaboradorEscolhidoIndiceConversao(false);
        colaboradorVO.setColaboradorEscolhidoPendencia(false);
        colaboradorVO.setListaTipoColaboradorVOs(new TipoColaborador(con)
                .consultarTipoColaborador(colaboradorVO.getCodigo().intValue(), Uteis.NIVELMONTARDADOS_ORGANIZADORCARTEIRA));

        colaboradorVO.setPessoa(montarColaboradorPessoaVO(tabelaResultados));

        return colaboradorVO;
    }

    private LoteVO montarLoteVO(ResultSet tabelaResultado) throws SQLException {
        LoteVO loteVO = new LoteVO();
        loteVO.setNovoObj(false);
        loteVO.setEmpresa(new EmpresaVO());
        loteVO.getEmpresa().setCodigo(tabelaResultado.getInt(ColunasLote.EMPRESA.getLabelComPrefixo()));
        loteVO.setCodigo(tabelaResultado.getInt(ColunasLote.CODIGO.getLabelComPrefixo()));
        loteVO.setUsuarioResponsavel(new UsuarioVO());
        loteVO.getUsuarioResponsavel().setCodigo(tabelaResultado.getInt(ColunasLote.USUARIO_RESPONSAVEL.getLabelComPrefixo()));
        loteVO.setDescricao(tabelaResultado.getString(ColunasLote.DESCRICAO.getLabelComPrefixo()));
        loteVO.setDataLancamento(tabelaResultado.getTimestamp(ColunasLote.DATA_LANCAMENTO.getLabelComPrefixo()));
        loteVO.setDataDeposito(tabelaResultado.getDate(ColunasLote.DATA_DEPOSITO.getLabelComPrefixo()));
        loteVO.setValor(tabelaResultado.getDouble(ColunasLote.VALOR.getLabelComPrefixo()));
        loteVO.setPagaMovConta(tabelaResultado.getInt(ColunasLote.PAGAMO_VCONTA.getLabelComPrefixo()));
        loteVO.setAvulso(tabelaResultado.getBoolean(ColunasLote.AVULSO.getLabelComPrefixo()));
        return loteVO;
    }

    private MovContaVO montarMovConta(ResultSet tabelaResultado, Boolean isAgruparPlanoContasCentroCusto,
                                      Boolean isIntegracaoAlterData) throws Exception {

        MovContaVO movConta = new MovContaVO();
        movConta.setCodigo(tabelaResultado.getInt(ColunasMovConta.CODIGO.getLabelComPrefixo()));
        movConta.setDescricao(tabelaResultado.getString(ColunasMovConta.DESCRICAO.getLabelComPrefixo()));
        movConta.setValor(tabelaResultado.getDouble(ColunasMovConta.VALOR.getLabelComPrefixo()));
        movConta.setDataUltimaAlteracao(tabelaResultado.getTimestamp(ColunasMovConta.DATA_ULTIMA_ALTERACAO.getLabelComPrefixo()));
        movConta.setCodigoBarras(tabelaResultado.getString(ColunasMovConta.CODIGOBARRAS.getLabelComPrefixo()));
        movConta.setTipoOperacaoLancamento(TipoOperacaoLancamento.getTipoOperacaoLancamento(tabelaResultado.getInt(ColunasMovConta.TIPO_OPERACAO.getLabelComPrefixo())));
        movConta.getPessoaVO().setCodigo(tabelaResultado.getInt(ColunasMovConta.PESSOA.getLabelComPrefixo()));
        movConta.setNumeroDocumento(tabelaResultado.getString(ColunasMovConta.NUMERO_DOCUMENTO.getLabelComPrefixo()));
        movConta.setRetiradaAutomaticaRecebivelOrigemCancelamento(tabelaResultado.getBoolean(ColunasMovConta.RETIRADA_AUTOMATICA_RECEBIVEL_LOTE_ORIGEM_CANCELAMENTO.getLabelComPrefixo()));
        try {
            movConta.setApp(tabelaResultado.getBoolean(ColunasMovConta.APP.getLabelComPrefixo()));
//            movConta.getPessoaVO().setCfp(tabelaResultado.getString(ColunasMovConta.CPF_PESSOA.getLabelComPrefixo()));
//            movConta.setMatricula(tabelaResultado.getString(ColunasMovConta.MATRICULA.getLabelComPrefixo()));
        } catch (Exception ignored) {
            // excecao ignorada...
        }

        movConta.getUsuarioVO().setCodigo(tabelaResultado.getInt(ColunasMovConta.USUARIO.getLabelComPrefixo()));
        movConta.getEmpresaVO().setCodigo(tabelaResultado.getInt(ColunasMovConta.EMPRESA.getLabelComPrefixo()));
        movConta.getContaVO().setCodigo(tabelaResultado.getInt(ColunasMovConta.CONTA.getLabelComPrefixo()));
        movConta.setObservacoes(tabelaResultado.getString(ColunasMovConta.OBSERVACOES.getLabelComPrefixo()));
        movConta.setDataQuitacao(tabelaResultado.getTimestamp(ColunasMovConta.DATA_QUITACAO.getLabelComPrefixo()));
        movConta.setDataLancamento(tabelaResultado.getTimestamp(ColunasMovConta.DATA_LANCAMENTO.getLabelComPrefixo()));
        movConta.setDataCompetencia(tabelaResultado.getTimestamp(ColunasMovConta.DATA_COMPETENCIA.getLabelComPrefixo()));
        movConta.setDataVencimento(tabelaResultado.getTimestamp(ColunasMovConta.DATA_VENCIMENTO.getLabelComPrefixo()));
        movConta.setAgendamentoFinanceiro(tabelaResultado.getInt(ColunasMovConta.AGENDAMENTO_FINANCEIRO.getLabelComPrefixo()));
        movConta.setNrParcela(tabelaResultado.getInt(ColunasMovConta.NR_PARCELA.getLabelComPrefixo()));
        movConta.getLote().setCodigo(tabelaResultado.getInt(ColunasMovConta.LOTE.getLabelComPrefixo()));
        movConta.setContaOrigem(tabelaResultado.getInt(ColunasMovConta.CONTA_ORIGEM.getLabelComPrefixo()));
        movConta.setApresentarNoCaixa(tabelaResultado.getBoolean(ColunasMovConta.APRESENTAR_NO_CAIXA.getLabelComPrefixo()));
        movConta.setNfseEmitida(tabelaResultado.getBoolean(ColunasMovConta.NFSE_EMITIDA.getLabelComPrefixo()));
        movConta.setLotePagouConta(new LoteVO());
        movConta.getLotePagouConta().setCodigo(tabelaResultado.getInt(ColunasMovConta.LOTE_PAGOU_CONTA.getLabelComPrefixo()));

        if (movConta.getLote().getCodigo() != null && movConta.getLote().getCodigo() != 0) {
            movConta.setLote(montarLoteVO(tabelaResultado));
        } else {
            movConta.setLote(new LoteVO());
        }

        movConta.setConjuntoPagamento(tabelaResultado.getString(ColunasMovConta.CONJUNTO_PAGAMENTO.getLabelComPrefixo()));
        movConta.setNovoObj(false);
        movConta.setCaixa(tabelaResultado.getInt(ColunasCaixa.CODIGO.getLabelComPrefixo()));
        if (isIntegracaoAlterData) {
            movConta.setMovContaContabilVO(getFacade().getFinanceiro().getMovContaContabil().consultar(movConta.getCodigo()));
        }

        movConta.getEmpresaVO().setNome(tabelaResultado.getString(ColunasEmpresa.NOME.getLabelComPrefixo()));
        movConta.getPessoaVO().setNome(tabelaResultado.getString(ColunasPessoa.NOME.getLabelComPrefixo()));
        movConta.getContaVO().setDescricao(tabelaResultado.getString(ColunasConta.DESCRICAO.getLabelComPrefixo()));

        movConta.setUsuarioVO(montarUsuarioVO(tabelaResultado));
        movConta.setCodigoNotaEmitida(tabelaResultado.getInt("nfseemitida.rps"));
        movConta.setChequesRetirados(new ArrayList<ChequeTO>());
        movConta.setCartoesRetirados(new ArrayList<CartaoCreditoTO>());

        Double valorPagoMovConta = 0.0;
        Double valorOriginalAlteradoMovConta = 0.0;
        try {
            valorPagoMovConta = tabelaResultado.getDouble(ColunasMovConta.VALOR_PAGO.getLabelComPrefixo());
            valorOriginalAlteradoMovConta = tabelaResultado.getDouble(ColunasMovConta.VALOR_ORIGINAL_ALTERADO.getLabelComPrefixo());
            movConta.setValorPago(valorPagoMovConta);
            movConta.setValorOriginalAlterado(valorOriginalAlteradoMovConta);
        } catch (Exception ignored) {}

        if (isAgruparPlanoContasCentroCusto) {
            movConta.setNomePlanoConta(tabelaResultado.getString(ColunasPlanoConta.NOME_PLANO_CONTA));
            movConta.setNomeCentroCusto(tabelaResultado.getString(ColunasCentroCusto.NOME_CENTRO_CUSTO));
            movConta.setDescricao(tabelaResultado.getString(ColunasMovContaRateio.DESCRICAO.getLabelComPrefixo()));
            Double valorMovContaRateio = tabelaResultado.getDouble(ColunasMovContaRateio.VALOR.getLabelComPrefixo());
            movConta.setValor(valorMovContaRateio);
            if (!UteisValidacao.emptyNumber(valorPagoMovConta) && movConta.getDataQuitacao() != null) {
                movConta.setValorPago(valorMovContaRateio);
                if (movConta.getDescricao().contains("VALOR PAGO SUPERIOR") ||
                        movConta.getValorPago() < movConta.getValorOriginalAlterado()) {
                    movConta.setValorOriginalAlterado((valorMovContaRateio * valorOriginalAlteradoMovConta) / valorOriginalAlteradoMovConta);
                }
            } else {
                movConta.setValorPago(0.0);
                movConta.setValorOriginalAlterado(tabelaResultado.getDouble(ColunasMovContaRateio.VALOR.getLabelComPrefixo()));
            }
        }

        try {
            movConta.setNumeroDocumento(tabelaResultado.getString(ColunasMovConta.NUMERO_DOCUMENTO.getLabelComPrefixo()));
        } catch (Exception ignored) {
        }
        try {
            movConta.setPayloadPix(tabelaResultado.getString(ColunasMovConta.PAYLOAD_PIX.getLabelComPrefixo()));
        } catch (Exception ignored) {
        }
        try {
            movConta.setCpfOuCnpjBeneficiario(tabelaResultado.getString(ColunasMovConta.CPF_OU_CNPJ_BENEFICIARIO.getLabelComPrefixo()));
        } catch (Exception ignored) {
        }
        try {
            movConta.setContaDeConsumo(tabelaResultado.getBoolean(ColunasMovConta.CONTA_DE_CONSUMO.getLabelComPrefixo()));
        } catch (Exception ignored) {
        }
        try {
            movConta.setPagoOrigemWebhook(tabelaResultado.getBoolean(ColunasMovConta.PAGO_ORIGEM_WEBHOOK.getLabelComPrefixo()));
        } catch (Exception ignored) {
        }
        try {
            movConta.setPresaEmLoteDePagamento(tabelaResultado.getBoolean(ColunasMovConta.PRESA_EM_LOTE_DE_PAGAMENTO.getLabelComPrefixo()));
        } catch (Exception ignored) {
        }
        try {
            movConta.setLoteDePagamento(tabelaResultado.getInt(ColunasMovConta.LOTE_DE_PAGAMENTO.getLabelComPrefixo()));
        } catch (Exception ignored) {
        }
        try {
            movConta.setCompraEstoque(tabelaResultado.getInt(ColunasMovConta.COMPRA_ESTOQUE.getLabelComPrefixo()));
        } catch (Exception ignored) {
        }

        return movConta;
    }

    private String getUltimosLancamentosAgendadosLeftJoin() {
        // Para clarificar a logica utilizada aqui, vou deixar este comentario. Entao vamos la, aqui queremos trazer
        // todos os lancamentos que tenham o mesmo codigo de agendamento financeiro e que sejam os ultimos, dessa forma
        // o registro que for o ultimo (mais recente) vai ter o valor do "mcagend.codigo" nulo na clausula WHERE
        // preferi deixar esse codigo separado num metodo para que o mesmo seja compreendido com maior facilidade

        return LEFT_JOIN + "movconta mcagend"
                + ON + igual(ColunasMovConta.AGENDAMENTO_FINANCEIRO, "mcagend.agendamentofinanceiro")
                + AND + "mcagend.codigo > " + ColunasMovConta.CODIGO.getLabelComPrefixo();
    }

    private String concatenarColunas(ColunaPrefixavel coluna1, ColunaPrefixavel coluna2, String alias) {
        return "(" + coluna1.getLabelComPrefixo() + " || ' - ' || " + coluna2.getLabelComPrefixo() + ")" + AS + alias;
    }

}

package negocio.facade.jdbc.financeiro.lancamentos;

import negocio.comuns.financeiro.CartaoCreditoTO;
import negocio.comuns.financeiro.ChequeTO;

/**
 * VO que comporta os dados de cheque e de cartão que são utilizados na busca de MovConta
 *
 * <AUTHOR>
 * @since 17/08/2018
 */
class ChequeCartaoVO {

    private Integer movContaID;
    private ChequeTO cheque;
    private CartaoCreditoTO cartao;

    public Integer getMovContaID() {
        return movContaID;
    }

    public void setMovContaID(Integer movContaID) {
        this.movContaID = movContaID;
    }

    public ChequeTO getCheque() {
        return cheque;
    }

    public void setCheque(ChequeTO cheque) {
        this.cheque = cheque;
    }

    public CartaoCreditoTO getCartao() {
        return cartao;
    }

    public void setCartao(CartaoCreditoTO cartao) {
        this.cartao = cartao;
    }
}

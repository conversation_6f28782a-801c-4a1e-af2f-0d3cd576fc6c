package negocio.facade.jdbc.financeiro.lancamentos;

import negocio.facade.jdbc.arquitetura.consulta.ColunaPrefixavel;

/**
 * <AUTHOR>
 * @since 15/08/2018
 */
enum ColunasLote implements ColunaPrefixavel {

    CODIGO("codigo"),
    EMPRESA("empresa"),
    USUARIO_RESPONSAVEL("usuarioresponsavel"),
    DESCRICAO("descricao"),
    DATA_LANCAMENTO("dataLancamento"),
    DATA_DEPOSITO("dataDeposito"),
    VALOR("valor"),
    PAGAMO_VCONTA("pagamovconta"),
    AVULSO("avulso");

    private static final String PREFIXO = "lote";
    private String label;

    ColunasLote(String label) {
        this.label = label;
    }

    public static String getPrefixo() {
        return PREFIXO;
    }

    @Override
    public String getLabel() {
        return label;
    }

    @Override
    public String getLabelComPrefixo() {
        return PREFIXO + "." + label;
    }

}

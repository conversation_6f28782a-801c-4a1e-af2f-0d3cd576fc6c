package negocio.facade.jdbc.financeiro.lancamentos;

import negocio.facade.jdbc.arquitetura.consulta.ColunaPrefixavel;

/**
 * <AUTHOR>
 * @since 14/08/2018
 */
enum ColunasMovContaRateio implements ColunaPrefixavel {

    PLANO_CONTA("planoConta"),
    CENTRO_CUSTO("centrocusto"),
    VALOR("valor"),
    DESCRICAO("descricao"),
    MOV_CONTA("movconta"),
    FORMA_PAGAMENTO("formapagamento"),
    TIPO_DOCUMENTO("tipodocumento");

    private static final String PREFIXO = "colunasMovContaRateio";
    private String label;

    ColunasMovContaRateio(String label) {
        this.label = label;
    }

    public static String getPrefixo() {
        return PREFIXO;
    }

    @Override
    public String getLabel() {
        return label;
    }

    @Override
    public String getLabelComPrefixo() {
        return PREFIXO + "." + label;
    }

}

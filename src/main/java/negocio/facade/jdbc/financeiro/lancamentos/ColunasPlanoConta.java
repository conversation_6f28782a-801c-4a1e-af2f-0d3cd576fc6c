package negocio.facade.jdbc.financeiro.lancamentos;

import negocio.facade.jdbc.arquitetura.consulta.ColunaPrefixavel;

/**
 * <AUTHOR>
 * @since 14/08/2018
 */
enum ColunasPlanoConta implements ColunaPrefixavel {

    CODIGO("codigo"),
    DESCRICAO("descricao"),
    CODIGO_PLANO_CONTAS("codigoPlanoContas"),
    NOME("nome");

    public static final String NOME_PLANO_CONTA = "nomePlanoConta";

    private static final String PREFIXO = "planoConta";
    private String label;

    ColunasPlanoConta(String label) {
        this.label = label;
    }

    public static String getPrefixo() {
        return PREFIXO;
    }

    @Override
    public String getLabel() {
        return label;
    }

    @Override
    public String getLabelComPrefixo() {
        return PREFIXO + "." + label;
    }

}

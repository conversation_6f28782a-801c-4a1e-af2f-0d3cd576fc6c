package negocio.facade.jdbc.financeiro.lancamentos;

import negocio.facade.jdbc.arquitetura.consulta.ColunaPrefixavel;

/**
 * <AUTHOR>
 * @since 15/08/2018
 */
enum ColunasCaixaMovConta implements ColunaPrefixavel {

    CODIGO("codigo"),
    CAIXA("caixa"),
    MOV_CONTA("movconta");

    private static final String PREFIXO = "caixamovconta";
    private String label;

    ColunasCaixaMovConta(String label) {
        this.label = label;
    }

    public static String getPrefixo() {
        return PREFIXO;
    }

    @Override
    public String getLabel() {
        return label;
    }

    @Override
    public String getLabelComPrefixo() {
        return PREFIXO + "." + label;
    }

}

package negocio.facade.jdbc.financeiro.lancamentos;

import negocio.facade.jdbc.arquitetura.consulta.ColunaPrefixavel;

/**
 * <AUTHOR>
 * @since 14/08/2018
 */
enum ColunasMovConta implements ColunaPrefixavel {

    CODIGO("codigo"),
    DESCRICAO("descricao"),
    VALOR("valor"),
    DATA_ULTIMA_ALTERACAO("dataUltimaAlteracao"),
    TIPO_OPERACAO("tipooperacao"),
    PESSOA("pessoa"),
    APP("app"),
    CODIGOBARRAS("codigobarras"),
//    CPF_PESSOA("cpfPessoa"),
//    MATRICULA("matricula"),
    USUARIO("usuario"),
    EMPRESA("empresa"),
    CONTA("conta"),
    OBSERVACOES("observacoes"),
    DATA_QUITACAO("dataquitacao"),
    DATA_LANCAMENTO("datalancamento"),
    DATA_COMPETENCIA("datacompetencia"),
    DATA_VENCIMENTO("datavencimento"),
    AGENDAMENTO_FINANCEIRO("agendamentoFinanceiro"),
    NR_PARCELA("nrParcela"),
    LOTE("lote"),
    CONTA_ORIGEM("contaorigem"),
    APRESENTAR_NO_CAIXA("apresentarnocaixa"),
    NFSE_EMITIDA("nfseemitida"),
    LOTE_PAGOU_CONTA("lotePagouConta"),
    CONJUNTO_PAGAMENTO("conjuntopagamento"),

    NUMERO_DOCUMENTO("numerodocumento"),
    VALOR_PAGO("valorpago"),
    PAYLOAD_PIX("payloadpix"),
    CPF_OU_CNPJ_BENEFICIARIO("cpfoucnpjbeneficiario"),
    CONTA_DE_CONSUMO("contaDeConsumo"),
    PAGO_ORIGEM_WEBHOOK("pagoOrigemWebhook"),
    PRESA_EM_LOTE_DE_PAGAMENTO("presaemlotedepagamento"),
    LOTE_DE_PAGAMENTO("lotedepagamento"),
    VALOR_ORIGINAL_ALTERADO("valororiginalalterado"),
    RETIRADA_AUTOMATICA_RECEBIVEL_LOTE_ORIGEM_CANCELAMENTO("retiradaAutomaticaRecebivelOrigemCancelamento"),
    COMPRA_ESTOQUE("compraestoque");
    private static final String PREFIXO = "movConta";
    private String label;

    ColunasMovConta(String label) {
        this.label = label;
    }

    public static String getPrefixo() {
        return PREFIXO;
    }

    @Override
    public String getLabel() {
        return label;
    }

    @Override
    public String getLabelComPrefixo() {
        return PREFIXO + "." + label;
    }
}

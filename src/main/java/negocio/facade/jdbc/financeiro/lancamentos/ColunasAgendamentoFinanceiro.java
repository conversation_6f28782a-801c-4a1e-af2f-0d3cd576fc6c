package negocio.facade.jdbc.financeiro.lancamentos;

import negocio.facade.jdbc.arquitetura.consulta.ColunaPrefixavel;

/**
 * <AUTHOR>
 * @since 14/08/2018
 */
enum ColunasAgendamentoFinanceiro implements ColunaPrefixavel {

    CODIGO("codigo"),
    VENCIMENTO_ULTIMA_PARCELA("vencimentoUltimaParcela"),
    PARCELA_INI("parcelaini");

    private static final String PREFIXO = "agendamentoFinanceiro";
    private String label;

    ColunasAgendamentoFinanceiro(String label) {
        this.label = label;
    }

    public static String getPrefixo() {
        return PREFIXO;
    }

    @Override
    public String getLabel() {
        return label;
    }

    @Override
    public String getLabelComPrefixo() {
        return PREFIXO + "." + label;
    }

}


package negocio.facade.jdbc.financeiro;

import negocio.comuns.financeiro.ItemTaxaPersonalVO;
import negocio.comuns.utilitarias.ConsistirException;
import negocio.comuns.utilitarias.Uteis;
import negocio.facade.jdbc.arquitetura.SuperEntidade;
import negocio.facade.jdbc.basico.Cliente;
import negocio.facade.jdbc.plano.Desconto;
import negocio.facade.jdbc.plano.Produto;
import negocio.interfaces.financeiro.ItemTaxaPersonalInterfaceFacade;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.List;

/**
 *
 * <AUTHOR>
 */
public class ItemTaxaPersonal extends SuperEntidade implements ItemTaxaPersonalInterfaceFacade {

    public ItemTaxaPersonal() throws Exception {
        super();
    }

    public ItemTaxaPersonal(Connection conexao) throws Exception {
        super(conexao);
    }

    @Override
    public void incluir(ItemTaxaPersonalVO obj, int controle) throws Exception {
        try {
            con.setAutoCommit(false);
            obj.validarDados();
            incluirSemCommit(obj, controle);
            con.commit();
        } catch (Exception e) {
            con.rollback();
            obj.setNovoObj(true);
            throw e;
        } finally {
            con.setAutoCommit(true);
        }
    }

    @Override
    public void incluirSemCommit(ItemTaxaPersonalVO obj, int controle) throws Exception {
        String sql = "INSERT INTO itemtaxapersonal "
                   + "(aluno, produto, desconto,descontoespecifico, controle ) "
                   + "VALUES (?, ?, ?, ?, ?)";

        PreparedStatement sqlInserir = con.prepareStatement(sql);
        sqlInserir.setInt(1, obj.getAluno().getCodigo().intValue());
        sqlInserir.setInt(2, obj.getProduto().getCodigo().intValue());
        if (obj.getDesconto().getCodigo().intValue() == 0){
            sqlInserir.setNull(3, 0);
            sqlInserir.setDouble(4, obj.getDesconto().getValor());
        } else{
            sqlInserir.setInt(3, obj.getDesconto().getCodigo().intValue());
            sqlInserir.setDouble(4, 0.0);
        }
        
        sqlInserir.setInt(5, controle);
        sqlInserir.execute();

        obj.setCodigo(obterValorChavePrimariaCodigo());
        obj.setNovoObj(false);
    }
    
    @Override
    public void excluir(int codigo) throws Exception {
        try {
            con.setAutoCommit(false);
            excluirSemCommit(codigo);
            con.commit();
        } catch (Exception e) {
            con.rollback();
            throw e;
        } finally {
            con.setAutoCommit(true);
        }
    }

    @Override
    public void excluirSemCommit(int codigo) throws Exception {
        String sql = "DELETE FROM itemtaxapersonal WHERE codigo = ?";
        PreparedStatement sqlExcluir = con.prepareStatement(sql);
        sqlExcluir.setInt(1, codigo);
        sqlExcluir.execute();
    }

    @Override
    public ItemTaxaPersonalVO consultarPorChavePrimaria(int codigo, int nivelMontarDados) throws Exception {
        String sql = "SELECT * FROM itemtaxapersonal WHERE codigo = ?";
        PreparedStatement sqlConsultar = con.prepareStatement(sql);
        sqlConsultar.setInt(1, codigo);
        ResultSet tabelaResultado = sqlConsultar.executeQuery();
        if (!tabelaResultado.next()) {
            throw new ConsistirException("Dados Não Encontrados.");
        }
        return (montarDados(tabelaResultado, nivelMontarDados, con));
    }

    @Override
    public List<ItemTaxaPersonalVO> consultarPorControle(int controle, int nivelMontarDados) throws Exception {
        String sql = "SELECT * FROM itemtaxapersonal WHERE controle = ? ";
        PreparedStatement sqlConsultar = con.prepareStatement(sql);
        sqlConsultar.setInt(1, controle);
        ResultSet tabelaResultado = sqlConsultar.executeQuery();
        return (montarDadosConsulta(tabelaResultado, nivelMontarDados));
    }

    public List<ItemTaxaPersonalVO> montarDadosConsulta(ResultSet dadosSQL, int nivelMontarDados) throws Exception {
        List<ItemTaxaPersonalVO> aux = new ArrayList<ItemTaxaPersonalVO>();
        while(dadosSQL.next()) 
            aux.add(montarDados(dadosSQL, nivelMontarDados, con));
        return aux;
    }

    public static ItemTaxaPersonalVO montarDados(ResultSet dadosSQL, int nivelMontarDados, Connection con) throws Exception {
        ItemTaxaPersonalVO obj = montarDadosBasicos(dadosSQL);
        if (nivelMontarDados == Uteis.NIVELMONTARDADOS_MINIMOS) {
            return obj;
        }
        montarDadosAluno(obj, Uteis.NIVELMONTARDADOS_DADOSBASICOS, con);
        montarDadosProduto(obj, Uteis.NIVELMONTARDADOS_DADOSBASICOS, con);
        montarDadosDesconto(obj, Uteis.NIVELMONTARDADOS_DADOSBASICOS, con);
        return obj;
    }

    private static ItemTaxaPersonalVO montarDadosBasicos(ResultSet dadosSQL) throws SQLException {
        ItemTaxaPersonalVO obj = new ItemTaxaPersonalVO();
        obj.setCodigo(dadosSQL.getInt("codigo"));
        obj.getAluno().setCodigo(dadosSQL.getInt("aluno"));
        obj.getProduto().setCodigo(dadosSQL.getInt("produto"));
        obj.getDesconto().setCodigo(dadosSQL.getInt("desconto"));
        obj.setDescontoEspecifico(dadosSQL.getDouble("descontoespecifico"));
        obj.setNovoObj(false);
        return obj;
    }

    private static void montarDadosAluno(ItemTaxaPersonalVO obj, int nivelMontarDados, Connection con) throws Exception {
        if (obj.getAluno().getCodigo().intValue() == 0) {
            // nao precisa fazer nada
        } else {
            Cliente cliente;
            try {
                cliente = new Cliente(con);
                obj.setAluno(cliente.consultarPorChavePrimaria(obj.getAluno().getCodigo(), nivelMontarDados));
            } finally {
                cliente = null;
            }
        }
    }

    private static void montarDadosProduto(ItemTaxaPersonalVO obj, int nivelMontarDados, Connection con) throws Exception {
        if (obj.getProduto().getCodigo().intValue() == 0) {
            // nao precisa fazer nada
        } else {
            Produto produto;
            try {
                produto = new Produto(con);
                obj.setProduto(produto.consultarPorChavePrimaria(obj.getProduto().getCodigo(), nivelMontarDados));
            } finally {
                produto = null;
            }
        }
    }

    private static void montarDadosDesconto(ItemTaxaPersonalVO obj, int nivelMontarDados, Connection con) throws Exception {
        if (obj.getDesconto().getCodigo().intValue() == 0) {
            // nao precisa fazer nada
        } else {
            Desconto desconto;
            try {
                desconto = new Desconto(con);
                obj.setDesconto(desconto.consultarPorChavePrimaria(obj.getDesconto().getCodigo(), nivelMontarDados));
            } finally {
                desconto = null;
            }
        }
    }

    public void alterarItemTaxaPersonal(int codigoMovProduto, int codigoItemTaxaPersonal) throws Exception {
        String sql = "UPDATE itemtaxapersonal SET movproduto = "+ codigoMovProduto +"WHERE codigo ="+ codigoItemTaxaPersonal;
        PreparedStatement sqlInserir = con.prepareStatement(sql);
        sqlInserir.execute();
    }
}

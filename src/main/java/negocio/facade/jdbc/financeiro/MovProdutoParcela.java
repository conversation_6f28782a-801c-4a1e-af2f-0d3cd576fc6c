package negocio.facade.jdbc.financeiro;

import br.com.pactosolucoes.enumeradores.SituacaoParcelaEnum;
import br.com.pactosolucoes.enumeradores.SituacaoProdutoEnum;
import negocio.comuns.financeiro.MovProdutoParcelaVO;
import negocio.comuns.utilitarias.ConsistirException;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisValidacao;
import negocio.facade.jdbc.arquitetura.SuperEntidade;
import negocio.facade.jdbc.contrato.MovProduto;

import java.sql.*;
import java.util.ArrayList;
import java.util.Iterator;
import java.util.List;

/**
 * Classe de persistência que encapsula todas as operações de manipulação dos
 * dados da classe <code>MovProdutoParcelaVO</code>. Responsável por implementar
 * operações como incluir, alterar, excluir e consultar pertinentes a classe
 * <code>MovProdutoParcelaVO</code>. Encapsula toda a interação com o banco de
 * dados.
 *
 * @see MovProdutoParcelaVO
 * @see SuperEntidade
 */
public class MovProdutoParcela extends SuperEntidade {

    public MovProdutoParcela() throws Exception {
        super();
        setIdEntidade("Contrato");
    }

    public MovProdutoParcela(Connection conexao) throws Exception {
        super(conexao);
        setIdEntidade("Contrato");
    }

    /**
     * Responsável por montar os dados de vários objetos, resultantes de uma
     * consulta ao banco de dados (<code>ResultSet</code>). Faz uso da operação
     * <code>montarDados</code> que realiza o trabalho para um objeto por vez.
     *
     * @return List Contendo vários objetos da classe
     *         <code>MovProdutoParcelaVO</code> resultantes da consulta.
     */
    public static List<MovProdutoParcelaVO> montarDadosConsulta(ResultSet tabelaResultado, int nivelMontarDados, Connection con) throws Exception {
        List<MovProdutoParcelaVO> vetResultado = new ArrayList<MovProdutoParcelaVO>();
        while (tabelaResultado.next()) {
            MovProdutoParcelaVO obj = montarDados(tabelaResultado, nivelMontarDados, con);
            vetResultado.add(obj);
        }
        return vetResultado;
    }

    /**
     * Responsável por montar os dados resultantes de uma consulta ao banco de
     * dados (<code>ResultSet</code>) em um objeto da classe
     * <code>MovProdutoParcelaVO</code>.
     *
     * @return O objeto da classe <code>MovProdutoParcelaVO</code> com os dados
     *         devidamente montados.
     */
    public static MovProdutoParcelaVO montarDados(ResultSet dadosSQL, int nivelMontarDados, Connection con) throws Exception {
        MovProdutoParcelaVO obj = new MovProdutoParcelaVO();
        obj.setCodigo(dadosSQL.getInt("codigo"));
        obj.setMovProduto(dadosSQL.getInt("movProduto"));
        obj.setMovParcela(dadosSQL.getInt("movParcela"));
        obj.setValorPago(dadosSQL.getDouble("valorPago"));
        obj.getReciboPagamento().setCodigo(dadosSQL.getInt("reciboPagamento"));
        obj.getMovParcelaOriginalMultaJuros().setCodigo(dadosSQL.getInt("movParcelaOriginalMultaJuros"));
        obj.setNovoObj(false);
        if (nivelMontarDados == Uteis.NIVELMONTARDADOS_DADOSBASICOS) {
            return obj;
        }

        if (nivelMontarDados == Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA) {
            MovProduto movProduto = new MovProduto(con);
            obj.setMovProdutoVO(movProduto.consultarPorChavePrimaria(obj.getMovProduto(), Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA));
            movProduto = null;
            return obj;
        }

        if (nivelMontarDados == Uteis.NIVELMONTARDADOS_DADOSENTIDADESPRINCIPAIS || nivelMontarDados == Uteis.NIVELMONTARDADOS_DADOSENTIDADESUBORDINADAS) {
            MovProduto movProduto = new MovProduto(con);
            obj.setMovProdutoVO(movProduto.consultarPorChavePrimaria(obj.getMovProduto(), Uteis.NIVELMONTARDADOS_VENDA));
            movProduto = null;
            return obj;
        }

        return obj;
    }

    /**
     * Operação responsável por retornar um novo objeto da classe
     * <code>MovProdutoParcelaVO</code>.
     */
    public MovProdutoParcelaVO novo() throws Exception {
        incluir(getIdEntidade());
        return new MovProdutoParcelaVO();
    }

    /**
     * Operação responsável por incluir no banco de dados um objeto da classe
     * <code>MovProdutoParcelaVO</code>. Primeiramente valida os dados (
     * <code>validarDados</code>) do objeto. Verifica a conexão com o banco de
     * dados e a permissão do usuário para realizar esta operacão na entidade.
     * Isto, através da operação <code>incluir</code> da superclasse.
     *
     * @param obj Objeto da classe <code>MovProdutoParcelaVO</code> que será
     *            gravado no banco de dados.
     * @throws Exception Caso haja problemas de conexão, restrição de acesso ou
     *                   validação de dados.
     */
    public void incluir(MovProdutoParcelaVO obj) throws Exception {
        MovProdutoParcelaVO.validarDados(obj);
        //incluir(getIdEntidade());
        obj.realizarUpperCaseDados();
        String sql = "INSERT INTO MovProdutoParcela(movProduto, movParcela, valorPago, reciboPagamento, movParcelaOriginalMultaJuros) VALUES (?, ?, ?, ?, ?)";
        PreparedStatement sqlInserir = con.prepareStatement(sql);
        sqlInserir.setInt(1, obj.getMovProduto());
        sqlInserir.setInt(2, obj.getMovParcela());
            if(obj.getValorPago() == null){
            sqlInserir.setDouble(3, Types.NULL);
        }else{
            sqlInserir.setDouble(3, obj.getValorPago());
        }
        if (obj.getReciboPagamento() != null && obj.getReciboPagamento().getCodigo() != 0) {
            sqlInserir.setInt(4, obj.getReciboPagamento().getCodigo());
        } else {
            sqlInserir.setNull(4, 0);
        }
        if (obj.getMovParcelaOriginalMultaJuros() != null && obj.getMovParcelaOriginalMultaJuros().getCodigo() != 0) {
            sqlInserir.setInt(5, obj.getMovParcelaOriginalMultaJuros().getCodigo());
        } else {
            sqlInserir.setNull(5, 0);
        }
        sqlInserir.execute();
        obj.setCodigo(obterValorChavePrimariaCodigo());
        obj.setNovoObj(false);
    }

    /**
     * Operação responsável por alterar no BD os dados de um objeto da classe
     * <code>MovProdutoParcelaVO</code>. Sempre utiliza a chave primária da
     * classe como atributo para localização do registro a ser alterado.
     * Primeiramente valida os dados (<code>validarDados</code>) do objeto.
     * Verifica a conexão com o banco de dados e a permissão do usuário para
     * realizar esta operacão na entidade. Isto, através da operação
     * <code>alterar</code> da superclasse.
     *
     * @param obj Objeto da classe <code>MovProdutoParcelaVO</code> que será
     *            alterada no banco de dados.
     * @throws Exception Caso haja problemas de conexão, restrição de acesso ou
     *                   validação de dados.
     */
    public void alterar(MovProdutoParcelaVO obj) throws Exception {
        MovProdutoParcelaVO.validarDados(obj);
        obj.realizarUpperCaseDados();
        String sql = "UPDATE MovProdutoParcela SET movProduto=?, movParcela=?, valorPago=?, reciboPagamento=?, movParcelaOriginalMultaJuros=? WHERE ((codigo = ?))";
        PreparedStatement sqlAlterar = con.prepareStatement(sql);
        sqlAlterar.setInt(1, obj.getMovProduto());
        sqlAlterar.setInt(2, obj.getMovParcela());
        sqlAlterar.setDouble(3, obj.getValorPago());
        if (obj.getReciboPagamento().getCodigo() != 0) {
            sqlAlterar.setInt(4, obj.getReciboPagamento().getCodigo());
        } else {
            sqlAlterar.setNull(4, 0);
        }
        if (!UteisValidacao.emptyNumber(obj.getMovParcelaOriginalMultaJuros().getCodigo())) {
            sqlAlterar.setInt(5, obj.getMovParcelaOriginalMultaJuros().getCodigo());
        } else {
            sqlAlterar.setNull(5, 0);
        }
        sqlAlterar.setInt(6, obj.getCodigo());
        sqlAlterar.execute();
    }

    /**
     * Operação responsável por excluir no BD um objeto da classe
     * <code>MovProdutoParcelaVO</code>. Sempre localiza o registro a ser
     * excluído através da chave primária da entidade. Primeiramente verifica a
     * conexão com o banco de dados e a permissão do usuário para realizar esta
     * operacão na entidade. Isto, através da operação <code>excluir</code> da
     * superclasse.
     *
     * @param obj Objeto da classe <code>MovProdutoParcelaVO</code> que será
     *            removido no banco de dados.
     * @throws Exception Caso haja problemas de conexão ou restrição de acesso.
     */
    public void excluir(MovProdutoParcelaVO obj) throws Exception {
        //excluir(getIdEntidade());
        String sql = "DELETE FROM MovProdutoParcela WHERE ((codigo = ?))";
        PreparedStatement sqlExcluir = con.prepareStatement(sql);
        sqlExcluir.setInt(1, obj.getCodigo());
        sqlExcluir.execute();
    }

    public void alterarRecibo(Integer movparcela, Integer recibopagamento) throws SQLException {
        String sql = "UPDATE movprodutoparcela "+
                "SET reciboPagamento = "+recibopagamento+
                "WHERE movparcela = "+movparcela;
        PreparedStatement statement =  con.prepareStatement(sql);
        statement.execute();
    }

    public void alterarSomenteReciboPagamentoSemCommit(MovProdutoParcelaVO obj) throws Exception {
        //alterar(getIdEntidade());
        String sql = "UPDATE MovProdutoParcela SET reciboPagamento=? WHERE ((codigo = ?))";
        PreparedStatement sqlAlterar = con.prepareStatement(sql);
        if(UteisValidacao.emptyNumber(obj.getReciboPagamento().getCodigo())){
            sqlAlterar.setNull(1, 0);
        } else {
            sqlAlterar.setInt(1, obj.getReciboPagamento().getCodigo());
        }
        sqlAlterar.setInt(2, obj.getCodigo());
        sqlAlterar.execute();
    }

    /**
     * Responsável por realizar uma consulta de <code>MovProdutoParcela</code>
     * através do valor do atributo <code>codigo</code> da classe
     * <code>MovParcela</code> Faz uso da operação
     * <code>montarDadosConsulta</code> que realiza o trabalho de prerarar o
     * List resultante.
     *
     * @return List Contendo vários objetos da classe
     *         <code>MovProdutoParcelaVO</code> resultantes da consulta.
     * @throws Exception Caso haja problemas de conexão ou restrição de acesso.
     */
    public List<MovProdutoParcelaVO> consultarPorReciboPagamneto(Integer valorConsulta, int nivelMontarDados) throws Exception {
        consultar(getIdEntidade(), true);
        String sqlStr = "SELECT * FROM MovProdutoParcela " +
                "WHERE reciboPagamento = " + valorConsulta + " " +
                "ORDER BY reciboPagamento,movparcela,movproduto";
        Statement stm = con.createStatement();
        ResultSet tabelaResultado = stm.executeQuery(sqlStr);
        return montarDadosConsulta(tabelaResultado, nivelMontarDados, con);
    }

    /**
     * Responsável por realizar uma consulta de <code>MovProdutoParcela</code>
     * através do valor do atributo <code>codigo</code> da classe
     * <code>MovParcela</code> Faz uso da operação
     * <code>montarDadosConsulta</code> que realiza o trabalho de prerarar o
     * List resultante.
     *
     * @return List Contendo vários objetos da classe
     *         <code>MovProdutoParcelaVO</code> resultantes da consulta.
     * @throws Exception Caso haja problemas de conexão ou restrição de acesso.
     */
    public List<MovProdutoParcelaVO> consultarPorCodigoMovParcela(Integer valorConsulta, int nivelMontarDados) throws Exception {
        consultar(getIdEntidade(), true);
        String sqlStr = "SELECT * FROM movprodutoparcela WHERE movparcela = " + valorConsulta + " ORDER BY movparcela";
        Statement stm = con.createStatement();
        ResultSet tabelaResultado = stm.executeQuery(sqlStr);
        return montarDadosConsulta(tabelaResultado, nivelMontarDados, con);
    }

    /**
     * Responsável por realizar uma consulta de <code>MovProdutoParcela</code>
     * através do valor do atributo <code>descricao</code> da classe
     * <code>MovProduto</code> Faz uso da operação
     * <code>montarDadosConsulta</code> que realiza o trabalho de prerarar o
     * List resultante.
     *
     * @return List Contendo vários objetos da classe
     *         <code>MovProdutoParcelaVO</code> resultantes da consulta.
     * @throws Exception Caso haja problemas de conexão ou restrição de acesso.
     */
    public List<MovProdutoParcelaVO> consultarPorCodigoMovProdutos(Integer valorConsulta, int nivelMontarDados) throws Exception {
        consultar(getIdEntidade(), true);
        String sqlStr = "SELECT MovProdutoParcela.* FROM MovProdutoParcela WHERE MovProdutoParcela.movProduto = " + valorConsulta;
        Statement stm = con.createStatement();
        ResultSet tabelaResultado = stm.executeQuery(sqlStr);
        return montarDadosConsulta(tabelaResultado, nivelMontarDados, con);
    }

    /**
     * Operação responsável por localizar um objeto da classe
     * <code>MovProdutoParcelaVO</code> através de sua chave primária.
     *
     * @throws Exception Caso haja problemas de conexão ou localização do objeto
     *                   procurado.
     */
    public MovProdutoParcelaVO consultarPorDescricaoMovProduto(String valorConsulta, int nivelMontarDados) throws Exception {
        consultar(getIdEntidade(), false);
        String sqlStr = "SELECT MovProdutoParcela.* FROM MovProdutoParcela, MovProduto WHERE MovProdutoParcela.movProduto = MovProduto.codigo AND upper( MovProduto.descricao ) LIKE('?%') ORDER BY MovProduto.descricao";
        PreparedStatement sqlConsultar = con.prepareStatement(sqlStr);
        sqlConsultar.setString(1, valorConsulta.toLowerCase());
        ResultSet tabelaResultado = sqlConsultar.executeQuery();
        if (!tabelaResultado.next()) {
            throw new ConsistirException("Dados Não Encontrados ( MovProdutoParcela ).");
        }
        return (montarDados(tabelaResultado, nivelMontarDados, con));
    }

    /**
     * Responsável por realizar uma consulta de <code>MovProdutoParcela</code>
     * através do valor do atributo <code>Integer codigo</code>. Retorna os
     * objetos com valores iguais ou superiores ao parâmetro fornecido. Faz uso
     * da operação <code>montarDadosConsulta</code> que realiza o trabalho de
     * prerarar o List resultante.
     *
     * @param controlarAcesso Indica se a aplicação deverá verificar se o usuário possui
     *                        permissão para esta consulta ou não.
     * @return List Contendo vários objetos da classe
     *         <code>MovProdutoParcelaVO</code> resultantes da consulta.
     * @throws Exception Caso haja problemas de conexão ou restrição de acesso.
     */
    public List consultarPorCodigo(Integer valorConsulta, boolean controlarAcesso, int nivelMontarDados) throws Exception {
        consultar(getIdEntidade(), controlarAcesso);
        String sqlStr = "SELECT * FROM MovProdutoParcela WHERE codigo >= " + valorConsulta + " ORDER BY codigo";
        Statement stm = con.createStatement();
        ResultSet tabelaResultado = stm.executeQuery(sqlStr);
        return (montarDadosConsulta(tabelaResultado, nivelMontarDados, con));
    }

    /**
     * Operação responsável por excluir todos os objetos da
     * <code>MovProdutoParcelaVO</code> no BD. Faz uso da operação
     * <code>excluir</code> disponível na classe <code>MovProdutoParcela</code>.
     *
     * @param movProduto campo chave para exclusão dos objetos no
     *                                BD.
     * @throws Exception Erro de conexão com o BD ou restrição de acesso a esta
     *                   operação.
     */
    public void excluirMovProdutoParcelas(Integer movProduto) throws Exception {
        excluir(getIdEntidade());
        String sql = "DELETE FROM MovProdutoParcela WHERE (movProduto = ?)";
        PreparedStatement sqlExcluir = con.prepareStatement(sql);
        sqlExcluir.setInt(1, movProduto);
        sqlExcluir.execute();
    }

    public void excluirMovProdutoParcelasRecibo(Integer codigoContrato) throws Exception {
        excluir(getIdEntidade());
        String sql = "DELETE FROM MovProdutoParcela WHERE  movparcela  in (SELECT codigo FROM movparcela WHERE contrato =?)";
        PreparedStatement sqlExcluir = con.prepareStatement(sql);
        sqlExcluir.setInt(1, codigoContrato);
        sqlExcluir.execute();
    }

    /**
     * Operação responsável por alterar todos os objetos da
     * <code>MovProdutoParcelaVO</code> contidos em um Hashtable no BD. Faz uso
     * da operação <code>excluirMovProdutoParcelas</code> e
     * <code>incluirMovProdutoParcelas</code> disponíveis na classe
     * <code>MovProdutoParcela</code>.
     *
     * @param objetos List com os objetos a serem alterados ou incluídos no BD.
     * @throws Exception Erro de conexão com o BD ou restrição de acesso a esta
     *                   operação.
     */
    public void alterarMovProdutoParcelas(Integer movParcela, List objetos) throws Exception {
        Iterator i = objetos.iterator();
        while (i.hasNext()) {
            MovProdutoParcelaVO movProdutoParcela = (MovProdutoParcelaVO) i.next();
            alterar(movProdutoParcela);
        }

        // excluirMovProdutoParcelas(movParcela);
        // incluirMovProdutoParcelas(movParcela, objetos);
    }

    /**
     * Operação responsável por alterar todos os objetos da
     * <code>MovProdutoParcelaVO</code> contidos em um Hashtable no BD. Faz uso
     * da operação <code>excluirMovProdutoParcelas</code> e
     * <code>incluirMovProdutoParcelas</code> disponíveis na classe
     * <code>MovProdutoParcela</code>.
     *
     * @param objetos List com os objetos a serem alterados ou incluídos no BD.
     * @throws Exception Erro de conexão com o BD ou restrição de acesso a esta
     *                   operação.
     */
    public void alterarMovProdutoParcelasPorCodigoProduto(Integer movProduto, List objetos) throws Exception {

        excluirMovProdutoParcelas(movProduto);
        Iterator i = objetos.iterator();
        while (i.hasNext()) {
            MovProdutoParcelaVO movProdutoParcela = (MovProdutoParcelaVO) i.next();
            movProdutoParcela.setMovProduto(movProduto);
            incluir(movProdutoParcela);
        }


    }

    /**
     * Operação responsável por incluir objetos da
     * <code>MovProdutoParcelaVO</code> no BD. Garantindo o relacionamento com a
     * entidade principal <code>contrato.MovProduto</code> através do atributo
     * de vínculo.
     *
     * @param objetos List contendo os objetos a serem gravados no BD da classe.
     * @throws Exception Erro de conexão com o BD ou restrição de acesso a esta
     *                   operação.
     */
    public void incluirMovProdutoParcelas(Integer movParcelaPrm, List objetos) throws Exception {
        Iterator e = objetos.iterator();
        while (e.hasNext()) {
            MovProdutoParcelaVO obj = (MovProdutoParcelaVO) e.next();
            obj.setMovParcela(movParcelaPrm);
            incluir(obj);
        }
        MovParcela movParcelaDao = new MovParcela(this.con);
        movParcelaDao.atualizarParcelaDCC(movParcelaPrm, null, null, null);
        movParcelaDao = null;
    }

    /**
     * Operação responsável por consultar todos os
     * <code>MovProdutoParcelaVO</code> relacionados a um objeto da classe
     * <code>contrato.MovProduto</code>.
     *
     * @param movProduto Atributo de <code>contrato.MovProduto</code> a ser utilizado
     *                   para localizar os objetos da classe
     *                   <code>MovProdutoParcelaVO</code>.
     * @return List Contendo todos os objetos da classe
     *         <code>MovProdutoParcelaVO</code> resultantes da consulta.
     * @throws Exception Erro de conexão com o BD ou restrição de acesso a esta
     *                   operação.
     */
    public List<MovProdutoParcelaVO> consultarMovProdutoParcelas(Integer movProduto, int nivelMontarDados) throws Exception {
        consultar(getIdEntidade());
        List<MovProdutoParcelaVO> objetos = new ArrayList<MovProdutoParcelaVO>();
        String sql = "SELECT * FROM MovProdutoParcela WHERE movProduto = ?";
        PreparedStatement sqlConsulta = con.prepareStatement(sql);
        sqlConsulta.setInt(1, movProduto);
        ResultSet resultado = sqlConsulta.executeQuery();
        while (resultado.next()) {
            MovProdutoParcelaVO novoObj = MovProdutoParcela.montarDados(resultado, nivelMontarDados, con);
            objetos.add(novoObj);
        }
        return objetos;
    }

    /**
     * Operação responsável por consultar todos os
     * <code>MovProdutoParcelaVO</code> relacionados a um objeto da classe
     * <code>contrato.MovProduto</code>.
     */
    public List<MovProdutoParcelaVO> consultarMovProdutoParcelasPorParcelas(Integer movParcela, int nivelMontarDados) throws Exception {
        consultar(getIdEntidade());
        List<MovProdutoParcelaVO> objetos = new ArrayList<MovProdutoParcelaVO>();
        String sql = "SELECT * FROM MovProdutoParcela WHERE movParcela = ?";
        PreparedStatement sqlConsulta = con.prepareStatement(sql);
        sqlConsulta.setInt(1, movParcela);
        ResultSet resultado = sqlConsulta.executeQuery();
        while (resultado.next()) {
            MovProdutoParcelaVO novoObj = MovProdutoParcela.montarDados(resultado, nivelMontarDados, con);
            objetos.add(novoObj);
        }
        return objetos;
    }

    /**
     * Operação responsável por localizar um objeto da classe
     * <code>MovProdutoParcelaVO</code> através de sua chave primária.
     *
     * @throws Exception Caso haja problemas de conexão ou localização do objeto
     *                   procurado.
     */
    public MovProdutoParcelaVO consultarPorChavePrimaria(Integer codigoPrm, int nivelMontarDados) throws Exception {
        consultar(getIdEntidade(), false);
        String sql = "SELECT * FROM MovProdutoParcela WHERE codigo = ?";
        PreparedStatement sqlConsultar = con.prepareStatement(sql);
        sqlConsultar.setInt(1, codigoPrm);
        ResultSet tabelaResultado = sqlConsultar.executeQuery();
        if (!tabelaResultado.next()) {
            throw new ConsistirException("Dados Não Encontrados ( MovProdutoParcela ).");
        }
        return (montarDados(tabelaResultado, nivelMontarDados, con));
    }

    public static void corrigirProdutosParcelas(Connection conProcesso) throws Exception{
        String sql = "select * from (\n" +
                "select descricao, contrato, codigo, round(valorparcela::numeric, 2) as valorparcela, round((select sum(valorpago) from movprodutoparcela where movparcela = m.codigo)::numeric,2) as valorsomado from movparcela m\n" +
                ") as f where valorparcela < valorsomado and descricao like 'PRODUTOS PARCELA%'";

        ResultSet rs = criarConsulta(sql, conProcesso);

        while(rs.next()){
            Double valorParcela = rs.getDouble("valorparcela");
            Integer codigo = rs.getInt("codigo");

            executarConsulta("update movprodutoparcela set valorpago = " +
                    valorParcela + " where movparcela = " + codigo, conProcesso);
        }
    }

    public void alterarStatusMovProduto(Integer parcela, SituacaoProdutoEnum situacaoProdutoEnum) throws SQLException {
        String sql = "UPDATE movproduto " +
                "SET situacao = '"+situacaoProdutoEnum.getCodigo()+"'"+
                "WHERE codigo IN (SELECT movproduto FROM movprodutoparcela WHERE movparcela ="+parcela+")";
        PreparedStatement statement = con.prepareStatement(sql);
        statement.execute();
    }

    public void excluirPorParcela(Integer parcela) throws SQLException {
        String sql = "DELETE FROM movproduto " +
                "WHERE movparcela = "+parcela;
        PreparedStatement statement = con.prepareStatement(sql);
        statement.execute();
    }
}

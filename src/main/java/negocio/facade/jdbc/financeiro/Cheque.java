/*
 * To change this template, choose Too<PERSON> | Templates
 * and open the template in the editor.
 */
package negocio.facade.jdbc.financeiro;

import br.com.pactosolucoes.ce.comuns.ex.ValidacaoException;
import br.com.pactosolucoes.comuns.util.Formatador;
import controle.arquitetura.threads.ThreadDemonstrativoFinanceiro;
import negocio.comuns.financeiro.BancoVO;
import negocio.comuns.financeiro.ChequeTO;
import negocio.comuns.financeiro.ChequeVO;
import negocio.comuns.financeiro.ConfiguracaoFinanceiroVO;
import negocio.comuns.financeiro.LoteVO;
import negocio.comuns.financeiro.MovContaVO;
import negocio.comuns.financeiro.MovPagamentoVO;
import negocio.comuns.financeiro.RelatorioDevolucaoChequeTO;
import negocio.comuns.utilitarias.Calendario;
import negocio.comuns.utilitarias.ConsistirException;
import negocio.comuns.utilitarias.Ordenacao;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisValidacao;
import negocio.comuns.utilitarias.UtilReflection;
import negocio.facade.jdbc.arquitetura.SuperEntidade;
import negocio.facade.jdbc.arquitetura.SuperFacadeJDBC;
import negocio.facade.jdbc.basico.Empresa;
import relatorio.negocio.jdbc.financeiro.ProdutoRatear;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.sql.Statement;
import java.sql.Types;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;

/**
 *
 * <AUTHOR>
 */
public class Cheque extends SuperEntidade {

    public Cheque() throws Exception {
        super();
        setIdEntidade("MovPagamento");
    }

    public Cheque(Connection conexao) throws Exception {
        super(conexao);
        setIdEntidade("MovPagamento");
    }

    /**
     * Operação responsável por retornar um novo objeto da classe <code>ChequeVO</code>.
     */
    public ChequeVO novo() throws Exception {
        incluir(getIdEntidade());
        ChequeVO obj = new ChequeVO();
        return obj;
    }

    /**
     * Operação responsável por incluir no banco de dados um objeto da classe <code>ChequeVO</code>.
     * Primeiramente valida os dados (<code>validarDados</code>) do objeto. Verifica a conexão com o banco de dados e a permissão do usuário
     * para realizar esta operacão na entidade.
     * Isto, através da operação <code>incluir</code> da superclasse.
     * @param obj  Objeto da classe <code>ChequeVO</code> que será gravado no banco de dados.
     * @exception Exception Caso haja problemas de conexão, restrição de acesso ou validação de dados.
     */
    public void incluir(ChequeVO obj) throws Exception {
        try {
            ChequeVO.validarDados(obj);
            //incluir(getIdEntidade());
            String sql = "INSERT INTO Cheque( MovPagamento, numero, agencia, banco, conta, valor, "
                    + "dataCompesancao, vistaOuPrazo, cpf, cnpj, situacao , movconta, nomenocheque, composicao, valortotal,produtospagos) "
                    + "VALUES ( ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ? , ?, ?, ?, ?, ?)";

            PreparedStatement sqlInserir = con.prepareStatement(sql);
            if (obj.getMovPagamento().intValue() != 0) {
                sqlInserir.setInt(1, obj.getMovPagamento().intValue());
            } else {
                sqlInserir.setNull(1, 0);
            }
            sqlInserir.setString(2, obj.getNumero());
            sqlInserir.setString(3, obj.getAgencia());
            sqlInserir.setInt(4, obj.getBanco().getCodigo().intValue());
            sqlInserir.setString(5, obj.getConta());
            sqlInserir.setDouble(6, obj.getValor());
            sqlInserir.setTimestamp(7, Uteis.getDataJDBCTimestamp(obj.getDataCompensacao()));
            sqlInserir.setString(8, obj.getVistaOuPrazo());
            sqlInserir.setString(9, obj.getCpf());
            sqlInserir.setString(10, obj.getCnpj());
            sqlInserir.setString(11, obj.getSituacao());

            if (obj.getMovConta().getCodigo() != 0) {
                sqlInserir.setInt(12, obj.getMovConta().getCodigo());
            } else {
                sqlInserir.setNull(12, 0);
            }
            sqlInserir.setString(13, obj.getNomeNoCheque());
            sqlInserir.setString(14, obj.getComposicao());
            sqlInserir.setDouble(15, obj.getValorTotal());
            sqlInserir.setString(16, obj.getProdutosPagos());
            
            sqlInserir.execute();
            obj.setCodigo(obterValorChavePrimariaCodigo());
            if (obj.getComposicao() != null && !obj.getComposicao().equals("") ){
            	atualizarComposicao(obj);
            }

            if (UtilReflection.objetoMaiorQueZero(obj, "getNfSeEmitidaChequeExcluido().getCodigo()")){
                obj.getNfSeEmitidaChequeExcluido().setCheque(obj);
                NFSeEmitida nfSeEmitidaDAO = new NFSeEmitida(con);
                nfSeEmitidaDAO.incluir(obj.getNfSeEmitidaChequeExcluido());
                nfSeEmitidaDAO = null;
            }

            obj.setNovoObj(false);
        } catch (Exception e) {
            throw e;
        }

    }
    
    private void atualizarComposicao(ChequeVO obj) throws SQLException {
		String [] cheques = obj.getComposicao().split(",");
		for (int i = 0; i < cheques.length; i++){
			Integer codigoCheque = Integer.parseInt(cheques[i]);
			String composicao = obj.getCodigo().toString();
			for (int j = 0; j < cheques.length; j++){
				if (j != i){
					composicao +=","+cheques[j];  
				}
			}
			alterarComposicao(codigoCheque, composicao);
		}
		
	}

	private void alterarComposicao(int codigo, String composicao) throws SQLException {
        String sql = "UPDATE cheque SET composicao=? WHERE codigo = ?";
        PreparedStatement sqlAlterar = con.prepareStatement(sql);
        
        sqlAlterar.setString(1, composicao);
               
        sqlAlterar.setInt(2, codigo);
        sqlAlterar.execute();
		
	}

    /**
     * Operação responsável por alterar no BD os dados de um objeto da classe <code>ChequeVO</code>.
     * Sempre utiliza a chave primária da classe como atributo para localização do registro a ser alterado.
     * Primeiramente valida os dados (<code>validarDados</code>) do objeto. Verifica a conexão com o banco de dados e a permissão do usuário
     * para realizar esta operacão na entidade.
     * Isto, através da operação <code>alterar</code> da superclasse.
     * @param obj    Objeto da classe <code>ChequeVO</code> que será alterada no banco de dados.
     * @exception Exception Caso haja problemas de conexão, restrição de acesso ou validação de dados.
     */
    public void alterar(ChequeVO obj) throws Exception {
        ChequeVO.validarDados(obj);
        alterar(getIdEntidade());
        String sql = "UPDATE Cheque set movPagamento=?, numero=?, agencia=?, "
                + "banco=?, conta=?, valor=?, dataCompesancao=?, vistaOuPrazo=?, "
                + "cpf=?, cnpj=?, situacao=?, nomenocheque=?, movconta=?, valortotal=?, produtospagos=?  WHERE codigo = ?";
        PreparedStatement sqlAlterar = con.prepareStatement(sql);
        if (obj.getMovPagamento().intValue() != 0) {
            sqlAlterar.setInt(1, obj.getMovPagamento().intValue());
        } else {
            sqlAlterar.setNull(1, 0);
        }
        sqlAlterar.setString(2, obj.getNumero());
        sqlAlterar.setString(3, obj.getAgencia());
        sqlAlterar.setInt(4, obj.getBanco().getCodigo().intValue());
        sqlAlterar.setString(5, obj.getConta());
        sqlAlterar.setDouble(6, obj.getValor());
        sqlAlterar.setTimestamp(7, Uteis.getDataJDBCTimestamp(obj.getDataCompensacao()));
        sqlAlterar.setString(8, obj.getVistaOuPrazo());
        sqlAlterar.setString(9, obj.getCpf());
        sqlAlterar.setString(10, obj.getCnpj());
        sqlAlterar.setString(11, obj.getSituacao());
        sqlAlterar.setString(12, obj.getNomeNoCheque());
        if (obj.getMovConta().getCodigo() > 0){
        	sqlAlterar.setInt(13, obj.getMovConta().getCodigo());
        }else{
        	sqlAlterar.setNull(13, 0);
        }
        sqlAlterar.setDouble(14, obj.getValorTotal());

        sqlAlterar.setString(15, obj.getProdutosPagos());

        sqlAlterar.setInt(16, obj.getCodigo().intValue());
        
        sqlAlterar.execute();
        if(obj.getComposicao()!= null && !obj.getComposicao().equals("") && obj.getSituacao().equals("CA")){
            apagarComposicao(obj.getCodigo());
        }
    }

    public void alterarSituacao(ChequeVO obj, boolean devolverCheque) throws Exception {
        PreparedStatement sqlAlterar = con.prepareStatement("UPDATE cheque set situacao=?, dataDevolucao=? WHERE codigo = ? ");
        sqlAlterar.setString(1, obj.getSituacao());
        if (devolverCheque){
            sqlAlterar.setTimestamp(2, Uteis.getDataJDBCTimestamp(Calendario.hoje()));
        }else{
            sqlAlterar.setNull(2, Types.NULL);
        }
        sqlAlterar.setInt(3, obj.getCodigo());
        sqlAlterar.execute();
    }



    /**
     * Operação responsável por excluir no BD um objeto da classe <code>ChequeVO</code>.
     * Sempre localiza o registro a ser excluído através da chave primária da entidade.
     * Primeiramente verifica a conexão com o banco de dados e a permissão do usuário
     * para realizar esta operacão na entidade.
     * Isto, através da operação <code>excluir</code> da superclasse.
     * @param obj    Objeto da classe <code>ChequeVO</code> que será removido no banco de dados.
     * @exception Exception Caso haja problemas de conexão ou restrição de acesso.
     */
    public void excluir(ChequeVO obj) throws Exception {
        excluir(getIdEntidade());
        String sql = "DELETE FROM Cheque WHERE ((codigo = ?))";
        PreparedStatement sqlExcluir = con.prepareStatement(sql);
        sqlExcluir.setInt(1, obj.getCodigo().intValue());
        sqlExcluir.execute();
    }

    /**
     * Operação responsável por excluir todos os objetos da <code>ChequeVO</code> no BD.
     * Faz uso da operação <code>excluir</code> disponível na classe <code>ChequeVO</code>.
     * @param movPagamento campo chave para exclusão dos objetos no BD.
     * @exception Exception  Erro de conexão com o BD ou restrição de acesso a esta operação.
     */
    public void excluirPagamentoCheques(Integer movPagamento) throws Exception {
        excluir(getIdEntidade());
        String sql = "DELETE FROM Cheque WHERE (movPagamento = ?)";
        PreparedStatement sqlExcluir = con.prepareStatement(sql);
        sqlExcluir.setInt(1, movPagamento);
        sqlExcluir.execute();
    }

    /**
     * Operação responsável por alterar todos os objetos da <code>ChequeVO</code> contidos em um Hashtable no BD.
     * Faz uso da operação <code>excluirPagamentoCheques</code> e <code>incluirPagamentoCheques</code> disponíveis na classe <code>Cheque</code>.
     * @param objetos  List com os objetos a serem alterados ou incluídos no BD.
     * @exception Exception  Erro de conexão com o BD ou restrição de acesso a esta operação.
     */
    public void alterarPagamentoCheques(MovPagamentoVO movPagamento, List objetos) throws Exception {
    	  Iterator e = objetos.iterator();
    	  String codigos = "";
    	  MovPagamentoVO originalMov = (MovPagamentoVO) movPagamento.getObjetoVOAntesAlteracao();
          String chequesAlterados = "";
          while (e.hasNext()) {
              ChequeVO objeto = (ChequeVO) e.next();
              if (objeto.getCodigo().equals(0)) {
                  incluir(objeto);
              } else {
            	  if (((objeto.getComposicao() != null && !objeto.getComposicao().equals("")) || movPagamento.getCredito()) &&
              			  (originalMov != null && originalMov.getCodigo().intValue() != 0)){
    	            	  Iterator i = originalMov.getChequeVOs().iterator();
                          boolean temOriginal = false;
    	            	  while(i.hasNext()){
    	            		  ChequeVO original = (ChequeVO) i.next();
    	            		  if (objeto.getCodigo().equals(original.getCodigo())){
    	            			  chequesAlterados = alterarChequeTransferido(objeto, original, chequesAlterados);
                                          temOriginal = true;
    	            		  }
    	            	  }
                          if(!temOriginal){
                              alterar(objeto);
                          }
              	  }else{
              		  alterar(objeto);
              	  }
              }
              if (codigos.equals("")) {
            	  codigos += objeto.getCodigo().toString();
              } else {
            	  codigos += ","+objeto.getCodigo().toString();
              }
          }
          if (!codigos.equals("")) {
        	  excluirChequesAlterarPagamento(movPagamento.getCodigo(), codigos);
          }
    }
    
    public String alterarChequeTransferido(ChequeVO obj, ChequeVO original,String chequesAlterados) throws Exception {
    	ChequeVO.validarDados(obj);
        alterar(getIdEntidade());
        String sqlConsulta = "SELECT codigo FROM cheque "; 
        String sql = "UPDATE Cheque set  numero=?, agencia=?, "
                + "banco=?, conta=?, dataCompesancao=?, vistaOuPrazo=?, "
                + "cpf=?, cnpj=?, nomenocheque=?, movconta=?  ";
        String sqlWhere = " WHERE numero=? AND agencia=? AND banco=? AND conta=? AND (codigo = ? OR movpagamento <> ?)";
        if(!UteisValidacao.emptyString(chequesAlterados)){
            sqlWhere += " and codigo not in ("+chequesAlterados+")";
        }
        PreparedStatement sqlConsultar = con.prepareStatement(sqlConsulta+sqlWhere);
        int j = 1;
        sqlConsultar.setString(j++, original.getNumero());
        sqlConsultar.setString(j++, original.getAgencia());
        sqlConsultar.setInt(j++, original.getBanco().getCodigo().intValue());
        sqlConsultar.setString(j++, original.getConta());
        sqlConsultar.setInt(j++, original.getCodigo());
        sqlConsultar.setInt(j++, original.getMovPagamento());
        ResultSet tabelaResultado = sqlConsultar.executeQuery();
        while (tabelaResultado.next()) {
            if(UteisValidacao.emptyString(chequesAlterados)){
                chequesAlterados += tabelaResultado.getInt("codigo");
            }else{
                 chequesAlterados += ","+tabelaResultado.getInt("codigo");
            }
                
        }
        
        PreparedStatement sqlAlterar = con.prepareStatement(sql+sqlWhere);
        int i = 1;
        sqlAlterar.setString(i++, obj.getNumero());
        sqlAlterar.setString(i++, obj.getAgencia());
        sqlAlterar.setInt(i++, obj.getBanco().getCodigo().intValue());
        sqlAlterar.setString(i++, obj.getConta());
        sqlAlterar.setTimestamp(i++, Uteis.getDataJDBCTimestamp(obj.getDataCompensacao()));
        sqlAlterar.setString(i++, obj.getVistaOuPrazo());
        sqlAlterar.setString(i++, obj.getCpf());
        sqlAlterar.setString(i++, obj.getCnpj());
        sqlAlterar.setString(i++, obj.getNomeNoCheque());
        if (obj.getMovConta().getCodigo() > 0){
        	sqlAlterar.setInt(i++, obj.getMovConta().getCodigo());
        }else{
        	sqlAlterar.setNull(i++, 0);
        }
        sqlAlterar.setString(i++, original.getNumero());
        sqlAlterar.setString(i++, original.getAgencia());
        sqlAlterar.setInt(i++, original.getBanco().getCodigo().intValue());
        sqlAlterar.setString(i++, original.getConta());
        sqlAlterar.setInt(i++, original.getCodigo());
        sqlAlterar.setInt(i++, original.getMovPagamento());
        
        sqlAlterar.execute();
        
        return chequesAlterados;
		
    }

	private void excluirChequesAlterarPagamento(Integer movPagamento,
			String codigosCheques) throws Exception {
		List<Integer> lotesAtualizar = new ArrayList<Integer>();
        ConfiguracaoFinanceiro confiDAO = new ConfiguracaoFinanceiro(con);
		ConfiguracaoFinanceiroVO conf = confiDAO.consultar();
        confiDAO = null;
		if(!conf.getUsarMovimentacaoContas()){
			ResultSet consulta = criarConsulta(" SELECT distinct cch.lote FROM chequecartaolote cch " +
						  " INNER JOIN  cheque ch ON ch.codigo = cch.cheque " +
						  " WHERE ch.movpagamento = "+movPagamento, con);
			while(consulta.next()){
				lotesAtualizar.add(consulta.getInt("lote"));
			}
		}
        excluir(getIdEntidade());
        String [] codigos= codigosCheques.split(","); 
        String sql = "DELETE FROM Cheque WHERE (movPagamento = ?) and codigo not in (";
        for (int i = 0; i < codigos.length; i++){
        	if (i == 0){
        		sql += " ? ";
        	}else {
        		sql += ", ?";
        	}
        }
        sql += ")";
        PreparedStatement sqlExcluir = con.prepareStatement(sql);
        int i = 1;
        sqlExcluir.setInt(i++, movPagamento.intValue());
        for (int j = 0; j < codigos.length; j++){
             sqlExcluir.setInt(i++, Integer.parseInt(codigos[j]));
        }
        sqlExcluir.execute();
        
        for(Integer lote : lotesAtualizar){
            Lote loteDAO = new Lote(con);
            loteDAO.atualizarValorLote(lote, true);
            loteDAO = null;
        }
		
	}

	/**
     * Operação responsável por incluir objetos da <code>ChequeVO</code> no BD.
     * Garantindo o relacionamento com a entidade principal <code>financeiro.MovPagamento</code> através do atributo de vínculo.
     * @param objetos List contendo os objetos a serem gravados no BD da classe.
     * @exception Exception  Erro de conexão com o BD ou restrição de acesso a esta operação.
     */
    public void incluirPagamentoCheques(Integer movPagamentoPrm, List objetos) throws Exception {
        try {
            Iterator e = objetos.iterator();
            while (e.hasNext()) {
                Integer codigo = 0;
                ChequeVO obj = (ChequeVO) e.next();
                codigo = obj.getCodigo();
                obj.setMovPagamento(movPagamentoPrm);
                if (codigo > 0 && obj.getSituacao().equals("EA")) {
                    obj.setComposicao(obterComposicao(codigo));
                }
                if (!obj.getComposicaoNova().equals("") && obj.getSituacao().equals("EA")) {
                    if (obj.getComposicao() != null && !obj.getComposicao().equals("")) {
                        obj.setComposicao(tratarDuplicacaoComposicao(obj.getComposicao() + "," + obj.getComposicaoNova()));
                    } else {
                        obj.setComposicao(obj.getComposicaoNova());
                    }
                }
                incluir(obj);
                if (codigo > 0 && obj.getSituacao().equals("EA")) {
                    if (obj.getComposicao() != null && !obj.getComposicao().equals("") && obj.codigoPertenceAComposicao(codigo)) {
                        Lote loteDAO = new Lote(con);
                        List<LoteVO> lotes = loteDAO.consultarPorChequeLista(codigo, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
                        loteDAO = null;
                        for (LoteVO lote : lotes) {
                            if (lote.getCodigo() > 0) {
                                adicionarChequeNoLoteCreditoContaCorrente(lote, obj);
                                alterarHistoricoChequeCreditoContaCorrente(codigo, obj);
                            }
                        }
                    } else {
                        alterarChequeNoLoteCreditoContaCorrente(codigo, obj);
                    }
                    if ((obj.getComposicao() != null) && (!obj.getComposicao().equals("")) && obj.codigoPertenceAComposicao(codigo)) {
                        NFSeEmitida nfSeEmitidaDAO = new NFSeEmitida(con);
                        nfSeEmitidaDAO.incluirNFSeParaChequeComposicao(obj.getComposicao(), obj.getCodigo());
                        nfSeEmitidaDAO = null;
                    } else {
                        NFSeEmitida nfSeEmitidaDAO = new NFSeEmitida(con);
                        nfSeEmitidaDAO.atualizarCheque(codigo, obj.getCodigo());
                        nfSeEmitidaDAO = null;
                    }
                }
            }
        } catch (Exception e) {
            throw e;
        }
    }

    /**
     * Operação responsável por consultar todos os <code>ChequeVO</code> relacionados a um objeto da classe <code>financeiro.MovPagamento</code>.
     * @param movPagamento  Atributo de <code>financeiro.MovPagamento</code> a ser utilizado para localizar os objetos da classe <code>ChequeVO</code>.
     * @return List  Contendo todos os objetos da classe <code>ChequeVO</code> resultantes da consulta.
     * @exception Exception  Erro de conexão com o BD ou restrição de acesso a esta operação.
     */
    public List<ChequeVO> consultarPagamentoCheques(Integer movPagamento, 
            Boolean comLote, boolean pagamentoAvulso,
            boolean chequeAvista,boolean chequeAprazo,int nivelMontarDados) throws Exception {
        consultar(getIdEntidade());
        List objetos = new ArrayList();
        String sql = "SELECT * FROM Cheque WHERE "+(pagamentoAvulso ? " movconta = ? " : " movPagamento = ? ");
        if (comLote != null) {
            if (comLote) {
                sql += " and codigo IN (select distinct(cheque) from chequecartaolote where cheque is not null)";
            } else {
                sql += " and codigo NOT IN (select distinct(cheque) from chequecartaolote where cheque is not null)";
            }
        }
        if(chequeAprazo && !chequeAvista){
            sql += " and vistaouprazo = 'PR' ";
        }
        if(!chequeAprazo && chequeAvista){
            sql += " and vistaouprazo = 'AV' ";
        }
        sql += " order by cheque.datacompesancao ";
        PreparedStatement sqlConsulta = con.prepareStatement(sql);
        sqlConsulta.setInt(1, movPagamento.intValue());
        ResultSet resultado = sqlConsulta.executeQuery();
        while (resultado.next()) {
            ChequeVO novoObj = new ChequeVO();
            novoObj = Cheque.montarDados(resultado, nivelMontarDados, con);
            objetos.add(novoObj);
        }
        return objetos;
    }

    public List<ChequeVO> consultarPorPagamentoCompensacao(int mp, Date compi, Date compf, Boolean pesquisarComLote, 
    								Boolean pagamentoAvulso,
                                                                boolean chequeAvista,boolean chequeAprazo,boolean considerarDataOriginal,
                                                                int nivelMontarDados) throws Exception {
        StringBuilder sql = new StringBuilder();
        sql.append("SELECT * FROM cheque WHERE ");
        sql.append(pagamentoAvulso ? " movconta = " + mp : " movpagamento = " + mp);
        if(considerarDataOriginal){
            sql.append(" and ((dataoriginal >= '").append(Uteis.getDataJDBC(compi)).append(" 00:00:00'");
            sql.append(" and dataoriginal <= '").append(Uteis.getDataJDBC(compf)).append(" 23:59:59' )");
            sql.append(" or (datacompesancao >= '").append(Uteis.getDataJDBC(compi)).append(" 00:00:00'");
            sql.append(" and datacompesancao <= '").append(Uteis.getDataJDBC(compf)).append(" 23:59:59' and dataoriginal is null)) ");
        }else{
            sql.append(" and datacompesancao >= '").append(Uteis.getDataJDBC(compi)).append(" 00:00:00'");
            sql.append(" and datacompesancao <= '").append(Uteis.getDataJDBC(compf)).append(" 23:59:59'");
        }
        if (pesquisarComLote != null) {
            if (pesquisarComLote) {
                sql.append(" and codigo IN (SELECT distinct(cheque) from chequecartaolote where cheque is not null)");
            } else {
                sql.append(" and codigo not IN (SELECT distinct(cheque) from chequecartaolote where cheque is not null)");
            }
        }
        if(chequeAprazo && !chequeAvista){
            sql.append(" and vistaouprazo = 'PR'");
        }
        if(!chequeAprazo && chequeAvista){
            sql.append(" and vistaouprazo = 'AV'");
        }
        
        PreparedStatement sqlConsulta = con.prepareStatement(sql.toString());
        ResultSet resultado = sqlConsulta.executeQuery();
        return montarDadosConsulta(resultado, nivelMontarDados, con);
    }

    public ResultSet consultarPorPeriodoLC(String nome, Date lanci, Date lancf, Date compi, Date compf, int empresa, int nivelMontarDados) throws Exception {
        StringBuilder sql = new StringBuilder();
        sql.append("SELECT ch.*, mp.datalancamento, mp.nomepagador FROM cheque ch ")
        .append("INNER JOIN movpagamento mp ON mp.codigo = ch.movpagamento ")
        .append("WHERE ch.situacao != 'CA'");
        if(!UteisValidacao.emptyNumber(empresa)){
            sql.append(" AND mp.empresa  = ").append(empresa);
        }
        if (!nome.trim().isEmpty()) {
            sql.append(" AND mp.nomepagador LIKE '%").append(nome).append("%'");
        }
        if (lanci != null) {
            sql.append(" AND mp.datalancamento >= '").append(Uteis.getDataJDBC(lanci)).append(" 00:00:00'").
                    append(" AND mp.datalancamento <= '").append(Uteis.getDataJDBC(lancf)).append(" 23:59:59'");
        }
        if (compi != null) {
            sql.append(" AND ch.datacompesancao >= '").append(Uteis.getDataJDBC(compi)).append(" 00:00:00'").
                    append(" AND ch.datacompesancao <= '").append(Uteis.getDataJDBC(compf)).append(" 23:59:59'");
        }
        sql.append(" ORDER BY mp.nomepagador");
        Statement sqlConsulta = con.createStatement();
        return sqlConsulta.executeQuery(sql.toString());
    }

    public double consultarValorChequesLote(int lote) throws Exception {
        String sql = "SELECT SUM(valor) as total FROM cheque "
                + " INNER JOIN chequecartaolote ccl on cheque.codigo = ccl.cheque "
                + " WHERE ccl.lote = " + lote;
        Statement sqlConsulta = con.createStatement();
        ResultSet resultado = sqlConsulta.executeQuery(sql);
        if (resultado.next()) {
            return resultado.getDouble("total");
        }
        return 0.0;
    }

    public List<ChequeVO> consultarPorLote(int lote, int nivelMontarDados) throws Exception {
        String sqlStr = "SELECT * FROM cheque c INNER JOIN chequecartaolote ccl ON ccl.cheque = c.codigo WHERE ccl.lote = " + lote;
        Statement stm = con.createStatement();
        ResultSet tabelaResultado = stm.executeQuery(sqlStr);
        return montarDadosConsulta(tabelaResultado, nivelMontarDados, con);
    }

    public List<ChequeVO> consultarPorMovContaLote(int movConta, int nivelMontarDados) throws Exception {
        String sqlStr = "SELECT * FROM cheque c "
                + "INNER JOIN chequecartaolote ccl ON ccl.cheque = c.codigo "
                + "INNER JOIN movconta mc ON ccl.lote = mc.lote "
                + "WHERE mc.codigo = " + movConta;
        Statement stm = con.createStatement();
        ResultSet tabelaResultado = stm.executeQuery(sqlStr);
        return montarDadosConsulta(tabelaResultado, nivelMontarDados, con);
    }

    public List<ChequeVO> consultarPorMovConta(int movConta, String conjunto, int nivelMontarDados) throws Exception {
        String sqlStr = "SELECT * FROM cheque c "
        	+ (UteisValidacao.emptyString(conjunto) ? "WHERE c.movconta = " + movConta :"WHERE c.movconta IN (" + conjunto+")");
        
        
        Statement stm = con.createStatement();
        ResultSet tabelaResultado = stm.executeQuery(sqlStr);
        return montarDadosConsulta(tabelaResultado, nivelMontarDados, con);
    }

    public ResultSet consultarParaGestaoLote(int lote) throws Exception {
        StringBuilder sql = new StringBuilder();
        sql.append("SELECT ch.*, b.codigobanco, mp.datalancamento, mp.nomepagador FROM cheque ch ")
        .append("INNER JOIN banco b ON b.codigo = ch.banco ")
        .append("INNER JOIN movpagamento mp ON mp.codigo = ch.movpagamento ")
        .append("INNER JOIN chequecartaolote ccl ON ccl.cheque = ch.codigo ")
        .append("WHERE ccl.lote = ").append(lote);
        sql.append(" UNION ALL ");
        sql.append(" SELECT ch.*, b.codigobanco, mc.datalancamento, p.nome FROM cheque ch ");
        sql.append("INNER JOIN banco b ON b.codigo = ch.banco and (ch.movpagamento is null or ch.movpagamento = 0)");
        sql.append(" INNER JOIN movconta mc ON mc.codigo = ch.movconta ");
        sql.append(" INNER JOIN pessoa p ON p.codigo = mc.pessoa  ");
        sql.append(" INNER JOIN chequecartaolote ccl ON ccl.cheque = ch.codigo WHERE ccl.lote = ").append(lote);
        Statement stm = con.createStatement();
        return stm.executeQuery(sql.toString());
    }

    /**
     * Responsável por montar os dados de vários objetos, resultantes de uma consulta ao banco de dados (<code>ResultSet</code>).
     * Faz uso da operação <code>montarDados</code> que realiza o trabalho para um objeto por vez.
     * @return  List Contendo vários objetos da classe <code>PagamentoMovParcelaVO</code> resultantes da consulta.
     */
    public static List<ChequeVO> montarDadosConsulta(ResultSet tabelaResultado, int nivelMontarDados, Connection con) throws Exception {
        List<ChequeVO> vetResultado = new ArrayList();
        while (tabelaResultado.next()) {
            ChequeVO obj = new ChequeVO();
            obj = montarDados(tabelaResultado, nivelMontarDados, con);
            vetResultado.add(obj);
        }
        return vetResultado;
    }

    /**
     * Responsável por montar os dados resultantes de uma consulta ao banco de dados (<code>ResultSet</code>)
     * em um objeto da classe <code>PagamentoMovParcelaVO</code>.
     * @return  O objeto da classe <code>PagamentoMovParcelaVO</code> com os dados devidamente montados.
     */
    public static ChequeVO montarDados(ResultSet dadosSQL, int nivelMontarDados, Connection con) throws Exception {
        ChequeVO obj = new ChequeVO();
        obj.setCodigo(dadosSQL.getInt("codigo"));
        obj.setMovPagamento(dadosSQL.getInt("movPagamento"));
        obj.setAgencia(dadosSQL.getString("agencia"));
        obj.getBanco().setCodigo(dadosSQL.getInt("banco"));
        obj.setConta(dadosSQL.getString("conta"));
        obj.setNumero(dadosSQL.getString("numero"));
        obj.setValor(dadosSQL.getDouble("valor"));
        obj.setValorTotal(dadosSQL.getDouble("valortotal"));
        obj.setComposicao(dadosSQL.getString("composicao"));
        obj.setDataCompensacao(dadosSQL.getTimestamp("dataCompesancao"));
        obj.setVistaOuPrazo(dadosSQL.getString("vistaOuPrazo"));
        obj.setCpf(dadosSQL.getString("cpf"));
        obj.setCnpj(dadosSQL.getString("cnpj"));
        obj.setSituacao(dadosSQL.getString("situacao"));
        obj.setDataOriginal(dadosSQL.getDate("dataoriginal"));
        obj.setNomeNoCheque(dadosSQL.getString("nomenocheque"));
        obj.getMovConta().setCodigo(dadosSQL.getInt("movconta"));
        obj.setProdutosPagos(dadosSQL.getString("produtospagos"));
        obj.setNovoObj(false);
        obj.setDataDevolucao(dadosSQL.getTimestamp("dataDevolucao"));
        if (nivelMontarDados == Uteis.NIVELMONTARDADOS_DADOSBASICOS) {
            return obj;
        }
        if (nivelMontarDados == Uteis.NIVELMONTARDADOS_MOVPAGAMENTOSINTETICO) {
            return obj;
        }
        if (nivelMontarDados == Uteis.NIVELMONTARDADOS_GESTAORECEBIVEIS) {
            montarDadosBanco(obj, Uteis.NIVELMONTARDADOS_DADOSBASICOS, con);
            return obj;
        }
        montarDadosBanco(obj, Uteis.NIVELMONTARDADOS_DADOSBASICOS, con);
        new HistoricoCheque(con).getCodigoLoteCheque(obj, true);
        obj.setPagaContaFinanceiro(chequePagaContaFinanceiro(obj.getCodigo(), con));
        return obj;
    }
    
    

    public static void montarDadosBanco(ChequeVO obj, int nivelMontarDados, Connection con) throws Exception {
        if (obj.getBanco().getCodigo().intValue() == 0) {
            obj.setBanco(new BancoVO());
            return;
        }
        Banco banco = new Banco(con);
        obj.setBanco(banco.consultarPorChavePrimaria(obj.getBanco().getCodigo(), nivelMontarDados));
    }

    /**
     * Operação responsável por localizar um objeto da classe <code>PagamentoMovParcelaVO</code>
     * através de sua chave primária. 
     * @exception Exception Caso haja problemas de conexão ou localização do objeto procurado.
     */
    public ChequeVO consultarPorChavePrimaria(Integer codigoPrm, int nivelMontarDados) throws Exception {
        consultar(getIdEntidade(), false);
        String sql = "SELECT * FROM Cheque WHERE codigo = ?";
        PreparedStatement sqlConsultar = con.prepareStatement(sql);
        sqlConsultar.setInt(1, codigoPrm.intValue());
        ResultSet tabelaResultado = sqlConsultar.executeQuery();
        if (!tabelaResultado.next()) {
            throw new ConsistirException("Dados Não Encontrados ( PagamentoMovParcela ).");
        }
        return (montarDados(tabelaResultado, nivelMontarDados, con));
    }

    public List<ChequeVO> consultarPorPagamentoSetandoProdutosRatear(int movPagamento, String produtosPagos, Boolean comLote) throws Exception {
        List<ChequeVO> cheques = consultarPagamentoCheques(movPagamento, comLote, false,false,false, Uteis.NIVELMONTARDADOS_TODOS);
        List<ProdutoRatear> produtosDoPagamento = ThreadDemonstrativoFinanceiro.pesquisarProdutosDoPagamento(
                movPagamento,produtosPagos, con, null);

        Ordenacao.ordenarLista(produtosDoPagamento, "codigoMovParcela");
        for (ChequeVO cheque : cheques) {

            //inicio a soma dos produtos adicionados ao cheque


            double valorPreenchidoDoCheque = povoarProdutosCheque(produtosDoPagamento, cheque, false, 0.0);

            povoarProdutosCheque(produtosDoPagamento, cheque, true, valorPreenchidoDoCheque);
        }
        return cheques;
    }

    private double povoarProdutosCheque(List<ProdutoRatear> produtosDoPagamento, ChequeVO cheque, boolean produtoMensal, Double valorPreenchidoDoCheque) {

        //itero nos produtos
        Iterator i = produtosDoPagamento.iterator();
        while (i.hasNext()) {
            ProdutoRatear produto = (ProdutoRatear) i.next();
            //verifico se o cheque já está com valor todo preenchido
            if (cheque.getValor().doubleValue() > valorPreenchidoDoCheque && produtoMensal == produto.getTipoProduto().equals("PM") && !produto.getEscolhido()) {
                //verifico o quanto falta para preencher o cheque
                double valorRestante = cheque.getValor().doubleValue() - valorPreenchidoDoCheque;
                //se o que falta pra preencher o valor do cheque é maior do que o valor do produto
                //então o valor a ratear deve ser o valor do produto. se não, pegar uma parte do produto
                if (valorRestante >= produto.getValorMovProdutoParcela()) {
                    produto.setEscolhido(true);
                    cheque.getProdutosRateio().add(produto);
                    valorPreenchidoDoCheque += produto.getValorMovProdutoParcela();
                } else {
                    ProdutoRatear pr = produto.clone();
                    pr.setEscolhido(false);
                    pr.setValorMovProdutoParcela(valorRestante);
                    cheque.getProdutosRateio().add(pr);
                    produto.setValorMovProdutoParcela(produto.getValorMovProdutoParcela() - valorRestante);
                    valorPreenchidoDoCheque += valorRestante;
                }

            }
            if (Uteis.arredondarForcando2CasasDecimais(cheque.getValor().doubleValue()) == Uteis.arredondarForcando2CasasDecimais(valorPreenchidoDoCheque)) {
                break;
            }

        }
        return valorPreenchidoDoCheque;
    }

    public void incluirMovContaCheques(MovContaVO movConta, List<ChequeVO> objetos) throws Exception {
        try {
            if (!objetos.isEmpty()) {
                Empresa empresaDAO = new Empresa(con);
                movConta.setEmpresaVO(empresaDAO.consultarPorChavePrimaria(movConta.getEmpresaVO().getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS));
                empresaDAO = null;
                Iterator<ChequeVO> e = objetos.iterator();
                while (e.hasNext()) {
                    ChequeVO obj = e.next();
                    obj.setVistaOuPrazo(obj.getDataCompensacao().compareTo(Uteis.somarDias(Calendario.hoje(), movConta.getEmpresaVO().getNrDiasChequeAVista())) == 1 ? "PR" : "AV");
                    obj.getMovConta().setCodigo(movConta.getCodigo());
                    obj.setValorTotal(obj.getValor());
                    incluir(obj);
                }
            }
        } catch (Exception e) {
            throw e;
        }
    }

    /**
     * <AUTHOR> Alcides
     * 05/12/2012
     */
    public Map<Integer, Double> consultarValorSaidaParaContas(List<ChequeVO> cheques) throws Exception {
        Map<Integer, Double> contas = new HashMap<Integer, Double>();
        String cods = "";
        for (ChequeVO cheque : cheques) {
            cods = cods + " ," + cheque.getCodigo();
        }
        StringBuilder sql = new StringBuilder();
        
        sql.append(" SELECT distinct c.codigo, c.descricao, SUM(ch.valor) as valorsaida FROM historicocheque hc "); 
        sql.append(" INNER JOIN movconta mc ON mc.codigo = hc.movconta "); 
        sql.append(" INNER JOIN conta c ON mc.conta = c.codigo ");
        sql.append(" INNER JOIN cheque ch ON ch.codigo = hc.cheque "); 
        sql.append(" WHERE ch.codigo IN(").append(cods.replaceFirst(",", "")).append(") AND datafim is null "); 
        sql.append(" GROUP BY c.codigo, c.descricao; ");
        
        ResultSet resultSet = criarConsulta(sql.toString(), con);

        while (resultSet.next()) {
            contas.put(resultSet.getInt("codigo"), resultSet.getDouble("valorsaida"));
        }
        return contas;
    }

    public void adicionarChequeNoLote(LoteVO lote, ChequeVO cheque) throws Exception {
    	String[] codigos = cheque.getObterTodosChequesComposicao().split(",");
    	for (String codigo : codigos){
    		
    		executarConsulta("INSERT INTO chequecartaolote(cheque, lote) VALUES (" + codigo + "," + lote.getCodigo() + ")", con);
    	}
    }
    
    public void adicionarChequeNoLoteCreditoContaCorrente(LoteVO lote, ChequeVO cheque) throws Exception {
    		executarConsulta("INSERT INTO chequecartaolote(cheque, lote) VALUES (" + cheque.getCodigo() + "," + lote.getCodigo() + ")", con);
    }
    
    public void alterarChequeNoLoteCreditoContaCorrente(Integer codigoAntigo, ChequeVO novo) throws Exception {
		executarConsulta("UPDATE chequecartaolote  SET cheque =" + novo.getCodigo()   + " WHERE cheque =" + codigoAntigo, con);
		alterarHistoricoChequeCreditoContaCorrente(codigoAntigo,novo);
    }
    public void alterarHistoricoChequeCreditoContaCorrente(Integer codigoAntigo, ChequeVO novo) throws Exception {
		executarConsulta("UPDATE historicocheque  SET cheque =" + novo.getCodigo()   + " WHERE cheque =" + codigoAntigo, con);
    }

    public void removerChequeNoLote(LoteVO lote, ChequeVO cheque, boolean atualizarHistorico) throws Exception {
        executarConsulta("DELETE FROM chequecartaolote WHERE cheque in (" +cheque.getObterTodosChequesComposicao()+ ") AND lote = " + lote.getCodigo(), con);
        if(atualizarHistorico){
           executarConsulta("DELETE FROM historicocheque WHERE cheque in (" +cheque.getObterTodosChequesComposicao()+ ") AND lote = " + lote.getCodigo(), con);	
           executarConsulta("UPDATE historicocheque set datafim = null WHERE cheque in (" 
        		   +cheque.getObterTodosChequesComposicao()+ 
        		   ") AND codigo in ( select max(codigo) from historicocheque where  cheque in (" +cheque.getObterTodosChequesComposicao()+ "))", con);
        }
    }

    public void removerChequesPagamentosLote(Integer movPagamento) throws Exception {
        executarConsulta("DELETE FROM chequecartaolote WHERE cheque in (select codigo from cheque where movpagamento =" + movPagamento +");", con);
    }

    public void alterarMinimo(ChequeVO obj) throws Exception {
        alterar(getIdEntidade());
        String sqlComposicao = "";
        String[]  codigos = null;
        if(obj.getComposicao() != null && !obj.getComposicao().equals("")) {
        	 codigos = obj.getComposicao().split(",");
        	 for (String codigo : codigos ){
        		 sqlComposicao += ",?";
             }
        }
        
        ResultSet consulta = criarConsulta("SELECT dataoriginal, mp.datalancamento, e.nrdiaschequeavista "
                + " FROM cheque inner join movpagamento mp on mp.codigo = cheque.movpagamento  "
                + " inner join empresa e on mp.empresa = e.codigo"
                + " WHERE cheque.codigo = "+obj.getCodigo(), con);
        int nrDiasAVista = 0;
        String vistaouprazo = obj.getVistaOuPrazo();
        boolean dataoriginaljainformada = false;
        if(consulta.next()){
                nrDiasAVista = consulta.getInt("nrdiaschequeavista");
        	dataoriginaljainformada = consulta.getDate("dataoriginal") != null;
                Date dataLancamento = consulta.getDate("datalancamento");
                vistaouprazo = obj.getDataCompensacao().compareTo(Uteis.somarDias(dataLancamento, nrDiasAVista)) == 1 ? "PR" : "AV";
        }
        String sql = dataoriginaljainformada ? 
            "UPDATE Cheque SET dataCompesancao=?, vistaouprazo = ? WHERE codigo in (?"+sqlComposicao+") "
            : "UPDATE Cheque SET dataCompesancao=?, vistaouprazo = ?, dataoriginal=? WHERE codigo in (?"+sqlComposicao+") ";
        
        PreparedStatement sqlAlterar = con.prepareStatement(sql);
        int i = 1;
        sqlAlterar.setTimestamp(i++, Uteis.getDataJDBCTimestamp(obj.getDataCompensacao()));
        sqlAlterar.setString(i++, vistaouprazo);
        if(!dataoriginaljainformada)
        	sqlAlterar.setDate(i++, Uteis.getDataJDBC(obj.getDataOriginal()));

        sqlAlterar.setInt(i++, obj.getCodigo());
        if(!sqlComposicao.equals("")) {
	        for (String codigo : codigos ){
	        	sqlAlterar.setInt(i++, Integer.parseInt(codigo));
	        }
        }
        sqlAlterar.execute();
        
    }
    
    public Boolean verificarContemLote(Integer movPagamento) throws Exception{
    	return existe(" select * from chequecartaolote ccl "+
    				  " inner join cheque c on ccl.cheque = c.codigo "+
    				  " where situacao <> 'DV' AND c.movpagamento = "+movPagamento, con);
    }
    
    public Boolean verificarContemLoteMovConta(Integer movConta) throws Exception{
    	return existe(" select * from chequecartaolote ccl "+
    				  " inner join cheque c on ccl.cheque = c.codigo "+
    				  " where situacao <> 'DV' AND c.movconta = "+movConta, con);
    }
    
    
    public void incluirChequeRetiradoLote(List<ChequeTO> cheques, int lote, Integer movConta) throws Exception{
    	for(ChequeTO cheque : cheques){
            incluirChequeRetiradoSemCommit(cheque, lote, movConta);
    	}
    }

    public void incluirChequeRetiradoSemCommit(ChequeTO cheque, int lote, Integer movConta) throws Exception {
        String sql = "INSERT INTO chequeretiradolote( "
                + "datalancamento, datacompensacao, valor, agencia, conta, numero, codigocheque,"
                + "nome, banco, lote, movconta)"
                + "VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?);";
        int i = 0;
        PreparedStatement stm = con.prepareStatement(sql);
        stm.setDate(++i, Uteis.getDataJDBC(cheque.getDataLancamento()));
        stm.setDate(++i, Uteis.getDataJDBC(cheque.getDataCompensacao()));
        stm.setDouble(++i, Uteis.arredondarForcando2CasasDecimais(cheque.getValor()));
        stm.setString(++i, cheque.getAgencia());
        stm.setString(++i, cheque.getConta());
        stm.setString(++i, cheque.getNumero());
        stm.setInt(++i, cheque.getCodigo());
        stm.setString(++i, UteisValidacao.emptyString(cheque.getNomeNoCheque()) ? cheque.getNomePagador() : cheque.getNomeNoCheque());
        stm.setString(++i, cheque.getNumeroBanco());
        stm.setInt(++i, lote);
        resolveFKNull(stm, ++i, movConta);
        stm.execute();
        cheque.setRemovido(true);
    }
    
    public List<ChequeTO> consultarChequesRetirados(Integer lote, Integer movConta) throws Exception{
    	String sql = "SELECT * FROM chequeretiradolote ";
    	if(!UteisValidacao.emptyNumber(lote)){
    		sql += "WHERE lote = "+lote;
    	}else if(!UteisValidacao.emptyNumber(movConta)){
    		sql += "WHERE movconta = "+movConta;
    	}
    	ResultSet result = criarConsulta(sql, con);
    	List<ChequeTO> lista = new ArrayList<ChequeTO>(); 
    	while(result.next()){
    		lista.add(montarDadosChequesRetirados(result));
    	}
        Ordenacao.ordenarLista(lista, "nomePagador");
    	return lista;
    }
    
    private ChequeTO montarDadosChequesRetirados(ResultSet rs) throws Exception{
    	ChequeTO cheque = new ChequeTO();
    	cheque.setAgencia(rs.getString("agencia"));
    	cheque.setCodigo(rs.getInt("codigocheque"));
    	cheque.setDataCompensacao(rs.getDate("datacompensacao"));
        cheque.setDataLancamento(rs.getDate("datalancamento"));
    	cheque.setValor(rs.getDouble("valor"));
    	cheque.setConta(rs.getString("conta"));
    	cheque.setNumero(rs.getString("numero"));
    	cheque.setNomePagador(rs.getString("nome"));
    	cheque.setNumeroBanco(rs.getString("banco"));
        cheque.setMovConta(rs.getInt("movconta"));
    	cheque.setRemovido(true);
    	return cheque;
    }
    
    public static int chequePagaContaFinanceiro(Integer codigo, Connection con) throws Exception{
    	ResultSet resultSet = criarConsulta("select lote.pagamovconta from chequecartaolote  ccl inner join lote on lote.codigo = ccl.lote " +
    			" where ccl.cheque = "+codigo+" and lote.pagamovconta is not null", con);
    	int codigoMovConta = 0;
    	if(resultSet.next()){
            codigoMovConta = resultSet.getInt("pagamovconta");
    	}
        if (codigoMovConta == 0) {
            StringBuilder sql = new StringBuilder();
            sql.append("select mov.codigo \n");
            sql.append("from chequecartaolote hist\n");
            sql.append("inner join movConta mov on mov.lotePagouConta = hist.lote \n");
            sql.append("where hist.cheque = ").append(codigo);
            Statement st = con.createStatement();
            ResultSet rs = st.executeQuery(sql.toString());
            if (rs.next()){
                codigoMovConta = rs.getInt("codigo");
    }
        }
        return codigoMovConta;
    }

     public String obterComposicao(Integer cheque) throws Exception{
    	ResultSet resultSet = criarConsulta("select composicao from cheque where codigo = " + cheque + ";", con);
    	if(resultSet.next()){
    		return resultSet.getString("composicao");
    	}
    	return "";
    }

    private void apagarComposicao(Integer codigo) throws Exception {
        executarConsulta("UPDATE cheque  SET composicao = '' WHERE codigo =" + codigo, con);
    }

    public String obterAvisosVinculos(String composicao,Integer reciboAtual, boolean preparacao) throws Exception {
        StringBuilder sql = new StringBuilder();
        sql.append(" SELECT ch.codigo as cheque,ch.valor, re.codigo AS recibo, re.nomepessoapagador, mcc.codigo, pe.nome FROM cheque ch ");
        sql.append(" inner join movpagamento mp on ch.movpagamento = mp.codigo ");
        sql.append(" left join movimentocontacorrenteclientecomposicao mccc on mp.codigo = mccc.movpagamento ");
        sql.append(" left join movimentocontacorrentecliente mcc on mccc.movimentocontacorrentecliente = mcc.codigo ");
        sql.append(" left join recibopagamento re on re.codigo = mp.recibopagamento ");
        sql.append(" left join pessoa pe on pe.codigo = mcc.pessoa or pe.codigo = re.pessoapagador ");
        sql.append(" where ch.codigo in (").append(composicao).append(") and ((mcc.codigo is null or  ");
        sql.append(" mcc.codigo in (SELECT mov.codigo FROM movimentocontacorrentecliente AS mov  ");
        sql.append(" INNER JOIN pessoa p ON p.codigo = mov.pessoa ");
        sql.append(" WHERE mov.codigo = (SELECT codigo FROM movimentocontacorrentecliente ");
        sql.append(" WHERE movimentocontacorrentecliente.pessoa = mov.pessoa  ");
        sql.append(" ORDER BY movimentocontacorrentecliente.codigo DESC  ");
        sql.append(" LIMIT 1) AND  ");
        sql.append(" mov.saldoatual > 0))  or ( re.codigo is not null");
        if(!UteisValidacao.emptyNumber(reciboAtual)){
            sql.append(" and re.codigo <> ").append(reciboAtual);
        }
        sql.append(")) ");

        ResultSet resultSet = criarConsulta(sql.toString(), con);
        StringBuilder retorno = new StringBuilder("");
        List<Integer> chequesAdicionados = new ArrayList<Integer>();
    	while(resultSet.next()){
            if(!chequesAdicionados.contains(resultSet.getInt("cheque"))){
                if(resultSet.getInt("recibo") > 0){
                    retorno.append("*O recibo ").append(resultSet.getInt("recibo")).append(" do(a) aluno(a) ").append(resultSet.getString("nomepessoapagador"));
                    retorno.append(" será alterado.\n");
                } else {
                    if(preparacao){
                        retorno.append(resultSet.getString("nome")).append(".\n");
                    } else {
                        retorno.append("*Será retirado R$ ").append(Uteis.arredondarForcando2CasasDecimais(resultSet.getDouble("valor"))).append(" da conta do(a) aluno(a) ").append(resultSet.getString("nome")).append(".\n");
                    }
                }
                chequesAdicionados.add(resultSet.getInt("cheque"));
            }
            
    	}
        return retorno.toString();
    }
    
      private String tratarDuplicacaoComposicao(String composicoes) {
        String composicao = "";
        String[] todasComposicoes = composicoes.split(",");
        List<String> composicoesAdicionadas = new ArrayList<String>();
        for (String str :todasComposicoes){
            if (!composicoesAdicionadas.contains(str)){
                composicao = (composicao.equals("") ? str : composicao + ","+str);
                composicoesAdicionadas.add(str);
            }
        }
        return composicao;
        
    }
      
    public String obterProdutosPagos(Integer cheque) throws Exception{
    	ResultSet resultSet = criarConsulta("select produtospagos from cheque where codigo = " + cheque + ";", con);
    	if(resultSet.next()){
    		return resultSet.getString("produtospagos");
    	}
    	return "";
    } 
    
    public List<ChequeVO> consultarTelaCliente(Integer contrato) throws Exception{
        ResultSet rs = criarConsulta("select distinct ch.codigo, b.nome as banco, ch.numero, ch.agencia, ch.conta, ch.nomenocheque, "
                +"ch.datacompesancao, ch.valor, ch.valortotal, ch.situacao from cheque ch \n"
                +"INNER JOIN movpagamento mp ON mp.codigo = ch.movpagamento  \n"
                +"INNER JOIN recibopagamento rp ON rp.codigo = mp.recibopagamento  \n"
                +"INNER JOIN pagamentomovparcela pmp ON pmp.recibopagamento = rp.codigo  \n"
                +"INNER JOIN movparcela mpar ON mpar.codigo = pmp.movparcela AND mpar.contrato = "+contrato
                +"INNER JOIN banco b ON b.codigo = ch.banco ORDER BY datacompesancao", con); 
        List<ChequeVO> cheques = new ArrayList<ChequeVO>();
        while(rs.next()){
            ChequeVO cheque = new ChequeVO();
            cheque.setBanco(new BancoVO());
            cheque.setNumero(rs.getString("numero"));
            cheque.setAgencia(rs.getString("agencia"));
            cheque.setConta(rs.getString("conta"));
            cheque.setNomeNoCheque(rs.getString("nomenocheque"));
            cheque.setDataCompensacao(rs.getDate("datacompesancao"));
            cheque.setValor(rs.getDouble("valor"));
            cheque.setValorTotal(rs.getDouble("valortotal"));
            cheque.getBanco().setNome(rs.getString("banco"));
            cheque.setSituacao(rs.getString("situacao"));
            cheque.setCodigo(rs.getInt("codigo"));
            cheques.add(cheque);
        }
        return cheques;
        
    }

    public List<RelatorioDevolucaoChequeTO> consultarDevolucaoCheque(Integer codigoEmpresa, Date dataDevolucaoIni, Date dataDevolucaoFim,
                                                                     Integer codigoPessoa, String numeroCheque,
                                                                     String agencia, String conta, Integer banco,
                                                                     boolean consultarSomenteChequesNaoRecebidos,
                                                                     String tipoConsulta )throws Exception{
        if (dataDevolucaoIni == null) {
            throw new ValidacaoException("A data início do período de pesquisa deve ser informado.");
        }

        if (dataDevolucaoFim == null) {
            throw new ValidacaoException("A data final do período de pesquisa deve ser informado.");
        }
        if (Calendario.menor(dataDevolucaoFim, dataDevolucaoIni)) {
            throw new ValidacaoException("A DATA DE TÉRMINO do PERÍODO DE PESQUISA não pode ser menor que a DATA DE INÍCIO");
        }
        List<RelatorioDevolucaoChequeTO> lista = new ArrayList<RelatorioDevolucaoChequeTO>();
        StringBuilder sql = new StringBuilder();
        sql.append("select distinct prodMovpro.tipoproduto, p.nome as nomePessoa, b.nome as nomeBanco, movconta.dataquitacao, ch.* \n");
        sql.append("from cheque ch \n");
        sql.append("inner join movPagamento mp on mp.codigo = ch.movPagamento \n");
        sql.append("left join (select max(prod.tipoproduto) as tipoproduto , max(movProd.datalancamento) as datalancamento,movProd.chequedevolucao, movProd.situacao from movProduto movProd \n");
        sql.append("left join produto prod on prod.codigo = movProd.produto where movProd.chequedevolucao is not null group by 3,4  ) as prodMovpro on  prodMovpro.chequedevolucao = ch.codigo \n");
        sql.append("left join movconta on movconta.codigo = ch.movconta  \n");

        sql.append("inner join pessoa p on p.codigo = mp.pessoa \n");
        sql.append("inner join banco b on b.codigo = ch.banco \n");
        sql.append("where 0 = 0 \n");
        if ((codigoEmpresa != null) && (codigoEmpresa > 0)){
            sql.append(" and  mp.empresa = ").append(codigoEmpresa);
        }
        if ((codigoPessoa != null) && (codigoPessoa > 0)){
            sql.append(" and mp.pessoa = ").append(codigoPessoa);
        }
        if ((numeroCheque != null) && (!numeroCheque.trim().equals(""))){
            sql.append(" and ch.numero like '%").append(numeroCheque).append("%'");
        }
        if ((agencia != null) && (!agencia.trim().equals(""))){
            sql.append(" and ch.agencia like '%").append(agencia).append("%'");
        }
        if ((conta != null) && (!conta.trim().equals(""))){
            sql.append(" and ch.conta like '%").append(conta).append("%'");
        }
        if ((banco != null) && (banco > 0)){
            sql.append(" and ch.banco = ").append(banco);
        }
        if (consultarSomenteChequesNaoRecebidos){
            sql.append(" and prodMovpro.situacao ='EA' \n");
        }

        if(tipoConsulta != null && tipoConsulta.equals("TROCA")){
            sql.append(" and prodMovpro.tipoproduto <> 'CH' \n");
        }else if(tipoConsulta != null && tipoConsulta.equals("DEVOLVIDOS")){
            sql.append(" and prodMovpro.tipoproduto = 'CH' \n");
        }

        sql.append(" and ((dataDevolucao BETWEEN ? and ?) or ");
        sql.append(" (prodMovpro.tipoproduto = 'CH' AND movconta.dataquitacao BETWEEN ? and ?)) ");
        PreparedStatement pst = con.prepareStatement(sql.toString());
        pst.setTimestamp(1, Uteis.getDataJDBCTimestamp(Calendario.getDataComHoraZerada(dataDevolucaoIni)));
        pst.setTimestamp(2, Uteis.getDataJDBCTimestamp(Calendario.getDataComHora(dataDevolucaoFim, "23:59:59")));
        pst.setTimestamp(3, Uteis.getDataJDBCTimestamp(Calendario.getDataComHoraZerada(dataDevolucaoIni)));
        pst.setTimestamp(4, Uteis.getDataJDBCTimestamp(Calendario.getDataComHora(dataDevolucaoFim, "23:59:59")));
        ResultSet rs = pst.executeQuery();
        while (rs.next()){
            RelatorioDevolucaoChequeTO obj = new RelatorioDevolucaoChequeTO();
            obj.setNomePessoa(rs.getString("nomePessoa"));
            obj.setNumeroCheque(rs.getString("numero"));
            obj.setNomeNoCheque(rs.getString("nomeNoCheque"));
            obj.setAgenciaCheque(rs.getString("agencia"));
            obj.setContaCheque(rs.getString("conta"));
            obj.setNomeBancoCheque(rs.getString("nomeBanco"));
            obj.setValorCheque(rs.getDouble("valortotal"));
            obj.setDataCompensacao(rs.getTimestamp("dataCompesancao"));
            obj.setDataDevolucao(rs.getTimestamp("dataDevolucao"));
            if(obj.getDataDevolucao() == null){
                obj.setDataDevolucao(rs.getTimestamp("dataquitacao"));
            }
            if(rs.getString("tipoproduto") != null) {
                obj.setDevolvido(rs.getString("tipoproduto").equals("CH"));
            }
            lista.add(obj);
        }
        return lista;
    }
    
    public String existeJaExisteChequeComMesmosDados(ChequeVO chequeVO, boolean pagamento) throws Exception{
        String retorno = "";
        String sqlConsulta = "SELECT p.nome, m.codigo as pagamento, mc.codigo as conta FROM cheque c left join movpagamento m on m.codigo = c.movpagamento l" +
                "eft join movconta mc on mc.codigo = c.movconta left join pessoa p on p.codigo = m.pessoa or p.codigo = mc.pessoa ";
        String sqlWhere = " WHERE c.numero=? AND c.agencia=? AND c.banco=? AND c.conta=? ";
        PreparedStatement sqlConsultar = con.prepareStatement(sqlConsulta+sqlWhere);
        int j = 1;
        sqlConsultar.setString(j++, chequeVO.getNumero());
        sqlConsultar.setString(j++, chequeVO.getAgencia());
        sqlConsultar.setInt(j++, chequeVO.getBanco().getCodigo().intValue());
        sqlConsultar.setString(j++, chequeVO.getConta());
        ResultSet tabelaResultado = sqlConsultar.executeQuery();
        String dadosCheque;
        while (tabelaResultado.next()) {
            if(UteisValidacao.emptyNumber(tabelaResultado.getInt("pagamento"))) {
                retorno += String.format("A conta %s do Financeiro pertencente a  %s, ", new Object[]{
                        tabelaResultado.getInt("conta"), tabelaResultado.getString("nome")
                });
            } else {
                retorno += String.format("O pagamento %s pertencente a %s, "
                        , new Object[]{tabelaResultado.getInt("pagamento"), tabelaResultado.getString("nome")});


            }
            dadosCheque = String.format("já  tem um cheque com o banco %s, agencia %s, conta %s e número %s."
                    , new Object[]{chequeVO.getBanco().getCodigoBanco()
                            , chequeVO.getAgencia(), chequeVO.getConta(), chequeVO.getNumero()});
            retorno += dadosCheque;
            if(pagamento) {
                break;
            }
        }
        return retorno;
        
    }

    public void validarChequeExistente(List<ChequeVO> cheques) throws Exception {
        String mensagem = "";
        for (ChequeVO chequeVO: cheques) {
            mensagem = existeJaExisteChequeComMesmosDados(chequeVO, true);
            if(!UteisValidacao.emptyString(mensagem)){
                mensagem +=  " Sistema não permite cheques com os mesmo dados";
                throw  new ConsistirException(mensagem);
            }
        }

    }
}

/*
 * To change this template, choose Too<PERSON> | Templates
 * and open the template in the editor.
 */
package negocio.facade.jdbc.financeiro;

import negocio.comuns.financeiro.MovParcelaVO;
import negocio.comuns.financeiro.TransacaoMovParcelaVO;
import negocio.comuns.financeiro.TransacaoVO;
import negocio.comuns.financeiro.enumerador.SituacaoTransacaoEnum;
import negocio.comuns.utilitarias.ConsistirException;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisValidacao;
import negocio.facade.jdbc.arquitetura.SuperEntidade;
import negocio.facade.jdbc.arquitetura.SuperFacadeJDBC;
import negocio.interfaces.financeiro.TransacaoMovParcelaInterfaceFacade;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.*;

/**
 *
 * <AUTHOR>
 */
public class TransacaoMovParcela extends SuperEntidade implements TransacaoMovParcelaInterfaceFacade {

    public TransacaoMovParcela(Connection conex) throws Exception {
        super(conex);
    }

    @Override
    public void incluir(TransacaoMovParcelaVO obj) throws Exception {

        String sql = "INSERT INTO transacaomovparcela(transacao, movparcela, nrTentativaParcela, valorMovParcela, valorMulta, valorJuros) VALUES (?,?,?,?,?,?)";

        PreparedStatement sqlInserir = con.prepareStatement(sql);
        int i = 0;
        sqlInserir.setInt(++i, obj.getTransacao().getCodigo());
        sqlInserir.setInt(++i, obj.getMovParcela().getCodigo());
        sqlInserir.setInt(++i, (obj.getMovParcela().getNrTentativas() + 1));
        sqlInserir.setDouble(++i, obj.getValorParcela());
        sqlInserir.setDouble(++i, obj.getValorMulta());
        sqlInserir.setDouble(++i, obj.getValorJuros());
        sqlInserir.execute();
        obj.setCodigo(obterValorChavePrimariaCodigo());
        obj.setNovoObj(false);
    }

    @Override
    public void alterar(TransacaoMovParcelaVO obj) throws Exception {

        String sql = "UPDATE transacaomovparcela SET transacao = ? movparcela = ?, nrTentativaParcela = ?, valorMovParcela = ?, valorMulta = ?, valorJuros = ? WHERE codigo = ?";

        PreparedStatement sqlAlterar = con.prepareStatement(sql);
        int i = 0;
        sqlAlterar.setInt(++i, obj.getTransacao().getCodigo());
        sqlAlterar.setInt(++i, obj.getMovParcela().getCodigo());
        sqlAlterar.setInt(++i, obj.getMovParcela().getNrTentativas());
        sqlAlterar.setDouble(++i, obj.getValorParcela());
        sqlAlterar.setDouble(++i, obj.getValorMulta());
        sqlAlterar.setDouble(++i, obj.getValorJuros());
        sqlAlterar.setInt(++i, obj.getCodigo());
        sqlAlterar.execute();
    }

    /**
     * Operação responsável por excluir no BD um objeto da classe <code>ContratoComposicaoVO</code>.
     * Sempre localiza o registro a ser excluído através da chave primária da entidade.
     * Primeiramente verifica a conexão com o banco de dados e a permissão do usuário
     * para realizar esta operacão na entidade.
     * Isto, através da operação <code>excluir</code> da superclasse.
     * @param obj    Objeto da classe <code>ContratoComposicaoVO</code> que será removido no banco de dados.
     * @exception Exception Caso haja problemas de conexão ou restrição de acesso.
     */
    @Override
    public void excluir(TransacaoMovParcelaVO obj) throws Exception {
        String sql = "DELETE FROM transacaomovparcela WHERE codigo = ? ";
        PreparedStatement sqlExcluir = con.prepareStatement(sql);
        sqlExcluir.setInt(1, obj.getCodigo());
        sqlExcluir.execute();
    }

    public void excluir(Integer codigo) throws Exception {
        String sql = "DELETE FROM transacaomovparcela WHERE codigo = " + codigo;
        PreparedStatement sqlExcluir = con.prepareStatement(sql);
        sqlExcluir.execute();
    }

    private static List montarDadosConsulta(ResultSet tabelaResultado,
            Connection con) throws Exception {

        List vetResultado = new ArrayList();
        while (tabelaResultado.next()) {
            TransacaoMovParcelaVO obj = new TransacaoMovParcelaVO();
            obj = montarDados(tabelaResultado, con);
            vetResultado.add(obj);
        }
        return vetResultado;
    }

    private static TransacaoMovParcelaVO montarDados(ResultSet ds, Connection con) throws Exception {
        MovParcela movParcelaFacade;
        Transacao transacaoFacade;
        TransacaoMovParcelaVO obj = new TransacaoMovParcelaVO();
        try {
            movParcelaFacade = new MovParcela(con);
            transacaoFacade = new Transacao(con);

            MovParcelaVO movParcelaVO = movParcelaFacade.consultarPorChavePrimaria(
                    ds.getInt("movparcela"), Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA);
            TransacaoVO transacaoVO = transacaoFacade.consultarPorChavePrimaria(ds.getInt("transacao"));

            obj.setValorParcela(ds.getDouble("valorMovParcela"));
            obj.setValorMulta(ds.getDouble("valorMulta"));
            obj.setValorJuros(ds.getDouble("valorJuros"));
            obj.setNrTentativaParcela(ds.getInt("nrTentativaParcela"));
            obj.setCodigo(ds.getInt("codigo"));
            obj.setMovParcela(movParcelaVO);
            obj.setTransacao(transacaoVO);

        } finally {
            movParcelaFacade = null;
            transacaoFacade = null;
        }

        return obj;
    }

    @Override
    public TransacaoMovParcelaVO consultarPorChavePrimaria(final int codigo) throws Exception {
        String sql = "SELECT * FROM transacaomovparcela WHERE codigo = ?";
        PreparedStatement sqlConsultar = con.prepareStatement(sql);
        sqlConsultar.setInt(1, codigo);
        ResultSet tabelaResultado = sqlConsultar.executeQuery();
        if (!tabelaResultado.next()) {
            throw new ConsistirException(String.format("Dados Não Encontrados ( Transacao %s)",
                    new Object[]{
                        codigo
                    }));
        }
        return (montarDados(tabelaResultado, con));
    }

    @Override
    public List<TransacaoMovParcelaVO> cosultarPorCodigoTransacao(final int codigoTransacao) throws Exception {

        String sql = "SELECT * FROM transacaomovparcela WHERE transacao = ?";
        PreparedStatement sqlConsultar = con.prepareStatement(sql);
        sqlConsultar.setInt(1, codigoTransacao);
        ResultSet tabelaResultado = sqlConsultar.executeQuery();
        return montarDadosConsulta(tabelaResultado, con);

    }

    @Override
    public List<TransacaoMovParcelaVO> cosultarPorCodigoContrato(final int codigoContrato) throws Exception {

        String sql = "SELECT * FROM transacaomovparcela WHERE movparcela in (select codigo from movparcela where contrato = ?)";
        PreparedStatement sqlConsultar = con.prepareStatement(sql);
        sqlConsultar.setInt(1, codigoContrato);
        ResultSet tabelaResultado = sqlConsultar.executeQuery();
        return montarDadosConsulta(tabelaResultado, con);

    }

    @Override
    public List<TransacaoMovParcelaVO> cosultarPorCodigoParcela(final int codigoMovParcela) throws Exception {

        String sql = "SELECT * FROM transacaomovparcela WHERE movparcela = ?";
        PreparedStatement sqlConsultar = con.prepareStatement(sql);
        sqlConsultar.setInt(1, codigoMovParcela);
        ResultSet tabelaResultado = sqlConsultar.executeQuery();
        return montarDadosConsulta(tabelaResultado, con);

    }

    @Override
    public void excluirPorTransacao(final int codTransacao) throws Exception {
        String sql = "delete from transacaomovparcela where transacao = " + codTransacao;
        SuperFacadeJDBC.executarConsulta(sql, con);
    }

    public void excluirPorMovParcela(final int codMovParcela) throws Exception {
        String sql = "delete from transacaomovparcela where movparcela = " + codMovParcela;
        SuperFacadeJDBC.executarConsulta(sql, con);
    }

    public Set<Integer> obterListaParcelasRetentativa(Set<Integer> transacoes) throws SQLException {
        if (UteisValidacao.emptyList(transacoes)) {
            return new HashSet<>();
        }

        String codigos = "";
        for (Integer tra : transacoes) {
            codigos += ("," + tra);
        }

        StringBuilder sql = new StringBuilder();
        sql.append("select \n");
        sql.append("distinct mp.codigo \n");
        sql.append("from transacaomovparcela tm  \n");
        sql.append("inner join movparcela mp on mp.codigo = tm.movparcela \n");
        sql.append("where tm.transacao in (").append(codigos.replaceFirst(",", "")).append(") \n");
        sql.append("and mp.situacao = 'EA' \n");
        sql.append("and mp.valorparcela > 0 \n");
        sql.append("order by 1 \n");
        Set<Integer> listaParcelas = new HashSet<>();
        try (PreparedStatement stm = con.prepareStatement(sql.toString())) {
            try (ResultSet rs = stm.executeQuery()) {
                while (rs.next()) {
                    listaParcelas.add(rs.getInt("codigo"));
                }
            }
        }
        return listaParcelas;
    }

    public Map<Integer, Integer> obterListaCodigosExcluirVinculadasATransacaoIncorretamente(int empresa) throws SQLException {
        //Esse sql verifica quais transações possuem mais de uma parcela vinculada, onde uma transação pelo menos está paga e as outras não.
        //Verifica também se a transação está aprovada ou concluída com sucesso
        //Verifica também se o valor da parcela paga da transação é igual ao valor da transação
        //Considera somente transações de ontem até hoje

        Map<Integer, Integer> mapa = new HashMap<>();
        StringBuilder sql = new StringBuilder();
        sql.append("SELECT tmp.codigo AS transacaomovparcela_codigo, t.codigo as transacao_codigo \n");
        sql.append("FROM transacao t \n");
        sql.append("INNER JOIN transacaomovparcela tmp ON t.codigo = tmp.transacao \n");
        sql.append("INNER JOIN movparcela m ON m.codigo = tmp.movparcela \n");
        sql.append("WHERE t.dataprocessamento BETWEEN CURRENT_DATE - 1 AND CURRENT_DATE \n");
        sql.append("and t.situacao in (").append(SituacaoTransacaoEnum.APROVADA.getId()).append(", ").append(SituacaoTransacaoEnum.CONCLUIDA_COM_SUCESSO.getId()).append(") \n");
        sql.append("and t.empresa = ").append(empresa).append(" \n");
        sql.append("  AND m.situacao = 'EA' \n");
        sql.append("  AND EXISTS ( \n");
        sql.append("    SELECT 1 \n");
        sql.append("    FROM transacaomovparcela tmp2 \n");
        sql.append("    INNER JOIN movparcela m2 ON m2.codigo = tmp2.movparcela \n");
        sql.append("    WHERE tmp2.transacao = t.codigo \n");
        sql.append("      AND m2.situacao = 'PG' \n");
        sql.append("      AND CAST(tmp2.valormovparcela AS DECIMAL(10, 2)) = CAST(t.valor AS DECIMAL(10, 2)) \n");
        sql.append(" ) \n");
        sql.append("GROUP BY tmp.codigo, t.codigo \n");
        try (PreparedStatement stm = con.prepareStatement(sql.toString())) {
            try (ResultSet rs = stm.executeQuery()) {
                while (rs.next()) {
                    mapa.put(rs.getInt("transacaomovparcela_codigo"), rs.getInt("transacao_codigo"));
                }
            }
        }
        return mapa;
    }

    public boolean existeTransacaoRecenteParaMovParcela(List<Integer> movParcelaIds) throws SQLException {
        try {
            String sql =
                    "SELECT EXISTS ( \n" +
                            "  SELECT 1 \n" +
                            "    FROM transacaomovparcela tmp \n" +
                            "    JOIN transacao t ON tmp.transacao = t.codigo \n" +
                            "   WHERE tmp.movparcela = ANY(?) \n" +
                            "     AND t.dataprocessamento >= now() - INTERVAL '1 minute' \n" +
                            ");";

            try (PreparedStatement ps = con.prepareStatement(sql)) {
                // monta o array de bigint para o Postgres
                java.sql.Array pgArray = con.createArrayOf("bigint", movParcelaIds.toArray());
                ps.setArray(1, pgArray);

                try (ResultSet rs = ps.executeQuery()) {
                    if (rs.next()) {
                        return rs.getBoolean(1);
                    } else {
                        return false;
                    }
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
            return false;
        }

    }

}

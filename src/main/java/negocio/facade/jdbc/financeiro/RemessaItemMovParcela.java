package negocio.facade.jdbc.financeiro;

import negocio.comuns.arquitetura.LogVO;
import negocio.comuns.arquitetura.UsuarioVO;
import negocio.comuns.basico.ClienteVO;
import negocio.comuns.financeiro.RemessaItemMovParcelaVO;
import negocio.comuns.financeiro.RemessaItemVO;
import negocio.comuns.financeiro.TransacaoVO;
import negocio.comuns.utilitarias.Calendario;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisValidacao;
import negocio.facade.jdbc.arquitetura.Log;
import negocio.facade.jdbc.arquitetura.SuperEntidade;
import negocio.facade.jdbc.basico.Cliente;
import negocio.interfaces.financeiro.RemessaItemMovParcelaInterfaceFacade;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.List;
import negocio.comuns.financeiro.MovParcelaVO;
import negocio.comuns.utilitarias.ConsistirException;
import org.json.JSONArray;
import org.json.JSONObject;

/**
 * Created by GlaucoT on 17/04/2015
 */
public class RemessaItemMovParcela extends SuperEntidade implements RemessaItemMovParcelaInterfaceFacade {

    public RemessaItemMovParcela() throws Exception {
    }

    public RemessaItemMovParcela(Connection conexao) throws Exception {
        super(conexao);
    }

    public static List<RemessaItemMovParcelaVO> montarDadosConsulta(ResultSet tabelaResultado, int nivelMontarDados, Connection c) throws Exception {
        List<RemessaItemMovParcelaVO> vetResultado = new ArrayList<RemessaItemMovParcelaVO>();
        while (tabelaResultado.next()) {
            RemessaItemMovParcelaVO obj = montarDados(tabelaResultado, nivelMontarDados, c);
            vetResultado.add(obj);
        }
        return vetResultado;
    }

    private static RemessaItemMovParcelaVO montarDados(ResultSet ds, int nivelMontarDados, Connection con) throws Exception {
        RemessaItemMovParcelaVO obj = new RemessaItemMovParcelaVO();
        obj.setCodigo(ds.getInt("codigo"));
        obj.getMovParcelaVO().setCodigo(ds.getInt("movparcela"));
        obj.getRemessaItemVO().setCodigo(ds.getInt("remessaitem"));
        obj.setValorOriginal(ds.getDouble("valororiginal"));
        obj.setValorMulta(ds.getDouble("valorMulta"));
        obj.setValorJuros(ds.getDouble("valorJuros"));
        obj.setNrTentativaParcela(ds.getInt("nrTentativaParcela"));
        obj.setJsonEstorno(ds.getString("jsonEstorno"));

        if (Uteis.NIVELMONTARDADOS_DADOSBASICOS == nivelMontarDados) {
            consultarMovParcela(con, obj);
            return obj;
        }
        if (Uteis.NIVELMONTARDADOS_DADOSENTIDADESUBORDINADAS == nivelMontarDados) {
            consultarMovParcela(con, obj);

            RemessaItem remessaItemDAO = new RemessaItem(con);
            obj.setRemessaItemVO(remessaItemDAO.consultarPorChavePrimaria(obj.getRemessaItemVO().getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS));
            remessaItemDAO = null;

            obj.getRemessaItemVO().setMovParcela(obj.getMovParcelaVO());
        }
        return obj;
    }

    @Override
    public void incluir(RemessaItemMovParcelaVO obj) throws Exception {
        try {
            con.setAutoCommit(false);
            incluirSemCommit(obj);
            con.commit();
        } catch (Exception ex) {
            con.rollback();
            con.setAutoCommit(true);
            throw ex;
        } finally {
            con.setAutoCommit(true);
        }
    }

    @Override
    public void incluirSemCommit(RemessaItemMovParcelaVO obj) throws Exception {
        String sql = "INSERT INTO remessaitemmovparcela(remessaitem, movparcela, valororiginal, valorMulta, valorJuros, nrTentativaParcela) " +
                "VALUES (?, ?, ?, ?, ?, ?)";
        try (PreparedStatement ps = con.prepareStatement(sql)) {
            int i = 0;
            ps.setInt(++i, obj.getRemessaItemVO().getCodigo());
            ps.setInt(++i, obj.getMovParcelaVO().getCodigo());
            ps.setDouble(++i, obj.getMovParcelaVO().getValorParcela());
            ps.setDouble(++i, obj.getMovParcelaVO().getValorMulta());
            ps.setDouble(++i, obj.getMovParcelaVO().getValorJuros());
            ps.setInt(++i, (obj.getMovParcelaVO().getNrTentativas() + 1));
            ps.execute();
        }
        obj.setCodigo(obterValorChavePrimariaCodigo());
        obj.setNovoObj(false);
    }

    public void alterarMovParcela(RemessaItemMovParcelaVO obj) throws Exception {
        String sql = "update remessaitemmovparcela set movparcela = ? where codigo = ?;";
        try (PreparedStatement ps = con.prepareStatement(sql)) {
            if (obj.getMovParcelaVO() == null) {
                resolveIntegerNull(ps, 1, 0);
            } else {
                resolveIntegerNull(ps, 1, obj.getMovParcelaVO().getCodigo());
            }
            ps.setInt(2, obj.getCodigo());
            ps.execute();
        }
    }

    public void alterarJsonEstorno(RemessaItemMovParcelaVO obj) throws Exception {
        String sql = "update remessaitemmovparcela set jsonEstorno = ? where codigo = ?;";
        try (PreparedStatement ps = con.prepareStatement(sql)) {
            ps.setString(1, obj.getJsonEstorno());
            ps.setInt(2, obj.getCodigo());
            ps.execute();
        }
    }

    @Override
    public List<RemessaItemMovParcelaVO> consultarPorRemessaItem(Integer codigoRemessaItem, int nivelMontarDados) throws Exception {
        String sql = "SELECT * FROM remessaitemmovparcela WHERE remessaitem = ?";
        PreparedStatement stm = con.prepareStatement(sql);
        stm.setInt(1, codigoRemessaItem);
        ResultSet rs = stm.executeQuery();
        return montarDadosConsulta(rs, nivelMontarDados, con);
    }

    public List<RemessaItemMovParcelaVO> consultarPorParcelaItem(Integer codigoParcela, int nivelMontarDados) throws Exception {
        String sql = "SELECT * FROM remessaitemmovparcela WHERE movparcela = ?";
        PreparedStatement stm = con.prepareStatement(sql);
        stm.setInt(1, codigoParcela);
        ResultSet rs = stm.executeQuery();
        return montarDadosConsulta(rs, nivelMontarDados, con);
    }

    @Override
    public List<RemessaItemMovParcelaVO> alterarRemessaItemMovParcela(RemessaItemVO item,  List<MovParcelaVO> parcelasRemover, List<MovParcelaVO> parcelasAdicionar) throws Exception {
        removerRemessaItemMovParcela(item, parcelasRemover);
        List<RemessaItemMovParcelaVO> list = new ArrayList<RemessaItemMovParcelaVO>();
        for(MovParcelaVO parcela : parcelasAdicionar){
            list.add(incluir(item, parcela));
        }
        return list;
    }

    private RemessaItemMovParcelaVO incluir(RemessaItemVO item, MovParcelaVO parcela) throws Exception{
        RemessaItemMovParcelaVO itemMov = new RemessaItemMovParcelaVO();
        itemMov.setMovParcelaVO(parcela);
        itemMov.setRemessaItemVO(item);
        itemMov.setNovoObj(true);
        incluirSemCommit(itemMov);
        return itemMov;
    }

    private void removerRemessaItemMovParcela(RemessaItemVO item, List<MovParcelaVO> parcelasRemover) throws Exception{
        String codigosParcelas = Uteis.retornarCodigos(parcelasRemover);
        StringBuilder sqlDelete = new StringBuilder("DELETE FROM remessaitemmovparcela WHERE remessaitem = ").append(item.getCodigo()).append(" AND movparcela IN (").append(codigosParcelas).append(")");
        PreparedStatement ps = getCon().prepareStatement(sqlDelete.toString());
        ps.executeUpdate();
    }

    private static void consultarMovParcela(Connection con, RemessaItemMovParcelaVO obj) throws Exception {
        MovParcela movParcela = new MovParcela(con);
        MovParcelaVO movParcelaVO;
        try {
            movParcelaVO = movParcela.consultarPorChavePrimaria(obj.getMovParcelaVO().getCodigo(), Uteis.NIVELMONTARDADOS_MINIMOS);
            obj.getRemessaItemVO().setMovParcela(obj.getMovParcelaVO());

            MovProdutoParcela movProdutoParcela = new MovProdutoParcela(con);
            obj.getMovParcelaVO().setMovProdutoParcelaVOs(movProdutoParcela.consultarMovProdutoParcelasPorParcelas(obj.getMovParcelaVO().getCodigo(), Uteis.NIVELMONTARDADOS_DADOSENTIDADESUBORDINADAS));
            movProdutoParcela = null;

            Cliente cliente = new Cliente(con);
            obj.setClienteVO(cliente.consultarPorCodigoPessoa(obj.getMovParcelaVO() .getPessoa().getCodigo(), Uteis.NIVELMONTARDADOS_MINIMOS));
            cliente = null;
        } catch (ConsistirException ex) {
            movParcelaVO = new MovParcelaVO();
            movParcelaVO.setCodigo(obj.getMovParcelaVO().getCodigo());
            movParcelaVO.setDescricao("* *  PARCELA ESTORNADA  * *");
        }
        obj.setMovParcelaVO(movParcelaVO);
        movParcela = null;
    }

    public void excluirPorCodigoRemessa(int codigoRemessa) throws SQLException {
        String sql = "DELETE " +
                "FROM remessaitemmovparcela " +
                "WHERE remessaitem IN (SELECT codigo FROM remessaitem WHERE remessa = "+codigoRemessa+")";
        try (PreparedStatement ps = con.prepareStatement(sql)) {
            ps.execute();
        }
    }

    public void removerParcelaBoleto(ClienteVO clienteVO, RemessaItemVO remessaItemVO, UsuarioVO usuarioVO) throws Exception {
        try {
            con.setAutoCommit(false);

            if (remessaItemVO == null || UteisValidacao.emptyNumber(remessaItemVO.getCodigo())) {
                throw new Exception("RmessaItem não encontrado.");
            }

            //para remessa fechada, remover apenas o remessaitemmovparcela, pois o arquivo já pode ter sido enviado ao banco.
            String sql = "DELETE FROM remessaitemmovparcela WHERE remessaitem = "+remessaItemVO.getCodigo();
            try (PreparedStatement ps = con.prepareStatement(sql)) {
                ps.execute();
            }

            //se a remessa tiver em aberto, pode remover também o item para que quando fechar a remessa não gerar no arquivo esse item.
            if (remessaItemVO.getRemessa() != null && remessaItemVO.getRemessa().getDataFechamento() == null) {
                String sql2 = "DELETE FROM remessaitem WHERE codigo = " + remessaItemVO.getCodigo();
                try (PreparedStatement ps2 = con.prepareStatement(sql2)) {
                    ps2.execute();
                }
            }

            gravarLogRemoverParcelaBoleto(clienteVO, remessaItemVO, usuarioVO);

            con.commit();
        } catch (Exception ex) {
            con.rollback();
            con.setAutoCommit(true);
            throw ex;
        } finally {
            con.setAutoCommit(true);
        }
    }

    private void gravarLogRemoverParcelaBoleto(ClienteVO clienteVO, RemessaItemVO remessaItemVO, UsuarioVO usuarioVO) {
        try {
            LogVO log = new LogVO();
            log.setNomeEntidade("CLIENTE-BOLETO");
            log.setNomeEntidadeDescricao("CLIENTE-BOLETO");
            log.setChavePrimaria(clienteVO.getCodigo().toString());
            log.setNomeCampo("CLIENTE-BOLETO");
            log.setValorCampoAnterior("");

            JSONObject json = new JSONObject();
            json.put("remessa", remessaItemVO.getRemessa().getCodigo());
            json.put("remessaItem", remessaItemVO.getCodigo());

            JSONArray array = new JSONArray();
            for (RemessaItemMovParcelaVO itemVO : remessaItemVO.getMovParcelas()) {
                JSONObject parc = new JSONObject();
                parc.put("codigo", itemVO.getMovParcelaVO().getCodigo());
                parc.put("descricao", itemVO.getMovParcelaVO().getDescricao());
                array.put(parc);
            }
            json.put("parcelas", array);

            log.setValorCampoAlterado(json.toString());
            log.setDataAlteracao(Calendario.hoje());
            log.setUsuarioVO(usuarioVO);
            log.setResponsavelAlteracao(usuarioVO.getNome());
            log.setUserOAMD(usuarioVO.getUserOamd());
            log.setOperacao("EXCLUSÃO");
            log.setPessoa(clienteVO.getPessoa().getCodigo());

            Log logDAO = new Log(con);
            logDAO.incluirSemCommit(log);
            logDAO = null;
        } catch (Exception ex) {
            ex.printStackTrace();
            Uteis.logar(null, "Erro ao gravarLogRemoverParcelaBoleto " + ex.getMessage());
        }
    }
}

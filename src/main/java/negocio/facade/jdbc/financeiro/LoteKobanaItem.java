package negocio.facade.jdbc.financeiro;

import negocio.comuns.financeiro.LoteKobanaItemVO;
import negocio.comuns.financeiro.enumerador.RegistrationStatusKobanaEnum;
import negocio.comuns.financeiro.enumerador.StatusPagamentoKobanaEnum;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisValidacao;
import negocio.facade.jdbc.arquitetura.SuperEntidade;
import negocio.interfaces.financeiro.LoteKobanaItemInterfaceFacade;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.Types;
import java.util.ArrayList;
import java.util.List;

public class LoteKobanaItem extends SuperEntidade implements LoteKobanaItemInterfaceFacade {

    public LoteKobanaItem() throws Exception {
        super();
    }

    public LoteKobanaItem(Connection con) throws Exception {
        super(con);
    }

    public LoteKobanaItemVO incluir(LoteKobanaItemVO obj) throws Exception {
        try {
            String sql = "INSERT INTO LoteKobanaItem(valor, lotekobana, movconta, uid, status, registration_status, financial_account_uid," +
                    " created_at, updated_at, codigobarras, qrcode, contaBancariaFornecedor) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING codigo";
            try (PreparedStatement sqlInserir = con.prepareStatement(sql)) {
                sqlInserir.setDouble(1, obj.getValor());
                sqlInserir.setInt(2, obj.getLoteKobanaVO().getCodigo());
                sqlInserir.setInt(3, obj.getMovcontaVO().getCodigo());
                sqlInserir.setString(4, obj.getUid());
                if (obj.getStatus() == null) {
                    sqlInserir.setNull(5, Types.NULL);
                } else {
                    sqlInserir.setInt(5, obj.getStatus().getCodigo());
                }
                if (obj.getRegistration_status() == null) {
                    sqlInserir.setNull(6, Types.NULL);
                } else {
                    sqlInserir.setInt(6, obj.getRegistration_status().getCodigo());
                }
                sqlInserir.setString(7, obj.getFinancial_account_uid());
                sqlInserir.setTimestamp(8, Uteis.getDataJDBCTimestamp(obj.getCreated_at()));
                sqlInserir.setTimestamp(9, Uteis.getDataJDBCTimestamp(obj.getUpdated_at()));
                sqlInserir.setString(10, obj.getCodigobarras().replaceAll(" ", "").replaceAll("  ", "").replaceAll("-", ""));
                sqlInserir.setString(11, obj.getQrcode());
                if (obj.getContaBancariaFornecedorVO() == null || UteisValidacao.emptyNumber(obj.getContaBancariaFornecedorVO().getCodigo())) {
                    sqlInserir.setNull(12, Types.NULL);
                } else {
                    sqlInserir.setInt(12, obj.getContaBancariaFornecedorVO().getCodigo());
                }

                ResultSet rsNovo = sqlInserir.executeQuery();
                rsNovo.next();
                obj.setCodigo(rsNovo.getInt(1));
                return obj;
            }
        } catch (Exception e) {
            throw e;
        }
    }

    public void alterar(final LoteKobanaItemVO obj) throws Exception {
        String sql = "UPDATE loteKobanaItem "
                + "set status = ?, registration_status = ?, "
                + "updated_at = ?, rejected_error = ?, rejected_at = ? WHERE codigo = ? ";

        try (PreparedStatement sqlAlterar = con.prepareStatement(sql)) {
            if (obj.getStatus() == null) {
                sqlAlterar.setNull(1, Types.NULL);
            } else {
                sqlAlterar.setInt(1, obj.getStatus().getCodigo());
            }
            if (obj.getRegistration_status() == null) {
                sqlAlterar.setNull(2, Types.NULL);
            } else {
                sqlAlterar.setInt(2, obj.getRegistration_status().getCodigo());
            }
            sqlAlterar.setTimestamp(3, Uteis.getDataJDBCTimestamp(obj.getUpdated_at()));
            if (UteisValidacao.emptyString(obj.getRejected_error())) {
                sqlAlterar.setNull(4, Types.NULL);
            } else {
                sqlAlterar.setString(4, obj.getRejected_error());
            }
            if (obj.getRejected_at() == null) {
                sqlAlterar.setNull(5, Types.NULL);
            } else {
                sqlAlterar.setTimestamp(5, Uteis.getDataJDBCTimestamp(obj.getRejected_at()));
            }
            sqlAlterar.setInt(6, obj.getCodigo());
            sqlAlterar.execute();
        }
    }

    public void atualizarByCodigoBarrasAndCodLote(final LoteKobanaItemVO obj) throws Exception {
        String sql = "UPDATE loteKobanaItem "
                + "set uid = ?, status = ?, registration_status = ?, "
                + "created_at = ?, updated_at = ? WHERE codigobarras = ? AND lotekobana = ? ";

        try (PreparedStatement sqlAlterar = con.prepareStatement(sql)) {
            sqlAlterar.setString(1, obj.getUid());
            if (obj.getStatus() == null) {
                sqlAlterar.setNull(2, Types.NULL);
            } else {
                sqlAlterar.setInt(2, obj.getStatus().getCodigo());
            }
            if (obj.getRegistration_status() == null) {
                sqlAlterar.setNull(3, Types.NULL);
            } else {
                sqlAlterar.setInt(3, obj.getRegistration_status().getCodigo());
            }
            sqlAlterar.setTimestamp(4, Uteis.getDataJDBCTimestamp(obj.getCreated_at()));
            sqlAlterar.setTimestamp(5, Uteis.getDataJDBCTimestamp(obj.getUpdated_at()));
            sqlAlterar.setString(6, obj.getCodigobarras().replaceAll(" ", "").replaceAll("  ", "").replaceAll("-", ""));
            sqlAlterar.setInt(7, obj.getLoteKobanaVO().getCodigo());
            sqlAlterar.execute();
        }
    }

    public void atualizarByQrCodeAndCodLote(final LoteKobanaItemVO obj) throws Exception {
        String sql = "UPDATE loteKobanaItem "
                + "set uid = ?, status = ?, registration_status = ?, "
                + "created_at = ?, updated_at = ? WHERE qrcode = ? AND lotekobana = ? ";

        try (PreparedStatement sqlAlterar = con.prepareStatement(sql)) {
            sqlAlterar.setString(1, obj.getUid());
            if (obj.getStatus() == null) {
                sqlAlterar.setNull(2, Types.NULL);
            } else {
                sqlAlterar.setInt(2, obj.getStatus().getCodigo());
            }
            if (obj.getRegistration_status() == null) {
                sqlAlterar.setNull(3, Types.NULL);
            } else {
                sqlAlterar.setInt(3, obj.getRegistration_status().getCodigo());
            }
            sqlAlterar.setTimestamp(4, Uteis.getDataJDBCTimestamp(obj.getCreated_at()));
            sqlAlterar.setTimestamp(5, Uteis.getDataJDBCTimestamp(obj.getUpdated_at()));
            sqlAlterar.setString(6, obj.getQrcode());
            sqlAlterar.setInt(7, obj.getLoteKobanaVO().getCodigo());
            sqlAlterar.execute();
        }
    }

    public void atualizarByContaBancariaFornecedorAndCodLote(final LoteKobanaItemVO obj) throws Exception {
        String sql = "UPDATE loteKobanaItem "
                + "set uid = ?, status = ?, registration_status = ?, "
                + "created_at = ?, updated_at = ? WHERE contaBancariaFornecedor = ? AND lotekobana = ?";

        try (PreparedStatement sqlAlterar = con.prepareStatement(sql)) {
            sqlAlterar.setString(1, obj.getUid());
            if (obj.getStatus() == null) {
                sqlAlterar.setNull(2, Types.NULL);
            } else {
                sqlAlterar.setInt(2, obj.getStatus().getCodigo());
            }
            if (obj.getRegistration_status() == null) {
                sqlAlterar.setNull(3, Types.NULL);
            } else {
                sqlAlterar.setInt(3, obj.getRegistration_status().getCodigo());
            }
            sqlAlterar.setTimestamp(4, Uteis.getDataJDBCTimestamp(obj.getCreated_at()));
            sqlAlterar.setTimestamp(5, Uteis.getDataJDBCTimestamp(obj.getUpdated_at()));
            sqlAlterar.setInt(6, obj.getContaBancariaFornecedorVO().getCodigo());
            sqlAlterar.setInt(7, obj.getLoteKobanaVO().getCodigo());
            sqlAlterar.execute();
        }
    }

    public List<LoteKobanaItemVO> consultarByLote(int codLote, int nivelMntarDados) throws Exception {
        StringBuilder sql = new StringBuilder();
        sql.append("SELECT *\n");
        sql.append("FROM loteKobanaItem \n");
        sql.append(" where loteKobana = ").append(codLote).append("\n");

        List<LoteKobanaItemVO> listaItems = new ArrayList<>();
        try (PreparedStatement stm = con.prepareStatement(sql.toString())) {
            try (ResultSet rs = stm.executeQuery()) {
                while (rs.next()) {
                    listaItems.add(montarDados(rs, nivelMntarDados));
                }
            }
        }
        return listaItems;
    }

    public LoteKobanaItemVO consultarByUID(String uid, int nivelMontarDados) throws Exception {
        StringBuilder sql = new StringBuilder();
        sql.append("SELECT *\n");
        sql.append(" FROM loteKobanaItem \n");
        sql.append(" where uid = '").append(uid).append("'\n");

        try (PreparedStatement stm = con.prepareStatement(sql.toString())) {
            try (ResultSet rs = stm.executeQuery()) {
                while (rs.next()) {
                    return montarDados(rs, nivelMontarDados);
                }
            }
        }
        return null;
    }

    public List<LoteKobanaItemVO> consultarByCodLote(int codLote, int nivelMontarDados) throws Exception {
        StringBuilder sql = new StringBuilder();
        sql.append("SELECT *\n");
        sql.append(" FROM loteKobanaItem \n");
        sql.append(" where lotekobana = ").append(codLote).append("\n");

        List<LoteKobanaItemVO> listaItems = new ArrayList<>();
        try (PreparedStatement stm = con.prepareStatement(sql.toString())) {
            try (ResultSet rs = stm.executeQuery()) {
                while (rs.next()) {
                    listaItems.add(montarDados(rs, nivelMontarDados));
                }
                return listaItems;
            }
        }
    }

    private LoteKobanaItemVO montarDados(ResultSet rs, int nivelMontarDados) throws Exception {

        LoteKobanaItemVO obj = new LoteKobanaItemVO();
        obj.setCodigo(rs.getInt("codigo"));
        obj.setValor(rs.getDouble("valor"));
        obj.setUid(rs.getString("uid"));
        obj.setStatus(StatusPagamentoKobanaEnum.obterPorCodigo(rs.getInt("status")));
        obj.setRegistration_status(RegistrationStatusKobanaEnum.obterPorCodigo(rs.getInt("registration_status")));
        obj.setFinancial_account_uid(rs.getString("financial_account_uid"));
        obj.setCreated_at(rs.getTimestamp("created_at"));
        obj.setUpdated_at(rs.getTimestamp("updated_at"));
        obj.setCodigobarras(rs.getString("codigobarras"));
        obj.setQrcode(rs.getString("qrcode"));
        obj.setRejected_error(rs.getString("rejected_error"));
        obj.setRejected_at(rs.getTimestamp("rejected_at"));

        ContaBancariaFornecedor contaBancariaFornecedorDAO;
        try {
            contaBancariaFornecedorDAO = new ContaBancariaFornecedor(con);
            obj.setContaBancariaFornecedorVO(contaBancariaFornecedorDAO.consultarPorCodigo(rs.getInt("contabancariafornecedor"), Uteis.NIVELMONTARDADOS_TODOS));
        } catch (Exception ex) {
        } finally {
            contaBancariaFornecedorDAO = null;
        }

        if (nivelMontarDados == Uteis.NIVELMONTARDADOS_TODOS) {
            MovConta movContaDAO;
            try {
                movContaDAO = new MovConta(con);
                obj.setMovcontaVO(movContaDAO.consultarPorCodigo(rs.getInt("movconta"), nivelMontarDados));
            } catch (Exception ex) {
                throw ex;
            } finally {
                movContaDAO = null;
            }
        } else {
            MovConta movContaDAO;
            try {
                movContaDAO = new MovConta(con);
                obj.setMovcontaVO(movContaDAO.consultarPorCodigo(rs.getInt("movconta"), Uteis.NIVELMONTARDADOS_MINIMOS));
            } catch (Exception ex) {
                throw ex;
            } finally {
                movContaDAO = null;
            }
        }


        LoteKobana loteKobanaDAO;
        try {
            loteKobanaDAO = new LoteKobana(con);
            obj.setLoteKobanaVO(loteKobanaDAO.consultarPorCodigo(rs.getInt("lotekobana"), Uteis.NIVELMONTARDADOS_DADOSBASICOS));
        } catch (Exception ex) {
            throw ex;
        } finally {
            loteKobanaDAO = null;
        }

        return obj;
    }
}

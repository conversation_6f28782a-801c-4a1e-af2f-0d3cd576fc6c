package negocio.facade.jdbc.financeiro;

import negocio.comuns.financeiro.HistoricoCartaoVO;
import negocio.comuns.utilitarias.UtilReflection;
import negocio.interfaces.financeiro.LoteInterfaceFacade;
import negocio.comuns.financeiro.LoteVO;
import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

import br.com.pactosolucoes.enumeradores.TipoOperacaoLancamento;
import negocio.comuns.arquitetura.UsuarioVO;
import negocio.comuns.basico.EmpresaVO;
import negocio.comuns.financeiro.CartaoCreditoVO;
import negocio.comuns.financeiro.ChequeTO;
import negocio.comuns.financeiro.ChequeVO;
import negocio.comuns.financeiro.HistoricoChequeVO;
import negocio.comuns.financeiro.MovContaVO;
import negocio.comuns.financeiro.StatusCheque;
import negocio.comuns.utilitarias.Calendario;
import negocio.comuns.utilitarias.ConsistirException;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisValidacao;
import negocio.facade.jdbc.arquitetura.SuperEntidade;
import negocio.facade.jdbc.arquitetura.Usuario;

/**
 *
 * <AUTHOR> Shiozawa
 */
public class Lote extends SuperEntidade implements LoteInterfaceFacade {

    public Lote() throws Exception {
        super();
    }

    public Lote(Connection conexao) throws Exception {
        super(conexao);
    }

    @Override
    public void incluir(LoteVO obj) throws Exception {
        try {
            con.setAutoCommit(false);
            incluirSemCommit(obj, false);
            con.commit();
        } catch (Exception e) {
            con.rollback();
            throw e;
        } finally {
            con.setAutoCommit(true);
        }
    }

    @Override
    public void incluirSemCommit(LoteVO obj, boolean atualizarDataCompensacao) throws Exception {
        obj.validarDados();
        obj.realizarUpperCaseDados();
        PreparedStatement sql = con.prepareStatement("INSERT INTO Lote (empresa, usuarioResponsavel, "
                + "descricao, dataLancamento, dataDeposito, valor, avulso) VALUES (?, ?, ?, ?, ?, ?, ?)");
        sql.setInt(1, obj.getEmpresa().getCodigo());
        sql.setInt(2, obj.getUsuarioResponsavel().getCodigo());
        sql.setString(3, obj.getDescricao());
        sql.setTimestamp(4, Uteis.getDataJDBCTimestamp(obj.getDataLancamento()));
        sql.setDate(5, Uteis.getDataJDBC(obj.getDataDeposito()));
        sql.setDouble(6, obj.getValor());
        sql.setBoolean(7, obj.getAvulso());
        sql.execute();
        obj.setCodigo(obterValorChavePrimariaCodigo());
        obj.setNovoObj(false);
        gravarRecebiveis(obj,atualizarDataCompensacao);
    }

    @Override
    public void alterar(LoteVO obj, boolean alterarDataLancamento) throws Exception {
        try {
            con.setAutoCommit(false);
            alterarSemCommit(obj, alterarDataLancamento);
            con.commit();
        } catch (Exception e) {
            con.rollback();
            throw e;
        } finally {
            con.setAutoCommit(true);
        }
    }

    @Override
    public void alterarSemCommit(LoteVO obj, boolean alterarDataLancamento) throws Exception {
        obj.validarDados();
        obj.realizarUpperCaseDados();
        PreparedStatement sqlAlterar = con.prepareStatement("UPDATE lote SET usuarioResponsavel=?, "
                + "descricao=?, dataLancamento=?, dataDeposito=?, valor=? WHERE codigo = ?");
        sqlAlterar.setInt(1, obj.getUsuarioResponsavel().getCodigo());
        sqlAlterar.setString(2, obj.getDescricao());
        sqlAlterar.setTimestamp(3, Uteis.getDataJDBCTimestamp(obj.getDataLancamento()));
        sqlAlterar.setDate(4, Uteis.getDataJDBC(obj.getDataDeposito()));
        sqlAlterar.setDouble(5, obj.getValor());
        sqlAlterar.setInt(6, obj.getCodigo());
        sqlAlterar.execute();
        
        if(obj.getTipoOperacao() != null && obj.getTipoOperacao().equals(TipoOperacaoLancamento.DEPOSITO)){
        	if(obj.getCheques() != null){
        		for(ChequeVO cheque : obj.getCheques()){
        			cheque.setDataOriginal(cheque.getDataCompensacao());
        			cheque.setDataCompensacao(obj.getDataDeposito());
                    Cheque chequeDAO = new Cheque(con);
                    chequeDAO.alterarMinimo(cheque);
                    chequeDAO = null;
        			}
            	}	
        	
        	if(obj.getCartoes() != null){
        		for(CartaoCreditoVO cartao : obj.getCartoes()){
        			cartao.setDataOriginal(cartao.getDataCompensacao());
        			cartao.setDataCompensacao(obj.getDataDeposito());
                    CartaoCredito cartaoCreditoDAO = new CartaoCredito(con);
                    cartaoCreditoDAO.alterarMinimo(cartao);
                    cartaoCreditoDAO = null;
        		}	
        	}
        }

        MovConta movContaDAO = new MovConta(con);
        movContaDAO.alterarSomenteDataQuitacaoPorLote(obj.getDataDeposito(), obj.getCodigo(), false);
        if(alterarDataLancamento){
            movContaDAO.alterarSomenteDataQuitacaoPorLote(obj.getDataLancamento(), obj.getCodigo(), true);
        }
        movContaDAO = null;
    }



    @Override
    public void alterarSomenteValor(LoteVO obj) throws Exception {
        PreparedStatement sqlAlterar = con.prepareStatement("UPDATE lote SET valor=? WHERE codigo = ?");
        sqlAlterar.setDouble(1, obj.getValor());
        sqlAlterar.setInt(2, obj.getCodigo());
        sqlAlterar.execute();
    }

    public boolean verificarContaDevolucao(int codigolote) throws Exception{
        StringBuilder sql = new StringBuilder();
        sql.append(" select comportamento from conta c ");
        sql.append(" inner join tipoconta tc on c.tipoconta = tc.codigo ");
        sql.append(" inner join movconta mc on c.codigo = mc.conta ");
        sql.append(" and mc.lote = ").append(codigolote);
        sql.append(" and mc.tipooperacao = ").append(TipoOperacaoLancamento.DEVOLUCAO_CHEQUE.getCodigo());
        ResultSet rs = criarConsulta(sql.toString(), con);
        return rs.next();

    }

    @Override
    public LoteVO consultarPorChavePrimaria(int codigo, int nivelMontarDados) throws Exception {
        consultar(getIdEntidade());
        StringBuilder str = new StringBuilder();
        str.append("SELECT * FROM lote WHERE codigo = ").append(codigo);
        PreparedStatement stm = con.prepareStatement(str.toString());
        ResultSet tabelaResultado = stm.executeQuery();
        if (tabelaResultado.next()) {
            return montarDados(tabelaResultado, nivelMontarDados, this.con);
        } else {
            return new LoteVO();
        }
    }

    public LoteVO consultarPorPagaMovConta(MovContaVO movcontaVO, int nivelMontarDados) throws Exception {
        /*
           O campo "pagamovconta" da tabela Lote foi substituido pelo campo "lotePagouConta" da tabela movConta
         */
        consultar(getIdEntidade());
        StringBuilder str = new StringBuilder();
        str.append("SELECT * FROM lote WHERE pagamovconta = ").append(movcontaVO.getCodigo());
        PreparedStatement stm = con.prepareStatement(str.toString());
        ResultSet tabelaResultado = stm.executeQuery();
        if (tabelaResultado.next()) {
            return montarDados(tabelaResultado, nivelMontarDados, this.con);
        } else if (movcontaVO != null && !UteisValidacao.emptyNumber(movcontaVO.getCodigo())) {
            MovConta movContaDAO = new MovConta(con);
            MovContaVO objMovConta = movContaDAO.consultarPorCodigo(movcontaVO.getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS);
            movcontaVO.setChaveArquivoConta(objMovConta.getChaveArquivoConta());
            movcontaVO.setChaveArquivoComprovante(objMovConta.getChaveArquivoComprovante());
            movcontaVO.setExtensaoArquivoContaMovConta(objMovConta.getExtensaoArquivoContaMovConta());
            movcontaVO.setExtensaoArquivoComprovanteMovConta(objMovConta.getExtensaoArquivoComprovanteMovConta());
            movContaDAO = null;
            if (UtilReflection.objetoMaiorQueZero(objMovConta, "getLotePagouConta().getCodigo()")) {
                return consultarPorChavePrimaria(objMovConta.getLotePagouConta().getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS);
            } else {
                return new LoteVO();
            }
        } else {
            return new LoteVO();
        }
    }
    @Override
    public List<LoteVO> consultarPorCodigoDescricaoPeriodoDL(int empresa, int codigo, String descricao, Date inicioD, 
    		Date fimD, Date inicioL, Date fimL, int codigoConta, boolean disponiveis, boolean avulsos,  int nivelMontarDados) throws Exception {
        consultar(getIdEntidade());
        StringBuilder str = new StringBuilder();
        str.append("SELECT DISTINCT c.descricao as contaLote, l.* \n ");
        str.append("FROM lote l \n");
        str.append("LEFT JOIN movconta mc ON mc.lote = l.codigo \n");
        str.append("LEFT JOIN conta c ON c.codigo = mc.conta \n");
        str.append("LEFT JOIN movconta mcLote ON mcLote.lotePagouConta = l.codigo \n");
        str.append("WHERE 1 = 1 ");
        if (codigoConta != 0) {
            str.append(" AND  mc.conta = ").append(codigoConta);
        }

        if (empresa != 0) {
            str.append(" AND l.empresa = ").append(empresa);
        }
        
        if (codigo != 0) {
            str.append(" AND l.codigo = ").append(codigo);
        }
        if (!descricao.trim().isEmpty()) {
            str.append(" AND l.descricao ILIKE '%").append(descricao).append("%' ");
        }
        if (inicioD != null) {
            str.append(" AND l.dataDeposito >= '").append(Uteis.getDataJDBC(inicioD)).append(" 00:00:00'").append(" AND l.dataDeposito <= '").append(Uteis.getDataJDBC(fimD)).append(" 23:59:59' ");
        }
         if (inicioL != null) {
            str.append(" AND l.dataLancamento >= '").append(Uteis.getDataJDBC(inicioL)).append(" 00:00:00'").append(" AND l.dataLancamento <= '").append(Uteis.getDataJDBC(fimL)).append(" 23:59:59' ");
        }
        if(disponiveis){
            str.append(" AND (pagamovconta is null and mcLote.lotePagouConta is null) " );
        }
        if(avulsos){
        	str.append(" AND avulso is true" );
        } 
        str.append(" ORDER BY l.codigo");
        PreparedStatement stm = con.prepareStatement(str.toString());
        ResultSet rs = stm.executeQuery();
        List<LoteVO> lista = new ArrayList<LoteVO>();
        while (rs.next()) {
            LoteVO loteVO = montarDados(rs, nivelMontarDados, con);
            loteVO.setContaLote(rs.getString("contaLote"));
            lista.add(loteVO);
        }
        return lista;
    }

    @Override
    public List<LoteVO> consultarPor(int empresa, int codigo, String descricao, Date inicioD,
                                                             Date fimD, Date inicioL, Date fimL, int codigoConta, boolean disponiveis, boolean avulsos,  int nivelMontarDados) throws Exception {
        consultar(getIdEntidade());
        StringBuilder str = new StringBuilder();
        str.append("SELECT DISTINCT l.* \n ");
        str.append("FROM lote l \n");
        str.append(" LEFT JOIN movconta mc ON mc.lote = l.codigo ");
        if (codigoConta != 0) {
            str.append(" AND mc.conta = ").append(codigoConta);
        }
        str.append(" WHERE 1 = 1 ");
        if (empresa != 0) {
            str.append(" AND l.empresa = ").append(empresa);
        }

        if (codigo != 0) {
            str.append(" AND l.codigo = ").append(codigo);
        }
        if (!descricao.trim().isEmpty()) {
            str.append(" AND l.descricao ILIKE '%").append(descricao).append("%' ");
        }
        if (inicioD != null) {
            str.append(" AND l.dataDeposito >= '").append(Uteis.getDataJDBC(inicioD)).append(" 00:00:00'").append(" AND l.dataDeposito <= '").append(Uteis.getDataJDBC(fimD)).append(" 23:59:59' ");
        }
        if (inicioL != null) {
            str.append(" AND l.dataLancamento >= '").append(Uteis.getDataJDBC(inicioL)).append(" 00:00:00'").append(" AND l.dataLancamento <= '").append(Uteis.getDataJDBC(fimL)).append(" 23:59:59' ");
        }
        if(disponiveis){
            str.append(" AND pagamovconta is null" );
        }
        if(avulsos){
            str.append(" AND avulso is true" );
        }
        str.append(" ORDER BY l.codigo");
        PreparedStatement stm = con.prepareStatement(str.toString());
        ResultSet tabelaResultado = stm.executeQuery();
        return montarDadosConsulta(tabelaResultado, nivelMontarDados, this.con);
    }

    public List<LoteVO> montarDadosConsulta(ResultSet tabelaResultado, int nivelMontarDados, Connection con) throws Exception {
        List<LoteVO> vetResultado = new ArrayList<LoteVO>();
        while (tabelaResultado.next()) {
            vetResultado.add(montarDados(tabelaResultado, nivelMontarDados, con));
        }
        return vetResultado;
    }
    
    private void preencherConta(LoteVO lote) throws Exception{
    	ResultSet resultSet = criarConsulta(" SELECT c.descricao, mc.tipooperacao, c.codigo as conta FROM conta c " +
    									    " INNER JOIN movconta mc ON mc.conta = c.codigo AND mc.lote = "+lote.getCodigo()+
                                                                            " AND mc.tipooperacao <> "+TipoOperacaoLancamento.TRANSFERENCIA.getCodigo()+
                                                                            " ORDER BY mc.codigo LIMIT 1", con);
    	if(resultSet.next()){
    		lote.setConta(resultSet.getString("descricao"));
    		lote.setCodigoContaContido(resultSet.getInt("conta"));
    		lote.setTipoOperacao(TipoOperacaoLancamento.getTipoOperacaoLancamento(resultSet.getInt("tipooperacao")));
    	}
    }

    public LoteVO montarDados(ResultSet dadosSQL, int nivelMontarDados, Connection con) throws Exception {
        LoteVO obj = montarDadosBasico(dadosSQL);
        preencherConta(obj);
        if (nivelMontarDados == Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA) {
            return obj;
        }
        
        montarDadosUsuario(obj, con);
        if (nivelMontarDados == Uteis.NIVELMONTARDADOS_DADOSBASICOS) {
            return obj;
        }
        montarListaCartoes(obj, con);
        montarListaCheques(obj, con);
        return obj;
    }

    private LoteVO montarDadosBasico(ResultSet dadosSQL) throws Exception {
        LoteVO obj = new LoteVO();
        obj.setNovoObj(false);
        obj.setEmpresa(new EmpresaVO());
        obj.getEmpresa().setCodigo(dadosSQL.getInt("empresa"));
        obj.setCodigo(dadosSQL.getInt("codigo"));
        obj.setUsuarioResponsavel(new UsuarioVO());
        obj.getUsuarioResponsavel().setCodigo(dadosSQL.getInt("usuarioresponsavel"));
        obj.setDescricao(dadosSQL.getString("descricao"));
        obj.setDataLancamento(dadosSQL.getTimestamp("dataLancamento"));
        obj.setDataDeposito(dadosSQL.getDate("dataDeposito"));
        obj.setValor(dadosSQL.getDouble("valor"));
        obj.setPagaMovConta(dadosSQL.getInt("pagamovconta"));
        obj.setAvulso(dadosSQL.getBoolean("avulso"));
        return obj;
    }

    public void montarDadosUsuario(LoteVO obj, Connection con) throws Exception {
        Usuario user = new Usuario(con);
        obj.setUsuarioResponsavel(user.consultarPorChavePrimaria(
                obj.getUsuarioResponsavel().getCodigo().intValue(),
                Uteis.NIVELMONTARDADOS_DADOSBASICOS));
        user = null;
    }

    public void montarListaCartoes(LoteVO obj, Connection con) throws Exception {
        CartaoCredito cartao = new CartaoCredito(con);
        obj.setCartoes(cartao.consultarPorLote(obj.getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS));
        cartao = null;
    }

    public void montarListaCheques(LoteVO obj, Connection con) throws Exception {
        Cheque cheque = new Cheque(con);
        obj.setCheques(cheque.consultarPorLote(obj.getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS));
        cheque = null;
    }
    
    public void gravarRecebiveis(LoteVO lote, boolean atualizarDataCompensacao) throws Exception{
    	if(lote.getCheques() != null){
    		for(ChequeVO cheque : lote.getCheques()){
    			if(atualizarDataCompensacao){
    				cheque.setDataOriginal(cheque.getDataCompensacao());
    				cheque.setDataCompensacao(lote.getDataDeposito());
                    Cheque chequeDAO = new Cheque(con);
                    chequeDAO.alterarMinimo(cheque);
                    chequeDAO = null;
    			}
    			String[]codigos = cheque.getObterTodosChequesComposicao().split(",");
        	
    			for (String codigo : codigos){
    				executarConsulta("INSERT INTO chequecartaolote(cheque, lote) VALUES ("+codigo+","+lote.getCodigo()+")", con);
    			}
        	}	
    	}
    	if(lote.getCartoes() != null){
    		for(CartaoCreditoVO cartao : lote.getCartoes()){
    			if(atualizarDataCompensacao){
    				cartao.setDataOriginal(cartao.getDataCompensacao());
    				cartao.setDataCompensacao(lote.getDataDeposito());
                    CartaoCredito cartaoCreditoDAO = new CartaoCredito(con);
                    cartaoCreditoDAO.alterarMinimo(cartao);
                    cartaoCreditoDAO = null;
    			}
    			String[]codigos = cartao.getObterTodosCartoesComposicao().split(",");
        		
    			for (String codigo : codigos){
    				executarConsulta("INSERT INTO chequecartaolote(cartao, lote) VALUES ("+codigo+","+lote.getCodigo()+")", con);
    			}
        		
        	}	
    	}
    }
    public void deleteRetiradosDoLote(Integer codigoLote) throws Exception{
        Cheque chequeDAO = new Cheque(con);
        List<ChequeTO> chequesRetirados = chequeDAO.consultarChequesRetirados(codigoLote, null);
        chequeDAO = null;
        for(ChequeTO cheque : chequesRetirados){
            if(!UteisValidacao.emptyNumber(cheque.getMovConta())){
                MovContaVO mov = new MovContaVO();
                mov.setCodigo(cheque.getMovConta());
                MovConta movContaDAO = new MovConta(con);
                movContaDAO.excluirSemCommit(mov, false, true);
                movContaDAO = null;
            }
        }
    }

    @Override
    public void excluirSemCommit(LoteVO obj, boolean excluirMovConta, boolean limparRelacinamentoCheque) throws Exception {
    	ResultSet consultaRecebiveis = criarConsulta("SELECT cheque, cartao FROM chequecartaolote where lote = "+obj.getCodigo(), con);
    	limparRelacionamentos(obj, excluirMovConta, limparRelacinamentoCheque);
        deleteRetiradosDoLote(obj.getCodigo());
        
        PreparedStatement sqlExcluir = con.prepareStatement("DELETE FROM lote WHERE codigo = ?; ");
        
        sqlExcluir.setInt(1, obj.getCodigo());
        sqlExcluir.execute();
        
        while (consultaRecebiveis.next()) {
            if(!UteisValidacao.emptyNumber(consultaRecebiveis.getInt("cheque"))){
                ResultSet consultaHistorico = criarConsulta("SELECT historicocheque.lote, historicocheque.codigo from historicocheque "
                        + " inner join lote on lote.codigo = historicocheque.lote "
                        + " where cheque = " + consultaRecebiveis.getInt("cheque") + " and lote <> " + obj.getCodigo() + " ORDER BY datainicio DESC LIMIT 1", con);
                if (consultaHistorico.next()) {
                    executarConsulta("UPDATE historicocheque SET datafim = null WHERE codigo = " + consultaHistorico.getInt("codigo"), con);
                    ResultSet resultSet = criarConsulta("SELECT lote, cheque from chequecartaolote where cheque = " + consultaRecebiveis.getInt("cheque") +
                            " AND lote = " + consultaHistorico.getInt("lote"), con);
                    if (!resultSet.next()) {
                        executarConsulta("INSERT into chequecartaolote (lote, cheque) values (" + consultaHistorico.getInt("lote") + ","
                                + consultaRecebiveis.getInt("cheque") + ")", con);
                    }
                }
            }

            if(!UteisValidacao.emptyNumber(consultaRecebiveis.getInt("cartao"))){
                ResultSet consultaHistorico = criarConsulta("SELECT historicocartao.lote, historicocartao.codigo from historicocartao "
                        + " inner join lote on lote.codigo = historicocartao.lote "
                        + " where cartao = " + consultaRecebiveis.getInt("cartao") + " and lote <> " + obj.getCodigo() + " ORDER BY datainicio DESC LIMIT 1", con);
                if (consultaHistorico.next()) {
                    executarConsulta("UPDATE historicocartao SET datafim = null WHERE codigo = " + consultaHistorico.getInt("codigo"), con);
                    ResultSet resultSet = criarConsulta("SELECT lote, cartao from chequecartaolote where cartao = " + consultaRecebiveis.getInt("cartao") +
                            " AND lote = " + consultaHistorico.getInt("lote"), con);
                    if (!resultSet.next()) {
                        executarConsulta("INSERT into chequecartaolote (lote, cartao) values (" + consultaHistorico.getInt("lote") + ","
                                + consultaRecebiveis.getInt("cartao") + ")", con);
                    }
                }
            }

        }

    }
    
    private void limparRelacionamentos(LoteVO obj, boolean excluirMovConta, boolean limparRelacionamentoCheque) throws Exception {

        executarConsulta("UPDATE cartaocredito SET datacompesancao = dataoriginal "
                + " where codigo in (SELECT cartao FROM chequecartaolote where lote = "
                + obj.getCodigo() + " ) AND dataoriginal is not null", con);
        if (limparRelacionamentoCheque) {
            executarConsulta("UPDATE cheque SET datacompesancao = dataoriginal, dataoriginal = null"
                    + " where codigo in (SELECT cheque FROM chequecartaolote where lote = "
                    + obj.getCodigo() + " ) AND dataoriginal is not null", con);
        }
        executarConsulta("delete from chequecartaolote where lote = " + obj.getCodigo(), con);
        executarConsulta("delete from historicocheque where lote = " + obj.getCodigo(), con);
        executarConsulta("delete from historicocartao where lote = " + obj.getCodigo(), con);
        if (excluirMovConta) {
            MovConta movContaDAO = new MovConta(con);
            movContaDAO.excluirPorLote(obj.getCodigo());
            movContaDAO = null;
        }
    }
    
    @Override
    public void excluir(LoteVO obj) throws Exception {
        try {
            con.setAutoCommit(false);
            excluirSemCommit(obj, true, true);
            con.commit();
        } catch (Exception e) {
            con.rollback();
            throw e;
        } finally {
            con.setAutoCommit(true);
        }
    }
   
    
    @Override
    public void alterarMinimo(LoteVO obj, ChequeVO ch, boolean remover, boolean atualizarHistorico, boolean alterarValores) throws Exception {
        try {
            con.setAutoCommit(false);
            alterarMinimoSemCommit(obj, ch, remover, atualizarHistorico, alterarValores);
            con.commit();
        } catch (Exception e) {
            con.rollback();
            throw e;
        } finally {
            con.setAutoCommit(true);
        }
    }
    
    public void alterarMinimoSemCommit(LoteVO obj, ChequeVO ch, boolean remover, boolean atualizarHistorico, boolean alterarValores) throws Exception {
        try {
            Cheque chequeDAO = new Cheque(con);
            if(remover){
                chequeDAO.removerChequeNoLote(obj, ch, atualizarHistorico);
				executarConsulta("UPDATE cheque SET datacompesancao = dataoriginal where codigo in ("+ch.getObterTodosChequesComposicao()+") and dataoriginal is not null", con);
            }else {
                chequeDAO.adicionarChequeNoLote(obj, ch);
            }
            chequeDAO = null;

            if(alterarValores){
            	alterarSomenteValor(obj);
                atualizarMovConta(obj, ch);	
            }
        } catch (Exception e) {
            throw e;
        }
    }

    
	/**
	 * Responsável por 
	 * <AUTHOR> Alcides
	 * 26/03/2013
	 */
	public void atualizarMovConta(LoteVO obj, ChequeVO ch) throws Exception {
        MovConta movContaDAO = new MovConta(con);
		MovContaVO movContaVO = movContaDAO.consultarPorLote(obj.getCodigo(), obj.getAvulso() ? Uteis.NIVELMONTARDADOS_LOTE_AVULSO : Uteis.NIVELMONTARDADOS_TODOS);
		if (!UteisValidacao.emptyNumber(movContaVO.getCodigo()) && !movContaVO.getValor().equals(obj.getValor())) {
            movContaDAO.alterarSomenteValor(movContaVO, obj.getValor());
			Double valorAnterior = movContaVO.getValor().doubleValue();
			movContaVO.setValor(obj.getValor());
            movContaDAO.salvarLogCaixaAlteracaoValor(movContaVO, valorAnterior, ch, obj);
		}
        movContaDAO = null;
	}
    
    @Override
    public void alterarMinimo(LoteVO obj, CartaoCreditoVO ca, boolean remover, boolean alterarValores) throws Exception {
        try {
            con.setAutoCommit(false);
            alterarMinimoSemCommit(obj, ca, remover, alterarValores);
            con.commit();
        } catch (Exception e) {
            con.rollback();
            throw e;
        } finally {
            con.setAutoCommit(true);
        }
    }
    
    public void alterarMinimoSemCommit(LoteVO obj, CartaoCreditoVO ca, boolean remover, boolean alterarValores) throws Exception {
        try {
            CartaoCredito cartaoCreditoDAO = new CartaoCredito(con);
            if(remover){
                cartaoCreditoDAO.removerCartaoNoLote(obj, ca);
            }else {
                cartaoCreditoDAO.adicionarCartaoNoLote(obj, ca);
            }
            cartaoCreditoDAO = null;

            alterarSomenteValor(obj);
            atualizarMovConta(obj, null);
        } catch (Exception e) {
            throw e;
        }
    }
    
    public Integer consultarContaLote(int lote) throws Exception{
    	String sql = "SELECT mc.conta FROM lote l INNER JOIN movconta mc ON mc.lote = l.codigo WHERE l.codigo = "+lote;
    	ResultSet consulta = criarConsulta(sql, con);
    	if(consulta.next()){
    		return consulta.getInt("conta");
    	}else{
    		return 0;
    	}
    }
    
    public void retirarChequeDoLote(ChequeVO cheque, int loteNaoRetirar) throws Exception{
    	String sql = "select l.valor, l.codigo from lote l "+
    				"inner join chequecartaolote ccl on ccl.lote = l.codigo and l.codigo <> "+loteNaoRetirar+" and ccl.cheque = "+cheque.getCodigo();
    	ResultSet resultSet = criarConsulta(sql, con);
    	while(resultSet.next()){
    		executarConsulta("delete from chequecartaolote where lote = "+resultSet.getInt("codigo") +" and cheque = "+cheque.getCodigo(), con);
    		Double novoValor = resultSet.getDouble("valor") - cheque.getValor();
    		executarConsulta("update lote set valor = "+novoValor+" where codigo = "+resultSet.getInt("codigo") , con);
    	}
    }
    
    
    public List<LoteVO> consultarRelacionamentosDoLote(LoteVO lote) throws Exception{
    	StringBuilder sql = new StringBuilder();
    	sql.append(" select distinct l.* from lote l \n");
    	sql.append(" inner join chequecartaolote ccl on ccl.lote = l.codigo \n");
    	sql.append(" WHERE (ccl.cheque in (select cheque from chequecartaolote where lote = "+lote.getCodigo()+") \n");
    	sql.append(" OR ccl.cartao in (select cartao from chequecartaolote where lote = "+lote.getCodigo()+")) \n");
    	sql.append(" AND l.codigo > "+lote.getCodigo()+" order by codigo ");
    	ResultSet consulta = criarConsulta(sql.toString(), con);
    	return montarDadosConsulta(consulta, Uteis.NIVELMONTARDADOS_DADOSBASICOS, con);
    	
    }
     
    public void retirarRecebivelLotes(ChequeVO cheque, CartaoCreditoVO cartao, boolean alterarValores) throws Exception{
    	try{
    		con.setAutoCommit(false);
    		String sql = "SELECT l.* FROM chequecartaolote ccl inner join lote l on l.codigo = ccl.lote WHERE ";
    		if(cheque != null){
        		sql += " ccl.cheque = "+cheque.getCodigo();
    		}else if(cartao != null){
        		sql += " ccl.cartao = "+cartao.getCodigo();
            }
    		ResultSet query = con.prepareStatement(sql).executeQuery();
    		List<LoteVO> lotes = montarDadosConsulta(query, Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA, con);
    		for(LoteVO lote : lotes){
    		    Integer movContaPagaPorLote = lote.getPagaMovConta();
    		    if ((movContaPagaPorLote == null) || (movContaPagaPorLote == 0)){
                    MovConta movContaDAO = new MovConta(con);
                    movContaPagaPorLote = movContaDAO.consultarMovContaLotePagou(lote.getCodigo());
                    movContaDAO = null;
                }
                if ((movContaPagaPorLote != null) && (movContaPagaPorLote > 0)){
    				throw new ConsistirException("O lote "+lote.getDescricao()+" foi usado para pagar uma conta e não pode ser alterado.");
    			}
    			if(cheque != null){
    				lote.setValor((lote.getValor() - cheque.getValor()) < 0 ? 0.0 : lote.getValor() - cheque.getValor());
    				alterarMinimo(lote, cheque, true, false, alterarValores);
		    		
					executarConsulta("UPDATE cheque SET datacompesancao = dataoriginal where codigo = "+cheque.getCodigo()+" and dataoriginal is not null", con);
        		}else if(cartao != null){
        			lote.setValor((lote.getValor() - cartao.getValor()) < 0 ? 0.0 : lote.getValor() - cartao.getValor());
    				alterarMinimo(lote, cartao, true, alterarValores);
    				executarConsulta("UPDATE cartaocredito SET datacompesancao = dataoriginal where codigo = "+cartao.getCodigo() +" and dataoriginal is not null", con);
                }
    			atualizarMovConta(lote, cheque);
    		}
    		if(cheque != null){
				HistoricoChequeVO histCheque = new HistoricoChequeVO();
				histCheque.setCheque(cheque);
				histCheque.setStatus(StatusCheque.RETIRAR_LOTES);
				histCheque.setDataInicio(Calendario.hoje());
				HistoricoCheque historico = new HistoricoCheque(con);
				historico.incluir(histCheque);
    		}
            if(cartao != null){
                HistoricoCartaoVO histCartao = new HistoricoCartaoVO();
                histCartao.setCartao(cartao);
                histCartao.setDataInicio(Calendario.hoje());
                HistoricoCartao historico = new HistoricoCartao(con);
                historico.incluir(histCartao);
            }
    		con.commit();
    	}catch (Exception e) {
    		con.rollback();
    		throw e;
    	}finally{
			con.setAutoCommit(true);
		}
    }
    
    public LoteVO consultarPorCartao(int codigo, int nivelMontarDados) throws Exception {
        consultar(getIdEntidade());
        StringBuilder str = new StringBuilder();
        str.append("SELECT * FROM lote l inner join chequecartaolote ccl on ccl.lote = l.codigo WHERE ccl.cartao = ")
        	.append(codigo)
            .append(" order by l.codigo desc limit 1");
        PreparedStatement stm = con.prepareStatement(str.toString());
        ResultSet tabelaResultado = stm.executeQuery();
        if (tabelaResultado.next()) {
            return montarDados(tabelaResultado, nivelMontarDados, this.con);
        } else {
            return new LoteVO();
        }
    }
    
    public List<LoteVO> consultarPorCartaoLista(int codigo, int nivelMontarDados) throws Exception {
        consultar(getIdEntidade());
        List<LoteVO> lotes = new ArrayList();
        StringBuilder str = new StringBuilder();
        str.append("SELECT * FROM lote l inner join chequecartaolote ccl on ccl.lote = l.codigo WHERE ccl.cartao = ")
        	.append(codigo)
            .append(" order by l.codigo");
        PreparedStatement stm = con.prepareStatement(str.toString());
        ResultSet tabelaResultado = stm.executeQuery();
        while (tabelaResultado.next()) {
            lotes.add(montarDados(tabelaResultado, nivelMontarDados, this.con));
        }
        return lotes;
    }
    
    public LoteVO consultarPorCheque(int codigo, int nivelMontarDados) throws Exception {
        consultar(getIdEntidade());
        StringBuilder str = new StringBuilder();
        str.append("SELECT * FROM lote l inner join chequecartaolote ccl on ccl.lote = l.codigo WHERE ccl.cheque = ")
        	.append(codigo)
            .append(" order by l.datalancamento desc limit 1");
        PreparedStatement stm = con.prepareStatement(str.toString());
        ResultSet tabelaResultado = stm.executeQuery();
        if (tabelaResultado.next()) {
            return montarDados(tabelaResultado, nivelMontarDados, this.con);
        } else {
            return new LoteVO();
        }
    }
    
    public List<LoteVO> consultarPorChequeLista(int codigo, int nivelMontarDados) throws Exception {
        consultar(getIdEntidade());
        List<LoteVO> lotes = new ArrayList();
        StringBuilder str = new StringBuilder();
        str.append("SELECT * FROM lote l inner join chequecartaolote ccl on ccl.lote = l.codigo WHERE ccl.cheque = ")
        	.append(codigo)
            .append(" order by l.codigo");
        PreparedStatement stm = con.prepareStatement(str.toString());
        ResultSet tabelaResultado = stm.executeQuery();
        while (tabelaResultado.next()) {
            lotes.add(montarDados(tabelaResultado, nivelMontarDados, this.con));
        }
        return lotes;
    }
    
    public int consultarLoteAvulso(int cheque) throws Exception{
    	ResultSet consulta = criarConsulta("SELECT ccl.lote FROM chequecartaolote ccl INNER JOIN lote l ON l.codigo = ccl.lote WHERE ccl.cheque = "+cheque+" and l.avulso", con);
    	if(consulta.next()){
    		return consulta.getInt("lote");
    	}
    	return 0;
    }
    
    @Override
    public void atualizarValorLote(Integer lote, boolean cheque) throws Exception{
        	executarConsulta(" UPDATE lote " +
        			" SET valor = (SELECT sum("+ (cheque ? "ch":"cc")+".valor) FROM " + (cheque ? "cheque ch ":"cartaocredito cc ")+
        			" INNER JOIN chequecartaolote cch ON "+ (cheque ? "cch.cheque = ch.codigo "
        					                                         :"cch.cartao = cc.codigo ")+" and cch.lote = "+lote+" )" +
        			" WHERE codigo = "+lote, con);
    }

    public void atualizarValoresLotePorMovPagamentoCheque(int movPagamento) throws Exception{
        String sql = " SELECT l.codigo FROM lote l INNER JOIN chequecartaolote ccl ON ccl.lote = l.codigo "+
                     " INNER JOIN cheque ch ON ch.codigo = ccl.cheque WHERE ch.movpagamento = "+movPagamento;
        ResultSet dados = criarConsulta(sql, con);
        while(dados.next()){
            LoteVO lote = new LoteVO(dados.getInt("codigo"));
            String cheques = " SELECT distinct ch.* FROM cheque ch "+
                             " INNER JOIN chequecartaolote ccl ON ccl.cheque = ch.codigo "+
                             " WHERE ccl.lote = "+lote.getCodigo()+" and ch.movpagamento = "+movPagamento;
            ResultSet dadosCheques = criarConsulta(cheques, con);
            List<ChequeVO> listaCheques = Cheque.montarDadosConsulta(dadosCheques, Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA, con);
            for(ChequeVO ch : listaCheques){
                 alterarMinimo(lote, ch, true, true, false);
            }
            atualizarValorLote(lote.getCodigo(), true);
         }
    }

    public void atualizarValoresLotePorMovPagamentoCartao(int movPagamento) throws Exception{
        String sql = " SELECT l.codigo FROM lote l INNER JOIN chequecartaolote ccl ON ccl.lote = l.codigo "+
                     " INNER JOIN cartaocredito cc ON cc.codigo = ccl.cartao WHERE cc.movpagamento = "+movPagamento;
        ResultSet dados = criarConsulta(sql, con);
        while(dados.next()){
            LoteVO lote = new LoteVO(dados.getInt("codigo"));
            String cartoes = " SELECT distinct cc.* FROM cartaocredito cc "+
                             " INNER JOIN chequecartaolote ccl ON ccl.cartao = cc.codigo "+
                             " WHERE ccl.lote = "+lote.getCodigo()+" and cc.movpagamento = "+movPagamento;
            ResultSet dadosCartoes = criarConsulta(cartoes, con);
            List<CartaoCreditoVO> listaCartoes = CartaoCredito.montarDadosConsulta(dadosCartoes, Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA, con);
            for(CartaoCreditoVO cc : listaCartoes){
                 alterarMinimo(lote, cc,true, true);
            }
            atualizarValorLote(lote.getCodigo(), false);
         }
    }
}

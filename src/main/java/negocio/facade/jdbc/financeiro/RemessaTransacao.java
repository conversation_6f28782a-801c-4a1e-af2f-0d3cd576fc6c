/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package negocio.facade.jdbc.financeiro;

import negocio.interfaces.financeiro.RemessaTransacaoInterfaceFacade;
import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.List;
import negocio.comuns.financeiro.RemessaTransacaoVO;
import negocio.comuns.financeiro.RemessaVO;
import negocio.comuns.financeiro.TransacaoVO;
import negocio.comuns.utilitarias.ConsistirException;
import negocio.facade.jdbc.arquitetura.SuperEntidade;
import negocio.interfaces.financeiro.TransacaoInterfaceFacade;

/**
 *
 * <AUTHOR>
 */
public class RemessaTransacao extends SuperEntidade implements RemessaTransacaoInterfaceFacade {

    public RemessaTransacao() throws Exception {
    }

    public RemessaTransacao(Connection con) throws Exception {
        super(con);
    }

    public void incluir(RemessaTransacaoVO obj) throws Exception {
        try {
            con.setAutoCommit(false);
            incluirSemCommit(obj);
            con.commit();
        } catch (Exception e) {
            con.rollback();
            throw e;
        } finally {
            con.setAutoCommit(true);
        }
    }

    public void incluirSemCommit(RemessaTransacaoVO obj) throws Exception {
        RemessaTransacaoVO.validarDados(obj);
        String sql = "INSERT INTO RemessaTransacao(remessa, transacao) "
                + "VALUES (?,?)";
        try (PreparedStatement sqlInserir = con.prepareStatement(sql)) {
            sqlInserir.setInt(1, obj.getRemessa().getCodigo());
            sqlInserir.setInt(2, obj.getTransacao().getCodigo());
            sqlInserir.execute();
        }
        obj.setCodigo(obterValorChavePrimariaCodigo());
        obj.setNovoObj(false);
    }

    public void alterar(RemessaTransacaoVO obj) throws Exception {
        RemessaTransacaoVO.validarDados(obj);
        String sql = "UPDATE RemessaTransacao "
                + "set remessa=?, transacao=?"
                + "WHERE ((codigo = ?))";

        PreparedStatement sqlAlterar = con.prepareStatement(sql);
        sqlAlterar.setInt(1, obj.getRemessa().getCodigo());
        sqlAlterar.setInt(2, obj.getTransacao().getCodigo());

        sqlAlterar.setInt(3, obj.getCodigo().intValue());

        sqlAlterar.execute();
    }

    /**
     * Operação responsável por excluir no BD um objeto da classe <code>ContratoComposicaoVO</code>.
     * Sempre localiza o registro a ser excluído através da chave primária da entidade.
     * Primeiramente verifica a conexão com o banco de dados e a permissão do usuário
     * para realizar esta operacão na entidade.
     * Isto, através da operação <code>excluir</code> da superclasse.
     * @param obj    Objeto da classe <code>ContratoComposicaoVO</code> que será removido no banco de dados.
     * @exception Execption Caso haja problemas de conexão ou restrição de acesso.
     */
    public void excluir(RemessaTransacaoVO obj) throws Exception {
        String sql = "DELETE FROM RemessaTransacao WHERE ((codigo = ?))";
        PreparedStatement sqlExcluir = con.prepareStatement(sql);
        sqlExcluir.setInt(1, obj.getCodigo().intValue());
        sqlExcluir.execute();
    }

    private static List montarDadosConsulta(ResultSet tabelaResultado, Connection con) throws Exception {

        List vetResultado = new ArrayList();
        while (tabelaResultado.next()) {
            RemessaTransacaoVO obj = new RemessaTransacaoVO();
            obj = montarDados(tabelaResultado, con);
            vetResultado.add(obj);
        }
        return vetResultado;
    }

    private static RemessaTransacaoVO montarDados(ResultSet ds, Connection con) throws Exception {

        RemessaTransacaoVO obj = new RemessaTransacaoVO();

        TransacaoInterfaceFacade transacaoDAO = new Transacao(con);
        Remessa remessaDAO = new Remessa(con);
        try {
            TransacaoVO transacao = transacaoDAO.consultarPorChavePrimaria(ds.getInt("transacao"));
            RemessaVO remessa = remessaDAO.consultarPorChavePrimaria(ds.getInt("remessa"));
            
            obj.setCodigo(ds.getInt("codigo"));
            obj.setTransacao(transacao);
            obj.setRemessa(remessa);

        } finally {
            transacaoDAO = null;
            remessaDAO = null;
        }

        return obj;
    }

    public RemessaTransacaoVO consultarPorChavePrimaria(final int codigo) throws Exception {
        String sql = "SELECT * FROM Remessa WHERE codigo = ?";
        PreparedStatement sqlConsultar = con.prepareStatement(sql);
        sqlConsultar.setInt(1, codigo);
        ResultSet tabelaResultado = sqlConsultar.executeQuery();
        if (!tabelaResultado.next()) {
            throw new ConsistirException(String.format("Dados Não Encontrados ( Remessa %s)",
                    new Object[]{
                        codigo
                    }));
        }
        return (montarDados(tabelaResultado, con));
    }
}

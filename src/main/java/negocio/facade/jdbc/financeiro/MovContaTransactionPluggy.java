package negocio.facade.jdbc.financeiro;

import negocio.comuns.financeiro.MovContaTransactionPluggyVO;
import negocio.comuns.financeiro.PluggyJaRecebidoZwVO;
import negocio.comuns.utilitarias.Calendario;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisValidacao;
import negocio.facade.jdbc.arquitetura.SuperEntidade;
import negocio.interfaces.financeiro.MovContaTransactionPluggyInterfaceFacade;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.Types;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

public class MovContaTransactionPluggy extends SuperEntidade implements MovContaTransactionPluggyInterfaceFacade {

    public MovContaTransactionPluggy() throws Exception {
        super();
    }

    public MovContaTransactionPluggy(Connection con) throws Exception {
        super(con);
    }

    public MovContaTransactionPluggyVO montarDados(ResultSet dadosSQL) throws Exception {
        MovContaTransactionPluggyVO obj = new MovContaTransactionPluggyVO();
        obj.setCodigo(dadosSQL.getInt("codigo"));
        obj.setMovConta(getFacade().getMovConta().consultarPorChavePrimaria(
                dadosSQL.getInt("movConta"), Uteis.NIVELMONTARDADOS_MINIMOS));
        obj.setIdTransaction(dadosSQL.getString("idTransaction"));
        obj.setDataOperacao(dadosSQL.getTimestamp("dataOperacao"));

        if (!UteisValidacao.emptyNumber(dadosSQL.getInt("pluggyJaRecebidoZw"))) {
            PluggyJaRecebidoZw pluggyJaRecebidoZwDAO = new PluggyJaRecebidoZw(con);
            try {
                obj.setPluggyJaRecebidoZw(pluggyJaRecebidoZwDAO.consultarPorChavePrimaria(dadosSQL.getInt("pluggyJaRecebidoZw")));
            } catch (Exception ex) {
            } finally {
                pluggyJaRecebidoZwDAO = null;
            }
        }
        return obj;
    }

    public void incluir(MovContaTransactionPluggyVO obj) throws Exception {
        try {
            String sql = "INSERT INTO movContaTransactionPluggy(movconta, idTransaction, dataOperacao) VALUES (?, ?, ?)";
            try (PreparedStatement sqlInserir = con.prepareStatement(sql)) {
                sqlInserir.setInt(1, obj.getMovConta().getCodigo());
                sqlInserir.setString(2, obj.getIdTransaction());
                sqlInserir.setTimestamp(3, Uteis.getDataJDBCTimestamp(obj.getDataOperacao()));
                sqlInserir.execute();
            }
        } catch (Exception e) {
            throw e;
        }
    }

    public List<MovContaTransactionPluggyVO> consultarPorIdTransaction(String idTransaction) throws Exception {
        String sql = "SELECT * FROM movContaTransactionPluggy WHERE idTransaction = '" + idTransaction + "'";
        List<MovContaTransactionPluggyVO> lstMovContaTransactionPluggyVO = new ArrayList<MovContaTransactionPluggyVO>();
        try (PreparedStatement ps = con.prepareStatement(sql)) {
            try (ResultSet rs = ps.executeQuery()) {
                while (rs.next()) {
                    lstMovContaTransactionPluggyVO.add(montarDados(rs));
                }
            }
        }
        return lstMovContaTransactionPluggyVO;
    }

    public MovContaTransactionPluggyVO consultarPorChavePrimaria(int codigo) throws Exception {
        String sql = "SELECT * FROM movContaTransactionPluggy WHERE codigo = '" + codigo + "'";
        try (PreparedStatement ps = con.prepareStatement(sql)) {
            try (ResultSet rs = ps.executeQuery()) {
                while (rs.next()) {
                    return montarDados(rs);
                }
            }
        }
        return null;
    }

    public void incluirJaRecebidoZW(MovContaTransactionPluggyVO obj) throws Exception {
        PluggyJaRecebidoZw pluggyJaRecebidoZwDAO;
        Date dataOperacao = Calendario.hoje();
        try {
            String sql = "INSERT INTO movContaTransactionPluggy(idTransaction, movconta, dataOperacao, pluggyJaRecebidoZW) VALUES (?, ?, ?, ?) RETURNING codigo";
            try (PreparedStatement sqlInserir = con.prepareStatement(sql)) {
                sqlInserir.setString(1, obj.getIdTransaction());
                sqlInserir.setNull(2, Types.NULL);
                sqlInserir.setTimestamp(3, Uteis.getDataJDBCTimestamp(dataOperacao));
                sqlInserir.setNull(4, Types.NULL);

                ResultSet rs = sqlInserir.executeQuery();
                if (rs.next()) {
                    obj.setCodigo(rs.getInt("codigo"));
                }
            } catch (Exception ex) {
                Uteis.logarDebug("Não foi possível incluir o registro na tabela movContaTransactionPluggy: " + ex.getMessage());
                throw ex;
            }

            //Incluir relacionamento PluggyJaRecebidoZwVO
            PluggyJaRecebidoZwVO pluggyJaRecebidoZwVO = new PluggyJaRecebidoZwVO();
            pluggyJaRecebidoZwVO.setMovContaTransactionPluggyVO(obj);
            pluggyJaRecebidoZwVO.setDescricao(obj.getPluggyTransactionDTO().getDescription() + " (JÁ RECEBIDO NO ADM)");
            pluggyJaRecebidoZwVO.setDataVencimento(obj.getPluggyTransactionDTO().getDate());
            pluggyJaRecebidoZwVO.setDataOperacao(Uteis.getDataJDBCTimestamp(dataOperacao));
            pluggyJaRecebidoZwVO.setValor(obj.getPluggyTransactionDTO().getAmount());

            pluggyJaRecebidoZwDAO = new PluggyJaRecebidoZw(con);
            pluggyJaRecebidoZwVO = pluggyJaRecebidoZwDAO.incluir(pluggyJaRecebidoZwVO);
            obj.setPluggyJaRecebidoZw(pluggyJaRecebidoZwVO);

            //Atualizar a coluna PluggyJaRecebidoZwVO da tabela movContaTransactionPluggy
            atualizarPluggyJaRecebidoZw(obj);

        } catch (Exception e) {
            throw e;
        } finally {
            pluggyJaRecebidoZwDAO = null;
        }
    }

    public void atualizarPluggyJaRecebidoZw(MovContaTransactionPluggyVO obj) throws Exception {
        try {
            executarConsulta("UPDATE movContaTransactionPluggy set pluggyJaRecebidoZw = " + obj.getPluggyJaRecebidoZw().getCodigo() + " WHERE codigo = " + obj.getCodigo(), con);
        } catch (Exception e) {
            throw e;
        }
    }

    public void excluirByMovConta(int movconta) throws Exception {
        try {
            executarConsulta("DELETE from movContaTransactionPluggy where movconta = " + movconta, con);
        } catch (Exception e) {
            throw e;
        }
    }

    public void excluirByChavePrimaria(int codigo) throws Exception {
        PluggyJaRecebidoZw pluggyJaRecebidoZwDAO;
        try {
            executarConsulta("DELETE from movContaTransactionPluggy where codigo = " + codigo, con);

            pluggyJaRecebidoZwDAO = new PluggyJaRecebidoZw(con);
            pluggyJaRecebidoZwDAO.excluirByMovContaTransactionPluggy(codigo);
        } catch (Exception e) {
            throw e;
        } finally {
            pluggyJaRecebidoZwDAO = null;
        }
    }
}



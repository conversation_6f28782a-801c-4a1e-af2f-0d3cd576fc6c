package negocio.facade.jdbc.financeiro;

import negocio.comuns.financeiro.PluggyAccountBloqueioVO;
import negocio.facade.jdbc.arquitetura.SuperEntidade;
import negocio.interfaces.financeiro.PluggyAccountBloqueioInterfaceFacade;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;

public class PluggyAccountBloqueio extends SuperEntidade implements PluggyAccountBloqueioInterfaceFacade {

    public PluggyAccountBloqueio() throws Exception {
        super();
    }

    public PluggyAccountBloqueio(Connection con) throws Exception {
        super(con);
    }

    public void incluir(PluggyAccountBloqueioVO obj) throws Exception {
        try {
            String sql = "INSERT INTO pluggyAccountBloqueio(id, pluggyitem) VALUES (?, ?)";
            obj.setCodigo(obterValorChavePrimariaCodigo() + 1);
            try (PreparedStatement sqlInserir = con.prepareStatement(sql)) {
                sqlInserir.setString(1, obj.getId());
                sqlInserir.setString(2, obj.getPluggyItem());
                sqlInserir.execute();
            }
        } catch (Exception e) {
            throw e;
        }
    }

    public void excluirById(String id) throws Exception {
        try {
            String sql = "DELETE FROM pluggyAccountBloqueio WHERE id = ?";
            try (PreparedStatement sqlInserir = con.prepareStatement(sql)) {
                sqlInserir.setString(1, id);
                sqlInserir.execute();
            }
        } catch (Exception e) {
            throw e;
        }
    }

    public void excluirByIdPluggyItem(String idPluggyItem) throws Exception {
        try {
            String sql = "DELETE FROM pluggyAccountBloqueio WHERE pluggyitem = ?";
            try (PreparedStatement sqlInserir = con.prepareStatement(sql)) {
                sqlInserir.setString(1, idPluggyItem);
                sqlInserir.execute();
            }
        } catch (Exception e) {
            throw e;
        }
    }

    public boolean consultarById(String id) throws Exception {
        String sql = "SELECT * FROM pluggyaccountbloqueio WHERE id = '" + id + "'";
        try (PreparedStatement ps = con.prepareStatement(sql)) {
            try (ResultSet rs = ps.executeQuery()) {
                if (rs.next()) {
                    return true; //possui Bloqueio
                }
            }
        }
        return false;
    }

}

/*
 * To change this template, choose Too<PERSON> | Templates
 * and open the template in the editor.
 */

package negocio.facade.jdbc.financeiro;

import br.com.pactosolucoes.ce.comuns.enumerador.Mes;
import java.sql.Connection;
import java.sql.DriverManager;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.util.ArrayList;
import java.util.Collection;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import negocio.comuns.arquitetura.UsuarioVO;
import negocio.comuns.financeiro.DFSinteticoDWVO;
import negocio.comuns.financeiro.ReceitaSinteticoDWVO;
import negocio.comuns.utilitarias.Calendario;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisValidacao;
import negocio.facade.jdbc.arquitetura.SuperEntidade;
import negocio.facade.jdbc.utilitarias.Conexao;
import negocio.interfaces.financeiro.DFSinteticoDWInterfaceFacade;
import relatorio.negocio.comuns.financeiro.ReceitaPorPeriodoSinteticoRelTO;
import relatorio.negocio.jdbc.financeiro.ReceitaPorPeriodoSinteticoRel;
import servicos.DemonstrativoFinanceiroSintetico;

/**
 *
 * <AUTHOR>
 */
public class DFSinteticoDW extends SuperEntidade implements DFSinteticoDWInterfaceFacade{

    public DFSinteticoDW() throws Exception {
        super();
    }

    public DFSinteticoDW(Connection con) throws Exception {
        super(con);
    }


    public static void main(String ... args){
        try{
            Connection con1 = DriverManager.getConnection("*****************************************", "zillyonweb", "pactodb");
            DFSinteticoDW dfsint = new DFSinteticoDW(con1);
            Conexao.guardarConexaoForJ2SE(con1);
            dfsint.gerarDadosSintetico(1, true, null);
        }catch(Exception e){
            e.printStackTrace();
        }

    }

     public void atualizarDadosBI(Connection con) throws Exception{
          DFSinteticoDW dfsint = new DFSinteticoDW(con);
          Conexao.guardarConexaoForJ2SE(con);
          dfsint.gerarDadosSintetico(null, true, null);
     }

    public List<DFSinteticoDWVO> consultar(Integer empresa) throws Exception{
        ResultSet rs = criarConsulta("select * from dfsinteticodw where empresa = " + empresa + " order by ano DESC, mes DESC LIMIT 12", con);
        return montarDadosConsulta(rs, con);
        
    }

    public String consultarParaGrafico(Integer empresa,Date dataInicio,Date dataFinal) throws Exception{
        ResultSet rs = null;
        if(dataInicio != null || dataFinal != null) {
            rs = criarConsulta("select * from dfsinteticodw where empresa = " + empresa + " " +
                    " and cast((ano || '-' || mes || '-' || '01') as date) BETWEEN '"+Uteis.getDataJDBC(dataInicio)+"' AND '"+Uteis.getDataJDBC(dataFinal)+"'"
                    +"  order by ano DESC, mes DESC LIMIT 6", con);
        }else{
            rs = criarConsulta("select * from dfsinteticodw where empresa = " + empresa +" order by ano DESC, mes DESC LIMIT 6", con);
        }
        StringBuilder sql = new StringBuilder("chartData = [");
        Date dataAtu = null;
        List<DFSinteticoDWVO> lista = new ArrayList<DFSinteticoDWVO>();
        while(rs.next()){
            if(dataAtu == null || Calendario.maior(rs.getTimestamp("dataexecucao"), dataAtu)){
                dataAtu = rs.getTimestamp("dataexecucao");
            }
            lista.add(montarDados(rs));
        }
        Collections.reverse(lista);
        
        for(DFSinteticoDWVO df : lista){
            Mes mes = Mes.getMesPeloCodigo(df.getMes());
            Double despesa = df.getDespesa() < 0 ? df.getDespesa() * -1 : df.getDespesa();
            sql.append(",{\"year\": \"").append(mes.getDescricao())
                    .append("\",\"receita\": \"")
                    .append(Uteis.arredondarForcando2CasasDecimais(df.getReceita()))
                    .append("\", \"faturamento\": \"")
                    .append(Uteis.arredondarForcando2CasasDecimais(df.getFaturamento()))
                    .append("\", \"despesas\": \"")
                    .append(Uteis.arredondarForcando2CasasDecimais(despesa))
                    .append("\", \"competencia\": \"")
                    .append(Uteis.arredondarForcando2CasasDecimais(df.getCompetencia())).append("\"}");
        }
        return (dataAtu == null ? " " : Uteis.getDataComHHMM(dataAtu)) + "/horaAtua/"+sql.toString().replaceFirst(",", "")+"]";

    }

    public static List<DFSinteticoDWVO> montarDadosConsulta(ResultSet tabelaResultado, Connection con) throws Exception {
        List<DFSinteticoDWVO> vetResultado = new ArrayList<DFSinteticoDWVO>();
        while (tabelaResultado.next()) {
            DFSinteticoDWVO obj = new DFSinteticoDWVO();
            obj = montarDados(tabelaResultado);
            vetResultado.add(obj);
        }
        return vetResultado;
    }

    public static DFSinteticoDWVO montarDados(ResultSet rs) throws Exception{
        DFSinteticoDWVO obj = new DFSinteticoDWVO();
        obj.setMes(rs.getInt("mes"));
        obj.setAno(rs.getInt("ano"));
        obj.setReceita(rs.getDouble("receita"));
        obj.setFaturamento(rs.getDouble("faturamento"));
        obj.setDespesa(rs.getDouble("despesa"));
        obj.setEmpresa(rs.getInt("empresa"));
        obj.setCompetencia(rs.getDouble("competencia"));
        obj.setDataExecucao(rs.getTimestamp("dataexecucao"));
        return obj;
        
    }

    public void incluir(Collection<DFSinteticoDWVO> lista, Integer empresa) throws Exception {
        try {
            con.setAutoCommit(false);
            for(DFSinteticoDWVO dfSintetico : lista){
                ResultSet rs = criarConsulta("SELECT * FROM dfsinteticodw WHERE mes = "
                        +dfSintetico.getMes()+" AND ano = "+dfSintetico.getAno()
                        +" AND empresa = "+empresa, con);
                PreparedStatement stm;
                boolean existe = rs.next();
                if(existe){
                    stm = con.prepareStatement("UPDATE dfsinteticodw SET mes = ?, ano = ?, receita = ?, faturamento = ?, "
                            + "despesa = ?, dataexecucao = ?, empresa = ?, competencia = ? WHERE mes = ? AND ano = ? AND empresa = ?");
                }else{
                    stm = con.prepareStatement("INSERT INTO dfsinteticodw(mes, ano, receita, faturamento, despesa, dataexecucao, empresa, competencia)" + " VALUES (?,?,?,?,?,?,?,?)");
                }
                
                int i = 1;
                stm.setInt(i++, dfSintetico.getMes());
                stm.setInt(i++, dfSintetico.getAno());
                stm.setDouble(i++, dfSintetico.getReceita());
                stm.setDouble(i++, dfSintetico.getFaturamento());
                stm.setDouble(i++, dfSintetico.getDespesa());
                stm.setTimestamp(i++, Uteis.getDataJDBCTimestamp(Calendario.hoje()));
                stm.setInt(i++, empresa);
                stm.setDouble(i++, dfSintetico.getCompetencia());
                if(existe){
                    stm.setInt(i++, dfSintetico.getMes());
                    stm.setInt(i++, dfSintetico.getAno());
                    stm.setInt(i++, empresa);
                }
                stm.execute();
            }


            con.commit();
        } catch (Exception e) {
            con.rollback();
            throw e;
        } finally {
            con.setAutoCommit(true);
        }
    }

    public void gerarDadosSinteticoProcesso(Integer empresa, Date inicio, Date fim) throws Exception {
        StringBuilder sql = new StringBuilder();
        sql.append("SELECT codigo FROM empresa ");
        if (!UteisValidacao.emptyNumber(empresa)) {
            sql.append("where codigo = ").append(empresa);
        }
        ResultSet rs = criarConsulta(sql.toString(), getCon());
        while (rs.next()) {
            DemonstrativoFinanceiroSintetico.processarDemonstrativo(getCon(), rs.getInt("codigo"), null, false, fim, inicio);
        }
    }

    public void gerarDadosSintetico(Integer empresa, boolean receita, Integer nrMeses) throws Exception {
        if (empresa == null || empresa == 0) {
            ResultSet rs = criarConsulta("SELECT codigo FROM empresa ", getCon());
            while (rs.next()) {
                DemonstrativoFinanceiroSintetico.processarDemonstrativo(getCon(), rs.getInt("codigo"), nrMeses);
                if (receita) {
                    gerarReceitaPorFormaPagamento(rs.getInt("codigo"), Calendario.hoje());
                }
            }
        } else {
            DemonstrativoFinanceiroSintetico.processarDemonstrativo(getCon(), empresa, nrMeses);
            if (receita) {
                gerarReceitaPorFormaPagamento(empresa, Calendario.hoje());
            }
        }
    }

    public ReceitaSinteticoDWVO consultarReceitaPorFormaPagamento(Integer empresa) throws Exception{
        ResultSet set = criarConsulta("SELECT * FROM receitasinteticodw WHERE empresa = " + empresa, con);
        if(set.next()){
            ReceitaSinteticoDWVO receita = new ReceitaSinteticoDWVO();
            receita.setAno(set.getInt("ano"));
            receita.setMes(set.getInt("mes"));
            receita.setCartaoCredito(set.getDouble("cartaocredito"));
            receita.setCartaoDebito(set.getDouble("cartaodebito"));
            receita.setChequePrazo(set.getDouble("chequeprazo"));
            receita.setChequeVista(set.getDouble("chequevista"));
            receita.setBoleto(set.getDouble("boleto"));
            receita.setDinheiro(set.getDouble("dinheiro"));
            receita.setPix(set.getDouble("pix"));
            receita.setTransferenciaBancaria(set.getDouble("transferenciaBancaria"));
            receita.setOutros(set.getDouble("outros"));
            receita.setDataExecucao(set.getTimestamp("dataexecucao"));
            receita.setEmpresa(set.getInt("empresa"));
            return receita;
        }else{
            return gerarReceitaPorFormaPagamento(empresa, Calendario.hoje());
        }
    }

    public ReceitaSinteticoDWVO gerarReceitaPorFormaPagamento(Integer empresa, Date data) throws Exception {

        ReceitaPorPeriodoSinteticoRel receitaPorPeriodoSinteticoRel = new ReceitaPorPeriodoSinteticoRel();
        //calcular datas de consulta
        receitaPorPeriodoSinteticoRel.setDataInicio(Calendario.getDataComHoraZerada(Uteis.obterPrimeiroDiaMes(data)));
        receitaPorPeriodoSinteticoRel.setDataTermino(Uteis.obterUltimoDiaMesUltimaHora(data));
        // a data termino setada está com hora zerada, por isso adiciona +1 para ficar zero horas do dia seguinte
        List<ReceitaPorPeriodoSinteticoRelTO> listaRegistro =
                receitaPorPeriodoSinteticoRel.montarDadosReceitaPorPeriodoSinteticoRelVO(empresa);
        ReceitaPorPeriodoSinteticoRelTO receitaPeriodoSintRelVO;
        if (listaRegistro == null || listaRegistro.isEmpty()) {
            return new ReceitaSinteticoDWVO();
        } else {
            receitaPeriodoSintRelVO = listaRegistro.get(0);
        }
        ReceitaSinteticoDWVO receitaSintetico = new ReceitaSinteticoDWVO();
        receitaSintetico.setCartaoCredito(Uteis.arredondarForcando2CasasDecimaisMantendoSinal(receitaPeriodoSintRelVO.getValorTotalCartaoCredito()));
        receitaSintetico.setCartaoDebito(Uteis.arredondarForcando2CasasDecimaisMantendoSinal(receitaPeriodoSintRelVO.getValorTotalCartaoDebito()));
        receitaSintetico.setChequePrazo(Uteis.arredondarForcando2CasasDecimaisMantendoSinal(receitaPeriodoSintRelVO.getValorTotalChequePrazo()));
        receitaSintetico.setChequeVista(Uteis.arredondarForcando2CasasDecimaisMantendoSinal(receitaPeriodoSintRelVO.getValorTotalChequeVista()));
        receitaSintetico.setDinheiro(Uteis.arredondarForcando2CasasDecimaisMantendoSinal(receitaPeriodoSintRelVO.getValorTotalDinheiro()));
        receitaSintetico.setPix(Uteis.arredondarForcando2CasasDecimaisMantendoSinal(receitaPeriodoSintRelVO.getValorTotalPix()));
        receitaSintetico.setTransferenciaBancaria(Uteis.arredondarForcando2CasasDecimaisMantendoSinal(receitaPeriodoSintRelVO.getValorTotalTransferenciaBancaria()));
        receitaSintetico.setBoleto(Uteis.arredondarForcando2CasasDecimaisMantendoSinal(receitaPeriodoSintRelVO.getValorTotalBoleto()));
        receitaSintetico.setOutros(Uteis.arredondarForcando2CasasDecimaisMantendoSinal(receitaPeriodoSintRelVO.getValorTotalOutros()));

        receitaSintetico.setEmpresa(empresa);
        receitaSintetico.setAno(Uteis.getAnoData(data));
        receitaSintetico.setMes(Uteis.getMesData(data));

        boolean consultaMesAtual = receitaSintetico.getAno().equals(Uteis.getAnoData(Calendario.hoje()))
                && receitaSintetico.getMes().equals(Uteis.getMesData(Calendario.hoje()));
        
        if(consultaMesAtual){
            gravarReceitaSintetico(receitaSintetico);
        }
        return receitaSintetico;
    }
    

    public void gravarReceitaSintetico(ReceitaSinteticoDWVO receitaSintetico) throws Exception{
        executarConsulta("DELETE FROM receitasinteticodw where empresa = "+receitaSintetico.getEmpresa(), con);
        StringBuilder sql = new StringBuilder();
        sql.append("INSERT INTO receitasinteticodw( \n");
        sql.append("mes, ano, cartaocredito, cartaodebito, chequevista, chequeprazo, boleto,  \n");
        sql.append("dinheiro, pix, transferenciaBancaria, outros, empresa, dataexecucao) \n");
        sql.append("VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?); ");

        PreparedStatement stm = con.prepareStatement(sql.toString());
        int i = 1;
        stm.setInt(i++, receitaSintetico.getMes());
        stm.setInt(i++, receitaSintetico.getAno());
        stm.setDouble(i++, receitaSintetico.getCartaoCredito());
        stm.setDouble(i++, receitaSintetico.getCartaoDebito());
        stm.setDouble(i++, receitaSintetico.getChequeVista());
        stm.setDouble(i++, receitaSintetico.getChequePrazo());
        stm.setDouble(i++, receitaSintetico.getBoleto());
        stm.setDouble(i++, receitaSintetico.getDinheiro());
        stm.setDouble(i++, receitaSintetico.getPix());
        stm.setDouble(i++, receitaSintetico.getTransferenciaBancaria());
        stm.setDouble(i++, receitaSintetico.getOutros());
        stm.setInt(i++, receitaSintetico.getEmpresa());
        receitaSintetico.setDataExecucao(Calendario.hoje());
        stm.setTimestamp(i++, Uteis.getDataJDBCTimestamp(receitaSintetico.getDataExecucao()));


        stm.execute();

    }
    


}

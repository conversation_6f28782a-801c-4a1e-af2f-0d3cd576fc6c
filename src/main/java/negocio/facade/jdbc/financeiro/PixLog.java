package negocio.facade.jdbc.financeiro;

import negocio.comuns.arquitetura.LogVO;
import negocio.comuns.financeiro.PixLogVO;
import negocio.comuns.utilitarias.Calendario;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisValidacao;
import negocio.facade.jdbc.arquitetura.Log;
import negocio.facade.jdbc.arquitetura.SuperEntidade;
import negocio.interfaces.financeiro.PixLogInterfaceFacade;

import java.sql.Connection;
import java.sql.PreparedStatement;

public class PixLog extends SuperEntidade implements PixLogInterfaceFacade {

    public PixLog() throws Exception {
        super();
    }

    public PixLog(Connection connection) throws Exception {
        super(connection);
    }

    public void incluir(PixLogVO obj) throws Exception {
        String sql = "INSERT INTO pixlog(dataRegistro, pix, usuario, operacao, log) VALUES (?,?,?,?,?)";
        try (PreparedStatement ps = con.prepareStatement(sql)) {
            int i = 0;
            ps.setTimestamp(++i, Uteis.getDataJDBCTimestamp(Calendario.hoje()));
            if (UteisValidacao.emptyNumber(obj.getPixVO().getCodigo())) {
                ps.setNull(++i, 0);
            } else {
                ps.setInt(++i, obj.getPixVO().getCodigo());
            }
            if (UteisValidacao.emptyNumber(obj.getUsuarioVO().getCodigo())) {
                ps.setNull(++i, 0);
            } else {
                ps.setInt(++i, obj.getUsuarioVO().getCodigo());
            }
            ps.setInt(++i, obj.getOperacao().getCodigo());
            ps.setString(++i, obj.getLog());
            ps.execute();
        }
        obj.setCodigo(obterValorChavePrimariaCodigo());
        obj.setNovoObj(false);

        Log logDAO;
        try {
            logDAO = new Log(this.con);

            LogVO logObj = new LogVO();
            logObj.setNomeEntidade("PIX");
            logObj.setDescricao("PIX-" + obj.getOperacao().getDescricao());
            logObj.setChavePrimaria(obj.getPixVO().getCodigo().toString());
            logObj.setDataAlteracao(Calendario.hoje());
            logObj.setOperacao("ALTERAÇÃO");
            logObj.setNomeCampo("PIX-" + obj.getOperacao().getDescricao());
            logObj.setPessoa(obj.getPixVO().getPessoa());
            logObj.setValorCampoAnterior("");
            logObj.setValorCampoAlterado(obj.getLog());
            if (!UteisValidacao.emptyNumber(obj.getUsuarioVO().getCodigo())) {
                logObj.setUsuarioVO(obj.getUsuarioVO());
                logObj.setResponsavelAlteracao(obj.getUsuarioVO().getNomeAbreviado());
                logObj.setUserOAMD(obj.getUsuarioVO().getUserOamd());
            }

            logDAO.incluirSemCommit(logObj);
        } catch (Exception ex) {
            ex.printStackTrace();
        } finally {
            logDAO = null;
        }
    }
}

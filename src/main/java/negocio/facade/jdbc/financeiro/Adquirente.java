package negocio.facade.jdbc.financeiro;

import negocio.comuns.financeiro.AdquirenteVO;
import negocio.comuns.financeiro.ConvenioCobrancaVO;
import negocio.comuns.financeiro.RemessaVO;
import negocio.comuns.financeiro.TransacaoVO;
import negocio.comuns.financeiro.enumerador.TipoCobrancaEnum;
import negocio.comuns.financeiro.enumerador.TipoConvenioCobrancaEnum;
import negocio.comuns.utilitarias.Ordenacao;
import negocio.comuns.utilitarias.UteisValidacao;
import negocio.facade.jdbc.arquitetura.SuperEntidade;
import negocio.interfaces.financeiro.AdquirenteInterfaceFacade;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

public class Adquirente extends SuperEntidade implements AdquirenteInterfaceFacade{

    public Adquirente() throws Exception {
        super();
    }

    public Adquirente(Connection con) throws Exception {
        super(con);
    }

    @Override
    public void incluir(AdquirenteVO obj) throws Exception {
        AdquirenteVO.validarDados(obj);
        String insert = "insert into adquirente (nome, tipoconvenio, situacao, cnpj) values (?,?,?, ?)";
        PreparedStatement stm = con.prepareStatement(insert);
        obj.setNome(obj.getNome().toUpperCase());
        stm.setString(1, obj.getNome());
        if(obj.getTipoConvenio() == null){
            stm.setNull(2,0);
        }else{
            stm.setInt(2, obj.getTipoConvenio().getCodigo());
        }
        stm.setBoolean(3, obj.isSituacao());
        stm.setString(4, obj.getCnpj());
        stm.execute();
        obj.setCodigo(obterValorChavePrimariaCodigo());
        obj.setNovoObj(false);

    }

    public AdquirenteVO montarDados(ResultSet rs) throws Exception{
        AdquirenteVO adquirenteVO = new AdquirenteVO();
        adquirenteVO.setCodigo(rs.getInt("codigo"));
        adquirenteVO.setSituacao(rs.getBoolean("situacao"));
        adquirenteVO.setNome(rs.getString("nome"));
        adquirenteVO.setTipoConvenio(TipoConvenioCobrancaEnum.valueOf(rs.getInt("tipoconvenio")));
        adquirenteVO.setCnpj(rs.getString("cnpj"));
        return adquirenteVO;
    }

    public AdquirenteVO consultarPorCodigo(Integer codigo) throws Exception{
        AdquirenteVO eCache = (AdquirenteVO) obterFromCache(codigo);
        if (eCache != null){
            return eCache;
        }
        ResultSet rs = criarConsulta("select * from adquirente where codigo = " + codigo, con);
        if (!rs.next()) {
            eCache = new AdquirenteVO();
            putToCache(eCache);
            return eCache;
        }
        eCache = (montarDados(rs));
        putToCache(eCache);
        return eCache;
    }

    @Override
    public void alterar(AdquirenteVO obj) throws Exception {
        AdquirenteVO.validarDados(obj);
        obj.setNome(obj.getNome().toUpperCase());
        String update = "update adquirente set nome = ? , tipoconvenio = ? , situacao = ?, cnpj = ? where codigo = ?";
        PreparedStatement stm = con.prepareStatement(update);
        stm.setString(1, obj.getNome());
        if(obj.getTipoConvenio() == null){
            stm.setNull(2, 0);
        }else{
            stm.setInt(2, obj.getTipoConvenio().getCodigo());
        }
        stm.setBoolean(3, obj.isSituacao());
        stm.setString(4, obj.getCnpj());
        stm.setInt(5, obj.getCodigo());
        stm.execute();
    }

    @Override
    public void excluir(AdquirenteVO obj) throws Exception {
        executarConsulta("delete from adquirente where codigo = "+obj.getCodigo(), con);
    }

    @Override
    public List<AdquirenteVO> consultarTodos(boolean ativos) throws Exception {
        List<AdquirenteVO> lista = new ArrayList<AdquirenteVO>();
        String whereAtivos = "";
        if(ativos){
            whereAtivos = "WHERE situacao = true ";
        }
        ResultSet rs = criarConsulta("SELECT * FROM adquirente "+whereAtivos+" order by nome", con);
        while(rs.next()){
            lista.add(montarDados(rs));
        }
        return lista;
    }

    @Override
    public List<AdquirenteVO> consultarTodosGeoidt(boolean ativos, boolean geoitdt) throws Exception {
        List<AdquirenteVO> lista = new ArrayList<AdquirenteVO>();
        String whereAtivos = "";
        if(ativos){
            whereAtivos = "WHERE situacao = true";
        }
        ResultSet rs = criarConsulta("SELECT * FROM adquirente "+ whereAtivos + " order by nome", con);
        while(rs.next()){
            lista.add(montarDados(rs));
        }
        return lista;
    }

    private String adquirenteOutrosConvenios(TransacaoVO transacaoVO, RemessaVO remessaVO) {
        if(transacaoVO != null) {
            if (transacaoVO.getConvenioCobrancaVO().getTipo().equals(TipoConvenioCobrancaEnum.DCC_E_REDE)) {
                return "REDE";
            } else if (transacaoVO.getConvenioCobrancaVO().getTipo().equals(TipoConvenioCobrancaEnum.DCC_GETNET_ONLINE)) {
                return "GETNET";
            } else if (transacaoVO.getConvenioCobrancaVO().getTipo().equals(TipoConvenioCobrancaEnum.DCC_CIELO_ONLINE)) {
                return "CIELO";
            } else if (transacaoVO.getConvenioCobrancaVO().getTipo().equals(TipoConvenioCobrancaEnum.DCC_STONE_ONLINE)) {
                return "STONE";
            } else if (transacaoVO.getConvenioCobrancaVO().getTipo().equals(TipoConvenioCobrancaEnum.DCC_FACILITEPAY)) {
                return "FYPAY";
            } else if (transacaoVO.getConvenioCobrancaVO().getTipo().equals(TipoConvenioCobrancaEnum.DCC_PAGOLIVRE)) {
                return "PAGOLIVRE";
            } else if (transacaoVO.getConvenioCobrancaVO().getTipo().equals(TipoConvenioCobrancaEnum.DCC_CEOPAG)) {
                return "CEOPAG";
            } else if (transacaoVO.getConvenioCobrancaVO().getTipo().equals(TipoConvenioCobrancaEnum.DCC_PAGBANK)) {
                return "PAGBANK";
            }
        }else if(remessaVO != null){
            if (remessaVO.getConvenioCobranca().getTipo().equals(TipoConvenioCobrancaEnum.DCC_GETNET)) {
                return "GETNET";
            } else if (remessaVO.getConvenioCobranca().getTipo().equals(TipoConvenioCobrancaEnum.DCC_BIN)) {
                return "BIN";
            }else if (remessaVO.getConvenioCobranca().getTipo().equals(TipoConvenioCobrancaEnum.DCC)) {
                return "CIELO";
            }
        }
        return "";
    }

    public int buscarCodAdquirenteCadastrada(String adquirente) throws Exception {
        int codAdquirente = 0;
            List<AdquirenteVO> adquirentes = consultarTodos(true);
            for(AdquirenteVO adq : adquirentes){
                if(adq.getNome().toLowerCase().contains(adquirente.toLowerCase())){
                    codAdquirente = adq.getCodigo();
                    break;
                }
            }
        return codAdquirente;
    }

    public String consultarJSON(String situacao) throws Exception {
        ResultSet rs = getRS(situacao);
        StringBuilder json = new StringBuilder();
        json.append("{\"aaData\":[");
        boolean dados = false;
        while (rs.next()) {
            dados = true;
            json.append("[\"").append(rs.getString("codigo")).append("\",");
            json.append("\"").append(rs.getString("nome").trim()).append("\",");
            //json.append("\"").append(rs.getBoolean("isgeoitd")).append("\",");
            Integer tipo = rs.getInt("tipoconvenio");
            TipoConvenioCobrancaEnum tpConv = UteisValidacao.emptyNumber(tipo) ?
                    null : TipoConvenioCobrancaEnum.valueOf(tipo);
            json.append("\"").append(tpConv == null ? "" : tpConv.getDescricao()).append("\",");
            if (rs.getBoolean("situacao")) {
                json.append("\"").append("ATIVO").append("\"],");
            } else {
                json.append("\"").append("INATIVO").append("\"],");
            }
        }
        if (dados) {
            json.deleteCharAt(json.toString().length() - 1);
        }
        json.append("]}");
        return json.toString();
    }

    private ResultSet getRS(String situacao) throws SQLException {
        StringBuilder sql = new StringBuilder();
        sql.append("SELECT nome, codigo, situacao, tipoConvenio FROM adquirente  ");
        if (situacao.equals("AT")) {
            sql.append("WHERE situacao = true ");
        } else if (situacao.equals("NA")) {
            sql.append("WHERE situacao = false ");
        }
        sql.append(" ORDER BY nome");

        PreparedStatement sqlConsultar = con.prepareStatement(sql.toString());
        return sqlConsultar.executeQuery();
    }

    public List consultarParaImpressao(String filtro, String ordem, String campoOrdenacao, int i, String situacao) throws SQLException {

        ResultSet rs = getRS(situacao);
        List lista = new ArrayList();

        while (rs.next()) {
            AdquirenteVO ad = new AdquirenteVO();
            Integer tipo = rs.getInt("tipoconvenio");
            TipoConvenioCobrancaEnum tpConv = UteisValidacao.emptyNumber(tipo) ?
                    null : TipoConvenioCobrancaEnum.valueOf(tipo);
            String geral = rs.getString("codigo") +
                    rs.getString("nome")
                    + (tpConv == null ? "" : tpConv.getDescricao()
                    + rs.getBoolean("situacao"));
            if (geral.toLowerCase().contains(filtro.toLowerCase())) {
                ad.setCodigo(rs.getInt("codigo"));
                ad.setNome(rs.getString("nome"));
                ad.setTipoConvenio(tpConv);
                ad.setSituacao(rs.getBoolean("situacao"));
                lista.add(ad);
            }
        }
        if (campoOrdenacao.equals("Código")) {
            Ordenacao.ordenarLista(lista, "codigo");
        } else if (campoOrdenacao.equals("Descrição")) {
            Ordenacao.ordenarLista(lista, "nome");
        } else if (campoOrdenacao.equals("Tipo Convênio")) {
            Ordenacao.ordenarLista(lista, "convenioApresentar");
        } else if (campoOrdenacao.equals(("Situação"))){
            Ordenacao.ordenarLista(lista,"situacao");
        }
        if (ordem.contains("desc")) {
            Collections.reverse(lista);
        }
        return lista;

    }


    @Override
    public AdquirenteVO consultarOuCriaSeNaoExistir(String nome) {
        try {
            if (UteisValidacao.emptyString(nome)) {
                return new AdquirenteVO();
            }
            String sql = "select * from adquirente  where upper(nome) = upper(?)";
            PreparedStatement ps = con.prepareStatement(sql);
            ps.setString(1, nome);
            ResultSet rs = ps.executeQuery();
            if (rs.next()) {
                return montarDados(rs);
            } else {
                AdquirenteVO nova = new AdquirenteVO();
                nova.setNome(nome.toUpperCase());
                incluir(nova);
                return nova;
            }
        } catch (Exception ex) {
            return new AdquirenteVO();
        }
    }

    public AdquirenteVO obterAdquirenteTransacao(TransacaoVO transacaoVO) {
        String adquirente = transacaoVO.getAdquirente();
        if (UteisValidacao.emptyString(adquirente)) {
            adquirente = adquirenteOutrosConvenios(transacaoVO, null);
        }
        return obterAdquirente(transacaoVO.getConvenioCobrancaVO(), adquirente);
    }

    public AdquirenteVO obterAdquirenteRemessaItem(RemessaVO remessaVO) {
        String nomeAdquirenteConsultar = adquirenteOutrosConvenios(null, remessaVO);
        return obterAdquirente(remessaVO.getConvenioCobranca(), nomeAdquirenteConsultar);
    }

    private AdquirenteVO obterAdquirente(ConvenioCobrancaVO convenioVO, String nomeAdquirenteConsultar) {
        try {
            List<AdquirenteVO> adquirentesCadastrados = consultarTodos(true);

            //consultar pelo tipo do convênio de cobrança
            for (AdquirenteVO adVO : adquirentesCadastrados) {
                if (adVO.getTipoConvenio() != null &&
                        convenioVO != null &&
                        !UteisValidacao.emptyNumber(convenioVO.getCodigo()) &&
                        convenioVO.getTipo() != null &&
                        convenioVO.getTipo().equals(adVO.getTipoConvenio())) {
                    return adVO;
                }
            }


            //consulta pelo nome e com o tipo online.. edi
            if (convenioVO != null && !UteisValidacao.emptyNumber(convenioVO.getCodigo()) &&
                    !convenioVO.getTipo().equals(TipoConvenioCobrancaEnum.NENHUM) &&
                    !UteisValidacao.emptyString(nomeAdquirenteConsultar)) {
                for (AdquirenteVO adVO : adquirentesCadastrados) {

                    //EDI
                    if (convenioVO.getTipo().getTipoCobranca().equals(TipoCobrancaEnum.EDI_DCC) &&
                            adVO.getNome().toUpperCase().contains(nomeAdquirenteConsultar.toUpperCase()) &&
                            (adVO.getNome().toUpperCase().contains("EDI") || adVO.getNome().toUpperCase().contains("REMESSA"))) {
                        return adVO;
                    }

                    //Online
                    if (convenioVO.getTipo().getTipoCobranca().equals(TipoCobrancaEnum.ONLINE) &&
                            adVO.getNome().toUpperCase().contains(nomeAdquirenteConsultar.toUpperCase()) &&
                            (adVO.getNome().toUpperCase().contains("ONLINE") || adVO.getNome().toUpperCase().contains("ON-LINE") || adVO.getNome().toUpperCase().contains("ON LINE"))) {
                        return adVO;
                    }
                }
            }


            //consulta pelo nome
            if (!UteisValidacao.emptyString(nomeAdquirenteConsultar)) {
                for (AdquirenteVO adVO : adquirentesCadastrados) {
                    if (adVO.getNome().toUpperCase().contains(nomeAdquirenteConsultar.toUpperCase())) {
                        return adVO;
                    }
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        return new AdquirenteVO();
    }

    public AdquirenteVO obterAdquirentePeloTipoConvenio(TipoConvenioCobrancaEnum tipoConvenio) {
        try {
            List<AdquirenteVO> adquirentesCadastrados = consultarTodos(true);

            for (AdquirenteVO adVO : adquirentesCadastrados) {
                if (adVO.getTipoConvenio() != null &&
                        tipoConvenio != null &&
                        tipoConvenio.equals(adVO.getTipoConvenio())) {
                    return adVO;
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        return new AdquirenteVO();
    }

}

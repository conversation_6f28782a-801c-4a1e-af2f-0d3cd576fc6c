package negocio.facade.jdbc.financeiro;

import com.google.common.base.Joiner;
import negocio.comuns.financeiro.MovParcelaVO;
import negocio.comuns.financeiro.PixMovParcelaVO;
import negocio.comuns.financeiro.PixVO;
import negocio.comuns.utilitarias.UteisValidacao;
import negocio.facade.jdbc.arquitetura.SuperEntidade;
import negocio.interfaces.financeiro.PixMovParcelaInterfaceFacade;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.SQLException;
import java.util.List;

public class PixMovParcela extends SuperEntidade implements PixMovParcelaInterfaceFacade {

    public PixMovParcela() throws Exception {
        super();
    }

    public PixMovParcela(Connection connection) throws Exception {
        super(connection);
    }

    public void incluir(PixVO pixVO, List<PixMovParcelaVO> pixMovParcelas) throws Exception {
        for (PixMovParcelaVO pixMovParcelaVO: pixMovParcelas) {
            pixMovParcelaVO.setPix(pixVO.getCodigo());
            incluir(pixMovParcelaVO);
        }
    }

    public PixMovParcelaVO incluir(PixVO pixVO, MovParcelaVO movParcelaVO) throws Exception {
        PixMovParcelaVO novo = new PixMovParcelaVO();
        novo.setPix(pixVO.getCodigo());
        novo.setMovparcela(movParcelaVO.getCodigo());
        novo.setValorJuros(movParcelaVO.getValorJuros());
        novo.setValorMulta(movParcelaVO.getValorMulta());
        incluir(novo);
        return novo;
    }

    public void incluir(PixMovParcelaVO pixMovParcelaVO) throws Exception {
        String sql = "INSERT INTO public.pixmovparcela (" +
                "movparcela, " +
                "pix, " +
                "valorMulta, " +
                "valorJuros )" +
                "VALUES " +
                "(?, ?, ?, ?);";

        PreparedStatement statement = con.prepareStatement(sql);
        int i = 1;
        statement.setInt(i++, pixMovParcelaVO.getMovparcela());
        statement.setInt(i++, pixMovParcelaVO.getPix());
        if (!UteisValidacao.emptyNumber(pixMovParcelaVO.getValorMulta())) {
            statement.setDouble(i++, pixMovParcelaVO.getValorMulta());
        } else {
            statement.setNull(i++, 0);
        }
        if (!UteisValidacao.emptyNumber(pixMovParcelaVO.getValorJuros())) {
            statement.setDouble(i++, pixMovParcelaVO.getValorJuros());
        } else {
            statement.setNull(i++, 0);
        }

        statement.execute();
        pixMovParcelaVO.setCodigo(obterValorChavePrimariaCodigo());
    }

    public void excluirPorContrato(Integer contrato) throws SQLException {
        String sql = "DELETE FROM pixmovparcela " +
                "WHERE movparcela IN (" +
                "       SELECT codigo FROM movparcela " +
                "       WHERE movparcela.contrato="+contrato+
                "   )";
        PreparedStatement statement = con.prepareStatement(sql);
        statement.execute();
    }

    @Override
    public void excluir(Integer movparcela, Integer pix) throws SQLException {
        String sql = "DELETE FROM pixmovparcela WHERE movparcela ="+movparcela+" AND pix="+pix;
        PreparedStatement statement = con.prepareStatement(sql);
        statement.execute();
    }

    @Override
    public void excluirPorParcela(Integer movparcela) throws SQLException {
        String sql = "DELETE FROM pixmovparcela WHERE movparcela ="+movparcela;
        PreparedStatement statement = con.prepareStatement(sql);
        statement.execute();
    }

    @Override
    public void excluirPorPix(List<Integer> codigosPix) throws SQLException {
        String sql = "DELETE FROM pixmovparcela WHERE pix in ("+ Joiner.on(",").join(codigosPix)+")";
        PreparedStatement statement = con.prepareStatement(sql);
        statement.execute();
    }

    public void excluirPorCodigo(Integer codigo) throws SQLException {
        String sql = "DELETE FROM pixmovparcela WHERE codigo = ?;";
        PreparedStatement ps = con.prepareStatement(sql);
        ps.setInt(1, codigo);
        ps.execute();
    }
}

/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package negocio.facade.jdbc.financeiro;

import br.com.pactosolucoes.comuns.json.DadosGameJSON;
import br.com.pactosolucoes.comuns.json.SimplesJSON;
import br.com.pactosolucoes.enumeradores.CampoBIEnum;
import br.com.pactosolucoes.enumeradores.TipoDadosGameEnum;
import org.json.JSONArray;
import org.json.JSONObject;
import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import negocio.comuns.basico.ClienteVO;
import negocio.comuns.basico.ColaboradorVO;
import negocio.comuns.basico.DadosGameVO;
import negocio.comuns.basico.enumerador.TipoObservacaoOperacaoEnum;
import negocio.comuns.financeiro.enumerador.IdentificadorDadosGerencialEnum;
import negocio.comuns.utilitarias.Calendario;
import negocio.comuns.utilitarias.Uteis;
import negocio.facade.jdbc.arquitetura.Log;
import negocio.facade.jdbc.arquitetura.SuperEntidade;
import negocio.facade.jdbc.arquitetura.SuperFacadeJDBC;
import negocio.facade.jdbc.basico.Cliente;
import negocio.facade.jdbc.basico.EstornoObservacao;
import negocio.facade.jdbc.basico.ObservacaoOperacao;
import negocio.facade.jdbc.contrato.Contrato;
import negocio.facade.jdbc.contrato.ContratoOperacao;
import negocio.interfaces.basico.DadosGameInterfaceFacade;
import relatorio.negocio.comuns.basico.PendenciaResumoPessoaRelVO;

/**
 *
 * <AUTHOR>
 */
public class DadosGame extends SuperEntidade implements DadosGameInterfaceFacade{

    public DadosGame() throws Exception {
        super();
    }

    public DadosGame(Connection conexao) throws Exception {
        super(conexao);
    }
    
    @Override
    public JSONArray contratosPorDuracao(Integer empresa) throws Exception {

        StringBuilder sql = new StringBuilder();
        sql.append(" SELECT count(c.codigo) as nrcontratos, cd.numeromeses FROM contrato c\n");
        sql.append(" INNER JOIN contratoduracao cd ON cd.contrato = c.codigo\n");
        sql.append(" WHERE situacao  = 'AT' AND c.empresa = ?\n");
        sql.append(" GROUP BY cd.numeromeses\n");
        sql.append(" ORDER BY cd.numeromeses");

        PreparedStatement stm = con.prepareStatement(sql.toString());

        stm.setInt(1, empresa);

        ResultSet rs = stm.executeQuery();

        JSONArray json = new JSONArray();
        while (rs.next()) {
            JSONObject obj = new JSONObject();
            obj.put("numeromeses", rs.getString("numeromeses"));
            obj.put("nrcontratos", rs.getInt("nrcontratos"));
            json.put(obj);
        }
        return json;
    }
    
    @Override
    public JSONArray analiseVendasPorDuracao(String mesAno, Integer empresa) throws Exception {
        
        Date dataBase = Uteis.obterUltimoDiaMesUltimaHora(Uteis.getDate("01/"+mesAno));
        Date dataLimite = Uteis.somarCampoData(Uteis.getDate("01/"+mesAno), Calendar.MONTH, -3);
        StringBuilder  sql = new StringBuilder();
        sql.append("SELECT numeromeses, MAX(nrcontratosnovos) AS nrcontratosnovos, MAX(mediavalorfinalnovos) AS mediavalorfinalnovos, \n");
        sql.append("MAX(mediavalormensalnovos) AS mediavalormensalnovos, MAX(nrcontratos) AS nrcontratos, MAX(mediavalorfinal) \n");
        sql.append("AS mediavalorfinal, MAX(mediavalormensal) AS mediavalormensal FROM ( \n");
//        sql.append("--ultimos 3 meses \n");
        sql.append("SELECT cd.numeromeses, \n");
        sql.append("COUNT(c.codigo) AS nrcontratosnovos, \n");
        sql.append("avg(c.valorfinal) as mediavalorfinalnovos, \n");
        sql.append("(avg(c.valorfinal))/cd.numeromeses AS mediavalormensalnovos, null as nrcontratos, null as mediavalorfinal, \n");
        sql.append("null AS mediavalormensal FROM contrato c \n");
        sql.append("INNER JOIN contratoduracao cd ON cd.contrato = c.codigo \n");
        sql.append("WHERE c.datalancamento BETWEEN ? AND ? \n");
        sql.append("AND c.situacaocontrato IN ('MA','RE') AND NOT c.bolsa \n");
        sql.append("AND c.empresa = ? \n");
        sql.append("GROUP BY cd.numeromeses \n");
        sql.append("UNION ALL \n");
//        sql.append("-- todos \n");
        sql.append("SELECT cd.numeromeses, \n");
        sql.append("null AS nrcontratosnovos, \n");
        sql.append("null as mediavalorfinalnovos, \n");
        sql.append("null AS mediavalormensalnovos, COUNT(c.codigo) as nrcontratos, avg(c.valorfinal) as mediavalorfinal, ");
        sql.append("(avg(c.valorfinal))/cd.numeromeses AS mediavalormensal \n");
        sql.append("FROM contrato c \n");
        sql.append("INNER JOIN contratoduracao cd ON cd.contrato = c.codigo \n");
        sql.append("WHERE c.situacao IN ('AT') AND NOT c.bolsa \n");
        sql.append("AND c.empresa = ? \n");
        sql.append("GROUP BY cd.numeromeses) AS consulta GROUP BY 1 ORDER BY 1\n");
        
        PreparedStatement stm = con.prepareStatement(sql.toString());
        
        stm.setTimestamp(1, Uteis.getDataHoraJDBC(dataLimite, "00:00:00"));
        stm.setTimestamp(2, Uteis.getDataJDBCTimestamp(dataBase));
        stm.setInt(3, empresa);
        stm.setInt(4, empresa);
        
        ResultSet rs = stm.executeQuery();
        
        JSONArray json = new JSONArray();
        while(rs.next()){
            JSONObject obj = new JSONObject();
            obj.put("numeromeses", rs.getString("numeromeses"));
            obj.put("nrcontratosnovos", rs.getInt("nrcontratosnovos"));
            obj.put("mediavalorfinalnovos", rs.getDouble("mediavalorfinalnovos"));
            obj.put("mediavalormensalnovos", rs.getDouble("mediavalormensalnovos"));
            obj.put("nrcontratos", rs.getInt("nrcontratos"));
            obj.put("mediavalorfinal", rs.getDouble("mediavalorfinal"));
            obj.put("mediavalormensal", rs.getDouble("mediavalormensal"));
            json.put(obj);
        }
        return json;
    }
    
    @Override
    public JSONArray obterConsultores(String mesAno, Integer nrMesesAnteriores, Integer empresa) throws Exception {
        StringBuilder sql = new StringBuilder();
        Date dataBase = Uteis.obterUltimoDiaMesUltimaHora(Uteis.getDate("01/"+mesAno));
        Date dataLimite = Uteis.somarCampoData(Uteis.getDate("01/"+mesAno), Calendar.MONTH, -nrMesesAnteriores);
        
        sql.append(" SELECT icv.mon, icv.yyyy, icv.consultor, p.nome, SUM(icv.nrbvs) AS nrbvs, \n");
        sql.append(" SUM(icv.nrcontratos) AS nrcontratos, SUM(icv.nrrenovar) AS nrrenovar, \n");
        sql.append(" SUM(icv.nrvencidos) AS nrvencidos,SUM(icv.desistentes) AS desistentes  FROM colaborador c  \n");
        sql.append(" INNER JOIN pessoa p ON p.codigo = c.pessoa  INNER JOIN \n");
        sql.append(" (SELECT to_char(data,'MM') as mon, \n");
        sql.append(" extract(year from data) as yyyy, qc.consultor, count(codigo) as nrbvs, 0 as nrcontratos, 0 as nrrenovar, ");
        sql.append(" 0 as nrvencidos, 0 AS desistentes FROM questionariocliente qc \n");
        sql.append(" WHERE data BETWEEN ? AND ? \n");
        sql.append(" GROUP BY 1,2,3\n");
        //-------------------------------
        sql.append(" UNION ALL \n");
        sql.append(" SELECT to_char(datalancamento,'MM') as mon, \n");
        sql.append(" extract(year from datalancamento) as yyyy,consultor, 0 as nrbvs, count(codigo) as nrcontratos, 0 as nrrenovar, ");
        sql.append(" 0, 0 FROM contrato\n");
        sql.append(" WHERE datalancamento BETWEEN ? AND ? ");
        sql.append("  AND situacaocontrato in ('MA','RE') \n");
        sql.append(" GROUP BY 1,2,3\n");
        //-------------------------------
        sql.append(" UNION ALL\n");
        sql.append(" select to_char(dataprevistarenovar,'MM') as mon, \n");
        sql.append(" extract(year from dataprevistarenovar) as yyyy,consultor, 0 as nrbvs, 0 as nrcontratos, count(codigo) as nrrenovar, ");
        sql.append("  0, 0 \n");
        sql.append(" from contrato where dataprevistarenovar  BETWEEN ? AND ?\n");
        sql.append(" GROUP BY 1,2,3\n");
        //-------------------------------
        sql.append(" UNION ALL\n");
        sql.append(" select to_char(dataprevistarenovar,'MM') as mon, \n");
        sql.append(" extract(year from dataprevistarenovar) as yyyy,consultor, 0, 0, 0, count(c.codigo), 0 \n");
        sql.append(" FROM contrato c\n");
        sql.append(" INNER JOIN historicocontrato co ON co.contrato = c.codigo AND co.tipohistorico = 'VE'\n");
        sql.append(" LEFT JOIN historicocontrato hco ON hco.contrato = c.codigo AND hco.tipohistorico = 'DE'\n");
        sql.append(" WHERE dataprevistarenovar  BETWEEN ? AND ?\n");
        sql.append(" AND hco.codigo is null AND datarenovarrealizada is null\n");
        sql.append(" GROUP BY 1,2,3\n");
        //-------------------
        sql.append(" UNION ALL\n");
        sql.append(" select to_char(dataprevistarenovar,'MM') as mon, \n");
        sql.append(" extract(year from dataprevistarenovar) as yyyy,consultor, 0, 0, 0, 0, count(c.codigo)\n");
        sql.append(" FROM contrato c\n");
        sql.append(" INNER JOIN historicocontrato hco ON hco.contrato = c.codigo AND hco.tipohistorico = 'DE'\n");
        sql.append(" WHERE dataprevistarenovar  BETWEEN ? AND ?\n");
        sql.append(" AND datarenovarrealizada is null\n");
        sql.append(" GROUP BY 1,2,3\n");
        sql.append(" ) AS icv ON icv.consultor = c.codigo \n");
        sql.append(" WHERE c.empresa = ? \n");
        sql.append(" GROUP BY 1,2,3,4 \n");
        sql.append(" ORDER BY 2,1,4");

        PreparedStatement stm = con.prepareStatement(sql.toString());
        stm.setTimestamp(1, Uteis.getDataHoraJDBC(dataLimite, "00:00:00"));
        stm.setTimestamp(2, Uteis.getDataJDBCTimestamp(dataBase));

        stm.setTimestamp(3, Uteis.getDataHoraJDBC(dataLimite, "00:00:00"));
        stm.setTimestamp(4, Uteis.getDataJDBCTimestamp(dataBase));

        stm.setTimestamp(5, Uteis.getDataHoraJDBC(dataLimite, "00:00:00"));
        stm.setTimestamp(6, Uteis.getDataJDBCTimestamp(dataBase));

        stm.setTimestamp(7, Uteis.getDataHoraJDBC(dataLimite, "00:00:00"));
        stm.setTimestamp(8, Uteis.getDataJDBCTimestamp(dataBase));

        stm.setTimestamp(9, Uteis.getDataHoraJDBC(dataLimite, "00:00:00"));
        stm.setTimestamp(10, Uteis.getDataJDBCTimestamp(dataBase));

        stm.setInt(11, empresa);
        ResultSet rs = stm.executeQuery();
        
        JSONArray json = new JSONArray();
        while(rs.next()){
            JSONObject obj = new JSONObject();
            obj.put("nome", rs.getString("nome"));
            obj.put("codigocolaborador", rs.getInt("consultor"));
            obj.put("ano", rs.getString("yyyy"));
            obj.put("mes", rs.getString("mon"));
            obj.put("visitas", rs.getInt("nrbvs"));
            obj.put("conversoes", rs.getInt("nrcontratos"));
            obj.put("arenovar", rs.getInt("nrrenovar"));
            obj.put("vencidos", rs.getInt("nrvencidos"));
            obj.put("desistentes", rs.getInt("desistentes"));
            json.put(obj);
        }
        return json;
    }

    @Override
    public List<DadosGameJSON> montarDadosGame(String mesAno, Integer nrMesesAnteriores, Integer empresa) throws Exception{
        Date dataBase = Uteis.obterUltimoDiaMesUltimaHora(Uteis.getDate("01/"+mesAno));
        Date dataLimite = Uteis.somarCampoData(Uteis.getDate("01/"+mesAno), Calendar.MONTH, -nrMesesAnteriores);
        List<DadosGameJSON> dados = new ArrayList<DadosGameJSON>();
        StringBuilder sql = new StringBuilder("SELECT identificador, datapesquisainicio, datapesquisafim, valor ");
        sql.append(" FROM dadosgerencialpmg \n");
        sql.append(" WHERE datapesquisainicio BETWEEN ? AND ? \n");
        sql.append(" AND periodicidade = 'MS' AND identificador IN (");
        sql.append("'").append(IdentificadorDadosGerencialEnum.ICV_TOTAL.getSigla()).append("',");
        sql.append("'").append(IdentificadorDadosGerencialEnum.CANCELADOS.getSigla()).append("',");
        sql.append("'").append(IdentificadorDadosGerencialEnum.BV_TOTAL.getSigla()).append("',");
        sql.append("'").append(IdentificadorDadosGerencialEnum.BV_MATRICULAS.getSigla()).append("',");
        sql.append("'").append(IdentificadorDadosGerencialEnum.MATRICULADOS.getSigla()).append("',");
        sql.append("'").append(IdentificadorDadosGerencialEnum.BV_REMATRICULAS.getSigla()).append("',");
        sql.append("'").append(IdentificadorDadosGerencialEnum.REMATRICULADOS.getSigla()).append("',");
        sql.append("'").append(IdentificadorDadosGerencialEnum.BV_RETORNOS.getSigla()).append("',");
        sql.append("'").append(IdentificadorDadosGerencialEnum.RETORNO_MATRICULAS.getSigla()).append("',");
        sql.append("'").append(IdentificadorDadosGerencialEnum.RETORNO_REMATRICULAS.getSigla()).append("',");
        sql.append("'").append(IdentificadorDadosGerencialEnum.RENOVACAO_PREVISAO.getSigla()).append("',");
        sql.append("'").append(IdentificadorDadosGerencialEnum.RENOVACAO_INDICE.getSigla()).append("',");
        sql.append("'").append(IdentificadorDadosGerencialEnum.FATURAMENTO_ATINGIDO.getSigla()).append("',");
        sql.append("'").append(IdentificadorDadosGerencialEnum.DESISTENTES.getSigla()).append("',");
        sql.append("'").append(IdentificadorDadosGerencialEnum.TRANCADOS.getSigla()).append("',");
        sql.append("'").append(IdentificadorDadosGerencialEnum.RE_TRANCADOS.getSigla()).append("',");
        sql.append("'").append(IdentificadorDadosGerencialEnum.RISCO_ALTO.getSigla()).append("',");
        sql.append("'").append(IdentificadorDadosGerencialEnum.RENOVACAO_REALIZADA.getSigla()).append("',");
        sql.append("'").append(IdentificadorDadosGerencialEnum.FATURAMENTO_RECEBIDO.getSigla()).append("',");
        sql.append("'").append(IdentificadorDadosGerencialEnum.DRE_FATURAMENTO_DE_CAIXA.getSigla()).append("',");
        sql.append("'").append(IdentificadorDadosGerencialEnum.DRE_FATURAMENTO.getSigla()).append("',");
        sql.append("'").append(IdentificadorDadosGerencialEnum.DRE_COMPETENCIA_NAO_QUITADA.getSigla()).append("',");
        sql.append("'").append(IdentificadorDadosGerencialEnum.DRE_COMPETENCIA.getSigla()).append("',");
        sql.append("'").append(IdentificadorDadosGerencialEnum.DRE_COMPETENCIA_QUITADA.getSigla()).append("',");
        sql.append("'").append(IdentificadorDadosGerencialEnum.DRE_RECEITA.getSigla()).append("'");
        sql.append(")");
        sql.append(" AND empresa = ? ");
        sql.append(" order by identificador asc, datapesquisainicio desc, datapesquisafim asc ");
        PreparedStatement stm = con.prepareStatement(sql.toString());
        stm.setTimestamp(1, Uteis.getDataHoraJDBC(dataLimite, "00:00:00"));
        stm.setTimestamp(2, Uteis.getDataJDBCTimestamp(dataBase));
        stm.setInt(3, empresa);
        Map<String, DadosGameJSON> mapaJson = new HashMap<String, DadosGameJSON>();
        ResultSet rs = stm.executeQuery();
        while (rs.next()) {
            try{
                String mesAnoDado = Uteis.getDataAplicandoFormatacao(rs.getDate("datapesquisainicio"), "MM/yyyy");
                DadosGameJSON dadosMes = mapaJson.get(mesAnoDado);
                if(dadosMes == null){
                    dadosMes = new DadosGameJSON();
                    dadosMes.setMesAno(mesAnoDado);
                    mapaJson.put(mesAnoDado, dadosMes);
                }
                IdentificadorDadosGerencialEnum indicador = IdentificadorDadosGerencialEnum.getFasePorSigla(rs.getString("identificador"));
                switch (indicador) {
                    case ICV_TOTAL:
                        dadosMes.setIcv(rs.getDouble("valor"));
                        break;
                    case CANCELADOS:
                        dadosMes.setCancelados(new Double(rs.getDouble("valor")).intValue());
                        break;
                    case BV_TOTAL:
                        dadosMes.setVisitas(new Double(rs.getDouble("valor")).intValue());
                        break;
                    case BV_MATRICULAS:
                        dadosMes.setVisitasMatricula(new Double(rs.getDouble("valor")).intValue());
                        break;
                    case MATRICULADOS:
                        dadosMes.setMatriculas(new Double(rs.getDouble("valor")).intValue());
                        break;
                    case BV_REMATRICULAS:
                        dadosMes.setVisitasRematricula(new Double(rs.getDouble("valor")).intValue());
                        break;
                    case REMATRICULADOS:
                        dadosMes.setRematriculas(new Double(rs.getDouble("valor")).intValue());
                        break;
                    case BV_RETORNOS:
                        dadosMes.setVisitasRetornos(new Double(rs.getDouble("valor")).intValue());
                        break;
                    case RETORNO_MATRICULAS:
                        dadosMes.setRetornoMatriculas(new Double(rs.getDouble("valor")).intValue());
                        break;
                    case RETORNO_REMATRICULAS:
                        dadosMes.setRetornoRematriculas(new Double(rs.getDouble("valor")).intValue());
                        break;
                    case RENOVACAO_PREVISAO:
                        dadosMes.setPrevisaoRenovacao(new Double(rs.getDouble("valor")).intValue());
                        break;
                    case RENOVACAO_INDICE:
                        dadosMes.setIndiceRenovacao(rs.getDouble("valor"));
                        break;
                    case RENOVACAO_REALIZADA:
                        dadosMes.setRenovados(new Double(rs.getDouble("valor")).intValue());
                        break;
                    case FATURAMENTO_ATINGIDO:
                        dadosMes.setFaturamento(rs.getDouble("valor"));
                        break;
                    case DESISTENTES:
                        dadosMes.setDesistencias(rs.getDouble("valor"));
                        break;
                    case TRANCADOS:
                        dadosMes.setTrancados(rs.getDouble("valor"));
                        break;
                    case RE_TRANCADOS:
                        dadosMes.setRetornoTrancado(rs.getDouble("valor"));
                        break;
                    case RISCO_ALTO:
                        dadosMes.setGrupoRisco(rs.getDouble("valor"));
                        break;
                    case FATURAMENTO_RECEBIDO:
                        dadosMes.setFechamentoCaixa(rs.getDouble("valor"));
                        break;
                }
            }catch(Exception e){
                
            }
        }
        int mesInicio = Uteis.getMesData(dataLimite);
        int anoInicio = Uteis.getAnoData(dataLimite);
        int mesBase = Uteis.getMesData(dataBase);
        int anoBase = Uteis.getAnoData(dataBase);
        StringBuilder sqlTicketMedio = new StringBuilder("SELECT mes, ano, campobi, valor FROM campobi \n");
        sqlTicketMedio.append("WHERE (mes >= ? and ano = ?) or (mes <= ? and ano = ?) \n");
        sqlTicketMedio.append("AND empresa = ? \n");
        sqlTicketMedio.append("AND campobi IN (");
        sqlTicketMedio.append(CampoBIEnum.CAIXA_POR_COMPETENCIA.getCodigo()).append(",");
        sqlTicketMedio.append(CampoBIEnum.ATIVOS_VENCIDOS_INICIO_MES.getCodigo()).append(",");
        sqlTicketMedio.append(CampoBIEnum.ATIVOS_VENCIDOS_FIM_MES.getCodigo()).append(",");
        sqlTicketMedio.append(CampoBIEnum.BOLSAS_MES.getCodigo()).append(",");
        sqlTicketMedio.append(CampoBIEnum.CAIXA_POR_COMPETENCIA.getCodigo()).append(",");
        sqlTicketMedio.append(CampoBIEnum.CAIXA_POR_RECEITA.getCodigo()).append(")");
        
        PreparedStatement stmTM = con.prepareStatement(sqlTicketMedio.toString());
        stmTM.setInt(1, mesInicio);
        stmTM.setInt(2, anoInicio);
        stmTM.setInt(3, mesBase);
        stmTM.setInt(4, anoBase);
        stmTM.setInt(5, empresa);
        
        ResultSet rsTM = stmTM.executeQuery();
        while(rsTM.next()){
            int mes = rsTM.getInt("mes");
            int ano = rsTM.getInt("ano");
            String mesAnoTM = (mes < 10 ? "0" : "") + mes + "/" + ano;
            DadosGameJSON dadosMes = mapaJson.get(mesAnoTM);
            if (dadosMes == null) {
                dadosMes = new DadosGameJSON();
                dadosMes.setMesAno(mesAnoTM);
                mapaJson.put(mesAnoTM, dadosMes);
            }
            CampoBIEnum campoBI = CampoBIEnum.getFromCodigo(rsTM.getInt("campobi"));
            switch(campoBI){
                case CAIXA_POR_COMPETENCIA:
                    dadosMes.setCompetencia(rsTM.getDouble("valor"));
                    break;
                case CAIXA_POR_RECEITA:
                    dadosMes.setReceita(rsTM.getDouble("valor"));
                    break;
                case ATIVOS_VENCIDOS_INICIO_MES:
                    dadosMes.setAtivosVencidosInicio(rsTM.getInt("valor"));
                    break;
                case ATIVOS_VENCIDOS_FIM_MES:
                    dadosMes.setAtivosVencidosFim(rsTM.getInt("valor"));
                    break;
                case BOLSAS_MES:
                    dadosMes.setBolsas(rsTM.getInt("valor"));
                    break;
            }
        }
        
        StringBuilder sqlDFSintetico = new StringBuilder("SELECT mes, ano, despesa FROM dfsinteticodw \n");
        sqlDFSintetico.append("WHERE (mes >= ? and ano = ?) or (mes <= ? and ano = ?) \n");
        sqlDFSintetico.append("AND empresa = ? \n");
        
        PreparedStatement stmDF = con.prepareStatement(sqlDFSintetico.toString());
        stmDF.setInt(1, mesInicio);
        stmDF.setInt(2, anoInicio);
        stmDF.setInt(3, mesBase);
        stmDF.setInt(4, anoBase);
        stmDF.setInt(5, empresa);
        
        ResultSet rsDF = stmDF.executeQuery();
        while(rsDF.next()){
            int mes = rsDF.getInt("mes");
            int ano = rsDF.getInt("ano");
            String mesAnodf = (mes < 10 ? "0" : "") + mes + "/" + ano;
            DadosGameJSON dadosMes = mapaJson.get(mesAnodf);
            if (dadosMes == null) {
                dadosMes = new DadosGameJSON();
                dadosMes.setMesAno(mesAnodf);
                mapaJson.put(mesAnodf, dadosMes);
            }
            dadosMes.setDespesas(rsDF.getDouble("despesa"));
        }
        dados.addAll(mapaJson.values());
        return dados;
    }
    
   
    @Override
    public JSONArray obterVencimentos(String mesAno, Integer empresa) throws Exception {
        StringBuilder sql = new StringBuilder();
        Date dataBase = Uteis.getDate("01/"+mesAno);
        sql.append(" SELECT to_char(dataprevistarenovar,'MM') as mon, \n");
        sql.append("extract(year from dataprevistarenovar) as yyyy,\n");
        sql.append("count(codigo) as nrcontratosvencendo\n");
        sql.append("FROM contrato \n");
        sql.append("WHERE dataprevistarenovar >= ?\n");
        sql.append("AND empresa >= ?\n");
        sql.append("GROUP BY 1,2\n");
        sql.append("ORDER BY 2,1");

        PreparedStatement stm = con.prepareStatement(sql.toString());
        stm.setTimestamp(1, Uteis.getDataJDBCTimestamp(dataBase));
        stm.setInt(2, empresa);
        
        
        ResultSet rs = stm.executeQuery();
        
        JSONArray json = new JSONArray();
        while(rs.next()){
            JSONObject obj = new JSONObject();
            obj.put("ano", rs.getString("yyyy"));
            obj.put("mes", rs.getString("mon"));
            obj.put("nrcontratosvencendo", rs.getInt("nrcontratosvencendo"));
            json.put(obj);
        }
        return json;
    }  
    
    @Override
    public void incluir(DadosGameVO dados) throws Exception {
        PreparedStatement stm = con.prepareStatement("INSERT INTO dadosgame (empresa, tipo, dados, nome) VALUES (?,?,?,?)");
        stm.setInt(1, dados.getEmpresa());
        stm.setInt(2, dados.getTipo());
        stm.setString(3, dados.getDados());
        stm.setString(4, dados.getNome());
        stm.execute();
    }
    
    @Override
    public void salvarConfigs(Integer empresa, String configs) throws Exception{
        executarConsulta("DELETE FROM dadosgame WHERE empresa = "+empresa
                        +" AND tipo = "+ TipoDadosGameEnum.CONFIGURACOES.ordinal(), con);
        DadosGameVO dadosGame = new DadosGameVO();
        dadosGame.setTipo(TipoDadosGameEnum.CONFIGURACOES.ordinal());
        dadosGame.setEmpresa(empresa);
        dadosGame.setNome("CONFIGS");
        dadosGame.setDados(configs);
        incluir(dadosGame);
    }
   
    @Override
    public String obterConfigs(Integer empresa) throws Exception{
        ResultSet rs = criarConsulta("SELECT dados FROM dadosgame WHERE empresa = "+empresa
                        +" AND tipo = "+ TipoDadosGameEnum.CONFIGURACOES.ordinal(), con);
        return rs.next() ? rs.getString("dados") : "";
    }
    
    
    @Override
    public JSONArray obterFaturamentoDuracao(String mesAno, Integer nrMesesAnteriores, Integer empresa) throws Exception{
        Date dataBase = Uteis.obterUltimoDiaMesUltimaHora(Uteis.getDate("01/"+mesAno));
        Date dataLimite = Uteis.somarCampoData(Uteis.getDate("01/"+mesAno), Calendar.MONTH, -nrMesesAnteriores);
        StringBuilder sql = new StringBuilder();
        sql.append(" select sum(c.valorfinal) as valor, to_char(c.datalancamento,'MM') as mon,\n");
        sql.append("extract(year from c.datalancamento) as yyyy, count(c.codigo) as nrcontratos, cd.numeromeses from contrato c\n");
        sql.append("INNER JOIN contratoduracao cd ON cd.contrato = c.codigo\n");
        sql.append("WHERE c.datalancamento between ? and ? and c.empresa = ?\n");
        sql.append("AND NOT c.bolsa \n");
        sql.append("GROUP BY 2,3,5\n");
        sql.append("ORDER BY 3,2,5");

        PreparedStatement stm = con.prepareStatement(sql.toString());
        stm.setTimestamp(1, Uteis.getDataJDBCTimestamp(dataLimite));
        stm.setTimestamp(2, Uteis.getDataJDBCTimestamp(dataBase));
        stm.setInt(3, empresa);
        
        ResultSet rs = stm.executeQuery();
        
        JSONArray json = new JSONArray();
        while(rs.next()){
            JSONObject obj = new JSONObject();
            obj.put("ano", rs.getString("yyyy"));
            obj.put("mes", rs.getString("mon"));
            obj.put("valor", rs.getDouble("valor"));
            obj.put("nrcontratos", rs.getInt("nrcontratos"));
            obj.put("numeromeses", rs.getInt("numeromeses"));
            json.put(obj);
        }
        return json;
    } 
    
    @Override
    public JSONArray obterMetasCRM(String mesAno, Integer nrMesesAnteriores, Integer empresa) throws Exception{
        Date dataBase = Uteis.obterUltimoDiaMesUltimaHora(Uteis.getDate("01/"+mesAno));
        Date dataLimite = Uteis.somarCampoData(Uteis.getDate("01/"+mesAno), Calendar.MONTH, -nrMesesAnteriores);
        StringBuilder sql = new StringBuilder();
        sql.append("SELECT to_char(a.dia,'MM') as mon, extract(year from a.dia) as yyyy, fm.identificadormeta, sum(meta) AS meta,\n");
        sql.append("sum(metaatingida) as metaatingida FROM fecharmeta fm\n");
        sql.append("INNER JOIN aberturameta a ON fm.aberturameta = a.codigo\n");
        sql.append("WHERE a.dia BETWEEN ? and ? and a.empresa = ?\n");
        sql.append("GROUP BY 1,2,3\n");
        sql.append("ORDER BY 2,1,3");

        PreparedStatement stm = con.prepareStatement(sql.toString());
        stm.setTimestamp(1, Uteis.getDataJDBCTimestamp(dataLimite));
        stm.setTimestamp(2, Uteis.getDataJDBCTimestamp(dataBase));
        stm.setInt(3, empresa);
        
        ResultSet rs = stm.executeQuery();
        
        JSONArray json = new JSONArray();
        while(rs.next()){
            JSONObject obj = new JSONObject();
            obj.put("ano", rs.getString("yyyy"));
            obj.put("mes", rs.getString("mon"));
            obj.put("identificadormeta", rs.getString("identificadormeta"));
            obj.put("meta", rs.getInt("meta"));
            obj.put("metaatingida", rs.getInt("metaatingida"));
            json.put(obj);
        }
        return json;
    }


    @Override
    public List<SimplesJSON> operacoesExcecoes(Integer empresa) throws Exception{
        Date inicio = Uteis.obterPrimeiroDiaMes(Calendario.hoje());
        List<SimplesJSON> bi = new ArrayList<SimplesJSON>();
        String nomeempresa = "";
        ResultSet resultSet = SuperFacadeJDBC.criarConsulta("select nome from empresa where codigo = " + empresa, con);
        if(resultSet.next()){
            nomeempresa = resultSet.getString("nome");
        }
//        INATIVOS_PERIODO_ACESSO
        //qtde de clientes inativos com periodo de acesso
        Cliente clienteDao = new Cliente(con);
        int inativosAcesso = clienteDao.contarClientesInativosComPeriodoAcesso(
                empresa, inicio, Calendario.hoje(), new ArrayList<ColaboradorVO>());
        bi.add(new SimplesJSON("INATIVOS_PERIODO_ACESSO", new Double(inativosAcesso)));
//      OPERACOES_RETROATIVAS
        ContratoOperacao operacaoDao = new ContratoOperacao(con);
        int contratoRetroativas = operacaoDao.contarOperacoesContratoRetroativas(empresa, inicio, Calendario.hoje(), new ArrayList<ColaboradorVO>());
        bi.add(new SimplesJSON("OPERACOES_RETROATIVAS", new Double(contratoRetroativas)));
//                EXCLUSAO_VISITANTES
        Log logDao = new Log(con);
        int qtdExclusao = logDao.contarPorNomeEntidadePorDataAlteracaoPorOperacao(
                "CLIENTE - VISITANTE", Uteis.getDataJDBC(inicio), Uteis.getDataJDBC(Calendario.hoje()),
        "EXCLUSÃO", nomeempresa, true, new ArrayList<ColaboradorVO>(),
                Uteis.NIVELMONTARDADOS_DADOSBASICOS);
        bi.add(new SimplesJSON("EXCLUSAO_VISITANTES", new Double(qtdExclusao)));
//                CONSULTORES_CONTRATOS_ALTERADOS
        int qtdAlteracaoConsultorContratos = logDao.contarPorNomeEntidadePorDataAlteracaoPorOperacao(
                "CONTRATO", Uteis.getDataJDBC(inicio), Uteis.getDataJDBC(Calendario.hoje()),
        "ALTERAÇÃO DO CONTRATO%",nomeempresa, true, new ArrayList<ColaboradorVO>(),
                Uteis.NIVELMONTARDADOS_DADOSBASICOS);
        bi.add(new SimplesJSON("CONSULTORES_CONTRATOS_ALTERADOS", new Double(qtdAlteracaoConsultorContratos)));
//                ESTORNOS_CONTRATOS_ADM
        EstornoObservacao estDao = new EstornoObservacao(con);
        int qtdEstornoAdmin = estDao.contarPorNomeEntidadePorDataAlteracaoPorOperacao(Uteis.getDataJDBC(inicio), Uteis.getDataJDBC(Calendario.hoje()),
        empresa, true, false, new ArrayList<ColaboradorVO>(), Uteis.NIVELMONTARDADOS_DADOSBASICOS);
        bi.add(new SimplesJSON("ESTORNOS_CONTRATOS_ADM", new Double(qtdEstornoAdmin)));
//                ESTORNOS_CONTRATOS_RECORRENCIA
        int qtdEstornoRecorrencia = estDao.contarPorNomeEntidadePorDataAlteracaoPorOperacao(Uteis.getDataJDBC(inicio),
                Uteis.getDataJDBC(Calendario.hoje()),
        empresa, false, true, new ArrayList<ColaboradorVO>(), Uteis.NIVELMONTARDADOS_DADOSBASICOS);
        bi.add(new SimplesJSON("ESTORNOS_CONTRATOS_RECORRENCIA", new Double(qtdEstornoRecorrencia)));
//                ESTORNO_CONTRATOS_USUARIO
        int qtdEstornoUsuario = estDao.contarPorNomeEntidadePorDataAlteracaoPorOperacao(Uteis.getDataJDBC(inicio),
                Uteis.getDataJDBC(Calendario.hoje()),
        empresa, false, false, new ArrayList<ColaboradorVO>(), Uteis.NIVELMONTARDADOS_DADOSBASICOS);
        bi.add(new SimplesJSON("ESTORNO_CONTRATOS_USUARIO", new Double(qtdEstornoUsuario)));
//                PARCELAS_CANCELADAS
        ObservacaoOperacao obsDao = new ObservacaoOperacao(con);
        int qtdParcelasCanceladas = obsDao.contarPorNomeEntidadePorDataAlteracaoPorOperacao(Uteis.getDataJDBC(inicio),
                Uteis.getDataJDBC(Calendario.hoje()),
        empresa, false, TipoObservacaoOperacaoEnum.PARCELA_CANCELADA,new ArrayList<ColaboradorVO>(), Uteis.NIVELMONTARDADOS_DADOSBASICOS);
        bi.add(new SimplesJSON("PARCELAS_CANCELADAS", new Double(qtdParcelasCanceladas)));
//                ESTORNOS_RECIBO
        int qtdEstornoRecibo = logDao.contarPorNomeEntidadeOperacaoPorDataAlteracao("RECIBOPAGAMENTO", "ESTORNO - RECIBO PAGAMENTO",
                nomeempresa, Uteis.getDataJDBC(inicio), Uteis.getDataJDBC(Calendario.hoje()),
        new ArrayList<ColaboradorVO>(), Uteis.NIVELMONTARDADOS_DADOSBASICOS);
        bi.add(new SimplesJSON("ESTORNOS_RECIBO", new Double(qtdEstornoRecibo)));
//                CONTRATOS_DATABASE_ALTERADA
        Contrato contratoDao = new Contrato(con);
        int qtdAlteracoesDataBaseContrato = contratoDao.contarContratoAlteracaoManual(
                inicio, Calendario.hoje(), empresa, new ArrayList<ColaboradorVO>());
        bi.add(new SimplesJSON("CONTRATOS_DATABASE_ALTERADA", new Double(qtdAlteracoesDataBaseContrato)));
//                PAGAMENTOS_DATABASE_ALTERADA
        MovPagamento pagDao = new MovPagamento(con);
        int qtdAlteracoesDataBasePagamento = pagDao.contarPagamentoAlteracaoManual(
                inicio, Calendario.hoje(), empresa, new ArrayList<ColaboradorVO>());
        bi.add(new SimplesJSON("PAGAMENTOS_DATABASE_ALTERADA", new Double(qtdAlteracoesDataBasePagamento)));
//                EDICOES_PAGAMENTO
        int qtdAlteracoesRecibo = logDao.contarPorNomeEntidadePorDataAlteracaoPorOperacao(
                "MOVPAGAMENTO", Uteis.getDataJDBC(inicio), Uteis.getDataJDBC(Calendario.hoje()),
                empresa, new ArrayList<ColaboradorVO>(),
                Uteis.NIVELMONTARDADOS_DADOSBASICOS);
        bi.add(new SimplesJSON("EDICOES_PAGAMENTO", new Double(qtdAlteracoesRecibo)));
//                CONTRATOS_CANCELADOS
        int qtdContratosCancelados = operacaoDao.obterNumeroContratosCancelados(inicio,
                Calendario.hoje(), new ArrayList<ColaboradorVO>(), empresa);
        bi.add(new SimplesJSON("CONTRATOS_CANCELADOS", new Double(qtdContratosCancelados)));
//                CLIENTES_BONUS
        int qtdClientesComBonus = operacaoDao.obterNumeroClientesComBonus(Calendario.hoje(), new ArrayList<ColaboradorVO>(), empresa);
        bi.add(new SimplesJSON("CLIENTES_BONUS", new Double(qtdClientesComBonus)));
//                FREEPASS
        int qtdClientesComFreePass = operacaoDao.obterNumeroClientesComFreePass(Calendario.hoje(), new ArrayList<ColaboradorVO>(), empresa);
        bi.add(new SimplesJSON("FREEPASS", new Double(qtdClientesComFreePass)));
//                GYMPASS
        int qtdClientesComGymPass = operacaoDao.obterNumeroClientesComGymPass(Calendario.hoje(), new ArrayList<ColaboradorVO>(), empresa);
        bi.add(new SimplesJSON("GYMPASS", new Double(qtdClientesComGymPass)));
        // ALUNOS_BOLSA
        //qtde de contratos do tipo bolsa
        int qtdContratosTipoBolsa = operacaoDao.obterNumeroContratosTipoBolsa(Uteis.getDataJDBC(inicio),
                Uteis.getDataJDBC(Calendario.hoje()), new ArrayList<ColaboradorVO>(), empresa);
        bi.add(new SimplesJSON("ALUNOS_BOLSA", new Double(qtdContratosTipoBolsa)));
        return bi;
    }

}

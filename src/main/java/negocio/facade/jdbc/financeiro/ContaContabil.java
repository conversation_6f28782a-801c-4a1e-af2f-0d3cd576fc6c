
package negocio.facade.jdbc.financeiro;

import negocio.comuns.financeiro.ContaContabilVO;
import negocio.comuns.utilitarias.Ordenacao;
import negocio.comuns.utilitarias.Uteis;
import negocio.facade.jdbc.arquitetura.SuperEntidade;
import negocio.interfaces.financeiro.ContaContabilInterfaceFacade;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.sql.Statement;
import java.sql.Types;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

public class ContaContabil extends SuperEntidade implements ContaContabilInterfaceFacade {

    public ContaContabil() throws Exception {
        super();
    }

    public ContaContabil(Connection con) throws Exception {
        super(con);
    }

    @Override
    public void alterar(ContaContabilVO obj) throws Exception {
        ContaContabilVO.validarDados(obj);
        alterar(getIdEntidade());
        StringBuilder sql = new StringBuilder();
        sql.append("UPDATE contaContabil ");
        sql.append("SET descricao = ?, ");
        sql.append("codigoIntegracao = ? ");
        sql.append("WHERE  codigo = ?");
        PreparedStatement sqlAlterar = con.prepareStatement(sql.toString());
        int i = 1;
        sqlAlterar.setString(i++, obj.getDescricao());
        if ((obj.getCodigoIntegracao() != null) && (obj.getCodigoIntegracao() >0 )){
            sqlAlterar.setInt(i++, obj.getCodigoIntegracao());
        }else{
            sqlAlterar.setNull(i++, Types.NULL);
        }
        sqlAlterar.setInt(i++, obj.getCodigo());
        sqlAlterar.execute();
    }

    @Override
    public void excluir(ContaContabilVO obj) throws Exception {
        excluir(getIdEntidade());
        String sql = "DELETE FROM contaContabil WHERE codigo = ?";
        PreparedStatement sqlExcluir = con.prepareStatement(sql);
        sqlExcluir.setInt(1, obj.getCodigo());
        sqlExcluir.execute();
    }

    @Override
    public void incluir(ContaContabilVO obj) throws Exception {
        ContaContabilVO.validarDados(obj);
        incluir(getIdEntidade());
        StringBuilder sql = new StringBuilder();
        sql.append("insert into contaContabil (descricao, codigoIntegracao) ");
        sql.append("values (?, ?)");
        PreparedStatement sqlInserir = con.prepareStatement(sql.toString());
        int i = 0;
        sqlInserir.setString(++i, obj.getDescricao());
        if ((obj.getCodigoIntegracao() != null) && (obj.getCodigoIntegracao() >0 )){
            sqlInserir.setInt(++i, obj.getCodigoIntegracao());
        }else{
            sqlInserir.setNull(++i, Types.NULL);
        }
        sqlInserir.execute();
        obj.setCodigo(obterValorChavePrimariaCodigo());
        obj.setNovoObj(false);
    }

    @Override
    public List<ContaContabilVO> consultarPorDescricao(String descricao) throws Exception{
        StringBuilder sql = new StringBuilder();
        sql.append("SELECT * ");
        sql.append("FROM contaContabil ");
        sql.append(" WHERE remove_acento_upper(descricao) ilike remove_acento_upper('%").append(descricao.replaceAll(" ", "%")).append("%') \n");
        PreparedStatement pst = con.prepareStatement(sql.toString());
        ResultSet tabelaResultado = pst.executeQuery();
        return (montarDadosConsulta(tabelaResultado, Uteis.NIVELMONTARDADOS_DADOSBASICOS));
    }

    @Override
    public List<ContaContabilVO> consultarTodas(int nivelMontarDados) throws Exception {
        String sqlStr = "SELECT * FROM contaContabil";
        Statement stm = con.createStatement();
        ResultSet tabelaResultado = stm.executeQuery(sqlStr);
        return (montarDadosConsulta(tabelaResultado, nivelMontarDados));
    }

    public static List<ContaContabilVO> montarDadosConsulta(ResultSet tabelaResultado, int nivelMontarDados) throws Exception {
        List<ContaContabilVO> lista = new ArrayList<ContaContabilVO>();
        while (tabelaResultado.next()) {
            ContaContabilVO obj = new ContaContabilVO();
            obj = montarDados(tabelaResultado, nivelMontarDados);
            lista.add(obj);
        }
        return lista;
    }


    
    public static ContaContabilVO montarDados(ResultSet dadosSQL, int nivelMontarDados) throws Exception {
        ContaContabilVO obj = new ContaContabilVO();
        obj.setCodigo(new Integer(dadosSQL.getInt("codigo")));
        obj.setDescricao(dadosSQL.getString("descricao"));
        obj.setCodigoIntegracao(dadosSQL.getInt("codigoIntegracao"));
        obj.setNovoObj(false);
        return obj;
    }
    
    @Override
    public ContaContabilVO consultarPorChavePrimaria(Integer codigoPrm, int nivelMontarDados) throws Exception {
        consultar(getIdEntidade(), false);
        String sql = "SELECT * FROM contaContabil WHERE codigo = ?";
        PreparedStatement pst = con.prepareStatement(sql);
        pst.setInt(1, codigoPrm.intValue());
        ResultSet rs = pst.executeQuery();
        if (rs.next()) {
            return (montarDados(rs, nivelMontarDados));
        }
        return null;
    }
    

    private ResultSet getRS() throws SQLException {
        StringBuilder sql = new StringBuilder();
        sql.append(" Select * \n");
        sql.append(" FROM ContaContabil");
        PreparedStatement sqlConsultar = con.prepareStatement(sql.toString());
        ResultSet rs = sqlConsultar.executeQuery();
        return rs;
    }

    public String consultarJSON() throws Exception {
        ResultSet rs = getRS();
        StringBuilder json = new StringBuilder();
        json.append("{\"aaData\":[");
        boolean dados = false;
        while (rs.next()) {
            dados = true;
            json.append("[\"").append(rs.getString("codigo")).append("\",");
            json.append("\"").append(rs.getString("descricao").trim()).append("\",");
            json.append("\"").append(rs.getInt("codigoIntegracao")).append("\"],");
        }
        if (dados) {
            json.deleteCharAt(json.toString().length() - 1);
        }
        json.append("]}");
        return json.toString();
    }

    public List consultarParaImpressao(String filtro, String ordem, String campoOrdenacao) throws SQLException {
        ResultSet rs = getRS();
        List<ContaContabilVO> lista = new ArrayList<ContaContabilVO>();
        while (rs.next()) {

            ContaContabilVO contaContabilVO = new ContaContabilVO();
            String geral = rs.getString("codigo") + rs.getString("descricao") + rs.getInt("codigoIntegracao");
            if (geral.toLowerCase().contains(filtro.toLowerCase())) {
                contaContabilVO.setCodigo(rs.getInt("codigo"));
                contaContabilVO.setDescricao(rs.getString("descricao"));
                contaContabilVO.setCodigoIntegracao(rs.getInt("codigoIntegracao"));
                lista.add(contaContabilVO);
            }
        }
        if (campoOrdenacao.equals("Código")) {
            Ordenacao.ordenarLista(lista, "codigo");
        } else if (campoOrdenacao.equals("Descrição")) {
            Ordenacao.ordenarLista(lista, "descricao");
        } else if (campoOrdenacao.equals("CodigoIntegracao")) {
            Ordenacao.ordenarLista(lista, "codigoIntegracao");
        }
        if (ordem.contains("desc")) {
            Collections.reverse(lista);
        }
        return lista;

    }

    public ContaContabilVO consultarDescricao(String descricao) throws Exception {
        StringBuilder sql = new StringBuilder();
        sql.append("SELECT * ");
        sql.append("FROM contaContabil ");
        sql.append(" WHERE remove_acento_upper(descricao) ilike remove_acento_upper('").append(descricao.replaceAll(" ", "%")).append("'); \n");
        PreparedStatement pst = con.prepareStatement(sql.toString());
        ResultSet tabelaResultado = pst.executeQuery();
        if (tabelaResultado.next()) {
            return (montarDados(tabelaResultado, Uteis.NIVELMONTARDADOS_DADOSBASICOS));
        } else {
            return new ContaContabilVO();
        }
    }
}

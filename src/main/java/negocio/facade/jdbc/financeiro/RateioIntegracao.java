/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package negocio.facade.jdbc.financeiro;

import br.com.pactosolucoes.comuns.util.Formatador;
import br.com.pactosolucoes.enumeradores.EntidadeRateioEnum;
import negocio.comuns.financeiro.CategoriaRateioTO;
import negocio.comuns.financeiro.ModalidadeRateioTO;
import negocio.comuns.financeiro.ProdutoRateioTO;
import negocio.comuns.financeiro.RateioIntegracaoTO;
import negocio.comuns.financeiro.enumerador.TipoES;
import negocio.comuns.utilitarias.UteisValidacao;
import negocio.facade.jdbc.arquitetura.SuperEntidade;
import negocio.facade.jdbc.arquitetura.SuperFacadeJDBC;
import negocio.interfaces.financeiro.RateioIntegracaoInterfaceFacade;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;

public class RateioIntegracao extends SuperEntidade implements RateioIntegracaoInterfaceFacade {

    public RateioIntegracao() throws Exception {
        super();
    }

    public RateioIntegracao(Connection con) throws Exception {
        super(con);
    }

    public void incluir(List<RateioIntegracaoTO> lista, Integer idEntidade, Integer tipo) throws SQLException, Exception {
        for (RateioIntegracaoTO obj : lista) {
            // salvar o rateio
            if (tipo.equals(EntidadeRateioEnum.CATEGORIA_PRODUTO.getTipo())) {
            	obj.setCodigoCategoria(idEntidade);
            } else if (tipo.equals(EntidadeRateioEnum.PRODUTO.getTipo())) {
            	obj.setCodigoProduto(idEntidade);
            } else if (tipo.equals(EntidadeRateioEnum.MODALIDADE.getTipo())) {
            	obj.setCodigoModalidade(idEntidade);
            } else if (tipo.equals(EntidadeRateioEnum.AMBIENTE.getTipo())) {
            	obj.setCodigoAmbiente(idEntidade);
            } else if (tipo.equals(EntidadeRateioEnum.AMBIENTES.getTipo())) {
            	obj.setRateioAmbientes(true);
            } else if (tipo.equals(EntidadeRateioEnum.PRODUTO_CE.getTipo())) {
            	obj.setCodigoProdutoCE(idEntidade);
            } else if (tipo.equals(EntidadeRateioEnum.SERVICO.getTipo())) {
            	obj.setCodigoServico(idEntidade);
            } else if (tipo.equals(EntidadeRateioEnum.SERVICOS.getTipo())) {
            	obj.setRateioServicos(true);
            } else if (tipo.equals(EntidadeRateioEnum.BENS_CONSUMO.getTipo())
            		|| tipo.equals(EntidadeRateioEnum.UTENSILIOS.getTipo())
            		|| tipo.equals(EntidadeRateioEnum.BRINQUEDOS.getTipo())) {
            	obj.setCodigoTipoProdutoCE(idEntidade);
            }else if (tipo.equals(EntidadeRateioEnum.CREDITO.getTipo())) {
            	obj.setCredito(true);
            }else if (tipo.equals(EntidadeRateioEnum.DEVOLUCAO_CREDITO.getTipo())) {
            	obj.setDevolucaoCredito(true);
            }else if (tipo.equals(EntidadeRateioEnum.GERAL_MODALIDADES.getTipo())) {
            	obj.setGeralModalidade(true);
            }
            incluir(obj);
            
        }
    }

    public void incluir(RateioIntegracaoTO obj) throws Exception{
        StringBuilder sql = new StringBuilder();
            /*
             * INSERT INTO rateiointegracao( codigo, produto, modalidade,
             * categoriaproduto, planoconta, centrocusto, percentagem,
             * descricao) VALUES (?, ?, ?, ?, ?, ?, ?, ?);
             */
        sql.append("INSERT INTO rateiointegracao( "
                + " produto, modalidade, categoriaproduto, ambiente, produtoce, servico, servicos, ambientes, planoconta, "
                + "centrocusto, percentagem, planocontarateio, tipoproduto, credito, devolucaocredito, empresa, geralmodalidade) "
                + "VALUES (?, ?, ?, ?, ?, ?,?,?,?,?,?,?, ?, ?, ?, ?, ?)");

            // Prepara a conexão
        try (PreparedStatement dc = con.prepareStatement(sql.toString())) {
            int i = 0;
            resolveFKNull(dc, ++i, obj.getCodigoProduto());
            resolveFKNull(dc, ++i, obj.getCodigoModalidade());
            resolveFKNull(dc, ++i, obj.getCodigoCategoria());
            resolveFKNull(dc, ++i, obj.getCodigoAmbiente());
            resolveFKNull(dc, ++i, obj.getCodigoProdutoCE());
            resolveFKNull(dc, ++i, obj.getCodigoServico());

            dc.setBoolean(++i, obj.getRateioServicos());
            dc.setBoolean(++i, obj.getRateioAmbientes());

            resolveFKNull(dc, ++i, obj.getCodigoPlanoContas());
            resolveFKNull(dc, ++i, obj.getCodigoCentroCustos());

            dc.setDouble(++i, obj.getPercentagem());

            resolveFKNull(dc, ++i, obj.getPlanoContasRateio());

            resolveFKNull(dc, ++i, obj.getCodigoTipoProdutoCE());

            dc.setBoolean(++i, obj.isCredito());
            dc.setBoolean(++i, obj.isDevolucaoCredito());
            resolveIntegerNull(dc, ++i, obj.getEmpresa());
            dc.setBoolean(++i, obj.isGeralModalidade());
            // Executa a consulta
            dc.execute();
        }
    }

    public List<RateioIntegracaoTO> consultarTodos(int nivelMontarDados)throws Exception{
        return consultar(null,null);
    }

    public List<CategoriaRateioTO> consultarRateiosCategorias() throws SQLException, Exception {

        // consultar as categorias e seus rateios
        Map<Integer, Map<Integer, Object>> categorias;
        try (ResultSet rs = criarConsulta("select "
                + "ct.codigo as codigocategoria, "
                + "ct.descricao as descricaocategoria, "
                + "rt.codigo as codigorateio, "
                + "rt.percentagem as percentagemrateio, "
                + "rt.planoconta as planocontasrateio, "
                + "rt.centrocusto as centrocustosrateio, "
                + "pl.codigoplanocontas as codigoplano, pl.nome as nomeplano, "
                + "cc.codigocentrocustos as codigocentro, cc.nome as nomecentro "
                + "from rateiointegracao rt "
                + "right join categoriaproduto ct on rt.categoriaProduto = ct.codigo "
                + "left join planoconta pl on rt.planoconta = pl.codigo "
                + "left join centrocusto cc on rt.centrocusto = cc.codigo "
                + "order by codigocategoria;", con)) {

            // Mapa para guardar as categorias
            // <Código da Categoria, Rateios e Propriedades da categoria>
            // Rateios e Propriedades da categoria - Map<Integer, Object>
            // chave interger - 1:informações da categoria, 2: rateios da
            // categoria(List<RateioTO>), 3: rateios de produtos, 4: outros produtos
            categorias = new HashMap<Integer, Map<Integer, Object>>();

            while (rs.next()) {
                int codigo = rs.getInt("codigocategoria");
                Map<Integer, Object> map = categorias.get(codigo);
                if (map == null) {
                    map = new HashMap<Integer, Object>();
                    // insere a categoria
                    String descCategoria = rs.getString("descricaocategoria");
                    map.put(1, descCategoria);

                    double percRateio = rs.getDouble("percentagemrateio");
                    if (!UteisValidacao.emptyNumber(percRateio)) {
                        // preenche um rateio para a categoria
                        RateioIntegracaoTO rateio = new RateioIntegracaoTO();
                        montarDados(rs, percRateio, rateio);
                        rateio.setCodigoCategoria(codigo);
                        // adiciona o rateio a lista
                        List<RateioIntegracaoTO> rateios = (List<RateioIntegracaoTO>) map.get(2);
                        if (rateios == null) {
                            rateios = new ArrayList<RateioIntegracaoTO>();
                            map.put(2, rateios);
                        }
                        rateios.add(rateio);
                    }
                } else {
                    // adiciona um rateio para esta categoria
                    double percRateio = rs.getDouble("percentagemrateio");
                    if (!UteisValidacao.emptyNumber(percRateio)) {
                        // preenche um rateio para a categoria
                        RateioIntegracaoTO rateio = new RateioIntegracaoTO();

                        montarDados(rs, percRateio, rateio);
                        rateio.setCodigoCategoria(codigo);
                        // adiciona o rateio a lista
                        List<RateioIntegracaoTO> rateios = (List<RateioIntegracaoTO>) map.get(2);
                        if (rateios == null) {
                            rateios = new ArrayList<RateioIntegracaoTO>();
                            map.put(2, rateios);
                        }
                        rateios.add(rateio);
                    }
                }
                categorias.put(codigo, map);
            }
        }
        // consultar os produtos e seus rateios
        try (ResultSet rsProd = criarConsulta("select "
                + "pd.codigo as codigoproduto, pd.descricao as descricaoproduto, "
                + "ct.codigo as codigocategoria, ct.descricao as descricaocategoria, "
                + "rt.percentagem as percentagemrateio, rt.codigo as codigorateio, "
                + "rt.produto as codigoprodutorateio, "
                + "rt.centrocusto as centrocustosrateio, rt.planoconta as planocontasrateio, "
                + "pl.codigoplanocontas as codigoplano, pl.nome as nomeplano, "
                + "cc.codigocentrocustos as codigocentro, cc.nome as nomecentro "
                + "from categoriaproduto ct, rateiointegracao rt "
                + "right join produto pd on rt.produto = pd.codigo "
                + "left join planoconta pl on rt.planoconta = pl.codigo "
                + "left join centrocusto cc on rt.centrocusto = cc.codigo "
                + "where pd.categoriaproduto = ct.codigo order by codigocategoria, codigoprodutorateio", con)) {

            int codigoProdutoAnterior = 0;
            while (rsProd.next()) {
                double percentagem = rsProd.getDouble("percentagemrateio");
                int codgCategoria = rsProd.getInt("codigocategoria");
                Map<Integer, Object> map = categorias.get(codgCategoria);

                // Mapa para guardar as categorias
                // <Código da Categoria, Rateios e Propriedades da categoria>
                // Rateios e Propriedades da categoria - Map<Integer, Object>
                // chave interger - 1:informações da categoria, 2: rateios da
                // categoria(List<RateioTO>), 3: rateios de produtos, 4: outros
                // produtos
                int codigoRateioAtual = rsProd.getInt("codigoproduto");
                if (!UteisValidacao.emptyNumber(percentagem)) {
                    // se houver percentagem é por que se trata de um produto com
                    // rateio.
                    if (map.get(3) == null) {
                        Map<Integer, ProdutoRateioTO> produtosRateios = new HashMap<Integer, ProdutoRateioTO>();
                        map.put(3, produtosRateios);
                    }
                    Map<Integer, ProdutoRateioTO> produtosRateios = (Map<Integer, ProdutoRateioTO>) map.get(3);
                    if (codigoProdutoAnterior == codigoRateioAtual) {
                        ProdutoRateioTO produtoRateio = produtosRateios.get(codigoRateioAtual);

                        RateioIntegracaoTO rateio = new RateioIntegracaoTO();
                        montarDados(rsProd, percentagem, rateio);
                        rateio.setCodigoProduto(rsProd.getInt("codigoproduto"));
                        rateio.setNomeProduto(rsProd.getString("descricaoproduto"));

                        List<RateioIntegracaoTO> rateios = produtoRateio.getRateios();
                        rateios.add(rateio);
                    } else {
                        // se trata de um novo produto com rateios
                        // preenche um rateio para um produto especívido
                        ProdutoRateioTO produtoRateio = new ProdutoRateioTO();
                        produtoRateio.setCodigo(codigoRateioAtual);
                        produtoRateio.setNome(rsProd.getString("descricaoproduto"));

                        RateioIntegracaoTO rateio = new RateioIntegracaoTO();
                        montarDados(rsProd, percentagem, rateio);
                        rateio.setCodigoProduto(rsProd.getInt("codigoproduto"));
                        rateio.setNomeProduto(rsProd.getString("descricaoproduto"));

                        List<RateioIntegracaoTO> rateios = produtoRateio.getRateios();
                        rateios.add(rateio);

                        produtosRateios.put(produtoRateio.getCodigo(),
                                produtoRateio);
                    }

                    //
                    // RateioIntegracaoTO rateio = new RateioIntegracaoTO();
                    // preencherRateio(rsProd, percentagem, rateio);
                    // rateio.setCodigoProduto(rsProd.getInt("codigoproduto"));
                    // rateio.setNomeProduto(rsProd.getString("descricaoproduto"));
                    // // adiciona o rateio a lista

                } else {
                    RateioIntegracaoTO rateio = new RateioIntegracaoTO();
                    montarDados(rsProd, percentagem, rateio);
                    rateio.setCodigoProduto(codigoRateioAtual);
                    rateio.setNomeProduto(rsProd.getString("descricaoproduto"));
                    // adiciona o rateio a lista
                    if (map.get(4) == null) {
                        List<RateioIntegracaoTO> rateios = new ArrayList<RateioIntegracaoTO>();
                        map.put(4, rateios);
                    }
                    List<RateioIntegracaoTO> rateios = (List<RateioIntegracaoTO>) map.get(4);
                    rateios.add(rateio);
                }
                codigoProdutoAnterior = codigoRateioAtual;
            }
        }

        Set<Integer> keySet = categorias.keySet();
        List<CategoriaRateioTO> lista = new ArrayList<CategoriaRateioTO>();
        for (Integer key : keySet) {
            CategoriaRateioTO cat = new CategoriaRateioTO();
            Map<Integer, Object> map = categorias.get(key);
            String nome = map.get(1).toString();
            cat.setCodigo(key);
            cat.setNome(nome);
            if (map.get(2) != null) {
                cat.setRateios((List<RateioIntegracaoTO>) map.get(2));
            }
            if (map.get(3) != null) {
                List<ProdutoRateioTO> listaRateiosProdutos = new ArrayList<ProdutoRateioTO>();
                Map<Integer, ProdutoRateioTO> produtosRateios = (Map<Integer, ProdutoRateioTO>) map.get(3);
                for (Integer key2 : produtosRateios.keySet()) {
                    ProdutoRateioTO obj = produtosRateios.get(key2);
                    listaRateiosProdutos.add(obj);
                }
                cat.setProdutosRateios(listaRateiosProdutos);
            }
            if (map.get(4) != null) {
                cat.setProdutos((List<RateioIntegracaoTO>) map.get(4));
            }
            lista.add(cat);
        }
        return lista;
    }

    public void excluir(Integer entidade, Integer tipo) throws Exception {
        // excluir(getIdEntidade());
        String sql = "DELETE FROM rateiointegracao WHERE ";

        if (tipo.equals(EntidadeRateioEnum.CATEGORIA_PRODUTO.getTipo())) {
            sql += " categoriaproduto = ?";
        } else if (tipo.equals(EntidadeRateioEnum.PRODUTO.getTipo())) {
            sql += " produto = ?";
        } else if (tipo.equals(EntidadeRateioEnum.MODALIDADE.getTipo())) {
            sql += " modalidade = ?";
        } else if (tipo.equals(EntidadeRateioEnum.AMBIENTE.getTipo())) {
        	sql += " ambiente = ?";
        } else if (tipo.equals(EntidadeRateioEnum.AMBIENTES.getTipo())) {
        	sql += " ambientes is true";
        } else if (tipo.equals(EntidadeRateioEnum.PRODUTO_CE.getTipo())) {
        	sql += " produtoce = ?";
        } else if (tipo.equals(EntidadeRateioEnum.SERVICO.getTipo())) {
        	sql += " servico = ?";
        } else if (tipo.equals(EntidadeRateioEnum.SERVICOS.getTipo())) {
        	sql += " servicos is true";
        } else if (tipo.equals(EntidadeRateioEnum.BENS_CONSUMO.getTipo()) 
        		|| tipo.equals(EntidadeRateioEnum.BRINQUEDOS.getTipo())
        		|| tipo.equals(EntidadeRateioEnum.UTENSILIOS.getTipo())) {
        	sql += " tipoproduto = ?";
        } else if (tipo.equals(EntidadeRateioEnum.DEVOLUCAO_CREDITO.getTipo())) {
        	sql += " devolucaocredito is true";
        } else if (tipo.equals(EntidadeRateioEnum.CREDITO.getTipo())) {
        	sql += " credito is true";
        }else if (tipo.equals(EntidadeRateioEnum.GERAL_MODALIDADES.getTipo())) {
        	sql += " geralmodalidade is true";
        }

        try (PreparedStatement sqlExcluir = con.prepareStatement(sql)) {
            if (!tipo.equals(EntidadeRateioEnum.SERVICOS.getTipo())
                    && !tipo.equals(EntidadeRateioEnum.AMBIENTES.getTipo())
                    && !tipo.equals(EntidadeRateioEnum.DEVOLUCAO_CREDITO.getTipo())
                    && !tipo.equals(EntidadeRateioEnum.CREDITO.getTipo())
                    && !tipo.equals(EntidadeRateioEnum.GERAL_MODALIDADES.getTipo())) {
                sqlExcluir.setInt(1, entidade);
            }
            sqlExcluir.execute();
        }
    }
    
    
    public void excluirRateiosCentroCusto(Integer planoConta) throws Exception {
        String sql = "DELETE FROM rateiointegracao WHERE planocontarateio = ?";
        try (PreparedStatement sqlExcluir = con.prepareStatement(sql)) {
            sqlExcluir.setInt(1, planoConta);
            sqlExcluir.execute();
        }
    }

    public List<RateioIntegracaoTO> consultar(EntidadeRateioEnum entidade, Integer valor) throws Exception {
        List<RateioIntegracaoTO> rateios = new ArrayList<RateioIntegracaoTO>();
        // consultar as categorias e seus rateios
        String sql = "select "
                + "rt.empresa as empresarateio,"
                + "emp.nome as nomeempresa,"
                + "rt.codigo as codigorateio, "
                + "rt.credito, rt.devolucaocredito, "
                + "rt.percentagem as percentagemrateio, "
                + "rt.planoconta as planocontasrateio, "
                + "rt.centrocusto as centrocustosrateio, "
                + "rt.modalidade as modalidaderateio, "
                + "rt.servico as servicorateio, "
                + "rt.ambiente as ambienterateio, "
                + "rt.geralmodalidade, "
                + "rt.produtoce as produtocerateio, "
                + "rt.tipoproduto as tipoprodutorateio, "
                + "rt.ambientes as ambientes, "
                + "rt.servicos as servicos, "
                + "rt.categoriaproduto as codigocategoria, "
                + "rt.produto as produtorateio, "
                + "pl.nome as nomeplano, "
                + "pl.tipoes, "
                + "rt.planoconta, "
                + "rt.centrocusto, "
                + "cc.nome as nomecentro, "
                + "pl.codigoplanocontas as codigoplano, pl.nome as nomeplano, "
                + "cc.codigocentrocustos as codigocentro, cc.nome as nomecentro "
                + "from rateiointegracao rt "
                + "left join planoconta pl on rt.planoconta = pl.codigo "
                + "left join centrocusto cc on rt.centrocusto = cc.codigo "
                + "left join empresa emp on rt.empresa = emp.codigo "
                + "where percentagem is not null ";
        if(entidade != null){
        	switch (entidade) {
    		case AMBIENTE:
    			sql += "and rt.ambiente = " + valor;
    			break;
    		case AMBIENTES:
    			sql += "and rt.ambientes is true ";
    			break;
    		case CATEGORIA_PRODUTO:
    			sql += "and rt.categoriaproduto = " + valor;
    			break;
    		case GERAL_MODALIDADES:
    			sql += "and rt.geralmodalidade is true";
    			break;
    		case MODALIDADE:
    			sql += "and rt.modalidade = " + valor;
    			break;
    		case PRODUTO:
    			sql += "and rt.produto = " + valor;
    			break;
    		case PRODUTO_CE:
    			sql += "and rt.produtoce = " + valor;
    			break;
    		case SERVICO:
    			sql += "and rt.servico = " + valor;
    			break;
    		case SERVICOS:
    			sql += "and rt.servicos is true ";
    			break;
    		case BENS_CONSUMO:
    			sql += "and rt.tipoproduto = " + valor;
    			break;
    		case UTENSILIOS:
    			sql += "and rt.tipoproduto = " + valor;
    			break;
    		case BRINQUEDOS:
    			sql += "and rt.tipoproduto = " + valor;
    			break;
                case CREDITO:
                        sql += "and rt.credito is true ";
                    break;
                case DEVOLUCAO_CREDITO:
                        sql += "and rt.devolucaocredito is true ";
                    break;
    		}
        }
        
        sql += " order by codigocategoria;";
        try (ResultSet rs = criarConsulta(sql, con)) {

            while (rs.next()) {
                RateioIntegracaoTO rateio = new RateioIntegracaoTO();
                rateio.setEmpresa(rs.getInt("empresarateio"));
                rateio.setNomeEmpresa(rs.getString("nomeempresa"));
                rateio.setCodigo(rs.getInt("codigorateio"));
                rateio.setCodigoCategoria(rs.getInt("codigocategoria"));
                rateio.setCodigoCentro(rs.getString("codigocentro"));
                rateio.setCodigoCentroCustos(rs.getInt("centrocustosrateio"));
                rateio.setCodigoPlano(rs.getString("codigoplano"));
                rateio.setCodigoPlanoContas(rs.getInt("planocontasrateio"));
                rateio.setNomeCentro(rs.getString("nomecentro"));
                rateio.setNomePlano(rs.getString("nomeplano"));
                rateio.setCodigoModalidade(rs.getInt("modalidaderateio"));
                double perc = rs.getDouble("percentagemrateio");
                rateio.setPercentagem(perc);
                String percDesc = Formatador.formatarValorPercentual(perc * 10);
                rateio.setPercentagemDesc(percDesc);

                rateio.setCodigoAmbiente(rs.getInt("ambienterateio"));
                rateio.setCodigoProdutoCE(rs.getInt("produtocerateio"));
                rateio.setCodigoServico(rs.getInt("servicorateio"));
                rateio.setCodigoTipoProdutoCE(rs.getInt("tipoprodutorateio"));
                rateio.setRateioAmbientes(rs.getBoolean("ambientes"));
                rateio.setRateioServicos(rs.getBoolean("servicos"));
                rateio.setCodigoProduto(rs.getInt("produtorateio"));
                try {
                    rateio.setPlanoConta(rs.getInt("planoConta"));
                } catch (Exception ignore) {}
                try {
                    rateio.setCentroCusto(rs.getInt("centroCusto"));
                } catch (Exception ignore) {}
                try {
                    rateio.setTipoES(TipoES.getTipoPadrao(rs.getInt("tipoes")));
                } catch (Exception ignore) {}
                try {
                    rateio.setGeralModalidade(rs.getBoolean("geralmodalidade"));
                } catch (Exception e) {
                    rateio.setGeralModalidade(false);
                }
                try {
                    rateio.setCredito(rs.getBoolean("credito"));
                } catch (Exception e) {
                    rateio.setCredito(false);
                }
                try {
                    rateio.setDevolucaoCredito(rs.getBoolean("devolucaocredito"));
                } catch (Exception e) {
                    rateio.setDevolucaoCredito(false);
                }
                try {
                    rateio.setEmpresa(rs.getInt("empresa"));
                } catch (Exception e) {
                }
                rateios.add(rateio);
            }
        }
        return rateios;
    }

    public List<RateioIntegracaoTO> consultarPlanoContaRateio(Integer planoconta) throws SQLException, Exception {
        List<RateioIntegracaoTO> rateios = new ArrayList<RateioIntegracaoTO>();
        // consultar os rateios de centro de custos para planos de conta do campo planocontarateio
        String sql = "select "
                + "rt.codigo as codigorateio, "
                + "rt.percentagem as percentagemrateio, "
                + "rt.centrocusto as centrocustosrateio, "
                + "cc.nome as nomecentro, "
                + "cc.codigocentrocustos as codigocentro, cc.nome as nomecentro "
                + "from rateiointegracao rt "
                + "left join planoconta pl on rt.planocontarateio = pl.codigo "
                + "left join centrocusto cc on rt.centrocusto = cc.codigo "
                + "where percentagem is not null and rt.planocontarateio =  " + planoconta;
        try (ResultSet rs = criarConsulta(sql, con)) {

            while (rs.next()) {
                RateioIntegracaoTO rateio = new RateioIntegracaoTO();
                rateio.setCodigo(rs.getInt("codigorateio"));
                rateio.setCodigoCentro(rs.getString("codigocentro"));
                rateio.setCodigoCentroCustos(rs.getInt("centrocustosrateio"));
                rateio.setNomeCentro(rs.getString("nomecentro"));
                double perc = rs.getDouble("percentagemrateio");
                rateio.setCodigoPlano("");
                rateio.setCodigoPlanoContas(0);
                rateio.setNomePlano("");
                rateio.setPercentagem(perc);
                String percDesc = Formatador.formatarValorPercentual(perc * 10);
                rateio.setPercentagemDesc(percDesc);
                rateios.add(rateio);
            }
        }
        return rateios;
    }

    private void montarDados(ResultSet rs, double percentagem, RateioIntegracaoTO rateio) throws SQLException {
        rateio.setPercentagem(percentagem);
        String percDesc = Formatador.formatarValorPercentual(percentagem * 10);
        rateio.setPercentagemDesc(percDesc);
        rateio.setCodigo(rs.getInt("codigorateio"));
        rateio.setCodigoCentroCustos(rs.getInt("centrocustosrateio"));
        rateio.setCodigoPlanoContas(rs.getInt("planocontasrateio"));
        rateio.setNomePlano(rs.getString("nomeplano"));
        rateio.setCodigoPlano(rs.getString("codigoplano"));
        rateio.setNomeCentro(rs.getString("nomecentro"));
        rateio.setCodigoCentro(rs.getString("codigocentro"));
    }

    public List<ModalidadeRateioTO> consultar() throws SQLException, Exception {

        // consultar as categorias e seus rateios
        Map<Integer, ModalidadeRateioTO> mapa;
        try (ResultSet rs = criarConsulta("select "
                + "md.codigo as codigomodalidade, "
                + "md.nome as descricaomodalidade, "
                + "rt.codigo as codigorateio, "
                + "rt.percentagem as percentagemrateio, "
                + "rt.planoconta as planocontasrateio, "
                + "rt.centrocusto as centrocustosrateio, "
                + "pl.codigoplanocontas as codigoplano, pl.nome as nomeplano, "
                + "cc.codigocentrocustos as codigocentro, cc.nome as nomecentro "
                + "from rateiointegracao rt "
                + "right join modalidade md on rt.modalidade = md.codigo "
                + "left join planoconta pl on rt.planoconta = pl.codigo "
                + "left join centrocusto cc on rt.centrocusto = cc.codigo "
                + "order by codigomodalidade", con)) {

            int codigoModalidadeAnterior = 0;
            mapa = new HashMap<Integer, ModalidadeRateioTO>();
            while (rs.next()) {
                int codigoModalidadeAtual = rs.getInt("codigomodalidade");
                if (codigoModalidadeAnterior == codigoModalidadeAtual) {
                    ModalidadeRateioTO modalidadeRateioTO = mapa.get(codigoModalidadeAtual);
                    RateioIntegracaoTO rateio = new RateioIntegracaoTO();
                    double percentagem = rs.getDouble("percentagemrateio");
                    montarDados(rs, percentagem, rateio);
                    modalidadeRateioTO.getRateios().add(rateio);
                } else {
                    ModalidadeRateioTO modalidadeRateioTO = new ModalidadeRateioTO();
                    modalidadeRateioTO.setCodigo(codigoModalidadeAtual);
                    modalidadeRateioTO.setNome(rs.getString("descricaomodalidade"));

                    double percentagem = rs.getDouble("percentagemrateio");
                    if (percentagem != 0.0d) {
                        RateioIntegracaoTO rateio = new RateioIntegracaoTO();
                        montarDados(rs, percentagem, rateio);
                        modalidadeRateioTO.getRateios().add(rateio);
                    }
                    mapa.put(codigoModalidadeAtual, modalidadeRateioTO);
                }
                codigoModalidadeAnterior = codigoModalidadeAtual;
            }
        }
        Set<Integer> keySet = mapa.keySet();
        List<ModalidadeRateioTO> lista = new ArrayList<ModalidadeRateioTO>();
        for (Integer key : keySet) {
            lista.add(mapa.get(key));
        }
        return lista;
    }

    /**
     * Responsável por obter codigos das Modalides Com Rateio
     * <AUTHOR>
     * 15/08/2011
     */
    public List<Integer> obterModalidesComRateio() throws Exception {
        List<Integer> codigos = new ArrayList<Integer>();
        String sql = "SELECT distinct(modalidade) from rateiointegracao";
        try (ResultSet consulta = criarConsulta(sql, con)) {
            while (consulta.next()) {
                codigos.add(consulta.getInt("modalidade"));
            }
        }
        return codigos;
    }

    /**
     * Responsável por obter codigos dos produtos Com Rateio
     * <AUTHOR>
     * 15/08/2011
     */
    public List<Integer> obterProdutosComRateio() throws Exception {
        List<Integer> codigos = new ArrayList<Integer>();
        String sql = "SELECT DISTINCT(rateiointegracao.produto) FROM rateiointegracao"
                   + " LEFT JOIN produto ON rateiointegracao.produto = produto.codigo"
                   + " WHERE produto.desativado = FALSE";
        try (ResultSet consulta = criarConsulta(sql, con)) {
            while (consulta.next()) {
                codigos.add(consulta.getInt("produto"));
            }
        }
        return codigos;
    }
    
    /**
     * <AUTHOR>
     * 21/11/2011
     */
    @Override
    public Boolean verificarExistenciaPlano(Integer codigoPlano) throws Exception{
    	return criarConsulta("SELECT * FROM rateiointegracao WHERE planoconta = "+codigoPlano, con).next();
    }

    @Override
    public List<RateioIntegracaoTO> obterRateiosDevolucaoDinheiro(String tipo) throws Exception{
        List<RateioIntegracaoTO> lista;
        try (ResultSet rs = criarConsulta("SELECT * FROM rateiointegracao WHERE produto IN (SELECT codigo FROM produto WHERE tipoproduto = '" + tipo + "')", con)) {
            lista = new ArrayList<RateioIntegracaoTO>();
            while (rs.next()) {
                lista.add(montarDadosRateio(rs));
            }
        }
        if(lista.isEmpty()){
            try (ResultSet rsCategoria = criarConsulta("SELECT * FROM rateiointegracao WHERE categoriaproduto"
                    + " IN (select codigo from categoriaproduto where descricao ilike 'DEVOLUÇÃO DINHEIRO')", con)) {
                while (rsCategoria.next()) {
                    lista.add(montarDadosRateio(rsCategoria));
                }
            }
        }
        return lista;
    }

    private static RateioIntegracaoTO montarDadosRateio(ResultSet rs) throws Exception{
        RateioIntegracaoTO rateio = new RateioIntegracaoTO();
        rateio.setCodigoProduto(rs.getInt("produto"));
        rateio.setCodigoPlanoContas(rs.getInt("planoconta"));
        rateio.setCodigoCentroCustos(rs.getInt("centrocusto"));
        rateio.setPercentagem(rs.getDouble("percentagem"));
        return rateio;
    }
}

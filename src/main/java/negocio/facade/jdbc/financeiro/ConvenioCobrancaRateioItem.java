package negocio.facade.jdbc.financeiro;

import negocio.comuns.financeiro.ConvenioCobrancaRateioItemVO;
import negocio.comuns.financeiro.ConvenioCobrancaRateioVO;
import negocio.comuns.utilitarias.Calendario;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisValidacao;
import negocio.facade.jdbc.arquitetura.SuperEntidade;
import negocio.interfaces.financeiro.ConvenioCobrancaRateioItemInterfaceFacade;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.Statement;
import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

/**
 * Created with IntelliJ IDEA.
 * User: Luiz Felipe
 * Date: 15/05/2020
 */
public class ConvenioCobrancaRateioItem extends SuperEntidade implements ConvenioCobrancaRateioItemInterfaceFacade {

    public ConvenioCobrancaRateioItem() throws Exception {
        super();
    }

    public ConvenioCobrancaRateioItem(Connection con) throws Exception {
        super(con);
    }

    public void gravar(ConvenioCobrancaRateioVO obj) throws Exception {

        List<ConvenioCobrancaRateioItemVO> listaAnterior = consultarPorConvenioCobrancaRateio(obj.getCodigo());

        Set<Integer> atuais = new HashSet<>();
        for (ConvenioCobrancaRateioItemVO itemVO : obj.getItens()) {
            itemVO.setConvenioCobrancaRateioVO(obj);
            if (UteisValidacao.emptyNumber(itemVO.getCodigo())) {
                incluir(itemVO);
            } else {
                alterar(itemVO);
            }
            atuais.add(itemVO.getCodigo());
        }
        excluirItensRemovidos(listaAnterior, atuais);
    }

    private void excluirItensRemovidos(List<ConvenioCobrancaRateioItemVO> listaAnterior, Set<Integer> atuais) throws Exception {
        for (ConvenioCobrancaRateioItemVO itemVO : listaAnterior) {
            if (!atuais.contains(itemVO.getCodigo())) {
                excluir(itemVO);
            }
        }
    }

    @Override
    public void incluir(ConvenioCobrancaRateioItemVO obj) throws Exception {
        String insert = "INSERT INTO conveniocobrancarateioitem(dataregistro, dataalteracao, conveniocobrancarateio, nomerecebedor, idrecebedorpagarme, porcentagem, recebedorPrincipal)" +
                " VALUES (?, ?, ?, ?, ?, ?, ?);";
        try (PreparedStatement ps = con.prepareStatement(insert)) {
            int i = 0;
            ps.setTimestamp(++i, Uteis.getDataJDBCTimestamp(Calendario.hoje()));
            ps.setTimestamp(++i, Uteis.getDataJDBCTimestamp(Calendario.hoje()));
            resolveFKNull(ps, ++i, obj.getConvenioCobrancaRateioVO().getCodigo());
            ps.setString(++i, obj.getNomeRecebedor());
            ps.setString(++i, obj.getIdRecebedor());
            ps.setDouble(++i, obj.getPorcentagem());
            ps.setBoolean(++i, obj.isRecebedorPrincipal());
            ps.execute();
        }
        obj.setCodigo(obterValorChavePrimariaCodigo());
        obj.setNovoObj(false);
    }

    @Override
    public void alterar(ConvenioCobrancaRateioItemVO obj) throws Exception {
        String update = "UPDATE conveniocobrancarateioitem SET dataalteracao=?, conveniocobrancarateio=?, nomerecebedor=?, idrecebedorpagarme=?, porcentagem=?, recebedorPrincipal=? WHERE codigo = ?";
        try (PreparedStatement ps = con.prepareStatement(update)) {
            int i = 0;
            ps.setTimestamp(++i, Uteis.getDataJDBCTimestamp(Calendario.hoje()));
            resolveFKNull(ps, ++i, obj.getConvenioCobrancaRateioVO().getCodigo());
            ps.setString(++i, obj.getNomeRecebedor());
            ps.setString(++i, obj.getIdRecebedor());
            ps.setDouble(++i, obj.getPorcentagem());
            ps.setBoolean(++i, obj.isRecebedorPrincipal());
            ps.setInt(++i, obj.getCodigo());
            ps.execute();
        }
    }

    @Override
    public void excluir(ConvenioCobrancaRateioItemVO obj) throws Exception {
        String sql = "DELETE FROM conveniocobrancarateioitem WHERE codigo = ?";
        try (PreparedStatement ps = con.prepareStatement(sql)) {
            ps.setInt(1, obj.getCodigo());
            ps.execute();
        }
    }

    public void excluirPorRateio(ConvenioCobrancaRateioVO obj) throws Exception {
        String sql = "DELETE FROM conveniocobrancarateioitem WHERE conveniocobrancarateio = ?";
        try (PreparedStatement ps = con.prepareStatement(sql)) {
            ps.setInt(1, obj.getCodigo());
            ps.execute();
        }
    }

    public List<ConvenioCobrancaRateioItemVO> consultarPorConvenioCobrancaRateio(Integer convenioCobrancaRateio) throws Exception {
        String sql = "select * from conveniocobrancarateioitem where conveniocobrancarateio = " + convenioCobrancaRateio + " order by codigo";
        try (Statement stm = con.createStatement()) {
            try (ResultSet rs = stm.executeQuery(sql)) {
                return montarDadosConulta(rs);
            }
        }
    }

    private List<ConvenioCobrancaRateioItemVO> montarDadosConulta(ResultSet rs) throws Exception {
        List<ConvenioCobrancaRateioItemVO> lista = new ArrayList<>();
        while (rs.next()) {
            lista.add(montarDados(rs));
        }
        return lista;
    }

    private ConvenioCobrancaRateioItemVO montarDados(ResultSet rs) throws Exception {
        ConvenioCobrancaRateioItemVO obj = new ConvenioCobrancaRateioItemVO();
        obj.setCodigo(rs.getInt("codigo"));
        obj.setDataRegistro(rs.getTimestamp("dataregistro"));
        obj.setDataAlteracao(rs.getTimestamp("dataalteracao"));
        obj.getConvenioCobrancaRateioVO().setCodigo(rs.getInt("convenioCobrancaRateio"));
        obj.setNomeRecebedor(rs.getString("nomeRecebedor"));
        obj.setIdRecebedor(rs.getString("idRecebedorPagarMe"));
        obj.setPorcentagem(rs.getDouble("porcentagem"));
        obj.setRecebedorPrincipal(rs.getBoolean("recebedorPrincipal"));
        return obj;
    }
}

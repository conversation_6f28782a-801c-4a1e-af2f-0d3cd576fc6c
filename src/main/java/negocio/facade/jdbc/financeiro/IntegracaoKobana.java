package negocio.facade.jdbc.financeiro;

import negocio.comuns.financeiro.FinancialAccountKobanaVO;
import negocio.comuns.financeiro.IntegracaoKobanaVO;
import negocio.comuns.financeiro.enumerador.AmbienteEnum;
import negocio.comuns.utilitarias.Uteis;
import negocio.facade.jdbc.arquitetura.SuperEntidade;
import negocio.interfaces.financeiro.IntegracaoKobanaInterfaceFacade;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.util.List;

public class IntegracaoKobana extends SuperEntidade implements IntegracaoKobanaInterfaceFacade {

    public IntegracaoKobana() throws Exception {
        super();
    }

    public IntegracaoKobana(Connection con) throws Exception {
        super(con);
    }

    public IntegracaoKobanaVO montarDados(ResultSet dadosSQL, int nivelMontarDados) throws Exception {
        IntegracaoKobanaVO obj = new IntegracaoKobanaVO();
        obj.setCodigo(dadosSQL.getInt("codigo"));
        obj.setAmbiente(dadosSQL.getInt("ambiente"));
        obj.setEmpresa(dadosSQL.getInt("empresa"));
        obj.setEmail(dadosSQL.getString("email"));
        obj.setBusiness_cnpj(dadosSQL.getString("business_cnpj"));
        obj.setNickname(dadosSQL.getString("nickname"));
        obj.setBusiness_legal_name(dadosSQL.getString("business_legal_name"));
        obj.setApi_access_token(dadosSQL.getString("api_access_token"));
        obj.setAtivo(dadosSQL.getBoolean("ativo"));
        obj.setId(dadosSQL.getInt("id"));
        obj.setCreated_At(dadosSQL.getTimestamp("created_at"));

        if (nivelMontarDados == Uteis.NIVELMONTARDADOS_DADOSBASICOS) {
            return obj;
        }

        FinancialAccountKobana financialAccountKobanaDAO;
        try {
            financialAccountKobanaDAO = new FinancialAccountKobana(con);
            List<FinancialAccountKobanaVO> financialAccounts = financialAccountKobanaDAO.consultarByCodIntegracaoKobana(obj.getCodigo());
            obj.setFinancialAccountsKobanaVO(financialAccounts);
        } catch (Exception e) {
            throw e;
        } finally {
            financialAccountKobanaDAO = null;
        }
        return obj;
    }

    public IntegracaoKobanaVO incluir(IntegracaoKobanaVO obj) throws Exception {
        try {
            String sql = "INSERT INTO integracaoKobana(empresa, email, business_cnpj, nickname, business_legal_name, ativo, api_access_token, id , created_at, ambiente)" +
                    " VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING codigo";
            try (PreparedStatement sqlInserir = con.prepareStatement(sql)) {
                sqlInserir.setInt(1, obj.getEmpresa());
                sqlInserir.setString(2, obj.getEmail());
                sqlInserir.setString(3, obj.getBusiness_cnpj());
                sqlInserir.setString(4, obj.getNickname());
                sqlInserir.setString(5, obj.getBusiness_legal_name());
                sqlInserir.setBoolean(6, obj.isAtivo());
                sqlInserir.setString(7, obj.getApi_access_token());
                sqlInserir.setInt(8, obj.getId());
                sqlInserir.setTimestamp(9, Uteis.getDataJDBCTimestamp(obj.getCreated_At()));
                if (obj.getAmbiente() == null) {
                    sqlInserir.setInt(10, AmbienteEnum.NENHUM.getCodigo());
                } else {
                    sqlInserir.setInt(10, obj.getAmbiente());
                }

                ResultSet rsNovo = sqlInserir.executeQuery();
                rsNovo.next();
                obj.setCodigo(rsNovo.getInt(1));
                return obj;
            }
        } catch (Exception e) {
            throw e;
        }
    }

    public IntegracaoKobanaVO consultar(Integer empresa, int nivelMontarDados) throws Exception {
        IntegracaoKobanaVO integracaoKobanaVO = new IntegracaoKobanaVO();
        String sql = "SELECT * FROM IntegracaoKobana WHERE empresa = " + empresa;
        try (PreparedStatement ps = con.prepareStatement(sql)) {
            try (ResultSet rs = ps.executeQuery()) {
                if (rs.next()) {
                    return montarDados(rs, nivelMontarDados);
                }
            }
        }
        return integracaoKobanaVO;
    }

    public void inativar(Integer id) throws Exception {
        String update = "UPDATE IntegracaoKobana set ativo = false where id = ?";
        try (PreparedStatement stm = con.prepareStatement(update)) {
            stm.setInt(1, id);
            stm.execute();
        }
    }

    public void reativar(Integer id) throws Exception {
        String update = "UPDATE IntegracaoKobana set ativo = true where id = ?";
        try (PreparedStatement stm = con.prepareStatement(update)) {
            stm.setInt(1, id);
            stm.execute();
        }
    }
}

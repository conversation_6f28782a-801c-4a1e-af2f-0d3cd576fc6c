package negocio.facade.jdbc.financeiro;

import org.json.JSONArray;
import negocio.comuns.financeiro.MovParcelaDetalhamentoVO;
import negocio.comuns.utilitarias.Uteis;
import negocio.facade.jdbc.arquitetura.SuperEntidade;
import negocio.interfaces.financeiro.MovParcelaDetalhamentoInterfaceFacade;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;

/**
 * Created by GlaucoT on 19/11/2015
 */
public class MovParcelaDetalhamento extends SuperEntidade implements MovParcelaDetalhamentoInterfaceFacade {
    public MovParcelaDetalhamento() throws Exception {
    }

    public MovParcelaDetalhamento(Connection conexao) throws Exception {
        super(conexao);
    }

    public static MovParcelaDetalhamentoVO montarDadosBasico(ResultSet dadosSQL) throws Exception {
        MovParcelaDetalhamentoVO obj = new MovParcelaDetalhamentoVO();
        obj.setNovoObj(false);
        obj.setCodigo(dadosSQL.getInt("codigo"));
        obj.getMovParcelaVO().setCodigo(dadosSQL.getInt("movparcela"));
        if (dadosSQL.getString("chequesdevolvidos") != null) {
            obj.setChequesDevolvidos(new JSONArray(dadosSQL.getString("chequesdevolvidos")));
        }
        if (dadosSQL.getString("notasEmitidasAnteriormente") != null) {
            obj.setNotasEmitidasAnteriormente(new JSONArray(dadosSQL.getString("notasEmitidasAnteriormente")));
        }
        return obj;
    }

    public static MovParcelaDetalhamentoVO montarDados(ResultSet dadosSQL, int nivelMontarDados, Connection con) throws Exception {
        MovParcelaDetalhamentoVO obj = montarDadosBasico(dadosSQL);

        if (Uteis.NIVELMONTARDADOS_MINIMOS == nivelMontarDados) {
            return obj;
        }
        return obj;
    }

    public void incluir(MovParcelaDetalhamentoVO obj) throws Exception {
        try {
            con.setAutoCommit(false);
            incluirSemCommit(obj);
            obj.setNovoObj(false);
            con.commit();
            con.setAutoCommit(true);
        } catch (Exception e) {
            obj.setNovoObj(true);
            con.rollback();
            con.setAutoCommit(true);
            throw e;
        }
    }

    public void incluirSemCommit(MovParcelaDetalhamentoVO obj) throws Exception {
        String sqlInsert = "INSERT INTO movparceladetalhamento (movparcela, chequesdevolvidos, notasEmitidasAnteriormente) VALUES (?, ?, ?);";
        PreparedStatement sqlInserir = con.prepareStatement(sqlInsert);
        int i = 0;
        sqlInserir.setInt(++i, obj.getMovParcelaVO().getCodigo());
        if (obj.getChequesDevolvidos() != null) {
            sqlInserir.setString(++i, obj.getChequesDevolvidos().toString());
        } else {
            sqlInserir.setNull(++i, 0);
        }
        if (obj.getNotasEmitidasAnteriormente() != null) {
            sqlInserir.setString(++i, obj.getNotasEmitidasAnteriormente().toString());
        } else {
            sqlInserir.setNull(++i, 0);
        }

        sqlInserir.execute();

        obj.setCodigo(obterValorChavePrimariaCodigo());
    }

    public MovParcelaDetalhamentoVO consultarPorMovParcela(Integer codMovParcela, int nivelMontarDados) throws Exception {
        String sql = "SELECT * FROM MovParcelaDetalhamento WHERE movparcela = ?";
        PreparedStatement sqlConsultar = con.prepareStatement(sql);
        sqlConsultar.setInt(1, codMovParcela);
        ResultSet tabelaResultado = sqlConsultar.executeQuery();
        if (!tabelaResultado.next()) {
            return null;
        }

        return (montarDados(tabelaResultado, nivelMontarDados, con));
    }
}

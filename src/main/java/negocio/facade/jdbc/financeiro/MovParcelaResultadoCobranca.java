package negocio.facade.jdbc.financeiro;

import negocio.comuns.financeiro.MovParcelaVO;
import negocio.comuns.financeiro.TransacaoVO;
import negocio.comuns.financeiro.enumerador.*;
import negocio.comuns.utilitarias.Calendario;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisValidacao;
import negocio.facade.jdbc.arquitetura.SuperEntidade;
import negocio.facade.jdbc.arquitetura.SuperFacadeJDBC;
import servicos.impl.cieloecommerce.CieloECommerceRetornoEnum;
import servicos.impl.dcc.golden.ItemInadimplenciaTO;
import servicos.impl.dcc.bin.DCCBinStatusEnum;
import servicos.impl.dcc.cielo.DCCCieloStatusEnum;
import servicos.impl.dcc.getnet.DCOGetNetStatusEnum;
import servicos.impl.dcccaixaonline.DCCCaixaOnlineRetornoEnum;
import servicos.impl.getnet.GetnetOnlineRetornoEnum;
import servicos.impl.pagarMe.PagarMeRetornoEnum;
import servicos.impl.pagbank.PagBankRetornoEnum;
import servicos.impl.redepay.ERedeRetornoEnum;
import servicos.impl.stone.StoneRetornoEnum;
import servicos.impl.stripe.StripeRetornoEnum;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.util.Date;
import java.util.List;

/**
 * Created with IntelliJ IDEA.
 * User: Luiz Felipe
 * Date: 16/05/2020
 */
public class MovParcelaResultadoCobranca extends SuperEntidade {

    public MovParcelaResultadoCobranca() throws Exception {
        super();
    }

    public MovParcelaResultadoCobranca(Connection con) throws Exception {
        super(con);
    }

    private void incluirRegistro(ItemInadimplenciaTO itemTO, boolean existeRegistro) {
        try {

            String sql = "INSERT INTO movparcelaresultadocobranca(dataalteracao, movparcela, nrtentativas, dataultimatentativa, meioultimatentativa, " +
                    "codigoretorno, motivoretorno, produtosparcela, operacaoretornocobranca, tipoconveniocobranca) VALUES (?,?,?,?,?,?,?,?,?,?);";

            if (existeRegistro) {
                sql = "UPDATE movparcelaresultadocobranca SET dataalteracao=?, movparcela=?, nrtentativas=?, dataultimatentativa=?, meioultimatentativa=?, " +
                        "codigoretorno=?, motivoretorno=?, produtosparcela=?, operacaoretornocobranca=?, tipoconveniocobranca=? WHERE movparcela = ?;";
            }

            try (PreparedStatement ps = con.prepareStatement(sql)) {
                int i = 0;
                ps.setTimestamp(++i, Uteis.getDataJDBCTimestamp(Calendario.hoje()));
                ps.setInt(++i, itemTO.getMovParcela());
                ps.setInt(++i, itemTO.getQtdTentativasCobranca());
                ps.setTimestamp(++i, Uteis.getDataJDBCTimestamp(itemTO.getDataUltimaTentativa()));
                ps.setString(++i, itemTO.getMeioUltimaTentativa());
                ps.setString(++i, itemTO.getCodigoRetornoUltimaTentativa());
                ps.setString(++i, itemTO.getMotivoRetornoUltimaTentativa());
                ps.setString(++i, itemTO.getProdutosParcela());
                ps.setInt(++i, itemTO.getOperacaoRetornoCobranca().getCodigo());
                ps.setInt(++i, itemTO.getTipoConvenioCobranca());
                if (existeRegistro) {
                    ps.setInt(++i, itemTO.getMovParcela());
                }
                ps.execute();
            }
        } catch (Exception ex) {
            ex.printStackTrace();
        }
    }

    private TipoConvenioCobrancaEnum obterTipoConvenioCobrancaEnum(TipoTransacaoEnum tipoTransacaoEnum) {
        for (TipoConvenioCobrancaEnum tipo : TipoConvenioCobrancaEnum.values()) {
            if (tipo != null &&
                    tipo.getTipoRemessa() != null &&
                    tipo.getTipoRemessa().getTipoTransacao() != null &&
                    tipo.getTipoRemessa().getTipoTransacao().equals(tipoTransacaoEnum)) {
                return tipo;
            }
        }
        return TipoConvenioCobrancaEnum.NENHUM;
    }

    private TipoConvenioCobrancaEnum obterTipoConvenioCobrancaEnum(TipoRemessaEnum tipoRemessaEnum) {
        for (TipoConvenioCobrancaEnum tipo : TipoConvenioCobrancaEnum.values()) {
            if (tipo != null &&
                    tipo.getTipoRemessa() != null &&
                    tipo.getTipoRemessa().equals(tipoRemessaEnum)) {
                return tipo;
            }
        }
        return TipoConvenioCobrancaEnum.NENHUM;
    }

    private String obterDescricaoRetorno(TipoConvenioCobrancaEnum tipoConvenioCobrancaEnum, String codigoRetorno) {
        try {
            if (tipoConvenioCobrancaEnum == null) {
                return "";
            }

            if (tipoConvenioCobrancaEnum.equals(TipoConvenioCobrancaEnum.DCC_CIELO_ONLINE)) {
                return CieloECommerceRetornoEnum.valueOff(codigoRetorno).getDescricao();
            } else if (tipoConvenioCobrancaEnum.equals(TipoConvenioCobrancaEnum.DCC_E_REDE)) {
                return ERedeRetornoEnum.valueOff(codigoRetorno).getDescricao();
            } else if (tipoConvenioCobrancaEnum.equals(TipoConvenioCobrancaEnum.DCC_GETNET_ONLINE)) {
                return GetnetOnlineRetornoEnum.valueOff(codigoRetorno).getDescricao();
            } else if (tipoConvenioCobrancaEnum.equals(TipoConvenioCobrancaEnum.DCC_STONE_ONLINE)) {
                return StoneRetornoEnum.valueOff(codigoRetorno).getDescricao();
            } else if (tipoConvenioCobrancaEnum.equals(TipoConvenioCobrancaEnum.DCC_PAGAR_ME)) {
                return PagarMeRetornoEnum.valueOff(codigoRetorno).getDescricao();
            } else if (tipoConvenioCobrancaEnum.equals(TipoConvenioCobrancaEnum.DCC_PAGBANK)) {
                return PagBankRetornoEnum.valueOff(codigoRetorno).getDescricao();
            } else if (tipoConvenioCobrancaEnum.equals(TipoConvenioCobrancaEnum.DCC_STRIPE)) {
                return StripeRetornoEnum.valueOff(codigoRetorno).getDescricao();
            } else if (tipoConvenioCobrancaEnum.equals(TipoConvenioCobrancaEnum.DCC)) {
                return DCCCieloStatusEnum.valueOff(codigoRetorno).getDescricao();
            } else if (tipoConvenioCobrancaEnum.equals(TipoConvenioCobrancaEnum.DCC_GETNET)) {
                return DCOGetNetStatusEnum.valueOff(codigoRetorno).getDescricao();
            } else if (tipoConvenioCobrancaEnum.equals(TipoConvenioCobrancaEnum.DCC_BIN)) {
                return DCCBinStatusEnum.valueOff(codigoRetorno).getDescricao();
            } else if (tipoConvenioCobrancaEnum.equals(TipoConvenioCobrancaEnum.DCC_CAIXA_ONLINE)) {
                return DCCCaixaOnlineRetornoEnum.valueOff(codigoRetorno).getDescricao();
            }

            //verificar se o codigo de retorno é um codigo pacto
            CodigoRetornoPactoEnum codigoRetornoPactoEnum = CodigoRetornoPactoEnum.consultarPorCodigo(codigoRetorno);
            if (codigoRetornoPactoEnum != null) {
                return codigoRetornoPactoEnum.getDescricao();
            }
            return "";
        } catch (Exception ex) {
            ex.printStackTrace();
            return "";
        }
    }

    public void processarParcelas(List<MovParcelaVO> listaMovParcela) throws Exception {
        if (!UteisValidacao.emptyList(listaMovParcela)) {
            String parcelas = "";
            for (MovParcelaVO movParcelaVO : listaMovParcela) {
                parcelas += "," + movParcelaVO.getCodigo();
            }
            processarParcelas(parcelas.replaceFirst(",", ""), null);
        }
    }

    public void processarParcelas(String listaMovParcela, Date mesProcessar) throws Exception {

        boolean filtrou = false;

        StringBuilder sql = new StringBuilder();
        sql.append("SELECT  \n");
        sql.append("m.codigo as codigoparcela, \n");
        sql.append("m.valorparcela, \n");
        sql.append("m.situacao as situacaoparcela, \n");
        sql.append("fp.descricao as formapagamento, \n");
        sql.append("mp.datalancamento as dataPagamento, \n");
        sql.append("array_to_string(array(select mov.descricao from movproduto mov inner join movprodutoparcela mpp on mpp.movproduto = mov.codigo where mpp.movparcela = mp.codigo), '<br/>', '') as produtos, \n");
        sql.append("exists(select codigo from movparcelaresultadocobranca where movparcela = m.codigo) as existeRegistro \n");
        sql.append("FROM movparcela m \n");
        sql.append("LEFT JOIN pagamentomovparcela pm ON pm.movparcela = m.codigo \n");
        sql.append("LEFT JOIN movpagamento mp ON mp.codigo = pm.movpagamento  \n");
        sql.append("LEFT JOIN formapagamento fp on mp.formapagamento = fp.codigo \n");
        if (mesProcessar != null) {
            filtrou = true;
            sql.append("WHERE m.datavencimento::date between '").append(Uteis.getDataFormatoBD(Uteis.obterPrimeiroDiaMes(mesProcessar)));
            sql.append("' and '").append(Uteis.getDataFormatoBD(Uteis.obterUltimoDiaMes(mesProcessar))).append("' \n");
        } else if (listaMovParcela != null && !UteisValidacao.emptyString(listaMovParcela.trim())) {
            filtrou = true;
            sql.append("WHERE m.codigo in (").append(listaMovParcela).append(") \n");
        }

        if (!filtrou) {
            return;
        }

        ResultSet rs = SuperFacadeJDBC.criarConsulta(sql.toString(), con);

        while (rs.next()) {

            ItemInadimplenciaTO itemTO = new ItemInadimplenciaTO();
            itemTO.setMovParcela(rs.getInt("codigoparcela"));
            itemTO.setValorParcela(rs.getDouble("valorparcela"));
            itemTO.setSituacaoParcela(rs.getString("situacaoparcela"));
            itemTO.setFormaPagamento(rs.getString("formapagamento"));
            itemTO.setDataPagamento(rs.getTimestamp("dataPagamento"));
            itemTO.setProdutosParcela(rs.getString("produtos"));

            boolean existeRegistro = rs.getBoolean("existeRegistro");

            consultarDadosUltimaTentativa(itemTO);
            incluirRegistro(itemTO, existeRegistro);
        }
    }

    private void consultarDadosUltimaTentativa(ItemInadimplenciaTO item) throws Exception {

        StringBuilder sqlBase = new StringBuilder();
        sqlBase.append("select * from ( \n");
        sqlBase.append("select  \n");
        sqlBase.append("'REMESSA' as meio,\n");
        sqlBase.append("r.dataregistro as data, \n");
        sqlBase.append("r.tipo as tipoEnum,\n");
        sqlBase.append("split_part(split_part(split_part(ri.props, 'StatusVenda=', 2), ',', 1), '}', 1) AS codigoRetorno,\n");
        sqlBase.append("'' as resposta,\n");
        sqlBase.append("''  as outrasinformacoes,\n");
        sqlBase.append("0 as situacao\n");
        sqlBase.append("from remessaitem ri \n");
        sqlBase.append("inner join remessa r on r.codigo = ri.remessa \n");
        sqlBase.append("left join remessaitemmovparcela rim on rim.remessaitem = ri.codigo \n");
        sqlBase.append("where (ri.movparcela = ").append(item.getMovParcela()).append(" or rim.movparcela = ").append(item.getMovParcela()).append(") \n");
        sqlBase.append("union \n");
        sqlBase.append("select  \n");
        sqlBase.append("'TRANSACAO' as meio,\n");
        sqlBase.append("t.dataprocessamento as data,\n");
        sqlBase.append("t.tipo as tipoEnum,\n");
        sqlBase.append("t.codigoretorno as codigoRetorno,\n");
        sqlBase.append("t.paramsresposta as resposta,\n");
        sqlBase.append("t.outrasinformacoes  as outrasinformacoes,\n");
        sqlBase.append("t.situacao as situacao\n");
        sqlBase.append("from transacao t \n");
        sqlBase.append("inner join transacaomovparcela tm on tm.transacao = t.codigo \n");
        sqlBase.append("where tm.movparcela = ").append(item.getMovParcela()).append(" \n");
        sqlBase.append(") sql \n");

        //total de tentivas
        Integer qtdTentativas = SuperFacadeJDBC.contar("SELECT COUNT(*) FROM (" + sqlBase.toString() + ") as qtd", con);
        item.setQtdTentativasCobranca(qtdTentativas);

        String motivoRetornoUltimaTentativaSalvar = "";


        //pegar o ultimo registro
        sqlBase.append("order by sql.data desc limit 1 \n");
        ResultSet rs = SuperFacadeJDBC.criarConsulta(sqlBase.toString(), con);
        if (rs.next()) {

            TipoConvenioCobrancaEnum tipoConvenioCobrancaEnum = null;
            String motivoRetornoUltimaTentativa;
            String meioUltimaTentativa;
            String motivoTransacao = "";

            Date dataUltimaTentativa = rs.getTimestamp("data");
            String codigoRetornoUltimaTentativa = rs.getString("codigoRetorno");
            Integer tipoEnum = rs.getInt("tipoEnum");
            String meio = rs.getString("meio");


            //remessa
            if (meio.equalsIgnoreCase("REMESSA")) {
                motivoRetornoUltimaTentativa = "EDI";

                TipoRemessaEnum tipoRemessaEnum = TipoRemessaEnum.getTipoRemessaEnum(tipoEnum);
                tipoConvenioCobrancaEnum = obterTipoConvenioCobrancaEnum(tipoRemessaEnum);

                if (tipoConvenioCobrancaEnum.equals(TipoConvenioCobrancaEnum.NENHUM)) {
                    meioUltimaTentativa = tipoRemessaEnum.getDescricao().toUpperCase();
                } else {
                    meioUltimaTentativa = tipoConvenioCobrancaEnum.getDescricao().toUpperCase();
                }

            } else { //transação
                motivoRetornoUltimaTentativa = "ONLINE";

                TipoTransacaoEnum tipoTransacaoEnum = TipoTransacaoEnum.getTipoTransacaoEnum(tipoEnum);
                tipoConvenioCobrancaEnum = obterTipoConvenioCobrancaEnum(tipoTransacaoEnum);

                if (tipoConvenioCobrancaEnum.equals(TipoConvenioCobrancaEnum.NENHUM)) {
                    meioUltimaTentativa = tipoTransacaoEnum.getDescricao().toUpperCase();
                } else {
                    meioUltimaTentativa = tipoConvenioCobrancaEnum.getDescricao().toUpperCase();
                }

                TransacaoVO transacaoVO = Transacao.obterObjetoTransacaoPorTipo(tipoTransacaoEnum);
                transacaoVO.setTipo(tipoTransacaoEnum);
                transacaoVO.setParamsResposta(rs.getString("resposta"));
                transacaoVO.setOutrasInformacoes(rs.getString("outrasinformacoes"));
                transacaoVO.setCodigoRetorno(codigoRetornoUltimaTentativa);
                transacaoVO.setSituacao(SituacaoTransacaoEnum.getSituacaoTransacaoEnum(rs.getInt("situacao")));

                motivoTransacao = transacaoVO.getCodigoRetornoGestaoTransacaoMotivo();
            }

            String retornoIdentificado = obterDescricaoRetorno(tipoConvenioCobrancaEnum, codigoRetornoUltimaTentativa);

            if (!UteisValidacao.emptyString(motivoTransacao) && UteisValidacao.emptyString(retornoIdentificado)) {
                retornoIdentificado = motivoTransacao;
            }

            item.setDataUltimaTentativa(dataUltimaTentativa);
            item.setMeioUltimaTentativa(meioUltimaTentativa);
            item.setCodigoRetornoUltimaTentativa(codigoRetornoUltimaTentativa);
            item.setOperacaoRetornoCobranca(OperacaoRetornoCobrancaEnum.obter(codigoRetornoUltimaTentativa, tipoConvenioCobrancaEnum));
            item.setTipoConvenioCobranca(tipoConvenioCobrancaEnum.getCodigo());
            motivoRetornoUltimaTentativaSalvar = (" - " + motivoRetornoUltimaTentativa + " - " + retornoIdentificado);
        }

        if (item.getSituacaoParcela().equalsIgnoreCase("PG") && item.getValorParcela() == 0.0) {
            //PARCELA COM SITUAÇÃO PAGA MAS COM VALOR ZERADO
            motivoRetornoUltimaTentativaSalvar = ("PARCELA PAGA - PARCELA SEM VALOR (0.0)" + motivoRetornoUltimaTentativaSalvar);
        } else if (item.getSituacaoParcela().equalsIgnoreCase("PG") && item.getValorParcela() > 0.0) {
            motivoRetornoUltimaTentativaSalvar = ("PARCELA PAGA - " + item.getFormaPagamento() + motivoRetornoUltimaTentativaSalvar);
        } else if (item.getSituacaoParcela().equalsIgnoreCase("RG")) {
            if (UteisValidacao.emptyString(motivoRetornoUltimaTentativaSalvar)) {
                motivoRetornoUltimaTentativaSalvar = " - PARCELA SEM TENTATIVA DE COBRANÇA";
            }
            motivoRetornoUltimaTentativaSalvar = ("PARCELA RENEGOCIADA" + motivoRetornoUltimaTentativaSalvar);
        } else if (item.getSituacaoParcela().equalsIgnoreCase("CA")) {
            if (UteisValidacao.emptyString(motivoRetornoUltimaTentativaSalvar)) {
                motivoRetornoUltimaTentativaSalvar = " - PARCELA SEM TENTATIVA DE COBRANÇA";
            }
            motivoRetornoUltimaTentativaSalvar = ("PARCELA CANCELADA" + motivoRetornoUltimaTentativaSalvar);
        } else if (item.getSituacaoParcela().equalsIgnoreCase("EA")) {
            if (UteisValidacao.emptyString(motivoRetornoUltimaTentativaSalvar)) {
                motivoRetornoUltimaTentativaSalvar = " - PARCELA SEM TENTATIVA DE COBRANÇA";
            }
            motivoRetornoUltimaTentativaSalvar = ("PARCELA EM ABERTO" + motivoRetornoUltimaTentativaSalvar);
        }

        item.setMotivoRetornoUltimaTentativa(motivoRetornoUltimaTentativaSalvar);
    }
}

package negocio.facade.jdbc.financeiro;

import negocio.comuns.financeiro.BancoVO;
import negocio.comuns.utilitarias.ConsistirException;
import negocio.comuns.utilitarias.Ordenacao;
import negocio.comuns.utilitarias.Uteis;
import negocio.facade.jdbc.arquitetura.SuperEntidade;
import negocio.interfaces.financeiro.BancoInterfaceFacade;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.sql.Statement;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

/**
 * Classe de persistência que encapsula todas as operações de manipulação dos dados da classe <code>BancoVO</code>.
 * Responsável por implementar operações como incluir, alterar, excluir e consultar pertinentes a classe <code>BancoVO</code>.
 * Encapsula toda a interação com o banco de dados.
 * @see BancoVO
 * @see SuperEntidade
 */
public class Banco extends SuperEntidade implements BancoInterfaceFacade {

    public Banco() throws Exception {
        super();
    }

    public Banco(Connection con) throws Exception {
        super(con);
    }

    /**
     * Operação responsável por retornar um novo objeto da classe <code>BancoVO</code>.
     */
    public BancoVO novo() throws Exception {
        incluir(getIdEntidade());
        BancoVO obj = new BancoVO();
        return obj;
    }

    /**
     * Operação responsável por incluir no banco de dados um objeto da classe <code>BancoVO</code>.
     * Primeiramente valida os dados (<code>validarDados</code>) do objeto. Verifica a conexão com o banco de dados e a permissão do usuário
     * para realizar esta operacão na entidade.
     * Isto, através da operação <code>incluir</code> da superclasse.
     * @param obj  Objeto da classe <code>BancoVO</code> que será gravado no banco de dados.
     * @exception Exception Caso haja problemas de conexão, restrição de acesso ou validação de dados.
     */
    public void incluir(BancoVO obj) throws Exception {
        try {
            BancoVO.validarDados(obj);
            incluir(getIdEntidade());
            obj.realizarUpperCaseDados();
            String sql = "INSERT INTO Banco(codigobanco, nome ) VALUES ( ? ,? )";
            try (PreparedStatement sqlInserir = con.prepareStatement(sql)) {
                sqlInserir.setInt(1, obj.getCodigoBanco());
                sqlInserir.setString(2, obj.getNome());
                sqlInserir.execute();
            }
            obj.setCodigo(obterValorChavePrimariaCodigo());
            obj.setNovoObj(new Boolean(false));
        } catch (Exception e) {
            obj.setNovoObj(new Boolean(true));
            throw e;
        }
    }

    /**
     * Operação responsável por alterar no BD os dados de um objeto da classe <code>BancoVO</code>.
     * Sempre utiliza a chave primária da classe como atributo para localização do registro a ser alterado.
     * Primeiramente valida os dados (<code>validarDados</code>) do objeto. Verifica a conexão com o banco de dados e a permissão do usuário
     * para realizar esta operacão na entidade.
     * Isto, através da operação <code>alterar</code> da superclasse.
     * @param obj    Objeto da classe <code>BancoVO</code> que será alterada no banco de dados.
     * @exception Exception Caso haja problemas de conexão, restrição de acesso ou validação de dados.
     */
    public void alterar(BancoVO obj) throws Exception {
        try {
            con.setAutoCommit(false);
            alterarSemCommit(obj);
            con.commit();
        } catch (Exception e) {
            con.rollback();
            con.setAutoCommit(true);
            throw e;
        } finally {
            con.setAutoCommit(true);
        }
    }

    public void alterarSemCommit(BancoVO obj) throws Exception {
            BancoVO.validarDados(obj);
            alterar(getIdEntidade());
            obj.realizarUpperCaseDados();
            String sql = "UPDATE Banco set codigobanco=?, nome=? WHERE (codigo = ?)";
        try (PreparedStatement sqlAlterar = con.prepareStatement(sql)) {
            sqlAlterar.setInt(1, obj.getCodigoBanco().intValue());
            sqlAlterar.setString(2, obj.getNome());
            sqlAlterar.setInt(3, obj.getCodigo().intValue());
            sqlAlterar.execute();
        }
    }


    /**
     * Operação responsável por excluir no BD um objeto da classe <code>BancoVO</code>.
     * Sempre localiza o registro a ser excluído através da chave primária da entidade.
     * Primeiramente verifica a conexão com o banco de dados e a permissão do usuário
     * para realizar esta operacão na entidade.
     * Isto, através da operação <code>excluir</code> da superclasse.
     * @param obj    Objeto da classe <code>BancoVO</code> que será removido no banco de dados.
     * @exception Exception Caso haja problemas de conexão ou restrição de acesso.
     */
    public void excluir(BancoVO obj) throws Exception {
        try {
            con.setAutoCommit(false);
            excluir(getIdEntidade());
            String sql = "DELETE FROM Banco WHERE ((codigo = ?))";
            try (PreparedStatement sqlExcluir = con.prepareStatement(sql)) {
                sqlExcluir.setInt(1, obj.getCodigo().intValue());
                sqlExcluir.execute();
            }
            con.commit();
        } catch (Exception e) {
            con.rollback();
            con.setAutoCommit(true);
            throw e;
        } finally {
            con.setAutoCommit(true);
        }
    }

    /**
     * Responsável por realizar uma consulta de <code>Banco</code> através do valor do atributo 
     * <code>String nome</code>. Retorna os objetos, com início do valor do atributo idêntico ao parâmetro fornecido.
     * Faz uso da operação <code>montarDadosConsulta</code> que realiza o trabalho de prerarar o List resultante.
     * @param   controlarAcesso Indica se a aplicação deverá verificar se o usuário possui permissão para esta consulta ou não.
     * @return  List Contendo vários objetos da classe <code>BancoVO</code> resultantes da consulta.
     * @exception Exception Caso haja problemas de conexão ou restrição de acesso.
     */
    public List consultarPorNome(String valorConsulta, boolean controlarAcesso, int nivelMontarDados) throws Exception {
        consultar(getIdEntidade(), controlarAcesso);
        String sqlStr = "SELECT * FROM Banco WHERE upper( nome ) like('" + valorConsulta.toUpperCase() + "%') ORDER BY nome";
        try (Statement stm = con.createStatement()) {
            try (ResultSet tabelaResultado = stm.executeQuery(sqlStr)) {
                return (montarDadosConsulta(tabelaResultado, nivelMontarDados));
            }
        }
    }

    public List<BancoVO> consultarTodos(int nivelMontarDados) throws Exception {
        String sqlStr = "SELECT * FROM Banco";
        try (Statement stm = con.createStatement()) {
            try (ResultSet tabelaResultado = stm.executeQuery(sqlStr)) {
                return (montarDadosConsulta(tabelaResultado, nivelMontarDados));
            }
        }
    }

    public List<BancoVO> consultarTodosComISPB(int nivelMontarDados) throws Exception {
        String sqlStr = "SELECT * FROM Banco WHERE ispb is not null and ispb <> ''";
        try (Statement stm = con.createStatement()) {
            try (ResultSet tabelaResultado = stm.executeQuery(sqlStr)) {
                return (montarDadosConsulta(tabelaResultado, nivelMontarDados));
            }
        }
    }

    public List<BancoVO> consultarTodosComISPB(boolean somenteNumeros, String texto, int nivelMontarDados) throws Exception {
        StringBuilder sb = new StringBuilder();
        sb.append("SELECT * FROM Banco WHERE ispb is not null and ispb <> ''\n");

        if (somenteNumeros) {
            sb.append("AND CAST(codigobanco AS TEXT) LIKE '%").append(texto).append("%'");
        } else {
            sb.append("AND nome ILIKE '%").append(texto).append("%'");
        }

        try (Statement stm = con.createStatement()) {
            try (ResultSet tabelaResultado = stm.executeQuery(sb.toString())) {
                return (montarDadosConsulta(tabelaResultado, nivelMontarDados));
            }
        }
    }

    /**
     * Responsável por realizar uma consulta de <code>Banco</code> através do valor do atributo 
     * <code>Integer codigo</code>. Retorna os objetos com valores iguais ou superiores ao parâmetro fornecido.
     * Faz uso da operação <code>montarDadosConsulta</code> que realiza o trabalho de prerarar o List resultante.
     * @param   controlarAcesso Indica se a aplicação deverá verificar se o usuário possui permissão para esta consulta ou não.
     * @return  List Contendo vários objetos da classe <code>BancoVO</code> resultantes da consulta.
     * @exception Exception Caso haja problemas de conexão ou restrição de acesso.
     */
    public List consultarPorCodigo(Integer valorConsulta, boolean controlarAcesso, int nivelMontarDados) throws Exception {
        consultar(getIdEntidade(), controlarAcesso);
        String sqlStr = "SELECT * FROM Banco WHERE codigo >= " + valorConsulta.intValue() + " ORDER BY codigo";
        try (Statement stm = con.createStatement()) {
            try (ResultSet tabelaResultado = stm.executeQuery(sqlStr)) {
                return (montarDadosConsulta(tabelaResultado, nivelMontarDados));
            }
        }
    }

    public List consultarPorCodigoBanco(Integer valorConsulta, boolean controlarAcesso, int nivelMontarDados) throws Exception {
        consultar(getIdEntidade(), controlarAcesso);
        String sqlStr = "SELECT * FROM Banco WHERE codigobanco >= " + valorConsulta.intValue() + " ORDER BY codigobanco";
        try (Statement stm = con.createStatement()) {
            try (ResultSet tabelaResultado = stm.executeQuery(sqlStr)) {
                return (montarDadosConsulta(tabelaResultado, nivelMontarDados));
            }
        }
    }

    public BancoVO consultarCodigoBanco(Integer valorConsulta, boolean controlarAcesso, int nivelMontarDados) throws Exception {
        consultar(getIdEntidade(), controlarAcesso);
        String sqlStr = "SELECT * FROM Banco WHERE codigobanco = ? ORDER BY codigobanco";
        try (PreparedStatement sqlConsultar = con.prepareStatement(sqlStr)) {
            sqlConsultar.setInt(1, valorConsulta.intValue());
            try (ResultSet tabelaResultado = sqlConsultar.executeQuery()) {
                if (!tabelaResultado.next()) {
                    return new BancoVO();
                }
                return (montarDados(tabelaResultado, nivelMontarDados));
            }
        }
    }

    public BancoVO consultarCodigo(Integer valorConsulta, boolean controlarAcesso, int nivelMontarDados) throws Exception {
        consultar(getIdEntidade(), controlarAcesso);
        String sqlStr = "SELECT * FROM Banco WHERE codigo = ? ORDER BY codigo";
        try (PreparedStatement sqlConsultar = con.prepareStatement(sqlStr)) {
            sqlConsultar.setInt(1, valorConsulta.intValue());
            try (ResultSet tabelaResultado = sqlConsultar.executeQuery()) {
                if (!tabelaResultado.next()) {
                    return new BancoVO();
                }
                return (montarDados(tabelaResultado, nivelMontarDados));
            }
        }
    }

    /**
     * Responsável por montar os dados de vários objetos, resultantes de uma consulta ao banco de dados (<code>ResultSet</code>).
     * Faz uso da operação <code>montarDados</code> que realiza o trabalho para um objeto por vez.
     * @return  List Contendo vários objetos da classe <code>BancoVO</code> resultantes da consulta.
     */
    public static List<BancoVO> montarDadosConsulta(ResultSet tabelaResultado, int nivelMontarDados) throws Exception {
        List<BancoVO> vetResultado = new ArrayList();
        while (tabelaResultado.next()) {
            vetResultado.add(montarDados(tabelaResultado, nivelMontarDados));
        }
        return vetResultado;
    }

    /**
     * Responsável por montar os dados resultantes de uma consulta ao banco de dados (<code>ResultSet</code>)
     * em um objeto da classe <code>BancoVO</code>.
     * @return  O objeto da classe <code>BancoVO</code> com os dados devidamente montados.
     */
    public static BancoVO montarDados(ResultSet dadosSQL, int nivelMontarDados) throws Exception {
        BancoVO obj = new BancoVO();
        obj.setCodigo(new Integer(dadosSQL.getInt("codigo")));
        obj.setCodigoBanco(new Integer(dadosSQL.getInt("codigobanco")));
        obj.setNome(dadosSQL.getString("nome"));
        obj.setIspb(dadosSQL.getString("ispb"));
        obj.setNovoObj(false);
        if (nivelMontarDados == Uteis.NIVELMONTARDADOS_DADOSBASICOS) {
            return obj;
        }

        return obj;
    }

    /**
     * Operação responsável por localizar um objeto da classe <code>BancoVO</code>
     * através de sua chave primária. 
     * @exception Exception Caso haja problemas de conexão ou localização do objeto procurado.
     */
    public BancoVO consultarPorChavePrimaria(Integer codigoPrm, int nivelMontarDados) throws Exception {
        consultar(getIdEntidade(), false);

        BancoVO eCache = (BancoVO) obterFromCache(codigoPrm);
        if (eCache != null){
            return eCache;
        }

        String sql = "SELECT * FROM Banco WHERE codigo = ?";
        try (PreparedStatement sqlConsultar = con.prepareStatement(sql)) {
            sqlConsultar.setInt(1, codigoPrm.intValue());
            try (ResultSet tabelaResultado = sqlConsultar.executeQuery()) {
                if (!tabelaResultado.next()) {
                    throw new ConsistirException("Dados Não Encontrados ( Banco ).");
                }
                eCache = (montarDados(tabelaResultado, nivelMontarDados));
            }
        }
        putToCache(eCache);
        return eCache;
    }

    public BancoVO criarOuConsultarSeExistePorNome(BancoVO obj) throws Exception {
        String sql = "SELECT * FROM Banco WHERE upper( nome ) like('" + obj.getNome() + "%') ORDER BY nome";
        try (PreparedStatement sqlConsultar = con.prepareStatement(sql)) {
            try (ResultSet tabelaResultado = sqlConsultar.executeQuery()) {
                if (!tabelaResultado.next()) {
                    incluir(obj);
                    return obj;
                } else {
                    return (Banco.montarDados(tabelaResultado, Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA));
                }
            }
        }
    }

    public String consultarJSON() throws Exception {
        StringBuilder json;
        boolean dados;
        try (ResultSet rs = getPS().executeQuery()) {
            json = new StringBuilder();
            json.append("{\"aaData\":[");
            dados = false;
            while (rs.next()) {
                dados = true;
                json.append("[\"").append(rs.getString("codigo")).append("\",");
                json.append("\"").append(rs.getString("codigobanco")).append("\",");
                json.append("\"").append(rs.getString("nome").trim()).append("\"],");
            }
        }
        if (dados) {
            json.deleteCharAt(json.toString().length() - 1);
        }
        json.append("]}");
        return json.toString();
    }

    private PreparedStatement getPS() throws SQLException {
        String sql = "SELECT codigo, codigobanco, nome FROM banco ORDER BY nome";
        return con.prepareStatement(sql);
    }

    public List consultarParaImpressao(String filtro, String ordem, String campoOrdenacao, int i) throws SQLException {
        List lista;
        try (ResultSet rs = getPS().executeQuery()) {
            lista = new ArrayList();
            while (rs.next()) {
                BancoVO banco = new BancoVO();
                String geral = rs.getString("codigo") + rs.getString("codigobanco") + rs.getString("nome");
                if (geral.toLowerCase().contains(filtro.toLowerCase())) {
                    banco.setCodigo(rs.getInt("codigo"));
                    banco.setCodigoBanco(rs.getInt("codigobanco"));
                    banco.setNome(rs.getString("nome"));
                    lista.add(banco);
                }
            }
        }
        if (campoOrdenacao.equals("Código")) {
            Ordenacao.ordenarLista(lista, "codigo");
        } else if (campoOrdenacao.equals("Código do Banco")) {
            Ordenacao.ordenarLista(lista, "codigoBanco");
        } else if (campoOrdenacao.equals("Nome")) {
            Ordenacao.ordenarLista(lista, "nome");
        }
        if (ordem.contains("desc")) {
            Collections.reverse(lista);
        }
        return lista;

    }
    
    public BancoVO consultarOuCriaPorCodigoBanco(Integer codigoBanco, final String nome) throws Exception {
        String sql = "SELECT * FROM Banco WHERE codigoBanco = " + codigoBanco ;
        try (PreparedStatement sqlConsultar = con.prepareStatement(sql)) {
            try (ResultSet tabelaResultado = sqlConsultar.executeQuery()) {
                if (!tabelaResultado.next()) {
                    BancoVO novo = new BancoVO();
                    novo.setCodigoBanco(codigoBanco);
                    novo.setNome(nome);
                    incluir(novo);
                    return novo;
                } else {
                    return (Banco.montarDados(tabelaResultado, Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA));
                }
            }
        }
    }

    public void incluirISPB(String ispb, int codigoBanco) throws Exception {
        String sql = "UPDATE Banco set ispb = ? WHERE codigobanco = ?";
        try (PreparedStatement sqlAlterar = con.prepareStatement(sql)) {
            sqlAlterar.setString(1, ispb);
            sqlAlterar.setInt(2, codigoBanco);
            sqlAlterar.execute();
        }
    }
}

package negocio.facade.jdbc.financeiro;

import br.com.pactosolucoes.atualizadb.processo.AjustarAutorizacaoCobranca;
import br.com.pactosolucoes.autorizacaocobranca.modelo.TipoAutorizacaoCobrancaEnum;
import negocio.comuns.basico.EmpresaVO;
import negocio.comuns.financeiro.ContaCorrenteVO;
import negocio.comuns.financeiro.ConvenioCobrancaVO;
import negocio.comuns.financeiro.TipoRemessaVO;
import negocio.comuns.financeiro.enumerador.*;
import negocio.comuns.plano.ConvenioCobrancaEmpresaVO;
import negocio.comuns.utilitarias.Calendario;
import negocio.comuns.utilitarias.ConsistirException;
import negocio.comuns.utilitarias.Ordenacao;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisValidacao;
import negocio.facade.jdbc.arquitetura.SuperEntidade;
import negocio.facade.jdbc.arquitetura.SuperFacadeJDBC;
import negocio.facade.jdbc.basico.Empresa;
import negocio.facade.jdbc.plano.ConvenioCobrancaEmpresa;
import negocio.facade.jdbc.utilitarias.Conexao;
import negocio.interfaces.financeiro.ConvenioCobrancaInterfaceFacade;
import org.apache.commons.io.input.ReversedLinesFileReader;
import org.json.JSONArray;
import org.json.JSONObject;
import servicos.impl.maxiPago.AdquirenteMaxiPagoEnum;
import servicos.impl.redepay.ERedeStatusConciliacaoEnum;
import servicos.util.ExecuteRequestHttpService;

import java.io.BufferedReader;
import java.io.File;
import java.io.IOException;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.io.OutputStreamWriter;
import java.net.HttpURLConnection;
import java.net.URL;
import java.nio.charset.Charset;
import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.sql.Statement;
import java.sql.Types;
import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * Classe de persistência que encapsula todas as operações de manipulação dos dados da classe <code>ConvenioCobrancaVO</code>.
 * Responsável por implementar operações como incluir, alterar, excluir e consultar pertinentes a classe <code>ConvenioCobrancaVO</code>.
 * Encapsula toda a interação com o banco de dados.
 * @see ConvenioCobrancaVO
 * @see SuperEntidade
 */
public class ConvenioCobranca extends SuperEntidade implements ConvenioCobrancaInterfaceFacade {

    private static final String API_CIELO_AFFILIATE = "https://api2.cielo.com.br";

    public ConvenioCobranca() throws Exception {
        super();
        setIdEntidade("ConvenioCobranca");
    }

    public ConvenioCobranca(Connection con) throws Exception {
        super(con);
        setIdEntidade("ConvenioCobranca");
    }

    /**
     * Operação responsável por retornar um novo objeto da classe <code>ConvenioCobrancaVO</code>.
     */
    public ConvenioCobrancaVO novo() throws Exception {
        incluir(getIdEntidade());
        return new ConvenioCobrancaVO();
    }

    /**
     * Operação responsável por incluir no banco de dados um objeto da classe <code>ConvenioCobrancaVO</code>.
     * Primeiramente valida os dados (<code>validarDados</code>) do objeto. Verifica a conexão com o banco de dados e a permissão do usuário
     * para realizar esta operacão na entidade.
     * Isto, através da operação <code>incluir</code> da superclasse.
     * @param obj  Objeto da classe <code>ConvenioCobrancaVO</code> que será gravado no banco de dados.
     * @exception Exception Caso haja problemas de conexão, restrição de acesso ou validação de dados.
     */
    public void incluir(ConvenioCobrancaVO obj) throws Exception {
        try {
            ConvenioCobrancaVO.validarDados(obj);
            if (obj.getTipo() != null && obj.getTipo().isPix()) {
                existeConvenioPixMesmaChaveNaMesmaEmpresa(obj,true);
            }
            incluir(getIdEntidade());
            obj.realizarUpperCaseDados();

            String sql = "INSERT INTO ConvenioCobranca( descricao, multa, juros, "
                    + "extensaoArquivoRemessa, extensaoArquivoRetorno, diretorioGravaRemessa, "
                    + "diretorioLerRetorno, mensagem, sequencialDoArquivo, contaEmpresa, "
                    + "numeroContrato, carteira, variacao, tipoRemessa, tipoConvenio, banco,"
                    + " hostSFTP, portSFTP, userSFTP, pwdSFTP, diretorioRemotoTIVIT_OUT, diretorioRemotoTIVIT_IN, enviarRemessaSFTPNow,"
                    + "diretorioLocalTIVIT,diretorioLocalUploadTIVIT,diretorioLocalDownloadTIVIT, chaveGETNET, nossaChave, nossaSenha,"
                    + "nomechavegetnet,nomenossachave, instrucoesboleto, sitesboleto, identificadorClienteEmpresa, carteiraBoleto, codigoTransmissao,"
                    + "diretorioGETNET_CANCELAMENTO_OUT, diretorioGETNET_CANCELAMENTO_IN,numeroLogico,sequencialArquivoCancelamento, situacao, sequencialItem,chaveapi, "
                    + "props, usarSequencialUnico, codigoAutenticacao01, codigoAutenticacao02, permitirReceberBoletoAposVencimento, dataCadastro, "
                    + "tarifaBoletoSeparadaValorPago, adicionarData, buscarCentralPacto, mascaraDataArquivo, "
                    + "nomechavebin, chavebin, dataChaveAPI, codigoAutenticacao03, processarRemessasAutomatico, gerarArquivoUnico, diasParaCompensacao, cnpj, operacao, diasAntecipacaoRemessaDCO, "
                    + "nrDiasProtesto, nrDiasBaixaAutomatica, agruparPorPessoaParcela, diasLimiteVencimentoParcelaDCO, omitirSequencialArquivo, diretorioRemotoExtrato, limiteItensRemessa, "
                    + "nomenclaturaArquivo, chavepjbank,credencialpjbank, "
                    + "utilizaExtrato, hostSFTPExtrato, portSFTPExtrato, userSFTPExtrato, pwdSFTPExtrato, nomenclaturaExtrato, mascaraDataDiretorioRemotoExtrato, diretorioLocalExtrato, diretorioLocalLogExtrato,"
                    + "mascaraDataDiretorioLocalExtrato, obterCentralPactoExtrato, ignorarValidacoesExtrato, vanExtrato, numeroCompromisso,porcentagemDescontoBoleto, cieloclientid, "
                    + "tipoParcelamentoStone, ambiente, bloquearCobrancaAutomatica, somenteExtrato, pactopay, pix_chave, pix_app_key, pix_client_id, pix_client_secret, pix_basic_auth, "
                    + "pix_expiracao, currency, layoutBoletoOnline, codigoAutenticacao04, codigoAutenticacao05, codigoAutenticacao06, codigoAutenticacao07, adquirenteMaxiPago, tipoboletopjbank, "
                    + "diasExpirarPix, enviarNotificacoes, gerarMultaEJurosRemessaItauCNAB400, apresentarInativoNoPactoPay, verificacaoZeroDollar, "
                    + "statusConciliacaoRedeOnline, requestIdConciliacaoRedeOnline, registrarBoletoOnlineImpressao, usaSplitPagamentoStoneV5, tipoCredencialStoneEnum) "
                    + "VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, "
                    + "?, ?, ?,?,?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, "
                    + "?, ?, ?, ?, ?,?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)";
            try (PreparedStatement sqlInserir = con.prepareStatement(sql)) {
                int i = 1;
                sqlInserir.setString(i++, obj.getDescricao());
                sqlInserir.setDouble(i++, obj.getMulta());
                sqlInserir.setDouble(i++, obj.getJuros());
                sqlInserir.setString(i++, obj.getExtensaoArquivoRemessa());
                sqlInserir.setString(i++, obj.getExtensaoArquivoRetorno());
                sqlInserir.setString(i++, obj.getDiretorioGravaRemessa());
                sqlInserir.setString(i++, obj.getDiretorioLerRetorno());
                sqlInserir.setString(i++, obj.getMensagem());
                sqlInserir.setInt(i++, obj.getSequencialDoArquivo());
                if (obj.getContaEmpresa().getCodigo() != 0) {
                    sqlInserir.setInt(i++, obj.getContaEmpresa().getCodigo());
                } else {
                    sqlInserir.setNull(i++, 0);
                }
                sqlInserir.setString(i++, obj.getNumeroContrato());
                sqlInserir.setInt(i++, obj.getCarteira());
                sqlInserir.setInt(i++, obj.getVariacao());
                if (obj.getTipoRemessa().getCodigo() != 0) {
                    sqlInserir.setInt(i++, obj.getTipoRemessa().getCodigo());
                } else {
                    sqlInserir.setNull(i++, 0);
                }
                sqlInserir.setInt(i++, obj.getTipo().getCodigo());
                resolveFKNull(sqlInserir, i++, obj.getBanco().getCodigo());

                sqlInserir.setString(i++, obj.getHostSFTP());
                sqlInserir.setString(i++, obj.getPortSFTP());
                sqlInserir.setString(i++, obj.getUserSFTP());
                sqlInserir.setString(i++, obj.getPwdSFTP());
                sqlInserir.setString(i++, obj.getDiretorioRemotoTIVIT_OUT());
                sqlInserir.setString(i++, obj.getDiretorioRemotoTIVIT_IN());
                sqlInserir.setBoolean(i++, obj.getEnviarRemessaSFTPNow());
                sqlInserir.setString(i++, obj.getDiretorioLocalTIVIT());
                sqlInserir.setString(i++, obj.getDiretorioLocalUploadTIVIT());
                sqlInserir.setString(i++, obj.getDiretorioLocalDownloadTIVIT());
                sqlInserir.setString(i++, obj.getChaveGETNET());
                sqlInserir.setString(i++, obj.getNossaChave());
                sqlInserir.setString(i++, obj.getNossaSenha());
                sqlInserir.setString(i++, obj.getNomeChaveGETNET());
                sqlInserir.setString(i++, obj.getNomeNossaChave());
                sqlInserir.setString(i++, obj.getInstrucoesBoleto());
                sqlInserir.setString(i++, obj.getSitesBoleto());
                sqlInserir.setInt(i++, obj.getIdentificadorClienteEmpresa().getCodigo());
                if (obj.getCarteiraBoleto() == null) {
                    sqlInserir.setNull(i++, 0);
                } else {
                    sqlInserir.setInt(i++, obj.getCarteiraBoleto());
                }
                sqlInserir.setString(i++, obj.getCodigoTransmissao());
                sqlInserir.setString(i++, obj.getDiretorioGETNET_CANCELAMENTO_OUT());
                sqlInserir.setString(i++, obj.getDiretorioGETNET_CANCELAMENTO_IN());
                sqlInserir.setString(i++, obj.getNumeroLogico().trim());
                sqlInserir.setInt(i++, obj.getSequencialArquivoCancelamento());
                sqlInserir.setInt(i++, obj.getSituacao().getCodigo());
                sqlInserir.setInt(i++, obj.getSequencialItem());
                sqlInserir.setString(i++, obj.getChaveAPI());
                sqlInserir.setString(i++, obj.getProps().toString());
                sqlInserir.setBoolean(i++, obj.getUsarSequencialUnico());
                sqlInserir.setString(i++, Uteis.removerEspacosInicioFimString(obj.getCodigoAutenticacao01()));
                sqlInserir.setString(i++, Uteis.removerEspacosInicioFimString(obj.getCodigoAutenticacao02()));
                sqlInserir.setBoolean(i++, obj.isPermitirReceberBoletoAposVencimento());
                sqlInserir.setTimestamp(i++, Uteis.getDataJDBCTimestamp(Calendario.hoje()));
                sqlInserir.setBoolean(i++, obj.isTarifaBoletoSeparadaValorPago());
                sqlInserir.setBoolean(i++, obj.isAdicionarData());
                sqlInserir.setBoolean(i++, obj.isBuscarCentralPacto());
                sqlInserir.setString(i++, obj.getMascaraDataArquivo());
                sqlInserir.setString(i++, obj.getNomeChaveBIN());
                sqlInserir.setString(i++, obj.getChaveBIN());
                sqlInserir.setTimestamp(i++, Uteis.getDataJDBCTimestamp(obj.getDataChaveAPI()));
                sqlInserir.setString(i++, Uteis.removerEspacosInicioFimString(obj.getCodigoAutenticacao03()));
                sqlInserir.setBoolean(i++, obj.isProcessarRemessasAutomatico());
                sqlInserir.setBoolean(i++, obj.isGerarArquivoUnico());
                sqlInserir.setInt(i++, obj.getDiasParaCompensacao());
                sqlInserir.setString(i++, obj.getCnpj());
                sqlInserir.setString(i++, obj.getOperacao());
                sqlInserir.setInt(i++, obj.getDiasAntecipacaoRemessaDCO());
                sqlInserir.setInt(i++, obj.getNrDiasProtesto());
                sqlInserir.setInt(i++, obj.getNrDiasBaixaAutomatica());
                sqlInserir.setBoolean(i++, obj.isAgruparPorPessoaParcela());
                sqlInserir.setInt(i++, obj.getDiasLimiteVencimentoParcelaDCO());
                sqlInserir.setBoolean(i++, obj.isOmitirSequencialArquivo());
                sqlInserir.setString(i++, obj.getDiretorioRemotoExtrato());
                sqlInserir.setInt(i++, obj.getLimiteItensRemessa());
                sqlInserir.setInt(i++, obj.getNomenclaturaArquivo().getCodigo());
                sqlInserir.setString(i++, obj.getChavePJBank());
                sqlInserir.setString(i++,obj.getCredencialPJBank());
                sqlInserir.setBoolean(i++, obj.isUtilizaExtrato());
                sqlInserir.setString(i++, obj.getHostSFTPExtrato());
                sqlInserir.setInt(i++, obj.getPortSFTPExtrato());
                sqlInserir.setString(i++, obj.getUserSFTPExtrato());
                sqlInserir.setString(i++, obj.getPwdSFTPExtrato());
                sqlInserir.setString(i++, obj.getNomenclaturaExtrato());
                sqlInserir.setString(i++, obj.getMascaraDataDiretorioRemotoExtrato());
                sqlInserir.setString(i++, obj.getDiretorioLocalExtrato());
                sqlInserir.setString(i++, obj.getDiretorioLocalLogExtrato());
                sqlInserir.setString(i++, obj.getMascaraDataDiretorioLocalExtrato());
                sqlInserir.setBoolean(i++, obj.isObterCentralPactoExtrato());
                sqlInserir.setBoolean(i++, obj.isIgnorarValidacoesExtrato());
                sqlInserir.setInt(i++, obj.getVanExtrato().getCodigo());
                sqlInserir.setString(i++, obj.getNumeroCompromisso());
                sqlInserir.setDouble(i++, obj.getDescontoBoleto());
                sqlInserir.setString(i++, obj.getCieloClientId());
                sqlInserir.setString(i++, obj.getTipoParcelamentoStone());
                sqlInserir.setInt(i++, obj.getAmbiente().getCodigo());
                sqlInserir.setBoolean(i++, obj.isBloquearCobrancaAutomatica());
                sqlInserir.setBoolean(i++, obj.isSomenteExtrato());
                sqlInserir.setBoolean(i++, obj.isPactoPay());
                sqlInserir.setString(i++, Uteis.removerEspacosInicioFimString(obj.getPixChave()));
                sqlInserir.setString(i++, Uteis.removerEspacosInicioFimString(obj.getPixAppKey()));
                sqlInserir.setString(i++, Uteis.removerEspacosInicioFimString(obj.getPixClientId()));
                sqlInserir.setString(i++, Uteis.removerEspacosInicioFimString(obj.getPixClientSecret()));
                sqlInserir.setString(i++, Uteis.removerEspacosInicioFimString(obj.getPixBasicAuth()));
                if (obj.getPixExpiracao() == null) {
                    sqlInserir.setNull(i++, 0);
                } else {
                    sqlInserir.setLong(i++, obj.getPixExpiracao());
                }
                if (obj.getCurrencyConvenioEnum() == null) {
                    sqlInserir.setNull(i++, 0);
                } else {
                    sqlInserir.setString(i++, obj.getCurrencyConvenioEnum().getDescricao());
                }
                sqlInserir.setString(i++, obj.getLayoutBoletoOnline().getId());
                sqlInserir.setString(i++, Uteis.removerEspacosInicioFimString(obj.getCodigoAutenticacao04()));
                sqlInserir.setString(i++, Uteis.removerEspacosInicioFimString(obj.getCodigoAutenticacao05()));
                sqlInserir.setString(i++, Uteis.removerEspacosInicioFimString(obj.getCodigoAutenticacao06()));
                sqlInserir.setString(i++, Uteis.removerEspacosInicioFimString(obj.getCodigoAutenticacao07()));
                sqlInserir.setInt(i++, obj.getAdquirenteMaxiPago().getId());
                sqlInserir.setInt(i++, obj.getTipoBoletoPJBank().getId());
                sqlInserir.setInt(i++, obj.getDiasExpirarPix());
                sqlInserir.setBoolean(i++, obj.isEnviarNotificacoes());
                sqlInserir.setBoolean(i++, obj.isGerarMultaEJurosRemessaItauCNAB400());
                sqlInserir.setBoolean(i++, obj.getApresentarInativoNoPactoPay());
                sqlInserir.setBoolean(i++, obj.isVerificacaoZeroDollar());
                sqlInserir.setInt(i++, obj.getStatusConciliacaoRedeOnline().getId());
                sqlInserir.setString(i++, obj.getRequestIdConciliacaoRedeOnline());
                sqlInserir.setBoolean(i++, obj.isRegistrarBoletoOnlineSomenteNaImpressao());
                sqlInserir.setBoolean(i++, obj.isUsaSplitPagamentoStoneV5());
                sqlInserir.setInt(i++, obj.getTipoCredencialStoneEnum().getCodigo());

                sqlInserir.execute();
            }
            obj.setCodigo(obterValorChavePrimariaCodigo());
            obj.setNovoObj(false);

            ConvenioCobrancaEmpresa convenioCobrancaEmpresaDAO = new ConvenioCobrancaEmpresa(con);
            convenioCobrancaEmpresaDAO.alterarPorConvenioCobranca(obj);
            convenioCobrancaEmpresaDAO = null;
        } catch (Exception e) {
            obj.setNovoObj(true);
            throw e;
        }
    }

    /**
     * Operação responsável por alterar no BD os dados de um objeto da classe <code>ConvenioCobrancaVO</code>.
     * Sempre utiliza a chave primária da classe como atributo para localização do registro a ser alterado.
     * Primeiramente valida os dados (<code>validarDados</code>) do objeto. Verifica a conexão com o banco de dados e a permissão do usuário
     * para realizar esta operacão na entidade.
     * Isto, através da operação <code>alterar</code> da superclasse.
     * @param obj    Objeto da classe <code>ConvenioCobrancaVO</code> que será alterada no banco de dados.
     * @exception Exception Caso haja problemas de conexão, restrição de acesso ou validação de dados.
     */
    public void alterar(ConvenioCobrancaVO obj) throws Exception {
        ConvenioCobrancaVO.validarDados(obj);
        if (obj.getTipo() != null && obj.getTipo().isPix()) {
            existeConvenioPixMesmaChaveNaMesmaEmpresa(obj,false);
        }
        alterar(getIdEntidade());
        obj.realizarUpperCaseDados();
        String sql = "UPDATE ConvenioCobranca set descricao=?, "
                + "multa=?, juros=?, extensaoArquivoRemessa=?, extensaoArquivoRetorno=?, "
                + "diretorioGravaRemessa=?, diretorioLerRetorno=?, mensagem=?, "
                + "sequencialDoArquivo=?, contaEmpresa=?, numeroContrato=?, "
                + "carteira=?, variacao=?, tipoRemessa=?, tipoConvenio=?, banco=?,"
                + "hostSFTP=?, portSFTP=?, userSFTP=?, pwdSFTP=?, "
                + "diretorioRemotoTIVIT_OUT=?, diretorioRemotoTIVIT_IN=?, enviarRemessaSFTPNow=?,"
                + "diretorioLocalTIVIT  =? ,diretorioLocalUploadTIVIT =? ,diretorioLocalDownloadTIVIT =?,"
                + "chaveGETNET = ?, nossaChave = ?, nossaSenha = ?, nomechavegetnet = ? ,nomenossachave = ?, "
                + "instrucoesboleto = ?, sitesboleto = ?, identificadorClienteEmpresa = ?, carteiraBoleto = ?, "
                + "codigoTransmissao = ?, diretorioGETNET_CANCELAMENTO_OUT = ?, diretorioGETNET_CANCELAMENTO_IN = ?,"
                + "numeroLogico = ? , sequencialArquivoCancelamento = ?, situacao = ?, chaveapi = ?, props = ?,"
                + "sequencialItem = ?, usarSequencialUnico = ?, codigoAutenticacao01 = ?, codigoAutenticacao02 = ?,"
                + "permitirReceberBoletoAposVencimento = ?, tarifaBoletoSeparadaValorPago = ?, adicionarData = ?, "
                + "buscarCentralPacto = ?, mascaraDataArquivo= ?, nomechavebin = ?, chavebin = ?, dataChaveAPI = ?,"
                + "codigoAutenticacao03 = ?, processarRemessasAutomatico = ?, gerarArquivoUnico = ?,"
                + "diasParaCompensacao = ?, cnpj = ?, operacao = ?, diasAntecipacaoRemessaDCO = ?, nrDiasProtesto = ?,"
                + "nrDiasBaixaAutomatica = ?, agruparPorPessoaParcela = ?, diasLimiteVencimentoParcelaDCO = ?, "
                + "omitirSequencialArquivo = ?, diretorioRemotoExtrato = ?, limiteItensRemessa = ?, nomenclaturaArquivo = ?,"
                + "chavepjbank = ?, credencialpjbank = ?, utilizaExtrato = ?,"
                + "hostSFTPExtrato = ?, portSFTPExtrato = ?, userSFTPExtrato = ?, "
                + "pwdSFTPExtrato = ?, nomenclaturaExtrato = ?, mascaraDataDiretorioRemotoExtrato = ?, "
                + "diretorioLocalExtrato = ?, diretorioLocalLogExtrato = ?, mascaraDataDiretorioLocalExtrato = ?, obterCentralPactoExtrato = ?, "
                + "ignorarValidacoesExtrato = ?, vanExtrato = ?, numeroCompromisso = ?, porcentagemDescontoBoleto = ?,"
                + "cieloclientid = ?, tipoParcelamentoStone = ?, ambiente = ?, bloquearCobrancaAutomatica = ?,"
                + "somenteExtrato = ?, accesstokencielo = ?, refreshtokencielo = ?, registerIDCredenciamentoCielo = ?, "
                + "pactopay = ?, pix_client_id = ?, pix_client_secret = ?, pix_app_key = ?, pix_basic_auth = ?, pix_chave = ?, pix_expiracao = ?, "
                + "currency = ?, layoutBoletoOnline = ?, codigoAutenticacao04 = ?, codigoAutenticacao05 = ?, codigoAutenticacao06 = ?, codigoAutenticacao07 = ?, "
                + "adquirenteMaxiPago = ?, tipoboletopjbank = ?, diasExpirarPix = ?, enviarNotificacoes = ?, gerarMultaEJurosRemessaItauCNAB400 = ?, apresentarInativoNoPactoPay = ?, verificacaoZeroDollar = ?, "
                + "statusConciliacaoRedeOnline = ?, requestIdConciliacaoRedeOnline = ?, registrarBoletoOnlineImpressao = ?, usaSplitPagamentoStoneV5 = ?, tipoCredencialStoneEnum = ?  "
                + "WHERE codigo = ?";
        try (PreparedStatement sqlAlterar = con.prepareStatement(sql)) {
            int i = 1;
            sqlAlterar.setString(i++, obj.getDescricao());
            sqlAlterar.setDouble(i++, obj.getMulta());
            sqlAlterar.setDouble(i++, obj.getJuros());
            sqlAlterar.setString(i++, obj.getExtensaoArquivoRemessa());
            sqlAlterar.setString(i++, obj.getExtensaoArquivoRetorno());
            sqlAlterar.setString(i++, obj.getDiretorioGravaRemessa());
            sqlAlterar.setString(i++, obj.getDiretorioLerRetorno());
            sqlAlterar.setString(i++, obj.getMensagem());
            sqlAlterar.setInt(i++, obj.getSequencialDoArquivo());
            if (obj.getContaEmpresa().getCodigo() != 0) {
                sqlAlterar.setInt(i++, obj.getContaEmpresa().getCodigo());
            } else {
                sqlAlterar.setNull(i++, 0);
            }
            sqlAlterar.setString(i++, obj.getNumeroContrato());
            sqlAlterar.setInt(i++, obj.getCarteira());
            sqlAlterar.setInt(i++, obj.getVariacao());
            if (obj.getTipoRemessa().getCodigo() != 0) {
                sqlAlterar.setInt(i++, obj.getTipoRemessa().getCodigo());
            } else {
                sqlAlterar.setNull(i++, 0);
            }
            sqlAlterar.setInt(i++, obj.getTipo().getCodigo());
            ConvenioCobranca.resolveFKNull(sqlAlterar, i++, obj.getBanco().getCodigo());

            sqlAlterar.setString(i++, obj.getHostSFTP());
            sqlAlterar.setString(i++, obj.getPortSFTP());
            sqlAlterar.setString(i++, obj.getUserSFTP());
            sqlAlterar.setString(i++, obj.getPwdSFTP());
            sqlAlterar.setString(i++, obj.getDiretorioRemotoTIVIT_OUT());
            sqlAlterar.setString(i++, obj.getDiretorioRemotoTIVIT_IN());
            sqlAlterar.setBoolean(i++, obj.getEnviarRemessaSFTPNow());
            sqlAlterar.setString(i++, obj.getDiretorioLocalTIVIT());
            sqlAlterar.setString(i++, obj.getDiretorioLocalUploadTIVIT());
            sqlAlterar.setString(i++, obj.getDiretorioLocalDownloadTIVIT());
            sqlAlterar.setString(i++, obj.getChaveGETNET());
            sqlAlterar.setString(i++, obj.getNossaChave());
            sqlAlterar.setString(i++, obj.getNossaSenha());
            sqlAlterar.setString(i++, obj.getNomeChaveGETNET());
            sqlAlterar.setString(i++, obj.getNomeNossaChave());
            sqlAlterar.setString(i++, obj.getInstrucoesBoleto());
            sqlAlterar.setString(i++, obj.getSitesBoleto());
            sqlAlterar.setInt(i++, obj.getIdentificadorClienteEmpresa().getCodigo());
            if (obj.getCarteiraBoleto() == null) {
                sqlAlterar.setNull(i++, Types.NULL);
            } else {
                sqlAlterar.setInt(i++, obj.getCarteiraBoleto());
            }
            sqlAlterar.setString(i++, obj.getCodigoTransmissao());
            sqlAlterar.setString(i++, obj.getDiretorioGETNET_CANCELAMENTO_OUT());
            sqlAlterar.setString(i++, obj.getDiretorioGETNET_CANCELAMENTO_IN());
            sqlAlterar.setString(i++, obj.getNumeroLogico());
            sqlAlterar.setInt(i++, obj.getSequencialArquivoCancelamento());
            sqlAlterar.setInt(i++, obj.getSituacao().getCodigo());
            sqlAlterar.setString(i++, obj.getChaveAPI());
            sqlAlterar.setString(i++, obj.getProps().toString());
            sqlAlterar.setInt(i++, obj.getSequencialItem());
            sqlAlterar.setBoolean(i++, obj.getUsarSequencialUnico());
            sqlAlterar.setString(i++, Uteis.removerEspacosInicioFimString(obj.getCodigoAutenticacao01()));
            sqlAlterar.setString(i++, Uteis.removerEspacosInicioFimString(obj.getCodigoAutenticacao02()));
            sqlAlterar.setBoolean(i++, obj.isPermitirReceberBoletoAposVencimento());
            sqlAlterar.setBoolean(i++, obj.isTarifaBoletoSeparadaValorPago());
            sqlAlterar.setBoolean(i++, obj.isAdicionarData());
            sqlAlterar.setBoolean(i++, obj.isBuscarCentralPacto());
            sqlAlterar.setString(i++, obj.getMascaraDataArquivo());
            sqlAlterar.setString(i++, obj.getNomeChaveBIN());
            sqlAlterar.setString(i++, obj.getChaveBIN());
            sqlAlterar.setTimestamp(i++, Uteis.getDataJDBCTimestamp(obj.getDataChaveAPI()));
            sqlAlterar.setString(i++, Uteis.removerEspacosInicioFimString(obj.getCodigoAutenticacao03()));
            sqlAlterar.setBoolean(i++, obj.isProcessarRemessasAutomatico());
            sqlAlterar.setBoolean(i++, obj.isGerarArquivoUnico());
            sqlAlterar.setInt(i++, obj.getDiasParaCompensacao());
            sqlAlterar.setString(i++, obj.getCnpj());
            sqlAlterar.setString(i++, obj.getOperacao());
            sqlAlterar.setInt(i++, obj.getDiasAntecipacaoRemessaDCO());
            sqlAlterar.setInt(i++, obj.getNrDiasProtesto());
            sqlAlterar.setInt(i++, obj.getNrDiasBaixaAutomatica());
            sqlAlterar.setBoolean(i++, obj.isAgruparPorPessoaParcela());
            sqlAlterar.setInt(i++, obj.getDiasLimiteVencimentoParcelaDCO());
            sqlAlterar.setBoolean(i++, obj.isOmitirSequencialArquivo());
            sqlAlterar.setString(i++, obj.getDiretorioRemotoExtrato());
            sqlAlterar.setInt(i++, obj.getLimiteItensRemessa());
            sqlAlterar.setInt(i++, obj.getNomenclaturaArquivo().getCodigo());
            sqlAlterar.setString(i++, obj.getChavePJBank());
            sqlAlterar.setString(i++, obj.getCredencialPJBank());
            sqlAlterar.setBoolean(i++, obj.isUtilizaExtrato());
            sqlAlterar.setString(i++, obj.getHostSFTPExtrato());
            sqlAlterar.setInt(i++, obj.getPortSFTPExtrato());
            sqlAlterar.setString(i++, obj.getUserSFTPExtrato());
            sqlAlterar.setString(i++, obj.getPwdSFTPExtrato());
            sqlAlterar.setString(i++, obj.getNomenclaturaExtrato());
            sqlAlterar.setString(i++, obj.getMascaraDataDiretorioRemotoExtrato());
            sqlAlterar.setString(i++, obj.getDiretorioLocalExtrato());
            sqlAlterar.setString(i++, obj.getDiretorioLocalLogExtrato());
            sqlAlterar.setString(i++, obj.getMascaraDataDiretorioLocalExtrato());
            sqlAlterar.setBoolean(i++, obj.isObterCentralPactoExtrato());
            sqlAlterar.setBoolean(i++, obj.isIgnorarValidacoesExtrato());
            sqlAlterar.setInt(i++, obj.getVanExtrato().getCodigo());
            sqlAlterar.setString(i++, obj.getNumeroCompromisso());
            sqlAlterar.setDouble(i++, obj.getDescontoBoleto());
            sqlAlterar.setString(i++, obj.getCieloClientId());
            sqlAlterar.setString(i++, obj.getTipoParcelamentoStone());
            sqlAlterar.setInt(i++, obj.getAmbiente().getCodigo());
            sqlAlterar.setBoolean(i++, obj.isBloquearCobrancaAutomatica());
            sqlAlterar.setBoolean(i++, obj.isSomenteExtrato());
            sqlAlterar.setString(i++, obj.getAccessTokenCielo());
            sqlAlterar.setString(i++, obj.getRefreshTokenCielo());
            sqlAlterar.setString(i++, obj.getRegisterIDCredenciamentoCielo());
            sqlAlterar.setBoolean(i++, obj.isPactoPay());
            sqlAlterar.setString(i++, Uteis.removerEspacosInicioFimString(obj.getPixClientId()));
            sqlAlterar.setString(i++, Uteis.removerEspacosInicioFimString(obj.getPixClientSecret()));
            sqlAlterar.setString(i++, Uteis.removerEspacosInicioFimString(obj.getPixAppKey()));
            sqlAlterar.setString(i++, Uteis.removerEspacosInicioFimString(obj.getPixBasicAuth()));
            sqlAlterar.setString(i++,Uteis.removerEspacosInicioFimString(obj.getPixChave()));
            sqlAlterar.setInt(i++, obj.getPixExpiracao());
            if (obj.getCurrencyConvenioEnum() == null) {
                sqlAlterar.setNull(i++, Types.NULL);
            } else {
                sqlAlterar.setString(i++, obj.getCurrencyConvenioEnum().getDescricao());
            }
            sqlAlterar.setString(i++, obj.getLayoutBoletoOnline().getId());
            sqlAlterar.setString(i++, Uteis.removerEspacosInicioFimString(obj.getCodigoAutenticacao04()));
            sqlAlterar.setString(i++, Uteis.removerEspacosInicioFimString(obj.getCodigoAutenticacao05()));
            sqlAlterar.setString(i++, Uteis.removerEspacosInicioFimString(obj.getCodigoAutenticacao06()));
            sqlAlterar.setString(i++, Uteis.removerEspacosInicioFimString(obj.getCodigoAutenticacao07()));
            sqlAlterar.setInt(i++, obj.getAdquirenteMaxiPago().getId());
            sqlAlterar.setInt(i++, obj.getTipoBoletoPJBank().getId());
            sqlAlterar.setInt(i++, obj.getDiasExpirarPix());
            sqlAlterar.setBoolean(i++, obj.isEnviarNotificacoes());
            sqlAlterar.setBoolean(i++, obj.isGerarMultaEJurosRemessaItauCNAB400());
            sqlAlterar.setBoolean(i++, obj.getApresentarInativoNoPactoPay());
            sqlAlterar.setBoolean(i++, obj.isVerificacaoZeroDollar());
            sqlAlterar.setInt(i++, obj.getStatusConciliacaoRedeOnline().getId());
            sqlAlterar.setString(i++, obj.getRequestIdConciliacaoRedeOnline());
            sqlAlterar.setBoolean(i++, obj.isRegistrarBoletoOnlineSomenteNaImpressao());
            sqlAlterar.setBoolean(i++, obj.isUsaSplitPagamentoStoneV5());
            sqlAlterar.setInt(i++, obj.getTipoCredencialStoneEnum().getCodigo());

            sqlAlterar.setInt(i++, obj.getCodigo());
            sqlAlterar.execute();
        }

        ConvenioCobrancaEmpresa convenioCobrancaEmpresaDAO = new ConvenioCobrancaEmpresa(con);
        convenioCobrancaEmpresaDAO.alterarPorConvenioCobranca(obj);
        convenioCobrancaEmpresaDAO = null;
    }

    /**
     * Operação responsável por excluir no BD um objeto da classe <code>ConvenioCobrancaVO</code>.
     * Sempre localiza o registro a ser excluído através da chave primária da entidade.
     * Primeiramente verifica a conexão com o banco de dados e a permissão do usuário
     * para realizar esta operacão na entidade.
     * Isto, através da operação <code>excluir</code> da superclasse.
     * @param obj    Objeto da classe <code>ConvenioCobrancaVO</code> que será removido no banco de dados.
     * @exception Exception Caso haja problemas de conexão ou restrição de acesso.
     */
    public void excluir(ConvenioCobrancaVO obj) throws Exception {
        try {
            con.setAutoCommit(false);
            excluir(getIdEntidade());
            String sql = "DELETE FROM ConvenioCobranca WHERE codigo = ?";
            try (PreparedStatement sqlExcluir = con.prepareStatement(sql)) {
                sqlExcluir.setInt(1, obj.getCodigo());
                sqlExcluir.execute();
            }
            con.commit();
        } catch (Exception e) {
            con.rollback();
            con.setAutoCommit(true);
            throw e;
        } finally {
            con.setAutoCommit(true);
        }
    }

    private List<ConvenioCobrancaVO> consultarPorEmpresaSituacaoTipo(Integer empresa, final Integer[] tipos, SituacaoConvenioCobranca situacao,
                                                                    boolean controlarAcesso, Boolean somenteExtrato, int nivelMontarDados) throws Exception {
        consultar(getIdEntidade(), controlarAcesso);

        String condicaoTipos = "";
        if (tipos != null) {
            for (Integer tipo : tipos) {
                condicaoTipos += ",'" + tipo + "'";
            }
            condicaoTipos = condicaoTipos.replaceFirst(",", "");
        }

        StringBuilder sql = new StringBuilder();
        sql.append("select  \n");
        sql.append("cc.*, \n");
        sql.append("cce.empresa \n");
        sql.append("from conveniocobranca cc \n");
        sql.append("inner join conveniocobrancaempresa cce on cce.conveniocobranca = cc.codigo \n");
        sql.append("where 1 = 1 \n");
        if (!UteisValidacao.emptyNumber(empresa)) {
            sql.append("and cce.empresa = ").append(empresa).append("\n");
        }
        if (!UteisValidacao.emptyString(condicaoTipos)) {
            sql.append("and cc.tipoconvenio in (").append(condicaoTipos).append(") \n");
        }
        if (situacao != null) {
            sql.append("and cc.situacao = ").append(situacao.getCodigo()).append(" \n");
        }
        if (somenteExtrato != null) {
            sql.append("and cc.somenteExtrato = ").append(somenteExtrato).append(" \n");
        }
        sql.append("ORDER BY cce.empresa \n");
        try (Statement stm = con.createStatement()) {
            try (ResultSet tabelaResultado = stm.executeQuery(sql.toString())) {
                return montarDadosConsulta(tabelaResultado, nivelMontarDados, con);
            }
        }
    }

    public List<ConvenioCobrancaVO> consultarPorEmpresa(Integer empresa, boolean controlarAcesso, int nivelMontarDados) throws Exception {
        return consultarPorEmpresaSituacaoTipo(empresa, null, null, controlarAcesso, null, nivelMontarDados);
    }

    @Override
    public List<ConvenioCobrancaVO> consultarPorEmpresaESituacao(Integer empresa, boolean controlarAcesso, int nivelMontarDados, final Integer[] tipos, SituacaoConvenioCobranca situacao) throws Exception {
        return consultarPorEmpresaSituacaoTipo(empresa, tipos, situacao, controlarAcesso, null, nivelMontarDados);
    }

    @Override
    public List<ConvenioCobrancaVO> consultarPorEmpresaESituacao(Integer empresa, boolean controlarAcesso, int nivelMontarDados, final Integer[] tipos, SituacaoConvenioCobranca situacao, boolean somenteExtrato) throws Exception {
        return consultarPorEmpresaSituacaoTipo(empresa, tipos, situacao, controlarAcesso, somenteExtrato, nivelMontarDados);
    }

    public List<ConvenioCobrancaVO> consultarPorEmpresa(Integer empresa, SituacaoConvenioCobranca situacao, boolean controlarAcesso, Boolean somenteExtrato, int nivelMontarDados) throws Exception{
        return consultarPorEmpresaSituacaoTipo(empresa, null, situacao, controlarAcesso, somenteExtrato, nivelMontarDados);
    }

    @Override
    public List<ConvenioCobrancaVO> consultarPorEmpresa(Integer empresa, boolean controlarAcesso, int nivelMontarDados, final Integer[] tipos) throws Exception {
        return consultarPorEmpresaSituacaoTipo(empresa, tipos, null, controlarAcesso, null, nivelMontarDados);
    }

    @Override
    public List consultarPorTipos(final TipoConvenioCobrancaEnum[] tipos, int empresa, boolean controlarAcesso, int nivelMontarDados) throws Exception {
        return consultarPorTiposESituacao(tipos, empresa, null, controlarAcesso, nivelMontarDados, null);
    }

    @Override
    public List consultarPorTiposESituacao(final TipoConvenioCobrancaEnum[] tipos, Integer empresa, SituacaoConvenioCobranca situacao,
                                           boolean controlarAcesso, int nivelMontarDados, Boolean somenteExtrato) throws Exception {
        return consultarPorTiposESituacao(tipos, empresa, situacao, controlarAcesso, nivelMontarDados, 0, somenteExtrato);
    }

    @Override
    public List consultarPorTiposESituacao(final TipoConvenioCobrancaEnum[] tipos, Integer empresa, SituacaoConvenioCobranca situacao, boolean controlarAcesso,
                                           int nivelMontarDados, int codPessoa, Boolean somenteExtrato) throws Exception {

        String condicaoTipos = "";
        if (tipos != null) {
            condicaoTipos = Uteis.splitFromArray(tipos, false, "codigo");
        }

        StringBuilder sql = new StringBuilder();
        sql.append("select  \n");
        sql.append("cc.*, \n");
        sql.append("cce.empresa \n");
        sql.append("from conveniocobranca cc \n");
        sql.append("inner join conveniocobrancaempresa cce on cce.conveniocobranca = cc.codigo \n");
        if(codPessoa != 0){
            sql.append("inner join autorizacaocobrancacliente acc on acc.conveniocobranca = cc.codigo \n");
            sql.append("inner join cliente c on c.codigo = acc.cliente \n");
            sql.append("inner join pessoa p on p.codigo = c.pessoa \n");
        }
        sql.append("where 1 = 1 \n");
        if (!UteisValidacao.emptyNumber(empresa)) {
            sql.append("and cce.empresa = ").append(empresa).append("\n");
        }
        if (!UteisValidacao.emptyString(condicaoTipos)) {
            sql.append("and cc.tipoconvenio in (").append(condicaoTipos).append(") \n");
        }
        if (situacao != null) {
            sql.append("and cc.situacao = ").append(situacao.getCodigo()).append(" \n");
        }
        if(codPessoa != 0){
            sql.append("and p.codigo = ").append(codPessoa).append(" \n");
            sql.append("and acc.ativa = true").append(" \n");
        }
        if (somenteExtrato != null) {
            sql.append("and cc.somenteExtrato = ").append(somenteExtrato).append(" \n");
        }

        sql.append("ORDER BY cce.empresa \n");
        try (Statement stm = con.createStatement()) {
            try (ResultSet tabelaResultado = stm.executeQuery(sql.toString())) {
                return montarDadosConsulta(tabelaResultado, nivelMontarDados, con);
            }
        }
    }

    @Override
    public List consultarPorTiposESituacaoEAmbiente(final TipoConvenioCobrancaEnum[] tipos, Integer empresa, SituacaoConvenioCobranca situacao,
                                           int nivelMontarDados, Boolean somenteExtrato, AmbienteEnum ambienteEnum) throws Exception {

        String condicaoTipos = "";
        if (tipos != null) {
            condicaoTipos = Uteis.splitFromArray(tipos, false, "codigo");
        }

        StringBuilder sql = new StringBuilder();
        sql.append("select  \n");
        sql.append("cc.*, \n");
        sql.append("cce.empresa \n");
        sql.append("from conveniocobranca cc \n");
        sql.append("inner join conveniocobrancaempresa cce on cce.conveniocobranca = cc.codigo \n");
        sql.append("where 1 = 1 \n");
        if (!UteisValidacao.emptyNumber(empresa)) {
            sql.append("and cce.empresa = ").append(empresa).append("\n");
        }
        if (!UteisValidacao.emptyString(condicaoTipos)) {
            sql.append("and cc.tipoconvenio in (").append(condicaoTipos).append(") \n");
        }
        if (situacao != null) {
            sql.append("and cc.situacao = ").append(situacao.getCodigo()).append(" \n");
        }
        if (somenteExtrato != null) {
            sql.append("and cc.somenteExtrato = ").append(somenteExtrato).append(" \n");
        }
        if (ambienteEnum != null) {
            sql.append("and cc.ambiente = ").append(ambienteEnum.getCodigo()).append(" \n");
        }

        sql.append("ORDER BY cce.empresa \n");
        try (Statement stm = con.createStatement()) {
            try (ResultSet tabelaResultado = stm.executeQuery(sql.toString())) {
                return montarDadosConsulta(tabelaResultado, nivelMontarDados, con);
            }
        }
    }

    public List consultarPjBankPorEmpresa( int empresa, SituacaoConvenioCobranca situacao, int nivelMontarDados) throws Exception {
        StringBuilder sql = new StringBuilder();
        sql.append("select  \n");
        sql.append("cc.*, \n");
        sql.append("cce.empresa \n");
        sql.append("from conveniocobranca cc \n");
        sql.append("inner join conveniocobrancaempresa cce on cce.conveniocobranca = cc.codigo \n");
        sql.append("where 1 = 1 \n");
        sql.append("and cce.empresa = ").append(empresa).append("\n");
        sql.append("and cc.tipoconvenio = ").append(TipoConvenioCobrancaEnum.BOLETO_PJBANK.getCodigo()).append(" \n");
        if (situacao != null) {
            sql.append("and cc.situacao = ").append(situacao.getCodigo()).append(" \n");
        }
        sql.append("ORDER BY cce.empresa \n");
        try (Statement stm = con.createStatement()) {
            try (ResultSet tabelaResultado = stm.executeQuery(sql.toString())) {
                return montarDadosConsulta(tabelaResultado, nivelMontarDados, con);
            }
        }
    }

    public List consultarBBPorEmpresa( int empresa, SituacaoConvenioCobranca situacao, int nivelMontarDados) throws Exception {
        StringBuilder sql = new StringBuilder();
        sql.append("select  \n");
        sql.append("cc.*, \n");
        sql.append("cce.empresa \n");
        sql.append("from conveniocobranca cc \n");
        sql.append("inner join conveniocobrancaempresa cce on cce.conveniocobranca = cc.codigo \n");
        sql.append("where 1 = 1 \n");
        sql.append("and cce.empresa = ").append(empresa).append("\n");
        sql.append("and cc.tipoconvenio = ").append(TipoConvenioCobrancaEnum.BOLETO_BANCO_BRASIL_ONLINE.getCodigo()).append(" \n");
        if (situacao != null) {
            sql.append("and cc.situacao = ").append(situacao.getCodigo()).append(" \n");
        }
        sql.append("ORDER BY cce.empresa \n");
        try (Statement stm = con.createStatement()) {
            try (ResultSet tabelaResultado = stm.executeQuery(sql.toString())) {
                return montarDadosConsulta(tabelaResultado, nivelMontarDados, con);
            }
        }
    }

    /**
     * Responsável por montar os dados de vários objetos, resultantes de uma consulta ao banco de dados (<code>ResultSet</code>).
     * Faz uso da operação <code>montarDados</code> que realiza o trabalho para um objeto por vez.
     * @return  List Contendo vários objetos da classe <code>ConvenioCobrancaVO</code> resultantes da consulta.
     */
    public static List<ConvenioCobrancaVO> montarDadosConsulta(ResultSet tabelaResultado, int nivelMontarDados, Connection con) throws Exception {
        List<ConvenioCobrancaVO> vetResultado = new ArrayList<ConvenioCobrancaVO>();
        while (tabelaResultado.next()) {
            ConvenioCobrancaVO obj = montarDados(tabelaResultado, nivelMontarDados, con);
            vetResultado.add(obj);
        }
        return vetResultado;
    }

    public static ConvenioCobrancaVO montarDadosPix(ResultSet resultSet) throws SQLException {
        ConvenioCobrancaVO convenioCobrancaVO = new ConvenioCobrancaVO();
        return montarDadosPix(convenioCobrancaVO, resultSet);
    }

    public static ConvenioCobrancaVO montarDadosPix(ConvenioCobrancaVO convenioCobrancaVO, ResultSet resultSet) throws SQLException {
        convenioCobrancaVO.setPixClientId(resultSet.getString("pix_client_id"));
        convenioCobrancaVO.setPixClientSecret(resultSet.getString("pix_client_secret"));
        convenioCobrancaVO.setPixAppKey(resultSet.getString("pix_app_key"));
        convenioCobrancaVO.setPixBasicAuth(resultSet.getString("pix_basic_auth"));
        convenioCobrancaVO.setPixChave(resultSet.getString("pix_chave"));
        convenioCobrancaVO.setPixExpiracao(resultSet.getInt("pix_expiracao"));

        return convenioCobrancaVO;
    }

    public static ConvenioCobrancaVO montarDados(ResultSet dadosSQL, int nivelMontarDados, Connection con) throws Exception {
        ConvenioCobrancaVO obj = new ConvenioCobrancaVO();
        obj.setCodigo(dadosSQL.getInt("codigo"));
        obj.setDescricao(dadosSQL.getString("descricao"));
        try {
            obj.setSituacao(SituacaoConvenioCobranca.getPorCodigo(dadosSQL.getInt("situacao")));
        } catch (Exception ex) {}

        if (nivelMontarDados == Uteis.NIVELMONTARDADOS_TELA_ALUNO) {
            return obj;
        }

        obj.setCodigoAutenticacao01(dadosSQL.getString("codigoAutenticacao01"));
        obj.setCodigoAutenticacao02(dadosSQL.getString("codigoAutenticacao02"));
        obj.setCodigoAutenticacao03(dadosSQL.getString("codigoAutenticacao03"));
        obj.setCodigoAutenticacao04(dadosSQL.getString("codigoAutenticacao04"));
        obj.setCodigoAutenticacao05(dadosSQL.getString("codigoAutenticacao05"));
        obj.setCodigoAutenticacao06(dadosSQL.getString("codigoAutenticacao06"));
        obj.setCodigoAutenticacao07(dadosSQL.getString("codigoAutenticacao07"));

        if (nivelMontarDados == Uteis.NIVELMONTARDADOS_CREDENCIAIS_CONVENIO) {
            return obj;
        }

        /**
         * Primeiro deve produto a empresa por "convenio_empresa" caso ela não exista o sistema busca pela "empresa"
         * by Luiz Felipe 17/04/2020
         */
        try {
            obj.getEmpresa().setCodigo(dadosSQL.getInt("convenio_empresa"));
        } catch (Exception ex) {
            obj.getEmpresa().setCodigo(0);
        }

        if (UteisValidacao.emptyNumber(obj.getEmpresa().getCodigo())) {
            try {
                obj.getEmpresa().setCodigo(dadosSQL.getInt("empresa"));
            } catch (Exception ex) {
                obj.getEmpresa().setCodigo(0);
            }
        }

        obj.setMulta(dadosSQL.getDouble("multa"));
        obj.setJuros(dadosSQL.getDouble("juros"));
        obj.setExtensaoArquivoRemessa(dadosSQL.getString("extensaoArquivoRemessa"));
        obj.setExtensaoArquivoRetorno(dadosSQL.getString("extensaoArquivoRetorno"));
        obj.setDiretorioGravaRemessa(dadosSQL.getString("diretorioGravaRemessa"));
        obj.setDiretorioLerRetorno(dadosSQL.getString("diretorioLerRetorno"));
        obj.setMensagem(dadosSQL.getString("mensagem"));
        obj.setSequencialDoArquivo(dadosSQL.getInt("sequencialDoArquivo"));
        obj.setSequencialArquivoCancelamento(dadosSQL.getInt("sequencialArquivoCancelamento"));
        obj.getContaEmpresa().setCodigo(dadosSQL.getInt("contaEmpresa"));
        obj.setNumeroContrato(dadosSQL.getString("numeroContrato"));
        obj.setCarteira(dadosSQL.getInt("carteira"));
        obj.setVariacao(dadosSQL.getInt("variacao"));
        obj.getTipoRemessa().setCodigo(dadosSQL.getInt("tipoRemessa"));
        obj.setTipo(TipoConvenioCobrancaEnum.valueOf(dadosSQL.getInt("tipoConvenio")));
        obj.setIdentificadorClienteEmpresa(IdentificadorClienteEmpresaEnum.valueOf(dadosSQL.getInt("identificadorClienteEmpresa")));
        obj.setNumeroLogico(dadosSQL.getString("numeroLogico"));
        obj.setDiasAntecipacaoRemessaDCO(dadosSQL.getInt("diasAntecipacaoRemessaDCO"));
        obj.setAgruparPorPessoaParcela(dadosSQL.getBoolean("agruparPorPessoaParcela"));
        obj.setDiasLimiteVencimentoParcelaDCO(dadosSQL.getInt("diasLimiteVencimentoParcelaDCO"));
        obj.setRegistrarBoletoOnlineSomenteNaImpressao(dadosSQL.getBoolean("registrarBoletoOnlineImpressao"));
        obj.setNovoObj(false);
        try {
            obj.setHostSFTP(dadosSQL.getString("hostSFTP"));
            obj.setPortSFTP(dadosSQL.getString("portSFTP"));
            obj.setUserSFTP(dadosSQL.getString("userSFTP"));
            obj.setPwdSFTP(dadosSQL.getString("pwdSFTP"));
            obj.setDiretorioRemotoTIVIT_OUT(dadosSQL.getString("diretorioRemotoTIVIT_OUT"));
            obj.setDiretorioRemotoTIVIT_IN(dadosSQL.getString("diretorioRemotoTIVIT_IN"));
            obj.setEnviarRemessaSFTPNow(dadosSQL.getBoolean("enviarRemessaSFTPNow"));
            obj.setDiretorioLocalTIVIT(dadosSQL.getString("diretorioLocalTIVIT"));
            obj.setDiretorioLocalUploadTIVIT(dadosSQL.getString("diretorioLocalUploadTIVIT"));
            obj.setDiretorioLocalDownloadTIVIT(dadosSQL.getString("diretorioLocalDownloadTIVIT"));
            obj.setChaveGETNET(dadosSQL.getString("chaveGETNET"));
            obj.setNossaChave(dadosSQL.getString("nossaChave"));
            obj.setNossaSenha(dadosSQL.getString("nossaSenha"));
            obj.setNomeNossaChave(dadosSQL.getString("nomeNossaChave"));
            obj.setNomeChaveGETNET(dadosSQL.getString("nomeChaveGETNET"));
            obj.setInstrucoesBoleto(dadosSQL.getString("instrucoesboleto"));
            obj.setSitesBoleto(dadosSQL.getString("sitesboleto"));
            obj.setCarteiraBoleto(dadosSQL.getInt("carteiraboleto"));
            obj.setCodigoTransmissao(dadosSQL.getString("codigotransmissao"));
            obj.setDiretorioGETNET_CANCELAMENTO_OUT(dadosSQL.getString("diretorioGETNET_CANCELAMENTO_OUT"));
            obj.setDiretorioGETNET_CANCELAMENTO_IN(dadosSQL.getString("diretorioGETNET_CANCELAMENTO_IN"));
            obj.setSequencialItem(dadosSQL.getInt("sequencialItem"));
            obj.setUsarSequencialUnico(dadosSQL.getBoolean("usarSequencialUnico"));
            obj.setChaveAPI(dadosSQL.getString("chaveapi"));
            obj.setProps(Uteis.obterMapFromString(dadosSQL.getString("props")));
            obj.setPermitirReceberBoletoAposVencimento(dadosSQL.getBoolean("permitirReceberBoletoAposVencimento"));
            obj.setDataCadastro(dadosSQL.getTimestamp("dataCadastro"));
            obj.setTarifaBoletoSeparadaValorPago(dadosSQL.getBoolean("tarifaBoletoSeparadaValorPago"));
            obj.setAdicionarData(dadosSQL.getBoolean("adicionarData"));
            obj.setBuscarCentralPacto(dadosSQL.getBoolean("buscarCentralPacto"));
            obj.setMascaraDataArquivo(dadosSQL.getString("mascaraDataArquivo"));
            obj.setChaveBIN(dadosSQL.getString("chavebin"));
            obj.setNomeChaveBIN(dadosSQL.getString("nomechavebin"));
            obj.setDataChaveAPI(dadosSQL.getTimestamp("dataChaveAPI"));
            obj.setProcessarRemessasAutomatico(dadosSQL.getBoolean("processarRemessasAutomatico"));
            obj.setGerarArquivoUnico(dadosSQL.getBoolean("gerarArquivoUnico"));
            obj.setDiasParaCompensacao(dadosSQL.getInt("diasParaCompensacao"));
            obj.setCnpj(dadosSQL.getString("cnpj"));
            obj.setOperacao(dadosSQL.getString("operacao"));
            obj.setNrDiasProtesto(dadosSQL.getInt("nrDiasProtesto"));
            obj.setNrDiasBaixaAutomatica(dadosSQL.getInt("nrDiasBaixaAutomatica"));
            obj.setOmitirSequencialArquivo(dadosSQL.getBoolean("omitirSequencialArquivo"));
            obj.setDiretorioRemotoExtrato(dadosSQL.getString("diretorioRemotoExtrato"));
            obj.setLimiteItensRemessa(dadosSQL.getInt("limiteItensRemessa"));
            obj.setNomenclaturaArquivo(NomenclaturaArquivoEnum.obterPorCodigo(dadosSQL.getInt("nomenclaturaArquivo")));
            obj.setChavePJBank(dadosSQL.getString("chavepjbank"));
            obj.setCredencialPJBank(dadosSQL.getString("credencialpjbank"));
            obj.setUtilizaExtrato(dadosSQL.getBoolean("utilizaExtrato"));
            obj.setHostSFTPExtrato(dadosSQL.getString("hostSFTPExtrato"));
            obj.setPortSFTPExtrato(dadosSQL.getInt("portSFTPExtrato"));
            obj.setUserSFTPExtrato(dadosSQL.getString("userSFTPExtrato"));
            obj.setPwdSFTPExtrato(dadosSQL.getString("pwdSFTPExtrato"));
            obj.setNomenclaturaExtrato(dadosSQL.getString("nomenclaturaExtrato"));
            obj.setMascaraDataDiretorioRemotoExtrato(dadosSQL.getString("mascaraDataDiretorioRemotoExtrato"));
            obj.setDiretorioLocalExtrato(dadosSQL.getString("diretorioLocalExtrato"));
            obj.setDiretorioLocalLogExtrato(dadosSQL.getString("diretorioLocalLogExtrato"));
            obj.setMascaraDataDiretorioLocalExtrato(dadosSQL.getString("mascaraDataDiretorioLocalExtrato"));
            obj.setObterCentralPactoExtrato(dadosSQL.getBoolean("obterCentralPactoExtrato"));
            obj.setIgnorarValidacoesExtrato(dadosSQL.getBoolean("ignorarValidacoesExtrato"));
            obj.setVanExtrato(VanExtratoEnum.obterPorCodigo(dadosSQL.getInt("vanExtrato")));
            obj.setNumeroCompromisso(dadosSQL.getString("numeroCompromisso"));
            obj.setDescontoBoleto(dadosSQL.getDouble("porcentagemDescontoBoleto"));
            obj.setCieloClientId(dadosSQL.getString("cieloclientid"));
            obj.setAccessTokenCielo(dadosSQL.getString("accesstokencielo"));
            obj.setRefreshTokenCielo(dadosSQL.getString("refreshtokencielo"));
            obj.setRegisterIDCredenciamentoCielo(dadosSQL.getString("registerIDCredenciamentoCielo"));
            obj.setAmbiente(AmbienteEnum.consultarPorCodigo(dadosSQL.getInt("ambiente")));
            if (!UteisValidacao.emptyNumber(dadosSQL.getInt("tipoCredencialStoneEnum"))) {
                obj.setTipoCredencialStoneEnum(TipoCredencialStoneEnum.consultarPorCodigo(dadosSQL.getInt("tipoCredencialStoneEnum")));
            } else {
                obj.setTipoCredencialStoneEnum(TipoCredencialStoneEnum.NENHUM);
            }
            obj.setBloquearCobrancaAutomatica(dadosSQL.getBoolean("bloquearCobrancaAutomatica"));
            obj.setSomenteExtrato(dadosSQL.getBoolean("somenteExtrato"));
            obj.setPactoPay(dadosSQL.getBoolean("pactopay"));
            montarDadosPix(obj, dadosSQL);
            obj.setCurrencyConvenioEnum(CurrencyConvenioEnum.valueOff(dadosSQL.getString("currency")));
            obj.setLayoutBoletoOnline(ArquivoLayoutRemessaEnum.getFromId(dadosSQL.getString("layoutBoletoOnline")));
            obj.setAdquirenteMaxiPago(AdquirenteMaxiPagoEnum.valueOff(dadosSQL.getInt("adquirenteMaxiPago")));
            obj.setTipoBoletoPJBank(TipoBoletoPJBankEnum.obterPorId(dadosSQL.getInt("tipoboletopjbank")));
            obj.setDiasExpirarPix(dadosSQL.getInt("diasExpirarPix"));
            obj.setEnviarNotificacoes(dadosSQL.getBoolean("enviarNotificacoes"));
            obj.setGerarMultaEJurosRemessaItauCNAB400(dadosSQL.getBoolean("gerarMultaEJurosRemessaItauCNAB400"));
            obj.setApresentarInativoNoPactoPay(dadosSQL.getBoolean("apresentarInativoNoPactoPay"));
            obj.setVerificacaoZeroDollar(dadosSQL.getBoolean("verificacaoZeroDollar"));
            obj.setStatusConciliacaoRedeOnline(ERedeStatusConciliacaoEnum.obterPorId(dadosSQL.getInt("statusConciliacaoRedeOnline")));
            obj.setRequestIdConciliacaoRedeOnline(dadosSQL.getString("requestIdConciliacaoRedeOnline"));
        } catch (Exception e) {
        }
        try{
            obj.setTipoParcelamentoStone(dadosSQL.getString("tipoParcelamentoStone"));
        }catch (Exception ignore){
        }
        try{
            obj.setDataGeracaoAccessToken(dadosSQL.getTimestamp("dataGeracaoAccessToken"));
        }catch (Exception ignore){}
        obj.setUsaSplitPagamentoStoneV5(dadosSQL.getBoolean("usaSplitPagamentoStoneV5"));
        if (nivelMontarDados == Uteis.NIVELMONTARDADOS_DADOSBASICOS) {
            return obj;
        }
        if (nivelMontarDados == Uteis.NIVELMONTARDADOS_DADOSENTIDADESPRINCIPAIS) {
            montarDadosBanco(dadosSQL.getInt("banco"), obj, con);
            montarDadosContaEmpresa(obj, Uteis.NIVELMONTARDADOS_DADOSBASICOS, con);
            montarDadosTipoRemessa(obj, nivelMontarDados, con);
            return obj;
        }
        montarDadosEmpresa(obj, Uteis.NIVELMONTARDADOS_GESTAOREMESSA, con);
        montarDadosContaEmpresa(obj, nivelMontarDados, con);
        montarDadosTipoRemessa(obj, nivelMontarDados, con);
        montarDadosBanco(dadosSQL.getInt("banco"), obj, con);
        montarDadosCfgEmpresa(obj, con);
        return obj;
    }

    public static void montarDadosTipoRemessa(ConvenioCobrancaVO obj, int nivelMontarDados, Connection con) throws Exception {
        if (obj.getTipoRemessa().getCodigo() == 0) {
            obj.setTipoRemessa(new TipoRemessaVO());
            return;
        }
        TipoRemessa tipoRemessa = new TipoRemessa(con);
        obj.setTipoRemessa(tipoRemessa.consultarPorChavePrimaria(obj.getTipoRemessa().getCodigo(), nivelMontarDados));
        tipoRemessa = null;
    }

    public static void montarDadosContaEmpresa(ConvenioCobrancaVO obj, int nivelMontarDados, Connection con) throws Exception {
        if (obj.getContaEmpresa().getCodigo() == 0) {
            obj.setContaEmpresa(new ContaCorrenteVO());
            return;
        }
        ContaCorrente contaCorrente = new ContaCorrente(con);
        obj.setContaEmpresa(contaCorrente.consultarPorChavePrimaria(obj.getContaEmpresa().getCodigo(), nivelMontarDados));
        contaCorrente = null;
    }

    public static void montarDadosEmpresa(ConvenioCobrancaVO obj, int nivelMontarDados, Connection con) throws Exception {
        if (obj.getEmpresa().getCodigo() == 0) {
            obj.setEmpresa(new EmpresaVO());
            return;
        }
        Empresa empresa = new Empresa(con);
        obj.setEmpresa(empresa.consultarPorChavePrimaria(obj.getEmpresa().getCodigo(), nivelMontarDados));
        empresa = null;
    }

    private static void montarDadosBanco(int codigo, ConvenioCobrancaVO obj, Connection con) throws Exception {
        if (codigo != 0) {
            Banco banco = new Banco(con);
            obj.setBanco(banco.consultarPorChavePrimaria(codigo, Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA));
            banco = null;
        }
    }

    public ConvenioCobrancaVO consultarPorCodigoSemInfoEmpresa(Integer codigoPrm, int nivelMontarDados) throws Exception {
        consultar(getIdEntidade(), false);
        String sql = "select \n" +
                "c.* \n" +
                "from conveniocobranca c\n" +
                "where c.codigo = ? ";
        try (PreparedStatement sqlConsultar = con.prepareStatement(sql)) {
            sqlConsultar.setInt(1, codigoPrm);
            try (ResultSet tabelaResultado = sqlConsultar.executeQuery()) {
                if (!tabelaResultado.next()) {
                    throw new ConsistirException("Dados Não Encontrados ( ConvenioCobranca ).");
                }
                return (montarDados(tabelaResultado, nivelMontarDados, con));
            }
        }
    }

    /**
     * Operação responsável por localizar um objeto da classe <code>ConvenioCobrancaVO</code>
     * através de sua chave primária.
     * @exception Exception Caso haja problemas de conexão ou localização do objeto procurado.
     */
    public ConvenioCobrancaVO consultarPorCodigoEmpresa(Integer codigoPrm, Integer empresa, int nivelMontarDados) throws Exception {

        StringBuilder sql = new StringBuilder();
        sql.append("select * \n");
        if (!UteisValidacao.emptyNumber(empresa)) {
            sql.append(", ").append(empresa).append(" as convenio_empresa ");
        }
        sql.append(" from conveniocobranca where codigo = ").append(codigoPrm);

        try (PreparedStatement pst = con.prepareStatement(sql.toString())) {
            try (ResultSet rs = pst.executeQuery()) {
                if (rs.next()) {
                    return montarDados(rs, nivelMontarDados, con);
                } else {
                    return new ConvenioCobrancaVO();
                }
            }
        }
    }

    public ConvenioCobrancaVO consultarPorChavePrimaria(Integer codigoPrm, int nivelMontarDados) throws Exception {
        ConvenioCobrancaVO eCache = (ConvenioCobrancaVO) obterFromCache(codigoPrm);
        if (eCache != null){
            return eCache;
        }
        String sql = "SELECT * FROM ConvenioCobranca WHERE codigo = ?";
        try (PreparedStatement sqlConsultar = con.prepareStatement(sql)) {
            sqlConsultar.setInt(1, codigoPrm);
            try (ResultSet tabelaResultado = sqlConsultar.executeQuery()) {
                if (!tabelaResultado.next()) {
                    throw new ConsistirException("Dados Não Encontrados ( ConvenioCobranca ).");
                }
                eCache = montarDados(tabelaResultado, nivelMontarDados, this.con);
            }
        }
        putToCache(eCache);
        return eCache;
    }

    public ConvenioCobrancaVO consultarPorBanco(Integer codBanco) throws Exception {
        String sql = "select c.* from conveniocobranca c inner join banco b on c.banco = b.codigo where b.codigobanco = ?";
        try (PreparedStatement sqlConsultar = con.prepareStatement(sql)) {
            sqlConsultar.setInt(1, codBanco);
            try (ResultSet tabelaResultado = sqlConsultar.executeQuery()) {
                if (!tabelaResultado.next()) {
                    throw new ConsistirException("Dados Não Encontrados ( ConvenioCobranca ).");
                }
                return  montarDados(tabelaResultado, Uteis.NIVELMONTARDADOS_DADOSBASICOS, this.con);
            }
        }
    }

    public ConvenioCobrancaVO criarOuConsultarSeExistePorDescricaoEEmpresa(ConvenioCobrancaVO obj, Integer empresa) throws Exception {
        String sql = "select \n" +
                "c.*,\n" +
                "cce.empresa\n" +
                "from conveniocobranca c\n" +
                "inner join conveniocobrancaempresa cce on cce.conveniocobranca = c.codigo\n" +
                "where cce.empresa = ? \n" +
                "and c.descricao = ? ";
        try (PreparedStatement sqlConsultar = con.prepareStatement(sql)) {
            sqlConsultar.setString(1, obj.getDescricao());
            sqlConsultar.setInt(2, empresa);
            try (ResultSet tabelaResultado = sqlConsultar.executeQuery()) {
                if (!tabelaResultado.next()) {
                    incluir(obj);
                    return obj;
                } else {
                    return montarDados(tabelaResultado, Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA, con);
                }
            }
        }
    }

    public ConvenioCobrancaVO consultarPorMovParcela(Integer codigoMovparcela, Integer empresa) throws Exception {
        String sql = "select \n" +
                "c.*,\n" +
                "cce.empresa\n" +
                "from conveniocobranca c\n" +
                "inner join conveniocobrancaempresa cce on cce.conveniocobranca = c.codigo\n" +
                "INNER JOIN remessa r ON r.conveniocobranca = cce.conveniocobranca\n" +
                "INNER JOIN remessaitem ri ON ri.remessa = r.codigo AND ri.movparcela = ?\n" +
                "where cce.empresa = ? ";
        try (PreparedStatement sqlConsultar = con.prepareStatement(sql)) {
            sqlConsultar.setInt(1, codigoMovparcela);
            sqlConsultar.setInt(2, empresa);
            try (ResultSet tabelaResultado = sqlConsultar.executeQuery()) {
                if (tabelaResultado.next()) {
                    return (ConvenioCobranca.montarDados(tabelaResultado, Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA, con));
                } else {
                    return new ConvenioCobrancaVO();
                }
            }
        }
    }

    @Override
    public void incrementarSequencialArquivo(ConvenioCobrancaVO convenio) throws Exception {
        convenio.setSequencialDoArquivo(convenio.getSequencialDoArquivo() + 1);
        executarConsulta("update conveniocobranca set sequencialDoArquivo = "
                + convenio.getSequencialDoArquivo() + " where codigo = " + convenio.getCodigo(), con);
    }
    @Override
    public void incrementarSequencialArquivoCancelamento(ConvenioCobrancaVO convenio) throws Exception {
        convenio.setSequencialArquivoCancelamento(convenio.getSequencialArquivoCancelamento() + 1);
        executarConsulta("update conveniocobranca set sequencialArquivoCancelamento = "
                + convenio.getSequencialArquivoCancelamento() + " where codigo = " + convenio.getCodigo(), con);
    }

    public String consultarJSON(Integer empresa, String situacao, String sEcho, Integer tipoCobrancaEnum, String clausulaLike, String colunaOrdenar, String sentidoOrdenar) throws Exception {
        StringBuilder json;
        boolean dados;
        String sql = getPS(empresa, situacao, tipoCobrancaEnum, clausulaLike, colunaOrdenar, sentidoOrdenar);
        Integer count = contar("select count(*) from (" +sql + ") as sql", getCon());

        try (ResultSet rs = con.prepareStatement(sql).executeQuery()) {
            json = new StringBuilder();
            json.append("{");
            json.append("\"iTotalRecords\":\"").append(count).append("\",");
            json.append("\"iTotalDisplayRecords\":\"").append(count).append("\",");
            json.append("\"sEcho\":\"").append(sEcho).append("\",");
            json.append("\"aaData\":[");
            dados = false;
            while (rs.next()) {
                dados = true;
                json.append("[\"").append(rs.getString("codigo")).append("\",");
                json.append("\"").append(Uteis.normalizarStringJSON(rs.getString("descricao"))).append("\",");
                json.append("\"").append(Uteis.normalizarStringJSON(rs.getString("empresa"))).append("\",");
                json.append("\"").append(rs.getString("banco")).append("\",");
                json.append("\"").append(rs.getInt("tipoconvenio") + " - " + TipoConvenioCobrancaEnum.valueOf(rs.getInt("tipoconvenio")).getDescricao()).append("\",");

                AmbienteEnum ambienteEnum = AmbienteEnum.consultarPorCodigo(rs.getInt("ambiente"));
                String ambiente = ambienteEnum.getDescricao();
                if (ambienteEnum.equals(AmbienteEnum.HOMOLOGACAO)) {
                    ambiente = "HOMOLOGAÇÃO";
                }

                json.append("\"").append(Uteis.normalizarStringJSON(ambiente)).append("\",");
                json.append("\"").append(rs.getBoolean("bloquearCobrancaAutomatica") ? "BLOQUEADA" : "LIBERADA").append("\",");
                json.append("\"").append(SituacaoConvenioCobranca.getPorCodigo(rs.getInt("situacao")).getDescricao().toUpperCase()).append("\"],");

            }
        }
        if(dados) {
            json.deleteCharAt(json.toString().length() - 1);
        }
        json.append("]}");
        return json.toString();
    }

    private String getPS(Integer empresa, String situacao, Integer tipoCobrancaEnum, String clausulaLike, String colunaOrdenar, String sentidoOrdenar) throws SQLException {
        StringBuilder sql = new StringBuilder();

        if (!UteisValidacao.emptyString(clausulaLike)) {
            sql.append("select * from ( \n");
        }
        sql.append("SELECT ");
        sql.append("convcob.codigo, ");
        sql.append("descricao, ");
        sql.append("convcob.situacao, ");
        sql.append("convcob.ambiente, ");
        sql.append("(SELECT string_agg(empresa.nome, ', ') ");
        sql.append(" FROM conveniocobrancaempresa cce ");
        sql.append(" INNER JOIN empresa ON empresa.codigo = cce.empresa ");
        sql.append(" WHERE cce.conveniocobranca = convcob.codigo ");
        sql.append(") AS empresa, ");
        sql.append("banco.nome AS banco, ");
        sql.append("tipoconvenio, ");
        sql.append("convcob.bloquearCobrancaAutomatica ");
        if (!UteisValidacao.emptyString(clausulaLike)) {
            sql.append(", \n");
            sql.append(montarCaseTipoConvenio());
        }
        sql.append(" FROM conveniocobranca convcob  ");
        sql.append("LEFT JOIN banco ON convcob.banco = banco.codigo ");
        sql.append("WHERE 1=1 ");
        if (!UteisValidacao.emptyNumber(empresa)) {
            sql.append("AND ").append(empresa).append(" IN (SELECT empresa FROM conveniocobrancaempresa cce WHERE  cce.conveniocobranca = convcob.codigo) ");
        }
        if (situacao != null && situacao.equalsIgnoreCase("AT")) {
            sql.append("AND convcob.situacao = ").append(SituacaoConvenioCobranca.ATIVO.getCodigo()).append(" ");
        } else if (situacao != null && situacao.equalsIgnoreCase("IN")) {
            sql.append("AND convcob.situacao = ").append(SituacaoConvenioCobranca.INATIVO.getCodigo()).append(" ");
        }

        if (tipoCobrancaEnum != null &&
                !tipoCobrancaEnum.equals(TipoCobrancaEnum.NENHUM.getId())) {
            TipoCobrancaEnum tipoEnum = TipoCobrancaEnum.obterPorId(tipoCobrancaEnum);
            if (tipoEnum != null) {
                List<TipoConvenioCobrancaEnum> tiposConvenio = TipoConvenioCobrancaEnum.obterListaTipoCobranca(tipoEnum);
                String tiposC = "";
                for (TipoConvenioCobrancaEnum tipo : tiposConvenio) {
                    tiposC += ("," + tipo.getCodigo());
                }
                sql.append("AND convcob.tipoconvenio in (").append(tiposC.replaceFirst(",", "")).append(") ");
            }
        }
        if (!UteisValidacao.emptyString(clausulaLike)) {
            sql.append(") AS t WHERE 1=1 ");
            sql.append(" AND (");
            sql.append("lower(codigo::VARCHAR) ~ '").append(clausulaLike).append("' \n");
            sql.append("OR lower(descricao) ~ '").append(clausulaLike).append("' \n");
            sql.append("OR lower(tipoConvenioDescricao) ~ '").append(clausulaLike).append("' \n");
            sql.append("OR lower(empresa) ~ '").append(clausulaLike).append("' \n");
            sql.append(")");
        }
        if (!UteisValidacao.emptyString(colunaOrdenar) || !UteisValidacao.emptyString(sentidoOrdenar)) {
            sql.append("ORDER BY ");
            if (colunaOrdenar.equals("codigo") || colunaOrdenar.equals("situacao") || colunaOrdenar.equals("ambiente") || colunaOrdenar.equals("bloquearcobrancaautomatica")) {
                sql.append("convcob.");
            }
            sql.append(colunaOrdenar).append(" ");
            if ((colunaOrdenar.equals("ambiente") || colunaOrdenar.equals("bloquearcobrancaautomatica") || colunaOrdenar.equals("situacao")) && sentidoOrdenar.equals("asc")) {
                sql.append("desc");
            } else if ((colunaOrdenar.equals("ambiente") || colunaOrdenar.equals("bloquearcobrancaautomatica") || colunaOrdenar.equals("situacao")) && sentidoOrdenar.equals("desc")) {
                sql.append("asc");
            } else {
                sql.append(sentidoOrdenar);
            }
        }
        return sql.toString();
    }
    public List consultarParaImpressao(String filtro, String ordem, String campoOrdenacao, int i) throws SQLException {

        List lista;
        String sql = getPS(i, null, null, filtro, null, null);
        try (ResultSet rs = con.prepareStatement(sql).executeQuery()) {
            lista = new ArrayList();
            while (rs.next()) {
                ConvenioCobrancaVO cCobranca = new ConvenioCobrancaVO();
                cCobranca.setCodigo(rs.getInt("codigo"));
                cCobranca.setDescricao(rs.getString("descricao"));
                cCobranca.getEmpresa().setNome(rs.getString("empresa"));
                if (!UteisValidacao.emptyString(rs.getString("banco"))) {
                    cCobranca.getBanco().setNome(rs.getString("banco"));
                } else {
                    cCobranca.getBanco().setNome("nenhum");
                }
                cCobranca.setTipo(TipoConvenioCobrancaEnum.valueOf(rs.getInt("tipoconvenio")));
                lista.add(cCobranca);
            }
        }
        if (campoOrdenacao.equals("Código")) {
            Ordenacao.ordenarLista(lista, "codigo");
        } else if (campoOrdenacao.equals("Descrição")) {
            Ordenacao.ordenarLista(lista, "descricao");
        } else if (campoOrdenacao.equals("Empresa")) {
            Ordenacao.ordenarLista(lista, "empresa_Apresentar");
        } else if (campoOrdenacao.equals("Banco")) {
            Ordenacao.ordenarLista(lista, "banco_Apresentar");
        } else if (campoOrdenacao.equals("Tipo")) {
            Ordenacao.ordenarLista(lista, "tipo_Apresentar");
        }
        if (ordem.contains("desc")) {
            Collections.reverse(lista);
        }
        return lista;

    }

    public List<ConvenioCobrancaVO> consultarSimplesPorTipos(TipoConvenioCobrancaEnum ... tipos) throws Exception {
        List<ConvenioCobrancaVO> convenios = new ArrayList<ConvenioCobrancaVO>();
        if(tipos.length == 0){
            return convenios;
        }
        String tiposStr = "" ;
        for(TipoConvenioCobrancaEnum t : tipos){
            tiposStr += ","+t.getCodigo();
        }

        String condicao = tiposStr.replaceFirst(",", "");

        try (ResultSet rs = SuperFacadeJDBC.criarConsulta("select \n" +
                "cc.*,\n" +
                "cce.empresa\n" +
                "from conveniocobranca cc\n" +
                "inner join conveniocobrancaempresa cce on cce.conveniocobranca = cc.codigo\n" +
                "where cc.tipoconvenio in (" + condicao + ")", con)) {

            while (rs.next()) {
                ConvenioCobrancaVO convenio = new ConvenioCobrancaVO();
                convenio.setCodigo(rs.getInt("codigo"));
                convenio.setDescricao(rs.getString("descricao"));
                convenios.add(convenio);
            }
        }
        return convenios;
    }

    @Override
    public Integer incrementarSequencialItem(Integer codigoConvenio) throws Exception {
        Integer sequencial = null;
        try (Connection con2 = Conexao.getInstance().obterNovaConexaoBaseadaOutra(con)) {
            try {
                con2.setAutoCommit(false);
                String sql = "SELECT sequencialItem FROM conveniocobranca WHERE codigo = " + codigoConvenio + " FOR UPDATE";
                try (ResultSet rs = con2.prepareStatement(sql).executeQuery()) {
                    rs.next();
                    sequencial = rs.getInt("sequencialItem");
                }
                sequencial++;
                String sqlUpdate = "UPDATE conveniocobranca SET sequencialItem = " + sequencial + " WHERE codigo = " + codigoConvenio;
                con2.prepareStatement(sqlUpdate).executeUpdate();
                con2.commit();
            } catch (Exception e) {
                con2.rollback();
                throw e;
            }finally {
                con2.setAutoCommit(true);
            }
        }
        return sequencial;
    }

    public List<ConvenioCobrancaVO> consultarTodosGeral(boolean comInformacaoEmpresa, Boolean somenteExtrato, int nivelmontardados) throws Exception {
        StringBuilder sql;
        if (comInformacaoEmpresa) {
            sql = new StringBuilder();
            sql.append("SELECT \n");
            sql.append("c.*, \n");
            sql.append("cce.empresa \n");
            sql.append("FROM conveniocobranca c \n");
            sql.append("INNER JOIN conveniocobrancaempresa cce ON cce.conveniocobranca = c.codigo \n");
            if (somenteExtrato != null) {
                sql.append("WHERE c.somenteExtrato = ").append(somenteExtrato).append(" \n");
            }
            sql.append("ORDER BY c.descricao \n");
        } else {
            sql = new StringBuilder();
            sql.append("select * from conveniocobranca \n");
            if (somenteExtrato != null) {
                sql.append("WHERE somenteExtrato = ").append(somenteExtrato).append(" \n");
            }
            sql.append("ORDER BY descricao");
        }
        try (Statement stm = con.createStatement()) {
            try (ResultSet tabelaResultado = stm.executeQuery(sql.toString())) {
                return montarDadosConsulta(tabelaResultado, nivelmontardados, con);
            }
        }
    }

    public List<ConvenioCobrancaVO> consultarTodosPorSituacaoEmpresa(int codEmpresa, int situacao, int nivelmontardados) throws Exception {
        StringBuilder sql = new StringBuilder();
        sql.append("SELECT c.*, cce.empresa FROM conveniocobranca c ");
        sql.append(" INNER JOIN conveniocobrancaempresa cce ON cce.conveniocobranca = c.codigo ");
        sql.append(" WHERE cce.empresa = ").append(codEmpresa);
        sql.append(" AND c.situacao = ").append(situacao);
        sql.append(" ORDER BY c.descricao ");
        try (Statement stm = con.createStatement()) {
            try (ResultSet tabelaResultado = stm.executeQuery(sql.toString())) {
                return montarDadosConsulta(tabelaResultado, nivelmontardados, con);
            }
        }
    }

    @Override
    public List<ConvenioCobrancaVO> consultarPorCodigo(int codigo, int nivelMontardadosTodos) throws Exception {
        List<ConvenioCobrancaVO> convenios = new ArrayList<ConvenioCobrancaVO>();
        String sql = "SELECT * FROM conveniocobranca WHERE codigo = "+codigo;
        try (PreparedStatement ps = con.prepareStatement(sql)) {
            try (ResultSet rs = ps.executeQuery()) {
                while (rs.next()) {
                    convenios.add(montarDados(rs, nivelMontardadosTodos, con));
                }
            }
        }

        return convenios;
    }

    @Override
    public ConvenioCobrancaVO consultarDadosPix(int codigo) throws Exception {
        String sql = "SELECT * FROM conveniocobranca WHERE codigo = "+codigo;
        try (PreparedStatement ps = con.prepareStatement(sql)) {
            try (ResultSet resultSet = ps.executeQuery()) {
                if (resultSet.next()) {
                    return montarDados(resultSet, Uteis.NIVELMONTARDADOS_DADOSBASICOS, con);
                }
            }
        }

        throw new Exception("Não foi encontrado o convênio de código "+codigo);
    }

    public ConvenioCobrancaVO consultarConvenioPixPorCodigoEmpresa(int codigoEmpresa, TipoConvenioCobrancaEnum tipoConvenioCobrancaEnum) throws Exception {
        String sql = "Select * from conveniocobranca c\n" +
                "inner join conveniocobrancaempresa c2 on c2.conveniocobranca = c.codigo\n" +
                "where c.tipoconvenio = " + tipoConvenioCobrancaEnum.getCodigo() +
                " and c2.empresa = " + codigoEmpresa;

        try (PreparedStatement ps = con.prepareStatement(sql)) {
            try (ResultSet resultSet = ps.executeQuery()) {
                if (resultSet.next()) {
                    return montarDados(resultSet, Uteis.NIVELMONTARDADOS_DADOSBASICOS, con);
                }
            }
        }
        throw new Exception("Não foi encontrado o convênio Pix da empresa de código " + codigoEmpresa);
    }

    @Override
    public List<ConvenioCobrancaVO> consultarPorTipoPix() throws Exception {
        List<ConvenioCobrancaVO> convenios = new ArrayList<ConvenioCobrancaVO>();
        List<Integer> tiposConvenio = TipoConvenioCobrancaEnum.obterCodigosTipoPix();
        String listIds = tiposConvenio.toString();
        String idsIn = listIds.substring(1, listIds.length()-1);
        String sql = "SELECT * FROM conveniocobranca WHERE situacao = 1 and tipoconvenio in (" +idsIn+")";
        try (PreparedStatement ps = con.prepareStatement(sql)) {
            try (ResultSet rs = ps.executeQuery()) {
                while (rs.next()) {
                    convenios.add(montarDados(rs, Uteis.NIVELMONTARDADOS_EMPRESAS, con));
                }
            }
        }
        return convenios;
    }

    public List<ConvenioCobrancaVO> consultarPorTipoPixComEmpresa(EmpresaVO empresaVO) throws Exception {
        List<ConvenioCobrancaVO> convenios = new ArrayList<ConvenioCobrancaVO>();
        List<Integer> tiposConvenio = TipoConvenioCobrancaEnum.obterCodigosTipoPix();
        String listIds = tiposConvenio.toString();
        String idsIn = listIds.substring(1, listIds.length() - 1);
        String sql = "SELECT * FROM conveniocobranca c inner join conveniocobrancaempresa c2 on c2.conveniocobranca = c.codigo" +
                " WHERE situacao = 1 and tipoconvenio in (" + idsIn + ") and c2.empresa = " + empresaVO.getCodigo();
        try (PreparedStatement ps = con.prepareStatement(sql)) {
            try (ResultSet rs = ps.executeQuery()) {
                while (rs.next()) {
                    convenios.add(montarDados(rs, Uteis.NIVELMONTARDADOS_EMPRESAS, con));
                }
            }
        }

        return convenios;
    }

    public List<ConvenioCobrancaVO> consultarPorTipoConvenio(int tipoConvenio, int nivelMontardadosBasicos) throws Exception {
        List<ConvenioCobrancaVO> convenios = new ArrayList<ConvenioCobrancaVO>();
        String sql = "SELECT * FROM conveniocobranca WHERE tipoconvenio = " + tipoConvenio;
        try (PreparedStatement ps = con.prepareStatement(sql)) {
            try (ResultSet rs = ps.executeQuery()) {
                while (rs.next()) {
                    convenios.add(montarDados(rs, nivelMontardadosBasicos, con));
                }
            }
        }

        return convenios;
    }

    public List<ConvenioCobrancaVO> consultarTodos(int nivelmontardados) throws Exception {
        return consultarTodosGeral(true, null, nivelmontardados);
    }

    public ConvenioCobrancaVO consultarPorTipoEBanco(TipoConvenioCobrancaEnum tipoConvenio, String codigoBancoConvenio) throws Exception {
        return consultarPorTipoEBanco(tipoConvenio, codigoBancoConvenio, null);
    }

    public ConvenioCobrancaVO consultarPorTipoEBanco(TipoConvenioCobrancaEnum tipoConvenio, String codigoBancoConvenio, Integer banco) throws Exception {
        StringBuilder sql = new StringBuilder();
        sql.append("select \n");
        sql.append("cc.*,\n");
        sql.append("cce.empresa\n");
        sql.append("from conveniocobranca cc\n");
        sql.append("inner join conveniocobrancaempresa cce on cce.conveniocobranca = cc.codigo\n");
        sql.append("LEFT JOIN banco b ON cc.banco = b.codigo\n");
        sql.append("where 1 = 1\n");
        if(banco == null){
            sql.append("AND b.codigobanco = ").append(codigoBancoConvenio).append("\n");
            sql.append("AND cc.tipoconvenio = ").append(tipoConvenio.getCodigo()).append("\n");
        }else{
            sql.append("AND b.codigo = ").append(banco).append("\n");
            sql.append("AND cc.tipoconvenio in (");
            sql.append(TipoConvenioCobrancaEnum.DCO_CAIXA.getCodigo()).append(",");
            sql.append(TipoConvenioCobrancaEnum.DCO_ITAU.getCodigo()).append(",");
            sql.append(TipoConvenioCobrancaEnum.DCO_BB.getCodigo()).append(",");
            sql.append(TipoConvenioCobrancaEnum.DCO_HSBC.getCodigo()).append(",");
            sql.append(TipoConvenioCobrancaEnum.DCO_SANTANDER.getCodigo()).append(",");
            sql.append(TipoConvenioCobrancaEnum.DCO_BRADESCO.getCodigo()).append(")\n");
        }
        try (Statement stm = con.createStatement()) {
            try (ResultSet tabelaResultado = stm.executeQuery(sql.toString())) {
                if (tabelaResultado.next()) {
                    return montarDados(tabelaResultado, Uteis.NIVELMONTARDADOS_MINIMOS, con);
                }
            }
        }
        return null;
    }

    public List<ConvenioCobrancaVO> consultarPorCodigoIn(String codigos, int nivelMontarDados) throws Exception {
        String sqlStr = "SELECT * FROM ConvenioCobranca WHERE codigo in (" + codigos + ")";
        try (Statement stm = con.createStatement()) {
            try (ResultSet tabelaResultado = stm.executeQuery(sqlStr)) {
                return (montarDadosConsulta(tabelaResultado, nivelMontarDados, con));
            }
        }
    }

    @Override
    public void alterarChaveAPIGetNet(ConvenioCobrancaVO convenioCobrancaVO) throws Exception {
        String sql = "UPDATE ConvenioCobranca set chaveAPI = ?, dataChaveAPI = ? WHERE codigo = ?";
        try (PreparedStatement ps = con.prepareStatement(sql)) {
            ps.setString(1, convenioCobrancaVO.getChaveAPI());
            ps.setTimestamp(2, Uteis.getDataJDBCTimestamp(convenioCobrancaVO.getDataChaveAPI()));
            ps.setInt(3, convenioCobrancaVO.getCodigo());
            ps.executeUpdate();
        }
    }

    private static void montarDadosCfgEmpresa(ConvenioCobrancaVO obj, Connection con) {
        try {
            ConvenioCobrancaEmpresa convenioCobrancaEmpresaDAO = new ConvenioCobrancaEmpresa(con);
            obj.setConfiguracoesEmpresa(convenioCobrancaEmpresaDAO.consultarPorConvenioCobranca(obj.getCodigo()));
            convenioCobrancaEmpresaDAO = null;
        } catch (Exception e) {
        }
    }

    @Override
    public List<ConvenioCobrancaVO> consultarTodosGestaoRemessas(int nivelmontardados) throws Exception {
        String sqlStr = "select * from (\n" +
                "select \n" +
                "c.*,\n" +
                "cce.empresa\n" +
                "from conveniocobranca c\n" +
                "inner join conveniocobrancaempresa cce on cce.conveniocobranca = c.codigo\n" +
                "where c.gerararquivounico = false\n" +
                "UNION\n" +
                "select \n" +
                "c.*,\n" +
                "(select empresa from conveniocobrancaempresa where conveniocobranca = c.codigo order by empresa limit 1) as empresa\n" +
                "from conveniocobranca c\n" +
                "where c.gerararquivounico = true\n" +
                ") as foo\n" +
                "order by descricao";
        try (Statement stm = con.createStatement()) {
            try (ResultSet tabelaResultado = stm.executeQuery(sqlStr)) {
                return montarDadosConsulta(tabelaResultado, nivelmontardados, con);
            }
        }
    }

    public void ajustarConvenio(Integer codigoEmpresa, Integer codigoConvenioCobrancaAnterior,
                                TipoAutorizacaoCobrancaEnum tipoAutorizacaoConvenioCobrancaAnterior,
                                Integer codigoConvenioCobrancaNovo, List<OperadorasExternasAprovaFacilEnum> listOperadoras) throws Exception {
        AjustarAutorizacaoCobranca.ajustarAutorizacaoCobrancaCliente(con, codigoEmpresa, codigoConvenioCobrancaAnterior, tipoAutorizacaoConvenioCobrancaAnterior, codigoConvenioCobrancaNovo, listOperadoras,false);
        AjustarAutorizacaoCobranca.ajustarAutorizacaoCobrancaColaborador(con, codigoEmpresa, codigoConvenioCobrancaAnterior, tipoAutorizacaoConvenioCobrancaAnterior, codigoConvenioCobrancaNovo, listOperadoras);
    }

    public List<ConvenioCobrancaVO> consultarPorEmpresaSituacaoTipoAutorizacao(Integer empresa, SituacaoConvenioCobranca situacao, TipoAutorizacaoCobrancaEnum tipoAutorizacaoEnum,
                                                                               Boolean somenteExtrato, int nivelMontarDados) throws Exception {

        String condicaoTipoConvenio = "";
        if (tipoAutorizacaoEnum != null) {
            condicaoTipoConvenio = Uteis.splitFromListInteger(TipoConvenioCobrancaEnum.obterCodigosTipoAutorizacao(tipoAutorizacaoEnum));
        }

        StringBuilder sql = new StringBuilder();
        sql.append("select  \n");
        sql.append("cc.*, \n");
        sql.append("cce.empresa \n");
        sql.append("from conveniocobranca cc \n");
        sql.append("inner join conveniocobrancaempresa cce on cce.conveniocobranca = cc.codigo \n");
        sql.append("where 1 = 1 \n");
        if (!UteisValidacao.emptyNumber(empresa)) {
            sql.append("and cce.empresa = ").append(empresa).append("\n");
        }
        if (!UteisValidacao.emptyString(condicaoTipoConvenio)) {
            sql.append("and cc.tipoconvenio in (").append(condicaoTipoConvenio).append(") \n");
        }
        if (situacao != null) {
            sql.append("and cc.situacao = ").append(situacao.getCodigo()).append(" \n");
        }
        if (somenteExtrato != null) {
            sql.append("and cc.somenteExtrato = ").append(somenteExtrato).append(" \n");
        }
        sql.append("ORDER BY cce.empresa \n");
        try (Statement stm = con.createStatement()) {
            try (ResultSet tabelaResultado = stm.executeQuery(sql.toString())) {
                return montarDadosConsulta(tabelaResultado, nivelMontarDados, con);
            }
        }
    }

    public List<ConvenioCobrancaVO> consultarConveniosTrocaDeEmpresa(Integer cliente, Integer empresaDestino) {
        List<ConvenioCobrancaVO> lista = new ArrayList<>();
        try {
            StringBuilder sql = new StringBuilder();
            sql.append("select  \n");
            sql.append("au.tipoautorizacao, \n");
            sql.append("cc.tipoconvenio \n");
            sql.append("from autorizacaocobrancacliente au \n");
            sql.append("inner join conveniocobranca cc on cc.codigo = au.conveniocobranca \n");
            sql.append("where au.ativa \n");
            sql.append("and au.cliente = ").append(cliente);

            ResultSet rs = SuperFacadeJDBC.criarConsulta(sql.toString(), con);

            if (rs.next()) {
                Integer tipoConveniocobranca = rs.getInt("tipoconvenio");
                Integer tipoautorizacao = rs.getInt("tipoautorizacao");

                lista.addAll(consultarPorEmpresaESituacao(empresaDestino, false, Uteis.NIVELMONTARDADOS_DADOSBASICOS, new Integer[]{tipoConveniocobranca}, SituacaoConvenioCobranca.ATIVO));
                //se não encontrou do mesmo tipo de convenio buscar convenio do mesmo tipo de autorizacao
                if (UteisValidacao.emptyList(lista)) {
                    lista.addAll(consultarPorEmpresaSituacaoTipoAutorizacao(empresaDestino, SituacaoConvenioCobranca.ATIVO, TipoAutorizacaoCobrancaEnum.valueOf(tipoautorizacao), false, Uteis.NIVELMONTARDADOS_DADOSBASICOS));
                }
            }
        } catch (Exception ignored) {
        }
        return lista;
    }

    public boolean existeConvenioCobrancaPorEmpresaSituacaoTipoCobranca(Integer empresa, TipoCobrancaEnum tipoCobrancaEnum,
                                                                        SituacaoConvenioCobranca situacao) throws Exception {

        List<TipoConvenioCobrancaEnum> listaTipoConvenio = TipoConvenioCobrancaEnum.obterListaTipoCobranca(tipoCobrancaEnum);
        List<Integer> listaTipo = new ArrayList<>();
        for (TipoConvenioCobrancaEnum tipo : listaTipoConvenio) {
            listaTipo.add(tipo.getCodigo());
        }
        Integer[] tiposPesquisar = new Integer[listaTipo.size()];
        tiposPesquisar = listaTipo.toArray(tiposPesquisar);
        return existeConvenioCobrancaPorEmpresaSituacaoTipo(empresa, tiposPesquisar, situacao);
    }

    public boolean existeConvenioCobrancaPorEmpresaSituacaoTipo(Integer empresa, final Integer[] tipos, SituacaoConvenioCobranca situacao) throws Exception {

        String condicaoTipos = "";
        if (tipos != null) {
            for (Integer tipo : tipos) {
                condicaoTipos += "," + tipo;
            }
            condicaoTipos = condicaoTipos.replaceFirst(",", "");
        }

        StringBuilder sql = new StringBuilder();
        sql.append("select exists( \n");
        sql.append("select \n");
        sql.append("cc.codigo \n");
        sql.append("from conveniocobranca cc \n");
        sql.append("inner join conveniocobrancaempresa cce on cce.conveniocobranca = cc.codigo \n");
        sql.append("where 1 = 1 \n");
        if (!UteisValidacao.emptyNumber(empresa)) {
            sql.append("and cce.empresa = ").append(empresa).append("\n");
        }
        if (!UteisValidacao.emptyString(condicaoTipos)) {
            sql.append("and cc.tipoconvenio in (").append(condicaoTipos).append(") \n");
        }
        if (situacao != null) {
            sql.append("and cc.situacao = ").append(situacao.getCodigo()).append(" \n");
        }
        sql.append(") existe \n");
        try (Statement stm = con.createStatement()) {
            try (ResultSet rs = stm.executeQuery(sql.toString())) {
                if (rs.next()) {
                    return rs.getBoolean("existe");
                } else {
                    return false;
                }
            }
        }
    }

    public List<ConvenioCobrancaVO> consultarPorTipoCobranca(TipoCobrancaEnum tipoCobrancaEnum, Integer empresa, SituacaoConvenioCobranca situacao,
                                                             Boolean somenteExtrato, int nivelMontarDados) throws Exception {
        return consultarPorTipoCobranca(new TipoCobrancaEnum[]{tipoCobrancaEnum}, empresa, situacao, somenteExtrato, nivelMontarDados);
    }

    public List<ConvenioCobrancaVO> consultarPorTipoCobranca(TipoCobrancaEnum[] arrayTipoCobrancaEnum, Integer empresa, SituacaoConvenioCobranca situacao,
                                                             Boolean somenteExtrato, int nivelMontarDados) throws Exception {
        TipoConvenioCobrancaEnum[] tipos = null;
        if (arrayTipoCobrancaEnum != null) {
            List<TipoConvenioCobrancaEnum> listaTipoConvenio = new ArrayList<>();
            for (TipoCobrancaEnum tipoCobrancaEnum : arrayTipoCobrancaEnum) {
                listaTipoConvenio.addAll(TipoConvenioCobrancaEnum.obterListaTipoCobranca(tipoCobrancaEnum));
            }
            tipos = new TipoConvenioCobrancaEnum[listaTipoConvenio.size()];
            tipos = listaTipoConvenio.toArray(tipos);
        }
        return consultarPorTiposESituacao(tipos, empresa, situacao, false, nivelMontarDados, 0, somenteExtrato);
    }

    public boolean somenteConvenioVindi() throws Exception {
        Integer qtdTotalConvenio = 0;
        Integer qtdConvenioVindi = 0;
        try (Statement stm = con.createStatement()) {
            try (ResultSet rs = stm.executeQuery("select count(*) as qtd from conveniocobranca where tipoconvenio = " + TipoConvenioCobrancaEnum.DCC_VINDI.getCodigo())) {
                if (rs.next()) {
                    qtdConvenioVindi = rs.getInt("qtd");
                }
            }
        }

        try (Statement stm = con.createStatement()) {
            try (ResultSet rs = stm.executeQuery("select count(*) as qtd from conveniocobranca")) {
                if (rs.next()) {
                    qtdTotalConvenio = rs.getInt("qtd");
                }
            }
        }

        return qtdTotalConvenio == 1 && qtdConvenioVindi == 1;
    }

    public boolean existeConvenioOnline(Set<Integer> empresas, SituacaoConvenioCobranca situacao) throws Exception {

        TipoConvenioCobrancaEnum[] tipos = null;
        List<TipoConvenioCobrancaEnum> listaTipoCobranca = TipoConvenioCobrancaEnum.obterListaTipoCobranca(TipoCobrancaEnum.ONLINE);
        tipos = new TipoConvenioCobrancaEnum[listaTipoCobranca.size()];
        tipos = listaTipoCobranca.toArray(tipos);

        String condicaoTipos = Uteis.splitFromArray(tipos, false, "codigo");

        StringBuilder sql = new StringBuilder();
        sql.append("select exists ( \n");
        sql.append("select  \n");
        sql.append("cc.codigo \n");
        sql.append("from conveniocobranca cc \n");
        sql.append("inner join conveniocobrancaempresa cce on cce.conveniocobranca = cc.codigo \n");
        sql.append("where cc.tipoconvenio in (").append(condicaoTipos).append(") \n");
        if (!UteisValidacao.emptyList(empresas)) {
            String empresa = "";
            for (Integer emp : empresas) {
                empresa += ("," + emp);
            }
            sql.append("and cce.empresa in (").append(empresa.replaceFirst(",", "")).append(") \n");
        }
        if (situacao != null) {
            sql.append("and cc.situacao = ").append(situacao.getCodigo()).append(" \n");
        }
        sql.append(") as existe");
        try (Statement stm = con.createStatement()) {
            try (ResultSet rs = stm.executeQuery(sql.toString())) {
                if (rs.next()) {
                    return rs.getBoolean("existe");
                } else {
                    return false;
                }
            }
        }
    }

    /**
     * Realiza credenciamento de Extrato Diário CIELO
     * @param convenioCobrancaVO
     * @param codigoAutorizacao
     * @throws Exception
     */
    public void credenciamentoExtratoDiarioCielo(ConvenioCobrancaVO convenioCobrancaVO, String codigoAutorizacao) throws Exception {
        try {
            credenciarExtratoCielo(convenioCobrancaVO, codigoAutorizacao);
        } catch (Exception e) {
            if (e.getMessage().toLowerCase().contains("these clients are not")) {
                String eMessage = "Erro no credenciamento do extrato EDI, entre em contato com um consultor Cielo. Info: " + e.getMessage();
                duplicacaoMatriz(convenioCobrancaVO, eMessage);
            } else {
                throw e;
            }
        }
    }

    private void credenciarExtratoCielo(ConvenioCobrancaVO convenioCobrancaVO, String codigoAutorizacao) throws Exception {

        accessTokenCielo(convenioCobrancaVO, codigoAutorizacao);

        String app = "PactoSolutionsOnboardingEdiApiProduction";
        String clientSecret = "2610a8e5-78d3-396e-99cd-be4eef81886a";
        String url = API_CIELO_AFFILIATE + "/edi-api/v2/edi/registers";

        // Credenciamento
        StringBuilder json = new StringBuilder();
        json.append("{\n");
        json.append(" \"merchantEMail\": \"" + convenioCobrancaVO.getCieloClientId().trim() + "\",\n");
        json.append(" \"merchants\": [\"" + convenioCobrancaVO.getNumeroContrato().trim() + "\"],\n");
        json.append(" \"type\": [\"SELL\", \"PAYMENT\", \"BALANCE\", \"NRC\"]\n");
        json.append("}");

        Map<String, String> headers = new HashMap<>();
        headers.put("Content-type", "application/json");
        headers.put("Authorization", "Bearer " + convenioCobrancaVO.getAccessTokenCielo());
        String resposta = executeHttpRequest(url, json.toString(), headers, ExecuteRequestHttpService.METODO_POST, "UTF-8");
        JSONObject obj = tratarRespostaCielo(resposta);
        convenioCobrancaVO.setRegisterIDCredenciamentoCielo(obj.optString("registerID"));
        alterar(convenioCobrancaVO);

    }

    /**
     * Consulta disponibilidade de duplicação de Matriz para credenciamento de Extrato CIELO,
     * caso seja possível será realizado a duplicação da matriz, caso não, deverá entrar em contato com a CIELO
     * @param convenioCobrancaVO
     * @return Boolean
     * @throws Exception
     */
    private boolean duplicacaoMatriz(ConvenioCobrancaVO convenioCobrancaVO, String eMessage) throws Exception {

        String url = API_CIELO_AFFILIATE + "/edi-api/v2/edi/mainmerchants";
        Map<String, String> headers = new HashMap<>();
        headers.put("Content-type", "application/json");
        headers.put("Authorization", "Bearer " + convenioCobrancaVO.getAccessTokenCielo());
        String resposta = executeHttpRequest(url, null, headers, ExecuteRequestHttpService.METODO_GET, "UTF-8");
        JSONArray objArray;
        JSONObject obj;
        try {
            objArray = new JSONArray(resposta);
            if (objArray.length() == 0) {
                throw null;
            }
        } catch (Exception e) {
            throw new Exception(eMessage);
        }
        for (int i=0; i < objArray.length(); i++) {
            obj = objArray.getJSONObject(i);

            // Merchant ID não disponivel para duplicação de matriz via Api
            if (obj.optString("ediStatus", "").equals("UNAVAILABLE")) {
                String merchants = obj.getJSONArray("merchants").toString().replace("[", "").replace("]", "");
                throw new Exception("MerchantID (" + merchants + ") já está vinculado a outra conciliadora, necessário entrar em contato com seu consultor Cielo para fazer a duplicação de Matriz.");
            }

            List<String> types = new ArrayList<>();
            types.add("SELL");
            types.add("PAYMENT");
            types.add("ANTECIPATION_CIELO");
            types.add("ASSIGNMENT");
            types.add("BALANCE");
            types.add("ANTECIPATION_ALELO");
            obj.put("type", types);
            obj.remove("editStatus");

            url = API_CIELO_AFFILIATE + "/edi-api/v2/edi";
            headers = new HashMap<>();
            headers.put("Content-type", "application/json");
            headers.put("Authorization", "Bearer " + convenioCobrancaVO.getAccessTokenCielo());
            resposta = executeHttpRequest(url, obj.toString(), headers, ExecuteRequestHttpService.METODO_PUT, "UTF-8");
            obj = tratarRespostaCielo(resposta);
            convenioCobrancaVO.setRegisterIDCredenciamentoCielo(obj.optString("registerID"));
            alterar(convenioCobrancaVO);

        }

        return true;

    }
    private JSONObject tratarRespostaCielo(String resposta) throws Exception {
        JSONObject obj;
        try {
            obj = new JSONObject(resposta);
        } catch (Exception e) {
            throw new Exception("Erro ao obter resposta da Cielo, tente novamente ou entre em contato com a Pacto");
        }

        String errorMessage = obj.optString("message", "").concat(obj.optString("value", ""));
        if (UteisValidacao.emptyString(errorMessage)) {
            errorMessage = obj.optString("error", "");
        }

        if (!UteisValidacao.emptyString(errorMessage)) {
            throw new Exception(errorMessage);
        }
        return obj;

    }

    /**
     * @param convenioCobrancaVO
     * @return Retorna status do credenciamento de extrato CIELO
     * @throws Exception
     */
    public String consultarStatusCredenciamentoCielo(ConvenioCobrancaVO convenioCobrancaVO) throws Exception {

        // Atualizar AcessToken
        accessTokenCielo(convenioCobrancaVO, null);

        // Consultar Status Credenciamento
        String url = API_CIELO_AFFILIATE + "/edi-api/v2/edi?registerID=" + convenioCobrancaVO.getRegisterIDCredenciamentoCielo();
        Map<String, String> headers = new HashMap<>();
        headers.put("Content-type", "application/json");
        headers.put("Authorization", "Bearer " + convenioCobrancaVO.getAccessTokenCielo());
        String resposta = executeHttpRequest(url, null, headers, ExecuteRequestHttpService.METODO_GET, "UTF-8");
        JSONObject obj = tratarRespostaCielo(resposta);
        String acknowledge = obj.getString("acknowledge");

        return acknowledge;

    }

    /**
     * Solicita ou atualiza AccessToken API CIELO
     * @param convenioCobrancaVO
     * @param codigoAutorizacao
     * @throws Exception
     */
    public void accessTokenCielo(ConvenioCobrancaVO convenioCobrancaVO, String codigoAutorizacao) throws Exception {

        String base64 = "ZjgyN2MzMGEtZWU2Ny0zOGYwLWI1NTctM2FjNDI5YjQ2YzA2OjI2MTBhOGU1LTc4ZDMtMzk2ZS05OWNkLWJlNGVlZjgxODg2YQ==";
        String url = API_CIELO_AFFILIATE + "/consent/v1/oauth/access-token";
        StringBuilder json = new StringBuilder();
        if (codigoAutorizacao == null) {
            json.append("{\n");
            json.append("    \"grant_type\": \"refresh_token\",\n");
            json.append("    \"refresh_token\": \"" + convenioCobrancaVO.getRefreshTokenCielo() + "\"\n");
            json.append("}");
        } else {
            json.append("{\n");
            json.append("    \"grant_type\": \"authorization_code\",\n");
            json.append("    \"code\": \"" + codigoAutorizacao + "\"\n");
            json.append("}");
        }

        Map<String, String> headers = new HashMap<>();
        headers.put("Content-type", "application/json");
        headers.put("Authorization", "Basic " + base64);
        String resposta = executeHttpRequest(url, json.toString(), headers, ExecuteRequestHttpService.METODO_POST, "UTF-8");
        JSONObject obj = tratarRespostaCielo(resposta);

        String accessToken = obj.getString("access_token");
        String refreshToken = obj.getString("refresh_token");

        convenioCobrancaVO.setAccessTokenCielo(accessToken);
        convenioCobrancaVO.setRefreshTokenCielo(refreshToken);
        alterar(convenioCobrancaVO);

    }

    public static String executeHttpRequest(String urlRequest, String corpo, Map<String, String> headers, String metodo, String encode) throws IOException {
        // Envia parametros
        URL url = new URL(urlRequest);
        HttpURLConnection conn = (HttpURLConnection) url.openConnection();
        for (String keyHeader : headers.keySet()) {
            conn.setRequestProperty(keyHeader, headers.get(keyHeader));
        }
        conn.setRequestMethod(metodo);
        conn.setConnectTimeout(5000);
        conn.setReadTimeout(360000);
        if (corpo != null) {
            conn.setDoOutput(true);
            OutputStreamWriter wr = new OutputStreamWriter(conn.getOutputStream(), Charset.forName(encode));
            wr.write(corpo);
            wr.flush();
            wr.close();
        }
        // Pega a Resposta
        InputStream in = null;
        try{
            in = conn.getInputStream();
        }catch (IOException  e){
            if(conn.getResponseCode() != 200){
                in = conn.getErrorStream();
            }
        }
        BufferedReader rd = new BufferedReader(new InputStreamReader(in, Charset.forName(encode)));

        String line;
        String resposta = "";
        while ((line = rd.readLine()) != null) {
            resposta += line + "\n";
        }
        rd.close();
        return resposta;
    }

    public List<String> lerLogProcessamentoExtratos(ConvenioCobrancaVO convenioVO, int qtdMaximaLinhasApresentarExtrato) {
        String diretorioLocalLogExtrato = convenioVO.getDiretorioLocalLogExtrato();
        Uteis.logarDebug("#### Ler os arquivos do diretório local -->> " + diretorioLocalLogExtrato);
        List<String> lastLines = new ArrayList<>();
        try (ReversedLinesFileReader reader = new ReversedLinesFileReader(new File(diretorioLocalLogExtrato), 1024, "UTF-8")) {
            String line = "";
            while ((line = reader.readLine()) != null && lastLines.size() < qtdMaximaLinhasApresentarExtrato) {
                lastLines.add(line);
            }
        } catch (IOException e) {
            e.printStackTrace();
        }
        return lastLines;
    }

    public ConvenioCobrancaVO obterConvenioPadraoVerificacaoCartao(Integer empresa, int nivelMontarDados) throws Exception {
        String sql = "SELECT \n" +
                "c.* \n" +
                "FROM conveniocobranca c \n" +
                "where c.codigo in (select convenioVerificacaoCartao from empresa where codigo = " + empresa + ")";
        try (PreparedStatement ps = con.prepareStatement(sql)) {
            try (ResultSet rs = ps.executeQuery()) {
                if (rs.next()) {
                    return montarDados(rs, nivelMontarDados, this.con);
                }
                return null;
            }
        }
    }

    public TipoCredencialStoneEnum obterTipoCredenciamentoStoneByCodigoConvenio(int codigoConvenio) throws Exception {
        try {
            StringBuilder sql = new StringBuilder();
            sql.append("SELECT tipocredencialstoneenum from conveniocobranca where codigo = ").append(codigoConvenio);

            try (Statement stm = con.createStatement()) {
                try (ResultSet rs = stm.executeQuery(sql.toString())) {
                    if (rs.next()) {
                        return TipoCredencialStoneEnum.consultarPorCodigo(rs.getInt("tipocredencialstoneenum"));
                    }
                }
            }
            return null;
        } catch (Exception ex) {
            return null;
        }
    }

    public void existeConvenioPixMesmaChaveNaMesmaEmpresa(ConvenioCobrancaVO obj, boolean novoObj) throws Exception {

        for (ConvenioCobrancaEmpresaVO empresa : obj.getConfiguracoesEmpresa()) {
            Integer codEmpresa = empresa.getEmpresa().getCodigo();

            StringBuilder sql = new StringBuilder();
            sql.append("SELECT EXISTS \n");
            sql.append("(SELECT * FROM conveniocobranca c \n");
            sql.append("INNER JOIN conveniocobrancaempresa c2 ON c2.conveniocobranca = c.codigo \n");
            sql.append("WHERE c.situacao = " + SituacaoConvenioCobranca.ATIVO.getCodigo() + "\n");
            sql.append("AND c2.empresa = " + codEmpresa + "\n");
            sql.append("AND c.pix_chave = '" + obj.getPixChave() + "'\n");
            if (!novoObj){
                sql.append("AND c.codigo <> " + obj.getCodigo());
            }
            sql.append(") as existe");

            try (Statement stm = con.createStatement()) {
                try (ResultSet rs = stm.executeQuery(sql.toString())) {
                    if (rs.next()) {
                        boolean existe = rs.getBoolean("existe");
                        if (existe){
                            throw new Exception("Já existe um convênio pix na empresa "+ empresa.getEmpresa().getNome() + " com a mesma 'chave da conta pix': " + obj.getPixChave());
                        }
                    }
                }
            }
        }
    }

    public Map<Integer, ConvenioCobrancaVO> obterMapaConvenios(Integer empresa, int nivelMontarDados) throws Exception {
        Map<Integer, ConvenioCobrancaVO> mapa = new HashMap<>();
        List<ConvenioCobrancaVO> convenios = consultarPorEmpresa(empresa, false, nivelMontarDados);
        for (ConvenioCobrancaVO obj : convenios) {
            mapa.put(obj.getCodigo(), obj);
        }
        return mapa;
    }

    public List<ConvenioCobrancaVO> consultarPorTipoECodEmpresa(TipoConvenioCobrancaEnum tipoConvenio, int codEmpresa, SituacaoConvenioCobranca situacaoConvenio, int nivelMontarDados) throws Exception {
        StringBuilder sql = new StringBuilder();
        sql.append("select * from conveniocobranca c \n");
        sql.append("inner join conveniocobrancaempresa cce on cce.conveniocobranca = c.codigo \n");
        sql.append("where tipoconvenio = ").append(tipoConvenio.getCodigo());
        sql.append(" and cce.empresa = ").append(codEmpresa);
        sql.append(" and c.situacao = ").append(situacaoConvenio.getCodigo());
        sql.append(" ORDER BY cce.empresa");
        try (Statement stm = con.createStatement()) {
            try (ResultSet tabelaResultado = stm.executeQuery(sql.toString())) {
                return montarDadosConsulta(tabelaResultado, nivelMontarDados, con);
            }
        }
    }

    public void preencherTabelasEnums(boolean forcarAtualizacao) {
        try {
            if (forcarAtualizacao) {
                SuperFacadeJDBC.executarUpdate("DELETE FROM tipoconveniocobranca", this.con);
                SuperFacadeJDBC.executarUpdate("DELETE FROM origemcobranca", this.con);
            }

            //TipoConvenioCobrancaEnum
            Integer totalCadastrado = SuperFacadeJDBC.contar("select count(*) as qtd from (select codigo from tipoconveniocobranca) as sql", this.con);
            if (totalCadastrado < TipoConvenioCobrancaEnum.values().length) {
                for (TipoConvenioCobrancaEnum tipo : TipoConvenioCobrancaEnum.values()) {
                    String sql = "INSERT INTO tipoconveniocobranca(codigo,descricao,tipo_cobranca) select ?, ?, ? where not exists(select codigo from tipoconveniocobranca where codigo = ?);";
                    try (PreparedStatement pst = con.prepareStatement(sql)) {
                        int i = 0;
                        pst.setInt(++i, tipo.getCodigo());
                        pst.setString(++i, processarDescricao(tipo.getDescricao()));
                        pst.setString(++i, processarDescricao(tipo.getTipoCobranca().getDescricao()));
                        pst.setInt(++i, tipo.getCodigo());
                        pst.execute();
                    }
                }
            }
        } catch (Exception ex) {
            ex.printStackTrace();
        }

        try {
            SuperFacadeJDBC.executarUpdate("DELETE FROM origemcobranca where reguacobranca is null", this.con);

            //OrigemCobrancaEnum
            Integer totalCadastrado = SuperFacadeJDBC.contar("select count(*) as qtd from (select codigo from origemcobranca) as sql", this.con);
            if (totalCadastrado < OrigemCobrancaEnum.values().length) {
                for (OrigemCobrancaEnum origem : OrigemCobrancaEnum.values()) {
                    String sql = "INSERT INTO origemcobranca(codigo,descricao, reguacobranca) select ?, ?, ? where not exists(select codigo from origemcobranca where codigo = ?);";
                    try (PreparedStatement pst = con.prepareStatement(sql)) {
                        int i = 0;
                        pst.setInt(++i, origem.getCodigo());
                        pst.setString(++i, processarDescricao(origem.getDescricao()));
                        pst.setBoolean(++i, origem.isReguaCobranca());
                        pst.setInt(++i, origem.getCodigo());
                        pst.execute();
                    }
                }
            }
        } catch (Exception ex) {
            ex.printStackTrace();
        }
    }

    private String processarDescricao(String descricao) {
        return Uteis.retirarAcentuacaoRegex(descricao).replaceAll(" ", "_")
                .replaceAll("\\.", "_")
                .replaceAll("-", "_")
                .replaceAll("\\(", "_")
                .replaceAll("\\)", "_")
                .replaceAll("___", "_")
                .replaceAll("__", "_")
                .toUpperCase();
    }

    private String montarCaseTipoConvenio() {
        StringBuilder sb = new StringBuilder();
        sb.append("CASE");
        TipoConvenioCobrancaEnum[] lista = TipoConvenioCobrancaEnum.values();
        for (TipoConvenioCobrancaEnum tipo : lista) {
            sb.append(" WHEN tipoconvenio = '" + tipo.getCodigo() + "' THEN '" + Uteis.retirarAcentuacao(tipo.getDescricao()).replaceAll("_", "") + "'");
        }
        sb.append(" END as tipoConvenioDescricao \n");
        return sb.toString();
    }

    public List<ConvenioCobrancaVO> obterListaConvenioCobrancaRetentativa(Integer empresa) throws Exception {
        try {
            Integer tipos[] = {TipoConvenioCobrancaEnum.DCC.getCodigo(),
                    TipoConvenioCobrancaEnum.DCC_GETNET.getCodigo(),
                    TipoConvenioCobrancaEnum.DCC_BIN.getCodigo(),
                    //transação online
                    TipoConvenioCobrancaEnum.DCC_VINDI.getCodigo(),
                    TipoConvenioCobrancaEnum.DCC_CIELO_ONLINE.getCodigo(),
                    TipoConvenioCobrancaEnum.DCC_MAXIPAGO.getCodigo(),
                    TipoConvenioCobrancaEnum.DCC_E_REDE.getCodigo(),
                    TipoConvenioCobrancaEnum.DCC_GETNET_ONLINE.getCodigo(),
                    TipoConvenioCobrancaEnum.DCC_STONE_ONLINE.getCodigo(),
                    TipoConvenioCobrancaEnum.DCC_STONE_ONLINE_V5.getCodigo(),
                    TipoConvenioCobrancaEnum.DCC_MUNDIPAGG.getCodigo(),
                    TipoConvenioCobrancaEnum.DCC_PAGAR_ME.getCodigo(),
                    TipoConvenioCobrancaEnum.DCC_PAGBANK.getCodigo(),
                    TipoConvenioCobrancaEnum.DCC_STRIPE.getCodigo(),
                    TipoConvenioCobrancaEnum.DCC_PAGOLIVRE.getCodigo(),
                    TipoConvenioCobrancaEnum.DCC_FACILITEPAY.getCodigo(),
                    TipoConvenioCobrancaEnum.DCC_CEOPAG.getCodigo(),
                    TipoConvenioCobrancaEnum.DCC_PINBANK.getCodigo()};

            List<ConvenioCobrancaVO> convenios = consultarPorEmpresaESituacao(empresa, false, Uteis.NIVELMONTARDADOS_TODOS,
                    tipos, SituacaoConvenioCobranca.ATIVO);
            List<ConvenioCobrancaVO> lista = new ArrayList<>();
            for (ConvenioCobrancaVO obj : convenios) {

                //PinBank deve mostrar como opção somente se o cliente tiver só um convênio ativo e que seja PinBank
//                if (convenios.size() == 1) {
//                    if (obj.getTipo().equals(TipoConvenioCobrancaEnum.DCC_PINBANK)) {
//                        lista.add(obj);
//                        break;
//                    }
//                }

                if (obj.isSomenteExtrato()) {
                    //ignorar convênios que são somente extrato
                    continue;
                }
//                if (obj.getTipo().equals(TipoConvenioCobrancaEnum.DCC_PINBANK)){
//                    //ignorar convênios que são PinBank
//                    continue;
//                }

                lista.add(obj);
            }
            Ordenacao.ordenarLista(lista, "descricao");
            return lista;
        } catch (Exception ex) {
            ex.printStackTrace();
            throw ex;
        }
    }

    @Override
    public List consultarPorDescricao(String valorConsulta, boolean controlarAcesso, int nivelMontarDados) throws Exception {
        consultar(getIdEntidade(), controlarAcesso);
        String sqlStr = "SELECT * FROM conveniocobranca  WHERE upper( descricao ) like('" + valorConsulta.toUpperCase() + "%') ORDER BY descricao";
        Statement stm = con.createStatement();
        ResultSet tabelaResultado = stm.executeQuery(sqlStr);
        return montarDadosConsulta(tabelaResultado, nivelMontarDados, con);
    }

    @Override
    public void alterarStatusConciliacaoERede(ConvenioCobrancaVO convenioCobrancaVO) throws Exception {
        String sql = "UPDATE ConvenioCobranca set statusconciliacaoredeonline = ? WHERE codigo = ?";
        try (PreparedStatement ps = con.prepareStatement(sql)) {
            ps.setInt(1, convenioCobrancaVO.getStatusConciliacaoRedeOnline().getId());
            ps.setInt(2, convenioCobrancaVO.getCodigo());
            ps.executeUpdate();
        }
    }

    @Override
    public void alterarBloquearCobrancasAutomaticas(int codConvenio, boolean bloquearcobrancaautomatica) throws Exception {
        String sql = "UPDATE conveniocobranca set bloquearcobrancaautomatica = " +  bloquearcobrancaautomatica + " WHERE codigo = " + codConvenio;
        try (PreparedStatement ps = con.prepareStatement(sql)) {
            ps.executeUpdate();
        }
    }

    public void incluirInformacoesAccessTokenPagBank(String accessToken, String authorizationCode, String refreshToken, int codConvenio) throws Exception {
        String sql = "UPDATE ConvenioCobranca set codigoautenticacao01 = ?, codigoautenticacao02 = ?, refreshtoken = ?, dataGeracaoAccessToken = ? WHERE codigo = ?";
        try (PreparedStatement ps = con.prepareStatement(sql)) {
            ps.setString(1, accessToken);
            ps.setString(2, authorizationCode);
            ps.setString(3, refreshToken);
            ps.setTimestamp(4, Uteis.getDataJDBCTimestamp(Calendario.hoje()));
            ps.setInt(5, codConvenio);
            ps.executeUpdate();
        }
    }
}

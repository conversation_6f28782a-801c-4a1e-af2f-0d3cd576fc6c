
package negocio.facade.jdbc.financeiro;

import negocio.comuns.financeiro.MetaFinanceiraEmpresaValoresVO;
import negocio.comuns.utilitarias.Uteis;
import negocio.facade.jdbc.arquitetura.SuperEntidade;
import negocio.interfaces.financeiro.MetaFinanceiraEmpresaValoresInterfaceFacade;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.util.ArrayList;
import java.util.List;

/**
 *
 * <AUTHOR>
 */
public class MetaFinanceiraEmpresaValores extends SuperEntidade implements MetaFinanceiraEmpresaValoresInterfaceFacade {

    public MetaFinanceiraEmpresaValores() throws Exception {
        super();
    }

    public MetaFinanceiraEmpresaValores(Connection conexao) throws Exception {
        super(conexao);
    }

    @Override
    public void incluir(MetaFinanceiraEmpresaValoresVO obj) throws Exception {
        try {
            con.setAutoCommit(false);
            incluirSemCommit(obj);
            con.commit();
        } catch (Exception e) {
            con.rollback();
            throw e;
        } finally {
            con.setAutoCommit(true);
        }
    }

    @Override
    public void incluirSemCommit(MetaFinanceiraEmpresaValoresVO obj) throws Exception {
        obj.validarDados();
        PreparedStatement sql = con.prepareStatement("INSERT INTO MetaFinanceiraEmpresaValores "
                + "(metaFinanceiraEmpresa, valor, cor, observacao) VALUES (?, ?, ?, ?)");
        sql.setInt(1, obj.getMetaFinanceiraEmpresa());
        sql.setDouble(2, obj.getValor());
        sql.setString(3, obj.getCor());
        sql.setString(4, obj.getObservacao());
        sql.execute();
        obj.setCodigo(obterValorChavePrimariaCodigo());
        obj.setNovoObj(false);
    }

    @Override
    public void alterar(MetaFinanceiraEmpresaValoresVO obj) throws Exception {
        try {
            con.setAutoCommit(false);
            alterarSemCommit(obj);
            con.commit();
        } catch (Exception e) {
            con.rollback();
            throw e;
        } finally {
            con.setAutoCommit(true);
        }
    }

    public List<MetaFinanceiraEmpresaValoresVO> consultar(int codigoEmpresa, int mes, int ano, int nivelMontarDados) throws Exception{
        StringBuilder sql = new StringBuilder();
        sql.append("select mv.*  \n");
        sql.append("from metaFinanceiraempresa mfe \n");
        sql.append("inner join metaFinanceiraempresaValores mv on mv.metaFinanceiraempresa = mfe.codigo \n");
        sql.append("where mfe.mes = ").append(mes).append(" and mfe.ano = ").append(ano).append(" and  mfe.empresa = ").append(codigoEmpresa).append(" \n");
        sql.append("order by mv.valor asc");
        PreparedStatement pst = con.prepareStatement(sql.toString());
        ResultSet rs = pst.executeQuery();
        return montarDadosConsulta(rs, nivelMontarDados, con);
    }

    @Override
    public void alterarSemCommit(MetaFinanceiraEmpresaValoresVO obj) throws Exception {
        obj.validarDados();
        PreparedStatement sqlAlterar = con.prepareStatement("UPDATE MetaFinanceiraEmpresaValores SET "
                + "metaFinanceiraEmpresa=?, valor=?, cor=?, observacao=? WHERE codigo = ?");
        sqlAlterar.setInt(1, obj.getMetaFinanceiraEmpresa());
        sqlAlterar.setDouble(2, obj.getValor());
        sqlAlterar.setString(3, obj.getCor());
        sqlAlterar.setString(4, obj.getObservacao());
        sqlAlterar.setInt(5, obj.getCodigo());
        sqlAlterar.execute();
    }

    @Override
    public void excluir(MetaFinanceiraEmpresaValoresVO obj) throws Exception {
        try {
            con.setAutoCommit(false);
            excluirSemCommit(obj);
            con.commit();
        } catch (Exception e) {
            con.rollback();
            throw e;
        } finally {
            con.setAutoCommit(true);
        }
    }

    @Override
    public void excluirSemCommit(MetaFinanceiraEmpresaValoresVO obj) throws Exception {
        PreparedStatement sqlExcluir = con.prepareStatement("DELETE FROM MetaFinanceiraEmpresaValores WHERE codigo = ?");
        sqlExcluir.setInt(1, obj.getCodigo());
        sqlExcluir.execute();
    }

    @Override
    public void excluirPelaMeta(int meta) throws Exception {
        PreparedStatement sqlExcluir = con.prepareStatement("DELETE FROM MetaFinanceiraEmpresaValores WHERE metaFinanceiraEmpresa = ?");
        sqlExcluir.setInt(1, meta);
        sqlExcluir.execute();
    }

    @Override
    public MetaFinanceiraEmpresaValoresVO consultarPorChavePrimaria(int codigo, int nivelMontarDados) throws Exception {
        StringBuilder str = new StringBuilder();
        str.append("SELECT * FROM MetaFinanceiraEmpresaValores WHERE codigo = ").append(codigo);
        PreparedStatement stm = con.prepareStatement(str.toString());
        ResultSet tabelaResultado = stm.executeQuery();
        if(tabelaResultado.next())
            return montarDados(tabelaResultado, nivelMontarDados, this.con);
        else
            return new MetaFinanceiraEmpresaValoresVO();
    }

    @Override
    public List<MetaFinanceiraEmpresaValoresVO> consultarPorMetaFinanceiraEmpresa(int codigo, int nivelMontarDados) throws Exception {
        StringBuilder str = new StringBuilder();
        str.append("SELECT * FROM MetaFinanceiraEmpresaValores WHERE metaFinanceiraEmpresa = ")
           .append(codigo)
           .append(" order by codigo");
        PreparedStatement stm = con.prepareStatement(str.toString());
        ResultSet tabelaResultado = stm.executeQuery();
        return montarDadosConsulta(tabelaResultado, nivelMontarDados, this.con);
    }
    
    public List<MetaFinanceiraEmpresaValoresVO> montarDadosConsulta(ResultSet tabelaResultado, int nivelMontarDados, Connection con) throws Exception {
        List<MetaFinanceiraEmpresaValoresVO> vetResultado = new ArrayList<MetaFinanceiraEmpresaValoresVO>();
        while (tabelaResultado.next())
            vetResultado.add(montarDados(tabelaResultado, nivelMontarDados, con));
        return vetResultado;
    }

    public MetaFinanceiraEmpresaValoresVO montarDados(ResultSet dadosSQL, int nivelMontarDados, Connection con) throws Exception {
        MetaFinanceiraEmpresaValoresVO obj = montarDadosBasico(dadosSQL);
        if (nivelMontarDados == Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA) {
            return obj;
        }
        return obj;
    }

    private MetaFinanceiraEmpresaValoresVO montarDadosBasico(ResultSet dadosSQL) throws Exception {
        MetaFinanceiraEmpresaValoresVO obj = new MetaFinanceiraEmpresaValoresVO();
        obj.setNovoObj(false);
        obj.setCodigo(dadosSQL.getInt("codigo"));
        obj.setObservacao(dadosSQL.getString("observacao"));
        obj.setCor(dadosSQL.getString("cor"));
        obj.setValor(dadosSQL.getDouble("valor"));
        obj.setMetaFinanceiraEmpresa(dadosSQL.getInt("metaFinanceiraEmpresa"));
        return obj;
    }
}

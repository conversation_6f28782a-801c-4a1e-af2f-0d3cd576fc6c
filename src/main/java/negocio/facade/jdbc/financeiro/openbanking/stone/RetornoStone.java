package negocio.facade.jdbc.financeiro.openbanking.stone;

import negocio.comuns.financeiro.openbanking.stone.PagamentoStoneVO;
import negocio.comuns.financeiro.openbanking.stone.RetornoStoneVO;
import negocio.facade.jdbc.arquitetura.SuperEntidade;
import negocio.interfaces.financeiro.openbanking.stone.RetornoStoneInterfaceFacade;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;

public class RetornoStone extends SuperEntidade implements RetornoStoneInterfaceFacade {

    public RetornoStone() throws Exception {
        super();
    }

    public RetornoStone(Connection con) throws Exception {
        super(con);
    }

    @Override
    public void incluir(RetornoStoneVO obj) throws Exception {
        PagamentoStoneVO.validarDados(obj);
        String insert = "insert into retornoStone (eventId, transacaoId, empresa, status, retorno, eventType)" +
                "values (?,?,?,?,?,?)";
        PreparedStatement stm = con.prepareStatement(insert);
        stm.setString(1, obj.getEventId());
        stm.setString(2, obj.getTransacaoId());
        stm.setInt(3, obj.getEmpresa());
        stm.setString(4, obj.getStatus());
        stm.setString(5, obj.getRetorno());
        stm.setString(6, obj.getEventType());
        stm.execute();
        obj.setNovoObj(false);
    }

    @Override
    public void alterar(RetornoStoneVO obj) throws Exception {
        PagamentoStoneVO.validarDados(obj);
        String update = "update retornoStone set transacaoId = ?, empresa = ?, status = ?, retorno = ?, eventType = ? where eventId = ?";
        PreparedStatement stm = con.prepareStatement(update);
        stm.setString(1, obj.getTransacaoId());
        stm.setInt(2, obj.getEmpresa());
        stm.setString(3, obj.getStatus());
        stm.setString(4, obj.getRetorno());
        stm.setString(5, obj.getEventType());
        stm.setString(6, obj.getEventId());
        stm.execute();
    }

    public RetornoStoneVO buscarRetornoStoneByEventId(String eventId, Integer empresa){
        StringBuilder sql = new StringBuilder();
        sql.append("select * from retornostone  where eventId = ? and empresa = ?");
        try (PreparedStatement sqlConsultar = con.prepareStatement(sql.toString())) {
            sqlConsultar.setString(1, eventId);
            sqlConsultar.setInt(2, empresa);
            try (ResultSet tabelaResultado = sqlConsultar.executeQuery()) {
                if (tabelaResultado.next()) {
                    return montarDados(tabelaResultado);
                }
                return null;
            }
        } catch (Exception e) {
            return null;
        }
    }

    public RetornoStoneVO montarDados(ResultSet rs) throws Exception{
        RetornoStoneVO retornoStone = new RetornoStoneVO();
        retornoStone.setEventId(rs.getString("eventId"));
        retornoStone.setTransacaoId(rs.getString("transacaoId"));
        retornoStone.setEmpresa(rs.getInt("empresa"));
        retornoStone.setStatus(rs.getString("status"));
        retornoStone.setRetorno(rs.getString("retorno"));
        retornoStone.setEventType(rs.getString("eventType"));
        return retornoStone;
    }
}

package negocio.facade.jdbc.financeiro.openbanking.stone;


import br.com.pactosolucoes.comuns.json.JSONMapper;
import negocio.comuns.financeiro.openbanking.stone.TransferenciaStoneVO;
import negocio.facade.jdbc.arquitetura.SuperEntidade;
import negocio.interfaces.financeiro.openbanking.stone.TransferenciaStoneInterfaceFacade;
import org.json.JSONObject;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;

public class TransferenciaStone extends SuperEntidade implements TransferenciaStoneInterfaceFacade {

    public TransferenciaStone() throws Exception {
        super();
    }

    public TransferenciaStone(Connection con) throws Exception {
        super(con);
    }

    @Override
    public void incluir(TransferenciaStoneVO obj) throws Exception {
        TransferenciaStoneVO.validarDados(obj);
        String insert = "insert into transferenciaStone (id, empresa, idempotency_key, account_id, created_at, created_by, amount, scheduled_to, movConta, eventIdWebhook, operation_type, movContaDesc)" +
                "values (?,?,?,?,?,?,?,?,?,?,?,?)";
        PreparedStatement stm = con.prepareStatement(insert);
        stm.setString(1, obj.getId());
        stm.setInt(2, obj.getEmpresa());
        stm.setString(3, obj.getIdempotency_key());
        stm.setString(4, obj.getAccount_id());
        stm.setString(5, obj.getCreated_at());
        stm.setString(6, obj.getCreated_by());
        stm.setInt(7, obj.getAmount());
        stm.setString(8, obj.getScheduled_to());
        stm.setInt(9, obj.getMovConta());
        stm.setString(10, obj.getEventIdWebhook());
        stm.setString(11, obj.getOperation_type());
        stm.setInt(12, obj.getMovContaDesc());
        stm.execute();
        obj.setCodigo(obterValorChavePrimariaCodigo());
        obj.setNovoObj(false);
    }

    @Override
    public void alterar(TransferenciaStoneVO obj) throws Exception {
        TransferenciaStoneVO.validarDados(obj);
        String update = "update transferenciaStone set id = ?, empresa = ?, idempotency_key = ?, account_id = ?," +
                " created_at = ?, created_by = ?, amount = ?, scheduled_to = ?, movConta = ?, eventIdWebhook = ?, operation_type = ?, movContaDesc = ? where codigo = ?";
        PreparedStatement stm = con.prepareStatement(update);
        stm.setString(1, obj.getId());
        stm.setInt(2, obj.getEmpresa());
        stm.setString(3, obj.getIdempotency_key());
        stm.setString(4, obj.getAccount_id());
        stm.setString(5, obj.getCreated_at());
        stm.setString(6, obj.getCreated_by());
        stm.setInt(7, obj.getAmount());
        stm.setString(8, obj.getScheduled_to());
        stm.setInt(9, obj.getMovConta());
        stm.setString(10, obj.getEventIdWebhook());
        stm.setString(11, obj.getOperation_type());
        stm.setInt(12, obj.getMovContaDesc());
        stm.setInt(13, obj.getCodigo());
        stm.execute();
    }

    public TransferenciaStoneVO montarDados(ResultSet rs) throws Exception{
        TransferenciaStoneVO transferenciaStone = new TransferenciaStoneVO();
        transferenciaStone.setCodigo(rs.getInt("codigo"));
        transferenciaStone.setId(rs.getString("id"));
        transferenciaStone.setEmpresa(rs.getInt("empresa"));
        transferenciaStone.setIdempotency_key(rs.getString("idempotency_key"));
        transferenciaStone.setAccount_id(rs.getString("account_id"));
        transferenciaStone.setCreated_at(rs.getString("created_at"));
        transferenciaStone.setCreated_by(rs.getString("created_by"));
        transferenciaStone.setAmount(rs.getInt("amount"));
        transferenciaStone.setScheduled_to(rs.getString("scheduled_to"));
        transferenciaStone.setMovConta(rs.getInt("movConta"));
        transferenciaStone.setEventIdWebhook(rs.getString("eventIdWebhook"));
        transferenciaStone.setOperation_type(rs.getString("operation_type"));
        transferenciaStone.setMovContaDesc(rs.getInt("movContaDesc"));
        return transferenciaStone;
    }


    public TransferenciaStoneVO montarTransferencia(String response, Integer movContaZW, Integer empresaZW, Integer movContaDesc) throws Exception {
        TransferenciaStoneVO transferenciaStone = JSONMapper.getObject(new JSONObject(response), TransferenciaStoneVO.class);
        transferenciaStone.setMovConta(movContaZW);
        transferenciaStone.setMovContaDesc(movContaDesc);
        transferenciaStone.setEmpresa(empresaZW);
        return transferenciaStone;
    }

    public TransferenciaStoneVO buscarTransferenciaStoneById(String id, Integer empresa){
            StringBuilder sql = new StringBuilder();
            sql.append("select * from transferenciastone  where id = ? and empresa = ?");
            try (PreparedStatement sqlConsultar = con.prepareStatement(sql.toString())) {
                sqlConsultar.setString(1, id);
                sqlConsultar.setInt(2, empresa);
                try (ResultSet tabelaResultado = sqlConsultar.executeQuery()) {
                    if (tabelaResultado.next()) {
                        return montarDados(tabelaResultado);
                    }
                    return null;
                }
            } catch (Exception e) {
                return null;
            }
    }

    @Override
    public TransferenciaStoneVO buscarTransferenciaStoneByMovConta(Integer movConta, Integer empresa) {
        StringBuilder sql = new StringBuilder();
        sql.append("select * from transferenciastone  where movconta = ? and empresa = ?");
        try (PreparedStatement sqlConsultar = con.prepareStatement(sql.toString())) {
            sqlConsultar.setInt(1, movConta);
            sqlConsultar.setInt(2, empresa);
            try (ResultSet tabelaResultado = sqlConsultar.executeQuery()) {
                if (tabelaResultado.next()) {
                    return montarDados(tabelaResultado);
                }
                return null;
            }
        } catch (Exception e) {
            return null;
        }
    }
}

package negocio.facade.jdbc.financeiro.openbanking.stone;
import br.com.pactosolucoes.comuns.json.JSONMapper;
import negocio.comuns.financeiro.openbanking.stone.ContaStoneVO;
import negocio.facade.jdbc.arquitetura.SuperEntidade;
import negocio.interfaces.financeiro.openbanking.stone.ContaStoneInterfaceFacade;
import org.json.JSONObject;

import java.sql.*;

public class ContaStone extends SuperEntidade implements ContaStoneInterfaceFacade {

    public ContaStone() throws Exception {
        super();
    }

    public ContaStone(Connection con) throws Exception {
        super(con);
    }

    @Override
    public void incluir(ContaStoneVO obj) throws Exception {
        ContaStoneVO.validarDados(obj);
        String insert = "insert into contaStone (empresa, account_code, branch_code, id, owner_document, owner_id, owner_name, restricted_features)" +
                "values (?,?,?,?,?,?,?,?)";
        PreparedStatement stm = con.prepareStatement(insert);
        stm.setInt(1, obj.getEmpresa());
        stm.setString(2, obj.getAccount_code());
        stm.setString(3, obj.getBranch_code());
        stm.setString(4, obj.getId());
        stm.setString(5, obj.getOwner_document());
        stm.setString(6, obj.getOwner_id());
        stm.setString(7, obj.getOwner_name());
        stm.setBoolean(8, obj.getRestricted_features());
        stm.execute();
        obj.setCodigo(obterValorChavePrimariaCodigo());
        obj.setNovoObj(false);
    }

    @Override
    public void alterar(ContaStoneVO obj) throws Exception {
        ContaStoneVO.validarDados(obj);
        String update = "update contaStone set empresa = ?, account_code = ?, branch_code = ?, id = ?, owner_document = ?, owner_id = ?, owner_name = ?, restricted_features = ? where codigo = ?";
        PreparedStatement stm = con.prepareStatement(update);
        stm.setInt(1, obj.getEmpresa());
        stm.setString(2, obj.getAccount_code());
        stm.setString(3, obj.getBranch_code());
        stm.setString(4, obj.getId());
        stm.setString(5, obj.getOwner_document());
        stm.setString(6, obj.getOwner_id());
        stm.setString(7, obj.getOwner_name());
        stm.setBoolean(8, obj.getRestricted_features());
        stm.setInt(9, obj.getCodigo());
        stm.execute();
    }

    public ContaStoneVO montarDados(ResultSet rs) throws Exception{
        ContaStoneVO contaStone = new ContaStoneVO();
        contaStone.setCodigo(rs.getInt("codigo"));
        contaStone.setEmpresa(rs.getInt("empresa"));
        contaStone.setAccount_code(rs.getString("account_code"));
        contaStone.setBranch_code(rs.getString("branch_code"));
        contaStone.setId(rs.getString("id"));
        contaStone.setOwner_document(rs.getString("owner_document"));
        contaStone.setOwner_id(rs.getString("owner_id"));
        contaStone.setOwner_name(rs.getString("owner_name"));
        contaStone.setRestricted_features(rs.getBoolean("restricted_features"));
        return contaStone;
    }

    public ContaStoneVO montarContaStoneInserirZwFin(String response, Integer empresaZW) throws Exception {
        ContaStoneVO contaStone = buscarContaStone(empresaZW);
        if (contaStone == null) {
            contaStone = JSONMapper.getObject(new JSONObject(response), ContaStoneVO.class);
            contaStone.setEmpresa(empresaZW);
        }


        // Conta Financeiro
        contaStone.setDescricaoConta("STONE PAGAMENTOS S.A");
        contaStone.setDescricaoCurtaConta("STONEBANK");
        contaStone.setCodigoBanco(197);
        contaStone.setAgenciaConta("0001");
        contaStone.setContaAtiva(true);
        contaStone.setContaTotalizadaBI(false);
        contaStone.setContaObservacao("Conta inserida através da integração stone openbank.\n" +
                "Esta conta tem permissão para:\n" +
                "Cria pedidos de pagamentos com código de barras.\n" +
                "Cria pedidos de transferência para folhas de pagamento.\n" +
                "Cria pedidos de transferência para outras contas Stone.\n" +
                "Cria pedidos de transferência para contas externas.\n" +
                "Visualiza informações das folhas de pagamento.\n" +
                "Visualiza saldo, extratos e comprovantes de uma conta de pagamento.\n" +
                "Busca de contatos associados à conta de pagamento.\n" +
                "Busca as taxas associadas à conta.\n" +
                "Cria, atualiza ou remove contatos associados à conta de pagamento.\n" +
                "Busca as operações pendentes em um determinado recurso.\n" +
                "Esta conta não tem permissão para:\n" +
                "Aprovação de transferências internas e externas.\n" +
                "Aprovação de pagamentos.");
        //TipoBanoZw
        contaStone.setDescricaoTipoConta("OpenBank");
        contaStone.setComportamentoTipoConta("OpenBank");
        // BancoOpenBankEnumZw
        contaStone.setDescricaoBancoOpenBankZW("STONE OPENBANK");
        return contaStone;
    }

    public String buscarAccountIdContaStone(Integer empresa) {
        StringBuilder sql = new StringBuilder();
        sql.append("select id from contastone  where empresa = ?");
        try (PreparedStatement sqlConsultar = con.prepareStatement(sql.toString())) {
            sqlConsultar.setInt(1, empresa);
            try (ResultSet tabelaResultado = sqlConsultar.executeQuery()) {
                if (tabelaResultado.next()) {
                    return tabelaResultado.getString("id");
                }
                return "";
            }
        } catch (SQLException e) {
            return "";
        }
    }

    public ContaStoneVO buscarContaStone(Integer empresa) {
        StringBuilder sql = new StringBuilder();
        sql.append("select * from contastone  where empresa = ?");
        try (PreparedStatement sqlConsultar = con.prepareStatement(sql.toString())) {
            sqlConsultar.setInt(1, empresa);
            try (ResultSet tabelaResultado = sqlConsultar.executeQuery()) {
                if (tabelaResultado.next()) {
                    return montarDados(tabelaResultado);
                }
                return null;
            }
        } catch (Exception e) {
            return null;
        }
    }
}

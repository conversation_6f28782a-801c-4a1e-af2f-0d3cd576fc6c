package negocio.facade.jdbc.financeiro;

import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.sql.Statement;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.sql.Connection;

import negocio.comuns.financeiro.CartaoCreditoTO;
import negocio.comuns.financeiro.CartaoCreditoVO;
import negocio.comuns.financeiro.HistoricoCartaoTO;
import negocio.comuns.financeiro.LoteVO;
import negocio.comuns.financeiro.MovContaRateioVO;
import negocio.comuns.financeiro.MovContaVO;
import negocio.comuns.financeiro.enumerador.TipoES;
import negocio.comuns.financeiro.enumerador.TipoFormaPagto;
import negocio.comuns.utilitarias.Calendario;
import negocio.comuns.utilitarias.ConsistirException;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisValidacao;
import negocio.comuns.utilitarias.UtilReflection;
import negocio.facade.jdbc.arquitetura.SuperEntidade;

/**
 * Classe de persistência que encapsula todas as operações de manipulação dos
 * dados da classe
 * <code>CartaoCreditoVO</code>. Responsável por implementar operações como
 * incluir, alterar, excluir e consultar pertinentes a classe
 * <code>CartaoCreditoVO</code>. Encapsula toda a interação com o banco de
 * dados.
 *
 * @see CartaoCreditoVO
 * @see SuperEntidade
 * @see MovPagamento
 */
public class CartaoCredito extends SuperEntidade {

    public CartaoCredito() throws Exception {
        super();
        setIdEntidade("MovPagamento");
    }

    public CartaoCredito(Connection conexao) throws Exception {
        super(conexao);
        setIdEntidade("MovPagamento");
    }

    /**
     * Operação responsável por retornar um novo objeto da classe
     * <code>CartaoCreditoVO</code>.
     */
    public CartaoCreditoVO novo() throws Exception {
        incluir(getIdEntidade());
        CartaoCreditoVO obj = new CartaoCreditoVO();
        return obj;
    }

    /**
     * Operação responsável por incluir no banco de dados um objeto da classe
     * <code>CartaoCreditoVO</code>. Primeiramente valida os dados (
     * <code>validarDados</code>) do objeto. Verifica a conexão com o banco de
     * dados e a permissão do usuário para realizar esta operacão na entidade.
     * Isto, através da operação
     * <code>incluir</code> da superclasse.
     *
     * @param obj Objeto da classe <code>CartaoCreditoVO</code> que será gravado
     * no banco de dados.
     * @exception Exception Caso haja problemas de conexão, restrição de acesso
     * ou validação de dados.
     */
    public void incluir(CartaoCreditoVO obj) throws Exception {
        CartaoCreditoVO.validarDados(obj);
        incluir(getIdEntidade());
        obj.realizarUpperCaseDados();

        String sql = "INSERT INTO CartaoCredito( movpagamento, dataCompesancao, valor, operadoracartao, situacao, movconta,nomenocartao, nrdocumento, composicao, valortotal, produtospagos,nrparcela) VALUES ( ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)";

        try (PreparedStatement sqlInserir = con.prepareStatement(sql)) {
            if (obj.getMovpagamento().getCodigo().intValue() != 0) {
                sqlInserir.setInt(1, obj.getMovpagamento().getCodigo().intValue());
            } else {
                sqlInserir.setNull(1, 0);
            }
            sqlInserir.setTimestamp(2, Uteis.getDataJDBCTimestamp(obj.getDataCompensacao()));
            sqlInserir.setDouble(3, obj.getValor().doubleValue());

            if (obj.getOperadora().getCodigo().intValue() != 0) {
                sqlInserir.setInt(4, obj.getOperadora().getCodigo().intValue());
            } else {
                sqlInserir.setNull(4, 0);
            }
            sqlInserir.setString(5, obj.getSituacao());

            if (obj.getMovConta() != null
                    && !UteisValidacao.emptyNumber(obj.getMovConta().getCodigo())) {
                sqlInserir.setInt(6, obj.getMovConta().getCodigo().intValue());
            } else {
                sqlInserir.setNull(6, 0);
            }

            sqlInserir.setString(7, obj.getNomeNoCartao());
            sqlInserir.setString(8, obj.getNrDocumento());

            sqlInserir.setString(9, obj.getComposicao());
            sqlInserir.setDouble(10, obj.getValorTotal());
            sqlInserir.setString(11, obj.getProdutosPagos());
            sqlInserir.setInt(12, obj.getNrParcela());

            sqlInserir.execute();
        }
        obj.setCodigo(obterValorChavePrimariaCodigo());
        if (obj.getComposicao() != null && !obj.getComposicao().equals("")) {
            atualizarComposicao(obj);
        }

        if (UtilReflection.objetoMaiorQueZero(obj, "getNfSeEmitidaCartaoExcluido().getCodigo()")){
            obj.getNfSeEmitidaCartaoExcluido().setCartaoCredito(obj);
            NFSeEmitida nfSeEmitidaDAO = new NFSeEmitida(con);
            nfSeEmitidaDAO.incluir(obj.getNfSeEmitidaCartaoExcluido());
            nfSeEmitidaDAO = null;
        }

        obj.setNovoObj(false);
    }

    private void atualizarComposicao(CartaoCreditoVO obj) throws SQLException {
        String[] cartoes = obj.getComposicao().split(",");
        for (int i = 0; i < cartoes.length; i++) {
            Integer codigoCartao = Integer.parseInt(cartoes[i]);
            String composicao = obj.getCodigo().toString();
            for (int j = 0; j < cartoes.length; j++) {
                if (j != i) {
                    composicao += "," + cartoes[j];
                }
            }
            alterarComposicao(codigoCartao, composicao);
        }

    }

    private void alterarComposicao(int codigo, String composicao) throws SQLException {
        String sql = "UPDATE cartaocredito SET composicao=? WHERE codigo = ?";
        try (PreparedStatement sqlAlterar = con.prepareStatement(sql)) {

            sqlAlterar.setString(1, composicao);

            sqlAlterar.setInt(2, codigo);
            sqlAlterar.execute();
        }

    }

    /**
     * Operação responsável por alterar no BD os dados de um objeto da classe
     * <code>CartaoCreditoVO</code>. Sempre utiliza a chave primária da classe
     * como atributo para localização do registro a ser alterado. Primeiramente
     * valida os dados (
     * <code>validarDados</code>) do objeto. Verifica a conexão com o banco de
     * dados e a permissão do usuário para realizar esta operacão na entidade.
     * Isto, através da operação
     * <code>alterar</code> da superclasse.
     *
     * @param obj Objeto da classe <code>CartaoCreditoVO</code> que será
     * alterada no banco de dados.
     * @exception Execption Caso haja problemas de conexão, restrição de acesso
     * ou validação de dados.
     */
    public void alterar(CartaoCreditoVO obj) throws Exception {
        CartaoCreditoVO.validarDados(obj);
        alterar(getIdEntidade());
        obj.realizarUpperCaseDados();
        String sql = "UPDATE CartaoCredito set movpagamento=?, dataCompesancao=?, "
                + "valor=?, dataoriginal=?, operadoracartao=?, situacao=?,nomenocartao = ?, nrdocumento = ?, movconta=?, valortotal=?, produtospagos=?, nrparcela=? WHERE codigo = ?";
        try (PreparedStatement sqlAlterar = con.prepareStatement(sql)) {
            if (obj.getMovpagamento().getCodigo().intValue() != 0) {
                sqlAlterar.setInt(1, obj.getMovpagamento().getCodigo().intValue());
            } else {
                sqlAlterar.setNull(1, 0);
            }
            sqlAlterar.setTimestamp(2, Uteis.getDataJDBCTimestamp(obj.getDataCompensacao()));
            sqlAlterar.setDouble(3, obj.getValor().doubleValue());
            if (obj.getDataOriginal() == null) {
                sqlAlterar.setNull(4, 0);
            } else {
                sqlAlterar.setDate(4, Uteis.getDataJDBC(obj.getDataOriginal()));
            }
            if (obj.getOperadora().getCodigo() == 0) {
                sqlAlterar.setNull(5, 0);
            } else {
                sqlAlterar.setInt(5, obj.getOperadora().getCodigo());
            }
            sqlAlterar.setString(6, obj.getSituacao());
            sqlAlterar.setString(7, obj.getNomeNoCartao());
            sqlAlterar.setString(8, obj.getNrDocumento());
            if (obj.getMovConta().getCodigo() > 0) {
                sqlAlterar.setInt(9, obj.getMovConta().getCodigo());
            } else {
                sqlAlterar.setNull(9, 0);
            }
            sqlAlterar.setDouble(10, obj.getValorTotal());
            sqlAlterar.setString(11, obj.getProdutosPagos());
            sqlAlterar.setInt(12, obj.getNrParcela());

            sqlAlterar.setInt(13, obj.getCodigo().intValue());

            sqlAlterar.execute();
        }
        if (obj.getComposicao() != null && !obj.getComposicao().equals("") && obj.getSituacao().equals("CA")) {
            apagarComposicao(obj.getCodigo());
        }
    }

    public void alterarDadosMudancaAutomaticaPagamentoConciliacao(CartaoCreditoVO obj) throws Exception {
        alterar(getIdEntidade());
        String sql = "UPDATE CartaoCredito set alterouDataRecebimentoZWAutomaticamente = ?, dataPgtoOriginalZWAntesDaAlteracaoAutomatica = ? WHERE codigo = ?";
        try (PreparedStatement sqlAlterar = con.prepareStatement(sql)) {
            sqlAlterar.setBoolean(1, obj.isAlterouDataRecebimentoZWAutomaticamente());
            sqlAlterar.setTimestamp(2, Uteis.getDataJDBCTimestamp(obj.getDataPgtoOriginalZWAntesDaAlteracaoAutomatica()));
            sqlAlterar.setInt(3, obj.getCodigo());
            sqlAlterar.execute();
        } catch (Exception ex) {}
    }

    /**
     * Operação responsável por excluir no BD um objeto da classe
     * <code>CartaoCreditoVO</code>. Sempre localiza o registro a ser excluído
     * através da chave primária da entidade. Primeiramente verifica a conexão
     * com o banco de dados e a permissão do usuário para realizar esta operacão
     * na entidade. Isto, através da operação
     * <code>excluir</code> da superclasse.
     *
     * @param obj Objeto da classe <code>CartaoCreditoVO</code> que será
     * removido no banco de dados.
     * @exception Execption Caso haja problemas de conexão ou restrição de
     * acesso.
     */
    public void excluir(CartaoCreditoVO obj) throws Exception {
        excluir(getIdEntidade());
        String sql = "DELETE FROM CartaoCredito WHERE ((codigo = ?))";
        try (PreparedStatement sqlExcluir = con.prepareStatement(sql)) {
            sqlExcluir.setInt(1, obj.getCodigo().intValue());
            sqlExcluir.execute();
        }
    }

    /**
     * Responsável por realizar uma consulta de
     * <code>CartaoCredito</code> através do valor do atributo
     * <code>codigo</code> da classe
     * <code>MovPagamento</code> Faz uso da operação
     * <code>montarDadosConsulta</code> que realiza o trabalho de prerarar o
     * List resultante.
     *
     * @return List Contendo vários objetos da classe
     * <code>CartaoCreditoVO</code> resultantes da consulta.
     * @exception Exception Caso haja problemas de conexão ou restrição de
     * acesso.
     */
    public List consultarPorCodigoMovPagamento(Integer valorConsulta, int nivelMontarDados) throws Exception {
        consultar(getIdEntidade(), true);
        String sqlStr = "SELECT CartaoCredito.* FROM CartaoCredito, MovPagamento WHERE CartaoCredito.movpagamento = MovPagamento.codigo and MovPagamento.codigo >= " + valorConsulta.intValue() + " ORDER BY MovPagamento.codigo";
        try (Statement stm = con.createStatement()) {
            try (ResultSet tabelaResultado = stm.executeQuery(sqlStr)) {
                return montarDadosConsulta(tabelaResultado, nivelMontarDados, con);
            }
        }
    }

    /**
     * Responsável por realizar uma consulta de
     * <code>CartaoCredito</code> através do valor do atributo
     * <code>Integer codigo</code>. Retorna os objetos com valores iguais ou
     * superiores ao parâmetro fornecido. Faz uso da operação
     * <code>montarDadosConsulta</code> que realiza o trabalho de prerarar o
     * List resultante.
     *
     * @param controlarAcesso Indica se a aplicação deverá verificar se o
     * usuário possui permissão para esta consulta ou não.
     * @return List Contendo vários objetos da classe
     * <code>CartaoCreditoVO</code> resultantes da consulta.
     * @exception Exception Caso haja problemas de conexão ou restrição de
     * acesso.
     */
    public List<CartaoCreditoVO> consultarPorCodigo(Integer valorConsulta, boolean controlarAcesso, int nivelMontarDados) throws Exception {
        consultar(getIdEntidade(), controlarAcesso);
        String sqlStr = "SELECT * FROM CartaoCredito WHERE codigo >= " + valorConsulta.intValue() + " ORDER BY codigo";
        try (Statement stm = con.createStatement()) {
            try (ResultSet tabelaResultado = stm.executeQuery(sqlStr)) {
                return (montarDadosConsulta(tabelaResultado, nivelMontarDados, con));
            }
        }
    }

    public List<CartaoCreditoVO> consultarPorPeriodoCompensacao(int mp, Date compi, Date compf, 
            Boolean pesquisarPorLote, Boolean pagamentoAvulso, int nivelMontarDados, boolean considerarDataOriginal) throws Exception {
        StringBuilder sql = new StringBuilder();
        sql.append("select *, (select lote from chequecartaolote where cartao = ");
        sql.append("CartaoCredito.codigo order by codigo desc limit 1) as lote \n");
        sql.append("from CartaoCredito where ").append(pagamentoAvulso ? "movconta = " : "movpagamento = ").append(mp);
        
        
        if(considerarDataOriginal){
            sql.append(" and ((dataoriginal >= '").append(Uteis.getDataJDBC(compi)).append(" 00:00:00'");
            sql.append(" and dataoriginal <= '").append(Uteis.getDataJDBC(compf)).append(" 23:59:59' )");
            sql.append(" or (datacompesancao >= '").append(Uteis.getDataJDBC(compi)).append(" 00:00:00'");
            sql.append(" and datacompesancao <= '").append(Uteis.getDataJDBC(compf)).append(" 23:59:59' and dataoriginal is null)) ");
        }else{
            sql.append(" and datacompesancao >= '").append(Uteis.getDataJDBC(compi)).append(" 00:00:00'\n");
            sql.append(" and datacompesancao <= '").append(Uteis.getDataJDBC(compf)).append(" 23:59:59'\n");
        }
        if (pesquisarPorLote != null) {
            if (pesquisarPorLote) {
                sql.append(" and codigo IN (SELECT distinct(cartao) from chequecartaolote where cartao is not null)\n");
            } else {
                sql.append(" and codigo not IN (SELECT distinct(cartao) from chequecartaolote where cartao is not null)\n");
            }
        }
        sql.append(" ORDER BY datacompesancao ");
        try (Statement stm = con.createStatement()) {
            try (ResultSet tabelaResultado = stm.executeQuery(sql.toString())) {
                return montarDadosConsulta(tabelaResultado, nivelMontarDados, con);
            }
        }
    }

    public ResultSet consultarPorPeriodoLC(String nome, Date lanci, Date lancf, Date compi, Date compf, int nivelMontarDados, int empresa) throws Exception {
        StringBuilder sql = new StringBuilder();
        sql.append("SELECT cc.*, mp.datalancamento, mp.nomepagador, mp.autorizacaocartao, oc.descricao FROM CartaoCredito cc ")
                .append("INNER JOIN movpagamento mp ON mp.codigo = cc.movpagamento ")
                .append("INNER JOIN operadoracartao oc ON oc.codigo = cc.operadoracartao ")
                .append("WHERE cc.codigo NOT IN (select distinct(cartao) from chequecartaolote  where cartao is not null) ");
        if (!nome.trim().isEmpty()) {
            sql.append(" AND mp.nomepagador LIKE '%").append(nome).append("%'");
        }
        if(!UteisValidacao.emptyNumber(empresa)){
            sql.append(" AND mp.empresa  = ").append(empresa);
        }
        if (lanci != null) {
            sql.append(" AND mp.datalancamento >= '").append(Uteis.getDataJDBC(lanci)).append(" 00:00:00'").
                    append(" AND mp.datalancamento <= '").append(Uteis.getDataJDBC(lancf)).append(" 23:59:59'");
        }
        if (compi != null) {
            sql.append(" AND cc.datacompesancao >= '").append(Uteis.getDataJDBC(compi)).append(" 00:00:00'").
                    append(" AND cc.datacompesancao <= '").append(Uteis.getDataJDBC(compf)).append(" 23:59:59'");
        }
        sql.append(" ORDER BY mp.nomepagador");
        PreparedStatement sqlConsulta = con.prepareStatement(sql.toString());
        return sqlConsulta.executeQuery();
    }

    public List<CartaoCreditoVO> consultarPorMovPagamento(int valorConsulta, Boolean pesquisarPorLote, boolean pagamentoAvulso, int nivelMontarDados) throws Exception {
        String sqlStr = "select *, (select lote from chequecartaolote where cartao = CartaoCredito.codigo order by codigo desc limit 1) as lote "
                + "from cartaocredito where " + (pagamentoAvulso ? "movconta = " : "movpagamento = ") + valorConsulta;
        if (pesquisarPorLote != null) {
            if (pesquisarPorLote) {
                sqlStr += " and cartaocredito.codigo in (select distinct(cartao) from chequecartaolote where cartao is not null )";
            } else {
                sqlStr += " and cartaocredito.codigo not in (select distinct(cartao) from chequecartaolote where cartao is not null)";
            }
        }
        sqlStr += " ORDER BY datacompesancao ";

        try (Statement stm = con.createStatement()) {
            try (ResultSet tabelaResultado = stm.executeQuery(sqlStr)) {
                return montarDadosConsulta(tabelaResultado, nivelMontarDados, con);
            }
        }
    }

    public double consultarValorCartaoLote(int lote) throws Exception {
        String sql = "SELECT SUM(valor) as total FROM cartaocredito "
                + "inner join chequecartaolote ccl on ccl.cartao = cartaocredito.codigo WHERE ccl.lote = " + lote;
        try (PreparedStatement sqlConsulta = con.prepareStatement(sql)) {
            try (ResultSet resultado = sqlConsulta.executeQuery()) {
                if (resultado.next()) {
                    return resultado.getDouble("total");
                }
            }
        }
        return 0.0;
    }

    public List<CartaoCreditoVO> consultarPorLote(int lote, int nivelMontarDados) throws Exception {
        String sqlStr = "SELECT * FROM cartaocredito cc INNER JOIN chequecartaolote ccl ON cc.codigo = ccl.cartao "
                + "WHERE ccl.lote = " + lote;
        try (Statement stm = con.createStatement()) {
            try (ResultSet tabelaResultado = stm.executeQuery(sqlStr)) {
                return montarDadosConsulta(tabelaResultado, nivelMontarDados, con);
            }
        }
    }

    public ResultSet consultarParaGestaoLote(int lote) throws Exception {
        StringBuilder sql = new StringBuilder();
        sql.append(" SELECT exists(select codigo from chequecartaolote where lote > ccl.lote and cartao = ccl.cartao) as saiu,\n");
        sql.append(" cc.*, mp.datalancamento, mp.nomepagador, oc.descricao, mp.autorizacaocartao, mp.nsu FROM cartaocredito cc \n");
        sql.append(" INNER JOIN movpagamento mp ON mp.codigo = cc.movpagamento \n");
        sql.append(" INNER JOIN chequecartaolote ccl ON cc.codigo = ccl.cartao \n");
        sql.append(" INNER JOIN operadoracartao oc ON oc.codigo = cc.operadoracartao WHERE ccl.lote = ").append(lote);
        sql.append(" UNION ALL \n");
        sql.append(" SELECT exists(select codigo from chequecartaolote where lote > ccl.lote and cartao = ccl.cartao) as saiu,\n");
        sql.append("cc.*, mc.datalancamento, p.nome, oc.descricao, mc.autorizacaocartao, '' as nsu FROM cartaocredito cc\n");
        sql.append(" INNER JOIN movconta mc ON mc.codigo = cc.movconta\n");
        sql.append(" INNER JOIN pessoa p on mc.pessoa = p.codigo\n");
        sql.append(" INNER JOIN chequecartaolote ccl ON cc.codigo = ccl.cartao \n");
        sql.append(" INNER JOIN operadoracartao oc ON oc.codigo = cc.operadoracartao WHERE ccl.lote = ").append(lote);
        Statement stm = con.createStatement();
        return stm.executeQuery(sql.toString());
    }

    /**
     * Responsável por montar os dados de vários objetos, resultantes de uma
     * consulta ao banco de dados (
     * <code>ResultSet</code>). Faz uso da operação
     * <code>montarDados</code> que realiza o trabalho para um objeto por vez.
     *
     * @return List Contendo vários objetos da classe
     * <code>CartaoCreditoVO</code> resultantes da consulta.
     */
    public static List<CartaoCreditoVO> montarDadosConsulta(ResultSet tabelaResultado, int nivelMontarDados, Connection con) throws Exception {
        List<CartaoCreditoVO> vetResultado = new ArrayList();
        while (tabelaResultado.next()) {
            CartaoCreditoVO obj = new CartaoCreditoVO();
            obj = montarDados(tabelaResultado, nivelMontarDados, con);
            vetResultado.add(obj);
        }
        tabelaResultado = null;
        return vetResultado;
    }

    /**
     * Responsável por montar os dados resultantes de uma consulta ao banco de
     * dados (
     * <code>ResultSet</code>) em um objeto da classe
     * <code>CartaoCreditoVO</code>.
     *
     * @return O objeto da classe <code>CartaoCreditoVO</code> com os dados
     * devidamente montados.
     */
    public static CartaoCreditoVO montarDados(ResultSet dadosSQL, int nivelMontarDados, Connection con) throws Exception {
        CartaoCreditoVO obj = new CartaoCreditoVO();
        obj.setCodigo(dadosSQL.getInt("codigo"));
        obj.setDataCompensacao(dadosSQL.getTimestamp("dataCompesancao"));
        obj.setValor(dadosSQL.getDouble("valor"));
        obj.getOperadora().setCodigo(dadosSQL.getInt("operadoraCartao"));
        obj.setSituacao(dadosSQL.getString("situacao"));
        obj.setNrParcela(dadosSQL.getInt("nrparcela"));
        obj.setNovoObj(false);
        obj.setLote(new LoteVO());

        if (nivelMontarDados != Uteis.NIVELMONTARDADOS_ADMINISTRATIVORUNNER_PROCESSARNOTAS) {
            obj.getMovpagamento().setCodigo(dadosSQL.getInt("movpagamento"));
        }

        try {
            obj.setNomeNoCartao(dadosSQL.getString("nomenocartao"));
            obj.setNrDocumento(dadosSQL.getString("nrdocumento"));
            obj.setValorTotal(dadosSQL.getDouble("valortotal"));
            obj.setComposicao(dadosSQL.getString("composicao"));
            obj.setDataOriginal(dadosSQL.getDate("dataoriginal"));
            obj.getMovConta().setCodigo(dadosSQL.getInt("movconta"));
            obj.setProdutosPagos(dadosSQL.getString("produtospagos"));
        } catch (Exception e) {
        }

        try {
            obj.getLote().setCodigo(dadosSQL.getInt("lote"));
        } catch (Exception e) {
            obj.getLote().setCodigo(0);
        }
        if (nivelMontarDados == Uteis.NIVELMONTARDADOS_DADOSBASICOS) {
            return obj;
        }
        if (nivelMontarDados == Uteis.NIVELMONTARDADOS_ESPECIFICO_RECEBIVEIS) {
            try {
                obj.setAlterouDataRecebimentoZWAutomaticamente(dadosSQL.getBoolean("alterouDataRecebimentoZWAutomaticamente"));
            } catch (Exception ignore) {
            }
            try {
                obj.setDataPgtoOriginalZWAntesDaAlteracaoAutomatica(dadosSQL.getDate("dataPgtoOriginalZWAntesDaAlteracaoAutomatica"));
            } catch (Exception ignore) {
            }
            return obj;
        }
        OperadoraCartao operadora = new OperadoraCartao(con);
        try{
            if (!UteisValidacao.emptyNumber(dadosSQL.getInt("operadoraCartao"))) {
                obj.setOperadora(operadora.consultarPorChavePrimaria(dadosSQL.getInt("operadoraCartao"), Uteis.NIVELMONTARDADOS_DADOSBASICOS));
            }
        }catch (Exception e){
            Uteis.logar(e, CartaoCredito.class);
        }

        if (nivelMontarDados == Uteis.NIVELMONTARDADOS_TODOS && !UteisValidacao.emptyNumber(obj.getMovpagamento().getCodigo())) {//pagamentos avulsos não tem movpagamento
            MovPagamento movPag = new MovPagamento(con);
            obj.setMovpagamento(movPag.consultarPorChavePrimaria(obj.getMovpagamento().getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS));
        }

        Integer codigoMovPagamento = dadosSQL.getInt("movpagamento");
        if (!UteisValidacao.emptyNumber(codigoMovPagamento) && nivelMontarDados == Uteis.NIVELMONTARDADOS_ADMINISTRATIVORUNNER_PROCESSARNOTAS) {
            MovPagamento movPag = new MovPagamento(con);
            obj.setMovpagamento(movPag.consultarPorChavePrimaria(codigoMovPagamento, Uteis.NIVELMONTARDADOS_ADMINISTRATIVORUNNER_PROCESSARNOTAS));
        }

        if (nivelMontarDados == Uteis.NIVELMONTARDADOS_MOVPAGAMENTOSINTETICO) {
            Lote lote = new Lote(con);
            obj.setLote(lote.consultarPorCartao(obj.getCodigo(), Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA));
            return obj;
        }
        Lote lote = new Lote(con);
        obj.setLote(lote.consultarPorCartao(obj.getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS));
        if (!UteisValidacao.emptyNumber(obj.getMovpagamento().getCodigo()) && nivelMontarDados != Uteis.NIVELMONTARDADOS_ADMINISTRATIVORUNNER_PROCESSARNOTAS) { //pagamentos avulsos não tem movpagamento
            MovPagamento movPag = new MovPagamento(con);
            obj.setMovpagamento(movPag.consultarPorChavePrimaria(obj.getMovpagamento().getCodigo(), Uteis.NIVELMONTARDADOS_TELACONSULTA));
        }
        return obj;
    }

    /**
     * Operação responsável por excluir todos os objetos da
     * <code>CartaoCreditoVO</code> no BD. Faz uso da operação
     * <code>excluir</code> disponível na classe
     * <code>CartaoCredito</code>.
     *
     * @param <code>movpagamento</code> campo chave para exclusão dos objetos no
     * BD.
     * @exception Exception Erro de conexão com o BD ou restrição de acesso a
     * esta operação.
     */
    public void excluirCartaoCreditos(Integer movpagamento) throws Exception {
        excluir(getIdEntidade());
        String sql = "DELETE FROM CartaoCredito WHERE (movpagamento = ?)";
        try (PreparedStatement sqlExcluir = con.prepareStatement(sql)) {
            sqlExcluir.setInt(1, movpagamento.intValue());
            sqlExcluir.execute();
        }
    }

    /**
     * Operação responsável por alterar todos os objetos da
     * <code>CartaoCreditoVO</code> contidos em um Hashtable no BD. Faz uso da
     * operação
     * <code>excluirCartaoCreditos</code> e
     * <code>incluirCartaoCreditos</code> disponíveis na classe
     * <code>CartaoCredito</code>.
     *
     * @param objetos List com os objetos a serem alterados ou incluídos no BD.
     * @exception Exception Erro de conexão com o BD ou restrição de acesso a
     * esta operação.
     */
    public void alterarCartaoCreditos(Integer movpagamento, List objetos) throws Exception {
        Iterator e = objetos.iterator();
        String codigos = "";
        while (e.hasNext()) {
            CartaoCreditoVO objeto = (CartaoCreditoVO) e.next();
            objeto.getMovpagamento().setCodigo(movpagamento);
            if (objeto.getCodigo().equals(0)) {
                incluir(objeto);
            } else {
                alterar(objeto);
            }
            if (codigos.equals("")) {
                codigos += objeto.getCodigo().toString();
            } else {
                codigos += "," + objeto.getCodigo().toString();
            }
        }
        if (!codigos.equals("")) {
            excluirCartoesAlterarPagamento(movpagamento, codigos);
        }
    }

    private void excluirCartoesAlterarPagamento(Integer movpagamento,
            String codigosCartaoes) throws Exception {
        excluir(getIdEntidade());
        String[] codigos = codigosCartaoes.split(",");
        String sql = "DELETE FROM cartaocredito WHERE (movPagamento = ?) and codigo not in (";
        for (int i = 0; i < codigos.length; i++) {
            if (i == 0) {
                sql += " ? ";
            } else {
                sql += ", ?";
            }
        }
        sql += ")";
        try (PreparedStatement sqlExcluir = con.prepareStatement(sql)) {
            int i = 1;
            sqlExcluir.setInt(i++, movpagamento.intValue());
            for (int j = 0; j < codigos.length; j++) {
                sqlExcluir.setInt(i++, Integer.parseInt(codigos[j]));
            }
            sqlExcluir.execute();
        }

    }

    /**
     * Operação responsável por incluir objetos da
     * <code>CartaoCreditoVO</code> no BD. Garantindo o relacionamento com a
     * entidade principal
     * <code>financeiro.MovPagamento</code> através do atributo de vínculo.
     *
     * @param objetos List contendo os objetos a serem gravados no BD da classe.
     * @exception Exception Erro de conexão com o BD ou restrição de acesso a
     * esta operação.
     */
    public void incluirCartaoCreditos(Integer movpagamentoPrm, List objetos) throws Exception {
        Iterator e = objetos.iterator();
        while (e.hasNext()) {
            Integer codigo = 0;
            CartaoCreditoVO obj = (CartaoCreditoVO) e.next();
            codigo = obj.getCodigo();
            obj.getMovpagamento().setCodigo(movpagamentoPrm);
            if (codigo > 0 && obj.getSituacao().equals("EA")) {
                obj.setComposicao(obterComposicao(codigo));
            }
            if (!obj.getComposicaoNova().equals("") && obj.getSituacao().equals("EA")) {
                if (obj.getComposicao() != null && !obj.getComposicao().equals("")) {
                    obj.setComposicao(tratarDuplicacaoComposicao(obj.getComposicao() + "," + obj.getComposicaoNova()));
                } else {
                    obj.setComposicao(obj.getComposicaoNova());
                }
            }
            incluir(obj);
            if (codigo > 0 && obj.getSituacao().equals("EA")) {
                if (obj.getComposicao() != null && !obj.getComposicao().equals("") && obj.codigoPertenceAComposicao(codigo)) {
                    Lote loteDAO = new Lote(con);
                    List<LoteVO> lotes = loteDAO.consultarPorCartaoLista(codigo, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
                    loteDAO = null;
                    for (LoteVO lote : lotes) {
                        if (lote.getCodigo() > 0) {
                            adicionarCartaoNoLoteCreditoContaCorrente(lote, obj);
                        }
                    }
                } else {
                    alterarCartaoNoLoteCreditoContaCorrente(codigo, obj);
                }

                NFSeEmitida nfSeEmitidaDAO = new NFSeEmitida(con);
                if ((obj.getComposicao() != null) && (!obj.getComposicao().equals("")) && obj.codigoPertenceAComposicao(codigo)){
                    nfSeEmitidaDAO.incluirNFSeParaCartaoCreditoComposicao(obj.getComposicao(), obj.getCodigo());
                }else{
                    nfSeEmitidaDAO.atualizarCartao(codigo, obj.getCodigo());
                }
                nfSeEmitidaDAO = null;
            }
        }
    }

    /**
     * Operação responsável por consultar todos os
     * <code>CartaoCreditoVO</code> relacionados a um objeto da classe
     * <code>financeiro.MovPagamento</code>.
     *
     * @param movpagamento Atributo de <code>financeiro.MovPagamento</code> a
     * ser utilizado para localizar os objetos da classe
     * <code>CartaoCreditoVO</code>.
     * @return List Contendo todos os objetos da classe
     * <code>CartaoCreditoVO</code> resultantes da consulta.
     * @exception Exception Erro de conexão com o BD ou restrição de acesso a
     * esta operação.
     */
    public List consultarCartaoCreditos(Integer movpagamento, int nivelMontarDados) throws Exception {
        consultar(getIdEntidade());
        List objetos = new ArrayList();
        String sql = "SELECT * FROM CartaoCredito WHERE movpagamento = ?";
        try (PreparedStatement sqlConsulta = con.prepareStatement(sql)) {
            sqlConsulta.setInt(1, movpagamento.intValue());
            try (ResultSet resultado = sqlConsulta.executeQuery()) {
                while (resultado.next()) {
                    CartaoCreditoVO novoObj = new CartaoCreditoVO();
                    novoObj = CartaoCredito.montarDados(resultado, nivelMontarDados, con);
                    objetos.add(novoObj);
                }
            }
        }
        return objetos;
    }

    /**
     * Operação responsável por localizar um objeto da classe
     * <code>CartaoCreditoVO</code> através de sua chave primária.
     *
     * @exception Exception Caso haja problemas de conexão ou localização do
     * objeto procurado.
     */
    public CartaoCreditoVO consultarPorChavePrimaria(Integer codigoPrm, int nivelMontarDados) throws Exception {
        consultar(getIdEntidade(), false);
        String sql = "SELECT * FROM CartaoCredito WHERE codigo = ?";
        try (PreparedStatement sqlConsultar = con.prepareStatement(sql)) {
            sqlConsultar.setInt(1, codigoPrm.intValue());
            try (ResultSet tabelaResultado = sqlConsultar.executeQuery()) {
                if (!tabelaResultado.next()) {
                    throw new ConsistirException("Dados Não Encontrados ( CartaoCredito ).");
                }
                return (montarDados(tabelaResultado, nivelMontarDados, con));
            }
        }
    }

    public List<CartaoCreditoVO> consultarPorMovContaLote(int movConta, int nivelMontarDados) throws Exception {
        String sqlStr = "SELECT * FROM cartaocredito c "
                + "INNER JOIN chequecartaolote ccl ON ccl.cartao = c.codigo "
                + "INNER JOIN movconta mc ON ccl.lote = mc.lote "
                + "WHERE mc.codigo = " + movConta;
        try (Statement stm = con.createStatement()) {
            try (ResultSet tabelaResultado = stm.executeQuery(sqlStr)) {
                return montarDadosConsulta(tabelaResultado, nivelMontarDados, con);
            }
        }
    }

    public void salvarListaCartaoCredito(MovContaVO movConta) throws Exception {
        Double valor = 0.0;
        for (MovContaRateioVO rateio : movConta.getMovContaRateios()) {
            if (rateio.getFormaPagamentoVO().getTipoFormaPagamento().equals(TipoFormaPagto.CARTAOCREDITO.getSigla())) {
                valor = valor + rateio.getValor();
            }
        }
        if (valor > 0.0) {
            movConta.setCartoes(new ArrayList<CartaoCreditoVO>());
            Double valorParcela = valor / movConta.getNrParcelaCartaoCredito().doubleValue();
            Date data = movConta.getDataQuitacao();
            for (int i = 1; i <= movConta.getNrParcelaCartaoCredito(); i++) {
                // Regra de negocio quando o pagamento e feito no cartao de credito joga o primeiro pagamento para daqui 30 dias
                data = Uteis.obterDataFuturaParcela(movConta.getDataQuitacao(), i);
                CartaoCreditoVO cartao = new CartaoCreditoVO();
                cartao.setOperadora(movConta.getOperadoraCartaoVO());
                cartao.setDataCompensacao(data);
                cartao.setMovConta(movConta);
                cartao.setValor(valorParcela);
                movConta.getCartoes().add(cartao);
            }

            Iterator<CartaoCreditoVO> e = movConta.getCartoes().iterator();
            LoteVO novoLote = new LoteVO();
            novoLote.setDescricao("CARTÕES - " + movConta.getDescricao());
            novoLote.setDataLancamento(Calendario.hoje());
            novoLote.setCartoes(new ArrayList<CartaoCreditoVO>());
            novoLote.setEmpresa(movConta.getEmpresaVO());
            novoLote.setUsuarioResponsavel(movConta.getUsuarioVO());

            while (e.hasNext()) {
                CartaoCreditoVO obj = e.next();
                incluir(obj);
                novoLote.adicionarCartao(obj);
            }
            Lote lote = new Lote();
            lote.incluir(novoLote);
            MovConta movContaDao = new MovConta();
            movContaDao.adicionarLote(movConta.getCodigo(), novoLote.getCodigo());
        }
    }

    /**
     * <AUTHOR> Alcides 05/12/2012
     */
    public Map<Integer, Double> consultarValorSaidaParaContas(List<CartaoCreditoVO> cartoes, Integer codigoLote) throws Exception {
        Map<Integer, Double> contas = new HashMap<Integer, Double>();
        for (CartaoCreditoVO cartao : cartoes) {
            int codigoConta = getCodigoConta(cartao, codigoLote);
            if (codigoConta > 0) {
                Double valor = contas.get(codigoConta);
                valor = valor == null ? cartao.getValor() : valor + cartao.getValor();
                contas.put(codigoConta, valor);
            }
        }
        return contas;
    }

    public void adicionarCartaoNoLote(LoteVO lote, CartaoCreditoVO cartao) throws Exception {
        String[] codigos = cartao.getObterTodosCartoesComposicao().split(",");
        for (String codigo : codigos) {
            executarConsulta("INSERT INTO chequecartaolote(cartao, lote) VALUES (" + codigo + "," + lote.getCodigo() + ")", con);
        }
    }

    public void adicionarCartaoNoLoteCreditoContaCorrente(LoteVO lote, CartaoCreditoVO cartao) throws Exception {
        executarConsulta("INSERT INTO chequecartaolote(cartao, lote) VALUES (" + cartao.getCodigo() + "," + lote.getCodigo() + ")", con);
    }

    public void alterarCartaoNoLoteCreditoContaCorrente(Integer codigoAntigo, CartaoCreditoVO novo) throws Exception {
        executarConsulta("UPDATE chequecartaolote  SET cartao = " + novo.getCodigo() + " WHERE cartao =" + codigoAntigo, con);
        alterarHistoricoCartaoCreditoContaCorrente(codigoAntigo,novo);
    }
    public void alterarHistoricoCartaoCreditoContaCorrente(Integer codigoAntigo, CartaoCreditoVO novo) throws Exception {
        executarConsulta("UPDATE historicocartao  SET cartao =" + novo.getCodigo()   + " WHERE cartao =" + codigoAntigo, con);
    }
    public void removerCartaoNoLote(LoteVO lote, CartaoCreditoVO cartao) throws Exception {
        executarConsulta("DELETE FROM chequecartaolote where cartao in (" + cartao.getObterTodosCartoesComposicao() + ") AND lote = " + lote.getCodigo(), con);
        executarConsulta("UPDATE cartaocredito SET datacompesancao = dataoriginal where dataoriginal is not null and codigo in (" + cartao.getObterTodosCartoesComposicao() + ")", con);
        executarConsulta("UPDATE historicocartao set datafim='"+Uteis.getDataJDBCTimestamp(Calendario.hoje())+"' where cartao in ("+cartao.getObterTodosCartoesComposicao()+") and datafim=null", con);
    }

    public void removerCartoesPagamentosLote(Integer movPagamento) throws Exception {
        executarConsulta("DELETE FROM chequecartaolote WHERE cartao in (select codigo from cartaocredito where movpagamento =" + movPagamento + ");", con);

    }

    public void alterarMinimo(CartaoCreditoVO obj) throws Exception {
        String sqlComposicao = "";
        String[] codigos = null;
        if (obj.getComposicao() != null && !obj.getComposicao().equals("")) {
            codigos = obj.getComposicao().split(",");
            for (String codigo : codigos) {
                sqlComposicao += ",?";
            }
        }

        boolean dataoriginaljainformada;
        try (ResultSet consulta = criarConsulta("SELECT dataoriginal FROM cartaocredito WHERE codigo = " + obj.getCodigo(), con)) {
            dataoriginaljainformada = false;
            if (consulta.next()) {
                dataoriginaljainformada = consulta.getDate("dataoriginal") != null;
            }
        }
        String sql = dataoriginaljainformada ? "UPDATE cartaocredito SET dataCompesancao=? WHERE codigo in (?" + sqlComposicao + ") " : "UPDATE cartaocredito SET dataCompesancao=?, dataoriginal=? WHERE codigo in (?" + sqlComposicao + ") ";
        try (PreparedStatement sqlAlterar = con.prepareStatement(sql)) {
            int i = 1;

            sqlAlterar.setTimestamp(i++, Uteis.getDataJDBCTimestamp(obj.getDataCompensacao()));
            if (!dataoriginaljainformada) {
                sqlAlterar.setDate(i++, Uteis.getDataJDBC(obj.getDataOriginal()));
            }

            sqlAlterar.setInt(i++, obj.getCodigo());
            if (!sqlComposicao.equals("")) {
                for (String codigo : codigos) {
                    sqlAlterar.setInt(i++, Integer.parseInt(codigo));
                }
            }
            sqlAlterar.execute();
        }


    }

    public void alterarDataCompensacao(String codigos, Date data) throws Exception {

        String sqlComposicao = "";

        String[] codigosCartoes = codigos.split(",");
        for (int j = 1; j < codigosCartoes.length; j++) {
            sqlComposicao += ",?";
        }
        int i = 1;
        String sql = "UPDATE cartaocredito SET dataOriginal= dataCompesancao WHERE codigo in (?" + sqlComposicao + ") and dataoriginal is null";
        try (PreparedStatement sqlAlterar = con.prepareStatement(sql)) {

            sqlAlterar.setInt(i++, Integer.parseInt(codigosCartoes[0]));
            for (int j = 1; j < codigosCartoes.length; j++) {
                sqlAlterar.setInt(i++, Integer.parseInt(codigosCartoes[j]));
            }
            sqlAlterar.execute();

            sql = "UPDATE cartaocredito SET dataCompesancao=? WHERE codigo in (?" + sqlComposicao + ")";
            try (PreparedStatement sqlAlterar2 = con.prepareStatement(sql)) {
                i = 1;
                sqlAlterar2.setTimestamp(i++, Uteis.getDataJDBCTimestamp(data));

                sqlAlterar2.setInt(i++, Integer.parseInt(codigosCartoes[0]));
                for (int j = 1; j < codigosCartoes.length; j++) {
                    sqlAlterar2.setInt(i++, Integer.parseInt(codigosCartoes[j]));
                }
                sqlAlterar2.execute();
            }
        }
    }

    public Boolean verificarContemLote(Integer movPagamento) throws Exception {
        return existe(" select * from chequecartaolote ccl "
                + " inner join cartaocredito c on ccl.cartao = c.codigo "
                + " where c.movpagamento = " + movPagamento, con);
    }
    
    public Boolean verificarContemLoteMovConta(Integer movconta) throws Exception {
        return existe(" select * from chequecartaolote ccl "
                + " inner join cartaocredito c on ccl.cartao = c.codigo "
                + " where c.movconta = " + movconta, con);
    }

    public void getContaLoteCartao(CartaoCreditoTO cc, Integer codigoLote) throws Exception {
        String codigos = "" + cc.getCodigo();
        if (cc.getCodigosComposicao() != null && !cc.getCodigosComposicao().equals("")) {
            codigos += "," + cc.getCodigosComposicao();
        }

        String sql = getSqlContaContido(codigos, null, codigoLote);

        try (ResultSet resultSet = criarConsulta(sql.toString(), con)) {
            if (resultSet.next()) {
                cc.setContaContido(resultSet.getString("contac"));
                cc.setCodigoContaContido(resultSet.getInt("codigoconta"));
            }
        }
    }

    public int getCodigoConta(CartaoCreditoVO cc, Integer codigoLote) throws Exception {
        String codigos = "" + cc.getCodigo();
        if (cc.getComposicao() != null && !cc.getComposicao().equals("")) {
            codigos += "," + cc.getComposicao();
        }

        String sql = getSqlContaContido(codigos, codigoLote, null);

        try (ResultSet resultSet = criarConsulta(sql.toString(), con)) {
            if (resultSet.next()) {
                return resultSet.getInt("codigoconta");
            }
        }
        return 0;
    }

    /**
     * Responsável por
     *
     * <AUTHOR> Alcides 08/05/2013
     */
    private String getSqlContaContido(String codigos, Integer loteNaoConsiderar, Integer codigoLote) {
        StringBuilder sql = new StringBuilder();
        sql.append(" SELECT c.codigo as codigoconta, coalesce(c.descricao, c.descricaocurta) as contac, mc.lote FROM  movconta mc \n");
        sql.append(" INNER JOIN conta c ON c.codigo = mc.conta \n");
        if (UteisValidacao.emptyNumber(codigoLote)) {
            sql.append(" WHERE mc.lote = (select lote from chequecartaolote ccl  inner join lote l on l.codigo = ccl.lote where ccl.cartao in (" + codigos + ") ");
            if (!UteisValidacao.emptyNumber(loteNaoConsiderar)) {
                sql.append(" AND ccl.lote <> " + loteNaoConsiderar);
            }
            sql.append(" order by l.datadeposito desc, l.codigo desc limit 1) \n");
        } else {
            sql.append(" WHERE mc.lote = " + codigoLote + " ");
        }



        sql.append(" ORDER BY mc.codigo limit 1");
        return sql.toString();
    }

    public void incluirCartaoRetiradoLote(List<CartaoCreditoTO> cartoes, int lote, Integer movConta) throws Exception {

        for (CartaoCreditoTO cartao : cartoes) {
            incluirCartaoRetiradoSemCommit(cartao, lote, movConta);
        }
    }

    public void incluirCartaoRetiradoSemCommit(CartaoCreditoTO cartao, int lote, Integer movConta) throws Exception {
        String sql = "INSERT INTO cartaoretiradolote( "
                + "datacompensacao, datalancamento, valor, operadora, codigocartao,"
                + "nome, lote, movconta, autorizacao) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?);";
        int i = 0;
        try (PreparedStatement stm = con.prepareStatement(sql)) {
            stm.setDate(++i, Uteis.getDataJDBC(cartao.getDataCompensacao()));
            stm.setDate(++i, Uteis.getDataJDBC(cartao.getDataLancamento()));
            stm.setDouble(++i, Uteis.arredondarForcando2CasasDecimais(cartao.getValor()));
            stm.setString(++i, cartao.getOperadora());
            stm.setInt(++i, cartao.getCodigo());
            stm.setString(++i, cartao.getNomePagador());
            stm.setInt(++i, lote);
            resolveFKNull(stm, ++i, movConta);
            stm.setString(++i, cartao.getAutorizacao());

            stm.execute();
        }
        cartao.setRemovido(true);
    }

    public List<CartaoCreditoTO> consultarCartoesRetirados(Integer lote, Integer movConta) throws Exception {
        String sql = "SELECT * FROM cartaoretiradolote ";
        if (!UteisValidacao.emptyNumber(lote)) {
            sql += "WHERE lote = " + lote;
        } else if (!UteisValidacao.emptyNumber(movConta)) {
            sql += "WHERE movconta = " + movConta;
        }
        List<CartaoCreditoTO> lista;
        try (ResultSet result = criarConsulta(sql, con)) {
            lista = new ArrayList<CartaoCreditoTO>();
            while (result.next()) {
                lista.add(montarDadosCartoesRetirados(result));
            }
        }
        return lista;
    }

    private CartaoCreditoTO montarDadosCartoesRetirados(ResultSet rs) throws Exception {
        CartaoCreditoTO cartao = new CartaoCreditoTO();
        cartao.setDataCompensacao(rs.getDate("datacompensacao"));
        cartao.setDataLancamento(rs.getDate("datalancamento"));
        cartao.setValor(rs.getDouble("valor"));
        cartao.setOperadora(rs.getString("operadora"));
        cartao.setCodigo(rs.getInt("codigocartao"));
        cartao.setNomePagador(rs.getString("nome"));
        cartao.setAutorizacao(rs.getString("autorizacao"));
        cartao.setRemovido(true);
        return cartao;
    }

    public String obterComposicao(Integer cartao) throws Exception {
        try (ResultSet resultSet = criarConsulta("select composicao from cartaocredito where codigo = " + cartao + ";", con)) {
            if (resultSet.next()) {
                return resultSet.getString("composicao");
            }
        }
        return "";
    }

    private void apagarComposicao(Integer codigo) throws Exception {
        executarConsulta("UPDATE cartaocredito  SET composicao = '' WHERE codigo =" + codigo, con);
    }

    public String obterAvisosVinculos(String composicao) throws Exception {
        StringBuilder sql = new StringBuilder();
        sql.append(" SELECT cc.codigo as cartao, cc.valor, re.codigo AS recibo, re.nomepessoapagador, mcc.codigo, pe.nome FROM cartaocredito cc ");
        sql.append(" inner join movpagamento mp on cc.movpagamento = mp.codigo ");
        sql.append(" left join movimentocontacorrenteclientecomposicao mccc on mp.codigo = mccc.movpagamento ");
        sql.append(" left join movimentocontacorrentecliente mcc on mccc.movimentocontacorrentecliente = mcc.codigo ");
        sql.append(" left join recibopagamento re on re.codigo = mp.recibopagamento ");
        sql.append(" left join pessoa pe on pe.codigo = mcc.pessoa or pe.codigo = re.pessoapagador ");
        sql.append(" where cc.codigo in (").append(composicao).append(") and ((mcc.codigo is null or  ");
        sql.append(" mcc.codigo in (SELECT mov.codigo FROM movimentocontacorrentecliente AS mov  ");
        sql.append(" INNER JOIN pessoa p ON p.codigo = mov.pessoa ");
        sql.append(" WHERE mov.codigo = (SELECT codigo FROM movimentocontacorrentecliente ");
        sql.append(" WHERE movimentocontacorrentecliente.pessoa = mov.pessoa  ");
        sql.append(" ORDER BY movimentocontacorrentecliente.codigo DESC  ");
        sql.append(" LIMIT 1) AND  ");
        sql.append(" mov.saldoatual > 0))  or ( re.codigo is not null)) ");

        StringBuilder retorno;
        try (ResultSet resultSet = criarConsulta(sql.toString(), con)) {
            retorno = new StringBuilder("");
            List<Integer> cartoesAdicionados = new ArrayList<Integer>();
            while (resultSet.next()) {
                if (!cartoesAdicionados.contains(resultSet.getInt("cartao"))) {
                    if (resultSet.getInt("recibo") > 0) {
                        retorno.append("*O recibo ").append(resultSet.getInt("recibo")).append(" do aluno ").append(resultSet.getString("nomepessoapagador"));
                        retorno.append(" será alterado.\n");
                    } else {
                        retorno.append("*Será retirado R$ ").append(Uteis.arredondarForcando2CasasDecimais(resultSet.getDouble("valor"))).append(" da conta do aluno ").append(resultSet.getString("nome")).append(".\n");
                    }
                    cartoesAdicionados.add(resultSet.getInt("cartao"));
                }
            }
        }
        return retorno.toString();
    }

    private String tratarDuplicacaoComposicao(String composicoes) {
        String composicao = "";
        String[] todasComposicoes = composicoes.split(",");
        List<String> composicoesAdicionadas = new ArrayList<String>();
        for (String str : todasComposicoes) {
            if (!composicoesAdicionadas.contains(str)) {
                composicao = (composicao.equals("") ? str : composicao + "," + str);
                composicoesAdicionadas.add(str);
            }
        }
        return composicao;

    }
    
    
    public List<HistoricoCartaoTO> consultarHistoricoCartaoCredito(Integer codigoCartao) throws Exception{
        StringBuilder sql = new StringBuilder();
        sql.append("select mc.lote, mcr.tipoes,mc.codigo as movconta, mc.tipooperacao, mc.dataquitacao, mc.descricao as desc, c.descricao as conta from chequecartaolote ccl \n");
        sql.append("INNER JOIN cartaocredito cc ON cc.codigo = ccl.cartao AND cc.codigo = ").append(codigoCartao).append("\n");
        sql.append("INNER JOIN movconta mc ON mc.lote = ccl.lote\n");
        sql.append("INNER JOIN movcontarateio mcr ON mcr.movconta = mc.codigo\n");
        sql.append("INNER JOIN conta c ON c.codigo = mc.conta\n");
        sql.append("WHERE mcr.tipoes = ").append(TipoES.ENTRADA.getCodigo()).append("\n");
        sql.append("ORDER BY mc.dataquitacao DESC");
        List<HistoricoCartaoTO> lista = new ArrayList<HistoricoCartaoTO>();
        try (ResultSet rs = criarConsulta(sql.toString(), con)) {
            Date ultimaData = null;
            while (rs.next()) {
                lista.add(new HistoricoCartaoTO(rs.getTimestamp("dataquitacao"), ultimaData,
                        rs.getString("desc"), rs.getString("conta"), rs.getInt("movconta"), rs.getInt("lote")));
                ultimaData = rs.getTimestamp("dataquitacao");
            }
        }
        return lista;
    }

    public String obterProdutosPagos(Integer cartao) throws Exception{
        try (ResultSet resultSet = criarConsulta("select produtospagos from cartaocredito where codigo = " + cartao + ";", con)) {
            if (resultSet.next()) {
                return resultSet.getString("produtospagos");
            }
        }
        return "";
    }
}

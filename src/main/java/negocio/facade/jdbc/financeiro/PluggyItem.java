package negocio.facade.jdbc.financeiro;

import negocio.comuns.financeiro.PluggyItemVO;
import negocio.facade.jdbc.arquitetura.SuperEntidade;
import negocio.interfaces.financeiro.PluggyItemInterfaceFacade;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.util.ArrayList;
import java.util.List;

public class PluggyItem extends SuperEntidade implements PluggyItemInterfaceFacade {

    public PluggyItem() throws Exception {
        super();
    }

    public PluggyItem(Connection con) throws Exception {
        super(con);
    }

    public void incluir(PluggyItemVO obj) throws Exception {
        try {
            String sql = "INSERT INTO pluggyItem(empresa, id, dadosRetorno, ativo ) VALUES (?, ?, ?, ?)";
            obj.setCodigo(obterValorChavePrimariaCodigo() + 1);
            try (PreparedStatement sqlInserir = con.prepareStatement(sql)) {
                sqlInserir.setInt(1, obj.getEmpresa());
                sqlInserir.setString(2, obj.getId());
                sqlInserir.setString(3, obj.getDadosRetorno());
                sqlInserir.setBoolean(4, obj.isAtivo());
                sqlInserir.execute();
            }
        } catch (Exception e) {
            throw e;
        }
    }

    public void update(PluggyItemVO obj) throws Exception {
        try {
            String sql = "UPDATE pluggyItem set dadosRetorno = ? where id = ? and empresa = ?";
            try (PreparedStatement sqlInserir = con.prepareStatement(sql)) {
                sqlInserir.setString(1, obj.getDadosRetorno());
                sqlInserir.setString(2, obj.getId());
                sqlInserir.setInt(3, obj.getEmpresa());
                sqlInserir.execute();
            }
        } catch (Exception e) {
            throw e;
        }
    }

    public List<PluggyItemVO> consultar(int empresa) throws Exception {
        List<PluggyItemVO> itens = new ArrayList<PluggyItemVO>();
        String sql = "SELECT * FROM pluggyItem WHERE empresa = " + empresa + " and ativo";
        try (PreparedStatement ps = con.prepareStatement(sql)) {
            try (ResultSet rs = ps.executeQuery()) {
                while (rs.next()) {
                    itens.add(montarDados(rs));
                }
            }
        }

        return itens;
    }

    public PluggyItemVO consultarByIdItem(String id) throws Exception {
        String sql = "SELECT * FROM pluggyItem WHERE id = '" + id + "'";
        try (PreparedStatement ps = con.prepareStatement(sql)) {
            try (ResultSet rs = ps.executeQuery()) {
                while (rs.next()) {
                    return montarDados(rs);
                }
            }
        }
        return null;
    }

    public void inativar(String id) throws Exception {
        String update = "UPDATE pluggyItem set ativo = false where id = ?";
        try (PreparedStatement stm = con.prepareStatement(update)) {
            stm.setString(1, id);
            stm.execute();
        }
    }

    public static PluggyItemVO montarDados(ResultSet dadosSQL) throws Exception {
        PluggyItemVO obj = new PluggyItemVO();
        obj.setCodigo(dadosSQL.getInt("codigo"));
        obj.setEmpresa(dadosSQL.getInt("empresa"));
        obj.setId(dadosSQL.getString("id"));
        obj.setDadosRetorno(dadosSQL.getString("dadosRetorno"));
        obj.setAtivo(dadosSQL.getBoolean("ativo"));

        return obj;
    }
}

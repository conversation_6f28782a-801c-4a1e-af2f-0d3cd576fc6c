package negocio.facade.jdbc.financeiro;

import negocio.comuns.arquitetura.UsuarioVO;
import negocio.comuns.basico.EmpresaVO;
import negocio.comuns.financeiro.*;
import negocio.comuns.financeiro.enumerador.TipoFormaPagto;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisValidacao;
import negocio.facade.jdbc.arquitetura.SuperEntidade;
import negocio.facade.jdbc.arquitetura.SuperFacadeJDBC;
import negocio.facade.jdbc.arquitetura.Usuario;
import negocio.interfaces.financeiro.ConfiguracaoMovimentacaoAutomaticaInterfaceFacade;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.util.ArrayList;
import java.util.List;

public class ConfiguracaoMovimentacaoAutomatica extends SuperEntidade implements ConfiguracaoMovimentacaoAutomaticaInterfaceFacade {


    public ConfiguracaoMovimentacaoAutomatica() throws Exception {
        super();
    }

    public ConfiguracaoMovimentacaoAutomatica(Connection con) throws Exception {
        super(con);
    }

    @Override
    public List<ConfiguracaoMovimentacaoAutomaticaVO> obterConfiguracoes(Integer empresa) throws Exception{
        List<ConfiguracaoMovimentacaoAutomaticaVO> configs = new ArrayList<ConfiguracaoMovimentacaoAutomaticaVO>();

        ResultSet rs= SuperFacadeJDBC.criarConsulta("select * from ConfiguracaoMovimentacaoAutomatica "
                +(UteisValidacao.emptyNumber(empresa) ? "" : (" where empresa = " + empresa)), con);
        while(rs.next()){
            configs.add(montarDados(rs, con, Uteis.NIVELMONTARDADOS_DADOSBASICOS));
        }
        return configs;
    }

    @Override
    public void incluir(ConfiguracaoMovimentacaoAutomaticaVO config) throws Exception {
        String sql = "insert into ConfiguracaoMovimentacaoAutomatica (empresa, usuario, adquirente, formapagamento, conta, tipoformapagto, convenio)" +
                " values (?,?,?,?,?,?,?)";
        PreparedStatement stm = con.prepareStatement(sql);
        int i = 1;
        if(UteisValidacao.emptyNumber(config.getEmpresa().getCodigo())){
            stm.setNull(i++, 0);
        }else{
            stm.setInt(i++, config.getEmpresa().getCodigo());
        }
        stm.setInt(i++, config.getUsuario().getCodigo());
        if(config.getAdquirente() == null || UteisValidacao.emptyNumber(config.getAdquirente().getCodigo())){
            stm.setNull(i++, 0);
        }else{
            stm.setInt(i++, config.getAdquirente().getCodigo());
        }
        if(config.getFormaPagamento() == null || UteisValidacao.emptyNumber(config.getFormaPagamento().getCodigo())){
            stm.setNull(i++, 0);
        }else{
            stm.setInt(i++, config.getFormaPagamento().getCodigo());
        }
        if(config.getConta() == null || UteisValidacao.emptyNumber(config.getConta().getCodigo())){
            stm.setNull(i++, 0);
        }else{
            stm.setInt(i++, config.getConta().getCodigo());
        }
        stm.setString(i++, config.getTipoFormaPagto() == null ? "" : config.getTipoFormaPagto().getSigla());
        if(config.getConvenio() == null || UteisValidacao.emptyNumber(config.getConvenio().getCodigo())){
            stm.setNull(i++, 0);
        }else{
            stm.setInt(i++, config.getConvenio().getCodigo());
        }

        stm.execute();

        config.setCodigo(obterValorChavePrimariaCodigo());

    }

    @Override
    public void remover(ConfiguracaoMovimentacaoAutomaticaVO config) throws Exception {
        String del = "delete from ConfiguracaoMovimentacaoAutomatica where codigo = " + config.getCodigo();
        SuperFacadeJDBC.executarConsulta(del, con);
    }

    @Override
    public void alterar(ConfiguracaoMovimentacaoAutomaticaVO config) throws Exception {
        String sql = "update ConfiguracaoMovimentacaoAutomatica set empresa = ?, " +
                " usuario = ?, adquirente = ?, formapagamento = ?, conta = ?, tipoformapagto = ?, convenio = ? " +
                " where codigo = ? ";
        PreparedStatement stm = con.prepareStatement(sql);
        int i = 1;
        stm.setInt(i++, config.getEmpresa().getCodigo());
        stm.setInt(i++, config.getUsuario().getCodigo());
        if(config.getAdquirente() == null || UteisValidacao.emptyNumber(config.getAdquirente().getCodigo())){
            stm.setNull(i++, 0);
        }else{
            stm.setInt(i++, config.getAdquirente().getCodigo());
        }
        if(config.getFormaPagamento() == null || UteisValidacao.emptyNumber(config.getFormaPagamento().getCodigo())){
            stm.setNull(i++, 0);
        }else{
            stm.setInt(i++, config.getFormaPagamento().getCodigo());
        }
        if(config.getConta() == null || UteisValidacao.emptyNumber(config.getConta().getCodigo())){
            stm.setNull(i++, 0);
        }else{
            stm.setInt(i++, config.getConta().getCodigo());
        }
        stm.setString(i++, config.getTipoFormaPagto().getSigla());
        if(config.getConvenio() == null || UteisValidacao.emptyNumber(config.getConvenio().getCodigo())){
            stm.setNull(i++, 0);
        }else{
            stm.setInt(i++, config.getConvenio().getCodigo());
        }
        stm.setInt(i++, config.getCodigo());

        stm.execute();
    }


    public ConfiguracaoMovimentacaoAutomaticaVO montarDados(ResultSet rs, Connection con, Integer nivelMontarDados) throws Exception{
        ConfiguracaoMovimentacaoAutomaticaVO obj = new ConfiguracaoMovimentacaoAutomaticaVO();
        obj.setCodigo(rs.getInt("codigo"));
        obj.setEmpresa(new EmpresaVO());
        obj.getEmpresa().setCodigo(rs.getInt("empresa"));
        obj.setUsuario(new UsuarioVO());
        obj.getUsuario().setCodigo(rs.getInt("usuario"));
        obj.setAdquirente(new AdquirenteVO());
        obj.getAdquirente().setCodigo(rs.getInt("adquirente"));
        obj.setFormaPagamento(new FormaPagamentoVO());
        obj.getFormaPagamento().setCodigo(rs.getInt("formapagamento"));
        obj.setTipoFormaPagto(TipoFormaPagto.getTipoFormaPagtoSigla(rs.getString("tipoformapagto")));
        obj.setConta(new ContaVO());
        obj.getConta().setCodigo(rs.getInt("conta"));
        obj.setConvenio(new ConvenioCobrancaVO());
        obj.getConvenio().setCodigo(rs.getInt("convenio"));
        if(nivelMontarDados == Uteis.NIVELMONTARDADOS_DADOSBASICOS){
            Usuario usuarioDao = new Usuario(con);
            obj.setUsuario(usuarioDao.consultarPorChavePrimaria(obj.getUsuario().getCodigo(), Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA));
        }

        return obj;
    }


}

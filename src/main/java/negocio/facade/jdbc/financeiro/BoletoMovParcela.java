package negocio.facade.jdbc.financeiro;

import negocio.comuns.financeiro.BoletoMovParcelaVO;
import negocio.comuns.financeiro.BoletoVO;
import negocio.comuns.financeiro.MovParcelaVO;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisValidacao;
import negocio.facade.jdbc.arquitetura.SuperEntidade;
import negocio.interfaces.financeiro.BoletoMovParcelaInterfaceFacade;
import org.json.JSONObject;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.util.ArrayList;
import java.util.List;

/**
 * Created with IntelliJ IDEA.
 * User: Lu<PERSON>
 * Date: 07/12/2021
 */
public class BoletoMovParcela extends SuperEntidade implements BoletoMovParcelaInterfaceFacade {

    public BoletoMovParcela(Connection con) throws Exception {
        super(con);
    }

    public void incluir(BoletoMovParcelaVO obj) throws Exception {
        String sql = "INSERT INTO boletomovparcela(boleto, movparcela, nrTentativaParcela, valorParcela, valorMulta, valorJuros, jsonEstorno) VALUES (?,?,?,?,?,?,?)";
        try (PreparedStatement ps = con.prepareStatement(sql)) {
            int i = 0;
            ps.setInt(++i, obj.getBoletoVO().getCodigo());
            resolveIntegerNull(ps, ++i, obj.getMovParcelaVO().getCodigo());

            if (obj.getMovParcelaVO().getNrTentativas() == null) {
                resolveIntegerNull(ps, ++i, null);
            } else {
                ps.setInt(++i, obj.getMovParcelaVO().getNrTentativas());
            }

            resolveDoubleNull(ps, ++i, obj.getValorParcela());
            resolveDoubleNull(ps, ++i, obj.getValorMulta());
            resolveDoubleNull(ps, ++i, obj.getValorJuros());
            ps.setString(++i, obj.getJsonEstorno());
            ps.execute();
        }
        obj.setCodigo(obterValorChavePrimariaCodigo());
        obj.setNovoObj(false);
    }

    public void alterar(BoletoMovParcelaVO obj) throws Exception {
        String sql = "UPDATE boletomovparcela SET boleto = ?, movparcela = ?, nrTentativaParcela = ?, valorParcela = ?, valorMulta = ?, valorJuros = ?, jsonEstorno = ? WHERE codigo = ?";
        try (PreparedStatement ps = con.prepareStatement(sql)) {
            int i = 0;
            ps.setInt(++i, obj.getBoletoVO().getCodigo());
            resolveIntegerNull(ps, ++i, obj.getMovParcelaVO().getCodigo());
            ps.setInt(++i, obj.getNrTentativaParcela());
            ps.setDouble(++i, obj.getValorParcela());
            ps.setDouble(++i, obj.getValorMulta());
            ps.setDouble(++i, obj.getValorJuros());
            ps.setString(++i, obj.getJsonEstorno());
            ps.setInt(++i, obj.getCodigo());
            ps.execute();
        }
    }

    public void excluir(BoletoMovParcelaVO obj) throws Exception {
        String sql = "DELETE FROM boletomovparcela WHERE codigo = ? ";
        try (PreparedStatement ps = con.prepareStatement(sql)) {
            ps.setInt(1, obj.getCodigo());
            ps.execute();
        }
    }

    private List<BoletoMovParcelaVO> montarDadosConsulta(ResultSet rs, int nivelMontarDados) throws Exception {
        List<BoletoMovParcelaVO> lista = new ArrayList<>();
        while (rs.next()) {
            lista.add(montarDados(rs, nivelMontarDados));
        }
        return lista;
    }

    private BoletoMovParcelaVO montarDados(ResultSet rs, int nivelMontarDados) throws Exception {
        MovParcela movParcelaDAO;
        try {
            movParcelaDAO = new MovParcela(con);

            BoletoMovParcelaVO obj = new BoletoMovParcelaVO();
            obj.setCodigo(rs.getInt("codigo"));
            obj.getBoletoVO().setCodigo(rs.getInt("boleto"));
            obj.getMovParcelaVO().setCodigo(rs.getInt("movparcela"));
            obj.setNrTentativaParcela(rs.getInt("nrTentativaParcela"));
            obj.setValorParcela(rs.getDouble("valorParcela"));
            obj.setValorMulta(rs.getDouble("valorMulta"));
            obj.setValorJuros(rs.getDouble("valorJuros"));
            obj.setJsonEstorno(rs.getString("jsonEstorno"));

            if (nivelMontarDados == Uteis.NIVELMONTARDADOS_DADOSBASICOS) {
                return obj;
            }

            if (nivelMontarDados == Uteis.NIVELMONTARDADOS_DADOSENTIDADESPRINCIPAIS) {
                if (!UteisValidacao.emptyNumber(obj.getMovParcelaVO().getCodigo())) {
                    obj.setMovParcelaVO(movParcelaDAO.consultarPorChavePrimaria(obj.getMovParcelaVO().getCodigo(), Uteis.NIVELMONTARDADOS_MINIMOS));
                }
            }
            return obj;
        } finally {
            movParcelaDAO = null;
        }
    }

    public BoletoMovParcelaVO consultarPorChavePrimaria(final int codigo, int nivelMontarDados) throws Exception {
        String sql = "SELECT * FROM boletomovparcela WHERE codigo = ?";
        try (PreparedStatement ps = con.prepareStatement(sql)) {
            ps.setInt(1, codigo);
            try (ResultSet rs = ps.executeQuery()) {
                if (rs.next()) {
                    return montarDados(rs, nivelMontarDados);
                }
            }
        }
        return null;
    }

    public List<BoletoMovParcelaVO> consultarPorCodigoBoleto(final int codigoBoleto, int nivelMontarDados) throws Exception {
        String sql = "SELECT * FROM boletomovparcela WHERE boleto = ?";
        try (PreparedStatement ps = con.prepareStatement(sql)) {
            ps.setInt(1, codigoBoleto);
            try (ResultSet rs = ps.executeQuery()) {
                return montarDadosConsulta(rs, nivelMontarDados);
            }
        }
    }

    public List<BoletoMovParcelaVO> consultarPorCodigoContrato(final int codigoContrato, int nivelMontarDados) throws Exception {
        String sql = "SELECT * FROM boletomovparcela WHERE movparcela in (select codigo from movparcela where contrato = ?)";
        try (PreparedStatement ps = con.prepareStatement(sql)) {
            ps.setInt(1, codigoContrato);
            try (ResultSet rs = ps.executeQuery()) {
                return montarDadosConsulta(rs, nivelMontarDados);
            }
        }
    }

    public List<BoletoMovParcelaVO> consultarPorCodigoParcela(final int codigoMovParcela, int nivelMontarDados) throws Exception {
        String sql = "SELECT * FROM boletomovparcela WHERE movparcela = ?";
        try (PreparedStatement ps = con.prepareStatement(sql)) {
            ps.setInt(1, codigoMovParcela);
            try (ResultSet rs = ps.executeQuery()) {
                return montarDadosConsulta(rs, nivelMontarDados);
            }
        }
    }

    public void excluirPorBoleto(final int codBoleto) throws Exception {
        String sql = "delete from boletomovparcela where boleto = ?";
        try (PreparedStatement ps = con.prepareStatement(sql)) {
            ps.setInt(1, codBoleto);
            ps.execute();
        }
    }

    public void excluirPorMovParcela(final int codMovParcela) throws Exception {
        String sql = "delete from boletomovparcela where movparcela = ?";
        try (PreparedStatement ps = con.prepareStatement(sql)) {
            ps.setInt(1, codMovParcela);
            ps.execute();
        }
    }

    public List<MovParcelaVO> consultarMovParcelasDoBoleto(BoletoVO boletoVO, int nivelMontarDados) throws Exception {
        List<MovParcelaVO> listaParcelas = new ArrayList<>();
        StringBuilder sql = new StringBuilder();
        sql.append("select  \n");
        sql.append("mp.* \n");
        sql.append("from boletomovparcela bm \n");
        sql.append("inner join movparcela mp on mp.codigo = bm.movparcela \n");
        sql.append("where bm.boleto = ? \n");
        try (PreparedStatement ps = con.prepareStatement(sql.toString())) {
            ps.setInt(1, boletoVO.getCodigo());
            try (ResultSet rs = ps.executeQuery()) {
                if (rs.next()) {
                    listaParcelas.add(MovParcela.montarDados(rs, nivelMontarDados, this.con));
                }
            }
        }
        return listaParcelas;
    }

    public List<BoletoMovParcelaVO> consultarPorBoletoSituacaoParcela(BoletoVO boletoVO, String situacaoParcela, int nivelMontarDados) throws Exception {
        StringBuilder sql = new StringBuilder();
        sql.append("select \n");
        sql.append("bm.* \n");
        sql.append("from boletomovparcela bm \n");
        sql.append("inner join movparcela mp on mp.codigo = bm.movparcela \n");
        sql.append("where bm.boleto = ").append(boletoVO.getCodigo()).append(" \n");
        if (!UteisValidacao.emptyString(situacaoParcela)) {
            sql.append("and mp.codigo is not null \n");
            sql.append("and upper(mp.situacao) = '").append(situacaoParcela.toUpperCase()).append("' \n");
        }
        try (PreparedStatement ps = con.prepareStatement(sql.toString())) {
            try (ResultSet rs = ps.executeQuery()) {
                return montarDadosConsulta(rs, nivelMontarDados);
            }
        }
    }

    public void alterarMovParcela(BoletoMovParcelaVO obj, MovParcelaVO movParcelaNova, String operacao) throws Exception {
        Boleto boletoDAO;
        try {
            boletoDAO = new Boleto(this.con);
            JSONObject jsonLog = boletoDAO.gerarBaseJSON(null, operacao);
            jsonLog.put("boletomovparcela", obj.getCodigo());
            jsonLog.put("movparcela_anterior", obj.getMovParcelaVO().getCodigo());
            jsonLog.put("movparcela_nova", movParcelaNova.getCodigo());

            String sql = "UPDATE boletomovparcela SET valorParcela = ?, valorMulta = ?, valorJuros = ?, movparcela = ? WHERE codigo = ?";
            try (PreparedStatement ps = this.getCon().prepareStatement(sql)) {
                int i = 0;
                resolveDoubleNull(ps, ++i, obj.getValorParcela());
                resolveDoubleNull(ps, ++i, obj.getValorMulta());
                resolveDoubleNull(ps, ++i, obj.getValorJuros());
                ps.setInt(++i, movParcelaNova.getCodigo());
                ps.setInt(++i, obj.getCodigo());
                ps.execute();
            }
            obj.setMovParcelaVO(movParcelaNova);
            boletoDAO.incluirBoletoHistorico(obj.getBoletoVO(), "Alterar MovParcela BoletoMovParcela", jsonLog.toString());
        } finally {
            boletoDAO = null;
        }
    }

    public BoletoMovParcelaVO incluirBoletoMovParcelaVO(BoletoVO boletoVO, MovParcelaVO movParcelaVO) throws Exception {
        BoletoMovParcelaVO novo = new BoletoMovParcelaVO();
        novo.setBoletoVO(boletoVO);
        novo.setMovParcelaVO(movParcelaVO);
        novo.setNrTentativaParcela(movParcelaVO.getNrTentativas());
        novo.setValorParcela(movParcelaVO.getValorParcela());
        novo.setValorJuros(movParcelaVO.getValorJuros());
        novo.setValorMulta(movParcelaVO.getValorMulta());
        incluir(novo);
        return novo;
    }

    public void incluirBoletoMovParcela(BoletoVO boletoVO) throws Exception {
        List<MovParcelaVO> lista = boletoVO.getListaParcelas();
        for (MovParcelaVO movParcelaVO : lista) {
            incluirBoletoMovParcelaVO(boletoVO, movParcelaVO);
        }
    }
}

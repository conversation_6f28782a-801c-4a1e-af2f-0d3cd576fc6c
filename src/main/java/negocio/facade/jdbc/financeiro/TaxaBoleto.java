package negocio.facade.jdbc.financeiro;

import negocio.comuns.financeiro.TaxaBoletoVO;
import negocio.comuns.utilitarias.Uteis;
import negocio.facade.jdbc.arquitetura.SuperEntidade;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;

public class TaxaBoleto extends SuperEntidade {
    public TaxaBoleto() throws Exception {
    }

    public TaxaBoleto(Connection conexao) throws Exception {
        super(conexao);
    }

    public static TaxaBoletoVO montarDados(ResultSet dadosSQL, Connection con) throws Exception {
        TaxaBoletoVO obj = new TaxaBoletoVO();
        obj.setCodigo(dadosSQL.getInt("codigo"));
        obj.setTipo(dadosSQL.getInt("tipo"));
        obj.setTaxa(dadosSQL.getDouble("taxa"));
        obj.getFormaPagamentoVO().setCodigo(dadosSQL.getInt("formapagamento"));
        obj.getEmpresa().setCodigo(dadosSQL.getInt("empresa"));
        try {
            obj.setVigenciaInicial(dadosSQL.getDate("vigenciaInicial"));
            obj.setVigenciaFinal(dadosSQL.getDate("vigenciaFinal"));
        } catch (Exception ignored) {
        }
        return obj;
    }

    public static TaxaBoletoVO montarDadosConsulta(ResultSet tabelaResultado, Connection con) throws Exception {
        TaxaBoletoVO obj = null;
        while (tabelaResultado.next()) {
            obj = montarDados(tabelaResultado, con);
        }
        return obj;
    }

    public void incluir(TaxaBoletoVO obj) throws Exception {
        String sql = "INSERT INTO taxaboleto (taxa, tipo, vigenciaInicial, vigenciaFinal, formapagamento, empresa) " +
                "VALUES (?,?,?,?,?,?)";
        int i = 0;
        try (PreparedStatement sqlInserir = con.prepareStatement(sql)) {
            sqlInserir.setDouble(++i, obj.getTaxa());
            sqlInserir.setInt(++i, obj.getTipo());
            sqlInserir.setDate(++i, Uteis.getDataJDBC(obj.getVigenciaInicial()));
            sqlInserir.setDate(++i, Uteis.getDataJDBC(obj.getVigenciaFinal()));
            sqlInserir.setInt(++i, obj.getFormaPagamentoVO().getCodigo());
            sqlInserir.setInt(++i, obj.getEmpresa().getCodigo());

            sqlInserir.execute();
        }
        obj.setCodigo(obterValorChavePrimariaCodigo());
        obj.setNovoObj(false);
    }

    public void alterar(TaxaBoletoVO obj) throws Exception {
        String sql = "UPDATE taxaboleto set taxa = ?, tipo = ?, vigenciaInicial = ?, vigenciaFinal = ?, formapagamento = ?, empresa = ?" +
                " WHERE codigo = ?";
        int i = 0;
        try (PreparedStatement sqlAlterar = con.prepareStatement(sql)) {
            sqlAlterar.setDouble(++i, obj.getTaxa());
            sqlAlterar.setInt(++i, obj.getTipo());
            sqlAlterar.setDate(++i, Uteis.getDataJDBC(obj.getVigenciaInicial()));
            sqlAlterar.setDate(++i, Uteis.getDataJDBC(obj.getVigenciaFinal()));
            sqlAlterar.setInt(++i, obj.getFormaPagamentoVO().getCodigo());
            sqlAlterar.setInt(++i, obj.getEmpresa().getCodigo());
            sqlAlterar.setInt(++i, obj.getCodigo());
            sqlAlterar.execute();
        }
        obj.setNovoObj(false);
    }

    public void excluir(TaxaBoletoVO obj) throws Exception {
        String sql = "DELETE FROM taxaboleto WHERE codigo = ?";
        try (PreparedStatement sqlExcluir = con.prepareStatement(sql)) {
            sqlExcluir.setInt(1, obj.getCodigo());
            sqlExcluir.execute();
        }
    }

    public void excluirPorFormaPagamento(Integer codFormaPagamento) throws Exception {
        String sql = "DELETE FROM taxaboleto WHERE formapagamento = ?";
        try (PreparedStatement sqlExcluir = con.prepareStatement(sql)) {
            sqlExcluir.setInt(1, codFormaPagamento);
            sqlExcluir.execute();
        }
    }

    public TaxaBoletoVO consultarPorFormaPagamento(int formaPagamento, Integer codEmpresa) throws Exception {
        String sql = "SELECT * FROM taxaboleto ";
        sql += " WHERE formapagamento = ? AND empresa = ?";

        try (PreparedStatement sqlConsultar = con.prepareStatement(sql)) {
            sqlConsultar.setInt(1, formaPagamento);
            sqlConsultar.setInt(2, codEmpresa);

            try (ResultSet tabelaResultado = sqlConsultar.executeQuery()) {
                return montarDadosConsulta(tabelaResultado, this.con);
            }
        }
    }
}

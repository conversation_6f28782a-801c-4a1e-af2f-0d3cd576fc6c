/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package negocio.facade.jdbc.financeiro;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.List;
import java.util.Map;
import negocio.comuns.financeiro.DFSinteticoDetalheVO;
import negocio.comuns.utilitarias.Uteis;
import negocio.facade.jdbc.arquitetura.SuperEntidade;
import static negocio.facade.jdbc.arquitetura.SuperFacadeJDBC.criarConsulta;
import negocio.interfaces.basico.DFSinteticoDetalheInterfaceFacade;

/**
 *
 * <AUTHOR>
 */
public class DFSinteticoDetalhe extends SuperEntidade implements DFSinteticoDetalheInterfaceFacade {

    public DFSinteticoDetalhe() throws Exception {
        super();
    }

    public DFSinteticoDetalhe(Connection conexao) throws Exception {
        super(conexao);
    }

    @Override
    public void incluir(DFSinteticoDetalheVO detalhe) throws Exception {
        PreparedStatement stm = con.prepareStatement("INSERT INTO DFSinteticoDetalhe (empresa, dados, dataMes) VALUES (?,?,?)");
        stm.setInt(1, detalhe.getEmpresa());
        stm.setString(2, detalhe.getDados());
        stm.setDate(3, Uteis.getDataJDBC(detalhe.getDataMes()));
        stm.execute();
    }

    /**
     *
     * @param mapaDetalhes
     * @param empresa
     * @throws Exception
     */
    @Override
    public void incluir(List<DFSinteticoDetalheVO> detalhes) throws Exception {
        for (DFSinteticoDetalheVO d : detalhes) {
            ResultSet rs = criarConsulta("SELECT * FROM DFSinteticoDetalhe WHERE datames = '"
                    + Uteis.getDataJDBC(d.getDataMes()) + "' AND empresa = " + d.getEmpresa(), con);
            if (rs.next()) {
                executarConsulta("DELETE FROM DFSinteticoDetalhe WHERE datames = '"
                        + Uteis.getDataJDBC(d.getDataMes()) + "' AND empresa = " + d.getEmpresa(), con);
            }
            incluir(d);
        }

    }

    @Override
    public List<DFSinteticoDetalheVO> consultar(String mesAno, Integer nrMesesAnteriores, Integer empresa) throws Exception {
        PreparedStatement stm = con.prepareStatement("SELECT * FROM DFSinteticoDetalhe "
                + " WHERE dataMes BETWEEN ? AND ? and empresa = ?");
        Date dataBase = Uteis.obterUltimoDiaMesUltimaHora(Uteis.getDate("01/"+mesAno));
        Date dataLimite = Uteis.somarCampoData(Uteis.getDate("01/"+mesAno), Calendar.MONTH, -nrMesesAnteriores);
        stm.setDate(1, Uteis.getDataJDBC(dataLimite));
        stm.setDate(2, Uteis.getDataJDBC(dataBase));
        stm.setInt(3, empresa);
        ResultSet rs = stm.executeQuery();
        List<DFSinteticoDetalheVO> lista = new ArrayList<DFSinteticoDetalheVO>();
        while(rs.next()){
            lista.add(new DFSinteticoDetalheVO(rs.getInt("empresa"), rs.getDate("dataMes"), rs.getString("dados")));
        }
        return lista;
    }
}

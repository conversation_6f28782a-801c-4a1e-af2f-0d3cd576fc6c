/*
 * To change this template, choose Too<PERSON> | Templates
 * and open the template in the editor.
 */
package negocio.facade.jdbc.financeiro;

import br.com.pactosolucoes.comuns.util.JSFUtilities;
import br.com.pactosolucoes.enumeradores.ProcessoAjusteGeralEnum;
import negocio.comuns.arquitetura.LogVO;
import negocio.comuns.arquitetura.PaginadorDTO;
import negocio.comuns.arquitetura.UsuarioVO;
import negocio.comuns.financeiro.*;
import negocio.comuns.financeiro.enumerador.SituacaoRemessaEnum;
import negocio.comuns.financeiro.enumerador.TipoRemessaEnum;
import negocio.comuns.utilitarias.*;
import negocio.facade.jdbc.arquitetura.Log;
import negocio.facade.jdbc.arquitetura.SuperEntidade;
import negocio.facade.jdbc.arquitetura.SuperFacadeJDBC;
import negocio.facade.jdbc.arquitetura.Usuario;
import negocio.facade.jdbc.basico.Empresa;
import negocio.facade.jdbc.basico.LogAjusteGeral;
import negocio.interfaces.financeiro.RemessaInterfaceFacade;
import negocio.interfaces.financeiro.TransacaoInterfaceFacade;
import org.json.JSONObject;
import servicos.impl.dcc.base.DCCAttEnum;
import servicos.impl.dcc.itau.DCOItauStatusEnum;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.*;

/**
 *
 * <AUTHOR>
 */
public class Remessa extends SuperEntidade implements RemessaInterfaceFacade {

    public Remessa() throws Exception {
    }

    public Remessa(Connection con) throws Exception {
        super(con);
    }

    @Override
    public void incluir(RemessaVO obj) throws Exception {
        RemessaVO.validarDados(obj);
        try {
            con.setAutoCommit(false);
            String sql = "INSERT INTO Remessa(tipo, dataRegistro, dataRegistroOriginal, dthrInicio, "
                    + "dthrFim, head, detail, trailer, retorno, empresa, "
                    + "identificador, conveniocobranca, situacaoremessa, usuario, "
                    + "props, headarquivo, trailerarquivo, nomearquivo, cancelamento, arquivounico, novoFormato, idpactopay) "
                    + "VALUES (?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)";

            try (PreparedStatement ps = con.prepareStatement(sql)) {
                if (obj.getIdentificador().isEmpty()) {
                    obj.gerarIdentificador(con);
                }

                int i = 0;
                ps.setInt(++i, obj.getTipo().getId());
                //dataRegistro
                ps.setTimestamp(++i, Uteis.getDataJDBCTimestamp(obj.getDataRegistro()));

                /**
                 * dataRegistroOriginal
                 * Utiliza a data de registro original para incluir com a mesma data!
                 * by Luiz Felipe 15/04/2020
                 */
                ps.setTimestamp(++i, Uteis.getDataJDBCTimestamp(obj.getDataRegistro()));

                ps.setTimestamp(++i, Uteis.getDataJDBCTimestamp(obj.getDataInicio()));
                ps.setTimestamp(++i, Uteis.getDataJDBCTimestamp(obj.getDataFim()));
                ps.setString(++i, obj.getHead().toString());
                ps.setString(++i, obj.getDetail().toString());
                ps.setString(++i, obj.getTrailer().toString());
                ps.setString(++i, obj.getRetorno().toString());
                RemessaItem.resolveFKNull(ps, ++i, obj.getEmpresa());
                ps.setString(++i, obj.getIdentificador());
                RemessaItem.resolveFKNull(ps, ++i, obj.getConvenioCobranca().getCodigo());
                ps.setInt(++i, obj.getSituacaoRemessa().getId());
                RemessaItem.resolveFKNull(ps, ++i, obj.getUsuario().getCodigo());
                ps.setString(++i, obj.getProps().toString());
                ps.setString(++i, obj.getHeaderArquivo().toString());
                ps.setString(++i, obj.getTrailerArquivo().toString());
                ps.setString(++i, obj.getNomeArquivo());
                ps.setBoolean(++i, obj.isCancelamento());
                ps.setBoolean(++i, obj.isArquivoUnico());
                ps.setBoolean(++i, obj.isNovoFormato());
                ps.setString(++i, obj.getIdPactoPay());
                ps.execute();
            }
            obj.setCodigo(obterValorChavePrimariaCodigo());

            if (JSFUtilities.isJSFContext()) {
                List lista = obj.gerarLogAlteracaoObjetoVO();
                Iterator it = lista.iterator();
                while (it.hasNext()) {
                    LogVO log = (LogVO) it.next();
                    log.setOperacao("INCLUSÃO");
                    log.setChavePrimaria(obj.getCodigo().toString());
                    log.setNomeEntidade("REMESSA");
                    log.setValorCampoAlterado(log.getValorCampoAlterado() + " ID: " + obj.getCodigo());
                    log.setPessoa(0);
                    Log logDAO = new Log(con);
                    logDAO.incluirSemCommit(log);
                    logDAO = null;
                }
            }

            obj.setNovoObj(false);
            incluirRemessaTransacoes(obj);
            incluirRemessaItens(obj);
            incluirRemessaCancelamentoItens(obj);
            con.commit();
        } catch (Exception e) {
            con.rollback();
            throw e;
        } finally {
            con.setAutoCommit(true);
        }

    }

    @Override
    public void alterar(RemessaVO obj) throws Exception {
        RemessaVO.validarDados(obj);
        String sql = "UPDATE Remessa "
                + "set tipo=?, dataregistro=?, dthrinicio=?, "
                + "dthrfim=?,head=?,trailer=?,retorno=?,empresa=?,identificador=?,"
                + "conveniocobranca=?,situacaoremessa=?,usuario=?,props=?, headarquivo=?, trailerarquivo=?, nomearquivo=?, "
                + "cancelamento = ?, arquivounico = ?, dataEnvio = ?, idpactopay = ? "
                + "WHERE codigo = ?";
        //metodo alterar pode não sobrepor a coluna DETAIL, pois ela não é montada na consulta
        int i = 1;
        try (PreparedStatement ps = con.prepareStatement(sql)) {
            ps.setInt(i++, obj.getTipo().getId());
            ps.setTimestamp(i++, Uteis.getDataJDBCTimestamp(obj.getDataRegistro()));
            ps.setTimestamp(i++, Uteis.getDataJDBCTimestamp(obj.getDataInicio()));
            ps.setTimestamp(i++, Uteis.getDataJDBCTimestamp(obj.getDataFim()));
            ps.setString(i++, obj.getHead().toString());
            ps.setString(i++, obj.getTrailer().toString());
            ps.setString(i++, obj.getRetorno().toString());
            resolveFKNull(ps, i++, obj.getEmpresa());
            ps.setString(i++, obj.getIdentificador());
            resolveFKNull(ps, i++, obj.getConvenioCobranca().getCodigo());
            ps.setInt(i++, obj.getSituacaoRemessa().getId());
            resolveFKNull(ps, i++, obj.getUsuario().getCodigo());
            ps.setString(i++, obj.getProps().toString());

            ps.setString(i++, obj.getHeaderArquivo().toString());
            ps.setString(i++, obj.getTrailerArquivo().toString());
            ps.setString(i++, obj.getNomeArquivo());
            ps.setBoolean(i++, obj.isCancelamento());
            ps.setBoolean(i++, obj.isArquivoUnico());
            ps.setTimestamp(i++, Uteis.getDataJDBCTimestamp(obj.getDataEnvio()));
            ps.setString(i++, obj.getIdPactoPay());

            ps.setInt(i++, obj.getCodigo());
            ps.execute();
        }
    }

    /**
     * Operação responsável por excluir no BD um objeto da classe <code>ContratoComposicaoVO</code>.
     * Sempre localiza o registro a ser excluído através da chave primária da entidade.
     * Primeiramente verifica a conexão com o banco de dados e a permissão do usuário
     * para realizar esta operacão na entidade.
     * Isto, através da operação <code>excluir</code> da superclasse.
     * @param obj    Objeto da classe <code>ContratoComposicaoVO</code> que será removido no banco de dados.
     * @exception Exception Caso haja problemas de conexão ou restrição de acesso.
     */
    @Override
    public void excluir(RemessaVO obj) throws Exception {

        RemessaCancelamentoItem remessaCancelamentoItemDAO = new RemessaCancelamentoItem(con);
        remessaCancelamentoItemDAO.excluirPorCodigoRemessa(obj.getCodigo());
        remessaCancelamentoItemDAO = null;

        RemessaItemMovParcela remessaItemMovParcelaDAO = new RemessaItemMovParcela(con);
        remessaItemMovParcelaDAO.excluirPorCodigoRemessa(obj.getCodigo());
        remessaItemMovParcelaDAO = null;

        RemessaItem remessaItemDAO = new RemessaItem(con);
        remessaItemDAO.excluirPorCodigoRemessa(obj.getCodigo());
        remessaItemDAO = null;


        String sql = "DELETE FROM Remessa WHERE codigo = ?";
        try (PreparedStatement sqlExcluir = con.prepareStatement(sql)) {
            sqlExcluir.setInt(1, obj.getCodigo());
            sqlExcluir.execute();
        }
    }

    public void excluirComLog(RemessaVO obj, UsuarioVO usuarioVO) throws Exception {
        try {
            con.setAutoCommit(false);

            excluir(obj);

            JSONObject json = new JSONObject();
            json.put("codigo", obj.getCodigo());
            json.put("situacao", obj.getSituacaoRemessa().getId());
            json.put("cancelamento", obj.isCancelamento());
            json.put("dataRemessa", obj.getDataRegistroApresentar());
            json.put("convenio", obj.getConvenioCobranca().getCodigo());
            json.put("empresa", obj.getEmpresa());
            LogAjusteGeral logDAO = new LogAjusteGeral(con);
            logDAO.incluir(Calendario.hoje(), usuarioVO.getNome(), usuarioVO.getUserOamd(), ProcessoAjusteGeralEnum.EXCLUIR_REMESSA, json.toString());
            logDAO = null;


            con.commit();
        } catch (Exception e) {
            con.rollback();
            throw e;
        } finally {
            con.setAutoCommit(true);
        }
    }

    public static List<RemessaVO> montarDadosConsulta(ResultSet tabelaResultado, Connection c) throws Exception {
        List<RemessaVO> vetResultado = new ArrayList<RemessaVO>();
        while (tabelaResultado.next()) {
            RemessaVO obj = montarDados(tabelaResultado, c);
            vetResultado.add(obj);
        }
        return vetResultado;
    }

    private static RemessaVO montarDados(ResultSet ds, Connection c) throws Exception {
        RemessaVO obj = new RemessaVO();
        obj.setCodigo(ds.getInt("codigo"));
        obj.setTipo(TipoRemessaEnum.getTipoRemessaEnum(ds.getInt("tipo")));
        obj.setDataRegistro(ds.getTimestamp("dataregistro"));
        obj.setDataInicio(ds.getTimestamp("dthrinicio"));
        obj.setDataFim(ds.getTimestamp("dthrfim"));
        obj.setHead(new StringBuilder(UteisValidacao.emptyString(ds.getString("head")) ? "" : ds.getString("head")));
        //não montar o 'detail', pois este só deve ser incluído e não será alterado mais
        //obj.setDetail(new StringBuffer(ds.getString("detail")));
        obj.setTrailer(new StringBuilder(UteisValidacao.emptyString(ds.getString("trailer")) ? "" : ds.getString("trailer")));
        obj.setRetorno(new StringBuilder(ds.getString("retorno") == null ? "" : ds.getString("retorno")));
        obj.setEmpresa(ds.getInt("empresa"));
        obj.setIdentificador(ds.getString("identificador"));
        obj.setSituacaoRemessa(SituacaoRemessaEnum.valueOf(ds.getInt("situacaoremessa")));
        obj.setProps(Uteis.obterMapFromString(ds.getString("props")));
        try {
            obj.setHeadMap(Uteis.obterMapFromString(ds.getString("head")));
        } catch (Exception ex){
            ex.printStackTrace();
        }
        obj.setValorBruto(ds.getDouble("valorbruto"));
        try {
            obj.setNomeArquivo(ds.getString("nomearquivo"));
            obj.setTrailerArquivo(new StringBuilder(ds.getString("trailerarquivo")));
            obj.setHeaderArquivo(new StringBuilder(ds.getString("headarquivo")));
            obj.setCancelamento(ds.getBoolean("cancelamento"));
            obj.setArquivoUnico(ds.getBoolean("arquivounico"));
            obj.setDataFechamento(ds.getTimestamp("datafechamento"));
            obj.getUsuarioFechamento().setCodigo(ds.getInt("usuariofechamento"));
            obj.setDataEnvio(ds.getTimestamp("dataEnvio"));
            obj.setDataRegistroOriginal(ds.getTimestamp("dataRegistroOriginal"));
            obj.setNovoFormato(ds.getBoolean("novoFormato"));
            obj.setIdPactoPay(ds.getString("idpactopay"));
        } catch (Exception e) {
        }
        montarDadosConvenioCobranca(ds.getInt("conveniocobranca"), ds.getInt("empresa"), obj, c);
        montarDadosEmpresa(ds.getInt("empresa"), obj, c);
        montarDadosUsuario(ds.getInt("usuario"), obj, c);

        obj.processarNomeArquivoDownload();

        return obj;
    }

    private static void montarDadosConvenioCobranca(int codigo, Integer empresa, RemessaVO obj, Connection c) throws Exception {
        if (codigo != 0) {
            ConvenioCobranca convenioFacade = new ConvenioCobranca(c);
            obj.setConvenioCobranca(convenioFacade.consultarPorCodigoEmpresa(codigo, empresa, Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA));
            convenioFacade = null;
        }
    }

    private static void montarDadosEmpresa(int codigo, RemessaVO obj, Connection c) throws Exception {
        if (codigo != 0) {
            Empresa empresaFacade = new Empresa(c);
            obj.setEmpresaVO(empresaFacade.consultarPorChavePrimaria(codigo, Uteis.NIVELMONTARDADOS_MINIMOS));
            empresaFacade = null;
        }
    }

    private static void montarDadosUsuario(int codigo, RemessaVO obj, Connection c) throws Exception {
        if (codigo != 0) {
            Usuario usuarioFacade = new Usuario(c);
            obj.setUsuario(usuarioFacade.consultarPorChavePrimaria(codigo, Uteis.NIVELMONTARDADOS_MINIMOS));
            usuarioFacade= null;
        }
    }

    @Override
    public RemessaVO consultarPorChavePrimaria(final int codigo) throws Exception {
        RemessaVO eCache = (RemessaVO) obterFromCache(codigo);
        if (eCache != null){
            return eCache;
        }

        String sql = "SELECT * FROM Remessa WHERE codigo = ?";
        try (PreparedStatement sqlConsultar = con.prepareStatement(sql)) {
            sqlConsultar.setInt(1, codigo);
            try (ResultSet tabelaResultado = sqlConsultar.executeQuery()) {
                if (!tabelaResultado.next()) {
                    throw new ConsistirException(String.format("Dados Não Encontrados ( Remessa %s)", codigo));
                }
                eCache = montarDados(tabelaResultado, con);
            }
        }
        putToCache(eCache);
        return eCache;
    }

    public List<RemessaVO> consultar(Date dataInicio, Date dataFim, List<Integer> empresas, List<Integer> convenios, List<SituacaoRemessaEnum> situacaoRemessaEnums,
                                     boolean somenteRemessaCancelamento, PaginadorDTO paginadorDTO) throws Exception {
        return  consultar(dataInicio, dataFim, empresas, convenios, situacaoRemessaEnums, somenteRemessaCancelamento, paginadorDTO, null);
    }

    public List<RemessaVO> consultar(Date dataInicio, Date dataFim, List<Integer> empresas, List<Integer> convenios, List<SituacaoRemessaEnum> situacaoRemessaEnums,
                                     boolean somenteRemessaCancelamento, PaginadorDTO paginadorDTO, Integer[] tiposRemessaEnum) throws Exception {
        StringBuilder sql = new StringBuilder();

        sql.append("select \n");
        sql.append("(SELECT sum(valoritemremessa) FROM remessaitem ri where ri.remessa = re.codigo) as valorbruto, \n");
        sql.append("re.* \n");
        sql.append("from remessa re \n");
        sql.append("where re.dataRegistro between '").append(Uteis.getDataJDBCTimestamp(Calendario.primeiraHoraDia(dataInicio))).append("' ");
        sql.append("and '").append(Uteis.getDataJDBCTimestamp(Calendario.ultimaHoraDia(dataFim))).append("' \n");

        if (tiposRemessaEnum != null) {
            sql.append("AND re.tipo in (").append(Uteis.splitFromArray(tiposRemessaEnum, false)).append(") \n");
        }

        if (!UteisValidacao.emptyList(empresas)) {
            String listaIn = "";
            for (Integer cod : empresas) {
                if (!UteisValidacao.emptyNumber(cod)) {
                    listaIn += "," + cod;
                }
            }
            listaIn = listaIn.replaceFirst(",", "");
            sql.append(" and re.empresa in (").append(listaIn).append(") \n");
        }
        if (!UteisValidacao.emptyList(convenios)) {
            String listaIn = "";
            for (Integer cod : convenios) {
                if (!UteisValidacao.emptyNumber(cod)) {
                    listaIn += "," + cod;
                }
            }
            listaIn = listaIn.replaceFirst(",", "");
            sql.append(" and re.conveniocobranca in (").append(listaIn).append(") \n");
        }
        if (somenteRemessaCancelamento) {
            sql.append(" and re.cancelamento \n");
        }

        if (!UteisValidacao.emptyList(situacaoRemessaEnums)) {
            StringBuilder situacao = new StringBuilder();
            for (SituacaoRemessaEnum sre : situacaoRemessaEnums) {
                situacao.append(sre.getId())
                        .append(",");
            }
            sql.append(" and re.situacaoremessa in (");
            sql.append(situacao.substring(0, situacao.length() - 1));
            sql.append(") \n");
        }

        sql.append(" order by re.dataRegistro desc \n");

        if (paginadorDTO != null) {
            Integer total = SuperFacadeJDBC.contar("SELECT COUNT(*) FROM (" + sql.toString() + ") as qtd", con);

            paginadorDTO.setQuantidadeTotalElementos(total.longValue());

            int maxResults = paginadorDTO.getSize() == null ? 10 : paginadorDTO.getSize().intValue();
            int indiceInicial = paginadorDTO.getPage() == null ? 0 : paginadorDTO.getPage().intValue() * maxResults;
            paginadorDTO.setSize((long) maxResults);
            paginadorDTO.setPage((long) indiceInicial);

            sql.append("LIMIT ").append(maxResults).append(" \n");
            sql.append("OFFSET ").append(indiceInicial).append(" \n");
        }

        try (PreparedStatement stm = con.prepareStatement(sql.toString())) {
            try (ResultSet rs = stm.executeQuery()) {
                return montarDadosConsulta(rs, con);
            }
        }
    }

    @Override
    public List<RemessaVO> consultar(Date dataInicio, Date dataFim, int codigoEmpresa, int convenio) throws Exception {
        List<Integer> empresas = new ArrayList<>();
        if (!UteisValidacao.emptyNumber(codigoEmpresa)) {
            empresas.add(codigoEmpresa);
        }
        List<Integer> convenios = new ArrayList<>();
        if (!UteisValidacao.emptyNumber(convenio)) {
            convenios.add(convenio);
        }
        return consultar(dataInicio, dataFim,empresas,convenios,null, false, null);
    }

    public RemessaVO consultarPorMovParcela(Integer codigoMovparcela) throws Exception {
        String sql = "SELECT r.* FROM remessa r" +
                " \nINNER JOIN remessaitem ri ON ri.remessa = r.codigo AND ri.movparcela = ?";
        try (PreparedStatement sqlConsultar = con.prepareStatement(sql)) {
            sqlConsultar.setInt(1, codigoMovparcela);
            try (ResultSet tabelaResultado = sqlConsultar.executeQuery()) {
                if (tabelaResultado.next()) {
                    return montarDados(tabelaResultado, con);
                } else {
                    return new RemessaVO();
                }
            }
        }
    }

    @Override
    public List<RemessaVO> consultarPorSituacao(SituacaoRemessaEnum situacao,
                                                int codigoEmpresa, int convenio) throws Exception {
        String sql = "select * from remessa where situacaoremessa = ? ";
        if (codigoEmpresa != 0) {
            sql += " and empresa = " + codigoEmpresa;
        }
        if (convenio != 0) {
            sql += " and conveniocobranca = " + convenio;
        }

        // NÃO ALTERAR O ORDER BY!!!
        // Está (order by codigo) é a forma mais correta para enviar as remessas na ordem correta.!
        //Evitando problemas de sequencial.! by Luiz Felipe
        sql += " order by codigo ";
        try (PreparedStatement stm = con.prepareStatement(sql)) {
            stm.setInt(1, situacao.getId());
            try (ResultSet rs = stm.executeQuery()) {
                return montarDadosConsulta(rs, con);
            }
        }
    }

    private void incluirRemessaTransacoes(RemessaVO remessaVO) throws Exception {
        List<TransacaoVO> lista = remessaVO.getListaTransacoes();
        RemessaTransacao tmpFacade = new RemessaTransacao(con);
        try {
            for (TransacaoVO transacaoVO : lista) {
                RemessaTransacaoVO tmp = new RemessaTransacaoVO();
                tmp.setRemessa(remessaVO);
                tmp.setTransacao(transacaoVO);
                tmpFacade.incluirSemCommit(tmp);
            }
        } finally {
            tmpFacade = null;
        }
    }

    private void incluirRemessaItens(RemessaVO remessaVO) throws Exception {
        RemessaItem tmpFacade = new RemessaItem(con);
        try {
            for (RemessaItemVO item : remessaVO.getListaItens()) {
                tmpFacade.incluir(item);
            }
        } finally {
            tmpFacade = null;
        }
    }

    private void incluirRemessaCancelamentoItens(RemessaVO remessa) throws Exception {
        RemessaCancelamentoItem remessaCancelamentoItemDAO = new RemessaCancelamentoItem(con);
        for (RemessaCancelamentoItemVO itemCancelamento : remessa.getListaItensCancelamento()) {
            remessaCancelamentoItemDAO.incluir(itemCancelamento);
        }
        remessaCancelamentoItemDAO = null;
    }

    @Override
    public void preencherTransacoes(RemessaVO remessaVO) throws Exception {
        TransacaoInterfaceFacade transacao = new Transacao(con);
        List<TransacaoVO> lista = transacao.consultarPorRemessa(remessaVO.getCodigo());
        remessaVO.setListaTransacoes(lista);
    }

    public boolean processarSituacaoRemessa(Integer codigoRemessa) throws Exception {
        RemessaItem itemDao = new RemessaItem(con);
        RemessaVO remessa = consultarPorChavePrimaria(codigoRemessa);
        boolean remessaProcessada = itemDao.existeItensASerProcessados(codigoRemessa, remessa.getDCO());
        if (remessaProcessada) {
            Uteis.logar(null, "### MARCAR REMESSA PARA PROCESSADA " + codigoRemessa + " | " + remessa.getTipo().name());
            remessa.setSituacaoRemessa(SituacaoRemessaEnum.RETORNO_PROCESSADO);
            alterar(remessa);
        }
        return remessaProcessada;
    }

    public boolean processarSituacaoRemessaItauBoleto(Integer codigoRemessa) throws Exception {
        RemessaItem itemDao = new RemessaItem(con);
        RemessaVO remessa = consultarPorChavePrimaria(codigoRemessa);
        List<RemessaItemVO> itens = itemDao.consultarPorCodigoRemessa(codigoRemessa, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
        for(RemessaItemVO item : itens){
            String status = item.getProps().get(DCCAttEnum.CodOcorrencia.name());
            if(UteisValidacao.emptyString(status) || DCOItauStatusEnum.StatusBD.getId().equals(status.trim())){
                return false;
            }
        }
        remessa.setSituacaoRemessa(SituacaoRemessaEnum.RETORNO_PROCESSADO);
        alterar(remessa);
        return true;
    }

    public RemessaVO consultarRemessaAberta(int convenio, int empresa, UsuarioVO usuarioVO) throws Exception {
        String sql = "SELECT * FROM remessa \n" +
                "WHERE datafechamento IS NULL \n" +
                "AND conveniocobranca = ? \n" +
                "AND empresa = ? \n";
        List<RemessaVO> remessasAbertas;
        try (PreparedStatement sqlConsultar = con.prepareStatement(sql)) {
            sqlConsultar.setInt(1, convenio);
            sqlConsultar.setInt(2, empresa);
            try (ResultSet tabelaResultado = sqlConsultar.executeQuery()) {
                remessasAbertas = new ArrayList<RemessaVO>();
                while (tabelaResultado.next()) {
                    remessasAbertas.add(montarDados(tabelaResultado, con));
                }
            }
        }

        List<RemessaVO> remessasDoDia = new ArrayList<RemessaVO>();
        for (RemessaVO remessaVO : remessasAbertas) {
            if (!Calendario.getDataComHoraZerada(remessaVO.getDataRegistro()).equals(Calendario.getDataComHoraZerada(Calendario.hoje()))) {
                executarFecharRemessa(usuarioVO, remessaVO);
            } else {
                remessasDoDia.add(remessaVO);
            }
        }

        if (remessasDoDia.size() == 0) {
            return null;
        }

        Ordenacao.ordenarLista(remessasDoDia, "dataRegistro");
        Collections.reverse(remessasDoDia);
        RemessaVO remessaRetornar = remessasDoDia.remove(0);

        for (RemessaVO remessaVO : remessasDoDia) {
            executarFecharRemessa(usuarioVO, remessaVO);
        }

        RemessaItem remessaItemDAO = new RemessaItem(con);
        remessaRetornar.setListaItens(remessaItemDAO.consultarPorCodigoRemessa(remessaRetornar.getCodigo(), Uteis.NIVELMONTARDADOS_GESTAOREMESSABOLETO));
        remessaItemDAO = null;
        return remessaRetornar;
    }

    private void executarFecharRemessa(UsuarioVO usuarioVO, RemessaVO remessaVO) throws SQLException {
        remessaVO.setUsuarioFechamento(usuarioVO);
        remessaVO.setDataFechamento(Calendario.hoje());
        fecharRemessa(remessaVO);
    }

    public void fecharRemessa(RemessaVO remessaVO) throws SQLException {
        String sql = "UPDATE remessa SET datafechamento = ?, usuariofechamento = ? WHERE codigo = ?";
        try (PreparedStatement sqlFecharRemessa = con.prepareStatement(sql)) {
            sqlFecharRemessa.setTimestamp(1, Uteis.getDataJDBCTimestamp(remessaVO.getDataFechamento()));
            sqlFecharRemessa.setInt(2, remessaVO.getUsuarioFechamento().getCodigo());
            sqlFecharRemessa.setInt(3, remessaVO.getCodigo());
            sqlFecharRemessa.execute();
        }
    }

    public Integer consultarOrdemDaycoval(Date dataPesquisar, Integer codConvenio) throws Exception {
        String sql = "SELECT row_number() OVER (PARTITION by 0) as _seq, codigo \n" +
                "FROM remessa \n" +
                "WHERE dataregistro::DATE = '" + Uteis.getDataJDBC(dataPesquisar) + "' \n" +
                "AND conveniocobranca  = " + codConvenio + "\n";
        int seq;
        try (PreparedStatement stm = con.prepareStatement(sql)) {
            try (ResultSet rs = stm.executeQuery()) {
                seq = 0;
                while (rs.next()) {
                    seq = rs.getInt("_seq");
                }
            }
        }
        return ++seq;
    }

    public List<RemessaVO> consultarPorIdentificador(String identificador) throws Exception {
        StringBuilder sql = new StringBuilder();
        sql.append("select * from remessa where identificador ilike '").append(identificador).append("%'");
        try (PreparedStatement stm = con.prepareStatement(sql.toString())) {
            try (ResultSet rs = stm.executeQuery()) {
                return montarDadosConsulta(rs, con);
            }
        }
    }

    public Integer consultarTotalConvitePorIndicado( Integer empresa)throws Exception{
        String sql = "select count(*) as total from remessa where situacaoremessa = 4 and dataregistro <= current_date - 5 and empresa = " +empresa ;
        try (PreparedStatement pst = con.prepareStatement(sql)) {
            try (ResultSet rs = pst.executeQuery()) {
                if (rs.next())
                    return rs.getInt("total");
            }
        }
        return 0;
    }

    public void incluirRemessaHistoricoEnvio(RemessaVO remessaVO, UsuarioVO usuarioVO) {
        try {
            String responsavel = "";
            if (usuarioVO != null) {
                if (!UteisValidacao.emptyString(usuarioVO.getNome())) {
                    responsavel = usuarioVO.getNome();
                } else if (!UteisValidacao.emptyString(usuarioVO.getUsername())) {
                    responsavel = usuarioVO.getUsername();
                } else {
                    responsavel = "Usuário: " + usuarioVO.getCodigo();
                }
            }

            String sql = "INSERT INTO remessahistoricoenvio(data, remessa, responsavel, nomeArquivoEnvio) VALUES (?, ?, ?, ?);";
            try (PreparedStatement sqlInserir = con.prepareStatement(sql)) {
                sqlInserir.setTimestamp(1, Uteis.getDataJDBCTimestamp(Calendario.hoje()));
                sqlInserir.setInt(2, remessaVO.getCodigo());
                sqlInserir.setString(3, responsavel);
                sqlInserir.setString(4, remessaVO.getNomeArquivoEnvio());
                sqlInserir.execute();
            }
        } catch (Exception ex) {
            ex.printStackTrace();
            Uteis.logar(true, null, "Erro ao incluir remessahistoricoenvio!: " + ex.getMessage());
        }
    }

    public void registrarLogReenvioReenvio(RemessaVO remessaVO) throws Exception {
        //REGISTRAR LOG
        String sqlLog = "insert into log(dataalteracao, pessoa, nomeentidade, nomeentidadedescricao, chaveprimaria, nomecampo, valorcampoanterior, valorcampoalterado, responsavelalteracao, operacao) " +
                "values (now(), 0, ?, ?, ?, ?, ?, ?, ?, ?);";

        try (PreparedStatement pst = con.prepareStatement(sqlLog)) {
            pst.setString(1, "REMESSA");
            pst.setString(2, "Remessa - Rejeitada");
            pst.setString(3, remessaVO.getCodigo().toString());
            pst.setString(4, "Remessa - Rejeitada");
            pst.setString(5, "");
            pst.setString(6, "Vou colocar como GERADA para realizar o reenvio");
            pst.setString(7, "PROCESSO AUTOMÁTICO");
            pst.setString(8, "ALTERAÇÃO");
            pst.execute();
        }
    }

    public void incluirRemessaRejeitada(Integer remessa, Integer convenioCobranca, String nomeArquivo) {
        try {
            String sql = "INSERT INTO remessarejeitada(data, remessa, convenioCobranca, arquivo) VALUES (?, ?, ?, ?);";
            try (PreparedStatement sqlInserir = con.prepareStatement(sql)) {
                sqlInserir.setTimestamp(1, Uteis.getDataJDBCTimestamp(Calendario.hoje()));
                sqlInserir.setInt(2, remessa);
                sqlInserir.setInt(3, convenioCobranca);
                sqlInserir.setString(4, nomeArquivo);
                sqlInserir.execute();
            }
        } catch (Exception ex) {
            ex.printStackTrace();
            Uteis.logar(true, null, "Erro ao incluir remessahistoricoenvio!: " + ex.getMessage());
        }
    }

    public boolean arquivoRejeitadoProcessado(Integer remessa, Integer convenioCobranca, String nomeArquivo) {
        try {
            StringBuilder sql = new StringBuilder();
            sql.append("select exists (select codigo from remessarejeitada ");
            sql.append(" where remessa = ").append(remessa);
            sql.append(" and convenioCobranca = ").append(convenioCobranca);
            sql.append(" and arquivo ilike '").append(nomeArquivo).append("') as existe ");

            ResultSet rs = SuperFacadeJDBC.criarConsulta(sql.toString(), con);

            //padrão retornar true para evitar remessa reenviada indevidamente!
            boolean existe = true;
            if (rs.next()) {
                existe = rs.getBoolean("existe");
            }
            return existe;
        } catch (Exception ex) {
            ex.printStackTrace();
            Uteis.logar(true, null, "Erro ao incluir remessahistoricoenvio!: " + ex.getMessage());
            //padrão retornar true para evitar remessa reenviada indevidamente!
            return true;
        }
    }

    public RemessaVO obterUltimaRemessaPorSituacao(final Integer[] situacao, int codigoEmpresa, Boolean cancelamento, int convenio) throws Exception {

        String condicaoSituacao = "";
        if (situacao != null) {
            condicaoSituacao = Uteis.splitFromArray(situacao, false);
        }

        String sql = "select * from remessa where 1 = 1 ";
        if (codigoEmpresa != 0) {
            sql += " and empresa = " + codigoEmpresa;
        }
        if (convenio != 0) {
            sql += " and conveniocobranca = " + convenio + " ";
        }
        if (!UteisValidacao.emptyString(condicaoSituacao)) {
            sql += " and situacaoremessa in (" + condicaoSituacao + ") ";
        }
        if (cancelamento != null) {
            sql += " and cancelamento = " + cancelamento + " ";
        }

        sql += " order by codigo desc limit 1";
        try (PreparedStatement stm = con.prepareStatement(sql)) {
            try (ResultSet rs = stm.executeQuery()) {
                if (rs.next()) {
                    return montarDados(rs, con);
                } else {
                    return null;
                }

            }
        }
    }

    public List<RemessaVO> consultarPorSituacao(final Integer[] situacao, Integer empresa, Integer convenio, Date dataRegistroMinima) throws Exception {

        String condicaoSituacao = "";
        if (situacao != null) {
            condicaoSituacao = Uteis.splitFromArray(situacao, false);
        }

        String sql = "select * from remessa where 1 = 1 ";
        if (!UteisValidacao.emptyNumber(empresa)) {
            sql += " and empresa = " + empresa;
        }
        if (!UteisValidacao.emptyNumber(convenio)) {
            sql += " and conveniocobranca = " + convenio;
        }
        if (!UteisValidacao.emptyString(condicaoSituacao)) {
            sql += " and situacaoremessa in (" + condicaoSituacao + ") ";
        }
        if (dataRegistroMinima != null) {
            sql += " and (dataregistro::date >= '" + Uteis.getDataFormatoBD(dataRegistroMinima) + "' OR dataregistrooriginal::date >= '" + Uteis.getDataFormatoBD(dataRegistroMinima) + "') ";
        }

        sql += " order by codigo ";
        try (PreparedStatement stm = con.prepareStatement(sql)) {
            try (ResultSet rs = stm.executeQuery()) {
                return montarDadosConsulta(rs, con);
            }
        }
    }

    public List<RemessaVO> consultarRemessasSemSitucao(Date dataRegistro) throws Exception {
        StringBuilder sql = new StringBuilder();
        sql.append("select  \n");
        sql.append("r.codigo, \n");
        sql.append("r.dataregistro, \n");
        sql.append("r.cancelamento, \n");
        sql.append("u.codigo as usuario, \n");
        sql.append("u.nome as usuarionome, \n");
        sql.append("cc.codigo as convenio, \n");
        sql.append("cc.descricao as convenioNome, \n");
        sql.append("e.timeZoneDefault, \n");
        sql.append("e.codigo as empresa, \n");
        sql.append("e.nome as empresanome \n");
        sql.append("from remessa r \n");
        sql.append("inner join usuario u on r.usuario = u.codigo \n");
        sql.append("inner join conveniocobranca cc on cc.codigo = r.conveniocobranca \n");
        sql.append("inner join empresa e on e.codigo = r.empresa \n");
        sql.append("where r.situacaoremessa = 0 \n");
        if (dataRegistro != null) {
            sql.append("and r.dataregistro::date = '").append(Uteis.getDataFormatoBD(Calendario.hoje())).append("' \n");
        }
        sql.append("and r.tipo not in (").append(TipoRemessaEnum.APROVA_FACIL.getId()).append(") \n");
        sql.append("order by r.dataregistro desc\n");

        List<RemessaVO> lista = new ArrayList<>();
        try (PreparedStatement stm = con.prepareStatement(sql.toString())) {
            try (ResultSet rs = stm.executeQuery()) {
                while (rs.next()) {
                    RemessaVO remessaVO = new RemessaVO();
                    remessaVO.setCodigo(rs.getInt("codigo"));
                    remessaVO.setDataRegistro(rs.getTimestamp("dataregistro"));
                    remessaVO.setCancelamento(rs.getBoolean("cancelamento"));
                    remessaVO.getUsuario().setCodigo(rs.getInt("usuario"));
                    remessaVO.getUsuario().setNome(rs.getString("usuarionome"));
                    remessaVO.getConvenioCobranca().setCodigo(rs.getInt("convenio"));
                    remessaVO.getConvenioCobranca().setDescricao(rs.getString("convenionome"));
                    remessaVO.setEmpresa(rs.getInt("empresa"));
                    remessaVO.getEmpresaVO().setCodigo(rs.getInt("empresa"));
                    remessaVO.getEmpresaVO().setNome(rs.getString("empresanome"));
                    remessaVO.getEmpresaVO().setTimeZoneDefault(rs.getString("timeZoneDefault"));

                    if (dataRegistro == null) {
                        // isso para evitar que seja consultado remessas que podem estar sendo processadas no mesmo momento
                        Date dataMaximaLancamento = Calendario.hoje();
                        if (!UteisValidacao.emptyString(remessaVO.getEmpresaVO().getTimeZoneDefault())) {
                            dataMaximaLancamento = Calendario.getDateInTimeZone(Calendario.hoje(), remessaVO.getEmpresaVO().getTimeZoneDefault());
                        }
                        Long diferencaMinutos = Calendario.diferencaEmMinutos(remessaVO.getDataRegistro(), dataMaximaLancamento);
                        if (diferencaMinutos.intValue() < 60) {
                            continue;
                        }
                    }

                    lista.add(remessaVO);
                }
            }
        }
        return lista;
    }

    public void alterarSituacao(RemessaVO remessaVO) throws SQLException {
        String sql = "UPDATE remessa SET situacaoremessa = ? WHERE codigo = ?;";
        try (PreparedStatement sqlInserir = con.prepareStatement(sql)) {
            sqlInserir.setInt(1, remessaVO.getSituacaoRemessa().getId());
            sqlInserir.setInt(2, remessaVO.getCodigo());
            sqlInserir.execute();
        }
    }

    public boolean existeRemessaPendente(Integer convenioCobranca) throws Exception {
        StringBuilder sqlStr = new StringBuilder("select exists ( \n");
        sqlStr.append("select \n");
        sqlStr.append("r.codigo \n");
        sqlStr.append("from remessa r \n");
        sqlStr.append("where r.situacaoremessa in (").append(SituacaoRemessaEnum.GERADA.getId()).append(",").append(SituacaoRemessaEnum.REMESSA_ENVIADA.getId()).append(") \n");
        if(!UteisValidacao.emptyNumber(convenioCobranca)){
            sqlStr.append(" and r.conveniocobranca = ").append(convenioCobranca).append(" \n");
        }
        sqlStr.append(") as existe");
        try (PreparedStatement stm = con.prepareStatement(sqlStr.toString())) {
            try (ResultSet tabelaResultado = stm.executeQuery()) {
                tabelaResultado.next();
                return tabelaResultado.getBoolean("existe");
            }
        }
    }

    public void alterarIdPactoPay(RemessaVO remessaVO) throws SQLException {
        String sql = "UPDATE remessa SET idpactopay = ? WHERE codigo = ?;";
        try (PreparedStatement sqlInserir = con.prepareStatement(sql)) {
            sqlInserir.setString(1, remessaVO.getIdPactoPay());
            sqlInserir.setInt(2, remessaVO.getCodigo());
            sqlInserir.execute();
        }
    }
}

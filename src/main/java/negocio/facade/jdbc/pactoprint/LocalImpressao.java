package negocio.facade.jdbc.pactoprint;

import negocio.comuns.basico.EmpresaVO;
import negocio.comuns.utilitarias.Calendario;
import negocio.comuns.utilitarias.ConsistirException;
import negocio.comuns.utilitarias.Ordenacao;
import negocio.comuns.utilitarias.Uteis;
import negocio.facade.jdbc.arquitetura.SuperEntidade;
import negocio.interfaces.pactoprint.LocalImpressaoInterfaceFacade;
import negocio.comuns.pactoprint.LocalImpressaoVO;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.sql.Statement;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

/**
 * date : 01/04/2015 14:29:04 
 * author: Ulisses
 */
public class LocalImpressao extends SuperEntidade implements LocalImpressaoInterfaceFacade {

	public LocalImpressao() throws Exception {
		super();
	}

	public LocalImpressao(Connection conexao) throws Exception {
		super(conexao);
	}
	public void alterar(LocalImpressaoVO localImpressaoVO) throws Exception{

        localImpressaoVO.validarDados(localImpressaoVO);
		validarNomeComputador(localImpressaoVO);
		StringBuilder sql = new StringBuilder();
		sql.append("update localImpressao set nome = ?, nomecomputador = ?, empresa = ?,datalancamento = ? where codigo = ? ");
		PreparedStatement pst = con.prepareStatement(sql.toString());
		pst.setString(1, localImpressaoVO.getNome().toUpperCase());
		pst.setString(2, localImpressaoVO.getNomeComputador().toUpperCase());
		pst.setInt(3, localImpressaoVO.getEmpresaVO().getCodigo());
		pst.setTimestamp(4, Uteis.getDataJDBCTimestamp(Calendario.hoje()));
		pst.setInt(5, localImpressaoVO.getCodigo());
		pst.execute();
	}

	private void validarNomeComputador(LocalImpressaoVO localImpressaoVO) throws Exception{

			StringBuilder sql = new StringBuilder();
			sql.append("select nomecomputador,codigo from localImpressao where nomecomputador = ?");
			PreparedStatement pst = con.prepareStatement(sql.toString());
			pst.setString(1, localImpressaoVO.getNomeComputador());
			ResultSet rs = pst.executeQuery();
		    if (rs.next()){
				if(localImpressaoVO.getCodigo()!= null) {
					if (rs.getInt("codigo") != localImpressaoVO.getCodigo())
						throw new ConsistirException("O campo NomeComputador(Local Impressao) estß sendo usado em outro cadastro!");
				}else
				{
					throw new ConsistirException("O campo NomeComputador(Local Impressao) estß sendo usado em outro cadastro!");
				}
		     }
	}

	public void incluir(LocalImpressaoVO localImpressaoVO) throws Exception{

		localImpressaoVO.validarDados(localImpressaoVO);
		validarNomeComputador(localImpressaoVO);
		StringBuilder sql = new StringBuilder();
		sql.append("insert into localImpressao (nome,nomecomputador, empresa, dataLancamento) values(?,?,?,?) ");
		PreparedStatement pst = con.prepareStatement(sql.toString());
		pst.setString(1, localImpressaoVO.getNome().toUpperCase());
		pst.setString(2, localImpressaoVO.getNomeComputador().toUpperCase());
		pst.setInt(3, localImpressaoVO.getEmpresaVO().getCodigo());
		pst.setTimestamp(4, Uteis.getDataJDBCTimestamp(Calendario.hoje()));
		pst.execute();
		localImpressaoVO.setCodigo(obterValorChavePrimariaCodigo());
		localImpressaoVO.setNovoObj(false);

	}
	public void excluir(LocalImpressaoVO localImpressaoVO) throws Exception{
		StringBuilder sql = new StringBuilder();
		sql.append(" delete from localImpressao where codigo = ").append(localImpressaoVO.getCodigo());
		PreparedStatement pst = getCon().prepareStatement(sql.toString());
		pst.execute();

	}

	public LocalImpressaoVO consultarPorNomeComputador(String nomeComputador, int nivelMontarDados) throws Exception{
    	StringBuilder sql = new StringBuilder();
    	sql.append(" select * from localImpressao where nomeComputador = '").append(nomeComputador).append("'");
    	Statement st = getCon().createStatement();
    	ResultSet dados = st.executeQuery(sql.toString());
    	if (dados.next()){
    		return montarDados(dados, nivelMontarDados);
    	}
    	return null;
	}
	
    private LocalImpressaoVO montarDados(ResultSet dadosSQL, int nivelMontarDados) throws Exception {
    	LocalImpressaoVO localImpressaoVO = new LocalImpressaoVO();
    	localImpressaoVO.setCodigo(dadosSQL.getInt("codigo"));
    	localImpressaoVO.setNome(dadosSQL.getString("nome"));
    	localImpressaoVO.setNomeComputador(dadosSQL.getString("nomeComputador"));
    	localImpressaoVO.setEmpresaVO(new EmpresaVO());
    	localImpressaoVO.getEmpresaVO().setCodigo(dadosSQL.getInt("empresa"));
    	localImpressaoVO.setDataLancamento(dadosSQL.getTimestamp("dataLancamento"));
    	localImpressaoVO.setNovoObj(false);

		if (nivelMontarDados == Uteis.NIVELMONTARDADOS_DADOSBASICOS) {
			return localImpressaoVO;
		}
		return localImpressaoVO;
    }
	public LocalImpressaoVO consultarPorCodigo(Integer codigo, int nivelMontarDados) throws Exception{
		StringBuilder sql = new StringBuilder();
		sql.append(" select * from localImpressao where codigo = ").append(codigo);
		Statement st = getCon().createStatement();
		ResultSet dados = st.executeQuery(sql.toString());
		if (dados.next()){
			return montarDados(dados, nivelMontarDados);
		}

		return null;

	}
    
    public List<LocalImpressaoVO> consultarLocalImpressao(Integer codigoEmpresa, int nivelMontarDados) throws Exception{
    	if ((codigoEmpresa == null) || (codigoEmpresa<= 0)){
    		throw new ConsistirException("C¾digo da empresa Ú necessßrio para realizar a consulta.");
    	}
    	StringBuilder sql = new StringBuilder();
    	sql.append("select * from localImpressao where empresa = ").append(codigoEmpresa);
    	Statement st = getCon().createStatement();
    	return montarDadosConsulta(st.executeQuery(sql.toString()), nivelMontarDados);
    	
    }
	private ResultSet getResultSetJSON() throws SQLException {
		String sql = "SELECT codigo, nome,nomecomputador FROM localImpressao ORDER BY nome";
		PreparedStatement pst = con.prepareStatement(sql);
		return pst.executeQuery();
	}
	public String consultarJSON() throws Exception{
		ResultSet resultSet = getResultSetJSON();
		StringBuilder json = new StringBuilder();
		json.append("{\"aaData\":[");
		boolean dados = false;
		while (resultSet.next()) {
			dados = true;
			json.append("[\"").append(resultSet.getString("codigo")).append("\",");
			json.append("\"").append(resultSet.getString("nome")).append("\",");
			json.append("\"").append(resultSet.getString("nomecomputador").trim()).append("\"],");
		}
		if (dados) {
			json.deleteCharAt(json.toString().length() - 1);
		}
		json.append("]}");
		return json.toString();
	}

    private List<LocalImpressaoVO> montarDadosConsulta(ResultSet dados, int nivelMontarDados) throws Exception {
        List<LocalImpressaoVO> lista = new ArrayList<LocalImpressaoVO>();
        while (dados.next()) {
        	LocalImpressaoVO obj = montarDados(dados, nivelMontarDados);
            lista.add(obj);
        }
        return lista;
    }


	public List consultarParaImpressao(String filtro, String ordem, String campoOrdenacao, int i) throws SQLException {

		ResultSet rs = getResultSetJSON();
		List lista = new ArrayList();
		while (rs.next()) {
			LocalImpressaoVO localImpressaoVO = new LocalImpressaoVO();
			String geral = rs.getString("codigo") + rs.getString("nome")+rs.getString("nomecomputador");
			if (geral.toLowerCase().contains(filtro.toLowerCase())) {
				localImpressaoVO.setCodigo(rs.getInt("codigo"));
				localImpressaoVO.setNome(rs.getString("nome"));
				localImpressaoVO.setNomeComputador(rs.getString("nomecomputador"));
				lista.add(localImpressaoVO);
			}
		}
		if (campoOrdenacao.equals("C¾digo")) {
			Ordenacao.ordenarLista(lista, "codigo");
		} else if (campoOrdenacao.equals("Nome")) {
			Ordenacao.ordenarLista(lista, "nome");
		}
		   else if (campoOrdenacao.equals("Nome_Computador")) {
		Ordenacao.ordenarLista(lista, "nomecomputador");
	}
		if (ordem.contains("desc")) {
			Collections.reverse(lista);
		}
		return lista;

	}



}

package negocio.facade.jdbc.memcached;

import negocio.facade.jdbc.arquitetura.MemCachedManager;
import org.jsoup.Connection;
import org.jsoup.Jsoup;
import org.jsoup.nodes.Document;
import org.jsoup.nodes.Element;
import org.jsoup.select.Elements;
import servicos.propriedades.PropsService;

import java.util.HashMap;

/**
 * Created by <PERSON> on 26/06/2016.
 */
public class Wiki {

    private String urlWiki;
    private String identificadorCache = "HINTS_PERFIL_ACESSO";
    private String keyWiki = "KEY_WIKI";

    public Wiki() throws Exception{
        urlWiki = PropsService.getPropertyValue(PropsService.urlWikiRaiz);
    }
    public MemCachedManager getMemCachedManager() throws Exception{
        return MemCachedManager.getInstance();
    }
    public  Elements obterElementosWiki(String selector,String url) throws Exception{
        String urlWikiContexto = urlWiki.substring(0 , urlWiki.indexOf("/index"));
        String urlLogin = urlWikiContexto+"/api.php?action=login&lgname=zillyonweb&lgpassword=xq7J4m3n";
        String token = "";
        Connection con = Jsoup.connect(urlWikiContexto+"/api.php?action=login&lgname=zillyonweb&lgpassword=xq7J4m3n&format=xml").cookie("wikipactodb_session","81orpqg9busrolc8b8ajnk0ge7");
        con.timeout(10000);
        token = con.get().select("login[token]").first().attr("token");
        Document doc = con.url(urlWikiContexto+"/api.php?action=login&lgname=zillyonweb&lgpassword=xq7J4m3n&lgtoken="+token).get();
        doc = con.url(urlWiki+url).get();
        Elements elements = doc.select(selector);
        return elements;
    }
    public HashMap<String,String> obterSalvarCacheHintsWiki() throws Exception{
        HashMap<String,String> hints =  new HashMap<String, String>();
        Elements elements = obterElementosWiki("li > b","/ZillyonWeb:Cadastros:Acesso_ao_sistema:Perfil_Acesso");
        for(Element element : elements){
            String codigoPerm = element.text().split("-")[0].trim();
            hints.put(codigoPerm,element.parent().ownText().replace("- ",""));
        }
        getMemCachedManager().gravar(hints,identificadorCache, keyWiki);
        return hints;
    }
    public HashMap<String,String> obterHintsPerfilAcesso() throws Exception{
        HashMap<String,String> hints = new HashMap<String, String>();
        hints =  getMemCachedManager().ler(hints.getClass(),identificadorCache, keyWiki);
        return hints;
    }

}

/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package negocio.facade.jdbc.feed;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import negocio.comuns.feed.PaginaInicialFeedVO;
import negocio.facade.jdbc.arquitetura.SuperEntidade;
import static negocio.facade.jdbc.arquitetura.SuperFacadeJDBC.criarConsulta;
import negocio.interfaces.feed.PaginaInicialInterfaceFacade;

/**
 *
 * <AUTHOR>
 */
public class PaginaInicialFeed extends SuperEntidade implements PaginaInicialInterfaceFacade {
    
    public PaginaInicialFeed() throws Exception{
        super();
    }
    
    public PaginaInicialFeed(Connection con) throws Exception{
        super(con);
    }
    
    @Override
    public PaginaInicialFeedVO incluirPaginaInicial(PaginaInicialFeedVO pagina) throws Exception {
        StringBuilder insert = new StringBuilder();
        insert.append(" INSERT INTO paginainicialfeed( \n");
        insert.append(" titulogrande, mensagemcapa, textobalaogrande, linkvideo, linkimagem) \n");
        insert.append(" VALUES ( ?, ?, ?, ?, ?) ");
        
        PreparedStatement insStm = con.prepareStatement(insert.toString());
        int i = 1;
        
        insStm.setString(i++, pagina.getTituloGrande());
        insStm.setString(i++, pagina.getMensagemCapa());
        insStm.setString(i++, pagina.getTextoBalaoGrande());
        insStm.setString(i++, pagina.getLinkVideo());
        insStm.setString(i++, pagina.getLinkImagem());
        
        insStm.execute();
        
        pagina.setCodigo(obterValorChavePrimariaCodigo());
        return pagina;
    }
    
    @Override
    public void alterarPaginaInicial(PaginaInicialFeedVO pagina) throws Exception {
        consultarPaginaInicial();
        
        StringBuilder update = new StringBuilder();
        update.append(" UPDATE paginainicialfeed SET titulogrande=?, mensagemcapa=?, textobalaogrande=?, ");
        update.append(" linkvideo=?, linkimagem=? ");
        
        PreparedStatement upStm = con.prepareStatement(update.toString());
        int i = 1;
        
        upStm.setString(i++, pagina.getTituloGrande());
        upStm.setString(i++, pagina.getMensagemCapa());
        upStm.setString(i++, pagina.getTextoBalaoGrande());
        upStm.setString(i++, pagina.getLinkVideo());
        upStm.setString(i++, pagina.getLinkImagem());
        
        
        upStm.execute();
    }
    
    @Override
    public PaginaInicialFeedVO consultarPaginaInicial() throws Exception{
        ResultSet rs = criarConsulta("SELECT * FROM paginainicialfeed", con);
        if(rs.next()){
            return montarPaginaInicial(rs);
        }else{
            return incluirPaginaInicial(new PaginaInicialFeedVO());
        }
    }
    
    @Override
    public PaginaInicialFeedVO montarPaginaInicial(ResultSet rs) throws Exception{
        PaginaInicialFeedVO capa = new PaginaInicialFeedVO();
        capa.setCodigo(rs.getInt("codigo"));
        capa.setTituloGrande(rs.getString("titulogrande"));
        capa.setMensagemCapa(rs.getString("mensagemcapa"));
        capa.setLinkImagem(rs.getString("linkimagem"));
        capa.setLinkVideo(rs.getString("linkvideo"));
        capa.setTextoBalaoGrande(rs.getString("textobalaogrande"));
        return capa;
    } 
}

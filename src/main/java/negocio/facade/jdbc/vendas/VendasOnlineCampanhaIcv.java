package negocio.facade.jdbc.vendas;

import negocio.comuns.basico.enumerador.TelaVendasOnLineEnum;
import negocio.comuns.utilitarias.Calendario;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisValidacao;
import negocio.facade.jdbc.arquitetura.SuperEntidade;
import negocio.interfaces.vendas.VendasOnlineCampanhaIcvInterfaceFacade;
import servicos.vendasonline.dto.VendaDTO;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.util.Date;

/**
 * Created by ulisses on 23/01/2023.
 */
public class VendasOnlineCampanhaIcv extends SuperEntidade implements VendasOnlineCampanhaIcvInterfaceFacade {




    public VendasOnlineCampanhaIcv() throws Exception {
        super();
    }

    public VendasOnlineCampanhaIcv(Connection con) throws Exception {
        super(con);
    }

    public void incluir(VendasOnlineCampanhaIcvVO vendasOnlineCampanhaIcvVO)throws Exception{
        vendasOnlineCampanhaIcvVO.validarDados();
        vendasOnlineCampanhaIcvVO.setDataInicioOperacao(Calendario.hoje());
        try(PreparedStatement pst = con.prepareStatement("insert into vendasOnlineCampanhaIcv(tela, link,empresa,evento,pessoa,dataInicioOperacao,ip,usuario) values(?,?,?,?,?,?,?,?)", PreparedStatement.RETURN_GENERATED_KEYS)){
            int i = 0;
            pst.setString(++i,vendasOnlineCampanhaIcvVO.getTela().name());
            pst.setString(++i,vendasOnlineCampanhaIcvVO.getLink());
            pst.setInt(++i,vendasOnlineCampanhaIcvVO.getEmpresa().getCodigo());
            resolveIntegerNull(pst,++i,vendasOnlineCampanhaIcvVO.getEvento().getCodigo());
            resolveIntegerNull(pst,++i,vendasOnlineCampanhaIcvVO.getPessoa().getCodigo());
            pst.setTimestamp(++i, Uteis.getDataJDBCTimestamp(vendasOnlineCampanhaIcvVO.getDataInicioOperacao()));
            resolveStringNull(pst,++i,vendasOnlineCampanhaIcvVO.getIp());
            resolveIntegerNull(pst,++i,vendasOnlineCampanhaIcvVO.getUsuarioVO().getCodigo());
            pst.execute();
            ResultSet rs = pst.getGeneratedKeys();
            if (rs.next()) {
                vendasOnlineCampanhaIcvVO.setCodigo(rs.getInt("codigo"));
            }
            vendasOnlineCampanhaIcvVO.setNovoObj(false);
        }
    }

    private void incluirRegistroCompleto(VendasOnlineCampanhaIcvVO vendasOnlineCampanhaIcvVO)throws Exception{
        vendasOnlineCampanhaIcvVO.validarDados();
        vendasOnlineCampanhaIcvVO.setDataInicioOperacao(Calendario.hoje());
        vendasOnlineCampanhaIcvVO.setDataFimOperacao(vendasOnlineCampanhaIcvVO.getDataInicioOperacao());
        try(PreparedStatement pst = con.prepareStatement("insert into vendasOnlineCampanhaIcv(tela, link,empresa,evento,pessoa,dataInicioOperacao,ip,usuario,contrato,vendaAvulsa,dataFimOperacao) values(?,?,?,?,?,?,?,?,?,?,?)", PreparedStatement.RETURN_GENERATED_KEYS)){
            int i = 0;
            pst.setString(++i,vendasOnlineCampanhaIcvVO.getTela().name());
            pst.setString(++i,vendasOnlineCampanhaIcvVO.getLink());
            pst.setInt(++i,vendasOnlineCampanhaIcvVO.getEmpresa().getCodigo());
            resolveIntegerNull(pst,++i,vendasOnlineCampanhaIcvVO.getEvento().getCodigo());
            resolveIntegerNull(pst,++i,vendasOnlineCampanhaIcvVO.getPessoa().getCodigo());
            pst.setTimestamp(++i, Uteis.getDataJDBCTimestamp(vendasOnlineCampanhaIcvVO.getDataInicioOperacao()));
            resolveStringNull(pst,++i,vendasOnlineCampanhaIcvVO.getIp());
            resolveIntegerNull(pst,++i,vendasOnlineCampanhaIcvVO.getUsuarioVO().getCodigo());
            resolveIntegerNull(pst,++i,vendasOnlineCampanhaIcvVO.getContrato().getCodigo());
            resolveIntegerNull(pst,++i,vendasOnlineCampanhaIcvVO.getVendaAvulsa());
            pst.setTimestamp(++i, Uteis.getDataJDBCTimestamp(vendasOnlineCampanhaIcvVO.getDataFimOperacao()));
            pst.execute();
            ResultSet rs = pst.getGeneratedKeys();
            if (rs.next()) {
                vendasOnlineCampanhaIcvVO.setCodigo(rs.getInt("codigo"));
            }
            vendasOnlineCampanhaIcvVO.setNovoObj(false);
        }
    }

    private VendasOnlineCampanhaIcvVO consultarRegistroSemConclusao(VendasOnlineCampanhaIcvVO vendasOnlineCampanhaIcvVO)throws Exception{
        /*
            Esta consulta é necessário para o seguinte cenário:  A pessoa acessa o link de produto e conclui a compra, dessa forma o codigo de registro fica na sessão do browser,
                                                                 e se o cliente fizer uma nova compra de outro produto, o código da venda anterior é enviado, e ao fazer uma segunda tentativa,
                                                                 o sistema verifica que o código já foi concluído, então incluir um novo registro e atualiza a informação do cliente,
                                                                  mas ao fazer o pagamento dá saldo insuficiente e o registro fica sem conclusão,
                                                                  e ao tentar concluir pela terceira vez, devemos pegar o último registro gerado para a tela/link.
         */
        PreparedStatement pst = con.prepareStatement("select * from vendasOnlineCampanhaIcv where dataFimOperacao is null and pessoa=? and tela=? and link=? and dataInicioOperacao::date =?");
        int i = 0;
        pst.setInt(++i,vendasOnlineCampanhaIcvVO.getPessoa().getCodigo());
        pst.setString(++i,vendasOnlineCampanhaIcvVO.getTela().name());
        pst.setString(++i,vendasOnlineCampanhaIcvVO.getLink());
        pst.setDate(++i,Uteis.getDataJDBC(Calendario.getDataComHoraZerada(vendasOnlineCampanhaIcvVO.getDataInicioOperacao())));
        ResultSet rs = pst.executeQuery();
        if (rs.next()){
            return montarDadosBasico(rs);
        }
        return null;
    }

    public void alterarInfoVenda(Integer codigoVendasOnlineCampanhaIcv, Integer contrato, Integer vendaAvulsa, Integer pessoa)throws Exception{
        if (!UteisValidacao.emptyNumber(codigoVendasOnlineCampanhaIcv)){
            VendasOnlineCampanhaIcvVO vendasOnlineCampanhaIcvVO = consultar(codigoVendasOnlineCampanhaIcv);
            if ((vendasOnlineCampanhaIcvVO != null) && (vendasOnlineCampanhaIcvVO.getDataFimOperacao() != null)){
                /* o código de registro da página já foi concluído, dessa forma vamos gerar novo registro.
                   Exemplo: A pessoa acessa o link de produto e conclui a compra, dessa forma o codigo de registro fica na sessão do browser,
                            e se o cliente fizer uma nova compra de outro produto, o código da venda anterior que é enviado.
                */
                vendasOnlineCampanhaIcvVO.getPessoa().setCodigo(pessoa);
                VendasOnlineCampanhaIcvVO registroSemConclusao = consultarRegistroSemConclusao(vendasOnlineCampanhaIcvVO);
                if (registroSemConclusao != null){
                    alterarInformacoesVenda(registroSemConclusao.getCodigo(),contrato,vendaAvulsa);
                }else{
                    vendasOnlineCampanhaIcvVO.getContrato().setCodigo(contrato);
                    vendasOnlineCampanhaIcvVO.setVendaAvulsa(vendaAvulsa);
                    incluirRegistroCompleto(vendasOnlineCampanhaIcvVO);
                }
            }else{
                alterarInformacoesVenda(codigoVendasOnlineCampanhaIcv,contrato,vendaAvulsa);
            }
        }
    }

    private void alterarInformacoesVenda(Integer codigoVendasOnlineCampanhaIcv, Integer contrato, Integer vendaAvulsa)throws Exception{
        // concluir a operação.
        try(PreparedStatement pst = con.prepareStatement("update vendasOnlineCampanhaIcv set  contrato=?, vendaAvulsa=?,dataFimOperacao=? where codigo = ? ")){
            int i = 0;
            resolveIntegerNull(pst,++i,contrato);
            resolveIntegerNull(pst,++i,vendaAvulsa);
            pst.setTimestamp(++i, Uteis.getDataJDBCTimestamp(Calendario.hoje()));
            pst.setInt(++i,codigoVendasOnlineCampanhaIcv);
            pst.execute();
        }
    }

    public void alterarInfoPessoa(VendaDTO vendaDTO, Integer codigoVendasOnlineCampanhaIcv, Integer pessoa, Date dataConclusao)throws Exception{
        if (!UteisValidacao.emptyNumber(codigoVendasOnlineCampanhaIcv)){
            VendasOnlineCampanhaIcvVO vendasOnlineCampanhaIcvVO = consultar(codigoVendasOnlineCampanhaIcv);
            if ((vendasOnlineCampanhaIcvVO != null) && (vendasOnlineCampanhaIcvVO.getDataFimOperacao() != null)){
                /* o código de registro da página já foi concluído, dessa forma vamos gerar novo registro.
                   Exemplo: A pessoa acessa o link de produto e conclui a compra, dessa forma o codigo de registro fica na sessão do browser,
                            e se o cliente fizer uma nova compra de outro produto, o código da venda anterior que é enviado.
                */
                vendasOnlineCampanhaIcvVO.getPessoa().setCodigo(pessoa);
                VendasOnlineCampanhaIcvVO registroSemConclusao = consultarRegistroSemConclusao(vendasOnlineCampanhaIcvVO);
                if (registroSemConclusao == null){
                    if (dataConclusao != null){
                        incluirRegistroCompleto(vendasOnlineCampanhaIcvVO);
                    }else{
                        incluir(vendasOnlineCampanhaIcvVO);
                    }
                    vendaDTO.setCodigoRegistroAcessoPagina(vendasOnlineCampanhaIcvVO.getCodigo());
                }else{
                    vendaDTO.setCodigoRegistroAcessoPagina(registroSemConclusao.getCodigo());
                }
            }else{
                alterarInformacoesPessoa(codigoVendasOnlineCampanhaIcv,pessoa, dataConclusao);
            }

        }
    }

    private void alterarInformacoesPessoa(Integer codigoVendasOnlineCampanhaIcv, Integer pessoa, Date dataConclusao)throws Exception{
        try(PreparedStatement pst = con.prepareStatement("update vendasOnlineCampanhaIcv set pessoa=?, dataFimOperacao=? where codigo = ? ")){
            int i = 0;
            resolveIntegerNull(pst,++i,pessoa);
            resolveTimestampNull(pst,++i,dataConclusao);
            pst.setInt(++i,codigoVendasOnlineCampanhaIcv);
            pst.execute();
        }
    }

    private VendasOnlineCampanhaIcvVO montarDadosBasico(ResultSet rs)throws Exception{
        VendasOnlineCampanhaIcvVO obj = new VendasOnlineCampanhaIcvVO();
        obj.setCodigo(rs.getInt("codigo"));
        obj.setTela(TelaVendasOnLineEnum.valueOf(rs.getString("tela")));
        obj.setLink(rs.getString("link"));
        obj.getEmpresa().setCodigo(rs.getInt("empresa"));
        obj.getEvento().setCodigo(rs.getInt("evento"));
        obj.getPessoa().setCodigo(rs.getInt("pessoa"));
        obj.getContrato().setCodigo(rs.getInt("contrato"));
        obj.getUsuarioVO().setCodigo(rs.getInt("usuario"));
        obj.setVendaAvulsa(rs.getInt("vendaAvulsa"));
        obj.setIp(rs.getString("ip"));
        obj.setDataInicioOperacao(rs.getTimestamp("dataInicioOperacao"));
        obj.setDataFimOperacao(rs.getTimestamp("dataFimOperacao"));
        return obj;
    }

    public VendasOnlineCampanhaIcvVO consultar(Integer codigo)throws Exception{
        ResultSet rs = con.createStatement().executeQuery("select * from vendasOnlineCampanhaIcv where codigo = " + codigo);
        VendasOnlineCampanhaIcvVO obj = null;
        if (rs.next()){
           obj = montarDadosBasico(rs);
        }
        return obj;
    }

}

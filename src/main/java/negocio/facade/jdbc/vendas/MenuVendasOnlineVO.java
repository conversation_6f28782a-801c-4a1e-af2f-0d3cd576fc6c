package negocio.facade.jdbc.vendas;

import negocio.comuns.utilitarias.Uteis;

public class MenuVendasOnlineVO {
    private Integer codigo;
    private Integer codVendasOnlineConfig = 0;
    private String menuPersonalizado01 = "";
    private String menuPersonalizado02 = "";
    private String menuPersonalizado03 = "";
    private String linkMenuPersonalizado01 = "";
    private String linkMenuPersonalizado02 = "";
    private String linkMenuPersonalizado03 = "";
    private String fotoKey = "";

    public Integer getCodigo() {
        return codigo;
    }

    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public Integer getCodVendasOnlineConfig() {
        return codVendasOnlineConfig;
    }

    public void setCodVendasOnlineConfig(Integer codVendasOnlineConfig) {
        this.codVendasOnlineConfig = codVendasOnlineConfig;
    }

    public String getMenuPersonalizado01() {
        return menuPersonalizado01;
    }

    public void setMenuPersonalizado01(String menuPersonalizado01) {
        this.menuPersonalizado01 = menuPersonalizado01;
    }

    public String getMenuPersonalizado02() {
        return menuPersonalizado02;
    }

    public void setMenuPersonalizado02(String menuPersonalizado02) {
        this.menuPersonalizado02 = menuPersonalizado02;
    }

    public String getMenuPersonalizado03() {
        return menuPersonalizado03;
    }

    public void setMenuPersonalizado03(String menuPersonalizado03) {
        this.menuPersonalizado03 = menuPersonalizado03;
    }

    public String getLinkMenuPersonalizado01() {
        return linkMenuPersonalizado01;
    }

    public void setLinkMenuPersonalizado01(String linkMenuPersonalizado01) {
        this.linkMenuPersonalizado01 = linkMenuPersonalizado01;
    }

    public String getLinkMenuPersonalizado02() {
        return linkMenuPersonalizado02;
    }

    public void setLinkMenuPersonalizado02(String linkMenuPersonalizado02) {
        this.linkMenuPersonalizado02 = linkMenuPersonalizado02;
    }

    public String getLinkMenuPersonalizado03() {
        return linkMenuPersonalizado03;
    }

    public void setLinkMenuPersonalizado03(String linkMenuPersonalizado03) {
        this.linkMenuPersonalizado03 = linkMenuPersonalizado03;
    }

    public String getFotoKey() {
        return fotoKey;
    }

    public void setFotoKey(String fotoKey) {
        this.fotoKey = fotoKey;
    }

    public String getUrlFoto() {
        try {
            return Uteis.getPaintFotoDaNuvem(getFotoKey());
        } catch (Exception e) {
            return "";
        }
    }
}

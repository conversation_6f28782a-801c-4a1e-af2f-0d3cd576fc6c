package negocio.facade.jdbc.vendas;

public class ContatoRodapeVendasOnlineVO {
    private Integer codigo;
    private Integer codVendasOnlineConfig = 0;
    private String horarioSegundaSexta = "";
    private String horarioSabado = "";
    private String horarioDomingoFeriado = "";
    private String linkWhatsapp = "";
    private String linkInstagram = "";
    private String linkFacebook = "";
    private String linkTwitter = "";

    public Integer getCodigo() {
        return codigo;
    }

    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public Integer getCodVendasOnlineConfig() {
        return codVendasOnlineConfig;
    }

    public void setCodVendasOnlineConfig(Integer codVendasOnlineConfig) {
        this.codVendasOnlineConfig = codVendasOnlineConfig;
    }

    public String getHorarioSegundaSexta() {
        return horarioSegundaSexta;
    }

    public void setHorarioSegundaSexta(String horarioSegundaSexta) {
        this.horarioSegundaSexta = horarioSegundaSexta;
    }

    public String getHorarioSabado() {
        return horarioSabado;
    }

    public void setHorarioSabado(String horarioSabado) {
        this.horarioSabado = horarioSabado;
    }

    public String getHorarioDomingoFeriado() {
        return horarioDomingoFeriado;
    }

    public void setHorarioDomingoFeriado(String horarioDomingoFeriado) {
        this.horarioDomingoFeriado = horarioDomingoFeriado;
    }

    public String getLinkWhatsapp() {
        return linkWhatsapp;
    }

    public void setLinkWhatsapp(String linkWhatsapp) {
        this.linkWhatsapp = linkWhatsapp;
    }

    public String getLinkInstagram() {
        return linkInstagram;
    }

    public void setLinkInstagram(String linkInstagram) {
        this.linkInstagram = linkInstagram;
    }

    public String getLinkFacebook() {
        return linkFacebook;
    }

    public void setLinkFacebook(String linkFacebook) {
        this.linkFacebook = linkFacebook;
    }

    public String getLinkTwitter() {
        return linkTwitter;
    }

    public void setLinkTwitter(String linkTwitter) {
        this.linkTwitter = linkTwitter;
    }
}

package negocio.facade.jdbc.vendas;

import negocio.comuns.utilitarias.Uteis;

public class ImagensAcademiaVendasVO{
    private Integer codigo;
    private String fotoKey;
    private Integer empresa;

    public ImagensAcademiaVendasVO() {

    }
    public ImagensAcademiaVendasVO(Integer codigo, String fotoKey, Integer empresa) {
        this.codigo = codigo;
        this.fotoKey = fotoKey;
        this.empresa = empresa;
    }

    public Integer getCodigo() {
        return codigo;
    }

    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public String getFotoKey() {
        return fotoKey;
    }

    public void setFotoKey(String fotoKey) {
        this.fotoKey = fotoKey;
    }

    public Integer getEmpresa() {
        return empresa;
    }

    public void setEmpresa(Integer empresa) {
        this.empresa = empresa;
    }

    public String getUrlFoto() {
        try {
            return Uteis.getPaintFotoDaNuvem(getFotoKey());
        } catch (Exception e) {
            return "";
        }
    }
}

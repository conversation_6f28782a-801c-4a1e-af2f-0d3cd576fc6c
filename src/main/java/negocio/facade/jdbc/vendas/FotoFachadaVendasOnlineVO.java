package negocio.facade.jdbc.vendas;

import negocio.comuns.utilitarias.Uteis;

public class FotoFachadaVendasOnlineVO {
    private Integer codigo;
    private Integer codVendasOnlineConfig = 0;
    private String fotoKey = "";

    public Integer getCodigo() {
        return codigo;
    }

    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public Integer getCodVendasOnlineConfig() {
        return codVendasOnlineConfig;
    }

    public void setCodVendasOnlineConfig(Integer codVendasOnlineConfig) {
        this.codVendasOnlineConfig = codVendasOnlineConfig;
    }

    public String getFotoKey() {
        return fotoKey;
    }

    public void setFotoKey(String fotoKey) {
        this.fotoKey = fotoKey;
    }
    public String getUrlFoto() {
        try {
            return Uteis.getPaintFotoDaNuvem(getFotoKey());
        } catch (Exception e) {
            return "";
        }
    }
}

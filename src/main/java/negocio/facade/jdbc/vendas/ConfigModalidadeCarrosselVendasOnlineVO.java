package negocio.facade.jdbc.vendas;

public class ConfigModalidadeCarrosselVendasOnlineVO {

    private Integer codigo;
    private Integer codVendasOnlineConfig = 0;
    private String tituloModalidadeVendas = "";
    private String descricaoModalidadeVendas = "";

    public ConfigModalidadeCarrosselVendasOnlineVO() {
    }

    public ConfigModalidadeCarrosselVendasOnlineVO(Integer codigo, String tituloModalidadeVendas, String descricaoModalidadeVendas) {
        this.codigo = codigo;
        this.tituloModalidadeVendas = tituloModalidadeVendas;
        this.descricaoModalidadeVendas = descricaoModalidadeVendas;
    }

    public Integer getCodigo() {
        return codigo;
    }

    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public Integer getCodVendasOnlineConfig() {
        return codVendasOnlineConfig;
    }

    public void setCodVendasOnlineConfig(Integer codVendasOnlineConfig) {
        this.codVendasOnlineConfig = codVendasOnlineConfig;
    }

    public String getTituloModalidadeVendas() {
        return tituloModalidadeVendas;
    }

    public void setTituloModalidadeVendas(String tituloModalidadeVendas) {
        this.tituloModalidadeVendas = tituloModalidadeVendas;
    }

    public String getDescricaoModalidadeVendas() {
        return descricaoModalidadeVendas;
    }

    public void setDescricaoModalidadeVendas(String descricaoModalidadeVendas) {
        this.descricaoModalidadeVendas = descricaoModalidadeVendas;
    }
}

package negocio.facade.jdbc.vendas;

import negocio.comuns.utilitarias.Uteis;

public class CaptacaoLeadsVendasOnlineVO {
    private Integer codigo;
    private Integer codVendasOnlineConfig = 0;
    private String tituloCabecalho = "";
    private String fotoKey = "";
    private Boolean usaIntegracaoCRM = null;
    private Integer responsavelPadrao = null;

    public Integer getCodigo() {
        return codigo;
    }

    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public Integer getCodVendasOnlineConfig() {
        return codVendasOnlineConfig;
    }

    public void setCodVendasOnlineConfig(Integer codVendasOnlineConfig) {
        this.codVendasOnlineConfig = codVendasOnlineConfig;
    }

    public String getTituloCabecalho() {
        return tituloCabecalho;
    }

    public void setTituloCabecalho(String tituloCabecalho) {
        this.tituloCabecalho = tituloCabecalho;
    }

    public String getFotoKey() {
        return fotoKey;
    }

    public void setFotoKey(String fotoKey) {
        this.fotoKey = fotoKey;
    }

    public String getUrlFoto() {
        try {
            return Uteis.getPaintFotoDaNuvem(getFotoKey());
        } catch (Exception e) {
            return "";
        }
    }

    public Boolean getUsaIntegracaoCRM() {
        return usaIntegracaoCRM;
    }

    public void setUsaIntegracaoCRM(Boolean usaIntegracaoCRM) {
        this.usaIntegracaoCRM = usaIntegracaoCRM;
    }

    public Integer getResponsavelPadrao() {
        return responsavelPadrao;
    }

    public void setResponsavelPadrao(Integer responsavelPadrao) {
        this.responsavelPadrao = responsavelPadrao;
    }
}

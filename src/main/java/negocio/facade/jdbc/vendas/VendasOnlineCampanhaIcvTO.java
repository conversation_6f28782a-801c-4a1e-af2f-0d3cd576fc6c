package negocio.facade.jdbc.vendas;

import negocio.comuns.arquitetura.SuperTO;

import java.util.Date;

/**
 * Created by ulisses on 31/01/2023.
 */
public class VendasOnlineCampanhaIcvTO extends SuperTO{

    private Integer codigo;
    private String tela;
    private String link;
    private Integer empresa;
    private Integer evento;
    private Integer pessoa;
    private Integer contrato;
    private Integer vendaAvulsa;
    private Integer usuario;
    private String ip;
    private Date dataInicioOperacao;
    private Date dataFimOperacao;

    public VendasOnlineCampanhaIcvTO(){}

    public VendasOnlineCampanhaIcvTO(VendasOnlineCampanhaIcvVO obj){
        this.codigo = obj.getCodigo();
        this.tela = obj.getTela().toString();
        this.link = obj.getLink();
        this.empresa = obj.getEmpresa().getCodigo();
        this.evento = obj.getEvento().getCodigo();
        this.pessoa = obj.getPessoa().getCodigo();
        this.contrato = obj.getContrato().getCodigo();
        this.vendaAvulsa = obj.getVendaAvulsa();
        this.usuario = obj.getUsuarioVO().getCodigo();
        this.ip = obj.getIp();
        this.dataInicioOperacao = obj.getDataInicioOperacao();
        this.dataFimOperacao = obj.getDataFimOperacao();
    }


    public Integer getCodigo() {
        return codigo;
    }

    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public String getTela() {
        return tela;
    }

    public void setTela(String tela) {
        this.tela = tela;
    }

    public String getLink() {
        return link;
    }

    public void setLink(String link) {
        this.link = link;
    }

    public Integer getEmpresa() {
        return empresa;
    }

    public void setEmpresa(Integer empresa) {
        this.empresa = empresa;
    }

    public Integer getEvento() {
        return evento;
    }

    public void setEvento(Integer evento) {
        this.evento = evento;
    }

    public Integer getPessoa() {
        return pessoa;
    }

    public void setPessoa(Integer pessoa) {
        this.pessoa = pessoa;
    }

    public Integer getContrato() {
        return contrato;
    }

    public void setContrato(Integer contrato) {
        this.contrato = contrato;
    }

    public Integer getVendaAvulsa() {
        return vendaAvulsa;
    }

    public void setVendaAvulsa(Integer vendaAvulsa) {
        this.vendaAvulsa = vendaAvulsa;
    }

    public Integer getUsuario() {
        return usuario;
    }

    public void setUsuario(Integer usuario) {
        this.usuario = usuario;
    }

    public String getIp() {
        return ip;
    }

    public void setIp(String ip) {
        this.ip = ip;
    }

    public Date getDataInicioOperacao() {
        return dataInicioOperacao;
    }

    public void setDataInicioOperacao(Date dataInicioOperacao) {
        this.dataInicioOperacao = dataInicioOperacao;
    }

    public Date getDataFimOperacao() {
        return dataFimOperacao;
    }

    public void setDataFimOperacao(Date dataFimOperacao) {
        this.dataFimOperacao = dataFimOperacao;
    }
}

package negocio.facade.jdbc.vendas;

import negocio.comuns.arquitetura.SuperVO;
import negocio.comuns.basico.EmpresaVO;
import negocio.comuns.financeiro.ConvenioCobrancaVO;
import negocio.comuns.financeiro.enumerador.TipoConvenioCobrancaEnum;
import negocio.comuns.utilitarias.ConsistirException;
import negocio.comuns.utilitarias.UteisValidacao;
import servicos.impl.stone.xml.authorization.request.InstalmentTypeInstlmtTp;

/**
 * Created with IntelliJ IDEA.
 * User: <PERSON><PERSON>
 * Date: 13/08/2020
 */
public class VendasOnlineConvenioTentativaVO extends SuperVO {

    private Integer codigo;
    private ConvenioCobrancaVO convenioCobrancaVO;
    private EmpresaVO empresaVO;
    private Integer ordem;
    private String tipoParcelamentoStone;

    public VendasOnlineConvenioTentativaVO() {
        super();
    }

    public VendasOnlineConvenioTentativaVO(ConvenioCobrancaVO convenioCobrancaVO, EmpresaVO empresaVO) {
        super();
        this.convenioCobrancaVO = convenioCobrancaVO;
        this.empresaVO = empresaVO;
    }

    public static void validarDados(VendasOnlineConvenioTentativaVO obj) throws ConsistirException {
        if (!obj.getValidarDados()) {
            return;
        }
        if (UteisValidacao.emptyNumber(obj.getConvenioCobrancaVO().getCodigo())) {
            throw new ConsistirException("Selecione um convênio de cobrança.");
        }
        if (UteisValidacao.emptyNumber(obj.getEmpresaVO().getCodigo())) {
            throw new ConsistirException("Selecione a empresa.");
        }
        if (UteisValidacao.emptyNumber(obj.getOrdem())) {
            throw new ConsistirException("A ordem não pode ser 0.");
        }
        if (obj.getConvenioCobrancaVO().isStone() && UteisValidacao.emptyString(obj.getTipoParcelamentoStone())) {
            throw new ConsistirException("Selecione o tipo de parcelamento Stone.");
        }
    }

    @Override
    public Integer getCodigo() {
        if (codigo == null) {
            codigo = 0;
        }
        return codigo;
    }

    @Override
    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public ConvenioCobrancaVO getConvenioCobrancaVO() {
        if (convenioCobrancaVO == null) {
            convenioCobrancaVO = new ConvenioCobrancaVO();
        }
        return convenioCobrancaVO;
    }

    public void setConvenioCobrancaVO(ConvenioCobrancaVO convenioCobrancaVO) {
        this.convenioCobrancaVO = convenioCobrancaVO;
    }

    public EmpresaVO getEmpresaVO() {
        if (empresaVO == null) {
            empresaVO = new EmpresaVO();
        }
        return empresaVO;
    }

    public void setEmpresaVO(EmpresaVO empresaVO) {
        this.empresaVO = empresaVO;
    }

    public Integer getOrdem() {
        if (ordem == null) {
            ordem = 0;
        }
        return ordem;
    }

    public void setOrdem(Integer ordem) {
        this.ordem = ordem;
    }

    public String getTipoParcelamentoStoneApresentar() {
        if (getTipoParcelamentoStone().equalsIgnoreCase(InstalmentTypeInstlmtTp.COM_JUROS_EMISSOR.getValor())) {
            return "PARCELAMENTO EMISSOR";
        } else if (getTipoParcelamentoStone().equalsIgnoreCase(InstalmentTypeInstlmtTp.SEM_JUROS_LOJISTA.getValor())) {
            return "PARCELAMENTO LOJISTA";
        } else {
            return "";
        }
    }

    public String getTipoParcelamentoStone() {
        if (tipoParcelamentoStone == null) {
            tipoParcelamentoStone = "";
        }
        return tipoParcelamentoStone;
    }

    public void setTipoParcelamentoStone(String tipoParcelamentoStone) {
        this.tipoParcelamentoStone = tipoParcelamentoStone;
    }

    public String getConvenioCobrancaApresentar() {
        if (getConvenioCobrancaVO().getTipo().equals(TipoConvenioCobrancaEnum.DCC_STONE_ONLINE) &&
                !UteisValidacao.emptyString(getTipoParcelamentoStone())) {
            if (getTipoParcelamentoStone().equalsIgnoreCase(InstalmentTypeInstlmtTp.COM_JUROS_EMISSOR.getValor())) {
                return getConvenioCobrancaVO().getDescricao() + " - PARCELAMENTO EMISSOR";
            } else if (getTipoParcelamentoStone().equalsIgnoreCase(InstalmentTypeInstlmtTp.SEM_JUROS_LOJISTA.getValor())) {
                return getConvenioCobrancaVO().getDescricao() + " - PARCELAMENTO LOJISTA";
            } else {
                return getConvenioCobrancaVO().getDescricao();
            }
        } else {
            return getConvenioCobrancaVO().getDescricao();
        }
    }
}

package negocio.facade.jdbc.vendas;

import negocio.comuns.utilitarias.UteisValidacao;

import java.util.Arrays;
import java.util.HashMap;
import java.util.List;

public class TurmaEHorarioVendasOnlineVO {

    private static HashMap<String, String> DIAS_SEMANA = new HashMap<>();

    private Integer codigoTurma;
    private Integer codigoHorario;
    private Integer codigoModalidade;
    private String descricao;
    private String horarioInicial;
    private String horarioFinal;
    private String diaSemana;
    private String nomeModalidade;
    private Double valorModalidade;
    private String ambiente;

    static {
        List<String> diasReduzido = Arrays.asList("SG", "TR", "QA", "QI", "SX", "SB", "DM");
        List<String> diasExtenso = Arrays.asList("SEGUNDAS", "TERÇAS", "QUARTAS", "QUINTAS", "SEXTAS", "SÁBADOS",
                "DOMINGOS");

        for (int i = 0; i < diasExtenso.size(); i++) {
            DIAS_SEMANA.put(diasReduzido.get(i), diasExtenso.get(i));
        }
    }

    public String getDescricaoCompleta() {
        String ambiente = UteisValidacao.emptyString(getAmbiente()) ? "" : " (" + getAmbiente() + ") ";
        return getCodigoTurma() + " - " + (descricao != null ? descricao.trim() : " *** ")  + ambiente;
    }

    public void setCodigoTurma(Integer codigoTurma) {
        this.codigoTurma = codigoTurma;
    }

    public Integer getCodigoTurma() {
        return codigoTurma;
    }

    public Integer getCodigoHorario() {
        return codigoHorario;
    }

    public void setCodigoHorario(Integer codigoHorario) {
        this.codigoHorario = codigoHorario;
    }

    public void setCodigoModalidade(Integer codigoModalidade) {
        this.codigoModalidade = codigoModalidade;
    }

    public Integer getCodigoModalidade() {
        return codigoModalidade;
    }

    public String getDescricao() {
        return descricao;
    }

    public void setDescricao(String descricao) {
        this.descricao = descricao;
    }

    public String getHorarioInicial() {
        return horarioInicial;
    }

    public void setHorarioFinal(String horarioFinal) {
        this.horarioFinal = horarioFinal;
    }

    public String getHorarioFinal() {
        return horarioFinal;
    }

    public void setHorarioInicial(String horarioInicial) {
        this.horarioInicial = horarioInicial;
    }

    public String getDiaSemana() {
        return diaSemana;
    }

    public void setDiaSemana(String diaSemana) {
        this.diaSemana = diaSemana;
    }

    public String getNomeModalidade() {
        return nomeModalidade;
    }

    public void setNomeModalidade(String nomeModalidade) {
        this.nomeModalidade = nomeModalidade;
    }

    public Double getValorModalidade() {
        return valorModalidade;
    }

    public void setValorModalidade(Double valorModalidade) {
        this.valorModalidade = valorModalidade;
    }

    public String getAmbiente() {
        return ambiente;
    }

    public void setAmbiente(String ambiente) {
        this.ambiente = ambiente;
    }

}

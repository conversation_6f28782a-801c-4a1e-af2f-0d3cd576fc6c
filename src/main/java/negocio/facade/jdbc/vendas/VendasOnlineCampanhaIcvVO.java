package negocio.facade.jdbc.vendas;

import negocio.comuns.arquitetura.SuperVO;
import negocio.comuns.arquitetura.UsuarioVO;
import negocio.comuns.basico.EmpresaVO;
import negocio.comuns.basico.PessoaVO;
import negocio.comuns.basico.enumerador.TelaVendasOnLineEnum;
import negocio.comuns.contrato.ContratoVO;
import negocio.comuns.crm.EventoVO;
import negocio.comuns.utilitarias.ConsistirException;
import negocio.comuns.utilitarias.UteisValidacao;
import org.json.JSONObject;
import servicos.vendasonline.dto.VendaDTO;

import java.util.Date;

/**
 * Created by ulisses on 23/01/2023.
 */
public class VendasOnlineCampanhaIcvVO extends SuperVO {

    private Integer codigo;
    private TelaVendasOnLineEnum tela;
    private String link;
    private EmpresaVO empresa;
    private EventoVO evento;
    private PessoaVO pessoa;
    private ContratoVO contrato;
    private UsuarioVO usuarioVO;
    private Integer vendaAvulsa;
    private String ip;
    private Date dataInicioOperacao;
    private Date dataFimOperacao;

    public VendasOnlineCampanhaIcvVO(){}

    public VendasOnlineCampanhaIcvVO(JSONObject jsonObject){
        this.tela =  TelaVendasOnLineEnum.valueOf(jsonObject.optString("tela"));
        this.link = jsonObject.optString("link");
        getEmpresa().setCodigo(jsonObject.optInt("empresa"));
        getEvento().setCodigo(jsonObject.optInt("evento"));
        this.ip = jsonObject.optString("ip");
        getUsuarioVO().setCodigo(jsonObject.optInt("usuario"));
    }

    public VendasOnlineCampanhaIcvVO(Integer codigo, Integer vendaAvulsa, Integer contrato, Integer pessoa){
        this.codigo = codigo;
        this.vendaAvulsa = vendaAvulsa;
        getContrato().setCodigo(contrato);
        getPessoa().setCodigo(pessoa);
    }


    @Override
    public Integer getCodigo() {
        return codigo;
    }

    @Override
    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public String getLink() {
        return link;
    }

    public void setLink(String link) {
        this.link = link;
    }

    public EmpresaVO getEmpresa() {
        if (empresa == null){
            this.empresa = new EmpresaVO();
        }
        return empresa;
    }

    public void setEmpresa(EmpresaVO empresa) {
        this.empresa = empresa;
    }

    public EventoVO getEvento() {
        if (evento == null){
            this.evento = new EventoVO();
        }
        return evento;
    }


    public void setEvento(EventoVO evento) {
        this.evento = evento;
    }

    public PessoaVO getPessoa() {
        if (pessoa == null){
            pessoa = new PessoaVO();
        }
        return pessoa;
    }

    public void setPessoa(PessoaVO pessoa) {
        this.pessoa = pessoa;
    }

    public ContratoVO getContrato() {
        if (contrato == null){
            this.contrato = new ContratoVO();
        }
        return contrato;
    }

    public TelaVendasOnLineEnum getTela() {
        return tela;
    }

    public void setTela(TelaVendasOnLineEnum tela) {
        this.tela = tela;
    }

    public void setContrato(ContratoVO contrato) {
        this.contrato = contrato;
    }

    public Integer getVendaAvulsa() {
        return vendaAvulsa;
    }

    public void setVendaAvulsa(Integer vendaAvulsa) {
        this.vendaAvulsa = vendaAvulsa;
    }

    public Date getDataInicioOperacao() {
        return dataInicioOperacao;
    }

    public void setDataInicioOperacao(Date dataInicioOperacao) {
        this.dataInicioOperacao = dataInicioOperacao;
    }

    public Date getDataFimOperacao() {
        return dataFimOperacao;
    }

    public void setDataFimOperacao(Date dataFimOperacao) {
        this.dataFimOperacao = dataFimOperacao;
    }

    @Override
    public UsuarioVO getUsuarioVO() {
        if (usuarioVO == null){
            this.usuarioVO = new UsuarioVO();
        }
        return usuarioVO;
    }

    @Override
    public void setUsuarioVO(UsuarioVO usuarioVO) {
        this.usuarioVO = usuarioVO;
    }

    public String getIp() {
        return ip;
    }

    public void setIp(String ip) {
        this.ip = ip;
    }

    public void validarDados()throws Exception{
        if (this.tela == null) {
            throw new ConsistirException("O campo Tela não informado ou inválido.");
        }
        if (UteisValidacao.emptyString(link)){
            throw new ConsistirException("O campo Link  deve ser informado.");
        }
        if ((this.empresa == null) || (this.empresa.getCodigo() == 0)) {
            throw new ConsistirException("O campo empresa  deve ser informado.");
        }
    }

}

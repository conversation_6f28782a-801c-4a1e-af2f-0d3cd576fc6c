package negocio.facade.jdbc.vendas;

import negocio.comuns.basico.enumerador.TipoLinkCampanhaEnum;
import negocio.comuns.utilitarias.Calendario;
import negocio.comuns.utilitarias.ConsistirException;
import negocio.comuns.utilitarias.Uteis;
import negocio.facade.jdbc.arquitetura.SuperEntidade;
import negocio.interfaces.vendas.VendasOnlineCampanhaInterfaceFacade;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * Created by ulisses on 23/11/2022.
 */
public class VendasOnlineCampanha extends SuperEntidade implements VendasOnlineCampanhaInterfaceFacade {

    private static StringBuilder SQL_SELECT_PADRAO = new StringBuilder()
                                              .append("select emp.codigo as codigoEmpresa, emp.nome as nomeEmpresa, \n")
                                               .append("       e.codigo as codigoEvento, e.descricao as descricaoEvento, e.vigenciaInicial, e.vigenciaFinal, \n")
                                               .append("       plano.codigo as codigoPlano, plano.descricao as descricaoPlano, \n")
                                               .append("       c.codigo as codigoCategoria, c.nome as nomeCategoria, \n")
                                               .append("       produto.codigo as codigoProduto, produto.descricao as descricaoProduto, \n")
                                               .append("        v.* \n")
                                               .append("from vendasOnlineCampanha v \n")
                                               .append("inner join evento e on e.codigo = v.evento \n")
                                               .append("left join empresa emp on emp.codigo = v.empresa \n")
                                               .append("left join plano on plano.codigo = v.plano \n")
                                               .append("left join categoria c on c.codigo = v.categoria \n")
                                               .append("left join produto on produto.codigo = v.produto \n");


    public VendasOnlineCampanha() throws Exception {
        super();
    }

    public VendasOnlineCampanha(Connection con) throws Exception {
        super(con);
    }

    public void incluir(VendasOnlineCampanhaVO vendasOnlineCampanhaVO)throws Exception{
        vendasOnlineCampanhaVO.validarDados();
        validarDuplicidade(vendasOnlineCampanhaVO);
        vendasOnlineCampanhaVO.setDataCadastro(Calendario.hoje());
        PreparedStatement pst = con.prepareStatement("insert into vendasOnlineCampanha(tipoLink, empresa,evento,plano,categoria,produto,quantidadeProduto,cupomDesconto,link,linkAgenda, dataCadastro) values(?,?,?,?,?,?,?,?,?,?,?)");
        pst.setInt(1,vendasOnlineCampanhaVO.getTipoLink());
        pst.setInt(2,vendasOnlineCampanhaVO.getEmpresaVO().getCodigo());
        pst.setInt(3,vendasOnlineCampanhaVO.getEventoVO().getCodigo());
        resolveIntegerNull(pst,4,vendasOnlineCampanhaVO.getPlanoVO().getCodigo());
        resolveIntegerNull(pst,5,vendasOnlineCampanhaVO.getCategoriaVO().getCodigo());
        resolveIntegerNull(pst,6,vendasOnlineCampanhaVO.getProdutoVO().getCodigo());
        resolveIntegerNull(pst,7,vendasOnlineCampanhaVO.getQuantidadeProduto());
        resolveStringNull(pst,8,vendasOnlineCampanhaVO.getCupomDesconto());
        pst.setString(9,vendasOnlineCampanhaVO.getLink());
        resolveStringNull(pst,10,vendasOnlineCampanhaVO.getLinkAgenda());
        pst.setTimestamp(11, Uteis.getDataJDBCTimestamp(vendasOnlineCampanhaVO.getDataCadastro()));
        pst.execute();
        vendasOnlineCampanhaVO.setCodigo(obterValorChavePrimariaCodigo());
        vendasOnlineCampanhaVO.setNovoObj(false);
    }

    private void validarDuplicidade(VendasOnlineCampanhaVO vendasOnlineCampanhaVO)throws Exception{
        StringBuilder sql = new StringBuilder();
        sql.append("select codigo \n");
        sql.append("from vendasOnlineCampanha \n");
        sql.append("where evento = ").append(vendasOnlineCampanhaVO.getEventoVO().getCodigo()).append(" \n");
        sql.append("and tipoLink = ").append(vendasOnlineCampanhaVO.getTipoLink()).append(" \n");
        if (vendasOnlineCampanhaVO.getTipoLink().equals(TipoLinkCampanhaEnum.PLANO.getCodigo())){
            if ((vendasOnlineCampanhaVO.getPlanoVO().getCodigo() != null) && (vendasOnlineCampanhaVO.getPlanoVO().getCodigo() >0)){
                sql.append("and plano =").append(vendasOnlineCampanhaVO.getPlanoVO().getCodigo()).append("\n");
            }else{
                sql.append("and plano is null \n");
            }
            if ((vendasOnlineCampanhaVO.getCategoriaVO().getCodigo() != null) && (vendasOnlineCampanhaVO.getCategoriaVO().getCodigo() >0)){
                sql.append("and categoria =").append(vendasOnlineCampanhaVO.getCategoriaVO().getCodigo()).append(" \n");
            }else{
                sql.append("and categoria is null \n");
            }
            if ((vendasOnlineCampanhaVO.getCupomDesconto() != null) && (!vendasOnlineCampanhaVO.getCupomDesconto().trim().equals(""))){
                sql.append("and upper(cupomDesconto) = '").append(vendasOnlineCampanhaVO.getCupomDesconto().toUpperCase()).append("' \n");
            }else{
                sql.append("and cupomDesconto is null \n");
            }
        }else if (vendasOnlineCampanhaVO.getTipoLink().equals(TipoLinkCampanhaEnum.PRODUTO.getCodigo())){
            if ((vendasOnlineCampanhaVO.getProdutoVO().getCodigo() != null) && (vendasOnlineCampanhaVO.getProdutoVO().getCodigo() >0)){
                sql.append("and produto =").append(vendasOnlineCampanhaVO.getProdutoVO().getCodigo()).append(" \n");
            }else{
                sql.append("and produto is null \n");
            }
        }
        ResultSet rs = con.createStatement().executeQuery(sql.toString());
        if (rs.next()){
            throw new ConsistirException("Operação não permitida. Já foi criado um link com estas mesmas informações.");
        }
    }

    public void excluir(VendasOnlineCampanhaVO vendasOnlineCampanhaVO)throws Exception{
        con.createStatement().execute("delete from vendasOnlineCampanha where codigo = " + vendasOnlineCampanhaVO.getCodigo());
    }

    public List<VendasOnlineCampanhaVO> consultar(Integer tipoLink)throws Exception{
        List<VendasOnlineCampanhaVO> lista = new ArrayList<>();
        StringBuilder sql = new StringBuilder();
        sql.append(SQL_SELECT_PADRAO.toString());
        sql.append(" where v.tipoLink =").append(tipoLink);
        sql.append(" order by v.codigo desc ");
        ResultSet rs = con.createStatement().executeQuery(sql.toString());
        while (rs.next()){
            lista.add(montarDados(rs));
        }
        return lista;
    }

    public List<VendasOnlineCampanhaVO> consultarTipoEEmpresa(Integer tipoLink, Integer empresa)throws Exception{
        List<VendasOnlineCampanhaVO> lista = new ArrayList<>();
        StringBuilder sql = new StringBuilder();
        sql.append(SQL_SELECT_PADRAO.toString());
        sql.append(" where v.tipoLink =").append(tipoLink);
        sql.append(" AND emp.codigo = ").append(empresa);
        sql.append(" order by v.codigo desc ");
        ResultSet rs = con.createStatement().executeQuery(sql.toString());
        while (rs.next()){
            lista.add(montarDados(rs));
        }
        return lista;
    }

    private VendasOnlineCampanhaVO montarDados(ResultSet rs)throws Exception{
        VendasOnlineCampanhaVO obj = new VendasOnlineCampanhaVO();
        obj.setCodigo(rs.getInt("codigo"));
        obj.setTipoLink(rs.getInt("tipoLink"));
        obj.getEmpresaVO().setCodigo(rs.getInt("codigoEmpresa"));
        obj.getEmpresaVO().setNome(rs.getString("nomeEmpresa"));
        obj.getEventoVO().setCodigo(rs.getInt("codigoEvento"));
        obj.getEventoVO().setDescricao(rs.getString("descricaoEvento"));
        obj.getPlanoVO().setCodigo(rs.getInt("codigoPlano"));
        if (rs.getString("descricaoPlano") != null){
            obj.getPlanoVO().setDescricao(rs.getString("descricaoPlano"));
        }else{
            obj.getPlanoVO().setDescricao("-");
        }
        obj.getCategoriaVO().setCodigo(rs.getInt("codigoCategoria"));
        if (rs.getString("nomeCategoria") != null){
            obj.getCategoriaVO().setNome(rs.getString("nomeCategoria"));
        }else{
            obj.getCategoriaVO().setNome("-");
        }
        obj.getProdutoVO().setCodigo(rs.getInt("codigoProduto"));
        if (rs.getString("descricaoProduto") != null){
            obj.getProdutoVO().setDescricao(rs.getString("descricaoProduto"));
        }else{
            obj.getProdutoVO().setDescricao("-");
        }
        obj.setQuantidadeProduto(rs.getInt("quantidadeProduto"));
        obj.setCupomDesconto(rs.getString("cupomDesconto"));
        if (obj.getCupomDesconto() == null){
            obj.setCupomDesconto("-");
        }
        obj.setLink(rs.getString("link"));
        obj.setLinkAgenda(rs.getString("linkAgenda"));
        obj.setDataCadastro(rs.getTimestamp("dataCadastro"));
        Date vigenciaInicial = rs.getDate("vigenciaInicial");
        Date vigenciaFinal = rs.getDate("vigenciaFinal");
        if (Calendario.entre(Calendario.hoje(),vigenciaInicial,vigenciaFinal)){
            obj.setStatusEvento("ATIVO");
        }else{
            obj.setStatusEvento("INATIVO");
        }
        return obj;
    }


}

package negocio.facade.jdbc.vendas;

import annotations.arquitetura.NaoControlarLogAlteracao;
import negocio.comuns.arquitetura.SuperVO;
import negocio.comuns.basico.EmpresaVO;
import negocio.comuns.financeiro.ConvenioCobrancaVO;
import negocio.comuns.plano.PlanoVO;
import negocio.comuns.plano.ProdutoVO;
import negocio.comuns.utilitarias.ConsistirException;
import negocio.comuns.utilitarias.UteisValidacao;

import java.util.Date;

/**
 * Created with IntelliJ IDEA.
 * User: <PERSON><PERSON>
 * Date: 23/06/2020
 */
public class VendasOnlineConvenioVO extends SuperVO {

    private Integer codigo;
    private Date dataRegistro;
    private ConvenioCobrancaVO convenioCobrancaVO;
    private EmpresaVO empresaVO;
    private ProdutoVO produtoVO;
    private PlanoVO planoVO;
    private String tipoParcelamentoStone;
    @NaoControlarLogAlteracao
    private ConvenioCobrancaVO convenioCobrancaConflito;
    private Integer formaPagamento;

    public VendasOnlineConvenioVO() {
        super();
    }

    public VendasOnlineConvenioVO(ConvenioCobrancaVO convenioCobrancaVO, EmpresaVO empresaVO) {
        super();
        this.convenioCobrancaVO = convenioCobrancaVO;
        this.empresaVO = empresaVO;
    }

    public static void validarDados(VendasOnlineConvenioVO obj, boolean formaPagamento) throws ConsistirException {
        if (!obj.getValidarDados()) {
            return;
        }
        if (!formaPagamento && UteisValidacao.emptyNumber(obj.getConvenioCobrancaVO().getCodigo())) {
            throw new ConsistirException("Selecione um convênio de cobrança.");
        }
        if (formaPagamento && UteisValidacao.emptyNumber(obj.getFormaPagamento())) {
            throw new ConsistirException("Selecione uma forma de pagamento.");
        }
        if (UteisValidacao.emptyNumber(obj.getEmpresaVO().getCodigo())) {
            throw new ConsistirException("Selecione a empresa.");
        }
        if (UteisValidacao.emptyNumber(obj.getPlanoVO().getCodigo()) && UteisValidacao.emptyNumber(obj.getProdutoVO().getCodigo())) {
            throw new ConsistirException("Selecione um plano ou produto.");
        }
    }

    public String getFormaPagamentoDescricao() {
        if (this.formaPagamento != null && this.formaPagamento == 1) {
            return "PIX";
        } else if (this.formaPagamento != null && this.formaPagamento == 2) {
            return "BOLETO";
        } else if (this.formaPagamento != null && this.formaPagamento == 3) {
            return "CARTÃO DE CRÉDITO";
        } else {
            return "";
        }
    }

    @Override
    public Integer getCodigo() {
        if (codigo == null) {
            codigo = 0;
        }
        return codigo;
    }

    @Override
    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public Date getDataRegistro() {
        return dataRegistro;
    }

    public void setDataRegistro(Date dataRegistro) {
        this.dataRegistro = dataRegistro;
    }

    public ConvenioCobrancaVO getConvenioCobrancaVO() {
        if (convenioCobrancaVO == null) {
            convenioCobrancaVO = new ConvenioCobrancaVO();
        }
        return convenioCobrancaVO;
    }

    public void setConvenioCobrancaVO(ConvenioCobrancaVO convenioCobrancaVO) {
        this.convenioCobrancaVO = convenioCobrancaVO;
    }

    public EmpresaVO getEmpresaVO() {
        if (empresaVO == null) {
            empresaVO = new EmpresaVO();
        }
        return empresaVO;
    }

    public void setEmpresaVO(EmpresaVO empresaVO) {
        this.empresaVO = empresaVO;
    }

    public ProdutoVO getProdutoVO() {
        if (produtoVO == null) {
            produtoVO = new ProdutoVO();
        }
        return produtoVO;
    }

    public void setProdutoVO(ProdutoVO produtoVO) {
        this.produtoVO = produtoVO;
    }

    public PlanoVO getPlanoVO() {
        if (planoVO == null) {
            planoVO = new PlanoVO();
        }
        return planoVO;
    }

    public void setPlanoVO(PlanoVO planoVO) {
        this.planoVO = planoVO;
    }

    public ConvenioCobrancaVO getConvenioCobrancaConflito() {
        if (convenioCobrancaConflito == null) {
            convenioCobrancaConflito = new ConvenioCobrancaVO();
        }
        return convenioCobrancaConflito;
    }

    public void setConvenioCobrancaConflito(ConvenioCobrancaVO convenioCobrancaConflito) {
        this.convenioCobrancaConflito = convenioCobrancaConflito;
    }

    public String getTipoParcelamentoStone() {
        if (tipoParcelamentoStone == null) {
            tipoParcelamentoStone = "";
        }
        return tipoParcelamentoStone;
    }

    public void setTipoParcelamentoStone(String tipoParcelamentoStone) {
        this.tipoParcelamentoStone = tipoParcelamentoStone;
    }

    public Integer getFormaPagamento() {
        return formaPagamento;
    }

    public void setFormaPagamento(Integer formaPagamento) {
        this.formaPagamento = formaPagamento;
    }
}

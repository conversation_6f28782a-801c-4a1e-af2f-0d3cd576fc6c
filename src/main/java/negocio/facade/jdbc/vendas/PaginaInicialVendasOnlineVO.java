package negocio.facade.jdbc.vendas;

import negocio.comuns.utilitarias.Uteis;

public class PaginaInicialVendasOnlineVO {
    private Integer codigo;
    private Integer codVendasOnlineConfig = 0;
    private String textoBoxArredondado = "";
    private String tituloPrincipal = "";
    private String fotoKey = "";
    private Integer posicao;
    private Boolean exibirBotaoAgendaSobreBanner = false;

    public Integer getCodigo() {
        return codigo;
    }

    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public Integer getCodVendasOnlineConfig() {
        return codVendasOnlineConfig;
    }

    public void setCodVendasOnlineConfig(Integer codVendasOnlineConfig) {
        this.codVendasOnlineConfig = codVendasOnlineConfig;
    }

    public String getTextoBoxArredondado() {
        return textoBoxArredondado;
    }

    public void setTextoBoxArredondado(String textoBoxArredondado) {
        this.textoBoxArredondado = textoBoxArredondado;
    }

    public String getTituloPrincipal() {
        return tituloPrincipal;
    }

    public void setTituloPrincipal(String tituloPrincipal) {
        this.tituloPrincipal = tituloPrincipal;
    }

    public String getFotoKey() {
        return fotoKey;
    }

    public void setFotoKey(String fotoKey) {
        this.fotoKey = fotoKey;
    }

    public String getUrlFoto() {
        try {
            return Uteis.getPaintFotoDaNuvem(getFotoKey());
        } catch (Exception e) {
            return "";
        }
    }

    public Integer getPosicao() {
        return posicao;
    }

    public void setPosicao(Integer posicao) {
        this.posicao = posicao;
    }

    public Boolean getExibirBotaoAgendaSobreBanner() {
        return exibirBotaoAgendaSobreBanner;
    }

    public void setExibirBotaoAgendaSobreBanner(Boolean exibirBotaoAgendaSobreBanner) {
        this.exibirBotaoAgendaSobreBanner = exibirBotaoAgendaSobreBanner;
    }

}

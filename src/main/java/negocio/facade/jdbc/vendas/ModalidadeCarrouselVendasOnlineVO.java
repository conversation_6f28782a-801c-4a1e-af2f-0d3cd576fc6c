package negocio.facade.jdbc.vendas;

import br.com.pactosolucoes.enumeradores.TipoAgrupamentoEnum;
import negocio.comuns.utilitarias.Uteis;

public class ModalidadeCarrouselVendasOnlineVO {
    private Integer codigo;
    private Integer codModalidade = 0;
    private Integer codVendasOnlineConfig = 0;
    private String tituloModalidadeVendas = "";
    private String descricaoModalidadeVendas = "";
    private String fotoKey = "";
    private Integer ordenacaoModalidadeVendas = 0;
    private Boolean banner = false;

    private TipoAgrupamentoEnum tipoAgrupamento;

    private String nomeArquivo;
    private byte[] bytesArquivo;

    public ModalidadeCarrouselVendasOnlineVO() {

    }
    public ModalidadeCarrouselVendasOnlineVO(Integer codigo, Integer codModalidade, Integer codVendasOnlineConfig, String tituloModalidadeVendas,
            String descricaoModalidadeVendas, String fotoKey, Integer ordenacaoModalidadeVendas, Boolean banner) {
        this.codigo = codigo;
        this.codModalidade = codModalidade;
        this.codVendasOnlineConfig = codVendasOnlineConfig;
        this.tituloModalidadeVendas = tituloModalidadeVendas;
        this.descricaoModalidadeVendas = descricaoModalidadeVendas;
        this.fotoKey = fotoKey;
        this.ordenacaoModalidadeVendas = ordenacaoModalidadeVendas;
        this.banner = banner;
    }

    public Integer getCodigo() {
        return codigo;
    }

    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public String getFotoKey() {
        return fotoKey;
    }

    public void setFotoKey(String fotoKey) {
        this.fotoKey = fotoKey;
    }

    public String getUrlFoto() {
        try {
            return Uteis.getPaintFotoDaNuvem(getFotoKey());
        } catch (Exception e) {
            return "";
        }
    }

    public Integer getCodModalidade() {
        return codModalidade;
    }

    public void setCodModalidade(Integer codModalidade) {
        this.codModalidade = codModalidade;
    }

    public String getTituloModalidadeVendas() {
        return tituloModalidadeVendas;
    }

    public void setTituloModalidadeVendas(String tituloModalidadeVendas) {
        this.tituloModalidadeVendas = tituloModalidadeVendas;
    }

    public String getDescricaoModalidadeVendas() {
        return descricaoModalidadeVendas;
    }

    public void setDescricaoModalidadeVendas(String descricaoModalidadeVendas) {
        this.descricaoModalidadeVendas = descricaoModalidadeVendas;
    }

    public Integer getOrdenacaoModalidadeVendas() {
        if(this.ordenacaoModalidadeVendas == null){
            this.ordenacaoModalidadeVendas = 0;
        }
        return ordenacaoModalidadeVendas;
    }

    public void setOrdenacaoModalidadeVendas(Integer ordenacaoModalidadeVendas) {
        this.ordenacaoModalidadeVendas = ordenacaoModalidadeVendas;
    }

    public Integer getCodVendasOnlineConfig() {
        return codVendasOnlineConfig;
    }

    public void setCodVendasOnlineConfig(Integer codVendasOnlineConfig) {
        this.codVendasOnlineConfig = codVendasOnlineConfig;
    }

    public Boolean getBanner() {
        return banner;
    }

    public void setBanner(Boolean banner) {
        this.banner = banner;
    }

    public String getNomeArquivo() {
        return nomeArquivo;
    }

    public void setNomeArquivo(String nomeArquivo) {
        this.nomeArquivo = nomeArquivo;
    }

    public byte[] getBytesArquivo() {
        return bytesArquivo;
    }

    public void setBytesArquivo(byte[] bytesArquivo) {
        this.bytesArquivo = bytesArquivo;
    }

    public TipoAgrupamentoEnum getTipoAgrupamento() {
        return tipoAgrupamento;
    }

    public void setTipoAgrupamento(TipoAgrupamentoEnum tipoAgrupamento) {
        this.tipoAgrupamento = tipoAgrupamento;
    }
}

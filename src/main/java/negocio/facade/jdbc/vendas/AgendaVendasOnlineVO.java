package negocio.facade.jdbc.vendas;

import negocio.comuns.arquitetura.SuperVO;
import negocio.comuns.plano.ProdutoVO;

import javax.faces.event.ValueChangeEvent;
import javax.faces.model.SelectItem;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

public class AgendaVendasOnlineVO extends SuperVO {

    public static Integer NOVO_PRODUTO = 0;
    public static Integer TODAS_AULAS = 0;

    private List<TurmaEHorarioVendasOnlineVO> listTurmaHorario;
    private List<TurmaEHorarioVendasOnlineVO> listTurmaHorarioLinkVisitante;
    private List<AulasVendasOnline> aulasVendasOnline;
    private List<AulasVendasOnline> aulasVendasOnlineLinkVisitante;
    private List<SelectItem> aulas;
    private List<SelectItem> produtosExistentes;
    private Integer aulaEscolhida;
    private String descricaoProduto;
    private String valorProduto;
    private Boolean estaEditando = false;
    private Integer tipoProduto = NOVO_PRODUTO;
    private Integer tipoAulasLinkVisitante = TODAS_AULAS;
    private List<ProdutoVO> produtoVOS;
    private Integer produtoEscolhido;
    private Integer aulaLinkVisitanteEscolhida;
    private List<SelectItem> aulasLinkVisitante;

    public AgendaVendasOnlineVO() {
        listTurmaHorario = new ArrayList<>();
        aulasVendasOnline = new ArrayList<>();
        aulas = new ArrayList<>();
    }

    public List<SelectItem> getAulas() {
        aulas = new ArrayList<>();
        if (Objects.nonNull(listTurmaHorario)) {
            for (TurmaEHorarioVendasOnlineVO vo : listTurmaHorario) {
                aulas.add(new SelectItem(vo.getCodigoTurma(), vo.getDescricaoCompleta()));
            }
        }

        return aulas;
    }

    public void setAulas(List<SelectItem> aulas) {
        this.aulas = aulas;
    }

    public List<SelectItem> getProdutosExistentes() {
        produtosExistentes = new ArrayList<>();

        if (Objects.nonNull(produtoVOS)) {
            for (ProdutoVO produtoVO : produtoVOS) {
                produtosExistentes.add(new SelectItem(produtoVO.getCodigo(), produtoVO.getDescricao()));
            }
        }

        return produtosExistentes;
    }

    public void setProdutosExistentes(List<SelectItem> produtosExistentes) {
        this.produtosExistentes = produtosExistentes;
    }

    public void setAulaEscolhida(Integer aulaEscolhida) {
        this.aulaEscolhida = aulaEscolhida;
    }

    public Integer getAulaEscolhida() {
        return aulaEscolhida;
    }

    public void setTurmaEHorario(List<TurmaEHorarioVendasOnlineVO> list) {
        this.listTurmaHorario = list;
    }

    public void setDescricaoProduto(String descricaoProduto) {
        this.descricaoProduto = descricaoProduto;
    }

    public String getDescricaoProduto() {
        return descricaoProduto;
    }

    public void setValorProduto(String valorProduto) {
        this.valorProduto = valorProduto;
    }

    public String getValorProduto() {
        return valorProduto;
    }

    public List<AulasVendasOnline> getAulasVendasOnline() {
        return aulasVendasOnline;
    }

    public void setAulasVendasOnline(List<AulasVendasOnline> aulasVendasOnline) {
        this.aulasVendasOnline = aulasVendasOnline;
    }

    public Boolean getEstaEditando() {
        return estaEditando;
    }

    public void setEstaEditando(Boolean estaEditando) {
        this.estaEditando = estaEditando;
    }

    public Integer getTipoProduto() {
        return tipoProduto;
    }

    public void setTipoProduto(Integer tipoProduto) {
        this.tipoProduto = tipoProduto;
    }

    public void trocarCategoria(ValueChangeEvent event){
        setTipoProduto(Integer.parseInt(String.valueOf(event.getNewValue())));
    }

    public void trocarTipoAulaLinkVisitante(ValueChangeEvent event){
        setTipoAulasLinkVisitante(Integer.parseInt(String.valueOf(event.getNewValue())));
    }

    public List<ProdutoVO> getProdutoVOS() {
        return produtoVOS;
    }

    public void setProdutoVOS(List<ProdutoVO> produtoVOS) {
        this.produtoVOS = produtoVOS;
    }

    public Integer getProdutoEscolhido() {
        return produtoEscolhido;
    }

    public void setProdutoEscolhido(Integer produtoEscolhido) {
        this.produtoEscolhido = produtoEscolhido;
    }

    @Override
    public String toString() {
        return "";
    }

    public Integer getTipoAulasLinkVisitante() {
        return tipoAulasLinkVisitante;
    }

    public void setTipoAulasLinkVisitante(Integer tipoAulasLinkVisitante) {
        this.tipoAulasLinkVisitante = tipoAulasLinkVisitante;
    }

    public Integer getAulaLinkVisitanteEscolhida() {
        return aulaLinkVisitanteEscolhida;
    }

    public void setAulaLinkVisitanteEscolhida(Integer aulaLinkVisitanteEscolhida) {
        this.aulaLinkVisitanteEscolhida = aulaLinkVisitanteEscolhida;
    }

    public List<SelectItem> getAulasLinkVisitante() {
        aulasLinkVisitante = new ArrayList<>();
        if (Objects.nonNull(listTurmaHorarioLinkVisitante)) {
            for (TurmaEHorarioVendasOnlineVO vo : listTurmaHorarioLinkVisitante) {
                aulasLinkVisitante.add(new SelectItem(vo.getCodigoTurma(), vo.getDescricaoCompleta()));
            }
        }

        return aulasLinkVisitante;
    }

    public void setAulasLinkVisitante(List<SelectItem> aulasLinkVisitante) {
        this.aulasLinkVisitante = aulasLinkVisitante;
    }

    public List<AulasVendasOnline> getAulasVendasOnlineLinkVisitante() {
        return aulasVendasOnlineLinkVisitante;
    }

    public void setAulasVendasOnlineLinkVisitante(List<AulasVendasOnline> aulasVendasOnlineLinkVisitante) {
        this.aulasVendasOnlineLinkVisitante = aulasVendasOnlineLinkVisitante;
    }

    public List<TurmaEHorarioVendasOnlineVO> getListTurmaHorarioLinkVisitante() {
        return listTurmaHorarioLinkVisitante;
    }

    public void setListTurmaHorarioLinkVisitante(List<TurmaEHorarioVendasOnlineVO> listTurmaHorarioLinkVisitante) {
        this.listTurmaHorarioLinkVisitante = listTurmaHorarioLinkVisitante;
    }
}

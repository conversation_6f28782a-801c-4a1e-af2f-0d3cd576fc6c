package negocio.facade.jdbc.vendas;

import negocio.comuns.arquitetura.SuperVO;
import negocio.comuns.basico.CategoriaVO;
import negocio.comuns.basico.EmpresaVO;
import negocio.comuns.crm.EventoVO;
import negocio.comuns.plano.PlanoVO;
import negocio.comuns.plano.ProdutoVO;
import negocio.comuns.utilitarias.ConsistirException;
import negocio.comuns.utilitarias.Uteis;

import java.text.SimpleDateFormat;
import java.util.Date;

/**
 * Created by ulisses on 23/11/2022.
 */
public class VendasOnlineCampanhaVO extends SuperVO {

    private Integer codigo;
    private Integer tipoLink;
    private EmpresaVO empresaVO;
    private EventoVO eventoVO;
    private PlanoVO planoVO;
    private CategoriaVO categoriaVO;
    private ProdutoVO produtoVO;
    private Integer quantidadeProduto;
    private String cupomDesconto;
    private String link;
    private String linkAgenda;
    private Date  dataCadastro;

    private String statusEvento; // atributo transient


    @Override
    public Integer getCodigo() {
        return codigo;
    }

    @Override
    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public Integer getTipoLink() {
        return tipoLink;
    }

    public void setTipoLink(Integer tipoLink) {
        this.tipoLink = tipoLink;
    }

    public EmpresaVO getEmpresaVO() {
        if (empresaVO == null){
            this.empresaVO = new EmpresaVO();
        }
        return empresaVO;
    }

    public void setEmpresaVO(EmpresaVO empresaVO) {
        this.empresaVO = empresaVO;
    }

    public EventoVO getEventoVO() {
        if (eventoVO == null){
            this.eventoVO = new EventoVO();
        }
        return eventoVO;
    }

    public void setEventoVO(EventoVO eventoVO) {
        this.eventoVO = eventoVO;
    }

    public PlanoVO getPlanoVO() {
        if (this.planoVO == null){
            this.planoVO = new PlanoVO();
            this.planoVO.setDescricao("-");
        }
        return planoVO;
    }

    public void setPlanoVO(PlanoVO planoVO) {
        this.planoVO = planoVO;
    }

    public CategoriaVO getCategoriaVO() {
        if (this.categoriaVO == null){
            this.categoriaVO = new CategoriaVO();
            this.categoriaVO.setNome("-");
        }
        return categoriaVO;
    }

    public void setCategoriaVO(CategoriaVO categoriaVO) {
        this.categoriaVO = categoriaVO;
    }

    public ProdutoVO getProdutoVO() {
        if (this.produtoVO == null){
            this.produtoVO = new ProdutoVO();
        }
        return produtoVO;
    }

    public void setProdutoVO(ProdutoVO produtoVO) {
        this.produtoVO = produtoVO;
    }

    public Integer getQuantidadeProduto() {
        return quantidadeProduto;
    }

    public void setQuantidadeProduto(Integer quantidadeProduto) {
        this.quantidadeProduto = quantidadeProduto;
    }

    public String getCupomDesconto() {
        return cupomDesconto;
    }

    public void setCupomDesconto(String cupomDesconto) {
        this.cupomDesconto = cupomDesconto;
    }

    public String getLink() {
        return link;
    }

    public void setLink(String link) {
        this.link = link;
    }

    public Date getDataCadastro() {
        return dataCadastro;
    }

    public void setDataCadastro(Date dataCadastro) {
        this.dataCadastro = dataCadastro;
    }

    public String getStatusEvento() {
        return statusEvento;
    }

    public void setStatusEvento(String statusEvento) {
        this.statusEvento = statusEvento;
    }

    public String getDataCadastro_apresentar(){
        return new SimpleDateFormat("dd/MM/yy HH:mm").format(this.dataCadastro);
    }

    public String getLinkAgenda() {
        return linkAgenda;
    }

    public void setLinkAgenda(String linkAgenda) {
        this.linkAgenda = linkAgenda;
    }

    public void validarDados()throws Exception{
        if ((getTipoLink() == null) || (getTipoLink() == 0)) {
            throw new ConsistirException("O campo tipoLink  deve ser informado.");
        }
        if ((this.empresaVO == null) || (this.empresaVO.getCodigo() == 0)) {
            throw new ConsistirException("O campo empresa  deve ser informado.");
        }
        if ((this.eventoVO == null) || (this.eventoVO.getCodigo() == 0)) {
            throw new ConsistirException("O campo evento  deve ser informado.");
        }


    }
}

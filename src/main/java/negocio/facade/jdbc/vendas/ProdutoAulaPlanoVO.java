package negocio.facade.jdbc.vendas;

import negocio.comuns.utilitarias.Uteis;

public class ProdutoAulaPlanoVO {

    private Integer codigoProduto;
    private String descricaoProduto;
    private Integer codigoModalidade;
    private String modalidade;
    private Integer codigoAula;
    private String descricaoAula;

    public ProdutoAulaPlanoVO() {
    }

    public Integer getCodigoProduto() {
        return codigoProduto;
    }

    public void setCodigoProduto(Integer codigoProduto) {
        this.codigoProduto = codigoProduto;
    }

    public String getDescricaoProduto() {
        return descricaoProduto;
    }

    public void setDescricaoProduto(String descricaoProduto) {
        this.descricaoProduto = descricaoProduto;
    }

    public Integer getCodigoModalidade() {
        return codigoModalidade;
    }

    public void setCodigoModalidade(Integer codigoModalidade) {
        this.codigoModalidade = codigoModalidade;
    }

    public String getModalidade() {
        return modalidade;
    }

    public void setModalidade(String modalidade) {
        this.modalidade = modalidade;
    }

    public Integer getCodigoAula() {
        return codigoAula;
    }

    public void setCodigoAula(Integer codigoAula) {
        this.codigoAula = codigoAula;
    }

    public String getDescricaoAula() {
        return descricaoAula;
    }

    public void setDescricaoAula(String descricaoAula) {
        this.descricaoAula = descricaoAula;
    }
}

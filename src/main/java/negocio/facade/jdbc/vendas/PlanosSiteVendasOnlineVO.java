package negocio.facade.jdbc.vendas;

import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisValidacao;

public class PlanosSiteVendasOnlineVO {

    private Integer codigo;
    private Integer codPlano = 0;
    private Integer codVendasOnlineConfig = 0;
    private String destaqueCabecalho = "";
    private String descricaoPlano = "";
    private String beneficio01 = "";
    private String beneficio02 = "";
    private String beneficio03 = "";
    private Double precoOriginal = 0.0;
    private Double valor = 0.0;
    private String titulo = "";
    private int maximoVezesParcelar = 0;
    private int duracao = 0;
    private Double valorMensalExibir = 0.0;
    private boolean recorrencia;
    private String beneficios = "";

    public Integer getCodigo() {
        return codigo;
    }

    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public Integer getCodPlano() {
        return codPlano;
    }

    public void setCodPlano(Integer codPlano) {
        this.codPlano = codPlano;
    }

    public Integer getCodVendasOnlineConfig() {
        return codVendasOnlineConfig;
    }

    public void setCodVendasOnlineConfig(Integer codVendasOnlineConfig) {
        this.codVendasOnlineConfig = codVendasOnlineConfig;
    }

    public String getDestaqueCabecalho() {
        return destaqueCabecalho;
    }

    public void setDestaqueCabecalho(String destaqueCabecalho) {
        this.destaqueCabecalho = destaqueCabecalho;
    }

    public String getDescricaoPlano() {
        return descricaoPlano;
    }

    public void setDescricaoPlano(String descricaoPlano) {
        this.descricaoPlano = descricaoPlano;
    }

    public String getBeneficio01() {
        return beneficio01;
    }

    public void setBeneficio01(String beneficio01) {
        this.beneficio01 = beneficio01;
    }

    public String getBeneficio02() {
        return beneficio02;
    }

    public void setBeneficio02(String beneficio02) {
        this.beneficio02 = beneficio02;
    }

    public String getBeneficio03() {
        return beneficio03;
    }

    public void setBeneficio03(String beneficio03) {
        this.beneficio03 = beneficio03;
    }

    public Double getPrecoOriginal() {
        return precoOriginal;
    }

    public void setPrecoOriginal(Double precoOriginal) {
        this.precoOriginal = precoOriginal;
    }

    public Double getValor() {
        return valor;
    }

    public void setValor(Double valor) {
        this.valor = valor;
    }

    public String getTitulo() {
        return titulo;
    }

    public void setTitulo(String titulo) {
        this.titulo = titulo;
    }


    public int getMaximoVezesParcelar() {
        return maximoVezesParcelar;
    }

    public void setMaximoVezesParcelar(int maximoVezesParcelar) {
        this.maximoVezesParcelar = maximoVezesParcelar;
    }

    public int getDuracao() {
        return duracao;
    }

    public void setDuracao(int duracao) {
        this.duracao = duracao;
    }

    public Double getValorMensalExibir() {
        if (UteisValidacao.emptyNumber(valorMensalExibir)) {
            return 0.0;
        }
        return valorMensalExibir;
    }

    public void setValorMensalExibir(Double valorMensalExibir) {
        this.valorMensalExibir = valorMensalExibir;
    }


    public boolean isRecorrencia() {
        return recorrencia;
    }

    public void setRecorrencia(boolean recorrencia) {
        this.recorrencia = recorrencia;
    }

    public String getBeneficios() {
        if(beneficios == null) {
            beneficios = "";
        }
        return beneficios;
    }

    public void setBeneficios(String beneficios) {
        this.beneficios = beneficios;
    }
}

package negocio.facade.jdbc.acesso;

import negocio.comuns.utilitarias.UteisValidacao;
import org.json.JSONArray;
import org.json.JSONObject;
import negocio.comuns.acesso.CameraVO;
import negocio.comuns.acesso.ServidorFacialVO;
import negocio.comuns.basico.EmpresaVO;
import negocio.comuns.utilitarias.Ordenacao;
import negocio.comuns.utilitarias.Uteis;
import negocio.facade.jdbc.arquitetura.SuperEntidade;
import negocio.interfaces.acesso.ServidorFacialInterfaceFacade;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.sql.Statement;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

public class ServidorFacial extends SuperEntidade implements ServidorFacialInterfaceFacade {

    public ServidorFacial() throws Exception {
        super();
    }

    public ServidorFacial(Connection conexao) throws Exception {
        super(conexao);
    }

    public static ServidorFacialVO montarDadosBasico(ResultSet dadosSQL) throws Exception {
        ServidorFacialVO obj = new ServidorFacialVO();
        obj.setNovoObj(false);
        obj.setCodigo(dadosSQL.getInt("codigo"));
        obj.setDescricao(dadosSQL.getString("descricao"));
        obj.setNomecomputador(dadosSQL.getString("nomeComputador"));
        obj.setFullHD(dadosSQL.getInt("fullHD"));
        obj.setFacePreviewWidth(dadosSQL.getInt("facePreviewWidth"));
        obj.setFalsoPositivo(dadosSQL.getDouble("falsoPositivo"));
        obj.setTempoMinimoReenvio(dadosSQL.getInt("tempoMinimoReenvio"));
        obj.setPortaReconhecimentoFacial(dadosSQL.getInt("portaReconhecimentoFacial"));
        obj.setTerminal(dadosSQL.getInt("terminal"));
        obj.setPastaFotosReconhecimentoFacial(dadosSQL.getString("pastaFotosReconhecimentoFacial"));
        obj.setPastaFotosProcessadasReconhecimentoFacial(dadosSQL.getString("pastaFotosProcessadasReconhecimentoFacial"));
        obj.setPastaLogsReconhecimentoFacial(dadosSQL.getString("pastaLogsReconhecimentoFacial"));
        obj.setPortaPCReconhecimentoFacial(dadosSQL.getInt("portaPCReconhecimentoFacial"));
        obj.getEmpresa().setCodigo(dadosSQL.getInt("empresa"));
        obj.setServidorBDFacial(dadosSQL.getString("servidorbdfacial"));
        obj.setDistanciaIdentificacao(dadosSQL.getInt("distanciaIdentificacao"));

        return obj;
    }

    @Override
    public void incluir(ServidorFacialVO obj) throws Exception {
        obj.validarDados();

        String sql = "INSERT INTO servidorfacial(nomecomputador, fullHD, \n" +
                "facePreviewWidth, falsoPositivo, tempoMinimoReenvio, \n" +
                "portaReconhecimentoFacial, terminal, pastaFotosReconhecimentoFacial, \n" +
                "pastaFotosProcessadasReconhecimentoFacial, pastaLogsReconhecimentoFacial, \n" +
                "portaPCReconhecimentoFacial, descricao, empresa, servidorbdfacial, distanciaIdentificacao) " +
                "VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?);";

        try (PreparedStatement sqlInserir = con.prepareStatement(sql)) {
            int i = 0;
            sqlInserir.setString(++i, obj.getNomecomputador());
            sqlInserir.setInt(++i, obj.getFullHD());
            sqlInserir.setInt(++i, obj.getFacePreviewWidth());
            sqlInserir.setDouble(++i, obj.getFalsoPositivo());
            sqlInserir.setInt(++i, obj.getTempoMinimoReenvio());
            sqlInserir.setInt(++i, obj.getPortaReconhecimentoFacial());
            sqlInserir.setInt(++i, obj.getTerminal());
            sqlInserir.setString(++i, obj.getPastaFotosReconhecimentoFacial());
            sqlInserir.setString(++i, obj.getPastaFotosProcessadasReconhecimentoFacial());
            sqlInserir.setString(++i, obj.getPastaLogsReconhecimentoFacial());
            sqlInserir.setInt(++i, obj.getPortaPCReconhecimentoFacial());
            sqlInserir.setString(++i, obj.getDescricao());
            sqlInserir.setInt(++i, obj.getEmpresa().getCodigo());
            sqlInserir.setString(++i, obj.getServidorBDFacial());
            if (UteisValidacao.emptyNumber(obj.getDistanciaIdentificacao())){
                sqlInserir.setNull(++i, 0);
            } else {
                sqlInserir.setInt(++i, obj.getDistanciaIdentificacao());
            }
            sqlInserir.execute();
        }
        obj.setCodigo(obterValorChavePrimariaCodigo());
        obj.setNovoObj(false);


        getFacade().getCamera().incluirCameras(obj.getCodigo(), obj.getCameras());
    }

    @Override
    public void alterar(ServidorFacialVO obj) throws Exception {

        obj.validarDados();

        String sql = "UPDATE servidorfacial SET nomecomputador = ?, fullHD = ?, \n" +
                "facePreviewWidth = ?, falsoPositivo = ?, tempoMinimoReenvio = ?,\n" +
                "portaReconhecimentoFacial = ?, terminal = ?, pastaFotosReconhecimentoFacial = ?, \n" +
                "pastaFotosProcessadasReconhecimentoFacial = ?, pastaLogsReconhecimentoFacial = ?, \n" +
                "portaPCReconhecimentoFacial = ?, descricao = ?, empresa = ?, servidorbdfacial = ?, \n" +
                "distanciaIdentificacao = ? \n" +
                "WHERE codigo = ?";
        try (PreparedStatement sqlAlterar = con.prepareStatement(sql)) {
            int i = 0;
            sqlAlterar.setString(++i, obj.getNomecomputador());
            sqlAlterar.setInt(++i, obj.getFullHD());
            sqlAlterar.setInt(++i, obj.getFacePreviewWidth());
            sqlAlterar.setDouble(++i, obj.getFalsoPositivo());
            sqlAlterar.setInt(++i, obj.getTempoMinimoReenvio());
            sqlAlterar.setInt(++i, obj.getPortaReconhecimentoFacial());
            sqlAlterar.setInt(++i, obj.getTerminal());
            sqlAlterar.setString(++i, obj.getPastaFotosReconhecimentoFacial());
            sqlAlterar.setString(++i, obj.getPastaFotosProcessadasReconhecimentoFacial());
            sqlAlterar.setString(++i, obj.getPastaLogsReconhecimentoFacial());
            sqlAlterar.setInt(++i, obj.getPortaPCReconhecimentoFacial());
            sqlAlterar.setString(++i, obj.getDescricao());
            sqlAlterar.setInt(++i, obj.getEmpresa().getCodigo());
            sqlAlterar.setString(++i, obj.getServidorBDFacial());
            if (UteisValidacao.emptyNumber(obj.getDistanciaIdentificacao())){
                sqlAlterar.setNull(++i, 0);
            } else {
                sqlAlterar.setInt(++i, obj.getDistanciaIdentificacao());
            }
            sqlAlterar.setInt(++i, obj.getCodigo());
            sqlAlterar.execute();
        }

        getFacade().getCamera().alterarCameras(obj.getCodigo(), obj.getCameras());
    }

    @Override
    public void excluir(ServidorFacialVO obj) throws Exception {
        String sql = "DELETE FROM servidorfacial WHERE codigo = ?";
        try (PreparedStatement sqlExcluir = con.prepareStatement(sql)) {
            int i = 0;
            sqlExcluir.setInt(++i, obj.getCodigo());
            sqlExcluir.execute();
        }
    }


    private PreparedStatement getPS() throws SQLException {
        String sql = "SELECT\n" +
                "  sf.codigo, sf.descricao, e.nome AS empresa\n" +
                "FROM servidorfacial sf\n" +
                "  LEFT JOIN empresa e ON sf.empresa = e.codigo\n" +
                "  ORDER BY descricao";
        return con.prepareStatement(sql);
    }

    public String consultarJSON() throws Exception {
        JSONObject aaData;
        JSONArray valores;
        try (ResultSet rs = getPS().executeQuery()) {
            aaData = new JSONObject();
            valores = new JSONArray();

            while (rs.next()) {
                JSONArray itemArray = new JSONArray();

                itemArray.put(rs.getInt("codigo"));
                itemArray.put(rs.getString("descricao"));
                itemArray.put(rs.getString("empresa"));
                valores.put(itemArray);
            }
        }

        aaData.put("aaData", valores);

        return aaData.toString();
    }

    public List consultarParaImpressao(String filtro, String ordem, String campoOrdenacao, Integer empresa) throws SQLException {
        List<ServidorFacialVO> lista;
        try (ResultSet rs = getPS().executeQuery()) {
            lista = new ArrayList<ServidorFacialVO>();
            while (rs.next()) {
                ServidorFacialVO servidorFacialVO = new ServidorFacialVO();
                String geral = rs.getString("codigo") + rs.getString("descricao") + rs.getString("empresa");
                if (geral.toLowerCase().contains(filtro.toLowerCase())) {
                    servidorFacialVO.setCodigo(rs.getInt("codigo"));
                    servidorFacialVO.setDescricao(rs.getString("descricao"));
                    servidorFacialVO.getEmpresa().setNome(rs.getString("empresa"));
                    lista.add(servidorFacialVO);
                }
            }
        }
        if (campoOrdenacao.equals("Código")) {
            Ordenacao.ordenarLista(lista, "codigo");
        } else if (campoOrdenacao.equals("Descrição")) {
            Ordenacao.ordenarLista(lista, "descricao");
        } else if (campoOrdenacao.equals("Empresa")) {
            Ordenacao.ordenarLista(lista, "empresa_Apresentar");
        }
        if (ordem.contains("desc")) {
            Collections.reverse(lista);
        }
        return lista;

    }

    public ServidorFacialVO consultarPorChavePrimaria(Integer codigoConsulta, int nivelMontarDados) throws Exception {
        String sqlStr = "SELECT * FROM servidorfacial WHERE codigo = " + codigoConsulta;
        try (Statement stm = con.createStatement()) {
            try (ResultSet tabelaResultado = stm.executeQuery(sqlStr)) {
                if (tabelaResultado.next()) {
                    return montarDados(tabelaResultado, nivelMontarDados);
                } else {
                    return new ServidorFacialVO();
                }
            }
        }
    }

    public  ServidorFacialVO consultarPorNomePC(String nomePC) throws Exception {
        String sqlStr = "SELECT * FROM servidorfacial WHERE nomeComputador = '" + nomePC +"'";
        try (Statement stm = con.createStatement()) {
            try (ResultSet tabelaResultado = stm.executeQuery(sqlStr)) {
                if (tabelaResultado.next()) {
                    return montarDados(tabelaResultado, Uteis.NIVELMONTARDADOS_DADOSENTIDADESPRINCIPAIS);
                } else {
                    return null;
                }
            }
        }
    }

    private ServidorFacialVO montarDados(ResultSet tabelaResultado, int nivelMontarDados) throws Exception {
        ServidorFacialVO obj = montarDadosBasico(tabelaResultado);

        if (nivelMontarDados == Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA) {
            return obj;
        }
        if (nivelMontarDados == Uteis.NIVELMONTARDADOS_MINIMOS) {
            return obj;
        }
        if (nivelMontarDados == Uteis.NIVELMONTARDADOS_DADOSBASICOS) {
            return obj;
        }
        List<CameraVO> cameras = new ArrayList<CameraVO>();
        if (nivelMontarDados == Uteis.NIVELMONTARDADOS_DADOSENTIDADESPRINCIPAIS) {
            Camera cameraDAO = new Camera(getCon());
            cameras = cameraDAO.consultarCameras(obj.getCodigo(), Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA);
            obj.setCameras(cameras);
            cameraDAO = null;
            return obj;
        }
        if (obj.getEmpresa().getCodigo() == 0) {
            obj.setEmpresa(new EmpresaVO());
        } else {
            obj.setEmpresa(getFacade().getEmpresa().consultarPorChavePrimaria(obj.getEmpresa().getCodigo(), Uteis.NIVELMONTARDADOS_TELACONSULTA));
        }
        if (nivelMontarDados == Uteis.NIVELMONTARDADOS_TODOS) {
            cameras = getFacade().getCamera().consultarCameras(obj.getCodigo(), Uteis.NIVELMONTARDADOS_DADOSENTIDADESPRINCIPAIS);
        }
        obj.setCameras(cameras);
        return obj;
    }
}

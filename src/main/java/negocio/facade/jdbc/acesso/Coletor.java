/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package negocio.facade.jdbc.acesso;

import negocio.comuns.acesso.ColetorVO;
import negocio.comuns.acesso.enumerador.DispositivoAlternativoEnum;
import negocio.comuns.acesso.enumerador.ModeloColetorEnum;
import negocio.comuns.acesso.enumerador.ModoTransmissaoEnum;
import negocio.comuns.acesso.enumerador.SentidoAcessoEnum;
import negocio.comuns.crm.GenericoTO;
import negocio.comuns.utilitarias.ConsistirException;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisValidacao;
import negocio.facade.jdbc.arquitetura.SuperEntidade;
import negocio.interfaces.acesso.ColetorInterfaceFacade;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.sql.Statement;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;

/**
 *
 * <AUTHOR>
 */
public class Coletor extends SuperEntidade implements ColetorInterfaceFacade {

    protected static String idEntidade = "LocalAcesso";

    public Coletor() throws Exception {
        super();
    }

    public Coletor(Connection conexao) throws Exception {
        super(conexao);
    }

    @Override
    public void alterar(ColetorVO obj) throws Exception {
        obj.atribuirConfiguracoesEspecificas();
        this.validarColetorPorNumero(obj);
        ColetorVO.validarDados(obj);
        alterar(idEntidade);
        String sql = "UPDATE Coletor SET descricao=?, modelo=?, numSerie=?, "
                + "portaComunicacao=?,modoTransmissao=?, velocTransmissao=?, "
                + "resolucaoDPI=?, releEntrada=?, tempoReleEntrada=?, "
                + "releSaida=?, tempoReleSaida=?, msgDisplay=?, "
                + "sentidoAcesso=?, aguardaGiro=?, arquivoPrograma=?, "
                + "padraoCadastro=?, sensorentrada=?, sensorsaida=?, "
                + "numeroterminal=?, desativado=?, portaParalela=?, "
                + "portaLeitorSerial=?, dispAlternativo=?, digitosLeituraCartao=?, "
                + "inverterSinal=?, ip=?, porta=?, cartaoMaster = ?, biometrico = ?, \n"
                + "biometriaNaCatraca = ?, leitorGertec = ?, numTerminalAcionamento = ?, \n"
                + "indiceCamera = ?, \n"
                + "usarSenhaAcessoComoBiometria = ?, \n"
                + "usarCatracaOffline = ?, \n"
                + "usarNumSerieFinger = ?, \n"
                + "numSeriePlc = ?, \n"
                + "utilizarMatriculaComoSenha = ?, \n"
                + "padraoCadastroFacial = ?, \n"
                + "ipServidor = ?,\n"
                + "mascaraSubrede = ?,\n"
                + "portaServidor = ?,\n"
                + "codigo_nfc = ?,\n"
                + "usuarioColetor = ?,\n"
                + "senhaColetor = ?,\n"
                + "maxtemperatura = ?,\n"
                + "usoobrigatoriodemascara = ?,\n"
                + "usaFacial = ?,\n"
                + "usaRtsp = ?\n"
                + "WHERE codigo = ?";
        int i = 1;
        try (PreparedStatement sqlAlterar = con.prepareStatement(sql)) {
            sqlAlterar.setString(i++, obj.getDescricao());
            sqlAlterar.setString(i++, obj.getModelo().getId());
            sqlAlterar.setString(i++, obj.getNumSerie());
            sqlAlterar.setString(i++, obj.getPortaComunicacao());
            sqlAlterar.setString(i++, obj.getModoTransmissao().getId());
            sqlAlterar.setInt(i++, obj.getVelocTransmissao());
            if (obj.getResolucaoDPI() != null && obj.getResolucaoDPI() != 0) {
                sqlAlterar.setInt(i++, obj.getResolucaoDPI());
            } else {
                sqlAlterar.setNull(i++, 0);
            }
            sqlAlterar.setInt(i++, obj.getReleEntrada());
            sqlAlterar.setInt(i++, obj.getTempoReleEntrada());
            if (!UteisValidacao.emptyNumber(obj.getReleSaida())) {
                sqlAlterar.setInt(i++, obj.getReleSaida());
            } else {
                sqlAlterar.setNull(i++, 0);
            }
            sqlAlterar.setInt(i++, obj.getTempoReleSaida());
            sqlAlterar.setString(i++, obj.getMsgDisplay());
            sqlAlterar.setString(i++, obj.getSentidoAcesso().getId());
            if (obj.isPodeAguardarGiro()) {
                sqlAlterar.setBoolean(i++, obj.getAguardaGiro());
            } else {
                sqlAlterar.setBoolean(i++, true);
            }
            sqlAlterar.setBytes(i++, obj.getArquivoPrograma());
            sqlAlterar.setBoolean(i++, obj.getPadraoCadastro());
            sqlAlterar.setInt(i++, obj.getSensorEntrada());
            sqlAlterar.setInt(i++, obj.getSensorSaida());
            sqlAlterar.setInt(i++, obj.getNumeroTerminal());
            sqlAlterar.setBoolean(i++, obj.getDesativado());
            sqlAlterar.setString(i++, obj.getPortaParalela());
            sqlAlterar.setString(i++, obj.getPortaLeitorSerial());
            sqlAlterar.setInt(i++, obj.getDispAlternativo().getId());
            sqlAlterar.setInt(i++, obj.getDigitosLeituraCartao());
            sqlAlterar.setBoolean(i++, obj.getInverterSinal());
            sqlAlterar.setString(i++, obj.getIp());
            sqlAlterar.setInt(i++, obj.getPorta());
            sqlAlterar.setString(i++, obj.getCartaoMaster());
            sqlAlterar.setBoolean(i++, obj.getBiometrico());
            sqlAlterar.setBoolean(i++, obj.getBiometriaNaCatraca());
            sqlAlterar.setInt(i++, obj.getLeitorGertec());

            if (UteisValidacao.emptyNumber(obj.getNumTerminalAcionamento())) {
                sqlAlterar.setNull(i++, 0);
            } else {
                sqlAlterar.setInt(i++, obj.getNumTerminalAcionamento());
            }

            if (UteisValidacao.emptyNumber(obj.getIndiceCamera())) {
                sqlAlterar.setNull(i++, 0);
            } else {
                sqlAlterar.setInt(i++, obj.getIndiceCamera());
            }
            sqlAlterar.setBoolean(i++, obj.isUsarSenhaAcessoComoBiometria());
            sqlAlterar.setBoolean(i++, obj.isUsarCatracaOffline());
            sqlAlterar.setBoolean(i++, obj.isUsarNumSerieFinger());
            sqlAlterar.setString(i++, obj.getNumSeriePlc());
            if (obj.getUtilizarMatriculaComoSenha() == null) {
                sqlAlterar.setNull(i++, 0);
            } else {
                sqlAlterar.setBoolean(i++, obj.getUtilizarMatriculaComoSenha());
            }
            sqlAlterar.setBoolean(i++, obj.getPadraoCadastroFacial());
            sqlAlterar.setString(i++, obj.getIpServidor());
            sqlAlterar.setString(i++, obj.getMascaraSubrede());
            sqlAlterar.setInt(i++, obj.getPortaServidor());
            if (UteisValidacao.emptyString(obj.getCodigoNFC())) {
                sqlAlterar.setNull(i++, 0);
            } else {
                sqlAlterar.setString(i++, obj.getCodigoNFC().trim());
            }
            //Usuário e senha
            if (UteisValidacao.emptyString(obj.getUsuarioColetor())) {
                sqlAlterar.setNull(i++, 0);
            } else {
                sqlAlterar.setString(i++, obj.getUsuarioColetor().trim());
            }
            if (UteisValidacao.emptyString(obj.getSenhaColetor())) {
                sqlAlterar.setNull(i++, 0);
            } else {
                sqlAlterar.setString(i++, obj.getSenhaColetor().trim());
            }
            if (UteisValidacao.emptyNumber(obj.getMaxTemperatura())) {
                sqlAlterar.setNull(i++, 0);
            } else {
                sqlAlterar.setDouble(i++, obj.getMaxTemperatura());
            }
            if (obj.getUsoMascaraObrigatorio() == null) {
                sqlAlterar.setNull(i++, 0);
            } else {
                sqlAlterar.setBoolean(i++, obj.getUsoMascaraObrigatorio());
            }
            sqlAlterar.setBoolean(i++, obj.getUsaFacial());
            sqlAlterar.setBoolean(i++, obj.getUsaRtsp());

            sqlAlterar.setInt(i++, obj.getCodigo());
            sqlAlterar.execute();
        }
    }

    @Override
    public void excluir(ColetorVO obj) throws Exception {
        try {
            excluir(idEntidade);
            con.setAutoCommit(false);
            String sql = "DELETE FROM Coletor WHERE codigo = ?";
            try (PreparedStatement sqlExcluir = con.prepareStatement(sql)) {
                sqlExcluir.setInt(1, obj.getCodigo());
                sqlExcluir.execute();
            }
            con.commit();
        } catch (Exception e) {
            con.rollback();
            con.setAutoCommit(true);
            throw e;
        } finally {
            con.setAutoCommit(true);
        }
    }

    @Override
    public void incluirColetores(Integer localAcessoPrm, List objetos) throws Exception {
        Iterator e = objetos.iterator();
        while (e.hasNext()) {
            ColetorVO obj = (ColetorVO) e.next();
            obj.setLocalAcesso(localAcessoPrm);
            incluirSemCommit(obj);
            // Incluir as permissões de Liberação de acesso do coletor
            getFacade().getValidacaoLocalAcesso().incluirValidacoes(obj.getCodigo(), obj.getPermissoes());
        }
    }

    @Override
    public void excluirColetores(Integer localAcesso, List<ColetorVO> listaColetores) throws Exception {
        excluir(idEntidade);

        //Excluir as validações do coletor.
        for (ColetorVO obj : listaColetores) {
            getFacade().getValidacaoLocalAcesso().excluirValidacoes(obj.getCodigo());
        }
        String sql = "DELETE FROM Coletor WHERE (localAcesso = ?)";
        try (PreparedStatement sqlExcluir = con.prepareStatement(sql)) {
            sqlExcluir.setInt(1, localAcesso);
            sqlExcluir.execute();
        }
    }

    private ArrayList<ColetorVO> consultarColetores(Integer localAcesso, Connection con, int nivelMontarDados) throws Exception {
        consultar(getIdEntidade());
        String sql = "SELECT * FROM Coletor WHERE localAcesso = ? ORDER BY descricao";
        ArrayList<ColetorVO> objetos;
        try (PreparedStatement sqlConsulta = con.prepareStatement(sql)) {
            sqlConsulta.setInt(1, localAcesso);
            try (ResultSet resultado = sqlConsulta.executeQuery()) {

                objetos = new ArrayList<ColetorVO>();
                while (resultado.next()) {
                    ColetorVO novoObj = Coletor.montarDados(resultado, nivelMontarDados);
                    novoObj.registrarObjetoVOAntesDaAlteracao();
                    objetos.add(novoObj);
                }
            }
        }
        return objetos;
    }
    
    @Override
    public ArrayList<ColetorVO> consultarColetores(Integer localAcesso, int nivelMontarDados) throws Exception {
        return consultarColetores(localAcesso, con, nivelMontarDados);
    }

    @Override
    public Map<Integer, ColetorVO> consultarColetores() throws Exception {
        consultar(getIdEntidade());
        String sql = "SELECT * FROM Coletor order by codigo";
        Map<Integer, ColetorVO> objetos;
        try (PreparedStatement sqlConsulta = con.prepareStatement(sql)) {
            try (ResultSet resultado = sqlConsulta.executeQuery()) {
                objetos = new HashMap<Integer, ColetorVO>();
                while (resultado.next()) {
                    ColetorVO novoObj = Coletor.montarDados(resultado, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
                    novoObj.carregarPermissoes();
                    objetos.put(novoObj.getNumeroTerminal(), novoObj);
                }
            }
        }
        return objetos;
    }

    public static ColetorVO montarDadosBasico(ResultSet dadosSQL) throws Exception {
        ColetorVO obj = new ColetorVO();
        obj.setCodigo(dadosSQL.getInt("codigo"));
        obj.setDescricao(dadosSQL.getString("descricao"));
        obj.setModelo(ModeloColetorEnum.valueOf(ModeloColetorEnum.class, dadosSQL.getString("modelo")));
        obj.setNumSerie(dadosSQL.getString("numSerie"));
        obj.setPortaComunicacao(dadosSQL.getString("portaComunicacao"));
        obj.setModoTransmissao(ModoTransmissaoEnum.valueOf(ModoTransmissaoEnum.class, dadosSQL.getString("modoTransmissao")));
        obj.setVelocTransmissao(dadosSQL.getInt("velocTransmissao"));
        obj.setResolucaoDPI(dadosSQL.getInt("resolucaoDPI"));
        obj.setReleEntrada(dadosSQL.getInt("releEntrada"));
        obj.setTempoReleEntrada(dadosSQL.getInt("tempoReleEntrada"));
        obj.setReleSaida(dadosSQL.getInt("releSaida"));
        obj.setTempoReleSaida(dadosSQL.getInt("tempoReleSaida"));
        obj.setMsgDisplay(dadosSQL.getString("msgDisplay"));
        obj.setSentidoAcesso(SentidoAcessoEnum.valueOf(SentidoAcessoEnum.class, dadosSQL.getString("sentidoAcesso")));
        obj.setAguardaGiro(dadosSQL.getBoolean("aguardaGiro"));
        obj.setArquivoPrograma(dadosSQL.getBytes("arquivoPrograma"));
        obj.setPadraoCadastro(dadosSQL.getBoolean("padraoCadastro"));
        obj.setLocalAcesso(dadosSQL.getInt("localAcesso"));
        obj.setSensorEntrada(dadosSQL.getInt("sensorentrada"));
        obj.setSensorSaida(dadosSQL.getInt("sensorsaida"));
        obj.setNumeroTerminal(dadosSQL.getInt("numeroterminal"));
        obj.setDesativado(dadosSQL.getBoolean("desativado"));
        obj.setPortaParalela(dadosSQL.getString("portaParalela"));
        obj.setPortaLeitorSerial(dadosSQL.getString("portaLeitorSerial"));
        obj.setDispAlternativo(DispositivoAlternativoEnum.getEnum(dadosSQL.getInt("dispAlternativo")));
        obj.setDigitosLeituraCartao(dadosSQL.getInt("digitosLeituraCartao"));
        obj.setInverterSinal(dadosSQL.getBoolean("inverterSinal"));
        obj.setIp(dadosSQL.getString("ip"));
        obj.setPorta(dadosSQL.getInt("porta"));
        obj.setBiometrico(dadosSQL.getBoolean("biometrico"));
        obj.setCartaoMaster(dadosSQL.getString("cartaoMaster"));
        obj.setLeitorGertec(dadosSQL.getInt("leitorGertec"));
//        obj.setBiometriaNaCatraca(dadosSQL.getBoolean("biometriaNaCatraca"));
        obj.setNumTerminalAcionamento(dadosSQL.getInt("numTerminalAcionamento"));
        obj.setIndiceCamera(dadosSQL.getInt("indiceCamera"));
        obj.setUsarSenhaAcessoComoBiometria(dadosSQL.getBoolean("usarSenhaAcessoComoBiometria"));
        obj.setUsarCatracaOffline(dadosSQL.getBoolean("usarCatracaOffline"));
        obj.setUsarNumSerieFinger(dadosSQL.getBoolean("usarNumSerieFinger"));
        obj.setNumSeriePlc(dadosSQL.getString("numSeriePlc"));
        try{
            obj.setUtilizarMatriculaComoSenha(dadosSQL.getBoolean("utilizarMatriculaComoSenha"));
            obj.setPortaServidor(dadosSQL.getInt("portaServidor"));
            obj.setUsuarioColetor(dadosSQL.getString("usuarioColetor"));
            obj.setSenhaColetor(dadosSQL.getString("senhaColetor"));
            obj.setMaxTemperatura(dadosSQL.getDouble("maxtemperatura"));
            obj.setUsoMascaraObrigatorio(dadosSQL.getBoolean("usoobrigatoriodemascara"));
        }catch(Exception e){}
        obj.setPadraoCadastroFacial(dadosSQL.getBoolean("padraoCadastroFacial"));
        obj.setIpServidor(dadosSQL.getString("ipServidor"));
        obj.setMascaraSubrede(dadosSQL.getString("mascaraSubrede"));
        obj.setCodigoNFC(dadosSQL.getString("codigo_nfc"));
        try {
            obj.setUsaFacial(dadosSQL.getBoolean("usaFacial"));
        }catch (Exception ignored){}
        try {
            obj.setUsaRtsp(dadosSQL.getBoolean("usaRtsp"));
        }catch (Exception ignored){}
        return obj;
    }

    public static ColetorVO montarDados(ResultSet dadosSQL, int nivelMontarDados) throws Exception {
        ColetorVO obj = montarDadosBasico(dadosSQL);
        if (nivelMontarDados == Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA) {
            return obj;
        }

        if (nivelMontarDados == Uteis.NIVELMONTARDADOS_DADOSENTIDADESPRINCIPAIS) {
            obj.carregarPermissoes();
        }
        obj.setNovoObj(false);
        return obj;
    }

    @Override
    public void alterarColetores(Integer localAcesso, List<ColetorVO> objetos) throws Exception {
        alterar(idEntidade);
        StringBuilder sqlColNaoExcluir = new StringBuilder();
        StringBuilder sql = new StringBuilder();
        // Pegar os códigos dos coletores que não deverão ser excluídos.
        for (int i = 0; i < objetos.size(); i++) {
            ColetorVO obj = objetos.get(i);
            if (i == 0) {
                sqlColNaoExcluir.append(obj.getCodigo());
            } else {
                sqlColNaoExcluir.append(",");
                sqlColNaoExcluir.append(obj.getCodigo());
            }
        }
        if (objetos.isEmpty()) {
            sqlColNaoExcluir.append("0");
        }
        //Excluir as validações dos coletores que foram excluídos da Lista .
        sql.append("delete from validacaolocalacesso where coletor in(");
        sql.append("select codigo from coletor where localAcesso = ");
        sql.append(localAcesso);
        sql.append(" and codigo not in( ");
        sql.append(sqlColNaoExcluir.toString());
        sql.append(")");
        sql.append(")");
        try (PreparedStatement pstExcluirValidacoes = con.prepareStatement(sql.toString())) {
            pstExcluirValidacoes.execute();
        }

        // Excluir os coletores que foram excluídos da Lista de Coletores.
        sql.delete(0, sql.length());
        sql.append("DELETE FROM  Coletor  WHERE localAcesso = ");
        sql.append(localAcesso);
        sql.append(" and codigo not in(");
        sql.append(sqlColNaoExcluir.toString());
        sql.append(")");
        try (PreparedStatement sqlExcluir = con.prepareStatement(sql.toString())) {
            sqlExcluir.execute();
        }
        Iterator e = objetos.iterator();
        while (e.hasNext()) {
            ColetorVO obj = (ColetorVO) e.next();
            if (obj.getCodigo().equals(0)) {
                obj.setLocalAcesso(localAcesso);
                obj.atribuirConfiguracoesEspecificas();
                incluirSemCommit(obj);
            } else {
                alterar(obj);
            }
            // Alterar as permissões de Liberação de acesso do coletor
            getFacade().getValidacaoLocalAcesso().alterarValidacoes(obj.getCodigo(), obj.getPermissoes());
        }
    }

    @Override
    public void incluir(ColetorVO obj) throws Exception {
        obj.atribuirConfiguracoesEspecificas();
        try {
            con.setAutoCommit(false);
            incluirSemCommit(obj);
            con.commit();
        } catch (Exception e) {
            con.rollback();
            con.setAutoCommit(true);
            throw e;
        } finally {
            con.setAutoCommit(true);
        }
    }

    @Override
    public void incluirSemCommit(ColetorVO obj) throws Exception {
        ColetorVO.validarDados(obj);
        this.validarColetorPorNumero(obj);
        incluir(idEntidade);
        String sql = "INSERT INTO Coletor( descricao, modelo, numSerie, portaComunicacao,"
                + "modoTransmissao, velocTransmissao, resolucaoDPI, releEntrada, tempoReleEntrada, "
                + "releSaida, tempoReleSaida, msgDisplay, sentidoAcesso, aguardaGiro, arquivoPrograma, "
                + "padraoCadastro, localAcesso, sensorentrada, sensorsaida, "
                + "numeroterminal, desativado, portaParalela, portaLeitorSerial, "
                + "dispAlternativo, digitosLeituraCartao, inverterSinal, ip, porta, "
                + "biometrico, cartaoMaster, biometriaNaCatraca,leitorGertec, \n"
                + "numTerminalAcionamento, indiceCamera, usarSenhaAcessoComoBiometria, usarCatracaOffline, usarNumSerieFinger, numSeriePlc, utilizarMatriculaComoSenha, padraoCadastroFacial,"
                + "ipServidor, mascaraSubrede,portaServidor, codigo_nfc, usuarioColetor, senhaColetor, maxtemperatura, usoobrigatoriodemascara, usaFacial, usaRtsp)\n"
                + " VALUES ( ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, "
                + "?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?,?,?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)";
        int i = 1;
        try (PreparedStatement sqlInserir = con.prepareStatement(sql)) {
            sqlInserir.setString(i++, obj.getDescricao());
            sqlInserir.setString(i++, obj.getModelo().getId());
            sqlInserir.setString(i++, obj.getNumSerie());
            sqlInserir.setString(i++, obj.getPortaComunicacao());
            sqlInserir.setString(i++, obj.getModoTransmissao().getId());
            sqlInserir.setInt(i++, obj.getVelocTransmissao());
            if (obj.getResolucaoDPI() != null && obj.getResolucaoDPI() != 0) {
                sqlInserir.setInt(i++, obj.getResolucaoDPI());
            } else {
                sqlInserir.setNull(i++, 0);
            }
            if (UteisValidacao.emptyNumber(obj.getReleEntrada())) {
                sqlInserir.setNull(i++, 0);
            } else {
                sqlInserir.setInt(i++, obj.getReleEntrada());
            }
            if (UteisValidacao.emptyNumber(obj.getTempoReleEntrada())) {
                sqlInserir.setNull(i++, 0);
            } else {
                sqlInserir.setInt(i++, obj.getTempoReleEntrada());
            }
            if (UteisValidacao.emptyNumber(obj.getReleSaida())) {
                sqlInserir.setNull(i++, 0);
            } else {
                sqlInserir.setInt(i++, obj.getReleSaida());
            }
            sqlInserir.setInt(i++, obj.getTempoReleSaida());
            sqlInserir.setString(i++, obj.getMsgDisplay());
            sqlInserir.setString(i++, obj.getSentidoAcesso().getId());
            if (obj.getModelo() == ModeloColetorEnum.MODELO_COLETOR_TRIXSTANDARD
                    || obj.getModelo() == ModeloColetorEnum.MODELO_COLETOR_ALMITEC
                    || obj.getModelo() == ModeloColetorEnum.MODELO_COLETOR_ALMITECMAC400) {
                sqlInserir.setBoolean(i++, obj.getAguardaGiro());
            } else {
                sqlInserir.setBoolean(i++, true);
            }
            sqlInserir.setBytes(i++, obj.getArquivoPrograma());
            sqlInserir.setBoolean(i++, obj.getPadraoCadastro());
            sqlInserir.setInt(i++, obj.getLocalAcesso());
            if (UteisValidacao.emptyNumber(obj.getSensorEntrada())) {
                sqlInserir.setNull(i++, 0);
            } else {
                sqlInserir.setInt(i++, obj.getSensorEntrada());
            }
            if (UteisValidacao.emptyNumber(obj.getSensorSaida())) {
                sqlInserir.setNull(i++, 0);
            } else {
                sqlInserir.setInt(i++, obj.getSensorSaida());
            }
            sqlInserir.setInt(i++, obj.getNumeroTerminal());
            sqlInserir.setBoolean(i++, obj.getDesativado());
            sqlInserir.setString(i++, obj.getPortaParalela());
            sqlInserir.setString(i++, obj.getPortaLeitorSerial());
            sqlInserir.setInt(i++, obj.getDispAlternativo().getId());
            sqlInserir.setInt(i++, obj.getDigitosLeituraCartao());
            sqlInserir.setBoolean(i++, obj.getInverterSinal());
            sqlInserir.setString(i++, obj.getIp());
            if (obj.getPorta() != null && obj.getPorta() != 0) {
                sqlInserir.setInt(i++, obj.getPorta());
            } else {
                sqlInserir.setNull(i++, 0);
            }
            sqlInserir.setBoolean(i++, obj.getBiometrico());
            sqlInserir.setString(i++, obj.getCartaoMaster());
            sqlInserir.setBoolean(i++, obj.getBiometriaNaCatraca());
            sqlInserir.setInt(i++, obj.getLeitorGertec());

            if (!UteisValidacao.emptyNumber(obj.getNumTerminalAcionamento())) {
                sqlInserir.setInt(i++, obj.getNumTerminalAcionamento());
            } else {
                sqlInserir.setNull(i++, 0);
            }

            if (!UteisValidacao.emptyNumber(obj.getIndiceCamera())) {
                sqlInserir.setInt(i++, obj.getIndiceCamera());
            } else {
                sqlInserir.setNull(i++, 0);
            }
            sqlInserir.setBoolean(i++, obj.isUsarSenhaAcessoComoBiometria());
            sqlInserir.setBoolean(i++, obj.isUsarCatracaOffline());
            sqlInserir.setBoolean(i++, obj.isUsarCatracaOffline());
            sqlInserir.setString(i++, obj.getNumSeriePlc());
            if (obj.getUtilizarMatriculaComoSenha() == null) {
                sqlInserir.setNull(i++, 0);
            } else {
                sqlInserir.setBoolean(i++, obj.getUtilizarMatriculaComoSenha());
            }
            sqlInserir.setBoolean(i++, obj.getPadraoCadastroFacial());
            sqlInserir.setString(i++, obj.getIpServidor());
            sqlInserir.setString(i++, obj.getMascaraSubrede());
            sqlInserir.setInt(i++, obj.getPortaServidor());
            if(UteisValidacao.emptyString(obj.getCodigoNFC())) {
                sqlInserir.setNull(i++, 0);
            } else {
                sqlInserir.setString(i++,obj.getCodigoNFC().trim());
            }
            // Usuário e senha
            if(UteisValidacao.emptyString(obj.getUsuarioColetor())) {
                sqlInserir.setNull(i++, 0);
            } else {
                sqlInserir.setString(i++,obj.getUsuarioColetor().trim());
            }
            if(UteisValidacao.emptyString(obj.getSenhaColetor())) {
                sqlInserir.setNull(i++, 0);
            } else {
                sqlInserir.setString(i++,obj.getSenhaColetor().trim());
            }
            if (UteisValidacao.emptyNumber(obj.getMaxTemperatura())) {
                sqlInserir.setNull(i++, 0);
            } else {
                sqlInserir.setDouble(i++, obj.getMaxTemperatura());
            }
            if (obj.getUsoMascaraObrigatorio() == null) {
                sqlInserir.setNull(i++, 0);
            } else {
                sqlInserir.setBoolean(i++, obj.getUsoMascaraObrigatorio());
            }
            sqlInserir.setBoolean(i++, obj.getUsaFacial());
            sqlInserir.setBoolean(i++, obj.getUsaRtsp());
            sqlInserir.execute();
        }
        obj.setCodigo(obterValorChavePrimariaCodigo());
        obj.setNovoObj(false);
    }

    public ColetorVO novo() throws Exception {
        incluir(idEntidade);
        return new ColetorVO();
    }

    public ColetorVO consultarPorCodigo(Integer valorConsulta) throws Exception {
        consultar(idEntidade);
        String sqlStr = "SELECT * FROM Coletor where codigo = " + valorConsulta;
        try (Statement stm = con.createStatement()) {
            try (ResultSet tabelaResultado = stm.executeQuery(sqlStr)) {
                tabelaResultado.next();
                return (montarDados(tabelaResultado, Uteis.NIVELMONTARDADOS_DADOSBASICOS));
            }
        }
    }

    public ColetorVO consultarPorNumeroTerminal(String numTerminal, int nivelMontarDados) throws Exception {
        consultar(idEntidade);
        String sqlStr = "SELECT * FROM Coletor where numeroterminal = " + numTerminal;
        try (Statement stm = con.createStatement()) {
            try (ResultSet tabelaResultado = stm.executeQuery(sqlStr)) {
                if (tabelaResultado.next()) {
                    return (montarDados(tabelaResultado, nivelMontarDados));
                } else {
                    return new ColetorVO();
                }
            }
        }

    }

    public void validarColetorPorNumero(ColetorVO coletorVO) throws SQLException, ConsistirException {
        String sql = "select * from coletor where numeroterminal = "
                + coletorVO.getNumeroTerminal()
                + " and codigo <> " + coletorVO.getCodigo();
        try (Statement stm = con.createStatement()) {
            try (ResultSet rs = stm.executeQuery(sql)) {
                if (rs.next()) {
                    throw new ConsistirException("Já existe outro coletor com o número de terminal "
                            + coletorVO.getNumeroTerminal() + " (" + rs.getString("descricao") + ")");
                }
            }
        }
    }

    public List<ColetorVO> consultarPorDescricao(String valorConsulta, int nivelMontarDados) throws Exception {
        String sql = "select * "
                + "from Coletor "
                + "where upper(descricao) like ('" + valorConsulta.toUpperCase() + "%') order by descricao";
        try (Statement stm = con.createStatement()) {
            try (ResultSet result = stm.executeQuery(sql)) {
                return montarDadosConsulta(result, nivelMontarDados);
            }
        }
    }

    private List montarDadosConsulta(ResultSet tabelaResultado, int nivelMontarDados) throws Exception {
        List vetResultado = new ArrayList();
        while (tabelaResultado.next()) {
            ColetorVO obj = montarDados(tabelaResultado, nivelMontarDados);
            vetResultado.add(obj);
        }
        return vetResultado;
    }

	public ArrayList<ColetorVO> consultarColetores(Integer localAcesso, Connection con) throws Exception {
		return consultarColetores(localAcesso, con, Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA);
	}

    public List<GenericoTO> consultarColetoresPorNumeroTerminal(String pref, int nivelMontarDados) throws Exception {
        String sql = "SELECT c.numeroterminal, c.descricao, la.nomecomputador \n" +
                "FROM coletor c \n" +
                "INNER JOIN localacesso la ON c.localacesso = la.codigo\n" +
                "WHERE 1 = 1\n" +
                "AND c.desativado is false\n" +
                "AND c.numeroterminal::character varying like '%" + pref + "%'";
        List<GenericoTO> objetos;
        try (PreparedStatement sqlConsulta = con.prepareStatement(sql)) {
            try (ResultSet resultado = sqlConsulta.executeQuery()) {
                objetos = new ArrayList<GenericoTO>();
                while (resultado.next()) {
                    GenericoTO generico = new GenericoTO();
                    generico.setCodigo(resultado.getInt("numeroterminal"));
                    generico.setLabel(resultado.getString("descricao"));
                    generico.setCodigoString(resultado.getString("nomecomputador"));
                    objetos.add(generico);
                }
            }
        }
        return objetos;
    }

    public List<ColetorVO> consultarPorEmpresa(Integer empresa) throws Exception {
        String sqlStr = "SELECT c.numeroterminal AS numeroTerminal, " +
                " c.codigo AS codigoColetor, " +
                " c.descricao AS descricaoColetor, " +
                " c.codigo_nfc, " +
                " l.codigo AS codigoLocalAcesso, " +
                " e.longitude AS longitude," +
                " e.latitude AS latitude " +
                " FROM localacesso l " +
                "   INNER JOIN coletor c ON c.localacesso = l.codigo " +
                "   INNER JOIN empresa e ON l.empresa = e.codigo " +
                " WHERE l.empresa = " + empresa + " ORDER BY c.descricao";

        try (Statement stm = con.createStatement()) {
            try (ResultSet rs = stm.executeQuery(sqlStr)) {
                List<ColetorVO> lista = new ArrayList<>();
                while (rs.next()) {
                    ColetorVO c = new ColetorVO();
                    c.setNumeroTerminal(rs.getInt("numeroTerminal"));
                    c.setCodigo(rs.getInt("codigoColetor"));
                    c.setLocalAcesso(rs.getInt("codigoLocalAcesso"));
                    c.setDescricao(rs.getString("descricaoColetor"));
                    c.setLongitude(rs.getString("longitude"));
                    c.setLatitude(rs.getString("latitude"));
                    try {
                        c.setCodigoNFC(rs.getString("codigo_nfc"));
                    } catch (Exception e) {
                    }
                    lista.add(c);
                }
                return lista;
            }
        }
    }

    public ColetorVO consultarPorNFC(String nfc) throws Exception {
        String sqlStr = "SELECT c.numeroterminal AS numeroTerminal, " +
                " c.codigo AS codigoColetor, " +
                " c.descricao AS descricaoColetor, " +
                " c.codigo_nfc, " +
                " l.codigo AS codigoLocalAcesso, " +
                " e.longitude AS longitude," +
                " e.latitude AS latitude " +
                " FROM localacesso l INNER JOIN coletor c ON c.localacesso = l.codigo " +
                "   INNER JOIN empresa e ON l.empresa = e.codigo " +
                "   WHERE c.codigo_nfc LIKE '" + nfc + "'";

        try (Statement stm = con.createStatement()) {
            try (ResultSet rs = stm.executeQuery(sqlStr)) {

                while (rs.next()) {
                    ColetorVO c = new ColetorVO();
                    c.setNumeroTerminal(rs.getInt("numeroTerminal"));
                    c.setCodigo(rs.getInt("codigoColetor"));
                    c.setLocalAcesso(rs.getInt("codigoLocalAcesso"));
                    c.setDescricao(rs.getString("descricaoColetor"));
                    c.setLongitude(rs.getString("longitude"));
                    c.setLatitude(rs.getString("latitude"));
                    try {
                        c.setCodigoNFC(rs.getString("codigo_nfc"));
                    } catch (Exception e) {
                    }
                    return c;
                }
                return new ColetorVO();
            }
        }
    }
}

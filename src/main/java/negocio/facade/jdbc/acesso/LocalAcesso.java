/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package negocio.facade.jdbc.acesso;

import negocio.comuns.acesso.ColetorVO;
import negocio.comuns.acesso.LocalAcessoVO;
import negocio.comuns.acesso.enumerador.CategoriaLocalAcessoEnum;
import negocio.comuns.basico.EmpresaVO;
import negocio.comuns.utilitarias.Calendario;
import negocio.comuns.utilitarias.Ordenacao;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisValidacao;
import negocio.facade.jdbc.arquitetura.SuperEntidade;
import negocio.facade.jdbc.basico.Empresa;
import negocio.interfaces.acesso.LocalAcessoInterfaceFacade;
import org.json.JSONArray;
import org.json.JSONObject;

import javax.ejb.Local;
import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.sql.Statement;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Date;
import java.util.Hashtable;
import java.util.List;

/**
 *
 * <AUTHOR>
 */
public class LocalAcesso extends SuperEntidade implements LocalAcessoInterfaceFacade {

    private Hashtable coletores;

    public LocalAcesso() throws Exception {
        super();
        setIdEntidade("LocalAcesso");
        setColetores(new Hashtable());
    }

    public LocalAcesso(Connection conexao) throws Exception {
        super(conexao);
        setIdEntidade("LocalAcesso");
        setColetores(new Hashtable());
    }

    public void alterar(LocalAcessoVO obj) throws Exception {
        try {
            con.setAutoCommit(false);
            alterarSemCommit(obj);
            con.commit();
        } catch (Exception e) {
            con.rollback();
            throw e;
        } finally {
            con.setAutoCommit(true);
        }
    }

    public void alterarSemCommit(LocalAcessoVO obj) throws Exception {
        alterar(getIdEntidade());
        String sql = "UPDATE LocalAcesso SET descricao=?, nomeComputador=?, empresa=?, tempoentreacessos=?, "
                + "servidorimpressoes=?, portaservidorimp=?, pedirSenhaLibParaCadaAcesso=?, utilizarModoOffline=?,"
                + "tempoentreacessoscolaborador=?, pedirSenhaCadastrarMaisBiometrias = ?, \n"
                + "usarReconhecimento = ?, portaImagens = ?, urlServidorCamera = ?, portaServidorCamera = ?, \n"
                + "categoriaLocalAcesso = ?, solicitarJustificativaLiberacaoManual = ?, ip = ?, tempoToleranciaSaida = ?, ignorarconsumocredito = ?, servidorFacialInner = ?, portaServidorFacialInner = ?, capacidadeLimite = ?, restringirAcessoOutrasUnidades = ?  \n"
                + "WHERE codigo = ?";
        try (PreparedStatement sqlAlterar = con.prepareStatement(sql)) {
            int i = 0;
            sqlAlterar.setString(++i, obj.getDescricao());
            sqlAlterar.setString(++i, obj.getNomeComputador());
            sqlAlterar.setInt(++i, obj.getEmpresa().getCodigo());
            if (obj.getTempoEntreAcessos() == null) {
                sqlAlterar.setNull(++i, java.sql.Types.NULL);
            } else {
                sqlAlterar.setInt(++i, obj.getTempoEntreAcessos());
            }
            sqlAlterar.setString(++i, obj.getServidorImpressoes());
            if (obj.getPortaServidorImp() != null && obj.getPortaServidorImp() != 0) {
                sqlAlterar.setInt(++i, obj.getPortaServidorImp());
            } else {
                sqlAlterar.setNull(++i, 0);
            }
            sqlAlterar.setBoolean(++i, obj.getPedirSenhaLibParaCadaAcesso());
            sqlAlterar.setBoolean(++i, obj.getUtilizarModoOffline());
            if (obj.getTempoEntreAcessosColaborador() == null) {
                sqlAlterar.setNull(++i, java.sql.Types.NULL);
            } else {
                sqlAlterar.setInt(++i, obj.getTempoEntreAcessosColaborador());
            }
            sqlAlterar.setBoolean(++i, obj.getPedirSenhaCadastrarMaisBiometrias());

            sqlAlterar.setBoolean(++i, obj.getUsarReconhecimento());
            if (obj.getPortaImagens() != null && obj.getPortaImagens() != 0) {
                sqlAlterar.setInt(++i, obj.getPortaImagens());
            } else {
                sqlAlterar.setNull(++i, 0);
            }
            sqlAlterar.setString(++i, obj.getUrlServidorCamera());
            if (obj.getPortaServidorCamera() != null && obj.getPortaServidorCamera() != 0) {
                sqlAlterar.setInt(++i, obj.getPortaServidorCamera());
            } else {
                sqlAlterar.setNull(++i, 0);
            }
            if (!UteisValidacao.emptyString(obj.getCategoriaLocalAcesso())) {
                sqlAlterar.setString(++i, obj.getCategoriaLocalAcesso());
            } else {
                sqlAlterar.setNull(++i, 0);
            }

            sqlAlterar.setBoolean(++i, obj.getSolicitarJustificativaLiberacaoManual());
            sqlAlterar.setString(++i, obj.getIp());

            sqlAlterar.setInt(++i, obj.getTempoToleranciaSaida());
            sqlAlterar.setBoolean(++i, obj.getIgnorarConsumoCredito());

            sqlAlterar.setString(++i, obj.getServidorFacialInner());
            if (!UteisValidacao.emptyNumber(obj.getPortaServidorFacialInner())) {
                sqlAlterar.setInt(++i, obj.getPortaServidorFacialInner());
            } else {
                sqlAlterar.setNull(++i, 0);
            }

            sqlAlterar.setInt(++i, obj.getCapacidadeLimite());
            sqlAlterar.setBoolean(++i, obj.getRestringirAcessoOutrasUnidades());


            sqlAlterar.setInt(++i, obj.getCodigo());
            sqlAlterar.execute();
        }
        getFacade().getColetor().alterarColetores(obj.getCodigo(), obj.getListaColetores());
    }

    public LocalAcessoVO consultarPorComputador(String nomeComputador, Integer empresa) throws Exception {
        consultar(getIdEntidade());
        StringBuilder sqlStr = new StringBuilder("SELECT * FROM LocalAcesso WHERE nomeComputador = '" + nomeComputador + "'");

        if (empresa != 0) {
            sqlStr.append(" AND empresa = ").append(empresa.toString());
        }

        try (Statement stm = con.createStatement()) {
            try (ResultSet tabelaResultado = stm.executeQuery(sqlStr.toString())) {

                if (!tabelaResultado.next()) {
                    return new LocalAcessoVO();
                }

                return montarDados(tabelaResultado, Uteis.NIVELMONTARDADOS_MINIMOS, con);
            }
        }
    }

    public void excluir(LocalAcessoVO obj) throws Exception {
        try {
            //Exclui a entidade relacionada primeiro
            getFacade().getColetor().excluirColetores(obj.getCodigo(), obj.getListaColetores());

            con.setAutoCommit(false);
            String sql = "DELETE FROM LocalAcesso WHERE ((codigo = ?))";
            try (PreparedStatement sqlExcluir = con.prepareStatement(sql)) {
                sqlExcluir.setInt(1, obj.getCodigo());
                sqlExcluir.execute();
            }

            con.commit();
        } catch (Exception e) {
            con.rollback();
            con.setAutoCommit(true);
            throw e;
        } finally {
            con.setAutoCommit(true);
        }
    }

    public void incluir(LocalAcessoVO obj) throws Exception {
        try {
            con.setAutoCommit(false);
            novo();
            LocalAcessoVO.validarDados(obj);
            String sql = "INSERT INTO LocalAcesso( descricao, nomeComputador, empresa, tempoentreacessos, " +
                    "servidorimpressoes, portaservidorimp, pedirSenhaLibParaCadaAcesso, utilizarModoOffline, " +
                    "tempoentreacessoscolaborador, pedirSenhaCadastrarMaisBiometrias, " +
                    "usarReconhecimento, portaImagens, urlServidorCamera, portaServidorCamera, categoriaLocalAcesso, " +
                    "solicitarJustificativaLiberacaoManual, ip, tempoToleranciaSaida, servidorFacialInner, portaServidorFacialInner, capacidadeLimite, restringirAcessoOutrasUnidades) VALUES ( ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)";

            try (PreparedStatement sqlInserir = con.prepareStatement(sql)) {
                int i = 0;
                sqlInserir.setString(++i, obj.getDescricao());
                sqlInserir.setString(++i, obj.getNomeComputador());
                sqlInserir.setInt(++i, obj.getEmpresa().getCodigo());
                sqlInserir.setInt(++i, obj.getTempoEntreAcessos());
                sqlInserir.setString(++i, obj.getServidorImpressoes());
                if (obj.getPortaServidorImp() != null && obj.getPortaServidorImp() != 0) {
                    sqlInserir.setInt(++i, obj.getPortaServidorImp());
                } else {
                    sqlInserir.setNull(++i, 0);
                }
                sqlInserir.setBoolean(++i, obj.getPedirSenhaLibParaCadaAcesso());
                sqlInserir.setBoolean(++i, obj.getUtilizarModoOffline());
                sqlInserir.setInt(++i, obj.getTempoEntreAcessosColaborador());
                sqlInserir.setBoolean(++i, obj.getPedirSenhaCadastrarMaisBiometrias());

                //SETA O ATRIBUTO DE RECONHECIMENTO FACIAL DE ACORDO DE A URL DO SERVIDOR DA CAMERA FOI PREENCHIDO
                if (obj.getUrlServidorCamera().isEmpty()) {
                    sqlInserir.setBoolean(++i, false);
                } else {
                    sqlInserir.setBoolean(++i, true);
                }

                if (obj.getPortaImagens() != null && obj.getPortaImagens() != 0) {
                    sqlInserir.setInt(++i, obj.getPortaImagens());
                } else {
                    sqlInserir.setNull(++i, 0);
                }
                sqlInserir.setString(++i, obj.getUrlServidorCamera());
                if (obj.getPortaServidorCamera() != null && obj.getPortaServidorCamera() != 0) {
                    sqlInserir.setInt(++i, obj.getPortaServidorCamera());
                } else {
                    sqlInserir.setNull(++i, 0);
                }
                if (!UteisValidacao.emptyString(obj.getCategoriaLocalAcesso())) {
                    sqlInserir.setString(++i, obj.getCategoriaLocalAcesso());
                } else {
                    sqlInserir.setNull(++i, 0);
                }
                sqlInserir.setBoolean(++i, obj.getSolicitarJustificativaLiberacaoManual());
                sqlInserir.setString(++i, obj.getIp());
                sqlInserir.setInt(++i, obj.getTempoToleranciaSaida());

                sqlInserir.setString(++i, obj.getServidorFacialInner());
                if (!UteisValidacao.emptyNumber(obj.getPortaServidorFacialInner())) {
                    sqlInserir.setInt(++i, obj.getPortaServidorFacialInner());
                } else {
                    sqlInserir.setNull(++i, 0);
                }

                sqlInserir.setInt(++i, obj.getCapacidadeLimite());
                sqlInserir.setBoolean(++i, obj.getRestringirAcessoOutrasUnidades());

                sqlInserir.execute();
            }

            obj.setCodigo(obterValorChavePrimariaCodigo());
            getFacade().getColetor().incluirColetores(obj.getCodigo(), obj.getListaColetores());

            con.commit();
            obj.setNovoObj(false);
        } catch (Exception e) {
            con.rollback();
            con.setAutoCommit(true);
            throw e;
        } finally {
            con.setAutoCommit(true);
        }
    }

    public void incluirSemCommit(LocalAcessoVO obj) throws Exception {
        novo();
        LocalAcessoVO.validarDados(obj);
        String sql = "INSERT INTO LocalAcesso( descricao, nomeComputador, empresa, tempoentreacessos, " +
                "servidorimpressoes, portaservidorimp, pedirSenhaLibParaCadaAcesso, utilizarModoOffline, " +
                "tempoentreacessoscolaborador, pedirSenhaCadastrarMaisBiometrias, " +
                "usarReconhecimento, portaImagens, urlServidorCamera, portaServidorCamera, solicitarJustificativaLiberacaoManual, ip, ignorarconsumocredito, servidorFacialInner, portaServidorFacialInner, capacidadeLimite, restringirAcessoOutrasUnidades) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)";
        try (PreparedStatement sqlInserir = con.prepareStatement(sql)) {
            int i = 0;
            sqlInserir.setString(++i, obj.getDescricao());
            sqlInserir.setString(++i, obj.getNomeComputador());
            sqlInserir.setInt(++i, obj.getEmpresa().getCodigo());
            sqlInserir.setInt(++i, obj.getTempoEntreAcessos());
            sqlInserir.setString(++i, obj.getServidorImpressoes());
            if (obj.getPortaServidorImp() != null && obj.getPortaServidorImp() != 0) {
                sqlInserir.setInt(++i, obj.getPortaServidorImp());
            } else {
                sqlInserir.setNull(++i, 0);
            }
            sqlInserir.setBoolean(++i, obj.getPedirSenhaLibParaCadaAcesso());
            sqlInserir.setBoolean(++i, obj.getUtilizarModoOffline());
            sqlInserir.setInt(++i, obj.getTempoEntreAcessosColaborador());
            sqlInserir.setBoolean(++i, obj.getPedirSenhaCadastrarMaisBiometrias());

            sqlInserir.setBoolean(++i, obj.getUsarReconhecimento());
            if (obj.getPortaImagens() != null && obj.getPortaImagens() != 0) {
                sqlInserir.setInt(++i, obj.getPortaImagens());
            } else {
                sqlInserir.setNull(++i, 0);
            }
            sqlInserir.setString(++i, obj.getUrlServidorCamera());
            if (obj.getPortaServidorCamera() != null && obj.getPortaServidorCamera() != 0) {
                sqlInserir.setInt(++i, obj.getPortaServidorCamera());
            } else {
                sqlInserir.setNull(++i, 0);
            }
            sqlInserir.setBoolean(++i, obj.getSolicitarJustificativaLiberacaoManual());
            sqlInserir.setString(++i, obj.getIp());
            sqlInserir.setBoolean(++i, obj.getIgnorarConsumoCredito());
            sqlInserir.setString(++i, obj.getServidorFacialInner());
            if (!UteisValidacao.emptyNumber(obj.getPortaServidorFacialInner())) {
                sqlInserir.setInt(++i, obj.getPortaServidorFacialInner());
            } else {
                sqlInserir.setNull(++i, 0);
            }

            sqlInserir.setInt(++i, obj.getCapacidadeLimite());
            sqlInserir.setBoolean(++i, obj.getRestringirAcessoOutrasUnidades());
            sqlInserir.execute();
        }
        obj.setCodigo(obterValorChavePrimariaCodigo());
        getFacade().getColetor().incluirColetores(obj.getCodigo(), obj.getListaColetores());
        obj.setNovoObj(false);
    }

    public LocalAcessoVO novo() throws Exception {
        incluir(getIdEntidade());
        return new LocalAcessoVO();
    }

    public Hashtable getColetores() {
        return coletores;
    }

    public void setColetores(Hashtable coletores) {
        this.coletores = coletores;
    }

    public List consultarPorCodigo(Integer valorConsulta, int empresa, boolean controlarAcesso, int nivelMontarDados) throws Exception {
        consultar(getIdEntidade(), controlarAcesso);
        String sqlStr = "SELECT * FROM LocalAcesso WHERE codigo = " + valorConsulta.toString();

        if (empresa == 0) {
            sqlStr += " ORDER BY codigo";
        } else {
            sqlStr += " AND empresa = " + empresa;
        }

        try (Statement stm = con.createStatement()) {
            try (ResultSet tabelaResultado = stm.executeQuery(sqlStr)) {
                return (montarDadosConsulta(tabelaResultado, nivelMontarDados, con));
            }
        }
    }

    public LocalAcessoVO consultarPorCodigo(Integer valorConsulta, int nivelMontarDados) throws Exception {
        String sqlStr = "SELECT * FROM LocalAcesso WHERE codigo = " + valorConsulta.toString();

        try (Statement stm = con.createStatement()) {
            try (ResultSet tabelaResultado = stm.executeQuery(sqlStr)) {
                if (tabelaResultado.next()) {
                    return (montarDados(tabelaResultado, nivelMontarDados, con));
                } else {
                    return new LocalAcessoVO();
                }
            }
        }
    }

    public List consultarPorNome(String valorConsulta, boolean controlarAcesso, int nivelMontarDados) throws Exception {
        consultar(getIdEntidade(), controlarAcesso);
        String sqlStr = "SELECT * FROM LocalAcesso WHERE upper( descricao ) like('" + valorConsulta.toUpperCase() + "%') ORDER BY descricao";
        try (Statement stm = con.createStatement()) {
            try (ResultSet tabelaResultado = stm.executeQuery(sqlStr)) {
                return (montarDadosConsulta(tabelaResultado, nivelMontarDados, con));
            }
        }
    }

    public static List<LocalAcessoVO> montarDadosConsulta(ResultSet tabelaResultado, int nivelMontarDados, Connection con) throws Exception {
        List<LocalAcessoVO> vetResultado = new ArrayList<LocalAcessoVO>();
        while (tabelaResultado.next()) {
            LocalAcessoVO obj = montarDados(tabelaResultado, nivelMontarDados, con);
            vetResultado.add(obj);
        }
        return vetResultado;
    }

    public static LocalAcessoVO montarDadosBasico(ResultSet dadosSQL) throws Exception {
        LocalAcessoVO obj = new LocalAcessoVO();
        obj.setNovoObj(false);
        obj.setCodigo(dadosSQL.getInt("codigo"));
        obj.setDescricao(dadosSQL.getString("descricao"));
        obj.setNomeComputador(dadosSQL.getString("nomecomputador"));
        obj.setTempoEntreAcessos(dadosSQL.getInt("tempoentreacessos"));
        obj.setTempoEntreAcessosColaborador(dadosSQL.getInt("tempoentreacessoscolaborador"));
        obj.setServidorImpressoes(dadosSQL.getString("servidorimpressoes"));
        obj.setPortaServidorImp(dadosSQL.getInt("portaservidorimp"));
        obj.setPedirSenhaLibParaCadaAcesso(dadosSQL.getBoolean("pedirSenhaLibParaCadaAcesso"));
        obj.setUtilizarModoOffline(dadosSQL.getBoolean("utilizarModoOffline"));
        obj.setEmpresa(new EmpresaVO());
        obj.getEmpresa().setCodigo(dadosSQL.getInt("empresa"));
        obj.setDataBaseOffline(dadosSQL.getTimestamp("databaseoffline"));
        obj.setDataDownloadBase(dadosSQL.getTimestamp("datadownloadbase"));
        try {
            obj.setCapacidadeLimite(dadosSQL.getInt("capacidadelimite"));
            obj.setRestringirAcessoOutrasUnidades(dadosSQL.getBoolean("restringirAcessoOutrasUnidades"));
        } catch (Exception ignored) {}
        try{
            obj.setVersaoAcesso(dadosSQL.getString("versaoAcesso"));
            obj.setPedirSenhaCadastrarMaisBiometrias(dadosSQL.getBoolean("pedirSenhaCadastrarMaisBiometrias"));
            obj.setSolicitarJustificativaLiberacaoManual(dadosSQL.getBoolean("solicitarJustificativaLiberacaoManual"));

            obj.setUsarReconhecimento(dadosSQL.getBoolean("usarReconhecimento"));
            obj.setPortaImagens(dadosSQL.getInt("portaImagens"));
            obj.setUrlServidorCamera(dadosSQL.getString("urlServidorCamera"));
            obj.setPortaServidorCamera(dadosSQL.getInt("portaServidorCamera"));

            obj.setCategoriaLocalAcesso(dadosSQL.getString("categoriaLocalAcesso"));
            obj.setIp(dadosSQL.getString("ip"));
            obj.setTempoToleranciaSaida(dadosSQL.getInt("tempoToleranciaSaida"));
            obj.setIgnorarConsumoCredito(dadosSQL.getBoolean("ignorarconsumocredito"));
        }catch (Exception ignored){
        }

        try{
            obj.setServidorFacialInner(dadosSQL.getString("servidorFacialInner"));
            obj.setPortaServidorFacialInner(dadosSQL.getInt("portaServidorFacialInner"));
        } catch (Exception ignored){
        }
        return obj;
    }

    public static LocalAcessoVO montarDados(ResultSet dadosSQL, int nivelMontarDados, Connection con) throws Exception {
        LocalAcessoVO obj = montarDadosBasico(dadosSQL);
        Coletor coletorDAO = new Coletor(con);
        Empresa empresaDAO = new Empresa(con);

        if (nivelMontarDados == Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA) {
            return obj;
        }

        if (nivelMontarDados == Uteis.NIVELMONTARDADOS_MINIMOS) {
            return obj;
        }
        ArrayList<ColetorVO> listaColetores = new ArrayList<ColetorVO>();
        if (nivelMontarDados == Uteis.NIVELMONTARDADOS_DADOSENTIDADESPRINCIPAIS) {
            listaColetores = coletorDAO.consultarColetores(obj.getCodigo(), Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA);
            obj.setListaColetores(listaColetores);
            return obj;
        }
        if (obj.getEmpresa().getCodigo() == 0) {
            obj.setEmpresa(new EmpresaVO());
        } else {
            obj.setEmpresa(empresaDAO.consultarPorChavePrimaria(obj.getEmpresa().getCodigo(), Uteis.NIVELMONTARDADOS_TELACONSULTA));
        }
        if (nivelMontarDados == Uteis.NIVELMONTARDADOS_TODOS) {
            listaColetores = coletorDAO.consultarColetores(obj.getCodigo(), Uteis.NIVELMONTARDADOS_DADOSENTIDADESPRINCIPAIS);
        }
        obj.setListaColetores(listaColetores);
        coletorDAO = null;
        empresaDAO = null;
        return obj;
    }

    public String consultarJSON() throws Exception {
        JSONObject aaData = new JSONObject();

        try (ResultSet rs = getPS().executeQuery()) {
            JSONArray lista = new JSONArray();
            while (rs.next()) {
                JSONArray itemLista = new JSONArray();
                itemLista.put(rs.getString("codigo"));
                itemLista.put(rs.getString("descricao"));
                itemLista.put(rs.getString("empresa"));
                lista.put(itemLista);
            }
            aaData.put("aaData", lista);
        }

        return aaData.toString();
    }

    private PreparedStatement getPS() throws SQLException {
        String sql = "SELECT\n" + "  la.codigo, descricao, e.nome as empresa\n" + "FROM localacesso la\n" + "  LEFT JOIN empresa e ON la.empresa = e.codigo\n" + "  ORDER BY descricao";
        return con.prepareStatement(sql);
    }

    public List consultarParaImpressao(String filtro, String ordem, String campoOrdenacao, Integer empresa) throws SQLException {

        List<LocalAcessoVO> lista;
        try (ResultSet rs = getPS().executeQuery()) {
            lista = new ArrayList<LocalAcessoVO>();

            while (rs.next()) {

                LocalAcessoVO local = new LocalAcessoVO();
                String geral = rs.getString("codigo") + rs.getString("descricao") + rs.getString("empresa");
                if (geral.toLowerCase().contains(filtro.toLowerCase())) {
                    local.setCodigo(rs.getInt("codigo"));
                    local.setDescricao(rs.getString("descricao"));
                    local.getEmpresa().setNome(rs.getString("empresa"));
                    lista.add(local);
                }
            }
        }
        if (campoOrdenacao.equals("Código")) {
            Ordenacao.ordenarLista(lista, "codigo");
        } else if (campoOrdenacao.equals("Descrição")) {
            Ordenacao.ordenarLista(lista, "descricao");
        } else if (campoOrdenacao.equals("Empresa")) {
            Ordenacao.ordenarLista(lista, "empresa_Apresentar");
        }
        if (ordem.contains("desc")) {
            Collections.reverse(lista);
        }
        return lista;

    }

    private List<LocalAcessoVO> consultarPorEmpresa(Integer empresa,boolean integracao, Connection con, int nivelMontarDados) throws Exception {
        String sqlStr = "SELECT * FROM LocalAcesso WHERE empresa = " + empresa ;
        if(integracao){
            sqlStr += " and utilizarmodooffline = false ";
        }
        sqlStr += " ORDER BY descricao";
        try (Statement stm = con.createStatement()) {
            try (ResultSet tabelaResultado = stm.executeQuery(sqlStr)) {
                return (montarDadosConsulta(tabelaResultado, nivelMontarDados, con));
            }
        }
    }

    public List<LocalAcessoVO> consultarPorEmpresa(Integer empresa,boolean integracao, int nivelMontarDados) throws Exception {
        return consultarPorEmpresa(empresa, integracao, con, nivelMontarDados);
    }

    public List<LocalAcessoVO> consultarPorEmpresa(Integer empresa, Connection con) throws Exception {
        return consultarPorEmpresa(empresa,false, con, Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA);
    }

    @Override
    public void atualizarDadosOffLocalAcesso(LocalAcessoVO localAcesso) throws Exception {
        DadosAcessoOffline off = new DadosAcessoOffline(con);
        off.preencherDadosLocalAcesso(localAcesso.getCodigo(), localAcesso.getEmpresa().getCodigo());

        localAcesso.setDataBaseOffline(Calendario.hoje());
        alterarDataBaseOffline(localAcesso, localAcesso.getDataBaseOffline());
    }

    @Override
    public void alterarDataBaseOffline(LocalAcessoVO localAcesso, Date dataBaseOffline) throws Exception {
        try {
            con.setAutoCommit(false);
            StringBuilder sql = new StringBuilder();
            sql.append("UPDATE localacesso SET databaseoffline=?, datadownloadbase = NULL WHERE 1 = 1 AND utilizarmodooffline IS TRUE\n");
            if (localAcesso != null) {
                sql.append("AND codigo = ?");
            }
            try (PreparedStatement sqlAlterar = con.prepareStatement(sql.toString())) {
                sqlAlterar.setTimestamp(1, Uteis.getDataJDBCTimestamp(dataBaseOffline));
                if (localAcesso != null) {
                    sqlAlterar.setInt(2, localAcesso.getCodigo());
                }
                sqlAlterar.execute();
            }

            con.commit();
        } catch (Exception e) {
            con.rollback();
            con.setAutoCommit(true);
            throw e;
        } finally {
            con.setAutoCommit(true);
        }
    }

    @Override
    public void alterarDataDownloadBase(Integer codLocalAcesso, Date dataDownloadBase) throws Exception {
        try (PreparedStatement sqlAlterar = con.prepareStatement("UPDATE localacesso SET datadownloadbase=? WHERE codigo = ?")) {
            sqlAlterar.setTimestamp(1, Uteis.getDataJDBCTimestamp(dataDownloadBase));
            sqlAlterar.setInt(2, codLocalAcesso);
            sqlAlterar.execute();
        }
    }

    @Override
    public void alterarVersaoAcesso(Integer codLocalAcesso, String versao,String parametros) throws Exception {
        try (PreparedStatement sqlAlterar = con.prepareStatement("UPDATE localacesso SET versaoAcesso =? , parametros = ? WHERE codigo = ?")) {
            sqlAlterar.setString(1, versao);
            sqlAlterar.setString(2, parametros);
            sqlAlterar.setInt(3, codLocalAcesso);
            sqlAlterar.execute();
        }
    }

    @Override
    public boolean utilizaModoOffLine(Integer codLocalAcesso) throws Exception {
        return existe("select codigo from localacesso where codigo = "
                + codLocalAcesso + " and utilizarmodooffline", con);
    }

    @Override
    public boolean temDadosOffLine(Integer codLocalAcesso) throws Exception {
        return existe("select exists(select codigo from localacesso where codigo ="
                + codLocalAcesso + " and coalesce(databaseoffline, '1899-12-30') >  current_date)", con);
    }

    @Override
    public Date ultimoDadosOffLineGerado(Integer codLocalAcesso) throws Exception {
        try (ResultSet rs = criarConsulta("select databaseoffline from localacesso where codigo = " + codLocalAcesso, con)) {
            if (rs.next()) {
                return Uteis.getDataJDBC(rs.getTimestamp(1));
            }
        }
        return null;
    }

    @Override
    public void alterarIpLocalAcesso(Integer codLocalAcesso, String ip) throws Exception {
        try (PreparedStatement sqlAlterar = con.prepareStatement("UPDATE localacesso SET ip = ? WHERE codigo = ?")) {
            sqlAlterar.setString(1, ip);
            sqlAlterar.setInt(2, codLocalAcesso);
            sqlAlterar.execute();
        }
    }

    @Override
    public String obterIp(String categoria) throws Exception {
        JSONArray arrayLocais = new JSONArray();

        CategoriaLocalAcessoEnum categoriaLocalAcessoEnum = CategoriaLocalAcessoEnum.getCategoria(categoria);

        StringBuilder sb = new StringBuilder();
        sb.append("SELECT codigo, ip FROM localacesso WHERE coalesce(ip, '') <> ''\n");
        if (categoriaLocalAcessoEnum != null) {
            sb.append("and categoriaLocalAcesso = '").append(categoriaLocalAcessoEnum.toString()).append("'\n");
        }

        try (ResultSet rs = criarConsulta(sb.toString(), con)) {
            while (rs.next()) {
                JSONObject objLocalAcesso = new JSONObject();
                objLocalAcesso.put("codigo", rs.getInt("codigo"));
                objLocalAcesso.put("ip", rs.getString("ip"));
                arrayLocais.put(objLocalAcesso);
            }
        }
        return arrayLocais.toString();
    }

    public boolean consultarExisteNFC(String nfc, Integer codigo) throws Exception {
        String sqlStr = "select count(0)\n" +
                        "from coletor c " +
                        "where c.codigo_nfc = '" + nfc + "' and not codigo = " + codigo;
        try (Statement stm = con.createStatement()) {
            try (ResultSet rs = stm.executeQuery(sqlStr)) {
                while(rs.next())
                {
                    Integer s = rs.getInt("count");
                    if(s >= 1)
                    {
                        return true;
                    }
                }
            } catch (Exception e) {
            }
        } catch (Exception e) {
        }
        return false;
    }
}

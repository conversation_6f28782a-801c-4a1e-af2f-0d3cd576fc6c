/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */

package negocio.facade.jdbc.acesso;

import java.sql.Connection;
import java.sql.PreparedStatement;
import negocio.interfaces.acesso.ValidacaoLocalAcessoInterfaceFacade;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.sql.Statement;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import negocio.comuns.acesso.ColetorVO;
import negocio.comuns.acesso.ComparatorValidacaoPorEmpresa;
import negocio.comuns.acesso.TipoHorarioEnum;
import negocio.comuns.acesso.TipoValidacaoEnum;
import negocio.comuns.acesso.ValidacaoLocalAcessoVO;
import negocio.comuns.arquitetura.UsuarioVO;
import negocio.comuns.basico.EmpresaVO;
import negocio.comuns.plano.HorarioVO;
import negocio.comuns.utilitarias.Uteis;
import negocio.facade.jdbc.arquitetura.SuperEntidade;
/**
 *
 * <AUTHOR>
 */
public class ValidacaoLocalAcesso extends SuperEntidade implements ValidacaoLocalAcessoInterfaceFacade {

    public ValidacaoLocalAcesso() throws Exception{
        super();
    }

    public ValidacaoLocalAcesso(Connection conexao) throws Exception{
        super(conexao);
    }

    public static List montarDadosConsulta(ResultSet tabelaResultado, int nivelMontarDados) throws Exception {
        List vetResultado = new ArrayList();
        while (tabelaResultado.next()) {
            ValidacaoLocalAcessoVO obj = new ValidacaoLocalAcessoVO();
            obj = montarDados(tabelaResultado, nivelMontarDados);
            vetResultado.add(obj);
        }
        return vetResultado;
    }

    public static ValidacaoLocalAcessoVO montarDadosBasico(ResultSet dadosSQL) throws Exception{
        ValidacaoLocalAcessoVO obj = new ValidacaoLocalAcessoVO();
        obj.setCodigo(dadosSQL.getInt("codigo"));
        obj.setDataRegistro(dadosSQL.getTimestamp("dataRegistro"));
        obj.setChave(dadosSQL.getInt("chave"));
        obj.setTipoHorario(TipoHorarioEnum.valueOf(dadosSQL.getInt("tipoHorario")));
        obj.setTipoValidacao(TipoValidacaoEnum.valueOf(dadosSQL.getInt("tipoValidacao")));
        obj.setEmpresa(new EmpresaVO());
        obj.getEmpresa().setCodigo(dadosSQL.getInt("empresa"));
        obj.setColetor(new ColetorVO());
        obj.getColetor().setCodigo(dadosSQL.getInt("coletor"));
        obj.setHorario(new HorarioVO());
        obj.getHorario().setCodigo(dadosSQL.getInt("horario"));
        obj.setUsuario(new UsuarioVO());
        obj.getUsuario().setCodigo(dadosSQL.getInt("usuario"));

        return obj;
    }

    public static ValidacaoLocalAcessoVO montarDados(ResultSet dadosSQL,
                                                     int nivelMontarDados) throws Exception {

        ValidacaoLocalAcessoVO obj = montarDadosBasico(dadosSQL);

        if (nivelMontarDados == Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA)
            return obj;

        if ((obj.getTipoValidacao() == TipoValidacaoEnum.TIPOVALIDACAO_Produto) &&
                (obj.getChave() > 0 )){
            // Consultar as informações do produto.
            obj.setProduto(getFacade().getProduto().consultarPorChavePrimaria(obj.getChave(), Uteis.NIVELMONTARDADOS_DADOSBASICOS));
        }
        if ((obj.getTipoValidacao() == TipoValidacaoEnum.TIPOVALIDACAO_Modalidade) &&
                (obj.getChave() > 0 )){
            // Consultar as informações do produto.
            obj.setModalidade(getFacade().getModalidade().consultarPorChavePrimaria(obj.getChave(), Uteis.NIVELMONTARDADOS_DADOSBASICOS));
        }


        if (obj.getEmpresa().getCodigo().intValue() != 0) {
            obj.setEmpresa(getFacade().getEmpresa().consultarPorChavePrimaria(
                    obj.getEmpresa().getCodigo(), Uteis.NIVELMONTARDADOS_TELACONSULTA));
        }
        if (obj.getColetor().getCodigo().intValue() != 0){
            obj.setColetor(getFacade().getColetor().consultarPorCodigo(obj.getColetor().getCodigo()));
        }
        if (obj.getHorario().getCodigo().intValue() != 0){
            obj.setHorario(getFacade().getHorario().consultarPorChavePrimaria(obj.getHorario().getCodigo(),
                    Uteis.NIVELMONTARDADOS_TODOS));
        }
        if (obj.getUsuario().getCodigo().intValue() != 0){
            obj.setUsuario(getFacade().getUsuario().consultarPorChavePrimaria(obj.getUsuario().getCodigo(),
                    Uteis.NIVELMONTARDADOS_MINIMOS));
        }

        obj.setNovoObj(false);
        return obj;
    }

    public List<ValidacaoLocalAcessoVO> consultar(final String condicao,
                                                  final int nivelMontarDados) throws SQLException, Exception{
        StringBuilder sb = new StringBuilder();
        sb.append("select * from ValidacaoLocalAcesso ");
        sb.append(" where %1$s");
        sb.append(" order by codigo");
        Statement stm = con.createStatement();
        ResultSet tabelaResultado = stm.executeQuery(String.format(sb.toString(), new Object[]{
                condicao
        }));

        return (montarDadosConsulta(tabelaResultado, nivelMontarDados));

    }

    private void verificaTipoValidacaoTodos(List<ValidacaoLocalAcessoVO> listaValidacoes) throws Exception{
        /*
         * Não permitir cadastrar mais de uma regra para a mesma empresa, se a mesma
         * já tiver a regra de Tipo Validação igual a "todos", pois esta regra anulará
         * as demais regras.
         *
         */
        String msgErro = "Se houver uma validação onde o 'Tipo Validação:' for igual a 'Todos', não é permitido incluir qualquer outra validação para a empresa.";
        if ((listaValidacoes != null) && (listaValidacoes.size() >1)){
            // Ordenar a lista de validações por Empresa
            Collections.sort(listaValidacoes, new ComparatorValidacaoPorEmpresa());
            String empresaAnt = "";
            int totValidacao = 0;
            boolean tipoValTodos = false;
            for (ValidacaoLocalAcessoVO validacao: listaValidacoes){
                if (empresaAnt.equals(validacao.getEmpresa().getNome())){
                    if (tipoValTodos)
                        throw new Exception(msgErro);
                    totValidacao ++;
                    if ((validacao.getTipoValidacao() == TipoValidacaoEnum.TIPOVALIDACAO_Todos) &&
                            (totValidacao > 1)) {
                        throw new Exception(msgErro);
                    }
                }else{
                    totValidacao = 1;
                    tipoValTodos = (validacao.getTipoValidacao() == TipoValidacaoEnum.TIPOVALIDACAO_Todos);
                }
                empresaAnt = validacao.getEmpresa().getNome();
            }
        }
    }

    public void alterarValidacoes(Integer coletorPrm, List<ValidacaoLocalAcessoVO> listaValidacoes) throws Exception{
        verificaTipoValidacaoTodos(listaValidacoes);

        // Excluir as validações que foram excluídos da Lista de Validações.
        StringBuilder sql = new StringBuilder();
        sql.append("delete from validacaolocalacesso where coletor = ");
        sql.append(coletorPrm);
        sql.append(" and codigo not in( ");
        for (int i=0; i<listaValidacoes.size(); i++){
            ValidacaoLocalAcessoVO obj = listaValidacoes.get(i);
            if (i == 0)
                sql.append(obj.getCodigo());
            else{
                sql.append(",");
                sql.append(obj.getCodigo());
            }
        }
        if (listaValidacoes.isEmpty())
            sql.append("0");
        sql.append(")");
        PreparedStatement pstExcluir = con.prepareStatement(sql.toString());
        pstExcluir.execute();
        sql = null;

        for (ValidacaoLocalAcessoVO obj : listaValidacoes){
            obj.getColetor().setCodigo(coletorPrm);
            if (obj.getCodigo().intValue() <= 0 )
                incluirSemCommit(obj);
            else
                alterarSemCommit(obj);
        }
    }

    public void incluirValidacoes(Integer coletorPrm, List<ValidacaoLocalAcessoVO> listaValidacoes) throws Exception{
        verificaTipoValidacaoTodos(listaValidacoes);
        for (ValidacaoLocalAcessoVO obj : listaValidacoes){
            obj.getColetor().setCodigo(coletorPrm);
            incluirSemCommit(obj);
        }
    }


    //private validar

    private void alterarSemCommit(ValidacaoLocalAcessoVO obj) throws Exception {
        ValidacaoLocalAcessoVO.validarDados(obj);
        String sql = "update validacaolocalacesso set tipovalidacao =?,chave=?, tipohorario=?, horario=?, empresa=? where (codigo =?)";
        PreparedStatement sqlAlterar = con.prepareStatement(sql);
        sqlAlterar.setInt(1, obj.getTipoValidacao().getId());
        if ((obj.getTipoValidacao() == TipoValidacaoEnum.TIPOVALIDACAO_Modalidade) ||
                (obj.getTipoValidacao() == TipoValidacaoEnum.TIPOVALIDACAO_Produto) ||
                (obj.getTipoValidacao() == TipoValidacaoEnum.TIPOVALIDACAO_ProdutoGymPass))
            sqlAlterar.setInt(2, obj.getChave());
        else
            sqlAlterar.setNull(2, java.sql.Types.NULL);
        sqlAlterar.setInt(3, obj.getTipoHorario().getId());
        if (obj.getTipoHorario() == TipoHorarioEnum.TIPOHORARIO_HorarioEspecifico)
            sqlAlterar.setInt(4, obj.getHorario().getCodigo());
        else
            sqlAlterar.setNull(4, java.sql.Types.NULL);
        sqlAlterar.setInt(5, obj.getEmpresa().getCodigo());
        sqlAlterar.setInt(6, obj.getCodigo());
        sqlAlterar.execute();

    }

    private void incluirSemCommit(ValidacaoLocalAcessoVO obj) throws Exception {
        ValidacaoLocalAcessoVO.validarDados(obj);
        String sql = "insert into validacaolocalacesso (coletor, tipovalidacao,chave, tipohorario, horario, usuario, dataregistro, empresa) values (?,?,?,?,?,?,?,?)";
        PreparedStatement sqlIncluir = con.prepareStatement(sql);
        sqlIncluir.setInt(1, obj.getColetor().getCodigo());
        sqlIncluir.setInt(2, obj.getTipoValidacao().getId());
        if ((obj.getTipoValidacao() == TipoValidacaoEnum.TIPOVALIDACAO_Modalidade) ||
                (obj.getTipoValidacao() == TipoValidacaoEnum.TIPOVALIDACAO_Produto) ||
                (obj.getTipoValidacao() == TipoValidacaoEnum.TIPOVALIDACAO_ProdutoGymPass))
            sqlIncluir.setInt(3, obj.getChave());
        else
            sqlIncluir.setNull(3, java.sql.Types.NULL);
        sqlIncluir.setInt(4, obj.getTipoHorario().getId());
        if (obj.getTipoHorario() == TipoHorarioEnum.TIPOHORARIO_HorarioEspecifico)
            sqlIncluir.setInt(5, obj.getHorario().getCodigo());
        else
            sqlIncluir.setNull(5, java.sql.Types.NULL);
        sqlIncluir.setInt(6, obj.getUsuario().getCodigo());
        sqlIncluir.setDate(7, Uteis.getDataJDBC(obj.getDataRegistro()));
        sqlIncluir.setInt(8, obj.getEmpresa().getCodigo());
        sqlIncluir.execute();
        obj.setCodigo(obterValorChavePrimariaCodigo());
        obj.setNovoObj(false);
    }

    public void excluirValidacoes(Integer coletorPrm) throws Exception{
        String sql = "DELETE FROM validacaolocalacesso WHERE (coletor = ?)";
        PreparedStatement sqlExcluir = con.prepareStatement(sql);
        sqlExcluir.setInt(1, coletorPrm.intValue());
        sqlExcluir.execute();
    }

    /*author   : Ulisses
     * Data    : 15/02/11
     * Objetivo: Consultar as validações pelo código do coletor.
     */
    public List<ValidacaoLocalAcessoVO> consultarPorColetor(final Integer codigoColetor, final int nivelMontarDados) throws SQLException, Exception{
        StringBuilder sb = new StringBuilder();
        sb.append("select * from ValidacaoLocalAcesso where coletor = " + codigoColetor);
        sb.append(" order by codigo");
        Statement stm = con.createStatement();
        ResultSet tabelaResultado = stm.executeQuery(sb.toString());
        List lista = new ArrayList();
        while (tabelaResultado.next()) {
            ValidacaoLocalAcessoVO obj = new ValidacaoLocalAcessoVO();
            obj = montarDados(tabelaResultado, nivelMontarDados);
            lista.add(obj);
        }
        return lista;
    }


}

package negocio.facade.jdbc.acesso;

import negocio.comuns.acesso.IntegracaoMemberVO;
import negocio.comuns.acesso.integracao.member.MemberDataJson;
import negocio.comuns.acesso.integracao.member.MemberVO;
import negocio.comuns.utilitarias.Calendario;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisValidacao;
import negocio.facade.jdbc.arquitetura.SuperEntidade;
import negocio.interfaces.acesso.MemberInterfaceFacade;
import org.json.JSONArray;
import org.json.JSONObject;
import java.util.Date;

import java.sql.*;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

public class Member extends SuperEntidade implements MemberInterfaceFacade {

    public Member() throws Exception {
        super();
    }

    public Member(Connection conexao) throws Exception {
        super(conexao);
    }

    @Override
    public void incluir(MemberVO obj) throws Exception {
        String sql = "INSERT INTO member(idMember, firstName, lastName, registerDate, idBranch, branchName, document," +
                " documentId, status, lastAccessDate, photoUrl, idMemberMembership, startDate, endDate, nameMembership," +
                " cancelDate, membershipStatus, lastsync, integracaomember, sincronizado, memberjson, salesjson, receivablesjson)\n"
                + " VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?::json, ?::json, ?::json)";
        int i = 0;
        try (PreparedStatement sqlInserir = con.prepareStatement(sql)) {
            sqlInserir.setInt(++i, obj.getIdMember());
            sqlInserir.setString(++i, obj.getFirstName());
            sqlInserir.setString(++i, obj.getLastName());
            sqlInserir.setString(++i, obj.getRegisterDate());
            sqlInserir.setInt(++i, obj.getIdBranch());
            sqlInserir.setString(++i, obj.getBranchName());
            sqlInserir.setString(++i, obj.getDocument());
            sqlInserir.setString(++i, obj.getDocumentId());
            sqlInserir.setString(++i, obj.getStatus());
            sqlInserir.setString(++i, obj.getLastAccessDate());
            sqlInserir.setString(++i, obj.getPhotoUrl());
            if (!UteisValidacao.emptyNumber(obj.getIdMemberMembership())) {
                sqlInserir.setInt(++i, obj.getIdMemberMembership());
            } else {
                sqlInserir.setNull(++i, 0);
            }
            sqlInserir.setString(++i, obj.getStartDate());
            sqlInserir.setString(++i, obj.getEndDate());
            sqlInserir.setString(++i, obj.getNameMembership());
            sqlInserir.setString(++i, obj.getCancelDate());
            sqlInserir.setString(++i, obj.getMembershipStatus());
            if (obj.getLastSync() == null) {
                sqlInserir.setNull(++i, Types.TIMESTAMP);
            } else {
                sqlInserir.setTimestamp(++i, Uteis.getDataJDBCTimestamp(obj.getLastSync()));
            }
            sqlInserir.setInt(++i, obj.getIntegracaoMemberVO().getCodigo());

            sqlInserir.setBoolean(++i, obj.isSincronizado());

            if (obj.getMemberJson() == null) {
                sqlInserir.setNull(++i, Types.VARCHAR);
            } else {
                sqlInserir.setString(++i, obj.getMemberJson().toString());
            }

            if (obj.getSalesJson() == null) {
                sqlInserir.setNull(++i, Types.VARCHAR);
            } else {
                sqlInserir.setString(++i, obj.getSalesJson().toString());
            }

            if (obj.getReceivablesJson() == null) {
                sqlInserir.setNull(++i, Types.VARCHAR);
            } else {
                sqlInserir.setString(++i, obj.getReceivablesJson().toString());
            }

            sqlInserir.execute();
        }
        obj.setCodigo(obterValorChavePrimariaCodigo());
        obj.setNovoObj(false);
    }

    @Override
    public void alterar(MemberVO obj) throws Exception {
        String sql = "UPDATE member SET idMember = ?, firstName = ?, lastName = ?, registerDate = ?, idBranch = ?,\n" +
                " branchName = ?, document = ?, documentId = ?, status = ?, lastAccessDate = ?, photoUrl = ?,\n" +
                " idMemberMembership = ?, startDate = ?, endDate = ?, nameMembership = ?, cancelDate = ?,\n" +
                " membershipStatus = ?, lastsync = ?, integracaomember = ?, sincronizado = ?, memberjson = ?::json, salesjson = ?::json, receivablesjson = ?::json  \n" +
                " WHERE codigo = ?";
        int i = 0;
        try (PreparedStatement sqlAlterar = con.prepareStatement(sql)) {
            sqlAlterar.setInt(++i, obj.getIdMember());
            sqlAlterar.setString(++i, obj.getFirstName());
            sqlAlterar.setString(++i, obj.getLastName());
            sqlAlterar.setString(++i, obj.getRegisterDate());
            sqlAlterar.setInt(++i, obj.getIdBranch());
            sqlAlterar.setString(++i, obj.getBranchName());
            sqlAlterar.setString(++i, obj.getDocument());
            sqlAlterar.setString(++i, obj.getDocumentId());
            sqlAlterar.setString(++i, obj.getStatus());
            sqlAlterar.setString(++i, obj.getLastAccessDate());
            sqlAlterar.setString(++i, obj.getPhotoUrl());

            if (UteisValidacao.emptyNumber(obj.getIdMemberMembership())) {
                sqlAlterar.setNull(++i, Types.INTEGER);
            } else {
                sqlAlterar.setInt(++i, obj.getIdMemberMembership());
            }

            if (UteisValidacao.emptyString(obj.getStartDate())) {
                sqlAlterar.setNull(++i, Types.VARCHAR);
            } else {
                sqlAlterar.setString(++i, obj.getStartDate());
            }

            if (UteisValidacao.emptyString(obj.getEndDate())) {
                sqlAlterar.setNull(++i, Types.VARCHAR);
            } else {
                sqlAlterar.setString(++i, obj.getEndDate());
            }

            if (UteisValidacao.emptyString(obj.getNameMembership())) {
                sqlAlterar.setNull(++i, Types.VARCHAR);
            } else {
                sqlAlterar.setString(++i, obj.getNameMembership());
            }

            if (UteisValidacao.emptyString(obj.getCancelDate())) {
                sqlAlterar.setNull(++i, Types.VARCHAR);
            } else {
                sqlAlterar.setString(++i, obj.getCancelDate());
            }

            if (UteisValidacao.emptyString(obj.getMembershipStatus())) {
                sqlAlterar.setNull(++i, Types.VARCHAR);
            } else {
                sqlAlterar.setString(++i, obj.getMembershipStatus());
            }

            if (obj.getLastSync() == null) {
                sqlAlterar.setNull(++i, Types.TIMESTAMP);
            } else {
                sqlAlterar.setTimestamp(++i, Uteis.getDataJDBCTimestamp(obj.getLastSync()));
            }

            sqlAlterar.setInt(++i, obj.getIntegracaoMemberVO().getCodigo());

            sqlAlterar.setBoolean(++i, obj.isSincronizado());

            if (obj.getMemberJson() == null) {
                sqlAlterar.setNull(++i, Types.VARCHAR);
            } else {
                sqlAlterar.setString(++i, obj.getMemberJson().toString());
            }

            if (obj.getSalesJson() == null) {
                sqlAlterar.setNull(++i, Types.VARCHAR);
            } else {
                sqlAlterar.setString(++i, obj.getSalesJson().toString());
            }

            if (obj.getReceivablesJson() == null) {
                sqlAlterar.setNull(++i, Types.VARCHAR);
            } else {
                sqlAlterar.setString(++i, obj.getReceivablesJson().toString());
            }

            sqlAlterar.setInt(++i, obj.getCodigo());
            sqlAlterar.execute();
        }
    }

    @Override
    public void excluir(MemberVO obj) throws Exception {
        String sql = "DELETE FROM member WHERE codigo = ?";
        try (PreparedStatement sqlExcluir = con.prepareStatement(sql)) {
            sqlExcluir.setInt(1, obj.getCodigo());
            sqlExcluir.execute();
        }
    }

    @Override
    public MemberVO consultarPorIdMember(Integer idMember) throws Exception {
        String sql = "SELECT * FROM member WHERE idMember = ?";
        int i = 0;
        try (PreparedStatement ps = con.prepareStatement(sql)) {
            ps.setInt(++i, idMember);
            try (ResultSet rs = ps.executeQuery()) {
                if (rs.next()) {
                    return montarDados(rs);
                }
            }
        }
        return null;
    }

    private MemberVO montarDados(ResultSet rs) throws Exception {
        MemberVO memberVO = new MemberVO();
        memberVO.setCodigo(rs.getInt("codigo"));
        memberVO.setIdMember(rs.getInt("idMember"));
        memberVO.setFirstName(rs.getString("firstName"));
        memberVO.setLastName(rs.getString("lastName"));
        memberVO.setRegisterDate(rs.getString("registerDate"));
        memberVO.setIdBranch(rs.getInt("idBranch"));
        memberVO.setBranchName(rs.getString("branchName"));
        memberVO.setDocument(rs.getString("document"));
        memberVO.setDocumentId(rs.getString("documentId"));
        memberVO.setStatus(rs.getString("status"));
        memberVO.setLastAccessDate(rs.getString("lastAccessDate"));
        memberVO.setPhotoUrl(rs.getString("photoUrl"));

        memberVO.setIdMemberMembership(rs.getInt("idMemberMembership"));
        memberVO.setStartDate(rs.getString("startDate"));
        memberVO.setEndDate(rs.getString("endDate"));
        memberVO.setNameMembership(rs.getString("nameMembership"));
        memberVO.setCancelDate(rs.getString("cancelDate"));
        memberVO.setMembershipStatus(rs.getString("membershipStatus"));

        memberVO.setLastSync(rs.getTimestamp("lastSync"));
        memberVO.getIntegracaoMemberVO().setCodigo(rs.getInt("integracaoMember"));
        montarDadosIntegracaoMember(memberVO);
        return memberVO;
    }

    private void montarDadosIntegracaoMember(MemberVO memberVO) throws Exception {
        if (!UteisValidacao.emptyNumber(memberVO.getIntegracaoMemberVO().getCodigo())) {
            IntegracaoMember integracaoMemberDAO = new IntegracaoMember(con);
            IntegracaoMemberVO integracaoMemberVO = integracaoMemberDAO.consultarPorCodigo(memberVO.getIntegracaoMemberVO().getCodigo());
            memberVO.setIntegracaoMemberVO(integracaoMemberVO);
        }
    }

    @Override
    public List<MemberVO> consultarMembersParaSincronizar(IntegracaoMemberVO integracao) throws Exception {
        String sql = "SELECT *\n" +
                "FROM member\n" +
                "WHERE coalesce(lastsync, '2022-01-01'::DATE) < '" + Uteis.getDataJDBC(Calendario.hoje()) + "'::DATE\n";
        if (integracao != null) {
            sql += "AND integracaomember = " + integracao.getCodigo() + "\n";
        }

        List<MemberVO> membersToSync = new ArrayList<>();

        try (PreparedStatement ps = con.prepareStatement(sql);
             ResultSet rs = ps.executeQuery()) {
            while (rs.next()) {
                MemberVO memberVO = montarDados(rs);
                membersToSync.add(memberVO);
            }
        } catch (Exception e) {
            throw new Exception(e);
        }
        return membersToSync;
    }

    @Override
    public List<MemberDataJson> obterMembersImportar(Integer codigoIntegracaoMember, List<Integer> idsMembers, Date dataBaseSincronizacao) throws Exception {
        String sql = "SELECT * FROM \"member\" m\n" +
                "WHERE m.integracaomember = " + codigoIntegracaoMember + " \n" +
                "AND m.memberjson is not null \n" +
                "AND m.salesjson is not null \n" +
                "AND m.receivablesjson is not null \n" +
                "AND m.sincronizado is false \n";
        if (!UteisValidacao.emptyList(idsMembers)) {
            sql += "AND m.idmember IN (" + idsMembers.stream().map(String::valueOf).collect(Collectors.joining(",")) + ") \n";
        }
        if (dataBaseSincronizacao != null ) {
            sql += "AND m.lastsync > '" + Calendario.getDataAplicandoFormatacao(dataBaseSincronizacao, "yyyy-MM-dd HH:mm:ss.SSS") + "' \n";
        }


        List<MemberDataJson> memberDataJsons = new ArrayList<>();

        try (PreparedStatement ps = con.prepareStatement(sql);
             ResultSet rs = ps.executeQuery()) {
            while (rs.next()) {
                MemberDataJson memberDataJson = new MemberDataJson();
                memberDataJson.setCodigoMember(rs.getInt("codigo"));
                memberDataJson.setIdMember(rs.getInt("idmember"));
                memberDataJson.setMemberJson(new JSONObject(rs.getString("memberjson")));
                memberDataJson.setSalesJson(new JSONArray(rs.getString("salesjson")));
                memberDataJson.setReceivablesJson(new JSONArray(rs.getString("receivablesjson")));
                memberDataJsons.add(memberDataJson);
            }
        } catch (Exception e) {
            throw new Exception(e);
        }
        return memberDataJsons;
    }

    public List<Integer> obterIdsMembersPorIntegracaoMember(Integer codigoIntegracaoMember) throws Exception {
        String sql = "SELECT idmember\n" +
                "FROM member\n" +
                "WHERE integracaomember = ?";

        List<Integer> idsMembers = new ArrayList<>();

        try (PreparedStatement ps = con.prepareStatement(sql)) {
            ps.setInt(1, codigoIntegracaoMember);
            try (ResultSet rs = ps.executeQuery()) {
                while (rs.next()) {
                    idsMembers.add(rs.getInt("idmember"));
                }
            }
        } catch (Exception e) {
            throw new Exception(e);
        }
        return idsMembers;
    }

    public void setarSincronizado(Integer codigoMember, boolean sincronizado) throws Exception {
        String sql = "UPDATE member SET sincronizado = ? WHERE codigo = ?";
        try (PreparedStatement ps = con.prepareStatement(sql)) {
            ps.setBoolean(1, sincronizado);
            ps.setInt(2, codigoMember);
            ps.execute();
        }
    }
}

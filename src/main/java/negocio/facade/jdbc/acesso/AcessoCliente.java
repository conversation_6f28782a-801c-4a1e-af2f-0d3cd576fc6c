/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package negocio.facade.jdbc.acesso;

import br.com.pactosolucoes.enumeradores.ProcessoAjusteGeralEnum;
import br.com.pactosolucoes.oamd.controle.basico.DAO;
import negocio.comuns.acesso.*;
import negocio.comuns.acesso.auxiliar.PessoaAcesso;
import negocio.comuns.acesso.enumerador.DirecaoAcessoEnum;
import negocio.comuns.acesso.enumerador.MeioIdentificacaoEnum;
import negocio.comuns.acesso.enumerador.SituacaoAcessoEnum;
import negocio.comuns.arquitetura.UsuarioVO;
import negocio.comuns.basico.ClienteVO;
import negocio.comuns.basico.EmpresaVO;
import negocio.comuns.basico.PessoaVO;
import negocio.comuns.plano.AcessoRealizadoTO;
import negocio.comuns.plano.AcessoBloqueadoTO;
import negocio.comuns.plano.AlunoHorarioTurmaTO;
import negocio.comuns.plano.HorarioTurmaVO;
import negocio.comuns.utilitarias.Calendario;
import negocio.comuns.utilitarias.ConsistirException;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisJSON;
import negocio.comuns.utilitarias.UteisValidacao;
import negocio.comuns.utilitarias.UtilReflection;
import negocio.facade.jdbc.arquitetura.*;
import negocio.facade.jdbc.basico.Email;
import negocio.facade.jdbc.basico.Empresa;
import negocio.facade.jdbc.basico.LogAjusteGeral;
import negocio.facade.jdbc.basico.Pessoa;
import negocio.facade.jdbc.basico.Telefone;
import negocio.interfaces.acesso.AcessoClienteInterfaceFacade;
import negocio.interfaces.acesso.ColetorInterfaceFacade;
import negocio.interfaces.acesso.LocalAcessoInterfaceFacade;
import negocio.interfaces.arquitetura.UsuarioInterfaceFacade;
import negocio.interfaces.basico.ClienteInterfaceFacade;
import negocio.interfaces.contrato.ControleCreditoTreinoInterfaceFacade;
import org.json.JSONArray;
import org.json.JSONObject;
import relatorio.negocio.comuns.basico.ResumoPessoaBIAcessoGymPassVO;
import relatorio.negocio.comuns.sad.SituacaoClienteSinteticoDWVO;
import servicos.bi.exportador.formatadores.FormatadorData;

import java.sql.*;
import java.text.SimpleDateFormat;
import java.time.LocalDateTime;
import java.util.*;
import java.util.Date;
import java.util.logging.Level;
import java.util.logging.Logger;

/**
 * <AUTHOR>
 */
public class AcessoCliente extends SuperEntidade implements AcessoClienteInterfaceFacade {

    protected static String idEntidade;

    public AcessoCliente() throws Exception {
        super();
        setIdEntidade("AcessoCliente");
    }

    public AcessoCliente(Connection conexao) throws Exception {
        super(conexao);
        setIdEntidade("AcessoCliente");
    }

    /*
     * 08/02/11 Ulisses...
     * Objetivo do método: Registrar a saída do aluno de forma rápida no banco de dados.
     */
    private AcessoClienteVO registrarSaida(Date dataAcesso,
                                           ClienteVO cliente,
                                           MeioIdentificacaoEnum meioIdentificacao,
                                           SituacaoAcessoEnum situacao,
                                           Integer localAcesso,
                                           UsuarioVO usuarioVO,
                                           ColetorVO coletor,
                                           String nomeCodEmpresaAcessou) throws Exception {

        PreparedStatement pstUa = con.prepareStatement("select codigo from acessocliente where cliente = " + cliente.getCodigo() + " and dthrentrada > '" + Uteis.getDataJDBC(dataAcesso) + "' and localacesso = " + localAcesso + "  order by dthrentrada desc limit 1");
        ResultSet rsUa = pstUa.executeQuery();
        if (rsUa.next()) {
            cliente.setUaCliente(new AcessoClienteVO());
            cliente.getUaCliente().setCodigo(rsUa.getInt("codigo"));
        }
        if ((cliente.getUaCliente() == null)
                || (cliente.getUaCliente().getCodigo() == null)
                || (cliente.getUaCliente().getCodigo() == 0)) {
            //throw new Exception("Erro ao registrar Saída. Não foi encontrado o último acesso do cliente. ");
            return registrarSaidaSemEntrada(dataAcesso, cliente, usuarioVO, situacao, localAcesso, coletor, meioIdentificacao, nomeCodEmpresaAcessou);
        }
        try {
            String sql = "update acessoCliente "
                    + "set dthrSaida = ?, sentido=?, meioIdentificacaoSaida=? "
                    + "where codigo = ? ";

            PreparedStatement pst = con.prepareStatement(sql);
            pst.setTimestamp(1, new java.sql.Timestamp(dataAcesso.getTime()));
            pst.setString(2, "S");
            pst.setInt(3, meioIdentificacao.getCodigo());
            pst.setInt(4, cliente.getUaCliente().getCodigo());
            pst.execute();
            AcessoClienteVO obj = new AcessoClienteVO();
            obj.setCodigo(cliente.getUaCliente().getCodigo());
            obj.setSentido("S");
            obj.setDataHoraSaida(dataAcesso);
            return obj;
        } catch (Exception e) {
            Logger.getLogger(getClass().getSimpleName()).log(Level.SEVERE, "#### ERRO AO REGISTRAR SAIDA DO CLIENTE. ERRO: " + e.getMessage());
            throw e;
        }
    }

    private AcessoClienteVO registrarSaidaSemEntrada(Date dataAcesso, ClienteVO cliente, UsuarioVO usuarioVO, SituacaoAcessoEnum situacao, Integer codigoLocalAcesso, ColetorVO coletor, MeioIdentificacaoEnum meioIdentificacao, String nomeCodEmpresaAcessou) throws Exception {
        try {
            String sql = "INSERT INTO acessocliente (cliente, dthrentrada, dthrSaida, sentido, situacao, localacesso, coletor, usuario, meioIdentificacaoEntrada, nomeCodEmpresaAcessou) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)";
            PreparedStatement pst = con.prepareStatement(sql);
            pst.setInt(1, cliente.getCodigo());
            pst.setTimestamp(2, Uteis.getDataJDBCTimestamp(dataAcesso));
            pst.setTimestamp(3, Uteis.getDataJDBCTimestamp(dataAcesso));
            pst.setString(4, "S");
            pst.setString(5, situacao.name());
            pst.setInt(6, codigoLocalAcesso);
            pst.setInt(7, coletor.getCodigo());
            if (UtilReflection.objetoMaiorQueZero(usuarioVO, "getCodigo()")) {
                pst.setInt(8, usuarioVO.getCodigo());

            } else {
                pst.setNull(8, Types.NULL);
            }
            pst.setInt(9, meioIdentificacao.getCodigo());
            resolveStringNull(pst, 10, nomeCodEmpresaAcessou);
            pst.execute();
            AcessoClienteVO obj = new AcessoClienteVO();
            obj.setCodigo(obterValorChavePrimariaCodigo());
            obj.setSentido("S");
            obj.setDataHoraEntrada(dataAcesso);
            obj.setDataHoraSaida(dataAcesso);
            return obj;
        } catch (Exception e) {
            Logger.getLogger(getClass().getSimpleName()).log(Level.SEVERE, "#### ERRO AO REGISTRAR SAIDA SEM ENTRADA(CLIENTE). ERRO: " + e.getMessage());
            throw e;
        }
    }

    @Override
    public void excluir(AcessoClienteVO acessoVO, String observacao, ControleCreditoTreinoInterfaceFacade controleCreditoTreino) throws Exception {
        String sql = "DELETE FROM acessoCliente WHERE codigo = ? ";
        PreparedStatement pst = con.prepareStatement(sql);
        pst.setInt(1, acessoVO.getCodigo());
        pst.execute();
        controleCreditoTreino.atualizarUtilizacaoParaFaltaExclusaoAcesso(acessoVO, observacao, false);
    }

    @Override
    public AcessoClienteVO registrarAcessoClienteSemControleTreino(Date dataAcesso,
                                                                   ClienteVO cliente,
                                                                   SituacaoAcessoEnum situacao,
                                                                   DirecaoAcessoEnum direcao,
                                                                   LocalAcessoVO local,
                                                                   ColetorVO coletor,
                                                                   UsuarioVO usuario,
                                                                   MeioIdentificacaoEnum meioIdentificacao,
                                                                   String key) throws Exception {
        AcessoClienteVO acesso;
        acesso = this.novo();
        acesso.setKey(key);
        acesso.setCliente(cliente);
        acesso.setColetor(coletor);
        acesso.setDataHoraEntrada(dataAcesso);
        acesso.setLocalAcesso(local);
        acesso.setUsuario(usuario);
        acesso.setSituacao(situacao);
        acesso.setMeioIdentificacaoEntrada(meioIdentificacao);
        acesso.setSentido("E");
        this.incluirSemControleTreino(acesso);

        return acesso;
    }

    @Override
    public AcessoClienteVO registrarAcessoCliente(Date dataAcesso, ClienteVO cliente, SituacaoAcessoEnum situacao,
                                                  DirecaoAcessoEnum direcao, LocalAcessoVO local, ColetorVO coletor,
                                                  UsuarioVO usuario, MeioIdentificacaoEnum meioIdentificacao, ControleCreditoTreinoInterfaceFacade controleCreditoTreino, String key) throws Exception {
        return registrarAcessoCliente(dataAcesso, cliente, situacao, direcao, local, coletor, usuario, meioIdentificacao, controleCreditoTreino, key, "", "", null);
    }

    @Override
    public AcessoClienteVO registrarAcessoCliente(Date dataAcesso, ClienteVO cliente, SituacaoAcessoEnum situacao,
                                                  DirecaoAcessoEnum direcao, LocalAcessoVO local, ColetorVO coletor,
                                                  UsuarioVO usuario, MeioIdentificacaoEnum meioIdentificacao, ControleCreditoTreinoInterfaceFacade controleCreditoTreino, String key, String timeZoneId, String nomeCodEmpresaAcessou, AutorizacaoAcessoGrupoEmpresarialVO autorizacao) throws Exception {

        return registrarAcessoCliente(dataAcesso, cliente, situacao, direcao, local, coletor, usuario, meioIdentificacao, controleCreditoTreino, key, timeZoneId, null, nomeCodEmpresaAcessou, autorizacao);
    }

    @Override
    public AcessoClienteVO registrarAcessoCliente(Date dataAcesso, ClienteVO cliente, SituacaoAcessoEnum situacao, DirecaoAcessoEnum direcao, LocalAcessoVO local, ColetorVO coletor, UsuarioVO usuario, MeioIdentificacaoEnum meioIdentificacao, ControleCreditoTreinoInterfaceFacade controleCreditoTreino, String key, String timeZoneId, LiberacaoAcessoVO liberacaoAcessoVO, String nomeCodEmpresaAcessou, AutorizacaoAcessoGrupoEmpresarialVO autorizacao) throws Exception {
        return registrarAcessoCliente(dataAcesso, cliente, situacao, direcao, local, coletor, usuario, meioIdentificacao, controleCreditoTreino, key, timeZoneId, null, nomeCodEmpresaAcessou, autorizacao, true);
    }

    @Override
    public AcessoClienteVO registrarAcessoCliente(Date dataAcesso, ClienteVO cliente, SituacaoAcessoEnum situacao,
                                                  DirecaoAcessoEnum direcao, LocalAcessoVO local, ColetorVO coletor,
                                                  UsuarioVO usuario, MeioIdentificacaoEnum meioIdentificacao,
                                                  ControleCreditoTreinoInterfaceFacade controleCreditoTreino,
                                                  String key, String timeZoneId, LiberacaoAcessoVO liberacaoAcessoVO, String nomeCodEmpresaAcessou, AutorizacaoAcessoGrupoEmpresarialVO autorizacao, boolean descontarCreditoTreino) throws Exception {

        try {
            if (UteisValidacao.emptyString(nomeCodEmpresaAcessou)) {
                if (local != null && local.getEmpresa() != null && !UteisValidacao.emptyNumber(local.getEmpresa().getCodigo())) {
                    Empresa empDAO = new Empresa(con);
                    try {
                        EmpresaVO emp = empDAO.consultarPorCodigo(local.getEmpresa().getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS);
                        nomeCodEmpresaAcessou = emp.getCodigo() + " - " + emp.getNome();
                    } catch (Exception ignore) {
                        nomeCodEmpresaAcessou = "";
                    } finally {
                        empDAO = null;
                    }
                }
            }
        } catch (Exception ignore) {
            nomeCodEmpresaAcessou = "";
        }

        if (direcao == DirecaoAcessoEnum.DA_ENTRADA) {
            AcessoClienteVO obj = this.novo();
            obj.setKey(key);
            obj.setCliente(cliente);
            obj.setColetor(coletor);
            obj.setDataHoraEntrada(dataAcesso);
            obj.setLocalAcesso(local);
            obj.setUsuario(usuario);
            obj.setSituacao(situacao);
            obj.setMeioIdentificacaoEntrada(meioIdentificacao);
            obj.setSentido("E");
            obj.setNomeCodEmpresaAcessou(nomeCodEmpresaAcessou);
            if (autorizacao != null && !UteisValidacao.emptyNumber(autorizacao.getCodigo()) &&
                    autorizacao.getIntegracao() != null && !UteisValidacao.emptyNumber(autorizacao.getIntegracao().getCodigo())) {
                obj.setNomeCodEmpresaOrigem(autorizacao.getIntegracao().getEmpresaRemota().getCodigo() + " - " + autorizacao.getIntegracao().getNomeEmpresa());
                obj.setNomeCpfEmailClienteOrigem(autorizacao.getCodigoMatricula() + ";" + autorizacao.getNomePessoa() + ";" + autorizacao.getCpf());
            }

            if (timeZoneId.isEmpty()) {
                obj.setDataRegistro(Calendario.hoje());
            } else {
                obj.setDataRegistro(Calendario.hoje(timeZoneId));
            }
            Integer result = this.incluir(obj, controleCreditoTreino, descontarCreditoTreino);
            notificarAcesso(obj, local.getEmpresa() != null ? local.getEmpresa().getCodigo() : null);
            return obj;
        } else if (direcao == DirecaoAcessoEnum.DA_SAIDA) {
            AcessoClienteVO obj = registrarSaida(dataAcesso, cliente, meioIdentificacao, situacao, local.getCodigo(), usuario, coletor, nomeCodEmpresaAcessou);
            notificarAcesso(obj, local.getEmpresa() != null ? local.getEmpresa().getCodigo() : null);
            return obj;
        }
        return null;
    }

    private void notificarAcesso(AcessoClienteVO acessoClienteVO, Integer empresa) {
        colocarAcessoNoMemcached(acessoClienteVO);
        Empresa empresaDAO;
        ZillyonWebFacade zillyonWebFacade;
        try {
            empresaDAO = new Empresa(this.con);
            if (acessoClienteVO != null &&
                    !UteisValidacao.emptyNumber(acessoClienteVO.getCodigo()) &&
                    !UteisValidacao.emptyNumber(empresa) &&
                    empresaDAO.isNotificarWebhook(empresa)) {
                zillyonWebFacade = new ZillyonWebFacade(this.con);
                zillyonWebFacade.notificarAcesso(acessoClienteVO);
            }
        } catch (Exception ex) {
//            ex.printStackTrace();
        } finally {
            empresaDAO = null;
            zillyonWebFacade = null;
        }
    }


    public void colocarAcessoNoMemcached(AcessoClienteVO acessoClienteVO) {
        try {
            String chave = DAO.resolveKeyFromConnection(con);
            String key = "accessos_recentes:" + chave;
            List<String> accessos = (List<String>) MemCachedManager.getInstance().getMemcachedClient().get(key);
            if(accessos == null){
                accessos = new ArrayList<>();
            }
            accessos.add(acessoClienteVO.getCliente().getCodigo() + ";" + Uteis.getDataAplicandoFormatacao(acessoClienteVO.getDataHoraEntrada(), "ddMMHHmm"));
            MemCachedManager.getInstance().getMemcachedClient().set(key, 86400, accessos);
        } catch (Exception e) {
            Logger.getLogger(getClass().getSimpleName()).log(Level.SEVERE, "#### ERRO AO ARMAZENAR ACESSO NO CACHE. ERRO: " + e.getMessage());
        }
    }


    @Override
    public List<AcessoClienteVO> consultarTodosAcessos(ClienteVO cliente, int nivelMontarDados) throws Exception {
        String sql = "SELECT * FROM acessocliente where cliente = ?";
        PreparedStatement stm = con.prepareStatement(sql);
        stm.setInt(1, cliente.getCodigo());

        stm.setFetchDirection(ResultSet.FETCH_REVERSE);
        ResultSet resultTabela = stm.executeQuery();

        return montarDadosConsulta(resultTabela, nivelMontarDados);
    }

    @Override
    public AcessoClienteVO consultarUltimoAcesso(ClienteVO cliente, int nivelMontarDados) throws Exception {
        String sql = "SELECT * FROM AcessoCliente WHERE cliente = ? "
                + "order by dthrentrada desc, codigo ASC limit 1";
        PreparedStatement sqlConsultar = con.prepareStatement(sql);
        sqlConsultar.setInt(1, cliente.getCodigo());
        ResultSet tabelaResultado = sqlConsultar.executeQuery();

        if (!tabelaResultado.next()) {
            return new AcessoClienteVO();
        } else {
            return montarDados(tabelaResultado, nivelMontarDados);
        }
    }

    @Override
    public List<AcessoClienteVO> consultarUltimoAcessoDia(ClienteVO cliente, Date dia, MeioIdentificacaoEnum meioIdentificacao, int nivelMontarDados) throws Exception {
        StringBuilder sql = new StringBuilder();
        sql.append(" SELECT * FROM AcessoCliente WHERE cliente = " + cliente.getCodigo());
        sql.append(" AND dthrentrada BETWEEN '" + Uteis.getDataJDBC(dia) + " 00:00:00' AND '" + Uteis.getDataJDBC(dia) + " 23:59:59' ");
        if (meioIdentificacao != null) {
            sql.append(" AND meioIdentificacaoEntrada = " + meioIdentificacao.getCodigo());
        }
        sql.append(" ORDER BY codigo DESC");
        PreparedStatement sqlConsultar = con.prepareStatement(sql.toString());
        ResultSet tabelaResultado = sqlConsultar.executeQuery();
        return montarDadosConsulta(tabelaResultado, nivelMontarDados);
    }

    @Override
    public List<AcessoClienteVO> consultarUltimos5Acessos(ClienteVO cliente, int nivelMontarDados) throws Exception {
        consultar(idEntidade);

        //String sql = "SELECT * FROM acessocliente where cliente = ? ORDER BY codigo DESC";
        String sql = "select * from AcessoCliente "
                + "where (cliente = ?) "
                + "order by dthrentrada DESC, codigo ASC "
                + "offset 0 limit 5";
        PreparedStatement stm = con.prepareStatement(sql);
        stm.setInt(1, cliente.getCodigo());
        ResultSet resultTabela = stm.executeQuery();

        return montarDadosConsulta(resultTabela, nivelMontarDados);
    }

    @Override
    public List<AcessoClienteVO> consultarUltimosAcessosDeterminadoPeloUsuario(ClienteVO cliente, Integer nrPaginaUltimosAcessos, int nivelMontarDados) throws Exception {
        consultar(idEntidade);

        //String sql = "SELECT * FROM acessocliente where cliente = ? ORDER BY codigo DESC";
        String sql = "select * from AcessoCliente "
                + "where (cliente = ?) "
                + "order by dthrentrada DESC, codigo ASC "
                + "offset 0 limit " + nrPaginaUltimosAcessos;
        PreparedStatement stm = con.prepareStatement(sql);
        stm.setInt(1, cliente.getCodigo());
        ResultSet resultTabela = stm.executeQuery();

        return montarDadosConsulta(resultTabela, nivelMontarDados);
    }

    @Override
    public Integer consultarQtdAcessosEntreDatas(ClienteVO cliente, Date dataInicio,
                                                 Date dataFim, boolean deveDistinguir) throws Exception {
        //consultar(idEntidade);
        String sql = "select count(codigo) as cont from AcessoCliente "
                + "where (cliente = ?) "
                + "and (dthrentrada between ? and ?)";

        if (deveDistinguir) {
            sql = "select count(cast(dthrentrada as date)), cast(dthrentrada as date) "
                    + "from AcessoCliente "
                    + "where cliente = ? and dthrentrada between ? and ? "
                    + "group by cast(dthrentrada as date)";
        }
        PreparedStatement stm = con.prepareStatement(sql,
                ResultSet.TYPE_SCROLL_INSENSITIVE, ResultSet.CONCUR_READ_ONLY);
        stm.setInt(1, cliente.getCodigo());
        stm.setTimestamp(2, Uteis.getDataJDBCTimestamp(dataInicio));
        stm.setTimestamp(3, Uteis.getDataJDBCTimestamp(dataFim));
        ResultSet resultTabela = stm.executeQuery();

        //verifica se deve retornar a quantidade de acessos total no período,
        //ou se deve ditinguir e retornar a quantidade de dias que teve pelo menos um acesso
        //no período
        if (!resultTabela.next()) {
            return 0;
        } else {
            if (deveDistinguir) {
                resultTabela.last();
                return resultTabela.getRow();
            } else {
                return resultTabela.getInt(1);
            }
        }
    }

    @Override
    public Integer consultarQtdAcessosHojePorCliente(Integer codigoCliente) throws Exception {
        StringBuilder sql = new StringBuilder();
        sql.append(" SELECT count(ac.codigo) as quantidade from acessocliente ac \n");
        sql.append(" WHERE ac.cliente = " + codigoCliente + " \n");
        sql.append(" AND ac.situacao in ('").append(SituacaoAcessoEnum.RV_LIBACESSOAUTORIZADO.getId()).append("','").append(SituacaoAcessoEnum.RV_LIBACESSOAUTORIZADOTOTALPASS.getId()).append("') \n");
        sql.append(" AND ac.dthrentrada::date = '" + Calendario.getDataAplicandoFormatacao(Calendario.hoje(), "yyyy-MM-dd") + "' ");

        PreparedStatement stm = con.prepareStatement(sql.toString());
        ResultSet tabelaResultado = stm.executeQuery();
        tabelaResultado.next();
        return tabelaResultado.getInt("quantidade");
    }

    private Integer incluirSemControleTreino(AcessoClienteVO acesso) throws Exception {
        try {
            String sql = "INSERT INTO acessocliente " +
                    "(cliente, dthrentrada, sentido, situacao, localacesso, coletor, usuario, meioIdentificacaoEntrada, dataRegistro) " +
                    "VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)";

            PreparedStatement ps = con.prepareStatement(sql);
            ps.setInt(1, acesso.getCliente().getCodigo());

            if (acesso.getDataHoraEntrada() == null) {
                ps.setNull(2, Types.NULL);
            } else {
                ps.setTimestamp(2, new java.sql.Timestamp(acesso.getDataHoraEntrada().getTime()));
            }
            ps.setString(3, acesso.getSentido());
            ps.setString(4, acesso.getSituacao().name());
            ps.setInt(5, acesso.getLocalAcesso().getCodigo());
            ps.setInt(6, acesso.getColetor().getCodigo());

            if (acesso.getUsuario() == null) {
                ps.setNull(7, Types.NULL);
            } else {
                ps.setInt(7, acesso.getUsuario().getCodigo());
            }
            ps.setInt(8, acesso.getMeioIdentificacaoEntrada().getCodigo());
            ps.setTimestamp(9, Uteis.getDataJDBCTimestamp(Calendario.hoje()));
            ps.execute();
            acesso.setCodigo(obterValorChavePrimariaCodigo());

            acesso.setNovoObj(false);
            return acesso.getCodigo();
        } catch (Exception e) {
            Uteis.logar(e, AcessoCliente.class);
            throw e;
        }
    }

    private Integer incluir(AcessoClienteVO obj, ControleCreditoTreinoInterfaceFacade controleCreditoTreino) throws Exception {
        return incluir(obj, controleCreditoTreino, true);
    }

    private Integer incluir(AcessoClienteVO obj, ControleCreditoTreinoInterfaceFacade controleCreditoTreino, boolean descontarCreditoTreino) throws Exception {
        try {
            AcessoClienteVO ultimoAcesso = null;
            SituacaoClienteSinteticoDWVO situacaoClienteSinteticoDWVO = null;
            if (obj.getSituacao().isLiberado()) {
                situacaoClienteSinteticoDWVO = controleCreditoTreino.getSituacaoClienteSinteticoDWDao().consultarCliente(obj.getCliente().getCodigo(), Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA);
                if (situacaoClienteSinteticoDWVO.isValidarSaldoCreditoTreino() && !obj.getLocalAcesso().getIgnorarConsumoCredito()) {
                    ultimoAcesso = consultarUltimoAcessoComEmpresa(obj.getCliente().getCodigo(), Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA);
                }
            }
            String sql = "INSERT INTO acessocliente (cliente, dthrentrada, sentido, situacao, localacesso, coletor, usuario," +
                    " meioIdentificacaoEntrada, dataRegistro, liberacaoAcesso, nomeCodEmpresaAcessou, nomeCodEmpresaOrigem, nomeCpfEmailClienteOrigem) VALUES" +
                    " (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)";
            try (PreparedStatement sqlInserir = con.prepareStatement(sql)) {
                int i = 0;
                sqlInserir.setInt(++i, obj.getCliente().getCodigo());

                sqlInserir.setTimestamp(++i, new java.sql.Timestamp(obj.getDataHoraEntrada().getTime()));
                sqlInserir.setString(++i, obj.getSentido());
                sqlInserir.setString(++i, obj.getSituacao().name());
                sqlInserir.setInt(++i, obj.getLocalAcesso().getCodigo());
                sqlInserir.setInt(++i, obj.getColetor().getCodigo());

                if (obj.getUsuario() == null) {
                    sqlInserir.setNull(++i, Types.INTEGER);
                } else {
                    sqlInserir.setInt(++i, obj.getUsuario().getCodigo());
                }
                sqlInserir.setInt(++i, obj.getMeioIdentificacaoEntrada().getCodigo());
                sqlInserir.setTimestamp(++i, Uteis.getDataJDBCTimestamp(obj.getDataRegistro()));
                if (obj.getLiberacaoAcessoVO() == null || UteisValidacao.emptyNumber(obj.getLiberacaoAcessoVO().getCodigo())) {
                    sqlInserir.setNull(++i, Types.INTEGER);
                } else {
                    sqlInserir.setInt(++i, obj.getLiberacaoAcessoVO().getCodigo());
                }
                if (UteisValidacao.emptyString(obj.getNomeCodEmpresaAcessou())) {
                    sqlInserir.setString(++i, "");
                } else {
                    sqlInserir.setString(++i, obj.getNomeCodEmpresaAcessou());
                }
                if (UteisValidacao.emptyString(obj.getNomeCodEmpresaOrigem())) {
                    sqlInserir.setString(++i, "");
                } else {
                    sqlInserir.setString(++i, obj.getNomeCodEmpresaOrigem());
                }
                if (UteisValidacao.emptyString(obj.getNomeCpfEmailClienteOrigem())) {
                    sqlInserir.setString(++i, "");
                } else {
                    sqlInserir.setString(++i, obj.getNomeCpfEmailClienteOrigem());
                }
                sqlInserir.execute();
                obj.setCodigo(obterValorChavePrimariaCodigo());
                obj.setNovoObj(false);
            }

            if (obj.getSituacao().isLiberado() && situacaoClienteSinteticoDWVO != null && descontarCreditoTreino) {
                processarCreditosTreino(obj, controleCreditoTreino, ultimoAcesso, situacaoClienteSinteticoDWVO);
            }

            //removido até criar configuração
//            liberarCobrancaAutomatica(obj);

            return obj.getCodigo();
        } catch (Exception e) {
            Logger.getLogger(getClass().getSimpleName()).log(Level.SEVERE, "#### ERRO AO REGISTRAR ENTRADA DO CLIENTE. ERRO: {}", e.getMessage());
            obj.setNovoObj(true);
            throw e;
        }
    }

    private void processarCreditosTreino(AcessoClienteVO obj, ControleCreditoTreinoInterfaceFacade controleCreditoTreino, AcessoClienteVO ultimoAcesso, SituacaoClienteSinteticoDWVO situacaoClienteSinteticoDWVO) {
        try {
            Logger.getLogger(getClass().getSimpleName()).log(Level.INFO, "#### Entrou no método processarCreditosTreino ");
            Logger.getLogger(getClass().getSimpleName()).log(Level.INFO, "#### PARÂMETROS");
            Logger.getLogger(getClass().getSimpleName()).log(Level.INFO, "#### Matrícula " + situacaoClienteSinteticoDWVO.getMatricula());
            if (ultimoAcesso != null) {
                if (ultimoAcesso.getCliente() != null && ultimoAcesso.getCliente().getEmpresa() != null) {
                    Logger.getLogger(getClass().getSimpleName()).log(Level.INFO, "#### Empresa - " + ultimoAcesso.getCliente().getEmpresa().getCodigo() + " - " + ultimoAcesso.getCliente().getEmpresa().getNome());
                }
                if (ultimoAcesso.getDataHoraSaida() != null) {
                    Logger.getLogger(getClass().getSimpleName()).log(Level.INFO, "#### ultimoAcesso.getDataHoraSaida " + ultimoAcesso.getDataHoraSaida());
                }
                if (ultimoAcesso.getDataHoraEntrada() != null) {
                    Logger.getLogger(getClass().getSimpleName()).log(Level.INFO, "#### ultimoAcesso.getDataHoraEntrada " + ultimoAcesso.getDataHoraEntrada());
                }
            } else {
                Logger.getLogger(getClass().getSimpleName()).log(Level.INFO, "#### ultimoAcesso está nulo");
            }
            if (obj != null){
                if (obj.getDataHoraEntrada() != null) {
                    Logger.getLogger(getClass().getSimpleName()).log(Level.INFO, "#### obj.getDataHoraEntrada " + obj.getDataHoraEntrada());
                }
            } else {
                Logger.getLogger(getClass().getSimpleName()).log(Level.INFO, "#### AcessoClienteVO recebido no parâmetro está nulo");
            }
            if (situacaoClienteSinteticoDWVO.isValidarSaldoCreditoTreino()
                    && !obj.getLocalAcesso().getIgnorarConsumoCredito()
                    && !UteisValidacao.emptyNumber(situacaoClienteSinteticoDWVO.getCodigoContrato())
                    && Calendario.entre(obj.getDataHoraEntrada(), situacaoClienteSinteticoDWVO.getDataVigenciaDe(), situacaoClienteSinteticoDWVO.getDataVigenciaAteAjustada())) {
                boolean diminuirCredito = true;
                if (ultimoAcesso != null) {
                    long minutosAposUltimoAcesso = Calendario.diferencaEmMinutos((ultimoAcesso.getDataHoraSaida() != null) ? ultimoAcesso.getDataHoraSaida() : ultimoAcesso.getDataHoraEntrada(), obj.getDataHoraEntrada());
                    Logger.getLogger(getClass().getSimpleName()).log(Level.INFO, "#### minutosAposUltimoAcesso " + minutosAposUltimoAcesso);
                    Logger.getLogger(getClass().getSimpleName()).log(Level.INFO, "#### minutosAposUltimoAcessoDiminuirCredito " + ultimoAcesso.getCliente().getEmpresa().getMinutosAposUltimoAcessoDiminuirCredito());
                    diminuirCredito = (Math.abs(minutosAposUltimoAcesso) >= ultimoAcesso.getCliente().getEmpresa().getMinutosAposUltimoAcessoDiminuirCredito());
                }
                if (diminuirCredito) {
                    Logger.getLogger(getClass().getSimpleName()).log(Level.INFO, "#### diminuirCredito está como true");
                    controleCreditoTreino.diminuirCreditoPorAcesso(obj.getKey(), situacaoClienteSinteticoDWVO, obj);
                }
            } else {
                StringBuilder sbLog = new StringBuilder();
                sbLog.append("#### NÃO DIMINUIU OS CREDITOS - ");
                if (!situacaoClienteSinteticoDWVO.isValidarSaldoCreditoTreino()){
                    sbLog.append("SINTÉTICO MARCADO PARA NÃO VALIDAR SALDOCREDITOTREINO ");
                }
                if (obj.getLocalAcesso().getIgnorarConsumoCredito()){
                    sbLog.append("LOCAL DE ACESSO MARCADO PARA IGNORAR CONSUMO CRÉDITO ");
                }
                if (UteisValidacao.emptyNumber(situacaoClienteSinteticoDWVO.getCodigoContrato())){
                    sbLog.append("CODIGOCONTRATO DO SINTÉTICO ESTÁ VAZIO - ").append(situacaoClienteSinteticoDWVO.getCodigoContrato()).append(" ");
                }
                if (!Calendario.entre(obj.getDataHoraEntrada(), situacaoClienteSinteticoDWVO.getDataVigenciaDe(), situacaoClienteSinteticoDWVO.getDataVigenciaAteAjustada())){
                    sbLog.append("HORA DE ENTRADA NÃO ESTÁ ENTRE O INÍCIO E FINAL DO CONTRATO - ");
                    sbLog.append("HORA DE ENTRADA ").append(obj.getDataHoraEntrada()).append(" - ");
                    sbLog.append("INÍCIO DO CONTRATO ").append(situacaoClienteSinteticoDWVO.getDataVigenciaDe()).append(" - ");
                    sbLog.append("FIM DO CONTRATO ").append(situacaoClienteSinteticoDWVO.getDataVigenciaAteAjustada());
                }
                Logger.getLogger(getClass().getSimpleName()).log(Level.INFO, sbLog.toString());
            }
        } catch (Exception e) {
            Uteis.logar("#### ERRO AO DIMINUIR CREDITO TREINO(APÓS GRAVAR O ACESSO). ERRO: " + e.getMessage());
            Logger.getLogger(getClass().getSimpleName()).log(Level.SEVERE, "#### ERRO AO DIMINUIR CREDITO TREINO(APÓS GRAVAR O ACESSO). ERRO: {}", e.getMessage());
            e.printStackTrace();
        } finally {
            Logger.getLogger(getClass().getSimpleName()).log(Level.INFO, "#### Saiu do método processarCreditosTreino");
        }
    }

    private void liberarCobrancaAutomatica(AcessoClienteVO obj) {
        Pessoa pessoaDAO = null;
        Usuario usuarioDAO = null;
        try {
            pessoaDAO = new Pessoa(con);

            PessoaVO pessoaVO = null;

            if (!UteisValidacao.emptyNumber(obj.getCliente().getPessoa().getCodigo())) {
                pessoaVO = obj.getCliente().getPessoa();
            } else {
                pessoaVO = new PessoaVO();
                pessoaVO.setCodigo(pessoaDAO.descobrirCodigoPessoa(obj.getCliente().getCodigo(), null, null, null));
            }

            if (pessoaVO == null || UteisValidacao.emptyNumber(pessoaVO.getCodigo())) {
                return;
            }

            pessoaDAO.obterInformacoesDeBloqueioCobrancaAutomatica(pessoaVO);

            if (pessoaVO.getDataBloqueioCobrancaAutomatica() == null) {
                return;
            }

            UsuarioVO usuarioVO = obj.getUsuarioVO();
            if (usuarioVO == null || UteisValidacao.emptyNumber(usuarioVO.getCodigo())) {
                usuarioDAO = new Usuario(con);
                usuarioVO = usuarioDAO.getUsuarioRecorrencia();
            }

            pessoaDAO.alterarDataBloqueioCobrancaAutomatica(null, null, pessoaVO.getCodigo(),
                    usuarioVO, false, "Acesso na Catraca");

        } catch (Exception ex) {
            ex.printStackTrace();
        } finally {
            pessoaDAO = null;
            usuarioDAO = null;
        }
    }

    public AcessoClienteVO consultarUltimoAcessoComEmpresa(Integer codigoCliente, int nivelMontarDados) throws Exception {
        StringBuilder sql = new StringBuilder();
        sql.append("SELECT e.codigo as codigoEmpresa, e.MinutosAposUltimoAcessoDiminuirCredito, ac.* \n");
        sql.append("FROM AcessoCliente ac \n");
        sql.append("inner join cliente cli on cli.codigo = ac.cliente \n");
        sql.append("inner join empresa e on e.codigo = cli.empresa \n");
        sql.append("inner join localacesso l on l.codigo = ac.localacesso \n");
        sql.append("WHERE cliente = ?  and  ignorarconsumocredito  = 'f' \n");
        sql.append("order by dthrentrada desc, ac.codigo ASC limit 1 \n");
        PreparedStatement sqlConsultar = con.prepareStatement(sql.toString());
        sqlConsultar.setInt(1, codigoCliente);
        ResultSet rs = sqlConsultar.executeQuery();
        if (rs.next()) {
            AcessoClienteVO obj = montarDados(rs, nivelMontarDados);
            obj.getCliente().setEmpresa(new EmpresaVO());
            obj.getCliente().getEmpresa().setCodigo(rs.getInt("codigoEmpresa"));
            obj.getCliente().getEmpresa().setMinutosAposUltimoAcessoDiminuirCredito(rs.getInt("minutosAposUltimoAcessoDiminuirCredito"));
            return obj;
        }
        return null;

    }


    public AcessoClienteVO novo() throws Exception {
        AcessoClienteVO obj = new AcessoClienteVO();
        return obj;
    }

    public void setIdEntidade(String idEntidade) {
        AcessoCliente.idEntidade = idEntidade;
    }

    private AcessoClienteVO montarDadosBasico(ResultSet dadosSQL, int nivelMontarDados) throws Exception {
        AcessoClienteVO obj = new AcessoClienteVO();
        if (nivelMontarDados == Uteis.NIVELMONTARDADOS_INDICARDORACESSOS) {
            PessoaVO pessoa = new PessoaVO();
            obj.setNovoObj(false);
            obj.setCodigo(dadosSQL.getInt("codigoacesso"));
            obj.setSentido(dadosSQL.getString("sentido"));
            obj.setDataHoraEntrada(dadosSQL.getTimestamp("dthrentrada"));
            obj.setDataHoraSaida(dadosSQL.getTimestamp("dthrsaida"));
            obj.setNomeCpfEmailClienteOrigem(dadosSQL.getString("nomecpfemailclienteorigem"));
            obj.setCliente(new ClienteVO());
            obj.getCliente().setCodigo(dadosSQL.getInt("codigocliente"));
            obj.getCliente().setMatricula(dadosSQL.getString("matricula"));
            pessoa.setCodigo(dadosSQL.getInt("codigopessoa"));
            pessoa.setNome(dadosSQL.getString("nome"));
            obj.getCliente().setPessoa(pessoa);
        } else {
            obj.setNovoObj(false);
            obj.setCodigo(dadosSQL.getInt("codigo"));
            obj.setMeioIdentificacaoEntrada(MeioIdentificacaoEnum.getMeioIdentificacao(dadosSQL.getInt("meioIdentificacaoEntrada")));
            obj.setMeioIdentificacaoSaida(MeioIdentificacaoEnum.getMeioIdentificacao(dadosSQL.getInt("meioIdentificacaoSaida")));
            obj.setSentido(dadosSQL.getString("sentido"));
            obj.setSituacao(SituacaoAcessoEnum.valueOf(dadosSQL.getString("situacao")));
            obj.setDataHoraEntrada(dadosSQL.getTimestamp("dthrentrada"));
            obj.setDataHoraSaida(dadosSQL.getTimestamp("dthrsaida"));
            obj.setNomeCpfEmailClienteOrigem(dadosSQL.getString("nomecpfemailclienteorigem"));
            obj.setCliente(new ClienteVO());
            obj.getCliente().setCodigo(dadosSQL.getInt("cliente"));
            obj.setLocalAcesso(new LocalAcessoVO());
            obj.getLocalAcesso().setCodigo(dadosSQL.getInt("localacesso"));
            obj.setColetor(new ColetorVO());
            obj.getColetor().setCodigo(dadosSQL.getInt("coletor"));
            obj.setUsuario(new UsuarioVO());
            obj.getUsuario().setCodigo(dadosSQL.getInt("usuario"));
            obj.setTicket(dadosSQL.getString("ticket"));
        }

        return obj;
    }

    private AcessoClienteVO montarDados(ResultSet dadosSQL, int nivelMontarDados) throws Exception {

        AcessoClienteVO obj = montarDadosBasico(dadosSQL, nivelMontarDados);

        if (nivelMontarDados == Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA) {
            return obj;
        }

        if (nivelMontarDados == Uteis.NIVELMONTARDADOS_DADOSBASICOS) {
            return obj;
        } else if ((nivelMontarDados == Uteis.NIVELMONTARDADOS_DADOSENTIDADESUBORDINADAS)
                || ((nivelMontarDados == Uteis.NIVELMONTARDADOS_TODOS))) {

            if (!UteisValidacao.emptyNumber(obj.getCliente().getCodigo())) {
                ClienteInterfaceFacade clienteDao = getFacade().getCliente();
                obj.setCliente(clienteDao.consultarPorChavePrimaria(obj.getCliente().getCodigo(), Uteis.NIVELMONTARDADOS_MINIMOS));
            }

            LocalAcessoInterfaceFacade localAcessoDao = getFacade().getLocalAcesso();
            obj.setLocalAcesso(localAcessoDao.consultarPorCodigo(obj.getLocalAcesso().getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS));

            ColetorInterfaceFacade coletorDao = getFacade().getColetor();
            obj.setColetor(coletorDao.consultarPorCodigo(obj.getColetor().getCodigo()));

            UsuarioInterfaceFacade usuarioDao = getFacade().getUsuario();
            try {
                obj.setUsuario(usuarioDao.consultarPorChavePrimaria(obj.getUsuario().getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS));
            } catch (Exception e) {
            }
        } else if (nivelMontarDados == Uteis.NIVELMONTARDADOS_INDICARDORACESSOS) {
            return obj;
        } else if (nivelMontarDados == Uteis.NIVELMONTARDADOS_DADOSENTIDADESPRINCIPAIS) {
            LocalAcesso localAcessoDao = new LocalAcesso(con);
            obj.setLocalAcesso(localAcessoDao.consultarPorCodigo(obj.getLocalAcesso().getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS));
            localAcessoDao = null;
        }
        return obj;
    }

    private List<AcessoClienteVO> montarDadosConsulta(ResultSet tabelaResultado, int nivelMontarDados) throws Exception {
        List<AcessoClienteVO> vetResultado = new ArrayList();
        while (tabelaResultado.next()) {
            AcessoClienteVO obj = new AcessoClienteVO();
            obj = montarDados(tabelaResultado, nivelMontarDados);
            vetResultado.add(obj);
        }
        return vetResultado;
    }

    @Override
    public AcessoClienteVO consultarPorChavePrimaria(Integer codigo, int nivelMontarDados) throws Exception {
        consultar(getIdEntidade(), false);
        String sql = "SELECT * FROM AcessoCliente WHERE codigo = ?";
        PreparedStatement sqlConsultar = con.prepareStatement(sql);
        sqlConsultar.setInt(1, codigo.intValue());
        ResultSet tabelaResultado = sqlConsultar.executeQuery();
        if (!tabelaResultado.next()) {
            throw new ConsistirException("Dados Não Encontrados ( AcessoCliente ).");
        }
        return (montarDados(tabelaResultado, nivelMontarDados));
    }

    @Override
    public AcessoClienteVO consultarPorCodigo(Integer codigo, int nivelMontarDados) throws Exception {
        consultar(getIdEntidade(), false);
        String sql = "SELECT * FROM AcessoCliente WHERE codigo = ?";
        PreparedStatement sqlConsultar = con.prepareStatement(sql);
        sqlConsultar.setInt(1, codigo.intValue());
        ResultSet tabelaResultado = sqlConsultar.executeQuery();
        if (tabelaResultado.next()) {
            return (montarDados(tabelaResultado, nivelMontarDados));
        }
        return new AcessoClienteVO();
    }

    /**
     * Responsável por obter a Hora do Ultimo Acesso do Cliente, em string
     *
     * <AUTHOR>
     * 26/09/2011
     */
    @Override
    public String obterHoraUltimoAcessoCliente(Integer codigoCliente) throws Exception {
        String sql = "SELECT dthrentrada FROM acessocliente WHERE cliente = " + codigoCliente + " ORDER BY codigo DESC LIMIT 1";
        ResultSet consulta = AcessoCliente.criarConsulta(sql, con);
        String retorno = "";
        if (consulta.next()) {
            retorno = Uteis.getDataAplicandoFormatacao(consulta.getTimestamp("dthrentrada"), "HH:mm:ss");
        }
        return retorno;

    }

    public int consultarTotalAcessosFiltros(int empresa, java.sql.Date dataInicial, java.sql.Date dataFinal, String horaInicial, String horaFinal) throws SQLException {
        String sqlStr = "select count(*) as qtde from acessocliente "
                + "inner join cliente on cliente.codigo = acessocliente.cliente "
                + "inner join empresa on empresa.codigo = cliente.empresa "
                + "where empresa = " + empresa + " "
                + "and ((acessocliente.dthrentrada >= '" + dataInicial + " " + horaInicial + "' and acessocliente.dthrentrada <= '" + dataFinal + " " + horaFinal + "')"
                + " or (acessocliente.dthrsaida >= '" + dataInicial + " " + horaInicial + "' and acessocliente.dthrsaida <= '" + dataFinal + " " + horaFinal + "'))"
                + " ";
        Statement stm = con.createStatement();
        ResultSet tabelaResultado = stm.executeQuery(sqlStr);
        if (tabelaResultado.next()) {
            return tabelaResultado.getInt("qtde");
        } else {
            return 0;
        }
    }

    @Override
    public AcessoClienteVO consultarUltimoAcessoPorLocal(ClienteVO cliente, Date data, Integer localAcesso, int nivelMontarDados) throws Exception {
        String sql = "SELECT * FROM AcessoCliente WHERE cliente = ? and dthrentrada > ? and localacesso = ? "
                + "order by dthrentrada desc limit 1";
        PreparedStatement sqlConsultar = con.prepareStatement(sql);
        sqlConsultar.setInt(1, cliente.getCodigo());
        sqlConsultar.setDate(2, Uteis.getDataJDBC(data));
        sqlConsultar.setInt(3, localAcesso);
        ResultSet tabelaResultado = sqlConsultar.executeQuery();

        if (!tabelaResultado.next()) {
            return new AcessoClienteVO();
        } else {
            return montarDados(tabelaResultado, nivelMontarDados);
        }
    }

    @Override
    public JSONArray obterAcessosPorDia(Integer codigoprofessor, Date inicio, Date fim, Integer empresa) throws Exception {
        StringBuilder sql = new StringBuilder(" select count(ac.codigo) as acessos, count(um.codigo) as acessotreino, ac.dthrentrada::date as dia from acessocliente ac\n");
        sql.append(" INNER JOIN cliente cli ON ac.cliente = cli.codigo \n");
        if (!UteisValidacao.emptyNumber(codigoprofessor)) {
            sql.append(" INNER JOIN vinculo vi ON vi.cliente = cli.codigo AND vi.tipovinculo = 'TW' AND vi.colaborador = ");
            sql.append(codigoprofessor);
        }
        sql.append(" LEFT JOIN usuariomovel um ON um.cliente = ac.cliente \n");
        sql.append(" WHERE ac.dthrentrada BETWEEN ? AND ? \n");
        sql.append(" AND cli.empresa = ? \n");
        sql.append(" GROUP BY ac.dthrentrada::date ");

        PreparedStatement stm = con.prepareStatement(sql.toString());
        stm.setTimestamp(1, Uteis.getDataJDBCTimestamp(Calendario.getDataComHoraZerada(inicio)));
        stm.setTimestamp(2, Uteis.getDataJDBCTimestamp(Calendario.getDataComHora(fim, "23:59")));
        stm.setInt(3, empresa);

        ResultSet rs = stm.executeQuery();
        JSONArray json = new JSONArray();
        while (rs.next()) {
            JSONObject obj = new JSONObject();
            obj.put("dia", rs.getDate("dia"));
            obj.put("acessos", rs.getString("acessos"));
            obj.put("acessotreino", rs.getString("acessotreino"));
            json.put(obj);
        }
        return json;
    }

    @Override
    public JSONArray obterListaAcessosPorDia(Integer codigoprofessor, Date inicio, Date fim, Integer empresa, boolean alunosTreino) throws Exception {
        StringBuilder sql = new StringBuilder(" select cli.codigomatricula, pes.nome, pes.codigo as codigopessoa, ac.dthrentrada ");
        sql.append(" FROM acessocliente ac\n");
        sql.append(" INNER JOIN cliente cli ON ac.cliente = cli.codigo \n");
        sql.append(alunosTreino ? " INNER JOIN usuariomovel um ON um.cliente = cli.codigo \n" : "");
        sql.append(" INNER JOIN pessoa pes ON pes.codigo = cli.pessoa \n");
        if (!UteisValidacao.emptyNumber(codigoprofessor)) {
            sql.append(" INNER JOIN vinculo vi ON vi.cliente = cli.codigo AND vi.tipovinculo = 'TW' AND vi.colaborador = ");
            sql.append(codigoprofessor);
        }
        sql.append(" WHERE ac.dthrentrada BETWEEN ? AND ? \n");
        sql.append(" AND cli.empresa = ? \n");
        sql.append(" ORDER BY ac.dthrentrada ");

        PreparedStatement stm = con.prepareStatement(sql.toString());
        stm.setTimestamp(1, Uteis.getDataJDBCTimestamp(Calendario.getDataComHoraZerada(inicio)));
        stm.setTimestamp(2, Uteis.getDataJDBCTimestamp(Calendario.getDataComHora(fim, "23:59")));
        stm.setInt(3, empresa);

        ResultSet rs = stm.executeQuery();
        JSONArray json = new JSONArray();
        while (rs.next()) {
            JSONObject obj = new JSONObject();
//            private Integer matricula;
            obj.put("matricula", rs.getInt("codigomatricula"));
//    private String nome;
            obj.put("nome", rs.getString("nome"));
//    private Integer codigoPessoa;
            obj.put("codigopessoa", rs.getInt("codigopessoa"));
//    private String dthrentrada;
            obj.put("dthrentrada", Uteis.getDataAplicandoFormatacao(rs.getTimestamp("dthrentrada"), "dd/MM/yy HH:mm"));
            json.put(obj);
        }
        return json;
    }

    @Override
    public JSONArray obterUltimosAcessos(Integer empresa, Date dia) throws Exception {
        StringBuilder sql = new StringBuilder(" select distinct acessocliente.cliente,dthrentrada from acessocliente ");
        sql.append(" INNER JOIN cliente ON cliente.codigo = acessocliente.cliente AND cliente.empresa = ? \n");
        sql.append(" LEFT JOIN matriculaalunohorarioturma maht ON cliente.pessoa = maht.pessoa AND maht.datafim >= dthrentrada \n");
        sql.append(" LEFT JOIN alunohorarioturma alt ON alt.cliente = cliente.codigo AND dthrentrada >= alt.dia \n");
        if (dia == null) {
            sql.append(" WHERE dthrentrada > ? \n");
        } else {
            sql.append(" WHERE dthrentrada BETWEEN ? AND ? \n");
        }
        sql.append("  AND (alt.cliente is not null OR maht.pessoa is not null) \n");
        sql.append(" ORDER BY dthrentrada DESC \n");

        PreparedStatement stm = con.prepareStatement(sql.toString());
        stm.setInt(1, empresa);
        if (dia == null) {
            stm.setDate(2, Uteis.getDataJDBC(Uteis.somarDias(Calendario.hoje(), -30)));
        } else {
            stm.setTimestamp(2, Uteis.getDataJDBCTimestamp(Calendario.getDataComHoraZerada(dia)));
            stm.setTimestamp(3, Uteis.getDataJDBCTimestamp(Calendario.getDataComHora(dia, "23:59:59")));
        }
        ResultSet rs = stm.executeQuery();
        JSONArray json = new JSONArray();
        while (rs.next()) {
            JSONObject obj = new JSONObject();
            obj.put("cliente", rs.getInt("cliente"));
            obj.put("dthrentrada", Uteis.getDataAplicandoFormatacao(rs.getTimestamp("dthrentrada"), "dd/MM/yyyy HH:mm"));
            json.put(obj);
        }
        return json;
    }

    @Override
    public String consultarUltimosAcessosJSON(Integer cliente, Integer nrPaginaUltimosAcessos) throws Exception {
        consultar(idEntidade);

        JSONObject aaData = new JSONObject();
        JSONArray valores = new JSONArray();

        StringBuilder sql = new StringBuilder("SELECT sentido, la.descricao as localacesso, co.descricao as coletor, dthrentrada, dthrsaida, nomeCodEmpresaAcessou, \n");
        sql.append(" meioidentificacaoentrada, ac.situacao, u.nome as usuario,ac.codigo as codigoAcesso, ac.ticket as ticket, ac.dataregistro FROM acessocliente ac \n");
        sql.append(" INNER JOIN localacesso la ON  la.codigo = ac.localacesso \n");
        sql.append(" INNER JOIN coletor co ON  co.codigo = ac.coletor \n");
        sql.append(" LEFT JOIN usuario u ON u.codigo = ac.usuario \n");
        sql.append(" WHERE ac.cliente = ? \n");
        sql.append(" ORDER BY dthrentrada DESC, ac.codigo ASC \n");
        if (nrPaginaUltimosAcessos != null) {
            sql.append(" offset 0 limit ").append(nrPaginaUltimosAcessos);
        }
        PreparedStatement stm = con.prepareStatement(sql.toString());
        stm.setInt(1, cliente);
        ResultSet rs = stm.executeQuery();
        while (rs.next()) {
            JSONArray itemArray = new JSONArray();
            itemArray.put(rs.getInt("codigoAcesso"));
            itemArray.put(rs.getString("sentido"));
            itemArray.put(rs.getString("localacesso"));
            itemArray.put(rs.getString("coletor"));
            itemArray.put(rs.getString("nomeCodEmpresaAcessou"));
            itemArray.put(Uteis.getDataAplicandoFormatacao(rs.getTimestamp("dthrentrada"), "dd/MM/yy HH:mm"));
            itemArray.put(Uteis.getDataAplicandoFormatacao(rs.getTimestamp("dthrentrada"), "yyyy-MM-dd HH:mm:ss"));
            itemArray.put(Uteis.getDataAplicandoFormatacao(rs.getTimestamp("dthrsaida"), "dd/MM/yy HH:mm"));
            itemArray.put(Uteis.getDataAplicandoFormatacao(rs.getTimestamp("dthrsaida"), "yyyy-MM-dd HH:mm:ss"));
            MeioIdentificacaoEnum meio = MeioIdentificacaoEnum.getMeioIdentificacao(rs.getInt("meioidentificacaoentrada"));
            itemArray.put(meio == null ? "" : meio.getDescricao());
            SituacaoAcessoEnum sit = SituacaoAcessoEnum.valueOf(rs.getString("situacao"));
            itemArray.put(sit.equals(SituacaoAcessoEnum.RV_LIBACESSOAUTORIZADO) ? "" : sit.getDescricao());
            itemArray.put(rs.getString("usuario"));
            itemArray.put(rs.getString("ticket"));
            itemArray.put(Uteis.getDataAplicandoFormatacao(rs.getTimestamp("dataregistro"), "dd/MM/yy HH:mm"));
            valores.put(itemArray);
        }
        aaData.put("aaData", valores);
        return aaData.toString();
    }

    public Date obterUltimoAcesso(Integer codigoCliente) throws Exception {
        ResultSet rs = criarConsulta(" SELECT dthrentrada FROM acessocliente WHERE cliente = " + codigoCliente +
                " AND (situacao NOT LIKE 'RV_BLOQ%' OR situacao LIKE 'RV_BLOQ%' AND usuario IS NOT NULL) ORDER BY dthrentrada DESC LIMIT 1 ", con);
        return rs.next() ? rs.getTimestamp("dthrentrada") : null;
    }

    public Date obterUltimoAcessoAntesDe(Integer codigoCliente, Date data) throws Exception {
        ResultSet rs = criarConsulta(" SELECT dthrentrada FROM acessocliente WHERE cliente = " + codigoCliente +
        " AND dthrentrada < '" + Uteis.getDataJDBCTimestamp(Calendario.getDataComHoraZerada(data)) + "' ORDER BY dthrentrada DESC LIMIT 1 ", con);
        return rs.next() ? rs.getTimestamp("dthrentrada") : null;
    }

    public List<Integer> alunosAcessoPorProfessorMes(int empresa, int professor, Date diaBase) throws Exception {
        StringBuilder sql = new StringBuilder();
        Date inicio = Uteis.obterPrimeiroDiaMes(diaBase);
        Date fim = diaBase;
        sql.append(" SELECT DISTINCT(acessocliente.cliente) as cliente from acessocliente ").append("\n");
        sql.append(" inner join cliente on cliente.codigo = acessocliente.cliente ").append("\n");
        sql.append(" inner join empresa on empresa.codigo = cliente.empresa ").append("\n");
        if (!UteisValidacao.emptyNumber(professor)) {
            sql.append(" inner join vinculo vin ON vin.colaborador = ").append(professor).append(" and vin.cliente = acessocliente.cliente").append("\n");
        }
        sql.append(" where empresa = ").append(empresa);
        sql.append(" and ((acessocliente.dthrentrada >= ? and acessocliente.dthrentrada <= ? )");
        sql.append(" or (acessocliente.dthrsaida >= ? ").append(" and acessocliente.dthrsaida <= ? ))");
        PreparedStatement stm = con.prepareStatement(sql.toString());
        stm.setTimestamp(1, Uteis.getDataJDBCTimestamp(inicio));
        stm.setTimestamp(2, Uteis.getDataJDBCTimestamp(fim));
        stm.setTimestamp(3, Uteis.getDataJDBCTimestamp(inicio));
        stm.setTimestamp(4, Uteis.getDataJDBCTimestamp(fim));
        ResultSet tabelaResultado = stm.executeQuery();
        List<Integer> clientes = new ArrayList<Integer>();
        while (tabelaResultado.next()) {
            clientes.add(tabelaResultado.getInt("cliente"));
        }
        return clientes;
    }

    public List<AcessoRealizadoTO> consultarAcessosRealizadosDia(Date dataConsulta, Integer codEmpresa) throws SQLException {

        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");

        String dataConsultaFormatada = sdf.format(dataConsulta);

        StringBuilder sql = new StringBuilder();

        sql.append("SELECT ")
                .append("ac.codigo, ")
                .append("ac.cliente, ")
                .append("ac.dthrentrada, ")
                .append("ac.dataregistro, ")
                .append("ac.meioidentificacaoentrada, ")
                .append("cli.matricula, ")
                .append("pes.nome ")
                .append("FROM acessocliente ac ")
                .append("INNER JOIN cliente cli ON ac.cliente = cli.codigo ")
                .append("INNER JOIN pessoa pes ON cli.pessoa = pes.codigo ");

        if (codEmpresa != null) {
            sql.append("INNER JOIN localacesso la ON la.codigo = ac.localacesso ");
        }

        sql.append("WHERE ac.situacao = 'RV_LIBACESSOAUTORIZADO' ")
                .append("AND DATE(ac.dataregistro) = '").append(dataConsultaFormatada).append("' ");

        if (codEmpresa != null) {
            sql.append("AND la.empresa = ").append(codEmpresa).append(";");
        }

        String queryAcessosRealizados = sql.toString();

        try (PreparedStatement stm = con.prepareStatement(queryAcessosRealizados);
             ResultSet resultTabela = stm.executeQuery()) {
            List<AcessoRealizadoTO> listaRegistrosAcessosRealizados = new ArrayList<>();

            while (resultTabela.next()) {
                Integer meioIdentificacaoId = resultTabela.getInt("meioidentificacaoentrada");

                MeioIdentificacaoEnum meioIdentificacaoEnum = MeioIdentificacaoEnum.getMeioIdentificacao(meioIdentificacaoId);

                AcessoRealizadoTO registroAcessoRealizado = new AcessoRealizadoTO();

                registroAcessoRealizado.setNomeAluno(resultTabela.getString("nome"));
                registroAcessoRealizado.setCodigoCliente(resultTabela.getInt("cliente"));
                registroAcessoRealizado.setMatriculaAluno(resultTabela.getString("matricula"));
                registroAcessoRealizado.setMeioIdentificacao(meioIdentificacaoEnum.getDescricao());

                //DATA SEM FORMATAÇÃO
                Timestamp dataRegistroTimestamp = resultTabela.getTimestamp("dataregistro");
                LocalDateTime dataRegistro = dataRegistroTimestamp.toLocalDateTime();
                registroAcessoRealizado.setDataRegistro(dataRegistro);

                //DATA FORMATADA
                String dataRegistroFormatada = Uteis.getDataAplicandoFormatacao(dataRegistroTimestamp, "dd/MM/yy HH:mm");
                registroAcessoRealizado.setDataRegistroFormatada(dataRegistroFormatada);

                listaRegistrosAcessosRealizados.add(registroAcessoRealizado);
            }

            ///ORDENANDO RESULTADOS DO MAIS RECENTE PRO MAIS ANTIGO
            listaRegistrosAcessosRealizados.sort(Comparator.comparing(AcessoRealizadoTO::getDataRegistro).reversed());

            return listaRegistrosAcessosRealizados;
        }
    }

    @Override
    public AcessoClienteVO consultarAcessoHorarioTurma(ClienteVO cliente, Date dia, MeioIdentificacaoEnum meioIdentificacao, HorarioTurmaVO horarioTurmaVO, int nivelMontarDados) throws Exception {
        String sql = "SELECT * FROM AcessoCliente WHERE cliente = ? AND meioIdentificacaoEntrada = ? AND dthrentrada BETWEEN ? AND ? "
                + "   ORDER BY codigo DESC LIMIT 1";
        PreparedStatement sqlConsultar = con.prepareStatement(sql);
        sqlConsultar.setInt(1, cliente.getCodigo());
        sqlConsultar.setInt(2, meioIdentificacao.getCodigo());
        sqlConsultar.setTimestamp(3, Uteis.getDataHoraJDBC(dia, horarioTurmaVO.getHoraInicialComTolerancia() + ":00"));
        sqlConsultar.setTimestamp(4, Uteis.getDataHoraJDBC(dia, horarioTurmaVO.getHoraFinal() + ":00"));

        ResultSet tabelaResultado = sqlConsultar.executeQuery();

        if (!tabelaResultado.next()) {
            return new AcessoClienteVO();
        } else {
            return montarDados(tabelaResultado, nivelMontarDados);
        }
    }


    public List<Integer> alunosAcessoPorProfessorPorPeriodo(int empresa, int professor, Date inicio, Date fim) throws Exception {
        StringBuilder sql = new StringBuilder();

        sql.append(" SELECT DISTINCT(acessocliente.cliente) as cliente from acessocliente ").append("\n");
        sql.append(" inner join cliente on cliente.codigo = acessocliente.cliente ").append("\n");
        sql.append(" inner join empresa on empresa.codigo = cliente.empresa ").append("\n");
        if (!UteisValidacao.emptyNumber(professor)) {
            sql.append(" inner join vinculo vin ON vin.colaborador = ").append(professor).append(" and vin.cliente = acessocliente.cliente").append("\n");
        }
        sql.append(" where empresa = ").append(empresa);
        sql.append(" and ((acessocliente.dthrentrada >= ? and acessocliente.dthrentrada <= ? )");
        sql.append(" or (acessocliente.dthrsaida >= ? ").append(" and acessocliente.dthrsaida <= ? ))");
        PreparedStatement stm = con.prepareStatement(sql.toString());
        stm.setTimestamp(1, Uteis.getDataJDBCTimestamp(inicio));
        stm.setTimestamp(2, Uteis.getDataJDBCTimestamp(fim));
        stm.setTimestamp(3, Uteis.getDataJDBCTimestamp(inicio));
        stm.setTimestamp(4, Uteis.getDataJDBCTimestamp(fim));
        List<Integer> clientes = new ArrayList<Integer>();
        try (ResultSet tabelaResultado = stm.executeQuery()) {
            while (tabelaResultado.next()) {
                clientes.add(tabelaResultado.getInt("cliente"));
            }
        }
        return clientes;
    }

    @Override
    public List<AcessoClienteVO> consultarAcessosClienteDataMeioIdentificacao(ClienteVO cliente, Date dataInicial, Date dataFinal, MeioIdentificacaoEnum meioIdentificacao, int nivelMontarDados) throws Exception {
        StringBuilder sql = new StringBuilder();
        sql.append("select * from AcessoCliente \n");
        sql.append("where 1 = 1 \n");
        if (!UteisValidacao.emptyNumber(cliente.getCodigo())) {
            sql.append("and cliente = ").append(cliente.getCodigo()).append(" \n");
        }
        if (meioIdentificacao != null) {
            sql.append("and meioidentificacaoentrada = ").append(meioIdentificacao.getCodigo()).append(" \n");
        }
        if (dataInicial != null) {
            sql.append("and dataregistro::DATE >= '").append(Uteis.getData(dataInicial)).append("' \n");
        }
        if (dataFinal != null) {
            sql.append("and dataregistro::DATE <= '").append(Uteis.getData(dataFinal)).append("' \n");
        }
        sql.append("order by dataregistro DESC");
        PreparedStatement stm = con.prepareStatement(sql.toString());
        ResultSet resultTabela = stm.executeQuery();
        return montarDadosConsulta(resultTabela, nivelMontarDados);
    }

    @Override
    public String consultarQuantidadeAcessosClientePeriodo(Integer codigoCliente, Date dataInicial, Date dataFinal) throws Exception {

        if (codigoCliente == null || dataInicial == null || dataFinal == null) {
            throw new Exception("Para consultar a quantidade de acessos do cliente, é necessário informar o código do cliente, a data inicial e a final.");
        }

        StringBuilder sql = new StringBuilder("SELECT count(1), dthrentrada::DATE FROM ")
                .append(AcessoCliente.class.getSimpleName())
                .append(" WHERE cliente = ").append(codigoCliente)
                .append(" AND dthrentrada BETWEEN '")
                .append(Uteis.getData(dataInicial, "bd")).append(" 00:00:00' AND '")
                .append(Uteis.getData(dataFinal, "bd")).append(" 23:59:59'")
                .append(" GROUP BY dthrentrada::DATE");

        Statement statement = null;
        ResultSet resultSet = null;
        try {
            statement = con.createStatement();
            resultSet = statement.executeQuery(sql.toString());

            AcessoClienteAgrupadoDataVO acessoClienteAgrupadoDataVO = new AcessoClienteAgrupadoDataVO();
            while (resultSet.next()) {
                acessoClienteAgrupadoDataVO.adicionarAcessoDiario(
                        resultSet.getLong(1), new Date(resultSet.getDate(2).getTime()));
            }

            return UteisJSON.toJSON(acessoClienteAgrupadoDataVO);
        } finally {
            if (resultSet != null && !resultSet.isClosed()) {
                resultSet.close();
            }

            if (statement != null && !statement.isClosed()) {
                statement.close();
            }
        }
    }

    @Override
    public AcessoClienteVO consultarUltimoAcessoDiferenteDe(ClienteVO cliente, Integer codigoDiferente, int nivelMontarDados) throws Exception {
        String sql = "SELECT * FROM AcessoCliente WHERE cliente = ? ";
        if (!UteisValidacao.emptyNumber(codigoDiferente)) {
            sql += " and codigo <> " + codigoDiferente + " \n ";
        }
        sql += " order by dthrentrada desc, codigo ASC limit 1";
        PreparedStatement sqlConsultar = con.prepareStatement(sql);
        sqlConsultar.setInt(1, cliente.getCodigo());
        ResultSet tabelaResultado = sqlConsultar.executeQuery();

        if (!tabelaResultado.next()) {
            return new AcessoClienteVO();
        } else {
            return montarDados(tabelaResultado, nivelMontarDados);
        }
    }

    @Override
    public List<AcessoClienteVO> consultarAcessoFuturo(int empresa, Date data, int nivelMontarDados) throws Exception {
        StringBuilder sql = new StringBuilder();
        sql.append("SELECT * from acessocliente ace \n");
        sql.append("inner join cliente cl on cl.codigo = ace.cliente \n");
        sql.append("where cast(ace.dthrentrada as date) > '").append(Uteis.getDataJDBC(data)).append("' \n");
        sql.append("and cl.empresa = ").append(empresa).append("\n");

        PreparedStatement stm = con.prepareStatement(sql.toString());
        ResultSet resultTabela = stm.executeQuery();
        return montarDadosConsulta(resultTabela, nivelMontarDados);
    }

    @Override
    public void excluirComLog(AcessoClienteVO acessoClienteVO, String observacao, ControleCreditoTreinoInterfaceFacade controleCreditoTreino, UsuarioVO usuario) throws Exception {
        String sql = "DELETE FROM acessoCliente WHERE codigo = ? ";
        PreparedStatement pst = con.prepareStatement(sql);
        pst.setInt(1, acessoClienteVO.getCodigo());
        pst.execute();
        controleCreditoTreino.atualizarUtilizacaoParaFaltaExclusaoAcesso(acessoClienteVO, observacao, false);
        gerarLogExclusao(acessoClienteVO, usuario);
    }

    private void gerarLogExclusao(AcessoClienteVO acessoClienteVo, UsuarioVO usuario) throws Exception {
        StringBuilder parametro = new StringBuilder();
        parametro.append("Foi excluído o acesso do cliente: ").append(acessoClienteVo.getCliente().getPessoa().getNome())
                .append(" Acesso do dia: ").append(acessoClienteVo.getDataHoraEntradaApresentar()).append("<br>");
        LogAjusteGeral logAjusteGeralDAO = new LogAjusteGeral(con);
        logAjusteGeralDAO.incluir(Calendario.hoje(), usuario.getNome(), usuario.getUserOamd(), ProcessoAjusteGeralEnum.EXCLUIR_ACESSO_FUTURO, parametro.toString());
    }

    @Override
    public void alterarAcessoEntradaSaida(AcessoClienteVO acessoClienteVO) throws Exception {
        try {
            StringBuilder sql = new StringBuilder();
            if (acessoClienteVO.getDataHoraSaida() != null) {
                sql.append(" UPDATE acessocliente SET dthrentrada = ? ,dthrsaida = ? where codigo = ? ");
            } else {
                sql.append(" UPDATE acessocliente SET dthrentrada = ? where codigo = ? ");
            }

            PreparedStatement pst = con.prepareStatement(sql.toString());
            if (acessoClienteVO.getDataHoraSaida() != null) {
                pst.setTimestamp(1, new java.sql.Timestamp(acessoClienteVO.getDataHoraEntrada().getTime()));
                pst.setTimestamp(2, new java.sql.Timestamp(acessoClienteVO.getDataHoraSaida().getTime()));
                pst.setInt(3, acessoClienteVO.getCodigo());
            } else {
                pst.setTimestamp(1, new java.sql.Timestamp(acessoClienteVO.getDataHoraEntrada().getTime()));
                pst.setInt(2, acessoClienteVO.getCodigo());
            }
            pst.execute();
        } catch (Exception e) {
            Logger.getLogger(getClass().getSimpleName()).log(Level.SEVERE, "#### ERRO AO ALTERAR ACESSO DO CLIENTE. ERRO: " + e.getMessage());
            throw e;
        }
    }

    @Override
    public List<AcessoClienteVO> consultaAcessoClienteDia(Integer empresa, Date data, int nivelMontarDados) throws Exception {

        StringBuilder sql = new StringBuilder();
        sql.append(" SELECT * from acessocliente ace \n");
        sql.append(" inner join cliente cl on cl.codigo = ace.cliente \n");
        sql.append(" WHERE dthrentrada >= ' " + Uteis.getDataJDBC(data) + " 00:00:00 ' ").append("\n");
        sql.append(" AND dthrentrada <= ' " + Uteis.getDataJDBC(data) + " 23:59:59 ' ").append("\n");
        sql.append(" and cl.empresa = ").append(empresa).append("\n");

        PreparedStatement stm = con.prepareStatement(sql.toString());
        ResultSet resultTabela = stm.executeQuery();
        return montarDadosConsulta(resultTabela, nivelMontarDados);

    }

    @Override
    public List<AcessoClienteVO> obterClientesPelaHora(Date dataInicial, Date dataFinal, Integer empresa, int nivelMontarDados) throws Exception {

        StringBuilder sql = new StringBuilder();
        sql.append("SELECT * FROM acessocliente ac INNER JOIN( \n");
        sql.append(" SELECT max(ac.codigo) codigo, cliente from acessocliente ac \n");
        sql.append(" inner join localacesso la on la.codigo = ac.localacesso \n");
        sql.append(" where (ac.dthrsaida is null \n");
        sql.append("        or (cast(ac.dthrsaida as date) = '" + Uteis.getDataJDBC(Calendario.getDataComHoraZerada(dataFinal)) + "'\n");
        sql.append("        and cast(ac.dthrsaida as time) > '" + Uteis.gethoraHHMMSSAjustado(dataFinal) + "')) \n");
        sql.append(" and ac.dthrentrada BETWEEN '" + Uteis.getDataJDBCTimestamp(dataInicial) + "' ");
        sql.append(" and '" + Uteis.getDataJDBCTimestamp(dataFinal) + "' \n");
        if (empresa != null) {
            sql.append(" and la.empresa = " + empresa).append("\n");
        }
        sql.append(" group by cliente ) t ON ac.codigo = t.codigo\n");
        sql.append(" order by dthrentrada");

        PreparedStatement stm = con.prepareStatement(sql.toString());
        ResultSet resultTabela = stm.executeQuery();
        return montarDadosConsulta(resultTabela, nivelMontarDados);
    }

    @Override
    public Integer obterQuantidadeClientePelaHora(Date dataInicial, Date dataFinal, Integer empresa) throws Exception {
        StringBuilder sql = new StringBuilder();
        sql.append(" SELECT count(distinct ac.cliente) as quantidade from acessocliente ac").append("\n");
        sql.append(" inner join localacesso la on la.codigo = ac.localacesso").append("\n");
        sql.append(" where (ac.dthrsaida is null").append("\n");
        sql.append("        or (cast(ac.dthrsaida as date) = '" + Uteis.getDataJDBC(Calendario.getDataComHoraZerada(dataFinal)) + "'\n");
        sql.append("        and cast(ac.dthrsaida as time) > '" + Uteis.gethoraHHMMSSAjustado(dataFinal) + "'))");
        sql.append(" and cast(ac.dthrentrada as date) = '" + Uteis.getDataJDBC(Calendario.getDataComHoraZerada(dataInicial)) + "'").append("\n");
        sql.append(" and cast(ac.dthrentrada as time) BETWEEN '" + Uteis.gethoraHHMMSSAjustado(dataInicial) + "' AND '" + Uteis.gethoraHHMMSSAjustado(dataFinal) + "'").append("\n");

        if (empresa != 0) {
            sql.append(" and la.empresa = " + empresa).append("\n");
        }

        PreparedStatement stm = con.prepareStatement(sql.toString());

        ResultSet tabelaResultado = stm.executeQuery();
        tabelaResultado.next();
        return tabelaResultado.getInt("quantidade");
    }

    @Override
    public String obterMediaTempoAcademiaPeriodo(Date dataInicial, Date dataFinal, Integer empresa) throws Exception {
        StringBuilder sql = new StringBuilder();
        sql.append(" SELECT AVG(ac.dthrsaida - ac.dthrentrada) as intervalo from acessocliente ac ").append("\n");
        sql.append(" inner join localacesso la on la.codigo = ac.localacesso ").append("\n");
        sql.append(" where ac.dthrsaida is not null ").append("\n");
        sql.append(" AND dthrentrada >= ' " + Uteis.getDataJDBC(dataInicial) + " 00:00:00 ' ").append("\n");
        sql.append(" AND dthrentrada <= ' " + Uteis.getDataJDBC(dataFinal) + " 23:59:59 ' ").append("\n");
        sql.append(" AND  ((ac.dthrsaida - ac.dthrentrada) > '00:10:00')  ");
        sql.append("  and la.empresa =  ").append(empresa);

        PreparedStatement stm = con.prepareStatement(sql.toString());
        ResultSet resultTabela = stm.executeQuery();

        resultTabela.next();
        return normalizaIntervalo(resultTabela.getString("intervalo"));
    }

    private String normalizaIntervalo(String intervalo) {
        if (intervalo == null || intervalo.trim().isEmpty()) {
            return "00:00:00";
        }
        intervalo = intervalo.trim();

        long totalSeconds = 0;

        try {
            int dias = 0;
            String tempo = intervalo;

            if (intervalo.contains("day")) {
                String[] partes = intervalo.split("day");
                dias = Integer.parseInt(partes[0].trim());
                tempo = partes[1].trim();
            }

            // Remove frações de segundo, se existir
            if (tempo.contains(".")) {
                tempo = tempo.substring(0, tempo.indexOf("."));
            }

            String[] hms = tempo.split(":");
            int horas = Integer.parseInt(hms[0]);
            int minutos = Integer.parseInt(hms[1]);
            int segundos = Integer.parseInt(hms[2]);

            totalSeconds = dias * 86400L + horas * 3600L + minutos * 60L + segundos;

            long horasTotais = totalSeconds / 3600;
            long minutosTotais = (totalSeconds % 3600) / 60;
            long segundosTotais = totalSeconds % 60;

            //Arredondar para 99 horas caso ultrapasse 100 horas
            if (horasTotais > 99) {
                horasTotais = 99;
                minutosTotais = 59;
                segundosTotais = 59;
            }

            return String.format("%02d:%02d:%02d", horasTotais, minutosTotais, segundosTotais);

        } catch (Exception e) {
            e.printStackTrace();
            return intervalo; // retorna original caso dê erro, para facilitar debug
        }
    }

    @Override
    public List<AcessoClienteVO> obterClientesPelaHoraComPendeciaFinanceira(Date dataInicial, Date dataFinal, Integer empresa, int nivelMontarDados) throws Exception {
        StringBuilder sqlCaixaEmAberto = new StringBuilder();
        sqlCaixaEmAberto.append(" SELECT ").append("\n");
        sqlCaixaEmAberto.append(" mp.contrato, ").append("\n");
        sqlCaixaEmAberto.append(" mp.vendaavulsa, ").append("\n");
        sqlCaixaEmAberto.append(" mp.aulaavulsadiaria, ").append("\n");
        sqlCaixaEmAberto.append(" mp.personal, ").append("\n");
        sqlCaixaEmAberto.append(" mp.pessoa, ").append("\n");
        sqlCaixaEmAberto.append(" pes.nome, ").append("\n");
        sqlCaixaEmAberto.append(" min(mp.dataregistro) as dataregistro ").append("\n");
        sqlCaixaEmAberto.append(" FROM movparcela mp ").append("\n");
        sqlCaixaEmAberto.append(" INNER JOIN pessoa pes ON pes.codigo = mp.pessoa ").append("\n");
        sqlCaixaEmAberto.append(" Left join colaborador co on co.pessoa = pes.codigo ").append("\n");
        sqlCaixaEmAberto.append(" LEFT JOIN cliente cli on pes.codigo = cli.pessoa ").append("\n");
        sqlCaixaEmAberto.append(" LEFT JOIN clientetitulardependente dependente ON cli.codigo = dependente.cliente ").append("\n");
        sqlCaixaEmAberto.append(" LEFT JOIN clientetitulardependente titular ON titular.codigo = dependente.clientetitular ").append("\n");
        sqlCaixaEmAberto.append(" LEFT JOIN clientetitulardependente dependentesDoTitular ON titular.codigo = dependentesDoTitular.clientetitular ").append("\n");
        sqlCaixaEmAberto.append(" LEFT JOIN cliente cliDependentes ON cliDependentes.codigo = dependentesDoTitular.cliente ").append("\n");
        sqlCaixaEmAberto.append(" LEFT JOIN negociacaoeventocontratoparcelas necp ON mp.codigo = necp.parcela ").append("\n");
        sqlCaixaEmAberto.append(" WHERE necp.parcela IS NULL ").append("\n");
        sqlCaixaEmAberto.append(" and ((pes.codigo = cliDependentes.pessoa) OR (pes.codigo = cli.pessoa AND dependente.codigo IS NULL) or (co.pessoa is not null)) ").append("\n");
        sqlCaixaEmAberto.append(" AND mp.regimerecorrencia = FALSE ").append("\n");
        sqlCaixaEmAberto.append(" AND mp.situacao= 'EA' ").append("\n");
        sqlCaixaEmAberto.append(" AND mp.datavencimento::DATE < '").append(Uteis.getDataJDBC(dataInicial)).append("' ").append("\n");
        sqlCaixaEmAberto.append(" AND cli.matricula = clie.matricula ").append("\n");
        sqlCaixaEmAberto.append(" AND mp.empresa = ").append(empresa).append("\n");
        sqlCaixaEmAberto.append(" GROUP BY mp.contrato, vendaavulsa, aulaavulsadiaria, personal, mp.pessoa, pes.nome ").append("\n");
        sqlCaixaEmAberto.append(" UNION ALL ").append("\n");
        sqlCaixaEmAberto.append(" SELECT mp.contrato, mp.vendaavulsa, mp.aulaavulsadiaria, mp.personal, mp.pessoa, v.nomecomprador AS nome, ").append("\n");
        sqlCaixaEmAberto.append(" min(mp.dataregistro) as dataregistro ").append("\n");
        sqlCaixaEmAberto.append(" FROM movparcela mp ").append("\n");
        sqlCaixaEmAberto.append(" INNER JOIN vendaavulsa v ON mp.vendaavulsa = v.codigo ").append("\n");
        sqlCaixaEmAberto.append(" INNER JOIN cliente c ON mp.pessoa = c.pessoa ").append("\n");
        sqlCaixaEmAberto.append(" WHERE v.tipocomprador LIKE 'CN' ").append("\n");
        sqlCaixaEmAberto.append(" AND mp.regimerecorrencia = FALSE ").append("\n");
        sqlCaixaEmAberto.append(" AND mp.situacao= 'EA' ").append("\n");
        sqlCaixaEmAberto.append(" AND mp.datavencimento::DATE < '").append(Uteis.getDataJDBC(dataInicial)).append("' ").append("\n");
        sqlCaixaEmAberto.append(" AND c.matricula = clie.matricula ").append("\n");
        sqlCaixaEmAberto.append(" AND mp.empresa = ").append(empresa).append("\n");
        sqlCaixaEmAberto.append(" GROUP BY contrato, vendaavulsa, aulaavulsadiaria, personal, mp.pessoa, v.nomecomprador ");

        StringBuilder sql = new StringBuilder();
        sql.append(" SELECT * from acessocliente ac").append("\n");
        sql.append(" inner join localacesso la on la.codigo = ac.localacesso").append("\n");
        sql.append(" inner join cliente clie on clie.codigo = ac.cliente").append("\n");
        sql.append(" where ac.dthrsaida is null").append("\n");
        sql.append(" and cast(ac.dthrentrada as date) = '" + Uteis.getDataJDBC(Calendario.getDataComHoraZerada(dataInicial)) + "'").append("\n");
        sql.append(" and cast(ac.dthrentrada as time) BETWEEN '" + Uteis.gethoraHHMMSSAjustado(dataInicial) + "' AND '" + Uteis.gethoraHHMMSSAjustado(dataFinal) + "'").append("\n");
        sql.append(" and la.empresa = " + empresa).append("\n");
        sql.append(" and clie.empresa = " + empresa).append("\n");
        sql.append(" and EXISTS (").append(sqlCaixaEmAberto).append(") \n");
        sql.append(" order by dthrentrada");

        PreparedStatement stm = con.prepareStatement(sql.toString());
        ResultSet resultTabela = stm.executeQuery();
        return montarDadosConsulta(resultTabela, nivelMontarDados);
    }

    @Override
    public Integer obterClientesPelaHoraComPendeciaFinanceira(Date dataInicial, Date dataFinal, Integer empresa) throws Exception {
        StringBuilder sqlCaixaEmAberto = new StringBuilder();
        sqlCaixaEmAberto.append(" SELECT ").append("\n");
        sqlCaixaEmAberto.append(" mp.contrato, ").append("\n");
        sqlCaixaEmAberto.append(" mp.vendaavulsa, ").append("\n");
        sqlCaixaEmAberto.append(" mp.aulaavulsadiaria, ").append("\n");
        sqlCaixaEmAberto.append(" mp.personal, ").append("\n");
        sqlCaixaEmAberto.append(" mp.pessoa, ").append("\n");
        sqlCaixaEmAberto.append(" pes.nome, ").append("\n");
        sqlCaixaEmAberto.append(" min(mp.dataregistro) as dataregistro ").append("\n");
        sqlCaixaEmAberto.append(" FROM movparcela mp ").append("\n");
        sqlCaixaEmAberto.append(" INNER JOIN pessoa pes ON pes.codigo = mp.pessoa ").append("\n");
        sqlCaixaEmAberto.append(" Left join colaborador co on co.pessoa = pes.codigo ").append("\n");
        sqlCaixaEmAberto.append(" LEFT JOIN cliente cli on pes.codigo = cli.pessoa ").append("\n");
        sqlCaixaEmAberto.append(" LEFT JOIN clientetitulardependente dependente ON cli.codigo = dependente.cliente ").append("\n");
        sqlCaixaEmAberto.append(" LEFT JOIN clientetitulardependente titular ON titular.codigo = dependente.clientetitular ").append("\n");
        sqlCaixaEmAberto.append(" LEFT JOIN clientetitulardependente dependentesDoTitular ON titular.codigo = dependentesDoTitular.clientetitular ").append("\n");
        sqlCaixaEmAberto.append(" LEFT JOIN cliente cliDependentes ON cliDependentes.codigo = dependentesDoTitular.cliente ").append("\n");
        sqlCaixaEmAberto.append(" LEFT JOIN negociacaoeventocontratoparcelas necp ON mp.codigo = necp.parcela ").append("\n");
        sqlCaixaEmAberto.append(" WHERE necp.parcela IS NULL ").append("\n");
        sqlCaixaEmAberto.append(" and ((pes.codigo = cliDependentes.pessoa) OR (pes.codigo = cli.pessoa AND dependente.codigo IS NULL) or (co.pessoa is not null)) ").append("\n");
        sqlCaixaEmAberto.append(" AND mp.regimerecorrencia = FALSE ").append("\n");
        sqlCaixaEmAberto.append(" AND mp.situacao= 'EA' ").append("\n");
        sqlCaixaEmAberto.append(" AND mp.datavencimento::DATE < '").append(Uteis.getDataJDBC(dataInicial)).append("' ").append("\n");
        sqlCaixaEmAberto.append(" AND cli.matricula = clie.matricula ").append("\n");
        if (empresa != 0) {
            sqlCaixaEmAberto.append(" AND mp.empresa = ").append(empresa).append("\n");
        }
        sqlCaixaEmAberto.append(" GROUP BY mp.contrato, vendaavulsa, aulaavulsadiaria, personal, mp.pessoa, pes.nome ").append("\n");
        sqlCaixaEmAberto.append(" UNION ALL ").append("\n");
        sqlCaixaEmAberto.append(" SELECT mp.contrato, mp.vendaavulsa, mp.aulaavulsadiaria, mp.personal, mp.pessoa, v.nomecomprador AS nome, ").append("\n");
        sqlCaixaEmAberto.append(" min(mp.dataregistro) as dataregistro ").append("\n");
        sqlCaixaEmAberto.append(" FROM movparcela mp ").append("\n");
        sqlCaixaEmAberto.append(" INNER JOIN vendaavulsa v ON mp.vendaavulsa = v.codigo ").append("\n");
        sqlCaixaEmAberto.append(" INNER JOIN cliente c ON mp.pessoa = c.pessoa ").append("\n");
        sqlCaixaEmAberto.append(" WHERE v.tipocomprador LIKE 'CN' ").append("\n");
        sqlCaixaEmAberto.append(" AND mp.regimerecorrencia = FALSE ").append("\n");
        sqlCaixaEmAberto.append(" AND mp.situacao= 'EA' ").append("\n");
        sqlCaixaEmAberto.append(" AND mp.datavencimento::DATE < '").append(Uteis.getDataJDBC(dataInicial)).append("' ").append("\n");
        sqlCaixaEmAberto.append(" AND c.matricula = clie.matricula ").append("\n");
        if (empresa != 0) {
            sqlCaixaEmAberto.append(" AND mp.empresa = ").append(empresa).append("\n");
        }
        sqlCaixaEmAberto.append(" GROUP BY contrato, vendaavulsa, aulaavulsadiaria, personal, mp.pessoa, v.nomecomprador ");

        StringBuilder sql = new StringBuilder();
        sql.append(" SELECT count(distinct ac.cliente) as valor from acessocliente ac").append("\n");
        sql.append(" inner join localacesso la on la.codigo = ac.localacesso").append("\n");
        sql.append(" inner join cliente clie on clie.codigo = ac.cliente").append("\n");
        sql.append(" where ac.dthrsaida is null").append("\n");
        sql.append(" and cast(ac.dthrentrada as date) = '" + Uteis.getDataJDBC(Calendario.getDataComHoraZerada(dataInicial)) + "'").append("\n");
        sql.append(" and cast(ac.dthrentrada as time) BETWEEN '" + Uteis.gethoraHHMMSSAjustado(dataInicial) + "' AND '" + Uteis.gethoraHHMMSSAjustado(dataFinal) + "'").append("\n");
        if (empresa != 0) {
            sql.append(" and la.empresa = " + empresa).append("\n");
            sql.append(" and clie.empresa = " + empresa).append("\n");
        }
        sql.append(" and EXISTS (").append(sqlCaixaEmAberto).append(") \n");

        PreparedStatement stm = con.prepareStatement(sql.toString());
        ResultSet resultTabela = stm.executeQuery();
        resultTabela.next();
        return resultTabela.getInt("valor");
    }

    @Override
    public List<AcessoClienteVO> obterClientesInativosPelaHora(Date dataInicial, Date dataFinal, Integer empresa, int nivelMontarDados) throws Exception {
        StringBuilder sql = new StringBuilder();
        sql.append(" SELECT * from acessocliente ac").append("\n");
        sql.append(" inner join localacesso la on la.codigo = ac.localacesso").append("\n");
        sql.append(" inner join cliente clie on clie.codigo = ac.cliente").append("\n");
        sql.append(" where ac.dthrsaida is null").append("\n");
        sql.append(" and cast(ac.dthrentrada as date) = '" + Uteis.getDataJDBC(Calendario.getDataComHoraZerada(dataInicial)) + "'").append("\n");
        sql.append(" and cast(ac.dthrentrada as time) BETWEEN '" + Uteis.gethoraHHMMSSAjustado(dataInicial) + "' AND '" + Uteis.gethoraHHMMSSAjustado(dataFinal) + "'").append("\n");
        sql.append(" and la.empresa = " + empresa).append("\n");
        sql.append(" and clie.situacao = 'IN' ").append("\n");
        sql.append(" and clie.empresa = " + empresa).append("\n");
        sql.append(" order by dthrentrada");

        PreparedStatement stm = con.prepareStatement(sql.toString());
        ResultSet resultTabela = stm.executeQuery();
        return montarDadosConsulta(resultTabela, nivelMontarDados);
    }

    @Override
    public Integer obterClientesInativosPelaHora(Date dataInicial, Date dataFinal, Integer empresa) throws Exception {
        StringBuilder sql = new StringBuilder();
        sql.append(" SELECT count(ac.cliente) as valor from acessocliente ac").append("\n");
        sql.append(" inner join localacesso la on la.codigo = ac.localacesso").append("\n");
        sql.append(" inner join cliente clie on clie.codigo = ac.cliente").append("\n");
        sql.append(" where ac.dthrsaida is null").append("\n");
        sql.append(" and cast(ac.dthrentrada as date) = '" + Uteis.getDataJDBC(Calendario.getDataComHoraZerada(dataInicial)) + "'").append("\n");
        sql.append(" and cast(ac.dthrentrada as time) BETWEEN '" + Uteis.gethoraHHMMSSAjustado(dataInicial) + "' AND '" + Uteis.gethoraHHMMSSAjustado(dataFinal) + "'").append("\n");
        if (empresa != 0) {
            sql.append(" and la.empresa = " + empresa).append("\n");
        }
        sql.append(" and clie.situacao = 'IN' ").append("\n");

        PreparedStatement stm = con.prepareStatement(sql.toString());
        ResultSet resultTabela = stm.executeQuery();
        resultTabela.next();
        return resultTabela.getInt("valor");
    }

    @Override
    public List<AcessoClienteVO> obterClientesOutraUnidadePelaHora(Date dataInicial, Date dataFinal, Integer empresa, int nivelMontarDados) throws Exception {
        StringBuilder sql = new StringBuilder();
        sql.append(" SELECT * from acessocliente ac").append("\n");
        sql.append(" inner join localacesso la on la.codigo = ac.localacesso").append("\n");
        sql.append(" inner join cliente clie on clie.codigo = ac.cliente").append("\n");
        sql.append(" where ac.dthrsaida is null").append("\n");
        sql.append(" and cast(ac.dthrentrada as date) = '" + Uteis.getDataJDBC(Calendario.getDataComHoraZerada(dataInicial)) + "'").append("\n");
        sql.append(" and cast(ac.dthrentrada as time) BETWEEN '" + Uteis.gethoraHHMMSSAjustado(dataInicial) + "' AND '" + Uteis.gethoraHHMMSSAjustado(dataFinal) + "'").append("\n");
        sql.append(" and la.empresa = " + empresa).append("\n");
        sql.append(" and clie.empresa <> " + empresa).append("\n");
        sql.append(" order by dthrentrada");

        PreparedStatement stm = con.prepareStatement(sql.toString());
        ResultSet resultTabela = stm.executeQuery();
        return montarDadosConsulta(resultTabela, nivelMontarDados);
    }

    @Override
    public Integer obterClientesOutraUnidadePelaHora(Date dataInicial, Date dataFinal, Integer empresa) throws Exception {
        StringBuilder sql = new StringBuilder();
        sql.append(" SELECT count(ac.cliente) as valor from acessocliente ac").append("\n");
        sql.append(" inner join localacesso la on la.codigo = ac.localacesso").append("\n");
        sql.append(" inner join cliente clie on clie.codigo = ac.cliente").append("\n");
        sql.append(" where ac.dthrsaida is null").append("\n");
        sql.append(" and cast(ac.dthrentrada as date) = '" + Uteis.getDataJDBC(Calendario.getDataComHoraZerada(dataInicial)) + "'").append("\n");
        sql.append(" and cast(ac.dthrentrada as time) BETWEEN '" + Uteis.gethoraHHMMSSAjustado(dataInicial) + "' AND '" + Uteis.gethoraHHMMSSAjustado(dataFinal) + "'").append("\n");
        if (empresa != 0) {
            sql.append(" and la.empresa = " + empresa).append("\n");
            sql.append(" and clie.empresa <> " + empresa).append("\n");
        }

        PreparedStatement stm = con.prepareStatement(sql.toString());
        ResultSet resultTabela = stm.executeQuery();
        resultTabela.next();
        return resultTabela.getInt("valor");
    }

    public String registrarTicket(String nomeOuMatriculaOuCodigoAcesso, String ticket, String dataHora) throws Exception {
        String sqlAtualizar = "UPDATE acessocliente SET ticket = ? \n" +
                "WHERE codigo IN \n" +
                "  (SELECT codigo FROM acessocliente \n" +
                "  WHERE cliente IN (SELECT cli.codigo FROM cliente cli\n" +
                "    INNER JOIN pessoa pes ON cli.pessoa = pes.codigo\n" +
                "       WHERE cli.codacesso = ? OR pes.nome = ? OR cli.matricula = ?)\n" +
                "   AND dthrentrada < ?\n" +
                "   AND dthrentrada::DATE = ?\n" +
                "   AND dthrsaida is null\n" +
                "   ORDER BY dthrentrada DESC\n" +
                "   LIMIT 1)\n" +
                "RETURNING codigo";

        PreparedStatement preparedStatement = con.prepareStatement(sqlAtualizar);
        int i = 0;
        preparedStatement.setString(++i, ticket);
        preparedStatement.setString(++i, nomeOuMatriculaOuCodigoAcesso);
        preparedStatement.setString(++i, nomeOuMatriculaOuCodigoAcesso);
        preparedStatement.setString(++i, nomeOuMatriculaOuCodigoAcesso);
        preparedStatement.setTimestamp(++i, Uteis.getDataJDBCTimestamp(Calendario.getDate("yyyy-MM-dd HH:mm:ss", dataHora)));
        preparedStatement.setDate(++i, Uteis.getDataJDBC(Calendario.getDate("yyyy-MM-dd HH:mm:ss", dataHora)));

        JSONArray results = new JSONArray();

        ResultSet rs = preparedStatement.executeQuery();
        while (rs.next()) {
            JSONObject objReturn = new JSONObject();
            objReturn.put("codigo", rs.getInt("codigo"));
            results.put(objReturn);
        }

        return results.toString();
    }

    public PessoaAcesso consultarTicket(PessoaAcesso cliente, Date dataAcesso) throws Exception {
        String sqlConsultar = "SELECT ticket, EXTRACT(EPOCH FROM (now()-dthrentrada)::INTERVAL)/60 as permanencia FROM acessocliente WHERE cliente = ? AND dthrentrada::DATE = ? ORDER BY dthrentrada DESC;";
        PreparedStatement preparedStatement = con.prepareStatement(sqlConsultar);
        int i = 0;
        preparedStatement.setInt(++i, cliente.getCodigo());
        preparedStatement.setDate(++i, Uteis.getDataJDBC(dataAcesso));
        ResultSet rs = preparedStatement.executeQuery();
        if (rs.next()) {
            cliente.setTicket(rs.getString("ticket"));
            cliente.setPermanencia(rs.getInt("permanencia"));
            return cliente;
        }
        return null;
    }

    public JSONArray obterAcessosDiaWeHelpClientes(Integer empresa, Date dia, boolean enviarCpfComoCodigoInterno) throws Exception {
        String sql = "select\n" +
                " emp.nome as nome_empresa,\n" +
                " cli.empresa as codigo_unidade,\n" +
                " pes.nome,\n" +
                " pes.cfp as cpf,\n" +
                " ac.cliente as codigo_pessoa,\n" +
                " pes.datacadastro,pes.datanasc,pes.sexo,est.descricao as estado,pa.nome as pais,\n" +
                " 'cliente' as tipo_pessoa,dthrentrada as data_hora_entrada,dthrsaida as data_hora_saida,pl.codigo as codPlano,sw.nomeplano as plano,sw.telefonescliente as telefone,pes.codigo as codigo_pes,\n" +
                " array_to_string(array(SELECT pes.nome FROM vinculo v LEFT JOIN colaborador col ON v.colaborador = col.codigo LEFT JOIN pessoa pes ON pes.codigo = col.pessoa WHERE v.cliente = cli.codigo AND v.tipovinculo IN ('PR', 'TW')),',') as vinculos,\n" +
                " array_to_string(array(SELECT cm.modalidade||'##'||mod.nome FROM contratomodalidade cm inner join modalidade mod on cm.modalidade = mod.codigo WHERE cm.contrato = sw.codigocontrato),',') as modalidades\n" +
                " from acessocliente ac  \n" +
                " INNER JOIN cliente cli ON cli.codigo = ac.cliente AND cli.empresa = ? \n" +
                " inner join pessoa pes on pes.codigo = cli.pessoa\n" +
                " inner join empresa emp on emp.codigo = cli.empresa\n" +
                " inner join situacaoclientesinteticodw sw on cli.codigo = sw.codigocliente\n" +
                " inner join contrato con on sw.codigocontrato = con.codigo\n" +
                " inner join plano pl on con.plano = pl.codigo\n" +
                " left join estado est on est.codigo = pes.estado\n" +
                " left join pais pa on pa.codigo = est.pais\n" +
                " WHERE dthrentrada BETWEEN ? AND ? \n" +
                " ORDER BY data_hora_entrada DESC \n";
        PreparedStatement stm = con.prepareStatement(sql);
        int i = 1;
        stm.setInt(i++, empresa);
        stm.setTimestamp(i++, Uteis.getDataJDBCTimestamp(Calendario.getDataComHoraZerada(dia)));
        stm.setTimestamp(i++, Uteis.getDataJDBCTimestamp(Calendario.getDataComHora(dia, "23:59:59")));
        ResultSet rs = stm.executeQuery();
        JSONArray json = new JSONArray();
        while (rs.next()) {
            ResultSet rsEmail = Email.criarConsulta("select email from email where pessoa = " + rs.getInt("codigo_pes"), con);
            StringBuilder email = new StringBuilder();
            while (rsEmail.next()) {
                email.append(rsEmail.getString("email")).append(";");
            }
            JSONObject obj = new JSONObject();
            if (enviarCpfComoCodigoInterno) {
                obj.put("internal_code", rs.getString("cpf"));
            }
            obj.put("nome_empresa", rs.getString("nome_empresa"));
            obj.put("codigo_unidade", rs.getInt("codigo_unidade"));
            obj.put("nome", rs.getString("nome"));
            obj.put("email", email.toString());
            obj.put("codigo_pessoa", rs.getInt("codigo_pessoa"));
            obj.put("data_cadastro", rs.getString("datacadastro"));
            obj.put("data_nascimento", rs.getString("datanasc"));
            obj.put("sexo", rs.getString("sexo"));
            obj.put("telefone", rs.getString("telefone"));
            obj.put("estado", rs.getString("estado"));
            obj.put("pais", rs.getString("pais"));
            obj.put("tipo_pessoa", rs.getString("tipo_pessoa"));
            obj.put("data_hora_entrada", rs.getString("data_hora_entrada"));
            obj.put("data_hora_saida", rs.getString("data_hora_saida"));
            obj.put("id_plano", rs.getInt("codPlano"));
            obj.put("plano", rs.getString("plano"));

            JSONArray arrayProfessores = new JSONArray();
            String professores = rs.getString("vinculos");
            if (!UteisValidacao.emptyString(professores)) {
                for (String professor : professores.split(",")) {
                    JSONObject objProfessor = new JSONObject();
                    objProfessor.put("nome", professor);
                    arrayProfessores.put(objProfessor);
                }
            }

            obj.put("professores", arrayProfessores);

            JSONArray arrayModalidades = new JSONArray();
            String modalidades = rs.getString("modalidades");
            if (!UteisValidacao.emptyString(modalidades)) {
                for (String objModalidade : modalidades.split(",")) {
                    try {
                        String[] objModalidadeSplited = objModalidade.split("##");
                        if (objModalidadeSplited.length > 1) {
                            Integer codigoModalidade = Integer.parseInt(objModalidadeSplited[0]);
                            String nomeModalidade = objModalidadeSplited[1];
                            JSONObject jsonModalidade = new JSONObject();
                            jsonModalidade.put("codigo", codigoModalidade);
                            jsonModalidade.put("nome", nomeModalidade);
                            arrayModalidades.put(jsonModalidade);
                        }
                    } catch (Exception ex) {
                        ex.printStackTrace();
                    }
                }
            }
            obj.put("modalidades", arrayModalidades);


            json.put(obj);
        }
        return json;
    }

    public JSONArray obterAcessosDiaWeHelpColaboradores(Integer empresa, Date dia, boolean enviarCpfComoCodigoInterno) throws Exception {

        String sql = " select emp.nome as nome_empresa,col.empresa as codigo_unidade,pes.nome,pes.cfp as cpf, col.codigo as codigo_pessoa,\n" +
                " pes.datacadastro,pes.datanasc,pes.sexo,est.descricao as estado,pa.nome as pais,\n" +
                " 'colaborador' as tipo_pessoa,dthrentrada as data_hora_entrada,dthrsaida as data_hora_saida,'' as plano,pes.codigo as codigo_pes\n" +
                " from acessocolaborador acc \n" +
                " inner join colaborador col on col.uacodigo = acc.codigo and col.empresa = ? \n" +
                " inner join pessoa pes on pes.codigo = col.pessoa\n" +
                " inner join empresa emp on emp.codigo = col.empresa\n" +
                " inner join estado est on est.codigo = pes.estado\n" +
                " inner join pais pa on pa.codigo = est.pais \n" +
                " WHERE dthrentrada BETWEEN ? AND ? \n" +
                " ORDER BY data_hora_entrada DESC \n";
        PreparedStatement stm = con.prepareStatement(sql);
        int i = 1;
        stm.setInt(i++, empresa);
        stm.setTimestamp(i++, Uteis.getDataJDBCTimestamp(Calendario.getDataComHoraZerada(dia)));
        stm.setTimestamp(i++, Uteis.getDataJDBCTimestamp(Calendario.getDataComHora(dia, "23:59:59")));
        ResultSet rs = stm.executeQuery();
        JSONArray json = new JSONArray();
        while (rs.next()) {
            ResultSet rsEmail = Email.criarConsulta("select email from email where pessoa = " + rs.getInt("codigo_pes"), con);
            ResultSet rsTelefone = Telefone.criarConsulta("select numero from telefone where pessoa = " + rs.getString("codigo_pes"), con);
            StringBuilder email = new StringBuilder();
            StringBuilder telefone = new StringBuilder();
            while (rsEmail.next()) {
                email.append(rsEmail.getString("email")).append(";");
            }
            while (rsTelefone.next()) {
                telefone.append(rsTelefone.getString("numero")).append(";");
            }
            JSONObject obj = new JSONObject();
            if (enviarCpfComoCodigoInterno) {
                obj.put("internal_code", rs.getString("cpf"));
            }
            obj.put("nome_empresa", rs.getString("nome_empresa"));
            obj.put("codigo_unidade", rs.getInt("codigo_unidade"));
            obj.put("nome", rs.getString("nome"));
            obj.put("email", email.toString());
            obj.put("codigo_pessoa", rs.getInt("codigo_pessoa"));
            obj.put("data_cadastro", rs.getString("datacadastro"));
            obj.put("data_nascimento", rs.getString("datanasc"));
            obj.put("sexo", rs.getString("sexo"));
            obj.put("telefone", telefone.toString());
            obj.put("estado", rs.getString("estado"));
            obj.put("pais", rs.getString("pais"));
            obj.put("tipo_pessoa", rs.getString("tipo_pessoa"));
            obj.put("data_hora_entrada", rs.getString("data_hora_entrada"));
            obj.put("data_hora_saida", rs.getString("data_hora_saida"));
            obj.put("plano", rs.getString("plano"));
            json.put(obj);
        }
        return json;
    }

    @Override
    public List<AcessoClienteVO> consultar(String sql, final int nivelMontarDados) throws Exception {
        try (ResultSet tabelaResultado = SuperFacadeJDBC.criarConsulta(sql, con)) {
            return (montarDadosConsulta(tabelaResultado, nivelMontarDados));
        }
    }

    public JSONObject consultarAlunosNaAcademia(Integer codEmpresa) throws Exception {
        Date agora = Calendario.hoje();
        String sql = "SELECT emp.capacidadesimultanea\n" +
                ", COUNT(DISTINCT ac.cliente) as lotacao\n" +
                ", MAX(dthrentrada) AS ultimaEntrada \n" +
                ", acessoSomenteComAgendamento \n" +
                "FROM empresa emp\n" +
                "LEFT JOIN localacesso la ON la.empresa = emp.codigo\n" +
                "LEFT JOIN acessocliente ac ON la.codigo = ac.localacesso\n" +
                "       AND dthrentrada + temposaidaacademia * '1 MINUTE'::INTERVAL > '" + Uteis.getDataJDBCTimestamp(agora) + "'\n" +
                "       AND dthrsaida IS NULL\n" +
                "       AND dthrentrada > '" + Uteis.getDataHoraJDBC(agora, "00:00:00") + "'\n" +
                "WHERE emp.codigo = " + codEmpresa + "\n" +
                "GROUP BY emp.capacidadesimultanea, emp.acessoSomenteComAgendamento;";

        return montarJSONAlunosNaAcademia(sql);
    }

    public Integer consultarQtdAlunosNaAcademiaPorLocalAcesso(Integer codEmpresa, Integer codigoLocalAcesso) throws Exception {

        Integer lotacao = 0;

        String sql = "SELECT COUNT(DISTINCT ac.cliente) as lotacao\n" +
                "FROM empresa emp\n" +
                "LEFT JOIN localacesso la ON la.empresa = emp.codigo\n" +
                "LEFT JOIN acessocliente ac ON la.codigo = ac.localacesso\n" +
                "       AND dthrentrada + temposaidaacademia * '1 MINUTE'::INTERVAL > '" + Uteis.getDataJDBCTimestamp(Calendario.hoje()) + "'\n" +
                "       AND dthrsaida IS NULL\n" +
                "       AND dthrentrada > '" + Uteis.getDataHoraJDBC(Calendario.hoje(), "00:00:00") + "'\n" +
                "WHERE emp.codigo = " + codEmpresa + "\n" +
                "AND la.codigo = " + codigoLocalAcesso + ";";

        try (ResultSet tabelaResultado = SuperFacadeJDBC.criarConsulta(sql, con)) {
            if (tabelaResultado.next()) {
                lotacao = tabelaResultado.getInt("lotacao");
            }
        }

        return lotacao;
    }



    public JSONObject consultarAlunosNaAcademiaDaqui10minutos(Integer codEmpresa) throws Exception {
        String sql = "SELECT emp.capacidadesimultanea\n" +
                ", COUNT(DISTINCT ac.cliente) as lotacao\n" +
                ", MAX(dthrentrada) AS ultimaEntrada \n" +
                ", acessoSomenteComAgendamento \n" +
                "FROM empresa emp\n" +
                "LEFT JOIN localacesso la ON la.empresa = emp.codigo\n" +
                "LEFT JOIN acessocliente ac ON la.codigo = ac.localacesso\n" +
                "       AND dthrentrada + temposaidaacademia * '1 MINUTE'::INTERVAL > now() + INTERVAL '10 minutes'\n" +
                "       AND dthrsaida IS NULL\n" +
                "WHERE emp.codigo = " + codEmpresa + "\n" +
                "GROUP BY emp.capacidadesimultanea, emp.acessoSomenteComAgendamento;";

        return montarJSONAlunosNaAcademia(sql);
    }

    public JSONObject consultarAlunosNaAcademia(Integer codEmpresa, Date dataAvaliar) throws Exception {
        String sql = "SELECT emp.capacidadesimultanea\n" +
                ", COUNT(DISTINCT ac.cliente) as lotacao\n" +
                ", MAX(dthrentrada) AS ultimaEntrada \n" +
                "FROM empresa emp\n" +
                "LEFT JOIN localacesso la ON la.empresa = emp.codigo\n" +
                "LEFT JOIN acessocliente ac ON la.codigo = ac.localacesso\n" +
                "       AND dthrentrada + temposaidaacademia * '1 MINUTE'::INTERVAL > '" + Uteis.getDataJDBCTimestamp(dataAvaliar) + "'\n" +
                "       AND dthrsaida IS NULL\n" +
                "WHERE emp.codigo = " + codEmpresa + "\n" +
                "GROUP BY emp.capacidadesimultanea;";

        return montarJSONAlunosNaAcademia(sql);
    }

    private JSONObject montarJSONAlunosNaAcademia(final String sql) throws Exception {
        try (ResultSet tabelaResultado = SuperFacadeJDBC.criarConsulta(sql, con)) {
            if (tabelaResultado.next()) {
                Date ultimaEntrada = tabelaResultado.getTimestamp("ultimaEntrada");
                JSONObject objAlunosNaAcademia = new JSONObject();
                objAlunosNaAcademia.put("capacidade", tabelaResultado.getInt("capacidadesimultanea"));
                objAlunosNaAcademia.put("naAcademia", tabelaResultado.getInt("lotacao"));
                objAlunosNaAcademia.put("ultimoAcesso", Uteis.getDataComHora(ultimaEntrada));
                objAlunosNaAcademia.put("acessoSomenteComAgendamento", tabelaResultado.getBoolean("acessoSomenteComAgendamento"));
                return objAlunosNaAcademia;
            }
            return new JSONObject();
        }
    }

    public Integer obterClientesNaAcademiaAulasAgendadas(Date dataConsulta, Integer codEmpresa) throws SQLException {
        StringBuilder sql = new StringBuilder();
        sql.append("SELECT count(*) as valor FROM (\n");
        sql.append("SELECT distinct aht.*, ht.diasemana, ht.horainicial, ht.horafinal, t.descricao, cli.matricula, pes.nome FROM acessocliente ac\n")
                .append("LEFT JOIN localacesso la ON ac.localacesso = la.codigo\n")
                .append("LEFT JOIN empresa emp ON emp.codigo = la.empresa\n")
                .append("LEFT JOIN alunohorarioturma aht ON aht.cliente = ac.cliente AND aht.dia::DATE = ac.dthrentrada::DATE\n")
                .append("LEFT JOIN horarioturma ht ON aht.horarioturma = ht.codigo\n")
                .append("LEFT JOIN turma t ON ht.turma = t.codigo\n")
                .append("LEFT JOIN cliente cli ON aht.cliente = cli.codigo\n")
                .append("LEFT JOIN pessoa pes ON cli.pessoa = pes.codigo\n")
                .append("WHERE     dthrentrada + temposaidaacademia * '1 MINUTE'::INTERVAL > now()\n")
                .append("      AND horafinal::time >= now()::time\n")
                .append("      AND dthrsaida IS NULL\n")
                .append("      AND aht.codigo IS NOT NULL\n");
        if (codEmpresa > 0) {
            sql.append(" AND emp.codigo = ").append(codEmpresa).append("\n");
        }
        sql.append(") as foo;");
        try (PreparedStatement stm = con.prepareStatement(sql.toString());
             ResultSet resultTabela = stm.executeQuery()) {
            resultTabela.next();
            return resultTabela.getInt("valor");
        }
    }

    public List<AlunoHorarioTurmaTO> consultarClientesNaAcademiaAulasAgendadas(Date dataConsulta, Integer codEmpresa) throws SQLException {
        StringBuilder sql = new StringBuilder();
        sql.append("SELECT distinct aht.*, ht.diasemana, ht.horainicial, ht.horafinal, t.descricao, cli.matricula, pes.nome FROM acessocliente ac\n")
                .append("LEFT JOIN localacesso la ON ac.localacesso = la.codigo\n")
                .append("LEFT JOIN empresa emp ON emp.codigo = la.empresa\n")
                .append("LEFT JOIN alunohorarioturma aht ON aht.cliente = ac.cliente AND aht.dia::DATE = ac.dthrentrada::DATE\n")
                .append("LEFT JOIN horarioturma ht ON aht.horarioturma = ht.codigo\n")
                .append("LEFT JOIN turma t ON ht.turma = t.codigo\n")
                .append("LEFT JOIN cliente cli ON aht.cliente = cli.codigo\n")
                .append("LEFT JOIN pessoa pes ON cli.pessoa = pes.codigo\n")
                .append("WHERE     dthrentrada + temposaidaacademia * '1 MINUTE'::INTERVAL > now()\n")
                .append("      AND horafinal::time >= now()::time\n")
                .append("      AND dthrsaida IS NULL\n")
                .append("      AND aht.codigo IS NOT NULL\n");
        if (codEmpresa > 0) {
            sql.append(" AND emp.codigo = ").append(codEmpresa).append("\n");
        }
        try (PreparedStatement stm = con.prepareStatement(sql.toString());
             ResultSet resultTabela = stm.executeQuery()) {
            List<AlunoHorarioTurmaTO> alunos = new ArrayList<>();
            while (resultTabela.next()) {
                AlunoHorarioTurmaTO aluno = new AlunoHorarioTurmaTO();
                aluno.setCodigoCliente(resultTabela.getInt("cliente"));
                aluno.setMatricula(resultTabela.getString("matricula"));
                aluno.setNome(resultTabela.getString("nome"));
                aluno.setInicioAula(resultTabela.getString("horainicial"));
                aluno.setFinalAula(resultTabela.getString("horafinal"));
                aluno.setDescricaoAula(resultTabela.getString("descricao"));

                alunos.add(aluno);
            }
            return alunos;
        }
    }

    public List<AcessoBloqueadoTO> consultarAcessosBloqueados(String periodo, Date primeiroDiaMes, Date dataConsulta, Integer codEmpresa) throws SQLException {

        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");

        String hoje = sdf.format(new Date());

        String dataConsultaFormatada = sdf.format(dataConsulta);

        String primeiroDiaMesFormatado = sdf.format(primeiroDiaMes);

        StringBuilder sql = new StringBuilder();

        sql.append("SELECT ")
                .append("ac.codigo, ")
                .append("ac.cliente, ")
                .append("ac.situacao, ")
                .append("ac.dthrentrada, ")
                .append("ac.dataregistro, ")
                .append("ac.meioidentificacaoentrada, ")
                .append("cli.matricula, ")
                .append("pes.nome ")
                .append("FROM acessocliente ac ")
                .append("INNER JOIN cliente cli ON ac.cliente = cli.codigo ")
                .append("INNER JOIN pessoa pes ON cli.pessoa = pes.codigo ");

        if (codEmpresa != null) {
            sql.append("INNER JOIN localacesso la ON la.codigo = ac.localacesso ");
        }

        sql.append("WHERE ac.situacao LIKE 'RV_BLOQ%' ");

        if (codEmpresa != null && codEmpresa != 0) {
            sql.append("AND la.empresa = ").append(codEmpresa).append(" ");
        }

        if(periodo.equals("mes")) {
            sql.append("AND DATE(ac.dthrentrada) BETWEEN '").append(primeiroDiaMesFormatado).append("' AND '").append(hoje).append("'");
        } else {
            sql.append("AND DATE(ac.dthrentrada) = '").append(dataConsultaFormatada).append("'");
        }

        String queryAcessosBloqueados = sql.toString();

        try (PreparedStatement stm = con.prepareStatement(queryAcessosBloqueados);
             ResultSet resultTabela = stm.executeQuery()) {
            List<AcessoBloqueadoTO> listaRegistrosAcessosBloqueados = new ArrayList<>();

            while (resultTabela.next()) {

                String situacaoAcessoId = resultTabela.getString("situacao");

                SituacaoAcessoEnum situacaoAcessoEnum = SituacaoAcessoEnum.consultarPorId(situacaoAcessoId);

                Integer meioIdentificacaoId = resultTabela.getInt("meioidentificacaoentrada");

                MeioIdentificacaoEnum meioIdentificacaoEnum = MeioIdentificacaoEnum.getMeioIdentificacao(meioIdentificacaoId);

                AcessoBloqueadoTO registroAcessoBloqueado = new AcessoBloqueadoTO();

                FormatadorData fd = new FormatadorData("dd/mm/yyyy hh:mm");

                registroAcessoBloqueado.setNomeAluno(resultTabela.getString("nome"));
                registroAcessoBloqueado.setCodigoCliente(resultTabela.getInt("cliente"));
                registroAcessoBloqueado.setMatriculaAluno(resultTabela.getString("matricula"));
                registroAcessoBloqueado.setMotivoBloqueio(situacaoAcessoEnum.getDescricao());
                registroAcessoBloqueado.setMeioIdentificacao(meioIdentificacaoEnum.getDescricao());
                registroAcessoBloqueado.setDataEntrada(resultTabela.getString("dthrentrada"));

                listaRegistrosAcessosBloqueados.add(registroAcessoBloqueado);
            }
            return listaRegistrosAcessosBloqueados;
        }
    }

    public List<Integer> consultarClientesNoHorario(Date data, String horaInicial, Integer codEmpresa) throws Exception {
        StringBuilder sql = new StringBuilder();
        sql.append("SELECT DISTINCT ac.cliente FROM acessocliente ac\n")
                .append("LEFT JOIN localacesso la ON ac.localacesso = la.codigo\n")
                .append("LEFT JOIN empresa emp ON la.empresa = emp.codigo AND la.empresa = ").append(codEmpresa).append("\n")
                .append("WHERE dthrentrada + temposaidaacademia * '1 MINUTE'::INTERVAL > '").append(Uteis.getDataHoraJDBC(data, horaInicial)).append("'\n")
                .append("AND dthrsaida IS NULL");

        List<Integer> clientes = new ArrayList<>();
        try (PreparedStatement stm = con.prepareStatement(sql.toString());
             ResultSet resultTabela = stm.executeQuery()) {
            while (resultTabela.next()) {
                clientes.add(resultTabela.getInt("cliente"));
            }
        }
        return clientes;
    }

    public int contarPresencasPorPeriodoModalidade(Date dataInicio, Date dataFim, Integer codEmpresa, Integer codModalidade) throws Exception {
        String sqlStr = "SELECT count(DISTINCT ac.cliente || ' - ' || (ac.dthrentrada::DATE)) as qtd FROM acessocliente ac\n" +
                "INNER JOIN localacesso la ON ac.localacesso = la.codigo\n" +
                "INNER JOIN cliente cli ON ac.cliente = cli.codigo\n" +
                "LEFT JOIN situacaoclientesinteticodw  sw ON sw.codigocliente = cli.codigo\n" +
                "INNER JOIN contrato c ON sw.codigocontrato = c.codigo\n" +
                "INNER JOIN contratomodalidade cm ON sw.codigocontrato = cm.contrato\n" +
                "WHERE la.empresa = " + codEmpresa + "\n" +
                "AND cm.modalidade = " + codModalidade + "\n" +
                "AND dthrentrada BETWEEN '" + Uteis.getDataHoraJDBC(dataInicio, "00:00:00") + "' AND '" + Uteis.getDataHoraJDBC(dataFim, "23:59:59") + "';";

        try (PreparedStatement stm = con.prepareStatement(sqlStr);
             ResultSet tabelaResultado = stm.executeQuery()) {
            if (tabelaResultado.next()) {
                return tabelaResultado.getInt("qtd");
            }
        }
        return 0;
    }

    public JSONArray consultarClientesSemTemplateBiometrico() throws SQLException {
        String sqlStr = "select codacesso, codacessoalternativo, cli.matricula from cliente cli\n" +
                "inner join pessoa pes on cli.pessoa  = pes.codigo \n" +
                "where coalesce(pes.assinaturadigitalbiometria, '') = '';";

        JSONArray array = new JSONArray();
        try (PreparedStatement stm = con.prepareStatement(sqlStr);
             ResultSet tabelaResultado = stm.executeQuery()) {
            while (tabelaResultado.next()) {
                JSONObject obj = new JSONObject();
                obj.put("codacesso", tabelaResultado.getString("codacesso"));
                obj.put("codacessoalternativo", tabelaResultado.getString("codacessoalternativo"));
                obj.put("matricula", tabelaResultado.getString("matricula"));
                array.put(obj);
            }
        }
        return array;
    }

    public JSONArray consultarColaboradoresSemTemplateBiometrico() throws SQLException {
        String sqlStr = "select codacesso, codacessoalternativo, col.codigo from colaborador col\n" +
                "inner join pessoa pes on col.pessoa  = pes.codigo \n" +
                "where coalesce(pes.assinaturadigitalbiometria, '') = '' ;";

        JSONArray array = new JSONArray();
        try (PreparedStatement stm = con.prepareStatement(sqlStr);
             ResultSet tabelaResultado = stm.executeQuery()) {
            while (tabelaResultado.next()) {
                JSONObject obj = new JSONObject();
                obj.put("codacesso", tabelaResultado.getString("codacesso"));
                obj.put("codacessoalternativo", tabelaResultado.getString("codacessoalternativo"));
                obj.put("matricula", tabelaResultado.getString("codigo"));
                array.put(obj);
            }
        }
        return array;
    }

    public Map<Integer, Integer> consultarQtdClientesAcessoGymPass(Date dataInicio, Date dataFim, Integer codigoEmpresa) throws Exception {
        StringBuilder sqlStr = new StringBuilder();
        sqlStr.append("SELECT count(*) AS qtd, ac.cliente AS cliente\n");
        sqlStr.append(" FROM acessocliente ac\n");
        sqlStr.append("         JOIN cliente c ON ac.cliente = c.codigo\n");
        sqlStr.append("         JOIN periodoacessocliente pac ON c.pessoa = pac.pessoa\n");
        sqlStr.append("         LEFT JOIN infocheckin i ON i.periodoacesso = pac.codigo\n");
        sqlStr.append(" WHERE c.gympassuniquetoken IS NOT NULL\n");
        sqlStr.append("  AND COALESCE(pac.tokenGymPass, '') <> ''\n");
        sqlStr.append("  AND pac.datainicioacesso::date <= ac.dthrentrada::date\n");
        sqlStr.append("  AND pac.datafinalacesso::date >= ac.dthrentrada::date\n");
        sqlStr.append("  AND pac.tipoacesso = 'PL'\n");
        sqlStr.append("  AND dthrentrada BETWEEN '").append(Uteis.getDataBDComHHMM(dataInicio)).append("' AND '").append(Uteis.getDataBDComHHMM(dataFim)).append("'\n");
        sqlStr.append(" AND ((c.empresa=").append(codigoEmpresa).append(" and i.codigo is null ) ");
        sqlStr.append(" or (i.codigo is not null and i.empresa = ").append(codigoEmpresa).append("))");
        sqlStr.append(" GROUP BY ac.cliente;");
        Map<Integer, Integer> resultado = new HashMap<>();

        try (PreparedStatement stm = con.prepareStatement(sqlStr.toString());
             ResultSet tabelaResultado = stm.executeQuery()) {
            while (tabelaResultado.next()) {
                resultado.put( tabelaResultado.getInt("cliente"),tabelaResultado.getInt("qtd"));
            }
        }
        return resultado.size() > 0 ? resultado : null;
    }

    public Map<Integer, Integer> consultarQtdClientesAcessoGymPassPorCheckin(Date dataInicio, Date dataFim, Integer codigoEmpresa) throws Exception {
        StringBuilder sqlStr = new StringBuilder();
        sqlStr.append("select count(*) AS qtd, cli.codigo  AS cliente\n");
        sqlStr.append("from  periodoacessocliente pa\n");
        sqlStr.append("inner join pessoa p ON p.codigo = pa.pessoa\n");
        sqlStr.append("JOIN cliente cli ON cli.pessoa  = p.codigo\n");
        sqlStr.append("left join acessocliente ac on ac.cliente = cli.codigo\n");
        sqlStr.append("and pa.datainicioacesso::date <= ac.dthrentrada::date\n");
        sqlStr.append("and pa.datafinalacesso::date >= ac.dthrentrada::date\n");
        sqlStr.append("where pa.tokengympass is not null and pa.tokengympass <> '' \n");
        sqlStr.append("and cli.empresa= ").append(codigoEmpresa).append("\n");
        sqlStr.append("and pa.datainicioacesso BETWEEN '").append(Uteis.getDataBDComHHMM(dataInicio)).append("' AND '").append(Uteis.getDataBDComHHMM(dataFim)).append("'\n");
        sqlStr.append("and ac.codigo is null\n");
        sqlStr.append("GROUP BY cli.codigo;\n");

        Map<Integer, Integer> resultado = new HashMap<>();

        try (PreparedStatement stm = con.prepareStatement(sqlStr.toString());
             ResultSet tabelaResultado = stm.executeQuery()) {
            while (tabelaResultado.next()) {
                resultado.put( tabelaResultado.getInt("cliente"),tabelaResultado.getInt("qtd"));
            }
        }
        return resultado.size() > 0 ? resultado : null;
    }

    public List<ResumoPessoaBIAcessoGymPassVO> consultarClientesAcessoGymPass(Date dataInicio, Date dataFim, Integer codigoEmpresa) throws Exception {
        StringBuilder sqlStr = new StringBuilder();
        sqlStr.append("SELECT c.codigo AS cliente, \n");
        sqlStr.append("c.matricula AS matricula, \n");
        sqlStr.append("pac.tokengympass AS tokenGymPass, \n");
        sqlStr.append("p.nome AS nome, \n");
        sqlStr.append("c.situacao AS situacaoCliente, \n");
        sqlStr.append("ac.dthrentrada AS dataAcesso \n");
        sqlStr.append("FROM acessocliente ac \n");
        sqlStr.append("INNER JOIN cliente c ON ac.cliente = c.codigo \n");
        sqlStr.append("INNER JOIN pessoa p ON p.codigo = c.pessoa \n");
        sqlStr.append("INNER JOIN periodoacessocliente pac ON c.pessoa = pac.pessoa \n");
        sqlStr.append("LEFT JOIN infocheckin i ON i.periodoacesso = pac.codigo \n");
        sqlStr.append("WHERE c.gympassuniquetoken IS NOT NULL \n");
        sqlStr.append("AND COALESCE(pac.tokenGymPass, '') <> '' \n");
        sqlStr.append("AND pac.datainicioacesso::date <= ac.dthrentrada::date \n");
        sqlStr.append("AND pac.datafinalacesso::date >= ac.dthrentrada::date \n");
        sqlStr.append("AND pac.tipoacesso = 'PL' \n");
        sqlStr.append("AND dthrentrada BETWEEN '");
        sqlStr.append(Uteis.getDataBDComHHMM(dataInicio));
        sqlStr.append("' AND '");
        sqlStr.append(Uteis.getDataBDComHHMM(dataFim)).append("' \n");
        sqlStr.append("AND ((c.empresa=").append(codigoEmpresa).append(" and i.codigo is null ) ");
        sqlStr.append(" or (i.codigo is not null and i.empresa = ").append(codigoEmpresa).append("))");
        sqlStr.append("GROUP BY c.codigo, c.matricula, pac.tokengympass, p.nome, c.situacao, ac.dthrentrada;");


        List<ResumoPessoaBIAcessoGymPassVO> lista = new ArrayList<>();

        try (PreparedStatement stm = con.prepareStatement(sqlStr.toString());
             ResultSet rs = stm.executeQuery()) {
            while (rs.next()) {
                ResumoPessoaBIAcessoGymPassVO obj = new ResumoPessoaBIAcessoGymPassVO();
                obj.setMatricula(rs.getString("matricula"));
                obj.setNome(rs.getString("nome"));
                obj.setCliente(rs.getInt("cliente"));
                obj.setSituacaoCliente(rs.getString("situacaoCliente"));
                obj.setDataAcesso(rs.getDate("dataAcesso"));
                obj.setTokenGymPass(rs.getString("tokenGymPass"));
                lista.add(obj);
            }
        }
        return lista;
    }

    public List<ResumoPessoaBIAcessoGymPassVO> consultarClientesAcessoGymPassPorCheckin(Date dataInicio,
                                                                                        Date dataFim, Integer codigoEmpresa) throws Exception {
        StringBuilder sqlStr = new StringBuilder();
        sqlStr.append("select pa.tipoacesso,\n");
        sqlStr.append("cli.codigo AS cliente,\n");
        sqlStr.append("cli.matricula AS matricula,\n");
        sqlStr.append("pa.tokengympass AS tokenGymPass,\n");
        sqlStr.append("p.nome AS nome,\n");
        sqlStr.append("cli.situacao AS situacaoCliente,\n");
        sqlStr.append("coalesce(ac.dthrentrada, pa.datainicioacesso) as dataAcesso\n");
        sqlStr.append("from  periodoacessocliente pa\n");
        sqlStr.append("inner join pessoa p ON p.codigo = pa.pessoa\n");
        sqlStr.append("JOIN cliente cli ON cli.pessoa  = p.codigo\n");
        sqlStr.append("left join acessocliente ac on ac.cliente = cli.codigo\n");
        sqlStr.append("and pa.datainicioacesso::date <= ac.dthrentrada::date\n");
        sqlStr.append("and pa.datafinalacesso::date >= ac.dthrentrada::date\n");
        sqlStr.append("where pa.tokengympass is not null and pa.tokengympass <> '' \n");
        sqlStr.append("and cli.empresa= ").append(codigoEmpresa).append("\n");
        sqlStr.append("and pa.datainicioacesso BETWEEN '").append(Uteis.getDataBDComHHMM(dataInicio)).append("' AND '").append(Uteis.getDataBDComHHMM(dataFim)).append("'\n");
        sqlStr.append("and ac.codigo is null;\n");

        List<ResumoPessoaBIAcessoGymPassVO> lista = new ArrayList<>();

        try (PreparedStatement stm = con.prepareStatement(sqlStr.toString());
             ResultSet rs = stm.executeQuery()) {
            while (rs.next()) {
                ResumoPessoaBIAcessoGymPassVO obj = new ResumoPessoaBIAcessoGymPassVO();
                obj.setMatricula(rs.getString("matricula"));
                obj.setNome(rs.getString("nome"));
                obj.setCliente(rs.getInt("cliente"));
                obj.setSituacaoCliente(rs.getString("situacaoCliente"));
                obj.setDataAcesso(rs.getTimestamp("dataAcesso"));
                obj.setTokenGymPass(rs.getString("tokenGymPass"));
                lista.add(obj);
            }
        }
        return lista;
    }

}

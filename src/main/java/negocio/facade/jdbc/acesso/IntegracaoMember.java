package negocio.facade.jdbc.acesso;

import negocio.comuns.acesso.IntegracaoMemberVO;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisValidacao;
import negocio.facade.jdbc.arquitetura.SuperEntidade;
import negocio.interfaces.acesso.IntegracaoMemberInterfaceFacade;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.Statement;
import java.util.ArrayList;
import java.util.List;

public class IntegracaoMember extends SuperEntidade implements IntegracaoMemberInterfaceFacade {

    public IntegracaoMember() throws Exception {
        super();
    }

    public IntegracaoMember(Connection conexao) throws Exception {
        super(conexao);
    }

    public void incluir(IntegracaoMemberVO obj) throws Exception {
        String sql = "INSERT INTO IntegracaoMember(username, token, descricao, descrica<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, emp<PERSON><PERSON>, member<PERSON><PERSON><PERSON>ass, " +
                "consult<PERSON><PERSON><PERSON><PERSON>, professor<PERSON><PERSON><PERSON>, d<PERSON><PERSON><PERSON><PERSON>, plan<PERSON><PERSON><PERSON><PERSON>, moda<PERSON><PERSON><PERSON><PERSON>, ho<PERSON>io<PERSON>rao, diasCarencia, dataInicialConsiderarLancamentos)\n"
                + " VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)";
        int i = 0;
        try (PreparedStatement sqlInserir = con.prepareStatement(sql)) {
            sqlInserir.setString(++i, obj.getUsername());
            sqlInserir.setString(++i, obj.getToken());
            sqlInserir.setString(++i, obj.getDescricao());
            sqlInserir.setString(++i, obj.getDescricaoParcialPlano());
            sqlInserir.setInt(++i, obj.getEmpresa().getCodigo());
            sqlInserir.setBoolean(++i, obj.isMemberFreePass());
            sqlInserir.setInt(++i, obj.getConsultorPadrao());
            sqlInserir.setInt(++i, obj.getProfessorPadrao());
            sqlInserir.setString(++i, obj.getDddPadrao());
            sqlInserir.setInt(++i, obj.getPlanoPadrao());
            sqlInserir.setInt(++i, obj.getModalidadePadrao());
            sqlInserir.setInt(++i, obj.getHorarioPadrao());
            sqlInserir.setInt(++i, obj.getDiasCarencia());
            if (obj.getDataInicialConsiderarLancamentos() != null) {
                sqlInserir.setDate(++i, Uteis.getDataJDBC(obj.getDataInicialConsiderarLancamentos()));
            } else {
                sqlInserir.setNull(++i, 0);
            }
            sqlInserir.execute();
        }
        obj.setCodigo(obterValorChavePrimariaCodigo());
        obj.setNovoObj(false);
    }

    public void alterar(IntegracaoMemberVO obj) throws Exception {
        String sql = "UPDATE IntegracaoMember " +
                "SET descricao = ?, username = ?, token = ?, descricaoparcialplano = ?, empresa = ?,\n" +
                "primeirasincronizacao = ?, ultimasincronizacao = ?, memberFreePass = ?, consultorPadrao = ?, professorPadrao = ?, dddPadrao = ?, \n" +
                "planoPadrao = ?, modalidadePadrao = ?, horarioPadrao = ?, diasCarencia = ?, dataInicialConsiderarLancamentos = ? \n" +
                "WHERE codigo = ?";
        int i = 0;
        try (PreparedStatement sqlAlterar = con.prepareStatement(sql)) {
            sqlAlterar.setString(++i, obj.getDescricao());
            sqlAlterar.setString(++i, obj.getUsername());
            sqlAlterar.setString(++i, obj.getToken());
            sqlAlterar.setString(++i, obj.getDescricaoParcialPlano());
            sqlAlterar.setInt(++i, obj.getEmpresa().getCodigo());
            resolveTimestampNull(sqlAlterar, ++i, obj.getPrimeiraSincronizacao());
            resolveTimestampNull(sqlAlterar, ++i, obj.getUltimaSincronizacao());
            sqlAlterar.setBoolean(++i, obj.isMemberFreePass());
            if (!UteisValidacao.emptyNumber(obj.getConsultorPadrao())) {
                sqlAlterar.setInt(++i, obj.getConsultorPadrao());
            } else {
                sqlAlterar.setNull(++i, 0);
            }
            if (!UteisValidacao.emptyNumber(obj.getProfessorPadrao())) {
                sqlAlterar.setInt(++i, obj.getProfessorPadrao());
            } else {
                sqlAlterar.setNull(++i, 0);
            }
            if (!UteisValidacao.emptyString(obj.getDddPadrao())) {
                sqlAlterar.setString(++i, obj.getDddPadrao());
            } else {
                sqlAlterar.setNull(++i, 0);
            }
            if (!UteisValidacao.emptyNumber(obj.getPlanoPadrao())) {
                sqlAlterar.setInt(++i, obj.getPlanoPadrao());
            } else {
                sqlAlterar.setNull(++i, 0);
            }
            if (!UteisValidacao.emptyNumber(obj.getModalidadePadrao())) {
                sqlAlterar.setInt(++i, obj.getModalidadePadrao());
            } else {
                sqlAlterar.setNull(++i, 0);
            }
            if (!UteisValidacao.emptyNumber(obj.getHorarioPadrao())) {
                sqlAlterar.setInt(++i, obj.getHorarioPadrao());
            } else {
                sqlAlterar.setNull(++i, 0);
            }
            if (!UteisValidacao.emptyNumber(obj.getDiasCarencia())) {
                sqlAlterar.setInt(++i, obj.getDiasCarencia());
            } else {
                sqlAlterar.setNull(++i, 0);
            }
            if (obj.getDataInicialConsiderarLancamentos() != null) {
                sqlAlterar.setDate(++i, Uteis.getDataJDBC(obj.getDataInicialConsiderarLancamentos()));
            } else {
                sqlAlterar.setNull(++i, 0);
            }
            sqlAlterar.setInt(++i, obj.getCodigo());
            sqlAlterar.execute();
        }
    }

    public void excluir(IntegracaoMemberVO obj) throws Exception {
        try {
            con.setAutoCommit(false);
            String sql = "DELETE FROM integracaomember WHERE codigo = ?";
            try (PreparedStatement sqlExcluir = con.prepareStatement(sql)) {
                sqlExcluir.setInt(1, obj.getCodigo());
                sqlExcluir.execute();
            }
            con.commit();
        } catch (Exception e) {
            con.rollback();
            con.setAutoCommit(true);
            throw e;
        } finally {
            con.setAutoCommit(true);
        }
    }

    public List<IntegracaoMemberVO> consultarIntegracoesPorEmpresa(Integer empresa) throws Exception {
        return consultarIntegracoesPorEmpresa(empresa, true);
    }

    public List<IntegracaoMemberVO> consultarIntegracoesPorEmpresa(Integer empresa, boolean memberFreePass) throws Exception {
        StringBuilder sqlStr = new StringBuilder();
        sqlStr.append("SELECT * FROM integracaomember\n");
        sqlStr.append("WHERE 1 = 1");
        if (!UteisValidacao.emptyNumber(empresa)) {
            sqlStr.append(" AND empresa = ").append(empresa).append("\n");
        }
        if (memberFreePass) {
            sqlStr.append(" AND memberFreePass is true \n");
        }
        sqlStr.append(" ORDER BY descricao");

        try (Statement stm = con.createStatement();
             ResultSet rs = stm.executeQuery(sqlStr.toString())) {
            List<IntegracaoMemberVO> lista = new ArrayList<>();
            while (rs.next()) {
                IntegracaoMemberVO integracaoMemberVO = montarDados(rs);
                lista.add(integracaoMemberVO);
            }
            return lista;
        }
    }

    public IntegracaoMemberVO consultarIntegracaoPorEmpresa(Integer empresa) throws Exception {
        if (UteisValidacao.emptyNumber(empresa)) {
            throw new Exception("Empresa não informada!");
        }
        StringBuilder sqlStr = new StringBuilder();
        sqlStr.append("SELECT * FROM integracaomember\n");
        sqlStr.append(" WHERE empresa = ").append(empresa).append("\n");
        sqlStr.append("ORDER BY codigo desc");

        try (Statement stm = con.createStatement(); ResultSet rs = stm.executeQuery(sqlStr.toString())) {
            return rs.next() ? montarDados(rs) : new IntegracaoMemberVO();
        }
    }

    public IntegracaoMemberVO consultarPorCodigo(Integer valorConsulta) throws Exception {
        String sqlStr = "SELECT * FROM integracaomember where codigo = " + valorConsulta;
        try (Statement stm = con.createStatement()) {
            try (ResultSet tabelaResultado = stm.executeQuery(sqlStr)) {
                tabelaResultado.next();
                return montarDados(tabelaResultado);
            }
        }
    }

    public static IntegracaoMemberVO montarDados(ResultSet dadosSQL) throws Exception {
        return montarDadosBasico(dadosSQL);
    }

    public static IntegracaoMemberVO montarDadosBasico(ResultSet dadosSQL) throws Exception {
        IntegracaoMemberVO obj = new IntegracaoMemberVO();
        obj.setNovoObj(false);

        obj.setCodigo(dadosSQL.getInt("codigo"));
        obj.setDescricao(dadosSQL.getString("descricao"));
        obj.setUsername(dadosSQL.getString("username"));
        obj.setToken(dadosSQL.getString("token"));
        obj.setDescricaoParcialPlano(dadosSQL.getString("descricaoparcialplano"));
        obj.getEmpresa().setCodigo(dadosSQL.getInt("empresa"));
        obj.setPrimeiraSincronizacao(dadosSQL.getTimestamp("primeirasincronizacao"));
        obj.setUltimaSincronizacao(dadosSQL.getTimestamp("ultimasincronizacao"));
        try {
            obj.setConsultorPadrao(dadosSQL.getInt("consultorPadrao"));
            obj.setProfessorPadrao(dadosSQL.getInt("professorPadrao"));
            obj.setDddPadrao(dadosSQL.getString("dddPadrao"));
            obj.setMemberFreePass(dadosSQL.getBoolean("memberFreePass"));
            obj.setPlanoPadrao(dadosSQL.getInt("planoPadrao"));
            obj.setModalidadePadrao(dadosSQL.getInt("modalidadePadrao"));
            obj.setHorarioPadrao(dadosSQL.getInt("horarioPadrao"));
            obj.setDiasCarencia(dadosSQL.getInt("diasCarencia"));
            obj.setDataInicialConsiderarLancamentos(dadosSQL.getDate("dataInicialConsiderarLancamentos"));
        } catch (Exception ignore) {}
        return obj;
    }
}

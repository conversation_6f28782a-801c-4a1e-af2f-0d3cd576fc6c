/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package negocio.facade.jdbc.acesso;

import br.com.pactosolucoes.comuns.util.JSFUtilities;
import negocio.comuns.acesso.AcessoClienteVO;
import negocio.comuns.acesso.ColetorVO;
import negocio.comuns.acesso.LiberacaoAcessoVO;
import negocio.comuns.acesso.LocalAcessoVO;
import negocio.comuns.acesso.enumerador.DirecaoAcessoEnum;
import negocio.comuns.acesso.enumerador.MeioIdentificacaoEnum;
import negocio.comuns.acesso.enumerador.SituacaoAcessoEnum;
import negocio.comuns.acesso.enumerador.TipoLiberacaoEnum;
import negocio.comuns.arquitetura.UsuarioVO;
import negocio.comuns.basico.ClienteVO;
import negocio.comuns.basico.PessoaVO;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisValidacao;
import negocio.facade.jdbc.arquitetura.SuperEntidade;
import negocio.facade.jdbc.arquitetura.ZillyonWebFacade;
import negocio.facade.jdbc.basico.Cliente;
import negocio.facade.jdbc.contrato.ControleCreditoTreino;
import negocio.interfaces.acesso.LiberacaoAcessoInterfaceFacade;
import relatorio.controle.basico.FechamentoAcessoRelTO;
import relatorio.negocio.comuns.sad.SituacaoClienteSinteticoEnum;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.sql.Statement;
import java.sql.Types;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 *
 * <AUTHOR>
 */
public class LiberacaoAcesso extends SuperEntidade implements LiberacaoAcessoInterfaceFacade {

    protected static String idEntidade;

    public LiberacaoAcesso() throws Exception {
        super();
    }

    public LiberacaoAcesso(Connection conexao) throws Exception {
        super(conexao);
    }

    private LiberacaoAcessoVO incluir(LiberacaoAcessoVO obj) throws Exception {
        try {
            //incluir(idEntidade);
            con.setAutoCommit(false);
            String sql = "INSERT INTO liberacaoAcesso (tipoLiberacao, datahora, sentido, localacesso, coletor, usuario, empresa, pessoa, justificativa, dthrjustificativa, usuariojustificou, nomegenerico) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)";
            PreparedStatement sqlInserir = con.prepareStatement(sql);
            int i = 0;
            sqlInserir.setInt(++i, obj.getTipoLiberacao().getCodigo());
            sqlInserir.setTimestamp(++i, new java.sql.Timestamp(obj.getDataHora().getTime()));
            sqlInserir.setString(++i, obj.getSentido().getId());
            sqlInserir.setInt(++i, obj.getLocalAcesso().getCodigo());
            sqlInserir.setInt(++i, obj.getColetor().getCodigo());
            sqlInserir.setInt(++i, obj.getUsuario().getCodigo());
            sqlInserir.setInt(++i, obj.getEmpresa());
            if (obj.getPessoa() == null) {
                sqlInserir = resolveFKNull(sqlInserir, ++i, 0);
            } else {
                sqlInserir = resolveFKNull(sqlInserir, ++i, obj.getPessoa().getCodigo());
            }
            sqlInserir.setString(++i, obj.getJustificativa());
            sqlInserir.setTimestamp(++i, Uteis.getDataJDBCTimestamp(obj.getDataHora()));
            sqlInserir.setInt(++i, obj.getUsuarioJustificou().getCodigo());
            if (UteisValidacao.emptyString(obj.getNomeGenerico())) {
                sqlInserir.setNull(++i, Types.NULL);
            } else {
                sqlInserir.setString(++i, obj.getNomeGenerico());
            }
            sqlInserir.execute();
            obj.setCodigo(obterValorChavePrimariaCodigo());
            obj.setNovoObj(false);
            con.commit();
            return obj;
        } catch (Exception e) {
            obj.setNovoObj(Boolean.TRUE);
            con.rollback();
            con.setAutoCommit(true);
            throw e;
        } finally {
            con.setAutoCommit(true);
        }

    }

    public void incluirJustificativa(LiberacaoAcessoVO obj, String nomeLabelPessoa) throws Exception {
        try {
            con.setAutoCommit(false);
            LiberacaoAcessoVO.validarDados(obj, nomeLabelPessoa);
            String sql = "UPDATE liberacaoacesso set justificativa=?, dthrjustificativa=?, usuariojustificou=?, "
                    + " pessoa=? WHERE ((codigo = ?))";
            PreparedStatement sqlAlterar = con.prepareStatement(sql);
            sqlAlterar.setString(1, obj.getJustificativa());
            sqlAlterar.setTimestamp(2, Uteis.getDataJDBCTimestamp(obj.getDthrJustificativa()));
            sqlAlterar.setInt(3, obj.getUsuarioJustificou().getCodigo());
            if (obj.getPessoa() != null && obj.getPessoa().getCodigo() != 0) {
                sqlAlterar.setInt(4, obj.getPessoa().getCodigo());
            } else {
                sqlAlterar.setNull(4, java.sql.Types.NULL);
            }
            sqlAlterar.setInt(5, obj.getCodigo());
            sqlAlterar.execute();
            con.commit();
        } catch (Exception e) {
            con.rollback();
            throw e;
        } finally {
            con.setAutoCommit(true);
        }
    }

    /**
     * Analisa se há algum usuário que justificou cadastrado com esse codigo de liberação
     * @param codigo
     * @return
     * @throws SQLException
     */
    public int consultarUsuarioJustificou(int codigoLiberacao) throws SQLException {
        StringBuilder sql = new StringBuilder();
        sql.append("select usuarioJustificou from liberacaoacesso where codigo = " + codigoLiberacao);
        PreparedStatement stm = con.prepareStatement(sql.toString());
        ResultSet tabelaResultado = stm.executeQuery();
        tabelaResultado.next();
        return tabelaResultado.getInt("usuarioJustificou");
    }

    /**
     * Consulta liberação de acesso por codigo
     * @param codigo
     * @return
     * @throws SQLException
     */
    public LiberacaoAcessoVO consultarPorChavePrimaria(int codigoLiberacao, int nivelMontarDados) throws SQLException, Exception {
        StringBuilder sql = new StringBuilder();
        sql.append("select lib.*, localacesso.descricao as descricaoLocal, "
                + "coletor.descricao as descricaoColetor, usu.nome as nomeUsuarioLiberou, "
                + "usuJust.nome as nomeUsuarioJustificou, pes.nome as nomePessoa "
                + "from liberacaoacesso as lib "
                + "inner join localacesso on localacesso.codigo = lib.localacesso "
                + "inner join coletor on coletor.codigo = lib.coletor "
                + "inner join usuario as usu on usu.codigo = lib.usuario "
                + "left join usuario as usuJust on usuJust.codigo = lib.usuariojustificou "
                + "left join pessoa as pes on pes.codigo = lib.pessoa "
                + "where lib.codigo = " + codigoLiberacao);
        Statement stm = con.createStatement();
        ResultSet tabelaResultado = stm.executeQuery(sql.toString());
        if (tabelaResultado.next()) {
            return montarDados(tabelaResultado, nivelMontarDados, con);
        }
        return new LiberacaoAcessoVO();
    }

    public LiberacaoAcessoVO registrarAcesso(Date dataAcesso, DirecaoAcessoEnum direcao, TipoLiberacaoEnum tipoLiberacao, LocalAcessoVO local, ColetorVO coletor, UsuarioVO usuario, Integer empresa, PessoaVO pessoa, String situacaoCliente, String justificativa, String nomeGenerico) throws Exception {
        LiberacaoAcessoVO obj = new LiberacaoAcessoVO();
        obj.setTipoLiberacao(tipoLiberacao);
        if (TipoLiberacaoEnum.CLIENTE.equals(tipoLiberacao) && "VI".equals(situacaoCliente)) {
            obj.setTipoLiberacao(TipoLiberacaoEnum.CLIENTE_VISITANTE);
        }
        obj.setColetor(coletor);
        obj.setDataHora(dataAcesso);
        obj.setLocalAcesso(local);
        obj.setUsuario(usuario);
        obj.setSentido(direcao);
        obj.setEmpresa(empresa);
        obj.setPessoa(pessoa);
        obj.setJustificativa(justificativa);
        obj.setDthrJustificativa(dataAcesso);
        obj.setUsuarioJustificou(usuario);
        obj.setNomeGenerico(nomeGenerico);
        this.incluir(obj);
        if (local.getSolicitarJustificativaLiberacaoManual()) {
            incluirJustificativa(obj, nomeGenerico);
        }
        return obj;
    }

    public int contarTotalLiberacaoFiltros(List<TipoLiberacaoEnum> listaTipoLiberacao, int empresa, java.sql.Date dataInicial, java.sql.Date dataFinal, String horaInicial, String horaFinal) throws SQLException {
        StringBuilder sql = new StringBuilder();
        sql.append("select count(*) as qtde from liberacaoacesso ");
        sql.append("where empresa = " + empresa + " ");
        sql.append("and (liberacaoacesso.datahora >= '" + dataInicial + " " + horaInicial + "' ");
        sql.append("and liberacaoacesso.datahora <= '" + dataFinal + " " + horaFinal + "') ");

        /* boolean temp = true;
        for (TipoLiberacaoEnum tipoLiberacao : listaTipoLiberacao) {
        if (temp) {
        temp = false;
        sqlStr += " AND (tipoliberacao = " + tipoLiberacao.getCodigo();
        } else {
        sqlStr += " OR tipoliberacao = " + tipoLiberacao.getCodigo();
        }

        }
        if (!temp) {
        sqlStr += ")";
        }*/
        StringBuilder sbIn = new StringBuilder();
        for (TipoLiberacaoEnum tipoLiberacao : listaTipoLiberacao) {
            if (sbIn.length() == 0) {
                sbIn.append("and tipoliberacao in(");
                sbIn.append(tipoLiberacao.getCodigo());
            } else {
                sbIn.append(",");
                sbIn.append(tipoLiberacao.getCodigo());
            }
        }
        if (!listaTipoLiberacao.isEmpty()) {
            sbIn.append(")");
        }
        Statement stm = con.createStatement();
        ResultSet tabelaResultado = stm.executeQuery(sql.toString() + sbIn.toString());
        if (tabelaResultado.next()) {
            return tabelaResultado.getInt("qtde");
        } else {
            return 0;
        }
    }

    public List<LiberacaoAcessoVO> consultarTotalLiberacaoFiltros(TipoLiberacaoEnum tipoLiberacao, int empresa, java.sql.Date dataInicial,
            java.sql.Date dataFinal, String horaInicial, String horaFinal, int nivelMontarDados, boolean justificados, boolean naoJustificados) throws Exception {
        String sqlStr = "select lib.*, localacesso.descricao as descricaoLocal, "
                + "coletor.descricao as descricaoColetor, usu.nome as nomeUsuarioLiberou, "
                + "usuJust.nome as nomeUsuarioJustificou, pes.nome as nomePessoa "
                + "from liberacaoacesso as lib "
                + "inner join localacesso on localacesso.codigo = lib.localacesso "
                + "inner join coletor on coletor.codigo = lib.coletor "
                + "inner join usuario as usu on usu.codigo = lib.usuario "
                + "left join usuario as usuJust on usuJust.codigo = lib.usuariojustificou "
                + "left join pessoa as pes on pes.codigo = lib.pessoa "
                + "where lib.empresa = " + empresa + " and "
                + "(lib.datahora >= '" + dataInicial + " " + horaInicial + "' "
                + "and lib.datahora <= '" + dataFinal + " " + horaFinal + "') ";
        if (tipoLiberacao != null) {
            sqlStr += " AND lib.tipoliberacao = " + tipoLiberacao.getCodigo();
        }
        if (justificados) {
            sqlStr += " AND coalesce(lib.justificativa, '') <> '' and lib.dthrjustificativa "
                    + "is not null and lib.usuariojustificou is not null ";
        }
        if (naoJustificados) {
            sqlStr += " AND coalesce(lib.justificativa, '') = ''";
        }

        sqlStr += " order by lib.dthrjustificativa";

        Statement stm = con.createStatement();
        ResultSet tabelaResultado = stm.executeQuery(sqlStr);
        return (montarDadosConsulta(tabelaResultado, nivelMontarDados, this.con));
    }

    public List<FechamentoAcessoRelTO> montarConsultaRelatorio(int totalLiberacoes, Double percentualLiberacoes, List<TipoLiberacaoEnum> listaTiposLiberacao,
            int empresa, java.sql.Date dataInicial, java.sql.Date dataFinal, String horaInicial, String horaFinal) throws SQLException, Exception {
        List<FechamentoAcessoRelTO> listaFechamentos = new ArrayList<FechamentoAcessoRelTO>();
        for (TipoLiberacaoEnum tiposLiberacao : listaTiposLiberacao) {
            //consultar total de acessos
            FechamentoAcessoRelTO fechamentoAcessoRelTO = new FechamentoAcessoRelTO();
            fechamentoAcessoRelTO.setTipoLiberacaoEnum(tiposLiberacao);
            String sqlStrTotalAcesso = "select count(*) as qtde from liberacaoacesso "
                    + "where empresa = " + empresa + " "
                    + "and (liberacaoacesso.datahora >= '" + dataInicial + " " + horaInicial + "' "
                    + "and liberacaoacesso.datahora <= '" + dataFinal + " " + horaFinal + "') and tipoLiberacao = " + tiposLiberacao.getCodigo();
            PreparedStatement sqlConsultar = con.prepareStatement(sqlStrTotalAcesso.toString());
            ResultSet tabelaResultado = sqlConsultar.executeQuery();
            if (tabelaResultado.next()) {
                fechamentoAcessoRelTO.setTotalAcesso(tabelaResultado.getInt("qtde"));
                if (fechamentoAcessoRelTO.getTotalAcesso() > 0) {
                    fechamentoAcessoRelTO.setPercentualAcessos(Uteis.arredondarForcando2CasasDecimais(
                            percentualLiberacoes * fechamentoAcessoRelTO.getTotalAcesso() / totalLiberacoes));
                }
            }
            //consultar liberações já justificadas
            String sqlStrJustificadas = "select count(*) as qtde from liberacaoacesso "
                    + "\nwhere empresa = " + empresa + " "
                    + "\nand (liberacaoacesso.datahora >= '" + dataInicial + " " + horaInicial + "' "
                    + "\nand liberacaoacesso.datahora <= '" + dataFinal + " " + horaFinal + "') and tipoLiberacao = " + tiposLiberacao.getCodigo()
                    + "\nand coalesce(justificativa, '') <> '' and dthrjustificativa is not null and usuariojustificou is not null;";
            sqlConsultar = con.prepareStatement(sqlStrJustificadas.toString());
            tabelaResultado = sqlConsultar.executeQuery();
            if (tabelaResultado.next()) {
                fechamentoAcessoRelTO.setJaJustificado(tabelaResultado.getInt("qtde"));
            }

            //consultar liberações que faltam ser justificadas
            String sqlStrFaltamJustificar = "select count(*) as qtde from liberacaoacesso "
                    + "where empresa = " + empresa + " "
                    + "and (liberacaoacesso.datahora >= '" + dataInicial + " " + horaInicial + "' "
                    + "and liberacaoacesso.datahora <= '" + dataFinal + " " + horaFinal + "') and tipoLiberacao = " + tiposLiberacao.getCodigo()
                    + "and coalesce(justificativa, '') = '';";
            sqlConsultar = con.prepareStatement(sqlStrFaltamJustificar.toString());
            tabelaResultado = sqlConsultar.executeQuery();
            if (tabelaResultado.next()) {
                fechamentoAcessoRelTO.setFaltaJustificar(tabelaResultado.getInt("qtde"));
            }
            listaFechamentos.add(fechamentoAcessoRelTO);
        }
        FechamentoAcessoRelTO total = new FechamentoAcessoRelTO();
        // total.setDescricaoTipoLiberacao_Apresentar("Total:");
        for (FechamentoAcessoRelTO fec : listaFechamentos) {
            total.setTotalAcesso(total.getTotalAcesso() + fec.getTotalAcesso());
            total.setJaJustificado(total.getJaJustificado() + fec.getJaJustificado());
            total.setFaltaJustificar(total.getFaltaJustificar() + fec.getFaltaJustificar());
        }
        //arrendondar a soma total 
        total.setPercentualAcessos(percentualLiberacoes);
        listaFechamentos.add(total);
        return listaFechamentos;
    }

    public static LiberacaoAcessoVO montarDados(ResultSet dadosSQL, int nivelMontarDados, Connection con) throws Exception {
        LiberacaoAcessoVO obj = montarDadosBasico(dadosSQL, nivelMontarDados);

        if (Uteis.NIVELMONTARDADOS_DADOSBASICOS == nivelMontarDados) {
            return obj;
        }
        if (Uteis.NIVELMONTARDADOS_DADOSENTIDADESPRINCIPAIS == nivelMontarDados) {
            obj.getLocalAcesso().setDescricao(dadosSQL.getString("descricaoLocal"));
            obj.getColetor().setDescricao(dadosSQL.getString("descricaoColetor"));
            obj.getUsuario().setNome(dadosSQL.getString("nomeUsuarioLiberou"));
            obj.getUsuarioJustificou().setNome(dadosSQL.getString("nomeUsuarioJustificou"));
            obj.getPessoa().setNome(dadosSQL.getString("nomePessoa"));
            return obj;
        }
        return obj;
    }

    public static LiberacaoAcessoVO montarDadosBasico(ResultSet dadosSQL, int nivelMontarDados) throws Exception {
        LiberacaoAcessoVO obj = new LiberacaoAcessoVO();
        obj.setNovoObj(false);
        obj.setCodigo(dadosSQL.getInt("codigo"));
        obj.getPessoa().setCodigo(dadosSQL.getInt("pessoa"));
        obj.setTipoLiberacao(TipoLiberacaoEnum.getTipoLiberacao(dadosSQL.getInt("tipoliberacao")));
        obj.setSentido(DirecaoAcessoEnum.getDirecaoAcessoEnum(dadosSQL.getString("sentido")));
        obj.setLocalAcesso(new LocalAcessoVO());
        obj.getLocalAcesso().setCodigo(dadosSQL.getInt("localacesso"));
        obj.setColetor(new ColetorVO());
        obj.getColetor().setCodigo(dadosSQL.getInt("coletor"));
        obj.setUsuario(new UsuarioVO());
        obj.getUsuario().setCodigo(dadosSQL.getInt("usuario"));
        obj.setEmpresa(dadosSQL.getInt("empresa"));
        obj.setDataHora(dadosSQL.getTimestamp("dataHora"));
        obj.setJustificativa(dadosSQL.getString("justificativa"));
        obj.setDthrJustificativa(dadosSQL.getTimestamp("dthrjustificativa"));
        obj.setUsuarioJustificou(new UsuarioVO());
        obj.getUsuarioJustificou().setCodigo(dadosSQL.getInt("usuariojustificou"));
        obj.setNomeGenerico(dadosSQL.getString("nomegenerico"));
        return obj;
    }

    public static List<LiberacaoAcessoVO> montarDadosConsulta(ResultSet tabelaResultado, int nivelMontarDados, Connection con) throws Exception {
        List<LiberacaoAcessoVO> vetResultado = new ArrayList();
        while (tabelaResultado.next()) {
            LiberacaoAcessoVO obj = new LiberacaoAcessoVO();
            obj = montarDados(tabelaResultado, nivelMontarDados, con);
            vetResultado.add(obj);
        }
        return vetResultado;
    }

    @Override
    public List<LiberacaoAcessoVO> consultarTotalLiberacao(Date data, Integer empresa,int nivelMontarDados) throws Exception {
        String sqlStr = "select lib.*, localacesso.descricao as descricaoLocal, "
                + "coletor.descricao as descricaoColetor, usu.nome as nomeUsuarioLiberou, "
                + "usuJust.nome as nomeUsuarioJustificou, pes.nome as nomePessoa "
                + "from liberacaoacesso as lib "
                + "inner join localacesso on localacesso.codigo = lib.localacesso "
                + "inner join coletor on coletor.codigo = lib.coletor "
                + "inner join usuario as usu on usu.codigo = lib.usuario "
                + "left join usuario as usuJust on usuJust.codigo = lib.usuariojustificou "
                + "left join pessoa as pes on pes.codigo = lib.pessoa "
                + "where lib.empresa = " + empresa
                + "and cast(lib.datahora as date) = '" + Uteis.getDataJDBC(data) + "' ";

        sqlStr += " order by lib.dthrjustificativa";

        Statement stm = con.createStatement();
        ResultSet tabelaResultado = stm.executeQuery(sqlStr);
        return (montarDadosConsulta(tabelaResultado, nivelMontarDados, this.con));
    }
    
    @Override
    public Integer consultarTotalLiberacao(Date data, Integer empresa) throws Exception {
        String sqlStr = "select COUNT(lib.codigo) as valor "
                + "from liberacaoacesso as lib "
                + "inner join localacesso on localacesso.codigo = lib.localacesso "
                + "inner join coletor on coletor.codigo = lib.coletor "
                + "inner join usuario as usu on usu.codigo = lib.usuario "
                + "left join usuario as usuJust on usuJust.codigo = lib.usuariojustificou "
                + "left join pessoa as pes on pes.codigo = lib.pessoa "
                + "WHERE";

        if(empresa != 0){
            sqlStr += " lib.empresa = " + empresa + " and ";
        }

        sqlStr += " cast(lib.datahora as date) = '" + Uteis.getDataJDBC(data) + "' ";

        Statement stm = con.createStatement();
        ResultSet tabelaResultado = stm.executeQuery(sqlStr);
        tabelaResultado.next();
        return tabelaResultado.getInt("valor");
    }
}

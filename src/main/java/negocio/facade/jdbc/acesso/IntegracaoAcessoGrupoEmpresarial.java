package negocio.facade.jdbc.acesso;

import negocio.comuns.acesso.IntegracaoAcessoGrupoEmpresarialVO;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisValidacao;
import negocio.facade.jdbc.arquitetura.SuperEntidade;
import negocio.facade.jdbc.basico.Empresa;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.util.ArrayList;
import java.util.List;

public class IntegracaoAcessoGrupoEmpresarial extends SuperEntidade implements negocio.interfaces.acesso.IntegracaoAcessoGrupoEmpresarialInterfaceFacade {

    public IntegracaoAcessoGrupoEmpresarial(Connection conexao) throws Exception {
        super(conexao);
        // TODO Auto-generated constructor stub
    }

    public IntegracaoAcessoGrupoEmpresarial() throws Exception {
    }

    @Override
    public void alterar(IntegracaoAcessoGrupoEmpresarialVO obj) throws Exception {
        try {
            con.setAutoCommit(false);
            alterarSemCommit(obj);
            con.commit();
        } catch (Exception e) {
            con.rollback();
            con.setAutoCommit(true);
            throw e;
        } finally {
            con.setAutoCommit(true);
        }
    }

    @Override
    public void alterarSemCommit(IntegracaoAcessoGrupoEmpresarialVO obj) throws Exception {
        IntegracaoAcessoGrupoEmpresarialVO.validarDados(obj);
        StringBuilder sqlAlterar = new StringBuilder();
        sqlAlterar.append("UPDATE integracaoacessogrupoempresarial set descricao = ?, empresalocal = ?, ");
        sqlAlterar.append(" urlZillyonWeb = ?, chave = ?, empresaremota = ?, localacesso = ?, coletor = ?, nomeempresa = ?, terminal = ?, ");
        sqlAlterar.append(" codigoChaveIntegracaoDigitais = ?\n");
        sqlAlterar.append(" where codigo = ?");
        PreparedStatement stm = con.prepareStatement(sqlAlterar.toString());
        int i = 1;
        stm.setString(i++, obj.getDescricao().toUpperCase());
        stm.setInt(i++, obj.getEmpresaLocal().getCodigo());
        stm.setString(i++, obj.getUrlZillyonWeb());
        stm.setString(i++, obj.getChave());
        stm.setInt(i++, obj.getEmpresaRemota().getCodigo());
        stm.setInt(i++, obj.getLocalAcesso());
        stm.setInt(i++, obj.getColetor());
        stm.setString(i++, obj.getEmpresaRemota().getNome());
        stm.setInt(i++, obj.getTerminal());
        if (obj.getCodigoChaveIntegracaoDigitais() != 0) {
            stm.setInt(i++, obj.getCodigoChaveIntegracaoDigitais());
        } else {
            stm.setNull(i++, 0);
        }
        stm.setInt(i++, obj.getCodigo());
        
        stm.execute();
    }

    public boolean obterPodeExcluir(int codigo) throws Exception{
        return !existe("SELECT * FROM autorizacaoacessogrupoempresarial "
                + "WHERE integracaoacessogrupoempresarial = "+codigo, con);
    }
    @Override
    public List<IntegracaoAcessoGrupoEmpresarialVO> consultar(String descricao, Integer empresa, Integer nivelMontarDados) throws Exception {
        if (descricao == null) {
            descricao = "";
        }
        String sql = "SELECT * FROM integracaoacessogrupoempresarial WHERE descricao LIKE '" + descricao.toUpperCase() + "%'";
        if (!UteisValidacao.emptyNumber(empresa)) {
            sql += " AND empresalocal = " + empresa;
        }
        sql += " order by descricao ";
        ResultSet query = con.prepareStatement(sql).executeQuery();
        return montarDadosConsulta(query, nivelMontarDados, con);
    }

    public List<IntegracaoAcessoGrupoEmpresarialVO> montarDadosConsulta(ResultSet tabelaResultado, int nivelMontarDados, Connection con) throws Exception {
        List<IntegracaoAcessoGrupoEmpresarialVO> vetResultado = new ArrayList<IntegracaoAcessoGrupoEmpresarialVO>();
        while (tabelaResultado.next()) {
            vetResultado.add(montarDados(tabelaResultado, nivelMontarDados, con));
        }
        return vetResultado;
    }

    public IntegracaoAcessoGrupoEmpresarialVO montarDados(ResultSet dadosSQL, int nivelMontarDados, Connection con) throws Exception {
        IntegracaoAcessoGrupoEmpresarialVO obj = new IntegracaoAcessoGrupoEmpresarialVO();
        obj.setChave(dadosSQL.getString("chave"));
        obj.setUrlZillyonWeb(dadosSQL.getString("urlzillyonweb"));
        obj.setDescricao(dadosSQL.getString("descricao"));
        Empresa empDAO = new Empresa(con);
        try {
            obj.setEmpresaLocal(empDAO.consultarPorCodigo(dadosSQL.getInt("empresalocal"), Uteis.NIVELMONTARDADOS_DADOSBASICOS));
        } catch (Exception ignore) {
            obj.getEmpresaLocal().setCodigo(dadosSQL.getInt("empresalocal"));
        }finally {
            empDAO = null;
        }
        obj.getEmpresaRemota().setCodigo(dadosSQL.getInt("empresaremota"));
        obj.setLocalAcesso(dadosSQL.getInt("localacesso"));
        obj.setColetor(dadosSQL.getInt("coletor"));
        obj.setCodigo(dadosSQL.getInt("codigo"));
        obj.setTerminal(dadosSQL.getInt("terminal"));
        obj.getEmpresaRemota().setNome(dadosSQL.getString("nomeempresa"));
        obj.setCodigoChaveIntegracaoDigitais(dadosSQL.getInt("codigoChaveIntegracaoDigitais"));
        obj.setNovoObj(false);
        obj.setPodeExcluir(obterPodeExcluir(obj.getCodigo()));
        return obj;
    }

    @Override
    public void incluir(IntegracaoAcessoGrupoEmpresarialVO obj) throws Exception {
        try {
            con.setAutoCommit(false);
            incluirSemCommit(obj);
            con.commit();
        } catch (Exception e) {
            con.rollback();
            con.setAutoCommit(true);
            throw e;
        } finally {
            con.setAutoCommit(true);
        }

    }

    @Override
    public void incluirSemCommit(IntegracaoAcessoGrupoEmpresarialVO obj) throws Exception {
        IntegracaoAcessoGrupoEmpresarialVO.validarDados(obj);
        StringBuilder insert = new StringBuilder();
        insert.append("INSERT INTO integracaoacessogrupoempresarial(descricao, empresalocal, ");
        insert.append(" urlZillyonWeb, chave, empresaremota, localacesso, coletor, nomeempresa, terminal, codigoChaveIntegracaoDigitais)");
        insert.append(" VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)");
        PreparedStatement stm = con.prepareStatement(insert.toString());
        int i = 1;
        stm.setString(i++, obj.getDescricao().toUpperCase());
        stm.setInt(i++, obj.getEmpresaLocal().getCodigo());
        stm.setString(i++, obj.getUrlZillyonWeb());
        stm.setString(i++, obj.getChave());
        stm.setInt(i++, obj.getEmpresaRemota().getCodigo());
        stm.setInt(i++, obj.getLocalAcesso());
        stm.setInt(i++, obj.getColetor());
        stm.setString(i++, obj.getEmpresaRemota().getNome());
        stm.setInt(i++, obj.getTerminal());
        if (obj.getCodigoChaveIntegracaoDigitais() != 0) {
            stm.setInt(i++, obj.getCodigoChaveIntegracaoDigitais());
        } else {
            stm.setNull(i++, 0);
        }
        stm.execute();

        obj.setCodigo(obterValorChavePrimariaCodigo());
        obj.setNovoObj(false);
    }

    @Override
    public void excluir(IntegracaoAcessoGrupoEmpresarialVO obj) throws Exception {
        executarConsulta("DELETE FROM integracaoacessogrupoempresarial WHERE codigo = "+obj.getCodigo(), con);
    }

    @Override
    public String consultarJSON() throws Exception {
        String sql = "SELECT\n" +
                "  ia.codigo, ia.descricao, ia.nomeempresa \n" +
                "FROM integracaoacessogrupoempresarial ia\n" +
                "  ORDER BY descricao";

        PreparedStatement sqlConsultar = con.prepareStatement(sql);
        ResultSet rs = sqlConsultar.executeQuery();

        StringBuilder json = new StringBuilder();
        json.append("{\"aaData\":[");
        boolean dados = false;
        while (rs.next()) {
            dados = true;
            json.append("[\"").append(rs.getString("codigo")).append("\",");
            json.append("\"").append(rs.getString("descricao").trim()).append("\",");
            json.append("\"").append(Uteis.normalizarStringJSON(rs.getString("nomeempresa")).trim()).append("\"],");
        }
        if(dados)
            json.deleteCharAt(json.toString().length()-1);
        json.append("]}");
        return json.toString();
    }

    public IntegracaoAcessoGrupoEmpresarialVO consultarPorCodigo(Integer codigo, Integer nivelMontarDados) throws Exception{
        ResultSet dadosSQL = criarConsulta("SELECT * FROM integracaoacessogrupoempresarial Where codigo = "+codigo, con);
        return dadosSQL.next() ? montarDados(dadosSQL, nivelMontarDados, con) : new IntegracaoAcessoGrupoEmpresarialVO();
    }

    public IntegracaoAcessoGrupoEmpresarialVO consultarPorCodigoChaveIdentificacao(String identificadorOutraEmpresa, Integer nivelMontarDados) throws Exception {
        ResultSet dadosSQL = criarConsulta("SELECT * FROM integracaoacessogrupoempresarial WHERE codigoChaveIntegracaoDigitais = " + identificadorOutraEmpresa, con);
        return dadosSQL.next() ? montarDados(dadosSQL, nivelMontarDados, con) : null;
    }

    public IntegracaoAcessoGrupoEmpresarialVO consultarPorChaveEEmpresa(String chave, Integer codEmpresaRemota, Integer codEmpresaLocal, Integer nivelMontarDados) throws Exception {
        ResultSet dadosSQL = criarConsulta("SELECT * FROM integracaoacessogrupoempresarial " +
                "WHERE chave = '" + chave + "' and empresaremota = " + codEmpresaRemota.toString() + " and empresalocal = " + codEmpresaLocal.toString(), con);
        return dadosSQL.next() ? montarDados(dadosSQL, nivelMontarDados, con) : null;
    }




}

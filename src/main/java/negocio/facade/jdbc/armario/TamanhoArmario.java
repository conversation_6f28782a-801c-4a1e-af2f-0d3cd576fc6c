/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package negocio.facade.jdbc.armario;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.sql.Statement;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import negocio.armario.TamanhoArmarioVO;
import negocio.comuns.utilitarias.ConsistirException;
import negocio.comuns.utilitarias.Ordenacao;
import negocio.facade.jdbc.arquitetura.SuperEntidade;
import negocio.interfaces.armario.TamanhoArmarioInterfaceFacade;

/**
 *
 * <AUTHOR>
 */
public class TamanhoArmario extends SuperEntidade implements TamanhoArmarioInterfaceFacade {

    public TamanhoArmario() throws Exception {
        super();
    }

    public TamanhoArmario(Connection con) throws Exception {
        super(con);
    }

    /**
     * Operação responsável por retornar um novo objeto da classe
     * <code>PerfilAcessoVO</code>.
     */
    @Override
    public TamanhoArmarioVO novo() throws Exception {
        TamanhoArmarioVO obj = new TamanhoArmarioVO();
        return obj;
    }

    @Override
    public void incluir(TamanhoArmarioVO obj) throws Exception {
        try {
            TamanhoArmarioVO.validarDados(obj);
            obj.realizarUpperCaseDados();
            String sql = "INSERT INTO TamanhoArmario(descricao) VALUES (?)";
            try (PreparedStatement sqlInserir = con.prepareStatement(sql)) {
                sqlInserir.setString(1, obj.getDescricao());
                sqlInserir.execute();
            }
            obj.setCodigo(obterValorChavePrimariaCodigo());
            obj.setNovoObj(false);
        } catch (Exception e) {
            throw e;
        }
    }

    @Override
    public void alterar(TamanhoArmarioVO obj) throws Exception {
        try {
            con.setAutoCommit(false);
            TamanhoArmarioVO.validarDados(obj);
            obj.realizarUpperCaseDados();
            String sql = "UPDATE TamanhoArmario set descricao=? WHERE codigo = ?";
            try (PreparedStatement sqlAlterar = con.prepareStatement(sql)) {
                sqlAlterar.setString(1, obj.getDescricao());
                sqlAlterar.setInt(2, obj.getCodigo().intValue());
                sqlAlterar.execute();
            }
            con.commit();
        } catch (Exception e) {
            con.rollback();
            con.setAutoCommit(true);
            throw e;
        } finally {
            con.setAutoCommit(true);
        }
    }

    @Override
    public void excluir(TamanhoArmarioVO obj) throws Exception {
        try {
            con.setAutoCommit(false);
            String sql = "DELETE FROM TamanhoArmario WHERE codigo = ?";
            try (PreparedStatement sqlExcluir = con.prepareStatement(sql)) {
                sqlExcluir.setInt(1, obj.getCodigo().intValue());
                sqlExcluir.execute();
            }
            con.commit();
        } catch (Exception e) {
            con.rollback();
            con.setAutoCommit(true);
            throw e;
        } finally {
            con.setAutoCommit(true);
        }
    }

    @Override
    public TamanhoArmarioVO consultarPorChavePrimaria(Integer codigo) throws Exception {
        String sql = "SELECT * FROM TamanhoArmario WHERE codigo = ?";
        try (PreparedStatement sqlConsultar = con.prepareStatement(sql)) {
            sqlConsultar.setInt(1, codigo);
            try (ResultSet tabelaResultado = sqlConsultar.executeQuery()) {
                if (!tabelaResultado.next()) {
                    throw new ConsistirException("Dados Não Encontrados (Tipo Armário).");
                }
                return (montarDados(tabelaResultado));
            }
        }
    }

    public static TamanhoArmarioVO montarDados(ResultSet dadosSQL) throws Exception {
        TamanhoArmarioVO obj = new TamanhoArmarioVO();
        obj.setCodigo(dadosSQL.getInt("codigo"));
        obj.setDescricao(dadosSQL.getString("descricao"));
        obj.setNovoObj(false);
        return obj;
    }

    @Override
    public String consultarJSON() throws Exception {
        StringBuilder json;
        boolean dados;
        try (ResultSet rs = getPS().executeQuery()) {
            json = new StringBuilder();
            json.append("{\"aaData\":[");
            dados = false;
            while (rs.next()) {
                dados = true;
                json.append("[\"").append(rs.getString("codigo")).append("\",");
                json.append("\"").append(rs.getString("descricao").trim()).append("\"],");
            }
        }
        if (dados) {
            json.deleteCharAt(json.toString().length() - 1);
        }
        json.append("]}");
        return json.toString();
    }

    private PreparedStatement getPS() throws SQLException {
        String sql = "SELECT codigo, descricao FROM TamanhoArmario ORDER BY descricao";
        return con.prepareStatement(sql);
    }

    @Override
    public List consultarParaImpressao(String filtro, String ordem, String campoOrdenacao, int i) throws Exception {
        List lista;
        try (ResultSet rs = getPS().executeQuery()) {
            lista = new ArrayList();
            while (rs.next()) {
                TamanhoArmarioVO cp = new TamanhoArmarioVO();
                String geral = rs.getString("codigo") + rs.getString("descricao");
                if (geral.toLowerCase().contains(filtro.toLowerCase())) {
                    cp.setCodigo(rs.getInt("codigo"));
                    cp.setDescricao(rs.getString("descricao"));
                    lista.add(cp);
                }
            }
        }
        if (campoOrdenacao.equals("Código")) {
            Ordenacao.ordenarLista(lista, "codigo");
        } else if (campoOrdenacao.equals("Descrição")) {
            Ordenacao.ordenarLista(lista, "descricao");
        }
        if (ordem.contains("desc")) {
            Collections.reverse(lista);
        }
        return lista;
    }

    @Override
    public List consultarPorDescricao(String valorConsulta) throws Exception {
        String sqlStr = "SELECT * FROM TamanhoArmario WHERE upper(descricao) like('" + valorConsulta.toUpperCase() + "%') ORDER BY descricao";
        try (Statement stm = con.createStatement()) {
            try (ResultSet tabelaResultado = stm.executeQuery(sqlStr)) {
                return (montarDadosConsulta(tabelaResultado));
            }
        }
    }

    public static List<TamanhoArmarioVO> montarDadosConsulta(ResultSet tabelaResultado) throws Exception {
        List<TamanhoArmarioVO> vetResultado = new ArrayList<TamanhoArmarioVO>();
        while (tabelaResultado.next()) {
            TamanhoArmarioVO obj = montarDados(tabelaResultado);
            vetResultado.add(obj);
        }
        return vetResultado;
    }
}

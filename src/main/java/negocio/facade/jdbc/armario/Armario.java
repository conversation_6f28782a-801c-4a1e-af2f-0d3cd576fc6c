/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package negocio.facade.jdbc.armario;

import negocio.armario.AluguelArmarioVO;
import negocio.armario.ArmarioVO;
import negocio.armario.GrupoArmarioEnum;
import negocio.armario.HistoricoArmarioVO;
import negocio.armario.OperacaoArmarioEnum;
import negocio.armario.StatusArmarioEnum;
import negocio.armario.TamanhoArmarioVO;
import negocio.comuns.arquitetura.LogVO;
import negocio.comuns.arquitetura.UsuarioVO;
import negocio.comuns.contrato.MovProdutoVO;
import negocio.comuns.financeiro.ItemVendaAvulsaVO;
import negocio.comuns.financeiro.VendaAvulsaVO;
import negocio.comuns.utilitarias.Calendario;
import negocio.comuns.utilitarias.ConsistirException;
import negocio.comuns.utilitarias.Ordenacao;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisValidacao;
import negocio.facade.jdbc.arquitetura.SuperEntidade;
import negocio.facade.jdbc.arquitetura.SuperFacadeJDBC;
import negocio.facade.jdbc.utilitarias.Conexao;
import negocio.interfaces.armario.ArmarioInterfaceFacade;
import negocio.interfaces.armario.TamanhoArmarioInterfaceFacade;
import relatorio.controle.basico.ArmarioRelTO;
import relatorio.controle.basico.FiltroArmarioTO;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.sql.Statement;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Date;
import java.util.List;

/**
 *
 * <AUTHOR>
 */
public class Armario extends SuperEntidade implements ArmarioInterfaceFacade {

    public Armario() throws Exception {
        super();
    }

    public Armario(Connection con) throws Exception {
        super(con);
    }

    @Override
    public ArmarioVO novo() throws Exception {
        ArmarioVO obj = new ArmarioVO();
        return obj;
    }

    @Override
    public void gerarArmarios(Integer inicio, Integer fim, TamanhoArmarioVO tamanhoArmario, GrupoArmarioEnum grupo,
            UsuarioVO responsavel, Integer empresa) throws Exception {
        for (int i = inicio; i <= fim; i++) {
            incluir(new ArmarioVO(tamanhoArmario, i, grupo, responsavel, empresa));
        }
    }

    @Override
    public void incluir(ArmarioVO obj) throws Exception {
        try {
            ArmarioVO.validarDados(obj);
            obj.realizarUpperCaseDados();
            String sql = "INSERT INTO armario(descricao, tamanhoarmario, grupo, status, datacadastro, responsavelCadastro, numeracao, empresa) "
                    + " VALUES (?,?,?,?,?,?,?,?)";
            int i = 1;
            try (PreparedStatement sqlInserir = con.prepareStatement(sql)) {
                sqlInserir.setString(i++, obj.getDescricao());
                resolveIntegerNull(sqlInserir, i++, obj.getTamanhoArmario().getCodigo());
                sqlInserir.setString(i++, obj.getGrupo().getCodigo());
                sqlInserir.setInt(i++, StatusArmarioEnum.ABERTO.ordinal());
                sqlInserir.setDate(i++, Uteis.getDataJDBC(Calendario.hoje()));
                sqlInserir.setInt(i++, obj.getResponsavelCadastro().getCodigo());
                sqlInserir.setInt(i++, obj.getNumeracao());
                sqlInserir.setInt(i, obj.getEmpresa().getCodigo());
                sqlInserir.execute();
            }
            obj.setCodigo(obterValorChavePrimariaCodigo());
            obj.setNovoObj(false);
        } catch (Exception e) {
            throw e;
        }
    }



    @Override
    public void alterar(ArmarioVO obj) throws Exception {
        try {
            ArmarioVO.validarDados(obj);
            obj.realizarUpperCaseDados();
            if (verificarDescricaoExistente(obj)) {
                throw new Exception("O nome (" + obj.getDescricao() + ") já existe no grupo (" + obj.getGrupo().getLabel() + ")");
            }
            String sql = "UPDATE armario set descricao = ?, "
                    + "tamanhoarmario = ?, grupo = ?, numeracao = ? "
                    + " WHERE codigo = ?";
            int i = 1;
            try (PreparedStatement sqlAlterar = con.prepareStatement(sql)) {
                sqlAlterar.setString(i++, obj.getDescricao());
                resolveIntegerNull(sqlAlterar, i++, obj.getTamanhoArmario().getCodigo());
                sqlAlterar.setString(i++, obj.getGrupo().getCodigo());
                sqlAlterar.setInt(i++, obj.getNumeracao());

                sqlAlterar.setInt(i, obj.getCodigo());
                sqlAlterar.execute();
            }

        } catch (Exception e) {
            throw e;
        }
    }

    @Override
    public void excluir(ArmarioVO obj) throws Exception {
        try {
            con.setAutoCommit(false);
            String sql = "DELETE FROM armario WHERE codigo = ?";
            try (PreparedStatement sqlExcluir = con.prepareStatement(sql)) {
                sqlExcluir.setInt(1, obj.getCodigo().intValue());
                sqlExcluir.execute();
            }
            con.commit();
        } catch (Exception e) {
            con.rollback();
            con.setAutoCommit(true);
            throw e;
        } finally {
            con.setAutoCommit(true);
        }
    }

    @Override
    public ArmarioVO consultarPorChavePrimaria(Integer codigo) throws Exception {
        String sql = "SELECT * FROM Armario WHERE codigo = ?";
        try (PreparedStatement sqlConsultar = con.prepareStatement(sql)) {
            sqlConsultar.setInt(1, codigo);
            try (ResultSet tabelaResultado = sqlConsultar.executeQuery()) {
                if (!tabelaResultado.next()) {
                    throw new ConsistirException("Dados Não Encontrados (Armário).");
                }
                return (montarDados(tabelaResultado, con));
            }
        }
    }

    @Override
    public Boolean verificarDescricaoExistente(ArmarioVO armario) throws Exception {
        StringBuilder sql = new StringBuilder();
        sql.append("SELECT null FROM Armario \n");
        sql.append("WHERE upper(descricao) = ? \n");
        sql.append("and grupo = ? \n");
        sql.append("and empresa = ? \n");
        sql.append("and status in (" +  StatusArmarioEnum.ABERTO.ordinal() + "," +  StatusArmarioEnum.FECHADO.ordinal() + ") \n");
        sql.append("and codigo != ?");
        int i = 1;
        try (PreparedStatement sqlConsultar = con.prepareStatement(sql.toString())) {
            sqlConsultar.setString(i++, armario.getDescricao().toUpperCase());
            sqlConsultar.setString(i++, armario.getGrupo().getCodigo());
            sqlConsultar.setInt(i++, armario.getEmpresa().getCodigo());
            sqlConsultar.setInt(i++, armario.getCodigo());
            try (ResultSet tabelaResultado = sqlConsultar.executeQuery()) {
                if (!tabelaResultado.next()) {
                    return false;
                }
                return true;
            }
        }
    }

    @Override
    public List<ArmarioVO> consultar(FiltroArmarioTO filtro) throws Exception {
        StringBuilder sql = new StringBuilder("SELECT armario.* FROM Armario");
        sql.append(obterWhereConsulta(filtro));
        sql.append(filtro.getOrdernarDataVigencia() ?
                (filtro.getHabilitadoGestaoArmarios() ? " ORDER BY alu.fimoriginal " : " ORDER BY movprod.datafinalvigencia "  ) : " ORDER BY descricao ");
        if(!UteisValidacao.emptyNumber(filtro.getLimit())) {
            sql.append(" LIMIT ").append(filtro.getLimit()).append(" OFFSET ").append(filtro.getOffset() <= 0 ? 0 : filtro.getOffset());
        }
        try (ResultSet tabelaResultado = criarConsulta(sql.toString(), con)) {
            return montarDadosConsulta(tabelaResultado, con);
        }
    }
    public String obterWhereConsulta(FiltroArmarioTO filtro){
        StringBuilder sql = new StringBuilder();
        sql.append(" LEFT JOIN AluguelArmario  alu ON alu.codigo = armario.aluguelatual\n");
        sql.append(" LEFT JOIN Movproduto movprod ON movprod.codigo = alu.movproduto\n");

        if(!UteisValidacao.emptyString(filtro.getValorConsulta()) && !filtro.getValorConsulta().equals("Pesquisar")){

            sql.append(" LEFT JOIN Cliente cli   ON cli.codigo = alu.cliente\n");
            sql.append(" LEFT JOIN Pessoa pes   ON pes.codigo = cli.pessoa\n");
        }
        sql.append(" WHERE armario.empresa = ").append(filtro.getEmpresa());
        if(!filtro.getFiltroConsulta().isEmpty()) {
            sql.append(" AND ( false");
            for (FiltroArmarioTO filtroItem : filtro.getFiltroConsulta()) {


                sql.append(" OR ( armario.tamanhoarmario = ").append(filtroItem.getTamanhoArmario().getCodigo());
                if(filtroItem.getGrupoSelecionado()!=null)
                sql.append(" AND  armario.grupo = '").append(filtroItem.getGrupoSelecionado().getCodigo()).append("' ");
                if(filtroItem.getStatus()!=null)
                sql.append(" AND armario.status = ").append(filtroItem.getStatus().getId());

                sql.append(")\n");

            }
            sql.append(" )\n");
        }
        if(!UteisValidacao.emptyString(filtro.getValorConsulta()) && !filtro.getValorConsulta().equals("Pesquisar")){
            sql.append(" AND ( UPPER(pes.nome) like '%").append(filtro.getValorConsulta().toUpperCase()).append("%' ");
            sql.append(" OR UPPER(armario.descricao) like '%").append(filtro.getValorConsulta().toUpperCase()).append("%')\n");
        }

       return sql.toString();
    }
    public int obterTotalArmarios(FiltroArmarioTO filtro) throws Exception{

        StringBuilder sql = new StringBuilder();
        sql.append("SELECT COUNT(*) as count FROM Armario");
        sql.append(obterWhereConsulta(filtro));
        try (PreparedStatement pr = con.prepareStatement(sql.toString())) {
            try (ResultSet rs = pr.executeQuery()) {
                if (rs.next()) {
                    return rs.getInt("count");
                }
            }
        }
        return 0;
    }
    public static List<ArmarioVO> montarDadosConsulta(ResultSet tabelaResultado, Connection con) throws Exception {
        List<ArmarioVO> vetResultado = new ArrayList<ArmarioVO>();
        while (tabelaResultado.next()) {
            ArmarioVO obj = montarDados(tabelaResultado, con);
            vetResultado.add(obj);
        }
        return vetResultado;
    }

    public static ArmarioVO montarDados(ResultSet dadosSQL, Connection con) throws Exception {
        ArmarioVO obj = new ArmarioVO();
        obj.setCodigo(dadosSQL.getInt("codigo"));
        obj.setDescricao(dadosSQL.getString("descricao"));
        obj.getResponsavelCadastro().setCodigo(dadosSQL.getInt("responsavelcadastro"));
        obj.setDataCadastro(dadosSQL.getDate("datacadastro"));
        obj.setNumeracao(dadosSQL.getInt("numeracao"));
        obj.getEmpresa().setCodigo(dadosSQL.getInt("empresa"));
        obj.getAluguelAtual().setCodigo(dadosSQL.getInt("aluguelatual"));

        TamanhoArmarioInterfaceFacade tamanhoDao = new TamanhoArmario(con);
        obj.setTamanhoArmario(tamanhoDao.consultarPorChavePrimaria(dadosSQL.getInt("tamanhoarmario")));
        obj.setGrupo(GrupoArmarioEnum.obterPorCodigo(dadosSQL.getString("grupo")));
        obj.setStatus(StatusArmarioEnum.obterPorOrdinal(dadosSQL.getInt("status")));
        obj.setNovoObj(false);
        if (obj.getStatus().equals(StatusArmarioEnum.FECHADO)) {
            preencherAluguel(obj, true, null, con);
        }
        if(obj.getStatus().equals(StatusArmarioEnum.FECHADO)
                && obj.getAluguelAtual() != null
                && obj.getAluguelAtual().getMovProduto().getDataFinalVigencia() != null
                && Calendario.menor(obj.getAluguelAtual().getMovProduto().getDataFinalVigencia(), Calendario.hoje())){

            if(!obterModuloArmario(con))
           getFacade().getArmario().abrirArmario(obj);
        }
        return obj;
    }

    @Override
    public void preencherHistoricoAluguel(ArmarioVO obj) throws Exception {
        preencherAluguel(obj, false, null, con);
    }

    public List<AluguelArmarioVO> obterAluguelPorCliente(int codigoCliente, int empresa, Boolean habilitadoGestao) throws Exception {
        return obterAluguelPorCliente(codigoCliente, empresa, habilitadoGestao, 0);
    }

    public AluguelArmarioVO obterAluguelArmario(Integer codigoAluguelArmario, Boolean habilitadoGestao) throws Exception {
        List<AluguelArmarioVO> lista = obterAluguelPorCliente(0, 0, habilitadoGestao, codigoAluguelArmario);
        return UteisValidacao.emptyList(lista) ? null : lista.get(0);
    }

    private List<AluguelArmarioVO> obterAluguelPorCliente(int codigoCliente, int empresa,
                                                          Boolean habilitadoGestao, Integer codigoAluguelArmario) throws Exception {
        StringBuilder sql = new StringBuilder();
        sql.append(" SELECT arm.numeracao, alu.chavedevolvida, alu.fimoriginal,arm.descricao , alu.contratoassinado,COALESCE(alu.dataInicio,datainiciovigencia) as dataInicio\n");
        sql.append(" ,alu.codigo as CodAlu,arm.codigo as CodArm,alu.datacadastro as dataAluguel,movprod.datainiciovigencia dataInicioVigencia,movprod.datafinalvigencia, alu.cliente as codClienteAlu \n");
        sql.append(" FROM AluguelArmario alu\n");
        sql.append(" INNER JOIN Armario arm ON  arm.codigo = alu.armario\n");
        sql.append(" INNER JOIN Movproduto movprod ON movprod.codigo = alu.movproduto\n");
        sql.append(" WHERE 1 = 1 \n");
        if (!UteisValidacao.emptyNumber(codigoAluguelArmario)) {
            sql.append("AND alu.codigo = ").append(codigoAluguelArmario).append(" \n");
        } else {
            sql.append("AND alu.cliente = ").append(codigoCliente).append(" \n");
            sql.append("AND arm.empresa = ").append(empresa).append(" \n");
        }
        sql.append("order by alu.datacadastro desc \n");
        List<AluguelArmarioVO> resultado;
        try (PreparedStatement stm = con.prepareStatement(sql.toString())) {
            try (ResultSet rs = stm.executeQuery()) {
                resultado = new ArrayList<AluguelArmarioVO>();

                while (rs.next()) {
                    if (!UteisValidacao.emptyNumber(codigoAluguelArmario) ||
                            !rs.getBoolean("chavedevolvida")) {
                        AluguelArmarioVO aluguel = new AluguelArmarioVO();
                        aluguel.setCodigo(rs.getInt("CodAlu"));
                        aluguel.getCliente().setCodigo(rs.getInt("codClienteAlu"));
                        aluguel.getArmario().setNumeracao(rs.getInt("numeracao"));
                        aluguel.setDataCadastro(rs.getDate("dataAluguel"));
                        aluguel.setFimOriginal(habilitadoGestao ? rs.getDate("fimoriginal") : rs.getDate("datafinalvigencia"));
                        aluguel.setDataInicio(habilitadoGestao ? rs.getDate("dataInicio") : rs.getDate("dataInicioVigencia"));
                        aluguel.setContratoAssinado(rs.getBoolean("contratoAssinado"));
                        aluguel.getArmario().setCodigo(rs.getInt("CodArm"));
                        aluguel.getArmario().setDescricao(rs.getString("descricao"));
                        aluguel.setChaveDevolvida(rs.getBoolean("chavedevolvida"));
                        resultado.add(aluguel);
                    }
                }
            }
        }
        return resultado;
    }
    public static void preencherAluguel(ArmarioVO obj, Boolean atual, Integer movProduto, Connection con) throws Exception {
        StringBuilder sql = new StringBuilder("SELECT mp.datainiciovigencia, mp.datafinalvigencia,us.nome as usuarionome, ");
        sql.append(" pes.nome as pessoanome, pes.codigo as pessoacodigo,aa.fimoriginal,aa.dataInicio as inicioAluguel,aa.relacionamentoRenovacao,  ");
        sql.append("p.descricao as produtonome,pes.foto, pes.fotokey, mp.produto, aa.*,p.nrdiasvigencia as nrDiasVigencia \n");
        sql.append("FROM aluguelarmario aa \n");
        sql.append("INNER JOIN movproduto mp ON mp.codigo = aa.movproduto \n");
        sql.append("INNER JOIN produto p ON mp.produto = p.codigo \n");
        sql.append("INNER JOIN pessoa pes ON pes.codigo = mp.pessoa \n");
        sql.append("INNER JOIN usuario us ON us.codigo = aa.responsavelcadastro \n");
        sql.append("WHERE ");
        if (movProduto != null) {
            sql.append(" mp.codigo = ? ");
        } else if (atual) {
            sql.append(" aa.codigo = ? ");
        } else {
            sql.append(" aa.armario = ?");
        }
        sql.append(" ORDER BY aa.datacadastro DESC  ");
        try (PreparedStatement stm = con.prepareStatement(sql.toString())) {
            if (movProduto != null) {
                stm.setInt(1, movProduto);
            } else if (atual) {
                stm.setInt(1, obj.getAluguelAtual().getCodigo());
            } else {
                stm.setInt(1, obj.getCodigo());
            }
            try (ResultSet rs = stm.executeQuery()) {
                while (rs.next()) {
                    AluguelArmarioVO aluguel = new AluguelArmarioVO();
                    if (movProduto != null) {
                        aluguel.getArmario().setCodigo(rs.getInt("armario"));
                    }
                    aluguel.getProduto().setDescricao(rs.getString("produtonome"));
                    aluguel.getProduto().setCodigo(rs.getInt("produto"));
                    aluguel.getProduto().setNrDiasVigencia(rs.getInt("nrDiasVigencia"));
                    aluguel.getCliente().getPessoa().setNome(rs.getString("pessoanome"));
                    aluguel.getRelacionamentoRenovacao().setCodigo(rs.getInt("relacionamentoRenovacao"));
                    aluguel.getCliente().getPessoa().setCodigo(rs.getInt("pessoacodigo"));
                    try {
                        aluguel.getCliente().getPessoa().setFoto(rs.getBytes("foto"));
                    } catch (Exception e) {
                    }
                    aluguel.getCliente().getPessoa().setFotoKey(rs.getString("fotokey"));
                    aluguel.setDataCadastro(rs.getDate("inicioAluguel"));
                    aluguel.getMovProduto().setDataFinalVigencia(rs.getDate("datafinalvigencia"));
                    aluguel.getMovProduto().setDataInicioVigencia(rs.getDate("datainiciovigencia"));
                    aluguel.getMovProduto().setCodigo(rs.getInt("movproduto"));
                    aluguel.getVendaAvulsa().setCodigo(rs.getInt("vendaavulsa"));
                    aluguel.getCliente().setCodigo(rs.getInt("cliente"));
                    aluguel.setValor(rs.getDouble("valor"));
                    aluguel.setCodigo(rs.getInt("codigo"));
                    aluguel.setRenovarAutomatico(rs.getBoolean("renovarAutomatico"));
                    aluguel.getResponsavelCadastro().setCodigo(rs.getInt("responsavelcadastro"));
                    aluguel.getResponsavelCadastro().setNome(rs.getString("usuarionome"));
                    aluguel.setFimOriginal(rs.getDate("fimoriginal"));
                    aluguel.setDataCadastro(rs.getTimestamp("datacadastro"));
                    if (atual) {
                        obj.setAluguelAtual(aluguel);
                    } else {
                        obj.getHistoricoAluguel().add(aluguel);
                    }
                }
            }
        }
    }

    @Override
    public void abrirArmario(ArmarioVO armario) throws Exception {
        SuperFacadeJDBC.executarConsulta("UPDATE armario SET aluguelatual = null, status = " + StatusArmarioEnum.ABERTO.ordinal()
                + " WHERE codigo = " + armario.getCodigo(), con);
        SuperFacadeJDBC.executarConsulta("UPDATE AluguelArmario SET  renovarAutomatico=false  WHERE codigo = " + armario.getAluguelAtual().getCodigo(), con);
    }
    @Override
    public void aluguelRenovado(ArmarioVO armario) throws Exception {
        SuperFacadeJDBC.executarConsulta("UPDATE AluguelArmario SET  renovarAutomatico=false,datarenovacaoautomatica='"+Uteis.getDataJDBC(armario.getAluguelAtual().getDataRenovacaoAutomatica())+"'  WHERE codigo = " + armario.getAluguelAtual().getCodigo(), con);
    }
    public void alterarVigencia(ArmarioVO armario) throws Exception{
        try (PreparedStatement stm = con.prepareStatement("UPDATE movproduto SET datafinalvigencia = ? WHERE codigo = ?")) {
            stm.setDate(1, Uteis.getDataJDBC(Calendario.hoje()));
            stm.setInt(2, armario.getAluguelAtual().getMovProduto().getCodigo());
            stm.execute();
        }
    }

    @Override
    public void inativarArmario(ArmarioVO armario) throws Exception {
        SuperFacadeJDBC.executarConsulta("UPDATE armario SET aluguelatual = null, status = " + StatusArmarioEnum.INATIVO.ordinal()
                + " WHERE codigo = " + armario.getCodigo(), con);
    }

    @Override
    public void fecharArmario(ArmarioVO armario) throws Exception {
        SuperFacadeJDBC.executarConsulta("UPDATE armario SET aluguelatual = " + armario.getAluguelAtual().getCodigo()
                + ", status = " + StatusArmarioEnum.FECHADO.ordinal()
                + " WHERE codigo = " + armario.getCodigo(), con);
    }
    public void gerarHistoricoOperacao(int codigoAluguel,int codigoArmario,String descricao ,OperacaoArmarioEnum operacao,int responsavel) throws Exception{
        StringBuilder sql = new StringBuilder();
        sql.append("INSERT INTO historicoaluguelarmario(descricao,aluguelArmario,operacao,dataoperacao,usuarioresponsavel,armario) VALUES (?,?,?,?,?,?)");
        try (PreparedStatement stm = con.prepareStatement(sql.toString())) {
            int i = 1;
            stm.setString(i++, descricao);
            stm.setInt(i++, codigoAluguel);
            stm.setInt(i++, operacao.getCodigo());
            stm.setTimestamp(i++, Uteis.getDataJDBCTimestamp(Calendario.hoje()));
            stm.setInt(i++, responsavel);
            stm.setInt(i++, codigoArmario);
            stm.execute();
        }
    }
    public void gravarContratoAssinadoArmario(AluguelArmarioVO aluguel)throws  Exception{
        SuperFacadeJDBC.executarConsulta("UPDATE aluguelArmario SET contratoAssinado = " + (aluguel.getContratoAssinado() ? "true": "false")
                + " WHERE codigo = " + aluguel.getCodigo(), con);
    }
    @Override
    public void trocarArmarioAluguel(ArmarioVO armario) throws Exception {
        SuperFacadeJDBC.executarConsulta("UPDATE aluguelarmario SET armario = " + armario.getCodigo()
                + ",renovarAutomatico = true WHERE codigo = " + armario.getAluguelAtual().getCodigo(), con);
    }
    @Override
    public VendaAvulsaVO alugarArmario(ArmarioVO armario, AluguelArmarioVO aluguel, UsuarioVO responsavel, Date inicioAluguel,Date fimAluguel,
                                       Integer nrVezes, Date vencimentoPrimeiraParcela) throws Exception{
        try {
            con.setAutoCommit(false);
             VendaAvulsaVO venda = montarVenda(aluguel, responsavel, inicioAluguel, fimAluguel, nrVezes, vencimentoPrimeiraParcela);
            Integer idVenda = getFacade().getVendaAvulsa().incluirSemCommit(venda, false, Calendario.hoje());
             venda.setCodigo(idVenda);
            aluguel.setArmario(armario);
            aluguel.setVendaAvulsa(venda);
            aluguel.setMovProduto(getFacade().getMovProduto().consultarPorVendaAvulsa(idVenda));
            if (aluguel.getMovProduto() == null) {
                throw new Exception("Venda não foi realizada com sucesso.");
            }
            gravarAluguel(aluguel);
            armario.setAluguelAtual(aluguel);
            fecharArmario(armario);
            con.commit();
            return venda;
        } catch (Exception e) {
            con.rollback();
            con.setAutoCommit(true);
            throw e;
        } finally {
            con.setAutoCommit(true);
        }
    }

    @Override
    public List<AluguelArmarioVO> consultarAluguelPorDataFim(Date dataFim) throws Exception{

        StringBuilder sql = new StringBuilder();
        sql.append(" Select alu.*,movprod.produto as produto,movprod.datafinalvigencia finalvigencia,alu.datainicio as inicioAluguel from Armario arm\n");
        sql.append(" INNER JOIN AluguelArmario alu ON alu.codigo = arm.aluguelatual\n");
        sql.append(" INNER JOIN MovProduto movprod on movprod.codigo = alu.movproduto\n");
        sql.append(" WHERE\n");
        sql.append(" (movprod.datafinalvigencia <= '").append(Uteis.getDataJDBC(dataFim)).append("'\n");
        sql.append(" OR alu.fimoriginal <= '").append(Uteis.getDataJDBC(dataFim)).append("')\n");
        sql.append(" AND alu.renovarAutomatico = true AND alu.dataRenovacaoAutomatica is null\n");
        sql.append(" AND arm.status = 1 ");
        List<AluguelArmarioVO> resultado;
        try (PreparedStatement stm = con.prepareStatement(sql.toString())) {
            try (ResultSet rs = stm.executeQuery()) {
                resultado = new ArrayList<AluguelArmarioVO>();

                while (rs.next()) {
                    AluguelArmarioVO aluguel = new AluguelArmarioVO();
                    aluguel.setCodigo(rs.getInt("codigo"));
                    aluguel.setFimOriginal(rs.getDate("fimoriginal") == null ? rs.getDate("datafinalvigencia") : rs.getDate("fimoriginal"));
                    aluguel.setValor(rs.getDouble("valor"));
                    aluguel.setProduto(getFacade().getProduto().consultarPorChavePrimaria(rs.getInt("produto"), Uteis.NIVELMONTARDADOS_DADOSBASICOS));
                    aluguel.setDataCadastro(rs.getDate("datacadastro"));
                    aluguel.setArmario(new ArmarioVO());
                    aluguel.setDataInicio(rs.getDate("inicioAluguel") == null ? rs.getDate("dataCadastro") : rs.getDate("inicioAluguel"));
                    aluguel.setContratoAssinado(rs.getBoolean("contratoAssinado"));
                    aluguel.setRenovarAutomatico(rs.getBoolean("renovarAutomatico"));
                    aluguel.setDataRenovacaoAutomatica(rs.getDate("dataRenovacaoAutomatica"));
                    aluguel.getArmario().setCodigo(rs.getInt("armario"));
                    aluguel.setMovProduto(new MovProdutoVO());
                    aluguel.getMovProduto().setCodigo(rs.getInt("movproduto"));
                    aluguel.setVendaAvulsa(getFacade().getVendaAvulsa().consultarPorChavePrimaria(rs.getInt("vendaavulsa"), Uteis.NIVELMONTARDADOS_DADOSENTIDADESUBORDINADAS));
                    aluguel.setCliente(getFacade().getCliente().consultarPorChavePrimaria(rs.getInt("cliente"), Uteis.NIVELMONTARDADOS_DADOSBASICOS));
                    aluguel.getResponsavelCadastro().setCodigo(rs.getInt("responsavelCadastro"));
                    resultado.add(aluguel);
                }
            }
        }
        return resultado;
    }
    @Override
    public VendaAvulsaVO alugarArmarioProrata(ArmarioVO armario, AluguelArmarioVO aluguel,VendaAvulsaVO venda,
                                       Date inicioAluguel, Date fimAluguel, Integer nrVezes, Date vencimentoPrimeiraParcela,Date dia) throws Exception {
        try {
            con.setAutoCommit(false);
            Integer idVenda = getFacade().getVendaAvulsa().incluirVendaAvulsaArmario(venda, false, dia);
            venda.setCodigo(idVenda);
            aluguel.setArmario(armario);
            aluguel.setVendaAvulsa(venda);
            aluguel.setMovProduto(getFacade().getMovProduto().consultarPorVendaAvulsa(idVenda));
            if (aluguel.getMovProduto() == null) {
                throw new Exception("Venda não foi realizada com sucesso.");
            }
            gravarAluguel(aluguel);
            armario.setAluguelAtual(aluguel);
            fecharArmario(armario);
            con.commit();
            return venda;
        } catch (Exception e) {
            con.rollback();
            con.setAutoCommit(true);
            throw e;
        } finally {
            con.setAutoCommit(true);
        }
    }

    @Override
    public  List<ArmarioRelTO> consultarArmariosRel(FiltroArmarioTO filtro) throws Exception{
        List<ArmarioRelTO> resultado = new ArrayList<ArmarioRelTO>();
        StringBuilder sql = new StringBuilder();
            sql.append(" Select pes.nome,cli.matricula,alu.datacadastro lancamento,alu.datainicio as datainicio,arm.descricao as numeroArmario,arm.grupo as tipo,tam.descricao as tamanho,");
            sql.append(" prod.descricao as planoLocacao,prod.nrdiasvigencia as vigencia,alu.fimoriginal,movprod.datafinalvigencia,alu.contratoAssinado, ");
            sql.append(" con.vigenciade, con.vigenciaateajustada, cli.situacao as situacaocliente, pla.descricao as planocontrato ");
            sql.append(" from AluguelArmario alu\n");
            sql.append(" INNER JOIN Armario arm ON arm.codigo = alu.armario\n");
            sql.append(" INNER JOIN cliente cli ON  cli.codigo = alu.cliente\n");
            sql.append(" INNER JOIN Pessoa pes ON pes.codigo = cli.pessoa\n");
            sql.append(" INNER JOIN Movproduto movprod ON movprod.codigo = alu.movproduto\n");
            sql.append(" INNER JOIN Produto prod ON prod.codigo = movprod.produto\n");
            sql.append(" INNER JOIN TamanhoArmario tam ON tam.codigo = arm.tamanhoarmario\n");
            sql.append(" LEFT JOIN contrato con ON con.codigo = (select codigocontrato from situacaoclientesinteticodw where codigopessoa = cli.pessoa)\n");
            sql.append(" LEFT JOIN plano pla on pla.codigo = con.plano\n");
            if(filtro.getSomenteParcelasAtrasadas())
            sql.append(" INNER JOIN MovParcela parcela ON parcela.vendaavulsa = alu.vendaavulsa\n");
            sql.append(" WHERE 1=1");
        if(filtro.getPeriodoLocacaoDe()!=null && filtro.getPeriodoLocacaoAte()!=null && filtro.getHabilitadoGestaoArmarios()) {
            sql.append(" AND (alu.datainicio::DATE between '").append(Uteis.getData(filtro.getPeriodoLocacaoDe())).append("' and '").append(Uteis.getData(filtro.getPeriodoLocacaoAte())).append("') \n");
        }
        if(filtro.getPeriodoLocacaoDe()!=null && filtro.getPeriodoLocacaoAte()!=null && !filtro.getHabilitadoGestaoArmarios()) {
            sql.append(" AND (alu.datacadastro::DATE between '").append(Uteis.getData(filtro.getPeriodoLocacaoDe())).append("' and '").append(Uteis.getData(filtro.getPeriodoLocacaoAte())).append("') \n");
        }
        if(filtro.getPeriodoRenovacaoDe()!=null && filtro.getPeriodoRenovacaoAte()!=null && filtro.getHabilitadoGestaoArmarios()) {
            sql.append(" AND (alu.datacadastro::DATE between '").append(Uteis.getData(filtro.getPeriodoRenovacaoDe())).append("' and '").append(Uteis.getData(filtro.getPeriodoRenovacaoAte())).append("') \n");
            sql.append(" or alu.relacionamentorenovacao != 0\n");
        }
        if(filtro.getPeriodoVencimentoDe()!=null && filtro.getPeriodoVencimentoAte()!=null ) {
            sql.append(" AND (movprod.datafinalvigencia::DATE between '").append(Uteis.getData(filtro.getPeriodoVencimentoDe())).append("' and '").append(Uteis.getData(filtro.getPeriodoVencimentoAte())).append("')\n");
        }
        if(!UteisValidacao.emptyNumber(filtro.getPlanoLocacao()) && filtro.getPlanoLocacao() > 0){
            sql.append(" AND prod.codigo = ").append(filtro.getPlanoLocacao()).append("\n");
        }
        if(!UteisValidacao.emptyString(filtro.getTipoArmarioSelecionado()) && !filtro.getTipoArmarioSelecionado().equals("A")){
            sql.append(" AND arm.grupo = '").append(filtro.getTipoArmarioSelecionado()).append("' \n");
        }
        if(!UteisValidacao.emptyNumber(filtro.getTamanhoArmarioSelecionado()) && filtro.getTamanhoArmarioSelecionado() > 0){
            sql.append(" AND arm.tamanhoarmario = ").append(filtro.getTamanhoArmarioSelecionado()).append("\n");
        }
        if(!UteisValidacao.emptyString(filtro.getNumeroArmario())){
            sql.append(" AND arm.descricao = '").append(filtro.getNumeroArmario()).append("'\n");
        }
        if(!UteisValidacao.emptyString(filtro.getContratoAssinado())){
            sql.append(" AND alu.contratoassinado = ").append(filtro.getContratoAssinado()).append("\n");
        }
        if(filtro.getSomenteParcelasAtrasadas()){
         sql.append(" AND (parcela.situacao = 'EA' AND parcela.datavencimento::DATE > '").append(Uteis.getData(Calendario.hoje())).append("')\n");
        }
        try (Statement stm = con.createStatement()) {
            try (ResultSet rs = stm.executeQuery(sql.toString())) {
                while (rs.next()) {
                    ArmarioRelTO armario = new ArmarioRelTO();
                    armario.setNome(rs.getString("nome"));
                    armario.setDataInicio(filtro.getHabilitadoGestaoArmarios() ? rs.getDate("datainicio") : rs.getDate("lancamento"));
                    armario.setMatricula(rs.getString("matricula"));
                    armario.setNumeroArmario(rs.getString("numeroArmario"));
                    armario.setTamanho(rs.getString("tamanho"));
                    armario.setPlanoLocacao(rs.getString("planoLocacao"));
                    armario.setTipo(rs.getString("tipo"));
                    armario.setContratoAssinado(rs.getBoolean("contratoAssinado") + "");
                    armario.setDataLancamento(rs.getDate("lancamento"));
                    armario.setDataFim(filtro.getHabilitadoGestaoArmarios() ? rs.getDate("fimoriginal") : rs.getDate("datafinalvigencia"));
                    armario.setVigencia(rs.getString("vigencia"));

                    String situacaocliente = rs.getString("situacaocliente");
                    if (situacaocliente.equalsIgnoreCase("AT")) {
                        armario.setSituacaoAluno("Ativo");
                    } else if (situacaocliente.equalsIgnoreCase("IN")) {
                        armario.setSituacaoAluno("Inativo");
                    } else if (situacaocliente.equalsIgnoreCase("VI")) {
                        armario.setSituacaoAluno("Visitante");
                    } else if (situacaocliente.equalsIgnoreCase("TR")) {
                        armario.setSituacaoAluno("Trancado");
                    }

                    armario.setPlanoContrato(rs.getString("planocontrato"));
                    armario.setDataInicioContrato(rs.getDate("vigenciade"));
                    armario.setDataFinalContrato(rs.getDate("vigenciaateajustada"));
                    resultado.add(armario);
                }
            }
        }
        return resultado;
    }

    @Override
    public void alterarAluguelArmario(AluguelArmarioVO aluguel, boolean abrir) throws Exception {
        try {
            con.setAutoCommit(false);
            try (PreparedStatement stm = con.prepareStatement("UPDATE movproduto SET datafinalvigencia = ? WHERE codigo = ?;")) {
                stm.setDate(1, Uteis.getDataJDBC(abrir ? Calendario.hoje() : aluguel.getMovProduto().getDataFinalVigencia()));
                stm.setInt(2, aluguel.getMovProduto().getCodigo());
                if (abrir) {
                    abrirArmario(aluguel.getArmario());
                }
                stm.execute();
            }
            con.commit();
        } catch (Exception e) {
            con.rollback();
            con.setAutoCommit(true);
            throw e;
        } finally {
            con.setAutoCommit(true);
        }
    }
    public static Boolean obterModuloArmario(Connection con)throws Exception{
        StringBuilder sql = new StringBuilder();
        sql.append("SELECT habilitarGestaoArmarios FROM ConfiguracaoSistema\n");
        sql.append(" where codigo = 1");
        try (ResultSet rs = criarConsulta(sql.toString(), con)) {
            while (rs.next()) {
                return rs.getBoolean("habilitarGestaoArmarios");
            }
        }
        return false;
    }
    @Override
    public void estornarAluguel(Integer codigoMovProduto, LogVO log) throws Exception {

        if (codigoMovProduto != null ) {
            ArmarioVO armario = new ArmarioVO();

            preencherAluguel(armario, true, codigoMovProduto, con);
            if (armario.getAluguelAtual() != null && !UteisValidacao.emptyNumber(armario.getAluguelAtual().getCodigo())) {
                ArmarioVO armarioVO = consultarPorChavePrimaria(armario.getAluguelAtual().getArmario().getCodigo());
                
                log.setValorCampoAlterado(log.getValorCampoAlterado() + "--------------------------------------\n\r");
                log.setValorCampoAlterado(log.getValorCampoAlterado() + " \n\rAluguelArmario ID = " + armario.getAluguelAtual().getCodigo()
                        + "\n\r" + "NomePessoa = " + armario.getAluguelAtual().getCliente().getPessoa().getNome()
                        + "\n\r" + "Valor = R$ " + armario.getAluguelAtual().getValor()
                        + "\n\r" + "DataInicio = " + armario.getAluguelAtual().getInicioApresentar()
                        + "\n\r" + "DataFim = " + armario.getAluguelAtual().getFimApresentar() + "\n\r");
                //retirar vinculos de movpagamento e recibo

                try (ResultSet rs = criarConsulta("select max(codigo) as maxcodigo from aluguelarmario where armario = " + armarioVO.getCodigo(), con)) {


                    executarConsulta("DELETE FROM historicoaluguelarmario WHERE aluguelarmario = " + armario.getAluguelAtual().getCodigo(), con);
                    executarConsulta("UPDATE aluguelarmario SET relacionamentoRenovacao = 0 WHERE relacionamentoRenovacao = " + armario.getAluguelAtual().getCodigo(), con);
                    executarConsulta("DELETE FROM aluguelarmario WHERE codigo = " + armario.getAluguelAtual().getCodigo(), con);
                    if (rs.next() && rs.getInt("maxcodigo") == armario.getAluguelAtual().getCodigo()
                            && armarioVO.getStatus().equals(StatusArmarioEnum.FECHADO)) {
                        abrirArmario(armarioVO);
                    }
                }
            }
        }
    }

    public void gravarAluguel(AluguelArmarioVO aluguel) throws Exception {
        StringBuilder insert = new StringBuilder("INSERT INTO aluguelarmario (cliente, armario, movproduto, \n");
        insert.append(" vendaavulsa, valor, responsavelcadastro, datacadastro, fimoriginal,contratoAssinado,renovarAutomatico,datainicio,relacionamentoRenovacao) VALUES (?,?,?,?,?,?,?,?,?,?,?,?)");
        try (PreparedStatement stm = con.prepareStatement(insert.toString())) {
            int i = 1;
            stm.setInt(i++, aluguel.getCliente().getCodigo());
            stm.setInt(i++, aluguel.getArmario().getCodigo());
            stm.setInt(i++, aluguel.getMovProduto().getCodigo());
            stm.setInt(i++, aluguel.getVendaAvulsa().getCodigo());
            stm.setDouble(i++, aluguel.getValor());
            stm.setInt(i++, aluguel.getResponsavelCadastro().getCodigo());
            stm.setTimestamp(i++, Uteis.getDataJDBCTimestamp(Calendario.hoje()));
            stm.setDate(i++, Uteis.getDataJDBC(aluguel.getFimOriginal()) == null ? Uteis.getDataJDBC(aluguel.getVendaAvulsa().getItemVendaAvulsaVOs().get(0).getDataValidade()) : Uteis.getDataJDBC(aluguel.getFimOriginal()));
            stm.setBoolean(i++, aluguel.getContratoAssinado() == null ? false : aluguel.getContratoAssinado());
            stm.setBoolean(i++, true);
            stm.setDate(i++, Uteis.getDataJDBC(aluguel.getDataInicio()) == null ? Uteis.getDataJDBC(Calendario.hoje()) : Uteis.getDataJDBC(aluguel.getDataInicio()));
            int codigoRenovacao = aluguel.getRelacionamentoRenovacao().getCodigo();
            stm.setInt(i++, UteisValidacao.emptyNumber(codigoRenovacao) ? 0 : codigoRenovacao);
            stm.execute();
        }
        aluguel.setCodigo(Conexao.obterUltimoCodigoGeradoTabela(con, "aluguelarmario"));
    }

    private VendaAvulsaVO montarVenda(AluguelArmarioVO aluguel,
            UsuarioVO responsavel, Date inicio, Date fim,
            Integer nrVezes, Date vencimentoPrimeiraParcela) throws Exception {
        VendaAvulsaVO venda = new VendaAvulsaVO();
        venda.setTipoComprador("CI");
        if (UteisValidacao.emptyNumber(aluguel.getCliente().getCodigo())) {
            throw new Exception("Cliente não encontrado.");
        }
        venda.setCliente(aluguel.getCliente());
        Date data = Calendario.hoje();
        venda.setDataRegistro(data == null ? Calendario.hoje() : data);
        venda.setNomeComprador(aluguel.getCliente().getPessoa().getNome());
        venda.setEmpresa(aluguel.getCliente().getEmpresa());
        venda.setResponsavel(responsavel);
        venda.setNrVezesParcelamento(nrVezes);
        venda.setVencimentoPrimeiraParcela(vencimentoPrimeiraParcela);
        if(Calendario.menor(inicio, Calendario.hoje())){
            venda.setDataRegistro(inicio);
        }
        ItemVendaAvulsaVO item = new ItemVendaAvulsaVO();
        if (UteisValidacao.emptyNumber(aluguel.getProduto().getCodigo())) {
            throw new Exception("Nenhum produto do tipo 'Armário' cadastrado.");
        } else {
            item.setProduto(getFacade().getProduto().consultarPorChavePrimaria(aluguel.getProduto().getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS));
            item.setQuantidade(1);
            item.setValorParcial(aluguel.getValor());
            item.setDataVenda(inicio);
            item.setDataValidade(fim);
            venda.getItemVendaAvulsaVOs().add(item);
        }
        return venda;
    }
    
    public Date verificarAlunoTemArmarioEmAberto(Integer codigoCliente) throws Exception {
        StringBuilder sql = new StringBuilder();
        sql.append("SELECT mp.datafinalvigencia FROM aluguelarmario aa\n");
        sql.append("INNER JOIN movproduto mp ON mp.codigo = aa.movproduto\n");
        sql.append("INNER JOIN armario a ON a.codigo = aa.armario\n");
        sql.append("WHERE mp.datafinalvigencia >= ? \n");
        sql.append("and aa.cliente = ? and a.status = ").append(StatusArmarioEnum.FECHADO.getId());
        try (PreparedStatement stm = con.prepareStatement(sql.toString())) {
            stm.setDate(1, Uteis.getDataJDBC(Calendario.hoje()));
            stm.setInt(2, codigoCliente);
            try (ResultSet rs = stm.executeQuery()) {
                return rs.next() ? rs.getDate("datafinalvigencia") : null;
            }
        }

    }
    public void estornoArmario(int codigoVenda) throws Exception{

        StringBuilder sql = new StringBuilder();
        sql.append("SELECT codigo FROM AluguelArmario\n");
        sql.append("WHERE\n");
        sql.append(" vendaavulsa = ").append(codigoVenda);
        int codigoAluguel;
        try (PreparedStatement stm = con.prepareStatement(sql.toString())) {
            try (ResultSet rs = stm.executeQuery()) {
                codigoAluguel = rs.next() ? rs.getInt("codigo") : 0;
            }
        }
        if(!UteisValidacao.emptyNumber(codigoAluguel)){
            sql = new StringBuilder();
            sql.append("DELETE FROM historicoaluguelarmario WHERE aluguelarmario = ").append(codigoAluguel);
            sql.append("UPDATE armario SET status = 0 ,aluguelatual = null WHERE aluguelatual = ").append(codigoAluguel);
            sql.append("UPDATE aluguelarmario SET relacionamentoRenovacao = 0 WHERE relacionamentoRenovacao = ").append(codigoAluguel);
            sql.append("DELETE FROM aluguelarmario WHERE codigo = ").append(codigoAluguel);
        }

    }
    public String consultarJSON(String codArmario) throws Exception {
        StringBuilder json;
        boolean dados;
        try (ResultSet rs = getPS(codArmario).executeQuery()) {
            json = new StringBuilder();
            json.append("{\"aaData\":[");
            dados = false;
            while (rs.next()) {
                dados = true;
                Date datacadastro = rs.getTimestamp("datacadastro");
                Date inicio = rs.getTimestamp("inicio");
                Date fim = rs.getTimestamp("fim");
                Date fimOriginal = rs.getTimestamp("fimoriginal");
                String descricao = rs.getString("descHistorico");
                OperacaoArmarioEnum operacao = OperacaoArmarioEnum.obterPorCodigo(rs.getInt("operacao"));
                json.append("[\"").append(UteisValidacao.emptyString(descricao) ? operacao.getDescricao() : descricao).append("\",");
                json.append("\"").append(rs.getString("nomepessoa")).append("\",");
                json.append("\"").append(rs.getString("nomeproduto").trim().replaceAll("\"", "\'")).append("\",");
                if (inicio != null) {
                    json.append("\"").append(Uteis.getDataAplicandoFormatacao(inicio, "dd/MM/yyyy")).append("\",");
                } else {
                    json.append("\"").append("").append("\",");
                }
                if (fimOriginal != null) {
                    json.append("\"").append(Uteis.getDataAplicandoFormatacao(fimOriginal, "dd/MM/yyyy")).append("\",");
                } else {
                    json.append("\"").append("").append("\",");
                }
                if (fim != null) {
                    json.append("\"").append(Uteis.getDataAplicandoFormatacao(fim, "dd/MM/yyyy")).append("\",");
                } else {
                    json.append("\"").append("").append("\",");
                }
                json.append("\"").append(rs.getString("nomeresponsavel").trim()).append("\",");
                if (datacadastro != null) {
                    json.append("\"").append(Uteis.getDataAplicandoFormatacao(datacadastro, "dd/MM/yyyy")).append("\"],");
                } else {
                    json.append("\"").append("").append("\",");
                }

            }
        }
        if (dados) {
            json.deleteCharAt(json.toString().length() - 1);
        }
        json.append("]}");
        return json.toString();
    }

    public int obterSumPorGrupo(String grupo, int empresa) throws Exception {
        String sql = " SELECT COUNT(*) as sum FROM ARMARIO\n" +
                " where grupo = '" + grupo + "'\n" +
                " and empresa = " + empresa;
        try (PreparedStatement sqlConsultar = con.prepareStatement(sql);
             ResultSet rs = sqlConsultar.executeQuery()) {
            if (rs.next()) {
                return rs.getInt("sum");
            }

        }
        return 0;
    }

    public int obterSumPorStatus(int status, int empresa) throws Exception {
        String sql = " SELECT COUNT(*) as sum FROM ARMARIO\n" +
                " where status = " + status + "\n" +
                " and empresa = " + empresa;
        try (PreparedStatement sqlConsultar = con.prepareStatement(sql);
             ResultSet rs = sqlConsultar.executeQuery()) {
            if (rs.next()) {
                return rs.getInt("sum");
            }

        }
        return 0;
    }

    public int obterSumPorTamanho(int status, int codEmpresa) throws Exception {
        String sql = " SELECT COUNT(*) as sum FROM ARMARIO\n" +
                " where tamanhoarmario = " + status + "\n" +
                " AND empresa = " + codEmpresa;

        try (PreparedStatement sqlConsultar = con.prepareStatement(sql);
             ResultSet rs = sqlConsultar.executeQuery()) {
            if (rs.next()) {
                return rs.getInt("sum");
            }
        }

        return 0;
    }

    private PreparedStatement getPS(String codArmario) throws SQLException {
        StringBuilder sql = new StringBuilder();
        sql.append("SELECT sw.nomecliente as nomepessoa, pr.descricao as nomeproduto, mp.datainiciovigencia as inicio, \n");
        sql.append("mp.datafinalvigencia as fim, aa.fimoriginal,u.nome as nomeresponsavel,coalesce(historico.dataoperacao,aa.datacadastro) as dataOrdenar, COALESCE(historico.dataOperacao,aa.datacadastro) as datacadastro,");
        sql.append("COALESCE(historico.operacao,0) as operacao,\n");
        sql.append("historico.descricao as descHistorico,historico.dataOperacao \n");
        sql.append("FROM aluguelarmario aa  \n");
        sql.append("LEFT JOIN historicoaluguelarmario historico ON historico.aluguelArmario = aa.codigo\n");
        sql.append("INNER JOIN armario a ON a.codigo = aa.armario \n");
        sql.append("INNER JOIN movproduto mp ON mp.codigo = aa.movproduto \n");
        sql.append("INNER JOIN usuario u ON u.codigo = COALESCE(historico.usuarioresponsavel,aa.responsavelcadastro) \n");
        sql.append("INNER JOIN situacaoclientesinteticodw sw ON sw.codigocliente = aa.cliente \n");
        sql.append("INNER JOIN produto pr ON pr.codigo = mp.produto \n");
        sql.append("WHERE a.codigo = ").append(codArmario).append(" OR historico.armario = ").append(codArmario).append("\n");
        sql.append("ORDER BY dataOrdenar DESC \n");
        
        return con.prepareStatement(sql.toString());
    }

    
    public List consultarParaImpressao(String filtro, String ordem, String campoOrdenacao, int i, String codArmario) throws SQLException {
        List lista;
        try (ResultSet rs = getPS(codArmario).executeQuery()) {
            lista = new ArrayList();
            while (rs.next()) {
                AluguelArmarioVO aluguel = new AluguelArmarioVO();
                String geral = rs.getString("descHistorico")
                        + rs.getString("nomepessoa")
                        + rs.getString("nomeproduto")
                        + rs.getString("inicio")
                        + rs.getString("fim")
                        + rs.getString("fimoriginal")
                        + rs.getString("nomeresponsavel")
                        + rs.getString("datacadastro");
                if (geral.toLowerCase().contains(filtro.toLowerCase())) {
                    aluguel.getCliente().getPessoa().setNome(rs.getString("nomepessoa"));
                    aluguel.getMovProduto().getProduto().setDescricao(rs.getString("nomeproduto"));
                    aluguel.getMovProduto().setDataFinalVigencia(rs.getDate("fim"));
                    aluguel.getMovProduto().setDataInicioVigencia(rs.getDate("inicio"));
                    aluguel.setFimOriginal(rs.getDate("fimoriginal"));
                    aluguel.getResponsavelCadastro().setNome(rs.getString("nomeresponsavel"));
                    aluguel.setDataCadastro(rs.getDate("datacadastro"));
                    HistoricoArmarioVO historicoArmarioVO = new HistoricoArmarioVO();
                    historicoArmarioVO.setAluguel(aluguel);
                    historicoArmarioVO.setReponsavel(aluguel.getResponsavelCadastro());
                    historicoArmarioVO.setDataOperacao(aluguel.getDataCadastro());
                    historicoArmarioVO.setDescricao(rs.getString("descHistorico"));
                    historicoArmarioVO.setOperacao(OperacaoArmarioEnum.obterPorCodigo(rs.getInt("operacao")));
                    lista.add(historicoArmarioVO);
                }
            }
        }
        if (campoOrdenacao.equals("Nome")) {
            Ordenacao.ordenarLista(lista, "nomePessoa");
        } else if (campoOrdenacao.equals("Produto")) {
            Ordenacao.ordenarLista(lista, "nomeProduto");
        } else if (campoOrdenacao.equals("Dt. Início")) {
            Ordenacao.ordenarLista(lista, "inicio");
        } else if (campoOrdenacao.equals("Dt. Fim")) {
            Ordenacao.ordenarLista(lista, "fimOriginal");
        } else if (campoOrdenacao.equals("Dt. Fim Ajustada")) {
            Ordenacao.ordenarLista(lista, "fim");
        } else if (campoOrdenacao.equals("Responsável")) {
            Ordenacao.ordenarLista(lista, "nomeresponsavel");
        } else if (campoOrdenacao.equals("Data cadastro")) {
            Ordenacao.ordenarLista(lista, "dataCadastro");
        }
        if (ordem.contains("desc")) {
            Collections.reverse(lista);
        }
        return lista;

    }

}

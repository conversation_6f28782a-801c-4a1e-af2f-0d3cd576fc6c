package negocio.facade.jdbc.armario;

import negocio.armario.AluguelArmarioVO;
import negocio.comuns.arquitetura.SuperTO;
import negocio.comuns.arquitetura.UsuarioVO;
import negocio.comuns.basico.ConfiguracaoSistemaVO;
import negocio.comuns.financeiro.ItemVendaAvulsaVO;
import negocio.comuns.financeiro.MovParcelaVO;
import negocio.comuns.financeiro.VendaAvulsaVO;
import negocio.comuns.plano.ProdutoVO;
import negocio.comuns.utilitarias.Calendario;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisValidacao;

import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;

/**
 * Created by Rafael on 20/10/2015.
 */
public class ProrataArmario extends SuperTO {
    private static final long serialVersionUID = 1815778015949828877L;
    private int qtdeDiasTolerancia;
    private int diaReferenciaProrata;
    private VendaAvulsaVO venda;
    private int diaAplicarProrata;
    private Date dataInicio;
    private int qtdeDiasCalculado;
    private Date hoje;
    private Date vencimentoParcela;
    private int numeroParcelas;
    ConfiguracaoSistemaVO configuracaoSistemaVO;
    private int nrDiasUsar = 0;

    public void verificarConfiguracoesSistema() throws Exception {
        diaAplicarProrata =configuracaoSistemaVO.getDiaProrataArmario();

        if(diaAplicarProrata == 0) throw new Exception("Sistema não está configurado para usar o Pró-rata.");
    }
    private int verificaProrata() throws Exception{
        int dia = Uteis.getDiaMesData(dataInicio);
        int mes = Uteis.getMesData(dataInicio);
        int ano = Uteis.getAnoData(dataInicio);
        if(diaReferenciaProrata >= dia) mes--;
        Calendar aux = Calendario.getInstance();
        aux.set(ano, mes, diaReferenciaProrata);
        return (int)Uteis.nrDiasEntreDatas(dataInicio, aux.getTime())+1;
    }
    public Double obterValorParcelaSemProporcional(AluguelArmarioVO aluguel){
        return (aluguel.getValor());
    }
    public Double obterValorParcela(AluguelArmarioVO aluguel){
            return ((aluguel.getValor()/30 *nrDiasUsar) /venda.getNrVezesParcelamento());
    }
    public void processarParcelas(AluguelArmarioVO aluguel) throws Exception {

        nrDiasUsar = verificaProrata();
        boolean gerarMesProporcional = false;
        int diasVigencia = aluguel.getProduto().getNrDiasVigencia();
        if (aluguel.getDataInicio() != null && aluguel.getFimOriginal() != null) {
            if (Calendario.diferencaEmDias(aluguel.getDataInicio(), aluguel.getFimOriginal()) != (diasVigencia - 1)) {
                gerarMesProporcional = (Uteis.getDiaMesData(dataInicio) > 30 ? 0 : Uteis.getDiaMesData(dataInicio)) > diaAplicarProrata;
            }
        }

        venda.setMovParcelaVOs(new ArrayList<MovParcelaVO>());
        if(gerarMesProporcional){
            if(diasVigencia > 31){
                MovParcelaVO proRata = new MovParcelaVO();
                proRata.setValorParcela((Uteis.arredondarForcando2CasasDecimais(aluguel.getValor()) / aluguel.getProduto().getNrDiasVigencia()) * nrDiasUsar);
                proRata.setDescricao(aluguel.getProduto().getDescricao() + " " + Uteis.getDataMesAnoConcatenado(venda.getVencimentoPrimeiraParcela()) + " PRO-RATA (" + nrDiasUsar+" DIAS)");
                proRata.setDataVencimento(Uteis.obterDataFuturaParcela(Calendario.maior(hoje,venda.getVencimentoPrimeiraParcela()) ? hoje : venda.getVencimentoPrimeiraParcela(), 0));
                proRata.setDataRegistro(venda.getDataRegistro());
                proRata.setPercentualJuro(venda.getEmpresa().getJuroParcela());
                proRata.setPercentualMulta(venda.getEmpresa().getMulta());
                proRata.setResponsavel(venda.getResponsavel());
                proRata.setSituacao("EA");

                ItemVendaAvulsaVO itemProRata =  (ItemVendaAvulsaVO)venda.getItemVendaAvulsaVOs().get(0).getClone(true);
                itemProRata.getProduto().setValorFinal(proRata.getValorParcela());
                itemProRata.getProduto().setDescricao(aluguel.getProduto().getDescricao() + " " + Uteis.getDataMesAnoConcatenado(venda.getVencimentoPrimeiraParcela()) + " PRO-RATA (" + nrDiasUsar+" DIAS)");
                venda.getItemVendaAvulsaVOs().add(itemProRata);

                venda.getMovParcelaVOs().add(proRata);
                venda.getItemVendaAvulsaVOs().get(0).setDataValidade(aluguel.getFimOriginal());

                aluguel.setFimOriginal(venda.getVencimentoPrimeiraParcela());
                aluguel.setValor(aluguel.getValor() - proRata.getValorParcela());
                gerarMovParcelaProRata(aluguel, 1, Uteis.obterDataFuturaParcela(venda.getVencimentoPrimeiraParcela(), 1));
                venda.setDescricaoAdicional("");
            }else{
                if(Calendario.maiorOuIgual(venda.getVencimentoPrimeiraParcela(),hoje)){
                    gerarMovParcela(aluguel, 1, venda.getVencimentoPrimeiraParcela());
                    aluguel.setFimOriginal(venda.getVencimentoPrimeiraParcela());
                    if (venda.getMovParcelaVOs().size() == 1) {
                        ProdutoVO produtoVO =  venda.getItemVendaAvulsaVOs().get(0).getProduto();
                        venda.getMovParcelaVOs().get(0).setDescricao(produtoVO.getDescricao());
                    }
                    venda.getItemVendaAvulsaVOs().get(0).setDataValidade(aluguel.getFimOriginal());
                }
            }
        }else{
            nrDiasUsar = 30;
            gerarMovParcela(aluguel,1,venda.getVencimentoPrimeiraParcela());
            aluguel.setFimOriginal(venda.getVencimentoPrimeiraParcela());
            aluguel.getMovProduto().setDataFinalVigencia(aluguel.getFimOriginal());
            venda.getItemVendaAvulsaVOs().get(0).setDataValidade(aluguel.getFimOriginal());
        }
    }
    public Boolean verificarMesFevereiro(Date mesInicio){
        if(Uteis.getMesData(mesInicio) == 2 && Uteis.getDiaMesData(mesInicio) > 27)
            return true;
        return false;
    }
    public void gerarMovParcelaProRata(AluguelArmarioVO aluguel, int mesInicio,Date dataInicio) throws Exception{

        int mes = mesInicio;
         Date data = (dataInicio == null ? hoje : dataInicio);
        int e = 1;
        while(e <= numeroParcelas){
            MovParcelaVO proRata = new MovParcelaVO();
            proRata.setValorParcela(Uteis.arredondarForcando2CasasDecimais(obterValorParcelaSemProporcional(aluguel)/venda.getNrVezesParcelamento()));
            proRata.setValorBaseCalculo(Uteis.arredondarForcando2CasasDecimais(obterValorParcelaSemProporcional(aluguel)/venda.getNrVezesParcelamento()));
            inicializarValoresParcelas(venda, proRata, e, data, "EA");
            data = obterDataFuturaSemFev(dataInicio, mes);
            venda.getMovParcelaVOs().add(e-1,proRata);
            mes++;
            e++;
        }
    }
    public void gerarMovParcela(AluguelArmarioVO aluguel, int mesInicio,Date dataInicio) throws Exception{
        int mes = mesInicio;
        Date data = (dataInicio == null ? dataInicio : dataInicio);
        Date dataAtual = (dataInicio == null ? dataInicio : dataInicio);
        int e = 1;
        double valorTotal = 0;

        ProdutoVO produtoVO =  venda.getItemVendaAvulsaVOs().get(0).getProduto();
        String descProduto = produtoVO.getDescricao();
        String nomeProduto = produtoVO.getDescricao().substring(0, descProduto.indexOf("(") >= 0 ?  descProduto.indexOf("(") : descProduto.length() );
        produtoVO.setDescricao(nomeProduto);
        venda.setDescricaoAdicional(nomeProduto);

        while(e <= numeroParcelas){
            MovParcelaVO proRata = new MovParcelaVO();
            double valorParcela = Uteis.arredondarForcando2CasasDecimais(obterValorParcela(aluguel) );
            proRata.setValorParcela(valorParcela);
            proRata.setValorBaseCalculo(valorParcela);
            if (aluguel.getValor() == 0.0) {
                inicializarValoresParcelas(venda, proRata, e, data, "PG");
            } else {
                inicializarValoresParcelas(venda, proRata, e, data, "EA");
            }
            data = obterDataFuturaSemFev(dataAtual, mes);
            venda.getMovParcelaVOs().add(e-1,proRata);
            mes++;
            e++;
            valorTotal += valorParcela;
        }
        if(valorTotal <  aluguel.getValor()) {
            produtoVO.setValorFinal(valorTotal);
            produtoVO.setDescricao(nomeProduto + "( PROPORCIONAL "+nrDiasUsar+" DIAS )");
        }
    }
    public Date obterDataFuturaSemFev(Date dia,int somar) throws Exception{

        if(Uteis.getDiaMesData(dia)>27 && Uteis.getMesData(dia) == 2) {
            Calendar aux = Calendar.getInstance();
            aux.set(Calendar.YEAR,Uteis.getAnoData(dia));
            aux.set(Calendar.MONTH, Uteis.getMesData(dia)-2);
            aux.set(Calendar.DAY_OF_MONTH, 30);
            return Uteis.obterDataFuturaParcela(aux.getTime(), somar+1);
        }else
      return Uteis.obterDataFuturaParcela(dia, somar);
    }
    private void inicializarValoresParcelas(VendaAvulsaVO obj, MovParcelaVO movParcela, int nrParcela, Date dataAtual, String situacaoParcela) throws Exception {
        if (obj.getNrVezesParcelamento() == 1) {
            movParcela.setDescricao(obj.getDescricaoAdicional().isEmpty() ? "Venda Avulso" : obj.getDescricaoAdicional());
        } else {
            movParcela.setDescricao("PARCELA ARMARIO " + nrParcela );
        }
        movParcela.setDataRegistro(obj.getDataRegistro());
        movParcela.setDataVencimento(Calendario.maior(hoje,dataAtual) ? hoje : dataAtual);
        movParcela.setPercentualJuro(obj.getEmpresa().getJuroParcela());
        movParcela.setPercentualMulta(obj.getEmpresa().getMulta());
        movParcela.setResponsavel(obj.getResponsavel());
        movParcela.setSituacao(situacaoParcela);
    }
    public VendaAvulsaVO montarVenda(AluguelArmarioVO aluguel,
                                      UsuarioVO responsavel,ProdutoVO produtoSelecionado,
                                      Integer nrVezes, Date vencimentoPrimeiraParcela) throws Exception {
        VendaAvulsaVO venda = new VendaAvulsaVO();
        venda.setTipoComprador("CI");
        if (UteisValidacao.emptyNumber(aluguel.getCliente().getCodigo())) {
            throw new Exception("Cliente não encontrado.");
        }
        if(produtoSelecionado == null){
            throw new Exception("Produto não selecioando");
        }
        venda.setCliente(aluguel.getCliente());
        Date data = hoje;
        venda.setDataRegistro(data == null ? hoje : data);
        venda.setNomeComprador(aluguel.getCliente().getPessoa().getNome());
        venda.setEmpresa(aluguel.getCliente().getEmpresa());
        venda.setResponsavel(responsavel);
        venda.setNrVezesParcelamento(nrVezes);
        venda.setVencimentoPrimeiraParcela(vencimentoPrimeiraParcela);
        venda.setDescricaoAdicional(produtoSelecionado.getDescricao());
        ItemVendaAvulsaVO item = new ItemVendaAvulsaVO();
        aluguel.setProduto(produtoSelecionado);
        aluguel.setFimOriginal(vencimentoPrimeiraParcela);
        aluguel.setDataInicio(dataInicio);
        item.setProduto(produtoSelecionado);
        item.setQuantidade(1);
        item.setValorParcial(aluguel.getValor());
        item.setDataVenda(dataInicio);
        item.setDataValidade(vencimentoPrimeiraParcela);
        venda.setValorTotal(0.0);
        venda.getItemVendaAvulsaVOs().add(item);
        return venda;
    }

    public int getQtdeDiasTolerancia() {
        return qtdeDiasTolerancia;
    }

    public void setQtdeDiasTolerancia(int qtdeDiasTolerancia) {
        this.qtdeDiasTolerancia = qtdeDiasTolerancia;
    }

    public int getDiaReferenciaProrata() {
        return diaReferenciaProrata;
    }

    public void setDiaReferenciaProrata(int diaReferenciaProrata) {
        this.diaReferenciaProrata = diaReferenciaProrata;
    }


    public Date getDataInicio() {
        return dataInicio;
    }

    public void setDataInicio(Date dataInicio) {
        this.dataInicio = dataInicio;
    }

    public int getQtdeDiasCalculado() {
        return qtdeDiasCalculado;
    }

    public void setQtdeDiasCalculado(int qtdeDiasCalculado) {
        this.qtdeDiasCalculado = qtdeDiasCalculado;
    }

    public VendaAvulsaVO getVenda() {
        return venda;
    }

    public void setVenda(VendaAvulsaVO venda) {
        this.venda = venda;
    }

    public int getNumeroParcelas() {
        return numeroParcelas;
    }

    public ConfiguracaoSistemaVO getConfiguracaoSistemaVO() {
        return configuracaoSistemaVO;
    }

    public void setConfiguracaoSistemaVO(ConfiguracaoSistemaVO configuracaoSistemaVO) {
        this.configuracaoSistemaVO = configuracaoSistemaVO;
    }

    public Date getVencimentoParcela() {
        return vencimentoParcela;
    }

    public void setVencimentoParcela(Date vencimentoParcela) {
        this.vencimentoParcela = vencimentoParcela;
    }

    public Date getHoje() {
        return hoje;
    }

    public void setHoje(Date hoje) {
        this.hoje = hoje;
    }

    public int getDiaAplicarProrata() {
        return diaAplicarProrata;
    }

    public void setDiaAplicarProrata(int diaAplicarProrata) {
        this.diaAplicarProrata = diaAplicarProrata;
    }

    public void setNumeroParcelas(int numeroParcelas) {
        this.numeroParcelas = numeroParcelas;
    }
}


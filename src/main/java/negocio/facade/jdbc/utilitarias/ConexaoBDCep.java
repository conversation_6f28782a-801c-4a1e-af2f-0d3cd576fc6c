package negocio.facade.jdbc.utilitarias;

import java.sql.*;
import javax.naming.*;
import javax.sql.*;
import negocio.comuns.utilitarias.EditorOC;
import negocio.comuns.utilitarias.Uteis;

public class ConexaoBDCep {
    private final boolean JNDI = false;
    private final String driverJDBC = "org.postgresql.Driver";
    private final String conexaoPadraoJDBC = "jdbc:postgresql:";
    private final String nomeJNDI = "";
    protected String nomeBD = "";
    protected String usernameBD = "postgres";
    protected String senhaBD = "admin";
    private String ipServidor = "";
    private String porta = "5432";
    /*
     Por enquanto subentende-se que o BDCep está no mesmo servidor do BD Principal
     */
    public ConexaoBDCep() {
        try {
            inicializarCfgConexaoBD();
        } catch (Exception ex) {
            ipServidor = "";
        }
    }

    private String getXMLDocumentCFG() throws Exception {
        EditorOC editor = new EditorOC();
        editor.montarEsqueleto(Uteis.nomeArqCFG);
        return editor.getText();
    }

    private String getValorTAG(String xml, String tagName) throws Exception {
        String tagNameInicial = "<" + tagName + ">";
        int posIni = xml.indexOf(tagNameInicial) + tagNameInicial.length();
        String tagNameFinal = "</" + tagName + ">";
        int posFinal = xml.lastIndexOf(tagNameFinal);
        String valor = xml.substring(posIni, posFinal);
        return valor;
    }

    private void inicializarCfgConexaoBD() throws Exception {
        String xml = getXMLDocumentCFG();
        ipServidor = getValorTAG(xml, "servidor");
        porta = getValorTAG(xml, "porta");
        ipServidor = "//" + ipServidor + ":" + porta + "/";
        nomeBD = getValorTAG(xml, "nomeBDCep");
        usernameBD = getValorTAG(xml, "username");
        senhaBD = getValorTAG(xml, "senha");
    }
    
    private Connection getConexaoJNDI() throws Exception {
        Context ctx = new InitialContext();
        if (ctx == null) {
            throw new Exception("Servidor não disponível - Contexto JNDI não localizado.");
        }
        DataSource ds = (DataSource)ctx.lookup("java:/comp/env/" + nomeJNDI);
        if (ds == null) {
            throw new Exception("JNDI Name inexistente. Não foi possível conectar o BD.");
        }
        Connection conn = ds.getConnection();
        return conn;
    }
    
    private Connection getConexaoJDBC() throws Exception {
        Class.forName(driverJDBC).newInstance();
        Connection conn = DriverManager.getConnection(conexaoPadraoJDBC + ipServidor + nomeBD, usernameBD, senhaBD);
        return conn;
    }
    
    public Connection getConexao() throws Exception {
        if (!JNDI) {
            return getConexaoJDBC();
        }
        else {
            return getConexaoJNDI();
        }
    }
    
    public static int obterUltimoCodigoGeradoTabela(Connection con, String nomeTabela) throws Exception {
        String csCodigoGerado = "SELECT LAST_INSERT_ID() from " + nomeTabela;
        Statement stmt = con.createStatement();
        ResultSet resultado = stmt.executeQuery(csCodigoGerado);
        resultado.first();
        return (resultado.getInt(1));
    }

    public String getNomeBD() {
        return nomeBD;
    }

    public void setNomeBD(String nomeBD) {
        this.nomeBD = nomeBD;
    }

    public String getUsernameBD() {
        return usernameBD;
    }

    public void setUsernameBD(String usernameBD) {
        this.usernameBD = usernameBD;
    }

    public String getSenhaBD() {
        return senhaBD;
    }

    public void setSenhaBD(String senhaBD) {
        this.senhaBD = senhaBD;
    }
    
}

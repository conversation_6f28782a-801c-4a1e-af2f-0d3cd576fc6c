package negocio.facade.jdbc.utilitarias;

import br.com.pactosolucoes.comuns.util.JSFUtilities;
import br.com.pactosolucoes.oamd.controle.basico.DAO;
import com.p6spy.engine.wrapper.ConnectionWrapper;
import negocio.comuns.utilitarias.Uteis;
import negocio.facade.jdbc.arquitetura.FacadeManager;

import javax.naming.Context;
import javax.naming.InitialContext;
import javax.servlet.http.HttpSession;
import javax.sql.DataSource;
import java.sql.*;
import java.util.HashMap;
import java.util.Map;
import java.util.logging.Level;
import java.util.logging.Logger;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

import negocio.facade.jdbc.arquitetura.SuperFacadeJDBC;
import org.postgresql.jdbc.PgConnection;
import servicos.propriedades.PropsService;

/**
 * Esta classe encapsula a lógica de obtensão da conecção.
 *
 * <AUTHOR>
 */
public class Conexao {

    private static final String driverJDBC_SQLSERVER = "net.sourceforge.jtds.jdbc.Driver";
    private static final String conexaoPadraoJDBC_SQLSERVER = "jdbc:jtds:sqlserver:";
    public static final String conexaoPadraoJDBC = "jdbc:postgresql:";
    public static final String conexaoPadraoJDBCDebug = "jdbc:p6spy:postgresql:";
    public static final String APPLICATION_NAME = "ApplicationName";
    public static final String INSTANCE_NAME = System.getProperty("com.sun.aas.instanceName", "ZW");
    //jdbc:p6spy:mysql
    /**
     * <AUTHOR>
     * @See InicioControle.java Conexao estatica para ser utilizada por objetos
     * J2SE como threads e ou, objetos JEE como webservices; Será mantida uma
     * conexão, para utilização eventual e não concorrente, para evitar dead
     * locks em transações longas
     */
    private static Map<String, Connection> pseudoConexao = new HashMap<String, Connection>();
    private static ThreadLocal<String> pseudoChave = new ThreadLocal<String>();
    public static final String SGBD_VERSION_COMP = "SGBD_VERSION_COMP";
    public static final String URL_METADATA = "URL_METADATA";
    public static final String SGBD_VERSION_MAJOR = "SGBD_VERSION_MAJOR";
    private static final String PATTERN_PGSQL_VERSION = "[0-9].[0-9].[0-9]";
    private static final String PGSQL_VERSION_COMMAND = "select version();";
    public static final String PGSQL_DRIVER = "org.postgresql.Driver";
    public static final String PGSQL_DRIVER_DEBUG = "com.p6spy.engine.spy.P6SpyDriver";
    private static final String CON = "con";
    private static final String PWD = "pwd";
    private static final String DESV = "desv";
    private static final String TRUE = "true";
    private String servidor = "";
    private String ipServidor = "";
    private String nomeJNDI = "jdbc/BDZillyonWebCE";
    private String nomeBD = "";
    private String usernameBD = "postgres";
    private String senhaBD = "pactodb";
    private String porta = "5432";
    private Boolean JNDI = false;
    private String url = "";
    private String urlOAMD = "";
    private boolean roboApenasComoServico = false;
    private boolean isNFe = false;
    private static volatile Conexao _instance;

    private Conexao() {
        try {
            // usa-se os parametros vindos do XML, caso na classe DAO
            // esteja inicializado o atributo usarCfgXML = true (apenas em
            // DESENV)
            inicializarCfgConexaoBD();
        } catch (Exception e) {
            ipServidor = ""; // localhost
        }
    }

    public static Conexao getInstance() {
        if (_instance == null) {
            synchronized (Conexao.class) {
                if (_instance == null) {
                    _instance = new Conexao();
                }
            }
        }
        JSFUtilities.storeOnSession("isNFE", _instance.isNFe);
        return _instance;
    }

    public static String getUrlWithAppName(final String url) {
        return url != null && !url.contains(APPLICATION_NAME) ? String.format("%s?%s=%s", url, APPLICATION_NAME, INSTANCE_NAME)
                : url;
    }

    public Conexao(final String url, final String user, final String pwd) {
        this.url = getUrlWithAppName(url);
        this.usernameBD = user;
        this.senhaBD = pwd;
    }

    /**
     * Esse método é utilizado para retornar uma constante de desenvolvimento
     * setada no arquivo cfgBD.xml
     *
     * @return Boolean
     * @throws Exception
     */
    public static Boolean getDesv() throws Exception {
        String desvSession = (String) JSFUtilities.getFromSession(DESV);
        String desvRequest = (String) JSFUtilities.getFromRequest(DESV);
        return desvSession != null && desvSession.equalsIgnoreCase(TRUE)
                || desvRequest != null && desvRequest.equalsIgnoreCase(TRUE);
    }

    public static boolean isPGSQL_Debug() {
        return PropsService.isTrue(PropsService.debugJDBC);
    }

    public static String getPGSQL_Driver() {
        return isPGSQL_Debug()
                ? PGSQL_DRIVER_DEBUG : PGSQL_DRIVER;
    }

    public static String getConexaoPadraoPGSQL() {
        return isPGSQL_Debug()
                ? conexaoPadraoJDBCDebug : conexaoPadraoJDBC;
    }

    public static Connection obterConexaoBancoEmpresas() throws ClassNotFoundException, SQLException {
        Conexao con = Conexao.getInstance();
        String url = con.getUrlOAMD();
        Class.forName(getPGSQL_Driver());
        Uteis.logar(null, "url->" + url);
        return DriverManager.getConnection(url, con.getUsernameBD(), con.senhaBD);
    }

    public static int obterUltimoCodigoGeradoTabela(Connection con, String nomeTabela) throws Exception {
        String csCodigoGerado = "SELECT last_value FROM " + nomeTabela + "_codigo_seq";
        Statement stmt = con.createStatement(ResultSet.TYPE_SCROLL_INSENSITIVE, ResultSet.CONCUR_READ_ONLY);
        ResultSet resultado = stmt.executeQuery(csCodigoGerado);
        resultado.first();
        return (resultado.getInt(1));
    }

    public static int obterUltimoCodigoGeradoTabela(Connection con, String nomeTabela, String identificadorPK) throws Exception {
        String csCodigoGerado = "SELECT last_value FROM " + nomeTabela + "_" + identificadorPK + "_seq";
        Statement stmt = con.createStatement(ResultSet.TYPE_SCROLL_INSENSITIVE, ResultSet.CONCUR_READ_ONLY);
        ResultSet resultado = stmt.executeQuery(csCodigoGerado);
        resultado.first();
        return (resultado.getInt(1));
    }


    public static Connection getConexaoThreadAtual(String chave) {
        return pseudoConexao.get(chave);
    }

    public static void putConexaoThreadAtual(final String chave, final Connection c) {
        pseudoConexao.remove(chave);
        pseudoConexao.put(chave, c);
        pseudoChave.set(chave);
    }

    public static Connection getConexaoForJ2SE() {
        return getConexaoThreadAtual(pseudoChave.get());
    }

    /**
     * Guarda uma conexão em contexto estático para utilização de outros objetos
     * não conhecedores do FacesContext
     *
     * @param conexao
     * @throws SQLException
     */

    public static void guardarConexaoForJ2SE(Connection conexao)
            throws SQLException {
        FacadeManager.limparFactory();
        putConexaoThreadAtual("", conexao);
    }

    public static void guardarConexaoForJ2SE(final String chave, Connection conexao)
            throws SQLException {
        FacadeManager.limparFactory();
        putConexaoThreadAtual(chave, conexao);

    }

    public static void guardarConexaoForServlet(final String chave, Connection conexao)
            throws SQLException {
        putConexaoThreadAtual(chave, conexao);
    }

    /**
     * Este deve ser o único ponto onde se obtem a conexão da sessão.
     *
     * @return
     */
    public static Connection getFromSession() {
        ConnectionSerializable conexao = (ConnectionSerializable) JSFUtilities.getFromSession(CON);
        if (conexao != null) {
            return conexao.getCon();
        } else {
            return null;
        }
    }

    public static boolean isRoboApenasComoServico() {
        ConnectionSerializable conexao = (ConnectionSerializable) JSFUtilities.getFromSession(CON);
        return conexao != null && conexao.isRoboApenasComoServico();
    }

    public static String obterVersaoSGBD(final Connection con) {
        if (con != null) {
            try (ResultSet rs = SuperFacadeJDBC.criarConsulta(PGSQL_VERSION_COMMAND, con)) {
                if (rs.next()) {
                    final String version = rs.getString(1);
                    final Pattern p = Pattern.compile(PATTERN_PGSQL_VERSION);
                    final Matcher m = p.matcher(version);
                    if (m.find()) {
                        return m.group();
                    }
                }
            } catch (Exception e) {
                Logger.getLogger(Conexao.class.getName()).log(Level.SEVERE, null, e);
            }
        }
        return null;
    }


    public static void storeOnSession(Connection con) {
        Conexao conexao = Conexao.getInstance();
        String pwd = (String) JSFUtilities.getFromSession(PWD);
        pwd = pwd == null ? conexao.getSenhaDefault() : pwd;
        obterVersaoSGBD(con);
        ConnectionSerializable conSerial = new ConnectionSerializable(con, pwd, conexao.roboApenasComoServico);
        JSFUtilities.storeOnSession(CON, conSerial);
    }

    public static Connection getFromSession(HttpSession session) {
        ConnectionSerializable conexao = (ConnectionSerializable) session.getAttribute(CON);
        if (conexao != null && conexao.getCon() != null) {
            return conexao.getCon();
        } else {
            return null;
        }
    }

    public static Connection initSession(final String key) throws Exception {
        Connection conEspecifica;
        if (Conexao.getFromSession() == null) {
            DAO dao = new DAO();
            conEspecifica = dao.obterConexaoEspecifica(key);
            dao = null;
            // armazena conexão na sessão.
            Conexao.storeOnSession(conEspecifica);
            JSFUtilities.storeOnSession("key", key);
            return conEspecifica;
        } else {
            return Conexao.getFromSession();
        }
    }

    private void inicializarCfgConexaoBD() throws Exception {
        String xml = Uteis.getXMLDocumentCFG(Uteis.nomeArqCFG);
        servidor = Uteis.getValorTAG(xml, "servidor");
        ipServidor = Uteis.getValorTAG(xml, "servidor");
        porta = Uteis.getValorTAG(xml, "porta");
        if (!porta.isEmpty()) {
            ipServidor = "//" + ipServidor + ":" + porta + "/";
        } else {
            ipServidor = "//" + ipServidor + "/";
        }
        nomeBD = Uteis.getValorTAG(xml, "nomeBD");
        usernameBD = Uteis.getValorTAG(xml, "username");
        senhaBD = Uteis.getValorTAG(xml, "senha");
        nomeJNDI = Uteis.getValorTAG(xml, "JNDI");
        isNFe = Uteis.getValorTAGBoolean(xml, "NFE");
        if (isNFe) {
            urlOAMD = Uteis.getValorTAG(xml, "url-oamd");
        } else {
            urlOAMD = Conexao.getUrlWithAppName(Uteis.getValorTAG(xml, "url-oamd"));
        }

        roboApenasComoServico = Uteis.getValorTAGBoolean(xml, "roboApenasComoServico");

    }

    private Connection getConexaoJDBC() throws Exception {
        String driver = getPGSQL_Driver();
        String conexao = getConexaoPadraoPGSQL();

        if (isNFe) {
            driver = Conexao.driverJDBC_SQLSERVER;
            conexao = Conexao.conexaoPadraoJDBC_SQLSERVER;
        }

        Class.forName(driver).newInstance();
        Connection conn;
        if (url.isEmpty()) {
            if (isNFe) {
                conn = DriverManager.getConnection(conexao + ipServidor + getNomeBD(), getUsernameBD(), senhaBD);
            } else {
                conn = DriverManager.getConnection(getUrlWithAppName(conexao + ipServidor + getNomeBD()), getUsernameBD(), senhaBD);
            }
        } else {
            if (isNFe) {
                conn = DriverManager.getConnection(url, getUsernameBD(), senhaBD);
            } else {
                conn = DriverManager.getConnection(getUrlWithAppName(url), getUsernameBD(), senhaBD);
            }
        }
        return conn;
    }

    private Connection getConexaoJNDI() throws Exception {
        Context ctx = new InitialContext();
        if (ctx == null) {
            throw new Exception("Servidor não disponível - Contexto JNDI não localizado.");
        }
        // DataSource ds =(DataSource)ctx.lookup("java:/comp/env/" + nomeJNDI);
        DataSource ds = (DataSource) ctx.lookup(nomeJNDI);
        if (ds == null) {
            throw new Exception("JNDI Name inexistente. Não foi possível conectar o BD.");
        }
        return ds.getConnection();
    }

    public Connection getConexao() throws Exception {
        if (!JNDI) {
            return getConexaoJDBC();
        } else {
            return getConexaoJNDI();
        }
    }

    public Connection obterNovaConexaoBaseadaOutra(Connection con) throws SQLException {
        String passwordBD = JSFUtilities.getFromSession(PWD) == null ?
                this.getSenhaDefault() : (String) JSFUtilities.getFromSession(PWD);
        PgConnection pgConnection = null;
        if (con instanceof ConnectionWrapper) {
            pgConnection =  ((PgConnection) ((ConnectionWrapper) con).getDelegate());
        } else {
            pgConnection = (PgConnection) con;
        }

        String u = getUrlWithAppName(pgConnection.getURL());
        String userBD = pgConnection.getUserName();
        return DriverManager.getConnection(u, userBD, passwordBD);
    }

    public String getIpServidor() {
        return ipServidor;
    }

    public String getPorta() {
        return porta;
    }

    public String getSenhaDefault() {
        return this.senhaBD;
    }

    public String getUrlOAMD() {
        if (Conexao.isPGSQL_Debug()) {
            return urlOAMD.replace(Conexao.conexaoPadraoJDBC, conexaoPadraoJDBCDebug);
        }
        return urlOAMD;
    }

    public String getServidor() {
        return servidor;
    }

    public String getUsernameBD() {
        return usernameBD;
    }

    public String getNomeBD() {
        return nomeBD;
    }

    public static String getPseudoChave_apresentar() {
        if (pseudoChave == null || pseudoChave.get() == null) {
            return "";
        }
        return pseudoChave.get();
    }

    public static String getPseudoConexao_apresentar() throws SQLException {
        if (pseudoConexao == null || pseudoConexao.isEmpty()) {
            return "vazio";
        }
        String retorno = "";
        for (Map.Entry<String, Connection> entrySet : pseudoConexao.entrySet()) {
            retorno += entrySet.getKey() + " / ";
            retorno += entrySet.getValue().getCatalog() + "\n";

        }
        return retorno;
    }
}

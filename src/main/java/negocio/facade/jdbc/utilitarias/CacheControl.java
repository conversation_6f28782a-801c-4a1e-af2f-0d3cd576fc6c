/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package negocio.facade.jdbc.utilitarias;

import java.util.HashMap;
import java.util.logging.Level;
import java.util.logging.Logger;
import negocio.comuns.arquitetura.SuperVO;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisValidacao;

/**
 *
 * <AUTHOR>
 */
public final class CacheControl {

    private static final ThreadLocal<Boolean> tlReuseReference = ThreadLocal.withInitial(() -> false);
    private static final ThreadLocal<HashMap<Class, HashMap<Integer, SuperVO>>> cache = ThreadLocal.withInitial(HashMap::new);
    private static final ThreadLocal<HashMap<Class, Boolean>> tlCacheEnable = ThreadLocal.withInitial(HashMap::new);

    public static boolean cacheEnabled(final Class clazz) {
        HashMap<Class, Boolean> cacheEnable = tlCacheEnable.get();
        return cacheEnable.getOrDefault(clazz, false);
    }

    public static void toggleCache(Class clazz, final boolean active) {
        HashMap<Class, Boolean> cacheEnable = tlCacheEnable.get();
        cacheEnable.put(clazz, active);
        if (cache.get().get(clazz) == null) {
            cache.get().put(clazz, new HashMap<>());
        }        
        cache.get().put(clazz, new HashMap<>());
    }

    public static void reuseReference() {
        tlReuseReference.set(true);
    }

    public static void clear() {
        tlCacheEnable.remove();
        cache.remove();
        tlReuseReference.set(false);
    }

    public static SuperVO getFromCache(final Integer codigo, final Class cls) {                
        if (CacheControl.cacheEnabled(cls)) {
            if (cache.get() != null && cache.get().get(cls) != null && codigo != null && codigo > 0) {
                SuperVO eCache = cache.get().get(cls).get(codigo);
                /*Uteis.logar(String.format("Obtendo %s from Cache codigo %s hashcode %s",
                        cls.getSimpleName(), codigo, eCache));*/
                return eCache;
            }
        }
        return null;
    }

    public static void putToCache(final SuperVO vo, final Class cls) {
        if (CacheControl.cacheEnabled(cls)) {
            if (!UteisValidacao.emptyNumber(vo.getCodigo())) {
                try {
                    if (cache.get().get(cls) == null) {
                        cache.get().put(cls, new HashMap<>());
                    }

                    boolean reuseReference = tlReuseReference.get();
                    if (reuseReference) {
                        cache.get().get(cls).put(vo.getCodigo(), vo);
                    } else {
                        cache.get().get(cls).put(vo.getCodigo(), (SuperVO) vo.getClone(true));
                    }
                    Uteis.logar(String.format("PUT %s to Cache codigo %s hashcode %s", vo.getClass().getSimpleName(), vo.getCodigo(), vo));
                } catch (IllegalArgumentException | IllegalAccessException | InstantiationException ex) {
                    Logger.getLogger(CacheControl.class.getName()).log(Level.SEVERE, null, ex);
                }
            }
        }
    }
}

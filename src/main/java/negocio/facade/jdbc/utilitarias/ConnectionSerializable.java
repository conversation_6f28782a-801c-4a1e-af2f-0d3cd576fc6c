/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package negocio.facade.jdbc.utilitarias;

import java.io.Serializable;
import java.sql.Connection;

/**
 *
 * <AUTHOR>
 */
public class ConnectionSerializable implements Serializable {

    transient Connection con;
    private String pwd;
    private boolean roboApenasComoServico = false;

    public ConnectionSerializable(Connection con, String pwd, boolean roboApenasComoServico) {
        this.con = con;
        this.pwd = pwd;
        this.roboApenasComoServico = roboApenasComoServico;
    }

    public Connection getCon() {
        return con;
    }

    public String getPwd() {
        return pwd;
    }

    public void setPwd(String pwd) {
        this.pwd = pwd;
    }

    public boolean isRoboApenasComoServico() {
        return roboApenasComoServico;
    }

    public void setRoboApenasComoServico(boolean roboApenasComoServico) {
        this.roboApenasComoServico = roboApenasComoServico;
    }
}

/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */

package negocio.facade.jdbc.utilitarias;

import java.sql.Connection;
import java.util.logging.Level;
import java.util.logging.Logger;
import javax.naming.InitialContext;
import javax.naming.NamingException;
import javax.sql.DataSource;
import negocio.comuns.utilitarias.EditorOC;
import negocio.comuns.utilitarias.Uteis;

/**
 *
 * <AUTHOR>
 */
public class PoolConexoes {    
            
    private static InitialContext context;
    private static DataSource source;
    private static PoolConexoes pool;

    static {        
        try {
            context = new InitialContext();

            source = null;
            String xml = null;
            try {
                xml = getXMLDocumentCFG();
            } catch (Exception ex) {
                Logger.getLogger(PoolConexoes.class.getName()).log(Level.SEVERE, ex.getMessage(), ex);
            }
            try {
                String nomeJNDI = getValorTAG(xml, "JNDI");
                source = (DataSource) context.lookup(nomeJNDI);
            } catch (Exception ex) {
                Logger.getLogger(PoolConexoes.class.getName()).log(Level.SEVERE, ex.getMessage(), ex);
            }

        } catch (NamingException ex) {
            Logger.getLogger(PoolConexoes.class.getName()).log(Level.SEVERE, ex.getMessage(), ex);
        }
    } 
    

    public static PoolConexoes getCurrentInstance() {        
        if (pool == null){            
            try {

                pool = new PoolConexoes();
                
            } catch (Exception ex) {
                Logger.getLogger(PoolConexoes.class.getName()).log(Level.SEVERE, ex.getMessage(), ex);
            }
            return pool;
        }else{
            return pool;
        }

    }

    private static String getXMLDocumentCFG() throws Exception {
        EditorOC editor = new EditorOC();        
        editor.montarEsqueleto(Uteis.nomeArqCFG);
        return editor.getText();
    }

    private static String getValorTAG(String xml, String tagName) throws Exception {
        String tagNameInicial = "<" + tagName + ">";
        int posIni = xml.indexOf(tagNameInicial) + tagNameInicial.length();
        String tagNameFinal = "</" + tagName + ">";
        int posFinal = xml.lastIndexOf(tagNameFinal);
        String valor = xml.substring(posIni, posFinal);
        return valor;
    }

    public Connection getConexao() throws Exception {
        Connection conn = null;
        try {            
            conn = source.getConnection();
        } catch (Exception e) {
            throw new Exception("Erro ao obter conexão do Pool: "+
                    e.getMessage());
        }
        return conn;
    }   

}

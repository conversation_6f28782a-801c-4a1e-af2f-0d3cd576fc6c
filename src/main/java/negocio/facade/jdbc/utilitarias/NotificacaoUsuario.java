package negocio.facade.jdbc.utilitarias;

import negocio.comuns.arquitetura.UsuarioVO;
import negocio.comuns.basico.EmpresaVO;
import negocio.comuns.basico.enumerador.TipoNotificacaoUsuarioEnum;
import negocio.comuns.utilitarias.*;
import negocio.facade.jdbc.arquitetura.SuperEntidade;
import negocio.facade.jdbc.arquitetura.Usuario;
import negocio.facade.jdbc.arquitetura.ZillyonWebFacade;
import negocio.interfaces.basico.NotificacaoUsuarioInterfaceFacade;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * Created by <PERSON><PERSON> on 26/01/2017.
 */
public class NotificacaoUsuario extends SuperEntidade implements NotificacaoUsuarioInterfaceFacade {

    public NotificacaoUsuario() throws Exception {
        super();
    }

    public NotificacaoUsuario(Connection con) throws Exception {
        super(con);
    }

    public static List<NotificacaoUsuarioVO> montarDadosConsulta(ResultSet tabelaResultado,
                                                                 int nivelMontarDados, Connection con) throws Exception {
        List<NotificacaoUsuarioVO> vetResultado = new ArrayList<NotificacaoUsuarioVO>();
        while (tabelaResultado.next()) {
            NotificacaoUsuarioVO obj = montarDados(tabelaResultado, nivelMontarDados, con);
            vetResultado.add(obj);
        }
        return vetResultado;
    }

    public static NotificacaoUsuarioVO montarDadosBasico(ResultSet dadosSQL) throws Exception {
        NotificacaoUsuarioVO obj = new NotificacaoUsuarioVO();
        obj.setNovoObj(false);
        obj.setCodigo(dadosSQL.getInt("codigo"));
        obj.getUsuarioVO().setCodigo(dadosSQL.getInt("usuario"));
        obj.setTipo(TipoNotificacaoUsuarioEnum.obterPorCodigo(dadosSQL.getInt("tipo")));
        obj.setDataLancamento(dadosSQL.getTimestamp("dataLancamento"));
        obj.setMensagem(dadosSQL.getString("mensagem"));
        obj.setLink(dadosSQL.getString("link"));
        obj.setDados(dadosSQL.getString("dados"));
        obj.setDataNaoApresentar(dadosSQL.getTimestamp("dataNaoApresentar"));
        obj.getEmpresaVO().setCodigo(dadosSQL.getInt("empresa"));
        return obj;
    }

    public static NotificacaoUsuarioVO montarDados(ResultSet dadosSQL, int nivelMontarDados, Connection con) throws Exception {
        NotificacaoUsuarioVO obj = montarDadosBasico(dadosSQL);

        if (nivelMontarDados == Uteis.NIVELMONTARDADOS_DADOSBASICOS) {
            return obj;
        }

        if (nivelMontarDados == Uteis.NIVELMONTARDADOS_MINIMOS) {
            montarDadosUsuario(obj, Uteis.NIVELMONTARDADOS_MINIMOS, con);
            return obj;
        }

        return obj;
    }

    public static void montarDadosUsuario(NotificacaoUsuarioVO obj, int nivelMontarDados, Connection con) throws Exception {
        if (obj.getUsuarioVO().getCodigo() == 0) {
            obj.setUsuarioVO(new UsuarioVO());
            return;
        }

        Usuario usuario = new Usuario(con);
        obj.setUsuarioVO(usuario.consultarPorChavePrimaria(obj.getUsuarioVO().getCodigo(), nivelMontarDados));
        usuario = null;
    }

    public void incluir(NotificacaoUsuarioVO obj) throws Exception {
        String sql = "INSERT INTO notificacaousuario(usuario, tipo, dataLancamento, mensagem, link, apresentarhoje, dataNaoApresentar, dados, empresa) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)";
        PreparedStatement sqlInserir = con.prepareStatement(sql);
        sqlInserir.setInt(1, obj.getUsuarioVO().getCodigo());
        sqlInserir.setInt(2, obj.getTipo().getCodigo());
        sqlInserir.setTimestamp(3, Uteis.getDataJDBCTimestamp(obj.getDataLancamento()));
        sqlInserir.setString(4, obj.getMensagem());
        sqlInserir.setString(5, obj.getLink());
        sqlInserir.setBoolean(6, obj.isApresentarHoje());
        sqlInserir.setTimestamp(7, Uteis.getDataJDBCTimestamp(obj.getDataNaoApresentar()));
        sqlInserir.setString(8, obj.getDados());
        resolveIntegerNull(sqlInserir, 9, obj.getEmpresaVO().getCodigo());
        sqlInserir.execute();

    }

    public void alterar(NotificacaoUsuarioVO obj) throws Exception {
        String sql = "UPDATE notificacaousuario set usuario = ?, tipo = ?, dataLancamento = ?, mensagem = ?, link = ?, apresentarhoje = ?, " +
                "dataNaoApresentar = ?, dados = ?, empresa = ? WHERE codigo = ?";
        PreparedStatement sqlAlterar = con.prepareStatement(sql);
        sqlAlterar.setInt(1, obj.getUsuarioVO().getCodigo());
        sqlAlterar.setInt(2, obj.getTipo().getCodigo());
        sqlAlterar.setTimestamp(3, Uteis.getDataJDBCTimestamp(obj.getDataLancamento()));
        sqlAlterar.setString(4, obj.getMensagem());
        sqlAlterar.setString(5, obj.getLink());
        sqlAlterar.setBoolean(6, obj.isApresentarHoje());
        sqlAlterar.setTimestamp(7, Uteis.getDataJDBCTimestamp(obj.getDataNaoApresentar()));
        sqlAlterar.setString(8, obj.getDados());
        resolveIntegerNull(sqlAlterar, 9, obj.getEmpresaVO().getCodigo());
        sqlAlterar.setInt(10, obj.getCodigo());
        sqlAlterar.execute();
    }

    public void excluir(NotificacaoUsuarioVO obj) throws Exception {
        String sql = "DELETE FROM notificacaousuario WHERE codigo = ?";
        PreparedStatement sqlExcluir = con.prepareStatement(sql);
        sqlExcluir.setInt(1, obj.getCodigo());
        sqlExcluir.execute();
    }

    public List<NotificacaoUsuarioVO> consultarPorUsuario(UsuarioVO usuario, int nivelMontarDados) throws Exception {
        List<NotificacaoUsuarioVO> objetos = new ArrayList<NotificacaoUsuarioVO>();
        String sql = "SELECT * FROM notificacaousuario WHERE usuario = ?";
        PreparedStatement sqlConsulta = con.prepareStatement(sql);
        sqlConsulta.setInt(1, usuario.getCodigo());
        ResultSet resultado = sqlConsulta.executeQuery();
        while (resultado.next()) {
            NotificacaoUsuarioVO novoObj = montarDados(resultado, nivelMontarDados, con);
            objetos.add(novoObj);
        }
        return objetos;
    }

    public NotificacaoUsuarioVO consultarPorUsuarioTipo(UsuarioVO usuario, TipoNotificacaoUsuarioEnum tipo, int nivelMontarDados) throws Exception {
        String sql = "SELECT * FROM notificacaousuario WHERE usuario = ? AND tipo = ?";
        PreparedStatement sqlConsulta = con.prepareStatement(sql);
        sqlConsulta.setInt(1, usuario.getCodigo());
        sqlConsulta.setInt(2, tipo.getCodigo());
        ResultSet resultado = sqlConsulta.executeQuery();
        if (!resultado.next()) {
            return new NotificacaoUsuarioVO();
        }
        return montarDados(resultado, nivelMontarDados, con);
    }

    public List<NotificacaoUsuarioVO> consultarPorUsuarioTipo(UsuarioVO usuario, List<TipoNotificacaoUsuarioEnum> listaTipos, int nivelMontarDados) throws Exception {
        List<NotificacaoUsuarioVO> objetos = new ArrayList<NotificacaoUsuarioVO>();

        String codigosBuscar = retornarCodigosTipos(listaTipos);
        StringBuilder sql = new StringBuilder();
        sql.append("SELECT * FROM notificacaousuario \n");
        sql.append("WHERE 1 = 1 \n");
        sql.append("AND usuario = ").append(usuario.getCodigo()).append(" \n");
        sql.append("AND tipo in (").append(codigosBuscar).append(")");
        PreparedStatement sqlConsulta = con.prepareStatement(sql.toString());
        ResultSet resultado = sqlConsulta.executeQuery();
        while (resultado.next()) {
            NotificacaoUsuarioVO novoObj = montarDados(resultado, nivelMontarDados, con);
            objetos.add(novoObj);
        }
        return objetos;
    }

    private static String retornarCodigosTipos(List<TipoNotificacaoUsuarioEnum> listaTipos) {
        if (UteisValidacao.emptyList(listaTipos)) {
            return "";
        }

        StringBuilder codigos = new StringBuilder();
        for (TipoNotificacaoUsuarioEnum obj : listaTipos) {
            if (codigos.toString().equals("")) {
                codigos.append(obj.getCodigo());
            } else {
                codigos.append(",").append(obj.getCodigo());
            }
        }

        return codigos.toString();
    }

    @Override
    public String enviarNotificacaoUsuario(Integer codUsuario, String username, String mensagem, String link, Integer tipoNotificacao) throws Exception {
        UsuarioVO usuarioVO = new UsuarioVO();
        ZillyonWebFacade zwFacade = new ZillyonWebFacade(con);

        if (codUsuario != null && codUsuario > 0) {
            usuarioVO = zwFacade.getUsuario().consultarPorCodigoUsuario(codUsuario, false, Uteis.NIVELMONTARDADOS_MINIMOS);
        } else if (!UteisValidacao.emptyString(username)) {
            List<UsuarioVO> listaUsuarios = zwFacade.getUsuario().consultarPorUsername(username, false, Uteis.NIVELMONTARDADOS_MINIMOS);
            if (!UteisValidacao.emptyList(listaUsuarios)) {
                usuarioVO = listaUsuarios.get(0);
            }
        }
        zwFacade = null;

        if (UteisValidacao.emptyNumber(usuarioVO.getCodigo())) {
            throw new Exception("Usuário não encontrado.");
        }

        TipoNotificacaoUsuarioEnum tipo = TipoNotificacaoUsuarioEnum.obterPorCodigo(tipoNotificacao);
        if (tipo == null) {
            throw new Exception("Tipo de Notificação não encontrado.");
        }

        if (tipo.isPermiteVarias()) { //SE PERMITE VÁRIAS SOMENTE INCLUIR

            NotificacaoUsuarioVO obj = new NotificacaoUsuarioVO();
            obj.setUsuarioVO(usuarioVO);
            obj.setTipo(tipo);
            obj.setDataLancamento(Calendario.hoje());
            obj.setMensagem(mensagem);
            obj.setLink(link);
            incluir(obj);
            return "OK - Incluido Novo Permite Varios";

        } else { //SE NÃO PERMITE VÁRIAS ALTERAR O ANTIGO COM AS NOVAS INFORMAÇÕES

            NotificacaoUsuarioVO obj = consultarPorUsuarioTipo(usuarioVO, tipo, Uteis.NIVELMONTARDADOS_DADOSBASICOS);

            if (!UteisValidacao.emptyNumber(obj.getCodigo())) {
                obj.setDataLancamento(Calendario.hoje());
                obj.setMensagem(mensagem);
                obj.setLink(link);
                alterar(obj);
                return "OK - Alterado";
            } else {
                obj.setUsuarioVO(usuarioVO);
                obj.setTipo(tipo);
                obj.setDataLancamento(Calendario.hoje());
                obj.setMensagem(mensagem);
                obj.setLink(link);
                incluir(obj);
                return "OK - Incluido Novo Nao Permite Varios";
            }
        }
    }

    public void excluirTodasPorTipoUsuario(NotificacaoUsuarioVO obj, UsuarioVO usuario) throws Exception {
        String sql = "DELETE FROM notificacaousuario WHERE tipo = ? AND usuario = ? ";
        PreparedStatement sqlExcluir = con.prepareStatement(sql);
        sqlExcluir.setInt(1, obj.getTipo().getCodigo());
        sqlExcluir.setInt(2, usuario.getCodigo());
        sqlExcluir.execute();
    }

    public NotificacaoUsuarioVO consultarNotificacao(UsuarioVO usuario, TipoNotificacaoUsuarioEnum tipoNotificacaoUsuarioEnum,
                                                     Boolean apresentarhoje, Date dataReferencia, EmpresaVO empresaVO,
                                                     int nivelMontarDados) throws Exception {
        StringBuilder sql = new StringBuilder();
        sql.append("SELECT * FROM notificacaousuario \n");
        sql.append("WHERE usuario = ").append(usuario.getCodigo()).append(" \n");
        sql.append("AND tipo = ").append(tipoNotificacaoUsuarioEnum.getCodigo()).append(" \n");
        if (empresaVO != null && !UteisValidacao.emptyNumber(empresaVO.getCodigo())) {
            sql.append("AND empresa = ").append(empresaVO.getCodigo()).append(" \n");
        }
        if (apresentarhoje != null) {
            sql.append("AND apresentarhoje = ").append(apresentarhoje).append(" \n");
        }
        if (dataReferencia != null) {
            sql.append("AND datalancamento::date = '").append(Uteis.getDataFormatoBD(dataReferencia)).append("' \n");
        }
        PreparedStatement sqlConsulta = con.prepareStatement(sql.toString());
        ResultSet resultado = sqlConsulta.executeQuery();
        if (!resultado.next()) {
            return new NotificacaoUsuarioVO();
        }
        return montarDados(resultado, nivelMontarDados, con);
    }
}

package negocio.facade.jdbc.notaFiscal;

import br.com.pactosolucoes.integracao.enotas.EnotasService;
import br.com.pactosolucoes.integracao.enotas.to.InutilizacaoNotaFiscalTO;
import negocio.comuns.arquitetura.UsuarioVO;
import negocio.comuns.notaFiscal.NotaFiscalOperacaoVO;
import negocio.comuns.notaFiscal.NotaFiscalVO;
import negocio.comuns.notaFiscal.OperacaoNotaFiscalEnum;
import negocio.comuns.notaFiscal.StatusEnotasEnum;
import negocio.comuns.notaFiscal.StatusOperacaoNotaFiscalEnum;
import negocio.comuns.utilitarias.Calendario;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisValidacao;
import negocio.facade.jdbc.arquitetura.SuperEntidade;
import negocio.interfaces.notaFiscal.NotaFiscalOperacaoInterfaceFacade;
import org.json.JSONObject;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.util.ArrayList;
import java.util.List;

public class NotaFiscalOperacao extends SuperEntidade implements NotaFiscalOperacaoInterfaceFacade {

    public NotaFiscalOperacao() throws Exception {
        super();
    }

    public NotaFiscalOperacao(Connection con) throws Exception {
        super(con);
    }

    public void incluir(NotaFiscalOperacaoVO obj) throws Exception {
        try {
            con.setAutoCommit(false);
            incluirSemCommit(obj);
            con.commit();
        } catch (Exception e) {
            con.rollback();
            con.setAutoCommit(true);
            throw e;
        } finally {
            con.setAutoCommit(true);
        }
    }

    public void incluirSemCommit(NotaFiscalOperacaoVO obj) throws Exception {
//        NotaFiscalOperacaoVO.validarDados(obj);
        String sql = "INSERT INTO NotaFiscalOperacao (dataRegistro, operacao, chave, idEmpresaEnotas, usuario, notafiscal, " +
                "justificativa, status, jsonRetorno, statusRetorno, numeroInicial, numeroFinal, ambienteEmissao, serie, tipoNota) " +
                "VALUES (?, ?, ?, ?, ?, ? ,? ,?, ? ,? ,?, ?, ?, ?, ?)";
        int i = 0;
        PreparedStatement sqlInserir = con.prepareStatement(sql);
        sqlInserir.setTimestamp(++i, Uteis.getDataJDBCTimestamp(Calendario.hoje()));
        sqlInserir.setInt(++i, obj.getOperacao().getCodigo());
        sqlInserir.setString(++i, obj.getChave());
        sqlInserir.setString(++i, obj.getIdEmpresaEnotas());
        resolveIntegerNull(sqlInserir, ++i, obj.getUsuarioVO().getCodigo());
        resolveIntegerNull(sqlInserir, ++i, obj.getNotaFiscalVO().getCodigo());
        sqlInserir.setString(++i, obj.getJustificativa());
        sqlInserir.setInt(++i, obj.getStatus().getCodigo());
        sqlInserir.setString(++i, obj.getJsonRetorno());
        sqlInserir.setString(++i, obj.getStatusRetorno());
        resolveIntegerNull(sqlInserir, ++i, obj.getNumeroInicial());
        resolveIntegerNull(sqlInserir, ++i, obj.getNumeroFinal());
        sqlInserir.setString(++i, obj.getAmbienteEmissao());
        sqlInserir.setString(++i, obj.getSerie());
        sqlInserir.setInt(++i, obj.getTipoNota());

        sqlInserir.execute();
        obj.setCodigo(obterValorChavePrimariaCodigo());
        obj.setNovoObj(false);
    }


    public void alterar(NotaFiscalOperacaoVO obj) throws Exception {
        try {
            con.setAutoCommit(false);
            alterarSemCommit(obj);
            con.commit();
        } catch (Exception e) {
            con.rollback();
            con.setAutoCommit(true);
            throw e;
        } finally {
            con.setAutoCommit(true);
        }
    }

    public void alterarSemCommit(NotaFiscalOperacaoVO obj) throws Exception {
//        NotaFiscalOperacaoVO.validarDados(obj);
        String sql = "UPDATE NotaFiscalOperacao SET dataRegistro = ? , operacao = ?, chave = ?, IdEmpresaEnotas = ?, usuario = ?, notafiscal = ?, "
                + "justificativa = ?, status = ?, jsonRetorno = ?, statusRetorno = ?, numeroInicial = ?, numeroFinal = ?, ambienteEmissao = ?, serie = ?, tipoNota = ? WHERE codigo = ?";
        int i = 0;
        PreparedStatement sqlAlterar = con.prepareStatement(sql);
        sqlAlterar.setTimestamp(++i, Uteis.getDataJDBCTimestamp(Calendario.hoje()));
        sqlAlterar.setInt(++i, obj.getOperacao().getCodigo());
        sqlAlterar.setString(++i, obj.getChave());
        sqlAlterar.setString(++i, obj.getIdEmpresaEnotas());
        resolveIntegerNull(sqlAlterar, ++i, obj.getUsuarioVO().getCodigo());
        resolveIntegerNull(sqlAlterar, ++i, obj.getNotaFiscalVO().getCodigo());
        sqlAlterar.setString(++i, obj.getJustificativa());
        sqlAlterar.setInt(++i, obj.getStatus().getCodigo());
        sqlAlterar.setString(++i, obj.getJsonRetorno());
        sqlAlterar.setString(++i, obj.getStatusRetorno());
        sqlAlterar.setInt(++i, obj.getNumeroInicial());
        sqlAlterar.setInt(++i, obj.getNumeroFinal());
        sqlAlterar.setString(++i, obj.getAmbienteEmissao());
        sqlAlterar.setString(++i, obj.getSerie());
        sqlAlterar.setInt(++i, obj.getTipoNota());

        sqlAlterar.setInt(++i, obj.getCodigo());
        sqlAlterar.execute();
    }

    public void excluir(NotaFiscalOperacaoVO obj) throws Exception {
        try {
            con.setAutoCommit(false);
            excluirSemCommit(obj);
            con.commit();
        } catch (Exception e) {
            con.rollback();
            con.setAutoCommit(true);
            throw e;
        } finally {
            con.setAutoCommit(true);
        }
    }

    public void excluirSemCommit(NotaFiscalOperacaoVO obj) throws Exception {
        String sql = "DELETE FROM NotaFiscalOperacao WHERE codigo = ?";
        PreparedStatement sqlExcluir = con.prepareStatement(sql);
        sqlExcluir.setInt(1, obj.getCodigo());
        sqlExcluir.execute();
    }

    public static List<NotaFiscalOperacaoVO> montarDadosConsulta(ResultSet rs, int nivelMontarDados, Connection con) throws Exception {
        List<NotaFiscalOperacaoVO> vetResultado = new ArrayList<NotaFiscalOperacaoVO>();
        while (rs.next()) {
            NotaFiscalOperacaoVO obj = montarDados(rs, nivelMontarDados, con);
            vetResultado.add(obj);
        }
        return vetResultado;
    }

    public static NotaFiscalOperacaoVO montarDados(ResultSet rs, int nivelMontarDados, Connection con) throws Exception {
        NotaFiscalOperacaoVO obj = new NotaFiscalOperacaoVO();
        obj.setCodigo(rs.getInt("codigo"));
        obj.setNovoObj(false);
        obj.setDataRegistro(rs.getTimestamp("dataRegistro"));
        obj.setOperacao(OperacaoNotaFiscalEnum.obterPorCodigo(rs.getInt("operacao")));
        obj.setChave(rs.getString("chave"));
        obj.setIdEmpresaEnotas(rs.getString("IdEmpresaEnotas"));
        obj.getUsuarioVO().setCodigo(rs.getInt("usuario"));
        obj.getNotaFiscalVO().setCodigo(rs.getInt("notaFiscal"));
        obj.setJustificativa(rs.getString("justificativa"));
        obj.setStatus(StatusOperacaoNotaFiscalEnum.obterPorCodigo(rs.getInt("status")));
        obj.setJsonRetorno(rs.getString("jsonRetorno"));
        obj.setJsonRetorno(rs.getString("statusRetorno"));
        obj.setNumeroInicial(rs.getInt("numeroInicial"));
        obj.setNumeroFinal(rs.getInt("numeroFinal"));
        obj.setAmbienteEmissao(rs.getString("ambienteEmissao"));
        obj.setSerie(rs.getString("serie"));
        obj.setTipoNota(rs.getInt("tipoNota"));

        if (nivelMontarDados == Uteis.NIVELMONTARDADOS_DADOSBASICOS) {
            return obj;
        }

        if (nivelMontarDados == Uteis.NIVELMONTARDADOS_DADOSENTIDADESPRINCIPAIS) {
            NotaFiscal notaFiscalDAO = new NotaFiscal(con);
            obj.setNotaFiscalVO(notaFiscalDAO.consultarPorChavePrimaria(obj.getNotaFiscalVO().getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS));
            notaFiscalDAO = null;
            return obj;
        }

        return obj;
    }

    public NotaFiscalOperacaoVO consultarPorChavePrimaria(Integer codigo, int nivelMontarDados) throws Exception {
        String sql = "SELECT * FROM NotaFiscalOperacao WHERE codigo = ?";
        PreparedStatement sqlConsultar = con.prepareStatement(sql);
        sqlConsultar.setInt(1, codigo);
        ResultSet rs = sqlConsultar.executeQuery();
        if (!rs.next()) {
            return new NotaFiscalOperacaoVO();
        }
        return montarDados(rs, nivelMontarDados, con);
    }

    private List<NotaFiscalOperacaoVO> consultarOperacoesAguardandoEnvio() throws Exception {
        StringBuilder sql = new StringBuilder();
        sql.append(" SELECT * FROM NotaFiscalOperacao \n ");
        sql.append(" WHERE 1 = 1 \n");
        sql.append(" AND status = ? \n");
        sql.append(" ORDER BY codigo ");
        PreparedStatement ps = con.prepareStatement(sql.toString());
        ps.setInt(1, StatusOperacaoNotaFiscalEnum.GERADA.getCodigo());
        ResultSet rs = ps.executeQuery();
        return montarDadosConsulta(rs, Uteis.NIVELMONTARDADOS_DADOSENTIDADESPRINCIPAIS, con);
    }

    private void atualizarStatus(NotaFiscalOperacaoVO obj) throws Exception {
        String sql = "UPDATE NotaFiscalOperacao SET status = ? WHERE codigo  = ?";
        PreparedStatement sqlInserir = con.prepareStatement(sql);
        sqlInserir.setInt(1, obj.getStatus().getCodigo());
        sqlInserir.setInt(2, obj.getCodigo());
        sqlInserir.execute();
    }

    public String realizarEnvioOperacoesServicoNota() {
        try {

            List<NotaFiscalOperacaoVO> lista = consultarOperacoesAguardandoEnvio();
            if (UteisValidacao.emptyList(lista)) {
                return "Nenhuma solicitação pendente.";
            }

            Uteis.logar(true, null, "### VOU ENVIAR OPERACAO - Total " + lista.size());

            EnotasService enotasService = new EnotasService(con);
            NotaFiscal notaDAO = new NotaFiscal(con);
            for (NotaFiscalOperacaoVO obj : lista) {
                try {
                    obj.setStatus(StatusOperacaoNotaFiscalEnum.ENVIADA);
                    atualizarStatus(obj);
                    enotasService.enviarOperacao(obj);
                    if (obj.getStatus().equals(StatusOperacaoNotaFiscalEnum.GERADA)) {
                        atualizarStatus(obj);
                    } else if (obj.getOperacao().equals(OperacaoNotaFiscalEnum.INUTILIZAR)) {
                        notaDAO.atualizarStatusNota(StatusEnotasEnum.INUTILIZACAOSOLICITADO, obj.getNotaFiscalVO());
                        obj.getNotaFiscalVO().setStatusNota(StatusEnotasEnum.INUTILIZACAOSOLICITADO.getDescricaoEnotas());
                    } else if (obj.getOperacao().equals(OperacaoNotaFiscalEnum.CANCELAR)) {
                        notaDAO.atualizarStatusNota(StatusEnotasEnum.SOLICITANDOCANCELAMENTO, obj.getNotaFiscalVO());
                        obj.getNotaFiscalVO().setStatusNota(StatusEnotasEnum.SOLICITANDOCANCELAMENTO.getDescricaoEnotas());
                    }
                } catch (Exception ex) {
                    ex.printStackTrace();
                }
            }

            enotasService = null;
            notaDAO = null;
            return "Solicitações enviadas.";
        } catch (Exception ex) {
            Uteis.logar(true, null, "ERRO realizarEnvioOperacoesServicoNota: " + ex.getMessage());
            return "Erro ao enviar solicitações: " + ex.getMessage();
        }
    }

    public void gerarSolicitacaoOperacao(String chave, String justificativa,
                                         NotaFiscalVO notaFiscalVO, UsuarioVO usuarioVO,
                                         OperacaoNotaFiscalEnum operacaoNotaFiscalEnum) throws Exception {

        try {
            con.setAutoCommit(false);

            NotaFiscalOperacaoVO obj = new NotaFiscalOperacaoVO();
            obj.setIdReferencia(notaFiscalVO.getIdReferencia());
            obj.setIdExterno(notaFiscalVO.getIdExterno());
            obj.setNotaFiscalVO(notaFiscalVO);
            obj.setUsuarioVO(usuarioVO);
            if(!UteisValidacao.emptyString(notaFiscalVO.getRps())){
                obj.setNumeroInicial(Integer.parseInt(notaFiscalVO.getRps()));
                obj.setNumeroFinal(Integer.parseInt(notaFiscalVO.getRps()));
            }else if (!UteisValidacao.emptyString(notaFiscalVO.getNumeroNota())) {
                obj.setNumeroInicial(Integer.parseInt(notaFiscalVO.getNumeroNota()));
                obj.setNumeroFinal(Integer.parseInt(notaFiscalVO.getNumeroNota()));
            }
            obj.setJustificativa(justificativa);
            obj.setOperacao(operacaoNotaFiscalEnum);
            obj.setDataRegistro(Calendario.hoje());
            obj.setChave(chave);
            obj.setIdEmpresaEnotas(notaFiscalVO.getIdEmpresaEnotas());
            obj.setStatus(StatusOperacaoNotaFiscalEnum.GERADA);
            obj.setTipoNota(notaFiscalVO.getTipo().getCodigo());
            obj.setAmbienteEmissao(notaFiscalVO.getConfiguracaoNotaFiscalVO().getAmbienteEmissao().toString());
            obj.setSerie(notaFiscalVO.getSerie());
            incluirSemCommit(obj);

            NotaFiscalHistorico historicoDAO = new NotaFiscalHistorico(con);
            historicoDAO.gerarHistoricoSemCommit(operacaoNotaFiscalEnum.getDescricao(),
                    "SOLICITAÇÃO MANUAL", "", notaFiscalVO.getCodigo(), usuarioVO);
            historicoDAO = null;

            con.commit();
        } catch (Exception e) {
            con.rollback();
            con.setAutoCommit(true);
            throw e;
        } finally {
            con.setAutoCommit(true);
        }

        realizarEnvioOperacoesServicoNota();
    }

    public void gerarSolicitacaoInutilizacaoPorFaixa(String chave, String idEmpresaEnotas,
                                                     String justificativa, Integer numeroInicial, Integer numeroFinal,
                                                     UsuarioVO usuarioVO, String ambienteEmissao, String serie, Integer tipoNota) throws Exception {
        NotaFiscalOperacaoVO obj = new NotaFiscalOperacaoVO();
        obj.setUsuarioVO(usuarioVO);
        obj.setNumeroInicial(numeroInicial);
        obj.setNumeroFinal(numeroFinal);
        obj.setJustificativa(justificativa);
        obj.setOperacao(OperacaoNotaFiscalEnum.INUTILIZAR);
        obj.setDataRegistro(Calendario.hoje());
        obj.setChave(chave);
        obj.setIdEmpresaEnotas(idEmpresaEnotas);
        obj.setStatus(StatusOperacaoNotaFiscalEnum.GERADA);
        obj.setAmbienteEmissao(ambienteEmissao);
        obj.setSerie(serie);
        obj.setTipoNota(tipoNota);
        incluirSemCommit(obj);

        realizarEnvioOperacoesServicoNota();
    }

    public NotaFiscalOperacaoVO consultarUltimaSoliciacaoInutilizacao(NotaFiscalVO notaFiscalVO) throws Exception {

        String sql = "select * from NotaFiscalOperacao " +
                "where notafiscal = "+notaFiscalVO.getCodigo()+" " +
                "and operacao = "+OperacaoNotaFiscalEnum.INUTILIZAR.getCodigo() +" " +
                "order by dataregistro desc " +
                "limit 1";

        PreparedStatement ps = con.prepareStatement(sql);
        ResultSet rs = ps.executeQuery();
        NotaFiscalOperacaoVO operacaoVO = null;
        if (rs.next()) {
            operacaoVO =  montarDados(rs, Uteis.NIVELMONTARDADOS_DADOSBASICOS, con);
        }

        return operacaoVO;
    }

    private void verificarSolicitacaoPendente(NotaFiscalVO notaFiscalVO) throws Exception {
        StringBuilder sql = new StringBuilder();
        sql.append("select * from NotaFiscalOperacao \n");
        sql.append("where notafiscal = ").append(notaFiscalVO.getCodigo()).append(" \n");
        sql.append("and status in (");
        sql.append(StatusOperacaoNotaFiscalEnum.GERADA.getCodigo()).append(",");
        sql.append(StatusOperacaoNotaFiscalEnum.ENVIADA.getCodigo()).append(") ");
        PreparedStatement ps = con.prepareStatement(sql.toString());
        ResultSet rs = ps.executeQuery();
        NotaFiscalOperacaoVO operacaoVO = new NotaFiscalOperacaoVO();
        if (rs.next()) {
            operacaoVO = montarDados(rs, Uteis.NIVELMONTARDADOS_DADOSBASICOS, con);
        }

        if (!UteisValidacao.emptyNumber(operacaoVO.getCodigo())) {
            throw new Exception("Existe uma solicitação de " + operacaoVO.getOperacao().getDescricao().toUpperCase()
                    + " ainda não processada para a nota fiscal (Cod. " + notaFiscalVO.getCodigo() + ")");
        }
    }

    public void processarRetornoOperacao(String jsonRetorno, Integer codNotaFiscalOperacao) throws Exception {
        try {
            con.setAutoCommit(false);

            NotaFiscalOperacaoVO operacaoVO = consultarPorChavePrimaria(codNotaFiscalOperacao, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
            if (UteisValidacao.emptyNumber(operacaoVO.getCodigo())) {
                throw new Exception("NotaFiscalOperacao não encontrada! " + codNotaFiscalOperacao);
            }

            // https://portal.enotasgw.com.br/knowledge-base/nfc-e-inutilizacao/
            operacaoVO.setJsonRetorno(jsonRetorno);


            if (!UteisValidacao.emptyNumber(operacaoVO.getNotaFiscalVO().getCodigo()) &&
                    operacaoVO.getOperacao().equals(OperacaoNotaFiscalEnum.CANCELAR) &&
                    jsonRetorno.toUpperCase().contains("CERTIFICADO DIGITAL DA EMPRESA") &&
                    jsonRetorno.toUpperCase().contains("VENCIDO")) {
                //certificado está vencido voltar nota para autorizada
                NotaFiscal notaFiscalDAO = new NotaFiscal(con);
                notaFiscalDAO.atualizarStatusNota(StatusEnotasEnum.AUTORIZADA, operacaoVO.getNotaFiscalVO());
                notaFiscalDAO = null;
            }

            if (jsonRetorno.startsWith("{")) {
                JSONObject json = new JSONObject(jsonRetorno);
                operacaoVO.setStatusRetorno(json.getString("status"));
            }
            operacaoVO.setStatus(StatusOperacaoNotaFiscalEnum.PROCESSADA);
            alterarSemCommit(operacaoVO);

            con.commit();
        } catch (Exception e) {
            e.printStackTrace();
            con.rollback();
            con.setAutoCommit(true);
            throw e;
        } finally {
            con.setAutoCommit(true);
        }
    }

    public List<NotaFiscalOperacaoVO> obterInutilizacoesRealizadas(String idEmpresaEnotas) throws Exception {
        String sql = "SELECT * \n" +
                "FROM NotaFiscalOperacao\n" +
                "WHERE 1 = 1 \n" +
                "AND operacao = ?\n" +
                "AND idempresaenotas = ?\n" +
                "AND notafiscal is null\n" +
                "ORDER BY codigo ";
        try (PreparedStatement ps = con.prepareStatement(sql)) {
            ps.setInt(1, OperacaoNotaFiscalEnum.INUTILIZAR.getCodigo());
            ps.setString(2, idEmpresaEnotas);
            try (ResultSet rs = ps.executeQuery()) {
                return montarDadosConsulta(rs, Uteis.NIVELMONTARDADOS_DADOSENTIDADESPRINCIPAIS, con);
            }
        }
    }

    public InutilizacaoNotaFiscalTO consultarInutilizacao(NotaFiscalOperacaoVO notaFiscalOperacaoVO) throws Exception {
        EnotasService enotasService = new EnotasService(con);
        InutilizacaoNotaFiscalTO inutilizacaoNotaFiscalTO = enotasService.consultarInutilizacao(notaFiscalOperacaoVO);
        enotasService = null;
        return inutilizacaoNotaFiscalTO;
    }
}

package negocio.facade.jdbc.notaFiscal;

import br.com.pactosolucoes.enumeradores.ProcessoAjusteGeralEnum;
import negocio.comuns.arquitetura.UsuarioVO;
import negocio.comuns.financeiro.NFSeEmitidaVO;
import negocio.comuns.notaFiscal.NotaFiscalFamiliaVO;
import negocio.comuns.notaFiscal.NotaFiscalVO;
import negocio.comuns.utilitarias.Uteis;
import negocio.facade.jdbc.arquitetura.SuperEntidade;
import negocio.facade.jdbc.financeiro.NFSeEmitida;
import negocio.facade.jdbc.financeiro.NFSeEmitidaHistorico;
import negocio.interfaces.notaFiscal.NotaFiscalFamiliaInterfaceFacade;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.sql.Statement;
import java.util.ArrayList;
import java.util.List;

public class NotaFiscalFamilia extends SuperEntidade implements NotaFiscalFamiliaInterfaceFacade {

    public NotaFiscalFamilia() throws Exception {
        super();
    }

    public NotaFiscalFamilia(Connection con) throws Exception {
        super(con);
    }

    public void incluir(NotaFiscalFamiliaVO obj) throws Exception {
        try {
            con.setAutoCommit(false);
            incluirSemCommit(obj);
            con.commit();
        } catch (Exception e) {
            con.rollback();
            con.setAutoCommit(true);
            throw e;
        } finally {
            con.setAutoCommit(true);
        }
    }

    public void incluirSemCommit(NotaFiscalFamiliaVO obj) throws Exception {
        String sql = "INSERT INTO NotaFiscalFamilia (sequencialFamilia, nfseEmitida, notaFiscal) VALUES (?, ?, ?)";
        int i = 0;
        PreparedStatement sqlInserir = con.prepareStatement(sql);
        resolveIntegerNull(sqlInserir, ++i, obj.getSequencialFamilia());
        resolveIntegerNull(sqlInserir, ++i, obj.getNfSeEmitidaVO().getCodigo());
        resolveIntegerNull(sqlInserir, ++i, obj.getNotaFiscalVO().getCodigo());

        sqlInserir.execute();
        obj.setCodigo(obterValorChavePrimariaCodigo());
        obj.setNovoObj(false);
    }


    public void alterar(NotaFiscalFamiliaVO obj) throws Exception {
        try {
            con.setAutoCommit(false);
            alterarSemCommit(obj);
            con.commit();
        } catch (Exception e) {
            con.rollback();
            con.setAutoCommit(true);
            throw e;
        } finally {
            con.setAutoCommit(true);
        }
    }

    public void alterarSemCommit(NotaFiscalFamiliaVO obj) throws Exception {
        String sql = "UPDATE NotaFiscalFamilia SET sequencialFamilia = ?, nfseEmitida = ?, notaFiscal = ? WHERE codigo = ?";
        int i = 0;
        PreparedStatement sqlAlterar = con.prepareStatement(sql);
        resolveIntegerNull(sqlAlterar, ++i, obj.getSequencialFamilia());
        resolveIntegerNull(sqlAlterar, ++i, obj.getNfSeEmitidaVO().getCodigo());
        resolveIntegerNull(sqlAlterar, ++i, obj.getNotaFiscalVO().getCodigo());

        sqlAlterar.setInt(++i, obj.getCodigo());
        sqlAlterar.execute();
    }

    public void excluir(NotaFiscalFamiliaVO obj) throws Exception {
        try {
            con.setAutoCommit(false);
            excluirSemCommit(obj);
            con.commit();
        } catch (Exception e) {
            con.rollback();
            con.setAutoCommit(true);
            throw e;
        } finally {
            con.setAutoCommit(true);
        }
    }

    public void excluirSemCommit(NotaFiscalFamiliaVO obj) throws Exception {
        String sql = "DELETE FROM NotaFiscalFamilia WHERE codigo = ?";
        PreparedStatement sqlExcluir = con.prepareStatement(sql);
        sqlExcluir.setInt(1, obj.getCodigo());
        sqlExcluir.execute();
    }

    public void excluirComLogNotaFamilia(Integer codNotaFiscal, NFSeEmitidaVO obj,
                                    UsuarioVO usuarioVO) throws SQLException {
        try {
            con.setAutoCommit(false);

            NFSeEmitida nfSeEmitidaDAO = new NFSeEmitida(con);
            nfSeEmitidaDAO.excluirComLogSemCommit(obj, usuarioVO, ProcessoAjusteGeralEnum.EXCLUIR_NOTA_FAMILIA);
            nfSeEmitidaDAO = null;

            NFSeEmitidaHistorico histDAO = new NFSeEmitidaHistorico(con);
            histDAO.excluirPorNFSeEmitida(obj);
            histDAO = null;

            NotaFiscal notaDAO = new NotaFiscal(con);
            notaDAO.marcarExcluido(true, codNotaFiscal, usuarioVO);
            notaDAO = null;

            con.commit();
        } catch (Exception ex) {
            con.rollback();
            con.setAutoCommit(true);
        } finally {
            con.setAutoCommit(true);
        }
    }

    public static List<NotaFiscalFamiliaVO> montarDadosConsulta(ResultSet rs, int nivelMontarDados, Connection con) throws Exception {
        List<NotaFiscalFamiliaVO> vetResultado = new ArrayList<>();
        while (rs.next()) {
            NotaFiscalFamiliaVO obj = montarDados(rs, nivelMontarDados, con);
            vetResultado.add(obj);
        }
        return vetResultado;
    }

    public static NotaFiscalFamiliaVO montarDados(ResultSet rs, int nivelMontarDados, Connection con) throws Exception {
        NotaFiscalFamiliaVO obj = new NotaFiscalFamiliaVO();
        obj.setCodigo(rs.getInt("codigo"));
        obj.setNovoObj(false);
        obj.setSequencialFamilia(rs.getInt("sequencialFamilia"));
        obj.getNfSeEmitidaVO().setCodigo(rs.getInt("NfSeEmitida"));
        obj.getNotaFiscalVO().setCodigo(rs.getInt("notaFiscal"));

        if (nivelMontarDados == Uteis.NIVELMONTARDADOS_DADOSBASICOS) {
            return obj;
        }

        if (nivelMontarDados == Uteis.NIVELMONTARDADOS_DADOSENTIDADESPRINCIPAIS) {
            NotaFiscal notaFiscalDAO = new NotaFiscal(con);
            obj.setNotaFiscalVO(notaFiscalDAO.consultarPorChavePrimaria(obj.getNotaFiscalVO().getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS));
            notaFiscalDAO = null;
            return obj;
        }

        return obj;
    }

    public NotaFiscalFamiliaVO consultarPorChavePrimaria(Integer codigo, int nivelMontarDados) throws Exception {
        String sql = "SELECT * FROM NotaFiscalFamilia WHERE codigo = ?";
        PreparedStatement sqlConsultar = con.prepareStatement(sql);
        sqlConsultar.setInt(1, codigo);
        ResultSet rs = sqlConsultar.executeQuery();
        if (!rs.next()) {
            return new NotaFiscalFamiliaVO();
        }
        return montarDados(rs, nivelMontarDados, con);
    }

    public List<NotaFiscalFamiliaVO> incluirLista(Integer sequencialFamilia, NotaFiscalVO notaFiscalVO, List<NFSeEmitidaVO> listaNFSeEmitida) throws Exception {
        List<NotaFiscalFamiliaVO> lista = new ArrayList<>();
        for (NFSeEmitidaVO nfse : listaNFSeEmitida) {
            NotaFiscalFamiliaVO novo = new NotaFiscalFamiliaVO();
            novo.setSequencialFamilia(sequencialFamilia);
            novo.setNotaFiscalVO(notaFiscalVO);
            novo.setNfSeEmitidaVO(nfse);
            incluirSemCommit(novo);
            lista.add(novo);
        }
        return lista;
    }

    public List<NotaFiscalFamiliaVO> consultarPorNota (Integer codigo, int nivelMontarDados) throws Exception {
        String sql = "SELECT * FROM NotaFiscalFamilia WHERE notafiscal = " + codigo;
        Statement stm = con.createStatement();
        ResultSet tabelaResultado = stm.executeQuery(sql);
        List<NotaFiscalFamiliaVO> notaFiscalFamiliaVOS = new ArrayList<NotaFiscalFamiliaVO>();
        while (tabelaResultado.next()) {
            notaFiscalFamiliaVOS.add(montarDados(tabelaResultado, nivelMontarDados, con));
        }
        return notaFiscalFamiliaVOS;
    }

    public String montarDescricaoExcluirNotaFiscal(List<NotaFiscalFamiliaVO> obj) throws Exception {
        StringBuilder descricaoNota = new StringBuilder();
        descricaoNota.append("<br><br>***** CÓDIGO NOTA EM GRUPO: ").append(obj.get(0).getNotaFiscalVO().getCodigo()).append(" *****<br>");
        descricaoNota.append("Notas que compõem Nota em Grupo: <br><br>");

        for(int i = 0; i < obj.size(); i++) {
            descricaoNota.append(getFacade().getNFSeEmitida().montarDescricaoExcluirNotaFiscal(getFacade().getNFSeEmitida().consultarPorChavePrimaria(obj.get(i).getNfSeEmitidaVO().getCodigo())));
            descricaoNota.append("<br><br>");
        }

        return descricaoNota.toString();
    }
}

package negocio.facade.jdbc.contrato;

import negocio.comuns.utilitarias.Uteis;
import negocio.facade.jdbc.arquitetura.SuperEntidade;
import negocio.interfaces.contrato.AssinaturaDigitalInterfaceFacade;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.sql.Statement;
import java.util.Date;

public class AssinaturaDigital extends SuperEntidade implements AssinaturaDigitalInterfaceFacade {

    public AssinaturaDigital() throws Exception {
        super();
    }

    public AssinaturaDigital(Connection connection) throws Exception {
        super(connection);
    }

    public void atualizarIPAssinaturaContrato (Integer codigoContrato, String ip, Date dataAssinaturaContrato) throws Exception {
        String sql = "UPDATE contrato set ipassinaturacontrato=?, dataassinaturacontrato=? WHERE ((codigo = ?))";
        try (PreparedStatement sqlAlterar = this.con.prepareStatement(sql)) {
            sqlAlterar.setString(1, ip);
            sqlAlterar.setTimestamp(2, Uteis.getDataJDBCTimestamp(dataAssinaturaContrato));
            sqlAlterar.setInt(3, codigoContrato);
            sqlAlterar.execute();
        }
    }

    public Boolean verificaSeContratoJaFoiAssinado(Integer codigoContrato) throws SQLException {
        try (PreparedStatement stm = this.con.prepareStatement("SELECT * FROM contrato WHERE codigo=? AND dataassinaturacontrato IS NOT NULL ")) {
            stm.setInt(1, codigoContrato);
            try (ResultSet rs = stm.executeQuery()){
                while (rs.next()) {
                    return true;
                }
            }
        }
        return false;
    }
}

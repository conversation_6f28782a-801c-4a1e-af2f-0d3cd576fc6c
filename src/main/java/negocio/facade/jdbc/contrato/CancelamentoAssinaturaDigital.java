package negocio.facade.jdbc.contrato;

import negocio.comuns.arquitetura.UsuarioVO;
import negocio.comuns.contrato.*;
import negocio.comuns.utilitarias.Calendario;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisValidacao;
import negocio.facade.jdbc.arquitetura.SuperEntidade;
import negocio.interfaces.contrato.CancelamentoAssinaturaDigitalInterfaceFacade;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;

public class CancelamentoAssinaturaDigital extends SuperEntidade implements CancelamentoAssinaturaDigitalInterfaceFacade {

    public CancelamentoAssinaturaDigital() throws Exception {
        super();
        setIdEntidade("CancelamentoContrato");
    }

    public CancelamentoAssinaturaDigital(Connection con) throws Exception {
        super(con);
        setIdEntidade("CancelamentoContrato");
    }

    @Override
    public void incluir(CancelamentoAssinaturaDigitalVO obj) throws Exception {
        String sql = "INSERT INTO CancelamentoAssinaturaDigital(contrato, contratooperacao, usuarioresponsavel, assinatura, lancamento) VALUES (?, ?, ?, ?, ?)";
        int i = 1;
        PreparedStatement sqlInserir = con.prepareStatement(sql);
        sqlInserir.setInt(i++, obj.getContrato().getCodigo());
        sqlInserir.setInt(i++, obj.getContratoOperacao().getCodigo());
        sqlInserir.setInt(i++, obj.getUsuarioResponsavel().getCodigo());
        sqlInserir.setString(i++, obj.getAssinatura());
        sqlInserir.setTimestamp(i++, Uteis.getDataJDBCTimestamp(Calendario.hoje()));
        sqlInserir.execute();
        obj.setCodigo(obterValorChavePrimariaCodigo());
        obj.setNovoObj(false);
    }

    @Override
    public void excluir(CancelamentoAssinaturaDigitalVO obj) throws Exception {
        excluir(getIdEntidade());
        String sql = "DELETE FROM CancelamentoAssinaturaDigital WHERE codigo = ?";
        PreparedStatement sqlExcluir = con.prepareStatement(sql);
        sqlExcluir.setInt(1, obj.getCodigo());
        sqlExcluir.execute();
    }

    @Override
    public void excluirPorContrato(Integer contrato) throws Exception {
        excluir(getIdEntidade());
        String sql = "DELETE FROM CancelamentoAssinaturaDigital WHERE contrato = ?";
        PreparedStatement sqlExcluir = con.prepareStatement(sql);
        sqlExcluir.setInt(1, contrato);
        sqlExcluir.execute();
    }

    @Override
    public void alterar(CancelamentoAssinaturaDigitalVO obj) throws Exception {
        String sql = "UPDATE CancelamentoAssinaturaDigital set contrato = ?, contratooperacao = ?, usuarioresponsavel = ?,"
                + "assinatura = ? WHERE codigo = ?";
        int i = 1;
        PreparedStatement sqlAlterar = con.prepareStatement(sql);
        sqlAlterar.setInt(i++, obj.getContrato().getCodigo());
        sqlAlterar.setInt(i++, obj.getContratoOperacao().getCodigo());
        sqlAlterar.setInt(i++, obj.getUsuarioResponsavel().getCodigo());
        sqlAlterar.setString(i++, obj.getAssinatura());
        sqlAlterar.setInt(i++, obj.getCodigo());
        sqlAlterar.execute();
    }

    @Override
    public CancelamentoAssinaturaDigitalVO consultarPorContratoCancelamento(Integer contrato) throws Exception {
        StringBuilder sql = new StringBuilder();
        sql.append(" SELECT c.codigo, c.contrato, c.contratoOperacao, c.usuarioResponsavel, u.nome, c.assinatura, c.lancamento ");
        sql.append(" FROM cancelamentoassinaturadigital c \n");
        sql.append(" inner join usuario u on u.codigo = c.usuarioresponsavel \n");
        sql.append(" inner join contratooperacao op on op.codigo = c.contratooperacao where c.contrato = ").append(contrato);
        ResultSet rs = criarConsulta(sql.toString(), con);
        CancelamentoAssinaturaDigitalVO ass = new CancelamentoAssinaturaDigitalVO();
        ass.setContrato(new ContratoVO());
        ass.getContrato().setCodigo(contrato);
        if (rs.next()) {
            ass.setCodigo(rs.getInt("codigo"));
            if (UteisValidacao.emptyString(rs.getString("assinatura"))) {
                ass.setAssinatura("");
            } else {
                ass.setAssinatura(rs.getString("assinatura"));
            }
            ass.setUsuarioResponsavel(new UsuarioVO());
            ass.getUsuarioResponsavel().setCodigo(rs.getInt("usuarioResponsavel"));
            ass.getUsuarioResponsavel().setNome(rs.getString("nome"));
            ass.setContratoOperacao(new ContratoOperacaoVO());
            ass.getContratoOperacao().setCodigo(rs.getInt("contratoOperacao"));
        }
        return ass;
    }

    @Override
    public void excluirAssinaturaPeloCancelamentoContrato(Integer contrato) throws Exception {
        excluir(getIdEntidade());
        String sql = "UPDATE CancelamentoAssinaturaDigital SET assinatura=null WHERE contrato = ?";
        try (PreparedStatement sqlExcluirAssinatura = con.prepareStatement(sql)) {
            sqlExcluirAssinatura.setInt(1, contrato);
            sqlExcluirAssinatura.execute();
        }
    }

    @Override
    public void excluirAssinaturaEletronicaCancelamento(Integer contrato) throws Exception {
        excluir(getIdEntidade());
        String sql = "UPDATE contrato SET dataassinaturacancelamento=null WHERE codigo = ?";
        try (PreparedStatement sqlExcluirAssinatura = con.prepareStatement(sql)) {
            sqlExcluirAssinatura.setInt(1, contrato);
            sqlExcluirAssinatura.execute();
        }
    }
}

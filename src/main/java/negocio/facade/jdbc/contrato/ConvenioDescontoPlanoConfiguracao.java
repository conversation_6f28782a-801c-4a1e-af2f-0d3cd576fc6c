package negocio.facade.jdbc.contrato;

import negocio.comuns.contrato.ConvenioDescontoPlanoConfiguracaoVO;
import negocio.comuns.utilitarias.Uteis;
import negocio.facade.jdbc.arquitetura.SuperEntidade;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.util.ArrayList;
import java.util.List;

public class ConvenioDescontoPlanoConfiguracao extends SuperEntidade {

    public ConvenioDescontoPlanoConfiguracao() throws Exception {
        super();
        setIdEntidade("ConfiguracaoConvenio");
    }

    public ConvenioDescontoPlanoConfiguracao(Connection con) throws Exception {
    	super(con);
        setIdEntidade("ConfiguracaoConvenio");
	}

    public static ConvenioDescontoPlanoConfiguracaoVO montarDados(ResultSet dadosSQL, int nivelMontarDados) throws Exception {
        ConvenioDescontoPlanoConfiguracaoVO obj = new ConvenioDescontoPlanoConfiguracaoVO();
        obj.setCodigo(dadosSQL.getInt("codigo"));
        obj.setDuracao(dadosSQL.getInt("duracao"));
        obj.setValorDesconto(dadosSQL.getDouble("valorDesconto"));
        obj.setPorcentagemDesconto(dadosSQL.getDouble("porcentagemDesconto"));
        obj.setTipoDesconto(dadosSQL.getString("tipoDesconto"));
        obj.setConvenioDesconto(dadosSQL.getInt("convenioDesconto"));
        obj.setPlano(dadosSQL.getInt("plano"));
        obj.setNovoObj(false);

        if (nivelMontarDados == Uteis.NIVELMONTARDADOS_DADOSBASICOS) {
            return obj;
        }

        return obj;
    }

    public List<ConvenioDescontoPlanoConfiguracaoVO> consultarConvenioDescontoPlanoConfiguraces(Integer convenioDesconto, int nivelMontarDados) throws Exception {
        consultar(getIdEntidade());
        List<ConvenioDescontoPlanoConfiguracaoVO> convenioDescontoPlanoConfiguracaoVOS = new ArrayList<>();
        String sql = "SELECT * FROM ConvenioDescontoPlanoConfiguracao WHERE convenioDesconto = ?";
        try (PreparedStatement sqlConsulta = con.prepareStatement(sql)) {
            sqlConsulta.setInt(1, convenioDesconto);
            try (ResultSet resultado = sqlConsulta.executeQuery()) {
                while (resultado.next()) {
                    ConvenioDescontoPlanoConfiguracaoVO novoObj = montarDados(resultado, nivelMontarDados);
                    convenioDescontoPlanoConfiguracaoVOS.add(novoObj);
                }
            }
        }
        return convenioDescontoPlanoConfiguracaoVOS;
    }
}
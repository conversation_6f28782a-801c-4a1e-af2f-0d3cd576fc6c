package negocio.facade.jdbc.contrato;

import negocio.comuns.arquitetura.UsuarioVO;
import negocio.comuns.basico.ClienteVO;
import negocio.comuns.basico.EmpresaVO;
import negocio.comuns.contrato.ContratoModalidadeHorarioTurmaVO;
import negocio.comuns.contrato.ContratoModalidadeTurmaVO;
import negocio.comuns.contrato.ContratoModalidadeVO;
import negocio.comuns.contrato.ContratoModalidadeVezesSemanaVO;
import negocio.comuns.contrato.ContratoVO;
import negocio.comuns.contrato.MatriculaAlunoHorarioTurmaVO;
import negocio.comuns.plano.HorarioTurmaVO;
import negocio.comuns.plano.ModalidadeVO;
import negocio.comuns.utilitarias.*;
import negocio.facade.jdbc.arquitetura.SuperEntidade;
import negocio.facade.jdbc.arquitetura.SuperFacadeJDBC;
import negocio.facade.jdbc.basico.Cliente;
import negocio.facade.jdbc.plano.Modalidade;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.Statement;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;

/**
 * Classe de persistência que encapsula todas as operações de manipulação dos dados da classe <code>ContratoModalidadeVO</code>.
 * Responsável por implementar operações como incluir, alterar, excluir e consultar pertinentes a classe <code>ContratoModalidadeVO</code>.
 * Encapsula toda a interação com o banco de dados.
 * @see ContratoModalidadeVO
 * @see SuperEntidade
 * @see Contrato
 */
public class ContratoModalidade extends SuperEntidade {   

    public ContratoModalidade() throws Exception {
        super();
        setIdEntidade("Contrato");
    }

    public ContratoModalidade(Connection conexao) throws Exception {
        super(conexao);
        setIdEntidade("Contrato");
    }

    /**
     * Operação responsável por retornar um novo objeto da classe <code>ContratoModalidadeVO</code>.
     */
    public ContratoModalidadeVO novo() throws Exception {
        incluir(getIdEntidade());
        ContratoModalidadeVO obj = new ContratoModalidadeVO();
        return obj;
    }

    /**
     * Operação responsável por incluir no banco de dados um objeto da classe <code>ContratoModalidadeVO</code>.
     * Primeiramente valida os dados (<code>validarDados</code>) do objeto. Verifica a conexão com o banco de dados e a permissão do usuário
     * para realizar esta operacão na entidade.
     * Isto, através da operação <code>incluir</code> da superclasse.
     * @param obj  Objeto da classe <code>ContratoModalidadeVO</code> que será gravado no banco de dados.
     * @exception Exception Caso haja problemas de conexão, restrição de acesso ou validação de dados.
     */
    public void incluirContratoModalidade(ContratoModalidadeVO obj) throws Exception {
        ContratoModalidadeVO.validarDados(obj);
        incluir(getIdEntidade());
        obj.realizarUpperCaseDados();
        String sql = "INSERT INTO ContratoModalidade( contrato, modalidade, valorFinalModalidade,VezesSemana, valorModalidade ) VALUES (?, ?, ?, ?, ? ) RETURNING codigo";
        try (PreparedStatement sqlInserir = con.prepareStatement(sql)) {
            if (obj.getContrato() != 0) {
                sqlInserir.setInt(1, obj.getContrato());
            } else {
                sqlInserir.setNull(1, 0);
            }
            if (obj.getModalidade().getCodigo() != 0) {
                sqlInserir.setInt(2, obj.getModalidade().getCodigo());
            } else {
                sqlInserir.setNull(2, 0);
            }
            sqlInserir.setDouble(3, obj.getValorFinalModalidade());
            if (obj.getNrVezesSemana() != 0) {
                sqlInserir.setInt(4, obj.getNrVezesSemana());
            } else {
                sqlInserir.setNull(4, 0);
            }
            sqlInserir.setDouble(5, obj.getValorModalidade());
            try (ResultSet rs = sqlInserir.executeQuery()) {
                if (rs.next()) {
                    obj.setCodigo(rs.getInt("codigo"));
                    obj.setNovoObj(false);
                }
            }
        }
    }

    /**
     * Operação responsável por alterar no BD os dados de um objeto da classe <code>ContratoModalidadeVO</code>.
     * Sempre utiliza a chave primária da classe como atributo para localização do registro a ser alterado.
     * Primeiramente valida os dados (<code>validarDados</code>) do objeto. Verifica a conexão com o banco de dados e a permissão do usuário
     * para realizar esta operacão na entidade.
     * Isto, através da operação <code>alterar</code> da superclasse.
     * @param obj    Objeto da classe <code>ContratoModalidadeVO</code> que será alterada no banco de dados.
     * @exception Exception Caso haja problemas de conexão, restrição de acesso ou validação de dados.
     */
    public void alterar(ContratoModalidadeVO obj) throws Exception {
        ContratoModalidadeVO.validarDados(obj);
        alterar(getIdEntidade());
        obj.realizarUpperCaseDados();
        String sql = "UPDATE ContratoModalidade set contrato=?, modalidade=?, valorFinalModalidade=?, VezesSemana=?, valorModalidade=? WHERE ((codigo = ?))";
        PreparedStatement sqlAlterar = con.prepareStatement(sql);
        if (obj.getContrato().intValue() != 0) {
            sqlAlterar.setInt(1, obj.getContrato().intValue());
        } else {
            sqlAlterar.setNull(1, 0);
        }
        if (obj.getModalidade().getCodigo().intValue() != 0) {
            sqlAlterar.setInt(2, obj.getModalidade().getCodigo().intValue());
        } else {
            sqlAlterar.setNull(2, 0);
        }
        sqlAlterar.setDouble(3, obj.getValorFinalModalidade().doubleValue());
        if (obj.getNrVezesSemana() != 0) {
            sqlAlterar.setInt(4, obj.getNrVezesSemana());
        } else {
            sqlAlterar.setNull(4, 0);
        }
        sqlAlterar.setDouble(5, obj.getValorFinalModalidade().doubleValue());
        sqlAlterar.setInt(6, obj.getCodigo().intValue());
        sqlAlterar.execute();
        if (obj.getModalidade().getUtilizarTurma()) {
            getFacade().getContratoModalidadeTurma().alterarContratoModalidadeTurma(obj.getCodigo(), obj.getContratoModalidadeTurmaVOs());
        } else {
            obj.gerarContratoModalidade();
            getFacade().getContratoModalidadeVezesSemana().alterarContratoModalidadeVezesSemana(obj.getCodigo(), obj.getContratoModalidadeVezesSemanaVO());
        }
    }

    /**
     * Operação responsável por excluir no BD um objeto da classe <code>ContratoModalidadeVO</code>.
     * Sempre localiza o registro a ser excluído através da chave primária da entidade.
     * Primeiramente verifica a conexão com o banco de dados e a permissão do usuário
     * para realizar esta operacão na entidade.
     * Isto, através da operação <code>excluir</code> da superclasse.
     * @param obj    Objeto da classe <code>ContratoModalidadeVO</code> que será removido no banco de dados.
     * @exception Exception Caso haja problemas de conexão ou restrição de acesso.
     */
    public void excluir(ContratoModalidadeVO obj) throws Exception {
        excluir(getIdEntidade());
        String sql = "DELETE FROM ContratoModalidade WHERE ((codigo = ?))";
        PreparedStatement sqlExcluir = con.prepareStatement(sql);
        sqlExcluir.setInt(1, obj.getCodigo().intValue());
        sqlExcluir.execute();
    }

    /**
     * Responsável por realizar uma consulta de <code>ContratoModalidade</code> através do valor do atributo 
     * <code>String nivelAluno</code>. Retorna os objetos, com início do valor do atributo idêntico ao parâmetro fornecido.
     * Faz uso da operação <code>montarDadosConsulta</code> que realiza o trabalho de prerarar o List resultante.
     * @param   controlarAcesso Indica se a aplicação deverá verificar se o usuário possui permissão para esta consulta ou não.
     * @return  List Contendo vários objetos da classe <code>ContratoModalidadeVO</code> resultantes da consulta.
     * @exception Exception Caso haja problemas de conexão ou restrição de acesso.
     */
    public List consultarPorNivelAluno(String valorConsulta, boolean controlarAcesso, int nivelMontarDados) throws Exception {
        consultar(getIdEntidade(), controlarAcesso);
        String sqlStr = "SELECT * FROM ContratoModalidade WHERE upper( nivelAluno ) like('" + valorConsulta.toUpperCase() + "%') ORDER BY nivelAluno";
        Statement stm = con.createStatement();
        ResultSet tabelaResultado = stm.executeQuery(sqlStr);
        return (montarDadosConsulta(tabelaResultado, nivelMontarDados, this.con));
    }

    /**
     * Responsável por realizar uma consulta de <code>ContratoModalidade</code> através do valor do atributo 
     * <code>nome</code> da classe <code>Modalidade</code>
     * Faz uso da operação <code>montarDadosConsulta</code> que realiza o trabalho de prerarar o List resultante.
     * @return  List Contendo vários objetos da classe <code>ContratoModalidadeVO</code> resultantes da consulta.
     * @exception Exception Caso haja problemas de conexão ou restrição de acesso.
     */
    public List consultarPorNomeModalidade(String valorConsulta, int nivelMontarDados) throws Exception {
        consultar(getIdEntidade(), true);
        String sqlStr = "SELECT ContratoModalidade.* FROM ContratoModalidade, Modalidade WHERE ContratoModalidade.modalidade = Modalidade.codigo and upper( Modalidade.nome ) like('" + valorConsulta.toUpperCase() + "%') ORDER BY Modalidade.nome";
        Statement stm = con.createStatement();
        ResultSet tabelaResultado = stm.executeQuery(sqlStr);
        return montarDadosConsulta(tabelaResultado, nivelMontarDados, this.con);
    }

    /**
     * Responsável por realizar uma consulta de <code>ContratoModalidade</code> através do valor do atributo 
     * <code>codigo</code> da classe <code>Contrato</code>
     * Faz uso da operação <code>montarDadosConsulta</code> que realiza o trabalho de prerarar o List resultante.
     * @return  List Contendo vários objetos da classe <code>ContratoModalidadeVO</code> resultantes da consulta.
     * @exception Exception Caso haja problemas de conexão ou restrição de acesso.
     */
    public List consultarPorCodigoContrato(Integer valorConsulta, int nivelMontarDados) throws Exception {
        consultar(getIdEntidade(), true);
        String sqlStr = "SELECT ContratoModalidade.* FROM ContratoModalidade, Contrato WHERE ContratoModalidade.contrato = Contrato.codigo and Contrato.codigo = " + valorConsulta.intValue() + " ORDER BY Contrato.codigo";
        Statement stm = con.createStatement();
        ResultSet tabelaResultado = stm.executeQuery(sqlStr);
        return montarDadosConsulta(tabelaResultado, nivelMontarDados, this.con);
    }

    /**
     * Responsável por realizar uma consulta de <code>ContratoModalidade</code> através do valor do atributo 
     * <code>Integer codigo</code>. Retorna os objetos com valores iguais ou superiores ao parâmetro fornecido.
     * Faz uso da operação <code>montarDadosConsulta</code> que realiza o trabalho de prerarar o List resultante.
     * @param   controlarAcesso Indica se a aplicação deverá verificar se o usuário possui permissão para esta consulta ou não.
     * @return  List Contendo vários objetos da classe <code>ContratoModalidadeVO</code> resultantes da consulta.
     * @exception Exception Caso haja problemas de conexão ou restrição de acesso.
     */
    public List consultarPorCodigo(Integer valorConsulta, boolean controlarAcesso, int nivelMontarDados) throws Exception {
        consultar(getIdEntidade(), controlarAcesso);
        String sqlStr = "SELECT * FROM ContratoModalidade WHERE codigo >= " + valorConsulta.intValue() + " ORDER BY codigo";
        Statement stm = con.createStatement();
        ResultSet tabelaResultado = stm.executeQuery(sqlStr);
        return (montarDadosConsulta(tabelaResultado, nivelMontarDados, this.con));
    }

    /**
     * Responsável por montar os dados de vários objetos, resultantes de uma consulta ao banco de dados (<code>ResultSet</code>).
     * Faz uso da operação <code>montarDados</code> que realiza o trabalho para um objeto por vez.
     * @return  List Contendo vários objetos da classe <code>ContratoModalidadeVO</code> resultantes da consulta.
     */
    public static List montarDadosConsulta(ResultSet tabelaResultado, int nivelMontarDados, Connection con) throws Exception {
        List vetResultado = new ArrayList();
        while (tabelaResultado.next()) {
            ContratoModalidadeVO obj = montarDados(tabelaResultado, nivelMontarDados, con);
            vetResultado.add(obj);
        }
        return vetResultado;
    }

    public static ContratoModalidadeVO montarDadosBasico(ResultSet dadosSQL) throws Exception {
       ContratoModalidadeVO obj = new ContratoModalidadeVO();
       obj.setNovoObj(false);
        obj.setCodigo(new Integer(dadosSQL.getInt("codigo")));
        obj.setContrato(new Integer(dadosSQL.getInt("contrato")));
        obj.getModalidade().setCodigo(new Integer(dadosSQL.getInt("modalidade")));
        obj.setValorFinalModalidade(new Double(dadosSQL.getDouble("valorFinalModalidade")));
        obj.setValorModalidade(new Double(dadosSQL.getDouble("valorModalidade")));
        obj.setNrVezesSemana(new Integer(dadosSQL.getInt("vezessemana")));
       return obj;
    }

    /**
     * Responsável por montar os dados resultantes de uma consulta ao banco de dados (<code>ResultSet</code>)
     * em um objeto da classe <code>ContratoModalidadeVO</code>.
     * @return  O objeto da classe <code>ContratoModalidadeVO</code> com os dados devidamente montados.
     */
    public static ContratoModalidadeVO montarDados(ResultSet dadosSQL, int nivelMontarDados, Connection con) throws Exception {
        ContratoModalidadeVO obj = montarDadosBasico(dadosSQL);

        if (nivelMontarDados == Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA) {
            return obj;
        }

        if (nivelMontarDados == Uteis.NIVELMONTARDADOS_CAIXAPARAOPERADOR) {
            montarDadosModalidade(obj, Uteis.NIVELMONTARDADOS_MINIMOS, con);
            return obj;
        }

        if (nivelMontarDados == Uteis.NIVELMONTARDADOS_ROBO) {
            montarDadosModalidade(obj, Uteis.NIVELMONTARDADOS_TELACONSULTA, con);
            return obj;
        }
        if (nivelMontarDados == Uteis.NIVELMONTARDADOS_DADOSBASICOS) {
            return obj;
        }
        ContratoModalidadeTurma contratoModalidadeTurma = new ContratoModalidadeTurma(con);
        obj.setContratoModalidadeTurmaVOs(contratoModalidadeTurma.consultarContratoModalidadeTurmas(obj.getCodigo(), Uteis.NIVELMONTARDADOS_TODOS));
        ContratoModalidadeProdutoSugerido contratoModalidadeProdutoSugerido = new ContratoModalidadeProdutoSugerido(con);
        obj.setContratoModalidadeProdutoSugeridoVOs(contratoModalidadeProdutoSugerido.consultarContratoModalidadeProdutoSugeridos(obj.getCodigo(), Uteis.NIVELMONTARDADOS_TODOS));
        contratoModalidadeProdutoSugerido = null;
        montarDadosContratoModalidadeVezesSemana(obj, con);
        montarDadosModalidade(obj, Uteis.NIVELMONTARDADOS_TODOS, con);
        obj.gerarPlanoModalidadeApartirContratoModalidadeVezesSemana();
        return obj;
    }

    /**
     * Operação responsável por montar os dados de um objeto da classe <code>ModalidadeVO</code> relacionado ao objeto <code>ContratoModalidadeVO</code>.
     * Faz uso da chave primária da classe <code>ModalidadeVO</code> para realizar a consulta.
     * @param obj  Objeto no qual será montado os dados consultados.
     */
    public static void montarDadosModalidade(ContratoModalidadeVO obj, int nivelMontarDados, Connection con) throws Exception {
        if (obj.getModalidade().getCodigo().intValue() == 0) {
            obj.setModalidade(new ModalidadeVO());
            return;
        }
        Modalidade modalidade = new Modalidade(con);
        obj.setModalidade(modalidade.consultarPorChavePrimaria(obj.getModalidade().getCodigo(), nivelMontarDados));
        modalidade = null;
        obj.getModalidade().setModalidadeEscolhida(true);


    }

    /**
     * Operação responsável por montar os dados de um objeto da classe <code>ModalidadeVO</code> relacionado ao objeto <code>ContratoModalidadeVO</code>.
     * Faz uso da chave primária da classe <code>ModalidadeVO</code> para realizar a consulta.
     * @param obj  Objeto no qual será montado os dados consultados.
     */
    public static void montarDadosContratoModalidadeVezesSemana(ContratoModalidadeVO obj, Connection con) throws Exception {
        if (obj.getContratoModalidadeVezesSemanaVO().getCodigo().intValue() == 0 && (obj.getNrVezesSemana() == null || obj.getNrVezesSemana() == 0) ) {
            obj.setContratoModalidadeVezesSemanaVO(new ContratoModalidadeVezesSemanaVO());
            return;
        }
        obj.setContratoModalidadeVezesSemanaVO(new ContratoModalidadeVezesSemana(con).consultarPorNrVezeSemana(obj, Uteis.NIVELMONTARDADOS_DADOSBASICOS));
        obj.getContratoModalidadeVezesSemanaVO().setVezeSemanaEscolhida(true);

    }

    /**
     * Operação responsável por excluir todos os objetos da <code>ContratoModalidadeVO</code> no BD.
     * Faz uso da operação <code>excluir</code> disponível na classe <code>ContratoModalidade</code>.
     * @param contrato campo chave para exclusão dos objetos no BD.
     * @exception Exception  Erro de conexão com o BD ou restrição de acesso a esta operação.
     */
    public void excluirContratoModalidades(Integer contrato) throws Exception {
        // ContratoModalidade.excluir(getIdEntidade());
        String sql = "DELETE FROM ContratoModalidade WHERE (contrato = ?)";
        PreparedStatement sqlExcluir = con.prepareStatement(sql);
        sqlExcluir.setInt(1, contrato.intValue());
        sqlExcluir.execute();
    }

    public void excluir(Integer contratoPrm, List objetos) throws Exception {
        Iterator e = objetos.iterator();
        while (e.hasNext()) {
            ContratoModalidadeVO obj = (ContratoModalidadeVO) e.next();
            if (obj.getModalidade().getModalidadeEscolhida()) {
                obj.setContrato(contratoPrm);
                excluirContratoModalidadesComDependentes(obj);
            }
        }
        excluirContratoModalidades(contratoPrm);
    }

    public void excluirContratoModalidade(Integer contrato, Integer modalidade) throws Exception {
        String sql = "DELETE FROM contratomodalidade " +
                "WHERE contrato = ? AND modalidade = ?;";

        PreparedStatement preparedStatement = con.prepareStatement(sql);
        preparedStatement.setInt(1, contrato);
        preparedStatement.setInt(2, modalidade);
        preparedStatement.execute();
    }


    public void excluirContratoModalidadesComDependentes(ContratoModalidadeVO contrato) throws Exception {
        Iterator j = contrato.getContratoModalidadeTurmaVOs().iterator();
        while (j.hasNext()) {
            ContratoModalidadeTurmaVO contratoModalidadeTurma = (ContratoModalidadeTurmaVO) j.next();
            getFacade().getContratoModalidadeHorarioTurma().excluirContratoModalidadeHorarioTurmas(contratoModalidadeTurma.getCodigo());
        }
        getFacade().getContratoModalidadeTurma().excluirContratoModalidadeTurma(contrato.getCodigo().intValue());
        getFacade().getContratoModalidadeProdutoSugerido().excluirContratoModalidadeProdutoSugerido(contrato.getCodigo().intValue());
        getFacade().getContratoModalidadeVezesSemana().excluirContratoModalidadeVezesSemana(contrato.getCodigo().intValue());
        //excluirContratoModalidades(contrato.getContrato());
    }

    /**
     * Operação responsável por alterar todos os objetos da <code>ContratoModalidadeVO</code> contidos em um Hashtable no BD.
     * Faz uso da operação <code>excluirContratoModalidades</code> e <code>incluirContratoModalidades</code> disponíveis na classe <code>ContratoModalidade</code>.
     * @param objetos  List com os objetos a serem alterados ou incluídos no BD.
     * @exception Exception  Erro de conexão com o BD ou restrição de acesso a esta operação.
     */
    public void alterarContratoModalidades(Integer contrato, List objetos) throws Exception {
        Iterator i = objetos.iterator();
        while (i.hasNext()) {
            ContratoModalidadeVO contratoModalidade = (ContratoModalidadeVO) i.next();
            Iterator j = contratoModalidade.getContratoModalidadeTurmaVOs().iterator();
            while (j.hasNext()) {
                ContratoModalidadeTurmaVO contratoModalidadeTurma = (ContratoModalidadeTurmaVO) j.next();
                getFacade().getContratoModalidadeHorarioTurma().excluirContratoModalidadeHorarioTurmas(contratoModalidadeTurma.getCodigo());
            }
            getFacade().getContratoModalidadeTurma().excluirContratoModalidadeTurma(contratoModalidade.getCodigo().intValue());
            getFacade().getContratoModalidadeProdutoSugerido().excluirContratoModalidadeProdutoSugerido(contratoModalidade.getCodigo().intValue());
            getFacade().getContratoModalidadeVezesSemana().excluirContratoModalidadeVezesSemana(contratoModalidade.getCodigo().intValue());
        }
        excluirContratoModalidades(contrato);
        getFacade().getZWFacade().incluirContratoModalidades(contrato, objetos);
    }

    /**
     * Operação responsável por consultar todos os <code>ContratoModalidadeVO</code> relacionados a um objeto da classe <code>contrato.Contrato</code>.
     * @param contrato  Atributo de <code>contrato.Contrato</code> a ser utilizado para localizar os objetos da classe <code>ContratoModalidadeVO</code>.
     * @return List  Contendo todos os objetos da classe <code>ContratoModalidadeVO</code> resultantes da consulta.
     * @exception Exception  Erro de conexão com o BD ou restrição de acesso a esta operação.
     */
    public List<ContratoModalidadeVO> consultarContratoModalidades(Integer contrato, Boolean controleAcesso, int nivelMontarDados) throws Exception {
        consultar(getIdEntidade(), controleAcesso);
        List<ContratoModalidadeVO> objetos = new ArrayList<ContratoModalidadeVO>();
        String sql = "SELECT * FROM ContratoModalidade WHERE contrato = ?";
        PreparedStatement sqlConsulta = con.prepareStatement(sql);
        sqlConsulta.setInt(1, contrato);
        ResultSet resultado = sqlConsulta.executeQuery();
        while (resultado.next()) {
            ContratoModalidadeVO novoObj = ContratoModalidade.montarDados(resultado, nivelMontarDados, this.con);
            objetos.add(novoObj);
        }
        Ordenacao.ordenarLista(objetos, "nomeModalidade_Apresentar");
        return objetos;
    }

    public ContratoModalidadeVO consultarPorChavePrimaria(Integer codigoPrm, int nivelMontarDados) throws Exception {
        consultar(getIdEntidade(), false);
        String sql = "SELECT * FROM ContratoModalidade WHERE codigo = ?";
        PreparedStatement sqlConsultar = con.prepareStatement(sql);
        sqlConsultar.setInt(1, codigoPrm.intValue());
        ResultSet tabelaResultado = sqlConsultar.executeQuery();
        if (!tabelaResultado.next()) {
            throw new ConsistirException("Dados Não Encontrados ( ContratoModalidade ).");
        }
        return (montarDados(tabelaResultado, nivelMontarDados, this.con));
    }

    public void manutencaoModalidade(List<ContratoModalidadeVO> listaModalidadeAdicionada,
                                     List<ContratoModalidadeVO> listaModalidadeExcluida,
                                     List<ContratoModalidadeVO> listaModalidadeAlterada, 
                                     ContratoVO contratoNovo, ContratoVO contratoAtingo, UsuarioVO usuario) throws Exception {
    	contratoNovo.setEmpresa(contratoAtingo.getEmpresa());
        adicionarMatricula(listaModalidadeAdicionada, contratoNovo);
        alterarMatricula(listaModalidadeAlterada, contratoNovo, contratoAtingo, usuario);
        excluirMatricula(listaModalidadeExcluida, contratoAtingo);
    }

    public void adicionarMatricula(List<ContratoModalidadeVO> listaModalidadeAdicionada, ContratoVO contrato) throws Exception {
        for (ContratoModalidadeVO modalidade : listaModalidadeAdicionada) {
            getFacade().getZWFacade().incluirContratoModalidade(modalidade);
            if(modalidade.getModalidade().getUtilizarTurma()) {
                Iterator i = modalidade.getContratoModalidadeTurmaVOs().iterator();
                while(i.hasNext()) {
                    ContratoModalidadeTurmaVO turma = (ContratoModalidadeTurmaVO)i.next();
                    Iterator j = turma.getContratoModalidadeHorarioTurmaVOs().iterator();
                    while(j.hasNext()) {
                        ContratoModalidadeHorarioTurmaVO horario = (ContratoModalidadeHorarioTurmaVO)j.next();
                        Date dataInicio = Calendario.hoje();
                        // se a data da manutencao for menor que a data de inicio da vigencia
                        // modifica a data da matricula para que seja igual a data de inicio do contrato
                        if(Uteis.getCompareData(contrato.getVigenciaDe(),dataInicio) >= 0)
                            dataInicio = contrato.getVigenciaDe();
                        MatriculaAlunoHorarioTurmaVO matricula = new MatriculaAlunoHorarioTurmaVO();
                        matricula.setEmpresa(contrato.getEmpresa().getCodigo());
                        matricula.setPessoa(contrato.getPessoa());
                        matricula.setContrato(contrato);
                        matricula.setDataInicio(dataInicio);
                        if (contrato.getVigenciaTurmaCreditoTreinoAte() != null) {
                            matricula.setDataFim(contrato.getVigenciaTurmaCreditoTreinoAte());
                        }else if (contrato.getContratoResponsavelRenovacaoMatricula() == null || contrato.getContratoResponsavelRenovacaoMatricula() == 0  ){
                        	matricula.setDataFim(Uteis.somarDias(contrato.getVigenciaAteAjustada(),contrato.getEmpresa().getToleranciaOcupacaoTurma()));
                        } else {
                			matricula.setDataFim(contrato.getVigenciaAteAjustada());
                        }
                        matricula.setHorarioTurma(horario.getHorarioTurma());
                        getFacade().getMatriculaAlunoHorarioTurma().incluirSemComit(matricula);
                    }
                }
            }
        }
    }

    public void alterarMatricula(List<ContratoModalidadeVO> listaModalidadeAlterada, ContratoVO contratoNovo, ContratoVO contratoAntigo, UsuarioVO usuarioVO) throws Exception {
        if (contratoAntigo.isVendaCreditoTreino()){
            Integer saldo = ControleCreditoTreino.consultarSaldoCredito(con, contratoAntigo.getCodigo());
            Integer aulasDesmarcasPassadasARepor = getFacade().getAulaDesmarcada().contarAulasDesmarcadasPorPeriodo(contratoNovo.getCodigo(), 0,null,Calendario.hoje()); //aulas que já passaram e não foram repostas e não consumiram crédito
            contratoNovo.gerarVigenciaMatriculaPlanoCreditoTreino(Calendario.hoje(),(saldo - aulasDesmarcasPassadasARepor));
        }
        for (ContratoModalidadeVO modalidade : listaModalidadeAlterada) {
            for(ContratoModalidadeVO modalidadeAntiga : contratoAntigo.getContratoModalidadeVOs()){
                if(modalidadeAntiga.getModalidade().getCodigo().equals(modalidade.getModalidade().getCodigo())){
                    modalidade.setValorFinalModalidade(modalidadeAntiga.getValorFinalModalidade()); //mantém o valor mensal da época da venda
                }
            }
            excluir(modalidade);
            getFacade().getZWFacade().incluirContratoModalidade(modalidade);
            if(modalidade.getModalidade().getUtilizarTurma()) {
                for (Object obj : modalidade.getContratoModalidadeTurmaVOs()) {
                    ContratoModalidadeTurmaVO turma = (ContratoModalidadeTurmaVO) obj;
                    for (Object obj1 : turma.getContratoModalidadeHorarioTurmaVOs()) {
                        Date dataFim = null;
                        if (contratoAntigo.getContratoResponsavelRenovacaoMatricula() == null || contratoAntigo.getContratoResponsavelRenovacaoMatricula() == 0) {
                            dataFim = Uteis.somarDias(contratoNovo.getVigenciaAteAjustada(), contratoNovo.getEmpresa().getToleranciaOcupacaoTurma());
                        } else {
                            dataFim = (contratoNovo.getVigenciaAteAjustada());
                        }
                        ContratoModalidadeHorarioTurmaVO horario = (ContratoModalidadeHorarioTurmaVO) obj1;

                        MatriculaAlunoHorarioTurmaVO matricula = getFacade().getMatriculaAlunoHorarioTurma().consultarMatriculaAtivaPorHorarioTurma(
                                contratoNovo.getCodigo(),
                                horario.getHorarioTurma().getCodigo(),
                                Calendario.hoje(),
                                Uteis.NIVELMONTARDADOS_MINIMOS);
                        // se a turma foi escolhida
                        if (horario.getHorarioTurma().getHorarioTurmaEscolhida()) {
                            // verifica se não existe matricula
                            if (matricula == null || matricula.getCodigo() == 0) {
                                Date dataInicio = Calendario.hoje();
                                // se a data da manutencao for menor que a data de inicio da vigencia
                                // modifica a data da matricula para que seja igual a data de inicio do contrato
                                if (Uteis.getCompareData(contratoNovo.getVigenciaDe(), dataInicio) >= 0)
                                    dataInicio = contratoNovo.getVigenciaDe();
                                MatriculaAlunoHorarioTurmaVO mat = new MatriculaAlunoHorarioTurmaVO();
                                mat.setEmpresa(contratoNovo.getEmpresa().getCodigo());
                                mat.setPessoa(contratoNovo.getPessoa());
                                mat.setContrato(contratoNovo);
                                mat.setDataInicio(dataInicio);
                                if (contratoNovo.getVigenciaTurmaCreditoTreinoAte() != null){
                                    dataFim = contratoNovo.getVigenciaTurmaCreditoTreinoAte();
                                }
                                mat.setDataFim(dataFim);
                                mat.setHorarioTurma(horario.getHorarioTurma());
                                getFacade().getMatriculaAlunoHorarioTurma().incluirSemComit(mat);
                                getFacade().getReposicao().excluirReposicoesFuturasContratoHorarioTurma(contratoNovo.getCodigo(), horario.getHorarioTurma().getCodigo(), Calendario.hoje()); //isso evita do aluno ocupar duas vagas na turma.
                            } else { // matricula já existe
                                if (contratoNovo.getVigenciaTurmaCreditoTreinoAte() != null){  // se for crédito, a data final e recalculada e pode ser diferente, então deve ser alterada
                                    matricula.setDataFim(contratoNovo.getVigenciaTurmaCreditoTreinoAte());
                                    getFacade().getMatriculaAlunoHorarioTurma().alterarSemCommit(matricula);
                                    getFacade().getAulaDesmarcada().excluirAulasDesmarcadasFuturasSemReposicao(contratoNovo, horario.getHorarioTurma().getCodigo(), contratoNovo.getVigenciaTurmaCreditoTreinoAte(), usuarioVO);
                                }
                            }
                            // se a turma nao foi escolhida
                        } else {
                            // verifica se não existe matricula
                            if (matricula != null && matricula.getCodigo() != 0) {
                                // se a data da manutencao for menor que a data de inicio da vigencia
                                // ou se a data de inicio do horario for igual a hoje apenas exclui
                                if (Uteis.getCompareData(contratoNovo.getVigenciaDe(), Calendario.hoje()) >= 0 ||
                                        Uteis.getCompareData(matricula.getDataInicio(), Calendario.hoje()) == 0)
                                    getFacade().getMatriculaAlunoHorarioTurma().excluirSemCommit(matricula);
                                else {
                                    matricula.setDataFim(Uteis.obterDataAnterior(Calendario.hoje(), 1));
                                    getFacade().getMatriculaAlunoHorarioTurma().alterarSemCommit(matricula);
                                }
                                getFacade().getAulaDesmarcada().excluirAulasDesmarcadasFuturasSemReposicao(contratoNovo, horario.getHorarioTurma().getCodigo(), Calendario.hoje(), usuarioVO);
                            }
                        }
                    }
                }
            }
        }
    }


    public void excluirMatricula(List<ContratoModalidadeVO> listaModalidadeExcluida, ContratoVO contratoAntigo) throws Exception {
        for (ContratoModalidadeVO modalidade : listaModalidadeExcluida) {
            excluir(modalidade);
            Iterator i = modalidade.getContratoModalidadeTurmaVOs().iterator();
            while(i.hasNext()) {
                ContratoModalidadeTurmaVO turma = (ContratoModalidadeTurmaVO)i.next();
                Iterator j = turma.getContratoModalidadeHorarioTurmaVOs().iterator();
                while(j.hasNext()) {
                    ContratoModalidadeHorarioTurmaVO horario = (ContratoModalidadeHorarioTurmaVO)j.next();
                    MatriculaAlunoHorarioTurmaVO matricula = getFacade().getMatriculaAlunoHorarioTurma().consultarMatriculaAtivaPorHorarioTurma(contratoAntigo.getCodigo(),
                                                                                                        horario.getHorarioTurma().getCodigo(),
                                                                                                        Calendario.hoje(),
                                                                                                        Uteis.NIVELMONTARDADOS_MINIMOS);
                   // verifica se não existe matricula
                    if(matricula == null || matricula.getCodigo().intValue() == 0) {
                        // nao faz nada
                    } else {
                        // se a data da manutencao for menor ou igual a data de inicio da vigencia
                        // ou se a data de inicio do horario for igual a hoje apenas exclui
                        if(Uteis.getCompareData(contratoAntigo.getVigenciaDe(), Calendario.hoje()) >= 0 ||
                                Uteis.getCompareData(matricula.getDataInicio(), Calendario.hoje()) == 0)
                            getFacade().getMatriculaAlunoHorarioTurma().excluirSemCommit(matricula);
                        else {
                            matricula.setDataFim(Uteis.obterDataAnterior(Calendario.hoje(), 1));
                            getFacade().getMatriculaAlunoHorarioTurma().alterarSemCommit(matricula);
                        }
                    }
                }
            }
        }
    }

    public List<ContratoModalidadeVO> consultar(String sql, int nivelMontarDados) throws Exception{
        ResultSet resultDados = SuperFacadeJDBC.criarConsulta(sql, con);
        return montarDadosConsulta(resultDados, nivelMontarDados, con);
    }

    public void incluirContratoModalidades(Integer contratoPrm, List objetos) throws Exception {
        Iterator e = objetos.iterator();
        while (e.hasNext()) {
            ContratoModalidadeVO obj = (ContratoModalidadeVO) e.next();
            if (obj.getModalidade().getModalidadeEscolhida()) {
                obj.setContrato(contratoPrm);
                incluirContratoModalidade(obj);
            }

        }
    }

    public List<ClienteVO> contarEvasaoPorModalidadeNoPeriodo(Date dataInicio, Date dataFim, ModalidadeVO modalidadeVO, EmpresaVO empresaVO) throws Exception {
        String sql = "SELECT distinct c.pessoa FROM historicocontrato hc\n" +
                "        INNER JOIN contratomodalidade cm ON hc.contrato = cm.contrato\n" +
                "        INNER JOIN contrato c ON cm.contrato = c.codigo\n" +
                "        WHERE tipohistorico IN ('CA', 'DE')\n" +
                "        AND modalidade = ?\n" +
                "        AND datainiciosituacao BETWEEN ? AND ?\n" +
                "        AND c.empresa = ?";

        Map<Integer, ClienteVO> mapClientes = new HashMap<>();
        Cliente clienteDao = new Cliente(this.con);
        try (PreparedStatement sqlConsultar = con.prepareStatement(sql)) {
            sqlConsultar.setInt(1, modalidadeVO.getCodigo());
            sqlConsultar.setTimestamp(2, Uteis.getDataHoraJDBC(dataInicio, "00:00:00"));
            sqlConsultar.setTimestamp(3, Uteis.getDataHoraJDBC(dataFim, "00:00:00"));
            sqlConsultar.setInt(4, empresaVO.getCodigo());
            try (ResultSet rs = sqlConsultar.executeQuery()) {
                while (rs.next()) {
                    Integer codPessoa = rs.getInt("pessoa");
                    ClienteVO clienteVO = mapClientes.get(codPessoa);
                    if (clienteVO == null) {
                        clienteVO = clienteDao.consultarPorCodigoPessoa(codPessoa, Uteis.NIVELMONTARDADOS_RELATORIO_SGP);
                        mapClientes.put(codPessoa, clienteVO);
                    }
                }
            }
        }
        return new ArrayList<>(mapClientes.values());
    }


    public boolean existeContratoAtivoComEssaModalidade(Integer matricula, Date dia,Integer modalidade, Integer tipoModalidade) throws Exception {

        StringBuilder sql = new StringBuilder("select c.codigo from contrato c inner join cliente cl on cl.pessoa = c.pessoa \n");
        sql.append("inner join contratomodalidade cm on cm.contrato = c.codigo inner join modalidade m on cm.modalidade = m.codigo\n");
        sql.append("where cl.codigomatricula = ").append(matricula).append(" and  '").append(Uteis.getDataFormatoBD(dia)).append("' between c.vigenciade and c.vigenciaateajustada");
        if (UteisValidacao.notEmptyNumber(tipoModalidade)) {
            sql.append(" and m.tipo = ").append(tipoModalidade);
        } else {
            sql.append(" and m.codigo = ").append(modalidade);
        }

        return existe(sql.toString(), con);
    }


}

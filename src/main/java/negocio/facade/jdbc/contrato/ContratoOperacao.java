package negocio.facade.jdbc.contrato;

import br.com.pactosolucoes.comuns.util.JSFUtilities;
import br.com.pactosolucoes.enumeradores.OrigemSistemaEnum;
import br.com.pactosolucoes.enumeradores.TemporalRemocaoPontoEnum;
import br.com.pactosolucoes.enumeradores.TipoHorarioCreditoTreinoEnum;
import br.com.pactosolucoes.enumeradores.TipoOperacaoContratoEnum;
import br.com.pactosolucoes.enumeradores.TipoOperacaoCreditoTreinoEnum;
import controle.basico.VinculoControle;
import negocio.comuns.arquitetura.LogVO;
import negocio.comuns.arquitetura.RoboVO;
import negocio.comuns.arquitetura.UsuarioVO;
import negocio.comuns.basico.ClienteVO;
import negocio.comuns.basico.ColaboradorVO;
import negocio.comuns.basico.ContratoDependenteVO;
import negocio.comuns.basico.EmpresaVO;
import negocio.comuns.basico.MovimentoContaCorrenteClienteVO;
import negocio.comuns.basico.VinculoVO;
import negocio.comuns.contrato.AfastamentoContratoDependenteVO;
import negocio.comuns.contrato.AtestadoContratoVO;
import negocio.comuns.contrato.BonusContratoVO;
import negocio.comuns.contrato.CancelamentoContratoVO;
import negocio.comuns.contrato.CarenciaContratoVO;
import negocio.comuns.contrato.ContratoDuracaoCreditoTreinoVO;
import negocio.comuns.contrato.ContratoModalidadeVO;
import negocio.comuns.contrato.ContratoOperacaoVO;
import negocio.comuns.contrato.ContratoVO;
import negocio.comuns.contrato.ControleCreditoTreinoVO;
import negocio.comuns.contrato.DadosContratoOperacaoWS;
import negocio.comuns.contrato.DadosRetornoOperacaoWS;
import negocio.comuns.contrato.HistoricoContratoVO;
import negocio.comuns.contrato.JustificativaOperacaoVO;
import negocio.comuns.contrato.JustificativaOperacaoWS;
import negocio.comuns.contrato.MatriculaAlunoHorarioTurmaVO;
import negocio.comuns.contrato.MovProdutoVO;
import negocio.comuns.contrato.PeriodoAcessoClienteVO;
import negocio.comuns.contrato.TrancamentoContratoVO;
import negocio.comuns.contrato.ValidacaoContratoOperacao;
import negocio.comuns.contrato.ValidacaoHistoricoContrato;
import negocio.comuns.financeiro.*;
import negocio.comuns.plano.HorarioTurmaVO;
import negocio.comuns.plano.ProdutoVO;
import negocio.comuns.utilitarias.*;
import negocio.facade.jdbc.arquitetura.SuperEntidade;
import negocio.facade.jdbc.arquitetura.SuperFacadeJDBC;
import negocio.facade.jdbc.arquitetura.Usuario;
import negocio.facade.jdbc.arquitetura.ZillyonWebFacade;
import negocio.facade.jdbc.basico.AulaDesmarcada;
import negocio.facade.jdbc.basico.Cliente;
import negocio.facade.jdbc.basico.ConfiguracaoSistema;
import negocio.facade.jdbc.basico.ContratoDependente;
import negocio.facade.jdbc.basico.Empresa;
import negocio.facade.jdbc.basico.Vinculo;
import negocio.facade.jdbc.financeiro.Boleto;
import negocio.facade.jdbc.financeiro.MovParcela;
import negocio.facade.jdbc.financeiro.PagamentoMovParcela;
import negocio.facade.jdbc.financeiro.ReciboDevolucao;
import negocio.facade.jdbc.plano.Produto;
import negocio.interfaces.contrato.ContratoOperacaoInterfaceFacade;
import org.json.JSONArray;
import relatorio.negocio.comuns.basico.PendenciaResumoPessoaRelVO;
import relatorio.negocio.comuns.sad.SituacaoClienteSinteticoEnum;
import servicos.impl.gestaoaula.GestaoAulaService;
import servicos.propriedades.PropsService;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.sql.Statement;
import java.sql.Types;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Collections;
import java.util.Date;
import java.util.Iterator;
import java.util.List;

/**
 * Classe de persistência que encapsula todas as operações de manipulação dos dados da classe <code>ContratoOperacaoVO</code>.
 * Responsável por implementar operações como incluir, alterar, excluir e consultar pertinentes a classe <code>ContratoOperacaoVO</code>.
 * Encapsula toda a interação com o banco de dados.
 * @see ContratoOperacaoVO
 * @see SuperEntidade
 */
public class ContratoOperacao extends SuperEntidade implements ContratoOperacaoInterfaceFacade {

    protected static String idEntidade;

    public ContratoOperacao() throws Exception {
        super();
        setIdEntidade("Contrato");
    }

    public ContratoOperacao(Connection con) throws Exception {
        super(con);
        setIdEntidade("Contrato");
    }

    /**
     * Operação responsável por retornar um novo objeto da classe <code>ContratoOperacaoVO</code>.
     */
    public ContratoOperacaoVO novo() throws Exception {
        incluir(getIdEntidade());
        return new ContratoOperacaoVO();
    }

    /**
     * Operação responsável por incluir no banco de dados um objeto da classe <code>ContratoOperacaoVO</code>.
     * Primeiramente valida os dados (<code>validarDados</code>) do objeto. Verifica a conexão com o banco de dados e a permissão do usuário
     * para realizar esta operacão na entidade.
     * Isto, através da operação <code>incluir</code> da superclasse.
     * @param obj  Objeto da classe <code>ContratoOperacaoVO</code> que será gravado no banco de dados.
     * @exception Exception Caso haja problemas de conexão, restrição de acesso ou validação de dados.
     */
    public void incluir(ContratoOperacaoVO obj) throws Exception {
        try {
            con.setAutoCommit(false);
            incluirSemCommit(obj, true);
            con.commit();
        } catch (Exception e) {
            obj.setNovoObj(true);
            con.rollback();
            con.setAutoCommit(true);
            throw e;
        } finally {
            con.setAutoCommit(true);
        }
    }

    public void incluir(ContratoOperacaoVO obj, Boolean controleAcesso) throws Exception {
        try {
            con.setAutoCommit(false);
            incluirSemCommit(obj, controleAcesso);
            con.commit();
        } catch (Exception e) {
            obj.setNovoObj(true);
            con.rollback();
            con.setAutoCommit(true);
            throw e;
        } finally {
            con.setAutoCommit(true);
        }
    }

    /**
     * Operação responsável por incluir no banco de dados um objeto da classe <code>ContratoOperacaoVO</code>.
     * Primeiramente valida os dados (<code>validarDados</code>) do objeto. Verifica a conexão com o banco de dados e a permissão do usuário
     * para realizar esta operacão na entidade.
     * Isto, através da operação <code>incluir</code> da superclasse.
     * @param obj  Objeto da classe <code>ContratoOperacaoVO</code> que será gravado no banco de dados.
     * @exception Exception Caso haja problemas de conexão, restrição de acesso ou validação de dados.
     */
    public void incluirSemCommit(ContratoOperacaoVO obj, Boolean controleAcesso) throws Exception {
        ContratoOperacaoVO.validarDados(obj);
        if (controleAcesso) {
            incluir(getIdEntidade());
        }
        obj.realizarUpperCaseDados();
        String sql = "INSERT INTO ContratoOperacao( contrato, tipoOperacao, "
                + "operacaoPaga, dataOperacao, dataInicioEfetivacaoOperacao, "
                + "dataFimEfetivacaoOperacao, responsavel, observacao, "
                + "descricaoCalculo, tipoJustificativa, responsavelLiberacao,"
                + "clienteTransfereDias,clienteRecebeDias, valor, nrdiasoperacao, informacoes,chavearquivo, origemSistema, informacoesDesfazer) "
                + "VALUES ( ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)";
        try (PreparedStatement sqlInserir = con.prepareStatement(sql)) {
            sqlInserir.setInt(1, obj.getContrato());
            sqlInserir.setString(2, obj.getTipoOperacao());
            sqlInserir.setBoolean(3, obj.isOperacaoPaga());
            sqlInserir.setTimestamp(4, Uteis.getDataJDBCTimestamp(obj.getDataOperacao()));
            sqlInserir.setDate(5, Uteis.getDataJDBC(obj.getDataInicioEfetivacaoOperacao()));
            sqlInserir.setDate(6, Uteis.getDataJDBC(obj.getDataFimEfetivacaoOperacao()));
            if (obj.getResponsavel().getCodigo() != 0) {
                sqlInserir.setInt(7, obj.getResponsavel().getCodigo());
            } else {
                sqlInserir.setNull(7, 0);
            }
            sqlInserir.setString(8, obj.getObservacao());
            sqlInserir.setString(9, obj.getDescricaoCalculo());
            if (obj.getTipoJustificativa().getCodigo() != 0) {
                sqlInserir.setInt(10, obj.getTipoJustificativa().getCodigo());
            } else {
                sqlInserir.setNull(10, 0);
            }
            if (obj.getResponsavelLiberacao().getCodigo() != 0) {
                sqlInserir.setInt(11, obj.getResponsavelLiberacao().getCodigo());
            } else {
                sqlInserir.setNull(11, 0);
            }
            if (obj.getClienteTransfereDias().getCodigo() != 0) {
                sqlInserir.setInt(12, obj.getClienteTransfereDias().getCodigo());
            } else {
                sqlInserir.setNull(12, 0);
            }
            if (obj.getClienteRecebeDias().getCodigo() != 0) {
                sqlInserir.setInt(13, obj.getClienteRecebeDias().getCodigo());
            } else {
                sqlInserir.setNull(13, 0);
            }
            if (UteisValidacao.emptyNumber(obj.getValor())) {
                sqlInserir.setNull(14, 0);
            } else {
                sqlInserir.setDouble(14, obj.getValor());
            }
            if (obj.getNrDiasOperacao() != 0) {
                sqlInserir.setInt(15, obj.getNrDiasOperacao());
            } else {
                sqlInserir.setNull(15, 0);
            }
            sqlInserir.setString(16, obj.getInformacoes());
            if (!UteisValidacao.emptyString(obj.getChaveArquivo())) {
                sqlInserir.setString(17, obj.getChaveArquivo());
            } else {
                sqlInserir.setNull(17, Types.VARCHAR);
            }
            sqlInserir.setInt(18, obj.getOrigemSistema().getCodigo());
            sqlInserir.setString(19, obj.getInformacoesDesfazer());

            sqlInserir.execute();
        }
        obj.setCodigo(obterValorChavePrimariaCodigo());
        obj.setNovoObj(false);
    }

    public void incluirOperacaoCancelamentoContratoTransferencia(
            CancelamentoContratoVO cancelamentoContratoVO,
            ContratoVO contrato, UsuarioVO usuario) throws Exception {
        try {
            con.setAutoCommit(false);
            getFacade().getContratoOperacao().retirarVinculoTWCancelamento(contrato.getPessoa().getCodigo(), contrato.getEmpresa().isRemoverVinculosAposDesistencia());
            alterarMatriculasSemOcupacao(contrato, contrato.getVigenciaAteAjustada(), Uteis.obterDataAnterior(cancelamentoContratoVO.getDataCancelamento(), 1));
            if (Uteis.getCompareData(cancelamentoContratoVO.getDataCancelamento(), negocio.comuns.utilitarias.Calendario.hoje()) <= 0) {
                ClienteVO cliente = getFacade().getCliente().consultarPorCodigoPessoa(contrato.getPessoa().getCodigo(), Uteis.NIVELMONTARDADOS_DADOSENTIDADESPRINCIPAIS);
                String situacao = getFacade().getZWFacade().obterSituacaoClienteAtivo(contrato, cliente);
                if (situacao.equals("")) {
                    situacao = getFacade().getZWFacade().obterSituacaoClienteTrancado(contrato, cliente);
                }
                cliente.setSituacao("IN");
                getFacade().getCliente().alterarSituacaoClienteSemPessoaCommit(cliente);
                if (contrato.getEmpresa().getZerarPontosAposVencimento() == TemporalRemocaoPontoEnum.QUANDO_CANCELADO_DESISTENTE ||
                        contrato.getEmpresa().getZerarPontosAposVencimento() == TemporalRemocaoPontoEnum.QUANDO_CANCELADO_VENCIDO)
                    getFacade().getHistoricoPontos().zerarPontuacaoRobo(contrato.getPessoa());
                contrato.setSituacao("CA");
                contrato.setVigenciaAteAjustada(negocio.comuns.utilitarias.Calendario.hoje());
                contrato.setDataPrevistaRenovar(negocio.comuns.utilitarias.Calendario.hoje());
                contrato.setDataPrevistaRematricula(negocio.comuns.utilitarias.Calendario.hoje());
                getFacade().getContrato().alterarSituacaoContrato(contrato);
                getFacade().getContrato().alterarDatasVigenciaContrato(contrato);


            }
            getFacade().getZWFacade().inicializarDadosOperacaoContrato(cancelamentoContratoVO, contrato, false, new JSONArray());
            getFacade().getZWFacade().inicializarDadosHistoricoContrato(cancelamentoContratoVO, contrato);
            getFacade().getZWFacade().alterarSituacaoContrato(cancelamentoContratoVO, contrato);
            getFacade().getZWFacade().inicializarDadosPeriodoAcessoCliente(cancelamentoContratoVO, contrato,false);
            getFacade().getZWFacade().gravarPeriodoAcesso(contrato, cancelamentoContratoVO);

            if (cancelamentoContratoVO.getListaContratos().size() > 1) {
                List listaParcela = getFacade().getZWFacade().cancelarPacelasContratoRenovados(contrato, cancelamentoContratoVO);
                Iterator i = listaParcela.iterator();
                while (i.hasNext()) {
                    MovParcelaVO parcela = (MovParcelaVO) i.next();
                    parcela.setSituacao("CA");
                    getFacade().getMovParcela().alterarSemCommit(parcela);
                }
                List listaMovProduto = getFacade().getZWFacade().cancelarMovProdutoContratoRenovados(contrato, cancelamentoContratoVO);
                Iterator j = listaMovProduto.iterator();
                while (j.hasNext()) {
                    MovProdutoVO movProduto = (MovProdutoVO) j.next();
                    movProduto.setQuitado(false);
                    movProduto.setSituacao("CA");
                    getFacade().getMovProduto().alterarSemCommit(movProduto);
                }
                for (ContratoVO contratoRenovado : cancelamentoContratoVO.getListaContratos()) {
                    if (!contrato.getCodigo().equals(contratoRenovado.getCodigo().intValue())) {
                        getFacade().getZWFacade().excluirPeriodoAcessoClienteContratoRenovados(contratoRenovado);
                    }
                }
            }

            if(contrato.getBolsa()){
                MovProduto movProduto  = new MovProduto();
                movProduto.cancelarMovProdutoFuturos(contrato);
                ClienteVO cliente = getFacade().getCliente().consultarPorCodigoPessoa(contrato.getPessoa().getCodigo(), Uteis.NIVELMONTARDADOS_SITUACAOCLIENTESINTETICODW);
                cliente.setDeveAtualizarDependentesSintetico(true);
                getFacade().getZWFacade().atualizarSintetico(cliente, Calendario.hoje(), SituacaoClienteSinteticoEnum.GRUPO_CONTRATO, false);
            } else {
                double valorCredito = Uteis.arredondarForcando2CasasDecimais(cancelamentoContratoVO.getAlteracaoValorTrans() ? cancelamentoContratoVO.getValorASerDevolvido() : cancelamentoContratoVO.getValorASerDevolvidoBaseCalculo());
                List<MovPagamentoVO> listaMovPagamento = new ArrayList<>();
                if (UtilReflection.objetoMaiorQueZero(cancelamentoContratoVO, "getContratoTransferenciaSaldo().getCodigo()")) {
                    valorCredito = valorCredito - cancelamentoContratoVO.getValorDevolverSaldoTransferenciaCredito();
                    // gerar credito conta corrente referente ao contrato atual.
                    listaMovPagamento.addAll(gerarmovimentocontacorrente(valorCredito, cancelamentoContratoVO, contrato, usuario, false));
                    // gerar credito conta corrente referente Ã transferencia de saldo do contrato renovado.
                    listaMovPagamento.addAll(gerarmovimentocontacorrente(cancelamentoContratoVO.getValorDevolverSaldoTransferenciaCredito(), cancelamentoContratoVO, cancelamentoContratoVO.getContratoTransferenciaSaldo(), usuario, false));
                } else {
                    listaMovPagamento.addAll(gerarmovimentocontacorrente(valorCredito, cancelamentoContratoVO, contrato, usuario, false));
                }
                //cancelamentoContratoVO.setListaPagamentosMovimento(gerarmovimentocontacorrente(cancelamentoContratoVO, contrato, usuario));
                cancelamentoContratoVO.setListaPagamentosMovimento(listaMovPagamento);
                if (contrato.getRegimeRecorrencia()) {
                    getFacade().getZWFacade().obterCancelamentoParcelasRecorrencia(cancelamentoContratoVO, contrato);
                } else {
                    getFacade().getZWFacade().obterCancelamentoParcelas(cancelamentoContratoVO);
                }

                MovimentoContaCorrenteClienteVO obj = new MovimentoContaCorrenteClienteVO();
                obj.setDescricao("Deposito do valor do Cancelamento");
                obj.setDataRegistro(negocio.comuns.utilitarias.Calendario.hoje());
                obj.setValor(0.0);
                Iterator i = cancelamentoContratoVO.getListaPagamentosMovimento().iterator();
                while (i.hasNext()) {
                    MovPagamentoVO pag = (MovPagamentoVO) i.next();
                    obj.setValor(Uteis.arredondarForcando2CasasDecimais(obj.getValor() + pag.getValor()));
                }

                obj.setTipoMovimentacao("CR");
                obj.setResponsavelAutorizacao(usuario);
                obj.setPessoa(contrato.getPessoa());
                obj.setMovPagamentosVOs(cancelamentoContratoVO.getListaPagamentosMovimento());

                getFacade().getMovimentoContaCorrenteCliente().adicionarmovimento(obj);
                if (cancelamentoContratoVO.getDepositaNaContaTerceiro()) {
                    obj = getFacade().getMovimentoContaCorrenteCliente().consultarPorCodigoPessoa(obj.getPessoa().getCodigo(), Uteis.NIVELMONTARDADOS_TODOS);
                    obj.setDescricao("Deposito do valor do Cancelamento efetuado pelo Aluno : " + contrato.getPessoa().getNome());
                    obj.setValor(obj.getSaldoAtual());
                    obj.setDataRegistro(negocio.comuns.utilitarias.Calendario.hoje());
                    obj.setPessoa(cancelamentoContratoVO.getCliente().getPessoa());
                    getFacade().getZWFacade().cancelarSaldoContaCorrente(contrato.getPessoa(), usuario, true, cancelamentoContratoVO.getCliente().getPessoa());
                    getFacade().getMovimentoContaCorrenteCliente().adicionarmovimento(obj);

                } else if (cancelamentoContratoVO.getTransferirEmDias()) {
                    obj = getFacade().getMovimentoContaCorrenteCliente().consultarPorCodigoPessoa(obj.getPessoa().getCodigo(), Uteis.NIVELMONTARDADOS_TODOS);
                    cancelamentoContratoVO.setValorASerDevolvidoBaseCalculo(Uteis.arredondarForcando2CasasDecimais(obj.getSaldoAtual()));
                    ContratoOperacaoVO operacaoTransferenciaSaida = new ContratoOperacaoVO();
                    ContratoOperacaoVO operacaoTransferenciaEntrada = new ContratoOperacaoVO();
                    cancelamentoContratoVO.setAjustadaContratoCreditadoAntiga(cancelamentoContratoVO.getContratoCreditadoNrDias().getVigenciaAteAjustada());
                    cancelamentoContratoVO.obterNrDiasAdicionarContratoClienteCreditado();
                    cancelamentoContratoVO.inicializarDadosPeriodoAcessoClienteRecebeDias(usuario);
                    getFacade().getZWFacade().cancelarSaldoContaCorrente(contrato.getPessoa(), usuario, true, cancelamentoContratoVO.getCliente().getPessoa());
                    operacaoTransferenciaSaida = cancelamentoContratoVO.inicializarDadosOperacaoContratoTransferirDias(contrato, usuario);
                    operacaoTransferenciaEntrada = cancelamentoContratoVO.inicializarDadosOperacaoContratoReceberDias(contrato, usuario);
                    incluirSemCommit(operacaoTransferenciaEntrada, false);
                    incluirSemCommit(operacaoTransferenciaSaida, false);
                    cancelamentoContratoVO.setComprovanteTransDiasVO(operacaoTransferenciaSaida);
                    gerarProdutosContratoCreditado(cancelamentoContratoVO, usuario);
                    gerarParcelaPagaContratoCredito(cancelamentoContratoVO, usuario);
                    getFacade().getMovPagamento().incluirListaPagamento(obj.getMovPagamentosVOs(),
                            cancelamentoContratoVO.getContratoCreditadoNrDias().getMovParcelaVOs(), null, cancelamentoContratoVO.getContratoCreditadoNrDias(), false, 0.0, false, null);
                    if (cancelamentoContratoVO.getContratoCreditadoNrDias().getContratoResponsavelRenovacaoMatricula() > 0) {
                        ajustarRenovacoesFuturas(cancelamentoContratoVO.getContratoCreditadoNrDias(), "Transferência Entrada", usuario, cancelamentoContratoVO.getContratoCreditadoNrDias().getCodigo());
                    }
                    alterarMatriculas(cancelamentoContratoVO.getContratoCreditadoNrDias(), negocio.comuns.utilitarias.Calendario.hoje(), cancelamentoContratoVO.getContratoCreditadoNrDias().getVigenciaAteAjustada(), false, null, operacaoTransferenciaEntrada.getTipoOperacao());
                    atualizaDataFimDeProdutoComVigenciaDeContrato(cancelamentoContratoVO.getContratoCreditadoNrDias().getCodigo(), null, cancelamentoContratoVO.getContratoCreditadoNrDias().getVigenciaAteAjustada());
                    ClienteVO cliente = getFacade().getCliente()
                            .consultarPorCodigoPessoa(cancelamentoContratoVO.getContratoCreditadoNrDias().getPessoa().getCodigo(),
                                    Uteis.NIVELMONTARDADOS_SITUACAOCLIENTESINTETICODW);
                    cliente.setDeveAtualizarDependentesSintetico(true);
                    getFacade().getZWFacade().atualizarSintetico(cliente, Calendario.hoje(), SituacaoClienteSinteticoEnum.GRUPO_CONTRATO, false);
                }
                atualizaDataFimDeProdutoComVigenciaDeContrato(contrato.getCodigo(), null, contrato.getVigenciaAteAjustada());
                ClienteVO cliente = getFacade().getCliente()
                        .consultarPorCodigoPessoa(contrato.getPessoa().getCodigo(),
                                Uteis.NIVELMONTARDADOS_SITUACAOCLIENTESINTETICODW);
                cliente.setDeveAtualizarDependentesSintetico(true);
                getFacade().getZWFacade().atualizarSintetico(cliente,
                        Calendario.hoje(), SituacaoClienteSinteticoEnum.GRUPO_CONTRATO, false);
            }

            //Cancelar boletos pendentes do contrato
            boolean cancelarBoletoAoCancelarOuEstonarContrato = PropsService.isTrue(PropsService.cancelarBoletoAoCancelarOuEstonarContrato);
            if (cancelarBoletoAoCancelarOuEstonarContrato) {
                Uteis.logar("##Cancelamento Automático de Boleto## | Vou cancelar os boletos pendentes para o contrato: " + contrato.getCodigo());
                Boleto boletoDAO;
                try {
                    boletoDAO = new Boleto(con);
                    List<BoletoVO> listaBoletosCancelar = boletoDAO.excluirBoletoContratoOrigemCancelamentoContrato(contrato, cancelamentoContratoVO.getResponsavelCancelamento());
                    if (!UteisValidacao.emptyList(listaBoletosCancelar)) {
                        boletoDAO.cancelarBoletos(listaBoletosCancelar, cancelamentoContratoVO.getResponsavelCancelamento(), "CancelamentoContrato");
                    } else {
                        Uteis.logar("##Cancelamento Automático de Boleto## | Não foi encontrado nenhum boleto pendente para cancelar do contrato: " + contrato.getCodigo());
                    }
                } catch (Exception ignore) {
                } finally {
                    boletoDAO = null;
                }
            } else {
                Uteis.logar("##Cancelamento Automático de Boleto## | Desativado...");
            }

            con.commit();
        } catch (Exception e) {
            con.rollback();
            con.setAutoCommit(true);
            throw e;
        } finally {
            con.setAutoCommit(true);
        }
    }
    
    private void gerarParcelaPagaContratoCredito(
			CancelamentoContratoVO cancelamentoContratoVO, UsuarioVO usuario) throws Exception {
    	 try {
	    	 MovParcelaVO movParcela = new MovParcelaVO();
	         double valorParcelas = 0;
	         double valorPrimeiraParcelas = 0;
	         Date dataAtual = negocio.comuns.utilitarias.Calendario.hoje();
	         Date data = negocio.comuns.utilitarias.Calendario.hoje();
	       
	         movParcela.setValorBaseCalculo(cancelamentoContratoVO.getValorASerDevolvidoBaseCalculo());
	         movParcela.setValorParcela(cancelamentoContratoVO.getValorASerDevolvidoBaseCalculo());
	
	         movParcela.setDescricao("PARCELA TRANSFERÊNCIA DE DIAS");
	         movParcela.setDataRegistro(negocio.comuns.utilitarias.Calendario.hoje());
	         movParcela.setDataVencimento(dataAtual);
	         movParcela.setPercentualJuro(cancelamentoContratoVO.getContratoCreditadoNrDias().getEmpresa().getJuroParcela());
	         movParcela.setPercentualMulta(cancelamentoContratoVO.getContratoCreditadoNrDias().getEmpresa().getJuroParcela());
	         movParcela.setResponsavel(usuario);
	         movParcela.setSituacao("EA");
	
	
	         movParcela.setContrato(cancelamentoContratoVO.getContratoCreditadoNrDias());
	         movParcela.setEmpresa(cancelamentoContratoVO.getContratoCreditadoNrDias().getEmpresa());
	         movParcela.setPessoa(cancelamentoContratoVO.getContratoCreditadoNrDias().getPessoa());
	         Iterator i = cancelamentoContratoVO.getContratoCreditadoNrDias().getMovProdutoVOs().iterator();
	         MovProdutoParcelaVO movProdutoParcela;
	         while (i.hasNext()){
	        	 MovProdutoVO movProduto = (MovProdutoVO) i.next();
	        	 movProdutoParcela = new MovProdutoParcelaVO();
		         movProdutoParcela.setMovProduto(movProduto.getCodigo());
                 movProdutoParcela.setMovProdutoVO(movProduto);
		         Double valor = Uteis.arredondarForcando2CasasDecimais(movProduto.getTotalFinal());
		         movProdutoParcela.setValorPago(valor);
		         movParcela.getMovProdutoParcelaVOs().add(movProdutoParcela);
	         }
	         getFacade().getZWFacade().incluirMovParcelaSemCommit(movParcela);
	         cancelamentoContratoVO.getContratoCreditadoNrDias().setMovParcelaVOs(new ArrayList<MovParcelaVO>());
	         cancelamentoContratoVO.getContratoCreditadoNrDias().getMovParcelaVOs().add(movParcela);
	         cancelamentoContratoVO.getContratoCreditadoNrDias().setValorBaseCalculo(cancelamentoContratoVO.getValorASerDevolvido()+
	        		 cancelamentoContratoVO.getContratoCreditadoNrDias().getValorBaseCalculo());
	         getFacade().getContrato().alterarValorBaseContrato(cancelamentoContratoVO.getContratoCreditadoNrDias(), false);
	     } catch (Exception e) {
	         throw e;
	     }
 
		
	}

	public void gerarProdutosContratoCreditado(CancelamentoContratoVO cancelamentoContratoVO, UsuarioVO usuario) throws Exception {
        try {
        	MovProdutoVO novo;
        	List <MovProdutoVO> movProdutosnovo = new ArrayList<MovProdutoVO>();
            int i = 0;
            //Date dataAtual = negocio.comuns.utilitarias.Calendario.hoje();
            Date dataAtual = cancelamentoContratoVO.getAjustadaContratoCreditadoAntiga();
            Date dataBaseCompetencia = Uteis.obterPrimeiroDiaMes(dataAtual);
            int meses = (int) (cancelamentoContratoVO.getNrDiasCreditarNoContrato() / 30);
            int mes = 1; 
            Integer restante = (int) (cancelamentoContratoVO.getNrDiasCreditarNoContrato() % 30);
            if (restante > 1){
                restante -= (meses / 2); // para avaliar meses de 31 dias
                if (restante < 0){
                    restante = 0;
                }

            }
            Integer duracaoContrato = getFacade().getZWFacade().obterNrDiasUtilizadoAcademiaAteDataEspecifica(cancelamentoContratoVO.getContratoCreditadoNrDias(), cancelamentoContratoVO.getAjustadaContratoCreditadoAntiga())/30; // necessario para casos em que o contrato já recebeu outra transferencia
            if (duracaoContrato == 0){
                duracaoContrato = 1;
            }
            Double totalfinal = 0.0;
            if( cancelamentoContratoVO.getValorDiaContratoCreditadoValorBase() < cancelamentoContratoVO.getValorDiaContratoValorBase()){
                totalfinal = Uteis.arredondarForcando2CasasDecimaisMantendoSinal((cancelamentoContratoVO.getValorASerDevolvidoBaseCalculo() / ((meses * 30) + restante)) * 30);
            } else {
                totalfinal = Uteis.arredondarForcando2CasasDecimaisMantendoSinal(cancelamentoContratoVO.getContratoCreditadoNrDias().getValorBaseCalculo() / duracaoContrato);
            }
            Date data = dataBaseCompetencia;
            Double valorTotalProdutos = cancelamentoContratoVO.getValorASerDevolvidoBaseCalculo();
            while (i < meses) {
            	novo = new MovProdutoVO();
                novo.setProduto(cancelamentoContratoVO.getContratoCreditadoNrDias().getPlano().getProdutoPadraoGerarParcelasContrato());
                novo.setApresentarMovProduto(true);
                novo.setQuitado(true);
                novo.setContrato(cancelamentoContratoVO.getContratoCreditadoNrDias());
                novo.setMesReferencia(Uteis.getMesReferenciaData(data));
                novo.setAnoReferencia(Uteis.getAnoData(data));
                data = Uteis.obterDataFuturaParcela(dataBaseCompetencia , mes);
                valorTotalProdutos = Uteis.arredondarForcando2CasasDecimaisMantendoSinal(valorTotalProdutos - totalfinal);
                if(valorTotalProdutos < 0.0){
                    novo.setPrecoUnitario(Uteis.arredondarForcando2CasasDecimais(valorTotalProdutos + totalfinal));
                    novo.setTotalFinal(Uteis.arredondarForcando2CasasDecimais(valorTotalProdutos + totalfinal));
                    restante = 30 - restante;
                    novo.setDescricao("PLANO TRANSFERIDO - " + novo.getMesReferencia() + " RESTANTE "+ restante.toString());
                } else {
                     novo.setTotalFinal(totalfinal);
                     novo.setPrecoUnitario(totalfinal);
                     novo.setDescricao("PLANO TRANSFERIDO - " + novo.getMesReferencia());
                }
                novo.setSituacao("EA");
                novo.setEmpresa(cancelamentoContratoVO.getContratoCreditadoNrDias().getEmpresa());
                novo.setPessoa(cancelamentoContratoVO.getContratoCreditadoNrDias().getPessoa());
                novo.setQuantidade(1);
                novo.setDataInicioVigencia(dataAtual);
                novo.setDataFinalVigencia(Uteis.obterDataFutura(dataAtual, ((cancelamentoContratoVO.getContratoCreditadoNrDias().getPlanoDuracao().getNumeroMeses()+mes) * 30)));
                novo.setDataLancamento(negocio.comuns.utilitarias.Calendario.hoje());
                novo.getResponsavelLancamento().setCodigo(usuario.getCodigo());
                novo.setValorDesconto(0.0);
                novo.setMovProdutoModalidades(getFacade().getZWFacade().gerarMovProdutoModalidade(MovProdutoModalidade.getValorSomaContratoModalidade(cancelamentoContratoVO.getContratoCreditadoNrDias().getContratoModalidadeVOs()),
                		cancelamentoContratoVO.getContratoCreditadoNrDias().getVigenciaDe(), cancelamentoContratoVO.getContratoCreditadoNrDias().getVigenciaAte(), cancelamentoContratoVO.getContratoCreditadoNrDias().getContratoModalidadeVOs(), novo.getTotalFinal()));
                mes++;
                i++;

                
                getFacade().getMovProduto().incluirSemCommit(novo);
                movProdutosnovo.add(novo);
            }
            if (valorTotalProdutos > 0.0 && restante > 0){
             	novo = new MovProdutoVO();
                novo.setProduto(cancelamentoContratoVO.getContratoCreditadoNrDias().getPlano().getProdutoPadraoGerarParcelasContrato());
                novo.setApresentarMovProduto(true);
                novo.setQuitado(true);
                novo.setContrato(cancelamentoContratoVO.getContratoCreditadoNrDias());
                novo.setMesReferencia(Uteis.getMesReferenciaData(data));
                novo.setAnoReferencia(Uteis.getAnoData(data));
                data = Uteis.obterDataFuturaParcela(dataAtual, mes);
                novo.setDescricao("PLANO TRANSFERIDO - " + novo.getMesReferencia() + " RESTANTE "+ restante.toString());
                novo.setSituacao("EA");
                novo.setEmpresa(cancelamentoContratoVO.getContratoCreditadoNrDias().getEmpresa());
                novo.setPessoa(cancelamentoContratoVO.getContratoCreditadoNrDias().getPessoa());
                novo.setQuantidade(1);
                novo.setDataInicioVigencia(dataAtual);
                novo.setDataFinalVigencia(Uteis.obterDataFutura(dataAtual, ((cancelamentoContratoVO.getContratoCreditadoNrDias().getPlanoDuracao().getNumeroMeses()+meses) * 30)));
                novo.setDataLancamento(negocio.comuns.utilitarias.Calendario.hoje());
                novo.getResponsavelLancamento().setCodigo(usuario.getCodigo());
                novo.setTotalFinal(Uteis.arredondarForcando2CasasDecimaisMantendoSinal(cancelamentoContratoVO.getValorASerDevolvidoBaseCalculo() - (meses * totalfinal)));
                novo.setValorDesconto(0.0);
                novo.setPrecoUnitario((novo.getTotalFinal()));
                novo.setMovProdutoModalidades(getFacade().getZWFacade().gerarMovProdutoModalidade(MovProdutoModalidade.getValorSomaContratoModalidade(cancelamentoContratoVO.getContratoCreditadoNrDias().getContratoModalidadeVOs()),
                		cancelamentoContratoVO.getContratoCreditadoNrDias().getVigenciaDe(), cancelamentoContratoVO.getContratoCreditadoNrDias().getVigenciaAte(), cancelamentoContratoVO.getContratoCreditadoNrDias().getContratoModalidadeVOs(), novo.getTotalFinal()));
                
                getFacade().getMovProduto().incluirSemCommit(novo);
                movProdutosnovo.add(novo);
             
            }

            
            cancelamentoContratoVO.getContratoCreditadoNrDias().setMovProdutoVOs(movProdutosnovo);
           
        } catch (Exception e) {
            throw e;
        }

    }

    public List<MovPagamentoVO> gerarmovimentocontacorrente(Double valorCredito,
    		CancelamentoContratoVO cancelamentoContratoVO, ContratoVO contrato, UsuarioVO usuario, boolean transferenciaDias) throws Exception {

        List contratosPorRecibo = getFacade().getReciboPagamento().consularReciboComCodigoContrato(contrato.getCodigo() );
        List <ReciboPagamentoVO> listaReciboPagamento = getFacade().getReciboPagamento().consularReciboPagamParcelaPorCodigoVariosContratos(contratosPorRecibo , Uteis.NIVELMONTARDADOS_DADOSBASICOS);
        //List <ReciboPagamentoVO> listaReciboPagamento = getFacade().getReciboPagamento().consularReciboPagamParcelaPorCodigoContrato(contrato.getCodigo() , Uteis.NIVELMONTARDADOS_DADOSBASICOS);

    	List <MovPagamentoVO> listaTodosPagamentos = new ArrayList<MovPagamentoVO>();
    	List <MovPagamentoVO> listaPagamentos = new ArrayList<MovPagamentoVO>();
    	List <MovPagamentoVO> pagamentosMovimento = new ArrayList<MovPagamentoVO>();
    	List <MovProdutoVO > produtosCancelados = new ArrayList<MovProdutoVO>();
        List <PagamentoMovParcelaVO> pagMovparcelaGeral = new ArrayList<PagamentoMovParcelaVO>();
    	Iterator k = listaReciboPagamento.iterator();

    	while (k.hasNext()) {
    		ReciboPagamentoVO recibo = (ReciboPagamentoVO) k.next();
    		listaTodosPagamentos.addAll(getFacade().getMovPagamento().consultarPorCodigoRecibo(recibo.getCodigo(), false, Uteis.NIVELMONTARDADOS_TODOS));
    	}
    	for(MovPagamentoVO pagamento : listaTodosPagamentos){ // retira os pagamentos que já foram transferidos
    		if(pagamento.getValor() > 0.0){
                    Ordenacao.ordenarListaReverse(pagamento.getPagamentoMovParcelaVOs(), "codigo");
                    if(pagamento.getFormaPagamento().getTipoFormaPagamento().equals("CH")){
                        for(ChequeVO cheque: pagamento.getChequeVOs()){
                            if(!cheque.getSituacao().equals("CA")){ // retirar cheque devolvidos do calculo
                                pagamento.setValorPP(Uteis.arredondarForcando2CasasDecimais(pagamento.getValorPP() + cheque.getValor()));
                            }
                        }
                        pagamento.setValor(Uteis.arredondarForcando2CasasDecimais(pagamento.getValor() - pagamento.getValorPP()));
                    } else {
                        pagamento.setValorPP(pagamento.getValor());
                    }
                    if(pagamento.getValorPP() > 0.0){
                        pagMovparcelaGeral.add((PagamentoMovParcelaVO) pagamento.getPagamentoMovParcelaVOs().get(0));
                    }
    		}
    	}
        Ordenacao.ordenarListaReverse(pagMovparcelaGeral, "codigo");// garantir que os ultimos pagamentos usados sejam transferidos primeiro. Para evitar problemas nos produtos pagos
        for(PagamentoMovParcelaVO pagParcela: pagMovparcelaGeral){
            for(MovPagamentoVO pagamento : listaTodosPagamentos){ 
                if(pagParcela.getMovPagamento().equals(pagamento.getCodigo())){
                    listaPagamentos.add(pagamento);
                }
            }
        }
    	try {
             if (cancelamentoContratoVO.getDepositaNaContaTerceiro() || cancelamentoContratoVO.getTransferirEmDias()) {
                 valorCredito = Uteis.arredondarForcando2CasasDecimais(valorCredito - cancelamentoContratoVO.getSaldoContaCorrenteCliente());
             }

    		List<MovProdutoVO> lista = getFacade().getMovProduto().consultarPorVendidoJuntoAoContratoOrdenado(contrato.getCodigo().intValue(),cancelamentoContratoVO.isCancelarParcelaAnuidade(), Uteis.NIVELMONTARDADOS_DADOSBASICOS);
    		Collections.reverse(lista);
    		List<PagamentoMovParcelaVO> pagMovparcelasNovos = new ArrayList<PagamentoMovParcelaVO>();
    		Iterator<MovProdutoVO> i = lista.iterator();
    		boolean continuarComparandoProdutosPM = true;
    		while (i.hasNext()) {
                        MovProdutoVO mp = i.next();
                        if(mp.getSituacao().equals("CA")){
                            continue;
                        }
                        if (!mp.getProduto().getTipoProduto().equals("PM")  && !mp.getProduto().getTipoProduto().equals("MM")) {
                            if (!mp.getSituacao().equals("PG")) {
                                Double valorPagoParcial = 0.0;
                                List<MovProdutoParcelaVO> mppEmAberto = new ArrayList<MovProdutoParcelaVO>();
                                for (Object obj : mp.getMovProdutoParcelaVOs()) {
                                    MovProdutoParcelaVO mpp = (MovProdutoParcelaVO) obj;
                                    if (mpp.getReciboPagamento() != null && mpp.getReciboPagamento().getCodigo().intValue() != 0) {
                                        valorPagoParcial  = Uteis.arredondarForcando2CasasDecimais(valorPagoParcial  + mpp.getValorPago());
                                    } else {
                                        mppEmAberto.add(mpp);
                                    }
                                }
                                if(valorPagoParcial > 0.0){
                                    MovProdutoVO novo = (MovProdutoVO) mp.getClone(true);
                                    novo.setTotalFinal(Uteis.arredondarForcando2CasasDecimais(mp.getTotalFinal() - valorPagoParcial));
                                    novo.setPrecoUnitario(novo.getTotalFinal());
                                    novo.setValorDesconto(0.0);
                                    novo.setValorFaturado(novo.getTotalFinal());
                                    novo.setSituacao("CA");    
                                    novo.setMovProdutoParcelaVOs(mppEmAberto);
                                    mp.setTotalFinal(valorPagoParcial);
                                    mp.setPrecoUnitario(Uteis.arredondarForcando2CasasDecimais(valorPagoParcial + mp.getValorDesconto()));
                                    mp.setSituacao("PG");
                                    mp.setValorFaturado(Uteis.arredondarForcando2CasasDecimais(mp.getValorFaturado() - novo.getTotalFinal()));
                                    getFacade().getMovProduto().incluirSemCommit(novo);
                                    getFacade().getMovProduto().alterarSemCommit(mp);
                                    for (Object obj : novo.getMovProdutoParcelaVOs()) {
                                            MovProdutoParcelaVO mpp = (MovProdutoParcelaVO) obj;
                                            mpp.setMovProduto(novo.getCodigo());
                                            getFacade().getMovProdutoParcela().alterar(mpp);
                                    }
                                } else {
                                    mp.setSituacao("CA");
                                    getFacade().getMovProduto().alterarSemCommit(mp);
                                }
                            }
                            continue;
    			}
                        if(continuarComparandoProdutosPM){
                            if (!mp.getSituacao().equals("PG")) {
                                    mp.setQuitado(false);
                                    mp.setSituacao("CA");
                                    Double valorPago = 0.0;
                                    List <MovProdutoParcelaVO> mppNovos = new ArrayList<MovProdutoParcelaVO>();
                                    List<MovProdutoParcelaVO> mppEmAberto = new ArrayList<MovProdutoParcelaVO>();
                                    for (Object obj : mp.getMovProdutoParcelaVOs()) {
                                            MovProdutoParcelaVO mpp = (MovProdutoParcelaVO) obj;
                                            if (mpp.getReciboPagamento() != null && mpp.getReciboPagamento().getCodigo() != 0) {
                                                Double valorProduto = 0.0;
                                                if(valorCredito <=  Uteis.arredondarForcando2CasasDecimais(mpp.getValorPago())){
                                                    valorProduto = valorCredito;
                                                    valorCredito = 0.0;
                                                    MovProdutoParcelaVO novo = (MovProdutoParcelaVO) mpp.getClone(true);
                                                    mpp.setValorPago(Uteis.arredondarForcando2CasasDecimais(mpp.getValorPago() - valorProduto));
                                                    valorPago =  Uteis.arredondarForcando2CasasDecimais(valorPago + mpp.getValorPago());
                                                    novo.setValorPago(valorProduto);
                                                    mppNovos.add(novo);
                                                    getFacade().getMovProdutoParcela().alterar(mpp);
                                                }else{
                                                    valorProduto = Uteis.arredondarForcando2CasasDecimais(mpp.getValorPago());
                                                    valorCredito = Uteis.arredondarForcando2CasasDecimais(valorCredito - mpp.getValorPago());
                                                }
                                                Iterator p = listaPagamentos.iterator();

                                                while(p.hasNext() && valorProduto > 0.0){
                                                        MovPagamentoVO movPagamento = (MovPagamentoVO) p.next();
                                                        Double valorDiferenca = 0.0;
                                                        if(movPagamento.getValorPP() > 0.0){
                                                                if(Uteis.arredondarForcando2CasasDecimais(valorProduto) > Uteis.arredondarForcando2CasasDecimais(movPagamento.getValorPP())){
                                                                    valorDiferenca = Uteis.arredondarForcando2CasasDecimais(valorProduto - movPagamento.getValorPP());
                                                                    valorProduto = movPagamento.getValorPP();
                                                                }
                                                                Iterator pm = movPagamento.getPagamentoMovParcelaVOs().iterator();
                                                                while (pm.hasNext()){
                                                                        PagamentoMovParcelaVO pagMov = (PagamentoMovParcelaVO) pm.next();
                                                                        if(mpp.getMovParcela() == pagMov.getMovParcela().getCodigo().intValue()){
                                                                                if(Uteis.arredondarForcando2CasasDecimais(pagMov.getValorPago()) <= valorProduto && pagMov.getValorPago() > 0 && pagMov.getMovPagamento() != 0){
                                                                                        movPagamento.setValorPP(Uteis.arredondarForcando2CasasDecimais(movPagamento.getValorPP()- pagMov.getValorPago()));
                                                                                        pagMov.setMovPagamento(0);
                                                                                        valorProduto -=  Uteis.arredondarForcando2CasasDecimais(pagMov.getValorPago());
                                                                                }
                                                                                if(Uteis.arredondarForcando2CasasDecimais(pagMov.getValorPago()) > valorProduto && valorProduto > 0 && pagMov.getMovPagamento() != 0){
                                                                                        movPagamento.setValorPP(Uteis.arredondarForcando2CasasDecimais(movPagamento.getValorPP()) - valorProduto);
                                                                                        PagamentoMovParcelaVO novoPag = (PagamentoMovParcelaVO) pagMov.getClone(true);
                                                                                        pagMov.setValorPago(Uteis.arredondarForcando2CasasDecimais(pagMov.getValorPago()) -valorProduto);
                                                                                        novoPag.setValorPago(valorProduto);
                                                                                        pagMovparcelasNovos.add(novoPag);
                                                                                        valorProduto = 0.0;
                                                                                }
                                                                        }
                                                                }
                                                                valorProduto = Uteis.arredondarForcando2CasasDecimais(valorProduto + valorDiferenca);
                                                        }

                                                }
                                        } else {
                                            mppEmAberto.add(mpp);
                                        }
                                    }
                                    if(valorPago > 0.0){
                                        MovProdutoVO produtoNovo = (MovProdutoVO) mp.getClone(true);
                                        mp.setTotalFinal(valorPago);
                                        mp.setPrecoUnitario(Uteis.arredondarForcando2CasasDecimais(mp.getTotalFinal()+ produtoNovo.getValorDesconto()));
                                        produtoNovo.setValorDesconto(0.0);
                                        produtoNovo.setTotalFinal(Uteis.arredondarForcando2CasasDecimais(produtoNovo.getTotalFinal() - valorPago));
                                        produtoNovo.setPrecoUnitario(produtoNovo.getTotalFinal());
                                        produtoNovo.setValorFaturado(produtoNovo.getTotalFinal());
                                        produtoNovo.setSituacao("CA");
                                        mp.setSituacao("PG");
                                        mp.setValorFaturado(Uteis.arredondarForcando2CasasDecimais(mp.getValorFaturado() - produtoNovo.getTotalFinal()));
                                        getFacade().getMovProduto().alterarSemCommit(mp);
                                        getFacade().getMovProduto().incluirSemCommit(produtoNovo);
                                        for (MovProdutoParcelaVO mppProdutoAberto: mppEmAberto){
                                            mppProdutoAberto.setMovProduto(produtoNovo.getCodigo());
                                            getFacade().getMovProdutoParcela().alterar(mppProdutoAberto);
                                        }
                                        for (MovProdutoParcelaVO mppProdutoNovo: mppNovos){
                                            mppProdutoNovo.setMovProduto(produtoNovo.getCodigo());
                                            getFacade().getMovProdutoParcela().incluir(mppProdutoNovo);
                                        }
                                    }
                                    if  (valorCredito <= 0.0){
                                        continuarComparandoProdutosPM = false;
                                    }
                                    produtosCancelados.add(mp);
                                    continue;
                            }
                            if (valorCredito >= Uteis.arredondarForcando2CasasDecimais(mp.getTotalFinal())) {
                                valorCredito = Uteis.arredondarForcando2CasasDecimais(valorCredito - mp.getTotalFinal());
                                    mp.setQuitado(false);
                                    mp.setSituacao("CA");
                                    for (Object obj : mp.getMovProdutoParcelaVOs()) {
                                            MovProdutoParcelaVO mpp = (MovProdutoParcelaVO) obj;
                                            Iterator p = listaPagamentos.iterator();
                                            Double valorProduto = Uteis.arredondarForcando2CasasDecimais(mpp.getValorPago());
                                            while(p.hasNext() && valorProduto > 0.0){
                                                    MovPagamentoVO movPagamento = (MovPagamentoVO) p.next();
                                                    Double valorDiferenca = 0.0;
                                                    if(movPagamento.getValorPP() > 0.0){
                                                            if(Uteis.arredondarForcando2CasasDecimais(valorProduto) > Uteis.arredondarForcando2CasasDecimais(movPagamento.getValorPP())){
                                                                valorDiferenca = Uteis.arredondarForcando2CasasDecimais(valorProduto - movPagamento.getValorPP());
                                                                valorProduto = movPagamento.getValorPP();
                                                            }
                                                            Iterator pm = movPagamento.getPagamentoMovParcelaVOs().iterator();
                                                            while (pm.hasNext()){
                                                                    PagamentoMovParcelaVO pagMov = (PagamentoMovParcelaVO) pm.next();
                                                                    if(mpp.getMovParcela() == pagMov.getMovParcela().getCodigo().intValue()){
                                                                            if(Uteis.arredondarForcando2CasasDecimais(pagMov.getValorPago()) <= valorProduto && pagMov.getValorPago() > 0 && pagMov.getMovPagamento() != 0){
                                                                                    movPagamento.setValorPP(Uteis.arredondarForcando2CasasDecimais(movPagamento.getValorPP()- pagMov.getValorPago()));
                                                                                    pagMov.setMovPagamento(0);
                                                                                    valorProduto =  Uteis.arredondarForcando2CasasDecimais(valorProduto - pagMov.getValorPago());
                                                                            } else {
                                                                                    if(Uteis.arredondarForcando2CasasDecimais(pagMov.getValorPago()) > valorProduto && valorProduto > 0 && pagMov.getMovPagamento() != 0){
                                                                                            movPagamento.setValorPP(Uteis.arredondarForcando2CasasDecimais(movPagamento.getValorPP()- valorProduto));
                                                                                            PagamentoMovParcelaVO novoPag = (PagamentoMovParcelaVO) pagMov.getClone(true);
                                                                                            novoPag.setValorPago(valorProduto); 
                                                                                            pagMov.setValorPago(Uteis.arredondarForcando2CasasDecimais(pagMov.getValorPago()) -valorProduto);
                                                                                            pagMovparcelasNovos.add(novoPag);
                                                                                            valorProduto = 0.0;
                                                                                    }
                                                                            }
                                                                    }

                                                            }
                                                            valorProduto = Uteis.arredondarForcando2CasasDecimais(valorProduto + valorDiferenca);
                                                    }

                                            }
                                    }

                                    produtosCancelados.add(mp);

                            } else if (valorCredito > 0) {

                                    Double valorAntigo =  Uteis.arredondarForcando2CasasDecimais(mp.getTotalFinal() - valorCredito);
                                    Double valorNovo = valorCredito;
                                    MovProdutoVO novo = (MovProdutoVO) mp.getClone(true);

                                    mp.setPrecoUnitario(valorAntigo + mp.getValorDesconto());
                                    mp.setTotalFinal(valorAntigo);

                                    List<MovProdutoParcelaVO> movProdutosParcelas = new ArrayList<MovProdutoParcelaVO>();
                                    novo.setPrecoUnitario(valorNovo);
                                    novo.setTotalFinal(valorNovo);
                                    novo.setValorDesconto(0.0);
                                    novo.setQuitado(false);
                                    novo.setSituacao("CA");
                                    novo.setValorFaturado(valorNovo);
                                    mp.setValorFaturado(Uteis.arredondarForcando2CasasDecimais(mp.getValorFaturado() - novo.getValorFaturado()));
                                    novo.setMovProdutoParcelaVOs(new ArrayList<MovProdutoParcelaVO>());
                                    Double valorMovProdutoAntigo = valorAntigo;

                                    for (Object obj : mp.getMovProdutoParcelaVOs()) {
                                                    MovProdutoParcelaVO mpp = (MovProdutoParcelaVO) obj;

                                                    if (Uteis.arredondarForcando2CasasDecimais(mpp.getValorPago()) <= valorMovProdutoAntigo && valorMovProdutoAntigo > 0){
                                                            valorMovProdutoAntigo -= Uteis.arredondarForcando2CasasDecimais(mpp.getValorPago());
                                                            movProdutosParcelas.add(mpp);
                                                    } else 	if(valorMovProdutoAntigo > 0){
                                                            MovProdutoParcelaVO mppNovo = (MovProdutoParcelaVO) mpp.getClone(true);
                                                            mppNovo.setValorPago(Uteis.arredondarForcando2CasasDecimais(mpp.getValorPago()) - valorMovProdutoAntigo);
                                                            mpp.setValorPago(valorMovProdutoAntigo);
                                                            movProdutosParcelas.add(mpp);
                                                            novo.getMovProdutoParcelaVOs().add(mppNovo);
                                                            valorMovProdutoAntigo = 0.0;
                                                    } else {
                                                            novo.getMovProdutoParcelaVOs().add(mpp);
                                                    }
                                            }
                                    mp.setMovProdutoParcelaVOs(new ArrayList<MovProdutoParcelaVO>());
                                    mp.getMovProdutoParcelaVOs().addAll(movProdutosParcelas);

                        novo.setMovProdutoModalidades(getFacade().getZWFacade().gerarMovProdutoModalidade(MovProdutoModalidade.getValorSomaContratoModalidade(contrato.getContratoModalidadeVOs()),
                                contrato.getVigenciaDe(), contrato.getVigenciaAte(), contrato.getContratoModalidadeVOs(), novo.getTotalFinal()));
                        getFacade().getMovProduto().incluirSemCommit(novo);
                        getFacade().getMovProdutoModalidade().incluir(novo);
                                    produtosCancelados.add(novo);
                                    getFacade().getMovProduto().alterarSemCommit(mp);
                                    for (Object obj : novo.getMovProdutoParcelaVOs()) {
                                            MovProdutoParcelaVO mpp = (MovProdutoParcelaVO) obj;
                                            mpp.setMovProduto(novo.getCodigo());
                                            getFacade().getMovProdutoParcela().incluir(mpp);
                                    }
                                    getFacade().getMovProdutoParcela().excluirMovProdutoParcelas(mp.getCodigo());
                                    for (Object obj : mp.getMovProdutoParcelaVOs()) {
                                            MovProdutoParcelaVO mpp = (MovProdutoParcelaVO) obj;
                                            mpp.setMovProduto(mp.getCodigo());
                                            getFacade().getMovProdutoParcela().incluir(mpp);
                                    }


                                    for (Object obj : novo.getMovProdutoParcelaVOs()) {
                                            MovProdutoParcelaVO mpp = (MovProdutoParcelaVO) obj;
                                            if (mpp.getReciboPagamento() != null && mpp.getReciboPagamento().getCodigo() != 0) {
                                                valorCredito = Uteis.arredondarForcando2CasasDecimais(valorCredito -mpp.getValorPago());
                                            }
                                            Iterator p = listaPagamentos.iterator();
                                            Double valorProduto = Uteis.arredondarForcando2CasasDecimais(mpp.getValorPago());
                                            while(p.hasNext() && valorProduto > 0.0){
                                                    MovPagamentoVO movPagamento = (MovPagamentoVO) p.next();
                                                    Double valorDiferenca = 0.0;
                                                    if(movPagamento.getValorPP() > 0.0){
                                                            if(Uteis.arredondarForcando2CasasDecimais(valorProduto) > Uteis.arredondarForcando2CasasDecimais(movPagamento.getValorPP())){
                                                                valorDiferenca = Uteis.arredondarForcando2CasasDecimais(valorProduto - movPagamento.getValorPP());
                                                                valorProduto = movPagamento.getValorPP();
                                                            }
                                                            Iterator pm = movPagamento.getPagamentoMovParcelaVOs().iterator();
                                                            while (pm.hasNext()){
                                                                    PagamentoMovParcelaVO pagMov = (PagamentoMovParcelaVO) pm.next();
                                                                    if(mpp.getMovParcela() == pagMov.getMovParcela().getCodigo().intValue()){
                                                                            if(Uteis.arredondarForcando2CasasDecimais(pagMov.getValorPago()) <= valorProduto && pagMov.getValorPago() > 0 && pagMov.getMovPagamento() != 0){
                                                                                    movPagamento.setValorPP(Uteis.arredondarForcando2CasasDecimais(movPagamento.getValorPP()- pagMov.getValorPago()));
                                                                                    pagMov.setMovPagamento(0);
                                                                                    valorProduto -=  Uteis.arredondarForcando2CasasDecimais(pagMov.getValorPago());
                                                                            }
                                                                            if(Uteis.arredondarForcando2CasasDecimais(pagMov.getValorPago()) > valorProduto && valorProduto > 0 && pagMov.getMovPagamento() != 0){
                                                                                    movPagamento.setValorPP(Uteis.arredondarForcando2CasasDecimais(movPagamento.getValorPP()- valorProduto));
                                                                                    PagamentoMovParcelaVO novoPag = (PagamentoMovParcelaVO) pagMov.getClone(true);
                                                                                    pagMov.setValorPago(Uteis.arredondarForcando2CasasDecimais(pagMov.getValorPago()) - valorProduto);
                                                                                    novoPag.setValorPago(valorProduto);
                                                                                    pagMovparcelasNovos.add(novoPag);
                                                                                    valorProduto =  0.0;
                                                                            }
                                                                    }
                                                            }
                                                            valorProduto = Uteis.arredondarForcando2CasasDecimais(valorProduto + valorDiferenca);
                                                    }
                                            }
                                    }
                                    mp.setMovProdutoModalidades(getFacade().getZWFacade().gerarMovProdutoModalidade(MovProdutoModalidade.getValorSomaContratoModalidade(contrato.getContratoModalidadeVOs()),
                                contrato.getVigenciaDe(), contrato.getVigenciaAte(), contrato.getContratoModalidadeVOs(), mp.getTotalFinal()));
                                    getFacade().getMovProduto().alterarSemCommit(mp);
                                    getFacade().getMovProdutoModalidade().excluirPorMovProduto(mp, contrato.getContratoModalidadeVOs());
                                    getFacade().getMovProdutoModalidade().incluir(mp);
                                    continuarComparandoProdutosPM = false;
                            }
                    }
                }
	
    		MovPagamentoVO novo = null;
                Collections.reverse(listaPagamentos);
    		Iterator pags = listaPagamentos.iterator();
    		while (pags.hasNext()){
    			MovPagamentoVO movPagamento = (MovPagamentoVO) pags.next();
                        if(movPagamento.getFormaPagamento().getTipoFormaPagamento().equals("CH")){
                            movPagamento.setValor(Uteis.arredondarForcando2CasasDecimais(movPagamento.getValor() + movPagamento.getValorPP()));
                        } else {
                            movPagamento.setValor(movPagamento.getValorPP());
                        }
    			if (Uteis.arredondarForcando2CasasDecimais(movPagamento.getValor()) < Uteis.arredondarForcando2CasasDecimais(movPagamento.getValorTotal())){
    				if (movPagamento.getFormaPagamento().getTipoFormaPagamento().equals("CA")) {
                                        getFacade().getMovPagamento().separarCartoesCancelados(movPagamento);
    					movPagamento.setCartaoCreditoVOs(Ordenacao.ordenarLista(movPagamento.getCartaoCreditoVOs(), "dataCompensacao"));
    					movPagamento = getFacade().getMovPagamento().atualizarListaCartaoCredito((MovPagamentoVO) movPagamento.getClone(true));
    				}
    				if (movPagamento.getFormaPagamento().getTipoFormaPagamento().equals("CH")) {
                                        getFacade().getMovPagamento().separarChequesCancelados(movPagamento);
    					movPagamento.setChequeVOs(Ordenacao.ordenarLista(movPagamento.getChequeVOs(), "dataCompensacao"));
    					movPagamento = getFacade().getMovPagamento().atualizarListaCheques((MovPagamentoVO) movPagamento.getClone(true));
    				}
    				
    				MovPagamentoVO movimento = getFacade().getMovPagamento().gerarMovpagamentoMovimentoCC((MovPagamentoVO) movPagamento.getClone(true));
    				if (movPagamento.getValor() != 0.0){
                                    novo = (MovPagamentoVO) movimento.getClone(true);
                                    novo.setValor(0.0);
                                    if(!movPagamento.getCredito()){
                                            novo.setCredito(false);
                                    }
                                    novo.setMovPagamentoOrigemCredito(0);
                                    novo.setReciboPagamento(movPagamento.getReciboPagamento());
                                    novo.setNovoObj(true);

                                    if (movPagamento.getFormaPagamento().getTipoFormaPagamento().equals("CA")) {
                                        Iterator m = novo.getCartaoCreditoVOs().iterator();
                                        while (m.hasNext()) {
                                            CartaoCreditoVO cartao = (CartaoCreditoVO) m.next();
                                            cartao.setSituacao("CA");
                                            cartao.setComposicao(null);
                                        }
                                        movPagamento = getFacade().getMovPagamento().retiraCartoesCancelados((MovPagamentoVO) movPagamento.getClone(true));
                                        movPagamento.getCartaoCreditoVOs().addAll(movPagamento.getCartoesCanceladoVOs());
                                    }
                                    if (movPagamento.getFormaPagamento().getTipoFormaPagamento().equals("CH")) {
                                        Iterator m = novo.getChequeVOs().iterator();
                                        while (m.hasNext()) {
                                            ChequeVO cheque = (ChequeVO) m.next();
                                            cheque.setSituacao("CA");
                                            cheque.setComposicao(null);
                                        }
                                        movPagamento = getFacade().getMovPagamento().retiraChequesCancelados((MovPagamentoVO) movPagamento.getClone(true));
                                        movPagamento.getChequeVOs().addAll(movPagamento.getChequesCanceladoVOs());
                                    }


                                    novo.setPagamentoMovParcelaVOs(new ArrayList<PagamentoMovParcelaVO>());
                                    List <PagamentoMovParcelaVO> parcelasPagamento = new ArrayList<PagamentoMovParcelaVO>();
                                    Iterator pmp = movPagamento.getPagamentoMovParcelaVOs().iterator();
                                    while (pmp.hasNext()){
                                            PagamentoMovParcelaVO pagParcela = (PagamentoMovParcelaVO) pmp.next();
                                            if(pagParcela.getMovPagamento() ==  0){
                                                    novo.getPagamentoMovParcelaVOs().add(pagParcela);
                                            } else {
                                                    parcelasPagamento.add(pagParcela);
                                            }
                                    }
                                    Iterator mppNovos = pagMovparcelasNovos.iterator();

                                    while (mppNovos.hasNext()){
                                            PagamentoMovParcelaVO pagParcela = (PagamentoMovParcelaVO) mppNovos.next();
                                            if(novo.getCodigo().intValue() == pagParcela.getMovPagamento().intValue()){
                                                    novo.getPagamentoMovParcelaVOs().add(pagParcela);
                                            }
                                    }

                                    movPagamento.setPagamentoMovParcelaVOs(new ArrayList<PagamentoMovParcelaVO>());
                                    movPagamento.getPagamentoMovParcelaVOs().addAll(parcelasPagamento);
                                    Ordenacao.ordenarLista(novo.getPagamentoMovParcelaVOs(), "dataVencimentoParcela");
                                    getFacade().getMovPagamento().incluirSemCommit(novo);
                                    Double valorTotal = new Double(movPagamento.getValor());
                                    movPagamento.setValorTotal(valorTotal);
    				} else{
    					Iterator mppNovos = pagMovparcelasNovos.iterator();
   		                while (mppNovos.hasNext()){
   		                	PagamentoMovParcelaVO pagParcela = (PagamentoMovParcelaVO) mppNovos.next();
   		                	if(movPagamento.getCodigo().intValue() == pagParcela.getMovPagamento().intValue()){
   		                		movPagamento.getPagamentoMovParcelaVOs().add(pagParcela);
   		                	}
   		                }
    				}
	                if(movPagamento.getMovPagamentoOrigemCredito() != null && movPagamento.getMovPagamentoOrigemCredito() > 0){
	                	movimento.setMovPagamentoOrigemCredito(movPagamento.getMovPagamentoOrigemCredito());
	                	 if (novo == null){
	 	                	movPagamento.setMovPagamentoOrigemCredito(0);
	 	                }
	                } else {
		                if (novo != null && novo.getCodigo() != 0){
		                	movimento.setMovPagamentoOrigemCredito(novo.getCodigo());
		                } else {
		                	movimento.setMovPagamentoOrigemCredito(movPagamento.getCodigo());
		                }
	                }
	               
	                getFacade().getMovPagamento().incluirSemCommit(movimento);
	                
	                movimento.setNovoObj(false);
	                movimento.setMovPagamentoEscolhida(true);
	       
	                pagamentosMovimento.add(movimento);
                        Ordenacao.ordenarLista(movPagamento.getPagamentoMovParcelaVOs(), "codigo");
	                getFacade().getMovPagamento().alterarSemCommit(movPagamento);
                        if (novo != null && novo.getCodigo() != 0){
                           getFacade().getPagamentoMovParcela().alterarPagamentoMovParcelas(novo.getCodigo(), novo.getPagamentoMovParcelaVOs(), novo.getReciboPagamento().getCodigo());
                        }
	                novo = null;
	
    			}
    		}
                
                Iterator mpc = produtosCancelados.iterator();
        
        	while (mpc.hasNext()){
        		MovProdutoVO cancelado = (MovProdutoVO) mpc.next();
        		getFacade().getMovProduto().alterarSemCommit(cancelado);
        	}
    		
    		Iterator l = listaReciboPagamento.iterator();

        	while (l.hasNext()) {
        		ReciboPagamentoVO recibo = (ReciboPagamentoVO) l.next();
        		getFacade().getMovPagamento().setarProdutosPagos(recibo.getCodigo());
        	}
        	
    		
    	} catch (Exception e) {
    		throw e;
    	}
    	return pagamentosMovimento;
    }

	public void incluirOperacaoBonus(BonusContratoVO bonusContratoVO, boolean alterarVigenciaIndependenteDaSituacao, Date dataPrevistaRenovar, boolean controleTransacao, Date dataProcessarSintetico) throws Exception {
        try {
            if (controleTransacao)
              con.setAutoCommit(false);
            Date dataVigente = bonusContratoVO.getContratoVO().getVigenciaAteAjustada();
            //WM - Contratos inativos não deve alterar as datas de vigência,
            //somente gravar histórico da operação e incluir o período de acesso respectivo
            boolean alterarVigencia = alterarVigenciaIndependenteDaSituacao;
            if (!alterarVigencia){
                alterarVigencia = bonusContratoVO.getContratoVO().getSituacao().equals("AT");
            }
            if (alterarVigencia){
                bonusContratoVO.getContratoVO().setVigenciaAteAjustada(bonusContratoVO.getDataTermino());
                bonusContratoVO.getContratoVO().setDataPrevistaRenovar((dataPrevistaRenovar != null) ? dataPrevistaRenovar : bonusContratoVO.getDataTermino());
                bonusContratoVO.getContratoVO().setDataPrevistaRematricula(bonusContratoVO.getDataTermino());
                bonusContratoVO.inicializarDadosHistoricoContrato();
                getFacade().getContrato().alterarDatasVigenciaContrato(bonusContratoVO.getContratoVO());
            }
            alterarMatriculas(bonusContratoVO.getContratoVO(), dataVigente, bonusContratoVO.getDataTermino(),false,null, bonusContratoVO.getAcrescentarDiaContrato().equals("AC") ? "BA" : "BR");
            bonusContratoVO.inicializarDadosOperacaoContratoBonus(dataVigente);
            bonusContratoVO.inicializarDadosPeridoAcesso();
            atualizaDataFimDeProdutoComVigenciaDeContrato(bonusContratoVO.getContratoVO().getCodigo(), null, bonusContratoVO.getContratoVO().getVigenciaAteAjustada());
            if(alterarVigencia){ //se o contrato estiver vigente, renovações futuras serão ajustadas
                ajustarRenovacoesFuturas(bonusContratoVO.getContratoVO(), (bonusContratoVO.getAcrescentarDiaContrato().equals("AC") ? "Bônus de Acréscimo": bonusContratoVO.getAcrescentarDiaContrato().equals("BC") ? "Bônus Coletivo" : bonusContratoVO.getAcrescentarDiaContrato().equals("BX") ? "Exclusão de Bônus Coletivo" :"Bônus de Redução"), bonusContratoVO.getResponsavelOperacao(), bonusContratoVO.getContratoVO().getCodigo());
                if (bonusContratoVO.getContratoVO().getPlano().getQuantidadeCompartilhamentos() > 0) {
                    atualizarVigenciaContratosDependentes(bonusContratoVO.getContratoVO());
                }
            }
            if (controleTransacao)
                con.commit();


            if (!bonusContratoVO.getAcrescentarDiaContrato().equals("BX")) {
                getFacade().getZWFacade().atualizarSintetico(getFacade().getCliente().
                                consultarPorCodigoPessoa(bonusContratoVO.getContratoVO().getPessoa().getCodigo(),
                                        Uteis.NIVELMONTARDADOS_SITUACAOCLIENTESINTETICODW),
                        dataProcessarSintetico == null ? Calendario.hoje() : dataProcessarSintetico, SituacaoClienteSinteticoEnum.GRUPO_CONTRATO, false);
            }
        } catch (Exception e) {
            if (controleTransacao){
                con.rollback();
                con.setAutoCommit(true);
            }
            throw e;
        } finally {
            if (controleTransacao)
              con.setAutoCommit(true);
        }
    }

    public void incluirOperacaoBonusSemCommit(BonusContratoVO bonusContratoVO) throws Exception {
        Date dataVigente = bonusContratoVO.getContratoVO().getVigenciaAteAjustada();
        //WM - Contratos inativos não deve alterar as datas de vigência,
        //somente gravar histórico da operação e incluir o período de acesso respectivo
        if (bonusContratoVO.getContratoVO().getSituacao().equals("AT")) {
            ContratoVO aux = (ContratoVO) bonusContratoVO.getContratoVO().getClone(false);
            aux.setVigenciaAteAjustada(bonusContratoVO.getDataTermino());
            aux.setDataPrevistaRenovar(bonusContratoVO.getDataTermino());
            aux.setDataPrevistaRematricula(bonusContratoVO.getDataTermino());
            getFacade().getContrato().alterarDatasVigenciaContrato(aux);
            alterarMatriculas(aux, dataVigente, bonusContratoVO.getDataTermino(),false,null, bonusContratoVO.getAcrescentarDiaContrato().equals("AC") ? "BA" : "BR");
        }
        bonusContratoVO.inicializarDadosOperacaoContratoBonus(dataVigente);
        bonusContratoVO.inicializarDadosHistoricoContrato();
        bonusContratoVO.inicializarDadosPeridoAcesso();
        if(bonusContratoVO.getContratoVO().getSituacao().equals("AT")){ //se o contrato estiver vigente, renovações futuras serão ajustadas
           ajustarRenovacoesFuturas(bonusContratoVO.getContratoVO(), (bonusContratoVO.getAcrescentarDiaContrato().equals("AC") ? "Bônus de Acréscimo": "Bônus de Redução"), bonusContratoVO.getResponsavelOperacao(), bonusContratoVO.getContratoVO().getCodigo());
        }
    }

    public void incluirOperacaoAtestado(AtestadoContratoVO atestadoContratoVO, UsuarioVO usuarioVO) throws Exception {
        GestaoAulaService gestaoAulaService = new GestaoAulaService(con, (String) JSFUtilities.getFromSession("key"));
        try {
            con.setAutoCommit(false);
            //lancamento Retroativo nao mofidicar o passado, portanto tabela de historio e periodoacesso nao e modificado.
            //so pode-se alterar situação do contrato se ainda não venceu,
            //caso contrário, apenas gerar um período de acesso
            if (Uteis.getCompareData(atestadoContratoVO.getDataInicio(),
                    negocio.comuns.utilitarias.Calendario.hoje()) >= 0) {//se começa hoje ou no futuro

                alterarMatriculas(atestadoContratoVO.getContratoVO(),
                        atestadoContratoVO.getContratoVO().getVigenciaAteAjustada(),
                        atestadoContratoVO.getDataTerminoRetorno(),false,atestadoContratoVO.getDataInicioRetorno(),"AT");

                //deve ser nessa ordem de execução, por causa das validações que pesquisam os registros existentes!!!
                //so pode-se alterar situação do contrato se ainda não venceu
                if (Calendario.menorOuIgual(Calendario.hoje(), atestadoContratoVO.getContratoVO().getVigenciaAteAjustada())) {
                    atestadoContratoVO.inicializarDadosHistoricoContratoAtestado();
                    atestadoContratoVO.alterarSituacaoContrato(atestadoContratoVO.getDataTerminoRetorno());
                }
                atestadoContratoVO.inicializarDadosOperacaoContrato(false);
                atestadoContratoVO.inicializarDadosPeriodoAcessoCliente();

            } else if (Uteis.getCompareData(atestadoContratoVO.getDataTermino(),
                    negocio.comuns.utilitarias.Calendario.hoje()) >= 0) {//se a data inicio é antes mas a data termino acaba hoje ou no futuro

                alterarMatriculas(atestadoContratoVO.getContratoVO(),
                        atestadoContratoVO.getContratoVO().getVigenciaAteAjustada(),
                        atestadoContratoVO.getDataTerminoRetorno(), false,atestadoContratoVO.getDataInicioRetorno(),"AT");
                Date inicioOriginal =  atestadoContratoVO.getDataInicio();
                //coloca a o atestado iniciando hoje para nao alterar o passado
              
                atestadoContratoVO.setDataInicio(Calendario.hoje());
                //so pode-se alterar situação do contrato se ainda não venceu
                if (Calendario.menorOuIgual(Calendario.hoje(), atestadoContratoVO.getContratoVO().getVigenciaAteAjustada())) {
                    atestadoContratoVO.inicializarDadosHistoricoContratoAtestado();
                    atestadoContratoVO.alterarSituacaoContrato(atestadoContratoVO.getDataTerminoRetorno());
                }
                atestadoContratoVO.setDataInicio(inicioOriginal);
                atestadoContratoVO.inicializarDadosOperacaoContrato(false);
                atestadoContratoVO.inicializarDadosPeriodoAcessoCliente();


            } else {//se inicia e termina no passado, nao alterar histórico, nem período acesso
                //so pode-se alterar situação do contrato se ainda não venceu
                alterarMatriculas(atestadoContratoVO.getContratoVO(),
                        atestadoContratoVO.getContratoVO().getVigenciaAteAjustada(),
                        atestadoContratoVO.getDataTerminoRetorno(),false,atestadoContratoVO.getDataInicioRetorno(),"AT");

                if (Calendario.menorOuIgual(Calendario.hoje(), atestadoContratoVO.getContratoVO().getVigenciaAteAjustada())) {
                    atestadoContratoVO.alterarSituacaoContrato(atestadoContratoVO.getDataTerminoRetorno());
                    atestadoContratoVO.alterarUltimoHistorico();
                }
                atestadoContratoVO.inicializarDadosOperacaoContrato(true);
                atestadoContratoVO.inicializarDadosPeriodoAcessoCliente();
            }
            atualizaDataFimDeProdutoComVigenciaDeContrato(atestadoContratoVO.getContratoVO().getCodigo(),null,atestadoContratoVO.getContratoVO().getVigenciaAteAjustada());
            if (Calendario.menorOuIgual(Calendario.hoje(), atestadoContratoVO.getContratoVO().getVigenciaAteAjustada())) { //se o contrato estiver vigente, renovações futuras serão ajustadas
                ajustarRenovacoesFuturas(atestadoContratoVO.getContratoVO(), "Atestado",atestadoContratoVO.getResponsavelOperacao(), atestadoContratoVO.getContratoVO().getCodigo());
            }

                gestaoAulaService.desmarcarAulasPorAfastamento(usuarioVO, OrigemSistemaEnum.ZW,
                        atestadoContratoVO.getContratoVO(),
                        atestadoContratoVO.getDataInicio(), atestadoContratoVO.getDataTermino());

            con.commit();

            getFacade().getZWFacade().atualizarSintetico(getFacade().getCliente().
                            consultarPorCodigoPessoa(atestadoContratoVO.getContratoVO().getPessoa().getCodigo(),
                                    Uteis.NIVELMONTARDADOS_SITUACAOCLIENTESINTETICODW),
                    Calendario.hoje(), SituacaoClienteSinteticoEnum.GRUPO_CONTRATO, false);
        } catch (Exception e) {
            con.rollback();
            con.setAutoCommit(true);
            throw e;
        } finally {
            con.setAutoCommit(true);
            gestaoAulaService = null;
        }

    }

    public void incluirOperacaoRetornoAtestado(AtestadoContratoVO atestadoContratoVO,
                                               UsuarioVO usuarioLogadoVO) throws Exception {
        AulaDesmarcada aulaDesmarcadaDAO;
        ZillyonWebFacade zillyonWebFacade;
        try {
            con.setAutoCommit(false);
            aulaDesmarcadaDAO = new AulaDesmarcada(this.con);
            zillyonWebFacade = new ZillyonWebFacade(this.con);

            ContratoOperacaoVO operacao = consultarOperacaoContratoPorDataInicioCodigoContratoTipoOperacao(Calendario.hoje(), atestadoContratoVO.getContratoVO().getCodigo(),"AT", false, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
            if (operacao != null) {
                ValidacaoHistoricoContrato.validarOperacaoRetornoNoMesmoDiaOperacaoOrigem(
                        operacao.getDataInicioEfetivacaoOperacao(),
                        Calendario.hoje());
            }
            atestadoContratoVO.alterarOperacaoAtestado();
            Date dataFinalContrato = atestadoContratoVO.inicializarDadosRetornoAtestadoHistoricoContrato();
            alterarMatriculas(atestadoContratoVO.getContratoVO(), atestadoContratoVO.getContratoVO().getVigenciaAteAjustada(), dataFinalContrato,false, Calendario.hoje(),"");
            atestadoContratoVO.alterarSituacaoContrato(dataFinalContrato);
            atestadoContratoVO.inicializarDadosRetornoManualPeriodoAcesso();
            atualizaDataFimDeProdutoComVigenciaDeContrato(atestadoContratoVO.getContratoVO().getCodigo(),null,atestadoContratoVO.getContratoVO().getVigenciaAteAjustada());
            ajustarRenovacoesFuturas(atestadoContratoVO.getContratoVO(), "Retorno de Atestado", atestadoContratoVO.getResponsavelOperacao(), atestadoContratoVO.getCodigo());
            aulaDesmarcadaDAO.excluirAulaDesmarcadaPorRetornoAfastamentoAntecipado(atestadoContratoVO.getContratoVO().getCodigo(), Calendario.getMaior(atestadoContratoVO.getDataInicio(), Calendario.hoje()), atestadoContratoVO.getDataTermino());
            con.commit();

            zillyonWebFacade.atualizarSintetico(getFacade().getCliente().
                            consultarPorCodigoPessoa(atestadoContratoVO.getContratoVO().getPessoa().getCodigo(),
                                    Uteis.NIVELMONTARDADOS_SITUACAOCLIENTESINTETICODW),
                    Calendario.hoje(), SituacaoClienteSinteticoEnum.GRUPO_CONTRATO, false);

            //LOG - INICIO
            try {
                atestadoContratoVO.setObjetoVOAntesAlteracao(new AtestadoContratoVO());
                atestadoContratoVO.setNovoObj(true);
                registrarLogObjetoVO(atestadoContratoVO, atestadoContratoVO.getCodigo(), "RETORNOATESTADOCONTRATO", atestadoContratoVO.getContratoVO().getPessoa().getCodigo());
            } catch (Exception e) {
                registrarLogErroObjetoVO("RETORNOATESTADOCONTRATO", atestadoContratoVO.getContratoVO().getPessoa().getCodigo(),
                        "ERRO AO GERAR LOG DE ALTERAÇÃO RETORNO ATESTADO CONTRATO", usuarioLogadoVO.getNome(), usuarioLogadoVO.getUserOamd(), this.con);
                e.printStackTrace();
            }
        } catch (Exception e) {
            con.rollback();
            con.setAutoCommit(true);
            throw e;
        } finally {
            con.setAutoCommit(true);
            aulaDesmarcadaDAO = null;
            zillyonWebFacade = null;
        }

    }

    public void incluirOperacaoCarencia(CarenciaContratoVO carenciaContratoVO, UsuarioVO usuarioVO) throws Exception {
        GestaoAulaService gestaoAulaService = new GestaoAulaService(con, (String) JSFUtilities.getFromSession("key"));
        try {
            con.setAutoCommit(false);
            alterarMatriculas(carenciaContratoVO.getContratoVO(),
                    carenciaContratoVO.getContratoVO().getVigenciaAteAjustada(), carenciaContratoVO.getDataTerminoRetorno(),false,carenciaContratoVO.getDataInicioRetorno(),"CR");

            if (Uteis.getCompareData(carenciaContratoVO.getDataInicio(),
                    negocio.comuns.utilitarias.Calendario.hoje()) >= 0) {//se começa hoje ou no futuro
                if (Calendario.menorOuIgual(Calendario.hoje(), carenciaContratoVO.getContratoVO().getVigenciaAteAjustada())) {
                    carenciaContratoVO.inicializarDadosHistoricoContratoCarencia();
                    carenciaContratoVO.alterarSituacaoContrato(carenciaContratoVO.getDataTerminoRetorno());
                }
                carenciaContratoVO.inicializarDadosOperacaoContrato(false);
                carenciaContratoVO.inicializarDadosPeriodoAcessoCliente();

            } else if (Uteis.getCompareData(carenciaContratoVO.getDataTermino(),
                    negocio.comuns.utilitarias.Calendario.hoje()) >= 0) {//se a data inicio é antes mas a data termino acaba hoje ou no futuro
                Date inicioOriginal = carenciaContratoVO.getDataInicio();
                //so pode-se alterar situação do contrato se ainda não venceu
                if (Calendario.menorOuIgual(Calendario.hoje(), carenciaContratoVO.getContratoVO().getVigenciaAteAjustada())) {
                    carenciaContratoVO.inicializarDadosHistoricoContratoCarencia();
                    carenciaContratoVO.alterarSituacaoContrato(carenciaContratoVO.getDataTerminoRetorno());
                }
                carenciaContratoVO.setDataInicio(inicioOriginal);
                carenciaContratoVO.inicializarDadosOperacaoContrato(false);
                carenciaContratoVO.inicializarDadosPeriodoAcessoCliente();

            } else {//se inicia e termina no passado, nao alterar histórico, nem período acesso
                if (Calendario.menorOuIgual(Calendario.hoje(), carenciaContratoVO.getContratoVO().getVigenciaAteAjustada())) {
                    carenciaContratoVO.alterarSituacaoContrato(carenciaContratoVO.getDataTerminoRetorno());
                    carenciaContratoVO.alterarUltimoHistorico();
                }
                carenciaContratoVO.inicializarDadosOperacaoContrato(true);
                carenciaContratoVO.inicializarDadosPeriodoAcessoCliente();
            }
            atualizaDataFimDeProdutoComVigenciaDeContrato(carenciaContratoVO.getContratoVO().getCodigo(),null,
                    carenciaContratoVO.getContratoVO().getVigenciaAteAjustada());
            ajustarRenovacoesFuturas(carenciaContratoVO.getContratoVO(), "Carência" , carenciaContratoVO.getResponsavelOperacao(),carenciaContratoVO.getContratoVO().getCodigo());

            gestaoAulaService.desmarcarAulasPorAfastamento(usuarioVO, OrigemSistemaEnum.ZW,
                    carenciaContratoVO.getContratoVO(), carenciaContratoVO.getDataInicio(), carenciaContratoVO.getDataTermino());

            con.commit();

            getFacade().getZWFacade().atualizarSintetico(getFacade().getCliente().
                            consultarPorCodigoPessoa(carenciaContratoVO.getContratoVO().getPessoa().getCodigo(),
                                    Uteis.NIVELMONTARDADOS_SITUACAOCLIENTESINTETICODW),
                    Calendario.hoje(), SituacaoClienteSinteticoEnum.GRUPO_CONTRATO, false);
        } catch (Exception e) {
            con.rollback();
            con.setAutoCommit(true);
            throw e;
        } finally {
            con.setAutoCommit(true);
            gestaoAulaService = null;
        }
    }

    public void incluirOperacaoRetornoCarencia(CarenciaContratoVO carenciaContratoVO) throws Exception {
        try {
            con.setAutoCommit(false);
            ContratoOperacaoVO operacao = getFacade().getContratoOperacao().consultarOperacaoContratoPorDataInicioCodigoContratoTipoOperacao(Calendario.hoje(),
                    carenciaContratoVO.getContratoVO().getCodigo(),"CR", false, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
            if (operacao != null) {
                ValidacaoHistoricoContrato.validarOperacaoRetornoNoMesmoDiaOperacaoOrigem(
                        operacao.getDataInicioEfetivacaoOperacao(),
                        Calendario.hoje());
            }
            carenciaContratoVO.alterarOperacaoCarencia();
            Date dataFinalContrato = carenciaContratoVO.inicializarDadosRetornoCarenciaHistoricoContrato();
            alterarMatriculas(carenciaContratoVO.getContratoVO(),
                    carenciaContratoVO.getContratoVO().getVigenciaAteAjustada(),
                    dataFinalContrato,false, Calendario.hoje(),"");
            carenciaContratoVO.alterarSituacaoContrato(dataFinalContrato);
            carenciaContratoVO.inicializarDadosRetornoManualPeriodoAcesso();
            ClienteVO cliente = getFacade().getCliente().consultarPorCodigoPessoa(
                    carenciaContratoVO.getContratoVO().getPessoa().getCodigo(),
                    Uteis.NIVELMONTARDADOS_DADOSENTIDADESPRINCIPAIS);
            atualizaDataFimDeProdutoComVigenciaDeContrato(carenciaContratoVO.getContratoVO().getCodigo(),null,
                    carenciaContratoVO.getContratoVO().getVigenciaAteAjustada());
            ajustarRenovacoesFuturas(carenciaContratoVO.getContratoVO(), "Retorno Férias", carenciaContratoVO.getResponsavelOperacao(), carenciaContratoVO.getContratoVO().getCodigo());
            getFacade().getAulaDesmarcada().excluirAulaDesmarcadaPorRetornoAfastamentoAntecipado(carenciaContratoVO.getContratoVO().getCodigo(), Calendario.getMaior(carenciaContratoVO.getDataInicio(), Calendario.hoje()), carenciaContratoVO.getDataTermino());
            con.commit();

            getFacade().getZWFacade().atualizarSintetico(getFacade().getCliente().
                            consultarPorCodigoPessoa(cliente.getPessoa().getCodigo(),
                                    Uteis.NIVELMONTARDADOS_SITUACAOCLIENTESINTETICODW),
                    Calendario.hoje(), SituacaoClienteSinteticoEnum.GRUPO_CONTRATO, false);
        } catch (Exception e) {
            con.rollback();
            con.setAutoCommit(true);
            throw e;
        } finally {
            con.setAutoCommit(true);
        }

    }

    public void incluirOperacaoAlterarHorario(ContratoOperacaoVO obj, ContratoVO contratoAntigo, ContratoVO contratoNovo, Double valor, boolean liberado) throws Exception {
        try {
            con.setAutoCommit(false);
            incluirSemCommit(obj, false);
            if(obj.getOperacaoPaga()){
                if(liberado){
                    contratoNovo.setValorBaseCalculo(contratoAntigo.getValorBaseCalculo());
                } else {
                    contratoNovo.setValorBaseCalculo(contratoAntigo.getValorBaseCalculo() + valor);
                }
            } else {
            	contratoNovo.setValorBaseCalculo(contratoAntigo.getValorBaseCalculo() - valor);
            }
            getFacade().getContrato().alterarContratoHorario(contratoNovo);
            if (valor != 0.0) {
                if (obj.getOperacaoPaga()) {
                    MovParcelaVO parcela = new MovParcelaVO();
                    parcela.getContrato().setCodigo(obj.getContrato());
                    parcela.setDataRegistro(obj.getDataOperacao());
                    parcela.setDataVencimento(obj.getDataOperacao());
                    parcela.setDataAlteracaoManual(Calendario.hoje());
                    parcela.setDescricao("Altera - Horário");
                    parcela.setResponsavel(obj.getResponsavel());
                    parcela.setValorParcela(valor);
                    parcela.setValorBaseCalculo(valor);
                    parcela.setEmpresa(contratoNovo.getEmpresa());
                    parcela.setPessoa(contratoNovo.getPessoa());
                    inicializarMovProduto(obj, parcela, contratoNovo, liberado);
                    getFacade().getZWFacade().incluirMovParcelaSemCommit(parcela);
                } else {
                	String descricao = "Deposito do troco da alteração de horário";
                	MovimentoContaCorrenteClienteVO conta = new MovimentoContaCorrenteClienteVO();
                	conta.setMovPagamentosVOs(inicializarMovProdutoTrocoManutencaoModalidadeAlteracaoHorario(obj.getResponsavel(), valor, contratoAntigo, descricao, "AH"));
                    if(!conta.getMovPagamentosVOs().isEmpty()){
	                    conta.setDescricao(descricao);
	                    conta.setDataRegistro(negocio.comuns.utilitarias.Calendario.hoje());
	                    conta.setValor(0.0);
	                    conta.setTipoMovimentacao("CR");
	                    conta.setResponsavelAutorizacao(obj.getResponsavel());
	                    conta.setPessoa(contratoNovo.getPessoa());
	                    Iterator mp =conta.getMovPagamentosVOs().iterator();
	                    while(mp.hasNext()){
	                    	MovPagamentoVO movPag = (MovPagamentoVO) mp.next();
	                    	conta.setValor(conta.getValor() + movPag.getValor());
	                    }
	                    getFacade().getMovimentoContaCorrenteCliente().adicionarmovimento(conta);
                    }
                }
            }

            con.commit();
        } catch (Exception e) {
            con.rollback();
            con.setAutoCommit(true);
            throw e;
        } finally {
            con.setAutoCommit(true);
        }

    }

    public void inicializarMovProduto(ContratoOperacaoVO obj, MovParcelaVO movParcela, ContratoVO contrato, boolean liberado) throws Exception {
        try {
            MovProdutoVO movProdutoVO = new MovProdutoVO();
            movProdutoVO.setProduto(getFacade().getProduto().consultarPorTipoProduto("AH", false, Uteis.NIVELMONTARDADOS_DADOSBASICOS));
            movProdutoVO.setApresentarMovProduto(false);
            movProdutoVO.setDescricao(movParcela.getDescricao());
            movProdutoVO.setMesReferencia(Uteis.getMesReferenciaData(negocio.comuns.utilitarias.Calendario.hoje()));
            movProdutoVO.setQuantidade(1);
            movProdutoVO.setAnoReferencia(Uteis.getAnoData(negocio.comuns.utilitarias.Calendario.hoje()));
            movProdutoVO.setDataInicioVigencia(negocio.comuns.utilitarias.Calendario.hoje());
            movProdutoVO.setDataFinalVigencia(negocio.comuns.utilitarias.Calendario.hoje());
            movProdutoVO.setDataLancamento(negocio.comuns.utilitarias.Calendario.hoje());
            movProdutoVO.setResponsavelLancamento(obj.getResponsavel());
            movProdutoVO.setEmpresa(contrato.getEmpresa());
            movProdutoVO.setPessoa(contrato.getPessoa());
            movProdutoVO.setContrato(contrato);
            if (liberado) {
                movProdutoVO.setResponsavelLancamento(obj.getResponsavelLiberacao());
                movProdutoVO.setQuitado(true);
                movProdutoVO.setSituacao("PG");
                movProdutoVO.setValorDesconto(movParcela.getValorParcela());
                movProdutoVO.setPrecoUnitario(movParcela.getValorParcela());
                movProdutoVO.setTotalFinal(0.0);
                movParcela.setValorParcela(0.0);
                movParcela.setSituacao("PG");
            } else {
                movProdutoVO.setQuitado(false);
                movProdutoVO.setSituacao("EA");
                movProdutoVO.setValorDesconto(0.0);
                movProdutoVO.setPrecoUnitario(movParcela.getValorParcela());
                movProdutoVO.setTotalFinal(movParcela.getValorParcela());
                movParcela.setSituacao("EA");
            }
            getFacade().getMovProduto().incluirSemCommit(movProdutoVO);
            MovProdutoParcelaVO movProdutoParcela = new MovProdutoParcelaVO();
            movProdutoParcela.setMovProduto(movProdutoVO.getCodigo());
            movProdutoParcela.setMovProdutoVO(movProdutoVO);
            movProdutoParcela.setValorPago(movProdutoVO.getTotalFinal());
            movParcela.getMovProdutoParcelaVOs().add(movProdutoParcela);
        } catch (Exception e) {
            throw e;
        }
    }

    public void inicializarMovProdutoNegativo(ContratoOperacaoVO obj, Double valor, ContratoVO contrato) throws Exception {
        try {
            MovProdutoVO movProdutoVO = new MovProdutoVO();

            ProdutoVO prodAlteracao = getFacade().getProduto().consultarPorTipoProduto("AH", false, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
            if (prodAlteracao.getCodigo() == 0) {
                throw new Exception("Não foi encontrado nenhum produto do tipo Alterar Horário");
            }
            movProdutoVO.setProduto(prodAlteracao);
            movProdutoVO.setApresentarMovProduto(false);
            movProdutoVO.setDescricao("Deposito do troco da alteração de horário");
            movProdutoVO.setMesReferencia(Uteis.getMesReferenciaData(negocio.comuns.utilitarias.Calendario.hoje()));
            movProdutoVO.setQuantidade(1);
            movProdutoVO.setAnoReferencia(Uteis.getAnoData(negocio.comuns.utilitarias.Calendario.hoje()));
            movProdutoVO.setDataInicioVigencia(negocio.comuns.utilitarias.Calendario.hoje());
            movProdutoVO.setDataFinalVigencia(negocio.comuns.utilitarias.Calendario.hoje());
            movProdutoVO.setDataLancamento(negocio.comuns.utilitarias.Calendario.hoje());
            movProdutoVO.setResponsavelLancamento(obj.getResponsavel());
            movProdutoVO.setEmpresa(contrato.getEmpresa());
            movProdutoVO.setPessoa(contrato.getPessoa());
            movProdutoVO.setContrato(contrato);
            movProdutoVO.setQuitado(true);
            movProdutoVO.setSituacao("PG");
            movProdutoVO.setValorDesconto(0.0);
            movProdutoVO.setPrecoUnitario(-1 * valor);
            movProdutoVO.setTotalFinal(-1 * valor);
            getFacade().getMovProduto().incluirSemCommit(movProdutoVO);
        } catch (Exception e) {
            throw e;
        }
    }
    public void incluirOperacaoManutencaoModalidade(List<ContratoModalidadeVO> listaModalidadeAdicionada,
            List<ContratoModalidadeVO> listaModalidadeExcluida,
            List<ContratoModalidadeVO> listaModalidadeAlterada,
            List<ContratoOperacaoVO> listaContratoOperacao,
            ContratoVO contratoNovo,
            ContratoVO contratoAntigo,
            Double valor,
            Double valorPagar,
            Boolean operacaoPaga,
            Boolean liberado,
            UsuarioVO resp,
            Integer diasRestantes,
            boolean alteracaoManual,
            List<HorarioTurmaVO> listaTodosHorarioTurma, List<MovParcelaVO> listaParcelaVOS) throws Exception {
        try {
            con.setAutoCommit(false);
            UsuarioVO responsavelOperacao = null;
            for (ContratoOperacaoVO ope : listaContratoOperacao) {
                incluirSemCommit(ope, false);
                responsavelOperacao = ope.getResponsavel();
            }
            if(operacaoPaga){
                if(liberado){
                    contratoNovo.setValorBaseCalculo(contratoAntigo.getValorBaseCalculo());
                } else {
                    Double valorSomar = alteracaoManual ? valorPagar : valor;
                    contratoNovo.setValorBaseCalculo(contratoAntigo.getValorBaseCalculo() + valorSomar);
                }
            } else {
                Double valorReduzir = alteracaoManual ? valorPagar : valor;
            	contratoNovo.setValorBaseCalculo(contratoAntigo.getValorBaseCalculo() - valorReduzir);
            }
            getFacade().getContrato().alterarContratoManutencaoModalidade(listaModalidadeAdicionada, listaModalidadeExcluida, listaModalidadeAlterada, contratoNovo, contratoAntigo,responsavelOperacao);
            VinculoControle.processarVinculosManutencaoModalidade(contratoNovo, contratoAntigo, resp);
            if (valor != 0.0 || (alteracaoManual && valorPagar != 0.0)) {
                if (operacaoPaga) {
                    if (UteisValidacao.emptyList(listaParcelaVOS)){
                        listaParcelaVOS = new ArrayList<MovParcelaVO>();
                        MovParcelaVO parcela = new MovParcelaVO();
                        parcela.getContrato().setCodigo(contratoNovo.getCodigo());
                        parcela.setDataRegistro(negocio.comuns.utilitarias.Calendario.hoje());
                        parcela.setDataVencimento(negocio.comuns.utilitarias.Calendario.hoje());
                        parcela.setDescricao("Manutenção Modalidade");
                        parcela.setResponsavel(resp);
                        parcela.setValorParcela(valorPagar);
                        parcela.setValorBaseCalculo(valorPagar);
                        parcela.setEmpresa(contratoNovo.getEmpresa());
                        parcela.setPessoa(contratoNovo.getPessoa());
                        parcela.setSituacao("EA");
                        listaParcelaVOS.add(parcela);
                    }
                    inicializarMovProdutoManutencaoModalidade(resp, listaParcelaVOS, contratoNovo, liberado, listaModalidadeAdicionada,listaModalidadeAlterada, (alteracaoManual ? 0.0 : (valor - valorPagar)), diasRestantes, valorPagar);
                    for ( MovParcelaVO parcelaVO: listaParcelaVOS) {
                        if(UteisValidacao.emptyNumber(parcelaVO.getCodigo())) {
                            getFacade().getZWFacade().incluirMovParcelaSemCommit(parcelaVO);
                        } else {
                            for(MovProdutoParcelaVO movProdutoParcelaVO : parcelaVO.getMovProdutoParcelaVOs()){
                                movProdutoParcelaVO.setMovParcela(parcelaVO.getCodigo());
                                getFacade().getMovProdutoParcela().incluir(movProdutoParcelaVO);
                            }
                            getFacade().getZWFacade().alterarMovParcela(parcelaVO);
                        }
                    }

                } else {
                	String descricao = "Deposito do troco da Manutenção Modalidade";
                	MovimentoContaCorrenteClienteVO conta = new MovimentoContaCorrenteClienteVO();
                        Double valorCreditar = alteracaoManual ? valorPagar : valor;
                	conta.setMovPagamentosVOs(inicializarMovProdutoTrocoManutencaoModalidadeAlteracaoHorario(resp, valorCreditar, contratoAntigo, descricao, "MM"));
                    if(!conta.getMovPagamentosVOs().isEmpty() && !UteisValidacao.emptyNumber(valorCreditar)){
	                    conta.setDescricao(descricao);
	                    conta.setDataRegistro(negocio.comuns.utilitarias.Calendario.hoje());
	                    conta.setValor(0.0);
	                    conta.setTipoMovimentacao("CR");
	                    conta.setResponsavelAutorizacao(resp);
	                    conta.setPessoa(contratoNovo.getPessoa());
	                    Iterator mp =conta.getMovPagamentosVOs().iterator();
	                    while(mp.hasNext()){
	                    	MovPagamentoVO movPag = (MovPagamentoVO) mp.next();
	                    	conta.setValor(conta.getValor() + movPag.getValor());
	                    }
	                    
	                    getFacade().getMovimentoContaCorrenteCliente().adicionarmovimento(conta);
                    }
                    
                }
            }
            alteracaoManutencaoModalidadeCreditoTreino(contratoNovo, resp, listaTodosHorarioTurma);
            con.commit();
            getFacade().getZWFacade().atualizarSintetico(getFacade().getCliente().
                            consultarPorCodigoPessoa(contratoNovo.getPessoa().getCodigo(),
                                    Uteis.NIVELMONTARDADOS_SITUACAOCLIENTESINTETICODW),
                    Calendario.hoje(), SituacaoClienteSinteticoEnum.GRUPO_CONTRATO, false);
        } catch (Exception e) {
            con.rollback();
            con.setAutoCommit(true);
            throw e;
        } finally {
            con.setAutoCommit(true);
        }

    }

    private void alteracaoManutencaoModalidadeCreditoTreino(ContratoVO contratoVO, UsuarioVO usuarioVO, List<HorarioTurmaVO> listaTodosHorarioTurma)throws Exception{
        if (!contratoVO.isVendaCreditoTreino()){
            return ;
        }
        if ((contratoVO.getDiferencaQtdeCreditoTreinoManutencaoModalidade() != null) && (contratoVO.getDiferencaQtdeCreditoTreinoManutencaoModalidade() != 0)){
            ControleCreditoTreinoVO controleCreditoTreinoVO = new ControleCreditoTreinoVO();
            controleCreditoTreinoVO.setTipoOperacaoCreditoTreinoEnum(TipoOperacaoCreditoTreinoEnum.MANUTENCAO_MODALIDADE);
            if (contratoVO.getDiferencaQtdeCreditoTreinoManutencaoModalidade() > 0){
                controleCreditoTreinoVO.setObservacao("Comprou novo(s) crédito(s) após aumentar a quantidade de vezes por semana.");
            }else{
                controleCreditoTreinoVO.setObservacao("Retirou crédito(s) após diminuir a quantidade de vezes por semana.");
            }
            controleCreditoTreinoVO.setQuantidade(contratoVO.getDiferencaQtdeCreditoTreinoManutencaoModalidade());
            controleCreditoTreinoVO.setContratoVO(contratoVO);
            controleCreditoTreinoVO.setDataOperacao(Calendario.hoje());
            controleCreditoTreinoVO.setUsuarioVO(usuarioVO);
            getFacade().getControleCreditoTreino().incluirSemCommit(controleCreditoTreinoVO, null, getFacade().getSituacaoClienteSinteticoDW(), null);
        }
        if (contratoVO.getQtdeVezesSemanaAposManutencaoModalidade() != null){
            getFacade().getContratoDuracaoCreditoTreino().alterarVezesSemana(contratoVO.getContratoDuracao().getContratoDuracaoCreditoTreinoVO(), contratoVO.getQtdeVezesSemanaAposManutencaoModalidade());
        }
        if (contratoVO.getVigenciaTurmaCreditoTreinoAte() != null){
            for (HorarioTurmaVO horarioTurmaVO: listaTodosHorarioTurma){
                Date dataFimMatricula = getFacade().getMatriculaAlunoHorarioTurma().pesquisarDataFimMatriculaTurmaCreditoTreino(contratoVO.getCodigo(), horarioTurmaVO.getCodigo());
                getFacade().getMatriculaAlunoHorarioTurma().alterarDataFimMatricula(contratoVO,horarioTurmaVO, dataFimMatricula, contratoVO.getVigenciaTurmaCreditoTreinoAte());
            }
        }

    }


    public void inicializarMovProdutoManutencaoModalidade(UsuarioVO res, List<MovParcelaVO>  listaMovParcelas,
    		ContratoVO contrato, Boolean liberado, 
    		List<ContratoModalidadeVO> modalidadesAdicionadas,
    		List<ContratoModalidadeVO> modalidadesAlteradas,
    		Double valorDesconto, Integer diasRestantes, Double valorTotal) throws Exception {
        try {
            int qtdDias = 1;
            if (diasRestantes != 0) {
                qtdDias = diasRestantes;
            }
        	Double valorPorDia = valorTotal / qtdDias;
        	
        	//obter produtos
        	List<MovProdutoVO> lista = getFacade().getMovProduto().consultarPorCodigoContratoTipoProduto(contrato.getCodigo().intValue(), "PM", false, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
        	List<MovProdutoVO> listaIterar = new ArrayList<MovProdutoVO>(lista);
        	//obter competencia do dia atual
        	String competencia = "";
            if(Uteis.getDiaMesData(Calendario.hoje()) < Uteis.getDiaMesData(contrato.getVigenciaDe())){
            	competencia = Uteis.getMesReferenciaData(Uteis.somarCampoData(Calendario.hoje(), Calendar.MONTH, -1));
            }else
            	competencia = Uteis.getDataMesAnoConcatenado();
            
            for(MovProdutoVO movProduto : listaIterar){
        		lista.remove(movProduto);
        		if(movProduto.getMesReferencia().equals(competencia)){
        			break;
        		}
        	}    

            //verificar quantos dias desse mês já foram utilizados
            int nrDiasFaltamMes = Uteis.obterNumeroDiasDoMes(Uteis.obterDataAnterior(Uteis.getMesData(Calendario.hoje()))) - Uteis.calcularDiasUtilizadosNoMes(contrato.getVigenciaDe(), Uteis.obterNumeroDiasDoMes(Uteis.obterDataAnterior(Uteis.getMesData(Calendario.hoje()))));        	
        	Double valorDoMes = Uteis.arredondarForcando2CasasDecimais(nrDiasFaltamMes*valorPorDia);
        	int numeroMeses = lista.size();
        	Double valorRestante = numeroMeses > 0 ? Uteis.arredondarForcando2CasasDecimais((valorTotal-valorDoMes)/numeroMeses) : 0.0;
        	Double cents = ((valorRestante*numeroMeses)+valorDoMes) - valorTotal;
        	valorDoMes = valorDoMes - cents;		
        	//iterar para achar o mes atual, retirar os produtos dos meses anteriores
        	incluirMovProdutoManutencao(res, listaMovParcelas, contrato, liberado, modalidadesAdicionadas, modalidadesAlteradas, valorDesconto, valorDoMes, competencia, valorTotal);
        	if(liberado || lista.isEmpty() || (listaMovParcelas.size() == 1 && UteisValidacao.emptyNumber(valorTotal))){
        		return;
        	} 
        	for(MovProdutoVO movProduto : lista){
        		incluirMovProdutoManutencao(res, listaMovParcelas, contrato, liberado, modalidadesAdicionadas, modalidadesAlteradas, valorDesconto, valorRestante, movProduto.getMesReferencia(), valorTotal);
        	}
        	
        } catch (Exception e) {
            throw e;
        }
    }

	/**
	 * Responsável por
	 * <AUTHOR> Alcides
	 * 04/04/2013
	 */
	private void incluirMovProdutoManutencao(UsuarioVO res, List<MovParcelaVO>  listaMovParcelas, ContratoVO contrato, Boolean liberado,
			List<ContratoModalidadeVO> modalidadesAdicionadas,
			List<ContratoModalidadeVO> modalidadesAlteradas,
			Double valorDesconto, Double valor, String competencia, Double valorTotal) throws Exception {
		MovProdutoVO movProdutoVO = new MovProdutoVO();
		movProdutoVO.setProduto(getFacade().getProduto().consultarPorTipoProduto("MM", false, Uteis.NIVELMONTARDADOS_DADOSBASICOS));
		movProdutoVO.setApresentarMovProduto(false);
		movProdutoVO.setDescricao("Manutenção Modalidade" + " - " + competencia);
		movProdutoVO.setMesReferencia(competencia);
		movProdutoVO.setQuantidade(1);
		movProdutoVO.setAnoReferencia(Integer.parseInt(competencia.substring(3, competencia.length())));
		movProdutoVO.setDataInicioVigencia(contrato.getVigenciaDe());
		movProdutoVO.setDataFinalVigencia(contrato.getVigenciaAteAjustada());
		movProdutoVO.setDataLancamento(negocio.comuns.utilitarias.Calendario.hoje());
		movProdutoVO.setResponsavelLancamento(res);
		movProdutoVO.setEmpresa(contrato.getEmpresa());
		movProdutoVO.setPessoa(contrato.getPessoa());
		movProdutoVO.setContrato(contrato);
		if (liberado || UteisValidacao.emptyNumber(valor)) {
		    movProdutoVO.setResponsavelLancamento(res);
		    movProdutoVO.setQuitado(true);
		    movProdutoVO.setSituacao("PG");
		    movProdutoVO.setValorDesconto(listaMovParcelas.get(0).getValorParcela());
		    movProdutoVO.setPrecoUnitario(listaMovParcelas.get(0).getValorParcela());
		    movProdutoVO.setTotalFinal(0.0);
            listaMovParcelas.get(0).setValorParcela(0.0);
            listaMovParcelas.get(0).setSituacao("PG");
		} else {
		    movProdutoVO.setQuitado(false);
		    movProdutoVO.setSituacao("EA");
		    valorDesconto = valorDesconto * valor/valorTotal;
		    movProdutoVO.setValorDesconto(valorDesconto);
		    movProdutoVO.setPrecoUnitario(valor + valorDesconto);
		    movProdutoVO.setTotalFinal(valor);
		}
		
		Double valorModalidades = 0.0;
		List<ContratoModalidadeVO> modalidades = new ArrayList<ContratoModalidadeVO>();
		for(ContratoModalidadeVO mod : modalidadesAdicionadas){
			valorModalidades += mod.getValorModalidade(); 
			mod.getModalidade().setModalidadeEscolhida(true);
			modalidades.add(mod);
		}
		for(ContratoModalidadeVO mod : modalidadesAlteradas){
			ContratoModalidadeVO modFake = (ContratoModalidadeVO) mod.getClone(true);
			modFake.setValorFinalModalidade(mod.getValorFinalModalidade());
			if(modFake.getValorFinalModalidade() > 0.0){
				valorModalidades += modFake.getValorFinalModalidade(); 
				modFake.getModalidade().setModalidadeEscolhida(true);
				modalidades.add(modFake);	
			}
		}
		
		movProdutoVO.setMovProdutoModalidades(
				getFacade().getZWFacade().gerarMovProdutoModalidade(valorModalidades, Calendario.hoje(),
                        contrato.getVigenciaAteAjustada(), modalidades, movProdutoVO.getTotalFinal()));
		getFacade().getMovProduto().incluirSemCommit(movProdutoVO);
		
		MovProdutoParcelaVO movProdutoParcela = new MovProdutoParcelaVO();
        movProdutoParcela.setMovProduto(movProdutoVO.getCodigo());
        movProdutoParcela.setMovProdutoVO(movProdutoVO);
        if (liberado || UteisValidacao.emptyNumber(valor)) {
            movProdutoParcela.setValorPago(movProdutoVO.getTotalFinal());
            listaMovParcelas.get(0).getMovProdutoParcelaVOs().add(movProdutoParcela);
        } else {
            movProdutoVO.setValorParcialmentePago(movProdutoVO.getTotalFinal());
            for (MovParcelaVO parcelaVO : listaMovParcelas){
                if(Uteis.arredondarForcando2CasasDecimais(parcelaVO.getValorBaseCalculo()) > 0.00){
                    processarProdutoParcela(movProdutoVO,parcelaVO);
                    if(Uteis.arredondarForcando2CasasDecimais(movProdutoVO.getValorParcialmentePago()) == 0.00 ){
                        break;
                    }
                }

            }
        }

	}

    private void processarProdutoParcela(MovProdutoVO produto, MovParcelaVO parcelaVO) throws Exception {
        MovProdutoParcelaVO mpp = new MovProdutoParcelaVO();
        mpp.setMovProduto(produto.getCodigo());
        mpp.setMovProdutoVO(produto);
        double valorPago = 0.0;
        if(Uteis.arredondarForcando2CasasDecimais(parcelaVO.getValorBaseCalculo()) >= Uteis.arredondarForcando2CasasDecimais(produto.getValorParcialmentePago())){
            valorPago =  produto.getValorParcialmentePago();
            parcelaVO.setValorBaseCalculo(Uteis.arredondarForcando2CasasDecimais(parcelaVO.getValorBaseCalculo() - produto.getValorParcialmentePago()));
            produto.setValorParcialmentePago(0.0);
            produto.setQuitado(true);
        } else {
            valorPago =  parcelaVO.getValorBaseCalculo();
            produto.setValorParcialmentePago(Uteis.arredondarForcando2CasasDecimais(produto.getValorParcialmentePago() - parcelaVO.getValorBaseCalculo()));
            parcelaVO.setValorBaseCalculo(0.0);
        }
        mpp.setValorPago(valorPago);
        parcelaVO.getMovProdutoParcelaVOs().add(mpp);
    }
    
    

    public List<MovPagamentoVO> inicializarMovProdutoTrocoManutencaoModalidadeAlteracaoHorario(UsuarioVO resp, Double valor, ContratoVO contrato,  String descricao, String tipoProduto) throws Exception {
    	List <MovPagamentoVO> pagamentosMovimento = new ArrayList<MovPagamentoVO>();
    	try {

        	MovProdutoVO movProdutoVO = new MovProdutoVO();
            movProdutoVO.setProduto(getFacade().getProduto().consultarPorTipoProduto(tipoProduto, false, Uteis.NIVELMONTARDADOS_DADOSBASICOS));
            movProdutoVO.setApresentarMovProduto(false);
            movProdutoVO.setDescricao(descricao);
            movProdutoVO.setMesReferencia(Uteis.getMesReferenciaData(negocio.comuns.utilitarias.Calendario.hoje()));
            movProdutoVO.setQuantidade(1);
            movProdutoVO.setAnoReferencia(Uteis.getAnoData(negocio.comuns.utilitarias.Calendario.hoje()));
            movProdutoVO.setDataInicioVigencia(negocio.comuns.utilitarias.Calendario.hoje());
            movProdutoVO.setDataFinalVigencia(negocio.comuns.utilitarias.Calendario.hoje());
            movProdutoVO.setDataLancamento(negocio.comuns.utilitarias.Calendario.hoje());
            movProdutoVO.setResponsavelLancamento(resp);
            movProdutoVO.setEmpresa(contrato.getEmpresa());
            movProdutoVO.setPessoa(contrato.getPessoa());
            movProdutoVO.setContrato(contrato);
            movProdutoVO.setQuitado(true);
            movProdutoVO.setSituacao("CA");
            movProdutoVO.setValorDesconto(0.0);
            movProdutoVO.setPrecoUnitario(0.0);
            movProdutoVO.setTotalFinal(0.0);


            Integer diasrestantes = contrato.obterNrDiasRestantesProFinalDoContrato(getFacade().getZWFacade().obterNrDiasContrato(contrato), getFacade().getZWFacade().obterNrDiasUtilizadoAcademiaAteDataEspecifica(contrato, negocio.comuns.utilitarias.Calendario.hoje()));
        
        	Integer meses = diasrestantes / 30;
            Integer diasrestantesMensal= diasrestantes % 30;

            if(meses == contrato.getContratoDuracao().getNumeroMeses()){
                diasrestantesMensal = 0;
            }
            Double valorDia =  valor /  diasrestantes;
            Double valorDiasRestantesMensal  = diasrestantesMensal * valorDia;
            Double valorMes = meses == 0 ? valor : Uteis.arredondarForcando2CasasDecimais((valor - valorDiasRestantesMensal) / meses); // valor para retirar por mês



        	List <MovPagamentoVO> listaPagamentos = new ArrayList<MovPagamentoVO>(); // lista de pagamentos que pagam o contrato

        	List <ReciboPagamentoVO> listaReciboPagamento = getFacade().getReciboPagamento().consularReciboPagamParcelaPorCodigoContrato(contrato.getCodigo() , Uteis.NIVELMONTARDADOS_DADOSBASICOS);
                List <PagamentoMovParcelaVO> pagMovparcelaGeral = new ArrayList<PagamentoMovParcelaVO>();
                List <MovPagamentoVO> listaTodosPagamentos = new ArrayList<MovPagamentoVO>();
        	Iterator k = listaReciboPagamento.iterator();
                while (k.hasNext()) {
                    ReciboPagamentoVO recibo = (ReciboPagamentoVO) k.next();
                    listaTodosPagamentos.addAll(getFacade().getMovPagamento().consultarPorCodigoRecibo(recibo.getCodigo(), false, Uteis.NIVELMONTARDADOS_TODOS));
                }
                for (MovPagamentoVO pagamento : listaTodosPagamentos) { // retira os pagamentos que já foram transferidos
                    if (pagamento.getValor() > 0.0) {
                        Ordenacao.ordenarListaReverse(pagamento.getPagamentoMovParcelaVOs(), "codigo");
                        pagMovparcelaGeral.add((PagamentoMovParcelaVO) pagamento.getPagamentoMovParcelaVOs().get(0));
                    }
                }
                Ordenacao.ordenarListaReverse(pagMovparcelaGeral, "codigo"); // garantir que os ultimos pagamentos usados sejam transferidos primeiro. Para evitar problemas nos produtos pagos
                for(PagamentoMovParcelaVO pagParcela: pagMovparcelaGeral){
                    for(MovPagamentoVO pagamento : listaTodosPagamentos){ 
                        if(pagParcela.getMovPagamento().equals(pagamento.getCodigo())){
                            listaPagamentos.add(pagamento);
                        }
                    }
                }

    		Double valorPagaProduto = valor; // valor total para ser retirado dos produtos
    		Integer tentativaAtualizarProdutos = 1;
            List<Integer> parcelasBloqueadasCobranca = null;
            List<PagamentoMovParcelaVO> pagMovparcelasNovos = new ArrayList<PagamentoMovParcelaVO>();
            while (valorPagaProduto > 0.0 && tentativaAtualizarProdutos <= 3 ) {
                List<MovProdutoVO> lista = getFacade().getMovProduto().consultarPorCodigoContratoOrdenado(contrato.getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS); // movprodutos do contrato
                List<MovParcelaVO> listaParcelas = getFacade().getMovParcela().consultarPorContrato(contrato.getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS);
                parcelasBloqueadasCobranca = validarParcelasEmCobranca(listaParcelas);
                Collections.reverse(lista);
                Iterator<MovProdutoVO> i = lista.iterator();
                Double valorRetirar = 0.0; //valor que deve ser retirado do produto
                List<MovProdutoVO> mpAlterar = new ArrayList<MovProdutoVO>();
                while (valorPagaProduto > 0.0 && i.hasNext()) {
                    MovProdutoVO mp = i.next();
                    if (!mp.getProduto().getTipoProduto().equals("PM") || mp.getSituacao().equals("CA")) { // verifica produtos que não são de plano
                        continue;
                    }
                    Ordenacao.ordenarListaReverse(mp.getMovProdutoParcelaVOs(), "movParcela");
                    if (!mp.getSituacao().equals("PG")) { //verifica produtos de plano não pagos
                        Double valorBaseMes = valorMes > Uteis.arredondarForcando2CasasDecimais(mp.getTotalFinal()) ? Uteis.arredondarForcando2CasasDecimais(mp.getTotalFinal()) : valorMes;
                        if (valorPagaProduto > valorBaseMes) {
                            valorPagaProduto = Uteis.arredondarForcando2CasasDecimais(valorPagaProduto - valorBaseMes);
                            valorRetirar = valorBaseMes;
                        } else {
                            valorRetirar = valorPagaProduto;
                            valorPagaProduto = 0.0;
                        }
                        List<MovProdutoParcelaVO> mppNovos = new ArrayList<MovProdutoParcelaVO>(); //novos movprodutoparcela para o produto
                        List<MovProdutoParcelaVO> mppPagos = new ArrayList<MovProdutoParcelaVO>(); //movprodutos parcialmente pagos
                        for (Object obj : mp.getMovProdutoParcelaVOs()) {
                            MovProdutoParcelaVO mpp = (MovProdutoParcelaVO) obj;
                            Double retirarParcela = 0.0;
                            if (mpp.getReciboPagamento() == null || UteisValidacao.emptyNumber(mpp.getReciboPagamento().getCodigo())) { // alterar os valores das parcelas  em aberto
                                if (parcelasBloqueadasCobranca.contains(mpp.getMovParcela())) {
                                    continue;
                                }
                                if (mpp.getValorPago() <= valorRetirar && mpp.getValorPago() > 0) {

                                    valorRetirar = Uteis.arredondarForcando2CasasDecimais(valorRetirar - mpp.getValorPago());
                                    retirarParcela = mpp.getValorPago();
                                    mp.setTotalFinal(Uteis.arredondarForcando2CasasDecimais(mp.getTotalFinal() - mpp.getValorPago()));
                                    mp.setPrecoUnitario(Uteis.arredondarForcando2CasasDecimais(mp.getTotalFinal() + mp.getValorDesconto()));
                                    mpp.setValorPago(0.0);


                                } else if (valorRetirar > 0) {
                                    mpp.setValorPago(Uteis.arredondarForcando2CasasDecimais(mpp.getValorPago() - valorRetirar));
                                    retirarParcela = valorRetirar;
                                    mp.setTotalFinal(Uteis.arredondarForcando2CasasDecimais(mp.getTotalFinal() - valorRetirar));
                                    mp.setPrecoUnitario(Uteis.arredondarForcando2CasasDecimais(mp.getTotalFinal() + mp.getValorDesconto()));
                                    valorRetirar = 0.0;
                                    mppNovos.add(mpp);
                                } else {
                                    mppNovos.add(mpp);
                                }
                                Iterator p = listaParcelas.iterator();
                                while (p.hasNext() && retirarParcela > 0) {
                                    MovParcelaVO parcela = (MovParcelaVO) p.next();
                                    if (mpp.getMovParcela().equals(parcela.getCodigo())) {
                                        parcela.setValorParcela(Uteis.arredondarForcando2CasasDecimais(parcela.getValorParcela() - retirarParcela));
                                        retirarParcela = 0.0;
                                        if (Uteis.arredondarForcando2CasasDecimais(parcela.getValorParcela()) == 0.00) {
                                            parcela.setSituacao("PG");
                                        }
                                        getFacade().getMovParcela().alterarSemCommit(parcela);
                                        continue;
                                    }
                                }
                            } else {
                                mppPagos.add(mpp);
                            }
                        }
                        if (!mppPagos.isEmpty()) {

                            for (Object obj : mppPagos) { // retirar valores dos produtos parcialmente pagos
                                MovProdutoParcelaVO mpp = (MovProdutoParcelaVO) obj;
                                Double valorProduto = 0.0;
                                if (Uteis.arredondarForcando2CasasDecimais(mpp.getValorPago()) <= valorRetirar && mpp.getValorPago() > 0) {
                                    valorProduto = mpp.getValorPago();
                                    valorRetirar = Uteis.arredondarForcando2CasasDecimais(valorRetirar - mpp.getValorPago());
                                    mp.setTotalFinal(Uteis.arredondarForcando2CasasDecimais(mp.getTotalFinal() - mpp.getValorPago()));
                                    mp.setPrecoUnitario(Uteis.arredondarForcando2CasasDecimais(mp.getTotalFinal() + mp.getValorDesconto()));
                                    movProdutoVO.getMovProdutoParcelaVOs().add(mpp);
                                    movProdutoVO.setTotalFinal(Uteis.arredondarForcando2CasasDecimais(movProdutoVO.getTotalFinal() + mpp.getValorPago()));

                                } else if (valorRetirar > 0) {
                                    MovProdutoParcelaVO novoMpp = (MovProdutoParcelaVO) mpp.getClone(true);
                                    novoMpp.setValorPago(valorRetirar);
                                    valorProduto = valorRetirar;
                                    mpp.setValorPago(Uteis.arredondarForcando2CasasDecimais(mpp.getValorPago() - valorRetirar));
                                    mp.setTotalFinal(Uteis.arredondarForcando2CasasDecimais(mp.getTotalFinal() - valorRetirar));
                                    mp.setPrecoUnitario(mp.getTotalFinal() + mp.getValorDesconto());
                                    valorRetirar = 0.0;
                                    mppNovos.add(mpp);
                                    movProdutoVO.getMovProdutoParcelaVOs().add(novoMpp);
                                    movProdutoVO.setTotalFinal(Uteis.arredondarForcando2CasasDecimais(movProdutoVO.getTotalFinal() + novoMpp.getValorPago()));

                                } else {
                                    mppNovos.add(mpp);
                                }

                                Iterator p = listaPagamentos.iterator();
                                while (p.hasNext() && valorProduto > 0.0) {
                                    MovPagamentoVO movPagamento = (MovPagamentoVO) p.next();
                                    if (movPagamento.getValor() > 0.0) {
                                        Iterator pm = movPagamento.getPagamentoMovParcelaVOs().iterator();
                                        while (pm.hasNext()) {
                                            PagamentoMovParcelaVO pagMov = (PagamentoMovParcelaVO) pm.next();
                                            if (mpp.getMovParcela().equals(pagMov.getMovParcela().getCodigo())) {
                                                if (Uteis.arredondarForcando2CasasDecimais(pagMov.getValorPago()) <= valorProduto && pagMov.getValorPago() > 0 && pagMov.getMovPagamento() != 0) {
                                                    movPagamento.setValor(movPagamento.getValor() - pagMov.getValorPago());
                                                    pagMov.setMovPagamento(0);
                                                    valorProduto -= pagMov.getValorPago();
                                                }
                                                if (Uteis.arredondarForcando2CasasDecimais(pagMov.getValorPago()) > valorProduto && valorProduto > 0 && pagMov.getMovPagamento() != 0) {
                                                    movPagamento.setValor(Uteis.arredondarForcando2CasasDecimais(movPagamento.getValor() - valorProduto));
                                                    PagamentoMovParcelaVO novoPag = (PagamentoMovParcelaVO) pagMov.getClone(true);
                                                    pagMov.setValorPago(Uteis.arredondarForcando2CasasDecimais(pagMov.getValorPago() - valorProduto));
                                                    novoPag.setValorPago(valorProduto);
                                                    pagMovparcelasNovos.add(novoPag);
                                                    valorProduto = 0.0;
                                                }
                                            }
                                        }
                                    }

                                }

                            }

                        }
                        if (Uteis.arredondarForcando2CasasDecimais(valorRetirar) > 0.00) {
                            valorPagaProduto = Uteis.arredondarForcando2CasasDecimais(valorPagaProduto + valorRetirar);
                        }
                        mp.setMovProdutoParcelaVOs(mppNovos);
                        if(Uteis.arredondarForcando2CasasDecimais(mp.getTotalFinal()) == 0.00){
                            mp.setSituacao("PG");
                        }
                        getFacade().getMovProduto().alterarSemCommit(mp);
                        getFacade().getMovProdutoParcela().alterarMovProdutoParcelas(mp.getCodigo(), mp.getMovProdutoParcelaVOs());
                        continue;
                    } else {
                        Double valorBaseMes = valorMes > Uteis.arredondarForcando2CasasDecimais(mp.getTotalFinal()) ? Uteis.arredondarForcando2CasasDecimais(mp.getTotalFinal()) : valorMes;
                        if (valorPagaProduto > valorBaseMes) {
                            valorPagaProduto = Uteis.arredondarForcando2CasasDecimais(valorPagaProduto - valorBaseMes);
                            valorRetirar = valorBaseMes;
                        } else {
                            valorRetirar = valorPagaProduto;
                            valorPagaProduto = 0.0;
                        }

                        List<MovProdutoParcelaVO> mppNovos = new ArrayList<MovProdutoParcelaVO>(); //Armazena  os movprodutos parcelas do movproduto antigo


                        for (Object obj : mp.getMovProdutoParcelaVOs()) {
                            MovProdutoParcelaVO mpp = (MovProdutoParcelaVO) obj;
                            Double valorProduto = 0.0;
                            if (Uteis.arredondarForcando2CasasDecimais(mpp.getValorPago()) <= valorRetirar && mpp.getValorPago() > 0) {
                                valorProduto = mpp.getValorPago();
                                valorRetirar = Uteis.arredondarForcando2CasasDecimais(valorRetirar - mpp.getValorPago());
                                mp.setTotalFinal(Uteis.arredondarForcando2CasasDecimais(mp.getTotalFinal() - mpp.getValorPago()));
                                mp.setPrecoUnitario(Uteis.arredondarForcando2CasasDecimais(mp.getTotalFinal() + mp.getValorDesconto()));
                                movProdutoVO.getMovProdutoParcelaVOs().add(mpp);
                                movProdutoVO.setTotalFinal(Uteis.arredondarForcando2CasasDecimais(movProdutoVO.getTotalFinal() + mpp.getValorPago()));

                            } else if (valorRetirar > 0) {
                                MovProdutoParcelaVO novoMpp = (MovProdutoParcelaVO) mpp.getClone(true);
                                novoMpp.setValorPago(valorRetirar);
                                valorProduto = valorRetirar;
                                mpp.setValorPago(Uteis.arredondarForcando2CasasDecimais(mpp.getValorPago() - valorRetirar));
                                mp.setTotalFinal(Uteis.arredondarForcando2CasasDecimais(mp.getTotalFinal() - valorRetirar));
                                mp.setPrecoUnitario(Uteis.arredondarForcando2CasasDecimais(mp.getTotalFinal() + mp.getValorDesconto()));
                                valorRetirar = 0.0;
                                mppNovos.add(mpp);
                                movProdutoVO.getMovProdutoParcelaVOs().add(novoMpp);
                                movProdutoVO.setTotalFinal(Uteis.arredondarForcando2CasasDecimais(movProdutoVO.getTotalFinal() + novoMpp.getValorPago()));

                            } else {
                                mppNovos.add(mpp);
                            }

                            Iterator p = listaPagamentos.iterator();
                            while (p.hasNext() && valorProduto > 0.0) {
                                MovPagamentoVO movPagamento = (MovPagamentoVO) p.next();
                                if (movPagamento.getValor() > 0.0) {
                                    Iterator pm = movPagamento.getPagamentoMovParcelaVOs().iterator();
                                    while (pm.hasNext()) {
                                        PagamentoMovParcelaVO pagMov = (PagamentoMovParcelaVO) pm.next();
                                        if (mpp.getMovParcela().equals(pagMov.getMovParcela().getCodigo())) {
                                            if (Uteis.arredondarForcando2CasasDecimais(pagMov.getValorPago()) <= valorProduto && pagMov.getValorPago() > 0 && pagMov.getMovPagamento() != 0) {
                                                movPagamento.setValor(Uteis.arredondarForcando2CasasDecimais(movPagamento.getValor() - pagMov.getValorPago()));
                                                pagMov.setMovPagamento(0);
                                                valorProduto = Uteis.arredondarForcando2CasasDecimais(valorProduto - pagMov.getValorPago());
                                            }
                                            if (Uteis.arredondarForcando2CasasDecimais(pagMov.getValorPago()) > valorProduto && valorProduto > 0 && pagMov.getMovPagamento() != 0) {
                                                movPagamento.setValor(Uteis.arredondarForcando2CasasDecimais(movPagamento.getValor() - valorProduto));
                                                PagamentoMovParcelaVO novoPag = (PagamentoMovParcelaVO) pagMov.getClone(true);
                                                pagMov.setValorPago(Uteis.arredondarForcando2CasasDecimais(pagMov.getValorPago() - valorProduto));
                                                novoPag.setValorPago(valorProduto);
                                                pagMovparcelasNovos.add(novoPag);
                                                valorProduto = 0.0;
                                            }
                                        }
                                    }
                                }

                            }

                        }


                        mp.setMovProdutoParcelaVOs(mppNovos);
                        mpAlterar.add(mp);
                        continue;
                    }
                }

                Ordenacao.ordenarLista(mpAlterar, "codigo");
                for (MovProdutoVO mp : mpAlterar) {
                    getFacade().getMovProduto().alterarSemCommit(mp);
                    getFacade().getMovProdutoParcela().alterarMovProdutoParcelasPorCodigoProduto(mp.getCodigo(), mp.getMovProdutoParcelaVOs());
                }
                tentativaAtualizarProdutos++;
            }

            if(Uteis.arredondarForcando2CasasDecimais(valorPagaProduto) > 0.00 && !UteisValidacao.emptyList(parcelasBloqueadasCobranca)){
                throw new Exception("Não foi possível conceder desconto na(s) parcela(s) do contrato, pois tem parcela(s) em remessa aguardando retorno ou com transação pendente. Aguarde a baixa dessa pendência para depois realizar a manutenção");
            }
    		if (Uteis.arredondarForcando2CasasDecimaisMantendoSinal(movProdutoVO.getTotalFinal()) > 0.00){
    			movProdutoVO.setPrecoUnitario(movProdutoVO.getTotalFinal());
    			getFacade().getMovProduto().incluirSemCommit(movProdutoVO);
    			getFacade().getMovProdutoParcela().alterarMovProdutoParcelasPorCodigoProduto(movProdutoVO.getCodigo(), movProdutoVO.getMovProdutoParcelaVOs());
    		}
    		
    		MovPagamentoVO novo = null;
                Collections.reverse(listaPagamentos);
    		Iterator pags = listaPagamentos.iterator();
    		      while (pags.hasNext()) {
                MovPagamentoVO movPagamento = (MovPagamentoVO) pags.next();
                if (movPagamento.getValor() < movPagamento.getValorTotal()) {
                    movPagamento.setValor(Uteis.arredondarForcando2CasasDecimais(movPagamento.getValor()));
                    if (movPagamento.getFormaPagamento().getTipoFormaPagamento().equals("CA")) {
                        getFacade().getMovPagamento().separarCartoesCancelados(movPagamento);
                        movPagamento.setCartaoCreditoVOs(Ordenacao.ordenarLista(movPagamento.getCartaoCreditoVOs(), "dataCompensacao"));
                        movPagamento = getFacade().getMovPagamento().atualizarListaCartaoCredito((MovPagamentoVO) movPagamento.getClone(true));
                    }
                    if (movPagamento.getFormaPagamento().getTipoFormaPagamento().equals("CH")) {
                        getFacade().getMovPagamento().separarChequesCancelados(movPagamento);
                        movPagamento.setChequeVOs(Ordenacao.ordenarLista(movPagamento.getChequeVOs(), "dataCompensacao"));
                        movPagamento = getFacade().getMovPagamento().atualizarListaCheques((MovPagamentoVO) movPagamento.getClone(true));
                    }

                    MovPagamentoVO movimento = getFacade().getMovPagamento().gerarMovpagamentoMovimentoCC((MovPagamentoVO) movPagamento.getClone(true));
                    if(movPagamento.getFormaPagamento().equals("CC") && !movPagamento.getCredito()){
                        movimento.setCredito(false);
                    }
                    if (Uteis.arredondarForcando2CasasDecimais(movPagamento.getValor()) > 0.00) {
                        novo = (MovPagamentoVO) movimento.getClone(true);
                        novo.setValor(0.0);
                        if (!movPagamento.getCredito()) {
                            novo.setCredito(false);
                        }
                        novo.setMovPagamentoOrigemCredito(0);
                        novo.setReciboPagamento(movPagamento.getReciboPagamento());
                        novo.setNovoObj(true);

                        if (movPagamento.getFormaPagamento().getTipoFormaPagamento().equals("CA")) {
                            Iterator m = novo.getCartaoCreditoVOs().iterator();
                            while (m.hasNext()) {
                                CartaoCreditoVO cartao = (CartaoCreditoVO) m.next();
                                cartao.setSituacao("CA");
                                cartao.setComposicao(null);
                            }
                            movPagamento = getFacade().getMovPagamento().retiraCartoesCancelados((MovPagamentoVO) movPagamento.getClone(true));
                            movPagamento.getCartaoCreditoVOs().addAll(movPagamento.getCartoesCanceladoVOs());
                        }
                        if (movPagamento.getFormaPagamento().getTipoFormaPagamento().equals("CH")) {
                            Iterator m = novo.getChequeVOs().iterator();
                            while (m.hasNext()) {
                                ChequeVO cheque = (ChequeVO) m.next();
                                cheque.setSituacao("CA");
                                cheque.setComposicao(null);
                            }
                            movPagamento = getFacade().getMovPagamento().retiraChequesCancelados((MovPagamentoVO) movPagamento.getClone(true));
                            movPagamento.getChequeVOs().addAll(movPagamento.getChequesCanceladoVOs());
                        }


                        novo.setPagamentoMovParcelaVOs(new ArrayList<PagamentoMovParcelaVO>());
                        List<PagamentoMovParcelaVO> parcelasPagamento = new ArrayList<PagamentoMovParcelaVO>();
                        Iterator pmp = movPagamento.getPagamentoMovParcelaVOs().iterator();
                        while (pmp.hasNext()) {
                            PagamentoMovParcelaVO pagParcela = (PagamentoMovParcelaVO) pmp.next();
                            if (pagParcela.getMovPagamento() == 0) {
                                novo.getPagamentoMovParcelaVOs().add(pagParcela);
                            } else {
                                parcelasPagamento.add(pagParcela);
                            }
                        }
                        Iterator mppNovos = pagMovparcelasNovos.iterator();

                        while (mppNovos.hasNext()) {
                            PagamentoMovParcelaVO pagParcela = (PagamentoMovParcelaVO) mppNovos.next();
                            if (novo.getCodigo().equals(pagParcela.getMovPagamento())) {
                                novo.getPagamentoMovParcelaVOs().add(pagParcela);
                            }
                        }

                        movPagamento.setPagamentoMovParcelaVOs(new ArrayList<PagamentoMovParcelaVO>());
                        movPagamento.getPagamentoMovParcelaVOs().addAll(parcelasPagamento);
                        Ordenacao.ordenarLista(novo.getPagamentoMovParcelaVOs(), "dataVencimentoParcela");
                        getFacade().getMovPagamento().incluirSemCommit(novo);
                        Double valorTotal = new Double(movPagamento.getValor());
                        movPagamento.setValorTotal(valorTotal);
                    } else {
                        Iterator mppNovos = pagMovparcelasNovos.iterator();

                        while (mppNovos.hasNext()) {
                            PagamentoMovParcelaVO pagParcela = (PagamentoMovParcelaVO) mppNovos.next();
                            if (movPagamento.getCodigo().equals(pagParcela.getMovPagamento())) {
                                movPagamento.getPagamentoMovParcelaVOs().add(pagParcela);
                            }
                        }

                    }
                    movimento.setResponsavelPagamento(resp);
                    if (movPagamento.getMovPagamentoOrigemCredito() != null && movPagamento.getMovPagamentoOrigemCredito() > 0) {
                        movimento.setMovPagamentoOrigemCredito(movPagamento.getMovPagamentoOrigemCredito());
                        if (novo == null) {
                            movPagamento.setMovPagamentoOrigemCredito(0);
                        }
                    } else {
                        if (novo != null && novo.getCodigo() != 0) {
                            movimento.setMovPagamentoOrigemCredito(novo.getCodigo());
                        } else {
                            movimento.setMovPagamentoOrigemCredito(movPagamento.getCodigo());
                        }
                    }
                    getFacade().getMovPagamento().incluirSemCommit(movimento);
                    movimento.setNovoObj(false);
                    if (movimento.getFormaPagamento().getTipoFormaPagamento().equals("CC") && !movPagamento.getCredito()) {//pode ser cc, mas ser um crédito de importação
                        getFacade().getMovimentoContaCorrenteCliente().ajustarDebitoAlteracaoManutencaoHorario(movPagamento, movimento, tipoProduto, contrato, resp);
                        getFacade().getMovPagamento().excluir(movimento);
                    } else {
                        pagamentosMovimento.add(movimento);
                    }
                    Ordenacao.ordenarLista(movPagamento.getPagamentoMovParcelaVOs(), "codigo");
                    getFacade().getMovPagamento().alterarSemCommit(movPagamento);
                    //necessario para que o servico de produtos pagos rode corretamente
                    if (novo != null && novo.getCodigo() != 0) {
                        getFacade().getPagamentoMovParcela().alterarPagamentoMovParcelas(novo.getCodigo(), novo.getPagamentoMovParcelaVOs(), novo.getReciboPagamento().getCodigo());
                    }
                    novo = null;
                }
            }

            Iterator l = listaReciboPagamento.iterator();

            while (l.hasNext()) {
                ReciboPagamentoVO recibo = (ReciboPagamentoVO) l.next();
                listaPagamentos = getFacade().getMovPagamento().consultarPorCodigoRecibo(recibo.getCodigo(), false, Uteis.NIVELMONTARDADOS_TODOS);
                getFacade().getMovPagamento().setarProdutosPagos(recibo.getCodigo());
            }



        } catch (Exception e) {
            throw e;
        }
        return pagamentosMovimento;
    }

    private List<Integer> validarParcelasEmCobranca(List<MovParcelaVO> listaParcelas) throws Exception {
        MovParcela movParcelaDao = new MovParcela(con);
        List<Integer> parcelasEmCobranca = new ArrayList<Integer>();
        for (MovParcelaVO parcela: listaParcelas) {
            if(parcela.getSituacao().equals("EA") && movParcelaDao.parcelaEstaBloqueadaPorCobranca(parcela)){
                parcelasEmCobranca.add(parcela.getCodigo());
            }
        }
        return  parcelasEmCobranca;
    }


    public void alterarMatriculas(ContratoVO contrato, Date vigenciaAtual, Date novaDataFinal, boolean trancamento, Date dataBaseRetorno, final String tipoOperacao) throws Exception {
        
        if(contrato.isVendaCreditoTreino() && contrato.getContratoDuracao().getContratoDuracaoCreditoTreinoVO().getTipoHorarioCreditoTreinoEnum() == TipoHorarioCreditoTreinoEnum.HORARIO_TURMA){
             if(tipoOperacao.equals("BR")){
                alterarMatriculasContratoCredito(contrato, vigenciaAtual, novaDataFinal);
            } else {
                return;
            }
        } else {
            MatriculaAlunoHorarioTurma matriculaAlunoHorarioTurmaDao = new MatriculaAlunoHorarioTurma(con);
            List<MatriculaAlunoHorarioTurmaVO> lista = matriculaAlunoHorarioTurmaDao.consultarMatriculasPorMaxDataFim(contrato.getCodigo());
            if (Calendario.menorOuIgual(Calendario.hoje(), vigenciaAtual) || trancamento) { //para contrato vigente
                for (MatriculaAlunoHorarioTurmaVO matricula : lista) {
                    if(Calendario.menor(matricula.getDataFim(), Calendario.hoje()) && !trancamento){
                        continue;// turmas retiradas por manutenção, onde o aluno tinha uma modalidade de turma que foi retirada e no lugar foi adicionada uma modalidade sem turma.
                    }
                    if (contrato.getContratoResponsavelRenovacaoMatricula() == null || contrato.getContratoResponsavelRenovacaoMatricula() == 0) {
                        matricula.setDataFim(Uteis.somarDias(novaDataFinal, contrato.getEmpresa().getToleranciaOcupacaoTurma()));
                        if (Calendario.menorComHora(matricula.getDataFim(),matricula.getDataInicio())){
                            matricula.setDataFim(matricula.getDataInicio());
                        }
                    } else {
                        matricula.setDataFim(novaDataFinal);
                    }
                    if (((contrato.isVendaCreditoTreino()) && (contrato.getSituacaoContrato().equals("RN"))) &&
                         (Calendario.maior(matricula.getDataInicio(), matricula.getDataFim()))){
                            // Neste caso o contrato foi renovado, e no mesmo dia foi estornado e renovado novamente.
                            matricula.setDataInicio(matricula.getDataFim());
                         matriculaAlunoHorarioTurmaDao.alterarVigenciaSemCommit(matricula);
                    }else{
                        matriculaAlunoHorarioTurmaDao.alterarSemCommit(matricula);
                    }

                }
            } else { // para contratos inativos, no caso de atestado e bônus
                if (tipoOperacao.equals("Estorno_Bonus_Atestado_ContratoInativo")) {
                    List<MatriculaAlunoHorarioTurmaVO> listaExistentes = matriculaAlunoHorarioTurmaDao.consultarMatriculaAtiva(contrato.getCodigo(), novaDataFinal);
                    for (MatriculaAlunoHorarioTurmaVO matricula : listaExistentes) {
                        matriculaAlunoHorarioTurmaDao.excluir(matricula);
                    }
                } else {
                    List<MatriculaAlunoHorarioTurmaVO> listaExistentes = matriculaAlunoHorarioTurmaDao.consultarMatriculaAtiva(contrato.getCodigo(), Calendario.hoje());
                    if (listaExistentes.isEmpty()) { // esse caso avalia se o aluno já teve uma operação lançada após o vencimento para não duplicar a matricula
                        for (MatriculaAlunoHorarioTurmaVO matricula : lista) {
                            if (Calendario.menor(matricula.getDataFim(), vigenciaAtual)) {
                                continue;
                            }
                            MatriculaAlunoHorarioTurmaVO novo = (MatriculaAlunoHorarioTurmaVO) matricula.getClone(true);
                            novo.setDataInicio(Calendario.hoje());
                            novo.setDataFim(novaDataFinal);
                            matriculaAlunoHorarioTurmaDao.incluirSemComit(novo);
                        }
                    } else {
                        for (MatriculaAlunoHorarioTurmaVO matricula : listaExistentes) {
                            if (Calendario.maior(novaDataFinal, matricula.getDataFim())) {
                                matricula.setDataFim(novaDataFinal);
                                matriculaAlunoHorarioTurmaDao.alterarSemCommit(matricula);
                            }
                        }
                    }
                }
            }
            matriculaAlunoHorarioTurmaDao = null;
        }
    }
     public static void alterarMatriculasContratoCredito(ContratoVO contrato, Date vigenciaAtual, Date novaDataFinal) throws Exception {
        List<MatriculaAlunoHorarioTurmaVO> lista = getFacade().getMatriculaAlunoHorarioTurma().consultarMatriculasPorMaxDataFim(contrato.getCodigo());
        for (MatriculaAlunoHorarioTurmaVO matricula : lista) {
              if (Calendario.maiorOuIgual(matricula.getDataFim(), Calendario.hoje()) && Calendario.maior(matricula.getDataFim(), novaDataFinal)) {
                  matricula.setDataFim(novaDataFinal);
                  if (((contrato.getSituacaoContrato().equals("RN"))) &&
                       (Calendario.maior(matricula.getDataInicio(), matricula.getDataFim()))){
                          // Neste caso o contrato foi renovado, e no mesmo dia foi estornado e renovado novamente.
                          matricula.setDataInicio(matricula.getDataFim());
                          getFacade().getMatriculaAlunoHorarioTurma().alterarVigenciaSemCommit(matricula);
                  }else{
                      getFacade().getMatriculaAlunoHorarioTurma().alterarSemCommit(matricula);
                  }
              }

          }
    }
    
    
    public static void ajustarMatriculasRetornoTrancamento(ContratoVO contrato, List<MatriculaAlunoHorarioTurmaVO> listaVigentes, Date novaDataFinal , boolean trancamento) throws Exception {
        for (MatriculaAlunoHorarioTurmaVO matricula : listaVigentes) {
            if (contrato.getContratoResponsavelRenovacaoMatricula() == null || contrato.getContratoResponsavelRenovacaoMatricula() == 0) {
                matricula.setDataFim(Uteis.somarDias(novaDataFinal, contrato.getEmpresa().getToleranciaOcupacaoTurma()));
            } else {
                matricula.setDataFim(novaDataFinal);
            }
            if(UteisValidacao.emptyNumber(matricula.getCodigo())){
                 getFacade().getMatriculaAlunoHorarioTurma().incluirSemComit(matricula);
            } else {
                getFacade().getMatriculaAlunoHorarioTurma().alterarSemCommit(matricula);
            }
        }
    }

    public static void alterarMatriculasSemOcupacao(ContratoVO contrato, Date vigenciaAtual, Date novaDataFinal) throws Exception {
        List<MatriculaAlunoHorarioTurmaVO> lista = getFacade().getMatriculaAlunoHorarioTurma().consultarMatriculaAtiva(contrato.getCodigo(),
                (Calendario.maiorOuIgual(vigenciaAtual, novaDataFinal)) ? novaDataFinal : vigenciaAtual); //Para casos onde o cancelamente acrescente dias na data final do contrato.
        for (MatriculaAlunoHorarioTurmaVO matricula : lista) {
            if (matricula.getDataInicio().compareTo(novaDataFinal) > 0) {
                 getFacade().getMatriculaAlunoHorarioTurma().excluirSemCommit(matricula);
            } else {
                matricula.setDataFim(novaDataFinal);
                 getFacade().getMatriculaAlunoHorarioTurma().alterarSemCommit(matricula);
            }
        }
    }
    
    public static void alterarMatriculasSemOcupacaoTrancamento(ContratoVO contrato, Date dataPesquisa, Date novaDataFinal) throws Exception {
        List<MatriculaAlunoHorarioTurmaVO> lista = getFacade().getMatriculaAlunoHorarioTurma().consultarMatriculasPorMaxDataFim(contrato.getCodigo());
        for (MatriculaAlunoHorarioTurmaVO matricula : lista) {
            if (matricula.getDataInicio().compareTo(novaDataFinal) > 0) {
                 getFacade().getMatriculaAlunoHorarioTurma().excluirSemCommit(matricula);
            } else {
                matricula.setDataFim(novaDataFinal);
                 getFacade().getMatriculaAlunoHorarioTurma().alterarSemCommit(matricula);
            }
        }
    }

    /**
     * Operação responsável por alterar no BD os dados de um objeto da classe <code>ContratoOperacaoVO</code>.
     * Sempre utiliza a chave primária da classe como atributo para localização do registro a ser alterado.
     * Primeiramente valida os dados (<code>validarDados</code>) do objeto. Verifica a conexão com o banco de dados e a permissão do usuário
     * para realizar esta operacão na entidade.
     * Isto, através da operação <code>alterar</code> da superclasse.
     * @param obj    Objeto da classe <code>ContratoOperacaoVO</code> que será alterada no banco de dados.
     * @exception Exception Caso haja problemas de conexão, restrição de acesso ou validação de dados.
     */
    public void alterar(ContratoOperacaoVO obj) throws Exception {
        try {
            con.setAutoCommit(false);
            alterarSemCommit(obj);
            con.commit();
        } catch (Exception e) {
            con.rollback();
            con.setAutoCommit(true);
            throw e;
        } finally {
            con.setAutoCommit(true);
        }
    }

    public void alterar(ContratoOperacaoVO obj, Boolean controleAcesso) throws Exception {
        try {
            con.setAutoCommit(false);
            alterarSemCommit(obj, controleAcesso);
            con.commit();
        } catch (Exception e) {
            con.rollback();
            con.setAutoCommit(true);
            throw e;
        } finally {
            con.setAutoCommit(true);
        }
    }

    public void alterarSemCommit(ContratoOperacaoVO obj) throws Exception {
        try {
            alterarSemCommit(obj, true);
        } catch (Exception e) {
            throw e;
        }
    }

    /**
     * Operação responsável por alterar no BD os dados de um objeto da classe <code>ContratoOperacaoVO</code>.
     * Sempre utiliza a chave primária da classe como atributo para localização do registro a ser alterado.
     * Primeiramente valida os dados (<code>validarDados</code>) do objeto. Verifica a conexão com o banco de dados e a permissão do usuário
     * para realizar esta operacão na entidade.
     * Isto, através da operação <code>alterar</code> da superclasse.
     * @param obj    Objeto da classe <code>ContratoOperacaoVO</code> que será alterada no banco de dados.
     * @exception Exception Caso haja problemas de conexão, restrição de acesso ou validação de dados.
     */
    public void alterarSemCommit(ContratoOperacaoVO obj, Boolean controleAcesso) throws Exception {
        try {
            ContratoOperacaoVO.validarDados(obj, false);
            if (controleAcesso) {
                alterar(getIdEntidade());
            }
            obj.realizarUpperCaseDados();
            String sql = "UPDATE ContratoOperacao set contrato=?, tipoOperacao=?, operacaoPaga=?, dataOperacao=?, dataInicioEfetivacaoOperacao=?, dataFimEfetivacaoOperacao=?, responsavel=?, observacao=?, descricaoCalculo=?, tipoJustificativa=?, responsavelLiberacao=?, clienteTransfereDias = ?,clienteRecebeDias=?,nrdiasoperacao=?, informacoes = ?, chavearquivo = ?, origemSistema = ?, informacoesDesfazer = ? WHERE ((codigo = ?))";
            try (PreparedStatement sqlAlterar = con.prepareStatement(sql)) {
                sqlAlterar.setInt(1, obj.getContrato());
                sqlAlterar.setString(2, obj.getTipoOperacao());
                sqlAlterar.setBoolean(3, obj.isOperacaoPaga());
                sqlAlterar.setDate(4, Uteis.getDataJDBC(obj.getDataOperacao()));
                sqlAlterar.setDate(5, Uteis.getDataJDBC(obj.getDataInicioEfetivacaoOperacao()));
                sqlAlterar.setDate(6, Uteis.getDataJDBC(obj.getDataFimEfetivacaoOperacao()));
                if (obj.getResponsavel().getCodigo() != 0) {
                    sqlAlterar.setInt(7, obj.getResponsavel().getCodigo());
                } else {
                    sqlAlterar.setNull(7, 0);
                }
                sqlAlterar.setString(8, obj.getObservacao());
                sqlAlterar.setString(9, obj.getDescricaoCalculo());
                if (obj.getTipoJustificativa().getCodigo() != 0) {
                    sqlAlterar.setInt(10, obj.getTipoJustificativa().getCodigo());
                } else {
                    sqlAlterar.setNull(10, 0);
                }
                if (obj.getResponsavelLiberacao().getCodigo() != 0) {
                    sqlAlterar.setInt(11, obj.getResponsavelLiberacao().getCodigo());
                } else {
                    sqlAlterar.setNull(11, 0);
                }
                if (obj.getClienteTransfereDias().getCodigo() != 0) {
                    sqlAlterar.setInt(12, obj.getClienteTransfereDias().getCodigo());
                } else {
                    sqlAlterar.setNull(12, 0);
                }
                if (obj.getClienteRecebeDias().getCodigo() != 0) {
                    sqlAlterar.setInt(13, obj.getClienteRecebeDias().getCodigo());
                } else {
                    sqlAlterar.setNull(13, 0);
                }
                if (obj.getNrDiasOperacao() != 0) {
                    sqlAlterar.setInt(14, obj.getNrDiasOperacao());
                } else {
                    sqlAlterar.setNull(14, 0);
                }
                sqlAlterar.setString(15, obj.getInformacoes());
                sqlAlterar.setString(16, obj.getChaveArquivo());
                sqlAlterar.setInt(17, obj.getOrigemSistema().getCodigo());
                sqlAlterar.setString(18, obj.getInformacoesDesfazer());
                sqlAlterar.setInt(19, obj.getCodigo());
                sqlAlterar.execute();
            }

        } catch (Exception e) {
            throw e;
        }
    }

    /**
     * Operação responsável por excluir no BD um objeto da classe <code>ContratoOperacaoVO</code>.
     * Sempre localiza o registro a ser excluído através da chave primária da entidade.
     * Primeiramente verifica a conexão com o banco de dados e a permissão do usuário
     * para realizar esta operacão na entidade.
     * Isto, através da operação <code>excluir</code> da superclasse.
     * @param obj    Objeto da classe <code>ContratoOperacaoVO</code> que será removido no banco de dados.
     * @exception Exception Caso haja problemas de conexão ou restrição de acesso.
     */
    public void excluir(ContratoOperacaoVO obj) throws Exception {
        try {
            con.setAutoCommit(false);
            excluirSemCommit(obj);
            con.commit();
        } catch (Exception e) {
            con.rollback();
            con.setAutoCommit(true);
            throw e;
        } finally {
            con.setAutoCommit(true);
        }
    }

    /**
     * Operação responsável por excluir no BD um objeto da classe <code>ContratoOperacaoVO</code>.
     * Sempre localiza o registro a ser excluído através da chave primária da entidade.
     * Primeiramente verifica a conexão com o banco de dados e a permissão do usuário
     * para realizar esta operacão na entidade.
     * Isto, através da operação <code>excluir</code> da superclasse.
     * @param obj    Objeto da classe <code>ContratoOperacaoVO</code> que será removido no banco de dados.
     * @exception Exception Caso haja problemas de conexão ou restrição de acesso.
     */
    public void excluirSemCommit(ContratoOperacaoVO obj) throws Exception {
        try {
            excluir(getIdEntidade());
            String sql = "DELETE FROM ContratoOperacao WHERE ((codigo = ?))";
            try (PreparedStatement sqlExcluir = con.prepareStatement(sql)) {
                sqlExcluir.setInt(1, obj.getCodigo());
                sqlExcluir.execute();
            }

        } catch (Exception e) {
            throw e;
        }
    }

    public boolean existeCarencia(Date data, Integer contratoCodigo) throws Exception {
        String sql = "SELECT COUNT(*) as carencias \n" +
                "FROM contratooperacao\n" +
                "WHERE tipooperacao = ?\n" +
                "  AND ? BETWEEN datainicioefetivacaooperacao AND datafimefetivacaooperacao\n" +
                "  AND contratooperacao.contrato = ?";

        try (PreparedStatement ps = con.prepareStatement(sql)) {
            int i = 0;
            ps.setString(++i, "CR");
            ps.setDate(++i, Uteis.getDataJDBC(data));
            ps.setInt(++i, contratoCodigo);
            try (ResultSet rs = ps.executeQuery()) {
                rs.next();
                return (rs.getInt("carencias") > 0);
            }
        }
    }

    /**
     * Responsável por realizar uma consulta de <code>ContratoOperacao</code> através do valor do atributo
     * <code>Integer responsavel</code>. Retorna os objetos com valores iguais ou superiores ao parâmetro fornecido.
     * Faz uso da operação <code>montarDadosConsulta</code> que realiza o trabalho de prerarar o List resultante.
     * @param   controlarAcesso Indica se a aplicação deverá verificar se o usuário possui permissão para esta consulta ou não.
     * @return  List Contendo vários objetos da classe <code>ContratoOperacaoVO</code> resultantes da consulta.
     * @exception Exception Caso haja problemas de conexão ou restrição de acesso.
     */
    public List consultarPorResponsavel(Integer valorConsulta, boolean controlarAcesso, int nivelMontarDados) throws Exception {
        consultar(getIdEntidade(), controlarAcesso);
        String sqlStr = "SELECT * FROM ContratoOperacao WHERE responsavel >= " + valorConsulta + " ORDER BY responsavel";
        try (Statement stm = con.createStatement()) {
            try (ResultSet tabelaResultado = stm.executeQuery(sqlStr)) {
                return (montarDadosConsulta(tabelaResultado, nivelMontarDados, this.con));
            }
        }
    }

    /**
     * Responsável por realizar uma consulta de <code>ContratoOperacao</code> através do valor do atributo
     * <code>Date dataOperacao</code>. Retorna os objetos com valores pertecentes ao período informado por parâmetro.
     * Faz uso da operação <code>montarDadosConsulta</code> que realiza o trabalho de prerarar o List resultante.
     * @param   controlarAcesso Indica se a aplicação deverá verificar se o usuário possui permissão para esta consulta ou não.
     * @return  List Contendo vários objetos da classe <code>ContratoOperacaoVO</code> resultantes da consulta.
     * @exception Exception Caso haja problemas de conexão ou restrição de acesso.
     */
    public List consultarPorDataOperacao(Date prmIni, Date prmFim, boolean controlarAcesso, int nivelMontarDados) throws Exception {
        consultar(getIdEntidade(), controlarAcesso);
        String sqlStr = "SELECT * FROM ContratoOperacao WHERE ((dataOperacao >= '" + Uteis.getDataJDBC(prmIni) + "') and (dataOperacao <= '" + Uteis.getDataJDBC(prmFim) + "')) ORDER BY dataOperacao";
        try (Statement stm = con.createStatement()) {
            try (ResultSet tabelaResultado = stm.executeQuery(sqlStr)) {
                return (montarDadosConsulta(tabelaResultado, nivelMontarDados, this.con));
            }
        }
    }

    /**
     * Responsável por realizar uma consulta de <code>ContratoOperacao</code> através do valor do atributo
     * <code>String tipoOperacao</code>. Retorna os objetos, com início do valor do atributo idêntico ao parâmetro fornecido.
     * Faz uso da operação <code>montarDadosConsulta</code> que realiza o trabalho de prerarar o List resultante.
     * @param   controlarAcesso Indica se a aplicação deverá verificar se o usuário possui permissão para esta consulta ou não.
     * @return  List Contendo vários objetos da classe <code>ContratoOperacaoVO</code> resultantes da consulta.
     * @exception Exception Caso haja problemas de conexão ou restrição de acesso.
     */
    public List consultarPorTipoOperacao(String valorConsulta, boolean controlarAcesso, int nivelMontarDados) throws Exception {
        consultar(getIdEntidade(), controlarAcesso);
        String sqlStr = "SELECT * FROM ContratoOperacao WHERE upper( tipoOperacao ) like('" + valorConsulta.toUpperCase() + "%') ORDER BY tipoOperacao";
        try (Statement stm = con.createStatement()) {
            try (ResultSet tabelaResultado = stm.executeQuery(sqlStr)) {
                return (montarDadosConsulta(tabelaResultado, nivelMontarDados, this.con));
            }
        }
    }

    /**
     * Responsável por realizar uma consulta de <code>ContratoOperacao</code> através do valor do atributo
     * <code>String tipoOperacao</code>. Retorna os objetos, com início do valor do atributo idêntico ao parâmetro fornecido.
     * Faz uso da operação <code>montarDadosConsulta</code> que realiza o trabalho de prerarar o List resultante.
     * @param   controlarAcesso Indica se a aplicação deverá verificar se o usuário possui permissão para esta consulta ou não.
     * @return  List Contendo vários objetos da classe <code>ContratoOperacaoVO</code> resultantes da consulta.
     * @exception Exception Caso haja problemas de conexão ou restrição de acesso.
     */
    public List consultarPorTipoOperacaoCodigoContrato(String valorConsulta, Integer contrato, boolean controlarAcesso, int nivelMontarDados) throws Exception {
        consultar(getIdEntidade(), controlarAcesso);
        String sqlStr = "SELECT * FROM ContratoOperacao WHERE upper( tipoOperacao ) like('" + valorConsulta.toUpperCase() + "%') and contrato = " + contrato + " ORDER BY tipoOperacao";
        try (Statement stm = con.createStatement()) {
            try (ResultSet tabelaResultado = stm.executeQuery(sqlStr)) {
                return (montarDadosConsulta(tabelaResultado, nivelMontarDados, this.con));
            }
        }
    }
    /**
     * Responsável por realizar uma consulta de <code>ContratoOperacao</code> através do valor do atributo
     * <code>Integer contrato</code>. Retorna os objetos com valores iguais ou superiores ao parâmetro fornecido.
     * Faz uso da operação <code>montarDadosConsulta</code> que realiza o trabalho de prerarar o List resultante.
     * @param   controlarAcesso Indica se a aplicação deverá verificar se o usuário possui permissão para esta consulta ou não.
     * @return  List Contendo vários objetos da classe <code>ContratoOperacaoVO</code> resultantes da consulta.
     * @exception Exception Caso haja problemas de conexão ou restrição de acesso.
     */
    public List consultarPorContrato(Integer valorConsulta, boolean controlarAcesso, int nivelMontarDados) throws Exception {
        consultar(getIdEntidade(), controlarAcesso);
        String sqlStr = "SELECT * FROM ContratoOperacao WHERE contrato = " + valorConsulta + " ORDER BY dataInicioEfetivacaoOperacao, codigo";
        try (Statement stm = con.createStatement()) {
            try (ResultSet tabelaResultado = stm.executeQuery(sqlStr)) {
                return (montarDadosConsulta(tabelaResultado, nivelMontarDados, this.con));
            }
        }
    }

    /**
     * Responsável por realizar uma consulta de <code>ContratoOperacao</code> através do valor do atributo
     * <code>Integer codigo</code>. Retorna os objetos com valores iguais ou superiores ao parâmetro fornecido.
     * Faz uso da operação <code>montarDadosConsulta</code> que realiza o trabalho de prerarar o List resultante.
     * @param   controlarAcesso Indica se a aplicação deverá verificar se o usuário possui permissão para esta consulta ou não.
     * @return  List Contendo vários objetos da classe <code>ContratoOperacaoVO</code> resultantes da consulta.
     * @exception Exception Caso haja problemas de conexão ou restrição de acesso.
     */
    public List consultarPorCodigo(Integer valorConsulta, boolean controlarAcesso, int nivelMontarDados) throws Exception {
        consultar(getIdEntidade(), controlarAcesso);
        String sqlStr = "SELECT * FROM ContratoOperacao WHERE codigo >= " + valorConsulta + " ORDER BY codigo";
        try (Statement stm = con.createStatement()) {
            try (ResultSet tabelaResultado = stm.executeQuery(sqlStr)) {
                return (montarDadosConsulta(tabelaResultado, nivelMontarDados, this.con));
            }
        }
    }

    /**
     * Operação responsável por obter o último valor gerado para uma chave primária.
     * É utilizada para obter o valor gerado pela SGBD para uma chave primária,
     * a apresentação do mesmo e a implementação de possíveis relacionamentos.
     */
    public ContratoOperacaoVO obterUltimaOperacaoContratoPorCodigoContratoTipoOperacao(Integer contrato, String tipoOperacao, int nivelMontarDados) throws Exception {
        String sqlStr = "SELECT * FROM ContratoOperacao WHERE  contrato = " + contrato + " and tipooperacao = '" + tipoOperacao.toUpperCase() + "' and datafimefetivacaooperacao IN (SELECT MAX(datafimefetivacaooperacao) FROM ContratoOperacao WHERE contrato = " + contrato + " and tipooperacao = '" + tipoOperacao.toUpperCase() + "') ";
        try (Statement stm = con.createStatement()) {
            try (ResultSet tabelaResultado = stm.executeQuery(sqlStr)) {
                if (!tabelaResultado.next()) {
                    return null;
                }
                return (montarDados(tabelaResultado, nivelMontarDados, this.con));
            }
        }
    }

    @Override
    public Boolean consultarPorDataEspecificaECodigoContratoETipoOperacao(Date data, Integer contrato, String tipoOperacao, boolean controlarAcesso, int nivelMontarDados) throws Exception {
        consultar(getIdEntidade(), controlarAcesso);
        String sqlStr = "SELECT * FROM ContratoOperacao WHERE dataInicioEfetivacaoOperacao <= '" + Uteis.getDataJDBC(data) + "' and datafimefetivacaooperacao >= '" + Uteis.getDataJDBC(data) + "' and contrato =  " + contrato.intValue() + " and tipoOperacao ='" + tipoOperacao.toUpperCase() + "' ORDER BY codigo";
        try (Statement stm = con.createStatement()) {
            try (ResultSet tabelaResultado = stm.executeQuery(sqlStr)) {
                return tabelaResultado.next();
            }
        }
    }

    public Boolean verificarUltimaOperacaoCodigoContratoETipoOperacao(Date data,Integer contrato, String tipoOperacao, boolean controlarAcesso) throws Exception {
        consultar(getIdEntidade(), controlarAcesso);
        String sqlStr = "SELECT * FROM ContratoOperacao WHERE codigo in (select max(codigo) from contratooperacao where contrato = " + contrato.intValue() + " and tipoOperacao in('TR','CA','TV','CR','AT','RT') and datainicioefetivacaooperacao <= '" + Uteis.getDataJDBC(data) + "') ";
        try (Statement stm = con.createStatement()) {
            try (ResultSet tabelaResultado = stm.executeQuery(sqlStr)) {
                return !(!tabelaResultado.next() || !tabelaResultado.getString("tipooperacao").equals(tipoOperacao));
            }
        }
    }

    public boolean consultarCodigoContratoETipoOperacao(Integer contrato, String tipoOperacao, boolean controlarAcesso, int nivelMontarDados) throws Exception {
        consultar(getIdEntidade(), controlarAcesso);
        String sqlStr = "SELECT * FROM ContratoOperacao WHERE contrato =  " + contrato.intValue() + " and tipoOperacao ='" + tipoOperacao.toUpperCase() + "' ORDER BY codigo";
        try (Statement stm = con.createStatement()) {
            try (ResultSet tabelaResultado = stm.executeQuery(sqlStr)) {
                return tabelaResultado.next();
            }
        }
    }

    public ContratoOperacaoVO consultarOperacaoContratoPorDataInicioCodigoContratoTipoOperacao(Date data, Integer contrato, String tipoOperacao, boolean controlarAcesso, int nivelMontarDados) throws Exception {
        consultar(getIdEntidade(), controlarAcesso);
        String sqlStr = "SELECT * FROM ContratoOperacao WHERE dataInicioEfetivacaoOperacao <= '" + Uteis.getDataJDBC(data) + "' and  datafimefetivacaooperacao >= '" + Uteis.getDataJDBC(data) + "' and contrato =  " + contrato.intValue() + " and tipoOperacao ='" + tipoOperacao.toUpperCase() + "' ORDER BY codigo DESC";
        try (Statement stm = con.createStatement()) {
            try (ResultSet tabelaResultado = stm.executeQuery(sqlStr)) {
                if (!tabelaResultado.next()) {
                    return new ContratoOperacaoVO();
                }
                return montarDados(tabelaResultado, nivelMontarDados, this.con);
            }
        }
    }

    public ContratoOperacaoVO consultarOperacaoContratoApenasInicioCodigoContratoTipoOperacao(Date data, Integer contrato, String tipoOperacao, boolean controlarAcesso, int nivelMontarDados) throws Exception {
        consultar(getIdEntidade(), controlarAcesso);
        String sqlStr = "SELECT * FROM ContratoOperacao WHERE dataInicioEfetivacaoOperacao = '" + Uteis.getDataJDBC(data) + "'  and contrato =  " + contrato.intValue() + " and tipoOperacao ='" + tipoOperacao.toUpperCase() + "' ORDER BY codigo DESC";
        try (Statement stm = con.createStatement()) {
            try (ResultSet tabelaResultado = stm.executeQuery(sqlStr)) {
                if (!tabelaResultado.next()) {
                    return new ContratoOperacaoVO();
                }
                return montarDados(tabelaResultado, nivelMontarDados, this.con);
            }
        }
    }

    public ContratoOperacaoVO obterOperacaoContratoPorDataEspecifica(Date data, Integer contrato, int nivelMontarDados) throws Exception {
        String sqlStr = "SELECT * FROM ContratoOperacao WHERE dataInicioEfetivacaoOperacao <= '" + Uteis.getDataJDBC(data) + "' and dataFimEfetivacaoOperacao >= '" + Uteis.getDataJDBC(data) + "' and contrato =  " + contrato.intValue();
        try (Statement stm = con.createStatement()) {
            try (ResultSet tabelaResultado = stm.executeQuery(sqlStr)) {
                if (!tabelaResultado.next()) {
                    return null;
                }
                return montarDados(tabelaResultado, nivelMontarDados, this.con);
            }
        }
    }

    @Override
    public boolean obterOperacaoContratoPorDataEspecifica(Date data, Integer contrato,
            String tipoOperacao) throws Exception {

        String sqlStr = "select exists (SELECT codigo FROM ContratoOperacao WHERE contrato = "
                + contrato.intValue() + " and tipoOperacao ='" + tipoOperacao.toUpperCase()
                + "' and '" + Uteis.getDataJDBC(data) + "' between dataInicioEfetivacaoOperacao and dataFimEfetivacaoOperacao limit 1) as existe";
        try (Statement stm = con.createStatement()) {
            try (ResultSet tabelaResultado = stm.executeQuery(sqlStr)) {
                tabelaResultado.next();
                return tabelaResultado.getBoolean("existe");
            }
        }
    }

    /**
     * Responsável por montar os dados de vários objetos, resultantes de uma consulta ao banco de dados (<code>ResultSet</code>).
     * Faz uso da operação <code>montarDados</code> que realiza o trabalho para um objeto por vez.
     * @return  List Contendo vários objetos da classe <code>ContratoOperacaoVO</code> resultantes da consulta.
     */
    public static List montarDadosConsulta(ResultSet tabelaResultado, int nivelMontarDados, Connection con) throws Exception {
        List vetResultado = new ArrayList();
        while (tabelaResultado.next()) {
            ContratoOperacaoVO obj = new ContratoOperacaoVO();
            obj = montarDados(tabelaResultado, nivelMontarDados, con);
            vetResultado.add(obj);
        }
        return vetResultado;
    }

    /**
     * Responsável por montar os dados resultantes de uma consulta ao banco de dados (<code>ResultSet</code>)
     * em um objeto da classe <code>ContratoOperacaoVO</code>.
     * @return  O objeto da classe <code>ContratoOperacaoVO</code> com os dados devidamente montados.
     */
    public static ContratoOperacaoVO montarDados(ResultSet dadosSQL, int nivelMontarDados, Connection con) throws Exception {
        ContratoOperacaoVO obj = new ContratoOperacaoVO();
        if (nivelMontarDados == Uteis.NIVELMONTARDADOS_ROBO) {
            obj.setCodigo(dadosSQL.getInt("codigo"));
            obj.setTipoOperacao(dadosSQL.getString("tipoOperacao"));
            obj.setDataInicioEfetivacaoOperacao(dadosSQL.getDate("dataInicioEfetivacaoOperacao"));
            obj.setDataFimEfetivacaoOperacao(dadosSQL.getDate("dataFimEfetivacaoOperacao"));
            obj.getTipoJustificativa().setCodigo(dadosSQL.getInt("tipoJustificativa"));
            return obj;
        }
        obj.setCodigo(dadosSQL.getInt("codigo"));
        obj.setContrato(dadosSQL.getInt("contrato"));
        obj.setTipoOperacao(dadosSQL.getString("tipoOperacao"));
        obj.setOperacaoPaga(dadosSQL.getBoolean("operacaoPaga"));
        obj.setDataOperacao(dadosSQL.getTimestamp("dataOperacao"));
        obj.setDataInicioEfetivacaoOperacao(dadosSQL.getDate("dataInicioEfetivacaoOperacao"));
        obj.setDataFimEfetivacaoOperacao(dadosSQL.getDate("dataFimEfetivacaoOperacao"));
        obj.getResponsavel().setCodigo(new Integer(dadosSQL.getInt("responsavel")));
        obj.setObservacao(dadosSQL.getString("observacao"));
        obj.setDescricaoCalculo(dadosSQL.getString("descricaoCalculo"));
        obj.getTipoJustificativa().setCodigo(dadosSQL.getInt("tipoJustificativa"));
        obj.getResponsavelLiberacao().setCodigo(dadosSQL.getInt("responsavelLiberacao"));
        obj.getClienteTransfereDias().setCodigo(dadosSQL.getInt("clienteTransfereDias"));
        obj.getClienteRecebeDias().setCodigo(dadosSQL.getInt("clienteRecebeDias"));
        obj.setNrDiasOperacao(dadosSQL.getInt("nrdiasoperacao"));
        obj.setInformacoes(dadosSQL.getString("informacoes"));
        obj.setChaveArquivo(dadosSQL.getString("chavearquivo"));
        obj.setOrigemSistema(OrigemSistemaEnum.getOrigemSistema(dadosSQL.getInt("origemSistema")));
        obj.setInformacoesDesfazer(dadosSQL.getString("informacoesDesfazer"));
        obj.setNovoObj(false);
        if (nivelMontarDados == Uteis.NIVELMONTARDADOS_DADOSBASICOS) {
            return obj;
        }
        if (nivelMontarDados == Uteis.NIVELMONTARDADOS_MOVIMENTACAOCONTRATOS) {
            montarDadosTipoJustificativa(obj, nivelMontarDados, con);
            return obj;
        }
        if (obj.getTipoOperacao().equals("CA")) {
            ReciboDevolucao recDev = new ReciboDevolucao(con);
            obj.setReciboDevolucao(recDev.consultarPorContrato(dadosSQL.getInt("contrato"), Uteis.NIVELMONTARDADOS_DADOSBASICOS));
            recDev = null;
        }
        montarDadosTipoJustificativa(obj, nivelMontarDados, con);
        montarDadosClienteTransfereDias(obj, nivelMontarDados, con);
        montarDadosClienteRecebeDias(obj, nivelMontarDados, con);
        montarDadosResponsavel(obj, nivelMontarDados, con);

        return obj;
    }

    /**
     * Operação responsável por montar os dados de um objeto da classe <code>CategoriaProdutoVO</code> relacionado ao objeto <code>ProdutoVO</code>.
     * Faz uso da chave primária da classe <code>CategoriaProdutoVO</code> para realizar a consulta.
     * @param obj  Objeto no qual será montado os dados consultados.
     */
    public static void montarDadosResponsavel(ContratoOperacaoVO obj, int nivelMontarDados, Connection con) throws Exception {
        if (obj.getResponsavel().getCodigo() == 0) {
            obj.setResponsavel(new UsuarioVO());
            return;
        }
        Usuario usuario = new Usuario(con);
        obj.setResponsavel(usuario.consultarPorChavePrimaria(obj.getResponsavel().getCodigo(), nivelMontarDados));
        usuario = null;
    }

    /**
     * Operação responsável por montar os dados de um objeto da classe <code>CategoriaProdutoVO</code> relacionado ao objeto <code>ProdutoVO</code>.
     * Faz uso da chave primária da classe <code>CategoriaProdutoVO</code> para realizar a consulta.
     * @param obj  Objeto no qual será montado os dados consultados.
     */
    public static void montarDadosTipoJustificativa(ContratoOperacaoVO obj, int nivelMontarDados, Connection con) throws Exception {
        if (obj.getTipoJustificativa().getCodigo() == 0) {
            obj.setTipoJustificativa(new JustificativaOperacaoVO());
            return;
        }
        JustificativaOperacao justificativaOperacao = new JustificativaOperacao(con);
        obj.setTipoJustificativa(justificativaOperacao.consultarPorChavePrimaria(obj.getTipoJustificativa().getCodigo(), nivelMontarDados));
        justificativaOperacao = null;
    }

    /**
     * Operação responsável por montar os dados de um objeto da classe <code>CategoriaProdutoVO</code> relacionado ao objeto <code>ProdutoVO</code>.
     * Faz uso da chave primária da classe <code>CategoriaProdutoVO</code> para realizar a consulta.
     * @param obj  Objeto no qual será montado os dados consultados.
     */
    public static void montarDadosClienteTransfereDias(ContratoOperacaoVO obj, int nivelMontarDados, Connection con) throws Exception {
        if (obj.getClienteTransfereDias().getCodigo() == 0) {
            obj.setClienteTransfereDias(new ClienteVO());
            return;
        }
        Cliente cliente = new Cliente(con);
        obj.setClienteTransfereDias(cliente.consultarPorChavePrimaria(obj.getClienteTransfereDias().getCodigo(), nivelMontarDados));
        cliente = null;
    }

    /**
     * Operação responsável por montar os dados de um objeto da classe <code>CategoriaProdutoVO</code> relacionado ao objeto <code>ProdutoVO</code>.
     * Faz uso da chave primária da classe <code>CategoriaProdutoVO</code> para realizar a consulta.
     * @param obj  Objeto no qual será montado os dados consultados.
     */
    public static void montarDadosClienteRecebeDias(ContratoOperacaoVO obj, int nivelMontarDados, Connection con) throws Exception {
        if (obj.getClienteRecebeDias().getCodigo() == 0) {
            obj.setClienteRecebeDias(new ClienteVO());
            return;
        }
        Cliente cliente = new Cliente(con);
        obj.setClienteRecebeDias(cliente.consultarPorChavePrimaria(obj.getClienteRecebeDias().getCodigo(), nivelMontarDados));
        cliente = null;
    }

    /**
     * Operação responsável por localizar um objeto da classe <code>ContratoOperacaoVO</code>
     * através de sua chave primária.
     * @exception Exception Caso haja problemas de conexão ou localização do objeto procurado.
     */
    public ContratoOperacaoVO consultarPorChavePrimaria(Integer codigoPrm, int nivelMontarDados) throws Exception {
        consultar(getIdEntidade(), false);
        String sql = "SELECT * FROM ContratoOperacao WHERE codigo = ?";
        try (PreparedStatement sqlConsultar = con.prepareStatement(sql)) {
            sqlConsultar.setInt(1, codigoPrm);
            try (ResultSet tabelaResultado = sqlConsultar.executeQuery()) {
                if (!tabelaResultado.next()) {
                    throw new ConsistirException("Dados Não Encontrados ( ContratoOperacao ).");
                }
                return (montarDados(tabelaResultado, nivelMontarDados, this.con));
            }
        }
    }

    public String consultarPorAssinaturaDigitalBiometria(Integer codigoContrato) throws Exception {
        String sql = "SELECT *"
        + " FROM pessoa pes"
        + " INNER JOIN contrato con"
        + " ON pes.codigo = con.pessoa"
        + " WHERE con.codigo = ?";

        try (PreparedStatement sqlConsultar = con.prepareStatement(sql)) {
            sqlConsultar.setInt(1, codigoContrato);

            try (ResultSet tabelaResultado = sqlConsultar.executeQuery()) {

                if (!tabelaResultado.next()) {
                    return null;
                }

                return tabelaResultado.getString("assinaturaDigitalBiometria");
            }
        }
    }

    @Override
    public boolean existeOperacaoRetroativoAEstaData(int codigoContrato,
            String tipoOperacao, Date dataBase) throws Exception {

        StringBuilder sb = new StringBuilder();
        sb.append("select exists (select contrato from contratoOperacao op ");
        sb.append("where op.contrato = %1$d ");
        sb.append("AND op.tipoOperacao = '%2$s' ");
        sb.append("AND cast(op.dataInicioEfetivacaoOperacao as date) <= '%3$s') ");
        sb.append("as existe");


        String sqlStr = String.format(sb.toString(),
                codigoContrato,
                tipoOperacao,
                Uteis.getDataJDBC(dataBase));

        try (PreparedStatement stm = con.prepareStatement(sqlStr)) {
            try (ResultSet tabelaResultado = stm.executeQuery()) {
                tabelaResultado.next();
                return tabelaResultado.getBoolean("existe");
            }
        }
    }

    @Override
    public boolean existeOperacaoInterceptaEstaData(int codigoContrato,
            String tipoOperacao, Date dataBase) throws Exception {

        StringBuilder sb = new StringBuilder();
        sb.append("select exists (select contrato from contratoOperacao op ");
        sb.append("where op.contrato = %1$d ");
        sb.append("AND op.tipoOperacao = '%2$s' ");
        sb.append("AND '%3$s' between cast(op.dataInicioEfetivacaoOperacao as date)");
        sb.append("AND cast(op.dataFimEfetivacaoOperacao as date))");
        sb.append("as existe");


        String sqlStr = String.format(sb.toString(),
                codigoContrato,
                tipoOperacao,
                Uteis.getDataJDBC(dataBase));

        try (PreparedStatement stm = con.prepareStatement(sqlStr)) {
            try (ResultSet tabelaResultado = stm.executeQuery()) {
                tabelaResultado.next();
                return tabelaResultado.getBoolean("existe");
            }
        }
    }

    @Override
    public boolean existeOperacaoParaEsteContrato(int codigoContrato,
            String tipoOperacao) throws Exception {

        StringBuilder sb = new StringBuilder();
        sb.append("select exists (select contrato from contratoOperacao op ");
        sb.append("where op.contrato = %1$d ");
        sb.append("AND op.tipoOperacao = '%2$s') as existe");


        String sqlStr = String.format(sb.toString(),
                codigoContrato,
                tipoOperacao);

        try (PreparedStatement stm = con.prepareStatement(sqlStr)) {
            try (ResultSet tabelaResultado = stm.executeQuery()) {
                tabelaResultado.next();
                return tabelaResultado.getBoolean("existe");
            }
        }
    }

    @Override
    public boolean existeOperacaoRetroativaParaEsteContrato(int codigoContrato,
            String tipoOperacao) throws Exception {

        StringBuilder sb = new StringBuilder();
        sb.append("select exists (select contrato from contratoOperacao op ");
        sb.append("where op.contrato = %1$d ");
        sb.append("AND op.dataOperacao > op.dataInicioEfetivacaoOperacao ");
        sb.append("AND op.tipoOperacao = '%2$s') as existe");


        String sqlStr = String.format(sb.toString(),
                codigoContrato,
                tipoOperacao);

        try (ResultSet tabelaResultado = criarConsulta(sqlStr, con)) {
            tabelaResultado.next();
            return tabelaResultado.getBoolean("existe");
        }
    }

    public int obterNumeroContratosCancelados(Date inicio, Date fim, List<ColaboradorVO> colaboradores, Integer codigoEmpresa) throws Exception {
        int total = 0;
        StringBuilder sql = obterContratosCanceladosSql(inicio, fim, colaboradores, codigoEmpresa, "COUNT(co.codigo) AS cont");
        try (ResultSet resultSet = ContratoOperacao.criarConsulta(sql.toString(), con)) {
            if (resultSet.next()) {
                total = resultSet.getInt("cont");
            }
        }
        return total;
    }

    public List<ClienteVO> obterClientesCancelados(Date inicio, Date fim,
            List<ColaboradorVO> colaboradores, Integer codigoEmpresa) throws Exception {

        StringBuilder sql = obterContratosCanceladosSql(inicio, fim, colaboradores, codigoEmpresa, "cli.*");
        try (ResultSet resultSet = criarConsulta(sql.toString(), con)) {
            return Cliente.montarDadosConsulta(resultSet, Uteis.NIVELMONTARDADOS_DADOSBASICOS, con);
        }


    }

    private StringBuilder obterContratosCanceladosSql(Date inicio, Date fim,
            List<ColaboradorVO> colaboradores, Integer codigoEmpresa, String campo) throws Exception {
        boolean filtrarColaborador = false;
        String codColaboradores = "";
        for (ColaboradorVO colaborador : colaboradores) {
            if (colaborador.getColaboradorEscolhidoOperacoes()) {
                codColaboradores = codColaboradores + "," + colaborador.getCodigo();
                filtrarColaborador = true;
            }

        }

        StringBuilder sql = new StringBuilder();
        sql.append("SELECT " + campo + " FROM contratooperacao co, usuario u, contrato c, cliente cli ");
        sql.append("WHERE tipooperacao LIKE 'CA' ");
        sql.append("AND dataoperacao BETWEEN '" + Uteis.getDataJDBC(inicio) + " 00:00:00' AND '" + Uteis.getDataJDBC(fim) + " 23:59:59' ");
        sql.append("AND u.codigo = co.responsavel ");
        sql.append("AND c.codigo = co.contrato ");
        sql.append("AND c.pessoa = cli.pessoa ");
        if( codigoEmpresa != 0){
            sql.append("AND c.empresa = " + codigoEmpresa + " ");
        }
        if (filtrarColaborador) {
            sql.append("AND u.colaborador IN (" + codColaboradores.replaceFirst(",", "") + ")");
        }
        return sql;
    }

    public int obterNumeroClientesComBonus(Date fim, List<ColaboradorVO> colaboradores, Integer codigoEmpresa) throws Exception {
        int total = 0;
        StringBuilder sql = obterClientesComBonusSql(fim, colaboradores, codigoEmpresa, "distinct COUNT(cli.codigo) AS cont", true);
        try (ResultSet resultSet = ContratoOperacao.criarConsulta(sql.toString(), con)) {
            if (resultSet.next()) {
                total = resultSet.getInt("cont");
            }
        }
        return total;
    }

    public List<PendenciaResumoPessoaRelVO> obterClientesComBonus(Date fim,
            List<ColaboradorVO> colaboradores, Integer codigoEmpresa) throws Exception {

        StringBuilder sql = obterClientesComBonusSql(fim, colaboradores, codigoEmpresa, " distinct (cli.*), co.*, jo.descricao",true);
        List<PendenciaResumoPessoaRelVO> listaPendenciaResumoPessoaRelVOs;
        try (ResultSet resultSet = criarConsulta(sql.toString(), con)) {
            listaPendenciaResumoPessoaRelVOs = new ArrayList<PendenciaResumoPessoaRelVO>();
            while (resultSet.next()) {
                PendenciaResumoPessoaRelVO pendenciaResumoPessoaRelVO = new PendenciaResumoPessoaRelVO();
                pendenciaResumoPessoaRelVO.setClienteVO(Cliente.montarDados(resultSet, Uteis.NIVELMONTARDADOS_DADOSBASICOS, con));
                pendenciaResumoPessoaRelVO.setContratoOperacaoVO(montarDados(resultSet, Uteis.NIVELMONTARDADOS_DADOSBASICOS, con));
                pendenciaResumoPessoaRelVO.getContratoOperacaoVO().setJustificativaApresentar(resultSet.getString("descricao"));
                listaPendenciaResumoPessoaRelVOs.add(pendenciaResumoPessoaRelVO);
            }
        }
        return listaPendenciaResumoPessoaRelVOs;
    }

    public int obterNumeroClientesComFreePass(Date data, List<ColaboradorVO> colaboradores, Integer codigoEmpresa) throws Exception {
        int total = 0;
        StringBuilder sql = obterClientesComFreePassSql(data,colaboradores, codigoEmpresa, "count(distinct(cli.codigo)) AS cont");
        try (ResultSet resultSet = ContratoOperacao.criarConsulta(sql.toString(), con)) {
            if (resultSet.next()) {
                total = resultSet.getInt("cont");
            }
        }
        return total;
    }

    public List<PendenciaResumoPessoaRelVO> obterClientesComFreePass(Date data,
            List<ColaboradorVO> colaboradores, Integer codigoEmpresa) throws Exception {

        StringBuilder sql = obterClientesComFreePassSql(data, colaboradores, codigoEmpresa, " distinct (cli.*),coalesce(pa.dataLancamento,pa.datainicioacesso) as dataLancamento, us.nome as responsavel, (EXTRACT(DAY FROM pa.datafinalacesso - pa.datainicioacesso) + 1) AS qtd, p.descricao AS produtodescricao");
        List<PendenciaResumoPessoaRelVO> listaPendenciaResumoPessoaRelVOs;
        try (ResultSet resultSet = criarConsulta(sql.toString(), con)) {
            listaPendenciaResumoPessoaRelVOs = new ArrayList<PendenciaResumoPessoaRelVO>();
            while (resultSet.next()) {
                PendenciaResumoPessoaRelVO pendenciaResumoPessoaRelVO = new PendenciaResumoPessoaRelVO();
                pendenciaResumoPessoaRelVO.setClienteVO(Cliente.montarDados(resultSet, Uteis.NIVELMONTARDADOS_DADOSBASICOS, con));
                pendenciaResumoPessoaRelVO.getColaboradorVO().getPessoa().setNome(resultSet.getString("responsavel"));
                pendenciaResumoPessoaRelVO.setQtdFreePass(resultSet.getInt("qtd"));
                pendenciaResumoPessoaRelVO.setDataRegistro(resultSet.getDate("dataLancamento"));
                pendenciaResumoPessoaRelVO.setProdutoDescricao(resultSet.getString("produtodescricao"));
                listaPendenciaResumoPessoaRelVOs.add(pendenciaResumoPessoaRelVO);
            }
        }
        return listaPendenciaResumoPessoaRelVOs;
    }

    public int obterNumeroClientesComGymPass(Date data, List<ColaboradorVO> colaboradores, Integer codigoEmpresa) throws Exception {
        int total = 0;
        StringBuilder sql = obterClientesComGymPassSql(data,colaboradores, codigoEmpresa, "count(distinct(cli.codigo)) AS cont");
        try (ResultSet resultSet = ContratoOperacao.criarConsulta(sql.toString(), con)) {
            if (resultSet.next()) {
                total = resultSet.getInt("cont");
            }
        }
        return total;
    }

    public List<PendenciaResumoPessoaRelVO> obterClientesComGymPass( Date data,
                                                                      List<ColaboradorVO> colaboradores, Integer codigoEmpresa) throws Exception {

        StringBuilder sql = obterClientesComGymPassSql(data,colaboradores, codigoEmpresa, " distinct (cli.*),coalesce(pa.dataLancamento,pa.datainicioacesso) as dataLancamento, us.nome as responsavel, pa.tokengympass, (EXTRACT(DAY FROM pa.datafinalacesso - pa.datainicioacesso) + 1) AS qtd");
        List<PendenciaResumoPessoaRelVO> listaPendenciaResumoPessoaRelVOs;
        try (ResultSet resultSet = criarConsulta(sql.toString(), con)) {
            listaPendenciaResumoPessoaRelVOs = new ArrayList<PendenciaResumoPessoaRelVO>();
            while (resultSet.next()) {
                PendenciaResumoPessoaRelVO pendenciaResumoPessoaRelVO = new PendenciaResumoPessoaRelVO();
                pendenciaResumoPessoaRelVO.setClienteVO(Cliente.montarDados(resultSet, Uteis.NIVELMONTARDADOS_DADOSBASICOS, con));
                pendenciaResumoPessoaRelVO.getColaboradorVO().getPessoa().setNome(resultSet.getString("responsavel"));
                pendenciaResumoPessoaRelVO.setQtdGymPass(resultSet.getInt("qtd"));
                pendenciaResumoPessoaRelVO.setDataRegistro(resultSet.getDate("dataLancamento"));
                pendenciaResumoPessoaRelVO.setToken(resultSet.getString("tokengympass"));
                listaPendenciaResumoPessoaRelVOs.add(pendenciaResumoPessoaRelVO);
            }
        }
        return listaPendenciaResumoPessoaRelVOs;
    }

    private StringBuilder obterClientesComGymPassSql(Date data,List<ColaboradorVO> colaboradores, Integer codigoEmpresa, String campo) throws Exception {
        boolean filtrarColaborador = false;
        String codColaboradores = "";
        for (ColaboradorVO colaborador : colaboradores) {
            if (colaborador.getColaboradorEscolhidoOperacoes()) {
                codColaboradores = codColaboradores + "," + colaborador.getCodigo();
                filtrarColaborador = true;
            }
        }
        StringBuilder sql = new StringBuilder();
        sql.append("SELECT\n");
        sql.append("  ").append(campo).append("\n");
        sql.append("FROM cliente cli\n");
        sql.append("  inner join periodoacessocliente pa on pa.pessoa = cli.pessoa\n");
        sql.append("  LEFT JOIN usuario us\n");
        sql.append("    ON pa.responsavel = us.codigo\n");
        sql.append(" WHERE 1 = 1 \n");
        sql.append(" AND coalesce(pa.tokengympass, '') <> '' \n");
        sql.append(" AND pa.tipoacesso = 'PL'\n");
        sql.append("     AND '").append(Uteis.getDataJDBC(data)).append("' BETWEEN pa.datainicioacesso  AND pa.datafinalacesso\n");
        if(codigoEmpresa != 0){
            sql.append("      AND cli.empresa = ").append(codigoEmpresa).append("\n");
        }
        if (filtrarColaborador) {
            sql.append("      AND us.colaborador   IN (").append(codColaboradores.replaceFirst(",", "")).append(")");
        }
        return sql;
    }
    
    private StringBuilder obterClientesComGymPassSql(Date dataIncial,Date dataFinal, Integer codigoEmpresa,String campo) throws Exception{
        StringBuilder sql = new StringBuilder();
        sql.append("SELECT\n");
        sql.append("  ").append(campo).append("\n");
        sql.append("FROM cliente cli\n");
        sql.append("  inner join periodoacessocliente pa on pa.pessoa = cli.pessoa\n");
        sql.append("  LEFT JOIN usuario us\n");
        sql.append("    ON pa.responsavel = us.codigo\n");
        sql.append(" WHERE 1 = 1 \n");
        sql.append(" AND coalesce(pa.tokengympass, '') <> '' \n");
        sql.append(" AND pa.tipoacesso = 'PL'\n");
        sql.append(" AND pa.datainicioacesso >= '"+Uteis.getDataJDBC(dataIncial)+"' ");
        sql.append(" AND pa.datafinalacesso <= '"+Uteis.getDataJDBC(dataFinal)+"' ");
        //sql.append("     AND '").append(Uteis.getDataJDBC(data)).append("' BETWEEN pa.datainicioacesso  AND pa.datafinalacesso\n");
        sql.append("      AND cli.empresa = ").append(codigoEmpresa).append("\n");

        return sql;
    }
    
    private StringBuilder obterClientesComFreePassSql(Date data,List<ColaboradorVO> colaboradores, Integer codigoEmpresa, String campo) throws Exception {
        boolean filtrarColaborador = false;
        String codColaboradores = "";
        for (ColaboradorVO colaborador : colaboradores) {
            if (colaborador.getColaboradorEscolhidoOperacoes()) {
                codColaboradores = codColaboradores + "," + colaborador.getCodigo();
                filtrarColaborador = true;
            }
        }
        StringBuilder sql = new StringBuilder();
        sql.append("SELECT\n");
        sql.append("  ").append(campo).append("\n");
        sql.append("FROM cliente cli\n");
        sql.append("  INNER JOIN periodoacessocliente pa ON pa.pessoa = cli.pessoa\n");
        sql.append("  LEFT JOIN produto p\n");
        sql.append("    ON pa.produto = p.codigo\n");
        sql.append("  LEFT JOIN usuario us\n");
        sql.append("    ON pa.responsavel = us.codigo\n");
        sql.append(" WHERE 1 = 1 \n");
        sql.append(" AND coalesce(pa.tokengympass, '') = '' \n");
        sql.append(" AND pa.tipoacesso = 'PL'\n");
        sql.append("     AND '").append(Uteis.getDataJDBC(data)).append("' BETWEEN pa.datainicioacesso AND pa.datafinalacesso\n");
        if(codigoEmpresa != 0){
            sql.append("      AND cli.empresa = ").append(codigoEmpresa).append("\n");
        }
        if (filtrarColaborador) {
            sql.append("      AND us.colaborador   IN (").append(codColaboradores.replaceFirst(",", "")).append(")");
        }

        return sql;
    }

    private StringBuilder obterClientesComBonusSql(Date fim,
            List<ColaboradorVO> colaboradores, Integer codigoEmpresa, String campo, boolean ignorarAutomatico) throws Exception {
        boolean filtrarColaborador = false;
        String codColaboradores = "";
        for (ColaboradorVO colaborador : colaboradores) {
            if (colaborador.getColaboradorEscolhidoOperacoes()) {
                codColaboradores = codColaboradores + "," + colaborador.getCodigo();
                filtrarColaborador = true;
            }
        }
        StringBuilder sql = new StringBuilder();
        sql.append("SELECT "+campo+" FROM contratooperacao co ");
        sql.append("left join usuario as us on us.codigo = co.responsavel ");
        sql.append("inner join contrato as cont on cont.codigo = co.contrato ");
        sql.append("inner join cliente as cli on cli.pessoa = cont.pessoa ");
        sql.append("inner join justificativaoperacao jo on co.tipojustificativa = jo.codigo ");
        sql.append("where (co.tipooperacao LIKE 'BA' or co.tipooperacao like 'BR') ");
        if(codigoEmpresa != 0){
            sql.append(" and cont.empresa = "+codigoEmpresa);
        }
        sql.append(" and (('"+Uteis.getDataJDBC(fim)+"' >= co.datainicioefetivacaooperacao and '"+Uteis.getDataJDBC(fim)+"' <= co.datafimefetivacaooperacao ) ");
        //se for o mês corrente deve ser considerado bonus que iniciam no futuro
        if(Uteis.getMesData(fim)==Uteis.getMesData(Calendario.hoje())){
            sql.append(" or co.datainicioefetivacaooperacao >= '"+ Uteis.getDataJDBC(fim)+"')");
        }else{
            sql.append(" )");
        }
        if (filtrarColaborador) {
            sql.append("AND us.colaborador IN (" + codColaboradores.replaceFirst(",", "") + ")");
        }
        if(ignorarAutomatico){
            sql.append("AND us.colaborador is not null"); //ignora lançamento do recorrencia
        }
        return sql;
    }

    public void ajustarRenovacoesFuturas(ContratoVO contrato, String operacao, UsuarioVO usuario, Integer codigoContratoAfetado) throws Exception {
        ContratoVO contratoFuturo = getFacade().getContrato().obterRenovacaoContrato(contrato.getCodigo(), false,
                Uteis.NIVELMONTARDADOS_TODOS);
        if(!UteisValidacao.emptyNumber(contratoFuturo.getCodigo())){
            Date data = contrato.getVigenciaDe();
            inicializarDadosContratoRenovacao(contratoFuturo, contrato);
            inicializarDadosPeriodoAcessoContratoRenovacao(contratoFuturo, data);
            inicializarDadosContratoOperacaoContratoRenovacao(contratoFuturo, contrato, operacao,usuario,codigoContratoAfetado);
            inicializarDadosHistoricoContratoContratoRenovacao(contratoFuturo, contrato, data);
            atualizaDataFimDeProdutoComVigenciaDeContrato(contratoFuturo.getCodigo(), contratoFuturo.getVigenciaDe(), contratoFuturo.getVigenciaAteAjustada());
            if(contratoFuturo.isVendaCreditoTreino()){
                ajustarDuracaoCreditoTreino(contratoFuturo);
            }
            if (contratoFuturo.getContratoResponsavelRenovacaoMatricula() != 0) {
                ajustarRenovacoesFuturas(contratoFuturo, operacao, usuario, codigoContratoAfetado);
            }
            if (contratoFuturo.getPlano().getQuantidadeCompartilhamentos() > 0) {
                atualizarVigenciaContratosDependentes(contratoFuturo);
            }
        }
    }

    private void atualizarVigenciaContratosDependentes(ContratoVO contratoVO) throws Exception {
        try {
            ContratoDependente contratoDependenteDAO = new ContratoDependente(con);
            List<ContratoDependenteVO> contratoDependenteVOS = contratoDependenteDAO.findAllByContrato(contratoVO.getCodigo());
            for (ContratoDependenteVO contratoDependenteVO : contratoDependenteVOS) {
                if (!UteisValidacao.emptyNumber(contratoDependenteVO.getCliente().getCodigo())) {
                    continue;
                }
                contratoDependenteVO.setDataInicio(contratoVO.getVigenciaDe());
                contratoDependenteVO.setDataFinalAjustada(contratoVO.getVigenciaAteAjustada());
                contratoDependenteDAO.alterarVigencia(contratoDependenteVO);
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    public void ajustarDuracaoCreditoTreino(ContratoVO contrato) throws Exception {
        try{
            ContratoDuracaoCreditoTreinoVO contratoDuracaoCreditoTreinoVO = getFacade().getContratoDuracaoCreditoTreino().consultarPorContrato(contrato.getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS);
            getFacade().getContratoDuracaoCreditoTreino().alterarDataUltimoCreditoMensal(contratoDuracaoCreditoTreinoVO, contrato.getVigenciaDe());
        } catch (Exception e) {
            throw e;
        }
    }

    public void inicializarDadosContratoRenovacao(ContratoVO contratoRenovacao, ContratoVO obj) throws Exception {
        try {
            Date dataInicioAnterior = contratoRenovacao.getVigenciaDe();
            contratoRenovacao.obterDataFinalContratoComContratoDuracao(Uteis.obterDataFutura2(obj.getVigenciaAteAjustada(), 1));
            getFacade().getContrato().alterarDatasVigenciaContrato(contratoRenovacao);
            inicializarDadosMatriculaTurmaContratoRenovacao(contratoRenovacao, dataInicioAnterior);
        } catch (Exception e) {
            throw e;
        }

    }

    public void inicializarDadosPeriodoAcessoContratoRenovacao(ContratoVO contrato, Date data) throws Exception {
        try {
            PeriodoAcessoClienteVO periodoAcesso = getFacade().getPeriodoAcessoCliente()
                    .obterUltimoDiaPeriodoAcessoContrato(contrato.getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS);
            if(periodoAcesso == null){
                return;
            }
            Date fim = periodoAcesso.getDataFinalAcesso();
            if(periodoAcesso.getTipoAcesso().equals("CA")){
            periodoAcesso.setDataInicioAcesso(contrato.getVigenciaDe());
            periodoAcesso.setDataFinalAcesso(contrato.getVigenciaAteAjustada());
                getFacade().getPeriodoAcessoCliente().alterarSemCommit(periodoAcesso);
            }else{
                List<PeriodoAcessoClienteVO> periodos = getFacade().getPeriodoAcessoCliente().consultarPorContrato(contrato.getCodigo(), false, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
                periodos = Ordenacao.ordenarLista(periodos, "dataFinalAcesso");
                Collections.reverse(periodos);
                Long diasDiferenca = Uteis.nrDiasEntreDatas(fim, contrato.getVigenciaAteAjustada());
                for(PeriodoAcessoClienteVO p : periodos){
                    if(p.getTipoAcesso().equals("CA")){
                        p.setDataInicioAcesso(contrato.getVigenciaDe());
                    }else{
                        p.setDataInicioAcesso(Uteis.somarDias(p.getDataInicioAcesso(), diasDiferenca.intValue()));
                    }
                    p.setDataFinalAcesso(Uteis.somarDias(p.getDataFinalAcesso(), diasDiferenca.intValue()));
                    getFacade().getPeriodoAcessoCliente().alterarSemCommit(p);
                }
            }
        } catch (Exception e) {
            throw e;
        }

    }
    public void inicializarDadosContratoOperacaoContratoRenovacao(ContratoVO contratoRenovacao, ContratoVO obj, String operacao, UsuarioVO responsavel, Integer codigoContratoAfetado) throws Exception {
        try {
            ContratoOperacaoVO objContratoOperacaoVO = new ContratoOperacaoVO();
            objContratoOperacaoVO.setContrato(contratoRenovacao.getCodigo());
            objContratoOperacaoVO.setDataFimEfetivacaoOperacao(negocio.comuns.utilitarias.Calendario.hoje());
            objContratoOperacaoVO.setDataInicioEfetivacaoOperacao(negocio.comuns.utilitarias.Calendario.hoje());
            objContratoOperacaoVO.setDataOperacao(negocio.comuns.utilitarias.Calendario.hoje());
            objContratoOperacaoVO.setDescricaoCalculo("");
            objContratoOperacaoVO.setObservacao("Modificação: \n\r"
                    + " Data de Início,\n\r"
                    + " Data de Término,\n\r"
                    + " Data de Previsão Renovação,\n\r"
                    + " Data de Previsão Rematricula,\n\r"
                    + " Devido uma OPERAÇÃO de "+operacao+" no contrato de Numero: " + codigoContratoAfetado + ".");
            objContratoOperacaoVO.setOperacaoPaga(false);
            objContratoOperacaoVO.setResponsavel(responsavel);
            objContratoOperacaoVO.setTipoOperacao("AC");
            getFacade().getContratoOperacao().incluirSemCommit(objContratoOperacaoVO, false);
        } catch (Exception e) {
            throw e;
        }
    }

    public void inicializarDadosHistoricoContratoContratoRenovacao(ContratoVO contratoRenovacao,
            ContratoVO obj, Date dataVigencia) throws Exception {
        try {
            //ajustar o historico do contrato renovado
            executarUpdate("update contrato set situacao = 'AT' where codigo = " + contratoRenovacao.getCodigo(), con);
            executarUpdate("delete from historicoContrato where contrato =  " + contratoRenovacao.getCodigo() + " and tipohistorico in ('AV', 'VE', 'DE')", con);
            HistoricoContratoVO objHistoricoContratoVO = getFacade().getHistoricoContrato().
                    obterUltimoHistoricoContratoPorContrato(contratoRenovacao.getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS);
            if (objHistoricoContratoVO != null) {
                objHistoricoContratoVO.setDataInicioSituacao(contratoRenovacao.getVigenciaDe());
                objHistoricoContratoVO.setDataFinalSituacao(contratoRenovacao.getVigenciaAteAjustada());
                getFacade().getHistoricoContrato().alterarSemCommit(objHistoricoContratoVO, false);
            } else {
                throw new Exception(String.format("Não foi possível encontrar histórico do contrato posterior "+contratoRenovacao.getCodigo()+" nesta data"));
            }
   
        } catch (Exception e) {
            throw e;
        }
    }

    public void inicializarDadosMatriculaTurmaContratoRenovacao(ContratoVO contratoSucessor, Date dataInicioAnterior) throws Exception {
        if (contratoSucessor.isVendaCreditoTreino()){
            Integer saldo = ControleCreditoTreino.consultarSaldoCredito(con, contratoSucessor.getCodigo());
            Integer aulasDesmarcasPassadasARepor = getFacade().getAulaDesmarcada().contarAulasDesmarcadasPorPeriodo(contratoSucessor.getCodigo(), 0,null,contratoSucessor.getVigenciaDe()); //aulas que já passaram e não foram repostas e não consumiram crédito
            contratoSucessor.gerarVigenciaMatriculaPlanoCreditoTreino(contratoSucessor.getVigenciaDe(),(saldo - aulasDesmarcasPassadasARepor));
        }
        List<MatriculaAlunoHorarioTurmaVO> lista = getFacade().getMatriculaAlunoHorarioTurma().consultarMatriculaAtiva(contratoSucessor.getCodigo(), dataInicioAnterior);
        for (MatriculaAlunoHorarioTurmaVO matricula : lista) {
            matricula.setDataInicio(contratoSucessor.getVigenciaDe());
            if (contratoSucessor.getVigenciaTurmaCreditoTreinoAte() != null){
                matricula.setDataFim(contratoSucessor.getVigenciaTurmaCreditoTreinoAte());
            } else if (contratoSucessor.getContratoResponsavelRenovacaoMatricula() == null || contratoSucessor.getContratoResponsavelRenovacaoMatricula() == 0) {
                matricula.setDataFim(Uteis.somarDias(contratoSucessor.getVigenciaAte(), contratoSucessor.getEmpresa().getToleranciaOcupacaoTurma()));
            } else {
                matricula.setDataFim(contratoSucessor.getVigenciaAte());
            }
            getFacade().getMatriculaAlunoHorarioTurma().alterarInicioFimMatriculaSemCommit(matricula);
        }

    }
    
    public ContratoVO estornarContratoOperacao(ContratoOperacaoVO obj, UsuarioVO responsavel) throws Exception {
        try{
            ContratoOperacaoVO contratoOperacao = consultarPorChavePrimaria(obj.getCodigo(), Uteis.NIVELMONTARDADOS_ROBO);
        } catch (Exception e) {
            throw new Exception("A operação parace já ter sido estornada. Consulte novamente o contrato e verifique se o estorno foi feito.");
        }
        ContratoVO contrato = getFacade().getContrato().consultarPorChavePrimaria(obj.getContrato(),
                Uteis.NIVELMONTARDADOS_TODOS);
        try {
            con.setAutoCommit(false);

            if ((obj.getTipoOperacao().equals("BA") || obj.getTipoOperacao().equals("AT")) && Calendario.maior(obj.getDataOperacao(), contrato.getVigenciaAteAjustada())) { //operações em contratos inativos
                excluirSemCommit(obj);
                // neste caso a data da operacao  é usada como nova vigencia para encontrar as matriculas adicionadas por essa operacao
                alterarMatriculas(contrato, contrato.getVigenciaAteAjustada(), obj.getDataOperacao(), false ,obj.getDataInicioEfetivacaoOperacao(),"Estorno_Bonus_Atestado_ContratoInativo");
                ajustarPeriodoAcesso(obj, contrato);
                logEstornoOperacao(obj, responsavel, contrato);
                con.commit();
                return contrato;
            }
            long nrDias = Uteis.nrDiasEntreDatas(obj.getDataInicioEfetivacaoOperacao(), obj.getDataFimEfetivacaoOperacao());
            nrDias++;

            Boolean alterarDatas = true;
            Boolean incluiuHistorico = true;
            //validar
            if(obj.getTipoOperacao().equals(TipoOperacaoContratoEnum.TRANCAMENTO.getSigla())){
                alterarDatas = verificarSeTeveRetorno(obj.getCodigo(), obj.getContrato());
                deletarProdutoTrancamento(obj.getContrato());
                deletarRelacaoTrancamento(obj.getCodigo(), obj.getContrato(), "RT");
                deletarRelacaoTrancamento(obj.getCodigo(), obj.getContrato(), "TV");
                deletarRegistroTrancamentoContrato(obj.getContrato());
                try {
                    deletarHistoricoTrancamento(obj.getContrato());
                } catch (Exception e){ 
                    if(Calendario.maiorOuIgual(Calendario.hoje(), obj.getDataInicioEfetivacaoOperacao())){
                        throw e;
                    }
                }
            }
            if(alterarDatas){
                int multiplicador = obj.getTipoOperacao().equals("BR") ? 1 : -1;
                Date novaData = Uteis.somarDias(contrato.getVigenciaAteAjustada(), (int) (nrDias * multiplicador));
                contrato.setVigenciaAteAjustada(novaData);
                contrato.setDataPrevistaRenovar(novaData);
            }else {
                contrato.setDataPrevistaRenovar(contrato.getVigenciaAteAjustada());
            }
            getFacade().getContrato().alterarDatasVigenciaContrato(contrato);
            excluirSemCommit(obj);

            // --------------- se não for retroativo, ajustar historico contrato
            if (Calendario.maiorOuIgual(obj.getDataFimEfetivacaoOperacao(), obj.getDataOperacao()) && !obj.getTipoOperacao().equals(TipoOperacaoContratoEnum.TRANCAMENTO.getSigla())) {
                try (ResultSet rs = criarConsulta("SELECT * FROM historicocontrato WHERE contrato = " + contrato.getCodigo()
                        + " ORDER BY datafinalsituacao DESC", con)) {
                    ajustarHistorico(obj, contrato, rs, "datainiciosituacao", "datafinalsituacao", "tipohistorico",
                            "historicocontrato");
                }
            } else {
                ajustarUltimoHistorico(contrato);
            }

            // ---------------ajustar periodo acesso cliente
            ajustarPeriodoAcesso(obj, contrato);
            alterarMatriculas(contrato, Calendario.hoje(), contrato.getVigenciaAteAjustada(),true,obj.getDataInicioEfetivacaoOperacao(),"");
            atualizaDataFimDeProdutoComVigenciaDeContrato(contrato.getCodigo(), null, contrato.getVigenciaAteAjustada());
            if (contrato.getContratoResponsavelRenovacaoMatricula() != 0) {
                ajustarRenovacoesFuturas(contrato, "Estorno Carencia/Atestado", responsavel,contrato.getCodigo());
            }
            getFacade().getAulaDesmarcada().excluirAulaDesmarcadaPorRetornoAfastamentoAntecipado(obj.getContrato(), Calendario.getMaior( obj.getDataInicioEfetivacaoOperacao() , Calendario.hoje()) , obj.getDataFimEfetivacaoOperacao());

            logEstornoOperacao(obj, responsavel, contrato);

            if (Calendario.maior(Calendario.hoje(), contrato.getVigenciaAteAjustada())) {
                RoboVO roboVO = new RoboVO();
                roboVO.setUsuarioVO(responsavel);
                roboVO.trataContratoAVencer(contrato.getEmpresa(), contrato);
                roboVO.trataContratoVencido(contrato.getEmpresa(), contrato, Calendario.hoje());
                roboVO.trataContratoDesistente(contrato.getEmpresa(), contrato);
                roboVO = null;
            } else {
                if(contrato.getSituacao().equals("TR") && Calendario.menorOuIgual(obj.getDataInicioEfetivacaoOperacao(), Calendario.hoje())) {
                    SuperFacadeJDBC.executarUpdate("Update contrato set situacao = 'AT' where codigo =" + contrato.getCodigo(), con);
                }
            }
            con.commit();
        } catch (Exception e) {
            con.rollback();
            con.setAutoCommit(true);
            throw e;
        } finally {
            con.setAutoCommit(true);
        }
        return contrato;
    }

    private void logEstornoOperacao(ContratoOperacaoVO obj, UsuarioVO responsavel, ContratoVO contrato) throws Exception {
        LogVO log = new LogVO();
        log.setOperacao("ESTORNO DE OPERAÇÃO");
        log.setChavePrimaria(obj.getCodigo().toString());
        log.setDataAlteracao(Calendario.hoje());
        log.setDescricao("Estorno de operação de contrato - " + obj.getTipoOperacao_Apresentar());
        log.setPessoa(contrato.getPessoa().getCodigo());
        log.setNomeEntidade("CONTRATO OPERAÇÃO");
        log.setResponsavelAlteracao(responsavel.getNome());
        log.setUserOAMD(responsavel.getUserOamd());
        log.setUsuarioVO(responsavel);
        log.setNomeCampo("OBS.");
        log.setValorCampoAlterado("Operação de " + obj.getTipoOperacao_Apresentar() + " do contrato " + contrato.getCodigo()
                + ", iniciada no dia " + Uteis.getData(obj.getDataInicioEfetivacaoOperacao()) + " e finalizada no dia "
                + Uteis.getData(obj.getDataFimEfetivacaoOperacao()) + ", lançada em " + Uteis.getData(obj.getDataOperacao())
                + " foi estornada, alterando o histórico do contrato e período de acesso do cliente. excluido desmarcação de aula a pardir da data da operação ");
        getFacade().getLog().incluirSemCommit(log);
    }

    private void ajustarPeriodoAcesso(ContratoOperacaoVO obj, ContratoVO contrato) throws Exception {
        try (ResultSet result = criarConsulta("SELECT * FROM periodoacessocliente WHERE contrato = " + contrato.getCodigo()
                + " ORDER BY datainicioacesso DESC", con)) {
            ajustarPeriodoAcesso(obj, contrato, result, "datainicioacesso", "datafinalacesso", "tipoacesso",
                    "periodoacessocliente");
        }
    }

    private void deletarRelacaoTrancamento(Integer codigotrancamento, Integer codigocontrato, String tipoOp) throws Exception{
        SuperFacadeJDBC.executarConsultaUpdate("DELETE from contratooperacao  where tipooperacao = '" + tipoOp + "' " +
                " AND codigo > " + codigotrancamento +
                " and contrato = " + codigocontrato,con);
    }
    
    private Boolean verificarSeTeveRetorno(Integer codigotrancamento, Integer codigocontrato) throws Exception{
        try (ResultSet qs = con.prepareStatement("SELECT * from contratooperacao WHERE tipooperacao = 'RT' " +
                " AND codigo > " + codigotrancamento +
                " and contrato = " + codigocontrato).executeQuery()) {
            return qs.next();
        }
    }

    private void deletarHistoricoTrancamento(Integer codigocontrato) throws Exception{
        try (ResultSet rs = con.prepareStatement("select codigo from historicocontrato  where contrato = " + codigocontrato + " AND tipohistorico = 'TR' ORDER BY datainiciosituacao DESC").executeQuery()) {
            if (rs.next()) {
                SuperFacadeJDBC.executarConsultaUpdate("DELETE from historicocontrato where tipohistorico = 'RT' " +
                        " AND codigo > " + rs.getInt("codigo") +
                        " and contrato = " + codigocontrato,con);
                SuperFacadeJDBC.executarConsultaUpdate("DELETE from historicocontrato where codigo = " + rs.getInt("codigo"),con) ;
            } else {
                throw new Exception("Não foi possível identificar o histórico do trancamento.");
            }
        }

    }
    private void deletarProdutoTrancamento(Integer codigocontrato) throws Exception{
        try (ResultSet rs = SuperFacadeJDBC.criarConsulta("SELECT movproduto.codigo, situacao, recibopagamento  FROM movproduto " +
                " inner join movprodutoparcela mpp on mpp.movproduto = movproduto.codigo " +
                "WHERE contrato = " + codigocontrato +
                " and descricao = 'TRANCAMENTO CONTRATO' ORDER BY codigo DESC LIMIT 1", con)) {
            if (rs.next()) {
                if (UteisValidacao.emptyNumber(rs.getInt("recibopagamento"))) {
                    con.prepareStatement("DELETE FROM movparcela WHERE codigo in (SELECT movparcela FROM movprodutoparcela where movproduto = " +
                            rs.getInt("codigo") + ")").execute();
                    con.prepareStatement("DELETE FROM movprodutoparcela where movproduto = " + rs.getInt("codigo")).execute();
                    con.prepareStatement("DELETE FROM movproduto where codigo = " + rs.getInt("codigo")).execute();
                    PagamentoMovParcela.corrigirPagamentosSemMovParcelas(con);
                } else {
                    throw new Exception("O trancamento não pode ser estornado pois a parcela gerada referente ao trancamento está paga. Estorne o pagamento dessa parcela e tente novamente.");
                }
            } else {
                throw new Exception("Não foi possível identificar o produto do trancamento.");
            }
        }

    }

    private void ajustarUltimoHistorico(ContratoVO contrato) throws Exception {
        HistoricoContratoVO ultimo = getFacade().getHistoricoContrato().obterUltimoHistoricoContratoPorContrato(contrato.getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS);
        if(ultimo.getTrancado()){ // se o ultimo for o de trancamento, significa que o contrato não teve retorno e deve continuar trancado
            return;
        }
        if (Calendario.maior(ultimo.getDataInicioSituacao(), contrato.getVigenciaAteAjustada())) {
            getFacade().getHistoricoContrato().excluir(ultimo);
            ajustarUltimoHistorico(contrato);
        } else {
            ultimo.setDataFinalSituacao(contrato.getVigenciaAteAjustada());
            getFacade().getHistoricoContrato().alterarSemCommit(ultimo, false);
        }
    }
    
    
    private void ajustarHistorico(ContratoOperacaoVO obj, ContratoVO contrato, ResultSet result, String dataInicialLb,
            String dataFinalLb, String tipo, String entidade) throws Exception {
        int cont = 0;
        String tipoHistorico = "";
        String operacao = obj.getTipoOperacao();
        if (operacao.equals("CR")) {
            tipoHistorico = "RC";
        } else if (operacao.equals("AT")) {
            tipoHistorico = "RA";
        }
        Date dataFinal = contrato.getVigenciaAteAjustada();
        while (result.next()) {
            String tpEnt = result.getString(tipo);
            if ((tpEnt.equals(operacao) || tpEnt.equals(tipoHistorico)) && cont < 2) {
                executarConsulta("DELETE FROM " + entidade + " WHERE codigo = " + result.getInt("codigo"), con);
                cont++;
            } else {
                if (!(tpEnt.equals("CR") || tpEnt.equals("AT") || tpEnt.equals("TR") || tpEnt.equals("BO") || tpEnt.equals("AV"))) {
                    executarConsulta("UPDATE " + entidade + " SET " + dataFinalLb + " = '" + Uteis.getDataJDBC(dataFinal)
                            + "' where codigo = " + result.getInt("codigo"), con);
                    break;
                } else {
                    executarConsulta("DELETE FROM " + entidade + " where codigo = " + result.getInt("codigo"), con);
                }
            }
        }

    }

        private void ajustarPeriodoAcesso(ContratoOperacaoVO obj, ContratoVO contrato, ResultSet result, String dataInicialLb,
			String dataFinalLb, String tipo, String entidade) throws Exception {
		String operacao = obj.getTipoOperacao();
		Date dataFinal = contrato.getVigenciaAteAjustada();
                Boolean deletar = true; // deleta os periodos de acesso até o periodo de acesso referente a operação
		while (result.next()) {
			String tpEnt = result.getString(tipo);
                        if (deletar && !tpEnt.equals("CA")){
                            executarConsulta("DELETE FROM " + entidade + " WHERE codigo = " + result.getInt("codigo"), con);
                            if (tpEnt.equals(operacao)) {
				deletar = false;
                            }
                        } else if (deletar && tpEnt.equals("CA")){ // Isso é necessário operações de contratos importadas, já que elas não alteram o periodo de acesso
                            executarConsulta("UPDATE " + entidade + " SET " + dataFinalLb + " = '" + Uteis.getDataJDBC(dataFinal)
							+ "' where codigo = " + result.getInt("codigo"), con);
			} else {
				if (!(tpEnt.equals("CR") || tpEnt.equals("AT") || tpEnt.equals("TR"))) {
					executarConsulta("UPDATE " + entidade + " SET " + dataFinalLb + " = '" + Uteis.getDataJDBC(dataFinal)
							+ "' where codigo = " + result.getInt("codigo"), con);

				} else {
                                    PeriodoAcessoClienteVO novoPeriodoAcesso = new PeriodoAcessoClienteVO();
                                    novoPeriodoAcesso.setContrato(result.getInt("contrato"));
                                    novoPeriodoAcesso.setContratoBaseadoRenovacao(result.getInt("contratobaseadorenovacao"));

                                    novoPeriodoAcesso.setDataFinalAcesso(dataFinal);
                                    novoPeriodoAcesso.setDataInicioAcesso(Uteis.somarDias(result.getDate(dataFinalLb), 1));
                                    if (tpEnt.equals("AT")){
                                        novoPeriodoAcesso.setTipoAcesso("RA");
                                    } else if (tpEnt.equals("CR")){
                                        novoPeriodoAcesso.setTipoAcesso("RC");
                                    } else {
                                        novoPeriodoAcesso.setTipoAcesso("RT");
                                    }
                                    novoPeriodoAcesso.setPessoa(result.getInt("pessoa"));
                                    getFacade().getPeriodoAcessoCliente().incluirSemCommit(novoPeriodoAcesso);
                                }
                                break;
			}
		}

	}



    private PreparedStatement getPS(Integer empresa, List<ColaboradorVO> colaboradores, Date inicio, Date fim) throws Exception {

        boolean filtrarColaborador = false;
        String codColaboradores = "";
        for (ColaboradorVO colaborador : colaboradores) {
            if (colaborador.getColaboradorEscolhidoOperacoes()) {
                codColaboradores = codColaboradores + "," + colaborador.getCodigo();
                filtrarColaborador = true;
            }

        }

        StringBuilder sql = new StringBuilder("SELECT\n" +
                "  cli.codigo,\n" +
                "  cli.matricula,\n" +
                "  p.nome,\n" +
                "  c.codigo      AS contrato,\n" +
                "  jus.descricao AS justificativa,\n" +
                "  u.nome        AS responsavel,\n" +
                "  co.dataoperacao AS datacancelamento\n" +
                "FROM contratooperacao co\n" +
                "  INNER JOIN usuario u ON u.codigo = co.responsavel\n" +
                "  INNER JOIN contrato c ON c.codigo = co.contrato\n" +
                "  INNER JOIN pessoa p ON p.codigo = c.pessoa\n" +
                "  INNER JOIN cliente cli ON cli.pessoa = p.codigo\n" +
                "  LEFT JOIN justificativaoperacao jus ON jus.codigo = co.tipojustificativa\n" +
                "WHERE co.tipooperacao LIKE 'CA' AND dataoperacao BETWEEN '" + Uteis.getDataJDBC(inicio) + " 00:00:00' AND '" + Uteis.getDataJDBC(fim) + " 23:59:59'\n" +
                " AND c.empresa = " + empresa + " ");
        if (filtrarColaborador) {
            sql.append("AND u.colaborador IN (" + codColaboradores.replaceFirst(",", "") + ")");
        }

        return con.prepareStatement(sql.toString());
    }

    public String consultarClientesCanceladosJSON(Integer empresa, List<ColaboradorVO> colaboradores, Date inicio, Date fim) throws Exception {
        StringBuilder json;
        boolean dados;
        try (ResultSet rs = getPS(empresa, colaboradores, inicio, fim).executeQuery()) {

            json = new StringBuilder();
            json.append("{\"aaData\":[");
            dados = false;
            while (rs.next()) {
                dados = true;
                json.append("[\"").append(rs.getString("codigo")).append("\",");
                json.append("\"").append(rs.getString("matricula")).append("\",");
                json.append("\"").append(Uteis.normalizarStringJSON(rs.getString("nome"))).append("\",");
                json.append("\"").append(rs.getString("contrato")).append("\",");
                json.append("\"").append(Uteis.normalizarStringJSON(rs.getString("justificativa"))).append("\",");
                json.append("\"").append(Uteis.normalizarStringJSON(rs.getString("responsavel"))).append("\",");
                json.append("\"").append(Uteis.getData(rs.getDate("datacancelamento"))).append("\"],");
            }
        }

        if(dados) {
            json.deleteCharAt(json.toString().length() - 1);
        }
        json.append("]}");
        return json.toString();
    }

    public List consultarParaImpressaoContratosCancelados(Integer empresa, List<ColaboradorVO> colaboradores, Date inicio, Date fim, String filtro, String ordem, String campoOrdenacao) throws Exception {
        List lista;
        try (ResultSet rs = getPS(empresa, colaboradores, inicio, fim).executeQuery()) {
            lista = new ArrayList();
            while (rs.next()) {
                ContratoOperacaoVO pas = new ContratoOperacaoVO();
                String geral = rs.getString("matricula") + rs.getString("nome") + rs.getInt("contrato") + rs.getString("justificativa") + rs.getString("responsavel") + Uteis.getData(rs.getDate("datacancelamento"));
                if (geral.toLowerCase().contains(filtro.toLowerCase())) {
                    pas.setMatricula(rs.getString("matricula"));
                    pas.setNome(rs.getString("nome"));
                    pas.setContrato(rs.getInt("contrato"));
                    pas.setJustificativa(rs.getString("justificativa"));
                    pas.setColaboradorResp(rs.getString("responsavel"));
                    pas.setDia(rs.getDate("datacancelamento"));
                    lista.add(pas);
                }
            }
        }
        if (campoOrdenacao.equals("Matrícula")) {
            Ordenacao.ordenarLista(lista, "matricula");
        } else if (campoOrdenacao.equals("Nome")) {
            Ordenacao.ordenarLista(lista, "nome");
        } else if (campoOrdenacao.equals("Contrato")) {
            Ordenacao.ordenarLista(lista, "contrato");
        } else if (campoOrdenacao.equals("Justificativa")) {
            Ordenacao.ordenarLista(lista, "justificativa");
        } else if (campoOrdenacao.equals("Responsável")) {
            Ordenacao.ordenarLista(lista, "responsavel");
        } else if (campoOrdenacao.equals("Dia")) {
            Ordenacao.ordenarLista(lista, "dia");
        }

        if (ordem.contains("desc")) {
            Collections.reverse(lista);
        }
        return lista;

    }
    
        /**
     * Conta as operações de contrato retroativas considerando a data de
     * lançamento da operação para o intervalo de datas
     *
     * @return
     */
    public int contarOperacoesContratoRetroativas(int empresa, Date inicio, Date fim, List<ColaboradorVO> lista) throws Exception {
        int qtde = 0;
        consultar(getIdEntidade(), false);
        String sqlStr = "SELECT COUNT(distinct  con.codigo) as qtd"
                + " FROM Cliente"
                + " inner join contrato  on contrato.pessoa = cliente.pessoa"
                + " inner join contratooperacao con on con.contrato = contrato.codigo X"
                + " where dataoperacao between '" + Uteis.getDataJDBC(inicio) + "' and '" + Uteis.getDataJDBC(fim) + "'"
                + " and dataoperacao > datainicioefetivacaooperacao";

        if(empresa != 0 ){
            sqlStr += " and cliente.empresa  = " + empresa;
        }

        // filtra a consulta pelos colaboradores
        for (ColaboradorVO co : lista) {
            if (co.getColaboradorEscolhidoOperacoes()) {
                if (qtde == 0) {
                    sqlStr = sqlStr.replaceAll("X", "INNER JOIN vinculo ON vinculo.cliente = cliente.codigo ");
                    qtde++;
                    sqlStr += " AND (";
                } else {
                    sqlStr += " OR ";
                }
                sqlStr += "vinculo.colaborador = " + co.getCodigo().intValue();
            }
        }
        sqlStr = sqlStr.replaceAll("X", "");
        sqlStr += (qtde > 0 ? ")" : "");

        try (Statement stm = con.createStatement()) {
            try (ResultSet ret = stm.executeQuery(sqlStr)) {
                ret.next();
                return ret.getInt("qtd");
            }
        }
    }

    public ContratoOperacaoVO consultarOperacaoBonusRenovacaoAntecipadaCreditoTreino(ContratoVO contratoVO, int nivelMontarDados)throws Exception{
        JustificativaOperacaoVO justificativaOperacaoVO = getFacade().getJustificativaOperacao().consultarOperacaoRenovacaoAntecipadaCreditoTreino(contratoVO.getEmpresa().getCodigo());
        StringBuilder sql = new StringBuilder();
        sql.append(" select * from contratoOperacao where contrato = ").append(contratoVO.getCodigo()).append(" and tipoJustificativa = ").append(justificativaOperacaoVO.getCodigo());
        try (Statement stm = con.createStatement()) {
            try (ResultSet rs = stm.executeQuery(sql.toString())) {
                if (rs.next()) {
                    return montarDados(rs, nivelMontarDados, con);
                }
            }
        }
        return null;
    }

    public ContratoOperacaoVO consultarPorTipoOperacaoCodigoContrato(Integer contrato,String tipoOperacao, boolean controlarAcesso, int nivelMontarDados) throws Exception {
        consultar(getIdEntidade(), controlarAcesso);
        String sqlStr = "SELECT * FROM ContratoOperacao WHERE upper( tipoOperacao ) = '" + tipoOperacao.toUpperCase() + "' and contrato = " + contrato + " ORDER BY tipoOperacao";
        try (Statement stm = con.createStatement()) {
            try (ResultSet tabelaResultado = stm.executeQuery(sqlStr)) {
                while (tabelaResultado.next()) {
                    return (montarDados(tabelaResultado, nivelMontarDados, this.con));
                }
            }
        }
        return null;
    }

    /**
     * Consulta as operações de contrato retroativas considerando a data de
     * lançamento da operação para o intervalo de datas
     *
     * @return
     */
    public List<PendenciaResumoPessoaRelVO> consultarOperacoesContratoRetroativas(int empresa, Date inicio, Date fim, List<ColaboradorVO> lista) throws Exception {
        int qtde = 0;
        consultar(getIdEntidade(), false);
        String sqlStr = "SELECT DISTINCT cliente.*,con.* as qtd,jo.descricao "
                + " FROM Cliente"
                + " inner join contrato  on contrato.pessoa = cliente.pessoa"
                + " inner join contratooperacao con on con.contrato = contrato.codigo X "
                + " left join justificativaoperacao jo on con.tipojustificativa = jo.codigo "
                + " where dataoperacao between '" + Uteis.getDataJDBC(inicio) + "' and '" + Uteis.getDataJDBC(fim) + "'"
                + " and dataoperacao > datainicioefetivacaooperacao"
                + " and cliente.empresa  = " + empresa;
        // filtra a consulta pelos colaboradores
        for (ColaboradorVO co : lista) {
            if (co.getColaboradorEscolhidoOperacoes()) {
                if (qtde == 0) {
                    sqlStr = sqlStr.replaceAll("X", "INNER JOIN vinculo ON vinculo.cliente = cliente.codigo ");
                    qtde++;
                    sqlStr += " AND (";
                } else {
                    sqlStr += " OR ";
                }
                sqlStr += "vinculo.colaborador = " + co.getCodigo().intValue();
            }
        }
        sqlStr = sqlStr.replaceAll("X", "");
        sqlStr += (qtde > 0 ? ")" : "");
        sqlStr += " order by con.dataoperacao ";

        List<PendenciaResumoPessoaRelVO> listaPendenciaResumoPessoaRelVOs;
        try (ResultSet resultSet = ContratoOperacao.criarConsulta(sqlStr, con)) {
            listaPendenciaResumoPessoaRelVOs = new ArrayList<PendenciaResumoPessoaRelVO>();
            while (resultSet.next()) {
                PendenciaResumoPessoaRelVO pendenciaResumoPessoaRelVO = new PendenciaResumoPessoaRelVO();
                pendenciaResumoPessoaRelVO.setClienteVO(Cliente.montarDados(resultSet, Uteis.NIVELMONTARDADOS_DADOSBASICOS, con));
                pendenciaResumoPessoaRelVO.setContratoOperacaoVO(montarDados(resultSet, Uteis.NIVELMONTARDADOS_TODOS, con));
                pendenciaResumoPessoaRelVO.setColaboradorVO(pendenciaResumoPessoaRelVO.getContratoOperacaoVO().getResponsavel().getColaboradorVO());
                pendenciaResumoPessoaRelVO.setDataRegistro(pendenciaResumoPessoaRelVO.getContratoOperacaoVO().getDataOperacao());
                pendenciaResumoPessoaRelVO.getContratoOperacaoVO().setJustificativaApresentar(resultSet.getString("descricao"));
                listaPendenciaResumoPessoaRelVOs.add(pendenciaResumoPessoaRelVO);
            }
        }
        return listaPendenciaResumoPessoaRelVOs;
        
    }

    public static void alterarMatriculasSemOcupacaoAoRetronarTrancamento(TrancamentoContratoVO trancamentoContratoVO) throws Exception {
        ContratoOperacao.alterarMatriculasSemOcupacaoTrancamento(trancamentoContratoVO.getContratoVO(),
                Uteis.obterDataAnterior(trancamentoContratoVO.getDataTrancamento(), 1),
                trancamentoContratoVO.getDataFimTrancamento());
    }

    public static void alterarMatriculasSemOcupacaoAoTrancar(TrancamentoContratoVO trancamentoContratoVO) throws Exception {
        alterarMatriculasSemOcupacaoTrancamento(trancamentoContratoVO.getContratoVO(), (trancamentoContratoVO.getDataPrimeiroTrancamentoSeq() == null ? trancamentoContratoVO.getDataTrancamento() : trancamentoContratoVO.getDataPrimeiroTrancamentoSeq()), trancamentoContratoVO.obtenhaDataFinalAposTrancamento());
    }
    
    @Override
    public void lancarBonusReducaoCreditoTreino(ContratoVO contrato, Integer nrDias,
            UsuarioVO usuario, JustificativaOperacaoVO justificativaOperacaoVO, String observacao,Date dataInicio, Date dataTermino, boolean renovacaoAntecipada) throws Exception {
        BonusContratoVO bonusRetirarDias = new BonusContratoVO();
        bonusRetirarDias.setContratoVO(contrato);
        bonusRetirarDias.setEmpresa(contrato.getEmpresa().getCodigo());
        bonusRetirarDias.setDataInicio(dataInicio);
        bonusRetirarDias.setDataTermino(dataTermino);
        bonusRetirarDias.setTipoJustificativa(justificativaOperacaoVO.getCodigo());
        bonusRetirarDias.setNrDias(nrDias);
        bonusRetirarDias.setResponsavelOperacao(usuario);
        bonusRetirarDias.setApresentarPeriodoBonus(true);
        bonusRetirarDias.setAcrescentarDiaContrato("RE");
        bonusRetirarDias.setBonusRetiraDiasParaRenovarAntecipado(renovacaoAntecipada);
        bonusRetirarDias.setObservacao(observacao);
        bonusRetirarDias.setNovoObj(true);
        incluirOperacaoBonus(bonusRetirarDias, true, contrato.getVigenciaAte(), false, null);
    }

    @Override
    public boolean existeOperacaoPendenteDeRetorno(Integer contrato, Date dataVerificar) throws Exception {
        String sql = "select count(codigo) > 0 as existeRetornoPendente from contratooperacao where contrato =? " +
                     " and tipooperacao in ('AT', 'TR', 'CR') and datafimefetivacaooperacao >= ?";

        try (PreparedStatement query = con.prepareStatement(sql)) {
            query.setInt(1, contrato);
            query.setTimestamp(2, Uteis.getDataJDBCTimestamp(dataVerificar));
            try (ResultSet rs = query.executeQuery()) {
                return rs.next() && rs.getBoolean("existeRetornoPendente");
            }
        }
    }

    public void atualizaDataFimDeProdutoComVigenciaDeContrato(Integer codigoContrato,Date vigenciaDe, Date vigenciaAjustada) throws Exception {
        MovProdutoVO movProduto;
        ProdutoVO produto;
        MovProduto movProdutoDAO = new MovProduto(con);
        Produto produtoDAO = new Produto(con);
        List produtos = movProdutoDAO.consultarPorCodigoContrato(codigoContrato, Uteis.NIVELMONTARDADOS_DADOSBASICOS);

        for (Object obj: produtos) {
            movProduto = (MovProdutoVO) obj;
            produto = produtoDAO.consultarPorChavePrimaria(movProduto.getProduto().getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS);
            if (produto.isPrevalecerVigenciaContrato()) {
                if(vigenciaDe != null){
                    movProduto.setDataInicioVigencia(vigenciaDe);
                }
                movProduto.setDataFinalVigencia(vigenciaAjustada);
                movProdutoDAO.alterarSemCommit(movProduto);
            }
        }
    }
    
     @Override
    public boolean existeOperacaoLancadaFuturaAEstaData(int codigoContrato,
            String tipos, Date dataAtual) throws Exception {

        StringBuilder sb = new StringBuilder();
        sb.append("select exists (select contrato from contratoOperacao op ");
        sb.append(" where op.contrato = ").append(codigoContrato);
        sb.append(" AND op.tipoOperacao in (").append(tipos).append(")");
        sb.append(" AND op.dataoperacao > '").append(Uteis.getDataJDBCTimestamp(dataAtual)).append("' ) ");
        sb.append(" as existe");


         try (PreparedStatement stm = con.prepareStatement(sb.toString())) {
             try (ResultSet tabelaResultado = stm.executeQuery()) {
                 tabelaResultado.next();
                 return tabelaResultado.getBoolean("existe");
             }
         }
     }

    @Override
    public void retirarVinculoTWCancelamento(Integer pessoa, boolean removerVinculo) throws Exception {
        //Caso o parametro remover apos desistencia estiver marcado ele ira remover os vinculos do Professor Treino Web com Aluno
        Cliente clienteDAO = new Cliente(con);
        ClienteVO cliente = clienteDAO.consultarPorCodigoPessoa(pessoa, Uteis.NIVELMONTARDADOS_MINIMOS);
;        if (removerVinculo) {
            Vinculo vinculoDAO = new Vinculo(con);
            List<VinculoVO> listaVinculos = vinculoDAO.consultarPorCodigoCliente(cliente.getCodigo(), Uteis.NIVELMONTARDADOS_MINIMOS, false);
            for (VinculoVO vinculoVO : listaVinculos) {
                if (vinculoVO.getTipoVinculo().equals("TW")) {
                    vinculoDAO.excluir(vinculoVO, "CANCELAMENTO - CONTRATO", false);
                }
            }
        }
    }

    private void deletarRegistroTrancamentoContrato(Integer codigoContrato) throws  Exception{
        TrancamentoContrato trancamentoContratoDao = new TrancamentoContrato(con);
        TrancamentoContratoVO trancamentoContratoVO = trancamentoContratoDao.obterUltimoTrancamento(codigoContrato, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
        trancamentoContratoDao.excluirSemCommit(trancamentoContratoVO);
        trancamentoContratoDao = null;
    }

    public void alterarSomenteChaveArquivo(AtestadoContratoVO obj) throws SQLException {
        String sql = "UPDATE ContratoOperacao SET chavearquivo = ? WHERE codigo = ?";
        try (PreparedStatement sqlAlterar = con.prepareStatement(sql)) {
            int i = 0;
            sqlAlterar.setString(++i, obj.getChaveArquivo());
            sqlAlterar.setInt(++i, obj.getContratoOperacaoVO().getCodigo());
            sqlAlterar.execute();
        }
    }

    @Override
    public List<PendenciaResumoPessoaRelVO> obterClientesComGymPassPorPeriodo(Date dataInicial, Date dataFinal, EmpresaVO empresa) throws Exception {
        
        StringBuilder sql = obterClientesComGymPassSql(dataInicial,dataFinal, empresa.getCodigo(),
                " distinct (cli.*), pa.datainicioacesso, us.nome as responsavel, pa.tokengympass, " +
                       " pa.tipoGymPass, pa.valorgympass ");
        List<PendenciaResumoPessoaRelVO> listaPendenciaResumoPessoaRelVOs;
        try (ResultSet resultSet = ContratoOperacao.criarConsulta(sql.toString(), con)) {
            listaPendenciaResumoPessoaRelVOs = new ArrayList<PendenciaResumoPessoaRelVO>();
            while (resultSet.next()) {
                PendenciaResumoPessoaRelVO pendenciaResumoPessoaRelVO = new PendenciaResumoPessoaRelVO();
                pendenciaResumoPessoaRelVO.setClienteVO(Cliente.montarDados(resultSet, Uteis.NIVELMONTARDADOS_DADOSBASICOS, con));
                pendenciaResumoPessoaRelVO.getColaboradorVO().getPessoa().setNome(resultSet.getString("responsavel"));
                pendenciaResumoPessoaRelVO.setDataRegistro(resultSet.getDate("datainicioacesso"));
                pendenciaResumoPessoaRelVO.setToken(resultSet.getString("tokengympass"));
                pendenciaResumoPessoaRelVO.setTipoGymPass(resultSet.getString("tipoGymPass"));
                pendenciaResumoPessoaRelVO.setValorEmAberto(resultSet.getDouble("valorgympass"));
                pendenciaResumoPessoaRelVO.setMoeda(empresa.getMoeda());
                listaPendenciaResumoPessoaRelVOs.add(pendenciaResumoPessoaRelVO);
            }
        }
        return listaPendenciaResumoPessoaRelVOs;
    }

    @Override
    public DadosContratoOperacaoWS obterDadosOperacaoContrato(ContratoVO contrato, String tipoOperacao) throws Exception{
        DadosContratoOperacaoWS dados = new DadosContratoOperacaoWS();
        dados.setContrato(contrato.getCodigo());
        dados.setTipoOperacao(tipoOperacao);
        ZillyonWebFacade zw = new ZillyonWebFacade(con);
        Integer diasContrato = zw.obterNrDiasContrato(contrato);
        Date dataAnterior = Uteis.obterDataAnterior(Calendario.hoje(), 1);
        Integer diasUtilizados = zw.obterNrDiasUtilizadoAcademiaAteDataEspecifica(contrato, dataAnterior);
        Integer diasRestantes = 0;

        if(contrato.isVendaCreditoTreino()){
            diasRestantes = ((Long) Uteis.nrDiasEntreDatas(Calendario.hoje(),contrato.getVigenciaAteAjustada())).intValue() + 1;
        } else {
            diasRestantes = contrato.obterNrDiasRestantesProFinalDoContrato(diasContrato, diasUtilizados);
        }
        dados.setQtdRestanteContrato(diasRestantes);
        JustificativaOperacao justificativasDAO = new JustificativaOperacao(con);
        List<JustificativaOperacaoVO> justificativas= justificativasDAO.consultarPorTipoOperacao(tipoOperacao, contrato.getEmpresa().getCodigo(), false, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
        justificativasDAO = null;
        for (JustificativaOperacaoVO justificativa : justificativas){
            JustificativaOperacaoWS justificativaWS = new JustificativaOperacaoWS(justificativa.getCodigo(), justificativa.getDescricao().trim());
            dados.getJustificativas().add(justificativaWS);
        }
        if(tipoOperacao.equals(TipoOperacaoContratoEnum.TRANCAMENTO.getSigla())){
            Produto produtoDao = new Produto(con);
            List<ProdutoVO> listaProdtutos = produtoDao.consultarPorDescricaoTipoProdutoAtivo("", TipoOperacaoContratoEnum.TRANCAMENTO.getSigla(), false, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
            produtoDao = null;
            for (ProdutoVO produto : listaProdtutos){
                dados.getProdutos().add(produto.toWS());
            }
        }
        if(tipoOperacao.equals(TipoOperacaoContratoEnum.CARENCIA.getSigla())){
            dados.setQtdMinimaCarencia(obterCarenciaMinima(contrato.getEmpresa().getCodigo()));
            try (ResultSet rs = criarConsulta("select carencia from contratoduracao where contrato =" + contrato.getCodigo(), con)) {
                if (rs.next()) {
                    dados.setQtdCarenciaPermitida(rs.getInt("carencia"));
                } else {
                    dados.setQtdCarenciaPermitida(0);
                }
            }
            Integer nrDiasUsados = 0;
            List<ContratoOperacaoVO> listaOperacoes = consultarPorContrato(contrato.getCodigo(), false, Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA);
            for (ContratoOperacaoVO op : listaOperacoes) {
                if (op.getCarenciaFerias()) {
                    nrDiasUsados += op.obterNrDiasContratoOperacao();
                }
            }
            dados.setQtdCarenciaUtilizado(nrDiasUsados);
        }
        return dados;
    }

    public DadosRetornoOperacaoWS validarDadosOperacaoContrato(ContratoVO contrato, final String tipoOperacao, Date inicio, Date fim, final Integer produto, final Integer justificativa) throws Exception {
        DadosRetornoOperacaoWS retorno = new DadosRetornoOperacaoWS();
        if(Calendario.maior(inicio, fim)){
            throw new Exception("Data de início deve ser menor ou igual a data final informada");
        }
        retorno.setContrato(contrato.getCodigo());
        retorno.setTipoOperacao(tipoOperacao);
        List<ContratoOperacaoVO> listaOperacoes = consultarPorContrato(contrato.getCodigo(), false, Uteis.NIVELMONTARDADOS_ROBO);
        if (tipoOperacao.equals(TipoOperacaoContratoEnum.CARENCIA.getSigla())) {
            CarenciaContratoVO carenciaContratoVO = new CarenciaContratoVO();
            carenciaContratoVO.setContratoVO(contrato);
            carenciaContratoVO.setDataInicio(inicio);
            carenciaContratoVO.setDataTermino(fim);
            carenciaContratoVO.setTipoJustificativa(justificativa);
            retorno.setDiasOperacao(validarDadosCarencia(carenciaContratoVO, listaOperacoes));
            retorno.setDataInicioRetorno(carenciaContratoVO.getDataInicioRetorno_Apresentar());
            retorno.setDataFinalRetorno(carenciaContratoVO.getDataTerminoRetorno_Apresentar());
            retorno.setDiasContrato(carenciaContratoVO.getNrDiasContrato());
            retorno.setDiasRestantes(carenciaContratoVO.getNrDiasRestanteContrato());
            retorno.setDiasUtilizados(carenciaContratoVO.getNrDiasUsadoContrato());
        } else {
            TrancamentoContratoVO trancamentoContratoVO = new TrancamentoContratoVO();
            trancamentoContratoVO.setContratoVO(contrato);
            trancamentoContratoVO.setDataTrancamento(inicio);
            trancamentoContratoVO.setTipoJustificativa(justificativa);
            validarDadosTrancamento(trancamentoContratoVO, produto, listaOperacoes);
            retorno.setDataInicioRetorno(trancamentoContratoVO.getDataRetorno_Apresentar());
            retorno.setDiasContrato(trancamentoContratoVO.getNrDiasContrato());
            retorno.setDiasUtilizados(trancamentoContratoVO.getNrDiasUtilizadosPeloClienteContrato());
            retorno.setDiasRestantes(trancamentoContratoVO.getNrDiasCongelado());
            retorno.setDiasCongelados(trancamentoContratoVO.getNrDiasCongelado());
            retorno.setDiasOperacao(trancamentoContratoVO.getProdutoTrancamento().getNrDiasVigencia());
            retorno.setValorOperacao(trancamentoContratoVO.getValorTrancamento());
        }
        return retorno;
    }

    private Integer validarDadosCarencia(CarenciaContratoVO carenciaContratoVO, List<ContratoOperacaoVO> listaOperacoes) throws Exception {
        ZillyonWebFacade zw = new ZillyonWebFacade(con);
        try (ResultSet rs = criarConsulta("select carencia from contratoduracao where contrato =" + carenciaContratoVO.getContratoVO().getCodigo(), con)) {
            if (rs.next()) {
                carenciaContratoVO.getContratoVO().getContratoDuracao().setCarencia(rs.getInt("carencia"));
            } else {
                carenciaContratoVO.getContratoVO().getContratoDuracao().setCarencia(0);
            }
        }
        Integer diasOperacao = 0;
        carenciaContratoVO.setNrDiasContrato(zw.obterNrDiasContrato(carenciaContratoVO.getContratoVO()));

        Date dataAnterior = Uteis.obterDataAnterior(carenciaContratoVO.getDataInicio(), 1);
        carenciaContratoVO.setNrDiasUsadoContrato(zw.obterNrDiasUtilizadoAcademiaAteDataEspecifica(carenciaContratoVO.getContratoVO(), dataAnterior));
        carenciaContratoVO.setNrDiasRestanteContrato(carenciaContratoVO.getContratoVO().obterNrDiasRestantesProFinalDoContrato(carenciaContratoVO.getNrDiasContrato(), carenciaContratoVO.getNrDiasUsadoContrato()));
        if (carenciaContratoVO.getNrDiasRestanteContrato() <= 0) {
            throw new Exception(String.format("Não é possível lançar férias nesse período, pois o contrato do cliente está vigente por meio de bônus e o número de dias utilizados"
                            + " (%s) é maior ou igual ao número de dias do contrato (%s).",
                    new Object[]{carenciaContratoVO.getNrDiasUsadoContrato(), carenciaContratoVO.getNrDiasContrato()}));
        }
        Integer qtdMinima = obterCarenciaMinima(carenciaContratoVO.getContratoVO().getEmpresa().getCodigo());
        Long diferenca = Uteis.nrDiasEntreDatas(carenciaContratoVO.getDataInicio(), carenciaContratoVO.getDataTermino());
        diasOperacao = diferenca.intValue() + 1;
        carenciaContratoVO.setPeriodoCarencia(diasOperacao.longValue());
        if (diasOperacao < qtdMinima) {
            throw new Exception("O Período de Férias Deve Ser Maior do que a Quantidade Mínima de Férias");
        }
        carenciaContratoVO.setDataInicioRetorno(Uteis.obterDataFutura2(carenciaContratoVO.getDataTermino(), 1));

        //diasRestante pode ser negativo, portanto utilizar os dias da Carência e não o restante do contrato
        if (Uteis.getCompareData(carenciaContratoVO.getDataTermino(), carenciaContratoVO.getContratoVO().getVigenciaAteAjustada()) > 0) {
            carenciaContratoVO.setQtdDiasCarenciaMaiorQueContrato(true);
            carenciaContratoVO.setDataTerminoRetorno(Uteis.obterDataFutura2(carenciaContratoVO.getDataTermino(), carenciaContratoVO.getNrDiasRestanteContrato().intValue()));
            carenciaContratoVO.setNrDias(carenciaContratoVO.getNrDiasRestanteContrato().intValue());
        } else {
            carenciaContratoVO.setQtdDiasCarenciaMaiorQueContrato(false);
            if (carenciaContratoVO.getNrDiasRestanteContrato().intValue() < diasOperacao.intValue()) { // contrato em periodo de bonus
                carenciaContratoVO.setDataTerminoRetorno(Uteis.obterDataFutura2(carenciaContratoVO.getContratoVO().getVigenciaAteAjustada(), carenciaContratoVO.getNrDiasRestanteContrato().intValue()));
                carenciaContratoVO.setNrDias(carenciaContratoVO.getNrDiasRestanteContrato().intValue());
            } else {
                carenciaContratoVO.setDataTerminoRetorno(Uteis.obterDataFutura2(carenciaContratoVO.getContratoVO().getVigenciaAteAjustada(), diasOperacao.intValue()));
                carenciaContratoVO.setNrDias(diasOperacao.intValue());
            }
        }
        ValidacaoHistoricoContrato.validarTrancamentoRetornoPendente(carenciaContratoVO.getContratoVO().getCodigo(), carenciaContratoVO.getDataInicio(), con);
        carenciaContratoVO.validarPeriodoCarencia(listaOperacoes);
        zw = null;
        return diasOperacao;
    }

    private void validarDadosTrancamento(TrancamentoContratoVO trancamentoContratoVO, Integer produto, List<ContratoOperacaoVO> listaOperacoes) throws Exception {
        ZillyonWebFacade zw = new ZillyonWebFacade(con);
        Produto proDAO = new Produto(con);
        try{
            if(!UteisValidacao.emptyNumber(trancamentoContratoVO.getContratoVO().getContratoResponsavelRenovacaoMatricula())){
                throw new ConsistirException("Trancamento não pode ser feito, pois esse contrato já foi renovado");
            }
            trancamentoContratoVO.setNrDiasContrato(zw.obterNrDiasContrato(trancamentoContratoVO.getContratoVO()));
            if (!trancamentoContratoVO.getContratoVO().isVendaCreditoTreino()) {
                trancamentoContratoVO.setNrDiasBonus(zw.obterNrDiasOperacoesBonusNoContratoParaDias(trancamentoContratoVO.getContratoVO(), trancamentoContratoVO.getDataTrancamento()));
            }
            trancamentoContratoVO.setNrDiasUtilizadosPeloClienteContrato(zw.obterNrDiasUtilizadoAcademiaAteDataEspecifica(trancamentoContratoVO.getContratoVO(), Uteis.obterDataAnterior(trancamentoContratoVO.getDataTrancamento(), 1)));
            trancamentoContratoVO.setValorBaseContrato(trancamentoContratoVO.getContratoVO().getValorBaseCalculo());
            trancamentoContratoVO.setValorDiaContratoValorBase(zw.obterValorDiaContratoValorBase(trancamentoContratoVO.getContratoVO()));
            trancamentoContratoVO.setNrDiasCongelado(trancamentoContratoVO.getContratoVO().obterNrDiasRestantesProFinalDoContrato(trancamentoContratoVO.getNrDiasContrato(), trancamentoContratoVO.getNrDiasUtilizadosPeloClienteContrato()));
            if (trancamentoContratoVO.getNrDiasBonus() > 0) {
                trancamentoContratoVO.setNrDiasCongelado(trancamentoContratoVO.getNrDiasCongelado() + trancamentoContratoVO.getNrDiasBonus());
            }
            if (trancamentoContratoVO.getNrDiasUtilizadosPeloClienteContrato() >= trancamentoContratoVO.getNrDiasContrato()) {
                trancamentoContratoVO.setValorUtilizadoPeloClienteBase(Uteis.arredondarForcando2CasasDecimais(trancamentoContratoVO.getContratoVO().getValorBaseCalculo()));
                trancamentoContratoVO.setValorCongelado(0.0);
            } else if (trancamentoContratoVO.getNrDiasUtilizadosPeloClienteContrato() == 0) {
                trancamentoContratoVO.setValorUtilizadoPeloClienteBase(0.0);
                trancamentoContratoVO.setValorCongelado(Uteis.arredondarForcando2CasasDecimais(trancamentoContratoVO.getContratoVO().getValorBaseCalculo()));
            } else {
                trancamentoContratoVO.setValorUtilizadoPeloClienteBase(Uteis.arredondarForcando2CasasDecimais(trancamentoContratoVO.getNrDiasUtilizadosPeloClienteContrato() * trancamentoContratoVO.getValorDiaContratoValorBase()));
                trancamentoContratoVO.setValorCongelado(Uteis.arredondarForcando2CasasDecimais(trancamentoContratoVO.getValorBaseContrato() - trancamentoContratoVO.getValorUtilizadoPeloClienteBase()));
            }
            if (trancamentoContratoVO.getNrDiasCongelado() < 0) {
                throw new ConsistirException("Não é possível realizar esta operação de trancamento na data informada, pois nesse período seu contrato terminou.");
            }
            ValidacaoContratoOperacao.validarPeriodoOperacao("Trancamento", trancamentoContratoVO.getDataTrancamento(), trancamentoContratoVO.getContratoVO(), listaOperacoes);
            ValidacaoHistoricoContrato.validarTrancamentoRetornoPendente(trancamentoContratoVO.getContratoVO().getCodigo(), trancamentoContratoVO.getDataTrancamento(), con);
            ProdutoVO obj = proDAO.consultarPorChavePrimaria(produto, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
            trancamentoContratoVO.setValorTrancamento(obj.getValorFinal());
            trancamentoContratoVO.setProdutoTrancamento(obj);
            trancamentoContratoVO.setDataFimTrancamento(Uteis.obterDataFutura2(trancamentoContratoVO.getDataTrancamento(), (obj.getNrDiasVigencia() - 1)));
            trancamentoContratoVO.setDataRetorno(Uteis.obterDataFutura2(trancamentoContratoVO.getDataTrancamento(), (obj.getNrDiasVigencia())));
        }catch (Exception e ) {
            throw e;
        } finally {
            zw = null;
            proDAO = null;
        }
    }

    private Integer obterCarenciaMinima(Integer codigoEmpresa) throws Exception {
        Empresa empresaDAO = new Empresa(con);
        Integer qtdMinima = empresaDAO.obterCarenciaEmpresa(codigoEmpresa);
        empresaDAO = null;
        if (qtdMinima != null && qtdMinima == 0) {
            ConfiguracaoSistema configDao = new ConfiguracaoSistema(con);
            qtdMinima = configDao.obterCarenciaConfiguracao();
            configDao = null;
        }
        if (qtdMinima == null) {
            qtdMinima = 0;
        }
        return qtdMinima;
    }

    public String gravarDadosOperacaoContrato(ContratoVO contrato, final String tipoOperacao,Date inicio, Date fim, final Integer produto, final Integer justificativa, final String obs, OrigemSistemaEnum origemSistemaEnum) throws Exception{
        List<ContratoOperacaoVO> listaOperacoes = consultarPorContrato(contrato.getCodigo(), false, Uteis.NIVELMONTARDADOS_ROBO);
        Usuario usuarioDao = new Usuario(con);
        Empresa empresaDAO = new Empresa(con);
        contrato.setEmpresa(empresaDAO.consultarPorCodigo(contrato.getEmpresa().getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS));
        UsuarioVO responsavel = usuarioDao.getUsuarioRecorrencia();
        usuarioDao = null;
        empresaDAO = null;
        if (tipoOperacao.equals(TipoOperacaoContratoEnum.CARENCIA.getSigla())) {
            CarenciaContratoVO carenciaContratoVO = new CarenciaContratoVO();
            carenciaContratoVO.setContratoVO(contrato);
            carenciaContratoVO.setDataInicio(inicio);
            carenciaContratoVO.setDataTermino(fim);
            carenciaContratoVO.setTipoJustificativa(justificativa);
            carenciaContratoVO.setObservacao(obs);
            carenciaContratoVO.setResponsavelOperacao(responsavel);
            carenciaContratoVO.setOrigemSistema(origemSistemaEnum);
            validarDadosCarencia(carenciaContratoVO, listaOperacoes);
            incluirOperacaoCarencia(carenciaContratoVO, responsavel);
        } else {
            TrancamentoContratoVO trancamentoContratoVO = new TrancamentoContratoVO();
            trancamentoContratoVO.setContratoVO(contrato);
            trancamentoContratoVO.setDataTrancamento(inicio);
            trancamentoContratoVO.setTipoJustificativa(justificativa);
            trancamentoContratoVO.setObservacao(obs);
            trancamentoContratoVO.setResponsavelOperacao(responsavel);
            trancamentoContratoVO.setOrigemSistema(origemSistemaEnum);
            validarDadosTrancamento(trancamentoContratoVO, produto, listaOperacoes);
            TrancamentoContrato trancamentoDAO = new TrancamentoContrato(con);
            trancamentoDAO.incluir(trancamentoContratoVO, responsavel);
        }
        return "";
    }

    public int obterNumeroContratosTipoBolsa(Date inicio, Date fim, List<ColaboradorVO> colaboradores, Integer codigoEmpresa) throws Exception {
        String projecao = "COUNT(c.codigo) as cont";
        try (ResultSet resultSet = executarConsultaContratosTipoBolsa(fim, colaboradores, codigoEmpresa, projecao)) {
            return resultSet.next() ? resultSet.getInt("cont") : 0;
        }
    }

    private ResultSet executarConsultaContratosTipoBolsa(Date fim, List<ColaboradorVO> colaboradores, Integer codigoEmpresa, String projecao) throws Exception {
        String codColaboradores = "";
        for (ColaboradorVO colaborador : colaboradores) {
            if (colaborador.getColaboradorEscolhidoOperacoes()) {
                codColaboradores = codColaboradores + "," + colaborador.getCodigo();
            }
        }

        StringBuilder sb = new StringBuilder();
        sb.append(" SELECT " + projecao + " FROM contrato c");
        sb.append(" INNER JOIN pessoa pes ON pes.codigo = c.pessoa");
        sb.append(" INNER JOIN cliente cli ON cli.pessoa = pes.codigo");
        sb.append(" INNER JOIN usuario u ON u.codigo = c.responsavelcontrato");
        sb.append(" WHERE c.bolsa");
        sb.append(" AND '" + Uteis.getDataJDBC(fim) + " 00:00:00' BETWEEN c.vigenciade AND c.vigenciaateajustada");
        if(codigoEmpresa != 0){
            sb.append(" AND c.empresa = " + codigoEmpresa + " ");
        }
        if (!codColaboradores.isEmpty()) {
            sb.append("AND c.consultor IN (" + codColaboradores.replaceFirst(",", "") + ")");
        }
        return criarConsulta(sb.toString(), con);
    }

    public List<PendenciaResumoPessoaRelVO> obterAlunosContratoTipoBolsa(Date inicio, Date fim,
                                                                     List<ColaboradorVO> colaboradores, Integer codigoEmpresa) throws Exception {

        String projecao = "cli.*, u.nome as responsavel, c.dataLancamento";
        List<PendenciaResumoPessoaRelVO> listaPendenciaResumoPessoaRelVOs;
        try (ResultSet resultSet = executarConsultaContratosTipoBolsa(fim, colaboradores, codigoEmpresa, projecao)) {
            listaPendenciaResumoPessoaRelVOs = new ArrayList<PendenciaResumoPessoaRelVO>();
            while (resultSet.next()) {
                PendenciaResumoPessoaRelVO pendenciaResumoPessoaRelVO = new PendenciaResumoPessoaRelVO();
                pendenciaResumoPessoaRelVO.setClienteVO(Cliente.montarDados(resultSet, Uteis.NIVELMONTARDADOS_DADOSBASICOS, con));
                pendenciaResumoPessoaRelVO.getColaboradorVO().getPessoa().setNome(resultSet.getString("responsavel"));
                pendenciaResumoPessoaRelVO.setDataRegistro(resultSet.getDate("dataLancamento"));
                listaPendenciaResumoPessoaRelVOs.add(pendenciaResumoPessoaRelVO);
            }
        }
        return listaPendenciaResumoPessoaRelVOs;
    }

    @Override
    public boolean existeOperacaoParaEstaData(int codigoCliente, String tipoOperacao, Date dataBase, int codigoContrato) throws Exception {

        StringBuilder sb = new StringBuilder();
        sb.append("select exists (select contrato from contratoOperacao op inner join contrato c on c.codigo = op.contrato inner join cliente cli on cli.pessoa = c.pessoa ");
        sb.append("where cli.codigo = %1$d\n");
        sb.append("AND op.tipoOperacao = '%2$s'\n");
        sb.append("AND '%3$s' between cast(op.dataInicioEfetivacaoOperacao as date)\n");
        sb.append("AND cast(op.dataFimEfetivacaoOperacao as date)\n");
        sb.append("AND contrato = %4$d)\n");
        sb.append("as existe");


        String sqlStr = String.format(sb.toString(),
                codigoCliente,
                tipoOperacao,
                Uteis.getDataJDBC(dataBase),
                codigoContrato);

        try (PreparedStatement stm = con.prepareStatement(sqlStr)) {
            try (ResultSet tabelaResultado = stm.executeQuery()) {
                tabelaResultado.next();
                return tabelaResultado.getBoolean("existe");
            }
        }
    }

    public ContratoOperacaoVO obterOperacaoParaEstaData(String tipoOperacao, Date dataBase, int codigoContrato) throws Exception {
        StringBuilder sql = new StringBuilder();
        sql.append("SELECT * FROM contratoOperacao op \n");
        sql.append("WHERE tipoOperacao = '").append(tipoOperacao).append("' \n");
        sql.append("AND '").append(dataBase).append("' BETWEEN CAST(dataInicioEfetivacaoOperacao AS date)\n");
        sql.append("AND CAST(dataFimEfetivacaoOperacao AS date)\n");
        sql.append("AND contrato = ").append(codigoContrato);

        try (PreparedStatement stm = con.prepareStatement(sql.toString())) {
            try (ResultSet rs = stm.executeQuery()) {
                if (rs.next()) {
                    return montarDados(rs, Uteis.NIVELMONTARDADOS_DADOSBASICOS, con);
                }
            }
        }
        return null;
    }

    public void processarDatasContrato(ContratoVO contratoVO, List<String> operacoesExcluir) throws Exception {
        ZillyonWebFacade zwDAO = new ZillyonWebFacade(con);
        try {
            con.setAutoCommit(false);
            List<ContratoOperacaoVO> listaOperacoes = consultarOperacoesQueAlteramVencimento(contratoVO.getCodigo());

            Date dataOriginal = contratoVO.getVigenciaAteAjustada();
            contratoVO.setVigenciaAteAjustada(contratoVO.getVigenciaAte());
            for (ContratoOperacaoVO operacaoVO : listaOperacoes) {
                if (!UteisValidacao.emptyList(operacoesExcluir)) {
                    boolean ignorarOperacao = false;
                    for (String tipoOperacao : operacoesExcluir) {
                        if (operacaoVO.getTipoOperacao().equals(tipoOperacao)) {
                            ignorarOperacao = true;
                        }
                    }
                    if (ignorarOperacao) {
                        continue;
                    }
                }
                if (operacaoVO.getTipoOperacao().equals("CA")) {
                    contratoVO.setVigenciaAteAjustada(operacaoVO.getDataFimEfetivacaoOperacao());
                    break;
                }
                if (operacaoVO.getTipoOperacao().equals("TR")) {
                    return;
                }
                if (operacaoVO.getTipoOperacao().equals("BA") || operacaoVO.getTipoOperacao().equals("BC") || operacaoVO.getTipoOperacao().equals("TE")) {
                    contratoVO.setVigenciaAteAjustada(Uteis.somarDias(contratoVO.getVigenciaAteAjustada(), operacaoVO.getNrDiasOperacao()));
                    continue;
                }

                if (operacaoVO.getTipoOperacao().equals("BR") || operacaoVO.getTipoOperacao().equals("BX")) {
                    contratoVO.setVigenciaAteAjustada(Uteis.somarDias(contratoVO.getVigenciaAteAjustada(), operacaoVO.getNrDiasOperacao()));
                    continue;
                }
                Integer diasRestante = 0;
                Integer diasAcrescentar = 0;
                if (contratoVO.isVendaCreditoTreino()) {
                    diasRestante = ((Long) Uteis.nrDiasEntreDatas(operacaoVO.getDataInicioEfetivacaoOperacao(), contratoVO.getVigenciaAteAjustada())).intValue() + 1;
                } else {
                    Integer nrDiasContratos = zwDAO.obterNrDiasContrato(contratoVO);
                    Date dataAnterior = Uteis.obterDataAnterior(operacaoVO.getDataInicioEfetivacaoOperacao(), 1);
                    Integer diasUtilizados = zwDAO.obterNrDiasUtilizadoAcademiaAteDataEspecifica(contratoVO, dataAnterior);
                    diasRestante = contratoVO.obterNrDiasRestantesProFinalDoContrato(nrDiasContratos, diasUtilizados);

                }
                Integer diasOperacao = ((Long) Uteis.nrDiasEntreDatas(operacaoVO.getDataInicioEfetivacaoOperacao(), operacaoVO.getDataFimEfetivacaoOperacao())).intValue() + 1;

                if (diasOperacao > diasRestante) {
                    diasAcrescentar = diasRestante;
                } else {
                    diasAcrescentar = diasOperacao;
                }

                contratoVO.setVigenciaAteAjustada(Uteis.somarDias(contratoVO.getVigenciaAteAjustada(), diasAcrescentar));
            }
            if (Calendario.igual(dataOriginal, contratoVO.getVigenciaAteAjustada())) {
                return;
            }
            SuperFacadeJDBC.executarUpdate("update contrato set dataprevistarenovar = '" + Uteis.getDataJDBC(contratoVO.getVigenciaAteAjustada()) + "', vigenciaateajustada = '" + Uteis.getDataJDBC(contratoVO.getVigenciaAteAjustada()) + "' where codigo = " + contratoVO.getCodigo(), con);
            if (!UteisValidacao.emptyList(operacoesExcluir)) {
                for (String tipoOperacao : operacoesExcluir) {
                    SuperFacadeJDBC.executarUpdate("delete from contratooperacao where contrato = " + contratoVO.getCodigo() + " and  tipooperacao = '" + tipoOperacao + "'", con);
                }
            }
            if(contratoVO.getContratoResponsavelRenovacaoMatricula() > 0){
                Usuario usuarioDAO = new Usuario(con);
                UsuarioVO usuario = usuarioDAO.getUsuarioRecorrencia();
                ajustarRenovacoesFuturas(contratoVO, "Reprocessamento Datas",usuario, contratoVO.getCodigo());
                usuarioDAO = null;
            }

            SuperFacadeJDBC.executarConsulta("delete from historicocontrato h "
                            + "where contrato = " + contratoVO.getCodigo()
                            + " and tipohistorico not in ('MA', 'RE', 'RN', 'AT', 'RA', 'CA', 'CR', 'RC','RT','TR', 'CT') and not exists (select codigo from historicocontrato  where contrato  = h.contrato and datainiciosituacao > h.datafinalsituacao and tipohistorico in ('AT', 'RA', 'CA', 'CR', 'RC','RT','TR'))",con);

            SuperFacadeJDBC.executarConsulta("update historicocontrato set datafinalsituacao  = '"
                    + Uteis.getDataJDBC(contratoVO.getVigenciaAteAjustada()) + "' where codigo in " +
                    "(select max(codigo) from historicocontrato where contrato = "+contratoVO.getCodigo()+")",  con);
            SuperFacadeJDBC.executarConsulta("update periodoacessocliente  set datafinalacesso  = '" + Uteis.getDataJDBC(contratoVO.getVigenciaAteAjustada()) + "' where codigo in (select max(codigo) from historicocontrato where contrato = "+contratoVO.getCodigo()+")",  con);

            con.commit();
        }catch (Exception e) {
            con.rollback();
        } finally {
            con.setAutoCommit(true);
            zwDAO = null;
        }


    }

    public List<ContratoOperacaoVO> consultarOperacoesQueAlteramVencimento( Integer contrato) throws Exception {
        String sqlStr = "SELECT * FROM ContratoOperacao WHERE contrato = " + contrato + " and tipooperacao in ('BA','BR','CR','TE','CA', 'TR', 'AT', 'BX', 'BC') ORDER BY dataoperacao, codigo";
        try (Statement stm = con.createStatement()) {
            try (ResultSet tabelaResultado = stm.executeQuery(sqlStr)) {
                return (montarDadosConsulta(tabelaResultado, Uteis.NIVELMONTARDADOS_DADOSBASICOS, this.con));
            }
        }
    }

    public int obterDiasAfastamentoContratoPorPeriodo(Integer codigoContrato, Date dataInicio, Date dataFinal) throws SQLException {
        int nrDias = 0;
        String sqlStr = "SELECT * FROM ContratoOperacao WHERE contrato = "+ codigoContrato +" and tipooperacao in ('AT', 'TR', 'CR') " +
                " and ('"+Uteis.getDataFormatoBD(dataInicio)+"' between datainicioefetivacaooperacao and datafimefetivacaooperacao or " +
                " '"+Uteis.getDataFormatoBD(dataFinal)+"' between datainicioefetivacaooperacao and datafimefetivacaooperacao or " +
                " datainicioefetivacaooperacao between '"+Uteis.getDataFormatoBD(dataInicio)+"' and '"+Uteis.getDataFormatoBD(dataFinal)+"')";
        try (Statement stm = con.createStatement()) {
            try (ResultSet tabelaResultado = stm.executeQuery(sqlStr)) {
                while (tabelaResultado.next()) {
                    Date dataInicioComparacao = dataInicio;
                    Date dataFinalComparacao = dataFinal;
                    if(Calendario.maior(tabelaResultado.getDate("datainicioefetivacaooperacao"), dataInicio)){
                        dataInicioComparacao = tabelaResultado.getDate("datainicioefetivacaooperacao");
                    }
                    if(Calendario.menor(tabelaResultado.getDate("datafimefetivacaooperacao"), dataFinal)){
                        dataFinalComparacao =  tabelaResultado.getDate("datafimefetivacaooperacao");
                    }
                    nrDias += (int) Uteis.nrDiasEntreDatas(dataInicioComparacao, dataFinalComparacao) + 1;// +1 porque o primeiro dia do intervalo deve contar

                }
            }
            return nrDias;
        }
    }

    public List<PeriodoMensal> obterPeriodosParaAfastamentoColetivo(Integer codigoContrato, Date dataInicio, Date dataFinal) throws Exception {
        List<PeriodoMensal> listaPeriodos = new ArrayList<>();
        String sqlStr = "SELECT * FROM ContratoOperacao WHERE contrato = "+ codigoContrato +" and tipooperacao in ('AT', 'TR', 'CR') " +
                " and ('"+Uteis.getDataFormatoBD(dataInicio)+"' between datainicioefetivacaooperacao and datafimefetivacaooperacao or " +
                " '"+Uteis.getDataFormatoBD(dataFinal)+"' between datainicioefetivacaooperacao and datafimefetivacaooperacao or " +
                " datainicioefetivacaooperacao between '"+Uteis.getDataFormatoBD(dataInicio)+"' and '"+Uteis.getDataFormatoBD(dataFinal)+"') order by datainicioefetivacaooperacao";
        List<Date> diasAfastamentoColetivo = Uteis.getDiasEntreDatas(dataInicio, dataFinal);
        try (Statement stm = con.createStatement()) {
            try (ResultSet tabelaResultado = stm.executeQuery(sqlStr)) {
                while (tabelaResultado.next()) {
                    Date dataInicioComparacao = dataInicio;
                    Date dataFinalComparacao = dataFinal;
                    if(Calendario.maior(tabelaResultado.getDate("datainicioefetivacaooperacao"), dataInicio)){
                        dataInicioComparacao = tabelaResultado.getDate("datainicioefetivacaooperacao");
                    }
                    if(Calendario.menor(tabelaResultado.getDate("datafimefetivacaooperacao"), dataFinal)){
                        dataFinalComparacao =  tabelaResultado.getDate("datafimefetivacaooperacao");
                    }
                    List<Date> diasOperacao = Uteis.getDiasEntreDatas(dataInicioComparacao , dataFinalComparacao);
                    for (Date dataOperacao : diasOperacao){
                        if(diasAfastamentoColetivo.contains(dataOperacao)){
                            diasAfastamentoColetivo.remove(dataOperacao);
                        }
                    }
                }
                if(diasAfastamentoColetivo.size() > 0) {
                    PeriodoMensal periodoOperacao = new PeriodoMensal();
                    for (Date diasAfastamento : diasAfastamentoColetivo) {
                        if(periodoOperacao.getDataInicio() == null){
                            periodoOperacao.setDataInicio(diasAfastamento);
                            periodoOperacao.setDataTermino(diasAfastamento);
                        } else if(Calendario.igual(periodoOperacao.getDataTermino(), Uteis.somarDias(diasAfastamento ,- 1))){
                            periodoOperacao.setDataTermino(diasAfastamento);
                        } else {
                            listaPeriodos.add(periodoOperacao);
                            periodoOperacao = new PeriodoMensal();
                            periodoOperacao.setDataInicio(diasAfastamento);
                            periodoOperacao.setDataTermino(diasAfastamento);
                        }
                    }
                    listaPeriodos.add(periodoOperacao);
                }
            }
            return listaPeriodos;
        }
    }

    public List consultarAfastamentoColetivoPorContrato(Integer codigoContrato, Date dataOperacao,  int nivelMontarDados) throws Exception {
        String sqlStr = "SELECT * FROM ContratoOperacao WHERE contrato = " + codigoContrato + " and dataoperacao::date = '"+ Uteis.getDataFormatoBD(dataOperacao) +"' ORDER BY dataInicioEfetivacaoOperacao";
        try (Statement stm = con.createStatement()) {
            try (ResultSet tabelaResultado = stm.executeQuery(sqlStr)) {
                return (montarDadosConsulta(tabelaResultado, nivelMontarDados, this.con));
            }
        }
    }


}

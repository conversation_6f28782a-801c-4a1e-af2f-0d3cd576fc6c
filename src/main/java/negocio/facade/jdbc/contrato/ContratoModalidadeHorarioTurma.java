package negocio.facade.jdbc.contrato;

import negocio.comuns.contrato.ContratoModalidadeHorarioTurmaVO;
import negocio.comuns.plano.HorarioTurmaVO;
import negocio.comuns.utilitarias.ConsistirException;
import negocio.comuns.utilitarias.Ordenacao;
import negocio.comuns.utilitarias.Uteis;
import negocio.facade.jdbc.arquitetura.SuperEntidade;
import negocio.facade.jdbc.plano.HorarioTurma;
import negocio.interfaces.contrato.ContratoModalidadeHorarioTurmaInterfaceFacade;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.Statement;
import java.util.ArrayList;
import java.util.Iterator;
import java.util.List;

/**
 * Classe de persistência que encapsula todas as operações de manipulação dos dados da classe <code>ContratoModalidadeHorarioTurmaVO</code>.
 * Responsável por implementar operações como incluir, alterar, excluir e consultar pertinentes a classe <code>ContratoModalidadeHorarioTurmaVO</code>.
 * Encapsula toda a interação com o banco de dados.
 * @see ContratoModalidadeHorarioTurmaVO
 * @see SuperEntidade
 * @see ContratoModalidadeTurma
 */
public class ContratoModalidadeHorarioTurma extends SuperEntidade implements ContratoModalidadeHorarioTurmaInterfaceFacade {

    public ContratoModalidadeHorarioTurma() throws Exception {
        super();
        setIdEntidade("Contrato");
    }

    public ContratoModalidadeHorarioTurma(Connection conexao) throws Exception {
        super(conexao);
        setIdEntidade("Contrato");
    }

    /**
     * Operação responsável por retornar um novo objeto da classe <code>ContratoModalidadeHorarioTurmaVO</code>.
     */
    @Override
    public ContratoModalidadeHorarioTurmaVO novo() throws Exception {
        incluir(getIdEntidade());
        return new ContratoModalidadeHorarioTurmaVO();
    }

    /**
     * Operação responsável por incluir no banco de dados um objeto da classe <code>ContratoModalidadeHorarioTurmaVO</code>.
     * Primeiramente valida os dados (<code>validarDados</code>) do objeto. Verifica a conexão com o banco de dados e a permissão do usuário
     * para realizar esta operacão na entidade.
     * Isto, através da operação <code>incluir</code> da superclasse.
     * @param obj  Objeto da classe <code>ContratoModalidadeHorarioTurmaVO</code> que será gravado no banco de dados.
     * @exception Exception Caso haja problemas de conexão, restrição de acesso ou validação de dados.
     */
    @Override
    public void incluir(ContratoModalidadeHorarioTurmaVO obj) throws Exception {
        ContratoModalidadeHorarioTurmaVO.validarDados(obj);
        incluir(getIdEntidade());
        obj.realizarUpperCaseDados();
        String sql = "INSERT INTO ContratoModalidadeHorarioTurma(contratoModalidadeTurma, horarioTurma, percOcupacao, percDesconto ) VALUES ( ?, ?, ?, ? )";
        PreparedStatement sqlInserir = con.prepareStatement(sql);
        if (obj.getContratoModalidadeTurma() != 0) {
            sqlInserir.setInt(1, obj.getContratoModalidadeTurma());
        } else {
            sqlInserir.setNull(1, 0);
        }
        sqlInserir.setInt(2, obj.getHorarioTurma().getCodigo());
        sqlInserir.setDouble(3, obj.getPercOcupacao());
        sqlInserir.setDouble(4, obj.getPercDesconto());
        sqlInserir.execute();
        obj.setCodigo(obterValorChavePrimariaCodigo());
        obj.setNovoObj(false);
    }

    /**
     * Operação responsável por alterar no BD os dados de um objeto da classe <code>ContratoModalidadeHorarioTurmaVO</code>.
     * Sempre utiliza a chave primária da classe como atributo para localização do registro a ser alterado.
     * Primeiramente valida os dados (<code>validarDados</code>) do objeto. Verifica a conexão com o banco de dados e a permissão do usuário
     * para realizar esta operacão na entidade.
     * Isto, através da operação <code>alterar</code> da superclasse.
     * @param obj    Objeto da classe <code>ContratoModalidadeHorarioTurmaVO</code> que será alterada no banco de dados.
     * @exception Exception Caso haja problemas de conexão, restrição de acesso ou validação de dados.
     */
    @Override
    public void alterar(ContratoModalidadeHorarioTurmaVO obj) throws Exception {
        ContratoModalidadeHorarioTurmaVO.validarDados(obj);
        alterar(getIdEntidade());
        obj.realizarUpperCaseDados();
        String sql = "UPDATE ContratoModalidadeHorarioTurma set contratoModalidadeTurma=?, horarioTurma=?, percOcupacao=?, percDesconto=? WHERE ((codigo = ?))";
        PreparedStatement sqlAlterar = con.prepareStatement(sql);
        if (obj.getContratoModalidadeTurma() != 0) {
            sqlAlterar.setInt(1, obj.getContratoModalidadeTurma());
        } else {
            sqlAlterar.setNull(1, 0);
        }
        sqlAlterar.setInt(2, obj.getHorarioTurma().getCodigo());
        sqlAlterar.setDouble(3, obj.getPercOcupacao());
        sqlAlterar.setDouble(4, obj.getPercDesconto());
        sqlAlterar.setInt(5, obj.getCodigo());
        sqlAlterar.execute();
    }

    /**
     * Operação responsável por excluir no BD um objeto da classe <code>ContratoModalidadeHorarioTurmaVO</code>.
     * Sempre localiza o registro a ser excluído através da chave primária da entidade.
     * Primeiramente verifica a conexão com o banco de dados e a permissão do usuário
     * para realizar esta operacão na entidade.
     * Isto, através da operação <code>excluir</code> da superclasse.
     * @param obj    Objeto da classe <code>ContratoModalidadeHorarioTurmaVO</code> que será removido no banco de dados.
     * @exception Exception Caso haja problemas de conexão ou restrição de acesso.
     */
    @Override
    public void excluir(ContratoModalidadeHorarioTurmaVO obj) throws Exception {
        excluir(getIdEntidade());
        String sql = "DELETE FROM ContratoModalidadeHorarioTurma WHERE codigo = ?";
        PreparedStatement sqlExcluir = con.prepareStatement(sql);
        sqlExcluir.setInt(1, obj.getCodigo());
        sqlExcluir.execute();
    }

    /**
     * Responsável por realizar uma consulta de <code>ContratoModalidadeHorarioTurma</code> através do valor do atributo
     * <code>Integer horarioTurma</code>. Retorna os objetos com valores iguais ou superiores ao parâmetro fornecido.
     * Faz uso da operação <code>montarDadosConsulta</code> que realiza o trabalho de prerarar o List resultante.
     * @param   controlarAcesso Indica se a aplicação deverá verificar se o usuário possui permissão para esta consulta ou não.
     * @return  List Contendo vários objetos da classe <code>ContratoModalidadeHorarioTurmaVO</code> resultantes da consulta.
     * @exception Exception Caso haja problemas de conexão ou restrição de acesso.
     */
    @Override
    public List consultarPorHorarioTurma(Integer valorConsulta, boolean controlarAcesso, int nivelMontarDados) throws Exception {
        consultar(getIdEntidade(), controlarAcesso);
        String sqlStr = "SELECT * FROM ContratoModalidadeHorarioTurma WHERE horarioTurma >= " + valorConsulta + " ORDER BY horarioTurma";
        Statement stm = con.createStatement();
        ResultSet tabelaResultado = stm.executeQuery(sqlStr);
        return (montarDadosConsulta(tabelaResultado, nivelMontarDados, this.con));
    }

    /**
     * Responsável por realizar uma consulta de <code>ContratoModalidadeHorarioTurma</code> através do valor do atributo
     * <code>codigo</code> da classe <code>ContratoModalidadeTurma</code>
     * Faz uso da operação <code>montarDadosConsulta</code> que realiza o trabalho de prerarar o List resultante.
     * @return  List Contendo vários objetos da classe <code>ContratoModalidadeHorarioTurmaVO</code> resultantes da consulta.
     * @exception Exception Caso haja problemas de conexão ou restrição de acesso.
     */
    @Override
    public List consultarPorCodigoContratoModalidadeTurma(Integer valorConsulta, int nivelMontarDados) throws Exception {
        consultar(getIdEntidade(), true);
        String sqlStr = "SELECT ContratoModalidadeHorarioTurma.* FROM ContratoModalidadeHorarioTurma, ContratoModalidadeTurma WHERE ContratoModalidadeHorarioTurma.contratoModalidadeTurma = ContratoModalidadeTurma.codigo and ContratoModalidadeTurma.codigo = " + valorConsulta + " ORDER BY ContratoModalidadeTurma.codigo";
        Statement stm = con.createStatement();
        ResultSet tabelaResultado = stm.executeQuery(sqlStr);
        return montarDadosConsulta(tabelaResultado, nivelMontarDados, this.con);
    }

    /**
     * Responsável por realizar uma consulta de <code>ContratoModalidadeHorarioTurma</code> através do valor do atributo
     * <code>Integer codigo</code>. Retorna os objetos com valores iguais ou superiores ao parâmetro fornecido.
     * Faz uso da operação <code>montarDadosConsulta</code> que realiza o trabalho de prerarar o List resultante.
     * @param   controlarAcesso Indica se a aplicação deverá verificar se o usuário possui permissão para esta consulta ou não.
     * @return  List Contendo vários objetos da classe <code>ContratoModalidadeHorarioTurmaVO</code> resultantes da consulta.
     * @exception Exception Caso haja problemas de conexão ou restrição de acesso.
     */
    @Override
    public List consultarPorCodigo(Integer valorConsulta, boolean controlarAcesso, int nivelMontarDados) throws Exception {
        consultar(getIdEntidade(), controlarAcesso);
        String sqlStr = "SELECT * FROM ContratoModalidadeHorarioTurma WHERE codigo >= " + valorConsulta + " ORDER BY codigo";
        Statement stm = con.createStatement();
        ResultSet tabelaResultado = stm.executeQuery(sqlStr);
        return (montarDadosConsulta(tabelaResultado, nivelMontarDados, this.con));
    }

    /**
     * Responsável por montar os dados de vários objetos, resultantes de uma consulta ao banco de dados (<code>ResultSet</code>).
     * Faz uso da operação <code>montarDados</code> que realiza o trabalho para um objeto por vez.
     * @return  List Contendo vários objetos da classe <code>ContratoModalidadeHorarioTurmaVO</code> resultantes da consulta.
     */
    public static List montarDadosConsulta(ResultSet tabelaResultado, int nivelMontarDados, Connection con) throws Exception {
        List vetResultado = new ArrayList();
        while (tabelaResultado.next()) {
            ContratoModalidadeHorarioTurmaVO obj = montarDados(tabelaResultado, nivelMontarDados, con);
            vetResultado.add(obj);
        }
        return vetResultado;
    }

    private static ContratoModalidadeHorarioTurmaVO montarDadosBasico(ResultSet dadosSQL) throws Exception {
        ContratoModalidadeHorarioTurmaVO obj = new ContratoModalidadeHorarioTurmaVO();
        obj.setNovoObj(false);
        obj.setCodigo(new Integer(dadosSQL.getInt("codigo")));
        obj.setContratoModalidadeTurma(dadosSQL.getInt("contratoModalidadeTurma"));
        obj.getHorarioTurma().setCodigo(dadosSQL.getInt("horarioTurma"));

        try{
            obj.setPercOcupacao(dadosSQL.getDouble("percOcupacao"));
            obj.setPercDesconto(dadosSQL.getDouble("percDesconto"));
        }catch (Exception ignored){}

        return obj;
    }

    /**
     * Responsável por montar os dados resultantes de uma consulta ao banco de dados (<code>ResultSet</code>)
     * em um objeto da classe <code>ContratoModalidadeHorarioTurmaVO</code>.
     * @return  O objeto da classe <code>ContratoModalidadeHorarioTurmaVO</code> com os dados devidamente montados.
     */
    public static ContratoModalidadeHorarioTurmaVO montarDados(ResultSet dadosSQL, int nivelMontarDados, Connection con) throws Exception {
        ContratoModalidadeHorarioTurmaVO obj = montarDadosBasico(dadosSQL);
        if (nivelMontarDados == Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA)
            return obj;
        if (nivelMontarDados == Uteis.NIVELMONTARDADOS_DADOSBASICOS)
            return obj;
        if (nivelMontarDados == Uteis.NIVELMONTARDADOS_SITUACAOCLIENTESINTETICODW) {
            montarDadosHorarioTurma(obj, Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA, con);
            return obj;
        }
        montarDadosHorarioTurma(obj, Uteis.NIVELMONTARDADOS_TODOS, con);
        return obj;
    }

    /**
     * Operação responsável por montar os dados de um objeto da classe <code>ColaboradorVO</code> relacionado ao objeto <code>ContratoVO</code>.
     * Faz uso da chave primária da classe <code>ColaboradorVO</code> para realizar a consulta.
     * @param obj  Objeto no qual será montado os dados consultados.
     */
    public static void montarDadosHorarioTurma(ContratoModalidadeHorarioTurmaVO obj, int nivelMontarDados, Connection con) throws Exception {
        if (obj.getHorarioTurma().getCodigo() == 0) {
            obj.setHorarioTurma(new HorarioTurmaVO());
            return;
        }
        HorarioTurma horarioTurma = new HorarioTurma(con);
        obj.setHorarioTurma(horarioTurma.consultarPorChavePrimaria(obj.getHorarioTurma().getCodigo(), nivelMontarDados));
        obj.getHorarioTurma().setHorarioTurmaEscolhida(true);
        horarioTurma = null;
    }

    /**
     * Operação responsável por excluir todos os objetos da <code>ContratoModalidadeHorarioTurmaVO</code> no BD.
     * Faz uso da operação <code>excluir</code> disponível na classe <code>ContratoModalidadeHorarioTurma</code>.
     * @param contratoModalidadeTurma campo chave para exclusão dos objetos no BD.
     * @exception Exception  Erro de conexão com o BD ou restrição de acesso a esta operação.
     */
    @Override
    public void excluirContratoModalidadeHorarioTurmas(Integer contratoModalidadeTurma) throws Exception {
        excluir(getIdEntidade());
        String sql = "DELETE FROM ContratoModalidadeHorarioTurma WHERE (contratoModalidadeTurma = ?)";
        PreparedStatement sqlExcluir = con.prepareStatement(sql);
        sqlExcluir.setInt(1, contratoModalidadeTurma);
        sqlExcluir.execute();
    }

    /**
     * Operação responsável por alterar todos os objetos da <code>ContratoModalidadeHorarioTurmaVO</code> contidos em um Hashtable no BD.
     * Faz uso da operação <code>excluirContratoModalidadeHorarioTurmas</code> e <code>incluirContratoModalidadeHorarioTurmas</code> disponíveis na classe <code>ContratoModalidadeHorarioTurma</code>.
     * @param objetos  List com os objetos a serem alterados ou incluídos no BD.
     * @exception Exception  Erro de conexão com o BD ou restrição de acesso a esta operação.
     */
    @Override
    public void alterarContratoModalidadeHorarioTurmas(Integer contratoModalidadeTurma, List objetos) throws Exception {
//        String str = "DELETE FROM ContratoModalidadeHorarioTurma WHERE contratoModalidadeTurma = " + contratoModalidadeTurma.intValue();
//        Iterator i = objetos.iterator();
//        while (i.hasNext()) {
//            ContratoModalidadeHorarioTurmaVO objeto = (ContratoModalidadeHorarioTurmaVO) i.next();
//            if (objeto.getHorarioTurma().getHorarioTurmaEscolhida()) {
//                str += " AND codigo <> " + objeto.getCodigo().intValue();
//            }
//        }
//        PreparedStatement sqlExcluir = con.prepareStatement(str);
//        sqlExcluir.execute();
        Iterator e = objetos.iterator();
        while (e.hasNext()) {
            ContratoModalidadeHorarioTurmaVO objeto = (ContratoModalidadeHorarioTurmaVO) e.next();
            if (objeto.getHorarioTurma().getHorarioTurmaEscolhida()) {
                if (objeto.getCodigo().equals(new Integer(0))) {
                    objeto.setContratoModalidadeTurma(contratoModalidadeTurma);
                    incluir(objeto);
                } else {
                    alterar(objeto);
                }
            }
        }
    }

    /**
     * Operação responsável por incluir objetos da <code>ContratoModalidadeHorarioTurmaVO</code> no BD.
     * Garantindo o relacionamento com a entidade principal <code>contrato.ContratoModalidadeTurma</code> através do atributo de vínculo.
     * @param objetos List contendo os objetos a serem gravados no BD da classe.
     * @exception Exception  Erro de conexão com o BD ou restrição de acesso a esta operação.
     */
    @Override
    public void incluirContratoModalidadeHorarioTurmas(Integer contratoModalidadeTurmaPrm, List objetos) throws Exception {
        Iterator e = objetos.iterator();
        while (e.hasNext()) {
            ContratoModalidadeHorarioTurmaVO obj = (ContratoModalidadeHorarioTurmaVO) e.next();
            if (obj.getHorarioTurma().getHorarioTurmaEscolhida()) {
                obj.setContratoModalidadeTurma(contratoModalidadeTurmaPrm);
                incluir(obj);
            }

        }
    }

    /**
     * Operação responsável por consultar todos os <code>ContratoModalidadeHorarioTurmaVO</code> relacionados a um objeto da classe <code>contrato.ContratoModalidadeTurma</code>.
     * @param contratoModalidadeTurma  Atributo de <code>contrato.ContratoModalidadeTurma</code> a ser utilizado para localizar os objetos da classe <code>ContratoModalidadeHorarioTurmaVO</code>.
     * @return List  Contendo todos os objetos da classe <code>ContratoModalidadeHorarioTurmaVO</code> resultantes da consulta.
     * @exception Exception  Erro de conexão com o BD ou restrição de acesso a esta operação.
     */
    @Override
    public List consultarContratoModalidadeHorarioTurmas(Integer contratoModalidadeTurma, int nivelMontarDados) throws Exception {
        consultar(getIdEntidade());
        List objetos = new ArrayList();
        String sql = "SELECT * FROM ContratoModalidadeHorarioTurma WHERE contratoModalidadeTurma = ?";
        PreparedStatement sqlConsulta = con.prepareStatement(sql);
        sqlConsulta.setInt(1, contratoModalidadeTurma);
        ResultSet resultado = sqlConsulta.executeQuery();
        while (resultado.next()) {
            ContratoModalidadeHorarioTurmaVO novoObj = ContratoModalidadeHorarioTurma.montarDados(resultado, nivelMontarDados, this.con);
            objetos.add(novoObj);
        }
        Ordenacao.ordenarLista(objetos, "diaDaSemana_Apresentar");
        return objetos;
    }

    /**
     * Operação responsável por localizar um objeto da classe <code>ContratoModalidadeHorarioTurmaVO</code>
     * através de sua chave primária.
     * @exception Exception Caso haja problemas de conexão ou localização do objeto procurado.
     */
    @Override
    public ContratoModalidadeHorarioTurmaVO consultarPorChavePrimaria(Integer codigoPrm, int nivelMontarDados) throws Exception {
        consultar(getIdEntidade(), false);
        String sql = "SELECT * FROM ContratoModalidadeHorarioTurma WHERE codigo = ?";
        PreparedStatement sqlConsultar = con.prepareStatement(sql);
        sqlConsultar.setInt(1, codigoPrm);
        ResultSet tabelaResultado = sqlConsultar.executeQuery();
        if (!tabelaResultado.next()) {
            throw new ConsistirException("Dados Não Encontrados ( ContratoModalidadeHorarioTurma ).");
        }
        return (montarDados(tabelaResultado, nivelMontarDados, this.con));
    }

    public boolean houveAlteracaoDeHorarioTurma(Integer codigoContrato, List<HorarioTurmaVO> listaHorarioTurma)throws Exception{
        StringBuilder sql = new StringBuilder();
        sql.append("select cmht.* \n");
        sql.append("from contratomodalidade cm \n");
        sql.append("inner join contratomodalidadeTurma cmt on cmt.contratomodalidade = cm.codigo \n");
        sql.append("inner join contratomodalidadeHorarioTurma cmht on  cmht.contratomodalidadeTurma = cmt.codigo \n");
        sql.append("where cm.contrato = ").append(codigoContrato);
        Statement st = con.createStatement();
        ResultSet rs = st.executeQuery(sql.toString());
        List<Integer> lista = new ArrayList<Integer>();
        for (HorarioTurmaVO horarioTurmaVO: listaHorarioTurma){
            lista.add(horarioTurmaVO.getCodigo());
        }
        int totalQueFoiAdicionadoJaEstaEmBanco = 0;
        int totalRegBD = 0;
        while (rs.next()){
            totalRegBD++;
            if (lista.contains(rs.getInt("horarioTurma"))){
                totalQueFoiAdicionadoJaEstaEmBanco++;
            }
        }
        if (totalRegBD != listaHorarioTurma.size()){
            return true;
        }else{
            return totalQueFoiAdicionadoJaEstaEmBanco != listaHorarioTurma.size();
        }

    }

    @Override
    public List consultarPorContrato(int codigoContrato, int nivelMontarDados) throws Exception {
        consultar(getIdEntidade());
        List objetos = new ArrayList();
        StringBuilder sql = new StringBuilder();
        sql.append("select cmht.* \n");
        sql.append("from contratoModalidade cm \n");
        sql.append("inner join contratomodalidadeturma cmt on cmt.contratomodalidade = cm.codigo \n");
        sql.append("inner join contratomodalidadehorarioturma cmht on cmht.contratomodalidadeturma = cmt.codigo \n");
        sql.append("where cm.contrato = ").append(codigoContrato);
        Statement st = con.createStatement();
        ResultSet rs = st.executeQuery(sql.toString());
        while (rs.next()) {
            ContratoModalidadeHorarioTurmaVO novoObj = ContratoModalidadeHorarioTurma.montarDados(rs, nivelMontarDados, this.con);
            objetos.add(novoObj);
        }
        return objetos;
    }

    public boolean existeContratoModalidadeHorarioTurma(int codigoHorarioTurma) throws Exception{
        StringBuilder sql = new StringBuilder("select count(*) reg from contratomodalidadehorarioturma where horarioturma = ").append(codigoHorarioTurma);
        Statement st = con.createStatement();
        ResultSet rs = st.executeQuery(sql.toString());

        if(rs.next()){
            return rs.getInt("reg") > 0;
        }
        return false;
    }
}

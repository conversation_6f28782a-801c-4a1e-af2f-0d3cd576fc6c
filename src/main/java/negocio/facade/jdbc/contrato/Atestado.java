package negocio.facade.jdbc.contrato;

import negocio.comuns.utilitarias.Calendario;
import org.json.JSONArray;
import org.json.JSONObject;
import negocio.comuns.basico.ArquivoVO;
import negocio.comuns.basico.PessoaVO;
import negocio.comuns.contrato.AtestadoVO;
import negocio.comuns.contrato.MovProdutoVO;
import negocio.comuns.financeiro.MovParcelaVO;
import negocio.comuns.financeiro.VendaAvulsaVO;
import negocio.comuns.utilitarias.Uteis;
import negocio.facade.jdbc.arquitetura.SuperEntidade;
import negocio.facade.jdbc.basico.Arquivo;
import negocio.facade.jdbc.financeiro.MovParcela;
import negocio.facade.jdbc.financeiro.MovProdutoParcela;
import negocio.interfaces.contrato.AtestadoInterfaceFacade;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.Statement;

/**
 * Created by glauco on 04/06/2014.
 */
public class Atestado extends SuperEntidade implements AtestadoInterfaceFacade {

    public Atestado() throws Exception {
    }

    public Atestado(Connection conexao) throws Exception {
        super(conexao);
    }

    public static AtestadoVO montarDados(ResultSet dadosSQL, int nivelMontarDados, Connection con) throws Exception {
        AtestadoVO obj = new AtestadoVO();
        obj.setCodigo(dadosSQL.getInt("codigo"));
        obj.getMovProduto().setCodigo(dadosSQL.getInt("movproduto"));
        obj.setParqPositivo(dadosSQL.getBoolean("parqpositivo"));
        obj.getArquivo().setCodigo(dadosSQL.getInt("arquivo"));
        obj.setObservacao(dadosSQL.getString("observacao"));
        obj.setAvaliacaoFisicaTW(dadosSQL.getInt("avaliacaoFisicaTW"));
        obj.setNovoObj(false);

        try {
            obj.setDataRegistro(dadosSQL.getTimestamp("dataregistro"));
        } catch (Exception ignored) {}

        if (nivelMontarDados == Uteis.NIVELMONTARDADOS_MINIMOS) {
            return obj;
        }

        montarDadosArquivo(obj, nivelMontarDados, con);

        return obj;
    }

    public static void montarDadosArquivo(AtestadoVO obj, int nivelMontarDados, Connection con) throws Exception {
        Arquivo arquivo = new Arquivo(con);
        obj.setArquivo(arquivo.consultarPorCodigo(obj.getArquivo().getCodigo(), nivelMontarDados));
        arquivo = null;
    }

    public AtestadoVO consultarPorMovProduto(Integer codMovProduto, int nivelMontarDados) throws Exception {
        String sqlStr = "SELECT a.* FROM atestado a\n" +
                "INNER JOIN movproduto mp ON mp.codigo = a.movproduto\n" +
                "WHERE mp.codigo = " + codMovProduto + "\n" +
                "ORDER BY a.codigo DESC\n" +
                "LIMIT 1";

        Statement stm = con.createStatement();
        ResultSet tabelaResultado = stm.executeQuery(sqlStr);
        if (!tabelaResultado.next()) {
            return new AtestadoVO();
        }
        return montarDados(tabelaResultado, nivelMontarDados, this.con);
    }

    public void incluir(AtestadoVO obj) throws Exception {
        String sql = "INSERT INTO atestado(movproduto, parqpositivo, arquivo, observacao, avaliacaoFisicaTW, dataRegistro) VALUES (?, ?, ?, ?, ?, ?)";
        PreparedStatement sqlInserir = con.prepareStatement(sql);
        int i = 0;
        sqlInserir.setInt(++i, obj.getMovProduto().getCodigo());
        sqlInserir.setBoolean(++i, obj.getParqPositivo());
        if (obj.getArquivo().getCodigo() > 0) {
            sqlInserir.setInt(++i, obj.getArquivo().getCodigo());
        } else {
            sqlInserir.setNull(++i, 0);
        }
        sqlInserir.setString(++i, obj.getObservacao());
        sqlInserir.setInt(++i, obj.getAvaliacaoFisicaTW());
        sqlInserir.setTimestamp(++i, Uteis.getDataJDBCTimestamp(Calendario.hoje()));
        sqlInserir.execute();
        obj.setCodigo(obterValorChavePrimariaCodigo());
        obj.setNovoObj(false);
    }

    public void alterar(AtestadoVO obj) throws Exception {
        String sql = "UPDATE atestado SET parqpositivo = ?, observacao = ?, avaliacaoFisicaTW = ? WHERE codigo = ?";
        PreparedStatement sqlAlterar = con.prepareStatement(sql);
        int i = 0;
        sqlAlterar.setBoolean(++i, obj.getParqPositivo());
        sqlAlterar.setString(++i, obj.getObservacao());
        sqlAlterar.setInt(++i, obj.getAvaliacaoFisicaTW());
        sqlAlterar.setInt(++i, obj.getCodigo());
        sqlAlterar.execute();
    }

    public AtestadoVO consultarUltimoAtestado(PessoaVO pessoa) throws Exception {
        String sqlStr = "SELECT a.* FROM atestado a\n" +
                "INNER JOIN movproduto mp ON mp.codigo = a.movproduto\n" +
                "WHERE pessoa = " + pessoa.getCodigo() + "\n" +
                "ORDER BY a.codigo DESC\n" +
                "LIMIT 1";
        Statement stm = con.createStatement();
        ResultSet tabelaResultado = stm.executeQuery(sqlStr);
        if (!tabelaResultado.next()) {
            return new AtestadoVO();
        }
        return montarDados(tabelaResultado, Uteis.NIVELMONTARDADOS_MINIMOS, this.con);
    }

    public JSONArray consultarAtestadoTreino(String key, Integer codigoCliente) throws Exception {
        StringBuilder sql = new StringBuilder();
        sql.append("SELECT mp.descricao,mp.datafinalvigencia,mp.datainiciovigencia,\n");
        sql.append("arq.codigo as arquivo,at.parqpositivo,arq.pessoa,arq.extensao as ext,at.observacao\n");
        sql.append(" FROM atestado at\n");
        sql.append("INNER JOIN MovProduto mp ON  mp.codigo = at.movproduto\n");
        sql.append("INNER JOIN Cliente cli ON cli.pessoa = mp.pessoa\n");
        sql.append("LEFT JOIN Arquivo arq ON  arq.codigo = at.arquivo\n");
        sql.append(" WHERE cli.codigomatricula = '").append(codigoCliente).append("' order by at.codigo desc");
        ResultSet rs = criarConsulta(sql.toString(), con);
        JSONArray json = new JSONArray();
        while (rs.next()) {
            ArquivoVO arquivoVO = new ArquivoVO();
            arquivoVO.setCodigo(rs.getInt("arquivo"));
            arquivoVO.getPessoa().setCodigo(rs.getInt("pessoa"));
            arquivoVO.setExtensao(rs.getString("ext"));
            JSONObject obj = new JSONObject();
            obj.put("descricao", rs.getString("descricao"));
            obj.put("dataInicio", rs.getDate("datainiciovigencia"));
            obj.put("observacao", rs.getString("observacao"));
            obj.put("dataFinal", rs.getDate("datafinalvigencia"));
            obj.put("codAtestado", arquivoVO.getCodigo());
            obj.put("parq", rs.getBoolean("parqpositivo"));
            obj.put("nomeArquivoGerado", arquivoVO.getNomeArquivoGerado());
            obj.put("extensao", arquivoVO.getExtensao());
            json.put(obj);
        }
        return json;
    }

    public AtestadoVO incluirAtestado(VendaAvulsaVO vendaAvulsaVO, ArquivoVO arquivoVO, String observacao, boolean parqPositivo, Integer avaliacaoFisicaTW) throws Exception {
        MovParcela movParcelaDAO = new MovParcela(con);
        MovProduto movProdutoDAO = new MovProduto(con);
        MovProdutoParcela movProdutoParcelaDAO = new MovProdutoParcela(con);
        Atestado atestadoDAO = new Atestado(con);

        MovParcelaVO movParcela = movParcelaDAO.consultarPorCodigoVendaAvulsa(vendaAvulsaVO.getCodigo(), "", false, Uteis.NIVELMONTARDADOS_MINIMOS);
        movParcela.setMovProdutoParcelaVOs(movProdutoParcelaDAO.consultarMovProdutoParcelasPorParcelas(movParcela.getCodigo(), Uteis.NIVELMONTARDADOS_TODOS));
        MovProdutoVO movProdutoVO = new MovProdutoVO();
        movProdutoVO.setCodigo(movProdutoDAO.consultarPorCodigoVendaAvulsaRetornaCodigo(vendaAvulsaVO.getCodigo()));
        AtestadoVO atestadoVO = new AtestadoVO();
        atestadoVO.setObservacao(observacao);
        atestadoVO.setParqPositivo(parqPositivo);
        atestadoVO.setMovProduto(movProdutoVO);
        atestadoVO.setArquivo(arquivoVO);
        atestadoVO.setAvaliacaoFisicaTW(avaliacaoFisicaTW);
        atestadoDAO.incluir(atestadoVO);

        movParcelaDAO = null;
        movProdutoDAO = null;
        movProdutoParcelaDAO = null;
        atestadoDAO = null;
        return atestadoVO;
    }
}

package negocio.facade.jdbc.contrato;

import java.util.Iterator;
import negocio.comuns.contrato.ConvenioDescontoConfiguracaoVO;
import negocio.facade.jdbc.arquitetura.*;

import java.sql.Connection;
import java.sql.ResultSet;
import java.sql.PreparedStatement;
import java.sql.Statement;
import java.util.List;
import java.util.ArrayList;
import negocio.comuns.utilitarias.*;

/**
 * Classe de persistência que encapsula todas as operações de manipulação dos dados da classe <code>ConvenioDescontoConfiguracaoVO</code>.
 * Responsável por implementar operações como incluir, alterar, excluir e consultar pertinentes a classe <code>ConvenioDescontoConfiguracaoVO</code>.
 * Encapsula toda a interação com o banco de dados.
 * @see ConvenioDescontoConfiguracaoVO
 * @see SuperEntidade
 * @see ConvenioDesconto
 */
public class ConvenioDescontoConfiguracao extends SuperEntidade {    

    public ConvenioDescontoConfiguracao() throws Exception {
        super();
        setIdEntidade("ConfiguracaoConvenio");
    }

    public ConvenioDescontoConfiguracao(Connection con) throws Exception {
    	super(con);
        setIdEntidade("ConfiguracaoConvenio");
	}

	/**
     * Operação responsável por retornar um novo objeto da classe <code>ConvenioDescontoConfiguracaoVO</code>.
     */
    public ConvenioDescontoConfiguracaoVO novo() throws Exception {
        incluir(getIdEntidade());
        ConvenioDescontoConfiguracaoVO obj = new ConvenioDescontoConfiguracaoVO();
        return obj;
    }

    /**
     * Operação responsável por incluir no banco de dados um objeto da classe <code>ConvenioDescontoConfiguracaoVO</code>.
     * Primeiramente valida os dados (<code>validarDados</code>) do objeto. Verifica a conexão com o banco de dados e a permissão do usuário
     * para realizar esta operacão na entidade.
     * Isto, através da operação <code>incluir</code> da superclasse.
     * @param obj  Objeto da classe <code>ConvenioDescontoConfiguracaoVO</code> que será gravado no banco de dados.
     * @exception Exception Caso haja problemas de conexão, restrição de acesso ou validação de dados.
     */
    public void incluir(ConvenioDescontoConfiguracaoVO obj) throws Exception {
        ConvenioDescontoConfiguracaoVO.validarDados(obj);
        //    ConvenioDescontoConfiguracao.incluir(getIdEntidade());
        obj.realizarUpperCaseDados();
        String sql = "INSERT INTO ConvenioDescontoConfiguracao( duracao, valorDesconto, porcentagemDesconto, tipoDesconto, convenioDesconto ) VALUES ( ?, ?, ?, ?, ? )";
        PreparedStatement sqlInserir = con.prepareStatement(sql);
        sqlInserir.setInt(1, obj.getDuracao().intValue());
        sqlInserir.setDouble(2, obj.getValorDesconto().doubleValue());
        sqlInserir.setDouble(3, obj.getPorcentagemDesconto().doubleValue());
        sqlInserir.setString(4, obj.getTipoDesconto());
        if (obj.getConvenioDesconto().intValue() != 0) {
            sqlInserir.setInt(5, obj.getConvenioDesconto().intValue());
        } else {
            sqlInserir.setNull(5, 0);
        }
        sqlInserir.execute();
        obj.setCodigo(obterValorChavePrimariaCodigo());
        obj.setNovoObj(new Boolean(false));
    }

    /**
     * Operação responsável por alterar no BD os dados de um objeto da classe <code>ConvenioDescontoConfiguracaoVO</code>.
     * Sempre utiliza a chave primária da classe como atributo para localização do registro a ser alterado.
     * Primeiramente valida os dados (<code>validarDados</code>) do objeto. Verifica a conexão com o banco de dados e a permissão do usuário
     * para realizar esta operacão na entidade.
     * Isto, através da operação <code>alterar</code> da superclasse.
     * @param obj    Objeto da classe <code>ConvenioDescontoConfiguracaoVO</code> que será alterada no banco de dados.
     * @exception Execption Caso haja problemas de conexão, restrição de acesso ou validação de dados.
     */
    public void alterar(ConvenioDescontoConfiguracaoVO obj) throws Exception {
        ConvenioDescontoConfiguracaoVO.validarDados(obj);
        alterar(getIdEntidade());
        obj.realizarUpperCaseDados();
        String sql = "UPDATE ConvenioDescontoConfiguracao set duracao=?, valorDesconto=?, porcentagemDesconto=?, tipoDesconto=?, convenioDesconto=? WHERE ((codigo = ?))";
        PreparedStatement sqlAlterar = con.prepareStatement(sql);
        sqlAlterar.setInt(1, obj.getDuracao());
        sqlAlterar.setDouble(2, obj.getValorDesconto());
        sqlAlterar.setDouble(3, obj.getPorcentagemDesconto());
        sqlAlterar.setString(4, obj.getTipoDesconto());
        if (obj.getConvenioDesconto() != 0) {
            sqlAlterar.setInt(5, obj.getConvenioDesconto());
        } else {
            sqlAlterar.setNull(5, 0);
        }
        sqlAlterar.setInt(6, obj.getCodigo());
        sqlAlterar.execute();
    }

    /**
     * Operação responsável por excluir no BD um objeto da classe <code>ConvenioDescontoConfiguracaoVO</code>.
     * Sempre localiza o registro a ser excluído através da chave primária da entidade.
     * Primeiramente verifica a conexão com o banco de dados e a permissão do usuário
     * para realizar esta operacão na entidade.
     * Isto, através da operação <code>excluir</code> da superclasse.
     * @param obj    Objeto da classe <code>ConvenioDescontoConfiguracaoVO</code> que será removido no banco de dados.
     * @exception Execption Caso haja problemas de conexão ou restrição de acesso.
     */
    public void excluir(ConvenioDescontoConfiguracaoVO obj) throws Exception {
        excluir(obj.getCodigo());
    }

    public void excluir(Integer codigo) throws Exception {
        excluir(getIdEntidade());
        String sql = "DELETE FROM ConvenioDescontoConfiguracao WHERE ((codigo = ?))";
        PreparedStatement sqlExcluir = con.prepareStatement(sql);
        sqlExcluir.setInt(1,codigo);
        sqlExcluir.execute();
    }

    /**
     * Responsável por realizar uma consulta de <code>ConvenioDescontoConfiguracao</code> através do valor do atributo 
     * <code>String tipoDesconto</code>. Retorna os objetos, com início do valor do atributo idêntico ao parâmetro fornecido.
     * Faz uso da operação <code>montarDadosConsulta</code> que realiza o trabalho de prerarar o List resultante.
     * @param   controlarAcesso Indica se a aplicação deverá verificar se o usuário possui permissão para esta consulta ou não.
     * @return  List Contendo vários objetos da classe <code>ConvenioDescontoConfiguracaoVO</code> resultantes da consulta.
     * @exception Exception Caso haja problemas de conexão ou restrição de acesso.
     */
    public List consultarPorTipoDesconto(String valorConsulta, boolean controlarAcesso, int nivelMontarDados) throws Exception {
        consultar(getIdEntidade(), controlarAcesso);
        String sqlStr = "SELECT * FROM ConvenioDescontoConfiguracao WHERE upper( tipoDesconto ) like('" + valorConsulta.toUpperCase() + "%') ORDER BY tipoDesconto";
        Statement stm = con.createStatement();
        ResultSet tabelaResultado = stm.executeQuery(sqlStr);
        return (montarDadosConsulta(tabelaResultado, nivelMontarDados));
    }

    /**
     * Responsável por realizar uma consulta de <code>ConvenioDescontoConfiguracao</code> através do valor do atributo 
     * <code>Double porcentagemDesconto</code>. Retorna os objetos com valores iguais ou superiores ao parâmetro fornecido.
     * Faz uso da operação <code>montarDadosConsulta</code> que realiza o trabalho de prerarar o List resultante.
     * @param   controlarAcesso Indica se a aplicação deverá verificar se o usuário possui permissão para esta consulta ou não.
     * @return  List Contendo vários objetos da classe <code>ConvenioDescontoConfiguracaoVO</code> resultantes da consulta.
     * @exception Exception Caso haja problemas de conexão ou restrição de acesso.
     */
    public List consultarPorPorcentagemDesconto(Double valorConsulta, boolean controlarAcesso, int nivelMontarDados) throws Exception {
        consultar(getIdEntidade(), controlarAcesso);
        String sqlStr = "SELECT * FROM ConvenioDescontoConfiguracao WHERE porcentagemDesconto >= " + valorConsulta.doubleValue() + " ORDER BY porcentagemDesconto";
        Statement stm = con.createStatement();
        ResultSet tabelaResultado = stm.executeQuery(sqlStr);
        return (montarDadosConsulta(tabelaResultado, nivelMontarDados));
    }

    /**
     * Responsável por realizar uma consulta de <code>ConvenioDescontoConfiguracao</code> através do valor do atributo 
     * <code>Double valorDesconto</code>. Retorna os objetos com valores iguais ou superiores ao parâmetro fornecido.
     * Faz uso da operação <code>montarDadosConsulta</code> que realiza o trabalho de prerarar o List resultante.
     * @param   controlarAcesso Indica se a aplicação deverá verificar se o usuário possui permissão para esta consulta ou não.
     * @return  List Contendo vários objetos da classe <code>ConvenioDescontoConfiguracaoVO</code> resultantes da consulta.
     * @exception Exception Caso haja problemas de conexão ou restrição de acesso.
     */
    public List consultarPorValorDesconto(Double valorConsulta, boolean controlarAcesso, int nivelMontarDados) throws Exception {
        consultar(getIdEntidade(), controlarAcesso);
        String sqlStr = "SELECT * FROM ConvenioDescontoConfiguracao WHERE valorDesconto >= " + valorConsulta.doubleValue() + " ORDER BY valorDesconto";
        Statement stm = con.createStatement();
        ResultSet tabelaResultado = stm.executeQuery(sqlStr);
        return (montarDadosConsulta(tabelaResultado, nivelMontarDados));
    }

    /**
     * Responsável por realizar uma consulta de <code>ConvenioDescontoConfiguracao</code> através do valor do atributo 
     * <code>numeroMeses</code> da classe <code>Duracao</code>
     * Faz uso da operação <code>montarDadosConsulta</code> que realiza o trabalho de prerarar o List resultante.
     * @return  List Contendo vários objetos da classe <code>ConvenioDescontoConfiguracaoVO</code> resultantes da consulta.
     * @exception Exception Caso haja problemas de conexão ou restrição de acesso.
     */
    public List consultarPorNumeroMesesDuracao(Integer valorConsulta, int nivelMontarDados) throws Exception {
        consultar(getIdEntidade(), true);
        String sqlStr = "SELECT ConvenioDescontoConfiguracao.* FROM ConvenioDescontoConfiguracao, Duracao WHERE ConvenioDescontoConfiguracao.duracao = Duracao.codigo and Duracao.numeroMeses >= " + valorConsulta.intValue() + " ORDER BY Duracao.numeroMeses";
        Statement stm = con.createStatement();
        ResultSet tabelaResultado = stm.executeQuery(sqlStr);
        return montarDadosConsulta(tabelaResultado, nivelMontarDados);
    }

    /**
     * Responsável por realizar uma consulta de <code>ConvenioDescontoConfiguracao</code> através do valor do atributo 
     * <code>Integer codigo</code>. Retorna os objetos com valores iguais ou superiores ao parâmetro fornecido.
     * Faz uso da operação <code>montarDadosConsulta</code> que realiza o trabalho de prerarar o List resultante.
     * @param   controlarAcesso Indica se a aplicação deverá verificar se o usuário possui permissão para esta consulta ou não.
     * @return  List Contendo vários objetos da classe <code>ConvenioDescontoConfiguracaoVO</code> resultantes da consulta.
     * @exception Exception Caso haja problemas de conexão ou restrição de acesso.
     */
    public List consultarPorCodigo(Integer valorConsulta, boolean controlarAcesso, int nivelMontarDados) throws Exception {
        consultar(getIdEntidade(), controlarAcesso);
        String sqlStr = "SELECT * FROM ConvenioDescontoConfiguracao WHERE codigo >= " + valorConsulta.intValue() + " ORDER BY codigo";
        Statement stm = con.createStatement();
        ResultSet tabelaResultado = stm.executeQuery(sqlStr);
        return (montarDadosConsulta(tabelaResultado, nivelMontarDados));
    }

    /**
     * Responsável por montar os dados de vários objetos, resultantes de uma consulta ao banco de dados (<code>ResultSet</code>).
     * Faz uso da operação <code>montarDados</code> que realiza o trabalho para um objeto por vez.
     * @return  List Contendo vários objetos da classe <code>ConvenioDescontoConfiguracaoVO</code> resultantes da consulta.
     */
    public static List montarDadosConsulta(ResultSet tabelaResultado, int nivelMontarDados) throws Exception {
        List vetResultado = new ArrayList();
        while (tabelaResultado.next()) {
            ConvenioDescontoConfiguracaoVO obj = new ConvenioDescontoConfiguracaoVO();
            obj = montarDados(tabelaResultado, nivelMontarDados);
            vetResultado.add(obj);
        }
        return vetResultado;
    }

    /**
     * Responsável por montar os dados resultantes de uma consulta ao banco de dados (<code>ResultSet</code>)
     * em um objeto da classe <code>ConvenioDescontoConfiguracaoVO</code>.
     * @return  O objeto da classe <code>ConvenioDescontoConfiguracaoVO</code> com os dados devidamente montados.
     */
    public static ConvenioDescontoConfiguracaoVO montarDados(ResultSet dadosSQL, int nivelMontarDados) throws Exception {
        ConvenioDescontoConfiguracaoVO obj = new ConvenioDescontoConfiguracaoVO();
        obj.setCodigo(new Integer(dadosSQL.getInt("codigo")));
        obj.setDuracao(new Integer(dadosSQL.getInt("duracao")));
        obj.setValorDesconto(new Double(dadosSQL.getDouble("valorDesconto")));
        obj.setPorcentagemDesconto(new Double(dadosSQL.getDouble("porcentagemDesconto")));
        obj.setTipoDesconto(dadosSQL.getString("tipoDesconto"));
        if(obj.getTipoDesconto().equals("PD")){
            obj.setApresentarPorcentagem(true);
            obj.setApresentarValorEspecifico(false);
        } else {
            obj.setApresentarPorcentagem(false);
            obj.setApresentarValorEspecifico(true);
        }
        obj.setConvenioDesconto(new Integer(dadosSQL.getInt("convenioDesconto")));
        obj.setNovoObj(new Boolean(false));

        if (nivelMontarDados == Uteis.NIVELMONTARDADOS_DADOSBASICOS) {
            return obj;
        }

        return obj;
    }

    /**
     * Operação responsável por excluir todos os objetos da <code>ConvenioDescontoConfiguracaoVO</code> no BD.
     * Faz uso da operação <code>excluir</code> disponível na classe <code>ConvenioDescontoConfiguracao</code>.
     * @param <code>convenioDesconto</code> campo chave para exclusão dos objetos no BD.
     * @exception Exception  Erro de conexão com o BD ou restrição de acesso a esta operação.
     */
    public void excluirConvenioDescontoConfiguracaos(Integer convenioDesconto) throws Exception {
        // ConvenioDescontoConfiguracao.excluir(getIdEntidade());
        String sql = "DELETE FROM ConvenioDescontoConfiguracao WHERE (convenioDesconto = ?)";
        PreparedStatement sqlExcluir = con.prepareStatement(sql);
        sqlExcluir.setInt(1, convenioDesconto.intValue());
        sqlExcluir.execute();
    }

    /**
     * Operação responsável por alterar todos os objetos da <code>ConvenioDescontoConfiguracaoVO</code> contidos em um Hashtable no BD.
     * Faz uso da operação <code>excluirConvenioDescontoConfiguracaos</code> e <code>incluirConvenioDescontoConfiguracaos</code> disponíveis na classe <code>ConvenioDescontoConfiguracao</code>.
     * @param objetos  List com os objetos a serem alterados ou incluídos no BD.
     * @exception Exception  Erro de conexão com o BD ou restrição de acesso a esta operação.
     */
    void alterarConvenioDescontoConfiguracaos(int codigoConvDesconto, List objetos) throws Exception {
        List<Integer> idsConfiguracoesAtuais = consultarPorCodigoConvenioDesconto(codigoConvDesconto);
        List<ConvenioDescontoConfiguracaoVO> listaTipada = (List<ConvenioDescontoConfiguracaoVO>) objetos;

        for(ConvenioDescontoConfiguracaoVO configuracaoVO: listaTipada) {
            try {
                //se objeto não existir uma exceção ConsistirException será lançada
                consultarPorChavePrimaria(configuracaoVO.getCodigo(), Uteis.NIVELMONTARDADOS_MINIMOS);

                //Se não lançou exceção então objeto existe e deve ser alterado
                alterar(configuracaoVO);
            } catch (ConsistirException ex) {
                incluir(configuracaoVO);
            }
        }

        for (Integer codConfig: idsConfiguracoesAtuais) {
            if (configuracaoExcluida(codConfig, listaTipada))
                excluir(codConfig);
        }
    }

    private boolean configuracaoExcluida(Integer codConfig, List<ConvenioDescontoConfiguracaoVO> objetos) {
        for(ConvenioDescontoConfiguracaoVO config: objetos) {
            if (config.getCodigo().equals(codConfig))
                return false;
        }

        return true;
    }

    private List<Integer> consultarPorCodigoConvenioDesconto(int codigoConvDesconto) throws Exception {
        consultar(getIdEntidade());
        List<Integer> codigos = new ArrayList<Integer>();
        String sql = "SELECT codigo FROM ConvenioDescontoConfiguracao WHERE convenioDesconto = ?";
        PreparedStatement sqlConsulta = con.prepareStatement(sql);
        sqlConsulta.setInt(1, codigoConvDesconto);
        ResultSet resultado = sqlConsulta.executeQuery();
        while (resultado.next()) {
            codigos.add(resultado.getInt("codigo"));
        }
        return codigos;
    }

    /**
     * Operação responsável por incluir objetos da <code>ConvenioDescontoConfiguracaoVO</code> no BD.
     * Garantindo o relacionamento com a entidade principal <code>contrato.ConvenioDesconto</code> através do atributo de vínculo.
     * @param objetos List contendo os objetos a serem gravados no BD da classe.
     * @exception Exception  Erro de conexão com o BD ou restrição de acesso a esta operação.
     */
    public void incluirConvenioDescontoConfiguracaos(Integer convenioDescontoPrm, List objetos) throws Exception {
        Iterator e = objetos.iterator();
        while (e.hasNext()) {
            ConvenioDescontoConfiguracaoVO obj = (ConvenioDescontoConfiguracaoVO) e.next();
            obj.setConvenioDesconto(convenioDescontoPrm);
            incluir(obj);
        }
    }

    /**
     * Operação responsável por consultar todos os <code>ConvenioDescontoConfiguracaoVO</code> relacionados a um objeto da classe <code>contrato.ConvenioDesconto</code>.
     * @param convenioDesconto  Atributo de <code>contrato.ConvenioDesconto</code> a ser utilizado para localizar os objetos da classe <code>ConvenioDescontoConfiguracaoVO</code>.
     * @return List  Contendo todos os objetos da classe <code>ConvenioDescontoConfiguracaoVO</code> resultantes da consulta.
     * @exception Exception  Erro de conexão com o BD ou restrição de acesso a esta operação.
     */
    public List consultarConvenioDescontoConfiguracaos(Integer convenioDesconto, int nivelMontarDados) throws Exception {
        consultar(getIdEntidade());
        List objetos = new ArrayList();
        String sql = "SELECT * FROM ConvenioDescontoConfiguracao WHERE convenioDesconto = ?";
        PreparedStatement sqlConsulta = con.prepareStatement(sql);
        sqlConsulta.setInt(1, convenioDesconto.intValue());
        ResultSet resultado = sqlConsulta.executeQuery();
        while (resultado.next()) {
            ConvenioDescontoConfiguracaoVO novoObj = new ConvenioDescontoConfiguracaoVO();
            novoObj = ConvenioDescontoConfiguracao.montarDados(resultado, nivelMontarDados);
            objetos.add(novoObj);
        }
        return objetos;
    }

    /**
     * Operação responsável por localizar um objeto da classe <code>ConvenioDescontoConfiguracaoVO</code>
     * através de sua chave primária. 
     * @exception Exception Caso haja problemas de conexão ou localização do objeto procurado.
     */
    public ConvenioDescontoConfiguracaoVO consultarPorChavePrimaria(Integer codigoPrm, int nivelMontarDados) throws Exception {
        consultar(getIdEntidade(), false);
        String sql = "SELECT * FROM ConvenioDescontoConfiguracao WHERE codigo = ?";
        PreparedStatement sqlConsultar = con.prepareStatement(sql);
        sqlConsultar.setInt(1, codigoPrm.intValue());
        ResultSet tabelaResultado = sqlConsultar.executeQuery();
        if (!tabelaResultado.next()) {
            throw new ConsistirException("Dados Não Encontrados ( ConvenioDescontoConfiguracao ).");
        }
        return (montarDados(tabelaResultado, nivelMontarDados));
    }    
}

package negocio.facade.jdbc.contrato;

import negocio.facade.jdbc.arquitetura.*;

import java.sql.Connection;
import java.sql.ResultSet;
import java.sql.PreparedStatement;
import java.sql.Statement;
import java.util.List;
import java.util.ArrayList;
import java.util.Iterator;
import negocio.comuns.utilitarias.*;
import negocio.comuns.contrato.ContratoPlanoProdutoSugeridoVO;
import negocio.comuns.plano.PlanoProdutoSugeridoVO;
import negocio.facade.jdbc.plano.PlanoProdutoSugerido;

/**
 * Classe de persistência que encapsula todas as operações de manipulação dos dados da classe <code>ContratoModalidadeTurmaVO</code>.
 * Responsável por implementar operações como incluir, alterar, excluir e consultar pertinentes a classe <code>ContratoModalidadeTurmaVO</code>.
 * Encapsula toda a interação com o banco de dados.
 * @see ContratoModalidadeTurmaVO
 * @see SuperEntidade
 */
public class ContratoPlanoProdutoSugerido extends SuperEntidade {    

    public ContratoPlanoProdutoSugerido() throws Exception {
        super();
        setIdEntidade("Contrato");
    }
    public ContratoPlanoProdutoSugerido(Connection conexao) throws Exception {
        super(conexao);
        setIdEntidade("Contrato");
    }

    /**
     * Operação responsável por retornar um novo objeto da classe <code>ContratoModalidadeTurmaVO</code>.
     */
    public ContratoPlanoProdutoSugeridoVO novo() throws Exception {
        incluir(getIdEntidade());
        ContratoPlanoProdutoSugeridoVO obj = new ContratoPlanoProdutoSugeridoVO();
        return obj;
    }

    /**
     * Operação responsável por incluir no banco de dados um objeto da classe <code>ContratoModalidadeTurmaVO</code>.
     * Primeiramente valida os dados (<code>validarDados</code>) do objeto. Verifica a conexão com o banco de dados e a permissão do usuário
     * para realizar esta operacão na entidade.
     * Isto, através da operação <code>incluir</code> da superclasse.
     * @param obj  Objeto da classe <code>ContratoModalidadeTurmaVO</code> que será gravado no banco de dados.
     * @exception Exception Caso haja problemas de conexão, restrição de acesso ou validação de dados.     */
    public void incluir(ContratoPlanoProdutoSugeridoVO obj) throws Exception {
        ContratoPlanoProdutoSugeridoVO.validarDados(obj);
        incluir(getIdEntidade());
        String sql = "INSERT INTO ContratoPlanoProdutoSugerido( contrato, planoProdutoSugerido, valorFinalProduto ) VALUES ( ?, ?, ? )";
        PreparedStatement sqlInserir = con.prepareStatement(sql);
        sqlInserir.setInt(1, obj.getContrato().intValue());
        if (obj.getPlanoProdutoSugerido().getCodigo().intValue() != 0) {
            sqlInserir.setInt(2, obj.getPlanoProdutoSugerido().getCodigo().intValue());
        } else {
            sqlInserir.setNull(2, 0);
        }
        sqlInserir.setDouble(3, obj.getValorFinalProduto());
        sqlInserir.execute();
        obj.setCodigo(obterValorChavePrimariaCodigo());
        obj.setNovoObj(new Boolean(false));
    }

    /**
     * Operação responsável por alterar no BD os dados de um objeto da classe <code>ContratoModalidadeTurmaVO</code>.
     * Sempre utiliza a chave primária da classe como atributo para localização do registro a ser alterado.
     * Primeiramente valida os dados (<code>validarDados</code>) do objeto. Verifica a conexão com o banco de dados e a permissão do usuário
     * para realizar esta operacão na entidade.
     * Isto, através da operação <code>alterar</code> da superclasse.
     * @param obj    Objeto da classe <code>ContratoModalidadeTurmaVO</code> que será alterada no banco de dados.
     * @exception Execption Caso haja problemas de conexão, restrição de acesso ou validação de dados.
     */
    public void alterar(ContratoPlanoProdutoSugeridoVO obj) throws Exception {
        ContratoPlanoProdutoSugeridoVO.validarDados(obj);
        alterar(getIdEntidade());
        String sql = "UPDATE ContratoPlanoProdutoSugerido set contrato=?, planoProdutoSugerido=? WHERE ((codigo = ?))";
        PreparedStatement sqlAlterar = con.prepareStatement(sql);
        sqlAlterar.setInt(1, obj.getContrato().intValue());
        if (obj.getPlanoProdutoSugerido().getCodigo().intValue() != 0) {
            sqlAlterar.setInt(2, obj.getPlanoProdutoSugerido().getCodigo().intValue());
        } else {
            sqlAlterar.setNull(2, 0);
        }
        sqlAlterar.setDouble(3, obj.getValorFinalProduto());
        sqlAlterar.setInt(4, obj.getCodigo().intValue());
        sqlAlterar.execute();
    }

    /**
     * Operação responsável por excluir no BD um objeto da classe <code>ContratoModalidadeTurmaVO</code>.
     * Sempre localiza o registro a ser excluído através da chave primária da entidade.
     * Primeiramente verifica a conexão com o banco de dados e a permissão do usuário
     * para realizar esta operacão na entidade.
     * Isto, através da operação <code>excluir</code> da superclasse.
     * @param obj    Objeto da classe <code>ContratoModalidadeTurmaVO</code> que será removido no banco de dados.
     * @exception Execption Caso haja problemas de conexão ou restrição de acesso.
     */
    public void excluir(ContratoPlanoProdutoSugeridoVO obj) throws Exception {
        excluir(getIdEntidade());
        String sql = "DELETE FROM ContratoPlanoProdutoSugerido WHERE ((codigo = ?))";
        PreparedStatement sqlExcluir = con.prepareStatement(sql);
        sqlExcluir.setInt(1, obj.getCodigo().intValue());
        sqlExcluir.execute();

    }

    /**
     * Responsável por montar os dados de vários objetos, resultantes de uma consulta ao banco de dados (<code>ResultSet</code>).
     * Faz uso da operação <code>montarDados</code> que realiza o trabalho para um objeto por vez.
     * @return  List Contendo vários objetos da classe <code>ContratoModalidadeTurmaVO</code> resultantes da consulta.
     */
    public static List montarDadosConsulta(ResultSet tabelaResultado, int nivelMontarDados, Connection con) throws Exception {
        List vetResultado = new ArrayList();
        while (tabelaResultado.next()) {
            ContratoPlanoProdutoSugeridoVO obj = new ContratoPlanoProdutoSugeridoVO();
            obj = montarDados(tabelaResultado, nivelMontarDados, con);
            vetResultado.add(obj);
        }
        return vetResultado;
    }

    /**
     * Responsável por montar os dados resultantes de uma consulta ao banco de dados (<code>ResultSet</code>)
     * em um objeto da classe <code>ContratoModalidadeTurmaVO</code>.
     * @return  O objeto da classe <code>ContratoModalidadeTurmaVO</code> com os dados devidamente montados.
     */
    public static ContratoPlanoProdutoSugeridoVO montarDados(ResultSet dadosSQL, int nivelMontarDados, Connection con) throws Exception {
        ContratoPlanoProdutoSugeridoVO obj = new ContratoPlanoProdutoSugeridoVO();
        obj.setCodigo(new Integer(dadosSQL.getInt("codigo")));
        obj.setContrato(new Integer(dadosSQL.getInt("contrato")));
        obj.getPlanoProdutoSugerido().setCodigo(new Integer(dadosSQL.getInt("planoProdutoSugerido")));
        obj.setValorFinalProduto(new Double(dadosSQL.getDouble("valorFinalProduto")));
        obj.setNovoObj(new Boolean(false));
        montarDadosPlanoProdutoSugerido(obj, Uteis.NIVELMONTARDADOS_TODOS, con);
        return obj;
    }

    /**
     * Operação responsável por montar os dados de um objeto da classe <code>ContratoModalidadeTurmaVO</code> relacionado ao objeto <code>ContratoModalidadeTurmaVO</code>.
     * Faz uso da chave primária da classe <code>ContratoModalidadeTurmaVO</code> para realizar a consulta.
     * @param obj  Objeto no qual será montado os dados consultados.
     */
    public static void montarDadosPlanoProdutoSugerido(ContratoPlanoProdutoSugeridoVO obj, int nivelMontarDados, Connection con) throws Exception {
        if (obj.getPlanoProdutoSugerido().getCodigo().intValue() == 0) {
            obj.setPlanoProdutoSugerido(new PlanoProdutoSugeridoVO());
            return;
        }
        PlanoProdutoSugerido planosPlanoProdutoSugerido = new PlanoProdutoSugerido(con);
        obj.setPlanoProdutoSugerido(planosPlanoProdutoSugerido.consultarPorChavePrimaria(obj.getPlanoProdutoSugerido().getCodigo(), nivelMontarDados));
        planosPlanoProdutoSugerido = null;
        obj.getPlanoProdutoSugerido().setProdutoSugeridoEscolhida(true);
    }

    /**
     * Operação responsável por excluir todos os objetos da <code>ContratoModalidadeVO</code> no BD.
     * Faz uso da operação <code>excluir</code> disponível na classe <code>ContratoModalidade</code>.
     * @param <code>contrato</code> campo chave para exclusão dos objetos no BD.
     * @exception Exception  Erro de conexão com o BD ou restrição de acesso a esta operação.
     */
    public void excluirContratoPlanoProdutoSugerido(Integer contrato) throws Exception {
        //ContratoPlanoProdutoSugerido.excluir(getIdEntidade());
        String sql = "DELETE FROM ContratoPlanoProdutoSugerido WHERE (contrato = ?)";
        PreparedStatement sqlExcluir = con.prepareStatement(sql);
        sqlExcluir.setInt(1, contrato.intValue());
        sqlExcluir.execute();
    }

    /**
     * Operação responsável por alterar todos os objetos da <code>ContratoModalidadeVO</code> contidos em um Hashtable no BD.
     * Faz uso da operação <code>excluirContratoModalidades</code> e <code>incluirContratoModalidades</code> disponíveis na classe <code>ContratoModalidade</code>.
     * @param objetos  List com os objetos a serem alterados ou incluídos no BD.
     * @exception Exception  Erro de conexão com o BD ou restrição de acesso a esta operação.
     */
    public void alterarContratoPlanoProdutoSugerido(Integer contrato, List objetos) throws Exception {
        excluirContratoPlanoProdutoSugerido(contrato);
        incluirContratoPlanoProdutoSugerido(contrato, objetos);
    }

    /**
     * Operação responsável por incluir objetos da <code>ContratoModalidadeVO</code> no BD.
     * Garantindo o relacionamento com a entidade principal <code>contrato.Contrato</code> através do atributo de vínculo.
     * @param objetos List contendo os objetos a serem gravados no BD da classe.
     * @exception Exception  Erro de conexão com o BD ou restrição de acesso a esta operação.
     */
    public void incluirContratoPlanoProdutoSugerido(Integer contratoPrm, List objetos) throws Exception {
        Iterator e = objetos.iterator();
        while (e.hasNext()) {
            ContratoPlanoProdutoSugeridoVO obj = (ContratoPlanoProdutoSugeridoVO) e.next();
            if (obj.getPlanoProdutoSugerido().getProdutoSugeridoEscolhida()) {
                obj.setContrato(contratoPrm);
                incluir(obj);
            }
        }
    }

    /**
     * Operação responsável por consultar todos os <code>ContratoModalidadeVO</code> relacionados a um objeto da classe <code>contrato.Contrato</code>.
     * @param contrato  Atributo de <code>contrato.Contrato</code> a ser utilizado para localizar os objetos da classe <code>ContratoModalidadeVO</code>.
     * @return List  Contendo todos os objetos da classe <code>ContratoModalidadeVO</code> resultantes da consulta.
     * @exception Exception  Erro de conexão com o BD ou restrição de acesso a esta operação.
     */
    public List consultarContratoPlanoProdutoSugeridos(Integer contrato, int nivelMontarDados) throws Exception {
        consultar(getIdEntidade());
        List objetos = new ArrayList();
        String sql = "SELECT * FROM ContratoPlanoProdutoSugerido WHERE contrato = ?";
        PreparedStatement sqlConsulta = con.prepareStatement(sql);
        sqlConsulta.setInt(1, contrato.intValue());
        ResultSet resultado = sqlConsulta.executeQuery();
        while (resultado.next()) {
            ContratoPlanoProdutoSugeridoVO novoObj = new ContratoPlanoProdutoSugeridoVO();
            novoObj = ContratoPlanoProdutoSugerido.montarDados(resultado, nivelMontarDados, this.con);
            objetos.add(novoObj);
        }
        return objetos;
    }

    /**
     * Operação responsável por localizar um objeto da classe <code>ContratoModalidadeTurmaVO</code>
     * através de sua chave primária. 
     * @exception Exception Caso haja problemas de conexão ou localização do objeto procurado.
     */
    public ContratoPlanoProdutoSugeridoVO consultarPorChavePrimaria(Integer codigoPrm, int nivelMontarDados) throws Exception {
        consultar(getIdEntidade(), false);
        String sql = "SELECT * FROM ContratoPlanoProdutoSugerido WHERE codigo = ?";
        PreparedStatement sqlConsultar = con.prepareStatement(sql);
        sqlConsultar.setInt(1, codigoPrm.intValue());
        ResultSet tabelaResultado = sqlConsultar.executeQuery();
        if (!tabelaResultado.next()) {
            throw new ConsistirException("Dados Não Encontrados ( ContratoPlanoProdutoSugerido ).");
        }
        return (montarDados(tabelaResultado, nivelMontarDados, this.con));
    }

}

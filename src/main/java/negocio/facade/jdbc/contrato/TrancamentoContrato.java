package negocio.facade.jdbc.contrato;

import br.com.pactosolucoes.comuns.util.JSFUtilities;
import br.com.pactosolucoes.enumeradores.OrigemSistemaEnum;
import br.com.pactosolucoes.enumeradores.TemporalRemocaoPontoEnum;
import negocio.facade.jdbc.arquitetura.*;

import java.sql.Connection;
import java.sql.ResultSet;
import java.sql.PreparedStatement;
import java.sql.Statement;
import java.util.List;
import java.util.ArrayList;
import java.util.Date;
import negocio.comuns.arquitetura.RoboVO;
import negocio.comuns.arquitetura.UsuarioVO;
import negocio.comuns.basico.ClienteVO;
import negocio.comuns.contrato.ContratoOperacaoVO;
import negocio.comuns.contrato.ContratoVO;
import negocio.comuns.contrato.HistoricoContratoVO;
import negocio.comuns.utilitarias.*;
import negocio.comuns.contrato.TrancamentoContratoVO;
import negocio.comuns.contrato.ValidacaoHistoricoContrato;
import negocio.comuns.plano.ProdutoVO;
import negocio.facade.jdbc.financeiro.MovParcela;
import negocio.facade.jdbc.plano.Produto;
import negocio.interfaces.contrato.TrancamentoContratoInterfaceFacade;
import relatorio.negocio.comuns.sad.SituacaoClienteSinteticoEnum;
import servicos.impl.gestaoaula.GestaoAulaService;
//import org.apache.tomcat.util.buf.TimeStamp;

/**
 * Classe de persistência que encapsula todas as operações de manipulação dos dados da classe <code>TrancamentoContratoVO</code>.
 * Responsável por implementar operações como incluir, alterar, excluir e consultar pertinentes a classe <code>TrancamentoContratoVO</code>.
 * Encapsula toda a interação com o banco de dados.
 * @see TrancamentoContratoVO
 * @see SuperEntidade
 */
public class TrancamentoContrato extends SuperEntidade implements TrancamentoContratoInterfaceFacade {

    public TrancamentoContrato() throws Exception {
        super();
        setIdEntidade("Contrato");
    }

    public TrancamentoContrato(Connection con) throws Exception {
        super(con);
        setIdEntidade("Contrato");
    }

    /**
     * Operação responsável por retornar um novo objeto da classe <code>TrancamentoContratoVO</code>.
     */
    public TrancamentoContratoVO novo() throws Exception {
        incluir(getIdEntidade());
        TrancamentoContratoVO obj = new TrancamentoContratoVO();
        return obj;
    }

    /**
     * Operação responsável por incluir no banco de dados um objeto da classe <code>TrancamentoContratoVO</code>.
     * Primeiramente valida os dados (<code>validarDados</code>) do objeto. Verifica a conexão com o banco de dados e a permissão do usuário
     * para realizar esta operacão na entidade.
     * Isto, através da operação <code>incluir</code> da superclasse.
     * @param obj  Objeto da classe <code>TrancamentoContratoVO</code> que será gravado no banco de dados.
     * @exception Exception Caso haja problemas de conexão, restrição de acesso ou validação de dados.
     */
    public void incluirSemCommit(TrancamentoContratoVO obj) throws Exception {
        try {
            // TrancamentoContratoVO.validarDados(obj);
            incluir(getIdEntidade());
            String sql = "INSERT INTO TrancamentoContrato( contrato, valorCongelado, nrDiasCongelado, dataTrancamento, dataFimTrancamento,dataRetorno,valorTrancamento, tipoJustificativa, observacao, responsavelOperacao, produtoTrancamento,alterarVencimentoparcelas ) VALUES ( ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ? , ?)";
            PreparedStatement sqlInserir = con.prepareStatement(sql);
            sqlInserir.setInt(1, obj.getContratoVO().getCodigo().intValue());
            sqlInserir.setDouble(2, obj.getValorCongelado());
            sqlInserir.setInt(3, obj.getNrDiasCongelado());
            sqlInserir.setDate(4, Uteis.getDataJDBC(obj.getDataTrancamento()));
            sqlInserir.setDate(5, Uteis.getDataJDBC(obj.getDataFimTrancamento()));
            sqlInserir.setDate(6, Uteis.getDataJDBC(obj.getDataRetorno()));
            sqlInserir.setDouble(7, obj.getValorTrancamento());
            sqlInserir.setInt(8, obj.getTipoJustificativa().intValue());
            sqlInserir.setString(9, obj.getObservacao());
            sqlInserir.setInt(10, obj.getResponsavelOperacao().getCodigo().intValue());
            sqlInserir.setInt(11, obj.getProdutoTrancamento().getCodigo().intValue());
            sqlInserir.setBoolean(12, obj.isAlterarVencimentoparcelas());
            sqlInserir.execute();
            obj.setCodigo(obterValorChavePrimariaCodigo());
            obj.setNovoObj(false);
        } catch (Exception e) {
            throw e;
        }
    }

    /**
     * Operação responsável por incluir no banco de dados um objeto da classe <code>TrancamentoContratoVO</code>.
     * Primeiramente valida os dados (<code>validarDados</code>) do objeto. Verifica a conexão com o banco de dados e a permissão do usuário
     * para realizar esta operacão na entidade.
     * Isto, através da operação <code>incluir</code> da superclasse.
     * @param obj  Objeto da classe <code>TrancamentoContratoVO</code> que será gravado no banco de dados.
     * @exception Exception Caso haja problemas de conexão, restrição de acesso ou validação de dados.
     */
    public void incluir(TrancamentoContratoVO obj, UsuarioVO usuarioVO) throws Exception {
        GestaoAulaService gestaoAulaService = new GestaoAulaService(con, (String) JSFUtilities.getFromSession("key"));
        try {
            con.setAutoCommit(false);
            TrancamentoContratoVO.validarDados(obj);
            incluir(getIdEntidade());
            ContratoOperacao.alterarMatriculasSemOcupacaoAoTrancar(obj);
            ClienteVO cliente = getFacade().getCliente().consultarPorCodigoPessoa(
                    obj.getContratoVO().getPessoa().getCodigo(),
                    Uteis.NIVELMONTARDADOS_DADOSENTIDADESPRINCIPAIS);

            //se o trancamento começa hoje ou é retroativo deve alterar situação de cliente e contrato
            if (Uteis.getCompareData(obj.getDataTrancamento(), negocio.comuns.utilitarias.Calendario.hoje()) <= 0) {
                obj.getContratoVO().setSituacao("TR");
                obj.getContratoVO().setDataPrevistaRenovar(negocio.comuns.utilitarias.Calendario.hoje());
                obj.getContratoVO().setDataPrevistaRematricula(negocio.comuns.utilitarias.Calendario.hoje());
                obj.inicializarDadosHistoricoContrato();
                getFacade().getContrato().alterarDatasVigenciaContrato(obj.getContratoVO(), true);
                getFacade().getContrato().alterarSituacaoContrato(obj.getContratoVO());

                cliente.setSituacao("TR");
                getFacade().getCliente().alterarSituacaoClienteSemPessoaCommit(cliente);
                obj.inicializarDadosOperacaoContrato();
                obj.modificarPeriodoAcesso();
            } else {
                //senão, o trancamento é futuro e nao deve alterar
                //a situação do cliente e do contrato agora, o Robô que fará
                //esse papel quando chegar na data do trancamento
                obj.inicializarDadosHistoricoContrato();
                obj.inicializarDadosOperacaoContrato();
                obj.modificarPeriodoAcesso();
            }
            obj.gravarPeriodoAcesso();
            incluirSemCommit(obj);
            //executar validacao do robo que coloca o trancamento vencido caso seja retroativo e já esteja vencido
            RoboVO roboVO = new RoboVO();
            roboVO.setUsuarioVO(obj.getResponsavelOperacao());
            roboVO.validarTrancamentoVencido(cliente, obj.getContratoVO(), cliente.getEmpresa());
            roboVO = null;
            //
            if(obj.isAlterarVencimentoparcelas()){
                alterarVencimentoParcelas(obj.getContratoVO(),obj.getProdutoTrancamento().getNrDiasVigencia(), "+", obj.getDataTrancamento());
            }
            obj.gerarParcela(false);
            getFacade().getContratoOperacao().atualizaDataFimDeProdutoComVigenciaDeContrato(obj.getContratoVO().getCodigo(), null, obj.getDataTrancamento());

            getFacade().getZWFacade().atualizarSintetico(getFacade().getCliente().
                            consultarPorCodigoPessoa(obj.getContratoVO().getPessoa().getCodigo(),
                                    Uteis.NIVELMONTARDADOS_SITUACAOCLIENTESINTETICODW),
                    Calendario.hoje(), SituacaoClienteSinteticoEnum.GRUPO_TODOS, false);

            Date inicioAfastamento = Calendario.menor(obj.getDataTrancamento(), Calendario.hoje()) ? Calendario.hoje() : obj.getDataTrancamento();
            gestaoAulaService.desmarcarAulasPorAfastamento(usuarioVO, OrigemSistemaEnum.ZW,
                    obj.getContratoVO(),inicioAfastamento, obj.getDataFimTrancamento());

            con.commit();
           
        } catch (Exception e) {
            con.rollback();
            con.setAutoCommit(true);
            throw e;
        } finally {
            con.setAutoCommit(true);
            gestaoAulaService = null;
        }
    }

    /**
     * Operação responsável por incluir no banco de dados um objeto da classe <code>TrancamentoContratoVO</code>.
     * Primeiramente valida os dados (<code>validarDados</code>) do objeto. Verifica a conexão com o banco de dados e a permissão do usuário
     * para realizar esta operacão na entidade.
     * Isto, através da operação <code>incluir</code> da superclasse.
     * @param obj  Objeto da classe <code>TrancamentoContratoVO</code> que será gravado no banco de dados.
     * @exception Exception Caso haja problemas de conexão, restrição de acesso ou validação de dados.
     */
    public void incluirRetorno(TrancamentoContratoVO obj) throws Exception {
        try {
            ClienteVO cliente = getFacade().getCliente().consultarPorCodigoPessoa(obj.getContratoVO().getPessoa().getCodigo(), Uteis.NIVELMONTARDADOS_DADOSENTIDADESPRINCIPAIS);
            con.setAutoCommit(false);
            incluir(getIdEntidade());
            ContratoOperacaoVO operacao = getFacade().getContratoOperacao().consultarOperacaoContratoPorDataInicioCodigoContratoTipoOperacao(Calendario.hoje(), obj.getContratoVO().getCodigo(), "TR",false, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
            if (operacao == null || operacao.getCodigo().intValue() == 0) {
                operacao = getFacade().getContratoOperacao().consultarOperacaoContratoPorDataInicioCodigoContratoTipoOperacao(Calendario.hoje(), obj.getContratoVO().getCodigo(), "TV",false, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
            }
            if (operacao != null && !operacao.getTipoOperacao().equals("TV")) {
                ValidacaoHistoricoContrato.validarOperacaoRetornoNoMesmoDiaOperacaoOrigem(
                        operacao.getDataInicioEfetivacaoOperacao(),
                        Calendario.hoje());
            }
            if (obj.getApresentarPanelClienteRetornoNoPrazo()) {
                if(obj.isAlterarVencimentoparcelas() && Calendario.menorOuIgual(Calendario.hoje(), obj.getDataFimTrancamento())){
                    int nrDiasOriginal = (int) (Uteis.nrDiasEntreDatas(obj.getDataTrancamento(), obj.getDataFimTrancamento()) + 1);
                    int nrDiasEfetivos= (int) (Uteis.nrDiasEntreDatas(obj.getDataTrancamento(), Uteis.obterDataAnterior(Calendario.hoje(), 1)) + 1);
                    int diferenca = nrDiasOriginal - nrDiasEfetivos;
                    if(diferenca > 0) {
                        alterarVencimentoParcelas(obj.getContratoVO(), diferenca , "-" , obj.getDataTrancamento());
                    }
                }
                obj.getContratoVO().setSituacao("AT");
                if (!(obj.getNrDiasCongelado() > 0)){
                    obj.getContratoVO().setVigenciaAteAjustada(Uteis.obterDataFutura2(negocio.comuns.utilitarias.Calendario.hoje(), (obj.getNrDiasCongelado())));
                }else {
                    obj.getContratoVO().setVigenciaAteAjustada(Uteis.obterDataFutura2(negocio.comuns.utilitarias.Calendario.hoje(), (obj.getNrDiasCongelado() - 1)));  // -1 porque conta o dia de hoje
                }
                obj.getContratoVO().setDataPrevistaRenovar(obj.getContratoVO().getVigenciaAteAjustada());
                obj.getContratoVO().setDataPrevistaRematricula(obj.getContratoVO().getVigenciaAteAjustada());
                getFacade().getContrato().alterarDatasVigenciaContrato(obj.getContratoVO(), true);
                getFacade().getContrato().alterarSituacaoContrato(obj.getContratoVO());
                cliente.setSituacao("AT");
                getFacade().getCliente().alterarSituacaoClienteSemPessoaCommit(cliente);
                obj.inicializarDadosOperacaoContratoRetornoNoPrazo();
                obj.inicializarDadosHistoricoContratoRetornoNoPrazo();
                obj.modificarPeriodoAcessoRetorno();
                obj.validarContratoRenovadosAntecipados();
                getFacade().getContratoOperacao().atualizaDataFimDeProdutoComVigenciaDeContrato(obj.getContratoVO().getCodigo(), null, obj.getContratoVO().getVigenciaAteAjustada());
            } else {
                if (obj.getTaxaDiasExcedidos()) {
                    incluirRetornoComTaxaExcedida(obj);
                    //executar validacao do robo que coloca o trancamento vencido caso seja retroativo e já esteja vencido
                    RoboVO roboVO = new RoboVO();
                    roboVO.setUsuarioVO(obj.getResponsavelOperacao());
                    roboVO.validarTrancamentoVencido(cliente, obj.getContratoVO(), cliente.getEmpresa());
                    roboVO = null;
                } else {
                    incluiRetornoComDescontarDiasExcedido(obj);
                }
            }
             //Altera Matriculas
      

            Contrato.gerarHistoricoTemporalUmContrato(obj.getContratoVO().getCodigo());
            if(!obj.getContratoVO().getSituacao().equals("TR") && !obj.getContratoVencido()){
                ContratoOperacao.ajustarMatriculasRetornoTrancamento(obj.getContratoVO(),
                        obj.getMatriculaVigentes(),
                        obj.getContratoVO().getVigenciaAteAjustada(), true);
            }
            getFacade().getZWFacade().atualizarSintetico(getFacade().getCliente().
                    consultarPorCodigoPessoa(obj.getContratoVO().getPessoa().getCodigo(),
                    Uteis.NIVELMONTARDADOS_SITUACAOCLIENTESINTETICODW),
                    Calendario.hoje(), SituacaoClienteSinteticoEnum.GRUPO_TODOS, false);
            getFacade().getAulaDesmarcada().excluirAulaDesmarcadaPorRetornoAfastamentoAntecipado(obj.getContratoVO().getCodigo(), Calendario.getMaior(obj.getDataTrancamento(), Calendario.hoje()), obj.getDataFimTrancamento());
            con.commit();
        } catch (Exception e) {
            con.rollback();
            con.setAutoCommit(true);
            throw e;
        } finally {
            con.setAutoCommit(true);
        }
    }

    public void incluirRetornoComTaxaExcedida(TrancamentoContratoVO obj) throws Exception {
        try {
            if (Uteis.getCompareData(obj.getDataTrancamento(), negocio.comuns.utilitarias.Calendario.hoje()) <= 0) {
                obj.getContratoVO().setSituacao("TR");
                getFacade().getContrato().alterarSituacaoContrato(obj.getContratoVO());
                ClienteVO cliente = getFacade().getCliente().consultarPorCodigoPessoa(
                        obj.getContratoVO().getPessoa().getCodigo(),
                        Uteis.NIVELMONTARDADOS_DADOSENTIDADESPRINCIPAIS);

                cliente.setSituacao("TR");
                getFacade().getCliente().alterarSituacaoClienteSemPessoaCommit(cliente);
                obj.inicializarDadosHistoricoContrato();
                obj.inicializarDadosOperacaoContrato();
                obj.modificarPeriodoAcesso();
                obj.gravarPeriodoAcesso();
            }
            ContratoOperacao.alterarMatriculasSemOcupacaoAoTrancar(obj);
            incluirSemCommit(obj);
            if(obj.isAlterarVencimentoparcelas()){
                alterarVencimentoParcelas(obj.getContratoVO(),obj.getProdutoTrancamento().getNrDiasVigencia(), "+", obj.getDataTrancamento());
            }
            obj.gerarParcela(false);
            getFacade().getContratoOperacao().atualizaDataFimDeProdutoComVigenciaDeContrato(obj.getContratoVO().getCodigo(), null, obj.getDataTrancamento());
        } catch (Exception e) {
            throw e;
        }
    }

    public void incluiRetornoComDescontarDiasExcedido(TrancamentoContratoVO obj) throws Exception {
        try {
            ClienteVO cliente = getFacade().getCliente().consultarPorCodigoPessoa(obj.getContratoVO().getPessoa().getCodigo(), Uteis.NIVELMONTARDADOS_DADOSENTIDADESPRINCIPAIS);
            if(obj.getContratoVencido()){
                cliente.setSituacao("IN");
                obj.getContratoVO().setSituacao("CA");
                obj.getContratoVO().setVigenciaAteAjustada(Calendario.hoje());
                if(cliente.getEmpresa().getZerarPontosAposVencimento()==TemporalRemocaoPontoEnum.QUANDO_CANCELADO_DESISTENTE || cliente.getEmpresa().getZerarPontosAposVencimento()==TemporalRemocaoPontoEnum.QUANDO_CANCELADO_VENCIDO )
                    getFacade().getHistoricoPontos().zerarPontuacaoRobo(cliente.getPessoa());
            } else {
                cliente.setSituacao("AT");
                obj.getContratoVO().setSituacao("AT");
                obj.getContratoVO().setVigenciaAteAjustada(Uteis.obterDataFutura2(negocio.comuns.utilitarias.Calendario.hoje(), ((obj.getNrDiasCongelado() - 1) - obj.getNumeroDiasDescontar()))); // -1 porque conta o dia de hoje
            }
            obj.getContratoVO().setDataPrevistaRenovar(obj.getContratoVO().getVigenciaAteAjustada());
            obj.getContratoVO().setDataPrevistaRematricula(obj.getContratoVO().getVigenciaAteAjustada());
            getFacade().getContrato().alterarDatasVigenciaContrato(obj.getContratoVO(), true);
            getFacade().getContrato().alterarSituacaoContrato(obj.getContratoVO());
           
            getFacade().getCliente().alterarSituacaoClienteSemPessoaCommit(cliente);
            obj.inicializarDadosHistoricoContratoRetornoForaPrazo();
            obj.inicializarDadosOperacaoContratoRetornoForaPrazo();
            if(obj.getContratoVencido()){
                incluirCancelamentoRetornoTrancamentoVencido(obj);
                incluirHistoricoCancelamentoRetornoTrancamentoVencido(obj);
            }else{
                obj.modificarPeriodoAcessoRetornoForaPrazo();
                obj.validarContratoRenovadosAntecipados();
                getFacade().getContratoOperacao().atualizaDataFimDeProdutoComVigenciaDeContrato(obj.getContratoVO().getCodigo(), null, obj.getContratoVO().getVigenciaAteAjustada());
            }
        } catch (Exception e) {
            throw e;
        }
    }

    /**
     * Operação responsável por alterar no BD os dados de um objeto da classe <code>TrancamentoContratoVO</code>.
     * Sempre utiliza a chave primária da classe como atributo para localização do registro a ser alterado.
     * Primeiramente valida os dados (<code>validarDados</code>) do objeto. Verifica a conexão com o banco de dados e a permissão do usuário
     * para realizar esta operacão na entidade.
     * Isto, através da operação <code>alterar</code> da superclasse.
     * @param obj    Objeto da classe <code>TrancamentoContratoVO</code> que será alterada no banco de dados.
     * @exception Execption Caso haja problemas de conexão, restrição de acesso ou validação de dados.
     */
    public void alterar(TrancamentoContratoVO obj) throws Exception {
        try {
            con.setAutoCommit(false);
            TrancamentoContratoVO.validarDados(obj);
            alterar(getIdEntidade());
            String sql = "UPDATE TrancamentoContrato set nome=?, nomeSemAcento=?, estado=?, Pais=? WHERE ((codigo = ?))";
            PreparedStatement sqlAlterar = con.prepareStatement(sql);
            sqlAlterar.setString(1, "");

            sqlAlterar.execute();
            con.commit();
        } catch (Exception e) {
            con.rollback();
            con.setAutoCommit(true);
            throw e;
        } finally {
            con.setAutoCommit(true);
        }
    }

    /**
     * Operação responsável por excluir no BD um objeto da classe <code>TrancamentoContratoVO</code>.
     * Sempre localiza o registro a ser excluído através da chave primária da entidade.
     * Primeiramente verifica a conexão com o banco de dados e a permissão do usuário
     * para realizar esta operacão na entidade.
     * Isto, através da operação <code>excluir</code> da superclasse.
     * @param obj    Objeto da classe <code>TrancamentoContratoVO</code> que será removido no banco de dados.
     * @exception Execption Caso haja problemas de conexão ou restrição de acesso.
     */
    public void excluir(TrancamentoContratoVO obj) throws Exception {
        try {
            con.setAutoCommit(false);
            excluirSemCommit(obj);
            con.commit();
        } catch (Exception e) {
            con.rollback();
            con.setAutoCommit(true);
            throw e;
        } finally {
            con.setAutoCommit(true);
        }
    }

    public void excluirSemCommit(TrancamentoContratoVO obj) throws Exception {
        try {
            excluir(getIdEntidade());
            String sql = "DELETE FROM TrancamentoContrato WHERE ((codigo = ?))";
            PreparedStatement sqlExcluir = con.prepareStatement(sql);
            sqlExcluir.setInt(1, obj.getCodigo().intValue());
            sqlExcluir.execute();
            if(obj.isAlterarVencimentoparcelas()){
                int nrdias = (int) (Uteis.nrDiasEntreDatas(obj.getDataTrancamento(), obj.getDataFimTrancamento()) + 1);
                alterarVencimentoParcelas(obj.getContratoVO(), nrdias , "-" , obj.getDataTrancamento());
            }
        } catch (Exception e) {
            throw e;
        }
    }

    /**
     * Responsável por realizar uma consulta de <code>TrancamentoContrato</code> através do valor do atributo
     * <code>Integer codigo</code>. Retorna os objetos com valores iguais ou superiores ao parâmetro fornecido.
     * Faz uso da operação <code>montarDadosConsulta</code> que realiza o trabalho de prerarar o List resultante.
     * @param   controlarAcesso Indica se a aplicação deverá verificar se o usuário possui permissão para esta consulta ou não.
     * @return  List Contendo vários objetos da classe <code>TrancamentoContratoVO</code> resultantes da consulta.
     * @exception Exception Caso haja problemas de conexão ou restrição de acesso.
     */
    public List consultarPorCodigo(Integer valorConsulta, boolean controlarAcesso, int nivelMontarDados) throws Exception {
        consultar(getIdEntidade(), controlarAcesso);
        String sqlStr = "SELECT * FROM TrancamentoContrato WHERE codigo >= " + valorConsulta.intValue() + " ORDER BY codigo";
        Statement stm = con.createStatement();
        ResultSet tabelaResultado = stm.executeQuery(sqlStr);
        return (montarDadosConsulta(tabelaResultado, nivelMontarDados));
    }

    /**
     * Responsável por realizar uma consulta de <code>TrancamentoContrato</code> através do valor do atributo
     * <code>Integer codigo</code>. Retorna os objetos com valores iguais ou superiores ao parâmetro fornecido.
     * Faz uso da operação <code>montarDadosConsulta</code> que realiza o trabalho de prerarar o List resultante.
     * @param   controlarAcesso Indica se a aplicação deverá verificar se o usuário possui permissão para esta consulta ou não.
     * @return  List Contendo vários objetos da classe <code>TrancamentoContratoVO</code> resultantes da consulta.
     * @exception Exception Caso haja problemas de conexão ou restrição de acesso.
     */
    public List consultarPorCodigoContrato(Integer valorConsulta, boolean controlarAcesso, int nivelMontarDados) throws Exception {
        consultar(getIdEntidade(), controlarAcesso);
        String sqlStr = "SELECT * FROM TrancamentoContrato WHERE contrato = " + valorConsulta.intValue() + " ORDER BY codigo";
        Statement stm = con.createStatement();
        ResultSet tabelaResultado = stm.executeQuery(sqlStr);
        return (montarDadosConsulta(tabelaResultado, nivelMontarDados));
    }

    /**
     * Operação responsável por obter o último valor gerado para uma chave primária.
     * É utilizada para obter o valor gerado pela SGBD para uma chave primária,
     * a apresentação do mesmo e a implementação de possíveis relacionamentos.
     */
    public TrancamentoContratoVO obterUltimoDiaRetornoContrato(Integer contrato, int nivelMontarDados) throws Exception {
        String sqlStr = "SELECT * FROM TrancamentoContrato WHERE datatrancamento IN (SELECT MAX(datatrancamento) FROM TrancamentoContrato WHERE contrato = " + contrato.intValue() + ") and contrato = " + contrato.intValue();
        Statement stm = con.createStatement();
        ResultSet tabelaResultado = stm.executeQuery(sqlStr);
        if (!tabelaResultado.next()) {
            return null;
        }
        return (montarDados(tabelaResultado, nivelMontarDados));
    }

    /**
     * Responsável por montar os dados de vários objetos, resultantes de uma consulta ao banco de dados (<code>ResultSet</code>).
     * Faz uso da operação <code>montarDados</code> que realiza o trabalho para um objeto por vez.
     * @return  List Contendo vários objetos da classe <code>TrancamentoContratoVO</code> resultantes da consulta.
     */
    public List montarDadosConsulta(ResultSet tabelaResultado, int nivelMontarDados) throws Exception {
        List vetResultado = new ArrayList();
        while (tabelaResultado.next()) {
            TrancamentoContratoVO obj = new TrancamentoContratoVO();
            obj = montarDados(tabelaResultado, nivelMontarDados);
            vetResultado.add(obj);
        }
        return vetResultado;
    }

    /**
     * Responsável por montar os dados resultantes de uma consulta ao banco de dados (<code>ResultSet</code>)
     * em um objeto da classe <code>TrancamentoContratoVO</code>.
     * @return  O objeto da classe <code>TrancamentoContratoVO</code> com os dados devidamente montados.
     */
    public TrancamentoContratoVO montarDados(ResultSet dadosSQL, int nivelMontarDados) throws Exception {
        TrancamentoContratoVO obj = new TrancamentoContratoVO();

        if (nivelMontarDados == Uteis.NIVELMONTARDADOS_ROBO) {
            obj.setDataFimTrancamento(dadosSQL.getDate("dataFimTrancamento"));
            obj.setDataRetorno(dadosSQL.getDate("dataRetorno"));
            return obj;
        }

        obj.setCodigo(new Integer(dadosSQL.getInt("codigo")));
        obj.getContratoVO().setCodigo(new Integer(dadosSQL.getInt("contrato")));
        obj.getProdutoTrancamento().setCodigo(new Integer(dadosSQL.getInt("produtoTrancamento")));
        obj.setValorCongelado(new Double(dadosSQL.getDouble("valorCongelado")));
        obj.setNrDiasCongelado(new Integer(dadosSQL.getInt("nrDiasCongelado")));
        obj.setDataTrancamento(dadosSQL.getDate("dataTrancamento"));
        obj.setDataFimTrancamento(dadosSQL.getDate("dataFimTrancamento"));
        obj.setDataRetorno(dadosSQL.getDate("dataRetorno"));
        obj.setValorTrancamento(new Double(dadosSQL.getDouble("valorTrancamento")));
        obj.setTipoJustificativa(new Integer(dadosSQL.getInt("tipoJustificativa")));
        obj.setObservacao((dadosSQL.getString("observacao")));
        obj.getResponsavelOperacao().setCodigo(new Integer(dadosSQL.getInt("responsavelOperacao")));
        obj.setAlterarVencimentoparcelas(dadosSQL.getBoolean("alterarVencimentoparcelas"));
        obj.setNovoObj(new Boolean(false));
        if (nivelMontarDados == Uteis.NIVELMONTARDADOS_DADOSBASICOS) {
            return obj;
        }
        if (nivelMontarDados == Uteis.NIVELMONTARDADOS_DADOSENTIDADESPRINCIPAIS) {
            montarDadosContrato(obj, Uteis.NIVELMONTARDADOS_DADOSBASICOS,con);
            montarDadosResponsavelOperacao(obj, Uteis.NIVELMONTARDADOS_DADOSBASICOS, con);
            montarDadosProdutoTrancamento(obj, Uteis.NIVELMONTARDADOS_DADOSBASICOS, con);
            return obj;
        }
        if (nivelMontarDados == Uteis.NIVELMONTARDADOS_MINIMOS) {
            montarDadosContrato(obj, Uteis.NIVELMONTARDADOS_MINIMOS, con);
            montarDadosResponsavelOperacao(obj, Uteis.NIVELMONTARDADOS_MINIMOS, con);
            return obj;
        }
        montarDadosContrato(obj, Uteis.NIVELMONTARDADOS_TODOS,con);
        montarDadosResponsavelOperacao(obj, Uteis.NIVELMONTARDADOS_DADOSBASICOS, con);
        montarDadosProdutoTrancamento(obj, Uteis.NIVELMONTARDADOS_DADOSBASICOS, con);

        return obj;
    }

    /**
     * Operação responsável por montar os dados de um objeto da classe <code>PaisVO</code> relacionado ao objeto <code>CidadeVO</code>.
     * Faz uso da chave primária da classe <code>PaisVO</code> para realizar a consulta.
     * @param obj  Objeto no qual será montado os dados consultados.
     */
    public static void montarDadosContrato(TrancamentoContratoVO obj, int nivelMontarDados, Connection con) throws Exception {
        if (obj.getContratoVO().getCodigo().intValue() == 0) {
            obj.setContratoVO(new ContratoVO());
            return;
        }
        Contrato contratoDAO = new Contrato(con);
        obj.setContratoVO(contratoDAO.consultarPorChavePrimaria(obj.getContratoVO().getCodigo().intValue(), nivelMontarDados));
        contratoDAO = null;
    }

    /**
     * Operação responsável por montar os dados de um objeto da classe <code>PaisVO</code> relacionado ao objeto <code>CidadeVO</code>.
     * Faz uso da chave primária da classe <code>PaisVO</code> para realizar a consulta.
     * @param obj  Objeto no qual será montado os dados consultados.
     */
    public static void montarDadosProdutoTrancamento(TrancamentoContratoVO obj, int nivelMontarDados, Connection con) throws Exception {
        if (obj.getProdutoTrancamento().getCodigo().intValue() == 0) {
            obj.setProdutoTrancamento(new ProdutoVO());
            return;
        }
        Produto produtoDAO = new Produto(con);
        obj.setProdutoTrancamento(produtoDAO.consultarPorChavePrimaria(obj.getProdutoTrancamento().getCodigo(), nivelMontarDados));
        produtoDAO = null;
    }

    /**
     * Operação responsável por montar os dados de um objeto da classe <code>PaisVO</code> relacionado ao objeto <code>CidadeVO</code>.
     * Faz uso da chave primária da classe <code>PaisVO</code> para realizar a consulta.
     * @param obj  Objeto no qual será montado os dados consultados.
     */
    public static void montarDadosResponsavelOperacao(TrancamentoContratoVO obj, int nivelMontarDados, Connection con) throws Exception {
        if (obj.getResponsavelOperacao().getCodigo().intValue() == 0) {
            obj.setResponsavelOperacao(new UsuarioVO());
            return;
        }
        Usuario usuarioDAO = new Usuario(con);
        obj.setResponsavelOperacao(usuarioDAO.consultarPorChavePrimaria(obj.getResponsavelOperacao().getCodigo(), nivelMontarDados));
        usuarioDAO = null;
    }

    /**
     * Operação responsável por localizar um objeto da classe <code>TrancamentoContratoVO</code>
     * através de sua chave primária.
     * @exception Exception Caso haja problemas de conexão ou localização do objeto procurado.
     */
    public TrancamentoContratoVO consultarPorChavePrimaria(Integer codigoPrm, int nivelMontarDados) throws Exception {
        consultar(getIdEntidade(), false);
        String sql = "SELECT * FROM TrancamentoContrato WHERE codigo = ?";
        PreparedStatement sqlConsultar = con.prepareStatement(sql);
        sqlConsultar.setInt(1, codigoPrm.intValue());
        ResultSet tabelaResultado = sqlConsultar.executeQuery();
        if (!tabelaResultado.next()) {
            throw new ConsistirException("Dados Não Encontrados ( TrancamentoContrato ).");
        }
        return (montarDados(tabelaResultado, nivelMontarDados));
    }
    
      public void incluirCancelamentoRetornoTrancamentoVencido(TrancamentoContratoVO trancVO) throws Exception{
        ContratoOperacaoVO obj = new ContratoOperacaoVO();
        obj.setContrato(trancVO.getContratoVO().getCodigo());
        obj.setDataFimEfetivacaoOperacao(negocio.comuns.utilitarias.Calendario.hoje());
        obj.setDataInicioEfetivacaoOperacao(negocio.comuns.utilitarias.Calendario.hoje());
       
        obj.setDataOperacao(negocio.comuns.utilitarias.Calendario.hoje());
        obj.setDescricaoCalculo("Contrato cancelado por retorno de trancamento descontando dias, em que número de dias a descontar era maior que os dias congelados");
        obj.setObservacao(trancVO.getObservacao());
        obj.setOperacaoPaga(true);
        obj.setResponsavel(trancVO.getResponsavelOperacao());
        obj.setTipoOperacao("CA");
        getFacade().getContratoOperacao().incluirSemCommit(obj, true);
    }

    private void incluirHistoricoCancelamentoRetornoTrancamentoVencido(TrancamentoContratoVO trancVO) throws Exception {
        HistoricoContratoVO obj = new HistoricoContratoVO();
        obj.setContrato(trancVO.getContratoVO().getCodigo());
        obj.setDataFinalSituacao(negocio.comuns.utilitarias.Calendario.hoje());
        obj.setDataInicioSituacao(negocio.comuns.utilitarias.Calendario.hoje());
        obj.setDescricao("CANCELADO");
        obj.setDataRegistro(negocio.comuns.utilitarias.Calendario.hoje());
        obj.setTipoHistorico("CA");
        obj.setResponsavelRegistro(trancVO.getResponsavelOperacao());
        obj.setSituacaoRelativaHistorico("");

        getFacade().getHistoricoContrato().incluirSemCommit(obj, true);
    }

    public TrancamentoContratoVO obterUltimoTrancamento(Integer contrato, int nivelMontarDados) throws Exception {
        String sqlStr = "SELECT * FROM TrancamentoContrato WHERE  contrato = " + contrato.intValue() + " order by codigo desc limit 1";
        Statement stm = con.createStatement();
        ResultSet tabelaResultado = stm.executeQuery(sqlStr);
        if (!tabelaResultado.next()) {
            return null;
        }
        return (montarDados(tabelaResultado, nivelMontarDados));
    }

    public void alterarVencimentoParcelas(ContratoVO contratoVO, final int nrdias, final String operacao, Date dataTrancamento) throws Exception {
        MovParcela movParcelaDao = new MovParcela(con);
        movParcelaDao.alterarDiasVencimentoMovParcelas(contratoVO, nrdias , operacao , true, dataTrancamento);
        movParcelaDao =null;
    }
}

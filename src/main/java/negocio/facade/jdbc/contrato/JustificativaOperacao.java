package negocio.facade.jdbc.contrato;

import negocio.comuns.basico.EmpresaVO;
import negocio.comuns.contrato.JustificativaOperacaoVO;
import negocio.comuns.utilitarias.ConsistirException;
import negocio.comuns.utilitarias.Ordenacao;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisValidacao;
import negocio.facade.jdbc.arquitetura.SuperEntidade;
import negocio.facade.jdbc.basico.Empresa;
import negocio.interfaces.contrato.JustificativaOperacaoInterfaceFacade;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.sql.Statement;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

/**
 * Classe de persistência que encapsula todas as operações de manipulação dos dados da classe <code>JustificativaOperacaoVO</code>.
 * Responsável por implementar operações como incluir, alterar, excluir e consultar pertinentes a classe <code>JustificativaOperacaoVO</code>.
 * Encapsula toda a interação com o banco de dados.
 * @see JustificativaOperacaoVO
 * @see SuperEntidade
 */
public class JustificativaOperacao extends SuperEntidade implements JustificativaOperacaoInterfaceFacade {

    public JustificativaOperacao() throws Exception {
        super();
    }

    public JustificativaOperacao(Connection con) throws Exception {
        super(con);
    }

    /**
     * Operação responsável por retornar um novo objeto da classe <code>JustificativaOperacaoVO</code>.
     */
    public JustificativaOperacaoVO novo() throws Exception {
        incluir(getIdEntidade());
        return new JustificativaOperacaoVO();
    }

    public void incluir(JustificativaOperacaoVO obj) throws Exception {
        try {
            incluir(getIdEntidade());
            incluirSemPermissao(obj);
        } catch (Exception e) {
            obj.setNovoObj(true);
            throw e;
        }
    }

    /**
     * Operação responsável por incluir no banco de dados um objeto da classe <code>JustificativaOperacaoVO</code>.
     * Primeiramente valida os dados (<code>validarDados</code>) do objeto. Verifica a conexão com o banco de dados e a permissão do usuário
     * para realizar esta operacão na entidade.
     * Isto, através da operação <code>incluir</code> da superclasse.
     * @param obj  Objeto da classe <code>JustificativaOperacaoVO</code> que será gravado no banco de dados.
     * @exception Exception Caso haja problemas de conexão, restrição de acesso ou validação de dados.
     */
    public void incluirSemPermissao(JustificativaOperacaoVO obj) throws Exception {
        try {
            JustificativaOperacaoVO.validarDados(obj);
            //JustificativaOperacao.incluir(getIdEntidade());
            obj.realizarUpperCaseDados();
            String sql = "INSERT INTO JustificativaOperacao(empresa, tipoOperacao, descricao, isentarMultaCancelamento, necessarioAnexarComprovante, ativa, naoCobrarParcelasAtrasadasCancelamento)\n" +
                    "VALUES ( ?, ?, ?, ?, ?, ?, ? )";
            PreparedStatement sqlInserir = con.prepareStatement(sql);
            int i = 0;
            if (obj.getEmpresa().getCodigo() != 0) {
                sqlInserir.setInt(++i, obj.getEmpresa().getCodigo());
            } else {
                sqlInserir.setNull(++i, 0);
            }
            sqlInserir.setString(++i, obj.getTipoOperacao());
            sqlInserir.setString(++i, obj.getDescricao().trim());
            sqlInserir.setBoolean(++i, obj.getIsentarMultaCancelamento());
            sqlInserir.setBoolean(++i, obj.getNecessarioAnexarComprovante());
            sqlInserir.setBoolean(++i, obj.getAtiva());
            sqlInserir.setBoolean(++i, obj.getNaoCobrarParcelasAtrasadasCancelamento());

            sqlInserir.execute();
            obj.setCodigo(obterValorChavePrimariaCodigo());
            obj.setNovoObj(false);
        } catch (Exception e) {
            obj.setNovoObj(true);
            throw e;
        }
    }

    /**
     * Operação responsável por alterar no BD os dados de um objeto da classe <code>JustificativaOperacaoVO</code>.
     * Sempre utiliza a chave primária da classe como atributo para localização do registro a ser alterado.
     * Primeiramente valida os dados (<code>validarDados</code>) do objeto. Verifica a conexão com o banco de dados e a permissão do usuário
     * para realizar esta operacão na entidade.
     * Isto, através da operação <code>alterar</code> da superclasse.
     * @param obj    Objeto da classe <code>JustificativaOperacaoVO</code> que será alterada no banco de dados.
     * @exception Exception Caso haja problemas de conexão, restrição de acesso ou validação de dados.
     */
    public void alterar(JustificativaOperacaoVO obj) throws Exception {
        try {
            con.setAutoCommit(false);
            alterarSemCommit(obj);
            con.commit();
        } catch (Exception e) {
            con.rollback();
            con.setAutoCommit(true);
            throw e;
        } finally {
            con.setAutoCommit(true);
        }
    }

    public void alterarSemCommit(JustificativaOperacaoVO obj) throws Exception{
            JustificativaOperacaoVO.validarDados(obj);
            alterar(getIdEntidade());
            obj.realizarUpperCaseDados();
            String sql = "UPDATE JustificativaOperacao\n" +
                    "set empresa=?, tipoOperacao=?, descricao=?, isentarMultaCancelamento = ?, necessarioAnexarComprovante = ?,\n" +
                    "ativa = ?, naoCobrarParcelasAtrasadasCancelamento = ?\n" +
                    "WHERE codigo = ?";
            PreparedStatement sqlAlterar = con.prepareStatement(sql);
            int i = 0;
            if (obj.getEmpresa().getCodigo() != 0) {
                sqlAlterar.setInt(++i, obj.getEmpresa().getCodigo());
            } else {
                sqlAlterar.setNull(++i, 0);
            }
            sqlAlterar.setString(++i, obj.getTipoOperacao());
            sqlAlterar.setString(++i, obj.getDescricao().trim());
            sqlAlterar.setBoolean(++i, obj.getIsentarMultaCancelamento());
            sqlAlterar.setBoolean(++i, obj.getNecessarioAnexarComprovante());
            sqlAlterar.setBoolean(++i, obj.getAtiva());
            sqlAlterar.setBoolean(++i, obj.getNaoCobrarParcelasAtrasadasCancelamento());
            sqlAlterar.setInt(++i, obj.getCodigo());
            sqlAlterar.execute();
        }

    /**
     * Operação responsável por excluir no BD um objeto da classe <code>JustificativaOperacaoVO</code>.
     * Sempre localiza o registro a ser excluído através da chave primária da entidade.
     * Primeiramente verifica a conexão com o banco de dados e a permissão do usuário
     * para realizar esta operacão na entidade.
     * Isto, através da operação <code>excluir</code> da superclasse.
     * @param obj    Objeto da classe <code>JustificativaOperacaoVO</code> que será removido no banco de dados.
     * @exception Exception Caso haja problemas de conexão ou restrição de acesso.
     */
    public void excluir(JustificativaOperacaoVO obj) throws Exception {
        try {
            con.setAutoCommit(false);
            excluir(getIdEntidade());
            String sql = "DELETE FROM JustificativaOperacao WHERE ((codigo = ?))";
            PreparedStatement sqlExcluir = con.prepareStatement(sql);
            sqlExcluir.setInt(1, obj.getCodigo());
            sqlExcluir.execute();
            con.commit();
        } catch (Exception e) {
            con.rollback();
            con.setAutoCommit(true);
            throw e;
        } finally {
            con.setAutoCommit(true);
        }
    }

    /**
     * Responsável por realizar uma consulta de <code>JustificativaOperacao</code> através do valor do atributo 
     * <code>String descricao</code>. Retorna os objetos, com início do valor do atributo idêntico ao parâmetro fornecido.
     * Faz uso da operação <code>montarDadosConsulta</code> que realiza o trabalho de prerarar o List resultante.
     * @param   controlarAcesso Indica se a aplicação deverá verificar se o usuário possui permissão para esta consulta ou não.
     * @return  List Contendo vários objetos da classe <code>JustificativaOperacaoVO</code> resultantes da consulta.
     * @exception Exception Caso haja problemas de conexão ou restrição de acesso.
     */
    public List<JustificativaOperacaoVO> consultarPorDescricao(String valorConsulta, Integer empresa, boolean controlarAcesso, int nivelMontarDados) throws Exception {
        consultar(getIdEntidade(), controlarAcesso);
        String sqlStr = "";
        if (empresa == 0) {
            sqlStr = "SELECT * FROM JustificativaOperacao WHERE upper( descricao ) like('" + valorConsulta.toUpperCase() + "%') ORDER BY descricao";
        } else {
            sqlStr = "SELECT * FROM JustificativaOperacao WHERE upper( descricao ) like('" + valorConsulta.toUpperCase() + "%') and empresa = " + empresa + " ORDER BY descricao";
        }
        try (Statement stm = con.createStatement()) {
            try (ResultSet tabelaResultado = stm.executeQuery(sqlStr)) {
                return (montarDadosConsulta(tabelaResultado, nivelMontarDados, this.con));
            }
        }
    }

    public JustificativaOperacaoVO obterJustificativaCancelamentoTransferenciaEmpresa(int empresa, boolean transferenciaDiasContrato) throws Exception {
        String just;
        if(transferenciaDiasContrato){
            just = JustificativaOperacaoVO.CANCELAMENTO_TRANSFERENCIA_DIAS_CONTRATO;
        }else{
            just = JustificativaOperacaoVO.CANCELAMENTO_TRANSFERENCIA_EMPRESA;
        }

        String sql = "SELECT * " +
                "FROM JustificativaOperacao " +
                "WHERE upper( descricao ) like('"+just+"') " +
                "   AND empresa = "+empresa+
                " LIMIT 1";
        try (PreparedStatement ps = con.prepareStatement(sql)) {
            try (ResultSet rs = ps.executeQuery()) {
                if (rs.next()) {
                    return montarDados(rs, Uteis.NIVELMONTARDADOS_DADOSBASICOS, con);
                } else {
                    JustificativaOperacaoVO justificativa = new JustificativaOperacaoVO();
                    EmpresaVO empresaVO = new EmpresaVO();
                    empresaVO.setCodigo(empresa);
                    justificativa.setEmpresa(empresaVO);
                    justificativa.setTipoOperacao("CA");
                    if (transferenciaDiasContrato) {
                        justificativa.setDescricao(JustificativaOperacaoVO.CANCELAMENTO_TRANSFERENCIA_DIAS_CONTRATO);
                    } else {
                        justificativa.setDescricao(JustificativaOperacaoVO.CANCELAMENTO_TRANSFERENCIA_EMPRESA);
                    }
                    incluirSemPermissao(justificativa);

                    return justificativa;
                }
            }
        }
    }

    @Override
    public JustificativaOperacaoVO obterJustificativaCancelamentoMudancaPlano(int empresa) throws Exception {
        String sql = "SELECT * " +
                "FROM JustificativaOperacao " +
                "WHERE upper( descricao ) like('"+JustificativaOperacaoVO.CANCELAMENTO_MUDANCA_PLANO+"') " +
                "   AND empresa = "+empresa+
                " LIMIT 1";
        try (PreparedStatement ps = con.prepareStatement(sql)) {
            try (ResultSet rs = ps.executeQuery()) {
                if (rs.next()) {
                    return montarDados(rs, Uteis.NIVELMONTARDADOS_DADOSBASICOS, con);
                } else {
                    JustificativaOperacaoVO justificativa = new JustificativaOperacaoVO();
                    EmpresaVO empresaVO = new EmpresaVO();
                    empresaVO.setCodigo(empresa);
                    justificativa.setEmpresa(empresaVO);
                    justificativa.setTipoOperacao("CA");
                    justificativa.setDescricao(JustificativaOperacaoVO.CANCELAMENTO_MUDANCA_PLANO);
                    incluirSemPermissao(justificativa);

                    return justificativa;
                }
            }
        }
    }

    /**
     * Responsável por realizar uma consulta de <code>JustificativaOperacao</code> através do valor do atributo 
     * <code>nome</code> da classe <code>Empresa</code>
     * Faz uso da operação <code>montarDadosConsulta</code> que realiza o trabalho de prerarar o List resultante.
     * @return  List Contendo vários objetos da classe <code>JustificativaOperacaoVO</code> resultantes da consulta.
     * @exception Exception Caso haja problemas de conexão ou restrição de acesso.
     */
    public List<JustificativaOperacaoVO> consultarPorNomeEmpresa(String valorConsulta, Integer empresa, int nivelMontarDados) throws Exception {
        consultar(getIdEntidade(), true);
        String sqlStr = "SELECT JustificativaOperacao.* FROM JustificativaOperacao, Empresa WHERE JustificativaOperacao.empresa = Empresa.codigo and upper( Empresa.nome ) like('" + valorConsulta.toUpperCase() + "%') ORDER BY Empresa.nome";
        try (Statement stm = con.createStatement()) {
            try (ResultSet tabelaResultado = stm.executeQuery(sqlStr)) {
                return montarDadosConsulta(tabelaResultado, nivelMontarDados, this.con);
            }
        }
    }

    public JustificativaOperacaoVO consultarOperacaoRenovacaoAntecipadaCreditoTreino(Integer codigoEmpresa)throws Exception{
        return consultarOperacaoCreditoTreino(codigoEmpresa,JustificativaOperacaoVO.JUSTIFICATIVA_RENOVACAO_ANTECIPADA_CREDITO_TREINO);
    }
    
    public JustificativaOperacaoVO consultarOperacaoCreditoTreino(Integer codigoEmpresa, String justificativa)throws Exception{
        StringBuilder sql = new StringBuilder();
        sql.append("select * from JustificativaOperacao \n");
        sql.append(" where tipoOperacao = 'BO' and descricao = '").append(justificativa).append("' \n");
        sql.append(" and empresa = ").append(codigoEmpresa);
        JustificativaOperacaoVO justificativaOperacaoVO;
        try (Statement st = con.createStatement()) {
            try (ResultSet rs = st.executeQuery(sql.toString())) {
                justificativaOperacaoVO = null;
                if (rs.next()) {
                    justificativaOperacaoVO = montarDados(rs, Uteis.NIVELMONTARDADOS_DADOSBASICOS, con);
                } else {
                    justificativaOperacaoVO = new JustificativaOperacaoVO();
                    justificativaOperacaoVO.setDescricao(justificativa);
                    justificativaOperacaoVO.setTipoOperacao("BO");
                    justificativaOperacaoVO.setEmpresa(new EmpresaVO());
                    justificativaOperacaoVO.getEmpresa().setCodigo(codigoEmpresa);
                    incluirSemPermissao(justificativaOperacaoVO);
                }
            }
        }
        return justificativaOperacaoVO;

    }

    @Override
    public boolean isUtiliza(Integer empresa, Integer justificativaoperacao) throws Exception {
        StringBuilder sql = new StringBuilder();
        sql.append(" SELECT co.codigo ");
        sql.append(" FROM justificativaoperacao jo ");
        sql.append(" JOIN contratooperacao co ON co.tipojustificativa = jo.codigo ");
        sql.append(" WHERE jo.codigo = ").append(justificativaoperacao);
        sql.append(" AND   jo.empresa = ").append(empresa);
        sql.append(" LIMIT 1 ");

        return existe(sql.toString(), con);
    }

    @Override
    public ResultSet consultarTotalJustificativaContratoCanceladoDeOutraUnidade(Integer empresa) throws Exception {
        String sql = "select count(codigo) as qtd from justificativaoperacao where tipooperacao = 'CA' and descricao like 'CT.%.CA.%' ";
        if(empresa != 0){
            sql += " and empresa="+String.valueOf(empresa);
        }
        Statement stm = con.createStatement();
        return stm.executeQuery(sql);
    }

    @Override
    public ResultSet consultarJustificativaContratoCanceladoDeOutraUnidade(Integer empresa) throws Exception {
        String sql = "select descricao as mensagem from justificativaoperacao where tipooperacao = 'CA' and descricao like 'CT.%.CA.%' ";

        if (empresa != 0) {
            sql += " and empresa=" + String.valueOf(empresa);
        }

        Statement stm = con.createStatement();
        return stm.executeQuery(sql);
    }

    /**
     * Responsável por realizar uma consulta de <code>JustificativaOperacao</code> através do valor do atributo 
     * <code>Integer codigo</code>. Retorna os objetos com valores iguais ou superiores ao parâmetro fornecido.
     * Faz uso da operação <code>montarDadosConsulta</code> que realiza o trabalho de prerarar o List resultante.
     * @param   controlarAcesso Indica se a aplicação deverá verificar se o usuário possui permissão para esta consulta ou não.
     * @return  List Contendo vários objetos da classe <code>JustificativaOperacaoVO</code> resultantes da consulta.
     * @exception Exception Caso haja problemas de conexão ou restrição de acesso.
     */
    public List<JustificativaOperacaoVO> consultarPorCodigo(Integer valorConsulta, Integer empresa, boolean controlarAcesso, int nivelMontarDados) throws Exception {
        consultar(getIdEntidade(), controlarAcesso);
        String sqlStr;
        if (empresa == 0) {
            sqlStr = "SELECT * FROM JustificativaOperacao WHERE codigo >=" + valorConsulta + " ORDER BY codigo";
        } else {
            sqlStr = "SELECT * FROM JustificativaOperacao WHERE codigo >=" + valorConsulta + " and empresa = " + empresa + " ORDER BY codigo";
        }
        try (Statement stm = con.createStatement()) {
            try (ResultSet tabelaResultado = stm.executeQuery(sqlStr)) {
                return (montarDadosConsulta(tabelaResultado, nivelMontarDados, this.con));
            }
        }
    }

    public List consultarPorTipoOperacao(String valorConsulta, Integer empresa, boolean controlarAcesso, int nivelMontarDados) throws Exception {
        consultar(getIdEntidade(), controlarAcesso);
        String sqlStr;
        if (empresa == 0) {
            sqlStr = "SELECT * FROM JustificativaOperacao WHERE ativa AND tipoOperacao ='" + valorConsulta.toUpperCase() + "'  ORDER BY tipoOperacao";
        } else {
            sqlStr = "SELECT * FROM JustificativaOperacao WHERE ativa AND tipoOperacao ='" + valorConsulta.toUpperCase() + "' and coalesce(empresa, 0) IN (0," + empresa + ") ORDER BY tipoOperacao";
        }
        try (Statement stm = con.createStatement()) {
            try (ResultSet tabelaResultado = stm.executeQuery(sqlStr)) {
                return (montarDadosConsulta(tabelaResultado, nivelMontarDados, this.con));
            }
        }
    }

    /**
     * Responsável por montar os dados de vários objetos, resultantes de uma consulta ao banco de dados (<code>ResultSet</code>).
     * Faz uso da operação <code>montarDados</code> que realiza o trabalho para um objeto por vez.
     * @return  List Contendo vários objetos da classe <code>JustificativaOperacaoVO</code> resultantes da consulta.
     */
    public static List montarDadosConsulta(ResultSet tabelaResultado, int nivelMontarDados, Connection con) throws Exception {
        List vetResultado = new ArrayList();
        while (tabelaResultado.next()) {
            JustificativaOperacaoVO obj = montarDados(tabelaResultado, nivelMontarDados, con);
            vetResultado.add(obj);
        }
        tabelaResultado = null;
        return vetResultado;
    }

    /**
     * Responsável por montar os dados resultantes de uma consulta ao banco de dados (<code>ResultSet</code>)
     * em um objeto da classe <code>JustificativaOperacaoVO</code>.
     * @return  O objeto da classe <code>JustificativaOperacaoVO</code> com os dados devidamente montados.
     */
    public static JustificativaOperacaoVO montarDados(ResultSet dadosSQL, int nivelMontarDados, Connection con) throws Exception {
        JustificativaOperacaoVO obj = new JustificativaOperacaoVO();
        obj.setCodigo(dadosSQL.getInt("codigo"));
        obj.getEmpresa().setCodigo(dadosSQL.getInt("empresa"));
        if (UteisValidacao.emptyNumber(obj.getEmpresa().getCodigo())) {
            obj.setApresentarTodasEmpresas(true);
        }
        obj.setTipoOperacao(dadosSQL.getString("tipoOperacao"));
        obj.setDescricao(dadosSQL.getString("descricao"));
        obj.setIsentarMultaCancelamento(dadosSQL.getBoolean("isentarMultaCancelamento"));
        obj.setNecessarioAnexarComprovante(dadosSQL.getBoolean("necessarioAnexarComprovante"));

        obj.setNaoCobrarParcelasAtrasadasCancelamento(dadosSQL.getBoolean("naoCobrarParcelasAtrasadasCancelamento"));
        obj.setAtiva(dadosSQL.getBoolean("ativa"));

        obj.setNovoObj(false);
        if (nivelMontarDados == Uteis.NIVELMONTARDADOS_DADOSBASICOS) {
            return obj;
        }

        montarDadosEmpresa(obj, nivelMontarDados, con);
        return obj;
    }

    /**
     * Operação responsável por montar os dados de um objeto da classe <code>EmpresaVO</code> relacionado ao objeto <code>JustificativaOperacaoVO</code>.
     * Faz uso da chave primária da classe <code>EmpresaVO</code> para realizar a consulta.
     * @param obj  Objeto no qual será montado os dados consultados.
     */
    public static void montarDadosEmpresa(JustificativaOperacaoVO obj, int nivelMontarDados, Connection con) throws Exception {
        if (obj.getEmpresa().getCodigo() == 0) {
            obj.setEmpresa(new EmpresaVO());
            return;
        }
        Empresa empresa = new Empresa(con);
        obj.setEmpresa(empresa.consultarPorChavePrimaria(obj.getEmpresa().getCodigo(), nivelMontarDados));
        empresa = null;
    }

    /**
     * Operação responsável por localizar um objeto da classe <code>JustificativaOperacaoVO</code>
     * através de sua chave primária. 
     * @exception Exception Caso haja problemas de conexão ou localização do objeto procurado.
     */
    public JustificativaOperacaoVO consultarPorChavePrimaria(Integer codigoPrm, int nivelMontarDados) throws Exception {
        consultar(getIdEntidade(), false);
        String sql = "SELECT * FROM JustificativaOperacao WHERE codigo = ?";
        try (PreparedStatement sqlConsultar = con.prepareStatement(sql)) {
            sqlConsultar.setInt(1, codigoPrm);
            try (ResultSet tabelaResultado = sqlConsultar.executeQuery()) {
                if (!tabelaResultado.next()) {
                    throw new ConsistirException("Dados Não Encontrados ( JustificativaOperacao ).");
                }
                return (montarDados(tabelaResultado, nivelMontarDados, this.con));
            }
        }
    }

    /**
     * <AUTHOR>
     * 12/07/2011
     */
    public JustificativaOperacaoVO criarOuConsultarSeExistePorNome(JustificativaOperacaoVO obj) throws Exception {
        String sql = "SELECT * FROM JustificativaOperacao WHERE descricao LIKE '"
                + obj.getDescricao() + "%' and empresa = "
                + obj.getEmpresa().getCodigo();
        try (PreparedStatement sqlConsultar = con.prepareStatement(sql)) {
            try (ResultSet tabelaResultado = sqlConsultar.executeQuery()) {
                if (!tabelaResultado.next()) {
                    incluirSemPermissao(obj);
                    return obj;
                } else {
                    return (montarDados(tabelaResultado, Uteis.NIVELMONTARDADOS_DADOSBASICOS, this.con));
                }
            }
        }

    }

    public String consultarJSON(Integer empresa) throws Exception {
        StringBuilder json;
        boolean dados;
        try (ResultSet rs = getPS(empresa).executeQuery()) {
            JustificativaOperacaoVO justificativa = new JustificativaOperacaoVO();
            json = new StringBuilder();
            json.append("{\"aaData\":[");
            dados = false;
            while (rs.next()) {
                dados = true;
                json.append("[\"").append(rs.getString("codigo")).append("\",");
                json.append("\"").append(rs.getString("descricao").trim()).append("\",");
                json.append("\"").append(Uteis.normalizarStringJSON(rs.getString("empresa"))).append("\",");
                justificativa.setTipoOperacao(rs.getString("tipo"));
                json.append("\"").append(justificativa.getTipoOperacao_Apresentar()).append("\"],");
            }
        }
        if(dados) {
            json.deleteCharAt(json.toString().length() - 1);
        }
        json.append("]}");
        return json.toString();
    }

    private PreparedStatement getPS(Integer empresa) throws SQLException {
        StringBuilder sql = new StringBuilder();
        sql.append("SELECT jo.codigo, descricao, e.nome AS empresa, tipooperacao AS tipo \n");
        sql.append("FROM justificativaoperacao jo \n");
        sql.append("LEFT JOIN empresa e ON jo.empresa = e.codigo \n");
        sql.append("where jo.descricao <> '").append(JustificativaOperacaoVO.JUSTIFICATIVA_RENOVACAO_ANTECIPADA_CREDITO_TREINO).append("' \n");
        if (empresa != 0) {
            sql.append(" and coalesce(jo.empresa,0) IN (0, ").append(empresa).append(") \n");
        }
        sql.append("ORDER BY descricao");
        return con.prepareStatement(sql.toString());
    }
     public List<JustificativaOperacaoVO> consultarParaImpressao(String filtro, String ordem, String campoOrdenacao, Integer empresa) throws SQLException {
         List<JustificativaOperacaoVO> lista;
         try (ResultSet rs = getPS(empresa).executeQuery()) {
             lista = new ArrayList<>();
             while (rs.next()) {
                 JustificativaOperacaoVO justificativa = new JustificativaOperacaoVO();
                 String geral = rs.getString("codigo") + rs.getString("descricao") + rs.getString("empresa") + rs.getString("tipo");

                 String[] filtroTemp = filtro.split("\\s");
                 boolean contemFiltro = true;
                 for (String temp : filtroTemp) {
                     contemFiltro = contemFiltro && geral.toLowerCase().contains(temp);
                 }

                 if (contemFiltro) {
                     justificativa.setCodigo(rs.getInt("codigo"));
                     justificativa.setDescricao(rs.getString("descricao"));
                     justificativa.getEmpresa().setNome(rs.getString("empresa"));
                     justificativa.setTipoOperacao(rs.getString("tipo"));
                     lista.add(justificativa);
                 }
             }
         }
         switch (campoOrdenacao) {
             case "Código":
                 Ordenacao.ordenarLista(lista, "codigo");
                 break;
             case "Descrição":
                 Ordenacao.ordenarLista(lista, "descricao");
                 break;
             case "Empresa":
                 Ordenacao.ordenarLista(lista, "empresa_Apresentar");
                 break;
             case "Tipo Operação":
                 Ordenacao.ordenarLista(lista, "tipoOperacao_Apresentar");
                 break;
         }
        if (ordem.contains("desc")) {
            Collections.reverse(lista);
        }
        return lista;

    }
}

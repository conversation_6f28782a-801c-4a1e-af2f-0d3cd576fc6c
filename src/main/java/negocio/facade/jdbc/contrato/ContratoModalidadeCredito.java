package negocio.facade.jdbc.contrato;

import negocio.comuns.basico.AulaDesmarcadaVO;
import negocio.comuns.basico.ReposicaoVO;
import negocio.comuns.contrato.ContratoModalidadeCreditoVO;
import negocio.comuns.contrato.ContratoModalidadeVO;
import negocio.comuns.contrato.ControleCreditoTreinoVO;
import negocio.comuns.plano.HorarioTurmaVO;
import negocio.comuns.plano.ModalidadeVO;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisValidacao;
import negocio.facade.jdbc.arquitetura.SuperEntidade;
import negocio.interfaces.contrato.ContratoModalidadeCreditoInterfaceFacade;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.Statement;
import java.util.ArrayList;
import java.util.List;

public class ContratoModalidadeCredito extends SuperEntidade implements ContratoModalidadeCreditoInterfaceFacade {

    public ContratoModalidadeCredito() throws Exception {
        super();
        setIdEntidade("Contrato");
    }

    public ContratoModalidadeCredito(Connection conexao) throws Exception {
        super(conexao);
        setIdEntidade("Contrato");
    }

    @Override
    public void incluir(ContratoModalidadeCreditoVO obj) throws Exception {
        ContratoModalidadeCreditoVO.validarDados(obj);
        String sql = "INSERT INTO ContratoModalidadeCredito(contratoModalidade, qtdCreditoCompra, qtdCreditoDisponivel, valorUnitario, valorMensal, valorTotal) VALUES (?, ?, ?, ?, ?, ?)";
        PreparedStatement sqlInserir = con.prepareStatement(sql);
        int i = 0;
        resolveIntegerNull(sqlInserir, ++i, obj.getContratoModalidadeVO().getCodigo());
        sqlInserir.setInt(++i, obj.getQtdCreditoCompra());
        sqlInserir.setInt(++i, obj.getQtdCreditoDisponivel());
        sqlInserir.setDouble(++i, obj.getValorUnitario());
        sqlInserir.setDouble(++i, obj.getValorMensal());
        sqlInserir.setDouble(++i, obj.getValorTotal());
        sqlInserir.execute();
        obj.setCodigo(obterValorChavePrimariaCodigo());
        obj.setNovoObj(false);
    }

    @Override
    public void alterar(ContratoModalidadeCreditoVO obj) throws Exception {
        ContratoModalidadeCreditoVO.validarDados(obj);
        String sql = "UPDATE ContratoModalidadeCredito set contratoModalidade = ?, qtdCreditoCompra = ?, qtdCreditoDisponivel = ?, valorUnitario = ?, valorMensal = ?, valorTotal = ? WHERE codigo = ?";
        PreparedStatement sqlAlterar = con.prepareStatement(sql);
        int i = 0;
        resolveIntegerNull(sqlAlterar, ++i, obj.getContratoModalidadeVO().getCodigo());
        sqlAlterar.setInt(++i, obj.getQtdCreditoCompra());
        sqlAlterar.setInt(++i, obj.getQtdCreditoDisponivel());
        sqlAlterar.setDouble(++i, obj.getValorUnitario());
        sqlAlterar.setDouble(++i, obj.getValorMensal());
        sqlAlterar.setDouble(++i, obj.getValorTotal());
        sqlAlterar.setInt(++i, obj.getCodigo());
        sqlAlterar.execute();
    }

    @Override
    public void excluir(ContratoModalidadeCreditoVO obj) throws Exception {
        String sql = "DELETE FROM ContratoModalidadeCredito WHERE codigo = ?";
        PreparedStatement sqlExcluir = con.prepareStatement(sql);
        sqlExcluir.setInt(1, obj.getCodigo());
        sqlExcluir.execute();
    }

    public static List<ContratoModalidadeCreditoVO> montarDadosConsulta(ResultSet rs, int nivelMontarDados, Connection con) throws Exception {
        List<ContratoModalidadeCreditoVO> vetResultado = new ArrayList<ContratoModalidadeCreditoVO>();
        while (rs.next()) {
            vetResultado.add(montarDados(rs, nivelMontarDados, con));
        }
        return vetResultado;
    }

    private static ContratoModalidadeCreditoVO montarDadosBasico(ResultSet rs) throws Exception {
        ContratoModalidadeCreditoVO obj = new ContratoModalidadeCreditoVO();
        obj.setNovoObj(false);
        obj.setCodigo(rs.getInt("codigo"));
        obj.getContratoModalidadeVO().setCodigo(rs.getInt("contratoModalidade"));
        obj.setQtdCreditoCompra(rs.getInt("qtdCreditoCompra"));
        obj.setQtdCreditoDisponivel(rs.getInt("qtdCreditoDisponivel"));
        obj.setValorUnitario(rs.getDouble("valorUnitario"));
        obj.setValorMensal(rs.getDouble("valorMensal"));
        obj.setValorTotal(rs.getDouble("valorTotal"));
        return obj;
    }

    public static ContratoModalidadeCreditoVO montarDados(ResultSet dadosSQL, int nivelMontarDados, Connection con) throws Exception {
        ContratoModalidadeCreditoVO obj = montarDadosBasico(dadosSQL);
        if (nivelMontarDados == Uteis.NIVELMONTARDADOS_DADOSBASICOS) {
            return obj;
        }
        montarDadosContratoModalidade(obj, Uteis.NIVELMONTARDADOS_TODOS, con);
        return obj;
    }

    public static void montarDadosContratoModalidade(ContratoModalidadeCreditoVO obj, int nivelMontarDados, Connection con) throws Exception {
        if (UteisValidacao.emptyNumber(obj.getContratoModalidadeVO().getCodigo())) {
            obj.setContratoModalidadeVO(new ContratoModalidadeVO());
            return;
        }
        ContratoModalidade contratoModalidadeDAO = new ContratoModalidade(con);
        obj.setContratoModalidadeVO(contratoModalidadeDAO.consultarPorChavePrimaria(obj.getContratoModalidadeVO().getCodigo(), nivelMontarDados));
        contratoModalidadeDAO = null;
    }

    @Override
    public ContratoModalidadeCreditoVO consultarPorChavePrimaria(Integer codigo, int nivelMontarDados) throws Exception {
        String sql = "SELECT * FROM ContratoModalidadeCredito WHERE codigo = ?";
        PreparedStatement sqlConsultar = con.prepareStatement(sql);
        sqlConsultar.setInt(1, codigo);
        ResultSet tabelaResultado = sqlConsultar.executeQuery();
        if (!tabelaResultado.next()) {
            return new ContratoModalidadeCreditoVO();
        }
        return montarDados(tabelaResultado, nivelMontarDados, this.con);
    }

    @Override
    public ContratoModalidadeCreditoVO consultarPorContratoModalidade(Integer contratoModalidade, int nivelMontarDados) throws Exception {
        String sql = "SELECT * FROM ContratoModalidadeCredito WHERE ContratoModalidade = ?";
        PreparedStatement sqlConsultar = con.prepareStatement(sql);
        sqlConsultar.setInt(1, contratoModalidade);
        ResultSet tabelaResultado = sqlConsultar.executeQuery();
        if (!tabelaResultado.next()) {
            return new ContratoModalidadeCreditoVO();
        }
        return montarDados(tabelaResultado, nivelMontarDados, this.con);
    }

    @Override
    public void incluirContratoModalidadeCredito(ContratoModalidadeVO contratoModalidadeVO) {
        try {
            if (contratoVendaCreditoSessao(contratoModalidadeVO.getContrato())) {
                contratoModalidadeVO.getContratoModalidadeCredito().setContratoModalidadeVO(contratoModalidadeVO);
                incluir(contratoModalidadeVO.getContratoModalidadeCredito());
            }
        } catch (Exception ignored) {
        }
    }

    @Override
    public List<ContratoModalidadeCreditoVO> consultarPorCodigoContrato(Integer contrato, int nivelMontarDados) throws Exception {
        if (UteisValidacao.emptyNumber(contrato)) {
            return new ArrayList<ContratoModalidadeCreditoVO>();
        }
        StringBuilder sql = new StringBuilder();
        sql.append("select \n");
        sql.append("cc.* \n");
        sql.append("from contratomodalidadecredito cc \n");
        sql.append("inner join contratomodalidade cm on cm.codigo = cc.contratomodalidade \n");
        sql.append("where cm.contrato = " + contrato);
        Statement stm = con.createStatement();
        ResultSet rs = stm.executeQuery(sql.toString());
        return montarDadosConsulta(rs, nivelMontarDados, this.con);
    }

    @Override
    public boolean contratoVendaCreditoSessao(Integer contrato) {
        try {
            if (UteisValidacao.emptyNumber(contrato)) {
                return false;
            } else {
                String sqlStr = "SELECT vendacreditosessao FROM contrato where codigo = " + contrato;
                Statement stm = con.createStatement();
                ResultSet rs = stm.executeQuery(sqlStr);
                if (rs.next()) {
                    return rs.getBoolean("vendacreditosessao");
                } else {
                    return false;
                }
            }
        } catch (Exception ex) {
         return false;
        }
    }
}

package negocio.facade.jdbc.contrato;

import java.sql.Connection;
import java.util.Hashtable;
import negocio.comuns.contrato.ContratoModalidadeHorarioTurmaVO;
import negocio.interfaces.contrato.*;
import negocio.comuns.contrato.ContratoModalidadeTurmaVO;
import negocio.facade.jdbc.arquitetura.*;
import java.sql.ResultSet;
import java.sql.PreparedStatement;
import java.sql.Statement;
import java.util.List;
import java.util.ArrayList;
import java.util.Iterator;
import negocio.comuns.utilitarias.*;
import negocio.comuns.plano.TurmaVO;
import negocio.facade.jdbc.plano.Turma;

/**
 * Classe de persistência que encapsula todas as operações de manipulação dos dados da classe <code>ContratoModalidadeTurmaVO</code>.
 * Responsável por implementar operações como incluir, alterar, excluir e consultar pertinentes a classe <code>ContratoModalidadeTurmaVO</code>.
 * Encapsula toda a interação com o banco de dados.
 * @see ContratoModalidadeTurmaVO
 * @see SuperEntidade
 */
public class ContratoModalidadeTurma extends SuperEntidade implements ContratoModalidadeTurmaInterfaceFacade {
    
    private Hashtable contratoModalidadeHorarioTurmas = new Hashtable();
;

    public ContratoModalidadeTurma() throws Exception {
        super();
        setIdEntidade("Contrato");        
    }

    public ContratoModalidadeTurma(Connection conexao) throws Exception {
        super(conexao);
        setIdEntidade("Contrato");
    }

    /**
     * Operação responsável por retornar um novo objeto da classe <code>ContratoModalidadeTurmaVO</code>.
     */
    public ContratoModalidadeTurmaVO novo() throws Exception {
        incluir(getIdEntidade());
        ContratoModalidadeTurmaVO obj = new ContratoModalidadeTurmaVO();
        return obj;
    }

    /**
     * Operação responsável por incluir no banco de dados um objeto da classe <code>ContratoModalidadeTurmaVO</code>.
     * Primeiramente valida os dados (<code>validarDados</code>) do objeto. Verifica a conexão com o banco de dados e a permissão do usuário
     * para realizar esta operacão na entidade.
     * Isto, através da operação <code>incluir</code> da superclasse.
     * @param obj  Objeto da classe <code>ContratoModalidadeTurmaVO</code> que será gravado no banco de dados.
     * @exception Exception Caso haja problemas de conexão, restrição de acesso ou validação de dados.     */
    public void incluirContratoModalidadeTurma(ContratoModalidadeTurmaVO obj) throws Exception {
        ContratoModalidadeTurmaVO.validarDados(obj);
        incluir(getIdEntidade());
        obj.realizarUpperCaseDados();
        String sql = "INSERT INTO ContratoModalidadeTurma( contratoModalidade, turma ) VALUES ( ?, ? )";
        try (PreparedStatement sqlInserir = con.prepareStatement(sql)) {
            sqlInserir.setInt(1, obj.getContratoModalidade().intValue());
            if (obj.getTurma().getCodigo().intValue() != 0) {
                sqlInserir.setInt(2, obj.getTurma().getCodigo().intValue());
            } else {
                sqlInserir.setNull(2, 0);
            }
            sqlInserir.execute();
        }
        obj.setCodigo(obterValorChavePrimariaCodigo());
        obj.setNovoObj(new Boolean(false));
        
    }

    /**
     * Operação responsável por alterar no BD os dados de um objeto da classe <code>ContratoModalidadeTurmaVO</code>.
     * Sempre utiliza a chave primária da classe como atributo para localização do registro a ser alterado.
     * Primeiramente valida os dados (<code>validarDados</code>) do objeto. Verifica a conexão com o banco de dados e a permissão do usuário
     * para realizar esta operacão na entidade.
     * Isto, através da operação <code>alterar</code> da superclasse.
     * @param obj    Objeto da classe <code>ContratoModalidadeTurmaVO</code> que será alterada no banco de dados.
     * @exception Execption Caso haja problemas de conexão, restrição de acesso ou validação de dados.
     */
    public void alterar(ContratoModalidadeTurmaVO obj) throws Exception {
        ContratoModalidadeTurmaVO.validarDados(obj);
        alterar(getIdEntidade());
        obj.realizarUpperCaseDados();
        String sql = "UPDATE ContratoModalidadeTurma set contratoModalidade=?, turma=? WHERE ((codigo = ?))";
        try (PreparedStatement sqlAlterar = con.prepareStatement(sql)) {
            sqlAlterar.setInt(1, obj.getContratoModalidade().intValue());
            if (obj.getTurma().getCodigo().intValue() != 0) {
                sqlAlterar.setInt(2, obj.getTurma().getCodigo().intValue());
            } else {
                sqlAlterar.setNull(2, 0);
            }
            sqlAlterar.setInt(3, obj.getCodigo().intValue());
            sqlAlterar.execute();
        }
        getFacade().getContratoModalidadeHorarioTurma().alterarContratoModalidadeHorarioTurmas(obj.getCodigo(), obj.getContratoModalidadeHorarioTurmaVOs());
    }

    /**
     * Operação responsável por excluir no BD um objeto da classe <code>ContratoModalidadeTurmaVO</code>.
     * Sempre localiza o registro a ser excluído através da chave primária da entidade.
     * Primeiramente verifica a conexão com o banco de dados e a permissão do usuário
     * para realizar esta operacão na entidade.
     * Isto, através da operação <code>excluir</code> da superclasse.
     * @param obj    Objeto da classe <code>ContratoModalidadeTurmaVO</code> que será removido no banco de dados.
     * @exception Execption Caso haja problemas de conexão ou restrição de acesso.
     */
    public void excluir(ContratoModalidadeTurmaVO obj) throws Exception {
        excluir(getIdEntidade());
        String sql = "DELETE FROM ContratoModalidadeTurma WHERE ((codigo = ?))";
        try (PreparedStatement sqlExcluir = con.prepareStatement(sql)) {
            sqlExcluir.setInt(1, obj.getCodigo().intValue());
            sqlExcluir.execute();
        }
        getFacade().getContratoModalidadeHorarioTurma().excluirContratoModalidadeHorarioTurmas(obj.getCodigo());
    }

    /**
     * Responsável por realizar uma consulta de <code>ContratoModalidadeTurma</code> através do valor do atributo 
     * <code>codigo</code> da classe <code>ContratoModalidadeTurma</code>
     * Faz uso da operação <code>montarDadosConsulta</code> que realiza o trabalho de prerarar o List resultante.
     * @return  List Contendo vários objetos da classe <code>ContratoModalidadeTurmaVO</code> resultantes da consulta.
     * @exception Exception Caso haja problemas de conexão ou restrição de acesso.
     */
    public List consultarPorCodigoContratoModalidadeTurma(Integer valorConsulta, int nivelMontarDados) throws Exception {
        consultar(getIdEntidade(), true);
        String sqlStr = "SELECT ContratoModalidadeTurma.* FROM ContratoModalidadeTurma, ContratoModalidadeTurma WHERE ContratoModalidadeTurma.turma = ContratoModalidadeTurma.codigo and ContratoModalidadeTurma.codigo >= " + valorConsulta.intValue() + " ORDER BY ContratoModalidadeTurma.codigo";
        try (Statement stm = con.createStatement()) {
            try (ResultSet tabelaResultado = stm.executeQuery(sqlStr)) {
                return montarDadosConsulta(tabelaResultado, nivelMontarDados, this.con);
            }
        }
    }

    /**
     * Responsável por realizar uma consulta de <code>ContratoModalidadeTurma</code> através do valor do atributo 
     * <code>Integer contratoModalidade</code>. Retorna os objetos com valores iguais ou superiores ao parâmetro fornecido.
     * Faz uso da operação <code>montarDadosConsulta</code> que realiza o trabalho de prerarar o List resultante.
     * @param   controlarAcesso Indica se a aplicação deverá verificar se o usuário possui permissão para esta consulta ou não.
     * @return  List Contendo vários objetos da classe <code>ContratoModalidadeTurmaVO</code> resultantes da consulta.
     * @exception Exception Caso haja problemas de conexão ou restrição de acesso.
     */
    public List consultarPorContratoModalidade(Integer valorConsulta, boolean controlarAcesso, int nivelMontarDados) throws Exception {
        consultar(getIdEntidade(), controlarAcesso);
        String sqlStr = "SELECT * FROM ContratoModalidadeTurma WHERE contratoModalidade >= " + valorConsulta.intValue() + " ORDER BY contratoModalidade";
        try (Statement stm = con.createStatement()) {
            try (ResultSet tabelaResultado = stm.executeQuery(sqlStr)) {
                return (montarDadosConsulta(tabelaResultado, nivelMontarDados, this.con));
            }
        }
    }

    /**
     * Responsável por realizar uma consulta de <code>ContratoModalidadeTurma</code> através do valor do atributo 
     * <code>Integer codigo</code>. Retorna os objetos com valores iguais ou superiores ao parâmetro fornecido.
     * Faz uso da operação <code>montarDadosConsulta</code> que realiza o trabalho de prerarar o List resultante.
     * @param   controlarAcesso Indica se a aplicação deverá verificar se o usuário possui permissão para esta consulta ou não.
     * @return  List Contendo vários objetos da classe <code>ContratoModalidadeTurmaVO</code> resultantes da consulta.
     * @exception Exception Caso haja problemas de conexão ou restrição de acesso.
     */
    public List consultarPorCodigo(Integer valorConsulta, boolean controlarAcesso, int nivelMontarDados) throws Exception {
        consultar(getIdEntidade(), controlarAcesso);
        String sqlStr = "SELECT * FROM ContratoModalidadeTurma WHERE codigo >= " + valorConsulta.intValue() + " ORDER BY codigo";
        try (Statement stm = con.createStatement()) {
            try (ResultSet tabelaResultado = stm.executeQuery(sqlStr)) {
                return (montarDadosConsulta(tabelaResultado, nivelMontarDados, this.con));
            }
        }
    }

    /**
     * Responsável por montar os dados de vários objetos, resultantes de uma consulta ao banco de dados (<code>ResultSet</code>).
     * Faz uso da operação <code>montarDados</code> que realiza o trabalho para um objeto por vez.
     * @return  List Contendo vários objetos da classe <code>ContratoModalidadeTurmaVO</code> resultantes da consulta.
     */
    public static List montarDadosConsulta(ResultSet tabelaResultado, int nivelMontarDados, Connection con) throws Exception {
        List vetResultado = new ArrayList();
        while (tabelaResultado.next()) {
            ContratoModalidadeTurmaVO obj = new ContratoModalidadeTurmaVO();
            obj = montarDados(tabelaResultado, nivelMontarDados, con);
            vetResultado.add(obj);
        }
        return vetResultado;
    }

    private static ContratoModalidadeTurmaVO montarDadosBasico(ResultSet dadosSQL) throws Exception {
        ContratoModalidadeTurmaVO obj = new ContratoModalidadeTurmaVO();
        obj.setNovoObj(false);
        obj.setCodigo(new Integer(dadosSQL.getInt("codigo")));
        obj.setContratoModalidade(new Integer(dadosSQL.getInt("contratoModalidade")));
        obj.getTurma().setCodigo(new Integer(dadosSQL.getInt("turma")));

        return obj;
    }

    /**
     * Responsável por montar os dados resultantes de uma consulta ao banco de dados (<code>ResultSet</code>)
     * em um objeto da classe <code>ContratoModalidadeTurmaVO</code>.
     * @return  O objeto da classe <code>ContratoModalidadeTurmaVO</code> com os dados devidamente montados.
     */
    public static ContratoModalidadeTurmaVO montarDados(ResultSet dadosSQL, int nivelMontarDados, Connection con) throws Exception {

        ContratoModalidadeTurmaVO obj = montarDadosBasico(dadosSQL);

        if (nivelMontarDados == Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA)
            return obj;

        if (nivelMontarDados == Uteis.NIVELMONTARDADOS_DADOSBASICOS) {
            return obj;
        }
        montarDadosContratoModalidadeHorarioTurma(obj, Uteis.NIVELMONTARDADOS_TODOS, con);
        if (nivelMontarDados == Uteis.NIVELMONTARDADOS_DADOSENTIDADESUBORDINADAS) {
            return obj;
        }
        montarDadosTurma(obj, Uteis.NIVELMONTARDADOS_DADOSBASICOS, con);
        return obj;
    }

    private static void montarDadosContratoModalidadeHorarioTurma(ContratoModalidadeTurmaVO obj, int nivelMontarDados, Connection con) throws Exception {
        if (obj == null || obj.getCodigo() == 0) {
            obj.setContratoModalidadeHorarioTurmaVOs(new ArrayList<ContratoModalidadeHorarioTurmaVO>());
        }
        ContratoModalidadeHorarioTurma contratoModalidadeHorarioTurma = new ContratoModalidadeHorarioTurma(con);
        obj.setContratoModalidadeHorarioTurmaVOs(contratoModalidadeHorarioTurma.consultarContratoModalidadeHorarioTurmas(obj.getCodigo(), nivelMontarDados));
        contratoModalidadeHorarioTurma = null;
    }

    /**
     * Operação responsável por montar os dados de um objeto da classe <code>ContratoModalidadeTurmaVO</code> relacionado ao objeto <code>ContratoModalidadeTurmaVO</code>.
     * Faz uso da chave primária da classe <code>ContratoModalidadeTurmaVO</code> para realizar a consulta.
     * @param obj  Objeto no qual será montado os dados consultados.
     */
    public static void montarDadosTurma(ContratoModalidadeTurmaVO obj, int nivelMontarDados, Connection con) throws Exception {
        if (obj.getTurma().getCodigo() == 0) {
            obj.setTurma(new TurmaVO());
            return;
        }
        Turma turma = new Turma(con);
        obj.setTurma(turma.consultarPorChavePrimaria(obj.getTurma().getCodigo(), nivelMontarDados));
        obj.getTurma().setTurmaEscolhida(true);
    }

    /**
     * Operação responsável por excluir todos os objetos da <code>ContratoModalidadeVO</code> no BD.
     * Faz uso da operação <code>excluir</code> disponível na classe <code>ContratoModalidade</code>.
     * @param <code>contrato</code> campo chave para exclusão dos objetos no BD.
     * @exception Exception  Erro de conexão com o BD ou restrição de acesso a esta operação.
     */
    public void excluirContratoModalidadeTurma(Integer contratoModalidade) throws Exception {
        excluir(getIdEntidade());
        String sql = "DELETE FROM ContratoModalidadeTurma WHERE (contratoModalidade = ?)";
        try (PreparedStatement sqlExcluir = con.prepareStatement(sql)) {
            sqlExcluir.setInt(1, contratoModalidade.intValue());
            sqlExcluir.execute();
        }
    }

    /**
     * Operação responsável por alterar todos os objetos da <code>ContratoModalidadeVO</code> contidos em um Hashtable no BD.
     * Faz uso da operação <code>excluirContratoModalidades</code> e <code>incluirContratoModalidades</code> disponíveis na classe <code>ContratoModalidade</code>.
     * @param objetos  List com os objetos a serem alterados ou incluídos no BD.
     * @exception Exception  Erro de conexão com o BD ou restrição de acesso a esta operação.
     */
    public void alterarContratoModalidadeTurma(Integer contratoModalidade, List objetos) throws Exception {
        try {
            obterTurmasSelecionadas(objetos);
            String str = "DELETE FROM ContratoModalidadeTurma WHERE contratoModalidade = " + contratoModalidade.intValue();
            Iterator j = objetos.iterator();
            while (j.hasNext()) {
                ContratoModalidadeTurmaVO objeto = (ContratoModalidadeTurmaVO) j.next();
                if (objeto.getTurma().getTurmaEscolhida() && objeto.getCodigo().intValue() != 0) {
                    str += " AND codigo <> " + objeto.getCodigo().intValue();
                    obterHorarioTurmaSelecionado(objeto);
                }
            }
            try (PreparedStatement sqlExcluir = con.prepareStatement(str)) {
                sqlExcluir.execute();
            }
            Iterator e = objetos.iterator();
            while (e.hasNext()) {
                ContratoModalidadeTurmaVO objeto = (ContratoModalidadeTurmaVO) e.next();
                if (objeto.getTurma().getTurmaEscolhida()) {
                    if (objeto.getCodigo().equals(new Integer(0))) {
                        objeto.setContratoModalidade(contratoModalidade);
                        getFacade().getZWFacade().incluirContratoModalidadeTurma(objeto);
                    } else {
                        alterar(objeto);
                    }
                }
            }
        } catch (Exception e) {
            throw e;
        }


    }

    public void obterTurmasSelecionadas(List objetos) {
        Boolean existeTurmaMarcada = false;
        Iterator i = objetos.iterator();
        while (i.hasNext()) {
            ContratoModalidadeTurmaVO objeto = (ContratoModalidadeTurmaVO) i.next();
            Iterator k = objeto.getContratoModalidadeHorarioTurmaVOs().iterator();
            existeTurmaMarcada = false;
            while (k.hasNext()) {
                ContratoModalidadeHorarioTurmaVO cmht = (ContratoModalidadeHorarioTurmaVO) k.next();
                if (cmht.getHorarioTurma().getHorarioTurmaEscolhida()) {
                    objeto.getTurma().setTurmaEscolhida(true);
                    existeTurmaMarcada = true;
                    break;
                }
            }
            if (!existeTurmaMarcada) {
                objeto.getTurma().setTurmaEscolhida(false);
            }
        }
    }

    public void obterHorarioTurmaSelecionado(ContratoModalidadeTurmaVO objeto) throws Exception {
        try {
            Boolean existeTurma = false;
            String sqlCmht = "DELETE FROM ContratoModalidadeHorarioTurma WHERE ContratoModalidadeTurma = " + objeto.getCodigo();
            Iterator k = objeto.getContratoModalidadeHorarioTurmaVOs().iterator();
            while (k.hasNext()) {
                ContratoModalidadeHorarioTurmaVO cmht = (ContratoModalidadeHorarioTurmaVO) k.next();
                if (cmht.getHorarioTurma().getHorarioTurmaEscolhida()) {
                    sqlCmht += " AND codigo <> " + cmht.getCodigo().intValue();
                    existeTurma = true;
                }
            }
            if (existeTurma) {
                try (PreparedStatement sql = con.prepareStatement(sqlCmht)) {
                    sql.execute();
                }
            }
        } catch (Exception e) {
            throw e;
        }
    }


    /**
     * Operação responsável por consultar todos os <code>ContratoModalidadeVO</code> relacionados a um objeto da classe <code>contrato.Contrato</code>.
     * @param contrato  Atributo de <code>contrato.Contrato</code> a ser utilizado para localizar os objetos da classe <code>ContratoModalidadeVO</code>.
     * @return List  Contendo todos os objetos da classe <code>ContratoModalidadeVO</code> resultantes da consulta.
     * @exception Exception  Erro de conexão com o BD ou restrição de acesso a esta operação.
     */
    public List consultarContratoModalidadeTurmas(Integer contratoModalidade, int nivelMontarDados) throws Exception {
        consultar(getIdEntidade());
        List objetos = new ArrayList();
        String sql = "SELECT * FROM ContratoModalidadeTurma WHERE contratoModalidade = ?";
        try (PreparedStatement sqlConsulta = con.prepareStatement(sql)) {
            sqlConsulta.setInt(1, contratoModalidade);
            try (ResultSet resultado = sqlConsulta.executeQuery()) {
                while (resultado.next()) {
                    ContratoModalidadeTurmaVO novoObj = ContratoModalidadeTurma.montarDados(resultado, nivelMontarDados, this.con);
                    objetos.add(novoObj);
                }
            }
        }
        Ordenacao.ordenarLista(objetos, "nomeTurma_Apresentar");
        return objetos;
    }

    public List<ContratoModalidadeTurmaVO> consultar(Integer codigoContrato, int nivelMontarDados)throws Exception{
        StringBuilder sql = new StringBuilder();
        sql.append("SELECT cmt.* \n");
        sql.append("FROM contratomodalidadeturma cmt \n");
        sql.append("INNER JOIN contratomodalidade cm ON cmt.contratomodalidade = cm.codigo \n");
        sql.append("WHERE cm.contrato = ").append(codigoContrato);
        try (Statement st = con.createStatement()) {
            try (ResultSet rs = st.executeQuery(sql.toString())) {
                return montarDadosConsulta(rs, nivelMontarDados, con);
            }
        }
    }

    /**
     * Operação responsável por adicionar um objeto da <code>ContratoModalidadeHorarioTurmaVO</code> no Hashtable <code>ContratoModalidadeHorarioTurmas</code>.
     * Neste Hashtable são mantidos todos os objetos de ContratoModalidadeHorarioTurma de uma determinada ContratoModalidadeTurma.
     * @param obj  Objeto a ser adicionado no Hashtable.
     */
    public void adicionarObjContratoModalidadeHorarioTurmas(ContratoModalidadeHorarioTurmaVO obj) throws Exception {
        getContratoModalidadeHorarioTurmas().put(obj.getHorarioTurma() + "", obj);
        //adicionarObjSubordinadoOC
    }

    /**
     * Operação responsável por remover um objeto da classe <code>ContratoModalidadeHorarioTurmaVO</code> do Hashtable <code>ContratoModalidadeHorarioTurmas</code>.
     * Neste Hashtable são mantidos todos os objetos de ContratoModalidadeHorarioTurma de uma determinada ContratoModalidadeTurma.
     * @param HorarioTurma Atributo da classe <code>ContratoModalidadeHorarioTurmaVO</code> utilizado como apelido (key) no Hashtable.
     */
    public void excluirObjContratoModalidadeHorarioTurmas(Integer HorarioTurma) throws Exception {
        getContratoModalidadeHorarioTurmas().remove(HorarioTurma + "");
        //excluirObjSubordinadoOC
    }

    /**
     * Operação responsável por localizar um objeto da classe <code>ContratoModalidadeTurmaVO</code>
     * através de sua chave primária. 
     * @exception Exception Caso haja problemas de conexão ou localização do objeto procurado.
     */
    public ContratoModalidadeTurmaVO consultarPorChavePrimaria(Integer codigoPrm, int nivelMontarDados) throws Exception {
        consultar(getIdEntidade(), false);
        String sql = "SELECT * FROM ContratoModalidadeTurma WHERE codigo = ?";
        try (PreparedStatement sqlConsultar = con.prepareStatement(sql)) {
            sqlConsultar.setInt(1, codigoPrm.intValue());
            try (ResultSet tabelaResultado = sqlConsultar.executeQuery()) {
                if (!tabelaResultado.next()) {
                    throw new ConsistirException("Dados Não Encontrados ( ContratoModalidadeTurma ).");
                }
                return (montarDados(tabelaResultado, nivelMontarDados, this.con));
            }
        }
    }

    

    public Hashtable getContratoModalidadeHorarioTurmas() {
        return (contratoModalidadeHorarioTurmas);
    }

    public void setContratoModalidadeHorarioTurmas(Hashtable contratoModalidadeHorarioTurmas) {
        this.contratoModalidadeHorarioTurmas = contratoModalidadeHorarioTurmas;
    }

    public Boolean existeContratoModalidadeTurma(Integer codigoTurma) throws Exception {
        consultar(getIdEntidade(), false);
        String sql = "SELECT * FROM ContratoModalidadeTurma WHERE turma = ?";
        try (PreparedStatement sqlConsultar = con.prepareStatement(sql)) {
            sqlConsultar.setInt(1, codigoTurma);
            try (ResultSet tabelaResultado = sqlConsultar.executeQuery()) {
                if (!tabelaResultado.next()) {
                    return false;
                }
            }
        }
        return true;
    }
}

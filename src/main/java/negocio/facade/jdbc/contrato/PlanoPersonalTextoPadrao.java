package negocio.facade.jdbc.contrato;

import negocio.comuns.contrato.PlanoPersonalTextoPadraoVO;
import negocio.comuns.plano.PlanoTextoPadraoVO;
import negocio.comuns.utilitarias.ConsistirException;
import negocio.comuns.utilitarias.Uteis;
import negocio.facade.jdbc.arquitetura.SuperEntidade;
import negocio.facade.jdbc.plano.PlanoTextoPadrao;
import negocio.interfaces.contrato.PlanoPersonalTextoPadraoInterfaceFacade;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.Statement;
import java.util.ArrayList;
import java.util.List;

public class PlanoPersonalTextoPadrao extends SuperEntidade implements PlanoPersonalTextoPadraoInterfaceFacade {

    public PlanoPersonalTextoPadrao() throws Exception {
        super();
        setIdEntidade("Contrato");
    }
    public PlanoPersonalTextoPadrao(Connection conexao) throws Exception {
        super(conexao);
        setIdEntidade("Contrato");
    }

    public PlanoPersonalTextoPadraoVO novo() throws Exception {
        incluir(getIdEntidade());
        PlanoPersonalTextoPadraoVO obj = new PlanoPersonalTextoPadraoVO();
        return obj;
    }

    public void incluir(PlanoPersonalTextoPadraoVO obj) throws Exception {
        try {
            PlanoPersonalTextoPadraoVO.validarDados(obj);
            incluir(getIdEntidade());
            String sql = "INSERT INTO planopersonaltextopadrao ( taxapersonal , planotextopadrao ) VALUES ( ?, ? )";
            PreparedStatement sqlInserir = con.prepareStatement(sql);
            sqlInserir.setInt(1, obj.getCodigoTaxaPersonal().intValue());
            if (obj.getPlanoTextoPadrao().getCodigo().intValue() != 0) {
                sqlInserir.setInt(2, obj.getPlanoTextoPadrao().getCodigo().intValue());
            } else {
                sqlInserir.setNull(2, 0);
            }
            sqlInserir.execute();
            obj.setCodigo(obterValorChavePrimariaCodigo());
            obj.setNovoObj(new Boolean(false));
        } catch (Exception e) {
            throw e;
        }
    }

    public void alterar(PlanoPersonalTextoPadraoVO obj) throws Exception {
        try {
            PlanoPersonalTextoPadraoVO.validarDados(obj);
            alterar(getIdEntidade());
            String sql = "UPDATE planopersonaltextopadrao set taxapersonal=?, planotextopadrao=? WHERE ((codigo = ?))";
            PreparedStatement sqlAlterar = con.prepareStatement(sql);
            sqlAlterar.setInt(1, obj.getCodigoTaxaPersonal().intValue());
            if (obj.getPlanoTextoPadrao().getCodigo().intValue() != 0) {
                sqlAlterar.setInt(2, obj.getPlanoTextoPadrao().getCodigo().intValue());
            } else {
                sqlAlterar.setNull(2, 0);
            }
            sqlAlterar.setInt(3, obj.getCodigo().intValue());
            sqlAlterar.execute();
        } catch (Exception e) {
            throw e;
        }
    }

    public void excluir(PlanoPersonalTextoPadraoVO obj) throws Exception {
        try {
            excluir(getIdEntidade());
            String sql = "DELETE FROM planopersonaltextopadrao WHERE ((codigo = ?))";
            PreparedStatement sqlExcluir = con.prepareStatement(sql);
            sqlExcluir.setInt(1, obj.getCodigo().intValue());
            sqlExcluir.execute();

        } catch (Exception e) {
            throw e;
        }
    }

    public PlanoPersonalTextoPadraoVO consultarPorChavePrimaria(Integer codigo, int nivelMontarDados) throws Exception {
        consultar(getIdEntidade(), false);
        String sql = "SELECT * FROM planopersonaltextopadrao WHERE codigo = ?";
        PreparedStatement sqlConsultar = con.prepareStatement(sql);
        sqlConsultar.setInt(1, codigo.intValue());
        ResultSet tabelaResultado = sqlConsultar.executeQuery();
        if (!tabelaResultado.next()) {
            throw new ConsistirException("Dados Não Encontrados ( PlanoPersonalTextoPadrao ).");
        }
        return (montarDados(tabelaResultado, nivelMontarDados, this.con));
    }

    public List consultarPorCodigo(Integer valorConsulta, boolean controlarAcesso, int nivelMontarDados) throws Exception {
        consultar(getIdEntidade(), controlarAcesso);
        String sqlStr = "SELECT * FROM planopersonaltextopadrao WHERE codigo >= " + valorConsulta.intValue() + " ORDER BY codigo";
        Statement stm = con.createStatement();
        ResultSet tabelaResultado = stm.executeQuery(sqlStr);
        return (montarDadosConsulta(tabelaResultado, nivelMontarDados, this.con));
    }

    public PlanoPersonalTextoPadraoVO consultarPlanoPersonalTextoPadrao(Integer taxapersonal, int nivelMontarDados) throws Exception {
        consultar(getIdEntidade());
        List objetos = new ArrayList();
        String sql = "SELECT * FROM planopersonaltextopadrao WHERE taxapersonal = ? ORDER BY codigo LIMIT 1";
        PreparedStatement sqlConsulta = con.prepareStatement(sql);
        sqlConsulta.setInt(1, taxapersonal.intValue());
        ResultSet resultado = sqlConsulta.executeQuery();
        if (!resultado.next()) {
            throw new ConsistirException("Dados Não Encontrados ( PlanoPersonalTextoPadrao ).");
        }
        return (montarDados(resultado, nivelMontarDados, this.con));
    }

    public PlanoPersonalTextoPadraoVO consultarPorCodigoPlanoPersonal(Integer codigoPrm, int nivelMontarDados) throws Exception {
        consultar(getIdEntidade(), false);
        String sql = "SELECT * FROM planopersonaltextopadrao WHERE taxapersonal = ?";
        PreparedStatement sqlConsultar = con.prepareStatement(sql);
        sqlConsultar.setInt(1, codigoPrm.intValue());
        ResultSet tabelaResultado = sqlConsultar.executeQuery();
        if (!tabelaResultado.next()) {
            throw new ConsistirException("Dados Não Encontrados ( PlanoPersonalTextoPadrao ).");
        }
        return (montarDados(tabelaResultado, nivelMontarDados, this.con));
    }

    public static List montarDadosConsulta(ResultSet tabelaResultado, int nivelMontarDados, Connection con) throws Exception {
        List vetResultado = new ArrayList();
        while (tabelaResultado.next()) {
            PlanoPersonalTextoPadraoVO obj = new PlanoPersonalTextoPadraoVO();
            obj = montarDados(tabelaResultado, nivelMontarDados, con);
            vetResultado.add(obj);
        }
        return vetResultado;
    }

    public static PlanoPersonalTextoPadraoVO montarDados(ResultSet dadosSQL, int nivelMontarDados, Connection con) throws Exception {
        PlanoPersonalTextoPadraoVO obj = new PlanoPersonalTextoPadraoVO();
        obj.setCodigo(new Integer(dadosSQL.getInt("codigo")));
        obj.getPlanoTextoPadrao().setCodigo(new Integer(dadosSQL.getInt("planoTextoPadrao")));
        obj.setCodigoTaxaPersonal(new Integer(dadosSQL.getInt("taxapersonal")));
        obj.setNovoObj(new Boolean(false));
        if (nivelMontarDados == Uteis.NIVELMONTARDADOS_DADOSBASICOS) {
            return obj;
        }
        montarDadosPlanoTextoPadrao(obj, nivelMontarDados, con);
        return obj;
    }

    public static void montarDadosPlanoTextoPadrao(PlanoPersonalTextoPadraoVO obj, int nivelMontarDados, Connection con) throws Exception {
        if (obj.getPlanoTextoPadrao().getCodigo().intValue() == 0) {
            obj.setPlanoTextoPadrao(new PlanoTextoPadraoVO());
            return;
        }
        PlanoTextoPadrao planoTextoPadrao = new PlanoTextoPadrao(con);
        obj.setPlanoTextoPadrao(planoTextoPadrao.consultarPorChavePrimaria(obj.getPlanoTextoPadrao().getCodigo(), nivelMontarDados));
        planoTextoPadrao = null;
    }

}

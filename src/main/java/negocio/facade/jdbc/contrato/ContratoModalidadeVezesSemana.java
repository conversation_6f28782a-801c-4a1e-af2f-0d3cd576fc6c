package negocio.facade.jdbc.contrato;

import negocio.comuns.contrato.ContratoModalidadeVO;
import negocio.facade.jdbc.arquitetura.*;

import java.sql.Connection;
import java.sql.ResultSet;
import java.sql.PreparedStatement;
import java.sql.Statement;
import java.util.List;
import java.util.ArrayList;
import negocio.comuns.contrato.ContratoModalidadeVezesSemanaVO;
import negocio.comuns.utilitarias.*;

/**
 * Classe de persistência que encapsula todas as operações de manipulação dos dados da classe <code>ContratoModalidadeVezesSemanaVO</code>.
 * Responsável por implementar operações como incluir, alterar, excluir e consultar pertinentes a classe <code>ContratoModalidadeVezesSemanaVO</code>.
 * Encapsula toda a interação com o banco de dados.
 * @see ContratoModalidadeVezesSemanaVO
 * @see SuperEntidade
 */
public class ContratoModalidadeVezesSemana extends SuperEntidade {    

    public ContratoModalidadeVezesSemana() throws Exception {
        super();
        setIdEntidade("Contrato");
    }

    public ContratoModalidadeVezesSemana(Connection conexao) throws Exception {
    	super(conexao);
        setIdEntidade("Contrato");
	}

	/**
     * Operação responsável por retornar um novo objeto da classe <code>ContratoModalidadeVezesSemanaVO</code>.
     */
    public ContratoModalidadeVezesSemanaVO novo() throws Exception {
        incluir(getIdEntidade());
        ContratoModalidadeVezesSemanaVO obj = new ContratoModalidadeVezesSemanaVO();
        return obj;
    }

    /**
     * Operação responsável por incluir no banco de dados um objeto da classe <code>ContratoModalidadeVezesSemanaVO</code>.
     * Primeiramente valida os dados (<code>validarDados</code>) do objeto. Verifica a conexão com o banco de dados e a permissão do usuário
     * para realizar esta operacão na entidade.
     * Isto, através da operação <code>incluir</code> da superclasse.
     * @param obj  Objeto da classe <code>ContratoModalidadeVezesSemanaVO</code> que será gravado no banco de dados.
     * @exception Exception Caso haja problemas de conexão, restrição de acesso ou validação de dados.
     */
    public void incluir(ContratoModalidadeVezesSemanaVO obj) throws Exception {
        ContratoModalidadeVezesSemanaVO.validarDados(obj);
        obj.realizarUpperCaseDados();
        String sql = "INSERT INTO ContratoModalidadeVezesSemana( nrVezes, contratoModalidade, percentualDesconto, valorEspecifico,  tipoValor, tipoOperacao ) VALUES ( ?, ?, ?, ?, ?, ?) RETURNING codigo";
        try (PreparedStatement sqlInserir = con.prepareStatement(sql)) {
            sqlInserir.setInt(1, obj.getNrVezes());
            sqlInserir.setInt(2, obj.getContratoModalidade());
            sqlInserir.setDouble(3, obj.getPercentualDesconto());
            sqlInserir.setDouble(4, obj.getValorEspecifico());
            sqlInserir.setString(5, obj.getTipoValor());
            sqlInserir.setString(6, obj.getTipoOperacao());
            try (ResultSet rs = sqlInserir.executeQuery()) {
                if (rs.next()) {
                    obj.setCodigo(rs.getInt("codigo"));
                    obj.setNovoObj(false);
                }
            }
        }
    }

    /**
     * Operação responsável por alterar no BD os dados de um objeto da classe <code>ContratoModalidadeVezesSemanaVO</code>.
     * Sempre utiliza a chave primária da classe como atributo para localização do registro a ser alterado.
     * Primeiramente valida os dados (<code>validarDados</code>) do objeto. Verifica a conexão com o banco de dados e a permissão do usuário
     * para realizar esta operacão na entidade.
     * Isto, através da operação <code>alterar</code> da superclasse.
     * @param obj    Objeto da classe <code>ContratoModalidadeVezesSemanaVO</code> que será alterada no banco de dados.
     * @exception Exception Caso haja problemas de conexão, restrição de acesso ou validação de dados.
     */
    public void alterar(ContratoModalidadeVezesSemanaVO obj) throws Exception {
        ContratoModalidadeVezesSemanaVO.validarDados(obj);
        alterar(getIdEntidade());
        obj.realizarUpperCaseDados();
        String sql = "UPDATE ContratoModalidadeVezesSemana set nrVezes=?, contratoModalidade=?, percentualDesconto=?, valorEspecifico=?, tipoValor=?, tipoOperacao=? WHERE ((codigo = ?))";
        PreparedStatement sqlAlterar = con.prepareStatement(sql);
        sqlAlterar.setInt(1, obj.getNrVezes().intValue());
        sqlAlterar.setInt(2, obj.getContratoModalidade().intValue());
        sqlAlterar.setDouble(3, obj.getPercentualDesconto().doubleValue());
        sqlAlterar.setDouble(4, obj.getValorEspecifico().doubleValue());
        sqlAlterar.setString(5, obj.getTipoValor());
        sqlAlterar.setString(6, obj.getTipoOperacao());
        sqlAlterar.setInt(7, obj.getCodigo().intValue());
        sqlAlterar.execute();
    }

    /**
     * Operação responsável por excluir no BD um objeto da classe <code>ContratoModalidadeVezesSemanaVO</code>.
     * Sempre localiza o registro a ser excluído através da chave primária da entidade.
     * Primeiramente verifica a conexão com o banco de dados e a permissão do usuário
     * para realizar esta operacão na entidade.
     * Isto, através da operação <code>excluir</code> da superclasse.
     * @param obj    Objeto da classe <code>ContratoModalidadeVezesSemanaVO</code> que será removido no banco de dados.
     * @exception Exception Caso haja problemas de conexão ou restrição de acesso.
     */
    public void excluir(ContratoModalidadeVezesSemanaVO obj) throws Exception {
        excluir(getIdEntidade());
        String sql = "DELETE FROM ContratoModalidadeVezesSemana WHERE ((codigo = ?))";
        PreparedStatement sqlExcluir = con.prepareStatement(sql);
        sqlExcluir.setInt(1, obj.getCodigo().intValue());
        sqlExcluir.execute();
    }

    /**
     * Operação responsável por excluir no BD um objeto da classe <code>ContratoModalidadeVezesSemanaVO</code>.
     * Sempre localiza o registro a ser excluído através da chave primária da entidade.
     * Primeiramente verifica a conexão com o banco de dados e a permissão do usuário
     * para realizar esta operacão na entidade.
     * Isto, através da operação <code>excluir</code> da superclasse.
     * @param obj    Objeto da classe <code>ContratoModalidadeVezesSemanaVO</code> que será removido no banco de dados.
     * @exception Exception Caso haja problemas de conexão ou restrição de acesso.
     */
    public void excluir(Integer codigoContratoModalidade) throws Exception {
        excluir(getIdEntidade());
        String sql = "DELETE FROM ContratoModalidadeVezesSemana WHERE ((contratoModalidade = ?))";
        PreparedStatement sqlExcluir = con.prepareStatement(sql);
        sqlExcluir.setInt(1, codigoContratoModalidade.intValue());
        sqlExcluir.execute();
    }

    /**
     * Responsável por realizar uma consulta de <code>ContratoModalidadeVezesSemana</code> através do valor do atributo
     * <code>Integer contratoModalidade</code>. Retorna os objetos com valores iguais ou superiores ao parâmetro fornecido.
     * Faz uso da operação <code>montarDadosConsulta</code> que realiza o trabalho de prerarar o List resultante.
     * @param   controlarAcesso Indica se a aplicação deverá verificar se o usuário possui permissão para esta consulta ou não.
     * @return  List Contendo vários objetos da classe <code>ContratoModalidadeVezesSemanaVO</code> resultantes da consulta.
     * @exception Exception Caso haja problemas de conexão ou restrição de acesso.
     */
    public List consultarPorContratoModalidade(Integer valorConsulta, boolean controlarAcesso, int nivelMontarDados) throws Exception {
        consultar(getIdEntidade(), controlarAcesso);
        String sqlStr = "SELECT * FROM ContratoModalidadeVezesSemana WHERE contratoModalidade >= " + valorConsulta.intValue() + " ORDER BY contratoModalidade";
        Statement stm = con.createStatement();
        ResultSet tabelaResultado = stm.executeQuery(sqlStr);
        return (montarDadosConsulta(tabelaResultado, nivelMontarDados));
    }

    /**
     * Responsável por realizar uma consulta de <code>ContratoModalidadeVezesSemana</code> através do valor do atributo
     * <code>nrVezes</code> da classe <code>VezesSemana</code>
     * Faz uso da operação <code>montarDadosConsulta</code> que realiza o trabalho de prerarar o List resultante.
     * @return  List Contendo vários objetos da classe <code>ContratoModalidadeVezesSemanaVO</code> resultantes da consulta.
     * @exception Exception Caso haja problemas de conexão ou restrição de acesso.
     */
    public List consultarPorNrVezesVezesSemana(Integer valorConsulta, int nivelMontarDados) throws Exception {
        consultar(getIdEntidade(), true);
        String sqlStr = "SELECT ContratoModalidadeVezesSemana.* FROM ContratoModalidadeVezesSemana, VezesSemana WHERE ContratoModalidadeVezesSemana.vezesSemana = VezesSemana.codigo and VezesSemana.nrVezes >= " + valorConsulta.intValue() + " ORDER BY VezesSemana.nrVezes";
        Statement stm = con.createStatement();
        ResultSet tabelaResultado = stm.executeQuery(sqlStr);
        return montarDadosConsulta(tabelaResultado, nivelMontarDados);
    }

    /**
     * Operação responsável por localizar um objeto da classe <code>ContratoModalidadeVezesSemanaVO</code>
     * através de sua chave primária.
     * @exception Exception Caso haja problemas de conexão ou localização do objeto procurado.
     */
    public ContratoModalidadeVezesSemanaVO consultarPorNrVezeSemana(ContratoModalidadeVO cmVo, int nivelMontarDados) throws Exception {
        consultar(getIdEntidade(), false);
        String sql = "SELECT ContratoModalidadeVezesSemana.* FROM ContratoModalidadeVezesSemana WHERE contratomodalidade = ? and ContratoModalidadeVezesSemana.nrVezes = ?";
        PreparedStatement sqlConsultar = con.prepareStatement(sql);
        sqlConsultar.setInt(1, cmVo.getCodigo().intValue());
        sqlConsultar.setInt(2, cmVo.getNrVezesSemana().intValue());
        ResultSet tabelaResultado = sqlConsultar.executeQuery();
        if (!tabelaResultado.next()) {
            return new ContratoModalidadeVezesSemanaVO();
        }
        return (montarDados(tabelaResultado, nivelMontarDados));
    }

    /**
     * Responsável por realizar uma consulta de <code>ContratoModalidadeVezesSemana</code> através do valor do atributo
     * <code>Integer codigo</code>. Retorna os objetos com valores iguais ou superiores ao parâmetro fornecido.
     * Faz uso da operação <code>montarDadosConsulta</code> que realiza o trabalho de prerarar o List resultante.
     * @param   controlarAcesso Indica se a aplicação deverá verificar se o usuário possui permissão para esta consulta ou não.
     * @return  List Contendo vários objetos da classe <code>ContratoModalidadeVezesSemanaVO</code> resultantes da consulta.
     * @exception Exception Caso haja problemas de conexão ou restrição de acesso.
     */
    public List consultarPorCodigo(Integer valorConsulta, boolean controlarAcesso, int nivelMontarDados) throws Exception {
        consultar(getIdEntidade(), controlarAcesso);
        String sqlStr = "SELECT * FROM ContratoModalidadeVezesSemana WHERE codigo >= " + valorConsulta.intValue() + " ORDER BY codigo";
        Statement stm = con.createStatement();
        ResultSet tabelaResultado = stm.executeQuery(sqlStr);
        return (montarDadosConsulta(tabelaResultado, nivelMontarDados));
    }

    /**
     * Responsável por montar os dados de vários objetos, resultantes de uma consulta ao banco de dados (<code>ResultSet</code>).
     * Faz uso da operação <code>montarDados</code> que realiza o trabalho para um objeto por vez.
     * @return  List Contendo vários objetos da classe <code>ContratoModalidadeVezesSemanaVO</code> resultantes da consulta.
     */
    public static List montarDadosConsulta(ResultSet tabelaResultado, int nivelMontarDados) throws Exception {
        List vetResultado = new ArrayList();
        while (tabelaResultado.next()) {
            ContratoModalidadeVezesSemanaVO obj = new ContratoModalidadeVezesSemanaVO();
            obj = montarDados(tabelaResultado, nivelMontarDados);
            vetResultado.add(obj);
        }
        return vetResultado;
    }

    /**
     * Responsável por montar os dados resultantes de uma consulta ao banco de dados (<code>ResultSet</code>)
     * em um objeto da classe <code>ContratoModalidadeVezesSemanaVO</code>.
     * @return  O objeto da classe <code>ContratoModalidadeVezesSemanaVO</code> com os dados devidamente montados.
     */
    public static ContratoModalidadeVezesSemanaVO montarDados(ResultSet dadosSQL, int nivelMontarDados) throws Exception {
        ContratoModalidadeVezesSemanaVO obj = new ContratoModalidadeVezesSemanaVO();
        obj.setCodigo(new Integer(dadosSQL.getInt("codigo")));
        obj.setNrVezes(new Integer(dadosSQL.getInt("nrVezes")));
        obj.setContratoModalidade(new Integer(dadosSQL.getInt("contratoModalidade")));
        obj.setTipoValor(dadosSQL.getString("tipoValor"));
        obj.setTipoOperacao(dadosSQL.getString("tipoOperacao"));
        obj.setPercentualDesconto(new Double(dadosSQL.getDouble("percentualDesconto")));
        obj.setValorEspecifico(new Double(dadosSQL.getDouble("valorEspecifico")));
        obj.setNovoObj(new Boolean(false));
        if (nivelMontarDados == Uteis.NIVELMONTARDADOS_DADOSBASICOS) {
            return obj;
        }
        return obj;
    }

    /**
     * Operação responsável por excluir todos os objetos da <code>HorarioTurmaVO</code> no BD.
     * Faz uso da operação <code>excluir</code> disponível na classe <code>HorarioTurma</code>.
     * @param <code>turma</code> campo chave para exclusão dos objetos no BD.
     * @exception Exception  Erro de conexão com o BD ou restrição de acesso a esta operação.
     */
    public void excluirContratoModalidadeVezesSemana(Integer contratoModalidadePrm) throws Exception {
        excluir(getIdEntidade());
        String sql = "DELETE FROM ContratoModalidadeVezesSemana WHERE ( contratoModalidade= ?)";
        PreparedStatement sqlExcluir = con.prepareStatement(sql);
        sqlExcluir.setInt(1, contratoModalidadePrm.intValue());
        sqlExcluir.execute();
    }

    /**
     * Operação responsável por alterar todos os objetos da <code>HorarioTurmaVO</code> contidos em um Hashtable no BD.
     * Faz uso da operação <code>excluirHorarioTurmas</code> e <code>incluirHorarioTurmas</code> disponíveis na classe <code>HorarioTurma</code>.
     * @param objetos  List com os objetos a serem alterados ou incluídos no BD.
     * @exception Exception  Erro de conexão com o BD ou restrição de acesso a esta operação.
     */
    public void alterarContratoModalidadeVezesSemana(Integer contratoModalidade, ContratoModalidadeVezesSemanaVO obj) throws Exception {
        String str = "DELETE FROM ContratoModalidadeVezesSemana WHERE contratoModalidade = " + contratoModalidade.intValue();
        PreparedStatement sqlExcluir = con.prepareStatement(str);
        sqlExcluir.execute();
        if (obj.getCodigo().equals(new Integer(0))) {
            obj.setContratoModalidade(contratoModalidade);
            incluir(obj);
        } else {
            alterar(obj);
        }

    }

    /**
     * Operação responsável por incluir objetos da <code>HorarioTurmaVO</code> no BD.
     * Garantindo o relacionamento com a entidade principal <code>plano.Turma</code> através do atributo de vínculo.
     * @param objetos List contendo os objetos a serem gravados no BD da classe.
     * @exception Exception  Erro de conexão com o BD ou restrição de acesso a esta operação.
     */
    public void incluirContratoModalidadeVezesSemana(Integer contratoModalidadePrm, ContratoModalidadeVezesSemanaVO obj) throws Exception {
        obj.setContratoModalidade(contratoModalidadePrm);
        incluir(obj);
    }

    /**
     * Operação responsável por consultar todos os <code>ContratoDuracaoVO</code> relacionados a um objeto da classe <code>plano.Contrato</code>.
     * @param plano  Atributo de <code>plano.Contrato</code> a ser utilizado para localizar os objetos da classe <code>ContratoDuracaoVO</code>.
     * @return List  Contendo todos os objetos da classe <code>ContratoDuracaoVO</code> resultantes da consulta.
     * @exception Exception  Erro de conexão com o BD ou restrição de acesso a esta operação.
     */
    public List consultarContratoVezesSemanaVOs(Integer contratoModalidade, int nivelMontarDados) throws Exception {
        consultar(getIdEntidade());
        List objetos = new ArrayList();
        String sql = "SELECT * FROM ContratoModalidadeVezesSemana WHERE contratoModalidade = ?";
        PreparedStatement sqlConsulta = con.prepareStatement(sql);
        sqlConsulta.setInt(1, contratoModalidade.intValue());
        ResultSet resultado = sqlConsulta.executeQuery();
        while (resultado.next()) {
            ContratoModalidadeVezesSemanaVO novoObj = new ContratoModalidadeVezesSemanaVO();
            novoObj = ContratoModalidadeVezesSemana.montarDados(resultado, nivelMontarDados);
            objetos.add(novoObj);
        }
        return objetos;
    }

    /**
     * Operação responsável por localizar um objeto da classe <code>ContratoModalidadeVezesSemanaVO</code>
     * através de sua chave primária.
     * @exception Exception Caso haja problemas de conexão ou localização do objeto procurado.
     */
    public ContratoModalidadeVezesSemanaVO consultarPorChavePrimaria(Integer codigoPrm, int nivelMontarDados) throws Exception {
        consultar(getIdEntidade(), false);
        String sql = "SELECT * FROM ContratoModalidadeVezesSemana WHERE codigo = ?";
        PreparedStatement sqlConsultar = con.prepareStatement(sql);
        sqlConsultar.setInt(1, codigoPrm.intValue());
        ResultSet tabelaResultado = sqlConsultar.executeQuery();
        if (!tabelaResultado.next()) {
            throw new ConsistirException("Dados Não Encontrados ( ContratoModalidadeVezesSemana ).");
        }
        return (montarDados(tabelaResultado, nivelMontarDados));
    }

}

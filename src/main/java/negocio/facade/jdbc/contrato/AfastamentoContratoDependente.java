package negocio.facade.jdbc.contrato;

import negocio.comuns.basico.ContratoDependenteVO;
import negocio.comuns.contrato.AfastamentoContratoDependenteVO;
import negocio.comuns.contrato.ContratoOperacaoVO;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisValidacao;
import negocio.facade.jdbc.arquitetura.SuperEntidade;
import negocio.facade.jdbc.basico.ContratoDependente;
import negocio.interfaces.contrato.AfastamentoContratoDependenteInterfaceFacade;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * Classe de persistência que encapsula todas as operações de manipulação dos dados da classe <code>ContratoOperacaoVO</code>.
 * Responsável por implementar operações como incluir, alterar, excluir e consultar pertinentes a classe <code>ContratoOperacaoVO</code>.
 * Encapsula toda a interação com o banco de dados.
 *
 * @see ContratoOperacaoVO
 * @see SuperEntidade
 */
public class AfastamentoContratoDependente extends SuperEntidade implements AfastamentoContratoDependenteInterfaceFacade {

    protected static String idEntidade;

    public AfastamentoContratoDependente() throws Exception {
        super();
        setIdEntidade("Contrato");
    }

    public AfastamentoContratoDependente(Connection con) throws Exception {
        super(con);
        setIdEntidade("Contrato");
    }

    public static List<AfastamentoContratoDependenteVO> montarDadosConsulta(ResultSet tabelaResultado, int nivelMontarDados, Connection con) throws Exception {
        List<AfastamentoContratoDependenteVO> listResultado = new ArrayList<>();
        while (tabelaResultado.next()) {
            AfastamentoContratoDependenteVO obj = montarDados(tabelaResultado, nivelMontarDados, con);
            listResultado.add(obj);
        }
        return listResultado;
    }

    public static AfastamentoContratoDependenteVO montarDados(ResultSet dadosSQL, int nivelMontarDados, Connection con) throws Exception {
        AfastamentoContratoDependenteVO obj = new AfastamentoContratoDependenteVO();
        obj.setCodigo(dadosSQL.getInt("codigo"));
        obj.setDataRegistro(dadosSQL.getTimestamp("dataregistro"));
        obj.setDataInicio(dadosSQL.getDate("inicioafastamento"));
        obj.setDataTermino(dadosSQL.getDate("finalafastamento"));
        obj.getContratoDependenteVO().setCodigo(dadosSQL.getInt("contratodependente"));
        obj.setObservacao(dadosSQL.getString("observacao"));
        obj.setTipoAfastamento(dadosSQL.getString("tipoafastamento"));
        obj.setJustificativa(dadosSQL.getInt("justificativaoperacao"));
        obj.setNrDiasSomar(dadosSQL.getInt("nrdiassomar"));

        obj.setNovoObj(false);
        if (nivelMontarDados == Uteis.NIVELMONTARDADOS_DADOSBASICOS) {
            return obj;
        }
        if (nivelMontarDados == Uteis.NIVELMONTARDADOS_TODOS) {
            montarDadosContratoDependente(obj, con);
            return obj;
        }
        return obj;
    }

    private static void montarDadosContratoDependente(AfastamentoContratoDependenteVO obj, Connection con) throws Exception {
        if (obj.getContratoDependenteVO().getCodigo() == 0) {
            obj.setContratoDependenteVO(new ContratoDependenteVO());
            return;
        }
        ContratoDependente contratoDependenteDAO = new ContratoDependente(con);
        ContratoDependenteVO contratoDependenteVO = contratoDependenteDAO.findByCodigo(obj.getContratoDependenteVO().getCodigo())
                .orElse(null);
        obj.setContratoDependenteVO(contratoDependenteVO);
        contratoDependenteDAO = null;
    }

    public void incluirSemCommit(AfastamentoContratoDependenteVO obj) throws Exception {
        String sql = "INSERT INTO afastamentocontratodependente(contratodependente, dataregistro, inicioafastamento, finalafastamento, nrdiasSomar, observacao, tipoafastamento, justificativaoperacao) "
                + "VALUES (?, ?, ?, ?, ?, ?, ?, ?)";

        try (PreparedStatement sqlInserir = con.prepareStatement(sql)) {
            int i = 0;
            sqlInserir.setInt(++i, obj.getContratoDependenteVO().getCodigo());
            sqlInserir.setTimestamp(++i, Uteis.getDataJDBCTimestamp(obj.getDataRegistro()));
            sqlInserir.setDate(++i, Uteis.getDataJDBC(obj.getDataInicio()));
            sqlInserir.setDate(++i, Uteis.getDataJDBC(obj.getDataTermino()));
            sqlInserir.setInt(++i, obj.getNrDiasSomar());
            sqlInserir.setString(++i, obj.getObservacao());
            sqlInserir.setString(++i, obj.getTipoAfastamento());
            sqlInserir.setInt(++i, obj.getJustificativa());
            sqlInserir.execute();
        }
        obj.setCodigo(obterValorChavePrimariaCodigo());
        obj.setNovoObj(false);
    }


    public void alterarSemCommit(AfastamentoContratoDependenteVO obj) throws Exception {
//        try {
//            ContratoOperacaoVO.validarDados(obj, false);
//            if (controleAcesso) {
//                alterar(getIdEntidade());
//            }
//            obj.realizarUpperCaseDados();
//            String sql = "UPDATE ContratoOperacao set contrato=?, tipoOperacao=?, operacaoPaga=?, dataOperacao=?, dataInicioEfetivacaoOperacao=?, dataFimEfetivacaoOperacao=?, responsavel=?, observacao=?, descricaoCalculo=?, tipoJustificativa=?, responsavelLiberacao=?, clienteTransfereDias = ?,clienteRecebeDias=?,nrdiasoperacao=?, informacoes = ?, chavearquivo = ?, origemSistema = ?, informacoesDesfazer = ? WHERE ((codigo = ?))";
//            try (PreparedStatement sqlAlterar = con.prepareStatement(sql)) {
//                sqlAlterar.setInt(1, obj.getContrato());
//                sqlAlterar.setString(2, obj.getTipoOperacao());
//                sqlAlterar.setBoolean(3, obj.isOperacaoPaga());
//                sqlAlterar.setDate(4, Uteis.getDataJDBC(obj.getDataOperacao()));
//                sqlAlterar.setDate(5, Uteis.getDataJDBC(obj.getDataInicioEfetivacaoOperacao()));
//                sqlAlterar.setDate(6, Uteis.getDataJDBC(obj.getDataFimEfetivacaoOperacao()));
//                if (obj.getResponsavel().getCodigo() != 0) {
//                    sqlAlterar.setInt(7, obj.getResponsavel().getCodigo());
//                } else {
//                    sqlAlterar.setNull(7, 0);
//                }
//                sqlAlterar.setString(8, obj.getObservacao());
//                sqlAlterar.setString(9, obj.getDescricaoCalculo());
//                if (obj.getTipoJustificativa().getCodigo() != 0) {
//                    sqlAlterar.setInt(10, obj.getTipoJustificativa().getCodigo());
//                } else {
//                    sqlAlterar.setNull(10, 0);
//                }
//                if (obj.getResponsavelLiberacao().getCodigo() != 0) {
//                    sqlAlterar.setInt(11, obj.getResponsavelLiberacao().getCodigo());
//                } else {
//                    sqlAlterar.setNull(11, 0);
//                }
//                if (obj.getClienteTransfereDias().getCodigo() != 0) {
//                    sqlAlterar.setInt(12, obj.getClienteTransfereDias().getCodigo());
//                } else {
//                    sqlAlterar.setNull(12, 0);
//                }
//                if (obj.getClienteRecebeDias().getCodigo() != 0) {
//                    sqlAlterar.setInt(13, obj.getClienteRecebeDias().getCodigo());
//                } else {
//                    sqlAlterar.setNull(13, 0);
//                }
//                if (obj.getNrDiasOperacao() != 0) {
//                    sqlAlterar.setInt(14, obj.getNrDiasOperacao());
//                } else {
//                    sqlAlterar.setNull(14, 0);
//                }
//                sqlAlterar.setString(15, obj.getInformacoes());
//                sqlAlterar.setString(16, obj.getChaveArquivo());
//                sqlAlterar.setInt(17, obj.getOrigemSistema().getCodigo());
//                sqlAlterar.setString(18, obj.getInformacoesDesfazer());
//                sqlAlterar.setInt(19, obj.getCodigo());
//                sqlAlterar.execute();
//            }
//
//        } catch (Exception e) {
//            throw e;
//        }
    }


    public void excluir(AfastamentoContratoDependenteVO obj) throws Exception {
        String sql = "DELETE FROM afastamentocontratodependente WHERE codigo = ?";
        try (PreparedStatement sqlExcluir = con.prepareStatement(sql)) {
            sqlExcluir.setInt(1, obj.getCodigo());
            sqlExcluir.execute();
        }
    }

    public AfastamentoContratoDependenteVO consultarPorChavePrimaria(Integer codigoPrm, int nivelMontarDados) throws Exception {
        String sql = "SELECT * FROM afastamentocontratodependente WHERE codigo = ?";
        try (PreparedStatement sqlConsultar = con.prepareStatement(sql)) {
            sqlConsultar.setInt(1, codigoPrm);
            try (ResultSet tabelaResultado = sqlConsultar.executeQuery()) {
                if (!tabelaResultado.next()) {
                    throw new Exception("Dados Não Encontrados ( afastamentocontratodependente ).");
                }
                return (montarDados(tabelaResultado, nivelMontarDados, this.con));
            }
        }
    }

    public List<AfastamentoContratoDependenteVO> consultarPorContratoDependente(Integer codigoContratoDependente, String tipoAfastamento, int nivelMontarDados) throws Exception {
        StringBuilder sbSql = new StringBuilder();
        sbSql.append("SELECT * FROM afastamentocontratodependente\n")
                .append("WHERE contratodependente = ?\n");
        if (!UteisValidacao.emptyString(tipoAfastamento)) {
            sbSql.append("AND tipoafastamento = ?");
        }

        try (PreparedStatement sqlConsultar = con.prepareStatement(sbSql.toString())) {
            sqlConsultar.setInt(1, codigoContratoDependente);
            if (!UteisValidacao.emptyString(tipoAfastamento)) {
                sqlConsultar.setString(2, tipoAfastamento);
            }
            try (ResultSet tabelaResultado = sqlConsultar.executeQuery()) {
                return montarDadosConsulta(tabelaResultado, nivelMontarDados, this.con);
            }
        }
    }

    public List<AfastamentoContratoDependenteVO> consultarPorContratoDependente(AfastamentoContratoDependenteVO afastamentoVO) throws Exception {
        String sbSql = "SELECT * FROM afastamentocontratodependente\n" +
                "WHERE contratodependente = ?\n" +
                "AND (inicioafastamento BETWEEN ? AND ?\n" +
                " OR finalafastamento BETWEEN ? AND ?)";

        try (PreparedStatement sqlConsultar = con.prepareStatement(sbSql)) {
            sqlConsultar.setInt(1, afastamentoVO.getContratoDependenteVO().getCodigo());
            sqlConsultar.setDate(2, Uteis.getDataJDBC(afastamentoVO.getDataInicio()));
            sqlConsultar.setDate(3, Uteis.getDataJDBC(afastamentoVO.getDataTermino()));
            sqlConsultar.setDate(4, Uteis.getDataJDBC(afastamentoVO.getDataInicio()));
            sqlConsultar.setDate(5, Uteis.getDataJDBC(afastamentoVO.getDataTermino()));

            try (ResultSet tabelaResultado = sqlConsultar.executeQuery()) {
                return montarDadosConsulta(tabelaResultado, Uteis.NIVELMONTARDADOS_DADOSBASICOS, this.con);
            }
        }
    }

    public boolean existeAfastamentoParaData(Integer codCliente, Date dataAvaliar) throws Exception {
        return existeAfastamentoParaData(codCliente, dataAvaliar, "");
    }

    public boolean existeAfastamentoParaData(Integer codCliente, Date dataAvaliar, String tipoAfastamento) throws Exception {
        String sql = "select afastamento.codigo from afastamentocontratodependente afastamento\n" +
                "inner join contratodependente cd on cd.codigo  = afastamento.contratodependente \n" +
                "where cd.cliente = " + codCliente + "\n" +
                "and '" + Uteis.getDataJDBC(dataAvaliar) + "' between inicioafastamento and finalafastamento \n";
        if (!UteisValidacao.emptyString(tipoAfastamento)) {
            sql += " and afastamento.tipoafastamento = '" + tipoAfastamento + "' \n";
        }
        return existe(sql, this.con);
    }

    public boolean existeAfastamentoPorContratoDependente(Integer codigoContratoDependente) throws Exception {
        return existe("select codigo from afastamentocontratodependente \n" +
                "where contratodependente = " + codigoContratoDependente + "\n", this.con);
    }

}

package negocio.facade.jdbc.contrato;

import negocio.comuns.contrato.ContratoDuracaoVO;
import negocio.comuns.utilitarias.ConsistirException;
import negocio.comuns.utilitarias.Uteis;
import negocio.facade.jdbc.arquitetura.SuperEntidade;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.Statement;
import java.util.ArrayList;
import java.util.List;
import negocio.comuns.arquitetura.LogVO;
import negocio.comuns.arquitetura.UsuarioVO;
import negocio.comuns.contrato.ContratoVO;

/**
 * Classe de persistência que encapsula todas as operações de manipulação dos dados da classe <code>ContratoDuracaoVO</code>.
 * Responsável por implementar operações como incluir, alterar, excluir e consultar pertinentes a classe <code>ContratoDuracaoVO</code>.
 * Encapsula toda a interação com o banco de dados.
 * @see ContratoDuracaoVO
 * @see SuperEntidade
 * @see Contrato
 */
public class ContratoDuracao extends SuperEntidade {

    public ContratoDuracao() throws Exception {
        super();
        setIdEntidade("Contrato");
    }
    public ContratoDuracao(Connection conexao) throws Exception {
        super(conexao);
        setIdEntidade("Contrato");
    }

    /**
     * Responsável por montar os dados de vários objetos, resultantes de uma consulta ao banco de dados (<code>ResultSet</code>).
     * Faz uso da operação <code>montarDados</code> que realiza o trabalho para um objeto por vez.
     * @return  List Contendo vários objetos da classe <code>ContratoDuracaoVO</code> resultantes da consulta.
     */
    public static List montarDadosConsulta(ResultSet tabelaResultado, int nivelMontarDados) throws Exception {
        List vetResultado = new ArrayList();
        while (tabelaResultado.next()) {
            ContratoDuracaoVO obj = new ContratoDuracaoVO();
            obj = montarDados(tabelaResultado, nivelMontarDados);
            vetResultado.add(obj);
        }
        return vetResultado;
    }

    /**
     * Responsável por montar os dados resultantes de uma consulta ao banco de dados (<code>ResultSet</code>)
     * em um objeto da classe <code>ContratoDuracaoVO</code>.
     * @return  O objeto da classe <code>ContratoDuracaoVO</code> com os dados devidamente montados.
     */
    public static ContratoDuracaoVO montarDados(ResultSet dadosSQL, int nivelMontarDados) throws Exception {
        ContratoDuracaoVO obj = new ContratoDuracaoVO();
        obj.setCodigo(dadosSQL.getInt("codigo"));
        obj.setNumeroMeses(dadosSQL.getInt("numeroMeses"));
        obj.setContrato(dadosSQL.getInt("Contrato"));
        obj.setNrMaximoParcelasCondPagamento(dadosSQL.getInt("nrMaximoParcelasCondPagamento"));
        obj.setTipoValor(dadosSQL.getString("tipoValor"));
        obj.setTipoOperacao(dadosSQL.getString("tipoOperacao"));
        obj.setPercentualDesconto(dadosSQL.getDouble("percentualDesconto"));
        obj.setValorEspecifico(dadosSQL.getDouble("valorEspecifico"));
        obj.setValorDesejado(dadosSQL.getDouble("valorDesejado"));
        obj.setValorDesejadoMensal(dadosSQL.getDouble("valorDesejadoMensal"));
        obj.setValorDesejadoParcela(dadosSQL.getDouble("valorDesejadoParcela"));
        obj.setCarencia(dadosSQL.getInt("carencia"));
        obj.setQuantidadeDiasExtra(dadosSQL.getInt("quantidadeDiasExtra"));
        obj.setNovoObj(false);

        if (nivelMontarDados == Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA) {
            return obj;
        }

        if (nivelMontarDados == Uteis.NIVELMONTARDADOS_DADOSBASICOS) {
            return obj;
        }


        //      montarDadosDuracao(obj, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
        return obj;
    }

    /**
     * Operação responsável por retornar um novo objeto da classe <code>ContratoDuracaoVO</code>.
     */
    public ContratoDuracaoVO novo() throws Exception {
        incluir(getIdEntidade());
        ContratoDuracaoVO obj = new ContratoDuracaoVO();
        return obj;
    }

    /**
     * Operação responsável por incluir no banco de dados um objeto da classe <code>ContratoDuracaoVO</code>.
     * Primeiramente valida os dados (<code>validarDados</code>) do objeto. Verifica a conexão com o banco de dados e a permissão do usuário
     * para realizar esta operacão na entidade.
     * Isto, através da operação <code>incluir</code> da superclasse.
     * @param obj  Objeto da classe <code>ContratoDuracaoVO</code> que será gravado no banco de dados.
     * @exception Exception Caso haja problemas de conexão, restrição de acesso ou validação de dados.
     */
    public void incluir(ContratoDuracaoVO obj) throws Exception {
        ContratoDuracaoVO.validarDados(obj);
        incluir(getIdEntidade());
        obj.realizarUpperCaseDados();
        String sql = "INSERT INTO ContratoDuracao( numeroMeses , contrato, nrMaximoParcelasCondPagamento, percentualDesconto, valorEspecifico,  tipoValor, tipoOperacao, valorDesejado, valorDesejadoMensal, valorDesejadoParcela,carencia,quantidadeDiasExtra) VALUES ( ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ? )";
        PreparedStatement sqlInserir = con.prepareStatement(sql);
        sqlInserir.setInt(1, obj.getNumeroMeses().intValue());
        if (obj.getContrato().intValue() != 0) {
            sqlInserir.setInt(2, obj.getContrato().intValue());
        } else {
            sqlInserir.setNull(2, 0);
        }
        sqlInserir.setInt(3, obj.getNrMaximoParcelasCondPagamento().intValue());
        sqlInserir.setDouble(4, obj.getPercentualDesconto().doubleValue());
        sqlInserir.setDouble(5, obj.getValorEspecifico().doubleValue());
        sqlInserir.setString(6, obj.getTipoValor());
        sqlInserir.setString(7, obj.getTipoOperacao());
        sqlInserir.setDouble(8, obj.getValorDesejado());
        sqlInserir.setDouble(9, obj.getValorDesejadoMensal());
        sqlInserir.setDouble(10, obj.getValorDesejadoParcela());
        sqlInserir.setInt(11, obj.getCarencia());
        sqlInserir.setInt(12, obj.getQuantidadeDiasExtra());
        sqlInserir.execute();
        obj.setCodigo(obterValorChavePrimariaCodigo());
        obj.setNovoObj(new Boolean(false));

        //CONTRATO DE CREDITO POR SESSÃO DEPENDE DO CONTRATO MODALIDADE
        //ENTÃO ELE SERÁ INCLUIRO SOMENTE APOS INCLUIR CONTRATO MODALIDADE
        if (obj.getContratoVO() != null && obj.getContratoVO().getPlano().isVendaCreditoTreino() && !obj.getContratoVO().getPlano().isCreditoSessao()){
            ContratoDuracaoCreditoTreino cdctDAO = new ContratoDuracaoCreditoTreino(con);
            obj.getContratoDuracaoCreditoTreinoVO().setDataUltimoCreditoMensal(obj.getContratoVO().getVigenciaDe());
            cdctDAO.incluir(obj.getContratoDuracaoCreditoTreinoVO());
            cdctDAO = null;
        }

    }

    /**
     * Operação responsável por alterar no BD os dados de um objeto da classe <code>ContratoDuracaoVO</code>.
     * Sempre utiliza a chave primária da classe como atributo para localização do registro a ser alterado.
     * Primeiramente valida os dados (<code>validarDados</code>) do objeto. Verifica a conexão com o banco de dados e a permissão do usuário
     * para realizar esta operacão na entidade.
     * Isto, através da operação <code>alterar</code> da superclasse.
     * @param obj    Objeto da classe <code>ContratoDuracaoVO</code> que será alterada no banco de dados.
     * @exception Execption Caso haja problemas de conexão, restrição de acesso ou validação de dados.
     */
    public void alterar(ContratoDuracaoVO obj) throws Exception {
        ContratoDuracaoVO.validarDados(obj);
        alterar(getIdEntidade());
        obj.realizarUpperCaseDados();
        String sql = "UPDATE ContratoDuracao set numeroMeses=?, contrato=?, nrMaximoParcelasCondPagamento=?,percentualDesconto=?, valorEspecifico=?, tipoValor=?, tipoOperacao=?, valorDesejado=?, valorDesejadoMensal = ?, valorDesejadoParcela = ?, carencia=?, quantidadeDiasExtra=? WHERE ((codigo = ?))";
        PreparedStatement sqlAlterar = con.prepareStatement(sql);
//        if (obj.getDuracao().getCodigo().intValue() != 0) {
//            sqlAlterar.setInt(1, obj.getDuracao().getCodigo().intValue());
//        } else {
//            sqlAlterar.setNull(1, 0);
//        }
        sqlAlterar.setInt(1, obj.getNumeroMeses().intValue());
        if (obj.getContrato().intValue() != 0) {
            sqlAlterar.setInt(2, obj.getContrato().intValue());
        } else {
            sqlAlterar.setNull(2, 0);
        }
        sqlAlterar.setInt(3, obj.getNrMaximoParcelasCondPagamento().intValue());
        sqlAlterar.setDouble(4, obj.getPercentualDesconto().doubleValue());
        sqlAlterar.setDouble(5, obj.getValorEspecifico().doubleValue());
        sqlAlterar.setString(6, obj.getTipoValor());
        sqlAlterar.setString(7, obj.getTipoOperacao());
        sqlAlterar.setDouble(8, obj.getValorDesejado());
        sqlAlterar.setDouble(9, obj.getValorDesejadoMensal());
        sqlAlterar.setDouble(10, obj.getValorDesejadoParcela());
        sqlAlterar.setInt(11, obj.getCarencia().intValue());
        sqlAlterar.setInt(12, obj.getQuantidadeDiasExtra());
        sqlAlterar.setInt(13, obj.getCodigo().intValue());
        sqlAlterar.execute();
    }

    /**
     * Operação responsável por excluir no BD um objeto da classe <code>ContratoDuracaoVO</code>.
     * Sempre localiza o registro a ser excluído através da chave primária da entidade.
     * Primeiramente verifica a conexão com o banco de dados e a permissão do usuário
     * para realizar esta operacão na entidade.
     * Isto, através da operação <code>excluir</code> da superclasse.
     * @param obj    Objeto da classe <code>ContratoDuracaoVO</code> que será removido no banco de dados.
     * @exception Execption Caso haja problemas de conexão ou restrição de acesso.
     */
    public void excluir(ContratoDuracaoVO obj) throws Exception {
        excluir(getIdEntidade());
        String sql = "DELETE FROM ContratoDuracao WHERE ((codigo = ?))";
        PreparedStatement sqlExcluir = con.prepareStatement(sql);
        sqlExcluir.setInt(1, obj.getCodigo().intValue());
        sqlExcluir.execute();
    }

    /**
     * Responsável por realizar uma consulta de <code>ContratoDuracao</code> através do valor do atributo
     * <code>Integer nrMaximoParcelasCondPagamento</code>. Retorna os objetos com valores iguais ou superiores ao parâmetro fornecido.
     * Faz uso da operação <code>montarDadosConsulta</code> que realiza o trabalho de prerarar o List resultante.
     * @param   controlarAcesso Indica se a aplicação deverá verificar se o usuário possui permissão para esta consulta ou não.
     * @return  List Contendo vários objetos da classe <code>ContratoDuracaoVO</code> resultantes da consulta.
     * @exception Exception Caso haja problemas de conexão ou restrição de acesso.
     */
    public List consultarPorNrMaximoParcelasCondPagamento(Integer valorConsulta, boolean controlarAcesso, int nivelMontarDados) throws Exception {
        consultar(getIdEntidade(), controlarAcesso);
        String sqlStr = "SELECT * FROM ContratoDuracao WHERE nrMaximoParcelasCondPagamento >= " + valorConsulta.intValue() + " ORDER BY nrMaximoParcelasCondPagamento";
        Statement stm = con.createStatement();
        ResultSet tabelaResultado = stm.executeQuery(sqlStr);
        return (montarDadosConsulta(tabelaResultado, nivelMontarDados));
    }

    /**
     * Responsável por realizar uma consulta de <code>ContratoDuracao</code> através do valor do atributo
     * <code>numeroMeses</code> da classe <code>Duracao</code>
     * Faz uso da operação <code>montarDadosConsulta</code> que realiza o trabalho de prerarar o List resultante.
     * @return  List Contendo vários objetos da classe <code>ContratoDuracaoVO</code> resultantes da consulta.
     * @exception Exception Caso haja problemas de conexão ou restrição de acesso.
     */
    public List consultarPorNumeroMesesDuracao(Integer valorConsulta, int nivelMontarDados) throws Exception {
        consultar(getIdEntidade(), true);
        String sqlStr = "SELECT ContratoDuracao.* FROM ContratoDuracao WHERE ContratoDuracao.numeroMeses >= " + valorConsulta.intValue() + " ORDER BY ContratoDuracao.numeroMeses";
        Statement stm = con.createStatement();
        ResultSet tabelaResultado = stm.executeQuery(sqlStr);
        return montarDadosConsulta(tabelaResultado, nivelMontarDados);
    }

    /**
     * Responsável por realizar uma consulta de <code>ContratoDuracao</code> através do valor do atributo
     * <code>Integer codigo</code>. Retorna os objetos com valores iguais ou superiores ao parâmetro fornecido.
     * Faz uso da operação <code>montarDadosConsulta</code> que realiza o trabalho de prerarar o List resultante.
     * @param   controlarAcesso Indica se a aplicação deverá verificar se o usuário possui permissão para esta consulta ou não.
     * @return  List Contendo vários objetos da classe <code>ContratoDuracaoVO</code> resultantes da consulta.
     * @exception Exception Caso haja problemas de conexão ou restrição de acesso.
     */
    public List consultarPorCodigo(Integer valorConsulta, boolean controlarAcesso, int nivelMontarDados) throws Exception {
        consultar(getIdEntidade(), controlarAcesso);
        String sqlStr = "SELECT * FROM ContratoDuracao WHERE codigo >= " + valorConsulta.intValue() + " ORDER BY codigo";
        Statement stm = con.createStatement();
        ResultSet tabelaResultado = stm.executeQuery(sqlStr);
        return (montarDadosConsulta(tabelaResultado, nivelMontarDados));
    }

//    /**
//     * Operação responsável por montar os dados de um objeto da classe <code>DuracaoVO</code> relacionado ao objeto <code>ContratoDuracaoVO</code>.
//     * Faz uso da chave primária da classe <code>DuracaoVO</code> para realizar a consulta.
//     * @param obj  Objeto no qual será montado os dados consultados.
//     */
//    public static void montarDadosDuracao(ContratoDuracaoVO obj, int nivelMontarDados) throws Exception {
//        if (obj.getDuracao().getCodigo().intValue() == 0) {
//            obj.setDuracao(new DuracaoVO());
//            return;
//        }
//        obj.setDuracao(new Duracao().consultarPorChavePrimaria(obj.getDuracao().getCodigo(), nivelMontarDados));
//    }

    /**
     * Operação responsável por consultar todos os <code>ContratoDuracaoVO</code> relacionados a um objeto da classe <code>Contrato.Contrato</code>.
     * @param Contrato  Atributo de <code>Contrato.Contrato</code> a ser utilizado para localizar os objetos da classe <code>ContratoDuracaoVO</code>.
     * @return List  Contendo todos os objetos da classe <code>ContratoDuracaoVO</code> resultantes da consulta.
     * @exception Exception  Erro de conexão com o BD ou restrição de acesso a esta operação.
     */
    public ContratoDuracaoVO consultarContratoDuracoes(Integer contrato, int nivelMontarDados) throws Exception {
        consultar(getIdEntidade());
        String sql = "SELECT * FROM ContratoDuracao WHERE contrato = ?";
        PreparedStatement sqlConsulta = con.prepareStatement(sql);
        sqlConsulta.setInt(1, contrato.intValue());
        ResultSet resultado = sqlConsulta.executeQuery();
        if (!resultado.next()) {
            throw new ConsistirException(String.format("Dados Não Encontrados ( ContratoDuracao -> Contrato %s)",
                    new Object[]{
                contrato
            }));
        }
        return (montarDados(resultado, nivelMontarDados));

    }

    public ContratoDuracaoVO consultarPorContrato(Integer codigoContrato, int nivelMontarDados) throws Exception {
        String sql = "SELECT * FROM ContratoDuracao WHERE contrato = ?";
        PreparedStatement pst = con.prepareStatement(sql);
        pst.setInt(1, codigoContrato);
        ResultSet rs = pst.executeQuery();
        if (rs.next()) {
            return (montarDados(rs, nivelMontarDados));
        }
        return null;
    }


    /**
     * Operação responsável por localizar um objeto da classe <code>ContratoDuracaoVO</code>
     * através de sua chave primária.
     * @exception Exception Caso haja problemas de conexão ou localização do objeto procurado.
     */
    public ContratoDuracaoVO consultarPorChavePrimaria(Integer codigoPrm, int nivelMontarDados) throws Exception {
        consultar(getIdEntidade(), false);
        String sql = "SELECT * FROM ContratoDuracao WHERE codigo = ?";
        PreparedStatement sqlConsultar = con.prepareStatement(sql);
        sqlConsultar.setInt(1, codigoPrm.intValue());
        ResultSet tabelaResultado = sqlConsultar.executeQuery();
        if (!tabelaResultado.next()) {
            throw new ConsistirException(String.format("Dados Não Encontrados ( ContratoDuracao %s)",
                    new Object[]{
                codigoPrm
            }));
        }
        return (montarDados(tabelaResultado, nivelMontarDados));
    }

    /**
     * Responsável por consultar apenas o numero de meses de duração do contrato
     * <AUTHOR>
     * 26/09/2011
     */
    public int consultarDuracaoMeses(Integer contrato) throws Exception {
        consultar(getIdEntidade());
        String sql = "SELECT numeromeses FROM ContratoDuracao WHERE contrato = ?";
        PreparedStatement sqlConsulta = con.prepareStatement(sql);
        sqlConsulta.setInt(1, contrato.intValue());
        ResultSet resultado = sqlConsulta.executeQuery();
        if (!resultado.next()) {
            throw new ConsistirException(String.format("Dados Não Encontrados ( ContratoDuracao -> Contrato %s)",
                    new Object[]{
                contrato
            }));
        }
        return resultado.getInt("numeromeses");

    }

     /**
     * Responsável por realizar uma consulta de <code>ContratoDuracao</code>
     * Consulta todos os meses distintos dos contratos.
     * @return  List Contendo vários objetos da classe <code>ContratoDuracaoVO</code> resultantes da consulta.
     * @exception Exception Caso haja problemas de conexão ou restrição de acesso.
     */
    public List<ContratoDuracaoVO> consultarNumeroMeses() throws Exception {
        String sqlStr = "SELECT\n  DISTINCT numeromeses\nFROM contratoduracao\nORDER BY numeromeses";
        Statement stm = con.createStatement();
        ResultSet tabelaResultado = stm.executeQuery(sqlStr);
        List<ContratoDuracaoVO> listaDuracoes = new ArrayList<ContratoDuracaoVO>();
        while (tabelaResultado.next()) {
            ContratoDuracaoVO contratoDuracaoVO = new ContratoDuracaoVO();
            contratoDuracaoVO.setNumeroMeses(tabelaResultado.getInt("numeroMeses"));
            listaDuracoes.add(contratoDuracaoVO);
        }
        return listaDuracoes;
    }
    
    
    public void alterarDiasCarenciaContrato(ContratoVO contrato, Integer nrDiasAnterior,  Integer nrDias, UsuarioVO responsavel) throws Exception{
        executarConsulta("UPDATE contratoduracao SET carencia = "+nrDias
                +", carenciaalterada = true WHERE contrato = "+contrato.getCodigo(), con);
        LogVO logDescricao = new LogVO();
        logDescricao.setNomeEntidade("DIASCARENCIA");
        logDescricao.setChavePrimaria(contrato.getCodigo().toString());
        logDescricao.setResponsavelAlteracao(responsavel.getNome());
        logDescricao.setValorCampoAlterado(nrDias.toString());
        logDescricao.setValorCampoAnterior(nrDiasAnterior.toString());
        logDescricao.setNomeCampo("Dias carência");
        logDescricao.setOperacao("ALTERAÇÃO");
        logDescricao.setPessoa(contrato.getPessoa().getCodigo());
        getFacade().getLog().incluir(logDescricao);
    }
        

}

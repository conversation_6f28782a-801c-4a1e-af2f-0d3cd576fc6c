package negocio.facade.jdbc.contrato;

import java.util.Date;
import negocio.interfaces.contrato.*;
import negocio.comuns.contrato.ConvenioVO;
import negocio.facade.jdbc.arquitetura.*;
import java.sql.ResultSet;
import java.sql.PreparedStatement;
import java.sql.Statement;
import java.util.List;
import java.util.ArrayList;
import negocio.comuns.utilitarias.*;

/**
 * Classe de persistência que encapsula todas as operações de manipulação dos dados da classe <code>ConvenioVO</code>.
 * Responsável por implementar operações como incluir, alterar, excluir e consultar pertinentes a classe <code>ConvenioVO</code>.
 * Encapsula toda a interação com o banco de dados.
 * @see ConvenioVO
 * @see SuperEntidade
 */
public class Convenio extends SuperEntidade implements ConvenioInterfaceFacade {   

    public Convenio() throws Exception {
        super();        
    }

    /**
     * Operação responsável por retornar um novo objeto da classe <code>ConvenioVO</code>.
     */
    public ConvenioVO novo() throws Exception {
        incluir(getIdEntidade());
        ConvenioVO obj = new ConvenioVO();
        return obj;
    }

    /**
     * Operação responsável por incluir no banco de dados um objeto da classe <code>ConvenioVO</code>.
     * Primeiramente valida os dados (<code>validarDados</code>) do objeto. Verifica a conexão com o banco de dados e a permissão do usuário
     * para realizar esta operacão na entidade.
     * Isto, através da operação <code>incluir</code> da superclasse.
     * @param obj  Objeto da classe <code>ConvenioVO</code> que será gravado no banco de dados.
     * @exception Exception Caso haja problemas de conexão, restrição de acesso ou validação de dados.
     */
    public void incluir(ConvenioVO obj) throws Exception {
        try {
            con.setAutoCommit(false);
            ConvenioVO.validarDados(obj);
            incluir(getIdEntidade());
            obj.realizarUpperCaseDados();
            String sql = "INSERT INTO Convenio( descricao, dataAssinatura, dataInicioVigencia, dataFinalVigencia, descontoParcela, responsavelAutorizacao, dataAutorizacao, situacao ) VALUES ( ?, ?, ?, ?, ?, ?, ?, ? )";
            PreparedStatement sqlInserir = con.prepareStatement(sql);
            sqlInserir.setString(1, obj.getDescricao());
            sqlInserir.setDate(2, Uteis.getDataJDBC(obj.getDataAssinatura()));
            sqlInserir.setDate(3, Uteis.getDataJDBC(obj.getDataInicioVigencia()));
            sqlInserir.setDate(4, Uteis.getDataJDBC(obj.getDataFinalVigencia()));
            sqlInserir.setDouble(5, obj.getDescontoParcela().doubleValue());
            sqlInserir.setInt(6, obj.getResponsavelAutorizacao().intValue());
            sqlInserir.setDate(7, Uteis.getDataJDBC(obj.getDataAutorizacao()));
            sqlInserir.setString(8, obj.getSituacao());
            sqlInserir.execute();
            obj.setCodigo(obterValorChavePrimariaCodigo());
            obj.setNovoObj(new Boolean(false));
            con.commit();
        } catch (Exception e) {
            con.rollback();
            con.setAutoCommit(true);
            throw e;
        } finally {
            con.setAutoCommit(true);
        }
    }

    /**
     * Operação responsável por alterar no BD os dados de um objeto da classe <code>ConvenioVO</code>.
     * Sempre utiliza a chave primária da classe como atributo para localização do registro a ser alterado.
     * Primeiramente valida os dados (<code>validarDados</code>) do objeto. Verifica a conexão com o banco de dados e a permissão do usuário
     * para realizar esta operacão na entidade.
     * Isto, através da operação <code>alterar</code> da superclasse.
     * @param obj    Objeto da classe <code>ConvenioVO</code> que será alterada no banco de dados.
     * @exception Execption Caso haja problemas de conexão, restrição de acesso ou validação de dados.
     */
    public void alterar(ConvenioVO obj) throws Exception {
        try {
            con.setAutoCommit(false);
            ConvenioVO.validarDados(obj);
            alterar(getIdEntidade());
            obj.realizarUpperCaseDados();
            String sql = "UPDATE Convenio set descricao=?, dataAssinatura=?, dataInicioVigencia=?, dataFinalVigencia=?, descontoParcela=?, responsavelAutorizacao=?, dataAutorizacao=?, situacao=? WHERE ((codigo = ?))";
            PreparedStatement sqlAlterar = con.prepareStatement(sql);
            sqlAlterar.setString(1, obj.getDescricao());
            sqlAlterar.setDate(2, Uteis.getDataJDBC(obj.getDataAssinatura()));
            sqlAlterar.setDate(3, Uteis.getDataJDBC(obj.getDataInicioVigencia()));
            sqlAlterar.setDate(4, Uteis.getDataJDBC(obj.getDataFinalVigencia()));
            sqlAlterar.setDouble(5, obj.getDescontoParcela().doubleValue());
            sqlAlterar.setInt(6, obj.getResponsavelAutorizacao().intValue());
            sqlAlterar.setDate(7, Uteis.getDataJDBC(obj.getDataAutorizacao()));
            sqlAlterar.setString(8, obj.getSituacao());
            sqlAlterar.setInt(9, obj.getCodigo().intValue());
            sqlAlterar.execute();
            con.commit();
        } catch (Exception e) {
            con.rollback();
            con.setAutoCommit(true);
            throw e;
        } finally {
            con.setAutoCommit(true);
        }
    }

    /**
     * Operação responsável por excluir no BD um objeto da classe <code>ConvenioVO</code>.
     * Sempre localiza o registro a ser excluído através da chave primária da entidade.
     * Primeiramente verifica a conexão com o banco de dados e a permissão do usuário
     * para realizar esta operacão na entidade.
     * Isto, através da operação <code>excluir</code> da superclasse.
     * @param obj    Objeto da classe <code>ConvenioVO</code> que será removido no banco de dados.
     * @exception Execption Caso haja problemas de conexão ou restrição de acesso.
     */
    public void excluir(ConvenioVO obj) throws Exception {
        try {
            con.setAutoCommit(false);
            excluir(getIdEntidade());
            String sql = "DELETE FROM Convenio WHERE ((codigo = ?))";
            PreparedStatement sqlExcluir = con.prepareStatement(sql);
            sqlExcluir.setInt(1, obj.getCodigo().intValue());
            sqlExcluir.execute();
            con.commit();
        } catch (Exception e) {
            con.rollback();
            con.setAutoCommit(true);
            throw e;
        } finally {
            con.setAutoCommit(true);
        }
    }

    /**
     * Responsável por realizar uma consulta de <code>Convenio</code> através do valor do atributo 
     * <code>Date dataAssinatura</code>. Retorna os objetos com valores pertecentes ao período informado por parâmetro.
     * Faz uso da operação <code>montarDadosConsulta</code> que realiza o trabalho de prerarar o List resultante.
     * @param   controlarAcesso Indica se a aplicação deverá verificar se o usuário possui permissão para esta consulta ou não.
     * @return  List Contendo vários objetos da classe <code>ConvenioVO</code> resultantes da consulta.
     * @exception Exception Caso haja problemas de conexão ou restrição de acesso.
     */
    public List consultarPorDataAssinatura(Date prmIni, Date prmFim, boolean controlarAcesso, int nivelMontarDados) throws Exception {
        consultar(getIdEntidade(), controlarAcesso);
        String sqlStr = "SELECT * FROM Convenio WHERE ((dataAssinatura >= '" + Uteis.getDataJDBC(prmIni) + "') and (dataAssinatura <= '" + Uteis.getDataJDBC(prmFim) + "')) ORDER BY dataAssinatura";
        Statement stm = con.createStatement();
        ResultSet tabelaResultado = stm.executeQuery(sqlStr);
        return (montarDadosConsulta(tabelaResultado, nivelMontarDados));
    }

    /**
     * Responsável por realizar uma consulta de <code>Convenio</code> através do valor do atributo 
     * <code>String descricao</code>. Retorna os objetos, com início do valor do atributo idêntico ao parâmetro fornecido.
     * Faz uso da operação <code>montarDadosConsulta</code> que realiza o trabalho de prerarar o List resultante.
     * @param   controlarAcesso Indica se a aplicação deverá verificar se o usuário possui permissão para esta consulta ou não.
     * @return  List Contendo vários objetos da classe <code>ConvenioVO</code> resultantes da consulta.
     * @exception Exception Caso haja problemas de conexão ou restrição de acesso.
     */
    public List consultarPorDescricao(String valorConsulta, boolean controlarAcesso, int nivelMontarDados) throws Exception {
        consultar(getIdEntidade(), controlarAcesso);
        String sqlStr = "SELECT * FROM Convenio WHERE upper( descricao ) like('" + valorConsulta.toUpperCase() + "%') ORDER BY descricao";
        Statement stm = con.createStatement();
        ResultSet tabelaResultado = stm.executeQuery(sqlStr);
        return (montarDadosConsulta(tabelaResultado, nivelMontarDados));
    }

    /**
     * Responsável por realizar uma consulta de <code>Convenio</code> através do valor do atributo 
     * <code>Integer codigo</code>. Retorna os objetos com valores iguais ou superiores ao parâmetro fornecido.
     * Faz uso da operação <code>montarDadosConsulta</code> que realiza o trabalho de prerarar o List resultante.
     * @param   controlarAcesso Indica se a aplicação deverá verificar se o usuário possui permissão para esta consulta ou não.
     * @return  List Contendo vários objetos da classe <code>ConvenioVO</code> resultantes da consulta.
     * @exception Exception Caso haja problemas de conexão ou restrição de acesso.
     */
    public List consultarPorCodigo(Integer valorConsulta, boolean controlarAcesso, int nivelMontarDados) throws Exception {
        consultar(getIdEntidade(), controlarAcesso);
        String sqlStr = "SELECT * FROM Convenio WHERE codigo >= " + valorConsulta.intValue() + " ORDER BY codigo";
        Statement stm = con.createStatement();
        ResultSet tabelaResultado = stm.executeQuery(sqlStr);
        return (montarDadosConsulta(tabelaResultado, nivelMontarDados));
    }

    /**
     * Responsável por montar os dados de vários objetos, resultantes de uma consulta ao banco de dados (<code>ResultSet</code>).
     * Faz uso da operação <code>montarDados</code> que realiza o trabalho para um objeto por vez.
     * @return  List Contendo vários objetos da classe <code>ConvenioVO</code> resultantes da consulta.
     */
    public static List montarDadosConsulta(ResultSet tabelaResultado, int nivelMontarDados) throws Exception {
        List vetResultado = new ArrayList();
        while (tabelaResultado.next()) {
            ConvenioVO obj = new ConvenioVO();
            obj = montarDados(tabelaResultado, nivelMontarDados);
            vetResultado.add(obj);
        }
        return vetResultado;
    }

    /**
     * Responsável por montar os dados resultantes de uma consulta ao banco de dados (<code>ResultSet</code>)
     * em um objeto da classe <code>ConvenioVO</code>.
     * @return  O objeto da classe <code>ConvenioVO</code> com os dados devidamente montados.
     */
    public static ConvenioVO montarDados(ResultSet dadosSQL, int nivelMontarDados) throws Exception {
        ConvenioVO obj = new ConvenioVO();
        obj.setCodigo(new Integer(dadosSQL.getInt("codigo")));
        obj.setDescricao(dadosSQL.getString("descricao"));
        obj.setDataAssinatura(dadosSQL.getDate("dataAssinatura"));
        obj.setDataInicioVigencia(dadosSQL.getDate("dataInicioVigencia"));
        obj.setDataFinalVigencia(dadosSQL.getDate("dataFinalVigencia"));
        obj.setDescontoParcela(new Double(dadosSQL.getDouble("descontoParcela")));
        obj.setResponsavelAutorizacao(new Integer(dadosSQL.getInt("responsavelAutorizacao")));
        obj.setDataAutorizacao(dadosSQL.getDate("dataAutorizacao"));
        obj.setSituacao(dadosSQL.getString("situacao"));
        obj.setNovoObj(false);
        if (nivelMontarDados == Uteis.NIVELMONTARDADOS_DADOSBASICOS) {
            return obj;
        }

        return obj;
    }

    /**
     * Operação responsável por localizar um objeto da classe <code>ConvenioVO</code>
     * através de sua chave primária. 
     * @exception Exception Caso haja problemas de conexão ou localização do objeto procurado.
     */
    public ConvenioVO consultarPorChavePrimaria(Integer codigoPrm, int nivelMontarDados) throws Exception {
        consultar(getIdEntidade(), false);
        String sql = "SELECT * FROM Convenio WHERE codigo = ?";
        PreparedStatement sqlConsultar = con.prepareStatement(sql);
        sqlConsultar.setInt(1, codigoPrm.intValue());
        ResultSet tabelaResultado = sqlConsultar.executeQuery();
        if (!tabelaResultado.next()) {
            throw new ConsistirException("Dados Não Encontrados ( Convenio ).");
        }
        return (montarDados(tabelaResultado, nivelMontarDados));
    }
}

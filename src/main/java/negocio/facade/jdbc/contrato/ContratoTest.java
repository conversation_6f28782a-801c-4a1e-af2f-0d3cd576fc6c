package negocio.facade.jdbc.contrato;

import java.sql.Connection;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.List;

import negocio.comuns.arquitetura.UsuarioVO;
import negocio.comuns.basico.EmpresaVO;
import negocio.comuns.contrato.ContratoVO;
import negocio.comuns.plano.PlanoVO;
import negocio.facade.jdbc.arquitetura.FacadeManager;
import negocio.facade.jdbc.arquitetura.ZillyonWebFacade;

/**
 * Classe criada para testar o DAO de contrato.
 * Utilizada pela importação de dados.
 * 
 * <AUTHOR>
 *
 */
public class ContratoTest {
	
	/**
	 * Método main
	 * 
	 * @param args
	 */
	public static void main(String... args){
    	System.out.println("aloha!");
		List<ContratoVO> l = criarLista();		
		incluirLista(l);
    }

	/**
	 * Delega para a DAO a inclusão de uma lista de contratos
	 * Controla a transação
	 * 
	 * @param lista List<ContratoVO>
	 */
	public static void incluirLista(List<ContratoVO> lista) {
		try {
			

		    

			ZillyonWebFacade c = new ZillyonWebFacade();
                        c.getCon().setAutoCommit(false);
			for (ContratoVO contratoVO : lista) {
				try {
				    c.incluirSemCommit(contratoVO);
				    c.getCon().commit();
				} catch (Exception e) {
					contratoVO.setCodigo(new Integer(0));
					contratoVO.setNovoObj(new Boolean(true));
				    c.getCon().rollback();
				    e.printStackTrace();
				} 
			}
		    c.getCon().setAutoCommit(true);
			
		} catch (SQLException e) {
			// TODO Auto-generated catch block
			e.printStackTrace();
		} catch (Exception e) {
			// TODO Auto-generated catch block
			e.printStackTrace();
		}
	}

	private static List<ContratoVO> criarLista() {
		List<ContratoVO> l = new ArrayList<ContratoVO>();
		for (int i = 0; i < 10; i++) {
			ContratoVO obj = criarObj();
			l.add(obj);
		}
		return l;
	}

	private static ContratoVO criarObj() {
		ContratoVO contrato = new ContratoVO();
		UsuarioVO responsavelContrato = new UsuarioVO();
		responsavelContrato.setCodigo(1);
		contrato.setResponsavelContrato(responsavelContrato);
		
		EmpresaVO empresa = new EmpresaVO();
		empresa.setCodigo(1);
		contrato.setEmpresa(empresa);
		
		PlanoVO plano = new PlanoVO();
		plano.setCodigo(1);
		contrato.setPlano(plano);
		
		List contratoModalidadeVOs = new ArrayList();
		contrato.setContratoModalidadeVOs(contratoModalidadeVOs );
		return contrato;
	}
}

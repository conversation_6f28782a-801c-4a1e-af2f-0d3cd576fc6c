package negocio.facade.jdbc.contrato;

import negocio.comuns.plano.CondicaoPagamentoVO;
import negocio.facade.jdbc.plano.CondicaoPagamento;
import negocio.facade.jdbc.arquitetura.*;

import java.sql.Connection;
import java.sql.ResultSet;
import java.sql.PreparedStatement;
import java.sql.Statement;
import java.util.List;
import java.util.ArrayList;
import negocio.comuns.contrato.ContratoCondicaoPagamentoVO;
import negocio.comuns.utilitarias.*;

/**
 * Classe de persistência que encapsula todas as operações de manipulação dos dados da classe <code>ContratoCondicaoPagamentoVO</code>.
 * Responsável por implementar operações como incluir, alterar, excluir e consultar pertinentes a classe <code>ContratoCondicaoPagamentoVO</code>.
 * Encapsula toda a interação com o banco de dados.
 * @see ContratoCondicaoPagamentoVO
 * @see SuperEntidade
 * @see Plano
 */
public class ContratoCondicaoPagamento extends SuperEntidade {    

    public ContratoCondicaoPagamento() throws Exception {
        super();
        setIdEntidade("Contrato");
    }
    public ContratoCondicaoPagamento(Connection conexao) throws Exception {
        super(conexao);
        setIdEntidade("Contrato");
    }
    /**
     * Operação responsável por retornar um novo objeto da classe <code>ContratoCondicaoPagamentoVO</code>.
     */
    public ContratoCondicaoPagamentoVO novo() throws Exception {
        incluir(getIdEntidade());
        ContratoCondicaoPagamentoVO obj = new ContratoCondicaoPagamentoVO();
        return obj;
    }

    /**
     * Operação responsável por incluir no banco de dados um objeto da classe <code>ContratoCondicaoPagamentoVO</code>.
     * Primeiramente valida os dados (<code>validarDados</code>) do objeto. Verifica a conexão com o banco de dados e a permissão do usuário
     * para realizar esta operacão na entidade.
     * Isto, através da operação <code>incluir</code> da superclasse.
     * @param obj  Objeto da classe <code>ContratoCondicaoPagamentoVO</code> que será gravado no banco de dados.
     * @exception Exception Caso haja problemas de conexão, restrição de acesso ou validação de dados.
     */
    public void incluir(ContratoCondicaoPagamentoVO obj) throws Exception {
        ContratoCondicaoPagamentoVO.validarDados(obj);
        incluir(getIdEntidade());
        obj.realizarUpperCaseDados();
        String sql = "INSERT INTO ContratoCondicaoPagamento( contrato, condicaoPagamento, percentualDesconto, tipooperacao, tipovalor, valorespecifico ) VALUES ( ?, ?, ?, ?, ?, ? )";
        PreparedStatement sqlInserir = con.prepareStatement(sql);
        if (obj.getContrato().intValue() != 0) {
            sqlInserir.setInt(1, obj.getContrato().intValue());
        } else {
            sqlInserir.setNull(1, 0);
        }
        if (obj.getCondicaoPagamento().getCodigo().intValue() != 0) {
            sqlInserir.setInt(2, obj.getCondicaoPagamento().getCodigo().intValue());
        } else {
            sqlInserir.setNull(2, 0);
        }
        sqlInserir.setDouble(3, obj.getPercentualDesconto().doubleValue());
        sqlInserir.setString(4, obj.getTipoOperacao());
        sqlInserir.setString(5, obj.getTipoValor());
        sqlInserir.setDouble(6, obj.getValorEspecifico().doubleValue());

        sqlInserir.execute();
        obj.setCodigo(obterValorChavePrimariaCodigo());
        obj.setNovoObj(new Boolean(false));
    }

    /**
     * Operação responsável por alterar no BD os dados de um objeto da classe <code>ContratoCondicaoPagamentoVO</code>.
     * Sempre utiliza a chave primária da classe como atributo para localização do registro a ser alterado.
     * Primeiramente valida os dados (<code>validarDados</code>) do objeto. Verifica a conexão com o banco de dados e a permissão do usuário
     * para realizar esta operacão na entidade.
     * Isto, através da operação <code>alterar</code> da superclasse.
     * @param obj    Objeto da classe <code>ContratoCondicaoPagamentoVO</code> que será alterada no banco de dados.
     * @exception Execption Caso haja problemas de conexão, restrição de acesso ou validação de dados.
     */
    public void alterar(ContratoCondicaoPagamentoVO obj) throws Exception {
        ContratoCondicaoPagamentoVO.validarDados(obj);
        alterar(getIdEntidade());
        obj.realizarUpperCaseDados();
        String sql = "UPDATE ContratoCondicaoPagamento set contrato=?, condicaoPagamento=?, percentualDesconto=?, tipooperacao=?, tipovalor=?, valorespecifico=? WHERE ((codigo = ?))";
        PreparedStatement sqlAlterar = con.prepareStatement(sql);
        if (obj.getContrato().intValue() != 0) {
            sqlAlterar.setInt(1, obj.getContrato().intValue());
        } else {
            sqlAlterar.setNull(1, 0);
        }
        if (obj.getCondicaoPagamento().getCodigo().intValue() != 0) {
            sqlAlterar.setInt(2, obj.getCondicaoPagamento().getCodigo().intValue());
        } else {
            sqlAlterar.setNull(2, 0);
        }
        sqlAlterar.setDouble(3, obj.getPercentualDesconto().doubleValue());
        sqlAlterar.setString(4, obj.getTipoOperacao());
        sqlAlterar.setString(5, obj.getTipoValor());
        sqlAlterar.setDouble(6, obj.getValorEspecifico().doubleValue());
        sqlAlterar.setInt(7, obj.getCodigo().intValue());
        sqlAlterar.execute();
    }

    /**
     * Operação responsável por excluir no BD um objeto da classe <code>ContratoCondicaoPagamentoVO</code>.
     * Sempre localiza o registro a ser excluído através da chave primária da entidade.
     * Primeiramente verifica a conexão com o banco de dados e a permissão do usuário
     * para realizar esta operacão na entidade.
     * Isto, através da operação <code>excluir</code> da superclasse.
     * @param obj    Objeto da classe <code>ContratoCondicaoPagamentoVO</code> que será removido no banco de dados.
     * @exception Execption Caso haja problemas de conexão ou restrição de acesso.
     */
    public void excluir(ContratoCondicaoPagamentoVO obj) throws Exception {
        excluir(getIdEntidade());
        String sql = "DELETE FROM ContratoCondicaoPagamento WHERE ((codigo = ?))";
        PreparedStatement sqlExcluir = con.prepareStatement(sql);
        sqlExcluir.setInt(1, obj.getCodigo().intValue());
        sqlExcluir.execute();
    }

    /**
     * Responsável por realizar uma consulta de <code>ContratoCondicaoPagamento</code> através do valor do atributo
     * <code>descricao</code> da classe <code>CondicaoPagamento</code>
     * Faz uso da operação <code>montarDadosConsulta</code> que realiza o trabalho de prerarar o List resultante.
     * @return  List Contendo vários objetos da classe <code>ContratoCondicaoPagamentoVO</code> resultantes da consulta.
     * @exception Execption Caso haja problemas de conexão ou restrição de acesso.
     */
    public List consultarPorDescricaoCondicaoPagamento(String valorConsulta, int nivelMontarDados) throws Exception {
        consultar(getIdEntidade(), true);
        String sqlStr = "SELECT ContratoCondicaoPagamento.* FROM ContratoCondicaoPagamento, CondicaoPagamento WHERE ContratoCondicaoPagamento.condicaoPagamento = CondicaoPagamento.codigo and upper( CondicaoPagamento.descricao ) like('" + valorConsulta.toUpperCase() + "%') ORDER BY CondicaoPagamento.descricao";
        Statement stm = con.createStatement();
        ResultSet tabelaResultado = stm.executeQuery(sqlStr);
        return montarDadosConsulta(tabelaResultado, nivelMontarDados, this.con);
    }

    /**
     * Responsável por realizar uma consulta de <code>ContratoCondicaoPagamento</code> através do valor do atributo
     * <code>Integer codigo</code>. Retorna os objetos com valores iguais ou superiores ao parâmetro fornecido.
     * Faz uso da operação <code>montarDadosConsulta</code> que realiza o trabalho de prerarar o List resultante.
     * @param   controlarAcesso Indica se a aplicação deverá verificar se o usuário possui permissão para esta consulta ou não.
     * @return  List Contendo vários objetos da classe <code>ContratoCondicaoPagamentoVO</code> resultantes da consulta.
     * @exception Exception Caso haja problemas de conexão ou restrição de acesso.
     */
    public List consultarPorCodigo(Integer valorConsulta, boolean controlarAcesso, int nivelMontarDados) throws Exception {
        consultar(getIdEntidade(), controlarAcesso);
        String sqlStr = "SELECT * FROM ContratoCondicaoPagamento WHERE codigo >= " + valorConsulta.intValue() + " ORDER BY codigo";
        Statement stm = con.createStatement();
        ResultSet tabelaResultado = stm.executeQuery(sqlStr);
        return (montarDadosConsulta(tabelaResultado, nivelMontarDados, this.con));
    }

    /**
     * Responsável por montar os dados de vários objetos, resultantes de uma consulta ao banco de dados (<code>ResultSet</code>).
     * Faz uso da operação <code>montarDados</code> que realiza o trabalho para um objeto por vez.
     * @return  List Contendo vários objetos da classe <code>ContratoCondicaoPagamentoVO</code> resultantes da consulta.
     */
    public static List montarDadosConsulta(ResultSet tabelaResultado, int nivelMontarDados,Connection con) throws Exception {
        List vetResultado = new ArrayList();
        while (tabelaResultado.next()) {
            ContratoCondicaoPagamentoVO obj = new ContratoCondicaoPagamentoVO();
            obj = montarDados(tabelaResultado, nivelMontarDados, con);
            vetResultado.add(obj);
        }
        return vetResultado;
    }

    /**
     * Responsável por montar os dados resultantes de uma consulta ao banco de dados (<code>ResultSet</code>)
     * em um objeto da classe <code>ContratoCondicaoPagamentoVO</code>.
     * @return  O objeto da classe <code>ContratoCondicaoPagamentoVO</code> com os dados devidamente montados.
     */
    public static ContratoCondicaoPagamentoVO montarDados(ResultSet dadosSQL, int nivelMontarDados, Connection con) throws Exception {
        ContratoCondicaoPagamentoVO obj = new ContratoCondicaoPagamentoVO();
        obj.setCodigo(new Integer(dadosSQL.getInt("codigo")));
        obj.setContrato(new Integer(dadosSQL.getInt("contrato")));
        obj.getCondicaoPagamento().setCodigo(new Integer(dadosSQL.getInt("condicaoPagamento")));
        obj.setTipoValor(dadosSQL.getString("tipoValor"));
        obj.setTipoOperacao(dadosSQL.getString("tipoOperacao"));
        obj.setPercentualDesconto(new Double(dadosSQL.getDouble("percentualDesconto")));
        obj.setValorEspecifico(new Double(dadosSQL.getDouble("valorEspecifico")));
        obj.setNovoObj(new Boolean(false));
        if (nivelMontarDados == Uteis.NIVELMONTARDADOS_DADOSBASICOS) {
            return obj;
        }

        montarDadosCondicaoPagamento(obj, Uteis.NIVELMONTARDADOS_DADOSBASICOS, con);
        return obj;
    }

    /**
     * Operação responsável por montar os dados de um objeto da classe <code>CondicaoPagamentoVO</code> relacionado ao objeto <code>ContratoCondicaoPagamentoVO</code>.
     * Faz uso da chave primária da classe <code>CondicaoPagamentoVO</code> para realizar a consulta.
     * @param obj  Objeto no qual será montado os dados consultados.
     */
    public static void montarDadosCondicaoPagamento(ContratoCondicaoPagamentoVO obj, int nivelMontarDados, Connection con) throws Exception {
        if (obj.getCondicaoPagamento().getCodigo().intValue() == 0) {
            obj.setCondicaoPagamento(new CondicaoPagamentoVO());
            return;
        }
        CondicaoPagamento condicaoPagamento = new CondicaoPagamento(con);
        obj.setCondicaoPagamento(condicaoPagamento.consultarPorChavePrimaria(obj.getCondicaoPagamento().getCodigo(), nivelMontarDados));
        condicaoPagamento = null;
    }

    /**
     * Operação responsável por consultar todos os <code>ContratoCondicaoPagamentoVO</code> relacionados a um objeto da classe <code>plano.Plano</code>.
     * @param plano  Atributo de <code>plano.Plano</code> a ser utilizado para localizar os objetos da classe <code>ContratoCondicaoPagamentoVO</code>.
     * @return List  Contendo todos os objetos da classe <code>ContratoCondicaoPagamentoVO</code> resultantes da consulta.
     * @exception Exception  Erro de conexão com o BD ou restrição de acesso a esta operação.
     */
    public ContratoCondicaoPagamentoVO consultarContratoCondicaoPagamentos(Integer contrato, int nivelMontarDados) throws Exception {
        consultar(getIdEntidade());
        List objetos = new ArrayList();
        String sql = "SELECT * FROM ContratoCondicaoPagamento WHERE contrato = ?";
        PreparedStatement sqlConsulta = con.prepareStatement(sql);
        sqlConsulta.setInt(1, contrato.intValue());
        ResultSet resultado = sqlConsulta.executeQuery();
        if (!resultado.next()) {
            throw new ConsistirException("Dados Não Encontrados ( ContratoCondicaoPagamento ).");
        }
        return (montarDados(resultado, nivelMontarDados, this.con));
    }

    /**
     * Operação responsável por localizar um objeto da classe <code>ContratoCondicaoPagamentoVO</code>
     * através de sua chave primária.
     * @exception Exception Caso haja problemas de conexão ou localização do objeto procurado.
     */
    public ContratoCondicaoPagamentoVO consultarPorChavePrimaria(Integer codigoPrm, int nivelMontarDados) throws Exception {
        consultar(getIdEntidade(), false);
        String sql = "SELECT * FROM ContratoCondicaoPagamento WHERE codigo = ?";
        PreparedStatement sqlConsultar = con.prepareStatement(sql);
        sqlConsultar.setInt(1, codigoPrm.intValue());
        ResultSet tabelaResultado = sqlConsultar.executeQuery();
        if (!tabelaResultado.next()) {
            throw new ConsistirException("Dados Não Encontrados ( ContratoCondicaoPagamento ).");
        }
        return (montarDados(tabelaResultado, nivelMontarDados, this.con));
    }
    
}

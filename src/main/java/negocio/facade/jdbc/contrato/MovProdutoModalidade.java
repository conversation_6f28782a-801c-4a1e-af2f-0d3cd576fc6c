package negocio.facade.jdbc.contrato;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.List;

import javax.servlet.http.HttpSession;

import negocio.comuns.contrato.ContratoModalidadeVO;
import negocio.comuns.contrato.ContratoVO;
import negocio.comuns.contrato.MovProdutoModalidadeVO;
import negocio.comuns.contrato.MovProdutoVO;
import negocio.comuns.utilitarias.Calendario;
import negocio.comuns.utilitarias.Uteis;
import negocio.facade.jdbc.arquitetura.SuperEntidade;
import negocio.facade.jdbc.plano.Modalidade;
import negocio.interfaces.contrato.MovProdutoModalidadeInterfaceFacade;

public class MovProdutoModalidade extends SuperEntidade implements MovProdutoModalidadeInterfaceFacade {

    public MovProdutoModalidade() throws Exception {
        super();
    }

    public MovProdutoModalidade(Connection con) throws Exception {
        super(con);
    }

    public MovProdutoModalidade(HttpSession session) throws Exception {
        super(session);
    }

    @Override
    public void alterar(MovProdutoModalidadeVO obj) throws Exception {
        StringBuilder sql = new StringBuilder();
        sql.append("UPDATE movprodutomodalidade SET modalidade = ?, movproduto = ?, valor = ?, datainicio = ?, datafim = ? , contrato=?  ");
        sql.append("WHERE codigo = ?");

        PreparedStatement stm = con.prepareStatement(sql.toString());
        int i = 1;
        stm.setInt(i++, obj.getModalidadeVO().getCodigo());
        stm.setInt(i++, obj.getMovProdutoVO().getCodigo());
        stm.setDouble(i++, obj.getValor());
        stm.setDate(i++, Uteis.getDataJDBC(obj.getDataInicio()));
        stm.setDate(i++, Uteis.getDataJDBC(obj.getDataFim()));
        stm = MovProdutoModalidade.resolveFKNull(stm, i++, obj.getContrato().getCodigo());
        stm.setInt(i++, obj.getCodigo());

        stm.execute();

    }

    @Override
    public MovProdutoModalidadeVO consultarPorChavePrimaria(Integer codigo, int nivelMontarDados) throws Exception {
        String sql = "SELECT * FROM movprodutomodalidade WHERE codigo = " + codigo;
        ResultSet dados = criarConsulta(sql, con);
        if (dados.next()) {
            return montarDados(dados, nivelMontarDados, con);
        } else {
            return new MovProdutoModalidadeVO();
        }
    }

    @Override
    public List<MovProdutoModalidadeVO> consultarPorMovProduto(Integer movProduto, int nivelMontarDados) throws Exception {
        String sql = "SELECT * FROM movprodutomodalidade WHERE movproduto = " + movProduto;
        ResultSet dados = criarConsulta(sql, con);
        return montarDadosConsulta(dados, nivelMontarDados, con);
    }

    public void excluirPorMovProduto(MovProdutoVO obj, List<ContratoModalidadeVO> modalidades) throws Exception {
        String sql = "DELETE FROM movprodutomodalidade WHERE movproduto = " + obj.getCodigo();
        if (modalidades != null && !modalidades.isEmpty()) {
            String codigos = obterCodigosConcatenados(modalidades);
            sql = sql + " AND modalidade IN (" + codigos + ")";
        }
        executarConsulta(sql, con);
    }

    /**
     * <AUTHOR> Alcides
     * 16/08/2012
     */
    public static String obterCodigosConcatenados(List<ContratoModalidadeVO> modalidades) {
        String codigos = "";
        for (ContratoModalidadeVO mod : modalidades) {
            codigos += "," + mod.getModalidade().getCodigo();
        }
        return codigos.replaceFirst(",", "");
    }

    @Override
    public void incluir(MovProdutoModalidadeVO obj) throws Exception {
    	ResultSet resultSet = criarConsulta("SELECT * FROM movprodutomodalidade where contrato = "+obj.getContrato().getCodigo()
    			+" and movproduto = "+obj.getMovProdutoVO().getCodigo()+ " and modalidade = "+obj.getModalidadeVO().getCodigo(), con);
    	if(resultSet.next()){
    		MovProdutoModalidadeVO movPrdMod = montarDados(resultSet, Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA, con);
    		movPrdMod.setValor(Uteis.arredondarForcando2CasasDecimais(movPrdMod.getValor()+obj.getValor()));
    		alterar(movPrdMod);
    	}else{
    		StringBuilder sql = new StringBuilder();
            sql.append("INSERT INTO movprodutomodalidade(modalidade, movproduto, valor, datainicio, datafim, contrato)   ");
            sql.append("VALUES (?,?,?,?,?,?) ");

            PreparedStatement stm = con.prepareStatement(sql.toString());
            int i = 1;
            stm.setInt(i++, obj.getModalidadeVO().getCodigo());
            stm.setInt(i++, obj.getMovProdutoVO().getCodigo());
            stm.setDouble(i++, obj.getValor());
            stm.setDate(i++, Uteis.getDataJDBC(obj.getDataInicio()));
            stm.setDate(i++, Uteis.getDataJDBC(obj.getDataFim()));
            stm = MovProdutoModalidade.resolveFKNull(stm, i++, obj.getContrato().getCodigo());
            stm.execute();
            obj.setCodigo(obterValorChavePrimariaCodigo());	
    	}
    	
        
    }

    public static MovProdutoModalidadeVO montarDados(ResultSet dados, int nivelMontarDados, Connection con) throws Exception {
        MovProdutoModalidadeVO movProdutoModalidadeVO = new MovProdutoModalidadeVO();
        movProdutoModalidadeVO.setCodigo(dados.getInt("codigo"));
        movProdutoModalidadeVO.getModalidadeVO().setCodigo(dados.getInt("modalidade"));
        movProdutoModalidadeVO.getMovProdutoVO().setCodigo(dados.getInt("movproduto"));
        movProdutoModalidadeVO.setValor(dados.getDouble("valor"));
        movProdutoModalidadeVO.setDataFim(dados.getDate("datafim"));
        movProdutoModalidadeVO.setDataInicio(dados.getDate("datainicio"));
        movProdutoModalidadeVO.getContrato().setCodigo(dados.getInt("contrato"));
        if (nivelMontarDados == Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA) {
            return movProdutoModalidadeVO;
        }
        Modalidade mod = new Modalidade(con);
        movProdutoModalidadeVO.setModalidadeVO(mod.consultarPorChavePrimaria(movProdutoModalidadeVO.getModalidadeVO().getCodigo(), nivelMontarDados));
        mod = null;
        if (nivelMontarDados == Uteis.NIVELMONTARDADOS_TELACONSULTA){
            return movProdutoModalidadeVO;
        }
        MovProduto mp = new MovProduto(con);
        movProdutoModalidadeVO.setMovProdutoVO(mp.consultarPorChavePrimaria(movProdutoModalidadeVO.getMovProdutoVO().getCodigo(), nivelMontarDados));
        mp = null;
        Contrato ct = new Contrato(con);
        ct.consultarPorChavePrimaria(movProdutoModalidadeVO.getContrato().getCodigo(), nivelMontarDados);
        ct = null;
        return movProdutoModalidadeVO;
    }

    public static List<MovProdutoModalidadeVO> montarDadosConsulta(ResultSet dados, int nivelMontarDados, Connection con) throws Exception {
        List<MovProdutoModalidadeVO> vetResultado = new ArrayList<MovProdutoModalidadeVO>();
        while (dados.next()) {
            MovProdutoModalidadeVO obj = new MovProdutoModalidadeVO();
            obj = montarDados(dados, nivelMontarDados, con);
            vetResultado.add(obj);
        }
        return vetResultado;
    }

    @Override
    public void incluir(MovProdutoVO obj) throws Exception {
        incluirLista(obj.getCodigo(), obj.getMovProdutoModalidades());

    }

    /**
     * Responsável por
     * <AUTHOR> Alcides
     * 16/08/2012
     */
    private void incluirLista(Integer codigoMovP, List<MovProdutoModalidadeVO> movPModalidades) throws Exception {
        for (MovProdutoModalidadeVO mpm : movPModalidades) {
            mpm.getMovProdutoVO().setCodigo(codigoMovP);
            incluir(mpm);
        }
    }

    public void realizarManutencaoMovProdutoModalidade(List<ContratoModalidadeVO> modalidadesAdicionadas,
    		List<ContratoModalidadeVO> modalidadesRetiradas, ContratoVO contratoNovo, ContratoVO contratoAntigo) throws Exception {
        String sql = "select * from movproduto INNER JOIN produto ON produto.codigo = movproduto.produto AND produto.tipoproduto LIKE 'PM' "
                + " where contrato = " + contratoNovo.getCodigo()
                + " order by anoreferencia, mesreferencia";

        //obter produtos plano mensal do contrato
        List<MovProdutoVO> produtosDoContrato = MovProduto.montarDadosConsulta(criarConsulta(sql, con), Uteis.NIVELMONTARDADOS_TODOS, con);
        boolean alterar = false;

        Double valorModalidadesExcluidas = 0.0;
        Double valorSomaModalidadesAdicionadas = getValorSomaContratoModalidade(modalidadesAdicionadas);
        String competencia = "";
        int diaAtual = Uteis.getDiaMesData(Calendario.hoje());
        int diaLancamento = Uteis.getDiaMesData(contratoNovo.getVigenciaDe());
        if(diaAtual < diaLancamento){
        	Date data = Uteis.somarCampoData(Calendario.hoje(), Calendar.MONTH, -1);
        	competencia = Uteis.getMesReferenciaData(data);
        }else
        	competencia = Uteis.getDataMesAnoConcatenado();
        int nrDiasMes = Uteis.obterNumeroDiasDoMes(Uteis.obterDataAnterior(Uteis.getMesData(Calendario.hoje())));
        int nrDiasUtilizadosMes = Uteis.calcularDiasUtilizadosNoMes(contratoNovo.getVigenciaDe(), nrDiasMes);

        //iterar nos produtos
        for (MovProdutoVO mpd : produtosDoContrato) {
        	Double valorAntigo = mpd.getTotalFinal();
        	for(Object obj : contratoAntigo.getMovProdutoVOs()){
        		MovProdutoVO mp = (MovProdutoVO) obj;
        		if(mpd.getCodigo().equals(mp.getCodigo())){
        			valorAntigo = mp.getTotalFinal();
        		}
        	}
        	valorModalidadesExcluidas = totalizarModalidadesNoProduto(modalidadesRetiradas, mpd, valorAntigo);
            //se for o produto do mês vigente
            if (mpd.getMesReferencia().equals(competencia)) {
                //daqui pra frente excluir o relacionamento entre as modalidades retiradas e os produtos
            	alterar = true;
                //verificar o valor das modalidades nesse produto
                
                Double valorADividir = atualizarValorComBaseDiasUtilizados(mpd, modalidadesRetiradas, 
                		contratoNovo, nrDiasUtilizadosMes, nrDiasMes);
                List<MovProdutoModalidadeVO> movProdutoModalidades =
                        getFacade().getZWFacade().gerarMovProdutoModalidade(valorSomaModalidadesAdicionadas,
                        Calendario.hoje(), contratoNovo.getVigenciaAteAjustada(), modalidadesAdicionadas, valorADividir);
                incluirLista(mpd.getCodigo(), movProdutoModalidades);
                continue;
            }
            if (alterar) {
            	if(!modalidadesRetiradas.isEmpty()){
            		excluirPorMovProduto(mpd, modalidadesRetiradas);
            	}
                List<MovProdutoModalidadeVO> movProdutoModalidades = getFacade().getZWFacade().gerarMovProdutoModalidade(valorSomaModalidadesAdicionadas,
                        Calendario.hoje(), contratoNovo.getVigenciaAteAjustada(), modalidadesAdicionadas, valorModalidadesExcluidas);
                incluirLista(mpd.getCodigo(), movProdutoModalidades);
            }
        }

        if (modalidadesRetiradas.size() > 0) {
            atualizarDataFimUsoModalidades(contratoNovo, Calendario.hoje(), modalidadesRetiradas);
        }
        
        getFacade().getMovProduto().atualizarDescricaoMovProdutoModalidade(contratoNovo.getCodigo());
    }

    public Double atualizarValorComBaseDiasUtilizados(MovProdutoVO movProd, List<ContratoModalidadeVO> modalidadesRetiradas, 
    		ContratoVO contrato, int nrDiasUtilizadosMes, int nrDiasMes) throws SQLException, Exception {
        Double valorRestante = 0.0;
        for (ContratoModalidadeVO contMod : modalidadesRetiradas) {
            String select = "SELECT valor FROM movprodutomodalidade WHERE movproduto = " + movProd.getCodigo() + " AND modalidade = " + contMod.getModalidade().getCodigo();
            ResultSet rs = criarConsulta(select, con);
            while(rs.next()){
	            Double valorOriginal = rs.getDouble("valor");
	            Double valor = (valorOriginal / nrDiasMes) * nrDiasUtilizadosMes;
	            valorRestante += (movProd.getTotalFinal() - valor);
	            String update = "UPDATE movprodutomodalidade SET valor = " + valor
	                    + " WHERE movproduto = " + movProd.getCodigo() + " AND modalidade = " + contMod.getModalidade().getCodigo();
	            executarConsulta(update, con);
	         }
        }
        return valorRestante;
    }

    /**
     * Responsável por totalizar o valor que um conjunto de modalidades representa para um produto
     * <AUTHOR> Alcides
     * 16/08/2012
     */
    public Double totalizarModalidadesNoProduto(List<ContratoModalidadeVO> modalidades, MovProdutoVO movProduto, Double valorAntigo) {
        Double total = 0.0;
        //iterar nas modalidades desejadas
        for (ContratoModalidadeVO mod : modalidades) {
            //iterar nas modalidades do produto
            for (MovProdutoModalidadeVO movPMod : movProduto.getMovProdutoModalidades()) {
                //se for a mesma, somar ao valor total
                if (movPMod.getModalidadeVO().getCodigo().equals(mod.getModalidade().getCodigo())) {
                    total += movPMod.getValor();
                }
            }
        }
        total = (total*movProduto.getTotalFinal());
        if(valorAntigo != 0.0) {
        	total /= valorAntigo;
        }
        return total;
    }

    /**
     * <AUTHOR> Alcides
     * 16/08/2012
     */
    public static Double getValorSomaContratoModalidade(List<ContratoModalidadeVO> modalidades) {
        Double valor = 0.0;
        for (ContratoModalidadeVO conMod : modalidades) {
            if (conMod.getModalidade().getModalidadeEscolhida()) {
                valor += conMod.getValorFinalModalidade();
            }
        }
        return valor;
    }

    /* (non-Javadoc)
     * @see negocio.interfaces.contrato.MovProdutoModalidadeInterfaceFacade#atualizarDataFimUsoModalidades(negocio.comuns.contrato.ContratoVO, java.util.Date, java.util.List)
     */
    public void atualizarDataFimUsoModalidades(ContratoVO contrato, Date dataFim, List<ContratoModalidadeVO> modalidades) throws Exception {

        StringBuilder sql = new StringBuilder();

        //atualizar data de fim de uso das modalidades num contrato
        sql.append("UPDATE movprodutomodalidade SET datafim = ? ");
        sql.append("WHERE movproduto IN (SELECT codigo FROM movproduto WHERE contrato = ? ) ");
        sql.append("AND modalidade IN (" + obterCodigosConcatenados(modalidades) + ")");

        PreparedStatement stm = con.prepareStatement(sql.toString());
        stm.setDate(1, Uteis.getDataJDBC(dataFim));
        stm.setInt(2, contrato.getCodigo());

        stm.execute();
    }

    /* (non-Javadoc)
     * @see negocio.interfaces.contrato.MovProdutoModalidadeInterfaceFacade#atualizarValorModalidadesNoProduto(java.lang.Double, java.lang.Double, negocio.comuns.contrato.MovProdutoVO, java.util.List)
     */
    public void atualizarValorModalidadesNoProduto(Double totalDasModalidades, Double valorADividir, MovProdutoVO produto, List<ContratoModalidadeVO> modalidades) throws Exception {
        for (ContratoModalidadeVO mod : modalidades) {
            Double novoValor = Uteis.arredondarForcando2CasasDecimais(valorADividir * (mod.getValorFinalModalidade() / totalDasModalidades));
            StringBuilder sql = new StringBuilder();
            //atualizar o valor usado da modalidade naquele mes
            sql.append("UPDATE movprodutomodalidade SET valor = ? ");
            sql.append("WHERE movproduto = ? AND modalidade = ? ");
            PreparedStatement stm = con.prepareStatement(sql.toString());
            stm.setDouble(1, novoValor);
            stm.setInt(2, produto.getCodigo());
            stm.setInt(3, mod.getModalidade().getCodigo());

            stm.execute();
        }
    }

    public boolean validarExisteMovProdModalidade(int contrato) throws SQLException {
        String sql = "SELECT EXISTS(SELECT * FROM movprodutomodalidade mm, movproduto m WHERE mm.movproduto = m.codigo AND m.contrato = ?) AS existe";
        PreparedStatement stm = con.prepareStatement(sql);
        stm.setInt(1, contrato);
        ResultSet resultSet = stm.executeQuery();
        resultSet.next();
        return resultSet.getBoolean("existe");
    }
    
    public boolean selectMigradorMOVProdutoModalidade() throws SQLException {
        String sql = "SELECT migrador_movprodutomodalidade();";

        PreparedStatement preparedStatement = con.prepareStatement(sql);

        return preparedStatement.execute();
    }
}

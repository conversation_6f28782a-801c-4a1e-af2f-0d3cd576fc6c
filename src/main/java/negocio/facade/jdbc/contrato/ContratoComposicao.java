/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package negocio.facade.jdbc.contrato;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.Statement;
import java.util.ArrayList;
import java.util.Iterator;
import java.util.List;
import negocio.comuns.contrato.ContratoComposicaoVO;
import negocio.comuns.plano.ComposicaoVO;
import negocio.comuns.utilitarias.ConsistirException;
import negocio.comuns.utilitarias.Uteis;
import negocio.facade.jdbc.arquitetura.SuperEntidade;
import negocio.facade.jdbc.plano.Composicao;

/**
 *
 * <AUTHOR>
 */
public class ContratoComposicao extends SuperEntidade {    

    public ContratoComposicao() throws Exception {
        super();
        setIdEntidade("Contrato");
    }
    public ContratoComposicao(Connection conexao) throws Exception {
        super(conexao);
        setIdEntidade("Contrato");
    }

    /**
     * Operação responsável por retornar um novo objeto da classe <code>ContratoComposicaoVO</code>.
     */
    public ContratoComposicaoVO novo() throws Exception {
        incluir(getIdEntidade());
        ContratoComposicaoVO obj = new ContratoComposicaoVO();
        return obj;
    }

    /**
     * Operação responsável por incluir no banco de dados um objeto da classe <code>ContratoComposicaoVO</code>.
     * Primeiramente valida os dados (<code>validarDados</code>) do objeto. Verifica a conexão com o banco de dados e a permissão do usuário
     * para realizar esta operacão na entidade.
     * Isto, através da operação <code>incluir</code> da superclasse.
     * @param obj  Objeto da classe <code>ContratoComposicaoVO</code> que será gravado no banco de dados.
     * @exception Exception Caso haja problemas de conexão, restrição de acesso ou validação de dados.
     */
    public void incluir(ContratoComposicaoVO obj) throws Exception {
        ContratoComposicaoVO.validarDados(obj);
        incluir(getIdEntidade());
        String sql = "INSERT INTO ContratoComposicao( contrato, composicao ) VALUES (?,? )";
        PreparedStatement sqlInserir = con.prepareStatement(sql);
        if (obj.getContrato().intValue() != 0) {
            sqlInserir.setInt(1, obj.getContrato().intValue());
        } else {
            sqlInserir.setNull(1, 0);
        }
        if (obj.getComposicaoVO().getCodigo().intValue() != 0) {
            sqlInserir.setInt(2, obj.getComposicaoVO().getCodigo().intValue());
        } else {
            sqlInserir.setNull(2, 0);
        }
        sqlInserir.execute();
        obj.setCodigo(obterValorChavePrimariaCodigo());
        obj.setNovoObj(new Boolean(false));
    }

    /**
     * Operação responsável por alterar no BD os dados de um objeto da classe <code>ContratoComposicaoVO</code>.
     * Sempre utiliza a chave primária da classe como atributo para localização do registro a ser alterado.
     * Primeiramente valida os dados (<code>validarDados</code>) do objeto. Verifica a conexão com o banco de dados e a permissão do usuário
     * para realizar esta operacão na entidade.
     * Isto, através da operação <code>alterar</code> da superclasse.
     * @param obj    Objeto da classe <code>ContratoComposicaoVO</code> que será alterada no banco de dados.
     * @exception Execption Caso haja problemas de conexão, restrição de acesso ou validação de dados.
     */
    public void alterar(ContratoComposicaoVO obj) throws Exception {
        ContratoComposicaoVO.validarDados(obj);
        alterar(getIdEntidade());
        String sql = "UPDATE ContratoComposicao set contrato=?,composicao=? WHERE ((codigo = ?))";
        PreparedStatement sqlAlterar = con.prepareStatement(sql);
        if (obj.getContrato().intValue() != 0) {
            sqlAlterar.setInt(1, obj.getContrato().intValue());
        } else {
            sqlAlterar.setNull(1, 0);
        }
        if (obj.getComposicaoVO().getCodigo().intValue() != 0) {
            sqlAlterar.setInt(2, obj.getComposicaoVO().getCodigo().intValue());
        } else {
            sqlAlterar.setNull(2, 0);
        }
        sqlAlterar.setInt(3, obj.getCodigo().intValue());
        sqlAlterar.execute();
    }

    /**
     * Operação responsável por excluir no BD um objeto da classe <code>ContratoComposicaoVO</code>.
     * Sempre localiza o registro a ser excluído através da chave primária da entidade.
     * Primeiramente verifica a conexão com o banco de dados e a permissão do usuário
     * para realizar esta operacão na entidade.
     * Isto, através da operação <code>excluir</code> da superclasse.
     * @param obj    Objeto da classe <code>ContratoComposicaoVO</code> que será removido no banco de dados.
     * @exception Execption Caso haja problemas de conexão ou restrição de acesso.
     */
    public void excluir(ContratoComposicaoVO obj) throws Exception {
        excluir(getIdEntidade());
        String sql = "DELETE FROM ContratoComposicao WHERE ((codigo = ?))";
        PreparedStatement sqlExcluir = con.prepareStatement(sql);
        sqlExcluir.setInt(1, obj.getCodigo().intValue());
        sqlExcluir.execute();
    }

    public void excluirContratoComposicaos(ContratoComposicaoVO obj) throws Exception {
        String sql = "DELETE FROM ContratoComposicao WHERE (contrato = ?)";
        PreparedStatement sqlExcluir = con.prepareStatement(sql);
        sqlExcluir.setInt(1, obj.getContrato().intValue());
        sqlExcluir.execute();
    }

    /**
     * Responsável por realizar uma consulta de <code>ContratoComposicao</code> através do valor do atributo
     * <code>nome</code> da classe <code>Modalidade</code>
     * Faz uso da operação <code>montarDadosConsulta</code> que realiza o trabalho de prerarar o List resultante.
     * @return  List Contendo vários objetos da classe <code>ContratoComposicaoVO</code> resultantes da consulta.
     * @exception Execption Caso haja problemas de conexão ou restrição de acesso.
     */
    public List consultarPorNomeComposicao(String valorConsulta, int nivelMontarDados) throws Exception {
        consultar(getIdEntidade(), true);
        String sqlStr = "SELECT ContratoComposicao.* FROM ContratoComposicao, Composicao WHERE ContratoComposicao. = Modalidade.codigo and upper( Modalidade.nome ) like('" + valorConsulta.toUpperCase() + "%') ORDER BY Modalidade.nome";
        Statement stm = con.createStatement();
        ResultSet tabelaResultado = stm.executeQuery(sqlStr);
        return montarDadosConsulta(tabelaResultado, nivelMontarDados, this.con);
    }

    /**
     * Responsável por realizar uma consulta de <code>ContratoComposicao</code> através do valor do atributo
     * <code>codigo</code> da classe <code>Contrato</code>
     * Faz uso da operação <code>montarDadosConsulta</code> que realiza o trabalho de prerarar o List resultante.
     * @return  List Contendo vários objetos da classe <code>ContratoComposicaoVO</code> resultantes da consulta.
     * @exception Exception Caso haja problemas de conexão ou restrição de acesso.
     */
    public List consultarPorCodigoContrato(Integer valorConsulta, int nivelMontarDados) throws Exception {
        consultar(getIdEntidade(), true);
        String sqlStr = "SELECT ContratoComposicao.* FROM ContratoComposicao, Contrato WHERE ContratoComposicao.contrato = Contrato.codigo and Contrato.codigo >= " + valorConsulta.intValue() + " ORDER BY Contrato.codigo";
        Statement stm = con.createStatement();
        ResultSet tabelaResultado = stm.executeQuery(sqlStr);
        return montarDadosConsulta(tabelaResultado, nivelMontarDados, this.con);
    }



    public ContratoComposicaoVO consultaPorCodigoContrato(Integer valorConsulta, int nivelMontarDados) throws Exception {
        //consultar(getIdEntidade(), true);
        String sqlStr = "SELECT ContratoComposicao.* FROM ContratoComposicao WHERE ContratoComposicao.contrato = " + valorConsulta;
        Statement stm = con.createStatement();
        ResultSet tabelaResultado = stm.executeQuery(sqlStr);
        if (tabelaResultado.next()){
            return montarDados(tabelaResultado, nivelMontarDados, this.con);
        }
        return null;
    }

    /**
     * Responsável por realizar uma consulta de <code>ContratoComposicao</code> através do valor do atributo
     * <code>Integer codigo</code>. Retorna os objetos com valores iguais ou superiores ao parâmetro fornecido.
     * Faz uso da operação <code>montarDadosConsulta</code> que realiza o trabalho de prerarar o List resultante.
     * @param   controlarAcesso Indica se a aplicação deverá verificar se o usuário possui permissão para esta consulta ou não.
     * @return  List Contendo vários objetos da classe <code>ContratoComposicaoVO</code> resultantes da consulta.
     * @exception Exception Caso haja problemas de conexão ou restrição de acesso.
     */
    public List consultarPorCodigo(Integer valorConsulta, boolean controlarAcesso, int nivelMontarDados) throws Exception {
        consultar(getIdEntidade(), controlarAcesso);
        String sqlStr = "SELECT * FROM ContratoComposicao WHERE codigo >= " + valorConsulta.intValue() + " ORDER BY codigo";
        Statement stm = con.createStatement();
        ResultSet tabelaResultado = stm.executeQuery(sqlStr);
        return (montarDadosConsulta(tabelaResultado, nivelMontarDados, this.con));
    }

    /**
     * Responsável por montar os dados de vários objetos, resultantes de uma consulta ao banco de dados (<code>ResultSet</code>).
     * Faz uso da operação <code>montarDados</code> que realiza o trabalho para um objeto por vez.
     * @return  List Contendo vários objetos da classe <code>ContratoComposicaoVO</code> resultantes da consulta.
     */
    public static List montarDadosConsulta(ResultSet tabelaResultado, int nivelMontarDados, Connection con) throws Exception {
        List vetResultado = new ArrayList();
        while (tabelaResultado.next()) {
            ContratoComposicaoVO obj = new ContratoComposicaoVO();
            obj = montarDados(tabelaResultado, nivelMontarDados, con);
            vetResultado.add(obj);
        }
        return vetResultado;
    }

    /**
     * Responsável por montar os dados resultantes de uma consulta ao banco de dados (<code>ResultSet</code>)
     * em um objeto da classe <code>ContratoComposicaoVO</code>.
     * @return  O objeto da classe <code>ContratoComposicaoVO</code> com os dados devidamente montados.
     */
    public static ContratoComposicaoVO montarDados(ResultSet dadosSQL, int nivelMontarDados, Connection con) throws Exception {
        ContratoComposicaoVO obj = new ContratoComposicaoVO();
        obj.setCodigo(new Integer(dadosSQL.getInt("codigo")));
        obj.setContrato(new Integer(dadosSQL.getInt("contrato")));
        obj.getComposicaoVO().setCodigo(new Integer(dadosSQL.getInt("composicao")));
        if (nivelMontarDados == Uteis.NIVELMONTARDADOS_DADOSBASICOS) {
            return obj;
        }
        montarDadosComposicao(obj, Uteis.NIVELMONTARDADOS_TODOS, con);
        return obj;
    }

    /**
     * Operação responsável por montar os dados de um objeto da classe <code>ModalidadeVO</code> relacionado ao objeto <code>ContratoComposicaoVO</code>.
     * Faz uso da chave primária da classe <code>ModalidadeVO</code> para realizar a consulta.
     * @param obj  Objeto no qual será montado os dados consultados.
     */
    public static void montarDadosComposicao(ContratoComposicaoVO obj, int nivelMontarDados, Connection con) throws Exception {
        if (obj.getComposicaoVO().getCodigo().intValue() == 0) {
            obj.setComposicaoVO(new ComposicaoVO());
            return;
        }
        Composicao composicao = new Composicao(con);
        obj.setComposicaoVO(composicao.consultarPorChavePrimaria(obj.getComposicaoVO().getCodigo().intValue(), Uteis.NIVELMONTARDADOS_DADOSENTIDADESUBORDINADAS));
        composicao = null;
        obj.getComposicaoVO().setComposicaoEscolhida(true);
    }

    /**
     * Operação responsável por excluir todos os objetos da <code>ContratoComposicaoVO</code> no BD.
     * Faz uso da operação <code>excluir</code> disponível na classe <code>ContratoComposicao</code>.
     * @param <code>contrato</code> campo chave para exclusão dos objetos no BD.
     * @exception Exception  Erro de conexão com o BD ou restrição de acesso a esta operação.
     */
    public void excluirContratoComposicaos(Integer contrato) throws Exception {
        // ContratoComposicao.excluir(getIdEntidade());
        String sql = "DELETE FROM ContratoComposicao WHERE (contrato = ?)";
        PreparedStatement sqlExcluir = con.prepareStatement(sql);
        sqlExcluir.setInt(1, contrato.intValue());
        sqlExcluir.execute();
    }

    /**
     * Operação responsável por alterar todos os objetos da <code>ContratoComposicaoVO</code> contidos em um Hashtable no BD.
     * Faz uso da operação <code>excluirContratoComposicaos</code> e <code>incluirContratoComposicaos</code> disponíveis na classe <code>ContratoComposicao</code>.
     * @param objetos  List com os objetos a serem alterados ou incluídos no BD.
     * @exception Exception  Erro de conexão com o BD ou restrição de acesso a esta operação.
     */
    public void alterarContratoComposicaos(Integer contrato, List objetos) throws Exception {
        String str = "DELETE FROM ContratoComposicao WHERE contrato = " + contrato.intValue();
        Iterator i = objetos.iterator();
        while (i.hasNext()) {
            ContratoComposicaoVO objeto = (ContratoComposicaoVO) i.next();
            str += " AND codigo <> " + objeto.getCodigo().intValue();
        }
        PreparedStatement sqlExcluir = con.prepareStatement(str);
        sqlExcluir.execute();
        Iterator e = objetos.iterator();
        while (e.hasNext()) {
            ContratoComposicaoVO obj = (ContratoComposicaoVO) e.next();
            if (obj.getCodigo().equals(new Integer(0))) {
                obj.setContrato(contrato);
                incluir(obj);
            } else {
                alterar(obj);
            }
        }
    }

    /**
     * Operação responsável por incluir objetos da <code>ContratoComposicaoVO</code> no BD.
     * Garantindo o relacionamento com a entidade principal <code>contrato.Contrato</code> através do atributo de vínculo.
     * @param objetos List contendo os objetos a serem gravados no BD da classe.
     * @exception Exception  Erro de conexão com o BD ou restrição de acesso a esta operação.
     */
    public void incluirContratoComposicaos(Integer contratoPrm, List objetos) throws Exception {
        Iterator e = objetos.iterator();
        while (e.hasNext()) {
            ContratoComposicaoVO obj = (ContratoComposicaoVO) e.next();
            if (obj.getComposicaoVO().getComposicaoEscolhida()) {
                obj.setContrato(contratoPrm);
                incluir(obj);
            }

        }
    }

    /**
     * Operação responsável por consultar todos os <code>ContratoComposicaoVO</code> relacionados a um objeto da classe <code>contrato.Contrato</code>.
     * @param contrato  Atributo de <code>contrato.Contrato</code> a ser utilizado para localizar os objetos da classe <code>ContratoComposicaoVO</code>.
     * @return List  Contendo todos os objetos da classe <code>ContratoComposicaoVO</code> resultantes da consulta.
     * @exception Exception  Erro de conexão com o BD ou restrição de acesso a esta operação.
     */
    public List consultarContratoComposicaos(Integer contrato, int nivelMontarDados) throws Exception {
        consultar(getIdEntidade());
        List objetos = new ArrayList();
        String sql = "SELECT * FROM ContratoComposicao WHERE contrato = ?";
        PreparedStatement sqlConsulta = con.prepareStatement(sql);
        sqlConsulta.setInt(1, contrato.intValue());
        ResultSet resultado = sqlConsulta.executeQuery();
        while (resultado.next()) {
            ContratoComposicaoVO novoObj = ContratoComposicao.montarDados(resultado, nivelMontarDados, this.con);
            objetos.add(novoObj);
        }
        return objetos;
    }

    /**
     * Operação responsável por localizar um objeto da classe <code>ContratoComposicaoVO</code>
     * através de sua chave primária.
     * @exception Exception Caso haja problemas de conexão ou localização do objeto procurado.
     */
    public ContratoComposicaoVO consultarPorChavePrimaria(Integer codigoPrm, int nivelMontarDados) throws Exception {
        consultar(getIdEntidade(), false);
        String sql = "SELECT * FROM ContratoComposicao WHERE codigo = ?";
        PreparedStatement sqlConsultar = con.prepareStatement(sql);
        sqlConsultar.setInt(1, codigoPrm.intValue());
        ResultSet tabelaResultado = sqlConsultar.executeQuery();
        if (!tabelaResultado.next()) {
            throw new ConsistirException("Dados Não Encontrados ( ContratoComposicao ).");
        }
        return (montarDados(tabelaResultado, nivelMontarDados, this.con));
    }
   
}

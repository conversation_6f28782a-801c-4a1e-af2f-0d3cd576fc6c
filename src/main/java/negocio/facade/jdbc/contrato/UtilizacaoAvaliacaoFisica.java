package negocio.facade.jdbc.contrato;

import negocio.comuns.basico.ClienteVO;
import negocio.comuns.basico.EmpresaVO;
import negocio.comuns.contrato.UtilizacaoAvaliacaoFisicaVO;
import negocio.comuns.utilitarias.Uteis;
import negocio.facade.jdbc.arquitetura.SuperEntidade;
import negocio.facade.jdbc.basico.Cliente;
import negocio.interfaces.contrato.UtilizacaoAvaliacaoFisicaInterfaceFacade;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

public class UtilizacaoAvaliacaoFisica extends SuperEntidade implements UtilizacaoAvaliacaoFisicaInterfaceFacade {

    public UtilizacaoAvaliacaoFisica() throws Exception {
        super();
    }

    public UtilizacaoAvaliacaoFisica(Connection conexao) throws Exception {
        super(conexao);
    }


    public UtilizacaoAvaliacaoFisicaVO findByCodAvaliacao(Integer codAvaliacao) throws SQLException {
        String sql = "SELECT * FROM utilizacaoavaliacaofisica\n" +
                "WHERE avaliacaofisica = ?";

        try (PreparedStatement pstm = con.prepareStatement(sql)) {
            pstm.setInt(1, codAvaliacao);
            try (ResultSet rs = pstm.executeQuery()) {
                if (rs.next()) {
                    return montarDados(rs, this.con);
                }
            }
        }
        return null;
    }

    public List<UtilizacaoAvaliacaoFisicaVO> findByMovproduto(Integer codMovproduto) throws SQLException {
        String sql = "SELECT * FROM utilizacaoavaliacaofisica\n" +
                "WHERE movproduto = ?";

        try (PreparedStatement pstm = con.prepareStatement(sql)) {
            pstm.setInt(1, codMovproduto);
            try (ResultSet rs = pstm.executeQuery()) {
                return montarDadosDadosConsulta(rs, this.con);
            }
        }
    }

    private List<UtilizacaoAvaliacaoFisicaVO> montarDadosDadosConsulta(ResultSet rs, Connection con) throws SQLException {
        List<UtilizacaoAvaliacaoFisicaVO> utilizacoes = new ArrayList<>();
        while (rs.next()) {
            UtilizacaoAvaliacaoFisicaVO utilizacao = montarDados(rs, con);
            utilizacoes.add(utilizacao);
        }
        return utilizacoes;
    }

    private UtilizacaoAvaliacaoFisicaVO montarDados(ResultSet rs, Connection con) throws SQLException {
        UtilizacaoAvaliacaoFisicaVO utilizacaoAvaliacaoFisicaVO = new UtilizacaoAvaliacaoFisicaVO();
        utilizacaoAvaliacaoFisicaVO.setCodigo(rs.getInt("codigo"));
        utilizacaoAvaliacaoFisicaVO.setCodAvaliacaoFisica(rs.getInt("avaliacaofisica"));
        utilizacaoAvaliacaoFisicaVO.setDataAvaliacaoFisica(rs.getTimestamp("dataavaliacao"));
        utilizacaoAvaliacaoFisicaVO.getMovProdutoVO().setCodigo(rs.getInt("movproduto"));
        utilizacaoAvaliacaoFisicaVO.setPrimeiraavaliacao(rs.getBoolean("primeiraavaliacao"));
        utilizacaoAvaliacaoFisicaVO.setNovoObj(false);
        return utilizacaoAvaliacaoFisicaVO;
    }

    public void gravar(UtilizacaoAvaliacaoFisicaVO utilizacao) throws Exception {
        UtilizacaoAvaliacaoFisicaVO utilizacaoAvaliacaoFisicaVO = findByCodAvaliacao(utilizacao.getCodAvaliacaoFisica());

        if (utilizacaoAvaliacaoFisicaVO == null) {
            incluir(utilizacao);
            utilizacao.setNovoObj(false);
        } else {
            alterar(utilizacao);
        }
    }

    @Override
    public void incluir(UtilizacaoAvaliacaoFisicaVO utilizacao) throws Exception {
        String sql = "INSERT INTO utilizacaoavaliacaofisica(movproduto, avaliacaofisica, dataavaliacao, primeiraavaliacao) VALUES (?, ?, ?, ?);";
        try (PreparedStatement psm = con.prepareStatement(sql)) {
            int i = 0;
            psm.setInt(++i, utilizacao.getMovProdutoVO().getCodigo());
            psm.setInt(++i, utilizacao.getCodAvaliacaoFisica());
            psm.setTimestamp(++i, Uteis.getDataJDBCTimestamp(utilizacao.getDataAvaliacaoFisica()));
            psm.setBoolean(++i, utilizacao.getPrimeiraavaliacao());
            psm.execute();
            utilizacao.setCodigo(obterValorChavePrimariaCodigo());
        }
    }

    @Override
    public void alterar(UtilizacaoAvaliacaoFisicaVO utilizacao) throws SQLException {
        String sql = "UPDATE utilizacaoavaliacaofisica SET movproduto = ?, avaliacaofisica = ?, dataavaliacao = ?, primeiraavaliacao = ? WHERE codigo = ?;";
        try (PreparedStatement psm = con.prepareStatement(sql)) {
            int i = 0;
            psm.setInt(++i, utilizacao.getMovProdutoVO().getCodigo());
            psm.setInt(++i, utilizacao.getCodAvaliacaoFisica());
            psm.setTimestamp(++i, Uteis.getDataJDBCTimestamp(utilizacao.getDataAvaliacaoFisica()));
            psm.setInt(++i, utilizacao.getCodigo());
            psm.setBoolean(++i, utilizacao.getPrimeiraavaliacao());
        }
    }

    @Override
    public void excluirPorCodAvaliacaoFisica(Integer codAvaliacaoFisica) throws SQLException {
        String sql = "DELETE FROM utilizacaoavaliacaofisica WHERE avaliacaofisica = ?;";
        try (PreparedStatement psm = con.prepareStatement(sql)) {
            int i = 0;
            psm.setInt(++i, codAvaliacaoFisica);
            psm.execute();
        }
    }

    public List<UtilizacaoAvaliacaoFisicaVO> findByDataPrimeiraUtilizacao(Date dataInicio, Date dataFim, EmpresaVO empresa) throws Exception {
        String sql = "SELECT uaf.*, mprod.pessoa\n" +
                "FROM utilizacaoavaliacaofisica uaf\n" +
                "INNER JOIN movproduto mprod ON uaf.movproduto = mprod.codigo\n" +
                "WHERE primeiraavaliacao = true\n" +
                "AND mprod.empresa = ?\n" +
                "AND dataavaliacao BETWEEN ? AND ?;";
        try (PreparedStatement psm = con.prepareStatement(sql)) {
            int i = 0;
            psm.setInt(++i, empresa.getCodigo());
            psm.setDate(++i, Uteis.getDataJDBC(dataInicio));
            psm.setDate(++i, Uteis.getDataJDBC(dataFim));
            try (ResultSet rs = psm.executeQuery()) {
                List<UtilizacaoAvaliacaoFisicaVO> utilizacoes = new ArrayList<>();
                Cliente clienteDAO = new Cliente(this.con);
                while (rs.next()) {
                    Integer pessoa = rs.getInt("pessoa");
                    ClienteVO clienteVO = clienteDAO.consultarPorCodigoPessoa(pessoa, Uteis.NIVELMONTARDADOS_RELATORIO_SGP);
                    UtilizacaoAvaliacaoFisicaVO utilizacao = montarDados(rs, con);
                    utilizacao.setClienteVO(clienteVO);
                    utilizacoes.add(utilizacao);
                }
                return utilizacoes;
            }
        }
    }

}

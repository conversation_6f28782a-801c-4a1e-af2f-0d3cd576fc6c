package negocio.facade.jdbc.ia;

import br.com.pactosolucoes.oamd.controle.basico.DAO;
import negocio.comuns.ia.RiscoEvasaoVO;
import negocio.comuns.utilitarias.Uteis;
import negocio.facade.jdbc.arquitetura.SuperEntidade;
import negocio.facade.jdbc.utilitarias.Conexao;
import negocio.interfaces.ia.RiscoEvasaoInterfaceFacade;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;

public class RiscoEvasao extends SuperEntidade implements RiscoEvasaoInterfaceFacade {

    public RiscoEvasao() throws Exception {
        super();
    }

    public RiscoEvasao(Connection con) throws Exception {
        super(con);
    }

    public void incluir(RiscoEvasaoVO obj) throws Exception {
        try {
            con.setAutoCommit(false);
            incluirSemCommit(obj);
            con.commit();
        } catch (Exception e) {
            con.rollback();
            con.setAutoCommit(true);
            throw e;
        } finally {
            con.setAutoCommit(true);
        }
    }

    public void incluirSemCommit(RiscoEvasaoVO obj) throws Exception {
        StringBuilder sql = new StringBuilder("INSERT INTO RiscoEvasao\n")
                .append("(cliente, datapredicao, chancesair30dias, datapredicao30dias)\n")
                .append("VALUES (?, ?, ?, ?)");
        int i = 0;
        PreparedStatement sqlInserir = con.prepareStatement(sql.toString());
        sqlInserir.setInt(++i, obj.getCliente());
        sqlInserir.setTimestamp(++i, Uteis.getDataJDBCTimestamp(obj.getDataPredicao()));
        sqlInserir.setFloat(++i, obj.getChanceSair30Dias());
        sqlInserir.setTimestamp(++i, Uteis.getDataJDBCTimestamp(obj.getDataPredicao30Dias()));

        sqlInserir.execute();
        //obj.setCodigo(obterValorChavePrimariaCodigo());
        //obj.setNovoObj(false);
    }

    public void alterar(RiscoEvasaoVO obj) throws Exception {
        try {
            con.setAutoCommit(false);
            alterarSemCommit(obj);
            con.commit();
        } catch (Exception e) {
            con.rollback();
            con.setAutoCommit(true);
            throw e;
        } finally {
            con.setAutoCommit(true);
        }
    }

    public void alterarSemCommit(RiscoEvasaoVO obj) throws Exception {
        StringBuilder sql = new StringBuilder("UPDATE RiscoEvasao\n")
                .append("SET datapredicao = ?, chancesair30dias = ?, datapredicao30dias = ?\n")
                .append("WHERE codigo = ?");
        int i = 0;
        PreparedStatement sqlAlterar = con.prepareStatement(sql.toString());
        sqlAlterar.setTimestamp(++i, Uteis.getDataJDBCTimestamp(obj.getDataPredicao()));
        sqlAlterar.setFloat(++i, obj.getChanceSair30Dias());
        sqlAlterar.setTimestamp(++i, Uteis.getDataJDBCTimestamp(obj.getDataPredicao30Dias()));

        sqlAlterar.setInt(++i, obj.getCodigo());
        sqlAlterar.execute();
    }

    public void gravarSQL(String sql) throws Exception {
        try {
            con.setAutoCommit(false);
            PreparedStatement sqlGravar = con.prepareStatement(sql);
            sqlGravar.execute();
            con.commit();
        } catch (Exception e) {
            con.rollback();
            con.setAutoCommit(true);
            throw e;
        } finally {
            con.setAutoCommit(true);
        }
    }

    public static RiscoEvasaoVO montarDados(ResultSet rs, Connection con) throws Exception {
        RiscoEvasaoVO obj = new RiscoEvasaoVO();
        obj.setCodigo(rs.getInt("codigo"));
        obj.setNovoObj(false);
        obj.setCliente(rs.getInt("cliente"));
        obj.setDataPredicao(rs.getTimestamp("datapredicao"));
        obj.setChanceSair30Dias(rs.getFloat("chancesair30dias"));
        obj.setDataPredicao30Dias(rs.getTimestamp("datapredicao30dias"));

        return obj;
    }

    public static void main(String args[]) throws Exception {
        String chave = "c";
        if (args.length > 0) {
            chave = args[0];
            Uteis.logar(null, "Obter conexão para chave: " + chave);
        }

//        Connection con = DriverManager.getConnection("**************************************", "zillyonweb", "pactodb");

        Uteis.debug = true;
        Connection con = new DAO().obterConexaoEspecifica(chave);
        Conexao.guardarConexaoForJ2SE(chave, con);
        //RiscoEvasao riscoEvasao = new RiscoEvasao(con);
        //notaFiscal.criarEstruturaEnotas();
    }
}
package negocio.facade.jdbc.integracoes;

import br.com.pactosolucoes.comuns.util.Formatador;
import negocio.comuns.financeiro.NotasFiscaisTO;
import negocio.comuns.notaFiscal.NotaFiscalVO;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisValidacao;
import negocio.facade.jdbc.arquitetura.SuperEntidade;
import negocio.interfaces.integracoes.IntegracaoSesiInterfaceFacade;
import org.json.JSONObject;

import javax.servlet.http.HttpSession;
import java.sql.Connection;
import java.sql.ResultSet;
import java.sql.Statement;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.TimeZone;

public class IntegracaoSesi  extends SuperEntidade implements IntegracaoSesiInterfaceFacade {
    public IntegracaoSesi() throws Exception {
    }


    public IntegracaoSesi(Connection conexao) throws Exception {
        super(conexao);
    }

    @Override
    public Integer obterValorChavePrimariaCodigo() throws Exception {
        return null;
    }



    public List<NotasFiscaisTO> consultarNotas(Integer pessoa, Integer max, Integer index, String orderBY, int nivelMontarDados) throws Exception {
        List<NotasFiscaisTO> listaNotas = new ArrayList<>();
        StringBuilder sql = obterConsultaSQL(pessoa, false, max, index, orderBY);
        Statement stm = con.createStatement();
        ResultSet rs = stm.executeQuery(sql.toString());
        SimpleDateFormat sdf;
        sdf = new SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss");
        sdf.setTimeZone(TimeZone.getTimeZone("UTC"));
        while (rs.next()) {
            NotasFiscaisTO obj = new NotasFiscaisTO();
            JSONObject nota = new JSONObject(rs.getString("resposta")).getJSONObject("respostaConsulta").getJSONObject("notaFiscal");
            obj.setCodNotaFiscal(rs.getInt("codigo"));
            try {
                obj.setSerie(nota.getInt("notaFiscalSerie")+"");
            } catch (Exception cast){
                obj.setSerie(nota.getString("notaFiscalSerie"));
            }
            obj.setNrRPS(nota.getInt("notaFiscalNumeroRPS")+"");
            Date dataEmissao = sdf.parse(nota.getString("dataEmissao"));
            obj.setDataEmissao(Uteis.getData(dataEmissao));
            JSONObject  itens = nota.getJSONObject("itens");
            obj.setValor(Formatador.formatarValorMonetario(itens.getDouble("valorTotal")));
            obj.setDataProcessamento(Uteis.getData(new Date(rs.getLong("datarequisicao"))));
            obj.setStatus(nota.getString("statusNFE"));
            listaNotas.add(obj);
        }
        return listaNotas;
    }

    private StringBuilder obterConsultaSQL(Integer pessoa, boolean count, Integer max, Integer index, String orderBY) {
        StringBuilder sql = new StringBuilder();
        if (count) {
            sql.append("SELECT count(*) \n");
        } else {
            sql.append("SELECT * \n");
        }

        sql.append("FROM integracaosesi \n");
        sql.append("WHERE  codigopessoa = ").append(pessoa).append(" \n");
        sql.append("and  funcionalidade = 'NF'  and status = 'SU' \n");
        sql.append(" and resposta ilike '%statusNFE\":\"Autorizada%' \n");
        if (!count) {
            //ORDER BY
            if (UteisValidacao.emptyString(orderBY)) {
                sql.append(" ORDER BY codigo \n");
            } else {
                sql.append(" ORDER BY ").append(orderBY).append(" \n");
            }

            //limit
            if (!UteisValidacao.emptyNumber(max)) {
                sql.append(" LIMIT ").append(max).append(" \n");
            }

            //offset
            if (!UteisValidacao.emptyNumber(index)) {
                sql.append(" OFFSET ").append(index).append(" \n");
            }
        }
        return sql;
    }

}

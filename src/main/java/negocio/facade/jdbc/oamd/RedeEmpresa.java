package negocio.facade.jdbc.oamd;

import br.com.pactosolucoes.comuns.json.JSONMapper;
import br.com.pactosolucoes.oamd.controle.basico.DAO;
import negocio.comuns.basico.ClienteValidacaoWS;
import negocio.facade.jdbc.arquitetura.SuperEntidadeOAMD;
import negocio.interfaces.oamd.RedeEmpresaInterfaceFacade;
import negocio.oamd.EmpresaFinanceiroVO;
import negocio.oamd.RedeEmpresaVO;
import org.json.JSONArray;
import org.json.JSONObject;
import servicos.impl.autenticacaoMs.AutenticacaoMsService;
import servicos.integracao.oamd.to.EmpresaFinanceiroOAMDTO;
import servicos.propriedades.PropsService;
import servicos.util.ExecuteRequestHttpService;

import java.sql.Connection;
import java.sql.ResultSet;
import java.sql.Statement;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * Created by ulisses on 27/05/2016.
 */
public class RedeEmpresa extends SuperEntidadeOAMD implements RedeEmpresaInterfaceFacade {

    public RedeEmpresa(Connection connection)throws Exception{
        super(connection);
    }

    public RedeEmpresaVO consultarPorChaveZW(String chaveZW)throws Exception{
        StringBuilder sql = new StringBuilder();
        sql.append("select rede.* \n ");
        sql.append("from redeEmpresa rede \n");
        sql.append("inner join empresaFinanceiro ef on ef.redeEmpresa_id = rede.id \n");
        sql.append("where ef.chaveZW = '").append(chaveZW).append("'");
        Statement st = getConOAMD().createStatement();
        ResultSet rs = st.executeQuery(sql.toString());
        if (rs.next()){
            return  montarDadosBasico(rs);
        }
        return null;
    }

    public static List<EmpresaRedeDTO> consultarEmpresasRede(String chave)throws Exception{
        String url = PropsService.getPropertyValue(PropsService.urlOamd) + "/prest/empresa/consultarEmpresasRede?chave="+chave;
        Map<String, String> headers = new HashMap<>();
        headers.put("Authorization", AutenticacaoMsService.token(chave));
        try{
            String response = ExecuteRequestHttpService.get(url, headers);
            return JSONMapper.getList(new JSONObject(response).getJSONArray("return"), EmpresaRedeDTO.class);
        }catch (Exception e){
            e.printStackTrace();
            throw new Exception("Falha ao obter empresas da rede");
        }
    }

    public RedeEmpresaVO consultarPorChaveZWOAMD(String chaveZW)throws Exception{
        ExecuteRequestHttpService executeRequestHttpService = new ExecuteRequestHttpService();
        String url = PropsService.getPropertyValue(PropsService.urlOamd) + "/prest/empresaFinanceiro/consultarPorChaveZWOAMD";
        Map<String, String> headers = new HashMap<>();
        headers.put("chaveZW", chaveZW);
        try{
            String response = executeRequestHttpService.executeRequestCupomDesconto(url, "", headers);
            JSONObject objSucesso =  new JSONObject(response).optJSONObject("sucesso");
            if (objSucesso != null) {
                return JSONMapper.getObject(objSucesso, RedeEmpresaVO.class);
            }
            return null;
        }catch (Exception e){
            throw new Exception("Falha ao obter rede empresa, tente novamente!");
        }
    }

    public RedeEmpresaVO consultarPorEmpresaFinanceiro(Integer idEmpresaFinanceiro)throws Exception{
        StringBuilder sql = new StringBuilder();
        sql.append("select rede.* \n ");
        sql.append("from redeEmpresa rede \n");
        sql.append("inner join empresaFinanceiro ef on ef.redeEmpresa_id = rede.id \n");
        sql.append("where ef.codigo = ").append(idEmpresaFinanceiro);
        Statement st = getConOAMD().createStatement();
        ResultSet rs = st.executeQuery(sql.toString());
        if (rs.next()){
            return  montarDadosBasico(rs);
        }
        return null;
    }

    public Integer consultarIdEmpresaFinanceiro(Integer redeEmpresa_Id, String chave)throws Exception{
        StringBuilder sql = new StringBuilder();
        sql.append("select * \n ");
        sql.append("from empresaFinanceiro \n");
        sql.append("where redeEmpresa_id = ").append(redeEmpresa_Id).append(" \n");
        sql.append("and chaveZW = '").append(chave).append("'");
        Statement st = getConOAMD().createStatement();
        ResultSet rs = st.executeQuery(sql.toString());
        if (rs.next()){
            return  rs.getInt("codigo");
        }
        return null;
    }

    public List<EmpresaFinanceiroVO> consultarUnidadesDaRede(RedeEmpresaVO redeEmpresaVO)throws Exception{
        StringBuilder sql = new StringBuilder();
        sql.append("select * from empresaFinanceiro where redeEmpresa_id = ").append(redeEmpresaVO.getId());
        Statement st = getConOAMD().createStatement();
        ResultSet rs = st.executeQuery(sql.toString());
        List<EmpresaFinanceiroVO> lista = new ArrayList<EmpresaFinanceiroVO>();
        while (rs.next()){
            EmpresaFinanceiroVO empresaFinanceiroVO = new EmpresaFinanceiroVO();
            empresaFinanceiroVO.setCodigo(rs.getInt("codigo"));
            empresaFinanceiroVO.setNomeFantasia(rs.getString("nomeFantasia"));
            empresaFinanceiroVO.setChaveZW(rs.getString("chaveZW"));
            empresaFinanceiroVO.setIdentificadorRemessa(rs.getInt("identificadorRemessa"));
            lista.add(empresaFinanceiroVO);
        }
        return lista;
    }

    public EmpresaFinanceiroVO consultarEmpresaFinanceiro(String chaveZW)throws Exception{
        StringBuilder sql = new StringBuilder();
        sql.append("select * from empresaFinanceiro where redeEmpresa_id is not null and chaveZW = '").append(chaveZW).append("'");
        Statement st = getConOAMD().createStatement();
        ResultSet rs = st.executeQuery(sql.toString());
        while (rs.next()){
            EmpresaFinanceiroVO empresaFinanceiroVO = new EmpresaFinanceiroVO();
            empresaFinanceiroVO.setCodigo(rs.getInt("codigo"));
            empresaFinanceiroVO.setNomeFantasia(rs.getString("nomeFantasia"));
            empresaFinanceiroVO.setChaveZW(rs.getString("chaveZW"));
            empresaFinanceiroVO.setIdentificadorRemessa(rs.getInt("identificadorRemessa"));
            return empresaFinanceiroVO;
        }
        return null;
    }

    public List<EmpresaFinanceiroOAMDTO> consultarUnidadesDaRedeOAMD(RedeEmpresaVO redeEmpresaVO)throws Exception{
        JSONArray jsonArray;
        List<EmpresaFinanceiroOAMDTO> listEmpresaFinanceiro = new ArrayList<>();
        ExecuteRequestHttpService executeRequestHttpService = new ExecuteRequestHttpService();
        String url = PropsService.getPropertyValue(PropsService.urlOamd) + "/prest/empresaFinanceiro/consultarUnidadesDaRedeOAMD";
        Map<String, String> headers = new HashMap<>();
        try{
            String response = executeRequestHttpService.executeRequestCupomDesconto(url, redeEmpresaVO.toJSON(), headers);
            jsonArray = new JSONObject(response).getJSONArray("sucesso");
            for (int i = 0; i < jsonArray.length(); i++) {
                JSONObject obj = (JSONObject) jsonArray.get(i);
                EmpresaFinanceiroOAMDTO EmpresaFinanceiroOAMDTO = JSONMapper.getObject(obj , EmpresaFinanceiroOAMDTO.class);
                listEmpresaFinanceiro.add(EmpresaFinanceiroOAMDTO);
            }
        }catch (Exception e){
            throw new Exception("Falha ao obter empresa financeiro, tente novamente.");
        }
        return listEmpresaFinanceiro;
    }

    public EmpresaFinanceiroVO consultarEmpresaFinanceiro(Integer codigo)throws Exception{
        StringBuilder sql = new StringBuilder();
        sql.append("select * from empresaFinanceiro where codigo = ").append(codigo);
        Statement st = getConOAMD().createStatement();
        ResultSet rs = st.executeQuery(sql.toString());
        while (rs.next()){
            EmpresaFinanceiroVO empresaFinanceiroVO = new EmpresaFinanceiroVO();
            empresaFinanceiroVO.setCodigo(rs.getInt("codigo"));
            empresaFinanceiroVO.setNomeFantasia(rs.getString("nomeFantasia"));
            empresaFinanceiroVO.setChaveZW(rs.getString("chaveZW"));
            return empresaFinanceiroVO;
        }
        return null;
    }

    public RedeEmpresaVO consultarPorChaveRede(String chaveRede)throws Exception{
        StringBuilder sql = new StringBuilder();
        sql.append("select * \n ");
        sql.append("from redeEmpresa  \n");
        sql.append("where chaveRede = '").append(chaveRede).append("'");
        Statement st = getConOAMD().createStatement();
        ResultSet rs = st.executeQuery(sql.toString());
        if (rs.next()){
            return  montarDadosBasico(rs);
        }
        return null;
    }

    public RedeEmpresaVO consultarPorId(Integer id)throws Exception{
        StringBuilder sql = new StringBuilder();
        sql.append("select * \n ");
        sql.append("from redeEmpresa  \n");
        sql.append("where id = ").append(id);
        Statement st = getConOAMD().createStatement();
        ResultSet rs = st.executeQuery(sql.toString());
        if (rs.next()){
            return  montarDadosBasico(rs);
        }
        return null;
    }

    private RedeEmpresaVO montarDadosBasico(ResultSet rs) throws Exception{
        RedeEmpresaVO obj = new RedeEmpresaVO();
        obj.setId(rs.getInt("id"));
        obj.setChaverede(rs.getString("chaveRede"));
        obj.setNome(rs.getString("nome"));

        try {
            obj.setCreditoPorRede(rs.getBoolean("creditoPorRede"));
            obj.setGerarCobrancaAutomaticaPacto(rs.getBoolean("gerarCobrancaAutomaticaPacto"));
            obj.setCreditos(rs.getInt("creditos"));
            obj.setQtdCreditoRenovar(rs.getInt("qtdCreditoRenovar"));
            obj.setQtdParcelas(rs.getInt("qtdParcelas"));
            obj.setGerarNotaFiscal(rs.getBoolean("gerarNotaFiscal"));
            obj.setTipocobrancapacto(rs.getInt("tipocobrancapacto"));
            obj.setQtdCreditoUtilizadoPosPagoPacto(rs.getInt("qtdCreditoUtilizadoPosPagoPacto"));
            obj.setNomeClienteCobranca(rs.getString("nomeClienteCobranca"));
            obj.setCelularClienteCobranca(rs.getString("celularClienteCobranca"));
            obj.setEmailClienteCobranca(rs.getString("emailClienteCobranca"));
            obj.setValorcreditopacto(rs.getDouble("valorcreditopacto"));
        }catch (Exception e){

        }
        return obj;
    }

    public Map<String, Connection> obterConexoesRedeEmpresa(String chaveZW)throws Exception{
        Map<String, Connection> mapaConexao = new HashMap<String, Connection>();
        StringBuilder sql = new StringBuilder();
        sql.append("select * \n ");
        sql.append("from empresaFinanceiro ef \n");
        sql.append("inner join empresa emp on emp.chave = ef.chaveZw \n");
        sql.append("where ef.redeEmpresa_id = (select redeEmpresa_id from empresaFinanceiro where chaveZW = '").append(chaveZW).append("')");
        Statement st = getConOAMD().createStatement();
        ResultSet rs = st.executeQuery(sql.toString());
        DAO dao = new DAO();
        while (rs.next()){
            Connection connection = dao.obterConexaoEspecifica(rs.getString("chave"));
            mapaConexao.put(rs.getString("chave"), connection);
        }
        dao = null;
        return mapaConexao;
    }

    public String consultarNomeEmpresaDaRede(String chaveZW)throws Exception{
        StringBuilder sql = new StringBuilder();
        sql.append("select * \n ");
        sql.append("from empresaFinanceiro \n");
        sql.append("where chaveZW = '").append(chaveZW).append("'");
        Statement st = getConOAMD().createStatement();
        ResultSet rs = st.executeQuery(sql.toString());
        if (rs.next()){
           return  rs.getString("nomeFantasia");
        }
        return null;
    }

    public String consultarNomeEmpresaDaRedeOAMDPrincipal(String chaveZW)throws Exception{
        ExecuteRequestHttpService executeRequestHttpService = new ExecuteRequestHttpService();
        String url = PropsService.getPropertyValue(PropsService.urlOamd) + "/prest/cupomDesconto/consultarNomeEmpresaDaRedeOAMDPrincipal";
        Map<String, String> headers = new HashMap<>();
        headers.put("chaveZW", chaveZW);
        try{
            String response = executeRequestHttpService.executeRequestCupomDesconto(url, "", headers);
            return new JSONObject(response).getString("sucesso");
        }catch (Exception e){
            throw new Exception("Falha ao obter nome empresa financeiro, tente novamente!");
        }
    }

    public ClienteValidacaoWS consultarClienteNaRedeEmpresa(String chaveZW, String cpf)throws Exception{
        Map<String, Connection> mapaConexaoRede = obterConexoesRedeEmpresa(chaveZW);
        ClienteValidacaoWS retorno = new ClienteValidacaoWS();
        try{
            Set<Map.Entry<String, Connection>> set = mapaConexaoRede.entrySet();

            for (Map.Entry<String, Connection> ent : set) {
                String chave = ent.getKey();
                Connection connection = ent.getValue();
                ClienteValidacaoWS clienteValidacaoWS = consultarClienteValidacao(cpf, connection);
                if (clienteValidacaoWS.getCodigo() > 0){
                    retorno = clienteValidacaoWS;
                    if (clienteValidacaoWS.getSituacaoDW().equals("AT")){
                        return  clienteValidacaoWS;
                    }
                }

            }
        }finally {
            Set<Map.Entry<String, Connection>> set = mapaConexaoRede.entrySet();
            for (Map.Entry<String, Connection> ent : set) {
                Connection connection = ent.getValue();
                connection.close();
                connection = null;
            }
        }
        return  retorno;
    }

    private ClienteValidacaoWS consultarClienteValidacao(String cpf, Connection connection)throws Exception{
        StringBuilder sql = new StringBuilder();
        sql.append("select p.codigo as codigoPessoa, cli.codigo, p.cfp, p.nome, dw.situacao as situacaoDW, \n");
        sql.append("   case \n");
        sql.append("     when cli.situacao = 'AT' then 'ATIVO' \n");
        sql.append("     when cli.situacao = 'VI' then 'VISITANTE' \n");
        sql.append("     when cli.situacao = 'IN' then 'INATIVO' \n");
        sql.append("     when cli.situacao = 'TR' then 'TRANCADO' \n");
        sql.append("     else  'INDEFINIDA' \n");
        sql.append("   end as situacaoCadastral, \n");
        sql.append("   emp.nome as nomeEmpresa, \n");
        sql.append("   sqlMovParcela.dataVencimento as vencimentoMaisAntigo \n");
        sql.append("from cliente cli \n");
        sql.append("inner join pessoa p on p.codigo = cli.pessoa \n");
        sql.append("inner join situacaoClienteSinteticoDW dw on dw.codigoPessoa = p.codigo \n");
        sql.append("inner join empresa emp on emp.codigo = cli.empresa \n");
        sql.append("left join( \n");
        sql.append("       select parc.pessoa, parc.dataVencimento \n");
        sql.append("       from movParcela parc \n");
        sql.append("       inner join pessoa p on p.codigo = parc.pessoa \n");
        sql.append("       where situacao = 'EA' and p.cfp = '").append(cpf).append("' \n");
        sql.append("       order by datavencimento limit 1 \n");
        sql.append(") as sqlMovParcela on  sqlMovParcela.pessoa = cli.pessoa \n");
        sql.append("where p.cfp = '").append(cpf).append("' \n");
        Statement st = connection.createStatement();
        ResultSet rs = st.executeQuery(sql.toString());
        if (rs.next()){
            SimpleDateFormat sdf = new SimpleDateFormat("dd-MM-yyyy");
            ClienteValidacaoWS clienteValidacaoWS = new ClienteValidacaoWS();
            clienteValidacaoWS.setCodigo(rs.getInt("codigo"));
            clienteValidacaoWS.setNome(rs.getString("nome"));
            clienteValidacaoWS.setCpf(rs.getString("cfp"));
            clienteValidacaoWS.setSituacaoCadastralCliente(rs.getString("situacaoCadastral"));
            clienteValidacaoWS.setNomeUnidade(rs.getString("nomeEmpresa"));
            clienteValidacaoWS.setSituacaoDW(rs.getString("situacaoDW"));
            if (rs.getDate("vencimentoMaisAntigo") != null)
              clienteValidacaoWS.setVencimentoMaisAntigo(sdf.format(rs.getDate("vencimentoMaisAntigo")));

            return clienteValidacaoWS;
        }
        return new ClienteValidacaoWS();
    }
}

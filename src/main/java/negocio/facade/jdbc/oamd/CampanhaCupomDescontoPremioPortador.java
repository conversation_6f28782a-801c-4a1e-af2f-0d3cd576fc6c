package negocio.facade.jdbc.oamd;

import br.com.pactosolucoes.comuns.json.JSONMapper;
import br.com.pactosolucoes.comuns.util.Formatador;
import negocio.comuns.arquitetura.LogVO;
import negocio.comuns.arquitetura.UsuarioVO;
import negocio.interfaces.arquitetura.LogInterfaceFacade;
import negocio.interfaces.oamd.CampanhaCupomDescontoPremioPortadorInterfaceFacade;
import negocio.oamd.CampanhaCupomDescontoPremioPortadorVO;
import negocio.oamd.CampanhaCupomDescontoVO;
import org.json.JSONArray;
import org.json.JSONObject;
import servicos.propriedades.PropsService;
import servicos.util.ExecuteRequestHttpService;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * Created by ulisses on 27/08/2017.
 */
public class CampanhaCupomDescontoPremioPortador implements CampanhaCupomDescontoPremioPortadorInterfaceFacade {

    public List<CampanhaCupomDescontoPremioPortadorVO> consultarOAMD(Integer idCampanha, String descricaoPlano) throws Exception {
        JSONArray jsonArray;
        List<CampanhaCupomDescontoPremioPortadorVO> list = new ArrayList<>();
        ExecuteRequestHttpService executeRequestHttpService = new ExecuteRequestHttpService();
        String url = PropsService.getPropertyValue(PropsService.urlOamd) + "/prest/campanhaCupomDescontoPremioPortador/consultarOAMD";
        Map<String, String> headers = new HashMap<>();
        headers.put("idCampanha", String.valueOf(idCampanha));

        try {
            String response = executeRequestHttpService.executeRequestCupomDesconto(url, descricaoPlano, headers);
            jsonArray = new JSONObject(response).getJSONArray("sucesso");
            for (int i = 0; i < jsonArray.length(); i++) {
                JSONObject obj = (JSONObject) jsonArray.get(i);
                CampanhaCupomDescontoPremioPortadorVO campanhaCupomDescontoPremioPortadorVO = JSONMapper.getObject(obj, CampanhaCupomDescontoPremioPortadorVO.class);
                list.add(campanhaCupomDescontoPremioPortadorVO);
            }
        } catch (Exception e) {
            throw new Exception("Falha ao obter campanha cupom desconto premio portador, recarregue a pagina.");
        }
        return list;
    }

    public void alterarOAMD(CampanhaCupomDescontoVO campanhaCupomDescontoVO, UsuarioVO usuarioVO, LogInterfaceFacade logFacade) throws Exception {
        ExecuteRequestHttpService executeRequestHttpService = new ExecuteRequestHttpService();
        String url = PropsService.getPropertyValue(PropsService.urlOamd) + "/prest/campanhaCupomDescontoPremioPortador/alterarOAMD";
        try {
            String response = executeRequestHttpService.executeRequestCupomDesconto(url, campanhaCupomDescontoVO.toJSON(), new HashMap<>());
            new JSONObject(response).get("string").toString().equals("sucesso");
            List<CampanhaCupomDescontoPremioPortadorVO> listaIncluir = CampanhaCupomDescontoPremioPortadorVO.montarListaInclusao(campanhaCupomDescontoVO.getListaPremioPortador());
            registrarLogAlteracao(campanhaCupomDescontoVO, listaIncluir, usuarioVO, logFacade);
        } catch (Exception e) {
            throw new Exception("Falha ao alterar campanha, recarregue a pagina.");
        }
    }

    public void registrarLogAlteracao(CampanhaCupomDescontoVO campanhaCupomDescontoVO, List<CampanhaCupomDescontoPremioPortadorVO> listaIncluir, UsuarioVO usuarioVO, LogInterfaceFacade logFacade) throws Exception {
        // registrar log das inclusões.
        StringBuilder premiosIncluir = new StringBuilder();
        StringBuilder logIncluir = new StringBuilder();
        prepararDescricao(premiosIncluir, listaIncluir);
        if (premiosIncluir.length() > 0) {
            logIncluir.append("\n\nINCLUSÃO DE PRÊMIOS PORTADOR CUPOM: \n\n");
            logIncluir.append(premiosIncluir);
        }

        StringBuilder premiosExcluir = new StringBuilder();
        StringBuilder logExcluir = new StringBuilder();
        List<CampanhaCupomDescontoPremioPortadorVO> listaExcluir = CampanhaCupomDescontoPremioPortadorVO.montarListaExclusao(campanhaCupomDescontoVO.getListaPremioPortadorAntesDeAlterar(), campanhaCupomDescontoVO.getListaPremioPortador());
        prepararDescricao(premiosExcluir, listaExcluir);
        if (premiosExcluir.length() > 0) {
            logExcluir.append("\nEXCLUSÃO DE PRÊMIOS PORTADOR CUPOM: \n\n");
            logExcluir.append(premiosExcluir);
        }

        if ((logIncluir.length() > 0) || (logExcluir.length() > 0)) {
            LogVO obj = new LogVO();
            obj.setChavePrimaria(String.valueOf(campanhaCupomDescontoVO.getId()));
            obj.setNomeEntidade("CAMPANHACUPOMDESCONTO");
            obj.setNomeEntidadeDescricao("CAMPANHACUPOMDESCONTO");
            obj.setResponsavelAlteracao(usuarioVO.getNome());
            obj.setUserOAMD(usuarioVO.getUserOamd());
            obj.setNomeCampo("MENSAGEM");
            obj.setOperacao("ALTERAÇÃO");
            obj.setValorCampoAlterado(logIncluir.toString() + logExcluir.toString());
            obj.setDataAlteracao(negocio.comuns.utilitarias.Calendario.hoje());

            logFacade.incluirSemCommit(obj);
        }
    }

    private void prepararDescricao(StringBuilder premiosExcluir, List<CampanhaCupomDescontoPremioPortadorVO> listaExcluir) {
        if (listaExcluir.size() > 0) {
            for (CampanhaCupomDescontoPremioPortadorVO obj : listaExcluir) {
                premiosExcluir.append("Tipo prêmio: ").append(obj.getTipoPremio()).append("\n");
                premiosExcluir.append("Descrição do prêmio: ").append(obj.getDescricaoPremio()).append("\n");
                premiosExcluir.append("Descrição do plano: ").append(obj.getDescricaoPlano()).append("\n");
                premiosExcluir.append("Valor de desconto: ").append(Formatador.formatarValorNumerico(obj.getValorDesconto(), Formatador.FRMT_NUM_PADRAO)).append("\n");
                premiosExcluir.append("Percentual de desconto: ").append(Formatador.formatarValorNumerico(obj.getPercentualDesconto(), Formatador.FRMT_NUM_PADRAO)).append("\n");
                premiosExcluir.append("\n");
            }
        }
    }

}

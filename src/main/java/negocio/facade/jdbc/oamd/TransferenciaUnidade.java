package negocio.facade.jdbc.oamd;

import negocio.comuns.utilitarias.Calendario;
import negocio.comuns.utilitarias.Uteis;
import negocio.facade.jdbc.arquitetura.SuperEntidadeOAMD;
import negocio.interfaces.oamd.TransferenciaUnidadeInterfaceFacade;
import negocio.oamd.RedeEmpresaVO;
import negocio.oamd.TransferenciaUnidadeVO;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.Statement;

/**
 * Created by ulisses on 17/06/2016.
 */
public class TransferenciaUnidade extends SuperEntidadeOAMD implements TransferenciaUnidadeInterfaceFacade {

    public TransferenciaUnidade(Connection connection)throws Exception{
        super(connection);
    }

    public void incluir(TransferenciaUnidadeVO transferenciaUnidadeVO)throws Exception{
        String sql = "insert into transferenciaUnidade(redeEmpresa_id, cpf, nomeAluno, dataTransferencia,dataAnuidade,chaveOrigem,unidadeOrigem,contratoOrigem) values (?, ?,?, ?,?, ?, ?, ?)";
        PreparedStatement pst = getConOAMD().prepareStatement(sql);
        pst.setInt(1, transferenciaUnidadeVO.getRedeEmpresaVO().getId());
        pst.setString(2, transferenciaUnidadeVO.getCpf());
        pst.setString(3, transferenciaUnidadeVO.getNomeAluno());
        pst.setTimestamp(4, Uteis.getDataJDBCTimestamp(Calendario.hoje()));
        pst.setDate(5, Uteis.getDataJDBC(transferenciaUnidadeVO.getDataAnuidade()));
        pst.setString(6, transferenciaUnidadeVO.getChaveOrigem());
        pst.setString(7, transferenciaUnidadeVO.getUnidadeOrigem());
        pst.setInt(8, transferenciaUnidadeVO.getContratoOrigem());
        pst.execute();
        transferenciaUnidadeVO.setId(obterValorChavePrimariaCodigo("id"));
        transferenciaUnidadeVO.setNovoObj(false);
    }
    public void alterar(TransferenciaUnidadeVO transferenciaUnidadeVO)throws Exception{
        String sql = "update transferenciaUnidade set chaveDestino =? ,unidadeDestino =?,contratoDestino=? where id = ?";
        PreparedStatement pst = getConOAMD().prepareStatement(sql);
        pst.setString(1, transferenciaUnidadeVO.getChaveDestino());
        pst.setString(2, transferenciaUnidadeVO.getUnidadeDestino());
        pst.setInt(3, transferenciaUnidadeVO.getContratoDestino());
        pst.setInt(4, transferenciaUnidadeVO.getId());
        pst.execute();
    }

    public void gravarMensagemErro(Integer idTransferencia, String erro)throws Exception{
        String sql = "update transferenciaUnidade set mensagemErro = ? where id = ? ";
        PreparedStatement pst = getConOAMD().prepareStatement(sql);
        pst.setString(1, erro);
        pst.setInt(2, idTransferencia);
        pst.execute();
    }

    private TransferenciaUnidadeVO montarDadosBasico(ResultSet rs) throws Exception{
        TransferenciaUnidadeVO obj = new TransferenciaUnidadeVO();
        obj.setId(rs.getInt("id"));
        obj.setCpf(rs.getString("cpf"));
        obj.setNomeAluno(rs.getString("nomeAluno"));
        obj.setDataTransferencia(rs.getTimestamp("dataTransferencia"));
        obj.setDataAnuidade(rs.getDate("dataAnuidade"));
        obj.setChaveOrigem(rs.getString("chaveOrigem"));
        obj.setUnidadeOrigem(rs.getString("unidadeOrigem"));
        obj.setContratoOrigem(rs.getInt("contratoOrigem"));
        obj.setChaveDestino(rs.getString("chaveDestino"));
        obj.setUnidadeDestino(rs.getString("unidadeDestino"));
        obj.setContratoDestino(rs.getInt("contratoDestino"));
        obj.setRedeEmpresaVO(new RedeEmpresaVO());
        obj.getRedeEmpresaVO().setId(rs.getInt("redeEmpresa_id"));
        return obj;
    }

    public TransferenciaUnidadeVO consultarUltimaTransferenciaUnidade(String chaveRede, String cpf)throws Exception{
        StringBuilder sql = new StringBuilder();
        sql.append("select transf.* \n");
        sql.append("from transferenciaunidade transf \n");
        sql.append("inner join redeEmpresa re on re.id = transf.redeEmpresa_id \n");
        sql.append("where cpf = '").append(cpf).append("' \n");
        sql.append(" and re.chaveRede = '").append(chaveRede).append("' \n");
        sql.append("order by dataTransferencia desc limit 1 ");
        Statement st = getConOAMD().createStatement();
        ResultSet rs = st.executeQuery(sql.toString());
        if (rs.next()){
            return montarDadosBasico(rs);
        }
        return null;
    }

}

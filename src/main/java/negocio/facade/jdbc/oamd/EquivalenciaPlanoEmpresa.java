package negocio.facade.jdbc.oamd;

import negocio.comuns.plano.PlanoVO;
import negocio.comuns.plano.PlanoWS;
import negocio.comuns.utilitarias.Uteis;
import negocio.facade.jdbc.arquitetura.SuperEntidadeOAMD;
import negocio.facade.jdbc.plano.Plano;
import negocio.interfaces.oamd.EquivalenciaPlanoEmpresaInterfaceFacade;
import negocio.interfaces.plano.PlanoInterfaceFacade;

import java.sql.Connection;
import java.sql.ResultSet;
import java.sql.Statement;

/**
 * Created by ulisses on 08/07/2016.
 */
public class EquivalenciaPlanoEmpresa extends SuperEntidadeOAMD implements EquivalenciaPlanoEmpresaInterfaceFacade {

    public EquivalenciaPlanoEmpresa(Connection connection)throws Exception{
        super(connection);
    }

    public PlanoWS consultarEquivalenciaPlanoUnidadeDestino(int codigoPlanoOrigem, int codigoEmpresaFinanceiroOrigem, String chaveDestino, Connection conDestino)throws Exception{
        PlanoWS planoWS = new PlanoWS();
        StringBuilder sql = new StringBuilder();
        sql.append("select * \n");
        sql.append("from EquivalenciaPlanoEmpresa eq \n");
        sql.append("inner join empresaFinanceiro ef on ef.codigo = eq.empresaFinanceiro_codigo \n");
        sql.append("where ef.chaveZW = '").append(chaveDestino).append("' \n");
        sql.append("and equivalenciaPlanoRedeEmpresa_id in( \n");
        sql.append("	select eq.equivalenciaPlanoRedeEmpresa_id \n");
        sql.append("	from EquivalenciaPlanoEmpresa eq \n");
        sql.append("	inner join empresaFinanceiro ef on ef.codigo = eq.empresaFinanceiro_codigo \n");
        sql.append("	where ef.codigo = ").append(codigoEmpresaFinanceiroOrigem).append(" and codigoPlano =  ").append(codigoPlanoOrigem).append(" ) ");
        Statement st = getConOAMD().createStatement();
        ResultSet rs = st.executeQuery(sql.toString());
        if (rs.next()){
            PlanoVO planoVO = consultarPlanoUnidadeDestino(conDestino, rs.getInt("codigoPlano"));
            return planoVO.toWS(conDestino);
        }else{
            planoWS.setMsgValidacao("Não foi encontrada nenhuma equivalência de plano na unidade de destino para o plano do aluno.");
        }
        return planoWS;
    }

    private PlanoVO consultarPlanoUnidadeDestino(Connection conDestino, Integer codigoPlano)throws Exception{
        PlanoInterfaceFacade planoDao = new Plano(conDestino);
        try{
            PlanoVO planoVO = planoDao.consultarPorChavePrimaria(codigoPlano, Uteis.NIVELMONTARDADOS_DADOSENTIDADESUBORDINADAS);
            return planoVO;
        }finally {
            planoDao = null;
        }
    }

}

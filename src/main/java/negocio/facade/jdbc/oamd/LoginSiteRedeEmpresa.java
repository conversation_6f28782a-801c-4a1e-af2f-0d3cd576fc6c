package negocio.facade.jdbc.oamd;

import negocio.facade.jdbc.arquitetura.SuperEntidadeOAMD;
import negocio.interfaces.oamd.LoginSiteRedeEmpresaInterfaceFacade;
import negocio.oamd.LoginSiteRedeEmpresaVO;
import negocio.oamd.RedeEmpresaVO;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.Statement;

/**
 * Created by ulisses on 26/05/2016.
 */
public class LoginSiteRedeEmpresa extends SuperEntidadeOAMD implements LoginSiteRedeEmpresaInterfaceFacade {

    public LoginSiteRedeEmpresa(Connection con)throws Exception{
        super(con);
    }


    public void registrarLoginRedeEmpresa(String email, RedeEmpresaVO redeEmpresaVO, Integer idEmpresaFinanceiro, String cpf)throws Exception{
        LoginSiteRedeEmpresaVO loginSiteRedeEmpresaVO = consultarPorEmail(redeEmpresaVO.getChaverede(),email);
        if (loginSiteRedeEmpresaVO == null){
            String sql = "insert into LoginSiteRedeEmpresa(email, empresaFinanceiro_codigo, redeEmpresa_id, cpf) values (?, ?, ?, ?)";
            PreparedStatement pst = getConOAMD().prepareStatement(sql);
            pst.setString(1, email);
            pst.setInt(2, idEmpresaFinanceiro);
            pst.setInt(3, redeEmpresaVO.getId());
            pst.setString(4, cpf);
            pst.execute();
        }else{
            String sql = "update LoginSiteRedeEmpresa set empresaFinanceiro_codigo=?, redeEmpresa_id=?, cpf = ? where id =?";
            PreparedStatement pst = getConOAMD().prepareStatement(sql);
            pst.setInt(1, idEmpresaFinanceiro);
            pst.setInt(2, redeEmpresaVO.getId());
            pst.setString(3, cpf);
            pst.setInt(4, loginSiteRedeEmpresaVO.getId());
            pst.execute();
        }
    }

    public LoginSiteRedeEmpresaVO consultar(String chaveRede, String email, boolean buscarPorCPF)throws Exception{
        StringBuilder sql = new StringBuilder();
        sql.append("select login.*, ef.chaveZW \n");
        sql.append("from LoginSiteRedeEmpresa login \n");
        sql.append("inner join empresaFinanceiro ef on ef.codigo = login.empresaFinanceiro_codigo \n");
        sql.append("inner join redeEmpresa rede on rede.id = ef.redeEmpresa_id \n");
        if (buscarPorCPF) {
            sql.append("where login.cpf = '").append(email).append("' \n");
        } else {
            sql.append("where login.email = '").append(email).append("' \n");
        }
        sql.append(" and rede.chaveRede = '").append(chaveRede).append("'");
        Statement st = getConOAMD().createStatement();
        ResultSet rs = st.executeQuery(sql.toString());
        if (rs.next()){
            LoginSiteRedeEmpresaVO obj = montarDadosBasico(rs);
            obj.setChaveZW(rs.getString("chaveZW"));
            return obj;
        }
        return null;

    }

    private LoginSiteRedeEmpresaVO montarDadosBasico(ResultSet rs)throws Exception{
        LoginSiteRedeEmpresaVO obj = new LoginSiteRedeEmpresaVO();
        obj.setId(rs.getInt("id"));
        obj.setEmail(rs.getString("email"));
        obj.setEmpresafinanceiro_codigo(rs.getInt("empresafinanceiro_codigo"));
        obj.setRedeEmpresaVO(new RedeEmpresaVO());
        obj.getRedeEmpresaVO().setId(rs.getInt("redeEmpresa_id"));
        return obj;
    }

    public LoginSiteRedeEmpresaVO consultarPorEmail(String chaveRede, String email)throws Exception{
        StringBuilder sql = new StringBuilder();
        sql.append("select l.* \n");
        sql.append("from LoginSiteRedeEmpresa l \n");
        sql.append("inner join redeEmpresa rede on rede.id = l.redeEmpresa_id \n");
        sql.append(" where rede.chaveRede = '").append(chaveRede).append("' \n");
        sql.append(" and l.email = '").append(email).append("'");
        Statement st = getConOAMD().createStatement();
        ResultSet rs = st.executeQuery(sql.toString());
        if (rs.next()){
            return montarDadosBasico(rs);
        }
        return null;
    }

}

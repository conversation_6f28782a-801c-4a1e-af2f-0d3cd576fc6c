package negocio.facade.jdbc.oamd;

import br.com.pactosolucoes.comuns.json.JSONMapper;
import negocio.comuns.arquitetura.UsuarioVO;
import negocio.comuns.utilitarias.ConsistirException;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisValidacao;
import negocio.interfaces.arquitetura.LogInterfaceFacade;
import negocio.interfaces.oamd.CampanhaCupomDescontoInterfaceFacade;
import negocio.oamd.CampanhaCupomDescontoVO;
import org.json.JSONArray;
import org.json.JSONObject;
import servicos.propriedades.PropsService;
import servicos.util.ExecuteRequestHttpService;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * Created by ulisses on 27/05/2016.
 */
public class CampanhaCupomDesconto implements CampanhaCupomDescontoInterfaceFacade {

    public CampanhaCupomDescontoVO incluirOAMD(CampanhaCupomDescontoVO campanhaCupomDesconto, UsuarioVO usuarioVO, LogInterfaceFacade logFacade) throws Exception {
        String url = PropsService.getPropertyValue(PropsService.urlOamd) + "/prest/campanhaCupomDesconto/incluirOAMD";
        try {
            String response = ExecuteRequestHttpService.executeRequestCupomDesconto(url, campanhaCupomDesconto.toJSON(), new HashMap<>());
            CampanhaCupomDescontoVO campanhaReturn = JSONMapper.getObject(new JSONObject(response).getJSONObject("sucesso"), CampanhaCupomDescontoVO.class);
            campanhaReturn.setListaPremioPortadorAntesDeAlterar(new ArrayList<>(campanhaCupomDesconto.getListaPremioPortador()));
            campanhaReturn.setObjetoVOAntesAlteracao(campanhaCupomDesconto.getObjetoVOAntesAlteracao());
            return campanhaReturn;
        } catch (Exception e) {
            throw new Exception("Falha ao incluir cupom, tente novamente!");
        }
    }

    public CampanhaCupomDescontoVO alterarOAMD(CampanhaCupomDescontoVO campanhaCupomDesconto, UsuarioVO usuarioVO, LogInterfaceFacade logFacade) throws Exception {
        String url = PropsService.getPropertyValue(PropsService.urlOamd) + "/prest/campanhaCupomDesconto/alterarOAMD";
        try {
            Uteis.logarDebug("url: " + url);
            String response = ExecuteRequestHttpService.executeRequestCupomDesconto(url, campanhaCupomDesconto.toJSON(), new HashMap<>());
            CampanhaCupomDescontoVO campanhaReturn = JSONMapper.getObject(new JSONObject(response).getJSONObject("sucesso"), CampanhaCupomDescontoVO.class);
            campanhaReturn.setListaPremioPortadorAntesDeAlterar(new ArrayList<>(campanhaCupomDesconto.getListaPremioPortador()));
            campanhaReturn.setObjetoVOAntesAlteracao(campanhaCupomDesconto.getObjetoVOAntesAlteracao());
            return campanhaReturn;
        } catch (Exception e) {
            e.printStackTrace();
            throw new Exception("Falha ao alterar campanha cupom, tente novamente!");
        }
    }

    public void gerarNovoLoteCupomDescontoOAMD(CampanhaCupomDescontoVO campanhaCupomDesconto, Integer quantidadeCupomGerar) throws Exception {
        String url = PropsService.getPropertyValue(PropsService.urlOamd) + "/prest/campanhaCupomDesconto/gerarNovoLoteCupomDescontoOAMD";
        Map<String, String> headers = new HashMap<>();
        headers.put("quantidadeCupomGerar", String.valueOf(quantidadeCupomGerar));
        try {
            String response = ExecuteRequestHttpService.executeRequestCupomDesconto(url, campanhaCupomDesconto.toJSON(), headers);
            new JSONObject(response).get("string").toString().equals("sucesso");
        } catch (Exception e) {
            throw new Exception("Falha gerar novo lote de cupons, tente novamente!");
        }
    }

    public void excluirOAMD(CampanhaCupomDescontoVO campanhaCupomDescontoVO) throws Exception {
        String url = PropsService.getPropertyValue(PropsService.urlOamd) + "/prest/campanhaCupomDesconto/excluirOAMD";
        try {
            Map<String, String> headers = new HashMap<>();
            headers.put("idCampanhaCupomDesconto", String.valueOf(campanhaCupomDescontoVO.getId()));

            String response = ExecuteRequestHttpService.executeRequestCupomDesconto(url, "", headers);
            new JSONObject(response).get("string").toString().equals("sucesso");
        } catch (Exception e) {
            throw new Exception("Falha ao excluir campanha, tente novamente!");
        }
    }


    public CampanhaCupomDescontoVO consultarPorId(Integer id) throws Exception {
        String url = PropsService.getPropertyValue(PropsService.urlOamd) + "/prest/campanhaCupomDesconto/consultarPorIdOAMD";
        Map<String, String> headers = new HashMap<>();
        headers.put("id", String.valueOf(id));
        try {
            String response = ExecuteRequestHttpService.executeRequestCupomDesconto(url, "", headers);
            CampanhaCupomDescontoVO campanhaCupomDescontoVO = JSONMapper.getObject(new JSONObject(response).getJSONObject("sucesso"), CampanhaCupomDescontoVO.class);
            campanhaCupomDescontoVO.setAplicarParaRede(campanhaCupomDescontoVO.getRedeEmpresaVO() != null && campanhaCupomDescontoVO.getRedeEmpresaVO().getId() != 0);
            return campanhaCupomDescontoVO;
        } catch (Exception e) {
            e.printStackTrace();
            throw new Exception("Falha ao buscar campanha, tente novamente!");
        }
    }

    public CampanhaCupomDescontoVO consultarCampanhaPorNumeroCupomOAMD(String numeroCupomDesconto) throws Exception {
        String url = PropsService.getPropertyValue(PropsService.urlOamd) + "/prest/campanhaCupomDesconto/consultarCampanhaPorNumeroCupomOAMD";
        Map<String, String> headers = new HashMap<>();
        headers.put("numeroCupomDesconto", numeroCupomDesconto);
        try {
            String response = ExecuteRequestHttpService.executeRequestCupomDesconto(url, "", headers);
            return JSONMapper.getObject(new JSONObject(response).getJSONObject("sucesso"), CampanhaCupomDescontoVO.class);
        } catch (Exception e) {
            throw new Exception("Falha consultar campanha por numero, tente novamente!");
        }
    }

    public List<CampanhaCupomDescontoJSON> consultarCampanhaCupomDescontoJSONOAMD(String chaveRedeEmpresa, Integer idFavorecido, boolean somenteVigente) throws Exception {
        String url = PropsService.getPropertyValue(PropsService.urlOamd) + "/prest/campanhaCupomDesconto/listarCampanhasCupomDesconto";
        Map<String, String> headers = new HashMap<>();

        if (UteisValidacao.emptyString(chaveRedeEmpresa) && (idFavorecido == null || idFavorecido == 0)) {
            throw new ConsistirException("Rede de empresa ou Id Favorecido precisa ser informado");
        }

        boolean possuiRede = false;
        if (!UteisValidacao.emptyString(chaveRedeEmpresa)) {
            url = url + "?chaveRedeEmpresa=" + chaveRedeEmpresa;
            possuiRede = true;
        }
        if (idFavorecido != null && idFavorecido > 0) {
            url = url + (possuiRede ? "&" : "?") + "codFinanceiro=" + idFavorecido;
        }
        url = url + (url.contains("?") ? "&" : "?") + "somenteVigentes=" + somenteVigente;

        try {
            String response = ExecuteRequestHttpService.executeRequestCupomDesconto(url, "", headers);
            JSONObject object = new JSONObject(response);
            JSONArray arraySucesso = object.optJSONArray("sucesso");
            if (arraySucesso != null) {
                return JSONMapper.getList(arraySucesso, CampanhaCupomDescontoJSON.class);
            }
            return new ArrayList<>();
        } catch (Exception e) {
            throw new Exception("Falha ao buscar lista de campanhas, tente novamente!");
        }
    }

    public boolean existeCampanhaCupomDescontoOAMD(Integer codigoFavorecido, boolean somenteVigente, String descricaoPlano) throws Exception {
        String url = PropsService.getPropertyValue(PropsService.urlOamd) + "/prest/campanhaCupomDesconto/existeCampanhaCupomDescontoOAMD";
        Map<String, String> headers = new HashMap<>();
        headers.put("codigoFavorecido", codigoFavorecido.toString());
        headers.put("somenteVigente", String.valueOf(somenteVigente));
        headers.put("descricaoPlano", descricaoPlano);
        try {
            String response = ExecuteRequestHttpService.executeRequestCupomDesconto(url, "", headers);
            return new JSONObject(response).getBoolean("sucesso");
        } catch (Exception e) {
            throw new Exception("Falha verificar se existe campanha cupom desconto, tente novamente!");
        }
    }

    public List<CampanhaCupomDescontoVO> consultarParaImpressaoOAMD(String filtro, String ordem, String campoOrdenacao, String chaveRedeEmpresa) throws Exception {
        String url = PropsService.getPropertyValue(PropsService.urlOamd) + "/prest/campanhaCupomDesconto/consultarParaImpressaoOAMD";
        Map<String, String> headers = new HashMap<>();
        headers.put("filtro", filtro);
        headers.put("ordem", ordem);
        headers.put("chaveRedeEmpresa", chaveRedeEmpresa);
        JSONArray jsonArray;
        List<CampanhaCupomDescontoVO> list = new ArrayList<>();
        try {
            String response = ExecuteRequestHttpService.executeRequestCupomDesconto(url, "", headers);
            jsonArray = new JSONObject(response).getJSONArray("sucesso");
            for (int i = 0; i < jsonArray.length(); i++) {
                JSONObject obj = (JSONObject) jsonArray.get(i);
                CampanhaCupomDescontoVO campanhaCupomDescontoVO = JSONMapper.getObject(obj, CampanhaCupomDescontoVO.class);
                list.add(campanhaCupomDescontoVO);
            }
        } catch (Exception e) {
            throw new Exception("Falha ao obter lista para impressão, recarregue a pagina.");
        }
        return list;
    }


}

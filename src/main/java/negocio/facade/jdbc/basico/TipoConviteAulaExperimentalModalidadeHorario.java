package negocio.facade.jdbc.basico;

import negocio.comuns.conviteaulaexperimental.TipoConviteAulaExperimentalModalidadeHorarioVO;
import negocio.comuns.conviteaulaexperimental.TipoConviteAulaExperimentalModalidadeVO;
import negocio.comuns.utilitarias.Uteis;
import negocio.facade.jdbc.arquitetura.SuperEntidade;
import negocio.interfaces.basico.TipoConviteAulaExperimentalModalidadeHorarioInterfaceFacade;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.util.ArrayList;
import java.util.List;

/**
 * Created by ulisses on 14/01/2016.
 */
public class TipoConviteAulaExperimentalModalidadeHorario extends SuperEntidade implements TipoConviteAulaExperimentalModalidadeHorarioInterfaceFacade {

    public TipoConviteAulaExperimentalModalidadeHorario() throws Exception {
        super();
    }

    public TipoConviteAulaExperimentalModalidadeHorario(Connection con) throws Exception {
        super(con);
    }


    public void incluirSemCommit(List<TipoConviteAulaExperimentalModalidadeHorarioVO> listaConviteModalidadeHorario)throws Exception{
        String sql = "insert into tipoConviteAulaExperimentalModalidadeHorario (tipoConviteAulaExperimentalModalidade,diasSemana, horaInicial, horaFinal) values(?,?,?,?)";
        PreparedStatement pst = con.prepareStatement(sql);
        for (TipoConviteAulaExperimentalModalidadeHorarioVO obj: listaConviteModalidadeHorario){
            TipoConviteAulaExperimentalModalidadeHorarioVO.validarDados(obj);
            obj.montarDiasSemana();
            pst.setInt(1, obj.getTipoConviteAulaExperimentalModalidadeVO().getCodigo());
            pst.setString(2, obj.getDiasSemana());
            pst.setString(3, obj.getHoraInicial());
            pst.setString(4, obj.getHoraFinal());
            pst.execute();
            obj.setCodigo(obterValorChavePrimariaCodigo());
            obj.setNovoObj(false);
        }

    }

    public void excluirSemCommit(Integer tipoConviteAulaExperimental)throws Exception{
        String sql = "delete from tipoConviteAulaExperimentalModalidadeHorario where tipoConviteAulaExperimentalModalidade in(select codigo from tipoConviteAulaExperimentalModalidade where tipoConviteAulaExperimental = ?)";
        PreparedStatement pst = con.prepareStatement(sql);
        pst.setInt(1, tipoConviteAulaExperimental);
        pst.execute();
    }

    public List<TipoConviteAulaExperimentalModalidadeHorarioVO> consultar(TipoConviteAulaExperimentalModalidadeVO tipoConviteAulaExperimentalModalidadeVO, int nivelMontarDados)throws Exception{
        String sql = "select * from tipoConviteAulaExperimentalModalidadeHorario where tipoConviteAulaExperimentalModalidade = ?";
        PreparedStatement pst = con.prepareStatement(sql);
        pst.setInt(1, tipoConviteAulaExperimentalModalidadeVO.getCodigo());
        ResultSet rs = pst.executeQuery();
        return montarDadosConsulta(rs, nivelMontarDados);
    }


    private TipoConviteAulaExperimentalModalidadeHorarioVO montarDadosBasico(ResultSet rs)throws Exception{
        TipoConviteAulaExperimentalModalidadeHorarioVO obj = new TipoConviteAulaExperimentalModalidadeHorarioVO();
        obj.setCodigo(rs.getInt("codigo"));
        TipoConviteAulaExperimentalModalidadeVO tipoConviteAulaExperimentalModalidadeVO = new TipoConviteAulaExperimentalModalidadeVO();
        tipoConviteAulaExperimentalModalidadeVO.setCodigo(rs.getInt("tipoConviteAulaExperimentalModalidade"));
        obj.setTipoConviteAulaExperimentalModalidadeVO(tipoConviteAulaExperimentalModalidadeVO);
        obj.setHoraInicial(rs.getString("horaInicial"));
        obj.setHoraFinal(rs.getString("horaFinal"));
        obj.setDiasSemana(rs.getString("diasSemana"));
        obj.selecionarDiasSemana();
        return obj;
    }


    private TipoConviteAulaExperimentalModalidadeHorarioVO montarDados(ResultSet rs, int nivelMontarDados)throws Exception{
        TipoConviteAulaExperimentalModalidadeHorarioVO obj = montarDadosBasico(rs);

        if (nivelMontarDados == Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA){
            return  obj;
        }
        if (nivelMontarDados == Uteis.NIVELMONTARDADOS_DADOSBASICOS){
            return  obj;
        }
        return obj;
    }

    private List<TipoConviteAulaExperimentalModalidadeHorarioVO> montarDadosConsulta(ResultSet rs, int nivelMontarDados)throws Exception{
        List<TipoConviteAulaExperimentalModalidadeHorarioVO> lista = new ArrayList<TipoConviteAulaExperimentalModalidadeHorarioVO>();
        while (rs.next()){
            TipoConviteAulaExperimentalModalidadeHorarioVO obj = montarDados(rs, nivelMontarDados);
            lista.add(obj);
        }
        return lista;
    }

}

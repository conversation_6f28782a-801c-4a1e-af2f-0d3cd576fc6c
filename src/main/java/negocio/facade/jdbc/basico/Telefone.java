package negocio.facade.jdbc.basico;

import br.com.pactosolucoes.comuns.util.Declaracao;
import br.com.pactosolucoes.enumeradores.TipoTelefoneEnum;
import negocio.comuns.arquitetura.LogVO;
import negocio.comuns.basico.TelefoneVO;
import negocio.comuns.crm.MalaDiretaVO;
import negocio.comuns.utilitarias.Calendario;
import negocio.comuns.utilitarias.ConsistirException;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisValidacao;
import negocio.facade.jdbc.arquitetura.Log;
import negocio.facade.jdbc.arquitetura.SuperEntidade;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.sql.Statement;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;

/**
 * Classe de persistência que encapsula todas as operações de manipulação dos dados da classe <code>TelefoneVO</code>.
 * Responsável por implementar operações como incluir, alterar, excluir e consultar pertinentes a classe <code>TelefoneVO</code>.
 * Encapsula toda a interação com o banco de dados.
 *
 * @see TelefoneVO
 * @see SuperEntidade
 * @see Pessoa
 */
public class Telefone extends SuperEntidade {

    public Telefone() throws Exception {
        super();
    }

    public Telefone(Connection conexao) throws Exception {
        super(conexao);
    }

    /**
     * Operação responsável por retornar um novo objeto da classe <code>TelefoneVO</code>.
     */
    public TelefoneVO novo() throws Exception {
        incluir(getIdEntidade());
        TelefoneVO obj = new TelefoneVO();
        return obj;
    }

    /**
     * Operação responsável por incluir no banco de dados um objeto da classe <code>TelefoneVO</code>.
     * Primeiramente valida os dados (<code>validarDados</code>) do objeto. Verifica a conexão com o banco de dados e a permissão do usuário
     * para realizar esta operacão na entidade.
     * Isto, através da operação <code>incluir</code> da superclasse.
     *
     * @param obj Objeto da classe <code>TelefoneVO</code> que será gravado no banco de dados.
     * @throws Exception Caso haja problemas de conexão, restrição de acesso ou validação de dados.
     */
    public void incluir(TelefoneVO obj) throws Exception {
        incluir(obj, true);
    }

    public void incluir(TelefoneVO obj, boolean controlarAcesso) throws Exception {
        obj.validarDados(obj.isUsarSistemaInternacional());
        if (controlarAcesso) {
            incluir(getIdEntidade());
        }
        obj.realizarUpperCaseDados();
        String sql = "INSERT INTO Telefone( numero, tipoTelefone, pessoa, descricao, ddi, receberSMS, usarNonoDigitoWApp) VALUES (?, ?, ?, ?, ?, ?, ?)";
        PreparedStatement sqlInserir = con.prepareStatement(sql);
        sqlInserir.setString(1, obj.getNumero());
        sqlInserir.setString(2, obj.getTipoTelefone());
        if (obj.getPessoa().intValue() != 0) {
            sqlInserir.setInt(3, obj.getPessoa().intValue());
        } else {
            sqlInserir.setNull(3, 0);
        }
        sqlInserir.setString(4, obj.getDescricao());
        if (!UteisValidacao.emptyString(obj.getDdi())) {
            sqlInserir.setString(5, obj.getDdi());
        } else {
            sqlInserir.setNull(5, 0);
        }
        sqlInserir.setBoolean(6, obj.isReceberSMS());
        sqlInserir.setBoolean(7, obj.isUsarNonoDigitoWApp());
        sqlInserir.execute();
        obj.setCodigo(obterValorChavePrimariaCodigo());
        obj.setNovoObj(false);
    }

    /**
     * Operação responsável por alterar no BD os dados de um objeto da classe <code>TelefoneVO</code>.
     * Sempre utiliza a chave primária da classe como atributo para localização do registro a ser alterado.
     * Primeiramente valida os dados (<code>validarDados</code>) do objeto. Verifica a conexão com o banco de dados e a permissão do usuário
     * para realizar esta operacão na entidade.
     * Isto, através da operação <code>alterar</code> da superclasse.
     *
     * @param obj Objeto da classe <code>TelefoneVO</code> que será alterada no banco de dados.
     * @throws Exception Caso haja problemas de conexão, restrição de acesso ou validação de dados.
     */
    public void alterar(TelefoneVO obj) throws Exception {
        alterar(obj, true);
    }

    public void alterar(TelefoneVO obj, boolean controlarAcesso) throws Exception {
        obj.validarDados(obj.isUsarSistemaInternacional());
        if (controlarAcesso) {
            alterar(getIdEntidade());
        }
        obj.realizarUpperCaseDados();
        String sql = "UPDATE Telefone set numero=?, tipoTelefone=?, pessoa=?, descricao=?, ddi=?, receberSMS=?, usarNonoDigitoWApp=? WHERE ((codigo = ?))";
        PreparedStatement sqlAlterar = con.prepareStatement(sql);
        sqlAlterar.setString(1, obj.getNumero());
        sqlAlterar.setString(2, obj.getTipoTelefone());
        if (obj.getPessoa().intValue() != 0) {
            sqlAlterar.setInt(3, obj.getPessoa().intValue());
        } else {
            sqlAlterar.setNull(3, 0);
        }
        sqlAlterar.setString(4, obj.getDescricao());
        if (!UteisValidacao.emptyString(obj.getDdi())) {
            sqlAlterar.setString(5, obj.getDdi());
        } else {
            sqlAlterar.setNull(5, 0);
        }
        sqlAlterar.setBoolean(6, obj.isReceberSMS());
        sqlAlterar.setBoolean(7, obj.isUsarNonoDigitoWApp());
        sqlAlterar.setInt(8, obj.getCodigo());
        sqlAlterar.execute();
    }

    /**
     * Operação responsável por excluir no BD um objeto da classe <code>TelefoneVO</code>.
     * Sempre localiza o registro a ser excluído através da chave primária da entidade.
     * Primeiramente verifica a conexão com o banco de dados e a permissão do usuário
     * para realizar esta operacão na entidade.
     * Isto, através da operação <code>excluir</code> da superclasse.
     *
     * @param obj Objeto da classe <code>TelefoneVO</code> que será removido no banco de dados.
     * @throws Exception Caso haja problemas de conexão ou restrição de acesso.
     */
    public void excluir(TelefoneVO obj) throws Exception {
        excluir(getIdEntidade());
        String sql = "DELETE FROM Telefone WHERE ((codigo = ?))";
        PreparedStatement sqlExcluir = con.prepareStatement(sql);
        sqlExcluir.setInt(1, obj.getCodigo().intValue());
        sqlExcluir.execute();
    }

    /**
     * Responsável por realizar uma consulta de <code>Telefone</code> através do valor do atributo
     * <code>nome</code> da classe <code>Pessoa</code>
     * Faz uso da operação <code>montarDadosConsulta</code> que realiza o trabalho de prerarar o List resultante.
     *
     * @return List Contendo vários objetos da classe <code>TelefoneVO</code> resultantes da consulta.
     * @throws Exception Caso haja problemas de conexão ou restrição de acesso.
     */
    public List consultarPorNomePessoa(String valorConsulta, int nivelMontarDados) throws Exception {
        consultar(getIdEntidade(), true);
        String sqlStr = "SELECT Telefone.* FROM Telefone, Pessoa WHERE Telefone.pessoa = Pessoa.codigo and upper( Pessoa.nome ) like('" + valorConsulta.toUpperCase() + "%') ORDER BY Pessoa.nome";
        Statement stm = con.createStatement();
        ResultSet tabelaResultado = stm.executeQuery(sqlStr);
        return montarDadosConsulta(tabelaResultado, nivelMontarDados);
    }

    /**
     * Responsável por realizar uma consulta de <code>Telefone</code> através do valor do atributo
     * <code>String tipoTelefone</code>. Retorna os objetos, com início do valor do atributo idêntico ao parâmetro fornecido.
     * Faz uso da operação <code>montarDadosConsulta</code> que realiza o trabalho de prerarar o List resultante.
     *
     * @param controlarAcesso Indica se a aplicação deverá verificar se o usuário possui permissão para esta consulta ou não.
     * @return List Contendo vários objetos da classe <code>TelefoneVO</code> resultantes da consulta.
     * @throws Exception Caso haja problemas de conexão ou restrição de acesso.
     */
    public List consultarPorTipoTelefone(String valorConsulta, boolean controlarAcesso, int nivelMontarDados) throws Exception {
        consultar(getIdEntidade(), controlarAcesso);
        String sqlStr = "SELECT * FROM Telefone WHERE upper( tipoTelefone ) like('" + valorConsulta.toUpperCase() + "%') ORDER BY tipoTelefone";
        Statement stm = con.createStatement();
        ResultSet tabelaResultado = stm.executeQuery(sqlStr);
        return (montarDadosConsulta(tabelaResultado, nivelMontarDados));
    }

    /**
     * Responsável por realizar uma consulta de <code>Telefone</code> através do valor do atributo
     * <code>String numero</code>. Retorna os objetos, com início do valor do atributo idêntico ao parâmetro fornecido.
     * Faz uso da operação <code>montarDadosConsulta</code> que realiza o trabalho de prerarar o List resultante.
     *
     * @param controlarAcesso Indica se a aplicação deverá verificar se o usuário possui permissão para esta consulta ou não.
     * @return List Contendo vários objetos da classe <code>TelefoneVO</code> resultantes da consulta.
     * @throws Exception Caso haja problemas de conexão ou restrição de acesso.
     */
    public List consultarPorNumero(String valorConsulta, boolean controlarAcesso, int nivelMontarDados) throws Exception {
        consultar(getIdEntidade(), controlarAcesso);
        String sqlStr = "SELECT * FROM Telefone WHERE upper( numero ) like('" + valorConsulta.toUpperCase() + "%') ORDER BY numero";
        Statement stm = con.createStatement();
        ResultSet tabelaResultado = stm.executeQuery(sqlStr);
        return (montarDadosConsulta(tabelaResultado, nivelMontarDados));
    }

    /**
     * Responsável por realizar uma consulta de <code>Telefone</code> através do valor do atributo 
     * <code>Integer codigo</code>. Retorna os objetos com valores iguais ou superiores ao parâmetro fornecido.
     * Faz uso da operação <code>montarDadosConsulta</code> que realiza o trabalho de prerarar o List resultante.
     * @param   controlarAcesso Indica se a aplicação deverá verificar se o usuário possui permissão para esta consulta ou não.
     * @return  List Contendo vários objetos da classe <code>TelefoneVO</code> resultantes da consulta.
     * @exception Exception Caso haja problemas de conexão ou restrição de acesso.
     */
    public List consultarPorCodigo(Integer valorConsulta, boolean controlarAcesso, int nivelMontarDados) throws Exception {
        consultar(getIdEntidade(), controlarAcesso);
        String sqlStr = "SELECT * FROM Telefone WHERE codigo >= " + valorConsulta.intValue() + " ORDER BY codigo";
        Statement stm = con.createStatement();
        ResultSet tabelaResultado = stm.executeQuery(sqlStr);
        return (montarDadosConsulta(tabelaResultado, nivelMontarDados));
    }

    /**
     * Responsável por montar os dados de vários objetos, resultantes de uma consulta ao banco de dados (<code>ResultSet</code>).
     * Faz uso da operação <code>montarDados</code> que realiza o trabalho para um objeto por vez.
     * @return  List Contendo vários objetos da classe <code>TelefoneVO</code> resultantes da consulta.
     */
    public static List montarDadosConsulta(ResultSet tabelaResultado, int nivelMontarDados) throws Exception {
        List vetResultado = new ArrayList();
        while (tabelaResultado.next()) {
            TelefoneVO obj = new TelefoneVO();
            obj = montarDados(tabelaResultado, nivelMontarDados);
            vetResultado.add(obj);
        }
        return vetResultado;
    }

    /**
     * Responsável por montar os dados de vários objetos, resultantes de uma consulta ao banco de dados (<code>ResultSet</code>).
     * Faz uso da operação <code>montarDados</code> que realiza o trabalho para um objeto por vez.
     * @return  List Contendo vários objetos da classe <code>TelefoneVO</code> resultantes da consulta.
     */
    public static List montarDadosConsultaTelefone(ResultSet tabelaResultado, int nivelMontarDados) throws Exception {
        List vetResultado = new ArrayList();
        while (tabelaResultado.next()) {
            TelefoneVO obj = new TelefoneVO();
            obj = montarDadosTelefone(tabelaResultado, nivelMontarDados);
            vetResultado.add(obj);
        }
        return vetResultado;
    }

    public static TelefoneVO montarDadosBasico(ResultSet dadosSQL) throws Exception {
        TelefoneVO obj = new TelefoneVO();
        obj.setNovoObj(new Boolean(false));
        obj.setCodigo(new Integer(dadosSQL.getInt("codigo")));
        if (!UteisValidacao.emptyString(dadosSQL.getString("ddi"))){
            obj.setDdi(dadosSQL.getString("ddi"));
        }
        obj.setNumero(dadosSQL.getString("numero"));
        obj.setTipoTelefone(dadosSQL.getString("tipoTelefone"));
        obj.setPessoa(new Integer(dadosSQL.getInt("pessoa")));
        obj.setDescricao(dadosSQL.getString("descricao"));
        try {
            obj.setUsarNonoDigitoWApp(dadosSQL.getBoolean("usarNonoDigitoWApp"));
        }  catch (Exception e) {
            obj.setUsarNonoDigitoWApp(false);
        }

        return obj;
    }

    /**
     * Responsável por montar os dados resultantes de uma consulta ao banco de dados (<code>ResultSet</code>)
     * em um objeto da classe <code>TelefoneVO</code>.
     * @return  O objeto da classe <code>TelefoneVO</code> com os dados devidamente montados.
     */
    public static TelefoneVO montarDados(ResultSet dadosSQL, int nivelMontarDados) throws Exception {
        TelefoneVO obj = montarDadosBasico(dadosSQL);

        if (nivelMontarDados == Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA)
            return obj;

        if (nivelMontarDados == Uteis.NIVELMONTARDADOS_DADOSBASICOS) {
            return obj;
        }

        return obj;
    }

    /**
     * Responsável por montar os dados resultantes de uma consulta ao banco de dados (<code>ResultSet</code>)
     * em um objeto da classe <code>TelefoneVO</code>.
     * @return  O objeto da classe <code>TelefoneVO</code> com os dados devidamente montados.
     */
    public static TelefoneVO montarDadosTelefone(ResultSet dadosSQL, int nivelMontarDados) throws Exception {
        TelefoneVO obj = new TelefoneVO();
        obj.setCodigo(new Integer(dadosSQL.getInt("codigo")));
        obj.setNumero(dadosSQL.getString("numero"));
        obj.setRe(dadosSQL.getString("re"));
        obj.setCo(dadosSQL.getString("co"));
        obj.setCe(dadosSQL.getString("ce"));
        obj.setDescricao(dadosSQL.getString("descricao"));
        obj.setDdi(dadosSQL.getString("ddi"));
        obj.setNovoObj(new Boolean(false));
        if (nivelMontarDados == Uteis.NIVELMONTARDADOS_DADOSBASICOS) {
            return obj;
        }

        return obj;
    }

//    /**
//     * Operação responsável por excluir todos os objetos da <code>TelefoneVO</code> no BD.
//     * Faz uso da operação <code>excluir</code> disponível na classe <code>Telefone</code>.
//     * @param <code>pessoa</code> campo chave para exclusão dos objetos no BD.
//     * @exception Exception  Erro de conexão com o BD ou restrição de acesso a esta operação.
//     */
    public void excluirTelefones(Integer pessoa) throws Exception {
        excluir(getIdEntidade());
        String sql = "DELETE FROM Telefone WHERE (pessoa = ?)";
        PreparedStatement sqlExcluir = con.prepareStatement(sql);
        sqlExcluir.setInt(1, pessoa.intValue());
        sqlExcluir.execute();
    }

//    /**
//     * Operação responsável por excluir todos os objetos da <code>TelefoneVO</code> no BD.
//     * Faz uso da operação <code>excluir</code> disponível na classe <code>Telefone</code>.
//     * @param <code>pessoa</code> campo chave para exclusão dos objetos no BD.
//     * @exception Exception  Erro de conexão com o BD ou restrição de acesso a esta operação.
//     */
    public void excluirTelefones(Integer pessoa, boolean validarPermissao) throws Exception {
        if (validarPermissao) {
            excluir(getIdEntidade()); // Faz a validação de permissões
        }
        String sql = "DELETE FROM Telefone WHERE (pessoa = ?)";
        PreparedStatement sqlExcluir = con.prepareStatement(sql);
        sqlExcluir.setInt(1, pessoa.intValue());
        sqlExcluir.execute();
    }

    /**
     * Operação responsável por alterar todos os objetos da <code>TelefoneVO</code> contidos em um Hashtable no BD.
     * Faz uso da operação <code>excluirTelefones</code> e <code>incluirTelefones</code> disponíveis na classe <code>Telefone</code>.
     *
     * @param objetos List com os objetos a serem alterados ou incluídos no BD.
     * @throws Exception Erro de conexão com o BD ou restrição de acesso a esta operação.
     */
    public void alterarTelefones(Integer pessoa, List objetos, Boolean empresaInternacional) throws Exception {
        alterarTelefones(pessoa, objetos, empresaInternacional, true);
    }

    public void alterarTelefones(Integer pessoa, List objetos, Boolean empresaInternacional, boolean controlarAcesso) throws Exception {
        String str = "DELETE FROM  Telefone  WHERE pessoa = " + pessoa;
        Iterator i = objetos.iterator();
        while (i.hasNext()) {
            TelefoneVO objeto = (TelefoneVO) i.next();
            str += " AND codigo <> " + objeto.getCodigo();
        }
        PreparedStatement sqlExcluir = con.prepareStatement(str);
        sqlExcluir.execute();
        Iterator e = objetos.iterator();
        while (e.hasNext()) {
            TelefoneVO obj = (TelefoneVO) e.next();
            obj.setUsarSistemaInternacional(empresaInternacional == null ? false : empresaInternacional);
            if (obj.getCodigo().equals(0)) {
                obj.setPessoa(pessoa);
                incluir(obj, controlarAcesso);
            } else {
                alterar(obj, controlarAcesso);
            }
        }
    }

    /**
     * Operação responsável por incluir objetos da <code>TelefoneVO</code> no BD.
     * Garantindo o relacionamento com a entidade principal <code>basico.Pessoa</code> através do atributo de vínculo.
     *
     * @param objetos List contendo os objetos a serem gravados no BD da classe.
     * @throws Exception Erro de conexão com o BD ou restrição de acesso a esta operação.
     */
    public void incluirTelefones(Integer pessoaPrm, List objetos) throws Exception {
        incluirTelefones(pessoaPrm, objetos, true);
    }
    public void incluirTelefones(Integer pessoaPrm, List objetos, boolean validarPermissao) throws Exception {
        Iterator e = objetos.iterator();
        while (e.hasNext()) {
            TelefoneVO obj = (TelefoneVO) e.next();
            if (!obj.isEmpty()) {
                obj.setPessoa(pessoaPrm);
                incluir(obj, validarPermissao);
            }
        }
    }

    /**
     * Operação responsável por consultar todos os <code>TelefoneVO</code> relacionados a um objeto da classe <code>basico.Pessoa</code>.
     *
     * @param pessoa Atributo de <code>basico.Pessoa</code> a ser utilizado para localizar os objetos da classe <code>TelefoneVO</code>.
     * @return List  Contendo todos os objetos da classe <code>TelefoneVO</code> resultantes da consulta.
     * @throws Exception Erro de conexão com o BD ou restrição de acesso a esta operação.
     */
    public List<TelefoneVO> consultarTelefones(Integer pessoa, Boolean controleAcesso, int nivelMontarDados) throws Exception {
        consultar(getIdEntidade(), controleAcesso);
        List<TelefoneVO> objetos = new ArrayList<TelefoneVO>();
        String sql = "SELECT * FROM Telefone WHERE pessoa = ? order by codigo";
        PreparedStatement sqlConsulta = con.prepareStatement(sql);
        sqlConsulta.setInt(1, pessoa);
        ResultSet resultado = sqlConsulta.executeQuery();
        while (resultado.next()) {
            TelefoneVO novoObj = Telefone.montarDados(resultado, nivelMontarDados);
            objetos.add(novoObj);
        }
        return objetos;
    }

    /**
     * Operação responsável por localizar um objeto da classe <code>TelefoneVO</code>
     * através de sua chave primária.
     *
     * @throws Exception Caso haja problemas de conexão ou localização do objeto procurado.
     */
    public TelefoneVO consultarPorChavePrimaria(Integer codigoPrm, int nivelMontarDados) throws Exception {
        consultar(getIdEntidade(), false);
        String sql = "SELECT * FROM Telefone WHERE codigo = ?";
        PreparedStatement sqlConsultar = con.prepareStatement(sql);
        sqlConsultar.setInt(1, codigoPrm.intValue());
        ResultSet tabelaResultado = sqlConsultar.executeQuery();
        if (!tabelaResultado.next()) {
            throw new ConsistirException("Dados Não Encontrados ( Telefone ).");
        }
        return (montarDados(tabelaResultado, nivelMontarDados));
    }

    public TelefoneVO consultar(Integer codigoPessoa, TipoTelefoneEnum tipoTelefoneEnum, int nivelMontarDados) throws Exception {
        String sql = "SELECT * FROM Telefone WHERE pessoa = ? and tipoTelefone = ?";
        PreparedStatement sqlConsultar = con.prepareStatement(sql);
        sqlConsultar.setInt(1, codigoPessoa);
        sqlConsultar.setString(2, tipoTelefoneEnum.getCodigo());
        ResultSet tabelaResultado = sqlConsultar.executeQuery();
        if (tabelaResultado.next()) {
            return (montarDados(tabelaResultado, nivelMontarDados));
        }
        return null;
    }


    /**
     * Método que consulta os registros os telefones que existam nas tabelas pessoa, telefone e interessado
     *
     * @param parametro
     * @return lista de interessadoVO
     * @throws Exception
     * <AUTHOR>
     */
    public List<TelefoneVO> consultarTelefones(final TelefoneVO parametro, int nivelMontarDados) throws Exception {
        // criar consulta
        StringBuilder sql = new StringBuilder();
        sql.append("select p.nome, I.telefone AS re, I.telefonecomercial AS co, I.celular, T.numero AS ce, T.codigo from pessoa P ");
        sql.append("left join interessado I on I.pessoa = P.codigo ");
        sql.append("inner join telefone T on T.pessoa = P.codigo ");
        sql.append("where T.numero = ? OR I.celular = ? OR I.telefonecomercial = ? OR I.telefone = ? ");
        // preparar consulta
        Declaracao dc = new Declaracao(sql.toString(), this.con);
        // executar consulta
        ResultSet tabelaResultado = dc.executeQuery();
        // retornar dados montados
        return this.montarDadosConsultaTelefone(tabelaResultado, nivelMontarDados);
    }

    public Map<String, List<TelefoneVO>> obterTelefonesEnvioAgendado(String sqlClientes) throws Exception {
        ResultSet resultSet = criarConsulta("SELECT t.numero, p.nome, cli.codigo as codigocliente, t.pessoa FROM telefone t "
                + " inner join pessoa p on p.codigo = t.pessoa "
                + " inner join cliente cli on p.codigo = cli.pessoa "
                + " WHERE t.pessoa IN(" + sqlClientes + ") and tipotelefone like 'CE'  order by cli.situacao ", con);
        Map<String, List<TelefoneVO>> mapa = new HashMap<String, List<TelefoneVO>>();
        while (resultSet.next()) {
            if (Uteis.telefoneValido(resultSet.getString("numero"))) {
                TelefoneVO t = new TelefoneVO();
                t.setCodigo(resultSet.getInt("codigocliente"));
                t.setNumero(resultSet.getString("numero"));
                t.setPessoa(resultSet.getInt("pessoa"));
                if (!mapa.containsKey(resultSet.getString("nome"))) {
                    mapa.put(resultSet.getString("nome"), new ArrayList<TelefoneVO>());
                }
                mapa.get(resultSet.getString("nome")).add(t);
            }

        }
        return mapa;

    }

    public Map<String, List<TelefoneVO>> obterTelefonesEnvioAgendadoBotConversa(String sqlClientes, MalaDiretaVO maladireta) throws Exception {
        ResultSet resultSet = criarConsulta(" select * from ( SELECT t.numero, p.nome, cli.codigo as codigocliente, t.pessoa FROM telefone t "
                + " inner join pessoa p on p.codigo = t.pessoa "
                + " inner join cliente cli on p.codigo = cli.pessoa "
                + " WHERE t.pessoa IN(" + sqlClientes + ") order by cli.situacao ) as tab "
                + " where tab.codigocliente not in (select h.cliente from historicocontato h  where h.maladireta = " + maladireta.getCodigo()
                + " and h.dia::date = '" + new java.sql.Date(maladireta.getDataEnvio().getTime()) + "') ", con);
        Map<String, List<TelefoneVO>> mapa = new HashMap<String, List<TelefoneVO>>();
        while (resultSet.next()) {
            if (Uteis.telefoneValido(resultSet.getString("numero"))) {
                TelefoneVO t = new TelefoneVO();
                t.setCodigo(resultSet.getInt("codigocliente"));
                t.setNumero(resultSet.getString("numero"));
                t.setPessoa(resultSet.getInt("pessoa"));
                if (!mapa.containsKey(resultSet.getString("nome"))) {
                    mapa.put(resultSet.getString("nome"), new ArrayList<TelefoneVO>());
                }
                mapa.get(resultSet.getString("nome")).add(t);
            }

        }
        return mapa;
    }

    public Map<String, List<TelefoneVO>> obterTelefonesIndicados(String sql) throws Exception {
        ResultSet resultSet = criarConsulta(sql, con);
        Map<String, List<TelefoneVO>> mapa = new HashMap<String, List<TelefoneVO>>();
        while (resultSet.next()) {
            TelefoneVO t = new TelefoneVO();
            t.setCodigo(resultSet.getInt("codigo"));
            t.setNumero(resultSet.getString("telefones"));

            if (!mapa.containsKey(resultSet.getString("nome"))) {
                mapa.put(resultSet.getString("nome"), new ArrayList<TelefoneVO>());
            }
            mapa.get(resultSet.getString("nome")).add(t);
        }
        return mapa;

    }

    public List<TelefoneVO> consultarTelefonesColaborador(Integer colaborador, Boolean controleAcesso, int nivelMontarDados) throws Exception {
        consultar(getIdEntidade(), controleAcesso);
        List<TelefoneVO> objetos = new ArrayList<TelefoneVO>();
        String sql = "SELECT * FROM Telefone t"
                + " INNER JOIN colaborador c ON c.pessoa = t.pessoa"
                + " WHERE c.codigo = ? order by t.codigo";
        PreparedStatement sqlConsulta = con.prepareStatement(sql);
        sqlConsulta.setInt(1, colaborador);
        ResultSet resultado = sqlConsulta.executeQuery();
        while (resultado.next()) {
            TelefoneVO novoObj = Telefone.montarDados(resultado, nivelMontarDados);
            objetos.add(novoObj);
        }
        return objetos;
    }

    public List<TelefoneVO> consultarTelefoneExistePassivoIndicado(String numero, Boolean controleAcesso) throws Exception {
        consultar(getIdEntidade(), controleAcesso);
        List<TelefoneVO> listaTelefones = new ArrayList<TelefoneVO>();
        StringBuilder sql = new StringBuilder();
        sql.append("select * from ( \n");
        sql.append("(select i.codigo, i.nomeindicado as nome, i.telefoneindicado  as celular, i.telefone  as residencial, '' as trabalho, 'INDICAÇÃO' as origem from indicado i where i.cliente is null) \n");
        sql.append("UNION ALL \n");
        sql.append("(select p.codigo, p.nome, p.telefonecelular  as celular, p.telefoneresidencial  as residencial, p.telefonetrabalho as trabalho , 'RECEPTIVO' as origem from passivo p where p.cliente is null)) as tabela \n");
        sql.append("WHERE celular ilike '%").append(numero).append("%' \n");
        sql.append("OR residencial ilike '%").append(numero).append("%' \n");
        sql.append("OR trabalho ilike '%").append(numero).append("%' \n");
        sql.append("LIMIT 5");

        PreparedStatement pst = con.prepareStatement(sql.toString());
        ResultSet resultado = pst.executeQuery();
        while (resultado.next()) {
            TelefoneVO telefoneResultado = new TelefoneVO();
            //UTILIZAR A DESCRIÇÃO PARA PASSAR O NOME DA PESSOA
            telefoneResultado.setDescricao(resultado.getString("nome"));
            //UTILIZAR O TIPO DE TELEFONE PARA INFORMAR SE É PASSIVO OU INDICADO
            telefoneResultado.setTipoTelefone(resultado.getString("origem"));
            listaTelefones.add(telefoneResultado);
        }
        return listaTelefones;
    }

    public void persistirTelefonesClienteLead(Integer codigoPessoa, String celular, String teleFoneResidencial, String teleFoneTrabalho) throws Exception {
        List<TelefoneVO> listaExistentes = consultarTelefones(codigoPessoa, false, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
        boolean celularJaCadastrado = false;
        boolean residenciaJaCadastrado = false;
        boolean trabalhoJaCadastrado = false;
        for (TelefoneVO telefoneVO : listaExistentes) {
            if (telefoneVO.getNumero().equals(celular)) {
                celularJaCadastrado = true;
            }
            if (telefoneVO.getNumero().equals(teleFoneResidencial)) {
                residenciaJaCadastrado = true;
            }
            if (telefoneVO.getNumero().equals(teleFoneTrabalho)) {
                trabalhoJaCadastrado = true;
            }
        }
        if (!UteisValidacao.emptyString(celular) && !celularJaCadastrado) {
            TelefoneVO celularVO = new TelefoneVO();
            celularVO.setNumero(celular);
            celularVO.setPessoa(codigoPessoa);
            celularVO.setTipoTelefone("CE");
            incluir(celularVO);
        }
        if (!UteisValidacao.emptyString(teleFoneResidencial) && !residenciaJaCadastrado) {
            TelefoneVO telefoneVO = new TelefoneVO();
            telefoneVO.setNumero(teleFoneResidencial);
            telefoneVO.setPessoa(codigoPessoa);
            telefoneVO.setTipoTelefone("RE");
            incluir(telefoneVO);
        }
        if (!UteisValidacao.emptyString(teleFoneTrabalho) && !trabalhoJaCadastrado) {
            TelefoneVO telefoneVO = new TelefoneVO();
            telefoneVO.setNumero(teleFoneTrabalho);
            telefoneVO.setPessoa(codigoPessoa);
            telefoneVO.setTipoTelefone("RE");
            incluir(telefoneVO);
        }
    }

    public boolean updateEspacoVazio(Integer codigoEmpresa) throws Exception {
        String update = "UPDATE telefone t SET numero = REPLACE(REPLACE(numero,' ',''),'-','') FROM cliente cli\n" +
                "WHERE cli.pessoa = t.pessoa\n" +
                "AND numero like '% %' OR numero like '%-%'\n" +
                "AND cli.empresa = ?";
        PreparedStatement pstm = con.prepareStatement(update);
        pstm.setInt(1, codigoEmpresa);
        pstm.execute();
        return true;
    }

    public boolean limparRegistrosSujos(Integer codigo) throws Exception {
        StringBuilder sql = new StringBuilder();
        sql.append("DELETE FROM telefone WHERE length(numero) = 0 AND pessoa IN " +
                        "(SELECT pessoa FROM cliente WHERE empresa = ").
                append(codigo).
                append(")");

        PreparedStatement preparedStatement = con.prepareStatement(sql.toString());
        preparedStatement.execute();

        return true;
    }

    public boolean updateRemoverZero(Integer codigo) throws SQLException {
        StringBuilder sql = new StringBuilder();
        sql.append("UPDATE telefone SET numero = SUBSTRING(numero, 2) WHERE numero like '0%' AND pessoa IN " +
                        "(SELECT pessoa FROM cliente WHERE empresa = ").
                append(codigo).
                append(")");
        PreparedStatement preparedStatement = con.prepareStatement(sql.toString());
        preparedStatement.execute();

        return true;
    }

    public boolean updateMascaraErrada(String ddd, Integer codigo) throws Exception {
        StringBuilder sql = new StringBuilder();
        sql.append("UPDATE telefone SET numero = '(").
                append(ddd).
                append(")9' || substring(numero, 3) WHERE numero like '").
                append(ddd).
                append("%' AND tipotelefone = 'CE' AND length(numero) = 10 AND pessoa IN " +
                        "(SELECT pessoa FROM cliente WHERE empresa = ").
                append(codigo).
                append(");\n");

        sql.append(" UPDATE telefone SET numero = '(").
                append(ddd).
                append(")' || substring(numero, 3) WHERE numero like '").
                append(ddd).
                append("%' AND tipotelefone = 'CE' AND length(numero) = 11 AND pessoa IN " +
                        "(SELECT pessoa FROM cliente WHERE empresa = ").
                append(codigo).
                append(");\n");

        sql.append(" UPDATE telefone SET numero = '(").
                append(ddd).
                append(")' || substring(numero, 3) WHERE numero like '").
                append(ddd).
                append("%' AND tipotelefone <> 'CE' AND length(numero) < 14  AND pessoa IN " +
                        "(SELECT pessoa FROM cliente WHERE empresa = ").
                append(codigo).
                append(");");

        PreparedStatement preparedStatement = con.prepareStatement(sql.toString());
        preparedStatement.execute();

        return true;
    }

    public boolean dezDigitos(Integer codigo) throws Exception {
        StringBuilder sql = new StringBuilder();
        sql.append("UPDATE telefone SET numero = '(' || substring(numero, 0, 3) || ')' || substring(numero, 3) " +
                        "WHERE length(numero) = 10 AND tipotelefone <> 'CE' AND pessoa IN " +
                        "(SELECT pessoa FROM cliente WHERE empresa = ").
                append(codigo).
                append(");\n");

        sql.append(" UPDATE telefone SET numero = '(' || substring(numero, 0, 3) || ')9' || substring(numero, 3) " +
                        "WHERE length(numero) = 10 AND tipotelefone = 'CE' AND pessoa IN " +
                        "(SELECT pessoa FROM cliente WHERE empresa = ").
                append(codigo).
                append(");");

        PreparedStatement preparedStatement = con.prepareStatement(sql.toString());
        preparedStatement.execute();

        return true;
    }

    public boolean oitoDigitos(String ddd, Integer codigo) throws Exception {
        StringBuilder sql = new StringBuilder();
        sql.append("UPDATE telefone SET numero = '(").
                append(ddd).
                append(")9' || numero WHERE length(numero) = 8 AND tipotelefone = 'CE' AND pessoa IN " +
                        "(SELECT pessoa FROM cliente WHERE empresa = ").
                append(codigo).
                append(");\n");

        sql.append(" UPDATE telefone SET numero = '(").
                append(ddd).
                append(")' || numero WHERE length(numero) = 8 AND tipotelefone <> 'CE' AND pessoa IN " +
                        "(SELECT pessoa FROM cliente WHERE empresa = ").
                append(codigo).
                append(");\n");

        sql.append(" UPDATE telefone SET numero = '(").
                append(ddd).
                append(")' || numero WHERE length(numero) = 9 AND pessoa IN " +
                        "(SELECT pessoa FROM cliente WHERE empresa  = ").
                append(codigo).
                append(");\n");

        PreparedStatement preparedStatement = con.prepareStatement(sql.toString());
        preparedStatement.execute();

        return true;
    }

    public boolean noveDigitos(Integer codigo) throws Exception {
        StringBuilder sql = new StringBuilder();
        sql.append("UPDATE telefone SET numero = '(' || substring(numero, 0, 3) || ')' || substring(numero, 3) " +
                        "WHERE length(numero) = 11 AND tipotelefone = 'CE' AND pessoa IN " +
                        "(SELECT pessoa FROM cliente WHERE empresa = ").
                append(codigo).
                append(");");

        PreparedStatement preparedStatement = con.prepareStatement(sql.toString());
        preparedStatement.execute();

        return true;
    }

    public Map<Integer,String> naoAlterados(Integer codigo) throws Exception {
        StringBuilder sql = new StringBuilder();
        sql.append("SELECT tel.codigo as codigo, cli.matricula, tel.pessoa as pessoa, pes.nome, tel.tipotelefone, tel.numero as numero FROM telefone tel\n").
                append(" LEFT JOIN pessoa pes ON tel.pessoa = pes.codigo\n").
                append(" LEFT JOIN cliente cli ON cli.pessoa = pes.codigo\n").
                append(" WHERE numero NOT LIKE '(%' AND cli.empresa = ").
                append(codigo);

        PreparedStatement preparedStatement = con.prepareStatement(sql.toString());
        ResultSet rs = preparedStatement.executeQuery();
        int cont = 0;
        Map<Integer, String> numerosNaoAlterados = new HashMap<Integer, String>();

        while (rs.next()) {
            numerosNaoAlterados.put(rs.getInt("codigo"), "Numero de Telefone: " + rs.getString("numero") + " - Pessoa: " + rs.getInt("pessoa"));
        }

        return numerosNaoAlterados;
    }

    public void removeTelefonesNaoAtualizados(Integer codigo, String numeroEPessoa) throws Exception {
        StringBuilder sql = new StringBuilder();

        sql.append("DELETE FROM telefone\n").
                append("WHERE codigo = ").
                append(codigo).
                append(";");

        PreparedStatement preparedStatement = con.prepareStatement(sql.toString());
        preparedStatement.execute();

        registrarLogExclusaoTelefone(codigo, numeroEPessoa);
    }

    public void registrarLogExclusaoTelefone (Integer codigo, String numeroEPessoa) throws Exception{
        Log logDAO = null;
        try {
            logDAO = new Log(this.con);
            //criar log
            LogVO obj = new LogVO();
            obj.setChavePrimaria(codigo.toString());
            obj.setPessoa(codigo);
            obj.setNomeEntidade("TELEFONE");
            obj.setNomeEntidadeDescricao("TELEFONE");
            obj.setOperacao("EXCLUSAO");
            obj.setResponsavelAlteracao("ADMIN");
            obj.setUserOAMD("AUTOMATICO");
            obj.setDataAlteracao(Calendario.hoje());
            obj.setNomeCampo("TODOS");
            obj.setValorCampoAnterior(numeroEPessoa);
            obj.setValorCampoAlterado("TODOS");
            logDAO.incluirSemCommit(obj);
        } catch (Exception ex) {
            ex.printStackTrace();
            throw ex;
        } finally {
            logDAO = null;
        }
    }

    /**
     * Retorna a lista de todos os codigos dos telefones cadastrados na base de dados.
     *
     * @return
     * @throws Exception
     */
    public List<Integer> consultarTodosCodigos() throws Exception {
        String sql = "SELECT codigo FROM Telefone ORDER BY codigo desc";
        List<Integer> codigos = new ArrayList<Integer>();
        ResultSet rs = con.prepareStatement(sql).executeQuery();
        while (rs.next()) {
            codigos.add(rs.getInt("codigo"));
        }
        return codigos;
    }

    public void atualizarTelefone(Integer codigo, String numero) throws Exception {
        String sql = "UPDATE telefone SET numero = ? WHERE codigo = ?";
        PreparedStatement ps = con.prepareStatement(sql);
        ps.setString(1, numero);
        ps.setInt(2, codigo);
        ps.executeUpdate();
    }

    public Boolean verificaSePossuiTelefone(Integer codigoPessoa) {
        String sql = "SELECT codigo FROM telefone WHERE pessoa = " + codigoPessoa;
        try (PreparedStatement ps = con.prepareStatement(sql)) {
            try (ResultSet rs = ps.executeQuery()) {
                if (rs.next()) {
                    return true;
                }
            }
        } catch (SQLException e) {
            throw new RuntimeException(e);
        }
        return false;
    }

    public List<TelefoneVO> consultarTelefoneExiste(String telefone, Integer pessoa,
                                                    Integer colaborador, int nivelMontarDados) throws Exception {
        StringBuilder sql = new StringBuilder();
        sql.append("select \n");
        sql.append("t.* \n");
        sql.append("from telefone t \n");
        sql.append("where t.numero ilike '").append(telefone).append("' \n");
        if (!UteisValidacao.emptyNumber(pessoa)) {
            sql.append("and t.pessoa = ").append(pessoa).append(" \n");
        }
        if (!UteisValidacao.emptyNumber(colaborador)) {
            sql.append("and t.pessoa in (select pessoa from colaborador where codigo = ").append(colaborador).append(") \n");
        }
        sql.append("order by codigo \n");

        List<TelefoneVO> lista = new ArrayList<>();
        try (PreparedStatement sqlConsulta = con.prepareStatement(sql.toString())) {
            try (ResultSet resultado = sqlConsulta.executeQuery()) {
                while (resultado.next()) {
                    lista.add(montarDados(resultado, nivelMontarDados));
                }
            }
        }
        return lista;
    }
}

package negocio.facade.jdbc.basico;

import negocio.comuns.basico.EstadoVO;
import negocio.comuns.basico.PaisVO;
import negocio.comuns.utilitarias.ConsistirException;
import negocio.comuns.utilitarias.Ordenacao;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisValidacao;
import negocio.facade.jdbc.arquitetura.SuperEntidade;
import negocio.interfaces.basico.PaisInterfaceFacade;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.sql.Statement;
import java.sql.Types;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Hashtable;
import java.util.List;

/**
 * Classe de persistência que encapsula todas as operações de manipulação dos dados da classe <code>PaisVO</code>.
 * Responsável por implementar operações como incluir, alterar, excluir e consultar pertinentes a classe <code>PaisVO</code>.
 * Encapsula toda a interação com o banco de dados.
 *
 * @see PaisVO
 * @see SuperEntidade
 */
public class Pais extends SuperEntidade implements PaisInterfaceFacade {

    private Hashtable estados;

    public Pais() throws Exception {
        super();
        setEstados(new Hashtable());
    }

    public Pais(Connection con) throws Exception {
        super(con);
        setEstados(new Hashtable());
    }

    /**
     * Responsável por montar os dados de vários objetos, resultantes de uma consulta ao banco de dados (<code>ResultSet</code>).
     * Faz uso da operação <code>montarDados</code> que realiza o trabalho para um objeto por vez.
     *
     * @return List Contendo vários objetos da classe <code>EstadoVO</code> resultantes da consulta.
     */
    public static List montarDadosConsultaEstado(ResultSet tabelaResultado, int nivelMontarDados) throws Exception {
        List vetResultado = new ArrayList();
        while (tabelaResultado.next()) {
            EstadoVO obj = montarDadosEstado(tabelaResultado, nivelMontarDados);
            vetResultado.add(obj);
        }
        return vetResultado;
    }

    /**
     * Responsável por montar os dados resultantes de uma consulta ao banco de dados (<code>ResultSet</code>)
     * em um objeto da classe <code>PaisVO</code>.
     *
     * @return O objeto da classe <code>PaisVO</code> com os dados devidamente montados.
     */
    public static EstadoVO montarDadosEstado(ResultSet dadosSQL, int nivelMontarDados) throws Exception {
        EstadoVO obj = new EstadoVO();
        obj.setCodigo(dadosSQL.getInt("codigo"));
        obj.setDescricao(dadosSQL.getString("descricao"));
        obj.setNovoObj(Boolean.FALSE);

        return obj;
    }

    /**
     * Responsável por montar os dados de vários objetos, resultantes de uma consulta ao banco de dados (<code>ResultSet</code>).
     * Faz uso da operação <code>montarDados</code> que realiza o trabalho para um objeto por vez.
     *
     * @return List Contendo vários objetos da classe <code>PaisVO</code> resultantes da consulta.
     */
    public static List montarDadosConsulta(ResultSet tabelaResultado, int nivelMontarDados, Connection con) throws Exception {
        List vetResultado = new ArrayList();
        while (tabelaResultado.next()) {
            PaisVO obj = montarDados(tabelaResultado, nivelMontarDados, con);
            vetResultado.add(obj);
        }
        return vetResultado;
    }
    /* (non-Javadoc)
     * @see negocio.interfaces.basico.PaisInterfaceFacade#excluir(negocio.comuns.basico.PaisVO)
     */

    /**
     * Responsável por montar os dados resultantes de uma consulta ao banco de dados (<code>ResultSet</code>)
     * em um objeto da classe <code>PaisVO</code>.
     *
     * @return O objeto da classe <code>PaisVO</code> com os dados devidamente montados.
     */
    public static PaisVO montarDados(ResultSet dadosSQL, int nivelMontarDados, Connection con) throws Exception {
        PaisVO obj = new PaisVO();
        obj.setCodigo(dadosSQL.getInt("codigo"));
        obj.setNome(dadosSQL.getString("nome"));
        try {
            obj.setNacionalidade(dadosSQL.getString("nacionalidade"));
        } catch (Exception ignored) {

        }
        obj.setNovoObj(false);

        if (Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA == nivelMontarDados) {
            return obj;
        }

        Estado estado = new Estado(con);
        obj.setEstadoVOs(estado.consultarEstados(obj.getCodigo(), nivelMontarDados));
        estado = null;

        return obj;
    }

    /**
     * Operação responsável por retornar um novo objeto da classe <code>PaisVO</code>.
     */
    public PaisVO novo() throws Exception {
        incluir(getIdEntidade());
        return new PaisVO();
    }

    /**
     * Operação responsável por incluir no banco de dados um objeto da classe <code>PaisVO</code>.
     * Primeiramente valida os dados (<code>validarDados</code>) do objeto. Verifica a conexão com o banco de dados e a permissão do usuário
     * para realizar esta operacão na entidade.
     * Isto, através da operação <code>incluir</code> da superclasse.
     *
     * @param obj Objeto da classe <code>PaisVO</code> que será gravado no banco de dados.
     * @throws Exception Caso haja problemas de conexão, restrição de acesso ou validação de dados.
     */
    public void incluir(PaisVO obj, boolean centralEventos) throws Exception {
        try {
            con.setAutoCommit(false);
            incluirSemCommit(obj);
            con.commit();
        } catch (Exception e) {
            obj.setNovoObj(true);
            con.rollback();
            con.setAutoCommit(true);
            throw e;
        } finally {
            con.setAutoCommit(true);
        }
    }

    public void incluirSemCommit(PaisVO obj) throws Exception {
        Estado estadoDAO;
        try {
            estadoDAO = new Estado(con);
            PaisVO.validarDados(obj);
            obj.realizarUpperCaseDados();
            String sql = "INSERT INTO Pais(nome, nacionalidade) VALUES (?, ?)";
            try (PreparedStatement sqlInserir = con.prepareStatement(sql)) {
                int i = 0;
                sqlInserir.setString(++i, obj.getNome());
                if (UteisValidacao.emptyString(obj.getNacionalidade())) {
                    sqlInserir.setNull(++i, Types.VARCHAR);
                } else {
                    sqlInserir.setString(++i, obj.getNacionalidade());
                }
                sqlInserir.execute();
            }
            obj.setCodigo(obterValorChavePrimariaCodigo());
            estadoDAO.incluirEstados(obj.getCodigo(), obj.getEstadoVOs());
            obj.setNovoObj(false);
        } catch (Exception e) {
            obj.setNovoObj(true);
            throw e;
        } finally {
            estadoDAO = null;
        }
    }

    public void incluir(PaisVO obj) throws Exception {
        this.incluir(obj, false);
    }
    /* (non-Javadoc)
     * @see negocio.interfaces.basico.PaisInterfaceFacade#excluir(negocio.comuns.basico.PaisVO)
     */

    /**
     * Operação responsável por alterar no BD os dados de um objeto da classe <code>PaisVO</code>.
     * Sempre utiliza a chave primária da classe como atributo para localização do registro a ser alterado.
     * Primeiramente valida os dados (<code>validarDados</code>) do objeto. Verifica a conexão com o banco de dados e a permissão do usuário
     * para realizar esta operacão na entidade.
     * Isto, através da operação <code>alterar</code> da superclasse.
     *
     * @param obj Objeto da classe <code>PaisVO</code> que será alterada no banco de dados.
     * @throws Exception Caso haja problemas de conexão, restrição de acesso ou validação de dados.
     */
    public void alterar(PaisVO obj, boolean centralEventos) throws Exception {
        try {
            con.setAutoCommit(false);
            alterarSemCommit(obj);
            con.commit();
        } catch (Exception e) {
            con.rollback();
            con.setAutoCommit(true);
            throw e;
        } finally {
            con.setAutoCommit(true);
        }
    }

    public void alterarSemCommit(PaisVO obj) throws Exception {
        Estado estadoDAO;
        try {
            estadoDAO = new Estado(con);

            PaisVO.validarDados(obj);
            obj.realizarUpperCaseDados();
            String sql = "UPDATE Pais set nome = ?, nacionalidade = ? WHERE codigo = ?";
            try (PreparedStatement sqlAlterar = con.prepareStatement(sql)) {
                int i = 0;
                sqlAlterar.setString(++i, obj.getNome());
                if (UteisValidacao.emptyString(obj.getNacionalidade())) {
                    sqlAlterar.setNull(++i, Types.VARCHAR);
                } else {
                    sqlAlterar.setString(++i, obj.getNacionalidade());
                }
                sqlAlterar.setInt(++i, obj.getCodigo());
                sqlAlterar.execute();
            }
            estadoDAO.alterarEstados(obj.getCodigo(), obj.getEstadoVOs());
        } finally {
            estadoDAO = null;
        }
    }

    public void alterar(PaisVO obj) throws Exception {
        this.alterar(obj, false);
    }

    /**
     * Operação responsável por excluir no BD um objeto da classe <code>PaisVO</code>.
     * Sempre localiza o registro a ser excluído através da chave primária da entidade.
     * Primeiramente verifica a conexão com o banco de dados e a permissão do usuário
     * para realizar esta operacão na entidade.
     * Isto, através da operação <code>excluir</code> da superclasse.
     *
     * @param obj Objeto da classe <code>PaisVO</code> que será removido no banco de dados.
     * @throws Exception Caso haja problemas de conexão ou restrição de acesso.
     */
    public void excluir(PaisVO obj, boolean centralEventos) throws Exception {
        Estado estadoDAO;
        try {
            con.setAutoCommit(false);
            estadoDAO = new Estado(con);
            if (!centralEventos) {
                excluir(getIdEntidade());
            }
            String sql = "DELETE FROM Pais WHERE codigo = ?";
            try (PreparedStatement sqlExcluir = con.prepareStatement(sql)) {
                sqlExcluir.setInt(1, obj.getCodigo());
                sqlExcluir.execute();
            }
            estadoDAO.excluirEstados(obj.getCodigo());
            con.commit();
        } catch (Exception e) {
            con.rollback();
            con.setAutoCommit(true);
            throw e;
        } finally {
            estadoDAO = null;
            con.setAutoCommit(true);
        }
    }

    public void excluir(PaisVO obj) throws Exception {
        this.excluir(obj, false);
    }

    /**
     * Responsável por realizar uma consulta de <code>Pais</code> através do valor do atributo
     * <code>String nome</code>. Retorna os objetos, com início do valor do atributo idêntico ao parâmetro fornecido.
     * Faz uso da operação <code>montarDadosConsulta</code> que realiza o trabalho de prerarar o List resultante.
     *
     * @param controlarAcesso Indica se a aplicação deverá verificar se o usuário possui permissão para esta consulta ou não.
     * @return List Contendo vários objetos da classe <code>PaisVO</code> resultantes da consulta.
     * @throws Exception Caso haja problemas de conexão ou restrição de acesso.
     */
    public List consultarPorNome(String valorConsulta, boolean controlarAcesso, int nivelMontarDados) throws Exception {
        consultar(getIdEntidade(), controlarAcesso);
        String sqlStr = "SELECT * FROM Pais WHERE upper( nome ) like('" + valorConsulta.toUpperCase() + "%') ORDER BY nome";
        try (Statement stm = con.createStatement();
             ResultSet tabelaResultado = stm.executeQuery(sqlStr)) {
            return (montarDadosConsulta(tabelaResultado, nivelMontarDados, this.con));
        }
    }

    public PaisVO consultarPorNome(String valorConsulta, int nivelMontarDados) throws Exception {
        String sqlStr = "SELECT * FROM Pais WHERE upper( nome ) like('" + valorConsulta.toUpperCase() + "%') ORDER BY nome";
        try (Statement stm = con.createStatement()) {
            try (ResultSet tabelaResultado = stm.executeQuery(sqlStr)) {
                if (!tabelaResultado.next()) {
                    return null;
                }
                return (montarDados(tabelaResultado, nivelMontarDados, this.con));
            }
        }
    }

    /**
     * Responsável por realizar uma consulta de <code>Pais</code> através do valor do atributo
     * <code>Integer codigo</code>. Retorna os objetos com valores iguais ou superiores ao parâmetro fornecido.
     * Faz uso da operação <code>montarDadosConsulta</code> que realiza o trabalho de prerarar o List resultante.
     *
     * @param controlarAcesso Indica se a aplicação deverá verificar se o usuário possui permissão para esta consulta ou não.
     * @return List Contendo vários objetos da classe <code>PaisVO</code> resultantes da consulta.
     * @throws Exception Caso haja problemas de conexão ou restrição de acesso.
     */
    public List consultarPorCodigo(Integer valorConsulta, boolean controlarAcesso, int nivelMontarDados) throws Exception {
        consultar(getIdEntidade(), controlarAcesso);
        String sqlStr = "SELECT * FROM Pais WHERE codigo >= " + valorConsulta.intValue() + " ORDER BY codigo";
        try (Statement stm = con.createStatement()) {
            try (ResultSet tabelaResultado = stm.executeQuery(sqlStr)) {
                return (montarDadosConsulta(tabelaResultado, nivelMontarDados, this.con));
            }
        }
    }

    public PaisVO consultarPorCodigoExato(Integer valorConsulta, boolean controlarAcesso, int nivelMontarDados) throws Exception {
        consultar(getIdEntidade(), controlarAcesso);
        String sql = "SELECT * FROM Pais WHERE codigo = ?";
        PaisVO pais;
        try (PreparedStatement sqlConsultar = con.prepareStatement(sql)) {
            sqlConsultar.setInt(1, valorConsulta.intValue());
            try (ResultSet tabelaResultado = sqlConsultar.executeQuery()) {
                pais = null;
                while (tabelaResultado.next()) {
                    pais = montarDados(tabelaResultado, nivelMontarDados, this.con);
                }
            }
        }
        return pais;
    }

    public List consultarEstadoPorPais(Integer valorConsulta, int nivelMontarDados) throws Exception {
        consultar(getIdEntidade(), false);
        String sqlStr = "SELECT * FROM estado WHERE pais = " + valorConsulta + " ORDER BY descricao";
        try (Statement stm = con.createStatement()) {
            try (ResultSet tabelaResultado = stm.executeQuery(sqlStr)) {
                return (montarDadosConsultaEstado(tabelaResultado, nivelMontarDados));
            }
        }
    }

    /**
     * Operação responsável por adicionar um objeto da <code>EstadoVO</code> no Hashtable <code>Estados</code>.
     * Neste Hashtable são mantidos todos os objetos de Estado de uma determinada Pais.
     *
     * @param obj Objeto a ser adicionado no Hashtable.
     */
    public void adicionarObjEstados(EstadoVO obj) throws Exception {
        getEstados().put(obj.getSigla() + "", obj);
        //adicionarObjSubordinadoOC
    }

    /**
     * Operação responsável por remover um objeto da classe <code>EstadoVO</code> do Hashtable <code>Estados</code>.
     * Neste Hashtable são mantidos todos os objetos de Estado de uma determinada Pais.
     *
     * @param Sigla Atributo da classe <code>EstadoVO</code> utilizado como apelido (key) no Hashtable.
     */
    public void excluirObjEstados(String Sigla) throws Exception {
        getEstados().remove(Sigla + "");
        //excluirObjSubordinadoOC
    }

    /**
     * Operação responsável por localizar um objeto da classe <code>PaisVO</code>
     * através de sua chave primária.
     *
     * @throws Exception Caso haja problemas de conexão ou localização do objeto procurado.
     */
    public PaisVO consultarPorChavePrimaria(Integer codigoPrm, int nivelMontarDados) throws Exception {
        consultar(getIdEntidade(), false);
        PaisVO eCache = (PaisVO) obterFromCache(codigoPrm);
        if (eCache != null) {
            return eCache;
        }
        String sql = "SELECT * FROM Pais WHERE codigo = ?";
        try (PreparedStatement sqlConsultar = con.prepareStatement(sql)) {
            sqlConsultar.setInt(1, codigoPrm.intValue());
            try (ResultSet tabelaResultado = sqlConsultar.executeQuery()) {
                if (!tabelaResultado.next()) {
                    throw new ConsistirException("Dados Não Encontrados ( Pais ).");
                }
                eCache = (montarDados(tabelaResultado, nivelMontarDados, this.con));
            }
        }
        putToCache(eCache);
        return eCache;
    }

    public Hashtable getEstados() {
        return (estados);
    }

    public void setEstados(Hashtable estados) {
        this.estados = estados;
    }

    public String consultarJSON() throws Exception {

        StringBuilder json;
        boolean dados;
        try (ResultSet rs = getPS().executeQuery()) {
            json = new StringBuilder();
            json.append("{\"aaData\":[");
            dados = false;
            while (rs.next()) {
                dados = true;
                json.append("[\"").append(rs.getString("codigo")).append("\",");
                json.append("\"").append(Uteis.normalizarStringJSON(rs.getString("nome").trim())).append("\"],");
            }
        }
        if (dados) {
            json.deleteCharAt(json.toString().length() - 1);
        }
        json.append("]}");
        return json.toString();
    }

    private PreparedStatement getPS() throws SQLException {
        String sql = "SELECT codigo, nome FROM pais ORDER BY nome";
        return con.prepareStatement(sql);
    }

    public List consultarParaImpressao(String filtro, String ordem, String campoOrdenacao, int i) throws SQLException {
        List lista;
        try (ResultSet rs = getPS().executeQuery()) {
            lista = new ArrayList();
            while (rs.next()) {
                PaisVO pais = new PaisVO();
                String geral = rs.getString("codigo") + rs.getString("nome");
                if (geral.toLowerCase().contains(filtro.toLowerCase())) {
                    pais.setCodigo(rs.getInt("codigo"));
                    pais.setNome(rs.getString("nome"));
                    lista.add(pais);
                }
            }
        }
        if (campoOrdenacao.equals("Código")) {
            Ordenacao.ordenarLista(lista, "codigo");
        } else if (campoOrdenacao.equals("Nome")) {
            Ordenacao.ordenarLista(lista, "nome");
        }
        if (ordem.contains("desc")) {
            Collections.reverse(lista);
        }
        return lista;

    }

    public List<PaisVO> consultarTodos(int nivelMontarDados) throws Exception {
        String sqlStr = "SELECT * FROM Pais ORDER BY nome";
        try (Statement stm = con.createStatement();
             ResultSet tabelaResultado = stm.executeQuery(sqlStr)) {
            return montarDadosConsulta(tabelaResultado, nivelMontarDados, this.con);
        }
    }
}

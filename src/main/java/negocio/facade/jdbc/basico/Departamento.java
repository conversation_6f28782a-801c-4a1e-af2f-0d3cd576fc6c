package negocio.facade.jdbc.basico;

import negocio.comuns.basico.DepartamentoVO;
import negocio.comuns.utilitarias.Ordenacao;
import negocio.comuns.utilitarias.Uteis;
import negocio.facade.jdbc.arquitetura.SuperEntidade;
import negocio.interfaces.basico.DepartamentoInterfaceFacade;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.sql.Statement;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

public class Departamento extends SuperEntidade implements DepartamentoInterfaceFacade {

    public Departamento() throws Exception {
        super();
    }

    public Departamento(Connection conexao) throws Exception {
        super(conexao);
    }

    public static List<DepartamentoVO> montarDadosConsulta(ResultSet tabelaResultado, int nivelMontarDados) throws Exception {
        List<DepartamentoVO> vetResultado = new ArrayList<DepartamentoVO>();
        while (tabelaResultado.next()) {
            DepartamentoVO obj = montarDados(tabelaResultado, nivelMontarDados);
            vetResultado.add(obj);
        }
        return vetResultado;
    }

    public static DepartamentoVO montarDadosBasico(ResultSet dadosSQL) throws Exception {
        DepartamentoVO obj = new DepartamentoVO();
        obj.setNovoObj(false);
        obj.setCodigo(dadosSQL.getInt("codigo"));
        obj.setNome(dadosSQL.getString("nome"));
        obj.getEmpresaVO().setCodigo(dadosSQL.getInt("empresa"));
        obj.setConcessionario(dadosSQL.getBoolean("concessionario"));
        return obj;
    }

    public static DepartamentoVO montarDados(ResultSet dadosSQL, int nivelMontarDados) throws Exception {
        DepartamentoVO obj = montarDadosBasico(dadosSQL);

        if (nivelMontarDados == Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA) {
            return obj;
        }

        if (nivelMontarDados == Uteis.NIVELMONTARDADOS_DADOSBASICOS) {
            return obj;
        }
        return obj;
    }

    public DepartamentoVO novo() throws Exception {
        DepartamentoVO obj = new DepartamentoVO();
        incluir(getIdEntidade());
        return obj;
    }

    public void incluir(DepartamentoVO obj) throws Exception {
        DepartamentoVO.validarDados(obj);
        String sql = "INSERT INTO Departamento(nome, empresa, concessionario) VALUES (?, ?, ?)";
        PreparedStatement sqlInserir = con.prepareStatement(sql);
        int i = 0;
        sqlInserir.setString(++i, obj.getNome());
        sqlInserir.setInt(++i, obj.getEmpresaVO().getCodigo());
        sqlInserir.setBoolean(++i, obj.isConcessionario());
        sqlInserir.execute();
        obj.setCodigo(obterValorChavePrimariaCodigo());
        obj.setNovoObj(false);
    }

    public void alterar(DepartamentoVO obj) throws Exception {
            DepartamentoVO.validarDados(obj);
            String sql = "UPDATE Departamento set nome = ?, empresa = ?, concessionario = ? WHERE codigo = ?";
            PreparedStatement sqlAlterar = con.prepareStatement(sql);
            int i = 0;
            sqlAlterar.setString(++i, obj.getNome());
            sqlAlterar.setInt(++i, obj.getEmpresaVO().getCodigo());
            sqlAlterar.setBoolean(++i, obj.isConcessionario());
            sqlAlterar.setInt(++i, obj.getCodigo());
            sqlAlterar.execute();
        }

    public void excluir(DepartamentoVO obj) throws Exception {
        try {
            con.setAutoCommit(false);
            String sql = "DELETE FROM departamento WHERE codigo = ?";
            PreparedStatement sqlExcluir = con.prepareStatement(sql);
            sqlExcluir.setInt(1, obj.getCodigo());
            sqlExcluir.execute();
            con.commit();
        } catch (Exception e) {
            con.rollback();
            con.setAutoCommit(true);
            throw e;
        } finally {
            con.setAutoCommit(true);
        }
    }

    public DepartamentoVO consultarPorChavePrimaria(Integer codigoPrm, int nivelMontarDados) throws Exception {
        String sql = "SELECT * FROM departamento WHERE codigo = ?";
        try (PreparedStatement sqlConsultar = con.prepareStatement(sql)) {
            sqlConsultar.setInt(1, codigoPrm);
            try (ResultSet tabelaResultado = sqlConsultar.executeQuery()) {
                if (!tabelaResultado.next()) {
                    return null;
                }
                return (montarDados(tabelaResultado, nivelMontarDados));
            }
        }
    }

    public String consultarJSON() throws Exception {
        StringBuilder json;
        boolean dados;
        try (ResultSet rs = getPS().executeQuery()) {
            json = new StringBuilder();
            json.append("{\"aaData\":[");
            dados = false;
            while (rs.next()) {
                dados = true;
                json.append("[\"").append(rs.getString("codigo")).append("\",");
                json.append("\"").append(rs.getString("nome").trim()).append("\",");
                json.append("\"").append((rs.getBoolean("concessionario")) ? "Sim" : "Não").append("\",");
                json.append("\"").append(rs.getString("empresa")).append("\"],");
            }
        }
        if (dados) {
            json.deleteCharAt(json.toString().length() - 1);
        }
        json.append("]}");
        return json.toString();
    }

    private PreparedStatement getPS() throws SQLException {
        String sql = "SELECT dp.codigo, dp.nome, dp.concessionario, emp.nome as empresa\n" +
                "FROM departamento dp\n" +
                "LEFT JOIN empresa emp ON dp.empresa = emp.codigo\n" +
                "ORDER BY nome;";
        return con.prepareStatement(sql);
    }

    public List consultarParaImpressao(String filtro, String ordem, String campoOrdenacao, int i) throws SQLException {
        List lista;
        try (ResultSet rs = getPS().executeQuery()) {
            lista = new ArrayList();

            while (rs.next()) {
                DepartamentoVO dep = new DepartamentoVO();
                String geral = rs.getString("codigo") + rs.getString("nome") + rs.getString("empresa");
                if (geral.toLowerCase().contains(filtro.toLowerCase())) {
                    dep.setCodigo(rs.getInt("codigo"));
                    dep.setNome(rs.getString("nome"));
                    dep.getEmpresaVO().setNome(rs.getString("nome"));
                    dep.setConcessionario(rs.getBoolean("concessionario"));
                    lista.add(dep);
                }
            }
        }
        if (campoOrdenacao.equals("Código")) {
            Ordenacao.ordenarLista(lista, "codigo");
        } else if (campoOrdenacao.equals("Nome")) {
            Ordenacao.ordenarLista(lista, "nome");
        }
        if (ordem.contains("desc")) {
            Collections.reverse(lista);
        }
        return lista;
    }

    public List<DepartamentoVO> consultarTodos(int nivelMontarDados) throws Exception {
        try (Statement st = con.createStatement()) {
            try (ResultSet rs = st.executeQuery("select * from departamento ")) {
                return montarDadosConsulta(rs, nivelMontarDados);
            }
        }

    }

    public List<DepartamentoVO> consultarPorNome(String valorConsulta, int empresa, boolean controlarAcesso, int nivelMontarDados) throws Exception {
        String sqlStr = "SELECT * FROM departamento\n" +
                "WHERE upper(nome) like('" + valorConsulta.toUpperCase() + "%')\n" +
                "AND empresa = " + empresa + "\n" +
                "ORDER BY nome";
        try (Statement stm = con.createStatement()) {
            try (ResultSet tabelaResultado = stm.executeQuery(sqlStr)) {
                return (montarDadosConsulta(tabelaResultado, nivelMontarDados));
            }
        }
    }
}

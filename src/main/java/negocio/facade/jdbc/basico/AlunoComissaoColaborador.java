/*
 * To change this template, choose Too<PERSON> | Templates
 * and open the template in the editor.
 */

package negocio.facade.jdbc.basico;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.util.ArrayList;
import java.util.Iterator;
import java.util.List;
import negocio.comuns.basico.AlunoComissaoColaboradorVO;
import negocio.comuns.basico.ColaboradorVO;
import negocio.comuns.basico.PessoaVO;
import negocio.comuns.utilitarias.Uteis;
import negocio.facade.jdbc.arquitetura.SuperEntidade;
import negocio.interfaces.basico.AlunoComissaoColaboradorInterfaceFacade;

/**
 * Classe responsável por persistir os dados de comissão por aluno de cada colaborador
 * <AUTHOR>
 */
public class AlunoComissaoColaborador extends SuperEntidade implements AlunoComissaoColaboradorInterfaceFacade {
    public AlunoComissaoColaborador() throws Exception {
        super();
    }

     public AlunoComissaoColaborador(Connection conexao) throws Exception {
        super(conexao);
    }


    /**
     * Operação responsável por incluir objetos da <code>ModalidadeComissaoColaboradorVO</code> no BD.
     * Garantindo o relacionamento com a entidade principal <code>colaborador.Colaborador</code> através do atributo de vínculo.
     * @param objetos List contendo os objetos a serem gravados no BD da classe.
     * @exception Exception  Erro de conexão com o BD ou restrição de acesso a esta operação.
     */
    public void incluirAlunosComissao(Integer colaboradorPrm, List<AlunoComissaoColaboradorVO> objetos) throws Exception {
        Iterator e = objetos.iterator();
        while (e.hasNext()) {
            AlunoComissaoColaboradorVO obj = (AlunoComissaoColaboradorVO) e.next();
            obj.getColaboradorVO().setCodigo(colaboradorPrm);
            incluir(obj);
        }

    }

    /**
     * Operação responsável por incluir no banco de dados um objeto da classe <code>AlunoComissaoColaboradorVO</code>.
     * Primeiramente valida os dados (<code>validarDados</code>) do objeto. Verifica a conexão com o banco de dados e a permissão do usuário
     * para realizar esta operacão na entidade.
     * Isto, através da operação <code>incluir</code> da superclasse.
     * @param obj  Objeto da classe <code>AlunoComissaoColaboradorVO</code> que será gravado no banco de dados.
     * @exception Exception Caso haja problemas de conexão, restrição de acesso ou validação de dados.
     */
    public void incluir(AlunoComissaoColaboradorVO obj) throws Exception {
        AlunoComissaoColaboradorVO.validarDados(obj);
        String sql = "INSERT INTO alunocomissaocolaborador(pessoa,colaborador, porcComissao,valorComissao) VALUES (?, ?, ?, ?)";
        PreparedStatement sqlInserir = con.prepareStatement(sql);
        sqlInserir.setInt(1, obj.getPessoaVO().getCodigo());
        sqlInserir.setInt(2, obj.getColaboradorVO().getCodigo());
        sqlInserir.setDouble(3, obj.getPorcComissao());
        sqlInserir.setDouble(4, obj.getValorComissao());
        sqlInserir.execute();
        obj.setNovoObj(false);
    }

    /**
     * Operação responsável por alterar no BD os dados de um objeto da classe <code>AlunoComissaoColaboradorVO</code>.
     * Sempre utiliza a chave primária da classe como atributo para localização do registro a ser alterado.
     * Primeiramente valida os dados (<code>validarDados</code>) do objeto. Verifica a conexão com o banco de dados e a permissão do usuário
     * para realizar esta operacão na entidade.
     * Isto, através da operação <code>alterar</code> da superclasse.
     * @param obj    Objeto da classe <code>AlunoComissaoColaboradorVO</code> que será alterada no banco de dados.
     * @exception Exception Caso haja problemas de conexão, restrição de acesso ou validação de dados.
     */
    public void alterar(AlunoComissaoColaboradorVO obj) throws Exception {
        AlunoComissaoColaboradorVO.validarDados(obj);
        alterar(getIdEntidade());
        String sql = "UPDATE alunocomissaocolaborador set pessoa=?, colaborador=?, porcComissao=?, valorComissao=? WHERE ((pessoa=? and colaborador=?))";
        PreparedStatement sqlAlterar = con.prepareStatement(sql);
        sqlAlterar.setInt(1, obj.getPessoaVO().getCodigo());
        sqlAlterar.setInt(2, obj.getColaboradorVO().getCodigo());
        sqlAlterar.setDouble(3, obj.getPorcComissao());
        sqlAlterar.setDouble(4, obj.getValorComissao());
        sqlAlterar.setInt(5, obj.getPessoaVO().getCodigo());
        sqlAlterar.setInt(6, obj.getColaboradorVO().getCodigo());
        sqlAlterar.execute();
    }

    /**
     * Operação responsável por excluir no BD um objeto da classe <code>AlunoComissaoColaboradorVO</code>.
     * Sempre localiza o registro a ser excluído através da chave primária da entidade.
     * Primeiramente verifica a conexão com o banco de dados e a permissão do usuário
     * para realizar esta operacão na entidade.
     * Isto, através da operação <code>excluir</code> da superclasse.
     * @param obj    Objeto da classe <code>AlunoComissaoColaboradorVO</code> que será removido no banco de dados.
     * @exception Exception Caso haja problemas de conexão ou restrição de acesso.
     */
    public void excluir(AlunoComissaoColaboradorVO obj) throws Exception {
        excluir(getIdEntidade());
        String sql = "DELETE FROM alunocomissaocolaborador WHERE ((pessoa=? and colaborador=?))";
        PreparedStatement sqlExcluir = con.prepareStatement(sql);
        sqlExcluir.setInt(1, obj.getPessoaVO().getCodigo());
        sqlExcluir.setInt(2, obj.getColaboradorVO().getCodigo());
        sqlExcluir.execute();
    }

    /**
     * Operação responsável por excluir todos os objetos da <code>AlunoComissaoColaboradorVO</code> no BD.
     * Faz uso da operação <code>excluir</code> disponível na classe <code>AlunoComissaoColaborador</code>.
     * @param colaborador campo chave para exclusão dos objetos no BD.
     * @exception Exception  Erro de conexão com o BD ou restrição de acesso a esta operação.
     */
    public void excluirAlunoComissao(Integer colaborador) throws Exception {
        String sql = "DELETE FROM alunocomissaocolaborador WHERE (colaborador = ?)";
        PreparedStatement sqlExcluir = con.prepareStatement(sql);
        sqlExcluir.setInt(1, colaborador);
        sqlExcluir.execute();
    }

    /**
     * Operação responsável por alterar todos os objetos da <code>AlunoComissaoColaboradorVO</code> contidos em um Hashtable no BD.
     * Faz uso da operação <code>excluirTipoColaboradors</code> e <code>incluirAlunosComissao</code> disponíveis na classe <code>AlunoComissaoColaborador</code>.
     * @param objetos  List com os objetos a serem alterados ou incluídos no BD.
     * @exception Exception  Erro de conexão com o BD ou restrição de acesso a esta operação.
     */
    public void alterarAlunoComissao(Integer colaborador, List<AlunoComissaoColaboradorVO> objetos) throws Exception {
        String str = "DELETE FROM  alunocomissaocolaborador  WHERE colaborador = " + colaborador;
        Iterator i = objetos.iterator();
      /*  while (i.hasNext()) {
            AlunoComissaoColaboradorVO objeto = (AlunoComissaoColaboradorVO) i.next();
            str += " AND codigo <> " + objeto.getCodigo().intValue();
        }*/
        PreparedStatement sqlExcluir = con.prepareStatement(str);
        sqlExcluir.execute();
        Iterator e = objetos.iterator();
        while (e.hasNext()) {
            AlunoComissaoColaboradorVO obj = (AlunoComissaoColaboradorVO) e.next();
           // if (obj.getCodigo().equals(new Integer(0))) {
                obj.getColaboradorVO().setCodigo(colaborador);
                incluir(obj);
            /*} else {
                alterar(obj);
            }*/
        }

    }

    /**
     * Operação responsável por consultar todos os <code>TurmaComissaoColaboradorVO</code> relacionados a um objeto da classe <code>colaborador.Colaborador</code>.
     * @param colaborador a ser utilizado para localizar os objetos da classe <code>TurmaComissaoColaboradorVO</code>.
     * @return List  Contendo todos os objetos da classe <code>TurmaComissaoColaboradorVO</code> resultantes da consulta.
     * @exception Exception  Erro de conexão com o BD ou restrição de acesso a esta operação.
     */
    public List<AlunoComissaoColaboradorVO> consultarAlunoComissao(Integer colaborador, int nivelMontarDados) throws Exception {
        consultar(getIdEntidade());
        List<AlunoComissaoColaboradorVO> objetos = new ArrayList<AlunoComissaoColaboradorVO>();
        String sql = "SELECT * FROM alunocomissaocolaborador WHERE colaborador = ?";
        PreparedStatement sqlConsulta = con.prepareStatement(sql);
        sqlConsulta.setInt(1, colaborador);
        ResultSet resultado = sqlConsulta.executeQuery();
        while (resultado.next()) {
            AlunoComissaoColaboradorVO novoObj = montarDados(resultado, nivelMontarDados, con);
            objetos.add(novoObj);
        }
        return objetos;
    }

     /**
     * Responsável por montar os dados de vários objetos, resultantes de uma consulta ao banco de dados (<code>ResultSet</code>).
     * Faz uso da operação <code>montarDados</code> que realiza o trabalho para um objeto por vez.
     * @return  List Contendo vários objetos da classe <code>AlunoComissaoColaboradorVO</code> resultantes da consulta.
     */
    public static List<AlunoComissaoColaboradorVO> montarDadosConsulta(ResultSet tabelaResultado, int nivelMontarDados, Connection con) throws Exception {
        List<AlunoComissaoColaboradorVO> vetResultado = new ArrayList<AlunoComissaoColaboradorVO>();
        while (tabelaResultado.next()) {
            AlunoComissaoColaboradorVO obj = montarDados(tabelaResultado, nivelMontarDados, con);
            vetResultado.add(obj);
        }
        return vetResultado;
    }

    /**
     * Responsável por montar os dados resultantes de uma consulta ao banco de dados (<code>ResultSet</code>)
     * em um objeto da classe <code>ModalidadeComissaoColaboradorVO</code>.
     * @return  O objeto da classe <code>ModalidadeComissaoColaboradorVO</code> com os dados devidamente montados.
     */
    public static AlunoComissaoColaboradorVO montarDados(ResultSet dadosSQL, int nivelMontarDados, Connection con) throws Exception {
        AlunoComissaoColaboradorVO obj = montarDadosBasicos(dadosSQL);
        if(nivelMontarDados == Uteis.NIVELMONTARDADOS_DADOSBASICOS){
            montarDadosAluno(obj, Uteis.NIVELMONTARDADOS_MINIMOS, con);
            return obj;
        }
        return obj;
    }

    public static AlunoComissaoColaboradorVO montarDadosBasicos(ResultSet dadosSQL) throws Exception {
        AlunoComissaoColaboradorVO obj = new AlunoComissaoColaboradorVO();
        obj.setPessoaVO(new PessoaVO());
        obj.getPessoaVO().setCodigo(dadosSQL.getInt("pessoa"));
        obj.setPorcComissao(dadosSQL.getDouble("porcComissao"));
        obj.setValorComissao(dadosSQL.getDouble("valorComissao"));
        obj.setColaboradorVO(new ColaboradorVO());
        obj.getColaboradorVO().setCodigo(dadosSQL.getInt("colaborador"));
        obj.setNovoObj(false);
        return obj;
    }

      /**
     * Operação responsável por montar os dados de um objeto da classe <code>ModalidadeComissaoColaboradorVO</code> relacionado ao objeto <code>ColaboradorVO</code>.
     * Faz uso da chave primária da classe <code>PessoaVO</code> para realizar a consulta.
     * @param obj  Objeto no qual será montado os dados consultados.
     */
    public static void montarDadosAluno(AlunoComissaoColaboradorVO obj, int nivelMontarDados, Connection con) throws Exception {
        if (obj.getPessoaVO().getCodigo() == 0) {
            obj.setPessoaVO(new PessoaVO());
            return;
        }
        Pessoa pessoa = new Pessoa(con);
        obj.setPessoaVO(pessoa.consultarPorChavePrimaria(obj.getPessoaVO().getCodigo(), nivelMontarDados));
        pessoa = null;
    }

}

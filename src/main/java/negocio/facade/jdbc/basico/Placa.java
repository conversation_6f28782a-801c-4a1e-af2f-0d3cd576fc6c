/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package negocio.facade.jdbc.basico;

import negocio.comuns.basico.PessoaVO;
import negocio.comuns.basico.PlacaVO;
import negocio.comuns.utilitarias.Uteis;
import negocio.facade.jdbc.arquitetura.SuperEntidade;
import negocio.interfaces.basico.PlacaInterfaceFacade;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.util.ArrayList;
import java.util.Iterator;
import java.util.List;

/**
 * <AUTHOR>
 */
public class Placa extends SuperEntidade implements PlacaInterfaceFacade {

    public Placa() throws Exception {
        super();
    }

    public Placa(Connection conexao) throws Exception {
        super(conexao);
    }

    public static List<PlacaVO> montarDadosConsulta(ResultSet tabelaResultado, 
            int nivelMontarDados, Connection con) throws Exception {
        List<PlacaVO> vetResultado = new ArrayList<PlacaVO>();
        while (tabelaResultado.next()) {
            PlacaVO obj = montarDados(tabelaResultado, nivelMontarDados, con);
            vetResultado.add(obj);
        }
        return vetResultado;
    }

    public static PlacaVO montarDadosBasico(ResultSet dadosSQL) throws Exception {
        PlacaVO obj = new PlacaVO();
        obj.setNovoObj(false);
        obj.setCodigo(dadosSQL.getInt("codigo"));
        obj.getPessoaVO().setCodigo(dadosSQL.getInt("pessoa"));
        obj.setPlaca(dadosSQL.getString("placa"));
        obj.setDescricao(dadosSQL.getString("descricao"));

        return obj;
    }

    public static PlacaVO montarDados(ResultSet dadosSQL, int nivelMontarDados, Connection con) throws Exception {
        PlacaVO obj = montarDadosBasico(dadosSQL);

        if (nivelMontarDados == Uteis.NIVELMONTARDADOS_DADOSBASICOS) {
            montarDadosPessoa(obj, Uteis.NIVELMONTARDADOS_MINIMOS, con);
            return obj;
        }

        return obj;
    }

    public static void montarDadosPessoa(PlacaVO obj, int nivelMontarDados, Connection con) throws Exception {
        if (obj.getPessoaVO().getCodigo() == 0) {
            obj.setPessoaVO(new PessoaVO());
            return;
        }

        Pessoa pessoa = new Pessoa(con);
        obj.setPessoaVO(pessoa.consultarPorChavePrimaria(obj.getPessoaVO().getCodigo(), nivelMontarDados));
        pessoa = null;
    }

    public void incluir(PlacaVO obj) throws Exception {
        String sql = "INSERT INTO placa(pessoa, placa, descricao) VALUES (?, ?, ?)";
        PreparedStatement sqlInserir = con.prepareStatement(sql);
        if (obj.getPessoaVO().getCodigo() != 0) {
            sqlInserir.setInt(1, obj.getPessoaVO().getCodigo());
        } else {
            sqlInserir.setNull(1, 0);
        }
        sqlInserir.setString(2, obj.getPlaca());
        sqlInserir.setString(3, obj.getDescricao());
        sqlInserir.execute();
        obj.setCodigo(obterValorChavePrimariaCodigo());
        obj.setNovoObj(false);
    }

    public void alterar(PlacaVO obj) throws Exception {
        String sql = "UPDATE placa set pessoa=?, placa=?, descricao=? WHERE codigo = ?";
        PreparedStatement sqlAlterar = con.prepareStatement(sql);
        if (obj.getPessoaVO().getCodigo() != 0) {
            sqlAlterar.setInt(1, obj.getPessoaVO().getCodigo());
        } else {
            sqlAlterar.setNull(1, 0);
        }
        sqlAlterar.setString(2, obj.getPlaca());
        sqlAlterar.setString(3, obj.getDescricao());
        sqlAlterar.setInt(4, obj.getCodigo());
        sqlAlterar.execute();
    }

    public void excluir(PlacaVO obj) throws Exception {
        excluir(getIdEntidade());
        String sql = "DELETE FROM placa WHERE codigo = ?";
        PreparedStatement sqlExcluir = con.prepareStatement(sql);
        sqlExcluir.setInt(1, obj.getCodigo());
        sqlExcluir.execute();
    }

    public void alterarPlacas(Integer pessoa, List objetos) throws Exception {
        String str = "DELETE FROM placa WHERE pessoa = " + pessoa;
        Iterator i = objetos.iterator();
        while (i.hasNext()) {
            PlacaVO objeto = (PlacaVO) i.next();
            str += " AND codigo <> " + objeto.getCodigo();
        }
        PreparedStatement sqlExcluir = con.prepareStatement(str);
        sqlExcluir.execute();
        Iterator e = objetos.iterator();
        while (e.hasNext()) {
            PlacaVO obj = (PlacaVO) e.next();
            if (obj.getCodigo().equals(new Integer(0))) {
                obj.getPessoaVO().setCodigo(pessoa);
                incluir(obj);
            } else {
                alterar(obj);
            }
        }
    }

    public List<PlacaVO> consultarPlacas(Integer pessoa, int nivelMontarDados) throws Exception {
        List<PlacaVO> objetos = new ArrayList<PlacaVO>();
        String sql = "SELECT * FROM placa WHERE pessoa = ?";
        PreparedStatement sqlConsulta = con.prepareStatement(sql);
        sqlConsulta.setInt(1, pessoa);
        ResultSet resultado = sqlConsulta.executeQuery();
        while (resultado.next()) {
            PlacaVO novoObj = montarDados(resultado, nivelMontarDados, con);
            objetos.add(novoObj);
        }
        return objetos;
    }

}
package negocio.facade.jdbc.basico;

import br.com.pactosolucoes.enumeradores.TipoColaboradorEnum;
import br.com.pactosolucoes.estrutura.paginacao.ConfPaginacao;
import br.com.pactosolucoes.oamd.controle.basico.DAO;
import negocio.comuns.CadastroDinamicoItemVO;
import negocio.comuns.acesso.AcessoColaboradorVO;
import negocio.comuns.acesso.enumerador.TipoAcessoEnum;
import negocio.comuns.arquitetura.UsuarioVO;
import negocio.comuns.basico.*;
import negocio.comuns.basico.enumerador.TiposMensagensEnum;
import negocio.comuns.basico.enumerador.negocio.comuns.basico.enumerador.cadastrodinamico.CadastroDinamicoColaboradorEnum;
import negocio.comuns.crm.GenericoTO;
import negocio.comuns.plano.ProdutoVO;
import negocio.comuns.utilitarias.Calendario;
import negocio.comuns.utilitarias.ConsistirException;
import negocio.comuns.utilitarias.Ordenacao;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisValidacao;
import negocio.facade.jdbc.arquitetura.SuperEntidade;
import negocio.facade.jdbc.plano.Produto;
import negocio.interfaces.basico.ColaboradorInterfaceFacade;
import negocio.interfaces.basico.PessoaInterfaceFacade;
import negocio.modulos.integracao.usuariomovel.UsuarioMovel;
import negocio.modulos.integracao.usuariomovel.UsuarioMovelVO;
import org.json.JSONArray;
import org.json.JSONObject;
import servicos.integracao.TreinoWSConsumer;
import servicos.operacoes.midias.MidiaService;
import servicos.operacoes.midias.commons.MidiaEntidadeEnum;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.sql.Statement;
import java.sql.Types;
import java.text.DecimalFormat;
import java.util.*;
import java.util.logging.Level;
import java.util.logging.Logger;

/**
 * Classe de persistência que encapsula todas as operações de manipulação dos
 * dados da classe
 * <code>ColaboradorVO</code>. Responsável por implementar operações como
 * incluir, alterar, excluir e consultar pertinentes a classe
 * <code>ColaboradorVO</code>. Encapsula toda a interação com o banco de dados.
 *
 * @see ColaboradorVO
 * @see SuperEntidade
 */
public class Colaborador extends SuperEntidade implements ColaboradorInterfaceFacade {

    private static final String sqlInsert = "INSERT INTO Colaborador( pessoa, situacao, codAcesso, "
            + "codAcessoAlternativo, funcionario, empresa, diavencimento, produtoDefault, porcComissao, "
            + "corAgendaProfissional, tokengoogle, usoCreditosPersonal, saldoCreditoPersonal, "
            + "emAtendimentoPersonal, tempoentreacessos, fotopersonal, cref, departamento, email_movidesk, "
            + "sincronizado_movidesk, bloquearAcessoSemCheckin, cargahoraria, codigoafiliadovitio, validadeCref,\n"
            + "porcComissaoIndicacaoEstudio, permitirAcessoRedeEmpresa,valorcomissao) "
            + "VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)";
    private static final String sqlUpdate = "UPDATE Colaborador set pessoa=?, situacao=?, "
            + " codAcesso=?, codAcessoAlternativo=?, funcionario=?, empresa=?, diavencimento=?, "
            + " produtoDefault=?, porcComissao=?, corAgendaProfissional=?, tokengoogle = ?, usoCreditosPersonal = ?,  "
            + " saldoCreditoPersonal=?, emAtendimentoPersonal=?, tempoentreacessos = ?, fotopersonal = ?, cref = ?, departamento= ?, "
            + " email_movidesk = ?, sincronizado_movidesk = ?, bloquearAcessoSemCheckin = ?, cargahoraria = ?,\n"
            + " codigoafiliadovitio = ?, validadeCref = ?, porcComissaoIndicacaoEstudio = ?, permitirAcessoRedeEmpresa = ?, valorcomissao=? \n"
            + " WHERE codigo = ?";

    private static final String sqlDelete = "DELETE FROM Colaborador WHERE codigo = ?";

    public Colaborador() throws Exception {
        super();
    }

    public Colaborador(Connection conexao) throws Exception {
        super(conexao);
    }

    /**
     * Operação responsável por retornar um novo objeto da classe
     * <code>ColaboradorVO</code>.
     */
    public ColaboradorVO novo() throws Exception {
        incluir(getIdEntidade());
        return new ColaboradorVO();
    }

    private PreparedStatement getRS(Integer empresa, Integer offset, Integer limit, Integer colOrdenar, final String dirOrdenar,
                            final String situacao, final String clausulaLike, final String tipoColaborador, Boolean exportar) throws SQLException {
        StringBuilder sql = new StringBuilder("SELECT DISTINCT \n"
                + "   col.codigo, p.nome, p.datanasc, col.situacao, de.nome as departamento , emp.nome AS empresa,  \n"
                + "  (SELECT array(SELECT descricao FROM tipocolaborador tc  WHERE tc.colaborador = col.codigo)) AS tipo\n"
                + "FROM colaborador col INNER JOIN pessoa p ON col.pessoa = p.codigo LEFT JOIN empresa emp ON col.empresa = emp.codigo\n"
                + "left join departamento de on col.departamento = de.codigo\n"
                + "left join tipocolaborador tc on tc.colaborador = col.codigo"
        );
        sql.append(" WHERE 1 = 1\n");
        if (empresa != 0) {
            sql.append(" AND col.empresa = ?");
        }
        if (!situacao.equals("TD")) {
            sql.append(" AND col.situacao = '").append(situacao).append("' \n");
        }
        if (!tipoColaborador.equals("TD")) {
            sql.append(" AND tc.descricao = '").append(tipoColaborador).append("' \n");
        }


        if (!UteisValidacao.emptyString(clausulaLike)) {
            sql.append(" AND (");
            sql.append("lower(col.codigo::VARCHAR) ~ '").append(clausulaLike).append("' OR\n");
            sql.append("lower(p.nome) ~ '").append(clausulaLike).append("' OR\n");
            sql.append("lower(col.situacao) ~ '").append(clausulaLike).append("' OR\n");
            sql.append("lower((SELECT array(SELECT descricao FROM tipocolaborador tc  WHERE tc.colaborador = col.codigo))::text) ~ '").append(clausulaLike).append("' OR\n");
            sql.append("lower(emp.nome) ~ '").append(clausulaLike).append("'\n");
            sql.append(")");
        }

        if (!exportar){
            sql.append("  ORDER BY ").append(colOrdenar + 1).append(" ").append(dirOrdenar).append("\n");
            if (limit > 0) {
                sql.append(" LIMIT ").append(limit).append("\n");
            }
            sql.append(" OFFSET ").append(offset).append("\n");

        }

        PreparedStatement sqlConsultar = con.prepareStatement(sql.toString());
        if (empresa != 0) {
            sqlConsultar.setInt(1, empresa);
        }
        return sqlConsultar;
    }

    private PreparedStatement prepararIncluir(ColaboradorVO obj) throws Exception {
        return prepararIncluir(obj, true);
    }

    private PreparedStatement prepararIncluir(ColaboradorVO obj, boolean validarPermissao) throws Exception {
        validarCodAcessoAlternativo(obj);
        try (PreparedStatement sqlInserir = con.prepareStatement(sqlInsert)) {
            if (obj.getPessoa().getCodigo() != 0) {
                sqlInserir.setInt(1, obj.getPessoa().getCodigo());
            } else {
                sqlInserir.setNull(1, 0);
            }
            sqlInserir.setString(2, obj.getSituacao());
            //sqlInserir.setString(3, obj.getCodAcesso());
            sqlInserir.setString(3, gerarCodigoAcesso(obj, 0));
            sqlInserir.setString(4, obj.getCodAcessoAlternativo());
            sqlInserir.setBoolean(5, obj.isFuncionario());
            sqlInserir.setInt(6, obj.getEmpresa().getCodigo());
            sqlInserir.setInt(7, obj.getDiaVencimento());
            if (obj.getProdutoDefault().getCodigo() == 0) {
                sqlInserir.setNull(8, 0);
            } else {
                sqlInserir.setInt(8, obj.getProdutoDefault().getCodigo());
            }
            sqlInserir.setDouble(9, obj.getPorcComissao());
            sqlInserir.setString(10, obj.getCorAgendaProfissional().isEmpty() ? "#000000" : obj.getCorAgendaProfissional());
            sqlInserir.setString(11, obj.getTokenGoogle());

            resolveIntegerNull(sqlInserir, 12, obj.getUsoCreditosPersonal());
            resolveIntegerNull(sqlInserir, 13, obj.getSaldoCreditoPersonal());
            sqlInserir.setBoolean(14, obj.isEmAtendimentoPersonal());
            if (obj.isConfigurarTempoEntreAcessos()) {
                sqlInserir.setInt(15, obj.getTempoEntreAcessos());
            } else {
                sqlInserir.setInt(15, -1);
            }
            sqlInserir.setBytes(16, obj.getFotoPersonal());
            sqlInserir.setString(17, obj.getCref());
            if (obj.getDepartamentoVO() == null || obj.getDepartamentoVO().getCodigo() == 0) {
                sqlInserir.setNull(18, Types.NULL);
            } else {
                sqlInserir.setInt(18, obj.getDepartamentoVO().getCodigo());
            }
            sqlInserir.setString(19, obj.getEmailMovidesk());
            sqlInserir.setBoolean(20, obj.getSincronizadoMovidesk());
            sqlInserir.setBoolean(21, obj.getBloquearAcessoSemCheckin());
            sqlInserir.setInt(22, obj.getCargaHoraria());
            sqlInserir.setString(23, obj.getCodigoAfiliadoVitio());
            sqlInserir.setDate(24, Uteis.getDataJDBC(obj.getValidadeCref()));
            sqlInserir.setDouble(25, obj.getPorcComissaoIndicacaoEstudio());
            sqlInserir.setBoolean(26, obj.isPermitirAcessoRedeEmpresa());
            sqlInserir.setDouble(27, obj.getValorComissao());
            sqlInserir.execute();
            obj.setCodigo(obterValorChavePrimariaCodigo());
            obj.setNovoObj(false);

            TipoColaborador tipoColaborador = new TipoColaborador(con);
            tipoColaborador.incluirTipoColaboradors(obj.getCodigo(), obj.getListaTipoColaboradorVOs(), validarPermissao);
            tipoColaborador = null;

            ModalidadeComissaoColaborador modalidadeComissaoColaborador = new ModalidadeComissaoColaborador(con);
            modalidadeComissaoColaborador.incluirModalidadesComissao(obj.getCodigo(), obj.getListaModalidadesComissaoColaboradorVO());
            modalidadeComissaoColaborador = null;

            TurmaComissaoColaborador turmaComissaoColaborador = new TurmaComissaoColaborador(con);
            turmaComissaoColaborador.incluirTurmasComissao(obj.getCodigo(), obj.getListaTurmaComissaoColaboradorVO());
            turmaComissaoColaborador = null;

            AlunoComissaoColaborador alunoComissaoColaborador = new AlunoComissaoColaborador(con);
            alunoComissaoColaborador.incluirAlunosComissao(obj.getCodigo(), obj.getListaAlunoComissaoColaboradorVOs());
            alunoComissaoColaborador = null;

            ColaboradorModalidade colaboradorModalidadeDao = new ColaboradorModalidade(con);
            colaboradorModalidadeDao.incluirColaboradorModalidades(obj, obj.getColaboradorModalidadeVOS());
            colaboradorModalidadeDao = null;

            if (!obj.getListaColaboradorIndisponivelCrmVOS().isEmpty()) {
                ColaboradorIndisponivelCrm colaboradorIndisponivelCrm = new ColaboradorIndisponivelCrm(con);
                colaboradorIndisponivelCrm.incluirColaboradorIndisponivelCrm(obj.getListaColaboradorIndisponivelCrmVOS());
                colaboradorIndisponivelCrm = null;
            }
            if (obj.getColaboradorInfoRhVO().informouAlgumCampo()){
                new ColaboradorInfoRh(con).incluir(obj);
            }
            incluirColaboradorDocumentoRh(obj);
            return sqlInserir;
        }
    }

    private void incluirColaboradorDocumentoRh(ColaboradorVO colaboradorVO)throws Exception{
        if (colaboradorVO.getListaDocumentoRh() != null){
            for (ColaboradorDocumentoRhVO colaboradorDocumentoRhVO:colaboradorVO.getListaDocumentoRh()){
                if ((colaboradorDocumentoRhVO.getCodigo() == null) || (colaboradorDocumentoRhVO.getCodigo() == 0)){
                    String key = DAO.resolveKeyFromConnection(con);
                    String chaveArquivo = MidiaService.getInstance().uploadObjectWithExtension(key,
                            MidiaEntidadeEnum.ANEXO_ARQUIVOS_RH,
                            colaboradorDocumentoRhVO.getIdentificadorAnexo().toString(),
                            colaboradorDocumentoRhVO.getArquivoDocumentoRh(),
                            colaboradorDocumentoRhVO.getExtensaoAnexo());

                    colaboradorDocumentoRhVO.setAnexo(chaveArquivo);
                    new ColaboradorDocumentoRh(con).incluir(colaboradorDocumentoRhVO);
                }
            }
        }
    }

    private PreparedStatement prepararAlterar(ColaboradorVO obj) throws Exception {
        validarCodAcessoAlternativo(obj);
        PreparedStatement sqlAlterar = con.prepareStatement(sqlUpdate);
        if (obj.getPessoa().getCodigo() != 0) {
            sqlAlterar.setInt(1, obj.getPessoa().getCodigo());
        } else {
            sqlAlterar.setNull(1, 0);
        }
        sqlAlterar.setString(2, obj.getSituacao());

        sqlAlterar.setString(3, gerarCodigoAcesso(obj, 0));
        sqlAlterar.setString(4, obj.getCodAcessoAlternativo());
        sqlAlterar.setBoolean(5, obj.isFuncionario());
        sqlAlterar.setInt(6, obj.getEmpresa().getCodigo());
        sqlAlterar.setInt(7, obj.getDiaVencimento());
        if (obj.getProdutoDefault().getCodigo() == 0) {
            sqlAlterar.setNull(8, 0);
        } else {
            sqlAlterar.setInt(8, obj.getProdutoDefault().getCodigo());
        }
        sqlAlterar.setDouble(9, obj.getPorcComissao());
        sqlAlterar.setString(10, (obj.getCorAgendaProfissional() != null && obj.getCorAgendaProfissional().isEmpty()) ? "#000000" : obj.getCorAgendaProfissional());
        sqlAlterar.setString(11, obj.getTokenGoogle());

        resolveIntegerNull(sqlAlterar, 12, obj.getUsoCreditosPersonal());
        resolveIntegerNull(sqlAlterar, 13, obj.getSaldoCreditoPersonal());
        sqlAlterar.setBoolean(14, obj.isEmAtendimentoPersonal());
        if (obj.isConfigurarTempoEntreAcessos()) {
            sqlAlterar.setInt(15, obj.getTempoEntreAcessos());
        } else {
            sqlAlterar.setInt(15, -1);
        }
        sqlAlterar.setBytes(16, obj.getFotoPersonal());
        sqlAlterar.setString(17, obj.getCref());
        if (obj.getDepartamentoVO() == null || obj.getDepartamentoVO().getCodigo() == 0) {
            sqlAlterar.setNull(18, Types.NULL);
        } else  {
            sqlAlterar.setInt(18, obj.getDepartamentoVO().getCodigo());
        }
        sqlAlterar.setString(19, obj.getEmailMovidesk());
        sqlAlterar.setBoolean(20, obj.getSincronizadoMovidesk());
        sqlAlterar.setBoolean(21, obj.getBloquearAcessoSemCheckin());
        if (obj.getCargaHoraria() != 0) {
            sqlAlterar.setInt(22, obj.getCargaHoraria());
        } else {
            sqlAlterar.setNull(22, 0);
        }
        sqlAlterar.setString(23, obj.getCodigoAfiliadoVitio());
        sqlAlterar.setDate(24, Uteis.getDataJDBC(obj.getValidadeCref()));
        sqlAlterar.setDouble(25, obj.getPorcComissaoIndicacaoEstudio());
        sqlAlterar.setBoolean(26, obj.isPermitirAcessoRedeEmpresa());
        sqlAlterar.setDouble(27, obj.getValorComissao());
        sqlAlterar.setInt(28, obj.getCodigo());
        sqlAlterar.execute();
        getFacade().getTipoColaborador().alterarTipoColaboradors(obj.getCodigo(), obj.getListaTipoColaboradorVOs());
        getFacade().getModalidadeComissaoColaborador().alterarModalidadeComissao(obj.getCodigo(), obj.getListaModalidadesComissaoColaboradorVO());
        getFacade().getTurmaComissaoColaborador().alterarTurmaComissao(obj.getCodigo(), obj.getListaTurmaComissaoColaboradorVO());
        getFacade().getAlunoComissaoColaborador().alterarAlunoComissao(obj.getCodigo(), obj.getListaAlunoComissaoColaboradorVOs());
        getFacade().getColaboradorModalidade().alterarPorColaborador(obj.getCodigo(), obj.getColaboradorModalidadeVOS());
        if (!obj.getListaColaboradorIndisponivelCrmVOS().isEmpty()){
            getFacade().getColaboradorIndisponivelCrm().incluirColaboradorIndisponivelCrmSemCommit(obj.getListaColaboradorIndisponivelCrmVOS());
        }

        UsuarioVO usuario = getFacade().getUsuario().consultarPorCodigoColaborador(obj.getCodigo(), Uteis.NIVELMONTARDADOS_MINIMOS);
        if (usuario.getCodigo() != 0) {
            usuario.setNome(obj.getPessoa().getNome());
            getFacade().getUsuario().alterarNomeUsuario(usuario);
        }
        return sqlAlterar;
    }

    public String gerarCodigoAcesso(ColaboradorVO obj, Integer somaDv) {
        Integer cod = obj.getCodigo();
        if (cod == 0) {
            try {
                Colaborador colaborador = new Colaborador(con);
                cod = colaborador.obterValorChavePrimariaCodigo() + 1;
                colaborador = null;
            } catch (Exception ex) {
                Logger.getLogger(ClienteVO.class.getName()).log(Level.SEVERE, null, ex);
            }
        }
        DecimalFormat idFormat = new DecimalFormat("00000");
        String numero = TipoAcessoEnum.TA_COLABORADOR.getId() + idFormat.format(cod) + "00" + "0";

        Integer dv = Uteis.gerarDV(numero, somaDv);

        return numero + dv;
    }

    private void prepararExcluir(ColaboradorVO obj, boolean excluirPessoa) throws Exception {
        try (PreparedStatement sqlExcluir = con.prepareStatement(sqlDelete)) {
            if (obj.getListaDocumentoRh() != null){
                for (ColaboradorDocumentoRhVO colaboradorDocumentoRhVO: obj.getListaDocumentoRh()){
                    getFacade().getColaboradorDocumentoRh().excluir(colaboradorDocumentoRhVO.getCodigo());
                }
            }
            if ((obj.getColaboradorInfoRhVO() != null) && (obj.getColaboradorInfoRhVO().getCodigo() != null)){
                getFacade().getColaboradorInfoRh().excluir(obj.getColaboradorInfoRhVO().getCodigo());
            }
            sqlExcluir.setInt(1, obj.getCodigo());
            sqlExcluir.execute();
            if (excluirPessoa) {
                PessoaInterfaceFacade pessoaFacade = getFacade().getPessoa();
                pessoaFacade.setIdEntidade(this.getIdEntidade());
                pessoaFacade.excluirConexaoInicializada(obj.getPessoa(), con);
            }
            getFacade().getTipoColaborador().excluirTipoColaborador(obj.getCodigo());
            getFacade().getModalidadeComissaoColaborador().excluirModalidadeComissao(obj.getCodigo());
            getFacade().getTurmaComissaoColaborador().excluirTurmaComissao(obj.getCodigo());
            getFacade().getAlunoComissaoColaborador().excluirAlunoComissao(obj.getCodigo());
            executarConsultaUpdate("DELETE FROM acessocolaborador WHERE colaborador = " + obj.getCodigo(), con);


        }

    }

    private void validarCamposDinamicosObrigatorios(ColaboradorVO colaboradorVO)throws  Exception{
        if (!colaboradorVO.isValidarCamposDinamico()){
            return;
        }
        List<CadastroDinamicoItemVO> lista = getFacade().getCadastroDinamicoItem().consultar("colaborador", Uteis.NIVELMONTARDADOS_DADOSBASICOS);
        ConfiguracaoSistemaVO configuracaoSistemaVO = getFacade().getConfiguracaoSistema().consultarConfigs(Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA);
        for (CadastroDinamicoItemVO cadastroDinamicoItemVO: lista){
            if (cadastroDinamicoItemVO.isCampoObrigatorio()){
                if (cadastroDinamicoItemVO.getNomeCampo().equals(CadastroDinamicoColaboradorEnum.PROFISSAO.toString())){
                    if ((colaboradorVO.getPessoa().getProfissao().getCodigo() == null) || (colaboradorVO.getPessoa().getProfissao().getCodigo() <= 0)){
                        throw new ConsistirException("O campo Profissão (Aba - Dados Pessoais) deve ser informado.");
                    }
                }else if (cadastroDinamicoItemVO.getNomeCampo().equals(CadastroDinamicoColaboradorEnum.NOMEPAI.toString())){
                    if ((colaboradorVO.getPessoa().getNomePai() == null) || (colaboradorVO.getPessoa().getNomePai().trim().equals(""))){
                        throw new ConsistirException("O campo Nome do Pai (Aba - Dados Pessoais) deve ser informado.");
                    }
                }else if (cadastroDinamicoItemVO.getNomeCampo().equals(CadastroDinamicoColaboradorEnum.NOMEMAE.toString())){
                    if ((colaboradorVO.getPessoa().getNomeMae() == null) || (colaboradorVO.getPessoa().getNomeMae().trim().equals(""))){
                        throw new ConsistirException("O campo Nome da Mãe ou Responsável (Aba - Dados Pessoais) deve ser informado.");
                    }
                }else if (cadastroDinamicoItemVO.getNomeCampo().equals(CadastroDinamicoColaboradorEnum.CPF.toString())){
                    if (colaboradorVO.getPessoa().getPessoaFisica() && (colaboradorVO.getPessoa().getCfp() == null || colaboradorVO.getPessoa().getCfp().trim().equals(""))){
                        throw new ConsistirException("O campo CPF (Aba - Dados Pessoais) deve ser informado.");
                    } else if (colaboradorVO.getPessoa().getPessoaJuridica() && (colaboradorVO.getPessoa().getCnpj() == null || colaboradorVO.getPessoa().getCnpj().trim().equals(""))){
                        throw new ConsistirException("O campo CNPJ (Aba - Dados Pessoais) deve ser informado.");
                    }
                }else if (cadastroDinamicoItemVO.getNomeCampo().equals(CadastroDinamicoColaboradorEnum.RG.toString())){
                    if (colaboradorVO.getPessoa().getPessoaFisica() && (colaboradorVO.getPessoa().getRg() == null || colaboradorVO.getPessoa().getRg().trim().equals(""))){
                        throw new ConsistirException("O campo RG (Aba - Dados Pessoais) deve ser informado.");
                    }
                }else if (!configuracaoSistemaVO.isUsarSistemaInternacional() && cadastroDinamicoItemVO.getNomeCampo().equals(CadastroDinamicoColaboradorEnum.ORGAOEMISSOR.toString())){
                    if (colaboradorVO.getPessoa().getPessoaFisica() && (colaboradorVO.getPessoa().getRgOrgao() == null || colaboradorVO.getPessoa().getRgOrgao().trim().equals(""))){
                        throw new ConsistirException("O campo Órgão Emissor (Aba - Dados Pessoais) deve ser informado.");
                    }
                }else if (!configuracaoSistemaVO.isUsarSistemaInternacional() && cadastroDinamicoItemVO.getNomeCampo().equals(CadastroDinamicoColaboradorEnum.ESTADOEMISSAO.toString())){
                    if (colaboradorVO.getPessoa().getPessoaFisica() && (colaboradorVO.getPessoa().getRgUf() == null || colaboradorVO.getPessoa().getRgUf().trim().equals(""))){
                        throw new ConsistirException("O campo Estado de Emissão (Aba - Dados Pessoais) deve ser informado.");
                    }
                }else if (cadastroDinamicoItemVO.getNomeCampo().equals(CadastroDinamicoColaboradorEnum.PAIS.toString())){
                    if ((colaboradorVO.getPessoa().getPais().getCodigo() == null) || (colaboradorVO.getPessoa().getPais().getCodigo() <= 0)){
                        throw new ConsistirException("O campo País (Aba - Dados Pessoais) deve ser informado.");
                    }
                }else if (cadastroDinamicoItemVO.getNomeCampo().equals(CadastroDinamicoColaboradorEnum.ESTADO.toString())){
                    if ((colaboradorVO.getPessoa().getEstadoVO().getCodigo() == null) || (colaboradorVO.getPessoa().getEstadoVO().getCodigo() <= 0)){
                        throw new ConsistirException("O campo Estado (Aba - Dados Pessoais) deve ser informado.");
                    }
                }else if (cadastroDinamicoItemVO.getNomeCampo().equals(CadastroDinamicoColaboradorEnum.CIDADE.toString())){
                    if ((colaboradorVO.getPessoa().getCidade().getCodigo() == null) || (colaboradorVO.getPessoa().getCidade().getCodigo() <= 0)){
                        throw new ConsistirException("O campo Cidade (Aba - Dados Pessoais) deve ser informado.");
                    }
                }else if (cadastroDinamicoItemVO.getNomeCampo().equals(CadastroDinamicoColaboradorEnum.ESTADOCIVIL.toString())){
                    if ((colaboradorVO.getPessoa().getEstadoCivil() == null) || (colaboradorVO.getPessoa().getEstadoCivil().trim().equals(""))){
                        throw new ConsistirException("O campo Estado Civil (Aba - Dados Pessoais) deve ser informado.");
                    }
                }else if (cadastroDinamicoItemVO.getNomeCampo().equals(CadastroDinamicoColaboradorEnum.NACIONALIDADE.toString())){
                    if ((colaboradorVO.getPessoa().getNacionalidade() == null) || (colaboradorVO.getPessoa().getNacionalidade().trim().equals(""))){
                        throw new ConsistirException("O campo Nacionalidade (Aba - Dados Pessoais) deve ser informado.");
                    }
                }else if (cadastroDinamicoItemVO.getNomeCampo().equals(CadastroDinamicoColaboradorEnum.NATURALIDADE.toString())){
                    if ((colaboradorVO.getPessoa().getNaturalidade() == null) || (colaboradorVO.getPessoa().getNaturalidade().trim().equals(""))){
                        throw new ConsistirException("O campo Naturalidade (Aba - Dados Pessoais) deve ser informado.");
                    }
                }else if (cadastroDinamicoItemVO.getNomeCampo().equals(CadastroDinamicoColaboradorEnum.SEXO.toString())){
                    if ((colaboradorVO.getPessoa().getSexo() == null) || (colaboradorVO.getPessoa().getSexo().trim().equals(""))){
                        throw new ConsistirException("O campo Sexo (Aba - Dados Pessoais) deve ser informado.");
                    }
                }else if (cadastroDinamicoItemVO.getNomeCampo().equals(CadastroDinamicoColaboradorEnum.WEBPAGE.toString())){
                    if ((colaboradorVO.getPessoa().getWebPage() == null) || (colaboradorVO.getPessoa().getWebPage().trim().equals(""))){
                        throw new ConsistirException("O campo Web Page (Aba - Dados Pessoais) deve ser informado.");
                    }
                }else if (cadastroDinamicoItemVO.getNomeCampo().equals(CadastroDinamicoColaboradorEnum.CREF.toString())){
                    if ((colaboradorVO.getCref() == null) || (colaboradorVO.getCref().trim().equals(""))){
                        throw new ConsistirException("O campo CREF (Aba - Dados do Colaborador) deve ser informado.");
                    }
                }
                else if (cadastroDinamicoItemVO.getNomeCampo().equals(CadastroDinamicoColaboradorEnum.CONTATOEMERGENCIA.toString())){
                    if ((colaboradorVO.getPessoa().getContatoEmergencia() == null) || (colaboradorVO.getPessoa().getContatoEmergencia().trim().equals(""))) {
                        throw new ConsistirException("O campo Contato Emergência (Aba - Dados do Colaborador) deve ser informado.");
                    }
                }else if (cadastroDinamicoItemVO.getNomeCampo().equals(CadastroDinamicoColaboradorEnum.TELEFONEEMERGENCIA.toString())){
                    if ((colaboradorVO.getPessoa().getTelefoneEmergencia() == null) || (colaboradorVO.getPessoa().getTelefoneEmergencia().trim().equals(""))) {
                        throw new ConsistirException("O campo Telefone Emergência (Aba - Dados do Colaborador) deve ser informado.");
                    }
                }else if (cadastroDinamicoItemVO.getNomeCampo().equals(CadastroDinamicoColaboradorEnum.PRODUTODEFAULTPERSONAL.toString())){
                    if ((colaboradorVO.getProdutoDefault().getCodigo() == null) || (colaboradorVO.getProdutoDefault().getCodigo() <= 0)){
                        throw new ConsistirException("O campo Produto Default do Personal trainer (Aba - Dados do Colaborador) deve ser informado.");
                    }
                }else if (cadastroDinamicoItemVO.getNomeCampo().equals(CadastroDinamicoColaboradorEnum.PORCENTAGEMCOMISSAO.toString())){
                    if ((colaboradorVO.getPorcComissao() == null) || (colaboradorVO.getPorcComissao() <= 0)){
                        throw new ConsistirException("O campo Porcentagem Comissão (%) (Aba - Dados do Colaborador) deve ser informado.");
                    }
                }else if (cadastroDinamicoItemVO.getNomeCampo().equals(CadastroDinamicoColaboradorEnum.VALORFIXOCOMISSAO.toString())){
                    if ((colaboradorVO.getValorComissao() == null) || (colaboradorVO.getValorComissao() <= 0)){
                        throw new ConsistirException("O campo Valor fixo Comissão (R$) (Aba - Dados do Colaborador) deve ser informado.");
                    }
                }else if (cadastroDinamicoItemVO.getNomeCampo().equals(CadastroDinamicoColaboradorEnum.AUTENTICARGOOGLE.toString())){
                    if ((colaboradorVO.getTokenGoogle() == null) || (colaboradorVO.getTokenGoogle().trim().equals(""))){
                        throw new ConsistirException("O campo Autenticar Google (Aba - Dados do Colaborador) deve ser informado.");
                    }
                }else if (cadastroDinamicoItemVO.getNomeCampo().equals(CadastroDinamicoColaboradorEnum.CEP.toString())){
                    if ((colaboradorVO.getPessoa().getEnderecoVOs() == null) || (colaboradorVO.getPessoa().getEnderecoVOs().isEmpty())) {
                        throw new ConsistirException("O campo Cep (Aba - Endereço) deve ser informado.");
                    }
                    for (EnderecoVO enderecoVO: colaboradorVO.getPessoa().getEnderecoVOs()){
                        if ((enderecoVO.getCep() == null) || (enderecoVO.getCep().trim().equals(""))){
                            throw new ConsistirException("O campo Cep (Aba - Endereço) deve ser informado.");
                        }
                    }

                }else if (cadastroDinamicoItemVO.getNomeCampo().equals(CadastroDinamicoColaboradorEnum.ENDERECO.toString())){
                    if ((colaboradorVO.getPessoa().getEnderecoVOs() == null) || (colaboradorVO.getPessoa().getEnderecoVOs().isEmpty())){
                        throw new ConsistirException("O campo Endereço (Aba - Endereço) deve ser informado.");
                    }
                    for (EnderecoVO enderecoVO: colaboradorVO.getPessoa().getEnderecoVOs()){
                        if ((enderecoVO.getEndereco() == null) || (enderecoVO.getEndereco().trim().equals(""))){
                            throw new ConsistirException("O campo Endereço (Aba - Endereço) deve ser informado.");
                        }
                    }
                }else if (cadastroDinamicoItemVO.getNomeCampo().equals(CadastroDinamicoColaboradorEnum.COMPLEMENTO.toString())){
                    if ((colaboradorVO.getPessoa().getEnderecoVOs() == null) || (colaboradorVO.getPessoa().getEnderecoVOs().isEmpty())){

                        throw new ConsistirException("O campo Complemento (Aba - Endereço) deve ser informado.");
                    }
                    for (EnderecoVO enderecoVO: colaboradorVO.getPessoa().getEnderecoVOs()){
                        if ((enderecoVO.getComplemento() == null) || (enderecoVO.getComplemento().trim().equals(""))){
                            throw new ConsistirException("O campo Complemento (Aba - Endereço) deve ser informado.");
                        }
                    }

                }else if (cadastroDinamicoItemVO.getNomeCampo().equals(CadastroDinamicoColaboradorEnum.NUMERO.toString())){
                    if ((colaboradorVO.getPessoa().getEnderecoVOs() == null) || (colaboradorVO.getPessoa().getEnderecoVOs().isEmpty())){
                        throw new ConsistirException("O campo Número (Aba - Endereço) deve ser informado.");
                    }
                    for (EnderecoVO enderecoVO: colaboradorVO.getPessoa().getEnderecoVOs()){
                        if ((enderecoVO.getNumero() == null) || (enderecoVO.getNumero().trim().equals(""))){
                            throw new ConsistirException("O campo Número (Aba - Endereço) deve ser informado.");
                        }
                    }
                }else if (cadastroDinamicoItemVO.getNomeCampo().equals(CadastroDinamicoColaboradorEnum.BAIRRO.toString())){
                    if ((colaboradorVO.getPessoa().getEnderecoVOs() == null) || (colaboradorVO.getPessoa().getEnderecoVOs().isEmpty()) ){
                        throw new ConsistirException("O campo Bairro (Aba - Endereço) deve ser informado.");
                    }
                    for (EnderecoVO enderecoVO: colaboradorVO.getPessoa().getEnderecoVOs()){
                        if ((enderecoVO.getBairro() == null) || (enderecoVO.getBairro().trim().equals(""))){
                            throw new ConsistirException("O campo Bairro (Aba - Endereço) deve ser informado.");
                        }
                    }
                }else if (cadastroDinamicoItemVO.getNomeCampo().equals(CadastroDinamicoColaboradorEnum.ENDERECOCORRESPONDENCIA.toString())){
                    if ((colaboradorVO.getPessoa().getEnderecoVOs() == null) || (colaboradorVO.getPessoa().getEnderecoVOs().isEmpty()) ){
                        throw new ConsistirException("O campo Endereço para Correspondência (Aba - Endereço) deve ser informado.");
                    }
                    for (EnderecoVO enderecoVO: colaboradorVO.getPessoa().getEnderecoVOs()){
                        if (enderecoVO.getEnderecoCorrespondencia() == null)  {
                            throw new ConsistirException("O campo Endereço para Correspondência (Aba - Endereço) deve ser informado.");
                        }
                    }
                }else if (cadastroDinamicoItemVO.getNomeCampo().equals(CadastroDinamicoColaboradorEnum.TIPOENDERECO.toString())){
                    if ((colaboradorVO.getPessoa().getEnderecoVOs() == null) || (colaboradorVO.getPessoa().getEnderecoVOs().isEmpty())){
                        throw new ConsistirException("O campo Tipo de Endereço (Aba - Endereço) deve ser informado.");
                    }
                    for (EnderecoVO enderecoVO: colaboradorVO.getPessoa().getEnderecoVOs()){
                        if ((enderecoVO.getTipoEndereco() == null) || (enderecoVO.getTipoEndereco().trim().equals(""))){
                            throw new ConsistirException("O campo Tipo de Endereço (Aba - Endereço) deve ser informado.");
                        }
                    }
                }else if (cadastroDinamicoItemVO.getNomeCampo().equals(CadastroDinamicoColaboradorEnum.EMAIL.toString())){
                    if ((colaboradorVO.getPessoa().getEmailVOs() == null) || (colaboradorVO.getPessoa().getEmailVOs().isEmpty())){
                        throw new ConsistirException("O campo Email (Aba - Email) deve ser informado.");
                    }
                    for (EmailVO emailVO: colaboradorVO.getPessoa().getEmailVOs()){
                        if ((emailVO.getEmail() == null) || (emailVO.getEmail().trim().equals(""))){
                            throw new ConsistirException("O campo Email (Aba - Email) deve ser informado.");
                        }
                    }
                }else if (cadastroDinamicoItemVO.getNomeCampo().equals(CadastroDinamicoColaboradorEnum.TELEFONE.toString())){
                    if ((colaboradorVO.getPessoa().getTelefoneVOs() == null) || (colaboradorVO.getPessoa().getTelefoneVOs().isEmpty())){
                        throw new ConsistirException("O Telefone (Aba - Telefone) deve ser informado.");
                    }
                    for (TelefoneVO telefoneVO: colaboradorVO.getPessoa().getTelefoneVOs()){
                        if ((telefoneVO.getNumero() == null) || (telefoneVO.getNumero().trim().equals(""))){
                            throw new ConsistirException("O Telefone (Aba - Telefone) deve ser informado.");
                        }
                    }
                }


            }
        }

    }

    /**
     * Operação responsável por incluir no banco de dados um objeto da classe
     * <code>ColaboradorVO</code>. Primeiramente valida os dados (
     * <code>validarDados</code>) do objeto. Verifica a conexão com o banco de
     * dados e a permissão do usuário para realizar esta operacão na entidade.
     * Isto, através da operação
     * <code>incluir</code> da superclasse.
     *
     * @param obj Objeto da classe <code>ColaboradorVO</code> que será gravado
     * no banco de dados.
     * @exception Exception Caso haja problemas de conexão, restrição de acesso
     * ou validação de dados.
     */
    public void incluir(ColaboradorVO obj, boolean centralEventos, boolean validarPermissao) throws Exception {
        try {
            con.setAutoCommit(false);
            ColaboradorVO.validarDados(obj);
            if (centralEventos) {
//            	super.incluirObj(this.getIdEntidade());
            } else {
                if(validarPermissao) {
                    super.incluir(this.getIdEntidade());
                }
            }
            if (!centralEventos) {
                validarCamposDinamicosObrigatorios(obj);
            }
            // Colaborador.incluir(getIdEntidade());
            obj.realizarUpperCaseDados();
            Pessoa pessoa = new Pessoa(con);
            pessoa.incluirConexaoInicializada(obj.getPessoa(), con, validarPermissao);
            pessoa = null;
            prepararIncluir(obj, validarPermissao);
            con.commit();
        } catch (Exception e) {
            con.rollback();
            con.setAutoCommit(true);
            throw e;
        } finally {
            con.setAutoCommit(true);
        }
    }

    /* (non-Javadoc)
     * @see negocio.interfaces.arquitetura.PerfilAcessoInterfaceFacade#incluir(negocio.comuns.arquitetura.PerfilAcessoVO)
     */
    public void incluir(ColaboradorVO obj) throws Exception {
        this.incluir(obj, false, true);
    }

    public void incluirVendaRapida(ColaboradorVO obj, boolean validarIncluir) throws Exception {
        this.incluir(obj, false, validarIncluir);
    }

    /**
     * Operação responsável por incluir no banco de dados um objeto da classe
     * <code>ColaboradorVO</code>. Primeiramente valida os dados (
     * <code>validarDados</code>) do objeto. Verifica a conexão com o banco de
     * dados e a permissão do usuário para realizar esta operacão na entidade.
     * Isto, através da operação
     * <code>incluir</code> da superclasse.
     *
     * @param obj Objeto da classe <code>ColaboradorVO</code> que será gravado
     * no banco de dados.
     * @exception Exception Caso haja problemas de conexão, restrição de acesso
     * ou validação de dados.
     */
    public void incluirSemCommit(ColaboradorVO obj) throws Exception {
        incluirSemCommit(obj, true);
    }

    public void incluirSemCommit(ColaboradorVO obj, boolean incluirPessoa) throws Exception {
        ColaboradorVO.validarDados(obj);
        incluir(getIdEntidade());
        obj.realizarUpperCaseDados();
        if (incluirPessoa) {
            Pessoa pessoaDAO= new Pessoa(con);
            pessoaDAO.setIdEntidade(this.getIdEntidade());
            pessoaDAO.incluirConexaoInicializada(obj.getPessoa(), con);
            pessoaDAO = null;
        }
        prepararIncluir(obj);
    }

    public void incluirApartirDaTelaUsuarioSistemaSemCommit(ColaboradorVO obj) throws Exception{
        incluir(getIdEntidade());
        obj.realizarUpperCaseDados();
        prepararIncluir(obj);

    }

    /**
     * Operação responsável por incluir no banco de dados um objeto da classe
     * <code>ColaboradorVO</code>. Primeiramente valida os dados (
     * <code>validarDados</code>) do objeto. Verifica a conexão com o banco de
     * dados e a permissão do usuário para realizar esta operacão na entidade.
     * Isto, através da operação
     * <code>incluir</code> da superclasse.
     *
     * @param obj Objeto da classe <code>ColaboradorVO</code> que será gravado
     * no banco de dados.
     * @exception Exception Caso haja problemas de conexão, restrição de acesso
     * ou validação de dados.
     */
    public void incluirSemPessoa(ColaboradorVO obj, boolean centralEventos) throws Exception {
        try {
            con.setAutoCommit(false);
            ColaboradorVO.validarDados(obj);
            PessoaVO.validarDados(obj.getPessoa());
            if (centralEventos) {
//            	incluirObj(getIdEntidade());
            } else {
                validarCamposDinamicosObrigatorios(obj);
                incluir(getIdEntidade());
            }

            obj.realizarUpperCaseDados();
            prepararIncluir(obj);
            con.commit();
        } catch (Exception e) {
            con.rollback();
            con.setAutoCommit(true);
            throw e;
        } finally {
            con.setAutoCommit(true);
        }
    }
    /* (non-Javadoc)
     * @see negocio.interfaces.arquitetura.PerfilAcessoInterfaceFacade#incluir(negocio.comuns.arquitetura.PerfilAcessoVO)
     */

    public void incluirSemPessoa(ColaboradorVO obj) throws Exception {
        this.incluirSemPessoa(obj, false);
    }

    /**
     * Operação responsável por alterar no BD os dados de um objeto da classe
     * <code>ColaboradorVO</code>. Sempre utiliza a chave primária da classe
     * como atributo para localização do registro a ser alterado. Primeiramente
     * valida os dados (
     * <code>validarDados</code>) do objeto. Verifica a conexão com o banco de
     * dados e a permissão do usuário para realizar esta operacão na entidade.
     * Isto, através da operação
     * <code>alterar</code> da superclasse.
     *
     * @param obj Objeto da classe <code>ColaboradorVO</code> que será alterada
     * no banco de dados.
     * @exception Exception Caso haja problemas de conexão, restrição de acesso
     * ou validação de dados.
     */
    public void alterar(ColaboradorVO obj, boolean centralEventos, boolean validarPermissao) throws Exception {
        try {
            con.setAutoCommit(false);
            if (validarPermissao) {
                alterar(this.getIdEntidade());
                validarCamposDinamicosObrigatorios(obj);
            }
            if (isProfessorTreinoWeb(obj)) {
                TreinoWSConsumer.sincronizarProfessor(DAO.resolveKeyFromConnection(con), obj.toProfessorSintetico(), obj.getEmpresa().getCodigo());
            }
            alterarSemCommit(obj);
            con.commit();
        } catch (Exception e) {
            con.rollback();
            con.setAutoCommit(true);
            throw e;
        } finally {
            con.setAutoCommit(true);
        }
    }

    private boolean isProfessorTreinoWeb(ColaboradorVO obj) {
        boolean podeSincronizar = false;
        if (obj != null && obj.getListaTipoColaboradorVOs() != null && !obj.getListaTipoColaboradorVOs().isEmpty()) {
            for (TipoColaboradorVO tipoColaboradorVO : obj.getListaTipoColaboradorVOs()) {
                if (TipoColaboradorEnum.PROFESSOR_TREINO.getSigla().equals(tipoColaboradorVO.getDescricao())) {
                    podeSincronizar = true;
                    break;
                }
            }
        }
        if (!podeSincronizar) {
            ColaboradorVO antesAlteracao = (ColaboradorVO) obj.getObjetoVOAntesAlteracao();
            if (antesAlteracao != null && antesAlteracao.getListaTipoColaboradorVOs() != null && !antesAlteracao.getListaTipoColaboradorVOs().isEmpty()) {
                for (TipoColaboradorVO tipoColaboradorVO : antesAlteracao.getListaTipoColaboradorVOs()) {
                    if (TipoColaboradorEnum.PROFESSOR_TREINO.getSigla().equals(tipoColaboradorVO.getDescricao())) {
                        podeSincronizar = true;
                        break;
                    }
                }
            }
        }
        return podeSincronizar;
    }

    public void alterarSemCommit(ColaboradorVO obj) throws Exception {
        ColaboradorVO.validarDados(obj);
        obj.realizarUpperCaseDados();
        PessoaInterfaceFacade pessoaFacade = getFacade().getPessoa();
        pessoaFacade.setIdEntidade(this.getIdEntidade());
        pessoaFacade.alterarConexaoInicializadaSemPermissao(obj.getPessoa(), con);
        try (PreparedStatement ps = prepararAlterar(obj)) {
            if (obj.getCodigoUsuarioColaboradorTransfAgendaAntigo() != null && obj.getCodigoUsuarioColaboradorTransfAgendaNovo() != null
                    && obj.getSituacao().equals("NA")) {
                getFacade().getAgenda().alterarConsultorResponsavelAgenda(obj.getCodigoUsuarioColaboradorTransfAgendaAntigo(), obj.getCodigoUsuarioColaboradorTransfAgendaNovo(), Calendario.hoje());
            }
        }

        if ((obj.getColaboradorInfoRhVO().getCodigo() != null) && (obj.getColaboradorInfoRhVO().getCodigo() >0)){
            new ColaboradorInfoRh(con).alterar(obj);
        }else if (obj.getColaboradorInfoRhVO().informouAlgumCampo()){
            new ColaboradorInfoRh(con).incluir(obj);
        }
        incluirColaboradorDocumentoRh(obj);

    }

    /* (non-Javadoc)
     * @see negocio.interfaces.arquitetura.PerfilAcessoInterfaceFacade#incluir(negocio.comuns.arquitetura.PerfilAcessoVO)
     */
    @Override
    public void alterar(ColaboradorVO obj) throws Exception {
        this.alterar(obj, false,true);
    }
    @Override
    public void alterarVendaRapida(ColaboradorVO obj, boolean validarAlterar) throws Exception {
        this.alterar(obj, false, validarAlterar);
    }
    public void alterarSemPermissao(ColaboradorVO obj) throws Exception {
        this.alterar(obj, false,false);
    }
    /**
     * Operação responsável por excluir no BD um objeto da classe
     * <code>ColaboradorVO</code>. Sempre localiza o registro a ser excluído
     * através da chave primária da entidade. Primeiramente verifica a conexão
     * com o banco de dados e a permissão do usuário para realizar esta operacão
     * na entidade. Isto, através da operação
     * <code>excluir</code> da superclasse.
     *
     * @param obj Objeto da classe <code>ColaboradorVO</code> que será removido
     * no banco de dados.
     * @exception Exception Caso haja problemas de conexão ou restrição de
     * acesso.
     */
    public void excluir(ColaboradorVO obj, boolean centralEventos) throws Exception {
        try {
            con.setAutoCommit(false);
            if (centralEventos) {
//            	excluirObj(getIdEntidade());
            } else {
                excluir(getIdEntidade());
            }
            prepararExcluir(obj,true);
            con.commit();
        } catch (Exception e) {
            con.rollback();
            con.setAutoCommit(true);
            throw e;
        } finally {
            con.setAutoCommit(true);
        }
    }

    public void alterarSituacao(String situacao, Integer codigoColaborador) throws Exception{
        String sql = "update colaborador set situacao = ? where codigo = ?";
        try (PreparedStatement pst = con.prepareStatement(sql)) {
            pst.setString(1, situacao);
            pst.setInt(2, codigoColaborador);
            pst.execute();
        }
    }

    public void alterarSituacaoPorCodigoUsuario(String situacao, Integer codUsuario) throws Exception{
        String sql = "update colaborador set situacao = ? where codigo in (select colaborador from usuario where codigo = ?)";
        try (PreparedStatement pst = con.prepareStatement(sql)) {
            pst.setString(1, situacao);
            pst.setInt(2, codUsuario);
            pst.execute();
        }
    }

    /* (non-Javadoc)
     * @see negocio.interfaces.arquitetura.PerfilAcessoInterfaceFacade#incluir(negocio.comuns.arquitetura.PerfilAcessoVO)
     */
    public void excluir(ColaboradorVO obj) throws Exception {
        this.excluir(obj, false);
    }

    /**
     * Operação responsável por excluir no BD um objeto da classe
     * <code>ColaboradorVO</code>. Sempre localiza o registro a ser excluído
     * através da chave primária da entidade. Primeiramente verifica a conexão
     * com o banco de dados e a permissão do usuário para realizar esta operacão
     * na entidade. Isto, através da operação
     * <code>excluir</code> da superclasse.
     *
     * @param obj Objeto da classe <code>ColaboradorVO</code> que será removido
     * no banco de dados.
     * @exception Exception Caso haja problemas de conexão ou restrição de
     * acesso.
     */
    public void excluirSemPessoa(ColaboradorVO obj, boolean centralEventos) throws Exception {
        try {
            con.setAutoCommit(false);
            excluir(getIdEntidade());
            String sql = "DELETE FROM Colaborador WHERE ((codigo = ?))";
            prepararExcluir(obj, false);
            con.commit();
        } catch (Exception e) {
            con.rollback();
            con.setAutoCommit(true);
            throw e;
        } finally {
            con.setAutoCommit(true);
        }
    }
    /* (non-Javadoc)
     * @see negocio.interfaces.arquitetura.PerfilAcessoInterfaceFacade#incluir(negocio.comuns.arquitetura.PerfilAcessoVO)
     */

    public void excluirSemPessoa(ColaboradorVO obj) throws Exception {
        this.excluirSemPessoa(obj, false);
    }

    public ColaboradorVO consultarColaboradorPorPessoaVinculada(Integer valorConsulta, int nivelMontarDados) throws Exception {
        consultar(getIdEntidade(), false);
        String sql = "select colaborador.* from colaborador "
                + "inner join vinculo on colaborador.codigo = vinculo.colaborador and vinculo.tipovinculo = 'CO' "
                + "inner join cliente on cliente.codigo = vinculo.cliente and cliente.pessoa= " + valorConsulta
                + " limit 1";
        try (PreparedStatement sqlConsultar = con.prepareStatement(sql)) {
            try (ResultSet tabelaResultado = sqlConsultar.executeQuery()) {
                if (!tabelaResultado.next()) {
                    return new ColaboradorVO();
                }
                return (montarDados(tabelaResultado, nivelMontarDados, this.con));
            }
        }
    }

    public ColaboradorVO consultarExisteColaborador(String valorConsulta, Date data, int nivelMontarDados) throws Exception {
        consultar(getIdEntidade(), false);
        String sql = "SELECT Colaborador.*, Pessoa.* FROM Pessoa, Colaborador WHERE (colaborador.pessoa = pessoa.codigo) and pessoa.nome = '" + valorConsulta.toUpperCase() + "' and pessoa.datanasc = '" + Uteis.getDataJDBC(data) + "'";
        try (PreparedStatement sqlConsultar = con.prepareStatement(sql)) {
            try (ResultSet tabelaResultado = sqlConsultar.executeQuery()) {
                if (!tabelaResultado.next()) {
                    return new ColaboradorVO();
                }
                return (montarDados(tabelaResultado, nivelMontarDados, this.con));
            }
        }
    }

    public ColaboradorVO consultarExisteColaboradorAlterar(String valorConsulta, Date data, Integer codigo, int nivelMontarDados) throws Exception {
        consultar(getIdEntidade(), false);
        String sql = "SELECT Colaborador.*, Pessoa.* FROM Pessoa, Colaborador WHERE (colaborador.pessoa = pessoa.codigo) and pessoa.nome = '" + valorConsulta.toUpperCase() + "' and pessoa.datanasc = '" + Uteis.getDataJDBC(data) + "' and Colaborador.codigo <> " + codigo + "";
        try (PreparedStatement sqlConsultar = con.prepareStatement(sql)) {
            try (ResultSet tabelaResultado = sqlConsultar.executeQuery()) {
                if (!tabelaResultado.next()) {
                    return new ColaboradorVO();
                }
                return (montarDados(tabelaResultado, nivelMontarDados, this.con));
            }
        }
    }

    public ColaboradorVO consultarPorCfp(String valorConsulta, int nivelMontarDados) throws Exception {
        consultar(getIdEntidade(), true);
        String sqlStr = "SELECT  Colaborador.*, Pessoa.* FROM Pessoa, Colaborador WHERE Colaborador.pessoa = pessoa.codigo and  upper( cfp ) like('" + valorConsulta.toUpperCase() + "%')";
        sqlStr += "  ORDER BY pessoa.cfp";
        try (PreparedStatement sqlConsultar = con.prepareStatement(sqlStr)) {
            try (ResultSet tabelaResultado = sqlConsultar.executeQuery()) {
                if (!tabelaResultado.next()) {
                    return new ColaboradorVO();
                }
                return (montarDados(tabelaResultado, nivelMontarDados, this.con));
            }
        }
    }

    /**
     * Operação responsável por excluir no BD um objeto da classe
     * <code>ColaboradorVO</code>. Sempre localiza o registro a ser excluído
     * através da chave primária da entidade. Primeiramente verifica a conexão
     * com o banco de dados e a permissão do usuário para realizar esta operacão
     * na entidade. Isto, através da operação
     * <code>excluir</code> da superclasse.
     *
     * @exception Exception Caso haja problemas de conexão ou restrição de
     * acesso.
     */
    public List consultarPorCfp(String valorConsulta, Integer empresa, boolean controlarAcesso, int nivelMontarDados) throws Exception {
        consultar(getIdEntidade(), controlarAcesso);
        String sqlStr = "";
        if (empresa == 0) {
            sqlStr = "SELECT Pessoa.*, Colaborador.* FROM Pessoa, Colaborador WHERE Colaborador.pessoa = pessoa.codigo and  upper( cfp ) like('" + valorConsulta.toUpperCase() + "%') ORDER BY cfp";
        } else {
            sqlStr = "SELECT Pessoa.*, Colaborador.* FROM Pessoa, Colaborador WHERE Colaborador.pessoa = pessoa.codigo and  upper( cfp ) like('" + valorConsulta.toUpperCase() + "%')and Colaborador.empresa =" + empresa.intValue() + " ORDER BY cfp";
        }
        try (Statement stm = con.createStatement()) {
            try (ResultSet tabelaResultado = stm.executeQuery(sqlStr)) {
                return (montarDadosConsulta(tabelaResultado, nivelMontarDados, this.con));
            }
        }
    }

    public List consultarPorDescricao(String valorConsulta, boolean controlarAcesso, int nivelMontarDados, TipoColaboradorEnum tipoColaborador ) throws Exception {
        consultar(getIdEntidade(), controlarAcesso);
            StringBuilder sql = new StringBuilder( "SELECT * FROM colaborador ");
        sql.append(" INNER JOIN pessoa on pessoa.codigo = colaborador.pessoa");
        sql.append(" WHERE upper( pessoa.nome ) like('%" + valorConsulta.toUpperCase() + "%') ");
        try (Statement stm = con.createStatement()) {
            try (ResultSet tabelaResultado = stm.executeQuery(sql.toString())) {
                return (montarDadosConsulta(tabelaResultado, nivelMontarDados, this.con));
            }
        }
    }

    public List consultarPorDescricaoProfissao(String valorConsulta, Integer empresa, int nivelMontarDados) throws Exception {
        consultar(getIdEntidade(), true);
        String sqlStr = "";
        if (empresa == 0) {
            sqlStr = "SELECT Pessoa.* , Colaborador.* FROM Pessoa, Profissao, Colaborador WHERE Pessoa.profissao = Profissao.codigo and Colaborador.pessoa = pessoa.codigo and  upper( Profissao.descricao ) like('" + valorConsulta.toUpperCase() + "%') ORDER BY Profissao.descricao";
        } else {
            sqlStr = "SELECT Pessoa.* , Colaborador.* FROM Pessoa, Profissao, Colaborador WHERE Pessoa.profissao = Profissao.codigo and Colaborador.pessoa = pessoa.codigo and  upper( Profissao.descricao ) like('" + valorConsulta.toUpperCase() + "%') and Colaborador.empresa =" + empresa.intValue() + " ORDER BY Profissao.descricao";
        }
        try (Statement stm = con.createStatement()) {
            try (ResultSet tabelaResultado = stm.executeQuery(sqlStr)) {
                return montarDadosConsulta(tabelaResultado, nivelMontarDados, this.con);
            }
        }
    }

    public List consultarPorNomeCidade(String valorConsulta, Integer empresa, boolean controlarAcesso, int nivelMontarDados) throws Exception {
        consultar(getIdEntidade(), controlarAcesso);
        String sqlStr = "";
        if (empresa == 0) {
            sqlStr = "SELECT Pessoa.*, Cliente.*,Cidade.* FROM Pessoa,Cliente, Cidade WHERE Pessoa.cidade = Cidade.codigo and Cliente.pessoa = pessoa.codigo and Cidade.nome like('" + valorConsulta.toLowerCase() + "%')ORDER BY Cidade.nome";
        } else {
            sqlStr = "SELECT Pessoa.*, Cliente.*,Cidade.* FROM Pessoa,Cliente, Cidade WHERE Pessoa.cidade = Cidade.codigo and Cliente.pessoa = pessoa.codigo and Cidade.nome like('" + valorConsulta.toLowerCase() + "%') and Colaborador.empresa =" + empresa.intValue() + " ORDER BY Cidade.nome";
        }
        try (Statement stm = con.createStatement()) {
            try (ResultSet tabelaResultado = stm.executeQuery(sqlStr)) {
                return montarDadosConsulta(tabelaResultado, nivelMontarDados, this.con);
            }
        }
    }

    /**
     * Responsável por realizar uma consulta de
     * <code>Cliente</code> através do valor do atributo
     * <code>String codAcesso</code>. Retorna os objetos, com início do valor do
     * atributo idêntico ao parâmetro fornecido. Faz uso da operação
     * <code>montarDadosConsulta</code> que realiza o trabalho de prerarar o
     * List resultante.
     *
     * @param controlarAcesso Indica se a aplicação deverá verificar se o
     * usuário possui permissão para esta consulta ou não.
     * @return List Contendo vários objetos da classe <code>ClienteVO</code>
     * resultantes da consulta.
     * @exception Exception Caso haja problemas de conexão ou restrição de
     * acesso.
     */
    public List consultarPorCodAcesso(String valorConsulta, Integer empresa, boolean controlarAcesso, int nivelMontarDados) throws Exception {
        consultar(getIdEntidade(), controlarAcesso);
        String sqlStr = "";
        if (empresa == 0) {
            sqlStr = "SELECT * FROM Colaborador WHERE upper( codAcesso ) like('" + valorConsulta.toUpperCase() + "%') ORDER BY codAcesso";
        } else {
            sqlStr = "SELECT * FROM Colaborador WHERE upper( codAcesso ) like('" + valorConsulta.toUpperCase() + "%') and Colaborador.empresa =" + empresa + " ORDER BY codAcesso";
        }
        try (Statement stm = con.createStatement()) {
            try (ResultSet tabelaResultado = stm.executeQuery(sqlStr)) {
                return (montarDadosConsulta(tabelaResultado, nivelMontarDados, this.con));
            }
        }
    }

    public ColaboradorVO consultarPorCodigoUsuario(Integer valorConsulta, Integer empresa, boolean controlarAcesso, int nivelMontarDados) throws Exception {
        consultar(getIdEntidade(), controlarAcesso);
        String sqlStr = "";
        if (empresa == 0) {
            sqlStr = "Select Colaborador.* FROM Colaborador, Usuario "
                    + "WHERE Colaborador.codigo = usuario.colaborador "
                    + "and usuario.codigo = " + valorConsulta + " ORDER BY codigo";
        } else {
            sqlStr = "Select Colaborador.* FROM Colaborador"
                    + " inner join usuario on usuario.colaborador = colaborador.codigo"
                    + " inner join usuarioperfilacesso usu on usu.usuario = usuario.codigo "
                    + " WHERE  usuario.codigo = " + valorConsulta
                    + " and usu.empresa =" + empresa + " ORDER BY codigo";

            try (PreparedStatement sqlConsultar = con.prepareStatement(sqlStr)) {
                try (ResultSet tabelaResultado = sqlConsultar.executeQuery()) {
                    if (!tabelaResultado.next()) {
                        return new ColaboradorVO();
                    }
                    ColaboradorVO colab =  montarDados(tabelaResultado, nivelMontarDados, this.con);
                    sqlStr = "Select Colaborador.* from Colaborador where pessoa = " + colab.getPessoa().getCodigo()  + " and empresa = " + empresa;
                }
            }

        }
        try (PreparedStatement sqlConsultar = con.prepareStatement(sqlStr)) {
            try (ResultSet tabelaResultado = sqlConsultar.executeQuery()) {
                if (!tabelaResultado.next()) {
                    return new ColaboradorVO();
                }
                return montarDados(tabelaResultado, nivelMontarDados, this.con);
            }
        }
    }

    /**
     * Responsável por realizar uma consulta de
     * <code>Cliente</code> através do valor do atributo
     * <code>nome</code> da classe
     * <code>Categoria</code> Faz uso da operação
     * <code>montarDadosConsulta</code> que realiza o trabalho de prerarar o
     * List resultante.
     *
     * @return List Contendo vários objetos da classe <code>ClienteVO</code>
     * resultantes da consulta.
     * @exception Exception Caso haja problemas de conexão ou restrição de
     * acesso.
     */
    /**
     * Responsável por realizar uma consulta de
     * <code>Colaborador</code> através do valor do atributo
     * <code>String situacao</code>. Retorna os objetos, com início do valor do
     * atributo idêntico ao parâmetro fornecido. Faz uso da operação
     * <code>montarDadosConsulta</code> que realiza o trabalho de prerarar o
     * List resultante.
     *
     * @param controlarAcesso Indica se a aplicação deverá verificar se o
     * usuário possui permissão para esta consulta ou não.
     * @return List Contendo vários objetos da classe <code>ColaboradorVO</code>
     * resultantes da consulta.
     * @exception Exception Caso haja problemas de conexão ou restrição de
     * acesso.
     */
    public List consultarPorSituacao(String valorConsulta, Integer empresa, boolean controlarAcesso, int nivelMontarDados) throws Exception {
        consultar(getIdEntidade(), controlarAcesso);
        String sqlStr = "";
        if (empresa == 0) {
            sqlStr = "SELECT * FROM Colaborador WHERE upper( situacao ) like('" + valorConsulta.toUpperCase() + "%') ORDER BY situacao";
        } else {
            sqlStr = "SELECT * FROM Colaborador WHERE upper( situacao ) like('" + valorConsulta.toUpperCase() + "%') and Colaborador.empresa =" + empresa + " ORDER BY situacao";
        }
        try (Statement stm = con.createStatement()) {
            try (ResultSet tabelaResultado = stm.executeQuery(sqlStr)) {
                return (montarDadosConsulta(tabelaResultado, nivelMontarDados, this.con));
            }
        }
    }

    public List<ColaboradorVO> consultarPorTipoColaborador(TipoColaboradorEnum tipoColaborador, Integer empresa, boolean controlarAcesso, int nivelMontarDados) throws Exception {
        consultar(getIdEntidade(), controlarAcesso);
        StringBuilder sqlStr = new StringBuilder();
        sqlStr.append("SELECT Colaborador.* FROM Colaborador, tipoColaborador, pessoa WHERE Colaborador.pessoa = pessoa.codigo\n");
        if (tipoColaborador != null) {
            sqlStr.append("and upper(tipoColaborador.descricao) like('").append(tipoColaborador.getSigla().toUpperCase()).append("%')\n");
        }
        if (empresa != 0) {
            sqlStr.append("and colaborador.empresa =").append(empresa).append("\n");
        }
        sqlStr.append("and colaborador.codigo= tipocolaborador.colaborador ORDER BY pessoa.nome");
        try (Statement stm = con.createStatement()) {
            try (ResultSet tabelaResultado = stm.executeQuery(sqlStr.toString())) {
                return (montarDadosConsulta(tabelaResultado, nivelMontarDados, this.con));
            }
        }
    }


    public List<ColaboradorVO> consultarPorTipos(List<TipoColaboradorEnum> tipos,
                                                 String situacao,
                                                 Integer empresa,
                                                 String where,
                                                 String orderBy,
                                                 int limit,
                                                 int nivelMontarDados) throws Exception {
        String sql = "SELECT " +
                " Colaborador.* " +
                "FROM Colaborador " +
                "INNER JOIN tipoColaborador ON colaborador.codigo = tipocolaborador.colaborador " +
                "INNER JOIN pessoa ON pessoa.codigo = colaborador.pessoa " +
                "WHERE 1 = 1 ";
        if (tipos != null && tipos.size() > 0) {
            String tiposWhereIn = "";
            int index = 1;
            for (TipoColaboradorEnum tipo: tipos) {
                tiposWhereIn += index == tipos.size()
                        ? "'"+tipo.getSigla() +"'"
                        : "'"+tipo.getSigla() +"',";
                index++;
            }
            sql += " AND tipoColaborador.descricao IN ("+tiposWhereIn+") ";
        }

        if (situacao != null) {
            sql += " AND situacao = '"+situacao.toUpperCase()+"' ";
        }

        if (empresa != 0) {
            sql += " AND empresa = "+empresa+" ";
        }

        if(where != null && where.length() > 0){
            sql += where;
        }

        sql += " GROUP BY Colaborador.codigo, Pessoa.nome ";


        if(UteisValidacao.emptyString(orderBy)) {
            sql += " ORDER BY Pessoa.nome ";
        } else {
            sql += " ORDER BY " + orderBy + ", Pessoa.nome ";
        }

        if(limit > 0 ){
            sql += " LIMIT "+Integer.toString(limit);
        }

        try (Statement st = con.createStatement()) {
            try (ResultSet rs = st.executeQuery(sql)) {
                return (montarDadosConsulta(rs, nivelMontarDados, this.con));
            }
        }
    }

    public List<ColaboradorVO> consultarPorTipoColaboradorAtivo(TipoColaboradorEnum tipoColaborador, String situacao, Integer empresa, boolean controlarAcesso, int nivelMontarDados) throws Exception {
        consultar(getIdEntidade(), controlarAcesso);
        StringBuilder sql = new StringBuilder("SELECT\n"
                + "  Colaborador.*\n"
                + "FROM Colaborador, tipoColaborador\n"
                + "WHERE colaborador.codigo = tipocolaborador.colaborador\n");
        if (tipoColaborador != null) {
            sql.append("      AND upper(tipoColaborador.descricao) LIKE ('").append(tipoColaborador.getSigla().toUpperCase()).append("%')");
        }

        if (situacao != null) {
            sql.append("      AND situacao = '").append(situacao.toUpperCase()).append("'\n");
        }

        if (empresa != 0) {
            sql.append("      AND empresa = ").append(empresa).append("\n");
        }

        sql.append("ORDER BY tipoColaborador.descricao");

        try (Statement stm = con.createStatement()) {
            try (ResultSet tabelaResultado = stm.executeQuery(sql.toString())) {
                return (montarDadosConsulta(tabelaResultado, nivelMontarDados, this.con));
            }
        }
    }

    public List<ColaboradorVO> consultarPorTipoColaboradorAtivoNomePessoa(String nome, TipoColaboradorEnum tipoColaborador, String situacao, Integer empresa, boolean controlarAcesso, int nivelMontarDados) throws Exception {
        consultar(getIdEntidade(), controlarAcesso);
        StringBuilder sqlStr = new StringBuilder();
        sqlStr.append("SELECT colaborador.* FROM Colaborador\n");
        sqlStr.append("inner join tipocolaborador tc on tc.colaborador = colaborador.codigo\n");
        sqlStr.append("inner join pessoa on pessoa.codigo = colaborador.pessoa\n");
        sqlStr.append("WHERE \n");
        sqlStr.append(" situacao = 'AT' and pessoa.nome ilike '").append(nome).append("%'\n");
        if (tipoColaborador != null) {
            sqlStr.append(" AND upper(tc.descricao) like('").append(tipoColaborador.getSigla()).append("%')\n");
        }
        if (empresa != null && empresa != 0) {
            sqlStr.append("and colaborador.empresa =  ").append(empresa);
        }
        sqlStr.append(" ORDER BY tc.descricao");
        try (Statement stm = con.createStatement()) {
            try (ResultSet tabelaResultado = stm.executeQuery(sqlStr.toString())) {
                return (montarDadosConsulta(tabelaResultado, nivelMontarDados, this.con));
            }
        }
    }
    public List<ColaboradorVO> consultarPorTipoColaboradorAtivoNomePessoa(String nome,int colaboradorDesconsiderar, ListIterator<TipoColaboradorVO> tipoColaborador, String situacao, Integer empresa, boolean controlarAcesso, int nivelMontarDados) throws Exception {
        consultar(getIdEntidade(), controlarAcesso);
        StringBuilder sqlStr = new StringBuilder();
        sqlStr.append("SELECT colaborador.* FROM Colaborador\n");
        sqlStr.append("inner join tipocolaborador tc on tc.colaborador = colaborador.codigo\n");
        sqlStr.append("inner join pessoa on pessoa.codigo = colaborador.pessoa\n");
        sqlStr.append("WHERE \n");
        if(tipoColaborador.hasNext()) {
            sqlStr.append("  UPPER(tc.descricao) in ('");
            while (tipoColaborador.hasNext()) {
                TipoColaboradorVO tipoColaboradorVO = (TipoColaboradorVO) tipoColaborador.next();
                sqlStr.append(tipoColaboradorVO.getDescricao()).append("','");
            }
            sqlStr.deleteCharAt(sqlStr.length()-1);
            sqlStr.deleteCharAt(sqlStr.length()-1);
         //   int n=0;
         //   sqlStr.replace(sqlStr.lastIndexOf("',")-1, sqlStr.lastIndexOf("',")+1, "");
            sqlStr.append(")\n");
            sqlStr.append(" AND");
        }
        if (empresa != null && empresa != 0) {
            sqlStr.append(" colaborador.empresa =  ").append(empresa);
            sqlStr.append(" AND");
        }
        sqlStr.append("  situacao = 'AT'");
        if(!nome.equals(""))
        sqlStr.append(" AND pessoa.nome ilike '").append(nome).append("%'\n");
        sqlStr.append(" AND colaborador.codigo != ").append(colaboradorDesconsiderar);
        if (empresa != null && empresa != 0) {
            sqlStr.append("AND colaborador.empresa =  ").append(empresa);
        }
        sqlStr.append(" ORDER BY tc.descricao");
        try (Statement stm = con.createStatement()) {
            try (ResultSet tabelaResultado = stm.executeQuery(sqlStr.toString())) {
                return (montarDadosConsulta(tabelaResultado, nivelMontarDados, this.con));
            }
        }
    }
    public List<ColaboradorVO> consultarPorTipoColaboradorAtivoCodigo(int codigo,ListIterator<TipoColaboradorVO> tipoColaborador, Integer empresa,Boolean controlarAcesso, int nivelMontarDados) throws Exception {
        consultar(getIdEntidade(), controlarAcesso);
        StringBuilder sqlStr = new StringBuilder();
        sqlStr.append("SELECT colaborador.* FROM Colaborador\n");
        sqlStr.append("inner join tipocolaborador tc on tc.colaborador = colaborador.codigo\n");
        sqlStr.append("inner join pessoa on pessoa.codigo = colaborador.pessoa\n");
        sqlStr.append("WHERE \n");
        sqlStr.append("  situacao = 'AT' AND colaborador.codigo =").append(codigo).append("\n");
        sqlStr.append(" AND");
        if(tipoColaborador.hasNext()) {
            sqlStr.append(" upper(tc.descricao) in ('");
            while (tipoColaborador.hasNext()) {
                TipoColaboradorVO tipoColaboradorVO = (TipoColaboradorVO) tipoColaborador.next();
                sqlStr.append(tipoColaboradorVO.getDescricao()).append("','");
            }
            sqlStr.deleteCharAt(sqlStr.length()-1);
            sqlStr.deleteCharAt(sqlStr.length()-1);
            sqlStr.append(")\n");
            sqlStr.append(" AND");
        }
        if (empresa != null && empresa != 0) {
            sqlStr.append(" colaborador.empresa =  ").append(empresa);
        }else{
            sqlStr.replace(sqlStr.lastIndexOf("AND"),sqlStr.lastIndexOf("AND")+1,"");
        }
        sqlStr.append(" ORDER BY tc.descricao");
        try (Statement stm = con.createStatement()) {
            try (ResultSet tabelaResultado = stm.executeQuery(sqlStr.toString())) {
                return (montarDadosConsulta(tabelaResultado, nivelMontarDados, this.con));
            }
        }
    }
    /**
     * Responsável por realizar uma consulta de
     * <code>Colaborador</code> através do valor do atributo
     * <code>nome</code> da classe
     * <code>Pessoa</code> Faz uso da operação
     * <code>montarDadosConsulta</code> que realiza o trabalho de prerarar o
     * List resultante.
     *
     * @return List Contendo vários objetos da classe <code>ColaboradorVO</code>
     * resultantes da consulta.
     * @exception Exception Caso haja problemas de conexão ou restrição de
     * acesso.
     */
    public List<ColaboradorVO> consultarPorNomePessoa(String valorConsulta, Integer empresa, int nivelMontarDados) throws Exception {
        consultar(getIdEntidade(), true);
        String sqlStr = "";
        if (empresa == 0) {
            sqlStr = "SELECT Colaborador.* FROM Colaborador, Pessoa WHERE Colaborador.pessoa = Pessoa.codigo and upper( Pessoa.nome ) like('" + valorConsulta.toUpperCase() + "%') and situacao = 'AT' ORDER BY Pessoa.nome";
        } else {
            sqlStr = "SELECT Colaborador.* FROM Colaborador, Pessoa WHERE Colaborador.pessoa = Pessoa.codigo and upper( Pessoa.nome ) like('" + valorConsulta.toUpperCase() + "%') and Colaborador.empresa =" + empresa.intValue() + " and situacao = 'AT' ORDER BY Pessoa.nome";
        }
        try (Statement stm = con.createStatement()) {
            try (ResultSet tabelaResultado = stm.executeQuery(sqlStr)) {
                return montarDadosConsulta(tabelaResultado, nivelMontarDados, this.con);
            }
        }
    }

    public List<ColaboradorVO> obterColaboradorPactoMetodoGestao(Integer empresa, int nivelMontarDados) throws Exception {
        consultar(getIdEntidade(), true);
        //o sql vai buscar o colaborador com pessoa 'PACTO - METODO DE GESTAO' e o encontrará mesmo caso haja variaçoes nesse nome, como ausencia de acentos etc
        //motivaçao: fix GCM-272
        String sqlStr = "SELECT Colaborador.* FROM Colaborador " +
                "JOIN Pessoa ON Colaborador.pessoa = Pessoa.codigo " +
                "WHERE UNACCENT(UPPER(Pessoa.nome)) LIKE '%PACTO%METODO%GESTAO%' " +
                "AND Colaborador.situacao = 'AT' ";

        if (empresa != null && empresa > 0) {
            sqlStr += "AND Colaborador.empresa = " + empresa.intValue() + " ";
        }

        sqlStr += "ORDER BY Pessoa.nome";

        try (Statement stm = con.createStatement()) {
            try (ResultSet tabelaResultado = stm.executeQuery(sqlStr)) {
                return montarDadosConsulta(tabelaResultado, nivelMontarDados, this.con);
            }
        }
    }


    public String consultarPorNomePessoa(int codigoColaborador) throws Exception {
        consultar(getIdEntidade(), true);
        String sqlStr = "";
        sqlStr = "SELECT corAgendaProfissional FROM Colaborador WHERE codigo = " + codigoColaborador;
        try (Statement stm = con.createStatement()) {
            try (ResultSet tabelaResultado = stm.executeQuery(sqlStr)) {
                tabelaResultado.next();
                return tabelaResultado.getString("corAgendaProfissional");
            }
        }
    }

    public List<ColaboradorVO> consultarPorNomeOperador(String valorConsulta, Integer empresa, int nivelMontarDados) throws Exception {
        consultar(getIdEntidade(), true);
        String sqlStr = "select * from colaborador"
                + " INNER JOIN pessoa ON colaborador.pessoa = pessoa.codigo"
                + " inner join usuario on usuario.colaborador = colaborador.codigo"
                + " inner join usuarioperfilacesso usuPer on usuPer.usuario = usuario.codigo "
                + " WHERE UPPER(pessoa.nome) like('" + valorConsulta.toUpperCase() + "%') and situacao = 'AT'  ";
        if (empresa != 0) {
            sqlStr += " AND usuPer.empresa =" + empresa;
        }
        sqlStr += " ORDER BY pessoa.nome";
        try (Statement stm = con.createStatement()) {
            try (ResultSet tabelaResultado = stm.executeQuery(sqlStr)) {
                return montarDadosConsulta(tabelaResultado, nivelMontarDados, this.con);
            }
        }
    }

    public ColaboradorVO consultarPorNomeColaborador(String valorConsulta, Integer empresa, int nivelMontarDados) throws Exception {
        consultar(getIdEntidade(), true);
        String sqlStr = "";
        if (empresa == 0) {
            sqlStr = "SELECT Colaborador.* FROM Colaborador, Pessoa WHERE Colaborador.pessoa = Pessoa.codigo and upper( Pessoa.nome ) like('" + valorConsulta.toUpperCase() + "%') ORDER BY Pessoa.nome";
        } else {
            sqlStr = "SELECT Colaborador.* FROM Colaborador, Pessoa WHERE Colaborador.pessoa = Pessoa.codigo and upper( Pessoa.nome ) like('" + valorConsulta.toUpperCase() + "%') and Colaborador.empresa =" + empresa.intValue() + " ORDER BY Pessoa.nome";
        }
        try (PreparedStatement sqlConsultar = con.prepareStatement(sqlStr)) {
            try (ResultSet tabelaResultado = sqlConsultar.executeQuery()) {
                if (!tabelaResultado.next()) {
                    return new ColaboradorVO();
                }
                return (montarDados(tabelaResultado, nivelMontarDados, this.con));
            }
        }
    }

    public ColaboradorVO consultarPorNomeColaboradorImportacao(String valorConsulta, Integer empresa, int nivelMontarDados) throws Exception {
        consultar(getIdEntidade(), true);

        String sql = "SELECT Colaborador.* FROM Colaborador, Pessoa WHERE Colaborador.pessoa = Pessoa.codigo AND upper(Pessoa.nome) LIKE ? ";
        if (empresa != 0) {
            sql += "AND Colaborador.empresa = ? ";
        }
        sql += "ORDER BY Pessoa.nome";

        try (PreparedStatement sqlConsultar = con.prepareStatement(sql)) {
            sqlConsultar.setString(1, valorConsulta.toUpperCase() + "%");
            if (empresa != 0) {
                sqlConsultar.setInt(2, empresa);
            }

            try (ResultSet tabelaResultado = sqlConsultar.executeQuery()) {
                if (!tabelaResultado.next()) {
                    return null;
                }
                return montarDados(tabelaResultado, nivelMontarDados, this.con);
            }
        }
    }


    public List consultarPorNomeColaboradorComLimite(String valorConsulta, Integer empresa, boolean controlarAcesso, int nivelMontarDados) throws Exception {
        consultar(getIdEntidade(), controlarAcesso);
        String sqlStr = "";
        if (empresa == 0) {
            sqlStr = "SELECT Colaborador.* FROM Colaborador, Pessoa WHERE Colaborador.pessoa = pessoa.codigo and upper(Pessoa.nome) like('" + valorConsulta.toUpperCase() + "%') ORDER BY pessoa.nome limit 50";
        } else {
            sqlStr = "SELECT Colaborador.* FROM Colaborador, Pessoa WHERE Colaborador.pessoa = pessoa.codigo and upper(Pessoa.nome) like('" + valorConsulta.toUpperCase() + "%') and Colaborador.empresa = " + empresa.intValue() + " ORDER BY pessoa.nome limit 50";
        }
        try (Statement stm = con.createStatement()) {
            try (ResultSet tabelaResultado = stm.executeQuery(sqlStr)) {
                return (montarDadosConsulta(tabelaResultado, nivelMontarDados, this.con));
            }
        }
    }

    public List consultarPorColaboradorComLimite(Integer empresa, boolean controlarAcesso, int nivelMontarDados) throws Exception {
        consultar(getIdEntidade(), controlarAcesso);
        String sqlStr = "";
        if (empresa == 0) {
            sqlStr = "SELECT Colaborador.* FROM Colaborador, Pessoa "
                    + "WHERE Colaborador.pessoa = pessoa.codigo "
                    + "ORDER BY pessoa.nome limit 50";
        } else {
            sqlStr = "SELECT Colaborador.* FROM Colaborador, Pessoa "
                    + "WHERE Colaborador.pessoa = pessoa.codigo and upper(Pessoa.nome) "
                    + "and Colaborador.empresa = " + empresa
                    + " ORDER BY pessoa.nome limit 50";
        }
        try (Statement stm = con.createStatement()) {
            try (ResultSet tabelaResultado = stm.executeQuery(sqlStr)) {
                return (montarDadosConsulta(tabelaResultado, nivelMontarDados, this.con));
            }
        }
    }

    public List consultarTodosColaboradoresComLimite(boolean controlarAcesso, int nivelMontarDados) throws Exception {
        consultar(getIdEntidade(), controlarAcesso);
        String sqlStr = "";
        sqlStr = "SELECT Colaborador.* FROM Colaborador , pessoa WHERE Colaborador.pessoa = pessoa.codigo  ORDER BY pessoa.nome limit 50";
        try (Statement stm = con.createStatement()) {
            try (ResultSet tabelaResultado = stm.executeQuery(sqlStr)) {
                return (montarDadosConsulta(tabelaResultado, nivelMontarDados, this.con));
            }
        }
    }

    public List consultarTodosConsultoresComLimite(boolean controlarAcesso, int nivelMontarDados) throws Exception {
        consultar(getIdEntidade(), controlarAcesso);
        String sqlStr = "";
        sqlStr = "SELECT Colaborador.* FROM Colaborador , pessoa WHERE Colaborador.pessoa = pessoa.codigo  ORDER BY pessoa.nome limit 50";
        try (Statement stm = con.createStatement()) {
            try (ResultSet tabelaResultado = stm.executeQuery(sqlStr)) {
                return (montarDadosConsulta(tabelaResultado, nivelMontarDados, this.con));
            }
        }
    }

    public List consultarTodosColaboradorComLimite(Integer empresa, boolean controlarAcesso, int nivelMontarDados) throws Exception {
        consultar(getIdEntidade(), controlarAcesso);
        String sqlStr = "";
        if (empresa == 0) {
            sqlStr = "SELECT Colaborador.* FROM Colaborador, pessoa ORDER BY pessoa.nome limit 50";
        } else {
            sqlStr = "SELECT Colaborador.* FROM Colaborador, Pessoa WHERE colaborador.empresa = " + empresa + " ORDER BY pessoa.nome limit 50";
        }

        try (Statement stm = con.createStatement()) {
            try (ResultSet tabelaResultado = stm.executeQuery(sqlStr)) {
                return (montarDadosConsulta(tabelaResultado, nivelMontarDados, this.con));
            }
        }
    }

    public List<ColaboradorVO> consultarTodosColaboradoresPorTipoComLimite(TipoColaboradorEnum tipoColaborador, boolean controlarAcesso, int nivelMontarDados) throws Exception {
        consultar(getIdEntidade(), controlarAcesso);
        StringBuilder sqlStr = new StringBuilder();
        sqlStr.append("SELECT Colaborador.* FROM Colaborador "
                + "inner join pessoa on pessoa.codigo = colaborador.pessoa "
                + "inner join tipocolaborador on tipocolaborador.colaborador =  colaborador.codigo ");
        if (tipoColaborador != null) {
            sqlStr.append(" WHERE  upper( tipoColaborador.descricao) like('").append(tipoColaborador.getSigla().toUpperCase()).append("')");
        }
        sqlStr.append(" ORDER BY pessoa.nome limit 50");
        try (Statement stm = con.createStatement()) {
            try (ResultSet tabelaResultado = stm.executeQuery(sqlStr.toString())) {
                return (montarDadosConsulta(tabelaResultado, nivelMontarDados, this.con));
            }
        }
    }

    public List<ColaboradorVO> consultarTodosColaboradoresPorTipoPorNomeComLimite(String nome, TipoColaboradorEnum tipoColaborador, boolean comPorcentagem, boolean controlarAcesso, int nivelMontarDados) throws Exception {
        consultar(getIdEntidade(), controlarAcesso);
        StringBuilder sqlStr = new StringBuilder();
        sqlStr.append("SELECT Colaborador.* FROM Colaborador "
                + "inner join pessoa on pessoa.codigo = colaborador.pessoa "
                + "left join tipocolaborador on tipocolaborador.colaborador =  colaborador.codigo "
                + " WHERE 1 = 1\n");
        if (tipoColaborador != null) {
            sqlStr.append(" and upper( tipoColaborador.descricao) like('").append(tipoColaborador.getSigla().toUpperCase()).append("')\n");
        }
        sqlStr.append(" and pessoa.nome ilike '").append(nome);
        if (comPorcentagem) {
            sqlStr.append("%'");
        } else {
            sqlStr.append("'");
        }
        sqlStr.append(" ORDER BY pessoa.nome limit 50");
        try (Statement stm = con.createStatement()) {
            try (ResultSet tabelaResultado = stm.executeQuery(sqlStr.toString())) {
                return (montarDadosConsulta(tabelaResultado, nivelMontarDados, this.con));
            }
        }
    }

    /**
     * Responsável por realizar uma consulta de
     * <code>Colaborador</code> através do valor do atributo
     * <code>codigo</code> da classe
     * <code>Empresa</code> Faz uso da operação
     * <code>montarDadosConsulta</code> que realiza o trabalho de prerarar o
     * List resultante.
     *
     * @return List Contendo vários objetos da classe <code>ColaboradorVO</code>
     * resultantes da consulta.
     * @exception Exception Caso haja problemas de conexão ou restrição de
     * acesso.
     */
    public List consultarPorCodigoEmpresa(Integer valorConsulta, int nivelMontarDados) throws Exception {
        consultar(getIdEntidade(), true);
        String sqlStr = "";
        sqlStr = "SELECT Colaborador.* FROM Colaborador, Empresa WHERE Colaborador.empresa='" + valorConsulta + "' ORDER BY Empresa.nome";
        try (Statement stm = con.createStatement()) {
            try (ResultSet tabelaResultado = stm.executeQuery(sqlStr)) {
                return montarDadosConsulta(tabelaResultado, nivelMontarDados, this.con);
            }
        }
    }

    /**
     * Responsável por realizar uma consulta de
     * <code>Colaborador</code> através do valor do atributo
     * <code>nome</code> da classe
     * <code>Empresa</code> Faz uso da operação
     * <code>montarDadosConsulta</code> que realiza o trabalho de prerarar o
     * List resultante.
     *
     * @return List Contendo vários objetos da classe <code>ColaboradorVO</code>
     * resultantes da consulta.
     * @exception Exception Caso haja problemas de conexão ou restrição de
     * acesso.
     */
    public List consultarPorNomeEmpresa(String valorConsulta, int nivelMontarDados) throws Exception {
        consultar(getIdEntidade(), true);
        String sqlStr = "";
        sqlStr = "SELECT Colaborador.* FROM Colaborador, Empresa WHERE Colaborador.empresa=empresa.codigo and upper( empresa.nome ) like('" + valorConsulta.toUpperCase() + "%') ORDER BY empresa.nome";
        try (Statement stm = con.createStatement()) {
            try (ResultSet tabelaResultado = stm.executeQuery(sqlStr)) {
                return montarDadosConsulta(tabelaResultado, nivelMontarDados, this.con);
            }
        }
    }

    public List<ColaboradorVO> consultarPorNomeTipoColaborador(String valorConsulta, Integer empresa, Boolean apenasAtivos, Boolean ignorarTipos, int nivelMontarDados, TipoColaboradorEnum... tiposColaborador) throws Exception {
        consultar(getIdEntidade(), true);
        StringBuilder sql = new StringBuilder();
        sql.append("SELECT DISTINCT colaborador.*, pessoa.nome\n");
        if (!ignorarTipos) {
            sql.append(", t.descricao as tipocolaborador \n");
        }
        sql.append("FROM colaborador\n");
        sql.append("INNER JOIN pessoa ON colaborador.pessoa = pessoa.codigo ");
        sql.append("INNER JOIN tipoColaborador t ON colaborador.codigo= t.colaborador ");
        sql.append("WHERE UPPER( pessoa.nome ) like('" + valorConsulta.toUpperCase() + "%') AND ");
        sql.append("UPPER( t.descricao) IN ('" + TipoColaboradorEnum.getTiposSQL(true, tiposColaborador) + "') ");
        sql.append((empresa == 0 ? "" : "AND colaborador.empresa = " + empresa + " "));
        if (apenasAtivos) {
            sql.append("AND situacao LIKE 'AT' ");
        }
        sql.append("ORDER BY pessoa.nome ");


        try (Statement stm = con.createStatement()) {
            try (ResultSet tabelaResultado = stm.executeQuery(sql.toString())) {
                return montarDadosConsulta(tabelaResultado, nivelMontarDados, this.con);
            }
        }
    }

    public List<ColaboradorVO> consultarPorCpfTipoColaborador(String valorConsulta, Integer empresa, Boolean apenasAtivos, Boolean ignorarTipos, int nivelMontarDados, TipoColaboradorEnum... tiposColaborador) throws Exception {
        consultar(getIdEntidade(), true);
        StringBuilder sql = new StringBuilder();
        sql.append("SELECT DISTINCT colaborador.*, pessoa.nome\n");
        if (!ignorarTipos) {
            sql.append(", t.descricao as tipocolaborador \n");
        }
        sql.append("FROM colaborador\n");
        sql.append("INNER JOIN pessoa ON colaborador.pessoa = pessoa.codigo ");
        sql.append("INNER JOIN tipoColaborador t ON colaborador.codigo= t.colaborador ");
        sql.append("WHERE UPPER( pessoa.cfp ) like('" + valorConsulta.toUpperCase() + "%') AND ");
        sql.append("UPPER( t.descricao) IN ('" + TipoColaboradorEnum.getTiposSQL(true, tiposColaborador) + "') ");
        sql.append((empresa == 0 ? "" : "AND colaborador.empresa = " + empresa + " "));
        if (apenasAtivos) {
            sql.append("AND situacao LIKE 'AT' ");
        }
        sql.append("ORDER BY pessoa.nome ");


        try (Statement stm = con.createStatement()) {
            try (ResultSet tabelaResultado = stm.executeQuery(sql.toString())) {
                return montarDadosConsulta(tabelaResultado, nivelMontarDados, this.con);
            }
        }
    }

    /**
     * Responsável por realizar uma consulta de
     * <code>Colaborador</code> através do valor do atributo
     * <code>Integer codigo</code>. Retorna os objetos com valores iguais ou
     * superiores ao parâmetro fornecido. Faz uso da operação
     * <code>montarDadosConsulta</code> que realiza o trabalho de prerarar o
     * List resultante.
     *
     * @param controlarAcesso Indica se a aplicação deverá verificar se o
     * usuário possui permissão para esta consulta ou não.
     * @return List Contendo vários objetos da classe <code>ColaboradorVO</code>
     * resultantes da consulta.
     * @exception Exception Caso haja problemas de conexão ou restrição de
     * acesso.
     */
    public List consultarPorCodigo(Integer valorConsulta, Integer empresa, boolean controlarAcesso, int nivelMontarDados) throws Exception {
        consultar(getIdEntidade(), controlarAcesso);
        String sqlStr = "";
        if (empresa == 0) {
            sqlStr = "SELECT * FROM Colaborador WHERE codigo >= " + valorConsulta.intValue() + " and situacao = 'AT' ORDER BY codigo";
        } else {
            sqlStr = "SELECT * FROM Colaborador WHERE codigo >= " + valorConsulta.intValue() + "and Colaborador.empresa =" + empresa.intValue() + " and situacao = 'AT' ORDER BY codigo";
        }
        try (Statement stm = con.createStatement()) {
            try (ResultSet tabelaResultado = stm.executeQuery(sqlStr)) {
                return (montarDadosConsulta(tabelaResultado, nivelMontarDados, this.con));
            }
        }
    }

    /**
     * 14/01/11 Ulisses... Responsável por realizar uma consulta de
     * <code>Colaborador</code> através do valor do atributo
     * <code>Integer codigo</code>. Retorna o objeto com o valor igual ao
     * parâmetro fornecido. Faz uso da operação
     * <code>montarDados</code> que realiza o trabalho de prerarar o objeto
     * resultante.
     *
     * @param controlarAcesso Indica se a aplicação deverá verificar se o
     * usuário possui permissão para esta consulta ou não.
     * @return ColaboradorVO Contendo um objeto da
     * classe <code>ColaboradorVO</code> resultante da consulta.
     * @exception Exception Caso haja problemas de conexão ou restrição de
     * acesso.
     */
    public ColaboradorVO consultarColaboradorPorCodigo(Integer valorConsulta, Integer empresa, boolean controlarAcesso, int nivelMontarDados) throws Exception {
        consultar(getIdEntidade(), controlarAcesso);
        String sqlStr = "";
        if (empresa == 0) {
            sqlStr = "SELECT * FROM Colaborador WHERE codigo = " + valorConsulta;
        } else {
            sqlStr = "SELECT * FROM Colaborador WHERE codigo = " + valorConsulta + " and Colaborador.empresa =" + empresa;
        }
        ColaboradorVO colaborador;
        try (Statement stm = con.createStatement()) {
            try (ResultSet tabelaResultado = stm.executeQuery(sqlStr)) {
                colaborador = null;
                if (tabelaResultado.next()) {
                    colaborador = (montarDados(tabelaResultado, nivelMontarDados, this.con));
                }
            }
        }
        return colaborador;
    }

    public ColaboradorVO consultarPorCodigoIgualTipoColaborador(Integer valorConsulta, Integer empresa, TipoColaboradorEnum tipoColaborador, boolean controlarAcesso, int nivelMontarDados) throws Exception {
        consultar(getIdEntidade(), controlarAcesso);
        StringBuilder sqlStr = new StringBuilder();
        sqlStr.append("SELECT Colaborador.* FROM Colaborador,  tipoColaborador  WHERE Colaborador.codigo = ").append(valorConsulta);
        if (tipoColaborador != null) {
            sqlStr.append(" and upper( tipoColaborador.descricao) like('").append(tipoColaborador.getSigla().toUpperCase()).append("%')\n");
        }
        sqlStr.append(" and situacao = 'AT' ");
        if (empresa == 0) {
            sqlStr.append(" and Colaborador.empresa =").append(empresa);
        }
        sqlStr.append(" and colaborador.codigo= tipocolaborador.colaborador ORDER BY Colaborador.codigo");

        ColaboradorVO colaborador;
        try (Statement stm = con.createStatement()) {
            try (ResultSet tabelaResultado = stm.executeQuery(sqlStr.toString())) {
                colaborador = null;
                if (tabelaResultado.next()) {
                    colaborador = (montarDados(tabelaResultado, nivelMontarDados, this.con));
                }
            }
        }
        return colaborador;
    }

    public ColaboradorVO consultarPorCodigoPessoa(Integer codigoPrm, Integer empresa, int nivelMontarDados) throws Exception {
        consultar(getIdEntidade(), false);
        String sqlStr = "";
        if (empresa == 0) {
            sqlStr = "SELECT * FROM Colaborador WHERE colaborador.pessoa = ?";
        } else {
            sqlStr = "SELECT * FROM Colaborador WHERE colaborador.pessoa = ? and Colaborador.empresa =" + empresa + " ORDER BY codigo";
        }
        try (PreparedStatement sqlConsultar = con.prepareStatement(sqlStr)) {
            sqlConsultar.setInt(1, codigoPrm.intValue());
            try (ResultSet tabelaResultado = sqlConsultar.executeQuery()) {
                if (!tabelaResultado.next()) {
                    ColaboradorVO obj = new ColaboradorVO();
                    obj.getPessoa().setCodigo(0);
                    return obj;
                }
                return (montarDados(tabelaResultado, nivelMontarDados, this.con));
            }
        }
    }

    public List consultarColaboradorPorClienteMensagem(Integer empresa, boolean controlarAcesso, int nivelMontarDados) throws Exception {
        consultar(getIdEntidade(), controlarAcesso);
        String sqlStr = "SELECT DISTINCT on (Colaborador.codigo) Colaborador.* FROM colaborador "
                + "inner join vinculo on vinculo.colaborador = colaborador.codigo "
                + "inner join cliente on vinculo.cliente =  cliente.codigo and cliente.empresa = " + empresa.intValue() + " and "
                + "( SELECT COUNT(*) FROM clienteMensagem where cliente.codigo = clienteMensagem.cliente and "
                + "(clienteMensagem.tipoMensagem = '" + TiposMensagensEnum.BOLETIM.getSigla() + "' OR "
                + " clienteMensagem.tipoMensagem = '" + TiposMensagensEnum.DADOS_INCOMPLETOS.getSigla() + "' OR "
                + " clienteMensagem.tipoMensagem = '" + TiposMensagensEnum.PARCELA_ATRASO.getSigla() + "')) > 0";
        try (Statement stm = con.createStatement()) {
            try (ResultSet tabelaResultado = stm.executeQuery(sqlStr)) {
                return (montarDadosConsulta(tabelaResultado, nivelMontarDados, this.con));
            }
        }
    }

    public List<ColaboradorVO> consultarColaboradorPorRisco(int empresa, boolean controlarAcesso, int nivelMontarDados) throws Exception {
        consultar(getIdEntidade(), controlarAcesso);
        String sqlStr = "SELECT DISTINCT on (Colaborador.codigo) Colaborador.* FROM colaborador "
                + "INNER JOIN vinculo ON vinculo.colaborador = colaborador.codigo "
                + "INNER JOIN risco ON risco.cliente = vinculo.cliente "
                + "WHERE risco.empresa = " + empresa;
        try (Statement stm = con.createStatement()) {
            try (ResultSet tabelaResultado = stm.executeQuery(sqlStr)) {
                return (montarDadosConsulta(tabelaResultado, nivelMontarDados, this.con));
            }
        }
    }

    /**
     * tras os consultores com clientes que possuem contratos previstos para
     * renovar para o periodo informado por parametro
     *
     * @param dataInicio
     * @param dataFim
     * @param empresa
     * @return qtde
     * @throws Exception
     */
    public List<ColaboradorVO> consultarColaboradorContratoPrevistoRenovar(Date dataInicio, Date dataFim, Integer empresa) throws Exception {
        StringBuilder sqlStr = new StringBuilder();
        sqlStr.append("SELECT DISTINCT(colaborador.*) FROM Contrato ");
        sqlStr.append("INNER JOIN cliente ON contrato.pessoa = cliente.pessoa ");
        sqlStr.append("INNER JOIN vinculo ON cliente.codigo = vinculo.cliente ");
        sqlStr.append("INNER JOIN colaborador ON vinculo.colaborador = colaborador.codigo ");
        sqlStr.append("LEFT JOIN ContratoOperacao ON contratoOperacao.contrato = contrato.codigo and contratoOperacao.tipoOperacao <> 'CA' ");
        sqlStr.append("WHERE dataprevistarenovar >= '" + Uteis.getDataJDBC(dataInicio) + "' and ");
        sqlStr.append("      dataprevistarenovar <= '" + Uteis.getDataJDBC(dataFim) + "' and ");
        sqlStr.append("      contrato.empresa = " + empresa.intValue() + " and ");
        sqlStr.append("      contrato.bolsa = false and (contrato.situacao = 'AT' or contrato.situacao = 'IN')");
        try (Statement stm = con.createStatement()) {
            try (ResultSet tabelaResultado = stm.executeQuery(sqlStr.toString())) {
                return montarDadosConsulta(tabelaResultado, Uteis.NIVELMONTARDADOS_INDICERENOVACAO, this.con);
            }
        }
    }

    /**
     * Responsável por montar os dados de vários objetos, resultantes de uma
     * consulta ao banco de dados (
     * <code>ResultSet</code>). Faz uso da operação
     * <code>montarDados</code> que realiza o trabalho para um objeto por vez.
     *
     * @return List Contendo vários objetos da classe <code>ColaboradorVO</code>
     * resultantes da consulta.
     */
    public static List<ColaboradorVO> montarDadosConsulta(ResultSet tabelaResultado, int nivelMontarDados, Connection con) throws Exception {
        List<ColaboradorVO> vetResultado = new ArrayList<ColaboradorVO>();
        while (tabelaResultado.next()) {
            ColaboradorVO obj = montarDados(tabelaResultado, nivelMontarDados, con);
            vetResultado.add(obj);
        }
        return vetResultado;
    }

    private static ColaboradorVO montarDadosBasico(ResultSet dadosSQL) throws Exception {
        ColaboradorVO obj = new ColaboradorVO();
        obj.setNovoObj(false);
        obj.setCodigo(dadosSQL.getInt("codigo"));
        obj.getPessoa().setCodigo(dadosSQL.getInt("pessoa"));
        obj.getEmpresa().setCodigo(dadosSQL.getInt("empresa"));
        obj.setCodAcesso(dadosSQL.getString("codAcesso"));
        obj.setSituacao(dadosSQL.getString("situacao"));
        obj.setCodAcesso(dadosSQL.getString("codAcesso"));
        obj.setCodAcessoAlternativo(dadosSQL.getString("codAcessoAlternativo"));
        obj.setFuncionario(dadosSQL.getBoolean("funcionario"));
        obj.setUaColaborador(new AcessoColaboradorVO());
        obj.getUaColaborador().setCodigo(dadosSQL.getInt("uaCodigo"));
        obj.setDiaVencimento(dadosSQL.getInt("diavencimento"));
        obj.getProdutoDefault().setCodigo(dadosSQL.getInt("produtoDefault"));
        obj.setPorcComissao(dadosSQL.getDouble("porcComissao"));
        obj.setCorAgendaProfissional(dadosSQL.getString("corAgendaProfissional"));
        obj.setCref(dadosSQL.getString("cref"));
        obj.setCodigoAfiliadoVitio(dadosSQL.getString("codigoafiliadovitio"));
        try {
            obj.setValorComissao(dadosSQL.getDouble("valorComissao"));
        } catch (Exception ignored) {
        }
        try {
            obj.setTokenGoogle(dadosSQL.getString("tokengoogle"));
            obj.setTipoColaborador(dadosSQL.getString("tipocolaborador"));
        } catch (Exception ignored) {
        }
        try {
            obj.setBloquearAcessoSemCheckin(dadosSQL.getBoolean("bloquearAcessoSemCheckin"));
        } catch (Exception ignored) {
        }
        try {
            obj.setUsoCreditosPersonal(dadosSQL.getInt("usoCreditosPersonal"));
            obj.setSaldoCreditoPersonal(dadosSQL.getInt("saldoCreditoPersonal"));
            obj.setEmAtendimentoPersonal(dadosSQL.getBoolean("emAtendimentoPersonal"));
        } catch (Exception ignored) {
        }
        try {
            obj.setTempoEntreAcessos(dadosSQL.getInt("tempoentreacessos"));
            obj.getDepartamentoVO().setCodigo(dadosSQL.getInt("departamento"));
            obj.setEmailMovidesk(dadosSQL.getString("email_movidesk"));
            obj.setSincronizadoMovidesk(dadosSQL.getBoolean("sincronizado_movidesk"));
            obj.setCargaHoraria(dadosSQL.getInt("cargahoraria"));
            obj.setValidadeCref(dadosSQL.getTimestamp("validadeCref"));
            obj.setPorcComissaoIndicacaoEstudio(dadosSQL.getDouble("porcComissaoIndicacaoEstudio"));
            obj.setPermitirAcessoRedeEmpresa(dadosSQL.getBoolean("permitirAcessoRedeEmpresa"));
        } catch (Exception ignored) {
        }
        try {
            obj.setSincronizadoRedeEmpresa(dadosSQL.getTimestamp("sincronizadoRedeEmpresa"));
        }catch (Exception ignore){}
        try {
            obj.setPublicIdProfessorMgb(dadosSQL.getString("publicIdProfessorMgb"));
        }catch (Exception ignore){}

        return obj;
    }

    /**
     * Responsável por montar os dados resultantes de uma consulta ao banco de
     * dados (
     * <code>ResultSet</code>) em um objeto da classe
     * <code>ColaboradorVO</code>.
     *
     * @return O objeto da classe <code>ColaboradorVO</code> com os dados
     * devidamente montados.
     */
    public static ColaboradorVO montarDados(ResultSet dadosSQL, int nivelMontarDados, Connection con) throws Exception {
        if (nivelMontarDados == Uteis.NIVELMONTARDADOS_CONSULTA_WS ||
                nivelMontarDados == Uteis.NIVELMONTARDADOS_CONSULTA_WS_COM_BIOMETRIAS) {
            ColaboradorVO obj = new ColaboradorVO();
            obj.setNovoObj(false);
            obj.setCodigo(dadosSQL.getInt("codigo"));
            obj.getPessoa().setCodigo(dadosSQL.getInt("pessoa"));
            obj.setCodAcesso(dadosSQL.getString("codAcesso"));
            obj.setCodAcessoAlternativo(dadosSQL.getString("codAcessoAlternativo"));
            obj.setTempoEntreAcessos(dadosSQL.getInt("tempoentreacessos"));
            try {
                obj.setSituacao(dadosSQL.getString("situacao"));
            } catch (Exception ignored) {}
            try {
                obj.setTipoColaborador(dadosSQL.getString("tipocolaborador"));
            } catch (Exception ignored) {}
            montarDadosPessoa(obj, nivelMontarDados, con);
            return obj;
        }
        ColaboradorVO obj = montarDadosBasico(dadosSQL);

        if (nivelMontarDados == Uteis.NIVELMONTARDADOS_MINIMOS) {
            montarDadosPessoa(obj, Uteis.NIVELMONTARDADOS_MINIMOS, con);
            return obj;
        }

        if (nivelMontarDados == Uteis.NIVELMONTARDADOS_LOGIN) {
            montarDadosPessoa(obj, Uteis.NIVELMONTARDADOS_MINIMOS, con);
            return obj;
        }

        if (nivelMontarDados == Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA) {
            return obj;
        }

        if (nivelMontarDados == Uteis.NIVELMONTARDADOS_INDICERENOVACAO) {
            montarDadosPessoa(obj, Uteis.NIVELMONTARDADOS_ROBO, con);
            montarDadosComissao(obj, Uteis.NIVELMONTARDADOS_DADOSBASICOS, con);
            return obj;
        }
        if (nivelMontarDados == Uteis.NIVELMONTARDADOS_CAIXAPARAOPERADOR) {
            montarDadosPessoa(obj, Uteis.NIVELMONTARDADOS_ROBO, con);
            Vinculo vinculo = new Vinculo(con);
            obj.setListaVinculos(vinculo.consultarVinculoPorCodigoColaborador(
                    obj.getCodigo(),"", Uteis.NIVELMONTARDADOS_CAIXAPARAOPERADOR));
            vinculo = null;
            return obj;
        }
        obj.setColaboradorEscolhidoIndiceConversao(false);
        obj.setColaboradorEscolhidoPendencia(false);
        TipoColaborador tipoColaborador = new TipoColaborador(con);
        obj.setListaTipoColaboradorVOs(tipoColaborador.consultarTipoColaborador(obj.getCodigo(), nivelMontarDados));
        tipoColaborador = null;
        if (nivelMontarDados == Uteis.NIVELMONTARDADOS_ORGANIZADORCARTEIRA) {
            montarDadosPessoa(obj, Uteis.NIVELMONTARDADOS_DADOSBASICOS, con);
            return obj;
        }

        montarDadosComissao(obj, Uteis.NIVELMONTARDADOS_DADOSBASICOS, con);
        montarDadosProduto(obj, Uteis.NIVELMONTARDADOS_DADOSBASICOS, con);
        montarDadosColaboradorModalidade(obj, Uteis.NIVELMONTARDADOS_DADOSBASICOS, con);
        if (nivelMontarDados == Uteis.NIVELMONTARDADOS_DADOSBASICOS) {
            montarDadosPessoa(obj, Uteis.NIVELMONTARDADOS_DADOSBASICOS, con);
            return obj;
        }
        if (nivelMontarDados == Uteis.NIVELMONTARDADOS_DADOSENTIDADESUBORDINADAS) {
            montarDadosPessoa(obj, Uteis.NIVELMONTARDADOS_DADOSENTIDADESUBORDINADAS, con);
            return obj;
        }
        montarDadosPessoa(obj, Uteis.NIVELMONTARDADOS_TODOS, con);
        montarDadosEmpresa(obj, Uteis.NIVELMONTARDADOS_DADOSBASICOS, con);
        return obj;
    }

    /**
     * Operação responsável por montar os dados de um objeto da classe
     * <code>PessoaVO</code> relacionado ao objeto
     * <code>ColaboradorVO</code>. Faz uso da chave primária da classe
     * <code>PessoaVO</code> para realizar a consulta.
     *
     * @param obj Objeto no qual será montado os dados consultados.
     */
    public static void montarDadosPessoa(ColaboradorVO obj, int nivelMontarDados, Connection con) throws Exception {
        if (obj.getPessoa().getCodigo() == 0) {
            obj.setPessoa(new PessoaVO());
            return;
        }
        Pessoa pessoa = new Pessoa(con);
        obj.setPessoa(pessoa.consultarPorChavePrimaria(obj.getPessoa().getCodigo(), nivelMontarDados));
        //não se pode preencher a foto num método genérico desse! isso consome muita memória desnecessária
        //obj.getPessoa().setFoto(new Pessoa(con).obterFoto(obj.getPessoa().getCodigo()));
        pessoa = null;
    }

    public static void montarDadosProduto(ColaboradorVO obj, int nivelMontarDados, Connection con) throws Exception {
        if (obj.getProdutoDefault().getCodigo() == 0) {
            obj.setProdutoDefault(new ProdutoVO());
        } else {
            Produto produto = new Produto(con);
            try {
                obj.setProdutoDefault(produto.consultarPorChavePrimaria(obj.getProdutoDefault().getCodigo(), nivelMontarDados));
            } catch (ConsistirException e) {
                obj.setProdutoDefault(new ProdutoVO());
            }
        }

    }

    /**
     * Operação responsável por montar os dados de um objeto da classe
     * <code>PessoaVO</code> relacionado ao objeto
     * <code>ColaboradorVO</code>. Faz uso da chave primária da classe
     * <code>PessoaVO</code> para realizar a consulta.
     *
     * @param obj Objeto no qual será montado os dados consultados.
     */
    public static void montarDadosEmpresa(ColaboradorVO obj, int nivelMontarDados, Connection con) throws Exception {
        if (obj.getEmpresa().getCodigo() == 0) {
            obj.setEmpresa(new EmpresaVO());
            return;
        }
        Empresa empresa = new Empresa(con);
        obj.setEmpresa(empresa.consultarPorChavePrimaria(obj.getEmpresa().getCodigo(), nivelMontarDados));
        empresa = null;
    }

    public static void montarDadosComissao(ColaboradorVO obj, int nivelMontarDados, Connection con) throws Exception {
        ModalidadeComissaoColaborador modalidadeComissaoColaborador =
                new ModalidadeComissaoColaborador(con);
        obj.setListaModalidadesComissaoColaboradorVO(
                modalidadeComissaoColaborador.consultarModalidadeComissao(obj.getCodigo().intValue(), Uteis.NIVELMONTARDADOS_DADOSBASICOS));
        modalidadeComissaoColaborador = null;

        TurmaComissaoColaborador turmaComissaoColaborador =
                new TurmaComissaoColaborador(con);
        obj.setListaTurmaComissaoColaboradorVO(
                turmaComissaoColaborador.consultarTurmaComissao(obj.getCodigo().intValue(), Uteis.NIVELMONTARDADOS_DADOSBASICOS));
        turmaComissaoColaborador = null;

        AlunoComissaoColaborador alunoComissaoColaborador =
                new AlunoComissaoColaborador(con);
        obj.setListaAlunoComissaoColaboradorVOs(alunoComissaoColaborador.consultarAlunoComissao(obj.getCodigo().intValue(), Uteis.NIVELMONTARDADOS_DADOSBASICOS));
        alunoComissaoColaborador = null;
    }


    public static void montarDadosColaboradorModalidade(ColaboradorVO obj, int nivelMontarDados, Connection con) throws Exception {
        ColaboradorModalidade colaboradorModalidadeDao = new ColaboradorModalidade(con);
        obj.setColaboradorModalidadeVOS(colaboradorModalidadeDao.consultarPorColaborador(obj.getCodigo(), nivelMontarDados));
    }


    /**
     * Operação responsável por localizar um objeto da classe
     * <code>ColaboradorVO</code> através de sua chave primária.
     *
     * @exception Exception Caso haja problemas de conexão ou localização do
     * objeto procurado.
     */
    public ColaboradorVO consultarPorChavePrimaria(Integer codigoPrm, int nivelMontarDados) throws Exception {
        consultar(getIdEntidade(), false);
        ColaboradorVO eCache = (ColaboradorVO) obterFromCache(codigoPrm);
        if (eCache != null){
            return eCache;
        }
        String sql = "SELECT * FROM Colaborador WHERE codigo = ?";
        try (PreparedStatement sqlConsultar = con.prepareStatement(sql)) {
            sqlConsultar.setInt(1, codigoPrm.intValue());
            try (ResultSet tabelaResultado = sqlConsultar.executeQuery()) {
                if (!tabelaResultado.next()) {
                    throw new ConsistirException("Dados Não Encontrados ( Colaborador ).");
                }
                eCache = (montarDados(tabelaResultado, nivelMontarDados, this.con));
            }
        }
        putToCache(eCache);
        return eCache;
    }

    public void registrarUltimoAcesso(int codigoColaborador, int codigoAcessoColaborador) throws Exception {
        alterar(getIdEntidade());
        String sql = "UPDATE Colaborador set uaCodigo =? WHERE (codigo = ?)";
        try (PreparedStatement sqlAlterar = con.prepareStatement(sql)) {
            sqlAlterar.setInt(1, codigoAcessoColaborador);
            sqlAlterar.setInt(2, codigoColaborador);
            sqlAlterar.execute();
        }
    }

    public List consultarColaboradorResponsavelPorMatriculaRematricula(Date prmIni, Date prmFim, Integer empresa, boolean controlarAcesso, int nivelMontarDados) throws Exception {
        consultar(getIdEntidade(), controlarAcesso);
        String sqlStr = "";
        if (empresa == 0) {
            sqlStr = "select Distinct(Colaborador.codigo), colaborador.* from contrato , cliente, vinculo, colaborador where (contrato.situacaoContrato = 'MA' or contrato.situacaoContrato = 'RE') "
                    + "and ((contrato.vigenciaDe >= '2009-09-01') and ( contrato.vigenciaDe <= '2009-09-05')) and cliente.pessoa = contrato.pessoa and vinculo.cliente =  cliente.codigo "
                    + "and colaborador.codigo =  vinculo.colaborador and vinculo.tipoVinculo = 'CO'";
        } else {
            sqlStr = "select Distinct(Colaborador.codigo), colaborador.* from contrato , cliente, vinculo, colaborador where (contrato.situacaoContrato = 'MA' or contrato.situacaoContrato = 'RE') "
                    + "and ((contrato.vigenciaDe >= '" + Uteis.getDataJDBC(prmIni) + "') and ( contrato.vigenciaDe <= '" + Uteis.getDataJDBC(prmFim) + "')) and cliente.pessoa = contrato.pessoa and vinculo.cliente =  cliente.codigo "
                    + "and colaborador.codigo =  vinculo.colaborador and vinculo.tipoVinculo = 'CO' and contrato.empresa =" + empresa.intValue();
        }
        try (Statement stm = con.createStatement()) {
            try (ResultSet tabelaResultado = stm.executeQuery(sqlStr)) {
                return (montarDadosConsulta(tabelaResultado, nivelMontarDados, this.con));
            }
        }
    }

    /**
     * Responsável por realizar uma consulta de
     * <code>Cliente</code> através do valor do atributo
     * <code>String codAlternativo</code>. Retorna os objetos, com início do
     * valor do atributo idêntico ao parâmetro fornecido. Faz uso da operação
     * <code>montarDadosConsulta</code> que realiza o trabalho de prerarar o
     * List resultante.
     *
     * @param controlarAcesso Indica se a aplicação deverá verificar se o
     * usuário possui permissão para esta consulta ou não.
     * @return List Contendo vários objetos da classe <code>ClienteVO</code>
     * resultantes da consulta.
     * @exception Exception Caso haja problemas de conexão ou restrição de
     * acesso.
     */
    @Override
    public List consultarPorCodAlternativo(String valorConsulta, Integer empresa, boolean controlarAcesso, int nivelMontarDados) throws Exception {
        consultar(getIdEntidade(), controlarAcesso);
        String sqlStr = "";
        if (empresa == 0) {
            sqlStr = "SELECT * FROM Colaborador WHERE upper( codAcessoAlternativo ) like('" + valorConsulta.toUpperCase() + "%') ORDER BY codAcessoAlternativo";
        } else {
            sqlStr = "SELECT * FROM Colaborador WHERE upper( codAcessoAlternativo ) like('" + valorConsulta.toUpperCase() + "%') and Colaborador.empresa =" + empresa.intValue() + " ORDER BY codAcessoAlternativo";
        }
        try (Statement stm = con.createStatement()) {
            try (ResultSet tabelaResultado = stm.executeQuery(sqlStr)) {
                return (montarDadosConsulta(tabelaResultado, nivelMontarDados, this.con));
            }
        }
    }

    /*author   : Ulisses
     * Data    : 16/02/11
     * Objetivo: Consultar um colaborador pelo código.
     */
    public ColaboradorVO consultarPorCodigo(Integer valorConsulta, Integer empresa, int nivelMontarDados) throws Exception {
        String sqlStr = "";
        if (empresa == 0) {
            sqlStr = "SELECT * FROM Colaborador WHERE codigo = " + valorConsulta.intValue();
        } else {
            sqlStr = "SELECT * FROM Colaborador WHERE codigo = " + valorConsulta.intValue() + " and Colaborador.empresa = " + empresa.intValue();
        }
        ColaboradorVO colaborador;
        try (Statement stm = con.createStatement()) {
            try (ResultSet tabelaResultado = stm.executeQuery(sqlStr)) {
                colaborador = null;
                if (tabelaResultado.next()) {
                    colaborador = montarDados(tabelaResultado, nivelMontarDados, this.con);
                }
            }
        }
        return colaborador;
    }

    /*author   : Ulisses
     * Data    : 16/02/11
     * Objetivo: Consultar um colaborador pelo código de acesso.
     */
    public ColaboradorVO consultarPorCodAcesso(String valorConsulta, Integer empresa, int nivelMontarDados) throws Exception {
        ColaboradorVO colaborador = null;
        String sqlStr = "";
        if (empresa == 0) {
            sqlStr = "SELECT * FROM Colaborador WHERE codAcesso ='" + valorConsulta.toUpperCase() + "'";
        } else {
            sqlStr = "SELECT * FROM Colaborador WHERE codAcesso ='" + valorConsulta.toUpperCase() + "' and Colaborador.empresa =" + empresa.intValue();
        }
        try (Statement stm = con.createStatement()) {
            try (ResultSet resultDados = stm.executeQuery(sqlStr)) {
                if (resultDados.next()) {
                    colaborador = montarDados(resultDados, nivelMontarDados, this.con);
                }
            }
        }

        return colaborador;
    }

    /*author   : Ulisses
     * Data    : 16/02/11
     * Objetivo: Consultar um colaborador pelo código alternativo.
     */
    public ColaboradorVO consultarPorCodAlternativo(String valorConsulta, Integer empresa, int nivelMontarDados) throws Exception {
        ColaboradorVO colaborador = null;
        String sqlStr = "";
        if (empresa == 0) {
            sqlStr = "SELECT * FROM Colaborador WHERE codAcessoAlternativo ='" + valorConsulta.toUpperCase() + "'";
        } else {
            sqlStr = "SELECT * FROM Colaborador WHERE codAcessoAlternativo ='" + valorConsulta.toUpperCase() + "' and Colaborador.empresa =" + empresa.intValue();
        }
        try (Statement stm = con.createStatement()) {
            try (ResultSet resultDados = stm.executeQuery(sqlStr)) {
                if (resultDados.next()) {
                    colaborador = montarDados(resultDados, nivelMontarDados, this.con);
                }
            }
        }

        return colaborador;
    }

    public List consultarPorCodigoTurmaEOuModalidade(Integer turma, Integer modalidade, Integer empresa, int nivelMontarDados) throws Exception {
        StringBuilder sql = new StringBuilder("SELECT\n"
                + "  DISTINCT col.*,\n"
                + "  pes.nome\n"
                + "FROM colaborador col\n"
                + "  INNER JOIN horarioturma ht\n"
                + "    ON ht.professor = col.codigo\n"
                + "  INNER JOIN turma tur\n"
                + "    ON tur.codigo = ht.turma\n"
                + "  INNER JOIN pessoa pes\n"
                + "    ON pes.codigo = col.pessoa\n");

        if (modalidade != 0) {
            sql.append("  INNER JOIN modalidade mod\n"
                    + "    ON mod.codigo = tur.modalidade\n");
        }

        if (empresa != 0) {
            sql.append("  INNER JOIN empresa emp\n"
                    + "    ON emp.codigo = col.empresa\n");
        }

        sql.append("WHERE 1 = 1\n");

        if (turma != 0) {
            sql.append("   AND tur.codigo = ").append(turma).append("\n");
        }

        if (modalidade != 0) {
            sql.append("   AND mod.codigo = ").append(modalidade).append("\n");
        }

        if (empresa != 0) {
            sql.append("   AND emp.codigo = ").append(empresa).append("\n");
        }

        sql.append("AND (col.situacao = 'AT' AND ht.situacao = 'AT')\n");
        sql.append("      OR col.codigo IN\n");
        sql.append("         (SELECT\n");
        sql.append("            DISTINCT ht.professor\n");
        sql.append("          FROM horarioturma ht\n");
        sql.append("            INNER JOIN turma t\n");
        sql.append("              ON ht.turma = t.codigo\n");
        if (empresa != 0) {
            sql.append("              AND t.empresa = ").append(empresa).append("\n");
        }
        if (modalidade != 0) {
            sql.append("              AND t.modalidade = ").append(modalidade).append("\n");
        }
        if (turma != 0) {
            sql.append("              AND t.codigo = ").append(turma).append("\n");
        }
        sql.append("          WHERE ht.situacao = 'AT'\n");
        sql.append("                AND\n");
        sql.append("                (t.datafinalvigencia > '").append(Uteis.getDataJDBCTimestamp(Calendario.hoje())).append("'\n");
        sql.append("                 OR ht.codigo IN\n");
        sql.append("                    (SELECT\n");
        sql.append("                       DISTINCT horarioturma\n");
        sql.append("                     FROM matriculaalunohorarioturma maht\n");
        sql.append("                       INNER JOIN horarioturma ht\n");
        sql.append("                         ON ht.codigo = maht.horarioturma\n");
        sql.append("                       INNER JOIN turma\n");
        sql.append("                         ON ht.turma = turma.codigo\n");
        sql.append("                     WHERE maht.datafim > '").append(Uteis.getDataJDBCTimestamp(Calendario.hoje())).append("'\n");

        if (empresa != 0) {
            sql.append("              AND turma.empresa = ").append(empresa).append("\n");
        }
        if (modalidade != 0) {
            sql.append("              AND turma.modalidade = ").append(modalidade).append("\n");
        }
        if (turma != 0) {
            sql.append("              AND turma.codigo = ").append(turma).append("\n");
        }

        sql.append("                    )\n");
        sql.append("                )\n");
        sql.append("          ORDER BY professor\n");
        sql.append("         )");

        sql.append("ORDER BY pes.nome;\n");

        try (Statement stm = con.createStatement()) {
            try (ResultSet tabelaResultado = stm.executeQuery(sql.toString())) {
                return (montarDadosConsulta(tabelaResultado, nivelMontarDados, this.con));
            }
        }
    }

    public Double getPorcComissao(int aluno, int modalidade, int turma, int colaborador) throws Exception {
        Double porc = null;
        StringBuilder sql = new StringBuilder();
        sql.append(" SELECT * FROM ( ").append(" SELECT 1 as ordem, porccomissao FROM alunocomissaocolaborador where pessoa = " + aluno + " and colaborador = " + colaborador + " ").append("	UNION ALL ").append(" SELECT 2 as ordem, porccomissao FROM turmacomissaocolaborador  where turma = " + turma + " and colaborador = " + colaborador + " ").append(" 		UNION ALL ").append(" SELECT 3 as ordem, porccomissao FROM modalidadecomissaocolaborador  where modalidade = " + modalidade + " and colaborador = " + colaborador + " ").append(" ) AS consulta ORDER BY ordem ");

        try (ResultSet query = con.prepareStatement(sql.toString()).executeQuery()) {
            if (query.next()) {
                porc = query.getDouble("porccomissao");

            }
        }
        return porc;

    }

    public Double getValorComissao(int aluno, int modalidade, int turma, int colaborador) throws Exception {
        Double porc = null;
        StringBuilder sql = new StringBuilder();
        sql.append(" SELECT * FROM ( ").append(" SELECT 1 as ordem, valorcomissao FROM alunocomissaocolaborador where pessoa = " + aluno + " and colaborador = " + colaborador + " ").append("	UNION ALL ").append(" SELECT 2 as ordem, valorcomissao FROM turmacomissaocolaborador  where turma = " + turma + " and colaborador = " + colaborador + " ").append(" 		UNION ALL ").append(" SELECT 3 as ordem, valorcomissao FROM modalidadecomissaocolaborador  where modalidade = " + modalidade + " and colaborador = " + colaborador + " ").append(" ) AS consulta ORDER BY ordem ");

        try (ResultSet query = con.prepareStatement(sql.toString()).executeQuery()) {
            if (query.next()) {
                porc = query.getDouble("valorcomissao");

            }
        }
        return porc;

    }

    @Override
    public ColaboradorVO consultarConsultorDoClienteNaData(int cliente, Date data) throws Exception {
        ColaboradorVO colaborador = new ColaboradorVO();
        String sqlStr = "SELECT\n"
                + "  hv.colaborador\n"
                + "FROM public.historicovinculo hv\n"
                + "WHERE tipocolaborador LIKE 'CO' AND cliente = '" + cliente + "'\n"
                + "      AND dataregistro < '" + Uteis.getDataJDBCTimestamp(data) + "' AND tipohistoricovinculo LIKE 'EN'\n"
                + "ORDER BY dataregistro DESC\n"
                + "LIMIT 1;";

        try (Statement stm = con.createStatement()) {
            try (ResultSet resultDados = stm.executeQuery(sqlStr)) {
                if (resultDados.next()) {
                    colaborador.setCodigo(resultDados.getInt(1));
                }
            }
        }
        return colaborador;
    }

    @Override
    public List<ColaboradorVO> consultarColaboradoresQuestionarios(final Date prmIni, final Date prmFim,
            Integer empresa, boolean controlarAcesso, int nivelMontarDados) throws Exception {
        consultar(getIdEntidade(), controlarAcesso);
        String sqlStr = "";
        if (empresa == 0) {
            sqlStr = "SELECT * FROM Colaborador where codigo in ("
                    + "SELECT distinct QuestionarioCliente.consultor FROM QuestionarioCliente WHERE ((data >= '" + Uteis.getDataJDBC(prmIni) + " 00:00:00') and (data <= '" + Uteis.getDataJDBC(prmFim) + " 23:59:59')) ORDER BY consultor)";
        } else {
            sqlStr = "SELECT * FROM Colaborador where codigo in ( "
                    + "SELECT distinct QuestionarioCliente.consultor FROM QuestionarioCliente, Cliente WHERE ((data >= '" + Uteis.getDataJDBC(prmIni) + " 00:00:00') and (data <= '" + Uteis.getDataJDBC(prmFim) + " 23:59:59'))and QuestionarioCliente.cliente = cliente.codigo and cliente.empresa = " + empresa.intValue() + " ORDER BY consultor)";
        }
        try (Statement stm = con.createStatement()) {
            try (ResultSet tabelaResultado = stm.executeQuery(sqlStr)) {
                return (montarDadosConsulta(tabelaResultado, nivelMontarDados, con));
            }
        }
    }

    public String obterNomePessoa(final int codColaborador) throws Exception {
        try (ResultSet rsNome = con.createStatement().executeQuery(
                "select nome from pessoa p inner join colaborador c on c.pessoa = p.codigo and c.codigo = " + codColaborador)) {
            if (rsNome.next()) {
                return rsNome.getString(1);
            } else {
                return "";
            }
        }
    }

    public List<ColaboradorVO> consultarPorNomeTipoVinculoPossivel(String nomeColaborador, TipoColaboradorEnum tipoColaborador, Integer codEmpresa, int nivelMontarDados) throws Exception {
        StringBuilder sql = new StringBuilder();
        sql.append("SELECT DISTINCT col.*, pes.nome\n");
        sql.append("FROM colaborador col\n");
        sql.append("         LEFT JOIN vinculo v on col.codigo = v.colaborador\n");
        if (codEmpresa > 0) {
            sql.append("        AND empresa = ").append(codEmpresa).append("\n");
        }

        sql.append("         INNER JOIN tipocolaborador tc on col.codigo = tc.colaborador\n");
        if (codEmpresa > 0) {
            sql.append("        AND empresa = ").append(codEmpresa).append("\n");
        }

        sql.append("         INNER JOIN pessoa pes on col.pessoa = pes.codigo\n");
        if (!UteisValidacao.emptyString(nomeColaborador)) {
            sql.append("      AND pes.nome like '%").append(nomeColaborador.toUpperCase()).append("%'\n");
        }
        sql.append("WHERE (tc.descricao = '").append(tipoColaborador.getSigla()).append("' or v.tipovinculo = '").append(tipoColaborador.getSigla()).append("')\n");
        sql.append("  AND situacao = 'AT';");

        try (PreparedStatement ps = con.prepareStatement(sql.toString())) {
            try (ResultSet rs = ps.executeQuery()) {
                List<ColaboradorVO> colaboradores = montarDadosConsulta(rs, nivelMontarDados, con);
                Ordenacao.ordenarLista(colaboradores, "pessoa_Apresentar");
                return colaboradores;
            }
        }
    }

    public String consultarJSON(Integer empresa, String sEcho, Integer offset, Integer limit, String clausulaLike,
                                Integer colOrdenar, String dirOrdenar, String situacao,String tipoColaborador) throws Exception {
        StringBuilder sqlCount = new StringBuilder("SELECT count(codigo) +1 FROM colaborador col");
        StringBuilder json;
        boolean dados;
        try (PreparedStatement ps = getRS(empresa, offset, limit, colOrdenar, dirOrdenar, situacao, clausulaLike, tipoColaborador, false)) {
            try (ResultSet rs = ps.executeQuery()) {
                StringBuilder sqlContarFiltrados = new StringBuilder("SELECT count(DISTINCT(col.codigo))\n");
                sqlContarFiltrados.append("FROM colaborador col\n");
                sqlContarFiltrados.append("INNER JOIN pessoa p ON col.pessoa = p.codigo\n");
                sqlContarFiltrados.append("left  JOIN empresa emp ON col.empresa = emp.codigo\n");
                sqlContarFiltrados.append("left join tipocolaborador tc on tc.colaborador = col.codigo\n");
                sqlContarFiltrados.append(" WHERE  1 = 1\n");
                if (!situacao.equals("TD")) {
                    sqlContarFiltrados.append(" and col.situacao = '").append(situacao).append("' \n");
                }
                if (!tipoColaborador.equals("TD")) {
                    sqlContarFiltrados.append(" and tc.descricao = '").append(tipoColaborador).append("' \n");
                }
                if (empresa != null && empresa != 0) {
                    sqlContarFiltrados.append(" and col.empresa = ").append(empresa);
                }
                if (!UteisValidacao.emptyString(clausulaLike)) {
                    sqlContarFiltrados.append(" AND (");
                    sqlContarFiltrados.append("lower(col.codigo::VARCHAR) ~ '").append(clausulaLike).append("' OR\n");
                    sqlContarFiltrados.append("lower(p.nome) ~ '").append(clausulaLike).append("' OR\n");
                    sqlContarFiltrados.append("lower(col.situacao) ~ '").append(clausulaLike).append("' OR\n");
                    sqlContarFiltrados.append("lower((SELECT array(SELECT descricao FROM tipocolaborador tc  WHERE tc.colaborador = col.codigo))::text) ~ '").append(clausulaLike).append("' OR\n");
                    sqlContarFiltrados.append("lower(emp.nome) ~ '").append(clausulaLike).append("'\n");
                    sqlContarFiltrados.append(")");
                }

                ColaboradorVO colab = new ColaboradorVO();
                String listaTipo;
                json = new StringBuilder();
                json.append("{");
                json.append("\"iTotalRecords\":\"").append(contar(sqlCount.toString(), getCon())).append("\",");
                json.append("\"iTotalDisplayRecords\":\"").append(contar(sqlContarFiltrados.toString(), getCon())).append("\",");
                json.append("\"sEcho\":\"").append(sEcho).append("\",");
                json.append("\"aaData\":[");

                dados = false;
                while (rs.next()) {
                    dados = true;
                    json.append("[\"").append(rs.getString("codigo")).append("\",");
                    json.append("\"").append(Uteis.normalizarStringJSON(rs.getString("nome"))).append("\",");
                    json.append("\"").append(rs.getDate("datanasc")).append("\",");
                    colab.setSituacao(rs.getString("situacao"));
                    json.append("\"").append(colab.getSituacao_Apresentar()).append("\",");

                    listaTipo = TipoColaboradorVO.listaTipo_Apresentar(rs.getString("tipo").replaceAll("\\{", "").replaceAll("\\}", ""));
                    json.append("\"").append(listaTipo).append("\",");
                    json.append("\"").append(Uteis.normalizarStringJSON(rs.getString("empresa"))).append("\"],");
                }
            }
        }
        if (dados) {
            json.deleteCharAt(json.toString().length() - 1);
        }
        json.append("]}");
        return json.toString();
    }

    @Override
    public void validarCodAcessoAlternativo(final ColaboradorVO obj) throws Exception {
        if (!obj.getCodAcessoAlternativo().isEmpty()) {
            int codigoCliente = getFacade().getPessoa().obterCodigoClienteComCodigoAlternativo(obj.getCodAcessoAlternativo());
            int codigoColab = getFacade().getPessoa().obterCodigoColaboradorComCodigoAlternativo(obj.getCodAcessoAlternativo());
            if (codigoCliente > 0) {
                throw new ConsistirException("Já existe um cliente com o mesmo Código Acesso Alternativo deste Colaborador!");
            }
            if (codigoColab > 0 && codigoColab != obj.getCodigo().intValue()) {
                ColaboradorVO colaborador = consultarColaboradorPorCodigo(codigoColab,0,true,Uteis.NIVELMONTARDADOS_DADOSBASICOS);
                if (colaborador.getPessoa().equals(obj.getPessoa().getCodigo())) {
                    throw new ConsistirException("Já existe um colaborador com o mesmo Código Acesso Alternativo!");
                }
            }
        }
    }

    public List consultarParaImpressao(String filtro, String ordem, String campoOrdenacao, Integer empresa,String situacao,String tipoColaborador) throws SQLException {
        //        cli.codigo, p.nome, p.datanasc, situacao, matricula, cat.nome AS categoria, emp.nome AS empresa
        List lista;
        try (PreparedStatement ps = getRS(empresa, 1, 0, 1, campoOrdenacao, situacao, filtro, tipoColaborador, true)) {
            try (ResultSet rs = ps.executeQuery()) {
                lista = new ArrayList();

                while (rs.next()) {
                    //col.codigo, p.nome, p.datanasc, col.situacao, emp.nome AS empresa,
                    //\n" + "  (SELECT array(SELECT descricao FROM tipocolaborador tc  WHERE tc.colaborador = col.codigo)) AS tipo
                    ColaboradorVO colab = new ColaboradorVO();
                    colab.setSituacao(rs.getString("situacao"));
                    colab.setDepartamentoApresentar(rs.getString("departamento"));
                    String geral = rs.getString("codigo") + rs.getString("nome") + rs.getString("datanasc")
                            + colab.getSituacao_Apresentar() + rs.getString("tipo") + rs.getString("empresa") + TipoColaboradorVO.listaTipo_Apresentar(rs.getString("tipo").replaceAll("\\{", "").replaceAll("\\}", ""));
                    if (geral.toLowerCase().contains(filtro.toLowerCase())) {

                        colab.setCodigo(rs.getInt("codigo"));
                        colab.getPessoa().setNome(rs.getString("nome"));
                        colab.getPessoa().setDataNasc(rs.getDate("datanasc"));
                        colab.setSituacao(rs.getString("situacao"));
                        colab.getEmpresa().setNome(rs.getString("empresa"));
                        String listaTipo = TipoColaboradorVO.listaTipo_Apresentar(rs.getString("tipo").replaceAll("\\{", "").replaceAll("\\}", ""));
                        colab.setTipoColaborador(listaTipo);
                        lista.add(colab);
                    }
                }
            }
        }
        if (campoOrdenacao.equals("Código")) {
            Ordenacao.ordenarLista(lista, "codigo");
        } else if (campoOrdenacao.equals("Nome")) {
            Ordenacao.ordenarLista(lista, "pessoa_Apresentar");
        } else if (campoOrdenacao.equals("Data de Nascimento")) {
            Ordenacao.ordenarLista(lista, "pessoa_DataNascimento");
        } else if (campoOrdenacao.equals("Situação")) {
            Ordenacao.ordenarLista(lista, "situacao_Apresentar");
        } else if (campoOrdenacao.equals("Empresa")) {
            Ordenacao.ordenarLista(lista, "empresa_Apresentar");
        }
        if (ordem.contains("desc")) {
            Collections.reverse(lista);
        }
        return lista;

    }

    public List<GenericoTO> consultarSimplesPorTipo(TipoColaboradorEnum tipo) throws Exception {
        return consultarSimples("SELECT p.nome as label, c.codigo FROM colaborador c"
                + " INNER JOIN pessoa p ON p.codigo = c.pessoa "
                + " INNER JOIN tipocolaborador t ON t.colaborador = c.codigo AND t.descricao = '" + tipo.getSigla() + "'"
                + " ORDER BY p.nome ", con);
    }

    public String consultarTokenGoogle(Integer codColaborador) throws Exception {
        String sql = "SELECT tokengoogle FROM colaborador WHERE codigo = " + codColaborador;
        try (PreparedStatement ps = con.prepareStatement(sql)) {
            try (ResultSet rs = ps.executeQuery()) {
                if (rs.next()) {
                    return rs.getString("tokengoogle");
                }
            }
        }
        return "";
    }

    /**
     * consulta os aniversariantes de hoje
     *
     * @return
     */
    public ResultSet contarAniversarioColaborador(int empresa, Date dataBaseInicio, Date dataBase) throws Exception {
        consultar(getIdEntidade(), false);

        int dia = Uteis.obterDiaData(dataBase);
        int mes = Uteis.getMesData(dataBase);
        String sqlStr = "SELECT COUNT(colaborador.codigo) AS qtd FROM colaborador "
                + " INNER JOIN pessoa ON pessoa.codigo = colaborador.pessoa "
                + " WHERE DATE_PART('MONTH',pessoa.datanasc) = " + mes
                + " AND DATE_PART('DAY',pessoa.datanasc) = " + dia;

        if(empresa != 0){
            sqlStr += " AND colaborador.empresa = " + empresa;
        }

        if (dataBaseInicio != null) {
            sqlStr += " AND pessoa.datacadastro >= '" + Uteis.getDataJDBC(dataBaseInicio) + " 00:00:00' \n";
        }

        Statement stm = con.createStatement();
        return stm.executeQuery(sqlStr);
    }

    public ResultSet consultarAniversarioColaborador(int empresa,ConfPaginacao paginacao, Date dataBaseInicio, Date dataBase) throws Exception {
        int qtde = 0;
        consultar(getIdEntidade(), false);
        int dia = Uteis.obterDiaData(dataBase);
        int mes = Uteis.getMesData(dataBase);
        consultar(getIdEntidade(), false);
        StringBuilder strSql = new StringBuilder();
        strSql.append("SELECT col.codigo AS codigoColaborador, col.pessoa,p.nome as nome,col.situacao,p.cfp AS cpf\n");
        strSql.append(" FROM Colaborador col ");
        strSql.append(" LEFT JOIN Pessoa p ON col.pessoa = p.codigo\n");
        strSql.append(" WHERE DATE_PART('MONTH',p.datanasc) = " + mes);
        strSql.append(" AND (DATE_PART('DAY',p.datanasc) = ?");
        strSql.append((Calendar.DAY_OF_WEEK == Calendar.MONDAY ? " OR  DATE_PART('DAY',p.datanasc) = " + (dia - 1) : ""));
        strSql.append(")");
        if(empresa != 0){
            strSql.append(" AND " + "col.empresa = ?");
        }
        if(paginacao != null && paginacao.getOrdernar()){
            strSql.append(" ORDER BY ").append(paginacao.getColunaOrdenacao()).append(" ").append(paginacao.getDirecaoOrdenacao());
        }
        if(paginacao != null && paginacao.isExistePaginacao()){
            strSql.append(" LIMIT ").append(paginacao.getItensPorPagina()).append(" OFFSET ").append((paginacao.getPaginaAtual() * paginacao.getItensPorPagina()));
        }
        PreparedStatement sql = con.prepareStatement(strSql.toString());
        sql.setInt(1,dia);
        if(empresa != 0){
            sql.setInt(2, empresa);
        }
        return sql.executeQuery();
    }

    public void atualizarSaldoPersonal(Integer codigoColaborador, Integer saldo) throws Exception {
        executarConsulta("UPDATE colaborador SET saldoCreditoPersonal = " + saldo
                + " WHERE codigo = " + codigoColaborador, con);
    }

    public void atualizarEmAtendimento(Integer codigoColaborador, boolean emAtendimento) throws Exception {
        executarConsulta("UPDATE colaborador SET emAtendimentoPersonal = " + emAtendimento
                + " WHERE codigo = " + codigoColaborador, con);
    }

    @Override
    public ColaboradorVO criarOuConsultarSeExistePorNome(ColaboradorVO obj, boolean controlarTransacao) throws Exception {
        String sql = "SELECT * FROM colaborador col "
                + "INNER JOIN pessoa pes ON col.pessoa = pes.codigo "
                + "WHERE pes.nome = ? AND col.empresa = ?";
        try (PreparedStatement sqlConsultar = con.prepareStatement(sql)) {
            sqlConsultar.setString(1, obj.getPessoa_Apresentar());
            sqlConsultar.setInt(2, obj.getEmpresa().getCodigo());
            try (ResultSet tabelaResultado = sqlConsultar.executeQuery()) {
                if (!tabelaResultado.next()) {
                    if (controlarTransacao) {
                        incluir(obj);
                    } else {
                        incluirSemCommit(obj);
                    }
                    return obj;
                } else {
                    return (montarDados(tabelaResultado, Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA, this.con));
                }
            }
        }
    }

    public byte[] obterFotoPersonal(Integer codigo) throws SQLException {
        byte[] foto;
        String sql = "SELECT fotopersonal FROM colaborador WHERE codigo = ?";
        try (PreparedStatement sqlFoto = con.prepareStatement(sql)) {
            sqlFoto.setInt(1, codigo);
            try (ResultSet rs = sqlFoto.executeQuery()) {
                if (rs.next()) {
                    foto = rs.getBytes(1);
                } else {
                    foto = null;
                }
            }
        }
        return foto;
    }
    
     public void atualizarUsuariosMoveisColaboradores() throws Exception {
         Integer codigoColaborador = 0;
        try {
            List lista = new UsuarioMovel(con).obterTodosColaboradores(false, Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA);
            String k = DAO.resolveKeyFromConnection(con);
            for (Object usu : lista){
                codigoColaborador =((UsuarioMovelVO)usu).getColaborador().getCodigo();
               ((UsuarioMovelVO)usu).setColaborador(consultarPorChavePrimaria(((UsuarioMovelVO)usu).getColaborador().getCodigo(), Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA));
               Pessoa peDao = new Pessoa(con);
               ((UsuarioMovelVO)usu).getColaborador().setPessoa(peDao.consultarPorChavePrimaria(((UsuarioMovelVO)usu).getColaborador().getPessoa().getCodigo(),Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA));
               Email emails = new Email(con);
               ((UsuarioMovelVO)usu).getColaborador().getPessoa().setEmailVOs(emails.consultarEmails(((UsuarioMovelVO)usu).getColaborador().getPessoa().getCodigo(), false, Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA));
                TipoColaborador tipoColaborador = new TipoColaborador(con);
                ((UsuarioMovelVO)usu).getColaborador().setListaTipoColaboradorVOs(tipoColaborador.consultarTipoColaborador(((UsuarioMovelVO)usu).getColaborador().getCodigo(),Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA));
                 TreinoWSConsumer.sincronizarUsuario(k, ((UsuarioMovelVO)usu).toUsuarioTreino());
            }
        } catch (Exception e) {
            Uteis.logar(null, "TreinoWS -> Não foi possível atualizar a situação do Colaborador " + codigoColaborador + " devido ao ERRO: " + e.getMessage());
        }
    }

    public Boolean verificarColaboradorTemUsuario(int colaborador) throws Exception {
        try (ResultSet rs = criarConsulta("SELECT exists(SELECT codigo FROM usuario WHERE colaborador = " + colaborador + ") AS temUsuario", con)) {
            if (rs.next()) {
                return rs.getBoolean("temUsuario");
            }
        }
        return true;
    }
    
    public JSONArray obterProfessores(Integer empresa, boolean apenasAtivos) throws Exception{
        StringBuilder sql = new StringBuilder();
        sql.append(" select distinct col.codigo, pes.nome, pes.fotokey  from colaborador col \n");
        sql.append(" INNER JOIN pessoa pes ON pes.codigo = col.pessoa \n");
        if(apenasAtivos){
             sql.append(" AND col.situacao = 'AT' \n");
        }
        sql.append(" INNER JOIN tipocolaborador tc ON tc.colaborador = col.codigo AND tc.descricao IN ('PR','TW') \n");
        if(!UteisValidacao.emptyNumber(empresa)){
            sql.append(" WHERE col.empresa = ").append(empresa);
        }
        sql.append(" ORDER BY nome \n");
        JSONArray arr;
        try (ResultSet rs = criarConsulta(sql.toString(), con)) {
            arr = new JSONArray();
            while (rs.next()) {
                JSONObject json = new JSONObject();
                json.put("codigo", rs.getInt("codigo"));
                json.put("nome", rs.getString("nome"));
                json.put("fotokey", Uteis.getPaintFotoDaNuvem(rs.getString("fotokey")));
                arr.put(json);
            }
        }
        return arr;
    }

    public ColaboradorVO consultarPorUsuarioEmpresaComBasePessoa(Integer codigoUsuario, Integer codigoEmpresa) throws Exception{
        StringBuilder sql = new StringBuilder();
        sql.append("SELECT c2.codigo");
        sql.append(" FROM usuario u");
        sql.append(" INNER JOIN colaborador c ON c.codigo = u.colaborador");
        sql.append(" INNER JOIN colaborador c2 ON c2.pessoa = c.pessoa");
        sql.append(" WHERE u.codigo = ?");
        sql.append(" AND c2.empresa = ? AND c2.situacao = 'AT'");
        ColaboradorVO colaborador;
        try (PreparedStatement ps = con.prepareStatement(sql.toString())) {
            ps.setInt(1, codigoUsuario);
            ps.setInt(2, codigoEmpresa);
            try (ResultSet rs = ps.executeQuery()) {
                colaborador = null;
                if (rs.next()) {
                    colaborador = new ColaboradorVO();
                    colaborador.setCodigo(rs.getInt("codigo"));
                }
            }
        }
        return colaborador;
    }
    
    public ColaboradorVO consultarPorCodigoPessoaSituacao(Integer codigoPrm, final String situacao, Integer empresa, int nivelMontarDados) throws Exception {
        consultar(getIdEntidade(), false);
        String sqlStr = "";
        if (empresa == 0) {
            sqlStr = "SELECT * FROM Colaborador WHERE colaborador.pessoa = ? and  situacao = ? ";
        } else {
            sqlStr = "SELECT * FROM Colaborador WHERE colaborador.pessoa = ? and  situacao = ?  and  Colaborador.empresa =" + empresa + " ORDER BY codigo";
}
        try (PreparedStatement sqlConsultar = con.prepareStatement(sqlStr)) {
            sqlConsultar.setInt(1, codigoPrm);
            sqlConsultar.setString(2, situacao);
            try (ResultSet tabelaResultado = sqlConsultar.executeQuery()) {
                if (!tabelaResultado.next()) {
                    ColaboradorVO obj = new ColaboradorVO();
                    obj.getPessoa().setCodigo(0);
                    return obj;
                }
                return (montarDados(tabelaResultado, nivelMontarDados, this.con));
            }
        }
    }
    
    public boolean existeColaboradorEmpresaPorCodigoPessoaSituacao(Integer codigoPessoa, Integer empresa, Boolean ativo) throws Exception {
        consultar(getIdEntidade(), false);
        String sqlStr = "SELECT * FROM Colaborador WHERE colaborador.pessoa = "+codigoPessoa;
        if (UteisValidacao.notEmptyNumber(empresa)) {
            sqlStr += " and  Colaborador.empresa =" + empresa;
        }
        if (ativo != null) {
            sqlStr += " and situacao = '"+(ativo ? "AT" : "NA")+"'";
        }
        return existe(sqlStr, con);
    }
    
    public ColaboradorVO consultarConsultorDoContrato(int codigoContrato, int nivelMontarDados) throws Exception{
        StringBuilder sql = new StringBuilder();
        sql.append("select consultor \n");
        sql.append("from contrato \n");
        sql.append("where codigo = ").append(codigoContrato);
        try (Statement st = con.createStatement()) {
            try (ResultSet rs = st.executeQuery(sql.toString())) {
                if (rs.next() && rs.getInt("consultor") > 0) {
                    return consultarPorChavePrimaria(rs.getInt("consultor"), nivelMontarDados);
                }
            }
        }
        return null;
    }
    
    public ColaboradorVO consultarConsultorDoReciboConsultor(int codigoRecibo, int nivelMontarDados) throws Exception{
        StringBuilder sql = new StringBuilder();
        sql.append("select consultor \n");
        sql.append("from reciboclienteconsultor \n");
        sql.append("where recibo = ").append(codigoRecibo);
        try (Statement st = con.createStatement()) {
            try (ResultSet rs = st.executeQuery(sql.toString())) {
                if (rs.next() && rs.getInt("consultor") > 0) {
                    return consultarPorChavePrimaria(rs.getInt("consultor"), nivelMontarDados);
                }
            }
        }
        return null;
    }


    public Map<Integer, List<String>> tiposDosColaboradores(Integer colaboradorFiltro) throws Exception{
        ResultSet resultSet = criarConsulta("select colaborador, descricao from tipocolaborador " +
                (UteisValidacao.emptyNumber(colaboradorFiltro) ? "" : ("where colaborador = " + colaboradorFiltro)), con);
        Map<Integer, List<String>> mapa = new HashMap<>();
        while(resultSet.next()){
            int colaborador = resultSet.getInt("colaborador");
            String tipostr = resultSet.getString("descricao");
            List<String> strings = mapa.get(colaborador);
            if(strings == null){
                strings = new ArrayList<>();
            }
            TipoColaboradorEnum tipo = TipoColaboradorEnum.getTipo(tipostr);
            strings.add(tipo == null ? tipostr : tipo.getDescricao());
            mapa.put(colaborador, strings);
        }
        return mapa;
    }

    public List<ColaboradorVO> consultarPorCodigoPessoa(Integer codigoPrm, int nivelMontarDados) throws Exception{
        String  sqlStr = "SELECT * FROM Colaborador WHERE colaborador.pessoa = ?";
        try (PreparedStatement sqlConsultar = con.prepareStatement(sqlStr)) {
            sqlConsultar.setInt(1, codigoPrm.intValue());
            try (ResultSet tabelaResultado = sqlConsultar.executeQuery()) {
                return (montarDadosConsulta(tabelaResultado, nivelMontarDados, this.con));
            }
        }
    }

    public ColaboradorVO consultarPorCodigoPessoaObjetoSimples(Integer codigoPrm, int nivelMontarDados) throws Exception {
        consultar(getIdEntidade(), false);
        String sql = "SELECT * FROM Colaborador WHERE colaborador.pessoa = ? ";
        try (PreparedStatement sqlConsultar = con.prepareStatement(sql)) {
            sqlConsultar.setInt(1, codigoPrm.intValue());
            try (ResultSet tabelaResultado = sqlConsultar.executeQuery()) {
                if (!tabelaResultado.next()) {
                    ColaboradorVO obj = new ColaboradorVO();
                    obj.getPessoa().setCodigo(codigoPrm);
                    return obj;
                }
                return (montarDados(tabelaResultado, nivelMontarDados, this.con));
            }
        }
    }

    public JSONObject detalhesProfessor(Integer codigo) throws Exception{
        JSONObject detalhes = new JSONObject();
        String  sqlStr = "select p.datacadastro from pessoa p\n" +
                "inner join colaborador c on c.pessoa = p.codigo\n" +
                "where c.codigo = ?";
        try (PreparedStatement sqlConsultar = con.prepareStatement(sqlStr)) {
            sqlConsultar.setInt(1, codigo);
            try (ResultSet rs = sqlConsultar.executeQuery()) {
                if(rs.next()){
                    detalhes.put("cadastro", Uteis.getData(rs.getDate("datacadastro")));
                }
            }
        }
        sqlStr = "select to_char(dataregistro, 'MM/YYYY') as mes, count(codigo) as cont from historicovinculo h \n" +
                "where colaborador = ? and tipohistoricovinculo = 'EN'\n" +
                "group by to_char(dataregistro, 'MM/YYYY')";
        JSONArray entradas = new JSONArray();
        try (PreparedStatement sqlConsultar = con.prepareStatement(sqlStr)) {
            sqlConsultar.setInt(1, codigo);
            try (ResultSet rs = sqlConsultar.executeQuery()) {
                while (rs.next()){
                    JSONObject json = new JSONObject();
                    json.put("mes", rs.getString("mes"));
                    json.put("cont", rs.getInt("cont"));
                    entradas.put(json);
                }
            }
        }
        detalhes.put("entradas", entradas);

        sqlStr = "select to_char(dataregistro, 'MM/YYYY') as mes, count(codigo) as cont from historicovinculo h \n" +
                "where colaborador = ? and tipohistoricovinculo = 'SD'\n" +
                "group by to_char(dataregistro, 'MM/YYYY')";
        JSONArray saidas = new JSONArray();
        try (PreparedStatement sqlConsultar = con.prepareStatement(sqlStr)) {
            sqlConsultar.setInt(1, codigo);
            try (ResultSet rs = sqlConsultar.executeQuery()) {
                while (rs.next()){
                    JSONObject json = new JSONObject();
                    json.put("mes", rs.getString("mes"));
                    json.put("cont", rs.getInt("cont"));
                    saidas.put(json);
                }
            }
        }
        detalhes.put("saidas", saidas);


        return detalhes;
    }

    public JSONObject detalhesColaboradorProfessor(Integer codigo) throws Exception{
        JSONObject detalhes = new JSONObject();
        String  sqlStr = "select c.cargahoraria from colaborador c \n" +
                "where c.codigo = ?";
        try (PreparedStatement sqlConsultar = con.prepareStatement(sqlStr)) {
            sqlConsultar.setInt(1, codigo);
            try (ResultSet rs = sqlConsultar.executeQuery()) {
                if(rs.next()){
                    detalhes.put("cargahoraria", rs.getInt("cargahoraria"));
                }
            }
        }

        sqlStr = "select c.cargahoraria, m.modalidade from colaborador c \n" +
                "inner join colaboradormodalidade m on m.colaborador = c.codigo \n" +
                "where c.codigo = ?";
        JSONArray entradas = new JSONArray();
        try (PreparedStatement sqlConsultar = con.prepareStatement(sqlStr)) {
            sqlConsultar.setInt(1, codigo);
            try (ResultSet rs = sqlConsultar.executeQuery()) {
                while (rs.next()){
                    JSONObject json = new JSONObject();
                    json.put("codigo", rs.getInt("modalidade"));
                    entradas.put(json);
                }
            }
        }
        detalhes.put("modalidades", entradas);
        return detalhes;
    }

    public void salvaCodigoVitioColaborador (Integer codigoCol, String codigoVitio) throws Exception {
        executarConsulta("UPDATE colaborador SET codigoAfiliadoVitio = '" + codigoVitio + "' WHERE codigo = " + codigoCol, con);
    }

    public List<ColaboradorVO> consultarColaboradorEmailOuCPF(String email, String cpf, Integer empresa, Integer colaborador, int nivelMontarDados) throws Exception {
        List<ColaboradorVO> lista = new ArrayList<>();
        if (UteisValidacao.emptyNumber(colaborador)) {
            if (UteisValidacao.emptyString(email) && UteisValidacao.emptyString(cpf)) {
                throw new Exception("Informe pelo menos um dos parametros (email) ou (cpf)");
            }
            if (!UteisValidacao.emptyString(email)) {
                try (Statement stEmail = con.createStatement()) {
                    try (ResultSet rsEmail = stEmail.executeQuery("select pessoa from email where email ilike '" + email + "'")) {
                        Set<Integer> pessoas = new HashSet<>();
                        while (rsEmail.next()) {
                            pessoas.add(rsEmail.getInt("pessoa"));
                        }
                        for (Integer pCode : pessoas) {
                            ColaboradorVO col = consultarPorCodigoPessoa(pCode, empresa, nivelMontarDados);
                            if (UteisValidacao.notEmptyNumber(col.getCodigo())) {
                                lista.add(col);
                            }
                        }
                    }
                }
            }
            if (lista.isEmpty() && !UteisValidacao.emptyString(cpf)) {
                List cols = consultarPorCfp(cpf, empresa, false, nivelMontarDados);
                for (Object c : cols) {
                    if (UteisValidacao.notEmptyNumber(((ColaboradorVO) c).getCodigo())) {
                        lista.add(((ColaboradorVO) c));
                    }
                }
            }
        } else {
            ColaboradorVO c = consultarPorCodigo(colaborador, empresa, nivelMontarDados);
            if (UteisValidacao.notEmptyNumber(c.getCodigo())) {
                lista.add(c);
            }
        }

        if (UteisValidacao.emptyList(lista)) {
            throw new Exception("Nenhum colaborador encontrado");
        }
        return lista;
    }

    public JSONArray obterColaboradorEmailCpfDTO(List<ColaboradorVO> colaboradores) {
        JSONArray array = new JSONArray();
        for (ColaboradorVO col : colaboradores) {
            JSONObject json = new JSONObject();
            try {
                json.put("codigo", col.getCodigo());
                json.put("situacao", col.getSituacao());
                json.put("codigoPessoa", col.getPessoa().getCodigo());
                json.put("nome", col.getPessoa().getNome());
                json.put("cpf", col.getPessoa().getCfp());
                json.put("sexo", col.getPessoa().getSexo());
                json.put("dataCadastro", col.getPessoa().getDataCadastro_Apresentar());
                json.put("dataNascimento", col.getPessoa_DataNascimento());
                json.put("urlFoto", Uteis.getPaintFotoDaNuvem(col.getPessoa().getFotoKey()));

                StringBuilder emails = new StringBuilder();
                int i = 0;
                for (EmailVO obj : col.getPessoa().getEmailVOs()) {
                    if (i > 0) {
                        emails.append(";");
                    }
                    emails.append(obj.getEmail());
                    i++;
                }
                json.put("emails", emails.toString());

                StringBuilder telefones = new StringBuilder();
                i = 0;
                for (TelefoneVO obj : col.getPessoa().getTelefoneVOs()) {
                    if (i > 0) {
                        telefones.append(";");
                    }
                    telefones.append(obj.getTipoTelefone()).append("-");
                    telefones.append(obj.getNumero());
                    i++;
                }
                json.put("telefones", telefones.toString());

                for (EnderecoVO obj : col.getPessoa().getEnderecoVOs()) {
                    json.put("endereco", obj.getEndereco());
                    json.put("numero", obj.getNumero());
                    json.put("complemento", obj.getComplemento());
                    json.put("bairro", obj.getBairro());
                    json.put("cep", obj.getCep());
                    break;
                }

                UsuarioMovel usuarioMovelDAO = new UsuarioMovel(con);
                json.put("nomeUsuarioMovel", usuarioMovelDAO.consultarPorColaborador(col, Uteis.NIVELMONTARDADOS_MINIMOS).getNome());
                usuarioMovelDAO = null;

                Empresa empresaDAO = new Empresa(con);
                EmpresaVO emp = empresaDAO.consultarPorCodigo(col.getEmpresa().getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS);
                empresaDAO = null;
                json.put("codigoCidade", emp.getCidade().getCodigoIBGE());
                json.put("nomeCidade", emp.getCidade().getNome());
                json.put("codigoEstado", emp.getEstado().getCodigoIBGE());
                json.put("siglaEstado", emp.getEstado().getSigla());
                json.put("empresa", emp.getCodigo());
                json.put("nomeEmpresa", emp.getNome());
            } catch (Exception ex) {
                json.put("msgErro", ex.getMessage());
            }
            array.put(json);
        }
        return array;
    }

    public void alterarSincronizadoRedeEmpresa(Integer codColaborador) throws Exception {
        String sql = "UPDATE colaborador SET sincronizadoRedeEmpresa = ? WHERE codigo = ?";
        try (PreparedStatement sqlAlterar = con.prepareStatement(sql)) {
            int i = 0;
            sqlAlterar.setTimestamp(++i, Uteis.getDataJDBCTimestamp(Calendario.hoje()));
            sqlAlterar.setInt(++i, codColaborador);
            sqlAlterar.execute();
        }
    }

    public void removerSincronizadoRedeEmpresa(String codAcesso) throws Exception {
        String sql = "UPDATE colaborador SET sincronizadoRedeEmpresa = ? WHERE codacesso = ?";
        try (PreparedStatement sqlAlterar = con.prepareStatement(sql)) {
            int i = 0;
            sqlAlterar.setNull(++i, Types.TIMESTAMP);
            sqlAlterar.setString(++i, codAcesso);
            sqlAlterar.execute();
        }
    }

    public ColaboradorVO consultarPorIdExterno(String idExterno, Integer empresa, int nivelMontarDados) throws Exception {
        StringBuilder sql = new StringBuilder();
        sql.append("SELECT \n");
        sql.append("c.* \n");
        sql.append("FROM colaborador c \n");
        sql.append("WHERE c.idexterno = '").append(idExterno.trim()).append("' \n");
        if (!UteisValidacao.emptyNumber(empresa)) {
            sql.append("AND c.empresa = ").append(empresa).append(" \n");
        }
        try (PreparedStatement sqlConsultar = con.prepareStatement(sql.toString())) {
            try (ResultSet rs = sqlConsultar.executeQuery()) {
                if (!rs.next()) {
                    return null;
                }
                return montarDados(rs, nivelMontarDados, this.con);
            }
        }
    }

    public void atualizarNovaPessoa(Integer codigoColaborador, Integer codigoPessoa) throws Exception {
        executarConsulta("UPDATE colaborador SET pessoa = " + codigoPessoa
                + " WHERE codigo = " + codigoColaborador, con);
    }
}

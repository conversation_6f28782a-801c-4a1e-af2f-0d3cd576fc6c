package negocio.facade.jdbc.basico;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.sql.Statement;
import java.util.ArrayList;
import java.util.Iterator;
import java.util.List;

import negocio.comuns.basico.MovimentoContaCorrenteClienteComposicaoVO;
import negocio.comuns.basico.MovimentoContaCorrenteClienteVO;
import negocio.comuns.financeiro.ConvenioCobrancaVO;
import negocio.comuns.financeiro.MovPagamentoVO;
import negocio.comuns.utilitarias.ConsistirException;
import negocio.comuns.utilitarias.Uteis;
import negocio.facade.jdbc.arquitetura.SuperEntidade;
import negocio.interfaces.basico.MovimentoContaCorrenteClienteComposicaoInterfaceFacade;

public class MovimentoContaCorrenteClienteComposicao extends SuperEntidade implements MovimentoContaCorrenteClienteComposicaoInterfaceFacade{

	public MovimentoContaCorrenteClienteComposicao() throws Exception {
		super();
        setIdEntidade("MovimentoContaCorrenteClienteComposicao");
	}
	public MovimentoContaCorrenteClienteComposicao(Connection con) throws Exception {
		super(con);
		setIdEntidade("MovimentoContaCorrenteClienteComposicao");
	}

	@Override
	public void alterar(MovimentoContaCorrenteClienteComposicaoVO obj)
			throws Exception {
		// TODO Auto-generated method stub
		
	}

	@Override
	public void alterar(MovimentoContaCorrenteClienteComposicaoVO obj,
			boolean centralEventos) throws Exception {
		// TODO Auto-generated method stub
		
	}

	@Override
	public void alterarSemCommit(MovimentoContaCorrenteClienteComposicaoVO obj,
			boolean centralEventos) throws Exception {
		// TODO Auto-generated method stub
		
	}

	@Override
	public MovimentoContaCorrenteClienteComposicaoVO consultarPorChavePrimaria(
			Integer codigo, int nivelMontarDados) throws Exception {
		   consultar(getIdEntidade(), false);
	        String sql = "SELECT * FROM MovimentoContaCorrenteClienteComposicao WHERE codigo = ?";
	        PreparedStatement sqlConsultar = con.prepareStatement(sql);
	        sqlConsultar.setInt(1, codigo.intValue());
	        ResultSet tabelaResultado = sqlConsultar.executeQuery();
	        if (!tabelaResultado.next()) {
	            throw new ConsistirException("Dados Não Encontrados ( MovimentoContaCorrenteClienteComposicao ).");
	        }
	        return (montarDados(tabelaResultado, nivelMontarDados));
	}

	@Override
	public MovimentoContaCorrenteClienteComposicaoVO consultarPorCodigoMovPagamento(Integer valorConsulta, int nivelMontarDados) throws Exception {
		String sqlStr = "SELECT MAX(codigo) FROM MovimentoContaCorrenteClienteComposicao WHERE movpagamento = " + valorConsulta.intValue();
		Statement stm = con.createStatement();
        ResultSet tabelaResultado = stm.executeQuery(sqlStr);
        if (!tabelaResultado.next()) {
            return null;
        }
        int codigo = tabelaResultado.getInt(1);
        if (codigo == 0) {
            return null;
        }
        return (consultarPorChavePrimaria(codigo, nivelMontarDados));
	}

	@Override
	public List <MovimentoContaCorrenteClienteComposicaoVO> consultarPorCodigoMovimento(Integer valorConsulta,
			boolean controlarAcesso, int nivelMontarDados) throws Exception {
		
		String sqlStr = "SELECT * FROM MovimentoContaCorrenteClienteComposicao WHERE MovimentoContaCorrenteCliente = " + valorConsulta.intValue();
		Statement stm = con.createStatement();
        ResultSet tabelaResultado = stm.executeQuery(sqlStr);
  
        return montarDadosConsulta(tabelaResultado, nivelMontarDados);
	}
	
    public static List montarDadosConsulta(ResultSet tabelaResultado, int nivelMontarDados) throws Exception {
        List vetResultado = new ArrayList();
        while (tabelaResultado.next()) {
        	MovimentoContaCorrenteClienteComposicaoVO obj = new MovimentoContaCorrenteClienteComposicaoVO();
            obj = montarDados(tabelaResultado, nivelMontarDados);
            vetResultado.add(obj);
        }

        return vetResultado;
    }
    
    public static MovimentoContaCorrenteClienteComposicaoVO montarDados(ResultSet dadosSQL, int nivelMontarDados) throws Exception {
    	MovimentoContaCorrenteClienteComposicaoVO  obj = new MovimentoContaCorrenteClienteComposicaoVO();
        obj.setCodigo(dadosSQL.getInt("codigo"));
        obj.setMovimento(dadosSQL.getInt("movimentocontacorrentecliente"));
        obj.setMovpagamento(dadosSQL.getInt("movpagamento"));
        return obj;
    }

	@Override
	public void excluir(MovimentoContaCorrenteClienteComposicaoVO obj)
			throws Exception {
		// TODO Auto-generated method stub
		
	}

	@Override
	public void excluir(MovimentoContaCorrenteClienteComposicaoVO obj,
			boolean centralEventos) throws Exception {
		// TODO Auto-generated method stub
		
	}
	
	@Override
	public void excluirComposicaoMovPagamento(Integer movpagamento) throws Exception {
		String sql = "delete from MovimentoContaCorrenteClienteComposicao where movpagamento = ?";
        PreparedStatement sqlInserir = con.prepareStatement(sql);
        int i = 1;   
        sqlInserir.setInt(i++, movpagamento);
      
        sqlInserir.execute();
		
	}

	@Override
	public void excluirComposicaoMovimento(Integer movimento) throws Exception {
		String sql = "delete from MovimentoContaCorrenteClienteComposicao where movimentocontacorrentecliente = ?";
        PreparedStatement sqlInserir = con.prepareStatement(sql);
        int i = 1;   
        sqlInserir.setInt(i++, movimento);
      
        sqlInserir.execute();
		
	}
	
	@Override
	public void incluir(MovimentoContaCorrenteClienteComposicaoVO obj)
			throws Exception {
		// TODO Auto-generated method stub
		
	}

	@Override
	public void incluirSemCommit(MovimentoContaCorrenteClienteComposicaoVO obj,
			boolean centralEventos) throws Exception {
		// TODO Auto-generated method stub
		
	}

	@Override
	public void incluirSemCommit(MovimentoContaCorrenteClienteComposicaoVO obj)
			throws Exception {
		String sql = "INSERT INTO MovimentoContaCorrenteClienteComposicao( movimentocontacorrentecliente, movpagamento) VALUES ( ?, ? )";
        PreparedStatement sqlInserir = con.prepareStatement(sql);
        int i = 1;   
        sqlInserir.setInt(i++, obj.getMovimento());
        sqlInserir.setInt(i++, obj.getMovpagamento());
      
        sqlInserir.execute();
		
	}

	@Override
	public MovimentoContaCorrenteClienteComposicaoVO novo() throws Exception {
		// TODO Auto-generated method stub
		return null;
	}
	@Override
	public void adicionarComposicao(MovimentoContaCorrenteClienteVO obj, boolean centralEventos) throws Exception {
		Iterator i = obj.getMovPagamentosVOs().iterator();
    	while (i.hasNext()){
    		MovPagamentoVO movPagamento= (MovPagamentoVO) i.next();
    		MovimentoContaCorrenteClienteComposicaoVO novo = new MovimentoContaCorrenteClienteComposicaoVO();
    		novo.setMovimento(obj.getCodigo());
    		novo.setMovpagamento(movPagamento.getCodigo());
    		incluirSemCommit(novo);
    	}
		
	}
	@Override
	public void alterarComposicao(Integer novo, Integer atual) throws SQLException {
		String sql = "UPDATE MovimentoContaCorrenteClienteComposicao set movimentocontacorrentecliente = ? WHERE movimentocontacorrentecliente = ? ";
        PreparedStatement sqlInserir = con.prepareStatement(sql);
        int i = 1;   
        sqlInserir.setInt(i++, novo);
        sqlInserir.setInt(i++, atual);
      
        sqlInserir.execute();
		
	}
	

}

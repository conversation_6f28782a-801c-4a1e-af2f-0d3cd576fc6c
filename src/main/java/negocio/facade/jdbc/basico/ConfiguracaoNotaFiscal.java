package negocio.facade.jdbc.basico;

import br.com.pactosolucoes.integracao.enotas.EnotasService;
import br.com.pactosolucoes.integracao.enotas.to.InfoCidadeEnotasTO;
import br.com.pactosolucoes.integracao.enotas.to.InfoEmpresaEnotasTO;
import br.com.pactosolucoes.integracao.enotas.to.InfoServicoEnotasTO;
import br.com.pactosolucoes.oamd.controle.basico.DAO;
import com.amazonaws.util.IOUtils;
import org.json.JSONArray;
import org.json.JSONObject;
import negocio.comuns.basico.*;
import negocio.comuns.financeiro.enumerador.TipoExigibilidadeISSEnum;
import negocio.comuns.financeiro.enumerador.TipoRelatorioDF;
import negocio.comuns.notaFiscal.AmbienteEmissaoNotaFiscalEnum;
import negocio.comuns.notaFiscal.TipoNotaFiscalEnum;
import negocio.comuns.utilitarias.Calendario;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisValidacao;
import negocio.facade.jdbc.arquitetura.SuperEntidade;
import negocio.facade.jdbc.arquitetura.SuperFacadeJDBC;
import negocio.facade.jdbc.utilitarias.Conexao;
import negocio.interfaces.basico.ConfiguracaoNotaFiscalInterfaceFacade;
import servicos.util.ExecuteRequestHttpService;

import javax.servlet.ServletContext;
import java.io.*;
import java.net.URL;
import java.net.URLConnection;
import java.security.KeyStore;
import java.security.cert.Certificate;
import java.security.cert.X509Certificate;
import java.sql.*;
import java.util.*;
import java.util.Date;
import java.util.logging.Level;
import java.util.logging.Logger;

public class ConfiguracaoNotaFiscal extends SuperEntidade implements ConfiguracaoNotaFiscalInterfaceFacade {

    public ConfiguracaoNotaFiscal() throws Exception {
        super();
    }

    public ConfiguracaoNotaFiscal(Connection con) throws Exception {
        super(con);
    }

    public void incluir(ConfiguracaoNotaFiscalVO obj) throws Exception {
        try {
            con.setAutoCommit(false);
            incluirSemCommit(obj);
            con.commit();
        } catch (Exception e) {
            con.rollback();
            con.setAutoCommit(true);
            throw e;
        } finally {
            con.setAutoCommit(true);
        }
    }

    public void incluirSemCommit(ConfiguracaoNotaFiscalVO obj) throws Exception {
        ConfiguracaoNotaFiscalVO.validarDados(obj);
        obj.realizarUpperCaseDados();
        String sql = "INSERT INTO ConfiguracaoNotaFiscal (empresa, tipoNotaFiscal, descricao, iss, pis, cofins, irrf, valorMinimoIRRF, "
                + "codListaServico, codTributacaoMunicipal, cnae, exigibilidadeiss, observacao, naturezaoperacao, cnpj, enviarNotaCidadeEmpresa, "
                + "pais, estado, cidade, cpfObrigatorio, emailObrigatorio, enderecoObrigatorio, serie, issRetido, "
                + "usarDescricaoFormaPagamento, enviarObservacaoNaDescricao, apresentarDuracaoPlano, apresentarDescricaoParcela, apresentarCompetencia, ativo, "
                + "enotas, ambienteEmissao, idEnotas, dataCadastro, inscricaoMunicipal, inscricaoEstadual, razaoSocial, nomeFantasia, optanteSimplesNacional, "
                + "email, telefoneComercial, incentivadorCultural, regimeEspecialTributacao, descricaoServico, enviarEmailCliente, logradouro, numero, "
                + "complemento, bairro, cep, chaveCertificado, senhaCertificado, formatoCertificado, chaveLogotipo, formatoLogotipo, limiteComplementoEndereco, "
                + "percentualAproximadoTributos, limiteDescricao, limiteObservacao, linkPrefeitura, apresentarTipoIntegracaoPagamento, percentualFederal, percentualEstadual, percentualMunicipal, csll) "
                + "VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, "
                + "?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)";
        int i = 0;
        try (PreparedStatement sqlInserir = con.prepareStatement(sql)) {
            resolveIntegerNull(sqlInserir, ++i, obj.getEmpresaVO().getCodigo());
            sqlInserir.setInt(++i, obj.getTipoNotaFiscal().getCodigo());
            sqlInserir.setString(++i, obj.getDescricao());
            sqlInserir.setDouble(++i, obj.getIss());
            sqlInserir.setDouble(++i, obj.getPis());
            sqlInserir.setDouble(++i, obj.getCofins());
            sqlInserir.setDouble(++i, obj.getIrrf());
            sqlInserir.setDouble(++i, obj.getValorMinimoIRRF());
            sqlInserir.setString(++i, obj.getCodListaServico());
            sqlInserir.setString(++i, obj.getCodTributacaoMunicipal());
            sqlInserir.setString(++i, obj.getCnae());
            sqlInserir.setInt(++i, obj.getExigibilidadeISS().getId());
            sqlInserir.setString(++i, obj.getObservacao());
            sqlInserir.setString(++i, obj.getNaturezaOperacao());
            sqlInserir.setString(++i, obj.getCnpj());
            sqlInserir.setBoolean(++i, obj.isEnviarNotaCidadeEmpresa());
            resolveIntegerNull(sqlInserir, ++i, obj.getPaisVO().getCodigo());
            resolveIntegerNull(sqlInserir, ++i, obj.getEstadoVO().getCodigo());
            resolveIntegerNull(sqlInserir, ++i, obj.getCidadeVO().getCodigo());
            sqlInserir.setBoolean(++i, obj.isCpfObrigatorio());
            sqlInserir.setBoolean(++i, obj.isEmailObrigatorio());
            sqlInserir.setBoolean(++i, obj.isEnderecoObrigatorio());
            sqlInserir.setString(++i, obj.getSerie());
            sqlInserir.setBoolean(++i, obj.isIssRetido());
            sqlInserir.setBoolean(++i, obj.isUsarDescricaoFormaPagamento());
            sqlInserir.setBoolean(++i, obj.isEnviarObservacaoNaDescricao());
            sqlInserir.setBoolean(++i, obj.isApresentarDuracaoPlano());
            sqlInserir.setBoolean(++i, obj.isApresentarDescricaoParcela());
            sqlInserir.setBoolean(++i, obj.isApresentarCompetencia());
            sqlInserir.setBoolean(++i, obj.isAtivo());
            sqlInserir.setBoolean(++i, obj.isEnotas());
            sqlInserir.setInt(++i, obj.getAmbienteEmissao().getCodigo());
            sqlInserir.setString(++i, obj.getIdEnotas());
            sqlInserir.setTimestamp(++i, Uteis.getDataJDBCTimestamp(Calendario.hoje()));
            sqlInserir.setString(++i, obj.getInscricaoMunicipal());
            sqlInserir.setString(++i, obj.getInscricaoEstadual());
            sqlInserir.setString(++i, obj.getRazaoSocial());
            sqlInserir.setString(++i, obj.getNomeFantasia());
            sqlInserir.setBoolean(++i, obj.isOptanteSimplesNacional());
            sqlInserir.setString(++i, obj.getEmail());
            sqlInserir.setString(++i, obj.getTelefoneComercial());
            sqlInserir.setBoolean(++i, obj.isIncentivadorCultural());
            sqlInserir.setString(++i, obj.getRegimeEspecialTributacao());
            sqlInserir.setString(++i, obj.getDescricaoServico());
            sqlInserir.setBoolean(++i, obj.isEnviarEmailCliente());
            sqlInserir.setString(++i, obj.getLogradouro());
            sqlInserir.setString(++i, obj.getNumero());
            sqlInserir.setString(++i, obj.getComplemento());
            sqlInserir.setString(++i, obj.getBairro());
            sqlInserir.setString(++i, obj.getCep());
            sqlInserir.setString(++i, obj.getChaveCertificado());
            sqlInserir.setString(++i, obj.getSenhaCertificado());
            sqlInserir.setString(++i, obj.getFormatoCertificado());
            sqlInserir.setString(++i, obj.getChaveLogotipo());
            sqlInserir.setString(++i, obj.getFormatoLogotipo());
            sqlInserir.setInt(++i, obj.getLimiteComplementoEndereco());
            sqlInserir.setDouble(++i, obj.getPercentualAproximadoTributos());
            sqlInserir.setInt(++i, obj.getLimiteDescricao());
            sqlInserir.setInt(++i, obj.getLimiteObservacao());
            sqlInserir.setString(++i, obj.getLinkPrefeitura());
            sqlInserir.setBoolean(++i, obj.isApresentarTipoIntegracaoPagamento());
            sqlInserir.setDouble(++i, obj.getPercentualFederal());
            sqlInserir.setDouble(++i, obj.getPercentualEstadual());
            sqlInserir.setDouble(++i, obj.getPercentualMunicipal());
            sqlInserir.setDouble(++i, obj.getCsll());

            sqlInserir.execute();
        }
        obj.setCodigo(obterValorChavePrimariaCodigo());
        obj.setNovoObj(false);

        ConfiguracaoNotaFiscalAmbiente ambienteDAO = new ConfiguracaoNotaFiscalAmbiente(con);
        ambienteDAO.incluirOuAlteraSemComitPorConfiguracaoNotaFiscal(obj);
        ambienteDAO = null;
    }

    public void alterar(ConfiguracaoNotaFiscalVO obj) throws Exception {
        try {
            con.setAutoCommit(false);
            alterarSemCommit(obj);
            con.commit();
        } catch (Exception e) {
            con.rollback();
            con.setAutoCommit(true);
            throw e;
        } finally {
            con.setAutoCommit(true);
        }
    }

    private void alterarSemCommit(ConfiguracaoNotaFiscalVO obj) throws Exception {
        ConfiguracaoNotaFiscalVO.validarDados(obj);
        obj.realizarUpperCaseDados();
        String sql = "UPDATE ConfiguracaoNotaFiscal SET empresa = ?, tipoNotaFiscal = ?, descricao = ?, iss = ?, pis = ?, cofins = ?, "
                + "irrf = ?, valorMinimoIRRF = ?,codListaServico = ?, codTributacaoMunicipal = ?, cnae = ?, exigibilidadeiss = ?, "
                + "observacao = ?, naturezaoperacao = ?, cnpj = ?, enviarNotaCidadeEmpresa = ?, pais = ?, estado = ?, cidade = ?, cpfObrigatorio = ?, "
                + "emailObrigatorio = ?, enderecoObrigatorio = ?, serie = ?, issRetido = ?, usarDescricaoFormaPagamento = ?, "
                + "enviarObservacaoNaDescricao = ?, apresentarDuracaoPlano = ?, apresentarDescricaoParcela = ?, apresentarCompetencia = ?, ativo = ?, "
                + "enotas = ?, ambienteEmissao = ?, idEnotas = ?, dataCadastro = ?, inscricaoMunicipal = ?, inscricaoEstadual = ?, razaoSocial = ?, "
                + "nomeFantasia = ?, optanteSimplesNacional = ?, email = ?, telefoneComercial = ?, incentivadorCultural = ?, regimeEspecialTributacao = ?, "
                + "descricaoServico = ?, enviarEmailCliente = ?, logradouro = ?, numero = ?, complemento = ?, bairro = ?, cep = ?, "
                + "chaveCertificado = ?, senhaCertificado = ?, formatoCertificado = ?, chaveLogotipo = ?, formatoLogotipo = ?, limiteComplementoEndereco = ?, "
                + "percentualAproximadoTributos = ?, limiteDescricao = ?, limiteObservacao = ?, linkPrefeitura = ?, apresentarTipoIntegracaoPagamento = ?, "
                + "percentualFederal = ?, percentualEstadual = ?, percentualMunicipal = ?, csll = ?\n"
                + "WHERE codigo = ? ";
        int i = 0;
        try (PreparedStatement sqlAlterar = con.prepareStatement(sql)) {
            resolveIntegerNull(sqlAlterar, ++i, obj.getEmpresaVO().getCodigo());
            sqlAlterar.setInt(++i, obj.getTipoNotaFiscal().getCodigo());
            sqlAlterar.setString(++i, obj.getDescricao());
            sqlAlterar.setDouble(++i, obj.getIss());
            sqlAlterar.setDouble(++i, obj.getPis());
            sqlAlterar.setDouble(++i, obj.getCofins());
            sqlAlterar.setDouble(++i, obj.getIrrf());
            sqlAlterar.setDouble(++i, obj.getValorMinimoIRRF());
            sqlAlterar.setString(++i, obj.getCodListaServico());
            sqlAlterar.setString(++i, obj.getCodTributacaoMunicipal());
            sqlAlterar.setString(++i, obj.getCnae());
            sqlAlterar.setInt(++i, obj.getExigibilidadeISS().getId());
            sqlAlterar.setString(++i, obj.getObservacao());
            sqlAlterar.setString(++i, obj.getNaturezaOperacao());
            sqlAlterar.setString(++i, obj.getCnpj());
            sqlAlterar.setBoolean(++i, obj.isEnviarNotaCidadeEmpresa());
            resolveIntegerNull(sqlAlterar, ++i, obj.getPaisVO().getCodigo());
            resolveIntegerNull(sqlAlterar, ++i, obj.getEstadoVO().getCodigo());
            resolveIntegerNull(sqlAlterar, ++i, obj.getCidadeVO().getCodigo());
            sqlAlterar.setBoolean(++i, obj.isCpfObrigatorio());
            sqlAlterar.setBoolean(++i, obj.isEmailObrigatorio());
            sqlAlterar.setBoolean(++i, obj.isEnderecoObrigatorio());
            sqlAlterar.setString(++i, obj.getSerie());
            sqlAlterar.setBoolean(++i, obj.isIssRetido());
            sqlAlterar.setBoolean(++i, obj.isUsarDescricaoFormaPagamento());
            sqlAlterar.setBoolean(++i, obj.isEnviarObservacaoNaDescricao());
            sqlAlterar.setBoolean(++i, obj.isApresentarDuracaoPlano());
            sqlAlterar.setBoolean(++i, obj.isApresentarDescricaoParcela());
            sqlAlterar.setBoolean(++i, obj.isApresentarCompetencia());
            sqlAlterar.setBoolean(++i, obj.isAtivo());
            sqlAlterar.setBoolean(++i, obj.isEnotas());
            sqlAlterar.setInt(++i, obj.getAmbienteEmissao().getCodigo());
            sqlAlterar.setString(++i, obj.getIdEnotas());
            sqlAlterar.setTimestamp(++i, Uteis.getDataJDBCTimestamp(obj.getDataCadastro()));
            sqlAlterar.setString(++i, obj.getInscricaoMunicipal());
            sqlAlterar.setString(++i, obj.getInscricaoEstadual());
            sqlAlterar.setString(++i, obj.getRazaoSocial());
            sqlAlterar.setString(++i, obj.getNomeFantasia());
            sqlAlterar.setBoolean(++i, obj.isOptanteSimplesNacional());
            sqlAlterar.setString(++i, obj.getEmail());
            sqlAlterar.setString(++i, obj.getTelefoneComercial());
            sqlAlterar.setBoolean(++i, obj.isIncentivadorCultural());
            sqlAlterar.setString(++i, obj.getRegimeEspecialTributacao());
            sqlAlterar.setString(++i, obj.getDescricaoServico());
            sqlAlterar.setBoolean(++i, obj.isEnviarEmailCliente());
            sqlAlterar.setString(++i, obj.getLogradouro());
            sqlAlterar.setString(++i, obj.getNumero());
            sqlAlterar.setString(++i, obj.getComplemento());
            sqlAlterar.setString(++i, obj.getBairro());
            sqlAlterar.setString(++i, obj.getCep());
            sqlAlterar.setString(++i, obj.getChaveCertificado());
            sqlAlterar.setString(++i, obj.getSenhaCertificado());
            sqlAlterar.setString(++i, obj.getFormatoCertificado());
            sqlAlterar.setString(++i, obj.getChaveLogotipo());
            sqlAlterar.setString(++i, obj.getFormatoLogotipo());
            sqlAlterar.setInt(++i, obj.getLimiteComplementoEndereco());
            sqlAlterar.setDouble(++i, obj.getPercentualAproximadoTributos());
            sqlAlterar.setInt(++i, obj.getLimiteDescricao());
            sqlAlterar.setInt(++i, obj.getLimiteObservacao());
            sqlAlterar.setString(++i, obj.getLinkPrefeitura());
            sqlAlterar.setBoolean(++i, obj.isApresentarTipoIntegracaoPagamento());
            sqlAlterar.setDouble(++i, obj.getPercentualFederal());
            sqlAlterar.setDouble(++i, obj.getPercentualEstadual());
            sqlAlterar.setDouble(++i, obj.getPercentualMunicipal());
            sqlAlterar.setDouble(++i, obj.getCsll());

            sqlAlterar.setInt(++i, obj.getCodigo());
            sqlAlterar.execute();
        }

        ConfiguracaoNotaFiscalAmbiente ambienteDAO = new ConfiguracaoNotaFiscalAmbiente(con);
        ambienteDAO.incluirOuAlteraSemComitPorConfiguracaoNotaFiscal(obj);
        ambienteDAO = null;
    }

    public void excluir(ConfiguracaoNotaFiscalVO obj) throws Exception {
        try {
            con.setAutoCommit(false);
            String sql = "DELETE FROM ConfiguracaoNotaFiscal WHERE codigo = ?";
            try (PreparedStatement sqlExcluir = con.prepareStatement(sql)) {
                sqlExcluir.setInt(1, obj.getCodigo());
                sqlExcluir.execute();
            }
            con.commit();
        } catch (Exception e) {
            con.rollback();
            con.setAutoCommit(true);
            throw e;
        } finally {
            con.setAutoCommit(true);
        }
    }

    public static List<ConfiguracaoNotaFiscalVO> montarDadosConsulta(ResultSet tabelaResultado, int nivelMontarDados, Connection con) throws Exception {
        List<ConfiguracaoNotaFiscalVO> vetResultado = new ArrayList<ConfiguracaoNotaFiscalVO>();
        while (tabelaResultado.next()) {
            ConfiguracaoNotaFiscalVO obj = montarDados(tabelaResultado, nivelMontarDados, con);
            vetResultado.add(obj);
        }
        return vetResultado;
    }

    public static ConfiguracaoNotaFiscalVO montarDados(ResultSet rs, int nivelMontarDados, Connection con) throws Exception {
        ConfiguracaoNotaFiscalVO obj = montarDadosBasicos(rs, con);

        if (nivelMontarDados == Uteis.NIVELMONTARDADOS_MINIMOS) {
            return obj;
        }

        ConfiguracaoNotaFiscalAmbiente ambienteDAO = new ConfiguracaoNotaFiscalAmbiente(con);
        obj.setConfigProducaoVO(ambienteDAO.consultarPorAmbienteEmissaoConfiguracaoNotaFiscal(AmbienteEmissaoNotaFiscalEnum.PRODUCAO, obj.getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS));
        obj.setConfigHomologacaoVO(ambienteDAO.consultarPorAmbienteEmissaoConfiguracaoNotaFiscal(AmbienteEmissaoNotaFiscalEnum.HOMOLOGACAO, obj.getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS));
        ambienteDAO = null;

        montarDadosPais(obj, nivelMontarDados, con);
        montarDadosEstado(obj, nivelMontarDados, con);
        montarDadosCidade(obj, nivelMontarDados, con);

        if (nivelMontarDados == Uteis.NIVELMONTARDADOS_DADOSBASICOS) {
            return obj;
        }

        return obj;

    }

    public static ConfiguracaoNotaFiscalVO montarDadosBasicos(ResultSet rs, Connection con) throws Exception {
        ConfiguracaoNotaFiscalVO obj = new ConfiguracaoNotaFiscalVO();
        obj.setCodigo(rs.getInt("codigo"));
        obj.setNovoObj(false);
        obj.getEmpresaVO().setCodigo(rs.getInt("empresa"));
        obj.setTipoNotaFiscal(TipoNotaFiscalEnum.obterPorCodigo(rs.getInt("tipoNotaFiscal")));
        obj.setDescricao(rs.getString("descricao"));
        obj.setIss(rs.getDouble("iss"));
        obj.setPis(rs.getDouble("pis"));
        obj.setCofins(rs.getDouble("cofins"));
        obj.setIrrf(rs.getDouble("irrf"));
        obj.setValorMinimoIRRF(rs.getDouble("valorMinimoIRRF"));
        obj.setCodListaServico(rs.getString("codListaServico"));
        obj.setCodTributacaoMunicipal(rs.getString("codTributacaoMunicipal"));
        obj.setCnae(rs.getString("cnae"));
        obj.setExigibilidadeISS(TipoExigibilidadeISSEnum.getTipo(rs.getInt("exigibilidadeiss")));
        obj.setObservacao(rs.getString("observacao"));
        obj.setNaturezaOperacao(rs.getString("naturezaoperacao"));
        obj.setCnpj(rs.getString("cnpj"));
        obj.setEnviarNotaCidadeEmpresa(rs.getBoolean("enviarNotaCidadeEmpresa"));
        obj.getPaisVO().setCodigo(rs.getInt("pais"));
        obj.getEstadoVO().setCodigo(rs.getInt("estado"));
        obj.getCidadeVO().setCodigo(rs.getInt("cidade"));
        obj.setCpfObrigatorio(rs.getBoolean("cpfObrigatorio"));
        obj.setEmailObrigatorio(rs.getBoolean("emailObrigatorio"));
        obj.setEnderecoObrigatorio(rs.getBoolean("enderecoObrigatorio"));
        obj.setSerie(rs.getString("serie"));
        obj.setIssRetido(rs.getBoolean("issRetido"));
        obj.setUsarDescricaoFormaPagamento(rs.getBoolean("usarDescricaoFormaPagamento"));
        obj.setEnviarObservacaoNaDescricao(rs.getBoolean("enviarObservacaoNaDescricao"));
        obj.setApresentarDuracaoPlano(rs.getBoolean("apresentarDuracaoPlano"));
        obj.setApresentarDescricaoParcela(rs.getBoolean("apresentarDescricaoParcela"));
        obj.setApresentarCompetencia(rs.getBoolean("apresentarCompetencia"));
        obj.setAtivo(rs.getBoolean("ativo"));
        obj.setEnotas(rs.getBoolean("enotas"));
        obj.setAmbienteEmissao(AmbienteEmissaoNotaFiscalEnum.obterPorCodigo(rs.getInt("ambienteEmissao")));
        obj.setIdEnotas(rs.getString("idEnotas"));
        obj.setDataCadastro(rs.getTimestamp("dataCadastro"));
        obj.setInscricaoMunicipal(rs.getString("inscricaoMunicipal"));
        obj.setInscricaoEstadual(rs.getString("inscricaoEstadual"));
        obj.setRazaoSocial(rs.getString("razaoSocial"));
        obj.setNomeFantasia(rs.getString("nomeFantasia"));
        obj.setOptanteSimplesNacional(rs.getBoolean("optanteSimplesNacional"));
        obj.setEmail(rs.getString("email"));
        obj.setTelefoneComercial(rs.getString("telefoneComercial"));
        obj.setIncentivadorCultural(rs.getBoolean("incentivadorCultural"));
        obj.setRegimeEspecialTributacao(rs.getString("regimeEspecialTributacao"));
        obj.setDescricaoServico(rs.getString("descricaoServico"));
        obj.setEnviarEmailCliente(rs.getBoolean("enviarEmailCliente"));
        obj.setLogradouro(rs.getString("logradouro"));
        obj.setNumero(rs.getString("numero"));
        obj.setComplemento(rs.getString("complemento"));
        obj.setBairro(rs.getString("bairro"));
        obj.setCep(rs.getString("cep"));
        obj.setChaveCertificado(rs.getString("chaveCertificado"));
        obj.setSenhaCertificado(rs.getString("senhaCertificado"));
        obj.setFormatoCertificado(rs.getString("formatoCertificado"));
        obj.setChaveLogotipo(rs.getString("chaveLogotipo"));
        obj.setFormatoLogotipo(rs.getString("formatoLogotipo"));
        obj.setDataCertificado(rs.getTimestamp("dataCertificado"));
        obj.setLimiteComplementoEndereco(rs.getInt("limiteComplementoEndereco"));
        obj.setPercentualAproximadoTributos(rs.getDouble("percentualAproximadoTributos"));
        obj.setLimiteDescricao(rs.getInt("limiteDescricao"));
        obj.setLimiteObservacao(rs.getInt("limiteObservacao"));
        obj.setLinkPrefeitura(rs.getString("linkPrefeitura"));
        obj.setApresentarTipoIntegracaoPagamento(rs.getBoolean("apresentarTipoIntegracaoPagamento"));
        obj.setPercentualFederal(rs.getDouble("percentualFederal"));
        obj.setPercentualEstadual(rs.getDouble("percentualEstadual"));
        obj.setPercentualMunicipal(rs.getDouble("percentualMunicipal"));
        obj.setCsll(rs.getDouble("csll"));
        return obj;
    }

    private static void montarDadosPais(ConfiguracaoNotaFiscalVO obj, int nivelMontarDados, Connection con) throws Exception {
        if (UteisValidacao.emptyNumber(obj.getPaisVO().getCodigo())) {
            obj.setPaisVO(new PaisVO());
            return;
        }
        Pais pais = new Pais(con);
        obj.setPaisVO(pais.consultarPorChavePrimaria(obj.getPaisVO().getCodigo(), nivelMontarDados));
        pais = null;
    }

    private static void montarDadosEstado(ConfiguracaoNotaFiscalVO obj, int nivelMontarDados, Connection con) throws Exception {
        if (UteisValidacao.emptyNumber(obj.getEstadoVO().getCodigo())) {
            obj.setEstadoVO(new EstadoVO());
            return;
        }
        Estado estado = new Estado(con);
        obj.setEstadoVO(estado.consultarPorChavePrimaria(obj.getEstadoVO().getCodigo(), nivelMontarDados));
        estado = null;
    }

    private static void montarDadosCidade(ConfiguracaoNotaFiscalVO obj, int nivelMontarDados, Connection con) throws Exception {
        if (UteisValidacao.emptyNumber(obj.getCidadeVO().getCodigo())) {
            obj.setCidadeVO(new CidadeVO());
            return;
        }
        Cidade cidade = new Cidade(con);
        obj.setCidadeVO(cidade.consultarPorChavePrimaria(obj.getCidadeVO().getCodigo(), nivelMontarDados));
        cidade = null;
    }

    public ConfiguracaoNotaFiscalVO consultarPorChavePrimaria(Integer codigo, int nivelMontarDados) throws Exception {
        ConfiguracaoNotaFiscalVO eCache = (ConfiguracaoNotaFiscalVO) obterFromCache(codigo);
        if (eCache != null){
            return eCache;
        }

        String sql = "SELECT * FROM ConfiguracaoNotaFiscal WHERE codigo = ?";
        try (PreparedStatement sqlConsultar = con.prepareStatement(sql)) {
            sqlConsultar.setInt(1, codigo);
            try (ResultSet tabelaResultado = sqlConsultar.executeQuery()) {
                if (!tabelaResultado.next()) {
                    return new ConfiguracaoNotaFiscalVO();
                }
                eCache = montarDados(tabelaResultado, nivelMontarDados, con);
            }
        }

        putToCache(eCache);
        return eCache;
    }

    public String consultarJSON(Integer empresa, String situacao) throws Exception {
        StringBuilder json;
        boolean dados;
        try (ResultSet rs = getPS(empresa, situacao).executeQuery()) {
            json = new StringBuilder();
            json.append("{\"aaData\":[");
            dados = false;
            while (rs.next()) {
                dados = true;

                String enotas = rs.getBoolean("enotas") ? " - eNotas" : "";
                String ambiente = "";
                if (rs.getBoolean("enotas")) {
                    AmbienteEmissaoNotaFiscalEnum ambEnum = AmbienteEmissaoNotaFiscalEnum.obterPorCodigo(rs.getInt("ambienteemissao"));
                    ambiente = ambEnum != null ? ambEnum.getDescricao().toUpperCase() : "";
                }

                json.append("[\"").append(rs.getString("codigo")).append("\",");
                json.append("\"").append(Uteis.normalizarStringJSON(rs.getString("descricao"))).append("\",");
                json.append("\"").append(Uteis.normalizarStringJSON(rs.getString("cidade"))).append("\",");
                json.append("\"").append(Uteis.normalizarStringJSON(rs.getString("cnae"))).append("\",");
                json.append("\"").append(rs.getString("cnpj")).append("\",");
                json.append("\"").append(TipoNotaFiscalEnum.obterPorCodigo(rs.getInt("tipoNotaFiscal"))).append(enotas).append("\",");
                json.append("\"").append(rs.getBoolean("ativo") ? "Ativo" : "Inativo").append("\",");
                json.append("\"").append(ambiente).append("\",");
                json.append("\"").append(Uteis.normalizarStringJSON(rs.getString("nomeEmpresa"))).append("\",");
                json.append("\"").append(Uteis.getDataComHora(UteisValidacao.converterData(rs.getString("dataCertificado"), "yyyy-MM-dd HH:mm:ss"))).append("\"],");
            }
        }
        if (dados) {
            json.deleteCharAt(json.toString().length() - 1);
        }
        json.append("]}");
        return json.toString();
    }

    private PreparedStatement getPS(Integer empresa, String situacao) throws SQLException {
        StringBuilder sql = new StringBuilder();
        sql.append("SELECT  \n");
        sql.append("c.codigo,  \n");
        sql.append("c.enotas,  \n");
        sql.append("c.descricao,  \n");
        sql.append("ci.nome as cidade, \n");
        sql.append("c.cnae,  \n");
        sql.append("c.cnpj,  \n");
        sql.append("c.tipoNotaFiscal,  \n");
        sql.append("c.ativo,  \n");
        sql.append("c.ambienteEmissao, \n");
        sql.append("e.nome as nomeEmpresa,  \n");
        sql.append("c.dataCertificado  \n");
        sql.append("FROM ConfiguracaoNotaFiscal c  \n");
        sql.append("left join cidade ci on ci.codigo = c.cidade \n");
        sql.append("inner join empresa e on e.codigo = c.empresa  \n");
        if (!UteisValidacao.emptyNumber(empresa)) {
            sql.append("where c.empresa = ").append(empresa).append(" \n");
            if (situacao.equals("AT")) {
                sql.append("and ativo");
            } else if(situacao.equals("IN")) {
                sql.append("and not ativo");
            }
        } else if(situacao.equals("AT")) {
            sql.append("where ativo");
        } else if (situacao.equals("IN")) {
            sql.append("where not ativo");
        }

        return con.prepareStatement(sql.toString());
    }

    public List<ConfiguracaoNotaFiscalVO> consultarConfiguracaoNotaFiscal(Integer empresa, Integer[] tipoNotaFiscal, Boolean ativo, Boolean enotas, int nivelMontarDados) throws Exception {
        StringBuilder sqlStr = new StringBuilder();
        sqlStr.append(" SELECT * FROM ConfiguracaoNotaFiscal \n ");
        sqlStr.append(" WHERE 1 = 1 \n");
        if (ativo != null) {
            sqlStr.append(" AND ativo = ").append(ativo).append(" \n");
        }
        if (enotas != null) {
            sqlStr.append(" AND enotas = ").append(enotas).append(" \n");
        }
        if (tipoNotaFiscal != null) {
            sqlStr.append(" AND tipoNotaFiscal in (").append(Uteis.splitFromArray(tipoNotaFiscal, false)).append(") \n");
        }
        if (!UteisValidacao.emptyNumber(empresa)) {
            sqlStr.append(" AND empresa = ").append(empresa).append(" \n");
        }
        sqlStr.append(" ORDER BY descricao");
        try (Statement stm = con.createStatement()) {
            try (ResultSet tabelaResultado = stm.executeQuery(sqlStr.toString())) {
                return montarDadosConsulta(tabelaResultado, nivelMontarDados, con);
            }
        }
    }

    public Map<Integer, ConfiguracaoNotaFiscalVO> consultarTodosMapa(Integer empresa, Boolean ativo, int nivelMontarDados) throws Exception {
        List<ConfiguracaoNotaFiscalVO> todos = consultarConfiguracaoNotaFiscal(empresa, null, null, ativo, nivelMontarDados);
        Map<Integer, ConfiguracaoNotaFiscalVO> mapa = new HashMap<Integer, ConfiguracaoNotaFiscalVO>();
        for (ConfiguracaoNotaFiscalVO notaVO : todos) {
            mapa.put(notaVO.getCodigo(), notaVO);
        }
        return mapa;
    }

    public void migrador() {
        try {
            SuperFacadeJDBC.executarUpdateExecutarProcessos("CREATE TABLE ConfiguracaoNotaFiscal\n" +
                    "(\n" +
                    "codigo serial PRIMARY KEY,\n" +
                    "empresa INTEGER,\n" +
                    "tipoNotaFiscal INTEGER,\n" +
                    "descricao CHARACTER VARYING(120),\n" +
                    "iss double precision,\n" +
                    "pis double precision,     \n" +
                    "cofins double precision,\n" +
                    "irrf double precision,\n" +
                    "valorMinimoIRRF double precision,\n" +
                    "codListaServico CHARACTER VARYING(20),\n" +
                    "codTributacaoMunicipal CHARACTER VARYING(20),\n" +
                    "cnae CHARACTER VARYING(50),\n" +
                    "exigibilidadeiss INTEGER,\n" +
                    "observacao TEXT,\n" +
                    "naturezaoperacao CHARACTER VARYING(255),\n" +
                    "cnpj CHARACTER VARYING(20),\n" +
                    "enviarNotaCidadeEmpresa boolean default false,\n" +
                    "pais INTEGER,\n" +
                    "estado INTEGER,\n" +
                    "cidade INTEGER,\n" +
                    "cpfObrigatorio boolean default true,\n" +
                    "emailObrigatorio boolean default true,\n" +
                    "enderecoObrigatorio boolean default true,\n" +
                    "serie CHARACTER VARYING(10),\n" +
                    "issRetido boolean default false,\n" +
                    "usarDescricaoFormaPagamento boolean default false,\n" +
                    "enviarObservacaoNaDescricao boolean default false,\n" +
                    "apresentarDuracaoPlano boolean default true,\n" +
                    "apresentarDescricaoParcela boolean default false,\n" +
                    "apresentarCompetencia boolean default false,\n" +
                    "ativo boolean default true, \n" +
                    "enotas boolean default false, \n" +
                    "ambienteEmissao INTEGER, \n" +
                    "idEnotas CHARACTER VARYING(255), \n" +
                    "dataCadastro timestamp without time zone, \n" +
                    "inscricaoMunicipal CHARACTER VARYING(50), \n" +
                    "inscricaoEstadual CHARACTER VARYING(50), \n" +
                    "razaoSocial CHARACTER VARYING(120), \n" +
                    "nomeFantasia CHARACTER VARYING(120), \n" +
                    "optanteSimplesNacional boolean default false,\n" +
                    "email CHARACTER VARYING(120), \n" +
                    "telefoneComercial CHARACTER VARYING(20), \n" +
                    "incentivadorCultural boolean default false,\n" +
                    "regimeEspecialTributacao CHARACTER VARYING(20),\n" +
                    "descricaoServico CHARACTER VARYING(255),\n" +
                    "enviarEmailCliente boolean default false,\n" +
                    "logradouro CHARACTER VARYING(255),\n" +
                    "numero CHARACTER VARYING(10),\n" +
                    "complemento CHARACTER VARYING(255),\n" +
                    "bairro CHARACTER VARYING(255),\n" +
                    "cep CHARACTER VARYING(15),\n" +
                    "chaveCertificado CHARACTER VARYING(255),\n" +
                    "senhaCertificado CHARACTER VARYING(50),\n" +
                    "formatoCertificado CHARACTER VARYING(5),\n" +
                    "chaveLogotipo CHARACTER VARYING(255),\n" +
                    "formatoLogotipo CHARACTER VARYING(5),\n" +
                    "CONSTRAINT fk_ConfiguracaoNotaFiscal_Empresa FOREIGN KEY (empresa) \n" +
                    "REFERENCES empresa (codigo) MATCH SIMPLE \n" +
                    "ON UPDATE NO ACTION ON DELETE RESTRICT);", con);


            SuperFacadeJDBC.executarUpdateExecutarProcessos("CREATE TABLE ConfiguracaoNotaFiscalAmbiente\n" +
                    "(\n" +
                    "codigo serial PRIMARY KEY,\n" +
                    "ConfiguracaoNotaFiscal INTEGER,\n" +
                    "ambienteEmissao INTEGER,\n" +
                    "serieNFe CHARACTER VARYING(120),\n" +
                    "sequencialNFe INTEGER,\n" +
                    "sequencialLoteNFe INTEGER,\n" +
                    "usuarioAcessoProvedor CHARACTER VARYING(255),\n" +
                    "senhaAcessoProvedor CHARACTER VARYING(255),\n" +
                    "tokenAcessoProvedor CHARACTER VARYING(255),\n" +
                    "idCSC CHARACTER VARYING(50),\n" +
                    "csc CHARACTER VARYING(120),\n" +
                    "CONSTRAINT fk_ConfiguracaoNotaFiscalAmbiente_ConfiguracaoNotaFiscal FOREIGN KEY (ConfiguracaoNotaFiscal) \n" +
                    "REFERENCES ConfiguracaoNotaFiscal (codigo) MATCH SIMPLE \n" +
                    "ON UPDATE NO ACTION ON DELETE CASCADE);", con);


            SuperFacadeJDBC.executarUpdateExecutarProcessos("CREATE TABLE NotaFiscal\n" +
                    "(\n" +
                    "codigo serial PRIMARY KEY,\n" +
                    "dataRegistro timestamp without time zone, \n" +
                    "idReferencia CHARACTER VARYING(255),\n" +
                    "idExterno CHARACTER VARYING(255),\n" +
                    "statusNota CHARACTER VARYING(255),\n" +
                    "motivostatus text,\n" +
                    "pessoa INTEGER,\n" +
                    "tipo INTEGER,\n" +
                    "empresa INTEGER,\n" +
                    "nfseEmitida INTEGER,\n" +
                    "notaFiscalConsumidorEletronica INTEGER,\n" +
                    "jsonEnvio text,\n" +
                    "jsonRetorno text,\n" +
                    "urlWebHook text,\n" +
                    "situacao INTEGER,\n" +
                    "configuracaoNotaFiscal INTEGER,\n" +
                    "CONSTRAINT fk_NotaFiscal_Pessoa FOREIGN KEY (pessoa) \n" +
                    "REFERENCES pessoa (codigo) MATCH SIMPLE \n" +
                    "ON UPDATE NO ACTION ON DELETE RESTRICT," +
                    "CONSTRAINT fk_NotaFiscal_Empresa FOREIGN KEY (empresa) \n" +
                    "REFERENCES empresa (codigo) MATCH SIMPLE \n" +
                    "ON UPDATE NO ACTION ON DELETE RESTRICT," +
                    "CONSTRAINT fk_NotaFiscal_ConfiguracaoNotaFiscal FOREIGN KEY (configuracaoNotaFiscal) \n" +
                    "REFERENCES configuracaoNotaFiscal (codigo) MATCH SIMPLE \n" +
                    "ON UPDATE NO ACTION ON DELETE RESTRICT" +
                    ");", con);

            SuperFacadeJDBC.executarUpdateExecutarProcessos("ALTER TABLE produto ADD COLUMN configuracaonotafiscalNFSe INTEGER;", con);
            SuperFacadeJDBC.executarUpdateExecutarProcessos("ALTER TABLE produto ADD CONSTRAINT fk_produto_configuracaonotafiscalNFSe FOREIGN KEY (configuracaonotafiscalNFSe) REFERENCES configuracaonotafiscal (codigo) ON UPDATE NO ACTION ON DELETE RESTRICT;", con);
            SuperFacadeJDBC.executarUpdateExecutarProcessos("ALTER TABLE produto ADD COLUMN configuracaonotafiscalNFCe INTEGER;", con);
            SuperFacadeJDBC.executarUpdateExecutarProcessos("ALTER TABLE produto ADD CONSTRAINT fk_produto_configuracaonotafiscalNFCe FOREIGN KEY (configuracaonotafiscalNFCe) REFERENCES configuracaonotafiscal (codigo) ON UPDATE NO ACTION ON DELETE RESTRICT;", con);

            SuperFacadeJDBC.executarUpdateExecutarProcessos("ALTER TABLE empresa ADD COLUMN configuracaonotafiscalNFSe INTEGER;", con);
            SuperFacadeJDBC.executarUpdateExecutarProcessos("ALTER TABLE empresa ADD CONSTRAINT fk_empresa_configuracaonotafiscalNFSe FOREIGN KEY (configuracaonotafiscalNFSe) REFERENCES configuracaonotafiscal (codigo) ON UPDATE NO ACTION ON DELETE RESTRICT;", con);
            SuperFacadeJDBC.executarUpdateExecutarProcessos("ALTER TABLE empresa ADD COLUMN configuracaonotafiscalNFCe INTEGER;", con);
            SuperFacadeJDBC.executarUpdateExecutarProcessos("ALTER TABLE empresa ADD CONSTRAINT fk_empresa_configuracaonotafiscalNFCe FOREIGN KEY (configuracaonotafiscalNFCe) REFERENCES configuracaonotafiscal (codigo) ON UPDATE NO ACTION ON DELETE RESTRICT;", con);

            SuperFacadeJDBC.executarUpdateExecutarProcessos("ALTER TABLE nfseemitida ADD COLUMN enotas BOOLEAN DEFAULT FALSE;", con);
            SuperFacadeJDBC.executarUpdateExecutarProcessos("ALTER TABLE nfseemitida ADD COLUMN resultadoEnvio text;", con);
            SuperFacadeJDBC.executarUpdateExecutarProcessos("ALTER TABLE nfseemitida ADD COLUMN situacaoNotaFiscal INTEGER;", con);

            SuperFacadeJDBC.executarUpdateExecutarProcessos("ALTER TABLE nfseemitidahistorico ADD COLUMN situacaoNotaFiscal INTEGER;", con);

            SuperFacadeJDBC.executarUpdateExecutarProcessos("update nfseemitida  set situacaonotafiscal = (status + 1) where situacaonotafiscal is null;", con);
            SuperFacadeJDBC.executarUpdateExecutarProcessos("update nfseemitidahistorico  set situacaonotafiscal = (select situacaonotafiscal from nfseemitida  where codigo =  nfseemitidahistorico.nfseemitida);", con);

            SuperFacadeJDBC.executarUpdateExecutarProcessos("ALTER TABLE nfseemitida ADD COLUMN configuracaonotafiscal INTEGER;", con);
            SuperFacadeJDBC.executarUpdateExecutarProcessos("ALTER TABLE nfseemitida ADD CONSTRAINT fk_nfseEmitida_configuracaonotafiscal FOREIGN KEY (configuracaonotafiscal) REFERENCES configuracaonotafiscal (codigo) ON UPDATE NO ACTION ON DELETE RESTRICT;", con);
            SuperFacadeJDBC.executarUpdateExecutarProcessos("ALTER TABLE nfseemitidahistorico ADD COLUMN configuracaonotafiscal INTEGER;", con);
            SuperFacadeJDBC.executarUpdateExecutarProcessos("ALTER TABLE nfseemitidahistorico ADD CONSTRAINT fk_nfseemitidahistorico_configuracaonotafiscal FOREIGN KEY (configuracaonotafiscal) REFERENCES configuracaonotafiscal (codigo) ON UPDATE NO ACTION ON DELETE RESTRICT;", con);


            SuperFacadeJDBC.executarUpdateExecutarProcessos("ALTER TABLE notafiscalconsumidoreletronica ADD COLUMN situacaoNotaFiscal INTEGER;", con);
            SuperFacadeJDBC.executarUpdateExecutarProcessos("ALTER TABLE notafiscalconsumidoreletronica ADD COLUMN enotas BOOLEAN DEFAULT FALSE;", con);
            SuperFacadeJDBC.executarUpdateExecutarProcessos("ALTER TABLE notafiscalconsumidoreletronica ADD COLUMN dataEnvio timestamp without time zone;", con);
            SuperFacadeJDBC.executarUpdateExecutarProcessos("ALTER TABLE notafiscalconsumidoreletronica ADD COLUMN resultadoEnvio text;", con);


            SuperFacadeJDBC.executarUpdateExecutarProcessos("ALTER TABLE notafiscalconsumidoreletronica ADD COLUMN configuracaonotafiscal INTEGER;", con);
            SuperFacadeJDBC.executarUpdateExecutarProcessos("ALTER TABLE notafiscalconsumidoreletronica ADD CONSTRAINT fk_nfseEmitida_configuracaonotafiscal FOREIGN KEY (configuracaonotafiscal) REFERENCES configuracaonotafiscal (codigo) ON UPDATE NO ACTION ON DELETE RESTRICT;", con);
            SuperFacadeJDBC.executarUpdateExecutarProcessos("ALTER TABLE notafiscalconsumidoreletronica ADD COLUMN idreferencia CHARACTER VARYING(255);", con);
            SuperFacadeJDBC.executarUpdateExecutarProcessos("ALTER TABLE notafiscalconsumidoreletronica ADD COLUMN pessoa Integer;", con);
            SuperFacadeJDBC.executarUpdateExecutarProcessos("ALTER TABLE notafiscalconsumidoreletronica ADD CONSTRAINT fk_notafiscalconsumidoreletronica_pessoa FOREIGN KEY (pessoa) REFERENCES pessoa (codigo) ON UPDATE NO ACTION ON DELETE RESTRICT;", con);

            SuperFacadeJDBC.executarUpdateExecutarProcessos("ALTER TABLE estado ADD COLUMN codigoibge character varying (20);", con);
            SuperFacadeJDBC.executarUpdateExecutarProcessos("ALTER TABLE cidade ADD COLUMN homologada boolean default false;", con);

            SuperFacadeJDBC.executarUpdateExecutarProcessos("ALTER TABLE ConfiguracaoSistema ADD COLUMN seqNotaFiscalFamilia INTEGER DEFAULT 0;", con);

            Estado estadoDAO = new Estado(con);
            estadoDAO.preencherCodigosIBGEEstado();
            estadoDAO = null;

            Cidade cidadeDAO = new Cidade(con);
            cidadeDAO.preencherCodigosIBGECidade();
            cidadeDAO = null;

            try (ResultSet rs = SuperFacadeJDBC.criarConsulta("select \n" +
                    "e.* , \n" +
                    "c.codigomunicipio as codIBGECidade \n" +
                    "from empresa e \n" +
                    "left join cidade c on c.codigo = e.cidade", con)) {

                while (rs.next()) {

                    Integer empresa = rs.getInt("codigo");

                    boolean empresaAtiva = rs.getBoolean("ativa");

                    if (!empresaAtiva) {
                        continue;
                    }

                    boolean usarnfse = rs.getBoolean("usarnfse");
                    boolean usarnfce = rs.getBoolean("usarnfce");

                    String inscricaoMunicipal = rs.getString("inscmunicipal");
                    String inscricaoEstadual = rs.getString("inscestadual");
                    String razaoSocial = rs.getString("razaosocial");
                    String nomeFantasia = rs.getString("nome");
                    String email = rs.getString("email");
                    String telefoneComercial = rs.getString("telcomercial1");
                    String logradouro = rs.getString("endereco");
                    String numero = rs.getString("numero");
                    String complemento = rs.getString("complemento");
                    String bairro = rs.getString("setor");
                    String cep = rs.getString("cep");

                    Double iss = rs.getDouble("iss");
                    String codigoListaServico = rs.getString("codigoListaServico");
                    String codigotributacaomunicipal = rs.getString("codigotributacaomunicipal");
                    Double aliquotapis = rs.getDouble("aliquotapis");
                    Double aliquotacofins = rs.getDouble("aliquotacofins");
                    Double aliquotairrf = rs.getDouble("aliquotairrf");
                    Double valorminimoirrf = rs.getDouble("valorminimoirrf");
                    Integer exigibilidadeiss = rs.getInt("exigibilidadeiss");
                    String observacaonfse = rs.getString("observacaonfse");
                    String observacaonfce = rs.getString("observacaonfce");
                    String naturezaOperacaoRPS = rs.getString("naturezaOperacaoRPS");
                    String naturezaOperacaoNFCe = rs.getString("naturezaOperacaoNFCe");
                    String codigocnae = rs.getString("codigocnae");
                    String codigonfcecnae = rs.getString("codigonfcecnae");

                    String serieRPS = rs.getString("serieRPS");
                    boolean issRetido = rs.getBoolean("issRetido");
                    boolean enviarNotaCidadeEmpresa = rs.getBoolean("enviarNotaCidadeEmpresa");
                    boolean enviarObservacaoNFSENaDescricao = rs.getBoolean("enviarObservacaoNFSENaDescricao");
                    String cnpj = rs.getString("cnpj");
                    Integer pais = rs.getInt("pais");
                    Integer estado = rs.getInt("estado");
                    Integer cidade = rs.getInt("cidade");
                    boolean cpfnfse = rs.getBoolean("cpfnfse");
                    boolean emailNFSe = rs.getBoolean("emailNFSe");
                    boolean enderecoNFSe = rs.getBoolean("enderecoNFSe");
                    boolean usarDescricaoFormaPagamentoNFCe = rs.getBoolean("usarDescricaoFormaPagamentoNFCe");
                    boolean enviarNFCeCidadeEmpresa = rs.getBoolean("enviarNFCeCidadeEmpresa");
                    boolean cpfobrigatorionfce = rs.getBoolean("cpfobrigatorionfce");
                    boolean enderecoObrigatorioNFCe = rs.getBoolean("enderecoObrigatorioNFCe");
                    boolean apresentarDuracaoPlanoNFSe = rs.getBoolean("apresentarDuracaoPlanoNFSe");
                    boolean usarDescricaoParcelaNFSe = rs.getBoolean("usarDescricaoParcelaNFSe");
                    Integer tipoGestaoNFSE = rs.getInt("tipogestaonfse");

                    for (TipoNotaFiscalEnum tipoNotaFiscalEnum : TipoNotaFiscalEnum.values()) {

                        if (tipoNotaFiscalEnum.equals(TipoNotaFiscalEnum.NFE)) {
                            continue;
                        }

                        List<ConfiguracaoNotaFiscalVO> lista = consultarConfiguracaoNotaFiscal(empresa, new Integer[]{tipoNotaFiscalEnum.getCodigo()}, true, null, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
                        if (!UteisValidacao.emptyList(lista)) {
                            System.out.println("JÁ EXISTE CONFIGURACAO DE NOTA FISCAL | " + tipoNotaFiscalEnum.getDescricao() + " | Empresa " + empresa);
                            continue;
                        }

                        String descricao = tipoNotaFiscalEnum.getDescricao().toUpperCase() + " - " + nomeFantasia;
                        if (descricao.length() > 120) {
                            descricao = descricao.substring(0, 120);
                        }

                        ConfiguracaoNotaFiscalVO novo = new ConfiguracaoNotaFiscalVO();
                        novo.setAtivo(true);
                        novo.setDescricao(descricao);
                        novo.setCnpj(cnpj);
                        novo.getEmpresaVO().setCodigo(empresa);
                        novo.getPaisVO().setCodigo(pais);
                        novo.getEstadoVO().setCodigo(estado);
                        novo.getCidadeVO().setCodigo(cidade);

                        novo.setInscricaoMunicipal(inscricaoMunicipal);
                        novo.setInscricaoEstadual(inscricaoEstadual);
                        novo.setRazaoSocial(razaoSocial);
                        novo.setNomeFantasia(nomeFantasia);
                        novo.setEmail(email);
                        novo.setTelefoneComercial(telefoneComercial);
                        novo.setLogradouro(logradouro);
                        novo.setNumero(numero);
                        novo.setComplemento(complemento);
                        novo.setBairro(bairro);
                        novo.setCep(cep);

                        ConfiguracaoNotaFiscalAmbienteVO confiProd = new ConfiguracaoNotaFiscalAmbienteVO(AmbienteEmissaoNotaFiscalEnum.PRODUCAO);
                        ConfiguracaoNotaFiscalAmbienteVO confiHomo = new ConfiguracaoNotaFiscalAmbienteVO(AmbienteEmissaoNotaFiscalEnum.HOMOLOGACAO);

                        novo.setConfigProducaoVO(confiProd);
                        novo.setConfigHomologacaoVO(confiHomo);

                        if (tipoNotaFiscalEnum.equals(TipoNotaFiscalEnum.NFCE)) {

                            novo.setTipoNotaFiscal(TipoNotaFiscalEnum.NFCE);
                            novo.setObservacao(observacaonfce);
                            novo.setNaturezaOperacao(naturezaOperacaoNFCe);
                            novo.setCnae(codigonfcecnae);
                            novo.setEnviarNotaCidadeEmpresa(enviarNFCeCidadeEmpresa);
                            novo.setCpfObrigatorio(cpfobrigatorionfce);
                            novo.setUsarDescricaoFormaPagamento(usarDescricaoFormaPagamentoNFCe);
                            novo.setEnderecoObrigatorio(enderecoObrigatorioNFCe);

                            if (!usarnfce) {
                                continue;
                            }

                        } else if (tipoNotaFiscalEnum.equals(TipoNotaFiscalEnum.NFSE)) {

                            novo.setTipoNotaFiscal(TipoNotaFiscalEnum.NFSE);
                            novo.setIss(iss);
                            novo.setPis(aliquotapis);
                            novo.setCofins(aliquotacofins);
                            novo.setIrrf(aliquotairrf);
                            novo.setValorMinimoIRRF(valorminimoirrf);
                            novo.setCodListaServico(codigoListaServico);
                            novo.setCodTributacaoMunicipal(codigotributacaomunicipal);
                            novo.setEnviarNotaCidadeEmpresa(enviarNotaCidadeEmpresa);
                            novo.setCpfObrigatorio(cpfnfse);
                            novo.setEmailObrigatorio(emailNFSe);
                            novo.setNaturezaOperacao(naturezaOperacaoRPS);
                            novo.setCnae(codigocnae);
                            novo.setExigibilidadeISS(TipoExigibilidadeISSEnum.getTipo(exigibilidadeiss));
                            novo.setObservacao(observacaonfse);
                            novo.setEnderecoObrigatorio(enderecoNFSe);
                            novo.setSerie(serieRPS);
                            novo.setIssRetido(issRetido);
                            novo.setEnviarObservacaoNaDescricao(enviarObservacaoNFSENaDescricao);
                            novo.setApresentarDuracaoPlano(apresentarDuracaoPlanoNFSe);
                            novo.setApresentarDescricaoParcela(usarDescricaoParcelaNFSe);
                            novo.setApresentarCompetencia(tipoGestaoNFSE.equals(TipoRelatorioDF.COMPETENCIA.getCodigo()));

                            if (!usarnfse) {
                                continue;
                            }
                        }

                        System.out.println("VOU CRIAR CONFIGURACAO DE NOTA FISCAL | " + descricao);
                        incluir(novo);

                        if (UteisValidacao.emptyNumber(novo.getCodigo())) {
                            throw new Exception("Erro ao MIGRAR informações de nota fiscal! " + descricao);
                        }

                        if (tipoNotaFiscalEnum.equals(TipoNotaFiscalEnum.NFSE)) {
                            SuperFacadeJDBC.executarUpdateExecutarProcessos("update empresa set configuracaonotafiscalNFSe = " + novo.getCodigo() + " where codigo = " + empresa, con);
                        } else if (tipoNotaFiscalEnum.equals(TipoNotaFiscalEnum.NFCE)) {
                            SuperFacadeJDBC.executarUpdateExecutarProcessos("update empresa set configuracaonotafiscalNFCe = " + novo.getCodigo() + " where codigo = " + empresa, con);
                        } else {
                            throw new Exception("Erro ao MIGRAR informações NFSe! " + descricao);
                        }

                    }
                }
            }
        } catch (Exception ex) {
            Uteis.logar(null, "Erro ConfiguracaoNotaFiscal MIGRADOR! | " + ex.getMessage());
        }
    }

    public String sincronizarEnotas(String chave, boolean enviarConfiguracoesAmbiente, ConfiguracaoNotaFiscalVO obj) {
        try {
            if (obj.isEnotas()) {
                EnotasService enotasService = new EnotasService(con);
                enotasService.incluirAtualizarEmpresa(enviarConfiguracoesAmbiente, obj);
                atualizarIdEnotas(obj);
                enotasService = null;
                return "Sincronizado com eNotas! ";
            } else {
                return "";
            }
        } catch (Exception ex) {
            return "Não foi sincronizado eNotas: " + ex.getMessage();
        }
    }

    public String enviarCertificadoEnotas(String chave, ConfiguracaoNotaFiscalVO obj) throws Exception {
        if (obj.isEnotas()) {
            EnotasService enotasService = new EnotasService(con);
            String msg = enotasService.enviarCertificado(chave, obj);
            enotasService = null;
            if (msg.toUpperCase().startsWith("ERRO:")) {
                throw new Exception(msg.replaceFirst("ERRO:", ""));
            }
            obterDataCertificado(obj);
            return "Certificado enviado! " + msg;
        } else {
            return "";
        }
    }

    public String enviarLogotipoEnotas(String chave, ConfiguracaoNotaFiscalVO obj) throws Exception {
        if (obj.isEnotas()) {
            EnotasService enotasService = new EnotasService(con);
            String msg = enotasService.enviarLogotipo(chave, obj);
            enotasService = null;
            if (msg.toUpperCase().startsWith("ERRO:")) {
                throw new Exception(msg.replaceFirst("ERRO:", ""));
            }
            return "Logotipo enviado! " + msg;
        } else {
            return "";
        }
    }

    public void obterInformacoesModuloNotas(String chave, ConfiguracaoNotaFiscalVO obj) throws Exception {
        EnotasService enotasService = new EnotasService(con);
        enotasService.obterInformacoesModuloNotas(chave, obj);
        enotasService = null;
    }

    public InfoCidadeEnotasTO obterInformacoesCidade(ConfiguracaoNotaFiscalVO obj) throws Exception {
        EnotasService enotasService = new EnotasService(con);
        InfoCidadeEnotasTO infoCidadeEnotasTO = enotasService.consultaInfoCidade(obj);
        enotasService = null;
        return infoCidadeEnotasTO;
    }

    public List<InfoServicoEnotasTO> obterInformacoesServico(ConfiguracaoNotaFiscalVO obj) throws Exception {
        EnotasService enotasService = new EnotasService(con);
        List<InfoServicoEnotasTO> lista = enotasService.consultaInfoServico(obj);
        enotasService = null;
        return lista;
    }

    private void atualizarIdEnotas(ConfiguracaoNotaFiscalVO obj) throws Exception {
        if (!UteisValidacao.emptyString(obj.getIdEnotas())) {
            String sql = "UPDATE configuracaoNotaFiscal SET idenotas = ? WHERE codigo = ?;";
            PreparedStatement sqlInserir = con.prepareStatement(sql);
            sqlInserir.setString(1, obj.getIdEnotas());
            sqlInserir.setInt(2, obj.getCodigo());
            sqlInserir.execute();
        }
    }

    public void alterarCertificado(ConfiguracaoNotaFiscalVO obj) throws Exception {
        String sql = "UPDATE configuracaoNotaFiscal SET formatoCertificado = ?, chaveCertificado = ? WHERE codigo = ?;";
        try (PreparedStatement sqlAlterar = con.prepareStatement(sql)) {
            sqlAlterar.setString(1, obj.getFormatoCertificado());
            sqlAlterar.setString(2, obj.getChaveCertificado());
            sqlAlterar.setInt(3, obj.getCodigo());
            sqlAlterar.execute();
        }
    }

    public void alterarLogotipo(ConfiguracaoNotaFiscalVO obj) throws Exception {
        String sql = "UPDATE configuracaoNotaFiscal SET formatoLogotipo = ?, chaveLogotipo = ? WHERE codigo = ?;";
        try (PreparedStatement sqlAlterar = con.prepareStatement(sql)) {
            sqlAlterar.setString(1, obj.getFormatoLogotipo());
            sqlAlterar.setString(2, obj.getChaveLogotipo());
            sqlAlterar.setInt(3, obj.getCodigo());
            sqlAlterar.execute();
        }
    }

    public static void main(String args[]) throws Exception {
        String chave = null;
        if (args.length > 0) {
            chave = args[0];
            Uteis.logar(null, "Obter conexão para chave: " + chave);
        } else {
            throw new Exception("Chave não informada");
        }

//        Connection con = DriverManager.getConnection("**************************************", "zillyonweb", "pactodb");

        Uteis.debug = true;
        Connection con = new DAO().obterConexaoEspecifica(chave);
        Conexao.guardarConexaoForJ2SE(chave, con);
        ConfiguracaoNotaFiscal configNotaDAO = new ConfiguracaoNotaFiscal(con);
        configNotaDAO.migrador();
    }

    public String atualizarDadosModuloNFe(ConfiguracaoNotaFiscalVO obj) throws Exception {
        if (obj.isEnotas()) {
            return "";
        }

        try {
            obj.setEmpresaVO(getFacade().getEmpresa().consultarPorChavePrimaria(obj.getEmpresaVO().getCodigo(), Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA));

            if (!obj.isEnotas() && (obj.getEmpresaVO().getUsarNFSe() || obj.getEmpresaVO().isUsarNFCe())) {
                JSONArray empresa = new JSONArray();
                JSONObject configNfse = new JSONObject();
                configNfse.put("iss", obj.getIss());
                configNfse.put("aliquotaPIS", obj.getPis());
                configNfse.put("aliquotaCOFINS", obj.getCofins());
                configNfse.put("aliquotaIRRF", obj.getIrrf());
                configNfse.put("codigoListaServico", obj.getCodListaServico());
                configNfse.put("codigoTributacaoMunicipio", obj.getCodTributacaoMunicipal());
                configNfse.put("codigoCNAE", obj.getCnae());
                configNfse.put("exigibilidadeISS", obj.getExigibilidadeISS().getId());

                JSONObject configNfce = new JSONObject();
                configNfce.put("codigoCNAE", obj.getCnae());

                JSONObject nfse = new JSONObject();
                nfse.put("nfse", configNfse);

                JSONObject nfce = new JSONObject();
                nfce.put("nfce", configNfce);

                empresa.put(nfse);
                empresa.put(nfce);

                Map<String, String> headers = new HashMap<String, String>();
                headers.put("Content-Type", "application/x-www-form-urlencoded");

                Map<String, String> corpo = new HashMap<String, String>();
                corpo.put("atualizarEmpresa", "true");
                corpo.put("chave", obj.getEmpresaVO().getChaveNFSe());
                corpo.put("params", empresa.toString());

                String urlConsultar = Uteis.getUrlModuloNFSe() + "/nota";
                String executeRequest = ExecuteRequestHttpService.executeHttpRequestGenerico(urlConsultar, headers, corpo, ExecuteRequestHttpService.METODO_POST, "ISO-8859-1", "ISO-8859-1");
                if (!UteisValidacao.emptyString(executeRequest)){
                    JSONObject jsonObject = new JSONObject(executeRequest);
                    if (jsonObject.has("return") && jsonObject.getString("return").toLowerCase().equals("sucesso")) {
                        return "Sincronizado com Módulo de Notas!";
                    } else {
                        throw new Exception(executeRequest);
                    }
                }
            }
        } catch (Exception ex) {
            throw new Exception("Sincronizar com Módulo de Notas: " + ex.getMessage());
        }
        return "";
    }

    public void obterDataCertificado(ConfiguracaoNotaFiscalVO obj) {
        try {
            if (UteisValidacao.emptyString(obj.getChaveCertificado()) || UteisValidacao.emptyString(obj.getSenhaCertificado())) {
                return;
            }

     //       InputStream arquivo2 = obterInputStreamDoKeyStore(Uteis.getPaintFotoDaNuvem(obj.getChaveCertificado()));

            InputStream arquivo = new FileInputStream(obj.getCaminhoCertificadoTemp());

            Date dataCertificado = null;

            KeyStore keystore = KeyStore.getInstance(("PKCS12"));
            keystore.load(arquivo, obj.getSenhaCertificado().toCharArray());

            Enumeration<String> eAliases = keystore.aliases();

            while (eAliases.hasMoreElements()) {
                String alias = eAliases.nextElement();

                Certificate certificado = (Certificate) keystore.getCertificate(alias);

                X509Certificate cert = (X509Certificate) certificado;

                dataCertificado = cert.getNotAfter();
                cert.checkValidity(cert.getNotAfter());
            }

            obj.setDataCertificado(dataCertificado);
        } catch (Exception ex) {
            obj.setDataCertificado(null);
        } finally {
            alterarDataCertificado(obj);
        }
    }

    private InputStream obterCertificado(ConfiguracaoNotaFiscalVO obj) {
        try {
            URL url = new URL(Uteis.getPaintFotoDaNuvem(obj.getChaveCertificado()));
            URLConnection urlConn = url.openConnection();
            urlConn.setConnectTimeout(10000);
            urlConn.setReadTimeout(10000);
            urlConn.setAllowUserInteraction(false);
            urlConn.setDoOutput(true);
            return urlConn.getInputStream();
        } catch (Exception ex) {
            return null;
        }
    }

    private void alterarDataCertificado(ConfiguracaoNotaFiscalVO obj) {
        try {
            String sql = "UPDATE ConfiguracaoNotaFiscal SET dataCertificado = ? WHERE codigo = ? ";
            try (PreparedStatement sqlAlterar = con.prepareStatement(sql)) {
                sqlAlterar.setTimestamp(1, Uteis.getDataJDBCTimestamp(obj.getDataCertificado()));
                sqlAlterar.setInt(2, obj.getCodigo());
                sqlAlterar.execute();
            }
        } catch (Exception ignored) {
        }
    }

    public InfoEmpresaEnotasTO consultarSituacaoEmpresaEnotas(ConfiguracaoNotaFiscalVO obj) throws Exception {
        InfoEmpresaEnotasTO infoEmpresaEnotasTO = new InfoEmpresaEnotasTO();
        if (obj.isEnotas()) {
            EnotasService enotasService = new EnotasService(con);
            infoEmpresaEnotasTO = enotasService.consultarSituacaoEmpresaEnotas(obj);
            enotasService = null;
        }
        return infoEmpresaEnotasTO;
    }

    public String obterLinkDownloadSetupSAT(ConfiguracaoNotaFiscalVO obj) throws Exception {
        if (obj.isEnotas()) {
            String link = "";
            EnotasService enotasService = new EnotasService(con);
            link = enotasService.obterLinkDownloadSetupSAT(obj);
            enotasService = null;
            return link;
        } else {
            throw new Exception("Configuração não utiliza eNotas");
        }
    }

    public String habilitarDesabilitarEmpresa(boolean isEnotas, String idEnotas) throws Exception {
        String retorno = "";
        EnotasService enotasService = new EnotasService(con);
        retorno = enotasService.habilitarDesabilitarEmpresa(isEnotas, idEnotas);
        enotasService = null;

        return retorno;
    }

    public void desabilitarEmpresaInativaEnotas(Integer idEmpresa) throws Exception {
        EnotasService enotasService = new EnotasService(con);
        for(String idEnotas : consultarIdEnotasConfiguracaoNotaFiscalEmpresa(idEmpresa)){
           enotasService.habilitarDesabilitarEmpresa(Boolean.FALSE, idEnotas);
        }
        enotasService = null;

    }

    public List<String> consultarIdEnotasConfiguracaoNotaFiscalEmpresa(Integer empresa) throws Exception {
        StringBuilder sqlStr = new StringBuilder();
        sqlStr.append(" select distinct idenotas  from configuracaonotafiscal ");
        sqlStr.append(" where empresa = ").append(empresa).append(" \n");
        sqlStr.append(" and idenotas is not null and enotas \n");

        List retorno = new ArrayList<String>();


        try (Statement stm = con.createStatement()) {
            try (ResultSet tabelaResultado = stm.executeQuery(sqlStr.toString())) {
                while (tabelaResultado.next()) {
                    retorno.add(tabelaResultado.getString("idenotas"));
                }
                return retorno;
            }
        }
    }

}

package negocio.facade.jdbc.basico;

import negocio.comuns.conviteaulaexperimental.TipoConviteAulaExperimentalModalidadeHorarioVO;
import negocio.comuns.conviteaulaexperimental.TipoConviteAulaExperimentalModalidadeVO;
import negocio.comuns.conviteaulaexperimental.TipoConviteAulaExperimentalVO;
import negocio.comuns.plano.ModalidadeVO;
import negocio.comuns.utilitarias.Uteis;
import negocio.facade.jdbc.arquitetura.SuperEntidade;
import negocio.interfaces.basico.TipoConviteAulaExperimentalModalidadeInterfaceFacade;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;

/**
 * Created by ulisses on 14/01/2016.
 */
public class TipoConviteAulaExperimentalModalidade extends SuperEntidade implements TipoConviteAulaExperimentalModalidadeInterfaceFacade {


    public TipoConviteAulaExperimentalModalidade() throws Exception {
        super();
    }

    public TipoConviteAulaExperimentalModalidade(Connection con) throws Exception {
        super(con);
    }

    public void incluirSemCommit(List<TipoConviteAulaExperimentalModalidadeVO> listaConviteModalidade)throws Exception{
        String sql = "insert into tipoConviteAulaExperimentalModalidade (tipoConviteAulaExperimental,modalidade) values(?,?)";
        PreparedStatement pst = con.prepareStatement(sql);
        for (TipoConviteAulaExperimentalModalidadeVO obj: listaConviteModalidade){
            pst.setInt(1,obj.getTipoConviteAulaExperimentalVO().getCodigo());
            pst.setInt(2,obj.getModalidadeVO().getCodigo());
            pst.execute();
            obj.setCodigo(obterValorChavePrimariaCodigo());
            obj.setNovoObj(false);
            for(TipoConviteAulaExperimentalModalidadeHorarioVO horario: obj.getListaHorario()){
                horario.setTipoConviteAulaExperimentalModalidadeVO(obj);
            }
            getFacade().getTipoConviteAulaExperimentalModalidadeHorario().incluirSemCommit(obj.getListaHorario_Apresentar());
        }
    }
    public void excluirSemComit(Integer tipoConviteAulaExperimental)throws Exception{
        getFacade().getTipoConviteAulaExperimentalModalidadeHorario().excluirSemCommit(tipoConviteAulaExperimental);
        String sql = "delete from tipoConviteAulaExperimentalModalidade where tipoConviteAulaExperimental = ?";
        PreparedStatement pst = con.prepareStatement(sql);
        pst.setInt(1, tipoConviteAulaExperimental);
        pst.execute();
    }

    private TipoConviteAulaExperimentalModalidadeVO montarDadosBasico(ResultSet rs)throws Exception{
        TipoConviteAulaExperimentalModalidadeVO obj = new TipoConviteAulaExperimentalModalidadeVO();
        obj.setCodigo(rs.getInt("codigo"));
        TipoConviteAulaExperimentalVO tipoConviteAulaExperimentalVO = new TipoConviteAulaExperimentalVO();
        tipoConviteAulaExperimentalVO.setCodigo(rs.getInt("tipoConviteAulaExperimental"));
        obj.setTipoConviteAulaExperimentalVO(tipoConviteAulaExperimentalVO);
        ModalidadeVO modalidadeVO = new ModalidadeVO();
        modalidadeVO.setCodigo(rs.getInt("modalidade"));
        obj.setModalidadeVO(modalidadeVO);
        return obj;
    }

    private TipoConviteAulaExperimentalModalidadeVO montarDados(ResultSet rs, int nivelMontarDados)throws Exception{
        TipoConviteAulaExperimentalModalidadeVO obj = montarDadosBasico(rs);

        if (nivelMontarDados == Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA){
            return  obj;
        }
        if (nivelMontarDados == Uteis.NIVELMONTARDADOS_DADOSBASICOS){
            return  obj;
        }
        if (nivelMontarDados == Uteis.NIVELMONTARDADOS_TODOS){
            obj.setModalidadeVO(getFacade().getModalidade().consultarPorChavePrimaria(obj.getModalidadeVO().getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS));
            List<TipoConviteAulaExperimentalModalidadeHorarioVO> listaHorario =getFacade().getTipoConviteAulaExperimentalModalidadeHorario().consultar(obj, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
            obj.setListaHorario(new HashSet<TipoConviteAulaExperimentalModalidadeHorarioVO>(listaHorario));
        }
        return obj;
    }

    private List<TipoConviteAulaExperimentalModalidadeVO> montarDadosConsulta(ResultSet rs, int nivelMontarDados)throws Exception{
        List<TipoConviteAulaExperimentalModalidadeVO> lista = new ArrayList<TipoConviteAulaExperimentalModalidadeVO>();
        while (rs.next()){
            TipoConviteAulaExperimentalModalidadeVO obj = montarDados(rs, nivelMontarDados);
            lista.add(obj);
        }
        return lista;
    }

    public List<TipoConviteAulaExperimentalModalidadeVO> consultar(TipoConviteAulaExperimentalVO tipoConviteAulaExperimentalVO, int nivelMontarDados)throws Exception{
        String sql = "select * from tipoConviteAulaExperimentalModalidade where tipoConviteAulaExperimental = ?";
        PreparedStatement pst = con.prepareStatement(sql);
        pst.setInt(1, tipoConviteAulaExperimentalVO.getCodigo());
        ResultSet rs = pst.executeQuery();
        return montarDadosConsulta(rs, nivelMontarDados);
    }


}

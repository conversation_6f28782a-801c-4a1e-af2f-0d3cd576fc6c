package negocio.facade.jdbc.basico;

import negocio.comuns.basico.ClassificacaoVO;
import negocio.comuns.utilitarias.ConsistirException;
import negocio.comuns.utilitarias.Ordenacao;
import negocio.comuns.utilitarias.Uteis;
import negocio.facade.jdbc.arquitetura.SuperEntidade;
import negocio.interfaces.basico.ClassificacaoInterfaceFacade;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.sql.Statement;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

/**
 * Classe de persistência que encapsula todas as operações de manipulação dos dados da classe <code>ClassificacaoVO</code>.
 * Responsável por implementar operações como incluir, alterar, excluir e consultar pertinentes a classe <code>ClassificacaoVO</code>.
 * Encapsula toda a interação com o banco de dados.
 * @see ClassificacaoVO
 * @see SuperEntidade
 */
public class Classificacao extends SuperEntidade implements ClassificacaoInterfaceFacade {

    public Classificacao() throws Exception {
        super();
    }

    public Classificacao(Connection con) throws Exception {
        super(con);
    }

    /**
     * Operação responsável por retornar um novo objeto da classe <code>ClassificacaoVO</code>.
     */
    public ClassificacaoVO novo() throws Exception {
        incluir(getIdEntidade());
        return new ClassificacaoVO();
    }

    /**
     * Operação responsável por incluir no banco de dados um objeto da classe <code>ClassificacaoVO</code>.
     * Primeiramente valida os dados (<code>validarDados</code>) do objeto. Verifica a conexão com o banco de dados e a permissão do usuário
     * para realizar esta operacão na entidade.
     * Isto, através da operação <code>incluir</code> da superclasse.
     * @param obj  Objeto da classe <code>ClassificacaoVO</code> que será gravado no banco de dados.
     * @exception Exception Caso haja problemas de conexão, restrição de acesso ou validação de dados.
     */
    public void incluir(ClassificacaoVO obj) throws Exception {
        ClassificacaoVO.validarDados(obj);
        incluir(getIdEntidade());
        obj.realizarUpperCaseDados();
        String sql = "INSERT INTO Classificacao( nome, enviarSMSAutomatico ) VALUES ( ? ,?)";
        try (PreparedStatement sqlInserir = con.prepareStatement(sql)) {
            sqlInserir.setString(1, obj.getNome());
            sqlInserir.setBoolean(2, obj.getEnviarSMSAutomatico());
            sqlInserir.execute();
        }
        obj.setCodigo(obterValorChavePrimariaCodigo());
        obj.setNovoObj(false);
    }

    /**
     * Operação responsável por alterar no BD os dados de um objeto da classe <code>ClassificacaoVO</code>.
     * Sempre utiliza a chave primária da classe como atributo para localização do registro a ser alterado.
     * Primeiramente valida os dados (<code>validarDados</code>) do objeto. Verifica a conexão com o banco de dados e a permissão do usuário
     * para realizar esta operacão na entidade.
     * Isto, através da operação <code>alterar</code> da superclasse.
     * @param obj    Objeto da classe <code>ClassificacaoVO</code> que será alterada no banco de dados.
     * @exception Exception Caso haja problemas de conexão, restrição de acesso ou validação de dados.
     */
    public void alterar(ClassificacaoVO obj) throws Exception {
        try {
            con.setAutoCommit(false);
            alterarSemCommit(obj);
            con.commit();
        } catch (Exception e) {
            con.rollback();
            con.setAutoCommit(true);
            throw e;
        } finally {
            con.setAutoCommit(true);
        }
    }

    public void alterarSemCommit(ClassificacaoVO obj) throws Exception {
            ClassificacaoVO.validarDados(obj);
            alterar(getIdEntidade());
            obj.realizarUpperCaseDados();
            String sql = "UPDATE Classificacao set nome=?,enviarSMSAutomatico=?  WHERE ((codigo = ?))";
        try (PreparedStatement sqlAlterar = con.prepareStatement(sql)) {
            sqlAlterar.setString(1, obj.getNome());
            sqlAlterar.setBoolean(2, obj.getEnviarSMSAutomatico());
            sqlAlterar.setInt(3, obj.getCodigo());
            sqlAlterar.execute();
        }
        //Atualizar atributo 'enviarSMSAutomatico' em SituacaoClienteSinteticoDW
            final String trueOrFalse = obj.getEnviarSMSAutomatico() ? "true" : "false";
            executarConsulta(
                    new StringBuilder().append("update situacaoclientesinteticodw  set enviosmsmarcadoclassif = ").
                    append(trueOrFalse).append(" ").
                    append("where codigocliente in (select cc.cliente from clienteclassificacao cc ").
                    append("inner join classificacao c on c.codigo = cc.classificacao ").
                    append("where cc.classificacao = ").append(obj.getCodigo()).append(")").toString(), con);
        }

    /**
     * Operação responsável por excluir no BD um objeto da classe <code>ClassificacaoVO</code>.
     * Sempre localiza o registro a ser excluído através da chave primária da entidade.
     * Primeiramente verifica a conexão com o banco de dados e a permissão do usuário
     * para realizar esta operacão na entidade.
     * Isto, através da operação <code>excluir</code> da superclasse.
     * @param obj    Objeto da classe <code>ClassificacaoVO</code> que será removido no banco de dados.
     * @exception Exception Caso haja problemas de conexão ou restrição de acesso.
     */
    public void excluir(ClassificacaoVO obj) throws Exception {
        try {
            con.setAutoCommit(false);
            excluir(getIdEntidade());
            String sql = "DELETE FROM Classificacao WHERE ((codigo = ?))";
            try (PreparedStatement sqlExcluir = con.prepareStatement(sql)) {
                sqlExcluir.setInt(1, obj.getCodigo());
                sqlExcluir.execute();
            }
            con.commit();
        } catch (Exception e) {
            con.rollback();
            con.setAutoCommit(true);
            throw e;
        } finally {
            con.setAutoCommit(true);
        }
    }

    /**
     * Responsável por realizar uma consulta de <code>Classificacao</code> através do valor do atributo 
     * <code>String nome</code>. Retorna os objetos, com início do valor do atributo idêntico ao parâmetro fornecido.
     * Faz uso da operação <code>montarDadosConsulta</code> que realiza o trabalho de prerarar o List resultante.
     * @param   controlarAcesso Indica se a aplicação deverá verificar se o usuário possui permissão para esta consulta ou não.
     * @return  List Contendo vários objetos da classe <code>ClassificacaoVO</code> resultantes da consulta.
     * @exception Exception Caso haja problemas de conexão ou restrição de acesso.
     */
    public List consultarPorNome(String valorConsulta, boolean controlarAcesso, int nivelMontarDados) throws Exception {
        consultar(getIdEntidade(), controlarAcesso);
        String sqlStr = "SELECT * FROM Classificacao WHERE upper( nome ) like('" + valorConsulta.toUpperCase() + "%') ORDER BY nome";
        try (Statement stm = con.createStatement()) {
            try (ResultSet tabelaResultado = stm.executeQuery(sqlStr)) {
                return (montarDadosConsulta(tabelaResultado, nivelMontarDados));
            }
        }
    }

    /**
     * Responsável por realizar uma consulta de <code>Classificacao</code> através do valor do atributo 
     * <code>Integer codigo</code>. Retorna os objetos com valores iguais ou superiores ao parâmetro fornecido.
     * Faz uso da operação <code>montarDadosConsulta</code> que realiza o trabalho de prerarar o List resultante.
     * @param   controlarAcesso Indica se a aplicação deverá verificar se o usuário possui permissão para esta consulta ou não.
     * @return  List Contendo vários objetos da classe <code>ClassificacaoVO</code> resultantes da consulta.
     * @exception Exception Caso haja problemas de conexão ou restrição de acesso.
     */
    public List consultarPorCodigo(Integer valorConsulta, boolean controlarAcesso, int nivelMontarDados) throws Exception {
        consultar(getIdEntidade(), controlarAcesso);
        String sqlStr = "SELECT * FROM Classificacao WHERE codigo >= " + valorConsulta + " ORDER BY codigo";
        try (Statement stm = con.createStatement()) {
            try (ResultSet tabelaResultado = stm.executeQuery(sqlStr)) {
                return (montarDadosConsulta(tabelaResultado, nivelMontarDados));
            }
        }
    }
    
    public ClassificacaoVO consultarPorCodigoExato(Integer valorConsulta, boolean controlarAcesso, int nivelMontarDados) throws Exception {
        consultar(getIdEntidade(), controlarAcesso);
        String sql = "SELECT * FROM Classificacao WHERE codigo = ?";
        ClassificacaoVO classificacao;
        try (PreparedStatement sqlConsultar = con.prepareStatement(sql)) {
            sqlConsultar.setInt(1, valorConsulta);
            try (ResultSet tabelaResultado = sqlConsultar.executeQuery()) {
                classificacao = null;
                while (tabelaResultado.next()) {
                    classificacao = montarDados(tabelaResultado, nivelMontarDados);
                }
            }
        }
        return classificacao;
    }

    /**
     * Responsável por montar os dados de vários objetos, resultantes de uma consulta ao banco de dados (<code>ResultSet</code>).
     * Faz uso da operação <code>montarDados</code> que realiza o trabalho para um objeto por vez.
     * @return  List Contendo vários objetos da classe <code>ClassificacaoVO</code> resultantes da consulta.
     */
    public static List montarDadosConsulta(ResultSet tabelaResultado, int nivelMontarDados) throws Exception {
        List vetResultado = new ArrayList();
        while (tabelaResultado.next()) {
            ClassificacaoVO obj = montarDados(tabelaResultado, nivelMontarDados);
            vetResultado.add(obj);
        }
        return vetResultado;
    }

    /**
     * Responsável por montar os dados resultantes de uma consulta ao banco de dados (<code>ResultSet</code>)
     * em um objeto da classe <code>ClassificacaoVO</code>.
     * @return  O objeto da classe <code>ClassificacaoVO</code> com os dados devidamente montados.
     */
    public static ClassificacaoVO montarDados(ResultSet dadosSQL, int nivelMontarDados) throws Exception {
        ClassificacaoVO obj = new ClassificacaoVO();
        obj.setCodigo(new Integer(dadosSQL.getInt("codigo")));
        obj.setNome(dadosSQL.getString("nome"));
        obj.setEnviarSMSAutomatico(dadosSQL.getBoolean("enviarSMSAutomatico"));
        obj.setNovoObj(false);
        if (nivelMontarDados == Uteis.NIVELMONTARDADOS_DADOSBASICOS) {
            return obj;
        }

        return obj;
    }

    /**
     * Operação responsável por localizar um objeto da classe <code>ClassificacaoVO</code>
     * através de sua chave primária. 
     * @exception Exception Caso haja problemas de conexão ou localização do objeto procurado.
     */
    public ClassificacaoVO consultarPorChavePrimaria(Integer codigoPrm, int nivelMontarDados) throws Exception {
        consultar(getIdEntidade(), false);
        String sql = "SELECT * FROM Classificacao WHERE codigo = ?";
        try (PreparedStatement sqlConsultar = con.prepareStatement(sql)) {
            sqlConsultar.setInt(1, codigoPrm);
            try (ResultSet tabelaResultado = sqlConsultar.executeQuery()) {
                if (!tabelaResultado.next()) {
                    throw new ConsistirException("Dados Não Encontrados ( Classificacao ).");
                }
                return (montarDados(tabelaResultado, nivelMontarDados));
            }
        }
    }

    public String consultarJSON() throws Exception {
        StringBuilder json;
        boolean dados;
        try (ResultSet rs = getPS().executeQuery()) {
            json = new StringBuilder();
            json.append("{\"aaData\":[");
            dados = false;
            while (rs.next()) {
                dados = true;
                json.append("[\"").append(rs.getString("codigo")).append("\",");
                json.append("\"").append(Uteis.normalizarStringJSON(rs.getString("nome"))).append("\",");
                if (rs.getBoolean("enviarsmsautomatico")) {
                    json.append("\"Sim\"],");
                } else {
                    json.append("\"Não\"],");
                }
            }
        }
        if(dados){
            json.deleteCharAt(json.toString().length()-1);
        }
        json.append("]}");
        return json.toString();
    }

    private PreparedStatement getPS() throws SQLException {
        String sql = "SELECT codigo, nome, enviarsmsautomatico FROM classificacao ORDER BY nome";
        return con.prepareStatement(sql);
    }

    public List consultarParaImpressao(String filtro, String ordem, String campoOrdenacao, int i) throws SQLException {
        List lista;
        try (ResultSet rs = getPS().executeQuery()) {
            lista = new ArrayList();
            while (rs.next()) {
                ClassificacaoVO cla = new ClassificacaoVO();
                String geral = rs.getString("codigo") + rs.getString("nome") + rs.getString("enviarsmsautomatico");
                if (geral.toLowerCase().contains(filtro.toLowerCase())) {
                    cla.setCodigo(rs.getInt("codigo"));
                    cla.setNome(rs.getString("nome"));
                    cla.setEnviarSMSAutomatico(rs.getBoolean("enviarsmsautomatico"));
                    lista.add(cla);
                }
            }
        }
        if (campoOrdenacao.equals("Código")) {
            Ordenacao.ordenarLista(lista, "codigo");
        } else if (campoOrdenacao.equals("Descrição")) {
            Ordenacao.ordenarLista(lista, "nome");
        } else if (campoOrdenacao.equals("SMS Automático")) {
            Ordenacao.ordenarLista(lista, "enviarSMSAutomatico_Apresentar");
        }
        if (ordem.contains("desc")) {
            Collections.reverse(lista);
        }
        return lista;

    }
}

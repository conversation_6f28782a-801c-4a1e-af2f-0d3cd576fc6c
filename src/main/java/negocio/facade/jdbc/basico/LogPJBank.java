package negocio.facade.jdbc.basico;

import negocio.comuns.financeiro.LogPJBankVO;
import negocio.interfaces.financeiro.LogPJBankInterfaceFacade;
import negocio.comuns.utilitarias.Uteis;
import negocio.facade.jdbc.arquitetura.SuperEntidade;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.util.ArrayList;
import java.util.List;

public class LogPJBank extends SuperEntidade implements LogPJBankInterfaceFacade {

    public LogPJBank() throws Exception {
        super();
    }

    public LogPJBank(Connection conexao) throws Exception {
        super(conexao);
    }

    public static List<LogPJBankVO> montarDadosConsulta(ResultSet tabelaResultado,
            int nivelMontarDados, Connection con) throws Exception {
        List<LogPJBankVO> vetResultado = new ArrayList<LogPJBankVO>();
        while (tabelaResultado.next()) {
            LogPJBankVO obj = montarDados(tabelaResultado, nivelMontarDados, con);
            vetResultado.add(obj);
        }
        return vetResultado;
    }

    public static LogPJBankVO montarDadosBasico(ResultSet dadosSQL) throws Exception {
        LogPJBankVO obj = new LogPJBankVO();
        obj.setNovoObj(false);
        obj.setCodigo(dadosSQL.getInt("codigo"));
//        obj.setBoletoPJBank(dadosSQL.getInt("boletopjbank"));
        obj.setDataRegistroLog(Uteis.getDataJDBCTimestamp(dadosSQL.getTimestamp("datalog")));
        obj.setDescricao(dadosSQL.getString("descricao"));
        return obj;
    }

    public static LogPJBankVO montarDados(ResultSet dadosSQL, int nivelMontarDados, Connection con) throws Exception {
        return montarDadosBasico(dadosSQL);
    }

    public void incluir(LogPJBankVO obj) throws Exception {
        String sql = "INSERT INTO logspjbank(boletopjbank, datalog, descricao) VALUES (?, ?, ?)";
        try (PreparedStatement pst = con.prepareStatement(sql)) {
            int i = 1;
            pst.setInt(i++, obj.getBoletoPJBank());
            pst.setTimestamp(i++, Uteis.getDataJDBCTimestamp(obj.getDataRegistroLog()));
            pst.setString(i++, obj.getDescricao());
            pst.execute();
        }
        obj.setNovoObj(false);
    }

    public List<LogPJBankVO> consultarTodos(int nivelMontarDados) throws Exception {
        List<LogPJBankVO> objetos = new ArrayList<LogPJBankVO>();
        String sql = "SELECT * FROM logspjbank ORDER BY datalog desc";
        PreparedStatement sqlConsulta = con.prepareStatement(sql);
        ResultSet resultado = sqlConsulta.executeQuery();
        while (resultado.next()) {
            LogPJBankVO novoObj = montarDados(resultado, nivelMontarDados, con);
            objetos.add(novoObj);
        }
        return objetos;
    }
}

package negocio.facade.jdbc.basico;

import negocio.comuns.basico.LogControleUsabilidadeVO;
import negocio.comuns.basico.LogIntegracoesVO;
import negocio.comuns.utilitarias.Uteis;
import negocio.facade.jdbc.arquitetura.SuperEntidade;
import negocio.interfaces.basico.LogIntegracoesInterfaceFacade;

import java.sql.Connection;
import java.sql.PreparedStatement;

public class LogIntegracoes extends SuperEntidade implements LogIntegracoesInterfaceFacade {
    public LogIntegracoes(Connection con) throws Exception {
        super(con);
    }

    @Override
    public void incluir(LogIntegracoesVO obj) throws Exception {
        try {
            String sql = "INSERT INTO logIntegracoes(servico, dadosRecebidos, dataLancamento, resultado, chaveprimaria) VALUES ( ?, ?, ?, ?, ?)";
            PreparedStatement sqlInserir = con.prepareStatement(sql);
            int i = 1;
            sqlInserir.setString(i++, obj.getServico());
            sqlInserir.setString(i++, obj.getDadosRecebidos());
            sqlInserir.setTimestamp(i++, Uteis.getDataJDBCTimestamp(obj.getDataLancamento()));
            sqlInserir.setString(i++, obj.getResultado());
            resolveStringNull(sqlInserir, i++, obj.getChavePrimaria());
            sqlInserir.execute();
            obj.setCodigo(this.obterValorChavePrimariaCodigo());
            obj.setNovoObj(false);
        } catch (Exception e) {
            throw e;
        } finally {
        }
    }

    @Override
    public void alterar(LogIntegracoesVO obj) throws Exception {
        try {
            con.setAutoCommit(false);
            String sql = "UPDATE logIntegracoes set servico=?, dadosRecebidos=?, dataLancamento=?, resultado=? WHERE ((codigo = ?))";
            PreparedStatement sqlAlterar = con.prepareStatement(sql);
            int i = 1;
            sqlAlterar.setString(i++, obj.getServico());
            sqlAlterar.setString(i++, obj.getDadosRecebidos());
            sqlAlterar.setTimestamp(i++, Uteis.getDataJDBCTimestamp(obj.getDataLancamento()));
            sqlAlterar.setString(i++, obj.getResultado());
            sqlAlterar.setInt(i++, obj.getCodigo());
            sqlAlterar.execute();
            con.commit();
        } catch (Exception e) {
            con.rollback();
            con.setAutoCommit(true);
            throw e;
        } finally {
            con.setAutoCommit(true);
        }
    }
}

package negocio.facade.jdbc.basico;

import negocio.comuns.basico.AsaasEmpresaVO;
import negocio.comuns.financeiro.enumerador.AmbienteEnum;
import negocio.comuns.financeiro.enumerador.CompanyTypeAsaasEnum;
import negocio.comuns.utilitarias.Uteis;
import negocio.facade.jdbc.arquitetura.SuperEntidade;
import negocio.interfaces.basico.AsaasEmpresaInterfaceFacade;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.Statement;

/**
 * Created with IntelliJ IDEA.
 * User: <PERSON>
 * Date: 25/05/2023
 */

public class AsaasEmpresa extends SuperEntidade implements AsaasEmpresaInterfaceFacade {

    public AsaasEmpresa() throws Exception {
        super();
    }

    public AsaasEmpresa(Connection conexao) throws Exception {
        super(conexao);
    }

    public void incluir(AsaasEmpresaVO obj) throws Exception {
        String sql = "INSERT INTO public.AsaasEmpresa(empresa, id, apiKey, walletId, ambiente, dataCriacao, paramsEnvio, paramsResposta, companyType)\n"
                + " VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)";

        try (PreparedStatement sqlInserir = con.prepareStatement(sql)) {
            int i = 1;
            sqlInserir.setInt(i++, obj.getEmpresa());
            sqlInserir.setString(i++, obj.getId());
            sqlInserir.setString(i++, obj.getApiKey());
            sqlInserir.setString(i++, obj.getWalletId());
            sqlInserir.setInt(i++, obj.getAmbienteEnum().getCodigo());
            sqlInserir.setDate(i++, Uteis.getDataJDBC(obj.getDataCriacao()));
            sqlInserir.setString(i++, obj.getParamsEnvio());
            sqlInserir.setString(i++, obj.getParamsResposta());
            sqlInserir.setInt(i++, obj.getCompanyType().getCodigo());

            sqlInserir.execute();
        }
    }

    public AsaasEmpresaVO consultar(int empresa) throws Exception {
        try {
            String sql = "SELECT * FROM asaasempresa WHERE empresa = " + empresa;
            try (Statement stm = con.createStatement()) {
                try (ResultSet rs = stm.executeQuery(sql)) {
                    while (rs.next()) {
                        return montarDados(rs);
                    }
                }
            }
            return null;
        } catch (Exception ex) {
            throw ex;
        }
    }

    public void excluir(String id) throws Exception {
        try {
            con.setAutoCommit(false);
            String sql = "DELETE FROM asaasempresa WHERE id = '" + id + "'";
            PreparedStatement sqlExcluir = con.prepareStatement(sql);
            sqlExcluir.execute();
            con.commit();
        } catch (Exception e) {
            con.rollback();
            con.setAutoCommit(true);
            throw e;
        } finally {
            con.setAutoCommit(true);
        }
    }

    public String consultarId(int empresa) throws Exception {
        try {
            String sql = "SELECT id FROM asaasempresa WHERE empresa = " + empresa + " limit 1";
            try (Statement stm = con.createStatement()) {
                try (ResultSet rs = stm.executeQuery(sql)) {
                    while (rs.next()) {
                        return rs.getString("id");
                    }
                }
            }
            return "";
        } catch (Exception ex) {
            throw ex;
        }
    }

    public int obterAmbiente(int empresa) throws Exception {
        try {
            String sql = "SELECT ambiente FROM asaasempresa WHERE empresa = " + empresa + " limit 1";
            try (Statement stm = con.createStatement()) {
                try (ResultSet rs = stm.executeQuery(sql)) {
                    while (rs.next()) {
                        return rs.getInt("ambiente");
                    }
                }
            }
            return 0;
        } catch (Exception ex) {
            throw ex;
        }
    }

    public String consultarApiKey(int empresa) throws Exception {
        try {
            String sql = "SELECT apikey FROM asaasempresa WHERE empresa = " + empresa + " limit 1";
            try (Statement stm = con.createStatement()) {
                try (ResultSet rs = stm.executeQuery(sql)) {
                    while (rs.next()) {
                        return rs.getString("apikey");
                    }
                }
            }
            return "";
        } catch (Exception ex) {
            throw ex;
        }
    }


    public static AsaasEmpresaVO montarDados(ResultSet dadosSQL) throws Exception {
        AsaasEmpresaVO obj = new AsaasEmpresaVO();
        obj.setCodigo(dadosSQL.getInt("codigo"));
        obj.setId(dadosSQL.getString("id"));
        obj.setEmpresa(dadosSQL.getInt("empresa"));
        obj.setApiKey(dadosSQL.getString("apiKey"));
        obj.setWalletId(dadosSQL.getString("walletId"));
        obj.setDataCriacao(dadosSQL.getTimestamp("dataCriacao"));
        obj.setAmbienteEnum(AmbienteEnum.consultarPorCodigo(dadosSQL.getInt("ambiente")));
        obj.setParamsEnvio(dadosSQL.getString("paramsEnvio"));
        obj.setParamsResposta(dadosSQL.getString("paramsResposta"));
        obj.setCompanyType(CompanyTypeAsaasEnum.obterPorCodigo(dadosSQL.getInt("companyType")));
        return obj;
    }

}

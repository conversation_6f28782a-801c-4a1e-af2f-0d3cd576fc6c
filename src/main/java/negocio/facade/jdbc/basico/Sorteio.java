package negocio.facade.jdbc.basico;

import org.json.JSONArray;
import org.json.JSONObject;
import negocio.comuns.basico.SorteioVO;
import negocio.comuns.utilitarias.Uteis;
import negocio.facade.jdbc.arquitetura.SuperEntidade;
import negocio.interfaces.basico.SorteioInterfaceFacade;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;

/**
 * Created by GlaucoT on 16/02/2016
 */
public class Sorteio extends SuperEntidade implements SorteioInterfaceFacade {
    public Sorteio() throws Exception {
    }

    public Sorteio(Connection conexao) throws Exception {
        super(conexao);
    }

    public void incluir(SorteioVO obj) throws Exception {
        String sql = "INSERT INTO sorteio(cliente, datasorteio, usuario, observacoes) VALUES (?, ?, ?, ?)";
        try (PreparedStatement sqlInserir = con.prepareStatement(sql)) {
            sqlInserir.setInt(1, obj.getClienteVO().getCodigo());
            sqlInserir.setTimestamp(2, Uteis.getDataJDBCTimestamp(obj.getDataSorteio()));
            sqlInserir.setInt(3, obj.getUsuarioVO().getCodigo());
            sqlInserir.setString(4, obj.getObservacoes());
            sqlInserir.execute();
        }
        obj.setCodigo(obterValorChavePrimariaCodigo());
        obj.setNovoObj(false);
    }


    private PreparedStatement getPS() throws SQLException {
        String sql = "SELECT s.codigo, p.nome as cliente, s.datasorteio, s.observacoes, u.nome as usuario FROM sorteio s\n" +
                "LEFT JOIN cliente c ON s.cliente = c.codigo\n" +
                "LEFT JOIN pessoa p ON p.codigo = c.pessoa\n" +
                "LEFT JOIN usuario u ON s.usuario = u.codigo;";
        return con.prepareStatement(sql);
    }

    public String consultarJSON() throws Exception {
        JSONObject aaData;
        JSONArray valores;
        try (ResultSet rs = getPS().executeQuery()) {
            aaData = new JSONObject();
            valores = new JSONArray();
            while (rs.next()) {
                JSONArray itemArray = new JSONArray();

                itemArray.put(rs.getInt("codigo"));
                itemArray.put(rs.getString("cliente"));
                itemArray.put(Uteis.getDataComHHMM(rs.getTimestamp("datasorteio")));
                itemArray.put(rs.getString("observacoes").replaceAll("\n", "<br/>"));
                itemArray.put(rs.getString("usuario"));
                valores.put(itemArray);
            }
        }

        aaData.put("aaData", valores);

        return aaData.toString();
    }

}

package negocio.facade.jdbc.basico;

import br.com.pactosolucoes.agendatotal.json.AgendamentoDesmarcadoJSON;
import br.com.pactosolucoes.ce.comuns.enumerador.DiaSemana;
import br.com.pactosolucoes.comuns.util.JSFUtilities;
import br.com.pactosolucoes.enumeradores.OrigemSistemaEnum;
import br.com.pactosolucoes.enumeradores.TipoOperacaoCreditoTreinoEnum;
import negocio.comuns.arquitetura.UsuarioVO;
import negocio.comuns.basico.AulaDesmarcadaVO;
import negocio.comuns.basico.ReposicaoVO;
import negocio.comuns.contrato.ContratoVO;
import negocio.comuns.contrato.ControleCreditoTreinoVO;
import negocio.comuns.contrato.MatriculaAlunoHorarioTurmaVO;
import negocio.comuns.contrato.OperacaoColetivaVO;
import negocio.comuns.contrato.StatusOperacaoColetivaEnum;
import negocio.comuns.plano.HorarioTurmaVO;
import negocio.comuns.utilitarias.Calendario;
import negocio.comuns.utilitarias.ConsistirException;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisValidacao;
import negocio.facade.jdbc.arquitetura.SuperEntidade;
import negocio.facade.jdbc.arquitetura.Usuario;
import negocio.facade.jdbc.contrato.ControleCreditoTreino;
import negocio.facade.jdbc.contrato.MatriculaAlunoHorarioTurma;
import negocio.facade.jdbc.contrato.OperacaoColetiva;
import negocio.facade.jdbc.plano.HorarioTurma;
import negocio.interfaces.basico.AulaDesmarcadaInterfaceFacade;
import negocio.interfaces.contrato.ControleCreditoTreinoInterfaceFacade;
import relatorio.negocio.jdbc.sad.SituacaoClienteSinteticoDW;
import servicos.impl.gestaoaula.GestaoAulaService;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.Statement;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;

public class AulaDesmarcada extends SuperEntidade implements AulaDesmarcadaInterfaceFacade {

    public AulaDesmarcada() throws Exception {
        super();
    }

    public AulaDesmarcada(Connection conexao) throws Exception {
        super(conexao);
    }

    public void validarDados(AulaDesmarcadaVO obj) throws Exception {

        if (obj.getDataOrigem() == null) {
            throw new ConsistirException("Data de Origem Aula Desmarcada deve ser informada.");
        }
        if (obj.getHorarioTurmaVO() == null || obj.getHorarioTurmaVO().getCodigo() == 0) {
            throw new ConsistirException("Horário Turma para a Aula Desmarcada deve ser informado.");
        }
        if (obj.getTurmaVO() == null || obj.getTurmaVO().getCodigo() == 0) {
            throw new ConsistirException("Turma de Aula Desmarcada deve ser informada.");
        }
        if (obj.getClienteVO() == null || obj.getClienteVO().getCodigo() == 0) {
            throw new ConsistirException("Cliente deve ser informado.");
        }
        if (obj.getContratoVO() == null || obj.getContratoVO().getCodigo() == 0) {
            throw new ConsistirException("Contrato deve ser informado.");
        }
        if (obj.getEmpresaVO() == null || obj.getEmpresaVO().getCodigo() == 0) {
            throw new ConsistirException("Contrato deve ser informado.");
        }

        if (Calendario.getInstance(obj.getDataOrigem()).get(Calendar.DAY_OF_WEEK) != obj.getHorarioTurmaVO().getDiaSemanaNumero()) {
            throw new ConsistirException(String.format("Você deve escolher uma data de um(a): %s, para a data da Aula Desmarcada",
                    DiaSemana.getDiaSemanaNumeral(obj.getHorarioTurmaVO().getDiaSemanaNumero()).getDescricao()));
        }

        if (Calendario.menor(obj.getDataOrigem(), Calendario.hoje())) {
            throw new ConsistirException("Aula desmarcada não pode ser retroativa!");
        }

        if (!(Calendario.maiorOuIgual(obj.getDataOrigem(), obj.getContratoVO().getVigenciaDe()) &&
                Calendario.menorOuIgual(obj.getDataOrigem(), obj.getContratoVO().getVigenciaAteAjustada()))) {
            throw new ConsistirException(String.format("A Data da aula Desmarcada deve "
                            + "estar dentro da Vigência do Contrato: de %s à %s.",
                    obj.getContratoVO().getVigenciaDe_Apresentar(),
                    obj.getContratoVO().getVigenciaAteAjustada_Apresentar()));
        }

    }

    public AulaDesmarcadaVO consultarPorCodigo(Integer codigo, int nivelMontarDados) throws Exception{
        String sql = "select * from auladesmarcada where codigo = ?";
        PreparedStatement pst = con.prepareStatement(sql);
        pst.setInt(1, codigo);
        ResultSet rs = pst.executeQuery();
        if (rs.next())
          return  montarDados(rs,nivelMontarDados);
        return null;
    }

    public void incluirSemCommit(final AulaDesmarcadaVO obj) throws Exception{
        final String sqlInsert = "insert into auladesmarcada (empresa, cliente, contrato, turma, horarioturma, datalancamento, dataorigem, datareposicao, usuario," +
                "origemSistema,desmarcadaPorAfastamento, permiteReporAulaDesmarcada, turmadestino,operacaocoletiva, justificativa) values (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)";
        PreparedStatement sqlInserir = con.prepareStatement(sqlInsert);

        int i = 0;
        sqlInserir.setInt(++i, obj.getEmpresaVO().getCodigo());
        sqlInserir.setInt(++i, obj.getClienteVO().getCodigo());
        sqlInserir.setInt(++i, obj.getContratoVO().getCodigo());
        sqlInserir.setInt(++i, obj.getTurmaVO().getCodigo());
        sqlInserir.setInt(++i, obj.getHorarioTurmaVO().getCodigo());
        sqlInserir.setTimestamp(++i, Uteis.getDataJDBCTimestamp(obj.getDataLancamento()));
        sqlInserir.setDate(++i, Uteis.getDataJDBC(obj.getDataOrigem()));
        sqlInserir.setDate(++i, Uteis.getDataJDBC(obj.getDataReposicao()));
        resolveIntegerNull(sqlInserir, ++i, obj.getUsuarioVO().getCodigo());
        sqlInserir.setInt(++i, obj.getOrigemSistemaEnum().getCodigo());
        sqlInserir.setBoolean(++i, obj.isDesmarcadaPorAfastamento());
        sqlInserir.setBoolean(++i, obj.isPermiteReporAulaDesmarcada());
        if(UteisValidacao.emptyNumber(obj.getTurmaDestino())){
            sqlInserir.setNull(++i, 0);
        }else{
            sqlInserir.setInt(++i, obj.getTurmaDestino());
        }

        resolveIntegerNull(sqlInserir, ++i, obj.getOperacaoColetiva().getCodigo());
        sqlInserir.setString(++i, obj.getJustificativa());
        sqlInserir.execute();
        obj.setCodigo(obterValorChavePrimariaCodigo());
        obj.setNovoObj(false);

        if ((obj.getContratoVO().isVendaCreditoTreino()) && (obj.isPermiteReporAulaDesmarcada())){
            ControleCreditoTreino controleCreditoTreino = new ControleCreditoTreino(this.con);
            boolean existeDebitoParaEssaAula = controleCreditoTreino.existeDescontoParaAulaDesmarcada(obj);
            boolean descontouCreditoAoMarcarAula = controleCreditoTreino.descontouCreditoAoMarcarAula(obj.getClienteVO().getCodigo(), obj.getHorarioTurmaVO(), obj.getDataOrigem());
            ControleCreditoTreinoVO controleCreditoTreinoVO = new ControleCreditoTreinoVO();
            controleCreditoTreinoVO.setTipoOperacaoCreditoTreinoEnum(TipoOperacaoCreditoTreinoEnum.DESMARCOU_AULA);
            controleCreditoTreinoVO.setQuantidade(0);
            controleCreditoTreinoVO.setContratoVO(obj.getContratoVO());
            controleCreditoTreinoVO.setAulaDesmarcadaVO(obj);
            controleCreditoTreinoVO.setUsuarioVO(obj.getUsuarioVO());
            if (obj.isDesmarcadaPorAfastamento()){
                controleCreditoTreinoVO.setObservacao("AULA DESMARCADA AUTOMATICAMENTE PELO SISTEMA EM DECORRÊNCIA DO AFASTAMENTO DO ALUNO.");
            }
            if(existeDebitoParaEssaAula || descontouCreditoAoMarcarAula){
                controleCreditoTreinoVO.setObservacao(controleCreditoTreinoVO.getObservacao()+" Aula havia sido descontada por FALTA/UTILIZAÇÃO/MARCAÇÃO antes de ser desmarcada");
                controleCreditoTreinoVO.setQuantidade(1);
            }
            SituacaoClienteSinteticoDW scsDWDao = new SituacaoClienteSinteticoDW(con);
            controleCreditoTreino.incluirSemCommit(controleCreditoTreinoVO,null, scsDWDao, null);
            controleCreditoTreino = null;
        }
    }

    public void incluir(final AulaDesmarcadaVO obj) throws Exception {
        try {
            con.setAutoCommit(false);
            incluirSemCommit(obj);
            con.commit();
        } catch (Exception e) {
            con.rollback();
            throw e;
        } finally {
            con.setAutoCommit(true);
            obj.setCodigo(obterValorChavePrimariaCodigo());
            obj.setNovoObj(false);
        }
    }

    public void alterar(final AulaDesmarcadaVO obj) throws Exception {
        final String sqlUpdate = "update auladesmarcada set empresa=?, cliente=?, contrato=?, turma=?, horarioturma=?, datalancamento=?, dataorigem=?, datareposicao=?, usuario=?,operacaocoletiva=? where codigo=?";
        PreparedStatement sqlAlterar= con.prepareStatement(sqlUpdate);
        int i = 0;
        sqlAlterar.setInt(++i, obj.getEmpresaVO().getCodigo());
        sqlAlterar.setInt(++i, obj.getClienteVO().getCodigo());
        sqlAlterar.setInt(++i, obj.getContratoVO().getCodigo());
        sqlAlterar.setInt(++i, obj.getTurmaVO().getCodigo());
        sqlAlterar.setInt(++i, obj.getHorarioTurmaVO().getCodigo());
        sqlAlterar.setTimestamp(++i, Uteis.getDataJDBCTimestamp(obj.getDataLancamento()));
        sqlAlterar.setDate(++i, Uteis.getDataJDBC(obj.getDataOrigem()));
        sqlAlterar.setDate(++i, Uteis.getDataJDBC(obj.getDataReposicao()));
        resolveIntegerNull(sqlAlterar, ++i, obj.getUsuarioVO().getCodigo());
        resolveIntegerNull(sqlAlterar, ++i, obj.getOperacaoColetiva().getCodigo());
        sqlAlterar.setInt(++i, obj.getCodigo().intValue());
        sqlAlterar.execute();
    }

     public void excluirSemCommit(final AulaDesmarcadaVO obj) throws Exception {
        ControleCreditoTreinoInterfaceFacade controleCreditoDao = new ControleCreditoTreino(con);
        controleCreditoDao.ajustarOperacaoExcluirAulaDesmarcada(obj);
        final String sql = "DELETE FROM auladesmarcada WHERE codigo = ?";
        PreparedStatement sqlExcluir = con.prepareStatement(sql);
        sqlExcluir.setInt(1, obj.getCodigo());
        sqlExcluir.execute();
    }


    public void atualizarDataReposicao(final AulaDesmarcadaVO obj) throws Exception {
        final String sqlUpdate = "update auladesmarcada set datareposicao = ?, reposicao = ? where codigo=?";
        PreparedStatement sqlAlterar= con.prepareStatement(sqlUpdate);
        int i = 0;
        sqlAlterar.setDate(++i, Uteis.getDataJDBC(obj.getDataReposicao()));
        sqlAlterar.setInt(++i, obj.getReposicaoVO().getCodigo());
        sqlAlterar.setInt(++i, obj.getCodigo());
        sqlAlterar.execute();
    }


    public Integer nrAlunosReposicao(HorarioTurmaVO obj, final String periodo) throws Exception {

        ResultSet rsSairam = criarConsulta(String.format(
                "SELECT COUNT(ad.codigo) AS qtde FROM auladesmarcada ad "
                        + "inner JOIN horarioturma ht ON ht.codigo = ad.horarioturma "
                        + "WHERE ad.horarioturma = %s AND "
                        + "datalancamento between %s and datareposicao is null ",
                new Object[]{
                        obj.getCodigo(),
                        periodo
                }), con);
        if (rsSairam.next()) {
            return rsSairam.getInt("qtde");
        }
        return 0;
    }

    public Integer consultarTotalAulasDesmarcadas(Integer codigoEmpresa, Integer codigoHorarioTurma, Date data, Set<Integer> codClientes) throws Exception{
        StringBuilder sql = new StringBuilder();
        if(codClientes != null){
            sql.append("SELECT  ad.cliente from aulaDesmarcada  ad").append("\n");
        }else{
            sql.append("SELECT  count(distinct(ad.cliente)) as total from aulaDesmarcada  ad").append("\n");
        }
        sql.append("        LEFT JOIN (Select max(codigo),horarioturma,cliente,datareposicao,codigo from reposicao rep \n" );
        sql.append("GROUP BY rep.horarioturma,rep.cliente,rep.datareposicao,rep.codigo order by rep.codigo desc limit 1) as rep ON rep.horarioTurma = ad.horarioTurma and rep.cliente = ad.cliente \n");
        sql.append("where ad.empresa =  ? and ad.horarioTurma = ? and ad.dataOrigem = ? and (  (rep.codigo = 0 or rep.codigo is null) or  not rep.datareposicao > ad.datareposicao)\n");

        PreparedStatement pst = con.prepareStatement(sql.toString());
        pst.setInt(1, codigoEmpresa);
        pst.setInt(2, codigoHorarioTurma);
        pst.setDate(3, Uteis.getDataJDBC(Calendario.getDataComHoraZerada(data)));
        ResultSet rs = pst.executeQuery();
        Integer total = 0;
        if(codClientes != null){
            while (rs.next()){
                codClientes.add(rs.getInt("cliente"));
                total++;
            }
        }else {
            if (rs.next()) {
                total = rs.getInt("total");
            }
        }
        return total;
    }


    public List<AulaDesmarcadaVO> consultarPorCliente(int cliente, int empresa) throws Exception {
        StringBuilder sql;
        PreparedStatement pst;
        ResultSet tabelaResultado;
        try{
            sql = new StringBuilder("select * from auladesmarcada where cliente = ? and empresa = ?");
            pst = con.prepareStatement(sql.toString());
            pst.setInt(1, cliente);
            pst.setInt(2, empresa);
            tabelaResultado = pst.executeQuery();
            return montarDadosConsulta(tabelaResultado, Uteis.NIVELMONTARDADOS_TODOS);
        }catch (Exception e){
            throw e;
        }finally {
            sql = null;
            tabelaResultado = null;
            pst = null;
        }
    }

    public boolean existeAulaDesmarcadaSemReporParaHorarioTurma(int codigoHorarioTurma) throws Exception {
        StringBuilder sql;
        try{
            sql = new StringBuilder("select * from auladesmarcada where horarioturma = %s");
            return existe(String.format(sql.toString(), codigoHorarioTurma), con);
        }catch (Exception e){
            throw e;
        }finally {
            sql = null;
        }
    }


    public Integer contarAulasDesmarcadas(int contrato, int empresa, int turma, int horarioTurma, Date dia, Date datainicio, Date datafim) throws Exception {
        StringBuilder sql;
        PreparedStatement pst;
        ResultSet tabelaResultado;
        try{
            sql = new StringBuilder("select count(codigo) as aulas from auladesmarcada ");
            if(dia == null){
                sql.append(" where datareposicao is null  ");
                if(datainicio != null){
                    sql.append(" and dataorigem::date >= ? ");
                }
                if(datafim != null){
                    sql.append(" and dataorigem::date <= ? ");
                }
            }else{
                sql.append(" where dataorigem BETWEEN ? AND ? ");
            }
            if(!UteisValidacao.emptyNumber(contrato)){
                sql.append(" and contrato = ? ");
            }
            if(!UteisValidacao.emptyNumber(turma)){
                sql.append(" and ( turma = ? or turmadestino = ? )");
            }
            if(!UteisValidacao.emptyNumber(empresa)){
                sql.append(" and empresa = ? ");
            }
            if(!UteisValidacao.emptyNumber(horarioTurma)){
                sql.append(" and horarioturma = ? ");
            }
            sql.append(" and permiteReporAulaDesmarcada = true ");
            sql.append(" and contratoanterior is null");
            pst = con.prepareStatement(sql.toString());
            int i = 1;
            if(dia != null){
                pst.setTimestamp(i++, Uteis.getDataHoraJDBC(dia, "00:00:00"));
                pst.setTimestamp(i++, Uteis.getDataHoraJDBC(dia, "23:59:59"));
            } else {
                if(datainicio != null){
                    pst.setDate(i++, Uteis.getDataJDBC(datainicio));
                }
                if(datafim != null){
                    pst.setDate(i++, Uteis.getDataJDBC(datafim));
                }
            }
            if(!UteisValidacao.emptyNumber(contrato)){
                pst.setInt(i++, contrato);
            }
            if(!UteisValidacao.emptyNumber(turma)){
                pst.setInt(i++, turma);
                pst.setInt(i++, turma);
            }
            if(!UteisValidacao.emptyNumber(empresa)){
                pst.setInt(i++, empresa);
            }

            if(!UteisValidacao.emptyNumber(horarioTurma)){
               pst.setInt(i++, horarioTurma);
            }

            tabelaResultado = pst.executeQuery();
            if (tabelaResultado.next()){
                return tabelaResultado.getInt("aulas");
            }
            return 0;
        }catch (Exception e){
            throw e;
        }finally {
            sql = null;
            tabelaResultado = null;
            pst = null;
        }
    }

    public Map<Date, Integer> contarAulasDesmarcadas(Integer horarioTurma, Date datainicio, Date datafim) throws Exception {
        Map<Date, Integer> desmarcacoesPorDia = new HashMap<>();
        String sql = "select dataorigem::date, count(codigo) as aulas from auladesmarcada "
                + " where dataorigem BETWEEN ? AND ? " +
                " and horarioturma = ? " +
                " and permiteReporAulaDesmarcada = true " +
                " group by dataorigem ";
        try (PreparedStatement pst = con.prepareStatement(sql)) {
            int i = 1;
            pst.setDate(i++, Uteis.getDataJDBC(datainicio));
            pst.setDate(i++, Uteis.getDataJDBC(datafim));
            pst.setInt(i++, horarioTurma);
            try (ResultSet rs = pst.executeQuery()) {
                while (rs.next()) {
                    Date dia = rs.getDate("dataorigem");
                    Integer aulas = rs.getInt("aulas");

                    desmarcacoesPorDia.put(dia, aulas);
                }
            }
        }
        return desmarcacoesPorDia;
    }


    public List<AulaDesmarcadaVO> consultarListaAulasDesmarcadas(int contrato, int empresa, int turma, int nivelMontarDados) throws Exception {
        StringBuilder sql;
        PreparedStatement pst;
        ResultSet tabelaResultado;
        try{
            sql = new StringBuilder("select * from auladesmarcada where permiteReporAulaDesmarcada = true and contrato = ? and datareposicao is null ");
            if(!UteisValidacao.emptyNumber(turma)){
                sql.append(" and ( turma = ? or turmadestino = ? )");
            }
            if(!UteisValidacao.emptyNumber(empresa)){
                sql.append("  and empresa = ?  ");
            }
            sql.append(" and contratoanterior is null ");
            sql.append("  order by dataorigem ");
            pst = con.prepareStatement(sql.toString());
            int  i = 1;
            pst.setInt(i++, contrato);
            if(!UteisValidacao.emptyNumber(turma)){
                 pst.setInt(i++, turma);
                 pst.setInt(i++, turma);
            }
            if(!UteisValidacao.emptyNumber(empresa)){
                pst.setInt(i++, empresa);
            }


            tabelaResultado = pst.executeQuery();
            return montarDadosConsulta(tabelaResultado,nivelMontarDados);
        }catch (Exception e){
            throw e;
        }finally {
            sql = null;
            tabelaResultado = null;
            pst = null;
        }
    }

    public AulaDesmarcadaVO consultar(Integer codigoContrato, Integer codigoHorarioTurma, Date dataOrigem, int nivelMontarDados)throws Exception{
        StringBuilder sql = new StringBuilder();
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
        sql.append("select * \n");
        sql.append("from auladesmarcada \n");
        sql.append("where contrato = ").append(codigoContrato).append(" and \n");
        sql.append("horarioTurma = ").append(codigoHorarioTurma).append(" and dataOrigem = '").append(sdf.format(dataOrigem)).append("'");
        Statement st = con.createStatement();
        ResultSet rs = st.executeQuery(sql.toString());
        if (rs.next()){
            return montarDados(rs,nivelMontarDados);
        }
        return null;
    }

    public AulaDesmarcadaVO consultarAulaDesmarcadaPorDiaHorarioContrato(int cliente, int empresa, int contrato, int turma, int horarioTurma, Date dataOrigem, int nivelMontarDados, boolean permiteReposicao, boolean aindaNaoReposta) throws Exception {
        AulaDesmarcadaVO aula = new AulaDesmarcadaVO();
        StringBuilder sql = new StringBuilder("select * from auladesmarcada where cliente=? and empresa=? and contrato=? and turma=? and horarioturma=? and dataorigem::date = '").append(Uteis.getDataFormatoBD(dataOrigem)).append("'");
        if (permiteReposicao) {
            sql.append(" AND permitereporauladesmarcada = 't' ");
        }
        if (aindaNaoReposta) {
            sql.append(" AND datareposicao is null ");
        }
        try (PreparedStatement pst = con.prepareStatement(sql.toString());) {
            pst.setInt(1, cliente);
            pst.setInt(2, empresa);
            pst.setInt(3, contrato);
            pst.setInt(4, turma);
            pst.setInt(5, horarioTurma);
            try (ResultSet tabelaResultado = pst.executeQuery()) {
                if (tabelaResultado.next()) {
                    aula = montarDados(tabelaResultado, nivelMontarDados);
                }
            }
        }
        return aula;
    }

    public AulaDesmarcadaVO consultarAulaDesmarcadaPorDiaHorarioContratoOrigemEReposicao(int cliente,
                                                                                        int empresa,
                                                                                        int contrato,
                                                                                        Integer contratoOrigem,
                                                                                        int turma,
                                                                                        int horarioTurma,
                                                                                        Date dataOrigem,
                                                                                        int nivelMontarDados) throws Exception {
        AulaDesmarcadaVO aula = new AulaDesmarcadaVO();
        StringBuilder sql = new StringBuilder();
        sql.append("select * \n");
        sql.append("from auladesmarcada \n");
        sql.append("where cliente=? \n");
        sql.append("and empresa=? \n");
        sql.append("and turma=? \n");
        sql.append("and horarioturma=? \n");
        if (contratoOrigem == null) {
            sql.append("and (contrato=? or contratoanterior=?) \n");
        } else {
            sql.append("and ((contrato=? or contratoanterior=?) or (contrato=? or contratoanterior=?))  \n");
        }
        sql.append("and dataorigem::date = '").append(Uteis.getDataFormatoBD(dataOrigem)).append("'");

        try (PreparedStatement pst = con.prepareStatement(sql.toString());) {
            pst.setInt(1, cliente);
            pst.setInt(2, empresa);
            pst.setInt(3, turma);
            pst.setInt(4, horarioTurma);
            if (contratoOrigem == null) {
                pst.setInt(5, contrato);
                pst.setInt(6, contrato);
            } else {
                pst.setInt(5, contrato);
                pst.setInt(6, contrato);
                pst.setInt(7, contratoOrigem);
                pst.setInt(8, contratoOrigem);
            }
            try (ResultSet tabelaResultado = pst.executeQuery()) {
                if (tabelaResultado.next()) {
                    aula = montarDados(tabelaResultado, nivelMontarDados);
                }
            }
        }
        return aula;
    }

    public List<AulaDesmarcadaVO> montarDadosConsulta(ResultSet tabelaResultado, int nivelMontarDados) throws Exception {
        List<AulaDesmarcadaVO> vetResultado = new ArrayList<AulaDesmarcadaVO>();
        while (tabelaResultado.next()) {
            AulaDesmarcadaVO obj = montarDados(tabelaResultado,nivelMontarDados);
            vetResultado.add(obj);
        }
        return vetResultado;
    }


    public AulaDesmarcadaVO montarDados(ResultSet dadosSQL, int nivelMontarDados) throws Exception{
        AulaDesmarcadaVO obj = new AulaDesmarcadaVO();
        obj.setCodigo(dadosSQL.getInt("codigo"));
        obj.getEmpresaVO().setCodigo(dadosSQL.getInt("empresa"));
        obj.getClienteVO().setCodigo(dadosSQL.getInt("cliente"));
        obj.getContratoVO().setCodigo(dadosSQL.getInt("contrato"));
        obj.getTurmaVO().setCodigo(dadosSQL.getInt("turma"));
        obj.getHorarioTurmaVO().setCodigo(dadosSQL.getInt("horarioturma"));
        obj.getUsuarioVO().setCodigo(dadosSQL.getInt("usuario"));
        obj.setDataLancamento(dadosSQL.getTimestamp("datalancamento"));
        obj.setDataOrigem(dadosSQL.getDate("dataorigem"));
        obj.setDataReposicao(dadosSQL.getDate("datareposicao"));
        obj.setOrigemSistemaEnum(OrigemSistemaEnum.getOrigemSistema(dadosSQL.getInt("origemSistema")));
        obj.setReposicaoVO(new ReposicaoVO());
        obj.getReposicaoVO().setCodigo(dadosSQL.getInt("reposicao"));
        obj.setDesmarcadaPorAfastamento(dadosSQL.getBoolean("desmarcadaPorAfastamento"));
        obj.setPermiteReporAulaDesmarcada(dadosSQL.getBoolean("permiteReporAulaDesmarcada"));
        obj.setNovoObj(false);
        obj.getContratoAnterior().setCodigo(dadosSQL.getInt("contratoanterior"));
        obj.getOperacaoColetiva().setCodigo(dadosSQL.getInt("operacaocoletiva"));
        if (nivelMontarDados == Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA){
            return obj;
        }
        montarHorarioTurma(obj,nivelMontarDados);
        montarUsuario(obj);
        montarOperacaoColetiva(obj,nivelMontarDados);

        return obj;
    }

    public AulaDesmarcadaVO consultarAulaDesmarcada(HorarioTurmaVO horarioTurmaOrigem, Date dataOrigem, Integer codigoContrato, int nivelMontarDados)throws Exception{
        StringBuilder sql = new StringBuilder();
        sql.append("select *  \n");
        sql.append("from auladesmarcada \n");
        sql.append("where contrato = ? and horarioTurma = ? \n");
        sql.append("and dataOrigem = ? ");
        PreparedStatement pst = con.prepareStatement(sql.toString());
        pst.setInt(1, codigoContrato);
        pst.setInt(2, horarioTurmaOrigem.getCodigo());
        pst.setDate(3, Uteis.getDataJDBC(Calendario.getDataComHoraZerada(dataOrigem)));
        ResultSet rs = pst.executeQuery();
        if (rs.next()){
            return montarDados(rs, nivelMontarDados);
        }
        return null;

    }

    public void montarHorarioTurma(AulaDesmarcadaVO obj, int nivelMontarDados) throws Exception{
        HorarioTurma horarioTurmaDao = new HorarioTurma(con);
        obj.setHorarioTurmaVO(horarioTurmaDao.consultarPorChavePrimaria(obj.getHorarioTurmaVO().getCodigo(), nivelMontarDados));
        horarioTurmaDao = null;
    }

    public void montarUsuario(AulaDesmarcadaVO obj) throws Exception{
        Usuario usuarioDao = new Usuario(con);
        try {
            obj.setUsuarioVO(usuarioDao.consultarPorChavePrimaria(obj.getUsuarioVO().getCodigo(), Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA));
        }catch (Exception e) {}
        usuarioDao = null;
    }

    public void montarOperacaoColetiva(AulaDesmarcadaVO obj, int nivelMontarDados) throws Exception{
        OperacaoColetiva operacaoColetiva = new OperacaoColetiva(con);
        obj.setOperacaoColetiva(operacaoColetiva.consultarPorChavePrimaria(obj.getOperacaoColetiva().getCodigo(), nivelMontarDados));
        operacaoColetiva = null;
    }
    @Override
    public AulaDesmarcadaVO consultarAulaDesmarcadaPorDataReposicaoHorarioContrato(int cliente, int empresa, int contrato, int turma, int horarioTurma, Date dataReposicao,int nivelMontarDados) throws Exception {
        StringBuilder sql;
        PreparedStatement pst;
        ResultSet tabelaResultado;
        AulaDesmarcadaVO aula = new AulaDesmarcadaVO();
        try{
            sql = new StringBuilder("select * from auladesmarcada where cliente=? and empresa=? and contrato=? and turma=? and horarioturma=? and datareposicao::date = '").append(Uteis.getData(dataReposicao)).append("'");
            pst = con.prepareStatement(sql.toString());
            pst.setInt(1, cliente);
            pst.setInt(2, empresa);
            pst.setInt(3, contrato);
            pst.setInt(4, turma);
            pst.setInt(5, horarioTurma);
            tabelaResultado = pst.executeQuery();
            if(tabelaResultado.next()){
               aula = montarDados(tabelaResultado, nivelMontarDados);
            }
        }catch (Exception e){
            throw e;
        }finally {
            sql = null;
            tabelaResultado = null;
            pst = null;
        }
        return aula;
    }

    @Override
    public List<AgendamentoDesmarcadoJSON> consultarAgendamentosDesmarcados(Date inicio, Date fim, Integer empresa)throws Exception{
        StringBuilder sql = new StringBuilder();
        sql.append("select cliente, dataorigem, datareposicao, horarioturma, contrato, justificativa from auladesmarcada ad inner join contrato con on con.codigo = ad.contrato  \n");
        sql.append(" where ad.empresa = ? and   ad.dataorigem BETWEEN ? and ?  \n");
        sql.append(" and  (ad.dataorigem::date   <=  con.vigenciaateajustada::date \n"); // para que contratos renovados antecipadamente não sejam afetados por aulas desmarcadas do contrato passado
        sql.append(" or (contratoresponsavelrenovacaomatricula = 0 and contratoresponsavelrematriculamatricula = 0))");//contratos trancados são considerados nessa condição
        PreparedStatement pst = con.prepareStatement(sql.toString());
        pst.setInt(1, empresa);
        pst.setTimestamp(2, Uteis.getDataJDBCTimestamp(Calendario.getDataComHoraZerada(inicio)));
        pst.setTimestamp(3, Uteis.getDataJDBCTimestamp(Calendario.getDataComHora(fim, "23:59:59")));
        
        ResultSet rs = pst.executeQuery();
        List<AgendamentoDesmarcadoJSON> lista = new ArrayList<AgendamentoDesmarcadoJSON>();
        while (rs.next()){
            AgendamentoDesmarcadoJSON json = new AgendamentoDesmarcadoJSON();
            json.setCodigoCliente(rs.getInt("cliente"));
            json.setReposto(rs.getDate("datareposicao") != null);
            json.setIdAgendamento(rs.getInt("horarioturma")+"_"+Uteis.getData(rs.getDate("dataorigem"), "ddMMyy"));
            json.setCodigoContrato(rs.getInt("contrato"));
            if(rs.getString("justificativa") == null) {
                json.setJustificativa("");
            } else {
                json.setJustificativa(rs.getString("justificativa"));
            }
            lista.add(json);
        }
        return lista;

    }

    /**
     * Incluido parametro dataFimAfastamento para não excluir aulas desmarcada fora do periodo de afastamento
     * @param codigoContrato
     * @param dataRetorno
     * @param dataFimAfastamento
     * @throws Exception
     */
    public void excluirAulaDesmarcadaPorRetornoAfastamentoAntecipado(Integer codigoContrato, Date dataRetorno, Date dataFimAfastamento)throws Exception{
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm");
        Date dataFim = Calendario.getDataComHora(dataFimAfastamento, "23:59");
        // Excluir os registros de controle de crédito.
        StringBuilder sql = new StringBuilder();
        sql.append("delete from controleCreditoTreino \n");
        sql.append("where aulaDesmarcada in( \n");
        sql.append("        select ad.codigo \n");
        sql.append("        from auladesmarcada ad \n");
        sql.append("        inner join horarioTurma ht on ht.codigo = ad.horarioTurma \n");
        sql.append("        where ad.desmarcadaPorAfastamento =true and ad.contrato = ").append(codigoContrato);
        sql.append("        and cast(cast (ad.dataOrigem as varchar(10)) || ' ' ||  ht.horaFinal as timestamp) between '").append(sdf.format(dataRetorno)).append("' and '").append(sdf.format(dataFim)).append("' \n");
        sql.append("        )");
        Statement stExcluirCredito = con.createStatement();
        stExcluirCredito.execute(sql.toString());

        sql.delete(0, sql.length());
        sql.append("delete from auladesmarcada ad \n");
        sql.append("using horarioTurma ht \n");
        sql.append("where desmarcadaPorAfastamento = true \n");
        sql.append("and ad.horarioTurma = ht.codigo \n");
        sql.append("and ad.contrato = ").append(codigoContrato);
        sql.append(" and cast(cast (ad.dataOrigem as varchar(10)) || ' ' ||  ht.horaFinal as timestamp) between '").append(sdf.format(dataRetorno)).append("' and '").append(sdf.format(dataFim)).append("' \n");
        Statement stExcluirAula = con.createStatement();
        stExcluirAula.execute(sql.toString());

    }

    public void atualizaPermiteReporAulaDesmarcada(Integer codigoAulaDesmarcada, boolean permiteReporAula)throws Exception{
        StringBuilder sql = new StringBuilder();
        sql.append("update auladesmarcada set permiteReporAulaDesmarcada = ").append((permiteReporAula) ? "true": "false");
        sql.append(" where codigo = ").append(codigoAulaDesmarcada);
        Statement st = con.createStatement();
        st.execute(sql.toString());
    }

    public boolean desmarcouAulaParaNaoPermitirReposicao(Integer codigoContrato, Integer codigoHorarioTurma, Date dataAula)throws Exception{
        StringBuilder sql = new StringBuilder();
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
        sql.append("select * \n");
        sql.append("from auladesmarcada \n");
        sql.append("where permiteReporAulaDesmarcada = false \n");
        sql.append("and contrato = ").append(codigoContrato).append(" \n");
        sql.append("and horarioTurma = ").append(codigoHorarioTurma).append(" \n");
        sql.append("and dataOrigem = '").append(sdf.format(dataAula)).append("'");
        Statement st = con.createStatement();
        ResultSet rs = st.executeQuery(sql.toString());
        return rs.next();
    }

    public List<AulaDesmarcadaVO>consultarAulaDesmarcadaQueNaoPodeSerReposta(Integer codigoContrato, Date dataBase, int nivelMontarDados)throws Exception{
        StringBuilder sql = new StringBuilder();
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        sql.append("select to_date(to_char(ad.dataOrigem, 'YYYY-MM-DD') ||' ' || ht.horaFinal,  'YYYY-MM-DD HH24:MI:SS') as dataHoraAula, ad.* \n");
        sql.append("from auladesmarcada ad \n");
        sql.append("inner join horarioTurma ht on ht.codigo = ad.horarioTurma \n");
        sql.append("where permiteReporAulaDesmarcada = false and  ad.contrato = ").append(codigoContrato).append(" \n");
        sql.append("and to_date(to_char(ad.dataOrigem, 'YYYY-MM-DD') ||' ' || ht.horaFinal,  'YYYY-MM-DD HH24:MI:SS') >= '").append(sdf.format(dataBase)).append("' \n");
        Statement st = con.createStatement();
        ResultSet rs = st.executeQuery(sql.toString());
        return montarDadosConsulta(rs, nivelMontarDados);
    }

    public AulaDesmarcadaVO consultarPorReposicao(Integer codigoReposicao,int nivelMontarDados)throws Exception{
        StringBuilder sql = new StringBuilder();
        sql.append("select * from aulaDesmarcada where reposicao = ").append(codigoReposicao);
        Statement st = con.createStatement();
        ResultSet rs = st.executeQuery(sql.toString());
        if (rs.next()){
            return montarDados(rs, nivelMontarDados);
        }
        return null;
    }

    @Override
     public Map<Date, List<Integer>> consultarAgendamentosDesmarcados(Integer cliente)throws Exception{
        Map<Date, List<Integer>> mapaDesmarcados = new HashMap<Date, List<Integer>>();
        StringBuilder sql = new StringBuilder();
        sql.append("SELECT dataorigem, horarioturma FROM auladesmarcada ad\n");
        sql.append("INNER JOIN cliente cli ON cli.codigo = ad.cliente\n");
        sql.append("INNER JOIN contrato c ON ad.contrato = c.codigo AND c.situacao = 'AT'\n");
        sql.append("WHERE dataorigem >= ? AND cliente = ? AND contratoanterior IS NULL");
        PreparedStatement pst = con.prepareStatement(sql.toString());
        pst.setTimestamp(1, Uteis.getDataJDBCTimestamp(Calendario.getDataComHoraZerada(Calendario.hoje())));
        pst.setInt(2,cliente);
        ResultSet rs = pst.executeQuery();
        while (rs.next()){
            List<Integer> horarios = mapaDesmarcados.get(rs.getDate("dataorigem"));
            if(horarios == null){
                horarios = new ArrayList<Integer>();
                mapaDesmarcados.put(rs.getDate("dataorigem"), horarios);
            }
            horarios.add(rs.getInt("horarioturma"));
        }
        return mapaDesmarcados;

    }

    @Override
    public String obterIdentificadorAulaDesmarcada(Integer cliente, Integer contrato, Integer modalidade)throws Exception{
        StringBuilder sql = new StringBuilder();
        sql.append("SELECT ad.dataorigem, ad.horarioturma, ad.turma, t.modalidade FROM auladesmarcada ad\n");
        sql.append("INNER JOIN turma t ON t.codigo = ad.turma\n");
        sql.append("INNER JOIN empresa e on ad.empresa = e.codigo\n");
        sql.append("where ad.permiteReporAulaDesmarcada = true and ad.cliente = ? AND ad.contrato = ?  AND t.modalidade = ? AND ad.datareposicao IS NULL\n");
        sql.append("and case when e.tempoaposfaltareposicao > 0 then ad.dataorigem >= cast(? as date) - e.tempoaposfaltareposicao else true end\n");
        sql.append("ORDER BY ad.dataorigem");
        PreparedStatement pst = con.prepareStatement(sql.toString());
        pst.setInt(1, cliente);
        pst.setInt(2, contrato);
        pst.setInt(3, modalidade);
        pst.setDate(4, Uteis.getDataJDBC(Calendario.hoje()));

        ResultSet rs = pst.executeQuery();
        if (rs.next()){
            return rs.getInt("horarioturma")+"_"+Uteis.getData(rs.getDate("dataorigem"), "ddMMyy");
        }
        return null;
    }
    @Override
    public void excluirAulasDesmarcadasFuturasSemReposicao(ContratoVO contratoVO, final Integer horarioTurma, Date dataBase,UsuarioVO usuario) throws Exception {
        String sql = "select *  FROM auladesmarcada WHERE contrato  = ?   and dataorigem::date > (?)::date and reposicao is null";
        if(!UteisValidacao.emptyNumber(horarioTurma)){
            sql += " and horarioturma=? ";
        }
        PreparedStatement sqlExcluir = con.prepareStatement(sql);
        int i = 1;
        sqlExcluir.setInt(i++, contratoVO.getCodigo());
        sqlExcluir.setDate(i++, Uteis.getDataJDBC(dataBase));
        if(!UteisValidacao.emptyNumber(horarioTurma)){
           sqlExcluir.setInt(i++, horarioTurma);
        }
        ResultSet rs = sqlExcluir.executeQuery();
        List<AulaDesmarcadaVO> listaExcluir = montarDadosConsulta(rs, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
        GestaoAulaService gestaoAulaService = new GestaoAulaService(con, (String) JSFUtilities.getFromSession("key"));
        for (AulaDesmarcadaVO aula : listaExcluir){
            aula.getClienteVO().setPessoa(contratoVO.getPessoa());
            aula.setUsuarioVO(usuario);
            gestaoAulaService.excluirAulaDesmarcadaSemComit(aula, "(MM)");
        }
        gestaoAulaService = null;
    }

    @Override
    public Integer contarAulasDesmarcadasPorPeriodoSemReposicao(int contrato, int horarioTurma, Date datainicio, Date datafim) throws Exception {
        return contarAulasDesmarcadasPorPeriodo(contrato, horarioTurma, datainicio, datafim, false);
    }

    @Override
    public Integer contarAulasDesmarcadasPorPeriodo(int contrato, int horarioTurma, Date datainicio, Date datafim) throws Exception {
        return contarAulasDesmarcadasPorPeriodo(contrato, horarioTurma, datainicio, datafim, true);
    }

    private Integer contarAulasDesmarcadasPorPeriodo(int contrato, int horarioTurma, Date datainicio, Date datafim, boolean permiteReporAulaDesmarcada) throws Exception {
        StringBuilder sql;
        PreparedStatement pst;
        ResultSet tabelaResultado;
        try{
            sql = new StringBuilder("select count(codigo) as aulas from auladesmarcada ");
            sql.append(" where permiteReporAulaDesmarcada = ").append(permiteReporAulaDesmarcada).append(" and reposicao is null ");
            if(!UteisValidacao.emptyNumber(contrato)){
                sql.append(" and contrato = ?  ");
            }
            if(!UteisValidacao.emptyNumber(horarioTurma)){
                sql.append(" and horarioturma = ? ");
            }
            if(datainicio != null){
                sql.append(" and dataorigem::date >= ? ");
            }
            if(datafim != null){
                sql.append(" and dataorigem::date <= ? ");
            }
            sql.append(" and contratoanterior is null");
            pst = con.prepareStatement(sql.toString());
            int i = 1;
            if(!UteisValidacao.emptyNumber(contrato)){
               pst.setInt(i++, contrato);
            }
            if(!UteisValidacao.emptyNumber(horarioTurma)){
                pst.setInt(i++, horarioTurma);
            }
            if(datainicio != null){
               pst.setDate(i++, Uteis.getDataJDBC(datainicio));
            }
            if(datafim != null){
                pst.setDate(i++, Uteis.getDataJDBC(datafim));
            }

            tabelaResultado = pst.executeQuery();
            if (tabelaResultado.next()){
                return tabelaResultado.getInt("aulas");
            }
            return 0;
        }catch (Exception e){
            throw e;
        }finally {
            sql = null;
            tabelaResultado = null;
            pst = null;
        }
    }

    public boolean validarConsistenciaDesmarcacao(AulaDesmarcadaVO aulaDesmarcada) throws Exception{
        int diaSemana = Calendario.getDiaSemana(aulaDesmarcada.getDataOrigem());
        if(!aulaDesmarcada.getHorarioTurmaVO().getDiaSemanaNumero().equals(diaSemana)){
             throw new Exception("Dia da semana da data informada, não é o mesmo do horário informado");
        }
        MatriculaAlunoHorarioTurma matriculaDAO = new MatriculaAlunoHorarioTurma(con);
        MatriculaAlunoHorarioTurmaVO matricula = matriculaDAO.consultarMatriculaAtivaPorHorarioTurma(
                                aulaDesmarcada.getClienteVO().getPessoa(),
                                aulaDesmarcada.getHorarioTurmaVO().getCodigo(),
                                aulaDesmarcada.getDataOrigem(),
                                Uteis.NIVELMONTARDADOS_MINIMOS);
        if(matricula == null || UteisValidacao.emptyNumber(matricula.getCodigo())){
            throw new Exception("Aluno não tem matricula vigente na turma na data dessa desmarcação");
        }
        return true;
    }

    public List<AulaDesmarcadaVO> consultarAulasDesmarcadasPassadasSemReposicao(Integer codigoContrato, int nivelMontarDados) throws Exception{
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        StringBuilder sql = new StringBuilder();
        sql.append(" SELECT  distinct ad.*  \n");
        sql.append("from aulaDesmarcada  ad \n");
        sql.append("inner join horarioTurma ht on ht.codigo = ad.horarioTurma \n");
        sql.append(" where ad.contrato = ").append(codigoContrato);
        sql.append(" and permiteReporAulaDesmarcada = true ");
        sql.append(" and datareposicao is null ");
        sql.append(" and  to_timestamp(to_char(ad.dataOrigem, 'YYYY-MM-DD') ||' ' || ht.horaFinal,  'YYYY-MM-DD HH24:MI:SS') < '");
        sql.append(sdf.format(Calendario.hoje())).append("' ");
        sql.append(" order by dataorigem desc ");

        Statement st = con.createStatement();
        ResultSet rs = st.executeQuery(sql.toString());

        return montarDadosConsulta(rs, nivelMontarDados);
    }

    @Override
    public boolean existeAulaParaReposicao(ReposicaoVO reposicao) throws Exception {
        StringBuilder sql = new StringBuilder();
        sql.append(" SELECT M.codigo FROM matriculaalunohorarioturma M ");
        sql.append(" JOIN horarioturma T ON T.codigo = M.horarioturma ");
        sql.append(" JOIN cliente      C ON C.pessoa = M.pessoa ");
        // Verifica se o aluno está tentando fazer uma reposição em uma aula que já está matriculado
        sql.append(" WHERE C.codigo = ? AND M.contrato = ? AND T.codigo = ? AND T.turma = ? ");
        // Avaliar a vigência do contrato para o a compra de credito o contanto ainda continuará vigente após a data fim
        sql.append(" AND (M.datainicio <= ? AND M.datafim >= ? )");
        // Só será permitido se a aula foi desmarcada
        sql.append(" AND NOT EXISTS ( ");
        sql.append("    SELECT A.codigo FROM auladesmarcada A ");
        sql.append("    WHERE  A.cliente = C.codigo AND A.contrato = M.contrato AND A.horarioturma = T.codigo AND A.dataorigem = ? ");
        sql.append(" ) ");

        PreparedStatement ps = con.prepareStatement(sql.toString());
        ps.setInt(1, reposicao.getCliente().getCodigo());
        ps.setInt(2, reposicao.getContrato().getCodigo());
        ps.setInt(3, reposicao.getHorarioTurma().getCodigo());
        ps.setInt(4, reposicao.getHorarioTurma().getTurma());
        ps.setDate(5, Uteis.getDataJDBC(reposicao.getDataReposicao()));
        ps.setDate(6, Uteis.getDataJDBC(reposicao.getDataReposicao()));
        ps.setDate(7, Uteis.getDataJDBC(reposicao.getDataReposicao()));

        // Verificar se o aluno já fez reposição para o horário
        boolean existe = ps.executeQuery().next();
        if (!existe) {
            sql = new StringBuilder();
            sql.append(" SELECT R.codigo FROM reposicao R ");
            sql.append(" WHERE  R.cliente = ? AND R.contrato = ? AND R.horarioturma = ? AND R.datareposicao = ? ");

            ps = con.prepareStatement(sql.toString());
            ps.setInt(1, reposicao.getCliente().getCodigo());
            ps.setInt(2, reposicao.getContrato().getCodigo());
            ps.setInt(3, reposicao.getHorarioTurma().getCodigo());
            ps.setDate(4, Uteis.getDataJDBC(reposicao.getDataReposicao()));

            existe =  ps.executeQuery().next();
        }
        return existe;
    }
    
    
    
    public void excluirAulasDesmarcadasFuturasSemReposicaoProcesso(ContratoVO contratoVO, final Integer horarioTurma, Date dataBase,UsuarioVO usuario) throws Exception {
        String sql = "select *  FROM auladesmarcada WHERE contrato  = ?   and dataorigem::date > (?)::date and reposicao is null";
        if(!UteisValidacao.emptyNumber(horarioTurma)){
            sql += " and horarioturma=? ";
        }
        PreparedStatement sqlExcluir = con.prepareStatement(sql);
        int i = 1;
        sqlExcluir.setInt(i++, contratoVO.getCodigo());
        sqlExcluir.setDate(i++, Uteis.getDataJDBC(dataBase));
        if(!UteisValidacao.emptyNumber(horarioTurma)){
           sqlExcluir.setInt(i++, horarioTurma);
        }
        ResultSet rs = sqlExcluir.executeQuery();
        List<AulaDesmarcadaVO> listaExcluir = montarDadosConsulta(rs, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
        for (AulaDesmarcadaVO aula : listaExcluir){
            aula.getClienteVO().setPessoa(contratoVO.getPessoa());
            aula.setUsuarioVO(usuario);
            excluirSemCommit(aula);
        }
    }

    @Override
    public List<AulaDesmarcadaVO> consultarListaAulasDesmarcadas(int contrato, int empresa, int nivelMontarDados) throws Exception {
        StringBuilder sql;
        PreparedStatement pst;
        ResultSet tabelaResultado;
        try{
            sql = new StringBuilder("select * from auladesmarcada where permiteReporAulaDesmarcada = true and contrato = ? and datareposicao is null ");
            if(!UteisValidacao.emptyNumber(empresa)){
                sql.append("  and empresa = ?  ");
            }
            sql.append("  order by dataorigem ");
            pst = con.prepareStatement(sql.toString());
            int  i = 1;
            pst.setInt(i++, contrato);
            if(!UteisValidacao.emptyNumber(empresa)){
                pst.setInt(i++, empresa);
            }

            tabelaResultado = pst.executeQuery();
            return montarDadosConsulta(tabelaResultado,nivelMontarDados);
        }catch (Exception e){
            throw e;
        }finally {
            sql = null;
            tabelaResultado = null;
            pst = null;
        }
    }

    @Override
    public void alterarContratoAulaEContratoAnterior(AulaDesmarcadaVO obj) throws Exception {
        final String sqlUpdate = "update auladesmarcada set contrato=?,contratoanterior=?  where codigo=?";
        PreparedStatement sqlAlterar= con.prepareStatement(sqlUpdate);
        int i = 0;
        sqlAlterar.setInt(++i, obj.getContratoVO().getCodigo());
        resolveIntegerNull(sqlAlterar, ++i, obj.getContratoAnterior().getCodigo());
        sqlAlterar.setInt(++i, obj.getCodigo());
        sqlAlterar.execute();
    }

    @Override
    public Integer contarAulasDesmarcadasContratoPassado(int contrato, int empresa, int turma, int horarioTurma, Date dia, Date datainicio, Date datafim,int contratoAnterior) throws Exception {
        StringBuilder sql;
        PreparedStatement pst;
        ResultSet tabelaResultado;
        try{
            sql = new StringBuilder("select count(codigo) as aulas from auladesmarcada ");
            if(dia == null){
                sql.append(" where datareposicao is null  ");
                if(datainicio != null){
                    sql.append(" and dataorigem::date >= ? ");
                }
                if(datafim != null){
                    sql.append(" and dataorigem::date <= ? ");
                }
            }else{
                sql.append(" where dataorigem BETWEEN ? AND ? ");
            }
            if(!UteisValidacao.emptyNumber(contrato)){
                sql.append(" and contrato = ? ");
            }
            if(!UteisValidacao.emptyNumber(turma)){
                sql.append(" and turma = ? ");
            }
            if(!UteisValidacao.emptyNumber(empresa)){
                sql.append(" and empresa = ? ");
            }
            if(!UteisValidacao.emptyNumber(horarioTurma)){
                sql.append(" and horarioturma = ? ");
            }
            if(!UteisValidacao.emptyNumber(contratoAnterior)){
                sql.append(" and contratoanterior = ? ");
            }
            sql.append(" and permiteReporAulaDesmarcada = true ");
            pst = con.prepareStatement(sql.toString());
            int i = 1;
            if(dia != null){
                pst.setTimestamp(i++, Uteis.getDataHoraJDBC(dia, "00:00:00"));
                pst.setTimestamp(i++, Uteis.getDataHoraJDBC(dia, "23:59:59"));
            } else {
                if(datainicio != null){
                    pst.setDate(i++, Uteis.getDataJDBC(datainicio));
                }
                if(datafim != null){
                    pst.setDate(i++, Uteis.getDataJDBC(datafim));
                }
            }
            if(!UteisValidacao.emptyNumber(contrato)){
                pst.setInt(i++, contrato);
            }
            if(!UteisValidacao.emptyNumber(turma)){
                pst.setInt(i++, turma);
            }
            if(!UteisValidacao.emptyNumber(empresa)){
                pst.setInt(i++, empresa);
            }

            if(!UteisValidacao.emptyNumber(horarioTurma)){
               pst.setInt(i++, horarioTurma);
            }
            
            if(!UteisValidacao.emptyNumber(contratoAnterior)){
               pst.setInt(i++, contratoAnterior);
            }
            tabelaResultado = pst.executeQuery();
            if (tabelaResultado.next()){
                return tabelaResultado.getInt("aulas");
            }
            return 0;
        }catch (Exception e){
            throw e;
        }finally {
            sql = null;
            tabelaResultado = null;
            pst = null;
        }
    }

    @Override
    public List<AulaDesmarcadaVO> consultarListaAulasDesmarcadasContratoPassado(int contrato, int empresa, int contratoPassado, int nivelMontarDados) throws Exception {
        StringBuilder sql;
        PreparedStatement pst;
        ResultSet tabelaResultado;
        try{
            sql = new StringBuilder("select * from auladesmarcada where permiteReporAulaDesmarcada = true and contrato = ? and datareposicao is null ");
            if(!UteisValidacao.emptyNumber(contratoPassado)){
                sql.append(" and contratoanterior = ? ");
            }
            if(!UteisValidacao.emptyNumber(empresa)){
                sql.append("  and empresa = ?  ");
            }
            sql.append("  order by dataorigem ");
            pst = con.prepareStatement(sql.toString());
            int  i = 1;
            pst.setInt(i++, contrato);
            if(!UteisValidacao.emptyNumber(contratoPassado)){
                 pst.setInt(i++, contratoPassado);
            }
            if(!UteisValidacao.emptyNumber(empresa)){
                pst.setInt(i++, empresa);
            }


            tabelaResultado = pst.executeQuery();
            return montarDadosConsulta(tabelaResultado,nivelMontarDados);
        }catch (Exception e){
            throw e;
        }finally {
            sql = null;
            tabelaResultado = null;
            pst = null;
        }
    }

    @Override
    public Map<Date, List<Integer>> consultarAgendamentosDesmarcadosSemValidarData(Integer cliente,Integer contrato) throws Exception {
        Map<Date, List<Integer>> mapaDesmarcados = new HashMap<Date, List<Integer>>();
        StringBuilder sql = new StringBuilder();
        sql.append("select dataorigem, horarioturma from auladesmarcada ad inner join situacaoclientesinteticodw sc on  ad.cliente = sc.codigocliente \n");
        sql.append("where cliente = ? and contrato = ? and contratoanterior is null");
        PreparedStatement pst = con.prepareStatement(sql.toString());
        pst.setInt(1,cliente);
        pst.setInt(2,contrato);
        ResultSet rs = pst.executeQuery();
        while (rs.next()){
            List<Integer> horarios = mapaDesmarcados.get(rs.getDate("dataorigem"));
            if(horarios == null){
                horarios = new ArrayList<Integer>();
                mapaDesmarcados.put(rs.getDate("dataorigem"), horarios);
            }
            horarios.add(rs.getInt("horarioturma"));
        }
        return mapaDesmarcados;
    }

    @Override
    public void voltarAulasDermarcadasEstorno(ContratoVO contrato) throws Exception {
        String sqlUpdate = "update auladesmarcada set contrato=contratoanterior where contrato=? and contratoanterior is not null";
        try (PreparedStatement sqlAlterar = con.prepareStatement(sqlUpdate)) {
            int i = 0;
            sqlAlterar.setInt(++i, contrato.getCodigo());
            sqlAlterar.execute();
        }catch (Exception e){
            Uteis.logar(e.getMessage());
        }
        if (UteisValidacao.notEmptyNumber(contrato.getContratoBaseadoRenovacao())) {
            sqlUpdate = "update auladesmarcada set contratoanterior=null where contratoanterior=?";
            try (PreparedStatement sqlAlterar = con.prepareStatement(sqlUpdate)) {
                int i = 0;
                sqlAlterar.setInt(++i, contrato.getContratoBaseadoRenovacao());
                ;
                sqlAlterar.execute();
            } catch (Exception e) {
                Uteis.logar(e.getMessage());
            }
        }
    }

    public void deletarAulasDermarcadasEstornoOperacaoColetiva(OperacaoColetivaVO operacaoColetiva) throws Exception {

        final String sql = "delete from auladesmarcada where operacaocoletiva = ? ";
        try {
            con.setAutoCommit(false);
            PreparedStatement sqlExcluir = con.prepareStatement(sql);
            sqlExcluir.setInt(1, operacaoColetiva.getCodigo());
            sqlExcluir.execute();
            operacaoColetiva.setStatus(StatusOperacaoColetivaEnum.EXCLUIDA);
            operacaoColetiva.setDataprocessamento(Calendario.hoje());
            operacaoColetiva.setResultado("Aulas desmarcadas do Período foram excluídas!");
        }catch (Exception e){
            con.rollback();
            throw new Exception(e.getMessage());
        }finally {
            con.setAutoCommit(true);
        }
        OperacaoColetiva operacaoDAO = new OperacaoColetiva(con);
        operacaoDAO.altetarSemCommit(operacaoColetiva);
        operacaoDAO = null;
    }

    public List<AulaDesmarcadaVO> consultarAulaDesmarcadaPorHorarioTurmaEPeriodo(final Integer codigoHorarioTurma,
                                                                                 final String periodo,
                                                                                 int nivelMontarDados,
                                                                                 Integer codContrato,
                                                                                 String reposicoesId) throws Exception {
        StringBuilder sql = new StringBuilder();
        sql.append("SELECT * FROM auladesmarcada a "
                 + "INNER JOIN horarioturma ht ON ht.codigo = a.horarioturma "
                 + "WHERE a.horarioturma = %s "
                 + "AND a.dataorigem between %s ");
        if (codContrato != null) {
            sql.append("AND contrato = ").append(codContrato);
        }
        if (!UteisValidacao.emptyString(reposicoesId)) {
            sql.append(" AND (a.reposicao NOT IN (").append(reposicoesId).append(") OR a.reposicao IS NULL)");
        }

        return montarDadosConsulta(
                criarConsulta(String.format(sql.toString(), codigoHorarioTurma, periodo), con),
                nivelMontarDados
        );
    }


    public void atualizarContratoDasAulasDesmarcadas() throws Exception {
        StringBuilder sql = new StringBuilder();
        sql.append("UPDATE auladesmarcada ad SET contrato = con1.codigo, contratoanterior = con2.codigo \n");
        sql.append("FROM contrato con1 \n");
        sql.append("INNER JOIN contrato con2 ON con2.codigo = con1.contratobaseadorenovacao \n");
        sql.append("INNER JOIN empresa emp ON emp.codigo = con1.empresa \n");
        sql.append("INNER JOIN pessoa pes ON pes.codigo = con1.pessoa \n");
        sql.append("WHERE ad.contrato = con2.codigo \n");
        sql.append("AND ad.empresa = con1.empresa \n");
        sql.append("AND ad.datareposicao is null \n");
        sql.append("AND emp.adicionarAulasDesmarcadasContratoAnterior = true \n");
        sql.append("AND con1.situacao = 'AT' \n");
        sql.append("AND DATE(con2.vigenciaateajustada) < CURRENT_DATE \n");
        sql.append("AND DATE(con1.vigenciade) <= CURRENT_DATE \n");

        try (PreparedStatement sqlAlterar = con.prepareStatement(sql.toString())) {
            sqlAlterar.execute();
        } catch (Exception e) {
            Uteis.logar(e.getMessage());
        }
    }

}

package negocio.facade.jdbc.basico;

import negocio.comuns.basico.ClienteVO;
import negocio.comuns.basico.ConfirmacaoEmailCompraVO;
import negocio.comuns.utilitarias.Calendario;
import negocio.comuns.utilitarias.ConsistirException;
import negocio.comuns.utilitarias.Uteis;
import negocio.facade.jdbc.arquitetura.SuperEntidade;
import negocio.interfaces.basico.ConfirmacaoEmailCompraInterfaceFacede;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;

public class ConfirmacaoEmail<PERSON>ompra extends SuperEntidade implements ConfirmacaoEmailCompraInterfaceFacede {

    public ConfirmacaoEmailCompra() throws Exception {
        super();
    }

    public ConfirmacaoEmailCompra(Connection conexao) throws Exception {
        super(conexao);
    }


    public void incluirCadastroEmailConfirmacaoCompra(ClienteVO cliente, String token, int nrParcelasAdesao, String numeroCupomDesconto, int nrParcelasPagamento, int diaVencimento, int plano) throws Exception {
        try {
            con.setAutoCommit(false);
            String sql = "INSERT INTO emailconfirmacaocompra( cliente, dataRegistro, email, token, nrParcelasAdesao, numeroCupomDesconto, nrParcelasPagamento, diaVencimento, plano)"
                    + " VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)";
            PreparedStatement sqlInserir = con.prepareStatement(sql);
            sqlInserir.setInt(1, cliente.getCodigo());
            sqlInserir.setTimestamp(2, Uteis.getDataJDBCTimestamp(Calendario.hoje()));
            sqlInserir.setString(3, cliente.getPessoa().getEmail());
            sqlInserir.setString(4, token);
            sqlInserir.setInt(5, nrParcelasAdesao);
            sqlInserir.setString(6, numeroCupomDesconto);
            sqlInserir.setInt(7, nrParcelasPagamento);
            sqlInserir.setInt(8, diaVencimento);
            sqlInserir.setInt(9, plano);

            sqlInserir.execute();
            cliente.setNovoObj(false);
            con.commit();

        } catch (Exception e) {
            cliente.setNovoObj(true);
            con.rollback();
            con.setAutoCommit(true);

        } finally {
            con.setAutoCommit(true);
        }
    }

    public ConfirmacaoEmailCompraVO consultarCadastroEmailConfirmacaoCompra(String token) throws Exception {
        String sql = "SELECT * FROM emailconfirmacaocompra where token = ?";
        PreparedStatement sqlConsultar = con.prepareStatement(sql);
        sqlConsultar.setString(1, token);
        ResultSet tabelaResultado = sqlConsultar.executeQuery();
        if (!tabelaResultado.next()) {
            throw new ConsistirException("Token não encontrado");
        }
        return montarDados(tabelaResultado);
    }


    public static ConfirmacaoEmailCompraVO montarDados(ResultSet dadosSQL) throws Exception {

        ConfirmacaoEmailCompraVO obj = new ConfirmacaoEmailCompraVO();
        obj.getCliente().setCodigo(dadosSQL.getInt("cliente"));
        obj.setDataRegistro(dadosSQL.getDate("dataregistro"));
        obj.setUrlRtorno(dadosSQL.getString("urlretorno"));
        obj.setToken(dadosSQL.getString("token"));
        obj.setEmail(dadosSQL.getString("email"));
        obj.setNrparcelasadesao(dadosSQL.getInt("nrparcelasadesao"));
        obj.setNumeroCupomDesconto(dadosSQL.getString("numeroCupomDesconto"));
        obj.setNrParcelasPagamento(dadosSQL.getInt("nrParcelasPagamento"));
        obj.setDiaVencimento(dadosSQL.getInt("diaVencimento"));
        obj.setPlano(dadosSQL.getInt("plano"));
        return obj;
    }
}

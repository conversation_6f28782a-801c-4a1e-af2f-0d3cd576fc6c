package negocio.facade.jdbc.basico;

import br.com.pactosolucoes.ce.comuns.ex.ValidacaoException;
import negocio.comuns.arquitetura.SuperVO;

import java.util.List;

public class PlanoTipoVO extends SuperVO {
    private Integer codigo;
    private String nome = "";
    private String tipo = "";
    private boolean ativo = true;
    private Integer limiteVendas = 0;
    private List<PlanoTipoTipoProdutoVO> tiposProduto;

    public PlanoTipoVO() {
    }

    public PlanoTipoVO(int codigo, String nome, boolean ativo, Integer limiteVendas) {
        this.codigo = codigo;
        this.nome = nome;
        this.ativo = ativo;
        this.limiteVendas = limiteVendas;
    }

    public void validarDados() throws ValidacaoException {
        if(getNome().isEmpty())
            throw new ValidacaoException("O nome do tipo de plano não pode ser vazio");
    }

    @Override
    public Integer getCodigo() {
        return codigo;
    }

    @Override
    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public String getAtivoTexto(){
        if(isAtivo())
            return "Ativo";
        else
            return "Inativo";
    }

    public String getNome() {
        return nome;
    }

    public void setNome(String nome) {
        this.nome = nome;
    }

    public boolean isAtivo() {
        return ativo;
    }

    public void setAtivo(boolean ativo) {
        this.ativo = ativo;
    }

    public void upperCase() {
        setNome(getNome().toUpperCase());
    }

    public void setTiposProduto(List<PlanoTipoTipoProdutoVO> tiposProduto) {
        this.tiposProduto = tiposProduto;
    }

    public List<PlanoTipoTipoProdutoVO> getTiposProduto() {
        return tiposProduto;
    }

    public String getTipo() {
        return tipo;
    }

    public void setTipo(String tipo) {
        this.tipo = tipo;
    }

    public Integer getLimiteVendas() {
        return limiteVendas;
    }

    public void setLimiteVendas(Integer limiteVendas) {
        this.limiteVendas = limiteVendas;
    }
}

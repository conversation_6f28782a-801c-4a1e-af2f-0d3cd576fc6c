package negocio.facade.jdbc.basico;

import br.com.pactosolucoes.integracao.pactopay.front.FacilitePayConfigDTO;
import negocio.comuns.arquitetura.UsuarioVO;
import negocio.comuns.basico.EmpresaVO;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisValidacao;
import negocio.facade.jdbc.arquitetura.SuperEntidade;
import negocio.facade.jdbc.arquitetura.Usuario;

import java.sql.Connection;


public class FacilitePay extends SuperEntidade {

    public FacilitePay() throws Exception {
        super();
    }

    public FacilitePay(Connection conexao) throws Exception {
        super(conexao);
    }

    public FacilitePayConfigDTO configFacilitePay(Integer codEmpresa, Integer codUsuario, String usernameUsuario) throws Exception {
        Empresa empresaDAO;
        try {
            empresaDAO = new Empresa(this.con);

            if (UteisValidacao.emptyNumber(codEmpresa)) {
                throw new Exception("Empresa não informada");
            }

            if (UteisValidacao.emptyNumber(codUsuario) && UteisValidacao.emptyString(usernameUsuario)) {
                throw new Exception("Usuário não informada");
            }

            UsuarioVO usuarioVO = obterUsuarioVO(usernameUsuario, codUsuario, Uteis.NIVELMONTARDADOS_METACOLABORADORRESPONSAVEL);
            EmpresaVO empresaVO = empresaDAO.consultarPorChavePrimaria(codEmpresa, Uteis.NIVELMONTARDADOS_EMPRESA_BASICO);
            return new FacilitePayConfigDTO(empresaVO, usuarioVO);
        } finally {
            empresaDAO = null;
        }
    }

    private UsuarioVO obterUsuarioVO(String username, Integer codigoUsuario, int nivelMontarDados) throws Exception {
        Usuario usuarioDAO;
        try {
            usuarioDAO = new Usuario(this.con);
            UsuarioVO usuarioVO = null;
            if (!UteisValidacao.emptyString(username)) {
                usuarioVO = usuarioDAO.consultarPorUsername(username, nivelMontarDados);
            }
            if (!UteisValidacao.emptyNumber(codigoUsuario) &&
                    (usuarioVO == null || UteisValidacao.emptyNumber(usuarioVO.getCodigo()))) {
                usuarioVO = usuarioDAO.consultarPorChavePrimaria(codigoUsuario, nivelMontarDados);
            }
            return usuarioVO;
        } finally {
            usuarioDAO = null;
        }
    }
}

package negocio.facade.jdbc.basico;

import negocio.comuns.basico.ColaboradorVO;
import negocio.comuns.basico.EstornoObservacaoVO;
import negocio.comuns.basico.PessoaVO;
import negocio.comuns.utilitarias.Uteis;
import negocio.facade.jdbc.arquitetura.SuperEntidade;
import negocio.interfaces.basico.EstornoObservacaoInterfaceFacade;

import java.sql.*;
import java.util.ArrayList;
import java.util.List;
import negocio.comuns.contrato.ContratoVO;
import negocio.comuns.utilitarias.Calendario;

/**
 * Created with IntelliJ IDEA.
 * User: GlaucoT
 * Date: 03/12/13
 * Time: 16:10
 * To change this template use File | Settings | File Templates.
 */
public class EstornoObservacao extends SuperEntidade implements EstornoObservacaoInterfaceFacade {

    public EstornoObservacao() throws Exception {
        super();
    }

    public EstornoObservacao(Connection conexao) throws Exception {
        super(conexao);
    }

    @Override
    public void incluir(EstornoObservacaoVO obj) throws Exception {
        String sql = "INSERT INTO estornoobservacao (justificativa, pessoa, contrato, empresa, usuarioresponsavel, dataestorno) VALUES (?, ?, ?, ?, ?, ?);";
        PreparedStatement sqlInserir = getCon().prepareStatement(sql);
        int i = 0;
        sqlInserir.setString(++i, obj.getJustificativa());
        sqlInserir.setInt(++i, obj.getPessoaVO().getCodigo());
        sqlInserir.setInt(++i, obj.getCodContrato());
        sqlInserir.setInt(++i, obj.getCodEmpresa());
        sqlInserir.setString(++i, obj.getUsuarioResponsavel());
        sqlInserir.setTimestamp(++i, Uteis.getDataJDBCTimestamp(obj.getDataEstorno()));
        sqlInserir.execute();
        obj.setCodigo(obterValorChavePrimariaCodigo());
        obj.setNovoObj(false);
    }

    @Override
    public void alterar(EstornoObservacaoVO obj) throws Exception {
        String sql = "UPDATE estornoobservacao\n" +
                "SET pessoa = ?, contrato = ?, empresa = ?, usuarioresponsavel = ?, observacao = ?, dataestorno = ?\n" +
                "WHERE codigo = ?;";
        PreparedStatement sqlAlterar = getCon().prepareStatement(sql);
        int i = 0;
        sqlAlterar.setInt(++i, obj.getPessoaVO().getCodigo());
        sqlAlterar.setInt(++i, obj.getCodContrato());
        sqlAlterar.setInt(++i, obj.getCodEmpresa());
        sqlAlterar.setString(++i, obj.getUsuarioResponsavel());
        sqlAlterar.setString(++i, obj.getJustificativa());
        sqlAlterar.setTimestamp(++i, Uteis.getDataJDBCTimestamp(obj.getDataEstorno()));
        sqlAlterar.setInt(++i, obj.getCodigo());
        sqlAlterar.execute();
    }

    @Override
    public void excluir(EstornoObservacaoVO obj) throws Exception {
        String sql = "DELETE FROM estornoobservacao WHERE codigo = ?;";
        PreparedStatement sqlExcluir = getCon().prepareStatement(sql);
        sqlExcluir.setInt(1, obj.getCodigo());
        sqlExcluir.execute();
    }

    @Override
    public List<EstornoObservacaoVO> consultar(Integer codPessoa, int nivelMontarDados) throws Exception {
        String sqlStr = "SELECT * FROM estornoobservacao WHERE pessoa = " + codPessoa + " ORDER BY codigo DESC;";
        Statement stm = con.createStatement();
        ResultSet tabelaResultado = stm.executeQuery(sqlStr);
        return montarDadosConsulta(tabelaResultado, nivelMontarDados);
    }

    @Override
    public List<EstornoObservacaoVO> consultarPorDataEstorno(Date dataEstorno, int nivelMontarDados) throws Exception {
        String sqlStr = "SELECT * FROM estornoobservacao WHERE dataestorno = " + dataEstorno + " ORDER BY codigo DESC;";
        Statement stm = con.createStatement();
        ResultSet tabelaResultado = stm.executeQuery(sqlStr);
        return montarDadosConsulta(tabelaResultado, nivelMontarDados);
    }

    private List<EstornoObservacaoVO> montarDadosConsulta(ResultSet tabelaResultado, int nivelMontarDados) throws Exception {
        List<EstornoObservacaoVO> vetResultado = new ArrayList<EstornoObservacaoVO>();
        while (tabelaResultado.next()) {
            EstornoObservacaoVO obj = montarDados(tabelaResultado, nivelMontarDados);
            vetResultado.add(obj);
        }
        return vetResultado;
    }

    private EstornoObservacaoVO montarDados(ResultSet dadosSQL, int nivelMontarDados) throws Exception {
        EstornoObservacaoVO obj = new EstornoObservacaoVO();
        obj.setCodigo(dadosSQL.getInt("codigo"));
        obj.setJustificativa(dadosSQL.getString("justificativa"));
        obj.getPessoaVO().setCodigo(dadosSQL.getInt("pessoa"));
        obj.setCodContrato(dadosSQL.getInt("contrato"));
        obj.setCodEmpresa(dadosSQL.getInt("empresa"));
        obj.setDataEstorno(dadosSQL.getTimestamp("dataestorno"));
        obj.setUsuarioResponsavel(dadosSQL.getString("usuarioresponsavel"));
        try {
            PessoaVO pessoaVO = getFacade().getPessoa().consultarPorChavePrimaria(obj.getPessoaVO().getCodigo(), Uteis.NIVELMONTARDADOS_MINIMOS);
            obj.setPessoaVO(pessoaVO);
        } catch (Exception ignored) {
        }

        obj.setNovoObj(false);

        return obj;
    }

    public int contarPorNomeEntidadePorDataAlteracaoPorOperacao(
            Date dataAlteracaoInicial, Date dataAlteracaoFim,
            Integer codEmpresa, boolean buscarComAdministrador, boolean buscarComRecorrencia,
            List<ColaboradorVO> lista, int nivelMontarDados) throws Exception {
        int qtde = 0;
        String sqlStr = "SELECT count(codigo) as cod FROM estornoobservacao WHERE dataestorno between '" + dataAlteracaoInicial + " 00:00:00' and '" + dataAlteracaoFim + " 23:59:59'";
        sqlStr += " and " + ((!buscarComAdministrador) ? "not" : "") + " (usuarioresponsavel = 'ADMINISTRADOR' )";
        sqlStr += " and " + ((!buscarComRecorrencia) ? "not" : "") + " (usuarioresponsavel like 'RECOR%' )";
        // filtra a consulta pelos colaboradores
        for (ColaboradorVO co : lista) {
            if (co.getColaboradorEscolhidoOperacoes()) {
                if (qtde == 0) {
                    sqlStr += " AND ( ";
                    qtde++;
                } else {
                    sqlStr += " OR ";
                }
                sqlStr += "usuarioresponsavel like '" + co.getPessoa().getNome() + "'";
            }
        }
        sqlStr += (qtde > 0 ? ")" : "");

        if (codEmpresa != null && codEmpresa > 0) {
            sqlStr += " and empresa = " + codEmpresa + "";
        }

        Statement stm = con.createStatement();
        ResultSet tabelaResultado = stm.executeQuery(sqlStr);
        if (!tabelaResultado.next()) {
            return 0;
        }
        return tabelaResultado.getInt("cod");
    }

    public List<EstornoObservacaoVO> consultarPorNomeEntidadePorDataAlteracaoPorOperacao(
            Date dataAlteracaoInicial, Date dataAlteracaoFim,
            Integer codEmpresa, boolean buscarComAdministrador, boolean buscarComRecorrencia,
            List<ColaboradorVO> lista, int nivelMontarDados) throws Exception {
        int qtde = 0;
        String sqlStr = "SELECT * FROM estornoobservacao WHERE dataestorno between '" + dataAlteracaoInicial + " 00:00:00' and '"
                + dataAlteracaoFim + " 23:59:59'";
        sqlStr += " and " + ((!buscarComAdministrador) ? "not" : "") + " (usuarioresponsavel ilike 'ADMINISTRADOR' )";
        sqlStr += " and " + ((!buscarComRecorrencia) ? "not" : "") + " (usuarioresponsavel ilike 'RECOR%' )";
        // filtra a consulta pelos colaboradores
        for (ColaboradorVO co : lista) {
            if (co.getColaboradorEscolhidoOperacoes()) {
                if (qtde == 0) {
                    sqlStr += " AND ( ";
                    qtde++;
                } else {
                    sqlStr += " OR ";
                }
                sqlStr += "usuarioresponsavel like '" + co.getPessoa().getNome() + "'";
            }
        }
        sqlStr += (qtde > 0 ? ")" : "");

        if (codEmpresa != null && codEmpresa > 0) {
            sqlStr += " and empresa = " + codEmpresa + "";
        }

        Statement stm = con.createStatement();
        ResultSet tabelaResultado = stm.executeQuery(sqlStr);
        return montarDadosConsulta(tabelaResultado, nivelMontarDados);
    }
    
     public void gravarEstornoObservacao(ContratoVO contrato,final String justificativa) throws Exception {
//            for (Object objLista : getListaPessoaVOs()) {
//                ContratoVO contratoVO1 = (ContratoVO) objLista;
//                EstornoObservacaoVO estornoObservacaoVO = new EstornoObservacaoVO();
//                estornoObservacaoVO.getPessoaVO().setCodigo(contratoVO1.getPessoa().getCodigo());
//                estornoObservacaoVO.setCodContrato(contratoVO1.getCodigo());
//                estornoObservacaoVO.setDataEstorno(Calendario.hoje());
//                estornoObservacaoVO.setJustificativa(getJustificativaEstorno());
//                estornoObservacaoVO.setCodEmpresa(contratoVO1.getEmpresa().getCodigo());
//                estornoObservacaoVO.setUsuarioResponsavel(getContratoVO().getUsuarioVO().getNome());
//
//                getFacade().getEstornoObservacao().incluir(estornoObservacaoVO);
//            }

        EstornoObservacaoVO estornoObservacaoVO = new EstornoObservacaoVO();
        estornoObservacaoVO.getPessoaVO().setCodigo(contrato.getPessoa().getCodigo());
        estornoObservacaoVO.setCodContrato(contrato.getCodigo());
        estornoObservacaoVO.setCodEmpresa(contrato.getEmpresa().getCodigo());
        estornoObservacaoVO.setDataEstorno(Calendario.hoje());
        estornoObservacaoVO.setJustificativa(justificativa);
        estornoObservacaoVO.setUsuarioResponsavel(contrato.getUsuarioVO().getNome());

        incluir(estornoObservacaoVO);
    }
}

package negocio.facade.jdbc.basico;

import br.com.pactosolucoes.comuns.util.FileUtilities;
import controle.arquitetura.threads.ThreadImportacao;
import importador.ImportacaoConfigTO;
import importador.json.*;
import negocio.comuns.acesso.IntegracaoMemberVO;
import negocio.comuns.acesso.integracao.member.enums.TipoOperacaoIntegracaoMembersEnum;
import negocio.comuns.arquitetura.UsuarioVO;
import negocio.comuns.basico.ConfigExcelImportacaoTO;
import negocio.comuns.basico.ProcessoImportacaoVO;
import negocio.comuns.basico.enumerador.TipoImportacaoEnum;
import negocio.comuns.crm.ConfiguracaoSistemaCRMVO;
import negocio.comuns.utilitarias.Calendario;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisEmail;
import negocio.comuns.utilitarias.UteisValidacao;
import negocio.facade.jdbc.arquitetura.SuperEntidade;
import negocio.facade.jdbc.arquitetura.Usuario;
import negocio.facade.jdbc.utilitarias.Conexao;
import negocio.interfaces.basico.ProcessoImportacaoInterfaceFacade;
import org.json.JSONObject;

import java.io.File;
import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

import static controle.arquitetura.SuperControle.getConfiguracaoSMTPNoReply;

/**
 * Created by Luiz Felipe on 20/12/2019.
 */
public class ProcessoImportacao extends SuperEntidade implements ProcessoImportacaoInterfaceFacade {

    public ProcessoImportacao() throws Exception {
        super();
        setIdEntidade("Empresa");
    }

    public ProcessoImportacao(Connection con) throws Exception {
        super(con);
        setIdEntidade("Empresa");
    }

    @Override
    public void incluir(ProcessoImportacaoVO obj) throws Exception {
        ProcessoImportacaoVO.validarDados(obj);
        obj.realizarUpperCaseDados();
        String sql = "INSERT INTO ProcessoImportacao(tipoImportacao, sequencialImportacao, descricao, empresa, usuario, configuracao, status, dataInicio, dataFim) "
                + "VALUES (?,?,?,?,?,?,?,?,?)";
        try (PreparedStatement ps = con.prepareStatement(sql)) {
            int i = 0;
            ps.setInt(++i, obj.getTipoImportacaoEnum().getCodigo());
            ps.setInt(++i, obj.getSequencialImportacao());
            ps.setString(++i, obj.getDescricao());
            resolveIntegerNull(ps, ++i, obj.getEmpresaVO().getCodigo());
            resolveIntegerNull(ps, ++i, obj.getUsuarioVO().getCodigo());
            ps.setString(++i, obj.getConfiguracao());
            ps.setString(++i, obj.getStatus());
            ps.setTimestamp(++i, Uteis.getDataJDBCTimestamp(obj.getDataInicio()));
            ps.setTimestamp(++i, Uteis.getDataJDBCTimestamp(obj.getDataFim()));
            ps.execute();
            obj.setCodigo(obterValorChavePrimariaCodigo());
            obj.setNovoObj(false);
            atualizarSequencialProcessoImportacao();
        }
    }

    private void atualizarSequencialProcessoImportacao() throws Exception {
        ConfiguracaoSistema configDAO = new ConfiguracaoSistema(con);
        configDAO.incrementarSequencialProcessoImportacao();
        configDAO = null;
    }

    @Override
    public void alterar(ProcessoImportacaoVO obj) throws Exception {
        ProcessoImportacaoVO.validarDados(obj);
        obj.realizarUpperCaseDados();
        String sql = "UPDATE ProcessoImportacao set tipoImportacao = ?, sequencialImportacao = ?, descricao = ?, empresa = ?, usuario = ?, configuracao = ?, status = ?, dataInicio = ?, dataFim = ? WHERE codigo = ?";
        try (PreparedStatement ps = con.prepareStatement(sql)) {
            int i = 0;
            ps.setInt(++i, obj.getTipoImportacaoEnum().getCodigo());
            ps.setInt(++i, obj.getSequencialImportacao());
            ps.setString(++i, obj.getDescricao());
            resolveIntegerNull(ps, ++i, obj.getEmpresaVO().getCodigo());
            resolveIntegerNull(ps, ++i, obj.getUsuarioVO().getCodigo());
            ps.setString(++i, obj.getConfiguracao());
            ps.setString(++i, obj.getStatus());
            ps.setTimestamp(++i, Uteis.getDataJDBCTimestamp(obj.getDataInicio()));
            ps.setTimestamp(++i, Uteis.getDataJDBCTimestamp(obj.getDataFim()));
            ps.setInt(++i, obj.getCodigo());
            ps.execute();
        }
    }

    @Override
    public void excluir(ProcessoImportacaoVO obj) throws Exception {
        String sql = "DELETE FROM ProcessoImportacao WHERE codigo = ?";
        try (PreparedStatement ps = con.prepareStatement(sql)) {
            int i = 0;
            ps.setInt(++i, obj.getCodigo());
            ps.execute();
        }
    }

    public List<ProcessoImportacaoVO> montarDadosConsulta(ResultSet tabelaResultado, int nivelMontarDados, Connection con) throws Exception {
        List<ProcessoImportacaoVO> vetResultado = new ArrayList<>();
        while (tabelaResultado.next()) {
            ProcessoImportacaoVO obj = montarDados(tabelaResultado, nivelMontarDados, con);
            vetResultado.add(obj);
        }
        return vetResultado;
    }

    public ProcessoImportacaoVO montarDados(ResultSet rs, int nivelMontarDados, Connection con) throws Exception {
        ProcessoImportacaoVO obj = new ProcessoImportacaoVO();
        obj.setCodigo(rs.getInt("codigo"));
        obj.setNovoObj(false);
        obj.setTipoImportacaoEnum(TipoImportacaoEnum.obterPorCodigo(rs.getInt("tipoImportacao")));
        obj.setSequencialImportacao(rs.getInt("sequencialImportacao"));
        obj.setDescricao(rs.getString("descricao"));
        obj.getEmpresaVO().setCodigo(rs.getInt("empresa"));
        obj.getUsuarioVO().setCodigo(rs.getInt("usuario"));
        obj.setConfiguracao(rs.getString("configuracao"));
        obj.setStatus(rs.getString("status"));
        obj.setDataInicio(rs.getTimestamp("dataInicio"));
        obj.setDataFim(rs.getTimestamp("dataFim"));

        if (nivelMontarDados == Uteis.NIVELMONTARDADOS_DADOSBASICOS) {
            return obj;
        }

        montarDadosUsuario(obj, Uteis.NIVELMONTARDADOS_DADOSBASICOS, con);
        return obj;
    }

    private void montarDadosUsuario(ProcessoImportacaoVO obj, int nivelMontarDados, Connection con) throws Exception {
        if (UteisValidacao.emptyNumber(obj.getUsuarioVO().getCodigo())) {
            obj.setUsuarioVO(new UsuarioVO());
            return;
        }
        Usuario usuarioDAO = new Usuario(con);
        obj.setUsuarioVO(usuarioDAO.consultarPorChavePrimaria(obj.getUsuarioVO().getCodigo(), nivelMontarDados));
        usuarioDAO = null;
    }

    @Override
    public ProcessoImportacaoVO consultarPorChavePrimaria(Integer codigo, int nivelMontarDados) throws Exception {
        String sql = "SELECT * FROM ProcessoImportacao WHERE codigo = ?";
        try (PreparedStatement ps = con.prepareStatement(sql)) {
            int i = 0;
            ps.setInt(++i, codigo);
            try (ResultSet rs = ps.executeQuery()) {
                if (!rs.next()) {
                    return new ProcessoImportacaoVO();
                }
                return montarDados(rs, nivelMontarDados, this.con);
            }
        }
    }

    public List<ProcessoImportacaoVO> consultarTodos(Integer limit, int nivelMontarDados) throws Exception {
        StringBuilder sql = new StringBuilder();
        sql.append("SELECT * FROM ProcessoImportacao \n");
        sql.append("order by codigo desc \n");
        if (!UteisValidacao.emptyNumber(limit)) {
            sql.append("LIMIT ").append(limit).append(" \n");
        }
        try (PreparedStatement ps = con.prepareStatement(sql.toString())) {
            try (ResultSet rs = ps.executeQuery()) {
                return montarDadosConsulta(rs, nivelMontarDados, this.con);
            }
        }
    }

    public void atualizarStatus(ProcessoImportacaoVO obj) throws Exception {
        String sql = "UPDATE ProcessoImportacao set status = ? where codigo = ?";
        try (PreparedStatement sqlInserir = con.prepareStatement(sql)) {
            sqlInserir.setString(1, obj.getStatus());
            sqlInserir.setInt(2, obj.getCodigo());
            sqlInserir.execute();
        }
    }

    public ProcessoImportacaoVO iniciarProcessoImportacaoVO(ImportacaoConfigTO configTO, Integer totalItens) throws Exception {
        return iniciarProcessoImportacaoVO(configTO, Calendario.hoje(), totalItens);
    }

    public ProcessoImportacaoVO iniciarProcessoImportacaoVO(ImportacaoConfigTO configTO, Date dataInicio, Integer totalItens) throws Exception {
        ConfiguracaoSistema configDAO = new ConfiguracaoSistema(con);
        Integer seq = configDAO.obterSequencialProcessoImportacao();
        configDAO = null;

        ProcessoImportacaoVO obj = new ProcessoImportacaoVO();
        obj.setDataInicio(dataInicio);
        obj.setUsuarioVO(new UsuarioVO());
        obj.getUsuarioVO().setCodigo(configTO.getUsuarioResponsavelImportacao());
        obj.setTipoImportacaoEnum(configTO.getTipoImportacaoEnum());
        if (configTO.getTipoOperacaoIntegracaoMembersEnum() != null
                && configTO.getTipoOperacaoIntegracaoMembersEnum().getId().equals(TipoOperacaoIntegracaoMembersEnum.RECEBIMENTOS_PARCELAS.getId())) {
            obj.setTipoImportacaoEnum(TipoImportacaoEnum.MEMBERS_EVO_RECEBIMENTOS_PARCELAS);
        }
        obj.setSequencialImportacao(seq);
        obj.atualizarJSONStatus(0, 0, 0, totalItens);
        obj.setConfiguracao(configTO.obterJSON());
        incluir(obj);
        return obj;
    }

    public void finalizarProcessoImportacaoVO(ProcessoImportacaoVO obj, ImportacaoConfigTO configTO) throws Exception {
        obj.setDataFim(Calendario.hoje());
        alterar(obj);
        enviarEmailImportacao(configTO.getListaEmails(), obj);
    }

    public void enviarEmailImportacao(List<String> listaEmails, ProcessoImportacaoVO obj) {
        try {
            if (UteisValidacao.emptyList(listaEmails)) {
                return;
            }

            Uteis.logar(null, "Enviar Email | ProcessoImportacaoVO | Código " + obj.getCodigo());
            ConfiguracaoSistemaCRMVO configuracaoSistemaCRMVO = getConfiguracaoSMTPNoReply();
            UteisEmail email = new UteisEmail();
            String assunto = "Importação de " + Uteis.obterNomeComApenasPrimeiraLetraMaiuscula(obj.getTipoImportacaoEnum().getDescricao());
            String html = gerarCorpoEmailImportacao(obj).toString();
            email.novo(assunto, configuracaoSistemaCRMVO);
            for (String emailEnviar : listaEmails) {
                try {
                    email.enviarEmail(emailEnviar, "", html, "");
                } catch (Exception ignored) {
                }
            }
        } catch (Exception ex) {
            Uteis.logar(null, "Erro Enviar Email | ProcessoImportacaoVO | Código " + obj.getCodigo() + " | " + ex.getMessage());
            ex.printStackTrace();
        }
    }

    private StringBuilder gerarCorpoEmailImportacao(ProcessoImportacaoVO obj) throws Exception {
        File arq = new File(getClass().getResource("/br/com/pactosolucoes/comuns/util/resources/emailPactoImportacao.txt").toURI());
        StringBuilder texto = FileUtilities.readContentFile(arq.getAbsolutePath());
        String aux = texto.toString()
                .replaceAll("#DESCRICAO_PROCESSO", Uteis.obterNomeComApenasPrimeiraLetraMaiuscula(obj.getTipoImportacaoEnum().getDescricao()))
                .replaceAll("#TOTAL_GERAL", obj.getTotal().toString())
                .replaceAll("#TOTAL_SUCESSO", obj.getSucesso().toString())
                .replaceAll("#TOTAL_FALHA", obj.getFalha().toString())
                .replaceAll("#DATA_INICIO", obj.getDataInicioApresentar())
                .replaceAll("#DATA_TERMINO", obj.getDataFimApresentar());
        return new StringBuilder(aux);
    }


    public void iniciarThreadClientesContratos(TipoImportacaoEnum tipoEnum, String chave,
                                               Integer usuario, List<String> emails,
                                               List<ClienteImportacaoJSON> clientesJSON,
                                               List<ContratoImportacaoJSON> contratosJSON,
                                               ConfigExcelImportacaoTO configExcelImportacaoTO) throws Exception {
        ImportacaoConfigTO configTO = obterImportacaoConfigTO(tipoEnum, chave, usuario, emails);
        Connection conImportacao = Conexao.getInstance().obterNovaConexaoBaseadaOutra(this.con);
        ThreadImportacao thread = new ThreadImportacao(configTO, configExcelImportacaoTO, conImportacao, clientesJSON, contratosJSON, null, null, null, null, null, null, null, null, null, null, null);
        thread.setDaemon(true);
        thread.start();
    }

    public void iniciarThreadProdutos(TipoImportacaoEnum tipoEnum, String chave,
                                      Integer usuario, List<String> emails,
                                      List<ProdutoImportacaoJSON> produtosJSON,
                                      ConfigExcelImportacaoTO configExcelImportacaoTO) throws Exception {
        ImportacaoConfigTO configTO = obterImportacaoConfigTO(tipoEnum, chave, usuario, emails);
        Connection conImportacao = Conexao.getInstance().obterNovaConexaoBaseadaOutra(this.con);
        ThreadImportacao thread = new ThreadImportacao(configTO, configExcelImportacaoTO,conImportacao, null, null, produtosJSON, null, null, null, null, null, null, null, null, null, null);
        thread.setDaemon(true);
        thread.start();
    }

    public void iniciarThreadContasFinanceiro(TipoImportacaoEnum tipoEnum, String chave,
                                              Integer usuario, List<String> emails,
                                              List<ContaImportacaoJSON> contasJSON,
                                              ConfigExcelImportacaoTO configExcelImportacaoTO) throws Exception {
        ImportacaoConfigTO configTO = obterImportacaoConfigTO(tipoEnum, chave, usuario, emails);
        Connection conImportacao = Conexao.getInstance().obterNovaConexaoBaseadaOutra(this.con);
        ThreadImportacao thread = new ThreadImportacao(configTO, configExcelImportacaoTO,conImportacao, null, null, null, contasJSON, null, null, null, null, null, null, null, null, null);
        thread.setDaemon(true);
        thread.start();
    }

    @Override
    public void iniciarThreadAlunoTurma(TipoImportacaoEnum tipoEnum, String chave, Integer usuario, List<String> emails, List<AlunoTurmaImportacaoJSON> alunoTurmaJson, ConfigExcelImportacaoTO configExcelImportacaoTO) throws Exception {
        ImportacaoConfigTO configTO = obterImportacaoConfigTO(tipoEnum, chave, usuario, emails);
        Connection conImportacao = Conexao.getInstance().obterNovaConexaoBaseadaOutra(this.con);
        ThreadImportacao thread = new ThreadImportacao(configTO, configExcelImportacaoTO,conImportacao, null, null, null, null, null, null, alunoTurmaJson, null, null, null, null, null, null);
        thread.setDaemon(true);
        thread.start();
    }

    @Override
    public void iniciarThreadTurma(TipoImportacaoEnum tipoEnum, String chave, Integer usuario, List<String> emails, List<TurmaImportacaoJSON> turmaJson, ConfigExcelImportacaoTO configExcelImportacaoTO) throws Exception {
        ImportacaoConfigTO configTO = obterImportacaoConfigTO(tipoEnum, chave, usuario, emails);
        Connection conImportacao = Conexao.getInstance().obterNovaConexaoBaseadaOutra(this.con);
        ThreadImportacao thread = new ThreadImportacao(configTO, configExcelImportacaoTO,conImportacao, null, null, null, null, null, null, null, turmaJson, null, null, null, null, null);
        thread.setDaemon(true);
        thread.start();
    }


    public void iniciarThreadFornecedor(TipoImportacaoEnum tipoEnum, String chave,
                                        Integer usuario, List<String> emails,
                                        List<FornecedorImportacaoJSON> fornecedoresJSON,
                                        ConfigExcelImportacaoTO configExcelImportacaoTO) throws Exception {
        ImportacaoConfigTO configTO = obterImportacaoConfigTO(tipoEnum, chave, usuario, emails);
        Connection conImportacao = Conexao.getInstance().obterNovaConexaoBaseadaOutra(this.con);
        ThreadImportacao thread = new ThreadImportacao(configTO, configExcelImportacaoTO,conImportacao, null, null, null, null, fornecedoresJSON, null, null, null, null, null, null, null, null);
        thread.setDaemon(true);
        thread.start();
    }

    public void iniciarThreadColaborador(TipoImportacaoEnum tipoEnum, String chave,
                                         Integer usuario, List<String> emails,
                                         List<ColaboradorImportacaoJSON> colaboradoresJSON,
                                         ConfigExcelImportacaoTO configExcelImportacaoTO) throws Exception {
        ImportacaoConfigTO configTO = obterImportacaoConfigTO(tipoEnum, chave, usuario, emails);
        Connection conImportacao = Conexao.getInstance().obterNovaConexaoBaseadaOutra(this.con);
        ThreadImportacao thread = new ThreadImportacao(configTO, configExcelImportacaoTO,conImportacao, null, null, null, null, null, colaboradoresJSON, null, null, null, null, null, null, null);
        thread.setDaemon(true);
        thread.start();
    }

    public void iniciarThreadMembers(TipoImportacaoEnum tipoEnum, String chave, Integer usuario, IntegracaoMemberVO integracaoMemberVO, String idsMembers, TipoOperacaoIntegracaoMembersEnum tipoOperacaoIntegracaoMembersEnum) throws Exception {
        ImportacaoConfigTO configTO = obterImportacaoConfigTO(tipoEnum, chave, usuario, integracaoMemberVO, idsMembers, tipoOperacaoIntegracaoMembersEnum);
        Connection conImportacao = Conexao.getInstance().obterNovaConexaoBaseadaOutra(this.con);
        ThreadImportacao thread = new ThreadImportacao(configTO, null, conImportacao, null, null, null, null, null, null, null, null, null, null, null, null, null);
        thread.start();
    }

    public void iniciarThreadParcelasPagamentos(TipoImportacaoEnum tipoEnum, String chave, Integer usuario,  List<String> emails, List<ParcelaPagamentoJSON> listaParcelaPagamentoJson, ConfigExcelImportacaoTO configExcelImportacaoTO) throws Exception {
        ImportacaoConfigTO configTO = obterImportacaoConfigTO(tipoEnum, chave, usuario, emails);
        Connection conImportacao = Conexao.getInstance().obterNovaConexaoBaseadaOutra(this.con);
        ThreadImportacao thread = new ThreadImportacao(configTO, configExcelImportacaoTO,conImportacao,
                null, null, null, null,
                null, null, null, null, listaParcelaPagamentoJson, null, null, null, null);
        thread.setDaemon(true);
        thread.start();

    }

    public void iniciarThreadTreinoAtividades(TipoImportacaoEnum tipoEnum, String chave, Integer usuario,  List<String> emails, List<TreinoAtividadeJSON> listaTreinoAtividadesJson, ConfigExcelImportacaoTO configExcelImportacaoTO) throws Exception {
        ImportacaoConfigTO configTO = obterImportacaoConfigTO(tipoEnum, chave, usuario, emails);
        Connection conImportacao = Conexao.getInstance().obterNovaConexaoBaseadaOutra(this.con);
        ThreadImportacao thread = new ThreadImportacao(configTO, configExcelImportacaoTO,conImportacao,
                null, null, null, null,
                null, null, null, null, null, chave, listaTreinoAtividadesJson, null, null);
        thread.setDaemon(true);
        thread.start();

    }

    public void iniciarThreadTreinoProgramas(TipoImportacaoEnum tipoEnum, String chave, Integer usuario,  List<String> emails, List<ProgramaFichaJSON> listaTreinoProgramasJson, ConfigExcelImportacaoTO configExcelImportacaoTO) throws Exception {
        ImportacaoConfigTO configTO = obterImportacaoConfigTO(tipoEnum, chave, usuario, emails);
        Connection conImportacao = Conexao.getInstance().obterNovaConexaoBaseadaOutra(this.con);
        ThreadImportacao thread = new ThreadImportacao(configTO, configExcelImportacaoTO,conImportacao,
                null, null, null, null,
                null, null, null, null, null, chave, null, listaTreinoProgramasJson, null);
        thread.setDaemon(true);
        thread.start();

    }

    public void iniciarThreadTreinoAtividadeFicha(TipoImportacaoEnum tipoEnum, String chave, Integer usuario,  List<String> emails, List<AtividadeFichaJSON> listaTreinoAtividadeFichaJson, ConfigExcelImportacaoTO configExcelImportacaoTO) throws Exception {
        ImportacaoConfigTO configTO = obterImportacaoConfigTO(tipoEnum, chave, usuario, emails);
        Connection conImportacao = Conexao.getInstance().obterNovaConexaoBaseadaOutra(this.con);
        ThreadImportacao thread = new ThreadImportacao(configTO, configExcelImportacaoTO,conImportacao,
                null, null, null, null,
                null, null, null, null, null, chave, null, null, listaTreinoAtividadeFichaJson);
        thread.setDaemon(true);
        thread.start();

    }

    private ImportacaoConfigTO obterImportacaoConfigTO(TipoImportacaoEnum tipoImportacaoEnum, String chave,
                                                       Integer usuario, List<String> emails){
        ImportacaoConfigTO obj = new ImportacaoConfigTO();
        obj.setTipoImportacaoEnum(tipoImportacaoEnum);
        obj.setChave(chave);
        obj.setUsuarioResponsavelImportacao(usuario);
        obj.setListaEmails(emails);
        return obj;
    }

    private ImportacaoConfigTO obterImportacaoConfigTO(TipoImportacaoEnum tipoImportacaoEnum, String chave,
                                                       Integer usuario, IntegracaoMemberVO integracaoMemberVO, String idsMembers, TipoOperacaoIntegracaoMembersEnum tipoOperacaoIntegracaoMembersEnum){
        ImportacaoConfigTO obj = new ImportacaoConfigTO();
        obj.setTipoImportacaoEnum(tipoImportacaoEnum);
        obj.setChave(chave);
        obj.setUsuarioResponsavelImportacao(usuario);
        obj.setListaEmails(new ArrayList<>());
        obj.setIntegracaoMemberVO(integracaoMemberVO);
        obj.setIdsMembers(idsMembers);
        obj.setTipoOperacaoIntegracaoMembersEnum(tipoOperacaoIntegracaoMembersEnum);
        return obj;
    }

    @Override
    public ProcessoImportacaoVO consultarUltimoExecutado(int nivelMontarDados) throws Exception {
        String sql = "SELECT * FROM ProcessoImportacao ORDER BY codigo DESC LIMIT 1";
        try (ResultSet rs = con.createStatement().executeQuery(sql)) {
            if (rs.next()) {
                return montarDados(rs, nivelMontarDados, this.con);
            } else {
                return new ProcessoImportacaoVO();
            }
        }
    }

    public void atualizarTipoProcesso(ProcessoImportacaoVO obj) throws Exception {
        String sql = "UPDATE ProcessoImportacao set tipoImportacao = ? where codigo = ?";
        try (PreparedStatement sqlInserir = con.prepareStatement(sql)) {
            sqlInserir.setInt(1, obj.getTipoImportacaoEnum().getCodigo());
            sqlInserir.setInt(2, obj.getCodigo());
            sqlInserir.execute();
        }
    }
}

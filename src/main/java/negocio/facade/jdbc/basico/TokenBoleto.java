package negocio.facade.jdbc.basico;

import negocio.comuns.utilitarias.Calendario;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisValidacao;
import negocio.facade.jdbc.arquitetura.SuperEntidade;
import servicos.boleto.TokenBoletoVO;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.util.UUID;

public class TokenBoleto extends SuperEntidade {

    public TokenBoleto() throws Exception {
        super();
    }

    public TokenBoleto(Connection connection) throws Exception {
        super(connection);
    }

    public static TokenBoletoVO montarDados(ResultSet dadosSQL, int nivelMontarDados) throws Exception {
        TokenBoletoVO obj = new TokenBoletoVO();
        obj.setCodigo(dadosSQL.getInt("codigo"));
        obj.setDataRegistro(dadosSQL.getTimestamp("dataRegistro"));
        obj.setToken(dadosSQL.getString("token"));
        obj.setDados(dadosSQL.getString("dados"));

        if (nivelMontarDados == Uteis.NIVELMONTARDADOS_DADOSBASICOS) {
            return obj;
        }

        return obj;
    }

    public TokenBoletoVO gerarToken(String dados) throws Exception {
        TokenBoletoVO obj = new TokenBoletoVO();
        obj.setToken(UUID.randomUUID().toString());
        obj.setDados(dados);
        obj.setDataRegistro(Calendario.hoje());

        String sql = "INSERT INTO public.tokenboleto(dataregistro,token,dados) VALUES (?, ?, ?) RETURNING CODIGO;";

        PreparedStatement ps = con.prepareStatement(sql);
        ps.setTimestamp(1, Uteis.getDataJDBCTimestamp(obj.getDataRegistro()));
        ps.setString(2, obj.getToken());
        ps.setString(3, obj.getDados());
        ResultSet rs = ps.executeQuery();
        if (rs.next()) {
            obj.setCodigo(rs.getInt("codigo"));
        }
        return obj;
    }

    public void alterar(TokenBoletoVO obj) throws Exception {
        String sql = "UPDATE public.tokenboleto SET dados = ? WHERE codigo = ?;";
        PreparedStatement ps = con.prepareStatement(sql);
        ps.setString(1, obj.getDados());
        ps.setInt(2, obj.getCodigo());
        ps.execute();
    }

    public TokenBoletoVO consultarPorToken(String token) throws Exception {
        if (UteisValidacao.emptyString(token)) {
            return null;
        }

        String sql = "SELECT * FROM tokenboleto WHERE token = ?";
        try (PreparedStatement ps = con.prepareStatement(sql)) {
            ps.setString(1, token);
            try (ResultSet rs = ps.executeQuery()) {
                if (rs.next()) {
                    return montarDados(rs, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
                }
            }
        }
        return null;
    }
}


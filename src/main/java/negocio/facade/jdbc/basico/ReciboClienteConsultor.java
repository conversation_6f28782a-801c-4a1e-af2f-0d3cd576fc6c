package negocio.facade.jdbc.basico;

import br.com.pactosolucoes.oamd.controle.basico.DAO;
import negocio.comuns.arquitetura.UsuarioVO;
import negocio.comuns.basico.ClienteVO;
import negocio.comuns.basico.ColaboradorVO;
import negocio.comuns.basico.EmpresaVO;
import negocio.comuns.contrato.ContratoVO;
import negocio.comuns.financeiro.ProdutoPago;
import negocio.comuns.financeiro.RecCliConsultorMovPgTO;
import negocio.comuns.financeiro.RecCliConsultorMovProdTO;
import negocio.comuns.financeiro.RecCliConsultorTO;
import negocio.comuns.financeiro.ReciboClienteConsultorVO;
import negocio.comuns.financeiro.ReciboPagamentoVO;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisValidacao;
import negocio.facade.jdbc.arquitetura.SuperEntidade;
import negocio.facade.jdbc.arquitetura.Usuario;
import negocio.facade.jdbc.utilitarias.Conexao;
import negocio.interfaces.basico.ReciboClienteConsultorInterfaceFacade;

import java.sql.Connection;
import java.sql.DriverManager;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.sql.Statement;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * Created with IntelliJ IDEA.
 * User: glauco
 * Date: 15/02/13
 * Time: 13:58
 */
public class ReciboClienteConsultor extends SuperEntidade implements ReciboClienteConsultorInterfaceFacade {


    public ReciboClienteConsultor() throws Exception {
    }

    public ReciboClienteConsultor(Connection conexao) throws Exception {
        super(conexao);
    }

    @Override
    public ReciboClienteConsultorVO novo() throws Exception {
        incluir(getIdEntidade());
        return new ReciboClienteConsultorVO();
    }

    @Override
    public void incluir(ReciboClienteConsultorVO obj) throws Exception {
        ReciboClienteConsultorVO.validarDados(obj);
        String sql = "INSERT INTO public.reciboclienteconsultor (recibo, consultor,responsavelParcela, cliente, valor, formapagamento) VALUES (?, ?, ?, ?, ? , ?);";
        PreparedStatement sqlInserir = con.prepareStatement(sql);
        sqlInserir.setInt(1, obj.getRecibo().getCodigo());

        if (obj.getConsultor().getCodigo() != null && obj.getConsultor().getCodigo() != 0) {
            sqlInserir.setInt(2, obj.getConsultor().getCodigo());
        } else {
            sqlInserir.setNull(2, 0);
        }
        if (obj.getResponsavelPagamento().getCodigo() != null && obj.getResponsavelPagamento().getCodigo() != 0) {
            sqlInserir.setInt(3, obj.getResponsavelPagamento().getCodigo());
        } else {
            sqlInserir.setNull(3, 0);
        }
        if (obj.getCliente().getCodigo() != null && obj.getCliente().getCodigo() != 0) {
            sqlInserir.setInt(4, obj.getCliente().getCodigo());
        } else {
            sqlInserir.setNull(4, 0);
        }

        sqlInserir.setDouble(5, obj.getValor());
        sqlInserir.setInt(6, obj.getFormaPagamento().getCodigo());
        sqlInserir.execute();
        obj.setCodigo(obterValorChavePrimariaCodigo());
        obj.setNovoObj(false);
    }

    @Override
    public void alterar(ReciboClienteConsultorVO obj) throws Exception {
        try {
            con.setAutoCommit(false);
            ReciboClienteConsultorVO.validarDados(obj);
            alterar(getIdEntidade());
            String sql = "UPDATE public.reciboclienteconsultor\n" +
                    "SET cliente = ?, consultor = ?, responsavelParcela = ?, formapagamento = ?, recibo = ?, valor = ? \n" +
                    "WHERE codigo = ?;";

            PreparedStatement sqlAlterar = con.prepareStatement(sql);
            sqlAlterar.setInt(1, obj.getRecibo().getCodigo());

            if (obj.getConsultor().getCodigo() != null && obj.getConsultor().getCodigo() != 0) {
                sqlAlterar.setInt(2, obj.getConsultor().getCodigo());
            } else {
                sqlAlterar.setNull(2, 0);
            }
            if (obj.getResponsavelPagamento().getCodigo() != null && obj.getResponsavelPagamento().getCodigo() != 0) {
                sqlAlterar.setInt(3, obj.getResponsavelPagamento().getCodigo());
            } else {
                sqlAlterar.setNull(3, 0);
            }
            sqlAlterar.setInt(4, obj.getCliente().getCodigo());
            sqlAlterar.setDouble(5, obj.getValor());
            sqlAlterar.setInt(6, obj.getFormaPagamento().getCodigo());

            sqlAlterar.setInt(7, obj.getCodigo());
            sqlAlterar.execute();

            con.commit();
        } catch (Exception e) {
            con.rollback();
            con.setAutoCommit(true);
            throw e;
        } finally {
            con.setAutoCommit(true);
        }
    }

    @Override
    public void excluir(ReciboClienteConsultorVO obj) throws Exception {
        try {
            con.setAutoCommit(false);
            excluir(getIdEntidade());

            String sql = "DELETE FROM public.reciboclienteconsultor\n" +
                    "        WHERE codigo = ?";

            PreparedStatement sqlExcluir = con.prepareStatement(sql);
            sqlExcluir.setInt(1, obj.getCodigo());
            sqlExcluir.execute();

            con.commit();
        } catch (Exception e) {
            con.rollback();
            con.setAutoCommit(true);
            throw e;
        } finally {
            con.setAutoCommit(true);
        }
    }

    @Override
    public void excluirPorReciboPagamento(final Integer codRecibo) throws Exception {
        String sql = "DELETE FROM reciboclienteconsultor WHERE recibo = ?";

        PreparedStatement sqlExcluir = con.prepareStatement(sql);
        sqlExcluir.setInt(1, codRecibo);
        sqlExcluir.execute();
    }

    public boolean excluirDados() throws Exception {
        String sql = "DELETE FROM reciboclienteconsultor;";

        PreparedStatement preparedStatement = con.prepareStatement(sql);
        preparedStatement.execute();
        return true;
    }

    public boolean selectMigradorPovoarReciboClienteConsultor() throws Exception {
        String sql = "SELECT migrador_povoarreciboclienteconsultor();";

        PreparedStatement preparedStatement = con.prepareStatement(sql);

        preparedStatement.execute();
        return true;
    }

    @Override
    public void atualizarRecibosSemConsultor(ColaboradorVO consultor, EmpresaVO empresaVO) throws Exception {
        try {
            con.setAutoCommit(false);
            String sql = "UPDATE public.reciboclienteconsultor\n" +
                    "SET consultor = ?\n" +
                    "WHERE recibo IN (SELECT\n" +
                    "                   codigo\n" +
                    "                 FROM public.recibopagamento rp\n" +
                    "                 WHERE empresa = ?) AND (consultor IS null OR consultor = 0);";

            PreparedStatement sqlAlterar = con.prepareStatement(sql);
            sqlAlterar.setInt(1, consultor.getCodigo());
            sqlAlterar.setInt(2, empresaVO.getCodigo());
            sqlAlterar.execute();
            con.commit();
        } catch (Exception e) {
            con.rollback();
            con.setAutoCommit(true);
            throw e;
        } finally {
            con.setAutoCommit(true);
        }
    }

    @Override
    public void incluirComRecibo(ReciboPagamentoVO obj) throws Exception {
        String sql = "(SELECT\n" +
                "  DISTINCT\n" +
                "  rp.codigo          AS recibo,\n" +
                "  rp.data            AS dataRec,\n" +
                "  cli.codigo         AS cliente,\n" +
                "  fp.codigo          AS formaPagamento,\n" +
                "  mpar.responsavel        AS responsavelParcela,\n"+
                "  sum(pmp.valorpago) AS valor\n" +
                "FROM public.recibopagamento rp\n" +
                "  INNER JOIN public.movpagamento mpag\n" +
                "    ON (mpag.recibopagamento = rp.codigo AND mpag.credito <> true)\n" +
                "  LEFT JOIN public.formapagamento fp\n" +
                "    ON mpag.formapagamento = fp.codigo\n" +
                "  LEFT JOIN public.pagamentomovparcela pmp\n" +
                "    ON pmp.movpagamento = mpag.codigo\n" +
                "  LEFT JOIN public.movparcela mpar\n" +
                "    ON mpar.codigo = pmp.movparcela\n" +
                "  LEFT JOIN public.cliente cli\n" +
                "    ON mpar.pessoa = cli.pessoa\n" +
                "WHERE rp.codigo = ?\n" +
                " and mpar.descricao not like '%EDIÇÃO DE PAGAMENTO%' " +
                " GROUP BY 1, 2, 3, 4 , 5\n" +
                "ORDER BY 1)";

        PreparedStatement stm = con.prepareStatement(sql);
        stm.setInt(1, obj.getCodigo());
        ResultSet tabelaResultado = stm.executeQuery();
        Date dataPesquisa = obj.getData();
        if(obj.getData().getHours() == 0 && obj.getData().getMinutes() == 0 && obj.getData().getSeconds()== 0){ // data base alterada
            dataPesquisa.setHours(23);
            dataPesquisa.setMinutes(59);
            dataPesquisa.setSeconds(59);
        }
        while (tabelaResultado.next()) {
            ReciboClienteConsultorVO reciboClienteConsultor = new ReciboClienteConsultorVO();

            ClienteVO clienteVO = new ClienteVO();
            clienteVO.setCodigo(tabelaResultado.getInt("cliente"));
            if (clienteVO.getCodigo() != 0) {
                reciboClienteConsultor.setCliente(clienteVO);
            } else {
                reciboClienteConsultor.getCliente().setCodigo(null);
            }
            reciboClienteConsultor.getFormaPagamento().setCodigo(tabelaResultado.getInt("formapagamento"));
            reciboClienteConsultor.setValor(tabelaResultado.getDouble("valor"));
            reciboClienteConsultor.getRecibo().setCodigo(tabelaResultado.getInt("recibo"));

            Colaborador colaboradorDAO = new Colaborador(con);
            ColaboradorVO colaboradorVO = colaboradorDAO.consultarConsultorDoClienteNaData(reciboClienteConsultor.getCliente().getCodigo(), dataPesquisa);
            colaboradorDAO = null;

            Usuario usuarioDAO = new Usuario(con);
            UsuarioVO usuario = usuarioDAO.consultarPorCodigoColaborador(obj.getEmpresa().getConsultorVendaAvulsa().getCodigo(),Uteis.NIVELMONTARDADOS_DADOSBASICOS);
            usuarioDAO = null;

            if (colaboradorVO.getCodigo() == 0) {
                colaboradorVO = obj.getEmpresa().getConsultorVendaAvulsa();
            }
            if(UteisValidacao.emptyNumber(usuario.getCodigo()) &&
                    !UteisValidacao.emptyNumber(obj.getResponsavelLancamento().getCodigo())){
                reciboClienteConsultor.setResponsavelPagamento(obj.getResponsavelLancamento());
            }else{
                reciboClienteConsultor.setResponsavelPagamento(usuario);
            }
            reciboClienteConsultor.setConsultor(colaboradorVO);

            this.incluir(reciboClienteConsultor);
        }
    }
    
    public void alterarConsultorContrato(ContratoVO contratoVO, Integer cliente) throws Exception{
        List<ReciboPagamentoVO> lista = getFacade().getReciboPagamento().consularReciboPagamParcelaPorCodigoContrato(contratoVO.getCodigo(), Uteis.NIVELMONTARDADOS_TELACONSULTA);
        String recibos = "";
        for (ReciboPagamentoVO recibo : lista) {
            recibos += recibo.getCodigo().toString() + ",";
        }
        if(!UteisValidacao.emptyList(lista)) {
            recibos = recibos.substring(0, (recibos.length() - 1));
            String sqlAlterar = "UPDATE public.reciboclienteconsultor "
                    + " SET  consultor = " + contratoVO.getConsultor().getCodigo()
                    + " WHERE cliente = " + cliente + " and recibo in (" + recibos + ");";
            executarConsulta(sqlAlterar, getCon());
        }

    }

    public ColaboradorVO consultarPorReciboCliente(Integer empresa, Integer codRecibo, Integer codCliente) throws Exception {
        String sqlStr = "SELECT * FROM reciboclienteconsultor WHERE recibo = ? AND cliente = ? LIMIT 1";
        PreparedStatement ps = con.prepareStatement(sqlStr);
        ps.setInt(1, codRecibo);
        ps.setInt(2, codCliente);
        ResultSet tabelaResultado = ps.executeQuery();
        if (tabelaResultado.next()) {
            Integer codColaborador = tabelaResultado.getInt("consultor");
            Colaborador colaboradorDAO = new Colaborador(con);
            ColaboradorVO colaboradorVO = colaboradorDAO.consultarColaboradorPorCodigo(codColaborador, empresa, false, Uteis.NIVELMONTARDADOS_MINIMOS);
            colaboradorDAO = null;
            return colaboradorVO;
        }
        return null;
    }

    public void excluirTodos() throws Exception {
        String sql = "DELETE FROM public.reciboclienteconsultor";

        PreparedStatement sqlExcluir = con.prepareStatement(sql);
        sqlExcluir.execute();
        if (!sqlExcluir.isClosed()) {
            sqlExcluir.close();
        }
        System.out.println("Todos os registros em reciboClienteConsultor foram excluídos.");
    }

    public void processarTodos() throws Exception {
        String sql = "SELECT codigo FROM recibopagamento;";

        PreparedStatement psRecibos = con.prepareStatement(sql);
        ResultSet rs = psRecibos.executeQuery();
        while (rs.next()) {
            final int codRecibo = rs.getInt("codigo");
            processarReciboClienteConsultor(codRecibo);
            System.out.println("O recibo " + codRecibo + " foi processado.");
        }
        fecharConsulta(psRecibos, rs);
    }

    public void processarReciboClienteConsultor(int codRecibo) throws Exception {
        excluirPorReciboPagamento(codRecibo);

        RecCliConsultorTO recCliConsultorTO = new RecCliConsultorTO();
        recCliConsultorTO.setCodRecibo(codRecibo);

        String sqlRecibo = "SELECT rp.data,\n" +
                "   rp.responsavellancamento as responsavel\n" +
                "FROM recibopagamento rp\n" +
                "WHERE rp.codigo = " + codRecibo;
        Statement stm = con.createStatement();
        ResultSet rs = stm.executeQuery(sqlRecibo);
        if (rs.next()) {
            recCliConsultorTO.setDataRecibo(rs.getTimestamp("data"));
            recCliConsultorTO.setResponsavelLancamentoPagamento(rs.getInt("responsavel"));
        }
        fecharConsulta(stm, rs);

        String sqlMovPagamento = "SELECT mpag.codigo,\n" +
                "       mpag.formapagamento,\n" +
                "       mpag.produtospagos\n" +
                "FROM movpagamento mpag\n" +
                "WHERE 1 = 1\n" +
                "  AND mpag.credito <> true\n" +
                "  AND recibopagamento = " + recCliConsultorTO.getCodRecibo();
        stm = con.createStatement();
        rs = stm.executeQuery(sqlMovPagamento);
        while (rs.next()) {
            RecCliConsultorMovPgTO movPagamentoTO = new RecCliConsultorMovPgTO();
            movPagamentoTO.setFormaPagamento(rs.getInt("formapagamento"));
            movPagamentoTO.setProdutosPagos(rs.getString("produtospagos"));
            recCliConsultorTO.getMovPagamentos().add(movPagamentoTO);
        }
        fecharConsulta(stm, rs);


        Colaborador colaboradorDAO = new Colaborador(con);
        for (RecCliConsultorMovPgTO movPagamentoTO : recCliConsultorTO.getMovPagamentos()) {
            Map<Integer, RecCliConsultorMovProdTO> relacaoPorClientes = new HashMap<Integer, RecCliConsultorMovProdTO>();
            for (ProdutoPago produtoPago : movPagamentoTO.getListaProdutosPagos()) {
                String sqlMovProduto = "SELECT mprod.codigo, cli.codigo as cliente, pes.nome as nomeCliente\n" +
                        "FROM movproduto mprod\n" +
                        "       LEFT JOIN cliente cli ON mprod.pessoa = cli.pessoa\n" +
                        "       LEFT JOIN pessoa pes ON cli.pessoa = pes.codigo\n" +
                        "WHERE mprod.codigo =" + produtoPago.getProduto();
                stm = con.createStatement();
                rs = stm.executeQuery(sqlMovProduto);
                if (rs.next()) {
                    Integer codCliente = rs.getInt("cliente");
                    RecCliConsultorMovProdTO recCliConsultorMovProdTO = relacaoPorClientes.get(codCliente);
                    if (recCliConsultorMovProdTO == null) {
                        ColaboradorVO colaboradorVO = colaboradorDAO.consultarConsultorDoClienteNaData(codCliente, recCliConsultorTO.getDataRecibo());

                        recCliConsultorMovProdTO = new RecCliConsultorMovProdTO();
                        recCliConsultorMovProdTO.setCodCliente(codCliente);
                        recCliConsultorMovProdTO.setCodConsultorEpoca(colaboradorVO.getCodigo());
                        recCliConsultorMovProdTO.setNomeCliente(rs.getString("nomeCliente"));
                        relacaoPorClientes.put(codCliente, recCliConsultorMovProdTO);
                    }

                    recCliConsultorMovProdTO.setValor(recCliConsultorMovProdTO.getValor() + produtoPago.getValor());
                }
            }
            movPagamentoTO.setMovProdutos(new ArrayList<RecCliConsultorMovProdTO>(relacaoPorClientes.values()));
        }

        List<ReciboClienteConsultorVO> recibosClientesConsultor = recCliConsultorTO.toReciboClienteConsultor();

        for (ReciboClienteConsultorVO rccVO : recibosClientesConsultor) {
            incluir(rccVO);
        }
    }

    private void fecharConsulta(Statement stm, ResultSet rs) throws SQLException {
        if (!rs.isClosed()) {
            rs.close();
        }
        if (!stm.isClosed()) {
            stm.close();
        }
    }

    public boolean existeReciboVendaAvulsaConsumidorNaData(Integer codigoEmpresa, Date dataInicio, Date dataFim) throws Exception {
        return existe("SELECT rcc.codigo \n" +
                "FROM reciboclienteconsultor rcc\n" +
                "INNER JOIN recibopagamento rpg ON rpg.codigo = rcc.recibo\n" +
                "INNER JOIN pagamentomovparcela pmp ON pmp.recibopagamento = rpg.codigo\n" +
                "INNER JOIN movparcela mpv ON mpv.codigo = pmp.movparcela\n" +
                "INNER JOIN vendaavulsa vav ON vav.codigo = mpv.vendaavulsa\n" +
                "WHERE rcc.consultor IS NULL AND rcc.cliente IS NULL\n" +
                "AND vav.tipocomprador = 'CN'\n"+
                "AND rpg.empresa = " + codigoEmpresa + "\n" +
                "AND rpg.data BETWEEN '" + Uteis.getDataJDBCTimestamp(dataInicio) + "' AND '" + Uteis.getDataJDBCTimestamp(dataFim) + "'", con);
    }

    public double consultarValoresReciboVendaAvulsaNaData(Integer codigoEmpresa, Date dataInicio, Date dataFim) throws Exception {
        String sqlRecibo = "SELECT SUM(rcc.valor) AS valorRecibo \n" +
                "FROM reciboclienteconsultor rcc\n" +
                "INNER JOIN recibopagamento rpg ON rpg.codigo = rcc.recibo\n" +
                "INNER JOIN pagamentomovparcela pmp ON pmp.recibopagamento = rpg.codigo\n" +
                "INNER JOIN movparcela mpv ON mpv.codigo = pmp.movparcela\n" +
                "INNER JOIN vendaavulsa vav ON vav.codigo = mpv.vendaavulsa\n" +
                "WHERE rcc.consultor IS NULL AND rcc.cliente IS NULL\n" +
                "AND vav.tipocomprador = 'CN'\n" +
                "AND rpg.empresa = " + codigoEmpresa + "\n" +
                "AND rpg.data BETWEEN '" + Uteis.getDataJDBCTimestamp(dataInicio) + "' AND '" + Uteis.getDataJDBCTimestamp(dataFim) + "'";
        Statement stm = con.createStatement();
        ResultSet rs = stm.executeQuery(sqlRecibo);
        if (rs.next()) {
            return rs.getDouble("valorRecibo");
        }
        return 0.0;
    }

    public static void main(String args[]) {
        try {
            Connection con1 = null;
            if (args.length == 0) {
                con1 = DriverManager.getConnection("************************************************************", "postgres", "pactodb");
            } else {
                con1 = new DAO().obterConexaoEspecifica(args[0]);
            }
            Conexao.guardarConexaoForJ2SE(con1);
            ReciboClienteConsultor reciboClienteConsultor = new ReciboClienteConsultor(con1);
//            reciboClienteConsultor.excluirTodos();
//            reciboClienteConsultor.processarTodos();
            reciboClienteConsultor.processarReciboClienteConsultor(997);
//            reciboClienteConsultor.processarReciboClienteConsultor(1119);
//            reciboClienteConsultor.processarReciboClienteConsultor(1189);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }
}

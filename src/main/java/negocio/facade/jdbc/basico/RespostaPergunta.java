package negocio.facade.jdbc.basico;

import java.util.Iterator;
import negocio.comuns.basico.RespostaPerguntaVO;
import negocio.facade.jdbc.arquitetura.*;

import java.sql.Connection;
import java.sql.ResultSet;
import java.sql.PreparedStatement;
import java.sql.Statement;
import java.util.List;
import java.util.ArrayList;
import negocio.comuns.utilitarias.*;
import negocio.interfaces.basico.RespostaPerguntaInterfaceFacade;

/**
 * Classe de persistência que encapsula todas as operações de manipulação dos dados da classe <code>RespostaPerguntaVO</code>.
 * Responsável por implementar operações como incluir, alterar, excluir e consultar pertinentes a classe <code>RespostaPerguntaVO</code>.
 * Encapsula toda a interação com o banco de dados.
 * @see RespostaPerguntaVO
 * @see SuperEntidade
 * @see Pergunta
 */
public class RespostaPergunta extends SuperEntidade implements RespostaPerguntaInterfaceFacade {    

    public RespostaPergunta() throws Exception {
        super();
        setIdEntidade("Pergunta");
    }

    public RespostaPergunta(Connection con) throws Exception {
    	super(con);
        setIdEntidade("Pergunta");
	}

	/**
     * Operação responsável por retornar um novo objeto da classe <code>RespostaPerguntaVO</code>.
     */
    public RespostaPerguntaVO novo() throws Exception {
        incluir(getIdEntidade());
        RespostaPerguntaVO obj = new RespostaPerguntaVO();
        return obj;
    }

    /**
     * Operação responsável por incluir no banco de dados um objeto da classe <code>RespostaPerguntaVO</code>.
     * Primeiramente valida os dados (<code>validarDados</code>) do objeto. Verifica a conexão com o banco de dados e a permissão do usuário
     * para realizar esta operacão na entidade.
     * Isto, através da operação <code>incluir</code> da superclasse.
     * @param obj  Objeto da classe <code>RespostaPerguntaVO</code> que será gravado no banco de dados.
     * @exception Exception Caso haja problemas de conexão, restrição de acesso ou validação de dados.
     */
    public void incluir(RespostaPerguntaVO obj) throws Exception {
        RespostaPerguntaVO.validarDados(obj);
        incluir(getIdEntidade());
        obj.realizarUpperCaseDados();
        String sql = "INSERT INTO RespostaPergunta( pergunta, descricaoRespota, nrQuestao ) VALUES ( ?, ?, ? )";
        try (PreparedStatement sqlInserir = con.prepareStatement(sql)) {
            if (obj.getPergunta().intValue() != 0) {
                sqlInserir.setInt(1, obj.getPergunta().intValue());
            } else {
                sqlInserir.setNull(1, 0);
            }
            sqlInserir.setString(2, obj.getDescricaoRespota());
            sqlInserir.setInt(3, obj.getNrQuestao());
            sqlInserir.execute();
        }
        obj.setCodigo(obterValorChavePrimariaCodigo());
        obj.setNovoObj(new Boolean(false));
    }

    /**
     * Operação responsável por alterar no BD os dados de um objeto da classe <code>RespostaPerguntaVO</code>.
     * Sempre utiliza a chave primária da classe como atributo para localização do registro a ser alterado.
     * Primeiramente valida os dados (<code>validarDados</code>) do objeto. Verifica a conexão com o banco de dados e a permissão do usuário
     * para realizar esta operacão na entidade.
     * Isto, através da operação <code>alterar</code> da superclasse.
     * @param obj    Objeto da classe <code>RespostaPerguntaVO</code> que será alterada no banco de dados.
     * @exception Execption Caso haja problemas de conexão, restrição de acesso ou validação de dados.
     */
    public void alterar(RespostaPerguntaVO obj) throws Exception {
        RespostaPerguntaVO.validarDados(obj);
        alterar(getIdEntidade());
        obj.realizarUpperCaseDados();
        String sql = "UPDATE RespostaPergunta set pergunta=?, descricaoRespota=?, nrQuestao =? WHERE ((codigo = ?))";
        try (PreparedStatement sqlAlterar = con.prepareStatement(sql)) {
            if (obj.getPergunta().intValue() != 0) {
                sqlAlterar.setInt(1, obj.getPergunta().intValue());
            } else {
                sqlAlterar.setNull(1, 0);
            }
            sqlAlterar.setString(2, obj.getDescricaoRespota());
            sqlAlterar.setInt(3, obj.getNrQuestao());
            sqlAlterar.setInt(4, obj.getCodigo().intValue());
            sqlAlterar.execute();
        }
    }

    /**
     * Operação responsável por excluir no BD um objeto da classe <code>RespostaPerguntaVO</code>.
     * Sempre localiza o registro a ser excluído através da chave primária da entidade.
     * Primeiramente verifica a conexão com o banco de dados e a permissão do usuário
     * para realizar esta operacão na entidade.
     * Isto, através da operação <code>excluir</code> da superclasse.
     * @param obj    Objeto da classe <code>RespostaPerguntaVO</code> que será removido no banco de dados.
     * @exception Execption Caso haja problemas de conexão ou restrição de acesso.
     */
    public void excluir(RespostaPerguntaVO obj) throws Exception {
        excluir(getIdEntidade());
        String sql = "DELETE FROM RespostaPergunta WHERE ((codigo = ?))";
        try (PreparedStatement sqlExcluir = con.prepareStatement(sql)) {
            sqlExcluir.setInt(1, obj.getCodigo().intValue());
            sqlExcluir.execute();
        }
    }

    /**
     * Responsável por realizar uma consulta de <code>RespostaPergunta</code> através do valor do atributo 
     * <code>String descricaoRespota</code>. Retorna os objetos, com início do valor do atributo idêntico ao parâmetro fornecido.
     * Faz uso da operação <code>montarDadosConsulta</code> que realiza o trabalho de prerarar o List resultante.
     * @param   controlarAcesso Indica se a aplicação deverá verificar se o usuário possui permissão para esta consulta ou não.
     * @return  List Contendo vários objetos da classe <code>RespostaPerguntaVO</code> resultantes da consulta.
     * @exception Exception Caso haja problemas de conexão ou restrição de acesso.
     */
    public List consultarPorDescricaoRespota(String valorConsulta, boolean controlarAcesso, int nivelMontarDados) throws Exception {
        consultar(getIdEntidade(), controlarAcesso);
        String sqlStr = "SELECT * FROM RespostaPergunta WHERE upper( descricaoRespota ) like('" + valorConsulta.toUpperCase() + "%') ORDER BY descricaoRespota";
        try (Statement stm = con.createStatement()) {
            try (ResultSet tabelaResultado = stm.executeQuery(sqlStr)) {
                return (montarDadosConsulta(tabelaResultado, nivelMontarDados));
            }
        }
    }

    /**
     * Responsável por realizar uma consulta de <code>RespostaPergunta</code> através do valor do atributo 
     * <code>descricao</code> da classe <code>Pergunta</code>
     * Faz uso da operação <code>montarDadosConsulta</code> que realiza o trabalho de prerarar o List resultante.
     * @return  List Contendo vários objetos da classe <code>RespostaPerguntaVO</code> resultantes da consulta.
     * @exception Execption Caso haja problemas de conexão ou restrição de acesso.
     */
    public List consultarPorDescricaoPergunta(String valorConsulta, int nivelMontarDados) throws Exception {
        consultar(getIdEntidade(), true);
        String sqlStr = "SELECT RespostaPergunta.* FROM RespostaPergunta, Pergunta WHERE RespostaPergunta.pergunta = Pergunta.codigo and upper( Pergunta.descricao ) like('" + valorConsulta.toUpperCase() + "%') ORDER BY Pergunta.descricao";
        try (Statement stm = con.createStatement()) {
            try (ResultSet tabelaResultado = stm.executeQuery(sqlStr)) {
                return montarDadosConsulta(tabelaResultado, nivelMontarDados);
            }
        }
    }

    /**
     * Responsável por realizar uma consulta de <code>RespostaPergunta</code> através do valor do atributo 
     * <code>Integer codigo</code>. Retorna os objetos com valores iguais ou superiores ao parâmetro fornecido.
     * Faz uso da operação <code>montarDadosConsulta</code> que realiza o trabalho de prerarar o List resultante.
     * @param   controlarAcesso Indica se a aplicação deverá verificar se o usuário possui permissão para esta consulta ou não.
     * @return  List Contendo vários objetos da classe <code>RespostaPerguntaVO</code> resultantes da consulta.
     * @exception Exception Caso haja problemas de conexão ou restrição de acesso.
     */
    public List consultarPorCodigo(Integer valorConsulta, boolean controlarAcesso, int nivelMontarDados) throws Exception {
        consultar(getIdEntidade(), controlarAcesso);
        String sqlStr = "SELECT * FROM RespostaPergunta WHERE codigo >= " + valorConsulta.intValue() + " ORDER BY codigo";
        try (Statement stm = con.createStatement()) {
            try (ResultSet tabelaResultado = stm.executeQuery(sqlStr)) {
                return (montarDadosConsulta(tabelaResultado, nivelMontarDados));
            }
        }
    }

    /**
     * Responsável por montar os dados de vários objetos, resultantes de uma consulta ao banco de dados (<code>ResultSet</code>).
     * Faz uso da operação <code>montarDados</code> que realiza o trabalho para um objeto por vez.
     * @return  List Contendo vários objetos da classe <code>RespostaPerguntaVO</code> resultantes da consulta.
     */
    public static List montarDadosConsulta(ResultSet tabelaResultado, int nivelMontarDados) throws Exception {
        List vetResultado = new ArrayList();
        while (tabelaResultado.next()) {
            RespostaPerguntaVO obj = new RespostaPerguntaVO();
            obj = montarDados(tabelaResultado, nivelMontarDados);
            vetResultado.add(obj);
        }
        return vetResultado;
    }

    /**
     * Responsável por montar os dados resultantes de uma consulta ao banco de dados (<code>ResultSet</code>)
     * em um objeto da classe <code>RespostaPerguntaVO</code>.
     * @return  O objeto da classe <code>RespostaPerguntaVO</code> com os dados devidamente montados.
     */
    public static RespostaPerguntaVO montarDados(ResultSet dadosSQL, int nivelMontarDados) throws Exception {
        RespostaPerguntaVO obj = new RespostaPerguntaVO();
        obj.setCodigo(new Integer(dadosSQL.getInt("codigo")));
        obj.setPergunta(new Integer(dadosSQL.getInt("pergunta")));
        obj.setDescricaoRespota(dadosSQL.getString("descricaoRespota"));
        obj.setNrQuestao(dadosSQL.getInt("nrQuestao"));
        obj.setNovoObj(new Boolean(false));
        if (nivelMontarDados == Uteis.NIVELMONTARDADOS_DADOSBASICOS) {
            return obj;
        }

        return obj;
    }

    /**
     * Operação responsável por excluir todos os objetos da <code>RespostaPerguntaVO</code> no BD.
     * Faz uso da operação <code>excluir</code> disponível na classe <code>RespostaPergunta</code>.
     * @param <code>pergunta</code> campo chave para exclusão dos objetos no BD.
     * @exception Exception  Erro de conexão com o BD ou restrição de acesso a esta operação.
     */
    public void excluirRespostaPerguntas(Integer pergunta) throws Exception {
        excluir(getIdEntidade());
        String sql = "DELETE FROM RespostaPergunta WHERE (pergunta = ?)";
        try (PreparedStatement sqlExcluir = con.prepareStatement(sql)) {
            sqlExcluir.setInt(1, pergunta.intValue());
            sqlExcluir.execute();
        }
    }
    
    public void excluirRespostaPerguntasEdicao(final Integer pergunta, final String codigosAtuais) throws Exception {
        excluir(getIdEntidade());
        String sql = "DELETE FROM RespostaPergunta WHERE (pergunta = ?)";
        if(!UteisValidacao.emptyString(codigosAtuais)){        
                sql += " and codigo not in ("+ codigosAtuais + ")";
        }
        try (PreparedStatement sqlExcluir = con.prepareStatement(sql)) {
            sqlExcluir.setInt(1, pergunta.intValue());
            sqlExcluir.execute();
        }
    }

    /**
     * Operação responsável por alterar todos os objetos da <code>RespostaPerguntaVO</code> contidos em um Hashtable no BD.
     * Faz uso da operação <code>excluirRespostaPerguntas</code> e <code>incluirRespostaPerguntas</code> disponíveis na classe <code>RespostaPergunta</code>.
     * @param objetos  List com os objetos a serem alterados ou incluídos no BD.
     * @exception Exception  Erro de conexão com o BD ou restrição de acesso a esta operação.
     */
    public void alterarRespostaPerguntas(Integer pergunta, List<RespostaPerguntaVO> objetos) throws Exception {
        String codigoRespostas = "";
        for (RespostaPerguntaVO resposta : objetos){
            if(UteisValidacao.emptyNumber(resposta.getCodigo())){
                resposta.setPergunta(pergunta);
                incluir(resposta);
            } else {
                alterar(resposta);
            }
            codigoRespostas += ","+resposta.getCodigo();
        }
        
        excluirRespostaPerguntasEdicao(pergunta, codigoRespostas.replaceFirst(",", ""));
    }

    /**
     * Operação responsável por incluir objetos da <code>RespostaPerguntaVO</code> no BD.
     * Garantindo o relacionamento com a entidade principal <code>basico.Pergunta</code> através do atributo de vínculo.
     * @param objetos List contendo os objetos a serem gravados no BD da classe.
     * @exception Exception  Erro de conexão com o BD ou restrição de acesso a esta operação.
     */
    public void incluirRespostaPerguntas(Integer perguntaPrm, List objetos) throws Exception {
        Iterator e = objetos.iterator();
        while (e.hasNext()) {
            RespostaPerguntaVO obj = (RespostaPerguntaVO) e.next();
            obj.setPergunta(perguntaPrm);
            incluir(obj);
        }
    }

    public void alterarRespostasPerguntas(Integer perguntaPrm, List objetos) throws Exception {
        Iterator e = objetos.iterator();
        while (e.hasNext()) {
            RespostaPerguntaVO obj = (RespostaPerguntaVO) e.next();
            obj.setPergunta(perguntaPrm);
            alterar(obj);
        }
    }

    /**
     * Operação responsável por consultar todos os <code>RespostaPerguntaVO</code> relacionados a um objeto da classe <code>basico.Pergunta</code>.
     * @param pergunta  Atributo de <code>basico.Pergunta</code> a ser utilizado para localizar os objetos da classe <code>RespostaPerguntaVO</code>.
     * @return List  Contendo todos os objetos da classe <code>RespostaPerguntaVO</code> resultantes da consulta.
     * @exception Exception  Erro de conexão com o BD ou restrição de acesso a esta operação.
     */
    public List consultarRespostaPerguntas(Integer pergunta, int nivelMontarDados) throws Exception {
        consultar(getIdEntidade());
        List objetos = new ArrayList();
        String sql = "SELECT * FROM RespostaPergunta WHERE pergunta = ? order by nrQuestao, (CASE when length(descricaorespota) = 1 THEN '0' || descricaorespota ELSE descricaorespota END)";
        try (PreparedStatement sqlConsulta = con.prepareStatement(sql)) {
            sqlConsulta.setInt(1, pergunta);
            try (ResultSet resultado = sqlConsulta.executeQuery()) {
                while (resultado.next()) {
                    RespostaPerguntaVO novoObj = RespostaPergunta.montarDados(resultado, nivelMontarDados);
                    objetos.add(novoObj);
                }
            }
        }
        return objetos;
    }

    /**
     * Operação responsável por localizar um objeto da classe <code>RespostaPerguntaVO</code>
     * através de sua chave primária. 
     * @exception Exception Caso haja problemas de conexão ou localização do objeto procurado.
     */
    public RespostaPerguntaVO consultarPorChavePrimaria(Integer codigoPrm, int nivelMontarDados) throws Exception {
        consultar(getIdEntidade(), false);
        String sql = "SELECT * FROM RespostaPergunta WHERE codigo = ?";
        try (PreparedStatement sqlConsultar = con.prepareStatement(sql)) {
            sqlConsultar.setInt(1, codigoPrm.intValue());
            try (ResultSet tabelaResultado = sqlConsultar.executeQuery()) {
                if (!tabelaResultado.next()) {
                    throw new ConsistirException("Dados Não Encontrados ( RespostaPergunta ).");
                }
                return (montarDados(tabelaResultado, nivelMontarDados));
            }
        }
    }    
}

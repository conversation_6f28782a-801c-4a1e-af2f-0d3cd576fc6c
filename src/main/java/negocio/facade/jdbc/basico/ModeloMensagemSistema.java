package negocio.facade.jdbc.basico;

import negocio.comuns.basico.ConfiguracaoSistemaVO;
import negocio.comuns.basico.ModeloMensagemSistemaVO;
import negocio.comuns.basico.enumerador.IdentificadorMensagemSistema;
import negocio.facade.jdbc.arquitetura.SuperEntidade;
import negocio.interfaces.basico.ModeloMensagemSistemaInterfaceFacade;

import javax.servlet.http.HttpSession;
import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;

/**
 * Created by johnys on 13/01/2017.
 */
public class ModeloMensagemSistema extends SuperEntidade implements ModeloMensagemSistemaInterfaceFacade{


    /**
     * SQL de inserção de um {@link ModeloMensagemSistemaVO}
     */
    private static final String SQL_INCLUIR = "INSERT INTO modelomensagemsistema(titulo, mensagem, identificador) VALUES(?, ?, ?);";

    public ModeloMensagemSistema() throws Exception {}

    public ModeloMensagemSistema(HttpSession session) throws Exception {
        super(session);
    }

    public ModeloMensagemSistema(Connection conexao) throws Exception {
        super(conexao);
    }

    @Override
    public void incluir(ModeloMensagemSistemaVO obj) throws Exception {
        try {
            con.setAutoCommit(false);
            ConfiguracaoSistemaVO.validarDados(obj);
            incluir(getIdEntidade());
            PreparedStatement sqlInserir = prepararIncluir(obj);
            sqlInserir.execute();
            obj.setCodigo(obterValorChavePrimariaCodigo());
            obj.setNovoObj(false);
            con.commit();
        } catch (Exception e) {
            con.rollback();
            con.setAutoCommit(true);
            throw e;
        } finally {
            con.setAutoCommit(true);
        }
    }

    /**
     * Cria o {@link PreparedStatement} da inclusão de uma nova {@link ModeloMensagemSistemaVO}
     * @param obj {@link ModeloMensagemSistemaVO} que será incluida
     * @return
     * @throws Exception
     */
    private PreparedStatement prepararIncluir(ModeloMensagemSistemaVO obj) throws Exception{
        PreparedStatement ps = con.prepareStatement(SQL_INCLUIR);
        ps.setString(1, obj.getTitulo());
        ps.setString(2, obj.getMensagem());
        ps.setString(3, obj.getIdentificador().name());
        return ps;
    }

    @Override
    public ModeloMensagemSistemaVO consultarPorIdentificador(IdentificadorMensagemSistema identificador) throws Exception {
        String sql = "SELECT * FROM modelomensagemsistema WHERE identificador = ?";
        PreparedStatement ps = con.prepareStatement(sql);
        ps.setString(1, identificador.name());
        ResultSet rs = ps.executeQuery();
        return rs.next() ? montarDadosBasico(rs) : new ModeloMensagemSistemaVO();
    }

    /**
     * Cria um objeto {@link ModeloMensagemSistemaVO} a partir dos dados do {@link ResultSet}. Esperando as colunas: codigo, titulo, mensagem e identificador.
     * @param dadosSQL {@link ResultSet} utilizado para obter os dados.
     * @return
     * @throws Exception
     */
    public static ModeloMensagemSistemaVO montarDadosBasico(ResultSet dadosSQL) throws Exception {
        ModeloMensagemSistemaVO modelo = new ModeloMensagemSistemaVO();
        modelo.setCodigo(dadosSQL.getInt("codigo"));
        modelo.setTitulo(dadosSQL.getString("titulo"));
        modelo.setMensagem(dadosSQL.getString("mensagem"));
        modelo.setIdentificador(IdentificadorMensagemSistema.valueOf(dadosSQL.getString("identificador")));
        return modelo;
    }
}

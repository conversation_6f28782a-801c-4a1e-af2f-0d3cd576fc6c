/*
 * To change this template, choose Too<PERSON> | Templates
 * and open the template in the editor.
 */
package negocio.facade.jdbc.basico;

import negocio.comuns.CampanhaDuracaoVO;
import negocio.comuns.basico.EmpresaVO;
import negocio.comuns.basico.ItemCampanhaVO;
import negocio.comuns.basico.enumerador.TipoItemCampanhaEnum;
import negocio.comuns.utilitarias.Calendario;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisValidacao;
import negocio.facade.jdbc.arquitetura.SuperEntidade;
import negocio.interfaces.basico.CampanhaDuracaoInterfaceFacade;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.sql.Statement;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 *
 */
public class CampanhaDuracao extends SuperEntidade implements CampanhaDuracaoInterfaceFacade{

    public CampanhaDuracao() throws Exception {
        super();
    }

    public CampanhaDuracao(Connection conexao) throws Exception {
        super(conexao);
    }

    @Override
    public void incluir(CampanhaDuracaoVO campanhaInclusao) throws Exception {
        try {
            con.setAutoCommit(false);
            incluir(getIdEntidade());
            StringBuilder sql = new StringBuilder();
            sql.append(" INSERT INTO campanhaduracao(nome,descricao,datainicial,datafinal,multiplicador,empresa) \n");
            sql.append(" VALUES(?, ?, ?, ?, ?, ?) \n");
            PreparedStatement sqlInserir = con.prepareStatement(sql.toString());
            sqlInserir.setString(1, campanhaInclusao.getNome());
            sqlInserir.setString(2, campanhaInclusao.getDescricao());
            sqlInserir.setDate(3, Uteis.getDataJDBC(campanhaInclusao.getDataInicial()));
            sqlInserir.setDate(4, Uteis.getDataJDBC(campanhaInclusao.getDataFinal()));
            sqlInserir.setInt(5, campanhaInclusao.getMultiplicador());
            List<EmpresaVO> empresas = new ArrayList<>();
            if (UteisValidacao.emptyNumber(campanhaInclusao.getEmpresa().getCodigo())) {
                empresas = getFacade().getEmpresa().consultarEmpresas(negocio.comuns.utilitarias.Uteis.NIVELMONTARDADOS_MINIMOS);

            }else
                empresas.add(campanhaInclusao.getEmpresa());

            for (EmpresaVO empresa : empresas) {
                if (empresa.isAtiva()) {
                    sqlInserir.setInt(6, empresa.getCodigo());
                    sqlInserir.execute();
                }
            }
            campanhaInclusao.setCodigo(obterValorChavePrimariaCodigo());

            for (ItemCampanhaVO itemCampanhaVO : campanhaInclusao.getListaItem()) {
                itemCampanhaVO.setCampanha(campanhaInclusao);
                for (EmpresaVO empresa : empresas) {
                    itemCampanhaVO.setEmpresa(empresa);
                    getFacade().getItemCampanha().incluir(itemCampanhaVO);
                }
            }
            con.commit();
        } catch (Exception e) {
            con.rollback();
            throw e;
        } finally {
            con.setAutoCommit(true);
        }
    }

    @Override
    public void alterar(CampanhaDuracaoVO campanhaDuracaoVO) throws Exception {
        try {
            con.setAutoCommit(false);
            alterar(getIdEntidade());
            alterarSemCommit(campanhaDuracaoVO);
            con.commit();
        } catch (Exception e) {
            con.rollback();
            throw e;
        }finally {
            con.setAutoCommit(true);
        }
    }

    @Override
    public void excluir(CampanhaDuracaoVO obj) throws Exception {
        try {
            excluir(getIdEntidade());
            con.setAutoCommit(false);
            if(obj.getListaItem().size()<=0){
                obj.setListaItem(getFacade().getItemCampanha().listaItemCampanha(obj, negocio.comuns.utilitarias.Uteis.NIVELMONTARDADOS_DADOSBASICOS));
            }

            for (ItemCampanhaVO itemCampanhaVO : obj.getListaItem()) {
                new ItemCampanha(con).excluirSemCommit(itemCampanhaVO);
            }
            StringBuilder sql = new StringBuilder();
            sql.append("DELETE FROM campanhaduracao WHERE codigo = ? AND empresa = ?");
            PreparedStatement sqlExcluir = con.prepareStatement(sql.toString());
            sqlExcluir.setInt(1, obj.getCodigo());
            sqlExcluir.setInt(2, obj.getEmpresa().getCodigo());
            sqlExcluir.execute();
            con.commit();
        } catch (Exception e) {
            con.rollback();
            throw e;
        }finally {
            con.setAutoCommit(true);
        }
    }

    @Override
    public List<CampanhaDuracaoVO> consultarTodos() throws Exception {
        StringBuilder sql = new StringBuilder();
        sql.append("SELECT * from campanhaduracao order by datafinal desc ");
        Statement stm = con.createStatement();
        ResultSet tabelaResultado = stm.executeQuery(sql.toString());
        return montarDadosConsulta(tabelaResultado, negocio.comuns.utilitarias.Uteis.NIVELMONTARDADOS_DADOSBASICOS, con);
    }

    @Override
    public void alterarSemCommit(CampanhaDuracaoVO obj) throws Exception {
        alterar(getIdEntidade());
        StringBuilder sql = new StringBuilder();
        sql.append("UPDATE campanhaduracao set nome = ?, descricao = ?, datainicial = ?, datafinal = ?, multiplicador = ? WHERE codigo = ? AND empresa = ?");
        PreparedStatement sqlAlterar = con.prepareStatement(sql.toString());
        sqlAlterar.setString(1, obj.getNome());
        sqlAlterar.setString(2, obj.getDescricao());
        sqlAlterar.setDate(3, Uteis.getDataJDBC(obj.getDataInicial()));
        sqlAlterar.setDate(4, Uteis.getDataJDBC(obj.getDataFinal()));
        sqlAlterar.setInt(5, obj.getMultiplicador());
        sqlAlterar.setInt(6, obj.getCodigo());
        sqlAlterar.setInt(7, obj.getEmpresa().getCodigo());
        sqlAlterar.execute();
        for (ItemCampanhaVO itemCampanhaVO : obj.getListaItem()) {
            itemCampanhaVO.setEmpresa(obj.getEmpresa());
            getFacade().getItemCampanha().alterarSemCommit(itemCampanhaVO);
        }
    }
    
    public List<CampanhaDuracaoVO> montarDadosConsulta(ResultSet tabelaResultado, int nivelMontarDados, Connection con) throws SQLException, Exception{
        List<CampanhaDuracaoVO> listaResultado = new ArrayList<CampanhaDuracaoVO>();
        while (tabelaResultado.next()) {
            listaResultado.add(montarDados(tabelaResultado, nivelMontarDados, con));
        }
        return listaResultado;
    }

    public CampanhaDuracaoVO montarDados(ResultSet tabelaResultado, int nivelMontarDados, Connection con) throws SQLException, Exception {
        CampanhaDuracaoVO campanha = new CampanhaDuracaoVO();
        campanha.setCodigo(tabelaResultado.getInt("codigo"));
        campanha.setNome(tabelaResultado.getString("nome"));
        campanha.setDescricao(tabelaResultado.getString("descricao"));
        campanha.setDataInicial(tabelaResultado.getDate("datainicial"));
        campanha.setDataFinal(tabelaResultado.getDate("datafinal"));
        campanha.setMultiplicador(tabelaResultado.getInt("multiplicador"));
        campanha.setEmpresa(new EmpresaVO());
        campanha.getEmpresa().setCodigo(tabelaResultado.getInt("empresa"));
        campanha.setEmpresa(new Empresa(con).consultarPorCodigo(tabelaResultado.getInt("empresa"), Uteis.NIVELMONTARDADOS_DADOSBASICOS));
        if (nivelMontarDados == Uteis.NIVELMONTARDADOS_TODOS) {
            campanha.setListaItem(new ItemCampanha(con).listaItemCampanha(campanha, Uteis.NIVELMONTARDADOS_DADOSBASICOS));
        }
        return campanha;
    }

    @Override
    public Integer multiplicadorPorCampanha(ItemCampanhaVO itemCampanha) throws Exception {
        ResultSet rs = con.createStatement().executeQuery("select pontos from itemcampanha where chaveestrangeira="+itemCampanha.getChaveestrangeira()+" and tipoitem="+itemCampanha.getTipoItemCampanha().getCodigo()+" and empresa="+itemCampanha.getEmpresa().getCodigo());
        if(rs.next())
            return rs.getInt("pontos");
        return 0;
    }

    @Override
    public CampanhaDuracaoVO consultarPorChavePrimaria(Integer codigo, final EmpresaVO empresa) throws Exception {
        if (UteisValidacao.emptyNumber(codigo)) {
            return new CampanhaDuracaoVO();
        }
        StringBuilder sql = new StringBuilder();
        sql.append("SELECT * from campanhaduracao\n");
        sql.append("WHERE codigo = ").append(codigo).append("\n");
        if (empresa != null) {
            sql.append("AND empresa=").append(empresa.getCodigo()).append("\n");
        }
        try (Statement stm = con.createStatement();
             ResultSet retornoPesquisa = stm.executeQuery(sql.toString())) {

            if (!retornoPesquisa.next()) {
                return new CampanhaDuracaoVO();
            }
            return montarDados(retornoPesquisa, Uteis.NIVELMONTARDADOS_TODOS, con);
        }
    }

    @Override
    public CampanhaDuracaoVO campanhaVigenteMultiplicador(Date dataRegistro, TipoItemCampanhaEnum tipoCategoria, Integer empresa) throws Exception {
        StringBuilder sql = new StringBuilder();
        sql.append("select c.codigo, c.nome, max(i.pontos ) total" +
                " from campanhaduracao c " +
                " left join itemcampanha i on c.codigo = i.campanha and c.empresa=i.empresa \n" +
                " where i.pontos>0 AND i.tipoitem =" + tipoCategoria.getCodigo()+
                " AND c.empresa=" + empresa +
                " AND '"+ Uteis.getSQLData(dataRegistro)+"' between c.datainicial AND c.datafinal group by c.codigo, c.descricao order by total desc limit 1 ");
        Statement stm = con.createStatement();
        ResultSet tabelaResultado = stm.executeQuery(sql.toString());
        if (tabelaResultado.next()) {
            return montarCampanhaMaior(tabelaResultado);
        }
        return new CampanhaDuracaoVO();
    }

    private CampanhaDuracaoVO montarCampanhaMaior(ResultSet tabelaResultado) throws Exception{
        CampanhaDuracaoVO maiorCampanha = new CampanhaDuracaoVO();
        maiorCampanha.setCodigo(tabelaResultado.getInt("codigo"));
        maiorCampanha.setNome(tabelaResultado.getString("nome"));
        maiorCampanha.setMultiplicador(tabelaResultado.getInt("total"));
        return maiorCampanha;
    }



    @Override
    public List<CampanhaDuracaoVO> consultarTotalizadorCampanhasAtivas(Date dataInicial, Date dataFinal, EmpresaVO empresa) throws Exception {
        StringBuilder sql = new StringBuilder();
        sql.append(" select c.codigo ,c.empresa, c.descricao, c.nome, c.datainicial, c.datafinal," +
                " SUM((select  coalesce(sum(pontos),0) from historicopontos h " +
                " inner join cliente cli on h.cliente=cli.codigo and cli.empresa= " +empresa.getCodigo()+
                " where tipodepontos = i.tipoitem and h.dataconfirmacao between '"+Calendario.getDataComHoraZerada(dataInicial)+"' and '"+Calendario.getDataComUltimaHora(dataFinal)+"' and h.codigocampanha=c.codigo )) multiplicador " +
                " from campanhaduracao c" +
                " left join itemcampanha i on i.campanha = c.codigo and c.empresa = i.empresa and i.pontos>0 \n" +
                " where " +
                " (('"+Uteis.getDataJDBC(dataInicial)+"' BETWEEN c.datainicial AND c.datafinal)" +
                " or ('"+Uteis.getDataJDBC(dataFinal)+"' BETWEEN c.datainicial AND c.datafinal)\n" +
                " or (c.datainicial > '"+Uteis.getDataJDBC(dataInicial)+"' and c.datafinal < '"+Uteis.getDataJDBC(dataFinal)+"'))\n" +
                (empresa.getCodigo()!=0?" and c.empresa = "+empresa.getCodigo():"")+ " group by 1,2,3,4,5,6 ");
        Statement stm = con.createStatement();
        ResultSet tabelaResultado = stm.executeQuery(sql.toString());
        return montarDadosConsulta(tabelaResultado, negocio.comuns.utilitarias.Uteis.NIVELMONTARDADOS_DADOSBASICOS, con);

    }
}

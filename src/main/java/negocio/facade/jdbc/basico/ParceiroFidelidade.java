package negocio.facade.jdbc.basico;

import negocio.comuns.basico.EmpresaVO;
import negocio.comuns.basico.ParceiroFidelidadeVO;
import negocio.comuns.basico.ProdutoParceiroFidelidadeVO;
import negocio.comuns.basico.TabelaParceiroFidelidadeVO;
import negocio.comuns.basico.enumerador.TipoParceiroEnum;
import negocio.comuns.utilitarias.ConsistirException;
import negocio.comuns.utilitarias.Uteis;
import negocio.facade.jdbc.arquitetura.SuperEntidade;
import negocio.interfaces.basico.ParceiroFidelidadeInterfaceFacade;
import negocio.interfaces.basico.TabelaParceiroFidelidadeInterfaceFacade;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.Statement;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 *
 * <AUTHOR>
 */
public class ParceiroFidelidade extends SuperEntidade implements ParceiroFidelidadeInterfaceFacade {

    public ParceiroFidelidade() throws Exception {
        super();
        setIdEntidade("Empresa");
    }

    public ParceiroFidelidade(Connection con) throws Exception {
        super(con);
        setIdEntidade("Empresa");
    }

    @Override
    public ParceiroFidelidadeVO novo() throws Exception {
        incluir(getIdEntidade());
        return new ParceiroFidelidadeVO();
    }

    @Override
    public void incluir(ParceiroFidelidadeVO obj) throws Exception {
        try {
            ParceiroFidelidadeVO.validarDados(obj);
            incluir(getIdEntidade());
            String sql = "INSERT INTO ParceiroFidelidade(empresa, tipoParceiro, validarCliente, clientID, clientSecret, clientIDRedemption, clientSecretRedemption, " +
                    "codigoLoja, codigoMaquina, codigoOferta, codigoResgate, tags, cpf, token, dataExpiracaoToken, ambienteProducao, parcelaVencidaGeraPonto) VALUES (?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)";
            int i = 0;
            try (PreparedStatement ps = con.prepareStatement(sql)) {
                ps.setInt(++i, obj.getEmpresa().getCodigo());
                ps.setInt(++i, obj.getTipoParceiro().getId());
                ps.setBoolean(++i, obj.isValidarCliente());
                ps.setString(++i, obj.getClientID());
                ps.setString(++i, obj.getClientSecret());
                ps.setString(++i, obj.getClientIDRedemption());
                ps.setString(++i, obj.getClientSecretRedemption());
                ps.setString(++i, obj.getCodigoLoja());
                ps.setString(++i, obj.getCodigoMaquina());
                ps.setString(++i, obj.getCodigoOferta());
                ps.setString(++i, obj.getCodigoResgate());
                ps.setString(++i, obj.getTags());
                ps.setString(++i, obj.getCpf());
                ps.setString(++i, obj.getToken());
                ps.setTimestamp(++i, Uteis.getDataJDBCTimestamp(obj.getDataExpiracaoToken()));
                ps.setBoolean(++i, obj.isAmbienteProducao());
                ps.setBoolean(++i, obj.isParcelaVencidaGeraPonto());
                ps.execute();
            }
            obj.setCodigo(obterValorChavePrimariaCodigo());
            obj.setNovoObj(false);
            persistirItens(obj);
            persistirProdutos(obj);
        } catch (Exception e) {
            obj.setCodigo(0);
            obj.setNovoObj(true);
            throw e;
        }
    }

    private void persistirItens(ParceiroFidelidadeVO obj) throws Exception {
        TabelaParceiroFidelidadeInterfaceFacade itemDao = new TabelaParceiroFidelidade(con);
        if (!obj.getItens().isEmpty()) {
            for (TabelaParceiroFidelidadeVO item : obj.getItens()) {
                item.setParceiro(obj);
                if (item.getCodigo() == 0) {
                    itemDao.incluir(item);
                } else {
                    itemDao.alterar(item);
                }
            }
        }
        itemDao = null;
    }

    private void persistirProdutos(ParceiroFidelidadeVO obj) throws Exception {
        ProdutoParceiroFidelidade produtoDao = new ProdutoParceiroFidelidade(con);
        if (!obj.getItens().isEmpty()) {
            for (ProdutoParceiroFidelidadeVO produto : obj.getProdutos()) {
                produto.setParceiroFidelidade(obj);
                if (produto.getCodigo() == 0) {
                    produtoDao.incluir(produto);
                } else {
                    produtoDao.alterar(produto);
                }
            }
        }
        produtoDao = null;
    }

    @Override
    public void alterar(ParceiroFidelidadeVO obj) throws Exception {
        try {
            ParceiroFidelidadeVO.validarDados(obj);
            alterar(getIdEntidade());
            String sql = "UPDATE ParceiroFidelidade set empresa = ?, tipoParceiro = ?, validarCliente = ?, clientID = ?, clientSecret = ?, clientIDRedemption = ?, clientSecretRedemption = ?, " +
                    "codigoLoja = ?, codigoMaquina = ?, codigoOferta = ?, codigoResgate = ?, tags = ?, cpf = ?, token = ?, dataExpiracaoToken = ?, ambienteProducao = ?, parcelaVencidaGeraPonto = ? WHERE codigo = ?";

            int i = 0;
            try (PreparedStatement ps = con.prepareStatement(sql)) {
                ps.setInt(++i, obj.getEmpresa().getCodigo());
                ps.setInt(++i, obj.getTipoParceiro().getId());
                ps.setBoolean(++i, obj.isValidarCliente());
                ps.setString(++i, obj.getClientID());
                ps.setString(++i, obj.getClientSecret());
                ps.setString(++i, obj.getClientIDRedemption());
                ps.setString(++i, obj.getClientSecretRedemption());
                ps.setString(++i, obj.getCodigoLoja());
                ps.setString(++i, obj.getCodigoMaquina());
                ps.setString(++i, obj.getCodigoOferta());
                ps.setString(++i, obj.getCodigoResgate());
                ps.setString(++i, obj.getTags());
                ps.setString(++i, obj.getCpf());
                ps.setString(++i, obj.getToken());
                ps.setTimestamp(++i, Uteis.getDataJDBCTimestamp(obj.getDataExpiracaoToken()));
                ps.setBoolean(++i, obj.isAmbienteProducao());
                ps.setBoolean(++i, obj.isParcelaVencidaGeraPonto());
                ps.setInt(++i, obj.getCodigo());
                ps.execute();
            }
            persistirItens(obj);
            persistirProdutos(obj);
        } catch (Exception e) {
            throw e;
        }
    }

    @Override
    public void excluir(ParceiroFidelidadeVO obj) throws Exception {
        try {
            excluir(getIdEntidade());
            String sql = "DELETE FROM ParceiroFidelidade WHERE codigo = ?";
            try (PreparedStatement ps = con.prepareStatement(sql)) {
                int i = 0;
                ps.setInt(++i, obj.getCodigo());
                ps.execute();
            }
        } catch (Exception e) {
            throw e;
        }
    }

    @Override
    public List consultarPorEmpresa(Integer codigoEmpresa, boolean controlarAcesso, int nivelMontarDados) throws Exception {
        consultar(getIdEntidade(), controlarAcesso);
        String sqlStr = String.format("SELECT * FROM ParceiroFidelidade WHERE empresa = %s ORDER BY codigo",
                codigoEmpresa);
        try (Statement stm = con.createStatement()) {
            try (ResultSet tabelaResultado = stm.executeQuery(sqlStr)) {
                return montarDadosConsulta(tabelaResultado, nivelMontarDados);
            }
        }
    }

    public List montarDadosConsulta(ResultSet tabelaResultado, int nivelMontarDados) throws Exception {
        List vetResultado = new ArrayList();
        while (tabelaResultado.next()) {
            ParceiroFidelidadeVO obj = montarDados(tabelaResultado, nivelMontarDados);
            vetResultado.add(obj);
        }
        return vetResultado;
    }

    public ParceiroFidelidadeVO montarDados(ResultSet dadosSQL, int nivelMontarDados) throws Exception {
        ParceiroFidelidadeVO obj = new ParceiroFidelidadeVO();
        obj.setCodigo(dadosSQL.getInt("codigo"));

        Empresa empresaDao = new Empresa(con);
        obj.setEmpresa(empresaDao.consultarPorChavePrimaria(dadosSQL.getInt("empresa"), Uteis.NIVELMONTARDADOS_MINIMOS));
        empresaDao = null;

        obj.setTipoParceiro(TipoParceiroEnum.valueOf(dadosSQL.getInt("tipoParceiro")));
        obj.setValidarCliente(dadosSQL.getBoolean("validarCliente"));
        obj.setClientID(dadosSQL.getString("clientID"));
        obj.setClientSecret(dadosSQL.getString("clientSecret"));
        obj.setClientIDRedemption(dadosSQL.getString("clientIDRedemption"));
        obj.setClientSecretRedemption(dadosSQL.getString("clientSecretRedemption"));
        obj.setCodigoLoja(dadosSQL.getString("codigoLoja"));
        obj.setCodigoMaquina(dadosSQL.getString("codigoMaquina"));
        obj.setCodigoOferta(dadosSQL.getString("codigoOferta"));
        obj.setCodigoResgate(dadosSQL.getString("codigoResgate"));
        obj.setTags(dadosSQL.getString("tags"));
        obj.setCpf(dadosSQL.getString("cpf"));
        obj.setToken(dadosSQL.getString("token"));
        obj.setDataExpiracaoToken(dadosSQL.getTimestamp("dataExpiracaoToken"));
        obj.setAmbienteProducao(dadosSQL.getBoolean("ambienteProducao"));
        obj.setParcelaVencidaGeraPonto(dadosSQL.getBoolean("parcelaVencidaGeraPonto"));

        if (nivelMontarDados == Uteis.NIVELMONTARDADOS_TODOS) {
            TabelaParceiroFidelidadeInterfaceFacade tabelaDao = new TabelaParceiroFidelidade(con);
            obj.setItens(tabelaDao.consultarPorParceiro(obj.getCodigo(), false, Uteis.NIVELMONTARDADOS_TODOS));
            tabelaDao = null;

            ProdutoParceiroFidelidade produtoDao = new ProdutoParceiroFidelidade(con);
            obj.setProdutos(produtoDao.consultarPorParceiro(obj.getCodigo(), Uteis.NIVELMONTARDADOS_TODOS));
            produtoDao = null;
        }

        obj.setNovoObj(false);
        return obj;
    }

    @Override
    public ParceiroFidelidadeVO consultarPorChavePrimaria(Integer codigoPrm, int nivelMontarDados) throws Exception {
        String sql = "SELECT * FROM ParceiroFidelidade WHERE codigo = ?";
        try (PreparedStatement ps = con.prepareStatement(sql)) {
            int i = 0;
            ps.setInt(++i, codigoPrm);
            try (ResultSet tabelaResultado = ps.executeQuery()) {
                if (!tabelaResultado.next()) {
                    throw new ConsistirException("Dados Não Encontrados ( ParceiroFidelidade ).");
                }
                return montarDados(tabelaResultado, nivelMontarDados);
            }
        }
    }

    @Override
    public ParceiroFidelidadeVO consultarPorEmpresaETipo(final Integer codigoEmpresa,
            final TipoParceiroEnum tipo, int nivelMontarDados) throws Exception {
        String sql = "SELECT * FROM ParceiroFidelidade WHERE empresa = ? and tipoParceiro = ?";
        try (PreparedStatement ps = con.prepareStatement(sql)) {
            int i = 0;
            ps.setInt(++i, codigoEmpresa);
            ps.setInt(++i, tipo.getId());
            try (ResultSet tabelaResultado = ps.executeQuery()) {
                if (!tabelaResultado.next()) {
                    return new ParceiroFidelidadeVO();
                }
                return montarDados(tabelaResultado, nivelMontarDados);
            }
        }
    }

    public ParceiroFidelidadeVO consultarOuIncluirSeNaoExiste(final EmpresaVO empresa, final TipoParceiroEnum tipoParceiroEnum) throws Exception {
        ParceiroFidelidadeVO parceiro = consultarPorEmpresaETipo(empresa.getCodigo(), tipoParceiroEnum, Uteis.NIVELMONTARDADOS_TODOS);
        if (parceiro == null) {
            parceiro = new ParceiroFidelidadeVO(empresa, tipoParceiroEnum);
            incluir(parceiro);
        }
        return parceiro;
    }

    public ParceiroFidelidadeVO consultarPorTabelaParceiroFidelidade(Integer tabelaParceiroFidelidade, int nivelMontarDados) throws Exception {
        String sql = "select \n" +
                "p.*\n" +
                "from parceirofidelidade p\n" +
                "inner join tabelaparceirofidelidade t on t.parceirofidelidade = p.codigo\n" +
                "where t.codigo = ?";
        try (PreparedStatement ps = con.prepareStatement(sql)) {
            int i = 0;
            ps.setInt(++i, tabelaParceiroFidelidade);
            try (ResultSet tabelaResultado = ps.executeQuery()) {
                if (!tabelaResultado.next()) {
                    return new ParceiroFidelidadeVO();
                }
                return montarDados(tabelaResultado, nivelMontarDados);
            }
        }
    }

    public void atualizarToken(String token, Date dataExpiracaoToken, Integer codigo) throws Exception {
        String sql = "UPDATE parceiroFidelidade set token = ?, dataExpiracaoToken = ?  WHERE codigo = ?";
        try (PreparedStatement ps = con.prepareStatement(sql)) {
            ps.setString(1, token);
            ps.setTimestamp(2, Uteis.getDataJDBCTimestamp(dataExpiracaoToken));
            ps.setInt(3, codigo);
            ps.executeUpdate();
        }
    }
}

package negocio.facade.jdbc.basico;

import negocio.comuns.basico.PerguntaVO;
import java.util.Iterator;
import negocio.comuns.basico.QuestionarioPerguntaVO;
import negocio.facade.jdbc.arquitetura.*;

import java.sql.Connection;
import java.sql.ResultSet;
import java.sql.PreparedStatement;
import java.sql.Statement;
import java.util.List;
import java.util.ArrayList;
import negocio.comuns.utilitarias.*;

/**
 * Classe de persistência que encapsula todas as operações de manipulação dos dados da classe <code>QuestionarioPerguntaVO</code>.
 * Responsável por implementar operações como incluir, alterar, excluir e consultar pertinentes a classe <code>QuestionarioPerguntaVO</code>.
 * Encapsula toda a interação com o banco de dados.
 * @see QuestionarioPerguntaVO
 * @see SuperEntidade
 * @see Questionario
 */
public class QuestionarioPergunta extends SuperEntidade {    

    public QuestionarioPergunta() throws Exception {
        super();
        setIdEntidade("Questionario");
    }

    public QuestionarioPergunta(Connection con) throws Exception {
        super(con);
        setIdEntidade("Questionario");
   }

	/**
     * Operação responsável por retornar um novo objeto da classe <code>QuestionarioPerguntaVO</code>.
     */
    public QuestionarioPerguntaVO novo() throws Exception {
        incluir(getIdEntidade());
        QuestionarioPerguntaVO obj = new QuestionarioPerguntaVO();
        return obj;
    }

    /**
     * Operação responsável por incluir no banco de dados um objeto da classe <code>QuestionarioPerguntaVO</code>.
     * Primeiramente valida os dados (<code>validarDados</code>) do objeto. Verifica a conexão com o banco de dados e a permissão do usuário
     * para realizar esta operacão na entidade.
     * Isto, através da operação <code>incluir</code> da superclasse.
     * @param obj  Objeto da classe <code>QuestionarioPerguntaVO</code> que será gravado no banco de dados.
     * @exception Exception Caso haja problemas de conexão, restrição de acesso ou validação de dados.
     */
    public void incluir(QuestionarioPerguntaVO obj) throws Exception {
        QuestionarioPerguntaVO.validarDados(obj);
        incluir(getIdEntidade());
        obj.realizarUpperCaseDados();
        String sql = "INSERT INTO QuestionarioPergunta( questionario, pergunta, obrigatoria, nrQuestao) VALUES ( ?, ?, ?, ?)";
        PreparedStatement sqlInserir = con.prepareStatement(sql);
        if (obj.getQuestionario().intValue() != 0) {
            sqlInserir.setInt(1, obj.getQuestionario().intValue());
        } else {
            sqlInserir.setNull(1, 0);
        }
        if (obj.getPergunta().getCodigo().intValue() != 0) {
            sqlInserir.setInt(2, obj.getPergunta().getCodigo().intValue());
        } else {
            sqlInserir.setNull(2, 0);
        }
        sqlInserir.setBoolean(3, obj.getObrigatoria());
        sqlInserir.setInt(4, obj.getNrQuestao());
        sqlInserir.execute();
        obj.setCodigo(obterValorChavePrimariaCodigo());
        obj.setNovoObj(new Boolean(false));
    }

    /**
     * Operação responsável por alterar no BD os dados de um objeto da classe <code>QuestionarioPerguntaVO</code>.
     * Sempre utiliza a chave primária da classe como atributo para localização do registro a ser alterado.
     * Primeiramente valida os dados (<code>validarDados</code>) do objeto. Verifica a conexão com o banco de dados e a permissão do usuário
     * para realizar esta operacão na entidade.
     * Isto, através da operação <code>alterar</code> da superclasse.
     * @param obj    Objeto da classe <code>QuestionarioPerguntaVO</code> que será alterada no banco de dados.
     * @exception Execption Caso haja problemas de conexão, restrição de acesso ou validação de dados.
     */
    public void alterar(QuestionarioPerguntaVO obj) throws Exception {
        QuestionarioPerguntaVO.validarDados(obj);
        alterar(getIdEntidade());
        obj.realizarUpperCaseDados();
        String sql = "UPDATE QuestionarioPergunta set questionario=?, pergunta=?, obrigatoria = ?, nrQuestao = ? WHERE ((codigo = ?))";
        PreparedStatement sqlAlterar = con.prepareStatement(sql);
        if (obj.getQuestionario().intValue() != 0) {
            sqlAlterar.setInt(1, obj.getQuestionario().intValue());
        } else {
            sqlAlterar.setNull(1, 0);
        }
        if (obj.getPergunta().getCodigo().intValue() != 0) {
            sqlAlterar.setInt(2, obj.getPergunta().getCodigo().intValue());
        } else {
            sqlAlterar.setNull(2, 0);
        }
        sqlAlterar.setBoolean(3, obj.getObrigatoria());
        sqlAlterar.setInt(4, obj.getNrQuestao());
        sqlAlterar.setInt(5, obj.getCodigo().intValue());
        sqlAlterar.execute();
    }

    /**
     * Operação responsável por excluir no BD um objeto da classe <code>QuestionarioPerguntaVO</code>.
     * Sempre localiza o registro a ser excluído através da chave primária da entidade.
     * Primeiramente verifica a conexão com o banco de dados e a permissão do usuário
     * para realizar esta operacão na entidade.
     * Isto, através da operação <code>excluir</code> da superclasse.
     * @param obj    Objeto da classe <code>QuestionarioPerguntaVO</code> que será removido no banco de dados.
     * @exception Execption Caso haja problemas de conexão ou restrição de acesso.
     */
    public void excluir(QuestionarioPerguntaVO obj) throws Exception {
        excluir(getIdEntidade());
        String sql = "DELETE FROM QuestionarioPergunta WHERE ((codigo = ?))";
        PreparedStatement sqlExcluir = con.prepareStatement(sql);
        sqlExcluir.setInt(1, obj.getCodigo().intValue());
        sqlExcluir.execute();
    }

    /**
     * Responsável por realizar uma consulta de <code>QuestionarioPergunta</code> através do valor do atributo 
     * <code>descricao</code> da classe <code>Pergunta</code>
     * Faz uso da operação <code>montarDadosConsulta</code> que realiza o trabalho de prerarar o List resultante.
     * @return  List Contendo vários objetos da classe <code>QuestionarioPerguntaVO</code> resultantes da consulta.
     * @exception Execption Caso haja problemas de conexão ou restrição de acesso.
     */
    public List consultarPorDescricaoPergunta(String valorConsulta, int nivelMontarDados) throws Exception {
        consultar(getIdEntidade(), true);
        String sqlStr = "SELECT QuestionarioPergunta.* FROM QuestionarioPergunta, Pergunta WHERE QuestionarioPergunta.pergunta = Pergunta.codigo and upper( Pergunta.descricao ) like('" + valorConsulta.toUpperCase() + "%') ORDER BY Pergunta.descricao";
        try (Statement   stm = con.createStatement()) {
            try (ResultSet tabelaResultado = stm.executeQuery(sqlStr)) {
                return montarDadosConsulta(tabelaResultado, nivelMontarDados, this.con);
            }
        }
    }

    /**
     * Responsável por realizar uma consulta de <code>QuestionarioPergunta</code> através do valor do atributo 
     * <code>descricao</code> da classe <code>Questionario</code>
     * Faz uso da operação <code>montarDadosConsulta</code> que realiza o trabalho de prerarar o List resultante.
     * @return  List Contendo vários objetos da classe <code>QuestionarioPerguntaVO</code> resultantes da consulta.
     * @exception Execption Caso haja problemas de conexão ou restrição de acesso.
     */
    public List consultarPorDescricaoQuestionario(String valorConsulta, int nivelMontarDados) throws Exception {
        consultar(getIdEntidade(), true);
        String sqlStr = "SELECT QuestionarioPergunta.* FROM QuestionarioPergunta, Questionario WHERE QuestionarioPergunta.questionario = Questionario.codigo and upper( Questionario.nomeInterno ) like('" + valorConsulta.toUpperCase() + "%') ORDER BY Questionario.nomeInterno,QuestionarioPergunta.codigo";
        try (Statement stm = con.createStatement()) {
            try (ResultSet tabelaResultado = stm.executeQuery(sqlStr)) {
                return montarDadosConsulta(tabelaResultado, nivelMontarDados, this.con);
            }
        }
    }

    /**
     * Responsável por realizar uma consulta de <code>QuestionarioPergunta</code> através do valor do atributo 
     * <code>Integer codigo</code>. Retorna os objetos com valores iguais ou superiores ao parâmetro fornecido.
     * Faz uso da operação <code>montarDadosConsulta</code> que realiza o trabalho de prerarar o List resultante.
     * @param   controlarAcesso Indica se a aplicação deverá verificar se o usuário possui permissão para esta consulta ou não.
     * @return  List Contendo vários objetos da classe <code>QuestionarioPerguntaVO</code> resultantes da consulta.
     * @exception Exception Caso haja problemas de conexão ou restrição de acesso.
     */
    public List consultarPorCodigo(Integer valorConsulta, boolean controlarAcesso, int nivelMontarDados) throws Exception {
        consultar(getIdEntidade(), controlarAcesso);
        String sqlStr = "SELECT * FROM QuestionarioPergunta WHERE codigo >= " + valorConsulta.intValue() + " ORDER BY codigo";
        try (Statement stm = con.createStatement()) {
            try (ResultSet tabelaResultado = stm.executeQuery(sqlStr)) {
                return (montarDadosConsulta(tabelaResultado, nivelMontarDados, this.con));
            }
        }
    }

    /**
     * Responsável por montar os dados de vários objetos, resultantes de uma consulta ao banco de dados (<code>ResultSet</code>).
     * Faz uso da operação <code>montarDados</code> que realiza o trabalho para um objeto por vez.
     * @return  List Contendo vários objetos da classe <code>QuestionarioPerguntaVO</code> resultantes da consulta.
     */
    public static List montarDadosConsulta(ResultSet tabelaResultado, int nivelMontarDados, Connection con) throws Exception {
        List vetResultado = new ArrayList();
        while (tabelaResultado.next()) {
            QuestionarioPerguntaVO obj = new QuestionarioPerguntaVO();
            obj = montarDados(tabelaResultado, nivelMontarDados, con);
            vetResultado.add(obj);
        }
        return vetResultado;
    }

    /**
     * Responsável por montar os dados resultantes de uma consulta ao banco de dados (<code>ResultSet</code>)
     * em um objeto da classe <code>QuestionarioPerguntaVO</code>.
     * @return  O objeto da classe <code>QuestionarioPerguntaVO</code> com os dados devidamente montados.
     */
    public static QuestionarioPerguntaVO montarDados(ResultSet dadosSQL, int nivelMontarDados, Connection con) throws Exception {
        QuestionarioPerguntaVO obj = new QuestionarioPerguntaVO();
        obj.setCodigo(dadosSQL.getInt("codigo"));
        obj.setQuestionario(dadosSQL.getInt("questionario"));
        obj.setObrigatoria(dadosSQL.getBoolean("obrigatoria"));
        obj.getPergunta().setCodigo(dadosSQL.getInt("pergunta"));
        obj.setNrQuestao(dadosSQL.getInt("nrQuestao"));
        obj.setNovoObj(false);
//        if (nivelMontarDados == Uteis.NIVELMONTARDADOS_DADOSBASICOS) {
//            return obj;
//        }

        montarDadosPergunta(obj, Uteis.NIVELMONTARDADOS_DADOSBASICOS, con);
        return obj;
    }

    /**
     * Operação responsável por montar os dados de um objeto da classe <code>PerguntaVO</code> relacionado ao objeto <code>QuestionarioPerguntaVO</code>.
     * Faz uso da chave primária da classe <code>PerguntaVO</code> para realizar a consulta.
     * @param obj  Objeto no qual será montado os dados consultados.
     */
    public static void montarDadosPergunta(QuestionarioPerguntaVO obj, int nivelMontarDados, Connection con) throws Exception {
        if (obj.getPergunta().getCodigo().intValue() == 0) {
            obj.setPergunta(new PerguntaVO());
            return;
        }
        Pergunta pergunta = new Pergunta(con); 
        obj.setPergunta(pergunta.consultarPorChavePrimaria(obj.getPergunta().getCodigo(), nivelMontarDados));
        pergunta = null;
    }

    /**
     * Operação responsável por excluir todos os objetos da <code>QuestionarioPerguntaVO</code> no BD.
     * Faz uso da operação <code>excluir</code> disponível na classe <code>QuestionarioPergunta</code>.
     * @param <code>questionario</code> campo chave para exclusão dos objetos no BD.
     * @exception Exception  Erro de conexão com o BD ou restrição de acesso a esta operação.
     */
    public void excluirQuestionarioPerguntas(Integer questionario) throws Exception {
        excluir(getIdEntidade());
        String sql = "DELETE FROM QuestionarioPergunta WHERE (questionario = ?)";
        PreparedStatement sqlExcluir = con.prepareStatement(sql);
        sqlExcluir.setInt(1, questionario.intValue());
        sqlExcluir.execute();
    }

    /**
     * Operação responsável por alterar todos os objetos da <code>QuestionarioPerguntaVO</code> contidos em um Hashtable no BD.
     * Faz uso da operação <code>excluirQuestionarioPerguntas</code> e <code>incluirQuestionarioPerguntas</code> disponíveis na classe <code>QuestionarioPergunta</code>.
     * @param objetos  List com os objetos a serem alterados ou incluídos no BD.
     * @exception Exception  Erro de conexão com o BD ou restrição de acesso a esta operação.
     */
    public void alterarQuestionarioPerguntas(Integer questionario, List objetos) throws Exception {
        excluirQuestionarioPerguntas(questionario);
        incluirQuestionarioPerguntas(questionario, objetos);
    }

   
    /**
     * Operação responsável por incluir objetos da <code>QuestionarioPerguntaVO</code> no BD.
     * Garantindo o relacionamento com a entidade principal <code>basico.Questionario</code> através do atributo de vínculo.
     * @param objetos List contendo os objetos a serem gravados no BD da classe.
     * @exception Exception  Erro de conexão com o BD ou restrição de acesso a esta operação.
     */
    public void incluirQuestionarioPerguntas(Integer questionarioPrm, List objetos) throws Exception {
        Iterator e = objetos.iterator();
        while (e.hasNext()) {
            QuestionarioPerguntaVO obj = (QuestionarioPerguntaVO) e.next();
            obj.setQuestionario(questionarioPrm);
            incluir(obj);
        }
    }

    /**
     * Operação responsável por consultar todos os <code>QuestionarioPerguntaVO</code> relacionados a um objeto da classe <code>basico.Questionario</code>.
     * @param questionario  Atributo de <code>basico.Questionario</code> a ser utilizado para localizar os objetos da classe <code>QuestionarioPerguntaVO</code>.
     * @return List  Contendo todos os objetos da classe <code>QuestionarioPerguntaVO</code> resultantes da consulta.
     * @exception Exception  Erro de conexão com o BD ou restrição de acesso a esta operação.
     */
    public List consultarQuestionarioPerguntas(Integer questionario, int nivelMontarDados) throws Exception {
        consultar(getIdEntidade());
        List objetos = new ArrayList();
        String sql = "SELECT * FROM QuestionarioPergunta WHERE questionario = ? order by nrQuestao";
        try (PreparedStatement sqlConsulta = con.prepareStatement(sql)) {
            sqlConsulta.setInt(1, questionario.intValue());
            try (ResultSet resultado = sqlConsulta.executeQuery()) {
                while (resultado.next()) {
                    QuestionarioPerguntaVO novoObj = new QuestionarioPerguntaVO();
                    novoObj = QuestionarioPergunta.montarDados(resultado, nivelMontarDados, this.con);
                    objetos.add(novoObj);
                }
            }
        }
        return objetos;
    }
    /**
     * Operação responsável por consultar todos os <code>QuestionarioPerguntaVO</code> relacionados a um objeto da classe <code>basico.Questionario</code>.
     * @param questionario  Atributo de <code>basico.Questionario</code> a ser utilizado para localizar os objetos da classe <code>QuestionarioPerguntaVO</code>.
     * @return List  Contendo todos os objetos da classe <code>QuestionarioPerguntaVO</code> resultantes da consulta.
     * @exception Exception  Erro de conexão com o BD ou restrição de acesso a esta operação.
     */
    public Boolean consultarQuestionarioPerguntasPorQuestionarioEPergunta(Integer questionario, Integer pergunta, int nivelMontarDados) throws Exception {
        consultar(getIdEntidade());
        String sql = "SELECT * FROM QuestionarioPergunta WHERE questionario = ? AND pergunta = ?";
        try (PreparedStatement sqlConsulta = con.prepareStatement(sql)) {
            sqlConsulta.setInt(1, questionario.intValue());
            sqlConsulta.setInt(2, pergunta.intValue());
            try (ResultSet resultado = sqlConsulta.executeQuery()) {
                if (resultado.next()) {
                    return true;
                }
            }
        }
        return false;
    }

    /**
     * Operação responsável por localizar um objeto da classe <code>QuestionarioPerguntaVO</code>
     * através de sua chave primária. 
     * @exception Exception Caso haja problemas de conexão ou localização do objeto procurado.
     */
    public QuestionarioPerguntaVO consultarPorChavePrimaria(Integer codigoPrm, int nivelMontarDados) throws Exception {
        consultar(getIdEntidade(), false);
        String sql = "SELECT * FROM QuestionarioPergunta WHERE codigo = ?";
        try (PreparedStatement sqlConsultar = con.prepareStatement(sql)) {
            sqlConsultar.setInt(1, codigoPrm.intValue());
            try (ResultSet tabelaResultado = sqlConsultar.executeQuery()) {
                if (!tabelaResultado.next()) {
                    throw new ConsistirException("Dados Não Encontrados ( QuestionarioPergunta ).");
                }
                return (montarDados(tabelaResultado, nivelMontarDados, this.con));
            }
        }
    }

}

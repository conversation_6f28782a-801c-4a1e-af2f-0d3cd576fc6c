package negocio.facade.jdbc.basico;

import negocio.comuns.basico.TotalPassVO;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.List;
import negocio.comuns.utilitarias.Uteis;
import negocio.facade.jdbc.arquitetura.SuperEntidade;
import negocio.interfaces.basico.TotalpassInterfaceFacade;
import javax.servlet.http.HttpSession;
import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.Statement;



public class ConfigTotalPass extends SuperEntidade implements TotalpassInterfaceFacade {

    @Override
    public List consultarPorEmpresa(Integer codigo, int nivelMontarDados) throws Exception {
        String sqlStr = "SELECT * FROM configtotalpass WHERE empresa_codigo = " + codigo ;
        try (Statement stm = con.createStatement()) {
            try (ResultSet tabelaResultado = stm.executeQuery(sqlStr)) {
                return (montarDadosConsulta(tabelaResultado, nivelMontarDados));
            }
        }
    }

    @Override
    public boolean possuiConfiguracaoTotalPassAtiva(Integer codigo) throws Exception {
        String sqlStr = "SELECT * FROM configtotalpass WHERE inativo IS FALSE AND empresa_codigo = " + codigo;
        return existe(sqlStr, con);
    }

    public Integer obterLimiteDeAcessosPorDiaTotalpass(Integer codEmpresa) throws SQLException {
        String sql = "SELECT limitedeacessospordia FROM configtotalpass WHERE empresa_codigo = ?";
        try (PreparedStatement pstm = con.prepareStatement(sql)) {
            pstm.setInt(1, codEmpresa);
            try (ResultSet rs = pstm.executeQuery()) {
                if (rs.next()) {
                    return rs.getInt("limitedeacessospordia");
                }
            }
        }
        return 0;
    }

    public Integer obterLimiteDeAulasPorDiaTotalpass(Integer codEmpresa) throws SQLException {
        String sql = "SELECT limitedeaulaspordia FROM configtotalpass WHERE empresa_codigo = ?";
        try (PreparedStatement pstm = con.prepareStatement(sql)) {
            pstm.setInt(1, codEmpresa);
            try (ResultSet rs = pstm.executeQuery()) {
                if (rs.next()) {
                    return rs.getInt("limitedeaulaspordia");
                }
            }
        }
        return 0;
    }

    public static List montarDadosConsulta(ResultSet tabelaResultado, int nivelMontarDados) throws Exception {
        List vetResultado = new ArrayList();
        while (tabelaResultado.next()) {
            TotalPassVO obj = new TotalPassVO();
            obj = montarDados(tabelaResultado, nivelMontarDados);
            vetResultado.add(obj);
        }
        return vetResultado;
    }

    public static TotalPassVO montarDados(ResultSet dadosSQL, int nivelMontarDados) throws Exception {
        TotalPassVO obj = new TotalPassVO();
        obj.setCodigoTotalpass(dadosSQL.getString(("codigototalpass")));
        obj.setApiKey(dadosSQL.getString("ApiKey"));
        obj.setInativo(dadosSQL.getBoolean("inativo"));
        obj.setEmpresa(dadosSQL.getInt("empresa_codigo"));
        obj.setPermitirWod(dadosSQL.getBoolean("permitirWod"));
        obj.setLimiteDeAcessosPorDia(dadosSQL.getInt("LimiteDeAcessosPorDia"));
        obj.setLimiteDeAulasPorDia(dadosSQL.getInt("LimiteDeAulasPorDia"));

        if (nivelMontarDados == Uteis.NIVELMONTARDADOS_DADOSBASICOS) {
            return obj;
        }

        return obj;
    }

    public ConfigTotalPass() throws Exception {  }

    public ConfigTotalPass(HttpSession session) throws Exception {
        super(session); }

    public ConfigTotalPass(Connection conexao) throws Exception {
        super(conexao); }


}

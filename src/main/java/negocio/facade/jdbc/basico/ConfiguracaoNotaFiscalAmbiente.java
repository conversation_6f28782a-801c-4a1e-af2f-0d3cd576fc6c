package negocio.facade.jdbc.basico;

import negocio.comuns.basico.ConfiguracaoNotaFiscalAmbienteVO;
import negocio.comuns.basico.ConfiguracaoNotaFiscalVO;
import negocio.comuns.notaFiscal.AmbienteEmissaoNotaFiscalEnum;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisValidacao;
import negocio.facade.jdbc.arquitetura.SuperEntidade;
import negocio.interfaces.basico.ConfiguracaoNotaFiscalAmbienteInterfaceFacade;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.util.ArrayList;
import java.util.List;

public class ConfiguracaoNotaFiscalAmbiente extends SuperEntidade implements ConfiguracaoNotaFiscalAmbienteInterfaceFacade {

    public ConfiguracaoNotaFiscalAmbiente() throws Exception {
        super();
    }

    public ConfiguracaoNotaFiscalAmbiente(Connection con) throws Exception {
        super(con);
    }

    public void incluir(ConfiguracaoNotaFiscalAmbienteVO obj) throws Exception {
        try {
            con.setAutoCommit(false);
            incluirSemCommit(obj);
            con.commit();
        } catch (Exception e) {
            con.rollback();
            con.setAutoCommit(true);
            throw e;
        } finally {
            con.setAutoCommit(true);
        }
    }

    public void incluirSemCommit(ConfiguracaoNotaFiscalAmbienteVO obj) throws Exception {
//        ConfiguracaoNotaFiscalAmbienteVO.validarDados(obj);
        String sql = "INSERT INTO ConfiguracaoNotaFiscalAmbiente (configuracaoNotaFiscal, ambienteEmissao, serieNFe, sequencialNFe, " +
                "sequencialLoteNFe, usuarioAcessoProvedor, senhaAcessoProvedor, tokenAcessoProvedor, idCSC, csc) " +
                "VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)";
        int i = 0;
        PreparedStatement sqlInserir = con.prepareStatement(sql);
        resolveIntegerNull(sqlInserir, ++i, obj.getConfiguracaoNotaFiscalVO().getCodigo());
        sqlInserir.setInt(++i, obj.getAmbienteEmissao().getCodigo());
        sqlInserir.setString(++i, obj.getSerieNFe());
        sqlInserir.setInt(++i, obj.getSequencialNFe());
        sqlInserir.setInt(++i, obj.getSequencialLoteNFe());
        sqlInserir.setString(++i, obj.getUsuarioAcessoProvedor());
        sqlInserir.setString(++i, obj.getSenhaAcessoProvedor());
        sqlInserir.setString(++i, obj.getTokenAcessoProvedor());
        sqlInserir.setString(++i, obj.getIdCSC());
        sqlInserir.setString(++i, obj.getCsc());

        sqlInserir.execute();
        obj.setCodigo(obterValorChavePrimariaCodigo());
        obj.setNovoObj(false);
    }


    public void alterar(ConfiguracaoNotaFiscalAmbienteVO obj) throws Exception {
        try {
            con.setAutoCommit(false);
            alterarSemCommit(obj);
            con.commit();
        } catch (Exception e) {
            con.rollback();
            con.setAutoCommit(true);
            throw e;
        } finally {
            con.setAutoCommit(true);
        }
    }

    public void alterarSemCommit(ConfiguracaoNotaFiscalAmbienteVO obj) throws Exception {
//        ConfiguracaoNotaFiscalAmbienteVO.validarDados(obj);
        String sql = "UPDATE ConfiguracaoNotaFiscalAmbiente SET configuracaoNotaFiscal = ?, ambienteEmissao = ?, serieNFe = ?, " +
                "sequencialNFe = ?, sequencialLoteNFe = ?, usuarioAcessoProvedor = ?, senhaAcessoProvedor = ?, tokenAcessoProvedor = ?, " +
                "idCSC = ?, csc = ? WHERE codigo = ?";
        int i = 0;
        PreparedStatement sqlAlterar = con.prepareStatement(sql);
        resolveIntegerNull(sqlAlterar, ++i, obj.getConfiguracaoNotaFiscalVO().getCodigo());
        sqlAlterar.setInt(++i, obj.getAmbienteEmissao().getCodigo());
        sqlAlterar.setString(++i, obj.getSerieNFe());
        sqlAlterar.setInt(++i, obj.getSequencialNFe());
        sqlAlterar.setInt(++i, obj.getSequencialLoteNFe());
        sqlAlterar.setString(++i, obj.getUsuarioAcessoProvedor());
        sqlAlterar.setString(++i, obj.getSenhaAcessoProvedor());
        sqlAlterar.setString(++i, obj.getTokenAcessoProvedor());
        sqlAlterar.setString(++i, obj.getIdCSC());
        sqlAlterar.setString(++i, obj.getCsc());

        sqlAlterar.setInt(++i, obj.getCodigo());
        sqlAlterar.execute();
    }

    public void excluir(ConfiguracaoNotaFiscalAmbienteVO obj) throws Exception {
        try {
            con.setAutoCommit(false);
            excluirSemCommit(obj);
            con.commit();
        } catch (Exception e) {
            con.rollback();
            con.setAutoCommit(true);
            throw e;
        } finally {
            con.setAutoCommit(true);
        }
    }

    public void excluirSemCommit(ConfiguracaoNotaFiscalAmbienteVO obj) throws Exception {
        String sql = "DELETE FROM ConfiguracaoNotaFiscalAmbiente WHERE codigo = ?";
        PreparedStatement sqlExcluir = con.prepareStatement(sql);
        sqlExcluir.setInt(1, obj.getCodigo());
        sqlExcluir.execute();
    }

    public void incluirOuAlteraSemComitPorConfiguracaoNotaFiscal(ConfiguracaoNotaFiscalVO configuracaoNotaFiscalVO) throws Exception {
        configuracaoNotaFiscalVO.getConfigProducaoVO().setConfiguracaoNotaFiscalVO(configuracaoNotaFiscalVO);
        if (UteisValidacao.emptyNumber(configuracaoNotaFiscalVO.getConfigProducaoVO().getCodigo())) {
            incluirSemCommit(configuracaoNotaFiscalVO.getConfigProducaoVO());
        } else if (!UteisValidacao.emptyNumber(configuracaoNotaFiscalVO.getConfigProducaoVO().getCodigo())) {
            alterarSemCommit(configuracaoNotaFiscalVO.getConfigProducaoVO());
        }

        configuracaoNotaFiscalVO.getConfigHomologacaoVO().setConfiguracaoNotaFiscalVO(configuracaoNotaFiscalVO);
        if (UteisValidacao.emptyNumber(configuracaoNotaFiscalVO.getConfigHomologacaoVO().getCodigo())) {
            incluirSemCommit(configuracaoNotaFiscalVO.getConfigHomologacaoVO());
        } else if (!UteisValidacao.emptyNumber(configuracaoNotaFiscalVO.getConfigHomologacaoVO().getCodigo())) {
            alterarSemCommit(configuracaoNotaFiscalVO.getConfigHomologacaoVO());
        }
    }

    public static List<ConfiguracaoNotaFiscalAmbienteVO> montarDadosConsulta(ResultSet rs, int nivelMontarDados, Connection con) throws Exception {
        List<ConfiguracaoNotaFiscalAmbienteVO> vetResultado = new ArrayList<ConfiguracaoNotaFiscalAmbienteVO>();
        while (rs.next()) {
            ConfiguracaoNotaFiscalAmbienteVO obj = montarDados(rs, nivelMontarDados, con);
            vetResultado.add(obj);
        }
        return vetResultado;
    }

    public static ConfiguracaoNotaFiscalAmbienteVO montarDados(ResultSet rs, int nivelMontarDados, Connection con) throws Exception {
        ConfiguracaoNotaFiscalAmbienteVO obj = new ConfiguracaoNotaFiscalAmbienteVO();
        obj.setCodigo(rs.getInt("codigo"));
        obj.setNovoObj(false);
        obj.getConfiguracaoNotaFiscalVO().setCodigo(rs.getInt("configuracaoNotaFiscal"));
        obj.setAmbienteEmissao(AmbienteEmissaoNotaFiscalEnum.obterPorCodigo(rs.getInt("ambienteEmissao")));
        obj.setSerieNFe(rs.getString("serieNFe"));
        obj.setSequencialNFe(rs.getInt("sequencialNFe"));
        obj.setSequencialLoteNFe(rs.getInt("sequencialLoteNFe"));
        obj.setUsuarioAcessoProvedor(rs.getString("usuarioAcessoProvedor"));
        obj.setSenhaAcessoProvedor(rs.getString("senhaAcessoProvedor"));
        obj.setTokenAcessoProvedor(rs.getString("tokenAcessoProvedor"));
        obj.setIdCSC(rs.getString("idCSC"));
        obj.setCsc(rs.getString("csc"));

        if (nivelMontarDados == Uteis.NIVELMONTARDADOS_DADOSBASICOS) {
            return obj;
        }
        return obj;
    }

    public ConfiguracaoNotaFiscalAmbienteVO consultarPorChavePrimaria(Integer codigo, int nivelMontarDados) throws Exception {
        String sql = "SELECT * FROM ConfiguracaoNotaFiscalAmbiente WHERE codigo = ?";
        PreparedStatement sqlConsultar = con.prepareStatement(sql);
        sqlConsultar.setInt(1, codigo);
        ResultSet rs = sqlConsultar.executeQuery();
        if (!rs.next()) {
            return new ConfiguracaoNotaFiscalAmbienteVO();
        }
        return montarDados(rs, nivelMontarDados, con);
    }

    public ConfiguracaoNotaFiscalAmbienteVO consultarPorAmbienteEmissaoConfiguracaoNotaFiscal(AmbienteEmissaoNotaFiscalEnum ambienteEmissaoNotaFiscalEnum,
                                                                                           Integer configuracaoNotaFiscal, int nivelMontarDados) throws Exception {
        StringBuilder sql = new StringBuilder();
        sql.append("SELECT * FROM ConfiguracaoNotaFiscalAmbiente \n");
        sql.append("WHERE 1 = 1 \n");
        if (ambienteEmissaoNotaFiscalEnum != null) {
            sql.append("AND ambienteEmissao = ").append(ambienteEmissaoNotaFiscalEnum.getCodigo()).append(" \n");
        }
        if (!UteisValidacao.emptyNumber(configuracaoNotaFiscal)) {
            sql.append("AND configuracaoNotaFiscal = ").append(configuracaoNotaFiscal).append(" \n");
        }
        PreparedStatement sqlConsultar = con.prepareStatement(sql.toString());
        ResultSet rs = sqlConsultar.executeQuery();
        if (!rs.next()) {
            return new ConfiguracaoNotaFiscalAmbienteVO(ambienteEmissaoNotaFiscalEnum);
        }
        return montarDados(rs, nivelMontarDados, con);
    }
}

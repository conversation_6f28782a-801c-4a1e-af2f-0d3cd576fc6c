package negocio.facade.jdbc.basico;

import java.util.Iterator;
import negocio.facade.jdbc.arquitetura.*;

import java.sql.Connection;
import java.sql.ResultSet;
import java.sql.PreparedStatement;
import java.sql.Statement;
import java.util.List;
import java.util.ArrayList;
import negocio.comuns.utilitarias.*;
import negocio.comuns.basico.ContaCorrenteEmpresaVO;
import negocio.comuns.financeiro.ContaCorrenteVO;
import negocio.facade.jdbc.financeiro.ContaCorrente;

/**
 * Classe de persistência que encapsula todas as operações de manipulação dos dados da classe <code>ContaCorrenteEmpresaVO</code>.
 * Responsável por implementar operações como incluir, alterar, excluir e consultar pertinentes a classe <code>ContaCorrenteEmpresaVO</code>.
 * Encapsula toda a interação com o banco de dados.
 * @see ContaCorrenteEmpresaVO
 * @see SuperEntidade
 * @see Empresa
 */
public class ContaCorrenteEmpresa extends SuperEntidade {    

    public ContaCorrenteEmpresa() throws Exception {
        super();        
        setIdEntidade("Empresa");
    }

    public ContaCorrenteEmpresa(Connection con) throws Exception {
    	super(con);        
        setIdEntidade("Empresa");
	}

	/**
     * Operação responsável por retornar um novo objeto da classe <code>ContaCorrenteEmpresaVO</code>.
     */
    public ContaCorrenteEmpresaVO novo() throws Exception {
        incluir(getIdEntidade());
        ContaCorrenteEmpresaVO obj = new ContaCorrenteEmpresaVO();
        return obj;
    }

    /**
     * Operação responsável por incluir no banco de dados um objeto da classe <code>ContaCorrenteEmpresaVO</code>.
     * Primeiramente valida os dados (<code>validarDados</code>) do objeto. Verifica a conexão com o banco de dados e a permissão do usuário
     * para realizar esta operacão na entidade.
     * Isto, através da operação <code>incluir</code> da superclasse.
     * @param obj  Objeto da classe <code>ContaCorrenteEmpresaVO</code> que será gravado no banco de dados.
     * @exception Exception Caso haja problemas de conexão, restrição de acesso ou validação de dados.
     */
    public void incluir(ContaCorrenteEmpresaVO obj) throws Exception {
        try{
        ContaCorrenteEmpresaVO.validarDados(obj);
        incluir(getIdEntidade());
        obj.realizarUpperCaseDados();
        String sql = "INSERT INTO ContaCorrenteEmpresa( contaCorrente, empresa ) VALUES ( ?, ? )";
            try (PreparedStatement sqlInserir = con.prepareStatement(sql)) {
                if (obj.getContaCorrente().getCodigo().intValue() != 0) {
                    sqlInserir.setInt(1, obj.getContaCorrente().getCodigo().intValue());
                } else {
                    sqlInserir.setNull(1, 0);
                }
                if (obj.getEmpresa().intValue() != 0) {
                    sqlInserir.setInt(2, obj.getEmpresa().intValue());
                } else {
                    sqlInserir.setNull(2, 0);
                }
                sqlInserir.execute();
            }
            obj.setCodigo(obterValorChavePrimariaCodigo());
        obj.setNovoObj(new Boolean(false));
        }catch (Exception e){
            System.out.println(e.getMessage());
            throw e;
    }
    }

    /**
     * Operação responsável por alterar no BD os dados de um objeto da classe <code>ContaCorrenteEmpresaVO</code>.
     * Sempre utiliza a chave primária da classe como atributo para localização do registro a ser alterado.
     * Primeiramente valida os dados (<code>validarDados</code>) do objeto. Verifica a conexão com o banco de dados e a permissão do usuário
     * para realizar esta operacão na entidade.
     * Isto, através da operação <code>alterar</code> da superclasse.
     * @param obj    Objeto da classe <code>ContaCorrenteEmpresaVO</code> que será alterada no banco de dados.
     * @exception Execption Caso haja problemas de conexão, restrição de acesso ou validação de dados.
     */
    public void alterar(ContaCorrenteEmpresaVO obj) throws Exception {
        ContaCorrenteEmpresaVO.validarDados(obj);
        alterar(getIdEntidade());
        obj.realizarUpperCaseDados();
        String sql = "UPDATE ContaCorrenteEmpresa set contaCorrente=?, empresa=? WHERE ((codigo = ?))";
        try (PreparedStatement sqlAlterar = con.prepareStatement(sql)) {
            if (obj.getContaCorrente().getCodigo().intValue() != 0) {
                sqlAlterar.setInt(1, obj.getContaCorrente().getCodigo().intValue());
            } else {
                sqlAlterar.setNull(1, 0);
            }
            if (obj.getEmpresa().intValue() != 0) {
                sqlAlterar.setInt(2, obj.getEmpresa().intValue());
            } else {
                sqlAlterar.setNull(2, 0);
            }
            sqlAlterar.setInt(3, obj.getCodigo().intValue());
            sqlAlterar.execute();
        }
    }

    /**
     * Operação responsável por excluir no BD um objeto da classe <code>ContaCorrenteEmpresaVO</code>.
     * Sempre localiza o registro a ser excluído através da chave primária da entidade.
     * Primeiramente verifica a conexão com o banco de dados e a permissão do usuário
     * para realizar esta operacão na entidade.
     * Isto, através da operação <code>excluir</code> da superclasse.
     * @param obj    Objeto da classe <code>ContaCorrenteEmpresaVO</code> que será removido no banco de dados.
     * @exception Execption Caso haja problemas de conexão ou restrição de acesso.
     */
    public void excluir(ContaCorrenteEmpresaVO obj) throws Exception {
        excluir(getIdEntidade());
        String sql = "DELETE FROM ContaCorrenteEmpresa WHERE ((codigo = ?))";
        try (PreparedStatement sqlExcluir = con.prepareStatement(sql)) {
            sqlExcluir.setInt(1, obj.getCodigo().intValue());
            sqlExcluir.execute();
        }
    }

    /**
     * Responsável por realizar uma consulta de <code>ContaCorrenteEmpresa</code> através do valor do atributo 
     * <code>nome</code> da classe <code>Empresa</code>
     * Faz uso da operação <code>montarDadosConsulta</code> que realiza o trabalho de prerarar o List resultante.
     * @return  List Contendo vários objetos da classe <code>ContaCorrenteEmpresaVO</code> resultantes da consulta.
     * @exception Execption Caso haja problemas de conexão ou restrição de acesso.
     */
    public List consultarPorNomeEmpresa(String valorConsulta, int nivelMontarDados) throws Exception {
        consultar(getIdEntidade(), true);
        String sqlStr = "SELECT ContaCorrenteEmpresa.* FROM ContaCorrenteEmpresa, Empresa WHERE ContaCorrenteEmpresa.empresa = Empresa.codigo and upper( Empresa.nome ) like('" + valorConsulta.toUpperCase() + "%') ORDER BY Empresa.nome";
        try (Statement stm = con.createStatement()) {
            try (ResultSet tabelaResultado = stm.executeQuery(sqlStr)) {
                return montarDadosConsulta(tabelaResultado, nivelMontarDados, this.con);
            }
        }
    }

    /**
     * Responsável por realizar uma consulta de <code>ContaCorrenteEmpresa</code> através do valor do atributo 
     * <code>contaCorrente</code> da classe <code>ContaCorrente</code>
     * Faz uso da operação <code>montarDadosConsulta</code> que realiza o trabalho de prerarar o List resultante.
     * @return  List Contendo vários objetos da classe <code>ContaCorrenteEmpresaVO</code> resultantes da consulta.
     * @exception Execption Caso haja problemas de conexão ou restrição de acesso.
     */
    public List consultarPorContaCorrenteContaCorrente(String valorConsulta, int nivelMontarDados) throws Exception {
        consultar(getIdEntidade(), true);
        String sqlStr = "SELECT ContaCorrenteEmpresa.* FROM ContaCorrenteEmpresa, ContaCorrente WHERE ContaCorrenteEmpresa.contaCorrente = ContaCorrente.codigo and upper( ContaCorrente.contaCorrente ) like('" + valorConsulta.toUpperCase() + "%') ORDER BY ContaCorrente.contaCorrente";
        try (Statement stm = con.createStatement()) {
            try (ResultSet tabelaResultado = stm.executeQuery(sqlStr)) {
                return montarDadosConsulta(tabelaResultado, nivelMontarDados, this.con);
            }
        }
    }

    /**
     * Responsável por realizar uma consulta de <code>ContaCorrenteEmpresa</code> através do valor do atributo 
     * <code>Integer codigo</code>. Retorna os objetos com valores iguais ou superiores ao parâmetro fornecido.
     * Faz uso da operação <code>montarDadosConsulta</code> que realiza o trabalho de prerarar o List resultante.
     * @param   controlarAcesso Indica se a aplicação deverá verificar se o usuário possui permissão para esta consulta ou não.
     * @return  List Contendo vários objetos da classe <code>ContaCorrenteEmpresaVO</code> resultantes da consulta.
     * @exception Exception Caso haja problemas de conexão ou restrição de acesso.
     */
    public List consultarPorCodigo(Integer valorConsulta, boolean controlarAcesso, int nivelMontarDados) throws Exception {
        consultar(getIdEntidade(), controlarAcesso);
        String sqlStr = "SELECT * FROM ContaCorrenteEmpresa WHERE codigo >= " + valorConsulta.intValue() + " ORDER BY codigo";
        try (Statement stm = con.createStatement()) {
            try (ResultSet tabelaResultado = stm.executeQuery(sqlStr)) {
                return (montarDadosConsulta(tabelaResultado, nivelMontarDados, this.con));
            }
        }
    }

    /**
     * Responsável por montar os dados de vários objetos, resultantes de uma consulta ao banco de dados (<code>ResultSet</code>).
     * Faz uso da operação <code>montarDados</code> que realiza o trabalho para um objeto por vez.
     * @return  List Contendo vários objetos da classe <code>ContaCorrenteEmpresaVO</code> resultantes da consulta.
     */
    public static List montarDadosConsulta(ResultSet tabelaResultado, int nivelMontarDados, Connection con) throws Exception {
        List vetResultado = new ArrayList();
        while (tabelaResultado.next()) {
            ContaCorrenteEmpresaVO obj = new ContaCorrenteEmpresaVO();
            obj = montarDados(tabelaResultado, nivelMontarDados, con);
            vetResultado.add(obj);
        }
        return vetResultado;
    }

    /**
     * Responsável por montar os dados resultantes de uma consulta ao banco de dados (<code>ResultSet</code>)
     * em um objeto da classe <code>ContaCorrenteEmpresaVO</code>.
     * @return  O objeto da classe <code>ContaCorrenteEmpresaVO</code> com os dados devidamente montados.
     */
    public static ContaCorrenteEmpresaVO montarDados(ResultSet dadosSQL, int nivelMontarDados, Connection con) throws Exception {
        ContaCorrenteEmpresaVO obj = new ContaCorrenteEmpresaVO();
        obj.setCodigo(new Integer(dadosSQL.getInt("codigo")));
        obj.getContaCorrente().setCodigo(new Integer(dadosSQL.getInt("contaCorrente")));
        obj.setEmpresa(new Integer(dadosSQL.getInt("empresa")));
        obj.setNovoObj(new Boolean(false));
        if (nivelMontarDados == Uteis.NIVELMONTARDADOS_DADOSBASICOS) {
            return obj;
        }

        montarDadosContaCorrente(obj, nivelMontarDados, con);
        return obj;
    }

    /**
     * Operação responsável por montar os dados de um objeto da classe <code>ContaCorrenteVO</code> relacionado ao objeto <code>ContaCorrenteEmpresaVO</code>.
     * Faz uso da chave primária da classe <code>ContaCorrenteVO</code> para realizar a consulta.
     * @param obj  Objeto no qual será montado os dados consultados.
     */
    public static void montarDadosContaCorrente(ContaCorrenteEmpresaVO obj, int nivelMontarDados, Connection con) throws Exception {
        if (obj.getContaCorrente().getCodigo().intValue() == 0) {
            obj.setContaCorrente(new ContaCorrenteVO());
            return;
        }
        ContaCorrente contaCorrente = new ContaCorrente(con);
        obj.setContaCorrente(contaCorrente.consultarPorChavePrimaria(obj.getContaCorrente().getCodigo(), nivelMontarDados));
    }

    /**
     * Operação responsável por excluir todos os objetos da <code>ContaCorrenteEmpresaVO</code> no BD.
     * Faz uso da operação <code>excluir</code> disponível na classe <code>ContaCorrenteEmpresa</code>.
     * @param <code>empresa</code> campo chave para exclusão dos objetos no BD.
     * @exception Exception  Erro de conexão com o BD ou restrição de acesso a esta operação.
     */
    public void excluirContaCorrenteEmpresas(Integer empresa) throws Exception {
        excluir(getIdEntidade());
        String sql = "DELETE FROM ContaCorrenteEmpresa WHERE (empresa = ?)";
        try (PreparedStatement sqlExcluir = con.prepareStatement(sql)) {
            sqlExcluir.setInt(1, empresa.intValue());
            sqlExcluir.execute();
        }
    }

    /**
     * Operação responsável por alterar todos os objetos da <code>ContaCorrenteEmpresaVO</code> contidos em um Hashtable no BD.
     * Faz uso da operação <code>excluirContaCorrenteEmpresas</code> e <code>incluirContaCorrenteEmpresas</code> disponíveis na classe <code>ContaCorrenteEmpresa</code>.
     * @param objetos  List com os objetos a serem alterados ou incluídos no BD.
     * @exception Exception  Erro de conexão com o BD ou restrição de acesso a esta operação.
     */
    public void alterarContaCorrenteEmpresas(Integer empresa, List<ContaCorrenteEmpresaVO> objetos) throws Exception {
         String codigoAtuais = "";
        for (ContaCorrenteEmpresaVO conta : objetos) {
             conta.setEmpresa(empresa);
            if(UteisValidacao.emptyNumber(conta.getCodigo())){
                incluir(conta);
            } else {
                alterar(conta);
            }
            codigoAtuais += ","+conta.getCodigo();
        }
        excluirContaCorrenteEmpresasCodigosAtuais(empresa,codigoAtuais.replaceFirst(",", ""));
    }
    
     public void excluirContaCorrenteEmpresasCodigosAtuais(final Integer codFormaPagamento,final String codigosAtuais) throws Exception {
        String sql = "DELETE FROM ContaCorrenteEmpresa WHERE (empresa = ?) ";
        if(!UteisValidacao.emptyString(codigosAtuais)){
            sql += " and codigo not in ("+codigosAtuais+")";
        }
         try (PreparedStatement sqlExcluir = con.prepareStatement(sql)) {
             sqlExcluir.setInt(1, codFormaPagamento);
             sqlExcluir.execute();
         }
     }

    /**
     * Operação responsável por incluir objetos da <code>ContaCorrenteEmpresaVO</code> no BD.
     * Garantindo o relacionamento com a entidade principal <code>basico.Empresa</code> através do atributo de vínculo.
     * @param objetos List contendo os objetos a serem gravados no BD da classe.
     * @exception Exception  Erro de conexão com o BD ou restrição de acesso a esta operação.
     */
    public void incluirContaCorrenteEmpresas(Integer empresaPrm, List objetos) throws Exception {
        Iterator e = objetos.iterator();
        while (e.hasNext()) {
            ContaCorrenteEmpresaVO obj = (ContaCorrenteEmpresaVO) e.next();
            obj.setEmpresa(empresaPrm);
            incluir(obj);
        }
    }

    /**
     * Operação responsável por consultar todos os <code>ContaCorrenteEmpresaVO</code> relacionados a um objeto da classe <code>basico.Empresa</code>.
     * @param empresa  Atributo de <code>basico.Empresa</code> a ser utilizado para localizar os objetos da classe <code>ContaCorrenteEmpresaVO</code>.
     * @return List  Contendo todos os objetos da classe <code>ContaCorrenteEmpresaVO</code> resultantes da consulta.
     * @exception Exception  Erro de conexão com o BD ou restrição de acesso a esta operação.
     */
    public List consultarContaCorrenteEmpresas(Integer empresa, int nivelMontarDados) throws Exception {
        consultar(getIdEntidade());
        List objetos = new ArrayList();
        String sql = "SELECT * FROM ContaCorrenteEmpresa WHERE empresa = ?";
        try (PreparedStatement sqlConsulta = con.prepareStatement(sql)) {
            sqlConsulta.setInt(1, empresa.intValue());
            try (ResultSet resultado = sqlConsulta.executeQuery()) {
                while (resultado.next()) {
                    ContaCorrenteEmpresaVO novoObj = new ContaCorrenteEmpresaVO();
                    novoObj = ContaCorrenteEmpresa.montarDados(resultado, nivelMontarDados, this.con);
                    objetos.add(novoObj);
                }
            }
        }
        return objetos;
    }

    /**
     * Operação responsável por localizar um objeto da classe <code>ContaCorrenteEmpresaVO</code>
     * através de sua chave primária. 
     * @exception Exception Caso haja problemas de conexão ou localização do objeto procurado.
     */
    public ContaCorrenteEmpresaVO consultarPorChavePrimaria(Integer codigoPrm, int nivelMontarDados) throws Exception {
        consultar(getIdEntidade(), false);
        String sql = "SELECT * FROM ContaCorrenteEmpresa WHERE codigo = ?";
        try (PreparedStatement sqlConsultar = con.prepareStatement(sql)) {
            sqlConsultar.setInt(1, codigoPrm.intValue());
            try (ResultSet tabelaResultado = sqlConsultar.executeQuery()) {
                if (!tabelaResultado.next()) {
                    throw new ConsistirException("Dados Não Encontrados ( ContaCorrenteEmpresa ).");
                }
                return (montarDados(tabelaResultado, nivelMontarDados, this.con));
            }
        }
    }    
}
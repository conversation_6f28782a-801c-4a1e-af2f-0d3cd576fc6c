package negocio.facade.jdbc.basico;

import negocio.comuns.arquitetura.PerfilAcessoVO;
import negocio.comuns.basico.ProfissaoVO;
import negocio.comuns.utilitarias.ConsistirException;
import negocio.comuns.utilitarias.Ordenacao;
import negocio.comuns.utilitarias.Uteis;
import negocio.facade.jdbc.arquitetura.SuperEntidade;
import negocio.interfaces.basico.ProfissaoInterfaceFacade;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.sql.Statement;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

/**
 * Classe de persistência que encapsula todas as operações de manipulação dos dados da classe <code>ProfissaoVO</code>.
 * Responsável por implementar operações como incluir, alterar, excluir e consultar pertinentes a classe <code>ProfissaoVO</code>.
 * Encapsula toda a interação com o banco de dados.
 * @see ProfissaoVO
 * @see SuperEntidade
 */
public class Profissao extends SuperEntidade implements ProfissaoInterfaceFacade {

    public Profissao() throws Exception {
        super();
    }

    public Profissao(Connection conexao) throws Exception {
        super(conexao);
    }

    /**
     * Operação responsável por retornar um novo objeto da classe <code>ProfissaoVO</code>.
     */
    public ProfissaoVO novo() throws Exception {
        incluir(getIdEntidade());
        ProfissaoVO obj = new ProfissaoVO();
        return obj;
    }

    /**
     * Operação responsável por incluir no banco de dados um objeto da classe <code>ProfissaoVO</code>.
     * Primeiramente valida os dados (<code>validarDados</code>) do objeto. Verifica a conexão com o banco de dados e a permissão do usuário
     * para realizar esta operacão na entidade.
     * Isto, através da operação <code>incluir</code> da superclasse.
     * @param obj  Objeto da classe <code>ProfissaoVO</code> que será gravado no banco de dados.
     * @exception Exception Caso haja problemas de conexão, restrição de acesso ou validação de dados.
     */
    public void incluir(ProfissaoVO obj, boolean centralEventos) throws Exception {
        try {
            ProfissaoVO.validarDados(obj);
            if (centralEventos) {
//            	incluirObj(getIdEntidade());
            } else {
                incluir(getIdEntidade());
            }

            obj.realizarUpperCaseDados();
            String sql = "INSERT INTO Profissao( descricao ) VALUES ( ? )";
            try (PreparedStatement sqlInserir = con.prepareStatement(sql)) {
                sqlInserir.setString(1, obj.getDescricao());
                sqlInserir.execute();
            }
            obj.setCodigo(obterValorChavePrimariaCodigo());
            obj.setNovoObj(new Boolean(false));
        } catch (Exception e) {
            throw e;
        }
    }
    /* (non-Javadoc)
     * @see negocio.interfaces.basico.ProfissaoInterfaceFacade#incluir(negocio.comuns.basico.ProfissaoVO)
     */

    public void incluir(ProfissaoVO obj) throws Exception {
        this.incluir(obj, false);
    }

    /**
     * Operação responsável por alterar no BD os dados de um objeto da classe <code>ProfissaoVO</code>.
     * Sempre utiliza a chave primária da classe como atributo para localização do registro a ser alterado.
     * Primeiramente valida os dados (<code>validarDados</code>) do objeto. Verifica a conexão com o banco de dados e a permissão do usuário
     * para realizar esta operacão na entidade.
     * Isto, através da operação <code>alterar</code> da superclasse.
     * @param obj    Objeto da classe <code>ProfissaoVO</code> que será alterada no banco de dados.
     * @exception Exception Caso haja problemas de conexão, restrição de acesso ou validação de dados.
     */
    public void alterar(ProfissaoVO obj, boolean centralEventos) throws Exception {
            ProfissaoVO.validarDados(obj);
            if (centralEventos) {
//            	 alterarObj(getIdEntidade());
            } else {
                alterar(getIdEntidade());
            }
            alterar(getIdEntidade());
            obj.realizarUpperCaseDados();
            String sql = "UPDATE Profissao set descricao=? WHERE ((codigo = ?))";
        try (PreparedStatement sqlAlterar = con.prepareStatement(sql)) {
            sqlAlterar.setString(1, obj.getDescricao());
            sqlAlterar.setInt(2, obj.getCodigo().intValue());
            sqlAlterar.execute();
        }
    }
    /* (non-Javadoc)
     * @see negocio.interfaces.basico.ProfissaoInterfaceFacade#incluir(negocio.comuns.basico.ProfissaoVO)
     */

    public void alterar(ProfissaoVO obj) throws Exception {
        this.alterar(obj, false);
    }

    /**
     * Operação responsável por excluir no BD um objeto da classe <code>ProfissaoVO</code>.
     * Sempre localiza o registro a ser excluído através da chave primária da entidade.
     * Primeiramente verifica a conexão com o banco de dados e a permissão do usuário
     * para realizar esta operacão na entidade.
     * Isto, através da operação <code>excluir</code> da superclasse.
     * @param obj    Objeto da classe <code>ProfissaoVO</code> que será removido no banco de dados.
     * @exception Exception Caso haja problemas de conexão ou restrição de acesso.
     */
    public void excluir(ProfissaoVO obj, boolean centralEventos) throws Exception {
        try {
            con.setAutoCommit(false);
            if (centralEventos) {
//        		excluirObj(getIdEntidade());
            } else {
                excluir(getIdEntidade());
            }
            String sql = "DELETE FROM Profissao WHERE ((codigo = ?))";
            try (PreparedStatement sqlExcluir = con.prepareStatement(sql)) {
                sqlExcluir.setInt(1, obj.getCodigo().intValue());
                sqlExcluir.execute();
            }
            con.commit();
        } catch (Exception e) {
            con.rollback();
            con.setAutoCommit(true);
            throw e;
        } finally {
            con.setAutoCommit(true);
        }
    }
    /* (non-Javadoc)
     * @see negocio.interfaces.basico.ProfissaoInterfaceFacade#incluir(negocio.comuns.basico.ProfissaoVO)
     */

    public void excluir(ProfissaoVO obj) throws Exception {
        this.excluir(obj, false);
    }

    /**
     * Responsável por realizar uma consulta de <code>Profissao</code> através do valor do atributo 
     * <code>String descricao</code>. Retorna os objetos, com início do valor do atributo idêntico ao parâmetro fornecido.
     * Faz uso da operação <code>montarDadosConsulta</code> que realiza o trabalho de prerarar o List resultante.
     * @param   controlarAcesso Indica se a aplicação deverá verificar se o usuário possui permissão para esta consulta ou não.
     * @return  List Contendo vários objetos da classe <code>ProfissaoVO</code> resultantes da consulta.
     * @exception Exception Caso haja problemas de conexão ou restrição de acesso.
     */
    public List consultarPorDescricao(String valorConsulta, boolean controlarAcesso, int nivelMontarDados) throws Exception {
        consultar(getIdEntidade(), controlarAcesso);
        String sqlStr = "SELECT * FROM Profissao WHERE upper( descricao ) like('" + valorConsulta.toUpperCase() + "%') ORDER BY descricao";
        try (Statement stm = con.createStatement()) {
            try (ResultSet tabelaResultado = stm.executeQuery(sqlStr)) {
                return (montarDadosConsulta(tabelaResultado, nivelMontarDados));
            }
        }
    }

    public int consultarCodigoPorDescricao(String valorConsulta) throws Exception {
        String sqlStr = "SELECT codigo FROM Profissao WHERE upper( descricao ) ilike('" + valorConsulta.toUpperCase() + "') ORDER BY descricao";
        try (Statement stm = con.createStatement()) {
            try (ResultSet tabelaResultado = stm.executeQuery(sqlStr)) {
                if (tabelaResultado.next()) {
                    return tabelaResultado.getInt("codigo");
                }
            }
        }
        return 0;
    }

    /**
     * Responsável por realizar uma consulta de <code>Profissao</code> através do valor do atributo 
     * <code>Integer codigo</code>. Retorna os objetos com valores iguais ou superiores ao parâmetro fornecido.
     * Faz uso da operação <code>montarDadosConsulta</code> que realiza o trabalho de prerarar o List resultante.
     * @param   controlarAcesso Indica se a aplicação deverá verificar se o usuário possui permissão para esta consulta ou não.
     * @return  List Contendo vários objetos da classe <code>ProfissaoVO</code> resultantes da consulta.
     * @exception Exception Caso haja problemas de conexão ou restrição de acesso.
     */
    public List consultarPorCodigo(Integer valorConsulta, boolean controlarAcesso, int nivelMontarDados) throws Exception {
        consultar(getIdEntidade(), controlarAcesso);
        String sqlStr = "SELECT * FROM Profissao WHERE codigo >= " + valorConsulta.intValue() + " ORDER BY codigo";
        try (Statement stm = con.createStatement()) {
            try (ResultSet tabelaResultado = stm.executeQuery(sqlStr)) {
                return (montarDadosConsulta(tabelaResultado, nivelMontarDados));
            }
        }
    }

    public ProfissaoVO consultarPorCodigoExato(Integer valorConsulta, boolean controlarAcesso, int nivelMontarDados) throws Exception {
        consultar(getIdEntidade(), controlarAcesso);
        String sql = "SELECT * FROM Profissao WHERE codigo = ?";
        ProfissaoVO profissao;
        try (PreparedStatement sqlConsultar = con.prepareStatement(sql)) {
            sqlConsultar.setInt(1, valorConsulta.intValue());
            try (ResultSet tabelaResultado = sqlConsultar.executeQuery()) {
                profissao = null;
                if (tabelaResultado.next()) {
                    profissao = montarDados(tabelaResultado, nivelMontarDados);
                }
            }
        }
        return profissao;
    }

    /**
     * Responsável por montar os dados de vários objetos, resultantes de uma consulta ao banco de dados (<code>ResultSet</code>).
     * Faz uso da operação <code>montarDados</code> que realiza o trabalho para um objeto por vez.
     * @return  List Contendo vários objetos da classe <code>ProfissaoVO</code> resultantes da consulta.
     */
    public static List montarDadosConsulta(ResultSet tabelaResultado, int nivelMontarDados) throws Exception {
        List vetResultado = new ArrayList();
        while (tabelaResultado.next()) {
            ProfissaoVO obj = new ProfissaoVO();
            obj = montarDados(tabelaResultado, nivelMontarDados);
            vetResultado.add(obj);
        }
        return vetResultado;
    }

    /**
     * Responsável por montar os dados resultantes de uma consulta ao banco de dados (<code>ResultSet</code>)
     * em um objeto da classe <code>ProfissaoVO</code>.
     * @return  O objeto da classe <code>ProfissaoVO</code> com os dados devidamente montados.
     */
    public static ProfissaoVO montarDados(ResultSet dadosSQL, int nivelMontarDados) throws Exception {
        ProfissaoVO obj = new ProfissaoVO();
        obj.setCodigo(dadosSQL.getInt("codigo"));
        obj.setDescricao(dadosSQL.getString("descricao"));
        obj.setNovoObj(false);
        if (nivelMontarDados == Uteis.NIVELMONTARDADOS_DADOSBASICOS) {
            return obj;
        }

        return obj;
    }

    /**
     * Operação responsável por localizar um objeto da classe <code>ProfissaoVO</code>
     * através de sua chave primária. 
     * @exception Exception Caso haja problemas de conexão ou localização do objeto procurado.
     */
    public ProfissaoVO consultarPorChavePrimaria(Integer codigoPrm, int nivelMontarDados) throws Exception {
        consultar(getIdEntidade(), false);
        ProfissaoVO eCache = (ProfissaoVO) obterFromCache(codigoPrm);
        if (eCache != null){
            return eCache;
        }
        String sql = "SELECT * FROM Profissao WHERE codigo = ?";
        try (PreparedStatement sqlConsultar = con.prepareStatement(sql)) {
            sqlConsultar.setInt(1, codigoPrm.intValue());
            try (ResultSet tabelaResultado = sqlConsultar.executeQuery()) {
                if (!tabelaResultado.next()) {
                    throw new ConsistirException("Dados Não Encontrados ( Profissao ).");
                }
                eCache = (montarDados(tabelaResultado, nivelMontarDados));
            }
        }
        putToCache(eCache);
        return eCache;
    }

    public String consultarJSON() throws Exception {
        StringBuilder json;
        boolean dados;
        try (ResultSet rs = getPS().executeQuery()) {
            json = new StringBuilder();
            json.append("{\"aaData\":[");
            dados = false;
            while (rs.next()) {
                dados = true;
                json.append("[\"").append(rs.getString("codigo")).append("\",");
                json.append("\"").append(Uteis.normalizarStringJSON(rs.getString("descricao"))).append("\"],");
            }
        }
        if (dados) {
            json.deleteCharAt(json.toString().length() - 1);
        }
        json.append("]}");
        return json.toString();
    }

    private PreparedStatement getPS() throws SQLException {
        String sql = "SELECT codigo, descricao FROM profissao ORDER BY descricao";
        return con.prepareStatement(sql);
    }

    public List consultarParaImpressao(String filtro, String ordem, String campoOrdenacao, int i) throws SQLException {
        List lista;
        try (ResultSet rs = getPS().executeQuery()) {
            lista = new ArrayList();
            while (rs.next()) {
                ProfissaoVO prof = new ProfissaoVO();
                String geral = rs.getInt("codigo") + rs.getString("descricao");
                if (geral.toLowerCase().contains(filtro.toLowerCase())) {
                    prof.setCodigo(rs.getInt("codigo"));
                    prof.setDescricao(rs.getString("descricao"));
                    lista.add(prof);
                }
            }
        }
        if (campoOrdenacao.equals("Código")) {
            Ordenacao.ordenarLista(lista, "codigo");
        } else if (campoOrdenacao.equals("Descrição")) {
            Ordenacao.ordenarLista(lista, "descricao");
        }
        if (ordem.contains("desc")) {
            Collections.reverse(lista);
        }
        return lista;

    }
    
    public ProfissaoVO criarOuConsultarProfissaoPorDescricao(final String descricao, int nivelMontarDados) throws Exception {
        List lista = consultarPorDescricao(descricao, false, nivelMontarDados);
        if (lista.isEmpty()) {
            ProfissaoVO profissaoVO = new ProfissaoVO();
            profissaoVO.setDescricao(descricao);
            incluir(profissaoVO, true);
            return profissaoVO;
        } else {
            return (ProfissaoVO ) lista.get(0);
        }
    }

    public int existeProfissaoInformada (String descricao) {
        String sql = "SELECT * FROM profissao WHERE upper(descricao) = '" + descricao.toUpperCase() + "'";
        try (PreparedStatement ps = con.prepareStatement(sql)) {
            try (ResultSet rs = ps.executeQuery()) {
                if (rs.next()) {
                    return rs.getInt("codigo");
                }
            }
        } catch (SQLException e) {
            throw new RuntimeException(e);
        }
        return 0;
    }
}

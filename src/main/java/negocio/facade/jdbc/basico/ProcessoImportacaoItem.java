package negocio.facade.jdbc.basico;

import negocio.comuns.basico.ProcessoImportacaoItemVO;
import negocio.comuns.basico.ProcessoImportacaoLogVO;
import negocio.comuns.basico.enumerador.TipoImportacaoEnum;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisValidacao;
import negocio.facade.jdbc.arquitetura.SuperEntidade;
import negocio.interfaces.basico.ProcessoImportacaoItemInterfaceFacade;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.util.ArrayList;
import java.util.List;

/**
 * Created by <PERSON><PERSON> on 20/12/2019.
 */
public class ProcessoImportacaoItem extends SuperEntidade implements ProcessoImportacaoItemInterfaceFacade {

    public ProcessoImportacaoItem() throws Exception {
        super();
        setIdEntidade("Empresa");
    }

    public ProcessoImportacaoItem(Connection con) throws Exception {
        super(con);
        setIdEntidade("Empresa");
    }

    @Override
    public void incluir(ProcessoImportacaoItemVO obj) throws Exception {
        ProcessoImportacaoItemVO.validarDados(obj);
        String sql = "INSERT INTO ProcessoImportacaoItem(processoImportacao, dados) VALUES (?,?)";
        int i = 0;
        PreparedStatement ps = con.prepareStatement(sql);
        ps.setInt(++i, obj.getProcessoImportacaoVO().getCodigo());
        ps.setString(++i, obj.getDados());
        ps.execute();
        obj.setCodigo(obterValorChavePrimariaCodigo());
        obj.setNovoObj(false);
    }

    @Override
    public void alterar(ProcessoImportacaoItemVO obj) throws Exception {
        ProcessoImportacaoItemVO.validarDados(obj);
        String sql = "UPDATE ProcessoImportacaoItem set processoImportacao = ?, dados = ? WHERE codigo = ?";
        PreparedStatement ps = con.prepareStatement(sql);
        int i = 0;
        ps.setInt(++i, obj.getProcessoImportacaoVO().getCodigo());
        ps.setString(++i, obj.getDados());
        ps.setInt(++i, obj.getCodigo());
        ps.execute();
    }

    @Override
    public void excluir(ProcessoImportacaoItemVO obj) throws Exception {
        String sql = "DELETE FROM ProcessoImportacaoItem WHERE codigo = ?";
        PreparedStatement ps = con.prepareStatement(sql);
        int i = 0;
        ps.setInt(++i, obj.getCodigo());
        ps.execute();
    }

    public List<ProcessoImportacaoItemVO> montarDadosConsulta(ResultSet tabelaResultado, int nivelMontarDados, Connection con) throws Exception {
        List<ProcessoImportacaoItemVO> vetResultado = new ArrayList<>();
        while (tabelaResultado.next()) {
            ProcessoImportacaoItemVO obj = montarDados(tabelaResultado, nivelMontarDados, con);
            vetResultado.add(obj);
        }
        return vetResultado;
    }

    public ProcessoImportacaoItemVO montarDados(ResultSet rs, int nivelMontarDados, Connection con) throws Exception {
        ProcessoImportacaoItemVO obj = new ProcessoImportacaoItemVO();
        obj.setCodigo(rs.getInt("codigo"));
        obj.setNovoObj(false);
        obj.getProcessoImportacaoVO().setCodigo(rs.getInt("processoImportacao"));
        obj.setDados(rs.getString("dados"));

        if (nivelMontarDados == Uteis.NIVELMONTARDADOS_DADOSBASICOS) {
            return obj;
        }
        return obj;
    }

    @Override
    public ProcessoImportacaoItemVO consultarPorChavePrimaria(Integer codigo, int nivelMontarDados) throws Exception {
        String sql = "SELECT * FROM ProcessoImportacaoItem WHERE codigo = ?";
        PreparedStatement ps = con.prepareStatement(sql);
        int i = 0;
        ps.setInt(++i, codigo);
        ResultSet rs = ps.executeQuery();
        if (!rs.next()) {
            return new ProcessoImportacaoItemVO();
        }
        return (montarDados(rs, nivelMontarDados, this.con));
    }

    public List<ProcessoImportacaoItemVO> consultarPorProcessoImportacao(Integer processoImportacao, Integer limit, Integer offSet, int nivelMontarDados) throws Exception {
        StringBuilder sql = new StringBuilder();
        sql.append("SELECT * FROM ProcessoImportacaoItem \n");
        sql.append(" WHERE processoImportacao = ").append(processoImportacao).append(" \n");
        if (!UteisValidacao.emptyNumber(limit)) {
            sql.append(" LIMIT ").append(limit).append(" \n");
        }
        if (!UteisValidacao.emptyNumber(offSet)) {
            sql.append(" OFFSET ").append(offSet).append(" \n");
        }
        sql.append("ORDER BY codigo \n");
        PreparedStatement ps = con.prepareStatement(sql.toString());
        ResultSet rs = ps.executeQuery();
        return montarDadosConsulta(rs, nivelMontarDados, this.con);
    }
}

package negocio.facade.jdbc.basico;

import negocio.comuns.arquitetura.LogVO;
import negocio.comuns.arquitetura.UsuarioVO;
import negocio.comuns.basico.ConfiguracaoReenvioMovParcelaEmpresaVO;
import negocio.comuns.basico.EmpresaVO;
import negocio.comuns.financeiro.ConvenioCobrancaVO;
import negocio.comuns.financeiro.enumerador.SituacaoConvenioCobranca;
import negocio.comuns.utilitarias.Calendario;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisValidacao;
import negocio.facade.jdbc.arquitetura.Log;
import negocio.facade.jdbc.arquitetura.SuperEntidade;
import negocio.facade.jdbc.financeiro.ConvenioCobranca;
import negocio.interfaces.basico.ConfiguracaoReenvioMovParcelaEmpresaInterfaceFacade;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.Statement;
import java.util.ArrayList;
import java.util.List;

public class ConfiguracaoReenvioMovParcelaEmpresa extends SuperEntidade implements ConfiguracaoReenvioMovParcelaEmpresaInterfaceFacade {

    public ConfiguracaoReenvioMovParcelaEmpresa() throws Exception {
        super();
        setIdEntidade("Empresa");
    }

    public ConfiguracaoReenvioMovParcelaEmpresa(Connection con) throws Exception {
        super(con);
        setIdEntidade("Empresa");
    }


    public ConfiguracaoReenvioMovParcelaEmpresaVO novo() throws Exception {
        return new ConfiguracaoReenvioMovParcelaEmpresaVO();
    }

    public void incluir(ConfiguracaoReenvioMovParcelaEmpresaVO obj) throws Exception {
        try {
            String sql = "INSERT INTO ConfiguracaoReenvioMovParcelaEmpresa(empresa, posicao, conveniocobranca, tentativasRealizar) VALUES (?, ?, ?, ?)";
            try (PreparedStatement sqlInserir = con.prepareStatement(sql)) {
                sqlInserir.setInt(1, obj.getEmpresaVO().getCodigo());
                sqlInserir.setInt(2, obj.getPosicao());
                sqlInserir.setInt(3, obj.getConvenioCobrancaVO().getCodigo());
                sqlInserir.setInt(4, obj.getTentativasRealizar());
                sqlInserir.execute();
            }
            obj.setCodigo(obterValorChavePrimariaCodigo());
            obj.setNovoObj(false);
        } catch (Exception e) {
            System.out.println(e.getMessage());
            throw e;
        }
    }


    public void alterar(ConfiguracaoReenvioMovParcelaEmpresaVO obj) throws Exception {
        String sql = "UPDATE ConfiguracaoReenvioMovParcelaEmpresa set empresa = ?, posicao = ?, conveniocobranca = ?, tentativasRealizar = ? WHERE codigo = ?";
        try (PreparedStatement sqlAlterar = con.prepareStatement(sql)) {
            sqlAlterar.setInt(1, obj.getEmpresaVO().getCodigo());
            sqlAlterar.setInt(2, obj.getPosicao());
            sqlAlterar.setInt(3, obj.getConvenioCobrancaVO().getCodigo());
            sqlAlterar.setInt(4, obj.getTentativasRealizar());
            sqlAlterar.setInt(5, obj.getCodigo());
            sqlAlterar.execute();
        }
    }

    public void excluir(ConfiguracaoReenvioMovParcelaEmpresaVO obj) throws Exception {
        String sql = "DELETE FROM ConfiguracaoReenvioMovParcelaEmpresa WHERE codigo = ?";
        try (PreparedStatement sqlExcluir = con.prepareStatement(sql)) {
            sqlExcluir.setInt(1, obj.getCodigo());
            sqlExcluir.execute();
        }
    }

    private List<ConfiguracaoReenvioMovParcelaEmpresaVO> montarDadosConsulta(ResultSet tabelaResultado, int nivelMontarDados, Connection con) throws Exception {
        List<ConfiguracaoReenvioMovParcelaEmpresaVO> lista = new ArrayList<>();
        while (tabelaResultado.next()) {
            lista.add(montarDados(tabelaResultado, nivelMontarDados, con));
        }
        return lista;
    }

    private ConfiguracaoReenvioMovParcelaEmpresaVO montarDados(ResultSet dadosSQL, int nivelMontarDados, Connection con) throws Exception {
        ConfiguracaoReenvioMovParcelaEmpresaVO obj = new ConfiguracaoReenvioMovParcelaEmpresaVO();
        obj.setNovoObj(false);
        obj.setCodigo(dadosSQL.getInt("codigo"));
        obj.getEmpresaVO().setCodigo(dadosSQL.getInt("empresa"));
        obj.setPosicao(dadosSQL.getInt("posicao"));
        obj.setTentativasRealizar(dadosSQL.getInt("tentativasRealizar"));

        ConvenioCobranca convenioCobranca = new ConvenioCobranca(con);
        obj.setConvenioCobrancaVO(convenioCobranca.consultarPorCodigoSemInfoEmpresa(dadosSQL.getInt("conveniocobranca"), Uteis.NIVELMONTARDADOS_DADOSBASICOS));
        obj.getConvenioCobrancaVO().setEmpresa(obj.getEmpresaVO());
        convenioCobranca = null;

        if (nivelMontarDados == Uteis.NIVELMONTARDADOS_DADOSBASICOS) {
            return obj;
        }
        return obj;
    }

    public void excluirConfiguracaoReenvioMovParcelaEmpresa(Integer empresa) throws Exception {
        excluir(getIdEntidade());
        String sql = "DELETE FROM ConfiguracaoReenvioMovParcelaEmpresa WHERE empresa = ?";
        try (PreparedStatement sqlExcluir = con.prepareStatement(sql)) {
            sqlExcluir.setInt(1, empresa);
            sqlExcluir.execute();
        }
    }

    public void alterarConfiguracaoReenvioMovParcelaEmpresa(Integer empresa, List<ConfiguracaoReenvioMovParcelaEmpresaVO> objetos) throws Exception {
        String codigoAtuais = "";
        for (ConfiguracaoReenvioMovParcelaEmpresaVO conta : objetos) {
            conta.getEmpresaVO().setCodigo(empresa);
            if (UteisValidacao.emptyNumber(conta.getCodigo())) {
                incluir(conta);
            } else {
                alterar(conta);
            }
            codigoAtuais += "," + conta.getCodigo();
        }
        excluirConfiguracaoReenvioMovParcelaEmpresaCodigosAtuais(empresa, codigoAtuais.replaceFirst(",", ""));
    }

    public void excluirConfiguracaoReenvioMovParcelaEmpresaCodigosAtuais(final Integer codEmpresa, final String codigosAtuais) throws Exception {
        String sql = "DELETE FROM ConfiguracaoReenvioMovParcelaEmpresa WHERE empresa = ? ";
        if (!UteisValidacao.emptyString(codigosAtuais)) {
            sql += " AND codigo not in (" + codigosAtuais + ")";
        }
        try (PreparedStatement sqlExcluir = con.prepareStatement(sql)) {
            sqlExcluir.setInt(1, codEmpresa);
            sqlExcluir.execute();
        }
    }

    public void incluirConfiguracaoReenvioMovParcelaEmpresa(Integer empresaPrm, List<ConfiguracaoReenvioMovParcelaEmpresaVO> objetos) throws Exception {
        for (ConfiguracaoReenvioMovParcelaEmpresaVO obj : objetos) {
            obj.getEmpresaVO().setCodigo(empresaPrm);
            incluir(obj);
        }
    }

    public List<ConfiguracaoReenvioMovParcelaEmpresaVO> consultarConfiguracaoReenvioMovParcelaEmpresa(Integer empresa, int nivelMontarDados) throws Exception {
        List<ConfiguracaoReenvioMovParcelaEmpresaVO> objetos = new ArrayList<ConfiguracaoReenvioMovParcelaEmpresaVO>();
        String sql = "SELECT * FROM ConfiguracaoReenvioMovParcelaEmpresa WHERE empresa = ? ORDER BY posicao";
        try (PreparedStatement sqlConsulta = con.prepareStatement(sql)) {
            sqlConsulta.setInt(1, empresa);
            try (ResultSet resultado = sqlConsulta.executeQuery()) {
                while (resultado.next()) {
                    ConfiguracaoReenvioMovParcelaEmpresaVO novoObj = montarDados(resultado, nivelMontarDados, this.con);
                    objetos.add(novoObj);
                }
            }
        }
        return objetos;
    }

    public ConfiguracaoReenvioMovParcelaEmpresaVO consultarPorChavePrimaria(Integer codigo, int nivelMontarDados) throws Exception {
        String sql = "SELECT * FROM ConfiguracaoReenvioMovParcelaEmpresa WHERE codigo = ?";
        try (PreparedStatement sqlConsultar = con.prepareStatement(sql)) {
            sqlConsultar.setInt(1, codigo);
            try (ResultSet tabelaResultado = sqlConsultar.executeQuery()) {
                if (!tabelaResultado.next()) {
                    return new ConfiguracaoReenvioMovParcelaEmpresaVO();
                }
                return (montarDados(tabelaResultado, nivelMontarDados, this.con));
            }
        }
    }

    public List<ConvenioCobrancaVO> consultarConvenioReenvioAutomatico(Integer empresa, int nivelMontarDados, final Integer[] tipos, SituacaoConvenioCobranca situacao) throws Exception {
        ConvenioCobranca convenioCobrancaDAO;
        try {
            convenioCobrancaDAO = new ConvenioCobranca(con);

            StringBuilder sql = new StringBuilder();
            sql.append("SELECT \n");
            sql.append("cr.* \n");
            sql.append("FROM configuracaoreenviomovparcelaempresa cr \n");
            sql.append("INNER JOIN conveniocobranca  cc on cc.codigo = cr.conveniocobranca \n");
            sql.append("WHERE cr.empresa = ").append(empresa).append(" \n");
            sql.append("AND cc.situacao = ").append(situacao.getCodigo()).append(" \n");
            if (tipos != null) {
                sql.append("AND cc.tipoconvenio in (").append(Uteis.splitFromArray(tipos, false)).append(") \n");
            }
            sql.append("ORDER BY cr.posicao");

            List<ConfiguracaoReenvioMovParcelaEmpresaVO> lista;
            try (Statement stm = con.createStatement()) {
                try (ResultSet tabelaResultado = stm.executeQuery(sql.toString())) {
                    lista = (montarDadosConsulta(tabelaResultado, nivelMontarDados, con));
                }
            }

            List<ConfiguracaoReenvioMovParcelaEmpresaVO> listaFinal = new ArrayList<ConfiguracaoReenvioMovParcelaEmpresaVO>();
            for (ConfiguracaoReenvioMovParcelaEmpresaVO config : lista) {
                Integer nrVezes = config.getTentativasRealizar();
                int i = 0;
                while (i < nrVezes) {
                    config.setTentativasRealizar(1);
                    listaFinal.add(config);
                    i++;
                }
            }

            int nrInicial = 0;
            int nrFinal = 0;
            int i = 0;
            List<ConvenioCobrancaVO> retorno = new ArrayList<ConvenioCobrancaVO>();
            for (ConfiguracaoReenvioMovParcelaEmpresaVO config : listaFinal) {
                config.setConvenioCobrancaVO(convenioCobrancaDAO.consultarPorCodigoEmpresa(config.getConvenioCobrancaVO().getCodigo(), empresa, Uteis.NIVELMONTARDADOS_TODOS));
                if (i == 0) {
                    nrFinal += (config.getTentativasRealizar() - 1);
                    config.getConvenioCobrancaVO().setSequenciaNrParcelaInicio(nrInicial);
                    config.getConvenioCobrancaVO().setSequenciaNrParcelaFim(nrFinal);
                } else {
                    nrInicial = (nrFinal + 1);
                    nrFinal += config.getTentativasRealizar();
                    config.getConvenioCobrancaVO().setSequenciaNrParcelaInicio(nrInicial);
                    config.getConvenioCobrancaVO().setSequenciaNrParcelaFim(nrFinal);
                }
                if (listaFinal.size() == (i + 1)) {
                    config.getConvenioCobrancaVO().setSequenciaNrParcelaFim(999);
                }
                retorno.add(config.getConvenioCobrancaVO());
                i++;
            }

            return retorno;
        } finally {
            convenioCobrancaDAO = null;
        }
    }

    public boolean existeConfiguracaoReenvioMovParcelaEmpresa(Integer empresa, Integer convenioCobranca) throws Exception {
        StringBuilder sqlStr = new StringBuilder("select exists ( \n");
        sqlStr.append("SELECT ce.codigo \n");
        sqlStr.append("FROM ConfiguracaoReenvioMovParcelaEmpresa ce \n");
        sqlStr.append("INNER JOIN empresa e on e.codigo = ce.empresa \n");
        sqlStr.append("WHERE e.habilitarreenvioautomaticoremessa \n");
        if(!UteisValidacao.emptyNumber(empresa)){
            sqlStr.append(" AND ce.empresa = ").append(empresa).append(" \n");
        }
        if(!UteisValidacao.emptyNumber(convenioCobranca)){
            sqlStr.append(" AND ce.convenioCobranca = ").append(convenioCobranca).append(" \n");
        }
        sqlStr.append(") as existe");
        try (PreparedStatement stm = con.prepareStatement(sqlStr.toString())) {
            try (ResultSet tabelaResultado = stm.executeQuery()) {
                tabelaResultado.next();
                return tabelaResultado.getBoolean("existe");
            }
        }
    }

    public void gravarLogConfiguracaoRetentativa(EmpresaVO empresaVO, UsuarioVO usuarioVO,
                                                 List<ConfiguracaoReenvioMovParcelaEmpresaVO> listaAnterior,
                                                 List<ConfiguracaoReenvioMovParcelaEmpresaVO> listaAtual) throws Exception {
        Log logDAO;
        try {
            logDAO = new Log(con);

            StringBuilder atual = obterLogListaConfiguracaoReenvioMovParcelaEmpresaVO(listaAtual);
            StringBuilder anterior = obterLogListaConfiguracaoReenvioMovParcelaEmpresaVO(listaAnterior);

            LogVO logVO = new LogVO();
            logVO.setOperacao("ALTERAÇÃO");
            logVO.setChavePrimaria(String.valueOf(empresaVO.getCodigo()));
            logVO.setNomeEntidade("EMPRESA");
            logVO.setNomeEntidadeDescricao("EMPRESA - ConfiguracaoReenvioMovParcelaEmpresaVO");
            logVO.setResponsavelAlteracao(usuarioVO.getNome());
            logVO.setUserOAMD(usuarioVO.getUserOamd());
            logVO.setNomeCampo("Retentativa Automática Convênio de Cobrança");
            logVO.setDataAlteracao(Calendario.hoje());
            logVO.setPessoa(0);
            logVO.setValorCampoAnterior(anterior.toString());
            logVO.setValorCampoAlterado(atual.toString());
            logDAO.incluirSemCommit(logVO);
        } finally {
            logDAO = null;
        }
    }

    private StringBuilder obterLogListaConfiguracaoReenvioMovParcelaEmpresaVO(List<ConfiguracaoReenvioMovParcelaEmpresaVO> lista) {
        StringBuilder log = new StringBuilder();
        if (UteisValidacao.emptyList(lista)) {
            return log;
        }
        for (ConfiguracaoReenvioMovParcelaEmpresaVO obj : lista) {
            log.append("Posição: ").append(obj.getPosicao()).append(" \n");
            log.append("Convênio: ").append(obj.getConvenioCobrancaVO().getCodigo()).append(" - ").append(obj.getConvenioCobrancaVO().getDescricao()).append(" \n\n");
        }
        return log;
    }

    public List<Integer> consultarApenasCodigoConveniosConfiguracaoReenvioMovParcelaEmpresa(Integer empresa) throws Exception {
        List<Integer> codigoConvenio = new ArrayList<Integer>();
        String sql = "SELECT conveniocobranca FROM ConfiguracaoReenvioMovParcelaEmpresa WHERE empresa = ? ORDER BY posicao";
        try (PreparedStatement sqlConsulta = con.prepareStatement(sql)) {
            sqlConsulta.setInt(1, empresa);
            try (ResultSet resultado = sqlConsulta.executeQuery()) {
                while (resultado.next()) {
                    codigoConvenio.add(resultado.getInt("conveniocobranca"));
                }
            }
        }
        return codigoConvenio;
    }

}

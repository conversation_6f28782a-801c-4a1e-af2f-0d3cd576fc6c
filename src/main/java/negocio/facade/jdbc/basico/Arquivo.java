package negocio.facade.jdbc.basico;

import negocio.comuns.basico.ArquivoVO;
import negocio.comuns.utilitarias.Calendario;
import negocio.comuns.utilitarias.Uteis;
import negocio.facade.jdbc.arquitetura.SuperEntidade;
import negocio.interfaces.basico.ArquivoInterfaceFacade;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.Statement;

/**
 * Created by glauco on 05/06/2014.
 */
public class Arquivo extends SuperEntidade implements ArquivoInterfaceFacade {
    public Arquivo() throws Exception {
    }

    public Arquivo(Connection conexao) throws Exception {
        super(conexao);
    }

    public static ArquivoVO montarDados(ResultSet dadosSQL, int nivelMontarDados) throws Exception {
        ArquivoVO obj = new ArquivoVO();
        obj.setCodigo(dadosSQL.getInt("codigo"));
        obj.getPessoa().setCodigo(dadosSQL.getInt("pessoa"));
        obj.setTipo(dadosSQL.getString("tipo"));
        obj.setExtensao(dadosSQL.getString("extensao"));
        try {
            obj.setDataRegistro(dadosSQL.getTimestamp("dataregistro"));
        } catch (Exception ignored) {}
        obj.setNovoObj(false);

        return obj;
    }

    public ArquivoVO consultarPorCodigo(Integer codArquivo, int nivelMontarDados) throws Exception {
        String sqlStr = "SELECT a.* FROM arquivo a\n" +
                "WHERE a.codigo = " + codArquivo + "\n;";

        Statement stm = con.createStatement();
        ResultSet tabelaResultado = stm.executeQuery(sqlStr);
        if (!tabelaResultado.next()) {
            return new ArquivoVO();
        }
        return montarDados(tabelaResultado, nivelMontarDados);
    }

    public void incluir(ArquivoVO obj) throws Exception {
        String sql = "INSERT INTO arquivo(tipo, extensao, pessoa, dataregistro) VALUES (?, ?, ?, ?)";
        PreparedStatement sqlInserir = con.prepareStatement(sql);
        int i = 0;
        sqlInserir.setString(++i, obj.getTipo());
        sqlInserir.setString(++i, obj.getExtensao());
        sqlInserir.setInt(++i, obj.getPessoa().getCodigo());
        sqlInserir.setTimestamp(++i, Uteis.getDataJDBCTimestamp(Calendario.hoje()));
        sqlInserir.execute();
        obj.setCodigo(obterValorChavePrimariaCodigo());
        obj.setNovoObj(false);
    }
}

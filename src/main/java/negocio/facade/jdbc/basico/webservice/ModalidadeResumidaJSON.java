package negocio.facade.jdbc.basico.webservice;

import java.util.ArrayList;
import java.util.List;

public class ModalidadeResumidaJSON {

    private int codigoModalidade = 0;
    private String modalidade;
    private Double valorModalidade;
    private Double valorFinalModalidade;
    private String centroDeCustos;
    private List<TurmaResumidaJSON> turmas = new ArrayList<TurmaResumidaJSON>();

    public List<TurmaResumidaJSON> getTurmas() {
        return turmas;
    }

    public void setTurmas(List<TurmaResumidaJSON> turmas) {
        this.turmas = turmas;
    }

    public int getCodigoModalidade() {
        return codigoModalidade;
    }

    public void setCodigoModalidade(int codigoModalidade) {
        this.codigoModalidade = codigoModalidade;
    }

    public String getModalidade() {
        return modalidade;
    }

    public void setModalidade(String modalidade) {
        this.modalidade = modalidade;
    }

    public Double getValorModalidade() {
        return valorModalidade;
    }

    public void setValorModalidade(Double valorModalidade) {
        this.valorModalidade = valorModalidade;
    }

    public Double getValorFinalModalidade() {
        return valorFinalModalidade;
    }

    public void setValorFinalModalidade(Double valorFinalModalidade) {
        this.valorFinalModalidade = valorFinalModalidade;
    }

    public String getCentroDeCustos() {
        return centroDeCustos;
    }

    public void setCentroDeCustos(String centroDeCustos) {
        this.centroDeCustos = centroDeCustos;
    }
}
package negocio.facade.jdbc.basico.webservice;

import br.com.pactosolucoes.comuns.json.SuperJSON;

import java.util.List;

public class VendaAvulsaJSON extends SuperJSON {
    private int codigo;
    private int codigoProduto;
    private String produto;
    private String centroDeCustos;
    private String dataRegistro;
    private Double valorDesconto;
    private int codConsultor;
    private String consultor;
    private String unidade;
    private Integer unidadeCodigo;
    private Double totalFinal;
    private Integer qtdParcela;
    private List<VendaAvulsaParcelaJSON> vendaAvulsaParcela;
    private ClienteResumidoJSON aluno;


    public int getCodigo() {
        return codigo;
    }

    public void setCodigo(int codigo) {
        this.codigo = codigo;
    }

    public int getCodigoProduto() {
        return codigoProduto;
    }

    public void setCodigoProduto(int codigoProduto) {
        this.codigoProduto = codigoProduto;
    }

    public String getProduto() {
        return produto;
    }

    public void setProduto(String produto) {
        this.produto = produto;
    }

    public String getDataRegistro() {
        return dataRegistro;
    }

    public void setDataRegistro(String dataRegistro) {
        this.dataRegistro = dataRegistro;
    }

    public Double getValorDesconto() {
        return valorDesconto;
    }

    public void setValorDesconto(Double valorDesconto) {
        this.valorDesconto = valorDesconto;
    }

    public int getCodConsultor() {
        return codConsultor;
    }

    public void setCodConsultor(int codConsultor) {
        this.codConsultor = codConsultor;
    }

    public String getConsultor() {
        return consultor;
    }

    public void setConsultor(String consultor) {
        this.consultor = consultor;
    }

    public String getUnidade() {
        return unidade;
    }

    public void setUnidade(String unidade) {
        this.unidade = unidade;
    }

    public Double getTotalFinal() {
        return totalFinal;
    }

    public void setTotalFinal(Double totalFinal) {
        this.totalFinal = totalFinal;
    }

    public Integer getQtdParcela() {
        return qtdParcela;
    }

    public void setQtdParcela(Integer qtdParcela) {
        this.qtdParcela = qtdParcela;
    }

    public List<VendaAvulsaParcelaJSON> getVendaAvulsaParcela() {
        return vendaAvulsaParcela;
    }

    public void setVendaAvulsaParcela(List<VendaAvulsaParcelaJSON> vendaAvulsaParcela) {
        this.vendaAvulsaParcela = vendaAvulsaParcela;
    }

    public void setAluno(ClienteResumidoJSON aluno) {
        this.aluno = aluno;
    }

    public ClienteResumidoJSON getAluno() {
        return aluno;
    }

    public Integer getUnidadeCodigo() {
        return unidadeCodigo;
    }

    public void setUnidadeCodigo(Integer unidadeCodigo) {
        this.unidadeCodigo = unidadeCodigo;
    }

    public String getCentroDeCustos() {
        return centroDeCustos;
    }

    public void setCentroDeCustos(String centroDeCustos) {
        this.centroDeCustos = centroDeCustos;
    }
}

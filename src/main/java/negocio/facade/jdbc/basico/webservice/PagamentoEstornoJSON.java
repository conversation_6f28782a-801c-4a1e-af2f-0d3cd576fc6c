package negocio.facade.jdbc.basico.webservice;

import br.com.pactosolucoes.comuns.json.SuperJSON;

import java.util.List;

public class PagamentoEstornoJSON extends SuperJSON {
    private String dataPagamento;
    private String formaPagamento;
    private List<ParcelaEstornoJSON> parcelaEstorno;
    private List<ReciboEstornoJSON> reciboEstorno;

    public String getDataPagamento() {
        return dataPagamento;
    }

    public void setDataPagamento(String dataPagamento) {
        this.dataPagamento = dataPagamento;
    }

    public String getFormaPagamento() {
        return formaPagamento;
    }

    public void setFormaPagamento(String formaPagamento) {
        this.formaPagamento = formaPagamento;
    }

    public List<ParcelaEstornoJSON> getParcelaEstorno() {
        return parcelaEstorno;
    }

    public void setParcelaEstorno(List<ParcelaEstornoJSON> parcelaEstorno) {
        this.parcelaEstorno = parcelaEstorno;
    }

    public List<ReciboEstornoJSON> getReciboEstorno() {
        return reciboEstorno;
    }

    public void setReciboEstorno(List<ReciboEstornoJSON> reciboEstorno) {
        this.reciboEstorno = reciboEstorno;
    }
}
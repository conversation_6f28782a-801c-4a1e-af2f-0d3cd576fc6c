package negocio.facade.jdbc.basico.webservice;

import com.fasterxml.jackson.databind.ObjectMapper;
import negocio.comuns.arquitetura.UsuarioVO;

public class UsuarioJSON {

    private String username;
    private String nome;
    private String senha;
    private String email;

    @Override
    public String toString() {
        try {
            ObjectMapper mapper = new ObjectMapper();
            return mapper.writeValueAsString(this);
        }catch (Exception e){
            return "erro: "+e.getMessage();
        }
    }

    public UsuarioJSON(UsuarioVO usuario) {
        this.username = usuario.getUsername();
        this.senha = usuario.getSenha();
        this.nome = usuario.getNome();
        this.email = usuario.getEmail();
    }

    public String getNome() {
        return nome;
    }

    public void setNome(String nome) {
        this.nome = nome;
    }

    public String getUsername() {
        return username;
    }

    public void setUsername(String username) {
        this.username = username;
    }

    public String getSenha() {
        return senha;
    }

    public void setSenha(String senha) {
        this.senha = senha;
    }

    public String getEmail() {
        return email;
    }

    public void setEmail(String email) {
        this.email = email;
    }
}

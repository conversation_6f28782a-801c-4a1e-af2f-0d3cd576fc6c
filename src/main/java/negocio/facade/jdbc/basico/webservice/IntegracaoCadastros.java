package negocio.facade.jdbc.basico.webservice;

import br.com.pactosolucoes.autorizacaocobranca.dao.AutorizacaoCobrancaCliente;
import br.com.pactosolucoes.autorizacaocobranca.interfaces.AutorizacaoCobrancaClienteInterfaceFacade;
import br.com.pactosolucoes.comuns.util.Formatador;
import br.com.pactosolucoes.enumeradores.*;
import br.com.pactosolucoes.integracao.log.LogJSON;
import controle.arquitetura.SuperControle;
import controle.modulos.integracao.UsuarioMovelControle;
import negocio.comuns.arquitetura.LogVO;
import negocio.comuns.arquitetura.UsuarioVO;
import negocio.comuns.basico.CidadeVO;
import negocio.comuns.basico.ClienteObservacaoVO;
import negocio.comuns.basico.ClienteVO;
import negocio.comuns.basico.ClienteValidacaoWS;
import negocio.comuns.basico.ColaboradorVO;
import negocio.comuns.basico.ConfiguracaoSistemaVO;
import negocio.comuns.basico.EmailVO;
import negocio.comuns.basico.EmpresaVO;
import negocio.comuns.basico.EnderecoVO;
import negocio.comuns.basico.EstadoVO;
import negocio.comuns.basico.FrequenciaAlunoWS;
import negocio.comuns.basico.PaisVO;
import negocio.comuns.basico.PerguntaClienteVO;
import negocio.comuns.basico.PessoaVO;
import negocio.comuns.basico.QuestionarioClienteVO;
import negocio.comuns.basico.QuestionarioPerguntaClienteVO;
import negocio.comuns.basico.QuestionarioPerguntaVO;
import negocio.comuns.basico.QuestionarioVO;
import negocio.comuns.basico.RespostaPergClienteVO;
import negocio.comuns.basico.RespostaPerguntaVO;
import negocio.comuns.basico.TelefoneVO;
import negocio.comuns.basico.TipoColaboradorVO;
import negocio.comuns.basico.VinculoVO;
import negocio.comuns.basico.enumerador.TipoBVEnum;
import negocio.comuns.basico.enumerador.TipoPessoaEnum;
import negocio.comuns.contrato.ContratoVO;
import negocio.comuns.crm.ConfiguracaoSistemaCRMVO;
import negocio.comuns.crm.ModeloMensagemVO;
import negocio.comuns.financeiro.MovParcelaVO;
import negocio.comuns.utilitarias.Calendario;
import negocio.comuns.utilitarias.ConsistirException;
import negocio.comuns.utilitarias.Ordenacao;
import negocio.comuns.utilitarias.SinteticoException;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisEmail;
import negocio.comuns.utilitarias.UteisValidacao;
import negocio.comuns.utilitarias.UtilReflection;
import negocio.facade.jdbc.arquitetura.Log;
import negocio.facade.jdbc.arquitetura.SuperEntidade;
import negocio.facade.jdbc.arquitetura.Usuario;
import negocio.facade.jdbc.arquitetura.ZillyonWebFacade;
import negocio.facade.jdbc.basico.Cidade;
import negocio.facade.jdbc.basico.Cliente;
import negocio.facade.jdbc.basico.ClienteObservacao;
import negocio.facade.jdbc.basico.Colaborador;
import negocio.facade.jdbc.basico.ConfiguracaoSistema;
import negocio.facade.jdbc.basico.Email;
import negocio.facade.jdbc.basico.Empresa;
import negocio.facade.jdbc.basico.Estado;
import negocio.facade.jdbc.basico.Pessoa;
import negocio.facade.jdbc.basico.Questionario;
import negocio.facade.jdbc.basico.QuestionarioCliente;
import negocio.facade.jdbc.contrato.Contrato;
import negocio.facade.jdbc.crm.ModeloMensagem;
import negocio.facade.jdbc.financeiro.MovParcela;
import negocio.interfaces.basico.CidadeInterfaceFacade;
import negocio.interfaces.basico.ClienteInterfaceFacade;
import negocio.interfaces.basico.ColaboradorInterfaceFacade;
import negocio.interfaces.basico.EstadoInterfaceFacade;
import negocio.interfaces.basico.PessoaInterfaceFacade;
import negocio.modulos.integracao.usuariomovel.UsuarioMovel;
import negocio.modulos.integracao.usuariomovel.UsuarioMovelInterfaceFacade;
import negocio.modulos.integracao.usuariomovel.UsuarioMovelVO;
import negocio.oamd.CupomDescontoWS;
import org.apache.commons.lang.StringUtils;
import org.json.JSONArray;
import org.json.JSONObject;
import relatorio.negocio.comuns.sad.SituacaoClienteSinteticoDWVO;
import relatorio.negocio.comuns.sad.SituacaoClienteSinteticoEnum;
import relatorio.negocio.jdbc.sad.SituacaoClienteSinteticoDW;
import servicos.http.MetodoHttpEnum;
import servicos.http.RequestHttpService;
import servicos.http.RespostaHttpDTO;
import servicos.impl.oamd.OAMDService;
import servicos.integracao.TreinoWSConsumer;
import servicos.operacoes.midias.MidiaService;
import servicos.operacoes.midias.commons.MidiaEntidadeEnum;
import servicos.propriedades.PropsService;

import java.sql.Connection;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.logging.Level;
import java.util.logging.Logger;

import static org.apache.commons.lang3.StringUtils.isNotBlank;

/**
 * Created by glauco on 27/11/2014
 */
public class IntegracaoCadastros extends SuperEntidade {



    public IntegracaoCadastros() throws Exception {
    }

    public IntegracaoCadastros(Connection conexao) throws Exception {
        super(conexao);
    }

    public String alterarDadosPessoaisClienteSite(final int codigoCliente,
                                                  final String endereco,
                                                  final String complemento,
                                                  final String numero,
                                                  final String bairro,
                                                  final String cep,
                                                  final Integer codigoCidade,
                                                  final Integer codigoEstado,
                                                  final String telCelular,
                                                  final String telResidencial) throws Exception {
        ClienteInterfaceFacade clienteDao = new Cliente(con);
        PessoaInterfaceFacade pessoaDao = new Pessoa(con);
        ConfiguracaoSistema configuracaoSistema = new ConfiguracaoSistema(con);
        con.setAutoCommit(false);
        try{

            ClienteVO clienteVO = clienteDao.consultarPorCodigo(codigoCliente,false,Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA);
            if (!UtilReflection.objetoMaiorQueZero(clienteVO, "getCodigo()")){
                return "NÃO FOI ENCONTRADO CLIENTE COM O CODIGO:" + codigoCliente;
            }
            pessoaDao.alterarPessoaSiteSemCommit(clienteVO.getPessoa().getCodigo(), endereco, complemento, numero, bairro, cep,codigoCidade,codigoEstado, telCelular, telResidencial, con);

            con.commit();
        }catch (Exception ex){
            con.rollback();
            return "ERRO: " + ex.getMessage();
        }finally {
            con.setAutoCommit(true);
            clienteDao = null;
            pessoaDao = null;
            configuracaoSistema = null;
        }
        return "SUCESSO";
    }

    public String alterarSenhaUsuarioMovel(String email, final String senha, String lembreteSenha)throws Exception{
        UsuarioMovelInterfaceFacade usuarioMovel = new UsuarioMovel(con);
        try{
            UsuarioMovelVO usuarioMovelVO = usuarioMovel.consultarPorNome(email,Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA);
            if (usuarioMovelVO == null) {
                throw new ConsistirException("ERRO: Operação não realizada, pois não foi possível encontrar o usuário com o email " + email);
            }
            usuarioMovel.alterarSenha(usuarioMovelVO.getCodigo(),senha,lembreteSenha);
        }catch (Exception ex){
            return "ERRO: " + ex.getMessage();
        }finally {
            usuarioMovel = null;
        }
        return "SUCESSO";
    }

    public String consultarEstado()throws Exception{
        EstadoInterfaceFacade estadoDao = new Estado(con);
        try{
            List<EstadoVO> listaEstado = estadoDao.consultarPorDescricao("", false, Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA);
            JSONArray jsonArray = new JSONArray();
            for (EstadoVO estadoVO : listaEstado) {
                JSONObject jsonObject = new JSONObject(estadoVO.toWS());
                jsonArray.put(jsonObject);
            }

            return jsonArray.toString();

        }catch (Exception ex){
            return "ERRO: " + ex.getMessage();
        }finally {
            estadoDao = null;
        }
    }

    public String consultarCidade(int codigoEstado)throws Exception{
        CidadeInterfaceFacade cidadeDao = new Cidade(con);
        try{
            List<CidadeVO> listaCidade = cidadeDao.consultarPorCodigoEstado(codigoEstado,Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA);
            JSONArray jsonArray = new JSONArray();
            for (CidadeVO cidadeVO : listaCidade) {
                JSONObject jsonObject = new JSONObject(cidadeVO.toWS());
                jsonArray.put(jsonObject);
            }
            return jsonArray.toString();
        }catch (Exception ex){
            return "ERRO: " + ex.getMessage();
        }finally {
            cidadeDao = null;
        }
    }

    public String consultarCupomDesconto(String key, String listaCupom){
        try{
            OAMDService oamdService = new OAMDService();
            try{
                List<CupomDescontoWS> listaCupomBD = oamdService.consultarCupomDesconto(key, listaCupom);
                JSONArray jsonArray = new JSONArray();
                for (CupomDescontoWS cupomDescontoWS : listaCupomBD) {
                    JSONObject jsonObject = new JSONObject(cupomDescontoWS);
                    jsonArray.put(jsonObject);
                }
                return jsonArray.toString();
            }catch (Exception ex){
                throw ex;
            }finally {
                oamdService = null;
            }
        }catch (Exception ex){
            Logger.getLogger( getClass().getName()).log(Level.SEVERE, null, "Erro ao executar metodo IntegracaoCadastros.consultarCupomDesconto. Erro:" + ex.getMessage());
        }
        return  "{[]}";
    }

    public String consultarClienteNaRedeEmpresa(String chaveZW, String cpf)throws Exception{
        try{
            OAMDService oamdService = new OAMDService();
            try{
                ClienteValidacaoWS clienteValidacaoWS = oamdService.consultarClienteNaRedeEmpresa(chaveZW, cpf);
                JSONObject jsonObject = new JSONObject(clienteValidacaoWS);
                return jsonObject.toString();
            }catch (Exception ex){
                throw ex;
            }finally {
                oamdService = null;
            }
        }catch (Exception ex){
            Logger.getLogger( getClass().getName()).log(Level.SEVERE, null, "Erro ao executar metodo IntegracaoCadastros.consultarClienteNaRedeEmpresa. Erro:" + ex.getMessage());
        }
        return  "{}";
    }

    public String consultarFrequenciaAluno(Integer codigoCliente)throws Exception{
        SituacaoClienteSinteticoDW situacaoClienteSinteticoDW = new SituacaoClienteSinteticoDW(con);
        try{
            FrequenciaAlunoWS frequenciaAlunoWS = situacaoClienteSinteticoDW.consultarFrequenciaAluno(codigoCliente);
            JSONObject jsonObject = new JSONObject(frequenciaAlunoWS);
            return jsonObject.toString();

        }catch (Exception ex){
            return "ERRO: " + ex.getMessage();
        }finally {
            situacaoClienteSinteticoDW = null;
        }

    }
    private ClienteVO validarSobreporVisitanteExistente(List pessoas,List emails,String dataNascimento,String email) throws Exception{
        if(!UteisValidacao.emptyList(pessoas)){
            PessoaVO pessoa = (PessoaVO) pessoas.get(0);
            Cliente clienteDao = new Cliente(con);
            Contrato contratoDao = new Contrato(con);
            ClienteVO clienteVO = clienteDao.consultarPorCodigoPessoa(pessoa.getCodigo(),Uteis.NIVELMONTARDADOS_DADOSBASICOS);
            ContratoVO contrato = contratoDao.consultarContratoVigentePorPessoa(pessoa.getCodigo(),false,false,Uteis.NIVELMONTARDADOS_DADOSBASICOS);
            contratoDao = null;

            if(clienteVO.getSituacao().equals(SituacaoClienteEnum.VISITANTE.getCodigo()) && UteisValidacao.emptyNumber(contrato.getCodigo())){
                if(!UteisValidacao.emptyString(dataNascimento) && Calendario.getDataComHoraZerada(pessoa.getDataNasc()).equals(Calendario.getDataComHoraZerada(Uteis.getDate(dataNascimento)))){
                    for(Object o : emails){
                        EmailVO emailVO =  (EmailVO)o;
                        if(emailVO.getEmail().equalsIgnoreCase(email)){
                            clienteVO = clienteDao.consultarPorChavePrimaria(clienteVO.getCodigo(),Uteis.NIVELMONTARDADOS_TODOS);
                            clienteDao = null;
                            return clienteVO;
                        }
                    }
                }
            }
        }
        return null;
    }

    private void validarInserirEmail(List emails, String email, PessoaVO pessoa) throws Exception {
        if(emails.size() == 0 && StringUtils.isNotBlank(email)){
            gravarEmail(pessoa, true, email);
        }
    }

    public void gravarEmail(PessoaVO pessoa, boolean emailcorrespondencia, String email) throws Exception {
        con.setAutoCommit(true);
        Email emailDao = new Email(con);
        EmailVO emailVO = new EmailVO();
        emailVO.setPessoa(pessoa.getCodigo());
        emailVO.setEmailCorrespondencia(emailcorrespondencia);
        emailVO.setEmail(email);
        emailDao.incluir(emailVO);
        emailDao = null;
        con.setAutoCommit(false);
    }

    private PessoaVO povoarTelefoneEnderecoVisitanteExistente(PessoaVO pessoaExistente ,String telResidencial,String telCelular,final String endereco, final String complemento,
            final String numero, final String bairro, final String cep) throws Exception{
        Pessoa pessoaDao = new Pessoa(con);
        pessoaExistente = pessoaDao.consultarPorChavePrimaria(pessoaExistente.getCodigo(),Uteis.NIVELMONTARDADOS_DADOSBASICOS);
        boolean telefoneComercialExistente = false;
        boolean telefoneResidencialExistente = false;
        for(TelefoneVO telefone : pessoaExistente.getTelefoneVOs()) {
            if (telefone.getTipoTelefone().equals(TipoTelefoneEnum.CELULAR.getCodigo()) && !UteisValidacao.emptyString(telCelular)) {
                telefoneComercialExistente = true;
                telefone.setNumero(telCelular);
            } else if (telefone.getTipoTelefone().equals(TipoTelefoneEnum.RESIDENCIAL.getCodigo()) && !UteisValidacao.emptyString(telResidencial)) {
                telefoneResidencialExistente = true;
                telefone.setNumero(telResidencial);
            }
        }
        if(!telefoneComercialExistente){
            povoarTelefonePessoa(pessoaExistente.getTelefoneVOs(),telCelular,TipoTelefoneEnum.CELULAR, "");
        }
        if(!telefoneResidencialExistente){
            povoarTelefonePessoa(pessoaExistente.getTelefoneVOs(),telResidencial,TipoTelefoneEnum.RESIDENCIAL, "");
        }
        boolean enderecoExistente = false;
        for(EnderecoVO enderecoVO : pessoaExistente.getEnderecoVOs()) {
            if (enderecoVO.getTipoEndereco().equals(TipoEnderecoEnum.RESIDENCIAL.getCodigo()) && !UteisValidacao.emptyString(endereco)) {
                enderecoExistente = true;
                enderecoVO.setTipoEndereco(TipoEnderecoEnum.RESIDENCIAL.getCodigo());
                enderecoVO.setEndereco(endereco);
                enderecoVO.setComplemento(complemento);
                enderecoVO.setNumero(numero);
                enderecoVO.setBairro(bairro);
                enderecoVO.setCep(cep);
                break;
            }
        }
        if(!enderecoExistente){
            povoarEndereco(pessoaExistente.getEnderecoVOs(),endereco,complemento,numero,bairro,cep);
        }
        return pessoaExistente;
    }
    private PessoaVO povoarTelefoneEndereco(PessoaVO pessoa ,String telCelular,String telComercial,final String endereco, final String complemento,
            final String numero, final String bairro, final String cep) throws Exception{
            povoarTelefonePessoa(pessoa.getTelefoneVOs(),telCelular,TipoTelefoneEnum.CELULAR, "");
            povoarTelefonePessoa(pessoa.getTelefoneVOs(),telComercial,TipoTelefoneEnum.RESIDENCIAL, "");
            povoarEndereco(pessoa.getEnderecoVOs(),endereco,complemento,numero,bairro,cep);
            return pessoa;
    }
    public void povoarTelefonePessoa(List<TelefoneVO> telefones ,String numero,TipoTelefoneEnum tipo, String descricao){
        if(UteisValidacao.emptyString(numero)){
            return;
        }
        TelefoneVO telefoneVO = new TelefoneVO();
        telefoneVO.setTipoTelefone(tipo.getCodigo());
        telefoneVO.setNumero(Formatador.formataTelefoneZW(numero));
        telefoneVO.setDescricao(descricao);
        telefones.add(telefoneVO);
    }

    public void povoarEndereco(List<EnderecoVO> enderecoVOS,final String endereco, final String complemento,
            final String numero, final String bairro, final String cep){
        if(UteisValidacao.emptyString(endereco)){
            return;
        }
        EnderecoVO enderecoVO = new EnderecoVO();
        enderecoVO.setTipoEndereco(TipoEnderecoEnum.RESIDENCIAL.getCodigo());
        enderecoVO.setEndereco(endereco);
        enderecoVO.setComplemento(complemento);
        enderecoVO.setNumero(numero);
        enderecoVO.setBairro(bairro);
        enderecoVO.setCep(cep);
        enderecoVO.setEnderecoCorrespondencia(true);
        enderecoVOS.add(enderecoVO);
    }

    public void adicionarConsultor(ClienteVO cliente, ColaboradorVO consultor){
        if(cliente != null && !UteisValidacao.emptyList(cliente.getVinculoVOs())){
            for(VinculoVO vinculo : cliente.getVinculoVOs()){
                if(vinculo.getTipoColaboradorVinculo().equals(TipoColaboradorEnum.CONSULTOR)){
                    vinculo.setColaborador(consultor);
                    return;
                }
            }
        }
        VinculoVO vinculoVO = new VinculoVO();
        vinculoVO.setColaborador(consultor);
        vinculoVO.setTipoVinculo(TipoColaboradorEnum.CONSULTOR.getSigla());
        cliente.getVinculoVOs().add(vinculoVO);
    }


    public String persistirClienteSite(Integer empresa, JSONObject cliente) throws Exception {
        return persistirClienteSite(cliente.optString("nome"),
                cliente.optString("cpf"), cliente.optString("email"), cliente.optString("sexo"),
                cliente.optString("dataNascimento"), cliente.optString("endereco"), cliente.optString("complemento"),
                cliente.optString("numero"), cliente.optString("bairro"), cliente.optString("cep"),
                cliente.optString("telCelular"), cliente.optString("telResidencial"), cliente.optString("senha"),
                empresa, cliente.optInt("codigoCidade"), cliente.optInt("codigoEstado"), cliente.optInt("idEmpresaFinanceiroRede"),
                cliente.optString("senhaUsuarioMovelJaDefinida"), cliente.optInt("consultor"),
                cliente.optString("observacao"),
                cliente.optString("nomeMae"),
                cliente.optString("dataNascimentoMae"),
                cliente.optString("cpfMae"));

    }

    public String persistirClienteSite(final String nome, final String cpf, final String email, final String sexo,
                                       final String dataNascimento, final String endereco, final String complemento,
                                       final String numero, final String bairro, final String cep,
                                       final String telCelular, final String telResidencial, final String senha,
                                       Integer empresa, Integer codigoCidade, Integer codigoEstado, Integer idEmpresaFinanceiroRede,
                                       String senhaUsuarioMovelJaDefinida, Integer consultor,
                                       String observacao) throws Exception {
        return persistirClienteSite(nome, cpf, email, sexo, dataNascimento, endereco, complemento, numero,
        bairro, cep, telCelular, telResidencial, senha, empresa, codigoCidade, codigoEstado, idEmpresaFinanceiroRede,
                senhaUsuarioMovelJaDefinida, consultor, observacao, null, null, null);

    }
    public String persistirClienteSite(final String nome, final String cpf, final String email, final String sexo,
                                       final String dataNascimento, final String endereco, final String complemento,
                                       final String numero, final String bairro, final String cep,
                                       final String telCelular, final String telResidencial, final String senha,
                                       Integer empresa, Integer codigoCidade, Integer codigoEstado, Integer idEmpresaFinanceiroRede,
                                       String senhaUsuarioMovelJaDefinida, Integer consultor,
                                       String observacao, final String nomeMae, final String dataNascimentoMae, final String cpfMae) throws Exception {
        String retorno = "";
        try {
            con.setAutoCommit(false);
            Empresa empresaDao = new Empresa(con);
            EmpresaVO empresaVO = empresaDao.consultarPorChavePrimaria(empresa, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
            Integer codigoPais = null;
            if ((codigoCidade != null) && (codigoCidade > 0)){
                Cidade cidade = new Cidade(con);
                CidadeVO cidadeVO = cidade.consultarPorCodigoExato(codigoCidade,false,Uteis.NIVELMONTARDADOS_DADOSBASICOS);
                if ((cidadeVO != null) && (cidadeVO.getPais() != null)) {
                    codigoPais = cidadeVO.getPais().getCodigo();
                }
            }

            UsuarioMovel usuarioMovelDao = new UsuarioMovel(con);
            Boolean temUsuarioMovel = !UteisValidacao.emptyString(usuarioMovelDao.obterUsuarioMesmoNome(email));

            Pessoa pessoaDao = new Pessoa(con);
            String cpfConsultar = cpf;
            if (cpfConsultar.length() == 11) {
                cpfConsultar = cpfConsultar.substring(0, 3) + "." + cpfConsultar.substring(3, 6) + "." + cpfConsultar.substring(6, 9) + "-" + cpfConsultar.substring(9);
            }

            ConfiguracaoSistema configuracaoSistema = new ConfiguracaoSistema(con);
            ConfiguracaoSistemaVO configuracaoSistemaVO = configuracaoSistema.consultarConfigs(Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA);
            boolean validarCpfDuplicado = configuracaoSistemaVO.isValidarCpfDuplicado();
            List pessoas = new ArrayList();
            if (validarCpfDuplicado) {
                pessoas = pessoaDao.consultarPorCfp(cpfConsultar, false, Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA);
            }


            List emails = consultarEmailExistente(email);
            if (validarCpfDuplicado && !UteisValidacao.emptyList(pessoas)) {
                //Aqui o autocommit será setado como true, ou seja tudo pra cima sera commitado, cuidado ao inserir algo.
                //Para o totem gravar email do visitante e nao retornar excecao
                validarInserirEmail(emails, email, (PessoaVO) pessoas.get(0));
                emails = consultarEmailExistente(email);
            }
            //Validar se a pessoa existente corresponde á informada pelo site
            // valida se possui contrato ativo, email , data nascimento
            ClienteVO clienteVO = validarSobreporVisitanteExistente(pessoas,emails,dataNascimento,email);
            boolean alterarVisitanteExistente = clienteVO != null;

            if ((emails.size() == 0 && pessoas.size() == 0) || alterarVisitanteExistente || !validarCpfDuplicado) {

                PessoaVO pessoaVO = new PessoaVO();
                if(alterarVisitanteExistente){
                    pessoaVO.setCodigo(((PessoaVO)pessoas.get(0)).getCodigo());
                    pessoaVO = povoarTelefoneEnderecoVisitanteExistente(pessoaVO,telResidencial, telCelular,endereco,complemento,numero,bairro,cep);
                }else {
                    pessoaVO = povoarTelefoneEndereco(pessoaVO,telCelular,telResidencial,endereco,complemento,numero,bairro,cep);
                }
                pessoaVO.setNome(nome);
                pessoaVO.getCidade().setCodigo(codigoCidade);
                pessoaVO.getEstadoVO().setCodigo(codigoEstado);
                if (codigoPais != null){
                    pessoaVO.setPais(new PaisVO());
                    pessoaVO.getPais().setCodigo(codigoPais);
                }
                if(!alterarVisitanteExistente) {
                    EmailVO emailVO = new EmailVO();
                    emailVO.setEmail(email);
                    emailVO.setEmailCorrespondencia(true);
                    pessoaVO.getEmailVOs().add(emailVO);
                }

                if (configuracaoSistemaVO.isCpfValidar()) {
                    if (UteisValidacao.isValidCPF(cpf)) {
                        pessoaVO.setCfp(cpf);
                    } else {
                        throw new Exception("CPF inválido.");
                    }
                } else {
                    pessoaVO.setCfp(cpf);
                }

                pessoaVO.setSexo(sexo);
                pessoaVO.setTipoPessoa(TipoPessoaEnum.ALUNO.getTipo());

                if ((dataNascimento != null) && (!dataNascimento.trim().equals("")) && (Calendario.getDate("dd/MM/yyyy", dataNascimento).after(Calendario.hoje()))) {
                    throw new Exception("Data de Nascimento superior a data atual.");
                }

                if (!UteisValidacao.emptyString(dataNascimento)) {
                    pessoaVO.setDataNasc(Calendario.getDate("dd/MM/yyyy", dataNascimento));
                }

                if (!UteisValidacao.emptyString(nomeMae)) {
                    pessoaVO.setNomeMae(nomeMae);
                }
                if (!UteisValidacao.emptyString(cpfMae)) {
                    pessoaVO.setCpfMae(cpfMae);
                }
                if (!UteisValidacao.emptyString(dataNascimentoMae)) {
                    try {
                        pessoaVO.setDataNascimentoResponsavel(Calendario.getDate("dd/MM/yyyy", dataNascimentoMae));
                    }catch (Exception e){
                        Uteis.logar(e, IntegracaoCadastros.class);
                    }
                }


                if (pessoaVO.getDataNasc() != null && pessoaVO.getIdade() > 18) {
                    if (!UteisValidacao.emptyString(pessoaVO.getNomeMae()) && !UteisValidacao.emptyString(pessoaVO.getCpfMae())) {
                        pessoaVO.setEmitirNomeTerceiro(true);
                        pessoaVO.setNomeTerceiro(pessoaVO.getNomeMae());
                        pessoaVO.setCpfCNPJTerceiro(pessoaVO.getCpfMae());
                    }
                }

                Colaborador colaborador = new Colaborador(con);
                ColaboradorVO colaboradorSite = null;
                if (!UteisValidacao.emptyNumber(consultor)) {
                    Colaborador colaboradorDao = new Colaborador(con);
                    colaboradorSite = colaboradorDao.consultarPorCodigoIgualTipoColaborador(consultor, empresaVO.getCodigo(), TipoColaboradorEnum.CONSULTOR, false, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
                }

                if (colaboradorSite == null) {
                    if (UteisValidacao.emptyNumber(empresaVO.getConsultorSite().getCodigo())) {
                        colaboradorSite = new ColaboradorVO();
                        TipoColaboradorVO tipoColaboradorVO = new TipoColaboradorVO();
                        tipoColaboradorVO.setDescricao(TipoColaboradorEnum.CONSULTOR.getSigla());

                        colaboradorSite.getPessoa().setNome("COLABORADOR SITE");
                        colaboradorSite.getPessoa().setDataNasc(new Date());
                        colaboradorSite.setEmpresa(empresaVO);
                        colaboradorSite.setSituacao("AT");
                        colaboradorSite.setDiaVencimento(1);
                        colaboradorSite.setPorcComissao(1.0);

                        colaboradorSite.getListaTipoColaboradorVOs().add(tipoColaboradorVO);
                        colaboradorSite = colaborador.criarOuConsultarSeExistePorNome(colaboradorSite, false);
                    } else {
                        colaboradorSite = empresaVO.getConsultorSite();
                    }
                }

                clienteVO = clienteVO == null ? new ClienteVO() : clienteVO;
                adicionarConsultor(clienteVO,colaboradorSite);
                clienteVO.setPessoa(pessoaVO);
                clienteVO.setEmpresa(empresaVO);
                Cliente cliente = new Cliente(con);
                if(!alterarVisitanteExistente) {
                    cliente.gerarNumeroMatricula(clienteVO, empresaVO, null);
                    cliente.incluirClienteSimplificado(clienteVO, false);
                    cliente = null;
                } else {
                    cliente.alterarClienteSimplesSite(clienteVO, configuracaoSistemaVO, false);
                }

                if (empresaVO.isCriarBvVendaSite() && !alterarVisitanteExistente) {
                    QuestionarioClienteVO questionarioClienteVO = new QuestionarioClienteVO();
                    Questionario questionario = new Questionario(con);
                    QuestionarioVO questionarioVO = questionario.consultarPorDescricao("BV MATRICULA", Uteis.NIVELMONTARDADOS_DADOSBASICOS);
                    questionarioClienteVO.setCliente(clienteVO);
                    questionarioClienteVO.setQuestionario(questionarioVO);
                    questionarioClienteVO.setConsultor(colaboradorSite);
                    questionarioClienteVO.setData(Calendario.hoje());
                    questionarioClienteVO.setTipoBV(TipoBVEnum.MA);

                    questionarioClienteVO.setQuestionarioPerguntaClienteVOs(new ArrayList());
                    for (QuestionarioPerguntaVO questionarioPerguntaVO: questionarioVO.getQuestionarioPerguntaVOs()){
                        // criar as perguntas do questionario do cliente
                        PerguntaClienteVO perguntaClienteVO = new PerguntaClienteVO();
                        perguntaClienteVO.setTipoPergunta(questionarioPerguntaVO.getPergunta().getTipoPergunta());
                        perguntaClienteVO.setDescricao(questionarioPerguntaVO.getPergunta().getDescricao());
                        perguntaClienteVO.setMultipla(questionarioPerguntaVO.getPergunta().getTipoPergunta().equals("ME"));
                        perguntaClienteVO.setSimples(questionarioPerguntaVO.getPergunta().getTipoPergunta().equals("SE") || questionarioPerguntaVO.getPergunta().getTipoPergunta().equals("SN"));
                        perguntaClienteVO.setTextual(questionarioPerguntaVO.getPergunta().getTipoPergunta().equals("TE"));

                        perguntaClienteVO.setRespostaPergClienteVOs(new ArrayList());
                        for(RespostaPerguntaVO respostaPerguntaVO: questionarioPerguntaVO.getPergunta().getRespostaPerguntaVOs()){
                            // criar as respostas das perguntas do questionario do cliente
                            RespostaPergClienteVO respostaPergClienteVO = new RespostaPergClienteVO();
                            respostaPergClienteVO.setRespostaOpcao(false);
                            respostaPergClienteVO.setDescricaoRespota(respostaPerguntaVO.getDescricaoRespota());
                            perguntaClienteVO.getRespostaPergClienteVOs().add(respostaPergClienteVO);
                        }
                        // criar o questionario do cliente.
                        QuestionarioPerguntaClienteVO questionarioPerguntaClienteVO = new QuestionarioPerguntaClienteVO();
                        questionarioPerguntaClienteVO.setQuestionarioCliente(questionarioClienteVO.getCodigo());
                        questionarioPerguntaClienteVO.setPerguntaCliente(perguntaClienteVO);
                        questionarioClienteVO.getQuestionarioPerguntaClienteVOs().add(questionarioPerguntaClienteVO);
                    }

                    QuestionarioCliente questionarioCliente = new QuestionarioCliente(con);
                    questionarioCliente.incluirSemComit(questionarioClienteVO, false, true);
                }

                retorno = "Cliente cadastrado com sucesso.";

            } else {
                if (!pessoas.isEmpty()) {
                    throw new Exception("CPF já cadastrado no sistema! Entre em contato com a academia e solicite o usuário para acesso.");
                } else if (!emails.isEmpty() && temUsuarioMovel) {
                    throw new Exception("Já existe um cliente com esse e-mail, por favor faça login.");
                } else if (!emails.isEmpty() && !temUsuarioMovel) {
                    throw new Exception("Já existe um cliente com esse e-mail.");
                }
            }

            if(!UteisValidacao.emptyString(email)
                    && !UteisValidacao.emptyString(senha)){
                retorno += incluirUsuarioMovel(null,empresaVO,clienteVO,email,senha, nome,senhaUsuarioMovelJaDefinida, idEmpresaFinanceiroRede, null, true);
            }

            try {
                ZillyonWebFacade zwFacade = new ZillyonWebFacade(con);
                zwFacade.atualizarSintetico(clienteVO, Calendario.hoje(), SituacaoClienteSinteticoEnum.GRUPO_TODOS, false);
                zwFacade = null;
            } catch (SinteticoException se) {
                Uteis.logar(null, "Problema ao processar sintético do cliente: " + clienteVO.getCodigo() + " - Empresa: " + empresaVO.getNome());
            }

            Usuario usuarioDao = new Usuario(con);
            UsuarioVO usuarioAdmin = usuarioDao.consultarPorChavePrimaria(1, Uteis.NIVELMONTARDADOS_MINIMOS);
            usuarioDao = null;

            if(!UteisValidacao.emptyString(observacao)){
                ClienteObservacaoVO clienteObservacaoVO = new ClienteObservacaoVO();
                clienteObservacaoVO.setClienteVO(clienteVO);
                clienteObservacaoVO.setObservacao(observacao);
                clienteObservacaoVO.setUsuarioVO(usuarioAdmin);
                clienteObservacaoVO.setDataCadastro(Calendario.hoje());
                ClienteObservacao cliObsDao = new ClienteObservacao(con);
                cliObsDao.incluir(clienteObservacaoVO);
            }

            Log logDao = new Log(con);
            logDao.incluirLogInclusaoClienteSite(clienteVO, usuarioAdmin);
            logDao = null;

            con.commit();
            return retorno + " | CodCliente " + clienteVO.getCodigo();
        } catch (Exception e) {
            con.rollback();
            con.setAutoCommit(true);
            return "ERRO: " + e.getMessage();
        } finally {
            con.setAutoCommit(true);
        }
    }

    private List consultarEmailExistente(String email) throws Exception {
        Email emailDao = new Email(con);
        List emails = emailDao.consultarEmailExiste(email, false, Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA);
        emailDao = null;
        return emails;
    }

    public String incluirUsuarioMovel(String key, EmpresaVO empresaVO,
                                      ClienteVO clienteVO, String email, String senha, String nomeCliente, String senhaUsuarioMovelJaDefinida,
                                      Integer idEmpresaFinanceiroRede, OrigemSistemaEnum origemSistemaEnum, boolean isEnviarEmailUsuarioMovelAutomaticamente)throws Exception{
        if ((email == null) || (email.trim().equals(""))){
            return "";
        }
        if ((idEmpresaFinanceiroRede != null) && (idEmpresaFinanceiroRede > 0)){
            OAMDService oamdService = new OAMDService();
            Logger.getLogger(this.getClass().getName()).log(Level.SEVERE, "#### INICIOU incluirLoginSiteRedeEmpresa");
            oamdService.incluirLoginSiteRedeEmpresa(email, idEmpresaFinanceiroRede, clienteVO.getPessoa().getCfp());
            Logger.getLogger(this.getClass().getName()).log(Level.SEVERE, "#### FINALIZOU incluirLoginSiteRedeEmpresa");
            oamdService = null;
        }
        UsuarioMovelVO usuarioMovelVO = new UsuarioMovelVO();
        usuarioMovelVO.setCliente(clienteVO);
        usuarioMovelVO.setEmpresa(empresaVO.getCodigo());
        usuarioMovelVO.setNome(email.toLowerCase().trim());
        usuarioMovelVO.setAtivo(true);
        usuarioMovelVO.setOrigem("API");
        usuarioMovelVO.setSenha(senha);
        usuarioMovelVO.setCpf(clienteVO.getPessoa().getCfp());

        UsuarioMovel usuarioMovel = new UsuarioMovel(con);
        List usuariosTmp = usuarioMovel.consultarPorNome(email, false, Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA);
        if (usuariosTmp.size() == 0) {
            // excluir o usuário móvel vinculado ao email antigo.
            usuarioMovel.excluir(clienteVO);
            // incluir novo usuário móvel ao novo email informado.
            usuarioMovel.incluir(usuarioMovelVO);
            if (null != key){
                new Thread(() -> {
                    try {
                        sincronizarTW(key,clienteVO,usuarioMovelVO);
                    } catch (Exception e) {
                        Uteis.logar("Erro ao sincronizar o usuário: " +
                                usuarioMovelVO.getNome()
                                + " com o TreinoWeb", TreinoWSConsumer.class);
                    }
                }).start();
            }

            if ((senhaUsuarioMovelJaDefinida != null) && (!senhaUsuarioMovelJaDefinida.equals(""))){
                usuarioMovel.alterarSenhaJaEncriptada(usuarioMovelVO.getCodigo(),senhaUsuarioMovelJaDefinida);
            } else if (isEnviarEmailUsuarioMovelAutomaticamente) {
                ConfiguracaoSistemaCRMVO configuracaoSistemaCRM = SuperControle.getConfiguracaoSMTPNoReply();
                UteisEmail uteisEmail = new UteisEmail();

                ModeloMensagemVO modeloMensagemVO = null;
                String assuntoEmail = "Senha de Acesso à sua academia";
                if (empresaVO.getModeloMensagemVendasOnline() != null && empresaVO.getModeloMensagemVendasOnline().getCodigo() != 0) {
                    ModeloMensagem modeloMensagemDao = new ModeloMensagem(con);
                    modeloMensagemVO = modeloMensagemDao.consultarPorChavePrimaria(empresaVO.getModeloMensagemVendasOnline().getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS);
                    assuntoEmail = modeloMensagemVO.getTitulo();
                }
                Logger.getLogger(this.getClass().getName()).log(Level.SEVERE, "#### INICIOU ENVIAR EMAIL");
                uteisEmail.novo(assuntoEmail, configuracaoSistemaCRM);

                final String genKey;
                if (origemSistemaEnum != null && origemSistemaEnum.equals(OrigemSistemaEnum.VENDAS_ONLINE_2)) {
                    genKey = MidiaService.getInstance().genKey(key, MidiaEntidadeEnum.FOTO_EMPRESA_EMAIL, empresaVO.getCodigo().toString());
                } else {
                    genKey = MidiaService.getInstance().genKey(key, MidiaEntidadeEnum.FOTO_EMPRESA, empresaVO.getCodigo().toString());
                }
                final String urlFotoEmpresa = Uteis.getPaintFotoDaNuvem(genKey);
                String mensagemEmail = prepararMensagemEmail(modeloMensagemVO, clienteVO.getPessoa().getCodigo(), nomeCliente, email, senha, false, empresaVO, urlFotoEmpresa, key);
                uteisEmail.enviarEmailN(new String[]{email}, mensagemEmail, assuntoEmail, "");
                Logger.getLogger(this.getClass().getName()).log(Level.SEVERE, "#### FINALIZOU ENVIAR EMAIL");
                return " Email de acesso enviado.";
            }
        }
        usuarioMovel = null;
        return "";
    }

    public String prepararMensagemEmail(ModeloMensagemVO modeloMensagemVO,
                                               Integer pessoa,
                                               String nome, String email, String senha,
                                               boolean recuperacao, EmpresaVO empresaVO, String urlFotoEmpresa, String key) throws Exception {
        if (modeloMensagemVO != null) {
            String modeloMensagem = modeloMensagemVO.getMensagem();
            //mantive as tags com @ pra garantir a retrocompatibilidade dos modelos já feitos
            modeloMensagem = modeloMensagem.replace("@USUARIO_VENDAS_ONLINE@", email);
            modeloMensagem = modeloMensagem.replace("@SENHA_VENDAS_ONLINE@", senha);
            modeloMensagem = modeloMensagem.replace("USUARIO_VENDAS_ONLINE", email);
            modeloMensagem = modeloMensagem.replace("SENHA_VENDAS_ONLINE", senha);
            modeloMensagem = modeloMensagem.replace("TAG_NOME", nome);
            modeloMensagem = modeloMensagem.replace("TAG_PNOME", Uteis.getPrimeiroNome(nome));
            if(pessoa != null && containsTagsContrato(modeloMensagem)){
                Contrato contratoDao = new Contrato(con);
                ContratoVO contratoVO = contratoDao.consultarUltimoContratoPorPessoa(pessoa, Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA);
                modeloMensagem = replaceTagsContrato(modeloMensagem, contratoVO);
            }
            return modeloMensagem;
        } else if (isNotBlank(key)){
            String  app_url_email = "";

            RequestHttpService httpService = new RequestHttpService();
            String urlTreino = PropsService.getPropertyValue(key, PropsService.urlTreino);
            RespostaHttpDTO respostaHttpDTO = httpService.executeRequest(urlTreino + "/prest/config/" + key + "/manutencao", null, null, null, MetodoHttpEnum.GET);
            JSONObject json = new JSONObject(respostaHttpDTO.getResponse());
            if (json.has("return")) {
                String returnValue = json.getString("return");
                returnValue = returnValue.replaceAll("\\\\", "");
                JSONObject nestedObj = new JSONObject(returnValue);
                app_url_email = nestedObj.getString("app_url_email");
            }

            return UsuarioMovelControle.montarMensagemEmailSenha(nome, email, senha, recuperacao, empresaVO, urlFotoEmpresa, false, "", "", app_url_email).toString();
        } else {
            return UsuarioMovelControle.montarMensagemEmailSenha(nome, email, senha, recuperacao, empresaVO, urlFotoEmpresa, false, "", "", "").toString();
        }
    }

    public boolean containsTagsContrato(String modeloMensagem){
        return modeloMensagem.contains(TagsEmailBoasVindasEnum.TAG_CARTAO_MASCARADO.getTag())
                || modeloMensagem.contains(TagsEmailBoasVindasEnum.TAG_DATA_CADASTRO.getTag())
                || modeloMensagem.contains(TagsEmailBoasVindasEnum.TAG_VENCIMENTO_PLANO.getTag())
                || modeloMensagem.contains(TagsEmailBoasVindasEnum.TAG_DESCRICAO_FINANCEIRA.getTag());
    }

    public String replaceTagsContrato(String modeloMensagem, ContratoVO contratoVO){
        for(TagsEmailBoasVindasEnum tag : TagsEmailBoasVindasEnum.values()){
            String valor = "";
            if(contratoVO != null){
                try {
                    switch (tag){
                        case TAG_DATA_CADASTRO:
                            valor = Calendario.getDataAplicandoFormatacao(contratoVO.getDataLancamento(), "dd/MM/yyyy");
                            break;
                        case TAG_CARTAO_MASCARADO:
                            AutorizacaoCobrancaClienteInterfaceFacade autorizacaoCobrancaClienteDao = new AutorizacaoCobrancaCliente(getCon());
                            valor = autorizacaoCobrancaClienteDao.cartaoMascaradoPessoa(contratoVO.getPessoa().getCodigo());
                            break;
                        case TAG_VENCIMENTO_PLANO:
                            valor = Calendario.getDataAplicandoFormatacao(contratoVO.getVigenciaAteAjustada(), "dd/MM/yyyy");
                            break;
                        case TAG_DESCRICAO_FINANCEIRA:
                            valor = tagDescricaoFinanceira(contratoVO.getCodigo());
                            break;
                    }

                }catch (Exception e){
                    Uteis.logar(e, IntegracaoCadastros.class);
                }
            }
            try {
                modeloMensagem = modeloMensagem.replace(tag.getTag(), valor);
            } catch (Exception e) {
                Uteis.logar(String.format("Ignorar erro em replaceTagsContrato %s ao preencher tag %s com valor %s do contrato %s",
                        e, tag, valor, contratoVO != null ? contratoVO.getCodigo() : "<sem contrato>"));
            }
        }
        return modeloMensagem;

    }

    public String tagDescricaoFinanceira(Integer contrato) throws Exception{
        MovParcela movParcelaDao = new MovParcela(getCon());
        List<MovParcelaVO> parcelas = movParcelaDao.consultarPorContrato(contrato, Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA);
        StringBuilder htmlTable = new StringBuilder();
        htmlTable.append("<table style=\"width: 100%\">");
        for(MovParcelaVO parcelaVO : parcelas){
            htmlTable.append("<tr>");
            htmlTable.append("<td>").append(parcelaVO.getDescricao()).append("</td>");
            htmlTable.append("<td style=\"text-align: center;\">").append(Calendario.getDataAplicandoFormatacao(parcelaVO.getDataVencimento(), "dd/MM/yyyy")).append("</td>");
            htmlTable.append("<td style=\"text-align: right;\">").append(Formatador.formatarValorMonetarioSemMoeda(parcelaVO.getValorParcela())).append("</td>");
            htmlTable.append("</tr>");
        }
        htmlTable.append("</table>");
        return htmlTable.toString();
    }

    public void recuperarUsuarioMovel(final String email, int empresa, String key) throws Exception {
        try {
            con.setAutoCommit(false);
            Empresa empresaDao = new Empresa(con);
            EmpresaVO empresaVO = empresaDao.consultarPorCodigo(empresa, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
            empresaDao = null;

            UsuarioMovel usuarioMovel = new UsuarioMovel(con);
            List usuariosTmp = usuarioMovel.consultarPorNome(email, false, Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA);

            List emails = consultarEmailExistente(email);
            if ((emails == null) || (emails.size() <= 0)){
                throw new Exception("Usuário não possui email cadastrado.");
            }

            if (usuariosTmp.size() >= 1) {

                ClienteVO clienteVO;
                Cliente cliente = new Cliente(con);
                EmailVO emailVO = (EmailVO) emails.get(0);
                clienteVO = cliente.consultarPorCodigoPessoa(emailVO.getPessoaVO().getCodigo(), Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA);

                ConfiguracaoSistemaCRMVO configuracaoSistemaCRM = SuperControle.getConfiguracaoSMTPNoReply();

                Integer senhaNova = UteisValidacao.gerarNumeroRandomico(10000, 50000);
                UsuarioMovelVO usuarioMovelVO = (UsuarioMovelVO) usuariosTmp.get(0);
                usuarioMovelVO.setSenha(senhaNova.toString());
                usuarioMovel.alterar(usuarioMovelVO);

                UteisEmail uteisEmail = new UteisEmail();

                ModeloMensagemVO modeloMensagemVO = null;
                String assuntoEmail = "Recuperação de Acesso ZillyonWeb";
                if (empresaVO.getModeloMensagemEsqueciMinhaSenhaVendasOnline() != null && empresaVO.getModeloMensagemEsqueciMinhaSenhaVendasOnline().getCodigo() != 0) {
                    ModeloMensagem modeloMensagemDao = new ModeloMensagem(con);
                    modeloMensagemVO = modeloMensagemDao.consultarPorChavePrimaria(empresaVO.getModeloMensagemEsqueciMinhaSenhaVendasOnline().getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS);
                    assuntoEmail = modeloMensagemVO.getTitulo();
                }

                uteisEmail.novo(assuntoEmail, configuracaoSistemaCRM);
                final String genKey = MidiaService.getInstance().genKey(key, MidiaEntidadeEnum.FOTO_EMPRESA, empresaVO.getCodigo().toString());
                final String urlFotoEmpresa = Uteis.getPaintFotoDaNuvem(genKey);
                String mensagemEmail = prepararMensagemEmail(modeloMensagemVO,
                        clienteVO.getPessoa().getCodigo(),
                        clienteVO.getPessoa().getNome(), email, senhaNova.toString(),
                        true, empresaVO, urlFotoEmpresa, key);

                uteisEmail.enviarEmailN(new String[]{email}, mensagemEmail, assuntoEmail, "");
                cliente = null;

                ZillyonWebFacade zwFacade = new ZillyonWebFacade(con);
                zwFacade.atualizarSintetico(clienteVO, Calendario.hoje(), SituacaoClienteSinteticoEnum.GRUPO_TODOS, false);
                zwFacade = null;
            } else {
                throw new Exception("Usuário não encontrado.");
            }
            usuarioMovel = null;
            con.commit();

        } catch (Exception e) {
            con.rollback();
            con.setAutoCommit(true);
            throw e;
        } finally {
            con.setAutoCommit(true);
        }
    }

    public String consultarColaboradores(int empresa) throws Exception {
        ColaboradorInterfaceFacade colaboradorDao = new Colaborador(con);
        try {
            List<ColaboradorVO> colaboradores = colaboradorDao.consultarPorTipoColaboradorAtivo(TipoColaboradorEnum.CONSULTOR, "AT", empresa, false, Uteis.NIVELMONTARDADOS_CONSULTA_WS);
            Ordenacao.ordenarLista(colaboradores, "codigo");
            JSONArray jsonArray = new JSONArray();
            for (ColaboradorVO colaborador : colaboradores) {
                JSONObject jsonObject = new JSONObject(colaborador.toWS());
                jsonArray.put(jsonObject);
}

            return jsonArray.toString();

        } catch (Exception ex) {
            return "ERRO: " + ex.getMessage();
        } finally {
            colaboradorDao = null;
        }
    }

    public String persistirLogAuditoriaJSON(String json) throws Exception {
        LogJSON logJSON = new LogJSON(json);
        LogVO logVO = new LogVO(logJSON);

        Log log = new Log(con);
        log.incluir(logVO);

        return "";
    }

    public String enviarEmail(String json) {
        try {

            JSONObject jsonObject = new JSONObject(json);

            Integer empresa = 0;
            if (jsonObject.has("empresa")) {
                empresa = jsonObject.getInt("empresa");
            }

            Integer modeloMsg = 0;
            if (jsonObject.has("modeloMsg")) {
                modeloMsg = jsonObject.getInt("modeloMsg");
            }

            Integer cliente = 0;
            if (jsonObject.has("cliente")) {
                cliente = jsonObject.getInt("cliente");
            }

            String assuntoEnviado = "";
            if (jsonObject.has("assunto")) {
                assuntoEnviado = jsonObject.getString("assunto");
            }

            String email = "";
            if (jsonObject.has("email")) {
                email = jsonObject.getString("email");
            }

            boolean emailCliente = true;
            if (jsonObject.has("emailCliente")) {
                emailCliente = jsonObject.getBoolean("emailCliente");
            }


            EmpresaVO empresaVO = new EmpresaVO();
            ClienteVO clienteVO = new ClienteVO();
            ContratoVO contratoVO = new ContratoVO();

            Empresa empresaDao = new Empresa(con);
            if (!UteisValidacao.emptyNumber(empresa)) {
                empresaVO = empresaDao.consultarPorChavePrimaria(empresa, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
            }

            if (!UteisValidacao.emptyNumber(cliente)) {

                SituacaoClienteSinteticoDW situacaoDao = new SituacaoClienteSinteticoDW(con);
                SituacaoClienteSinteticoDWVO situacaoDW = situacaoDao.consultarCliente(cliente, Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA);
                situacaoDao = null;

                Cliente clienteDao = new Cliente(con);
                clienteVO = clienteDao.consultarPorChavePrimaria(cliente, Uteis.NIVELMONTARDADOS_MINIMOS);
                clienteDao = null;

                Pessoa pessoaDao = new Pessoa(con);
                PessoaVO pessoaVO = pessoaDao.consultarPorChavePrimaria(clienteVO.getPessoa().getCodigo(), Uteis.NIVELMONTARDADOS_TELACONSULTA);
                clienteVO.setPessoa(pessoaVO);
                pessoaDao = null;

                Contrato contratoDAO = new Contrato(con);
                boolean buscarUltimoContrato = true;
                if (!UteisValidacao.emptyNumber(situacaoDW.getCodigoContrato())) {
                    try {
                        contratoVO = contratoDAO.consultarPorChavePrimaria(situacaoDW.getCodigoContrato(), Uteis.NIVELMONTARDADOS_ROBO);
                        buscarUltimoContrato = false;
                    } catch (Exception ignored){
                    }
                }
                if (buscarUltimoContrato) {
                    contratoVO = contratoDAO.consultarUltimoContratoPorPessoa(clienteVO.getPessoa().getCodigo(), Uteis.NIVELMONTARDADOS_ROBO);
                }

                if (UteisValidacao.emptyNumber(empresa) && !UteisValidacao.emptyNumber(contratoVO.getEmpresa().getCodigo())) {
                    empresaVO = empresaDao.consultarPorChavePrimaria(contratoVO.getEmpresa().getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS);
                }
            }
            empresaDao = null;

            ConfiguracaoSistemaCRMVO configuracaoSistemaCRM = SuperControle.getConfiguracaoSMTPNoReply();
            UteisEmail uteisEmail = new UteisEmail();

            String assunto = "";
            ModeloMensagemVO modeloMensagemVO = new ModeloMensagemVO();
            ModeloMensagem modeloMensagemDao = new ModeloMensagem(con);
            if (!UteisValidacao.emptyNumber(modeloMsg)) {

                modeloMensagemVO = modeloMensagemDao.consultarPorChavePrimaria(modeloMsg, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
                assunto = modeloMensagemVO.getTitulo();

            } else if (empresaVO.getModeloMensagemVendasOnline() != null && empresaVO.getModeloMensagemVendasOnline().getCodigo() != 0) {
                modeloMensagemVO = modeloMensagemDao.consultarPorChavePrimaria(empresaVO.getModeloMensagemVendasOnline().getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS);
                assunto = modeloMensagemVO.getTitulo();
            }

            if (!UteisValidacao.emptyString(assuntoEnviado)) {
                assunto = assuntoEnviado;
            }

            if (UteisValidacao.emptyString(assunto)) {
                assunto = empresaVO.getNome();
            }

            if (UteisValidacao.emptyNumber(modeloMensagemVO.getCodigo())) {
                throw new Exception("Modelo de mensagem nao encontrado");
            }

            uteisEmail.novo(assunto, configuracaoSistemaCRM);

            String mensagemEmail = preparaEmail(empresaVO, clienteVO, contratoVO, modeloMensagemVO);

            List<String> emailsEnviar = new ArrayList<String>();
            if (!UteisValidacao.emptyString(email)) {
                emailsEnviar.add(email);
            }

            if (emailCliente) {
                for (EmailVO emailVO : clienteVO.getPessoa().getEmailVOs()) {
                    if (emailVO.getEmailCorrespondencia()) {
                        emailsEnviar.add(emailVO.getEmail());
                    }
                }
            }

            boolean existeEmailValido = false;
            for (String emailEnviar : emailsEnviar) {
                if (UteisValidacao.validaEmail(emailEnviar)) {
                    existeEmailValido = true;
                }
            }
            if (existeEmailValido) {
                try {
                    uteisEmail.enviarEmailN(emailsEnviar.toArray(new String[emailsEnviar.size()]), mensagemEmail, assunto, "");
                } catch (Exception ex) {
                    throw new Exception("Erro ao enviar emails");
                }
            } else {
                throw new Exception("Não foi encontrado nenhum email válido.");
            }

            Logger.getLogger(this.getClass().getName()).log(Level.SEVERE, "#### FINALIZOU ENVIAR EMAIL");
            return "Email enviado com sucesso.";
        } catch (Exception ex) {
            return "ERRO:" + ex.getMessage();
        }
    }

    private String preparaEmail(EmpresaVO empresaVO, ClienteVO clienteVO, ContratoVO contratoVO, ModeloMensagemVO modeloMensagemVO) {
        String modeloMensagem = modeloMensagemVO.getMensagem();
        if (!UteisValidacao.emptyNumber(contratoVO.getCodigo())) {
            modeloMensagem = modeloMensagem.replace("@TAG_CONTRATO_CODIGO@", contratoVO.getCodigo().toString());
            modeloMensagem = modeloMensagem.replace("@TAG_CONTRATO_DATA_LANCAMENTO@", contratoVO.getDataLancamento_Apresentar());
            modeloMensagem = modeloMensagem.replace("@TAG_CONTRATO_INICIO@", contratoVO.getVigenciaDe_Apresentar());
            modeloMensagem = modeloMensagem.replace("@TAG_CONTRATO_VENCIMENTO@", contratoVO.getVigenciaAteAjustada_Apresentar());
            modeloMensagem = modeloMensagem.replace("@TAG_CONTRATO_PLANO@", contratoVO.getPlano().getDescricao());
            modeloMensagem = modeloMensagem.replace("@TAG_CONTRATO_DURACAO@", contratoVO.getContratoDuracao().getNumeroMeses().toString());
        }

        if (!UteisValidacao.emptyNumber(clienteVO.getCodigo())) {
            modeloMensagem = modeloMensagem.replace("@TAG_NOME@", clienteVO.getNome_Apresentar());
            modeloMensagem = modeloMensagem.replace("@TAG_MATRICULA@", clienteVO.getMatricula());
            modeloMensagem = modeloMensagem.replace("@TAG_CPF@", clienteVO.getPessoa().getCfp());
        }

        if (!UteisValidacao.emptyNumber(empresaVO.getCodigo())) {
            modeloMensagem = modeloMensagem.replace("@TAG_NOME_EMPRESA@", empresaVO.getNome());
            modeloMensagem = modeloMensagem.replace("@TAG_CNPJ_EMPRESA@", empresaVO.getCNPJ());
            modeloMensagem = modeloMensagem.replace("@TAG_RAZAO_EMPRESA@", empresaVO.getRazaoSocial());
        }
        return modeloMensagem;
    }

    public String enviarEmailAngular(String json) {
        try {

            JSONObject jsonObject = new JSONObject(json);

            String assuntoEnviado = "";
            if (jsonObject.has("assunto")) {
                assuntoEnviado = jsonObject.getString("assunto");
            }

            String emails = "";
            String[] emailArray = new String[0];
            if (jsonObject.has("email")) {
                emails = jsonObject.getString("email");
                emailArray = emails.split(";");
            }

            String mensagemEmail = "";
            if (jsonObject.has("mensagem")) {
                mensagemEmail = jsonObject.getString("mensagem");
            }

            List<String> emailsEnviar = new ArrayList<>();
            if (!UteisValidacao.emptyString(emails)) {
                for (String email : emailArray) {
                    emailsEnviar.add(email);
                }
            }

            UteisEmail uteisEmail = new UteisEmail();
            ConfiguracaoSistemaCRMVO configuracaoSistemaCRM = SuperControle.getConfiguracaoSMTPNoReply();
            uteisEmail.novo(assuntoEnviado, configuracaoSistemaCRM);
            uteisEmail.enviarEmailN(emailsEnviar.toArray(new String[emailsEnviar.size()]), mensagemEmail, assuntoEnviado, "");

            Logger.getLogger(this.getClass().getName()).log(Level.SEVERE, "#### FINALIZOU ENVIAR EMAIL");
            return "Email enviado com sucesso.";
        } catch (Exception ex) {
            return "ERRO:" + ex.getMessage();
        }
    }

    public void sincronizarTW(String key, ClienteVO cliente, UsuarioMovelVO usuarioMovelVO)throws Exception{
        UsuarioMovel usuarioMovel = new UsuarioMovel(con);
        usuarioMovel.sincronizarTW(key,cliente,usuarioMovelVO);
    }

}

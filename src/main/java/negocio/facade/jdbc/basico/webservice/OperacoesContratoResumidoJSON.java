package negocio.facade.jdbc.basico.webservice;

import br.com.pactosolucoes.comuns.json.SuperJSON;

import java.util.ArrayList;
import java.util.List;

public class OperacoesContratoResumidoJSON extends SuperJSON {
    private String tipooOperacao;
    private int contrato;
    private String data;
    private int codConsultor;
    private String consultor;
    private String empresa;
    private String justificativa;
    private String descricaoCalculo;
    private String tipoCancelamento;
    private String observacao;
    private List<ParcelaResumidaJSON> parcelas;
    private Double valorDevolvido;
    private Double valorMultaCancelamento;
    private ClienteResumidoJSON aluno;

    public String getTipooOperacao() {
        return tipooOperacao;
    }

    public void setTipooOperacao(String tipooOperacao) {
        this.tipooOperacao = tipooOperacao;
    }

    public int getContrato() {
        return contrato;
    }

    public void setContrato(int contrato) {
        this.contrato = contrato;
    }

    public String getData() {
        return data;
    }

    public void setData(String data) {
        this.data = data;
    }

    public List<ParcelaResumidaJSON> getParcelas() {
        if (parcelas == null) {
            parcelas = new ArrayList<ParcelaResumidaJSON>();
        }
        return parcelas;
    }

    public void setParcelas(List<ParcelaResumidaJSON> parcelas) {
        this.parcelas = parcelas;
    }

    public int getCodConsultor() {
        return codConsultor;
    }

    public void setCodConsultor(int codConsultor) {
        this.codConsultor = codConsultor;
    }

    public String getConsultor() {
        return consultor;
    }

    public void setConsultor(String consultor) {
        this.consultor = consultor;
    }

    public String getEmpresa() {
        return empresa;
    }

    public void setEmpresa(String empresa) {
        this.empresa = empresa;
    }

    public String getJustificativa() {
        return justificativa;
    }

    public void setJustificativa(String justificativa) {
        this.justificativa = justificativa;
    }

    public String getDescricaoCalculo() {
        return descricaoCalculo;
    }

    public void setDescricaoCalculo(String descricaoCalculo) {
        this.descricaoCalculo = descricaoCalculo;
    }

    public String getTipoCancelamento() {
        return tipoCancelamento;
    }

    public void setTipoCancelamento(String tipoCancelamento) {
        this.tipoCancelamento = tipoCancelamento;
    }

    public String getObservacao() {
        return observacao;
    }

    public void setObservacao(String observacao) {
        this.observacao = observacao;
    }

    public Double getValorDevolvido() {
        return valorDevolvido;
    }

    public void setValorDevolvido(Double valorDevolvido) {
        this.valorDevolvido = valorDevolvido;
    }

    public Double getValorMultaCancelamento() {
        return valorMultaCancelamento;
    }

    public void setValorMultaCancelamento(Double valorMultaCancelamento) {
        this.valorMultaCancelamento = valorMultaCancelamento;
    }

    public ClienteResumidoJSON getAluno() {
        return aluno;
    }

    public void setAluno(ClienteResumidoJSON aluno) {
        this.aluno = aluno;
    }
}
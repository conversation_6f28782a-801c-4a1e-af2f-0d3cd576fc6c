package negocio.facade.jdbc.basico.webservice;

import br.com.pactosolucoes.comuns.json.SuperJSON;

import java.util.List;

public class ReciboResumidoJSON extends SuperJSON {

    private int codigo;
    private int unidadeCodigo;
    private String responsavelLancamento;
    private int responsavelLancamentoCodigo;
    private String unidade;
    private String data;
    private ClienteResumidoJSON aluno;
    private List<PagamentoResumidoJSON> formas;
    private List<ParcelasPagasJSON> parcelasPagas;

    public int getCodigo() {
        return codigo;
    }

    public void setCodigo(int codigo) {
        this.codigo = codigo;
    }

    public ClienteResumidoJSON getAluno() {
        return aluno;
    }

    public void setAluno(ClienteResumidoJSON aluno) {
        this.aluno = aluno;
    }

    public List<PagamentoResumidoJSON> getFormas() {
        return formas;
    }

    public void setFormas(List<PagamentoResumidoJSON> formas) {
        this.formas = formas;
    }

    public List<ParcelasPagasJSON> getParcelasPagas() {
        return parcelasPagas;
    }

    public void setParcelasPagas(List<ParcelasPagasJSON> parcelasPagas) {
        this.parcelasPagas = parcelasPagas;
    }

    public int getUnidadeCodigo() {
        return unidadeCodigo;
    }

    public void setUnidadeCodigo(int unidadeCodigo) {
        this.unidadeCodigo = unidadeCodigo;
    }

    public String getUnidade() {
        return unidade;
    }

    public void setUnidade(String unidade) {
        this.unidade = unidade;
    }

    public String getData() {
        return data;
    }

    public void setData(String data) {
        this.data = data;
    }

    public String getResponsavelLancamento() {
        return responsavelLancamento;
    }

    public void setResponsavelLancamento(String responsavelLancamento) {
        this.responsavelLancamento = responsavelLancamento;
    }

    public int getResponsavelLancamentoCodigo() {
        return responsavelLancamentoCodigo;
    }

    public void setResponsavelLancamentoCodigo(int responsavelLancamentoCodigo) {
        this.responsavelLancamentoCodigo = responsavelLancamentoCodigo;
    }


}

package negocio.facade.jdbc.basico.webservice;

import negocio.comuns.financeiro.MovParcelaVO;
import negocio.comuns.utilitarias.Uteis;
import org.json.JSONObject;
import org.w3c.dom.stylesheets.LinkStyle;

import java.util.ArrayList;
import java.util.List;

public class ParcelasRenegociadasJSON {

    private Double valorParcela;
    private int codigo;
    private String dataVencimento;
    private String descricao;

    public Double getValorParcela() {
        return valorParcela;
    }

    public void setValorParcela(Double valorParcela) {
        this.valorParcela = valorParcela;
    }

    public int getCodigo() {
        return codigo;
    }

    public void setCodigo(int codigo) {
        this.codigo = codigo;
    }

    public String getDataVencimento() {
        return dataVencimento;
    }

    public void setDataVencimento(String dataVencimento) {
        this.dataVencimento = dataVencimento;
    }

    public String getDescricao() {
        return descricao;
    }

    public void setDescricao(String descricao) {
        this.descricao = descricao;
    }

    public List<ParcelasRenegociadasJSON> fromJson(MovParcelaVO movParcelaVO){
        List<ParcelasRenegociadasJSON> parcelasRenegociadas = new ArrayList<>();
        JSONObject jsonParcelas = new JSONObject(movParcelaVO.getParcelasRenegociadas());
        for (int i = 0; i < jsonParcelas.getJSONArray("listaMovParcelaRenegociar").length(); i++) {

            JSONObject j = new JSONObject(jsonParcelas.getJSONArray("listaMovParcelaRenegociar").get(i).toString());

            ParcelasRenegociadasJSON parcelasRenegociadasJSON = new ParcelasRenegociadasJSON();
            parcelasRenegociadasJSON.setCodigo(j.getInt("codigo"));
            parcelasRenegociadasJSON.setDataVencimento(j.getString("dataVencimento"));
            parcelasRenegociadasJSON.setDescricao(j.getString("descricao"));
            parcelasRenegociadasJSON.setValorParcela(Uteis.arredondarForcando2CasasDecimais(j.getDouble("valorParcela")));
            parcelasRenegociadas.add(parcelasRenegociadasJSON);
        }
        return parcelasRenegociadas;
    }
}

package negocio.facade.jdbc.basico.webservice.sesice;

import negocio.comuns.utilitarias.UteisValidacao;

public enum TiposStatusClienteSesiCeEnum {

    STATUS_SESI_MATRICULADO (1,"MA", "Matriculado"),
    STATUS_SESI_CANCELADO (2, "CA", "Cancelado"),
    STATUS_SESI_EVADIDO (3, "EV", "Evadido");

    private Integer id;
    private String sigla;
    private String descricao;

    TiposStatusClienteSesiCeEnum(Integer id, String sigla, String descricao){
        this.id = id;
        this.sigla = sigla;
        this.descricao = descricao;
    }

    public static TiposStatusClienteSesiCeEnum obterConsultarPorDescricao(String descricao) {
        if (!UteisValidacao.emptyString(descricao)) {
            for (TiposStatusClienteSesiCeEnum tipo : values()) {
                if (tipo.getDescricao().toUpperCase().equals(descricao.toUpperCase())) {
                    return tipo;
                }
            }
        }
        return null;
    }

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getSigla() {
        return sigla;
    }

    public String getDescricao() {
        return descricao;
    }

    public void setSigla(String sigla) {
        this.sigla = sigla;
    }

    public void setDescricao(String descricao) {
        this.descricao = descricao;
    }
}

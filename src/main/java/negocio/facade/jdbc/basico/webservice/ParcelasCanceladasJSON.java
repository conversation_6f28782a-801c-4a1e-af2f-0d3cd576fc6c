package negocio.facade.jdbc.basico.webservice;

import br.com.pactosolucoes.comuns.json.SuperJSON;

public class ParcelasCanceladasJSON extends SuperJSON {
    private int contrato;
    private int codigoParcela;
    private String dataCancelamento;
    private String dataVencimento;
    private Double valorTotal;
    private String matricula;
    private String nomeCliente;
    private String responsavelLancamento;

    public int getContrato() {
        return contrato;
    }

    public void setContrato(int contrato) {
        this.contrato = contrato;
    }

    public int getCodigoParcela() {
        return codigoParcela;
    }

    public void setCodigoParcela(int codigoParcela) {
        this.codigoParcela = codigoParcela;
    }

    public String getDataCancelamento() {
        return dataCancelamento;
    }

    public void setDataCancelamento(String dataCancelamento) {
        this.dataCancelamento = dataCancelamento;
    }

    public String getDataVencimento() {
        return dataVencimento;
    }

    public void setDataVencimento(String dataVencimento) {
        this.dataVencimento = dataVencimento;
    }

    public Double getValorTotal() {
        return valorTotal;
    }

    public void setValorTotal(Double valorTotal) {
        this.valorTotal = valorTotal;
    }

    public String getMatricula() {
        return matricula;
    }

    public void setMatricula(String matricula) {
        this.matricula = matricula;
    }

    public String getNomeCliente() {
        return nomeCliente;
    }

    public void setNomeCliente(String nomeCliente) {
        this.nomeCliente = nomeCliente;
    }

    public String getResponsavelLancamento() {
        return responsavelLancamento;
    }

    public void setResponsavelLancamento(String responsavelLancamento) {
        this.responsavelLancamento = responsavelLancamento;
    }
}
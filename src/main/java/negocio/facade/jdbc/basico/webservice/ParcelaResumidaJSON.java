package negocio.facade.jdbc.basico.webservice;

import br.com.pactosolucoes.comuns.json.SuperJSON;

import java.util.List;

public class ParcelaResumidaJSON extends SuperJSON {

    private int codigo;
    private String descricao;
    private double valor;
    private String vencimento;
    private String dataLancamento;
    private String situacao;
    private String dataAlteracaoSituacao;
    private List<ParcelasRenegociadasJSON> parcelasRenegociadas;
    public List<ParcelasRenegociadasJSON> getParcelasRenegociadas() {
        return parcelasRenegociadas;
    }


    public void setParcelasRenegociadas(List<ParcelasRenegociadasJSON> parcelasRenegociadas) {
        this.parcelasRenegociadas = parcelasRenegociadas;
    }

    public String getDataAlteracaoSituacao() {
        return dataAlteracaoSituacao;
    }

    public void setDataAlteracaoSituacao(String dataAlteracaoSituacao) {
        this.dataAlteracaoSituacao = dataAlteracaoSituacao;
    }

    public int getCodigo() {
        return codigo;
    }

    public void setCodigo(int codigo) {
        this.codigo = codigo;
    }

    public String getDescricao() {
        return descricao;
    }

    public void setDescricao(String descricao) {
        this.descricao = descricao;
    }

    public double getValor() {
        return valor;
    }

    public void setValor(double valor) {
        this.valor = valor;
    }

    public String getVencimento() {
        return vencimento;
    }

    public void setVencimento(String vencimento) {
        this.vencimento = vencimento;
    }

    public String getSituacao() {
        return situacao;
    }

    public void setSituacao(String situacao) {
        this.situacao = situacao;
    }

    public String getDataLancamento() {
        return dataLancamento;
    }

    public void setDataLancamento(String dataLancamento) {
        this.dataLancamento = dataLancamento;
    }


}

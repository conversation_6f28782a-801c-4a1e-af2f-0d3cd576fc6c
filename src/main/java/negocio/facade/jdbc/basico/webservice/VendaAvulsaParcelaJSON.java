package negocio.facade.jdbc.basico.webservice;

import br.com.pactosolucoes.comuns.json.SuperJSON;

public class VendaAvulsaParcelaJSON extends SuperJSON {
    private int codigo;
    private int codigoParcelaAnterior;
    private String situacao;
    private String descricao;
    private String dataLancamento;
    private String dataVencimento;
    private Double valor;

    private String dataAlteracaoSituacao;

    public int getCodigo() {
        return codigo;
    }

    public void setCodigo(int codigo) {
        this.codigo = codigo;
    }

    public int getCodigoParcelaAnterior() {
        return codigoParcelaAnterior;
    }

    public void setCodigoParcelaAnterior(int codigoParcelaAnterior) {
        this.codigoParcelaAnterior = codigoParcelaAnterior;
    }

    public String getSituacao() {
        return situacao;
    }

    public void setSituacao(String situacao) {
        this.situacao = situacao;
    }

    public String getDescricao() {
        return descricao;
    }

    public void setDescricao(String descricao) {
        this.descricao = descricao;
    }

    public String getDataLancamento() {
        return dataLancamento;
    }

    public void setDataLancamento(String dataLancamento) {
        this.dataLancamento = dataLancamento;
    }

    public String getDataVencimento() {
        return dataVencimento;
    }

    public void setDataVencimento(String dataVencimento) {
        this.dataVencimento = dataVencimento;
    }

    public Double getValor() {
        return valor;
    }

    public void setValor(Double valor) {
        this.valor = valor;
    }

    public String getDataAlteracaoSituacao() {
        return dataAlteracaoSituacao;
    }

    public void setDataAlteracaoSituacao(String dataAlteracaoSituacao) {
        this.dataAlteracaoSituacao = dataAlteracaoSituacao;
    }
}

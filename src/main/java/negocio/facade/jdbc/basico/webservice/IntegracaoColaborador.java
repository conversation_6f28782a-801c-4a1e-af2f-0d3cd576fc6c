package negocio.facade.jdbc.basico.webservice;

import br.com.pactosolucoes.ce.comuns.enumerador.DiaSemana;
import br.com.pactosolucoes.enumeradores.TipoColaboradorEnum;
import org.json.JSONObject;
import negocio.comuns.acesso.DadosAcessoOfflineVO;
import negocio.comuns.arquitetura.*;
import negocio.comuns.basico.*;
import negocio.comuns.feed.PerfilUsuarioEnum;
import negocio.comuns.utilitarias.Calendario;
import negocio.comuns.utilitarias.ConsistirException;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisValidacao;
import negocio.facade.jdbc.arquitetura.PerfilAcesso;
import negocio.facade.jdbc.arquitetura.SuperFacadeJDBC;
import negocio.facade.jdbc.arquitetura.Usuario;
import negocio.facade.jdbc.basico.*;

import java.security.MessageDigest;
import java.sql.Connection;
import java.sql.ResultSet;
import java.util.*;

public class IntegracaoColaborador extends IntegracaoCadastros {

    public IntegracaoColaborador() throws Exception {
    }

    public IntegracaoColaborador(Connection conexao) throws Exception {
        super(conexao);
    }

    public UsuarioJSON gravarColaboradorIntegracao(String dados) throws Exception {
        JSONObject json = new JSONObject(dados);
        ResultSet rsex = SuperFacadeJDBC.criarConsulta("select codigo from colaborador where idexterno = '" + json.optString("codigoProtheus").trim() + "'", con);
        if (rsex.next()) {
            return null;
        }

        Colaborador dao = new Colaborador(con);
        String tipo = json.optString("tipo");

        if (UteisValidacao.emptyString(json.optString("email").trim())) {
            throw new Exception("Email é obrigatório");
        }
        if (UteisValidacao.emptyString(json.optString("telefone").trim()) && UteisValidacao.emptyString(json.optString("celular").trim())) {
            throw new Exception("Telefone ou celular são obrigatórios");
        }
        ColaboradorVO colaboradorVO = new ColaboradorVO();
        TipoColaboradorVO tipoColaboradorVO = new TipoColaboradorVO();
        PerfilUsuarioEnum perfil = null;
        if (tipo.equals("G")) {
            tipoColaboradorVO.setDescricao(TipoColaboradorEnum.COORDENADOR.getSigla());
            perfil = PerfilUsuarioEnum.GERENTE;
        } else if (tipo.equals("R")) {
            tipoColaboradorVO.setDescricao(TipoColaboradorEnum.CONSULTOR.getSigla());
            perfil = PerfilUsuarioEnum.CONSULTOR;
        } else {
            tipoColaboradorVO.setDescricao(TipoColaboradorEnum.PROFESSOR.getSigla());
            perfil = PerfilUsuarioEnum.PROFESSOR;
        }
        colaboradorVO.getPessoa().setCfp(json.getString("cpf").trim());
        colaboradorVO.getPessoa().setNome(json.getString("nome").trim());
        colaboradorVO.getPessoa().setSexo(json.getString("sexo").trim());
        colaboradorVO.getPessoa().setDataNasc(Uteis.getDate(json.getString("dataNascimento").trim(), "yyyyMMdd"));
        colaboradorVO.setEmpresa(new EmpresaVO());
        ResultSet rs = SuperFacadeJDBC.criarConsulta("select codigo from empresa where idexterno = '" + json.optString("unidade") + "'", con);
        if (rs.next()) {
            colaboradorVO.getEmpresa().setCodigo(rs.getInt("codigo"));
        } else {
            throw new Exception("Problema ao obter a unidade do colaborador");
        }
        colaboradorVO.setSituacao("AT");
        colaboradorVO.setDiaVencimento(1);
        colaboradorVO.setPorcComissao(1.0);
        colaboradorVO.getListaTipoColaboradorVOs().add(tipoColaboradorVO);
        dao.incluirSemCommit(colaboradorVO);

        if (!UteisValidacao.emptyNumber(colaboradorVO.getCodigo())) {
            executarConsulta("update colaborador set idexterno = '" + json.optString("codigoProtheus").trim() + "' where codigo = "
                    + colaboradorVO.getCodigo(), con);
        }

        ResultSet rspes = SuperFacadeJDBC.criarConsulta("select pessoa from colaborador where codigo = " + colaboradorVO.getCodigo(), con);
        if (rspes.next()) {
            colaboradorVO.getPessoa().setCodigo(rspes.getInt("pessoa"));
        }else{
            throw new Exception("Problema ao obter a pessoa do colaborador");
        }

        EmailVO email = new EmailVO();
        email.setEmail(json.optString("email").trim());
        email.setPessoa(colaboradorVO.getPessoa().getCodigo());
        Email emaildao = new Email();
        emaildao.incluir(email);

        UsuarioEmailVO uemailVO = new UsuarioEmailVO();
        uemailVO.setEmail(json.optString("email").trim());

        Telefone teldao = new Telefone(con);
        if (!UteisValidacao.emptyString(json.optString("telefone").trim())) {
            TelefoneVO telefone = new TelefoneVO();
            telefone.setNumero(json.optString("ddd").trim() + json.optString("telefone").trim());
            telefone.setPessoa(colaboradorVO.getPessoa().getCodigo());
            telefone.setTipoTelefone("RE");
            teldao.incluir(telefone);
        }

        if (!UteisValidacao.emptyString(json.optString("celular").trim())) {
            TelefoneVO celular = new TelefoneVO();
            celular.setNumero(json.optString("ddd").trim() + json.optString("celular").trim());
            celular.setTipoTelefone("CE");
            celular.setPessoa(colaboradorVO.getPessoa().getCodigo());
            teldao.incluir(celular);
        }

        JSONObject jsonEndereco = json.getJSONObject("endereco");
        if (!UteisValidacao.emptyString(jsonEndereco.optString("endereco"))) {
            EnderecoVO enderecoVO = new EnderecoVO();
            enderecoVO.setEndereco(jsonEndereco.optString("endereco"));
            enderecoVO.setNumero(jsonEndereco.optString("numero"));
            enderecoVO.setBairro(jsonEndereco.optString("bairro"));
            enderecoVO.setPessoa(colaboradorVO.getPessoa().getCodigo());
            Endereco enderecodao = new Endereco(con);
            enderecodao.incluir(enderecoVO);
        }



        UsuarioVO usuario = new UsuarioVO();
        usuario.setUsername(json.getString("username"));
        usuario.setColaboradorVO(colaboradorVO);
        usuario.setUsuarioPerfilAcessoVOs(new ArrayList());
        usuario.setEmail(uemailVO.getEmail());
        usuario.setEmailVO(email);

        String uuid = UUID.randomUUID().toString();
        String senhaRandomica = uuid.substring(0, uuid.indexOf("-"));
        usuario.setSenha(senhaRandomica);
        System.out.println(senhaRandomica);
        UsuarioJSON usuarioJSON = new UsuarioJSON(usuario);
        UsuarioPerfilAcessoVO usuarioPerfilAcessoVO = new UsuarioPerfilAcessoVO();
        usuarioPerfilAcessoVO.setEmpresa(colaboradorVO.getEmpresa());
        PerfilAcesso perfilAcessoDao = new PerfilAcesso(con);
        PerfilAcessoVO perfilAcessoVO = perfilAcessoDao.consultarPorTipo(perfil, false, Uteis.NIVELMONTARDADOS_DADOSENTIDADESUBORDINADAS);
        usuarioPerfilAcessoVO.setPerfilAcesso(perfilAcessoVO);
        usuario.setNome(colaboradorVO.getPessoa().getNome());
        usuarioJSON.setNome(colaboradorVO.getPessoa().getNome());
        usuario.getUsuarioPerfilAcessoVOs().add(usuarioPerfilAcessoVO);
        usuario.setTipoUsuario("CE");
        if(usuario.getUsername().length() > 10){
            usuario.setUsername(usuario.getUsername().substring(0, 10));
            usuarioJSON.setUsername(usuario.getUsername());
        }
        gravarSimples(usuario);
        return usuarioJSON;

    }

    public void gravarSimples(UsuarioVO usuarioVO) throws Exception {
        Usuario usuarioDao = new Usuario(con);
        //SOLICITACAO MAXIMILIANO NÃO ALTERAR -- TICKET #3319!!!
        if (!Uteis.removerEspacosInicioFimString(usuarioVO.getSenha()).equals(usuarioVO.getSenha())) {
            throw new Exception("A senha não pode conter espaços no início ou no final");
        }
        //SOLICITACAO MAXIMILIANO NÃO ALTERAR -- TICKET #3319!!!
        if (!Uteis.removerEspacosInicioFimString(usuarioVO.getUsername()).equals(usuarioVO.getUsername())) {
            throw new Exception("O nome de usuário não pode conter espaços no início ou no final");
        }

        if (usuarioVO.getEmailVO().getEmail() == null || usuarioVO.getEmailVO().getEmail().trim().isEmpty()) {
            throw new Exception("O campo EMAIL (Usuário) deve ser informado.");
        }
        Map<String, List<HorarioAcessoSistemaVO>> mapHorariosAcessoSistema = preencherHorariosAcessosSistema("00:00", "23:59");
        usuarioVO.setUsuarioHorarioAcessoSistemaVOs(new ArrayList());
        for (List<HorarioAcessoSistemaVO> listaHorarios : mapHorariosAcessoSistema.values()) {
            Iterator i = listaHorarios.iterator();
            while (i.hasNext()) {
                HorarioAcessoSistemaVO objExistente = (HorarioAcessoSistemaVO) i.next();
                usuarioVO.getUsuarioHorarioAcessoSistemaVOs().add(objExistente);
            }
        }
        if (usuarioVO.isNovoObj()) {
            usuarioVO.setSenhaNaoCriptografada(usuarioVO.getSenha());
            usuarioVO.setDataUltimaAlteracaoSenha(Calendario.hoje());
            usuarioDao.incluir(usuarioVO);
        } else {
            usuarioDao.alterar(usuarioVO);
        }
    }

    public Map<String, List<HorarioAcessoSistemaVO>> preencherHorariosAcessosSistema(String horaInicial, String horaFinal) throws Exception {

        HorarioAcessoSistema hrdao = new HorarioAcessoSistema(con);
        List<HorarioAcessoSistemaVO> listaDomingo = new ArrayList<HorarioAcessoSistemaVO>();
        List<HorarioAcessoSistemaVO> listaSegunda = new ArrayList<HorarioAcessoSistemaVO>();
        List<HorarioAcessoSistemaVO> listaTerca = new ArrayList<HorarioAcessoSistemaVO>();
        List<HorarioAcessoSistemaVO> listaQuarta = new ArrayList<HorarioAcessoSistemaVO>();
        List<HorarioAcessoSistemaVO> listaQuinta = new ArrayList<HorarioAcessoSistemaVO>();
        List<HorarioAcessoSistemaVO> listaSexta = new ArrayList<HorarioAcessoSistemaVO>();
        List<HorarioAcessoSistemaVO> listaSabado = new ArrayList<HorarioAcessoSistemaVO>();
        List<String> listaDiasSemanaEscolhidos = new ArrayList<String>();
        Map<String, List<HorarioAcessoSistemaVO>> mapHorariosAcessoSistema = new HashMap<String, List<HorarioAcessoSistemaVO>>();
        HorarioAcessoSistemaVO hor = new HorarioAcessoSistemaVO();
        Integer codigoTemp = hrdao.consultarUltimoCodigo();
        hor.setCodigo(codigoTemp++);
        hor.setDiaSemana(DiaSemana.DOMINGO);
        hor.setHoraInicial(horaInicial);
        hor.setHoraFinal(horaFinal);
        listaDomingo.add(hor);
        mapHorariosAcessoSistema.put(DiaSemana.DOMINGO.getDescricao(), listaDomingo);
        listaDiasSemanaEscolhidos.add(DiaSemana.DOMINGO.getDescricao());

        HorarioAcessoSistemaVO hor2 = new HorarioAcessoSistemaVO();
        hor2.setCodigo(codigoTemp++);
        hor2.setDiaSemana(DiaSemana.SEGUNDA_FEIRA);
        hor2.setHoraInicial(horaInicial);
        hor2.setHoraFinal(horaFinal);
        listaSegunda.add(hor2);
        mapHorariosAcessoSistema.put(DiaSemana.SEGUNDA_FEIRA.getDescricao(), listaSegunda);
        listaDiasSemanaEscolhidos.add(DiaSemana.SEGUNDA_FEIRA.getDescricao());

        HorarioAcessoSistemaVO hor3 = new HorarioAcessoSistemaVO();
        hor3.setCodigo(codigoTemp++);
        hor3.setDiaSemana(DiaSemana.TERCA_FEIRA);
        hor3.setHoraInicial(horaInicial);
        hor3.setHoraFinal(horaFinal);
        listaTerca.add(hor3);
        mapHorariosAcessoSistema.put(DiaSemana.TERCA_FEIRA.getDescricao(), listaTerca);
        listaDiasSemanaEscolhidos.add(DiaSemana.TERCA_FEIRA.getDescricao());

        HorarioAcessoSistemaVO hor4 = new HorarioAcessoSistemaVO();
        hor4.setCodigo(codigoTemp++);
        hor4.setDiaSemana(DiaSemana.QUARTA_FEIRA);
        hor4.setHoraInicial(horaInicial);
        hor4.setHoraFinal(horaFinal);
        listaQuarta.add(hor4);
        mapHorariosAcessoSistema.put(DiaSemana.QUARTA_FEIRA.getDescricao(), listaQuarta);
        listaDiasSemanaEscolhidos.add(DiaSemana.QUARTA_FEIRA.getDescricao());

        HorarioAcessoSistemaVO hor5 = new HorarioAcessoSistemaVO();
        hor5.setCodigo(codigoTemp++);
        hor5.setDiaSemana(DiaSemana.QUINTA_FEIRA);
        hor5.setHoraInicial(horaInicial);
        hor5.setHoraFinal(horaFinal);
        listaQuinta.add(hor5);
        mapHorariosAcessoSistema.put(DiaSemana.QUINTA_FEIRA.getDescricao(), listaQuinta);
        listaDiasSemanaEscolhidos.add(DiaSemana.QUINTA_FEIRA.getDescricao());

        HorarioAcessoSistemaVO hor6 = new HorarioAcessoSistemaVO();
        hor6.setCodigo(codigoTemp++);
        hor6.setDiaSemana(DiaSemana.SEXTA_FEIRA);
        hor6.setHoraInicial(horaInicial);
        hor6.setHoraFinal(horaFinal);
        listaSexta.add(hor6);
        mapHorariosAcessoSistema.put(DiaSemana.SEXTA_FEIRA.getDescricao(), listaSexta);
        listaDiasSemanaEscolhidos.add(DiaSemana.SEXTA_FEIRA.getDescricao());

        HorarioAcessoSistemaVO hor7 = new HorarioAcessoSistemaVO();
        hor7.setCodigo(codigoTemp++);
        hor7.setDiaSemana(DiaSemana.SABADO);
        hor7.setHoraInicial(horaInicial);
        hor7.setHoraFinal(horaFinal);
        listaSabado.add(hor7);
        mapHorariosAcessoSistema.put(DiaSemana.SABADO.getDescricao(), listaSabado);
        listaDiasSemanaEscolhidos.add(DiaSemana.SABADO.getDescricao());

        return mapHorariosAcessoSistema;
    }

}

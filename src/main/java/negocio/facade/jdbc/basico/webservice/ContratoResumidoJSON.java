package negocio.facade.jdbc.basico.webservice;

import br.com.pactosolucoes.comuns.json.SuperJSON;

import java.util.List;

public class ContratoResumidoJSON extends SuperJSON {

    private int codigo;
    private String versao;
    private String inicio;
    private String fim;
    private String lancamento;
    private String responsavelLancamento;
    private int responsavelLancamentoCodigo;
    private int duracao;
    private int qtdParcelas;
    private Double valordescontoespecifico;
    private Double valordescontoporcentagem;
    private int codConsultor;
    private String consultor;
    private ClienteResumidoJSON aluno;
    private List<ParcelaResumidaJSON> parcelas;
    private List<ModalidadeResumidaJSON> modalidades;
    private String unidade;
    private int unidadeCodigo;
    private boolean renovadoAutomaticamente;
    private String dataTerminoOriginal;

    public int getCodigo() {
        return codigo;
    }

    public void setCodigo(int codigo) {
        this.codigo = codigo;
    }

    public String getVersao() {
        return versao;
    }

    public void setVersao(String versao) {
        this.versao = versao;
    }

    public String getInicio() {
        return inicio;
    }

    public void setInicio(String inicio) {
        this.inicio = inicio;
    }

    public String getFim() {
        return fim;
    }

    public void setFim(String fim) {
        this.fim = fim;
    }

    public String getLancamento() {
        return lancamento;
    }

    public void setLancamento(String lancamento) {
        this.lancamento = lancamento;
    }

    public int getDuracao() {
        return duracao;
    }

    public void setDuracao(int duracao) {
        this.duracao = duracao;
    }

    public int getQtdParcelas() {
        return qtdParcelas;
    }

    public void setQtdParcelas(int qtdParcelas) {
        this.qtdParcelas = qtdParcelas;
    }

    public Double getValordescontoespecifico() {
        return valordescontoespecifico;
    }

    public void setValordescontoespecifico(Double valordescontoespecifico) {
        this.valordescontoespecifico = valordescontoespecifico;
    }

    public Double getValordescontoporcentagem() {
        return valordescontoporcentagem;
    }

    public void setValordescontoporcentagem(Double valordescontoporcentagem) {
        this.valordescontoporcentagem = valordescontoporcentagem;
    }

    public int getCodConsultor() {
        return codConsultor;
    }

    public void setCodConsultor(int codConsultor) {
        this.codConsultor = codConsultor;
    }

    public String getConsultor() {
        return consultor;
    }

    public void setConsultor(String consultor) {
        this.consultor = consultor;
    }

    public ClienteResumidoJSON getAluno() {
        return aluno;
    }

    public void setAluno(ClienteResumidoJSON aluno) {
        this.aluno = aluno;
    }

    public List<ParcelaResumidaJSON> getParcelas() {
        return parcelas;
    }

    public void setParcelas(List<ParcelaResumidaJSON> parcelas) {
        this.parcelas = parcelas;
    }

    public String getUnidade() {
        return unidade;
    }

    public void setUnidade(String empresa) {
        this.unidade = empresa;
    }

    public int getUnidadeCodigo() {
        return unidadeCodigo;
    }

    public void setUnidadeCodigo(int unidadeCodigo) {
        this.unidadeCodigo = unidadeCodigo;
    }

    public boolean isRenovadoAutomaticamente() {
        return renovadoAutomaticamente;
    }

    public void setRenovadoAutomaticamente(boolean renovadoAutomaticamente) {
        this.renovadoAutomaticamente = renovadoAutomaticamente;
    }

    public String getResponsavelLancamento() {
        return responsavelLancamento;
    }

    public void setResponsavelLancamento(String responsavelLancamento) {
        this.responsavelLancamento = responsavelLancamento;
    }

    public int getResponsavelLancamentoCodigo() {
        return responsavelLancamentoCodigo;
    }

    public void setResponsavelLancamentoCodigo(int responsavelLancamentoCodigo) {
        this.responsavelLancamentoCodigo = responsavelLancamentoCodigo;
    }

    public List<ModalidadeResumidaJSON> getModalidades() {
        return modalidades;
    }

    public void setModalidades(List<ModalidadeResumidaJSON> modalidades) {
        this.modalidades = modalidades;
    }

    public String getDataTerminoOriginal() {
        return dataTerminoOriginal;
    }

    public void setDataTerminoOriginal(String dataTerminoOriginal) {
        this.dataTerminoOriginal = dataTerminoOriginal;
    }
}
package negocio.facade.jdbc.basico.webservice.sesice;

import negocio.comuns.utilitarias.Calendario;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisValidacao;
import org.json.JSONObject;

import java.text.ParseException;
import java.util.Date;

public class AlunoSesiCeJSON {

    private String nome;
    private String cpf;
    private String dataNascimento;
    private String sexo;
    private String necessidadesEspeciais;
    private String email;
    private String celular;
    private String dataValidadeCadastro;
    private String nomeTitular;
    private String cpfTitular;
    private String categoria;
    private Integer idTurma;
    private Integer idModalidade;
    private Integer idMatricula;
    private String statusMatricula;
    private String razaoSocial;
    private String dataInicioContrato;
    private String dataFimContrato;
    private String dataCancelamento;
    private String dataEvasao;
    private String xnumpro;
    private JSONObject dadosJson;

    public AlunoSesiCeJSON(JSONObject json) {
        this.dadosJson = json;
        this.nome = json.optString("nome");
        this.cpf = json.optString("cpf");
        this.dataNascimento = json.optString("data_nascimento");
        this.sexo = json.optString("sexo");
        this.necessidadesEspeciais = json.optString("necessidades_especiais");
        this.email = json.optString("email");
        this.celular = json.optString("celular");
        this.dataValidadeCadastro = json.optString("data_validade_cadastro");
        this.nomeTitular = json.optString("nome_titular");
        this.cpfTitular = json.optString("cpf_titular");
        this.categoria = json.optString("categoria");
        this.idTurma = json.optInt("id_turma");
        this.idModalidade = json.optInt("id_modalidade");
        this.idMatricula = json.optInt("id_matricula");
        this.statusMatricula = json.optString("status_matricula");
        this.razaoSocial = json.optString("razao_social");
        this.dataInicioContrato = json.optString("data_inicio_contrato");
        this.dataFimContrato = json.optString("data_fim_contrato");
        this.dataCancelamento = json.optString("data_cancelamento");
        this.dataEvasao = json.optString("data_evasao");
        this.xnumpro = json.optString("xnumpro");
    }

    public String getNome() {
        return nome;
    }

    public void setNome(String nome) {
        this.nome = nome;
    }

    public String getCpf() {
        return cpf;
    }

    public void setCpf(String cpf) {
        this.cpf = cpf;
    }

    public String getDataNascimento() {
        if (dataNascimento == null) {
            dataNascimento = "";
        }
        return dataNascimento;
    }

    public void setDataNascimento(String dataNascimento) {
        this.dataNascimento = dataNascimento;
    }

    public String getSexo() {
        return sexo;
    }

    public void setSexo(String sexo) {
        this.sexo = sexo;
    }

    public String getNecessidadesEspeciais() {
        return necessidadesEspeciais;
    }

    public void setNecessidadesEspeciais(String necessidadesEspeciais) {
        this.necessidadesEspeciais = necessidadesEspeciais;
    }

    public String getEmail() {
        return email;
    }

    public void setEmail(String email) {
        this.email = email;
    }

    public String getCelular() {
        if (celular == null) {
            celular = "";
        }
        return celular;
    }

    public void setCelular(String celular) {
        this.celular = celular;
    }

    public String getDataValidadeCadastro() {
        if (dataValidadeCadastro == null) {
            dataValidadeCadastro = "";
        }
        return dataValidadeCadastro;
    }

    public void setDataValidadeCadastro(String dataValidadeCadastro) {
        this.dataValidadeCadastro = dataValidadeCadastro;
    }

    public String getNomeTitular() {
        return nomeTitular;
    }

    public void setNomeTitular(String nomeTitular) {
        this.nomeTitular = nomeTitular;
    }

    public String getCpfTitular() {
        return cpfTitular;
    }

    public void setCpfTitular(String cpfTitular) {
        this.cpfTitular = cpfTitular;
    }

    public String getCategoria() {
        return categoria;
    }

    public void setCategoria(String categoria) {
        this.categoria = categoria;
    }

    public Integer getIdTurma() {
        return idTurma;
    }

    public void setIdTurma(Integer idTurma) {
        this.idTurma = idTurma;
    }

    public Integer getIdModalidade() {
        return idModalidade;
    }

    public void setIdModalidade(Integer idModalidade) {
        this.idModalidade = idModalidade;
    }

    public Integer getIdMatricula() {
        return idMatricula;
    }

    public void setIdMatricula(Integer idMatricula) {
        this.idMatricula = idMatricula;
    }

    public String getStatusMatricula() {
        return statusMatricula;
    }

    public void setStatusMatricula(String statusMatricula) {
        this.statusMatricula = statusMatricula;
    }

    public String getRazaoSocial() {
        return razaoSocial;
    }

    public void setRazaoSocial(String razaoSocial) {
        this.razaoSocial = razaoSocial;
    }

    public String getDataInicioContrato() {
        return dataInicioContrato;
    }

    public void setDataInicioContrato(String dataInicioContrato) {
        this.dataInicioContrato = dataInicioContrato;
    }

    public String getDataFimContrato() {
        return dataFimContrato;
    }

    public void setDataFimContrato(String dataFimContrato) {
        this.dataFimContrato = dataFimContrato;
    }

    public String getDataCancelamento() {
        return dataCancelamento;
    }

    public void setDataCancelamento(String dataCancelamento) {
        this.dataCancelamento = dataCancelamento;
    }

    public String getDataEvasao() {
        return dataEvasao;
    }

    public void setDataEvasao(String dataEvasao) {
        this.dataEvasao = dataEvasao;
    }

    public String getXnumpro() {
        return xnumpro;
    }

    public void setXnumpro(String xnumpro) {
        this.xnumpro = xnumpro;
    }

    public JSONObject getDadosJson() {
        return dadosJson;
    }

    public void setDadosJson(JSONObject dadosJson) {
        this.dadosJson = dadosJson;
    }

    public void validarDados() throws Exception {
        if (UteisValidacao.emptyNumber(getIdMatricula())) {
            throw new Exception("Matrícula do aluno está vazia.");
        }
        if (UteisValidacao.emptyString(getNome())) {
            throw new Exception("Nome do aluno está vazio.");
        }
        if (!UteisValidacao.emptyString(getCpf()) && Uteis.removerMascara(getCpf()).length() != 11) {
            throw new Exception("CPF inválido. CPF: " + getCpf());
        }

        if (!UteisValidacao.emptyString(getDataNascimento())
                && !getDataNascimento().split(" ")[0].matches("\\d{4}-\\d{2}-\\d{2}")) {
            throw new Exception("Data de nascimento inválida. Data de nascimento: " + getDataNascimento());
        }

        TiposStatusClienteSesiCeEnum status = TiposStatusClienteSesiCeEnum.obterConsultarPorDescricao(getStatusMatricula());

        if (status == null) {
            throw new Exception("Status da matrícula inválido.");
        }

        if (status.getSigla().equals(TiposStatusClienteSesiCeEnum.STATUS_SESI_MATRICULADO.getSigla())) {
            if (UteisValidacao.emptyString(getXnumpro())) {
                throw new Exception("Xnumpro está vazio.");
            }
            if (UteisValidacao.emptyString(getDataInicioContrato())) {
                throw new Exception("Data de início do contrato está vazia.");
            }
            if (UteisValidacao.emptyString(getDataFimContrato())) {
                throw new Exception("Data fim do contrato está vazia.");
            }
            // verificar se data inicio e fim estão no padrão aaaa-MM-dd
            if (!getDataInicioContrato().split(" ")[0].matches("\\d{4}-\\d{2}-\\d{2}")) {
                throw new Exception("A data de início do contrato não está no padrão aaaa-MM-dd. Data informada: " + getDataInicioContrato());
            }
            if (!getDataFimContrato().split(" ")[0].matches("\\d{4}-\\d{2}-\\d{2}")) {
                throw new Exception("A data de fim do contrato não está no padrão aaaa-MM-dd. Data informada: " + getDataFimContrato());
            }
        }
        if (status.getSigla().equals(TiposStatusClienteSesiCeEnum.STATUS_SESI_CANCELADO.getSigla())) {
            if (UteisValidacao.emptyString(getDataCancelamento())) {
                throw new Exception("Data de cancelamento está vazia.");
            }
            if (!getDataCancelamento().split(" ")[0].matches("\\d{4}-\\d{2}-\\d{2}")) {
                throw new Exception("A data de cancelamento não está no padrão aaaa-MM-dd. Data informada: " + getDataCancelamento());
            }
        }
        if (status.getSigla().equals(TiposStatusClienteSesiCeEnum.STATUS_SESI_EVADIDO.getSigla())) {
            if (UteisValidacao.emptyString(getDataEvasao())) {
                throw new Exception("Data de evasão está vazia.");
            }
            if (!getDataEvasao().split(" ")[0].matches("\\d{4}-\\d{2}-\\d{2}")) {
                throw new Exception("A data de evasão não está no padrão aaaa-MM-dd. Data informada: " + getDataEvasao());
            }
        }
    }

    public TiposStatusClienteSesiCeEnum obterStatusCliente() {
        return TiposStatusClienteSesiCeEnum.obterConsultarPorDescricao(getStatusMatricula());
    }

    public Date obterDataCancelamento() throws Exception {
        try {
            return obterStatusCliente().getSigla().equals(TiposStatusClienteSesiCeEnum.STATUS_SESI_CANCELADO.getSigla())
                    ? Calendario.getDate("yyyy-MM-dd", getDataCancelamento()) : Calendario.getDate("yyyy-MM-dd", getDataEvasao());
        } catch (Exception ex) {
            throw new Exception("Erro ao obter data de cancelamento. Erro: " + ex.getMessage());
        }
    }
}

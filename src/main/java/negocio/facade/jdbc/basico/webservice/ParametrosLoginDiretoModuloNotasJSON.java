package negocio.facade.jdbc.basico.webservice;

import br.com.pactosolucoes.comuns.json.SuperJSON;
import org.json.JSONObject;

/**
 * Respons\u00E1vel por concentrar os par\u00E2metros necess\u00E1rios para a realiza\u00E7\u00E3o do login direto ao m\u00F3dulo de notas.
 *
 * <AUTHOR>
 * @since 20/11/2018
 */
public class ParametrosLoginDiretoModuloNotasJSON extends SuperJSON {

    private String nomeUsuario = "";
    private String chaveNFSe = "";
    private Boolean permissaoAlterarRPS = false;
    private Boolean administrador = false;
    private Boolean usarNFCe = false;
    private Boolean usarNFSe = false;

    public ParametrosLoginDiretoModuloNotasJSON(String json) {
        this(new JSONObject(json));
    }

    public ParametrosLoginDiretoModuloNotasJSON(JSONObject json) {
        nomeUsuario = json.optString( "nomeUsuario");
        chaveNFSe = json.optString( "chaveNFSe");
        permissaoAlterarRPS = json.optBoolean( "permissaoAlterarRPS");
        administrador = json.optBoolean( "administrador");
        usarNFCe = json.optBoolean( "usarNFCe");
        usarNFSe = json.optBoolean( "usarNFSe");
    }

    public String getChaveNFSe() {
        return chaveNFSe;
    }

    public void setChaveNFSe(String chaveNFSe) {
        this.chaveNFSe = chaveNFSe;
    }

    public boolean getPermissaoAlterarRPS() {
        return permissaoAlterarRPS;
    }

    public void setPermissaoAlterarRPS(Boolean permissaoAlterarRPS) {
        this.permissaoAlterarRPS = permissaoAlterarRPS;
    }

    public String getNomeUsuario() {
        return nomeUsuario;
    }

    public void setNomeUsuario(String nomeUsuario) {
        this.nomeUsuario = nomeUsuario;
    }

    public boolean isAdministrador() {
        return administrador;
    }

    public void setAdministrador(Boolean administrador) {
        this.administrador = administrador;
    }

    public Boolean isUsarNFCe() {
        return usarNFCe;
    }

    public void setUsarNFCe(Boolean usarNFCe) {
        this.usarNFCe = usarNFCe;
    }

    public boolean isUsarNFSe() {
        return usarNFSe;
    }

    public void setUsarNFSe(Boolean usarNFSe) {
        this.usarNFSe = usarNFSe;
    }
}

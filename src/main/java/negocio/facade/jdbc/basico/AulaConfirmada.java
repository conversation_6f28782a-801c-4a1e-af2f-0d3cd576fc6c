package negocio.facade.jdbc.basico;

import br.com.pactosolucoes.agendatotal.json.AgendamentoConfirmadoJSON;
import br.com.pactosolucoes.enumeradores.OrigemSistemaEnum;
import negocio.comuns.utilitarias.Calendario;
import negocio.comuns.utilitarias.ConsistirException;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisValidacao;
import negocio.facade.jdbc.arquitetura.SuperEntidade;
import negocio.interfaces.basico.AulaConfirmadaInterfaceFacade;

import java.sql.*;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * Created by Rafael on 20/12/2016.
 */
public class AulaConfirmada extends SuperEntidade implements AulaConfirmadaInterfaceFacade {

    public AulaConfirmada() throws Exception {
    }

    public AulaConfirmada(Connection conexao) throws Exception {
        super(conexao);
    }

    public void validarDados(AulaConfirmadaVO obj) throws Exception {

        if (obj.getDia() == null) {
            throw new ConsistirException("Dia da Aula Confirmada deve ser informada.");
        }
        if (obj.getHorarioTurmaVO() == null || obj.getHorarioTurmaVO().getCodigo() == 0) {
            throw new ConsistirException("Hor?rio Turma para a Aula Confirmada deve ser informado.");
        }
        if (obj.getClienteVO() == null || obj.getClienteVO().getCodigo() == 0) {
            throw new ConsistirException("Cliente deve ser informado.");
        }

    }
    public void incluir(final AulaConfirmadaVO obj) throws Exception{
        try {
            con.setAutoCommit(false);
            incluirSemCommit(obj);
            con.commit();
        } catch (Exception e) {
            con.rollback();
            throw e;
        } finally {
            con.setAutoCommit(true);
            obj.setCodigo(obterValorChavePrimariaCodigo());
            obj.setNovoObj(false);
        }
    }

    public void incluirSemCommit(final AulaConfirmadaVO obj) throws Exception{
        StringBuilder sql = new StringBuilder();
        sql.append("INSERT INTO aulaconfirmada( cliente, colaborador, horario, diaaula, \n");
        sql.append("            datalancamento, origem, autorizado, autorizadoGestaoRede, codAcessoAutorizado, matriculaAutorizado)\n");
        sql.append("    VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?);\n");
        PreparedStatement sqlInserir = con.prepareStatement(sql.toString());
        int i = 0;
        sqlInserir = resolveFKNull(sqlInserir, ++i, obj.getClienteVO().getCodigo());
        sqlInserir = resolveFKNull(sqlInserir, ++i, obj.getColaboradorVO().getCodigo());
        sqlInserir.setInt(++i, obj.getHorarioTurmaVO().getCodigo());
        sqlInserir.setDate(++i, Uteis.getDataJDBC(obj.getDia()));
        sqlInserir.setTimestamp(++i, Uteis.getDataJDBCTimestamp(obj.getDataLancamento()));
        sqlInserir.setString(++i, obj.getOrigemSistemaEnum().name());
        sqlInserir = resolveFKNull(sqlInserir, ++i, obj.getAutorizado());
        sqlInserir.setBoolean(++i, obj.getAutorizadoGestaoRede());
        sqlInserir.setString(++i, obj.getCodAcessoAutorizado());
        sqlInserir = resolveFKNull(sqlInserir, ++i, obj.getMatriculaAutorizado());

        sqlInserir.execute();
        obj.setCodigo(obterValorChavePrimariaCodigo());
        obj.setNovoObj(false);
    }

    public List<AulaConfirmadaVO> consultarAulaConfirmada(int cliente, int horarioTurma, Date dia , int nivelMontarDados) throws Exception{
       StringBuilder sql = new StringBuilder();
       sql.append(" SELECT * from aulaConfirmada\n");
       sql.append(" WHERE cliente = ? AND horarioTurma = ? and diaAula = ? ");
       PreparedStatement sqlConsultar = con.prepareStatement(sql.toString());
       int i = 0;
        sqlConsultar.setInt(i++,cliente);
        sqlConsultar.setInt(i++,horarioTurma);
        sqlConsultar.setDate(i++,Uteis.getDataJDBC(dia));
        return montarDadosConsulta(sqlConsultar.executeQuery(),nivelMontarDados);
    }

    public List<AulaConfirmadaVO> montarDadosConsulta(ResultSet tabelaResultado, int nivelMontarDados) throws Exception {
        List<AulaConfirmadaVO> vetResultado = new ArrayList<AulaConfirmadaVO>();
        while (tabelaResultado.next()) {
            AulaConfirmadaVO obj = montarDados(tabelaResultado,nivelMontarDados);
            vetResultado.add(obj);
        }
        return vetResultado;
    }

    public AulaConfirmadaVO montarDados(ResultSet dadosSQL, int nivelMontarDados) throws Exception{
        AulaConfirmadaVO obj = new AulaConfirmadaVO();
        obj.setCodigo(dadosSQL.getInt("codigo"));
        obj.getClienteVO().setCodigo(dadosSQL.getInt("cliente"));
        obj.getHorarioTurmaVO().setCodigo(dadosSQL.getInt("horarioturma"));
        obj.getColaboradorVO().setCodigo(dadosSQL.getInt("colaborador"));
        obj.setDataLancamento(dadosSQL.getTimestamp("datalancamento"));
        obj.setDia(dadosSQL.getTimestamp("diaAula"));
        obj.setOrigemSistemaEnum(OrigemSistemaEnum.valueOf(dadosSQL.getString("origemSistema")));
        obj.setNovoObj(false);
        if (nivelMontarDados == Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA){
            return obj;
        }
        montarHorarioTurma(obj,nivelMontarDados);
        montarColaborador(obj,Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA);

        return obj;
    }

    public void excluir(final AulaConfirmadaVO obj) throws Exception {
        try {
            con.setAutoCommit(false);
            String sql = "DELETE FROM aulaconfirmada WHERE cliente = ? and horario = ? and diaAula >= ? AND diaAula <= ?";
            if(!UteisValidacao.emptyNumber(obj.getPassivoVO().getCodigo())){
                sql = sql + " and passivo = ?";
            }
            int i = 1;
            PreparedStatement sqlExcluir = con.prepareStatement(sql);
            if(UteisValidacao.emptyNumber(obj.getClienteVO().getCodigo())){
                sqlExcluir.setNull(i++, Types.NULL);
            }else{
                sqlExcluir.setInt(i++, obj.getClienteVO().getCodigo());
            }
            sqlExcluir.setInt(i++, obj.getHorarioTurmaVO().getCodigo());
            sqlExcluir.setDate(i++ ,Uteis.getDataJDBC(Calendario.getDataComHoraZerada(obj.getDia())));
            sqlExcluir.setDate(i++ ,Uteis.getDataJDBC(Uteis.getDataComUltimaHora(obj.getDia())));
            if(!UteisValidacao.emptyNumber(obj.getPassivoVO().getCodigo())){
                sqlExcluir.setInt(i++, obj.getPassivoVO().getCodigo());
            }
            sqlExcluir.execute();
            con.commit();
        } catch (Exception e) {
            con.rollback();
            throw e;
        } finally {
            con.setAutoCommit(true);
        }
    }

    public void montarHorarioTurma(AulaConfirmadaVO obj, int nivelMontarDados) throws Exception{
        obj.setHorarioTurmaVO(getFacade().getHorarioTurma().consultarPorChavePrimaria(obj.getHorarioTurmaVO().getCodigo(), nivelMontarDados));
    }

    public void montarColaborador(AulaConfirmadaVO obj,int nivelMontarDados) throws Exception{
        if(obj.getColaboradorVO() != null && !UteisValidacao.emptyNumber(obj.getColaboradorVO().getCodigo())){
            obj.setColaboradorVO(getFacade().getColaborador().consultarPorChavePrimaria(obj.getColaboradorVO().getCodigo(), nivelMontarDados));
        }

    }

    @Override
    public List<AgendamentoConfirmadoJSON> consultarAgendamentosConfirmados(Date inicio, Date fim)throws Exception{
        StringBuilder sql = new StringBuilder();
        sql.append("select cliente, diaAula, horario from aulaconfirmada \n");
        sql.append("where diaAula BETWEEN ? and ? ");
        PreparedStatement pst = con.prepareStatement(sql.toString());
        pst.setTimestamp(1, Uteis.getDataJDBCTimestamp(Calendario.getDataComHoraZerada(inicio)));
        pst.setTimestamp(2, Uteis.getDataJDBCTimestamp(Calendario.getDataComHora(fim, "23:59:59")));
        ResultSet rs = pst.executeQuery();
        List<AgendamentoConfirmadoJSON> lista = new ArrayList<AgendamentoConfirmadoJSON>();
        while (rs.next()){
            AgendamentoConfirmadoJSON json = new AgendamentoConfirmadoJSON();
            json.setCodigoCliente(rs.getInt("cliente"));
            json.setIdAgendamento(rs.getInt("horario")+"_"+Uteis.getData(rs.getDate("diaAula"), "ddMMyy"));
            lista.add(json);
        }
        return lista;

    }

    @Override
    public List<AulaConfirmadaVO> consultarAulaConfirmadaPorData(Date dia) throws Exception {
       List<AulaConfirmadaVO> lista = new ArrayList<AulaConfirmadaVO>();
       Statement stm = con.createStatement();
       StringBuilder sql = new StringBuilder();
       sql.append(" SELECT * from aulaConfirmada\n");
       sql.append(" WHERE cast(datalancamento as date) = '"+Uteis.getDataJDBC(dia)+"'");
       ResultSet rs = stm.executeQuery(sql.toString());
       
        while (rs.next()) {            
            AulaConfirmadaVO obj = new AulaConfirmadaVO();
            obj.setCodigo(rs.getInt("codigo"));
            obj.getClienteVO().setCodigo(rs.getInt("cliente"));
            obj.setDataLancamento(rs.getTimestamp("datalancamento"));
            obj.setDia(rs.getTimestamp("diaAula"));
            obj.getHorarioTurmaVO().setCodigo(rs.getInt("horario"));
            obj.setOrigemSistemaEnum(OrigemSistemaEnum.valueOf(rs.getString("origem")));
            obj.getColaboradorVO().setCodigo(rs.getInt("colaborador"));
            montarHorarioTurma(obj,Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA);
            montarColaborador(obj,Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA);
            lista.add(obj);
        }
       
       return lista;
    }

}

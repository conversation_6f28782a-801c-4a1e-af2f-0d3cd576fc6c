package negocio.facade.jdbc.basico;

import org.json.JSONObject;
import negocio.comuns.basico.*;
import negocio.comuns.basico.enumerador.TipoBVEnum;
import negocio.comuns.basico.enumerador.TipoServicoEnum;
import negocio.comuns.utilitarias.*;
import negocio.facade.jdbc.arquitetura.SuperEntidade;
import negocio.interfaces.basico.QuestionarioInterfaceFacade;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.sql.Statement;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Hashtable;
import java.util.List;

/**
 * Classe de persistência que encapsula todas as operações de manipulação dos
 * dados da classe
 * <code>QuestionarioVO</code>. Responsável por implementar operações como
 * incluir, alterar, excluir e consultar pertinentes a classe
 * <code>QuestionarioVO</code>. Encapsula toda a interação com o banco de dados.
 *
 * @see QuestionarioVO
 * @see SuperEntidade
 */
public class Questionario extends SuperEntidade implements QuestionarioInterfaceFacade {

    private Hashtable questionarioPerguntas;

    public Questionario() throws Exception {
        super();
        setQuestionarioPerguntas(new Hashtable());
    }

    public Questionario(Connection con) throws Exception {
        super(con);
        setQuestionarioPerguntas(new Hashtable());

    }

    public QuestionarioVO novo() throws Exception {
        incluir(getIdEntidade());
        return new QuestionarioVO();
    }

    public void incluir(QuestionarioVO obj) throws Exception {
        this.incluir(obj, false);
    }

    /**
     * Operação responsável por incluir no banco de dados um objeto da classe
     * <code>QuestionarioVO</code>. Primeiramente valida os dados (
     * <code>validarDados</code>) do objeto. Verifica a conexão com o banco de
     * dados e a permissão do usuário para realizar esta operacão na entidade.
     * Isto, através da operação
     * <code>incluir</code> da superclasse.
     *
     * @param obj Objeto da classe <code>QuestionarioVO</code> que será gravado
     * no banco de dados.
     * @exception Exception Caso haja problemas de conexão, restrição de acesso
     * ou validação de dados.
     */
    public void incluir(QuestionarioVO obj, boolean centralEventos) throws Exception {
        try {
            con.setAutoCommit(false);
            if (centralEventos) {
//            	incluirObj(getIdEntidade());
            }
            incluirSemCommit(obj);
            con.commit();
        } catch (Exception e) {
            con.rollback();
            con.setAutoCommit(true);
            throw e;
        } finally {
            con.setAutoCommit(true);
        }
    }

    public void incluirSemCommit(QuestionarioVO obj) throws Exception {
            QuestionarioVO.validarDados(obj);
            obj.realizarUpperCaseDados();
            String sql = "INSERT INTO Questionario( titulopesquisa, tipoquestionario, fundoCor, fundoImagem, textoInicio, textoFim, somenteUmaResposta, ativo, nomeinterno) VALUES (?, ?, ?, ?, ?, ?, ?, ?,?)";
            PreparedStatement sqlInserir = con.prepareStatement(sql);
            sqlInserir.setString(1, obj.getTituloPesquisa());
            sqlInserir.setString(2, obj.getTipoQuestionario());
            sqlInserir.setString(3, obj.getFundoCor());
            sqlInserir.setString(4, obj.getFundoImagem());
            sqlInserir.setString(5, obj.getTextoInicio());
            sqlInserir.setString(6, obj.getTextoFim());
            sqlInserir.setBoolean(7, obj.isSomenteUmaResposta());
            sqlInserir.setBoolean(8, obj.isAtivo());
            sqlInserir.setString(9, obj.getNomeInterno());
            sqlInserir.execute();
            obj.setCodigo(obterValorChavePrimariaCodigo());
            obj.setNovoObj(false);
            Ordenacao.ordenarLista(obj.getQuestionarioPerguntaVOs(), "nrQuestao");
            getFacade().getQuestionarioPergunta().incluirQuestionarioPerguntas(obj.getCodigo(), obj.getQuestionarioPerguntaVOs());
        }




    /* (non-Javadoc)
     * @see negocio.interfaces.basico.QuestionarioInterfaceFacade#incluir(negocio.comuns.basico.QuestionarioVO)
     */
    public void alterar(QuestionarioVO obj) throws Exception {
        this.alterar(obj, false);
    }

    /**
     * Operação responsável por alterar no BD os dados de um objeto da classe
     * <code>QuestionarioVO</code>. Sempre utiliza a chave primária da classe
     * como atributo para localização do registro a ser alterado. Primeiramente
     * valida os dados (
     * <code>validarDados</code>) do objeto. Verifica a conexão com o banco de
     * dados e a permissão do usuário para realizar esta operacão na entidade.
     * Isto, através da operação
     * <code>alterar</code> da superclasse.
     *
     * @param obj Objeto da classe <code>QuestionarioVO</code> que será alterada
     * no banco de dados.
     * @exception Exception Caso haja problemas de conexão, restrição de acesso
     * ou validação de dados.
     */
    public void alterar(QuestionarioVO obj, boolean centralEventos) throws Exception {
        try {
            con.setAutoCommit(false);
            alterarSemCommit(obj);
            con.commit();
        } catch (Exception e) {
            con.rollback();
            con.setAutoCommit(true);
            throw e;
        } finally {
            con.setAutoCommit(true);
            }
    }

    public void alterarSemCommit(QuestionarioVO obj) throws Exception {
        QuestionarioVO.validarDados(obj);
        obj.realizarUpperCaseDados();
        String sql = "UPDATE Questionario SET titulopesquisa=?, tipoquestionario = ?, fundoCor = ?, fundoImagem = ?, textoInicio = ?, textoFim = ?, somenteUmaResposta = ?, ativo = ?, nomeinterno = ? WHERE codigo = ?;";
        PreparedStatement sqlAlterar = con.prepareStatement(sql);
        sqlAlterar.setString(1, obj.getTituloPesquisa());
        sqlAlterar.setString(2, obj.getTipoQuestionario());
        sqlAlterar.setString(3, obj.getFundoCor());
        sqlAlterar.setString(4, obj.getFundoImagem());
        sqlAlterar.setString(5, obj.getTextoInicio());
        sqlAlterar.setString(6, obj.getTextoFim());
        sqlAlterar.setBoolean(7, obj.isSomenteUmaResposta());
        sqlAlterar.setBoolean(8, obj.isAtivo());
        sqlAlterar.setString(9, obj.getNomeInterno());
        sqlAlterar.setInt(10, obj.getCodigo());
        sqlAlterar.execute();
        getFacade().getQuestionarioPergunta().alterarQuestionarioPerguntas(obj.getCodigo(), obj.getQuestionarioPerguntaVOs());
    }

    /* (non-Javadoc)
     * @see negocio.interfaces.basico.QuestionarioInterfaceFacade#incluir(negocio.comuns.basico.QuestionarioVO)
     */

    public void excluir(QuestionarioVO obj) throws Exception {
        this.excluir(obj, false);
    }

    /**
     * Operação responsável por excluir no BD um objeto da classe
     * <code>QuestionarioVO</code>. Sempre localiza o registro a ser excluído
     * através da chave primária da entidade. Primeiramente verifica a conexão
     * com o banco de dados e a permissão do usuário para realizar esta operacão
     * na entidade. Isto, através da operação
     * <code>excluir</code> da superclasse.
     *
     * @param obj Objeto da classe <code>QuestionarioVO</code> que será removido
     * no banco de dados.
     * @exception Exception Caso haja problemas de conexão ou restrição de
     * acesso.
     */
    public void excluir(QuestionarioVO obj, boolean centralEventos) throws Exception {
        try {
            con.setAutoCommit(false);
            if (centralEventos) {
//            	excluirObj(getIdEntidade());
            } else {
                excluir(getIdEntidade());
            }
            String sql = "DELETE FROM Questionario WHERE ((codigo = ?))";
            PreparedStatement sqlExcluir = con.prepareStatement(sql);
            sqlExcluir.setInt(1, obj.getCodigo());
            sqlExcluir.execute();
            getFacade().getQuestionarioPergunta().excluirQuestionarioPerguntas(obj.getCodigo());
            con.commit();
        } catch (Exception e) {
            con.rollback();
            con.setAutoCommit(true);
            throw e;
        } finally {
            con.setAutoCommit(true);
        }
    }

    /**
     * Responsável por realizar uma consulta de
     * <code>Questionario</code> através do valor do atributo
     * <code>String descricao</code>. Retorna os objetos, com início do valor do
     * atributo idêntico ao parâmetro fornecido. Faz uso da operação
     * <code>montarDadosConsulta</code> que realiza o trabalho de prerarar o
     * List resultante.
     *
     * @param controlarAcesso Indica se a aplicação deverá verificar se o
     * usuário possui permissão para esta consulta ou não.
     * @return List Contendo vários objetos da
     * classe <code>QuestionarioVO</code> resultantes da consulta.
     * @exception Exception Caso haja problemas de conexão ou restrição de
     * acesso.
     */
    public List consultarPorDescricao(String valorConsulta, String tipoQuestionario, boolean controlarAcesso, int nivelMontarDados) throws Exception {
        consultar(getIdEntidade(), controlarAcesso);
        StringBuilder sb = new StringBuilder();
        sb.append("SELECT\n");
        sb.append("  *\n");
        sb.append("FROM Questionario\n");
        sb.append("WHERE 1 = 1\n");
        if (!UteisValidacao.emptyString(valorConsulta)) {
            sb.append("      AND upper(nomeinterno) LIKE ('").append(valorConsulta.toUpperCase()).append("%')\n");
        }
        if (!UteisValidacao.emptyString(tipoQuestionario)) {
            sb.append("      AND tipoquestionario = '").append(tipoQuestionario).append("'\n");
        }
        sb.append("ORDER BY nomeinterno");
        try (Statement stm = con.createStatement()) {
            try (ResultSet tabelaResultado = stm.executeQuery(sb.toString())) {
                return (montarDadosConsulta(tabelaResultado, nivelMontarDados, this.con));
            }
        }
    }

    public QuestionarioVO consultarPorDescricao(String valorConsulta, int nivelMontarDados) throws Exception {
        String sqlStr = "SELECT * FROM Questionario WHERE upper( nomeinterno ) like('" + valorConsulta.toUpperCase() + "') ORDER BY nomeinterno";
        try (Statement stm = con.createStatement()) {
            try (ResultSet tabelaResultado = stm.executeQuery(sqlStr)) {
                if (!tabelaResultado.next()) {
                    throw new ConsistirException("Dados Não Encontrados ( Questionario - "+valorConsulta+" ).");
                }
                return montarDados(tabelaResultado, nivelMontarDados, con);
            }
        }
    }

    /**
     * Responsável por realizar uma consulta de
     * <code>Questionario</code> através do valor do atributo
     * <code>Integer codigo</code>. Retorna os objetos com valores iguais ou
     * superiores ao parâmetro fornecido. Faz uso da operação
     * <code>montarDadosConsulta</code> que realiza o trabalho de prerarar o
     * List resultante.
     *
     * @param controlarAcesso Indica se a aplicação deverá verificar se o
     * usuário possui permissão para esta consulta ou não.
     * @return List Contendo vários objetos da
     * classe <code>QuestionarioVO</code> resultantes da consulta.
     * @exception Exception Caso haja problemas de conexão ou restrição de
     * acesso.
     */
    public List consultarPorCodigo(Integer valorConsulta, boolean controlarAcesso, int nivelMontarDados) throws Exception {
        consultar(getIdEntidade(), controlarAcesso);
        String sqlStr = "SELECT * FROM Questionario WHERE codigo >= " + valorConsulta + " ORDER BY codigo";
        try (Statement stm = con.createStatement()) {
            try (ResultSet tabelaResultado = stm.executeQuery(sqlStr)) {
                return (montarDadosConsulta(tabelaResultado, nivelMontarDados, this.con));
            }
        }
    }

    public QuestionarioVO consultarPorCodigoExato(Integer valorConsulta, boolean controlarAcesso, int nivelMontarDados) throws Exception {
        String sqlStr = "SELECT * FROM Questionario WHERE codigo = " + valorConsulta + " ORDER BY codigo";
        QuestionarioVO questionario;
        try (Statement stm = con.createStatement()) {
            try (ResultSet tabelaResultado = stm.executeQuery(sqlStr)) {
                questionario = null;
                if (tabelaResultado.next()) {
                    questionario = montarDados(tabelaResultado, nivelMontarDados, this.con);
                }
            }
        }
        return questionario;
    }

    /**
     * Responsável por montar os dados de vários objetos, resultantes de uma
     * consulta ao banco de dados (
     * <code>ResultSet</code>). Faz uso da operação
     * <code>montarDados</code> que realiza o trabalho para um objeto por vez.
     *
     * @return List Contendo vários objetos da
     * classe <code>QuestionarioVO</code> resultantes da consulta.
     */
    public static List montarDadosConsulta(ResultSet tabelaResultado, int nivelMontarDados, Connection con) throws Exception {
        List<QuestionarioVO> vetResultado = new ArrayList<>();
        while (tabelaResultado.next()) {
            QuestionarioVO obj = montarDados(tabelaResultado, nivelMontarDados, con);
            vetResultado.add(obj);
        }
        return vetResultado;
    }

    /**
     * Responsável por montar os dados resultantes de uma consulta ao banco de
     * dados (
     * <code>ResultSet</code>) em um objeto da classe
     * <code>QuestionarioVO</code>.
     *
     * @return O objeto da classe <code>QuestionarioVO</code> com os dados
     * devidamente montados.
     */
    public static QuestionarioVO montarDados(ResultSet dadosSQL, int nivelMontarDados, Connection con) throws Exception {
        QuestionarioVO obj = new QuestionarioVO();
        obj.setCodigo(dadosSQL.getInt("codigo"));
        obj.setNomeInterno(dadosSQL.getString("nomeinterno"));

        if (nivelMontarDados == Uteis.NIVELMONTARDADOS_MINIMOS) {
            return obj;
        }

        obj.setTituloPesquisa(dadosSQL.getString("titulopesquisa"));
        obj.setTipoQuestionario(dadosSQL.getString("tipoQuestionario"));
        obj.setFundoCor(dadosSQL.getString("fundoCor"));
        obj.setFundoImagem(dadosSQL.getString("fundoImagem"));
        obj.setTextoInicio(dadosSQL.getString("textoInicio"));
        obj.setTextoFim(dadosSQL.getString("textoFim"));
        obj.setSomenteUmaResposta(dadosSQL.getBoolean("somenteUmaResposta"));
        obj.setAtivo(dadosSQL.getBoolean("ativo"));
        obj.setNovoObj(false);

//        if (nivelMontarDados == Uteis.NIVELMONTARDADOS_DADOSBASICOS) {
//            return obj;
//        }

        QuestionarioPergunta questionarioPergunta = new QuestionarioPergunta(con);
        obj.setQuestionarioPerguntaVOs(questionarioPergunta.consultarQuestionarioPerguntas(obj.getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS));
        questionarioPergunta = null;
        if (nivelMontarDados == Uteis.NIVELMONTARDADOS_DADOSENTIDADESUBORDINADAS) {
            return obj;
        }

        return obj;
    }

    /**
     * Operação responsável por localizar um objeto da classe
     * <code>QuestionarioVO</code> através de sua chave primária.
     *
     * @exception Exception Caso haja problemas de conexão ou localização do
     * objeto procurado.
     */
    public QuestionarioVO consultarPorChavePrimaria(Integer codigoPrm, int nivelMontarDados) throws Exception {
        consultar(getIdEntidade(), false);
        String sql = "SELECT * FROM Questionario WHERE codigo = ?";
        try (PreparedStatement sqlConsultar = con.prepareStatement(sql)) {
            sqlConsultar.setInt(1, codigoPrm);
            try (ResultSet tabelaResultado = sqlConsultar.executeQuery()) {
                if (!tabelaResultado.next()) {
                    throw new ConsistirException("Dados Não Encontrados ( Questionario ).");
                }
                return (montarDados(tabelaResultado, nivelMontarDados, this.con));
            }
        }
    }

    public Hashtable getQuestionarioPerguntas() {
        return questionarioPerguntas;
    }

    public void setQuestionarioPerguntas(Hashtable questionarioPerguntas) {
        this.questionarioPerguntas = questionarioPerguntas;
    }

    public String consultarJSON(boolean cadastroPesquisa) throws Exception {

        StringBuilder json;
        boolean dados;
        try (ResultSet rs = getPS(cadastroPesquisa).executeQuery()) {
            json = new StringBuilder();
            json.append("{\"aaData\":[");
            dados = false;
            while (rs.next()) {
                dados = true;
                json.append("[\"").append(rs.getString("codigo")).append("\",");
                json.append("\"").append(Uteis.normalizarStringJSON(rs.getString("nomeinterno").trim())).append("\",");
                if (cadastroPesquisa) {
                    if (rs.getBoolean("ativo")) {
                        json.append("\"").append("Ativo").append("\"],");
                    } else {
                        json.append("\"").append("Inativo").append("\"],");
                    }
                } else {
                    json.append("\"").append(" ").append("\"],");
                }
            }
        }
        if (dados) {
            json.deleteCharAt(json.toString().length() - 1);
        }
        json.append("]}");
        return json.toString();
    }

    private PreparedStatement getPS(boolean cadastroPesquisa) throws SQLException {
        StringBuilder sql = new StringBuilder();
        sql.append("SELECT codigo, nomeinterno, ativo FROM questionario \n");
        if (cadastroPesquisa) {
            sql.append(" where tipoquestionario = '").append(TipoServicoEnum.PESQUISA.getTipo()).append("' \n");
        } else {
            sql.append(" where tipoquestionario <> '").append(TipoServicoEnum.PESQUISA.getTipo()).append("' \n");
        }
        sql.append(" ORDER BY nomeinterno");
        return con.prepareStatement(sql.toString());
    }

    public List consultarParaImpressao(String filtro, String ordem, String campoOrdenacao, int i, boolean cadastroPesquisa) throws SQLException {

        List lista;
        try (ResultSet rs = getPS(cadastroPesquisa).executeQuery()) {
            lista = new ArrayList();
            while (rs.next()) {
                QuestionarioVO q = new QuestionarioVO();
                String geral = rs.getString("codigo") + rs.getString("nomeinterno");
                if (geral.toLowerCase().contains(filtro.toLowerCase())) {
                    q.setCodigo(rs.getInt("codigo"));
                    q.setNomeInterno(rs.getString("nomeinterno"));
                    lista.add(q);
                }
            }
        }
        if (campoOrdenacao.equals("Código")) {
            Ordenacao.ordenarLista(lista, "codigo");
        } else if (campoOrdenacao.equals("Descrição")) {
            Ordenacao.ordenarLista(lista, "nomeinterno");
        }
        if (ordem.contains("desc")) {
            Collections.reverse(lista);
        }
        return lista;

    }

    public QuestionarioVO obterQuestionario(Integer empresa, String t) throws Exception{
        StringBuilder sql = new StringBuilder();
        sql.append(" SELECT q.* FROM questionario q \n");
        sql.append(" INNER JOIN empresa e ON e.");
        TipoBVEnum tipo = TipoBVEnum.valueOf(t);
        switch(tipo){
            case MA:
                sql.append("questionarioprimeiravisita");
                break;
            case RE:
                sql.append("questionariorematricula");
                break;
            case RT:
                sql.append("questionarioretorno");
                break;
        }
        sql.append(" = q.codigo \n");
        sql.append(" WHERE e.codigo = ").append(empresa);
        QuestionarioVO questionario;
        try (ResultSet rs = criarConsulta(sql.toString(), con)) {
            questionario = new QuestionarioVO();
            if (rs.next()) {
                questionario = montarDados(rs, Uteis.NIVELMONTARDADOS_TODOS, con);
            }
        }
        return questionario;
    }

    /**
     *
     * @param cliente
     * @param questionario
     * @param respostas
     * @return
     * @throws Exception
     */
    @Override
    public QuestionarioClienteVO responderQuestionario(Integer cliente, Integer questionario, JSONObject respostas, String tipo, boolean somenteValidar) throws Exception{
        QuestionarioVO questionarioVO = consultarPorChavePrimaria(questionario, Uteis.NIVELMONTARDADOS_TODOS);
        if(questionarioVO == null
                || UteisValidacao.emptyNumber(questionarioVO.getCodigo())){
            throw new Exception("Não foi possível encontrar o questionário.");
        }
        QuestionarioClienteVO respondido = new QuestionarioClienteVO();
        if(!somenteValidar){
            try (ResultSet rs = criarConsulta("select consultorsite from empresa e\n" +
                    "inner join cliente c ON c.empresa = e.codigo\n" +
                    "where c.codigo = " + cliente, con)) {
                if (rs.next()) {
                    respondido.setConsultor(new ColaboradorVO());
                    respondido.getConsultor().setCodigo(rs.getInt("consultorsite"));
                } else {
                    throw new Exception("Não foi possível encontrar o consultor padrão.");
                }
            }
        }


        respondido.setData(Calendario.hoje());
        respondido.setUltimaAtualizacao(Calendario.hoje());
        respondido.setQuestionario(questionarioVO);
        respondido.setTipoBV(TipoBVEnum.valueOf(tipo));
        respondido.setCliente(new ClienteVO());
        respondido.getCliente().setCodigo(cliente);
        respondido.setQuestionarioPerguntaClienteVOs(new ArrayList());
        Ordenacao.ordenarLista(questionarioVO.getQuestionarioPerguntaVOs(), "nrQuestao");
        for(QuestionarioPerguntaVO qp : questionarioVO.getQuestionarioPerguntaVOs()){
            QuestionarioPerguntaClienteVO qpc = new QuestionarioPerguntaClienteVO();
            PerguntaClienteVO perguntaCliente = new PerguntaClienteVO();
            perguntaCliente.setTextual(qp.getPergunta().getTipoPergunta().equals("TE"));
            perguntaCliente.setTipoPergunta(qp.getPergunta().getTipoPergunta());
            perguntaCliente.setDescricao(qp.getPergunta().getDescricao());
            perguntaCliente.setMultipla(qp.getPergunta().getTipoPergunta().equals("ME"));
            perguntaCliente.setSimples(qp.getPergunta().getTipoPergunta().equals("SN") || qp.getPergunta().getTipoPergunta().equals("SE"));
            perguntaCliente.setRespostaPergClienteVOs(new ArrayList());

            if(qp.getPergunta().getTipoPergunta().equals("TE")){
                String resposta = respostas.optString("resp"+qp.getPergunta().getCodigo());
                RespostaPergClienteVO rpc = new RespostaPergClienteVO();
                rpc.setRespostaOpcao(false);
                rpc.setDescricaoRespota(resposta);
                perguntaCliente.getRespostaPergClienteVOs().add(rpc);
            }else{
                for(RespostaPerguntaVO rp : qp.getPergunta().getRespostaPerguntaVOs()){
                    String resposta = respostas.optString("resp"+rp.getCodigo()+"-"+qp.getPergunta().getCodigo());
                    RespostaPergClienteVO rpc = new RespostaPergClienteVO();
                    rpc.setRespostaOpcao(Boolean.valueOf(resposta.toUpperCase()));
                    rpc.setDescricaoRespota(rp.getDescricaoRespota());
                    perguntaCliente.getRespostaPergClienteVOs().add(rpc);
                }
            }
            qpc.setPerguntaCliente(perguntaCliente);
            respondido.getQuestionarioPerguntaClienteVOs().add(qpc);
        }
        if(somenteValidar){
            for (QuestionarioPerguntaClienteVO q : new ArrayList<QuestionarioPerguntaClienteVO>(respondido.getQuestionarioPerguntaClienteVOs())) {
                if (q.getPerguntaCliente().getSimples() || q.getPerguntaCliente().getMultipla()) {
                    int cont = 0;
                    for(RespostaPergClienteVO respostaPergCliente : new ArrayList<RespostaPergClienteVO>(q.getPerguntaCliente().getRespostaPergClienteVOs())){
                        if (respostaPergCliente.getRespostaOpcao()) {
                            cont = 1;
                        }
                    }
                    if (cont == 0) {
                        throw new ConsistirException("Marque uma das ALTERNATIVAS  Para Pergunta " + q.getPerguntaCliente().getDescricao().toString() + " !.");
                    }
                }
            }
        }else{
            QuestionarioCliente qcDao = new QuestionarioCliente(con);
            qcDao.incluir(respondido, Boolean.FALSE);
        }
        return respondido;
    }

    public void alterarFundoImagem(QuestionarioVO obj) throws Exception {
        String sql = "UPDATE Questionario SET fundoImagem = ? WHERE codigo = ?;";
        try (PreparedStatement sqlAlterar = con.prepareStatement(sql)) {
            sqlAlterar.setString(1, obj.getFundoImagem());
            sqlAlterar.setInt(2, obj.getCodigo());
            sqlAlterar.execute();
        }
    }


    public List<QuestionarioVO> consultarQuestionariosPorTipo(TipoServicoEnum tipoServicoEnum, boolean somenteAtivos, int nivelMontarDados) throws Exception {
        consultar(getIdEntidade(), false);
        String sql = " select * from questionario where 1 = 1 ";
        if (tipoServicoEnum != null) {
            sql += " and tipoquestionario = '" + tipoServicoEnum.getTipo() + "' ";
        }
        if (somenteAtivos) {
            sql += " and ativo = true ";
        }
        try (Statement stm = con.createStatement()) {
            try (ResultSet tabelaResultado = stm.executeQuery(sql)) {
                return (montarDadosConsulta(tabelaResultado, nivelMontarDados, this.con));
            }
        }
    }
}

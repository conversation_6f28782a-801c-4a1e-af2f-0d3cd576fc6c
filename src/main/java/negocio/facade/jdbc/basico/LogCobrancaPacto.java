/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package negocio.facade.jdbc.basico;

import negocio.comuns.arquitetura.PaginadorDTO;
import negocio.comuns.basico.LogCobrancaPactoVO;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisValidacao;
import negocio.facade.jdbc.arquitetura.SuperEntidade;
import negocio.facade.jdbc.arquitetura.SuperFacadeJDBC;
import negocio.interfaces.basico.LogCobrancaPactoInterfaceFacade;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 */
public class LogCobrancaPacto extends SuperEntidade implements LogCobrancaPactoInterfaceFacade {

    public LogCobrancaPacto() throws Exception {
        super();
    }

    public LogCobrancaPacto(Connection conexao) throws Exception {
        super(conexao);
    }

    public static List<LogCobrancaPactoVO> montarDadosConsulta(ResultSet tabelaResultado, int nivelMontarDados, Connection con) throws Exception {
        List<LogCobrancaPactoVO> vetResultado = new ArrayList<LogCobrancaPactoVO>();
        while (tabelaResultado.next()) {
            LogCobrancaPactoVO obj = montarDados(tabelaResultado, nivelMontarDados, con);
            vetResultado.add(obj);
        }
        return vetResultado;
    }

    public static LogCobrancaPactoVO montarDadosBasico(ResultSet dadosSQL, int nivelMontarDados, Connection con) throws Exception {
        LogCobrancaPactoVO obj = new LogCobrancaPactoVO();
        obj.setNovoObj(false);
        obj.setCodigo(dadosSQL.getInt("codigo"));
        obj.setDataCobranca(Uteis.getDataJDBCTimestamp(dadosSQL.getTimestamp("dataCobranca")));
        obj.setQuantidade(dadosSQL.getInt("quantidade"));
        obj.setValorTotal(dadosSQL.getDouble("valorTotal"));
        obj.setQtdParcelas(dadosSQL.getInt("qtdParcelas"));
        obj.setGerarNota(dadosSQL.getBoolean("gerarNota"));
        obj.setNomeUsuarioOAMD(dadosSQL.getString("nomeUsuarioOAMD"));
        obj.setEmpresa(dadosSQL.getInt("empresa"));
        obj.setJustificativa(dadosSQL.getString("justificativa"));
        obj.setGerarCobrancaFinanceiro(dadosSQL.getBoolean("gerarCobrancaFinanceiro"));
        obj.setObservacao(dadosSQL.getString("observacao"));
        obj.setItensCobrancaPacto(dadosSQL.getString("itensCobrancaPacto"));
        obj.setJsonCobranca(dadosSQL.getString("jsonCobranca"));
        obj.setTipoCobrancaPacto(dadosSQL.getInt("tipocobrancapacto"));
        obj.setTabelaCreditoPacto(dadosSQL.getString("tabelaCreditoPacto"));
        obj.setValorMedioUnitario(dadosSQL.getDouble("valorMedioUnitario"));
        return obj;
    }

    public static LogCobrancaPactoVO montarDados(ResultSet dadosSQL, int nivelMontarDados, Connection con) throws Exception {
        return montarDadosBasico(dadosSQL, nivelMontarDados, con);
    }

    public void incluir(LogCobrancaPactoVO obj) throws Exception {
        String sql = "INSERT INTO logcobrancapacto (dataCobranca, quantidade, valorTotal, qtdParcelas, gerarNota, nomeUsuarioOAMD, empresa, " +
                "justificativa, gerarCobrancaFinanceiro, observacao, itensCobrancaPacto, jsonCobranca, tipocobrancapacto, tabelaCreditoPacto) " +
                " VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?);";
        PreparedStatement sqlInserir = con.prepareStatement(sql);
        int i = 0;
        sqlInserir.setTimestamp(++i, Uteis.getDataJDBCTimestamp(obj.getDataCobranca()));
        sqlInserir.setInt(++i, obj.getQuantidade());
        sqlInserir.setDouble(++i, obj.getValorTotal());
        sqlInserir.setInt(++i, obj.getQtdParcelas());
        sqlInserir.setBoolean(++i, obj.isGerarNota());
        sqlInserir.setString(++i, obj.getNomeUsuarioOAMD());
        sqlInserir.setInt(++i, obj.getEmpresa());
        sqlInserir.setString(++i, obj.getJustificativa());
        sqlInserir.setBoolean(++i, obj.isGerarCobrancaFinanceiro());
        sqlInserir.setString(++i, obj.getObservacao());
        sqlInserir.setString(++i, obj.getItensCobrancaPacto());
        sqlInserir.setString(++i, obj.getJsonCobranca());
        sqlInserir.setInt(++i, obj.getTipoCobrancaPacto());
        sqlInserir.setString(++i, obj.getTabelaCreditoPacto());
        sqlInserir.execute();
        obj.setCodigo(obterValorChavePrimariaCodigo());
        obj.setNovoObj(false);
    }

    public List<LogCobrancaPactoVO> consultarTodas(Integer empresa, PaginadorDTO paginadorDTO, int nivelMontarDados) throws Exception {
        return consultar(empresa, null, null, null, paginadorDTO, nivelMontarDados);
    }

    public List<LogCobrancaPactoVO> consultar(Integer empresa, Date dataCobrancaInicio, Date dataCobrancaFinal,
                                              Integer limit, PaginadorDTO paginadorDTO, int nivelMontarDados) throws Exception {
        List<LogCobrancaPactoVO> objetos = new ArrayList<LogCobrancaPactoVO>();
        StringBuilder sql = new StringBuilder();
        sql.append("SELECT \n");
        sql.append("* \n");
        sql.append("FROM logcobrancapacto \n");
        sql.append("WHERE 1 = 1 \n");
        if (!UteisValidacao.emptyNumber(empresa)) {
            sql.append("and empresa = ").append(empresa).append(" \n");
        }
        if (dataCobrancaInicio != null) {
            sql.append("and datacobranca::date >= '").append(Uteis.getDataFormatoBD(dataCobrancaInicio)).append("' \n");
        }
        if (dataCobrancaFinal != null) {
            sql.append("and datacobranca::date <= '").append(Uteis.getDataFormatoBD(dataCobrancaFinal)).append("' \n");
        }
        sql.append("ORDER BY dataCobranca desc \n");


        if (paginadorDTO != null) {
            Integer total = SuperFacadeJDBC.contar("SELECT COUNT(*) FROM (" + sql + ") as qtd", con);

            paginadorDTO.setQuantidadeTotalElementos(total.longValue());

            int maxResults = paginadorDTO.getSize() == null ? 10 : paginadorDTO.getSize().intValue();
            int indiceInicial = paginadorDTO.getPage() == null ? 0 : paginadorDTO.getPage().intValue() * maxResults;
            paginadorDTO.setSize((long) maxResults);

            sql.append("LIMIT ").append(maxResults).append(" \n");
            sql.append("OFFSET ").append(indiceInicial).append(" \n");
        } else {
            if (!UteisValidacao.emptyNumber(limit)) {
                sql.append("LIMIT ").append(limit).append(" \n");
            }
        }

        PreparedStatement sqlConsulta = con.prepareStatement(sql.toString());
        ResultSet resultado = sqlConsulta.executeQuery();
        while (resultado.next()) {
            LogCobrancaPactoVO novoObj = montarDados(resultado, nivelMontarDados, con);
            objetos.add(novoObj);
        }
        return objetos;
    }

    public LogCobrancaPactoVO consultarChavePrimaria(Integer codigo, int nivelMontarDados) throws Exception {
        PreparedStatement sqlConsulta = con.prepareStatement("SELECT * FROM logcobrancapacto WHERE codigo = " + codigo.toString());
        ResultSet resultado = sqlConsulta.executeQuery();
        if (resultado.next()) {
            return montarDados(resultado, nivelMontarDados, con);
        }
        return null;
    }

    public void alterarValorMedioUnitario(LogCobrancaPactoVO obj) throws Exception {
        String sql = "update logcobrancapacto set valorMedioUnitario = ? where codigo = ?;";
        try (PreparedStatement sqlAlterar = con.prepareStatement(sql)) {
            sqlAlterar.setDouble(1, obj.getValorMedioUnitario());
            sqlAlterar.setInt(2, obj.getCodigo());
            sqlAlterar.execute();
        }
    }
}

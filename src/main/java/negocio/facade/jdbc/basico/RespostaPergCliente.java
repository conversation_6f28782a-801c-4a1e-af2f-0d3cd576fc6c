package negocio.facade.jdbc.basico;

import java.sql.Connection;
import java.util.Iterator;
import negocio.comuns.basico.RespostaPergClienteVO;
import negocio.facade.jdbc.arquitetura.*;
import java.sql.ResultSet;
import java.sql.PreparedStatement;
import java.sql.Statement;
import java.util.List;
import java.util.ArrayList;
import negocio.comuns.utilitarias.*;

/**
 * Classe de persistência que encapsula todas as operações de manipulação dos dados da classe <code>RespostaPergClienteVO</code>.
 * Responsável por implementar operações como incluir, alterar, excluir e consultar pertinentes a classe <code>RespostaPergClienteVO</code>.
 * Encapsula toda a interação com o banco de dados.
 * @see RespostaPergClienteVO
 * @see SuperEntidade
 * @see PerguntaCliente
 */
public class RespostaPergCliente extends SuperEntidade {    

    public RespostaPergCliente() throws Exception {
        super();
        setIdEntidade("Cliente");
    }

    public RespostaPergCliente(Connection connection) throws Exception {
        super(connection);
        setIdEntidade("Cliente");
    }

    /**
     * Operação responsável por retornar um novo objeto da classe <code>RespostaPergClienteVO</code>.
     */
    public RespostaPergClienteVO novo() throws Exception {
        incluir(getIdEntidade());
        RespostaPergClienteVO obj = new RespostaPergClienteVO();
        return obj;
    }

    /**
     * Operação responsável por incluir no banco de dados um objeto da classe <code>RespostaPergClienteVO</code>.
     * Primeiramente valida os dados (<code>validarDados</code>) do objeto. Verifica a conexão com o banco de dados e a permissão do usuário
     * para realizar esta operacão na entidade.
     * Isto, através da operação <code>incluir</code> da superclasse.
     * @param obj  Objeto da classe <code>RespostaPergClienteVO</code> que será gravado no banco de dados.
     * @exception Exception Caso haja problemas de conexão, restrição de acesso ou validação de dados.
     */
    public void incluir(RespostaPergClienteVO obj, Boolean validarQuestinario) throws Exception {
        incluir(obj, validarQuestinario, false);
    }
    public void incluir(RespostaPergClienteVO obj, Boolean validarQuestinario , boolean controlarAcesso) throws Exception {
        if (validarQuestinario) {
            RespostaPergClienteVO.validarDados(obj);
        }
        if(controlarAcesso){
            incluir(getIdEntidade());
        }
        obj.realizarUpperCaseDados();
        String sql = "INSERT INTO RespostaPergCliente( perguntaCliente, descricaoRespota, respostaOpcao ) VALUES ( ?, ?, ? )";
        PreparedStatement sqlInserir = con.prepareStatement(sql);
        if (obj.getPerguntaCliente().intValue() != 0) {
            sqlInserir.setInt(1, obj.getPerguntaCliente().intValue());
        } else {
            sqlInserir.setNull(1, 0);
        }
        sqlInserir.setString(2, obj.getDescricaoRespota());
        sqlInserir.setBoolean(3, obj.isRespostaOpcao().booleanValue());
        sqlInserir.execute();
        obj.setCodigo(obterValorChavePrimariaCodigo());
        obj.setNovoObj(new Boolean(false));
    }

    /**
     * Operação responsável por alterar no BD os dados de um objeto da classe <code>RespostaPergClienteVO</code>.
     * Sempre utiliza a chave primária da classe como atributo para localização do registro a ser alterado.
     * Primeiramente valida os dados (<code>validarDados</code>) do objeto. Verifica a conexão com o banco de dados e a permissão do usuário
     * para realizar esta operacão na entidade.
     * Isto, através da operação <code>alterar</code> da superclasse.
     * @param obj    Objeto da classe <code>RespostaPergClienteVO</code> que será alterada no banco de dados.
     * @exception Execption Caso haja problemas de conexão, restrição de acesso ou validação de dados.
     */
    public void alterar(RespostaPergClienteVO obj) throws Exception {
        RespostaPergClienteVO.validarDados(obj);
        alterar(getIdEntidade());
        obj.realizarUpperCaseDados();
        String sql = "UPDATE RespostaPergCliente set perguntaCliente=?, descricaoRespota=?, respostaOpcao=? WHERE ((codigo = ?))";
        PreparedStatement sqlAlterar = con.prepareStatement(sql);
        if (obj.getPerguntaCliente().intValue() != 0) {
            sqlAlterar.setInt(1, obj.getPerguntaCliente().intValue());
        } else {
            sqlAlterar.setNull(1, 0);
        }
        sqlAlterar.setString(2, obj.getDescricaoRespota());
        sqlAlterar.setBoolean(3, obj.isRespostaOpcao().booleanValue());
        sqlAlterar.setInt(4, obj.getCodigo().intValue());
        sqlAlterar.execute();
    }

    /**
     * Operação responsável por excluir no BD um objeto da classe <code>RespostaPergClienteVO</code>.
     * Sempre localiza o registro a ser excluído através da chave primária da entidade.
     * Primeiramente verifica a conexão com o banco de dados e a permissão do usuário
     * para realizar esta operacão na entidade.
     * Isto, através da operação <code>excluir</code> da superclasse.
     * @param obj    Objeto da classe <code>RespostaPergClienteVO</code> que será removido no banco de dados.
     * @exception Execption Caso haja problemas de conexão ou restrição de acesso.
     */
    public void excluir(RespostaPergClienteVO obj) throws Exception {
        excluir(getIdEntidade());
        String sql = "DELETE FROM RespostaPergCliente WHERE ((codigo = ?))";
        PreparedStatement sqlExcluir = con.prepareStatement(sql);
        sqlExcluir.setInt(1, obj.getCodigo().intValue());
        sqlExcluir.execute();
    }

    /**
     * Responsável por realizar uma consulta de <code>RespostaPergCliente</code> através do valor do atributo 
     * <code>String descricaoRespota</code>. Retorna os objetos, com início do valor do atributo idêntico ao parâmetro fornecido.
     * Faz uso da operação <code>montarDadosConsulta</code> que realiza o trabalho de prerarar o List resultante.
     * @param   controlarAcesso Indica se a aplicação deverá verificar se o usuário possui permissão para esta consulta ou não.
     * @return  List Contendo vários objetos da classe <code>RespostaPergClienteVO</code> resultantes da consulta.
     * @exception Exception Caso haja problemas de conexão ou restrição de acesso.
     */
    public List consultarPorDescricaoRespota(String valorConsulta, boolean controlarAcesso, int nivelMontarDados) throws Exception {
        consultar(getIdEntidade(), controlarAcesso);
        String sqlStr = "SELECT * FROM RespostaPergCliente WHERE upper( descricaoRespota ) like('" + valorConsulta.toUpperCase() + "%') ORDER BY descricaoRespota";
        Statement stm = con.createStatement();
        ResultSet tabelaResultado = stm.executeQuery(sqlStr);
        return (montarDadosConsulta(tabelaResultado, nivelMontarDados));
    }

    /**
     * Responsável por realizar uma consulta de <code>RespostaPergCliente</code> através do valor do atributo 
     * <code>descricao</code> da classe <code>PerguntaCliente</code>
     * Faz uso da operação <code>montarDadosConsulta</code> que realiza o trabalho de prerarar o List resultante.
     * @return  List Contendo vários objetos da classe <code>RespostaPergClienteVO</code> resultantes da consulta.
     * @exception Execption Caso haja problemas de conexão ou restrição de acesso.
     */
    public List consultarPorDescricaoPerguntaCliente(String valorConsulta, int nivelMontarDados) throws Exception {
        consultar(getIdEntidade(), true);
        String sqlStr = "SELECT RespostaPergCliente.* FROM RespostaPergCliente, PerguntaCliente WHERE RespostaPergCliente.perguntaCliente = PerguntaCliente.codigo and upper( PerguntaCliente.descricao ) like('" + valorConsulta.toUpperCase() + "%') ORDER BY PerguntaCliente.descricao";
        Statement stm = con.createStatement();
        ResultSet tabelaResultado = stm.executeQuery(sqlStr);
        return montarDadosConsulta(tabelaResultado, nivelMontarDados);
    }

    /**
     * Responsável por realizar uma consulta de <code>RespostaPergCliente</code> através do valor do atributo 
     * <code>Integer codigo</code>. Retorna os objetos com valores iguais ou superiores ao parâmetro fornecido.
     * Faz uso da operação <code>montarDadosConsulta</code> que realiza o trabalho de prerarar o List resultante.
     * @param   controlarAcesso Indica se a aplicação deverá verificar se o usuário possui permissão para esta consulta ou não.
     * @return  List Contendo vários objetos da classe <code>RespostaPergClienteVO</code> resultantes da consulta.
     * @exception Exception Caso haja problemas de conexão ou restrição de acesso.
     */
    public List consultarPorCodigo(Integer valorConsulta, boolean controlarAcesso, int nivelMontarDados) throws Exception {
        consultar(getIdEntidade(), controlarAcesso);
        String sqlStr = "SELECT * FROM RespostaPergCliente WHERE codigo >= " + valorConsulta.intValue() + " ORDER BY codigo";
        Statement stm = con.createStatement();
        ResultSet tabelaResultado = stm.executeQuery(sqlStr);
        return (montarDadosConsulta(tabelaResultado, nivelMontarDados));
    }

    /**
     * Responsável por montar os dados de vários objetos, resultantes de uma consulta ao banco de dados (<code>ResultSet</code>).
     * Faz uso da operação <code>montarDados</code> que realiza o trabalho para um objeto por vez.
     * @return  List Contendo vários objetos da classe <code>RespostaPergClienteVO</code> resultantes da consulta.
     */
    public static List montarDadosConsulta(ResultSet tabelaResultado, int nivelMontarDados) throws Exception {
        List vetResultado = new ArrayList();
        while (tabelaResultado.next()) {
            RespostaPergClienteVO obj = new RespostaPergClienteVO();
            obj = montarDados(tabelaResultado, nivelMontarDados);
            vetResultado.add(obj);
        }
        return vetResultado;
    }

    /**
     * Responsável por montar os dados resultantes de uma consulta ao banco de dados (<code>ResultSet</code>)
     * em um objeto da classe <code>RespostaPergClienteVO</code>.
     * @return  O objeto da classe <code>RespostaPergClienteVO</code> com os dados devidamente montados.
     */
    public static RespostaPergClienteVO montarDados(ResultSet dadosSQL, int nivelMontarDados) throws Exception {
        RespostaPergClienteVO obj = new RespostaPergClienteVO();
        obj.setCodigo(new Integer(dadosSQL.getInt("codigo")));
        obj.setPerguntaCliente(new Integer(dadosSQL.getInt("perguntaCliente")));
        obj.setDescricaoRespota(dadosSQL.getString("descricaoRespota"));
        obj.setRespostaOpcao(new Boolean(dadosSQL.getBoolean("respostaOpcao")));
        // obj.setRespostaTextual( dadosSQL.getString("respostaTextual"));
        obj.setNovoObj(new Boolean(false));
        if (nivelMontarDados == Uteis.NIVELMONTARDADOS_DADOSBASICOS) {
            return obj;
        }

        return obj;
    }

    /**
     * Operação responsável por excluir todos os objetos da <code>RespostaPergClienteVO</code> no BD.
     * Faz uso da operação <code>excluir</code> disponível na classe <code>RespostaPergCliente</code>.
     * @param <code>perguntaCliente</code> campo chave para exclusão dos objetos no BD.
     * @exception Exception  Erro de conexão com o BD ou restrição de acesso a esta operação.
     */
    public void excluirRespostaPergClientes(Integer perguntaCliente) throws Exception {
        excluirObj(getIdEntidade());
        String sql = "DELETE FROM RespostaPergCliente WHERE (perguntaCliente = ?)";
        PreparedStatement sqlExcluir = con.prepareStatement(sql);
        sqlExcluir.setInt(1, perguntaCliente.intValue());
        sqlExcluir.execute();
    }

    /**
     * Operação responsável por alterar todos os objetos da <code>RespostaPergClienteVO</code> contidos em um Hashtable no BD.
     * Faz uso da operação <code>excluirRespostaPergClientes</code> e <code>incluirRespostaPergClientes</code> disponíveis na classe <code>RespostaPergCliente</code>.
     * @param objetos  List com os objetos a serem alterados ou incluídos no BD.
     * @exception Exception  Erro de conexão com o BD ou restrição de acesso a esta operação.
     */
    public void alterarRespostaPergClientes(Integer perguntaCliente, List objetos, Boolean validarQuestionario) throws Exception {
        excluirRespostaPergClientes(perguntaCliente);
        incluirRespostaPergClientes(perguntaCliente, objetos, validarQuestionario);
    }

    /**
     * Operação responsável por incluir objetos da <code>RespostaPergClienteVO</code> no BD.
     * Garantindo o relacionamento com a entidade principal <code>basico.PerguntaCliente</code> através do atributo de vínculo.
     * @param objetos List contendo os objetos a serem gravados no BD da classe.
     * @exception Exception  Erro de conexão com o BD ou restrição de acesso a esta operação.
     */
    public void incluirRespostaPergClientes(Integer perguntaClientePrm, List objetos, Boolean validarQuestionario) throws Exception {
        incluirRespostaPergClientes(perguntaClientePrm, objetos, validarQuestionario, false);
    }

    public void incluirRespostaPergClientes(Integer perguntaClientePrm, List objetos, Boolean validarQuestionario, boolean controlarAcesso) throws Exception {
        Iterator e = objetos.iterator();
        while (e.hasNext()) {
            RespostaPergClienteVO obj = (RespostaPergClienteVO) e.next();
            obj.setPerguntaCliente(perguntaClientePrm);
            incluir(obj, validarQuestionario, controlarAcesso);
        }
    }

    /**
     * Operação responsável por consultar todos os <code>RespostaPergClienteVO</code> relacionados a um objeto da classe <code>basico.PerguntaCliente</code>.
     * @param perguntaCliente  Atributo de <code>basico.PerguntaCliente</code> a ser utilizado para localizar os objetos da classe <code>RespostaPergClienteVO</code>.
     * @return List  Contendo todos os objetos da classe <code>RespostaPergClienteVO</code> resultantes da consulta.
     * @exception Exception  Erro de conexão com o BD ou restrição de acesso a esta operação.
     */
    public List consultarRespostaPergClientes(Integer perguntaCliente, int nivelMontarDados) throws Exception {
        consultar(getIdEntidade());
        List objetos = new ArrayList();
        String sql = "SELECT * FROM RespostaPergCliente WHERE perguntaCliente = ?";
        PreparedStatement sqlConsulta = con.prepareStatement(sql);
        sqlConsulta.setInt(1, perguntaCliente.intValue());
        ResultSet resultado = sqlConsulta.executeQuery();
        while (resultado.next()) {
            RespostaPergClienteVO novoObj = new RespostaPergClienteVO();
            novoObj = RespostaPergCliente.montarDados(resultado, nivelMontarDados);
            objetos.add(novoObj);
        }
        return objetos;
    }

    /**
     * Operação responsável por localizar um objeto da classe <code>RespostaPergClienteVO</code>
     * através de sua chave primária. 
     * @exception Exception Caso haja problemas de conexão ou localização do objeto procurado.
     */
    public RespostaPergClienteVO consultarPorChavePrimaria(Integer codigoPrm, int nivelMontarDados) throws Exception {
        consultar(getIdEntidade(), false);
        String sql = "SELECT * FROM RespostaPergCliente WHERE codigo = ?";
        PreparedStatement sqlConsultar = con.prepareStatement(sql);
        sqlConsultar.setInt(1, codigoPrm.intValue());
        ResultSet tabelaResultado = sqlConsultar.executeQuery();
        if (!tabelaResultado.next()) {
            throw new ConsistirException("Dados Não Encontrados ( RespostaPergCliente ).");
        }
        return (montarDados(tabelaResultado, nivelMontarDados));
    }    
}

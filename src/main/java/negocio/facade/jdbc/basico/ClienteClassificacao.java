package negocio.facade.jdbc.basico;

import negocio.comuns.basico.ClassificacaoVO;
import java.util.Iterator;
import negocio.comuns.basico.ClienteClassificacaoVO;
import negocio.facade.jdbc.arquitetura.*;

import java.sql.Connection;
import java.sql.ResultSet;
import java.sql.PreparedStatement;
import java.sql.Statement;
import java.util.List;
import java.util.ArrayList;
import negocio.comuns.utilitarias.*;

/**
 * Classe de persistência que encapsula todas as operações de manipulação dos dados da classe <code>ClienteClassificacaoVO</code>.
 * Responsável por implementar operações como incluir, alterar, excluir e consultar pertinentes a classe <code>ClienteClassificacaoVO</code>.
 * Encapsula toda a interação com o banco de dados.
 * @see ClienteClassificacaoVO
 * @see SuperEntidade
 * @see Cliente
 */
public class ClienteClassificacao extends SuperEntidade {    

    public ClienteClassificacao() throws Exception {
        super();        
        setIdEntidade("Cliente");
    }

    public ClienteClassificacao(Connection con) throws Exception {
        super(con);        
        setIdEntidade("Cliente");
    
	}

	/**
     * Operação responsável por retornar um novo objeto da classe <code>ClienteClassificacaoVO</code>.
     */
    public ClienteClassificacaoVO novo() throws Exception {
        incluir(getIdEntidade());
        ClienteClassificacaoVO obj = new ClienteClassificacaoVO();
        return obj;
    }

    /**
     * Operação responsável por incluir no banco de dados um objeto da classe <code>ClienteClassificacaoVO</code>.
     * Primeiramente valida os dados (<code>validarDados</code>) do objeto. Verifica a conexão com o banco de dados e a permissão do usuário
     * para realizar esta operacão na entidade.
     * Isto, através da operação <code>incluir</code> da superclasse.
     * @param obj  Objeto da classe <code>ClienteClassificacaoVO</code> que será gravado no banco de dados.
     * @exception Exception Caso haja problemas de conexão, restrição de acesso ou validação de dados.
     */
    public void incluir(ClienteClassificacaoVO obj) throws Exception {
        ClienteClassificacaoVO.validarDados(obj);
        incluir(getIdEntidade());
        obj.realizarUpperCaseDados();
        String sql = "INSERT INTO ClienteClassificacao( cliente, classificacao ) VALUES ( ?, ? )";
        PreparedStatement sqlInserir = con.prepareStatement(sql);
        if (obj.getCliente().intValue() != 0) {
            sqlInserir.setInt(1, obj.getCliente().intValue());
        } else {
            sqlInserir.setNull(1, 0);
        }
        if (obj.getClassificacao().getCodigo().intValue() != 0) {
            sqlInserir.setInt(2, obj.getClassificacao().getCodigo().intValue());
        } else {
            sqlInserir.setNull(2, 0);
        }
        sqlInserir.execute();
        obj.setCodigo(obterValorChavePrimariaCodigo());
        obj.setNovoObj(new Boolean(false));
    }

    /**
     * Operação responsável por alterar no BD os dados de um objeto da classe <code>ClienteClassificacaoVO</code>.
     * Sempre utiliza a chave primária da classe como atributo para localização do registro a ser alterado.
     * Primeiramente valida os dados (<code>validarDados</code>) do objeto. Verifica a conexão com o banco de dados e a permissão do usuário
     * para realizar esta operacão na entidade.
     * Isto, através da operação <code>alterar</code> da superclasse.
     * @param obj    Objeto da classe <code>ClienteClassificacaoVO</code> que será alterada no banco de dados.
     * @exception Execption Caso haja problemas de conexão, restrição de acesso ou validação de dados.
     */
    public void alterar(ClienteClassificacaoVO obj) throws Exception {
        ClienteClassificacaoVO.validarDados(obj);
        alterar(getIdEntidade());
        obj.realizarUpperCaseDados();
        String sql = "UPDATE ClienteClassificacao set cliente=?, classificacao=? WHERE ((codigo = ?))";
        PreparedStatement sqlAlterar = con.prepareStatement(sql);
        if (obj.getCliente().intValue() != 0) {
            sqlAlterar.setInt(1, obj.getCliente().intValue());
        } else {
            sqlAlterar.setNull(1, 0);
        }
        if (obj.getClassificacao().getCodigo().intValue() != 0) {
            sqlAlterar.setInt(2, obj.getClassificacao().getCodigo().intValue());
        } else {
            sqlAlterar.setNull(2, 0);
        }
        sqlAlterar.setInt(3, obj.getCodigo().intValue());
        sqlAlterar.execute();
    }

    /**
     * Operação responsável por excluir no BD um objeto da classe <code>ClienteClassificacaoVO</code>.
     * Sempre localiza o registro a ser excluído através da chave primária da entidade.
     * Primeiramente verifica a conexão com o banco de dados e a permissão do usuário
     * para realizar esta operacão na entidade.
     * Isto, através da operação <code>excluir</code> da superclasse.
     * @param obj    Objeto da classe <code>ClienteClassificacaoVO</code> que será removido no banco de dados.
     * @exception Execption Caso haja problemas de conexão ou restrição de acesso.
     */
    public void excluir(ClienteClassificacaoVO obj) throws Exception {
        excluir(getIdEntidade());
        String sql = "DELETE FROM ClienteClassificacao WHERE ((codigo = ?))";
        PreparedStatement sqlExcluir = con.prepareStatement(sql);
        sqlExcluir.setInt(1, obj.getCodigo().intValue());
        sqlExcluir.execute();
    }

    /**
     * Responsável por realizar uma consulta de <code>ClienteClassificacao</code> através do valor do atributo 
     * <code>nome</code> da classe <code>Classificacao</code>
     * Faz uso da operação <code>montarDadosConsulta</code> que realiza o trabalho de prerarar o List resultante.
     * @return  List Contendo vários objetos da classe <code>ClienteClassificacaoVO</code> resultantes da consulta.
     * @exception Execption Caso haja problemas de conexão ou restrição de acesso.
     */
    public List consultarPorNomeClassificacao(String valorConsulta, int nivelMontarDados) throws Exception {
        consultar(getIdEntidade(), true);
        String sqlStr = "SELECT ClienteClassificacao.* FROM ClienteClassificacao, Classificacao WHERE ClienteClassificacao.classificacao = Classificacao.codigo and upper( Classificacao.nome ) like('" + valorConsulta.toUpperCase() + "%') ORDER BY Classificacao.nome";
        Statement stm = con.createStatement();
        ResultSet tabelaResultado = stm.executeQuery(sqlStr);
        return montarDadosConsulta(tabelaResultado, nivelMontarDados, con);
    }

    /**
     * Responsável por realizar uma consulta de <code>ClienteClassificacao</code> através do valor do atributo 
     * <code>matricula</code> da classe <code>Cliente</code>
     * Faz uso da operação <code>montarDadosConsulta</code> que realiza o trabalho de prerarar o List resultante.
     * @return  List Contendo vários objetos da classe <code>ClienteClassificacaoVO</code> resultantes da consulta.
     * @exception Execption Caso haja problemas de conexão ou restrição de acesso.
     */
    public List consultarPorMatriculaCliente(String valorConsulta, int nivelMontarDados) throws Exception {
        consultar(getIdEntidade(), true);
        String sqlStr = "SELECT ClienteClassificacao.* FROM ClienteClassificacao, Cliente WHERE ClienteClassificacao.cliente = Cliente.codigo and upper( Cliente.matricula ) like('" + valorConsulta.toUpperCase() + "%') ORDER BY Cliente.matricula";
        Statement stm = con.createStatement();
        ResultSet tabelaResultado = stm.executeQuery(sqlStr);
        return montarDadosConsulta(tabelaResultado, nivelMontarDados, con);
    }

    /**
     * Responsável por realizar uma consulta de <code>ClienteClassificacao</code> através do valor do atributo 
     * <code>Integer codigo</code>. Retorna os objetos com valores iguais ou superiores ao parâmetro fornecido.
     * Faz uso da operação <code>montarDadosConsulta</code> que realiza o trabalho de prerarar o List resultante.
     * @param   controlarAcesso Indica se a aplicação deverá verificar se o usuário possui permissão para esta consulta ou não.
     * @return  List Contendo vários objetos da classe <code>ClienteClassificacaoVO</code> resultantes da consulta.
     * @exception Exception Caso haja problemas de conexão ou restrição de acesso.
     */
    public List consultarPorCodigo(Integer valorConsulta, boolean controlarAcesso, int nivelMontarDados) throws Exception {
        consultar(getIdEntidade(), controlarAcesso);
        String sqlStr = "SELECT * FROM ClienteClassificacao WHERE codigo >= " + valorConsulta.intValue() + " ORDER BY codigo";
        Statement stm = con.createStatement();
        ResultSet tabelaResultado = stm.executeQuery(sqlStr);
        return (montarDadosConsulta(tabelaResultado, nivelMontarDados, con));
    }

    /**
     * Responsável por montar os dados de vários objetos, resultantes de uma consulta ao banco de dados (<code>ResultSet</code>).
     * Faz uso da operação <code>montarDados</code> que realiza o trabalho para um objeto por vez.
     * @return  List Contendo vários objetos da classe <code>ClienteClassificacaoVO</code> resultantes da consulta.
     */
    public static List montarDadosConsulta(ResultSet tabelaResultado, int nivelMontarDados, Connection con) throws Exception {
        List vetResultado = new ArrayList();
        while (tabelaResultado.next()) {
            ClienteClassificacaoVO obj = new ClienteClassificacaoVO();
            obj = montarDados(tabelaResultado, nivelMontarDados, con);
            vetResultado.add(obj);
        }
        return vetResultado;
    }

    /**
     * Responsável por montar os dados resultantes de uma consulta ao banco de dados (<code>ResultSet</code>)
     * em um objeto da classe <code>ClienteClassificacaoVO</code>.
     * @return  O objeto da classe <code>ClienteClassificacaoVO</code> com os dados devidamente montados.
     */
    public static ClienteClassificacaoVO montarDados(ResultSet dadosSQL, int nivelMontarDados, Connection con) throws Exception {
        ClienteClassificacaoVO obj = new ClienteClassificacaoVO();
        obj.setCodigo(new Integer(dadosSQL.getInt("codigo")));
        obj.setCliente(new Integer(dadosSQL.getInt("cliente")));
        obj.getClassificacao().setCodigo(new Integer(dadosSQL.getInt("classificacao")));
        obj.setNovoObj(new Boolean(false));
//        if (nivelMontarDados == Uteis.NIVELMONTARDADOS_DADOSBASICOS) {
//            return obj;
//        }

        montarDadosClassificacao(obj, Uteis.NIVELMONTARDADOS_DADOSBASICOS, con);
        return obj;
    }

    /**
     * Operação responsável por montar os dados de um objeto da classe <code>ClassificacaoVO</code> relacionado ao objeto <code>ClienteClassificacaoVO</code>.
     * Faz uso da chave primária da classe <code>ClassificacaoVO</code> para realizar a consulta.
     * @param obj  Objeto no qual será montado os dados consultados.
     */
    public static void montarDadosClassificacao(ClienteClassificacaoVO obj, int nivelMontarDados, Connection con) throws Exception {
        if (obj.getClassificacao().getCodigo().intValue() == 0) {
            obj.setClassificacao(new ClassificacaoVO());
            return;
        }

        Classificacao classificacaoDAO = new Classificacao(con);
        try {
            obj.setClassificacao(classificacaoDAO.consultarPorChavePrimaria(obj.getClassificacao().getCodigo(), nivelMontarDados));
        }catch(Exception e){
            e.getStackTrace();
        }finally {
            classificacaoDAO = null;
        }
    }

    /**
     * Operação responsável por excluir todos os objetos da <code>ClienteClassificacaoVO</code> no BD.
     * Faz uso da operação <code>excluir</code> disponível na classe <code>ClienteClassificacao</code>.
     * @param <code>cliente</code> campo chave para exclusão dos objetos no BD.
     * @exception Exception  Erro de conexão com o BD ou restrição de acesso a esta operação.
     */
    public void excluirClienteClassificacaos(Integer cliente) throws Exception {
        excluir(getIdEntidade());
        String sql = "DELETE FROM ClienteClassificacao WHERE (cliente = ?)";
        PreparedStatement sqlExcluir = con.prepareStatement(sql);
        sqlExcluir.setInt(1, cliente.intValue());
        sqlExcluir.execute();
    }

    /**
     * Operação responsável por alterar todos os objetos da <code>ClienteClassificacaoVO</code> contidos em um Hashtable no BD.
     * Faz uso da operação <code>excluirClienteClassificacaos</code> e <code>incluirClienteClassificacaos</code> disponíveis na classe <code>ClienteClassificacao</code>.
     * @param objetos  List com os objetos a serem alterados ou incluídos no BD.
     * @exception Exception  Erro de conexão com o BD ou restrição de acesso a esta operação.
     */
    public void alterarClienteClassificacaos(Integer cliente, List objetos) throws Exception {
        String str = "DELETE FROM ClienteClassificacao WHERE cliente = " + cliente.intValue();
        Iterator i = objetos.iterator();
        while (i.hasNext()) {
            ClienteClassificacaoVO objeto = (ClienteClassificacaoVO) i.next();
            str += " AND codigo <> " + objeto.getCodigo().intValue();
        }
        PreparedStatement sqlExcluir = con.prepareStatement(str);
        sqlExcluir.execute();
        Iterator e = objetos.iterator();
        while (e.hasNext()) {
            ClienteClassificacaoVO obj = (ClienteClassificacaoVO) e.next();
            if (obj.getCodigo().equals(new Integer(0))) {
                obj.setCliente(cliente);
                incluir(obj);
            } else {
                alterar(obj);
            }
        }
        //        excluirClienteClassificacaos( cliente );
//        incluirClienteClassificacaos( cliente, objetos );
    }

    /**
     * Operação responsável por incluir objetos da <code>ClienteClassificacaoVO</code> no BD.
     * Garantindo o relacionamento com a entidade principal <code>basico.Cliente</code> através do atributo de vínculo.
     * @param objetos List contendo os objetos a serem gravados no BD da classe.
     * @exception Exception  Erro de conexão com o BD ou restrição de acesso a esta operação.
     */
    public void incluirClienteClassificacaos(Integer clientePrm, List objetos) throws Exception {
        Iterator e = objetos.iterator();
        while (e.hasNext()) {
            ClienteClassificacaoVO obj = (ClienteClassificacaoVO) e.next();
            obj.setCliente(clientePrm);
            incluir(obj);
        }
    }

    /**
     * Operação responsável por consultar todos os <code>ClienteClassificacaoVO</code> relacionados a um objeto da classe <code>basico.Cliente</code>.
     * @param cliente  Atributo de <code>basico.Cliente</code> a ser utilizado para localizar os objetos da classe <code>ClienteClassificacaoVO</code>.
     * @return List  Contendo todos os objetos da classe <code>ClienteClassificacaoVO</code> resultantes da consulta.
     * @exception Exception  Erro de conexão com o BD ou restrição de acesso a esta operação.
     */
    public List consultarClienteClassificacaos(Integer cliente, int nivelMontarDados) throws Exception {
        consultar(getIdEntidade());
        List objetos = new ArrayList();
        String sql = "SELECT * FROM ClienteClassificacao WHERE cliente = ?";
        PreparedStatement sqlConsulta = con.prepareStatement(sql);
        sqlConsulta.setInt(1, cliente.intValue());
        ResultSet resultado = sqlConsulta.executeQuery();
        while (resultado.next()) {
            ClienteClassificacaoVO novoObj = new ClienteClassificacaoVO();
            novoObj = ClienteClassificacao.montarDados(resultado, nivelMontarDados, con);
            objetos.add(novoObj);
        }
        return objetos;
    }

    /**
     * Operação responsável por localizar um objeto da classe <code>ClienteClassificacaoVO</code>
     * através de sua chave primária. 
     * @exception Exception Caso haja problemas de conexão ou localização do objeto procurado.
     */
    public ClienteClassificacaoVO consultarPorChavePrimaria(Integer codigoPrm, int nivelMontarDados) throws Exception {
        consultar(getIdEntidade(), false);
        String sql = "SELECT * FROM ClienteClassificacao WHERE codigo = ?";
        PreparedStatement sqlConsultar = con.prepareStatement(sql);
        sqlConsultar.setInt(1, codigoPrm.intValue());
        ResultSet tabelaResultado = sqlConsultar.executeQuery();
        if (!tabelaResultado.next()) {
            throw new ConsistirException("Dados Não Encontrados ( ClienteClassificacao ).");
        }
        return (montarDados(tabelaResultado, nivelMontarDados, con));
    }
    public boolean consultarSeExisteClienteComClassificacaoEnvioSMSMarcado(int codigoCliente) throws Exception {
        String sql = "select exists (select  clienteclassificacao.codigo from clienteclassificacao ";
        sql +=" inner join classificacao on classificacao.codigo = clienteclassificacao.classificacao";
        sql +=" where cliente = "+codigoCliente+" and classificacao.enviarsmsautomatico limit 1) as existe";
        PreparedStatement stm = con.prepareStatement(sql);
        ResultSet tabelaResultado = stm.executeQuery();
        tabelaResultado.next();
        return tabelaResultado.getBoolean("existe");
    }
}

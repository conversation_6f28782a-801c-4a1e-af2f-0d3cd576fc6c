package negocio.facade.jdbc.basico;

import br.com.pactosolucoes.enumeradores.ConfiguracaoEmailEnum;
import br.com.pactosolucoes.integracao.pactopay.front.PactoPayConfigConvenioDTO;
import br.com.pactosolucoes.integracao.pactopay.front.PactoPayConfigDTO;
import br.com.pactosolucoes.integracao.pactopay.front.PactoPayConfiguracaoEmailDTO;
import br.com.pactosolucoes.integracao.pactopay.front.PactoPayConfiguracaoGymBotDTO;
import br.com.pactosolucoes.integracao.pactopay.front.PactoPayConfiguracaoGymBotProDTO;
import br.com.pactosolucoes.oamd.controle.basico.DAO;
import br.com.pactosolucoes.pactopay.PactoPayCobrancaAntecipadaService;
import br.com.pactosolucoes.pactopay.PactoPayCobrancaPendenteService;
import br.com.pactosolucoes.pactopay.PactoPayComunicacaoCartaoService;
import br.com.pactosolucoes.pactopay.PactoPayComunicacaoDTO;
import br.com.pactosolucoes.pactopay.PactoPayResultadoCobrancaService;
import br.com.pactosolucoes.pactopay.PactoPaySuperService;
import controle.arquitetura.SuperControle;
import negocio.comuns.arquitetura.LogVO;
import negocio.comuns.arquitetura.ResultadoServicosVO;
import negocio.comuns.arquitetura.UsuarioVO;
import negocio.comuns.basico.ConfiguracaoReenvioMovParcelaEmpresaVO;
import negocio.comuns.basico.EmpresaVO;
import negocio.comuns.basico.PactoPayComunicacaoVO;
import negocio.comuns.basico.PactoPayConfigVO;
import negocio.comuns.basico.enumerador.ServicoEnum;
import negocio.comuns.financeiro.ConvenioCobrancaVO;
import negocio.comuns.financeiro.TransacaoVO;
import negocio.comuns.utilitarias.Calendario;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisValidacao;
import negocio.facade.jdbc.arquitetura.Log;
import negocio.facade.jdbc.arquitetura.ResultadoServicos;
import negocio.facade.jdbc.arquitetura.SuperEntidade;
import negocio.facade.jdbc.arquitetura.Usuario;
import negocio.facade.jdbc.financeiro.ConvenioCobranca;
import negocio.interfaces.basico.PactoPayConfigInterfaceFacade;
import org.json.JSONObject;
import servicos.impl.pactoPay.PactoPayComunicacaoService;
import servicos.integracao.sendy.services.SendyImpl;
import servicos.integracao.sendy.to.BrandTO;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.util.ArrayList;
import java.util.List;

public class PactoPayConfig extends SuperEntidade implements PactoPayConfigInterfaceFacade {

    public PactoPayConfig() throws Exception {
        super();
    }

    public PactoPayConfig(Connection conexao) throws Exception {
        super(conexao);
    }

    private PactoPayConfigVO montarDadosBasico(ResultSet rs) throws Exception {
        PactoPayConfigVO obj = new PactoPayConfigVO();
        obj.setNovoObj(false);
        obj.setCodigo(rs.getInt("codigo"));
        obj.getEmpresaVO().setCodigo(rs.getInt("empresa"));

        obj.setEnvioAutomaticoCobranca(rs.getBoolean("envioAutomaticoCobranca"));

        obj.setCobrancaAntecipadaAtivo(rs.getBoolean("cobrancaAntecipadaAtivo"));
        obj.setCobrancaAntecipadaSMS(rs.getBoolean("cobrancaAntecipadaSMS"));
        obj.setCobrancaAntecipadaEmail(rs.getBoolean("cobrancaAntecipadaEmail"));
        obj.setCobrancaAntecipadaWhatsApp(rs.getBoolean("cobrancaAntecipadaWhatsApp"));
        obj.setCobrancaAntecipadaApp(rs.getBoolean("cobrancaAntecipadaApp"));
        obj.setCobrancaAntecipadaGymbotPro(rs.getBoolean("cobrancaantecipadagymbotpro"));
        obj.setCobrancaAntecipadaComAutorizacao(rs.getBoolean("cobrancaAntecipadaComAutorizacao"));
        obj.setCobrancaAntecipadaAplicarDesconto(rs.getBoolean("cobrancaAntecipadaAplicarDesconto"));
        obj.setCobrancaAntecipadaDesconto(rs.getDouble("cobrancaAntecipadaDesconto"));
        obj.setCobrancaAntecipadaValorFixo(rs.getBoolean("cobrancaAntecipadaValorFixo"));
        obj.setCobrancaAntecipadaDiasAnteriores(rs.getInt("cobrancaAntecipadaDiasAnteriores"));
        obj.setCobrancaAntecipadaDiasLimitePagamento(rs.getInt("cobrancaAntecipadaDiasLimitePagamento"));

        obj.setComunicadoResultadoCobrancaAtivo(rs.getBoolean("comunicadoResultadoCobrancaAtivo"));
        obj.setComunicadoResultadoCobrancaSMS(rs.getBoolean("comunicadoResultadoCobrancaSMS"));
        obj.setComunicadoResultadoCobrancaEmail(rs.getBoolean("comunicadoResultadoCobrancaEmail"));
        obj.setComunicadoResultadoCobrancaWhatsApp(rs.getBoolean("comunicadoResultadoCobrancaWhatsApp"));
        obj.setComunicadoResultadoCobrancaApp(rs.getBoolean("comunicadoResultadoCobrancaApp"));
        obj.setComunicadoResultadoCobrancaGymbotPro(rs.getBoolean("comunicadoresultadocobrancagymbotpro"));
        obj.setComunicadoResultadoCobrancaAprovada(rs.getBoolean("comunicadoResultadoCobrancaAprovada"));
        obj.setComunicadoResultadoCobrancaNegada(rs.getBoolean("comunicadoResultadoCobrancaNegada"));
        obj.setComunicadoResultadoCobrancaCancelada(rs.getBoolean("comunicadoResultadoCobrancaCancelada"));

        obj.setComunicadoAtrasoAtivo(rs.getBoolean("comunicadoAtrasoAtivo"));
        obj.setComunicadoAtrasoSMS(rs.getBoolean("comunicadoAtrasoSMS"));
        obj.setComunicadoAtrasoEmail(rs.getBoolean("comunicadoAtrasoEmail"));
        obj.setComunicadoAtrasoWhatsApp(rs.getBoolean("comunicadoAtrasoWhatsApp"));
        obj.setComunicadoAtrasoApp(rs.getBoolean("comunicadoAtrasoApp"));
        obj.setComunicadoAtrasoGymbotPro(rs.getBoolean("comunicadoatrasogymbotpro"));
        obj.setComunicadoAtrasoComAutorizacao(rs.getBoolean("comunicadoAtrasoComAutorizacao"));
        obj.setComunicadoAtrasoDiasVencidoMinimo(rs.getInt("comunicadoAtrasoDiasVencidoMinimo"));
        obj.setComunicadoAtrasoDiasVencidoMaximo(rs.getInt("comunicadoAtrasoDiasVencidoMaximo"));
        obj.setComunicadoAtrasoIntervaloDias(rs.getInt("comunicadoAtrasoIntervaloDias"));
        obj.setComunicadoAtrasoQtdMaximaEnvios(rs.getInt("comunicadoAtrasoQtdMaximaEnvios"));

        obj.setComunicadoCartaoAtivo(rs.getBoolean("comunicadoCartaoAtivo"));
        obj.setComunicadoCartaoSMS(rs.getBoolean("comunicadoCartaoSMS"));
        obj.setComunicadoCartaoEmail(rs.getBoolean("comunicadoCartaoEmail"));
        obj.setComunicadoCartaoWhatsApp(rs.getBoolean("comunicadoCartaoWhatsApp"));
        obj.setComunicadoCartaoApp(rs.getBoolean("comunicadoCartaoApp"));
        obj.setComunicadoCartaoGymbotPro(rs.getBoolean("comunicadocartaogymbotpro"));
        obj.setComunicadoCartaoVencido(rs.getBoolean("comunicadoCartaoVencido"));
        obj.setComunicadoCartaoProximoVencimento(rs.getBoolean("comunicadoCartaoProximoVencimento"));

        obj.setRetentativaAutomaticaAtivo(rs.getBoolean("retentativaAutomaticaAtivo"));

        obj.setQtdDiasEficienciaComunicacao(rs.getInt("qtddiaseficienciacomunicacao"));

        try {
            if (!UteisValidacao.emptyString(rs.getString("configuracaoemail"))) {
                obj.setConfiguracaoEmail(new PactoPayConfiguracaoEmailDTO(rs.getString("configuracaoemail"), true));
            }
        } catch (Exception ex) {
            ex.printStackTrace();
        }
        try {
            if (!UteisValidacao.emptyString(rs.getString("configuracaogymbot"))) {
                obj.setConfiguracaoGymBot(new PactoPayConfiguracaoGymBotDTO(rs.getString("configuracaogymbot")));
            }
        } catch (Exception ex) {
            ex.printStackTrace();
        }
        try {
            if(!UteisValidacao.emptyString(rs.getString("configuracaogymbotpro"))) {
                obj.setConfiguracaoGymBotPro(new PactoPayConfiguracaoGymBotProDTO(rs.getString("configuracaogymbotpro")));
            }
        }catch (Exception ex) {
            ex.printStackTrace();
        }

        return obj;
    }

    private PactoPayConfigVO montarDados(ResultSet dadosSQL, int nivelMontarDados, Connection con) throws Exception {
        PactoPayConfigVO obj = montarDadosBasico(dadosSQL);
        if (nivelMontarDados == Uteis.NIVELMONTARDADOS_DADOSBASICOS) {
            return obj;
        }

        if (nivelMontarDados == Uteis.NIVELMONTARDADOS_DADOSENTIDADESPRINCIPAIS) {
            montarDadosEmpresa(obj, Uteis.NIVELMONTARDADOS_GESTAOREMESSA, con);
            return obj;
        }
        return obj;
    }

    private void montarDadosEmpresa(PactoPayConfigVO obj, int nivelMontarDados, Connection con) throws Exception {
        if (UteisValidacao.emptyNumber(obj.getEmpresaVO().getCodigo())) {
            obj.setEmpresaVO(new EmpresaVO());
            return;
        }

        Empresa empresaDAO;
        try {
            empresaDAO = new Empresa(con);
            obj.setEmpresaVO(empresaDAO.consultarPorChavePrimaria(obj.getEmpresaVO().getCodigo(), nivelMontarDados));
        } finally {
            empresaDAO = null;
        }
    }

    private void incluir(PactoPayConfigVO obj, UsuarioVO usuarioVO) throws Exception {
        try {
            this.con.setAutoCommit(false);
            incluirSemCommit(obj, usuarioVO);
            this.con.commit();
        } catch (Exception ex) {
            this.con.rollback();
            throw ex;
        } finally {
            this.con.setAutoCommit(true);
        }
    }

    private void incluirSemCommit(PactoPayConfigVO obj, UsuarioVO usuarioVO) throws Exception {
        PactoPayConfigVO.validarDados(obj);
        String sql = "INSERT INTO pactopayconfig(empresa, envioAutomaticoCobranca, " +
                "" +
                "cobrancaAntecipadaAtivo, cobrancaAntecipadaSMS, cobrancaAntecipadaEmail, cobrancaAntecipadaWhatsApp, cobrancaAntecipadaApp, cobrancaantecipadagymbotpro, " +
                "cobrancaAntecipadaComAutorizacao, cobrancaAntecipadaAplicarDesconto, cobrancaAntecipadaDesconto, " +
                "cobrancaAntecipadaValorFixo, cobrancaAntecipadaDiasAnteriores, cobrancaAntecipadaDiasLimitePagamento, " +
                "" +
                "comunicadoResultadoCobrancaAtivo, comunicadoResultadoCobrancaSMS, comunicadoResultadoCobrancaEmail, comunicadoResultadoCobrancaWhatsApp, comunicadoResultadoCobrancaApp, " +
                "comunicadoresultadocobrancagymbotpro, comunicadoResultadoCobrancaAprovada, comunicadoResultadoCobrancaNegada, comunicadoResultadoCobrancaCancelada, " +
                "" +
                "comunicadoAtrasoAtivo, comunicadoAtrasoSMS, comunicadoAtrasoEmail, comunicadoAtrasoWhatsApp, comunicadoAtrasoApp, comunicadoatrasogymbotpro, " +
                "comunicadoAtrasoComAutorizacao, comunicadoAtrasoDiasVencidoMinimo, comunicadoAtrasoDiasVencidoMaximo, " +
                "comunicadoAtrasoIntervaloDias, comunicadoAtrasoQtdMaximaEnvios, " +
                "" +
                "comunicadoCartaoAtivo, comunicadoCartaoSMS, comunicadoCartaoEmail, comunicadoCartaoWhatsApp, comunicadoCartaoApp, comunicadocartaogymbotpro, " +
                "comunicadoCartaoVencido, comunicadoCartaoProximoVencimento, " +
                "" +
                "retentativaAutomaticaAtivo, configuracaoemail, configuracaogymbot, configuracaogymbotpro) " +
                "VALUES (?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)";
        try (PreparedStatement ps = con.prepareStatement(sql)) {
            int i = 0;
            resolveIntegerNull(ps, ++i, obj.getEmpresaVO().getCodigo());
            ps.setBoolean(++i, obj.isEnvioAutomaticoCobranca());

            ps.setBoolean(++i, obj.isCobrancaAntecipadaAtivo());
            ps.setBoolean(++i, obj.isCobrancaAntecipadaSMS());
            ps.setBoolean(++i, obj.isCobrancaAntecipadaEmail());
            ps.setBoolean(++i, obj.isCobrancaAntecipadaWhatsApp());
            ps.setBoolean(++i, obj.isCobrancaAntecipadaApp());
            ps.setBoolean(++i, obj.isCobrancaAntecipadaGymbotPro());
            ps.setBoolean(++i, obj.isCobrancaAntecipadaComAutorizacao());
            ps.setBoolean(++i, obj.isCobrancaAntecipadaAplicarDesconto());
            resolveDoubleNull(ps, ++i, obj.getCobrancaAntecipadaDesconto());
            ps.setBoolean(++i, obj.isCobrancaAntecipadaValorFixo());
            resolveIntegerNull(ps, ++i, obj.getCobrancaAntecipadaDiasAnteriores());
            resolveIntegerNull(ps, ++i, obj.getCobrancaAntecipadaDiasLimitePagamento());

            ps.setBoolean(++i, obj.isComunicadoResultadoCobrancaAtivo());
            ps.setBoolean(++i, obj.isComunicadoResultadoCobrancaSMS());
            ps.setBoolean(++i, obj.isComunicadoResultadoCobrancaEmail());
            ps.setBoolean(++i, obj.isComunicadoResultadoCobrancaWhatsApp());
            ps.setBoolean(++i, obj.isComunicadoResultadoCobrancaApp());
            ps.setBoolean(++i, obj.isComunicadoResultadoCobrancaGymbotPro());
            ps.setBoolean(++i, obj.isComunicadoResultadoCobrancaAprovada());
            ps.setBoolean(++i, obj.isComunicadoResultadoCobrancaNegada());
            ps.setBoolean(++i, obj.isComunicadoResultadoCobrancaCancelada());

            ps.setBoolean(++i, obj.isComunicadoAtrasoAtivo());
            ps.setBoolean(++i, obj.isComunicadoAtrasoSMS());
            ps.setBoolean(++i, obj.isComunicadoAtrasoEmail());
            ps.setBoolean(++i, obj.isComunicadoAtrasoWhatsApp());
            ps.setBoolean(++i, obj.isComunicadoAtrasoApp());
            ps.setBoolean(++i, obj.isComunicadoAtrasoGymbotPro());
            ps.setBoolean(++i, obj.isComunicadoAtrasoComAutorizacao());
            resolveIntegerNull(ps, ++i, obj.getComunicadoAtrasoDiasVencidoMinimo());
            resolveIntegerNull(ps, ++i, obj.getComunicadoAtrasoDiasVencidoMaximo());
            resolveIntegerNull(ps, ++i, obj.getComunicadoAtrasoIntervaloDias());
            resolveIntegerNull(ps, ++i, obj.getComunicadoAtrasoQtdMaximaEnvios());

            ps.setBoolean(++i, obj.isComunicadoCartaoAtivo());
            ps.setBoolean(++i, obj.isComunicadoCartaoSMS());
            ps.setBoolean(++i, obj.isComunicadoCartaoEmail());
            ps.setBoolean(++i, obj.isComunicadoCartaoWhatsApp());
            ps.setBoolean(++i, obj.isComunicadoCartaoApp());
            ps.setBoolean(++i, obj.isComunicadoCartaoGymbotPro());
            ps.setBoolean(++i, obj.isComunicadoCartaoVencido());
            ps.setBoolean(++i, obj.isComunicadoCartaoProximoVencimento());

            ps.setBoolean(++i, obj.isRetentativaAutomaticaAtivo());

            ps.setString(++i, obj.getConfiguracaoEmail().toString());
            ps.setString(++i, obj.getConfiguracaoGymBot().toString());
            ps.setString(++i, obj.getConfiguracaoGymBotPro().toString());
            ps.execute();
        }
        obj.setCodigo(obterValorChavePrimariaCodigo());
        obj.setNovoObj(false);
        gerarLog(obj, usuarioVO, true);
    }

    private void alterar(PactoPayConfigVO obj) throws Exception {
        PactoPayConfigVO.validarDados(obj);
        String sql = "UPDATE pactopayconfig SET empresa = ?, envioAutomaticoCobranca = ?, " +
                "" +
                "cobrancaAntecipadaAtivo = ?, cobrancaAntecipadaSMS = ?, cobrancaAntecipadaEmail = ?, cobrancaAntecipadaWhatsApp = ?, cobrancaAntecipadaApp = ?, cobrancaAntecipadaGymbotPro = ?, " +
                "cobrancaAntecipadaComAutorizacao = ?, cobrancaAntecipadaAplicarDesconto = ?, cobrancaAntecipadaDesconto = ?, " +
                "cobrancaAntecipadaValorFixo = ?, cobrancaAntecipadaDiasAnteriores = ?, cobrancaAntecipadaDiasLimitePagamento = ?, " +
                "" +
                "comunicadoResultadoCobrancaAtivo = ?, comunicadoResultadoCobrancaSMS = ?, comunicadoResultadoCobrancaEmail = ?, comunicadoResultadoCobrancaWhatsApp = ?, " +
                "comunicadoResultadoCobrancaApp = ?, comunicadoResultadoCobrancaGymbotPro = ?, " +
                "comunicadoResultadoCobrancaAprovada = ?, comunicadoResultadoCobrancaNegada = ?, comunicadoResultadoCobrancaCancelada = ?, " +
                "" +
                "comunicadoAtrasoAtivo = ?, comunicadoAtrasoSMS = ?, comunicadoAtrasoEmail = ?, comunicadoAtrasoWhatsApp = ?, comunicadoAtrasoApp = ?, comunicadoAtrasoGymbotPro = ?, " +
                "comunicadoAtrasoComAutorizacao = ?, comunicadoAtrasoDiasVencidoMinimo = ?, comunicadoAtrasoDiasVencidoMaximo = ?, " +
                "comunicadoAtrasoIntervaloDias = ?, comunicadoAtrasoQtdMaximaEnvios = ?, " +
                "" +
                "comunicadoCartaoAtivo = ?, comunicadoCartaoSMS = ?, comunicadoCartaoEmail = ?, comunicadoCartaoWhatsApp = ?, comunicadoCartaoApp = ?, comunicadoCartaoGymbotPro = ?, " +
                "comunicadoCartaoVencido = ?, comunicadoCartaoProximoVencimento = ?, " +
                "" +
                "retentativaAutomaticaAtivo = ?, configuracaoemail = ?, configuracaogymbot = ?, configuracaogymbotpro = ?, qtddiaseficienciacomunicacao = ? " +
                " WHERE codigo = ?";
        try (PreparedStatement ps = con.prepareStatement(sql)) {
            int i = 0;
            resolveIntegerNull(ps, ++i, obj.getEmpresaVO().getCodigo());
            ps.setBoolean(++i, obj.isEnvioAutomaticoCobranca());

            ps.setBoolean(++i, obj.isCobrancaAntecipadaAtivo());
            ps.setBoolean(++i, obj.isCobrancaAntecipadaSMS());
            ps.setBoolean(++i, obj.isCobrancaAntecipadaEmail());
            ps.setBoolean(++i, obj.isCobrancaAntecipadaWhatsApp());
            ps.setBoolean(++i, obj.isCobrancaAntecipadaApp());
            ps.setBoolean(++i, obj.isCobrancaAntecipadaGymbotPro());
            ps.setBoolean(++i, obj.isCobrancaAntecipadaComAutorizacao());
            ps.setBoolean(++i, obj.isCobrancaAntecipadaAplicarDesconto());
            resolveDoubleNull(ps, ++i, obj.getCobrancaAntecipadaDesconto());
            ps.setBoolean(++i, obj.isCobrancaAntecipadaValorFixo());
            resolveIntegerNull(ps, ++i, obj.getCobrancaAntecipadaDiasAnteriores());
            resolveIntegerNull(ps, ++i, obj.getCobrancaAntecipadaDiasLimitePagamento());

            ps.setBoolean(++i, obj.isComunicadoResultadoCobrancaAtivo());
            ps.setBoolean(++i, obj.isComunicadoResultadoCobrancaSMS());
            ps.setBoolean(++i, obj.isComunicadoResultadoCobrancaEmail());
            ps.setBoolean(++i, obj.isComunicadoResultadoCobrancaWhatsApp());
            ps.setBoolean(++i, obj.isComunicadoResultadoCobrancaApp());
            ps.setBoolean(++i, obj.isComunicadoResultadoCobrancaGymbotPro());
            ps.setBoolean(++i, obj.isComunicadoResultadoCobrancaAprovada());
            ps.setBoolean(++i, obj.isComunicadoResultadoCobrancaNegada());
            ps.setBoolean(++i, obj.isComunicadoResultadoCobrancaCancelada());

            ps.setBoolean(++i, obj.isComunicadoAtrasoAtivo());
            ps.setBoolean(++i, obj.isComunicadoAtrasoSMS());
            ps.setBoolean(++i, obj.isComunicadoAtrasoEmail());
            ps.setBoolean(++i, obj.isComunicadoAtrasoWhatsApp());
            ps.setBoolean(++i, obj.isComunicadoAtrasoApp());
            ps.setBoolean(++i, obj.isComunicadoAtrasoGymbotPro());
            ps.setBoolean(++i, obj.isComunicadoAtrasoComAutorizacao());
            resolveIntegerNull(ps, ++i, obj.getComunicadoAtrasoDiasVencidoMinimo());
            resolveIntegerNull(ps, ++i, obj.getComunicadoAtrasoDiasVencidoMaximo());
            resolveIntegerNull(ps, ++i, obj.getComunicadoAtrasoIntervaloDias());
            resolveIntegerNull(ps, ++i, obj.getComunicadoAtrasoQtdMaximaEnvios());

            ps.setBoolean(++i, obj.isComunicadoCartaoAtivo());
            ps.setBoolean(++i, obj.isComunicadoCartaoSMS());
            ps.setBoolean(++i, obj.isComunicadoCartaoEmail());
            ps.setBoolean(++i, obj.isComunicadoCartaoWhatsApp());
            ps.setBoolean(++i, obj.isComunicadoCartaoApp());
            ps.setBoolean(++i, obj.isComunicadoCartaoGymbotPro());
            ps.setBoolean(++i, obj.isComunicadoCartaoVencido());
            ps.setBoolean(++i, obj.isComunicadoCartaoProximoVencimento());

            ps.setBoolean(++i, obj.isRetentativaAutomaticaAtivo());

            ps.setString(++i, obj.getConfiguracaoEmail().toString());
            ps.setString(++i, obj.getConfiguracaoGymBot().toString());
            ps.setString(++i, obj.getConfiguracaoGymBotPro().toString());

            resolveIntegerNull(ps, ++i, obj.getQtdDiasEficienciaComunicacao());

            ps.setInt(++i, obj.getCodigo());
            ps.execute();
        }
    }

    public PactoPayConfigDTO consultarParaPactoPay(Integer empresa) throws Exception {
        Empresa empresaDAO;
        try {
            empresaDAO = new Empresa(this.con);

            EmpresaVO empresaVO = empresaDAO.consultarPorChavePrimaria(empresa, Uteis.NIVELMONTARDADOS_GESTAOREMESSA);
            PactoPayConfigVO pactoPayConfigVO = consultarPorEmpresa(empresaVO.getCodigo(), Uteis.NIVELMONTARDADOS_GESTAOREMESSA);
            return pactoPayConfigVO.toDTO(empresaVO, this.con);
        } finally {
            empresaDAO = null;
        }
    }

    public PactoPayConfigVO consultarPorEmpresa(Integer empresa, int nivelMontarDados) throws Exception {
        Usuario usuarioDAO;
        try {
            usuarioDAO = new Usuario(this.con);

            String sql = "SELECT * FROM pactopayconfig WHERE empresa = ?";
            try (PreparedStatement ps = con.prepareStatement(sql)) {
                ps.setInt(1, empresa);
                try (ResultSet rs = ps.executeQuery()) {
                    if (rs.next()) {
                        return montarDados(rs, nivelMontarDados, this.con);
                    } else {
                        PactoPayConfigVO pactoPayConfigVO = new PactoPayConfigVO(true);
                        pactoPayConfigVO.getEmpresaVO().setCodigo(empresa);
                        incluir(pactoPayConfigVO, usuarioDAO.getUsuarioRecorrencia());
                        return pactoPayConfigVO;
                    }
                }
            }
        } finally {
            usuarioDAO = null;
        }
    }

    public PactoPayConfigVO consultarPorChavePrimaria(Integer codigo, int nivelMontarDados) throws Exception {
        String sql = "SELECT * FROM pactopayconfig WHERE codigo = ?";
        try (PreparedStatement ps = con.prepareStatement(sql)) {
            ps.setInt(1, codigo);
            try (ResultSet rs = ps.executeQuery()) {
                if (rs.next()) {
                    return montarDados(rs, nivelMontarDados, this.con);
                } else {
                    throw new Exception("PactoPayConfig não encontrada com o código " + codigo);
                }
            }
        }
    }

    private void gerarLog(PactoPayConfigVO obj, UsuarioVO usuarioVO, boolean inclusao) throws Exception {
        Log logDAO;
        try {
            logDAO = new Log(con);
            LogVO log = new LogVO();
            log.setNomeEntidade(this.getClass().getSimpleName().toUpperCase());
            log.setNomeEntidadeDescricao(this.getClass().getSimpleName().toUpperCase());
            log.setOperacao(inclusao ? "INCLUSÃO" : "ALTERAÇÃO");
            log.setNomeCampo(this.getClass().getSimpleName().toUpperCase());
            log.setResponsavelAlteracao(usuarioVO.getNome());
            log.setUserOAMD(usuarioVO.getUserOamd());
            log.setChavePrimaria(obj.getCodigo().toString());
            log.setValorCampoAlterado("");
            log.setValorCampoAnterior("");
            logDAO.incluirSemCommit(log);
        } finally {
            logDAO = null;
        }
    }

    public void alterarConfiguracoes(Integer empresa, UsuarioVO usuarioVO, PactoPayConfigDTO pactoPayConfigDTO) throws Exception {
        Usuario usuarioDAO;
        Empresa empresaDAO;
        ConfiguracaoReenvioMovParcelaEmpresa configuracaoReenvioDAO;
        ConvenioCobranca convenioCobrancaDAO;
        try {
            con.setAutoCommit(false);
            usuarioDAO = new Usuario(con);
            empresaDAO = new Empresa(con);
            configuracaoReenvioDAO = new ConfiguracaoReenvioMovParcelaEmpresa(con);
            convenioCobrancaDAO = new ConvenioCobranca(con);

            if (UteisValidacao.emptyNumber(empresa)) {
                throw new Exception("Empresa não informada");
            }
            if (usuarioVO == null || UteisValidacao.emptyNumber(usuarioVO.getCodigo())) {
                throw new Exception("Usuário não informado");
            }

            usuarioVO = usuarioDAO.consultarPorChavePrimaria(usuarioVO.getCodigo(), Uteis.NIVELMONTARDADOS_METACOLABORADORRESPONSAVEL);
            EmpresaVO empresaVO = empresaDAO.consultarPorChavePrimaria(empresa, Uteis.NIVELMONTARDADOS_GESTAOREMESSA);
            empresaVO.registrarObjetoVOAntesDaAlteracao();

            PactoPayConfigVO obj = consultarPorEmpresa(empresa, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
            obj.registrarObjetoVOAntesDaAlteracao();
            obj.alterarBaseadoDTO(pactoPayConfigDTO); //alterar objeto conforme dto
            alterar(obj);
            SuperControle.registrarLogObjetoVOGeralComConexao(obj, this.getClass().getSimpleName().toUpperCase(), obj.getCodigo(), usuarioVO, this.con);

            //alterar configurações da empresa
            List<ConfiguracaoReenvioMovParcelaEmpresaVO> listaAnterior = configuracaoReenvioDAO.consultarConfiguracaoReenvioMovParcelaEmpresa(empresaVO.getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS);
            empresaVO.setConfiguracaoReenvioMovParcelaEmpresaVOS(listaAnterior);

            empresaVO.setHabilitarReenvioAutomaticoRemessa(pactoPayConfigDTO.getMultiplos_convenios().isAtivo());
            empresaVO.setQtdExecucoesRetentativa(pactoPayConfigDTO.getMultiplos_convenios().getQtd_tentativas());

            empresaVO.setQtdDiasLimiteCobrancaParcelasRecorrencia(pactoPayConfigDTO.getRetentativa().getLimite_dias());
            empresaVO.setQtdDiasRepetirCobrancaParcelasRecorrencia(pactoPayConfigDTO.getRetentativa().getIntervalo_dias());

            if (pactoPayConfigDTO.getMultiplos_convenios().isAtivo()) {
                List<ConfiguracaoReenvioMovParcelaEmpresaVO> listaNova = new ArrayList<>();
                for (PactoPayConfigConvenioDTO convDTO : pactoPayConfigDTO.getMultiplos_convenios().getConvenios()) {
                    ConvenioCobrancaVO convenioCobrancaVO = convenioCobrancaDAO.consultarPorChavePrimaria(convDTO.getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS);
                    ConfiguracaoReenvioMovParcelaEmpresaVO novo = new ConfiguracaoReenvioMovParcelaEmpresaVO();
                    novo.setPosicao(convDTO.getPosicao());
                    novo.setConvenioCobrancaVO(convenioCobrancaVO);
                    novo.setTentativasRealizar(1);
                    novo.setEmpresaVO(empresaVO);
                    listaNova.add(novo);
                }

                //verificar se houve alteração
                if (houveAlteracaoConfiguracaoReenvioMovParcelaEmpresa(listaAnterior, listaNova)) {
                    empresaVO.setConfiguracaoReenvioMovParcelaEmpresaVOS(listaNova);
                    configuracaoReenvioDAO.alterarConfiguracaoReenvioMovParcelaEmpresa(empresaVO.getCodigo(), empresaVO.getConfiguracaoReenvioMovParcelaEmpresaVOS());
                    configuracaoReenvioDAO.gravarLogConfiguracaoRetentativa(empresaVO, usuarioVO, listaAnterior, listaNova);
                }
            }
            empresaVO.validarConfiguracaoMultiplosConvenios();
            empresaVO.validarConfiguracaoRetentativa();

            empresaDAO.alterarConfiguracoesPactoPay(empresaVO);
            SuperControle.registrarLogObjetoVOGeralComConexao(empresaVO, empresaVO.getClass().getSimpleName().toUpperCase(), empresaVO.getCodigo(), usuarioVO, this.con);

            con.commit();
        } catch (Exception ex) {
            ex.printStackTrace();
            con.rollback();
            throw ex;
        } finally {
            usuarioDAO = null;
            empresaDAO = null;
            configuracaoReenvioDAO = null;
            convenioCobrancaDAO = null;
            con.setAutoCommit(true);
        }
    }

    private boolean houveAlteracaoConfiguracaoReenvioMovParcelaEmpresa(List<ConfiguracaoReenvioMovParcelaEmpresaVO> listaAnterior,
                                                                       List<ConfiguracaoReenvioMovParcelaEmpresaVO> listaNova) {
        if (listaAnterior.size() != listaNova.size()) {
            return true;
        }

        for (ConfiguracaoReenvioMovParcelaEmpresaVO novo : listaNova) {
            for (ConfiguracaoReenvioMovParcelaEmpresaVO anterior : listaAnterior) {
                if (novo.getPosicao().equals(anterior.getPosicao()) &&
                        !novo.getConvenioCobrancaVO().getCodigo().equals(anterior.getConvenioCobrancaVO().getCodigo())) {
                    return true;
                }
            }
        }
        return false;
    }

    public boolean isEnvioAutomaticoCobranca(Integer empresa) throws Exception {
        PactoPayConfigVO pactoPayConfigVO = consultarPorEmpresa(empresa, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
        return pactoPayConfigVO.isEnvioAutomaticoCobranca();
    }

    public void processarCobrancaAntecipada(Integer empresa, boolean comException, Integer codigoPessoa, boolean enviarSemJenkins) throws Exception {
        PactoPayCobrancaAntecipadaService service;
        ResultadoServicosVO resultado = new ResultadoServicosVO(ServicoEnum.REGUA_COBRANCA_COBRANCA_ANTECIPADA, empresa);
        try {
            service = new PactoPayCobrancaAntecipadaService(this.con);

            PactoPayConfigVO configVO = consultarPorEmpresa(empresa, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
            if (!configVO.isCobrancaAntecipadaAtivo()) {
                throw new Exception("Cobrança antecipada não está ativa");
            }
            service.processar(configVO, codigoPessoa, enviarSemJenkins);
        } catch (Exception ex) {
            ex.printStackTrace();
            resultado.getResultado().put(ex.getMessage());
            if (comException) {
                throw ex;
            }
        } finally {
            service = null;
            gravarResultadoServicos(resultado);
        }
    }

    public void processarCobrancaPendente(Integer empresa, boolean comException, Integer codigoPessoa, boolean enviarSemJenkins) throws Exception {
        PactoPayCobrancaPendenteService service;
        ResultadoServicosVO resultado = new ResultadoServicosVO(ServicoEnum.REGUA_COBRANCA_COBRANCA_PENDENTE, empresa);
        try {
            service = new PactoPayCobrancaPendenteService(this.con);

            PactoPayConfigVO configVO = consultarPorEmpresa(empresa, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
            if (!configVO.isComunicadoAtrasoAtivo()) {
                throw new Exception("Comunicado de atraso não está ativo");
            }

            service.processar(configVO, codigoPessoa, enviarSemJenkins);
        } catch (Exception ex) {
            ex.printStackTrace();
            resultado.getResultado().put(ex.getMessage());
            if (comException) {
                throw ex;
            }
        } finally {
            service = null;
            gravarResultadoServicos(resultado);
        }
    }

    public void processarCartao(Integer empresa, boolean comException, Integer codigoPessoa, boolean enviarSemJenkins) throws Exception {
        PactoPayComunicacaoCartaoService service;
        ResultadoServicosVO resultado = new ResultadoServicosVO(ServicoEnum.REGUA_COBRANCA_CARTAO_VENCENDO, empresa);
        try {
            service = new PactoPayComunicacaoCartaoService(this.con);

            PactoPayConfigVO configVO = consultarPorEmpresa(empresa, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
            if (!configVO.isComunicadoCartaoAtivo()) {
                throw new Exception("Comunicado de Cartão não está ativo");
            }
            service.processar(configVO, codigoPessoa, enviarSemJenkins);
        } catch (Exception ex) {
            ex.printStackTrace();
            resultado.getResultado().put(ex.getMessage());
            if (comException) {
                throw ex;
            }
        } finally {
            service = null;
            gravarResultadoServicos(resultado);
        }
    }

    public void processarResultadoCobranca(TransacaoVO transacaoVO, boolean exception,
                                           Integer codigoPessoa, boolean enviarSemJenkins) throws Exception {
        PactoPayResultadoCobrancaService service;
        try {
            service = new PactoPayResultadoCobrancaService(this.con);
            service.processar(transacaoVO, exception, codigoPessoa, enviarSemJenkins);
        } finally {
            service = null;
        }
    }

    public void realizarEnvioExemploResultadoCobranca(Integer empresa, String celular, String email,
                                                      String modeloEnvio, String meioEnvio) throws Exception {
        PactoPayResultadoCobrancaService service;
        try {
            service = new PactoPayResultadoCobrancaService(this.con);
            service.realizarEnvioExemplo(empresa, celular, email, modeloEnvio, meioEnvio);
        } finally {
            service = null;
        }
    }

    private void gravarResultadoServicos(ResultadoServicosVO obj) {
        ResultadoServicos resultadoServicosDAO;
        try {
            resultadoServicosDAO = new ResultadoServicos(this.con);
            if (obj == null) {
                return;
            }
            resultadoServicosDAO.gravarResultado(obj);
        } catch (Exception ex) {
            ex.printStackTrace();
            Uteis.logar(null, "# ERRO gravarResultadoServicos: " + ex.getMessage());
        } finally {
            resultadoServicosDAO = null;
        }
    }

    public void testeConfigEmail(Integer empresa, String email, PactoPayConfiguracaoEmailDTO configDTO) throws Exception {
        PactoPayComunicacaoService pactoPayComunicacaoService;
        try {
            pactoPayComunicacaoService = new PactoPayComunicacaoService(this.con);

            if (UteisValidacao.emptyNumber(empresa)) {
                throw new Exception("Empresa não informada");
            }
            if (UteisValidacao.emptyString(email)) {
                throw new Exception("E-mail não informado");
            }
            if (!UteisValidacao.validaEmail(email)) {
                throw new Exception("E-mail informado não é válido");
            }

            List<String> listaEmail = new ArrayList<>();
            listaEmail.add(email);

            PactoPayComunicacaoDTO pactoPayComunicacaoDTO = new PactoPayComunicacaoDTO();
            pactoPayComunicacaoDTO.setDestinatario(listaEmail);
            pactoPayComunicacaoDTO.setAssunto("E-mail configuração Régua Cobrança");
            pactoPayComunicacaoDTO.setMensagem("E-mail configuração Régua Cobrança");

            PactoPayComunicacaoVO pactoPayComunicacaoVO = new PactoPayComunicacaoVO();
            pactoPayComunicacaoVO.getEmpresaVO().setCodigo(empresa);
            pactoPayComunicacaoVO.setComunicacao(new JSONObject(pactoPayComunicacaoDTO).toString());
            pactoPayComunicacaoService.enviarEmailLista(pactoPayComunicacaoVO, configDTO.obterConfigCRMVO());
        } finally {
            pactoPayComunicacaoService = null;
        }
    }

    public void criarEmail(Integer empresa, String email,
                           String remetente, UsuarioVO usuarioVO) throws Exception {
        SendyImpl sendyImpl;
        PactoPaySuperService pactoPaySuperService;
        String jsonEnvio = "";
        String jsonResp = "";
        try {
            sendyImpl = new SendyImpl();
            pactoPaySuperService = new PactoPaySuperService(this.con);

            if (UteisValidacao.emptyNumber(empresa)) {
                throw new Exception("Empresa não informada");
            }
            if (UteisValidacao.emptyString(email)) {
                throw new Exception("E-mail não informado");
            }
            if (!UteisValidacao.validaEmail(email)) {
                throw new Exception("E-mail informado não é válido");
            }
            if (UteisValidacao.emptyString(remetente)) {
                throw new Exception("Remetente não informado");
            }

//            if (sendyImpl.listIdentities().stream().noneMatch(email::equals)) {
//                sendyImpl.verifyEmailIdentity(email);
//                throw new Exception("Primeiro aprove a verificação de segurança enviada ao e-mail informado.");
//            }

            String key = DAO.resolveKeyFromConnection(this.con);
            String appKey = pactoPaySuperService.obterAppKey(key, empresa);

            BrandTO brandTO = new BrandTO();

            remetente = remetente.toUpperCase();
            brandTO.setAppName(remetente);
            brandTO.setFromName(remetente);

            String userSmtp = Uteis.getUserMailFacilitePay();
            String domainSmtp = Uteis.getDomainMailFacilitePay();
            brandTO.setDomain(domainSmtp);
            brandTO.setUserSmtp(userSmtp);
            brandTO.setFromEmail(userSmtp);
            brandTO.setReplyTo(email);
            brandTO.setPasswordSmtp(Uteis.getPasswordMailFacilitePay());
            brandTO.setQuota("30000");
            brandTO.setHostSmtp(ConfiguracaoEmailEnum.MAILGUN.getSmtpPadrao());
            brandTO.setSmtpPort(ConfiguracaoEmailEnum.MAILGUN.getPortaPadrao()[0]);
            brandTO.setSmtpTsl("tls");
            brandTO.setAppKey(appKey);

            jsonEnvio = brandTO.getJSON();
            jsonResp = sendyImpl.createBrand(brandTO);

            PactoPayConfigVO pactoPayConfigVO = consultarPorEmpresa(empresa, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
            pactoPayConfigVO.getConfiguracaoEmail().setWagi(true);
            pactoPayConfigVO.getConfiguracaoEmail().setWagiEmail(email);
            pactoPayConfigVO.getConfiguracaoEmail().setRemetente(remetente);
            pactoPayConfigVO.getConfiguracaoEmail().setSmtpEmail(email);
            pactoPayConfigVO.getConfiguracaoEmail().setSmtpLogin(email);
            pactoPayConfigVO.getConfiguracaoEmail().setAppKeyWagiCriado(appKey);
            alterarConfiguracaoEmail(empresa, pactoPayConfigVO, usuarioVO);
        } catch (Exception ex) {
            ex.printStackTrace();
            Uteis.logarDebug("Erro PactoPayConfig | criarEmail | " + ex.getMessage() + " | Resp: " + jsonResp);
            gerarLogConfigEmail(empresa, jsonEnvio, "Erro | " + ex.getMessage(), "PACTOPAY_CRIAR_EMAIL_REGUA_COBRANCA", usuarioVO);
            throw ex;
        } finally {
            gerarLogConfigEmail(empresa, jsonEnvio, jsonResp, "PACTOPAY_CRIAR_EMAIL_REGUA_COBRANCA", usuarioVO);
            sendyImpl = null;
            pactoPaySuperService = null;
        }
    }

    private void gerarLogConfigEmail(Integer empresa, String valorAnterior,
                                     String novoValor, String nomeEntidade,
                                     UsuarioVO usuarioVO) {
        Log logDAO;
        try {
            logDAO = new Log(con);

            LogVO obj = new LogVO();
            obj.setChavePrimaria(empresa.toString());
            obj.setNomeEntidade(nomeEntidade);
            obj.setNomeEntidadeDescricao(nomeEntidade);
            obj.setOperacao("Regua de Cobranca");
            obj.setDataAlteracao(Calendario.hoje());
            if (usuarioVO != null &&
                    !UteisValidacao.emptyNumber(usuarioVO.getCodigo())) {
                try {
                    obj.setUsuarioVO(usuarioVO);
                    obj.setResponsavelAlteracao(usuarioVO.getNome());
                    obj.setUserOAMD(usuarioVO.getUserOamd());
                } catch (Exception ex) {
                    obj.setResponsavelAlteracao("");
                }
            } else {
                obj.setResponsavelAlteracao("API_PACTOPAY");
            }
            obj.setNomeCampo(nomeEntidade);
            obj.setPessoa(0);
            obj.setValorCampoAnterior(valorAnterior);
            obj.setValorCampoAlterado(novoValor);
            logDAO.incluirSemCommit(obj);
        } catch (Exception ex) {
            ex.printStackTrace();
        } finally {
            logDAO = null;
        }
    }

    public void alterarConfiguracaoEmail(Integer empresa, PactoPayConfigVO obj, UsuarioVO usuarioVO) throws Exception {
        String configAnterior = "";
        if (!UteisValidacao.emptyNumber(empresa)) {
            configAnterior = consultarPorEmpresa(empresa, Uteis.NIVELMONTARDADOS_DADOSBASICOS).getConfiguracaoEmail().toString();
        }
        String sql = "UPDATE pactopayconfig SET configuracaoemail = ? WHERE codigo = ?";
        try (PreparedStatement ps = con.prepareStatement(sql)) {
            int i = 0;
            ps.setString(++i, obj.getConfiguracaoEmail().toString());
            ps.setInt(++i, obj.getCodigo());
            ps.execute();
        }
        gerarLogConfigEmail(empresa, configAnterior, obj.getConfiguracaoEmail().toString(), "PACTOPAY_CONFIG_EMAIL_REGUA_COBRANCA", usuarioVO);
    }

    public void alterarConfiguracaoGymBot(Integer empresa, PactoPayConfigVO obj, UsuarioVO usuarioVO) throws Exception {
        String configAnterior = "";
        if (!UteisValidacao.emptyNumber(empresa)) {
            configAnterior = consultarPorEmpresa(empresa, Uteis.NIVELMONTARDADOS_DADOSBASICOS).getConfiguracaoGymBot().toString();
        }
        String sql = "UPDATE pactopayconfig SET configuracaogymbot = ? WHERE codigo = ?";
        try (PreparedStatement ps = con.prepareStatement(sql)) {
            int i = 0;
            ps.setString(++i, obj.getConfiguracaoGymBot().toString());
            ps.setInt(++i, obj.getCodigo());
            ps.execute();
        }
        gerarLogConfigEmail(empresa, configAnterior, obj.getConfiguracaoGymBot().toString(), "PACTOPAY_CONFIG_GYMBOT_REGUA_COBRANCA", usuarioVO);
    }

    public void alterarConfiguracaoGymBotPro(Integer empresa, PactoPayConfigVO obj, UsuarioVO usuarioVO) throws Exception {
        String configAnterior = "";
        if (!UteisValidacao.emptyNumber(empresa)) {
            configAnterior = consultarPorEmpresa(empresa, Uteis.NIVELMONTARDADOS_DADOSBASICOS).getConfiguracaoGymBotPro().toString();
        }
        String sql = "UPDATE pactopayconfig SET configuracaogymbotpro = ? WHERE codigo = ?";
        try (PreparedStatement ps = con.prepareStatement(sql)) {
            int i = 0;
            ps.setString(++i, obj.getConfiguracaoGymBotPro().toString());
            ps.setInt(++i, obj.getCodigo());
            ps.execute();
        }
        gerarLogConfigEmail(empresa, configAnterior, obj.getConfiguracaoGymBotPro().toString(), "PACTOPAY_CONFIG_GYMBOTPRO_REGUA_COBRANCA", usuarioVO);
    }
}

package negocio.facade.jdbc.basico;

import negocio.comuns.basico.GympassDiaVO;
import negocio.comuns.basico.GympassVO;
import negocio.comuns.utilitarias.Ordenacao;
import negocio.comuns.utilitarias.Uteis;
import negocio.facade.jdbc.arquitetura.SuperEntidade;
import negocio.interfaces.basico.GympassInterfaceFacade;

import java.sql.*;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Date;
import java.util.List;

public class Gympass extends SuperEntidade implements GympassInterfaceFacade {

    public Gympass() throws Exception {
        super();
    }

    public Gympass(Connection con) throws Exception {
        super(con);
    }

    public GympassVO novo() throws Exception {
        incluir(getIdEntidade());
        return new GympassVO();
    }

    public void incluir(GympassVO obj) throws Exception {
        try {
            con.setAutoCommit(false);
            incluirSemCommit(obj);
            processarListaDias(obj.getCodigo(), obj.getDias());
            con.commit();
        } catch (Exception e) {
            con.rollback();
            throw e;
        } finally {
            con.setAutoCommit(true);
        }
    }

    private void incluirSemCommit(GympassVO obj) throws Exception {
        GympassVO.validarDados(obj);
        int i = 1;
        String sql = "INSERT INTO gympass (valormaximo, valorminimo, datainicial, datafinal) VALUES (?, ?, ?, ?)";
        try (PreparedStatement sqlInserir = con.prepareStatement(sql)) {
            sqlInserir.setDouble(i++, obj.getValorMaximo());
            sqlInserir.setDouble(i++, obj.getValorMinimo());
            sqlInserir.setDate(i++, Uteis.getDataJDBC(obj.getDataInicial()));
            sqlInserir.setDate(i++, Uteis.getDataJDBC(obj.getDataFinal()));
            sqlInserir.execute();
        }
        obj.setCodigo(obterValorChavePrimariaCodigo());
    }

    public void alterar(GympassVO obj) throws Exception {
        try {
            con.setAutoCommit(false);
            alterarSemCommit(obj);
            processarListaDias(obj.getCodigo(), obj.getDias());
            con.commit();
        } catch (Exception e) {
            con.rollback();
            throw e;
        } finally {
            con.setAutoCommit(true);
        }
    }

    public void excluir(GympassVO obj) throws Exception {
        try {
            con.setAutoCommit(false);
            StringBuilder sql = new StringBuilder();
            sql.append("DELETE FROM gympass WHERE codigo = ?");
            try (PreparedStatement sqlExcluir = con.prepareStatement(sql.toString())) {
                sqlExcluir.setInt(1, obj.getCodigo());
                sqlExcluir.execute();
            }
            con.commit();
        } catch (Exception e) {
            con.rollback();
            con.setAutoCommit(true);
            throw e;
        }finally {
            con.setAutoCommit(true);
        }
    }

    private void alterarSemCommit(GympassVO obj) throws Exception {
        GympassVO.validarDados(obj);
        int i = 1;
        String sql = "UPDATE gympass SET valormaximo=?, valorminimo=?, datainicial=?, datafinal=? WHERE codigo=?";
        try (PreparedStatement sqlAlterar = con.prepareStatement(sql)) {
            sqlAlterar.setDouble(i++, obj.getValorMaximo());
            sqlAlterar.setDouble(i++, obj.getValorMinimo());
            sqlAlterar.setDate(i++, Uteis.getDataJDBC(obj.getDataInicial()));
            sqlAlterar.setDate(i++, Uteis.getDataJDBC(obj.getDataFinal()));
            sqlAlterar.setInt(i++, obj.getCodigo());
            sqlAlterar.execute();
        }
    }

    private void processarListaDias(int codigo, List<GympassDiaVO> lista) throws Exception {
        for (GympassDiaVO dia : lista) {
            if (dia.isNovoObj()) {
                getFacade().getGympassDia().incluir(codigo, dia);
            } else if (!dia.equals(dia.getObjetoVOAntesAlteracao())) {
                getFacade().getGympassDia().alterar(codigo, dia);
            }
        }
    }

    public GympassVO buscarPorCodigo(int codigo, int nivelMontarDados) throws Exception {
        String sql = "SELECT * FROM gympass WHERE codigo = ?";
        try (PreparedStatement stm = con.prepareStatement(sql)) {
            stm.setInt(1, codigo);
            try (ResultSet tabelaResultado = stm.executeQuery()) {
                if (tabelaResultado.next())
                    return montarDados(tabelaResultado, nivelMontarDados);
                else
                    return new GympassVO();
            }
        }
    }

    public GympassVO buscarPorTabelaAtiva(java.util.Date dataConsulta, int nivelMontarDados) throws Exception {
        String sql = "SELECT * FROM gympass WHERE ? BETWEEN datainicial AND datafinal";
        try (PreparedStatement stm = con.prepareStatement(sql)) {
            stm.setDate(1, Uteis.getDataJDBC(dataConsulta));
            try (ResultSet tabelaResultado = stm.executeQuery()) {
                if (tabelaResultado.next())
                    return montarDados(tabelaResultado, nivelMontarDados);
                else
                    return new GympassVO();
            }
        }
    }

    public GympassVO buscarPorTabelaAtivaPeriodo(java.util.Date dataInicio, java.util.Date dataFinal, int nivelMontarDados) throws Exception {
        int i = 1;
        String sql = "SELECT * FROM gympass WHERE " +
                     "(? BETWEEN datainicial AND datafinal) OR (? BETWEEN datainicial AND datafinal) OR " +
                     "(datainicial BETWEEN ? AND ?) OR (datafinal BETWEEN ? AND ?);";
        try (PreparedStatement stm = con.prepareStatement(sql)) {
            stm.setDate(i++, Uteis.getDataJDBC(dataInicio));
            stm.setDate(i++, Uteis.getDataJDBC(dataFinal));
            stm.setDate(i++, Uteis.getDataJDBC(dataInicio));
            stm.setDate(i++, Uteis.getDataJDBC(dataFinal));
            stm.setDate(i++, Uteis.getDataJDBC(dataInicio));
            stm.setDate(i++, Uteis.getDataJDBC(dataFinal));
            try (ResultSet tabelaResultado = stm.executeQuery()) {
                if (tabelaResultado.next())
                    return montarDados(tabelaResultado, nivelMontarDados);
                else
                    return null;
            }
        }
    }

    public Date buscarDataInicio() throws Exception {
        String sql = "SELECT * FROM gympass ORDER BY datainicial DESC LIMIT 1";
        try (Statement stm = con.createStatement()) {
            try (ResultSet tabelaResultado = stm.executeQuery(sql)) {
                if (tabelaResultado.next())
                    return tabelaResultado.getDate("datainicial");
                else
                    return new Date();
            }
        }
    }

    public List consultar(int nivelMontarDados) throws Exception {
        String sqlStr = "SELECT * FROM gympass";
        try (Statement stm = con.createStatement()) {
            try (ResultSet tabelaResultado = stm.executeQuery(sqlStr)) {
                return (montarDadosConsulta(tabelaResultado, nivelMontarDados));
            }
        }
    }

    public List consultarParaImpressao(String filtro, String ordem, String campoOrdenacao) throws SQLException {
        List lista;
        try (ResultSet rs = getPS().executeQuery()) {
            lista = new ArrayList();
            while (rs.next()) {
                GympassVO gympass = new GympassVO();
                gympass.setCodigo(rs.getInt("codigo"));
                gympass.setValorMaximo(rs.getDouble("valormaximo"));
                gympass.setValorMinimo(rs.getDouble("valorminimo"));
                gympass.setDataInicial(rs.getDate("datainicial"));
                gympass.setDataFinal(rs.getDate("datafinal"));
                lista.add(gympass);

            }
        }

        if (campoOrdenacao.equals("Codigo")) {
            Ordenacao.ordenarLista(lista, "codigo");
        }else if(campoOrdenacao.equals("ValorMáximo")){
            Ordenacao.ordenarLista(lista, "valormaximo");
        }else if(campoOrdenacao.equals("ValorMínimo")){
            Ordenacao.ordenarLista(lista, "valorminimo");
        }else if(campoOrdenacao.equals("DataInicial")){
            Ordenacao.ordenarLista(lista, "datainicial");
        }else if(campoOrdenacao.equals("DataFinal")){
            Ordenacao.ordenarLista(lista, "datafinal");
        }

        if (ordem.contains("desc")) {
            Collections.reverse(lista);
        }

        return lista;
    }

    private PreparedStatement getPS() throws SQLException {
        String sql = "SELECT * FROM gympass ORDER BY codigo";
        return con.prepareStatement(sql);
    }

    public String consultarJSON() throws Exception {
        StringBuilder json;
        boolean dados;
        try (ResultSet rs = getPS().executeQuery()) {
            json = new StringBuilder();
            json.append("{\"aaData\":[");
            dados = false;
            while (rs.next()) {
                dados = true;
                json.append("[\"").append(rs.getString("codigo")).append("\",");
                json.append("\"").append(Uteis.getDoubleFormatado(rs.getDouble("valormaximo"))).append("\",");
                json.append("\"").append(Uteis.getData(rs.getDate("datainicial"))).append("\",");
                json.append("\"").append(Uteis.getData(rs.getDate("datafinal"))).append("\"],");
            }
        }
        if (dados) {
            json.deleteCharAt(json.toString().length() - 1);
        }
        json.append("]}");
        return json.toString();
    }

    public List<GympassVO> montarDadosConsulta(ResultSet tabelaResultado, int nivelMontarDados) throws Exception {
        List<GympassVO> vetResultado = new ArrayList();
        while (tabelaResultado.next()) {
            GympassVO obj = montarDados(tabelaResultado, nivelMontarDados);
            vetResultado.add(obj);
        }
        return vetResultado;
    }

    public GympassVO montarDados(ResultSet dadosSQL, int nivelMontarDados) throws Exception {
        GympassDia gympassDia = new GympassDia(con);
        GympassVO obj = new GympassVO();
        obj.setCodigo(dadosSQL.getInt("codigo"));
        obj.setValorMaximo(dadosSQL.getDouble("valormaximo"));
        obj.setValorMinimo(dadosSQL.getDouble("valorminimo"));
        obj.setDataInicial(dadosSQL.getDate("datainicial"));
        obj.setDataFinal(dadosSQL.getDate("datafinal"));
        obj.setNovoObj(false);
        if (nivelMontarDados == Uteis.NIVELMONTARDADOS_TODOS) {
            obj.setDias(gympassDia.consultarPorCodigo(obj.getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS));
            return obj;
        }
        return obj;
    }
}

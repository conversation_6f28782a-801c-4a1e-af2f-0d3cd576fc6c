package negocio.facade.jdbc.basico;

import negocio.comuns.CadastroDinamicoItemVO;
import negocio.comuns.basico.CadastroDinamicoVO;
import negocio.comuns.basico.enumerador.negocio.comuns.basico.enumerador.cadastrodinamico.CadastroDinamicoEnumInterface;
import negocio.comuns.utilitarias.Uteis;
import negocio.facade.jdbc.arquitetura.SuperEntidade;
import negocio.interfaces.basico.CadastroDinamicoInterfaceFacade;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.util.List;

/**
 * Created by ulisses on 14/08/2015.
 */
public class CadastroDinamico extends SuperEntidade implements CadastroDinamicoInterfaceFacade {

    public CadastroDinamico() throws Exception {
        super();
    }

    public CadastroDinamico(Connection conexao) throws Exception {
        super(conexao);
    }


    public void incluirSemCommit(CadastroDinamicoVO cadastroDinamicoVO) throws Exception{
        cadastroDinamicoVO.validarDados();
        con.setAutoCommit(false);
        String sqlInsert = "insert into CadastroDinamico(nomeTabela) values (?)";
        try (PreparedStatement pst = con.prepareStatement(sqlInsert)) {
            pst.setString(1, cadastroDinamicoVO.getNomeTabela());
            pst.execute();
        }
        cadastroDinamicoVO.setCodigo(obterValorChavePrimariaCodigo());
        cadastroDinamicoVO.setNovoObj(false);
    }

    public void excluir(CadastroDinamicoVO cadastroDinamicoVO) throws Exception{

    }

    public CadastroDinamicoVO consultarPorCodigo(Integer codigo,  int nivelMontarDados) throws Exception{
        StringBuilder sql = new StringBuilder();
        sql.append("select * from CadastroDinamico where codigo = ").append(codigo);
        try (PreparedStatement pst = con.prepareStatement(sql.toString())) {
            try (ResultSet rs = pst.executeQuery()) {
                if (rs.next()) {
                    return montarDados(rs, nivelMontarDados);
                }
            }
        }
        return null;
    }

    public void incluirTabelaCadastroDinamico(String nomeTabela, List<CadastroDinamicoEnumInterface> listaCadastroDinamicoEnumInterface)throws Exception{
        try{
            CadastroDinamicoVO cadastroDinamicoVO = consultar(nomeTabela, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
            if (cadastroDinamicoVO == null){
                cadastroDinamicoVO = new CadastroDinamicoVO();
                cadastroDinamicoVO.setNomeTabela(nomeTabela);
                incluirSemCommit(cadastroDinamicoVO);
                getFacade().getCadastroDinamicoItem().incluirSemCommit(cadastroDinamicoVO, listaCadastroDinamicoEnumInterface);
            }else{
                for (CadastroDinamicoEnumInterface cadastroDinamicoEnumInterface: listaCadastroDinamicoEnumInterface){
                    CadastroDinamicoItemVO cadastroDinamicoItemVO = getFacade().getCadastroDinamicoItem().consultarPorNome(cadastroDinamicoVO.getCodigo(), cadastroDinamicoEnumInterface.toString(), Uteis.NIVELMONTARDADOS_DADOSBASICOS);
                    if (cadastroDinamicoItemVO == null){
                        getFacade().getCadastroDinamicoItem().incluirSemCommit(cadastroDinamicoVO.getCodigo(), cadastroDinamicoEnumInterface.toString(), cadastroDinamicoEnumInterface.getLabel());
                    }
                }
            }
            List<CadastroDinamicoItemVO> listaItem = getFacade().getCadastroDinamicoItem().consultar(nomeTabela, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
            for (CadastroDinamicoItemVO cadastroDinamicoItemVO: listaItem){
                // Excluir do banco de dados os campos que não serão mais configurados pelo usuário.
                boolean econtrou = false;
                for (CadastroDinamicoEnumInterface cadastroDinamicoEnumInterface: listaCadastroDinamicoEnumInterface){
                    if (cadastroDinamicoEnumInterface.toString().equals(cadastroDinamicoItemVO.getNomeCampo())){
                        econtrou = true;
                        break;
                    }
                }
                if (!econtrou){
                    getFacade().getCadastroDinamicoItem().excluirSemCommit(cadastroDinamicoItemVO);
                }
            }
        }catch (Exception e){
            throw e;
        }
    }

    public CadastroDinamicoVO consultar(String nomeTabela, int nivelMontarDados) throws Exception{
        StringBuilder sql = new StringBuilder();
        sql.append("select * from CadastroDinamico where upper(nomeTabela) = '").append(nomeTabela.toUpperCase()).append("'");
        try (PreparedStatement pst = con.prepareStatement(sql.toString())) {
            try (ResultSet rs = pst.executeQuery()) {
                if (rs.next()) {
                    return montarDados(rs, nivelMontarDados);
                }
            }
        }
        return null;
    }

    public CadastroDinamicoVO montarDados(ResultSet dadosSQL, int nivelMontarDados) throws Exception {
        CadastroDinamicoVO cadastroDinamicoVO = new CadastroDinamicoVO();
        cadastroDinamicoVO.setCodigo(dadosSQL.getInt("codigo"));
        cadastroDinamicoVO.setNomeTabela(dadosSQL.getString("nomeTabela"));
        cadastroDinamicoVO.setNovoObj(false);
        return cadastroDinamicoVO;
    }

}

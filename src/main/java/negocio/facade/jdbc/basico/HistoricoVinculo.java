package negocio.facade.jdbc.basico;

import br.com.pactosolucoes.comuns.json.DadosBITreinoJSON;
import br.com.pactosolucoes.enumeradores.TipoColaboradorEnum;
import negocio.comuns.arquitetura.UsuarioVO;
import negocio.comuns.basico.ClienteVO;
import negocio.comuns.basico.ColaboradorVO;
import negocio.comuns.basico.HistoricoVinculoVO;
import negocio.comuns.utilitarias.Calendario;
import negocio.comuns.utilitarias.ConsistirException;
import negocio.comuns.utilitarias.Uteis;
import negocio.facade.jdbc.arquitetura.SuperEntidade;
import negocio.interfaces.basico.HistoricoVinculoInterfaceFacade;
import org.apache.commons.lang.StringUtils;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.Statement;
import java.util.*;

/**
 * Classe de persistência que encapsula todas as operações de manipulação dos
 * dados da classe
 * <code>HistoricoVinculoVO</code>. Responsável por implementar operações como
 * incluir, alterar, excluir e consultar pertinentes a classe
 * <code>HistoricoVinculoVO</code>. Encapsula toda a interação com o banco de
 * dados.
 *
 * @see HistoricoVinculoVO
 * @see SuperEntidade
 */
public class HistoricoVinculo extends SuperEntidade implements HistoricoVinculoInterfaceFacade {

    public HistoricoVinculo() throws Exception {
        super();
        setIdEntidade("Cliente");
    }

    public HistoricoVinculo(Connection con) throws Exception {
        super(con);
        setIdEntidade("Cliente");
    }

    /**
     * Operação responsável por retornar um novo objeto da classe
     * <code>HistoricoVinculoVO</code>.
     */
    public HistoricoVinculoVO novo() throws Exception {
        incluir(getIdEntidade());
        HistoricoVinculoVO obj = new HistoricoVinculoVO();
        return obj;
    }

    /**
     * Operação responsável por incluir no banco de dados um objeto da classe
     * <code>HistoricoVinculoVO</code>. Primeiramente valida os dados (
     * <code>validarDados</code>) do objeto. Verifica a conexão com o banco de
     * dados e a permissão do usuário para realizar esta operacão na entidade.
     * Isto, através da operação
     * <code>incluir</code> da superclasse.
     *
     * @param obj Objeto da classe <code>HistoricoVinculoVO</code> que será
     * gravado no banco de dados.
     * @exception Exception Caso haja problemas de conexão, restrição de acesso
     * ou validação de dados.
     */
    public void incluir(HistoricoVinculoVO obj) throws Exception {
        try {
            con.setAutoCommit(false);
            HistoricoVinculoVO.validarDados(obj);
            incluir(getIdEntidade());
            obj.realizarUpperCaseDados();
            String sql = "INSERT INTO HistoricoVinculo( cliente, dataRegistro, tipoHistoricoVinculo, colaborador, tipoColaborador, origem ) VALUES ( ?, ?, ?, ?, ?, ?)";
            try (PreparedStatement sqlInserir = con.prepareStatement(sql)) {
                if (obj.getCliente().getCodigo().intValue() != 0) {
                    sqlInserir.setInt(1, obj.getCliente().getCodigo().intValue());
                } else {
                    sqlInserir.setNull(1, 0);
                }
                sqlInserir.setTimestamp(2, Uteis.getDataJDBCTimestamp(obj.getDataRegistro()));
                sqlInserir.setString(3, obj.getTipoHistoricoVinculo());
                if (obj.getColaborador().getCodigo().intValue() != 0) {
                    sqlInserir.setInt(4, obj.getColaborador().getCodigo().intValue());
                } else {
                    sqlInserir.setNull(4, 0);
                }
                sqlInserir.setString(5, obj.getTipoColaborador());
                sqlInserir.setString(6, obj.getOrigem());
                sqlInserir.execute();
            }
            obj.setCodigo(obterValorChavePrimariaCodigo());
            obj.setNovoObj(new Boolean(false));
            con.commit();
        } catch (Exception e) {
            obj.setNovoObj(new Boolean(true));
            con.rollback();
            con.setAutoCommit(true);
            throw e;
        } finally {
            con.setAutoCommit(true);
        }
    }

    public void incluirSemCommit(HistoricoVinculoVO obj) throws Exception {
        incluirSemCommit(obj, true);
    }

    /**
     * Operação responsável por incluir no banco de dados um objeto da classe
     * <code>HistoricoVinculoVO</code>. Primeiramente valida os dados (
     * <code>validarDados</code>) do objeto. Verifica a conexão com o banco de
     * dados e a permissão do usuário para realizar esta operacão na entidade.
     * Isto, através da operação
     * <code>incluir</code> da superclasse.
     *
     * @param obj Objeto da classe <code>HistoricoVinculoVO</code> que será
     * gravado no banco de dados.
     * @exception Exception Caso haja problemas de conexão, restrição de acesso
     * ou validação de dados.
     */
    public void incluirSemCommit(HistoricoVinculoVO obj, boolean controlarLog) throws Exception {
        HistoricoVinculoVO.validarDados(obj);
        if (controlarLog) {
            incluirCRM(getIdEntidade());
        }
        obj.realizarUpperCaseDados();
        String sql = "INSERT INTO HistoricoVinculo (cliente, dataRegistro, tipoHistoricoVinculo, colaborador, tipoColaborador, origem, usuarioresponsavel) VALUES (?, ?, ?, ?, ?, ?, ?)";
        try (PreparedStatement sqlInserir = con.prepareStatement(sql)) {
            if (obj.getCliente().getCodigo() != 0) {
                sqlInserir.setInt(1, obj.getCliente().getCodigo());
            } else {
                sqlInserir.setNull(1, 0);
            }
            sqlInserir.setTimestamp(2, Uteis.getDataJDBCTimestamp((obj.getDataRegistro())));
            sqlInserir.setString(3, obj.getTipoHistoricoVinculo());
            if (obj.getColaborador().getCodigo() != 0) {
                sqlInserir.setInt(4, obj.getColaborador().getCodigo());
            } else {
                sqlInserir.setNull(4, 0);
            }
            sqlInserir.setString(5, obj.getTipoColaborador());
            sqlInserir.setString(6, obj.getOrigem());
            if (obj.getUsuarioVO() != null && obj.getUsuarioVO().getCodigo() != 0) {
                sqlInserir.setInt(7, obj.getUsuarioVO().getCodigo());
            } else {
                sqlInserir.setNull(7, 0);
            }
            sqlInserir.execute();
        }
        obj.setCodigo(obterValorChavePrimariaCodigo());
        obj.setNovoObj(false);
    }

    /**
     * Operação responsável por alterar no BD os dados de um objeto da classe
     * <code>HistoricoVinculoVO</code>. Sempre utiliza a chave primária da
     * classe como atributo para localização do registro a ser alterado.
     * Primeiramente valida os dados (
     * <code>validarDados</code>) do objeto. Verifica a conexão com o banco de
     * dados e a permissão do usuário para realizar esta operacão na entidade.
     * Isto, através da operação
     * <code>alterar</code> da superclasse.
     *
     * @param obj Objeto da classe <code>HistoricoVinculoVO</code> que será
     * alterada no banco de dados.
     * @exception Exception Caso haja problemas de conexão, restrição de acesso
     * ou validação de dados.
     */
    public void alterar(HistoricoVinculoVO obj) throws Exception {
        try {
            con.setAutoCommit(false);
            HistoricoVinculoVO.validarDados(obj);
            alterar(getIdEntidade());
            obj.realizarUpperCaseDados();
            String sql = "UPDATE HistoricoVinculo set cliente=?, dataRegistro=?, tipoHistoricoVinculo=?, colaborador=?, tipoColaborador=?, origem=?, usuarioresponsavel=? WHERE codigo = ?";
            try (PreparedStatement sqlAlterar = con.prepareStatement(sql)) {
                if (obj.getCliente().getCodigo() != 0) {
                    sqlAlterar.setInt(1, obj.getCliente().getCodigo());
                } else {
                    sqlAlterar.setNull(1, 0);
                }
                sqlAlterar.setTimestamp(2, Uteis.getDataJDBCTimestamp((obj.getDataRegistro())));
                sqlAlterar.setString(3, obj.getTipoHistoricoVinculo());
                if (obj.getColaborador().getCodigo() != 0) {
                    sqlAlterar.setInt(4, obj.getColaborador().getCodigo());
                } else {
                    sqlAlterar.setNull(4, 0);
                }
                sqlAlterar.setString(5, obj.getTipoColaborador());
                sqlAlterar.setString(6, obj.getOrigem());
                if (obj.getUsuarioVO() != null && obj.getUsuarioVO().getCodigo() != 0) {
                    sqlAlterar.setInt(7, obj.getUsuarioVO().getCodigo());
                } else {
                    sqlAlterar.setNull(7, 0);
                }
                sqlAlterar.setInt(8, obj.getCodigo());
                sqlAlterar.execute();
            }
            con.commit();
        } catch (Exception e) {
            con.rollback();
            con.setAutoCommit(true);
            throw e;
        } finally {
            con.setAutoCommit(true);
        }
    }

    /**
     * Operação responsável por excluir no BD um objeto da classe
     * <code>HistoricoVinculoVO</code>. Sempre localiza o registro a ser
     * excluído através da chave primária da entidade. Primeiramente verifica a
     * conexão com o banco de dados e a permissão do usuário para realizar esta
     * operacão na entidade. Isto, através da operação
     * <code>excluir</code> da superclasse.
     *
     * @param obj Objeto da classe <code>HistoricoVinculoVO</code> que será
     * removido no banco de dados.
     * @exception Exception Caso haja problemas de conexão ou restrição de
     * acesso.
     */
    public void excluir(HistoricoVinculoVO obj) throws Exception {
        try {
            con.setAutoCommit(false);
            excluirSemCommit(obj);
            con.commit();
        } catch (Exception e) {
            con.rollback();
            con.setAutoCommit(true);
            throw e;
        } finally {
            con.setAutoCommit(true);
        }
    }

    public void excluirSemCommit(HistoricoVinculoVO obj) throws Exception {
        excluir(getIdEntidade());
        String sql = "DELETE FROM HistoricoVinculo WHERE ((codigo = ?))";
        try (PreparedStatement sqlExcluir = con.prepareStatement(sql)) {
            sqlExcluir.setInt(1, obj.getCodigo().intValue());
            sqlExcluir.execute();
        }
    }

    /**
     * Responsável por realizar uma consulta de
     * <code>HistoricoVinculo</code> através do valor do atributo
     * <code>situacao</code> da classe
     * <code>Colaborador</code> Faz uso da operação
     * <code>montarDadosConsulta</code> que realiza o trabalho de prerarar o
     * List resultante.
     *
     * @return List Contendo vários objetos da
     * classe <code>HistoricoVinculoVO</code> resultantes da consulta.
     * @exception Exception Caso haja problemas de conexão ou restrição de
     * acesso.
     */
    public List consultarPorSituacaoColaborador(String valorConsulta, int nivelMontarDados) throws Exception {
        consultar(getIdEntidade(), true);
        String sqlStr = "SELECT HistoricoVinculo.* FROM HistoricoVinculo, Colaborador WHERE HistoricoVinculo.colaborador = Colaborador.codigo and upper( Colaborador.situacao ) like('" + valorConsulta.toUpperCase() + "%') ORDER BY Colaborador.situacao";
        try (Statement stm = con.createStatement()) {
            try (ResultSet tabelaResultado = stm.executeQuery(sqlStr)) {
                return montarDadosConsulta(tabelaResultado, nivelMontarDados);
            }
        }
    }

    /**
     * Responsável por realizar uma consulta de
     * <code>HistoricoVinculo</code> através do valor do atributo
     * <code>String tipoHistoricoVinculo</code>. Retorna os objetos, com início
     * do valor do atributo idêntico ao parâmetro fornecido. Faz uso da operação
     * <code>montarDadosConsulta</code> que realiza o trabalho de prerarar o
     * List resultante.
     *
     * @param controlarAcesso Indica se a aplicação deverá verificar se o
     * usuário possui permissão para esta consulta ou não.
     * @return List Contendo vários objetos da
     * classe <code>HistoricoVinculoVO</code> resultantes da consulta.
     * @exception Exception Caso haja problemas de conexão ou restrição de
     * acesso.
     */
    public List consultarPorTipoHistoricoVinculo(String valorConsulta, boolean controlarAcesso, int nivelMontarDados) throws Exception {
        consultar(getIdEntidade(), controlarAcesso);
        String sqlStr = "SELECT * FROM HistoricoVinculo WHERE upper( tipoHistoricoVinculo ) like('" + valorConsulta.toUpperCase() + "%') ORDER BY tipoHistoricoVinculo";
        try (Statement stm = con.createStatement()) {
            try (ResultSet tabelaResultado = stm.executeQuery(sqlStr)) {
                return (montarDadosConsulta(tabelaResultado, nivelMontarDados));
            }
        }
    }

    /**
     * Responsável por realizar uma consulta de
     * <code>HistoricoVinculo</code> através do valor do atributo
     * <code>Date dataRegistro</code>. Retorna os objetos com valores
     * pertecentes ao período informado por parâmetro. Faz uso da operação
     * <code>montarDadosConsulta</code> que realiza o trabalho de prerarar o
     * List resultante.
     *
     * @param controlarAcesso Indica se a aplicação deverá verificar se o
     * usuário possui permissão para esta consulta ou não.
     * @return List Contendo vários objetos da
     * classe <code>HistoricoVinculoVO</code> resultantes da consulta.
     * @exception Exception Caso haja problemas de conexão ou restrição de
     * acesso.
     */
    public List consultarPorDataRegistro(Date prmIni, Date prmFim, boolean controlarAcesso, int nivelMontarDados) throws Exception {
        consultar(getIdEntidade(), controlarAcesso);
        String sqlStr = "SELECT * FROM HistoricoVinculo WHERE ((dataRegistro >= '" + Uteis.getDataJDBC(prmIni) + "') and (dataRegistro <= '" + Uteis.getDataJDBC(prmFim) + "')) ORDER BY dataRegistro";
        try (Statement stm = con.createStatement()) {
            try (ResultSet tabelaResultado = stm.executeQuery(sqlStr)) {
                return (montarDadosConsulta(tabelaResultado, nivelMontarDados));
            }
        }
    }

    /**
     * Responsável por realizar uma consulta de
     * <code>HistoricoVinculo</code> através do valor do atributo
     * <code>situacao</code> da classe
     * <code>Cliente</code> Faz uso da operação
     * <code>montarDadosConsulta</code> que realiza o trabalho de prerarar o
     * List resultante.
     *
     * @return List Contendo vários objetos da
     * classe <code>HistoricoVinculoVO</code> resultantes da consulta.
     * @exception Exception Caso haja problemas de conexão ou restrição de
     * acesso.
     */
    public List consultarPorSituacaoCliente(String valorConsulta, int nivelMontarDados) throws Exception {
        consultar(getIdEntidade(), true);
        String sqlStr = "SELECT HistoricoVinculo.* FROM HistoricoVinculo, Cliente WHERE HistoricoVinculo.cliente = Cliente.codigo and upper( Cliente.situacao ) like('" + valorConsulta.toUpperCase() + "%') ORDER BY Cliente.situacao";
        try (Statement stm = con.createStatement()) {
            try (ResultSet tabelaResultado = stm.executeQuery(sqlStr)) {
                return montarDadosConsulta(tabelaResultado, nivelMontarDados);
            }
        }
    }

    /**
     * Responsável por realizar uma consulta de
     * <code>HistoricoVinculo</code> através do valor do atributo
     * <code>Integer codigo</code>. Retorna os objetos com valores iguais ou
     * superiores ao parâmetro fornecido. Faz uso da operação
     * <code>montarDadosConsulta</code> que realiza o trabalho de prerarar o
     * List resultante.
     *
     * @param controlarAcesso Indica se a aplicação deverá verificar se o
     * usuário possui permissão para esta consulta ou não.
     * @return List Contendo vários objetos da
     * classe <code>HistoricoVinculoVO</code> resultantes da consulta.
     * @exception Exception Caso haja problemas de conexão ou restrição de
     * acesso.
     */
    public List consultarPorCodigo(Integer valorConsulta, boolean controlarAcesso, int nivelMontarDados) throws Exception {
        consultar(getIdEntidade(), controlarAcesso);
        String sqlStr = "SELECT * FROM HistoricoVinculo WHERE codigo >= " + valorConsulta.intValue() + " ORDER BY codigo";
        try (Statement stm = con.createStatement()) {
            try (ResultSet tabelaResultado = stm.executeQuery(sqlStr)) {
                return (montarDadosConsulta(tabelaResultado, nivelMontarDados));
            }
        }
    }

    /**
     * Responsável por montar os dados de vários objetos, resultantes de uma
     * consulta ao banco de dados (
     * <code>ResultSet</code>). Faz uso da operação
     * <code>montarDados</code> que realiza o trabalho para um objeto por vez.
     *
     * @return List Contendo vários objetos da
     * classe <code>HistoricoVinculoVO</code> resultantes da consulta.
     */
    public List<HistoricoVinculoVO> consultarPorCodigoClienteOrganizadorCarteira(Integer valorConsulta, boolean controlarAcesso, int nivelMontarDados) throws Exception {
        consultar(getIdEntidade(), controlarAcesso);
        String sqlStr = "SELECT * FROM HistoricoVinculo WHERE cliente = " + valorConsulta + " ORDER BY dataRegistro desc ";
        try (Statement stm = con.createStatement()) {
            try (ResultSet tabelaResultado = stm.executeQuery(sqlStr)) {
                return (montarDadosConsulta(tabelaResultado, nivelMontarDados));
            }
        }
    }

    public static List<HistoricoVinculoVO> montarDadosConsulta(ResultSet tabelaResultado, int nivelMontarDados) throws Exception {
        List<HistoricoVinculoVO> vetResultado = new ArrayList<HistoricoVinculoVO>();
        while (tabelaResultado.next()) {
            HistoricoVinculoVO historicoVinculoVO = montarDados(tabelaResultado, nivelMontarDados);
            vetResultado.add(historicoVinculoVO);
        }
        tabelaResultado = null;
        return vetResultado;
    }

    /**
     * Responsável por montar os dados resultantes de uma consulta ao banco de
     * dados (
     * <code>ResultSet</code>) em um objeto da classe
     * <code>HistoricoVinculoVO</code>.
     *
     * @return O objeto da classe <code>HistoricoVinculoVO</code> com os dados
     * devidamente montados.
     */
    public static HistoricoVinculoVO montarDados(ResultSet dadosSQL, int nivelMontarDados) throws Exception {
        HistoricoVinculoVO obj = new HistoricoVinculoVO();
        obj.setCodigo(new Integer(dadosSQL.getInt("codigo")));
        obj.getCliente().setCodigo(new Integer(dadosSQL.getInt("cliente")));
        obj.setDataRegistro(dadosSQL.getTimestamp("dataRegistro"));
        obj.setTipoHistoricoVinculo(dadosSQL.getString("tipoHistoricoVinculo"));
        obj.getColaborador().setCodigo(new Integer(dadosSQL.getInt("colaborador")));
        obj.setTipoColaborador(dadosSQL.getString("tipoColaborador"));
        obj.setOrigem(dadosSQL.getString("origem"));
        try {
            if (dadosSQL.getInt("usuarioresponsavel") != 0) {
                obj.setUsuarioVO(new UsuarioVO());
                obj.getUsuarioVO().setCodigo(dadosSQL.getInt("usuarioresponsavel"));
            }
        } catch (Exception e) {
            // TODO: handle exception
        }

        obj.setNovoObj(false);
        if (nivelMontarDados == Uteis.NIVELMONTARDADOS_DADOSBASICOS) {
            return obj;
        }

        if (nivelMontarDados == Uteis.NIVELMONTARDADOS_TELACONSULTAESPECIAL) {
            montarDadosCliente(obj, Uteis.NIVELMONTARDADOS_GESTAO_PERSONAL);
            montarDadosColaborador(obj, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
            montarDadosUsuario(obj, Uteis.NIVELMONTARDADOS_MINIMOS);
            return obj;
        }

        montarDadosCliente(obj, nivelMontarDados);
        montarDadosColaborador(obj, nivelMontarDados);
        montarDadosUsuario(obj, Uteis.NIVELMONTARDADOS_MINIMOS);
        return obj;
    }

    /**
     * Operação responsável por montar os dados de um objeto da classe
     * <code>ColaboradorVO</code> relacionado ao objeto
     * <code>HistoricoVinculoVO</code>. Faz uso da chave primária da classe
     * <code>ColaboradorVO</code> para realizar a consulta.
     *
     * @param obj Objeto no qual será montado os dados consultados.
     */
    public static void montarDadosColaborador(HistoricoVinculoVO obj, int nivelMontarDados) throws Exception {
        if (obj.getColaborador().getCodigo().intValue() == 0) {
            obj.setColaborador(new ColaboradorVO());
            return;
        }
        obj.setColaborador(getFacade().getColaborador().consultarPorChavePrimaria(obj.getColaborador().getCodigo(), nivelMontarDados));
    }

    /**
     * Operação responsável por montar os dados de um objeto da classe
     * <code>ClienteVO</code> relacionado ao objeto
     * <code>HistoricoVinculoVO</code>. Faz uso da chave primária da classe
     * <code>ClienteVO</code> para realizar a consulta.
     *
     * @param obj Objeto no qual será montado os dados consultados.
     */
    public static void montarDadosCliente(HistoricoVinculoVO obj, int nivelMontarDados) throws Exception {
        if (obj.getCliente().getCodigo().intValue() == 0) {
            obj.setCliente(new ClienteVO());
            return;
        }
        obj.setCliente(getFacade().getCliente().consultarPorChavePrimaria(obj.getCliente().getCodigo(), nivelMontarDados));
    }

    private static void montarDadosUsuario(HistoricoVinculoVO obj, int nivelMontarDados) throws Exception {
        if (obj.getUsuarioVO() == null || obj.getUsuarioVO().getCodigo() == 0) {
            obj.setUsuarioVO(new UsuarioVO());
            return;
        }
        obj.setUsuarioVO(getFacade().getUsuario().consultarPorChavePrimaria(obj.getUsuarioVO().getCodigo(), nivelMontarDados));
    }

    /**
     * Operação responsável por localizar um objeto da classe
     * <code>HistoricoVinculoVO</code> através de sua chave primária.
     *
     * @exception Exception Caso haja problemas de conexão ou localização do
     * objeto procurado.
     */
    public HistoricoVinculoVO consultarPorChavePrimaria(Integer codigoPrm, int nivelMontarDados) throws Exception {
        consultar(getIdEntidade(), false);
        String sql = "SELECT * FROM HistoricoVinculo WHERE codigo = ?";
        try (PreparedStatement sqlConsultar = con.prepareStatement(sql)) {
            sqlConsultar.setInt(1, codigoPrm.intValue());
            try (ResultSet tabelaResultado = sqlConsultar.executeQuery()) {
                if (!tabelaResultado.next()) {
                    throw new ConsistirException("Dados Não Encontrados ( HistoricoVinculo ).");
                }
                return (montarDados(tabelaResultado, nivelMontarDados));
            }
        }
    }

    public List<HistoricoVinculoVO> consultarPorCodigoClienteCodigoConsultor(Integer cliente, Integer consultor, int nivelMontarDados) throws Exception {
        String sql = "SELECT * FROM historicovinculo\n"
                + "WHERE 1 = 1 \n"
                + "      AND tipocolaborador = 'PR'\n"
                + "      AND cliente = ?\n"
                + "      AND colaborador = ?;";
        try (PreparedStatement sqlConsultar = con.prepareStatement(sql)) {
            sqlConsultar.setInt(1, cliente);
            sqlConsultar.setInt(2, consultor);
            try (ResultSet tabelaResultado = sqlConsultar.executeQuery()) {
                if (!tabelaResultado.next()) {
                    throw new ConsistirException("Dados Não Encontrados ( HistoricoVinculo ).");
                }
                return (montarDadosConsulta(tabelaResultado, nivelMontarDados));
            }
        }
    }

    public Boolean verificarVinculoNaData(Date data, Integer codigoCliente, Integer codigoColaborador) throws Exception {
        StringBuilder sql = new StringBuilder();
        
        sql.append(" SELECT COUNT(codigo) AS cont FROM historicovinculo hv");
        sql.append(" WHERE (cliente = " + codigoCliente + " AND hv.tipohistoricovinculo = 'EN' AND dataregistro <= '" + Uteis.getDataJDBC(data) + " 23:59:59' AND colaborador =" + codigoColaborador + " ) \n");
        sql.append(" AND  not exists ( SELECT cliente from historicovinculo ");
        sql.append(" WHERE colaborador =" + codigoColaborador + " and cliente = " + codigoCliente + " AND hv.tipocolaborador = historicovinculo.tipocolaborador AND tipohistoricovinculo = 'SD' ");
        sql.append(" AND  dataregistro <= '" + Uteis.getDataJDBC(data) + " 23:59:59' and dataregistro >= hv.dataregistro );");
        try (Statement stm = con.createStatement()) {
            try (ResultSet rs = stm.executeQuery(sql.toString())) {
                rs.next();
                return rs.getInt("cont") > 0;
            }
        }
    }

    public Boolean verificarVinculoNaData(Date data, Integer codigoCliente, Integer codigoColaborador, String tipoColaborador) throws Exception {
        String tipoColaboradorSQL = "";
        if (tipoColaborador.equals("CO")) {
            tipoColaboradorSQL = "AND tipocolaborador = 'CO'\n";
        } else if (tipoColaborador.equals("PR")) {
            tipoColaboradorSQL = "AND tipocolaborador = 'PR'\n";
        } else {
            tipoColaboradorSQL = "AND tipocolaborador NOT IN ('CO', 'PR')\n";
        }

        StringBuilder sql = new StringBuilder();
        sql.append(" SELECT COUNT(codigo) AS cont FROM historicovinculo hv");
        sql.append(" WHERE (cliente = " + codigoCliente + " and hv.tipohistoricovinculo = 'EN' AND dataregistro <= '" + Uteis.getDataJDBC(data) + " 23:59:59' AND colaborador =" + codigoColaborador + "\n" + tipoColaboradorSQL + " ) \n");
        sql.append(" AND not exists ( SELECT cliente from historicovinculo ");
        sql.append(" WHERE colaborador =").append(codigoColaborador)
                .append("\nand cliente = ").append(codigoCliente)
                .append("\nAND hv.tipocolaborador = historicovinculo.tipocolaborador")
                .append("\nAND tipohistoricovinculo = 'SD' ").append(tipoColaboradorSQL)
                .append("\nAND dataregistro <= '" + Uteis.getDataJDBC(data) + " 23:59:59'")
                .append("\nand dataregistro >= hv.dataregistro) \n");
        try (Statement stm = con.createStatement()) {
            try (ResultSet rs = stm.executeQuery(sql.toString())) {
                rs.next();
                return rs.getInt("cont") > 0;
            }
        }
    }

    public Boolean verificarVinculoNoPeriodo(Date data, Integer codigoCliente, Integer codigoColaborador) throws Exception {
        StringBuilder sql = new StringBuilder();
        sql.append(" SELECT COUNT(codigo) AS cont FROM historicovinculo hv");
        sql.append(" WHERE hv.tipohistoricovinculo = 'EN' AND dataregistro <= '" + Uteis.getDataJDBC(data) + " 23:59:59' AND colaborador =" + codigoColaborador + " AND cliente = " + codigoCliente + " \n");
        sql.append(" and dataregistro >= (SELECT MAX(dataregistro) from historicovinculo \n");
        sql.append(" WHERE tipohistoricovinculo = 'EN' AND dataregistro <= '" + Uteis.getDataJDBC(data) + " 23:59:59' AND cliente = " + codigoCliente + ") \n");
        try (ResultSet consulta = criarConsulta(sql.toString(), con)) {
            consulta.next();
            return consulta.getInt("cont") > 0;
        }
    }

    public List<HistoricoVinculoVO> consultarVinculoNaData(Date dataFinal, Integer codigoColaborador, int nivelMontarDados, TipoColaboradorEnum... tiposColaborador) throws Exception {
        StringBuilder sql = new StringBuilder();
        sql.append("SELECT\n");
        sql.append("  hv.*\n");
        sql.append("FROM historicovinculo hv\n");
        sql.append("WHERE 1 = 1\n");
        sql.append("      AND hv.colaborador = ").append(codigoColaborador).append("\n");
        sql.append("      AND (hv.dataregistro <= '").append(Uteis.getDataJDBC(dataFinal)).append(" 23:59:59' AND tipohistoricovinculo = 'EN')\n");
        sql.append("      AND cliente NOT IN (SELECT\n");
        sql.append("                            cliente\n");
        sql.append("                          FROM historicovinculo\n");
        sql.append("                          WHERE 1 = 1\n");
        sql.append("                                AND colaborador = ").append(codigoColaborador).append("\n");
        sql.append("                                AND tipohistoricovinculo = 'SD' AND historicovinculo.codigo > hv.codigo and historicovinculo.tipocolaborador = hv.tipocolaborador)\n");
        if (tiposColaborador != null && tiposColaborador.length > 0) {
            sql.append("      AND hv.tipocolaborador IN ('").append(TipoColaboradorEnum.getTiposSQL(true, tiposColaborador)).append("')\n");
        }

        try (ResultSet consulta = criarConsulta(sql.toString(), con)) {
            return montarDadosConsulta(consulta, nivelMontarDados);
        }
    }

    public HistoricoVinculoVO consultarPorMaisRecenteClienteConsultor(Integer cliente, Integer consultor, int nivelMontarDados) throws Exception {
        String sql = "SELECT * FROM historicovinculo "
                + "WHERE cliente = ? "
                + " AND colaborador = ?"
                + " ORDER BY dataregistro desc limit 1 ";
        try (PreparedStatement sqlConsultar = con.prepareStatement(sql)) {
            sqlConsultar.setInt(1, cliente);
            sqlConsultar.setInt(2, consultor);
            try (ResultSet tabelaResultado = sqlConsultar.executeQuery()) {
                if (!tabelaResultado.next()) {
                    return null;
                }
                return (montarDados(tabelaResultado, nivelMontarDados));
            }
        }
    }

    public List<HistoricoVinculoVO> consultarUltimoHistoricoSaidaTransferencia(Date dataTransferencia, UsuarioVO respTransferencia, int nivelMontarDados) throws Exception {
        List<HistoricoVinculoVO> histVinculos = new ArrayList<>();
        StringBuilder sql = new StringBuilder();
        sql.append("select * from historicovinculo hv");
        sql.append(" left join historicovinculo hv2 on hv2.usuarioresponsavel = ").append(respTransferencia.getCodigo());
        sql.append(" and hv.cliente = hv2.cliente and hv2.codigo < hv.codigo ");
        sql.append(" and hv2.tipocolaborador = hv.tipocolaborador ");
        sql.append(" and hv2.tipohistoricovinculo = '").append("SD").append("'");
        sql.append(" and hv2.dataregistro between '").append(Uteis.getDataFormatoBD(dataTransferencia)).append(" 00:00:00").append("'");
        sql.append(" and '").append(Uteis.getDataFormatoBD(dataTransferencia)).append(" 23:59:59").append("'");
        sql.append(" where hv.dataregistro between '").append(Uteis.getDataFormatoBD(dataTransferencia)).append(" 00:00:00").append("'");
        sql.append(" and '").append(Uteis.getDataFormatoBD(dataTransferencia)).append(" 23:59:59").append("'");
        sql.append(" and hv.usuarioresponsavel = ").append(respTransferencia.getCodigo());
        sql.append(" and hv.tipohistoricovinculo = '").append("SD").append("'");
        sql.append(" and hv2.codigo is null");
        sql.append(" order by hv.codigo asc");

        try (PreparedStatement sqlConsultar = con.prepareStatement(sql.toString())) {
            try (ResultSet tabelaResultado = sqlConsultar.executeQuery()) {
                while (tabelaResultado.next()) {
                    histVinculos.add((montarDados(tabelaResultado, nivelMontarDados)));
                }
                return histVinculos;
            }
        }
    }

    @Override
    public List<HistoricoVinculoVO> consultarPorCodigoClientePeriodo(Date dataInicio, Date datafim, Integer valorConsulta, boolean controlarAcesso, int nivelMontarDados) throws Exception {
        consultar(getIdEntidade(), controlarAcesso);
        String sqlStr = "SELECT * FROM HistoricoVinculo WHERE cliente = " + valorConsulta + " AND dataregistro <= '" + Uteis.getDataJDBC(datafim) + " 23:59:59' ORDER BY dataRegistro";
        HashMap<String, HistoricoVinculoVO> atuais;
        String identificador;
        List<HistoricoVinculoVO> vinculos;
        try (Statement stm = con.createStatement()) {
            try (ResultSet tabelaResultado = stm.executeQuery(sqlStr)) {
                atuais = new HashMap<String, HistoricoVinculoVO>();

                vinculos = montarDadosConsulta(tabelaResultado, nivelMontarDados);
            }
        }
        for (HistoricoVinculoVO his : vinculos) {
            identificador = his.getColaborador().getCodigo().toString() + his.getTipoColaborador();
            if (his.getTipoHistoricoVinculo().equals("EN")) {
                atuais.put(identificador, his);
            } else if (Calendario.menor(his.getDataRegistro(), datafim)) {
                atuais.remove(identificador);
            }
        }

        vinculos = new ArrayList<HistoricoVinculoVO>();
        Set<String> codigosAtuais = atuais.keySet();
        for (String str : codigosAtuais) {
            vinculos.add(atuais.get(str));
        }

        return vinculos;
    }

    public String obterSqlMediaPermanenciaCarteira(Date fim, Integer professor, boolean cliente) {

        StringBuilder sql = new StringBuilder("SELECT ");
        if (cliente) {
            sql.append("cli.matricula, pes.nome,");
        }
        sql.append("cast(coalesce((SELECT dataregistro FROM historicovinculo \n");
        sql.append("WHERE cliente = hv.cliente AND tipocolaborador = 'TW' \n");
        sql.append("AND colaborador = hv.colaborador AND tipohistoricovinculo = 'SD' \n");
        sql.append("AND dataregistro >= hv.dataregistro ORDER BY dataregistro LIMIT 1),'");
        sql.append(Uteis.getDataAplicandoFormatacao(fim, "yyyy-MM-dd")).append(" 23:59:59') as date) - \n");
        sql.append("cast(hv.dataregistro  as date) AS duracao \n");
        sql.append("FROM historicovinculo hv \n");
        if (cliente) {
            sql.append("INNER JOIN cliente cli ON cli.codigo = hv.cliente\n");
            sql.append("INNER JOIN pessoa pes ON pes.codigo = cli.pessoa\n");
        }
        sql.append("WHERE hv.tipocolaborador = 'TW'\n");
        sql.append("AND hv.colaborador = ").append(professor).append("\n");
        sql.append("AND hv.tipohistoricovinculo = 'EN'\n");
        sql.append("ORDER BY hv.dataregistro DESC ");
        return sql.toString();
    }

    public Integer obterMediaPermanencia(Date fim, Integer professor) throws Exception {
        List<Integer> valores;
        try (ResultSet rs = criarConsulta("SELECT duracao FROM (" + obterSqlMediaPermanenciaCarteira(fim, professor, false)
                + ") as historico", con)) {
            valores = new ArrayList<Integer>();
            while (rs.next()) {
                int duracao = rs.getInt("duracao");
                if(duracao > 0){
                    valores.add(duracao);
                }
            }
        }
        if(valores.isEmpty()){
            return 0;
        }
        Collections.sort(valores);
        return Uteis.mediana(valores);
    }

    @Override
    public List<Integer> obterTrocasCarteira(Integer empresa, Date inicio, Date fim, Integer professor) throws Exception {
        List<Integer> lista;
        try (ResultSet rs = criarConsulta(getSqlTrocasCarteira(empresa, inicio, fim, professor).toString(), con)) {
            lista = new ArrayList<Integer>();
            while (rs.next()) {
                lista.add(rs.getInt("codigo"));
            }
        }
        return lista;
    }

    
    public StringBuilder getSqlTrocasCarteira(Integer empresa, Date inicio, Date fim, Integer professor) throws Exception{
        StringBuilder sql = new StringBuilder("SELECT distinct cli.codigo ");
        sql.append(" FROM historicovinculo hv \n");
        sql.append(" INNER JOIN cliente cli ON cli.codigo = hv.cliente AND cli.empresa = ").append(empresa).append("\n"); 
        sql.append(" WHERE hv.colaborador = ").append(professor).append("\n");
        sql.append(" AND hv.tipocolaborador = 'TW'\n");
        sql.append(" AND hv.dataregistro::DATE BETWEEN '").append(Uteis.getDataJDBC(inicio));
        sql.append("' AND '").append(Uteis.getDataJDBC(fim)).append("' \n");
        sql.append(" AND hv.tipohistoricovinculo = 'SD'");
        return sql;
    }

    @Override
    public void preencherDadosBITreino(DadosBITreinoJSON dados, Date fim, Integer professor) throws Exception {
        dados.setMediaPermanenciaCarteira(obterMediaPermanencia(fim, professor));
        try (ResultSet rsMenor = criarConsulta("SELECT * FROM (" + obterSqlMediaPermanenciaCarteira(fim, professor, true)
                + ") AS media WHERE duracao > 0 ORDER BY duracao LIMIT 1", con)) {
            if (rsMenor.next()) {
                dados.setMenorPermanencia(rsMenor.getInt("duracao"));
                dados.setMatriculaClienteMenorPermanencia(rsMenor.getString("matricula"));
                dados.setNomeClienteMenorPermanencia(rsMenor.getString("nome"));
            }
        }
        try (ResultSet rsMaior = criarConsulta("SELECT * FROM (" + obterSqlMediaPermanenciaCarteira(fim, professor, true)
                + ") AS media WHERE duracao > 0 ORDER BY duracao DESC LIMIT 1", con)) {
            if (rsMaior.next()) {
                dados.setMaiorPermanencia(rsMaior.getInt("duracao"));
                dados.setMatriculaClienteMaiorPermanencia(rsMaior.getString("matricula"));
                dados.setNomeClienteMaiorPermanencia(rsMaior.getString("nome"));
            }
        }
    }

    public void incluirHistoricoVinculoSaidaGestaoPersonal(Date dataFinal, Integer codigoColaborador, Integer codigoCliente, UsuarioVO usuarioVO) throws Exception {
        StringBuilder sql = new StringBuilder();
        sql.append("INSERT INTO historicovinculo(dataregistro, tipohistoricovinculo, tipocolaborador, cliente, colaborador, origem, usuarioresponsavel) \n");
        sql.append("SELECT '").append(Uteis.getDataJDBC(dataFinal)).append("', 'SD', tipocolaborador, cliente, colaborador, 'GESTÃO PERSONAL', ").append(usuarioVO.getCodigo()).append(" \n");
        sql.append("FROM historicovinculo hv \n");
        sql.append("WHERE 1 = 1\n");
        sql.append("AND hv.colaborador = ").append(codigoColaborador).append("\n");
        sql.append("AND hv.cliente = ").append(codigoCliente).append("\n");
        sql.append("AND (hv.dataregistro <= '").append(Uteis.getDataJDBC(dataFinal)).append(" 23:59:59' AND tipohistoricovinculo = 'EN')\n");
        sql.append("AND cliente NOT IN (SELECT\n");
        sql.append("    cliente\n");
        sql.append("    FROM historicovinculo\n");
        sql.append("    WHERE 1 = 1\n");
        sql.append("    AND colaborador = ").append(codigoColaborador).append("\n");
        sql.append("    AND tipohistoricovinculo = 'SD' AND historicovinculo.codigo > hv.codigo and historicovinculo.tipocolaborador = hv.tipocolaborador)\n");
        sql.append("AND hv.tipocolaborador IN ('PT','PI','PE')\n");
        sql.append("ORDER BY hv.codigo DESC LIMIT 1;");

        try {
            con.setAutoCommit(false);
            try (PreparedStatement sqlExcluir = con.prepareStatement(sql.toString())) {
                sqlExcluir.execute();
            }
            con.commit();
        } catch (Exception e) {
            con.rollback();
            con.setAutoCommit(true);
            throw e;
        } finally {
            con.setAutoCommit(true);
        }
    }
}

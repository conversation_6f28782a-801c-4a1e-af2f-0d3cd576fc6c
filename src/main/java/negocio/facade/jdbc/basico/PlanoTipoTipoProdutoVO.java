package negocio.facade.jdbc.basico;

import negocio.comuns.arquitetura.SuperVO;
import negocio.comuns.plano.enumerador.TipoProduto;

public class PlanoTipoTipoProdutoVO extends SuperVO {

    private Integer codigo;
    private Integer planoTipo;
    private String tipoProduto;
    private String codigoOperacaoFinanceira;

    private TipoProduto tipoProdutoEnum;

    public PlanoTipoTipoProdutoVO(String codigoTipo, TipoProduto tipoProduto) {
        this.tipoProduto = codigoTipo;
        this.tipoProdutoEnum = tipoProduto;
    }

    public PlanoTipoTipoProdutoVO(Integer codigo, Integer planotipo, String tipoproduto, String codigoOperacaoFinanceira) {
        this.codigo = codigo;
        this.planoTipo = planotipo;
        this.tipoProduto = tipoproduto;
        this.codigoOperacaoFinanceira = codigoOperacaoFinanceira;
        this.tipoProdutoEnum = TipoProduto.getTipoProdutoCodigo(tipoproduto);
    }

    @Override
    public Integer getCodigo() {
        return codigo;
    }

    @Override
    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public String getTipoProduto() {
        return tipoProduto;
    }

    public void setTipoProduto(String tipoProduto) {
        this.tipoProduto = tipoProduto;
    }

    public String getCodigoOperacaoFinanceira() {
        return codigoOperacaoFinanceira;
    }

    public void setCodigoOperacaoFinanceira(String codigoOperacaoFinanceira) {
        this.codigoOperacaoFinanceira = codigoOperacaoFinanceira;
    }

    public TipoProduto getTipoProdutoEnum() {
        return tipoProdutoEnum;
    }

    public void setTipoProdutoEnum(TipoProduto tipoProdutoEnum) {
        this.tipoProdutoEnum = tipoProdutoEnum;
    }

    public Integer getPlanoTipo() {
        return planoTipo;
    }

    public void setPlanoTipo(Integer planoTipo) {
        this.planoTipo = planoTipo;
    }
}

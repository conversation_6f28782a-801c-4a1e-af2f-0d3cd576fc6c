/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package negocio.facade.jdbc.basico;

import br.com.pactosolucoes.enumeradores.ProcessoAjusteGeralEnum;
import org.json.JSONArray;
import org.json.JSONObject;
import negocio.comuns.basico.LogAjusteGeralVO;
import negocio.comuns.utilitarias.Calendario;
import negocio.comuns.utilitarias.Uteis;
import negocio.facade.jdbc.arquitetura.SuperEntidade;
import negocio.interfaces.basico.LogAjusteGeralInterfaceFacade;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 */
public class LogAjusteGeral extends SuperEntidade implements LogAjusteGeralInterfaceFacade {

    public LogAjusteGeral() throws Exception {
        super();
    }

    public LogAjusteGeral(Connection conexao) throws Exception {
        super(conexao);
    }

    public static List<LogAjusteGeralVO> montarDadosConsulta(ResultSet tabelaResultado,
            int nivelMontarDados, Connection con) throws Exception {
        List<LogAjusteGeralVO> vetResultado = new ArrayList<LogAjusteGeralVO>();
        while (tabelaResultado.next()) {
            LogAjusteGeralVO obj = montarDados(tabelaResultado, nivelMontarDados, con);
            vetResultado.add(obj);
        }
        return vetResultado;
    }

    public static LogAjusteGeralVO montarDadosBasico(ResultSet dadosSQL) throws Exception {
        LogAjusteGeralVO obj = new LogAjusteGeralVO();
        obj.setNovoObj(false);
        obj.setCodigo(dadosSQL.getInt("codigo"));
        obj.setDatalancamento(Uteis.getDataJDBCTimestamp(dadosSQL.getTimestamp("dataLancamento")));
        obj.setUsuario(dadosSQL.getString("usuario"));
        obj.setUsuarioOAMD(dadosSQL.getString("usuarioOAMD"));
        obj.setProcessoAjusteGeralEnum(ProcessoAjusteGeralEnum.getConsultarPorCodigo(dadosSQL.getInt("processoAjusteGeral")));
        obj.setParametros(dadosSQL.getString("parametros"));
        return obj;
    }

    public static LogAjusteGeralVO montarDados(ResultSet dadosSQL, int nivelMontarDados, Connection con) throws Exception {
        return montarDadosBasico(dadosSQL);
    }

    public void incluir(LogAjusteGeralVO obj) throws Exception {
        String sql = "INSERT INTO logAjusteGeral(datalancamento, usuario, usuarioOAMD, processoAjusteGeral, parametros) VALUES (?, ?, ?, ?, ?)";
        PreparedStatement sqlInserir = con.prepareStatement(sql);
        int i = 1;
        sqlInserir.setTimestamp(i++, Uteis.getDataJDBCTimestamp(obj.getDatalancamento()));
        sqlInserir.setString(i++, obj.getUsuario());
        sqlInserir.setString(i++, obj.getUsuarioOAMD());
        sqlInserir.setInt(i++, obj.getProcessoAjusteGeralEnum().getCodigo());
        sqlInserir.setString(i++, obj.getParametros());
        sqlInserir.execute();
        obj.setCodigo(obterValorChavePrimariaCodigo());
        obj.setNovoObj(false);
    }

    public void incluir(Date dataLancamento, String usuarioLogado, String usuarioOAMD, ProcessoAjusteGeralEnum processoAjusteGeralEnum, String parametros) throws Exception{
        LogAjusteGeralVO obj = new LogAjusteGeralVO();
        obj.setDatalancamento(Calendario.hoje());
        obj.setUsuario(usuarioLogado);
        obj.setUsuarioOAMD(usuarioOAMD);
        obj.setProcessoAjusteGeralEnum(processoAjusteGeralEnum);
        obj.setParametros(parametros);
        incluir(obj);
    }

    public List<LogAjusteGeralVO> consultarPorAjusteGeral(ProcessoAjusteGeralEnum processoAjusteGeralEnum, int nivelMontarDados) throws Exception {
        List<LogAjusteGeralVO> objetos = new ArrayList<LogAjusteGeralVO>();
        String sql = "SELECT * FROM logAjusteGeral WHERE processoajustegeral = " + processoAjusteGeralEnum.getCodigo() + " ORDER BY datalancamento desc";
        PreparedStatement sqlConsulta = con.prepareStatement(sql);
        ResultSet resultado = sqlConsulta.executeQuery();
        while (resultado.next()) {
            LogAjusteGeralVO novoObj = montarDados(resultado, nivelMontarDados, con);
            objetos.add(novoObj);
        }
        return objetos;
    }

    public List<LogAjusteGeralVO> consultarPorAjusteGeral(Integer codigoProcessoAjusteGeral, int nivelMontarDados) throws Exception {
        List<LogAjusteGeralVO> objetos = new ArrayList<LogAjusteGeralVO>();
        String sql = "SELECT * FROM logAjusteGeral WHERE processoajustegeral = " + codigoProcessoAjusteGeral + " ORDER BY datalancamento desc";
        PreparedStatement sqlConsulta = con.prepareStatement(sql);
        ResultSet resultado = sqlConsulta.executeQuery();
        while (resultado.next()) {
            LogAjusteGeralVO novoObj = montarDados(resultado, nivelMontarDados, con);
            objetos.add(novoObj);
        }
        return objetos;
    }

    public List<LogAjusteGeralVO> consultarTodas(int nivelMontarDados) throws Exception {
        List<LogAjusteGeralVO> objetos = new ArrayList<LogAjusteGeralVO>();
        String sql = "SELECT * FROM logAjusteGeral ORDER BY datalancamento desc";
        PreparedStatement sqlConsulta = con.prepareStatement(sql);
        ResultSet resultado = sqlConsulta.executeQuery();
        while (resultado.next()) {
            LogAjusteGeralVO novoObj = montarDados(resultado, nivelMontarDados, con);
            objetos.add(novoObj);
        }
        return objetos;
    }
}
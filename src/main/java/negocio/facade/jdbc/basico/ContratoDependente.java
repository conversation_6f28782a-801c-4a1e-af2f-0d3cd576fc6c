package negocio.facade.jdbc.basico;

import negocio.comuns.arquitetura.LogVO;
import negocio.comuns.arquitetura.UsuarioVO;
import negocio.comuns.basico.ClienteVO;
import negocio.comuns.basico.ContratoDependenteVO;
import negocio.comuns.basico.FamiliarVO;
import negocio.comuns.basico.ParentescoVO;
import negocio.comuns.contrato.AfastamentoContratoDependenteVO;
import negocio.comuns.contrato.ContratoVO;
import negocio.comuns.utilitarias.Calendario;
import negocio.comuns.utilitarias.ConsistirException;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisValidacao;
import negocio.facade.jdbc.arquitetura.Log;
import negocio.facade.jdbc.arquitetura.SuperEntidade;
import negocio.facade.jdbc.arquitetura.ZillyonWebFacade;
import negocio.facade.jdbc.contrato.AfastamentoContratoDependente;
import negocio.facade.jdbc.contrato.Contrato;
import negocio.interfaces.basico.ContratoDependenteInterfaceFacade;
import negocio.interfaces.contrato.ContratoInterfaceFacade;
import relatorio.negocio.comuns.sad.SituacaoClienteSinteticoEnum;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.Statement;
import java.sql.Types;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Optional;

public class ContratoDependente extends SuperEntidade implements ContratoDependenteInterfaceFacade {

    public static final String SQL_DEPENDENTES = "select %s  from contratodependentehistorico ch \n"
            + "inner join contratodependente dpc on dpc.codigo = ch.contratodependente\n"
            + "inner join cliente cli on ch.dependente = cli.codigo\n"
            + "inner join empresa e on e.codigo = cli.empresa\n"
            + "inner join pessoa p on p.codigo = cli.pessoa\n"
            + "inner join contrato c on c.codigo = dpc.contrato\n"
            + "where 1 = 1\n"
            + " %s ";

    public static final String SQL_HISTORICO_DEPENDENTES = "select %s from contratodependentehistorico cdh\n" +
            "inner join contratodependente dpc on dpc.codigo = cdh.contratodependente\n" +
            "inner join cliente cli on cdh.dependente = cli.codigo\n" +
            "where 1 = 1\n" +
            "%s";

    public static final String SQL_DEPENDENTES_VINCULADOS_CONTAR = String.format(SQL_HISTORICO_DEPENDENTES,
            "count(distinct cdh.dependente)",
            " AND cdh.iniciodependencia  >= '%s' AND cdh.iniciodependencia  <= '%s' %s ");

    public static final String SQL_DEPENDENTES_DESVINCULADOS_CONTAR = String.format(SQL_HISTORICO_DEPENDENTES,
            "count(distinct cdh.dependente)",
            " AND (coalesce (cdh.finaldependencia,dpc.datafinalajustada) + interval '1 day')  >= '%s'" +
                    " AND (coalesce (cdh.finaldependencia,dpc.datafinalajustada) + interval '1 day') <= '%s' %s ");

    public static final String SQL_DEPENDENTES_VIGENTE_CONTAR = String.format(SQL_DEPENDENTES,
            "count(distinct cli.codigo)",
            " AND ch.iniciodependencia <=  '%s' AND coalesce(ch.finaldependencia,dpc.datafinal) >= '%s'  %s ");

    public static final String SQL_DEPENDENTES_VIGENTE_LISTAR = String.format(SQL_DEPENDENTES,
            "c.*, p.nome as nomedependente, cli.matricula as matriculadependente, cli.codigo as  codigodependente",
            " AND ch.iniciodependencia <=  '%s' AND coalesce(ch.finaldependencia,dpc.datafinal) >= '%s'  %s ");

    public ContratoDependente() throws Exception {
    }

    public ContratoDependente(Connection conexao) throws Exception {
        super(conexao);
    }

    private void incluir(ContratoDependenteVO obj) throws Exception {
        String sql = "INSERT INTO contratodependente(contrato, datainicio, datafinal, datafinalajustada, posicaodependente)"
                + " VALUES (?, ?, ?, ?, ?)";

        try (PreparedStatement sqlInserir = con.prepareStatement(sql)) {
            int i = 0;
            sqlInserir.setInt(++i, obj.getContrato().getCodigo());
            sqlInserir.setDate(++i, Uteis.getDataJDBC(obj.getDataInicio()));
            sqlInserir.setDate(++i, Uteis.getDataJDBC(obj.getDataFinal()));
            sqlInserir.setDate(++i, Uteis.getDataJDBC(obj.getDataFinalAjustada()));
            sqlInserir.setInt(++i, obj.getPosicaoDependente());
            sqlInserir.execute();
        }

        obj.setCodigo(obterValorChavePrimariaCodigo());
        obj.setNovoObj(false);
    }

    public void alterar(ContratoDependenteVO obj) throws Exception {
        if (obj != null) {
            String sql = "UPDATE contratodependente\n" +
                    "SET cliente = ?\n" +
                    "WHERE codigo = ?";

            try (PreparedStatement sqlInserir = con.prepareStatement(sql)) {
                int i = 0;
                if (obj.getCliente() != null && !UteisValidacao.emptyNumber(obj.getCliente().getCodigo())) {
                    sqlInserir.setInt(++i, obj.getCliente().getCodigo());
                } else {
                    sqlInserir.setNull(++i, Types.INTEGER);
                }
                sqlInserir.setInt(++i, obj.getCodigo());
                sqlInserir.execute();
            }

            obj.setNovoObj(false);

            if (obj.getCliente() != null && obj.getCliente().getCodigo() > 0) {
                ContratoDependenteHistorico contratoDependenteHistoricoDAO = new ContratoDependenteHistorico(con);
                contratoDependenteHistoricoDAO.incluirDependente(obj);
                contratoDependenteHistoricoDAO = null;
            }
        }
    }

    @Override
    public void incluir(ContratoVO obj) throws Exception {
        List<ContratoDependenteVO> contratosDependentes = new ArrayList<>();
        for (int i = 0; i < obj.getPlano().getQuantidadeCompartilhamentos(); i++) {
            ContratoDependenteVO contratoDependenteVO = new ContratoDependenteVO();
            contratoDependenteVO.setContrato(obj);
            contratoDependenteVO.setDataInicio(obj.getVigenciaDe());
            contratoDependenteVO.setDataFinal(obj.getVigenciaAte());
            contratoDependenteVO.setDataFinalAjustada(obj.getVigenciaAteAjustada());
            contratoDependenteVO.setPosicaoDependente(i + 1);
            contratosDependentes.add(contratoDependenteVO);
        }

        if (!contratosDependentes.isEmpty()) {
            incluir(contratosDependentes);
        }
    }

    private void incluir(List<ContratoDependenteVO> contratosDependentes) throws Exception {
        for (ContratoDependenteVO contratoDependenteVO : contratosDependentes) {
            incluir(contratoDependenteVO);
        }
    }

    @Override
    public void finalizarDependencia(ContratoDependenteVO obj) throws Exception {
        String sql = "UPDATE contratodependente SET cliente = NULL WHERE codigo = ?";

        try (PreparedStatement sqlInserir = con.prepareStatement(sql)) {
            int i = 0;
            sqlInserir.setInt(++i, obj.getCodigo());
            sqlInserir.execute();
        }

        AfastamentoContratoDependente afastamentoDAO = new AfastamentoContratoDependente(con);
        if (afastamentoDAO.existeAfastamentoPorContratoDependente(obj.getCodigo())) {
            String sqlDataOriginal = "UPDATE contratodependente SET datafinalajustada = datafinal WHERE codigo = ?";
            try (PreparedStatement  pstm = con.prepareStatement(sqlDataOriginal)) {
                int i = 0;
                pstm.setInt(++i, obj.getCodigo());
                pstm.execute();
            }
            String sqlRemoverAfastamentos = "DELETE FROM afastamentocontratodependente WHERE contratodependente = ?";
            try (PreparedStatement  pstm = con.prepareStatement(sqlRemoverAfastamentos)) {
                int i = 0;
                pstm.setInt(++i, obj.getCodigo());
                pstm.execute();
            }
        }

        if (!Calendario.igual(obj.getDataInicio(), obj.getContrato().getVigenciaDe()) && Calendario.maior(obj.getDataInicio(), Calendario.hoje())) {
            String sqlVigenciaContratoTitular = "update contratodependente set datainicio = ?, datafinalajustada = ? where codigo = ?";
            try (PreparedStatement  pstm = con.prepareStatement(sqlVigenciaContratoTitular)) {
                int i = 0;
                pstm.setDate(++i, Uteis.getDataJDBC(obj.getContrato().getVigenciaDe()));
                pstm.setDate(++i, Uteis.getDataJDBC(obj.getContrato().getVigenciaAteAjustada()));
                pstm.setInt(++i, obj.getCodigo());
                pstm.execute();
            }
        }

        ContratoDependenteHistorico contratoDependenteHistoricoDAO = new ContratoDependenteHistorico(con);
        contratoDependenteHistoricoDAO.removerDependente(obj);
        contratoDependenteHistoricoDAO = null;
    }

    @Override
    public void finalizarDependenciaTitularCancelado(ContratoDependenteVO obj) throws Exception {
        String sql = "UPDATE contratodependente SET datafinalajustada = ? WHERE codigo = ?";

        try (PreparedStatement sqlInserir = con.prepareStatement(sql)) {
            int i = 0;
            sqlInserir.setDate(++i, Uteis.getDataJDBC(obj.getContrato().getVigenciaAteAjustada()));
            sqlInserir.setInt(++i, obj.getCodigo());
            sqlInserir.execute();
        }

        ContratoDependenteHistorico cdhDAO = new ContratoDependenteHistorico(con);
        cdhDAO.removerDependente(obj);
        cdhDAO = null;
    }

    public boolean existeDependenciaVigente(Integer codCliente) throws Exception {
        String sql = "SELECT codigo FROM contratodependente\n" +
                "WHERE cliente = " + codCliente + "\n" +
                "AND datafinalajustada >= current_date";
        return existe(sql, con);
    }

    public boolean existeDependenciaVigenteNoPeriodo(Integer codCliente, Date dataInicio, Date dataFinal) throws Exception {
        StringBuilder sql = new StringBuilder();
        sql.append("SELECT codigo FROM contratodependente\n");
        sql.append("WHERE cliente = ").append(codCliente).append("\n");
        sql.append("AND (datafinalajustada > '").append(Calendario.getData(dataInicio, "yyyy-MM-dd")).append("'\n");
        sql.append("OR '").append(Calendario.getData(dataFinal, "yyyy-MM-dd")).append("' BETWEEN datainicio AND datafinalajustada)\n");
        return existe(sql.toString(), con);
    }

    public int quantidadeContratoDependente(Integer contrato, boolean somenteDisponiveis) throws Exception {
        StringBuilder sql = new StringBuilder();
        sql.append("SELECT count(codigo) as qtd\n")
                .append("FROM contratodependente c\n")
                .append("WHERE contrato = ").append(contrato).append("\n");
        if (somenteDisponiveis){
            sql.append("AND coalesce(cliente,0) = 0");
        }

        try (PreparedStatement ps = con.prepareStatement(sql.toString());
            ResultSet rs = ps.executeQuery()) {
            if (rs.next()) {
                return rs.getInt("qtd");
            }
            return 0;
        }
    }

    public Optional<ContratoDependenteVO> findByCodigo(Integer codigo) throws Exception {
        String sql = "SELECT * FROM contratodependente WHERE codigo = ?";
        try (PreparedStatement ps = con.prepareStatement(sql)) {
            int i = 0;
            ps.setInt(++i, codigo);
            try (ResultSet rs = ps.executeQuery()) {
                if (rs.next()) {
                    return Optional.of(montarDados(rs, con));
                }
                return Optional.empty();
            }
        }
    }

    public Optional<ContratoDependenteVO> findByDependente(Integer dependente) throws Exception {
        String sql = "SELECT * FROM contratodependente WHERE cliente = ? " +
                "AND datafinalajustada > ?";
        try (PreparedStatement ps = con.prepareStatement(sql)) {
            int i = 0;
            ps.setInt(++i, dependente);
            ps.setDate(++i, Uteis.getDataJDBC(Calendario.hoje()));
            try (ResultSet rs = ps.executeQuery()) {
                if (rs.next()) {
                    return Optional.of(montarDados(rs, con));
                }
                return Optional.empty();
            }
        }
    }

    public List<ContratoDependenteVO> findAllByContratoOrderByPosicaoDependente(Integer contrato) throws Exception {
        List<ContratoDependenteVO>  contratosDependentes = new ArrayList<>();

        String sql = "SELECT * FROM contratodependente WHERE COALESCE(cliente,0) <> 0 AND contrato = ? ORDER BY posicaodependente;";
        try (PreparedStatement ps = con.prepareStatement(sql)) {
            int i = 0;
            ps.setInt(++i, contrato);
            try (ResultSet rs = ps.executeQuery()) {
                while (rs.next()) {
                    ContratoDependenteVO contratoDependenteVO = montarDados(rs, con);
                    contratosDependentes.add(contratoDependenteVO);
                }
                return contratosDependentes;
            }
        }
    }

    public List<ContratoDependenteVO> findAllByContrato(Integer contrato) throws Exception {
        List<ContratoDependenteVO>  contratoDependentes = new ArrayList<>();

        String sql = "SELECT * FROM contratodependente WHERE contrato = ?;";
        try (PreparedStatement ps = con.prepareStatement(sql)) {
            int i = 0;
            ps.setInt(++i, contrato);
            try (ResultSet rs = ps.executeQuery()) {
                while (rs.next()) {
                    ContratoDependenteVO contratoDependenteVO = montarDados(rs, con);
                    contratoDependentes.add(contratoDependenteVO);
                }
                return contratoDependentes;
            }
        }
    }

    public List<ContratoDependenteVO> findAllByContratoAndCliente(Integer contrato, Integer cliente) throws Exception {
        List<ContratoDependenteVO>  contratoDependentes = new ArrayList<>();
        String sql = "SELECT * FROM contratodependente WHERE contrato = ? and cliente = ?;";
        try (PreparedStatement ps = con.prepareStatement(sql)) {
            int i = 0;
            ps.setInt(++i, contrato);
            ps.setInt(++i, cliente);
            try (ResultSet rs = ps.executeQuery()) {
                while (rs.next()) {
                    contratoDependentes.add(montarDados(rs, con));
                }
                return contratoDependentes;
            }
        }
    }

    public Optional<ContratoDependenteVO> consultarProximoContratoDependenteDisponivelPorContrato(Integer codigoContrato) throws Exception {
        String sql = "SELECT * FROM contratodependente WHERE contrato = ? " +
                "AND COALESCE(cliente,0) = 0 ORDER BY posicaodependente limit 1";
        try (PreparedStatement ps = con.prepareStatement(sql)) {
            int i = 0;
            ps.setInt(++i, codigoContrato);
            try (ResultSet rs = ps.executeQuery()) {
                if (rs.next()) {
                    return Optional.of(montarDados(rs, con));
                }
                return Optional.empty();
            }
        }
    }

    public List<ContratoDependenteVO> findAllByCliente(ClienteVO clienteVO, Integer limit) throws Exception {
        List<ContratoDependenteVO> contratosDependentes = new ArrayList<>();

        String sql = "SELECT * FROM contratodependente WHERE cliente = ? ORDER BY datafinalajustada DESC;";
        if(!UteisValidacao.emptyNumber(limit)){
            sql = sql.replace(";"," limit "+limit+";");
        }
        try (PreparedStatement ps = con.prepareStatement(sql)) {
            int i = 0;
            ps.setInt(++i, clienteVO.getCodigo());
            try (ResultSet rs = ps.executeQuery()) {
                while (rs.next()) {
                    ContratoDependenteVO contratoDependenteVO = montarDados(rs, con);
                    contratosDependentes.add(contratoDependenteVO);
                }
                return contratosDependentes;
            }
        }
    }

    public List<ContratoDependenteVO> findAllVigentByCliente(Integer codCliente, Date dataConsulta) throws Exception {
        List<ContratoDependenteVO> contratosDependentes = new ArrayList<>();

        StringBuilder sql = new StringBuilder();

        sql.append("SELECT *\n");
        sql.append("FROM contratodependente\n");
        sql.append("WHERE cliente = ").append(codCliente).append("\n");
        sql.append("AND '").append(Calendario.getData(dataConsulta, "yyyy-MM-dd")).append(" 00:00:00.000' BETWEEN datainicio AND datafinalajustada");

        try (Statement ps = con.createStatement()) {
            try (ResultSet rs = ps.executeQuery(sql.toString())) {
                while (rs.next()) {
                    ContratoDependenteVO contratoDependenteVO = montarDados(rs, con);
                    contratosDependentes.add(contratoDependenteVO);
                }
                return contratosDependentes;
            }
        }
    }

    private static ContratoDependenteVO montarDados(ResultSet rs, Connection con) throws Exception {
        ContratoDependenteVO contratoDependenteVO = new ContratoDependenteVO();
        contratoDependenteVO.setCodigo(rs.getInt("codigo"));
        contratoDependenteVO.setPosicaoDependente(rs.getInt("posicaodependente"));
        contratoDependenteVO.getCliente().setCodigo(rs.getInt("cliente"));
        contratoDependenteVO.getContrato().setCodigo(rs.getInt("contrato"));
        contratoDependenteVO.setDataInicio(rs.getDate("datainicio"));
        contratoDependenteVO.setDataFinal(rs.getDate("datafinal"));
        contratoDependenteVO.setDataFinalAjustada(rs.getDate("datafinalajustada"));

        montarDadosContrato(contratoDependenteVO, Uteis.NIVELMONTARDADOS_TIPOPESSOA, con);
        return contratoDependenteVO;
    }

    public static void montarDadosContrato(ContratoDependenteVO obj, int nivelMontarDados, Connection con) throws Exception {
        ContratoInterfaceFacade contrato = new Contrato(con);
        if (obj.getContrato().getCodigo() == 0) {
            obj.setContrato(new ContratoVO());
        } else {
            obj.setContrato(contrato.consultarPorChavePrimaria(obj.getContrato().getCodigo(), nivelMontarDados));
        }
    }

    public void incluirAfastamento(AfastamentoContratoDependenteVO afastamentoVO) throws Exception {
        try {
            con.setAutoCommit(false);
            validarConcorrenciaAfastamentos(afastamentoVO);

            AfastamentoContratoDependente afastamentoDAO = new AfastamentoContratoDependente(con);
            afastamentoDAO.incluirSemCommit(afastamentoVO);
            afastamentoDAO = null;

            atualizarVigencia(afastamentoVO.getContratoDependenteVO());

            if (UteisValidacao.emptyNumber(afastamentoVO.getContratoDependenteVO().getCliente().getTitularPlanoCompartilhado())
                    && Calendario.maior(afastamentoVO.getContratoDependenteVO().getDataFinalAjustada(), Calendario.hoje())) {
                refazerVinculoFamiliarCompartilharmentoContratoDependente(afastamentoVO.getContratoDependenteVO());
            }

            ZillyonWebFacade zillyonWebFacadeDAO = new ZillyonWebFacade(con);
            zillyonWebFacadeDAO.atualizarSintetico(afastamentoVO.getContratoDependenteVO().getCliente(), Calendario.hoje(),
                    SituacaoClienteSinteticoEnum.GRUPO_TODOS, false);
            zillyonWebFacadeDAO= null;

            logInclusaoOperacao(afastamentoVO, afastamentoVO.getResponsavelOperacao());

            con.commit();
        } catch (Exception ex) {
            con.rollback();
            throw ex;
        } finally {
            con.setAutoCommit(true);
        }
    }

    public void excluirAfastamento(AfastamentoContratoDependenteVO afastamentoVO, UsuarioVO responsavelOperacao) throws Exception {
        try {
            con.setAutoCommit(false);

            AfastamentoContratoDependente afastamentoDAO = new AfastamentoContratoDependente(con);
            afastamentoDAO.excluir(afastamentoVO);
            afastamentoDAO = null;

            atualizarVigencia(afastamentoVO.getContratoDependenteVO());

            ZillyonWebFacade zillyonWebFacadeDAO = new ZillyonWebFacade(con);
            zillyonWebFacadeDAO.atualizarSintetico(afastamentoVO.getContratoDependenteVO().getCliente(), Calendario.hoje(),
                    SituacaoClienteSinteticoEnum.GRUPO_TODOS, false);
            zillyonWebFacadeDAO= null;

            logEstornoOperacao(afastamentoVO, responsavelOperacao);

            con.commit();
        } catch (Exception ex) {
            con.rollback();
            throw ex;
        } finally {
            con.setAutoCommit(true);
        }
    }

    @Override
    public List<ContratoDependenteVO> findAllByContratoOrderByDataFinal(ClienteVO clienteVO) {

        List<ContratoDependenteVO> contratoDependentes = new ArrayList<>();

        String sql = "SELECT * FROM contratodependente WHERE cliente = ? ORDER BY datafinalajustada DESC limit 1;";
        try (PreparedStatement ps = con.prepareStatement(sql)) {
            int i = 0;
            ps.setInt(++i, clienteVO.getCodigo());
            try (ResultSet rs = ps.executeQuery()) {
                while (rs.next()) {
                    ContratoDependenteVO contratoDependenteVO = montarDados(rs, con);
                    contratoDependentes.add(contratoDependenteVO);
                }
                return contratoDependentes;
            }
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    @Override
    public void alterarVigenciaFinalContratoDependente(Integer contrato, Date dataFinal) throws Exception {
        String sql = "UPDATE contratodependente SET datafinalajustada = ? WHERE contrato = ?";

        try (PreparedStatement sqlUpdate = con.prepareStatement(sql)) {
            int i = 0;
            sqlUpdate.setDate(++i, Uteis.getDataJDBC(dataFinal));
            sqlUpdate.setInt(++i, contrato);
            sqlUpdate.execute();
        }
    }

    @Override
    public void removerDependenteContrato(Integer codigo) throws Exception {
        String sql = "UPDATE contratodependente SET cliente = null WHERE codigo = ?";

        try (PreparedStatement sqlUpdate = con.prepareStatement(sql)) {
            int i = 0;
            sqlUpdate.setInt(++i, codigo);
            sqlUpdate.execute();
        }
    }

    private void logEstornoOperacao(AfastamentoContratoDependenteVO obj, UsuarioVO responsavel) throws Exception {
        ContratoDependenteVO contrato = obj.getContratoDependenteVO();

        LogVO log = new LogVO();
        log.setOperacao("ESTORNO DE OPERAÇÃO");
        log.setChavePrimaria(obj.getCodigo().toString());
        log.setDataAlteracao(Calendario.hoje());
        log.setDescricao("Estorno do afastamento de dependente - " + obj.getTipoAfastamentoApresentar());
        log.setPessoa(contrato.getCliente().getPessoa().getCodigo());
        log.setNomeEntidade("CONTRATO DEPENDENTE");
        log.setResponsavelAlteracao(responsavel.getNome());
        log.setUserOAMD(responsavel.getUserOamd());
        log.setUsuarioVO(responsavel);
        log.setNomeCampo("OBS.");
        log.setValorCampoAlterado("Operação de " + obj.getTipoAfastamentoApresentar() + " do contrato dependente " + contrato.getCodigo()
                + ", iniciada no dia " + Uteis.getData(obj.getDataInicio()) + " e finalizada no dia "
                + Uteis.getData(obj.getDataTermino()) + ", lançada em " + Uteis.getData(obj.getDataRegistro())
                + " foi estornada, alterando o histórico do contrato e período de acesso do cliente.");
        Log logDAO = new Log(this.con);
        logDAO.incluirSemCommit(log);
        logDAO = null;
    }

    private void logInclusaoOperacao(AfastamentoContratoDependenteVO obj, UsuarioVO responsavel) throws Exception {
        ContratoDependenteVO contrato = obj.getContratoDependenteVO();

        LogVO log = new LogVO();
        log.setOperacao("INCLUSÃO DE OPERAÇÃO");
        log.setChavePrimaria(obj.getCodigo().toString());
        log.setDataAlteracao(Calendario.hoje());
        log.setDescricao("Afastamento de dependente - " + obj.getTipoAfastamentoApresentar());
        log.setPessoa(contrato.getCliente().getPessoa().getCodigo());
        log.setNomeEntidade("CONTRATO DEPENDENTE");
        log.setResponsavelAlteracao(responsavel.getNome());
        log.setUserOAMD(responsavel.getUserOamd());
        log.setUsuarioVO(responsavel);
        log.setNomeCampo("OBS.");
        log.setValorCampoAlterado("Operação de " + obj.getTipoAfastamentoApresentar() + " do contrato dependente " + contrato.getCodigo()
                + ", iniciada no dia " + Uteis.getData(obj.getDataInicio()) + " e finalizada no dia "
                + Uteis.getData(obj.getDataTermino()) + ", lançada em " + Uteis.getData(obj.getDataRegistro())
                + " foi lançada, alterando o histórico do contrato e período de acesso do cliente.");
        Log logDAO = new Log(this.con);
        logDAO.incluirSemCommit(log);
        logDAO = null;
    }

    private void atualizarVigencia(ContratoDependenteVO contratoDependenteVO) throws Exception {
        AfastamentoContratoDependente afastamentoDAO = new AfastamentoContratoDependente(con);

        List<AfastamentoContratoDependenteVO> afastamentos = afastamentoDAO.consultarPorContratoDependente(contratoDependenteVO.getCodigo(), null, Uteis.NIVELMONTARDADOS_MINIMOS);
        int qtdDias = 0;
        for (AfastamentoContratoDependenteVO afastamento : afastamentos) {
            qtdDias += afastamento.getNrDiasSomar();
        }

        Date novaData = Calendario.somarDias(contratoDependenteVO.getDataFinal(), qtdDias);
        contratoDependenteVO.setDataFinalAjustada(novaData);
        String sql = "UPDATE contratodependente SET datafinalajustada = ? WHERE codigo = ?";
        try (PreparedStatement ps = con.prepareStatement(sql)) {
            ps.setDate(1, Uteis.getDataJDBC(novaData));
            ps.setInt(2, contratoDependenteVO.getCodigo());
            ps.execute();
        }
    }

    private void validarConcorrenciaAfastamentos(AfastamentoContratoDependenteVO afastamentoVO) throws Exception {
        AfastamentoContratoDependente afastamentoContratoDependente = new AfastamentoContratoDependente(con);
        List<AfastamentoContratoDependenteVO> afastamentosConcorrente = afastamentoContratoDependente.consultarPorContratoDependente(afastamentoVO);
        if (!afastamentosConcorrente.isEmpty()) {
            throw new ConsistirException("Não será possível lançar este afastamento, pois já possui um afastamento para este aluno.");
        }
    }

    public void alterarVigencia(ContratoDependenteVO contratoDependenteVO) throws Exception {
        String sql = "UPDATE contratodependente SET datainicio = ?, datafinalajustada = ? WHERE codigo = ?";
        try (PreparedStatement ps = con.prepareStatement(sql)) {
            int i = 0;
            ps.setDate(++i, Uteis.getDataJDBC(contratoDependenteVO.getDataInicio()));
            ps.setDate(++i, Uteis.getDataJDBC(contratoDependenteVO.getDataFinalAjustada()));
            ps.setInt(++i, contratoDependenteVO.getCodigo());
            ps.execute();
        }
    }

    private void refazerVinculoFamiliarCompartilharmentoContratoDependente(ContratoDependenteVO contratoDependenteVO) throws Exception {
        try {
            Cliente clienteDAO = new Cliente(con);
            ClienteVO clienteTitular = clienteDAO.consultarPorCodigoPessoa(contratoDependenteVO.getContrato().getPessoa().getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS);
            ClienteVO clienteDependente = clienteDAO.consultarPorChavePrimaria(contratoDependenteVO.getCliente().getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS);
            clienteDAO = null;

            Parentesco parentescoDAO = new Parentesco(con);
            ParentescoVO parentescoTitular = parentescoDAO.obterParentescoCriandoSeNaoExiste("DEPENDENTE");
            parentescoDAO = null;

            Familiar familiarDAO = new Familiar(con);
            FamiliarVO familiarVO = familiarDAO.consultarPorClienteFamiliar(clienteTitular.getCodigo(), clienteDependente.getCodigo());
            familiarVO.setCliente(clienteTitular.getCodigo());
            familiarVO.setFamiliar(clienteDependente.getCodigo());
            familiarVO.setNome(clienteDependente.getNome_Apresentar());
            familiarVO.setCodAcesso(clienteDependente.getCodAcesso());
            familiarVO.setParentesco(parentescoTitular);
            familiarVO.setCompartilharPlano(true);
            familiarVO.setContratoCompartilhado(contratoDependenteVO.getContrato().getCodigo());
            familiarVO.setContratoDependente(new ContratoDependenteVO());
            familiarVO.getContratoDependente().setCodigo(contratoDependenteVO.getCodigo());
            familiarVO.getContratoDependente().setCliente(clienteDependente);

            if (UteisValidacao.emptyNumber(familiarVO.getCodigo())) {
                familiarDAO.incluir(familiarVO, true);
            } else {
                familiarDAO.alterar(familiarVO);
            }
            familiarDAO = null;
        } catch (Exception ex) {
            Uteis.logar(ex, ContratoDependente.class);
            throw new Exception(String.format("Falha ao tentar incluir vínculo familiar com o titular! Erro: " + ex.getMessage()));
        }
    }
}

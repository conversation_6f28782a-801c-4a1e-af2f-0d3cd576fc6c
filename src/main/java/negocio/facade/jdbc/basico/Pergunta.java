package negocio.facade.jdbc.basico;

import negocio.comuns.basico.PerguntaVO;
import negocio.comuns.basico.RespostaPerguntaVO;
import negocio.comuns.utilitarias.ConsistirException;
import negocio.comuns.utilitarias.Ordenacao;
import negocio.comuns.utilitarias.Uteis;
import negocio.facade.jdbc.arquitetura.SuperEntidade;
import negocio.interfaces.basico.PerguntaInterfaceFacade;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.sql.Statement;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Hashtable;
import java.util.List;

/**
 * Classe de persistência que encapsula todas as operações de manipulação dos dados da classe <code>PerguntaVO</code>.
 * Responsável por implementar operações como incluir, alterar, excluir e consultar pertinentes a classe <code>PerguntaVO</code>.
 * Encapsula toda a interação com o banco de dados.
 * @see PerguntaVO
 * @see SuperEntidade
 */
public class Pergunta extends SuperEntidade implements PerguntaInterfaceFacade {

    private Hashtable respostaPerguntas;

    public Pergunta() throws Exception {
        super();
        setRespostaPerguntas(new Hashtable());
    }

    public Pergunta(Connection con) throws Exception {
        super(con);
        setRespostaPerguntas(new Hashtable());
    }

    /**
     * Operação responsável por retornar um novo objeto da classe <code>PerguntaVO</code>.
     */
    public PerguntaVO novo() throws Exception {
        incluir(getIdEntidade());
        PerguntaVO obj = new PerguntaVO();
        return obj;
    }

    /* (non-Javadoc)
     * @see negocio.interfaces.basico.PerguntaInterfaceFacade#incluir(negocio.comuns.basico.PerguntaVO)
     */
    public void incluir(PerguntaVO obj) throws Exception {
        this.incluir(obj, false);
    }

    /**
     * Operação responsável por incluir no banco de dados um objeto da classe <code>PerguntaVO</code>.
     * Primeiramente valida os dados (<code>validarDados</code>) do objeto. Verifica a conexão com o banco de dados e a permissão do usuário
     * para realizar esta operacão na entidade.
     * Isto, através da operação <code>incluir</code> da superclasse.
     * @param obj  Objeto da classe <code>PerguntaVO</code> que será gravado no banco de dados.
     * @exception Exception Caso haja problemas de conexão, restrição de acesso ou validação de dados.
     */
    public void incluir(PerguntaVO obj, boolean centralEventos) throws Exception {
        try {
            con.setAutoCommit(false);
            PerguntaVO.validarDados(obj);
            if (centralEventos) {
//            	super.incluirObj(this.getIdEntidade());
            }
            // Pergunta.incluir(getIdEntidade());
            obj.realizarUpperCaseDados();
            String sql = "INSERT INTO Pergunta( descricao, tipoPergunta ) VALUES ( ?, ? )";
            try (PreparedStatement sqlInserir = con.prepareStatement(sql)) {
                sqlInserir.setString(1, obj.getDescricao());
                sqlInserir.setString(2, obj.getTipoPergunta());
                sqlInserir.execute();
            }
            obj.setCodigo(obterValorChavePrimariaCodigo());
            getFacade().getRespostaPergunta().incluirRespostaPerguntas(obj.getCodigo(), obj.getRespostaPerguntaVOs());
            obj.setNovoObj(false);
            con.commit();
        } catch (Exception e) {
            obj.setNovoObj(true);
            con.rollback();
            con.setAutoCommit(true);
            throw e;
        } finally {
            con.setAutoCommit(true);
        }
    }

    public void incluirSemCommit(PerguntaVO obj) throws Exception {
        try {
            PerguntaVO.validarDados(obj);
            obj.realizarUpperCaseDados();
            String sql = "INSERT INTO Pergunta( descricao, tipoPergunta ) VALUES ( ?, ? )";
            try (PreparedStatement sqlInserir = con.prepareStatement(sql)) {
                sqlInserir.setString(1, obj.getDescricao());
                sqlInserir.setString(2, obj.getTipoPergunta());
                sqlInserir.execute();
            }
            obj.setCodigo(obterValorChavePrimariaCodigo());
            getFacade().getRespostaPergunta().incluirRespostaPerguntas(obj.getCodigo(), obj.getRespostaPerguntaVOs());
            obj.setNovoObj(false);
        } catch (Exception e) {
            obj.setNovoObj(true);
            throw e;
        }
    }
    /* (non-Javadoc)
     * @see negocio.interfaces.basico.PerguntaInterfaceFacade#incluir(negocio.comuns.basico.PerguntaVO)
     */

    public void alterar(PerguntaVO obj) throws Exception {
        this.alterar(obj, false);
    }

    /**
     * Operação responsável por alterar no BD os dados de um objeto da classe <code>PerguntaVO</code>.
     * Sempre utiliza a chave primária da classe como atributo para localização do registro a ser alterado.
     * Primeiramente valida os dados (<code>validarDados</code>) do objeto. Verifica a conexão com o banco de dados e a permissão do usuário
     * para realizar esta operacão na entidade.
     * Isto, através da operação <code>alterar</code> da superclasse.
     * @param obj    Objeto da classe <code>PerguntaVO</code> que será alterada no banco de dados.
     * @exception Exception Caso haja problemas de conexão, restrição de acesso ou validação de dados.
     */
    public void alterar(PerguntaVO obj, boolean centralEventos) throws Exception {
        try {
            con.setAutoCommit(false);
            alterarSemCommit(obj);
            con.commit();
        } catch (Exception e) {
            con.rollback();
            con.setAutoCommit(true);
            throw e;
        } finally {
            con.setAutoCommit(true);
            }
    }

    public void alterarSemCommit(PerguntaVO obj) throws Exception {
            PerguntaVO.validarDados(obj);
            obj.realizarUpperCaseDados();
            String sql = "UPDATE Pergunta set descricao=?, tipoPergunta=? WHERE ((codigo = ?))";
        try (PreparedStatement sqlAlterar = con.prepareStatement(sql)) {
            sqlAlterar.setString(1, obj.getDescricao());
            sqlAlterar.setString(2, obj.getTipoPergunta());
            sqlAlterar.setInt(3, obj.getCodigo().intValue());
            sqlAlterar.execute();
        }
        getFacade().getRespostaPergunta().alterarRespostaPerguntas(obj.getCodigo(), obj.getRespostaPerguntaVOs());
        }

    /* (non-Javadoc)
     * @see negocio.interfaces.basico.PerguntaInterfaceFacade#incluir(negocio.comuns.basico.PerguntaVO)
     */

    public void excluir(PerguntaVO obj) throws Exception {
        this.excluir(obj, false);
    }

    /**
     * Operação responsável por excluir no BD um objeto da classe <code>PerguntaVO</code>.
     * Sempre localiza o registro a ser excluído através da chave primária da entidade.
     * Primeiramente verifica a conexão com o banco de dados e a permissão do usuário
     * para realizar esta operacão na entidade.
     * Isto, através da operação <code>excluir</code> da superclasse.
     * @param obj    Objeto da classe <code>PerguntaVO</code> que será removido no banco de dados.
     * @exception Exception Caso haja problemas de conexão ou restrição de acesso.
     */
    public void excluir(PerguntaVO obj, boolean centralEventos) throws Exception {
        try {
            con.setAutoCommit(false);
            if (centralEventos) {
//            	excluirObj(getIdEntidade());
            } else {
                excluir(getIdEntidade());
            }
            String sql = "DELETE FROM Pergunta WHERE ((codigo = ?))";
            try (PreparedStatement sqlExcluir = con.prepareStatement(sql)) {
                sqlExcluir.setInt(1, obj.getCodigo().intValue());
                sqlExcluir.execute();
            }
            getFacade().getRespostaPergunta().excluirRespostaPerguntas(obj.getCodigo());
            con.commit();
        } catch (Exception e) {
            con.rollback();
            con.setAutoCommit(true);
            throw e;
        } finally {
            con.setAutoCommit(true);
        }
    }

    /**
     * Responsável por realizar uma consulta de <code>Pergunta</code> através do valor do atributo 
     * <code>String tipoPergunta</code>. Retorna os objetos, com início do valor do atributo idêntico ao parâmetro fornecido.
     * Faz uso da operação <code>montarDadosConsulta</code> que realiza o trabalho de prerarar o List resultante.
     * @param   controlarAcesso Indica se a aplicação deverá verificar se o usuário possui permissão para esta consulta ou não.
     * @return  List Contendo vários objetos da classe <code>PerguntaVO</code> resultantes da consulta.
     * @exception Exception Caso haja problemas de conexão ou restrição de acesso.
     */
    public List consultarPorTipoPergunta(String valorConsulta, boolean controlarAcesso, int nivelMontarDados) throws Exception {
        consultar(getIdEntidade(), controlarAcesso);
        String sqlStr = "SELECT * FROM Pergunta WHERE upper( tipoPergunta ) like('" + valorConsulta.toUpperCase() + "%') ORDER BY tipoPergunta";
        try (Statement stm = con.createStatement()) {
            try (ResultSet tabelaResultado = stm.executeQuery(sqlStr)) {
                return (montarDadosConsulta(tabelaResultado, nivelMontarDados, this.con));
            }
        }
    }

    /**
     * Responsável por realizar uma consulta de <code>Pergunta</code> através do valor do atributo 
     * <code>String descricao</code>. Retorna os objetos, com início do valor do atributo idêntico ao parâmetro fornecido.
     * Faz uso da operação <code>montarDadosConsulta</code> que realiza o trabalho de prerarar o List resultante.
     * @param   controlarAcesso Indica se a aplicação deverá verificar se o usuário possui permissão para esta consulta ou não.
     * @return  List Contendo vários objetos da classe <code>PerguntaVO</code> resultantes da consulta.
     * @exception Exception Caso haja problemas de conexão ou restrição de acesso.
     */
    public List consultarPorDescricao(String valorConsulta, boolean controlarAcesso, int nivelMontarDados) throws Exception {
        consultar(getIdEntidade(), controlarAcesso);
        String sqlStr = "SELECT * FROM Pergunta WHERE upper( descricao ) like('%" + valorConsulta.toUpperCase() + "%') ORDER BY descricao";
        try (Statement stm = con.createStatement()) {
            try (ResultSet tabelaResultado = stm.executeQuery(sqlStr)) {
                return (montarDadosConsulta(tabelaResultado, nivelMontarDados, this.con));
            }
        }
    }

    /**
     * Responsável por realizar uma consulta de <code>Pergunta</code> através do valor do atributo
     * <code>String descricao</code>. Retorna os objetos, com início do valor do atributo idêntico ao parâmetro fornecido.
     * @param   valorConsulta Indica se a aplicação deverá verificar se o usuário possui permissão para esta consulta ou não.
     * @return  int Contendo o código da pergunta <code>PerguntaVO</code> resultantes da consulta.
     * @exception Exception Caso haja problemas de conexão ou restrição de acesso.
     */
    public int consultarPorDescricao(String valorConsulta) throws Exception {
        consultar(getIdEntidade());
        String sqlStr = "SELECT codigo FROM Pergunta WHERE upper( descricao ) like('" + valorConsulta.toUpperCase() + "%') ORDER BY descricao";
        try (Statement stm = con.createStatement()) {
            try (ResultSet tabelaResultado = stm.executeQuery(sqlStr)) {
                if (tabelaResultado.next()) {
                    return tabelaResultado.getInt("codigo");
                }
            }
        }
        return 0;
    }

    /**
     * Responsável por realizar uma consulta de <code>Pergunta</code> através do valor do atributo 
     * <code>Integer codigo</code>. Retorna os objetos com valores iguais ou superiores ao parâmetro fornecido.
     * Faz uso da operação <code>montarDadosConsulta</code> que realiza o trabalho de prerarar o List resultante.
     * @param   controlarAcesso Indica se a aplicação deverá verificar se o usuário possui permissão para esta consulta ou não.
     * @return  List Contendo vários objetos da classe <code>PerguntaVO</code> resultantes da consulta.
     * @exception Exception Caso haja problemas de conexão ou restrição de acesso.
     */
    public List consultarPorCodigo(Integer valorConsulta, boolean controlarAcesso, int nivelMontarDados) throws Exception {
        consultar(getIdEntidade(), controlarAcesso);
        String sqlStr = "SELECT * FROM Pergunta WHERE codigo >= " + valorConsulta.intValue() + " ORDER BY codigo";
        try (Statement stm = con.createStatement()) {
            try (ResultSet tabelaResultado = stm.executeQuery(sqlStr)) {
                return (montarDadosConsulta(tabelaResultado, nivelMontarDados, this.con));
            }
        }
    }

    public PerguntaVO consultarPorCodigoExato(Integer valorConsulta, boolean controlarAcesso, int nivelMontarDados) throws Exception {
        consultar(getIdEntidade(), controlarAcesso);
        String sql = "SELECT * FROM Pergunta WHERE codigo = ?";
        PerguntaVO pergunta;
        try (PreparedStatement sqlConsultar = con.prepareStatement(sql)) {
            sqlConsultar.setInt(1, valorConsulta.intValue());
            try (ResultSet tabelaResultado = sqlConsultar.executeQuery()) {
                pergunta = null;
                while (tabelaResultado.next()) {
                    pergunta = montarDados(tabelaResultado, nivelMontarDados, this.con);
                }
            }
        }
        return pergunta;
    }

    /**
     * Responsável por montar os dados de vários objetos, resultantes de uma consulta ao banco de dados (<code>ResultSet</code>).
     * Faz uso da operação <code>montarDados</code> que realiza o trabalho para um objeto por vez.
     * @return  List Contendo vários objetos da classe <code>PerguntaVO</code> resultantes da consulta.
     */
    public static List montarDadosConsulta(ResultSet tabelaResultado, int nivelMontarDados, Connection con) throws Exception {
        List vetResultado = new ArrayList();
        while (tabelaResultado.next()) {
            PerguntaVO obj = new PerguntaVO();
            obj = montarDados(tabelaResultado, nivelMontarDados, con);
            vetResultado.add(obj);
        }
        return vetResultado;
    }

    /**
     * Responsável por montar os dados resultantes de uma consulta ao banco de dados (<code>ResultSet</code>)
     * em um objeto da classe <code>PerguntaVO</code>.
     * @return  O objeto da classe <code>PerguntaVO</code> com os dados devidamente montados.
     */
    public static PerguntaVO montarDados(ResultSet dadosSQL, int nivelMontarDados, Connection con) throws Exception {
        PerguntaVO obj = new PerguntaVO();
        obj.setCodigo(new Integer(dadosSQL.getInt("codigo")));
        obj.setDescricao(dadosSQL.getString("descricao"));
        obj.setTipoPergunta(dadosSQL.getString("tipoPergunta"));
        obj.setNovoObj(new Boolean(false));
//        if (nivelMontarDados == Uteis.NIVELMONTARDADOS_DADOSBASICOS) {
//            return obj;
//        }
        RespostaPergunta respostaPergunta = new RespostaPergunta(con);
        obj.setRespostaPerguntaVOs(respostaPergunta.consultarRespostaPerguntas(obj.getCodigo(), nivelMontarDados));
        if (nivelMontarDados == Uteis.NIVELMONTARDADOS_DADOSENTIDADESUBORDINADAS) {
            return obj;
        }
        respostaPergunta = null;
        return obj;
    }

    /**
     * Operação responsável por adicionar um objeto da <code>RespostaPerguntaVO</code> no Hashtable <code>RespostaPerguntas</code>.
     * Neste Hashtable são mantidos todos os objetos de RespostaPergunta de uma determinada Pergunta.
     * @param obj  Objeto a ser adicionado no Hashtable.
     */
    public void adicionarObjRespostaPerguntas(RespostaPerguntaVO obj) throws Exception {
        getRespostaPerguntas().put(obj.getDescricaoRespota() + "", obj);
        //adicionarObjSubordinadoOC
    }

    /**
     * Operação responsável por remover um objeto da classe <code>RespostaPerguntaVO</code> do Hashtable <code>RespostaPerguntas</code>.
     * Neste Hashtable são mantidos todos os objetos de RespostaPergunta de uma determinada Pergunta.
     * @param DescricaoRespota Atributo da classe <code>RespostaPerguntaVO</code> utilizado como apelido (key) no Hashtable.
     */
    public void excluirObjRespostaPerguntas(String DescricaoRespota) throws Exception {
        getRespostaPerguntas().remove(DescricaoRespota + "");
        //excluirObjSubordinadoOC
    }

    /**
     * Operação responsável por localizar um objeto da classe <code>PerguntaVO</code>
     * através de sua chave primária. 
     * @exception Exception Caso haja problemas de conexão ou localização do objeto procurado.
     */
    public PerguntaVO consultarPorChavePrimaria(Integer codigoPrm, int nivelMontarDados) throws Exception {
        consultar(getIdEntidade(), false);
        String sql = "SELECT * FROM Pergunta WHERE codigo = ?";
        try (PreparedStatement sqlConsultar = con.prepareStatement(sql)) {
            sqlConsultar.setInt(1, codigoPrm.intValue());
            try (ResultSet tabelaResultado = sqlConsultar.executeQuery()) {
                if (!tabelaResultado.next()) {
                    throw new ConsistirException("Dados Não Encontrados ( Pergunta ).");
                }
                return (montarDados(tabelaResultado, nivelMontarDados, this.con));
            }
        }
    }

    public Hashtable getRespostaPerguntas() {
        return (respostaPerguntas);
    }

    public void setRespostaPerguntas(Hashtable respostaPerguntas) {
        this.respostaPerguntas = respostaPerguntas;
    }

    public String consultarJSON() throws Exception {
        StringBuilder json;
        boolean dados;
        try (ResultSet rs = getPS().executeQuery()) {
            PerguntaVO pergunta = new PerguntaVO();
            json = new StringBuilder();
            json.append("{\"aaData\":[");
            dados = false;
            while (rs.next()) {
                dados = true;
                json.append("[\"").append(rs.getString("codigo")).append("\",");
                json.append("\"").append(Uteis.normalizarStringJSON(rs.getString("descricao").trim())).append("\",");
                pergunta.setTipoPergunta(rs.getString("tipopergunta"));
                json.append("\"").append(pergunta.getTipoPergunta_Apresentar()).append("\"],");
            }
        }
        if (dados) {
            json.deleteCharAt(json.toString().length() - 1);
        }
        json.append("]}");
        return json.toString();
    }

    private PreparedStatement getPS() throws SQLException {
        String sql = "SELECT codigo, descricao, tipopergunta FROM pergunta ORDER BY descricao";
        return con.prepareStatement(sql);
    }

    public List consultarParaImpressao(String filtro, String ordem, String campoOrdenacao, int i) throws SQLException {

        List lista;
        try (ResultSet rs = getPS().executeQuery()) {
            lista = new ArrayList();
            while (rs.next()) {
                PerguntaVO p = new PerguntaVO();
                String geral = rs.getString("codigo") + rs.getString("descricao") + rs.getString("tipopergunta");
                if (geral.toLowerCase().contains(filtro.toLowerCase())) {
                    p.setCodigo(rs.getInt("codigo"));
                    p.setDescricao(rs.getString("descricao"));
                    p.setTipoPergunta(rs.getString("tipopergunta"));
                    lista.add(p);
                }
            }
        }
        if (campoOrdenacao.equals("Código")) {
            Ordenacao.ordenarLista(lista, "codigo");
        } else if (campoOrdenacao.equals("Descrição")) {
            Ordenacao.ordenarLista(lista, "descricao");
        } else if (campoOrdenacao.equals("Tipo")) {
            Ordenacao.ordenarLista(lista, "tipoPergunta_Apresentar");
        }
        if (ordem.contains("desc")) {
            Collections.reverse(lista);
        }
        return lista;

    }
}

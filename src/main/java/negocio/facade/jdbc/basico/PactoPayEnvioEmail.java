package negocio.facade.jdbc.basico;

import negocio.comuns.basico.PactoPayComunicacaoVO;
import negocio.comuns.basico.PactoPayEnvioEmailComunicacaoVO;
import negocio.comuns.basico.PactoPayEnvioEmailVO;
import negocio.comuns.financeiro.enumerador.PactoPayEnvioEmailStatusEnum;
import negocio.comuns.utilitarias.Calendario;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisValidacao;
import negocio.facade.jdbc.arquitetura.SuperEntidade;
import negocio.interfaces.basico.PactoPayEnvioEmailInterfaceFacade;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

public class PactoPayEnvioEmail extends SuperEntidade implements PactoPayEnvioEmailInterfaceFacade {

    public PactoPayEnvioEmail() throws Exception {
        super();
    }

    public PactoPayEnvioEmail(Connection conexao) throws Exception {
        super(conexao);
    }

    private PactoPayEnvioEmailVO montarDadosBasico(ResultSet rs) throws Exception {
        PactoPayEnvioEmailVO obj = new PactoPayEnvioEmailVO();
        obj.setNovoObj(false);
        obj.setCodigo(rs.getInt("codigo"));
        obj.setDataRegistro(rs.getTimestamp("dataRegistro"));
        obj.getPactoPayConfigVO().setCodigo(rs.getInt("pactopayconfig"));
        obj.getEmpresaVO().setCodigo(rs.getInt("empresa"));
        obj.setAssunto(rs.getString("assunto"));
        obj.setMensagem(rs.getString("mensagem"));
        obj.setStatus(PactoPayEnvioEmailStatusEnum.obterPorId(rs.getInt("status")));
        obj.setDados(rs.getString("dados"));
        obj.setEnvio(rs.getString("envio"));
        obj.setResposta(rs.getString("resposta"));
        obj.setDataExecucao(rs.getTimestamp("dataexecucao"));
        return obj;
    }

    private PactoPayEnvioEmailVO montarDados(ResultSet dadosSQL, int nivelMontarDados, Connection con) throws Exception {
        PactoPayEnvioEmailVO obj = montarDadosBasico(dadosSQL);
        if (nivelMontarDados == Uteis.NIVELMONTARDADOS_DADOSBASICOS) {
            return obj;
        }

        PactoPayComunicacao pactoPayComunicacaoDAO = new PactoPayComunicacao(con);
        obj.setListaPactoPayComunicacao(pactoPayComunicacaoDAO.consultarPorPactoPayEnvioEmail(obj.getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS));
        pactoPayComunicacaoDAO = null;
        return obj;
    }

    public void incluir(PactoPayEnvioEmailVO obj) throws Exception {
        try {
            this.con.setAutoCommit(false);
            incluirSemCommit(obj);
            this.con.commit();
        } catch (Exception ex) {
            this.con.rollback();
            throw ex;
        } finally {
            this.con.setAutoCommit(true);
        }
    }

    public void incluirSemCommit(PactoPayEnvioEmailVO obj) throws Exception {
        PactoPayEnvioEmailVO.validarDados(obj);
        String sql = "INSERT INTO pactopayenvioemail" +
                "(dataRegistro, pactopayconfig, empresa, " +
                "assunto, mensagem, status, " +
                "dados, envio, resposta, dataExecucao) " +
                "VALUES (?,?,?,?,?,?,?,?,?,?)";
        try (PreparedStatement ps = con.prepareStatement(sql)) {
            int i = 0;
            ps.setTimestamp(++i, Uteis.getDataJDBCTimestamp(Calendario.hoje()));
            resolveIntegerNull(ps, ++i, obj.getPactoPayConfigVO().getCodigo());
            resolveIntegerNull(ps, ++i, obj.getEmpresaVO().getCodigo());
            ps.setString(++i, obj.getAssunto());
            ps.setString(++i, obj.getMensagem());
            ps.setInt(++i, obj.getStatus().getId());
            ps.setString(++i, obj.getDados());
            ps.setString(++i, obj.getEnvio());
            ps.setString(++i, obj.getResposta());
            ps.setTimestamp(++i, Uteis.getDataJDBCTimestamp(obj.getDataExecucao()));

            ps.execute();
        }
        obj.setCodigo(obterValorChavePrimariaCodigo());
        obj.setNovoObj(false);
        incluirPactoPayEnvioEmailComunicacao(obj);
    }

    public void alterar(PactoPayEnvioEmailVO obj) throws Exception {
        try {
            this.con.setAutoCommit(false);
            alterarSemCommit(obj);
            this.con.commit();
        } catch (Exception ex) {
            this.con.rollback();
            throw ex;
        } finally {
            this.con.setAutoCommit(true);
        }
    }

    public void gravarSemCommit(PactoPayEnvioEmailVO obj) throws Exception {
        if (UteisValidacao.emptyNumber(obj.getCodigo())) {
            this.incluirSemCommit(obj);
        } else {
            this.alterarSemCommit(obj);
        }
    }

    public void alterarSemCommit(PactoPayEnvioEmailVO obj) throws Exception {
        PactoPayEnvioEmailVO.validarDados(obj);
        String sql = "UPDATE pactopayenvioemail SET " +
                "pactopayconfig = ?, empresa = ?, assunto = ?, " +
                "mensagem = ?, status = ?, dados = ?, " +
                "envio = ?, resposta = ?, dataExecucao = ? " +
                "WHERE codigo = ?";
        try (PreparedStatement ps = con.prepareStatement(sql)) {
            int i = 0;
            resolveIntegerNull(ps, ++i, obj.getPactoPayConfigVO().getCodigo());
            resolveIntegerNull(ps, ++i, obj.getEmpresaVO().getCodigo());
            ps.setString(++i, obj.getAssunto());
            ps.setString(++i, obj.getMensagem());
            ps.setInt(++i, obj.getStatus().getId());
            ps.setString(++i, obj.getDados());
            ps.setString(++i, obj.getEnvio());
            ps.setString(++i, obj.getResposta());
            ps.setTimestamp(++i, Uteis.getDataJDBCTimestamp(obj.getDataExecucao()));

            ps.setInt(++i, obj.getCodigo());
            ps.execute();
        }
    }

    public PactoPayEnvioEmailVO consultarPorChavePrimaria(Integer codigo, int nivelMontarDados) throws Exception {
        String sql = "SELECT * FROM PactoPayEnvioEmail WHERE codigo = ?";
        try (PreparedStatement ps = con.prepareStatement(sql)) {
            ps.setInt(1, codigo);
            try (ResultSet rs = ps.executeQuery()) {
                if (rs.next()) {
                    return montarDados(rs, nivelMontarDados, this.con);
                } else {
                    return null;
                }
            }
        }
    }

    public void alteraJobExcluido(boolean jobexcluido, Integer codigo) throws SQLException {
        try (PreparedStatement ps = con.prepareStatement("UPDATE pactopayenvioemail SET jobexcluido = ? WHERE codigo = ?")) {
            int i = 0;
            ps.setBoolean(++i, jobexcluido);
            ps.setInt(++i, codigo);
            ps.execute();
        }
    }

    public List<PactoPayEnvioEmailVO> consultar(Date dataInicialRegistro, Date dataFinalRegistro,
                                                int nivelMontarDados) throws Exception {

        StringBuilder sql = new StringBuilder();
        sql.append("SELECT \n");
        sql.append("p.*, \n");
        sql.append("pe.nome as nome_pessoa, \n");
        sql.append("e.nome as nome_empresa \n");
        sql.append("FROM pactopayenvioemail p \n");
        sql.append("LEFT JOIN pessoa pe on pe.codigo = p.pessoa \n");
        sql.append("LEFT JOIN empresa e on e.codigo = p.empresa \n");
        sql.append("WHERE 1 = 1 \n");
        boolean filtrou = false;
        if (dataInicialRegistro != null && dataFinalRegistro != null) {
            sql.append("AND p.dataregistro::date between '").append(Uteis.getDataFormatoBD(dataInicialRegistro)).append("' and '").append(Uteis.getDataFormatoBD(dataFinalRegistro)).append("' \n");
            filtrou = true;
        }
        sql.append("ORDER BY p.dataregistro desc \n");

        if (!filtrou) {
            throw new Exception("Informe algum filtro");
        }

        PreparedStatement statement = con.prepareStatement(sql.toString());
        ResultSet resultSet = statement.executeQuery();
        List<PactoPayEnvioEmailVO> lista = new ArrayList<>();
        while (resultSet.next()) {
            try {
                lista.add(montarDados(resultSet, nivelMontarDados, this.con));
            } catch (Exception ex) {
                ex.printStackTrace();
            }
        }
        return lista;
    }

    private void incluirPactoPayEnvioEmailComunicacao(PactoPayEnvioEmailVO obj) throws Exception {
        PactoPayEnvioEmailComunicacao pactoPayEnvioEmailComunicacaoDAO = new PactoPayEnvioEmailComunicacao(this.con);
        for (PactoPayComunicacaoVO comunicacaoVO : obj.getListaPactoPayComunicacao()) {
            try {
                PactoPayEnvioEmailComunicacaoVO objNovo = new PactoPayEnvioEmailComunicacaoVO() ;
                objNovo.setPactoPayEnvioEmailVO(obj);
                objNovo.setPactoPayComunicacaoVO(comunicacaoVO);
                pactoPayEnvioEmailComunicacaoDAO.incluirSemCommit(objNovo);
            } catch (Exception ex) {
                ex.printStackTrace();
            }
        }
        pactoPayEnvioEmailComunicacaoDAO = null;
    }
}

package negocio.facade.jdbc.basico;

import java.sql.Connection;
import negocio.interfaces.basico.*;
import negocio.comuns.basico.TipoColaboradorVO;
import negocio.facade.jdbc.arquitetura.*;
import java.sql.ResultSet;
import java.sql.PreparedStatement;
import java.sql.Statement;
import java.util.List;
import java.util.ArrayList;
import java.util.Iterator;
import negocio.comuns.utilitarias.*;
//import org.apache.tomcat.util.buf.TimeStamp;

/**
 * Classe de persistência que encapsula todas as operações de manipulação dos dados da classe <code>TipoColaboradorVO</code>.
 * Responsável por implementar operações como incluir, alterar, excluir e consultar pertinentes a classe <code>TipoColaboradorVO</code>.
 * Encapsula toda a interação com o banco de dados.
 * @see TipoColaboradorVO
 * @see SuperEntidade
 */
public class TipoColaborador extends SuperEntidade implements TipoColaboradorInterfaceFacade {   

    public TipoColaborador() throws Exception {
        super();
        setIdEntidade("Colaborador");
    }

    public TipoColaborador(Connection conexao) throws Exception {
        super(conexao);
        setIdEntidade("Colaborador");
    }

    /**
     * Operação responsável por retornar um novo objeto da classe <code>TipoColaboradorVO</code>.
     */
    public TipoColaboradorVO novo() throws Exception {
        incluir(getIdEntidade());
        return new TipoColaboradorVO();
    }

    /**
     * Operação responsável por incluir no banco de dados um objeto da classe <code>TipoColaboradorVO</code>.
     * Primeiramente valida os dados (<code>validarDados</code>) do objeto. Verifica a conexão com o banco de dados e a permissão do usuário
     * para realizar esta operacão na entidade.
     * Isto, através da operação <code>incluir</code> da superclasse.
     * @param obj  Objeto da classe <code>TipoColaboradorVO</code> que será gravado no banco de dados.
     * @exception Exception Caso haja problemas de conexão, restrição de acesso ou validação de dados.
     */
    public void incluir(TipoColaboradorVO obj) throws Exception {
        incluir(obj,true);
    }
    public void incluirSemPermissao(TipoColaboradorVO obj) throws Exception {
        incluir(obj,false);
    }
    private void incluir(TipoColaboradorVO obj,boolean validarPermissao) throws Exception {
        TipoColaboradorVO.validarDados(obj);
        if(validarPermissao) {
            incluir(getIdEntidade());
        }
        String sql = "INSERT INTO TipoColaborador( descricao, colaborador) VALUES ( ?, ? )";
        try (PreparedStatement sqlInserir = con.prepareStatement(sql)) {
            sqlInserir.setString(1, obj.getDescricao());
            if (obj.getColaborador() != 0) {
                sqlInserir.setInt(2, obj.getColaborador());
            } else {
                sqlInserir.setNull(2, 0);
            }
            sqlInserir.execute();
        }
        obj.setCodigo(obterValorChavePrimariaCodigo());
        obj.setNovoObj(false);
    }
    /**
     * Operação responsável por alterar no BD os dados de um objeto da classe <code>TipoColaboradorVO</code>.
     * Sempre utiliza a chave primária da classe como atributo para localização do registro a ser alterado.
     * Primeiramente valida os dados (<code>validarDados</code>) do objeto. Verifica a conexão com o banco de dados e a permissão do usuário
     * para realizar esta operacão na entidade.
     * Isto, através da operação <code>alterar</code> da superclasse.
     * @param obj    Objeto da classe <code>TipoColaboradorVO</code> que será alterada no banco de dados.
     * @exception Exception Caso haja problemas de conexão, restrição de acesso ou validação de dados.
     */
    public void alterar(TipoColaboradorVO obj) throws Exception{
        alterar(obj,true);
    }
    public void alterarSemPermissao(TipoColaboradorVO obj) throws Exception{
        alterar(obj,false);
    }
    private void alterar(TipoColaboradorVO obj,boolean validarPermissao) throws Exception {
        TipoColaboradorVO.validarDados(obj);
        if(validarPermissao) {
            alterar(getIdEntidade());
        }
        String sql = "UPDATE TipoColaborador set descricao=?, colaborador=? WHERE ((codigo = ?))";
        try (PreparedStatement sqlAlterar = con.prepareStatement(sql)) {
            sqlAlterar.setString(1, obj.getDescricao());
            sqlAlterar.setInt(2, obj.getColaborador());
            sqlAlterar.setInt(3, obj.getCodigo());
            sqlAlterar.execute();
        }
    }

    /**
     * Operação responsável por excluir no BD um objeto da classe <code>TipoColaboradorVO</code>.
     * Sempre localiza o registro a ser excluído através da chave primária da entidade.
     * Primeiramente verifica a conexão com o banco de dados e a permissão do usuário
     * para realizar esta operacão na entidade.
     * Isto, através da operação <code>excluir</code> da superclasse.
     * @param obj    Objeto da classe <code>TipoColaboradorVO</code> que será removido no banco de dados.
     * @exception Exception Caso haja problemas de conexão ou restrição de acesso.
     */
    public void excluir(TipoColaboradorVO obj) throws Exception {
        excluir(getIdEntidade());
        String sql = "DELETE FROM TipoColaborador WHERE ((codigo = ?))";
        try (PreparedStatement sqlExcluir = con.prepareStatement(sql)) {
            sqlExcluir.setInt(1, obj.getCodigo());
            sqlExcluir.execute();
        }
    }

    /**
     * Operação responsável por excluir todos os objetos da <code>TipoColaboradorVO</code> no BD.
     * Faz uso da operação <code>excluir</code> disponível na classe <code>TipoColaborador</code>.
     * @param colaborador campo chave para exclusão dos objetos no BD.
     * @exception Exception  Erro de conexão com o BD ou restrição de acesso a esta operação.
     */
    public void excluirTipoColaborador(Integer colaborador) throws Exception {
        String sql = "DELETE FROM TipoColaborador WHERE (colaborador = ?)";
        try (PreparedStatement sqlExcluir = con.prepareStatement(sql)) {
            sqlExcluir.setInt(1, colaborador);
            sqlExcluir.execute();
        }
    }

    /**
     * Operação responsável por alterar todos os objetos da <code>TipoColaboradorVO</code> contidos em um Hashtable no BD.
     * Faz uso da operação <code>excluirTipoColaboradors</code> e <code>incluirTipoColaboradors</code> disponíveis na classe <code>TipoColaborador</code>.
     * @param objetos  List com os objetos a serem alterados ou incluídos no BD.
     * @exception Exception  Erro de conexão com o BD ou restrição de acesso a esta operação.
     */
    public void alterarTipoColaboradors(Integer colaborador, List objetos) throws Exception {
        String str = "DELETE FROM  TipoColaborador  WHERE colaborador = " + colaborador;
        Iterator i = objetos.iterator();
        while (i.hasNext()) {
            TipoColaboradorVO objeto = (TipoColaboradorVO) i.next();
            str += " AND codigo <> " + objeto.getCodigo();
        }
        try (PreparedStatement sqlExcluir = con.prepareStatement(str)) {
            sqlExcluir.execute();
        }
        Iterator e = objetos.iterator();
        List<String> tipoAdicionados = new ArrayList<>();
        while (e.hasNext()) {
            TipoColaboradorVO obj = (TipoColaboradorVO) e.next();
            if (obj.getCodigo().equals(new Integer(0))) {
                obj.setColaborador(colaborador);
                if(!tipoAdicionados.contains(obj.getDescricao())) {
                    incluirSemPermissao(obj);
                    tipoAdicionados.add(obj.getDescricao());
                }
            } else {
                if(!tipoAdicionados.contains(obj.getDescricao())) {
                    alterarSemPermissao(obj);
                    tipoAdicionados.add(obj.getDescricao());
                } else {
                    excluir(obj);
                }

            }
        }

    }

    /**
     * Operação responsável por incluir objetos da <code>TipoColaboradorVO</code> no BD.
     * Garantindo o relacionamento com a entidade principal <code>plano.Plano</code> através do atributo de vínculo.
     * @param objetos List contendo os objetos a serem gravados no BD da classe.
     * @exception Exception  Erro de conexão com o BD ou restrição de acesso a esta operação.
     */
    public void incluirTipoColaboradors(Integer colaboradorPrm, List objetos, boolean validarPermissao) throws Exception {
        Iterator e = objetos.iterator();
        List<String> tipoAdicionados = new ArrayList<>();
        while (e.hasNext()) {
            TipoColaboradorVO obj = (TipoColaboradorVO) e.next();
            obj.setColaborador(colaboradorPrm);
            if(!tipoAdicionados.contains(obj.getDescricao())) {
                incluir(obj, validarPermissao);
                tipoAdicionados.add(obj.getDescricao());
            }
        }

    }

    /**
     * Operação responsável por consultar todos os <code>TipoColaboradorVO</code> relacionados a um objeto da classe <code>plano.Plano</code>.
     * @param colaborador a ser utilizado para localizar os objetos da classe <code>TipoColaboradorVO</code>.
     * @return List  Contendo todos os objetos da classe <code>TipoColaboradorVO</code> resultantes da consulta.
     * @exception Exception  Erro de conexão com o BD ou restrição de acesso a esta operação.
     */
    public List consultarTipoColaborador(Integer colaborador, int nivelMontarDados) throws Exception {
        consultar(getIdEntidade());
        List<TipoColaboradorVO> objetos = new ArrayList<TipoColaboradorVO>();
        String sql = "SELECT * FROM TipoColaborador WHERE colaborador = ?";
        try (PreparedStatement sqlConsulta = con.prepareStatement(sql)) {
            sqlConsulta.setInt(1, colaborador);
            try (ResultSet resultado = sqlConsulta.executeQuery()) {
                while (resultado.next()) {
                    TipoColaboradorVO novoObj = TipoColaborador.montarDados(resultado, nivelMontarDados);
                    objetos.add(novoObj);
                }
            }
        }
        return objetos;
    }

    /**
     * Responsável por realizar uma consulta de <code>TipoColaborador</code> através do valor do atributo
     * <code>nome</code> da classe <code>Pais</code>
     * Faz uso da operação <code>montarDadosConsulta</code> que realiza o trabalho de prerarar o List resultante.
     * @return  List Contendo vários objetos da classe <code>TipoColaboradorVO</code> resultantes da consulta.
     * @exception Exception Caso haja problemas de conexão ou restrição de acesso.
     */
    public List<TipoColaboradorVO> consultarPorCodigoColaborador(Integer valorConsulta, boolean controlarAcesso, int nivelMontarDados) throws Exception {
        consultar(getIdEntidade(), true);
        String sqlStr = "SELECT TipoColaborador.* FROM TipoColaborador, Colaborador WHERE TipoColaborador.colaborador = Colaborador.codigo and Colaborador.codigo =" + valorConsulta.intValue() + " ORDER BY TipoColaborador.descricao";
        try (Statement stm = con.createStatement()) {
            try (ResultSet tabelaResultado = stm.executeQuery(sqlStr)) {
                return montarDadosConsulta(tabelaResultado, nivelMontarDados);
            }
        }
    }

    /**
     * Responsável por realizar uma consulta de <code>TipoColaborador</code> através do valor do atributo
     * <code>String nome</code>. Retorna os objetos, com início do valor do atributo idêntico ao parâmetro fornecido.
     * Faz uso da operação <code>montarDadosConsulta</code> que realiza o trabalho de prerarar o List resultante.
     * @param   controlarAcesso Indica se a aplicação deverá verificar se o usuário possui permissão para esta consulta ou não.
     * @return  List Contendo vários objetos da classe <code>TipoColaboradorVO</code> resultantes da consulta.
     * @exception Exception Caso haja problemas de conexão ou restrição de acesso.
     */
    public List consultarPorDescricao(String valorConsulta, boolean controlarAcesso, int nivelMontarDados) throws Exception {
        consultar(getIdEntidade(), controlarAcesso);
        String sqlStr = "SELECT * FROM TipoColaborador WHERE upper( descricao ) like('" + valorConsulta.toUpperCase() + "%') ORDER BY descricao";
        try (Statement stm = con.createStatement()) {
            try (ResultSet tabelaResultado = stm.executeQuery(sqlStr)) {
                return (montarDadosConsulta(tabelaResultado, nivelMontarDados));
            }
        }
    }

    /**
     * Responsável por realizar uma consulta de <code>TipoColaborador</code> através do valor do atributo
     * <code>Integer codigo</code>. Retorna os objetos com valores iguais ou superiores ao parâmetro fornecido.
     * Faz uso da operação <code>montarDadosConsulta</code> que realiza o trabalho de prerarar o List resultante.
     * @param   controlarAcesso Indica se a aplicação deverá verificar se o usuário possui permissão para esta consulta ou não.
     * @return  List Contendo vários objetos da classe <code>TipoColaboradorVO</code> resultantes da consulta.
     * @exception Exception Caso haja problemas de conexão ou restrição de acesso.
     */
    public List consultarPorCodigo(Integer valorConsulta, boolean controlarAcesso, int nivelMontarDados) throws Exception {
        consultar(getIdEntidade(), controlarAcesso);
        String sqlStr = "SELECT * FROM TipoColaborador WHERE codigo >= " + valorConsulta + " ORDER BY codigo";
        try (Statement stm = con.createStatement()) {
            try (ResultSet tabelaResultado = stm.executeQuery(sqlStr)) {
                return (montarDadosConsulta(tabelaResultado, nivelMontarDados));
            }
        }
    }

    /**
     * Responsável por montar os dados de vários objetos, resultantes de uma consulta ao banco de dados (<code>ResultSet</code>).
     * Faz uso da operação <code>montarDados</code> que realiza o trabalho para um objeto por vez.
     * @return  List Contendo vários objetos da classe <code>TipoColaboradorVO</code> resultantes da consulta.
     */
    public static List<TipoColaboradorVO> montarDadosConsulta(ResultSet tabelaResultado, int nivelMontarDados) throws Exception {
        List<TipoColaboradorVO> vetResultado = new ArrayList<TipoColaboradorVO>();
        while (tabelaResultado.next()) {
            TipoColaboradorVO obj = montarDados(tabelaResultado, nivelMontarDados);
            vetResultado.add(obj);
        }
        return vetResultado;
    }

    /**
     * Responsável por montar os dados resultantes de uma consulta ao banco de dados (<code>ResultSet</code>)
     * em um objeto da classe <code>TipoColaboradorVO</code>.
     * @return  O objeto da classe <code>TipoColaboradorVO</code> com os dados devidamente montados.
     */
    public static TipoColaboradorVO montarDados(ResultSet dadosSQL, int nivelMontarDados) throws Exception {
        TipoColaboradorVO obj = montarDadosBasicos(dadosSQL);
        if(nivelMontarDados == Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA)
            return obj;
        return obj;
    }

    public static TipoColaboradorVO montarDadosBasicos(ResultSet dadosSQL) throws Exception {
        TipoColaboradorVO obj = new TipoColaboradorVO();
        obj.setCodigo(dadosSQL.getInt("codigo"));
        obj.setDescricao(dadosSQL.getString("descricao"));
        obj.setColaborador(dadosSQL.getInt("colaborador"));
        obj.setNovoObj(false);
        return obj;
    }

    /**
     * Operação responsável por localizar um objeto da classe <code>TipoColaboradorVO</code>
     * através de sua chave primária.
     * @exception Exception Caso haja problemas de conexão ou localização do objeto procurado.
     */
    public TipoColaboradorVO consultarPorChavePrimaria(Integer codigoPrm, int nivelMontarDados) throws Exception {
        consultar(getIdEntidade(), false);
        String sql = "SELECT * FROM TipoColaborador WHERE codigo = ?";
        try (PreparedStatement sqlConsultar = con.prepareStatement(sql)) {
            sqlConsultar.setInt(1, codigoPrm);
            try (ResultSet tabelaResultado = sqlConsultar.executeQuery()) {
                if (!tabelaResultado.next()) {
                    throw new ConsistirException("Dados Não Encontrados ( TipoColaborador ).");
                }
                return (montarDados(tabelaResultado, nivelMontarDados));
            }
        }
    }

}

/*
 * To change this template, choose Too<PERSON> | Templates
 * and open the template in the editor.
 */
package negocio.facade.jdbc.basico;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.util.ArrayList;
import java.util.Iterator;
import java.util.List;
import negocio.comuns.basico.ColaboradorVO;
import negocio.comuns.basico.ModalidadeComissaoColaboradorVO;
import negocio.comuns.plano.ModalidadeVO;
import negocio.comuns.utilitarias.Uteis;
import negocio.facade.jdbc.arquitetura.SuperEntidade;
import negocio.facade.jdbc.plano.Modalidade;
import negocio.interfaces.basico.ModalidadeComissaoColaboradorInterfaceFacade;

/**
 * Classe de persistência dos dados de ModalidadeComissaoColaborador que está relacionado a dados de comissão por modalidade
 * de cada colaborador.
 * <AUTHOR>
 */
public class ModalidadeComissaoColaborador extends SuperEntidade implements ModalidadeComissaoColaboradorInterfaceFacade {

    public ModalidadeComissaoColaborador() throws Exception {
        super();
    }

    public ModalidadeComissaoColaborador(Connection conexao) throws Exception {
        super(conexao);
    }


    /**
     * Operação responsável por incluir objetos da <code>ModalidadeComissaoColaboradorVO</code> no BD.
     * Garantindo o relacionamento com a entidade principal <code>colaborador.Colaborador</code> através do atributo de vínculo.
     * @param objetos List contendo os objetos a serem gravados no BD da classe.
     * @exception Exception  Erro de conexão com o BD ou restrição de acesso a esta operação.
     */
    public void incluirModalidadesComissao(Integer colaboradorPrm, List<ModalidadeComissaoColaboradorVO> objetos) throws Exception {
        try {
            Iterator e = objetos.iterator();
            while (e.hasNext()) {
                ModalidadeComissaoColaboradorVO obj = (ModalidadeComissaoColaboradorVO) e.next();
                obj.getColaboradorVO().setCodigo(colaboradorPrm);
                incluir(obj);
            }
        } catch (Exception e) {
            throw e;
        }

    }

    /**
     * Operação responsável por incluir no banco de dados um objeto da classe <code>ModalidadeComissaoColaboradorVO</code>.
     * Primeiramente valida os dados (<code>validarDados</code>) do objeto. Verifica a conexão com o banco de dados e a permissão do usuário
     * para realizar esta operacão na entidade.
     * Isto, através da operação <code>incluir</code> da superclasse.
     * @param obj  Objeto da classe <code>ModalidadeComissaoColaboradorVO</code> que será gravado no banco de dados.
     * @exception Exception Caso haja problemas de conexão, restrição de acesso ou validação de dados.
     */
    public void incluir(ModalidadeComissaoColaboradorVO obj) throws Exception {
        try {
            ModalidadeComissaoColaboradorVO.validarDados(obj);
            String sql = "INSERT INTO modalidadecomissaocolaborador(modalidade,colaborador, porcComissao,valorComissao) VALUES (?, ?, ?, ?)";
            try (PreparedStatement sqlInserir = con.prepareStatement(sql)) {
                sqlInserir.setInt(1, obj.getModalidade().getCodigo());
                sqlInserir.setInt(2, obj.getColaboradorVO().getCodigo());
                sqlInserir.setDouble(3, obj.getPorcComissao());
                sqlInserir.setDouble(4, obj.getValorComissao());
                sqlInserir.execute();
            }
            obj.setNovoObj(new Boolean(false));
        } catch (Exception e) {
            throw e;
        }
    }

    /**
     * Operação responsável por alterar no BD os dados de um objeto da classe <code>TipoColaboradorVO</code>.
     * Sempre utiliza a chave primária da classe como atributo para localização do registro a ser alterado.
     * Primeiramente valida os dados (<code>validarDados</code>) do objeto. Verifica a conexão com o banco de dados e a permissão do usuário
     * para realizar esta operacão na entidade.
     * Isto, através da operação <code>alterar</code> da superclasse.
     * @param obj    Objeto da classe <code>TipoColaboradorVO</code> que será alterada no banco de dados.
     * @exception Execption Caso haja problemas de conexão, restrição de acesso ou validação de dados.
     */
    public void alterar(ModalidadeComissaoColaboradorVO obj) throws Exception {
        try {
            ModalidadeComissaoColaboradorVO.validarDados(obj);
            alterar(getIdEntidade());
            String sql = "UPDATE modalidadecomissaocolaborador set modalidade = ?,  colaborador=?, porcComissao=?, valorComissao=? WHERE ((modalidade= ? and colaborador=?))";
            try (PreparedStatement sqlAlterar = con.prepareStatement(sql)) {
                sqlAlterar.setInt(1, obj.getModalidade().getCodigo());
                sqlAlterar.setInt(2, obj.getColaboradorVO().getCodigo());
                sqlAlterar.setDouble(3, obj.getPorcComissao());
                sqlAlterar.setDouble(4, obj.getValorComissao());
                sqlAlterar.setInt(5, obj.getModalidade().getCodigo());
                sqlAlterar.setInt(6, obj.getColaboradorVO().getCodigo());
                sqlAlterar.execute();
            }
        } catch (Exception e) {
            throw e;
        }
    }

    /**
     * Operação responsável por excluir no BD um objeto da classe <code>ModalidadeComissaoColaboradorVO</code>.
     * Sempre localiza o registro a ser excluído através da chave primária da entidade.
     * Primeiramente verifica a conexão com o banco de dados e a permissão do usuário
     * para realizar esta operacão na entidade.
     * Isto, através da operação <code>excluir</code> da superclasse.
     * @param obj    Objeto da classe <code>ModalidadeComissaoColaboradorVO</code> que será removido no banco de dados.
     * @exception Execption Caso haja problemas de conexão ou restrição de acesso.
     */
    public void excluir(ModalidadeComissaoColaboradorVO obj) throws Exception {
        try {
            excluir(getIdEntidade());
            String sql = "DELETE FROM modalidadecomissaocolaborador WHERE ((modalidade = ? and colaborador = ?))";
            try (PreparedStatement sqlExcluir = con.prepareStatement(sql)) {
                sqlExcluir.setInt(1, obj.getModalidade().getCodigo().intValue());
                sqlExcluir.setInt(2, obj.getColaboradorVO().getCodigo().intValue());
                sqlExcluir.execute();
            }
        } catch (Exception e) {
            throw e;
        }
    }

    /**
     * Operação responsável por excluir todos os objetos da <code>TipoColaboradorVO</code> no BD.
     * Faz uso da operação <code>excluir</code> disponível na classe <code>TipoColaborador</code>.
     * @param <code>plano</code> campo chave para exclusão dos objetos no BD.
     * @exception Exception  Erro de conexão com o BD ou restrição de acesso a esta operação.
     */
    public void excluirModalidadeComissao(Integer colaborador) throws Exception {
        String sql = "DELETE FROM modalidadecomissaocolaborador WHERE (colaborador = ?)";
        try (PreparedStatement sqlExcluir = con.prepareStatement(sql)) {
            sqlExcluir.setInt(1, colaborador.intValue());
            sqlExcluir.execute();
        }
    }

    /**
     * Operação responsável por alterar todos os objetos da <code>ModalidadeComissaoColaboradorVO</code> contidos em um Hashtable no BD.
     * Faz uso da operação <code>excluirModalidadeComissao</code> e <code>incluirModalidadeComissao</code> disponíveis na classe <code>ModalidadeComissaoColaborador</code>.
     * @param objetos  List com os objetos a serem alterados ou incluídos no BD.
     * @exception Exception  Erro de conexão com o BD ou restrição de acesso a esta operação.
     */
    public void alterarModalidadeComissao(Integer colaborador, List objetos) throws Exception {
        String str = "DELETE FROM  modalidadecomissaocolaborador  WHERE colaborador = " + colaborador.intValue();
        Iterator i = objetos.iterator();
        /*while (i.hasNext()) {
            ModalidadeComissaoColaboradorVO objeto = (ModalidadeComissaoColaboradorVO) i.next();
            str += " AND modalidade <> " + objeto.getModalidade().getCodigo().intValue();
        }*/
        try (PreparedStatement sqlExcluir = con.prepareStatement(str)) {
            sqlExcluir.execute();
        }
        Iterator e = objetos.iterator();
        while (e.hasNext()) {
            ModalidadeComissaoColaboradorVO obj = (ModalidadeComissaoColaboradorVO) e.next();
            //if (obj.getCodigo().equals(new Integer(0))) {
                obj.getColaboradorVO().setCodigo(colaborador);
                incluir(obj);
            //} else {
              //  alterar(obj);
            //}
        }

    }

    /**
     * Operação responsável por consultar todos os <code>TipoColaboradorVO</code> relacionados a um objeto da classe <code>plano.Plano</code>.
     * @param plano  Atributo de <code>plano.Plano</code> a ser utilizado para localizar os objetos da classe <code>TipoColaboradorVO</code>.
     * @return List  Contendo todos os objetos da classe <code>TipoColaboradorVO</code> resultantes da consulta.
     * @exception Exception  Erro de conexão com o BD ou restrição de acesso a esta operação.
     */
    public List consultarModalidadeComissao(Integer colaborador, int nivelMontarDados) throws Exception {
        consultar(getIdEntidade());
        List objetos = new ArrayList();
        String sql = "SELECT * FROM modalidadecomissaocolaborador WHERE colaborador = ?";
        try (PreparedStatement sqlConsulta = con.prepareStatement(sql)) {
            sqlConsulta.setInt(1, colaborador.intValue());
            try (ResultSet resultado = sqlConsulta.executeQuery()) {
                while (resultado.next()) {
                    ModalidadeComissaoColaboradorVO novoObj = new ModalidadeComissaoColaboradorVO();
                    novoObj = montarDados(resultado, nivelMontarDados, this.con);
                    objetos.add(novoObj);
                }
            }
        }
        return objetos;
    }

     /**
     * Responsável por montar os dados de vários objetos, resultantes de uma consulta ao banco de dados (<code>ResultSet</code>).
     * Faz uso da operação <code>montarDados</code> que realiza o trabalho para um objeto por vez.
     * @return  List Contendo vários objetos da classe <code>TipoColaboradorVO</code> resultantes da consulta.
     */
    public static List montarDadosConsulta(ResultSet tabelaResultado, int nivelMontarDados, Connection con) throws Exception {
        List vetResultado = new ArrayList();
        while (tabelaResultado.next()) {
            ModalidadeComissaoColaboradorVO obj = new ModalidadeComissaoColaboradorVO();
            obj = montarDados(tabelaResultado, nivelMontarDados, con);
            vetResultado.add(obj);
        }
        return vetResultado;
    }

    /**
     * Responsável por montar os dados resultantes de uma consulta ao banco de dados (<code>ResultSet</code>)
     * em um objeto da classe <code>ModalidadeComissaoColaboradorVO</code>.
     * @return  O objeto da classe <code>ModalidadeComissaoColaboradorVO</code> com os dados devidamente montados.
     */
    public static ModalidadeComissaoColaboradorVO montarDados(ResultSet dadosSQL, int nivelMontarDados, Connection con) throws Exception {
        ModalidadeComissaoColaboradorVO obj = montarDadosBasicos(dadosSQL);
        if(nivelMontarDados == Uteis.NIVELMONTARDADOS_DADOSBASICOS){
            montarDadosModalidade(obj, Uteis.NIVELMONTARDADOS_MINIMOS, con);
            return obj;
        }
        return obj;
    }

    public static ModalidadeComissaoColaboradorVO montarDadosBasicos(ResultSet dadosSQL) throws Exception {
        ModalidadeComissaoColaboradorVO obj = new ModalidadeComissaoColaboradorVO();
        obj.setModalidade(new ModalidadeVO());
        obj.getModalidade().setCodigo(dadosSQL.getInt("modalidade"));
        obj.setPorcComissao(Uteis.arredondarForcando2CasasDecimais(dadosSQL.getDouble("porcComissao")));
        obj.setValorComissao(dadosSQL.getDouble("valorComissao"));
        obj.setColaboradorVO(new ColaboradorVO());
        obj.getColaboradorVO().setCodigo(dadosSQL.getInt("colaborador"));
        obj.setNovoObj(false);
        return obj;
    }

    /**
     * Operação responsável por montar os dados de um objeto da classe <code>PessoaVO</code> relacionado ao objeto <code>ColaboradorVO</code>.
     * Faz uso da chave primária da classe <code>PessoaVO</code> para realizar a consulta.
     * @param obj  Objeto no qual será montado os dados consultados.
     */
    public static void montarDadosModalidade(ModalidadeComissaoColaboradorVO obj, int nivelMontarDados, Connection con) throws Exception {
        if (obj.getModalidade().getCodigo().intValue() == 0) {
            obj.setModalidade(new ModalidadeVO());
            return;
        }
        Modalidade modalidade = new Modalidade(con);
        obj.setModalidade(modalidade.consultarPorChavePrimaria(obj.getModalidade().getCodigo(), nivelMontarDados));
        modalidade = null;
    }
}

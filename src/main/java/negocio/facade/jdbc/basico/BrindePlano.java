package negocio.facade.jdbc.basico;

import negocio.comuns.basico.BrindeTipoEnum;
import negocio.comuns.basico.BrindeVO;
import negocio.comuns.plano.PlanoVO;
import negocio.facade.jdbc.arquitetura.SuperEntidade;
import negocio.interfaces.basico.BrindePlanoInterfaceFacade;

import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.List;

public class BrindePlano extends SuperEntidade implements BrindePlanoInterfaceFacade {

    public BrindePlano() throws Exception {}

    @Override
    public void incluir(BrindeVO brinde) throws SQLException {
        String values = "";
        for (Integer codigoPlano: brinde.getCodigoPlanos()) {
            values += "("+brinde.getCodigo()+", "+codigoPlano+"), ";
        }
        if(values.length() > 0){
            values = values.substring(0, values.length() - 2);
            String sqlInsert = "INSERT INTO brindeplano (brinde, plano) VALUES "+values;
            PreparedStatement ps = con.prepareStatement(sqlInsert);
            ps.execute();
        }
    }

    @Override
    public void atualizar(BrindeVO brinde) throws SQLException {
        if(!brinde.isAplicarPontuacaoParaTodosOsPlanos()){
            String sql = "DELETE FROM brindeplano " +
                    "WHERE brinde = "+brinde.getCodigo();
            PreparedStatement ps = con.prepareStatement(sql);
            ps.execute();
            incluir(brinde);
        }
    }

    @Override
    public List<Integer> consultarCodigosPlanos(Integer codigoBrinde) throws SQLException {
        List<Integer> codigos = new ArrayList<Integer>();
        String sql = "SELECT plano FROM brindeplano " +
                "WHERE brinde = "+codigoBrinde;
        PreparedStatement ps = con.prepareStatement(sql);
        ResultSet rs = ps.executeQuery();
        while (rs.next()){
            codigos.add(rs.getInt("plano"));
        }

        return codigos;
    }
}

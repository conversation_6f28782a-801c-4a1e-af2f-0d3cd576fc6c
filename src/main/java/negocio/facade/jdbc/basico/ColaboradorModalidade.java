package negocio.facade.jdbc.basico;

import negocio.comuns.basico.ColaboradorModalidadeVO;
import negocio.comuns.basico.ColaboradorVO;
import negocio.comuns.utilitarias.Uteis;
import negocio.facade.jdbc.arquitetura.SuperEntidade;
import negocio.facade.jdbc.plano.Modalidade;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.util.ArrayList;
import java.util.Iterator;
import java.util.List;

public class ColaboradorModalidade extends SuperEntidade {

    public ColaboradorModalidade() throws Exception {
        super();
        setIdEntidade("Colaborador");
    }

    public ColaboradorModalidade(Connection con) throws Exception {
        super(con);
        setIdEntidade("Colaborador");
    }

    public ColaboradorModalidadeVO novo() throws Exception {
        incluir(getIdEntidade());
        ColaboradorModalidadeVO obj = new ColaboradorModalidadeVO();
        return obj;
    }

    public void incluir(ColaboradorModalidadeVO obj) throws Exception {
        ColaboradorModalidadeVO.validarDados(obj);
        String sql = "INSERT INTO colaboradormodalidade ( colaborador, modalidade ) VALUES ( ?, ? )";
        PreparedStatement sqlInserir = con.prepareStatement(sql);
        if (obj.getColaboradorVO().getCodigo() != 0) {
            sqlInserir.setInt(1, obj.getColaboradorVO().getCodigo());
        } else {
            sqlInserir.setNull(1, 0);
        }
        if (obj.getModalidadeVO().getCodigo() != 0) {
            sqlInserir.setInt(2, obj.getModalidadeVO().getCodigo());
        } else {
            sqlInserir.setNull(2, 0);
        }
        sqlInserir.execute();
        obj.setCodigo(obterValorChavePrimariaCodigo());
        obj.setNovoObj(false);
    }

    public void alterar(ColaboradorModalidadeVO obj) throws Exception {
        ColaboradorModalidadeVO.validarDados(obj);
        alterar(getIdEntidade());
        String sql = "UPDATE colaboradormodalidade set colaborador = ?, modalidade = ? WHERE codigo = ?";
        PreparedStatement sqlAlterar = con.prepareStatement(sql);
        if (obj.getColaboradorVO().getCodigo() != 0) {
            sqlAlterar.setInt(1, obj.getColaboradorVO().getCodigo());
        } else {
            sqlAlterar.setNull(1, 0);
        }
        if (obj.getModalidadeVO().getCodigo() != 0) {
            sqlAlterar.setInt(2, obj.getModalidadeVO().getCodigo());
        } else {
            sqlAlterar.setNull(2, 0);
        }
        sqlAlterar.setInt(3, obj.getCodigo());
        sqlAlterar.execute();
    }

    public void alterarPorColaborador(Integer colaborador, List objetos) throws Exception {
        String str = "DELETE FROM colaboradormodalidade WHERE colaborador = " + colaborador;
        Iterator i = objetos.iterator();
        while (i.hasNext()) {
            ColaboradorModalidadeVO objeto = (ColaboradorModalidadeVO) i.next();
            str += " AND codigo <> " + objeto.getCodigo();
        }
        PreparedStatement sqlExcluir = con.prepareStatement(str);
        sqlExcluir.execute();
        Iterator e = objetos.iterator();
        while (e.hasNext()) {
            ColaboradorModalidadeVO obj = (ColaboradorModalidadeVO) e.next();
            if (obj.getCodigo().equals(0)) {
                obj.getColaboradorVO().setCodigo(colaborador);
                incluir(obj);
            } else {
                alterar(obj);
            }
        }
    }

    public List<ColaboradorModalidadeVO> consultarPorColaborador(Integer colaborador, int nivelMontarDados) throws Exception {
        consultar(getIdEntidade());
        List<ColaboradorModalidadeVO> objetos = new ArrayList();
        String sql = "SELECT * FROM colaboradormodalidade WHERE colaborador = ?";
        PreparedStatement sqlConsulta = con.prepareStatement(sql);
        sqlConsulta.setInt(1, colaborador.intValue());
        ResultSet resultado = sqlConsulta.executeQuery();
        while (resultado.next()) {
            ColaboradorModalidadeVO novoObj = new ColaboradorModalidadeVO();
            novoObj = ColaboradorModalidade.montarDados(resultado, nivelMontarDados, this.con);
            objetos.add(novoObj);
        }
        return objetos;
    }

    public static ColaboradorModalidadeVO montarDados(ResultSet dadosSQL, int nivelMontarDados, Connection con) throws Exception {
        ColaboradorModalidadeVO obj = new ColaboradorModalidadeVO();
        Colaborador colaboradorDao = new Colaborador(con);
        Modalidade modalidadeDao = new Modalidade(con);
        obj.setCodigo(dadosSQL.getInt("codigo"));
        obj.setColaboradorVO(colaboradorDao.consultarPorChavePrimaria(dadosSQL.getInt("colaborador"), Uteis.NIVELMONTARDADOS_MINIMOS));
        obj.setModalidadeVO(modalidadeDao.consultarPorChavePrimaria(dadosSQL.getInt("modalidade") , Uteis.NIVELMONTARDADOS_MINIMOS));
        obj.setNovoObj(Boolean.FALSE);

        return obj;
    }

    public void excluirPorColaborador(Integer colaborador) throws Exception {
        excluir(getIdEntidade());
        String sql = "DELETE FROM colaboradormodalidade WHERE colaborador = ?";
        PreparedStatement sqlExcluir = con.prepareStatement(sql);
        sqlExcluir.setInt(1, colaborador);
        sqlExcluir.execute();
    }

    public void incluirColaboradorModalidades(ColaboradorVO colaboradorVO, List objetos) throws Exception {
        Iterator e = objetos.iterator();
        while (e.hasNext()) {
            ColaboradorModalidadeVO obj = (ColaboradorModalidadeVO) e.next();
            obj.setColaboradorVO(colaboradorVO);
            incluir(obj);
        }
    }


}

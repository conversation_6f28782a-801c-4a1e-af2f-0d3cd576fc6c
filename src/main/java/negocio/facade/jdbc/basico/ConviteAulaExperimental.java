/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package negocio.facade.jdbc.basico;


import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import negocio.comuns.basico.ClienteVO;
import negocio.comuns.basico.PessoaVO;
import negocio.comuns.basico.ReposicaoVO;
import negocio.comuns.conviteaulaexperimental.ConviteAulaExperimentalBITO;
import negocio.comuns.conviteaulaexperimental.ConviteAulaExperimentalVO;
import negocio.comuns.conviteaulaexperimental.TipoConviteAulaExperimentalVO;
import negocio.comuns.plano.HorarioTurmaVO;
import negocio.comuns.utilitarias.Calendario;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisValidacao;
import negocio.facade.jdbc.arquitetura.SuperEntidade;
import static negocio.facade.jdbc.arquitetura.SuperFacadeJDBC.criarConsulta;
import negocio.interfaces.basico.ConviteAulaExperimentalInterfaceFacade;

/**
 *
 * <AUTHOR>
 */
public class ConviteAulaExperimental extends SuperEntidade implements ConviteAulaExperimentalInterfaceFacade{

    public ConviteAulaExperimental() throws Exception {
        super();
    }

    public ConviteAulaExperimental(Connection con) throws Exception {
        super(con);
    }
    
    @Override
    public ConviteAulaExperimentalBITO gerarBI(Date dataBase, Integer empresa) throws Exception{
        ConviteAulaExperimentalBITO bi = new ConviteAulaExperimentalBITO();
        bi.setAlunosSemConsultor(contar("SELECT count(cliente.codigo) FROM cliente \n" +
                                 "LEFT JOIN vinculo ON vinculo.cliente = cliente.codigo AND vinculo.tipovinculo = 'CO'\n" +
                                 "WHERE vinculo.codigo IS NULL", con));
        ResultSet rs = consultarConvitesBI(dataBase == null ? null : Uteis.obterPrimeiroDiaMes(dataBase), dataBase, 
                empresa, null, null, null, true, null, null, null, null);
        while(rs.next()){
            montarDadosBI(rs, bi);
        }
        return bi;
    }
    
    @Override
    public List<ConviteAulaExperimentalBITO> obterListaBI(Date dataBase,Integer empresa) throws Exception{
        Map<Date,ConviteAulaExperimentalBITO> mapabi = new HashMap<Date, ConviteAulaExperimentalBITO>();
        ResultSet rs = consultarConvitesBI(dataBase == null ? null : Uteis.obterPrimeiroDiaMes(dataBase), dataBase, 
                empresa, null, null, null, true, null, null, null, null);
        while(rs.next()){
            Date dia = Calendario.getDataComHoraZerada(rs.getDate("datalancamento"));
            ConviteAulaExperimentalBITO caeDia = mapabi.get(dia);
            if(caeDia == null){
                caeDia = new ConviteAulaExperimentalBITO();
                caeDia.setDia(Uteis.getDataAplicandoFormatacao(dia, "yyyy-MM-dd"));
                mapabi.put(dia, caeDia);
            }
            montarDadosBI(rs, caeDia);
        }
        
        return new ArrayList<ConviteAulaExperimentalBITO>(mapabi.values());
    }

    @Override
    public List<ConviteAulaExperimentalBITO> consultarRelatorio(Date inicio, Date fim, Integer empresa, 
            String clienteConvidou, String usuarioConvidou, String convidado, 
            Boolean todos, Boolean validou, Boolean agendou, Boolean compareceu, Integer tipo) throws Exception{
        
        ResultSet rs = consultarConvitesBI(inicio, fim, empresa, clienteConvidou, usuarioConvidou, convidado, 
                        todos, validou, agendou, compareceu, tipo);
        List<ConviteAulaExperimentalBITO> lista = new ArrayList<ConviteAulaExperimentalBITO>();
        while(rs.next()){
            ConviteAulaExperimentalBITO caeDia = new ConviteAulaExperimentalBITO();
            caeDia.setDataLancamento(rs.getDate("datalancamento"));
            caeDia.setDataPresenca(rs.getDate("datapresenca"));
            caeDia.setDataValidacao(rs.getDate("datavalidacaoconvidado"));
            String clienteConv = rs.getString("nomecliente");
            String indicadoConv = rs.getString("nomeindicado");
            String passivoConv = rs.getString("passivo");
            caeDia.setConvite(rs.getString("tipo"));
            caeDia.setConvidado(UteisValidacao.emptyString(clienteConv) ?
                    (UteisValidacao.emptyString(indicadoConv) ? passivoConv : 
                    indicadoConv) : clienteConv);
            caeDia.setUsuarioConvidou(rs.getString("usuarioconvidou"));
            caeDia.setClienteConvidou(rs.getString("clienteconvidou"));
            caeDia.setAgendou(!UteisValidacao.emptyNumber(rs.getInt("reposicao")));
            caeDia.setCompareceu(caeDia.getDataPresenca() != null);
            lista.add(caeDia);
        }
        
        return lista;
    }

    public ResultSet consultarConvitesBI(Date inicio, Date fim, Integer empresa, 
            String clienteConvidou, String usuarioConvidou, String convidado, 
            Boolean todos, Boolean validou, Boolean agendou, Boolean compareceu, Integer tipo) throws Exception{
        StringBuilder sql = new StringBuilder();
        sql.append(" SELECT r.codigo as reposicao, r.datapresenca, datavalidacaoconvidado, cae.datalancamento, \n");
        sql.append(" i.nomeindicado, sw.nomecliente, u.nome as usuarioconvidou, tcae.descricao as tipo,\n");
        sql.append(" swconv.nomecliente as clienteconvidou, p.nome as passivo FROM conviteaulaexperimental cae \n");
        sql.append(" INNER JOIN tipoconviteaulaexperimental tcae ON tcae.codigo = cae.tipoconviteaulaexperimental \n");
        sql.append(" LEFT JOIN reposicao r ON r.conviteaulaexperimental = cae.codigo \n");
        sql.append(" LEFT JOIN indicado i ON i.codigo = cae.indicadoconvidado \n");
        sql.append(" LEFT JOIN passivo p ON p.codigo = cae.passivoconvidado \n");
        sql.append(" LEFT JOIN situacaoclientesinteticodw sw ON sw.codigocliente = cae.clienteconvidado \n");
        sql.append(" LEFT JOIN situacaoclientesinteticodw swconv ON swconv.codigocliente = cae.clienteconvidou \n");
        sql.append(" LEFT JOIN usuario u ON u.codigo = cae.usuarioconvidou \n");
        
        if(inicio != null && fim != null){
            sql.append(" WHERE cae.datalancamento BETWEEN ? AND ?  \n");
        }
        if(!UteisValidacao.emptyNumber(empresa)){
            sql.append(inicio == null || fim == null  ? " WHERE " : " AND ").append(" tcae.empresa = ? ");
        }
        if(!UteisValidacao.emptyNumber(tipo)){
            sql.append(" AND tcae.codigo = ? ");
        }
        if(!todos){
            sql.append(" AND datavalidacaoconvidado IS ").append(validou ? " NOT " : "  ").append("NULL \n");
            sql.append(" AND r.codigo IS ").append(agendou ? " NOT " : "  ").append("NULL \n");
            sql.append(" AND r.datapresenca IS ").append(compareceu ? " NOT " : "  ").append("NULL \n");
        }
        if(!UteisValidacao.emptyString(clienteConvidou) || !UteisValidacao.emptyString(usuarioConvidou)){
            sql.append(" AND (");
            String cliConvidou = UteisValidacao.emptyString(clienteConvidou) ?
                    "" :
                    (" swconv.nomeconsulta like remove_acento_upper('"+clienteConvidou+"%')"+
                    (UteisValidacao.emptyString(usuarioConvidou) ? "" : " OR "));
            sql.append(cliConvidou);
            
            String usConvidou = UteisValidacao.emptyString(usuarioConvidou) ?
                    "" :
                    (" upper(u.nome) LIKE upper('"+usuarioConvidou+"%')");
            sql.append(usConvidou);
            sql.append(")\n");
        }
        if(!UteisValidacao.emptyString(convidado)){
             sql.append(" AND (sw.nomeconsulta like remove_acento_upper('").append(convidado).append("') OR upper(i.nomeindicado) LIKE upper('");
             sql.append(convidado).append("%') OR upper(p.nome) LIKE upper('").append(convidado).append("%'))");
        }
        int i = 1;
        PreparedStatement pst = con.prepareStatement(sql.toString());
        if(inicio != null && fim != null){
            pst.setDate(i++, Uteis.getDataJDBC(inicio));
            pst.setTimestamp(i++, Uteis.getDataHoraJDBC(fim, "23:59:59"));
        }
        if(!UteisValidacao.emptyNumber(tipo)){
            pst.setInt(i++, tipo);
        }
        if(!UteisValidacao.emptyNumber(empresa)){
            pst.setInt(i++, empresa);
        }
        return pst.executeQuery();
    }
    
    public void montarDadosBI(ResultSet rs, ConviteAulaExperimentalBITO bi) throws Exception {
        bi.setConvitesEnviados(bi.getConvitesEnviados() + 1);
        if (rs.getDate("datavalidacaoconvidado") != null) {
            bi.setConvitesValidados(bi.getConvitesValidados() + 1);
        }
        if (!UteisValidacao.emptyNumber(rs.getInt("reposicao"))) {
            bi.setAgendaramAula(bi.getAgendaramAula() + 1);
        }
        if (rs.getDate("datapresenca") != null) {
            bi.setAgendaramCompareceram(bi.getAgendaramCompareceram() + 1);
        }
    }
    
    @Override
    public List<ConviteAulaExperimentalVO> consultarConvitesRecebidos(Integer cliente) throws Exception {
        StringBuilder sql = new StringBuilder();
        sql.append(" SELECT cae.codigo as codigoconvite, cae.datalancamento, tcae.descricao, cliconvidou.matricula, cliconvidou.codigomatricula, cliconvidou.codigo as clienteconvidou, pesconvidou.nome as nomeconvidou, \n");
        sql.append(" tcae.quantidadeaulaexperimental, tcae.vigenciafinal,  \n");
        sql.append(" ARRAY_TO_STRING(ARRAY(SELECT nome FROM modalidade m INNER JOIN tipoconviteaulaexperimentalmodalidade tcaem ON tcaem.modalidade = m.codigo  \n");
        sql.append(" AND tcaem.tipoconviteaulaexperimental = tcae.codigo), '<br/>') as modalidades \n");
        sql.append(" FROM conviteaulaexperimental cae \n");
        sql.append(" INNER JOIN tipoconviteaulaexperimental tcae ON tcae.codigo = cae.tipoconviteaulaexperimental \n");
        sql.append(" INNER JOIN cliente cliconvidou ON cliconvidou.codigo = cae.clienteconvidou \n");
        sql.append(" INNER JOIN pessoa pesconvidou ON pesconvidou.codigo = cliconvidou.pessoa \n");
        sql.append(" WHERE clienteindicadooupassivo = ").append(cliente);
        ResultSet rs = criarConsulta(sql.toString(), con);
        List<ConviteAulaExperimentalVO> lista = new ArrayList<ConviteAulaExperimentalVO>();
        while(rs.next()){
            ConviteAulaExperimentalVO convite = new ConviteAulaExperimentalVO();
            convite.setDataLancamento(rs.getDate("datalancamento"));
            convite.setTipoConviteAulaExperimentalVO(new TipoConviteAulaExperimentalVO());
            convite.getTipoConviteAulaExperimentalVO().setDescricao(rs.getString("descricao"));
            convite.setClienteConvidou(new ClienteVO());
            convite.getClienteConvidou().setCodigo(rs.getInt("clienteconvidou"));
            convite.getClienteConvidou().setCodigoMatricula(rs.getInt("codigomatricula"));
            convite.getClienteConvidou().setMatricula(rs.getString("matricula"));
            convite.getClienteConvidou().setPessoa(new PessoaVO());
            convite.getClienteConvidou().getPessoa().setNome(rs.getString("nomeconvidou"));
            convite.getTipoConviteAulaExperimentalVO().setQuantidadeAulaExperimental(rs.getInt("quantidadeaulaexperimental"));
            convite.getTipoConviteAulaExperimentalVO().setVigenciaFinal(rs.getDate("vigenciafinal"));
            convite.setModalidades(rs.getString("modalidades"));
            StringBuilder sqlAulas = new StringBuilder();
            sqlAulas.append(" SELECT ht.identificadorturma, horafinal, horainicial, r.datareposicao FROM reposicao r \n");
            sqlAulas.append(" INNER JOIN horarioturma ht ON ht.codigo = r.horarioturma \n");
            sqlAulas.append(" WHERE r.conviteaulaexperimental = ").append(rs.getInt("codigoconvite"));
            sqlAulas.append(" ORDER BY datareposicao DESC");
            convite.setReposicoes(new ArrayList<ReposicaoVO>());
            ResultSet rsAulas = criarConsulta(sqlAulas.toString(), con);
            while(rsAulas.next()){
                ReposicaoVO reposicao = new ReposicaoVO();
                reposicao.setHorarioTurma(new HorarioTurmaVO());
                reposicao.getHorarioTurma().setIdentificadorTurma(rsAulas.getString("identificadorturma"));
                reposicao.getHorarioTurma().setHoraInicial(rsAulas.getString("horainicial"));
                reposicao.getHorarioTurma().setHoraFinal(rsAulas.getString("horafinal"));
                reposicao.setDataReposicao(rsAulas.getDate("datareposicao"));
                convite.getReposicoes().add(reposicao);
            }
            lista.add(convite);
        }
        return lista;
    }
    
    
}

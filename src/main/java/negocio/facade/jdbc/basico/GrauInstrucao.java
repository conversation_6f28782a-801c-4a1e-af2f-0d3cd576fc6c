package negocio.facade.jdbc.basico;

import negocio.comuns.basico.GrauInstrucaoVO;
import negocio.comuns.basico.ProfissaoVO;
import negocio.comuns.utilitarias.ConsistirException;
import negocio.comuns.utilitarias.Ordenacao;
import negocio.comuns.utilitarias.Uteis;
import negocio.facade.jdbc.arquitetura.SuperEntidade;
import negocio.interfaces.basico.GrauInstrucaoInterfaceFacade;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.sql.Statement;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

public class GrauInstrucao extends SuperEntidade implements GrauInstrucaoInterfaceFacade { 

    public GrauInstrucao() throws Exception {
        super();        
    }

    public GrauInstrucao(Connection con) throws Exception {
		super(con);
	}

	public GrauInstrucaoVO novo() throws Exception {
        incluir(getIdEntidade());
        GrauInstrucaoVO obj = new GrauInstrucaoVO();
        return obj;
    }

    public void incluir(GrauInstrucaoVO obj) throws Exception {
    	this.incluir(obj, false);
    }
    
    public void incluir(GrauInstrucaoVO obj, boolean centralEventos) throws Exception {
        try {
            GrauInstrucaoVO.validarDados(obj);
            if(centralEventos){
//            	incluirObj(getIdEntidade());
            }else{
            	incluir(getIdEntidade());
            }
            obj.realizarUpperCaseDados();
            String sql = "INSERT INTO Grauinstrucao( descricao ) VALUES ( ? )";
            PreparedStatement sqlInserir = con.prepareStatement(sql);
            sqlInserir.setString(1, obj.getDescricao());
            sqlInserir.execute();
            obj.setCodigo(obterValorChavePrimariaCodigo());
            obj.setNovoObj(new Boolean(false));
        } catch (Exception e) {
            throw e;
        }
    }
    
    public void alterar(GrauInstrucaoVO obj) throws Exception {
    	this.alterar(obj,false);
    }

    public void alterar(GrauInstrucaoVO obj, boolean centralEventos) throws Exception {
            GrauInstrucaoVO.validarDados(obj);
            if(centralEventos){
//            	alterarObj(getIdEntidade());
            }else
            	alterar(getIdEntidade());
            obj.realizarUpperCaseDados();
            String sql = "UPDATE Grauinstrucao set descricao=? WHERE ((codigo = ?))";
            PreparedStatement sqlAlterar = con.prepareStatement(sql);
            sqlAlterar.setString(1, obj.getDescricao());
            sqlAlterar.setInt(2, obj.getCodigo().intValue());
            sqlAlterar.execute();
        }
    
    public void excluir(GrauInstrucaoVO obj) throws Exception {
    	this.excluir(obj, false);
    }

    public void excluir(GrauInstrucaoVO obj, boolean centralEventos) throws Exception {
        try {
            con.setAutoCommit(false);
            if(centralEventos){
            	excluirObj(getIdEntidade());
            }else
            	excluir(getIdEntidade());
            String sql = "DELETE FROM Grauinstrucao WHERE ((codigo = ?))";
            PreparedStatement sqlExcluir = con.prepareStatement(sql);
            sqlExcluir.setInt(1, obj.getCodigo().intValue());
            sqlExcluir.execute();
            con.commit();
        } catch (Exception e) {
            con.rollback();
            con.setAutoCommit(true);
            throw e;
        } finally {
            con.setAutoCommit(true);
        }
    }

    public List consultarPorDescricao(String valorConsulta, boolean controlarAcesso, int nivelMontarDados) throws Exception {
        consultar(getIdEntidade(), controlarAcesso);
        String sqlStr = "SELECT * FROM Grauinstrucao WHERE upper( descricao ) like('" + valorConsulta.toUpperCase() + "%') ORDER BY descricao";
        Statement stm = con.createStatement();
        ResultSet tabelaResultado = stm.executeQuery(sqlStr);
        return (montarDadosConsulta(tabelaResultado, nivelMontarDados));
    }

    public List consultarPorCodigo(Integer valorConsulta, boolean controlarAcesso, int nivelMontarDados) throws Exception {
        consultar(getIdEntidade(), controlarAcesso);
        String sqlStr = "SELECT * FROM Grauinstrucao WHERE codigo >= " + valorConsulta.intValue() + " ORDER BY codigo";
        Statement stm = con.createStatement();
        ResultSet tabelaResultado = stm.executeQuery(sqlStr);
        return (montarDadosConsulta(tabelaResultado, nivelMontarDados));
    }
    
    public GrauInstrucaoVO consultarPorCodigoExato(Integer valorConsulta, boolean controlarAcesso, int nivelMontarDados) throws Exception {
    	consultar(getIdEntidade(), controlarAcesso);
        String sqlStr = "SELECT * FROM Grauinstrucao WHERE codigo = " + valorConsulta.intValue() + " ORDER BY codigo";
        Statement stm = con.createStatement();
        ResultSet tabelaResultado = stm.executeQuery(sqlStr);
        
        GrauInstrucaoVO grauInstrucao = null;
        if (tabelaResultado.next()) {
        	grauInstrucao = montarDados(tabelaResultado, nivelMontarDados);
        }
        return grauInstrucao;
    }

    public GrauInstrucaoVO consultarPorChavePrimaria(Integer codigoPrm, int nivelMontarDados) throws Exception {
        consultar(getIdEntidade(), false);
        GrauInstrucaoVO eCache = (GrauInstrucaoVO) obterFromCache(codigoPrm);
        if (eCache != null){
            return eCache;
        }
        String sql = "SELECT * FROM Grauinstrucao WHERE codigo = ?";
        PreparedStatement sqlConsultar = con.prepareStatement(sql);
        sqlConsultar.setInt(1, codigoPrm.intValue());
        ResultSet tabelaResultado = sqlConsultar.executeQuery();
        if (!tabelaResultado.next()) {
            throw new ConsistirException("Dados Não Encontrados ( Grauinstrucao ).");
        }
        eCache = (montarDados(tabelaResultado, nivelMontarDados));
        putToCache(eCache);
        return eCache;
    }

    public static List montarDadosConsulta(ResultSet tabelaResultado, int nivelMontarDados) throws Exception {
        List vetResultado = new ArrayList();
        while (tabelaResultado.next()) {
            GrauInstrucaoVO obj = new GrauInstrucaoVO();
            obj = montarDados(tabelaResultado, nivelMontarDados);
            vetResultado.add(obj);
        }
        return vetResultado;
    }

    public static GrauInstrucaoVO montarDados(ResultSet dadosSQL, int nivelMontarDados) throws Exception {
        GrauInstrucaoVO obj = new GrauInstrucaoVO();
        obj.setCodigo(dadosSQL.getInt("codigo"));
        obj.setDescricao(dadosSQL.getString("descricao"));
        obj.setNovoObj(false);
        if (nivelMontarDados == Uteis.NIVELMONTARDADOS_DADOSBASICOS) {
            return obj;
        }

        return obj;
    }

    public String consultarJSON() throws Exception {
        StringBuilder json;
        boolean dados;
        try (ResultSet rs = getPS().executeQuery()) {
            json = new StringBuilder();
            json.append("{\"aaData\":[");
            dados = false;
            while (rs.next()) {
                dados = true;
                json.append("[\"").append(rs.getString("codigo")).append("\",");
                json.append("\"").append(Uteis.normalizarStringJSON(rs.getString("descricao"))).append("\"],");
            }
        }
        if(dados) {
            json.deleteCharAt(json.toString().length()-1);
        }
        json.append("]}");
        return json.toString();
    }

    private PreparedStatement getPS() throws SQLException {
        String sql = "select codigo, descricao from grauinstrucao;";
        return con.prepareStatement(sql);
    }
     public List consultarParaImpressao(String filtro, String ordem, String campoOrdenacao, int i) throws SQLException {
         List lista;
         try (ResultSet rs = getPS().executeQuery()) {
             lista = new ArrayList();
             while (rs.next()) {
                 GrauInstrucaoVO grau = new GrauInstrucaoVO();
                 String geral = rs.getString("codigo") + rs.getString("descricao");
                 if (geral.toLowerCase().contains(filtro.toLowerCase())) {
                     grau.setCodigo(rs.getInt("codigo"));
                     grau.setDescricao(rs.getString("descricao"));
                     lista.add(grau);
                 }
             }
         }
         if (campoOrdenacao.equals("Código")) {
            Ordenacao.ordenarLista(lista, "codigo");
        } else if (campoOrdenacao.equals("Descrição")) {
            Ordenacao.ordenarLista(lista, "descricao");
        }
        if (ordem.contains("desc")) {
            Collections.reverse(lista);
        }
        return lista;

    }

    public int retornaCodigoGrauInstrucaoInformadoCasoExista(String escolaridade) {
        String sql = "SELECT codigo FROM grauinstrucao WHERE upper(descricao) = '" + escolaridade.toUpperCase() + "'";

        try (PreparedStatement ps = con.prepareStatement(sql)) {
            try (ResultSet rs = ps.executeQuery()) {
                if (rs.next()) {
                    return rs.getInt("codigo");
                }
            }
        } catch (SQLException e) {
            throw new RuntimeException(e);
        }
        return 0;
    }
}

package negocio.facade.jdbc.basico;

import negocio.comuns.financeiro.MovParcelaVO;
import negocio.comuns.plano.PlanoVO;
import negocio.comuns.plano.enumerador.TipoProduto;
import negocio.comuns.utilitarias.Uteis;
import negocio.facade.jdbc.arquitetura.SuperEntidade;
import org.json.JSONArray;

import java.sql.*;
import java.util.ArrayList;
import java.util.List;

public class PlanoTipo extends SuperEntidade{

    public PlanoTipo() throws Exception {
        super();
    }

    public PlanoTipo(Connection connection) throws Exception {
        super(connection);
    }

    public void incluir(PlanoTipoVO planoTipo) throws Exception {
        validarPermissaoInclusao(getIdEntidade());
        planoTipo.upperCase();
        String sql = "INSERT INTO " +
                "PlanoTipo( nome, ativo, tipo) " +
                " VALUES ( ?, ?, ?)";
        try (PreparedStatement ps = con.prepareStatement(sql)) {
            ps.setString(1, planoTipo.getNome());
            ps.setBoolean(2, planoTipo.isAtivo());
            ps.setString(3, planoTipo.getTipo());
            ps.execute();
        }
        planoTipo.setCodigo(obterValorChavePrimariaCodigo());
        planoTipo.setNovoObj(new Boolean(false));

        autlizarPlanoTipoTipoProduto(planoTipo);
    }

    public void alterar(PlanoTipoVO planoTipo) throws Exception {
        PlanoTipoVO.validarDados(planoTipo);
        validarPermissaoAlteracao(getIdEntidade());
        planoTipo.upperCase();
        String sql = "UPDATE planoTipo " +
                "set nome=?, " +
                "   ativo=?," +
                "   tipo=?  " +
                "WHERE ((codigo = ?))";
        try (PreparedStatement ps = con.prepareStatement(sql)) {
            ps.setString(1, planoTipo.getNome());
            ps.setBoolean(2, planoTipo.isAtivo());
            ps.setString(3, planoTipo.getTipo());
            ps.setInt(4, planoTipo.getCodigo());
            ps.execute();
        }

        autlizarPlanoTipoTipoProduto(planoTipo);
    }

    public void autlizarPlanoTipoTipoProduto(PlanoTipoVO planoTipo) throws SQLException {
        if(planoTipo.getTiposProduto().size() > 0){
            String sqlDelete = "DELETE FROM planoTipoTipoProduto " +
                    "WHERE planotipo = "+planoTipo.getCodigo();
            try (PreparedStatement ps = con.prepareStatement(sqlDelete)) {
                ps.execute();

                String sqlInsert = "INSERT INTO PlanoTipoTipoProduto (planoTipo, tipoProduto, codigoOperacaoFinanceira) VALUES ";
                String values = "";
                for (PlanoTipoTipoProdutoVO tipo : planoTipo.getTiposProduto()) {
                    values += "(" + planoTipo.getCodigo() + ", '" + tipo.getTipoProduto() + "', '" + tipo.getCodigoOperacaoFinanceira() + "'),";
                }
                values = Uteis.removerUltimoCaractere(values);
                sqlInsert += values;
                try (PreparedStatement ps2 = con.prepareStatement(sqlInsert)) {
                    ps2.execute();
                }
            }
        }
    }


    public void excluir(Integer codigo) throws Exception {
        validarPerissaoExclusao(getIdEntidade());
        String sql = "DELETE FROM planoTipo WHERE codigo = ?";
        try (PreparedStatement ps = con.prepareStatement(sql)) {
            ps.setInt(1, codigo);
            ps.execute();
        }
    }

    public List<PlanoTipoVO> consultar(int nivelMontarDados) throws Exception {
        return consultar(null, null, nivelMontarDados);
    }

    public List<PlanoTipoVO> consultar(Integer codigo, int nivelMontarDados) throws Exception {
        return consultar(codigo, null, nivelMontarDados);
    }

    public PlanoTipoVO consultarPorCodigo(Integer codigo, int nivelMontarDados) throws Exception {
        List<PlanoTipoVO> tipos = consultar(codigo, null, nivelMontarDados);
        if(tipos.size() == 1){
            return tipos.get(0);
        }else{
            return null;
        }
    }

    public PlanoTipoVO consultar(PlanoVO plano) throws Exception {
        if(plano.getPlanoTipo() != null && plano.getPlanoTipo().getCodigo() != 0){
            return consultar(plano.getPlanoTipo().getCodigo(), null, Uteis.NIVELMONTARDADOS_TODOS).get(0);
        }else{
            return null;
        }
    }

    public List<PlanoTipoVO> consultar(Integer codigo, String nome, int nivelMontarDados) throws Exception {
        consultar(getIdEntidade());
        String sql = "SELECT * " +
                "FROM PlanoTipo " +
                "WHERE 1 = 1 ";

        if(nome != null){
            sql += "AND upper( nome ) like('%" + nome.toUpperCase() + "%') ";
        }

        if(codigo != null)
            sql += "AND codigo = "+codigo;

        sql += " ORDER BY nome";

        try (Statement st = con.createStatement()) {
            try (ResultSet rs = st.executeQuery(sql)) {
                return (montarListaDados(rs, nivelMontarDados, con));
            }
        }
    }

    public static List<PlanoTipoVO> montarListaDados(ResultSet rs, int nivelMontarDados, Connection con) throws SQLException {
        List<PlanoTipoVO> tipos = new ArrayList<PlanoTipoVO>();
        while (rs.next()) {
            tipos.add(montarDados(rs, nivelMontarDados, con));
        }
        return tipos;
    }

    public static PlanoTipoVO montarDados(ResultSet rs, int nivelMontarDados, Connection con) throws SQLException {
        PlanoTipoVO planoTipo = new PlanoTipoVO(
                rs.getInt("codigo"),
                rs.getString("nome"),
                rs.getBoolean("ativo"),
                rs.getInt("limiteVendas"));
        try {
            planoTipo.setTipo(rs.getString("tipo"));
        }catch (Exception e){}

        if (nivelMontarDados == Uteis.NIVELMONTARDADOS_TODOS) {
            planoTipo.setTiposProduto(montarListaTiposProdutos(planoTipo.getCodigo(), con));
        }

        return planoTipo;
    }

    public static List<PlanoTipoTipoProdutoVO> montarListaTiposProdutos(Integer codigoPlanoTipo, Connection con) throws SQLException {
        List<TipoProduto> todosTiposProduto = TipoProduto.obterRelacionadosEmPlanoTipo();
        List<PlanoTipoTipoProdutoVO> listaTiposProdutos = new ArrayList<PlanoTipoTipoProdutoVO>();
        String values = "";
        for (TipoProduto tipo: todosTiposProduto) {
            values += "('"+tipo.getCodigo()+"'),";
        }
        values = Uteis.removerUltimoCaractere(values);

        String sql = "SELECT * " +
                "FROM (VALUES "+values+") AS t (codigoTipoProduto) " +
                "LEFT JOIN planotipotipoproduto ON planotipotipoproduto.tipoproduto = t.codigoTipoProduto " +
                "   AND planotipotipoproduto.planotipo = "+codigoPlanoTipo;
        try (PreparedStatement ps = con.prepareStatement(sql)) {
            try (ResultSet rs = ps.executeQuery()) {

                while (rs.next()) {
                    listaTiposProdutos.add(new PlanoTipoTipoProdutoVO(
                            rs.getInt("codigo"),
                            rs.getInt("planotipo"),
                            rs.getString("codigoTipoProduto"),
                            rs.getString("codigoOperacaoFinanceira")
                    ));
                }
            }
        }

        return listaTiposProdutos;
    }

    public JSONArray consultarJson(int nivelMontarDados) throws Exception {
        return PlanoTipo.toJsonArrayValues(consultar(nivelMontarDados));
    }

    public static JSONArray toJsonArrayValues(List<PlanoTipoVO> planoTipos){
        JSONArray jsonResult = new JSONArray();
        for (PlanoTipoVO tipo: planoTipos) {

            JSONArray jsonValues = new JSONArray();
            jsonValues.put(tipo.getCodigo());
            jsonValues.put(tipo.getNome());
            jsonValues.put(tipo.getAtivoTexto());

            jsonResult.put(jsonValues);
        }

        return jsonResult;
    }

    public String consultarCodigoOperacaoFinanceiraParaParcelas(PlanoTipoVO planoTipo, String codigoTipoProduto) throws SQLException {
        String sql = "SELECT * FROM planotipotipoproduto " +
                "WHERE planotipo ="+planoTipo.getCodigo() +
                " AND tipoproduto = '"+codigoTipoProduto+"'";
        try (PreparedStatement ps = con.prepareStatement(sql)) {
            try (ResultSet rs = ps.executeQuery()) {
                if (rs.next())
                    return rs.getString("codigoOperacaoFinanceira");
                else
                    return "";
            }
        }
    }
}

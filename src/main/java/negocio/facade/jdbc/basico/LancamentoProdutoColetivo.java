/*
 * To change this template, choose Too<PERSON> | Templates
 * and open the template in the editor.
 */
package negocio.facade.jdbc.basico;

import br.com.pactosolucoes.ce.comuns.enumerador.Mes;
import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import negocio.comuns.arquitetura.LogVO;
import negocio.comuns.arquitetura.UsuarioVO;
import negocio.comuns.basico.AmostraClienteTO;
import negocio.comuns.basico.LancamentoProdutoColetivoVO;
import negocio.comuns.basico.enumerador.TipoLancamentoProdutoColetivoEnum;
import negocio.comuns.contrato.ContratoModalidadeVO;
import negocio.comuns.contrato.ContratoVO;
import negocio.comuns.utilitarias.Calendario;
import negocio.comuns.utilitarias.Ordenacao;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisValidacao;
import negocio.facade.jdbc.arquitetura.Log;
import negocio.facade.jdbc.arquitetura.SuperEntidade;
import negocio.facade.jdbc.arquitetura.Usuario;
import negocio.facade.jdbc.financeiro.PagamentoMovParcela;
import negocio.facade.jdbc.plano.Modalidade;
import negocio.facade.jdbc.plano.Plano;
import negocio.facade.jdbc.plano.Produto;
import negocio.interfaces.basico.LancamentoProdutoColetivoInterfaceFacade;
import servicos.operacoes.LancamentoProdutoColetivoService;

/**
 *
 * <AUTHOR>
 */
public class LancamentoProdutoColetivo extends SuperEntidade implements LancamentoProdutoColetivoInterfaceFacade {

    public LancamentoProdutoColetivo() throws Exception {
        super();
    }

    public LancamentoProdutoColetivo(Connection con) throws Exception {
        super(con);
    }

    @Override
    public void incluir(LancamentoProdutoColetivoVO obj) throws Exception {
        try {
            con.setAutoCommit(false);
            incluirSemCommit(obj);
            con.commit();
        } catch (Exception e) {
            obj.setNovoObj(true);
            obj.setCodigo(new Integer(0));
            con.rollback();
            con.setAutoCommit(true);
            throw e;
        } finally {
            con.setAutoCommit(true);
        }
    }

    public void incluirSemCommit(LancamentoProdutoColetivoVO obj) throws Exception {
        LancamentoProdutoColetivoVO.validarDados(obj);
        StringBuilder insert = new StringBuilder();
        insert.append("INSERT INTO lancamentoprodutocoletivo( \n");
        insert.append("produto, descricao, plano, mes, parcela, tipolancamento, \n");
        insert.append("modalidade, nrvezesparcelar, usuario, dataespecifica, datalancamento, datafim, empresa, jaFoiLancado, valor, matriculas)\n");
        insert.append("VALUES ( ?, ?, ?, ?, ?, ?,?, ?, ?, ?, ?, ?, ?, ?, ?, ?);");
        try (PreparedStatement stm = con.prepareStatement(insert.toString())) {
            int i = 0;
            stm.setInt(++i, obj.getProdutoVO().getCodigo());
            stm.setString(++i, obj.getDescricao());
            resolveFKNull(stm, ++i, obj.getPlanoVO().getCodigo());
            resolveIntegerNull(stm, ++i, obj.getMes() == null ? null : obj.getMes().getCodigo());
            resolveIntegerNull(stm, ++i, obj.getParcela());
            stm.setInt(++i, obj.getTipoLancamento().ordinal());
            resolveFKNull(stm, ++i, obj.getModalidadeVO().getCodigo());
            stm.setInt(++i, obj.getNrVezesParcelar());
            stm.setInt(++i, obj.getUsuario().getCodigo());
            resolveDateNull(stm, ++i, obj.getDataEspecifica());
            stm.setTimestamp(++i, Uteis.getDataJDBCTimestamp(obj.getDataLancamento()));
            stm.setTimestamp(++i, Uteis.getDataJDBCTimestamp(obj.getDataFim()));
            stm.setInt(++i, obj.getEmpresa().getCodigo());
            stm.setBoolean(++i, obj.getJaFoiLancado());
            stm.setDouble(++i, obj.getValor());
            stm.setString(++i, obj.getMatriculas());
            stm.execute();
        }
        obj.setCodigo(obterValorChavePrimariaCodigo());
        obj.setNovoObj(false);
    }

    @Override
    public void excluir(LancamentoProdutoColetivoVO lancamento) throws Exception {
        executarConsulta("DELETE FROM lancamentoprodutocoletivo WHERE codigo = " + lancamento.getCodigo(), con);
    }

    @Override
    public void alterar(LancamentoProdutoColetivoVO obj) throws Exception {
        try {
            con.setAutoCommit(false);
            alterarSemCommit(obj);
            con.commit();
        } catch (Exception e) {
            con.rollback();
            con.setAutoCommit(true);
            throw e;
        } finally {
            con.setAutoCommit(true);
        }
    }

    public void alterarSemCommit(LancamentoProdutoColetivoVO obj) throws Exception {
        LancamentoProdutoColetivoVO.validarDados(obj);
        StringBuilder alter = new StringBuilder();
        alter.append("UPDATE lancamentoprodutocoletivo \n");
        alter.append("SET produto=?, descricao=?, plano=?, mes=?, parcela=?, \n");
        alter.append("tipolancamento=?, modalidade=?, nrvezesparcelar=?, usuario=?, \n");
        alter.append("dataespecifica=?, datalancamento=?, datafim=?, empresa = ?, jaFoiLancado = ?, valor = ?, matriculas = ? \n");
        alter.append("WHERE codigo = ?;");
        try (PreparedStatement stm = con.prepareStatement(alter.toString())) {
            int i = 0;
            stm.setInt(++i, obj.getProdutoVO().getCodigo());
            stm.setString(++i, obj.getDescricao());
            resolveFKNull(stm, ++i, obj.getPlanoVO().getCodigo());
            resolveIntegerNull(stm, ++i, obj.getMes() == null ? null : obj.getMes().getCodigo());
            resolveIntegerNull(stm, ++i, obj.getParcela());
            stm.setInt(++i, obj.getTipoLancamento().ordinal());
            resolveFKNull(stm, ++i, obj.getModalidadeVO().getCodigo());
            stm.setInt(++i, obj.getNrVezesParcelar());
            stm.setInt(++i, obj.getUsuario().getCodigo());
            resolveDateNull(stm, ++i, obj.getDataEspecifica());
            stm.setTimestamp(++i, Uteis.getDataJDBCTimestamp(obj.getDataLancamento()));
            stm.setTimestamp(++i, Uteis.getDataJDBCTimestamp(obj.getDataFim()));
            stm.setInt(++i, obj.getEmpresa().getCodigo());
            stm.setBoolean(++i, obj.getJaFoiLancado());
            stm.setDouble(++i, obj.getValor());
            stm.setString(++i, obj.getMatriculas());
            stm.setInt(++i, obj.getCodigo());
            stm.execute();
        }

    }

    @Override
    public List<LancamentoProdutoColetivoVO> consultarTodos(Integer nivelMontarDados) throws Exception {
        try (ResultSet rs = criarConsulta("SELECT * FROM lancamentoprodutocoletivo", con)) {
            return montarDadosConsulta(rs, nivelMontarDados, con);
        }
    }

    public static List<LancamentoProdutoColetivoVO> montarDadosConsulta(ResultSet tabelaResultado, int nivelMontarDados, Connection con) throws Exception {
        List<LancamentoProdutoColetivoVO> vetResultado = new ArrayList<LancamentoProdutoColetivoVO>();
        while (tabelaResultado.next()) {
            LancamentoProdutoColetivoVO obj = montarDados(tabelaResultado, nivelMontarDados, con);
            vetResultado.add(obj);
        }
        return vetResultado;
    }

    public static LancamentoProdutoColetivoVO montarDados(ResultSet rs, int nivelMontarDados, Connection con) throws Exception {
        LancamentoProdutoColetivoVO obj = new LancamentoProdutoColetivoVO();
        obj.setCodigo(rs.getInt("codigo"));
        obj.setDescricao(rs.getString("descricao"));
        obj.getProdutoVO().setCodigo(rs.getInt("produto"));
        obj.getUsuario().setCodigo(rs.getInt("usuario"));
        obj.getModalidadeVO().setCodigo(rs.getInt("modalidade"));
        obj.getEmpresa().setCodigo(rs.getInt("empresa"));
        obj.getPlanoVO().setCodigo(rs.getInt("plano"));
        obj.setDataEspecifica(rs.getTimestamp("dataespecifica"));
        obj.setDataLancamento(rs.getTimestamp("datalancamento"));
        obj.setDataFim(rs.getTimestamp("datafim"));
        obj.setMes(Mes.getMesPeloCodigo(rs.getInt("mes")));
        obj.setParcela(rs.getInt("parcela"));
        obj.setNrVezesParcelar(rs.getInt("nrvezesparcelar"));
        obj.setTipoLancamento(TipoLancamentoProdutoColetivoEnum.getFromId(rs.getInt("tipolancamento")));
        obj.getEmpresa().setCodigo(rs.getInt("empresa"));
        obj.setJaFoiLancado(rs.getBoolean("jaFoiLancado"));
        try {
            obj.setMatriculas(rs.getString("matriculas"));
        }catch (Exception e){}

        obj.setValor(rs.getDouble("valor"));
        obj.setVigenciaContratoInicio(rs.getDate("vigenciaContratoInicio"));
        obj.setVigenciaContratoFim(rs.getDate("vigenciaContratoFim"));

        if (nivelMontarDados == Uteis.NIVELMONTARDADOS_TODOS) {
            if (!UteisValidacao.emptyNumber(obj.getUsuario().getCodigo())) {
                Usuario usuarioDao = new Usuario(con);
                obj.setUsuario(usuarioDao.consultarPorCodigo(obj.getUsuario().getCodigo(), Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA));
            }
            if (!UteisValidacao.emptyNumber(obj.getProdutoVO().getCodigo())) {
                Produto produtoDao = new Produto(con);
                obj.setProdutoVO(produtoDao.consultarPorChavePrimaria(obj.getProdutoVO().getCodigo(), Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA));
            }
            if (!UteisValidacao.emptyNumber(obj.getModalidadeVO().getCodigo())) {
                Modalidade modalidadeDao = new Modalidade(con);
                obj.setModalidadeVO(modalidadeDao.consultarPorChavePrimaria(obj.getModalidadeVO().getCodigo(), Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA));
            }
            if (!UteisValidacao.emptyNumber(obj.getPlanoVO().getCodigo())) {
                Plano planoDao = new Plano(con);
                obj.setPlanoVO(planoDao.consultarPorChavePrimaria(obj.getPlanoVO().getCodigo(), Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA));
            }
        }
        return obj;
    }

    @Override
    public String consultarJSON(Integer empresa) throws Exception {
        StringBuilder json;
        boolean dados;
        try (ResultSet rs = getPS(empresa).executeQuery()) {
            json = new StringBuilder();
            json.append("{\"aaData\":[");
            dados = false;
            while (rs.next()) {
                dados = true;
                json.append("[\"").append(rs.getString("codigo")).append("\",");
                json.append("\"").append(rs.getString("descricao").trim()).append("\",");
                json.append("\"").append(rs.getString("produto").trim()).append("\",");
                json.append("\"").append(rs.getString("usuario").trim()).append("\",");
                json.append("\"").append(rs.getTimestamp("datalancamento")).append("\"],");
            }
        }
        if (dados) {
            json.deleteCharAt(json.toString().length() - 1);
        }
        json.append("]}");
        return json.toString();
    }

    private PreparedStatement getPS(Integer empresa) throws SQLException {
        StringBuilder sql = new StringBuilder("SELECT lpc.codigo, lpc.descricao, p.descricao as produto, u.nome as usuario, lpc.datalancamento\n");
        sql.append(" FROM lancamentoprodutocoletivo lpc\n");
        sql.append(" INNER JOIN produto p ON p.codigo = lpc.produto \n");
        sql.append(" INNER JOIN usuario u ON u.codigo = lpc.usuario \n");

        if (empresa != 0) {
            sql.append(" WHERE lpc.empresa = ?");
        }
        sql.append("  ORDER BY lpc.descricao");

        PreparedStatement sqlConsultar = con.prepareStatement(sql.toString());
        if (empresa != 0) {
            sqlConsultar.setInt(1, empresa);
        }
        return sqlConsultar;
    }

    public List consultarParaImpressao(String filtro, String ordem, String campoOrdenacao, int codigoEmpresa) throws SQLException {
        List lista;
        try (ResultSet rs = getPS(codigoEmpresa).executeQuery()) {
            lista = new ArrayList();
            while (rs.next()) {
                LancamentoProdutoColetivoVO lancProd = new LancamentoProdutoColetivoVO();
                String geral = rs.getString("codigo") + rs.getString("descricao") + rs.getString("produto") + rs.getString("usuario") + rs.getString("datalancamento");
                if (geral.toLowerCase().contains(filtro.toLowerCase())) {
                    lancProd.setCodigo(rs.getInt("codigo"));
                    lancProd.setDescricao(rs.getString("descricao"));
                    lancProd.getProdutoVO().setDescricao(rs.getString("produto"));
                    lancProd.getUsuario().setNome(rs.getString("usuario"));
                    lancProd.setDataLancamento(rs.getDate("dataLancamento"));
                    lista.add(lancProd);
                }
            }
        }
        if (campoOrdenacao.equals("Código")) {
            Ordenacao.ordenarLista(lista, "codigo");
        } else if (campoOrdenacao.equals("Descrição")) {
            Ordenacao.ordenarLista(lista, "descricao");
        } else if (campoOrdenacao.equals("Produto")) {
            Ordenacao.ordenarLista(lista, "produtoNome");
        } else if (campoOrdenacao.equals("Usuário")) {
            Ordenacao.ordenarLista(lista, "usuarioNome");
        } else if (campoOrdenacao.equals("Lançamento")) {
            Ordenacao.ordenarLista(lista, "dataLancamento");
        }

        if (ordem.contains("desc")) {
            Collections.reverse(lista);
        }
        return lista;
    }

    @Override
    public LancamentoProdutoColetivoVO consultarPorChavePrimaria(Integer codigo, Integer nivelMontarDados) throws Exception {
        try (ResultSet rs = criarConsulta("SELECT * FROM lancamentoprodutocoletivo where codigo = " + codigo, con)) {
            if (rs.next()) {
                return montarDados(rs, nivelMontarDados, con);
            }
        }
        return new LancamentoProdutoColetivoVO();
    }

    @Override
    public void atualizarJaFoiLancado(LancamentoProdutoColetivoVO lancamento) throws Exception {
        executarConsulta("UPDATE lancamentoprodutocoletivo SET jafoilancado = true where codigo = " + lancamento.getCodigo(), con);
    }

    @Override
    public List<AmostraClienteTO> consultarAmostraLancamentoColetivo(LancamentoProdutoColetivoVO lancamento, boolean ignorarJaLancandos) throws Exception {
        StringBuilder sql = new StringBuilder("SELECT * FROM ( SELECT cli.codigocliente, cli.nomecliente as nome , cli.matricula, max(con.codigo) as contrato \n");
        sql.append(" FROM situacaoclientesinteticodw cli \n");
        sql.append(" INNER JOIN contrato con ON cli.codigopessoa = con.pessoa AND con.situacao = 'AT'  \n");
        if (!UteisValidacao.emptyNumber(lancamento.getPlanoVO().getCodigo())) {
            sql.append("AND con.plano = ").append(lancamento.getPlanoVO().getCodigo()).append("\n");
        }
        if (!UteisValidacao.emptyNumber(lancamento.getModalidadeVO().getCodigo())) {
            sql.append("INNER JOIN contratomodalidade conm ON conm.contrato = con.codigo AND conm.modalidade = ").append(lancamento.getModalidadeVO().getCodigo()).append(" \n");
        }
        if (lancamento.getTipoContratoSemProduto()) {
            sql.append("LEFT JOIN movproduto mprod ON mprod.contrato = con.codigo AND mprod.produto = ").append(lancamento.getProdutoVO().getCodigo()).append("\n");
        }
        sql.append(" WHERE cli.empresacliente = ").append(lancamento.getEmpresa().getCodigo()).append(" and cli.situacao = 'AT' \n");
        if(lancamento.getVigenciaContratoFim()!=null && lancamento.getVigenciaContratoInicio()!=null){
            sql.append(" AND con.vigenciade <'").append(Uteis.getData(lancamento.getVigenciaContratoFim())).append("'\n");
            sql.append(" AND con.vigenciaate >='").append(Uteis.getData(lancamento.getVigenciaContratoFim())).append("'\n");
            sql.append(" AND con.vigenciaate > '").append(Uteis.getData(Calendario.hoje())).append("'\n");
        }
        if(lancamento.getDataEspecifica()!=null){
            sql.append(" AND con.vigenciade <='").append(Uteis.getData(lancamento.getDataEspecifica())).append("'\n");
            sql.append(" AND (con.vigenciaate >= '").append(Uteis.getData(lancamento.getDataEspecifica())).append("'\n");
            sql.append("      OR con.vigenciaateajustada >= '").append(Uteis.getData(Calendario.hoje())).append("')\n");
        }

        if(!UteisValidacao.emptyString(lancamento.getMatriculas())){

            sql.append(" AND cli.matricula in (").append(lancamento.getMatriculas()).append(") \n");
        }

        if(ignorarJaLancandos){
            sql.append(" AND cli.codigopessoa not in(select pessoa from movproduto where lancamentocoletivo = ").append(lancamento.getCodigo());
            sql.append(" and lancamentocoletivo is not null ) ");
        }
        if (lancamento.getTipoContratoSemProduto()) {
            sql.append("AND mprod.codigo is null\n");
        }


        sql.append(" GROUP BY  cli.codigocliente, cli.nomecliente, cli.matricula ) AS consulta \n");

        if (lancamento.getMes().getCodigo() == 0) {
            if (!UteisValidacao.emptyNumber(lancamento.getParcela())) {
                sql.append("WHERE contrato IN (SELECT contrato FROM movparcela WHERE descricao LIKE 'PARCELA " + lancamento.getParcela() + "')");
            }
        }

        //se o mes diferente de 0 e menor ou igual a 9 muda acrescenta no LIKE 0 e /%...
        if (lancamento.getMes().getCodigo() != 0 && lancamento.getMes().getCodigo() <= 9){
            sql.append("WHERE contrato IN (SELECT contrato FROM movproduto WHERE mesreferencia LIKE '0"+ lancamento.getMes().getCodigo() +"/%')");
        }

        //se o mes diferente de 0 e maior que 9 acrescenta no LIKE /%...
        if (lancamento.getMes().getCodigo() != 0 && lancamento.getMes().getCodigo() > 9){
            sql.append("WHERE contrato IN (SELECT contrato FROM movproduto WHERE mesreferencia LIKE '"+ lancamento.getMes().getCodigo() +"/%')");
        }

        sql.append("ORDER by nome");

        Cliente clienteDao = new Cliente(con);
        return clienteDao.consultarAmostra(sql.toString(), null);
    }

    @Override
    public int lancarProdutos(LancamentoProdutoColetivoVO lancamento, boolean ignorarJaLancados) throws Exception {
        LancamentoProdutoColetivoService servico = new LancamentoProdutoColetivoService(con);
        return servico.lancarProdutoColetivo(lancamento.getCodigo(), ignorarJaLancados);

    }

    @Override
    public List<AmostraClienteTO> consultarJaAlcancadosLancamentoColetivo(LancamentoProdutoColetivoVO lancamento) throws Exception {
        StringBuilder sql = new StringBuilder("SELECT distinct cli.codigocliente, cli.nomecliente as nome , cli.matricula FROM situacaoclientesinteticodw cli \n");
        sql.append("INNER JOIN movproduto mp ON mp.pessoa = cli.codigopessoa and mp.lancamentocoletivo = ").append(lancamento.getCodigo());
        sql.append(" order by nome");

        Cliente clienteDao = new Cliente(con);
        return clienteDao.consultarAmostra(sql.toString(), null);
    }

    @Override
    public List<LancamentoProdutoColetivoVO> consultarLancamentosEscopoContrato(ContratoVO contrato) throws Exception {

        String modalidades = "";
        for (ContratoModalidadeVO cm : contrato.getContratoModalidadeVOs()) {
            modalidades += "," + cm.getModalidade().getCodigo();
        }
        StringBuilder sql = new StringBuilder("select * from lancamentoprodutocoletivo \n");
        sql.append(" WHERE tipolancamento IN (").append(TipoLancamentoProdutoColetivoEnum.MES.ordinal());
        sql.append(",").append(TipoLancamentoProdutoColetivoEnum.PARCELA.ordinal()).append(") \n");
        sql.append("AND (plano = ? OR plano is null) \n");
        sql.append("AND (modalidade IN(").append(modalidades.replaceFirst(",", "")).append(") OR modalidade is null) \n");
        sql.append("AND empresa = ? \n");
        sql.append("AND (datafim >= ? or datafim is null)");
        int i = 1;
        try (PreparedStatement stm = con.prepareStatement(sql.toString())) {
            stm.setInt(i++, contrato.getPlano().getCodigo());
            stm.setInt(i++, contrato.getEmpresa().getCodigo());
            stm.setDate(i++, Uteis.getDataJDBC(Calendario.hoje()));
            try (ResultSet rs = stm.executeQuery()) {
                return montarDadosConsulta(rs, Uteis.NIVELMONTARDADOS_TODOS, con);
            }
        }

    }

    public void estornarProdutosAbertosLancadosColetivamente(LancamentoProdutoColetivoVO lancamento, UsuarioVO usuario) throws Exception {
        try {
            con.setAutoCommit(false);
            StringBuilder logTexto = new StringBuilder();
            try (ResultSet rs = criarConsulta("SELECT cli.codigomatricula, mp.totalfinal, mp.codigo FROM movproduto mp \n"
                    + "INNER JOIN cliente cli ON mp.pessoa = cli.pessoa \n"
                    + "WHERE lancamentocoletivo = " + lancamento.getCodigo()
                    + "and not EXISTS (SELECT mpa.codigo FROM movparcela mpa \n"
                    + "inner join movprodutoparcela mpp ON mpa.codigo = mpp.movparcela and mpp.movproduto = mp.codigo and mpa.situacao = 'PG')\n"
                    + "and mp.situacao = 'EA'", con)) {
                while (rs.next()) {
                    int codigoMovProduto = rs.getInt("codigo");
                    logTexto.append(rs.getInt("codigomatricula")).append("- Vlr.:").append(Uteis.arredondarForcando2CasasDecimais(rs.getDouble("totalfinal"))).append("||");
                    StringBuilder sqlDelete = new StringBuilder("DELETE FROM movparcela WHERE codigo IN \n");
                    sqlDelete.append(" (SELECT mp.codigo FROM movparcela mp \n");
                    sqlDelete.append(" INNER JOIN movprodutoparcela mpp ON mp.codigo = mpp.movparcela ");
                    sqlDelete.append(" and mpp.movproduto = ").append(codigoMovProduto);
                    sqlDelete.append(" );\n");
                    sqlDelete.append(" DELETE FROM movproduto WHERE codigo = ").append(codigoMovProduto);
                    executarConsulta(sqlDelete.toString(), con);
                }

                PagamentoMovParcela.corrigirPagamentosSemMovParcelas(con);
            }
            LogVO log = new LogVO();
            log.setNomeEntidade("LANCAMENTOPRODUTOCOLETIVO");
            log.setNomeEntidade("LancamentoProdutoColetivoVO");
            log.setChavePrimaria(lancamento.getCodigo().toString());
            log.setNomeCampo("Estornar abertos");
            log.setValorCampoAnterior("-");
            log.setValorCampoAlterado(logTexto.toString().length() > 10000 ? logTexto.toString().substring(0, 9999) : logTexto.toString());
//            log.setValorCampoAlterado(logTexto.toString());
            log.setResponsavelAlteracao(usuario.getNome());
            log.setUserOAMD(usuario.getUserOamd());
            log.setOperacao("ESTORNO");
            Log logDao = new Log(con);
            logDao.incluirSemCommit(log);
            con.commit();
        } catch (Exception e) {
            con.rollback();
            con.setAutoCommit(true);
            throw e;
        } finally {
            con.setAutoCommit(true);
        }
    }
}

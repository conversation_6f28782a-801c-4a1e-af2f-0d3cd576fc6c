/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package negocio.facade.jdbc.basico;

import negocio.comuns.basico.EmailVO;
import negocio.comuns.basico.PessoaVO;
import negocio.comuns.utilitarias.ConsistirException;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisValidacao;
import negocio.facade.jdbc.arquitetura.SuperEntidade;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.Statement;
import java.util.ArrayList;
import java.util.Iterator;
import java.util.List;

/**
 *
 * <AUTHOR>
 */
public class Email extends SuperEntidade {    
    protected PessoaVO pessoa;

    public Email() throws Exception {
        super();        
    }

    public Email(Connection conexao) throws Exception {
        super(conexao);
    }


    /**
     * Operação responsável por retornar um novo objeto da classe <code>EmailVO</code>.
     */
    public EmailVO novo() throws Exception {
        incluir(getIdEntidade());
        EmailVO obj = new EmailVO();
        return obj;
    }

    /**
     * Operação responsável por incluir no banco de dados um objeto da classe <code>EmailVO</code>.
     * Primeiramente valida os dados (<code>validarDados</code>) do objeto. Verifica a conexão com o banco de dados e a permissão do usuário
     * para realizar esta operacão na entidade.
     * Isto, através da operação <code>incluir</code> da superclasse.
     * @param obj  Objeto da classe <code>EmailVO</code> que será gravado no banco de dados.
     * @exception Exception Caso haja problemas de conexão, restrição de acesso ou validação de dados.
     */
    public void incluir(EmailVO obj) throws Exception {
        incluir(obj, true);
    }

    public void incluir(EmailVO obj, boolean controlarAcesso) throws Exception {
        if (controlarAcesso) {
            incluir(getIdEntidade());
        }
        obj.realizarUpperCaseDados();
        String sql = "INSERT INTO Email(email, pessoa, emailCorrespondencia,receberEmailNovidades, bloqueadobounce) VALUES ( ?, ?, ?, ?, ?)";
        try (PreparedStatement sqlInserir = con.prepareStatement(sql)) {
            sqlInserir.setString(1, obj.getEmail());
            if (obj.getPessoa().intValue() != 0) {
                sqlInserir.setInt(2, obj.getPessoa().intValue());
            } else {
                sqlInserir.setNull(2, 0);
            }
            sqlInserir.setBoolean(3, obj.getEmailCorrespondencia());
            sqlInserir.setBoolean(4, obj.isReceberEmailNovidades());
            sqlInserir.setBoolean(5, obj.getBloqueadoBounce());
            sqlInserir.execute();
        }
        obj.setCodigo(obterValorChavePrimariaCodigo());
        obj.setNovoObj(false);
    }

    /**
     * Operação responsável por alterar no BD os dados de um objeto da classe <code>EmailVO</code>.
     * Sempre utiliza a chave primária da classe como atributo para localização do registro a ser alterado.
     * Primeiramente valida os dados (<code>validarDados</code>) do objeto. Verifica a conexão com o banco de dados e a permissão do usuário
     * para realizar esta operacão na entidade.
     * Isto, através da operação <code>alterar</code> da superclasse.
     * @param obj    Objeto da classe <code>EmailVO</code> que será alterada no banco de dados.
     * @exception Exception Caso haja problemas de conexão, restrição de acesso ou validação de dados.
     */
    public void alterar(EmailVO obj) throws Exception {
        alterar(obj, true);
    }

    public void alterar(EmailVO obj, boolean controlarAcesso) throws Exception {
        if (controlarAcesso) {
            alterar(getIdEntidade());
        }
        obj.realizarUpperCaseDados();
        String sql = "UPDATE Email set Email=?, Pessoa=?, EmailCorrespondencia=?, receberEmailNovidades=? WHERE ((codigo = ?))";
        try (PreparedStatement sqlAlterar = con.prepareStatement(sql)) {
            sqlAlterar.setString(1, obj.getEmail());
            if (!obj.getPessoa().equals(0)) {
                sqlAlterar.setInt(2, obj.getPessoa());
            } else {
                sqlAlterar.setNull(2, 0);
            }
            sqlAlterar.setBoolean(3, obj.getEmailCorrespondencia());
            sqlAlterar.setBoolean(4, obj.isReceberEmailNovidades());
            sqlAlterar.setInt(5, obj.getCodigo());
            sqlAlterar.execute();
        }
    }

    /**
     * Operação responsável por excluir no BD um objeto da classe <code>EmailVO</code>.
     * Sempre localiza o registro a ser excluído através da chave primária da entidade.
     * Primeiramente verifica a conexão com o banco de dados e a permissão do usuário
     * para realizar esta operacão na entidade.
     * Isto, através da operação <code>excluir</code> da superclasse.
     * @param obj    Objeto da classe <code>EmailVO</code> que será removido no banco de dados.
     * @exception Exception Caso haja problemas de conexão ou restrição de acesso.
     */
    public void excluir(EmailVO obj) throws Exception {
        excluir(getIdEntidade());
        String sql = "DELETE FROM Email WHERE ((codigo = ?))";
        try (PreparedStatement sqlExcluir = con.prepareStatement(sql)) {
            sqlExcluir.setInt(1, obj.getCodigo().intValue());
            sqlExcluir.execute();
        }
    }

    /**
     * Responsável por realizar uma consulta de <code>Email</code> através do valor do atributo 
     * <code>Integer codigo</code>. Retorna os objetos com valores iguais ou superiores ao parâmetro fornecido.
     * Faz uso da operação <code>montarDadosConsulta</code> que realiza o trabalho de prerarar o List resultante.
     * @param   controlarAcesso Indica se a aplicação deverá verificar se o usuário possui permissão para esta consulta ou não.
     * @return  List Contendo vários objetos da classe <code>EmailVO</code> resultantes da consulta.
     * @exception Exception Caso haja problemas de conexão ou restrição de acesso.
     */
    public List consultarPorCodigo(Integer valorConsulta, boolean controlarAcesso, int nivelMontarDados) throws Exception {
        consultar(getIdEntidade(), controlarAcesso);
        String sqlStr = "SELECT * FROM Email WHERE codigo >= " + valorConsulta.intValue() + " ORDER BY codigo";
        try (Statement stm = con.createStatement()) {
            try (ResultSet tabelaResultado = stm.executeQuery(sqlStr)) {
                return (montarDadosConsulta(tabelaResultado, nivelMontarDados));
            }
        }
    }

    /**
     * Responsável por montar os dados de vários objetos, resultantes de uma consulta ao banco de dados (<code>ResultSet</code>).
     * Faz uso da operação <code>montarDados</code> que realiza o trabalho para um objeto por vez.
     * @return  List Contendo vários objetos da classe <code>EmailVO</code> resultantes da consulta.
     */
    public static List<EmailVO> montarDadosConsulta(ResultSet tabelaResultado, int nivelMontarDados) throws Exception {
        List<EmailVO> vetResultado = new ArrayList<EmailVO>();
        while (tabelaResultado.next()) {
            EmailVO obj = montarDados(tabelaResultado, nivelMontarDados);
            vetResultado.add(obj);
        }
        return vetResultado;
    }

    public static EmailVO montarDadosBasico(ResultSet dadosSQL) throws Exception {
        EmailVO obj = new EmailVO();
        obj.setNovoObj(false);
        obj.setCodigo(dadosSQL.getInt("codigo"));
        obj.setEmail(dadosSQL.getString("Email"));
        obj.setEmailCorrespondencia(dadosSQL.getBoolean("emailCorrespondencia"));
        obj.setPessoa(dadosSQL.getInt("pessoa"));
        obj.setBloqueadoBounce(dadosSQL.getBoolean("bloqueadoBounce"));
        try {
            obj.setBounced(dadosSQL.getBoolean("bloqueioOptin") == true ? 1 : 0);
        }
        catch (Exception ex){}

        return obj;
    }

    /**
     * Responsável por montar os dados resultantes de uma consulta ao banco de dados (<code>ResultSet</code>)
     * em um objeto da classe <code>EmailVO</code>.
     * @return  O objeto da classe <code>EmailVO</code> com os dados devidamente montados.
     */
    public static EmailVO montarDados(ResultSet dadosSQL, int nivelMontarDados) throws Exception {
        EmailVO obj = montarDadosBasico(dadosSQL);

        if (nivelMontarDados == Uteis.NIVELMONTARDADOS_CLIENTES_MAILING){
        	obj.getPessoaVO().setNome(dadosSQL.getString("nome"));
                obj.setCliente(dadosSQL.getInt("codigocliente"));
            return obj;	
        }
        

        if (nivelMontarDados == Uteis.NIVELMONTARDADOS_DADOSBASICOS) {
            return obj;
        }
        return obj;
    }

    /**
     * Operação responsável por excluir todos os objetos da <code>EmailVO</code> no BD.
     * Faz uso da operação <code>excluir</code> disponível na classe <code>Email</code>.
     * @param pessoa campo chave para exclusão dos objetos no BD.
     * @exception Exception  Erro de conexão com o BD ou restrição de acesso a esta operação.
     */
    public void excluirEmails(Integer pessoa) throws Exception {
        excluir(getIdEntidade());
        String sql = "DELETE FROM Email WHERE (pessoa = ?)";
        try (PreparedStatement sqlExcluir = con.prepareStatement(sql)) {
            sqlExcluir.setInt(1, pessoa.intValue());
            sqlExcluir.execute();
        }
    }

    /**
     * Operação responsável por excluir todos os objetos da <code>EnderecoVO</code> no BD.
     * Faz uso da operação <code>excluir</code> disponível na classe <code>Endereco</code>.
     * @param <code>pessoa</code> campo chave para exclusão dos objetos no BD.
     * @exception Exception  Erro de conexão com o BD ou restrição de acesso a esta operação.
     */
    public void excluirEmails(Integer pessoa, boolean validarPermissao)  throws Exception {
        if (validarPermissao) {
            excluir(getIdEntidade()); // Faz a validação de permissões
        }
        String sql = "DELETE FROM Email WHERE (pessoa = ?)";
        PreparedStatement sqlExcluir = con.prepareStatement(sql);
        sqlExcluir.setInt(1, pessoa.intValue());
        sqlExcluir.execute();
    }

    /**
     * Operação responsável por alterar todos os objetos da <code>EmailVO</code> contidos em um Hashtable no BD.
     * Faz uso da operação <code>excluirEmails</code> e <code>incluirEmails</code> disponíveis na classe <code>Email</code>.
     * @param objetos  List com os objetos a serem alterados ou incluídos no BD.
     * @exception Exception  Erro de conexão com o BD ou restrição de acesso a esta operação.
     */
    public void alterarEmails(Integer pessoa, List objetos) throws Exception {
        alterarEmails(pessoa, objetos, true);
    }

    public void alterarEmails(Integer pessoa, List objetos, boolean controlarAcesso) throws Exception {
        String str = "DELETE FROM  Email  WHERE pessoa = " + pessoa;
        Iterator i = objetos.iterator();
        while (i.hasNext()) {
            EmailVO objeto = (EmailVO) i.next();
            str += " AND codigo <> " + objeto.getCodigo();
        }
        try (PreparedStatement sqlExcluir = con.prepareStatement(str)) {
            sqlExcluir.execute();
        }
        Iterator e = objetos.iterator();
        while (e.hasNext()) {
            EmailVO obj = (EmailVO) e.next();
            if (obj.getCodigo().equals(0)) {
                obj.setPessoa(pessoa);
                incluir(obj, controlarAcesso);
            } else {
                alterar(obj, controlarAcesso);
            }
        }

        //        excluirEmails(pessoa);
//        incluirEmails(pessoa, objetos);
    }

    /**
     * Operação responsável por incluir objetos da <code>EmailVO</code> no BD.
     * Garantindo o relacionamento com a entidade principal <code>basico.Pessoa</code> através do atributo de vínculo.
     * @param objetos List contendo os objetos a serem gravados no BD da classe.
     * @exception Exception  Erro de conexão com o BD ou restrição de acesso a esta operação.
     */
    public void incluirEmails(Integer pessoaPrm, List objetos) throws Exception {
        incluirEmails(pessoaPrm, objetos, true);
    }
    public void incluirEmails(Integer pessoaPrm, List objetos, boolean validarPermissao) throws Exception {
        Iterator e = objetos.iterator();
        while (e.hasNext()) {
            EmailVO obj = (EmailVO) e.next();
            if(!obj.isEmpty()) {
                obj.setPessoa(pessoaPrm);
                incluir(obj, validarPermissao);
            }
        }
    }

    public List<EmailVO> consultarEmails(Integer pessoa, boolean somenteEmailCorrespondencia, Boolean controleAcesso, int nivelMontarDados) throws Exception {
        return consultarEmailsGeral(pessoa, somenteEmailCorrespondencia, controleAcesso, nivelMontarDados);
    }

    public List<EmailVO> consultarEmails(Integer pessoa, Boolean controleAcesso, int nivelMontarDados) throws Exception {
        return consultarEmailsGeral(pessoa, false, controleAcesso, nivelMontarDados);
    }

    private List<EmailVO> consultarEmailsGeral(Integer pessoa, boolean somenteEmailCorrespondencia, Boolean controleAcesso, int nivelMontarDados) throws Exception {
        consultar(getIdEntidade(), controleAcesso);
        List<EmailVO> objetos = new ArrayList<EmailVO>();
        StringBuilder sql = new StringBuilder();
        sql.append("select e.*, false  bloqueioOptin  from Email e left join cliente c on e.pessoa  = c.pessoa \n" +
                "  WHERE e.pessoa = ? ");
        if (somenteEmailCorrespondencia) {
            sql.append(" and e.emailcorrespondencia = true and e.bloqueadobounce is false ");
        }
        try (PreparedStatement sqlConsulta = con.prepareStatement(sql.toString())) {
            sqlConsulta.setInt(1, pessoa);
            try (ResultSet resultado = sqlConsulta.executeQuery()) {
                while (resultado.next()) {
                    EmailVO novoObj = Email.montarDados(resultado, nivelMontarDados);
                    objetos.add(novoObj);
                }
            }
        }
        return objetos;
    }

    /**
     * Operação responsável por consultar todos os <code>EmailVO</code> relacionados a um objeto da classe <code>basico.Pessoa</code>.
     * @return List  Contendo todos os objetos da classe <code>EmailVO</code> resultantes da consulta.
     * @exception Exception  Erro de conexão com o BD ou restrição de acesso a esta operação.
     */
    public List consultarEmailExiste(String email, Boolean controleAcesso, int nivelMontarDados) throws Exception {
        consultar(getIdEntidade(), controleAcesso);
        EmailVO emailResultado = null;
        List listaEmails = new ArrayList();
        String sql = "SELECT email.email, pessoa.nome, pessoa.codigo " +
                "FROM email " +
                "         inner join pessoa on email.pessoa = pessoa.codigo " +
                "WHERE email.email ilike ?";
        try (PreparedStatement sqlConsulta = con.prepareStatement(sql)) {
            sqlConsulta.setString(1, email);
            try (ResultSet resultado = sqlConsulta.executeQuery()) {
                while (resultado.next()) {
                    emailResultado = new EmailVO();
                    emailResultado.setEmail(resultado.getString("email"));
                    emailResultado.getPessoaVO().setCodigo(resultado.getInt("codigo"));
                    emailResultado.getPessoaVO().setNome(resultado.getString("nome"));
                    listaEmails.add(emailResultado);
                }
            }
        }
        return listaEmails;
    }

    /**
     * Operação responsável por localizar um objeto da classe <code>EmailVO</code>
     * através de sua chave primária. 
     * @exception Exception Caso haja problemas de conexão ou localização do objeto procurado.
     */
    public EmailVO consultarPorChavePrimaria(Integer codigoPrm, int nivelMontarDados) throws Exception {
        consultar(getIdEntidade(), false);
        String sql = "select e.*, false bloqueioOptin  from Email e left join cliente c on e.pessoa  = c.pessoa \n" +
                "left join optin o2 on o2.cliente = c.codigo  WHERE  e.codigo = ?";
        try (PreparedStatement sqlConsultar = con.prepareStatement(sql)) {
            sqlConsultar.setInt(1, codigoPrm.intValue());
            try (ResultSet tabelaResultado = sqlConsultar.executeQuery()) {
                if (!tabelaResultado.next()) {
                    throw new ConsistirException("Dados Não Encontrados ( Email ).");
                }
                return (montarDados(tabelaResultado, nivelMontarDados));
            }
        }
    }

    

    /**
     * Operação responsável por montar os dados de um objeto da classe
     * <code>PessoaVO</code> relacionado ao objeto <code>ContratoVO</code>. Faz
     * uso da chave primária da classe <code>PessoaVO</code> para realizar a
     * consulta.
     *
     * @param obj
     *            Objeto no qual será montado os dados consultados.
     */
    public void montarDadosPessoa(EmailVO obj, int nivelMontarDados) throws Exception {
        Pessoa pessoaDAO;
        try {
            if (obj.getPessoaVO().getCodigo() == 0) {
                obj.setPessoaVO(new PessoaVO());
                return;
            }
            pessoaDAO = new Pessoa(con);
            obj.setPessoaVO(pessoaDAO.consultarPorChavePrimaria(obj.getPessoaVO().getCodigo(), nivelMontarDados));
        } finally {
            pessoaDAO = null;
        }
    }

    public List<EmailVO> obterEmailsEnvioAgendado(String sqlClientes) throws Exception {
        return montarDadosConsulta(
                criarConsulta("SELECT * ,o2.bloqueadoBounce bloqueioOptin , p.nome, cli.codigo as codigocliente FROM email "
                + " inner join pessoa p on email.pessoa = p.codigo "
                + " inner join cliente cli on cli.pessoa = p.codigo "
                        + " LEFT JOIN optin o2 ON o2.cliente = cli.codigo AND email.email = o2.email "
                + " Where emailcorrespondencia and email.pessoa IN(" + sqlClientes + ")  order by cli.situacao ", con), Uteis.NIVELMONTARDADOS_CLIENTES_MAILING);
    }

    public List<EmailVO> obterEmailsIndicados(String sql) throws Exception{
        List<EmailVO> emails = new ArrayList<EmailVO>();
        try (ResultSet rs = criarConsulta(sql, con)) {
            while (rs.next()) {
                EmailVO email = new EmailVO();
                email.setEmail(rs.getString("emails"));
                email.setCliente(rs.getInt("codigo"));
                emails.add(email);
            }
        }
        return emails;
    }
    
    public List<Integer> obterCodigosClientes(String sql) throws Exception{
        List<Integer> lista = new ArrayList<Integer>();
        try (ResultSet rs = criarConsulta(sql, con)) {
            while (rs.next()) {
                lista.add(rs.getInt("codcliente"));
            }
        }
        return lista;
    }

    public List<EmailVO> consultarEmailExistePassivoIndicado(String email, Boolean controleAcesso) throws Exception {
        consultar(getIdEntidade(), controleAcesso);
        List<EmailVO> listaEmails = new ArrayList<EmailVO>();
        StringBuilder sql = new StringBuilder();
        sql.append("select * from ( \n");
        sql.append("(select i.codigo, i.nomeindicado as nome, i.email, 'INDICAÇÃO' as origem from indicado i where i.cliente is null) \n");
        sql.append("UNION ALL \n");
        sql.append("(select p.codigo, p.nome, p.email, 'RECEPTIVO' as origem from passivo p where p.cliente is null)) as tabela \n");
        sql.append("WHERE email ilike '%").append(email).append("%'");

        try (PreparedStatement pst = con.prepareStatement(sql.toString())) {
            try (ResultSet resultado = pst.executeQuery()) {
                while (resultado.next()) {
                    EmailVO emailResultado = new EmailVO();
                    emailResultado.getPessoaVO().setNome(resultado.getString("nome"));
                    //SERA UTILIZANDO O EMAIL PARA ENVIAR O TIPO PASSIVO OU INDICADO
                    emailResultado.setEmail(resultado.getString("origem"));
                    listaEmails.add(emailResultado);
                }
            }
        }
        return listaEmails;
    }

    public void embaralharEmails() throws  Exception{
        String sql = "UPDATE email e  SET email =  ( SELECT lower(substring(p.nome from 0 for position(' ' IN p.nome))) || e.codigo ||'@teste.com' FROM pessoa p WHERE p.codigo = e.pessoa)";
        con.prepareStatement(sql).executeUpdate();
    }

    public boolean consultarEmailExiste(String email, Integer pessoa) throws Exception {
        if (UteisValidacao.notEmptyNumber(pessoa)) {
            StringBuilder sql = new StringBuilder();
            sql.append("SELECT codigo FROM email e\n");
            sql.append("WHERE e.email ILIKE '").append(email).append("'\n");
            sql.append("AND e.pessoa = ").append(pessoa);
            return existe(sql.toString(), getCon());
        } else {
            return false;
        }
    }

    public List<EmailVO> consultarEmailExiste(String email, Integer pessoa,
                                              Integer colaborador, int nivelMontarDados) throws Exception {
        StringBuilder sql = new StringBuilder();
        sql.append("select \n");
        sql.append("e.* \n");
        sql.append("from email e \n");
        sql.append("where e.email ilike '").append(email).append("' \n");
        if (!UteisValidacao.emptyNumber(pessoa)) {
            sql.append("and e.pessoa = ").append(pessoa).append(" \n");
        }
        if (!UteisValidacao.emptyNumber(colaborador)) {
            sql.append("and e.pessoa in (select pessoa from colaborador where codigo = ").append(colaborador).append(") \n");
        }
        sql.append("order by codigo \n");

        List<EmailVO> lista = new ArrayList<>();
        try (PreparedStatement sqlConsulta = con.prepareStatement(sql.toString())) {
            try (ResultSet resultado = sqlConsulta.executeQuery()) {
                while (resultado.next()) {
                    lista.add(montarDados(resultado, nivelMontarDados));
                }
            }
        }
        return lista;
    }
}



package negocio.facade.jdbc.basico.renovacaosintetico;

import negocio.facade.jdbc.arquitetura.consulta.ColunaPrefixavel;

/**
 * ColunaPrefixavel necessárias à busca de dados do relatório sintético de renovação
 *
 * <AUTHOR>
 * @since 07/08/2018
 */
public enum ColunasContrato implements ColunaPrefixavel {

    CODIGO("codigo"),
    EMPRESA("empresa"),
    PESSOA("pessoa"),
    CONSULTOR("consultor"),
    PLANO("plano"),
    SITUACAO("situacao"),
    NOME_MODALIDADES("nomeModalidades"),
    OBSERVACAO("observacao"),
    ESTENDE_COBERTURA_FAMILIARES("estendeCoberturaFamiliares"),
    VIGENCIA_DE("vigenciaDe"),
    VIGENCIA_ATE("vigenciaAte"),
    CONVENIO_DESCONTO("convenioDesconto"),
    VALOR_BASE_CALCULO("valorBaseCalculo"),
    VALOR_FINAL("valorFinal"),
    PAGAR_COM_BOLETO("pagarComBoleto"),
    RESPONSAVEL_CONTRATO("responsavelcontrato"),
    RESPONSAVEL_LIBERACAO_CONDICAO_PAGAMENTO("responsavelLiberacaoCondicaoPagamento"),
    DIVIDIR_PRODUTOS_NAS_PARCELAS("dividirProdutosNasParcelas"),
    DESCONTO("desconto"),
    TIPO_DESCONTO("tipoDesconto"),
    VALOR_DESCONTO_ESPECIFICO("valorDescontoEspecifico"),
    VALOR_DESCONTO_PORCENTAGEM("valorDescontoPorcentagem"),
    VIGENCIA_ATE_AJUSTADA("vigenciaAteAjustada"),
    DATA_LANCAMENTO("dataLancamento"),
    SITUACAO_CONTRATO("situacaoContrato"),
    DATA_PREVISTA_RENOVAR("dataPrevistaRenovar"),
    DATA_RENOVAR_REALIZADA("dataRenovarRealizada"),
    SITUACAO_RENOVACAO("situacaoRenovacao"),
    DATA_PREVISTA_REMATRICULA("dataPrevistaRematricula"),
    DATA_REMATRICULA_REALIZADA("dataRematriculaRealizada"),
    DATA_MATRICULA("dataMatricula"),
    SITUACAO_REMATRICULA("situacaoRematricula"),
    SOMA_PRODUTO("somaProduto"),
    CONTRATO_BASEADO_RENOVACAO("contratoBaseadoRenovacao"),
    CONTRATO_BASEADO_REMATRICULA("contratoBaseadoRematricula"),
    CONTRATO_RESPONSAVEL_RENOVACAO_MATRICULA("contratoResponsavelRenovacaoMatricula"),
    CONTRATO_RESPONSAVEL_REMATRICULA_MATRICULA("contratoResponsavelRematriculaMatricula"),
    BOLSA("bolsa"),
    DIA_VENCIMENTO_PRORATA("diavencimentoprorata"),
    NAO_PERMITIR_RENOVACAO_REMATRICULA_CONTRATOS_ANTERIORES("naoPermitirRenovacaoRematriculaDeContratoAnteriores"),
    DATA_ALTERACAO_MANUAL("dataAlteracaoManual"),
    RESPONSAVEL_DATABASE("responsavelDataBase"),
    REGIME_RECORRENCIA("regimeRecorrencia"),
    ORIGEM_CONTRATO("origemcontrato"),
    RENOVAVEL_AUTOMATICAMENTE("renovavelautomaticamente"),
    QUANTIDADE_MAXIMA_FREQUENCIA("quantidadeMaximaFrequencia"),
    VENDA_CREDITO_TREINO("vendaCreditoTreino"),
    NUMERO_CUPOM_DESCONTO("numeroCupomDesconto"),
//    AGENDADO_CONVITE_AULA_EXPERIMENTAL("agendadoConviteAulaExperimental"),
    PERMITE_RENOVACAO_AUTOMATICA("permiterenovacaoautomatica"),
    GRUPO("grupo"),
    VALOR_ARREDONDAMENTO("valorarredondamento"),
    IMPORTACAO("importacao"),
    CROSSFIT("crossfit"),
    NOME_CONVENIO_DESCONTO("nomeConvenioDesconto"),
    VALOR_CONVENIO_DESCONTO("valorConvenioDesconto"),
    PERCENTUAL_CONVENIO_DESCONTO("percentualConvenioDesconto");

    private static final String CONTRATO_PREFIXO = "contrato";
    private static final String NOME_TABELA = "Contrato";
    private String label;

    ColunasContrato(String label) {
        this.label = label;
    }

    /**
     * @return O prefixo dessas colunas
     */
    public static String getPrefixo() {
        return CONTRATO_PREFIXO;
    }

    public static String getNomeTabela() {
        return NOME_TABELA;
    }

    public static String getNomeTabelaComPrefixo() {
        return getNomeTabela() + " " + getPrefixo();
    }

    @Override
    public String getLabel() {
        return label;
    }

    @Override
    public String getLabelComPrefixo() {
        return CONTRATO_PREFIXO + "." + label;
    }

}

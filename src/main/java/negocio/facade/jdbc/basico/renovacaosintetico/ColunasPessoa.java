package negocio.facade.jdbc.basico.renovacaosintetico;

import negocio.facade.jdbc.arquitetura.consulta.ColunaPrefixavel;

/**
 * <AUTHOR>
 * @since 07/08/2018
 */
enum ColunasPessoa implements ColunaPrefixavel {

    WEBPAGE("webpage"),
    GRAU_INSTRUCAO("grauinstrucao"),
    SEXO("sexo"),
    NATURALIDADE("naturalidade"),
    NACIONALIDADE("nacionalidade"),
    ESTADO_CIVIL("estadocivil"),
    PAIS("pais"),
    ESTADO("estado"),
    CIDADE("cidade"),
    RG_UF("rguf"),
    RG_ORGAO("rgorgao"),
    RG("rg"),
    CFP("cfp"),
    NOME_MAE("nomemae"),
    NOME_PAI("nomepai"),
    DATA_NASC("datanasc"),
    NOME("nome"),
    DATA_CADASTRO("datacadastro"),
    PROFISSAO("profissao"),
    CODIGO("codigo"),
    SENHA_ACESSO("senhaacesso"),
    LIBERA_SENHA_ACESSO("liberaSenhaAcesso"),
    ID_EXTERNO("idexterno"),
    FOTO_KEY("fotokey"),
    FOTO("foto"),
    TIPO_PESSOA("tipoPessoa"),
    CNPJ("cnpj"),
    INSC_ESTADUAL("inscEstadual"),
    CFDF("cfdf"),
    INSC_MUNICIPAL("inscMunicipal"),
    ID_VINDI("idvindi"),
    DATA_ALTERACAO_VINDI("dataAlteracaoVindi"),
    EMITIR_NOTA_NOME_MAE("emitirNotaNomeMae"),
    ID_MAXI_PAGO("idMaxiPago"),
    DATA_ALTERACAO_MAXI_PAGO("dataAlteracaoMaxiPago");

    private static final String PREFIXO = "pessoa";
    private String label;

    ColunasPessoa(String label) {
        this.label = label;
    }

    /**
     * @return O prefixo dessas colunas
     */
    public static String getPrefixo() {
        return PREFIXO;
    }

    @Override
    public String getLabel() {
        return label;
    }

    @Override
    public String getLabelComPrefixo() {
        return PREFIXO + "." + label;
    }

}

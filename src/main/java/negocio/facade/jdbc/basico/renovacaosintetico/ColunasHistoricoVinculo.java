package negocio.facade.jdbc.basico.renovacaosintetico;

import negocio.facade.jdbc.arquitetura.consulta.ColunaPrefixavel;

/**
 * <AUTHOR>
 * @since 08/08/2018
 */
enum ColunasHistoricoVinculo implements ColunaPrefixavel {

    CODIGO("codigo"),
    CLIENTE("cliente"),
    DATA_REGISTRO("dataRegistro"),
    TIPO_HISTORICO_VINCULO("tipoHistoricoVinculo"),
    COLABORADOR("colaborador"),
    TIPO_COLABORADOR("tipoColaborador"),
    ORIGEM("origem"),
    USUARIO_RESPONSAVEL("usuarioresponsavel");

    private static final String PREFIXO = "historicovinculo";
    private String label;

    ColunasHistoricoVinculo(String label) {
        this.label = label;
    }

    /**
     * @return O prefixo dessas colunas
     */
    public static String getPrefixo() {
        return PREFIXO;
    }

    @Override
    public String getLabel() {
        return label;
    }

    @Override
    public String getLabelComPrefixo() {
        return PREFIXO + "." + label;
    }

}

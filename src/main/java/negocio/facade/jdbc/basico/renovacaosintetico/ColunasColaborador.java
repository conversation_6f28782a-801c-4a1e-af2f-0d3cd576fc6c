package negocio.facade.jdbc.basico.renovacaosintetico;

import negocio.facade.jdbc.arquitetura.consulta.ColunaPrefixavel;

/**
 * <AUTHOR>
 * @since 08/08/2018
 */
enum ColunasColaborador implements ColunaPrefixavel {

    CODIGO("codigo"),
    PESSOA("pessoa"),
    EMPRESA("empresa"),
    COD_ACESSO("codAcesso"),
    SITUACAO("situacao"),
    COD_ACESSO_ALTERNATIVO("codAcessoAlternativo"),
    FUNCIONARIO("funcionario"),
    UA_CODIGO("uaCodigo"),
    DIA_VENCIMENTO("diavencimento"),
    PRODUTO_DEFAULT("produtoDefault"),
    PORC_COMISSAO("porcComissao"),
    COR_AGENDA_PROFISSIONAL("corAgendaProfissional"),
    CREF("cref"),
//    ACESSO_AUTORIZADO_ATE("acessoAutorizadoAte"),
    TOKEN_GOOGLE("tokengoogle"),
//    TIPO_COLABORADOR("tipocolaborador"),
    USO_CREDITOS_PERSONAL("usoCreditosPersonal"),
    SALDO_CREDITO_PERSONAL("saldoCreditoPersonal"),
    EM_ATENDIMENTO_PERSONAL("emAtendimentoPersonal"),
    TEMPO_ENTRE_ACESSOS("tempoentreacessos"),
    DEPARTAMENTO("departamento");

    private static final String PREFIXO = "colaborador";
    private String label;

    ColunasColaborador(String label) {
        this.label = label;
    }

    /**
     * @return O prefixo dessas colunas
     */
    public static String getPrefixo() {
        return PREFIXO;
    }

    @Override
    public String getLabel() {
        return label;
    }

    @Override
    public String getLabelComPrefixo() {
        return PREFIXO + "." + label;
    }

}

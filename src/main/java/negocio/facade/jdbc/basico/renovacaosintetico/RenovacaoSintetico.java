package negocio.facade.jdbc.basico.renovacaosintetico;

import br.com.pactosolucoes.enumeradores.TipoContratoEnum;
import negocio.comuns.acesso.AcessoClienteVO;
import negocio.comuns.acesso.AcessoColaboradorVO;
import negocio.comuns.arquitetura.UsuarioVO;
import negocio.comuns.basico.ClienteVO;
import negocio.comuns.basico.ColaboradorVO;
import negocio.comuns.basico.HistoricoVinculoVO;
import negocio.comuns.basico.PessoaVO;
import negocio.comuns.basico.enumerador.TipoPessoa;
import negocio.comuns.contrato.ContratoDuracaoVO;
import negocio.comuns.contrato.ContratoVO;
import negocio.comuns.plano.PlanoVO;
import negocio.comuns.utilitarias.Calendario;
import negocio.comuns.utilitarias.Uteis;
import negocio.facade.jdbc.arquitetura.consulta.ConsultaFacade;
import negocio.interfaces.basico.RenovacaoSinteticoFacade;
import org.apache.commons.lang.StringUtils;
import relatorio.negocio.comuns.sad.RenovacaoContratoSinteticoTO;

import java.sql.ResultSet;
import java.sql.SQLException;
import java.sql.Statement;
import java.util.*;

import static negocio.facade.jdbc.arquitetura.consulta.ConsultaUtil.*;

/**
 * Implementação básica de um relatório sintético de renovação
 *
 * <AUTHOR> Karlus
 * @since 06/08/2018
 */
public class RenovacaoSintetico extends ConsultaFacade implements RenovacaoSinteticoFacade {

    private static final String SELECT_CONTRATOS;
    private static final String SELECT_HISTORICOS_VINCULO;

    static {
        SELECT_CONTRATOS = new StringBuilder(SELECT)
                .append(getColunasPrefixadas(ColunasContrato.values())).append(",")
                .append(getColunasPrefixadas(ColunasContratoDuracao.values())).append(",")
                .append(getColunasPrefixadas(ColunasPessoa.values())).append(",")
                .append(getColunasPrefixadas(ColunasCliente.values())).append(",")
                .append(getColunasPrefixadas(ColunasPlano.values())).append(",")
                .append("ARRAY_TO_STRING((SELECT ARRAY_AGG(email.email) FROM email WHERE email.pessoa = pessoa.codigo), ', ') as \"emails\",\n")
                .append("ARRAY_TO_STRING((SELECT ARRAY_AGG(mo.nome ORDER BY mo.nome) FROM modalidade mo INNER JOIN contratomodalidade cm ON cm.modalidade = mo.codigo WHERE cm.contrato = contrato.codigo), ', ') as nomeModalidadesConcatenado,\n")
                .append("((case when (diasacessosemanapassada < 0) then 0 else diasacessosemanapassada end)  + (case when diasacessosemana2 < 0 then 0 else diasacessosemana2 end)  + (case when (diasacessosemana3 < 0) then 0 else diasacessosemana3 end) + (case when  (diasacessosemana4 < 0) then 0 else diasacessosemana4 end))::float / 4 as mediaAcesso4semanas,\n" )
                .append(" (('%s' :: DATE) - ((COALESCE(ac.dthrentrada, contrato.vigenciaDe) :: DATE)+1))  as diassemacesso, \n")
                .append(" (select max(dia) from historicocontato  where cliente =cliente.codigo) as ultimocontatoCrm, \n")
                .append(" (select count(codigo) from alunohorarioturma  where cliente =  cliente.codigo and dia > '%s'::date - interval '28 days') as checkin4semanas,\n")
                .append(" (select replace(replace(replace(array_agg(distinct pescol.nome)::text, '\"', ''), '{','') , '}','') from vinculo vin inner join colaborador col on col.codigo = vin.colaborador inner join pessoa pescol on pescol.codigo = col.pessoa where vin.cliente = cliente.codigo and tipovinculo in ('PR', 'TW') ) as professores\n")
                .append(" FROM Contrato ").append(ColunasContrato.getPrefixo())
                .append(innerJoin("ContratoDuracao", ColunasContratoDuracao.getPrefixo(), ColunasContratoDuracao.CONTRATO, ColunasContrato.CODIGO))
                .append(innerJoin("public.Pessoa", ColunasPessoa.getPrefixo(), ColunasPessoa.CODIGO, ColunasContrato.PESSOA))
                .append(innerJoin("Cliente", ColunasCliente.getPrefixo(), ColunasCliente.PESSOA, ColunasContrato.PESSOA))
                .append(innerJoin("Plano", ColunasPlano.getPrefixo(), ColunasPlano.CODIGO, ColunasContrato.PLANO))
                .append(" INNER JOIN situacaoclientesinteticodw situacaosintetico on situacaosintetico.codigopessoa = pessoa.codigo \n ")
                .append(" LEFT JOIN acessocliente ac ON cliente.uacodigo = ac.codigo \n ")
                .toString();

        SELECT_HISTORICOS_VINCULO = new StringBuilder(SELECT)
                .append(getColunasPrefixadas(ColunasHistoricoVinculo.values())).append(",")
                .append(getColunasPrefixadas(ColunasColaborador.values())).append(",")
                .append(getColunasPrefixadas(ColunasPessoa.values()))
                .append(" FROM HistoricoVinculo ").append(ColunasHistoricoVinculo.getPrefixo())
                .append(innerJoin("Colaborador", ColunasColaborador.getPrefixo(), ColunasColaborador.CODIGO, ColunasHistoricoVinculo.COLABORADOR))
                .append(innerJoin("Pessoa", ColunasPessoa.getPrefixo(), ColunasColaborador.PESSOA, ColunasPessoa.CODIGO))
                .toString();
    }

    public RenovacaoSintetico() throws Exception {
        super();
    }

    @Override
    public List<RenovacaoContratoSinteticoTO> consultarRenovacaoContratosSinteticos(Date dataInicial, Date dataFinal,
                                                                                    Integer codigoEmpresa, Boolean isBolsa,
                                                                                    Boolean isTrancado, Boolean isCancelado) throws Exception {
        Statement stm = null;
        ResultSet tabelaResultadoContratos = null;
        ResultSet tabelaResultadoVinculos = null;

        try {
            stm = con.createStatement();

            final String SQL_CONTRATOS = montarSQLRenovacaoContratosSinteticos(dataInicial, dataFinal, codigoEmpresa, isBolsa, isTrancado, isCancelado);
            tabelaResultadoContratos = stm.executeQuery(SQL_CONTRATOS);
            List<RenovacaoContratoSinteticoTO> renovacaoContratoSinteticoTOS = montarDadosRelatorio(tabelaResultadoContratos);

            if(renovacaoContratoSinteticoTOS.size() == 0){
                return new ArrayList<RenovacaoContratoSinteticoTO>();
            }

            final String SQL_VINCULOS = montarSQLHistoricosVinculo(dataFinal, getCodigosCliente(renovacaoContratoSinteticoTOS));
            tabelaResultadoVinculos = stm.executeQuery(SQL_VINCULOS);
            Map<Integer, List<HistoricoVinculoVO>> historicosAgrupadosCodigoCliente =
                    montarVinculosAgrupadosCodigoCliente(tabelaResultadoVinculos, getRenovacaoContratoSinteticosVOsAgrupadosCodCliente(renovacaoContratoSinteticoTOS));

            for (RenovacaoContratoSinteticoTO renovacaoContratoSinteticoTO : renovacaoContratoSinteticoTOS) {

                List<HistoricoVinculoVO> historicoVinculoVOList = historicosAgrupadosCodigoCliente.get(renovacaoContratoSinteticoTO.getContratoVO().getCliente().getCodigo());
                if(historicoVinculoVOList != null) {
                    renovacaoContratoSinteticoTO.setHistoricosVinculoVO(organizarHistoricosVinculos(historicoVinculoVOList, dataFinal));
                }else{
                    ClienteVO cl = renovacaoContratoSinteticoTO.getContratoVO().getCliente();
                    throw new Exception(String.format("Cliente %s(mat. %s) está sem vínculo, por favor corriga o(s) cliente(s) sem vínculo para prosseguir com esta operação", cl.getNome_Apresentar(), cl.getMatricula()));
                }
            }

            return renovacaoContratoSinteticoTOS;

        } finally {
            fecharResultSetQuietly(tabelaResultadoContratos);
            fecharResultSetQuietly(tabelaResultadoVinculos);
            fecharStatementQuietly(stm);
        }
    }

    private List<HistoricoVinculoVO> organizarHistoricosVinculos(List<HistoricoVinculoVO> historicoVinculoVOS, Date dataFinal) {

        Map<String, HistoricoVinculoVO> historicosVinculoAgrupados = new HashMap<String, HistoricoVinculoVO>();
        String identificador;

        for (HistoricoVinculoVO his : historicoVinculoVOS) {
            identificador = his.getColaborador().getCodigo().toString() + his.getTipoColaborador();
            if (his.getTipoHistoricoVinculo().equals("EN")) {
                historicosVinculoAgrupados.put(identificador, his);
            } else if (Calendario.menor(his.getDataRegistro(), dataFinal)) {
                historicosVinculoAgrupados.remove(identificador);
            }
        }

        historicoVinculoVOS.clear();
        Set<String> codigosAtuais = historicosVinculoAgrupados.keySet();
        for (String str : codigosAtuais) {
            historicoVinculoVOS.add(historicosVinculoAgrupados.get(str));
        }

        return historicoVinculoVOS;
    }

    private String getCodigosCliente(List<RenovacaoContratoSinteticoTO> renovacaoContratoSinteticoTOS) {
        Set<Integer> codigosClientes = new HashSet<Integer>();
        for (RenovacaoContratoSinteticoTO renovacaoContratoSinteticoTO : renovacaoContratoSinteticoTOS) {
            codigosClientes.add(renovacaoContratoSinteticoTO.getContratoVO().getCliente().getCodigo());
        }

        return StringUtils.join(codigosClientes, ",");
    }

    private Map<Integer, RenovacaoContratoSinteticoTO> getRenovacaoContratoSinteticosVOsAgrupadosCodCliente(
            List<RenovacaoContratoSinteticoTO> renovacaoContratoSinteticoTOS) {

        Map<Integer, RenovacaoContratoSinteticoTO> renovacoesAgrupadasCodCliente = new HashMap<Integer, RenovacaoContratoSinteticoTO>();
        for (RenovacaoContratoSinteticoTO renovacaoContratoSinteticoTO : renovacaoContratoSinteticoTOS) {
            renovacoesAgrupadasCodCliente.put(renovacaoContratoSinteticoTO.getContratoVO().getCliente().getCodigo(), renovacaoContratoSinteticoTO);
        }
        return renovacoesAgrupadasCodCliente;
    }

    private String montarSQLRenovacaoContratosSinteticos(Date dataInicial, Date dataFinal, Integer codigoEmpresa,
                                                         Boolean isBolsa, Boolean isTrancado, Boolean isCancelado) throws Exception {

        StringBuilder sql = new StringBuilder(String.format(SELECT_CONTRATOS, new Object[]{
                Calendario.hoje(),
                Calendario.hoje()
        }))
                .append(WHERE)
                .append(dataBetween(ColunasContrato.DATA_PREVISTA_RENOVAR.getLabelComPrefixo(), dataInicial, dataFinal));

        if(codigoEmpresa > 0){
            sql.append(andIgual(ColunasContrato.EMPRESA, codigoEmpresa));
        }

        if (!isTrancado) {
            sql.append(AND).append(ColunasContrato.SITUACAO.getLabelComPrefixo()).append(" <> 'TR' ");
        }

        if (!isCancelado) {
            sql
                    .append(AND).append(ColunasContrato.SITUACAO.getLabelComPrefixo()).append(" <> 'CA' ")
                    .append(" AND NOT EXISTS (SELECT contratooperacao.codigo FROM contratooperacao contratooperacao")
                    .append(" WHERE contratooperacao.contrato = ").append(ColunasContrato.CODIGO.getLabelComPrefixo())
                    .append(" AND contratooperacao.tipooperacao = 'CA') ");
        }

        if (!isBolsa) {
            sql.append(AND).append(ColunasContrato.BOLSA.getLabelComPrefixo()).append(" = false");
        }

        return sql.toString();
    }

    private String montarSQLHistoricosVinculo(Date dataFinal, String codigosClienteSeparadosVirgula) throws Exception {

        return new StringBuilder(SELECT_HISTORICOS_VINCULO)

                .append(WHERE).append(ColunasHistoricoVinculo.CLIENTE.getLabelComPrefixo()).append(IN).append(codigosClienteSeparadosVirgula).append(IN_END)
                .append(AND).append(ColunasHistoricoVinculo.DATA_REGISTRO.getLabelComPrefixo()).append(" <= '").append(Uteis.getDataJDBC(dataFinal)).append(" 23:59:59'")

                .append(ORDER_BY).append(ColunasHistoricoVinculo.DATA_REGISTRO.getLabelComPrefixo())

                .toString();
    }

    private Map<Integer, List<HistoricoVinculoVO>> montarVinculosAgrupadosCodigoCliente(ResultSet dataSet,
                                                                                        Map<Integer, RenovacaoContratoSinteticoTO> renovacaoContratoSinteticoTOsAgrupadosCodigoCliente) throws SQLException {

        Map<Integer, List<HistoricoVinculoVO>> historicosAgrupadosCodCliente = new HashMap<Integer, List<HistoricoVinculoVO>>();

        while (dataSet.next()) {
            ClienteVO clienteVO = renovacaoContratoSinteticoTOsAgrupadosCodigoCliente.get(
                    dataSet.getInt(ColunasHistoricoVinculo.CLIENTE.getLabelComPrefixo())).getContratoVO().getCliente();

            ColaboradorVO colaboradorVO = getColaboradorVO(dataSet);
            colaboradorVO.setPessoa(getPessoaVO(dataSet, false));

            HistoricoVinculoVO historicoVinculoVO = getHistoricoVinculoVO(dataSet);
            historicoVinculoVO.setColaborador(colaboradorVO);

            List<HistoricoVinculoVO> historicos = historicosAgrupadosCodCliente.get(clienteVO.getCodigo());
            if (historicos == null) {
                historicos = new ArrayList<HistoricoVinculoVO>();
            }
            historicos.add(historicoVinculoVO);
            historicosAgrupadosCodCliente.put(clienteVO.getCodigo(), historicos);
        }

        return historicosAgrupadosCodCliente;
    }

    private ColaboradorVO getColaboradorVO(ResultSet dataSet) throws SQLException {
        ColaboradorVO colaboradorVO = new ColaboradorVO();
        colaboradorVO.setNovoObj(false);
        colaboradorVO.setCodigo(dataSet.getInt(ColunasColaborador.CODIGO.getLabelComPrefixo()));
        colaboradorVO.getPessoa().setCodigo(dataSet.getInt(ColunasColaborador.PESSOA.getLabelComPrefixo()));
        colaboradorVO.getEmpresa().setCodigo(dataSet.getInt(ColunasColaborador.EMPRESA.getLabelComPrefixo()));
        colaboradorVO.setCodAcesso(dataSet.getString(ColunasColaborador.COD_ACESSO.getLabelComPrefixo()));
        colaboradorVO.setCodAcessoAlternativo(dataSet.getString(ColunasColaborador.COD_ACESSO_ALTERNATIVO.getLabelComPrefixo()));
        colaboradorVO.setSituacao(dataSet.getString(ColunasColaborador.SITUACAO.getLabelComPrefixo()));
        colaboradorVO.setFuncionario(dataSet.getBoolean(ColunasColaborador.FUNCIONARIO.getLabelComPrefixo()));
        colaboradorVO.setUaColaborador(new AcessoColaboradorVO());
        colaboradorVO.getUaColaborador().setCodigo(dataSet.getInt(ColunasColaborador.UA_CODIGO.getLabelComPrefixo()));
        colaboradorVO.setDiaVencimento(dataSet.getInt(ColunasColaborador.DIA_VENCIMENTO.getLabelComPrefixo()));
        colaboradorVO.getProdutoDefault().setCodigo(dataSet.getInt(ColunasColaborador.PRODUTO_DEFAULT.getLabelComPrefixo()));
        colaboradorVO.setPorcComissao(dataSet.getDouble(ColunasColaborador.PORC_COMISSAO.getLabelComPrefixo()));
        colaboradorVO.setCorAgendaProfissional(dataSet.getString(ColunasColaborador.COR_AGENDA_PROFISSIONAL.getLabelComPrefixo()));
        colaboradorVO.setCref(dataSet.getString(ColunasColaborador.CREF.getLabelComPrefixo()));
        try {
//            colaboradorVO.setAcessoAutorizadoAte(dataSet.getTimestamp(ColunasColaborador.ACESSO_AUTORIZADO_ATE.getLabelComPrefixo()));
            colaboradorVO.setTokenGoogle(dataSet.getString(ColunasColaborador.TOKEN_GOOGLE.getLabelComPrefixo()));
//            colaboradorVO.setTipoColaborador(dataSet.getString(ColunasColaborador.TIPO_COLABORADOR.getLabelComPrefixo()));
            colaboradorVO.setUsoCreditosPersonal(dataSet.getInt(ColunasColaborador.USO_CREDITOS_PERSONAL.getLabelComPrefixo()));
            colaboradorVO.setSaldoCreditoPersonal(dataSet.getInt(ColunasColaborador.SALDO_CREDITO_PERSONAL.getLabelComPrefixo()));
            colaboradorVO.setEmAtendimentoPersonal(dataSet.getBoolean(ColunasColaborador.EM_ATENDIMENTO_PERSONAL.getLabelComPrefixo()));
            colaboradorVO.setTempoEntreAcessos(dataSet.getInt(ColunasColaborador.TEMPO_ENTRE_ACESSOS.getLabelComPrefixo()));
            colaboradorVO.getDepartamentoVO().setCodigo(dataSet.getInt(ColunasColaborador.DEPARTAMENTO.getLabelComPrefixo()));
        } catch (Exception ignored) {
        }

        return colaboradorVO;
    }

    private HistoricoVinculoVO getHistoricoVinculoVO(ResultSet dataSetVinculos) throws SQLException {
        HistoricoVinculoVO historicoVinculoVO = new HistoricoVinculoVO();
        historicoVinculoVO.setCodigo(dataSetVinculos.getInt(ColunasHistoricoVinculo.CODIGO.getLabelComPrefixo()));
        historicoVinculoVO.getCliente().setCodigo(dataSetVinculos.getInt(ColunasHistoricoVinculo.CLIENTE.getLabelComPrefixo()));
        historicoVinculoVO.setDataRegistro(dataSetVinculos.getTimestamp(ColunasHistoricoVinculo.DATA_REGISTRO.getLabelComPrefixo()));
        historicoVinculoVO.setTipoHistoricoVinculo(dataSetVinculos.getString(ColunasHistoricoVinculo.TIPO_HISTORICO_VINCULO.getLabelComPrefixo()));
        historicoVinculoVO.getColaborador().setCodigo(dataSetVinculos.getInt(ColunasHistoricoVinculo.COLABORADOR.getLabelComPrefixo()));
        historicoVinculoVO.setTipoColaborador(dataSetVinculos.getString(ColunasHistoricoVinculo.TIPO_COLABORADOR.getLabelComPrefixo()));
        historicoVinculoVO.setOrigem(dataSetVinculos.getString(ColunasHistoricoVinculo.ORIGEM.getLabelComPrefixo()));

        try {
            if (dataSetVinculos.getInt(ColunasHistoricoVinculo.USUARIO_RESPONSAVEL.getLabelComPrefixo()) != 0) {
                historicoVinculoVO.setUsuarioVO(new UsuarioVO());
                historicoVinculoVO.getUsuarioVO().setCodigo(dataSetVinculos.getInt(ColunasHistoricoVinculo.USUARIO_RESPONSAVEL.getLabelComPrefixo()));
            }
        } catch (Exception e) {
            // excecao ignorada
        }

        historicoVinculoVO.setNovoObj(false);
        return historicoVinculoVO;
    }

    private List<RenovacaoContratoSinteticoTO> montarDadosRelatorio(ResultSet dataSet) throws Exception {
        final List<RenovacaoContratoSinteticoTO> renovacaoContratoSinteticoTOS = new ArrayList<RenovacaoContratoSinteticoTO>();

        while (dataSet.next()) {

            ContratoDuracaoVO contratoDuracaoVO = getContratoDuracaoVO(dataSet);
            PessoaVO pessoaVO = getPessoaVO(dataSet, true);

            ClienteVO clienteVO = getClienteVO(dataSet);
            clienteVO.setPessoa(pessoaVO);

            PlanoVO planoVO = getPlanoVO(dataSet);

            ContratoVO contratoVO = getContratoVO(dataSet);
            contratoVO.setContratoDuracao(contratoDuracaoVO);
            contratoVO.setPessoa(pessoaVO);
            contratoVO.setCliente(clienteVO);
            contratoVO.setPlano(planoVO);
            contratoVO.setNomeModalidadesConcatenado(dataSet.getString("nomeModalidadesConcatenado"));

            RenovacaoContratoSinteticoTO renovacaoContratoSinteticoTO = new RenovacaoContratoSinteticoTO();
            renovacaoContratoSinteticoTO.setContratoVO(contratoVO);
            renovacaoContratoSinteticoTO.setCheckin4semanas(dataSet.getInt("checkin4semanas"));
            renovacaoContratoSinteticoTO.setDiasSemAcesso(dataSet.getInt("diassemacesso"));
            renovacaoContratoSinteticoTO.setMediaAcessos4semanas(dataSet.getDouble("mediaAcesso4semanas"));
            renovacaoContratoSinteticoTO.setUltimoContatoCRM(dataSet.getTimestamp("ultimocontatoCrm"));
            renovacaoContratoSinteticoTO.setProfessores(dataSet.getString("professores"));
            renovacaoContratoSinteticoTOS.add(renovacaoContratoSinteticoTO);
        }

        return renovacaoContratoSinteticoTOS;
    }

    private PlanoVO getPlanoVO(ResultSet dataSet) throws SQLException {
        PlanoVO plano = new PlanoVO();
        plano.setNovoObj(false);
        plano.setCodigo(dataSet.getInt(ColunasPlano.CODIGO.getLabelComPrefixo()));
        plano.setDescricao(dataSet.getString(ColunasPlano.DESCRICAO.getLabelComPrefixo()));
        plano.getEmpresa().setCodigo(dataSet.getInt(ColunasPlano.EMPRESA.getLabelComPrefixo()));
        plano.setVigenciaDe(dataSet.getDate(ColunasPlano.VIGENCIA_DE.getLabelComPrefixo()));
        plano.setVigenciaAte(dataSet.getDate(ColunasPlano.VIGENCIA_ATE.getLabelComPrefixo()));
        plano.setIngressoAte(dataSet.getDate(ColunasPlano.INGRESSO_ATE.getLabelComPrefixo()));
        plano.setBolsa(dataSet.getBoolean(ColunasPlano.BOLSA.getLabelComPrefixo()));
        plano.setPermitePagarComBoleto(dataSet.getBoolean(ColunasPlano.PERMITE_PAGAR_COM_BOLETO.getLabelComPrefixo()));
        plano.getProdutoPadraoGerarParcelasContrato().setCodigo(dataSet.getInt(ColunasPlano.PRODUTO_PADRAO_GERAR_PARCELAS_CONTRATO.getLabelComPrefixo()));
        plano.getProdutoTaxaCancelamento().setCodigo(dataSet.getInt(ColunasPlano.PRODUTO_TAXA_CANCELAMENTO.getLabelComPrefixo()));
        plano.setPercentualMultaCancelamento(dataSet.getDouble(ColunasPlano.PERCENTUAL_MULTA_CANCELAMENTO.getLabelComPrefixo()));
        plano.getPlanoTextoPadrao().setCodigo(dataSet.getInt(ColunasPlano.PLANO_TEXTO_PADRAO.getLabelComPrefixo()));
        plano.getReciboTextoPadrao().setCodigo(dataSet.getInt(ColunasPlano.RECIBO_TEXTO_PADRAO.getLabelComPrefixo()));
        plano.setProrataObrigatorio(dataSet.getBoolean(ColunasPlano.PRORATA_OBRIGATORIO.getLabelComPrefixo()));
        plano.setDiasVencimentoProrata(dataSet.getString(ColunasPlano.DIAS_VENCIMENTO_PRORATA.getLabelComPrefixo()));
        plano.setListaDiasVencimento(dataSet.getString(ColunasPlano.DIAS_VENCIMENTO_PRORATA.getLabelComPrefixo()));
        plano.getDescontoAntecipado().setCodigo(dataSet.getInt(ColunasPlano.DESCONTO_ANTECIPADO.getLabelComPrefixo()));
        plano.setRegimeRecorrencia(dataSet.getBoolean(ColunasPlano.RECORRENCIA.getLabelComPrefixo()));
        plano.setCorrespondenciaZD(dataSet.getString(ColunasPlano.CORRESPONDENCIA_ZD.getLabelComPrefixo()));
        plano.setComissao(dataSet.getBoolean(ColunasPlano.COMISSAO.getLabelComPrefixo()));
        plano.setDescricaoEncantamento(dataSet.getString(ColunasPlano.DESCRICAO_ENCANTAMENTO.getLabelComPrefixo()));
        plano.setQuantidadeMaximaFrequencia(dataSet.getInt(ColunasPlano.QUANTIDADE_MAXIMA_FREQUENCIA.getLabelComPrefixo()));
        plano.setTipoFrequencia(dataSet.getInt(ColunasPlano.TIPO_FREQUENCIA.getLabelComPrefixo()));
        plano.setVendaCreditoTreino(dataSet.getBoolean(ColunasPlano.VENDA_CREDITO_TREINO.getLabelComPrefixo()));
        plano.setDiaDoMesDescontoBoletoPagAntecipado(dataSet.getInt(ColunasPlano.DIA_DO_MES_DESCONTO_BOLETO_PAG_ANTECIPADO.getLabelComPrefixo()));
        plano.setPorcentagemDescontoBoletoPagAntecipado(dataSet.getDouble(ColunasPlano.PORCENTAGEM_DESCONTO_BOLETO_PAG_ANTECIPADO.getLabelComPrefixo()));

        try {
            plano.setCobrarAdesaoSeparada(dataSet.getBoolean(ColunasPlano.COBRAR_ADESAO_SEPARADA.getLabelComPrefixo()));
            plano.setNrVezesParcelarAdesao(dataSet.getInt(ColunasPlano.NR_VEZES_PARCELAR_ADESAO.getLabelComPrefixo()));
            plano.setCobrarProdutoSeparado(dataSet.getBoolean(ColunasPlano.COBRAR_PRODUTO_SEPARADO.getLabelComPrefixo()));
            plano.setNrVezesParcelarProduto(dataSet.getInt(ColunasPlano.NR_VEZES_PARCELAR_PRODUTO.getLabelComPrefixo()));

            plano.setSite(dataSet.getBoolean(ColunasPlano.SITE.getLabelComPrefixo()));
            plano.setRenovavelAutomaticamente(dataSet.getBoolean(ColunasPlano.RENOVAVEL_AUTOMATICAMENTE.getLabelComPrefixo()));
            plano.setInicioMinimoContrato(dataSet.getDate(ColunasPlano.INICIO_MINIMO_CONTRATO.getLabelComPrefixo()));
            plano.setParcelamentoOperadora(dataSet.getBoolean(ColunasPlano.PARCELAMENTO_OPERADORA.getLabelComPrefixo()));
            plano.setRenovarProdutoObrigatorio(dataSet.getBoolean(ColunasPlano.RENOVAR_PRODUTO_OBRIGATORIO.getLabelComPrefixo()));
            plano.setRenovarAnuidadeAutomaticamente(dataSet.getBoolean(ColunasPlano.RENOVAR_ANUIDADE_AUTOMATICAMENTE.getLabelComPrefixo()));
            plano.setPontos(dataSet.getInt(ColunasPlano.PONTOS.getLabelComPrefixo()));
            plano.getTermoAceite().setCodigo(dataSet.getInt(ColunasPlano.TERMO_ACEITE.getLabelComPrefixo()));
            plano.setTotem(dataSet.getBoolean(ColunasPlano.TOTEM.getLabelComPrefixo()));
            plano.getConvenioCobrancaPrivateLabel().setCodigo(dataSet.getInt(ColunasPlano.CONVENIO_COBRANCA_PRIVATELABEL.getLabelComPrefixo()));
            plano.setCreditoTreinoNaoCumulativo(dataSet.getBoolean(ColunasPlano.CREDITO_TREINO_NAO_CUMULATIVO.getLabelComPrefixo()));
            plano.setRenovarAutomaticamenteUtilizandoValorBaseContrato(dataSet.getBoolean(ColunasPlano.RENOVAR_AUTO_UTILIZANDO_VALOR_BASE_CONTRATO.getLabelComPrefixo()));
        } catch (Exception ignored) {
        }

        return plano;
    }

    private ClienteVO getClienteVO(ResultSet dataSet) throws SQLException {
        ClienteVO clienteVO = new ClienteVO();
        clienteVO.setNovoObj(false);
        clienteVO.setCodigo(dataSet.getInt(ColunasCliente.CODIGO.getLabelComPrefixo()));
        clienteVO.getPessoa().setCodigo(dataSet.getInt(ColunasCliente.PESSOA.getLabelComPrefixo()));
        clienteVO.setSituacao(dataSet.getString(ColunasCliente.SITUACAO.getLabelComPrefixo()));
        clienteVO.setMatricula(dataSet.getString(ColunasCliente.MATRICULA.getLabelComPrefixo()));
        clienteVO.setCodigoMatricula(dataSet.getInt(ColunasCliente.CODIGO_MATRICULA.getLabelComPrefixo()));
        clienteVO.getCategoria().setCodigo(dataSet.getInt(ColunasCliente.CATEGORIA.getLabelComPrefixo()));
        clienteVO.setCodAcesso(dataSet.getString(ColunasCliente.COD_ACESSO.getLabelComPrefixo()));
        clienteVO.setCodAcessoAlternativo(dataSet.getString(ColunasCliente.COD_ACESSO_ALTERNATIVO.getLabelComPrefixo()));
        clienteVO.setBanco(dataSet.getString(ColunasCliente.BANCO.getLabelComPrefixo()));
        clienteVO.setAgencia(dataSet.getString(ColunasCliente.AGENCIA.getLabelComPrefixo()));
        clienteVO.setAgenciaDigito(dataSet.getString(ColunasCliente.AGENCIA_DIGITO.getLabelComPrefixo()));
        clienteVO.setConta(dataSet.getString(ColunasCliente.CONTA.getLabelComPrefixo()));
        clienteVO.getEmpresa().setCodigo(dataSet.getInt(ColunasCliente.EMPRESA.getLabelComPrefixo()));
        clienteVO.setContaDigito(dataSet.getString(ColunasCliente.CONTA_DIGITO.getLabelComPrefixo()));
        clienteVO.setIdentificadorParaCobranca(dataSet.getString(ColunasCliente.IDENTIFICADOR_PARA_COBRANCA.getLabelComPrefixo()));
        clienteVO.getFreePass().setCodigo(dataSet.getInt(ColunasCliente.FREE_PASS.getLabelComPrefixo()));
        clienteVO.setResponsavelFreePass(dataSet.getInt(ColunasCliente.RESPONSAVEL_FREE_PASS.getLabelComPrefixo()));
        clienteVO.setUaCliente(new AcessoClienteVO());
        clienteVO.getUaCliente().setCodigo(dataSet.getInt(ColunasCliente.UA_CODIGO.getLabelComPrefixo()));
        clienteVO.setMatriculaExterna(dataSet.getLong(ColunasCliente.MATRICULA_EXTERNA.getLabelComPrefixo()));
//        clienteVO.setDataValidadeCarteirinha(dataSet.getDate(ColunasCliente.DATA_VALIDADE_CARTEIRINHA.getLabelComPrefixo()));
        clienteVO.setPorcentagemDescontoBoletoPagAntecipado(dataSet.getDouble(ColunasCliente.PORCENTAGEM_DESCONTO_BOLETO_PAG_ANTECIPADO.getLabelComPrefixo()));

        try {
            clienteVO.getPessoaResponsavel().setCodigo(dataSet.getInt(ColunasCliente.PESSOA_RESPONSAVEL.getLabelComPrefixo()));
            clienteVO.setParqPositivo(dataSet.getBoolean(ColunasCliente.PARQ_POSITIVO.getLabelComPrefixo()));
            clienteVO.setVerificarCliente(dataSet.getBoolean(ColunasCliente.VERIFICAR_CLIENTE.getLabelComPrefixo()));
            clienteVO.setVerificadoEm(dataSet.getTimestamp(ColunasCliente.VERIFICADO_EM.getLabelComPrefixo()));
            clienteVO.getObjecao().setCodigo(dataSet.getInt(ColunasCliente.OBJECAO.getLabelComPrefixo()));
            clienteVO.setAnexo(dataSet.getString(ColunasCliente.ANEXO.getLabelComPrefixo()));
            clienteVO.setNomeAnexo(dataSet.getString(ColunasCliente.NOME_ANEXO.getLabelComPrefixo()));
            clienteVO.setDataCadastroAnexo(dataSet.getDate(ColunasCliente.DATA_CADASTRO_ANEXO.getLabelComPrefixo()));
        } catch (Exception ignored) {
        }

        return clienteVO;
    }

    private PessoaVO getPessoaVO(ResultSet dataSet, boolean preencherEmail) throws SQLException {
        PessoaVO pessoaVO = new PessoaVO();
        pessoaVO.setNovoObj(false);
        pessoaVO.setCodigo(dataSet.getInt(ColunasPessoa.CODIGO.getLabelComPrefixo()));
        pessoaVO.setNome(dataSet.getString(ColunasPessoa.NOME.getLabelComPrefixo()));
        pessoaVO.getProfissao().setCodigo(dataSet.getInt(ColunasPessoa.PROFISSAO.getLabelComPrefixo()));
        pessoaVO.setDataCadastro(dataSet.getDate(ColunasPessoa.DATA_CADASTRO.getLabelComPrefixo()));
        pessoaVO.setDataNasc(dataSet.getDate(ColunasPessoa.DATA_NASC.getLabelComPrefixo()));
        pessoaVO.setNomePai(dataSet.getString(ColunasPessoa.NOME_PAI.getLabelComPrefixo()));
        pessoaVO.setNomeMae(dataSet.getString(ColunasPessoa.NOME_MAE.getLabelComPrefixo()));
        pessoaVO.setCfp(dataSet.getString(ColunasPessoa.CFP.getLabelComPrefixo()));
        pessoaVO.setLiberaSenhaAcesso(dataSet.getBoolean(ColunasPessoa.LIBERA_SENHA_ACESSO.getLabelComPrefixo()));
        pessoaVO.setRg(dataSet.getString(ColunasPessoa.RG.getLabelComPrefixo()));
        pessoaVO.setRgOrgao(dataSet.getString(ColunasPessoa.RG_ORGAO.getLabelComPrefixo()));
        pessoaVO.setRgUf(dataSet.getString(ColunasPessoa.RG_UF.getLabelComPrefixo()));
        pessoaVO.getCidade().setCodigo(dataSet.getInt(ColunasPessoa.CIDADE.getLabelComPrefixo()));
        pessoaVO.getEstadoVO().setCodigo(dataSet.getInt(ColunasPessoa.ESTADO.getLabelComPrefixo()));
        pessoaVO.getPais().setCodigo(dataSet.getInt(ColunasPessoa.PAIS.getLabelComPrefixo()));
        pessoaVO.setEstadoCivil(dataSet.getString(ColunasPessoa.ESTADO_CIVIL.getLabelComPrefixo()));
        pessoaVO.setNacionalidade(dataSet.getString(ColunasPessoa.NACIONALIDADE.getLabelComPrefixo()));
        pessoaVO.setNaturalidade(dataSet.getString(ColunasPessoa.NATURALIDADE.getLabelComPrefixo()));
        pessoaVO.setSexo(dataSet.getString(ColunasPessoa.SEXO.getLabelComPrefixo()));
        pessoaVO.setWebPage(dataSet.getString(ColunasPessoa.WEBPAGE.getLabelComPrefixo()));
        pessoaVO.getGrauInstrucao().setCodigo(dataSet.getInt(ColunasPessoa.GRAU_INSTRUCAO.getLabelComPrefixo()));
        pessoaVO.setCategoriaPessoa(TipoPessoa.obterPorCodigo(dataSet.getInt(ColunasPessoa.TIPO_PESSOA.getLabelComPrefixo())));
        pessoaVO.setFotoKey(dataSet.getString(ColunasPessoa.FOTO_KEY.getLabelComPrefixo()));
        pessoaVO.setIdVindi(dataSet.getInt(ColunasPessoa.ID_VINDI.getLabelComPrefixo()));
        pessoaVO.setDataAlteracaoVindi(dataSet.getDate(ColunasPessoa.DATA_ALTERACAO_VINDI.getLabelComPrefixo()));
        pessoaVO.setIdMaxiPago(dataSet.getString(ColunasPessoa.ID_MAXI_PAGO.getLabelComPrefixo()));
        pessoaVO.setDataAlteracaoMaxiPago(dataSet.getDate(ColunasPessoa.DATA_ALTERACAO_MAXI_PAGO.getLabelComPrefixo()));

        try {
            pessoaVO.setFoto(dataSet.getBytes(ColunasPessoa.FOTO.getLabelComPrefixo()));
        } catch (Exception ignored) {
        }

        try {
            pessoaVO.setCategoriaPessoa(TipoPessoa.obterPorCodigo(dataSet.getInt(ColunasPessoa.TIPO_PESSOA.getLabelComPrefixo())));
            pessoaVO.setCnpj(dataSet.getString(ColunasPessoa.CNPJ.getLabelComPrefixo()));
            pessoaVO.setInscEstadual(dataSet.getString(ColunasPessoa.INSC_ESTADUAL.getLabelComPrefixo()));
            pessoaVO.setInscMunicipal(dataSet.getString(ColunasPessoa.INSC_MUNICIPAL.getLabelComPrefixo()));
            pessoaVO.setCfdf(dataSet.getString(ColunasPessoa.CFDF.getLabelComPrefixo()));
            pessoaVO.setEmitirNotaNomeMae(dataSet.getBoolean(ColunasPessoa.EMITIR_NOTA_NOME_MAE.getLabelComPrefixo()));
        } catch (Exception ignored) {
        }

        if (preencherEmail) {
            pessoaVO.setEmail(dataSet.getString("emails"));
        }
        return pessoaVO;
    }

    private ContratoVO getContratoVO(ResultSet dataSet) throws SQLException {
        ContratoVO contratoVO = new ContratoVO();
        contratoVO.setNovoObj(false);
        contratoVO.setCodigo(dataSet.getInt(ColunasContrato.CODIGO.getLabelComPrefixo()));
        contratoVO.getEmpresa().setCodigo(dataSet.getInt(ColunasContrato.EMPRESA.getLabelComPrefixo()));
        contratoVO.getPessoa().setCodigo(dataSet.getInt(ColunasContrato.PESSOA.getLabelComPrefixo()));
        contratoVO.getConsultor().setCodigo(dataSet.getInt(ColunasContrato.CONSULTOR.getLabelComPrefixo()));
        contratoVO.getPlano().setCodigo(dataSet.getInt(ColunasContrato.PLANO.getLabelComPrefixo()));
        contratoVO.setSituacao(dataSet.getString(ColunasContrato.SITUACAO.getLabelComPrefixo()));
        contratoVO.setNomeModalidades(dataSet.getString(ColunasContrato.NOME_MODALIDADES.getLabelComPrefixo()));
        contratoVO.setObservacao(dataSet.getString(ColunasContrato.OBSERVACAO.getLabelComPrefixo()));
        contratoVO.setEstendeCoberturaFamiliares(dataSet.getBoolean(ColunasContrato.ESTENDE_COBERTURA_FAMILIARES.getLabelComPrefixo()));
        contratoVO.setVigenciaDe(dataSet.getDate(ColunasContrato.VIGENCIA_DE.getLabelComPrefixo()));
        contratoVO.setVigenciaAte(dataSet.getDate(ColunasContrato.VIGENCIA_ATE.getLabelComPrefixo()));
        contratoVO.getConvenioDesconto().setCodigo(dataSet.getInt(ColunasContrato.CONVENIO_DESCONTO.getLabelComPrefixo()));
        contratoVO.setValorBaseCalculo(dataSet.getDouble(ColunasContrato.VALOR_BASE_CALCULO.getLabelComPrefixo()));
        contratoVO.setValorFinal(dataSet.getDouble(ColunasContrato.VALOR_FINAL.getLabelComPrefixo()));
        contratoVO.setPagarComBoleto(dataSet.getBoolean(ColunasContrato.PAGAR_COM_BOLETO.getLabelComPrefixo()));
        contratoVO.getResponsavelContrato().setCodigo(dataSet.getInt(ColunasContrato.RESPONSAVEL_CONTRATO.getLabelComPrefixo()));
        contratoVO.getResponsavelLiberacaoCondicaoPagamento().setCodigo(dataSet.getInt(ColunasContrato.RESPONSAVEL_LIBERACAO_CONDICAO_PAGAMENTO.getLabelComPrefixo()));
        contratoVO.setDividirProdutosNasParcelas(dataSet.getBoolean(ColunasContrato.DIVIDIR_PRODUTOS_NAS_PARCELAS.getLabelComPrefixo()));
        contratoVO.getDesconto().setCodigo(dataSet.getInt(ColunasContrato.DESCONTO.getLabelComPrefixo()));
        contratoVO.setTipoDesconto(dataSet.getString(ColunasContrato.TIPO_DESCONTO.getLabelComPrefixo()));
        contratoVO.setValorDescontoEspecifico(dataSet.getDouble(ColunasContrato.VALOR_DESCONTO_ESPECIFICO.getLabelComPrefixo()));
        contratoVO.setValorDescontoPorcentagem(dataSet.getDouble(ColunasContrato.VALOR_DESCONTO_PORCENTAGEM.getLabelComPrefixo()));
        contratoVO.setVigenciaAteAjustada(dataSet.getDate(ColunasContrato.VIGENCIA_ATE_AJUSTADA.getLabelComPrefixo()));
        contratoVO.setDataLancamento(dataSet.getTimestamp(ColunasContrato.DATA_LANCAMENTO.getLabelComPrefixo()));
        contratoVO.setSituacaoContrato(dataSet.getString(ColunasContrato.SITUACAO_CONTRATO.getLabelComPrefixo()));
        contratoVO.setDataPrevistaRenovar(dataSet.getDate(ColunasContrato.DATA_PREVISTA_RENOVAR.getLabelComPrefixo()));
        contratoVO.setDataRenovarRealizada(dataSet.getDate(ColunasContrato.DATA_RENOVAR_REALIZADA.getLabelComPrefixo()));
        contratoVO.setSituacaoRenovacao(dataSet.getString(ColunasContrato.SITUACAO_RENOVACAO.getLabelComPrefixo()));
        contratoVO.setDataPrevistaRematricula(dataSet.getDate(ColunasContrato.DATA_PREVISTA_REMATRICULA.getLabelComPrefixo()));
        contratoVO.setDataRematriculaRealizada(dataSet.getDate(ColunasContrato.DATA_REMATRICULA_REALIZADA.getLabelComPrefixo()));
        contratoVO.setDataMatricula(dataSet.getDate(ColunasContrato.DATA_MATRICULA.getLabelComPrefixo()));
        contratoVO.setSituacaoRematricula(dataSet.getString(ColunasContrato.SITUACAO_REMATRICULA.getLabelComPrefixo()));
        contratoVO.setSomaProduto(dataSet.getDouble(ColunasContrato.SOMA_PRODUTO.getLabelComPrefixo()));
        contratoVO.setContratoBaseadoRenovacao(dataSet.getInt(ColunasContrato.CONTRATO_BASEADO_RENOVACAO.getLabelComPrefixo()));
        contratoVO.setContratoBaseadoRematricula(dataSet.getInt(ColunasContrato.CONTRATO_BASEADO_REMATRICULA.getLabelComPrefixo()));
        contratoVO.setContratoResponsavelRenovacaoMatricula(dataSet.getInt(ColunasContrato.CONTRATO_RESPONSAVEL_RENOVACAO_MATRICULA.getLabelComPrefixo()));
        contratoVO.setContratoResponsavelRematriculaMatricula(dataSet.getInt(ColunasContrato.CONTRATO_RESPONSAVEL_REMATRICULA_MATRICULA.getLabelComPrefixo()));
        contratoVO.setBolsa(dataSet.getBoolean(ColunasContrato.BOLSA.getLabelComPrefixo()));
        contratoVO.setDiaVencimentoProrata(dataSet.getInt(ColunasContrato.DIA_VENCIMENTO_PRORATA.getLabelComPrefixo()));
        contratoVO.setNaoPermitirRenovacaoRematriculaDeContratoAnteriores(dataSet.getBoolean(ColunasContrato.NAO_PERMITIR_RENOVACAO_REMATRICULA_CONTRATOS_ANTERIORES.getLabelComPrefixo()));
        contratoVO.setDataAlteracaoManual(dataSet.getDate(ColunasContrato.DATA_ALTERACAO_MANUAL.getLabelComPrefixo()));
        contratoVO.setResponsavelDataBase(new UsuarioVO());
        contratoVO.getResponsavelDataBase().setCodigo(dataSet.getInt(ColunasContrato.RESPONSAVEL_DATABASE.getLabelComPrefixo()));
        contratoVO.setRegimeRecorrencia(dataSet.getBoolean(ColunasContrato.REGIME_RECORRENCIA.getLabelComPrefixo()));
        contratoVO.setContratoAgendadoEspontaneo(TipoContratoEnum.getTipo(dataSet.getInt(ColunasContrato.ORIGEM_CONTRATO.getLabelComPrefixo())));
        contratoVO.setRenovavelAutomaticamente(dataSet.getBoolean(ColunasContrato.RENOVAVEL_AUTOMATICAMENTE.getLabelComPrefixo()));
        contratoVO.setQuantidadeMaximaFrequencia(dataSet.getInt(ColunasContrato.QUANTIDADE_MAXIMA_FREQUENCIA.getLabelComPrefixo()));
        contratoVO.setVendaCreditoTreino(dataSet.getBoolean(ColunasContrato.VENDA_CREDITO_TREINO.getLabelComPrefixo()));
        contratoVO.setNumeroCupomDesconto(dataSet.getString(ColunasContrato.NUMERO_CUPOM_DESCONTO.getLabelComPrefixo()));
//        contratoVO.setAgendadoConviteAulaExperimental(new ConviteAulaExperimentalVO());
//        contratoVO.getAgendadoConviteAulaExperimental().setCodigo(dataSet.getInt(ColunasContrato.AGENDADO_CONVITE_AULA_EXPERIMENTAL.getLabelComPrefixo()));
        contratoVO.setPermiteRenovacaoAutomatica(dataSet.getBoolean(ColunasContrato.PERMITE_RENOVACAO_AUTOMATICA.getLabelComPrefixo()));
        contratoVO.getGrupo().setCodigo(dataSet.getInt(ColunasContrato.GRUPO.getLabelComPrefixo()));

        try {
            contratoVO.setValorArredondamento(dataSet.getDouble(ColunasContrato.VALOR_ARREDONDAMENTO.getLabelComPrefixo()));
        } catch (Exception e) {
            contratoVO.setValorArredondamento(0.0);
        }

        try {
            contratoVO.setImportacao(dataSet.getBoolean(ColunasContrato.IMPORTACAO.getLabelComPrefixo()));
            contratoVO.setCrossfit(dataSet.getBoolean(ColunasContrato.CROSSFIT.getLabelComPrefixo()));
        } catch (Exception e) {
            contratoVO.setImportacao(false);
        }

        try {
            contratoVO.setNomeConvenioDesconto(dataSet.getString(ColunasContrato.NOME_CONVENIO_DESCONTO.getLabelComPrefixo()));
            contratoVO.setValorConvenioDesconto(dataSet.getDouble(ColunasContrato.VALOR_CONVENIO_DESCONTO.getLabelComPrefixo()));
            contratoVO.setPercentualConvenioDesconto(dataSet.getDouble(ColunasContrato.PERCENTUAL_CONVENIO_DESCONTO.getLabelComPrefixo()));
        } catch (Exception ignored) {
        }

        return contratoVO;
    }

    private ContratoDuracaoVO getContratoDuracaoVO(ResultSet dataSet) throws SQLException {
        ContratoDuracaoVO contratoDuracaoVO = new ContratoDuracaoVO();
        contratoDuracaoVO.setCodigo(dataSet.getInt(ColunasContratoDuracao.CODIGO.getLabelComPrefixo()));
        contratoDuracaoVO.setNumeroMeses(dataSet.getInt(ColunasContratoDuracao.NUMERO_MESES.getLabelComPrefixo()));
        contratoDuracaoVO.setContrato(dataSet.getInt(ColunasContratoDuracao.CONTRATO.getLabelComPrefixo()));
        contratoDuracaoVO.setNrMaximoParcelasCondPagamento(dataSet.getInt(ColunasContratoDuracao.NR_MAXIMO_PARCELAS_PAGAMENTO.getLabelComPrefixo()));
        contratoDuracaoVO.setTipoValor(dataSet.getString(ColunasContratoDuracao.TIPO_VALOR.getLabelComPrefixo()));
        contratoDuracaoVO.setTipoOperacao(dataSet.getString(ColunasContratoDuracao.TIPO_OPERACAO.getLabelComPrefixo()));
        contratoDuracaoVO.setPercentualDesconto(dataSet.getDouble(ColunasContratoDuracao.PERCENTUAL_DESCONTO.getLabelComPrefixo()));
        contratoDuracaoVO.setValorEspecifico(dataSet.getDouble(ColunasContratoDuracao.VALOR_ESPECIFICO.getLabelComPrefixo()));
        contratoDuracaoVO.setValorDesejado(dataSet.getDouble(ColunasContratoDuracao.VALOR_DESEJADO.getLabelComPrefixo()));
        contratoDuracaoVO.setValorDesejadoMensal(dataSet.getDouble(ColunasContratoDuracao.VALOR_DESEJADO_MENSAL.getLabelComPrefixo()));
        contratoDuracaoVO.setValorDesejadoParcela(dataSet.getDouble(ColunasContratoDuracao.VALOR_DESEJADO_PARCELA.getLabelComPrefixo()));
        contratoDuracaoVO.setCarencia(dataSet.getInt(ColunasContratoDuracao.CARENCIA.getLabelComPrefixo()));
        contratoDuracaoVO.setQuantidadeDiasExtra(dataSet.getInt(ColunasContratoDuracao.QUANTIDADE_DIAS_EXTRA.getLabelComPrefixo()));
        contratoDuracaoVO.setNovoObj(false);
        return contratoDuracaoVO;
    }

}

package negocio.facade.jdbc.basico.renovacaosintetico;

import negocio.facade.jdbc.arquitetura.consulta.ColunaPrefixavel;

/**
 * <AUTHOR>
 * @since 07/08/2018
 */
enum ColunasPlano implements ColunaPrefixavel {

    CODIGO("codigo"),
    DESCRICAO("descricao"),
    EMPRESA("empresa"),
    VIGENCIA_DE("vigenciaDe"),
    VIGENCIA_ATE("vigenciaAte"),
    INGRESSO_ATE("ingressoAte"),
    BOLSA("bolsa"),
    PERMITE_PAGAR_COM_BOLETO("permitepagarcomboleto"),
    PRODUTO_PADRAO_GERAR_PARCELAS_CONTRATO("produtoPadraoGerarParcelasContrato"),
    PRODUTO_TAXA_CANCELAMENTO("produtoTaxaCancelamento"),
    PERCENTUAL_MULTA_CANCELAMENTO("percentualMultaCancelamento"),
    PLANO_TEXTO_PADRAO("planoTextoPadrao"),
    RECIBO_TEXTO_PADRAO("reciboTextoPadrao"),
    PRORATA_OBRIGATORIO("prorataObrigatorio"),
    DIAS_VENCIMENTO_PRORATA("diasVencimentoProrata"),
    DESCONTO_ANTECIPADO("descontoAntecipado"),
    RECORRENCIA("recorrencia"),
    CORRESPONDENCIA_ZD("correspondencia_zd"),
    COMISSAO("comissao"),
    DESCRICAO_ENCANTAMENTO("descricaoencantamento"),
    QUANTIDADE_MAXIMA_FREQUENCIA("quantidadeMaximaFrequencia"),
    TIPO_FREQUENCIA("tipoFrequencia"),
    VENDA_CREDITO_TREINO("vendaCreditoTreino"),
    DIA_DO_MES_DESCONTO_BOLETO_PAG_ANTECIPADO("diaDoMesDescontoBoletoPagAntecipado"),
    PORCENTAGEM_DESCONTO_BOLETO_PAG_ANTECIPADO("porcentagemDescontoBoletoPagAntecipado"),
    COBRAR_ADESAO_SEPARADA("CobrarAdesaoSeparada"),
    NR_VEZES_PARCELAR_ADESAO("NrVezesParcelarAdesao"),
    COBRAR_PRODUTO_SEPARADO("cobrarProdutoSeparado"),
    NR_VEZES_PARCELAR_PRODUTO("nrVezesParcelarProduto"),
    SITE("site"),
    RENOVAVEL_AUTOMATICAMENTE("renovavelAutomaticamente"),
    INICIO_MINIMO_CONTRATO("inicioMinimoContrato"),
    PARCELAMENTO_OPERADORA("parcelamentoOperadora"),
    RENOVAR_PRODUTO_OBRIGATORIO("renovarprodutoobrigatorio"),
    RENOVAR_ANUIDADE_AUTOMATICAMENTE("renovaranuidadeautomaticamente"),
    PONTOS("pontos"),
    TERMO_ACEITE("termoAceite"),
    TOTEM("totem"),
    CONVENIO_COBRANCA_PRIVATELABEL("convenioCobrancaPrivateLabel"),
    CREDITO_TREINO_NAO_CUMULATIVO("creditoTreinoNaoCumulativo"),
    RENOVAR_AUTO_UTILIZANDO_VALOR_BASE_CONTRATO("renovarAutomaticamenteUtilizandoValorBaseContrato");

    private static final String PREFIXO = "plano";
    private String label;

    ColunasPlano(String label) {
        this.label = label;
    }

    /**
     * @return O prefixo dessas colunas
     */
    public static String getPrefixo() {
        return PREFIXO;
    }

    @Override
    public String getLabel() {
        return label;
    }

    @Override
    public String getLabelComPrefixo() {
        return PREFIXO + "." + label;
    }

}

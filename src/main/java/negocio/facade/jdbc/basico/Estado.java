package negocio.facade.jdbc.basico;

import negocio.comuns.basico.EstadoVO;
import negocio.comuns.basico.PaisVO;
import negocio.comuns.utilitarias.ConsistirException;
import negocio.comuns.utilitarias.Uteis;
import negocio.facade.jdbc.arquitetura.SuperEntidade;
import negocio.facade.jdbc.arquitetura.SuperFacadeJDBC;
import negocio.interfaces.basico.EstadoInterfaceFacade;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.Statement;
import java.util.ArrayList;
import java.util.Iterator;
import java.util.List;

/**
 * Classe de persistência que encapsula todas as operações de manipulação dos dados da classe <code>EstadoVO</code>.
 * Responsável por implementar operações como incluir, alterar, excluir e consultar pertinentes a classe <code>EstadoVO</code>.
 * Encapsula toda a interação com o banco de dados.
 * @see EstadoVO
 * @see SuperEntidade
 * @see Pais
 */
public class Estado extends SuperEntidade implements EstadoInterfaceFacade {    

    public Estado() throws Exception {
        super();        
        setIdEntidade("Pais");
    }

    public Estado(Connection con) throws Exception {
    	   super(con);        
           setIdEntidade("Pais");
       }

	/**
     * Operação responsável por retornar um novo objeto da classe <code>EstadoVO</code>.
     */
    public EstadoVO novo() throws Exception {
        incluir(getIdEntidade());
        EstadoVO obj = new EstadoVO();
        return obj;
    }

    /**
     * Operação responsável por incluir no banco de dados um objeto da classe <code>EstadoVO</code>.
     * Primeiramente valida os dados (<code>validarDados</code>) do objeto. Verifica a conexão com o banco de dados e a permissão do usuário
     * para realizar esta operacão na entidade.
     * Isto, através da operação <code>incluir</code> da superclasse.
     * @param obj  Objeto da classe <code>EstadoVO</code> que será gravado no banco de dados.
     * @exception Exception Caso haja problemas de conexão, restrição de acesso ou validação de dados.
     */
    public void incluir(EstadoVO obj) throws Exception {
        EstadoVO.validarDados(obj);
        incluir(getIdEntidade());
        obj.realizarUpperCaseDados();
        String sql = "INSERT INTO Estado( descricao, sigla, pais, codigoibge) VALUES ( ?, ?, ?, ?)";
        try (PreparedStatement sqlInserir = con.prepareStatement(sql)) {
            sqlInserir.setString(1, obj.getDescricao());
            sqlInserir.setString(2, obj.getSigla());
            if (obj.getPais() != 0) {
                sqlInserir.setInt(3, obj.getPais());
            } else {
                sqlInserir.setNull(3, 0);
            }
            sqlInserir.setString(4, obj.getCodigoIBGE());
            sqlInserir.execute();
        }
        obj.setCodigo(obterValorChavePrimariaCodigo());
        obj.setNovoObj(Boolean.FALSE);
    }

    /**
     * Operação responsável por alterar no BD os dados de um objeto da classe <code>EstadoVO</code>.
     * Sempre utiliza a chave primária da classe como atributo para localização do registro a ser alterado.
     * Primeiramente valida os dados (<code>validarDados</code>) do objeto. Verifica a conexão com o banco de dados e a permissão do usuário
     * para realizar esta operacão na entidade.
     * Isto, através da operação <code>alterar</code> da superclasse.
     * @param obj    Objeto da classe <code>EstadoVO</code> que será alterada no banco de dados.
     * @exception Exception Caso haja problemas de conexão, restrição de acesso ou validação de dados.
     */
    public void alterar(EstadoVO obj) throws Exception {
        EstadoVO.validarDados(obj);
        alterar(getIdEntidade());
        obj.realizarUpperCaseDados();
        String sql = "UPDATE Estado set descricao=?, sigla=?, pais=?, codigoibge = ? WHERE ((codigo = ?))";
        try (PreparedStatement sqlAlterar = con.prepareStatement(sql)) {
            sqlAlterar.setString(1, obj.getDescricao());
            sqlAlterar.setString(2, obj.getSigla());
            if (obj.getPais() != 0) {
                sqlAlterar.setInt(3, obj.getPais());
            } else {
                sqlAlterar.setNull(3, 0);
            }
            sqlAlterar.setString(4, obj.getCodigoIBGE());
            sqlAlterar.setInt(5, obj.getCodigo());
            sqlAlterar.execute();
        }
    }

    /**
     * Operação responsável por excluir no BD um objeto da classe <code>EstadoVO</code>.
     * Sempre localiza o registro a ser excluído através da chave primária da entidade.
     * Primeiramente verifica a conexão com o banco de dados e a permissão do usuário
     * para realizar esta operacão na entidade.
     * Isto, através da operação <code>excluir</code> da superclasse.
     * @param obj    Objeto da classe <code>EstadoVO</code> que será removido no banco de dados.
     * @exception Exception Caso haja problemas de conexão ou restrição de acesso.
     */
    public void excluir(EstadoVO obj) throws Exception {
        excluir(getIdEntidade());
        String sql = "DELETE FROM Estado WHERE ((codigo = ?))";
        try (PreparedStatement sqlExcluir = con.prepareStatement(sql)) {
            sqlExcluir.setInt(1, obj.getCodigo());
            sqlExcluir.execute();
        }
    }

    /**
     * Responsável por realizar uma consulta de <code>Estado</code> através do valor do atributo 
     * <code>nome</code> da classe <code>Pais</code>
     * Faz uso da operação <code>montarDadosConsulta</code> que realiza o trabalho de prerarar o List resultante.
     * @return  List Contendo vários objetos da classe <code>EstadoVO</code> resultantes da consulta.
     * @exception Exception Caso haja problemas de conexão ou restrição de acesso.
     */
    public List consultarPorNomePais(String valorConsulta, int nivelMontarDados) throws Exception {
        consultar(getIdEntidade(), true);
        String sqlStr = "SELECT Estado.* FROM Estado, Pais WHERE Estado.pais = Pais.codigo and upper( Pais.nome ) like('" + valorConsulta.toUpperCase() + "%') ORDER BY Pais.nome";
        try (Statement stm = con.createStatement()) {
            try (ResultSet tabelaResultado = stm.executeQuery(sqlStr)) {
                return montarDadosConsulta(tabelaResultado, nivelMontarDados);
            }
        }
    }

    /**
     * Responsável por realizar uma consulta de <code>Estado</code> através do valor do atributo 
     * <code>String sigla</code>. Retorna os objetos, com início do valor do atributo idêntico ao parâmetro fornecido.
     * Faz uso da operação <code>montarDadosConsulta</code> que realiza o trabalho de prerarar o List resultante.
     * @param   controlarAcesso Indica se a aplicação deverá verificar se o usuário possui permissão para esta consulta ou não.
     * @return  List Contendo vários objetos da classe <code>EstadoVO</code> resultantes da consulta.
     * @exception Exception Caso haja problemas de conexão ou restrição de acesso.
     */
    public List consultarPorSigla(String valorConsulta, boolean controlarAcesso, int nivelMontarDados) throws Exception {
        consultar(getIdEntidade(), controlarAcesso);
        String sqlStr = "SELECT * FROM Estado WHERE upper( sigla ) like('" + valorConsulta.toUpperCase() + "%') ORDER BY sigla";
        try (Statement stm = con.createStatement()) {
            try (ResultSet tabelaResultado = stm.executeQuery(sqlStr)) {
                return (montarDadosConsulta(tabelaResultado, nivelMontarDados));
            }
        }
    }

    public EstadoVO consultarPorSiglaDescricaoEPais(String valorConsulta, String descricaoEstado, Integer codigoPais, boolean controlarAcesso, int nivelMontarDados) throws Exception {
        consultar(getIdEntidade(), controlarAcesso);
        String sqlStr = "SELECT * FROM Estado WHERE upper( sigla ) like('" + valorConsulta.toUpperCase() + "%') and upper( descricao ) like('" + descricaoEstado.toUpperCase() + "%') and pais =  " + codigoPais.intValue() + " ORDER BY sigla";
        try (Statement stm = con.createStatement()) {
            try (ResultSet tabelaResultado = stm.executeQuery(sqlStr)) {
                if (!tabelaResultado.next()) {
                    return null;
                }
                return (montarDados(tabelaResultado, nivelMontarDados));
            }
        }
    }
    public EstadoVO consultarPorSiglaUf(String valorConsulta, boolean controlarAcesso, int nivelMontarDados) throws Exception {
        consultar(getIdEntidade(), controlarAcesso);
        String sqlStr = "SELECT * FROM Estado WHERE upper( sigla ) like('" + valorConsulta.toUpperCase() + "%') ORDER BY sigla";
        try (Statement stm = con.createStatement()) {
            try (ResultSet tabelaResultado = stm.executeQuery(sqlStr)) {
                if (!tabelaResultado.next()) {
                    return new EstadoVO();
                }
                return (montarDados(tabelaResultado, nivelMontarDados));
            }
        }
    }

    public EstadoVO consultarPorSiglaUf(String valorConsulta, PaisVO paisVO, int nivelMontarDados) throws Exception {
        String sqlStr = "SELECT * FROM Estado WHERE upper( sigla ) like('" + valorConsulta.toUpperCase() + "%') AND pais = " + paisVO.getCodigo() + " ORDER BY sigla";
        try (Statement stm = con.createStatement()) {
            try (ResultSet tabelaResultado = stm.executeQuery(sqlStr)) {
                if (!tabelaResultado.next()) {
                    return new EstadoVO();
                }
                return (montarDados(tabelaResultado, nivelMontarDados));
            }
        }
    }

    /**
     * Responsável por realizar uma consulta de <code>Estado</code> através do valor do atributo 
     * <code>String descricao</code>. Retorna os objetos, com início do valor do atributo idêntico ao parâmetro fornecido.
     * Faz uso da operação <code>montarDadosConsulta</code> que realiza o trabalho de prerarar o List resultante.
     * @param   controlarAcesso Indica se a aplicação deverá verificar se o usuário possui permissão para esta consulta ou não.
     * @return  List Contendo vários objetos da classe <code>EstadoVO</code> resultantes da consulta.
     * @exception Exception Caso haja problemas de conexão ou restrição de acesso.
     */
    public List consultarPorDescricao(String valorConsulta, boolean controlarAcesso, int nivelMontarDados) throws Exception {
        consultar(getIdEntidade(), controlarAcesso);
        String sqlStr = "SELECT * FROM Estado WHERE upper( descricao ) like('" + valorConsulta.toUpperCase() + "%') ORDER BY descricao";
        try (Statement stm = con.createStatement()) {
            try (ResultSet tabelaResultado = stm.executeQuery(sqlStr)) {
                return (montarDadosConsulta(tabelaResultado, nivelMontarDados));
            }
        }
    }

    /**
     * Responsável por realizar uma consulta de <code>Estado</code> através do valor do atributo 
     * <code>Integer codigo</code>. Retorna os objetos com valores iguais ou superiores ao parâmetro fornecido.
     * Faz uso da operação <code>montarDadosConsulta</code> que realiza o trabalho de prerarar o List resultante.
     * @param   controlarAcesso Indica se a aplicação deverá verificar se o usuário possui permissão para esta consulta ou não.
     * @return  List Contendo vários objetos da classe <code>EstadoVO</code> resultantes da consulta.
     * @exception Exception Caso haja problemas de conexão ou restrição de acesso.
     */
    public List consultarPorCodigo(Integer valorConsulta, boolean controlarAcesso, int nivelMontarDados) throws Exception {
        consultar(getIdEntidade(), controlarAcesso);
        String sqlStr = "SELECT * FROM Estado WHERE codigo >= " + valorConsulta.intValue() + " ORDER BY codigo";
        try (Statement stm = con.createStatement()) {
            try (ResultSet tabelaResultado = stm.executeQuery(sqlStr)) {
                return (montarDadosConsulta(tabelaResultado, nivelMontarDados));
            }
        }
    }

    /**
     * Responsável por montar os dados de vários objetos, resultantes de uma consulta ao banco de dados (<code>ResultSet</code>).
     * Faz uso da operação <code>montarDados</code> que realiza o trabalho para um objeto por vez.
     * @return  List Contendo vários objetos da classe <code>EstadoVO</code> resultantes da consulta.
     */
    public static List montarDadosConsulta(ResultSet tabelaResultado, int nivelMontarDados) throws Exception {
        List vetResultado = new ArrayList();
        while (tabelaResultado.next()) {
            EstadoVO obj = new EstadoVO();
            obj = montarDados(tabelaResultado, nivelMontarDados);
            vetResultado.add(obj);
        }
        return vetResultado;
    }

    /**
     * Responsável por montar os dados resultantes de uma consulta ao banco de dados (<code>ResultSet</code>)
     * em um objeto da classe <code>EstadoVO</code>.
     * @return  O objeto da classe <code>EstadoVO</code> com os dados devidamente montados.
     */
    public static EstadoVO montarDados(ResultSet dadosSQL, int nivelMontarDados) throws Exception {
        EstadoVO obj = new EstadoVO();
        obj.setCodigo(dadosSQL.getInt("codigo"));
        obj.setDescricao(dadosSQL.getString("descricao"));
        obj.setSigla(dadosSQL.getString("sigla"));
        obj.setPais(dadosSQL.getInt("pais"));
        try {
            obj.setCodigoIBGE(dadosSQL.getString("codigoibge"));
        } catch (Exception ex){

        }
        obj.setNovoObj(false);
        if (nivelMontarDados == Uteis.NIVELMONTARDADOS_DADOSBASICOS) {
            return obj;
        }

        return obj;
    }

    /**
     * Operação responsável por excluir todos os objetos da <code>EstadoVO</code> no BD.
     * Faz uso da operação <code>excluir</code> disponível na classe <code>Estado</code>.
     * @param pais campo chave para exclusão dos objetos no BD.
     * @exception Exception  Erro de conexão com o BD ou restrição de acesso a esta operação.
     */
    public void excluirEstados(Integer pais) throws Exception {
        excluir(getIdEntidade());
        String sql = "DELETE FROM Estado WHERE (pais = ?)";
        try (PreparedStatement sqlExcluir = con.prepareStatement(sql)) {
            sqlExcluir.setInt(1, pais.intValue());
            sqlExcluir.execute();
        }
    }

    public void alterarEstados(Integer pais, List objetos) throws Exception {
        String str = "DELETE FROM Estado WHERE pais = " + pais.intValue();
        Iterator i = objetos.iterator();
        while (i.hasNext()) {
            EstadoVO objeto = (EstadoVO) i.next();
            str += " AND codigo <> " + objeto.getCodigo().intValue();
        }
        try (PreparedStatement sqlExcluir = con.prepareStatement(str)) {
            sqlExcluir.execute();
        }
        Iterator e = objetos.iterator();
        while (e.hasNext()) {
            EstadoVO obj = (EstadoVO) e.next();
            if (obj.getCodigo().equals(new Integer(0))) {
                incluir(obj);
            } else {
                alterar(obj);
            }
        }
    }

    /**
     * Operação responsável por alterar todos os objetos da <code>EstadoVO</code> contidos em um Hashtable no BD.
     * Faz uso da operação <code>excluirEstados</code> e <code>incluirEstados</code> disponíveis na classe <code>Estado</code>.
     * @param objetos  List com os objetos a serem alterados ou incluídos no BD.
     * @exception Exception  Erro de conexão com o BD ou restrição de acesso a esta operação.
     */
//    public void alterarEstados( Integer pais, List objetos ) throws Exception {
//        excluirEstados( pais );
//        incluirEstados( pais, objetos );
//    }
    /**
     * Operação responsável por incluir objetos da <code>EstadoVO</code> no BD.
     * Garantindo o relacionamento com a entidade principal <code>basico.Pais</code> através do atributo de vínculo.
     * @param objetos List contendo os objetos a serem gravados no BD da classe.
     * @exception Exception  Erro de conexão com o BD ou restrição de acesso a esta operação.
     */
    public void incluirEstados(Integer paisPrm, List objetos) throws Exception {
        Iterator e = objetos.iterator();
        while (e.hasNext()) {
            EstadoVO obj = (EstadoVO) e.next();
            obj.setPais(paisPrm);
            incluir(obj);
        }
    }

    /**
     * Operação responsável por consultar todos os <code>EstadoVO</code> relacionados a um objeto da classe <code>basico.Pais</code>.
     * @param pais  Atributo de <code>basico.Pais</code> a ser utilizado para localizar os objetos da classe <code>EstadoVO</code>.
     * @return List  Contendo todos os objetos da classe <code>EstadoVO</code> resultantes da consulta.
     * @exception Exception  Erro de conexão com o BD ou restrição de acesso a esta operação.
     */
    public List consultarEstados(Integer pais, int nivelMontarDados) throws Exception {
        consultar(getIdEntidade());
        List objetos = new ArrayList();
        String sql = "SELECT * FROM Estado WHERE pais = ?";
        try (PreparedStatement sqlConsulta = con.prepareStatement(sql)) {
            sqlConsulta.setInt(1, pais.intValue());
            try (ResultSet resultado = sqlConsulta.executeQuery()) {
                while (resultado.next()) {
                    EstadoVO novoObj = new EstadoVO();
                    novoObj = Estado.montarDados(resultado, nivelMontarDados);
                    objetos.add(novoObj);
                }
            }
        }
        return objetos;
    }

    /**
     * Operação responsável por localizar um objeto da classe <code>EstadoVO</code>
     * através de sua chave primária. 
     * @exception Exception Caso haja problemas de conexão ou localização do objeto procurado.
     */
    public EstadoVO consultarPorChavePrimaria(Integer codigoPrm, int nivelMontarDados) throws Exception {
        consultar(getIdEntidade(), false);
        EstadoVO eCache = (EstadoVO) obterFromCache(codigoPrm);
        if (eCache != null){
            return eCache;
        }
        String sql = "SELECT * FROM Estado WHERE codigo = ?";
        try (PreparedStatement sqlConsultar = con.prepareStatement(sql)) {
            sqlConsultar.setInt(1, codigoPrm.intValue());
            try (ResultSet tabelaResultado = sqlConsultar.executeQuery()) {
                if (!tabelaResultado.next()) {
                    throw new ConsistirException("Dados Não Encontrados ( Estado ).");
                }
                eCache = (montarDados(tabelaResultado, nivelMontarDados));
            }
        }
        putToCache(eCache);
        return eCache;
    }
     /**
     * Operação responsável por localizar um objeto da classe <code>EstadoVO</code>
     * através de sua chave primária.
     * @exception Exception Caso haja problemas de conexão ou localização do objeto procurado.
     */
    public EstadoVO consultarPorCodigo(Integer codigoPrm, int nivelMontarDados) throws Exception {
        consultar(getIdEntidade(), false);
        String sql = "SELECT * FROM Estado WHERE codigo = ?";
        try (PreparedStatement sqlConsultar = con.prepareStatement(sql)) {
            sqlConsultar.setInt(1, codigoPrm.intValue());
            try (ResultSet tabelaResultado = sqlConsultar.executeQuery()) {
                if (!tabelaResultado.next()) {
                    return new EstadoVO();
                }
                return (montarDados(tabelaResultado, nivelMontarDados));
            }
        }
    }

    public EstadoVO consultarPorCodigoEmpresa(Integer codigoPrm, int nivelMontarDados) throws Exception {
        consultar(getIdEntidade(), false);
        String sql = "SELECT estado.* FROM estado \n" +
                "INNER JOIN empresa emp ON emp.estado = estado.codigo\n" +
                "WHERE emp.codigo = ?";
        try (PreparedStatement sqlConsultar = con.prepareStatement(sql)) {
            sqlConsultar.setInt(1, codigoPrm.intValue());
            try (ResultSet tabelaResultado = sqlConsultar.executeQuery()) {
                if (!tabelaResultado.next()) {
                    return new EstadoVO();
                }
                return (montarDados(tabelaResultado, nivelMontarDados));
            }
        }
    }

    public void preencherCodigosIBGEEstado() {
        try {
            SuperFacadeJDBC.executarUpdateExecutarProcessos("update estado set codigoibge = 11 where sigla = 'RO';", con);
            SuperFacadeJDBC.executarUpdateExecutarProcessos("update estado set codigoibge = 12 where sigla = 'AC';", con);
            SuperFacadeJDBC.executarUpdateExecutarProcessos("update estado set codigoibge = 13 where sigla = 'AM';", con);
            SuperFacadeJDBC.executarUpdateExecutarProcessos("update estado set codigoibge = 14 where sigla = 'RR';", con);
            SuperFacadeJDBC.executarUpdateExecutarProcessos("update estado set codigoibge = 15 where sigla = 'PA';", con);
            SuperFacadeJDBC.executarUpdateExecutarProcessos("update estado set codigoibge = 16 where sigla = 'AP';", con);
            SuperFacadeJDBC.executarUpdateExecutarProcessos("update estado set codigoibge = 17 where sigla = 'TO';", con);
            SuperFacadeJDBC.executarUpdateExecutarProcessos("update estado set codigoibge = 21 where sigla = 'MA';", con);
            SuperFacadeJDBC.executarUpdateExecutarProcessos("update estado set codigoibge = 22 where sigla = 'PI';", con);
            SuperFacadeJDBC.executarUpdateExecutarProcessos("update estado set codigoibge = 23 where sigla = 'CE';", con);
            SuperFacadeJDBC.executarUpdateExecutarProcessos("update estado set codigoibge = 24 where sigla = 'RN';", con);
            SuperFacadeJDBC.executarUpdateExecutarProcessos("update estado set codigoibge = 25 where sigla = 'PB';", con);
            SuperFacadeJDBC.executarUpdateExecutarProcessos("update estado set codigoibge = 26 where sigla = 'PE';", con);
            SuperFacadeJDBC.executarUpdateExecutarProcessos("update estado set codigoibge = 27 where sigla = 'AL';", con);
            SuperFacadeJDBC.executarUpdateExecutarProcessos("update estado set codigoibge = 28 where sigla = 'SE';", con);
            SuperFacadeJDBC.executarUpdateExecutarProcessos("update estado set codigoibge = 29 where sigla = 'BA';", con);
            SuperFacadeJDBC.executarUpdateExecutarProcessos("update estado set codigoibge = 31 where sigla = 'MG';", con);
            SuperFacadeJDBC.executarUpdateExecutarProcessos("update estado set codigoibge = 32 where sigla = 'ES';", con);
            SuperFacadeJDBC.executarUpdateExecutarProcessos("update estado set codigoibge = 33 where sigla = 'RJ';", con);
            SuperFacadeJDBC.executarUpdateExecutarProcessos("update estado set codigoibge = 35 where sigla = 'SP';", con);
            SuperFacadeJDBC.executarUpdateExecutarProcessos("update estado set codigoibge = 41 where sigla = 'PR';", con);
            SuperFacadeJDBC.executarUpdateExecutarProcessos("update estado set codigoibge = 42 where sigla = 'SC';", con);
            SuperFacadeJDBC.executarUpdateExecutarProcessos("update estado set codigoibge = 43 where sigla = 'RS';", con);
            SuperFacadeJDBC.executarUpdateExecutarProcessos("update estado set codigoibge = 50 where sigla = 'MS';", con);
            SuperFacadeJDBC.executarUpdateExecutarProcessos("update estado set codigoibge = 51 where sigla = 'MT';", con);
            SuperFacadeJDBC.executarUpdateExecutarProcessos("update estado set codigoibge = 52 where sigla = 'GO';", con);
            SuperFacadeJDBC.executarUpdateExecutarProcessos("update estado set codigoibge = 53 where sigla = 'DF';", con);
        } catch (Exception ignored) {
        }
    }
}

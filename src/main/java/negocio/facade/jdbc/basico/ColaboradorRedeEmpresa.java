package negocio.facade.jdbc.basico;

import negocio.comuns.basico.ColaboradorRedeEmpresaVO;
import negocio.comuns.utilitarias.Uteis;
import negocio.facade.jdbc.arquitetura.SuperEntidade;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;

import static org.apache.commons.lang3.StringUtils.isNotBlank;

public class ColaboradorRedeEmpresa extends SuperEntidade {

    public ColaboradorRedeEmpresa(Connection conexao) throws Exception {
        super(conexao);
    }

    public ColaboradorRedeEmpresa() throws Exception {
        super();
    }

    public void inserir(ColaboradorRedeEmpresaVO colaboradorRedeEmpresaVO) throws SQLException {
        String sql = "INSERT INTO ColaboradorRedeEmpresa " +
                "(colaborador,chaveorigem,chavedestino,datacadastro, nomeUnidade, mensagemSituacao, empresadestino)" +
                "VALUES" +
                "(?,?,?,?,?,?,?)";

        try (PreparedStatement preparedStatement = con.prepareStatement(sql)) {
            int i = 1;
            preparedStatement.setInt(i++, colaboradorRedeEmpresaVO.getColaborador());
            preparedStatement.setString(i++, colaboradorRedeEmpresaVO.getChaveOrigem());
            if (isNotBlank(colaboradorRedeEmpresaVO.getChaveDestino())) {
                preparedStatement.setString(i++, colaboradorRedeEmpresaVO.getChaveDestino());
            } else {
                preparedStatement.setString(i++, "");
            }
            preparedStatement.setTimestamp(i++, Uteis.getTimestamp());
            preparedStatement.setString(i++, colaboradorRedeEmpresaVO.getNomeUnidade());
            preparedStatement.setString(i++, colaboradorRedeEmpresaVO.getMensagemSituacao());
            preparedStatement.setInt(i++, colaboradorRedeEmpresaVO.getEmpresaDestino());
            preparedStatement.execute();
        }
    }

    public void alterarMensagemSituacao(Integer colaborador, String chaveOrigem, String chaveDestino, String mensagemSituacao, Integer empresaDestino) throws SQLException {
        String sql = "UPDATE ColaboradorRedeEmpresa set " +
                "mensagemSituacao = ? " +
                "WHERE colaborador = ? AND chaveorigem = ? AND chavedestino = ? AND empresadestino = ?";

        try (PreparedStatement preparedStatement = con.prepareStatement(sql)) {
            int i = 1;
            preparedStatement.setString(i++, mensagemSituacao);
            preparedStatement.setInt(i++, colaborador);
            preparedStatement.setString(i++, chaveOrigem);
            preparedStatement.setString(i++, chaveDestino);
            preparedStatement.setInt(i++, empresaDestino);
            preparedStatement.execute();
        }
    }

    public void limparDataAtualizacao(Integer colaborador, String chaveOrigem, String chaveDestino, Integer empresaDestino) throws SQLException {
        String sql = "UPDATE ColaboradorRedeEmpresa set " +
                "dataatualizacao = ? " +
                "WHERE colaborador = ? AND chaveorigem = ? AND chavedestino = ? AND empresadestino = ?";

        try (PreparedStatement preparedStatement = con.prepareStatement(sql)) {
            int i = 1;
            preparedStatement.setTimestamp(i++, null);
            preparedStatement.setInt(i++, colaborador);
            preparedStatement.setString(i++, chaveOrigem);
            preparedStatement.setString(i++, chaveDestino);
            preparedStatement.setInt(i++, empresaDestino);
            preparedStatement.execute();
        }
    }

    public void alterarDataAtualizacao(Integer colaborador, String chaveOrigem, String chaveDestino, String mensagemSituacao, Integer colaboradorReplicado, Integer empresaDestino) throws SQLException {
        String sql = "UPDATE ColaboradorRedeEmpresa set " +
                "dataatualizacao = ?, mensagemSituacao = ?, colaboradorreplicado = ? " +
                "WHERE colaborador = ? AND chaveorigem = ? AND empresadestino = ? AND chaveDestino = ?";

        try (PreparedStatement preparedStatement = con.prepareStatement(sql)) {
            int i = 1;
            preparedStatement.setTimestamp(i++, Uteis.getTimestamp());
            preparedStatement.setString(i++, mensagemSituacao);
            preparedStatement.setInt(i++, colaboradorReplicado);
            preparedStatement.setInt(i++, colaborador);
            preparedStatement.setString(i++, chaveOrigem);
            preparedStatement.setInt(i++, empresaDestino);
            preparedStatement.setString(i++, chaveDestino);
            preparedStatement.execute();
        }
    }

    public ColaboradorRedeEmpresaVO consultarPorChaveEmpresaColaborador(String chaveDestino, Integer empresaDestino, Integer colaborador) throws SQLException {
        String sql = "SELECT * FROM ColaboradorRedeEmpresa " +
                "WHERE chaveDestino = ? AND empresaDestino = ? AND colaborador = ?";

        try (PreparedStatement preparedStatement = con.prepareStatement(sql)) {
            int i = 1;
            preparedStatement.setString(i++, chaveDestino);
            preparedStatement.setInt(i++, empresaDestino);
            preparedStatement.setInt(i++, colaborador);
            try (ResultSet resultSet = preparedStatement.executeQuery()) {
                if (resultSet.next()) {
                    return montarDados(resultSet);
                }
            }
        }
        return null;
    }

    public ColaboradorRedeEmpresaVO montarDados(ResultSet resultSet) throws SQLException {
        ColaboradorRedeEmpresaVO colaboradorRedeEmpresaVO = new ColaboradorRedeEmpresaVO();
        colaboradorRedeEmpresaVO.setChaveOrigem(resultSet.getString("chaveorigem"));
        colaboradorRedeEmpresaVO.setChaveDestino(resultSet.getString("chavedestino"));
        colaboradorRedeEmpresaVO.setCodigo(resultSet.getInt("codigo"));
        colaboradorRedeEmpresaVO.setDatacadastro(resultSet.getDate("datacadastro"));
        colaboradorRedeEmpresaVO.setColaborador(resultSet.getInt("colaborador"));
        colaboradorRedeEmpresaVO.setDataatualizacao(resultSet.getDate("dataatualizacao"));
        colaboradorRedeEmpresaVO.setNomeUnidade(resultSet.getString("nomeUnidade"));
        colaboradorRedeEmpresaVO.setMensagemSituacao(resultSet.getString("mensagemsituacao"));
        colaboradorRedeEmpresaVO.setColaboradorReplicado(resultSet.getInt("colaboradorreplicado"));
        colaboradorRedeEmpresaVO.setEmpresaDestino(resultSet.getInt("empresadestino"));

        return colaboradorRedeEmpresaVO;
    }
}

package negocio.facade.jdbc.basico;

import br.com.pactosolucoes.agendatotal.json.AgendadoJSON;
import negocio.comuns.basico.PresencaVO;
import negocio.comuns.contrato.MatriculaAlunoHorarioTurmaVO;
import negocio.comuns.plano.TurmaVO;
import negocio.comuns.utilitarias.ConsistirException;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisValidacao;
import negocio.facade.jdbc.arquitetura.SuperEntidade;
import negocio.facade.jdbc.arquitetura.SuperFacadeJDBC;
import negocio.facade.jdbc.contrato.MatriculaAlunoHorarioTurma;
import negocio.interfaces.basico.PresencaInterfaceFacade;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.Types;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 *
 * <AUTHOR>
 */
public class Presenca extends SuperEntidade implements PresencaInterfaceFacade {

    public static final String SQL_INSERIR = "INSERT INTO presenca(dadosturma, datachamada, datapresenca) VALUES (?, ?, ?)";
    public static final String SQL_ALTERAR = "UPDATE presenca SET dadosturma=?, datachamada=?, datapresenca=? WHERE codigo = ?";
    public static final String SQL_EXCLUIR_CODIGO = "DELETE FROM presenca WHERE codigo = ?";
    public static final String SQL_CONTAR_PRESENCA_ALUNO_HT_PERIODO = "SELECT COUNT(pr.codigo) AS qtde FROM presenca pr "
            + "INNER JOIN matriculaalunohorarioturma maht ON maht.codigo = pr.dadosturma "
            + "WHERE maht.pessoa = ? AND maht.horarioturma = ? AND datapresenca >= ? AND datapresenca <= ?";
    public static final String SQL_CONSULTAR_CHAVE_PRIMARIA = "SELECT * FROM presenca WHERE codigo = ?";
    public static final String SQL_CONSULTAR_HT_PERIODO_PRESENCA = "SELECT pr.* FROM presenca pr "
            + "INNER JOIN matriculaalunohorarioturma maht ON maht.codigo = pr.dadosturma "
            + "WHERE maht.horarioturma = ? AND datapresenca >= ? AND datapresenca <= ?";
    public static final String SQL_CONSULTAR_PESSOA_PERIODO_PRESENCA = "SELECT pr.* FROM presenca pr "
            + "INNER JOIN matriculaalunohorarioturma maht ON maht.codigo = pr.dadosturma "
            + "WHERE maht.pessoa = ? AND datapresenca >= ? AND datapresenca <= ?";
    public static final String SQL_CONSULTAR_PESSOA_HT_PERIODO_PRESENCA = "SELECT pr.* FROM presenca pr "
            + "INNER JOIN matriculaalunohorarioturma maht ON maht.codigo = pr.dadosturma "
            + "WHERE maht.pessoa = ? AND maht.horarioturma = ? AND datapresenca >= ? AND datapresenca <= ?";
    public static final String SQL_CONTAR_PRESENCA_ALUNO_TURMA = "select count(codigo) from presenca where "
            + "dadosturma = %s and datapresenca is not null  ";

    public Presenca() throws Exception {
        super();
    }

    public Presenca(Connection conexao) throws Exception {
        super(conexao);
    }

    private void validarReposicaoNoMesmoDia(final PresencaVO p) throws Exception {
        MatriculaAlunoHorarioTurma matriculaAlunoHorarioTurmaDAO = new MatriculaAlunoHorarioTurma(con);
        try{
            if (p.getRepo() == null) {
                MatriculaAlunoHorarioTurmaVO matAluno = matriculaAlunoHorarioTurmaDAO.consultarPorChavePrimaria(p.getDadosTurma(), Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA);
                String consulta = String.format("select codigo from reposicao "
                                + "where horarioturmaorigem = %s and cast(dataorigem as date) = '%s' and contrato = %s",
                        new Object[]{
                                matAluno.getHorarioTurma().getCodigo(),
                                Uteis.getDataFormatoBD(p.getDataPresenca()),
                                matAluno.getContrato().getCodigo()
                        });

                if (existe(consulta, con)) {
                    throw new ConsistirException("O Aluno já marcou uma Reposição para esta aula. "
                            + "Não é possível registrar a Presença nesta aula.");
                }
            }
        }catch(Exception e){
            e.getStackTrace();
        }finally {
            matriculaAlunoHorarioTurmaDAO = null;
        }
    }

    @Override
    public void incluir(PresencaVO presenca) throws Exception {
        try {
            con.setAutoCommit(false);
            presenca.validarDados();
            validarReposicaoNoMesmoDia(presenca);
            incluirSemCommit(presenca);
            con.commit();
        } catch (Exception e) {
            con.rollback();
            throw e;
        } finally {
            con.setAutoCommit(true);
        }
    }

    @Override
    public void incluirSemCommit(PresencaVO presenca) throws Exception {
        PreparedStatement sqlInserir = con.prepareStatement(SQL_INSERIR);
        if (presenca.getDadosTurma() != 0) {
            sqlInserir.setInt(1, presenca.getDadosTurma());
        } else {
            sqlInserir.setNull(1, Types.NULL);
        }
        sqlInserir.setDate(2, Uteis.getDataJDBC(presenca.getDataChamada()));
        sqlInserir.setDate(3, Uteis.getDataJDBC(presenca.getDataPresenca()));

        sqlInserir.execute();
        presenca.setCodigo(obterValorChavePrimariaCodigo());
        presenca.setNovoObj(false);
    }

    @Override
    public void alterar(PresencaVO presenca) throws Exception {
        try {
            con.setAutoCommit(false);
            presenca.validarDados();
            alterarSemCommit(presenca);
            con.commit();
        } catch (Exception e) {
            con.rollback();
            throw e;
        } finally {
            con.setAutoCommit(true);
        }
    }

    @Override
    public void alterarSemCommit(PresencaVO presenca) throws Exception {
        PreparedStatement sqlAlterar = con.prepareStatement(SQL_ALTERAR);
        if (presenca.getDadosTurma() != 0) {
            sqlAlterar.setInt(1, presenca.getDadosTurma());
        } else {
            sqlAlterar.setNull(1, Types.NULL);
        }
        sqlAlterar.setDate(2, Uteis.getDataJDBC(presenca.getDataChamada()));
        sqlAlterar.setDate(3, Uteis.getDataJDBC(presenca.getDataPresenca()));
        sqlAlterar.setInt(4, presenca.getCodigo());
        sqlAlterar.execute();
    }

    @Override
    public void excluir(PresencaVO presenca) throws Exception {
        try {
            con.setAutoCommit(false);
            excluirSemCommit(presenca);
            con.commit();
        } catch (Exception e) {
            con.rollback();
            throw e;
        } finally {
            con.setAutoCommit(true);
        }
    }

    @Override
    public void excluirSemCommit(PresencaVO presenca) throws Exception {
        PreparedStatement sqlExcluir = con.prepareStatement(SQL_EXCLUIR_CODIGO);
        sqlExcluir.setInt(1, presenca.getCodigo());
        sqlExcluir.execute();
    }

    @Override
    public int contarPorPessoaPeriodoPresenca(int codigoPessoa, int codigoHorarioTurma, Date dataInicial, Date dataFinal) throws Exception {
        PreparedStatement sqlConsultar = con.prepareStatement(SQL_CONTAR_PRESENCA_ALUNO_HT_PERIODO);
        sqlConsultar.setInt(1, codigoPessoa);
        sqlConsultar.setInt(2, codigoHorarioTurma);
        sqlConsultar.setDate(3, Uteis.getDataJDBC(dataInicial));
        sqlConsultar.setDate(4, Uteis.getDataJDBC(dataFinal));
        ResultSet tabelaResultado = sqlConsultar.executeQuery();
        if (tabelaResultado.next()) {
            return tabelaResultado.getInt("qtde");
        }
        return 0;
    }

    @Override
    public int contarPresencasAluno(final int codMatAlunoHorarioTurma, Date periodoInicial, Date periodoFinal) throws Exception {
        String condicaoPeriodo = "";
        if (periodoInicial != null && periodoFinal != null) {
            condicaoPeriodo = "AND datapresenca >= '" + Uteis.getDataJDBC(periodoInicial) + "' AND datapresenca <= '" + Uteis.getDataJDBC(periodoFinal) + "' ";
        }
        return SuperFacadeJDBC.contar(String.format(SQL_CONTAR_PRESENCA_ALUNO_TURMA,
                new Object[]{
                    codMatAlunoHorarioTurma
                }) + condicaoPeriodo, con);
    }

    @Override
    public PresencaVO consultarPorChavePrimaria(int codigo, int nivelMontarDados) throws Exception {
        PreparedStatement sqlConsultar = con.prepareStatement(SQL_CONSULTAR_CHAVE_PRIMARIA);
        sqlConsultar.setInt(1, codigo);
        ResultSet tabelaResultado = sqlConsultar.executeQuery();
        return montarDados(tabelaResultado, nivelMontarDados, this.con);
    }

    @Override
    public PresencaVO consultarPorPessoaHorarioPeriodoPresenca(int codigoPessoa, int codigoHorarioTurma, Date dataInicial, Date dataFinal, int nivelMontarDados) throws Exception {
        PreparedStatement sqlConsultar = con.prepareStatement(SQL_CONSULTAR_PESSOA_HT_PERIODO_PRESENCA);
        sqlConsultar.setInt(1, codigoPessoa);
        sqlConsultar.setInt(2, codigoHorarioTurma);
        sqlConsultar.setDate(3, Uteis.getDataJDBC(dataInicial));
        sqlConsultar.setDate(4, Uteis.getDataJDBC(dataFinal));
        ResultSet tabelaResultado = sqlConsultar.executeQuery();
        if (tabelaResultado.next()) {
            return montarDados(tabelaResultado, nivelMontarDados, this.con);
        } else {
            return new PresencaVO();
        }
    }

    @Override
    public List<PresencaVO> montarDadosConsulta(ResultSet tabelaResultado, int nivelMontarDados, Connection con) throws Exception {
        List<PresencaVO> resultado = new ArrayList<PresencaVO>();
        while (tabelaResultado.next()) {
            resultado.add(montarDados(tabelaResultado, nivelMontarDados, con));
        }
        return resultado;
    }

    @Override
    public PresencaVO montarDados(ResultSet resultado, int nivelMontarDados, Connection con) throws Exception {
        PresencaVO obj = montarDadosBasicos(resultado);
        if (nivelMontarDados == Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA) {
            return obj;
        }
        return obj;
    }

    @Override
    public PresencaVO montarDadosBasicos(ResultSet resultado) throws Exception {
        PresencaVO obj = new PresencaVO();
        obj.setNovoObj(false);
        obj.setCodigo(resultado.getInt("codigo"));
        obj.setDadosTurma(resultado.getInt("dadosturma"));
        obj.setDataChamada(resultado.getDate("datachamada"));
        obj.setDataPresenca(resultado.getDate("datapresenca"));
        obj.setPresente(true);
        obj.setAula(true);
        obj.setNovoObj(false);

        try{
            obj.setDataCadastro(resultado.getTimestamp("datacadastro"));
        }catch (Exception e){}

        return obj;
    }
    
    @Override
    public List<AgendadoJSON> consultarPresencasTVGestor(Date inicio,Date fim, Integer empresa) throws Exception{
        List<AgendadoJSON> lista = new ArrayList<AgendadoJSON>();
        StringBuilder sql = new StringBuilder();
        sql.append(" SELECT sc.codigocliente as cliente, sc.matricula, sc.telefonescliente, sc.nomecliente as nome, sc.codigopessoa as pessoa, \n");
        sql.append(" t.codigo as turma, ma.codigo as matriculahorarioturma, \n");
        sql.append(" ht.codigo as horarioturma, ma.datainicio, ma.datafim, ma.contrato, datapresenca FROM presenca p  \n");
        sql.append(" INNER JOIN matriculaalunohorarioturma ma ON ma.codigo = p.dadosturma  \n");
        sql.append(" INNER JOIN situacaoclientesinteticodw sc ON sc.codigopessoa = ma.pessoa  \n");
        sql.append(" INNER JOIN horarioturma ht ON ma.horarioturma = ht.codigo  \n");
        sql.append(" INNER JOIN turma t ON ht.turma = t.codigo  \n");
        sql.append(" where p.datapresenca BETWEEN '").append(Uteis.getDataJDBC(inicio)).append("'");
        sql.append(" AND '").append(Uteis.getDataJDBC(fim)).append("'");
        sql.append(" AND t.empresa = ").append(empresa);
        ResultSet rs = criarConsulta(sql.toString(), con);
        while(rs.next()){
            AgendadoJSON agendado = new AgendadoJSON();
            agendado.setCodigoCliente(rs.getInt("cliente"));
            agendado.setCodigoContrato(rs.getInt("contrato"));
            agendado.setMatricula(rs.getString("matricula"));
            agendado.setNome(rs.getString("nome"));
            agendado.setTelefones(rs.getString("telefonescliente"));
            agendado.setCodigoPessoa(rs.getInt("pessoa"));
            agendado.setId_agendamento(String.valueOf(rs.getInt("horarioturma")));
            agendado.setInicio(Uteis.getDataAplicandoFormatacao(rs.getDate("datapresenca"), "dd/MM/yyyy"));
            lista.add(agendado);
        }
        return lista;
    }

    @Override
    public List<PresencaVO> listaPresenca(Integer codigo) throws Exception {
        StringBuilder sql = new StringBuilder();
        sql.append(" SELECT * from presenca where dadosturma = "+codigo);
        ResultSet rs = criarConsulta(sql.toString(), con);
        return montarDadosConsulta(rs, Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA, con);
    }

    public int contarPresencasPorPeriodoModalidade(Date prmIni, Date prmFim, Integer empresa, Integer codModalidade) throws Exception {
        return contarPresencasPorPeriodoModalidade(prmIni,prmFim,empresa, codModalidade,null);
    }
    public int contarPresencasPorPeriodoModalidade(Date prmIni, Date prmFim, Integer empresa, Integer codModalidade, TurmaVO turmaSelecaoVO) throws Exception {
        String sqlStr = "SELECT count(distinct p.codigo) as qtd FROM presenca p\n" +
                "INNER JOIN matriculaalunohorarioturma maht ON maht.codigo = p.dadosturma\n" +
                "INNER JOIN horarioturma ht ON maht.horarioturma = ht.codigo\n" +
                "INNER JOIN turma t ON ht.turma = t.codigo\n" +
                "WHERE (('" + Uteis.getDataJDBC(prmIni) + "' BETWEEN t.dataInicialVigencia and t.datafinalvigencia) OR ('" + Uteis.getDataJDBC(prmFim) + "' BETWEEN t.dataInicialVigencia and t.datafinalvigencia))\n" +
                "AND p.datapresenca BETWEEN '" + Uteis.getDataJDBC(prmIni) + "' AND '" + Uteis.getDataJDBC(prmFim) + "'\n" +
                "AND t.empresa = " + empresa + "\n" +
                "AND t.modalidade = " + codModalidade + ";";
        if(turmaSelecaoVO!=null && !UteisValidacao.emptyNumber(turmaSelecaoVO.getCodigo())){
            sqlStr = sqlStr.replace(";", " AND t.codigo = " + turmaSelecaoVO.getCodigo() + ";");
        }

        try (PreparedStatement stm = con.prepareStatement(sqlStr);
             ResultSet tabelaResultado = stm.executeQuery()) {
            if (tabelaResultado.next()) {
                return tabelaResultado.getInt("qtd");
            }
        }

        return 0;
    }

    public boolean presencaJaMarcada(Date dataPresenca, Integer dadosTurma) throws Exception{
        String sqlStr = "SELECT codigo FROM presenca p\n" +
                "WHERE dadosturma = "+dadosTurma+"\n" +
                "AND p.datapresenca = '" + Uteis.getDataFormatoBD(dataPresenca) + "'\n";

        try (PreparedStatement stm = con.prepareStatement(sqlStr);
             ResultSet tabelaResultado = stm.executeQuery()) {
            if (tabelaResultado.next()) {
                return true;
            }
        }
        return  false;
    }

}

package negocio.facade.jdbc.basico;

import com.sun.xml.fastinfoset.stax.events.Util;
import negocio.facade.jdbc.arquitetura.SuperEntidade;
import org.json.JSONArray;
import org.json.JSONObject;
import servicos.propriedades.PropsService;

import java.io.BufferedReader;
import java.io.InputStreamReader;
import java.io.OutputStreamWriter;
import java.net.HttpURLConnection;
import java.net.URL;
import java.nio.charset.StandardCharsets;
import java.sql.Connection;
import java.util.logging.Level;
import java.util.logging.Logger;

public class PushMobileRunnable extends SuperEntidade {

    public PushMobileRunnable (Connection coneection) throws Exception {
        super(coneection);
    }

    public void enviarNotificacaoPush(final String ctx, final String titulo, final String message, final String usuario, final String path) {
        new Thread() {
            @Override
            public void run() {
                try {
                    String tokenPushMobile = PropsService.getPropertyValue(PropsService.tokenPushMobile);
                    String urlAppDoAlunoUnificado = PropsService.getPropertyValue(PropsService.urlAppDoAlunoUnificado);

                    JSONObject json = new JSONObject();
                    json.put("refClienteApp", ctx);
                    json.put("refUsuarioApp", ctx + "_" + usuario);
                    json.put("chaveZw", ctx);
                    json.put("titulo", titulo);
                    json.put("mensagem", message);

                    HttpURLConnection httpUrlConnection = (HttpURLConnection) new URL(urlAppDoAlunoUnificado + path).openConnection();
                    httpUrlConnection.setRequestMethod("POST");
                    httpUrlConnection.setDoOutput(true);
                    httpUrlConnection.setRequestProperty("Content-Type", "application/json");
                    httpUrlConnection.setRequestProperty("Accept", "application/json");
                    httpUrlConnection.setRequestProperty("Authorization", "Bearer " + tokenPushMobile);

                    OutputStreamWriter wr = new OutputStreamWriter(httpUrlConnection.getOutputStream(), StandardCharsets.UTF_8);
                    wr.write(json.toString());
                    wr.flush();

                    System.out.println("Response code: " + httpUrlConnection.getResponseCode());

                    InputStreamReader inputStreamReader = new InputStreamReader(httpUrlConnection.getInputStream());
                    System.out.println(new BufferedReader(inputStreamReader).readLine());

                } catch (Exception ex) {
                    Logger.getLogger(PushMobileRunnable.class.getName()).log(
                            Level.SEVERE,
                            String.format("ERRO AO ENVIAR PUSH => \nMensagem: %s\nEmail: %s", message, usuario),
                            ex
                    );
                }
            }
        }.start();
    }

    public void enviarNotificacaoPushAppAluno(final String horario, final String ctx, final String titulo, final String message, final String usuario, final String path) {
        Thread thread = new Thread(() -> {
            try {
                String tokenPushMobile = PropsService.getPropertyValue(PropsService.tokenPushMobileAppAluno);
                String urlAppDoAlunoUnificado = PropsService.getPropertyValue(PropsService.urlAppDoAlunoUnificado);


                JSONObject json = new JSONObject();
                json.put("userNameUsuario", usuario);
                json.put("chave", ctx);
                json.put("titulo", titulo);
                json.put("content", message);
                if (!Util.isEmptyString(horario)) {
                    JSONArray horarios = new JSONArray();
                    horarios.put(horario);
                    json.put("horarios", horarios);
                }

                JSONArray jsonArray = new JSONArray();
                jsonArray.put(json);

                HttpURLConnection httpUrlConnection = (HttpURLConnection) new URL(urlAppDoAlunoUnificado + path).openConnection();
                httpUrlConnection.setRequestMethod("POST");
                httpUrlConnection.setDoOutput(true);
                httpUrlConnection.setRequestProperty("Content-Type", "application/json");
                httpUrlConnection.setRequestProperty("Accept", "application/json");
                httpUrlConnection.setRequestProperty("Authorization", tokenPushMobile);

                OutputStreamWriter wr = new OutputStreamWriter(httpUrlConnection.getOutputStream(), StandardCharsets.UTF_8);
                wr.write(jsonArray.toString());
                wr.flush();

                System.out.println("Response code: " + httpUrlConnection.getResponseCode());

                InputStreamReader inputStreamReader = new InputStreamReader(httpUrlConnection.getInputStream());
                System.out.println(new BufferedReader(inputStreamReader).readLine());

            } catch (Exception ex) {
                Logger.getLogger(PushMobileRunnable.class.getName()).log(
                        Level.SEVERE,
                        String.format("ERRO AO ENVIAR PUSH => \nMensagem: %s\nEmail: %s", message, usuario),
                        ex
                );
            }
        });
        thread.start();
        Runtime.getRuntime().addShutdownHook(thread);
    }

}

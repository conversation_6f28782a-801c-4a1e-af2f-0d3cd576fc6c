/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package negocio.facade.jdbc.basico;

import br.com.pactosolucoes.comuns.json.ConfigCobrancaMensalJSON;
import br.com.pactosolucoes.enumeradores.TipoCobrancaPactoEnum;
import br.com.pactosolucoes.oamd.controle.basico.DAO;
import negocio.comuns.basico.CreditoPactoHistoricoVO;
import negocio.comuns.basico.EmpresaVO;
import negocio.comuns.basico.LogCobrancaPactoVO;
import negocio.comuns.utilitarias.Calendario;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisValidacao;
import negocio.facade.jdbc.arquitetura.SuperEntidade;
import negocio.interfaces.basico.CreditoPactoHistoricoInterfaceFacade;
import org.json.JSONObject;
import servicos.adm.CreditoDCCService;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR> Felipe
 */
public class CreditoPactoHistorico extends SuperEntidade implements CreditoPactoHistoricoInterfaceFacade {

    public CreditoPactoHistorico() throws Exception {
        super();
    }

    public CreditoPactoHistorico(Connection conexao) throws Exception {
        super(conexao);
    }

    public static List<CreditoPactoHistoricoVO> montarDadosConsulta(ResultSet rs, int nivelMontarDados, Connection con) throws Exception {
        List<CreditoPactoHistoricoVO> vetResultado = new ArrayList<>();
        while (rs.next()) {
            vetResultado.add(montarDados(rs, nivelMontarDados, con));
        }
        return vetResultado;
    }

    public static CreditoPactoHistoricoVO montarDadosBasico(ResultSet rs, int nivelMontarDados, Connection con) throws Exception {
        CreditoPactoHistoricoVO obj = new CreditoPactoHistoricoVO();
        obj.setNovoObj(false);
        obj.setCodigo(rs.getInt("codigo"));
        obj.setDataRegistro(Uteis.getDataJDBCTimestamp(rs.getTimestamp("dataRegistro")));
        obj.setDataAtualizacao(Uteis.getDataJDBCTimestamp(rs.getTimestamp("dataAtualizacao")));
        obj.setDataRegistro(rs.getDate("dataReferencia"));
        obj.getEmpresaVO().setCodigo(rs.getInt("empresa"));
        obj.setTipoCobrancaPacto(TipoCobrancaPactoEnum.getConsultarPorCodigo(rs.getInt("tipocobrancapacto")));
        obj.setQtdTotal(rs.getInt("qtdtotal"));
        obj.setValorTotal(rs.getDouble("valorTotal"));
        obj.setValorMedioUnitario(rs.getDouble("valorMedioUnitario"));
        return obj;
    }

    public static CreditoPactoHistoricoVO montarDados(ResultSet dadosSQL, int nivelMontarDados, Connection con) throws Exception {
        return montarDadosBasico(dadosSQL, nivelMontarDados, con);
    }

    public void incluir(CreditoPactoHistoricoVO obj) throws Exception {
        String sql = "INSERT INTO CreditoPactoHistorico (dataRegistro, dataAtualizacao, dataReferencia,"
                + "empresa, tipocobrancapacto, qtdtotal, valortotal, valorMedioUnitario) " +
                " VALUES (?,?,?,?,?,?,?,?);";
        PreparedStatement sqlInserir = con.prepareStatement(sql);
        int i = 0;
        sqlInserir.setTimestamp(++i, Uteis.getDataJDBCTimestamp(obj.getDataRegistro()));
        sqlInserir.setTimestamp(++i, Uteis.getDataJDBCTimestamp(obj.getDataAtualizacao()));
        sqlInserir.setDate(++i, Uteis.getDataJDBC(Uteis.obterPrimeiroDiaMes(obj.getDataReferencia())));
        sqlInserir.setInt(++i, obj.getEmpresaVO().getCodigo());
        sqlInserir.setInt(++i, obj.getTipoCobrancaPacto().getCodigo());
        sqlInserir.setInt(++i, obj.getQtdTotal());
        sqlInserir.setDouble(++i, Uteis.arredondarForcando2CasasDecimais(obj.getValorTotal()));
        sqlInserir.setDouble(++i, Uteis.arredondarForcando2CasasDecimais(obj.getValorMedioUnitario()));
        sqlInserir.execute();
        obj.setCodigo(obterValorChavePrimariaCodigo());
        obj.setNovoObj(false);
    }

    public void alterar(CreditoPactoHistoricoVO obj) throws Exception {
        String sql = "UPDATE CreditoPactoHistorico SET dataAtualizacao = ?, dataReferencia = ?, "
                + "empresa = ?, tipocobrancapacto = ?, qtdtotal = ?, valortotal = ?, valorMedioUnitario = ? "
                + " WHERE codigo = ? ";
        PreparedStatement sqlAlterar = con.prepareStatement(sql);
        int i = 0;
        sqlAlterar.setTimestamp(++i, Uteis.getDataJDBCTimestamp(obj.getDataAtualizacao()));
        sqlAlterar.setDate(++i, Uteis.getDataJDBC(Uteis.obterPrimeiroDiaMes(obj.getDataReferencia())));
        sqlAlterar.setInt(++i, obj.getEmpresaVO().getCodigo());
        sqlAlterar.setInt(++i, obj.getTipoCobrancaPacto().getCodigo());
        sqlAlterar.setInt(++i, obj.getQtdTotal());
        sqlAlterar.setDouble(++i, Uteis.arredondarForcando2CasasDecimais(obj.getValorTotal()));
        sqlAlterar.setDouble(++i, Uteis.arredondarForcando2CasasDecimais(obj.getValorMedioUnitario()));
        sqlAlterar.setInt(++i, obj.getCodigo());
        sqlAlterar.execute();
    }

    public CreditoPactoHistoricoVO consultarPorEmpresaMesReferencia(Integer empresa, Date mesReferencia, int nivelMontarDados) throws Exception {
        StringBuilder sql = new StringBuilder();
        sql.append("SELECT \n");
        sql.append("* \n");
        sql.append("FROM CreditoPactoHistorico \n");
        sql.append("WHERE empresa = ").append(empresa).append(" \n");
        sql.append("and datareferencia::date = '").append(Uteis.getDataFormatoBD(Uteis.obterPrimeiroDiaMes(mesReferencia))).append("' \n");

        PreparedStatement sqlConsulta = con.prepareStatement(sql.toString());
        ResultSet resultado = sqlConsulta.executeQuery();
        if (resultado.next()) {
            return montarDados(resultado, nivelMontarDados, con);
        }
        return null;
    }

    public void processarTodasEmpresas(Date mesReferencia) {
        Empresa empresaDAO;
        try {
            Uteis.logarDebug("INICIO | Processamento CreditoPactoHistorico!");
            empresaDAO = new Empresa(this.con);
            List<EmpresaVO> empresas = empresaDAO.consultarEmpresas();
            for (EmpresaVO empresaVO : empresas) {
                processarCreditoPactoHistorico(empresaVO.getCodigo(), mesReferencia);
            }
        } catch (Exception ex) {
            ex.printStackTrace();
        } finally {
            empresaDAO = null;
            Uteis.logarDebug("FIM | Processamento CreditoPactoHistorico!");
        }
    }

    public void processarCreditoPactoHistorico(Integer empresa, Date mesReferencia) {
        Empresa empresaDAO;
        LogCobrancaPacto logCobrancaPactoDAO;
        CreditoDCCService creditoDCCService;
        try {
            empresaDAO = new Empresa(this.con);
            logCobrancaPactoDAO = new LogCobrancaPacto(this.con);
            creditoDCCService = new CreditoDCCService(this.con);

            if (mesReferencia == null) {
                mesReferencia = Calendario.hoje();
            }
            //sempre colocar primeiro dia do mes
            mesReferencia = Uteis.obterPrimeiroDiaMes(mesReferencia);


            String key = DAO.resolveKeyFromConnection(this.con);
            EmpresaVO empresaVO = empresaDAO.consultarPorChavePrimaria(empresa, Uteis.NIVELMONTARDADOS_GESTAOREMESSA);

            CreditoPactoHistoricoVO historicoVO = consultarPorEmpresaMesReferencia(empresaVO.getCodigo(), mesReferencia, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
            if (historicoVO == null) {
                historicoVO = new CreditoPactoHistoricoVO();
                historicoVO.setDataRegistro(Calendario.hoje());
                historicoVO.setEmpresaVO(empresaVO);
            } else {
                historicoVO.setDataAtualizacao(Calendario.hoje());
            }
            historicoVO.setDataReferencia(mesReferencia);
            historicoVO.setTipoCobrancaPacto(TipoCobrancaPactoEnum.getConsultarPorCodigo(empresaVO.getTipoCobrancaPacto()));

            //limpar para reprocessar os totais
            historicoVO.setQtdTotal(0);
            historicoVO.setValorTotal(0.0);
            historicoVO.setValorMedioUnitario(0.0);

            List<LogCobrancaPactoVO> lista = logCobrancaPactoDAO.consultar(empresaVO.getCodigo(), Uteis.obterPrimeiroDiaMes(mesReferencia),
                    Uteis.obterUltimoDiaMes(mesReferencia),
                    null, null, Uteis.NIVELMONTARDADOS_DADOSBASICOS);

            for (LogCobrancaPactoVO obj : lista) {
                try {
                    Integer qtdTotal = 0;
                    Double valorTotal = 0.0;
                    if (obj.getTipoCobrancaPacto().equals(TipoCobrancaPactoEnum.POS_PAGO_EFETIVADO_MENSAL.getCodigo()) ||
                            obj.getTipoCobrancaPacto().equals(TipoCobrancaPactoEnum.POS_PAGO_EFETIVADO_MENSAL_REDE_EMPRESAS.getCodigo())) {

                        JSONObject jsonCobranca = new JSONObject(obj.getJsonCobranca());

                        valorTotal = jsonCobranca.getDouble("valorMensal");
                        qtdTotal = jsonCobranca.getInt("qtdMaximaCredito");


//                    } else if (obj.getTipoCobrancaPacto().equals(TipoCobrancaPactoEnum.POS_PAGO_EFETIVADO.getCodigo()) ||
//                            obj.getTipoCobrancaPacto().equals(TipoCobrancaPactoEnum.PRE_PAGO.getCodigo()) ||
//                            obj.getTipoCobrancaPacto().equals(TipoCobrancaPactoEnum.POS_PAGO_TENTATIVA.getCodigo())){
                    } else {
                        valorTotal = obj.getValorTotal();
                        qtdTotal = obj.getQuantidade();
                    }

                    //preencher histórico
                    if (!UteisValidacao.emptyNumber(valorTotal) &&
                            !UteisValidacao.emptyNumber(qtdTotal)) {
                        historicoVO.setValorTotal(historicoVO.getValorTotal() + valorTotal);
                        historicoVO.setQtdTotal(historicoVO.getQtdTotal() + qtdTotal);
                        historicoVO.setValorMedioUnitario(historicoVO.getValorTotal() / historicoVO.getQtdTotal());
                    }

                    obj.setValorMedioUnitario(valorTotal / qtdTotal);
                    if (!UteisValidacao.emptyNumber(obj.getValorMedioUnitario())) {
                        logCobrancaPactoDAO.alterarValorMedioUnitario(obj);
                    }
                } catch (Exception ex) {
                    ex.printStackTrace();
                }
            }

            try {
                if (empresaVO.getTipoCobrancaPactoPosPagoMensal() &&
                        UteisValidacao.emptyNumber(historicoVO.getValorMedioUnitario())) {
                    if (Calendario.dataNoMesmoMesAno(mesReferencia, Calendario.hoje())) {
                        ConfigCobrancaMensalJSON configJSON = creditoDCCService.consultarConfigCobrancaMensalJSON(key, empresaVO);
                        historicoVO.setQtdTotal(configJSON.getQtdMaxima());
                        historicoVO.setValorTotal(configJSON.getValorMensal());
                        historicoVO.setValorMedioUnitario(configJSON.getValorMensal() / configJSON.getQtdMaxima());
                    }
                }
            } catch (Exception ex) {
                ex.printStackTrace();
            }

            if (UteisValidacao.emptyNumber(historicoVO.getCodigo())) {
                incluir(historicoVO);
            } else {
                alterar(historicoVO);
            }
        } catch (Exception ex) {
            ex.printStackTrace();
        } finally {
            empresaDAO = null;
            logCobrancaPactoDAO = null;
            creditoDCCService = null;
        }
    }

//    public static void main(String[] args) {
//        CreditoPactoHistorico creditoPactoHistoricoDAO;
//        try {
//            Connection con = DriverManager.getConnection("*******************************************", "postgres", "pactodb");
//
//            SuperControle superControle = new SuperControle();
//            Conexao.guardarConexaoForJ2SE(con);
//            AtualizadorBD atualizador = new AtualizadorBD(con);
//            try {
//                atualizador.atualizar(superControle.getVersaoBD(), 1);
//            } catch (Exception ignored) {
//            }
//
//            creditoPactoHistoricoDAO = new CreditoPactoHistorico(con);
//            List<Date> mesesProcessar = Uteis.getMesesEntreDatas(Calendario.somarMeses(Calendario.hoje(), -6), Calendario.hoje());
//            for (Date mesReferencia : mesesProcessar) {
//                creditoPactoHistoricoDAO.processarTodasEmpresas(mesReferencia);
//            }
//        } catch (Exception ex) {
//            ex.printStackTrace();
//        } finally {
//            creditoPactoHistoricoDAO = null;
//        }
//    }
}

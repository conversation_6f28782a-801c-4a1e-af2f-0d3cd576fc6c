package negocio.facade.jdbc.basico;

import negocio.comuns.basico.ConfiguracaoSorteioVO;
import negocio.comuns.crm.GenericoTO;
import negocio.comuns.plano.PlanoVO;
import negocio.facade.jdbc.arquitetura.SuperEntidade;
import negocio.interfaces.basico.ConfiguracaoSorteioInterfaceFacade;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;

/**
 * Created by GlaucoT on 16/02/2016
 */
public class ConfiguracaoSorteio extends SuperEntidade implements ConfiguracaoSorteioInterfaceFacade {
    public ConfiguracaoSorteio() throws Exception {
    }

    public ConfiguracaoSorteio(Connection conexao) throws Exception {
        super(conexao);
    }

    public ConfiguracaoSorteioVO obterConfiguracao(Integer codEmpresa) throws Exception {
        String sql = "SELECT * FROM configuracaosorteio WHERE empresa = ?";
        PreparedStatement ps = getCon().prepareStatement(sql);
        ps.setInt(1, codEmpresa);
        ResultSet rs = ps.executeQuery();
        if (rs.next()) {
            ConfiguracaoSorteioVO configuracaoSorteioVO = new ConfiguracaoSorteioVO();
            configuracaoSorteioVO.setNovoObj(false);
            configuracaoSorteioVO.getEmpresaVO().setCodigo(rs.getInt("empresa"));
            configuracaoSorteioVO.setCodigo(rs.getInt("codigo"));
            String planos = rs.getString("planos");
            if (planos != null && !planos.isEmpty()) {
                String[] planosTmp = planos.split("\\|:");
                for (String plano : planosTmp) {
                    if (!plano.isEmpty()) {
                        String[] codDescricao = plano.split(",");
                        PlanoVO planoVO = new PlanoVO();
                        planoVO.setCodigo(Integer.parseInt(codDescricao[0]));
                        planoVO.setDescricao(codDescricao[1]);
                        planoVO.setSelecionado(true);
                        configuracaoSorteioVO.getPlanoVOs().add(planoVO);
                    }
                }
            }


            String situacoes = rs.getString("situacoescliente");
            if (situacoes != null && !situacoes.isEmpty()) {
                String[] situacoesTmp = situacoes.split("\\|:");
                for (String situacao : situacoesTmp) {
                    if (!situacao.isEmpty()) {
                        String[] codDescricao = situacao.split(",");
                        GenericoTO genericoTO = new GenericoTO(codDescricao[0], codDescricao[1]);
                        genericoTO.setSelecionado(true);
                        configuracaoSorteioVO.getSituacoesCliente().add(genericoTO);
                    }
                }
            }

            return configuracaoSorteioVO;
        }
        return new ConfiguracaoSorteioVO();
    }

    public void incluir(ConfiguracaoSorteioVO config) throws Exception {
        String sql = "INSERT INTO configuracaosorteio(empresa, situacoescliente, planos) VALUES (?, ?, ?)";
        PreparedStatement sqlInserir = con.prepareStatement(sql);
        int i = 0;
        sqlInserir.setInt(++i, config.getEmpresaVO().getCodigo());
        sqlInserir.setString(++i, config.getSituacoes());
        sqlInserir.setString(++i, config.getPlanos());
        sqlInserir.execute();
        config.setCodigo(obterValorChavePrimariaCodigo());
        config.setNovoObj(false);
    }

    public void alterar(ConfiguracaoSorteioVO config) throws Exception {
        String sql = "UPDATE configuracaosorteio SET situacoescliente = ?, planos = ? WHERE codigo = ?";
        PreparedStatement sqlAlterar = con.prepareStatement(sql);
        int i = 0;
        sqlAlterar.setString(++i, config.getSituacoes());
        sqlAlterar.setString(++i, config.getPlanos());
        sqlAlterar.setInt(++i, config.getCodigo());
        sqlAlterar.execute();
    }
}

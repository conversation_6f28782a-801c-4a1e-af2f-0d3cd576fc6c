package negocio.facade.jdbc.basico;

import negocio.comuns.basico.GrupoVO;
import java.util.Iterator;
import negocio.comuns.basico.ClienteGrupoVO;
import negocio.facade.jdbc.arquitetura.*;

import java.sql.Connection;
import java.sql.ResultSet;
import java.sql.PreparedStatement;
import java.sql.Statement;
import java.util.List;
import java.util.ArrayList;
import negocio.comuns.utilitarias.*;

/**
 * Classe de persistência que encapsula todas as operações de manipulação dos dados da classe <code>ClienteGrupoVO</code>.
 * Responsável por implementar operações como incluir, alterar, excluir e consultar pertinentes a classe <code>ClienteGrupoVO</code>.
 * Encapsula toda a interação com o banco de dados.
 * @see ClienteGrupoVO
 * @see SuperEntidade
 * @see Cliente
 */
public class ClienteGrupo extends SuperEntidade {    

    public ClienteGrupo() throws Exception {
        super();        
        setIdEntidade("Cliente");
    }

    public ClienteGrupo(Connection con) throws Exception {
		super(con);
	}

	/**
     * Operação responsável por retornar um novo objeto da classe <code>ClienteGrupoVO</code>.
     */
    public ClienteGrupoVO novo() throws Exception {
        incluir(getIdEntidade());
        ClienteGrupoVO obj = new ClienteGrupoVO();
        return obj;
    }

    /**
     * Operação responsável por incluir no banco de dados um objeto da classe <code>ClienteGrupoVO</code>.
     * Primeiramente valida os dados (<code>validarDados</code>) do objeto. Verifica a conexão com o banco de dados e a permissão do usuário
     * para realizar esta operacão na entidade.
     * Isto, através da operação <code>incluir</code> da superclasse.
     * @param obj  Objeto da classe <code>ClienteGrupoVO</code> que será gravado no banco de dados.
     * @exception Exception Caso haja problemas de conexão, restrição de acesso ou validação de dados.
     */
    public void incluir(ClienteGrupoVO obj) throws Exception {
        ClienteGrupoVO.validarDados(obj);
        incluir(getIdEntidade());
        obj.realizarUpperCaseDados();
        String sql = "INSERT INTO ClienteGrupo( grupo, cliente ) VALUES ( ?, ? )";
        PreparedStatement sqlInserir = con.prepareStatement(sql);
        if (obj.getGrupo().getCodigo().intValue() != 0) {
            sqlInserir.setInt(1, obj.getGrupo().getCodigo().intValue());
        } else {
            sqlInserir.setNull(1, 0);
        }
        if (obj.getCliente().intValue() != 0) {
            sqlInserir.setInt(2, obj.getCliente().intValue());
        } else {
            sqlInserir.setNull(2, 0);
        }
        sqlInserir.execute();
        obj.setCodigo(obterValorChavePrimariaCodigo());
        obj.setNovoObj(new Boolean(false));
    }

    /**
     * Operação responsável por alterar no BD os dados de um objeto da classe <code>ClienteGrupoVO</code>.
     * Sempre utiliza a chave primária da classe como atributo para localização do registro a ser alterado.
     * Primeiramente valida os dados (<code>validarDados</code>) do objeto. Verifica a conexão com o banco de dados e a permissão do usuário
     * para realizar esta operacão na entidade.
     * Isto, através da operação <code>alterar</code> da superclasse.
     * @param obj    Objeto da classe <code>ClienteGrupoVO</code> que será alterada no banco de dados.
     * @exception Execption Caso haja problemas de conexão, restrição de acesso ou validação de dados.
     */
    public void alterar(ClienteGrupoVO obj) throws Exception {
        ClienteGrupoVO.validarDados(obj);
        alterar(getIdEntidade());
        obj.realizarUpperCaseDados();
        String sql = "UPDATE ClienteGrupo set grupo=?, cliente=? WHERE ((codigo = ?))";
        PreparedStatement sqlAlterar = con.prepareStatement(sql);
        if (obj.getGrupo().getCodigo().intValue() != 0) {
            sqlAlterar.setInt(1, obj.getGrupo().getCodigo().intValue());
        } else {
            sqlAlterar.setNull(1, 0);
        }
        if (obj.getCliente().intValue() != 0) {
            sqlAlterar.setInt(2, obj.getCliente().intValue());
        } else {
            sqlAlterar.setNull(2, 0);
        }
        sqlAlterar.setInt(3, obj.getCodigo().intValue());
        sqlAlterar.execute();
    }

    /**
     * Operação responsável por excluir no BD um objeto da classe <code>ClienteGrupoVO</code>.
     * Sempre localiza o registro a ser excluído através da chave primária da entidade.
     * Primeiramente verifica a conexão com o banco de dados e a permissão do usuário
     * para realizar esta operacão na entidade.
     * Isto, através da operação <code>excluir</code> da superclasse.
     * @param obj    Objeto da classe <code>ClienteGrupoVO</code> que será removido no banco de dados.
     * @exception Execption Caso haja problemas de conexão ou restrição de acesso.
     */
    public void excluir(ClienteGrupoVO obj) throws Exception {
        excluir(getIdEntidade());
        String sql = "DELETE FROM ClienteGrupo WHERE ((codigo = ?))";
        PreparedStatement sqlExcluir = con.prepareStatement(sql);
        sqlExcluir.setInt(1, obj.getCodigo().intValue());
        sqlExcluir.execute();
    }

    /**
     * Responsável por realizar uma consulta de <code>ClienteGrupo</code> através do valor do atributo 
     * <code>matricula</code> da classe <code>Cliente</code>
     * Faz uso da operação <code>montarDadosConsulta</code> que realiza o trabalho de prerarar o List resultante.
     * @return  List Contendo vários objetos da classe <code>ClienteGrupoVO</code> resultantes da consulta.
     * @exception Execption Caso haja problemas de conexão ou restrição de acesso.
     */
    public List consultarPorMatriculaCliente(String valorConsulta, int nivelMontarDados) throws Exception {
        consultar(getIdEntidade(), true);
        String sqlStr = "SELECT ClienteGrupo.* FROM ClienteGrupo, Cliente WHERE ClienteGrupo.cliente = Cliente.codigo and upper( Cliente.matricula ) like('" + valorConsulta.toUpperCase() + "%') ORDER BY Cliente.matricula";
        Statement stm = con.createStatement();
        ResultSet tabelaResultado = stm.executeQuery(sqlStr);
        return montarDadosConsulta(tabelaResultado, nivelMontarDados, this.con);
    }

    /**
     * Responsável por realizar uma consulta de <code>ClienteGrupo</code> através do valor do atributo 
     * <code>descricao</code> da classe <code>Grupo</code>
     * Faz uso da operação <code>montarDadosConsulta</code> que realiza o trabalho de prerarar o List resultante.
     * @return  List Contendo vários objetos da classe <code>ClienteGrupoVO</code> resultantes da consulta.
     * @exception Execption Caso haja problemas de conexão ou restrição de acesso.
     */
    public List consultarPorDescricaoGrupo(String valorConsulta, int nivelMontarDados) throws Exception {
        consultar(getIdEntidade(), true);
        String sqlStr = "SELECT ClienteGrupo.* FROM ClienteGrupo, Grupo WHERE ClienteGrupo.grupo = Grupo.codigo and upper( Grupo.descricao ) like('" + valorConsulta.toUpperCase() + "%') ORDER BY Grupo.descricao";
        Statement stm = con.createStatement();
        try (ResultSet tabelaResultado = stm.executeQuery(sqlStr)) {
            return montarDadosConsulta(tabelaResultado, nivelMontarDados, this.con);
        }
    }

    /**
     * Responsável por realizar uma consulta de <code>ClienteGrupo</code> através do valor do atributo 
     * <code>Integer codigo</code>. Retorna os objetos com valores iguais ou superiores ao parâmetro fornecido.
     * Faz uso da operação <code>montarDadosConsulta</code> que realiza o trabalho de prerarar o List resultante.
     * @param   controlarAcesso Indica se a aplicação deverá verificar se o usuário possui permissão para esta consulta ou não.
     * @return  List Contendo vários objetos da classe <code>ClienteGrupoVO</code> resultantes da consulta.
     * @exception Exception Caso haja problemas de conexão ou restrição de acesso.
     */
    public List consultarPorCodigo(Integer valorConsulta, boolean controlarAcesso, int nivelMontarDados) throws Exception {
        consultar(getIdEntidade(), controlarAcesso);
        String sqlStr = "SELECT * FROM ClienteGrupo WHERE codigo >= " + valorConsulta.intValue() + " ORDER BY codigo";
        Statement stm = con.createStatement();
        try (ResultSet tabelaResultado = stm.executeQuery(sqlStr)) {
            return (montarDadosConsulta(tabelaResultado, nivelMontarDados, this.con));
        }
    }

    /**
     * Responsável por montar os dados de vários objetos, resultantes de uma consulta ao banco de dados (<code>ResultSet</code>).
     * Faz uso da operação <code>montarDados</code> que realiza o trabalho para um objeto por vez.
     * @return  List Contendo vários objetos da classe <code>ClienteGrupoVO</code> resultantes da consulta.
     */
    public static List montarDadosConsulta(ResultSet tabelaResultado, int nivelMontarDados, Connection con) throws Exception {
        List vetResultado = new ArrayList();
        while (tabelaResultado.next()) {
            ClienteGrupoVO obj = new ClienteGrupoVO();
            obj = montarDados(tabelaResultado, nivelMontarDados, con);
            vetResultado.add(obj);
        }
        return vetResultado;
    }

    /**
     * Responsável por montar os dados resultantes de uma consulta ao banco de dados (<code>ResultSet</code>)
     * em um objeto da classe <code>ClienteGrupoVO</code>.
     * @return  O objeto da classe <code>ClienteGrupoVO</code> com os dados devidamente montados.
     */
    public static ClienteGrupoVO montarDados(ResultSet dadosSQL, int nivelMontarDados, Connection con) throws Exception {
        ClienteGrupoVO obj = new ClienteGrupoVO();
        obj.setCodigo(new Integer(dadosSQL.getInt("codigo")));
        obj.getGrupo().setCodigo(new Integer(dadosSQL.getInt("grupo")));
        obj.setCliente(new Integer(dadosSQL.getInt("cliente")));
        obj.setNovoObj(new Boolean(false));
//        if (nivelMontarDados == Uteis.NIVELMONTARDADOS_DADOSBASICOS) {
//            return obj;
//        }

        montarDadosGrupo(obj, Uteis.NIVELMONTARDADOS_DADOSBASICOS, con);
        return obj;
    }

    /**
     * Operação responsável por montar os dados de um objeto da classe <code>GrupoVO</code> relacionado ao objeto <code>ClienteGrupoVO</code>.
     * Faz uso da chave primária da classe <code>GrupoVO</code> para realizar a consulta.
     * @param obj  Objeto no qual será montado os dados consultados.
     */
    public static void montarDadosGrupo(ClienteGrupoVO obj, int nivelMontarDados, Connection con) throws Exception {
        if (obj.getGrupo().getCodigo().intValue() == 0) {
            obj.setGrupo(new GrupoVO());
            return;
        }
        Grupo grupo = new Grupo(con);
        obj.setGrupo(grupo.consultarPorChavePrimaria(obj.getGrupo().getCodigo(), nivelMontarDados));
        grupo = null;
    }

    /**
     * Operação responsável por excluir todos os objetos da <code>ClienteGrupoVO</code> no BD.
     * Faz uso da operação <code>excluir</code> disponível na classe <code>ClienteGrupo</code>.
     * @param <code>cliente</code> campo chave para exclusão dos objetos no BD.
     * @exception Exception  Erro de conexão com o BD ou restrição de acesso a esta operação.
     */
    public void excluirClienteGrupos(Integer cliente) throws Exception {
        excluir(getIdEntidade());
        String sql = "DELETE FROM ClienteGrupo WHERE (cliente = ?)";
        PreparedStatement sqlExcluir = con.prepareStatement(sql);
        sqlExcluir.setInt(1, cliente.intValue());
        sqlExcluir.execute();
    }

    /**
     * Operação responsável por alterar todos os objetos da <code>ClienteGrupoVO</code> contidos em um Hashtable no BD.
     * Faz uso da operação <code>excluirClienteGrupos</code> e <code>incluirClienteGrupos</code> disponíveis na classe <code>ClienteGrupo</code>.
     * @param objetos  List com os objetos a serem alterados ou incluídos no BD.
     * @exception Exception  Erro de conexão com o BD ou restrição de acesso a esta operação.
     */
    public void alterarClienteGrupos(Integer cliente, List objetos) throws Exception {
        String str = "DELETE FROM ClienteGrupo WHERE cliente = " + cliente.intValue();
        Iterator i = objetos.iterator();
        while (i.hasNext()) {
            ClienteGrupoVO objeto = (ClienteGrupoVO) i.next();
            str += " AND codigo <> " + objeto.getCodigo().intValue();
        }
        PreparedStatement sqlExcluir = con.prepareStatement(str);
        sqlExcluir.execute();
        Iterator e = objetos.iterator();
        while (e.hasNext()) {
            ClienteGrupoVO obj = (ClienteGrupoVO) e.next();
            if (obj.getCodigo().equals(new Integer(0))) {
                obj.setCliente(cliente);
                incluir(obj);
            } else {
                alterar(obj);
            }
        }
    //       excluirClienteGrupos( cliente );
//        incluirClienteGrupos( cliente, objetos );
    }

    /**
     * Operação responsável por incluir objetos da <code>ClienteGrupoVO</code> no BD.
     * Garantindo o relacionamento com a entidade principal <code>basico.Cliente</code> através do atributo de vínculo.
     * @param objetos List contendo os objetos a serem gravados no BD da classe.
     * @exception Exception  Erro de conexão com o BD ou restrição de acesso a esta operação.
     */
    public void incluirClienteGrupos(Integer clientePrm, List objetos) throws Exception {
        Iterator e = objetos.iterator();
        while (e.hasNext()) {
            ClienteGrupoVO obj = (ClienteGrupoVO) e.next();
            obj.setCliente(clientePrm);
            incluir(obj);
        }
    }

    /**
     * Operação responsável por consultar todos os <code>ClienteGrupoVO</code> relacionados a um objeto da classe <code>basico.Cliente</code>.
     * @param cliente  Atributo de <code>basico.Cliente</code> a ser utilizado para localizar os objetos da classe <code>ClienteGrupoVO</code>.
     * @return List  Contendo todos os objetos da classe <code>ClienteGrupoVO</code> resultantes da consulta.
     * @exception Exception  Erro de conexão com o BD ou restrição de acesso a esta operação.
     */
    public List consultarClienteGrupos(Integer cliente, int nivelMontarDados) throws Exception {
        consultar(getIdEntidade());
        List objetos = new ArrayList();
        String sql = "SELECT * FROM ClienteGrupo WHERE cliente = ?";
        try (PreparedStatement sqlConsulta = con.prepareStatement(sql)) {
            sqlConsulta.setInt(1, cliente.intValue());
            try (ResultSet resultado = sqlConsulta.executeQuery()) {
                while (resultado.next()) {
                    ClienteGrupoVO novoObj = new ClienteGrupoVO();
                    novoObj = ClienteGrupo.montarDados(resultado, nivelMontarDados, this.con);
                    objetos.add(novoObj);
                }
            }
        }
        return objetos;
    }

    /**
     * Operação responsável por localizar um objeto da classe <code>ClienteGrupoVO</code>
     * através de sua chave primária. 
     * @exception Exception Caso haja problemas de conexão ou localização do objeto procurado.
     */
    public ClienteGrupoVO consultarPorChavePrimaria(Integer codigoPrm, int nivelMontarDados) throws Exception {
        consultar(getIdEntidade(), false);
        String sql = "SELECT * FROM ClienteGrupo WHERE codigo = ?";
        try (PreparedStatement sqlConsultar = con.prepareStatement(sql)) {
            sqlConsultar.setInt(1, codigoPrm.intValue());
            try (ResultSet tabelaResultado = sqlConsultar.executeQuery()) {
                if (!tabelaResultado.next()) {
                    throw new ConsistirException("Dados Não Encontrados ( ClienteGrupo ).");
                }
                return (montarDados(tabelaResultado, nivelMontarDados, this.con));
            }
        }
    }

    /**
     * Operação responsável por obter o último valor gerado para uma chave primária.
     * É utilizada para obter o valor gerado pela SGBD para uma chave primária, 
     * a apresentação do mesmo e a implementação de possíveis relacionamentos. 
     */
    public  Integer obterValorChavePrimariaCodigo() throws Exception {
        inicializar();
        String sqlStr = "SELECT MAX(codigo) FROM ClienteGrupo";
        try (Statement stm = con.createStatement()) {
            try (ResultSet tabelaResultado = stm.executeQuery(sqlStr)) {
                tabelaResultado.next();
                return (new Integer(tabelaResultado.getInt(1)));
            }
        }
    }
    /**
     * Operação responsável por consultar todos os <code>ClienteGrupoVO</code> relacionados a um objeto da classe <code>basico.Cliente</code>.
     * @param cliente  Atributo de <code>basico.Cliente</code> a ser utilizado para localizar os objetos da classe <code>ClienteGrupoVO</code>.
     * @return List  Contendo todos os objetos da classe <code>ClienteGrupoVO</code> resultantes da consulta.
     * @exception Exception  Erro de conexão com o BD ou restrição de acesso a esta operação.
     */
    public List consultarClienteGruposPorPessoa(Integer pessoa, int nivelMontarDados) throws Exception {
        consultar(getIdEntidade());
        List objetos = new ArrayList();
        String sql = "SELECT cg.* FROM ClienteGrupo cg, cliente c, pessoa p WHERE cg.cliente = c.codigo  AND p.codigo = c.pessoa AND p.codigo = ?";
        try (PreparedStatement sqlConsulta = con.prepareStatement(sql)) {
            sqlConsulta.setInt(1, pessoa.intValue());
            try (ResultSet resultado = sqlConsulta.executeQuery()) {
                while (resultado.next()) {
                    ClienteGrupoVO novoObj = new ClienteGrupoVO();
                    novoObj = ClienteGrupo.montarDados(resultado, nivelMontarDados, this.con);
                    objetos.add(novoObj);
                }
            }
        }
        return objetos;
    }
}
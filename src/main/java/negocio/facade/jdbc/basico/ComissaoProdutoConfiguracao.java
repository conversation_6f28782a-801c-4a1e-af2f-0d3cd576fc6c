package negocio.facade.jdbc.basico;

import negocio.comuns.contrato.ComissaoProdutoConfiguracaoVO;
import negocio.comuns.plano.CategoriaProdutoVO;
import negocio.comuns.plano.ProdutoVO;
import negocio.comuns.utilitarias.Calendario;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisValidacao;
import negocio.facade.jdbc.arquitetura.SuperEntidade;
import negocio.facade.jdbc.plano.CategoriaProduto;
import negocio.facade.jdbc.plano.Produto;
import negocio.interfaces.basico.ComissaoProdutoConfiguracaoInterfaceFacade;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.List;

/**
 * Created by GlaucoT on 25/01/2017
 */
public class ComissaoProdutoConfiguracao extends SuperEntidade implements ComissaoProdutoConfiguracaoInterfaceFacade {

    public ComissaoProdutoConfiguracao() throws Exception {
    }

    public ComissaoProdutoConfiguracao(Connection conexao) throws Exception {
        super(conexao);
    }

    public void incluir(ComissaoProdutoConfiguracaoVO obj) throws Exception {
        String sql = "INSERT INTO comissaoprodutoconfiguracao(valorfixo, porcentagem, empresa, produto, categoriaproduto, vigenciainicio, vigenciafinal) "
                + "VALUES (?, ?, ?, ?, ?, ?, ?)";
        try (PreparedStatement sqlInserir = con.prepareStatement(sql)) {
            int i = 0;
            sqlInserir.setDouble(++i, obj.getValorFixo());
            sqlInserir.setDouble(++i, obj.getPorcentagem());
            sqlInserir.setInt(++i, obj.getEmpresa().getCodigo());
            resolveFKNull(sqlInserir, ++i, obj.getProduto().getCodigo());
            resolveFKNull(sqlInserir, ++i, obj.getCategoriaProduto().getCodigo());
            sqlInserir.setString(++i, Uteis.getDataAplicandoFormatacao(obj.getVigenciaInicio(), "MM/yyyy"));
            sqlInserir.setString(++i, Uteis.getDataAplicandoFormatacao(obj.getVigenciaFinal(), "MM/yyyy"));

            sqlInserir.execute();
        }
        obj.setCodigo(obterValorChavePrimariaCodigo());
        obj.setNovoObj(false);
    }

    private void excluirPorProduto(Integer codProduto) throws SQLException {
        String sql = "DELETE FROM comissaoprodutoconfiguracao WHERE produto = ?";
        try (PreparedStatement sqlExcluir = con.prepareStatement(sql)) {
            sqlExcluir.setInt(1, codProduto);
            sqlExcluir.execute();
        }
    }

    private void excluirPorCategoriaProduto(Integer codCategoriaProduto) throws SQLException {
        String sql = "DELETE FROM comissaoprodutoconfiguracao WHERE categoriaproduto = ?";
        try (PreparedStatement sqlExcluir = con.prepareStatement(sql)) {
            sqlExcluir.setInt(1, codCategoriaProduto);
            sqlExcluir.execute();
        }
    }

    @Override
    public List<ComissaoProdutoConfiguracaoVO> consultarPorProduto(Integer codProduto) throws Exception {
        String sql = "SELECT * FROM comissaoprodutoconfiguracao cpc WHERE produto = ? ORDER BY codigo DESC";
        try (PreparedStatement sqlConsultar = con.prepareStatement(sql)) {
            sqlConsultar.setInt(1, codProduto);
            try (ResultSet rs = sqlConsultar.executeQuery()) {
                return montarDadosConsulta(rs, con);
            }
        }
    }

    private List<ComissaoProdutoConfiguracaoVO> montarDadosConsulta(ResultSet rs, Connection con) throws Exception {
        List<ComissaoProdutoConfiguracaoVO> lista = new ArrayList<ComissaoProdutoConfiguracaoVO>();
        while (rs.next()) {
            ComissaoProdutoConfiguracaoVO comissao = new ComissaoProdutoConfiguracaoVO();
            comissao.setNovoObj(false);
            comissao.getProduto().setCodigo(rs.getInt("produto"));
            comissao.getCategoriaProduto().setCodigo(rs.getInt("categoriaproduto"));
            comissao.setCodigo(rs.getInt("codigo"));
            comissao.getEmpresa().setCodigo(rs.getInt("empresa"));
            comissao.setPorcentagem(rs.getDouble("porcentagem"));
            comissao.setValorFixo(rs.getDouble("valorfixo"));
            if (!UteisValidacao.emptyString(rs.getString("vigenciainicio"))) {
                comissao.setVigenciaInicio(Calendario.getDate("MM/yyyy", rs.getString("vigenciainicio")));
            }
            if (!UteisValidacao.emptyString(rs.getString("vigenciafinal"))) {
                comissao.setVigenciaFinal(Calendario.getDate("MM/yyyy", rs.getString("vigenciafinal")));
            }

            montarDadosProduto(comissao, con);
            montarDadosCategoriaProduto(comissao, con);
            montarDadosEmpresa(comissao, con);

            lista.add(comissao);
        }
        return lista;
    }

    private void montarDadosProduto(ComissaoProdutoConfiguracaoVO obj, Connection con) throws Exception {
        if (obj.getProduto().getCodigo() != 0) {
            Produto produto = new Produto(con);
            obj.setProduto(produto.consultarPorChavePrimaria(obj.getProduto().getCodigo(), Uteis.NIVELMONTARDADOS_MINIMOS));
            produto = null;
        }
    }

    private void montarDadosCategoriaProduto(ComissaoProdutoConfiguracaoVO obj, Connection con) throws Exception {
        if (obj.getCategoriaProduto().getCodigo() != 0) {
            CategoriaProduto categoriaProduto = new CategoriaProduto(con);
            obj.setCategoriaProduto(categoriaProduto.consultarPorChavePrimaria(obj.getCategoriaProduto().getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS));
            categoriaProduto = null;
        }
    }

    private void montarDadosEmpresa(ComissaoProdutoConfiguracaoVO obj, Connection con) throws Exception {
        if (obj.getEmpresa().getCodigo() != 0) {
            Empresa empresa = new Empresa(con);
            obj.setEmpresa(empresa.consultarPorChavePrimaria(obj.getEmpresa().getCodigo(), Uteis.NIVELMONTARDADOS_MINIMOS));
            empresa = null;
        }
    }

    @Override
    public List<ComissaoProdutoConfiguracaoVO> consultarPorCategoriaProduto(Integer codCategoriaProduto) throws Exception {
        String sql = "SELECT * FROM comissaoprodutoconfiguracao cpc WHERE categoriaproduto = ? ORDER BY codigo DESC";
        try (PreparedStatement sqlConsultar = con.prepareStatement(sql)) {
            sqlConsultar.setInt(1, codCategoriaProduto);
            try (ResultSet rs = sqlConsultar.executeQuery()) {
                return montarDadosConsulta(rs, con);
            }
        }
    }

    @Override
    public void atualizarPorProduto(ProdutoVO obj) throws Exception {
        excluirPorProduto(obj.getCodigo());
        for (ComissaoProdutoConfiguracaoVO comissaoProduto : obj.getComissaoProdutos()) {
            incluir(comissaoProduto);
        }
    }

    @Override
    public void atualizarPorCategoriaProduto(CategoriaProdutoVO obj) throws Exception {
        excluirPorCategoriaProduto(obj.getCodigo());
        for (ComissaoProdutoConfiguracaoVO comissaoProduto : obj.getComissaoCategoriaProdutos()) {
            incluir(comissaoProduto);
        }
    }
}

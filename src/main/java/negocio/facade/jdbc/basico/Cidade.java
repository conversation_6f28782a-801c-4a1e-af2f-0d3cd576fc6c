package negocio.facade.jdbc.basico;

import br.com.pactosolucoes.comuns.util.FileUtilities;
import negocio.comuns.basico.CidadeVO;
import negocio.comuns.basico.EstadoVO;
import negocio.comuns.basico.PaisVO;
import negocio.comuns.utilitarias.*;
import negocio.facade.jdbc.arquitetura.SuperEntidade;
import negocio.facade.jdbc.arquitetura.SuperFacadeJDBC;
import negocio.facade.jdbc.utilitarias.Cep;
import negocio.interfaces.basico.CidadeInterfaceFacade;
import org.json.JSONArray;
import org.json.JSONObject;

import java.io.BufferedReader;
import java.io.File;
import java.io.StringReader;
import java.sql.*;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

/**
 * Classe de persistência que encapsula todas as operações de manipulação dos dados da classe <code>CidadeVO</code>.
 * Responsável por implementar operações como incluir, alterar, excluir e consultar pertinentes a classe <code>CidadeVO</code>.
 * Encapsula toda a interação com o banco de dados.
 * @see CidadeVO
 * @see SuperEntidade
 */
public class Cidade extends SuperEntidade implements CidadeInterfaceFacade {

    protected static String idEntidade;

    public Cidade() throws Exception {
        super();        
    }

    public Cidade(Connection con) throws Exception {
    	   super(con);
	}

	/**
     * Operação responsável por retornar um novo objeto da classe <code>CidadeVO</code>.
     */
    public CidadeVO novo() throws Exception {
        incluir(getIdEntidade());
        return new CidadeVO();
    }

    /* (non-Javadoc)
     * @see negocio.interfaces.basico.CidadeInterfaceFacade#incluir(negocio.comuns.basico.CidadeVO)
     */
    public void incluir(CidadeVO obj) throws Exception {
    	this.incluir(obj, false);
    }
    
    /**
     * Operação responsável por incluir no banco de dados um objeto da classe <code>CidadeVO</code>.
     * Primeiramente valida os dados (<code>validarDados</code>) do objeto. Verifica a conexão com o banco de dados e a permissão do usuário
     * para realizar esta operacão na entidade.
     * Isto, através da operação <code>incluir</code> da superclasse.
     * @param obj  Objeto da classe <code>CidadeVO</code> que será gravado no banco de dados.
     * @exception Exception Caso haja problemas de conexão, restrição de acesso ou validação de dados.
     */
    public void incluir(CidadeVO obj, boolean centralEventos) throws Exception {
        CidadeVO.validarDados(obj);
        boolean existeCidadeEstadoPais = consultarPorNomeEstadoPais(obj.getNome(), obj.getEstado().getCodigo(), obj.getPais().getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS);
        if (existeCidadeEstadoPais) {
            throw new ConsistirException("Já existe uma cidade cadastrada com o nome " + obj.getNome());
        }
        if(!centralEventos){
            incluir(getIdEntidade());
        }
        obj.realizarUpperCaseDados();
        String sql = "INSERT INTO Cidade( nome,nomeSemAcento, estado, Pais, codigoMunicipio, homologada) VALUES ( ?, ?, ?, ?, ?, ?)";
        try (PreparedStatement sqlInserir = con.prepareStatement(sql)) {
            sqlInserir.setString(1, obj.getNome().trim().toUpperCase());
            obj.setNomeSemAcento(Uteis.retirarAcentuacaoEPontuacao(obj.getNome()));
            sqlInserir.setString(2, obj.getNomeSemAcento().toUpperCase());
            if (obj.getPais().getCodigo() != 0) {
                sqlInserir.setInt(3, obj.getEstado().getCodigo());
            } else {
                sqlInserir.setNull(3, 0);
            }
            if (obj.getPais().getCodigo() != 0) {
                sqlInserir.setInt(4, obj.getPais().getCodigo());
            } else {
                sqlInserir.setNull(4, 0);
            }
            sqlInserir.setString(5, obj.getCodigoMunicipio());
            sqlInserir.setBoolean(6, obj.isHomologada());
            sqlInserir.execute();
        }
        obj.setCodigo(obterValorChavePrimariaCodigo());
        obj.setNovoObj(false);
    }
    /* (non-Javadoc)
     * @see negocio.interfaces.basico.CidadeInterfaceFacade#incluir(negocio.comuns.basico.CidadeVO)
     */
    public void alterar(CidadeVO obj) throws Exception {
    	this.alterar(obj, false);
    }
    /**
     * Operação responsável por alterar no BD os dados de um objeto da classe <code>CidadeVO</code>.
     * Sempre utiliza a chave primária da classe como atributo para localização do registro a ser alterado.
     * Primeiramente valida os dados (<code>validarDados</code>) do objeto. Verifica a conexão com o banco de dados e a permissão do usuário
     * para realizar esta operacão na entidade.
     * Isto, através da operação <code>alterar</code> da superclasse.
     * @param obj    Objeto da classe <code>CidadeVO</code> que será alterada no banco de dados.
     * @exception Exception Caso haja problemas de conexão, restrição de acesso ou validação de dados.
     */
    public void alterar(CidadeVO obj, boolean centralEventos) throws Exception {
            CidadeVO.validarDados(obj);
            CidadeVO cidade = consultarPorNome(Uteis.retirarAcentuacaoEPontuacao(obj.getNome()), Uteis.NIVELMONTARDADOS_DADOSBASICOS);
             if(cidade != null && !UteisValidacao.emptyNumber(cidade.getCodigo()) && cidade.getEstado().getCodigo().equals(obj.getEstado().getCodigo()) && !cidade.getCodigo().equals(obj.getCodigo())){
                  throw new ConsistirException("Já existe cidade cadastrada com esse nome e estado");
             }
            if(!centralEventos){
            	alterar(getIdEntidade());
            }
            obj.realizarUpperCaseDados();
            String sql = "UPDATE Cidade set nome=?, nomeSemAcento=?, estado=?, Pais=?, codigomunicipio=?, homologada = ? WHERE ((codigo = ?))";
        try (PreparedStatement sqlAlterar = con.prepareStatement(sql)) {
            sqlAlterar.setString(1, obj.getNome().trim());
            obj.setNomeSemAcento(obj.getNome());
            sqlAlterar.setString(2, Uteis.retirarAcentuacaoEPontuacao(obj.getNomeSemAcento()));
            sqlAlterar.setInt(3, obj.getEstado().getCodigo());
            if (obj.getPais().getCodigo() != 0) {
                sqlAlterar.setInt(4, obj.getPais().getCodigo());
            } else {
                sqlAlterar.setNull(4, 0);
            }
            sqlAlterar.setString(5, obj.getCodigoMunicipio());
            sqlAlterar.setBoolean(6, obj.isHomologada());
            sqlAlterar.setInt(7, obj.getCodigo());
            sqlAlterar.execute();
        }
    }
    /* (non-Javadoc)
     * @see negocio.interfaces.basico.CidadeInterfaceFacade#incluir(negocio.comuns.basico.CidadeVO)
     */
    public void excluir(CidadeVO obj) throws Exception {
    	this.excluir(obj, false);
    }
    /**
     * Operação responsável por excluir no BD um objeto da classe <code>CidadeVO</code>.
     * Sempre localiza o registro a ser excluído através da chave primária da entidade.
     * Primeiramente verifica a conexão com o banco de dados e a permissão do usuário
     * para realizar esta operacão na entidade.
     * Isto, através da operação <code>excluir</code> da superclasse.
     * @param obj    Objeto da classe <code>CidadeVO</code> que será removido no banco de dados.
     * @exception Exception Caso haja problemas de conexão ou restrição de acesso.
     */
    public void excluir(CidadeVO obj, boolean centralEventos) throws Exception {
        try {
            con.setAutoCommit(false);
            if (!centralEventos) {
                excluir(getIdEntidade());
            }
            String sql = "DELETE FROM Cidade WHERE ((codigo = ?))";
            try (PreparedStatement sqlExcluir = con.prepareStatement(sql)) {
                sqlExcluir.setInt(1, obj.getCodigo());
                sqlExcluir.execute();
            }
            con.commit();
        } catch (Exception e) {
            con.rollback();
            con.setAutoCommit(true);
            throw e;
        } finally {
            con.setAutoCommit(true);
        }
    }

    /**
     * Responsável por realizar uma consulta de <code>Cidade</code> através do valor do atributo 
     * <code>nome</code> da classe <code>Pais</code>
     * Faz uso da operação <code>montarDadosConsulta</code> que realiza o trabalho de prerarar o List resultante.
     * @return  List Contendo vários objetos da classe <code>CidadeVO</code> resultantes da consulta.
     * @exception Exception Caso haja problemas de conexão ou restrição de acesso.
     */
    public List consultarPorNomePais(String valorConsulta, int nivelMontarDados) throws Exception {
        consultar(getIdEntidade(), true);
        String sqlStr = "SELECT Cidade.* FROM Cidade, Pais WHERE Cidade.Pais = Pais.codigo and upper( Pais.nome ) like('" + valorConsulta.toUpperCase() + "%') ORDER BY Pais.nome";
        try (Statement stm = con.createStatement()) {
            try (ResultSet tabelaResultado = stm.executeQuery(sqlStr)) {
                return montarDadosConsulta(tabelaResultado, nivelMontarDados, this.con);
            }
        }
    }
    /**
     * Método usado para buscar todas as cidades cadastradas no banco
     * @return
     * @throws Exception
     */
    public ResultSet consultar() throws Exception {
        consultar(getIdEntidade(), true);
        String sqlStr = "SELECT * FROM Cidade";
        Statement stm = con.createStatement();
        return stm.executeQuery(sqlStr);
    }

    /**
     * Responsável por realizar uma consulta de <code>Cidade</code> através do valor do atributo 
     * <code>nome</code> da classe <code>Pais</code>
     * Faz uso da operação <code>montarDadosConsulta</code> que realiza o trabalho de prerarar o List resultante.
     * @return  List Contendo vários objetos da classe <code>CidadeVO</code> resultantes da consulta.
     * @exception Exception Caso haja problemas de conexão ou restrição de acesso.
     */
    public List consultarPorCodigoPais(Integer valorConsulta, int nivelMontarDados) throws Exception {
        consultar(getIdEntidade(), true);
        String sqlStr = "SELECT Cidade.* FROM Cidade, Pais WHERE Cidade.Pais = Pais.codigo and Pais.codigo =" + valorConsulta + " ORDER BY Pais.nome";
        try (Statement stm = con.createStatement()) {
            try (ResultSet tabelaResultado = stm.executeQuery(sqlStr)) {
                return montarDadosConsulta(tabelaResultado, nivelMontarDados, this.con);
            }
        }
    }

    /**
     * Responsável por realizar uma consulta de <code>Cidade</code> através do valor do atributo 
     * <code>String estado</code>. Retorna os objetos, com início do valor do atributo idêntico ao parâmetro fornecido.
     * Faz uso da operação <code>montarDadosConsulta</code> que realiza o trabalho de prerarar o List resultante.
     * @param   controlarAcesso Indica se a aplicação deverá verificar se o usuário possui permissão para esta consulta ou não.
     * @return  List Contendo vários objetos da classe <code>CidadeVO</code> resultantes da consulta.
     * @exception Exception Caso haja problemas de conexão ou restrição de acesso.
     */
    public List consultarPorEstado(String valorConsulta, boolean controlarAcesso, int nivelMontarDados) throws Exception {
        consultar(getIdEntidade(), controlarAcesso);
        String sqlStr = " SELECT cidade.* FROM cidade, estado WHERE cidade.estado = estado.codigo "
        	          + " AND (estado.sigla like(UPPER('" + valorConsulta.toUpperCase() + "%')) "
        	          + " OR estado.descricao like(UPPER('" + valorConsulta.toUpperCase() + "%'))) ORDER BY estado.descricao, cidade.nome ";
        try (Statement stm = con.createStatement()) {
            try (ResultSet tabelaResultado = stm.executeQuery(sqlStr)) {
                return (montarDadosConsulta(tabelaResultado, nivelMontarDados, this.con));
            }
        }
    }

    /**
     * Responsável por realizar uma consulta de <code>Cidade</code> através do valor do atributo 
     * <code>String nome</code>. Retorna os objetos, com início do valor do atributo idêntico ao parâmetro fornecido.
     * Faz uso da operação <code>montarDadosConsulta</code> que realiza o trabalho de prerarar o List resultante.
     * @param   controlarAcesso Indica se a aplicação deverá verificar se o usuário possui permissão para esta consulta ou não.
     * @return  List Contendo vários objetos da classe <code>CidadeVO</code> resultantes da consulta.
     * @exception Exception Caso haja problemas de conexão ou restrição de acesso.
     */
    public List consultarPorNome(String valorConsulta, boolean controlarAcesso, int nivelMontarDados) throws Exception {
        consultar(getIdEntidade(), controlarAcesso);
        String sqlStr = "SELECT * FROM Cidade WHERE upper( nome ) like('" + valorConsulta.toUpperCase() + "%') ORDER BY nome";
        try (Statement stm = con.createStatement()) {
            try (ResultSet tabelaResultado = stm.executeQuery(sqlStr)) {
                return (montarDadosConsulta(tabelaResultado, nivelMontarDados, this.con));
            }
        }
    }

    public CidadeVO consultarPorNome(String valorConsulta, int nivelMontarDados) throws Exception {
        String sqlStr = "SELECT * FROM Cidade WHERE ( UPPER(nomesemacento) ) like ('" + valorConsulta.toUpperCase() + "%') ORDER BY nomesemacento";
        try (Statement stm = con.createStatement()) {
            try (ResultSet tabelaResultado = stm.executeQuery(sqlStr)) {
                if (!tabelaResultado.next()) {
                    return null;
                }
                return (montarDados(tabelaResultado, nivelMontarDados, this.con));
            }
        }
    }
    /**
     * Método usado para saber se o nome da cidade digitada no campo
     * já existe no banco com o mesmo estado e país
     * @param cidade
     * @param estado
     * @param pais
     * @param nivelMontarDados
     * @return
     * @throws Exception
     */
    public boolean consultarPorNomeEstadoPais(String cidade, int estado, int pais, int nivelMontarDados) throws Exception {
        String sqlStr = "SELECT * FROM cidade " +
                "WHERE nome LIKE ? " +
                "OR nomesemacento LIKE ? " +
                "AND estado = ? " +
                "AND pais = ?";
        try (PreparedStatement pstm = con.prepareStatement(sqlStr)) {
            pstm.setString(1, cidade.toUpperCase());
            pstm.setString(2, cidade.toUpperCase());
            pstm.setInt(3, estado);
            pstm.setInt(4, pais);
            try (ResultSet tabelaResultado = pstm.executeQuery()) {
                if (!tabelaResultado.next()) {
                    return false;
                }
            }
        }
        return true;
    }
    /**
     * Responsável por realizar uma consulta de <code>Cidade</code> através do valor do atributo 
     * <code>Integer codigo</code>. Retorna os objetos com valores iguais ou superiores ao parâmetro fornecido.
     * Faz uso da operação <code>montarDadosConsulta</code> que realiza o trabalho de prerarar o List resultante.
     * @param   controlarAcesso Indica se a aplicação deverá verificar se o usuário possui permissão para esta consulta ou não.
     * @return  List Contendo vários objetos da classe <code>CidadeVO</code> resultantes da consulta.
     * @exception Exception Caso haja problemas de conexão ou restrição de acesso.
     */
    public List consultarPorCodigo(Integer valorConsulta, boolean controlarAcesso, int nivelMontarDados) throws Exception {
        consultar(getIdEntidade(), controlarAcesso);
        String sqlStr = "SELECT * FROM Cidade WHERE codigo >= " + valorConsulta + " ORDER BY codigo";
        try (Statement stm = con.createStatement()) {
            try (ResultSet tabelaResultado = stm.executeQuery(sqlStr)) {
                return (montarDadosConsulta(tabelaResultado, nivelMontarDados, this.con));
            }
        }
    }
    
    public CidadeVO consultarPorCodigoExato(Integer valorConsulta, boolean controlarAcesso, int nivelMontarDados) throws Exception {
        return consultarPorChavePrimaria(valorConsulta, nivelMontarDados);
    }

    /**
     * Responsável por montar os dados de vários objetos, resultantes de uma consulta ao banco de dados (<code>ResultSet</code>).
     * Faz uso da operação <code>montarDados</code> que realiza o trabalho para um objeto por vez.
     * @return  List Contendo vários objetos da classe <code>CidadeVO</code> resultantes da consulta.
     */
    public static List montarDadosConsulta(ResultSet tabelaResultado, int nivelMontarDados, Connection con) throws Exception {
        List<CidadeVO> vetResultado = new ArrayList<CidadeVO>();
        while (tabelaResultado.next()) {
            CidadeVO obj = montarDados(tabelaResultado, nivelMontarDados, con);
            vetResultado.add(obj);
        }
        return vetResultado;
    }

    /**
     * Responsável por montar os dados resultantes de uma consulta ao banco de dados (<code>ResultSet</code>)
     * em um objeto da classe <code>CidadeVO</code>.
     * @return  O objeto da classe <code>CidadeVO</code> com os dados devidamente montados.
     */
    public static CidadeVO montarDados(ResultSet dadosSQL, int nivelMontarDados, Connection con) throws Exception {
        CidadeVO obj = new CidadeVO();
        obj.setCodigo(dadosSQL.getInt("codigo"));
        obj.setNome(dadosSQL.getString("nome"));
        obj.setNomeSemAcento(dadosSQL.getString("nomeSemAcento"));
        obj.getEstado().setCodigo(dadosSQL.getInt("Estado"));
        obj.getPais().setCodigo(dadosSQL.getInt("Pais"));
        try {
            obj.setCodigoMunicipio(dadosSQL.getString("codigomunicipio"));
        } catch (Exception e) {
            //ignore
        }
        try {
            obj.setHomologada(dadosSQL.getBoolean("homologada"));
        } catch (Exception e) {
            //ignore
        }

        obj.setNovoObj(false);
        montarDadosPais(obj, Uteis.NIVELMONTARDADOS_DADOSBASICOS, con);
        montarDadosEstado(obj, Uteis.NIVELMONTARDADOS_DADOSBASICOS, con);
        return obj;
    }

    /**
     * Operação responsável por montar os dados de um objeto da classe <code>PaisVO</code> relacionado ao objeto <code>CidadeVO</code>.
     * Faz uso da chave primária da classe <code>PaisVO</code> para realizar a consulta.
     * @param obj  Objeto no qual será montado os dados consultados.
     */
    public static void montarDadosPais(CidadeVO obj, int nivelMontarDados, Connection con) throws Exception {
        if (obj.getPais().getCodigo() == 0) {
            obj.setPais(new PaisVO());
            return;
        }
        Pais pais = new Pais(con); 
        obj.setPais(pais.consultarPorChavePrimaria(obj.getPais().getCodigo(), nivelMontarDados));
        pais = null;
    }

    public static void montarDadosEstado(CidadeVO obj, int nivelMontarDados, Connection con) throws Exception {
        if (obj.getEstado().getCodigo() == 0) {
            obj.setEstado(new EstadoVO());
            return;
        }
        Estado estado = new Estado(con);
        obj.setEstado(estado.consultarPorChavePrimaria(obj.getEstado().getCodigo(), nivelMontarDados));
        estado = null;
    }

    /**
     * Operação responsável por localizar um objeto da classe <code>CidadeVO</code>
     * através de sua chave primária. 
     * @exception Exception Caso haja problemas de conexão ou localização do objeto procurado.
     */
    public CidadeVO consultarPorChavePrimaria(Integer codigoPrm, int nivelMontarDados) throws Exception {
        consultar(getIdEntidade(), false);
        CidadeVO eCache = (CidadeVO) obterFromCache(codigoPrm);
        if (eCache != null){
            return eCache;
        }
        try {
            String sql = "SELECT * FROM Cidade WHERE codigo = ?";
            try (PreparedStatement sqlConsultar = con.prepareStatement(sql)) {
                sqlConsultar.setInt(1, codigoPrm);
                try (ResultSet tabelaResultado = sqlConsultar.executeQuery()) {
                    if (!tabelaResultado.next()) {
                        throw new ConsistirException("Dados Não Encontrados ( Cidade ).");
                    }
                    eCache = (montarDados(tabelaResultado, nivelMontarDados, this.con));
                }
            }
            putToCache(eCache);
        } catch(Exception e) {
            e.printStackTrace();
        }
        return eCache;
    }

    public List consultarPorCodigoEstado(Integer valorConsulta, int nivelMontarDados) throws Exception {
        consultar(getIdEntidade(), false);
        String sqlStr = "SELECT * FROM Cidade WHERE estado = " + valorConsulta + " ORDER BY nome";
        try (Statement stm = con.createStatement()) {
            try (ResultSet tabelaResultado = stm.executeQuery(sqlStr)) {
                return (montarDadosConsulta(tabelaResultado, nivelMontarDados, this.con));
            }
        }
    }

    public String consultarJSON() throws Exception {
        JSONObject aaData = new JSONObject();

        try (ResultSet rs = getPS().executeQuery()) {
            JSONArray lista = new JSONArray();
            while (rs.next()) {
                JSONArray itemLista = new JSONArray();
                itemLista.put(rs.getString("codigo"));
                itemLista.put(rs.getString("nome"));
                itemLista.put(rs.getString("estado"));
                itemLista.put(rs.getString("pais"));
                lista.put(itemLista);
            }
            aaData.put("aaData", lista);
        }

        return aaData.toString();
    }

    private PreparedStatement getPS() throws SQLException {
        String sql = "SELECT cidade.codigo, cidade.nome, estado.descricao AS estado, pais.nome AS pais\n" +
                "FROM cidade\n" +
                "  LEFT JOIN estado ON cidade.estado = estado.codigo\n" +
                "  LEFT JOIN pais ON cidade.pais = pais.codigo\n" +
                "  ORDER BY cidade.nome";
        return con.prepareStatement(sql);
    }
    public List consultarParaImpressao(String filtro, String ordem, String campoOrdenacao, int i) throws SQLException {
        List lista;
        try (ResultSet rs = getPS().executeQuery()) {
            lista = new ArrayList();
            while (rs.next()) {
                CidadeVO cd = new CidadeVO();
                String geral = rs.getString("codigo") + rs.getString("nome") + rs.getString("estado") + rs.getString("pais");
                if (geral.toLowerCase().contains(filtro.toLowerCase())) {
                    cd.setCodigo(rs.getInt("codigo"));
                    cd.setNome(rs.getString("nome"));
                    cd.getEstado().setDescricao(rs.getString("estado"));
                    cd.getPais().setNome(rs.getString("pais"));
                    lista.add(cd);
                }
            }
        }
        if (campoOrdenacao.equals("Código")) {
            Ordenacao.ordenarLista(lista, "codigo");
        } else if (campoOrdenacao.equals("Nome")) {
            Ordenacao.ordenarLista(lista, "nome");
        } else if (campoOrdenacao.equals("Estado")) {
            Ordenacao.ordenarLista(lista, "estado");
        } else if (campoOrdenacao.equals("País")) {
            Ordenacao.ordenarLista(lista, "pais");
        }
        if (ordem.contains("desc")) {
            Collections.reverse(lista);
        }
        return lista;
    }

    public CidadeVO consultarPorNomeCidadeSiglaEstado(String cidade, String siglaEstado) throws Exception {
        consultar(getIdEntidade(), false);

        StringBuilder sql = new StringBuilder();

        sql.append("select \n");
        sql.append("c.* \n");
        sql.append("from cidade c \n");
        sql.append("inner join estado e on c.estado = e.codigo \n");
        sql.append("where 1 = 1 \n");
        sql.append("and c.nomeSemAcento ilike '").append(Uteis.retirarAcentuacaoEPontuacao(cidade)).append("' \n");
        sql.append("and e.sigla ilike '").append(siglaEstado).append("'");

        try (Statement stm = con.createStatement()) {
            try (ResultSet tabelaResultado = stm.executeQuery(sql.toString())) {
                if (!tabelaResultado.next()) {
                    return new CidadeVO();
                }
                return montarDados(tabelaResultado, Uteis.NIVELMONTARDADOS_DADOSBASICOS, this.con);
            }
        }
    }

    public void preencherCodigosIBGECidade() {
        try {
            File arq = new File(getClass().getResource("/br/com/pactosolucoes/comuns/util/codigoIBGECidades.txt").toURI());
            StringBuilder texto = FileUtilities.readContentFile(arq.getAbsolutePath());
            BufferedReader arquivo = new BufferedReader(new StringReader(texto.toString()));
            String linha = null;
            while ((linha = arquivo.readLine()) != null) {
                try {
                    if (!UteisValidacao.emptyString(linha)) {
                        String[] dados = linha.split("\\|");

                        String ibge = dados[0].trim();
                        String estadoUF = dados[1].trim();
                        String cidade = dados[2].trim();

                        CidadeVO cidadeVO = consultaPorNomeEstadoCriaSeNaoExiste(cidade, estadoUF);

                        if (!UteisValidacao.emptyNumber(cidadeVO.getCodigo())) {
                            System.out.println("VOU PREENCHER: " + estadoUF + " - " + cidade + " - IBGE " + ibge);
                            SuperFacadeJDBC.executarUpdateExecutarProcessos("update cidade set codigoMunicipio = '" + ibge + "' where codigo = " + cidadeVO.getCodigo() + ";", con);
                        } else {
                            System.out.println("NÃO ENCONTREI: " + estadoUF + " - " + cidade + " - IBGE " + ibge);
                        }
                    }
                } catch (Exception ex) {
                    System.out.println("ERRO PROCESSAR LINHA: " + linha);
                    System.out.println("ERRO : " + ex.getMessage());
                }
            }
        } catch (Exception ignored) {
        }
    }

    public CidadeVO consultaPorNomeEstadoCriaSeNaoExiste(String cidade, String uf) {
        try {
            CidadeVO cidadeVO = consultarPorNomeCidadeSiglaEstado(cidade, uf);
            if (UteisValidacao.emptyNumber(cidadeVO.getCodigo())) {
                Estado estadoDAO = new Estado(con);
                EstadoVO estadoVO = estadoDAO.consultarPorSiglaUf(uf, false, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
                if (!UteisValidacao.emptyNumber(estadoVO.getCodigo())) {
                    System.out.println("VOU CRIAR CIDADE: " + cidade);
                    cidadeVO = new CidadeVO();
                    cidadeVO.setNome(cidade);
                    cidadeVO.getPais().setCodigo(estadoVO.getPais());
                    cidadeVO.setEstado(estadoVO);
                    incluir(cidadeVO);
                }
            }
            return cidadeVO;
        } catch (Exception ex) {
            System.out.println(ex.getMessage());
            return new CidadeVO();
        }
    }

    public List<CidadeVO> consultarCidadesHomologadas(int nivelMontarDados) throws Exception {
        String sqlStr = "SELECT * FROM Cidade WHERE homologada = true ORDER BY nome";
        try (Statement stm = con.createStatement()) {
            try (ResultSet tabelaResultado = stm.executeQuery(sqlStr)) {
                return montarDadosConsulta(tabelaResultado, nivelMontarDados, this.con);
            }
        }
    }

    public CidadeVO obterCidadeCEP(String cep) {
        Pais paisDAO = null;
        Estado estadoDAO = null;
        Cep cepDAO = null;
        try {
            paisDAO = new Pais(con);
            estadoDAO = new Estado(con);
            cepDAO = new Cep();

            if (UteisValidacao.emptyString(cep)) {
                throw new Exception("Informe o CEP corretamente!");
            }

            cep = Uteis.removerMascara(cep);

            CepVO cepVO = new CepVO();
            try {
                cepVO = cepDAO.consultarPorNumeroCep(Uteis.removerMascara(cep), Uteis.NIVELMONTARDADOS_DADOSBASICOS);
            } catch (Exception e) {
                try {
                    cepVO = cepDAO.consultarPorNumeroCepGeral(Uteis.removerMascara(cep), Uteis.NIVELMONTARDADOS_DADOSBASICOS);
                } catch (Exception ex) {
                    cepVO = cepDAO.consultarApiViaCEP(cep);
                }
            }

            PaisVO objPais = null;
            EstadoVO objEstado = null;
            CidadeVO objCidade = null;
            if (!UteisValidacao.emptyString(cepVO.getCidadeDescricao().trim())) {
                objCidade = consultaPorNomeEstadoCriaSeNaoExiste(Uteis.retirarAcentuacao(cepVO.getCidadeDescricao().trim()), cepVO.getUfSigla());
            }

            if (objCidade != null) {
                objPais = objCidade.getPais();
                objEstado = objCidade.getEstado();
            } else {
                objCidade = new CidadeVO();
                objPais = paisDAO.consultarPorNome("Brasil", Uteis.NIVELMONTARDADOS_DADOSBASICOS);

                if (objPais == null) {
                    throw new Exception("O país de nome Brasil deve ser cadastrado.");
                }
                objEstado = estadoDAO.consultarPorSiglaDescricaoEPais(
                        cepVO.getUfSigla().trim(),
                        cepVO.getUfDescricao().trim(), objPais.getCodigo(), false,
                        Uteis.NIVELMONTARDADOS_DADOSBASICOS);

                if (objEstado == null) {
                    objEstado = new EstadoVO();
                    objEstado.setDescricao(cepVO.getUfDescricao());
                    objEstado.setSigla(cepVO.getUfSigla());
                    objEstado.setPais(objPais.getCodigo());
                    estadoDAO.incluir(objEstado);

                }
                objCidade.setPais(objPais);
                objCidade.setEstado(objEstado);
                objCidade.setNome(cepVO.getCidadeDescricao().trim());
                incluir(objCidade);
            }


            if (objPais != null) {
                objCidade.setPais(objPais);
            }

            if (objEstado != null) {
                objCidade.setEstado(objEstado);
            }

            return objCidade;
        } catch (Exception ex) {
            ex.printStackTrace();
            return null;
        } finally {
            estadoDAO = null;
            paisDAO = null;
            cepDAO = null;
        }
    }

    public CidadeVO consultarCidadePorNomeEstadoPais(String nome, int estado, int pais, int nivelMontarDados) throws Exception {
        String sqlStr = "SELECT * FROM cidade " +
                "WHERE nome LIKE ? " +
                "OR nomesemacento LIKE ? " +
                "AND estado = ? " +
                "AND pais = ?";
        try (PreparedStatement pstm = con.prepareStatement(sqlStr)) {
            pstm.setString(1, nome.toUpperCase());
            pstm.setString(2, nome.toUpperCase());
            pstm.setInt(3, estado);
            pstm.setInt(4, pais);
            try (ResultSet tabelaResultado = pstm.executeQuery()) {
                if (tabelaResultado.next()) {
                    return montarDados(tabelaResultado, nivelMontarDados, con);
                }
            }
        }
        return null;
    }
}

package negocio.facade.jdbc.basico;

import negocio.comuns.basico.ColaboradorVO;
import negocio.comuns.basico.ObservacaoOperacaoVO;
import negocio.comuns.basico.enumerador.TipoObservacaoOperacaoEnum;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisValidacao;
import negocio.facade.jdbc.arquitetura.SuperEntidade;
import negocio.interfaces.basico.ObservacaoOperacaoInterfaceFacade;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.Statement;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

public class ObservacaoOperacao extends SuperEntidade implements ObservacaoOperacaoInterfaceFacade {

    public ObservacaoOperacao() throws Exception {
        super();
    }

    public ObservacaoOperacao(Connection conexao) throws Exception {
        super(conexao);
    }

    @Override
    public void incluir(ObservacaoOperacaoVO obj) throws Exception {
        String sql = "INSERT INTO observacaooperacao (justificativa, dataoperacao, tipooperacao, movparcela, valor, usuarioresponsavel) VALUES (?, ?, ?, ?, ?, ?);";
        PreparedStatement sqlInserir = getCon().prepareStatement(sql);
        int i = 0;
        sqlInserir.setString(++i, obj.getJustificativa());
        sqlInserir.setTimestamp(++i, Uteis.getDataJDBCTimestamp(obj.getDataOperacao()));
        sqlInserir.setString(++i, obj.getTipoObservacao().getTipo());
        if (obj.getMovParcela() != null && obj.getMovParcela().getCodigo() > 0) {
            sqlInserir.setInt(++i, obj.getMovParcela().getCodigo());
        } else {
            sqlInserir.setNull(++i, 0);
        }
        if (obj.getValorOperacao() != null) {
            sqlInserir.setDouble(++i, obj.getValorOperacao());
        } else {
            sqlInserir.setNull(++i, 0);
        }
        sqlInserir.setString(++i, obj.getUsuarioResponsavel());
        sqlInserir.execute();
        obj.setCodigo(obterValorChavePrimariaCodigo());
        obj.setNovoObj(false);
    }

    @Override
    public void alterar(ObservacaoOperacaoVO obj) throws Exception {
        String sql = "UPDATE observacaooperacao\n" +
                "SET justificativa = ?, dataoperacao = ?, tipooperacao = ?, movparcela = ?, valor = ?, usuarioresponsavel = ?\n" +
                "WHERE codigo = ?;";
        PreparedStatement sqlAlterar = getCon().prepareStatement(sql);
        int i = 0;
        sqlAlterar.setString(++i, obj.getJustificativa());
        sqlAlterar.setTimestamp(++i, Uteis.getDataJDBCTimestamp(obj.getDataOperacao()));
        sqlAlterar.setString(++i, obj.getTipoObservacao().getTipo());
        if (obj.getMovParcela() != null && obj.getMovParcela().getCodigo() > 0) {
            sqlAlterar.setInt(++i, obj.getMovParcela().getCodigo());
        } else {
            sqlAlterar.setNull(++i, 0);
        }
        if (obj.getValorOperacao() != null) {
            sqlAlterar.setDouble(++i, obj.getValorOperacao());
        } else {
            sqlAlterar.setNull(++i, 0);
        }
        sqlAlterar.setString(++i, obj.getUsuarioResponsavel());
        sqlAlterar.setInt(++i, obj.getCodigo());
        sqlAlterar.execute();
    }

    @Override
    public void excluir(ObservacaoOperacaoVO obj) throws Exception {
        String sql = "DELETE FROM estornoobservacao WHERE codigo = ?;";
        PreparedStatement sqlExcluir = getCon().prepareStatement(sql);
        sqlExcluir.setInt(1, obj.getCodigo());
        sqlExcluir.execute();
    }

//    @Override
//    public List<ObservacaoOperacaoVO> consultar(Integer codPessoa, int nivelMontarDados) throws Exception {
//        String sqlStr = "SELECT * FROM estornoobservacao WHERE pessoa = " + codPessoa + " ORDER BY codigo DESC;";
//        Statement stm = con.createStatement();
//        ResultSet tabelaResultado = stm.executeQuery(sqlStr);
//        return montarDadosConsulta(tabelaResultado, nivelMontarDados);
//    }

//    @Override
//    public List<ObservacaoOperacaoVO> consultarPorDataEstorno(Date dataEstorno, int nivelMontarDados) throws Exception {
//        String sqlStr = "SELECT * FROM estornoobservacao WHERE dataestorno = " + dataEstorno + " ORDER BY codigo DESC;";
//        Statement stm = con.createStatement();
//        ResultSet tabelaResultado = stm.executeQuery(sqlStr);
//        return montarDadosConsulta(tabelaResultado, nivelMontarDados);
//    }

    private List<ObservacaoOperacaoVO> montarDadosConsulta(ResultSet tabelaResultado, int nivelMontarDados) throws Exception {
        List<ObservacaoOperacaoVO> vetResultado = new ArrayList<ObservacaoOperacaoVO>();
        while (tabelaResultado.next()) {
            ObservacaoOperacaoVO obj = montarDados(tabelaResultado, nivelMontarDados);
            vetResultado.add(obj);
        }
        return vetResultado;
    }

    private ObservacaoOperacaoVO montarDados(ResultSet dadosSQL, int nivelMontarDados) throws Exception {
        ObservacaoOperacaoVO obj = new ObservacaoOperacaoVO();
        obj.setCodigo(dadosSQL.getInt("codigo"));
        obj.setJustificativa(UteisValidacao.emptyString(dadosSQL.getString("justificativa"))? dadosSQL.getString("obCancel") : dadosSQL.getString("justificativa"));
        obj.setTipoCancelamento(UteisValidacao.emptyString(dadosSQL.getString("obCancel"))? "Manual" : "Cancelamento Contrato");
        obj.setDataOperacao(dadosSQL.getTimestamp("dataoperacao"));
        obj.setTipoObservacao(TipoObservacaoOperacaoEnum.get(dadosSQL.getString("tipooperacao")));
        obj.getMovParcela().setCodigo(dadosSQL.getInt("movparcela"));
        obj.setValorOperacao(dadosSQL.getDouble("valor"));
        obj.setUsuarioResponsavel(dadosSQL.getString("usuarioresponsavel"));
        obj.setNovoObj(false);

        return obj;
    }

    public int contarPorNomeEntidadePorDataAlteracaoPorOperacao(Date dataAlteracaoInicial, Date dataAlteracaoFim,
                                                                Integer codEmpresa, boolean buscarComAdministrador,
                                                                TipoObservacaoOperacaoEnum tipoObservacao,
                                                                List<ColaboradorVO> lista, int nivelMontarDados) throws Exception {
        int qtde = 0;
        String sqlStr = "SELECT count(observacaooperacao.codigo) as cod FROM observacaooperacao\n";
        if (tipoObservacao.equals(TipoObservacaoOperacaoEnum.PARCELA_CANCELADA)) {
            sqlStr += "INNER JOIN movparcela ON movparcela.codigo = observacaooperacao.movparcela\n";
        }
        sqlStr += "WHERE dataoperacao between '" + dataAlteracaoInicial + " 00:00:00' and '" + dataAlteracaoFim + " 23:59:59'";
        sqlStr += " and " + ((!buscarComAdministrador) ? "not" : "") + " (usuarioresponsavel like 'ADMINISTRADOR' )";
        // filtra a consulta pelos colaboradores
        for (ColaboradorVO co : lista) {
            if (co.getColaboradorEscolhidoOperacoes()) {
                if (qtde == 0) {
                    sqlStr += " AND ( ";
                    qtde++;
                } else {
                    sqlStr += " OR ";
                }
                if (co.getPessoa().getNome().toUpperCase().contains("PACTO")) {
                    sqlStr += "usuarioresponsavel like '" + co.getPessoa().getNome() + "%'";
                } else {
                sqlStr += "usuarioresponsavel like '" + co.getPessoa().getNome() + "'";
            }
        }
        }
        sqlStr += (qtde > 0 ? ")" : "");

        sqlStr += "AND tipooperacao  = '" + tipoObservacao.getTipo() + "'\n";

        if (codEmpresa != null && codEmpresa > 0) {
            sqlStr += " and empresa = " + codEmpresa + "";
        }

        Statement stm = con.createStatement();
        ResultSet tabelaResultado = stm.executeQuery(sqlStr);
        if (!tabelaResultado.next()) {
            return 0;
        }
        return tabelaResultado.getInt("cod");
    }

    public List<ObservacaoOperacaoVO> consultarPorNomeEntidadePorDataAlteracaoPorOperacao(Date dataAlteracaoInicial, Date dataAlteracaoFim, Integer codEmpresa, boolean buscarComAdministrador, TipoObservacaoOperacaoEnum tipoObservacao, List<ColaboradorVO> lista, int nivelMontarDados) throws Exception {
    int qtde = 0;
        String sqlStr = "SELECT oo.*, co.observacao as obCancel FROM observacaooperacao oo\n";
        if (tipoObservacao.equals(TipoObservacaoOperacaoEnum.PARCELA_CANCELADA)) {
            sqlStr += "INNER JOIN movparcela ON movparcela.codigo = oo.movparcela\n";
            sqlStr += "LEFT JOIN contratooperacao co ON co.contrato = movparcela.contrato and co.tipooperacao = 'CA'\n";
        }
        sqlStr += "WHERE oo.dataoperacao between '" + dataAlteracaoInicial + " 00:00:00' and '" + dataAlteracaoFim + " 23:59:59'";
        sqlStr += " and " + ((!buscarComAdministrador) ? "not" : "") + " (usuarioresponsavel ilike 'ADMINISTRADOR' )";

        // filtra a consulta pelos colaboradores
        for (ColaboradorVO co : lista) {
            if (co.getColaboradorEscolhidoOperacoes()) {
                if (qtde == 0) {
                    sqlStr += " AND ( ";
                    qtde++;
                } else {
                    sqlStr += " OR ";
                }
                if (co.getPessoa().getNome().toUpperCase().contains("PACTO")) {
                    sqlStr += "usuarioresponsavel like '" + co.getPessoa().getNome() + "%'";
                } else {
                sqlStr += "usuarioresponsavel like '" + co.getPessoa().getNome() + "'";
            }
        }
        }
        sqlStr += (qtde > 0 ? ")" : "");

        sqlStr += "AND oo.tipooperacao  = '" + tipoObservacao.getTipo() + "'\n";

        if (codEmpresa != null && codEmpresa > 0) {
            sqlStr += " and empresa = " + codEmpresa + "";
        }

        Statement stm = con.createStatement();
        ResultSet tabelaResultado = stm.executeQuery(sqlStr);
        return montarDadosConsulta(tabelaResultado, nivelMontarDados);
    }
}

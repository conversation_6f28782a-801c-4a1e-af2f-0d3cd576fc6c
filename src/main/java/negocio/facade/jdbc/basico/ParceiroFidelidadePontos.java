package negocio.facade.jdbc.basico;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.Statement;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

import negocio.comuns.arquitetura.UsuarioVO;
import negocio.comuns.basico.*;
import negocio.comuns.basico.enumerador.TipoPontoParceiroFidelidadeEnum;
import negocio.comuns.financeiro.MovPagamentoVO;
import negocio.comuns.financeiro.ReciboPagamentoVO;
import negocio.comuns.utilitarias.Calendario;
import negocio.comuns.utilitarias.ConsistirException;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisValidacao;
import negocio.facade.jdbc.arquitetura.SuperEntidade;
import negocio.facade.jdbc.arquitetura.SuperFacadeJDBC;
import negocio.interfaces.basico.ParceiroFidelidadePontosInterfaceFacade;
import negocio.interfaces.basico.TabelaParceiroFidelidadeInterfaceFacade;

/**
 *
 * <AUTHOR>
 */
public class ParceiroFidelidadePontos extends SuperEntidade implements ParceiroFidelidadePontosInterfaceFacade {

    public ParceiroFidelidadePontos() throws Exception {
        super();
    }

    public ParceiroFidelidadePontos(Connection con) throws Exception {
        super(con);
    }

    @Override
    public ParceiroFidelidadePontosVO novo() throws Exception {        
        return new ParceiroFidelidadePontosVO();
    }

    @Override
    public void incluir(ParceiroFidelidadePontosVO obj) throws Exception {
        ParceiroFidelidadePontosVO.validarDados(obj);        
        obj.realizarUpperCaseDados();
        String sql = "INSERT INTO ParceiroFidelidadePontos(datalancamento, tabelaParceiroFidelidade, multiplicador, "
                + "tipoPontoParceiroFidelidade, pontos, pessoa, cpf, movPagamento, reciboPagamento, historico, retornoParceiroFidelidade, usuario) "
                + "VALUES (?,?,?,?,?,?,?,?,?,?,?,?)";

        int i = 0;
        PreparedStatement sqlInserir = con.prepareStatement(sql);
        sqlInserir.setTimestamp(++i, Uteis.getDataJDBCTimestamp(obj.getDataLancamento()));
        sqlInserir.setInt(++i, obj.getTabelaParceiroFidelidade().getCodigo());
        sqlInserir.setDouble(++i, obj.getMultiplicador());
        sqlInserir.setInt(++i, obj.getTipoPontoParceiroFidelidade().getCodigo());
        sqlInserir.setInt(++i, obj.getPontos());
        sqlInserir.setInt(++i, obj.getPessoa());
        sqlInserir.setString(++i, obj.getCpf());
        sqlInserir.setInt(++i, obj.getMovPagamento().getCodigo());
        sqlInserir.setInt(++i, obj.getReciboPagamento().getCodigo());
        sqlInserir.setString(++i, obj.getHistorico());
        sqlInserir.setString(++i, obj.getRetornoParceiroFidelidade());
        sqlInserir.setInt(++i, obj.getUsuario().getCodigo());

        sqlInserir.execute();
        obj.setCodigo(obterValorChavePrimariaCodigo());
        obj.setNovoObj(false);
    }

    @Override
    public void alterar(ParceiroFidelidadePontosVO obj) throws Exception {
        try {
            ParceiroFidelidadePontosVO.validarDados(obj);
            obj.realizarUpperCaseDados();
            String sql = "UPDATE ParceiroFidelidadePontos set datalancamento = ?, tabelaParceiroFidelidade = ?, multiplicador= ?, " +
                    "tipoPontoParceiroFidelidade = ?, pontos = ?, pessoa = ?, cpf = ?, movPagamento = ?, reciboPagamento = ?, " +
                    "historico = ?, retornoParceiroFidelidade = ?, usuario = ? WHERE codigo = ?";

            int i = 0;
            PreparedStatement sqlAlterar = con.prepareStatement(sql);
            sqlAlterar.setTimestamp(++i, Uteis.getDataJDBCTimestamp(obj.getDataLancamento()));
            sqlAlterar.setInt(++i, obj.getTabelaParceiroFidelidade().getCodigo());
            sqlAlterar.setDouble(++i, obj.getMultiplicador());
            sqlAlterar.setInt(++i, obj.getTipoPontoParceiroFidelidade().getCodigo());
            sqlAlterar.setInt(++i, obj.getPontos());
            sqlAlterar.setInt(++i, obj.getPessoa());
            sqlAlterar.setString(++i, obj.getCpf());
            sqlAlterar.setInt(++i, obj.getMovPagamento().getCodigo());
            sqlAlterar.setInt(++i, obj.getReciboPagamento().getCodigo());
            sqlAlterar.setString(++i, obj.getHistorico());
            sqlAlterar.setString(++i, obj.getRetornoParceiroFidelidade());
            sqlAlterar.setInt(++i, obj.getUsuario().getCodigo());

            sqlAlterar.setInt(++i, obj.getCodigo());
            sqlAlterar.execute();
        } catch (Exception e) {
            throw e;
        }
    }

    @Override
    public void excluir(ParceiroFidelidadePontosVO obj) throws Exception {
        try {            
            String sql = "DELETE FROM ParceiroFidelidadePontos WHERE ((codigo = ?))";
            PreparedStatement ps = con.prepareStatement(sql);
            int i = 0;
            ps.setInt(++i, obj.getCodigo());
            ps.execute();
        } catch (Exception e) {
            throw e;
        }
    }

    @Override
    public List<ParceiroFidelidadePontosVO> consultarPorHistorico(String valorConsulta, boolean controlarAcesso, int nivelMontarDados) throws Exception {
        String sqlStr = "SELECT * FROM ParceiroFidelidadePontos WHERE upper( historico ) like('" + valorConsulta.toUpperCase() + "%') ORDER BY historico";
        Statement stm = con.createStatement();
        ResultSet tabelaResultado = stm.executeQuery(sqlStr);
        return montarDadosConsulta(tabelaResultado, nivelMontarDados);
    }

    @Override
    public List<ParceiroFidelidadePontosVO> consultarPorCodigo(Integer valorConsulta, boolean controlarAcesso, int nivelMontarDados) throws Exception {
        String sqlStr = "SELECT * FROM ParceiroFidelidadePontos WHERE codigo = " + valorConsulta + " ORDER BY codigo";
        Statement stm = con.createStatement();
        ResultSet tabelaResultado = stm.executeQuery(sqlStr);
        return montarDadosConsulta(tabelaResultado, nivelMontarDados);
    }

    public List<ParceiroFidelidadePontosVO> montarDadosConsulta(ResultSet tabelaResultado, int nivelMontarDados) throws Exception {
        List<ParceiroFidelidadePontosVO> vetResultado = new ArrayList<ParceiroFidelidadePontosVO>();
        while (tabelaResultado.next()) {
            ParceiroFidelidadePontosVO obj = montarDados(tabelaResultado, nivelMontarDados);
            vetResultado.add(obj);
        }
        return vetResultado;
    }

    public ParceiroFidelidadePontosVO montarDados(ResultSet rs, int nivelMontarDados) throws Exception {
        ParceiroFidelidadePontosVO obj = new ParceiroFidelidadePontosVO();
        obj.setCodigo(rs.getInt("codigo"));
        obj.setHistorico(rs.getString("historico"));
        TabelaParceiroFidelidadeInterfaceFacade tabelaParceiroDao = new TabelaParceiroFidelidade(con);
        obj.setTabelaParceiroFidelidade(tabelaParceiroDao.consultarPorChavePrimaria(rs.getInt("tabelaParceiroFidelidade"), Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA));
        obj.setMultiplicador(rs.getDouble("multiplicador"));
        obj.setPontos(rs.getInt("pontos"));
        obj.setDataLancamento(rs.getTimestamp("dataLancamento"));
        obj.setPessoa(rs.getInt("pessoa"));
        obj.setCpf(rs.getString("cpf"));
        obj.setMovPagamento(new MovPagamentoVO());
        obj.getMovPagamento().setCodigo(rs.getInt("movPagamento"));
        obj.setReciboPagamento(new ReciboPagamentoVO());
        obj.getReciboPagamento().setCodigo(rs.getInt("reciboPagamento"));
        obj.setTipoPontoParceiroFidelidade(TipoPontoParceiroFidelidadeEnum.valueOf(rs.getInt("tipoPontoParceiroFidelidade")));
        obj.setRetornoParceiroFidelidade(rs.getString("retornoParceiroFidelidade"));
        obj.setUsuario(new UsuarioVO());
        obj.getUsuario().setCodigo(rs.getInt("usuario"));
        obj.setNovoObj(false);

        if (nivelMontarDados == Uteis.NIVELMONTARDADOS_DADOSBASICOS) {
            return obj;
        }

        return obj;
    }

    @Override
    public ParceiroFidelidadePontosVO consultarPorChavePrimaria(Integer codigoPrm, int nivelMontarDados) throws Exception {        
        String sql = "SELECT * FROM ParceiroFidelidadePontos WHERE codigo = ?";
        PreparedStatement ps = con.prepareStatement(sql);
        int i = 0;
        ps.setInt(++i, codigoPrm);
        ResultSet tabelaResultado = ps.executeQuery();
        if (!tabelaResultado.next()) {
            return new ParceiroFidelidadePontosVO();
        }
        return (montarDados(tabelaResultado, nivelMontarDados));
    }

    @Override
    public void inserirPontos(ParceiroFidelidadePontosVO parceiroFidelidadePontosVO, String retornoAPIParceiro) throws Exception {
        parceiroFidelidadePontosVO.setRetornoParceiroFidelidade(retornoAPIParceiro);
        parceiroFidelidadePontosVO.setHistorico(parceiroFidelidadePontosVO.getMsgGravarHistorico());
        incluir(parceiroFidelidadePontosVO);
    }

    @Override
    public List<ParceiroFidelidadePontosVO> consultarRelatorio(Date dataInicio, Date dataFinal, Integer pessoa) throws Exception {
        List<ParceiroFidelidadePontosVO> retorno = new ArrayList<ParceiroFidelidadePontosVO>();

        StringBuilder sql = new StringBuilder();
        sql.append("select  \n");
        sql.append("c.codigo as codigoCliente, \n");
        sql.append("c.matricula, \n");
        sql.append("p.nome, \n");
        sql.append("t.nometabela, \n");
        sql.append("t.defaultrecorrencia, \n");
        sql.append("mp.valortotal, \n");
        sql.append("po.* \n");
        sql.append("from parceirofidelidadepontos po \n");
        sql.append("inner join pessoa p on p.codigo = po.pessoa \n");
        sql.append("left join cliente c on c.pessoa = p.codigo \n");
        sql.append("left join tabelaparceirofidelidade t on t.codigo = po.tabelaparceirofidelidade \n");
        sql.append("left join movpagamento mp on mp.codigo = po.movpagamento \n");
        sql.append("where 1 = 1  \n");
        if (dataInicio != null && dataFinal != null) {
            sql.append("and po.datalancamento::date between '").append(Uteis.getDataJDBC(dataInicio)).append("' and '").append(Uteis.getDataJDBC(dataFinal)).append("' \n");
        }
        if (!UteisValidacao.emptyNumber(pessoa)) {
            sql.append("and po.pessoa = ").append(pessoa);
        }
        Statement stm = con.createStatement();
        ResultSet rs = stm.executeQuery(sql.toString());
        while (rs.next()) {
            ParceiroFidelidadePontosVO obj = new ParceiroFidelidadePontosVO();
            obj.getClienteVO().setCodigo(rs.getInt("codigoCliente"));
            obj.getClienteVO().setMatricula(rs.getString("matricula"));
            obj.getClienteVO().getPessoa().setNome(rs.getString("nome"));
            obj.getClienteVO().getPessoa().setNome(rs.getString("nome"));
            obj.setPontos(rs.getInt("pontos"));
            obj.setDataLancamento(rs.getTimestamp("datalancamento"));
            obj.setHistorico(rs.getString("historico"));
            obj.getTabelaParceiroFidelidade().setCodigo(rs.getInt("tabelaParceiroFidelidade"));
            obj.getTabelaParceiroFidelidade().setNomeTabela(rs.getString("nometabela"));
            obj.getTabelaParceiroFidelidade().setDefaultRecorrencia(rs.getBoolean("defaultrecorrencia"));
            obj.setMultiplicador(rs.getDouble("multiplicador"));
            obj.setPontos(rs.getInt("pontos"));
            obj.setPessoa(rs.getInt("pessoa"));
            obj.setCpf(rs.getString("cpf"));
            obj.getMovPagamento().setCodigo(rs.getInt("movPagamento"));
            obj.getMovPagamento().setValorTotal(rs.getDouble("valortotal"));
            obj.getReciboPagamento().setCodigo(rs.getInt("reciboPagamento"));
            obj.setTipoPontoParceiroFidelidade(TipoPontoParceiroFidelidadeEnum.valueOf(rs.getInt("tipoPontoParceiroFidelidade")));
            obj.setRetornoParceiroFidelidade(rs.getString("retornoParceiroFidelidade"));
            retorno.add(obj);
        }
        return retorno;
    }

    public void alterarTipoPonto(TipoPontoParceiroFidelidadeEnum tipoPontoParceiroFidelidade, Integer codigo) throws Exception {
        String sql = "UPDATE parceirofidelidadepontos set tipoPontoParceiroFidelidade = ? WHERE codigo = ?";
        PreparedStatement ps = con.prepareStatement(sql);
        ps.setInt(1, tipoPontoParceiroFidelidade.getCodigo());
        ps.setInt(2, codigo);
        ps.executeUpdate();
    }

    @Override
    public List<ParceiroFidelidadePontosVO> consultarPorReciboPagamento(Integer reciboPagamento, int nivelMontarDados) throws Exception {
        String sqlStr = "SELECT * FROM ParceiroFidelidadePontos WHERE recibopagamento = " + reciboPagamento;
        Statement stm = con.createStatement();
        ResultSet tabelaResultado = stm.executeQuery(sqlStr);
        return montarDadosConsulta(tabelaResultado, nivelMontarDados);
    }
}
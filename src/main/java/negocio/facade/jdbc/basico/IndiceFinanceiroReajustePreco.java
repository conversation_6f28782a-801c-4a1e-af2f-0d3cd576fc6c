package negocio.facade.jdbc.basico;

import br.com.pactosolucoes.enumeradores.TipoPlanoEnum;
import negocio.comuns.basico.enumerador.TipoIndiceFinanceiroEnum;
import negocio.comuns.plano.IndiceFinanceiroReajustePrecoVO;
import negocio.comuns.utilitarias.Calendario;
import negocio.comuns.utilitarias.ConsistirException;
import negocio.comuns.utilitarias.Ordenacao;
import negocio.comuns.utilitarias.Uteis;
import negocio.facade.jdbc.arquitetura.SuperEntidade;
import negocio.interfaces.basico.IndiceFinanceiroReajustePrecoInterfaceFacade;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.sql.Statement;
import java.text.DecimalFormat;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

/**
 * Created by ulisses on 19/11/2016.
 */
public class IndiceFinanceiroReajustePreco extends SuperEntidade implements IndiceFinanceiroReajustePrecoInterfaceFacade {

    public IndiceFinanceiroReajustePreco() throws Exception {
        super();
    }

    public IndiceFinanceiroReajustePreco(Connection conexao) throws Exception {
        super(conexao);
    }


    public void incluir(IndiceFinanceiroReajustePrecoVO indiceFinanceiroReajustePrecoVO)throws Exception{
        try{
            DecimalFormat df = new DecimalFormat("00");
            IndiceFinanceiroReajustePrecoVO.validarDados(indiceFinanceiroReajustePrecoVO);
            StringBuilder sql = new StringBuilder();
            sql.append("insert into indiceFinanceiroReajustePreco (tipoindice, mes, ano, percentualacumulado,aplicarreajusterenovacaocontratorecorrencia, datalancamento, tipoPlano) \n");
            sql.append("values(?,?,?,?,?,?,?)");
            try (PreparedStatement pst = con.prepareStatement(sql.toString())) {
                pst.setInt(1, indiceFinanceiroReajustePrecoVO.getTipoIndice());
                pst.setString(2, df.format(Integer.parseInt(indiceFinanceiroReajustePrecoVO.getMes())));
                pst.setString(3, indiceFinanceiroReajustePrecoVO.getAno());
                pst.setDouble(4, indiceFinanceiroReajustePrecoVO.getPercentualAcumulado());
                pst.setBoolean(5, indiceFinanceiroReajustePrecoVO.getAplicarreajusterenovacaocontratorecorrencia());
                pst.setTimestamp(6, Uteis.getDataJDBCTimestamp(Calendario.hoje()));
                pst.setInt(7, indiceFinanceiroReajustePrecoVO.getTipoPlano());
                pst.execute();
            }
            indiceFinanceiroReajustePrecoVO.setCodigo(obterValorChavePrimariaCodigo());
            indiceFinanceiroReajustePrecoVO.setNovoObj(false);
        }catch (Exception ex){
            if (ex.getMessage().toUpperCase().contains("MESANOINDICEFINANCEIRO_UNIQUE")){
                throw new ConsistirException("Operação não permitida. Já existe um cadastro de índice financeiro para o mês/ano informado.");
            }
            throw ex;
        }

    }
    public void alterar(IndiceFinanceiroReajustePrecoVO indiceFinanceiroReajustePrecoVO)throws Exception{
        try{
            IndiceFinanceiroReajustePrecoVO.validarDados(indiceFinanceiroReajustePrecoVO);
            DecimalFormat df = new DecimalFormat("00");
            StringBuilder sql = new StringBuilder();
            sql.append("update indiceFinanceiroReajustePreco set tipoindice=?, mes=?, ano=?, percentualacumulado=?, " +
                    "aplicarreajusterenovacaocontratorecorrencia=?, tipoPlano=? " +
                    "where codigo = ?");
            try (PreparedStatement pst = con.prepareStatement(sql.toString())) {
                pst.setInt(1, indiceFinanceiroReajustePrecoVO.getTipoIndice());
                pst.setString(2, df.format(Integer.parseInt(indiceFinanceiroReajustePrecoVO.getMes())));
                pst.setString(3, indiceFinanceiroReajustePrecoVO.getAno());
                pst.setDouble(4, indiceFinanceiroReajustePrecoVO.getPercentualAcumulado());
                pst.setBoolean(5, indiceFinanceiroReajustePrecoVO.getAplicarreajusterenovacaocontratorecorrencia());
                pst.setInt(6, indiceFinanceiroReajustePrecoVO.getTipoPlano());
                pst.setInt(7, indiceFinanceiroReajustePrecoVO.getCodigo());
                pst.execute();
            } catch (Exception e){
                e.printStackTrace();
            }
        }catch (Exception ex){
            if (ex.getMessage().toUpperCase().contains("MESANOINDICEFINANCEIRO_UNIQUE")){
                throw new ConsistirException("Operação não permitida. Já existe um cadastro de índice financeiro para o mês/ano informado.");
            }
            throw ex;
        }

    }

    public void excluir(IndiceFinanceiroReajustePrecoVO indiceFinanceiroReajustePrecoVO)throws Exception{
        StringBuilder sql = new StringBuilder();
        sql.append("delete from indiceFinanceiroReajustePreco where codigo =?");
        try (PreparedStatement pst = con.prepareStatement(sql.toString())) {
            pst.setInt(1, indiceFinanceiroReajustePrecoVO.getCodigo());
            pst.execute();
        }
    }

    public IndiceFinanceiroReajustePrecoVO consultar(String mes, String ano, int nivelMontarDados)throws Exception{
        StringBuilder sql = new StringBuilder();
        sql.append("select * \n");
        sql.append("from indiceFinanceiroReajustePreco \n");
        sql.append("where mes = '").append(mes).append("' \n");
        sql.append("and ano = '").append(ano).append("'");
        try (Statement st = con.createStatement()) {
            try (ResultSet rs = st.executeQuery(sql.toString())) {
                if (rs.next()) {
                    return montarDados(rs, nivelMontarDados);
                }
            }
        }
        return null;
    }

    public IndiceFinanceiroReajustePrecoVO consultarIndiceFinanceiroPeriodo(String mes, String ano, Boolean renovacaoAutoamtica, TipoPlanoEnum tipoPlanoEnum, boolean somentePercentualPositivo, int nivelMontarDados)throws Exception{
        StringBuilder sql = new StringBuilder();
        sql.append("SELECT * \n");
        sql.append("FROM indiceFinanceiroReajustePreco \n");
        sql.append("WHERE mes::int = '").append(mes).append("'\n");
        sql.append("AND ano = '").append(ano).append("'\n");
        if (somentePercentualPositivo) {
            sql.append("AND percentualAcumulado > 0\n");
        }
        sql.append("AND aplicarreajusterenovacaocontratorecorrencia = ").append(renovacaoAutoamtica).append("\n");
        sql.append("AND tipoplano = ").append(tipoPlanoEnum.getCodigo());

        try (Statement st = con.createStatement()) {
            try (ResultSet rs = st.executeQuery(sql.toString())) {
                if (rs.next()) {
                    return montarDados(rs, nivelMontarDados);
                }
            }
        }
        return null;

    }

    public IndiceFinanceiroReajustePrecoVO consultarPorCodigo(Integer codigo, int nivelMontarDados)throws Exception{
        StringBuilder sql = new StringBuilder();
        sql.append("select * \n");
        sql.append("from indiceFinanceiroReajustePreco \n");
        sql.append("where codigo = ").append(codigo);
        try (Statement st = con.createStatement()) {
            try (ResultSet rs = st.executeQuery(sql.toString())) {
                if (rs.next()) {
                    return montarDados(rs, nivelMontarDados);
                }
            }
        }
        return null;
    }

    public static IndiceFinanceiroReajustePrecoVO montarDadosBasico(ResultSet rs) throws Exception {
        IndiceFinanceiroReajustePrecoVO obj = new IndiceFinanceiroReajustePrecoVO();
        obj.setNovoObj(false);
        obj.setCodigo(rs.getInt("codigo"));
        obj.setTipoIndice(rs.getInt("tipoIndice"));
        obj.setMes(rs.getString("mes"));
        obj.setAno(rs.getString("ano"));
        obj.setPercentualAcumulado(rs.getDouble("percentualAcumulado"));
        obj.setAplicarreajusterenovacaocontratorecorrencia(rs.getBoolean("aplicarreajusterenovacaocontratorecorrencia"));
        obj.setDatalancamento(rs.getTimestamp("dataLancamento"));
        obj.setTipoPlano(rs.getInt("tipoPlano"));
        return obj;
    }

    public static List<IndiceFinanceiroReajustePrecoVO> montarDadosConsulta(ResultSet rs, int nivelMontarDados) throws Exception {
        List<IndiceFinanceiroReajustePrecoVO> lista = new ArrayList<IndiceFinanceiroReajustePrecoVO>();
        while (rs.next()) {
            IndiceFinanceiroReajustePrecoVO obj = montarDados(rs, nivelMontarDados);
            lista.add(obj);
        }
        return lista;
    }

    public static IndiceFinanceiroReajustePrecoVO montarDados(ResultSet rs, int nivelMontarDados) throws Exception {
        IndiceFinanceiroReajustePrecoVO obj = montarDadosBasico(rs);
        if (nivelMontarDados == Uteis.NIVELMONTARDADOS_DADOSBASICOS) {
            return obj;
        }
        return obj;
    }

    private PreparedStatement getPS() throws SQLException {
        StringBuilder sql = new StringBuilder();
        sql.append("select *  \n");
        sql.append("from indicefinanceiroreajustepreco  \n");
        sql.append("order by ano, mes");
        return con.prepareStatement(sql.toString());
    }

    public List consultarParaImpressao(String filtro, String ordem, String campoOrdenacao) throws Exception {
        List lista;
        try (ResultSet rs = getPS().executeQuery()) {
            lista = new ArrayList();
            while (rs.next()) {
                String geral = rs.getString("codigo") + rs.getString("tipoindice") + rs.getString("mes") + rs.getString("ano");
                if (geral.toLowerCase().contains(filtro.toLowerCase())) {
                    IndiceFinanceiroReajustePrecoVO indiceFinanceiroReajustePrecoVO = montarDadosBasico(rs);
                    lista.add(indiceFinanceiroReajustePrecoVO);
                }
            }
        }
        if (campoOrdenacao.equals("Código")) {
            Ordenacao.ordenarLista(lista, "codigo");
        } else if (campoOrdenacao.equals("Tipo Índice")) {
            Ordenacao.ordenarLista(lista, "TipoIndice");
        } else if (campoOrdenacao.equals("Mês")) {
            Ordenacao.ordenarLista(lista, "mes");
        } else if (campoOrdenacao.equals("Ano")) {
            Ordenacao.ordenarLista(lista, "ano");
        }
        if (ordem.contains("desc")) {
            Collections.reverse(lista);
        }
        return lista;
    }

    public String consultarJSON() throws Exception {
        StringBuilder json;
        boolean dados;
        try (ResultSet rs = getPS().executeQuery()) {
            json = new StringBuilder();
            json.append("{\"aaData\":[");
            dados = false;
            while (rs.next()) {
                dados = true;
                json.append("[\"").append(rs.getString("codigo")).append("\",");
                TipoIndiceFinanceiroEnum tipoIndiceFinanceiroEnum = TipoIndiceFinanceiroEnum.getIndice(rs.getInt("tipoIndice"));
                json.append("\"").append(Uteis.normalizarStringJSON(tipoIndiceFinanceiroEnum.getSigla())).append("\",");
                json.append("\"").append(Uteis.normalizarStringJSON(TipoPlanoEnum.getTipoPlanoEnum(rs.getInt("tipoplano")).getDescricao())).append("\",");
                json.append("\"").append(Uteis.normalizarStringJSON(String.format("%.2f", rs.getDouble("PercentualAcumulado")) + " %")).append("\",");
                json.append("\"").append(Uteis.normalizarStringJSON(rs.getString("mes").trim())).append("\",");
                json.append("\"").append(Uteis.normalizarStringJSON(rs.getString("ano").trim())).append("\"],");
            }
        }
        if(dados) {
            json.deleteCharAt(json.toString().length()-1);
        }
        json.append("]}");
        return json.toString();
    }



}

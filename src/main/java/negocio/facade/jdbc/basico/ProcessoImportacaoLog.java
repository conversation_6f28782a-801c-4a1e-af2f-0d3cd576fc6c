package negocio.facade.jdbc.basico;

import negocio.comuns.basico.ProcessoImportacaoLogVO;
import negocio.comuns.basico.enumerador.TipoLogEnum;
import negocio.comuns.utilitarias.Calendario;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisValidacao;
import negocio.facade.jdbc.arquitetura.SuperEntidade;
import negocio.interfaces.basico.ProcessoImportacaoInterfaceFacade;
import negocio.interfaces.basico.ProcessoImportacaoLogInterfaceFacade;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.util.ArrayList;
import java.util.List;

/**
 * Created by <PERSON><PERSON> on 20/12/2019.
 */
public class ProcessoImportacaoLog extends SuperEntidade implements ProcessoImportacaoLogInterfaceFacade {

    public ProcessoImportacaoLog() throws Exception {
        super();
        setIdEntidade("Empresa");
    }

    public ProcessoImportacaoLog(Connection con) throws Exception {
        super(con);
        setIdEntidade("Empresa");
    }

    @Override
    public void incluir(ProcessoImportacaoLogVO obj) throws Exception {
        String sql = "INSERT INTO ProcessoImportacaoLog(processoImportacao, dataRegistro, tipoLog, mensagem) VALUES (?,?,?,?)";
        int i = 0;
        PreparedStatement ps = con.prepareStatement(sql);
        resolveIntegerNull(ps, ++i, obj.getProcessoImportacaoVO().getCodigo());
        ps.setTimestamp(++i, Uteis.getDataJDBCTimestamp(Calendario.hoje()));
        ps.setInt(++i, obj.getTipoLog().getCodigo());
        ps.setString(++i, obj.getMensagem());
        ps.execute();
        obj.setCodigo(obterValorChavePrimariaCodigo());
        obj.setNovoObj(false);
    }

    @Override
    public void excluir(ProcessoImportacaoLogVO obj) throws Exception {
        String sql = "DELETE FROM ProcessoImportacaoLog WHERE codigo = ?";
        PreparedStatement ps = con.prepareStatement(sql);
        int i = 0;
        ps.setInt(++i, obj.getCodigo());
        ps.execute();
    }

    public List<ProcessoImportacaoLogVO> montarDadosConsulta(ResultSet tabelaResultado, int nivelMontarDados) throws Exception {
        List<ProcessoImportacaoLogVO> vetResultado = new ArrayList<>();
        while (tabelaResultado.next()) {
            ProcessoImportacaoLogVO obj = montarDados(tabelaResultado, nivelMontarDados);
            vetResultado.add(obj);
        }
        return vetResultado;
    }

    public ProcessoImportacaoLogVO montarDados(ResultSet rs, int nivelMontarDados) throws Exception {
        ProcessoImportacaoLogVO obj = new ProcessoImportacaoLogVO();
        obj.setCodigo(rs.getInt("codigo"));
        obj.setNovoObj(false);
        obj.getProcessoImportacaoVO().setCodigo(rs.getInt("processoImportacao"));
        obj.setDataRegistro(rs.getTimestamp("dataRegistro"));
        obj.setTipoLog(TipoLogEnum.obterPorCodigo(rs.getInt("tipoLog")));
        obj.setMensagem(rs.getString("mensagem"));

        if (nivelMontarDados == Uteis.NIVELMONTARDADOS_DADOSBASICOS) {
            return obj;
        }
        return obj;
    }

    @Override
    public ProcessoImportacaoLogVO consultarPorChavePrimaria(Integer codigo, int nivelMontarDados) throws Exception {
        String sql = "SELECT * FROM ProcessoImportacao WHERE codigo = ?";
        PreparedStatement ps = con.prepareStatement(sql);
        int i = 0;
        ps.setInt(++i, codigo);
        ResultSet rs = ps.executeQuery();
        if (!rs.next()) {
            return new ProcessoImportacaoLogVO();
        }
        return montarDados(rs, nivelMontarDados);
    }

    public List<ProcessoImportacaoLogVO> consultarPorProcessoImportacao(Integer processoImportacao, Integer limit, Integer offSet, int nivelMontarDados) throws Exception {
        StringBuilder sql = new StringBuilder();
        sql.append("SELECT * FROM ProcessoImportacaoLog \n");
        sql.append(" WHERE processoImportacao = ").append(processoImportacao).append(" \n");
        sql.append("ORDER BY dataRegistro desc \n");
        if (!UteisValidacao.emptyNumber(limit)) {
            sql.append(" LIMIT ").append(limit).append(" \n");
        }
        if (!UteisValidacao.emptyNumber(offSet)) {
            sql.append(" OFFSET ").append(offSet).append(" \n");
        }
        PreparedStatement ps = con.prepareStatement(sql.toString());
        ResultSet rs = ps.executeQuery();
        return montarDadosConsulta(rs, nivelMontarDados);
    }
}

package negocio.facade.jdbc.basico;

import negocio.comuns.basico.ColaboradorIndisponivelCrmVO;
import negocio.comuns.basico.ColaboradorVO;
import negocio.comuns.utilitarias.ConsistirException;
import negocio.comuns.utilitarias.Uteis;
import negocio.facade.jdbc.arquitetura.SuperEntidade;
import negocio.interfaces.basico.ColaboradorIndisponivelCrmInterfaceFacade;

import java.lang.Exception;
import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

public class ColaboradorIndisponivelCrm extends SuperEntidade implements ColaboradorIndisponivelCrmInterfaceFacade {

    public ColaboradorIndisponivelCrm() throws Exception {
        super();
    }

    public ColaboradorIndisponivelCrm(Connection conexao) throws Exception {
        super(conexao);
    }

    public void validarDados(ColaboradorIndisponivelCrmVO obj) throws Exception {

        if (obj.getEmpresaVO().getCodigo() == 0){
            throw new ConsistirException("Empresa não informada!");
        }
        if (obj.getMotivo().equals("")){
            throw new ConsistirException("Favor informar o motivo da indisponibilidade!");
        }
        if (obj.getDtInicio() == null){
            throw new ConsistirException("Favor informar a Data inicio da indisponibilidade!");
        }
        if (obj.getDtFim() == null){
            throw new ConsistirException("Favor informar a Data Fim da indisponibilidade!");
        }
        if (obj.getColaboradorIndisponivelVO().getCodigo() == 0){
            throw new ConsistirException("Favor informar o colaborador indisponível");
        }
        if (obj.getColaboradorSuplenteVO().getCodigo() == 0){
            throw new ConsistirException("Favor informar o colaborador suplente!");
        }
    }

    public void incluirSemCommit(final ColaboradorIndisponivelCrmVO obj) throws Exception{
        validarDados(obj);
        final String sqlInsert = "insert into colaboradorIndisponivelCrm (empresa, colaboradorSuplente, colaboradorIndisponivel, dtInicio, dtFim, motivo) values (?, ?, ?, ?, ?, ?)";
        PreparedStatement sqlInserir = con.prepareStatement(sqlInsert);

        int i = 0;
        sqlInserir.setInt(++i, obj.getEmpresaVO().getCodigo());
        sqlInserir.setInt(++i, obj.getColaboradorSuplenteVO().getCodigo());
        sqlInserir.setInt(++i, obj.getColaboradorIndisponivelVO().getCodigo());
        sqlInserir.setDate(++i, Uteis.getDataJDBC(obj.getDtInicio()));
        sqlInserir.setDate(++i, Uteis.getDataJDBC(obj.getDtFim()));
        sqlInserir.setString(++i, obj.getMotivo().toUpperCase());
        sqlInserir.execute();
        obj.setCodigo(obterValorChavePrimariaCodigo());
    }

    public void incluir(final ColaboradorIndisponivelCrmVO obj) throws Exception {
        try {
            con.setAutoCommit(false);
            incluirSemCommit(obj);
            con.commit();
            obj.setNovoObj(false);
        } catch (Exception e) {
            con.rollback();
            throw e;
        } finally {
            con.setAutoCommit(true);
        }
    }

    public void alterar(final ColaboradorIndisponivelCrmVO obj) throws Exception {
        try {
            validarDados(obj);

            con.setAutoCommit(false);

            final String sqlUpdate = "update colaboradorIndisponivelCrm set empresa=?, colaboradorSuplente=?, colaboradorIndisponivel=?, dtInicio=?, dtFim=?, motivo=? where codigo=?";
            PreparedStatement sqlAlterar= con.prepareStatement(sqlUpdate);

            int i = 0;
            sqlAlterar.setInt(++i, obj.getEmpresaVO().getCodigo());
            sqlAlterar.setInt(++i, obj.getColaboradorSuplenteVO().getCodigo());
            sqlAlterar.setInt(++i, obj.getColaboradorIndisponivelVO().getCodigo());
            sqlAlterar.setDate(++i, Uteis.getDataJDBC(obj.getDtInicio()));
            sqlAlterar.setDate(++i, Uteis.getDataJDBC(obj.getDtFim()));
            sqlAlterar.setString(++i, obj.getMotivo().toUpperCase());
            sqlAlterar.setInt(++i, obj.getCodigo().intValue());
            sqlAlterar.execute();
            con.commit();

        } catch (Exception e) {
            con.rollback();
            throw e;
        } finally {
            con.setAutoCommit(true);
        }
    }

    public void excluir(final ColaboradorIndisponivelCrmVO obj) throws Exception {
        try {
            con.setAutoCommit(false);
            excluirSemCommit(obj);
            con.commit();
        } catch (Exception e) {
            con.rollback();
            con.setAutoCommit(true);
            throw e;
        } finally {
            con.setAutoCommit(true);
        }

    }

    public void excluirSemCommit(final ColaboradorIndisponivelCrmVO obj) throws Exception {
        final String sql = "DELETE FROM colaboradorIndisponivelCrm WHERE codigo = ?";
        PreparedStatement sqlExcluir = con.prepareStatement(sql);
        sqlExcluir.setInt(1, obj.getCodigo());
        sqlExcluir.execute();
    }


    public void incluirColaboradorIndisponivelCrmSemCommit(List<ColaboradorIndisponivelCrmVO> lista) throws Exception{
        for (ColaboradorIndisponivelCrmVO obj: lista){
            if (obj.getCodigo() != null){
                excluirSemCommit(obj);
            }
            incluirSemCommit(obj);
            getFacade().getGrupoColaboradorParticipante().alteraTipoVisaoSemCommit(obj.getColaboradorSuplenteVO().getCodigo(),"IG");
        }
    }

    public void incluirColaboradorIndisponivelCrm(List<ColaboradorIndisponivelCrmVO> lista) throws Exception{
        for (ColaboradorIndisponivelCrmVO obj: lista){
            if (obj.getCodigo() != null){
                excluir(obj);
            }
            incluir(obj);
            getFacade().getGrupoColaboradorParticipante().alteraTipoVisao(obj.getColaboradorSuplenteVO().getCodigo(),"IG");
        }
    }


    public List<ColaboradorIndisponivelCrmVO> consultarPorColaboradorSuplente(int colaboradorSuplente, int empresa) throws Exception {
        StringBuilder sql;
        PreparedStatement pst;
        ResultSet tabelaResultado;
        try{
            sql = new StringBuilder("select * from colaboradorindisponivelcrm where colaboradorsuplente = ? and empresa = ?");
            pst = con.prepareStatement(sql.toString());
            pst.setInt(1, colaboradorSuplente);
            pst.setInt(2, empresa);
            tabelaResultado = pst.executeQuery();
            return montarDadosConsulta(tabelaResultado, Uteis.NIVELMONTARDADOS_TODOS);
        }catch (Exception e){
            throw e;
        }finally {
            sql = null;
            tabelaResultado = null;
            pst = null;
        }
    }


    public List<ColaboradorIndisponivelCrmVO> consultarPorColaborador(int colaborador, int empresa) throws Exception {
        StringBuilder sql;
        PreparedStatement pst;
        ResultSet tabelaResultado;
        try{
            sql = new StringBuilder("select * from colaboradorindisponivelcrm where colaboradorIndisponivel = ? and empresa = ?");
            pst = con.prepareStatement(sql.toString());
            pst.setInt(1, colaborador);
            pst.setInt(2, empresa);
            tabelaResultado = pst.executeQuery();
            return montarDadosConsulta(tabelaResultado, Uteis.NIVELMONTARDADOS_TODOS);
        }catch (Exception e){
            throw e;
        }finally {
            sql = null;
            tabelaResultado = null;
            pst = null;
        }
    }
    //  está tem como finalidade consultar colaboradores que estajam indiponiveís ou cobrindo meta
    // de algum outro colaborador.
    public List<ColaboradorIndisponivelCrmVO> consultarPorColaboradorIndispOuSupl(int colaborador, int empresa) throws Exception {
        StringBuilder sql;
        PreparedStatement pst;
        ResultSet tabelaResultado;
        try{
            sql = new StringBuilder();
            sql.append(" SELECT * FROM colaboradorindisponivelcrm\n");
            sql.append(" WHERE \n");
            sql.append(" (colaboradorIndisponivel = ?\n");
            sql.append(" OR colaboradorsuplente = ? )\n");
            sql.append(" AND empresa = ?\n");
            pst = con.prepareStatement(sql.toString());
            pst.setInt(1, colaborador);
            pst.setInt(2, colaborador);
            pst.setInt(3, empresa);
            tabelaResultado = pst.executeQuery();
            return montarDadosConsulta(tabelaResultado, Uteis.NIVELMONTARDADOS_TODOS);
        }catch (Exception e){
            throw e;
        }finally {
            sql = null;
            tabelaResultado = null;
            pst = null;
        }
    }

     public List<ColaboradorIndisponivelCrmVO> consultarPorEmpresa(int empresa) throws Exception {
        StringBuilder sql;
        PreparedStatement pst;
        ResultSet tabelaResultado;
        try{
            sql = new StringBuilder();
            sql.append(" SELECT * FROM colaboradorindisponivelcrm\n");
            sql.append(" WHERE \n");
            sql.append(" empresa = ?\n");
            pst = con.prepareStatement(sql.toString());
            pst.setInt(1, empresa);
            tabelaResultado = pst.executeQuery();
            return montarDadosConsulta(tabelaResultado, Uteis.NIVELMONTARDADOS_TODOS);
        }catch (Exception e){
            throw e;
        }finally {
            sql = null;
            tabelaResultado = null;
            pst = null;
        }
    }

    public List<ColaboradorIndisponivelCrmVO> consultarPorColaboradorSuplentePorPeriodo(int colaboradorSuplente, int empresa, Date data) throws Exception {
        StringBuilder sql;
        PreparedStatement pst;
        ResultSet tabelaResultado;
        try{
            sql = new StringBuilder("select * from colaboradorindisponivelcrm where colaboradorsuplente = ? and empresa = ? and dtinicio::date <= '").append(Uteis.getDataJDBC(data)).append("' and dtfim::date >= '").append(Uteis.getDataJDBC(data)).append("'");
            pst = con.prepareStatement(sql.toString());
            pst.setInt(1, colaboradorSuplente);
            pst.setInt(2, empresa);
            tabelaResultado = pst.executeQuery();
            return montarDadosConsulta(tabelaResultado, Uteis.NIVELMONTARDADOS_TODOS);
        }catch (Exception e){
            throw e;
        }finally {
            sql = null;
            tabelaResultado = null;
            pst = null;
        }
    }


    public List<ColaboradorIndisponivelCrmVO> montarDadosConsulta(ResultSet tabelaResultado, int nivelMontarDados) throws Exception {
        List<ColaboradorIndisponivelCrmVO> vetResultado = new ArrayList<ColaboradorIndisponivelCrmVO>();
        while (tabelaResultado.next()) {
            ColaboradorIndisponivelCrmVO obj = montarDados(tabelaResultado,nivelMontarDados);
            vetResultado.add(obj);
        }
        return vetResultado;
    }


    public ColaboradorIndisponivelCrmVO montarDados(ResultSet dadosSQL, int nivelMontarDados) throws Exception{
        ColaboradorIndisponivelCrmVO obj = new ColaboradorIndisponivelCrmVO();
        obj.setCodigo(dadosSQL.getInt("codigo"));
        obj.getEmpresaVO().setCodigo(dadosSQL.getInt("empresa"));
        obj.getColaboradorIndisponivelVO().setCodigo(dadosSQL.getInt("colaboradorindisponivel"));
        obj.getColaboradorSuplenteVO().setCodigo(dadosSQL.getInt("colaboradorsuplente"));
        obj.setDtInicio(dadosSQL.getDate("dtinicio"));
        obj.setDtFim(dadosSQL.getDate("dtfim"));
        obj.setMotivo(dadosSQL.getString("motivo"));
        obj.setNovoObj(false);
        montarDadosColaboradorSuplente(obj,Uteis.NIVELMONTARDADOS_DADOSBASICOS);
        montarDadosColaboradorIndisponivel(obj,Uteis.NIVELMONTARDADOS_DADOSBASICOS);
        return obj;
    }

    public void montarDadosColaboradorSuplente(ColaboradorIndisponivelCrmVO obj, int nivelMontarDados) throws Exception{
        if (obj.getColaboradorSuplenteVO().getCodigo().intValue() == 0){
            obj.setColaboradorSuplenteVO(new ColaboradorVO());
        }else {
         obj.setColaboradorSuplenteVO(getFacade().getColaborador().consultarPorChavePrimaria(obj.getColaboradorSuplenteVO().getCodigo(), nivelMontarDados));
        }
    }

    public void montarDadosColaboradorIndisponivel(ColaboradorIndisponivelCrmVO obj, int nivelMontarDados) throws Exception{
        if (obj.getColaboradorIndisponivelVO().getCodigo().intValue() == 0){
            obj.setColaboradorIndisponivelVO(new ColaboradorVO());
        }else {
           obj.setColaboradorIndisponivelVO(getFacade().getColaborador().consultarPorChavePrimaria(obj.getColaboradorIndisponivelVO().getCodigo(),nivelMontarDados));
        }
    }

}

package negocio.facade.jdbc.basico;

import negocio.comuns.basico.ClienteTokenVO;
import negocio.comuns.utilitarias.Calendario;
import negocio.comuns.utilitarias.ConsistirException;
import negocio.comuns.utilitarias.Uteis;
import negocio.facade.jdbc.arquitetura.SuperEntidade;
import negocio.interfaces.basico.ClienteTokenInterfaceFacade;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.util.Random;

public class ClienteToken extends SuperEntidade implements ClienteTokenInterfaceFacade {

    public ClienteToken() throws Exception {
    }

    public ClienteToken(Connection conexao) throws Exception {
        super(conexao);
    }

    public static ClienteTokenVO montarDados(ResultSet dadosSQL, int nivelMontarDados, Connection con) throws Exception {
        ClienteTokenVO obj = new ClienteTokenVO();
        obj.setCodigo(dadosSQL.getInt("codigo"));
        obj.getClienteVO().setCodigo(dadosSQL.getInt("cliente"));
        obj.setValidoAte(dadosSQL.getTimestamp("validoate"));
        obj.setUtilizadoEm(dadosSQL.getTimestamp("utilizadoem"));
        obj.setCodigoValidacao(dadosSQL.getString("codigovalidacao"));
        obj.setNovoObj(false);
        return obj;
    }

    private String getRandomPass() {
        char[] chart = {'0', '1', '2', '3', '4', '5', '6', '7', '8', '9'};
        char[] senha = new char[6];
        int chartLenght = chart.length;
        Random rdm = new Random();
        for (int x = 0; x < 6; x++) {
            senha[x] = chart[rdm.nextInt(chartLenght)];
        }
        return new String(senha);
    }


    @Override
    public ClienteTokenVO incluir(Integer codCliente) throws Exception {
        ClienteTokenVO obj = new ClienteTokenVO();
        obj.getClienteVO().setCodigo(codCliente);
        obj.setValidoAte(Uteis.somarDias(Calendario.hoje(), 1));
        obj.setCodigoValidacao(getRandomPass());

        String sql = "INSERT INTO clientetoken(cliente, validoate, codigovalidacao) VALUES (?, ?, ?);";
        PreparedStatement ps = getCon().prepareStatement(sql);
        int i = 0;
        ps.setInt(++i, obj.getClienteVO().getCodigo());
        ps.setDate(++i, Uteis.getDataJDBC(obj.getValidoAte()));
        ps.setString(++i, obj.getCodigoValidacao());
        ps.execute();

        obj.setCodigo(obterValorChavePrimariaCodigo());
        obj.setNovoObj(false);
        return obj;
    }

    @Override
    public String utilizar(ClienteTokenVO obj) throws Exception {
        if (obj == null) {
            throw new ConsistirException("Código Inválido");
        }

        if (obj.getUtilizadoEm() != null) {
            throw new ConsistirException("Token já utilizado");
        }

        if (Calendario.maiorOuIgualComHora(Calendario.hoje(), obj.getValidoAte())) {
            throw new ConsistirException("Token Expirado");
        }

        String sql = "UPDATE clientetoken SET utilizadoem = ? WHERE codigo = ?;";
        PreparedStatement ps = getCon().prepareStatement(sql);
        int i = 0;
        ps.setTimestamp(++i, Uteis.getDataJDBCTimestamp(Calendario.hoje()));
        ps.setInt(++i, obj.getCodigo());
        ps.execute();

        return "OK";
    }

    @Override
    public ClienteTokenVO verificar(Integer codCliente, String codigoValidacao) throws Exception {
        String sql = "SELECT * FROM clientetoken WHERE cliente = ? AND codigovalidacao = ?;";
        PreparedStatement ps = getCon().prepareStatement(sql);
        int i = 0;
        ps.setInt(++i, codCliente);
        ps.setString(++i, codigoValidacao);
        ResultSet rs = ps.executeQuery();
        ClienteTokenVO obj = null;
        if (rs.next()) {
            obj = montarDados(rs, Uteis.NIVELMONTARDADOS_DADOSBASICOS, getCon());
        }
        return obj;
    }
}

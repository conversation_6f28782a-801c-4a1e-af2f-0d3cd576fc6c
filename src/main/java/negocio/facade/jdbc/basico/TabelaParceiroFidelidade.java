package negocio.facade.jdbc.basico;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.Statement;
import java.util.ArrayList;
import java.util.List;
import negocio.comuns.basico.TabelaParceiroFidelidadeItemVO;
import negocio.comuns.basico.TabelaParceiroFidelidadeVO;
import negocio.comuns.utilitarias.ConsistirException;
import negocio.comuns.utilitarias.Uteis;
import negocio.facade.jdbc.arquitetura.SuperEntidade;
import negocio.facade.jdbc.arquitetura.SuperFacadeJDBC;
import negocio.interfaces.basico.ParceiroFidelidadeInterfaceFacade;
import negocio.interfaces.basico.TabelaParceiroFidelidadeInterfaceFacade;
import negocio.interfaces.basico.TabelaParceiroFidelidadeItemInterfaceFacade;

/**
 *
 * <AUTHOR>
 */
public class TabelaParceiroFidelidade extends SuperEntidade implements TabelaParceiroFidelidadeInterfaceFacade {

    public TabelaParceiroFidelidade() throws Exception {
        super();
        setIdEntidade("Empresa");
    }

    public TabelaParceiroFidelidade(Connection con) throws Exception {
        super(con);
        setIdEntidade("Empresa");
    }

    @Override
    public TabelaParceiroFidelidadeVO novo() throws Exception {
        incluir(getIdEntidade());
        return new TabelaParceiroFidelidadeVO();
    }

    @Override
    public void incluir(TabelaParceiroFidelidadeVO obj) throws Exception {
        incluir(getIdEntidade());
        obj.realizarUpperCaseDados();
        TabelaParceiroFidelidadeVO.validarDados(obj);
        String sql = "INSERT INTO TabelaParceiroFidelidade( nomeTabela, parceiroFidelidade, defaultrecorrencia ) VALUES ( ?,?,? )";
        int i = 1;
        PreparedStatement ps = con.prepareStatement(sql);
        ps.setString(i++, obj.getNomeTabela());
        ps = resolveFKNull(ps, i++, obj.getParceiro().getCodigo());
        ps.setBoolean(i++, obj.isDefaultRecorrencia());
        ps.execute();
        obj.setCodigo(obterValorChavePrimariaCodigo());
        obj.setNovoObj(false);
        persistirItens(obj);
    }
    
    private void persistirItens(TabelaParceiroFidelidadeVO obj) throws Exception {
        TabelaParceiroFidelidadeItemInterfaceFacade itemDao = new TabelaParceiroFidelidadeItem(con);
        if (!obj.getItens().isEmpty()) {
            for (TabelaParceiroFidelidadeItemVO item : obj.getItens()) {
                item.setTabelaParceiro(obj);
                obj.validarIntervalo(item);
                if (item.getCodigo() == 0) {
                    itemDao.incluir(item);
                } else {
                    itemDao.alterar(item);
                }
            }
        }
        itemDao = null;
    }

    @Override
    public void alterar(TabelaParceiroFidelidadeVO obj) throws Exception {
        try {
            alterar(getIdEntidade());
            obj.realizarUpperCaseDados();
            TabelaParceiroFidelidadeVO.validarDados(obj);
            validarUnicaTabelaDefaultRecorrencia(obj);
            String sql = "UPDATE TabelaParceiroFidelidade set nomeTabela=?, parceiroFidelidade=?, defaultrecorrencia=?  WHERE ((codigo = ?))";
            PreparedStatement ps = con.prepareStatement(sql);
            int i = 1;
            ps.setString(i++, obj.getNomeTabela());
            ps = resolveFKNull(ps, i++, obj.getParceiro().getCodigo());
            ps.setBoolean(i++, obj.isDefaultRecorrencia());
            ps.setInt(i++, obj.getCodigo());
            ps.execute();
            persistirItens(obj);
        } catch (Exception e) {
            throw e;
        }
    }

    @Override
    public void excluir(TabelaParceiroFidelidadeVO obj) throws Exception {
        try {
            excluir(getIdEntidade());
            String sql = "DELETE FROM TabelaParceiroFidelidade WHERE ((codigo = ?))";
            PreparedStatement ps = con.prepareStatement(sql);
            int i = 1;
            ps.setInt(i++, obj.getCodigo());
            ps.execute();
        } catch (Exception e) {
            throw e;
        }
    }

    @Override
    public List consultarPorNome(String valorConsulta, boolean controlarAcesso, int nivelMontarDados) throws Exception {
        consultar(getIdEntidade(), controlarAcesso);
        String sqlStr = "SELECT * FROM TabelaParceiroFidelidade WHERE upper( nome ) like('" + valorConsulta.toUpperCase() + "%') ORDER BY nome";
        Statement stm = con.createStatement();
        ResultSet tabelaResultado = stm.executeQuery(sqlStr);
        return (montarDadosConsulta(tabelaResultado, nivelMontarDados));
    }

    @Override
    public TabelaParceiroFidelidadeVO obterDefaultRecorrencia(final Integer empresa) throws Exception {
        String sqlStr = String.format("SELECT t.* FROM TabelaParceiroFidelidade t "
                + "inner join ParceiroFidelidade p on p.codigo = t.parceirofidelidade "
                + "WHERE p.empresa = %s and t.defaultrecorrencia", empresa);
        Statement stm = con.createStatement();
        ResultSet rs = stm.executeQuery(sqlStr);
        if (rs.next()) {
            return montarDados(rs, Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA);
        } else {
            return null;
        }
    }
    
    private void validarUnicaTabelaDefaultRecorrencia(TabelaParceiroFidelidadeVO tabela) throws Exception {
        if (tabela.isDefaultRecorrencia()) {
            String sqlStr = String.format("SELECT t.* FROM TabelaParceiroFidelidade t "
                    + "inner join ParceiroFidelidade p on p.codigo = t.parceirofidelidade "
                    + "WHERE p.empresa = %s and t.defaultrecorrencia and t.codigo <> %s",
                    tabela.getParceiro().getEmpresa().getCodigo(), tabela.getCodigo());
            if (SuperFacadeJDBC.existe(sqlStr, con)) {
                throw new ConsistirException(String.format("Já existe outra tabela como Recorrência nesta mesma empresa."));
            }
        }
    }

    @Override
    public List consultarPorCodigo(Integer valorConsulta, boolean controlarAcesso, int nivelMontarDados) throws Exception {
        consultar(getIdEntidade(), controlarAcesso);
        String sqlStr = "SELECT * FROM TabelaParceiroFidelidade WHERE codigo = " + valorConsulta + " ORDER BY codigo";
        Statement stm = con.createStatement();
        ResultSet tabelaResultado = stm.executeQuery(sqlStr);
        return (montarDadosConsulta(tabelaResultado, nivelMontarDados));
    }
    
    @Override
    public List consultarPorParceiro(final Integer codigoParceiro, boolean controlarAcesso, int nivelMontarDados) throws Exception {
        consultar(getIdEntidade(), controlarAcesso);
        String sqlStr = "SELECT * FROM TabelaParceiroFidelidade WHERE parceirofidelidade = " + 
                codigoParceiro + " ORDER BY nomeTabela";
        Statement stm = con.createStatement();
        ResultSet tabelaResultado = stm.executeQuery(sqlStr);
        return (montarDadosConsulta(tabelaResultado, nivelMontarDados));
    }

    public List montarDadosConsulta(ResultSet tabelaResultado, int nivelMontarDados) throws Exception {
        List vetResultado = new ArrayList();
        while (tabelaResultado.next()) {
            TabelaParceiroFidelidadeVO obj = montarDados(tabelaResultado, nivelMontarDados);
            vetResultado.add(obj);
        }
        return vetResultado;
    }

    public TabelaParceiroFidelidadeVO montarDados(ResultSet rs, int nivelMontarDados) throws Exception {
        TabelaParceiroFidelidadeVO obj = new TabelaParceiroFidelidadeVO();
        obj.setCodigo(rs.getInt("codigo"));
        obj.setNomeTabela(rs.getString("nomeTabela"));
        ParceiroFidelidadeInterfaceFacade parceiroDao = new ParceiroFidelidade(con);
        obj.setParceiro(parceiroDao.consultarPorChavePrimaria(
                rs.getInt("parceirofidelidade"), Uteis.NIVELMONTARDADOS_DADOSBASICOS));
        obj.setDefaultRecorrencia(rs.getBoolean("defaultrecorrencia"));
        obj.setNovoObj(false);
        parceiroDao = null;
        if (nivelMontarDados == Uteis.NIVELMONTARDADOS_DADOSBASICOS) {
            return obj;
        }        
        if (nivelMontarDados == Uteis.NIVELMONTARDADOS_TODOS) {
            TabelaParceiroFidelidadeItemInterfaceFacade itemDao = new TabelaParceiroFidelidadeItem(con);
            obj.setItens(itemDao.consultarPorTabelaParceiro(obj.getCodigo(), false, nivelMontarDados));
        }
        return obj;
    }

    @Override
    public TabelaParceiroFidelidadeVO consultarPorChavePrimaria(Integer codigoPrm, int nivelMontarDados) throws Exception {
        consultar(getIdEntidade(), false);
        String sql = "SELECT * FROM TabelaParceiroFidelidade WHERE codigo = ?";
        PreparedStatement ps = con.prepareStatement(sql);
        int i = 1;
        ps.setInt(i++, codigoPrm);
        ResultSet tabelaResultado = ps.executeQuery();
        if (!tabelaResultado.next()) {
            return new TabelaParceiroFidelidadeVO();
        }
        return (montarDados(tabelaResultado, nivelMontarDados));
    }
}
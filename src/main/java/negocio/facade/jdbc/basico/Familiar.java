package negocio.facade.jdbc.basico;

import br.com.pactosolucoes.oamd.controle.basico.DAO;
import controle.basico.BIFamiliaTO;
import controle.basico.TipoBIFamiliaEnum;
import negocio.comuns.basico.*;

import java.util.*;

import negocio.comuns.basico.enumerador.CausaSugestaoEnum;
import negocio.facade.jdbc.arquitetura.*;

import java.sql.Connection;
import java.sql.ResultSet;
import java.sql.PreparedStatement;
import java.sql.Statement;

import negocio.comuns.utilitarias.*;
import negocio.facade.jdbc.utilitarias.Conexao;
import relatorio.negocio.comuns.sad.SituacaoClienteSinteticoEnum;

/**
 * Classe de persistência que encapsula todas as operações de manipulação dos
 * dados da classe
 * <code>FamiliarVO</code>. Responsável por implementar operações como incluir,
 * alterar, excluir e consultar pertinentes a classe
 * <code>FamiliarVO</code>. Encapsula toda a interação com o banco de dados.
 *
 * @see FamiliarVO
 * @see SuperEntidade
 * @see Cliente
 */
public class Familiar extends SuperEntidade {

    public Familiar() throws Exception {
        super();
    }

    public Familiar(Connection con) throws Exception {
        super(con);
    }

    /**
     * Operação responsável por retornar um novo objeto da classe
     * <code>FamiliarVO</code>.
     */
    public FamiliarVO novo() throws Exception {
        incluir(getIdEntidade());
        return new FamiliarVO();
    }

    /**
     * Operação responsável por incluir no banco de dados um objeto da classe
     * <code>FamiliarVO</code>. Primeiramente valida os dados (
     * <code>validarDados</code>) do objeto. Verifica a conexão com o banco de
     * dados e a permissão do usuário para realizar esta operacão na entidade.
     * Isto, através da operação
     * <code>incluir</code> da superclasse.
     *
     * @param obj Objeto da classe <code>FamiliarVO</code> que será gravado no
     * banco de dados.
     * @exception Exception Caso haja problemas de conexão, restrição de acesso
     * ou validação de dados.
     */
    public void incluir(FamiliarVO obj, boolean gerarAssociado) throws Exception {
        FamiliarVO.validarDados(obj);
        incluir(getIdEntidade());
        obj.realizarUpperCaseDados();
        String sql = "INSERT INTO familiar(cliente, familiar, parentesco, codAcesso, identificador,\n" +
                "nome, compartilharPlano, dependenteInicial) VALUES ( ?, ?, ?, ?, ?, ?, ?, ?)";
        try (PreparedStatement sqlInserir = con.prepareStatement(sql)) {
            int i = 0;
            if (!UteisValidacao.emptyNumber(obj.getCliente())) {
                sqlInserir.setInt(++i, obj.getCliente());
            } else {
                sqlInserir.setNull(++i, 0);
            }
            sqlInserir.setInt(++i, obj.getFamiliar());
            if (!UteisValidacao.emptyNumber(obj.getParentesco().getCodigo())) {
                sqlInserir.setInt(++i, obj.getParentesco().getCodigo());
            } else {
                sqlInserir.setNull(++i, 0);
            }
            sqlInserir.setString(++i, obj.getCodAcesso());
            sqlInserir.setString(++i, obj.getIdentificador());
            sqlInserir.setString(++i, obj.getNome());
            sqlInserir.setBoolean(++i, obj.isCompartilharPlano());
            if (!UteisValidacao.emptyNumber(obj.getDependenteInicial())) {
                sqlInserir.setInt(++i, obj.getDependenteInicial());
            } else {
                sqlInserir.setNull(++i, 0);
            }
            sqlInserir.execute();
        }
        obj.setCodigo(obterValorChavePrimariaCodigo());
        obj.setNovoObj(false);
        if (gerarAssociado) {
            gerarFamiliarAssociado(obj.getCliente(), obj.getFamiliar(), obj.getParentesco());
        }
        if (obj.isCompartilharPlano()) {
            incluirDependentePlanoCompartilhado(obj);
        }
    }

    private void validarRemoverDependentePlanoCompartilhado(FamiliarVO obj) throws Exception {
        ContratoDependente dependentePlanoCompartilhadoDAO = new ContratoDependente(con);
        // Aqui de toda forma é necessário retirar a dependencia do familiar apagando o TitularPlanoCompartilhado, evitando do familiar continuar com a situação de DP
        Cliente clienteDao = new Cliente(con);
        clienteDao.preencherTitularPlanoCompartilhado(null, obj.getFamiliar());
        // Se existir ainda um contrato compartilhado vigente, ele encerra o contrato
        boolean existeDependencia = dependentePlanoCompartilhadoDAO.existeDependenciaVigente(obj.getFamiliar());
        if (existeDependencia) {
            clienteDao.finalizarDependenciaPlanoCompartilhado(obj.getFamiliar(), false);
            ClienteVO clienteDependente = clienteDao.consultarPorCodigo(obj.getFamiliar(),false,Uteis.NIVELMONTARDADOS_DADOSBASICOS);
            clienteDao = null;
            ZillyonWebFacade zillyonWebFacadeDAO = new ZillyonWebFacade(con);
            zillyonWebFacadeDAO.atualizarSintetico(clienteDependente, Calendario.hoje(),
                    SituacaoClienteSinteticoEnum.GRUPO_TODOS, false);
            zillyonWebFacadeDAO= null;
        }
    }

    private void incluirDependentePlanoCompartilhado(FamiliarVO obj) throws Exception {
        Cliente clienteDao = new Cliente(con);
        clienteDao.preencherTitularPlanoCompartilhado(obj.getCliente(), obj.getFamiliar());
        ClienteVO clienteDependente = clienteDao.consultarPorCodigo(obj.getFamiliar(),false,Uteis.NIVELMONTARDADOS_DADOSBASICOS);
        clienteDao = null;

        ContratoDependente contratoDependenteDAO = new ContratoDependente(con);
        boolean existeDependencia = contratoDependenteDAO.existeDependenciaVigente(obj.getFamiliar());
        if (existeDependencia && obj.getContratoDependente() != null && obj.getContratoDependente().getDataInicio() != null
                && obj.getContratoDependente().getDataFinal() != null) {
            existeDependencia = contratoDependenteDAO.existeDependenciaVigenteNoPeriodo(obj.getFamiliar(), obj.getContratoDependente().getDataInicio(), obj.getContratoDependente().getDataFinal());
        }
        if (!existeDependencia) {
            contratoDependenteDAO.alterar(obj.getContratoDependente());
        }

        ZillyonWebFacade zillyonWebFacadeDAO = new ZillyonWebFacade(con);
        zillyonWebFacadeDAO.atualizarSintetico(clienteDependente, Calendario.hoje(),
                SituacaoClienteSinteticoEnum.GRUPO_TODOS, false);
        zillyonWebFacadeDAO.sincronizarClienteDependenteRedeEmpresa(obj.getContratoDependente());

        zillyonWebFacadeDAO= null;
        contratoDependenteDAO = null;
    }

    private void gerarFamiliarAssociado(Integer cliente, Integer familiar, ParentescoVO parentesco) throws Exception {
        Cliente clienteDao = new Cliente(con);
        ClienteVO novoFamiliar = clienteDao.consultarPorCodigo(cliente, false, Uteis.NIVELMONTARDADOS_MINIMOS);

        ParentescoVO parentescoNovo = null;
        String identificador = null;
        //é pai ou mãe??
        if (parentesco.getDescricao().equalsIgnoreCase("MÃE")
                || parentesco.getDescricao().equalsIgnoreCase("MAE")
                || parentesco.getDescricao().equalsIgnoreCase("PAI")) {
            //se for mulher, é filha
            if (novoFamiliar.getPessoa().getSexo() != null
                    && !novoFamiliar.getPessoa().getSexo().isEmpty()
                    && novoFamiliar.getPessoa().getSexo().equals("F")) {
                parentescoNovo = obterParentescoAssociado("FILHA", null);
                identificador = "FILHA";
            }
            //se for homem ou não tiver sexo, é filho
            if (parentescoNovo == null) {
                parentescoNovo = obterParentescoAssociado("FILHO", null);
                identificador = "FILHO";
            }
            //é filho ou filha??    
        } else if (parentesco.getDescricao().equalsIgnoreCase("FILHO")
                || parentesco.getDescricao().equalsIgnoreCase("FILHA")) {
            //se o sexo não for informado, não grava nada
            if (novoFamiliar.getPessoa().getSexo() == null
                    || novoFamiliar.getPessoa().getSexo().isEmpty()) {
                return;
            }
            //se é homem é pai
            if (novoFamiliar.getPessoa().getSexo().equals("M")) {
                parentescoNovo = obterParentescoAssociado("PAI", null);
                identificador = "PAI";
            }//MULHER É MÃE 
            else {
                parentescoNovo = obterParentescoAssociado("MÃE", "MAE");
                identificador = "MÃE";
            }
            //se é irmão ou irmã
        } else if (parentesco.getDescricao().equalsIgnoreCase("IRMA")
                || parentesco.getDescricao().equalsIgnoreCase("IRMÃO")
                || parentesco.getDescricao().equalsIgnoreCase("IRMAO")
                || parentesco.getDescricao().equalsIgnoreCase("IRMÃ")) {
            //se for mulher, é irmã
            if (novoFamiliar.getPessoa().getSexo() != null
                    && !novoFamiliar.getPessoa().getSexo().isEmpty()
                    && novoFamiliar.getPessoa().getSexo().equals("F")) {
                parentescoNovo = obterParentescoAssociado("IRMÃ", "IRMA");
                identificador = "IRMÃ";
            }
            //se for homem ou não tiver sexo, é filho
            if (parentescoNovo == null) {
                parentescoNovo = obterParentescoAssociado("IRMÃO", "IRMAO");
                identificador = "IRMÃO";
            }
        } else if (parentesco.getDescricao().equalsIgnoreCase("TITULAR")){
            parentescoNovo = obterParentescoAssociado("DEPENDENTE", "DEPENDENTE");
        } else if (parentesco.getDescricao().equalsIgnoreCase("DEPENDENTE")) {
            parentescoNovo = obterParentescoAssociado("TITULAR", "TITULAR");
        } else {
            parentescoNovo = parentesco;
            identificador = "--";
        }

        if (parentescoNovo == null) {
            return;
        }
        FamiliarVO familiarNovo = new FamiliarVO();
        familiarNovo.setCliente(familiar);
        familiarNovo.setCodAcesso(novoFamiliar.getCodAcesso());
        familiarNovo.setFamiliar(novoFamiliar.getCodigo());
        familiarNovo.setNome(novoFamiliar.getPessoa().getNome());
        familiarNovo.setIdentificador(identificador);
        familiarNovo.setParentesco(parentescoNovo);
        incluir(familiarNovo, false);
    }

    private ParentescoVO obterParentescoAssociado(String parentesco, String opcao) throws Exception {
        Parentesco parentescoDao = new Parentesco(con);
        List<ParentescoVO> parentescos = parentescoDao.consultarPorDescricao(parentesco, true, Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA);
        if (!UteisValidacao.emptyList(parentescos)) {
            return parentescos.get(0);
        }
        if(opcao != null){
            return obterParentescoAssociado(opcao, null);
        }
        return null;
    }

    /**
     * Operação responsável por alterar no BD os dados de um objeto da classe
     * <code>FamiliarVO</code>. Sempre utiliza a chave primária da classe como
     * atributo para localização do registro a ser alterado. Primeiramente
     * valida os dados (
     * <code>validarDados</code>) do objeto. Verifica a conexão com o banco de
     * dados e a permissão do usuário para realizar esta operacão na entidade.
     * Isto, através da operação
     * <code>alterar</code> da superclasse.
     *
     * @param obj Objeto da classe <code>FamiliarVO</code> que será alterada no
     * banco de dados.
     * @exception Exception Caso haja problemas de conexão, restrição de acesso
     * ou validação de dados.
     */
    public void alterar(FamiliarVO obj) throws Exception {
        FamiliarVO.validarDados(obj);
        alterar(getIdEntidade());
        obj.realizarUpperCaseDados();
        String sql = "UPDATE Familiar set cliente = ?, familiar = ?, parentesco = ?, codAcesso = ?, \n" +
                "identificador = ?, nome = ?, compartilharPlano = ?, dependenteInicial = ?\n" +
                "WHERE codigo = ?";

        try (PreparedStatement sqlAlterar = con.prepareStatement(sql)) {
            int i = 0;
            if (obj.getCliente() != 0) {
                sqlAlterar.setInt(++i, obj.getCliente());
            } else {
                sqlAlterar.setNull(++i, 0);
            }
            sqlAlterar.setInt(++i, obj.getFamiliar());
            if (obj.getParentesco().getCodigo() != 0) {
                sqlAlterar.setInt(++i, obj.getParentesco().getCodigo());
            } else {
                sqlAlterar.setNull(++i, 0);
            }
            sqlAlterar.setString(++i, obj.getCodAcesso());
            sqlAlterar.setString(++i, obj.getIdentificador());
            sqlAlterar.setString(++i, obj.getNome());
            sqlAlterar.setBoolean(++i, obj.isCompartilharPlano());
            sqlAlterar.setInt(++i, obj.getDependenteInicial());
            sqlAlterar.setInt(++i, obj.getCodigo());
            sqlAlterar.execute();
        }

        if (obj.isCompartilharPlano()) {
            incluirDependentePlanoCompartilhado(obj);
        } else {
            validarRemoverDependentePlanoCompartilhado(obj);
        }
    }

    /**
     * Operação responsável por excluir no BD um objeto da classe
     * <code>FamiliarVO</code>. Sempre localiza o registro a ser excluído
     * através da chave primária da entidade. Primeiramente verifica a conexão
     * com o banco de dados e a permissão do usuário para realizar esta operacão
     * na entidade. Isto, através da operação
     * <code>excluir</code> da superclasse.
     *
     * @param obj Objeto da classe <code>FamiliarVO</code> que será removido no
     * banco de dados.
     * @exception Exception Caso haja problemas de conexão ou restrição de
     * acesso.
     */
    public void excluir(FamiliarVO obj) throws Exception {
        excluir(getIdEntidade());
        String sql = "DELETE FROM Familiar WHERE codigo = ?";
        try (PreparedStatement sqlExcluir = con.prepareStatement(sql)) {
            sqlExcluir.setInt(1, obj.getCodigo());
            sqlExcluir.execute();
        }
    }

    /**
     * Responsável por realizar uma consulta de
     * <code>Familiar</code> através do valor do atributo
     * <code>String identificador</code>. Retorna os objetos, com início do
     * valor do atributo idêntico ao parâmetro fornecido. Faz uso da operação
     * <code>montarDadosConsulta</code> que realiza o trabalho de prerarar o
     * List resultante.
     *
     * @param controlarAcesso Indica se a aplicação deverá verificar se o
     * usuário possui permissão para esta consulta ou não.
     * @return List Contendo vários objetos da classe <code>FamiliarVO</code>
     * resultantes da consulta.
     * @exception Exception Caso haja problemas de conexão ou restrição de
     * acesso.
     */
    public List<FamiliarVO> consultarPorIdentificador(String valorConsulta, boolean controlarAcesso, int nivelMontarDados) throws Exception {
        consultar(getIdEntidade(), controlarAcesso);
        String sqlStr = "SELECT * FROM Familiar WHERE upper( identificador ) like('" + valorConsulta.toUpperCase() + "%') ORDER BY identificador";
        try (Statement stm = con.createStatement()) {
            try (ResultSet tabelaResultado = stm.executeQuery(sqlStr)) {
                return (montarDadosConsulta(tabelaResultado, nivelMontarDados, this.con));
            }
        }
    }

    /**
     * Responsável por realizar uma consulta de
     * <code>Familiar</code> através do valor do atributo
     * <code>matricula</code> da classe
     * <code>Cliente</code> Faz uso da operação
     * <code>montarDadosConsulta</code> que realiza o trabalho de prerarar o
     * List resultante.
     *
     * @return List Contendo vários objetos da classe <code>FamiliarVO</code>
     * resultantes da consulta.
     * @exception Exception Caso haja problemas de conexão ou restrição de
     * acesso.
     */
    public List<FamiliarVO> consultarPorMatriculaCliente(String valorConsulta, int nivelMontarDados) throws Exception {
        consultar(getIdEntidade(), true);
        String sqlStr = "SELECT Familiar.* FROM Familiar, Cliente WHERE Familiar.cliente = Cliente.codigo and upper( Cliente.matricula ) like('" + valorConsulta.toUpperCase() + "%') ORDER BY Cliente.matricula";
        try (Statement stm = con.createStatement()) {
            try (ResultSet tabelaResultado = stm.executeQuery(sqlStr)) {
                return montarDadosConsulta(tabelaResultado, nivelMontarDados, this.con);
            }
        }
    }

    /**
     * Responsável por realizar uma consulta de
     * <code>Familiar</code> através do valor do atributo
     * <code>Integer codigo</code>. Retorna os objetos com valores iguais ou
     * superiores ao parâmetro fornecido. Faz uso da operação
     * <code>montarDadosConsulta</code> que realiza o trabalho de prerarar o
     * List resultante.
     *
     * @param controlarAcesso Indica se a aplicação deverá verificar se o
     * usuário possui permissão para esta consulta ou não.
     * @return List Contendo vários objetos da classe <code>FamiliarVO</code>
     * resultantes da consulta.
     * @exception Exception Caso haja problemas de conexão ou restrição de
     * acesso.
     */
    public List<FamiliarVO> consultarPorCodigo(Integer valorConsulta, boolean controlarAcesso, int nivelMontarDados) throws Exception {
        consultar(getIdEntidade(), controlarAcesso);
        String sqlStr = "SELECT * FROM Familiar WHERE codigo >= " + valorConsulta.intValue() + " ORDER BY codigo";
        try (Statement stm = con.createStatement()) {
            try (ResultSet tabelaResultado = stm.executeQuery(sqlStr)) {
                return (montarDadosConsulta(tabelaResultado, nivelMontarDados, this.con));
            }
        }
    }

    /**
     * Responsável por montar os dados de vários objetos, resultantes de uma
     * consulta ao banco de dados (
     * <code>ResultSet</code>). Faz uso da operação
     * <code>montarDados</code> que realiza o trabalho para um objeto por vez.
     *
     * @return List Contendo vários objetos da classe <code>FamiliarVO</code>
     * resultantes da consulta.
     */
    public static List<FamiliarVO> montarDadosConsulta(ResultSet tabelaResultado, int nivelMontarDados, Connection con) throws Exception {
        List<FamiliarVO> vetResultado = new ArrayList<>();
        while (tabelaResultado.next()) {
            FamiliarVO obj = montarDados(tabelaResultado, nivelMontarDados, con);
            vetResultado.add(obj);
        }
        return vetResultado;
    }

    /**
     * Responsável por montar os dados resultantes de uma consulta ao banco de
     * dados (
     * <code>ResultSet</code>) em um objeto da classe
     * <code>FamiliarVO</code>.
     *
     * @return O objeto da classe <code>FamiliarVO</code> com os dados
     * devidamente montados.
     */
    public static FamiliarVO montarDados(ResultSet dadosSQL, int nivelMontarDados, Connection con) throws Exception {
        FamiliarVO obj = new FamiliarVO();
        obj.setCodigo(dadosSQL.getInt("codigo"));
        obj.setCliente(dadosSQL.getInt("cliente"));
        obj.setFamiliar(dadosSQL.getInt("familiar"));
        obj.getParentesco().setCodigo(dadosSQL.getInt("parentesco"));
        obj.setCodAcesso(dadosSQL.getString("codAcesso"));
        obj.setNome(dadosSQL.getString("nome"));
        obj.setIdentificador(dadosSQL.getString("identificador"));
        obj.setCompartilharPlano(dadosSQL.getBoolean("compartilharPlano"));
        obj.setDependenteInicial(dadosSQL.getInt("dependenteInicial"));
        obj.setNovoObj(false);

        montarDadosParentesco(obj, Uteis.NIVELMONTARDADOS_DADOSBASICOS, con);
        return obj;
    }

    /**
     * Operação responsável por montar os dados de um objeto da classe
     * <code>ParentescoVO</code> relacionado ao objeto
     * <code>FamiliarVO</code>. Faz uso da chave primária da classe
     * <code>ParentescoVO</code> para realizar a consulta.
     *
     * @param obj Objeto no qual será montado os dados consultados.
     */
    public static void montarDadosParentesco(FamiliarVO obj, int nivelMontarDados, Connection con) throws Exception {
        if (obj.getParentesco().getCodigo() == 0) {
            obj.setParentesco(new ParentescoVO());
            return;
        }
        Parentesco parentesco = new Parentesco(con);
        obj.setParentesco(parentesco.consultarPorChavePrimaria(obj.getParentesco().getCodigo(), nivelMontarDados));
        parentesco = null;
    }

    /**
     * Operação responsável por alterar todos os objetos da
     * <code>FamiliarVO</code> contidos em um Hashtable no BD. Faz uso da
     * operação
     * <code>excluirFamiliars</code> e
     * <code>incluirFamiliars</code> disponíveis na classe
     * <code>Familiar</code>.
     *
     * @param familiares List com os objetos a serem alterados ou incluídos no BD.
     * @exception Exception Erro de conexão com o BD ou restrição de acesso a
     * esta operação.
     */
    public void alterarFamiliars(Integer cliente, List<FamiliarVO> familiares, List<FamiliarVO> familiaresExcluidos) throws Exception {
        StringBuilder str = new StringBuilder("DELETE FROM Familiar WHERE cliente = " + cliente);
        for (FamiliarVO objeto : familiares) {
            str.append(" AND codigo <> ").append(objeto.getCodigo());
        }

        try (PreparedStatement sqlExcluir = con.prepareStatement(str.toString())) {
            sqlExcluir.execute();
        }

        Cliente clienteDAO = new Cliente(con);
        for (FamiliarVO familiarExcluido : familiaresExcluidos) {
            clienteDAO.finalizarDependenciaPlanoCompartilhado(familiarExcluido.getFamiliar(), true);
        }
        clienteDAO = null;

        Iterator<FamiliarVO> e = familiares.iterator();
        while (e.hasNext()) {
            FamiliarVO obj = e.next();
            if (obj.getCodigo().equals(0)) {
                obj.setCliente(cliente);
                incluir(obj, true);
            } else {
                alterar(obj);
            }
        }
    }

    /**
     * Operação responsável por incluir objetos da
     * <code>FamiliarVO</code> no BD. Garantindo o relacionamento com a entidade
     * principal
     * <code>basico.Cliente</code> através do atributo de vínculo.
     *
     * @param objetos List contendo os objetos a serem gravados no BD da classe.
     * @exception Exception Erro de conexão com o BD ou restrição de acesso a
     * esta operação.
     */
    public void incluirFamiliars(Integer clientePrm, List objetos) throws Exception {
        Iterator e = objetos.iterator();
        while (e.hasNext()) {
            FamiliarVO obj = (FamiliarVO) e.next();
            obj.setCliente(clientePrm);
            incluir(obj, true);
        }
    }

    /**
     * Operação responsável por consultar todos os
     * <code>FamiliarVO</code> relacionados a um objeto da classe
     * <code>basico.Cliente</code>.
     *
     * @param cliente Atributo de <code>basico.Cliente</code> a ser utilizado
     * para localizar os objetos da classe <code>FamiliarVO</code>.
     * @return List Contendo todos os objetos da classe <code>FamiliarVO</code>
     * resultantes da consulta.
     * @exception Exception Erro de conexão com o BD ou restrição de acesso a
     * esta operação.
     */
    public List consultarFamiliars(Integer cliente, boolean situacao, int nivelMontarDados) throws Exception {
        consultar(getIdEntidade());
        List objetos = new ArrayList();
        String sql = situacao ? "SELECT familiar.situacao,familiar.matricula, f.* FROM Familiar f " +
                        "INNER JOIN cliente c ON c.codigo = f.cliente " +
                        "INNER JOIN cliente familiar ON f.familiar = familiar.codigo " +
                        "WHERE cliente = ?"
                : "SELECT * FROM Familiar WHERE cliente = ?";
        try (PreparedStatement sqlConsulta = con.prepareStatement(sql)) {
            sqlConsulta.setInt(1, cliente);
            try (ResultSet resultado = sqlConsulta.executeQuery()) {
                while (resultado.next()) {
                    FamiliarVO novoObj = Familiar.montarDados(resultado, nivelMontarDados, this.con);
                    if (situacao) {
                        novoObj.setSituacao(resultado.getString("situacao"));
                        novoObj.setMatricula(resultado.getString("matricula"));
                    }
                    objetos.add(novoObj);
                }
            }
        }
        return objetos;
    }

    /**
     * Operação responsável por localizar um objeto da classe
     * <code>FamiliarVO</code> através de sua chave primária.
     *
     * @exception Exception Caso haja problemas de conexão ou localização do
     * objeto procurado.
     */
    public FamiliarVO consultarPorChavePrimaria(Integer codigoPrm, int nivelMontarDados) throws Exception {
        consultar(getIdEntidade(), false);
        String sql = "SELECT * FROM Familiar WHERE codigo = ?";
        try (PreparedStatement sqlConsultar = con.prepareStatement(sql)) {
            sqlConsultar.setInt(1, codigoPrm);
            try (ResultSet tabelaResultado = sqlConsultar.executeQuery()) {
                if (!tabelaResultado.next()) {
                    throw new ConsistirException("Dados Não Encontrados ( Familiar ).");
                }
                return (montarDados(tabelaResultado, nivelMontarDados, this.con));
            }
        }
    }

    public List<FamiliarTO>  consultarFamiliaresMatricula(String matricula, boolean addproprio) throws Exception {
        List<FamiliarTO> lista = new ArrayList<>();
        StringBuilder sql = new StringBuilder();
        sql.append(" select pes.codigo as codpessoa, f.codigo as cf, p.descricao as parentesco, sw.codigocliente, sw.nomecliente, pes.fotokey, sw.datavigenciaateajustada, sw.situacao, sw.situacaocontrato, sw.matricula from familiar  f ");
        sql.append(" inner join cliente cli on cli.codigo = f.cliente ");
        sql.append(" inner join parentesco p on p.codigo = f.parentesco ");
        sql.append(" inner join situacaoclientesinteticodw sw on sw.codigocliente = f.familiar ");
        sql.append(" inner join pessoa pes on sw.codigopessoa = pes.codigo ");
        sql.append(" where cli.codigomatricula = ").append(matricula);
        try (ResultSet rs = criarConsulta(sql.toString(), con)) {
            while (rs.next()) {
                lista.add(montarFamiliar(rs));
            }
        }
        if(!addproprio){
            lista = Ordenacao.ordenarLista(lista, "nome");
            return lista;
        }
        sql = new StringBuilder();
        sql.append(" select sw.codigocliente, pes.codigo as codpessoa, ");
        sql.append(" sw.nomecliente, pes.fotokey, sw.datavigenciaateajustada, sw.situacao, sw.situacaocontrato, sw.matricula ");
        sql.append(" from situacaoclientesinteticodw sw ");
        sql.append(" inner join pessoa pes on sw.codigopessoa = pes.codigo ");
        sql.append(" where sw.matricula = ").append(matricula);
        try (ResultSet rsmat = criarConsulta(sql.toString(), con)) {
            if (rsmat.next()) {
                lista.add(montarFamiliar(rsmat));
            }
        }

        lista = Ordenacao.ordenarLista(lista, "nome");
        return lista;
    }

    private FamiliarTO montarFamiliar(ResultSet rs) {
        FamiliarTO familiar = new FamiliarTO();
        try {
            familiar.setCodigo(rs.getInt("cf"));
            familiar.setParentesco(rs.getString("parentesco").toLowerCase());
        }catch (Exception e){

        }
        try {
            familiar.setCodigoPessoa(rs.getInt("codpessoa"));
            familiar.setFotoKey(rs.getString("fotokey"));
            familiar.setVencimento(rs.getDate("datavigenciaateajustada"));
            familiar.setNome(rs.getString("nomecliente"));
            familiar.setSituacao(rs.getString("situacao"));
            familiar.setSituacaoContrato(rs.getString("situacaocontrato"));
            familiar.setCodigoMatricula(rs.getInt("matricula"));
            familiar.setCodigoCliente(rs.getInt("codigocliente"));
        }catch (Exception e){
            e.printStackTrace();
        }
        return familiar;
    }

    public void gravarEdicaoFamilia(List<FamiliarTO> familiares, ClienteVO clienteVO, Integer empresa) throws Exception{
        String codigos = "";
        for(FamiliarTO f : familiares){
            if(!UteisValidacao.emptyNumber(f.getCodigo())){
                codigos += "," + f.getCodigo();
            }

        }

        executarConsulta("delete from familiar where cliente = " + clienteVO.getCodigo()
            + (UteisValidacao.emptyString(codigos) ? "" : (" and codigo not in (" + codigos.replaceFirst("\\,", "") + ")")), con);

        ParentescoVO parentescoVO = getFacade().getParentesco().obterParentescoCriandoSeNaoExiste("FAMILIAR");
        for(FamiliarTO f : familiares){
            if(UteisValidacao.emptyNumber(f.getCodigo())){
                FamiliarVO familiarVO = new FamiliarVO();
                familiarVO.setParentesco(parentescoVO);
                familiarVO.setCliente(clienteVO.getCodigo());
                familiarVO.setIdentificador("FAMIL");
                familiarVO.setFamiliar(f.getCodigoCliente());
                familiarVO.setCodAcesso(clienteVO.getCodAcesso());
                familiarVO.setNome(f.getNome());
                incluir(familiarVO, true);
            }
        }

        gerarSintetico(empresa);

    }

    public FamiliaTO detalheFamiliaSugerida(Integer codigosugestao) throws Exception{
        try (ResultSet resultSet = criarConsulta("select codigos from sugestaofamilia where codigo = " + codigosugestao, con)) {
            if (resultSet.next()) {
                return montarFamilia(resultSet.getString("codigos"), codigosugestao);
            }
        }
        return new FamiliaTO();
    }

    public FamiliaTO montarFamilia(String codigos, Integer codigosugestao) throws Exception{
        StringBuilder sql = new StringBuilder();
        sql.append(" select distinct cli.codigomatricula, cli.codigo, p.nome, p.fotokey, cli.situacao  from cliente cli");
        sql.append(" inner join pessoa p on p.codigo = cli.pessoa ");
        sql.append(" where cli.codigo in (").append(codigos.replaceFirst("\\-", "").replace("-", ",")).append(") ");

        FamiliaTO familia;
        try (ResultSet rs = criarConsulta(sql.toString(), con)) {
            familia = new FamiliaTO();
            familia.setCodigoSugestao(codigosugestao);
            familia.setIntegrantes(new ArrayList<>());
            while (rs.next()) {
                FamiliarTO f = new FamiliarTO();
                f.setNome(rs.getString("nome").toLowerCase());
                f.setFotoKey(rs.getString("fotokey"));
                f.setCodigoMatricula(rs.getInt("codigomatricula"));
                f.setCodigoCliente(rs.getInt("codigo"));
                f.setSituacao(rs.getString("situacao"));
                familia.getIntegrantes().add(f);
            }
        }

        return familia;

    }

    public void gravarSugestao(SugestaoFamiliaVO sugestao) throws Exception {
        String sql = "INSERT INTO sugestaofamilia(causa, codigos, situacoes) VALUES (?, ?, ?) ";
        try (PreparedStatement stm = con.prepareStatement(sql)) {
            stm.setInt(1, sugestao.getCausa().getId());
            stm.setString(2, sugestao.getCodigos());
            stm.setString(3, sugestao.getSituacoes());
            stm.execute();
        }
    }

    public FamiliarTO montarFamiliar(Integer codigo) throws Exception {
        StringBuilder sql = new StringBuilder();
        sql.append(" select sw.nomecliente, pes.fotokey, sw.codigocliente, sw.datavigenciaateajustada, sw.situacao, sw.situacaocontrato, sw.matricula from situacaoclientesinteticodw sw ");
        sql.append(" inner join pessoa pes on sw.codigopessoa = pes.codigo ");
        sql.append(" where sw.codigocliente = ").append(codigo);
        try (ResultSet rs = criarConsulta(sql.toString(), con)) {
            if (rs.next()) {
                FamiliarTO familiar = new FamiliarTO();
                try {
                    familiar.setFotoKey(rs.getString("fotokey"));
                    familiar.setVencimento(rs.getDate("datavigenciaateajustada"));
                    familiar.setNome(rs.getString("nomecliente"));
                    familiar.setSituacao(rs.getString("situacao"));
                    familiar.setSituacaoContrato(rs.getString("situacaocontrato"));
                    familiar.setCodigoMatricula(rs.getInt("matricula"));
                    familiar.setCodigoCliente(rs.getInt("codigocliente"));
                    return familiar;
                } catch (Exception e) {
                    e.printStackTrace();
                }
            }
        }
        return null;
    }

    public Map<Integer, CausaSugestaoEnum> sugestoes() throws Exception{
        Map<Integer, CausaSugestaoEnum> sugestoes;
        try (ResultSet rs = criarConsulta("select codigo, causa from sugestaofamilia where not negado and not aceito", con)) {
            sugestoes = new HashMap<>();
            while (rs.next()) {
                sugestoes.put(rs.getInt("codigo"), CausaSugestaoEnum.getFromId(rs.getInt("causa")));
            }
        }
        return sugestoes;
    }

    public static void main(String ... args) throws Exception{
        Connection con = new DAO().obterConexaoEspecifica("acqua");
        Conexao.guardarConexaoForJ2SE("acqua", con);
        Familiar dao = new Familiar(con);
        dao.gerarSintetico(1);
    }

    public void gerarSintetico(Integer empresa) throws Exception{
        executarConsulta("delete from familiasintetico", con);

        Map<Integer, Set<Integer>> familiaMap = new HashMap<>();
        Map<Integer,Integer> mapaOndeEsta = new HashMap<>();
        String sql = "select distinct f.cliente from familiar f inner join cliente cli on cli.codigo = f.cliente " +
                (UteisValidacao.emptyNumber(empresa) ? "" : ("where cli.empresa = " + empresa));
        try (ResultSet rs = criarConsulta(sql, con)) {

            while (rs.next()) {
                Integer cliente = rs.getInt("cliente");
                Integer chave = mapaOndeEsta.get(cliente);
                Set<Integer> familia;
                if (chave == null) {
                    familia = new HashSet<>();
                    familia.add(cliente);
                    chave = cliente;
                } else {
                    familia = familiaMap.get(chave);
                }
                try (ResultSet clienters = criarConsulta("select familiar from familiar where cliente = " + cliente, con)) {
                    while (clienters.next()) {
                        mapaOndeEsta.put(clienters.getInt("familiar"), chave);
                        familia.add(clienters.getInt("familiar"));
                    }
                }
                familiaMap.put(chave, familia);
            }
        }

        for(Integer k : familiaMap.keySet()){
            String codigos = "";
            List<Integer> sortedList = new ArrayList<>(familiaMap.get(k));
            Collections.sort(sortedList);
            for(Integer c : sortedList){
                codigos += "," + c;
            }

            FamiliaSinteticoVO sintetico = new FamiliaSinteticoVO();
            sintetico.setEmpresa(empresa);
            codigos = codigos.replaceFirst(",", "");
            sintetico.setCodigos(codigos);
            try (ResultSet rssit = criarConsulta("select distinct situacao from cliente where codigo in (" + codigos + ")", con)) {
                sintetico.setSituacoes("");
                while (rssit.next()) {
                    sintetico.setSituacoes(sintetico.getSituacoes().concat(",").concat(rssit.getString("situacao")));
                }
            }
            sintetico.setSituacoes(sintetico.getSituacoes().replaceFirst(",",""));
            try (ResultSet rsvalor = criarConsulta("select sum(valorfinal) as valor from contrato c inner join cliente cli on cli.pessoa = c.pessoa where c.situacao = 'AT' and cli.codigo in ("
                    + codigos + ") ", con)) {

                if (rsvalor.next()) {
                    sintetico.setValorContratos(rsvalor.getDouble("valor"));
                }
            }

            try (ResultSet rsacessos = criarConsulta("select count(codigo) as total from acessocliente where cliente in (" + codigos + ") ", con)) {
                if (rsacessos.next()) {
                    sintetico.setAcessos(rsacessos.getInt("total"));
                }
            }

            try (ResultSet rsantigo = criarConsulta("select min(vigenciade) as min from contrato c inner join cliente cli on cli.pessoa = c.pessoa where cli.codigo in ("
                    + codigos + ") ", con)) {

                if (rsantigo.next()) {
                    sintetico.setInicioMaisAntigo(rsantigo.getDate("min"));
                }
            }

            try (ResultSet rsrecente = criarConsulta("select MAX(vigenciade) as max from contrato c inner join cliente cli on cli.pessoa = c.pessoa where cli.codigo in ("
                    + codigos + ") and situacaocontrato = 'MA'", con)) {

                if (rsrecente.next()) {
                    sintetico.setInicioMaisRecente(rsrecente.getDate("max"));
                }
            }

            try (ResultSet rsrisco = criarConsulta("select max(r.peso) as peso from risco r inner join cliente cli on cli.codigo = r.cliente where cli.situacao = 'AT' and r.cliente in ("
                    + codigos + ") ", con)) {

                if (rsrisco.next()) {
                    sintetico.setRisco(rsrisco.getInt("peso"));
                }
            }

            gravarFamiliaSintetico(sintetico);
        }

    }

    private void gravarFamiliaSintetico(FamiliaSinteticoVO sintetico) throws Exception{
        try (PreparedStatement stm = con.prepareStatement("INSERT INTO familiasintetico\n" +
                "(codigos, situacoes, valorcontratos, iniciomaisantigo, iniciomaisrecente, acessos, empresa, risco)\n" +
                "VALUES(?, ?, ?, ?, ?, ?, ?, ?)")) {
            int i = 0;
            stm.setString(++i, sintetico.getCodigos());
            stm.setString(++i, sintetico.getSituacoes());
            stm.setDouble(++i, sintetico.getValorContratos());
            stm.setDate(++i, sintetico.getInicioMaisAntigo() == null ? null : Uteis.getDataJDBC(sintetico.getInicioMaisAntigo()));
            stm.setDate(++i, sintetico.getInicioMaisRecente() == null ? null : Uteis.getDataJDBC(sintetico.getInicioMaisRecente()));
            stm.setInt(++i, sintetico.getAcessos());
            stm.setInt(++i, sintetico.getEmpresa());
            stm.setInt(++i, sintetico.getRisco());
            stm.execute();
        }
    }

    public BIFamiliaTO montarGestao(Integer empresa) throws Exception{
        BIFamiliaTO bi = new BIFamiliaTO();
        bi.setCadastradas(contar("select count(*) from familiasintetico where empresa = " + empresa, con));
        bi.setAtivas(contar("select count(*) from familiasintetico where situacoes like '%AT%' and empresa = " + empresa, con));
        bi.setRisco(contar("select count(*) from familiasintetico where risco > 5 and empresa = " + empresa, con));
        bi.setComInativo(contar("select count(*) from familiasintetico where situacoes like '%IN%' AND situacoes like '%AT%' and empresa = " + empresa, con));

        try (ResultSet rsvalor = criarConsulta("select codigos, valorcontratos from familiasintetico where situacoes like '%AT%' order by valorcontratos desc limit 1 ", con)) {
            if (rsvalor.next()) {
                bi.setValor(rsvalor.getDouble("valorcontratos"));
                bi.setMaiorValor(montarFamilia(rsvalor.getString("codigos"), null));
            }
        }

        try (ResultSet rsacessos = criarConsulta("select codigos, iniciomaisantigo, acessos from familiasintetico where situacoes like '%AT%' order by acessos desc limit 1 ", con)) {
            if (rsacessos.next()) {
                bi.setAcessosDesde(rsacessos.getDate("iniciomaisantigo"));
                bi.setAcessos(rsacessos.getInt("acessos"));
                bi.setMaisAcessos(montarFamilia(rsacessos.getString("codigos"), null));
            }
        }

        try (ResultSet rsantigos = criarConsulta("select codigos, iniciomaisantigo from familiasintetico where situacoes like '%AT%' order by iniciomaisantigo limit 1 ", con)) {
            if (rsantigos.next()) {
                bi.setDesde(rsantigos.getDate("iniciomaisantigo"));
                bi.setMaisTempo(montarFamilia(rsantigos.getString("codigos"), null));
            }
        }

        try (ResultSet rsrecentes = criarConsulta("select codigos,iniciomaisrecente  from familiasintetico where situacoes like '%AT%' order by iniciomaisrecente desc limit 1 ", con)) {
            if (rsrecentes.next()) {
                bi.setRecente(rsrecentes.getDate("iniciomaisrecente"));
                bi.setMaisRecente(montarFamilia(rsrecentes.getString("codigos"), null));
            }
        }

        return bi;
    }

    public void tranformarSugestaoEmFamilia(FamiliaTO sugestao, Integer empresa) throws Exception{
        if(UteisValidacao.emptyList(sugestao.getIntegrantes())){
            throw new Exception("Foi encontrado um problema com a sugestão de família");
        }
        ParentescoVO parentescoVO = getFacade().getParentesco().obterParentescoCriandoSeNaoExiste("FAMILIAR");
        for(FamiliarTO cli : sugestao.getIntegrantes()){
            try (ResultSet rscodacesso = criarConsulta(" select codacesso from cliente where codigo = " + cli.getCodigoCliente(), con)) {
                if (rscodacesso.next()) {
                    String codAcesso = rscodacesso.getString("codacesso");
                    for (FamiliarTO f : sugestao.getIntegrantes()) {
                        if (!f.getCodigoCliente().equals(cli.getCodigoCliente())) {
                            FamiliarVO familiarVO = new FamiliarVO();
                            familiarVO.setParentesco(parentescoVO);
                            familiarVO.setCliente(cli.getCodigoCliente());
                            familiarVO.setIdentificador("FAMIL");
                            familiarVO.setFamiliar(f.getCodigoCliente());
                            familiarVO.setCodAcesso(codAcesso);
                            familiarVO.setNome(f.getNome());
                            incluir(familiarVO, false);
                        }
                    }
                }
            }
        }

        executarConsulta("update sugestaofamilia set aceito = true where codigo = " + sugestao.getCodigoSugestao(), con);
        gerarSintetico(empresa);
    }

    public void negarSugestaoFamilia(FamiliaTO sugestao) throws Exception{
        executarConsulta("update sugestaofamilia set negado = false where codigo = " + sugestao.getCodigoSugestao(), con);
    }

    public List<FamiliaSinteticoVO> familias(TipoBIFamiliaEnum tipo, Integer empresa) throws Exception{
        List<FamiliaSinteticoVO> familias = new ArrayList<>();

        ResultSet rs = null;

        switch (tipo){
            case FAMILIAS:
                rs = criarConsulta("select * from familiasintetico where empresa = " + empresa, con);
                break;
            case ATIVAS:
                rs = criarConsulta("select * from familiasintetico where situacoes like '%AT%' and empresa = " + empresa, con);
                break;
            case INATIVA:
                rs = criarConsulta("select * from familiasintetico where risco > 5 and empresa = " + empresa, con);
                break;
            case RISCO:
                rs = criarConsulta("select * from familiasintetico where situacoes like '%IN%' AND situacoes like '%AT%' and empresa = " + empresa, con);
                break;
        }

        if(rs == null){
            return null;
        }

        try {
            while (rs.next()) {
                try {
                    FamiliaSinteticoVO familia = new FamiliaSinteticoVO();
                    familia.setAcessos(rs.getInt("acessos"));
                    familia.setRisco(rs.getInt("risco"));
                    familia.setSituacoes(rs.getString("situacoes"));
                    familia.setValorContratos(rs.getDouble("valorcontratos"));
                    try (ResultSet rsnomes = criarConsulta("select array_to_string(array(select nomecliente from situacaoclientesinteticodw where codigocliente in (" +
                            rs.getString("codigos") + ") order by nomecliente asc), ',')  as nomes", con)) {

                        if (rsnomes.next()) {
                            familia.setNomes(rsnomes.getString("nomes"));
                        }
                    }

                    try (ResultSet rsemails = criarConsulta("\n" +
                            "\n" +
                            "select array_to_string(array(select email from situacaoclientesinteticodw s inner join email e on e.pessoa = s.codigopessoa where codigocliente in (" +
                            rs.getString("codigos") +
                            ") order by nomecliente asc), ',') as emails", con)) {

                        if (rsemails.next()) {
                            familia.setEmails(rsemails.getString("emails"));
                        }
                    }

                    familias.add(familia);
                } catch (Exception ignored) {
                }
            }
        } finally {
            rs.close();
        }

        return Ordenacao.ordenarLista(familias, "nomes");
    }

    public FamiliarVO consultarPorDependentePlanoCompartilhado(Integer codClienteDependente) throws Exception {
        String sqlStr = "SELECT * FROM Familiar\n" +
                "WHERE familiar = " + codClienteDependente + "\n" +
                "AND compartilharPlano = TRUE;";
        try (Statement stm = con.createStatement();
             ResultSet tabelaResultado = stm.executeQuery(sqlStr)) {
            if (tabelaResultado.next()) {
                return montarDados(tabelaResultado, Uteis.NIVELMONTARDADOS_DADOSBASICOS, con);
            }
            return new FamiliarVO();
        }
    }

    public FamiliarVO consultarPorClienteFamiliar(Integer codCliente, Integer codClienteFamiliar) throws Exception {
        String sqlStr = "SELECT * FROM familiar\n" +
                "WHERE cliente = " + codCliente + "\n" +
                "AND familiar = " + codClienteFamiliar + "\n" +
                "ORDER BY codigo DESC";
        try (Statement stm = con.createStatement();
             ResultSet tabelaResultado = stm.executeQuery(sqlStr)) {
            if (tabelaResultado.next()) {
                return montarDados(tabelaResultado, Uteis.NIVELMONTARDADOS_DADOSBASICOS, con);
            }
            return new FamiliarVO();
        }
    }

    public List<FamiliarVO> consultarFamiliaresDependentes(ClienteVO clienteVO, int nivelMontarDados) throws Exception {
        String sqlStr = "SELECT * FROM Familiar\n" +
                "WHERE cliente = " + clienteVO.getCodigo() + "\n" +
                "AND compartilharPlano = TRUE;";
        try (Statement stm = con.createStatement();
             ResultSet tabelaResultado = stm.executeQuery(sqlStr)) {
            return montarDadosConsulta(tabelaResultado, nivelMontarDados, con);
        }
    }

    public void desabilitarCompartilhamentoPlano(Integer codigo) throws Exception {
        String sql = "UPDATE familiar SET compartilharPlano = FALSE WHERE codigo = ?";
        try (PreparedStatement stm = con.prepareStatement(sql)) {
            stm.setInt(1, codigo);
            stm.execute();
        }
    }

    public List<FamiliarVO> consultarPorCodigoFamiliar(Integer codClienteFamiliar, int niverMontarDados) throws Exception {
        String sqlStr = "SELECT * FROM familiar\n" +
                "WHERE familiar = " + codClienteFamiliar + "\n" +
                "ORDER BY codigo DESC";
        try (Statement stm = con.createStatement();
             ResultSet tabelaResultado = stm.executeQuery(sqlStr)) {
            List<FamiliarVO> familiarVOS = new ArrayList<>();
            while (tabelaResultado.next()) {
                familiarVOS.add(montarDados(tabelaResultado, niverMontarDados, con));
            }
            return familiarVOS;
        }
    }
}

package negocio.facade.jdbc.basico;

import negocio.comuns.basico.ConfiguracaoSistemaCadastroClienteVO;
import negocio.comuns.basico.ConfiguracaoSistemaVO;
import negocio.comuns.utilitarias.ConsistirException;
import negocio.comuns.utilitarias.Uteis;
import negocio.facade.jdbc.arquitetura.SuperEntidade;
import negocio.interfaces.basico.ConfiguracaoSistemaCadastroClienteInterfaceFacade;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.Statement;
import java.util.ArrayList;
import java.util.Iterator;
import java.util.List;

/**
 * Classe de persistência que encapsula todas as operações de manipulação dos
 * dados da classe
 * <code>ConfiguracaoSistemaCadastroClienteVO</code>. Responsável por
 * implementar operações como incluir, alterar, excluir e consultar pertinentes
 * a classe
 * <code>ConfiguracaoSistemaCadastroClienteVO</code>. Encapsula toda a interação
 * com o banco de dados.
 *
 * @see ConfiguracaoSistemaCadastroClienteVO
 * @see SuperEntidade
 */
public class ConfiguracaoSistemaCadastroCliente extends SuperEntidade implements ConfiguracaoSistemaCadastroClienteInterfaceFacade {

    protected ConfiguracaoSistemaVO configuracaoSistemaVO;

    public ConfiguracaoSistemaCadastroCliente() throws Exception {
        super();
    }

    public ConfiguracaoSistemaCadastroCliente(Connection c) throws Exception {
        super(c);
    }

    public void alterar(ConfiguracaoSistemaVO obj) throws Exception {
        try {
            con.setAutoCommit(false);
            alterarConfiguracaoClienteSemCommit(obj);
            con.commit();
        } catch (Exception e) {
            con.rollback();
            throw new Exception(e);
        } finally {
            con.setAutoCommit(true);
        }
    }

    public void alterar(ConfiguracaoSistemaCadastroClienteVO obj) throws Exception{
        String sql = "update ConfiguracaoSistemaCadastroCliente set obrigatorio=?, mostrar=?, pendente=? where trim(nome) =trim(?) and visitante =?";
        try (PreparedStatement pst = con.prepareStatement(sql)) {
            pst.setBoolean(1, obj.getObrigatorio());
            pst.setBoolean(2, obj.getMostrar());
            pst.setBoolean(3, obj.getPendente());
            pst.setString(4, obj.getNome());
            pst.setBoolean(5, obj.isVisitante());
            pst.execute();
        }
    }

    /**
     * Operação responsável por alterar no BD os dados de um objeto da classe
     * <code>ConfiguracaoSistemaCadastroClienteVO</code>. Sempre utiliza a chave
     * primária da classe como atributo para localização do registro a ser
     * alterado. Primeiramente valida os dados (
     * <code>validarDados</code>) do objeto. Verifica a conexão com o banco de
     * dados e a permissão do usuário para realizar esta operacão na entidade.
     * Isto, através da operação
     * <code>alterar</code> da superclasse.
     *
     * @param obj Objeto da classe <code>ConfiguracaoSistemaVO</code> que será
     * alterada no banco de dados.
     * @exception Exception Caso haja problemas de conexão, restrição de acesso
     * ou validação de dados.
     */
    public void alterarConfiguracaoClienteSemCommit(ConfiguracaoSistemaVO obj) throws Exception {
        String sql = "UPDATE ConfiguracaoSistemaCadastroCliente set obrigatorio = ?, mostrar = ?, pendente = ? WHERE trim(nome) = trim(?) AND visitante = ?";
        try (PreparedStatement sqlAlterar = con.prepareStatement(sql)) {
            if (obj.getListaConfiguracaoCamposCliente() != null) {
                Iterator i = obj.getListaConfiguracaoCamposCliente().iterator();
                while (i.hasNext()) {
                    ConfiguracaoSistemaCadastroClienteVO configuracaoCliente = (ConfiguracaoSistemaCadastroClienteVO) i.next();
                    sqlAlterar.setBoolean(1, configuracaoCliente.getObrigatorio());
                    sqlAlterar.setBoolean(2, configuracaoCliente.getMostrar());
                    sqlAlterar.setBoolean(3, configuracaoCliente.getPendente());
                    sqlAlterar.setString(4, configuracaoCliente.getNome());
                    sqlAlterar.setBoolean(5, false);
                    sqlAlterar.execute();
                }
            }
            if (obj.getListaConfiguracaoCamposVisitante() != null) {
                Iterator v = obj.getListaConfiguracaoCamposVisitante().iterator();
                while (v.hasNext()) {
                    ConfiguracaoSistemaCadastroClienteVO configuracaoCliente = (ConfiguracaoSistemaCadastroClienteVO) v.next();
                    sqlAlterar.setBoolean(1, configuracaoCliente.getObrigatorio());
                    sqlAlterar.setBoolean(2, configuracaoCliente.getMostrar());
                    sqlAlterar.setBoolean(3, configuracaoCliente.getPendente());
                    sqlAlterar.setString(4, configuracaoCliente.getNome());
                    sqlAlterar.setBoolean(5, true);
                    sqlAlterar.execute();
                }
            }
        }


    }

    /**
     * Responsável por realizar uma consulta de
     * <code>ConfiguracaoSistemaCadastroCliente</code> através do valor do
     * atributo
     * <code>descricao</code> da classe
     * <code>Questionario</code> Faz uso da operação
     * <code>montarDadosConsulta</code> que realiza o trabalho de prerarar o
     * List resultante.
     *
     * @return List Contendo vários objetos da
     * classe <code>ConfiguracaoSistemaVO</code> resultantes da consulta.
     * @exception Exception Caso haja problemas de conexão ou restrição de
     * acesso.
     */
    @Override
    public List<ConfiguracaoSistemaCadastroClienteVO> consultar(Boolean visitante) throws Exception {
        consultar(getIdEntidade(), false);
        String sqlStr = "SELECT * FROM ConfiguracaoSistemaCadastroCliente WHERE " + (visitante ? " visitante " : " not visitante") + " ORDER BY nome";
        try (Statement stm = con.createStatement()) {
            try (ResultSet tabelaResultado = stm.executeQuery(sqlStr)) {
                return montarDadosConsulta(tabelaResultado, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
            }
        }
    }

    public List consultarTodos(int nivelMontarDados) throws Exception{
        try (Statement st = con.createStatement()) {
            try (ResultSet rs = st.executeQuery("select * from ConfiguracaoSistemaCadastroCliente")) {
                return montarDadosConsulta(rs, nivelMontarDados);
            }
        }
    }

    @Override
    public ConfiguracaoSistemaCadastroClienteVO consultarConfiguracaoCampoNome(String nome, boolean visitante) throws Exception {
        consultar(getIdEntidade(), false);
        String sql = "select  * from configuracaosistemacadastrocliente where trim(nome) = '" + nome + "' and visitante = '"+visitante+"'";
        try (Statement stm = con.createStatement()) {
            try (ResultSet tabelaResultado = stm.executeQuery(sql)) {
                if (!tabelaResultado.next()) {
                    throw new ConsistirException("Dados Não Encontrados ( ConfiguracaoSistemaCadastroCliente ).");
                }
                return montarDados(tabelaResultado, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
            }
        }
    }

    /**
     * Responsável por montar os dados de vários objetos, resultantes de uma
     * consulta ao banco de dados (
     * <code>ResultSet</code>). Faz uso da operação
     * <code>montarDados</code> que realiza o trabalho para um objeto por vez.
     *
     * @return List Contendo vários objetos da
     * classe <code>ConfiguracaoSistemaCadastroClienteVO</code> resultantes da
     * consulta.
     */
    public static List<ConfiguracaoSistemaCadastroClienteVO> montarDadosConsulta(ResultSet tabelaResultado, int nivelMontarDados) throws Exception {
        List<ConfiguracaoSistemaCadastroClienteVO> vetResultado = new ArrayList<>();
        while (tabelaResultado.next()) {
            ConfiguracaoSistemaCadastroClienteVO obj = montarDados(tabelaResultado, nivelMontarDados);
            vetResultado.add(obj);
        }
        return vetResultado;
    }

    /**
     * Responsável por montar os dados resultantes de uma consulta ao banco de
     * dados (
     * <code>ResultSet</code>) em um objeto da classe
     * <code>ConfiguracaoSistemaCadastroClienteVO</code>.
     *
     * @return O objeto da
     * classe <code>ConfiguracaoSistemaCadastroClienteVO</code> com os dados
     * devidamente montados.
     */
    public static ConfiguracaoSistemaCadastroClienteVO montarDados(ResultSet dadosSQL, int nivelMontarDados) throws Exception {
        ConfiguracaoSistemaCadastroClienteVO obj = new ConfiguracaoSistemaCadastroClienteVO();
        obj.setNome(dadosSQL.getString("nome"));
        obj.setObrigatorio(dadosSQL.getBoolean("obrigatorio"));
        obj.setMostrar(dadosSQL.getBoolean("mostrar"));
        obj.setPendente(dadosSQL.getBoolean("pendente"));
        return obj;
    }
}

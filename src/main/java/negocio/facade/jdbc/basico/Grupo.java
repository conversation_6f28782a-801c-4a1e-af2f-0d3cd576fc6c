package negocio.facade.jdbc.basico;

import negocio.comuns.basico.GrupoVO;
import negocio.comuns.basico.PessoaVO;
import negocio.comuns.utilitarias.ConsistirException;
import negocio.comuns.utilitarias.Ordenacao;
import negocio.comuns.utilitarias.Uteis;
import negocio.facade.jdbc.arquitetura.SuperEntidade;
import negocio.interfaces.basico.GrupoInterfaceFacade;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.sql.Statement;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

/**
 * Classe de persistência que encapsula todas as operações de manipulação dos dados da classe <code>GrupoVO</code>.
 * Responsável por implementar operações como incluir, alterar, excluir e consultar pertinentes a classe <code>GrupoVO</code>.
 * Encapsula toda a interação com o banco de dados.
 * @see GrupoVO
 * @see SuperEntidade
 */
public class Grupo extends SuperEntidade implements GrupoInterfaceFacade {    

    public Grupo() throws Exception {
        super();        
    }

    public Grupo(Connection con) throws Exception {
		super(con);
	}

	/**
     * Operação responsável por retornar um novo objeto da classe <code>GrupoVO</code>.
     */
    public GrupoVO novo() throws Exception {
        incluir(getIdEntidade());
        GrupoVO obj = new GrupoVO();
        return obj;
    }

    /**
     * Operação responsável por incluir no banco de dados um objeto da classe <code>GrupoVO</code>.
     * Primeiramente valida os dados (<code>validarDados</code>) do objeto. Verifica a conexão com o banco de dados e a permissão do usuário
     * para realizar esta operacão na entidade.
     * Isto, através da operação <code>incluir</code> da superclasse.
     * @param obj  Objeto da classe <code>GrupoVO</code> que será gravado no banco de dados.
     * @exception Exception Caso haja problemas de conexão, restrição de acesso ou validação de dados.
     */
    public void incluir(GrupoVO obj) throws Exception {
        try {
            GrupoVO.validarDados(obj);
            incluir(getIdEntidade());
            obj.realizarUpperCaseDados();
            String sql = "INSERT INTO Grupo( descricao, percentualDescontoGrupo, valorDescontoGrupo, tipoDesconto, quantidadeMinimaAluno, situacaoAluno, tipo, grupoInativo)" +
                    " VALUES ( ?, ?, ?, ? , ?, ?, ?, ?)";
            try (PreparedStatement sqlInserir = con.prepareStatement(sql)) {
                sqlInserir.setString(1, obj.getDescricao());
                sqlInserir.setDouble(2, obj.getPercentualDescontoGrupo().doubleValue());
                sqlInserir.setDouble(3, obj.getValorDescontoGrupo().doubleValue());
                sqlInserir.setString(4, obj.getTipoDesconto());
                sqlInserir.setInt(5, obj.getQuantidadeMinimaAluno() == null ? 0 : obj.getQuantidadeMinimaAluno());
                sqlInserir.setString(6, obj.getSituacaoAluno());
                sqlInserir.setString(7, obj.getTipo());
                sqlInserir.setBoolean(8, obj.getGrupoInativo());
                sqlInserir.execute();
            }
            obj.setCodigo(obterValorChavePrimariaCodigo());
            obj.setNovoObj(new Boolean(false));
        } catch (Exception e) {
            throw e;
        }
    }

    /**
     * Operação responsável por alterar no BD os dados de um objeto da classe <code>GrupoVO</code>.
     * Sempre utiliza a chave primária da classe como atributo para localização do registro a ser alterado.
     * Primeiramente valida os dados (<code>validarDados</code>) do objeto. Verifica a conexão com o banco de dados e a permissão do usuário
     * para realizar esta operacão na entidade.
     * Isto, através da operação <code>alterar</code> da superclasse.
     * @param obj    Objeto da classe <code>GrupoVO</code> que será alterada no banco de dados.
     * @exception Exception Caso haja problemas de conexão, restrição de acesso ou validação de dados.
     */
    public void alterar(GrupoVO obj) throws Exception {
        try {
            con.setAutoCommit(false);
            alterarSemCommit(obj);
            con.commit();
        } catch (Exception e) {
            con.rollback();
            con.setAutoCommit(true);
            throw e;
        } finally {
            con.setAutoCommit(true);
        }
    }

    public void alterarSemCommit(GrupoVO obj) throws Exception{
        GrupoVO.validarDados(obj);
        alterar(getIdEntidade());
        obj.realizarUpperCaseDados();
        String sql = "UPDATE Grupo set descricao=?, percentualDescontoGrupo=?, valorDescontoGrupo=?, tipoDesconto=?, quantidadeMinimaAluno=?, situacaoAluno=?, tipo=?, grupoInativo=? WHERE ((codigo = ?))";
        try (PreparedStatement sqlAlterar = con.prepareStatement(sql)) {
            sqlAlterar.setString(1, obj.getDescricao());
            sqlAlterar.setDouble(2, obj.getPercentualDescontoGrupo().doubleValue());
            sqlAlterar.setDouble(3, obj.getValorDescontoGrupo().doubleValue());
            sqlAlterar.setString(4, obj.getTipoDesconto());
            sqlAlterar.setInt(5, obj.getQuantidadeMinimaAluno() == null ? 0 : obj.getQuantidadeMinimaAluno());
            sqlAlterar.setString(6, obj.getSituacaoAluno());
            sqlAlterar.setString(7, obj.getTipo());
            sqlAlterar.setBoolean(8, obj.getGrupoInativo());
            sqlAlterar.setInt(9, obj.getCodigo().intValue());
            sqlAlterar.execute();
        }
    }

    /**
     * Operação responsável por excluir no BD um objeto da classe <code>GrupoVO</code>.
     * Sempre localiza o registro a ser excluído através da chave primária da entidade.
     * Primeiramente verifica a conexão com o banco de dados e a permissão do usuário
     * para realizar esta operacão na entidade.
     * Isto, através da operação <code>excluir</code> da superclasse.
     * @param obj    Objeto da classe <code>GrupoVO</code> que será removido no banco de dados.
     * @exception Exception Caso haja problemas de conexão ou restrição de acesso.
     */
    public void excluir(GrupoVO obj) throws Exception {
        try {
            con.setAutoCommit(false);
            excluir(getIdEntidade());
            String sql = "DELETE FROM Grupo WHERE ((codigo = ?))";
            try (PreparedStatement sqlExcluir = con.prepareStatement(sql)) {
                sqlExcluir.setInt(1, obj.getCodigo().intValue());
                sqlExcluir.execute();
            }
            con.commit();
        } catch (Exception e) {
            con.rollback();
            con.setAutoCommit(true);
            throw e;
        } finally {
            con.setAutoCommit(true);
        }
    }

    /**
     * Responsável por realizar uma consulta de <code>Grupo</code> através do valor do atributo 
     * <code>String tipoDesconto</code>. Retorna os objetos, com início do valor do atributo idêntico ao parâmetro fornecido.
     * Faz uso da operação <code>montarDadosConsulta</code> que realiza o trabalho de prerarar o List resultante.
     * @param   controlarAcesso Indica se a aplicação deverá verificar se o usuário possui permissão para esta consulta ou não.
     * @return  List Contendo vários objetos da classe <code>GrupoVO</code> resultantes da consulta.
     * @exception Exception Caso haja problemas de conexão ou restrição de acesso.
     */
    public List consultarPorTipoDesconto(String valorConsulta, boolean controlarAcesso, int nivelMontarDados) throws Exception {
        consultar(getIdEntidade(), controlarAcesso);
        String sqlStr = "SELECT * FROM Grupo WHERE upper( tipoDesconto ) like('" + valorConsulta.toUpperCase() + "%') ORDER BY tipoDesconto";
        try (Statement stm = con.createStatement()) {
            try (ResultSet tabelaResultado = stm.executeQuery(sqlStr)) {
                return (montarDadosConsulta(tabelaResultado, nivelMontarDados));
            }
        }
    }

    /**
     * Responsável por realizar uma consulta de <code>Grupo</code> através do valor do atributo 
     * <code>String descricao</code>. Retorna os objetos, com início do valor do atributo idêntico ao parâmetro fornecido.
     * Faz uso da operação <code>montarDadosConsulta</code> que realiza o trabalho de prerarar o List resultante.
     * @param   controlarAcesso Indica se a aplicação deverá verificar se o usuário possui permissão para esta consulta ou não.
     * @return  List Contendo vários objetos da classe <code>GrupoVO</code> resultantes da consulta.
     * @exception Exception Caso haja problemas de conexão ou restrição de acesso.
     */
    public List consultarPorDescricao(String valorConsulta, boolean controlarAcesso, int nivelMontarDados) throws Exception {
        consultar(getIdEntidade(), controlarAcesso);
        String sqlStr = "SELECT * FROM Grupo WHERE upper( descricao ) like('" + valorConsulta.toUpperCase() + "%') ORDER BY descricao";
        try (Statement stm = con.createStatement()) {
            try (ResultSet tabelaResultado = stm.executeQuery(sqlStr)) {
                return (montarDadosConsulta(tabelaResultado, nivelMontarDados));
            }
        }
    }

    /**
     * Responsável por realizar uma consulta de <code>Grupo</code> através do valor do atributo 
     * <code>Integer codigo</code>. Retorna os objetos com valores iguais ou superiores ao parâmetro fornecido.
     * Faz uso da operação <code>montarDadosConsulta</code> que realiza o trabalho de prerarar o List resultante.
     * @param   controlarAcesso Indica se a aplicação deverá verificar se o usuário possui permissão para esta consulta ou não.
     * @return  List Contendo vários objetos da classe <code>GrupoVO</code> resultantes da consulta.
     * @exception Exception Caso haja problemas de conexão ou restrição de acesso.
     */
    public List consultarPorCodigo(Integer valorConsulta, boolean controlarAcesso, int nivelMontarDados) throws Exception {
        consultar(getIdEntidade(), controlarAcesso);
        String sqlStr = "SELECT * FROM Grupo WHERE codigo >= " + valorConsulta.intValue() + " ORDER BY codigo";
        try (Statement stm = con.createStatement()) {
            try (ResultSet tabelaResultado = stm.executeQuery(sqlStr)) {
                return (montarDadosConsulta(tabelaResultado, nivelMontarDados));
            }
        }
    }
    
    public GrupoVO consultarPorCodigoExato(Integer valorConsulta, boolean controlarAcesso, int nivelMontarDados) throws Exception {
        consultar(getIdEntidade(), controlarAcesso);
        String sql = "SELECT * FROM Grupo WHERE codigo = ?";
        GrupoVO grupo;
        try (PreparedStatement sqlConsultar = con.prepareStatement(sql)) {
            sqlConsultar.setInt(1, valorConsulta.intValue());
            try (ResultSet tabelaResultado = sqlConsultar.executeQuery()) {
                grupo = null;
                while (tabelaResultado.next()) {
                    grupo = montarDados(tabelaResultado, nivelMontarDados);
                }
            }
        }
        return grupo;
    }

    /**
     * Responsável por realizar uma consulta de <code>Grupo</code> através do valor do atributo
     * <code>Boolean grupoInativo</code>. Retorna os objetos, com início do valor do atributo idêntico ao parâmetro fornecido.
     * Faz uso da operação <code>montarDadosConsulta</code> que realiza o trabalho de prerarar o List resultante.
     * @param   controlarAcesso Indica se a aplicação deverá verificar se o usuário possui permissão para esta consulta ou não.
     * @return  List Contendo vários objetos da classe <code>GrupoVO</code> resultantes da consulta.
     * @exception Exception Caso haja problemas de conexão ou restrição de acesso.
     */
    public List consultarPorGrupoInativo(String valorConsulta, boolean controlarAcesso, int nivelMontarDados) throws Exception {
        consultar(getIdEntidade(), controlarAcesso);
        boolean itemConsulta;
        String sqlStr = "";
        if(valorConsulta == "Todos"){
            sqlStr = "SELECT * FROM Grupo ORDER BY descricao";
        }
        else{
            if(valorConsulta == "Inativo"){
                itemConsulta = true;
            }
            else{
                itemConsulta = false;
            }
            sqlStr = "SELECT * FROM Grupo WHERE grupoInativo=" + itemConsulta + " ORDER BY descricao";
        }

        try (Statement stm = con.createStatement()) {
            try (ResultSet tabelaResultado = stm.executeQuery(sqlStr)) {
                return (montarDadosConsulta(tabelaResultado, nivelMontarDados));
            }
        }
    }

    /**
     * Responsável por montar os dados de vários objetos, resultantes de uma consulta ao banco de dados (<code>ResultSet</code>).
     * Faz uso da operação <code>montarDados</code> que realiza o trabalho para um objeto por vez.
     * @return  List Contendo vários objetos da classe <code>GrupoVO</code> resultantes da consulta.
     */
    public static List montarDadosConsulta(ResultSet tabelaResultado, int nivelMontarDados) throws Exception {
        List vetResultado = new ArrayList();
        while (tabelaResultado.next()) {
            GrupoVO obj = new GrupoVO();
            obj = montarDados(tabelaResultado, nivelMontarDados);
            vetResultado.add(obj);
        }
        return vetResultado;
    }

    /**
     * Responsável por montar os dados resultantes de uma consulta ao banco de dados (<code>ResultSet</code>)
     * em um objeto da classe <code>GrupoVO</code>.
     * @return  O objeto da classe <code>GrupoVO</code> com os dados devidamente montados.
     */
    public static GrupoVO montarDados(ResultSet dadosSQL, int nivelMontarDados) throws Exception {
        GrupoVO obj = new GrupoVO();
        obj.setCodigo(new Integer(dadosSQL.getInt("codigo")));
        obj.setDescricao(dadosSQL.getString("descricao"));
        obj.setPercentualDescontoGrupo(new Double(dadosSQL.getDouble("percentualDescontoGrupo")));
        obj.setValorDescontoGrupo(new Double(dadosSQL.getDouble("valorDescontoGrupo")));
        obj.setTipoDesconto(dadosSQL.getString("tipoDesconto"));
        obj.setQuantidadeMinimaAluno(dadosSQL.getInt("quantidadeMinimaAluno"));
        obj.setSituacaoAluno(dadosSQL.getString("situacaoAluno"));
        obj.setTipoDesconto(dadosSQL.getString("tipoDesconto"));
        obj.setNovoObj(new Boolean(false));
        obj.setTipo(dadosSQL.getString("tipo"));
        obj.setGrupoInativo(dadosSQL.getBoolean("grupoInativo"));
        if (nivelMontarDados == Uteis.NIVELMONTARDADOS_DADOSBASICOS) {
            return obj;
        }

        return obj;
    }

    /**
     * Operação responsável por localizar um objeto da classe <code>GrupoVO</code>
     * através de sua chave primária. 
     * @exception Exception Caso haja problemas de conexão ou localização do objeto procurado.
     */
    public GrupoVO consultarPorChavePrimaria(Integer codigoPrm, int nivelMontarDados) throws Exception {
        consultar(getIdEntidade(), false);
        String sql = "SELECT * FROM Grupo WHERE codigo = ?";
        try (PreparedStatement sqlConsultar = con.prepareStatement(sql)) {
            sqlConsultar.setInt(1, codigoPrm.intValue());
            try (ResultSet tabelaResultado = sqlConsultar.executeQuery()) {
                if (!tabelaResultado.next()) {
                    throw new ConsistirException("Dados Não Encontrados ( Grupo ).");
                }
                return (montarDados(tabelaResultado, nivelMontarDados));
            }
        }
    }

    public String consultarJSON() throws Exception {
        StringBuilder json;
        boolean dados;
        try (ResultSet rs = getPS().executeQuery()) {
            GrupoVO grupo = new GrupoVO();
            json = new StringBuilder();
            json.append("{\"aaData\":[");
            dados = false;
            while (rs.next()) {
                dados = true;
                json.append("[\"").append(rs.getString("codigo")).append("\",");
                json.append("\"").append(Uteis.normalizarStringJSON(rs.getString("descricao").trim())).append("\",");
                grupo.setTipoDesconto(rs.getString("tipodesconto"));
                json.append("\"").append(grupo.getTipoDesconto_Apresentar()).append("\"],");
            }
        }
        if(dados) {
            json.deleteCharAt(json.toString().length()-1);
        }
        json.append("]}");
        return json.toString();
    }

    private PreparedStatement getPS() throws SQLException {
        String sql = "select codigo, descricao, tipodesconto from grupo;";
        return con.prepareStatement(sql);
    }
     public List consultarParaImpressao(String filtro, String ordem, String campoOrdenacao, int i) throws SQLException {
         List lista;
         try (ResultSet rs = getPS().executeQuery()) {
             lista = new ArrayList();
             while (rs.next()) {
                 GrupoVO grupo = new GrupoVO();
                 String geral = rs.getString("codigo") + rs.getString("descricao") + rs.getString("tipodesconto");
                 if (geral.toLowerCase().contains(filtro.toLowerCase())) {
                     grupo.setCodigo(rs.getInt("codigo"));
                     grupo.setDescricao(rs.getString("descricao"));
                     grupo.setTipoDesconto(rs.getString("tipodesconto"));
                     lista.add(grupo);
                 }
             }
         }
         if (campoOrdenacao.equals("Código")) {
            Ordenacao.ordenarLista(lista, "codigo");
        } else if (campoOrdenacao.equals("Descrição")) {
            Ordenacao.ordenarLista(lista, "descricao");
        } else if (campoOrdenacao.equals("Tipo")) {
            Ordenacao.ordenarLista(lista, "tipoDesconto_Apresentar");
        }
        if (ordem.contains("desc")) {
            Collections.reverse(lista);
        }
        return lista;

    }

    /**
     * Consulta todos os grupos do cliente que atenda a quantidade mínima de vincluos
     * Vários grupos podem atender essa condição. Nesse caso o grupo que resultar em um melhor desconto é retornado.
     *
     * @param pessoaVO
     * @return null se nenhum desconto foi encontrato.
     *         GrupoVO do grupo com maior valor de desconto com base no valor de contrato.
     */
    @Override
    public GrupoVO consultarMelhorGrupoPorVinculo(PessoaVO pessoaVO, int nivelMontarDados, double valorFinalContrato) throws Exception {

        // Grupos do tipo "GR" com maior valor de desconto final
        String sqlGrupo = "SELECT * FROM (SELECT grupo1.*," +
                "  CASE WHEN grupo1.tipodesconto = 'PE' THEN grupo1.percentualdescontogrupo * "+valorFinalContrato+"\n" +
                "    WHEN grupo1.tipodesconto = 'VA' THEN grupo1.valordescontogrupo\n" +
                "  END AS valorFinalDesconto,\n" +
                "  (SELECT COUNT(clientegrupo.cliente)\n" +
                "   FROM clientegrupo\n" +
                "     INNER JOIN grupo ON grupo.codigo = clientegrupo.grupo\n" +
                "     INNER JOIN cliente ON cliente.codigo = clientegrupo.cliente\n" +
                "   WHERE clientegrupo.grupo = grupo1.codigo AND (cliente.situacao = grupo.situacaoaluno OR grupo.situacaoaluno IS NULL OR trim(grupo.situacaoaluno) = '')\n" +
                "  ) AS \"qtdeAlunosMesmoGrupoESituacao\"\n" +
                "FROM grupo AS grupo1\n" +
                "  INNER JOIN clientegrupo on grupo1.codigo = clientegrupo.grupo\n" +
                "  INNER JOIN cliente on clientegrupo.cliente = cliente.codigo\n" +
                "WHERE cliente.pessoa = "+pessoaVO.getCodigo()+" AND grupo1.grupoInativo = false AND grupo1.tipo = 'GR'\n"+
                "ORDER BY valorFinalDesconto DESC\n"+
                "LIMIT 1 ) AS sq\n"+
                "WHERE \"qtdeAlunosMesmoGrupoESituacao\" >= quantidademinimaaluno";

        GrupoVO grupo;
        try (Statement stmG = con.createStatement()) {
            try (ResultSet rsGrupo = stmG.executeQuery(sqlGrupo)) {

                Double valorTotaldescontoGrupo = rsGrupo.next() ? rsGrupo.getDouble("valorfinaldesconto") : 0;

                // Grupo com tipo de vinculo "Em familia"
                StringBuilder sqlFamilia = new StringBuilder();
                sqlFamilia.append("SELECT\n");
                sqlFamilia.append("\tg.*,\n");
                sqlFamilia.append("\tCASE\n");
                sqlFamilia.append("\tWHEN g.tipodesconto = 'PE' THEN g.percentualdescontogrupo * ").append(valorFinalContrato).append("\n");
                sqlFamilia.append("\tWHEN g.tipodesconto = 'VA' THEN g.valordescontogrupo\n");
                sqlFamilia.append("\tEND AS valorFinalDesconto\n");
                sqlFamilia.append("FROM Grupo g\n");
                sqlFamilia.append("INNER JOIN ClienteGrupo cg ON cg.grupo = g.codigo\n");
                sqlFamilia.append("INNER JOIN cliente ON cliente.codigo = cg.cliente\n");
                sqlFamilia.append("WHERE g.tipo = 'FA'\n");
                sqlFamilia.append("AND (g.situacaoaluno IS NULL OR g.situacaoaluno = '' OR g.situacaoaluno = 'AT')\n");
                sqlFamilia.append("AND (g.quantidadeMinimaAluno > 0 AND (SELECT COUNT(familiar.familiar)\n");
                sqlFamilia.append("\tFROM familiar\n");
                sqlFamilia.append("\tINNER JOIN cliente AS cf ON cf.codigo = familiar.familiar\n");
                sqlFamilia.append("\tINNER JOIN cliente AS c ON c.codigo = familiar.cliente\n");
                sqlFamilia.append("\tWHERE c.pessoa = ").append(pessoaVO.getCodigo()).append("\n");
                sqlFamilia.append("\tAND ((g.situacaoaluno IS NULL OR g.situacaoaluno = '') OR g.situacaoaluno = cf.situacao)\n");
                sqlFamilia.append("\tAND g.grupoInativo = FALSE) >= g.quantidademinimaaluno)\n");
                sqlFamilia.append("AND cliente.pessoa = ").append(pessoaVO.getCodigo()).append("\n");
                sqlFamilia.append("ORDER BY valorFinalDesconto DESC\n");
                sqlFamilia.append("LIMIT 1");

                try (Statement stmF = con.createStatement()) {
                    try (ResultSet rsFamilia = stmF.executeQuery(sqlFamilia.toString())) {

                        Double valorTotaldescontoFalimia = rsFamilia.next() ? rsFamilia.getDouble("valorfinaldesconto") : 0;


                        if (valorTotaldescontoGrupo == 0 && valorTotaldescontoFalimia == 0) {
                            grupo = null;
                        } else if (valorTotaldescontoGrupo > valorTotaldescontoFalimia) {
                            grupo = montarDados(rsGrupo, nivelMontarDados);
                        } else {
                            grupo = montarDados(rsFamilia, nivelMontarDados);
                        }
                    }
                }
            }
        }

        return grupo;
    }

    @Override
    public GrupoVO consultarGrupoVinculoFamiliar(PessoaVO pessoaVO, int nivelMontarDados, double valorFinalContrato) throws Exception {

        // Grupos do tipo "GR" com maior valor de desconto final
        String sqlGrupo = "SELECT * FROM (SELECT grupo1.*," +
                "  CASE WHEN grupo1.tipodesconto = 'PE' THEN grupo1.percentualdescontogrupo * "+valorFinalContrato+"\n" +
                "    WHEN grupo1.tipodesconto = 'VA' THEN grupo1.valordescontogrupo\n" +
                "  END AS valorFinalDesconto,\n" +
                "  (SELECT COUNT(clientegrupo.cliente)\n" +
                "   FROM clientegrupo\n" +
                "     INNER JOIN grupo on grupo.codigo = clientegrupo.grupo\n" +
                "     INNER JOIN cliente on cliente.codigo = clientegrupo.cliente\n" +
                "   WHERE clientegrupo.grupo = grupo1.codigo AND (cliente.situacao = grupo.situacaoaluno OR grupo.situacaoaluno IS NULL OR trim(grupo.situacaoaluno) = '')\n" +
                "  ) AS \"qtdeAlunosMesmoGrupoESituacao\"\n" +
                "FROM grupo as grupo1\n" +
                "  INNER JOIN clientegrupo ON grupo1.codigo = clientegrupo.grupo\n" +
                "  INNER JOIN cliente ON clientegrupo.cliente = cliente.codigo\n" +
                "WHERE cliente.pessoa = "+pessoaVO.getCodigo()+" AND grupo1.grupoInativo = false AND grupo1.tipo = 'GR'\n"+
                "ORDER BY valorFinalDesconto DESC\n"+
                "LIMIT 1 ) AS sq\n"+
                "WHERE \"qtdeAlunosMesmoGrupoESituacao\" >= quantidademinimaaluno";

        GrupoVO grupo;
        try (Statement stmG = con.createStatement()) {
            try (ResultSet rsGrupo = stmG.executeQuery(sqlGrupo)) {

                Double valorTotaldescontoGrupo = rsGrupo.next() ? rsGrupo.getDouble("valorfinaldesconto") : 0;

                // Grupo com tipo de vinculo "Em familia"
                String sqlFamilia = "SELECT g.*,\n" +
                        "CASE\n" +
                        "WHEN g.tipodesconto = 'PE' THEN g.percentualdescontogrupo *" + valorFinalContrato + "\n" +
                        "WHEN g.tipodesconto = 'VA' THEN g.valordescontogrupo\n" +
                        "END as valorFinalDesconto\n" +
                        "FROM Grupo g WHERE g.tipo = 'FA' AND g.situacaoaluno = 'AT'\n" +
                        "GROUP BY g.tipodesconto, g.valordescontogrupo, g.percentualdescontogrupo, g.descricao, g.codigo\n" +
                        "HAVING ((SELECT COUNT(familiar.familiar)\n" +
                        " FROM familiar\n" +
                        "       INNER JOIN cliente AS cf ON cf.codigo = familiar.familiar\n" +
                        "       INNER JOIN cliente AS c ON c.codigo = familiar.cliente\n" +
                        "       WHERE c.pessoa = "+pessoaVO.getCodigo()+ " AND cf.situacao = 'AT' AND g.grupoInativo = false) >= g.quantidademinimaaluno)\n" +
                        " ORDER BY valorFinalDesconto desc limit 1";

                try (Statement stmF = con.createStatement()) {
                    try (ResultSet rsFamilia = stmF.executeQuery(sqlFamilia)) {

                        Double valorTotaldescontoFalimia = rsFamilia.next() ? rsFamilia.getDouble("valorfinaldesconto") : 0;


                        if (valorTotaldescontoGrupo == 0 && valorTotaldescontoFalimia == 0) {
                            grupo = null;
                        } else if (valorTotaldescontoGrupo > valorTotaldescontoFalimia) {
                            grupo = montarDados(rsGrupo, nivelMontarDados);
                        } else {
                            grupo = montarDados(rsFamilia, nivelMontarDados);
                        }
                    }
                }
            }
        }

        return grupo;
    }


}

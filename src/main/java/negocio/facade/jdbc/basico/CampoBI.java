/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package negocio.facade.jdbc.basico;

import br.com.pactosolucoes.enumeradores.BIEnum;
import br.com.pactosolucoes.enumeradores.CampoBIEnum;
import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import negocio.comuns.basico.CampoBIVO;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisValidacao;
import negocio.facade.jdbc.arquitetura.SuperEntidade;
import negocio.interfaces.basico.CampoBIInterfaceFacade;

/**
 *
 * <AUTHOR>
 */
public class CampoBI extends SuperEntidade implements CampoBIInterfaceFacade{

    public CampoBI() throws Exception{
        super();
    }
    
    public CampoBI(Connection con) throws Exception{
        super(con);
    }
    
    @Override
    public List<CampoBIVO> consultarPorBI(BIEnum bi, Integer mes, Integer ano, Integer empresa) throws Exception {
        StringBuilder sql = new StringBuilder();
        sql.append("SELECT * FROM campobi WHERE bi = ").append(bi.getCodigo()).append(" AND mes = ").append(mes).append(" AND ano = ").append(ano);
        if (!UteisValidacao.emptyNumber(empresa)) {
            sql.append(" AND empresa = ").append(empresa);
        }
        ResultSet rs = criarConsulta(sql.toString(), con);
        return montarDadosConsulta(rs);
    }

    @Override
    public void deletarPorBI(BIEnum bi, Integer mes, Integer ano, Integer empresa) throws Exception {
        executarConsulta("DELETE FROM campobi WHERE bi = "+bi.getCodigo()+" AND mes = "+mes+" AND ano = "+ano+" AND empresa = "+empresa, con);
    }

    @Override
    public void incluir(CampoBIVO campoBI) throws Exception {
        PreparedStatement stm = con.prepareStatement("INSERT INTO campobi (campobi, bi, valor, data, mes, ano, empresa) VALUES (?,?,?,?,?,?,?)");
        stm.setInt(1, campoBI.getCampoBiEnum().getCodigo());
        stm.setInt(2, campoBI.getCampoBiEnum().getBi().getCodigo());
        stm.setDouble(3, campoBI.getValor());
        stm.setTimestamp(4, Uteis.getDataJDBCTimestamp(campoBI.getData()));
        stm.setInt(5, campoBI.getMes());
        stm.setInt(6, campoBI.getAno());
        stm.setInt(7, campoBI.getEmpresa());
        stm.execute();
        
    }

    @Override
    public void incluirPorBI(List<CampoBIVO> campos) throws Exception {
        for(CampoBIVO campo : campos){
            incluir(campo);
        }
    }
    
    public List<CampoBIVO> montarDadosConsulta(ResultSet rs ) throws SQLException{
        List<CampoBIVO> lista = new ArrayList<CampoBIVO>();
        while(rs.next()){
            lista.add(montarDados(rs));
        }
        return lista;
    }
    
    public CampoBIVO montarDados(ResultSet rs) throws SQLException{
        CampoBIVO campoBi = new CampoBIVO();
        campoBi.setCodigo(rs.getInt("codigo"));
        campoBi.setValor(rs.getDouble("valor"));
        campoBi.setMes(rs.getInt("mes"));
        campoBi.setEmpresa(rs.getInt("empresa"));
        campoBi.setAno(rs.getInt("ano"));
        campoBi.setData(rs.getTimestamp("data"));
        campoBi.setCampoBiEnum(CampoBIEnum.getFromCodigo(rs.getInt("campobi")));
        return campoBi;
    }
    
    @Override
    public Map<CampoBIEnum, CampoBIVO> obterMapaPorBI(BIEnum bi, Integer mes, Integer ano, Integer empresa) throws Exception{
        List<CampoBIVO> lista = consultarPorBI(bi, mes, ano, empresa);
        Map<CampoBIEnum, CampoBIVO> mapa = new HashMap<>();
        for(CampoBIVO campo : lista){
            mapa.put(campo.getCampoBiEnum(), campo);
        }
        return mapa;
    }
}

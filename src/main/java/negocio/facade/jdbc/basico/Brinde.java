/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package negocio.facade.jdbc.basico;

import negocio.comuns.basico.enumerador.TipoPessoaEnum;
import org.json.JSONArray;
import org.json.JSONObject;
import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.sql.Statement;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Date;
import java.util.List;

import negocio.comuns.basico.BrindeVO;
import negocio.comuns.basico.EmpresaVO;
import negocio.comuns.basico.enumerador.TipoItemCampanhaEnum;
import negocio.comuns.utilitarias.Ordenacao;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisValidacao;
import negocio.facade.jdbc.arquitetura.SuperEntidade;
import negocio.interfaces.basico.BrindeInterfaceFacade;
import relatorio.negocio.comuns.basico.ListaPaginadaTO;

public class Brinde extends SuperEntidade implements BrindeInterfaceFacade{

    public Brinde() throws Exception {
        super();
        setIdEntidade("Brinde");
    }

    public Brinde(Connection conexao) throws Exception{
        super(conexao);
        setIdEntidade("Brinde");
    }

    @Override
    public BrindeVO novo() throws Exception {
        incluir(getIdEntidade());
        BrindeVO brindes = new BrindeVO();
        return brindes;
    }

    @Override
    public void incluir(BrindeVO obj) throws Exception {
        incluir(getIdEntidade());
        BrindeVO.validarDados(obj);
        StringBuilder sql = new StringBuilder();
        sql.append(" INSERT INTO brinde(ativo, pontos, nome, descricao, empresa, aplicarPontuacaoParaTodosOsPlanos) \n");
        sql.append(" VALUES (?, ?, ?, ?, ?, ?)");
        try (PreparedStatement sqlInserir = con.prepareStatement(sql.toString())) {
            sqlInserir.setBoolean(1, obj.getAtivo());
            sqlInserir.setInt(2, obj.getPontos());
            sqlInserir.setString(3, obj.getNome());
            sqlInserir.setString(4, obj.getDescricao());
            sqlInserir.setInt(5, obj.getEmpresa().getCodigo());
            sqlInserir.setBoolean(6, obj.isAplicarPontuacaoParaTodosOsPlanos());
            sqlInserir.execute();
        }
        obj.setCodigo(obterValorChavePrimariaCodigo());

        getFacade().getBrindePlano().atualizar(obj);
    }

    @Override
    public void alterar(BrindeVO obj) throws Exception {
        try {
            alterar(getIdEntidade());
            con.setAutoCommit(false);
            alterarSemCommit(obj);
            con.commit();
        } catch (Exception e) {
            con.rollback();
            con.setAutoCommit(true);
            throw e;
        } finally {
            con.setAutoCommit(true);
        }
    }

    @Override
    public void excluir(BrindeVO obj) throws Exception {
        try {
            excluir(getIdEntidade());
            con.setAutoCommit(false);
            StringBuilder sql = new StringBuilder();
            sql.append("DELETE FROM brinde WHERE (codigo = ?)");
            try (PreparedStatement sqlExcluir = con.prepareStatement(sql.toString())) {
                sqlExcluir.setInt(1, obj.getCodigo());
                sqlExcluir.execute();
            }
            con.commit();
        } catch (Exception e) {
            con.rollback();
            con.setAutoCommit(true);
            throw e;
        }finally {
            con.setAutoCommit(true);
        }
    }

    @Override
    public List<BrindeVO> consultarTodosBrindes(Integer empresa) throws Exception {
        StringBuilder sql = new StringBuilder();
        sql.append("SELECT * from brinde WHERE empresa = "+empresa+"");
        try (Statement stm = con.createStatement()) {
            try (ResultSet tabelaResultado = stm.executeQuery(sql.toString())) {
                return montarDadosConsulta(tabelaResultado, Uteis.NIVELMONTARDADOS_DADOSBASICOS, this.con);
            }
        }
    }

    public List<BrindeVO> montarDadosConsulta(ResultSet tabelaResultado, int nivelMontarDados, Connection con) throws SQLException, Exception{
        List<BrindeVO> listaResultado = new ArrayList<BrindeVO>();
        while (tabelaResultado.next()) {
            BrindeVO brinde = montarDados(tabelaResultado, nivelMontarDados, con);
            listaResultado.add(brinde);
        }

        return listaResultado;
    }

    public BrindeVO montarDados(ResultSet rs, int nivelMontarDados, Connection con) throws SQLException, Exception{
        BrindeVO brinde = new BrindeVO();
        brinde.setCodigo(rs.getInt("codigo"));
        brinde.setNome(rs.getString("nome"));
        brinde.setAtivo(rs.getBoolean("ativo"));
        brinde.setPontos(rs.getInt("pontos"));
        brinde.setAplicarPontuacaoParaTodosOsPlanos(rs.getBoolean("aplicarPontuacaoParaTodosOsPlanos"));
        if (nivelMontarDados == Uteis.NIVELMONTARDADOS_TODOS) {
            brinde.setDescricao(rs.getString("descricao"));
            Empresa empresa = new Empresa(con);
            brinde.setEmpresa(empresa.consultarPorChavePrimaria(rs.getInt("empresa"), Uteis.NIVELMONTARDADOS_DADOSBASICOS));
            brinde.setCodigoPlanos(getFacade().getBrindePlano().consultarCodigosPlanos(brinde.getCodigo()));
        }

        if (nivelMontarDados == Uteis.NIVELMONTARDADOS_DADOSBASICOS) {
            return brinde;
        }

        if(nivelMontarDados == Uteis.NIVELMONTARDADOS_RESULTADOS_BI){
            Empresa empresa = new Empresa(con);
            brinde.setEmpresa(empresa.consultarPorChavePrimaria(rs.getInt("empresa"), Uteis.NIVELMONTARDADOS_DADOSBASICOS));
        }

        return brinde;
    }

    @Override
    public List<BrindeVO> consultarTodosBrindes(Integer empresa, int nivelMontarDados) throws Exception {
        StringBuilder sql = new StringBuilder();
        sql.append("SELECT * from brinde WHERE empresa = "+empresa+"");
        try (Statement stm = con.createStatement()) {
            try (ResultSet tabelaResultado = stm.executeQuery(sql.toString())) {
                return montarDadosConsulta(tabelaResultado, nivelMontarDados, this.con);
            }
        }
    }

    @Override
    public void alterarSemCommit(BrindeVO obj) throws Exception {
        BrindeVO.validarDados(obj);
        StringBuilder sql = new StringBuilder();
        sql.append("UPDATE brinde set ativo = ?, pontos = ?, nome = ?, descricao = ?,  aplicarPontuacaoParaTodosOsPlanos = ? WHERE codigo = ?");
        try (PreparedStatement sqlAlterar = con.prepareStatement(sql.toString())) {
            sqlAlterar.setBoolean(1, obj.getAtivo());
            sqlAlterar.setInt(2, obj.getPontos());
            sqlAlterar.setString(3, obj.getNome());
            sqlAlterar.setString(4, obj.getDescricao());
            sqlAlterar.setBoolean(5, obj.isAplicarPontuacaoParaTodosOsPlanos());
            sqlAlterar.setInt(6, obj.getCodigo());
            sqlAlterar.execute();
        }

        getFacade().getBrindePlano().atualizar(obj);
    }

    @Override
    public String consultarJSON(Integer empresa) throws Exception {
        StringBuilder json;
        boolean dados;
        try (ResultSet rs = getPS(empresa).executeQuery()) {
            json = new StringBuilder();
            json.append("{\"aaData\":[");
            dados = false;
            while (rs.next()) {
                dados = true;
                json.append("[\"").append(rs.getString("codigo")).append("\",");
                json.append("\"").append(Uteis.normalizarStringJSON(rs.getString("nome"))).append("\",");
                if (rs.getBoolean("ativo")) {
                    json.append("\"").append("Ativo").append("\",");
                } else {
                    json.append("\"").append("Inativo").append("\",");
                }

                json.append("\"").append(rs.getInt("pontos")).append("\"],");
            }
        }
        if (dados) {
            json.deleteCharAt(json.toString().length() - 1);
        }
        json.append("]}");
        return json.toString();
    }

    private PreparedStatement getPS(Integer empresa) throws SQLException {
        StringBuilder sql = new StringBuilder();
        sql.append("SELECT * from brinde WHERE empresa = "+empresa+"");
        return con.prepareStatement(sql.toString());
    }

    private PreparedStatement getPS(Integer ponto,Integer empresa, String mostrarTodos)throws SQLException{
        StringBuilder sql = new StringBuilder();
        if (UteisValidacao.emptyNumber(empresa)){
            if (!UteisValidacao.emptyString(mostrarTodos)) {
                if (mostrarTodos.equals("nao")) {
                    sql.append("SELECT * from brinde where pontos <= "+ponto+" AND ativo");
                }else{
                    sql.append("SELECT * from brinde where ativo");
                }
            }else{
                sql.append("SELECT * from brinde where pontos <= "+ponto+" AND ativo");
            }
        }else{
            if (!UteisValidacao.emptyString(mostrarTodos)) {
                if (mostrarTodos.equals("nao")) {
                    sql.append("SELECT * from brinde where pontos <= "+ponto+" AND empresa = "+empresa+" AND ativo");
                }else{
                    sql.append("SELECT * from brinde where empresa = "+empresa+" AND ativo");
                }
            }else{
                sql.append("SELECT * from brinde where pontos <= "+ponto+" AND empresa = "+empresa+" AND ativo");
            }
        }
        return con.prepareStatement(sql.toString());
    }

    @Override
    public BrindeVO consultarPorChavePrimaria(Integer codigo) throws Exception {
        if (UteisValidacao.emptyNumber(codigo)) {
            return new BrindeVO();
        }
        StringBuilder sql = new StringBuilder();
        sql.append("SELECT * from brinde WHERE codigo = "+codigo+"");
        try (Statement stm = con.createStatement()) {
            try (ResultSet retornoPesquisa = stm.executeQuery(sql.toString())) {
                retornoPesquisa.next();
                return montarDados(retornoPesquisa, Uteis.NIVELMONTARDADOS_TODOS, con);
            }
        }
    }

    @Override
    public List consultarParaImpressao(String filtro, String ordem, String campoOrdenacao, int empresa) throws SQLException {
        List lista;
        try (ResultSet rs = getPS(empresa).executeQuery()) {
            lista = new ArrayList();
            while (rs.next()) {
                BrindeVO brinde = new BrindeVO();
                String geral = rs.getString("codigo") + rs.getString("nome") + (rs.getBoolean("ativo") ? "ATIVO" : "INATIVO") + rs.getInt("pontos");
                if (geral.toLowerCase().contains(filtro.toLowerCase())) {
                    brinde.setCodigo(rs.getInt("codigo"));
                    brinde.setNome(rs.getString("nome"));
                    brinde.setAtivo(rs.getBoolean("ativo"));
                    brinde.setPontos(rs.getInt("pontos"));
                    lista.add(brinde);
                }
            }
        }

        if (campoOrdenacao.equals("Codigo")) {
            Ordenacao.ordenarLista(lista, "codigo");
        }else if(campoOrdenacao.equals("Nome")){
            Ordenacao.ordenarLista(lista, "nome");
        }else if(campoOrdenacao.equals("Situação")){
            Ordenacao.ordenarLista(lista, "ativo");
        }else if(campoOrdenacao.equals("Pontos")){
            Ordenacao.ordenarLista(lista, "pontos");
        }

        if (ordem.contains("desc")) {
            Collections.reverse(lista);
        }

        return lista;
    }

    @Override
    public List<BrindeVO> consultarBrindeDisponiveisPorPonto(Double ponto, Integer empresa) throws Exception {
        StringBuilder sql = new StringBuilder();
        if (UteisValidacao.emptyNumber(empresa)){
            sql.append("SELECT * from brinde where pontos <= "+ponto+" AND ativo");
        }else{
            sql.append("SELECT * from brinde where pontos <= "+ponto+" AND empresa = "+empresa+" AND ativo");
        }
        try (Statement stm = con.createStatement()) {
            try (ResultSet tabelaResultado = stm.executeQuery(sql.toString())) {
                return montarDadosConsulta(tabelaResultado, Uteis.NIVELMONTARDADOS_TODOS, this.con);
            }
        }
    }

    public List<BrindeVO> consultarBrindeDisponiveisPorPonto(Integer ponto, Integer empresa, String mostrarTodos) throws Exception {
        try (ResultSet rs = getPS(ponto, empresa, mostrarTodos).executeQuery()) {
            return montarDadosConsulta(rs, Uteis.NIVELMONTARDADOS_TODOS, this.con);
        }
    }

    @Override
    public String consultarBrindeDisponiveisPorPontoJson(Integer ponto, Integer empresa,String mostrarTodos) throws Exception {
        StringBuilder json;
        boolean dados;
        try (ResultSet rs = getPS(ponto, empresa, mostrarTodos).executeQuery()) {
            JSONObject objReturn = new JSONObject();
            JSONArray aaData = new JSONArray();

            while (rs.next()) {
                JSONArray itemArray = new JSONArray();
                itemArray.put(rs.getString("codigo"));
                itemArray.put(rs.getString("nome"));
                itemArray.put(rs.getString("pontos"));

                aaData.put(itemArray);
            }

            objReturn.put("aaData", aaData);
            return objReturn.toString();
        }
    }

    @Override
    public List<BrindeVO> consultarBrindesAtivos(Integer empresa, int nivelMontarDados) throws Exception {
        StringBuilder sql = new StringBuilder();
        sql.append("SELECT * from brinde WHERE ativo = true ");
        if (!UteisValidacao.emptyNumber(empresa)) {
            sql.append(" and empresa = ").append(empresa);
        }
        try (Statement stm = con.createStatement()) {
            try (ResultSet tabelaResultado = stm.executeQuery(sql.toString())) {
                return montarDadosConsulta(tabelaResultado, nivelMontarDados, this.con);
            }
        }
    }

    @Override
    public JSONArray consultarListaBrindeAtivoTreino(Integer empresa) throws Exception {
        StringBuilder sql = new StringBuilder();

        sql.append(" SELECT * from brinde ").append("\n");
        sql.append(" where 1=1 ").append("\n");
        if (empresa != null) {
            sql.append(" AND empresa = "+empresa).append("\n");
        }
        sql.append(" AND ativo = true ").append("\n");
        JSONArray array;
        try (Statement stm = con.createStatement()) {
            try (ResultSet tabelaResultado = stm.executeQuery(sql.toString())) {

                array = new JSONArray();
                while (tabelaResultado.next()) {
                    JSONObject json = new JSONObject();
                    json.put("codigo", tabelaResultado.getInt("codigo"));
                    json.put("ativo", tabelaResultado.getBoolean("ativo"));
                    json.put("pontos", tabelaResultado.getInt("pontos"));
                    json.put("nome", tabelaResultado.getString("nome"));
                    json.put("descricao", tabelaResultado.getString("descricao"));
                    array.put(json);
                }
            }
        }
        return array;
    }

    @Override
    public boolean existeBrindeParaPlano(Integer codigoPlano) throws Exception {
        String sql = "SELECT COUNT(*) qtde_brindes " +
                "FROM brinde " +
                "WHERE ativo IS TRUE " +
                "  AND (aplicarPontuacaoParaTodosOsPlanos IS TRUE " +
                "         OR (SELECT COUNT(*) FROM brindeplano WHERE brinde = brinde.codigo AND plano = "+codigoPlano+") > 0)";
        try (PreparedStatement ps = con.prepareStatement(sql)) {
            try (ResultSet rs = ps.executeQuery()) {
                rs.next();
                if (rs.getInt("qtde_brindes") > 0) {
                    return true;
                } else {
                    return false;
                }
            }
        }
    }

    @Override
    public List<BrindeVO> consultarTotalizadorBiBrindes(Date dataInicial, Date dataFinal, EmpresaVO empresa, ListaPaginadaTO paginadorListaBrindesBi) throws Exception {
        StringBuilder sql = new StringBuilder();
        sql.append("SELECT b.codigo,\n");
        sql.append("\tb.nome,\n");
        sql.append("\tNULL ativo,\n");
        sql.append("\tb.empresa,\n");
        sql.append("\tNULL aplicarPontuacaoParaTodosOsPlanos,\n");
        sql.append("\tcount(h.brinde) pontos\n");
        sql.append("FROM brinde b\n");
        sql.append("INNER JOIN historicopontos h ON h.brinde = b.codigo\n");
        sql.append("WHERE tipodepontos = ").append(TipoItemCampanhaEnum.RESGATE_BRINDE.getCodigo()).append("\n");
        sql.append("AND h.dataconfirmacao > '").append(Uteis.getDataJDBC(dataInicial)).append(" 00:00:00'").append("\n");
        sql.append("AND h.dataconfirmacao < '").append(Uteis.getDataJDBC(dataFinal)).append(" 23:59:59'").append("\n");
        if (!UteisValidacao.emptyNumber(empresa.getCodigo())) {
            sql.append("AND b.empresa = ").append(empresa.getCodigo()).append("\n");
        }
        sql.append("GROUP BY b.codigo, b.nome, b.empresa\n");
        sql.append("ORDER BY pontos DESC\n");

        try (Statement stm = con.createStatement()) {
            try (ResultSet tabelaResultado = stm.executeQuery("select count (*) total from ( "+sql.toString()+" ) total ")) {
                if(tabelaResultado.next())
                    paginadorListaBrindesBi.setCount(tabelaResultado.getInt("total"));
            }
            try (ResultSet tabelaResultado = stm.executeQuery(sql.toString()+" LIMIT " +paginadorListaBrindesBi.getLimit()+" OFFSET "+paginadorListaBrindesBi.getOffset())) {
                return montarDadosConsulta(tabelaResultado, Uteis.NIVELMONTARDADOS_RESULTADOS_BI, this.con);
            }
        }
    }
}

package negocio.facade.jdbc.basico;

import negocio.comuns.basico.PactoPayEnvioEmailComunicacaoVO;
import negocio.comuns.utilitarias.Uteis;
import negocio.facade.jdbc.arquitetura.SuperEntidade;
import negocio.interfaces.basico.PactoPayEnvioEmailComunicacaoInterfaceFacade;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;

public class PactoPayEnvioEmailComunicacao extends SuperEntidade implements PactoPayEnvioEmailComunicacaoInterfaceFacade {

    public PactoPayEnvioEmailComunicacao() throws Exception {
        super();
    }

    public PactoPayEnvioEmailComunicacao(Connection conexao) throws Exception {
        super(conexao);
    }

    private PactoPayEnvioEmailComunicacaoVO montarDadosBasico(ResultSet rs) throws Exception {
        PactoPayEnvioEmailComunicacaoVO obj = new PactoPayEnvioEmailComunicacaoVO();
        obj.setNovoObj(false);
        obj.setCodigo(rs.getInt("codigo"));
        obj.getPactoPayEnvioEmailVO().setCodigo(rs.getInt("pactopayenvioemail"));
        obj.getPactoPayComunicacaoVO().setCodigo(rs.getInt("pactopaycomunicacao"));
        return obj;
    }

    private PactoPayEnvioEmailComunicacaoVO montarDados(ResultSet dadosSQL, int nivelMontarDados, Connection con) throws Exception {
        PactoPayEnvioEmailComunicacaoVO obj = montarDadosBasico(dadosSQL);
        if (nivelMontarDados == Uteis.NIVELMONTARDADOS_DADOSBASICOS) {
            return obj;
        }
        return obj;
    }

    public void incluirSemCommit(PactoPayEnvioEmailComunicacaoVO obj) throws Exception {
        PactoPayEnvioEmailComunicacaoVO.validarDados(obj);
        String sql = "INSERT INTO pactopayenvioemailcomunicacao(pactopayenvioemail, pactopaycomunicacao) VALUES (?,?)";
        try (PreparedStatement ps = con.prepareStatement(sql)) {
            int i = 0;
            resolveIntegerNull(ps, ++i, obj.getPactoPayEnvioEmailVO().getCodigo());
            resolveIntegerNull(ps, ++i, obj.getPactoPayComunicacaoVO().getCodigo());
            ps.execute();
        }
        obj.setCodigo(obterValorChavePrimariaCodigo());
        obj.setNovoObj(false);
    }

    public PactoPayEnvioEmailComunicacaoVO consultarPorChavePrimaria(Integer codigo, int nivelMontarDados) throws Exception {
        String sql = "SELECT * FROM PactoPayEnvioEmailComunicacao WHERE codigo = ?";
        try (PreparedStatement ps = con.prepareStatement(sql)) {
            ps.setInt(1, codigo);
            try (ResultSet rs = ps.executeQuery()) {
                if (rs.next()) {
                    return montarDados(rs, nivelMontarDados, this.con);
                } else {
                    return null;
                }
            }
        }
    }
}

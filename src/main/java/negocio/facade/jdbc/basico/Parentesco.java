package negocio.facade.jdbc.basico;

import java.sql.SQLException;
import negocio.interfaces.basico.*;
import negocio.comuns.basico.ParentescoVO;
import negocio.facade.jdbc.arquitetura.*;

import java.sql.Connection;
import java.sql.ResultSet;
import java.sql.PreparedStatement;
import java.sql.Statement;
import java.util.List;
import java.util.ArrayList;
import java.util.Collections;
import negocio.comuns.utilitarias.*;

/**
 * Classe de persistência que encapsula todas as operações de manipulação dos dados da classe <code>ParentescoVO</code>.
 * Responsável por implementar operações como incluir, alterar, excluir e consultar pertinentes a classe <code>ParentescoVO</code>.
 * Encapsula toda a interação com o banco de dados.
 * @see ParentescoVO
 * @see SuperEntidade
 */
public class Parentesco extends SuperEntidade implements ParentescoInterfaceFacade {    

    public Parentesco() throws Exception {
        super();        
    }

    public Parentesco(Connection con) throws Exception {
		super(con);
	}

	/**
     * Operação responsável por retornar um novo objeto da classe <code>ParentescoVO</code>.
     */
    public ParentescoVO novo() throws Exception {
        incluir(getIdEntidade());
        ParentescoVO obj = new ParentescoVO();
        return obj;
    }

    /**
     * Operação responsável por incluir no banco de dados um objeto da classe <code>ParentescoVO</code>.
     * Primeiramente valida os dados (<code>validarDados</code>) do objeto. Verifica a conexão com o banco de dados e a permissão do usuário
     * para realizar esta operacão na entidade.
     * Isto, através da operação <code>incluir</code> da superclasse.
     * @param obj  Objeto da classe <code>ParentescoVO</code> que será gravado no banco de dados.
     * @exception Exception Caso haja problemas de conexão, restrição de acesso ou validação de dados.
     */
    public void incluir(ParentescoVO obj) throws Exception {
        try {
            ParentescoVO.validarDados(obj);
            incluir(getIdEntidade());
            obj.realizarUpperCaseDados();
            String sql = "INSERT INTO Parentesco( descricao, idadeLimiteDependencia ) VALUES ( ?, ? )";
            try (PreparedStatement sqlInserir = con.prepareStatement(sql)) {
                sqlInserir.setString(1, obj.getDescricao());
                sqlInserir.setInt(2, obj.getIdadeLimiteDependencia().intValue());
                sqlInserir.execute();
            }
            obj.setCodigo(obterValorChavePrimariaCodigo());
            obj.setNovoObj(new Boolean(false));
        } catch (Exception e) {
            throw e;
        }
    }

    /**
     * Operação responsável por alterar no BD os dados de um objeto da classe <code>ParentescoVO</code>.
     * Sempre utiliza a chave primária da classe como atributo para localização do registro a ser alterado.
     * Primeiramente valida os dados (<code>validarDados</code>) do objeto. Verifica a conexão com o banco de dados e a permissão do usuário
     * para realizar esta operacão na entidade.
     * Isto, através da operação <code>alterar</code> da superclasse.
     * @param obj    Objeto da classe <code>ParentescoVO</code> que será alterada no banco de dados.
     * @exception Execption Caso haja problemas de conexão, restrição de acesso ou validação de dados.
     */
    public void alterar(ParentescoVO obj) throws Exception {
        try {
            con.setAutoCommit(false);
            alterarSemCommit(obj);
            con.commit();
        } catch (Exception e) {
            con.rollback();
            con.setAutoCommit(true);
            throw e;
        } finally {
            con.setAutoCommit(true);
        }
    }

    public void alterarSemCommit(ParentescoVO obj) throws Exception {
            ParentescoVO.validarDados(obj);
            alterar(getIdEntidade());
            obj.realizarUpperCaseDados();
            String sql = "UPDATE Parentesco set descricao=?, idadeLimiteDependencia=? WHERE ((codigo = ?))";
        try (PreparedStatement sqlAlterar = con.prepareStatement(sql)) {
            sqlAlterar.setString(1, obj.getDescricao());
            sqlAlterar.setInt(2, obj.getIdadeLimiteDependencia().intValue());
            sqlAlterar.setInt(3, obj.getCodigo().intValue());
            sqlAlterar.execute();
        }
    }

    /**
     * Operação responsável por excluir no BD um objeto da classe <code>ParentescoVO</code>.
     * Sempre localiza o registro a ser excluído através da chave primária da entidade.
     * Primeiramente verifica a conexão com o banco de dados e a permissão do usuário
     * para realizar esta operacão na entidade.
     * Isto, através da operação <code>excluir</code> da superclasse.
     * @param obj    Objeto da classe <code>ParentescoVO</code> que será removido no banco de dados.
     * @exception Execption Caso haja problemas de conexão ou restrição de acesso.
     */
    public void excluir(ParentescoVO obj) throws Exception {
        try {
            con.setAutoCommit(false);
            excluir(getIdEntidade());
            String sql = "DELETE FROM Parentesco WHERE ((codigo = ?))";
            try (PreparedStatement sqlExcluir = con.prepareStatement(sql)) {
                sqlExcluir.setInt(1, obj.getCodigo().intValue());
                sqlExcluir.execute();
            }
            con.commit();
        } catch (Exception e) {
            con.rollback();
            con.setAutoCommit(true);
            throw e;
        } finally {
            con.setAutoCommit(true);
        }
    }

    /**
     * Responsável por realizar uma consulta de <code>Parentesco</code> através do valor do atributo 
     * <code>Integer idadeLimiteDependencia</code>. Retorna os objetos com valores iguais ou superiores ao parâmetro fornecido.
     * Faz uso da operação <code>montarDadosConsulta</code> que realiza o trabalho de prerarar o List resultante.
     * @param   controlarAcesso Indica se a aplicação deverá verificar se o usuário possui permissão para esta consulta ou não.
     * @return  List Contendo vários objetos da classe <code>ParentescoVO</code> resultantes da consulta.
     * @exception Exception Caso haja problemas de conexão ou restrição de acesso.
     */
    public List consultarPorIdadeLimiteDependencia(Integer valorConsulta, boolean controlarAcesso, int nivelMontarDados) throws Exception {
        consultar(getIdEntidade(), controlarAcesso);
        String sqlStr = "SELECT * FROM Parentesco WHERE idadeLimiteDependencia >= " + valorConsulta.intValue() + " ORDER BY idadeLimiteDependencia";
        try (Statement stm = con.createStatement()) {
            try (ResultSet tabelaResultado = stm.executeQuery(sqlStr)) {
                return (montarDadosConsulta(tabelaResultado, nivelMontarDados));
            }
        }
    }
    
    public ParentescoVO consultarPorIdadeLimiteDependenciaExata(Integer valorConsulta, boolean controlarAcesso, int nivelMontarDados) throws Exception {
        consultar(getIdEntidade(), controlarAcesso);
        String sqlStr = "SELECT * FROM Parentesco WHERE idadeLimiteDependencia = " + valorConsulta.intValue() + " ORDER BY idadeLimiteDependencia";
        ParentescoVO parentesco;
        try (Statement stm = con.createStatement()) {
            try (ResultSet tabelaResultado = stm.executeQuery(sqlStr)) {
                parentesco = null;
                while (tabelaResultado.next()) {
                    parentesco = montarDados(tabelaResultado, nivelMontarDados);
                }
            }
        }
        return parentesco;
    }

    /**
     * Responsável por realizar uma consulta de <code>Parentesco</code> através do valor do atributo 
     * <code>String descricao</code>. Retorna os objetos, com início do valor do atributo idêntico ao parâmetro fornecido.
     * Faz uso da operação <code>montarDadosConsulta</code> que realiza o trabalho de prerarar o List resultante.
     * @param   controlarAcesso Indica se a aplicação deverá verificar se o usuário possui permissão para esta consulta ou não.
     * @return  List Contendo vários objetos da classe <code>ParentescoVO</code> resultantes da consulta.
     * @exception Exception Caso haja problemas de conexão ou restrição de acesso.
     */
    public List consultarPorDescricao(String valorConsulta, boolean controlarAcesso, int nivelMontarDados) throws Exception {
        consultar(getIdEntidade(), controlarAcesso);
        String sqlStr = "SELECT * FROM Parentesco WHERE upper( descricao ) like('" + valorConsulta.toUpperCase() + "%') ORDER BY descricao";
        try (Statement stm = con.createStatement()) {
            try (ResultSet tabelaResultado = stm.executeQuery(sqlStr)) {
                return (montarDadosConsulta(tabelaResultado, nivelMontarDados));
            }
        }
    }

    /**
     * Responsável por realizar uma consulta de <code>Parentesco</code> através do valor do atributo 
     * <code>Integer codigo</code>. Retorna os objetos com valores iguais ou superiores ao parâmetro fornecido.
     * Faz uso da operação <code>montarDadosConsulta</code> que realiza o trabalho de prerarar o List resultante.
     * @param   controlarAcesso Indica se a aplicação deverá verificar se o usuário possui permissão para esta consulta ou não.
     * @return  List Contendo vários objetos da classe <code>ParentescoVO</code> resultantes da consulta.
     * @exception Exception Caso haja problemas de conexão ou restrição de acesso.
     */
    public List consultarPorCodigo(Integer valorConsulta, boolean controlarAcesso, int nivelMontarDados) throws Exception {
        consultar(getIdEntidade(), controlarAcesso);
        String sqlStr = "SELECT * FROM Parentesco WHERE codigo >= " + valorConsulta.intValue() + " ORDER BY codigo";
        try (Statement stm = con.createStatement()) {
            try (ResultSet tabelaResultado = stm.executeQuery(sqlStr)) {
                return (montarDadosConsulta(tabelaResultado, nivelMontarDados));
            }
        }
    }
    
    public ParentescoVO consultarPorCodigoExato(Integer valorConsulta, boolean controlarAcesso, int nivelMontarDados) throws Exception {
        consultar(getIdEntidade(), controlarAcesso);
        String sql = "SELECT * FROM Parentesco WHERE codigo = ?";
        ParentescoVO parentesco;
        try (PreparedStatement sqlConsultar = con.prepareStatement(sql)) {
            sqlConsultar.setInt(1, valorConsulta.intValue());
            try (ResultSet tabelaResultado = sqlConsultar.executeQuery()) {
                parentesco = null;
                while (tabelaResultado.next()) {
                    parentesco = montarDados(tabelaResultado, nivelMontarDados);
                }
            }
        }
        return parentesco;
    }

    /**
     * Responsável por montar os dados de vários objetos, resultantes de uma consulta ao banco de dados (<code>ResultSet</code>).
     * Faz uso da operação <code>montarDados</code> que realiza o trabalho para um objeto por vez.
     * @return  List Contendo vários objetos da classe <code>ParentescoVO</code> resultantes da consulta.
     */
    public static List montarDadosConsulta(ResultSet tabelaResultado, int nivelMontarDados) throws Exception {
        List vetResultado = new ArrayList();
        while (tabelaResultado.next()) {
            ParentescoVO obj = new ParentescoVO();
            obj = montarDados(tabelaResultado, nivelMontarDados);
            vetResultado.add(obj);
        }
        return vetResultado;
    }

    /**
     * Responsável por montar os dados resultantes de uma consulta ao banco de dados (<code>ResultSet</code>)
     * em um objeto da classe <code>ParentescoVO</code>.
     * @return  O objeto da classe <code>ParentescoVO</code> com os dados devidamente montados.
     */
    public static ParentescoVO montarDados(ResultSet dadosSQL, int nivelMontarDados) throws Exception {
        ParentescoVO obj = new ParentescoVO();
        obj.setCodigo(new Integer(dadosSQL.getInt("codigo")));
        obj.setDescricao(dadosSQL.getString("descricao"));
        obj.setIdadeLimiteDependencia(new Integer(dadosSQL.getInt("idadeLimiteDependencia")));
        obj.setNovoObj(new Boolean(false));
        if (nivelMontarDados == Uteis.NIVELMONTARDADOS_DADOSBASICOS) {
            return obj;
        }

        return obj;
    }

    /**
     * Operação responsável por localizar um objeto da classe <code>ParentescoVO</code>
     * através de sua chave primária. 
     * @exception Exception Caso haja problemas de conexão ou localização do objeto procurado.
     */
    public ParentescoVO consultarPorChavePrimaria(Integer codigoPrm, int nivelMontarDados) throws Exception {
        consultar(getIdEntidade(), false);
        String sql = "SELECT * FROM Parentesco WHERE codigo = ?";
        try (PreparedStatement sqlConsultar = con.prepareStatement(sql)) {
            sqlConsultar.setInt(1, codigoPrm.intValue());
            try (ResultSet tabelaResultado = sqlConsultar.executeQuery()) {
                if (!tabelaResultado.next()) {
                    throw new ConsistirException("Dados Não Encontrados ( Parentesco ).");
                }
                return (montarDados(tabelaResultado, nivelMontarDados));
            }
        }
    }

    public String consultarJSON() throws Exception {
        StringBuilder json;
        boolean dados;
        try (ResultSet rs = getPS().executeQuery()) {
            json = new StringBuilder();
            json.append("{\"aaData\":[");
            dados = false;
            while (rs.next()) {
                dados = true;
                json.append("[\"").append(rs.getString("codigo")).append("\",");
                json.append("\"").append(Uteis.normalizarStringJSON(rs.getString("descricao").trim())).append("\",");
                json.append("\"").append(rs.getString("idadelimitedependencia")).append("\"],");
            }
        }
        if(dados) {
            json.deleteCharAt(json.toString().length()-1);
        }
        json.append("]}");
        return json.toString();
    }

    private PreparedStatement getPS() throws SQLException {
        String sql = "select codigo, descricao, idadelimitedependencia from parentesco ORDER BY descricao";
        return con.prepareStatement(sql);
    }
     public List consultarParaImpressao(String filtro, String ordem, String campoOrdenacao, int i) throws SQLException {
         List lista;
         try (ResultSet rs = getPS().executeQuery()) {
             lista = new ArrayList();
             while (rs.next()) {
                 ParentescoVO p = new ParentescoVO();
                 String geral = rs.getString("codigo") + rs.getString("descricao") + rs.getString("idadelimitedependencia");
                 if (geral.toLowerCase().contains(filtro.toLowerCase())) {
                     p.setCodigo(rs.getInt("codigo"));
                     p.setDescricao(rs.getString("descricao"));
                     p.setIdadeLimiteDependencia(rs.getInt("idadelimitedependencia"));
                     lista.add(p);
                 }
             }
         }
         if (campoOrdenacao.equals("Código")) {
            Ordenacao.ordenarLista(lista, "codigo");
        } else if (campoOrdenacao.equals("Descrição")) {
            Ordenacao.ordenarLista(lista, "descricao");
        } else if (campoOrdenacao.equals("Idade Limite")) {
            Ordenacao.ordenarLista(lista, "idadeLimiteDependencia");
        }
        if (ordem.contains("desc")) {
            Collections.reverse(lista);
        }
        return lista;

    }

    public ParentescoVO obterParentescoDescricao(String valorConsulta, boolean controlarAcesso, int nivelMontarDados) throws Exception {
        consultar(getIdEntidade(), controlarAcesso);
        String sqlStr = "SELECT * FROM Parentesco WHERE upper( descricao ) like('" + valorConsulta.toUpperCase() + "%') ORDER BY descricao";
        try (Statement stm = con.createStatement()) {
            try (ResultSet tabelaResultado = stm.executeQuery(sqlStr)) {
                if (tabelaResultado.next()) {
                    return montarDados(tabelaResultado, nivelMontarDados);
                }
            }
        }
        return null;
    }

    public ParentescoVO obterParentescoCriandoSeNaoExiste(String valorConsulta) throws Exception {
        String sqlStr = "SELECT * FROM Parentesco WHERE upper( descricao ) like('" + valorConsulta.toUpperCase() + "%')";
        try (Statement stm = con.createStatement()) {
            try (ResultSet tabelaResultado = stm.executeQuery(sqlStr)) {
                if (tabelaResultado.next()) {
                    return montarDados(tabelaResultado, Uteis.NIVELMONTARDADOS_TODOS);
                } else {
                    ParentescoVO obj = new ParentescoVO();
                    obj.setDescricao(valorConsulta);
                    obj.setIdadeLimiteDependencia(99);
                    incluir(obj);
                    return obj;
                }
            }
        }
    }
}

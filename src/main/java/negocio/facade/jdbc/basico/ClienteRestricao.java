package negocio.facade.jdbc.basico;

import br.com.pactosolucoes.enumeradores.TipoClienteRestricaoEnum;
import negocio.comuns.utilitarias.Uteis;
import negocio.facade.jdbc.arquitetura.SuperEntidade;
import negocio.interfaces.basico.ClienteRestricaoInterfaceFacade;
import servicos.impl.admCoreMs.ClienteRestricaoDTO;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.Statement;
import java.util.ArrayList;
import java.util.List;

public class ClienteRestricao extends SuperEntidade implements ClienteRestricaoInterfaceFacade {

    public ClienteRestricao() throws Exception {
        super();
        setIdEntidade("ClienteRestricao");
    }

    public ClienteRestricao(Connection conexao) throws Exception {
        super(conexao);
        setIdEntidade("ClienteRestricao");
    }

    @Override
    public void incluir(ClienteRestricaoDTO obj) throws Exception {
        incluir(getIdEntidade());

        final StringBuilder sql = new StringBuilder();
        sql.append(" INSERT INTO clienterestricao(nome, codigomatricula, cpf, observacao, codigoempresa, nomeempresa, chaveempresa, tipo) \n");
        sql.append(" VALUES (?, ?, ?, ?, ?, ?, ?, ?)");

        try (PreparedStatement sqlInserir = con.prepareStatement(sql.toString())) {
            sqlInserir.setString(1, obj.getNome());
            sqlInserir.setInt(2, obj.getCodigoMatricula());
            sqlInserir.setString(3, obj.getCpf());
            sqlInserir.setString(4, obj.getObservacao());
            sqlInserir.setInt(5, obj.getCodigoEmpresa());
            sqlInserir.setString(6, obj.getNomeEmpresa());
            sqlInserir.setString(7, obj.getChaveEmpresa());
            sqlInserir.setString(8, obj.getTipo());
            sqlInserir.execute();
        }
    }

    @Override
    public void excluir(String cpf, TipoClienteRestricaoEnum tipoCliente) throws Exception {
        try {
            con.setAutoCommit(false);
            excluirSemCommit(cpf, tipoCliente);
            con.commit();
        } catch (Exception e) {
            con.rollback();
            con.setAutoCommit(true);
            throw e;
        } finally {
            con.setAutoCommit(true);
        }
    }

    public void excluirSemCommit(String cpf, TipoClienteRestricaoEnum tipoCliente) throws Exception {
        try {
            final String sql = "DELETE FROM clienterestricao WHERE (cpf = ? and tipo = ?)";
            try (PreparedStatement sqlExcluir = con.prepareStatement(sql)) {
                sqlExcluir.setString(1, cpf);
                sqlExcluir.setString(2, tipoCliente.getSigla());
                sqlExcluir.execute();
            }
        } catch (Exception e) {
            throw e;
        }
    }

    @Override
    public void excluir(List<String> cpfs, TipoClienteRestricaoEnum tipoCliente) throws Exception {
        try {
            con.setAutoCommit(false);
            excluirSemCommit(cpfs, tipoCliente);
            con.commit();
        } catch (Exception e) {
            con.rollback();
            con.setAutoCommit(true);
            throw e;
        } finally {
            con.setAutoCommit(true);
        }
    }


    public void excluirSemCommit(List<String> cpfs, TipoClienteRestricaoEnum tipoCliente) throws Exception {
        try {
            final String sql = "DELETE FROM clienterestricao WHERE cpf = ANY (?) and tipo = ?";
            try (PreparedStatement sqlExcluir = con.prepareStatement(sql)) {
                sqlExcluir.setArray(1, con.createArrayOf("text", cpfs.toArray()));
                sqlExcluir.setString(2, tipoCliente.getSigla());
                sqlExcluir.execute();
            }
        } catch (Exception e) {
            throw e;
        }
    }

    @Override
    public List<ClienteRestricaoDTO> consultarClienteRestricaoPorCpf(String cpf) throws Exception {
        final String sqlStr = "select * from clienterestricao c where c.cpf = '" + Uteis.formatarCpfCnpj(cpf, true) + "'";

        return this.executarConsultaClienteRestricao(sqlStr);
    }

    private List<ClienteRestricaoDTO> executarConsultaClienteRestricao(String sql) throws Exception {
        try (Statement stm = con.createStatement()) {
            try (ResultSet tabelaResultado = stm.executeQuery(sql)) {
                return montarDadosConsultaClienteRestricao(tabelaResultado, this.con);
            }
        }
    }

    public List<ClienteRestricaoDTO> montarDadosConsultaClienteRestricao(ResultSet tabelaResultado, Connection con) throws Exception {
        List<ClienteRestricaoDTO> vetResultado = new ArrayList();
        while (tabelaResultado.next()) {
            ClienteRestricaoDTO obj = montarDadosClienteRestricao(tabelaResultado, con);
            vetResultado.add(obj);
        }
        return vetResultado;
    }

    public static ClienteRestricaoDTO montarDadosClienteRestricao(ResultSet dadosSQL, Connection con) throws Exception {
        final ClienteRestricaoDTO obj = new ClienteRestricaoDTO();
        obj.setCodigo(dadosSQL.getInt("codigo"));
        obj.setNome(dadosSQL.getString("nome"));
        obj.setCodigoMatricula(dadosSQL.getInt("codigomatricula"));
        obj.setCpf(dadosSQL.getString("cpf"));
        obj.setObservacao(dadosSQL.getString("observacao"));
        obj.setCodigoEmpresa(dadosSQL.getInt("codigoempresa"));
        obj.setNomeEmpresa(dadosSQL.getString("nomeempresa"));
        obj.setChaveEmpresa(dadosSQL.getString("chaveempresa"));
        obj.setTipo(dadosSQL.getString("tipo"));

        return obj;
    }
}

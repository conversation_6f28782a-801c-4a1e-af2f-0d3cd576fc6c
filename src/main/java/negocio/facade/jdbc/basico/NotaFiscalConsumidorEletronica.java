package negocio.facade.jdbc.basico;

import br.com.pactosolucoes.comuns.util.Formatador;
import br.com.pactosolucoes.enumeradores.ProcessoAjusteGeralEnum;
import negocio.comuns.financeiro.NFSeEmitidaVO;
import negocio.facade.jdbc.financeiro.NFSeEmitidaHistorico;
import negocio.facade.jdbc.notaFiscal.NotaFiscal;
import org.json.JSONArray;
import org.json.JSONObject;
import negocio.comuns.arquitetura.UsuarioVO;
import negocio.comuns.basico.EmpresaVO;
import negocio.comuns.financeiro.NotaFiscalConsumidorEletronicaFormasPagamentoVO;
import negocio.comuns.financeiro.NotaFiscalConsumidorEletronicaItensVO;
import negocio.comuns.financeiro.NotaFiscalConsumidorEletronicaVO;
import negocio.comuns.nfe.RetornoEnvioNotaFiscalTO;
import negocio.comuns.nfe.enumerador.ResultadoEnvioNFSeEnum;
import negocio.comuns.notaFiscal.SituacaoNotaFiscalEnum;
import negocio.comuns.utilitarias.Calendario;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisValidacao;
import negocio.facade.jdbc.arquitetura.SuperEntidade;
import negocio.facade.jdbc.basico.model.NFeEmpresaDiretorioExecucaoPOJO;
import negocio.interfaces.basico.NotaFiscalConsumidorEletronicaInterfaceFacade;
import org.apache.commons.lang.StringUtils;
import servicos.util.ExecuteRequestHttpService;

import java.io.IOException;
import java.sql.*;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/*
 * Created by Luiz Felipe on 27/03/2017.
 */
public class NotaFiscalConsumidorEletronica extends SuperEntidade implements NotaFiscalConsumidorEletronicaInterfaceFacade {

    private static final SimpleDateFormat FORMATADOR_DATA = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
    private static final String PARAMETRO_CERT_VENC_REQUER_CERT = "UsaCertificado";
    private static final String PARAMETRO_CERT_VENC_DATA = "Data";

    private static final String PARAMETRO_CLIENTES = "clientes";
    private static final String PARAMETRO_CLIENTES_IDENTIFICADOR = "Id";
    private static final String PARAMETRO_CLIENTES_NOME_EXECUTAVEL = "NomeExe";
    private static final String PARAMETRO_CLIENTES_EMPRESAS = "Empresas";

    private static final String METODO_GRAVARNFCE = ".gravarNFCe";
    private static final String METODO_INUTILIZARNUMEROSNFCE = ".inutilizarNumerosNFCe";
    private static final String METODO_REENVIARNFCE = ".reenviarNFCe";
    private static final String METODO_GETDATACERTIFICADO = ".PegarDataDoCertificadoDB";
    private static final String METODO_GET_DIRETORIOS_EXECUCAO = ".ClientesConectados";
    private static final String METODO_GET_ADICIONAR_EMPRESA = "/AdicionarEmpresa";
    private static final String METODO_GET_REMOVER_EMPRESA = "/RemoverEmpresa";
    private static final String METODO_GET_REINICIAR_EXECUTAVEL = "/Reiniciar";
    private static final String METODO_GET_REINICIAR_TODOS_EXECUTAVEIS = "/ReiniciarGeral";

    public NotaFiscalConsumidorEletronica() throws Exception {
        super();
    }

    public NotaFiscalConsumidorEletronica(Connection con) throws Exception {
        super(con);
    }

    public void incluir(NotaFiscalConsumidorEletronicaVO obj) throws Exception {
        String sql = "INSERT INTO notafiscalconsumidoreletronica(id_NFCe, dataRegistro, reciboPagamento, usuario, valorTotal, movconta, cheque, movpagamento, "
                + "movproduto, cartaocredito, jsonEnviar, empresa, idreferencia, pessoa, enotas, situacaoNotaFiscal, dataEnvio, resultadoEnvio, configuracaonotafiscal) "
                + "VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)";
        PreparedStatement sqlInserir = con.prepareStatement(sql);
        sqlInserir.setInt(1, obj.getId_NFCe());
        sqlInserir.setTimestamp(2, Uteis.getDataJDBCTimestamp(obj.getDataRegistro()));
        if (obj.getReciboPagamento().getCodigo() != 0) {
            sqlInserir.setInt(3, obj.getReciboPagamento().getCodigo());
        } else {
            sqlInserir.setNull(3, 0);
        }
        sqlInserir.setInt(4, obj.getUsuario().getCodigo());
        sqlInserir.setDouble(5, obj.getValorTotal());
        if (obj.getMovConta().getCodigo() != 0) {
            sqlInserir.setInt(6, obj.getMovConta().getCodigo());
        } else {
            sqlInserir.setNull(6, 0);
        }
        if (obj.getCheque().getCodigo() != 0) {
            sqlInserir.setInt(7, obj.getCheque().getCodigo());
        } else {
            sqlInserir.setNull(7, 0);
        }
        if (obj.getMovPagamento().getCodigo() != 0) {
            sqlInserir.setInt(8, obj.getMovPagamento().getCodigo());
        } else {
            sqlInserir.setNull(8, 0);
        }
        if (obj.getMovProduto().getCodigo() != 0) {
            sqlInserir.setInt(9, obj.getMovProduto().getCodigo());
        } else {
            sqlInserir.setNull(9, 0);
        }
        if (obj.getCartaoCredito().getCodigo() != 0) {
            sqlInserir.setInt(10, obj.getCartaoCredito().getCodigo());
        } else {
            sqlInserir.setNull(10, 0);
        }
        sqlInserir.setString(11, obj.getJsonEnviar());

        if (obj.getEmpresa().getCodigo() != 0) {
            sqlInserir.setInt(12, obj.getEmpresa().getCodigo());
        } else {
            sqlInserir.setNull(12, 0);
        }
        sqlInserir.setString(13, obj.getIdReferencia());
        resolveIntegerNull(sqlInserir, 14, obj.getPessoaVO().getCodigo());
        sqlInserir.setBoolean(15, obj.isEnotas());
        sqlInserir.setInt(16, obj.getSituacaoNotaFiscal().getCodigo());
        sqlInserir.setTimestamp(17, Uteis.getDataJDBCTimestamp(obj.getDataEnvio()));
        sqlInserir.setString(18, obj.getResultadoEnvio());
        resolveIntegerNull(sqlInserir, 19, obj.getConfiguracaoNotaFiscalVO().getCodigo());

        sqlInserir.execute();
        obj.setCodigo(obterValorChavePrimariaCodigo());
        incluirItensNota(obj);
        incluirFormasPagamentoNota(obj);
        obj.setNovoObj(false);
    }

    private void incluirItensNota(NotaFiscalConsumidorEletronicaVO obj) throws Exception {
        for (NotaFiscalConsumidorEletronicaItensVO itensVO : obj.getItensNota()) {
            itensVO.setNotaFiscalConsumidorEletronica(obj);
            incluirItem(itensVO);
        }
    }

    private void incluirItem(NotaFiscalConsumidorEletronicaItensVO obj) throws Exception {
        String sql = "INSERT INTO notafiscalconsumidoreletronicaitens(notafiscalconsumidoreletronica, produto, descricao, valorUnitario, quantidade, valorDescontoOuAcrescimo, valorTotal) VALUES (?, ?, ?, ?, ?, ?, ?)";
        PreparedStatement sqlInserir = con.prepareStatement(sql);
        sqlInserir.setInt(1, obj.getNotaFiscalConsumidorEletronica().getCodigo());
        sqlInserir.setInt(2, obj.getProduto().getCodigo());
        sqlInserir.setString(3, obj.getDescricao());
        sqlInserir.setDouble(4, obj.getValorUnitario());
        sqlInserir.setInt(5, obj.getQuantidade());
        sqlInserir.setDouble(6, obj.getValorDescontoOuAcrescimo());
        sqlInserir.setDouble(7, obj.getValorTotal());
        sqlInserir.execute();
        obj.setCodigo(obterValorChavePrimariaCodigo());
        obj.setNovoObj(false);
    }

    private void incluirFormasPagamentoNota(NotaFiscalConsumidorEletronicaVO notaVO) throws Exception {
        for (NotaFiscalConsumidorEletronicaFormasPagamentoVO obj : notaVO.getFormasPagamentoNota()) {
            obj.setNotaFiscalConsumidorEletronica(notaVO);
            incluirFormaPagamento(obj);
        }
    }

    private void incluirFormaPagamento(NotaFiscalConsumidorEletronicaFormasPagamentoVO obj) throws Exception {
        String sql = "INSERT INTO notafiscalconsumidoreletronicaformaspagamento(notafiscalconsumidoreletronica, formapagamento, valor) VALUES (?, ?, ?)";
        PreparedStatement sqlInserir = con.prepareStatement(sql);
        sqlInserir.setInt(1, obj.getNotaFiscalConsumidorEletronica().getCodigo());
        sqlInserir.setInt(2, obj.getFormaPagamento().getCodigo());
        sqlInserir.setDouble(3, obj.getValor());
        sqlInserir.execute();
        obj.setCodigo(obterValorChavePrimariaCodigo());
        obj.setNovoObj(false);
    }

    public void atualizarIdNFCe(SituacaoNotaFiscalEnum situacaoNotaFiscalEnum, Integer id_nfce, NotaFiscalConsumidorEletronicaVO obj) throws Exception {
        String sql = "UPDATE notafiscalconsumidoreletronica SET id_nfce = ?, situacaoNotaFiscal = ? WHERE codigo = ?";
        PreparedStatement sqlUpdate = con.prepareStatement(sql);
        sqlUpdate.setInt(1, id_nfce);
        sqlUpdate.setInt(2, situacaoNotaFiscalEnum.getCodigo());
        sqlUpdate.setInt(3, obj.getCodigo());
        sqlUpdate.execute();
    }

    public NotaFiscalConsumidorEletronicaVO consultarPorChavePrimaria(Integer codigo, int nivelMontarDados) throws Exception {
        String sql = "SELECT * FROM notafiscalconsumidoreletronica WHERE codigo = " + codigo;
        Statement stm = con.createStatement();
        ResultSet tabelaResultado = stm.executeQuery(sql);
        if (tabelaResultado.next()) {
            return montarDados(tabelaResultado, nivelMontarDados, con);
        }
        return new NotaFiscalConsumidorEletronicaVO();
    }

    public NotaFiscalConsumidorEletronicaVO consultarPorIdNFCe(Integer IdNFCe, int nivelMontarDados) throws Exception {
        String sql = "SELECT * FROM notafiscalconsumidoreletronica WHERE id_nfce = " + IdNFCe;
        Statement stm = con.createStatement();
        ResultSet tabelaResultado = stm.executeQuery(sql);
        if (tabelaResultado.next()) {
            return montarDados(tabelaResultado, nivelMontarDados, con);
        }
        return new NotaFiscalConsumidorEletronicaVO();
    }

    public NotaFiscalConsumidorEletronicaVO consultarPorReciboPagamento(Integer reciboPagamento, int nivelMontarDados) throws Exception {
        String sql = "SELECT * FROM notafiscalconsumidoreletronica WHERE reciboPagamento = " + reciboPagamento;
        Statement stm = con.createStatement();
        ResultSet tabelaResultado = stm.executeQuery(sql);
        if (tabelaResultado.next()) {
            return montarDados(tabelaResultado, nivelMontarDados, con);
        }
        return new NotaFiscalConsumidorEletronicaVO();
    }

    private NotaFiscalConsumidorEletronicaVO consultarPorMovConta(Integer movConta, int nivelMontarDados) throws Exception {
        String sql = "SELECT * FROM notafiscalconsumidoreletronica WHERE movconta = " + movConta;
        Statement stm = con.createStatement();
        ResultSet tabelaResultado = stm.executeQuery(sql);
        if (tabelaResultado.next()) {
            return montarDados(tabelaResultado, nivelMontarDados, con);
        }
        return new NotaFiscalConsumidorEletronicaVO();
    }

    public static NotaFiscalConsumidorEletronicaVO montarDados(ResultSet dadosSQL, int nivelMontarDados, Connection con) throws Exception {
        NotaFiscalConsumidorEletronicaVO obj = new NotaFiscalConsumidorEletronicaVO();
        obj.setCodigo(dadosSQL.getInt("codigo"));
        obj.setId_NFCe(dadosSQL.getInt("id_nfce"));
        obj.setDataRegistro(dadosSQL.getDate("dataregistro"));
        obj.getReciboPagamento().setCodigo(dadosSQL.getInt("recibopagamento"));
        obj.getUsuario().setCodigo(dadosSQL.getInt("usuario"));
        obj.setValorTotal(dadosSQL.getDouble("valortotal"));
        obj.getMovConta().setCodigo(dadosSQL.getInt("movconta"));
        obj.setIdReferencia(dadosSQL.getString("idreferencia"));
        obj.getPessoaVO().setCodigo(dadosSQL.getInt("pessoa"));
        obj.setJsonEnviar(dadosSQL.getString("jsonEnviar"));
        obj.setEnotas(dadosSQL.getBoolean("enotas"));
        obj.setSituacaoNotaFiscal(SituacaoNotaFiscalEnum.obterPorCodigo(dadosSQL.getInt("situacaoNotaFiscal")));
        obj.setDataEnvio(dadosSQL.getTimestamp("dataEnvio"));
        obj.setResultadoEnvio(dadosSQL.getString("resultadoEnvio"));
        obj.getConfiguracaoNotaFiscalVO().setCodigo(dadosSQL.getInt("configuracaonotafiscal"));

        if (nivelMontarDados == Uteis.NIVELMONTARDADOS_DADOSBASICOS) {
            return obj;
        }

        obj.setItensNota(consultarItens(obj.getCodigo(), nivelMontarDados, con));
        obj.setFormasPagamentoNota(consultarFormaPagamento(obj.getCodigo(), nivelMontarDados, con));

        obj.setNovoObj(false);
        return obj;
    }

    private static NotaFiscalConsumidorEletronicaItensVO montarDadosItens(ResultSet dadosSQL, int nivelMontarDados, Connection con) throws Exception {
        NotaFiscalConsumidorEletronicaItensVO obj = new NotaFiscalConsumidorEletronicaItensVO();
        obj.setCodigo(dadosSQL.getInt("codigo"));
        obj.getNotaFiscalConsumidorEletronica().setCodigo(dadosSQL.getInt("notafiscalconsumidoreletronica"));
        obj.getProduto().setCodigo(dadosSQL.getInt("produto"));
        obj.setDescricao(dadosSQL.getString("descricao"));
        obj.setValorUnitario(dadosSQL.getDouble("valorunitario"));
        obj.setQuantidade(dadosSQL.getInt("quantidade"));
        obj.setValorDescontoOuAcrescimo(dadosSQL.getDouble("valordescontoouacrescimo"));
        obj.setValorTotal(dadosSQL.getDouble("valortotal"));
        obj.setNovoObj(false);
        return obj;
    }

    private static NotaFiscalConsumidorEletronicaFormasPagamentoVO montarDadosFormasPagamento(ResultSet dadosSQL, int nivelMontarDados, Connection con) throws Exception {
        NotaFiscalConsumidorEletronicaFormasPagamentoVO obj = new NotaFiscalConsumidorEletronicaFormasPagamentoVO();
        obj.setCodigo(dadosSQL.getInt("codigo"));
        obj.getNotaFiscalConsumidorEletronica().setCodigo(dadosSQL.getInt("notafiscalconsumidoreletronica"));
        obj.getFormaPagamento().setCodigo(dadosSQL.getInt("formapagamento"));
        obj.setValor(dadosSQL.getDouble("valor"));
        obj.setNovoObj(false);
        return obj;
    }

    private static List<NotaFiscalConsumidorEletronicaItensVO> consultarItens(Integer codigoNotaFiscal, int nivelMontarDados, Connection con) throws Exception {
        List<NotaFiscalConsumidorEletronicaItensVO> retorno = new ArrayList<NotaFiscalConsumidorEletronicaItensVO>();
        String sql = "SELECT * FROM notafiscalconsumidoreletronicaitens WHERE notafiscalconsumidoreletronica = " + codigoNotaFiscal;
        Statement stm = con.createStatement();
        ResultSet tabelaResultado = stm.executeQuery(sql);
        while (tabelaResultado.next()) {
            NotaFiscalConsumidorEletronicaItensVO novoObj = montarDadosItens(tabelaResultado, nivelMontarDados, con);
            retorno.add(novoObj);
        }
        return retorno;
    }

    private static List<NotaFiscalConsumidorEletronicaFormasPagamentoVO> consultarFormaPagamento(Integer codigoNotaFiscal, int nivelMontarDados, Connection con) throws Exception {
        List<NotaFiscalConsumidorEletronicaFormasPagamentoVO> retorno = new ArrayList<NotaFiscalConsumidorEletronicaFormasPagamentoVO>();
        String sql = "SELECT * FROM notafiscalconsumidoreletronicaformaspagamento WHERE notafiscalconsumidoreletronica = " + codigoNotaFiscal;
        Statement stm = con.createStatement();
        ResultSet tabelaResultado = stm.executeQuery(sql);
        while (tabelaResultado.next()) {
            NotaFiscalConsumidorEletronicaFormasPagamentoVO novoObj = montarDadosFormasPagamento(tabelaResultado, nivelMontarDados, con);
            retorno.add(novoObj);
        }
        return retorno;
    }

    public String montarDescricaoExcluirNotaFiscal(NotaFiscalConsumidorEletronicaVO notaFiscalConsumidorEletronicaVO) throws Exception {
        StringBuilder descricaoNota = new StringBuilder();
        descricaoNota.append("Código NFCeEmitida: ").append(notaFiscalConsumidorEletronicaVO.getCodigo());
        descricaoNota.append("<br>Id_NFCe: ").append(notaFiscalConsumidorEletronicaVO.getId_NFCe());
        descricaoNota.append("<br>Recibo: ").append(notaFiscalConsumidorEletronicaVO.getReciboPagamento().getCodigo());
        descricaoNota.append("<br>Valor: ").append(Formatador.formatarValorMonetario(notaFiscalConsumidorEletronicaVO.getValorTotal()));

        //INFORMAÇÕES DO FORMA PAGAMENTO
        descricaoNota.append("<br>FORMA DE PAGAMENTO: ");
        for (NotaFiscalConsumidorEletronicaFormasPagamentoVO forma : notaFiscalConsumidorEletronicaVO.getFormasPagamentoNota()) {
            descricaoNota.append("<br><br>Forma_Pagamento: ").append(forma.getFormaPagamento().getCodigo());
            descricaoNota.append("<br>Valor: ").append(Formatador.formatarValorMonetario(forma.getValor()));
        }

        //INFORMAÇÕES DO ITENS
        descricaoNota.append("<br><br>ITENS: ");
        for (NotaFiscalConsumidorEletronicaItensVO item : notaFiscalConsumidorEletronicaVO.getItensNota()) {
            descricaoNota.append("<br><br>Produto: ").append(item.getProduto().getCodigo());
            descricaoNota.append("<br>Descrição: ").append(item.getDescricao());
            descricaoNota.append("<br>Valor Unitário: ").append(Formatador.formatarValorMonetario(item.getValorUnitario()));
            descricaoNota.append("<br>Quantidade: ").append(item.getQuantidade());
            descricaoNota.append("<br>Desconto: ").append(Formatador.formatarValorMonetario(item.getValorDescontoOuAcrescimo()));
            descricaoNota.append("<br>Valor Total: ").append(Formatador.formatarValorMonetario(item.getValorTotal()));
        }
        return descricaoNota.toString();
    }

    public void excluirComLog(NotaFiscalConsumidorEletronicaVO obj, UsuarioVO usuarioLogado, ProcessoAjusteGeralEnum processoAjusteGeralEnum) throws SQLException {
        try {
            con.setAutoCommit(false);
            excluir(obj);
            LogAjusteGeral logAjusteGeralDAO = new LogAjusteGeral(con);
            logAjusteGeralDAO.incluir(Calendario.hoje(), usuarioLogado.getNome(), usuarioLogado.getUserOamd(), processoAjusteGeralEnum, montarDescricaoExcluirNotaFiscal(obj));
            logAjusteGeralDAO = null;
            con.setAutoCommit(true);
        } catch (Exception ex) {
            con.rollback();
            con.setAutoCommit(true);
        }
    }

    public void excluirComLogSemCommit(NotaFiscalConsumidorEletronicaVO obj, UsuarioVO usuarioLogado, ProcessoAjusteGeralEnum processoAjusteGeralEnum) throws Exception {
        excluir(obj);
        LogAjusteGeral logAjusteGeralDAO = new LogAjusteGeral(con);
        logAjusteGeralDAO.incluir(Calendario.hoje(), usuarioLogado.getNome(), usuarioLogado.getUserOamd(), processoAjusteGeralEnum, montarDescricaoExcluirNotaFiscal(obj));
        logAjusteGeralDAO = null;
    }

    public void excluir(NotaFiscalConsumidorEletronicaVO obj) throws Exception {
        String sql = "DELETE FROM notafiscalconsumidoreletronicaitens WHERE notafiscalconsumidoreletronica = " + obj.getCodigo() + "; \n";
        sql += "DELETE FROM notafiscalconsumidoreletronicaformaspagamento WHERE notafiscalconsumidoreletronica = " + obj.getCodigo() + ";\n";
        sql += "DELETE FROM notafiscalconsumidoreletronica WHERE codigo = " + obj.getCodigo() + ";";
        PreparedStatement sqlExcluir = con.prepareStatement(sql);
        sqlExcluir.execute();
    }
    public String consultarPorPessoaParaTela(Integer codigoPessoa, Integer limit) throws Exception {
        StringBuilder sql = new StringBuilder();
        sql.append("select array_to_string(array(select \n");
        sql.append("n.id_nfce \n");
        sql.append("From notafiscalconsumidoreletronica n \n");
        sql.append("inner join recibopagamento r on r.codigo = n.recibopagamento\n");
        sql.append("where r.pessoapagador = ").append(codigoPessoa);
        sql.append(" order by n.id_nfce desc ");
        if (!UteisValidacao.emptyNumber(limit)) {
            sql.append(" limit ").append(limit);
        }
        sql.append("), ',', '') as nfce");
        Statement stm = con.createStatement();
        ResultSet rs = stm.executeQuery(sql.toString());
        if (rs.next()) {
            return rs.getString("nfce");
        } else {
            return "";
        }
    }

    private String executaRequisicaoRestNFCe(String metodo, String json) throws IOException {
        ExecuteRequestHttpService req = new ExecuteRequestHttpService();
        Map<String, String> header = new HashMap<String, String>();
        header.put("Content-Type", "application/json");
        return req.executeHttpRequestNFSe(Uteis.getUrlServiceNFSeRest() + metodo, header, json, ExecuteRequestHttpService.METODO_POST, "UTF-8", "UTF-8");
    }

    public RetornoEnvioNotaFiscalTO reenviarNFCe(JSONObject jsonObject, Integer id_nfce) {
        RetornoEnvioNotaFiscalTO retornoEnvioNotaFiscalTO = new RetornoEnvioNotaFiscalTO();
        try {
            Uteis.logar(null, "Solicitar reenviarNFCe ID_NFCE: " + id_nfce);
            String retorno = executaRequisicaoRestNFCe(METODO_REENVIARNFCE, jsonObject.toString());

            JSONObject obj = new JSONObject(retorno);
            retornoEnvioNotaFiscalTO.setMensagem(obj.getString("mensagemRetorno"));
            retornoEnvioNotaFiscalTO.setResultadoEnvio(ResultadoEnvioNFSeEnum.getTipo(obj.getInt("Result")));
        } catch (Exception e) {
            retornoEnvioNotaFiscalTO.setResultadoEnvio(ResultadoEnvioNFSeEnum.ERROINESPERADO);
            retornoEnvioNotaFiscalTO.setMensagem(e.getMessage());

        }
        return retornoEnvioNotaFiscalTO;
    }

    public RetornoEnvioNotaFiscalTO inutilizarNFCe(JSONObject jsonObject, Integer id_nfce) {
        RetornoEnvioNotaFiscalTO retornoEnvioNotaFiscalTO = new RetornoEnvioNotaFiscalTO();
        try {
            Uteis.logar(null, "Solicitar inutilizarNFCe ID_NFCE: " + id_nfce);
            String retorno = executaRequisicaoRestNFCe(METODO_INUTILIZARNUMEROSNFCE, jsonObject.toString());

            JSONObject obj = new JSONObject(retorno);
            retornoEnvioNotaFiscalTO.setMensagem(obj.getString("mensagemRetorno"));
            retornoEnvioNotaFiscalTO.setResultadoEnvio(ResultadoEnvioNFSeEnum.getTipo(obj.getInt("Result")));
        } catch (Exception e) {
            retornoEnvioNotaFiscalTO.setResultadoEnvio(ResultadoEnvioNFSeEnum.ERROINESPERADO);
            retornoEnvioNotaFiscalTO.setMensagem(e.getMessage());

        }
        return retornoEnvioNotaFiscalTO;
    }

    public NotaFiscalConsumidorEletronicaVO consultaPorMovPagamento(Integer movPagamento, int nivelMontarDados) throws Exception {
        String sql = "SELECT * FROM notafiscalconsumidoreletronica WHERE movPagamento = " + movPagamento;
        Statement stm = con.createStatement();
        ResultSet tabelaResultado = stm.executeQuery(sql);
        if (tabelaResultado.next()) {
            return montarDados(tabelaResultado, nivelMontarDados, con);
        }
        return new NotaFiscalConsumidorEletronicaVO();
    }

    public List<NotaFiscalConsumidorEletronicaVO> consultaListaPorCheque(String composicaoCheque) throws Exception {
        String sql = "SELECT * FROM notafiscalconsumidoreletronica WHERE cheque in (" + composicaoCheque + ")";
        Statement stm = con.createStatement();
        ResultSet tabelaResultado = stm.executeQuery(sql);
        return montarDadosConsulta(tabelaResultado, Uteis.NIVELMONTARDADOS_DADOSBASICOS, con);
    }

    public List<NotaFiscalConsumidorEletronicaVO> consultaListaPorCartao(String composicaoCartao) throws Exception {
        String sql = "SELECT * FROM notafiscalconsumidoreletronica WHERE cartaocredito in (" + composicaoCartao + ")";
        Statement stm = con.createStatement();
        ResultSet tabelaResultado = stm.executeQuery(sql);
        return montarDadosConsulta(tabelaResultado, Uteis.NIVELMONTARDADOS_DADOSBASICOS, con);
    }

    public List<NotaFiscalConsumidorEletronicaVO> montarDadosConsulta(ResultSet tabelaResultado, int nivelMontarDados, Connection con) throws Exception {
        List<NotaFiscalConsumidorEletronicaVO> notas = new ArrayList<NotaFiscalConsumidorEletronicaVO>();
        while (tabelaResultado.next()) {
            NotaFiscalConsumidorEletronicaVO nota = montarDados(tabelaResultado, nivelMontarDados, con);
            notas.add(nota);
        }
        return notas;
    }

    @Override
    public boolean isCertificadoVencido(String cnpj) throws Exception {
        String cnpjCorrigido = StringUtils.isBlank(cnpj) ? "" : cnpj.replaceAll("[^0-9]", "");
        if (StringUtils.isBlank(cnpjCorrigido)) {
            Uteis.logar("CNPJ vazio!");
            return true;
        }

        final String retorno = requisitarNFSeRest(Uteis.getUrlServiceNFSeRest() + METODO_GETDATACERTIFICADO + "?CPFCNPJ=" + cnpjCorrigido);
        try {
            JSONObject jsonResposta = new JSONObject(retorno);
            return jsonResposta.getBoolean(PARAMETRO_CERT_VENC_REQUER_CERT)
                    && FORMATADOR_DATA.parse(jsonResposta.getString(PARAMETRO_CERT_VENC_DATA)).before(Calendario.hoje());
        } catch (Exception e) {
            Uteis.logar(e, NotaFiscalConsumidorEletronica.class);
            return true;
        }
    }

    @Override
    public List<NFeEmpresaDiretorioExecucaoPOJO> getExecutaveisDelphi() throws IOException {
        final String retorno = requisitarNFSeRest(Uteis.getUrlServiceNFSeRestAdmin() + METODO_GET_DIRETORIOS_EXECUCAO);
        final List<NFeEmpresaDiretorioExecucaoPOJO> executaveis = new ArrayList<NFeEmpresaDiretorioExecucaoPOJO>();

        try {
            final JSONObject jsonResposta = new JSONObject(retorno);
            final JSONArray clientesDelphi = jsonResposta.getJSONArray(PARAMETRO_CLIENTES);

            for (int i = 0; i < clientesDelphi.length() ; i++) {
                final JSONObject clienteDelphi = clientesDelphi.getJSONObject(i);
                executaveis.add(new NFeEmpresaDiretorioExecucaoPOJO(
                        clienteDelphi.getString(PARAMETRO_CLIENTES_IDENTIFICADOR),
                        getPastaExecutavel(clienteDelphi.getString(PARAMETRO_CLIENTES_NOME_EXECUTAVEL)),
                        clienteDelphi.getJSONArray(PARAMETRO_CLIENTES_EMPRESAS).length()));
            }
        } catch (Exception e) {
            Uteis.logar(e, NotaFiscalConsumidorEletronica.class);
        }

        return executaveis;
    }

    private String getPastaExecutavel(final String caminhoCompletoExecutavel) {
        if (StringUtils.isBlank(caminhoCompletoExecutavel)) {
            return "";
        }

        return caminhoCompletoExecutavel.split("\\\\")[2].replace("PactoNFSe_", "");
    }

    @Override
    public void adicionarEmpresa(String identificadorExecutavel, Integer idEmpresa) throws IOException {
        requisitarNFSeRest(Uteis.getUrlServiceNFSeRestAdmin() + METODO_GET_ADICIONAR_EMPRESA + "?Identificador=" + identificadorExecutavel + "&idEmpresa=" + idEmpresa);
    }

    public void removerEmpresa(String identificadorExecutavel, Integer idEmpresa) throws IOException {
        requisitarNFSeRest(Uteis.getUrlServiceNFSeRestAdmin() + METODO_GET_REMOVER_EMPRESA + "?Identificador=" + identificadorExecutavel + "&idEmpresa=" + idEmpresa);
    }

    @Override
    public void reiniciarExecutavelDelphi(String identificadorExecutavel) throws IOException {
        requisitarNFSeRest(Uteis.getUrlServiceNFSeRestAdmin() + METODO_GET_REINICIAR_EXECUTAVEL + "?identificador=" + identificadorExecutavel);
    }

    @Override
    public void reiniciarTodosExecutaveisDelphi() throws IOException {
        requisitarNFSeRest(Uteis.getUrlServiceNFSeRestAdmin() + METODO_GET_REINICIAR_TODOS_EXECUTAVEIS);
    }

    private String requisitarNFSeRest(final String url) throws IOException {
        final Map<String, String> header = new HashMap<String, String>();
        header.put("Content-Type", "application/json");
        return ExecuteRequestHttpService.executeHttpRequestNFSe(
                url,
                header,
                "",
                ExecuteRequestHttpService.METODO_GET,
                "UTF-8",
                "UTF-8");
    }

    public boolean existeNFCeIdReferencia(String idReferencia) throws Exception {
        return existe("SELECT codigo FROM notafiscalconsumidoreletronica WHERE idreferencia = '" + idReferencia + "'", con);
    }

    public void atualizarJsonEnviarIdReferencia(NotaFiscalConsumidorEletronicaVO notaFiscalConsumidorEletronicaVO) throws Exception {
        String sql = "UPDATE notafiscalconsumidoreletronica SET jsonEnviar  = ?, idreferencia  = ? WHERE codigo = ?";
        PreparedStatement sqlInserir = con.prepareStatement(sql);
        sqlInserir.setString(1, notaFiscalConsumidorEletronicaVO.getJsonEnviar());
        sqlInserir.setString(2, notaFiscalConsumidorEletronicaVO.getIdReferencia());
        sqlInserir.setInt(3, notaFiscalConsumidorEletronicaVO.getCodigo());
        sqlInserir.execute();
    }

    public List<NotaFiscalConsumidorEletronicaVO> consultarNotasAguardandoEnvio() throws Exception {
        String sql = "SELECT * FROM notafiscalconsumidoreletronica WHERE enotas = false and situacaoNotaFiscal = " + SituacaoNotaFiscalEnum.GERADA.getCodigo()+ " order by codigo ";
        Statement stm = con.createStatement();
        ResultSet tabelaResultado = stm.executeQuery(sql);
        List<NotaFiscalConsumidorEletronicaVO> notasEmitidas = new ArrayList<NotaFiscalConsumidorEletronicaVO>();
        while (tabelaResultado.next()) {
            notasEmitidas.add(montarDados(tabelaResultado, Uteis.NIVELMONTARDADOS_DADOSBASICOS, con));
        }
        return notasEmitidas;
    }

    public List<NotaFiscalConsumidorEletronicaVO> consultarNotasAguardandoEnvioEnotas() throws Exception {
        String sql = "SELECT * FROM notafiscalconsumidoreletronica WHERE enotas = true and situacaoNotaFiscal = " + SituacaoNotaFiscalEnum.GERADA.getCodigo() + " order by codigo ";
        Statement stm = con.createStatement();
        ResultSet tabelaResultado = stm.executeQuery(sql);
        List<NotaFiscalConsumidorEletronicaVO> notasEmitidas = new ArrayList<NotaFiscalConsumidorEletronicaVO>();
        while (tabelaResultado.next()) {
            notasEmitidas.add(montarDados(tabelaResultado, Uteis.NIVELMONTARDADOS_DADOSBASICOS, con));
        }
        return notasEmitidas;
    }

    public void atualizarDataEnvioSituacaoResultadoEnvio(NotaFiscalConsumidorEletronicaVO obj) throws Exception {
        String sql = "UPDATE notafiscalconsumidoreletronica SET dataenvio = ?, situacaoNotaFiscal  = ?, resultadoEnvio = ? WHERE codigo = ?";
        PreparedStatement sqlInserir = con.prepareStatement(sql);
        sqlInserir.setTimestamp(1, Uteis.getDataJDBCTimestamp(obj.getDataEnvio()));
        sqlInserir.setInt(2, obj.getSituacaoNotaFiscal().getCodigo());
        sqlInserir.setString(3, obj.getResultadoEnvio());
        sqlInserir.execute();
    }


    public void atualizarSituacao(SituacaoNotaFiscalEnum situacaoNotaFiscal, Integer codNotaFiscalConsumidor) throws Exception {
        String sql = "UPDATE notafiscalconsumidoreletronica SET situacaoNotaFiscal = ? WHERE codigo  = ?";
        PreparedStatement sqlInserir = con.prepareStatement(sql);
        sqlInserir.setInt(1, situacaoNotaFiscal.getCodigo());
        sqlInserir.setInt(2, codNotaFiscalConsumidor);
        sqlInserir.execute();
    }

    public void excluirComLogEnotas(Integer codNotaFiscal, NotaFiscalConsumidorEletronicaVO obj,
                                    UsuarioVO usuarioVO) throws SQLException {
        try {
            con.setAutoCommit(false);

            excluirComLogEnotasSemCommit(codNotaFiscal, obj, usuarioVO);

            con.commit();
        } catch (Exception ex) {
            con.rollback();
            con.setAutoCommit(true);
        } finally {
            con.setAutoCommit(true);
        }
    }

    public void excluirComLogEnotasSemCommit(Integer codNotaFiscal, NotaFiscalConsumidorEletronicaVO obj,
                                             UsuarioVO usuarioVO) throws Exception {
        excluirComLogSemCommit(obj, usuarioVO, ProcessoAjusteGeralEnum.EXCLUIR_NOTA_FISCAL_ENOTAS);

        NotaFiscal notaDAO = new NotaFiscal(con);
        notaDAO.marcarExcluido(true, codNotaFiscal, usuarioVO);
        notaDAO = null;
    }
}

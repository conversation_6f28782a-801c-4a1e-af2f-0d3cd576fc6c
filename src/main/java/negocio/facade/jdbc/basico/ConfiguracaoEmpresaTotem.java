package negocio.facade.jdbc.basico;

import negocio.comuns.ConfiguracaoEmpresaTotemVO;
import negocio.comuns.TotemTO;
import negocio.comuns.basico.enumerador.ConfigTotemEnum;
import negocio.comuns.utilitarias.Ordenacao;
import negocio.comuns.utilitarias.Uteis;
import negocio.facade.jdbc.arquitetura.SuperEntidade;
import negocio.interfaces.basico.ConfiguracaoEmpresaTotemInterfaceFacade;
import negocio.interfaces.basico.EmpresaInterfaceFacade;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class ConfiguracaoEmpresaTotem extends SuperEntidade implements ConfiguracaoEmpresaTotemInterfaceFacade {


    public ConfiguracaoEmpresaTotem() throws Exception {
        super();
    }

    public ConfiguracaoEmpresaTotem(Connection conexao) throws Exception {
        super(conexao);
    }

    @Override
    public ConfiguracaoEmpresaTotemVO novo() throws Exception {
        incluir(getIdEntidade());
        return new ConfiguracaoEmpresaTotemVO();
    }

    @Override
    public void incluir(ConfiguracaoEmpresaTotemVO obj) throws Exception {
        String insert = "insert into ConfiguracaoEmpresaTotem (configtotem, empresa, valor, totem) values (?, ?, ?, ?)";
        PreparedStatement stm = con.prepareStatement(insert);
        stm.setInt(1 , obj.getConfigTotem().ordinal());
        stm.setInt(2 , obj.getEmpresa());
        stm.setString(3, obj.getValor());
        stm.setString(4, obj.getTotem() == null ? "TOTEM" : obj.getTotem().toUpperCase());
        stm.execute();
    }


    @Override
    public void excluir(Integer empresa) throws Exception {
        PreparedStatement stm = con.prepareStatement("DELETE FROM ConfiguracaoEmpresaTotem where empresa = ? ");
        stm.setInt(1 , empresa);
        stm.execute();
    }

    public String obterValorConfigs(Integer empresa, String totem, ConfigTotemEnum configTotemEnum) throws Exception {
        ResultSet rs = criarConsulta("SELECT valor FROM ConfiguracaoEmpresaTotem where empresa = " + empresa + " and totem ilike '" +totem+ "' and configtotem = " + configTotemEnum.ordinal(), con);
        if (rs.next()){
            return rs.getString("valor");
        }
        return null;
    }

    public List<TotemTO> obterConfigs(Integer empresa, String key) throws Exception{
        List<TotemTO> totens = new ArrayList<TotemTO>();
        Map<String, Map<ConfigTotemEnum, ConfiguracaoEmpresaTotemVO>> cfgs = new HashMap<String, Map<ConfigTotemEnum, ConfiguracaoEmpresaTotemVO>>();
        ResultSet rs = criarConsulta("SELECT * FROM ConfiguracaoEmpresaTotem where empresa = " + empresa, con);
        while(rs.next()){
            String totem = rs.getString("totem");
            Map<ConfigTotemEnum, ConfiguracaoEmpresaTotemVO> map = cfgs.get(totem);
            if(map == null){
                map = new HashMap<ConfigTotemEnum, ConfiguracaoEmpresaTotemVO>();
                cfgs.put(totem, map);
            }
            ConfiguracaoEmpresaTotemVO cfg = new ConfiguracaoEmpresaTotemVO();
            cfg.setEmpresa(rs.getInt("empresa"));
            cfg.setValor(rs.getString("valor"));
            cfg.setTotem(totem);
            ConfigTotemEnum configtotem = ConfigTotemEnum.obter(rs.getInt("configtotem"));
            cfg.setConfigTotem(configtotem);
            map.put(configtotem, cfg);
        }
        for(String totem : cfgs.keySet()){
            TotemTO tt = new TotemTO();
            tt.setTotem(totem);
            tt.setKey(key);
            tt.setEmpresa(empresa);
            tt.setConfigs(cfgs.get(totem));
            for(ConfigTotemEnum cfg : ConfigTotemEnum.values()){
                ConfiguracaoEmpresaTotemVO totemVO = tt.getConfigs().get(cfg);
                if(totemVO == null){
                    ConfiguracaoEmpresaTotemVO cfgvo = new ConfiguracaoEmpresaTotemVO();
                    cfgvo.setEmpresa(empresa);
                    cfgvo.setValor(cfg.getPadrao());
                    cfgvo.setConfigTotem(cfg);
                    tt.getConfigs().put(cfg, cfgvo);
                }
            }
            totens.add(tt);
        }

        return Ordenacao.ordenarLista(totens, "totem");
    }

    public void gravar(List<TotemTO> totens, Integer empresa) throws Exception{
        try {
            con.setAutoCommit(false);
            excluir(empresa);
            for(TotemTO totem : totens){
                for(ConfigTotemEnum ct : ConfigTotemEnum.values()){
                    ConfiguracaoEmpresaTotemVO cfg = totem.getConfigs().get(ct);
                    cfg.setEmpresa(empresa);
                    cfg.setTotem(Uteis.retirarAcentuacaoRegex(totem.getTotem().toUpperCase()));
                    incluir(cfg);
                }
            }
            con.commit();
        } catch (Exception e) {
            con.rollback();
            throw e;
        } finally {
            con.setAutoCommit(true);
        }
    }
}

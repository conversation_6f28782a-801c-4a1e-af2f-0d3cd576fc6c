package negocio.facade.jdbc.basico;

import java.sql.Connection;
import negocio.interfaces.basico.*;
import negocio.comuns.basico.CategoriaVO;
import negocio.facade.jdbc.arquitetura.*;
import java.sql.ResultSet;
import java.sql.PreparedStatement;
import java.sql.SQLException;
import java.sql.Statement;
import java.util.HashMap;
import java.util.List;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Map;

import negocio.comuns.financeiro.MovParcelaVO;
import negocio.comuns.utilitarias.*;

/**
 * Classe de persistência que encapsula todas as operações de manipulação dos dados da classe <code>CategoriaVO</code>.
 * Responsável por implementar operações como incluir, alterar, excluir e consultar pertinentes a classe <code>CategoriaVO</code>.
 * Encapsula toda a interação com o banco de dados.
 * @see CategoriaVO
 * @see SuperEntidade
 */
public class Categoria extends SuperEntidade implements CategoriaInterfaceFacade {

    public Categoria() throws Exception {
        super();
    }

    public Categoria(Connection conexao) throws Exception {
        super(conexao);
    }

    /**
     * Operação responsável por retornar um novo objeto da classe <code>CategoriaVO</code>.
     */
    public CategoriaVO novo() throws Exception {
        CategoriaVO obj = new CategoriaVO();
        incluir(getIdEntidade());
        return obj;
    }

    /**
     * Operação responsável por incluir no banco de dados um objeto da classe <code>CategoriaVO</code>.
     * Primeiramente valida os dados (<code>validarDados</code>) do objeto. Verifica a conexão com o banco de dados e a permissão do usuário
     * para realizar esta operacão na entidade.
     * Isto, através da operação <code>incluir</code> da superclasse.
     * @param obj  Objeto da classe <code>CategoriaVO</code> que será gravado no banco de dados.
     * @exception Exception Caso haja problemas de conexão, restrição de acesso ou validação de dados.
     */
    public void incluir(CategoriaVO obj, boolean centralEventos) throws Exception {

        CategoriaVO.validarDados(obj);
        if (centralEventos) {
//            	incluirObj(getIdEntidade());
        } else {
            incluir(getIdEntidade());
        }
        obj.realizarUpperCaseDados();
        String sql = "INSERT INTO Categoria( nome, tipoCategoria, nrConvitePermitido, nomeexterno ) VALUES ( ?, ?, ?, ? )";
        try (PreparedStatement sqlInserir = con.prepareStatement(sql)) {
            sqlInserir.setString(1, obj.getNome());
            sqlInserir.setString(2, obj.getTipoCategoria());
            sqlInserir.setInt(3, obj.getNrConvitePermitido());
            sqlInserir.setString(4, obj.getNomeExterno());
            sqlInserir.execute();
        }
        obj.setCodigo(obterValorChavePrimariaCodigo());
        obj.setNovoObj(Boolean.FALSE);
    }

    /* (non-Javadoc)
     * @see negocio.interfaces.basico.CategoriaInterfaceFacade#incluir(negocio.comuns.basico.CategoriaVO)
     */
    public void incluir(CategoriaVO obj) throws Exception {
        incluir(obj, false);
    }

    /**
     * Operação responsável por alterar no BD os dados de um objeto da classe <code>CategoriaVO</code>.
     * Sempre utiliza a chave primária da classe como atributo para localização do registro a ser alterado.
     * Primeiramente valida os dados (<code>validarDados</code>) do objeto. Verifica a conexão com o banco de dados e a permissão do usuário
     * para realizar esta operacão na entidade.
     * Isto, através da operação <code>alterar</code> da superclasse.
     * @param obj    Objeto da classe <code>CategoriaVO</code> que será alterada no banco de dados.
     * @exception Exception Caso haja problemas de conexão, restrição de acesso ou validação de dados.
     */
    public void alterar(CategoriaVO obj, boolean centralEventos) throws Exception {
        try {
            con.setAutoCommit(false);
            alterarSemCommit(obj);
            con.commit();
        } catch (Exception e) {
            con.rollback();
            con.setAutoCommit(true);
            throw e;
        } finally {
            con.setAutoCommit(true);
            }
    }

    public void alterarSemCommit(CategoriaVO obj) throws Exception{
        CategoriaVO.validarDados(obj);
            obj.realizarUpperCaseDados();
            String sql = "UPDATE Categoria set nome=?, tipoCategoria=?, nrConvitePermitido=?, nomeexterno = ? WHERE ((codigo = ?))";
        try (PreparedStatement sqlAlterar = con.prepareStatement(sql)) {
            sqlAlterar.setString(1, obj.getNome());
            sqlAlterar.setString(2, obj.getTipoCategoria());
            sqlAlterar.setInt(3, obj.getNrConvitePermitido());
            sqlAlterar.setString(4, obj.getNomeExterno());
            sqlAlterar.setInt(5, obj.getCodigo());
            sqlAlterar.execute();
        }
    }

    /* (non-Javadoc)
     * @see negocio.interfaces.basico.CategoriaInterfaceFacade#alterar(negocio.comuns.basico.CategoriaVO)
     */
    public void alterar(CategoriaVO obj) throws Exception {
        this.alterar(obj, false);
    }

    /**
     * Operação responsável por excluir no BD um objeto da classe <code>CategoriaVO</code>.
     * Sempre localiza o registro a ser excluído através da chave primária da entidade.
     * Primeiramente verifica a conexão com o banco de dados e a permissão do usuário
     * para realizar esta operacão na entidade.
     * Isto, através da operação <code>excluir</code> da superclasse.
     * @param obj    Objeto da classe <code>CategoriaVO</code> que será removido no banco de dados.
     * @exception Exception Caso haja problemas de conexão ou restrição de acesso.
     */
    public void excluir(CategoriaVO obj, boolean centralEventos) throws Exception {
        try {
            con.setAutoCommit(false);
            if (centralEventos) {
//            	excluirObj(getIdEntidade());
            } else {
                excluir(getIdEntidade());
            }
            String sql = "DELETE FROM Categoria WHERE ((codigo = ?))";
            try (PreparedStatement sqlExcluir = con.prepareStatement(sql)) {
                sqlExcluir.setInt(1, obj.getCodigo().intValue());
                sqlExcluir.execute();
            }
            con.commit();
        } catch (Exception e) {
            con.rollback();
            con.setAutoCommit(true);
            throw e;
        } finally {
            con.setAutoCommit(true);
        }
    }

    /* (non-Javadoc)
     * @see negocio.interfaces.basico.CategoriaInterfaceFacade#excluir(negocio.comuns.basico.CategoriaVO)
     */
    public void excluir(CategoriaVO obj) throws Exception {
        this.excluir(obj, false);

    }

    /**
     * Responsável por realizar uma consulta de <code>Categoria</code> através do valor do atributo 
     * <code>Integer nrConvitePermitido</code>. Retorna os objetos com valores iguais ou superiores ao parâmetro fornecido.
     * Faz uso da operação <code>montarDadosConsulta</code> que realiza o trabalho de prerarar o List resultante.
     * @param   controlarAcesso Indica se a aplicação deverá verificar se o usuário possui permissão para esta consulta ou não.
     * @return  List Contendo vários objetos da classe <code>CategoriaVO</code> resultantes da consulta.
     * @exception Exception Caso haja problemas de conexão ou restrição de acesso.
     */
    public List consultarPorNrConvitePermitido(Integer valorConsulta, boolean controlarAcesso, int nivelMontarDados) throws Exception {
        consultar(getIdEntidade(), controlarAcesso);
        String sqlStr = "SELECT * FROM Categoria WHERE nrConvitePermitido >= " + valorConsulta.intValue() + " ORDER BY nrConvitePermitido";
        try (Statement stm = con.createStatement()) {
            try (ResultSet tabelaResultado = stm.executeQuery(sqlStr)) {
                return (montarDadosConsulta(tabelaResultado, nivelMontarDados));
            }
        }
    }

    /**
     * Responsável por realizar uma consulta de <code>Categoria</code> através do valor do atributo 
     * <code>String tipoCategoria</code>. Retorna os objetos, com início do valor do atributo idêntico ao parâmetro fornecido.
     * Faz uso da operação <code>montarDadosConsulta</code> que realiza o trabalho de prerarar o List resultante.
     * @param   controlarAcesso Indica se a aplicação deverá verificar se o usuário possui permissão para esta consulta ou não.
     * @return  List Contendo vários objetos da classe <code>CategoriaVO</code> resultantes da consulta.
     * @exception Exception Caso haja problemas de conexão ou restrição de acesso.
     */
    public List consultarPorTipoCategoria(String valorConsulta, boolean controlarAcesso, int nivelMontarDados) throws Exception {
        consultar(getIdEntidade(), controlarAcesso);
        String sqlStr = "SELECT * FROM Categoria WHERE upper( tipoCategoria ) like('" + valorConsulta.toUpperCase() + "%') ORDER BY tipoCategoria";
        try (Statement stm = con.createStatement()) {
            try (ResultSet tabelaResultado = stm.executeQuery(sqlStr)) {
                return (montarDadosConsulta(tabelaResultado, nivelMontarDados));
            }
        }
    }

    /**
     * Responsável por realizar uma consulta de <code>Categoria</code> através do valor do atributo 
     * <code>String nome</code>. Retorna os objetos, com início do valor do atributo idêntico ao parâmetro fornecido.
     * Faz uso da operação <code>montarDadosConsulta</code> que realiza o trabalho de prerarar o List resultante.
     * @param   controlarAcesso Indica se a aplicação deverá verificar se o usuário possui permissão para esta consulta ou não.
     * @return  List Contendo vários objetos da classe <code>CategoriaVO</code> resultantes da consulta.
     * @exception Exception Caso haja problemas de conexão ou restrição de acesso.
     */
    public List<CategoriaVO> consultarPorNome(String valorConsulta, boolean controlarAcesso, int nivelMontarDados) throws Exception {
        consultar(getIdEntidade(), controlarAcesso);
        String sqlStr = "SELECT * FROM Categoria WHERE upper( nome ) like('" + valorConsulta.toUpperCase() + "%') ORDER BY nome";
        try (Statement stm = con.createStatement()) {
            try (ResultSet tabelaResultado = stm.executeQuery(sqlStr)) {
                return (montarDadosConsulta(tabelaResultado, nivelMontarDados));
            }
        }
    }

    /**
     * Responsável por realizar uma consulta de <code>Categoria</code> através do valor do atributo 
     * <code>Integer codigo</code>. Retorna os objetos com valores iguais ou superiores ao parâmetro fornecido.
     * Faz uso da operação <code>montarDadosConsulta</code> que realiza o trabalho de prerarar o List resultante.
     * @param   controlarAcesso Indica se a aplicação deverá verificar se o usuário possui permissão para esta consulta ou não.
     * @return  List Contendo vários objetos da classe <code>CategoriaVO</code> resultantes da consulta.
     * @exception Exception Caso haja problemas de conexão ou restrição de acesso.
     */
    public List consultarPorCodigo(Integer valorConsulta, boolean controlarAcesso, int nivelMontarDados) throws Exception {
        consultar(getIdEntidade(), controlarAcesso);
        String sqlStr = "SELECT * FROM Categoria WHERE codigo >= " + valorConsulta.intValue() + " ORDER BY codigo";
        try (Statement stm = con.createStatement()) {
            try (ResultSet tabelaResultado = stm.executeQuery(sqlStr)) {
                return (montarDadosConsulta(tabelaResultado, nivelMontarDados));
            }
        }
    }

    /**
     * Responsável por montar os dados de vários objetos, resultantes de uma consulta ao banco de dados (<code>ResultSet</code>).
     * Faz uso da operação <code>montarDados</code> que realiza o trabalho para um objeto por vez.
     * @return  List Contendo vários objetos da classe <code>CategoriaVO</code> resultantes da consulta.
     */
    public static List<CategoriaVO> montarDadosConsulta(ResultSet tabelaResultado, int nivelMontarDados) throws Exception {
        List<CategoriaVO> vetResultado = new ArrayList<CategoriaVO>();
        while (tabelaResultado.next()) {
            CategoriaVO obj = montarDados(tabelaResultado, nivelMontarDados);
            vetResultado.add(obj);
        }
        return vetResultado;
    }

    public static CategoriaVO montarDadosBasico(ResultSet dadosSQL) throws Exception {
        CategoriaVO obj = new CategoriaVO();
        obj.setNovoObj(false);
        obj.setCodigo(dadosSQL.getInt("codigo"));
        obj.setNome(dadosSQL.getString("nome"));
        obj.setTipoCategoria(dadosSQL.getString("tipoCategoria"));
        obj.setNrConvitePermitido(dadosSQL.getInt("nrConvitePermitido"));
        obj.setNomeExterno(dadosSQL.getString("nomeexterno"));

        try{
            obj.setValidarSituacaoEmpresaSesi(dadosSQL.getBoolean("validarSituacaoEmpresaSesi"));
        }catch (Exception ex){}

        obj.setObrigatorioCnpjClienteSesi(dadosSQL.getBoolean("obrigatorioCnpjClienteSesi"));
        return obj;
    }

    /**
     * Responsável por montar os dados resultantes de uma consulta ao banco de dados (<code>ResultSet</code>)
     * em um objeto da classe <code>CategoriaVO</code>.
     * @return  O objeto da classe <code>CategoriaVO</code> com os dados devidamente montados.
     */
    public static CategoriaVO montarDados(ResultSet dadosSQL, int nivelMontarDados) throws Exception {
        CategoriaVO obj = montarDadosBasico(dadosSQL);

        if (nivelMontarDados == Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA) {
            return obj;
        }

        if (nivelMontarDados == Uteis.NIVELMONTARDADOS_DADOSBASICOS) {
            return obj;
        }
        return obj;
    }

    /**
     * Operação responsável por localizar um objeto da classe <code>CategoriaVO</code>
     * através de sua chave primária. 
     * @exception Exception Caso haja problemas de conexão ou localização do objeto procurado.
     */
    public CategoriaVO consultarPorChavePrimaria(Integer codigoPrm, int nivelMontarDados) throws Exception {
        consultar(getIdEntidade(), false);
        String sql = "SELECT * FROM Categoria WHERE codigo = ?";
        try (PreparedStatement sqlConsultar = con.prepareStatement(sql)) {
            sqlConsultar.setInt(1, codigoPrm.intValue());
            try (ResultSet tabelaResultado = sqlConsultar.executeQuery()) {
                if (!tabelaResultado.next()) {
                    throw new ConsistirException("Dados Não Encontrados ( Categoria ).");
                }
                return (montarDados(tabelaResultado, nivelMontarDados));
            }
        }
    }

    /*author   : Ulisses
     * Data    : 16/02/11
     * Objetivo: Consultar uma categoria pelo código.
     */
    public CategoriaVO consultarPorCodigo(Integer valorConsulta, int nivelMontarDados) throws Exception {
        CategoriaVO categoria = null;
        String sqlStr = "SELECT * FROM Categoria WHERE codigo = " + valorConsulta.intValue();
        try (Statement stm = con.createStatement()) {
            try (ResultSet resultDados = stm.executeQuery(sqlStr)) {
                if (resultDados.next()) {
                    categoria = montarDados(resultDados, nivelMontarDados);
                }
            }
        }

        return categoria;
    }
    
    public void montarCategoriasParcelas(List<MovParcelaVO> parcelas){
        try{
           String sql = " select nome from categoria\n" +
                        " where codigo = (SELECT categoria FROM cliente WHERE pessoa = %s LIMIT 1)"; 
           for(MovParcelaVO mp : parcelas){
               try (ResultSet rs = criarConsulta(String.format(sql, new Object[]{mp.getPessoa().getCodigo()}), con)) {
                   if (rs.next()) {
                       mp.setCategoriaAluno(new CategoriaVO(rs.getString("nome")));
                   }
               }
           }
        }catch(Exception e){
            // 
        }
    }

    public String consultarJSON() throws Exception {
        StringBuilder json;
        boolean dados = false;
        try (ResultSet rs = getPS().executeQuery()) {
            CategoriaVO cat = new CategoriaVO();
            json = new StringBuilder();
            json.append("{\"aaData\":[");
            dados = false;
            while (rs.next()) {
                dados = true;
                json.append("[\"").append(rs.getString("codigo")).append("\",");
                json.append("\"").append(Uteis.normalizarStringJSON(rs.getString("nome"))).append("\",");
                cat.setTipoCategoria(rs.getString("tipocategoria"));
                json.append("\"").append(cat.getTipoCategoria_Apresentar()).append("\"],");
            }
        }
        if (dados) {
            json.deleteCharAt(json.toString().length() - 1);
        }
        json.append("]}");
        return json.toString();
    }

    private PreparedStatement getPS() throws SQLException {
        String sql = "SELECT codigo, nome, tipocategoria FROM categoria ORDER BY nome;";
        return con.prepareStatement(sql);
    }

    public List consultarParaImpressao(String filtro, String ordem, String campoOrdenacao, int i) throws SQLException {
        List lista;
        try (ResultSet rs = getPS().executeQuery()) {
            lista = new ArrayList();

            while (rs.next()) {

                CategoriaVO cat = new CategoriaVO();
                String geral = rs.getString("codigo") + rs.getString("nome") + rs.getString("tipocategoria");
                if (geral.toLowerCase().contains(filtro.toLowerCase())) {
                    cat.setCodigo(rs.getInt("codigo"));
                    cat.setNome(rs.getString("nome"));
                    cat.setTipoCategoria(rs.getString("tipocategoria"));
                    lista.add(cat);
                }
            }
        }
        if (campoOrdenacao.equals("Código")) {
            Ordenacao.ordenarLista(lista, "codigo");
        } else if (campoOrdenacao.equals("Nome")) {
            Ordenacao.ordenarLista(lista, "pessoa_Apresentar");
        } else if (campoOrdenacao.equals("Tipo")) {
            Ordenacao.ordenarLista(lista, "tipoCategoria_Apresentar");
        }
        if (ordem.contains("desc")) {
            Collections.reverse(lista);
        }
        return lista;

    }
    
    public CategoriaVO criarOuConsultarCategoriaPorNome(final String nome, int nivelMontarDados) throws Exception {
        List lista = consultarPorNome(nome, false, nivelMontarDados);
        if (lista.isEmpty()) {
            CategoriaVO  categoriaVO = new CategoriaVO();
            categoriaVO.setNome(nome);
            categoriaVO.setTipoCategoria("AL");
            incluir(categoriaVO, true);
            return categoriaVO;
        } else {
            return (CategoriaVO) lista.get(0);
        }
    }

    public CategoriaVO consultarPorNomeExterno(final String nomeExterno, int nivelMontarDados) throws Exception {
        String sql = "SELECT * FROM categoria WHERE nomeExterno = ?";
        try (PreparedStatement sqlConsultar = con.prepareStatement(sql)) {
            sqlConsultar.setString(1, nomeExterno);
            try (ResultSet tabelaResultado = sqlConsultar.executeQuery()) {
                if (!tabelaResultado.next()) {
                    return new CategoriaVO();
                }
                return (montarDados(tabelaResultado, nivelMontarDados));
            }
        }
    }

    public Map<String, Integer> categoriasCadastradas() throws Exception {
        ResultSet resultSet = SuperFacadeJDBC.criarConsulta("SELECT nome, codigo FROM categoria", con);
        Map<String, Integer> todasCategorias = new HashMap<>();

        while(resultSet.next()) {
            todasCategorias.put(resultSet.getString("nome"), resultSet.getInt("codigo"));
        }

        return todasCategorias;
    }
}

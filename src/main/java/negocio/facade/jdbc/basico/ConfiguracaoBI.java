/*
 * To change this template, choose Too<PERSON> | Templates
 * and open the template in the editor.
 */
package negocio.facade.jdbc.basico;

import br.com.pactosolucoes.enumeradores.BIEnum;
import br.com.pactosolucoes.enumeradores.ConfiguracaoBIEnum;
import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.sql.Types;
import java.util.ArrayList;
import java.util.EnumMap;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import negocio.comuns.basico.ConfiguracaoBIVO;
import negocio.comuns.utilitarias.UteisValidacao;
import negocio.facade.jdbc.arquitetura.SuperEntidade;
import static negocio.facade.jdbc.arquitetura.SuperFacadeJDBC.executarConsulta;
import negocio.interfaces.basico.ConfiguracaoBIInterfaceFacade;

/**
 *
 * <AUTHOR>
 */
public class ConfiguracaoBI extends SuperEntidade implements ConfiguracaoBIInterfaceFacade{

    
    public ConfiguracaoBI() throws Exception{
        super();
    }
    
    public ConfiguracaoBI(Connection con) throws Exception{
        super(con);
    }
    
    @Override
    public List<ConfiguracaoBIVO> consultarPorBI(BIEnum bi, Integer empresa) throws Exception {
        PreparedStatement stm = con.prepareStatement("SELECT * FROM ConfiguracaoBI WHERE bi = ? AND empresa = ?");
        stm.setInt(1, bi.getCodigo());
        stm.setInt(2, empresa);

        List<ConfiguracaoBIVO> lista = montarDadosConsulta(stm.executeQuery());
        List<ConfiguracaoBIVO> listaNovos = new ArrayList<ConfiguracaoBIVO>();
        for(ConfiguracaoBIEnum cfg : ConfiguracaoBIEnum.values()){
            if(cfg.getBi() != null && cfg.getBi().equals(bi)){
                boolean naoExiste = true;
                for(ConfiguracaoBIVO cfgVo : lista){
                    if(cfgVo.getConfiguracao().equals(cfg)){
                        naoExiste = false;
                        break;
                    }
                }
                if(naoExiste){
                    ConfiguracaoBIVO cfgVo = new ConfiguracaoBIVO();
                    cfgVo.setBi(bi);
                    cfgVo.setConfiguracao(cfg);
                    cfgVo.setValor(cfg.getValorPadrao());
                    cfgVo.setEmpresa(empresa);
                    incluir(cfgVo);
                    listaNovos.add(cfgVo);
                }
            }
        }
        lista.addAll(listaNovos);
        return lista;
    }
    
    public List<ConfiguracaoBIVO> montarDadosConsulta(ResultSet rs ) throws SQLException{
        List<ConfiguracaoBIVO> lista = new ArrayList<ConfiguracaoBIVO>();
        while(rs.next()){
            lista.add(montarDados(rs));
        }
        return lista;
    }
    
    public ConfiguracaoBIVO montarDados(ResultSet rs) throws SQLException{
        ConfiguracaoBIVO cfgBi = new ConfiguracaoBIVO();
        cfgBi.setCodigo(rs.getInt("codigo"));
        cfgBi.setValor(rs.getString("valor"));
        cfgBi.setEmpresa(rs.getInt("empresa"));
        cfgBi.setUsuarioResponsavel(rs.getInt("usuarioresponsavel"));
        cfgBi.setBi(BIEnum.getFromCodigo(rs.getInt("bi")));
        cfgBi.setConfiguracao(ConfiguracaoBIEnum.getFromCodigo(rs.getInt("configuracao")));
        return cfgBi;
    }
    
    @Override
    public void deletarPorBI(BIEnum bi, Integer empresa) throws Exception {
        executarConsulta("DELETE FROM ConfiguracaoBI WHERE bi = "+bi.getCodigo()+" AND empresa = "+empresa, con);
    }

    @Override
    public void incluir(ConfiguracaoBIVO cfg) throws Exception {
        PreparedStatement stm = con.prepareStatement("INSERT INTO ConfiguracaoBI (Configuracao, bi, valor, empresa, usuarioresponsavel) VALUES (?,?,?,?,?)");
        stm.setInt(1, cfg.getConfiguracao().getCodigo());
        if (cfg.getBi() == null) {
            stm.setNull(2, Types.NULL);
        } else {
            stm.setInt(2, cfg.getBi().getCodigo());
        }
        stm.setString(3, cfg.getValor());
        stm.setInt( 4, cfg.getEmpresa());
        resolveIntegerNull(stm, 5, cfg.getUsuarioResponsavel());
        stm.execute();
    }

    @Override
    public void incluirPorBI(BIEnum bi, Integer empresa, List<ConfiguracaoBIVO> campos) throws Exception {
        deletarPorBI(bi, empresa);
        for(ConfiguracaoBIVO campo : campos){
            incluir(campo);
        }
    }

    @Override
    public void incluirPorConfig(Integer empresa, ConfiguracaoBIEnum cfg, String valor) throws Exception {
        executarConsulta("DELETE FROM ConfiguracaoBI WHERE configuracao = "+cfg.getCodigo()+" AND empresa = "+empresa, con);
        ConfiguracaoBIVO campo = new ConfiguracaoBIVO();
        campo.setConfiguracao(cfg);
        campo.setValor(valor);
        campo.setEmpresa(empresa);
        incluir(campo);
    }
    
    @Override
    public String valorPorConfig(Integer empresa, ConfiguracaoBIEnum cfg) throws Exception {
        ResultSet rs = criarConsulta("SELECT valor FROM ConfiguracaoBI WHERE configuracao = "+cfg.getCodigo()+" AND empresa = "+empresa, con);
        if(rs.next()){
            return rs.getString("valor");
        }else{
            incluirPorConfig(empresa, cfg, cfg.getValorPadrao());
            return cfg.getValorPadrao();
        }
    }
    
    @Override
    public Map<ConfiguracaoBIEnum, Boolean> mapaConfigs(Integer empresa, ConfiguracaoBIEnum ... cfgs) throws Exception{
        Map<ConfiguracaoBIEnum, Boolean> mapa = new EnumMap<ConfiguracaoBIEnum, Boolean>(ConfiguracaoBIEnum.class);
        for(ConfiguracaoBIEnum cfg : cfgs){
            mapa.put(cfg, Boolean.valueOf(valorPorConfig(empresa, cfg)));
        }
        return mapa;
    }
    
}

package negocio.facade.jdbc.basico;

import annotations.arquitetura.ChaveEstrangeira;
import br.com.pactosolucoes.enumeradores.OrigemSistemaEnum;
import negocio.comuns.arquitetura.SuperVO;
import negocio.comuns.basico.ClienteVO;
import negocio.comuns.basico.ColaboradorVO;
import negocio.comuns.basico.EmpresaVO;
import negocio.comuns.crm.PassivoVO;
import negocio.comuns.plano.HorarioTurmaVO;

import java.util.Date;

/**
 * Created by <PERSON> on 19/12/2016.
 */
public class AulaConfirmadaVO extends SuperVO {

    private Date dia;

    @ChaveEstrangeira
    private ClienteVO clienteVO;
    @ChaveEstrangeira
    private HorarioTurmaVO horarioTurmaVO;
    @ChaveEstrangeira
    private ColaboradorVO colaboradorVO;
    @ChaveEstrangeira
    private OrigemSistemaEnum origemSistemaEnum = OrigemSistemaEnum.ZW;
    private Integer autorizado;
    private Date dataLancamento;
    @ChaveEstrangeira
    private PassivoVO passivoVO;

    private Boolean autorizadoGestaoRede;
    private String codAcessoAutorizado;
    private Integer matriculaAutorizado;

    public Date getDia() {
        return dia;
    }

    public void setDia(Date dia) {
        this.dia = dia;
    }

    public ClienteVO getClienteVO() {
        if(clienteVO == null ){
            clienteVO = new ClienteVO();
        }
        return clienteVO;
    }

    public void setClienteVO(ClienteVO clienteVO) {
        this.clienteVO = clienteVO;
    }

    public HorarioTurmaVO getHorarioTurmaVO() {
        if(horarioTurmaVO == null){
            horarioTurmaVO = new HorarioTurmaVO();
        }
        return horarioTurmaVO;
    }

    public void setHorarioTurmaVO(HorarioTurmaVO horarioTurmaVO) {
        this.horarioTurmaVO = horarioTurmaVO;
    }

    public ColaboradorVO getColaboradorVO() {
        if(colaboradorVO == null){
            colaboradorVO = new ColaboradorVO();
        }
        return colaboradorVO;
    }

    public void setColaboradorVO(ColaboradorVO colaboradorVO) {
        this.colaboradorVO = colaboradorVO;
    }

    public OrigemSistemaEnum getOrigemSistemaEnum() {
        return origemSistemaEnum;
    }

    public void setOrigemSistemaEnum(OrigemSistemaEnum origemSistemaEnum) {
        this.origemSistemaEnum = origemSistemaEnum;
    }

    public Date getDataLancamento() {
        return dataLancamento;
    }

    public void setDataLancamento(Date dataLancamento) {
        this.dataLancamento = dataLancamento;
    }

    public Integer getAutorizado() {
        return autorizado;
    }

    public void setAutorizado(Integer autorizado) {
        this.autorizado = autorizado;
    }

    public Boolean getAutorizadoGestaoRede() {
        if (autorizadoGestaoRede == null){
            autorizadoGestaoRede = false;
        }
        return autorizadoGestaoRede;
    }

    public void setAutorizadoGestaoRede(Boolean autorizadoGestaoRede) {
        this.autorizadoGestaoRede = autorizadoGestaoRede;
    }

    public String getCodAcessoAutorizado() {
        if (codAcessoAutorizado == null){
            codAcessoAutorizado = "";
        }
        return codAcessoAutorizado;
    }

    public void setCodAcessoAutorizado(String codAcessoAutorizado) {
        this.codAcessoAutorizado = codAcessoAutorizado;
    }

    public Integer getMatriculaAutorizado() {
        if (matriculaAutorizado == null) {
            matriculaAutorizado = 0;
        }
        return matriculaAutorizado;
    }

    public void setMatriculaAutorizado(Integer matriculaAutorizado) {
        this.matriculaAutorizado = matriculaAutorizado;
    }

    public PassivoVO getPassivoVO() {
        if(passivoVO == null ){
            passivoVO = new PassivoVO();
        }
        return passivoVO;
    }

    public void setPassivoVO(PassivoVO passivoVO) {
        this.passivoVO = passivoVO;
    }
}

package negocio.facade.jdbc.basico;

import negocio.comuns.arquitetura.UsuarioVO;
import negocio.comuns.basico.ClienteVO;
import negocio.comuns.basico.PessoaAnexoVO;
import negocio.comuns.basico.PessoaVO;
import negocio.comuns.contrato.ContratoAssinaturaDigitalVO;
import negocio.comuns.contrato.ContratoVO;
import negocio.comuns.utilitarias.Calendario;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisValidacao;
import negocio.facade.jdbc.arquitetura.SuperEntidade;
import negocio.interfaces.basico.PessoaAnexoInterfaceFacade;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

public class PessoaAnexo extends SuperEntidade implements PessoaAnexoInterfaceFacade {
    public PessoaAnexo() throws Exception {
        super();
        setIdEntidade("Pessoa");
    }

    public PessoaAnexo(Connection con) throws Exception {
        super(con);
        setIdEntidade("Contrato");
    }

    @Override
    public void incluir(PessoaAnexoVO obj) throws Exception {
        String sql = "INSERT INTO pessoaanexo(\n" +
                "            pessoa, usuarioresponsavel, tipoanexo, lancamento, anexo1, \n" +
                "            anexo2)\n" +
                "    VALUES (?, ?, ?, ?, ?, \n" +
                "            ?);\n";
        int i = 1;
        PreparedStatement sqlInserir = con.prepareStatement(sql);
        sqlInserir.setInt(i++, obj.getPessoa().getCodigo());
        sqlInserir.setInt(i++, obj.getUsuarioResponsavel().getCodigo());
        sqlInserir.setInt(i++, obj.getTipoAnexo().getCodigo());
        sqlInserir.setTimestamp(i++, Uteis.getDataJDBCTimestamp(Calendario.hoje()));
        sqlInserir.setString(i++, obj.getAnexo1());
        sqlInserir.setString(i++, obj.getAnexo2());

        sqlInserir.execute();
        obj.setCodigo(obterValorChavePrimariaCodigo());
        obj.setNovoObj(false);
    }

    public String obterPorPessoaTipoAnexo(final Integer pessoa, String tipos) throws Exception {
        StringBuilder sql = new StringBuilder();
        sql.append(" SELECT * from pessoaanexo ");
        sql.append(" WHERE pessoa = ").append(pessoa);
        if (UteisValidacao.emptyString(tipos)) {
            sql.append(" AND tipoanexo in (").append(tipos).append(")");
        }
        ResultSet rs = criarConsulta(sql.toString(), con);
        if (rs.next()) {
            return rs.getString("assinatura");
        }
        return null;
    }

    @Override
    public void excluir(PessoaAnexoVO obj) throws Exception {
        excluir(getIdEntidade());
        String sql = "DELETE FROM pessoaanexo WHERE codigo = ?";
        PreparedStatement sqlExcluir = con.prepareStatement(sql);
        sqlExcluir.setInt(1, obj.getCodigo().intValue());
        sqlExcluir.execute();
    }

    @Override
    public void excluirPorPessoa(Integer pessoa, String tipos) throws Exception {
        excluir(getIdEntidade());
        String sql = "DELETE FROM pessoaanexo WHERE pessoa  = ?";
        if (UteisValidacao.emptyString(tipos)) {
            sql += " AND tipoanexo in (" + tipos + ")";
        }
        PreparedStatement sqlExcluir = con.prepareStatement(sql);
        sqlExcluir.setInt(1, pessoa);
        sqlExcluir.execute();
    }

    @Override
    public void alterar(PessoaAnexoVO obj) throws Exception {
        String sql = "UPDATE pessoaanexo\n" +
                "   SET  pessoa=?, usuarioresponsavel=?, tipoanexo=?, \n" +
                "       anexo1=?, anexo2=?\n" +
                " WHERE codigo = ?;\n";
        int i = 1;
        PreparedStatement sqlAlterar = con.prepareStatement(sql);
        sqlAlterar.setInt(i++, obj.getPessoa().getCodigo());
        sqlAlterar.setInt(i++, obj.getUsuarioResponsavel().getCodigo());
        sqlAlterar.setInt(i++, obj.getTipoAnexo().getCodigo());
        sqlAlterar.setString(i++, obj.getAnexo1());
        sqlAlterar.setString(i++, obj.getAnexo2());
        sqlAlterar.setInt(i++, obj.getCodigo());
        sqlAlterar.execute();
    }

    @Override
    public List<PessoaVO> consultarAlunosCartaoVacina(boolean cadastrados, String filtro, Integer empresa, boolean todos, String tiposAnexo) throws Exception {
        StringBuilder sql = new StringBuilder();
        sql.append(" SELECT p.codigo as codigopessoa,col.codigo as codigocolaborador, cli.matricula, p.nome, p.fotokey, cli.matricula, pan.lancamento from pessoa p ");
        sql.append(" left JOIN situacaoclientesinteticodw cli ON p.codigo = cli.codigopessoa ");
        sql.append(" left JOIN colaborador col ON p.codigo = col.pessoa ");
        sql.append(" left JOIN empresa emp  ON emp.codigo = col.empresa or emp.codigo = cli.empresacliente ");
        sql.append(" LEFT JOIN pessoaanexo pan ON pan.pessoa= p.codigo ");
        sql.append(" WHERE  ");
        if (!UteisValidacao.emptyString(filtro)) {
            sql.append(" (cli.nomeconsulta LIKE remove_acento_upper('").append(filtro.replaceAll(" ", "%")).append("%')");
            sql.append("  OR cli.matricula::varchar  = '").append(filtro).append("'  or p.nomeconsulta LIKE remove_acento_upper('");
            sql.append(filtro.replaceAll(" ", "%")).append("%') or col.codigo::varchar = '").append(filtro).append("')");
        } else {
            sql.append("  (cli.situacao = 'AT' or col.situacao = 'AT') ");
        }
        if (empresa != null) {
            sql.append(" AND (cli.empresacliente = ").append(empresa).append(" or col.empresa = ").append(empresa).append(") ");
        }
        sql.append(cadastrados ? " AND (pan.codigo is not null) " : " AND (pan.codigo  is null) ");

        sql.append(cadastrados ? " ORDER BY pan.lancamento DESC " : " ORDER BY p.nome ").append(todos ? "" : "LIMIT 20");
        ResultSet rs = criarConsulta(sql.toString(), con);
        List<PessoaVO> lista = new ArrayList<>();
        while (rs.next()) {
            PessoaVO pessoa = new PessoaVO();
            pessoa.setCodigo(rs.getInt("codigopessoa"));
            if (UteisValidacao.emptyNumber(rs.getInt("matricula"))) {
                pessoa.setIdentificadorEntidade("Colaborador: " + rs.getInt("codigocolaborador"));
            } else {
                pessoa.setIdentificadorEntidade("Matricula: " + rs.getInt("matricula"));
            }
            pessoa.setNome(rs.getString("nome").toLowerCase());
            pessoa.setFotoKey(rs.getString("fotokey"));
            pessoa.setLancamentoCartaoVacina(rs.getTimestamp("lancamento"));
            lista.add(pessoa);
        }
        return lista;
    }

    public PessoaAnexoVO consultarPorPessoa(Integer pessoa, String tipoAnexo) throws Exception {
        StringBuilder sql = new StringBuilder();
        sql.append(" SELECT pa.codigo,  u.nome as nomeusuario, ");
        sql.append(" pa.anexo1, pa.anexo2, pa.tipoanexo");
        sql.append(" FROM pessoa p \n");
        sql.append(" inner join pessoaanexo pa on p.codigo = pa.pessoa  ");
        sql.append(" inner join usuario u on u.codigo = pa.usuarioresponsavel where p.codigo = ").append(pessoa);
        sql.append(" and tipoanexo in (").append(tipoAnexo).append(")");
        ResultSet rs = criarConsulta(sql.toString(), con);
        PessoaAnexoVO  docs = new PessoaAnexoVO();
        docs.setPessoa(new PessoaVO());
        docs.getPessoa().setCodigo(pessoa);
        if (rs.next()) {
            docs.setCodigo(rs.getInt("codigo"));

            if (UteisValidacao.emptyString(rs.getString("anexo1"))) {
                docs.setAnexo1("");
            } else {
                docs.setAnexo1(rs.getString("anexo1"));
            }

            docs.setUsuarioResponsavel(new UsuarioVO());
            docs.getUsuarioResponsavel().setNome(rs.getString("nomeusuario"));
        }
        return docs;
    }

    @Override
    public Integer countAlunosCartaoVacina(boolean cadastrados, String filtro, Integer empresa, String tiposAnexo) throws Exception {
        StringBuilder sql = new StringBuilder();
        sql.append(" SELECT count(p.codigo) as cont from pessoa p ");
        sql.append(" left JOIN situacaoclientesinteticodw cli ON p.codigo = cli.codigopessoa ");
        sql.append(" left JOIN colaborador col ON p.codigo = col.pessoa ");
        sql.append(" LEFT JOIN pessoaanexo pan ON pan.pessoa= p.codigo ");
        sql.append(" WHERE  ");
        if (!UteisValidacao.emptyString(filtro)) {
            sql.append(" (cli.nomeconsulta LIKE remove_acento_upper('").append(filtro.replaceAll(" ", "%")).append("%')");
            sql.append("  OR cli.matricula::varchar  = '").append(filtro).append( "'  or p.nomeconsulta LIKE remove_acento_upper('");
            sql.append(filtro.replaceAll(" ", "%")).append("%') or col.codigo::varchar = '").append(filtro).append("')");
        } else {
            sql.append("  (cli.situacao = 'AT' or col.situacao = 'AT') ");
        }
        if (empresa != null) {
            sql.append(" AND (cli.empresacliente = ").append(empresa).append(" or col.empresa = ").append(empresa).append(") ");
        }
        sql.append(cadastrados ? " AND (pan.codigo is not null) " : " AND (pan.codigo  is null) ");
        ResultSet rs = criarConsulta(sql.toString(), con);
        if (rs.next()) {
            return rs.getInt("cont");
        }
        return 0;
    }

    public boolean existesAnexoPessoaTipoIdade(Integer pessoa, String tipoAnexo) throws Exception {
        StringBuilder sql = new StringBuilder();
        sql.append(" SELECT pa.codigo ");
        sql.append(" FROM pessoa p \n");
        sql.append(" inner join pessoaanexo pa on p.codigo = pa.pessoa  ");
        sql.append("  where p.codigo = ").append(pessoa);
        sql.append(" and tipoanexo in (").append(tipoAnexo).append(")");
        ResultSet rs = criarConsulta(sql.toString(), con);
        if (rs.next()) {
           return true;
        }
        return false;
    }
}

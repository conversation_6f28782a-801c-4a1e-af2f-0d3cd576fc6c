package negocio.facade.jdbc.arquitetura;

import acesso.webservice.DaoAuxiliar;
import br.com.pactosolucoes.comuns.util.JSFUtilities;
import negocio.comuns.arquitetura.PerfilAcessoVO;
import negocio.comuns.arquitetura.PermissaoVO;
import negocio.comuns.arquitetura.UsuarioTO;
import negocio.comuns.arquitetura.UsuarioVO;
import negocio.comuns.basico.AvisoInternoDTO;
import negocio.comuns.basico.PessoaVO;
import negocio.comuns.feed.PerfilUsuarioEnum;
import negocio.comuns.utilitarias.*;
import negocio.interfaces.arquitetura.PerfilAcessoInterfaceFacade;
import org.json.JSONArray;
import org.json.JSONObject;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.sql.Statement;
import java.sql.Timestamp;
import java.util.*;

/**
 * Classe de persistência que encapsula todas as operações de manipulação dos dados da classe <code>PerfilAcessoVO</code>.
 * Responsável por implementar operações como incluir, alterar, excluir e consultar pertinentes a classe <code>PerfilAcessoVO</code>.
 * Encapsula toda a interação com o banco de dados.
 *
 * @see PerfilAcessoVO
 * @see SuperEntidade
 */
public class PerfilAcesso extends SuperEntidade implements PerfilAcessoInterfaceFacade {
    private Hashtable permissaos;

    public PerfilAcesso() throws Exception {
        super();
        setPermissaos(new Hashtable());
    }

    public PerfilAcesso(Connection con) throws Exception {
        super(con);
        setPermissaos(new Hashtable());
    }

    /**
     * Operação responsável por retornar um novo objeto da classe <code>PerfilAcessoVO</code>.
     */
    public PerfilAcessoVO novo() throws Exception {
        incluir(getIdEntidade());
        PerfilAcessoVO obj = new PerfilAcessoVO();
        return obj;
    }

    /**
     * Operação responsável por incluir no banco de dados um objeto da classe <code>PerfilAcessoVO</code>.
     * Primeiramente valida os dados (<code>validarDados</code>) do objeto. Verifica a conexão com o banco de dados e a permissão do usuário
     * para realizar esta operacão na entidade.
     * Isto, através da operação <code>incluir</code> da superclasse.
     *
     * @param obj Objeto da classe <code>PerfilAcessoVO</code> que será gravado no banco de dados.
     * @throws Exception Caso haja problemas de conexão, restrição de acesso ou validação de dados.
     */
    public void incluir(PerfilAcessoVO obj, boolean centralEventos) throws Exception {
        try {
            con.setAutoCommit(false);
            PerfilAcessoVO.validarDados(obj);
            if (centralEventos) {
//            	super.incluirObj(getIdEntidade());
            } else {
                incluir(getIdEntidade());
            }
            obj.realizarUpperCaseDados();
            String sql = "INSERT INTO PerfilAcesso( nome, tipo, porcetagemDescontoContrato ) VALUES ( ?, ?, ? )";
            try (PreparedStatement sqlInserir = con.prepareStatement(sql)) {
                sqlInserir.setString(1, obj.getNome());
                resolveIntegerNullComZero(sqlInserir, 2, obj.getTipo() == null?null:obj.getTipo().getId());
                sqlInserir.setDouble(3,obj.getPorcetagemDescontoContrato());

                sqlInserir.execute();
            }
            obj.setCodigo(obterValorChavePrimariaCodigo());
            obj.setNovoObj(new Boolean(false));
            getFacade().getPermissao().incluirPermissoes(obj.getCodigo(), obj.getPermissaoVOs());
            con.commit();
        } catch (Exception e) {
            con.rollback();
            con.setAutoCommit(true);
            throw e;
        } finally {
            con.setAutoCommit(true);
        }
    }

    /**
     * Operação responsável por incluir no banco de dados um objeto da classe <code>PerfilAcessoVO</code>.
     * Primeiramente valida os dados (<code>validarDados</code>) do objeto. Verifica a conexão com o banco de dados e a permissão do usuário
     * para realizar esta operacão na entidade.
     * Isto, através da operação <code>incluir</code> da superclasse.
     *
     * @param obj Objeto da classe <code>PerfilAcessoVO</code> que será gravado no banco de dados.
     * @throws Exception Caso haja problemas de conexão, restrição de acesso ou validação de dados.
     */
    public void incluirSemCommit(PerfilAcessoVO obj) throws Exception {
        try {
            PerfilAcessoVO.validarDados(obj);
            obj.realizarUpperCaseDados();
            String sql = "INSERT INTO PerfilAcesso( nome, tipo, porcetagemDescontoContrato) VALUES ( ?, ? , ? )";
            try (PreparedStatement sqlInserir = con.prepareStatement(sql)) {
                sqlInserir.setString(1, obj.getNome());
                resolveIntegerNullComZero(sqlInserir, 2, obj.getTipo() == null?null:obj.getTipo().getId());
                sqlInserir.setDouble(3,obj.getPorcetagemDescontoContrato());

                sqlInserir.execute();
            }
            obj.setCodigo(obterValorChavePrimariaCodigo());
            obj.setNovoObj(new Boolean(false));
            getFacade().getPermissao().incluirPermissoes(obj.getCodigo(), obj.getPermissaoVOs());
        } catch (Exception e) {
            throw e;
        }
    }

    /* (non-Javadoc)
     * @see negocio.interfaces.arquitetura.PerfilAcessoInterfaceFacade#incluir(negocio.comuns.arquitetura.PerfilAcessoVO)
     */
    public void incluir(PerfilAcessoVO obj) throws Exception {
        this.incluir(obj, false);
    }

    /**
     * Operação responsável por alterar no BD os dados de um objeto da classe <code>PerfilAcessoVO</code>.
     * Sempre utiliza a chave primária da classe como atributo para localização do registro a ser alterado.
     * Primeiramente valida os dados (<code>validarDados</code>) do objeto. Verifica a conexão com o banco de dados e a permissão do usuário
     * para realizar esta operacão na entidade.
     * Isto, através da operação <code>alterar</code> da superclasse.
     *
     * @param obj Objeto da classe <code>PerfilAcessoVO</code> que será alterada no banco de dados.
     * @throws Exception Caso haja problemas de conexão, restrição de acesso ou validação de dados.
     */
    public void alterar(PerfilAcessoVO obj, boolean centralEventos) throws Exception {
        try {
            con.setAutoCommit(false);
            alterar(getIdEntidade());
            alterarSemCommit(obj);
            con.commit();
        } catch (Exception e) {
            con.rollback();
            con.setAutoCommit(true);
            throw e;
        } finally {
            con.setAutoCommit(true);
            }
    }

    public void alterarSemCommit(PerfilAcessoVO obj) throws Exception {
        PerfilAcessoVO.validarDados(obj);
            obj.realizarUpperCaseDados();
            String sql = "UPDATE PerfilAcesso SET nome=?, tipo=?, porcetagemDescontoContrato = ? WHERE codigo = ?";
        try (PreparedStatement sqlAlterar = con.prepareStatement(sql)) {
            sqlAlterar.setString(1, obj.getNome());
            resolveIntegerNullComZero(sqlAlterar, 2, obj.getTipo() == null?null:obj.getTipo().getId());
            sqlAlterar.setDouble(3, obj.getPorcetagemDescontoContrato());
            sqlAlterar.setInt(4, obj.getCodigo());
            sqlAlterar.execute();
        }
        getFacade().getPermissao().alterarPermissaos(obj.getCodigo(), obj.getPermissaoVOs());
    }

    /* (non-Javadoc)
     * @see negocio.interfaces.arquitetura.PerfilAcessoInterfaceFacade#incluir(negocio.comuns.arquitetura.PerfilAcessoVO)
     */
    public void alterar(PerfilAcessoVO obj) throws Exception {
        this.alterar(obj, false);
    }

    /**
     * Operação responsável por excluir no BD um objeto da classe <code>PerfilAcessoVO</code>.
     * Sempre localiza o registro a ser excluído através da chave primária da entidade.
     * Primeiramente verifica a conexão com o banco de dados e a permissão do usuário
     * para realizar esta operacão na entidade.
     * Isto, através da operação <code>excluir</code> da superclasse.
     *
     * @param obj Objeto da classe <code>PerfilAcessoVO</code> que será removido no banco de dados.
     * @throws Exception Caso haja problemas de conexão ou restrição de acesso.
     */
    public void excluir(PerfilAcessoVO obj, boolean centralEventos) throws Exception {
        try {
            con.setAutoCommit(false);
            if (centralEventos) {
//            	excluirObj(getIdEntidade());
            } else {
                excluir(getIdEntidade());
            }
            String sql = "DELETE FROM PerfilAcesso WHERE ((codigo = ?))";
            try (PreparedStatement sqlExcluir = con.prepareStatement(sql)) {
                sqlExcluir.setInt(1, obj.getCodigo().intValue());
                sqlExcluir.execute();
            }
            getFacade().getPermissao().excluirPermissaos(obj.getCodigo());
            con.commit();
        } catch (Exception e) {
            con.rollback();
            con.setAutoCommit(true);
            throw e;
        } finally {
            con.setAutoCommit(true);
        }
    }

    /* (non-Javadoc)
     * @see negocio.interfaces.arquitetura.PerfilAcessoInterfaceFacade#incluir(negocio.comuns.arquitetura.PerfilAcessoVO)
     */
    public void excluir(PerfilAcessoVO obj) throws Exception {
        this.excluir(obj, false);
    }

    /**
     * Responsável por realizar uma consulta de <code>PerfilAcesso</code> através do valor do atributo
     * <code>String nome</code>. Retorna os objetos, com início do valor do atributo idêntico ao parâmetro fornecido.
     * Faz uso da operação <code>montarDadosConsulta</code> que realiza o trabalho de prerarar o List resultante.
     *
     * @param controlarAcesso Indica se a aplicação deverá verificar se o usuário possui permissão para esta consulta ou não.
     * @return List Contendo vários objetos da classe <code>PerfilAcessoVO</code> resultantes da consulta.
     * @throws Exception Caso haja problemas de conexão ou restrição de acesso.
     */
    public List consultarPorNome(String valorConsulta, boolean controlarAcesso, int nivelMontarDados) throws Exception {
        consultar(getIdEntidade(), controlarAcesso);
        PerfilAcesso perfilAcesso = new PerfilAcesso(con);
        String sqlStr = "SELECT * FROM PerfilAcesso WHERE upper( nome ) like('" + valorConsulta.toUpperCase() + "%')  ORDER BY nome";

        try (Statement stm = con.createStatement()) {
            try (ResultSet tabelaResultado = stm.executeQuery(sqlStr)) {
                return (montarDadosConsulta(tabelaResultado, nivelMontarDados));
            }
        }
    }

    public List consultarPorParteDoNome(String valorConsulta, boolean controlarAcesso, int nivelMontarDados) throws Exception {
        consultar(getIdEntidade(), controlarAcesso);
        PerfilAcesso perfilAcesso = new PerfilAcesso(con);
        String sqlStr = "SELECT * FROM PerfilAcesso WHERE upper( nome ) like('%" + valorConsulta.toUpperCase() + "%')  ORDER BY nome";

        try (Statement stm = con.createStatement()) {
            try (ResultSet tabelaResultado = stm.executeQuery(sqlStr)) {
                return (montarDadosConsulta(tabelaResultado, nivelMontarDados));
            }
        }
    }

    public PerfilAcessoVO consultarPorTipo(PerfilUsuarioEnum tipo, boolean controlarAcesso, int nivelMontarDados) throws Exception {
        consultar(getIdEntidade(), controlarAcesso);
        String sqlStr = "SELECT * FROM PerfilAcesso WHERE tipo = " + tipo.ordinal() + " ORDER BY nome";
        try (Statement stm = con.createStatement()) {
            try (ResultSet tabelaResultado = stm.executeQuery(sqlStr)) {
                if (tabelaResultado.next()) {
                    return montarDados(tabelaResultado, nivelMontarDados);
                }
            }
        }
        return null;
    }

    public int consultarPorNome(String valorConsulta) throws Exception {
        String sqlStr = "SELECT codigo FROM PerfilAcesso WHERE upper( nome ) like('" + valorConsulta.toUpperCase() + "%') ORDER BY nome";
        try (Statement stm = con.createStatement()) {
            try (ResultSet tabelaResultado = stm.executeQuery(sqlStr)) {
                if (tabelaResultado.next()) {
                    return tabelaResultado.getInt("codigo");
                }
            }
        }
        return 0;
    }

    /**
     * Responsável por realizar uma consulta de <code>PerfilAcesso</code> através do valor do atributo
     * <code>Integer codigo</code>. Retorna os objetos com valores iguais ou superiores ao parâmetro fornecido.
     * Faz uso da operação <code>montarDadosConsulta</code> que realiza o trabalho de prerarar o List resultante.
     *
     * @param controlarAcesso Indica se a aplicação deverá verificar se o usuário possui permissão para esta consulta ou não.
     * @return List Contendo vários objetos da classe <code>PerfilAcessoVO</code> resultantes da consulta.
     * @throws Exception Caso haja problemas de conexão ou restrição de acesso.
     */
    public List consultarPorCodigo(Integer valorConsulta, boolean controlarAcesso, int nivelMontarDados) throws Exception {
        consultar(getIdEntidade(), controlarAcesso);
        String sqlStr = "SELECT * FROM PerfilAcesso WHERE codigo >= " + valorConsulta.intValue() + " ORDER BY codigo";
        try (Statement stm = con.createStatement()) {
            try (ResultSet tabelaResultado = stm.executeQuery(sqlStr)) {
                return (montarDadosConsulta(tabelaResultado, nivelMontarDados));
            }
        }
    }

    /**
     * Responsável por montar os dados de vários objetos, resultantes de uma consulta ao banco de dados (<code>ResultSet</code>).
     * Faz uso da operação <code>montarDados</code> que realiza o trabalho para um objeto por vez.
     *
     * @return List Contendo vários objetos da classe <code>PerfilAcessoVO</code> resultantes da consulta.
     */
    public List montarDadosConsulta(ResultSet tabelaResultado, int nivelMontarDados) throws Exception {
        List vetResultado = new ArrayList();
        while (tabelaResultado.next()) {
            PerfilAcessoVO obj = new PerfilAcessoVO();
            obj = montarDados(tabelaResultado, nivelMontarDados);
            vetResultado.add(obj);
        }
        return vetResultado;
    }

    /**
     * Responsável por montar os dados resultantes de uma consulta ao banco de dados (<code>ResultSet</code>)
     * em um objeto da classe <code>PerfilAcessoVO</code>.
     *
     * @return O objeto da classe <code>PerfilAcessoVO</code> com os dados devidamente montados.
     */
    public PerfilAcessoVO montarDados(ResultSet dadosSQL, int nivelMontarDados) throws Exception {
        PerfilAcessoVO obj = new PerfilAcessoVO();
        Permissao permissao = new Permissao(con);
        obj.setCodigo(dadosSQL.getInt("codigo"));
        obj.setNome(dadosSQL.getString("nome"));
        obj.setUnificado(dadosSQL.getBoolean("unificado"));
        try {
            obj.setTipo(UteisValidacao.emptyNumber(dadosSQL.getInt("tipo")) ? null : PerfilUsuarioEnum.getFromOrdinal(dadosSQL.getInt("tipo")));
            obj.setPorcetagemDescontoContrato(dadosSQL.getDouble("porcetagemDescontoContrato"));
        } catch (Exception ignored) {
        }
        obj.setNovoObj(Boolean.FALSE);

        obj.setPermissaoVOs(permissao.consultarPermissaos(obj.getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS));
        return obj;
    }

    /**
     * Operação responsável por adicionar um objeto da <code>PermissaoVO</code> no Hashtable <code>Permissaos</code>.
     * Neste Hashtable são mantidos todos os objetos de Permissao de uma determinada PerfilAcesso.
     *
     * @param obj Objeto a ser adicionado no Hashtable.
     */
    public void adicionarObjPermissaos(PermissaoVO obj) throws Exception {
        getPermissaos().put(obj.getNomeEntidade() + "", obj);
        //adicionarObjSubordinadoOC
    }

    /**
     * Operação responsável por remover um objeto da classe <code>PermissaoVO</code> do Hashtable <code>Permissaos</code>.
     * Neste Hashtable são mantidos todos os objetos de Permissao de uma determinada PerfilAcesso.
     *
     * @param NomeEntidade Atributo da classe <code>PermissaoVO</code> utilizado como apelido (key) no Hashtable.
     */
    public void excluirObjPermissaos(String NomeEntidade) throws Exception {
        getPermissaos().remove(NomeEntidade + "");
        //excluirObjSubordinadoOC
    }

    /**
     * Operação responsável por localizar um objeto da classe <code>PerfilAcessoVO</code>
     * através de sua chave primária.
     *
     * @throws Exception Caso haja problemas de conexão ou localização do objeto procurado.
     */
    public PerfilAcessoVO consultarPorChavePrimaria(Integer codigoPrm, int nivelMontarDados) throws Exception {
        consultar(getIdEntidade(), false);
        PerfilAcessoVO eCache = (PerfilAcessoVO) obterFromCache(codigoPrm);
        if (eCache != null){
            return eCache;
        }
        String sql = "SELECT * FROM PerfilAcesso WHERE codigo = ?";
        try (PreparedStatement sqlConsultar = con.prepareStatement(sql)) {
            sqlConsultar.setInt(1, codigoPrm);
            try (ResultSet tabelaResultado = sqlConsultar.executeQuery()) {
                if (!tabelaResultado.next()) {
                    throw new ConsistirException("Dados Não Encontrados ( PerfilAcesso ).");
                }
                eCache = (montarDados(tabelaResultado, nivelMontarDados));
            }
        }
        putToCache(eCache);
        return eCache;
    }

    public Hashtable getPermissaos() {
        return (permissaos);
    }

    public void setPermissaos(Hashtable permissaos) {
        this.permissaos = permissaos;
    }

    public PerfilAcessoVO criarOuConsultarSeExistePorNome(PerfilAcessoVO obj) throws Exception {
        try {
            String sql = "SELECT * FROM PerfilAcesso WHERE nome = ?";
            try (PreparedStatement sqlConsultar = con.prepareStatement(sql)) {
                sqlConsultar.setString(1, obj.getNome());
                try (ResultSet tabelaResultado = sqlConsultar.executeQuery()) {
                    if (!tabelaResultado.next()) {
                        incluir(obj);
                        return obj;
                    } else {
                        return (montarDados(tabelaResultado, Uteis.NIVELMONTARDADOS_DADOSBASICOS));
                    }
                }
            }

        } catch (Exception e) {
            throw e;
        }
    }

    public String consultarJSON() throws Exception {
        JSONObject aaData = new JSONObject();
        JSONArray valores = new JSONArray();

        try (ResultSet rs = getPS().executeQuery()) {
            while (rs.next()) {
                JSONArray itemArray = new JSONArray();
                itemArray.put(rs.getString("codigo"));
                itemArray.put(rs.getString("nome"));
                valores.put(itemArray);
            }
        }
        aaData.put("aaData", valores);
        return aaData.toString();
    }

    public boolean temPerfilUnificado(){
        String sql = "SELECT * FROM perfilacesso where unificado = true";
        try (PreparedStatement sqlConsultar = con.prepareStatement(sql)) {
            try (ResultSet tabelaResultado = sqlConsultar.executeQuery()) {
                if (!tabelaResultado.next()) {
                    return false;
                }
                return true;
            }
        } catch (Exception e) {
            return false;
        }
    }

    private PreparedStatement getPS() throws SQLException {
        String sql = "SELECT codigo, nome FROM perfilacesso where unificado = false ORDER BY nome";
        return con.prepareStatement(sql);
    }

    public List consultarParaImpressao(String filtro, String ordem, String campoOrdenacao, int i) throws SQLException {

        List lista;
        try (ResultSet rs = getPS().executeQuery()) {
            lista = new ArrayList();

            while (rs.next()) {

                PerfilAcessoVO perfil = new PerfilAcessoVO();
                String geral = rs.getString("codigo") + rs.getString("nome");
                if (geral.toLowerCase().contains(filtro.toLowerCase())) {
                    perfil.setCodigo(rs.getInt("codigo"));
                    perfil.setNome(rs.getString("nome"));

                    lista.add(perfil);
                }
            }
        }
        if (campoOrdenacao.equals("Código")) {
            Ordenacao.ordenarLista(lista, "codigo");
        } else if (campoOrdenacao.equals("Descrição")) {
            Ordenacao.ordenarLista(lista, "nome");
        }
        if (ordem.contains("desc")) {
            Collections.reverse(lista);
        }
        return lista;
    }

    public List<UsuarioTO> consultarUsuariosDoPerfil(Integer codPerfilAcesso, String filtro, String ordem, String campoOrdenacao, String situacao) throws Exception {
        List<UsuarioTO> lista = new ArrayList<>();

        try (ResultSet rs = getRS(codPerfilAcesso, situacao, null, 0, filtro, 0, "asc")) {
            while (rs.next()) {
                UsuarioTO usuarioTO = new UsuarioTO();
                usuarioTO.setCodigo(rs.getInt("codigoUsuario"));
                usuarioTO.setUserName(rs.getString("username"));
                usuarioTO.setNome(rs.getString("nome"));
                usuarioTO.setPerfilAcesso(rs.getString("perfilacesso"));
                usuarioTO.setNomeEmpresa(rs.getString("empresa"));
                lista.add(usuarioTO);
            }
        }

        if (campoOrdenacao.equals("Código Usuário")) {
            Ordenacao.ordenarLista(lista, "codigoUsuario");
        } else if (campoOrdenacao.equals("Usuário")) {
            Ordenacao.ordenarLista(lista, "userName");
        } else if (campoOrdenacao.equals("Nome")) {
            Ordenacao.ordenarLista(lista, "nome");
        } else if (campoOrdenacao.equals("Perfil Acesso")) {
            Ordenacao.ordenarLista(lista, "perfilAcesso");
        } else if (campoOrdenacao.equals("Empresa")) {
            Ordenacao.ordenarLista(lista, "nomeEmpresa");
        }
        if (ordem.contains("desc")) {
            Collections.reverse(lista);
        }

        return lista;
    }

    public ResultSet getRS(Integer perfilAcesso, String situacao, Integer offset, Integer limit, String clausulaLike, Integer colOrdenar, String dirOrdenar) throws Exception {
        StringBuilder sql = new StringBuilder();
        sql.append("select \n");
        sql.append("u.codigo as codigoUsuario, \n");
        sql.append("u.username, \n");
        sql.append("u.nome, \n");
        sql.append("p.nome as perfilacesso, \n");
        sql.append("e.nome as empresa \n");
        adicionarFiltroConsulta(perfilAcesso, situacao, clausulaLike, sql);
        sql.append("  ORDER BY ").append(colOrdenar + 1).append(" ").append(dirOrdenar).append("\n");
        if (limit > 0) {
            sql.append(" LIMIT ").append(limit).append("\n");
        }
        sql.append(" OFFSET ").append(offset).append("\n");

        PreparedStatement sqlConsultar = con.prepareStatement(sql.toString());
        return sqlConsultar.executeQuery();
    }

    public String consultarUsuariosPerfilAcessoJSON(Integer perfilAcesso, String situacao, String sEcho, Integer offset, Integer limit, String clausulaLike, Integer colOrdenar, String dirOrdenar) throws Exception {
        StringBuilder sqlCount = new StringBuilder("SELECT count(codigo) FROM usuario u");

        StringBuilder json;
        boolean dados;
        try (ResultSet rs = getRS(perfilAcesso, situacao, offset, limit, clausulaLike, colOrdenar, dirOrdenar)) {
            StringBuilder sqlContarFiltrados = new StringBuilder("SELECT count(u.codigo)\n");
            adicionarFiltroConsulta(perfilAcesso, situacao, clausulaLike, sqlContarFiltrados);

            json = new StringBuilder();
            json.append("{");
            json.append("\"iTotalRecords\":\"").append(contar(sqlCount.toString(), getCon())).append("\",");
            json.append("\"iTotalDisplayRecords\":\"").append(contar(sqlContarFiltrados.toString(), getCon())).append("\",");
            json.append("\"sEcho\":\"").append(sEcho).append("\",");
            json.append("\"aaData\":[");

            dados = false;
            while (rs.next()) {
                dados = true;
                json.append("[\"").append(rs.getString("codigoUsuario")).append("\",");
                json.append("\"").append(rs.getString("username")).append("\",");
                json.append("\"").append(Uteis.normalizarStringJSON(rs.getString("nome"))).append("\",");
                json.append("\"").append(Uteis.normalizarStringJSON(rs.getString("perfilacesso"))).append("\",");

                json.append("\"").append(Uteis.normalizarStringJSON(rs.getString("empresa").trim())).append("\"],");
            }
        }

        if (dados) {
            json.deleteCharAt(json.toString().length() - 1);
        }
        json.append("]}");

        return json.toString();
    }

    private void adicionarFiltroConsulta(Integer perfilAcesso, String situacao, String clausulaLike, StringBuilder sqlContarFiltrados) {
        sqlContarFiltrados.append("from usuario u \n");
        sqlContarFiltrados.append("inner join usuarioperfilacesso up on up.usuario =  u.codigo \n");
        sqlContarFiltrados.append("inner join perfilacesso p on p.codigo = up.perfilacesso \n");
        sqlContarFiltrados.append("inner join empresa e on e.codigo = up.empresa \n");
        sqlContarFiltrados.append("inner join colaborador col on col.codigo = u.colaborador \n");
        sqlContarFiltrados.append("where p.codigo = ").append(perfilAcesso).append(" \n");
        if (!situacao.equals("TD")) {
            sqlContarFiltrados.append(" and col.situacao = '").append(situacao.toUpperCase()).append("' \n");
        }

        if (!UteisValidacao.emptyString(clausulaLike)) {
            sqlContarFiltrados.append(" AND (");
            sqlContarFiltrados.append("lower(u.codigo::VARCHAR) ~ '").append(clausulaLike).append("' \n");
            sqlContarFiltrados.append("OR lower(u.username) ~ '").append(clausulaLike).append("' \n");
            sqlContarFiltrados.append("OR lower(u.nome) ~ '").append(clausulaLike).append("' \n");
            sqlContarFiltrados.append(")");
            if (clausulaLike.toLowerCase().equals("[aà-æ]t[iì-ï]v[oò-ö]") && situacao.equals("AT")) {
                sqlContarFiltrados.append(" or desativado = true");
            } else if (clausulaLike.toLowerCase().equals("[iì-ï][nñ][aà-æ]t[iì-ï]v[oò-ö]") && situacao.equals("NA")) {
                sqlContarFiltrados.append(" or desativado = false");
            }
        }
    }


    public PerfilAcessoVO consultarPorUsuarioEmpresa(Integer usuario, Integer empresa, int nivelMontarDados) throws Exception {
        consultar(getIdEntidade(), false);
        String sql = "select p.* from usuario u inner join usuarioperfilacesso up on up.usuario = u.codigo and up.empresa = ? inner join perfilacesso p on p.codigo = up.perfilacesso where u.codigo = ?";
        try (PreparedStatement sqlConsultar = con.prepareStatement(sql)) {
            sqlConsultar.setInt(1, empresa);
            sqlConsultar.setInt(2, usuario);
            try (ResultSet tabelaResultado = sqlConsultar.executeQuery()) {
                if (!tabelaResultado.next()) {
                    throw new ConsistirException("Usuário não tem perfil na empresa informada.");
                }
                return (montarDados(tabelaResultado, nivelMontarDados));
            }
        }
    }

    public Double consultarMaximoDescontoPorUsuarioEmpresa(Integer usuario, Integer empresa) throws Exception {
        consultar(getIdEntidade(), false);
        String sql = "select p.porcetagemDescontoContrato from usuario u " +
                " inner join usuarioperfilacesso up on up.usuario = u.codigo and up.empresa = ? " +
                " inner join perfilacesso p on p.codigo = up.perfilacesso where u.codigo = ?";
        try (PreparedStatement stm = con.prepareStatement(sql)) {
            stm.setInt(1, empresa);
            stm.setInt(2, usuario);
            try (ResultSet rs = stm.executeQuery()) {
                if (rs.next()) {
                    return rs.getDouble("porcetagemDescontoContrato");
                }
                return 0.0;
            }
        }
    }

    public List<AvisoInternoDTO> avisosInternos(Integer empresa, Integer usuario, Boolean permissao) {
        List<AvisoInternoDTO> avisos = new ArrayList<>();
        try {
            String sqlAtual = "select distinct(a.codigo), a.ativo, u.nome, a.conteudo, a.dataExpiracao, a.dataPublicacao, " +
                    "a.empresa, a.visivelParaTodos from avisointerno a " +
                    "inner join usuario u on u.codigo = a.autor " +
                    "left join (avisointerno_usuario au left join usuario usu on au.usuario_codigo = usu.codigo) " +
                    "on a.codigo = au.avisointerno_codigo and (usu.codigo = " + usuario + ") " +
                    "left join avisointerno_perfilacesso ap on a.codigo= ap.avisointerno_codigo " +
                    "left join perfilacesso p on ap.perfilacesso_codigo = p.codigo " +
                    "left join usuarioperfilacesso up on (up.perfilacesso = p.codigo) " +
                    "where a.ativo=true and a.empresa=" + empresa +
                    " and a.dataExpiracao >= CURRENT_DATE " +
                    "and (a.visivelParaTodos = true or usu.codigo is not null or up.usuario = " + usuario;

            if(permissao) {
                sqlAtual += "OR a.autor = " + usuario;
            }
            sqlAtual += ") ORDER BY a.dataPublicacao DESC";

            try (PreparedStatement stm = con.prepareStatement(sqlAtual)) {
                try (ResultSet rs = stm.executeQuery()) {
                    while (rs.next()) {
                        AvisoInternoDTO aviso = new AvisoInternoDTO();
                        aviso.setCodigo(rs.getInt("codigo"));
                        aviso.setAviso(rs.getString("conteudo"));
                        aviso.setDataPublicacao(Uteis.getDataAplicandoFormatacao(
                                rs.getTimestamp("dataPublicacao"), "dd/MM/yyyy HH:mm"));
                        aviso.setAutor(rs.getString("nome").toLowerCase());
                        avisos.add(aviso);
                    }
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        return avisos;
    }

    public AvisoInternoDTO getAvisoInternoPorCodigo(int codigo) throws Exception {
        AvisoInternoDTO aviso = null;
        String sql = "SELECT a.codigo, conteudo, datapublicacao, autor, dataexpiracao, visivelparatodos FROM avisointerno a " +
                "inner join usuario u on u.codigo = a.autor " +
                "WHERE a.codigo = ? AND a.ativo";

        String sqlAtual = "SELECT a.codigo, conteudo, datapublicacao, autor, dataexpiracao, visivelparatodos, " +
                "au.usuario_codigo, ap.perfilacesso_codigo " +
                "FROM avisointerno a " +
                "LEFT JOIN avisointerno_usuario au ON au.avisointerno_codigo = a.codigo " +
                "LEFT JOIN avisointerno_perfilacesso ap ON ap.avisointerno_codigo = a.codigo " +
                "WHERE a.codigo = ? AND a.ativo";

        try (PreparedStatement stm = con.prepareStatement(sqlAtual)) {
            stm.setInt(1, codigo);
            try (ResultSet rs = stm.executeQuery()) {
                while (rs.next()) {
                    if (aviso == null) {
                        aviso = new AvisoInternoDTO();
                        aviso.setCodigo(rs.getInt("codigo"));
                        aviso.setAviso(rs.getString("conteudo"));
                        aviso.setAutor(rs.getString("autor").toLowerCase());
                        aviso.setDataAte(rs.getDate("dataExpiracao"));
                        aviso.setVisivelParaTodos(rs.getBoolean("visivelParaTodos"));
                    }
                    int usuarioCodigo = rs.getInt("usuario_codigo");
                    if (usuarioCodigo != 0 && !aviso.getUsuarios().contains(usuarioCodigo)) {
                        aviso.getUsuarios().add(usuarioCodigo);
                    }
                    int perfilCodigo = rs.getInt("perfilacesso_codigo");
                    if (perfilCodigo != 0 && !aviso.getPerfis().contains(perfilCodigo)) {
                        aviso.getPerfis().add(perfilCodigo);
                    }
                }

            }
        }
        return aviso;
    }

    public void atualizarAvisoInterno(AvisoInternoDTO aviso,
                                      List<PerfilAcessoVO> perfis,
                                      List<UsuarioVO> usuarios) throws Exception {
        String sql = "UPDATE avisointerno SET conteudo = ?, datapublicacao = ?, dataexpiracao = ?, visivelparatodos = ? WHERE codigo = ?";
        try (PreparedStatement stm = con.prepareStatement(sql)) {
            stm.setString(1, aviso.getAviso());
            stm.setTimestamp(2, new java.sql.Timestamp(Calendario.hoje().getTime()));
            stm.setTimestamp(3, new java.sql.Timestamp(aviso.getDataAte().getTime()));
            stm.setBoolean(4, (perfis == null || perfis.isEmpty())
                    && (usuarios == null || usuarios.isEmpty()));
            stm.setInt(5, aviso.getCodigo());
            stm.executeUpdate();
            executarConsulta("DELETE FROM avisointerno_perfilacesso WHERE avisointerno_codigo = " + aviso.getCodigo(), con);
            executarConsulta("DELETE FROM avisointerno_usuario WHERE avisointerno_codigo = " + aviso.getCodigo(), con);
            gravarPerfisUsuariosAvisos(perfis, usuarios, aviso.getCodigo());

        }
    }

    public void deletarAvisoInterno(int codigo) throws Exception {
        String sql = "DELETE FROM avisointerno WHERE codigo = ?";
        try (PreparedStatement stm = con.prepareStatement(sql)) {
            stm.setInt(1, codigo);
            stm.executeUpdate();
        }
    }

    public void inserirAvisoInterno(Integer empresa,
                                    String aviso,
                                    Integer autor,
                                    Date limite,
                                    List<PerfilAcessoVO> perfis,
                                    List<UsuarioVO> usuarios) throws Exception{
        String sql = "INSERT INTO avisointerno (conteudo, datapublicacao, dataexpiracao, autor, ativo, visivelparatodos, empresa) " +
                "VALUES (?, ?, ?, ?, ?, ?, ?)";
        try (PreparedStatement stm = con.prepareStatement(sql)) {
            stm.setString(1, aviso);
            stm.setTimestamp(2, new java.sql.Timestamp(Calendario.hoje().getTime()));
            stm.setTimestamp(3, new java.sql.Timestamp(limite.getTime()));
            stm.setInt(4, autor);
            stm.setBoolean(5, true);
            stm.setBoolean(6, (perfis == null || perfis.isEmpty())
                    && (usuarios == null || usuarios.isEmpty()));
            stm.setInt(7, empresa);
            stm.execute();
            Integer codigoAviso = obterValorChavePrimariaAvisoInterno();
            gravarPerfisUsuariosAvisos(perfis, usuarios, codigoAviso);
        }
    }

    private Integer obterValorChavePrimariaAvisoInterno() throws SQLException {
        String csCodigoGerado = "SELECT last_value FROM avisointerno_codigo_seq";
        Statement stmt = con.createStatement(ResultSet.TYPE_SCROLL_INSENSITIVE, ResultSet.CONCUR_READ_ONLY);
        ResultSet resultado = stmt.executeQuery(csCodigoGerado);
        resultado.first();
        return (resultado.getInt(1));
    }

    private void gravarPerfisUsuariosAvisos(List<PerfilAcessoVO> perfis, List<UsuarioVO> usuarios, Integer codigoAviso) throws SQLException {
        String sql;
        if(!UteisValidacao.emptyList(perfis)){
            for(PerfilAcessoVO perfil : perfis){
                sql = "INSERT INTO avisointerno_perfilacesso (avisointerno_codigo, perfilacesso_codigo) VALUES (?, ?)";
                try (PreparedStatement stmPerfil = con.prepareStatement(sql)) {
                    stmPerfil.setInt(1, codigoAviso);
                    stmPerfil.setInt(2, perfil.getCodigo());
                    stmPerfil.execute();
                }
            }
        }

        if(!UteisValidacao.emptyList(usuarios)){
            for(UsuarioVO usuario : usuarios){
                sql = "INSERT INTO avisointerno_usuario (avisointerno_codigo, usuario_codigo) VALUES (?, ?)";
                try (PreparedStatement stmUsuario = con.prepareStatement(sql)) {
                    stmUsuario.setInt(1, codigoAviso);
                    stmUsuario.setInt(2, usuario.getCodigo());
                    stmUsuario.execute();
                }
            }
        }
    }


}

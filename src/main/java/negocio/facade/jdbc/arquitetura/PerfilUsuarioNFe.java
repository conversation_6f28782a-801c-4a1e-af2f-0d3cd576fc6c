package negocio.facade.jdbc.arquitetura;

import negocio.comuns.arquitetura.PerfilUsuarioNFeVO;
import negocio.comuns.nfe.EmpresaNFeVO;
import negocio.comuns.utilitarias.ConsistirException;
import negocio.facade.jdbc.nfe.EmpresaNFe;
import negocio.interfaces.arquitetura.PerfilUsuarioNFeInterfaceFacade;

import javax.faces.model.SelectItem;
import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.Statement;
import java.util.ArrayList;
import java.util.List;

public class PerfilUsuarioNFe extends SuperEntidade implements PerfilUsuarioNFeInterfaceFacade {

    public PerfilUsuarioNFe() throws Exception {
        super();
    }

    public PerfilUsuarioNFe(Connection con) throws Exception {
        super(con);
    }

    @Override
    public PerfilUsuarioNFeVO novo() throws Exception {
        return new PerfilUsuarioNFeVO();
    }

    @Override
    public void incluir(PerfilUsuarioNFeVO obj) throws Exception {
        boolean sucesso = false;
        try {
            con.setAutoCommit(false);
            PerfilUsuarioNFeVO.validarDados(obj);
            validarDados(obj);

            String sql = "INSERT INTO PerfilUsuario(Nome, PermissaoCRUDUsuario, PermissaoIncluirEmpresa, " +
                    "PermissaoAlterarEmpresa, PermissaoAlterarEmpresaBasico,  PermissaoCancelamentoNota, " +
                    "PermissaoCRUDPerfil, Id_Empresa, permiteAlterarNumeroRPS) " +
                    "VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)";

            PreparedStatement sqlInserir = con.prepareStatement(sql);
            sqlInserir.setString(1, obj.getNomePerfilUsuario());
            sqlInserir.setBoolean(2, obj.isPermiteCRUD_Usuario());
            sqlInserir.setBoolean(3, obj.isPermiteIncluirEmpresa());
            sqlInserir.setBoolean(4, obj.isPermiteAlterarEmpresa());
            sqlInserir.setBoolean(5, obj.isPermiteAlterarBasicoEmpresa());
            sqlInserir.setBoolean(6, obj.isPermiteCancelarNota());
            sqlInserir.setBoolean(7, obj.isPermiteCRUD_Perfil());
            sqlInserir.setInt(8, obj.getEmpresa().getId_Empresa());
            sqlInserir.setBoolean(9, obj.isPermiteAlterarNumeroRPS());

            sqlInserir.execute();
            obj.setNovoObj(true);
            con.commit();
            sucesso = true;
        } catch (Exception e) {
            con.rollback();
            con.setAutoCommit(true);
            sucesso = false;
            throw e;
        } finally {
            con.setAutoCommit(true);

            if (sucesso) {
                int idDoPerfil = (obtenhaProximoIdDaTabela() - 1);
                obj.setCodigo(idDoPerfil);
            }
        }
    }


    private int obtenhaProximoIdDaTabela() throws Exception {
        String sql = "SELECT IDENT_CURRENT('PerfilUsuario') as id;";

        Statement stm = con.createStatement();
        ResultSet tabelaResultado = stm.executeQuery(sql);
        int proximoId = 0;
        if (tabelaResultado.next()) {
            proximoId = tabelaResultado.getInt("id") + 1;
        }

        return proximoId;
    }

    @Override
    public void alterar(PerfilUsuarioNFeVO obj) throws Exception {
        try {
            con.setAutoCommit(false);
            PerfilUsuarioNFeVO.validarDados(obj);
            validarDados(obj);

            String sql = "UPDATE PerfilUsuario set Nome = ?, PermissaoCRUDUsuario = ?, PermissaoIncluirEmpresa = ?, " +
                    "PermissaoAlterarEmpresa = ?, PermissaoAlterarEmpresaBasico = ?, PermissaoCancelamentoNota = ?, " +
                    "PermissaoCRUDPerfil=?, Id_Empresa =?, permiteAlterarNumeroRPS = ? WHERE Id_PerfilUsuario = ?";

            PreparedStatement sqlAlterar = con.prepareStatement(sql);
            sqlAlterar.setString(1, obj.getNomePerfilUsuario());
            sqlAlterar.setBoolean(2, obj.isPermiteCRUD_Usuario());
            sqlAlterar.setBoolean(3, obj.isPermiteIncluirEmpresa());
            sqlAlterar.setBoolean(4, obj.isPermiteAlterarEmpresa());
            sqlAlterar.setBoolean(5, obj.isPermiteAlterarBasicoEmpresa());
            sqlAlterar.setBoolean(6, obj.isPermiteCancelarNota());
            sqlAlterar.setBoolean(7, obj.isPermiteCRUD_Perfil());
            sqlAlterar.setInt(8, obj.getEmpresa().getId_Empresa());
            sqlAlterar.setBoolean(9, obj.isPermiteAlterarNumeroRPS());
            sqlAlterar.setInt(10, obj.getCodigo());

            sqlAlterar.execute();
            con.commit();
        } catch (Exception e) {
            con.rollback();
            con.setAutoCommit(true);
            throw e;
        } finally {
            con.setAutoCommit(true);
        }
    }

    private void validarDados(PerfilUsuarioNFeVO obj) throws Exception {
        if (existePerfilNaEmpresa(obj.getNomePerfilUsuario(), obj.getEmpresa().getId_Empresa(), obj.getCodigo())) {
            throw new ConsistirException("Nome de Perfil já cadastrado para a Empresa");
        }
    }

    @Override
    public void excluir(PerfilUsuarioNFeVO obj) throws Exception {
        try {
            con.setAutoCommit(false);
            String sql = "DELETE FROM PerfilUsuario WHERE (Id_PerfilUsuario = ?)";
            PreparedStatement sqlExcluir = con.prepareStatement(sql);
            sqlExcluir.setInt(1, obj.getCodigo());
            sqlExcluir.execute();
            con.commit();
        } catch (Exception e) {
            con.rollback();
            con.setAutoCommit(true);
            throw e;
        } finally {
            con.setAutoCommit(true);
        }
    }

    @Override
    public PerfilUsuarioNFeVO consultarPorChavePrimaria(Integer codigo) throws Exception {
        String sql = "SELECT * FROM PerfilUsuario WHERE Id_PerfilUsuario = ?";
        PreparedStatement sqlConsultar = con.prepareStatement(sql);
        sqlConsultar.setInt(1, codigo);
        ResultSet tabelaResultado = sqlConsultar.executeQuery();
        if (!tabelaResultado.next()) {
            throw new ConsistirException("Dados Não Encontrados ( PerfilUsuario ).");
        }
        return (montarDados(tabelaResultado, this.con));
    }

    private PerfilUsuarioNFeVO montarDados(ResultSet dadosSQL, Connection con) throws Exception {
        PerfilUsuarioNFeVO obj = new PerfilUsuarioNFeVO();
        obj.setNomePerfilUsuario(dadosSQL.getString("Nome"));
        obj.setPermiteCRUD_Usuario(dadosSQL.getBoolean("PermissaoCRUDUsuario"));
        obj.setPermiteIncluirEmpresa(dadosSQL.getBoolean("PermissaoIncluirEmpresa"));
        obj.setPermiteAlterarEmpresa(dadosSQL.getBoolean("PermissaoAlterarEmpresa"));
        obj.setPermiteAlterarBasicoEmpresa(dadosSQL.getBoolean("PermissaoAlterarEmpresaBasico"));
        obj.setPermiteCancelarNota(dadosSQL.getBoolean("PermissaoCancelamentoNota"));
        obj.setPermiteCRUD_Perfil(dadosSQL.getBoolean("PermissaoCRUDPerfil"));
        obj.setCodigo(dadosSQL.getInt("Id_PerfilUsuario"));
        obj.setPermiteAlterarNumeroRPS(dadosSQL.getBoolean("permiteAlterarNumeroRPS"));

        EmpresaNFe empresa = new EmpresaNFe(con);
        obj.setEmpresa(empresa.consultarPorChavePrimaria(dadosSQL.getInt("Id_Empresa")));

        return obj;
    }

    public List<PerfilUsuarioNFeVO> consultarPorCodigo(int codigo) throws Exception {
        List<PerfilUsuarioNFeVO> perfis = new ArrayList<PerfilUsuarioNFeVO>();

        StringBuilder sql = new StringBuilder("SELECT * FROM PerfilUsuario ");

        if (codigo != 0) {
            sql.append("WHERE Id_PerfilUsuario = ").append(codigo);
        }

        PreparedStatement sqlConsulta = con.prepareStatement(sql.toString());

        ResultSet tabelaResultado = sqlConsulta.executeQuery();
        while (tabelaResultado.next()) {
            PerfilUsuarioNFeVO perfil = montarDados(tabelaResultado, this.con);
            perfis.add(perfil);
        }

        return perfis;
    }

    public List<PerfilUsuarioNFeVO> consultarPorCodigoEEmpresa(int codigo, int id_empresa) throws Exception {
        List<PerfilUsuarioNFeVO> perfis = new ArrayList<PerfilUsuarioNFeVO>();

        StringBuilder sql = new StringBuilder("SELECT * FROM PerfilUsuario pu ");
        sql.append("WHERE pu.Id_Empresa = ").append(id_empresa);

        if (codigo != 0) {
            sql.append(" AND pu.Id_PerfilUsuario = ").append(codigo);
        }

        PreparedStatement sqlConsulta = con.prepareStatement(sql.toString());

        ResultSet tabelaResultado = sqlConsulta.executeQuery();
        while (tabelaResultado.next()) {
            PerfilUsuarioNFeVO perfil = montarDados(tabelaResultado, this.con);
            perfis.add(perfil);
        }

        return perfis;
    }


    public List<PerfilUsuarioNFeVO> consultarPorNome(String nome) throws Exception {
        List<PerfilUsuarioNFeVO> perfis = new ArrayList<PerfilUsuarioNFeVO>();

        StringBuilder sql = new StringBuilder("SELECT * FROM PerfilUsuario pu ");
        sql.append("WHERE pu.Nome LIKE '%").append(nome).append("%'");

        PreparedStatement sqlConsulta = con.prepareStatement(sql.toString());
        ResultSet tabelaResultado = sqlConsulta.executeQuery();

        while (tabelaResultado.next()) {
            PerfilUsuarioNFeVO perfil = montarDados(tabelaResultado, this.con);
            perfis.add(perfil);
        }

        return perfis;
    }

    public List<PerfilUsuarioNFeVO> consultarPorNomeEEmpresa(String nome, int id_empresa) throws Exception {
        List<PerfilUsuarioNFeVO> perfis = new ArrayList<PerfilUsuarioNFeVO>();

        StringBuilder sql = new StringBuilder("SELECT * FROM PerfilUsuario pu ");
        sql.append("WHERE pu.Nome LIKE '%").append(nome).append("%' ");
        sql.append("AND pu.Id_Empresa = ").append(id_empresa);

        PreparedStatement sqlConsulta = con.prepareStatement(sql.toString());
        ResultSet tabelaResultado = sqlConsulta.executeQuery();

        while (tabelaResultado.next()) {
            PerfilUsuarioNFeVO perfil = montarDados(tabelaResultado, this.con);
            perfis.add(perfil);
        }

        return perfis;
    }

    public List<PerfilUsuarioNFeVO> consultarPorNomeEmpresa(String nomeEmpresa, int id_empresa) throws Exception {
        List<PerfilUsuarioNFeVO> perfis = new ArrayList<PerfilUsuarioNFeVO>();

        StringBuilder sql = new StringBuilder("");

        sql.append("select \n");
        sql.append("p.* \n");
        sql.append("from PerfilUsuario p \n");
        sql.append("inner join empresa e on e.Id_Empresa = p.Id_Empresa \n");
        sql.append("where  1 = 1 \n");
        sql.append("and e.NomeFantasia like '%").append(nomeEmpresa).append("%' \n");

        if (id_empresa != 0) {
            sql.append("and p.Id_Empresa = ").append(id_empresa);
        }

        PreparedStatement sqlConsulta = con.prepareStatement(sql.toString());
        ResultSet tabelaResultado = sqlConsulta.executeQuery();

        while (tabelaResultado.next()) {
            PerfilUsuarioNFeVO perfil = montarDados(tabelaResultado, this.con);
            perfis.add(perfil);
        }

        return perfis;
    }

    public List<SelectItem> obtenhaPerfisDaEmpresa(EmpresaNFeVO empresa) throws Exception {
        List<SelectItem> perfis = new ArrayList<SelectItem>();

        String sql = "SELECT * FROM PerfilUsuario WHERE Id_Empresa = ?";
        PreparedStatement sqlConsultar = con.prepareStatement(sql);
        sqlConsultar.setInt(1, empresa.getId_Empresa());
        ResultSet tabelaResultado = sqlConsultar.executeQuery();

        while (tabelaResultado.next()) {
            perfis.add(new SelectItem(tabelaResultado.getInt("Id_PerfilUsuario"), tabelaResultado.getString("Nome")));
        }

        return perfis;
    }

    public void criarPerfilAdministrador(int idEmpresa) throws Exception {
        PerfilUsuarioNFeVO perfilPacto = new PerfilUsuarioNFeVO();

        perfilPacto.setNomePerfilUsuario("Administrador");
        perfilPacto.setPermiteCRUD_Usuario(true);
        perfilPacto.setPermiteIncluirEmpresa(true);
        perfilPacto.setPermiteAlterarEmpresa(true);
        perfilPacto.setPermiteAlterarBasicoEmpresa(true);
        perfilPacto.setPermiteCancelarNota(true);
        perfilPacto.setPermiteCRUD_Perfil(true);
        perfilPacto.getEmpresa().setId_Empresa(idEmpresa);

        incluir(perfilPacto);
    }

    public void criarPerfilConsultor(int idEmpresa) throws Exception {
        PerfilUsuarioNFeVO perfilPacto = new PerfilUsuarioNFeVO();

        perfilPacto.setNomePerfilUsuario("CONSULTOR");
        perfilPacto.setPermiteCRUD_Usuario(false);
        perfilPacto.setPermiteIncluirEmpresa(false);
        perfilPacto.setPermiteAlterarEmpresa(false);
        perfilPacto.setPermiteAlterarBasicoEmpresa(false);
        perfilPacto.setPermiteCancelarNota(true);
        perfilPacto.setPermiteCRUD_Perfil(false);
        perfilPacto.getEmpresa().setId_Empresa(idEmpresa);

        incluir(perfilPacto);
    }

    public int obtenhaIdDoPerfilAdministrador(int idDaEmpresa) throws Exception {
        return obtenhaIdDoPerfil("Administrador", idDaEmpresa);
    }

    public int obtenhaIdDoPerfilConsultor(int idDaEmpresa) throws Exception {
        return obtenhaIdDoPerfil("CONSULTOR", idDaEmpresa);
    }

    public int obtenhaIdDoPerfil(String nomeDoPerfil, int idDaEmpresa) throws Exception {
        String sql = "SELECT * FROM PerfilUsuario WHERE Nome Like ? AND Id_Empresa = ?";
        PreparedStatement sqlConsultar = con.prepareStatement(sql);
        sqlConsultar.setString(1, nomeDoPerfil);
        sqlConsultar.setInt(2, idDaEmpresa);

        ResultSet tabelaResultado = sqlConsultar.executeQuery();
        if (!tabelaResultado.next()) {
            throw new ConsistirException("Perfil " + nomeDoPerfil + " não está cadastrado.");
        }
        return tabelaResultado.getInt("Id_PerfilUsuario");
    }


    private boolean existePerfilNaEmpresa(String nome, int idEmpresa, int idPerfil) throws Exception {
        StringBuilder sqlStr = new StringBuilder("SELECT * FROM PerfilUsuario ");
        sqlStr.append("WHERE Nome like '").append(nome).append("' ");
        sqlStr.append("AND Id_Empresa = ").append(idEmpresa);
        sqlStr.append("AND Id_PerfilUsuario <> ").append(idPerfil);

        Statement stm = con.createStatement();
        ResultSet tabelaResultado = stm.executeQuery(sqlStr.toString());
        return tabelaResultado.next();
    }
}

package negocio.facade.jdbc.arquitetura;

import negocio.facade.jdbc.utilitarias.Conexao;

import java.sql.Connection;

/**
 * Created by ulisses on 07/02/2017.
 */
public class SuperEntidadeOAMD extends SuperFacadeJdbcOAMD {

    public SuperEntidadeOAMD(Connection connection)throws Exception{
        super(connection);
    }

    public Integer obterValorChavePrimariaCodigo() throws Exception {
        return Conexao.obterUltimoCodigoGeradoTabela(getConOAMD(), this.getClass().getSimpleName());
    }

    public Integer obterValorChavePrimariaCodigo(String identificadorPK) throws Exception {
        return Conexao.obterUltimoCodigoGeradoTabela(getConOAMD(), this.getClass().getSimpleName(), identificadorPK);
    }

    public Integer obterValorChavePrimariaCodigoTabela(String tabela) throws Exception {
        return Conexao.obterUltimoCodigoGeradoTabela(getConOAMD(), tabela);
    }

}

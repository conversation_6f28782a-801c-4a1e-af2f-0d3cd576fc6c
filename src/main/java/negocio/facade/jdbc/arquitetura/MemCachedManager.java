package negocio.facade.jdbc.arquitetura;

import br.com.pactosolucoes.comuns.util.JSFUtilities;
import negocio.comuns.arquitetura.BannerVO;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisValidacao;
import negocio.facade.jdbc.memcached.ObjetoCacheEnum;
import negocio.interfaces.arquitetura.CachedManagerInterfaceFacade;
import net.spy.memcached.AddrUtil;
import net.spy.memcached.ConnectionFactoryBuilder;
import net.spy.memcached.FailureMode;
import net.spy.memcached.MemcachedClient;
import net.spy.memcached.MemcachedNode;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.logging.Level;
import java.util.logging.Logger;

/**
 * Created by ulisses on 16/06/2016.
 */
public final class MemCachedManager implements CachedManagerInterfaceFacade {

    private static MemCachedManager INSTANCE;
    private MemcachedClient memcachedClient;
    private static final int TEMPOEXPIRAR = 60000;


    public MemCachedManager() {
        instanciarMemcachedClient();
    }

    private void instanciarMemcachedClient() {
        try {
            String xml = Uteis.getXMLDocumentCFG(Uteis.nomeArqCFG);
            String ipServidorMemcached = Uteis.getValorTAG(xml, "ipServidoresMemCached");
            ipServidorMemcached = isServidorMemCachedNaoConfigurado(ipServidorMemcached) ? Uteis.getServidorMemCached() : ipServidorMemcached;
            if (isServidorMemCachedNaoConfigurado(ipServidorMemcached)) {
                Uteis.logar(null, "SERVIDOR MEMCACHED NÃO CONFIGURADO. FAÇA A CONFIGURAÇÃO DA TAG ipServidoresMemCached EM cfgBD.xml ");
            } else {
                memcachedClient = new MemcachedClient(
                        new ConnectionFactoryBuilder().setDaemon(true).
                                setOpTimeout(5000).
                                setFailureMode(FailureMode.Retry).build(),
                        AddrUtil.getAddresses(ipServidorMemcached));
                gravar("ON", TEMPOEXPIRAR, "USO_MEMCACHED_STATUS", "-");

            }
        } catch (java.lang.Exception e) {
            Logger.getLogger(MemCachedManager.class.getName()).log(Level.SEVERE, "Não foi possível carregar a configuração dos servidores menCached. Erro:" + e.getMessage(), e);
        }
    }

    private boolean isServidorMemCachedNaoConfigurado(String ipServidorMemcached) {
        return UteisValidacao.emptyString(ipServidorMemcached) || ipServidorMemcached.equalsIgnoreCase("DISABLED");
    }

    public void gravar(Object obj) {
        try {
            if (this.memcachedClient != null && obj != null) {
                this.memcachedClient.set((String) JSFUtilities.getFromSession("key") + "_" + obj.getClass().getName(), TEMPOEXPIRAR, obj);
            }
        } catch (Exception ex) {
            Logger.getLogger(MemCachedManager.class.getName())
                    .log(Level.WARNING, "Falha ao gravar objeto na MemCached", ex);
        }
    }

    public void remover(Class classe) {
        try {
            if (this.memcachedClient != null) {
                this.memcachedClient.delete((String) JSFUtilities.getFromSession("key") + "_" + classe.getName());
            }
        } catch (Exception ex) {
            Logger.getLogger(MemCachedManager.class.getName())
                    .log(Level.WARNING, "Falha ao remover objeto da MemCached", ex);
        }
    }

    public void remover(Class classe, String identificador) {
        try {
            if (this.memcachedClient != null) {
                this.memcachedClient.delete(getChaveComIdentificador(classe, identificador, (String) JSFUtilities.getFromSession("key")));
            }
        } catch (Exception ex) {
            Logger.getLogger(MemCachedManager.class.getName())
                    .log(Level.WARNING, "Falha ao remover objeto da MemCached", ex);
        }
    }

    public void remover(Class classe, String identificador, String key) {
        try {
            if (this.memcachedClient != null) {
                this.memcachedClient.delete(getChaveComIdentificador(classe, identificador, key));
            }
        } catch (Exception ex) {
            Logger.getLogger(MemCachedManager.class.getName())
                    .log(Level.WARNING, "Falha ao remover objeto da MemCached", ex);
        }
    }

    public void gravar(Object obj, int tempoExpirar) {
        try {
            if (this.memcachedClient != null && obj != null) {
                this.memcachedClient.set((String) JSFUtilities.getFromSession("key") + "_" + obj.getClass().getName(), tempoExpirar, obj);
            }
        } catch (Exception ex) {
            Logger.getLogger(MemCachedManager.class.getName())
                    .log(Level.WARNING, "Falha ao gravar objeto na MemCached", ex);
        }
    }

    public void gravar(Object obj, int tempoExpirar, String identificador) {
        gravar(obj, tempoExpirar, identificador, (String) JSFUtilities.getFromSession("key"));
    }

    public void gravar(Object obj, int tempoExpirar, String identificador, String key) {
        try {
            if (this.memcachedClient != null && obj != null) {
                this.memcachedClient.set(getChaveComIdentificador(obj.getClass(), identificador, key), tempoExpirar, obj);
            }
        } catch (Exception ex) {
            Logger.getLogger(MemCachedManager.class.getName())
                    .log(Level.WARNING, "Falha ao gravar objeto na MemCached", ex);
        }
    }

    public void gravar(Object obj, String identificador, String key) {
        gravar(obj, TEMPOEXPIRAR, identificador, key);
    }

    public void gravar(Object obj, String identificador) {
        gravar(obj, TEMPOEXPIRAR, identificador, (String) JSFUtilities.getFromSession("key"));
    }


    public <T> T ler(Class classe) {
        try {
            if (this.memcachedClient != null) {
                return (T) this.memcachedClient.get((String) JSFUtilities.getFromSession("key") + "_" + classe.getName());
            }
        } catch (Exception ex) {
            Logger.getLogger(MemCachedManager.class.getName())
                    .log(Level.WARNING, "Falha ao ler objeto da MemCached", ex);
        }
        return null;
    }

    public boolean getMemcachedOn(){
        try {
           for(MemcachedNode node : getInstance ().getMemcachedClient().getNodeLocator().getAll()){
               if(node.isActive()){
                   return true;
               }
           }
            return false;
        }catch (Exception e){
            return false;
        }
    }

    public <T> T ler(Class classe, String identificador, String key) {
        try {
            if (this.memcachedClient != null) {
                return (T) this.memcachedClient.get(getChaveComIdentificador(classe, identificador, key));
            }
        } catch (Exception ex) {
            Logger.getLogger(MemCachedManager.class.getName())
                    .log(Level.WARNING, "Falha ao ler objeto da MemCached", ex);
        }
        return null;
    }

    public <T> T ler(Class classe, String identificador) {
        try {
            if (this.memcachedClient != null) {
                return (T) this.memcachedClient.get(getChaveComIdentificador(classe, identificador, (String) JSFUtilities.getFromSession("key")));
            }
        } catch (Exception ex) {
            Logger.getLogger(MemCachedManager.class.getName())
                    .log(Level.WARNING, "Falha ao ler objeto da MemCached", ex);
        }
        return null;
    }

    private String getChaveComIdentificador(Class classe, String identificador, String key) {
        return "["+key +"]["+ classe.getSimpleName() +"]["+ identificador+"]";
    }

    public static MemCachedManager getInstance(){
        if (INSTANCE == null)
            INSTANCE = new MemCachedManager();
        return INSTANCE;
    }

    public MemcachedClient getMemcachedClient() {
        return memcachedClient;
    }


    public void salvarEmCache(String key, String valor, ObjetoCacheEnum objetoCacheEnum, Integer tempoSegundosExpiracao) {
        if (this.memcachedClient != null) {
            Map<String, String> map = obterMapa(objetoCacheEnum);
            if (map == null) {
                map = new HashMap<>();
            }
            map.put(key.toUpperCase(), valor);
            this.memcachedClient.set(objetoCacheEnum.name(), tempoSegundosExpiracao, map);
        }
    }

    public String resetCacheMap(ObjetoCacheEnum objetoCacheEnum, Integer tempoSegundoExpiracaoCache) {
        try {
            Map<String, Object> map = (Map<String, Object>) this.memcachedClient.get(objetoCacheEnum.name());
            if (map != null) {
                map.clear();
            } else {
                map = new HashMap<>();
            }
            memcachedClient.set(objetoCacheEnum.name(), tempoSegundoExpiracaoCache, map);
            return "ok";
        } catch (Exception ex) {
            return "falha ao tentar resetar cache: " + ex.getMessage();
        }
    }

    public Map<String, String> obterMapa(ObjetoCacheEnum objetoCacheEnum) {
        if (this.memcachedClient != null && objetoCacheEnum != null) {
            return (Map<String, String>) this.memcachedClient.get(objetoCacheEnum.name());
        }
        return null;
    }
}

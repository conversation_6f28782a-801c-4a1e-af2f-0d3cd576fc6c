package negocio.facade.jdbc.arquitetura.consulta;

/**
 * Interface para as colunas prefixadas usadas na query do relatório
 *
 * <AUTHOR>
 * @since 07/08/2018
 */
public interface ColunaPrefixavel {

    /**
     * @return O label (nome) da coluna
     */
    String getLabel();

    /**
     * @return O label da coluna antecedido pelo prefixo (e.g.: PREFIXO.label)
     */
    String getLabelComPrefixo();

}

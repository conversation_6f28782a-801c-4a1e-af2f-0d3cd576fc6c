package negocio.facade.jdbc.arquitetura.consulta;

import negocio.comuns.utilitarias.Uteis;
import negocio.facade.jdbc.arquitetura.funcionais.Function;
import org.apache.commons.lang.StringUtils;

import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.sql.Statement;
import java.util.Collection;
import java.util.HashSet;
import java.util.Set;

/**
 * Utilitário para o relatório sintético de renovação
 *
 * <AUTHOR>
 * @since 07/08/2018
 */
public class ConsultaUtil {

    private static final String QUERY_PARAM = " ? ";

    /**
     * @param colunasPrefixadas A lista de colunas que implementam a interface {@link ColunaPrefixavel}
     * @return A lista de nomes das colunas separada por vírgula com seus respectivos alias
     */
    public static String getColunasPrefixadas(ColunaPrefixavel[] colunasPrefixadas) {
        StringBuilder prefixosContratosSeparadosVirgula = new StringBuilder();
        String virgula = "";
        for (ColunaPrefixavel coluna : colunasPrefixadas) {
            prefixosContratosSeparadosVirgula
                    .append(virgula)
                    .append(getColunaPrefixada(coluna));
            virgula = ",";
        }

        return prefixosContratosSeparadosVirgula.toString();
    }

    /**
     * Veja em {@link #getColunasPrefixadas(ColunaPrefixavel[])}.
     */
    public static String getColunasPrefixadasVarargs(ColunaPrefixavel... colunasPrefixadas) {
        return getColunasPrefixadas(colunasPrefixadas);
    }

    /**
     * @param coluna A coluna que será prefixada
     * @return O label da coluna com seu respectivo prefixo seguido de um alias.
     */
    public static String getColunaPrefixada(ColunaPrefixavel coluna) {
        return coluna.getLabelComPrefixo() + " AS \"" + coluna.getLabelComPrefixo() + "\"";
    }

    /**
     * Tenta fechar o resultSet silenciosamente. Caso não consiga, apenas logga a exceção e volta
     *
     * @param resultSet ResultSet que será fechado.
     */
    public static void fecharResultSetQuietly(ResultSet resultSet) {
        try {
            if (resultSet != null && !resultSet.isClosed()) {
                resultSet.close();
            }
        } catch (SQLException e) {
            // se nao for possivel fechar alguma stream entao apenas ignora a mesma
            Uteis.logar(e, ConsultaUtil.class);
        }
    }

    /**
     * Tenta fechar o statement silenciosamente. Caso não consiga, apenas logga a exceção e volta
     *
     * @param statement Statement que será fechado.
     */
    public static void fecharStatementQuietly(Statement statement) {
        try {
            if (statement != null && !statement.isClosed()) {
                statement.close();
            }
        } catch (SQLException e) {
            // se nao for possivel fechar alguma stream entao apenas ignora a mesma
            Uteis.logar(e, ConsultaUtil.class);
        }
    }

    /**
     * @return a partir do tamanho da collection <code>values</code>, concatena um {@link #QUERY_PARAM} e uma vírgula para cada valor.
     *         <br>Por exemplo, se você tem uma coleção de String:
     *         <ul>
     *            <li>[0] - "Batata"</li>
     *            <li>[1] - "Repolho"</li>
     *            <li>[2] - "Jiló"</li>
     *         </ul>
     *
     *         Retorna: " ?, ?, ? "
     */
    public static String returnStringParamsQueryPorLista(Collection<?> values) {
        StringBuilder sb = new StringBuilder();
        for (int i = 0; i < values.size(); i++) {
            sb.append(QUERY_PARAM).append(", ");
        }

        if (StringUtils.isNotBlank(sb.toString())) {
            return sb.substring(0, sb.toString().lastIndexOf(","));
        }

        return sb.toString();
    }

    /**
     * Veja em {@link #returnPreparedStatmentValoresSetados(Collection, PreparedStatement, int)}.
     *
     * Esta sobrecarga sempre começará setando o <code>preparedStatement</code> a partir do <b>index 1</b>. <br><b>Devolve o mesmo preparedStatement</b>.
     */
    public static PreparedStatement returnPreparedStatmentValoresSetados(Collection<?> values, PreparedStatement preparedStatement) throws SQLException {
        return returnPreparedStatmentValoresSetados(values, preparedStatement, 1);
    }

    /**
     * @return a partir do tamanho da collection <code>values</code> e de um <code>preparedStatement</code>, para cada item de <code>values</code>,
     *         é setado no <code>preparedStatement</code> conforme o tipo da collection.
     *
     *         <br>Por exemplo, se você tem uma coleção de inteiros:
     *         <ul>
     *            <li>[0] - 15</li>
     *            <li>[1] - 30</li>
     *            <li>[2] - 40</li>
     *         </ul>
     *
     *         É executado:
     *
     *         <ul>
     *            <li>preparedStatement.setInt(1, 15);</li>
     *            <li>preparedStatement.setInt(1, 30);</li>
     *            <li>preparedStatement.setInt(1, 40);</li>
     *        </ul>
     */
    public static PreparedStatement returnPreparedStatmentValoresSetados(Collection<?> values, PreparedStatement preparedStatement, int startIndex) throws SQLException {
        int i = startIndex;
        for (Object obj : values) {

            if (obj instanceof Integer) {
                Integer integerValue = (Integer) obj;

                if (integerValue != 0) {
                    preparedStatement.setInt(i++, integerValue);
                }
            } else if (obj instanceof String) {
                String stringValue = (String) obj;

                if (StringUtils.isNotBlank(stringValue)) {
                    preparedStatement.setString(i++, stringValue);
                }
            }

        }

        return preparedStatement;
    }

    /**
     * @param list que será iterada e para cada valor é considerado a <code>function</code> e concatenado em um <b>Set</b>.
     * @param function que receba um <code>&lt;T&gt;</code> e devolve um <code>&lt;R&gt;</code>, atráves de {@link Function#apply(Object)}.
     *
     * @return um <b>Set</b> de <code>&lt;R&gt;</code>.
     */
    public static <T, R> Set<R> concatenarValoresCollectionEmUmSet(Collection<T> list, Function<T, R> function) {
        Set<R> set = new HashSet<R>();

        for (T obj : list) {
            R returnedValue = function.apply(obj);

            if (returnedValue != null) {
                set.add(returnedValue);
            }
        }

        return set;
    }

}

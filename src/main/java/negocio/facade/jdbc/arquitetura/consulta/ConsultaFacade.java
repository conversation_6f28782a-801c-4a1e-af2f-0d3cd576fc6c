package negocio.facade.jdbc.arquitetura.consulta;

import negocio.comuns.utilitarias.Uteis;
import negocio.facade.jdbc.arquitetura.SuperEntidade;
import org.apache.commons.lang.StringUtils;

import java.sql.Connection;
import java.util.Collection;
import java.util.Date;

/**
 * Facade de consulta abstrato com algumas funcionalidades pré-definidas para auxiliar os demais facades
 *
 * <AUTHOR>
 * @since 14/08/2018
 */
public abstract class ConsultaFacade extends SuperEntidade {

    protected static final String SELECT = " SELECT ";
    protected static final String FROM = " FROM ";
    protected static final String DISTINCT = " DISTINCT ";
    protected static final String INNER_JOIN = " INNER JOIN ";
    protected static final String LEFT_JOIN = " LEFT JOIN ";
    protected static final String FULL_OUTER_JOIN = " FULL OUTER JOIN ";
    protected static final String ON = " ON ";
    protected static final String AND = " AND ";
    protected static final String OR = " OR ";
    protected static final String IN = " IN ( ";
    protected static final String IN_END = " ) ";
    protected static final String WHERE = " WHERE ";
    protected static final String INIT_WHERE = " 1 = 1 ";
    protected static final String ORDER_BY = " ORDER BY ";
    protected static final String DESC = " DESC ";
    protected static final String AS = " AS ";
    protected static final String IS_NULL = " is null ";
    protected static final String IS_NOT_NULL = " is not null ";
    protected static final String LIMIT = " LIMIT ";
    protected static final String ILIKE = " ILIKE ";
    protected static final String SUM = " SUM ";
    protected static final String COUNT = " COUNT ";
    protected static final String GROUP_BY = " GROUP BY ";

    /**
     * @throws Exception Caso haja algum problema na preparação da conexão com o banco
     */
    public ConsultaFacade() throws Exception {
        super();
    }

    public ConsultaFacade(Connection conexao) throws Exception {
        super(conexao);
    }

    protected static String andDiferente(ColunaPrefixavel coluna, Integer valor) {
        return AND + diferente(coluna, valor);
    }

    protected static String diferente(ColunaPrefixavel coluna, Integer valor) {
        return diferente(coluna.getLabelComPrefixo(), String.valueOf(valor));
    }

    protected static String diferente(String campo, String valor) {
        return campo + " <> " + valor;
    }

    protected static String andIgual(ColunaPrefixavel coluna, Integer valor) {
        return AND + igual(coluna, valor);
    }

    protected static String andIgual(ColunaPrefixavel coluna, String valor) {
        return AND + igual(coluna, valor);
    }

    protected static String igual(ColunaPrefixavel coluna, Integer valor) {
        return igual(coluna.getLabelComPrefixo(), String.valueOf(valor));
    }

    protected static String igual(ColunaPrefixavel coluna, String valor) {
        return igual(coluna.getLabelComPrefixo(), valor);
    }

    protected static String igual(ColunaPrefixavel colunaA, ColunaPrefixavel colunaB) {
        return igual(colunaA.getLabelComPrefixo(), colunaB.getLabelComPrefixo());
    }

    protected static String igual(String campo, String valor) {
        return campo + " = " + valor;
    }

    protected static String andIsNull(ColunaPrefixavel coluna) {
        return AND + isNull(coluna);
    }

    protected static String andIsNotNull(ColunaPrefixavel coluna) {
        return AND + isNotNull(coluna);
    }

    protected static String isNull(ColunaPrefixavel coluna) {
        return coluna.getLabelComPrefixo() + IS_NULL;
    }

    protected static String isNotNull(ColunaPrefixavel coluna) {
        return coluna.getLabelComPrefixo() + IS_NOT_NULL;
    }

    protected static String andIn(ColunaPrefixavel coluna, Collection<?> objetos) {
        return andIn(coluna.getLabelComPrefixo(), objetos);
    }

    protected static String andIn(ColunaPrefixavel coluna, String objetosSeparadosVirgula) {
        return andIn(coluna.getLabelComPrefixo(), objetosSeparadosVirgula);
    }

    protected static String andIn(String campo, Collection<?> objetos) {
        return andIn(campo, StringUtils.join(objetos, ","));
    }

    protected static String andIn(String campo, String objetosSeparadosVirgula) {
        return AND + in(campo, objetosSeparadosVirgula);
    }

    protected static String in(ColunaPrefixavel coluna, Collection<?> objetos) {
        return in(coluna.getLabelComPrefixo(), StringUtils.join(objetos, ","));
    }

    protected static String in(String campo, String objetosSeparadosVirgula) {
        return campo + IN + objetosSeparadosVirgula + IN_END;
    }

    protected static String innerJoin(String tabela, String prefixoTabela, ColunaPrefixavel campoTabela, ColunaPrefixavel campoExterno) {
        return join(INNER_JOIN, tabela, prefixoTabela, campoTabela.getLabelComPrefixo(), campoExterno.getLabelComPrefixo());
    }

    protected static String leftJoin(String tabela, String prefixoTabela, ColunaPrefixavel campoTabela, ColunaPrefixavel campoExterno) {
        return join(LEFT_JOIN, tabela, prefixoTabela, campoTabela.getLabelComPrefixo(), campoExterno.getLabelComPrefixo());
    }

    protected static String fullOuterJoin(String tabela, String prefixoTabela, ColunaPrefixavel campoTabela, ColunaPrefixavel campoExterno) {
        return join(FULL_OUTER_JOIN, tabela, prefixoTabela, campoTabela.getLabelComPrefixo(), campoExterno.getLabelComPrefixo());
    }

    private static String join(String tipoJoin, String tabela, String prefixoTabela, String campoTabela, String campoExterno) {
        return tipoJoin + tabela + " " + prefixoTabela + ON + igual(campoTabela, campoExterno);
    }

    protected static String limit(Integer valor) {
        return LIMIT + valor;
    }

    protected String andIsBoolean(ColunaPrefixavel coluna, boolean valor) {
        return valor ? andIgual(coluna, "TRUE") : andIgual(coluna, "FALSE");
    }

    protected String andEntre(ColunaPrefixavel coluna, String valorMinimo, String valorMaximo) {
        return andEntre(coluna.getLabelComPrefixo(), valorMinimo, valorMaximo);
    }

    protected String andEntre(ColunaPrefixavel coluna, Double valorMinimo, Double valorMaximo) {
        String valorMin = valorMinimo == null ? null : valorMinimo.toString();
        String valorMax = valorMaximo == null ? null : valorMaximo.toString();

        return andEntre(coluna.getLabelComPrefixo(), valorMin, valorMax);
    }

    protected String andLikeInsensitiveBothSides(ColunaPrefixavel coluna, String valor) {
        return AND + coluna.getLabelComPrefixo() + ILIKE + "'%" + valor + "%'";
    }

    protected String somar(ColunaPrefixavel coluna) {
        return SUM + "(" + coluna.getLabelComPrefixo() + ")";
    }

    protected String contar() {
        return contarPor("1");
    }

    protected String contar(ColunaPrefixavel coluna) {
        return contarPor(coluna.getLabelComPrefixo());
    }

    protected String contarPor(String valor) {
        return COUNT + "(" + valor + ")";
    }

    protected String agrupar(ColunaPrefixavel coluna) {
        return GROUP_BY + coluna.getLabelComPrefixo();
    }

    protected String andEntre(String campo, String valorMinimo, String valorMaximo) {

        if (StringUtils.isBlank(campo) || (StringUtils.isBlank(valorMinimo) && StringUtils.isBlank(valorMaximo))) {
            return "";
        }

        String query;
        if (valorMinimo == null) {
            query = AND + campo + " <= " + valorMaximo;
        } else if (valorMaximo == null) {
            query = AND + campo + " >= " + valorMinimo;
        } else {
            query = AND + campo + " >= " + valorMinimo + AND + campo + " <= " + valorMaximo;
        }

        return query;
    }

    protected String dataBetween(String campo, Date dataInicial, Date dataFinal) throws Exception {
        if (dataInicial == null && dataFinal == null) {
            return "";
        }

        String query;
        if (dataInicial == null) {
            query = campo + " <= '" + Uteis.getDataJDBC(dataFinal) + "'";
        } else if (dataFinal == null) {
            query = campo + " >= '" + Uteis.getDataJDBC(dataInicial) + "'";
        } else {
            query = campo + " >= '" + Uteis.getDataJDBC(dataInicial) + "'" + AND + campo + " <= '" + Uteis.getDataJDBC(dataFinal) + "'";
        }

        return query;
    }

}

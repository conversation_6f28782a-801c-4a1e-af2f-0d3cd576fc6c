package negocio.facade.jdbc.arquitetura;

import negocio.comuns.arquitetura.PerfilUsuarioNFeVO;
import negocio.comuns.arquitetura.UsuarioNFeVO;
import negocio.comuns.utilitarias.ConsistirException;
import negocio.comuns.utilitarias.Uteis;
import negocio.interfaces.arquitetura.UsuarioNFeIntefaceFacade;

import java.sql.*;
import java.util.ArrayList;
import java.util.List;

public class UsuarioNFe extends SuperEntidade implements UsuarioNFeIntefaceFacade {

    public UsuarioNFe() throws Exception {
        super();
    }

    @Override
    public UsuarioNFeVO novo() throws Exception {
        return new UsuarioNFeVO();
    }

    private int obtenhaProximoIdDaTabela() throws Exception {
        String sql = "SELECT IDENT_CURRENT('Usuario') as id;";

        Statement stm = con.createStatement();
        ResultSet tabelaResultado = stm.executeQuery(sql);
        int proximoId = 0;
        if (tabelaResultado.next()) {
            proximoId = tabelaResultado.getInt("id") + 1;
        }

        return proximoId;
    }

    @Override
    public void incluir(UsuarioNFeVO obj) throws Exception {
        boolean sucesso = false;
        try {
            con.setAutoCommit(false);
            UsuarioNFeVO.validarDados(obj);
            validarDados(obj);

            String sql = "INSERT INTO Usuario(Nome, Usuario, Senha, Status, Id_PerfilUsuario) VALUES (?, ?, ?, ?, ?)";

            PreparedStatement sqlInserir = con.prepareStatement(sql);
            sqlInserir.setString(1, obj.getNome());
            sqlInserir.setString(2, obj.getUsuario());
            obj.setSenha(Uteis.encriptarNFe(obj.getSenha()));
            sqlInserir.setString(3, obj.getSenha());
            sqlInserir.setBoolean(4, obj.getStatus());

            PerfilUsuarioNFeVO perfilAcessoVO = obj.getPerfilUsuarioNFe();

            if (perfilAcessoVO.getCodigo() == null || perfilAcessoVO.getCodigo() == 0) {
                sqlInserir.setNull(5, Types.NUMERIC);
            } else {
                sqlInserir.setInt(5, perfilAcessoVO.getCodigo());
            }

            sqlInserir.execute();
            obj.setNovoObj(true);
            con.commit();
            sucesso = true;
        } catch (Exception e) {
            con.rollback();
            con.setAutoCommit(true);
            sucesso = false;
            throw e;
        } finally {
            con.setAutoCommit(true);

            if (sucesso) {
                int idUsuario = (obtenhaProximoIdDaTabela() - 1);
                obj.setId_Usuario(idUsuario);
            }
        }
    }

    @Override
    public void alterar(UsuarioNFeVO obj) throws Exception {
        try {
            con.setAutoCommit(false);
            UsuarioNFeVO.validarDados(obj);
            validarDados(obj);

            String sql = "UPDATE Usuario set Nome=?, Usuario=?, Senha=?, Status=?, Id_PerfilUsuario=? WHERE Id_Usuario=?";
            PreparedStatement sqlAlterar = con.prepareStatement(sql);
            sqlAlterar.setString(1, obj.getNome());
            sqlAlterar.setString(2, obj.getUsuario());
            sqlAlterar.setString(3, Uteis.encriptarNFe(obj.getSenha()));
            sqlAlterar.setBoolean(4, obj.getStatus());
            sqlAlterar.setInt(5, obj.getPerfilUsuarioNFe().getCodigo());
            sqlAlterar.setInt(6, obj.getId_Usuario());

            sqlAlterar.execute();
            con.commit();
        } catch (Exception e) {
            con.rollback();
            con.setAutoCommit(true);
            throw e;
        } finally {
            con.setAutoCommit(true);
        }
    }

    @Override
    public void excluir(UsuarioNFeVO obj) throws Exception {
        try {
            con.setAutoCommit(false);
            String sql = "DELETE FROM Usuario WHERE (Id_Usuario = ?)";
            PreparedStatement sqlExcluir = con.prepareStatement(sql);
            sqlExcluir.setInt(1, obj.getId_Usuario());
            sqlExcluir.execute();
            con.commit();
        } catch (Exception e) {
            con.rollback();
            con.setAutoCommit(true);
            throw e;
        } finally {
            con.setAutoCommit(true);
        }
    }

    @Override
    public UsuarioNFeVO consultarPorChavePrimaria(Integer idUsuario) throws Exception {
        consultar(getIdEntidade(), false);
        String sql = "SELECT * FROM Usuario WHERE Id_Usuario = ? AND Usuario NOT LIKE 'admin'";
        PreparedStatement sqlConsultar = con.prepareStatement(sql);
        sqlConsultar.setInt(1, idUsuario);
        ResultSet tabelaResultado = sqlConsultar.executeQuery();
        if (!tabelaResultado.next()) {
            throw new ConsistirException("Dados Não Encontrados ( Usuário ).");
        }
        return (montarDados(tabelaResultado, this.con));
    }

    /**
     * Responsável por montar os dados resultantes de uma consulta ao banco de
     * dados (<code>ResultSet</code>) em um objeto da classe
     * <code>UsuarioVO</code>.
     *
     * @return O objeto da classe <code>UsuarioVO</code> com os dados
     *         devidamente montados.
     */
    public static UsuarioNFeVO montarDados(ResultSet dadosSQL, Connection con) throws Exception {
        UsuarioNFeVO obj = new UsuarioNFeVO();
        obj.setNovoObj(false);
        obj.setId_Usuario(dadosSQL.getInt("Id_Usuario"));
        obj.setNome(dadosSQL.getString("Nome"));
        obj.setUsuario(dadosSQL.getString("Usuario"));
        obj.setSenha(dadosSQL.getString("Senha"));
        obj.setSenhaDescriptografada(Uteis.desencriptarNFe(obj.getSenha()));
        obj.setStatus(dadosSQL.getBoolean("Status"));

        if (!obj.getUsuario().equalsIgnoreCase("admin")) {
            PerfilUsuarioNFe perfilUsuario = new PerfilUsuarioNFe(con);
            obj.setPerfilUsuarioNFe(perfilUsuario.consultarPorChavePrimaria(dadosSQL.getInt("Id_PerfilUsuario")));
        } else {
            obj.setAdministrador(true);
        }

        return obj;
    }

    @Override
    public List<UsuarioNFeVO> consultarPorCodigo(int codigo) throws Exception {
        List<UsuarioNFeVO> usuarios = new ArrayList<UsuarioNFeVO>();

        StringBuilder sql = new StringBuilder("SELECT * FROM Usuario u ");
        sql.append("WHERE Usuario NOT LIKE 'admin' ");

        if (codigo != 0) {
            sql.append("AND u.Id_Usuario = ").append(codigo);
        }

        PreparedStatement sqlConsulta = con.prepareStatement(sql.toString());

        ResultSet tabelaResultado = sqlConsulta.executeQuery();
        while (tabelaResultado.next()) {
            UsuarioNFeVO usuario = montarDados(tabelaResultado, this.con);
            usuarios.add(usuario);
        }

        return usuarios;
    }

    public List<UsuarioNFeVO> consultarPorCodigoEEmpresa(int codigo, int id_empresa) throws Exception {
        List<UsuarioNFeVO> usuarios = new ArrayList<UsuarioNFeVO>();

        StringBuilder sql = new StringBuilder("SELECT * FROM Usuario u ");
        sql.append("LEFT JOIN PerfilUsuario pu ");
        sql.append("ON u.Id_PerfilUsuario = pu.Id_PerfilUsuario ");
        sql.append("WHERE u.Usuario NOT LIKE 'admin' ");
        sql.append("AND pu.Id_Empresa = ").append(id_empresa);

        if (codigo != 0) {
            sql.append(" AND u.Id_Usuario = ").append(codigo);
        }

        PreparedStatement sqlConsulta = con.prepareStatement(sql.toString());

        ResultSet tabelaResultado = sqlConsulta.executeQuery();
        while (tabelaResultado.next()) {
            UsuarioNFeVO usuario = montarDados(tabelaResultado, this.con);
            usuarios.add(usuario);
        }

        return usuarios;
    }

    public List<UsuarioNFeVO> consultarPorNome(String nome) throws Exception {
        List<UsuarioNFeVO> usuarios = new ArrayList<UsuarioNFeVO>();

        String sql = "SELECT * FROM Usuario WHERE Nome LIKE '%" + nome + "%' AND Usuario NOT LIKE 'admin';";

        PreparedStatement sqlConsulta = con.prepareStatement(sql);
        ResultSet tabelaResultado = sqlConsulta.executeQuery();

        while (tabelaResultado.next()) {
            UsuarioNFeVO usuario = montarDados(tabelaResultado, this.con);
            usuarios.add(usuario);
        }

        return usuarios;
    }

    public List<UsuarioNFeVO> consultarPorNomeEEmpresa(String nome, int id_empresa) throws Exception {
        List<UsuarioNFeVO> usuarios = new ArrayList<UsuarioNFeVO>();

        StringBuilder sql = new StringBuilder("SELECT * FROM Usuario u ");
        sql.append("LEFT JOIN PerfilUsuario pu ");
        sql.append("ON u.Id_PerfilUsuario = pu.Id_PerfilUsuario ");
        sql.append("WHERE u.Usuario NOT LIKE 'admin' ");
        sql.append("AND pu.Id_Empresa = ? ");
        sql.append("AND u.Nome LIKE '%").append(nome).append("%'");

        PreparedStatement sqlConsulta = con.prepareStatement(sql.toString());
        sqlConsulta.setInt(1, id_empresa);

        ResultSet tabelaResultado = sqlConsulta.executeQuery();
        while (tabelaResultado.next()) {
            UsuarioNFeVO usuario = montarDados(tabelaResultado, this.con);
            usuarios.add(usuario);
        }

        return usuarios;
    }

    public List<UsuarioNFeVO> consultarPorUsername(String username) throws Exception {
        List<UsuarioNFeVO> usuarios = new ArrayList<UsuarioNFeVO>();

        String sql = "SELECT * FROM Usuario WHERE Usuario LIKE '%" + username + "%' AND Usuario NOT LIKE 'admin';";

        PreparedStatement sqlConsulta = con.prepareStatement(sql);
        ResultSet tabelaResultado = sqlConsulta.executeQuery();

        while (tabelaResultado.next()) {
            UsuarioNFeVO usuario = montarDados(tabelaResultado, this.con);
            usuarios.add(usuario);
        }

        return usuarios;
    }

    public List<UsuarioNFeVO> consultarPorUsernameEEmpresa(String username, int id_empresa) throws Exception {
        List<UsuarioNFeVO> usuarios = new ArrayList<UsuarioNFeVO>();

        StringBuilder sql = new StringBuilder("SELECT * FROM Usuario u ");
        sql.append("LEFT JOIN PerfilUsuario pu ");
        sql.append("ON u.Id_PerfilUsuario = pu.Id_PerfilUsuario ");
        sql.append("WHERE u.Usuario NOT LIKE 'admin' ");
        sql.append("AND pu.Id_Empresa = ? ");
        sql.append("AND u.Usuario LIKE '%").append(username).append("%'");

        PreparedStatement sqlConsulta = con.prepareStatement(sql.toString());
        sqlConsulta.setInt(1, id_empresa);

        ResultSet tabelaResultado = sqlConsulta.executeQuery();
        while (tabelaResultado.next()) {
            UsuarioNFeVO usuario = montarDados(tabelaResultado, this.con);
            usuarios.add(usuario);
        }

        return usuarios;
    }

    public List<UsuarioNFeVO> consultarPorNomeEmpresa(String nomeEmpresa, int id_empresa) throws Exception {
        List<UsuarioNFeVO> usuarios = new ArrayList<UsuarioNFeVO>();

        StringBuilder sql = new StringBuilder();

        sql.append("select \n");
        sql.append("u.* \n");
        sql.append("from usuario u \n");
        sql.append("inner join PerfilUsuario p on p.Id_PerfilUsuario = u.Id_PerfilUsuario \n");
        sql.append("inner join empresa e on e.Id_Empresa = p.Id_Empresa \n");
        sql.append("where 1 = 1 \n");
        sql.append("and e.nomefantasia like '%").append(nomeEmpresa).append("%' \n");

        if(id_empresa != 0) {
            sql.append("and Id_Empresa = ").append(id_empresa);
        }

        PreparedStatement sqlConsulta = con.prepareStatement(sql.toString());

        ResultSet tabelaResultado = sqlConsulta.executeQuery();
        while (tabelaResultado.next()) {
            UsuarioNFeVO usuario = montarDados(tabelaResultado, this.con);
            usuarios.add(usuario);
        }

        return usuarios;
    }

    public boolean existeUsernameNaEmpresa(String usuario, int idEmpresa, int idUsuario) throws Exception {
        StringBuilder sqlStr = new StringBuilder("SELECT * FROM Usuario u ");
        sqlStr.append("LEFT JOIN PerfilUsuario pu ON u.Id_PerfilUsuario = pu.Id_PerfilUsuario ");
        sqlStr.append("WHERE Usuario like '").append(usuario).append("' ");
        sqlStr.append("AND Id_Empresa = ").append(idEmpresa);
        sqlStr.append("AND Id_Usuario <> ").append(idUsuario);

        Statement stm = con.createStatement();
        ResultSet tabelaResultado = stm.executeQuery(sqlStr.toString());
        return tabelaResultado.next();
    }

    public void criarUsuarioPacto(int idDoPerfilPacto) throws Exception {
        UsuarioNFeVO usuarioPacto = new UsuarioNFeVO();
        usuarioPacto.setNome("PACTOBR");
        usuarioPacto.setUsuario("PACTOBR");
        usuarioPacto.setSenha("P4ZW15PMG");
        usuarioPacto.setStatus(true);
        usuarioPacto.getPerfilUsuarioNFe().setCodigo(idDoPerfilPacto);

        incluir(usuarioPacto);
    }

    private void validarDados(UsuarioNFeVO obj) throws Exception {
        int idEmpresa = obj.getPerfilUsuarioNFe().getEmpresa().getId_Empresa();
        if (existeUsernameNaEmpresa(obj.getUsername(), idEmpresa, obj.getId_Usuario())) {
            throw new ConsistirException("Nome de Usuário já cadastrado para esta Empresa");
        }
    }
}
package negocio.facade.jdbc.arquitetura;

import negocio.comuns.arquitetura.LogProcessoSistemaVO;
import negocio.comuns.utilitarias.Calendario;
import negocio.comuns.utilitarias.Uteis;
import negocio.interfaces.arquitetura.LogProcessoSistemaInterfaceFacade;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.Types;

public class LogProcessoSistema extends SuperEntidade implements LogProcessoSistemaInterfaceFacade {

    protected static String idEntidade;

    public LogProcessoSistema() throws Exception {
        super();
    }

    public LogProcessoSistema(Connection con) throws Exception {
        super(con);
    }

    @Override
    public LogProcessoSistemaVO incluir(LogProcessoSistemaVO obj) throws Exception {
        String sql = "INSERT INTO logprocessosistema(processo, inicio, final, usuario, usuariooamd) VALUES (?, ?, ?, ?, ?) RETURNING codigo";
        try (PreparedStatement sqlInserir = con.prepareStatement(sql)) {
            int i = 0;
            sqlInserir.setString(++i, obj.getProcesso());
            sqlInserir.setTimestamp(++i, Uteis.getDataJDBCTimestamp(obj.getDataInicio()));
            if (obj.getDataFinal() == null) {
                sqlInserir.setNull(++i, Types.TIMESTAMP);
            } else {
                sqlInserir.setTimestamp(++i, Uteis.getDataJDBCTimestamp(obj.getDataFinal()));
            }
            sqlInserir.setInt(++i, obj.getUsuario().getCodigo());
            sqlInserir.setString(++i, obj.getUsuariooamd());
            try (ResultSet rs = sqlInserir.executeQuery()) {
                if (rs.next()) {
                    obj.setCodigo(rs.getInt("codigo"));
                }
            }
        }
        return obj;
    }

    @Override
    public void finalizar(LogProcessoSistemaVO obj) throws Exception {
        String sql = "UPDATE logprocessosistema SET final = ? WHERE codigo = ?";
        try (PreparedStatement sqlAlterar = con.prepareStatement(sql)) {
            int i = 0;
            sqlAlterar.setTimestamp(++i, Uteis.getDataJDBCTimestamp(Calendario.hoje()));
            sqlAlterar.setInt(++i, obj.getCodigo());
            sqlAlterar.execute();
        }
    }
}

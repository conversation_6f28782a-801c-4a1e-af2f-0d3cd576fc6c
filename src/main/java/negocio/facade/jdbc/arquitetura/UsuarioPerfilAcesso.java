package negocio.facade.jdbc.arquitetura;

import java.sql.Connection;

import br.com.pactosolucoes.comuns.util.JSFUtilities;
import negocio.comuns.basico.EstadoVO;
import negocio.facade.jdbc.basico.*;
import java.util.Iterator;
import java.sql.ResultSet;
import java.sql.PreparedStatement;
import java.sql.Statement;
import java.util.List;
import java.util.ArrayList;
import javax.faces.model.SelectItem;
import negocio.comuns.utilitarias.*;
import negocio.comuns.arquitetura.*;
import negocio.comuns.basico.EmpresaVO;

/**
 * Classe de persistência que encapsula todas as operações de manipulação dos dados da classe <code>UsuarioPerfilAcessoVO</code>.
 * Responsável por implementar operações como incluir, alterar, excluir e consultar pertinentes a classe <code>UsuarioPerfilAcessoVO</code>.
 * Encapsula toda a interação com o banco de dados.
 * @see UsuarioPerfilAcessoVO
 * @see SuperEntidade
 * @see Cliente
 */
public class UsuarioPerfilAcesso extends SuperEntidade {

    public UsuarioPerfilAcesso() throws Exception {
        super();
        setIdEntidade("Usuario");
    }

    public UsuarioPerfilAcesso(Connection conexao) throws Exception {
        super(conexao);
        setIdEntidade("Usuario");
    }

    /**
     * Operação responsável por retornar um novo objeto da classe <code>UsuarioPerfilAcessoVO</code>.
     */
    public UsuarioPerfilAcessoVO novo() throws Exception {
        incluir(getIdEntidade());
        UsuarioPerfilAcessoVO obj = new UsuarioPerfilAcessoVO();
        return obj;
    }

    /**
     * Operação responsável por incluir no banco de dados um objeto da classe <code>UsuarioPerfilAcessoVO</code>.
     * Primeiramente valida os dados (<code>validarDados</code>) do objeto. Verifica a conexão com o banco de dados e a permissão do usuário
     * para realizar esta operacão na entidade.
     * Isto, através da operação <code>incluir</code> da superclasse.
     * @param obj  Objeto da classe <code>UsuarioPerfilAcessoVO</code> que será gravado no banco de dados.
     * @exception Exception Caso haja problemas de conexão, restrição de acesso ou validação de dados.
     */
    public void incluir(UsuarioPerfilAcessoVO obj) throws Exception {
        UsuarioPerfilAcessoVO.validarDados(obj);
        //incluir(getIdEntidade());
        PerfilAcesso perfilAcesso = new PerfilAcesso(con);
        String sql = "INSERT INTO UsuarioPerfilAcesso( usuario, empresa, perfilAcesso, unificado) VALUES ( ?, ?, ?, ?)";

        try (PreparedStatement sqlInserir = con.prepareStatement(sql)) {
            sqlInserir.setInt(1, obj.getUsuario().intValue());
            sqlInserir.setInt(2, obj.getEmpresa().getCodigo().intValue());
            sqlInserir.setInt(3, obj.getPerfilAcesso().getCodigo().intValue());
            sqlInserir.setBoolean(4, obj.getPerfilAcesso().isUnificado());
            sqlInserir.execute();
        }
        obj.setCodigo(obterValorChavePrimariaCodigo());
        obj.setNovoObj(new Boolean(false));
    }

    /**
     * Operação responsável por incluir no banco de dados um objeto da classe <code>UsuarioPerfilAcessoVO</code>.
     * Primeiramente valida os dados (<code>validarDados</code>) do objeto. Verifica a conexão com o banco de dados e a permissão do usuário
     * para realizar esta operacão na entidade.
     * Isto, através da operação <code>incluir</code> da superclasse.
     * @param obj  Objeto da classe <code>UsuarioPerfilAcessoVO</code> que será gravado no banco de dados.
     * @exception Exception Caso haja problemas de conexão, restrição de acesso ou validação de dados.
     */
    public void incluirAdministrador(UsuarioPerfilAcessoVO obj) throws Exception {
        // UsuarioPerfilAcessoVO.validarDados(obj);
        incluir(getIdEntidade());
        String sql = "INSERT INTO UsuarioPerfilAcesso( usuario, empresa, perfilAcesso) VALUES ( ?, ?, ?)";
        try (PreparedStatement sqlInserir = con.prepareStatement(sql)) {
            sqlInserir.setInt(1, obj.getUsuario().intValue());
            sqlInserir.setInt(2, obj.getEmpresa().getCodigo().intValue());
            sqlInserir.setInt(3, obj.getPerfilAcesso().getCodigo().intValue());
            sqlInserir.execute();
        }
        obj.setCodigo(obterValorChavePrimariaCodigo());
        obj.setNovoObj(new Boolean(false));
    }

    /**
     * Operação responsável por alterar no BD os dados de um objeto da classe <code>UsuarioPerfilAcessoVO</code>.
     * Sempre utiliza a chave primária da classe como atributo para localização do registro a ser alterado.
     * Primeiramente valida os dados (<code>validarDados</code>) do objeto. Verifica a conexão com o banco de dados e a permissão do usuário
     * para realizar esta operacão na entidade.
     * Isto, através da operação <code>alterar</code> da superclasse.
     * @param obj    Objeto da classe <code>UsuarioPerfilAcessoVO</code> que será alterada no banco de dados.
     * @exception Exception Caso haja problemas de conexão, restrição de acesso ou validação de dados.
     */
    public void alterar(UsuarioPerfilAcessoVO obj) throws Exception {
        UsuarioPerfilAcessoVO.validarDados(obj);
        //alterar(getIdEntidade());
        String sql = "UPDATE UsuarioPerfilAcesso set usuario=?, empresa=?, perfilAcesso=? WHERE ((codigo = ?))";
        try (PreparedStatement sqlAlterar = con.prepareStatement(sql)) {
            sqlAlterar.setInt(1, obj.getUsuario().intValue());
            sqlAlterar.setInt(2, obj.getEmpresa().getCodigo().intValue());
            sqlAlterar.setInt(3, obj.getPerfilAcesso().getCodigo().intValue());
            sqlAlterar.setInt(4, obj.getCodigo().intValue());
            sqlAlterar.execute();
        }
    }

    /**
     * Operação responsável por excluir no BD um objeto da classe <code>UsuarioPerfilAcessoVO</code>.
     * Sempre localiza o registro a ser excluído através da chave primária da entidade.
     * Primeiramente verifica a conexão com o banco de dados e a permissão do usuário
     * para realizar esta operacão na entidade.
     * Isto, através da operação <code>excluir</code> da superclasse.
     * @param obj    Objeto da classe <code>UsuarioPerfilAcessoVO</code> que será removido no banco de dados.
     * @exception Exception Caso haja problemas de conexão ou restrição de acesso.
     */
    public void excluir(UsuarioPerfilAcessoVO obj) throws Exception {
        excluir(getIdEntidade());
        String sql = "DELETE FROM UsuarioPerfilAcesso WHERE ((codigo = ?))";
        try (PreparedStatement sqlExcluir = con.prepareStatement(sql)) {
            sqlExcluir.setInt(1, obj.getCodigo().intValue());
            sqlExcluir.execute();
        }
    }

    /**
     * Responsável por realizar uma consulta de <code>UsuarioPerfilAcesso</code> através do valor do atributo 
     * <code>nome</code> da classe <code>Cidade</code>
     * Faz uso da operação <code>montarDadosConsulta</code> que realiza o trabalho de prerarar o List resultante.
     * @return  List Contendo vários objetos da classe <code>UsuarioPerfilAcessoVO</code> resultantes da consulta.
     * @exception Exception Caso haja problemas de conexão ou restrição de acesso.
     */
    public List consultarPorNomeEmpresa(String valorConsulta) throws Exception {
        consultar(getIdEntidade(), true);
        String sqlStr = "SELECT UsuarioPerfilAcesso.* FROM UsuarioPerfilAcesso, Empresa WHERE UsuarioPerfilAcesso.empresa = Empresa.codigo and upper( Empresa.nomeFantasia ) like('" + valorConsulta.toUpperCase() + "%') ORDER BY Empresa.nomeFantasia";
        try (Statement stm = con.createStatement()) {
            try (ResultSet tabelaResultado = stm.executeQuery(sqlStr)) {
                return montarDadosConsulta(tabelaResultado, this.con);
            }
        }
    }

    /**
     * Responsável por realizar uma consulta de <code>UsuarioPerfilAcesso</code> através do valor do atributo 
     * <code>Integer codigo</code>. Retorna os objetos com valores iguais ou superiores ao parâmetro fornecido.
     * Faz uso da operação <code>montarDadosConsulta</code> que realiza o trabalho de prerarar o List resultante.
     * @param   controlarAcesso Indica se a aplicação deverá verificar se o usuário possui permissão para esta consulta ou não.
     * @return  List Contendo vários objetos da classe <code>UsuarioPerfilAcessoVO</code> resultantes da consulta.
     * @exception Exception Caso haja problemas de conexão ou restrição de acesso.
     */
    public List consultarPorCodigo(Integer valorConsulta, boolean controlarAcesso) throws Exception {
        consultar(getIdEntidade(), controlarAcesso);
        String sqlStr = "SELECT * FROM UsuarioPerfilAcesso WHERE codigo >= " + valorConsulta.intValue() + " ORDER BY codigo";
        try (Statement stm = con.createStatement()) {
            try (ResultSet tabelaResultado = stm.executeQuery(sqlStr)) {
                return (montarDadosConsulta(tabelaResultado, this.con));
            }
        }
    }

    /**
     * Responsável por montar os dados de vários objetos, resultantes de uma consulta ao banco de dados (<code>ResultSet</code>).
     * Faz uso da operação <code>montarDados</code> que realiza o trabalho para um objeto por vez.
     * @return  List Contendo vários objetos da classe <code>UsuarioPerfilAcessoVO</code> resultantes da consulta.
     */
    public static List montarDadosConsulta(ResultSet tabelaResultado, Connection con) throws Exception {
        List vetResultado = new ArrayList();
        while (tabelaResultado.next()) {
            UsuarioPerfilAcessoVO obj = new UsuarioPerfilAcessoVO();
            obj = montarDados(tabelaResultado, Uteis.NIVELMONTARDADOS_DADOSBASICOS, con);
            vetResultado.add(obj);
        }
        return vetResultado;
    }

    private static UsuarioPerfilAcessoVO montarDadosBasico(ResultSet dadosSQL) throws Exception {
        UsuarioPerfilAcessoVO obj = new UsuarioPerfilAcessoVO();
        obj.setNovoObj(new Boolean(false));
        obj.setCodigo(new Integer(dadosSQL.getInt("codigo")));
        obj.setUsuario(new Integer(dadosSQL.getInt("usuario")));
        obj.getEmpresa().setCodigo(dadosSQL.getInt("empresa"));
        obj.getPerfilAcesso().setCodigo(new Integer(dadosSQL.getInt("perfilAcesso")));
        return obj;

    }

    /**
     * Responsável por montar os dados resultantes de uma consulta ao banco de dados (<code>ResultSet</code>)
     * em um objeto da classe <code>UsuarioPerfilAcessoVO</code>.
     * @return  O objeto da classe <code>UsuarioPerfilAcessoVO</code> com os dados devidamente montados.
     */
    public static UsuarioPerfilAcessoVO montarDados(ResultSet dadosSQL, int nivelMontarDados, Connection con) throws Exception {
        UsuarioPerfilAcessoVO obj = montarDadosBasico(dadosSQL);

        if (nivelMontarDados == Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA) {
            return obj;
        }
        if (nivelMontarDados == Uteis.NIVELMONTARDADOS_MINIMOS) {
            montarDadosPerfilAcesso(obj, con);
            return obj;
        }
        if (nivelMontarDados == Uteis.NIVELMONTARDADOS_DADOSBASICOS) {
            montarDadosEmpresa(obj, con);
            montarDadosPerfilAcesso(obj, con);
            return obj;
        }
        if (nivelMontarDados == Uteis.NIVELMONTARDADOS_LOGIN) {
            montarDadosEmpresaLogin(obj, con);
            montarDadosPerfilAcesso(obj, con);
            return obj;
        }
        return obj;
    }

    /**
     * Operação responsável por montar os dados de um objeto da classe <code>CidadeVO</code> relacionado ao objeto <code>UsuarioPerfilAcessoVO</code>.
     * Faz uso da chave primária da classe <code>CidadeVO</code> para realizar a consulta.
     * @param obj  Objeto no qual será montado os dados consultados.
     */
    public static void montarDadosEmpresa(UsuarioPerfilAcessoVO obj, Connection con) throws Exception {
        if (obj.getEmpresa().getCodigo().intValue() == 0) {
            obj.setEmpresa(new EmpresaVO());
            return;
        }
        Empresa empresa = new Empresa(con);
        obj.setEmpresa(empresa.consultarPorChavePrimaria(obj.getEmpresa().getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS));
        empresa = null;
    }

    public static void montarDadosEmpresaLogin(UsuarioPerfilAcessoVO obj, Connection con) throws Exception {
        if (obj.getEmpresa().getCodigo().intValue() == 0) {
            obj.setEmpresa(new EmpresaVO());
            return;
        }
        Empresa empresa = new Empresa(con);
        obj.setEmpresa(empresa.consultarPorChavePrimaria(obj.getEmpresa().getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS));
        empresa = null;
    }

    /**
     * Operação responsável por montar os dados de um objeto da classe <code>CidadeVO</code> relacionado ao objeto <code>UsuarioPerfilAcessoVO</code>.
     * Faz uso da chave primária da classe <code>CidadeVO</code> para realizar a consulta.
     * @param obj  Objeto no qual será montado os dados consultados.
     */
    public static void montarDadosPerfilAcesso(UsuarioPerfilAcessoVO obj, Connection con) throws Exception {
        if (obj.getPerfilAcesso().getCodigo() == 0) {
            obj.setPerfilAcesso(new PerfilAcessoVO());
            return;
        }
        PerfilAcesso perfilAcesso = new PerfilAcesso(con);
        obj.setPerfilAcesso(perfilAcesso.consultarPorChavePrimaria(obj.getPerfilAcesso().getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS));
        perfilAcesso = null;
    }

    /**
     * Operação responsável por excluir todos os objetos da <code>UsuarioPerfilAcessoVO</code> no BD.
     * Faz uso da operação <code>excluir</code> disponível na classe <code>UsuarioPerfilAcesso</code>.
     * @param usuario campo chave para exclusão dos objetos no BD.
     * @exception Exception  Erro de conexão com o BD ou restrição de acesso a esta operação.
     */
    public void excluirUsuarioPerfilAcesso(Integer usuario) throws Exception {
        //UsuarioPerfilAcesso.excluir(getIdEntidade());
        PerfilAcesso perfilAcesso = new PerfilAcesso(con);
        String sql = "DELETE FROM UsuarioPerfilAcesso WHERE (usuario = ?) ";

        try (PreparedStatement sqlExcluir = con.prepareStatement(sql)) {
            sqlExcluir.setInt(1, usuario.intValue());
            sqlExcluir.execute();
        }
    }

    /**
     * Operação responsável por alterar todos os objetos da <code>UsuarioPerfilAcessoVO</code> contidos em um Hashtable no BD.
     * Faz uso da operação <code>excluirUsuarioPerfilAcessos</code> e <code>incluirUsuarioPerfilAcessos</code> disponíveis na classe <code>UsuarioPerfilAcesso</code>.
     * @param objetos  List com os objetos a serem alterados ou incluídos no BD.
     * @exception Exception  Erro de conexão com o BD ou restrição de acesso a esta operação.
     */
    public void alterarUsuarioPerfilAcesso(Integer usuario, List objetos) throws Exception {
        excluirUsuarioPerfilAcesso(usuario);
        incluirUsuarioPerfilAcesso(usuario, objetos);
    }

    /**
     * Operação responsável por incluir objetos da <code>UsuarioPerfilAcessoVO</code> no BD.
     * Garantindo o relacionamento com a entidade principal <code>cadastro.Cliente</code> através do atributo de vínculo.
     * @param objetos List contendo os objetos a serem gravados no BD da classe.
     * @exception Exception  Erro de conexão com o BD ou restrição de acesso a esta operação.
     */
    public void incluirUsuarioPerfilAcesso(Integer usuarioprm, List objetos) throws Exception {
        Iterator e = objetos.iterator();
        while (e.hasNext()) {
            UsuarioPerfilAcessoVO obj = (UsuarioPerfilAcessoVO) e.next();
            obj.setUsuario(usuarioprm);
            incluir(obj);
        }

    }

    /**
     * Operação responsável por incluir objetos da <code>UsuarioPerfilAcessoVO</code> no BD.
     * Garantindo o relacionamento com a entidade principal <code>cadastro.Cliente</code> através do atributo de vínculo.
     * @param objetos List contendo os objetos a serem gravados no BD da classe.
     * @exception Exception  Erro de conexão com o BD ou restrição de acesso a esta operação.
     */
    public void incluirUsuarioPerfilAcessoAdministrador(Integer usuarioprm, List objetos) throws Exception {
        Iterator e = objetos.iterator();
        while (e.hasNext()) {
            UsuarioPerfilAcessoVO obj = (UsuarioPerfilAcessoVO) e.next();
            obj.setUsuario(usuarioprm);
            incluirAdministrador(obj);
        }
    }

    /**
     * Operação responsável por consultar todos os <code>UsuarioPerfilAcessoVO</code> relacionados a um objeto da classe <code>cadastro.Cliente</code>.
     * @param usuario  Atributo a ser utilizado para localizar os objetos da classe <code>UsuarioPerfilAcessoVO</code>.
     * @return List  Contendo todos os objetos da classe <code>UsuarioPerfilAcessoVO</code> resultantes da consulta.
     * @exception Exception  Erro de conexão com o BD ou restrição de acesso a esta operação.
     */
    public List<UsuarioPerfilAcessoVO> consultarUsuarioPerfilAcesso(Integer usuario, int nivelMontarDados) throws Exception {
        consultar(getIdEntidade());
        List<UsuarioPerfilAcessoVO> objetos = new ArrayList<>();

        String sql = "SELECT * FROM UsuarioPerfilAcesso WHERE usuario = ?";

        try (PreparedStatement sqlConsulta = con.prepareStatement(sql)) {
            sqlConsulta.setInt(1, usuario);
            try (ResultSet resultado = sqlConsulta.executeQuery()) {
                while (resultado.next()) {
                    UsuarioPerfilAcessoVO novoObj = montarDados(resultado, nivelMontarDados, this.con);
                    objetos.add(novoObj);
                }
            }
        }
        return objetos;
    }
    
    /**
     * Operação responsável por consultar todos os <code>UsuarioPerfilAcessoVO</code> relacionados a um objeto da classe <code>cadastro.Cliente</code>.
     * @param usuario  Atributo a ser utilizado para localizar os objetos da classe <code>UsuarioPerfilAcessoVO</code>.
     * @return List  Contendo todos os objetos da classe <code>UsuarioPerfilAcessoVO</code> resultantes da consulta.
     * @exception Exception  Erro de conexão com o BD ou restrição de acesso a esta operação.
     */
    public List<UsuarioPerfilAcessoVO> consultarUsuarioPerfilAcessoEmpresaAtiva(Integer usuario, int nivelMontarDados) throws Exception {
        consultar(getIdEntidade());
        List<UsuarioPerfilAcessoVO> objetos = new ArrayList<UsuarioPerfilAcessoVO>();
        PerfilAcesso perfilAcesso = new PerfilAcesso(con);

        String sql = "SELECT usPer,* FROM UsuarioPerfilAcesso usPer inner join empresa emp on emp.codigo = usPer.empresa  WHERE emp.ativa and usuario = ? order by emp.nome ";

        try (PreparedStatement sqlConsulta = con.prepareStatement(sql)) {
            sqlConsulta.setInt(1, usuario);
            try (ResultSet resultado = sqlConsulta.executeQuery()) {
                while (resultado.next()) {
                    UsuarioPerfilAcessoVO novoObj = UsuarioPerfilAcesso.montarDados(resultado, nivelMontarDados, this.con);
                    objetos.add(novoObj);
                }
            }
        }

        perfilAcesso = null;
        return objetos;
    }
    

    /**
     * Operação responsável por localizar um objeto da classe <code>UsuarioPerfilAcessoVO</code>
     * através de sua chave primária. 
     * @exception Exception Caso haja problemas de conexão ou localização do objeto procurado.
     */
    public UsuarioPerfilAcessoVO consultarPorChavePrimaria(Integer codigoPrm) throws Exception {
        consultar(getIdEntidade(), false);
        String sql = "SELECT * FROM UsuarioPerfilAcesso WHERE codigo = ?";
        try (PreparedStatement sqlConsultar = con.prepareStatement(sql)) {
            sqlConsultar.setInt(1, codigoPrm.intValue());
            try (ResultSet tabelaResultado = sqlConsultar.executeQuery()) {
                if (!tabelaResultado.next()) {
                    throw new ConsistirException("Dados Não Encontrados ( UsuarioPerfilAcesso ).");
                }
                return (montarDados(tabelaResultado, Uteis.NIVELMONTARDADOS_DADOSBASICOS, this.con));
            }
        }
    }

    public List<SelectItem> obterListaEmpresasPermitidas(final UsuarioVO usuarioVO) throws Exception {
        List<UsuarioPerfilAcessoVO> listaPerfisDoUsuario = getFacade().getUsuarioPerfilAcesso().consultarUsuarioPerfilAcessoEmpresaAtiva(usuarioVO.getCodigo(),
                Uteis.NIVELMONTARDADOS_LOGIN);
        List<SelectItem> listaEmpresas = new ArrayList();
        if (!usuarioVO.getAdministrador()) {
            for (UsuarioPerfilAcessoVO usuarioPerfilAcessoVO : listaPerfisDoUsuario) {
                listaEmpresas.add(new SelectItem(usuarioPerfilAcessoVO.getEmpresa().getCodigo(), usuarioPerfilAcessoVO.getEmpresa().getNome().toString()));
            }
        }
        return listaEmpresas;
    }
}

package negocio.facade.jdbc.arquitetura;


import br.com.pactosolucoes.comuns.util.Formatador;
import negocio.comuns.arquitetura.UsuarioTelefoneVO;
import negocio.comuns.utilitarias.ConsistirException;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisValidacao;
import negocio.interfaces.arquitetura.UsuarioTelefoneInterfaceFacade;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;

public class UsuarioTelefone extends SuperEntidade implements UsuarioTelefoneInterfaceFacade {

    public UsuarioTelefone() throws Exception {
        super();
    }
    public UsuarioTelefone(Connection conexao) throws Exception {
        super(conexao);
    }

    public void validarDados(UsuarioTelefoneVO obj) throws Exception {

        if (obj == null) {
            throw new ConsistirException("Dados não informados(UsuarioTelefone).");
        }
        if (obj.getUsuario() == 0) {
            throw new ConsistirException("Não informado o usuário(UsuarioTelefone).");
        }
        if (UteisValidacao.emptyString(obj.getNumero())) {
            throw new ConsistirException("O telefone deve ser informado(UsuarioTelefone).");
        }

    }

    public void incluir(final UsuarioTelefoneVO obj) throws Exception{
        try {
            con.setAutoCommit(false);
            incluirSemCommit(obj);
            con.commit();
        } catch (Exception e) {
            con.rollback();
            throw e;
        } finally {
            con.setAutoCommit(true);
        }
    }

    public void incluirSemCommit(final UsuarioTelefoneVO obj) throws Exception {
        try {
            PreparedStatement sqlInserir = con.prepareStatement("INSERT INTO UsuarioTelefone(usuario, ddi, numero, verificado) VALUES (?,?,?,?);");
            int i = 0;
            sqlInserir.setInt(++i, obj.getUsuario());
            sqlInserir.setString(++i, obj.getDdi());
            sqlInserir.setString(++i, Formatador.formataTelefoneZW(obj.getNumero()));
            sqlInserir.setBoolean(++i, obj.isVerificado());
            sqlInserir.execute();
            obj.setCodigo(obterValorChavePrimariaCodigo());
            obj.setNovoObj(false);
        } catch (Exception ex) {
            Uteis.logar(ex, this.getClass());
            ex.printStackTrace();
            throw ex;
        }
    }

    public void alterar(final UsuarioTelefoneVO obj) throws Exception {
        try {
            con.setAutoCommit(false);
            final String sql = "UPDATE UsuarioTelefone SET ddi = ?, numero = ?, verificado = ? WHERE usuario = ? ";
            int i = 1;
            PreparedStatement sqlExcluir = con.prepareStatement(sql);
            sqlExcluir.setString(i++, obj.getDdi());
            sqlExcluir.setString(i++, Formatador.formataTelefoneZW(obj.getNumero()));
            sqlExcluir.setBoolean(i++, obj.isVerificado());
            sqlExcluir.setInt(i++, obj.getUsuario());
            sqlExcluir.execute();
            con.commit();
        } catch (Exception e) {
            con.rollback();
            throw e;
        } finally {
            con.setAutoCommit(true);
        }
    }

    public void excluirSemCommit(final UsuarioTelefoneVO obj) throws Exception {
        final String sql = "DELETE FROM UsuarioTelefone WHERE usuario = ? ";
        int i = 0;
        PreparedStatement sqlExcluir = con.prepareStatement(sql);
        sqlExcluir.setInt(++i, obj.getUsuario());
        sqlExcluir.execute();
    }

    public void excluir(final UsuarioTelefoneVO obj) throws Exception {
        try {
            con.setAutoCommit(false);
            excluirSemCommit(obj);
            con.commit();
        } catch (Exception e) {
            con.rollback();
            throw e;
        } finally {
            con.setAutoCommit(true);
        }
    }

    public UsuarioTelefoneVO consultarPorUsuario(Integer usuario) throws Exception {
        String sql = "SELECT * FROM UsuarioTelefone WHERE usuario = ?";
        PreparedStatement sqlConsultar = con.prepareStatement(sql);
        sqlConsultar.setInt(1, usuario);
        ResultSet rs = sqlConsultar.executeQuery();
        if (!rs.next()) {
            return new UsuarioTelefoneVO();
        }else {
            return montarDadosBasico(rs);
        }
    }

    private UsuarioTelefoneVO montarDadosBasico(ResultSet rs) throws Exception {
        UsuarioTelefoneVO obj = new UsuarioTelefoneVO();
        obj.setNovoObj(false);
        obj.setCodigo(rs.getInt("codigo"));
        obj.setDdi(rs.getString("ddi"));
        obj.setNumero(rs.getString("numero"));
        obj.setUsuario(rs.getInt("usuario"));
        obj.setVerificado(rs.getBoolean("verificado"));
        return obj;
    }

    public void gravar(final UsuarioTelefoneVO obj) throws Exception {
        if (UteisValidacao.emptyNumber(obj.getCodigo())) {
            incluir(obj);
        } else {
            alterar(obj);
        }
    }

    public boolean existeTelefone(UsuarioTelefoneVO usuarioTelefoneVO) throws Exception {
        StringBuilder sql = new StringBuilder();
        sql.append(" SELECT u.codigo \n");
        sql.append(" FROM usuariotelefone u ");
        sql.append(" WHERE u.numero = ? ");
        if (!UteisValidacao.emptyNumber(usuarioTelefoneVO.getCodigo())) {
            sql.append("AND u.codigo <> ? ");
        }
        PreparedStatement sqlConsultar = con.prepareStatement(sql.toString());
        sqlConsultar.setString(1, Formatador.formataTelefoneZW(usuarioTelefoneVO.getNumero()));
        if (!UteisValidacao.emptyNumber(usuarioTelefoneVO.getCodigo())) {
            sqlConsultar.setInt(2, usuarioTelefoneVO.getCodigo());
        }
        ResultSet rs = sqlConsultar.executeQuery();
        return rs.next();
    }

    public UsuarioTelefoneVO consultarPorNumero(String numero) throws Exception {
        String sql = "SELECT * FROM usuariotelefone WHERE numero = ?";
        PreparedStatement pst = con.prepareStatement(sql);
        pst.setString(1, Formatador.formataTelefoneZW(numero));
        ResultSet rs = pst.executeQuery();
        if (!rs.next()) {
            return new UsuarioTelefoneVO();
        }else {
            return montarDadosBasico(rs);
        }
    }
}

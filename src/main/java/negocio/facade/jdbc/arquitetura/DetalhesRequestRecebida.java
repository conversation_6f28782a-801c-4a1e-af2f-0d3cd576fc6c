package negocio.facade.jdbc.arquitetura;

import negocio.comuns.arquitetura.DetalhesRequestRecebidaVO;
import negocio.comuns.utilitarias.Uteis;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;

/**
 * Created with IntelliJ IDEA.
 * User: <PERSON>
 * Date: 09/10/2024
 */

public class DetalhesRequestRecebida extends SuperEntidade {

    public DetalhesRequestRecebida() throws Exception {
        super();
    }

    public DetalhesRequestRecebida(Connection conexao) throws Exception {
        super(conexao);
    }

    public DetalhesRequestRecebidaVO incluir(DetalhesRequestRecebidaVO obj) throws Exception {
        try {
            String sql = "INSERT INTO DetalhesRequestRecebida(dataRegistro, url, body, userAgent, ip)" +
                    " VALUES (?, ?, ?, ?, ?) RETURNING codigo";
            PreparedStatement sqlInserir = con.prepareStatement(sql);
            sqlInserir.setTimestamp(1, Uteis.getDataJDBCTimestamp(obj.getDataRegistro()));
            sqlInserir.setString(2, obj.getUrl());
            sqlInserir.setString(3, obj.getBody());
            sqlInserir.setString(4, obj.getUserAgent());
            sqlInserir.setString(5, obj.getIp());
            ResultSet rsCodigo = sqlInserir.executeQuery();
            rsCodigo.next();
            obj.setCodigo(rsCodigo.getInt(1));
            obj.setNovoObj(false);
            return obj;
        } catch (Exception ex) {
            Uteis.logarDebug("Não foi possível gravar o detalhe da requisição recebida: " + ex.getMessage());
            throw ex;
        }
    }
}

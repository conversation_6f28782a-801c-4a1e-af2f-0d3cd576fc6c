package negocio.facade.jdbc.arquitetura;

import negocio.facade.jdbc.utilitarias.Conexao;

import java.io.Serializable;
import java.sql.Connection;
import java.sql.Statement;
import java.util.logging.Level;
import java.util.logging.Logger;

/**
 * Created by ulisses on 07/02/2017.
 */
public class SuperFacadeJdbcOAMD implements Serializable {

    private Connection ConOAMD = null;

    public SuperFacadeJdbcOAMD(Connection connection)throws Exception{
        this.ConOAMD = connection;
    }

    public void prepararConexao() throws Exception {
        try {
            if (getConOAMD() == null) {
                inicializar();
            } else {
                try (Statement stm = getConOAMD().createStatement()) {
                    stm.execute("select 1");
                }
            }
        }catch (Exception ex) {
            Logger.getLogger(SuperFacadeJdbcOAMD.class.getName()).log(Level.SEVERE, "#### Erro ao conectar no BD OAMD. Será feita uma nova tentativa de conexão.", ex);
            setConOAMD(null);
            inicializar();
            Logger.getLogger(SuperFacadeJdbcOAMD.class.getName()).log(Level.INFO, "#### NOVA CONEXÃO COM o BD OAMD OBTIDA COM SUCESSO.");
            throw ex;
        }
    }


    public void inicializar() throws Exception {
        if ((getConOAMD()== null) || (getConOAMD().isClosed())) {
            setConOAMD(Conexao.obterConexaoBancoEmpresas());
        }
    }


    public Connection getConOAMD() {
        return ConOAMD;
    }

    public void setConOAMD(Connection conOAMD) {
        ConOAMD = conOAMD;
    }

}

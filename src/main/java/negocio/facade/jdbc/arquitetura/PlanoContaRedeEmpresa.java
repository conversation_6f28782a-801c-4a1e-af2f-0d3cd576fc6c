package negocio.facade.jdbc.arquitetura;

import negocio.comuns.arquitetura.PlanoContaRedeEmpresaVO;
import negocio.comuns.utilitarias.Uteis;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;

import static org.apache.commons.lang3.StringUtils.isNotBlank;

public class PlanoContaRedeEmpresa extends SuperEntidade {

    public PlanoContaRedeEmpresa(Connection conexao) throws Exception {
        super(conexao);
    }

    public PlanoContaRedeEmpresa() throws Exception {
        super();
    }

    public void inserir(PlanoContaRedeEmpresaVO planoContaRedeEmpresaVO) throws SQLException {
        String sql = "INSERT INTO PlanoContaRedeEmpresa " +
                "(planoConta,chaveorigem,chavedestino,datacadastro, nomeUnidade, mensagemSituacao, empresadestino)" +
                "VALUES" +
                "(?,?,?,?,?,?,?)";

        try (PreparedStatement preparedStatement = con.prepareStatement(sql)) {
            int i = 1;
            preparedStatement.setInt(i++, planoContaRedeEmpresaVO.getPlanoConta());
            preparedStatement.setString(i++, planoContaRedeEmpresaVO.getChaveOrigem());
            if (isNotBlank(planoContaRedeEmpresaVO.getChaveDestino())) {
                preparedStatement.setString(i++, planoContaRedeEmpresaVO.getChaveDestino());
            } else {
                preparedStatement.setString(i++, "");
            }
            preparedStatement.setTimestamp(i++, Uteis.getTimestamp());
            preparedStatement.setString(i++, planoContaRedeEmpresaVO.getNomeUnidade());
            preparedStatement.setString(i++, planoContaRedeEmpresaVO.getMensagemSituacao());
            preparedStatement.setInt(i++, planoContaRedeEmpresaVO.getEmpresaDestino());
            preparedStatement.execute();
        }
    }

    public void alterarMensagemSituacao(Integer planoConta, String chaveOrigem, String chaveDestino, String mensagemSituacao, Integer empresaDestino) throws SQLException {
        String sql = "UPDATE PlanoContaRedeEmpresa set " +
                "mensagemSituacao = ? " +
                "WHERE planoConta = ? AND chaveorigem = ? AND chavedestino = ? AND empresadestino = ?";

        try (PreparedStatement preparedStatement = con.prepareStatement(sql)) {
            int i = 1;
            preparedStatement.setString(i++, mensagemSituacao);
            preparedStatement.setInt(i++, planoConta);
            preparedStatement.setString(i++, chaveOrigem);
            preparedStatement.setString(i++, chaveDestino);
            preparedStatement.setInt(i++, empresaDestino);
            preparedStatement.execute();
        }
    }

    public void limparDataAtualizacao(Integer planoConta, String chaveOrigem, String chaveDestino, Integer empresaDestino) throws SQLException {
        String sql = "UPDATE PlanoContaRedeEmpresa set " +
                "dataatualizacao = ? " +
                "WHERE planoConta = ? AND chaveorigem = ? AND chavedestino = ? AND empresadestino = ?";

        try (PreparedStatement preparedStatement = con.prepareStatement(sql)) {
            int i = 1;
            preparedStatement.setTimestamp(i++, null);
            preparedStatement.setInt(i++, planoConta);
            preparedStatement.setString(i++, chaveOrigem);
            preparedStatement.setString(i++, chaveDestino);
            preparedStatement.setInt(i++, empresaDestino);
            preparedStatement.execute();
        }
    }

    public void alterarDataAtualizacao(Integer planoConta, String chaveOrigem, String chaveDestino, String mensagemSituacao, Integer usuarioReplicado, Integer empresaDestino) throws SQLException {
        String sql = "UPDATE PlanoContaRedeEmpresa set " +
                "dataatualizacao = ?, mensagemSituacao = ?, planocontareplicado = ? " +
                "WHERE planoConta = ? AND chaveorigem = ? AND empresadestino = ? AND chaveDestino = ?";

        try (PreparedStatement preparedStatement = con.prepareStatement(sql)) {
            int i = 1;
            preparedStatement.setTimestamp(i++, Uteis.getTimestamp());
            preparedStatement.setString(i++, mensagemSituacao);
            preparedStatement.setInt(i++, usuarioReplicado);
            preparedStatement.setInt(i++, planoConta);
            preparedStatement.setString(i++, chaveOrigem);
            preparedStatement.setInt(i++, empresaDestino);
            preparedStatement.setString(i++, chaveDestino);
            preparedStatement.execute();
        }
    }

    public PlanoContaRedeEmpresaVO consultarPorChaveEmpresaPlanoConta(String chaveDestino, Integer empresaDestino, Integer planoConta) throws SQLException {
        String sql = "SELECT * FROM PlanoContaRedeEmpresa " +
                "WHERE chaveDestino = ? AND empresaDestino = ? AND planoConta = ?";

        try (PreparedStatement preparedStatement = con.prepareStatement(sql)) {
            int i = 1;
            preparedStatement.setString(i++, chaveDestino);
            preparedStatement.setInt(i++, empresaDestino);
            preparedStatement.setInt(i++, planoConta);
            try (ResultSet resultSet = preparedStatement.executeQuery()) {
                if (resultSet.next()) {
                    return montarDados(resultSet);
                }
            }
        }
        return null;
    }

    public PlanoContaRedeEmpresaVO montarDados(ResultSet resultSet) throws SQLException {
        PlanoContaRedeEmpresaVO planoContaRedeEmpresaVO = new PlanoContaRedeEmpresaVO();
        planoContaRedeEmpresaVO.setChaveOrigem(resultSet.getString("chaveorigem"));
        planoContaRedeEmpresaVO.setChaveDestino(resultSet.getString("chavedestino"));
        planoContaRedeEmpresaVO.setCodigo(resultSet.getInt("codigo"));
        planoContaRedeEmpresaVO.setDatacadastro(resultSet.getDate("datacadastro"));
        planoContaRedeEmpresaVO.setPlanoConta(resultSet.getInt("planoConta"));
        planoContaRedeEmpresaVO.setDataatualizacao(resultSet.getDate("dataatualizacao"));
        planoContaRedeEmpresaVO.setNomeUnidade(resultSet.getString("nomeUnidade"));
        planoContaRedeEmpresaVO.setMensagemSituacao(resultSet.getString("mensagemsituacao"));
        planoContaRedeEmpresaVO.setPlanoContaReplicado(resultSet.getInt("planoContaReplicado"));
        planoContaRedeEmpresaVO.setEmpresaDestino(resultSet.getInt("empresadestino"));

        return planoContaRedeEmpresaVO;
    }
}

package negocio.facade.jdbc.arquitetura;

import javax.faces.context.FacesContext;
import java.io.Serializable;
import java.util.Map;
import java.util.Set;

public abstract class FacadeManager implements Serializable {

    private static ThreadLocal<FacadeFactory> facadeFactory = new ThreadLocal<FacadeFactory>();

    public static FacadeFactory getFacade() {
        FacesContext fc = FacesContext.getCurrentInstance();
        if (fc != null) {
            FacadeFactory facade = (FacadeFactory) fc.getExternalContext().getSessionMap().get(FacadeFactory.class.getSimpleName());
            if (facade == null) {
                facade = new FacadeFactory();
                FacesContext.getCurrentInstance().getExternalContext().getSessionMap().put(FacadeFactory.class.getSimpleName(), facade);
            }
            return facade;
        } else {
            if (facadeFactory.get() == null) {
                facadeFactory.set(new FacadeFactory());
            }

            return facadeFactory.get();
        }
    }

    public static void limparFactory() {
        facadeFactory.set(null);
        FacesContext fc = FacesContext.getCurrentInstance();
        if (fc != null) {
            Map<String, Object> objsSession = fc.getExternalContext().getSessionMap();
            Set<String> nomes = objsSession.keySet();
            for (String s : nomes) {
                Object obj = objsSession.get(s);
                if (obj instanceof FacadeManager) {
                    objsSession.remove(s);
                    obj = null;
                    System.out.println("FacadeManager removeu -> " + s);
                }else if (obj.getClass() == FacadeFactory.class){
                    objsSession.remove(s);
                    obj = null;
                    System.out.println("FacadeManager removeu -> " + s);
                }

            }
        }
    }
}

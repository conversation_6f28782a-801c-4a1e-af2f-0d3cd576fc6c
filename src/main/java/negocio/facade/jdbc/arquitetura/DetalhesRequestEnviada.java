package negocio.facade.jdbc.arquitetura;

import negocio.comuns.arquitetura.DetalhesRequestEnviadaVO;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisValidacao;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.Types;

/**
 * Created with IntelliJ IDEA.
 * User: <PERSON>
 * Date: 09/10/2024
 */

public class DetalhesRequestEnviada extends SuperEntidade {

    public DetalhesRequestEnviada() throws Exception {
        super();
    }

    public DetalhesRequestEnviada(Connection conexao) throws Exception {
        super(conexao);
    }

    public DetalhesRequestEnviadaVO incluir(DetalhesRequestEnviadaVO obj) throws Exception {
        try {
            String sql = "INSERT INTO DetalhesRequestEnviada(dataRegistro, url, statusResponse, tempoRequisicaoMs, sucesso, nomeTabelaForeignKey," +
                    " codigoTabelaForeignKey, response, origem, body) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING codigo";
            PreparedStatement sqlInserir = con.prepareStatement(sql);
            sqlInserir.setTimestamp(1, Uteis.getDataJDBCTimestamp(obj.getDataRegistro()));
            sqlInserir.setString(2, obj.getUrl());
            sqlInserir.setInt(3, obj.getStatusResponse());
            sqlInserir.setLong(4, obj.getTempoRequisicaoMs());
            sqlInserir.setBoolean(5, obj.isSucesso());
            if (UteisValidacao.emptyString(obj.getNomeTabelaForeignKey())) {
                sqlInserir.setNull(6, Types.NULL);
            } else {
                sqlInserir.setString(6, obj.getNomeTabelaForeignKey());
            }
            if (UteisValidacao.emptyNumber(obj.getCodigoTabelaForeignKey())) {
                sqlInserir.setNull(7, Types.NULL);
            } else {
                sqlInserir.setInt(7, obj.getCodigoTabelaForeignKey());
            }
            if (UteisValidacao.emptyString(obj.getResponse())) {
                sqlInserir.setNull(8, Types.NULL);
            } else {
                sqlInserir.setString(8, obj.getResponse());
            }
            if (UteisValidacao.emptyString(obj.getOrigem())) {
                sqlInserir.setNull(9, Types.NULL);
            } else {
                sqlInserir.setString(9, obj.getOrigem());
            }   if (UteisValidacao.emptyString(obj.getBody())) {
                sqlInserir.setNull(10, Types.NULL);
            } else {
                sqlInserir.setString(10, obj.getBody());
            }
            ResultSet rsCodigo = sqlInserir.executeQuery();
            rsCodigo.next();
            obj.setCodigo(rsCodigo.getInt(1));
            obj.setNovoObj(false);
            return obj;
        } catch (Exception ex) {
            Uteis.logarDebug("Não foi possível gravar o detalhe da requisição enviada: " + ex.getMessage());
            throw ex;
        }
    }
    
    public DetalhesRequestEnviadaVO consultarPorChavePrimaria(Integer codigo) throws Exception {
        try {
            String sql = "SELECT codigo, dataRegistro, url, statusResponse, tempoRequisicaoMs, sucesso, " +
                         "nomeTabelaForeignKey, codigoTabelaForeignKey, response, origem, body " +
                         "FROM DetalhesRequestEnviada WHERE codigo = ?";
            
            PreparedStatement stmt = con.prepareStatement(sql);
            stmt.setInt(1, codigo);
            
            ResultSet rs = stmt.executeQuery();
            
            if (rs.next()) {
                DetalhesRequestEnviadaVO vo = new DetalhesRequestEnviadaVO();
                vo.setCodigo(rs.getInt("codigo"));
                vo.setDataRegistro(rs.getTimestamp("dataRegistro"));
                vo.setUrl(rs.getString("url"));
                vo.setStatusResponse(rs.getInt("statusResponse"));
                vo.setTempoRequisicaoMs(rs.getLong("tempoRequisicaoMs"));
                vo.setSucesso(rs.getBoolean("sucesso"));
                vo.setNomeTabelaForeignKey(rs.getString("nomeTabelaForeignKey"));
                vo.setCodigoTabelaForeignKey(rs.getInt("codigoTabelaForeignKey"));
                vo.setResponse(rs.getString("response"));
                vo.setOrigem(rs.getString("origem"));
                vo.setBody(rs.getString("body"));
                
                return vo;
            }
            
            return null;
        } catch (Exception ex) {
            Uteis.logarDebug("Não foi possível consultar por chave primária: " + ex.getMessage());
            throw ex;
        }
    }

    public void alterar(DetalhesRequestEnviadaVO obj) throws Exception {
        try {
            String sql = "UPDATE DetalhesRequestEnviada SET dataRegistro = ?, url = ?, statusResponse = ?, " +
                         "tempoRequisicaoMs = ?, sucesso = ?, nomeTabelaForeignKey = ?, codigoTabelaForeignKey = ?, " +
                         "response = ?, origem = ?, body = ? WHERE codigo = ?";
            
            PreparedStatement stmt = con.prepareStatement(sql);
            stmt.setTimestamp(1, Uteis.getDataJDBCTimestamp(obj.getDataRegistro()));
            stmt.setString(2, obj.getUrl());
            stmt.setInt(3, obj.getStatusResponse());
            stmt.setLong(4, obj.getTempoRequisicaoMs());
            stmt.setBoolean(5, obj.isSucesso());
            
            if (UteisValidacao.emptyString(obj.getNomeTabelaForeignKey())) {
                stmt.setNull(6, Types.VARCHAR);
            } else {
                stmt.setString(6, obj.getNomeTabelaForeignKey());
            }
            
            if (UteisValidacao.emptyNumber(obj.getCodigoTabelaForeignKey())) {
                stmt.setNull(7, Types.INTEGER);
            } else {
                stmt.setInt(7, obj.getCodigoTabelaForeignKey());
            }
            
            stmt.setString(8, obj.getResponse());
            stmt.setString(9, obj.getOrigem());
            stmt.setString(10, obj.getBody());
            stmt.setInt(11, obj.getCodigo());
            
            stmt.executeUpdate();
        } catch (Exception ex) {
            Uteis.logarDebug("Não foi possível alterar o detalhe da requisição enviada: " + ex.getMessage());
            throw ex;
        }
    }
}

package negocio.facade.jdbc.arquitetura;


import acesso.webservice.DaoAuxiliar;
import br.com.pactosolucoes.comuns.util.FileUtilities;
import negocio.comuns.arquitetura.UsuarioEmailVO;
import negocio.comuns.utilitarias.ConsistirException;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisValidacao;
import negocio.interfaces.arquitetura.UsuarioEmailInterfaceFacade;

import java.io.File;
import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;

/**
 * Created by Rafael on 27/12/2016.
 */
public class UsuarioEmail extends SuperEntidade implements UsuarioEmailInterfaceFacade{

    public UsuarioEmail() throws Exception {
        super();
    }
    public UsuarioEmail(Connection conexao) throws Exception {
        super(conexao);
    }

    public void validarDados(UsuarioEmailVO obj) throws Exception {

        if (obj == null) {
            throw new ConsistirException("Dados não informados(UsuarioEmail).");
        }
        if (obj.getUsuario() == 0) {
            throw new ConsistirException("Não informado o usuário(UsuarioEmail).");
        }
        if (UteisValidacao.emptyString(obj.getEmail())) {
            throw new ConsistirException("O email deve ser informado(UsuarioEmail).");
        }

    }

    public void incluir(final UsuarioEmailVO obj) throws Exception{
        try {
            con.setAutoCommit(false);
            incluirSemCommit(obj);
            con.commit();
        } catch (Exception e) {
            con.rollback();
            throw e;
        } finally {
            con.setAutoCommit(true);
        }
    }

    public void incluirSemCommit(final UsuarioEmailVO obj) throws Exception {
        try {
            PreparedStatement sqlInserir = con.prepareStatement("INSERT INTO usuarioEmail(usuario, email, verificado) VALUES (?, ?, ?);");
            int i = 0;
            sqlInserir.setInt(++i, obj.getUsuario());
            sqlInserir.setString(++i, obj.getEmail().trim().toUpperCase());
            sqlInserir.setBoolean(++i, obj.isVerificado());
            sqlInserir.execute();
            obj.setCodigo(obterValorChavePrimariaCodigo());
            obj.setNovoObj(false);
        } catch (Exception ex) {
            Uteis.logar(ex, this.getClass());
            ex.printStackTrace();
            throw ex;
        }
    }

    public void alterar(final UsuarioEmailVO obj) throws Exception {
        try {
            con.setAutoCommit(false);
            final String sql = "UPDATE UsuarioEmail  SET email = ?, verificado = ? WHERE usuario = ? ";
            int i = 1;
            PreparedStatement sqlExcluir = con.prepareStatement(sql);
            sqlExcluir.setString(i++, obj.getEmail().trim().toUpperCase());
            sqlExcluir.setBoolean(i++, obj.isVerificado());
            sqlExcluir.setInt(i++, obj.getUsuario());
            sqlExcluir.execute();
            con.commit();
        } catch (Exception e) {
            con.rollback();
            throw e;
        } finally {
            con.setAutoCommit(true);
        }
    }

    public void excluirSemCommit(final UsuarioEmailVO obj) throws Exception {
        final String sql = "DELETE FROM UsuarioEmail WHERE usuario = ? ";
        int i = 0;
        PreparedStatement sqlExcluir = con.prepareStatement(sql);
        sqlExcluir.setInt(++i, obj.getUsuario());
        sqlExcluir.execute();
    }

    public void excluir(final UsuarioEmailVO obj) throws Exception {
        try {
            con.setAutoCommit(false);
            excluirSemCommit(obj);
            con.commit();
        } catch (Exception e) {
            con.rollback();
            throw e;
        } finally {
            con.setAutoCommit(true);
        }
    }

    public static UsuarioEmailVO montarDadosBasico(ResultSet rs) throws Exception {
        UsuarioEmailVO obj = new UsuarioEmailVO();
        obj.setNovoObj(false);
        obj.setCodigo(rs.getInt("codigo"));
        obj.setEmail(rs.getString("email"));
        obj.setUsuario(rs.getInt("usuario"));
        try {
            obj.setVerificado(rs.getBoolean("verificado"));
        } catch (Exception ignored) {
        }
        return obj;
    }

    public UsuarioEmailVO consultarPorEmail(String email) throws Exception {
        String sql = "SELECT * FROM UsuarioEmail WHERE upper(email) = ?";
        PreparedStatement sqlConsultar = con.prepareStatement(sql);
        sqlConsultar.setString(1, email.toUpperCase());
        ResultSet rs = sqlConsultar.executeQuery();
        if (!rs.next()) {
            return new UsuarioEmailVO();
        }else {
            return montarDadosBasico(rs);
        }
    }
    public UsuarioEmailVO consultarPorUsuario(Integer usuario) throws Exception {
        String sql = "SELECT * FROM UsuarioEmail WHERE usuario = ?";
        PreparedStatement sqlConsultar = con.prepareStatement(sql);
        sqlConsultar.setInt(1, usuario);
        ResultSet rs = sqlConsultar.executeQuery();
        if (!rs.next()) {
            return new UsuarioEmailVO();
        }else {
            return montarDadosBasico(rs);
        }
    }

    public boolean existeEmail(UsuarioEmailVO usuarioEmailVO) throws Exception {
        StringBuilder sql = new StringBuilder();
        sql.append(" SELECT UE.codigo ");
        sql.append(" FROM UsuarioEmail UE ");
        sql.append(" WHERE upper(UE.email) = ? ");
        if (!UteisValidacao.emptyNumber(usuarioEmailVO.getCodigo())) {
            sql.append("AND UE.codigo <> ? ");
        }
        PreparedStatement sqlConsultar = con.prepareStatement(sql.toString());
        sqlConsultar.setString(1, usuarioEmailVO.getEmail().trim().toUpperCase());
        if (!UteisValidacao.emptyNumber(usuarioEmailVO.getCodigo())) {
            sqlConsultar.setInt(2, usuarioEmailVO.getCodigo());
        }
        ResultSet rs = sqlConsultar.executeQuery();
        return rs.next();
    }

    public void enviarEmailUsuarioApp(String key, String nome, String usuario, String senha) throws Exception{
        DaoAuxiliar.retornarAcessoControle(key).enviarEmail(new String[]{usuario}, "Informações de acesso ao App do Gestor",
                gerarCorpoEmailSenhaUsuarioApp(key, nome, usuario, senha));
    }

    public StringBuilder gerarCorpoEmailSenhaUsuarioApp(final String key,final String nome,
            final String usuario, final String senha) throws Exception {
        File arq = new File(getClass().getResource("/br/com/pactosolucoes/comuns/util/resources/emailPactoAppGestor.txt").toURI());
        StringBuilder texto = FileUtilities.readContentFile(arq.getAbsolutePath(), "ISO-8859-1");
        final String aux = texto.toString().replaceAll("#CHAVE_ACADEMIA ", key)
                .replaceAll("#NEW_USER", nome)
                .replaceAll("#USER_NAME", usuario.toLowerCase())
                .replaceAll("#SENHA", UteisValidacao.emptyString(senha) ? "A senha não foi alterada" : senha);
        return new StringBuilder(aux);

    }

    public void gravar(final UsuarioEmailVO obj) throws Exception {
        if (UteisValidacao.emptyNumber(obj.getCodigo())) {
            incluir(obj);
        } else {
            alterar(obj);
        }
    }
}

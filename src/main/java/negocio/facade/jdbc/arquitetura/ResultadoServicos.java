package negocio.facade.jdbc.arquitetura;

import negocio.comuns.arquitetura.ResultadoServicosItensVO;
import negocio.comuns.arquitetura.ResultadoServicosVO;
import negocio.comuns.utilitarias.Calendario;
import negocio.comuns.utilitarias.Uteis;
import negocio.interfaces.arquitetura.ResultadoServicosInterfaceFacade;

import java.sql.Connection;
import java.sql.PreparedStatement;

public class ResultadoServicos extends SuperEntidade implements ResultadoServicosInterfaceFacade {

    public ResultadoServicos() throws Exception {
        super();
    }

    public ResultadoServicos(Connection conexao) throws Exception {
        super(conexao);
    }

    private void incluir(ResultadoServicosVO obj) throws Exception {
        String sql = "INSERT INTO ResultadoServicos(servico, empresa, resultado, finalizado, dataHoraInicio, dataHoraFim, descricao) VALUES (?, ?, ?, ?, ?, ?, ?)";
        PreparedStatement sqlInserir = con.prepareStatement(sql);
        sqlInserir.setInt(1, obj.getServico().getCodigo());
        resolveIntegerNull(sqlInserir, 2, obj.getEmpresa());
        sqlInserir.setString(3, obj.getResultado().toString());
        sqlInserir.setBoolean(4, obj.isFinalizado());
        sqlInserir.setTimestamp(5, Uteis.getDataJDBCTimestamp(obj.getDataHoraInicio()));
        sqlInserir.setTimestamp(6, Uteis.getDataJDBCTimestamp(obj.getDataHoraFim()));
        sqlInserir.setString(7, obj.getDescricao());
        sqlInserir.execute();
        obj.setCodigo(obterValorChavePrimariaCodigo());
        obj.setNovoObj(false);
        incluirItens(obj);
    }

    public void gravarResultado(ResultadoServicosVO resultadoServicosVO) {
        try {
            System.out.println("[DEBUG] " + Calendario.hoje() + " - " + "INCLUIR RESULTADO SERVICO: " + resultadoServicosVO.getServico().getDescricao().toUpperCase());
            resultadoServicosVO.setDataHoraFim(Calendario.hoje());
            resultadoServicosVO.setFinalizado(true);
            incluir(resultadoServicosVO);
        } catch (Exception ex) {
            System.out.println("[DEBUG] " + Calendario.hoje() + " - " + "ERRO INCLUIR RESULTADO SERVICO: " + resultadoServicosVO.getServico().getDescricao().toUpperCase() + " || EMPRESA: " + resultadoServicosVO.getEmpresa() + " || ERRO: " + ex.getMessage());
        }
    }

    private void incluirItens(ResultadoServicosVO obj) {
        try {
            for (int e = 0; e < obj.getResultado().length(); e++) {
                String descricao = obj.getResultado().getString(e);
                ResultadoServicosItensVO novo = new ResultadoServicosItensVO();
                novo.setResultadoServicos(obj.getCodigo());
                novo.setDescricao(descricao);
                incluirResultadoServicosItens(novo);
            }
        } catch (Exception ex) {
            System.out.println("[DEBUG] " + Calendario.hoje() + " - " + "ERRO INCLUIR ITENS DO RESULTADO SERVICO: " + obj.getServico().getDescricao().toUpperCase() + " || EMPRESA: " + obj.getEmpresa() + " || ERRO: " + ex.getMessage());
        }
    }

    private void incluirResultadoServicosItens(ResultadoServicosItensVO obj) throws Exception {
        String sql = "INSERT INTO ResultadoServicosItens(ResultadoServicos, descricao) VALUES (?, ?)";
        PreparedStatement sqlInserir = con.prepareStatement(sql);
        sqlInserir.setInt(1, obj.getResultadoServicos());
        sqlInserir.setString(2, obj.getDescricao());
        sqlInserir.execute();
    }
}

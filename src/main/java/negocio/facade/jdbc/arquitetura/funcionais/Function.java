package negocio.facade.jdbc.arquitetura.funcionais;

/**
 * Simula a interface funcional do Java 8.
 *
 * <AUTHOR>
 * @since 21/01/2019
 * @see <a href="https://docs.oracle.com/javase/8/docs/api/java/util/function/Function.html">Veja a documentação oficial</a>
 */
public interface Function<T, R> {

    /**
     * @param t argumento
     *
     * @return retorna um &lt;R&gt; a partir do argumento &lt;T&gt;
     */
    R apply(T t);
}
package negocio.facade.jdbc.arquitetura;

import negocio.comuns.arquitetura.LogApiVO;
import negocio.comuns.utilitarias.Uteis;
import negocio.interfaces.arquitetura.LogApiInterfaceFacade;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.util.Date;

public class LogApi extends SuperEntidade implements LogApiInterfaceFacade {

    public LogApi() throws Exception {
    }

    public LogApi(Connection conexao) throws Exception {
        super(conexao);
    }

    private Integer codigo;
    private String descricaoToken;
    private Date dataUso;
    private String ip;
    private String method;
    private String uri;
    private String params;

    @Override
    public void incluir(LogApiVO obj) throws Exception {
        String sql = "INSERT INTO logapi(descricaoToken, datauso, ip, method, uri, params) VALUES ( ?, ?, ?, ?, ?, ?)";
        try (PreparedStatement sqlInserir = con.prepareStatement(sql)) {
            int i = 0;
            sqlInserir.setString(++i, obj.getDescricaoToken());
            sqlInserir.setTimestamp(++i, Uteis.getDataJDBCTimestamp(obj.getDataUso()));
            sqlInserir.setString(++i, obj.getIp());
            sqlInserir.setString(++i, obj.getMethod());
            sqlInserir.setString(++i, obj.getUri());
            sqlInserir.setString(++i, obj.getParams());
            sqlInserir.execute();

            obj.setCodigo(obterValorChavePrimariaCodigo());
            obj.setNovoObj(false);
        }
    }
}

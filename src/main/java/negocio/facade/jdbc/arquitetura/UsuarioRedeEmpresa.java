package negocio.facade.jdbc.arquitetura;

import negocio.comuns.arquitetura.UsuarioRedeEmpresaVO;
import negocio.comuns.utilitarias.Uteis;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;

import static org.apache.commons.lang3.StringUtils.isNotBlank;

public class UsuarioRedeEmpresa extends SuperEntidade {

    public UsuarioRedeEmpresa(Connection conexao) throws Exception {
        super(conexao);
    }

    public UsuarioRedeEmpresa() throws Exception {
        super();
    }

    public void inserir(UsuarioRedeEmpresaVO usuarioRedeEmpresaVO) throws SQLException {
        String sql = "INSERT INTO UsuarioRedeEmpresa " +
                "(usuario,chaveorigem,chavedestino,datacadastro, nomeUnidade, mensagemSituacao, empresadestino)" +
                "VALUES" +
                "(?,?,?,?,?,?,?)";

        try (PreparedStatement preparedStatement = con.prepareStatement(sql)) {
            int i = 1;
            preparedStatement.setInt(i++, usuarioRedeEmpresaVO.getUsuario());
            preparedStatement.setString(i++, usuarioRedeEmpresaVO.getChaveOrigem());
            if (isNotBlank(usuarioRedeEmpresaVO.getChaveDestino())) {
                preparedStatement.setString(i++, usuarioRedeEmpresaVO.getChaveDestino());
            } else {
                preparedStatement.setString(i++, "");
            }
            preparedStatement.setTimestamp(i++, Uteis.getTimestamp());
            preparedStatement.setString(i++, usuarioRedeEmpresaVO.getNomeUnidade());
            preparedStatement.setString(i++, usuarioRedeEmpresaVO.getMensagemSituacao());
            preparedStatement.setInt(i++, usuarioRedeEmpresaVO.getEmpresaDestino());
            preparedStatement.execute();
        }
    }

    public void alterarMensagemSituacao(Integer usuario, String chaveOrigem, String chaveDestino, String mensagemSituacao, Integer empresaDestino) throws SQLException {
        String sql = "UPDATE UsuarioRedeEmpresa set " +
                "mensagemSituacao = ? " +
                "WHERE usuario = ? AND chaveorigem = ? AND chavedestino = ? AND empresadestino = ?";

        try (PreparedStatement preparedStatement = con.prepareStatement(sql)) {
            int i = 1;
            preparedStatement.setString(i++, mensagemSituacao);
            preparedStatement.setInt(i++, usuario);
            preparedStatement.setString(i++, chaveOrigem);
            preparedStatement.setString(i++, chaveDestino);
            preparedStatement.setInt(i++, empresaDestino);
            preparedStatement.execute();
        }
    }

    public void limparDataAtualizacao(Integer usuario, String chaveOrigem, String chaveDestino, Integer empresaDestino) throws SQLException {
        String sql = "UPDATE UsuarioRedeEmpresa set " +
                "dataatualizacao = ? " +
                "WHERE usuario = ? AND chaveorigem = ? AND chavedestino = ? AND empresadestino = ?";

        try (PreparedStatement preparedStatement = con.prepareStatement(sql)) {
            int i = 1;
            preparedStatement.setTimestamp(i++, null);
            preparedStatement.setInt(i++, usuario);
            preparedStatement.setString(i++, chaveOrigem);
            preparedStatement.setString(i++, chaveDestino);
            preparedStatement.setInt(i++, empresaDestino);
            preparedStatement.execute();
        }
    }

    public void alterarDataAtualizacao(Integer usuario, String chaveOrigem, String chaveDestino, String mensagemSituacao, Integer usuarioReplicado, Integer empresaDestino) throws SQLException {
        String sql = "UPDATE UsuarioRedeEmpresa set " +
                "dataatualizacao = ?, mensagemSituacao = ?, usuarioreplicado = ? " +
                "WHERE usuario = ? AND chaveorigem = ? AND empresadestino = ? AND chaveDestino = ?";

        try (PreparedStatement preparedStatement = con.prepareStatement(sql)) {
            int i = 1;
            preparedStatement.setTimestamp(i++, Uteis.getTimestamp());
            preparedStatement.setString(i++, mensagemSituacao);
            preparedStatement.setInt(i++, usuarioReplicado);
            preparedStatement.setInt(i++, usuario);
            preparedStatement.setString(i++, chaveOrigem);
            preparedStatement.setInt(i++, empresaDestino);
            preparedStatement.setString(i++, chaveDestino);
            preparedStatement.execute();
        }
    }

    public UsuarioRedeEmpresaVO consultarPorChaveEmpresaUsuario(String chaveDestino, Integer empresaDestino, Integer usuario) throws SQLException {
        String sql = "SELECT * FROM UsuarioRedeEmpresa " +
                "WHERE chaveDestino = ? AND empresaDestino = ? AND usuario = ?";

        try (PreparedStatement preparedStatement = con.prepareStatement(sql)) {
            int i = 1;
            preparedStatement.setString(i++, chaveDestino);
            preparedStatement.setInt(i++, empresaDestino);
            preparedStatement.setInt(i++, usuario);
            try (ResultSet resultSet = preparedStatement.executeQuery()) {
                if (resultSet.next()) {
                    return montarDados(resultSet);
                }
            }
        }
        return null;
    }

    public UsuarioRedeEmpresaVO montarDados(ResultSet resultSet) throws SQLException {
        UsuarioRedeEmpresaVO usuarioRedeEmpresaVO = new UsuarioRedeEmpresaVO();
        usuarioRedeEmpresaVO.setChaveOrigem(resultSet.getString("chaveorigem"));
        usuarioRedeEmpresaVO.setChaveDestino(resultSet.getString("chavedestino"));
        usuarioRedeEmpresaVO.setCodigo(resultSet.getInt("codigo"));
        usuarioRedeEmpresaVO.setDatacadastro(resultSet.getDate("datacadastro"));
        usuarioRedeEmpresaVO.setUsuario(resultSet.getInt("usuario"));
        usuarioRedeEmpresaVO.setDataatualizacao(resultSet.getDate("dataatualizacao"));
        usuarioRedeEmpresaVO.setNomeUnidade(resultSet.getString("nomeUnidade"));
        usuarioRedeEmpresaVO.setMensagemSituacao(resultSet.getString("mensagemsituacao"));
        usuarioRedeEmpresaVO.setUsuarioReplicado(resultSet.getInt("usuarioreplicado"));
        usuarioRedeEmpresaVO.setEmpresaDestino(resultSet.getInt("empresadestino"));

        return usuarioRedeEmpresaVO;
    }
}

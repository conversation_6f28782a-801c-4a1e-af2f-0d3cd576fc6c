package negocio.facade.jdbc.nfe;

import negocio.comuns.nfe.ItemRPSNFeVO;
import negocio.comuns.nfe.NotaFiscalDeServicoVO;
import negocio.facade.jdbc.arquitetura.SuperEntidade;
import negocio.interfaces.nfe.ItemRPSNFeInterfaceFacade;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.util.ArrayList;
import java.util.List;

public class ItemRPSNFe extends SuperEntidade implements ItemRPSNFeInterfaceFacade {

    public ItemRPSNFe() throws Exception {
        super();
    }

    public ItemRPSNFe(Connection con) throws Exception {
        super(con);
    }

    @Override
    public ItemRPSNFeVO novo() throws Exception {
        incluir(getIdEntidade());
        return new ItemRPSNFeVO();
    }

    @Override
    public void incluir(ItemRPSNFeVO obj) throws Exception {
        //To change body of implemented methods use File | Settings | File Templates.
    }

    @Override
    public void alterar(ItemRPSNFeVO obj) throws Exception {
        //To change body of implemented methods use File | Settings | File Templates.
    }

    @Override
    public void excluir(ItemRPSNFeVO obj) throws Exception {
        //To change body of implemented methods use File | Settings | File Templates.
    }

    @Override
    public ItemRPSNFeVO consultarPorChavePrimaria(Integer codigo) throws Exception {
        return null;  //To change body of implemented methods use File | Settings | File Templates.
    }

    @Override
    public List<ItemRPSNFeVO> listarItensDaNota(NotaFiscalDeServicoVO notaVO) throws Exception {
        List<ItemRPSNFeVO> itens = new ArrayList<ItemRPSNFeVO>();
        String sql = "SELECT * FROM ItemRPS WHERE Id_RPS = ? ORDER BY Ordem;";

        PreparedStatement sqlConsulta = con.prepareStatement(sql);
        sqlConsulta.setInt(1, notaVO.getIdRPS());

        ResultSet tabelaResultado = sqlConsulta.executeQuery();
        while (tabelaResultado.next()) {
            ItemRPSNFeVO item = montarDados(tabelaResultado, this.con);
            itens.add(item);
        }

        return itens;
    }

    private ItemRPSNFeVO montarDados(ResultSet dadosSQL, Connection con) throws Exception {
        ItemRPSNFeVO item = new ItemRPSNFeVO();

        item.setId_itemRPS(dadosSQL.getInt("Id_ItemRPS"));
        item.setOrdem(dadosSQL.getInt("Ordem"));
        item.setValorUnitario(dadosSQL.getDouble("ValorUnitario"));
        item.setQuantidade(dadosSQL.getInt("Quantidade"));
        item.setDescricao(dadosSQL.getString("Descricao"));
        item.setTributavel(dadosSQL.getString("Tributavel"));

        return item;
    }
}

package negocio.facade.jdbc.nfe;

import negocio.comuns.financeiro.NFSeEmitidaFormaPagamentoTO;

import java.util.ArrayList;
import java.util.List;

public class RetornoNFSe {

    private Double valor = 0.0;
    private StringBuilder descricao = new StringBuilder();
    private boolean algumServico = false;
    private List<NFSeEmitidaFormaPagamentoTO> listaPagamentos;

    public Double getValor() {
        return valor;
    }

    public void setValor(Double valor) {
        this.valor = valor;
    }

    public StringBuilder getDescricao() {
        return descricao;
    }

    public void setDescricao(StringBuilder descricao) {
        this.descricao = descricao;
    }

    public boolean isAlgumServico() {
        return algumServico;
    }

    public void setAlgumServico(boolean algumServico) {
        this.algumServico = algumServico;
    }

    public List<NFSeEmitidaFormaPagamentoTO> getListaPagamentos() {
        if (listaPagamentos == null) {
            listaPagamentos = new ArrayList<NFSeEmitidaFormaPagamentoTO>();
        }
        return listaPagamentos;
    }

    public void setListaPagamentos(List<NFSeEmitidaFormaPagamentoTO> listaPagamentos) {
        this.listaPagamentos = listaPagamentos;
    }
}

package negocio.facade.jdbc.nfe;

import negocio.comuns.nfe.EmpresaNFeVO;
import negocio.comuns.utilitarias.ConsistirException;
import negocio.comuns.utilitarias.Ordenacao;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisValidacao;
import negocio.facade.jdbc.arquitetura.SuperEntidade;
import negocio.interfaces.nfe.EmpresaNFeInterfaceFacade;

import javax.faces.model.SelectItem;
import java.io.FileInputStream;
import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.sql.Statement;
import java.util.ArrayList;
import java.util.List;

public class EmpresaNFe extends SuperEntidade implements EmpresaNFeInterfaceFacade {

    public EmpresaNFe() throws Exception {
        super();
    }

    public EmpresaNFe(Connection con) throws Exception {
        super(con);
    }

    @Override
    public EmpresaNFeVO novo() throws Exception {
        return new EmpresaNFeVO();
    }

    private boolean existeCNPJNoBanco(String cnpj, int idEmpresa) throws Exception {
        StringBuilder sqlStr = new StringBuilder("SELECT * FROM Empresa ");
        sqlStr.append("WHERE CPFCNPJ like '").append(cnpj).append("' ");
        sqlStr.append("AND Id_empresa <> ").append(idEmpresa);

        Statement stm = con.createStatement();
        ResultSet tabelaResultado = stm.executeQuery(sqlStr.toString());
        return tabelaResultado.next();
    }

    private int obtenhaProximoIdDaTabela() throws Exception {
        String sql = "SELECT IDENT_CURRENT('Empresa') as id;";

        Statement stm = con.createStatement();
        ResultSet tabelaResultado = stm.executeQuery(sql);
        int proximoId = 0;
        if (tabelaResultado.next()) {
            proximoId = tabelaResultado.getInt("id") + 1;
        }

        return proximoId;
    }

    @Override
    public void incluir(EmpresaNFeVO obj) throws Exception {
        boolean sucesso = false;
        try {
            con.setAutoCommit(false);
            EmpresaNFeVO.validarDados(obj);

            if (existeCNPJNoBanco(obj.getCpf_cnpj(), obj.getId_Empresa())) {
                throw new ConsistirException("CPF/CNPJ Já cadastrado.");
            }

            String sql = "INSERT INTO Empresa(Id_Cidade, CPFCNPJ, InscricaoMunicipal, RazaoSocial, NomeFantasia, Ativa, " +
                    "DDDTelefone, Telefone, SenhaCertificado, RegimeEspecialTributacao, OptanteSimplesNacional, " +
                    "IncentivadorCultural, Chave, Senha, ccm, AliquotaSimples, InscricaoEstadual,Logradouro,Bairro,CFOP, " +
                    "CEP,Logomarca,Numero,Complemento,Email,EnviarImpostoIBPT,EnviarEmail,IdCsc,csc,InformacaoFisco,CSTPis, " +
                    "CSTCofins, usarXMLPrefeitura, serieNFCe, usarXMLManipulado, chaveZW, empresaZW, usaModuloNFCe, " +
                    "usarXMLManipuladoNFCe, CNPJMatriz, RazaoSocialMatriz, UsuarioSistema, id_executavel, EnviarApenasUmRPSPorVez,\n " +
                    "ProcessoHomologacao, NaoUsarDescricaoItemRPS)\n " +
                    "VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)";

            PreparedStatement sqlInserir = con.prepareStatement(sql);
            int i = 0;
            sqlInserir.setInt(++i, obj.getMunicipio().getId_Municipio());
            sqlInserir.setString(++i, obj.getCpf_cnpj());
            sqlInserir.setString(++i, obj.getInscricaoMunicipal());
            sqlInserir.setString(++i, obj.getRazaoSocial());
            sqlInserir.setString(++i, obj.getNomeFantasia());
            sqlInserir.setBoolean(++i, obj.isAtiva());
            sqlInserir.setInt(++i, obj.getDdd_telefone());
            sqlInserir.setString(++i, obj.getTelefone());
            sqlInserir.setString(++i, Uteis.encriptarNFe(obj.getSenhaCertificado()));
            sqlInserir.setInt(++i, obj.getCRT());
            sqlInserir.setBoolean(++i, obj.isOptateSimplesNacional());
            sqlInserir.setBoolean(++i, obj.isIncentivadorCultural());
            sqlInserir.setString(++i, Uteis.encriptar(String.valueOf(obtenhaProximoIdDaTabela())));
            sqlInserir.setString(++i, Uteis.encriptarNFe(obj.getSenhaInscricaoMunicipal()));
            sqlInserir.setString(++i, obj.getCcm());
            sqlInserir.setDouble(++i, obj.getAliquotaSimples());
            sqlInserir.setString(++i, obj.getInscricaoEstadual());
            sqlInserir.setString(++i, obj.getLogradouro());
            sqlInserir.setString(++i, obj.getBairro());
            sqlInserir.setString(++i, obj.getCFOP());
            sqlInserir.setString(++i, obj.getCEP());
            sqlInserir.setBytes(++i, obj.getFoto());
            sqlInserir.setInt(++i, obj.getNumEndereco());
            sqlInserir.setString(++i, obj.getComplemento());
            sqlInserir.setString(++i, obj.getEmail());
            sqlInserir.setBoolean(++i, obj.isImprimirImposto());
            sqlInserir.setBoolean(++i, obj.isEnviarEmail());
            sqlInserir.setString(++i, obj.getIdCsc());
            sqlInserir.setString(++i, obj.getCsc());
            if (UteisValidacao.emptyString(obj.getInformacaoFisco())){
                sqlInserir.setNull(++i, 0);
            } else {
                sqlInserir.setString(++i, obj.getInformacaoFisco());
            }
            sqlInserir.setString(++i, obj.getCSTPis());
            sqlInserir.setString(++i, obj.getCSTCofins());
            sqlInserir.setBoolean(++i, obj.isUsarXMLPrefeitura());
            sqlInserir.setString(++i, obj.getSerieNFCe());
            sqlInserir.setBoolean(++i, obj.isUsarXMLManipulado());
            sqlInserir.setString(++i, obj.getChaveZW());
            sqlInserir.setInt(++i, obj.getEmpresaZW());
            sqlInserir.setBoolean(++i, obj.isUsaModuloNFCe());
            sqlInserir.setBoolean(++i, obj.isUsarXMLManipuladoNFCe());
            sqlInserir.setString(++i, obj.getCnpjMatriz());
            sqlInserir.setString(++i, obj.getRazaoSocialMatriz());
            sqlInserir.setString(++i, obj.getUsuarioInscricaoMunicipal());
            sqlInserir.setString(++i, obj.getIdentificadorExecutavel());
            sqlInserir.setBoolean(++i, obj.isEnviarApenasUmRPSPorVez());
            sqlInserir.setBoolean(++i, obj.isProcessoHomologacao());
            sqlInserir.setBoolean(++i, obj.isNaoUsarDescricaoItemRPS());

            sqlInserir.execute();
            obj.setNovoObj(true);
            con.commit();

            sucesso = true;
        } catch (Exception e) {
            con.rollback();
            con.setAutoCommit(true);
            sucesso = false;
            throw e;
        } finally {
            con.setAutoCommit(true);

            if (sucesso) {
                int idDaEmpresa = (obtenhaProximoIdDaTabela() - 1);
                obj.setId_Empresa(idDaEmpresa);

                getFacade().getPerfilUsuarioNFe().criarPerfilConsultor(idDaEmpresa);
                int idDoPerfilPacto = getFacade().getPerfilUsuarioNFe().obtenhaIdDoPerfilConsultor(idDaEmpresa);
                getFacade().getUsuarioNFe().criarUsuarioPacto(idDoPerfilPacto);
            }
        }
    }

    public void incluirCertificado(EmpresaNFeVO empresaNFeVO) throws Exception {
        try {
            FileInputStream fis = new FileInputStream(empresaNFeVO.getCertificado());
            int tamanho = (int) empresaNFeVO.getCertificado().length();

            con.setAutoCommit(false);

            String sql = "INSERT INTO Arquivo (Id_PKRef, Identificador, Arquivo) VALUES (?, ?, ?);";

            PreparedStatement sqlInserir = con.prepareStatement(sql);
            sqlInserir.setInt(1, empresaNFeVO.getId_Empresa());
            sqlInserir.setString(2, "Certificado");
            sqlInserir.setBinaryStream(3, fis, tamanho);
            sqlInserir.execute();

            con.commit();
        } catch (Exception e) {
            con.rollback();
            con.setAutoCommit(true);
            throw e;
        } finally {
            con.setAutoCommit(true);
        }
    }

    @Override
    public void alterar(EmpresaNFeVO obj) throws Exception {
        try {
            con.setAutoCommit(false);
            EmpresaNFeVO.validarDados(obj);

            if (existeCNPJNoBanco(obj.getCpf_cnpj(), obj.getId_Empresa())) {
                throw new ConsistirException("CPF/CNPJ Já cadastrado.");
            }

            String sql = "UPDATE Empresa set Id_Cidade = ?, CPFCNPJ = ?, InscricaoMunicipal = ?, RazaoSocial = ?, NomeFantasia = ?, " +
                    "Ativa = ?, DDDTelefone = ?, Telefone = ?, SenhaCertificado = ?, RegimeEspecialTributacao = ?, " +
                    "OptanteSimplesNacional = ?, IncentivadorCultural = ?, Senha = ?, ccm = ?, AliquotaSimples = ?, " +
                    "InscricaoEstadual = ?, Logradouro = ?, Bairro = ?, CFOP = ?, CEP = ?, Logomarca = ?, Numero = ?, " +
                    "Complemento = ?, Email = ?, EnviarImpostoIBPT = ?, EnviarEmail = ?, IdCsc = ?, csc = ?, InformacaoFisco = ?, " +
                    "CSTPis = ?, CSTCofins = ?, usarXMLPrefeitura = ?, serieNFCe = ?, usarXMLManipulado = ?, chaveZW = ?, empresaZW = ?, " +
                    "usaModuloNFCe = ?, usarXMLManipuladoNFCe = ?, CNPJMatriz = ?, RazaoSocialMatriz = ?, UsuarioSistema = ?, id_executavel = ?, \n" +
                    "EnviarApenasUmRPSPorVez = ?, ProcessoHomologacao = ?, NaoUsarDescricaoItemRPS = ? \n" +
                    "WHERE Id_Empresa = ?";

            PreparedStatement sqlAlterar = con.prepareStatement(sql);
            int i = 0;
            sqlAlterar.setInt(++i, obj.getMunicipio().getId_Municipio());
            sqlAlterar.setString(++i, obj.getCpf_cnpj());
            sqlAlterar.setString(++i, obj.getInscricaoMunicipal());
            sqlAlterar.setString(++i, obj.getRazaoSocial());
            sqlAlterar.setString(++i, obj.getNomeFantasia());
            sqlAlterar.setBoolean(++i, obj.isAtiva());
            sqlAlterar.setInt(++i, obj.getDdd_telefone());
            sqlAlterar.setString(++i, obj.getTelefone());
            sqlAlterar.setString(++i, Uteis.encriptarNFe(obj.getSenhaCertificado()));
            sqlAlterar.setInt(++i, obj.getCRT());
            sqlAlterar.setBoolean(++i, obj.isOptateSimplesNacional());
            sqlAlterar.setBoolean(++i, obj.isIncentivadorCultural());
            sqlAlterar.setString(++i, Uteis.encriptarNFe(obj.getSenhaInscricaoMunicipal()));
            sqlAlterar.setString(++i, obj.getCcm());
            sqlAlterar.setDouble(++i, obj.getAliquotaSimples());
            sqlAlterar.setString(++i, obj.getInscricaoEstadual());
            sqlAlterar.setString(++i, obj.getLogradouro());
            sqlAlterar.setString(++i, obj.getBairro());
            sqlAlterar.setString(++i, obj.getCFOP());
            sqlAlterar.setString(++i, obj.getCEP());
            sqlAlterar.setBytes(++i, obj.getFoto());
            sqlAlterar.setInt(++i, obj.getNumEndereco());
            sqlAlterar.setString(++i, obj.getComplemento());
            sqlAlterar.setString(++i, obj.getEmail());
            sqlAlterar.setBoolean(++i, obj.isImprimirImposto());
            sqlAlterar.setBoolean(++i, obj.isEnviarEmail());
            sqlAlterar.setString(++i, obj.getIdCsc());
            sqlAlterar.setString(++i, obj.getCsc());
            if (UteisValidacao.emptyString(obj.getInformacaoFisco())){
                sqlAlterar.setNull(++i, 0);
            } else {
                sqlAlterar.setString(++i, obj.getInformacaoFisco());
            }
            sqlAlterar.setString(++i, obj.getCSTPis());
            sqlAlterar.setString(++i, obj.getCSTCofins());
            sqlAlterar.setBoolean(++i, obj.isUsarXMLPrefeitura());
            sqlAlterar.setString(++i, obj.getSerieNFCe());
            sqlAlterar.setBoolean(++i, obj.isUsarXMLManipulado());
            sqlAlterar.setString(++i, obj.getChaveZW());
            sqlAlterar.setInt(++i, obj.getEmpresaZW());
            sqlAlterar.setBoolean(++i, obj.isUsaModuloNFCe());
            sqlAlterar.setBoolean(++i, obj.isUsarXMLManipuladoNFCe());
            sqlAlterar.setString(++i, obj.getCnpjMatriz());
            sqlAlterar.setString(++i, obj.getRazaoSocialMatriz());
            sqlAlterar.setString(++i, obj.getUsuarioInscricaoMunicipal());
            sqlAlterar.setString(++i, obj.getIdentificadorExecutavel());
            sqlAlterar.setBoolean(++i, obj.isEnviarApenasUmRPSPorVez());
            sqlAlterar.setBoolean(++i, obj.isProcessoHomologacao());
            sqlAlterar.setBoolean(++i, obj.isNaoUsarDescricaoItemRPS());
            sqlAlterar.setInt(++i, obj.getId_Empresa());

            sqlAlterar.execute();
            con.commit();
        } catch (Exception e) {
            con.rollback();
            con.setAutoCommit(true);
            throw e;
        } finally {
            con.setAutoCommit(true);
        }
    }

    @Override
    public void excluir(EmpresaNFeVO obj) throws Exception {
        if (podeExcluirEmpresa(obj)) {
            try {
                con.setAutoCommit(false);
                String sql = "DELETE FROM Empresa WHERE (Id_Empresa = ?)";

                PreparedStatement sqlExcluir = con.prepareStatement(sql);
                sqlExcluir.setInt(1, obj.getId_Empresa());

                sqlExcluir.execute();
                con.commit();
            } catch (Exception e) {
                con.rollback();
                con.setAutoCommit(true);
                throw e;
            } finally {
                con.setAutoCommit(true);
            }
        } else {
            throw new ConsistirException("Existem dados cadastrados para esta Empresa.");
        }
    }

    private boolean podeExcluirEmpresa(EmpresaNFeVO empresa) throws Exception {
        List<SelectItem> perfis = getFacade().getPerfilUsuarioNFe().obtenhaPerfisDaEmpresa(empresa);

        return perfis.isEmpty();
    }

    @Override
    public EmpresaNFeVO consultarPorChavePrimaria(Integer codigo) throws Exception {
        consultar(getIdEntidade(), false);
        String sql = "SELECT * FROM Empresa WHERE Id_Empresa = ?";
        PreparedStatement sqlConsultar = con.prepareStatement(sql);
        sqlConsultar.setInt(1, codigo);
        ResultSet tabelaResultado = sqlConsultar.executeQuery();
        if (!tabelaResultado.next()) {
            throw new ConsistirException("Dados Não Encontrados ( Empresa ).");
        }
        return (montarDados(tabelaResultado, this.con));
    }

    public List<EmpresaNFeVO> consultarPorCodigo(int codigo,String situacaoEmpresa) throws Exception {
        List<EmpresaNFeVO> empresas = new ArrayList<EmpresaNFeVO>();

        StringBuilder sql = new StringBuilder("SELECT * FROM Empresa ");

        if (codigo != 0) {
            sql.append("WHERE Id_Empresa = ").append(codigo);
        }
        if (!situacaoEmpresa.equals("todos")) {
            sql.append("AND Ativa = '").append(situacaoEmpresa).append("'");
        }

        sql.append(" ORDER BY Id_Empresa ");

        PreparedStatement sqlConsulta = con.prepareStatement(sql.toString());

        ResultSet tabelaResultado = sqlConsulta.executeQuery();
        while (tabelaResultado.next()) {
            EmpresaNFeVO empresa = montarDados(tabelaResultado, this.con);
            empresas.add(empresa);
        }

        return empresas;
    }

    public List<EmpresaNFeVO> consultarPorRazaoSocial(String razaoSocial,String situacaoEmpresa) throws Exception {
        List<EmpresaNFeVO> empresas = new ArrayList<EmpresaNFeVO>();

        StringBuilder sql = new StringBuilder("SELECT * FROM Empresa ");
        sql.append("WHERE RazaoSocial Like '%").append(razaoSocial).append("%'");
        if (!situacaoEmpresa.equals("todos")) {
            sql.append("AND Ativa = '").append(situacaoEmpresa).append("'");
        }
        PreparedStatement sqlConsulta = con.prepareStatement(sql.toString());

        ResultSet tabelaResultado = sqlConsulta.executeQuery();
        while (tabelaResultado.next()) {
            EmpresaNFeVO empresa = montarDados(tabelaResultado, this.con);
            empresas.add(empresa);
        }

        return empresas;
    }

    public List<EmpresaNFeVO> consultarPorNomeFantasia(String nomeFantasia,String situacaoEmpresa) throws Exception {
        List<EmpresaNFeVO> empresas = new ArrayList<EmpresaNFeVO>();

        StringBuilder sql = new StringBuilder("SELECT * FROM Empresa ");
                sql.append("WHERE nomeFantasia Like '%").append(nomeFantasia).append("%'");
                if (!situacaoEmpresa.equals("todos")) {
                    sql.append("AND Ativa = '").append(situacaoEmpresa).append("'");
                }
        PreparedStatement sqlConsulta = con.prepareStatement(sql.toString());

        ResultSet tabelaResultado = sqlConsulta.executeQuery();
        while (tabelaResultado.next()) {
            EmpresaNFeVO empresa = montarDados(tabelaResultado, this.con);
            empresas.add(empresa);
        }

        return empresas;
    }


    public List<EmpresaNFeVO> consultarPorCidade(String cidade,String situacaoEmpresa) throws Exception {
        List<EmpresaNFeVO> empresas = new ArrayList<EmpresaNFeVO>();

        StringBuilder sql = new StringBuilder("SELECT e.* FROM empresa e INNER JOIN Cidade C ON e.Id_Cidade = C.Id_Cidade ");
        sql.append("WHERE c.Nome Like '%").append(cidade).append("%'");
        if (!situacaoEmpresa.equals("todos")) {
            sql.append("AND Ativa = '").append(situacaoEmpresa).append("'");
        }
        PreparedStatement sqlConsulta = con.prepareStatement(sql.toString());

        ResultSet tabelaResultado = sqlConsulta.executeQuery();
        while (tabelaResultado.next()) {
            EmpresaNFeVO empresa = montarDados(tabelaResultado, this.con);
            empresas.add(empresa);
        }

        return empresas;
    }

    public List<SelectItem> obtenhaEmpresasCadastradas() throws Exception {
        List<SelectItem> empresas = new ArrayList<SelectItem>();

        String sql = "SELECT Id_Empresa,NomeFantasia,RazaoSocial FROM Empresa WHERE Ativa = 'true' ORDER BY NomeFantasia;";
        PreparedStatement sqlConsultar = con.prepareStatement(sql);

        ResultSet rs = sqlConsultar.executeQuery();

        while (rs.next()) {

            String nomeFantasia = rs.getString("NomeFantasia");
            String razaoSocial = rs.getString("RazaoSocial");

            String nomeUtilizar = nomeFantasia;
            if (UteisValidacao.emptyString(nomeFantasia)) {
                nomeUtilizar = razaoSocial;
            }

            empresas.add(new SelectItem(rs.getInt("Id_Empresa"), nomeUtilizar.toUpperCase()));
        }
        Ordenacao.ordenarLista(empresas, "label");
        empresas.add(0, new SelectItem("0", "Selecione uma empresa"));
        return empresas;
    }

    private EmpresaNFeVO montarDados(ResultSet dadosSQL, Connection con) throws Exception {
        EmpresaNFeVO obj = new EmpresaNFeVO();
        obj.setId_Empresa(dadosSQL.getInt("Id_Empresa"));
        obj.setChave(dadosSQL.getString("Chave"));
        obj.setChaveZW(dadosSQL.getString("ChaveZW"));
        obj.setEmpresaZW(dadosSQL.getInt("EmpresaZW"));
        obj.setCpf_cnpj(dadosSQL.getString("CPFCNPJ"));
        obj.setInscricaoMunicipal(dadosSQL.getString("InscricaoMunicipal"));
        obj.setRazaoSocial(dadosSQL.getString("RazaoSocial"));
        obj.setNomeFantasia(dadosSQL.getString("NomeFantasia"));
        obj.setAtiva(dadosSQL.getBoolean("Ativa"));
        obj.setDdd_telefone(dadosSQL.getInt("DDDTelefone"));
        obj.setTelefone(dadosSQL.getString("Telefone"));
        obj.setSenhaCertificado(dadosSQL.getString("SenhaCertificado"));
        obj.setSenhaCertificadoDescripto(Uteis.desencriptarNFe(obj.getSenhaCertificado()));
        obj.setCRT(dadosSQL.getInt("RegimeEspecialTributacao"));
        obj.setOptateSimplesNacional(dadosSQL.getBoolean("OptanteSimplesNacional"));
        obj.setIncentivadorCultural(dadosSQL.getBoolean("IncentivadorCultural"));
        obj.setCcm(dadosSQL.getString("ccm"));
        obj.setAliquotaSimples(dadosSQL.getDouble("aliquotaSimples"));
        obj.setInscricaoEstadual(dadosSQL.getString("InscricaoEstadual"));
        obj.setUsuarioInscricaoMunicipal(dadosSQL.getString("UsuarioSistema"));
        obj.setSenhaInscricaoMunicipal(dadosSQL.getString("Senha"));
        obj.setSenhaInscricaoMunicipalDescripto(Uteis.desencriptarNFe(obj.getSenhaInscricaoMunicipal()));
        obj.setLogradouro(dadosSQL.getString("Logradouro"));
        obj.setBairro(dadosSQL.getString("Bairro"));
        obj.setCEP(dadosSQL.getString("CEP"));
        obj.setFoto(dadosSQL.getBytes("Logomarca"));
        obj.setCFOP(dadosSQL.getString("CFOP"));
        obj.setNumEndereco(dadosSQL.getInt("Numero"));
        obj.setComplemento(dadosSQL.getString("Complemento"));
        obj.setEmail(dadosSQL.getString("Email"));
        obj.setImprimirImposto(dadosSQL.getBoolean("EnviarImpostoIBPT"));
        obj.setEnviarEmail(dadosSQL.getBoolean("EnviarEmail"));
        obj.setDataVencimentoCertificado(dadosSQL.getTimestamp("DataCertificado"));
        obj.setIdCsc(dadosSQL.getString("idcsc"));
        obj.setCsc(dadosSQL.getString("csc"));
        obj.setInformacaoFisco(dadosSQL.getString("InformacaoFisco"));
        obj.setCSTPis(dadosSQL.getString("CSTPis"));
        obj.setCSTCofins(dadosSQL.getString("CSTCofins"));
        obj.setUsarXMLPrefeitura(dadosSQL.getBoolean("usarXMLPrefeitura"));
        obj.setSerieNFCe(dadosSQL.getString("SerieNFCe"));
        obj.setUsarXMLManipulado(dadosSQL.getBoolean("usarXMLManipulado"));
        obj.setUsaModuloNFCe(dadosSQL.getBoolean("usaModuloNFCe"));
        obj.setUsarXMLManipuladoNFCe(dadosSQL.getBoolean("usarXMLManipuladoNFCe"));
        obj.setCnpjMatriz(dadosSQL.getString("CNPJMatriz"));
        obj.setRazaoSocialMatriz(dadosSQL.getString("RazaoSocialMatriz"));
        MunicipioNFe municipio = new MunicipioNFe(con);
        obj.setMunicipio(municipio.consultarPorChavePrimaria(dadosSQL.getInt("Id_Cidade")));
        obj.setParams(dadosSQL.getString("params"));
        obj.setIdentificadorExecutavel(dadosSQL.getString("id_executavel"));
        obj.setEnviarApenasUmRPSPorVez(dadosSQL.getBoolean("EnviarApenasUmRPSPorVez"));
        obj.setProcessoHomologacao(dadosSQL.getBoolean("ProcessoHomologacao"));
        obj.setNaoUsarDescricaoItemRPS(dadosSQL.getBoolean("NaoUsarDescricaoItemRPS"));

        return obj;
    }

    public boolean temCerficadoCadastrado(EmpresaNFeVO empresaNFeVO) throws Exception {
        String sql = "SELECT * FROM Arquivo WHERE Id_PKRef = ? AND Identificador = ?";
        PreparedStatement sqlConsultar = con.prepareStatement(sql);
        sqlConsultar.setInt(1, empresaNFeVO.getId_Empresa());
        sqlConsultar.setString(2, "Certificado");

        ResultSet tabelaResultado = sqlConsultar.executeQuery();

        return tabelaResultado.next();
    }

    public void atualizarCertificado(EmpresaNFeVO empresaNFeVO) throws Exception {
        try {
            FileInputStream fis = new FileInputStream(empresaNFeVO.getCertificado());
            int tamanho = (int) empresaNFeVO.getCertificado().length();

            con.setAutoCommit(false);

            String sql = "UPDATE Arquivo SET Arquivo = ? WHERE Id_PKRef = ? AND Identificador = ?";

            PreparedStatement sqlAlterar = con.prepareStatement(sql);
            sqlAlterar.setBinaryStream(1, fis, tamanho);
            sqlAlterar.setInt(2, empresaNFeVO.getId_Empresa());
            sqlAlterar.setString(3, "Certificado");

            sqlAlterar.execute();
            con.commit();
        } catch (Exception e) {
            con.rollback();
            con.setAutoCommit(true);
            throw e;
        } finally {
            con.setAutoCommit(true);
        }
    }

    public EmpresaNFeVO consultarPorChave(String chave) throws Exception {
        StringBuilder sql = new StringBuilder("SELECT * FROM Empresa ");
        sql.append("WHERE chave Like '%").append(chave).append("%'");
        PreparedStatement sqlConsulta = con.prepareStatement(sql.toString());

        ResultSet tabelaResultado = sqlConsulta.executeQuery();
        if (tabelaResultado.next()) {
            return montarDados(tabelaResultado, this.con);
        } else {
            return new EmpresaNFeVO();
        }
    }

    public void atualizarParametros(EmpresaNFeVO empresaNFeVO, String params) throws SQLException {
        String sqlAtualizarParamsEmpresa = "UPDATE empresa SET params = '" + params + "'\n" +
                "WHERE Id_Empresa = " + empresaNFeVO.getId_Empresa() + ";";
        PreparedStatement sqlAlterar = con.prepareStatement(sqlAtualizarParamsEmpresa);
        sqlAlterar.execute();
    }
}

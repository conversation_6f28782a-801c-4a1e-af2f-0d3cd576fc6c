package negocio.facade.jdbc.nfe;

import br.com.pactosolucoes.comuns.util.FileUtilities;
import br.com.pactosolucoes.comuns.util.Formatador;
import br.com.pactosolucoes.comuns.util.StringUtilities;
import negocio.comuns.basico.webservice.IntegracaoNFSeWS;
import negocio.comuns.nfe.DocumentoFiscalRelatorioPeriodoTO;
import negocio.comuns.nfe.FiltroNFeTO;
import negocio.comuns.nfe.FormaPagamentoNFCeVO;
import negocio.comuns.nfe.ItemNFCeVO;
import negocio.comuns.nfe.LogOperacaoNotaVO;
import negocio.comuns.nfe.NFCeFormaPagamentoImprimirTO;
import negocio.comuns.nfe.NFCeImprimirTO;
import negocio.comuns.nfe.NFCeProdutoImprimirTO;
import negocio.comuns.nfe.NFSeImprimirTO;
import negocio.comuns.nfe.NFeFaturasImprimirTO;
import negocio.comuns.nfe.NFeImprimirTO;
import negocio.comuns.nfe.NFeObsImprimirTO;
import negocio.comuns.nfe.NFeProdutoImprimirTO;
import negocio.comuns.nfe.NotaFiscalConsumidorNFCeVO;
import negocio.comuns.nfe.NotaFiscalDeServicoVO;
import negocio.comuns.nfe.NotasImprimirTO;
import negocio.comuns.nfe.NumerosInutilizarVO;
import negocio.comuns.nfe.OperacaoNotaVO;
import negocio.comuns.nfe.enumerador.CRTEnum;
import negocio.comuns.nfe.enumerador.FormaPagamentoNFCeEnum;
import negocio.comuns.nfe.enumerador.ModeloDocumentoFiscal;
import negocio.comuns.nfe.enumerador.NaturezaOperacaoEnum;
import negocio.comuns.nfe.enumerador.StatusNFCeEnum;
import negocio.comuns.nfe.enumerador.TipoNotaEnum;
import negocio.comuns.utilitarias.Calendario;
import negocio.comuns.utilitarias.ConsistirException;
import negocio.comuns.utilitarias.QRCode;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisValidacao;
import negocio.facade.jdbc.arquitetura.SuperEntidade;
import negocio.interfaces.nfe.NotaFiscalDeServicoInterfaceFacade;
import net.glxn.qrgen.image.ImageType;
import org.apache.commons.io.FileUtils;
import org.apache.commons.lang.StringUtils;
import org.json.JSONArray;
import org.json.JSONException;
import org.json.JSONObject;
import relatorio.controle.arquitetura.SuperControleRelatorio;
import servicos.operacoes.midias.MidiaService;
import servicos.operacoes.midias.commons.MidiaEntidadeEnum;

import javax.servlet.http.HttpServletRequest;
import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.sql.Statement;
import java.sql.Types;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;

import static negocio.comuns.nfe.enumerador.StatusNFCeEnum.CANCELADO;
import static negocio.comuns.nfe.enumerador.StatusNFCeEnum.NAO_AUTORIZADO;

public class NotaFiscalDeServico extends SuperEntidade implements NotaFiscalDeServicoInterfaceFacade {

    public NotaFiscalDeServico() throws Exception {
        super();
    }

    public NotaFiscalDeServico(Connection con) throws Exception {
        super(con);
    }

    private StringBuilder getSQLBase() {StringBuilder sql = new StringBuilder();
        sql.append("SELECT \n");
        sql.append("r.*, \n");
        sql.append("cast((SELECT COUNT(*) FROM OperacaoRPS o WHERE o.Id_RPS = r.Id_RPS AND o.Operacao = 'Envio e-mail' AND o.Finalizado = 1) as bit) EmailEnviado, \n");
        sql.append("(select id_lote from rps where id_rps = r.IdReenvio) as idLoteReenvio, \n");
        sql.append("c.tipolayout as tipolayoutCidadePrest, \n");
        sql.append("(select top 1 Id_DadosImpressaoNFe from DadosImpressaoNFe where id_rps = r.id_rps) as dadosNFe, \n");
        sql.append("(select top 1 id_DadosImpressaoNFSe from DadosImpressaoNFSe where id_rps = r.id_rps) as dadosNFSe, \n");
        sql.append("ce.horascancelamento, \n");
        sql.append("ce.cancelaratefinalmes, \n");
        sql.append("l.urlconfirmacao \n");
        sql.append("FROM RPS r \n");
        sql.append("LEFT JOIN Lote l ON r.Id_Lote = l.Id_lote \n");
        sql.append("LEFT JOIN cidade c ON c.Id_Cidade = r.Id_CidadePrest \n");
        sql.append("LEFT JOIN empresa e ON l.id_empresa = e.id_empresa \n");
        sql.append("LEFT JOIN cidade ce ON ce.id_cidade = e.id_cidade \n");
        sql.append("WHERE 1 = 1 \n");
        return sql;
    }

    @Override
    public NotaFiscalDeServicoVO consultarPorChavePrimaria(Integer codigo, Integer nivelMontarDados) throws Exception {

        StringBuilder sql = getSQLBase();
        sql.append("AND r.id_rps = ? ");

        PreparedStatement sqlConsulta = con.prepareStatement(sql.toString());
        sqlConsulta.setInt(1, codigo);

        ResultSet tabelaResultado = sqlConsulta.executeQuery();
        if (!tabelaResultado.next()) {
            throw new ConsistirException("Dados Não Encontrados ( NFSe ).");
        }
        return montarDados(tabelaResultado, nivelMontarDados, this.con);
    }

    public List<NotaFiscalDeServicoVO> listarNotas(Integer empresaVO, FiltroNFeTO filtroTO, String ordenador) throws Exception {
        List<NotaFiscalDeServicoVO> notas = new ArrayList<NotaFiscalDeServicoVO>();

        StringBuilder sql = getSQLBase();
        sql.append("AND l.Id_Empresa = ? \n");
        sql.append(filtroTO.obtenhaCondicoes());

        if (ordenador != null && !ordenador.equals("")) {
            sql.append("\n ORDER BY ").append(ordenador).append(" ASC ");
        } else {
            sql.append("\nORDER BY r.Id_RPS DESC");
        }
        PreparedStatement sqlConsulta = con.prepareStatement(sql.toString());

        if (empresaVO == null) {
            sqlConsulta.setNull(1, Types.INTEGER);
        } else {
            sqlConsulta.setInt(1, empresaVO);
        }

        ResultSet tabelaResultado = sqlConsulta.executeQuery();

        Date d1 = negocio.comuns.utilitarias.Calendario.hoje();
        while (tabelaResultado.next()) {
            NotaFiscalDeServicoVO nota = montarDados(tabelaResultado, Uteis.NIVELMONTARDADOS_DADOSBASICOS, this.con);
            notas.add(nota);
        }
        Date d2 = negocio.comuns.utilitarias.Calendario.hoje();
        System.out.println("Tempo montar dados Notas: " + (d2.getTime() - d1.getTime()));
        return notas;
    }

    private NotaFiscalDeServicoVO montarDados(ResultSet dadosSQL, Integer nivelMontarDados, Connection con) throws Exception {
        NotaFiscalDeServicoVO nota = new NotaFiscalDeServicoVO();
        nota.setIdRPS(dadosSQL.getInt("Id_RPS"));
        nota.setIdLote(dadosSQL.getInt("Id_Lote"));
        nota.setStatus(dadosSQL.getString("Status"));
        nota.setDataEmissao(dadosSQL.getTimestamp("DataEmissao"));
        nota.setDataProcessamento(dadosSQL.getTimestamp("DataProcessamento"));
        nota.setNaturezaOperacao(dadosSQL.getString("NaturezaOperacao"));
        nota.setSerieRPS(dadosSQL.getString("SerieRPS"));
        nota.setNumeroRPS(dadosSQL.getInt("NumeroRPS"));
        nota.setNumeroNota(dadosSQL.getString("NumeroNota"));
        nota.setInscricaoMunicipalCons(dadosSQL.getString("InscricaoMunicipalCons"));
        nota.setCpfCnpjCons(dadosSQL.getString("CPFCNPJCons"));
        nota.setRazaoSocialCons(dadosSQL.getString("RazaoSocialCons"));
        nota.setNomeAluno(dadosSQL.getString("NomeAluno"));
        nota.setTipoLogradouroCons(dadosSQL.getString("TipoLogradouroCons"));
        nota.setLogradouroCons(dadosSQL.getString("LogradouroCons"));
        nota.setNumeroEnderecoCons(dadosSQL.getString("NumeroEnderecoCons"));
        nota.setComplementoEnderecoCons(dadosSQL.getString("ComplementoEnderecoCons"));
        nota.setTipoBairroCons(dadosSQL.getString("TipoBairroCons"));
        nota.setBairroCons(dadosSQL.getString("BairroCons"));
        nota.setCepCons(dadosSQL.getString("CEPCons"));
        nota.setEmailCons(dadosSQL.getString("EmailCons"));
        nota.setCodAtividade(dadosSQL.getString("CodAtividade"));
        nota.setItemListaServico(dadosSQL.getString("ItemListaServico"));
        nota.setCodigoCnae(dadosSQL.getString("CodigoCnae"));
        nota.setCodigoTributacaoMunicipio(dadosSQL.getString("CodigoTributacaoMunicipio"));
        nota.setAliquotaAtividade(dadosSQL.getDouble("AliquotaAtividade"));
        nota.setTipoRecolhimento(dadosSQL.getString("TipoRecolhimento"));
        nota.setTributacao(dadosSQL.getString("Tributacao"));
        nota.setValorServicos(dadosSQL.getDouble("ValorServicos"));
        nota.setValorDeducoes(dadosSQL.getDouble("ValorDeducoes"));
        nota.setValorTotal(dadosSQL.getDouble("ValorTotal"));
        nota.setValorLiquido(dadosSQL.getDouble("ValorLiquido"));
        nota.setValorPIS(dadosSQL.getDouble("ValorPIS"));
        nota.setValorCOFINS(dadosSQL.getDouble("ValorCOFINS"));
        nota.setValorINSS(dadosSQL.getDouble("ValorINSS"));
        nota.setValorIR(dadosSQL.getDouble("ValorIR"));
        nota.setValorCSLL(dadosSQL.getDouble("ValorCSLL"));
        nota.setAliquotaPIS(dadosSQL.getDouble("AliquotaPIS"));
        nota.setValorCOFINS(dadosSQL.getDouble("AliquotaCOFINS"));
        nota.setAliquotaINSS(dadosSQL.getDouble("AliquotaINSS"));
        nota.setAliquotaIR(dadosSQL.getDouble("AliquotaIR"));
        nota.setAliquotaCSLL(dadosSQL.getDouble("AliquotaCSLL"));
        nota.setISSRetido(dadosSQL.getInt("ISSRetido"));
        nota.setOutrasRetencoes(dadosSQL.getDouble("OutrasRetencoes"));
        nota.setBaseCalculo(dadosSQL.getDouble("BaseCalculo"));
        nota.setValorISSRetido(dadosSQL.getDouble("ValorISSRetido"));
        nota.setDescontoIncondicionado(dadosSQL.getDouble("DescontoIncondicionado"));
        nota.setDescontoCondicionado(dadosSQL.getDouble("DescontoCondicionado"));
        nota.setDescricao(dadosSQL.getString("Descricao"));
        nota.setEmailEnviado(dadosSQL.getBoolean("EmailEnviado"));
        nota.setMotivo(dadosSQL.getString("motivo"));
        nota.setCodigoVerificacao(dadosSQL.getString("CodigoVerificacao"));
        nota.setTelefoneCons(dadosSQL.getString("TelefoneCons"));
        nota.setInscricaoEstadual(dadosSQL.getString("InscricaoEstadual"));
        nota.setCfdf(dadosSQL.getString("CFDF"));
        nota.setObservacao(dadosSQL.getString("Observacao"));
        nota.setExigibilidadeISS(dadosSQL.getInt("exigibilidadeISS"));
        nota.getCidadePrest().setCodigo(dadosSQL.getInt("Id_CidadePrest"));
        nota.getCidadePrest().setTipoLayout(dadosSQL.getInt("tipolayoutCidadePrest"));
        nota.getLote().getEmpresaNFeVO().getMunicipio().setHorasCancelamento(dadosSQL.getInt("horascancelamento"));
        nota.getLote().getEmpresaNFeVO().getMunicipio().setCancelarAteFinalMes(dadosSQL.getBoolean("cancelaratefinalmes"));
        nota.getLote().setUrlConfirmacao(dadosSQL.getString("urlconfirmacao"));
        nota.setIdReenvio(dadosSQL.getInt("idLoteReenvio"));
        nota.setExcluido(dadosSQL.getBoolean("excluido"));
        nota.setIdReferencia(dadosSQL.getString("idReferencia"));

        Integer dadosNFe = dadosSQL.getInt("dadosNFe");
        Integer dadosNFSe = dadosSQL.getInt("dadosNFSe");
        if (!UteisValidacao.emptyNumber(dadosNFe) || !UteisValidacao.emptyNumber(dadosNFSe)) {
            nota.setExisteDadosImpressao(true);
        } else {
            nota.setExisteDadosImpressao(false);
        }

        if (nivelMontarDados == Uteis.NIVELMONTARDADOS_DADOSBASICOS) {
            return nota;
        }

        if (nivelMontarDados == Uteis.NIVELMONTARDADOS_DADOSENTIDADESPRINCIPAIS) {
            MunicipioNFe cidade = new MunicipioNFe(con);
            nota.setCidadeCons(cidade.consultarPorChavePrimaria(dadosSQL.getInt("Id_CidadeCons")));
            nota.setCidadePrest(cidade.consultarPorChavePrimaria(dadosSQL.getInt("Id_CidadePrest")));
            return nota;
        }

        LoteNFe lote = new LoteNFe(con);
        nota.setLote(lote.consultarPorChavePrimaria(dadosSQL.getInt("Id_Lote")));

        return nota;
    }

    @Override
    public void cancelarNota(NotaFiscalDeServicoVO obj, int idUsuario) throws Exception {
        try {
            con.setAutoCommit(false);

            StringBuilder sqlCancelamento = new StringBuilder("INSERT INTO OperacaoRPS ");
            sqlCancelamento.append("(Id_RPS, Id_Usuario, DataHora, Operacao, Descricao) ");
            sqlCancelamento.append("Values (?, ?, GETDATE(), ?, ?)");

            PreparedStatement sqlCancelar = con.prepareStatement(sqlCancelamento.toString());

            sqlCancelar.setInt(1, obj.getIdRPS());
            sqlCancelar.setInt(2, idUsuario);
            sqlCancelar.setString(3, "Cancelamento");
            sqlCancelar.setString(4, obj.getJustificativaParaCancelar());

            sqlCancelar.execute();

            obj.setNovoObj(true);
            con.commit();
        } catch (Exception e) {
            con.rollback();
            con.setAutoCommit(true);
            throw e;
        } finally {
            con.setAutoCommit(true);
        }
    }

    public void cancelarNotaNFCe(NotaFiscalConsumidorNFCeVO obj, int idUsuario) throws Exception {
        try {
            con.setAutoCommit(false);

            StringBuilder sqlCancelamento = new StringBuilder("INSERT INTO operacaonfce ");
            sqlCancelamento.append("(Id_NFCE, Id_Usuario, DataHora, Operacao, Descricao) ");
            sqlCancelamento.append("Values (?, ?, GETDATE(), ?, ?)");

            PreparedStatement sqlCancelar = con.prepareStatement(sqlCancelamento.toString());

            sqlCancelar.setInt(1, obj.getId_NFCe());
            sqlCancelar.setInt(2, idUsuario);
            sqlCancelar.setString(3, "Cancelamento");
            sqlCancelar.setString(4, obj.getJustificativaParaCancelar());

            sqlCancelar.execute();

            obj.setNovoObj(true);
            con.commit();
        } catch (Exception e) {
            con.rollback();
            con.setAutoCommit(true);
            throw e;
        } finally {
            con.setAutoCommit(true);
        }
    }

    public NotaFiscalDeServicoVO alterarStatusDaNota(NotaFiscalDeServicoVO nota, String status) throws Exception {
        nota.setStatus(status);
        try {
            con.setAutoCommit(false);

            String sql = "UPDATE RPS SET Status = ? WHERE Id_RPS = ?";

            PreparedStatement sqlAlterar = con.prepareStatement(sql);
            sqlAlterar.setString(1, status);
            sqlAlterar.setInt(2, nota.getIdRPS());

            sqlAlterar.execute();
            con.commit();
        } catch (Exception e) {
            con.rollback();
            con.setAutoCommit(true);
            throw e;
        } finally {
            con.setAutoCommit(true);
        }
        return nota;
    }

    public void enviarEmail(NotaFiscalDeServicoVO obj, int idUsuario, String observacao) throws Exception {
        try {
            con.setAutoCommit(false);

            String sql = "INSERT INTO OperacaoRPS (Id_RPS, Id_Usuario, DataHora, Operacao, Descricao, observacao) VALUES (?, ?, GETDATE(), ?, ?, ?)";

            PreparedStatement sqlSolicitarEnvioEmail = con.prepareStatement(sql);
            sqlSolicitarEnvioEmail.setInt(1, obj.getIdRPS());
            sqlSolicitarEnvioEmail.setInt(2, idUsuario);
            sqlSolicitarEnvioEmail.setString(3, "Envio e-mail");
            sqlSolicitarEnvioEmail.setString(4, obj.getEmailConsParaEnvio());
            sqlSolicitarEnvioEmail.setString(5, observacao);

            sqlSolicitarEnvioEmail.execute();
            con.commit();
        } catch (Exception e) {
            con.rollback();
            con.setAutoCommit(true);
            throw e;
        } finally {
            con.setAutoCommit(true);
        }
    }

    public List<OperacaoNotaVO> historicoDaNota(NotaFiscalDeServicoVO obj) throws Exception {
        List<OperacaoNotaVO> historico = new ArrayList<OperacaoNotaVO>();

        StringBuilder sql = new StringBuilder("SELECT * FROM OperacaoRPS o ");
        sql.append("LEFT JOIN Usuario u ON o.Id_Usuario = u.Id_Usuario ");
        sql.append("WHERE Id_RPS = ? ORDER BY DataHora DESC;");

        PreparedStatement sqlConsulta = con.prepareStatement(sql.toString());
        sqlConsulta.setInt(1, obj.getIdRPS());

        ResultSet tabelaResultado = sqlConsulta.executeQuery();
        while (tabelaResultado.next()) {
            OperacaoNotaVO operacao = montarDadosDaOperacao(tabelaResultado);
            historico.add(operacao);
        }

        return historico;
    }

    private OperacaoNotaVO montarDadosDaOperacao(ResultSet dadosSQL) throws Exception {
        OperacaoNotaVO operacao = new OperacaoNotaVO();
        operacao.setId_OperacaoRPS(dadosSQL.getInt("Id_OperacaoRPS"));
        operacao.setId_RPS(dadosSQL.getInt("Id_RPS"));
        operacao.setId_Usuario(dadosSQL.getInt("Id_Usuario"));
        operacao.setNomeDoUsuario(dadosSQL.getString("Nome"));
        operacao.setDataHora(dadosSQL.getTimestamp("DataHora"));
        operacao.setOperacao(dadosSQL.getString("Operacao"));
        operacao.setDescricao(dadosSQL.getString("Descricao"));
        operacao.setFinalizado(dadosSQL.getBoolean("Finalizado"));
        operacao.setObservacao(dadosSQL.getString("observacao"));

        return operacao;
    }

    public byte[] obtenhaArquivoNota(String extensao, Integer codigoReferencia, Date dataReferencia, boolean NFCe, boolean xmlPrefeitura, boolean xmlManipulado, String caminho, boolean xmlCancelamento, boolean xmlManipuladoNFCe) throws Exception {
        byte[] arquivoDaNota = new byte[0];
        if(extensao.equals("pdf")){
            arquivoDaNota  = obtenhaPDFNota(codigoReferencia, dataReferencia, NFCe);
            if (arquivoDaNota == null) {
                arquivoDaNota  = obtenhaPDFNota(codigoReferencia, Uteis.somarCampoData(dataReferencia, Calendar.MONTH, 1), NFCe);
            }
        } else {
            arquivoDaNota = obtenhaXMLNota(codigoReferencia, NFCe, xmlPrefeitura, xmlManipulado, xmlCancelamento, xmlManipuladoNFCe);
        }
        return arquivoDaNota;
    }


    public byte[] obtenhaArquivoDaNota(NotaFiscalDeServicoVO obj, String tipoDoArquivo) throws Exception {
        byte[] xmlDaNota = new byte[0];
        String sql = "SELECT * FROM Arquivo WHERE Id_PKRef = ? AND Identificador = ? ;";

        PreparedStatement sqlConsulta = con.prepareStatement(sql);
        sqlConsulta.setInt(1, obj.getIdRPS());
        sqlConsulta.setString(2, tipoDoArquivo);

        ResultSet tabelaResultado = sqlConsulta.executeQuery();
        if (tabelaResultado.next()) {
            xmlDaNota = tabelaResultado.getBytes("Arquivo");
//            OutputStream arquivo = new FileOutputStream("arquivo.png");
//            arquivo.write(xmlDaNota);
//            arquivo.close();
        }

        return xmlDaNota;
    }

    public byte[] obtenhaPDFNota(Integer codigoReferencia, Date dataReferencia, boolean NFCe) throws Exception {
        byte[] pdfDaNota = new byte[0];
        if(dataReferencia == null){
            return pdfDaNota;
        }

        String chave = "pactonfse/"+Uteis.getAnoData(dataReferencia)+"/" + Uteis.getMesReferencia(dataReferencia) + "/PDFNF";
        if (NFCe) {
            chave = "pactonfse/"+Uteis.getAnoData(dataReferencia)+"/" + Uteis.getMesReferencia(dataReferencia) + "/PDFNC";
        }

        if (!UteisValidacao.emptyNumber(codigoReferencia)) {
            pdfDaNota = MidiaService.getInstancePdf().downloadObjectAsByteArray(chave,
                    MidiaEntidadeEnum.PDF_NFE, codigoReferencia.toString(), null);
        }
        return pdfDaNota;
    }

    public byte[] obtenhaXMLNota(Integer codigoReferencia, boolean NFCe, boolean xmlPrefeitura, boolean xmlManipulado, boolean xmlCancelamento, boolean xmlManipuladoNFCe) throws Exception {
        byte[] xmlDaNota = new byte[0];

        String identificadorPasta = obterPastaReferencia(codigoReferencia);
        String identificadorArquivo = "XML-N" + codigoReferencia;
        String chave = "pactonfse/XML-N/"+ identificadorPasta + "/";

        if (NFCe) {

            if (xmlCancelamento) {
                identificadorArquivo = "XMLCC" + codigoReferencia;
                chave = "pactonfse/XMLCC/"+ identificadorPasta + "/";
            } else {
                if (xmlManipuladoNFCe) {
                    identificadorArquivo = "XMLPC" + codigoReferencia;
                    chave = "pactonfse/XMLPC/" + identificadorPasta + "/";
                } else {
                    identificadorArquivo = "XMLNC" + codigoReferencia;
                    chave = "pactonfse/XMLNC/" + identificadorPasta + "/";
                }
            }

        } else {

            if (xmlCancelamento) {

                identificadorArquivo = "XML-C" + codigoReferencia;
                chave = "pactonfse/XML-C/"+ identificadorPasta + "/";

            } else {

                if (xmlPrefeitura) {
                    //TICKET #8639
                    identificadorArquivo = "XMLSO" + codigoReferencia;
                    chave = "pactonfse/XMLSO/"+ identificadorPasta + "/";
                }

                if (xmlManipulado) {
                    //TICKET #9361
                    identificadorArquivo = "XMLPR" + codigoReferencia;
                    chave = "pactonfse/XMLPR/"+ identificadorPasta + "/";
                }

            }
        }

        if (!UteisValidacao.emptyNumber(codigoReferencia)) {
            xmlDaNota  = MidiaService.getInstancePdf().downloadObjectAsByteArray(chave,MidiaEntidadeEnum.XML_NFE, identificadorArquivo, null);

            if (xmlDaNota == null && !xmlManipulado) {
                identificadorArquivo = "XMLPR" + codigoReferencia;
                chave = "pactonfse/XMLPR/" + identificadorPasta + "/";
                xmlDaNota = MidiaService.getInstancePdf().downloadObjectAsByteArray(chave, MidiaEntidadeEnum.XML_NFE, identificadorArquivo, null);
            }
        }
        return xmlDaNota ;
    }

    public String obterPastaReferencia(Integer codigoReferencia) {

        String identiInicio = "";
        String identiFinal = "";

        String rps = codigoReferencia.toString();
        String novo = "";

        if (rps.length() > 3) {
            novo = rps.substring(0, rps.length()-3);
            identiInicio = novo + "000";
            identiFinal = novo + "999";
        } else {
            identiInicio = "0";
            identiFinal = "999";
        }

        return  identiInicio + "-" + identiFinal;
    }

    public List<LogOperacaoNotaVO> logDaNota(NotaFiscalDeServicoVO obj) throws Exception {
        List<LogOperacaoNotaVO> listaLog = new ArrayList<LogOperacaoNotaVO>();

        StringBuilder sql = new StringBuilder();

        sql.append("SELECT top 20 * FROM logoperacaorps \n");
        sql.append("WHERE id_operacaorps IN (SELECT id_operacaorps FROM operacaorps \n");
        sql.append("WHERE id_rps = ? ) \n");
        sql.append("ORDER BY DataHora DESC");

        PreparedStatement sqlConsulta = con.prepareStatement(sql.toString());
        sqlConsulta.setInt(1, obj.getIdRPS());

        ResultSet tabelaResultado = sqlConsulta.executeQuery();
        while (tabelaResultado.next()) {
            LogOperacaoNotaVO log = new LogOperacaoNotaVO();
            log.setId_LogOperacaoRPS(tabelaResultado.getInt("Id_LogOperacaoRPS"));
            log.setId_OperacaoRPS(tabelaResultado.getInt("Id_OperacaoRPS"));
            log.setDataHora(tabelaResultado.getTimestamp("DataHora"));
            log.setResultado(tabelaResultado.getString("resultado"));
            log.setNotaFiscalVO(obj);
            listaLog.add(log);
        }
        return listaLog;
    }

    public void moverPDFNotas() throws SQLException, Exception{
        StringBuilder sql = new StringBuilder();
        sql.append("SELECT \n");
        sql.append("r.* \n");
        sql.append("FROM RPS r \n");
        sql.append("WHERE r.excluido = 0 \n");
        sql.append(" AND r.Status LIKE 'Autorizado'");
        sql.append("ORDER BY r.Id_RPS ");

        PreparedStatement sqlConsulta = con.prepareStatement(sql.toString());


        ResultSet tabelaResultado = sqlConsulta.executeQuery();

        Date d1 = negocio.comuns.utilitarias.Calendario.hoje();
        String key ="";
        String keyDestinatario = "";
        while (tabelaResultado.next()) {
            Date dataProcessamento = tabelaResultado.getDate("DataProcessamento");
            if(dataProcessamento != null){
                key= "prod-zwphotos/PDFNF"+ tabelaResultado.getInt("Id_RPS")+".pdf";
                keyDestinatario = "pactonfse/"+Uteis.getAnoData(dataProcessamento)+"/" + Uteis.getMesReferencia(dataProcessamento)+"/PDFNF"+ tabelaResultado.getInt("Id_RPS")+".pdf";
                MidiaService.getInstancePdf().moveObject(key,keyDestinatario);
            }
        }
        Date d2 = negocio.comuns.utilitarias.Calendario.hoje();
        System.out.println("Tempo mover Notas: " + (d2.getTime() - d1.getTime()));
    }

    public void alterarStatusNotaFiscal(String status, String motivo, Integer id_rps) throws Exception {
        try {
            con.setAutoCommit(false);

            String sql = "UPDATE rps SET status = ? , motivo = ? WHERE id_rps = ?";

            PreparedStatement sqlAlterarNota = con.prepareStatement(sql);
            sqlAlterarNota.setString(1, status);
            sqlAlterarNota.setString(2, motivo);
            sqlAlterarNota.setInt(3, id_rps);
            sqlAlterarNota.execute();

            con.commit();
        } catch (Exception e) {
            con.rollback();
            con.setAutoCommit(true);
            throw e;
        } finally {
            con.setAutoCommit(true);
        }
    }

    public void alterarNotasParaExcluidas(String notas) throws Exception {
        try {
            con.setAutoCommit(false);

            StringBuilder sql = new StringBuilder();
            sql.append("UPDATE RPS SET EXCLUIDO = 1 WHERE ID_Lote IN (").append(notas).append(");");

            PreparedStatement sqlExcluir = con.prepareStatement(sql.toString());
            sqlExcluir.execute();
            con.commit();
        } catch (Exception e) {
            con.rollback();
            con.setAutoCommit(true);
            throw e;
        } finally {
            con.setAutoCommit(true);
        }
    }

    public void inserirLogNotasExcluidas(String notas) throws Exception {
        try {
            con.setAutoCommit(false);

            StringBuilder sql = new StringBuilder();
            sql.append("insert into LogExclusaoNotas(DataHora,descricao) values (GETDATE(),'").append(notas).append("');");

            PreparedStatement sqlLog = con.prepareStatement(sql.toString());
            sqlLog.execute();
            con.commit();
        } catch (Exception e) {
            con.rollback();
            con.setAutoCommit(true);
            throw e;
        } finally {
            con.setAutoCommit(true);
        }
    }

    public Integer existeNotaDiferenteDeNaoAutorizada(String listaLote) throws Exception {
        String sql = "select count(*) as sql from rps where status <> 'Não autorizado' and id_lote in ("+listaLote+")";
        PreparedStatement sqlConsulta = con.prepareStatement(sql);
        ResultSet tabelaResultado = sqlConsulta.executeQuery();
        if (tabelaResultado.next()) {
            return tabelaResultado.getInt("sql");
        }
        return 0;
    }

    public List<NotaFiscalConsumidorNFCeVO> listarNFCe(Integer empresaVO, FiltroNFeTO filtroTO, String ordenador) throws Exception {
        List<NotaFiscalConsumidorNFCeVO> notas = new ArrayList<NotaFiscalConsumidorNFCeVO>();

        StringBuilder sql = getSQLBasicoNFCe();
        sql.append("AND n.Id_Empresa = ").append(empresaVO).append(" ");
        sql.append(filtroTO.obtenhaCondicoesNFCe());
        sql.append(" ORDER BY n.id_nfce desc");

        PreparedStatement sqlConsulta = con.prepareStatement(sql.toString());
        ResultSet tabelaResultado = sqlConsulta.executeQuery();

        Date d1 = negocio.comuns.utilitarias.Calendario.hoje();
        while (tabelaResultado.next()) {
            NotaFiscalConsumidorNFCeVO nota = montarDadosNFCe(tabelaResultado, Uteis.NIVELMONTARDADOS_DADOSBASICOS, this.con);
            if (!UteisValidacao.emptyString(filtroTO.getStatus())) {
                if (nota.getStatus().equals(filtroTO.getStatus())) {
                    notas.add(nota);
                }
            } else {
                notas.add(nota);
            }
        }
        Date d2 = negocio.comuns.utilitarias.Calendario.hoje();
        System.out.println("Tempo montar dados NFC-e: " + (d2.getTime() - d1.getTime()));
        return notas;
    }

    public List<ItemNFCeVO> consultarItensNFCe(Integer id_nfce, int nivelMontarDados, Connection con) throws Exception {
        List<ItemNFCeVO> retorno = new ArrayList<ItemNFCeVO>();
        String sql = "SELECT * FROM itemnfce WHERE id_nfce = " + id_nfce;
        Statement stm = con.createStatement();
        ResultSet tabelaResultado = stm.executeQuery(sql);
        while (tabelaResultado.next()) {
            ItemNFCeVO novoObj = montarDadosItemNFCe(tabelaResultado, nivelMontarDados, con);
            retorno.add(novoObj);
        }
        return retorno;
    }

    public List<FormaPagamentoNFCeVO> consultarFormasPagamentoNFCe(Integer id_nfce, int nivelMontarDados, Connection con) throws Exception {
        List<FormaPagamentoNFCeVO> retorno = new ArrayList<FormaPagamentoNFCeVO>();
        String sql = "select\n" +
                "nf.*,\n" +
                "fp.Nome as formapagamento\n" +
                "from NFCe_FormaPagamento nf\n" +
                "inner join FormaPagamento fp on nf.Id_FormaPagamento = fp.Id_FormaPagamento\n" +
                "where nf.Id_NFCe =" + id_nfce;
        Statement stm = con.createStatement();
        ResultSet tabelaResultado = stm.executeQuery(sql);
        while (tabelaResultado.next()) {
            FormaPagamentoNFCeVO novoObj = montarDadosFormaPagamentoNFCe(tabelaResultado, nivelMontarDados, con);
            retorno.add(novoObj);
        }
        return retorno;
    }

    private NotaFiscalConsumidorNFCeVO montarDadosNFCe(ResultSet dadosSQL, Integer nivelMontarDados, Connection con) throws Exception {
        NotaFiscalConsumidorNFCeVO nota = new NotaFiscalConsumidorNFCeVO();
        nota.setId_NFCe(dadosSQL.getInt("id_NFCe"));
        nota.setNumeroEnvio(dadosSQL.getString("numeroEnvio"));
        nota.getEmpresaNFe().setCodigo(dadosSQL.getInt("id_empresa"));
        nota.setDestCPFCNPJ(dadosSQL.getString("destcpfcnpj"));
        nota.setDataHoraEmissao(dadosSQL.getTimestamp("dataHoraEmissao"));
        nota.setDestNome(dadosSQL.getString("destnome"));
        nota.setDestFone(dadosSQL.getString("destfone"));
        nota.setDestCEP(dadosSQL.getString("destcep"));
        nota.setDestLogradouro(dadosSQL.getString("destlogradouro"));
        nota.setDestNumero(dadosSQL.getString("destnumero"));
        nota.setDestComplemento(dadosSQL.getString("destcomplemento"));
        nota.setDestBairro(dadosSQL.getString("destbairro"));
        nota.getDestCidade().setId_Municipio(dadosSQL.getInt("destid_cidade"));
        nota.getDestCidade().setUf(dadosSQL.getString("ufcidade"));
        nota.setCnae(dadosSQL.getString("cnae"));
        nota.setValorTotal(dadosSQL.getDouble("valorTotal"));
        nota.setEnvioFinalizado(dadosSQL.getBoolean("envioFinalizado"));
        nota.setResultadoEnvio(dadosSQL.getString("resultadoEnvio"));
        nota.setChave(dadosSQL.getString("chave"));
        nota.setNotaCancelando(dadosSQL.getBoolean("notaCancelando"));
        nota.setNotaCancelada(dadosSQL.getBoolean("notaCancelada"));
        nota.setIdReenvio(dadosSQL.getInt("idReenvio"));
        Integer nfceInutilizado = dadosSQL.getInt("nfceInutilizado");
        if (UteisValidacao.emptyNumber(nfceInutilizado)) {
            nota.setInutilizado(false);
        } else {
            nota.setInutilizado(true);
        }
        nota.setQtdTentativaInutilizado(dadosSQL.getInt("qtdTentativaInutilizado"));


        if (nota.isNotaCancelando() || nota.isNotaCancelada()) {

            if (nota.isNotaCancelada()) {
                nota.setStatus(StatusNFCeEnum.CANCELADO.getDescricao());
            } else {
                nota.setStatus(StatusNFCeEnum.CANCELANDO.getDescricao());
            }

        } else if (!UteisValidacao.emptyString(nota.getChave())) {

            nota.setStatus(StatusNFCeEnum.AUTORIZADO.getDescricao());

        } else if (nota.isEnvioFinalizado() && UteisValidacao.emptyString(nota.getChave())) {

            nota.setStatus(StatusNFCeEnum.NAO_AUTORIZADO.getDescricao());
            nota.setMotivo(nota.getResultadoEnvio());

        } else if (!nota.isEnvioFinalizado()) {

            nota.setStatus(StatusNFCeEnum.ENVIANDO.getDescricao());
        }

        if (nota.getStatus().equals(StatusNFCeEnum.NAO_AUTORIZADO.getDescricao()) && !UteisValidacao.emptyNumber(nota.getIdReenvio())) {
            nota.setStatus(StatusNFCeEnum.REENVIADO.getDescricao());
        }

        if (nivelMontarDados == Uteis.NIVELMONTARDADOS_DADOSBASICOS) {
            return nota;
        }

        nota.setItensNFCe(consultarItensNFCe(nota.getId_NFCe(), nivelMontarDados, con));
        nota.setFormasPagamentoNFCe(consultarFormasPagamentoNFCe(nota.getId_NFCe(), nivelMontarDados, con));
        nota.setDestCidade(getFacade().getMunicipioNFe().consultarPorChavePrimaria(nota.getDestCidade().getId_Municipio()));

        return nota;
    }

    private ItemNFCeVO montarDadosItemNFCe(ResultSet dadosSQL, Integer nivelMontarDados, Connection con) throws Exception {
        ItemNFCeVO item = new ItemNFCeVO();
        item.setId_ItemNFCe(dadosSQL.getInt("id_itemNFCe"));
        item.getNotaFiscalConsumidorNFCeVO().setId_NFCe(dadosSQL.getInt("id_NFCe"));
        item.setValorUnitario(dadosSQL.getDouble("valorunitario"));
        item.setDescUnidade(dadosSQL.getString("descunidade"));
        item.setQuantidade(dadosSQL.getInt("quantidade"));
        item.setDescricao(dadosSQL.getString("descricao"));
        item.setNcm(dadosSQL.getString("ncm"));
        item.setCfop(dadosSQL.getString("cfop"));
        item.setBaseCalculoICMS(dadosSQL.getDouble("basecalculoicms"));
        item.setAliquotaICMS(dadosSQL.getDouble("AliquotaICMS"));
        item.setValorICMS(dadosSQL.getDouble("ValorICMS"));
        item.setBaseCalculoPIS(dadosSQL.getDouble("BaseCalculoPIS"));
        item.setAliquotaPIS(dadosSQL.getDouble("AliquotaPIS"));
        item.setValorPIS(dadosSQL.getDouble("valorPIS"));
        item.setBaseCalculoCOFINS(dadosSQL.getDouble("BaseCalculoCOFINS"));
        item.setAliquotaCOFINS(dadosSQL.getDouble("AliquotaCOFINS"));
        item.setValorCOFINS(dadosSQL.getDouble("ValorCOFINS"));
        item.setBaseCalculoISSQN(dadosSQL.getDouble("BaseCalculoISSQN"));
        item.setAliquotaISSQN(dadosSQL.getDouble("AliquotaISSQN"));
        item.setValorISSQN(dadosSQL.getDouble("ValorISSQN"));
        item.setCodigoMunicipioISSQN(dadosSQL.getString("CodigoMunicipioISSQN"));
        item.setCodigoListaServicoISSQN(dadosSQL.getString("CodigoListaServicoISSQN"));
        item.setInformacaoAdicional(dadosSQL.getString("InformacaoAdicional"));
        return item;
    }

    private FormaPagamentoNFCeVO montarDadosFormaPagamentoNFCe(ResultSet dadosSQL, Integer nivelMontarDados, Connection con) throws Exception {
        FormaPagamentoNFCeVO forma = new FormaPagamentoNFCeVO();
        forma.setId_NFCe_FormaPagamento(dadosSQL.getInt("id_nfce_formapagamento"));
        forma.getNotaFiscalConsumidorNFCeVO().setId_NFCe(dadosSQL.getInt("id_nfce"));
        forma.setFormaPagamento(dadosSQL.getString("formaPagamento"));
        forma.setValor(dadosSQL.getDouble("valor"));
        return forma;
    }

    public NotaFiscalConsumidorNFCeVO consultarPorChavePrimariaNFCe(Integer codigo, Integer nivelMontarDados) throws Exception {

        StringBuilder sql = getSQLBasicoNFCe();
        sql.append("AND n.Id_NFCe = ").append(codigo);

        PreparedStatement sqlConsulta = con.prepareStatement(sql.toString());
        ResultSet tabelaResultado = sqlConsulta.executeQuery();
        if (!tabelaResultado.next()) {
            throw new ConsistirException("Dados Não Encontrados ( NFC-e ).");
        }
        return montarDadosNFCe(tabelaResultado, nivelMontarDados, this.con);
    }

    private StringBuilder getSQLBasicoNFCe() {
        StringBuilder sql = new StringBuilder();
        sql.append("SELECT \n");
        sql.append("n.*, \n");
        sql.append("c.uf as ufcidade, \n");
        sql.append("(select sum((valorunitario * quantidade)) from ItemNFCe where Id_NFCe = n.Id_NFCe) as valorTotal, \n");
        sql.append("(select finalizado from OperacaoNFCe where operacao = 'Envio' and Id_NFCe = n.Id_NFCe) as envioFinalizado, \n");
        sql.append("(select descricao from OperacaoNFCe where operacao = 'Envio' and Id_NFCe = n.Id_NFCe) as resultadoEnvio, \n");
        sql.append("(select count(id_NumerosInutilizar) from NumerosInutilizar where Id_Empresa = n.id_empresa and n.numeroenvio between numinicial and NumFinal and Finalizado = 1 and retorno = '') as nfceInutilizado, \n");
        sql.append("(select count(id_NumerosInutilizar) from NumerosInutilizar where Id_Empresa = n.id_empresa and n.numeroenvio between numinicial and NumFinal) as qtdTentativaInutilizado, \n");
        sql.append("r.chave, \n");
        sql.append("(select top 1 finalizado from OperacaoNFCe where operacao = 'Cancelamento' and Id_NFCe = n.Id_NFCe order by Id_OperacaoNFCe desc) as notaCancelando, \n");
        sql.append("(select top 1 finalizado from OperacaoNFCe where operacao = 'Cancelamento' and Id_NFCe = n.Id_NFCe and finalizado = 1 order by Id_OperacaoNFCe desc) as notaCancelada  \n");
        sql.append("FROM NFCE n \n");
        sql.append("LEFT JOIN cidade c on c.id_cidade = n.destid_cidade \n");
        sql.append("LEFT JOIN NFCeRetorno r on r.Id_NFCe = n.Id_NFCe \n");
        sql.append("WHERE 1 = 1 \n");
        return sql;
    }

    public boolean empresaUsaXMLPrefeitura(Integer idRPS, Integer idEmpresa) throws Exception {
        StringBuilder sql = new StringBuilder();
        sql.append("select \n");
        sql.append("top 1 e.usarXMLPrefeitura \n");
        sql.append("from rps r \n");
        sql.append("inner join lote l on l.id_lote = r.id_lote \n");
        sql.append("inner join empresa e on e.id_empresa = l.id_empresa \n");
        sql.append("where  1 = 1 \n");
        if (!UteisValidacao.emptyNumber(idRPS)) {
            sql.append("and r.id_rps = ").append(idRPS).append(" \n");
        }
        if (!UteisValidacao.emptyNumber(idEmpresa)) {
            sql.append("and e.id_empresa = ").append(idEmpresa).append(" \n");
        }
        PreparedStatement sqlConsulta = con.prepareStatement(sql.toString());
        ResultSet tabelaResultado = sqlConsulta.executeQuery();
        if (tabelaResultado.next()) {
            return tabelaResultado.getBoolean("usarXMLPrefeitura");
        } else {
            return false;
        }
    }

    public boolean empresaUsaXMLManipulado(Integer idRPS, Integer idEmpresa) throws Exception {
        StringBuilder sql = new StringBuilder();
        sql.append("select \n");
        sql.append("top 1 e.usarXMLManipulado \n");
        sql.append("from rps r \n");
        sql.append("inner join lote l on l.id_lote = r.id_lote \n");
        sql.append("inner join empresa e on e.id_empresa = l.id_empresa \n");
        sql.append("where  1 = 1 \n");
        if (!UteisValidacao.emptyNumber(idRPS)) {
            sql.append("and r.id_rps = ").append(idRPS).append(" \n");
        }
        if (!UteisValidacao.emptyNumber(idEmpresa)) {
            sql.append("and e.id_empresa = ").append(idEmpresa).append(" \n");
        }
        PreparedStatement sqlConsulta = con.prepareStatement(sql.toString());
        ResultSet tabelaResultado = sqlConsulta.executeQuery();
        if (tabelaResultado.next()) {
            return tabelaResultado.getBoolean("usarXMLManipulado");
        } else {
            return false;
        }
    }

    public boolean empresaUsaXMLManipuladoNFCe(Integer idRPS, Integer idEmpresa) throws Exception {
        StringBuilder sql = new StringBuilder();
        sql.append("select top 1 e.usarXMLManipuladoNFCe\n");
        sql.append("from NFCe n\n");
        sql.append("       inner join Empresa e ON n.Id_Empresa = e.Id_Empresa\n");
        sql.append("where  1 = 1 \n");
        if (!UteisValidacao.emptyNumber(idRPS)) {
            sql.append("and n.Id_NFCe = ").append(idRPS).append(" \n");
        }
        if (!UteisValidacao.emptyNumber(idEmpresa)) {
            sql.append("and e.id_empresa = ").append(idEmpresa).append(" \n");
        }
        PreparedStatement sqlConsulta = con.prepareStatement(sql.toString());
        ResultSet tabelaResultado = sqlConsulta.executeQuery();
        if (tabelaResultado.next()) {
            return tabelaResultado.getBoolean("usarXMLManipuladoNFCe");
        } else {
            return false;
        }
    }

    public List<Integer> listaNotasGerarPDF() throws Exception {
        List<Integer> notas = new ArrayList<Integer>();

        StringBuilder sql = new StringBuilder();
        sql.append("SELECT top 15 id_rps from rps order by id_rps desc ");
        PreparedStatement sqlConsulta = con.prepareStatement(sql.toString());
        ResultSet tabelaResultado = sqlConsulta.executeQuery();

        Date d1 = negocio.comuns.utilitarias.Calendario.hoje();
        while (tabelaResultado.next()) {
            notas.add(tabelaResultado.getInt("id_rps"));
        }
        Date d2 = negocio.comuns.utilitarias.Calendario.hoje();
        System.out.println("Tempo montar dados Notas: " + (d2.getTime() - d1.getTime()));
        return notas;
    }

    public List<NumerosInutilizarVO> logInutilizarNFCE(NotaFiscalConsumidorNFCeVO obj) throws Exception {
        List<NumerosInutilizarVO> listaLog = new ArrayList<NumerosInutilizarVO>();

        PreparedStatement sqlConsulta = con.prepareStatement("select * from NumerosInutilizar where id_empresa in (select id_empresa from nfce where id_nfce = "+ obj.getId_NFCe() +") and " +Integer.parseInt(obj.getNumeroEnvio())+ " between numinicial and NumFinal");
        ResultSet tabelaResultado = sqlConsulta.executeQuery();
        while (tabelaResultado.next()) {
            NumerosInutilizarVO log = new NumerosInutilizarVO();
            log.setId_NumerosInutilizar(tabelaResultado.getInt("Id_NumerosInutilizar"));
            log.setId_Empresa(tabelaResultado.getInt("Id_Empresa"));
            log.setSerie(tabelaResultado.getInt("serie"));
            log.setNumInicial(tabelaResultado.getInt("numInicial"));
            log.setNumFinal(tabelaResultado.getInt("numFinal"));
            log.setRetorno(tabelaResultado.getString("retorno"));
            log.setFinalizado(tabelaResultado.getBoolean("finalizado"));
            listaLog.add(log);
        }
        return listaLog;
    }

    public byte[] obtenhaXMLInutilizarNFCE(NotaFiscalConsumidorNFCeVO obj) throws Exception {

        Integer id_NumerosInutilizar = 0;
        PreparedStatement sqlConsulta = con.prepareStatement("select * from NumerosInutilizar where id_empresa in (select id_empresa from nfce where id_nfce = "+ obj.getId_NFCe()+") and " + obj.getNumeroEnvio() + " between numinicial and NumFinal and finalizado = 1 and retorno = ''");
        ResultSet tabelaResultado = sqlConsulta.executeQuery();
        while (tabelaResultado.next()) {
            id_NumerosInutilizar = tabelaResultado.getInt("Id_NumerosInutilizar");
        }


        byte[] xml = new byte[0];

        String chave = "pactonfse/XMLIN";

        if (!UteisValidacao.emptyNumber(id_NumerosInutilizar)) {
            xml = MidiaService.getInstancePdf().downloadObjectAsByteArray(chave,
                    MidiaEntidadeEnum.XML_NFE, id_NumerosInutilizar.toString(), null);
        }
        return xml;
    }

    private byte[] obtenhaXMLManipulado(String caminho, Integer codigoReferencia, Date dataReferencia) throws Exception {
        byte[] byteOutro = getFacade().getNotaNFSe().obtenhaArquivoNota("xml", codigoReferencia, dataReferencia, false, true, false, "", false, false);
        byte[] byteNosso = getFacade().getNotaNFSe().obtenhaArquivoNota("xml", codigoReferencia, dataReferencia, false, false, false , "", false, false);

        String pathOutro = caminho + File.separator + codigoReferencia + "-Outro.xml";
        String pathNosso = caminho + File.separator + codigoReferencia + "-Nosso.xml";

        FileUtils.writeByteArrayToFile(new File(pathOutro), byteOutro);
        FileUtils.writeByteArrayToFile(new File(pathNosso), byteNosso);

        File arqOutro = new File(pathOutro);
        StringBuilder textoOutro = FileUtilities.readContentFile(arqOutro.getAbsolutePath());
        String textoAdicionar = textoOutro.subSequence(textoOutro.indexOf("<protNFe"), textoOutro.indexOf("</protNFe>")+10).toString();


        File arq = new File(pathNosso);
        StringBuilder texto = FileUtilities.readContentFile(arq.getAbsolutePath());

        texto.subSequence(texto.indexOf("<idLote>"), texto.indexOf("</idLote>")+10);

        texto.delete(texto.indexOf("<idLote>"), texto.indexOf("</idLote>")+9);
        texto.delete(texto.indexOf("<indSinc>"), texto.indexOf("</indSinc>")+10);
        String textoString =  texto.toString();
        textoString =  textoString.replaceAll("<cEAN/>", "<cEAN />");
        textoString =  textoString.replaceAll("<cEANTrib/>", "<cEANTrib />");
        textoString =  textoString.replaceAll("enviNFe", "nfeProc");
        textoString =  textoString.replaceAll("</NFe>", "");
        textoString =  textoString.replaceAll("</nfeProc>", "");

        StringBuilder salvar = new StringBuilder(textoString);
        salvar.append("</NFe>");
        salvar.append(textoAdicionar);
        salvar.append("</nfeProc>");

        String pathFinal = caminho + File.separator + codigoReferencia + "-Final.xml";
        StringUtilities.saveToFile(salvar, pathFinal);

        FileInputStream fileInputStream = null;
        byte[] bytesArray = null;
        File file = new File(pathFinal);
        bytesArray = new byte[(int) file.length()];
        //read file into bytes[]
        fileInputStream = new FileInputStream(file);
        fileInputStream.read(bytesArray);
        return bytesArray;

    }

    public List<HashMap<String, String>> consultarNFSePorRPS(String listaRps, String chaveEmpresa) throws Exception {

        if (UteisValidacao.emptyString(listaRps)) {
            return new ArrayList<>();
        }

        String sql = "SELECT r.IdReferencia, r.Id_Lote FROM RPS r " +
                "       INNER JOIN Lote l ON r.Id_Lote = l.Id_lote " +
                "       INNER JOIN empresa e ON l.id_empresa = e.id_empresa " +
                "where e.Chave = '" + chaveEmpresa + "' " +
                " and r.IdReferencia IN (" + listaRps + ") ";

        Statement sqlConsulta = con.createStatement();
        ResultSet tabelaResultado = sqlConsulta.executeQuery(sql.toString());

        Date d1 = negocio.comuns.utilitarias.Calendario.hoje();
        List<HashMap<String, String>> notas = new ArrayList<>();
        HashMap<String, String> nota = new HashMap<>();
        while (tabelaResultado.next()) {
            nota = new HashMap<>();
            nota.put("idLote", tabelaResultado.getString(2));
            nota.put("idReferencia", tabelaResultado.getString(1));
            notas.add(nota);
        }
        Date d2 = negocio.comuns.utilitarias.Calendario.hoje();
        System.out.println("Tempo montar dados consultarNotas: " + (d2.getTime() - d1.getTime()));
        return notas;
    }
    public List<NotaFiscalDeServicoVO> consultarNFSe(String listaLote) throws Exception {
        List<NotaFiscalDeServicoVO> notas = new ArrayList<NotaFiscalDeServicoVO>();
        if (UteisValidacao.emptyString(listaLote)) {
            return notas;
        }

        StringBuilder sql = getSQLBase();
        sql.append("AND r.id_lote in (").append(listaLote).append(") \n");
        sql.append("ORDER BY r.Id_RPS DESC");

        Statement sqlConsulta = con.createStatement();
        ResultSet tabelaResultado = sqlConsulta.executeQuery(sql.toString());

        Date d1 = negocio.comuns.utilitarias.Calendario.hoje();
        while (tabelaResultado.next()) {
            NotaFiscalDeServicoVO nota = montarDados(tabelaResultado, Uteis.NIVELMONTARDADOS_DADOSBASICOS, this.con);
            notas.add(nota);
        }
        Date d2 = negocio.comuns.utilitarias.Calendario.hoje();
        System.out.println("Tempo montar dados consultarNotas: " + (d2.getTime() - d1.getTime()));
        return notas;
    }

    public List<NotaFiscalConsumidorNFCeVO> consultarNFCe(String listaNFCe) throws Exception {
        List<NotaFiscalConsumidorNFCeVO> notas = new ArrayList<NotaFiscalConsumidorNFCeVO>();
        if (UteisValidacao.emptyString(listaNFCe)) {
            return notas;
        }

        StringBuilder sql = getSQLBasicoNFCe();
        sql.append(" AND n.id_nfce in (").append(listaNFCe).append(") ");
        sql.append(" ORDER BY n.id_nfce desc");

        PreparedStatement sqlConsulta = con.prepareStatement(sql.toString());
        ResultSet tabelaResultado = sqlConsulta.executeQuery();

        Date d1 = negocio.comuns.utilitarias.Calendario.hoje();
        while (tabelaResultado.next()) {
            NotaFiscalConsumidorNFCeVO nota = montarDadosNFCe(tabelaResultado, Uteis.NIVELMONTARDADOS_DADOSBASICOS, this.con);
            notas.add(nota);
        }
        Date d2 = negocio.comuns.utilitarias.Calendario.hoje();
        System.out.println("Tempo montar dados consultarNFCe: " + (d2.getTime() - d1.getTime()));
        return notas;
    }


    private List<NFSeImprimirTO> consultarNFSeParaGeracaoPDF(String codigosId_RPS) throws Exception {
        StringBuilder sql = new StringBuilder();

        sql.append("select  \n");
        sql.append("e.id_empresa, \n");
        sql.append("e.logomarca, \n");
        sql.append("e.id_cidade as cidadeEmpresa, \n");
        sql.append("r.AliquotaRetorno, \n");
        sql.append("dbo.DecodeUTF8String(r.descricao) as descricao, \n");
        sql.append("d.* \n");
        sql.append("from DadosImpressaoNFSe d \n");
        sql.append("inner join empresa e on e.id_empresa = d.id_empresa \n");
        sql.append("inner join rps r on r.id_rps = d.id_rps \n");
        sql.append("where d.id_rps in (").append(codigosId_RPS).append(")");

        Statement st = con.createStatement();
        ResultSet rs = st.executeQuery(sql.toString());
        List<NFSeImprimirTO> retorno = new ArrayList<NFSeImprimirTO>();
        while (rs.next()) {
            NFSeImprimirTO obj = new NFSeImprimirTO();

            Integer empresa = rs.getInt("id_empresa");

            obj.setId_RPS(rs.getString("id_rps"));
            obj.setIdEmpresaNFSe(empresa);
            obj.setId_RPS(rs.getString("id_rps"));
            obj.setRazaoSocialPrestador(rs.getString("PrestadorServicoRazaoSocial"));
            obj.setCnpjPrestador(rs.getString("PrestadorCnpj"));
            obj.setInscricaoMunicipalPrestador(rs.getString("PrestadorInscricaoMunicipal"));
            obj.setInscricaoEstadualPrestador(rs.getString("PrestadorInscricaoEstadual"));

            String logradouroPrestador = rs.getString("PrestadorServicoEnderecoTipoLogradouro");
            String enderecoPrestador = rs.getString("PrestadorServicoEnderecoEndereco");
            String numeroPrestador = rs.getString("PrestadorServicoEnderecoNumero");
            String bairroPrestador = rs.getString("PrestadorServicoEnderecoBairro");
            String cepPrestador = rs.getString("PrestadorServicoEnderecoCEP");

            String enderecoPrestadorSalvar = enderecoPrestador;
            if (!UteisValidacao.emptyString(numeroPrestador)) {
                enderecoPrestadorSalvar += ", " + numeroPrestador;
            }
            if (!UteisValidacao.emptyString(bairroPrestador)) {
                enderecoPrestadorSalvar += " - " + bairroPrestador;
            }
            if (!UteisValidacao.emptyString(cepPrestador)) {
                enderecoPrestadorSalvar += " - CEP: " + cepPrestador;
            }

            obj.setEnderecoPrestador(enderecoPrestadorSalvar);
            obj.setComplementoPrestador(rs.getString("PrestadorServicoEnderecoComplemento"));

            obj.setMunicipioPrestador(rs.getString("PrestadorServicoEnderecoxMunicipio"));
            obj.setUfPrestador(rs.getString("PrestadorServicoEnderecoUF"));
            obj.setTelefonePrestador(rs.getString("PrestadorServicoContatoTelefone"));
            obj.setEmailPrestador(rs.getString("PrestadorServicoContatoEmail"));
            obj.setLogomarcaPrestadorByte(rs.getBytes("logomarca"));

            obj.setRazaoSocialTomador(rs.getString("TomadorRazaoSocial"));
            obj.setCnpjTomador(rs.getString("TomadorIdentificacaoTomadorCpfCnpj"));
            obj.setInscricaoMunicipalTomador(rs.getString("TomadorIdentificacaoTomadorInscricaoMunicipal"));
//            obj.setInscricaoEstadualTomador(rs.getString("inscricaoEstadualTomador"));

            String tipoLogradouroTomador = rs.getString("TomadorEnderecoTipoLogradouro");
            String enderecoTomador = rs.getString("TomadorEnderecoEndereco");
            String numeroTomador = rs.getString("TomadorEnderecoNumero");
            String bairroTomador = rs.getString("TomadorEnderecoBairro");
            String cepTomador = rs.getString("TomadorEnderecoCEP");

            String enderecoTomadorSalvar = enderecoTomador;
            if (!UteisValidacao.emptyString(numeroTomador)) {
                enderecoTomadorSalvar += ", " + numeroTomador;
            }
            if (!UteisValidacao.emptyString(bairroTomador)) {
                enderecoTomadorSalvar += " - " + bairroTomador;
            }
            if (!UteisValidacao.emptyString(cepTomador)) {
                enderecoTomadorSalvar += " - CEP: " + cepTomador;
            }

            obj.setEnderecoTomador(enderecoTomadorSalvar);
            obj.setComplementoTomador(rs.getString("TomadorEnderecoComplemento"));
            obj.setMunicipioTomador(rs.getString("TomadorEnderecoxMunicipio"));
            obj.setUfTomador(rs.getString("TomadorEnderecoUF"));
            obj.setTelefoneTomador(rs.getString("TomadorContatoTelefone"));
            obj.setEmailTomador(rs.getString("TomadorContatoEmail"));
            obj.setCidadePrestacao(rs.getString("PrestadorServicoEnderecoxMunicipio"));
            obj.setNumeroNota(rs.getString("Numero"));
            Date dataServico = rs.getDate("DataEmissao");
            obj.setDataServico(Uteis.getData(dataServico));
            Date dataHoraEmissao = rs.getTimestamp("DataProcessamento");
            obj.setDataHoraEmissao(Uteis.getDataAplicandoFormatacao(dataHoraEmissao, "dd/MM/yyyy HH:mm:ss"));
            obj.setCodigoAutorizacao(rs.getString("CodigoVerificacao"));
            obj.setDataCompetencia(rs.getString("Competencia"));
            obj.setNumeroRPS(rs.getString("IdentificacaoRpsNumero"));
            obj.setMunicipioPrestacao(rs.getString("PrestadorServicoEnderecoxMunicipio"));
            final String observacao = rs.getString("descricao");
            obj.setServicosDescricao(StringUtils.isNotBlank(observacao) ? observacao.replaceAll("\\\\n", "") : "");
            obj.setCodigoServico(rs.getString("ServicoItemListaServico"));

            obj.setValorPIS(Formatador.formatarValorMonetario(rs.getDouble("ServicoValoresValorPis")));
            obj.setValorCOFINS(Formatador.formatarValorMonetario(rs.getDouble("ServicoValoresValorCofins")));
            obj.setValorIR(Formatador.formatarValorMonetario(rs.getDouble("ServicoValoresValorIr")));
            obj.setValorINSS(Formatador.formatarValorMonetario(rs.getDouble("ServicoValoresValorInss")));
            obj.setValorCSLL(Formatador.formatarValorMonetario(rs.getDouble("ServicoValoresValorCsll")));
            obj.setValorServicos(Formatador.formatarValorMonetario(rs.getDouble("servicovaloresvalorservicos")));
            obj.setDescontoIncondicionado(Formatador.formatarValorMonetario(rs.getDouble("ServicoValoresDescontoIncondicionado")));
            obj.setDescontoCondicionado(Formatador.formatarValorMonetario(rs.getDouble("ServicoValoresDescontoCondicionado")));
            obj.setOutrasRetencoes(Formatador.formatarValorMonetario(rs.getDouble("ServicoValoresOutrasRetencoes")));
            obj.setIssRetidoValor(Formatador.formatarValorMonetario(rs.getDouble("ServicoValoresValorIssRetido")));
            obj.setValorLiquido(Formatador.formatarValorMonetario(rs.getDouble("ServicoValoresValorLiquidoNfse")));
            obj.setBaseCalculo(Formatador.formatarValorMonetario(rs.getDouble("ServicoValoresBaseCalculo")));

            Double servicoValoresValorInss = rs.getDouble("ServicoValoresValorInss");
            Double servicoValoresValorpis = rs.getDouble("ServicoValoresValorpis");
            Double servicoValoresValorCsll = rs.getDouble("ServicoValoresValorCsll");
            Double servicoValoresValorCofins = rs.getDouble("ServicoValoresValorCofins");
            Double servicoValoresValorIr = rs.getDouble("ServicoValoresValorIr");

            obj.setRetencoesFederais(Formatador.formatarValorMonetario(servicoValoresValorInss + servicoValoresValorpis + servicoValoresValorCsll + servicoValoresValorCofins + servicoValoresValorIr));
            obj.setDeducoesPermitidas(Formatador.formatarValorMonetario(rs.getDouble("ServicoValoresValorDeducoes")));

            obj.setNaturezaOperacao("");
            String naturezaOperacaoString = rs.getString("NaturezaOperacao");
            if (!UteisValidacao.emptyString(naturezaOperacaoString)) {
                try {
                    NaturezaOperacaoEnum natOperaEnum = NaturezaOperacaoEnum.getTipo(Integer.parseInt(naturezaOperacaoString));
                    if (natOperaEnum != null) {
                        obj.setNaturezaOperacao(natOperaEnum.getDescricao());
                    }
                } catch (Exception ignored) {

                }
            }

            CRTEnum regimeEnum = CRTEnum.getTipo(rs.getInt("RegimeEspecialTributacao"));
            if (regimeEnum != null) {
                obj.setRegimeEspecial(regimeEnum.getDescricao());
            } else {
                obj.setRegimeEspecial("");
            }

            boolean optanteSimplesNacional = rs.getBoolean("OptanteSimplesNacional");
            obj.setSimplesNacional(optanteSimplesNacional ? "SIM" : "NÃO");

            boolean incentivadorCultural = rs.getBoolean("IncentivadorCultural");
            obj.setIncentivadorCultural(incentivadorCultural ? "SIM" : "NÃO");

            Double aliquota = rs.getDouble("ServicoValoresAliquota");

            Integer idCidadeEmpresa = rs.getInt("cidadeEmpresa");
            if (idCidadeEmpresa.equals(5)) { //Goiânia
                aliquota = rs.getDouble("AliquotaRetorno");
            }

            Double aliquotaMult = (aliquota* 100);
            if (aliquotaMult < 100) {
                aliquota = aliquotaMult;
            }

            obj.setAliquota(Formatador.formatarValorMonetarioSemMoeda(aliquota));

            boolean reterISS = rs.getBoolean("ServicoValoresIssRetido");
            obj.setReterISS(reterISS ? "SIM" : "NÃO");

            obj.setValorISS(Formatador.formatarValorMonetario(rs.getDouble("ServicoValoresValorIssRetido")));
            obj.setValorTotalNota(Formatador.formatarValorMonetario(rs.getDouble("servicovaloresvalorservicos")));
            obj.setOutrasInformacoes(rs.getString("OutrasInformacoes"));

            if (empresa.equals(44)) {// PACTO SOLUCOES
                Double baseCalculo = rs.getDouble("ServicoValoresBaseCalculo");
                obj.setValorISS(Formatador.formatarValorMonetario(Uteis.arredondarForcando2CasasDecimaisMantendoSinal((baseCalculo * aliquota) / 100)));
            }

            retorno.add(obj);
        }
        return retorno;
    }

    public JSONObject consultarDash(String chave) throws SQLException, JSONException {
        JSONObject jsonObject = new JSONObject();
        StringBuilder sql = new StringBuilder();
        sql.append("select \n");
        sql.append("(\n");
        sql.append("select COUNT(distinct(o.id_rps)) \n");
        sql.append("from operacaorps o\n");
        sql.append("inner join rps r on r.Id_RPS = o.Id_RPS\n");
        sql.append("inner join lote l on l.Id_Lote = r.Id_Lote\n");
        sql.append("inner join empresa e on e.id_empresa = l.id_empresa\n");
        sql.append("where 1 = 1\n");
        sql.append("and o.datahora >= CONVERT(DATETIME, FLOOR(CONVERT(FLOAT(24), GETDATE()))) \n");
        sql.append("and o.operacao = 'Gravação'\n");
        if (!UteisValidacao.emptyString(chave)) {
            sql.append("and e.Chave = '").append(chave).append("' \n");
        }
        sql.append(") as notasHoje, \n");
        sql.append("(\n");
        sql.append("select \n");
        sql.append("COUNT(id_rps) \n");
        sql.append("from rps r\n");
        sql.append("inner join lote l on l.Id_Lote = r.Id_Lote\n");
        sql.append("inner join empresa e on e.id_empresa = l.id_empresa\n");
        sql.append("where 1 = 1\n");
        if (!UteisValidacao.emptyString(chave)) {
            sql.append("and e.Chave = '").append(chave).append("' \n");
        }
        sql.append("and r.status = 'Enviando'\n");
        sql.append(") as notasEnviando, \n");
        sql.append("(\n");
        sql.append("select \n");
        sql.append("COUNT(o.Id_RPS) \n");
        sql.append("from operacaorps o\n");
        sql.append("inner join rps r on r.Id_RPS = o.Id_RPS\n");
        sql.append("inner join lote l on l.Id_Lote = r.Id_Lote\n");
        sql.append("inner join empresa e on e.id_empresa = l.id_empresa\n");
        sql.append("where 1 = 1\n");
        sql.append("and o.operacao = 'Envio e-mail' \n");
        sql.append("and o.finalizado = 0\n");
        if (!UteisValidacao.emptyString(chave)) {
            sql.append("and e.Chave = '").append(chave).append("' \n");
        }
        sql.append("\n");
        sql.append(") as emailPendente, \n");
        sql.append("(\n");
        sql.append("select \n");
        sql.append("FORMAT(max(datahora), 'dd/MM/yyyy HH:mm:ss') \n");
        sql.append("from operacaorps o\n");
        sql.append("inner join rps r on r.Id_RPS = o.Id_RPS\n");
        sql.append("inner join lote l on l.Id_Lote = r.Id_Lote\n");
        sql.append("inner join empresa e on e.id_empresa = l.id_empresa\n");
        sql.append("where 1 =1 \n");
        sql.append("and o.operacao = 'Gravação'\n");
        if (!UteisValidacao.emptyString(chave)) {
            sql.append("and e.Chave = '").append(chave).append("' \n");
        }
        sql.append(") as ultimaNota,\n");
        sql.append("( \n");
        sql.append("select  \n");
        sql.append("sum(r.ValorServicos) \n");
        sql.append("from rps r \n");
        sql.append("inner join lote l on l.Id_Lote = r.Id_Lote \n");
        sql.append("inner join empresa e on e.id_empresa = l.id_empresa \n");
        sql.append("where 1 = 1 \n");
        if (!UteisValidacao.emptyString(chave)) {
            sql.append("and e.Chave = '").append(chave).append("' \n");
        }
        sql.append("and r.status = 'Autorizado' \n");
        sql.append("and r.dataemissao >= DATEADD(month, -1, getdate()) \n");
        sql.append("and (FORMAT(r.dataemissao, 'MM/yyyy') = FORMAT(getdate(), 'MM/yyyy')) \n");
        sql.append(") as valorTotal, \n");
        sql.append("( \n");
        sql.append("select  \n");
        sql.append("count(r.id_rps) \n");
        sql.append("from rps r \n");
        sql.append("inner join lote l on l.Id_Lote = r.Id_Lote \n");
        sql.append("inner join empresa e on e.id_empresa = l.id_empresa \n");
        sql.append("where 1 = 1 \n");
        if (!UteisValidacao.emptyString(chave)) {
            sql.append("and e.Chave = '").append(chave).append("' \n");
        }
        sql.append("and r.dataemissao >= DATEADD(month, -1, getdate()) \n");
        sql.append("and (FORMAT(r.dataemissao, 'MM/yyyy') = FORMAT(getdate(), 'MM/yyyy')) \n");
        sql.append(") as notasMes, \n");
        sql.append("(\n");
        sql.append("select \n");
        sql.append("COUNT(id_rps) \n");
        sql.append("from rps r\n");
        sql.append("inner join lote l on l.Id_Lote = r.Id_Lote\n");
        sql.append("inner join empresa e on e.id_empresa = l.id_empresa\n");
        sql.append("where 1 = 1\n");
        if (!UteisValidacao.emptyString(chave)) {
            sql.append("and e.Chave = '").append(chave).append("' \n");
        }
        sql.append("and r.status = 'Autorizado'\n");
        sql.append("and r.dataemissao >= DATEADD(month, -1, getdate()) \n");
        sql.append("and (FORMAT(r.dataemissao, 'MM/yyyy') = FORMAT(getdate(), 'MM/yyyy')) \n");
        sql.append(") as notasAutorizadas \n");

        Statement stm = con.createStatement();
        ResultSet rs = stm.executeQuery(sql.toString());
        if (rs.next()) {
            jsonObject.put("notasHoje", rs.getInt("notasHoje"));
            jsonObject.put("notasEnviando", rs.getInt("notasEnviando"));
            jsonObject.put("emailPendente", rs.getInt("emailPendente"));
            jsonObject.put("ultimaNota", rs.getString("ultimaNota"));
            jsonObject.put("valorTotal", rs.getDouble("valorTotal"));
            jsonObject.put("notasMes", rs.getInt("notasMes"));
            jsonObject.put("notasAutorizadas", rs.getInt("notasAutorizadas"));
        }
        return jsonObject;
    }

    public void inserirLogNotaReenviada(Date dataReenvio, String usuario, Integer id_Referencia, TipoNotaEnum tipoNotaEnum) throws Exception {
        try {
            con.setAutoCommit(false);

            String sql = "insert into LogReenvioNotas(DataHora, DataHoraEmissaoReenvio, Id_Referencia, Usuario, TipoNota) values (?, ?, ?, ?, ?)";

            PreparedStatement sqlLog = con.prepareStatement(sql);
            sqlLog.setTimestamp(1, Uteis.getDataJDBCTimestamp(Calendario.hoje()));
            sqlLog.setTimestamp(2, Uteis.getDataJDBCTimestamp(dataReenvio));
            sqlLog.setInt(3, id_Referencia);
            sqlLog.setString(4, usuario);
            sqlLog.setString(5, tipoNotaEnum.getDescricao());
            sqlLog.execute();
            con.commit();
        } catch (Exception e) {
            con.rollback();
            con.setAutoCommit(true);
            throw e;
        } finally {
            con.setAutoCommit(true);
        }
    }

    public JSONArray consultarGestaoDeNotas(String chave, String mesReferencia) throws Exception {

        Date dataMesReferencia = Uteis.getDate(mesReferencia ,"dd-MM-yyyy");
        dataMesReferencia = Uteis.obterPrimeiroDiaMes(dataMesReferencia);

        StringBuilder sql = new StringBuilder();

        sql.append("select \n");
        sql.append("distinct(r.status), \n");
        sql.append("count(r.id_rps) as qtdNotas, \n");
        sql.append("sum(r.valorservicos) as valor \n");
        sql.append("from rps r \n");
        sql.append("inner join lote l on l.Id_Lote = r.Id_Lote \n");
        sql.append("inner join empresa e on e.id_empresa = l.id_empresa \n");
        sql.append("where 1 = 1 \n");
        sql.append("and e.Chave = '").append(chave).append("' \n");
        sql.append("and r.dataemissao >= '").append(Uteis.getDataAplicandoFormatacao(dataMesReferencia, "yyyy-MM-dd" + " 00:00:00")).append("'\n");
        sql.append("and FORMAT(r.dataemissao, 'MM/yyyy') = '").append(Uteis.getDataAplicandoFormatacao(dataMesReferencia, "MM/yyyy")).append("' \n");
        sql.append("group by r.status \n");
        sql.append("order by r.status \n");

        Statement stm = con.createStatement();
        ResultSet rs = stm.executeQuery(sql.toString());
        JSONArray jsonRetorno = new JSONArray();
        while (rs.next()) {
            JSONObject jsonObject = new JSONObject();
            jsonObject.put("status", rs.getString("status"));
            jsonObject.put("qtdNotas", rs.getInt("qtdNotas"));
            jsonObject.put("valor", rs.getDouble("valor"));
            jsonRetorno.put(jsonObject);
        }
        return jsonRetorno;
    }

    private List<NFeImprimirTO> consultarNFeParaGeracaoPDF(String codigosId_RPS) throws Exception {
        StringBuilder sql = new StringBuilder();

        sql.append("select  \n");
        sql.append("l.Protocolo, \n");
        sql.append("e.id_empresa, \n");
        sql.append("e.logomarca, \n");
        sql.append("e.DDDTelefone, \n");
        sql.append("e.Telefone, \n");
        sql.append("e.impressaoPDFAvista, \n");
        sql.append("r.CodigoVerificacao, \n");
        sql.append("r.numeronota, \n");
        sql.append("d.*,\n");
        sql.append("dbo.retornar_formas_pagamento(r.id_rps) as formas \n");
        sql.append("from DadosImpressaoNFe d \n");
        sql.append("inner join empresa e on e.id_empresa = d.id_empresa \n");
        sql.append("inner join rps r on r.Id_RPS = d.Id_RPS \n");
        sql.append("inner join lote l on l.Id_Lote = r.Id_Lote \n");
        sql.append("where d.id_rps in (").append(codigosId_RPS).append(")");

        Statement st = con.createStatement();
        ResultSet rs = st.executeQuery(sql.toString());
        List<NFeImprimirTO> retorno = new ArrayList<NFeImprimirTO>();
        while (rs.next()) {
            NFeImprimirTO obj = new NFeImprimirTO();
            obj.setId_RPS(rs.getString("id_rps"));
            obj.setId_DadosImpressaoNFe(rs.getInt("Id_DadosImpressaoNFe"));
            obj.setRazaoSocialPrestador(rs.getString("EmitxNome"));
            obj.setEnderecoPrestador(rs.getString("EmitEnderEmitxLgr"));
            obj.setComplementoPrestador(rs.getString("EmitEnderEmitxCpl"));
            obj.setBairroPrestador(rs.getString("EmitEnderEmitxBairro"));
            obj.setMunicipioPrestador(rs.getString("EmitEnderEmitxMun") + " - " + rs.getString("EmitEnderEmitUF"));
            obj.setLogomarcaPrestadorByte(rs.getBytes("logomarca"));

            String cepPrestador = rs.getString("EmitEnderEmitCEP");
            if (cepPrestador.length() == 8) {
                cepPrestador = "CEP: " + Uteis.aplicarMascara(cepPrestador, "99999-999");
            }
            obj.setCepPrestador(cepPrestador);

            obj.setTelefonePrestador(rs.getString("EmitEnderEmitfone"));
            obj.setNumeroNota(rs.getString("numeronota"));
//            String nota =  rs.getString("IdenNF");
//            Uteis.aplicarMascara(nota, "999.999.999");
            obj.setSerieNota(rs.getString("IdeSerie"));
            obj.setChaveAcesso(rs.getString("CodigoVerificacao"));
            obj.setNaturezaOperacao(rs.getString("IdeNatOp"));
            obj.setProtocoloAutorizacao(rs.getString("Protocolo"));
            obj.setCnpjPrestador(Uteis.aplicarMascara(rs.getString("EmitCNPJCPF"), "99.999.999/9999-99"));
            obj.setRazaoSocialTomador(rs.getString("DestxNome"));

            String cnpjCPFTomador = rs.getString("DestCNPJCPF");
            if (cnpjCPFTomador.length() == 14) {
                cnpjCPFTomador = Uteis.aplicarMascara(cnpjCPFTomador, "99.999.999/9999-99");
            } else if (cnpjCPFTomador.length() == 11) {
                cnpjCPFTomador = Uteis.aplicarMascara(cnpjCPFTomador, "999.999.999-99");
            }

            obj.setCnpjTomador(cnpjCPFTomador);
            obj.setEnderecoTomador(rs.getString("DestEnderDestxLgr"));
            obj.setBairroTomador(rs.getString("DestEnderDestxBairro"));

            String cepTomador = rs.getString("DestEnderDestCEP");
            if (cepTomador.length() == 8) {
                cepTomador = Uteis.aplicarMascara(cepTomador, "99999-999");
            }
            obj.setCepTomador(cepTomador);

            obj.setMunicipioTomador(rs.getString("DestEnderDestxMun"));
            obj.setTelefoneTomador(rs.getString("DestEnderDestfone"));
            obj.setUfTomador(rs.getString("DestEnderDestUF"));
            obj.setInscricaoEstadualTomador(rs.getString("DestIE"));

            obj.setDataEmissao(Uteis.getData(rs.getTimestamp("IdedEmi")));
            obj.setDataSaida(Uteis.getData(rs.getTimestamp("IdedEmi")));
            obj.setHoraSaida(Uteis.gethoraHHMMSSFormatado(rs.getTimestamp("IdedEmi")));

            obj.setBaseCalculoICMS(Formatador.formatarValorMonetarioSemMoeda(rs.getDouble("TotalICMSTotvBC")));
            obj.setValorICMS(Formatador.formatarValorMonetarioSemMoeda(rs.getDouble("TotalICMSTotvICMS")));
            obj.setBaseCalculoICMSSubs(Formatador.formatarValorMonetarioSemMoeda(rs.getDouble("TotalICMSTotvICMS")));
            obj.setValorICMSSubs(Formatador.formatarValorMonetarioSemMoeda(rs.getDouble("TotalICMSTotvICMS")));
            obj.setValorTributos(Formatador.formatarValorMonetarioSemMoeda(rs.getDouble("TotalICMSTotvTotTrib")));
            obj.setValorTotalProdutos(Formatador.formatarValorMonetarioSemMoeda(rs.getDouble("TotalICMSTotvICMS")));
            obj.setValorFrete(Formatador.formatarValorMonetarioSemMoeda(0.0));
            obj.setValorSeguro(Formatador.formatarValorMonetarioSemMoeda(0.0));
            obj.setValorDesconto(Formatador.formatarValorMonetarioSemMoeda(0.0));
            obj.setValorOutrasDespesas(Formatador.formatarValorMonetarioSemMoeda(0.0));
            obj.setValorIPI(Formatador.formatarValorMonetarioSemMoeda(0.0));
            obj.setValorIRRF(Formatador.formatarValorMonetarioSemMoeda(0.0));
            obj.setValorTotalNota(Formatador.formatarValorMonetarioSemMoeda(rs.getDouble("TotalICMSTotvNF")));

            obj.setInscricaoEstadualPrestador(rs.getString("EmitIE"));
            obj.setValorTotalServicos(Formatador.formatarValorMonetarioSemMoeda(rs.getDouble("TotalISSQNtotvServ")));
            obj.setValorBaseCalculoISSQN(Formatador.formatarValorMonetarioSemMoeda(rs.getDouble("TotalISSQNTotvBC")));
            obj.setValorISSQN(Formatador.formatarValorMonetarioSemMoeda(rs.getDouble("TotalISSQNtotvISS")));

            obj.setProdutos(consultarProdutosNFeParaGeracaoPDF(obj.getId_DadosImpressaoNFe()));
            obj.setObservacoes(consultarObservacoesNFeParaGeracaoPDF(obj.getId_DadosImpressaoNFe()));
            obj.setFaturas(consultarFaturasNFeParaGeracaoPDF(obj));

            obj.setInfAdicinfAdFisco(rs.getString("infAdicinfAdFisco"));

            boolean impressaoPDFAvista = rs.getBoolean("impressaoPDFAvista");
            if (impressaoPDFAvista) {

                obj.setNomeFormaPagamento("PAGAMENTO À VISTA");

            } else {

                String formapg = StringUtilities.doRemoverAcentos(rs.getString("formas"));

                if (formapg.toUpperCase().contains("CREDITO") || formapg.toUpperCase().contains("CHEQUE") ||
                        formapg.toUpperCase().contains("DITO") || formapg.toUpperCase().contains("CH")) {
                    obj.setNomeFormaPagamento("PAGAMENTO À PRAZO");
                } else {
                    obj.setNomeFormaPagamento("PAGAMENTO À VISTA");
                }

            }
            retorno.add(obj);
        }
        return retorno;
    }

    private List<NFeProdutoImprimirTO> consultarProdutosNFeParaGeracaoPDF(Integer id_DadosImpressaoNFe) throws Exception {

        Statement st = con.createStatement();
        ResultSet rs = st.executeQuery("select * from DadosImpressaoNFeDet where Id_DadosImpressaoNFe = " + id_DadosImpressaoNFe);
        List<NFeProdutoImprimirTO> retorno = new ArrayList<NFeProdutoImprimirTO>();
        while (rs.next()) {
            NFeProdutoImprimirTO obj = new NFeProdutoImprimirTO();

            obj.setCodigoProduto(rs.getString("ProdcProd"));
            obj.setDescricao(rs.getString("ProdxProd"));
            obj.setNcm(rs.getString("ProdNCM"));
            obj.setCst("000");
            obj.setCfop(rs.getString("ProdCFOP"));
            obj.setUnidade("UN");
            Integer qtd = rs.getInt("ProdqCom");
            obj.setQuantidade(Formatador.formatarValorMonetarioSemMoeda(qtd.doubleValue()));
            obj.setValorUnitario(Formatador.formatarValorMonetarioSemMoeda(rs.getDouble("ProdvProd")));
            obj.setValorTotal(Formatador.formatarValorMonetarioSemMoeda(rs.getDouble("ProdvProd")));
            obj.setBaseICMS(Formatador.formatarValorMonetarioSemMoeda(0.0));
            obj.setValorICMS(Formatador.formatarValorMonetarioSemMoeda(0.0));
            obj.setValorIPI(Formatador.formatarValorMonetarioSemMoeda(0.0));
            obj.setAliquotaICMS(Formatador.formatarValorMonetarioSemMoeda(0.0));
            obj.setAliquotaIPI(Formatador.formatarValorMonetarioSemMoeda(0.0));
            retorno.add(obj);
        }
        return retorno;
    }

    private List<NFeObsImprimirTO> consultarObservacoesNFeParaGeracaoPDF(Integer id_DadosImpressaoNFe) throws Exception {

        Statement st = con.createStatement();
        ResultSet rs = st.executeQuery("select * from DadosImpressaoNFeObs where Id_DadosImpressaoNFe = " + id_DadosImpressaoNFe + " order by Id_DadosImpressaoNFeObs");
        List<NFeObsImprimirTO> retorno = new ArrayList<NFeObsImprimirTO>();
        String obs = "";
        String imposto = "";
        while (rs.next()) {
            String campo = rs.getString("campo");
            if (campo.toLowerCase().equals("obs")) {
                final String observacao = rs.getString("texto");
                obs += StringUtils.isNotBlank(observacao) ? observacao.replaceAll("\\\\n", "") : "";
            } else if (campo.toLowerCase().equals("imposto")) {
                imposto += rs.getString("texto");
            }
        }

        if (!UteisValidacao.emptyString(obs)) {
            NFeObsImprimirTO obj = new NFeObsImprimirTO();
            obj.setCampo("Obs");
            obj.setTexto(obs);
            retorno.add(obj);
        }

        if (!UteisValidacao.emptyString(imposto)) {
            NFeObsImprimirTO obj = new NFeObsImprimirTO();
            obj.setCampo("Imposto");
            obj.setTexto(imposto);
            retorno.add(obj);
        }

        return retorno;
    }

    private List<NFeFaturasImprimirTO> consultarFaturasNFeParaGeracaoPDF(NFeImprimirTO nFeImprimirTO) throws Exception {

        String sql = "select * from Rps_FormaPagamento where dtvencimento > '2000-01-01 00:00:00.000' and numero is not null and id_rps = " + nFeImprimirTO.getId_RPS() + " order by numero";
        Statement st = con.createStatement();
        ResultSet rs = st.executeQuery(sql);

        List<NFeFaturasImprimirTO> retorno = new ArrayList<NFeFaturasImprimirTO>();
        while (rs.next()) {
            NFeFaturasImprimirTO fatura = new NFeFaturasImprimirTO();
            fatura.setDataVencimento(Uteis.getData(rs.getDate("dtvencimento")));
            fatura.setOrdem(rs.getInt("numero"));
            fatura.setValor(Formatador.formatarValorMonetario(rs.getDouble("valor")));
            fatura.setNumero(StringUtilities.formatarCampoForcandoZerosAEsquerda(rs.getInt("numero"), 3));
            retorno.add(fatura);
        }

        if (UteisValidacao.emptyList(retorno)) {
            nFeImprimirTO.setApresentarFaturas(false);
        } else {
            nFeImprimirTO.setApresentarFaturas(true);
        }

        return retorno;
    }

    public List<NotasImprimirTO> consultarNotasParaGeracaoPDF(String codigosId_RPS) throws Exception {
        List<NotasImprimirTO> listaNotasGeral = new ArrayList<NotasImprimirTO>();

        List<NFSeImprimirTO> listaNFSe = consultarNFSeParaGeracaoPDF(codigosId_RPS);
        for (NFSeImprimirTO nfse : listaNFSe) {
            NotasImprimirTO obj = new NotasImprimirTO();
            obj.setNotaNFe(null);
            obj.setNotaNFSe(nfse);
            listaNotasGeral.add(obj);
        }

        List<NFeImprimirTO> listaNFe = consultarNFeParaGeracaoPDF(codigosId_RPS);
        for (NFeImprimirTO nfe : listaNFe) {
            NotasImprimirTO obj = new NotasImprimirTO();
            obj.setNotaNFe(nfe);
            obj.setNotaNFSe(null);
            listaNotasGeral.add(obj);
        }

        Map<String, String> map = new HashMap<String, String>();
        List<NotasImprimirTO> listaNotasGeralRetornar = new ArrayList<NotasImprimirTO>();
        for (NotasImprimirTO obj : listaNotasGeral) {
            if (!map.containsKey(obj.getIdRPS())) {
               map.put(obj.getIdRPS(), obj.getIdRPS());
               listaNotasGeralRetornar.add(obj);
            }
        }

        return listaNotasGeralRetornar;
    }

    public String consultarProximoCodigoTabelaCodigo(String identificador) throws Exception {
        PreparedStatement sqlConsulta = con.prepareStatement("select * from codigo where identificador = '"+identificador+"'");
        ResultSet rs = sqlConsulta.executeQuery();
        if (rs.next()) {
            return rs.getString("proximoCodigo");
        } else {
            return "";
        }
    }

    public void alterarProximoCodigoTabelaCodigo(String proximoCodigo, String identificador) throws Exception {
        try {
            con.setAutoCommit(false);

            StringBuilder sql = new StringBuilder();
            sql.append("UPDATE codigo SET proximoCodigo = '").append(proximoCodigo).append("' WHERE identificador = '").append(identificador).append("';");

            PreparedStatement sqlAlterar = con.prepareStatement(sql.toString());
            sqlAlterar.execute();
            con.commit();
        } catch (Exception e) {
            con.rollback();
            con.setAutoCommit(true);
            throw e;
        } finally {
            con.setAutoCommit(true);
        }
    }

    public List<NFCeImprimirTO> consultarNFCeParaGeracaoPDF(String codigosId_NFCe) throws Exception {
        StringBuilder sql = new StringBuilder();

        sql.append("select \n");
        sql.append("d.* \n");
        sql.append("from DadosImpressaoNFCe d \n");
        sql.append("inner join empresa e on e.Id_Empresa = d.Id_Empresa \n");
        sql.append("where d.Id_NFCe in (").append(codigosId_NFCe).append(")");

        Statement st = con.createStatement();
        ResultSet rs = st.executeQuery(sql.toString());
        List<NFCeImprimirTO> retorno = new ArrayList<NFCeImprimirTO>();
        while (rs.next()) {
            NFCeImprimirTO obj = new NFCeImprimirTO();

            obj.setIdNFCe(rs.getInt("id_nfce"));
            obj.setId_DadosImpressaoNFCe(rs.getInt("Id_DadosImpressaoNFCe"));
            obj.setNomeFantasia(rs.getString("EmitXFant"));
            obj.setRazaoSocialPrestador(rs.getString("EmitXNome"));

            String inscricaoEstadual = rs.getString("EmitIE");
            String inscricaoMunicipal = rs.getString("EmitIM");
            String cnpjPrestador = rs.getString("EmitCNPJCPF");
            if (cnpjPrestador.length() == 14) {
                cnpjPrestador = Uteis.aplicarMascara(cnpjPrestador, "99.999.999/9999-99");
            }
            obj.setCnpjPrestador(cnpjPrestador);


            String infoPrestador = "";
            if (!UteisValidacao.emptyString(cnpjPrestador)) {
                infoPrestador += "CNPJ: " + cnpjPrestador;
            }
            if (!UteisValidacao.emptyString(inscricaoMunicipal)) {
                infoPrestador += "     Inscrição Municipal: " + inscricaoMunicipal;
            }
            if (!UteisValidacao.emptyString(inscricaoEstadual)) {
                infoPrestador += "     Inscrição Estadual: " + inscricaoEstadual;
            }
            obj.setInfoPrestador(infoPrestador);


            String logradouroPrestador = rs.getString("EmitEnderEmitXLgr");
            String numeroPrestador = rs.getString("EmitEnderEmitNro");
            String complementoPrestador = rs.getString("EmitEnderEmitXCpl");
            String bairroPrestador = rs.getString("EmitEnderEmitXBairro");
            String cidadePrestador = rs.getString("EmitEnderEmitXMun");
            String ufPrestador = rs.getString("EmitEnderEmitUF");
            String cepPrestador = rs.getString("EmitEnderEmitCEP");

            String enderecoPrestador = "";
            if (!UteisValidacao.emptyString(logradouroPrestador)) {
                enderecoPrestador += logradouroPrestador;
            }
            if (!UteisValidacao.emptyString(numeroPrestador)) {
                enderecoPrestador += ", " + numeroPrestador;
            }
            if (!UteisValidacao.emptyString(complementoPrestador)) {
                enderecoPrestador += " - " + complementoPrestador;
            }
            if (!UteisValidacao.emptyString(bairroPrestador)) {
                enderecoPrestador += " - " + bairroPrestador;
            }
            if (!UteisValidacao.emptyString(cidadePrestador)) {
                enderecoPrestador += " - " + cidadePrestador;
            }
            if (!UteisValidacao.emptyString(ufPrestador)) {
                enderecoPrestador += " - " + ufPrestador;
            }
            if (!UteisValidacao.emptyString(cepPrestador)) {
                if (cepPrestador.length() == 8) {
                    cepPrestador = Uteis.aplicarMascara(cepPrestador, "99999-999");
                }
                enderecoPrestador += " - " + cepPrestador;
            }
            obj.setEnderecoPrestador(enderecoPrestador);

            String telefonePrestador = rs.getString("EmitEnderEmitFone");
            if (!UteisValidacao.emptyString(telefonePrestador)) {
                obj.setTelefonePrestador("TEL: " + telefonePrestador);
            }

            obj.setValorTotal(Formatador.formatarValorMonetario(rs.getDouble("TotalICMSTotVNF")));
            obj.setChaveAcesso(rs.getString("ProcNFeChNFe"));

            String cfpConsumidor = rs.getString("DestCNPJCPF");
            if (!UteisValidacao.emptyString(cfpConsumidor)) {
                if (cfpConsumidor.length() == 14) {
                    cfpConsumidor = Uteis.aplicarMascara(cfpConsumidor, "99.999.999/9999-99");
                } else if (cfpConsumidor.length() == 11) {
                    cfpConsumidor = Uteis.aplicarMascara(cfpConsumidor, "999.999.999-99");
                }
                obj.setCpfConsumidor("CNPJ/CPF/ID Estrangeiro - " + cfpConsumidor);
            }

            obj.setNomeConsumidor(rs.getString("DestXNome"));


            String logradouroConsumidor = rs.getString("DestEnderDestXLgr");
            String numeroConsumidor = rs.getString("DestEnderDestNro");
            String complementoConsumidor = rs.getString("DestEnderDestXCpl");
            String bairroConsumidor = rs.getString("DestEnderDestXBairro");
            String cidadeConsumidor = rs.getString("DestEnderDestXMun");
            String ufConsumidor = rs.getString("DestEnderDestUF");
            String cepConsumidor = rs.getString("DestEnderDestCEP");

            String enderecoConsumidor = "";
            if (!UteisValidacao.emptyString(logradouroConsumidor)) {
                enderecoConsumidor += logradouroConsumidor;
            }
            if (!UteisValidacao.emptyString(numeroConsumidor)) {
                enderecoConsumidor += ", " + numeroConsumidor;
            }
            if (!UteisValidacao.emptyString(complementoConsumidor)) {
                enderecoConsumidor += " - " + complementoConsumidor;
            }
            if (!UteisValidacao.emptyString(bairroConsumidor)) {
                enderecoConsumidor += " - " + bairroConsumidor;
            }
            if (!UteisValidacao.emptyString(cidadeConsumidor)) {
                enderecoConsumidor += " - " + cidadeConsumidor;
            }
            if (!UteisValidacao.emptyString(ufConsumidor)) {
                enderecoConsumidor += " - " + ufConsumidor;
            }
            if (!UteisValidacao.emptyString(cepConsumidor)) {
                if (cepConsumidor.length() == 8) {
                    cepConsumidor = Uteis.aplicarMascara(cepConsumidor, "99999-999");
                }
                enderecoConsumidor += " - " + cepConsumidor;
            }
            obj.setEnderecoConsumidor(enderecoConsumidor);


            obj.setProdutos(consultarProdutosNFCeParaGeracaoPDF(obj.getId_DadosImpressaoNFCe()));
            obj.setPagamentos(consultarPagamentosNFCeParaGeracaoPDF(obj.getId_DadosImpressaoNFCe()));

            Integer qtdProdutos = obj.getProdutos().size();
            obj.setQtdProdutos(qtdProdutos.toString());

            String dataEmissao = Uteis.getDataAplicandoFormatacao(rs.getTimestamp("IdeDSaiEnt"), "dd/MM/yyyy HH:mm:ss");
            String numeroNota = rs.getString("IdeNNF");
            String serieNota = rs.getString("IdeSerie");
            String informacoesNota = "Número " + numeroNota  + " Série " + serieNota + " Emissão " + dataEmissao;

            obj.setInformacoesNota(informacoesNota);

            String protocolo = rs.getString("ProcNFeNProt");
            obj.setProtocolo("Protocolo de Autorização: " + protocolo + " " + dataEmissao);

            obj.setUrlQRCode(rs.getString("QRCode"));

            obj.setObservacoes(rs.getString("InfAdicInfCpl"));

            retorno.add(obj);
        }

        Map<String, String> map = new HashMap<String, String>();
        List<NFCeImprimirTO> listaNotasGeralRetornar = new ArrayList<NFCeImprimirTO>();
        for (NFCeImprimirTO obj : retorno) {
            if (!map.containsKey(obj.getIdNFCe().toString())) {
                map.put(obj.getIdNFCe().toString(), obj.getIdNFCe().toString());
                listaNotasGeralRetornar.add(obj);
            }
        }

        return listaNotasGeralRetornar;
    }

    private List<NFCeProdutoImprimirTO> consultarProdutosNFCeParaGeracaoPDF(Integer id_DadosImpressaoNFCe) throws Exception {

        Statement st = con.createStatement();
        ResultSet rs = st.executeQuery("select * from DadosImpressaoNFCeDet where Id_DadosImpressaoNFCe = " + id_DadosImpressaoNFCe);
        List<NFCeProdutoImprimirTO> retorno = new ArrayList<NFCeProdutoImprimirTO>();
        while (rs.next()) {
            NFCeProdutoImprimirTO obj = new NFCeProdutoImprimirTO();

            obj.setCodigo(rs.getString("ProdCProd"));
            obj.setDescricao(rs.getString("ProdxProd"));
            obj.setQuantidade(Formatador.formatarValorMonetarioSemMoeda(rs.getDouble("ProdQCom")));
            obj.setUnidade(rs.getString("ProdUTrib"));

            obj.setValorUnitario(Formatador.formatarValorMonetario(rs.getDouble("ProdVUnCom")));
            obj.setValorDesconto(Formatador.formatarValorMonetario(rs.getDouble("ProdVDesc")));
            obj.setValorAcrescimo(Formatador.formatarValorMonetario(0.0));
            obj.setValorTotal(Formatador.formatarValorMonetario(rs.getDouble("ProdVProd")));
            obj.setCofins(Formatador.formatarValorMonetario(rs.getDouble("CofinsVCOFINS")));

            retorno.add(obj);
        }
        return retorno;
    }

    private List<NFCeFormaPagamentoImprimirTO> consultarPagamentosNFCeParaGeracaoPDF(Integer id_DadosImpressaoNFCe) throws Exception {

        Statement st = con.createStatement();
        ResultSet rs = st.executeQuery("select * from DadosImpressaoNFCePag where Id_DadosImpressaoNFCe = " + id_DadosImpressaoNFCe);
        List<NFCeFormaPagamentoImprimirTO> retorno = new ArrayList<NFCeFormaPagamentoImprimirTO>();
        while (rs.next()) {
            NFCeFormaPagamentoImprimirTO obj = new NFCeFormaPagamentoImprimirTO();
            Integer codPagamento = rs.getInt("PagTPag");
            codPagamento = codPagamento - 1; //devido um problema no delphi subtrai 1 do enum
            obj.setCodigo(codPagamento.toString());
            obj.setDescricao(FormaPagamentoNFCeEnum.obterPorCodigo(codPagamento).getDescricao());
            obj.setValor(Formatador.formatarValorMonetario(rs.getDouble("PagVPag")));
            retorno.add(obj);
        }
        return retorno;
    }

    public void limparIdReferenciaParaReenvio(NotaFiscalDeServicoVO notaFiscalDeServicoVO) throws Exception {
        try {
            con.setAutoCommit(false);

            StringBuilder sql = new StringBuilder();
            sql.append("UPDATE RPS SET IdReferencia = '' WHERE ID_Lote = ").append(notaFiscalDeServicoVO.getIdLote());
            PreparedStatement sqlExcluir = con.prepareStatement(sql.toString());
            sqlExcluir.execute();
            inserirLogGenericoNotas(notaFiscalDeServicoVO.getIdLote(), null, "IdReferencia Anterior : " + notaFiscalDeServicoVO.getIdReferencia());

            con.commit();
        } catch (Exception e) {
            con.rollback();
            con.setAutoCommit(true);
            throw e;
        } finally {
            con.setAutoCommit(true);
        }
    }

    public void inserirLogGenericoNotas(Integer Id_Lote, Integer id_NFCe, String descricao) throws Exception {
        PreparedStatement sqlLog = con.prepareStatement("insert into LogGenericoNotas(DataHora,Id_Lote,id_NFCe,descricao) values (GETDATE(),?,?,?);");
        int i = 0;
        if (UteisValidacao.emptyNumber(Id_Lote)) {
            sqlLog.setNull(++i, 0);
        } else {
            sqlLog.setInt(++i, Id_Lote);
        }
        if (UteisValidacao.emptyNumber(id_NFCe)) {
            sqlLog.setNull(++i, 0);
        } else {
            sqlLog.setInt(++i, id_NFCe);
        }
        sqlLog.setString(++i, descricao);
        sqlLog.execute();
    }

    public List<NotaFiscalDeServicoVO> consultarPorId_Lote(String listaIdLote) throws Exception {
        StringBuilder sql = getSQLBase();
        sql.append("AND r.id_lote IN (").append(listaIdLote).append(")");

        Statement st = con.createStatement();
        ResultSet rs = st.executeQuery(sql.toString());
        List<NotaFiscalDeServicoVO> notas = new ArrayList<NotaFiscalDeServicoVO>();
        while (rs.next()) {
            NotaFiscalDeServicoVO nota = montarDados(rs, Uteis.NIVELMONTARDADOS_DADOSBASICOS, this.con);
            notas.add(nota);
        }
        return notas;
    }

    public boolean empresaNFE(Integer idEmpresa) throws Exception {
        String sql = "select \n" +
                "c.TipoLayout\n" +
                "from empresa e \n" +
                "inner join cidade c on c.id_cidade = e.id_cidade\n" +
                "where e.id_empresa = " + idEmpresa;
        PreparedStatement sqlConsulta = con.prepareStatement(sql);
        ResultSet tabelaResultado = sqlConsulta.executeQuery();
        Integer tipoLayout = 0;
        if (tabelaResultado.next()) {
            tipoLayout = tabelaResultado.getInt("TipoLayout");
        }
        return tipoLayout == 3;
    }

    public List<NumerosInutilizarVO> logInutilizarNFE(Integer idEmpresa) throws Exception {
        List<NumerosInutilizarVO> listaLog = new ArrayList<NumerosInutilizarVO>();

        PreparedStatement sqlConsulta = con.prepareStatement("select * from NumerosInutilizarNFE where id_empresa = " + idEmpresa + " order by dataprocesso desc, Id_NumerosInutilizarNFe desc");
        ResultSet tabelaResultado = sqlConsulta.executeQuery();
        while (tabelaResultado.next()) {
            NumerosInutilizarVO log = new NumerosInutilizarVO();
            log.setId_NumerosInutilizar(tabelaResultado.getInt("Id_NumerosInutilizarnfe"));
            log.setId_Empresa(tabelaResultado.getInt("Id_Empresa"));
            log.setSerie(tabelaResultado.getInt("serie"));
            log.setNumInicial(tabelaResultado.getInt("numInicial"));
            log.setNumFinal(tabelaResultado.getInt("numFinal"));
            log.setRetorno(tabelaResultado.getString("retorno"));
            log.setFinalizado(tabelaResultado.getBoolean("finalizado"));
            listaLog.add(log);
        }
        return listaLog;
    }

    /**
     * Veja em: {@link IntegracaoNFSeWS#consultarQuantidadeNotasNaoAutorizadasOuCanceladasDesdeOntem(String, String, String, List)}.
     */
    public Map<ModeloDocumentoFiscal, DocumentoFiscalRelatorioPeriodoTO> consultarQuantidadeNotasNaoAutorizadasOuCanceladasDesdeOntem(String chaveNFSe,
                                                                                                                                      String chaveZW,
                                                                                                                                      String cpfCnpj,
                                                                                                                                      Set<ModeloDocumentoFiscal> modelosPesquisa) throws Exception {
        StringBuilder select = new StringBuilder()
                .append("  SELECT                                                                           ")
                .append("         COUNT(*) as total,                                                        ")
                .append("         status,                                                                   ")
                .append("         DATEADD(day, DATEDIFF(day, 0, GETDATE()), -1) as 'periodoLimiteInferior', ")
                .append("         DATEADD(day, DATEDIFF(day, 0, GETDATE()), 0)  as 'periodoLimiteSuperior', ");

        StringBuilder whereAndGroupBYNFCe = returnSqlWhereConsultarQuantidadeNotasNaoAutorizadasOuCanceladas("DataHoraEmissao");
        StringBuilder whereAndGroupBYNFSe = returnSqlWhereConsultarQuantidadeNotasNaoAutorizadasOuCanceladas("DataEmissao");

        PreparedStatement sqlConsulta = con.prepareStatement(new StringBuilder()
                .append(select)
                .append("         '").append(ModeloDocumentoFiscal.NFCe.name()).append("' as modelo         ")

                .append("          FROM    NFCe    nfce                                                     ")
                .append("    INNER JOIN Empresa empresa ON nfce.Id_Empresa = empresa.Id_Empresa             ")

                .append(whereAndGroupBYNFCe)

                .append("                                     UNION                                         ")

                .append(select)
                .append("         '").append(ModeloDocumentoFiscal.NFSe.name()).append("' as modelo         ")

                .append("          FROM     RPS     rps                                                     ")
                .append("    INNER JOIN    Lote    lote ON    lote.Id_Lote = rps.Id_Lote                    ")
                .append("    INNER JOIN Empresa empresa ON lote.Id_Empresa = empresa.Id_Empresa             ")

                .append(whereAndGroupBYNFSe)
                .toString());

        sqlConsulta.setString(1, chaveNFSe);
        sqlConsulta.setString(2, chaveZW);
        sqlConsulta.setString(3, cpfCnpj);
        sqlConsulta.setString(4, chaveNFSe);
        sqlConsulta.setString(5, chaveZW);
        sqlConsulta.setString(6, cpfCnpj);

        ResultSet result = sqlConsulta.executeQuery();
        Map<ModeloDocumentoFiscal, DocumentoFiscalRelatorioPeriodoTO> mapRelatorioPorModelo = new HashMap<ModeloDocumentoFiscal, DocumentoFiscalRelatorioPeriodoTO>();

        while (result.next()) {
            Date limiteInferior = result.getTimestamp("periodoLimiteInferior");
            Date limiteSuperior = result.getTimestamp("periodoLimiteSuperior");

            StatusNFCeEnum statusEnum = StatusNFCeEnum.getEnumByDescricaoThrowsException(result.getString("status"));
            ModeloDocumentoFiscal modeloEnum = ModeloDocumentoFiscal.getEnumByNameThrowsException(result.getString("modelo"));

            if (modelosPesquisa.contains(modeloEnum)) {
                DocumentoFiscalRelatorioPeriodoTO relatorioTO;
                if (mapRelatorioPorModelo.get(modeloEnum) == null) {
                    relatorioTO = new DocumentoFiscalRelatorioPeriodoTO();
                    relatorioTO.setPeriodoLimiteInferior(limiteInferior);
                    relatorioTO.setPeriodoLimiteSuperior(limiteSuperior);
                } else {
                    relatorioTO = mapRelatorioPorModelo.get(modeloEnum);
                }

                relatorioTO.getMapQuantidadePorStatus().put(statusEnum.toString(), result.getInt("total"));

                mapRelatorioPorModelo.put(modeloEnum, relatorioTO);
            }
        }

        return mapRelatorioPorModelo;
    }

    private StringBuilder returnSqlWhereConsultarQuantidadeNotasNaoAutorizadasOuCanceladas(String dataEmissaoColumnName) {
        String status = "'" + NAO_AUTORIZADO.getDescricao() + "', '" + CANCELADO.getDescricao() + "'";
        String sqlServerDataOntemTimeZerado = "DATEADD(day, DATEDIFF(day, 0, GETDATE()), -1)";
        String sqlServerDataHojeTimeZerado = "DATEADD(day, DATEDIFF(day, 0, GETDATE()), 0)";
        return new StringBuilder()
                .append("   WHERE   empresa.Chave = ?                               ")
                .append("     AND empresa.chavezw = ?                               ")
                .append("     AND empresa.CPFCNPJ = ?                               ")
                .append("     AND ").append(dataEmissaoColumnName).append(" BETWEEN ").append(sqlServerDataOntemTimeZerado)
                                                                      .append(" AND ").append(sqlServerDataHojeTimeZerado)
                .append("     AND          Status IN (").append(status).append(")   ")
                .append("GROUP BY status                                            ");
    }

    public JSONObject consultarInformacoesEmpresa(String key, String cnpj) throws Exception {

        String cnpjFormatado = Uteis.aplicarMascara(cnpj, "99.999.999/9999-99");
        String cnpjSemMascara = Uteis.removerMascara(cnpj);

        StringBuilder sql = new StringBuilder();
        sql.append("select \n");
        sql.append("id_empresa \n");
        sql.append("from empresa \n");
        sql.append("where (CPFCNPJ = '").append(cnpjSemMascara).append("' or CPFCNPJ = '").append(cnpjFormatado).append("')");
        Statement stmEm = con.createStatement();
        ResultSet rsEm = stmEm.executeQuery(sql.toString());

        Integer idEmpresa = 0;
        if (rsEm.next()) {
            idEmpresa = rsEm.getInt("id_empresa");
        }

        if (UteisValidacao.emptyNumber(idEmpresa)) {
            throw new Exception("Não foi encontrada empresa com o CNPJ " + cnpjFormatado);
        }

        StringBuilder sqlDados = new StringBuilder();
        sqlDados.append("select   \n");
        sqlDados.append("top 1  \n");
        sqlDados.append("r.serierps,  \n");
        sqlDados.append("(select top 1 ProximoCodigo from codigo where identificador = 'SeqLote_").append(idEmpresa).append("' order by Id_Codigo desc) as seqLote,  \n");
        sqlDados.append("(select top 1 ProximoCodigo from codigo where identificador = 'SeqRPS_").append(idEmpresa).append("' order by Id_Codigo desc) as seqRPS,  \n");
        sqlDados.append("r.AliquotaAtividade, \n");
        sqlDados.append("r.ItemListaServico, \n");
        sqlDados.append("r.CodigoCnae, \n");
        sqlDados.append("r.CodigoTributacaoMunicipio, \n");
        sqlDados.append("r.ISSRetido, \n");
        sqlDados.append("r.ExigibilidadeISS \n");
        sqlDados.append("from rps r  \n");
        sqlDados.append("inner join lote l on l.id_lote = r.id_lote  \n");
        sqlDados.append("inner join empresa e on e.Id_Empresa = l.Id_Empresa  \n");
        sqlDados.append("where e.id_empresa = ").append(idEmpresa).append(" \n");
        sqlDados.append("and r.status = 'Autorizado'  \n");
        sqlDados.append("order by r.id_rps desc  \n");
        Statement stm = con.createStatement();
        ResultSet rs = stm.executeQuery(sqlDados.toString());

        JSONObject jsonObject = new JSONObject();


        if (rs.next()) {

            jsonObject.put("serierps", rs.getString("serierps"));
            jsonObject.put("proximoLote", rs.getInt("seqLote"));
            jsonObject.put("proximoRPS", rs.getInt("seqRPS"));
            jsonObject.put("aliquotaAtividade", rs.getDouble("AliquotaAtividade"));
            jsonObject.put("itemListaServico", rs.getString("ItemListaServico"));
            jsonObject.put("codigoCnae", rs.getString("CodigoCnae"));
            jsonObject.put("codigoTributacaoMunicipio", rs.getString("CodigoTributacaoMunicipio"));
            jsonObject.put("iSSRetido", rs.getBoolean("ISSRetido"));
            jsonObject.put("exigibilidadeISS", rs.getString("ExigibilidadeISS"));

        }

        byte[] logomarca = null;
        byte[] certificado = null;

        StringBuilder sqlEmpresa = new StringBuilder();
        sqlEmpresa.append("select \n");
        sqlEmpresa.append("(select top 1 arquivo from arquivo  where Identificador = 'Certificado' and Id_PKRef = ").append(idEmpresa).append(" order by id_arquivo desc) as certificado,  \n");
        sqlEmpresa.append("(select top 1 NumeroEnvio from nfce where  Id_Empresa = e.Id_Empresa order by id_nfce desc) as numeroenvioNFCE, \n");
        sqlEmpresa.append("e.logomarca,  \n");
        sqlEmpresa.append("e.CPFCNPJ, \n");
        sqlEmpresa.append("e.InscricaoMunicipal, \n");
        sqlEmpresa.append("e.InscricaoEstadual, \n");
        sqlEmpresa.append("e.SenhaCertificado, \n");
        sqlEmpresa.append("e.RegimeEspecialTributacao, \n");
        sqlEmpresa.append("e.optanteSimplesNacional, \n");
        sqlEmpresa.append("e.IncentivadorCultural, \n");
        sqlEmpresa.append("e.idCsc, \n");
        sqlEmpresa.append("e.csc, \n");
        sqlEmpresa.append("e.ccm, \n");
        sqlEmpresa.append("e.aliquotaSimples, \n");
        sqlEmpresa.append("e.UsuarioSistema, \n");
        sqlEmpresa.append("e.Senha \n");
        sqlEmpresa.append("from empresa e \n");
        sqlEmpresa.append("where e.id_empresa = ").append(idEmpresa);
        Statement stmEmp = con.createStatement();
        ResultSet rsEmp = stmEmp.executeQuery(sqlEmpresa.toString());

        if (rsEmp.next()) {

            jsonObject.put("idEmpresa", idEmpresa);
            jsonObject.put("cpfCNPJ", rsEmp.getString("CPFCNPJ"));
            jsonObject.put("senhaCertificado", Uteis.desencriptarNFe(rsEmp.getString("SenhaCertificado")));
            jsonObject.put("numeroenvioNFCE", rsEmp.getString("numeroenvioNFCE"));
            jsonObject.put("inscricaoMunicipal", rsEmp.getString("InscricaoMunicipal"));
            jsonObject.put("inscricaoEstadual", rsEmp.getString("InscricaoEstadual"));
            jsonObject.put("regimeEspecialTributacao", rsEmp.getString("RegimeEspecialTributacao"));
            jsonObject.put("optanteSimplesNacional", rsEmp.getBoolean("optanteSimplesNacional"));
            jsonObject.put("incentivadorCultural", rsEmp.getInt("IncentivadorCultural"));
            jsonObject.put("idCsc", rsEmp.getString("idCsc"));
            jsonObject.put("csc", rsEmp.getString("csc"));
            jsonObject.put("ccm", rsEmp.getString("ccm"));
            jsonObject.put("aliquotaSimples", rsEmp.getDouble("aliquotaSimples"));
            jsonObject.put("usuarioInscricaoMunicipal", rsEmp.getString("UsuarioSistema"));
            jsonObject.put("senhaInscricaoMunicipal", Uteis.desencriptarNFe(rsEmp.getString("Senha")));

            logomarca = rsEmp.getBytes("Logomarca");
            certificado = rsEmp.getBytes("certificado");
        }

        try {
            jsonObject.put("certificado", enviarArquivo(key, "CERT_NFE_" + idEmpresa + ".pfx", certificado));
        } catch (Exception ignored) {
            jsonObject.put("certificado", "");
        }

        try {
            jsonObject.put("logomarca", enviarArquivo(key, "LOGO_NFE_" + idEmpresa+ ".png", logomarca));
        } catch (Exception ignored) {
            jsonObject.put("logomarca", "");
        }

        return jsonObject;
    }

    private String enviarArquivo(String key, String identificador, byte[] arquivo) throws Exception {
        String chave = MidiaService.getInstance().uploadObjectFromByteArray(key, MidiaEntidadeEnum.NOTAFISCAL_LOGOTIPO,
                identificador, arquivo, false);
        return Uteis.getPaintFotoDaNuvem(chave);
    }

    public NotaFiscalConsumidorNFCeVO consultarStatusGestaoNFCe(Integer codigo) throws Exception {

        StringBuilder sql = getSQLBasicoNFCe();
        sql.append("AND n.Id_NFCe = ").append(codigo);

        PreparedStatement sqlConsulta = con.prepareStatement(sql.toString());
        ResultSet tabelaResultado = sqlConsulta.executeQuery();
        if (!tabelaResultado.next()) {
            return new NotaFiscalConsumidorNFCeVO();
        }

        NotaFiscalConsumidorNFCeVO notaFiscalConsumidorNFCeVO = montarDadosNFCe(tabelaResultado, Uteis.NIVELMONTARDADOS_DADOSBASICOS, this.con);
        if (UteisValidacao.emptyNumber(notaFiscalConsumidorNFCeVO.getIdReenvio())) {
            return notaFiscalConsumidorNFCeVO;
        } else {
            return consultarStatusGestaoNFCe(notaFiscalConsumidorNFCeVO.getIdReenvio());
        }
    }

    public String gerarPDFNotasNFSe(String codigosId_RPS, HttpServletRequest request) throws Exception {
        List<NotasImprimirTO> listaNotasGeral = consultarNotasParaGeracaoPDF(codigosId_RPS);

        if (listaNotasGeral.size() > 1) {
            //GERAR ARQUIVO ZIP COM VÁRIOS

            List<String> nomes = new ArrayList<String>();
            List<String> absolutePathToZip = new ArrayList<String>();

            for (NotasImprimirTO nota : listaNotasGeral) {
                try {
                    String arquivo = gerarArquivoPDFNotaFiscal(nota, request);
                    File pdfFile = new File(request.getRealPath("relatorio") + File.separator + arquivo);
                    byte[] b = new byte[(int) pdfFile.length()];
                    FileInputStream fileInputStream = new FileInputStream(pdfFile);
                    fileInputStream.read(b);

                    nomes.add(nota.getNomeArquivo() + ".pdf");
                    absolutePathToZip.add(pdfFile.getAbsolutePath());
                } catch (Exception ignored) {
                }
            }

            String arquivo = "PDF" + System.currentTimeMillis() + ".zip";

            String nomeArquivoGerado = request.getRealPath("relatorio") + File.separator + arquivo;
            boolean gerou = Uteis.zipFiles(nomeArquivoGerado, absolutePathToZip, nomes);

            if (!gerou) {
                throw new Exception("Erro ao gerar arquivo");
            }

            return arquivo;
        } else if (listaNotasGeral.size() == 1) {
            //GERAR SOMENTE DE 1 ARQUIVO

            String arquivo = gerarArquivoPDFNotaFiscal(listaNotasGeral.get(0), request);
            File pdfFile = new File(request.getRealPath("relatorio") + File.separator + arquivo);
            return listaNotasGeral.get(0).getNomeArquivo() + ".pdf";
        } else {
            throw new Exception("Nenhuma nota.");
        }
    }

    private String gerarArquivoPDFNotaFiscal(NotasImprimirTO nota, HttpServletRequest request) throws Exception {

        verificaImagemPadrao(nota, request);

        if (nota.getNFSe()) {

            List<NFSeImprimirTO> lista = new ArrayList<NFSeImprimirTO>();
            lista.add(nota.getNotaNFSe());

            Map<String, Object> params = new HashMap<String, Object>();
            params.put("nomeDesignIReport", "relatorio" + File.separator + "designRelatorio" + File.separator + "nfse" + File.separator + "NFSe.jrxml");
            params.put("SUBREPORT_DIR", "relatorio" + File.separator + "designRelatorio" + File.separator + "nfse" + File.separator);
            params.put("tipoRelatorio", "PDF");
            params.put("tipoImplementacao", "OBJETO");
            params.put("nomeRelatorio", "NFSE");
            params.put("nomeEmpresa", "");
            params.put("listaObjetos", lista);
            return new SuperControleRelatorio().imprimirNotaFiscal(request, params);

        } else {

            List<NFeImprimirTO> lista = new ArrayList<NFeImprimirTO>();
            lista.add(nota.getNotaNFe());

            Map<String, Object> params = new HashMap<String, Object>();
            params.put("nomeDesignIReport", "relatorio" + File.separator + "designRelatorio" + File.separator + "nfse" + File.separator + "NFe.jrxml");
            params.put("SUBREPORT_DIR", "relatorio" + File.separator + "designRelatorio" + File.separator + "nfse" + File.separator);
            params.put("tipoRelatorio", "PDF");
            params.put("tipoImplementacao", "OBJETO");
            params.put("nomeRelatorio", "NFe");
            params.put("nomeEmpresa", "");
            params.put("listaObjetos", lista);
            return new SuperControleRelatorio().imprimirNotaFiscal(request, params);
        }
    }

    private void verificaImagemPadrao(NotasImprimirTO nota, HttpServletRequest request) throws Exception {
        InputStream imagemPadrao = getImagemPadrao(request);

        if (nota.getNFSe() && nota.getNotaNFSe().getLogomarcaPrefeitura() == null) {
            nota.getNotaNFSe().setLogomarcaPrefeitura(imagemPadrao);
        }

        processarImagemLogomarcaPrestador(nota, request, imagemPadrao);
    }

    private InputStream getImagemPadrao(HttpServletRequest request) throws Exception {
        String caminho = request.getRealPath("images") + File.separator + "logo_nfse.jpg";
        File imagem = new File(caminho);
        ByteArrayOutputStream arrayOutputStream = new ByteArrayOutputStream();
        byte buffer[] = new byte[4096];
        int bytesRead = 0;
        FileInputStream fi = new FileInputStream(imagem.getAbsolutePath());
        while ((bytesRead = fi.read(buffer)) != -1) {
            arrayOutputStream.write(buffer, 0, bytesRead);
        }
        byte[] a = (arrayOutputStream.toByteArray());
        InputStream fs = new ByteArrayInputStream(a);
        arrayOutputStream.close();
        fi.close();
        return fs;
    }

    private void processarImagemLogomarcaPrestador(NotasImprimirTO nota, HttpServletRequest request, InputStream imagemPadrao) {
        try {
            if (nota.getLogomarcaPrestadorByte().length == 0) {
                throw new Exception("Sem imagem");
            }

            String identificador = "NFSE_LOGOMARCA" + "_" + nota.getIdEmpresa();
            String caminhoBase = File.separator + "imgTempFeed" + File.separator + identificador + ".jpg";
            String caminhoSalvar = request.getRealPath("imagens") + caminhoBase;

            File arqGerado = FileUtilities.saveToFileReturnFile(nota.getLogomarcaPrestadorByte(), caminhoSalvar);
            ByteArrayOutputStream arrayOutputStream = new ByteArrayOutputStream();
            byte buffer[] = new byte[4096];
            int bytesRead = 0;
            FileInputStream fi = new FileInputStream(arqGerado.getAbsolutePath());
            while ((bytesRead = fi.read(buffer)) != -1) {
                arrayOutputStream.write(buffer, 0, bytesRead);
            }
            byte[] a = (arrayOutputStream.toByteArray());
            InputStream fs = new ByteArrayInputStream(a);
            arrayOutputStream.close();
            fi.close();
            nota.setLogomarcaPrestador(fs);
        } catch (Exception e) {
            nota.setLogomarcaPrestador(imagemPadrao);
        }
    }

    public String gerarPDFNotasNFCe(String codigosId_NFCe, HttpServletRequest request) throws Exception {
        List<NFCeImprimirTO> listaNotasNFCe = consultarNFCeParaGeracaoPDF(codigosId_NFCe);

        if (listaNotasNFCe.size() > 1) {
            //GERAR ARQUIVO ZIP COM VÁRIOS

            List<byte[]> arquivos = new ArrayList<byte[]>();
            List<String> nomes = new ArrayList<String>();

            for (NFCeImprimirTO nota : listaNotasNFCe) {
                try {
                    String arquivo = gerarArquivoPDFNotaFiscalNFCe(nota, request);
                    File pdfFile = new File(request.getRealPath("relatorio") + File.separator + arquivo);

                    byte[] b = new byte[(int) pdfFile.length()];
                    FileInputStream fileInputStream = new FileInputStream(pdfFile);
                    fileInputStream.read(b);

                    arquivos.add(b);
                    nomes.add(nota.getNomeArquivoPDF() + ".pdf");
                } catch (Exception ignored) {
                }
            }

            String arquivo = "PDF-NFCe-" + System.currentTimeMillis() + ".zip";

            String nomeArquivoGerado = request.getRealPath("relatorio") + File.separator + arquivo;
            boolean gerou = Uteis.zip(nomeArquivoGerado, arquivos, nomes);

            if (!gerou) {
                throw new Exception("Erro ao gerar arquivo");
            }
            return arquivo;
        } else if (listaNotasNFCe.size() == 1) {
            //GERAR SOMENTE DE 1 ARQUIVO

            String arquivo = gerarArquivoPDFNotaFiscalNFCe(listaNotasNFCe.get(0), request);
            File pdfFile = new File(request.getRealPath("relatorio") + File.separator + arquivo);
            return listaNotasNFCe.get(0).getNomeArquivoPDF() + ".pdf";
        } else {
            throw new Exception("Nenhuma nota.");
        }
    }

    private String gerarArquivoPDFNotaFiscalNFCe(NFCeImprimirTO nota, HttpServletRequest request) throws Exception {

        processarQRCode(nota);

        List<NFCeImprimirTO> lista = new ArrayList<NFCeImprimirTO>();
        lista.add(nota);

        Map<String, Object> params = new HashMap<String, Object>();
        params.put("nomeDesignIReport", "relatorio" + File.separator + "designRelatorio" + File.separator + "nfse" + File.separator + "NFCe.jrxml");
        params.put("SUBREPORT_DIR", "relatorio" + File.separator + "designRelatorio" + File.separator + "nfse" + File.separator);
        params.put("tipoRelatorio", "PDF");
        params.put("tipoImplementacao", "OBJETO");
        params.put("nomeRelatorio", "NFCE");
        params.put("nomeEmpresa", "");
        params.put("listaObjetos", lista);
        return new SuperControleRelatorio().imprimirNotaFiscal(request, params);
    }

    private void processarQRCode(NFCeImprimirTO nota) throws IOException {

        String textoQRCode = nota.getUrlQRCode();

        ByteArrayOutputStream out = QRCode.from(textoQRCode).to(ImageType.PNG).withSize(325, 325).stream();
        byte[] a = (out.toByteArray());
        InputStream fs = new ByteArrayInputStream(a);
        out.close();
        nota.setQrCode(fs);
        fs.close();
    }

    public String obtenhaLinkXML(Integer codigoReferencia, boolean NFCe, boolean xmlPrefeitura, boolean xmlManipulado, boolean xmlCancelamento, boolean xmlManipuladoNFCe) {
        String identificadorPasta = obterPastaReferencia(codigoReferencia);
        String identificadorArquivo = "XML-N" + codigoReferencia;
        String chave = "pactonfse/XML-N/"+ identificadorPasta + "/";

        if (NFCe) {

            if (xmlCancelamento) {
                identificadorArquivo = "XMLCC" + codigoReferencia;
                chave = "pactonfse/XMLCC/"+ identificadorPasta + "/";
            } else {
                if (xmlManipuladoNFCe) {
                    identificadorArquivo = "XMLPC" + codigoReferencia;
                    chave = "pactonfse/XMLPC/" + identificadorPasta + "/";
                } else {
                    identificadorArquivo = "XMLNC" + codigoReferencia;
                    chave = "pactonfse/XMLNC/" + identificadorPasta + "/";
                }
            }

        } else {

            if (xmlCancelamento) {

                identificadorArquivo = "XML-C" + codigoReferencia;
                chave = "pactonfse/XML-C/"+ identificadorPasta + "/";

            } else {

                if (xmlPrefeitura) {
                    //TICKET #8639
                    identificadorArquivo = "XMLSO" + codigoReferencia;
                    chave = "pactonfse/XMLSO/"+ identificadorPasta + "/";
                }

                if (xmlManipulado) {
                    //TICKET #9361
                    identificadorArquivo = "XMLPR" + codigoReferencia;
                    chave = "pactonfse/XMLPR/"+ identificadorPasta + "/";
                }

            }
        }
        return "https://cdn1.pactorian.net/" + chave + identificadorArquivo + ".xml";
    }
}

package negocio.facade.jdbc.nfe;

import br.com.pactosolucoes.comuns.util.Formatador;
import br.com.pactosolucoes.comuns.util.StringUtilities;
import br.com.pactosolucoes.oamd.controle.basico.DAO;
import org.json.JSONArray;
import org.json.JSONException;
import org.json.JSONObject;
import negocio.comuns.arquitetura.UsuarioVO;
import negocio.comuns.basico.*;
import negocio.comuns.contrato.MovProdutoVO;
import negocio.comuns.financeiro.CartaoCreditoVO;
import negocio.comuns.financeiro.ChequeVO;
import negocio.comuns.financeiro.ItemGestaoNotaFamiliaTO;
import negocio.comuns.financeiro.ItemGestaoNotasTO;
import negocio.comuns.financeiro.MovContaVO;
import negocio.comuns.financeiro.MovPagamentoVO;
import negocio.comuns.financeiro.NFSeEmitidaFormaPagamentoTO;
import negocio.comuns.financeiro.NFSeEmitidaVO;
import negocio.comuns.financeiro.ReciboPagamentoVO;
import negocio.comuns.financeiro.enumerador.TipoFormaPagto;
import negocio.comuns.financeiro.enumerador.TipoRelatorioDF;
import negocio.comuns.nfe.LoteNFeVO;
import negocio.comuns.nfe.RetornoEnvioNotaFiscalTO;
import negocio.comuns.nfe.enumerador.ResultadoEnvioNFSeEnum;
import negocio.comuns.nfe.enumerador.StatusNotaEnum;
import negocio.comuns.notaFiscal.NotaProcessarTO;
import negocio.comuns.notaFiscal.SituacaoNotaFiscalEnum;
import negocio.comuns.plano.ProdutoVO;
import negocio.comuns.utilitarias.Calendario;
import negocio.comuns.utilitarias.ConsistirException;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisValidacao;
import negocio.comuns.utilitarias.UtilReflection;
import negocio.comuns.notaFiscal.TipoNotaFiscalEnum;
import negocio.facade.jdbc.arquitetura.SuperEntidade;
import negocio.facade.jdbc.basico.*;
import negocio.facade.jdbc.notaFiscal.NotaFiscal;
import negocio.facade.jdbc.financeiro.MovPagamento;
import negocio.facade.jdbc.financeiro.NFSeEmitida;
import negocio.facade.jdbc.financeiro.ReciboPagamento;
import negocio.interfaces.nfe.LoteNFeInterfaceFacade;
import org.apache.commons.lang.StringUtils;
import servicos.util.ExecuteRequestHttpService;

import java.io.IOException;
import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.Statement;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collection;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;

public class LoteNFe extends SuperEntidade implements LoteNFeInterfaceFacade {

    private static final String METODO_RETORNARSTATUS = ".retornarStatus";
    private static final String METODO_PEGARCODIGOSRPSDOSIDEXTERNOS = ".pegarCodigosRPSDosIdsExternos";
    private static final String METODO_REENVIARRPS = ".reenviarRPS";
    private static final String METODO_GRAVARLISTALOTECOMREFERENCIAASYNC = ".gravarListaLoteComReferenciaAsync";
    private static final String METODO_INUTILIZARNUMEROSNFE = ".inutilizarNumerosNFe";

    private static final String IDENTIFICADOR_FAMILIA = "FAM_";

    public LoteNFe() throws Exception {
        super();
    }

    public LoteNFe(Connection con) throws Exception {
        super(con);
    }

    private EmpresaVO obterEmpresaVO(Integer codEmpresa) throws Exception {
        Empresa empresaDAO = new Empresa(con);
        EmpresaVO empresaVO = empresaDAO.consultarPorChavePrimaria(codEmpresa, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
        empresaDAO = null;
        return empresaVO;
    }

    private ConfiguracaoNotaFiscalVO obterConfiguracaoNotaFiscalVO(Integer codConfiguracaoNotaFiscal) throws Exception {
        ConfiguracaoNotaFiscal configuracaoNotaFiscalDAO = new ConfiguracaoNotaFiscal(con);
        ConfiguracaoNotaFiscalVO configuracaoNotaFiscalVO = configuracaoNotaFiscalDAO.consultarPorChavePrimaria(codConfiguracaoNotaFiscal, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
        configuracaoNotaFiscalDAO = null;
        return configuracaoNotaFiscalVO;
    }

    @Override
    public LoteNFeVO consultarPorChavePrimaria(Integer codigo) throws Exception {
        String sql = "SELECT * FROM Lote WHERE Id_Lote = ?";
        PreparedStatement sqlConsultar = con.prepareStatement(sql);
        sqlConsultar.setInt(1, codigo);
        ResultSet tabelaResultado = sqlConsultar.executeQuery();
        if (!tabelaResultado.next()) {
            throw new ConsistirException("Dados Não Encontrados ( Lote ).");
        }
        return (montarDados(tabelaResultado, this.con));
    }

    private LoteNFeVO montarDados(ResultSet dadosSQL, Connection con) throws Exception {
        LoteNFeVO obj = new LoteNFeVO();
        obj.setId_lote(dadosSQL.getInt("Id_Lote"));
        obj.setDataIni(Uteis.getSQLData(dadosSQL.getDate("DataIni")));
        obj.setDataFim(Uteis.getSQLData(dadosSQL.getDate("DataFim")));
        obj.setQtdRPS(dadosSQL.getInt("QtdRPS"));
        obj.setValorTotalServico(dadosSQL.getDouble("ValorTotalServicos"));
        obj.setValorTotalDeducoes(dadosSQL.getDouble("ValorTotalDeducoes"));

        EmpresaNFe empresaNFe = new EmpresaNFe(con);
        obj.setEmpresaNFeVO(empresaNFe.consultarPorChavePrimaria(dadosSQL.getInt("Id_Empresa")));

        return obj;
    }

    public FornecedorVO obterFornecedor(PessoaVO pessoa) {
        FornecedorVO forncedor = new FornecedorVO();
        forncedor.setDescricao(pessoa.getNome());
        forncedor.setPessoa(pessoa);
        forncedor.setCnpj(pessoa.getCnpj());
        forncedor.setCfdf(pessoa.getCfdf());
        forncedor.setInscricaoEstadual(pessoa.getInscEstadual());
        return forncedor;
    }

    private RetornoNFSe montarNFSeFamilia(Map<String, JSONObject> itens, ItemGestaoNotasTO itemNota, EmpresaVO empresaVO, TipoRelatorioDF tipoRelatorioDF) throws Exception {
        RetornoNFSe retornoNFSe = null;
        switch (tipoRelatorioDF) {
            case FATURAMENTO_DE_CAIXA: {
                retornoNFSe = montarNFSeFamiliaFaturamentoCaixa(itens, itemNota, empresaVO);
                break;
            }
            case FATURAMENTO:
            case COMPETENCIA: {
                itemNota.setMovProdutoVO(getFacade().getMovProduto().consultarPorChavePrimaria(itemNota.getMovProdutoVO().getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS));
                if (empresaVO.getTipoProdutoEmissaoNFSe().contains(itemNota.getMovProdutoVO().getProduto().getTipoProduto())) {
                    retornoNFSe = new RetornoNFSe();
                    retornoNFSe.setAlgumServico(true);
                    retornoNFSe.setValor(retornoNFSe.getValor() + itemNota.getMovProdutoVO().getTotalFinal());
                    retornoNFSe.getDescricao().append(adicionarItemNotaFamilia(itens, itemNota.getMovProdutoVO(), itemNota.getMovProdutoVO().getTotalFinal(), empresaVO, itemNota.getMovProdutoVO().getPessoa(), true));
                    retornoNFSe.getListaPagamentos().addAll(obterPagamentoMovProduto(itemNota.getMovProdutoVO().getCodigo()));
                }
                break;
            }
            case RECEITA: {
                retornoNFSe = montarNFSeFamiliaReceita(itens, itemNota, empresaVO);
                break;
            }
        }

        return retornoNFSe;
    }

    private RetornoNFSe montarNFSeFamiliaReceita(Map<String, JSONObject> itens, ItemGestaoNotasTO itemNota, EmpresaVO empresaVO) throws Exception {
        RetornoNFSe retornoNFSe = new RetornoNFSe();
        String produtosPagos;
        String siglaTipoFormaPagamento;
        if (itemNota.getChequeVO() != null) {
            produtosPagos = itemNota.getChequeVO().getProdutosPagos();
            siglaTipoFormaPagamento = TipoFormaPagto.CHEQUE.getSigla();
        } else if (itemNota.getCartaoCreditoVO() != null) {
            produtosPagos = itemNota.getCartaoCreditoVO().getProdutosPagos();
            siglaTipoFormaPagamento = TipoFormaPagto.CARTAOCREDITO.getSigla();
        } else if (itemNota.getMovPagamentoVO() != null) {
            produtosPagos = itemNota.getMovPagamentoVO().getProdutosPagos();
            itemNota.getMovPagamentoVO().setFormaPagamento(getFacade().getFormaPagamento().consultarPorChavePrimaria(itemNota.getMovPagamentoVO().getFormaPagamento().getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS));
            siglaTipoFormaPagamento = itemNota.getMovPagamentoVO().getFormaPagamento().getTipoFormaPagamento();
        } else {
            throw new ConsistirException("O item da nota não está associado a nenhum pagamento.");
        }
        if (UteisValidacao.emptyString(produtosPagos)) {
            throw new ConsistirException("Este pagamento não está associado a produtos.");
        }
        String[] produtos = produtosPagos.split("\\|");
        for (String prod : produtos) {
            if (UteisValidacao.emptyString(prod) || prod.equals("null")) {
                continue;
            }
            String[] split = prod.split(",");
            Integer codigoMovProduto = Integer.valueOf(split[0]);
            itemNota.setMovProdutoVO(getFacade().getMovProduto().consultarPorChavePrimaria(codigoMovProduto, Uteis.NIVELMONTARDADOS_DADOSBASICOS));
            if (empresaVO.getTipoProdutoEmissaoNFSe().contains(itemNota.getMovProdutoVO().getProduto().getTipoProduto())) {
                retornoNFSe.setAlgumServico(true);
                retornoNFSe.setValor(retornoNFSe.getValor() + Double.valueOf(split[3]));
                retornoNFSe.getDescricao().append(adicionarItemNotaFamilia(itens, itemNota.getMovProdutoVO(), Double.valueOf(split[3]), empresaVO, itemNota.getMovProdutoVO().getPessoa(), false));
            }
        }

        NFSeEmitidaFormaPagamentoTO forma = new NFSeEmitidaFormaPagamentoTO();
        forma.setSiglaTipoFormaPagamento(siglaTipoFormaPagamento);
        forma.setValor(retornoNFSe.getValor());
        retornoNFSe.getListaPagamentos().add(forma);
        return retornoNFSe;
    }

    private RetornoNFSe montarNFSeFamiliaFaturamentoCaixa(Map<String, JSONObject> itens, ItemGestaoNotasTO itemNota, EmpresaVO empresaVO) throws Exception {
        RetornoNFSe retornoNFSe = new RetornoNFSe();
        for (MovPagamentoVO pagamento : itemNota.getReciboPagamentoVO().getPagamentosDesteRecibo()) {
            if (!pagamento.getCredito()) {
                String produtosPagos = pagamento.getProdutosPagos();
                if (UteisValidacao.emptyString(produtosPagos)) {
                    throw new ConsistirException("Este pagamento não está associado a produtos.");
                }
                String[] produtos = produtosPagos.split("\\|");
                for (String prod : produtos) {
                    if (UteisValidacao.emptyString(prod) || prod.equals("null")) {
                        continue;
                    }
                    String[] split = prod.split(",");
                    Integer codigoMovProduto = Integer.valueOf(split[0]);
                    MovProdutoVO movProdutoVO = getFacade().getMovProduto().consultarPorChavePrimaria(codigoMovProduto, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
                    if (empresaVO.getTipoProdutoEmissaoNFSe().contains(movProdutoVO.getProduto().getTipoProduto())) {
                        retornoNFSe.setAlgumServico(true);
                        retornoNFSe.setValor(retornoNFSe.getValor() + Double.valueOf(split[3]));
                        retornoNFSe.getDescricao().append(adicionarItemNotaFamilia(itens, movProdutoVO, Double.valueOf(split[3]), empresaVO, movProdutoVO.getPessoa(), false));
                        retornoNFSe.getListaPagamentos().addAll(obterPagamentoMovProduto(movProdutoVO.getCodigo()));
                    }
                }
            }
        }
        return retornoNFSe;
    }

    private String adicionarItemNotaFamilia(Map<String, JSONObject> itens, MovProdutoVO movProduto, Double valorUnitario, EmpresaVO empresa, PessoaVO pessoa, boolean itemPorCliente) throws JSONException {
        String identificador = null;
        String descricao = "";
        if (itemPorCliente) {
            identificador = movProduto.getCodigo().toString() + (pessoa != null ? pessoa.getCodigo().toString() : "");
        } else {
            identificador = movProduto.getProduto().getCodigo().toString() + (pessoa != null ? pessoa.getCodigo().toString() : "");
        }
        JSONObject item = itens.get(identificador);
        if (item == null) {
            item = new JSONObject();
            item.put("ValorUnitario", valorUnitario);
            item.put("Quantidade", 1);
            item.put("Descricao", "");
            item.put("Tributavel", true);
            item.put("NCM", movProduto.getProduto().getNcm());
            itens.put(identificador, item);
            String descricaoMovProduto = (movProduto.getProduto().getTipoProduto().equals("PM") ? movProduto.getDescricao().substring(0, (movProduto.getDescricao().indexOf("- " + movProduto.getMesReferencia()) - 1)) : movProduto.getDescricao());
            String meses = "";
            if (movProduto.getContrato_Apresentar() > 0) {
                meses = (movProduto.getContrato().getContratoDuracao().getNumeroMeses() == 1) ?
                        movProduto.getContrato().getContratoDuracao().getNumeroMeses() + " MÊS" :
                        movProduto.getContrato().getContratoDuracao().getNumeroMeses() + " MESES";
            }
            if (movProduto.getProduto().getDescricao().equals(descricaoMovProduto)) {
                if (empresa.getTipoGestaoNFSe() == TipoRelatorioDF.COMPETENCIA.getCodigo()) {
                    descricao = movProduto.getProduto().getDescricao() + " " + meses + " Compet. " + movProduto.getMesReferencia();
                } else {
                    descricao = movProduto.getProduto().getDescricao() + " " + meses;
                }
            } else {
                if (empresa.getTipoGestaoNFSe() == TipoRelatorioDF.COMPETENCIA.getCodigo()) {
                    descricao = movProduto.getProduto().getDescricao() + " " + descricaoMovProduto + " " + meses + " Compet. " + movProduto.getMesReferencia();
                } else {
                    descricao = movProduto.getProduto().getDescricao() + " " + descricaoMovProduto + " " + meses;
                }
            }
            if (pessoa != null) {
                if (empresa.isEmitirNomeAlunoNotaFamilia()) {
                    item.put("Descricao", StringUtilities.doRemoverAcentos(descricao + " - " + pessoa.getNomeAbreviado()));
                } else {
                    item.put("Descricao", StringUtilities.doRemoverAcentos(descricao));
                }
            } else {
                item.put("Descricao", StringUtilities.doRemoverAcentos(descricao));
            }
        } else {
            item.put("ValorUnitario", item.getDouble("ValorUnitario") + valorUnitario);
            item.put("Quantidade", item.getInt("Quantidade") + 1);
            itens.put(identificador, item);
        }
        return descricao;
    }

    public class RetornoNFSe {
        private Double valor = 0.0;
        private StringBuilder descricao = new StringBuilder();
        private boolean algumServico = false;
        private List<NFSeEmitidaFormaPagamentoTO> listaPagamentos;

        public Double getValor() {
            return valor;
        }

        public void setValor(Double valor) {
            this.valor = valor;
        }

        public StringBuilder getDescricao() {
            return descricao;
        }

        public void setDescricao(StringBuilder descricao) {
            this.descricao = descricao;
        }

        public boolean isAlgumServico() {
            return algumServico;
        }

        public void setAlgumServico(boolean algumServico) {
            this.algumServico = algumServico;
        }

        public List<NFSeEmitidaFormaPagamentoTO> getListaPagamentos() {
            if (listaPagamentos == null) {
                listaPagamentos = new ArrayList<NFSeEmitidaFormaPagamentoTO>();
            }
            return listaPagamentos;
        }

        public void setListaPagamentos(List<NFSeEmitidaFormaPagamentoTO> listaPagamentos) {
            this.listaPagamentos = listaPagamentos;
        }
    }

    private Date obterDataCompetencia(EmpresaVO empresa, Date dataEmissao, Date dataCompetencia, Date dataEmissaoGestaoNotas) {
        return obterData(empresa, dataEmissao, dataCompetencia, dataEmissaoGestaoNotas, false);
    }

    private Date obterDataEmissao(EmpresaVO empresa, Date dataEmissao, Date dataCompetencia, Date dataEmissaoGestaoNotas) {
        return obterData(empresa, dataEmissao, dataCompetencia, dataEmissaoGestaoNotas, true);
    }

    private Date obterData(EmpresaVO empresa, Date dataEmissao, Date dataCompetencia, Date dataEmissaoGestaoNotas, boolean obterDataEmissao) {

        Date dataEmissaoJSON;
        Date dataCompetenciaJSON;
        if (empresa.isPermiteAlterarDataEmissaoNFSe()) {
            //SE A EMPRESA ESTIVER HABILITADA PARA ALTERAR A DATA DE EMISSAO NO GESTAO DE NOTAS
            if (dataEmissaoGestaoNotas == null) {
                dataEmissaoGestaoNotas = Calendario.hoje();
            }
            dataEmissaoJSON = dataEmissaoGestaoNotas;
            dataCompetenciaJSON = dataEmissaoGestaoNotas;

        } else if ((empresa.getTipoGestaoNFSe() == TipoRelatorioDF.RECEITA.getCodigo()) && empresa.getEmiteNFSEPorDataCompensacao() && dataEmissao != null && dataCompetencia != null) {
            //EMITIR PELA DATA DE COMPENSAÇÃO -- SOMENTE POR RECEITA.
            dataEmissaoJSON = dataEmissao;
            dataCompetenciaJSON = dataCompetencia;
        } else {
            dataEmissaoJSON = Calendario.hoje();
            dataCompetenciaJSON = Calendario.hoje();
        }

        if (Calendario.igual(dataEmissaoJSON, Calendario.hoje())) {
            //verifica se é igual a data atual para poder colocar a hora se for a data atual
            dataEmissaoJSON = Calendario.hoje();
        }

        if (Calendario.igual(dataCompetenciaJSON, Calendario.hoje())) {
            //verifica se é igual a data atual para poder colocar a hora se for a data atual
            dataCompetenciaJSON = Calendario.hoje();
        }

        if (obterDataEmissao) {
            return dataEmissaoJSON;
        } else {
            return dataCompetenciaJSON;
        }
    }

    private void validarInformacoesEnvioDeNota(TipoNotaFiscalEnum notaFiscalTipo, EmpresaVO empresaVO) throws Exception {

        if (notaFiscalTipo.equals(TipoNotaFiscalEnum.NFSE)) {
            //VALIDAR INFORMACOES DE NFSE

            if (!empresaVO.getUsarNFSe()) {
                throw new Exception("Empresa não configurada para gerar " + notaFiscalTipo.getDescricao());
            }

            if (UteisValidacao.emptyNumber(empresaVO.getConfiguracaoNotaFiscalNFSe().getCodigo())) {
                throw new Exception("Empresa não tem configuração de nota fiscal configurada (Configurações da Empresa)!");
            }


        } else if (notaFiscalTipo.equals(TipoNotaFiscalEnum.NFCE)) {
            //VALIDAR INFORMACOES DE NFCE

            if (!empresaVO.isUsarNFCe()) {
                throw new Exception("Empresa não configurada para gerar " + notaFiscalTipo.getDescricao());
            }
            if (UteisValidacao.emptyNumber(empresaVO.getConfiguracaoNotaFiscalNFCe().getCodigo())) {
                throw new Exception("Empresa não tem configuração de nota fiscal configurada (Configurações da Empresa)!");
            }

        }
    }

    private String executaRequisicaoRestNFSe(String metodo, JSONObject jsonObject) throws IOException {
        Map<String, String> header = new HashMap<String, String>();
        header.put("Content-Type", "application/json");
        return ExecuteRequestHttpService.executeHttpRequestNFSe(Uteis.getUrlServiceNFSeRest() + metodo, header, jsonObject.toString(), ExecuteRequestHttpService.METODO_POST, "UTF-8", "UTF-8");
    }

    public StatusNotaEnum retornarStatus(Integer id_rps) {
        try {
            Uteis.logar(null, "Solicitar retornarStatus ID_RPS: " + id_rps);
            JSONObject objJSON = new JSONObject();
            objJSON.put("id_rps", id_rps);

            String retorno = executaRequisicaoRestNFSe(METODO_RETORNARSTATUS, objJSON);

            JSONObject obj = new JSONObject(retorno);
            return StatusNotaEnum.getStatus(obj.getInt("Result"));
        } catch (Exception e) {
            return StatusNotaEnum.NENHUM;
        }
    }

    public String pegarLinkNFSeDoIdExterno(List<Integer> idsReferencia) {
        try {
            Uteis.logar(null, "Solicitar PegarLinkExternoNFSe IDs: " + Arrays.toString(idsReferencia.toArray()));
            JSONArray arrayIds = new JSONArray();
            for (Integer idReferencia : idsReferencia) {
                arrayIds.put(idReferencia.toString());
            }

            JSONObject objJSON = new JSONObject();
            objJSON.put("listaIdExterno", arrayIds);

            return executaRequisicaoRestNFSe(METODO_PEGARCODIGOSRPSDOSIDEXTERNOS, objJSON);
        } catch (Exception e) {
            return "";
        }
    }

    public RetornoEnvioNotaFiscalTO reenviarRPS(JSONObject jsonObject, Integer id_rps) {
        RetornoEnvioNotaFiscalTO retornoEnvioNotaFiscalTO = new RetornoEnvioNotaFiscalTO();
        try {
            Uteis.logar(null, "Solicitar ReenviarRPS ID_RPS: " + id_rps);
            String retorno = executaRequisicaoRestNFSe(METODO_REENVIARRPS, jsonObject);

            JSONObject obj = new JSONObject(retorno);
            retornoEnvioNotaFiscalTO.setMensagem(obj.getString("mensagemRetorno"));
            retornoEnvioNotaFiscalTO.setResultadoEnvio(ResultadoEnvioNFSeEnum.getTipo(obj.getInt("Result")));
        } catch (Exception e) {
            retornoEnvioNotaFiscalTO.setResultadoEnvio(ResultadoEnvioNFSeEnum.ERROINESPERADO);
            retornoEnvioNotaFiscalTO.setMensagem(e.getMessage());

        }
        return retornoEnvioNotaFiscalTO;
    }



    private List<NFSeEmitidaFormaPagamentoTO> obterPagamentoMovProduto(Integer movProduto) {
        List<NFSeEmitidaFormaPagamentoTO> listaFormaPagamento = new ArrayList<NFSeEmitidaFormaPagamentoTO>();
        try {

            String sqlStr = "select \n" +
                    "fp.tipoformapagamento,\n" +
                    "mp.produtospagos \n" +
                    "from movpagamento mp\n" +
                    "inner join formapagamento fp on fp.codigo = mp.formapagamento\n" +
                    "where credito = false and mp.produtospagos like '%|" + movProduto + ",%'";
            Statement stm = con.createStatement();
            ResultSet rs = stm.executeQuery(sqlStr);
            while (rs.next()) {

                String tipoformapagamento = rs.getString("tipoformapagamento");
                String produtosPagos = rs.getString("produtospagos");

                String[] produtos = produtosPagos.split("\\|");
                for (String prod : produtos) {
                    if (UteisValidacao.emptyString(prod) || prod.equals("null")) {
                        continue;
                    }
                    String[] split = prod.split(",");
                    Integer codigoMovProduto = Integer.valueOf(split[0]);
                    if (codigoMovProduto.equals(movProduto)) {
                        Double valorPago = Double.valueOf(split[3]);

                        NFSeEmitidaFormaPagamentoTO novo = new NFSeEmitidaFormaPagamentoTO();
                        novo.setSiglaTipoFormaPagamento(tipoformapagamento);
                        novo.setValor(valorPago);
                        listaFormaPagamento.add(novo);
                    }
                }
            }
        } catch (Exception e) {
            listaFormaPagamento = new ArrayList<NFSeEmitidaFormaPagamentoTO>();
        }
        return listaFormaPagamento;
    }

    private String obterDescricaoTipoFormaPagamento(String sigla) {
        if (sigla == null) {
            return "";
        }
        if (sigla.equals("CA")) {
            return "Cartao de Credito";
        }
        if (sigla.equals("CD")) {
            return "Cartao de Debito";
        }
        if (sigla.equals("CH")) {
            return "Cheque";
        }
        if (sigla.equals("AV")) {
            return "Dinheiro";
        }
        if (sigla.equals("CC")) {
            return "Conta Corrente";
        }
        if (sigla.equals("BB")) {
            return "Boleto Bancario";
        }
        if (sigla.equals("CO")) {
            return "Convenio";
        }
        if (sigla.equals("PD")) {
            return "Pagamento Digital";
        }
        if (sigla.equals("LO")) {
            return "Lote";
        }
        return "";
    }

    private String retornarCodigosFamilia(List<ItemGestaoNotaFamiliaTO> itensFamilia) {
        StringBuilder codigos = new StringBuilder();
        if (UteisValidacao.emptyList(itensFamilia)) {
            return "";
        }
        for (ItemGestaoNotaFamiliaTO obj : itensFamilia) {
            if (obj.getNfSeEmitidaVO() == null || UteisValidacao.emptyNumber(obj.getNfSeEmitidaVO().getCodigo())) {
                continue;
            }
            if (codigos.toString().equals("")) {
                codigos.append(obj.getNfSeEmitidaVO().getCodigo());
            } else {
                codigos.append(",").append(obj.getNfSeEmitidaVO().getCodigo());
            }
        }
        return codigos.toString();
    }

    public void gravarLotePorFamiliaAsync(ConfiguracaoNotaFiscalVO configNotaFiscalVO, List<ItemGestaoNotaFamiliaTO> notasEmitir,
                                          TipoRelatorioDF tipoRelatorioDF, EmpresaVO empresaVO, Date dataEmissaoGestaoNotas, String chave) throws Exception {

        Date d1Enviar = Calendario.hoje();

        if (UteisValidacao.emptyNumber(configNotaFiscalVO.getCodigo())) {
            throw new Exception("Não foi encontrada configuração de emissão para nota em grupo!");
        }

        configNotaFiscalVO = obterConfiguracaoNotaFiscalVO(configNotaFiscalVO.getCodigo());

        JSONObject jsonEnviar = processarItensFamiliaGerarJSON(configNotaFiscalVO, notasEmitir, tipoRelatorioDF, empresaVO, dataEmissaoGestaoNotas, chave);

        RetornoEnvioNotaFiscalTO retornoEnvioNotaFiscalTO = enviarListaLoteComReferenciaAsync(jsonEnviar);

        String codigosNFSeEmitida = retornarCodigosFamilia(notasEmitir);

        if (!retornoEnvioNotaFiscalTO.getResultadoEnvio().equals(ResultadoEnvioNFSeEnum.OK)) {

            for (ItemGestaoNotaFamiliaTO itemGestaoNotaFamiliaTO : notasEmitir) {
                if (itemGestaoNotaFamiliaTO.getNfseemitida()) {
                    String msg = "Nota aguardando envio: " + retornoEnvioNotaFiscalTO.getMensagem();
                    itemGestaoNotaFamiliaTO.setRetorno(msg);
                    itemGestaoNotaFamiliaTO.setSituacaoNotaFiscal(SituacaoNotaFiscalEnum.GERADA);
                }
            }

            NFSeEmitida nfse = new NFSeEmitida(con);
            nfse.atualizarDataEnvioSituacao(null, SituacaoNotaFiscalEnum.GERADA, codigosNFSeEmitida);
            nfse = null;

            throw new Exception(retornoEnvioNotaFiscalTO.getMensagem());

        }

        Date d2Enviar = Calendario.hoje();
        Uteis.logar(null, "Tempo gravarLotePorFamiliaAsync: " + (d2Enviar.getTime() - d1Enviar.getTime()));

    }

    private JSONObject processarItensFamiliaGerarJSON(ConfiguracaoNotaFiscalVO configNotaFiscalVO, List<ItemGestaoNotaFamiliaTO> notasEmitir,
                                                      TipoRelatorioDF tipoRelatorioDF, EmpresaVO empresaVO, Date dataEmissaoGestaoNotas, String chave) throws Exception {
        try {

            if (UteisValidacao.emptyString(chave)) {
                throw new Exception("Chave não informada.");
            }

            Date d1 = Calendario.hoje();

            JSONArray jsonArray = new JSONArray();

            for (ItemGestaoNotaFamiliaTO itemGestaoNotaFamiliaTO : notasEmitir) {
                itemGestaoNotaFamiliaTO.setJsonNotaEnviar(null);

                for (ItemGestaoNotasTO itemNota : itemGestaoNotaFamiliaTO.getReciboPagamentoVOs()) {
                    if (itemNota.isSelecionado()) {
                        if (tipoRelatorioDF.equals(TipoRelatorioDF.FATURAMENTO_DE_CAIXA)) {
                            itemNota.setReciboPagamentoVO(getFacade().getReciboPagamento().consultarPorChavePrimaria(itemNota.getReciboPagamentoVO().getCodigo(), Uteis.NIVELMONTARDADOS_TELACONSULTA));
                            itemNota.getReciboPagamentoVO().setPagamentosDesteRecibo(getFacade().getMovPagamento().consultarPorCodigoRecibo(itemNota.getReciboPagamentoVO().getCodigo(), false, Uteis.NIVELMONTARDADOS_TODOS));
                        }
                    }
                }

                gerarJSONNotaFamilia(configNotaFiscalVO, itemGestaoNotaFamiliaTO, tipoRelatorioDF, dataEmissaoGestaoNotas, chave);

                if (itemGestaoNotaFamiliaTO.getJsonNotaEnviar() != null) {

                    itemGestaoNotaFamiliaTO.setNfseemitida(true);
                    itemGestaoNotaFamiliaTO.setSelecionado(false);
                    itemGestaoNotaFamiliaTO.setSituacaoNotaFiscal(SituacaoNotaFiscalEnum.ENVIADA);
                    itemGestaoNotaFamiliaTO.setRetorno("Dados enviados com sucesso.");

                    Integer nfseEmitida = itemGestaoNotaFamiliaTO.getNfSeEmitidaVO().getCodigo();

                    String urlConfirmacao = Uteis.getUrlAplicacao() + "/retorno/nota?key=" + chave + "&nfseEmitida=" + nfseEmitida + "&idLote=";
                    itemGestaoNotaFamiliaTO.getJsonNotaEnviar().put("urlConfirmacao", urlConfirmacao);

                    NFSeEmitida nfse = new NFSeEmitida(con);
                    nfse.atualizarDataReferenciaJsonEnviar(itemGestaoNotaFamiliaTO.getDataReferenciaItem(), itemGestaoNotaFamiliaTO.getJsonNotaEnviar().toString(), itemGestaoNotaFamiliaTO.getNfSeEmitidaVO().getCodigo());
                    nfse.atualizarDataEnvioSituacao(Calendario.hoje(), SituacaoNotaFiscalEnum.ENVIADA, itemGestaoNotaFamiliaTO.getNfSeEmitidaVO().getCodigo().toString());
                    nfse = null;

                    jsonArray.put(itemGestaoNotaFamiliaTO.getJsonNotaEnviar());

                } else {

                    itemGestaoNotaFamiliaTO.setNfseemitida(false);
                    itemGestaoNotaFamiliaTO.setSelecionado(false);
                    itemGestaoNotaFamiliaTO.setSituacaoNotaFiscal(SituacaoNotaFiscalEnum.ERRO_GERACAO);

                }

            }

            if (jsonArray.length() < 1) {
                throw new Exception("Nenhuma nota selecionada");
            }

            JSONObject jsonObject = new JSONObject();
            jsonObject.put("listaLotes", jsonArray);

            Date d2 = Calendario.hoje();

            Uteis.logar(null, "Solicitar inclusão NFSe CNPJ: " + Formatador.removerMascara(empresaVO.getCNPJ()) + " | processarItensFamiliaGerarJSON | Qtd Notas: " + jsonArray.length() + " | Tempo Montar Dados: " + (d2.getTime() - d1.getTime()));
            return jsonObject;
        } catch (Exception e) {
            Uteis.logar(null, "ERRO! gravarLotePorFamiliaAsync ERRO: " + e.getMessage());
            throw e;
        }
    }

    private void gerarJSONNotaFamilia(ConfiguracaoNotaFiscalVO configNotaFiscalVO, ItemGestaoNotaFamiliaTO itemGestaoNotaFamiliaTO,
                                      TipoRelatorioDF tipoRelatorioDF, Date dataEmissaoGestaoNotas, String chave) {
        try {

            EmpresaVO empresa = obterEmpresaVO(itemGestaoNotaFamiliaTO.getClientePagador().getEmpresa().getCodigo());
            validarInformacoesEnvioDeNota(configNotaFiscalVO.getTipoNotaFiscal(), empresa);

            PessoaVO pessoa = itemGestaoNotaFamiliaTO.getClientePagador().getPessoa();
            Double valorNFSe = 0.0;
            Map<String, JSONObject> itens = new HashMap<String, JSONObject>();
            StringBuilder descricao = new StringBuilder();
            boolean algumServico = false;
            List<NFSeEmitidaFormaPagamentoTO> listaFormaPagamento = new ArrayList<NFSeEmitidaFormaPagamentoTO>();

            for (ItemGestaoNotasTO itemNota : itemGestaoNotaFamiliaTO.getReciboPagamentoVOs()) {
                if (itemNota.isSelecionado()) {
                    RetornoNFSe retornoNFSe = montarNFSeFamilia(itens, itemNota, empresa, tipoRelatorioDF);
                    valorNFSe += retornoNFSe.getValor();
                    algumServico = retornoNFSe.isAlgumServico();
                    descricao.append(retornoNFSe.getDescricao());
                    itemNota.setNfseemitida(true);
                    listaFormaPagamento.addAll(retornoNFSe.getListaPagamentos());
                }
            }

            if (!algumServico) {
                throw new Exception("Nenhum serviço vendido neste recibo.");
            }

            Date dataEmissao = obterDataEmissao(empresa, null, null, dataEmissaoGestaoNotas);
            Date dataCompetencia = obterDataCompetencia(empresa, null, null, dataEmissaoGestaoNotas);

            ConfiguracaoSistema configSistemaDAO = new ConfiguracaoSistema(con);
            String idReferencia = chave + "_" + IDENTIFICADOR_FAMILIA + configSistemaDAO.obterSequencialNotaFamilia();
            JSONObject jsonObject = montarJSONNotaFamlia(configNotaFiscalVO, empresa, pessoa, valorNFSe, itens, descricao.toString(), obterFornecedor(pessoa), "", dataEmissao, dataCompetencia, idReferencia, listaFormaPagamento);

            itemGestaoNotaFamiliaTO.setJsonNotaEnviar(jsonObject);

            gravarNFSeEmitidaFamilia(itemGestaoNotaFamiliaTO, empresa, tipoRelatorioDF, valorNFSe, idReferencia, pessoa.getCodigo(), dataEmissao, dataCompetencia);

            configSistemaDAO.incrementarSequencialNotaFamilia();
        } catch (Exception e) {
            itemGestaoNotaFamiliaTO.setJsonNotaEnviar(null);
            itemGestaoNotaFamiliaTO.setRetorno(e.getMessage());
        }
    }

    private JSONObject montarJSONNotaFamlia(ConfiguracaoNotaFiscalVO configNotaFiscalVO,
                                            EmpresaVO empresa, PessoaVO pessoa, Double valorNFSe, Map<?, JSONObject> itens,
                                            String descricao, FornecedorVO fornecedor, String observacao, Date dataEmissao,
                                            Date dataCompetencia, String iReferencia, List<NFSeEmitidaFormaPagamentoTO> listaFormaPagamento) throws Exception {

        configNotaFiscalVO = obterConfiguracaoNotaFiscalVO(configNotaFiscalVO.getCodigo());

        JSONObject objRPS = new JSONObject();

        objRPS.put("DataEmissao", Uteis.getDataAplicandoFormatacao(dataEmissao, "yyyy-MM-dd HH:mm:ss"));
        objRPS.put("DataCompetencia", Uteis.getDataAplicandoFormatacao(dataCompetencia, "yyyy-MM-dd HH:mm:ss"));
        objRPS.put("InscricaoMunicipal", "");

        String CPFCNPJCons;
        String razaoSocialCons;
        String nomeAluno;
        String inscricaoEstadual = "";
        if (fornecedor == null) {
            boolean usarNomeResponsavelNota = new ConfiguracaoSistema(con).usarNomeResponsavelNota();

            if (pessoa.isEmitirNomeTerceiro()) {

                if (UteisValidacao.emptyString(pessoa.getNomeTerceiro())) {
                    throw new ConsistirException(String.format("Cliente %s não possui nome do terceiro cadastrado.", pessoa.getNome()));
                } else if (UteisValidacao.emptyString(pessoa.getCpfCNPJTerceiro())) {
                    throw new ConsistirException(String.format("Cliente %s não possui CPF do terceiro cadastrado.", pessoa.getNome()));
                }

                CPFCNPJCons = Formatador.removerMascara(pessoa.getCpfCNPJTerceiro());
                razaoSocialCons = pessoa.getNomeTerceiro();
                nomeAluno = pessoa.getNome();

            } else if (pessoa.getPessoaJuridica()) {

                if (UteisValidacao.emptyString(pessoa.getCnpj())) {
                    throw new ConsistirException(String.format("Cliente %s não possui CNPJ cadastrado.", pessoa.getNome()));
                }
                if (!UteisValidacao.emptyString(pessoa.getInscEstadual())) {
                    inscricaoEstadual = Formatador.removerMascara(pessoa.getInscEstadual());
                }

                CPFCNPJCons = Formatador.removerMascara(pessoa.getCnpj());
                razaoSocialCons = pessoa.getNome();
                nomeAluno = pessoa.getNome();

            } else if (pessoa.getDataNasc() != null && usarNomeResponsavelNota && (Uteis.nrDiasEntreDatas(pessoa.getDataNasc(), Calendario.hoje()) < 18 * 365 + 4)) {
                ClienteVO clienteVO = getFacade().getCliente().consultarPorCodigoPessoa(pessoa.getCodigo(), Uteis.NIVELMONTARDADOS_DADOSENTIDADESUBORDINADAS);
                if (clienteVO.getPessoaResponsavel() != null && !clienteVO.getPessoaResponsavel().getCodigo().equals(0)) {
                    CPFCNPJCons = Formatador.removerMascara(clienteVO.getPessoaResponsavel().getCfp());
                    razaoSocialCons = clienteVO.getPessoaResponsavel().getNome();
                    nomeAluno = clienteVO.getPessoa().getNome();
                } else if (!clienteVO.getPessoa().getCfp().isEmpty() && clienteVO.getPessoa().isEmitirNotaNomeMae() && !UteisValidacao.emptyString(clienteVO.getPessoa().getNomeMae())) {
                    CPFCNPJCons = Formatador.removerMascara(clienteVO.getPessoa().getCfp());
                    razaoSocialCons = clienteVO.getPessoa().getNomeMae();
                    nomeAluno = clienteVO.getPessoa().getNome();
                } else if (!clienteVO.getPessoa().getCfp().isEmpty() && !clienteVO.getPessoa().isEmitirNotaNomeMae() && !UteisValidacao.emptyString(clienteVO.getPessoa().getNomePai())) {
                    CPFCNPJCons = Formatador.removerMascara(clienteVO.getPessoa().getCfp());
                    razaoSocialCons = clienteVO.getPessoa().getNomePai();
                    nomeAluno = clienteVO.getPessoa().getNome();
                } else if (UteisValidacao.emptyString(clienteVO.getPessoa().getCfp())) {
                    throw new ConsistirException(String.format("Cliente %s não possui CPF cadastrado.", pessoa.getNome()));
                } else {
                    throw new ConsistirException(String.format("Cliente %s não possui responsável cadastrado.", pessoa.getNome()));
                }
            } else {
                if (UteisValidacao.emptyString(pessoa.getCfp())) {
                    throw new ConsistirException(String.format("Cliente %s não possui CPF cadastrado.", pessoa.getNome()));
                }
                CPFCNPJCons = Formatador.removerMascara(pessoa.getCfp());
                razaoSocialCons = pessoa.getNome();
                nomeAluno = pessoa.getNome();
            }
        } else {
            if (UteisValidacao.emptyString(fornecedor.getCnpj())) {
                throw new ConsistirException(String.format("Fornecedor %s não possui CNPJ cadastrado.", pessoa.getNome()));
            }
            CPFCNPJCons = Formatador.removerMascara(fornecedor.getCnpj());
            razaoSocialCons = fornecedor.getNomeApresentar();
            nomeAluno = fornecedor.getNomeApresentar();
        }

        objRPS.put("RazaoSocial", StringUtilities.doRemoverAcentos(razaoSocialCons));
        objRPS.put("CPFCNPJ", CPFCNPJCons);
        objRPS.put("NomeAluno", StringUtilities.doRemoverAcentos(nomeAluno));
        objRPS.put("NatOp", configNotaFiscalVO.getNaturezaOperacao());
        objRPS.put("SerieRPS", configNotaFiscalVO.getSerie());
        objRPS.put("TipoBairro", " ");
        objRPS.put("TipoLogradouro", " ");
        objRPS.put("AliquotaAtividade", configNotaFiscalVO.getIss() / 100.0);
        objRPS.put("AliquotaPIS", configNotaFiscalVO.getPis());
        objRPS.put("AliquotaCOFINS", configNotaFiscalVO.getCofins());

        Double valorISS = valorNFSe * (configNotaFiscalVO.getIss() / 100.0);
        objRPS.put("ISSRetido", configNotaFiscalVO.isIssRetido() ? 1 : 2);
        objRPS.put("ValorISSRetido", valorISS);

        Double valorPIS = valorNFSe * (configNotaFiscalVO.getPis() / 100.0);
        objRPS.put("ValorPIS", valorPIS);

        Double valorCOFINS = valorNFSe * (configNotaFiscalVO.getCofins() / 100.0);
        objRPS.put("ValorCOFINS", valorCOFINS);

        Double valorIRRF = 0.0;
        if (valorNFSe >= configNotaFiscalVO.getValorMinimoIRRF()) {
            valorIRRF = valorNFSe * (configNotaFiscalVO.getIrrf() / 100.0);
        }
        objRPS.put("ValorIRRFR", valorIRRF);


        List<NFSeEmitidaFormaPagamentoTO> listaFormaPagamentoFinal = new ArrayList<NFSeEmitidaFormaPagamentoTO>();
        if (!UteisValidacao.emptyList(listaFormaPagamento)) {

            Map<String, Double> map = new HashMap<String, Double>();

            for (NFSeEmitidaFormaPagamentoTO obj : listaFormaPagamento) {

                if(map.containsKey(obj.getSiglaTipoFormaPagamento())) {
                    map.put(obj.getSiglaTipoFormaPagamento(), map.get(obj.getSiglaTipoFormaPagamento()) + obj.getValor());
                } else {
                    map.put(obj.getSiglaTipoFormaPagamento(), obj.getValor());
                }
            }

            for (String key : map.keySet()){
                NFSeEmitidaFormaPagamentoTO novo = new NFSeEmitidaFormaPagamentoTO();
                novo.setSiglaTipoFormaPagamento(key);
                novo.setValor(map.get(key));
                listaFormaPagamentoFinal.add(novo);
            }
        }

        JSONArray formasPagamento = new JSONArray();
        for (NFSeEmitidaFormaPagamentoTO obj : listaFormaPagamentoFinal) {
            JSONObject objForma = new JSONObject();
            objForma.put("formaPagamento", StringUtilities.doRemoverAcentos(obterDescricaoTipoFormaPagamento(obj.getSiglaTipoFormaPagamento())));
            objForma.put("valor", obj.getValor());
            formasPagamento.put(objForma);
        }
        objRPS.put("formasPagamento", formasPagamento);


        //VERIFICAR LUIZ FELIPE
        Collection<JSONObject> itensCollection = itens.values();
        JSONArray itensArray = new JSONArray();
        if (!itensCollection.isEmpty()) {
            itensArray = new JSONArray(itensCollection);
        }

        EnderecoVO endereco;
        if (configNotaFiscalVO.isEnderecoObrigatorio() && pessoa.getEnderecoVOs().isEmpty()) {
            if (fornecedor == null) {
                throw new ConsistirException(String.format("Cliente %s não possui endereço cadastrado.", pessoa.getNome()));
            } else {
                throw new ConsistirException(String.format("Fornecedor %s não possui endereço cadastrado.", pessoa.getNome()));
            }
        } else if (!configNotaFiscalVO.isEnderecoObrigatorio() && pessoa.getEnderecoVOs().isEmpty()) {
            endereco = new EnderecoVO();
        } else {
            endereco = pessoa.getEnderecoVOs().get(0);
        }

        objRPS.put("Logradouro", StringUtilities.doRemoverAcentos(endereco.getEndereco()));
        objRPS.put("NumeroEndereco", endereco.getNumero());
        objRPS.put("ComplementoEndereco", StringUtilities.doRemoverAcentos(endereco.getComplemento()));
        objRPS.put("Bairro", StringUtilities.doRemoverAcentos(endereco.getBairro()));

        String uf = "";
        String cidade = "";
        String cfop = "";
        if ((empresa.isEnviarNotaCidadeEmpresa() && fornecedor == null) || UteisValidacao.emptyString(pessoa.getEstadoVO().getSigla())) {
            uf = empresa.getEstado().getSigla();
            cidade = empresa.getCidade().getNomeSemAcento();
        } else {
            uf = pessoa.getEstadoVO().getSigla();
            cidade = pessoa.getCidade().getNomeSemAcento();

            if (!pessoa.getEstadoVO().getSigla().equals(empresa.getEstado().getSigla())) {
                cfop = "6933"; //se for de outro estado o CFOP é outro
            }
        }

        objRPS.put("UF", uf);
        objRPS.put("Cidade", StringUtilities.doRemoverAcentos(cidade));
        objRPS.put("CFOP", cfop);
        objRPS.put("UFPrestacao", empresa.getEstado().getSigla());
        objRPS.put("CidadePrestacao", empresa.getCidade().getNomeSemAcento());
        objRPS.put("CEP", endereco.getCep());

        EmailVO emailVO;
        if (configNotaFiscalVO.isEmailObrigatorio() && pessoa.getEmailVOs().isEmpty()) {
            if (fornecedor == null) {
                throw new ConsistirException(String.format("Cliente %s não possui email cadastrado.", pessoa.getNome()));
            } else {
                throw new ConsistirException(String.format("Fornecedor %s não possui email cadastrado.", pessoa.getNome()));
            }
        } else if (!configNotaFiscalVO.isEmailObrigatorio() && pessoa.getEmailVOs().isEmpty()) {
            emailVO = new EmailVO();
        } else {
            emailVO = pessoa.getEmailVOs().get(0);
        }

        objRPS.put("Email", emailVO.getEmail());
        objRPS.put("EnviarEmail", !emailVO.getBloqueadoBounce());

        TelefoneVO telefoneVO;
        if (pessoa.getTelefoneVOs().isEmpty()) {
            telefoneVO = new TelefoneVO();
        } else {
            telefoneVO = pessoa.getTelefoneVOs().get(0);
        }

        objRPS.put("TelefoneCons", telefoneVO.getNumero());
        objRPS.put("ValorServicos", valorNFSe);
        objRPS.put("ItemListaServico", configNotaFiscalVO.getCodListaServico());
        objRPS.put("CodigoTributacaoMunicipio", configNotaFiscalVO.getCodTributacaoMunicipal());

        String codigoCnae = "";
        if (configNotaFiscalVO.getCnae() != null && !configNotaFiscalVO.getCnae().trim().equals("")) {
            codigoCnae = configNotaFiscalVO.getCnae();
        }
        objRPS.put("CodigoCnae", codigoCnae);

        descricao = descricao.replace("[VALOR_NFSE]", Formatador.formatarValorMonetario(valorNFSe));
        descricao = descricao.replace("[VALOR_ISS]", Formatador.formatarValorMonetario(valorISS));
        descricao = descricao.replace("[VALOR_PIS]", Formatador.formatarValorMonetario(valorPIS));
        descricao = descricao.replace("[VALOR_COFINS]", Formatador.formatarValorMonetario(valorCOFINS));
        descricao = descricao.replace("[VALOR_TOTAL_IMPOSTO]", Formatador.formatarValorMonetario(valorISS + valorCOFINS + valorPIS));

        Double valorTributos = valorNFSe * (configNotaFiscalVO.getPercentualAproximadoTributos() / 100.0);
        descricao = descricao.replace("[TRIBUTOS_VALOR]", Formatador.formatarValorMonetario(valorTributos));
        descricao = descricao.replace("[TRIBUTOS_PERCENTUAL]", Formatador.formatarValorMonetarioSemMoeda(configNotaFiscalVO.getPercentualAproximadoTributos()) + "%");

        descricao = descricao.replace("[VALOR_FEDERAL]", Formatador.formatarValorMonetario(valorNFSe * (configNotaFiscalVO.getPercentualFederal() / 100.0)));
        descricao = descricao.replace("[VALOR_ESTADUAL]", Formatador.formatarValorMonetario(valorNFSe * (configNotaFiscalVO.getPercentualEstadual() / 100.0)));
        descricao = descricao.replace("[VALOR_MUNICIPAL]", Formatador.formatarValorMonetario(valorNFSe * (configNotaFiscalVO.getPercentualMunicipal() / 100.0)));

        objRPS.put("Descricao", StringUtilities.doRemoverAcentos(descricao.replaceFirst("/", "")));

        if (observacao != null) {
            observacao = observacao.replace("[VALOR_NFSE]", Formatador.formatarValorMonetario(valorNFSe));
            observacao = observacao.replace("[VALOR_ISS]", Formatador.formatarValorMonetario(valorISS));
            observacao = observacao.replace("[VALOR_PIS]", Formatador.formatarValorMonetario(valorPIS));
            observacao = observacao.replace("[VALOR_COFINS]", Formatador.formatarValorMonetario(valorCOFINS));
            observacao = observacao.replace("[VALOR_TOTAL_IMPOSTO]", Formatador.formatarValorMonetario(valorISS + valorCOFINS + valorPIS));
            observacao = observacao.replace("[TRIBUTOS_VALOR]", Formatador.formatarValorMonetario(valorTributos));
            observacao = observacao.replace("[TRIBUTOS_PERCENTUAL]", Formatador.formatarValorMonetarioSemMoeda(configNotaFiscalVO.getPercentualAproximadoTributos()) + "%");
            observacao = observacao.replace("[VALOR_FEDERAL]", Formatador.formatarValorMonetario(valorNFSe * (configNotaFiscalVO.getPercentualFederal() / 100.0)));
            observacao = observacao.replace("[VALOR_ESTADUAL]", Formatador.formatarValorMonetario(valorNFSe * (configNotaFiscalVO.getPercentualEstadual() / 100.0)));
            observacao = observacao.replace("[VALOR_MUNICIPAL]", Formatador.formatarValorMonetario(valorNFSe * (configNotaFiscalVO.getPercentualMunicipal() / 100.0)));
        }

        objRPS.put("Observacao", StringUtilities.doRemoverAcentos(observacao));
        objRPS.put("ExigibilidadeISS", configNotaFiscalVO.getExigibilidadeISS().getId());

        String cfdf = "";
        if (fornecedor != null) {
            inscricaoEstadual = Formatador.removerMascara(fornecedor.getInscricaoEstadual());
            cfdf = Formatador.removerMascara(fornecedor.getCfdf());
        }

        objRPS.put("InscricaoEstadual", inscricaoEstadual);
        objRPS.put("CFDF", cfdf);

        if (itensArray.length() == 0) {
            JSONObject item = new JSONObject();
            item.put("ValorUnitario", valorNFSe);
            item.put("Quantidade", 1);
            item.put("Descricao", "PLANO");
            item.put("Tributavel", true);
            item.put("NCM", "");
            itensArray.put(item);
        }
        objRPS.put("Itens", itensArray);

        objRPS.put("InformacaoFisco", "");
        objRPS.put("IdRPSAnterior", 0);
        objRPS.put("CodigoAtividade", "");
        objRPS.put("ValorINSS", 0);
        objRPS.put("ValorIR", 0);
        objRPS.put("ValorCSLL", 0);
        objRPS.put("AliquotaINSS", 0);
        objRPS.put("AliquotaIR", 0);
        objRPS.put("AliquotaCSLL", 0);
        objRPS.put("ValorDeducoes", 0);
        objRPS.put("OutrasRetencoes", 0);
        objRPS.put("DescontoIncondicionado", 0);
        objRPS.put("DescontoCondicionado", 0);

        JSONArray arrayRPS = new JSONArray();
        arrayRPS.put(objRPS);

        JSONObject objRetorno = new JSONObject();
        objRetorno.put("CPFCNPJ", Formatador.removerMascara(empresa.getCNPJ()));
        objRetorno.put("referencia", iReferencia);
        objRetorno.put("RPS", arrayRPS);
        return objRetorno;
    }

    private void gravarNFSeEmitidaFamilia(ItemGestaoNotaFamiliaTO itemGestaoNotaFamiliaTO, EmpresaVO empresa, TipoRelatorioDF tipoRelatorioDF, Double valor, String idReferencia, Integer pessoa, Date dataEmissao, Date dataCompetencia) throws Exception {
        NFSeEmitida nfseDao = new NFSeEmitida(con);
        ReciboPagamento reciboPagamentoDao = new ReciboPagamento(con);
        NFSeEmitidaVO nfSeEmitidaVO = null;
        MovPagamento movPagamentoDao = new MovPagamento(con);

        for (ItemGestaoNotasTO itemNota : itemGestaoNotaFamiliaTO.getReciboPagamentoVOs()) {
            if (itemNota.isSelecionado()) {
                nfSeEmitidaVO = null;
                if ((tipoRelatorioDF == TipoRelatorioDF.COMPETENCIA) || (tipoRelatorioDF == TipoRelatorioDF.FATURAMENTO)) {
                    if (empresa.getTipoProdutoEmissaoNFSe().contains(itemNota.getMovProdutoVO().getProduto().getTipoProduto())) {
                        nfSeEmitidaVO = nfseDao.consultaPorProduto(itemNota.getMovProdutoVO().getCodigo());
                    } else {
                        continue;
                    }
                } else if (tipoRelatorioDF == TipoRelatorioDF.FATURAMENTO_DE_CAIXA) {
                    reciboPagamentoDao.gravarNFSEEmitida(itemNota.getReciboPagamentoVO().getCodigo());
                    itemNota.getReciboPagamentoVO().setNfseEmitida(true);
                    nfSeEmitidaVO = nfseDao.consultarPorRecibo(itemNota.getReciboPagamentoVO().getCodigo());
                } else if (tipoRelatorioDF == TipoRelatorioDF.RECEITA) {
                    if (itemNota.getChequeVO() != null) {
                        nfSeEmitidaVO = nfseDao.consultaPorCheque(itemNota.getChequeVO().getObterTodosChequesComposicao());
                    } else if (itemNota.getCartaoCreditoVO() != null) {
                        nfSeEmitidaVO = nfseDao.consultaPorCartao(itemNota.getCartaoCreditoVO().getObterTodosCartoesComposicao());
                    } else if (itemNota.getMovPagamentoVO() != null) {
                        nfSeEmitidaVO = nfseDao.consultaPorPagamento(itemNota.getMovPagamentoVO().getCodigo());
                    }
                }

                if (nfSeEmitidaVO == null) {
                    nfSeEmitidaVO = new NFSeEmitidaVO();
                    if ((tipoRelatorioDF == TipoRelatorioDF.COMPETENCIA) || (tipoRelatorioDF == TipoRelatorioDF.FATURAMENTO)) {
                        nfSeEmitidaVO.setMovProdutoVO(itemNota.getMovProdutoVO());
                        nfSeEmitidaVO.setContrato(itemNota.getMovProdutoVO().getContrato());
                    } else if (tipoRelatorioDF == TipoRelatorioDF.FATURAMENTO_DE_CAIXA) {
                        nfSeEmitidaVO.setRecibo(itemNota.getReciboPagamentoVO());
                        nfSeEmitidaVO.setContrato(itemNota.getReciboPagamentoVO().getContrato());
                    } else if (tipoRelatorioDF == TipoRelatorioDF.RECEITA) {
                        if (itemNota.getChequeVO() != null) {
                            MovPagamentoVO movPagamentoVO = movPagamentoDao.consultarPorChavePrimaria(itemNota.getChequeVO().getMovPagamento(), Uteis.NIVELMONTARDADOS_DADOSBASICOS);
                            nfSeEmitidaVO.setCheque(itemNota.getChequeVO());
                            nfSeEmitidaVO.setContrato(movPagamentoVO.getReciboPagamento().getContrato());
                        } else if (itemNota.getCartaoCreditoVO() != null) {
                            nfSeEmitidaVO.setCartaoCredito(itemNota.getCartaoCreditoVO());
                            nfSeEmitidaVO.setContrato(itemNota.getCartaoCreditoVO().getMovpagamento().getReciboPagamento().getContrato());
                        } else if (itemNota.getMovPagamentoVO() != null) {
                            nfSeEmitidaVO.setMovPagamento(itemNota.getMovPagamentoVO());
                            if (!UtilReflection.objetoMaiorQueZero(itemNota, "getMovPagamentoVO().getReciboPagamento().getContrato().getCodigo()")) {
                                itemNota.getMovPagamentoVO().setReciboPagamento(getFacade().getReciboPagamento().consultarPorCodigoMovPagamento(itemNota.getMovPagamentoVO().getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS));
                            }
                            nfSeEmitidaVO.setContrato(itemNota.getMovPagamentoVO().getReciboPagamento().getContrato());
                        }
                    }

                    nfSeEmitidaVO.setIdRps(null);
                    nfSeEmitidaVO.setValor(valor);
                    nfSeEmitidaVO.setIdReferencia(idReferencia);
                    nfSeEmitidaVO.setDataRegistro(Calendario.hoje());
                    nfSeEmitidaVO.setDataEmissao(dataEmissao);
                    nfSeEmitidaVO.setPessoa(pessoa);
                    nfSeEmitidaVO.setEmpresa(empresa.getCodigo());
                    nfSeEmitidaVO.setNotaFamilia(true);
                    if (itemGestaoNotaFamiliaTO.getSituacaoNotaFiscal() == null){
                        nfSeEmitidaVO.setSituacaoNotaFiscal(SituacaoNotaFiscalEnum.NENHUM);
                    }else {
                        nfSeEmitidaVO.setSituacaoNotaFiscal(itemGestaoNotaFamiliaTO.getSituacaoNotaFiscal());
                    }
                    nfseDao.incluir(nfSeEmitidaVO);
                }

                itemGestaoNotaFamiliaTO.getNfSeEmitidaLista().add(nfSeEmitidaVO);
                itemGestaoNotaFamiliaTO.setNfSeEmitidaVO(nfSeEmitidaVO);

            }
        }
        nfseDao = null;
        reciboPagamentoDao = null;
        movPagamentoDao = null;

    }

    public RetornoEnvioNotaFiscalTO inutilizarNFe(JSONObject jsonObject) {
        RetornoEnvioNotaFiscalTO retornoEnvioNotaFiscalTO = new RetornoEnvioNotaFiscalTO();
        try {
            Uteis.logar(null, "Solicitar inutilizarNFe: " + jsonObject);
            String retorno = executaRequisicaoRestNFSe(METODO_INUTILIZARNUMEROSNFE, jsonObject);

            JSONObject obj = new JSONObject(retorno);
            retornoEnvioNotaFiscalTO.setMensagem(obj.getString("mensagemRetorno"));
            retornoEnvioNotaFiscalTO.setResultadoEnvio(ResultadoEnvioNFSeEnum.getTipo(obj.getInt("Result")));
        } catch (Exception e) {
            retornoEnvioNotaFiscalTO.setResultadoEnvio(ResultadoEnvioNFSeEnum.ERROINESPERADO);
            retornoEnvioNotaFiscalTO.setMensagem(e.getMessage());

        }
        return retornoEnvioNotaFiscalTO;
    }

    private RetornoEnvioNotaFiscalTO enviarListaLoteComReferenciaAsync(JSONObject jsonObject) {
        Date d1Enviar = Calendario.hoje();
        RetornoEnvioNotaFiscalTO retornoEnvioNotaFiscalTO = new RetornoEnvioNotaFiscalTO();
        try {
            String retorno = executaRequisicaoRestNFSe(METODO_GRAVARLISTALOTECOMREFERENCIAASYNC, jsonObject);

            JSONObject obj = new JSONObject(retorno);
            retornoEnvioNotaFiscalTO.setIdLote(obj.getInt("Result"));
            retornoEnvioNotaFiscalTO.setMensagem(obj.getString("mensagemRetorno"));
            retornoEnvioNotaFiscalTO.setResultadoEnvio(ResultadoEnvioNFSeEnum.getTipo(obj.getInt("resultado")));
        } catch (Exception e) {
            retornoEnvioNotaFiscalTO.setResultadoEnvio(ResultadoEnvioNFSeEnum.ERROINESPERADO);
            retornoEnvioNotaFiscalTO.setMensagem(e.getMessage());

        }
        Date d2Enviar = Calendario.hoje();
        Uteis.logar(null, "Tempo Enviar Notas: " + (d2Enviar.getTime() - d1Enviar.getTime()));
        return retornoEnvioNotaFiscalTO;
    }

}

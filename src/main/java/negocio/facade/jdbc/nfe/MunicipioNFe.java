package negocio.facade.jdbc.nfe;

import negocio.comuns.nfe.MunicipioNFeVO;
import negocio.comuns.utilitarias.ConsistirException;
import negocio.facade.jdbc.arquitetura.SuperEntidade;
import negocio.interfaces.nfe.MunicipioNFeInterfaceFacade;

import javax.faces.model.SelectItem;
import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.List;

public class MunicipioNFe extends SuperEntidade implements MunicipioNFeInterfaceFacade {

    public MunicipioNFe() throws Exception {
        super();
    }

    public MunicipioNFe(Connection con) throws Exception {
        super(con);
    }

    @Override
    public MunicipioNFeVO novo() throws Exception {
        incluir(getIdEntidade());
        return new MunicipioNFeVO();
    }

    @Override
    public void incluir(MunicipioNFeVO obj) throws Exception {
        try {
            incluir(getIdEntidade());
            con.setAutoCommit(false);
            MunicipioNFeVO.validarDados(obj);

            String sql = "INSERT INTO Cidade(Id_Cidade, Nome, UF, CodSIAFI, CodIBGE, TipoLayout)"
                    + " VALUES (?, ?, ?, ?, ?, ?)";

            PreparedStatement sqlInserir = con.prepareStatement(sql);
            int i = 0;
            sqlInserir.setInt(++i, obj.getId_Municipio());
            sqlInserir.setString(++i, obj.getNome());
            sqlInserir.setString(++i, obj.getUf());
            sqlInserir.setInt(++i, obj.getCodSIAFI());
            sqlInserir.setInt(++i, obj.getCodIBGE());
            sqlInserir.setInt(++i, obj.getCodIBGE());

            sqlInserir.execute();
            obj.setNovoObj(true);
            con.commit();
        } catch (Exception e) {
            con.rollback();
            con.setAutoCommit(true);
            throw e;
        } finally {
            con.setAutoCommit(true);
        }
    }

    @Override
    public void alterar(MunicipioNFeVO obj) throws Exception {
        try {
            con.setAutoCommit(false);
            alterar(getIdEntidade());

            MunicipioNFeVO.validarDados(obj);

            String sql = "UPDATE Cidade set Nome=?, UF=?, CodSIAFI=?, CodIBGE=?, TipoLayout = ? WHERE (Id_Cidade=?)";
            PreparedStatement sqlAlterar = con.prepareStatement(sql);
            int i = 0;
            sqlAlterar.setString(++i, obj.getNome());
            sqlAlterar.setString(++i, obj.getUf());
            sqlAlterar.setInt(++i, obj.getCodSIAFI());
            sqlAlterar.setInt(++i, obj.getCodIBGE());
            sqlAlterar.setInt(++i, obj.getTipoLayout());
            sqlAlterar.setInt(++i, obj.getId_Municipio());
            sqlAlterar.execute();

            con.commit();
        } catch (Exception e) {
            con.rollback();
            con.setAutoCommit(true);
            throw e;
        } finally {
            con.setAutoCommit(true);
        }
    }

    @Override
    public void excluir(MunicipioNFeVO obj) throws Exception {
        try {
            con.setAutoCommit(false);
            excluir(getIdEntidade());
            String sql = "DELETE FROM Cidade WHERE (Id_Cidade = ?)";
            PreparedStatement sqlExcluir = con.prepareStatement(sql);
            sqlExcluir.setInt(1, obj.getId_Municipio());
            sqlExcluir.execute();
            con.commit();
        } catch (Exception e) {
            con.rollback();
            con.setAutoCommit(true);
            throw e;
        } finally {
            con.setAutoCommit(true);
        }
    }

    @Override
    public MunicipioNFeVO consultarPorChavePrimaria(Integer codigo) throws Exception {
        consultar(getIdEntidade(), false);
        String sql = "SELECT * FROM Cidade WHERE Id_Cidade = ?";
        PreparedStatement sqlConsultar = con.prepareStatement(sql);
        sqlConsultar.setInt(1, codigo);
        ResultSet tabelaResultado = sqlConsultar.executeQuery();
        if (!tabelaResultado.next()) {
            return new MunicipioNFeVO();
        }
        return (montarDados(tabelaResultado));
    }

    private MunicipioNFeVO montarDados(ResultSet dadosSQL) throws SQLException {
        MunicipioNFeVO obj = new MunicipioNFeVO();
        obj.setId_Municipio(dadosSQL.getInt("Id_Cidade"));
        obj.setNome(dadosSQL.getString("Nome"));
        obj.setUf(dadosSQL.getString("Uf"));
        obj.setCodIBGE(dadosSQL.getInt("CodIBGE"));
        obj.setCodSIAFI(dadosSQL.getInt("CodSIAFI"));
        obj.setTipoLayout(dadosSQL.getInt("TipoLayout"));
        obj.setHorasCancelamento(dadosSQL.getInt("HorasCancelamento"));
        obj.setCancelarAteFinalMes(dadosSQL.getBoolean("CancelarAteFinalMes"));
        obj.setRequerCertificado(dadosSQL.getBoolean("RequerCertificado"));
        return obj;
    }

    public List<SelectItem> obtenhaMunicipiosHomologados() throws Exception {
        List<SelectItem> municipios = new ArrayList<SelectItem>();

        String sql = "SELECT * FROM Cidade WHERE Homologada = 1 ORDER BY nome";
        PreparedStatement sqlConsultar = con.prepareStatement(sql);

        ResultSet tabelaResultado = sqlConsultar.executeQuery();

        while (tabelaResultado.next()) {
            municipios.add(new SelectItem(tabelaResultado.getInt("Id_Cidade"), tabelaResultado.getString("Nome")));
        }

        return municipios;
    }

    public List<SelectItem> obtenhaMunicipios(String estadoUF) throws Exception {
        List<SelectItem> municipios = new ArrayList<SelectItem>();
        String sql = "SELECT * FROM Cidade WHERE uf = '"+estadoUF+"' ORDER BY nome";
        PreparedStatement sqlConsultar = con.prepareStatement(sql);
        ResultSet tabelaResultado = sqlConsultar.executeQuery();
        while (tabelaResultado.next()) {
            municipios.add(new SelectItem(tabelaResultado.getInt("Id_Cidade"), tabelaResultado.getString("Nome")));
        }
        return municipios;
    }
}

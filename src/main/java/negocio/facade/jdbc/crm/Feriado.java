package negocio.facade.jdbc.crm;

import negocio.comuns.basico.CidadeVO;

import java.sql.*;
import java.util.*;
import java.util.Date;

import negocio.facade.jdbc.arquitetura.*;

import br.com.pactosolucoes.comuns.util.Declaracao;
import negocio.comuns.basico.EmpresaVO;
import negocio.comuns.basico.EstadoVO;
import negocio.comuns.basico.PaisVO;
import negocio.comuns.crm.FeriadoVO;
import negocio.comuns.utilitarias.*;
import negocio.facade.jdbc.basico.Cidade;
import negocio.facade.jdbc.basico.Estado;
import negocio.facade.jdbc.basico.Pais;
import negocio.interfaces.crm.FeriadoInterfaceFacade;

/**
 * Classe de persistência que encapsula todas as operações de manipulação dos
 * dados da classe <code>FeriadoVO</code>. Responsável por implementar operações
 * como incluir, alterar, excluir e consultar pertinentes a classe
 * <code>FeriadoVO</code>. Encapsula toda a interação com o banco de dados.
 * 
 * @see FeriadoVO
 * @see SuperEntidade
 */
public class Feriado extends SuperEntidade implements FeriadoInterfaceFacade {

	protected static String idEntidade;

	public Feriado() throws Exception {
		super();
		setIdEntidade("Feriado");
	}

	public Feriado(Connection con) throws Exception {
		super(con);
	}

	/**
	 * Operação responsável por retornar um novo objeto da classe
	 * <code>FeriadoVO</code>.
	 */
	public FeriadoVO novo() throws Exception {
		incluirCRM(getIdEntidade());
		FeriadoVO obj = new FeriadoVO();
		return obj;
	}

	/**
	 * Operação responsável por incluir no banco de dados um objeto da classe
	 * <code>FeriadoVO</code>. Primeiramente valida os dados (
	 * <code>validarDados</code>) do objeto. Verifica a conexão com o banco de
	 * dados e a permissão do usuário para realizar esta operacão na entidade.
	 * Isto, através da operação <code>incluir</code> da superclasse.
	 * 
	 * @param obj
	 *            Objeto da classe <code>FeriadoVO</code> que será gravado no
	 *            banco de dados.
	 * @exception Exception
	 *                Caso haja problemas de conexão, restrição de acesso ou
	 *                validação de dados.
	 */
	public void incluir(FeriadoVO obj) throws Exception {
		FeriadoVO.validarDados(obj);
		incluirCRM(getIdEntidade());
		obj.realizarUpperCaseDados();
		String sql = "INSERT INTO Feriado( descricao, dia, mes, nacional, estadual, estado, cidade, naoRecorrente, pais ) VALUES ( ?, ?, ?, ?, ?, ?, ?, ?, ? )";
		try (PreparedStatement sqlInserir = con.prepareStatement(sql)) {
			sqlInserir.setString(1, obj.getDescricao());
			sqlInserir.setDate(2, Uteis.getDataJDBC(obj.getDia()));
			sqlInserir.setString(3, obj.getMes());
			sqlInserir.setBoolean(4, obj.isNacional().booleanValue());
			sqlInserir.setBoolean(5, obj.isEstadual().booleanValue());
			if (obj.getEstado().getCodigo().intValue() != 0) {
				sqlInserir.setInt(6, obj.getEstado().getCodigo().intValue());
			} else {
				sqlInserir.setNull(6, 0);
			}
			if (obj.getCidade().getCodigo().intValue() != 0) {
				sqlInserir.setInt(7, obj.getCidade().getCodigo().intValue());
			} else {
				sqlInserir.setNull(7, 0);
			}
			sqlInserir.setBoolean(8, obj.isNaoRecorrente().booleanValue());
			if (obj.getPais().getCodigo().intValue() != 0) {
				sqlInserir.setInt(9, obj.getPais().getCodigo().intValue());
			} else {
				sqlInserir.setNull(9, 0);
			}
			sqlInserir.execute();
		}
		obj.setCodigo(obterValorChavePrimariaCodigo());
		obj.setNovoObj(new Boolean(false));
	}

	/**
	 * Operação responsável por alterar no BD os dados de um objeto da classe
	 * <code>FeriadoVO</code>. Sempre utiliza a chave primária da classe como
	 * atributo para localização do registro a ser alterado. Primeiramente
	 * valida os dados (<code>validarDados</code>) do objeto. Verifica a conexão
	 * com o banco de dados e a permissão do usuário para realizar esta operacão
	 * na entidade. Isto, através da operação <code>alterar</code> da
	 * superclasse.
	 * 
	 * @param obj
	 *            Objeto da classe <code>FeriadoVO</code> que será alterada no
	 *            banco de dados.
	 * @exception Execption
	 *                Caso haja problemas de conexão, restrição de acesso ou
	 *                validação de dados.
	 */
	public void alterar(FeriadoVO obj) throws Exception {
		try {
			con.setAutoCommit(false);
			FeriadoVO.validarDados(obj);
			alterarCRM(getIdEntidade());
			obj.realizarUpperCaseDados();
			String sql = "UPDATE Feriado set descricao=?, dia=?, mes=?, nacional=?, estadual=?, estado=?, cidade=?, naoRecorrente=?, pais=? WHERE ((codigo = ?))";
			try (PreparedStatement sqlAlterar = con.prepareStatement(sql)) {
				sqlAlterar.setString(1, obj.getDescricao());
				sqlAlterar.setDate(2, Uteis.getDataJDBC(obj.getDia()));
				sqlAlterar.setString(3, obj.getMes());
				sqlAlterar.setBoolean(4, obj.isNacional().booleanValue());
				sqlAlterar.setBoolean(5, obj.isEstadual().booleanValue());
				if (obj.getEstado().getCodigo().intValue() != 0) {
					sqlAlterar.setInt(6, obj.getEstado().getCodigo().intValue());
				} else {
					sqlAlterar.setNull(6, 0);
				}
				if (obj.getCidade().getCodigo().intValue() != 0) {
					sqlAlterar.setInt(7, obj.getCidade().getCodigo().intValue());
				} else {
					sqlAlterar.setNull(7, 0);
				}
				sqlAlterar.setBoolean(8, obj.isNaoRecorrente().booleanValue());
				if (obj.getPais().getCodigo().intValue() != 0) {
					sqlAlterar.setInt(9, obj.getPais().getCodigo().intValue());
				} else {
					sqlAlterar.setNull(9, 0);
				}
				sqlAlterar.setInt(10, obj.getCodigo().intValue());
				sqlAlterar.execute();
			}
			con.commit();
		} catch (Exception e) {
			con.rollback();
			con.setAutoCommit(true);
			throw e;
		} finally {
			con.setAutoCommit(true);
		}
	}

	/**
	 * Operação responsável por excluir no BD um objeto da classe
	 * <code>FeriadoVO</code>. Sempre localiza o registro a ser excluído através
	 * da chave primária da entidade. Primeiramente verifica a conexão com o
	 * banco de dados e a permissão do usuário para realizar esta operacão na
	 * entidade. Isto, através da operação <code>excluir</code> da superclasse.
	 * 
	 * @param obj
	 *            Objeto da classe <code>FeriadoVO</code> que será removido no
	 *            banco de dados.
	 * @exception Execption
	 *                Caso haja problemas de conexão ou restrição de acesso.
	 */
	public void excluir(FeriadoVO obj) throws Exception {
		try {
			con.setAutoCommit(false);
			excluirCRM(getIdEntidade());
			String sql = "DELETE FROM Feriado WHERE ((codigo = ?))";
			try (PreparedStatement sqlExcluir = con.prepareStatement(sql)) {
				sqlExcluir.setInt(1, obj.getCodigo().intValue());
				sqlExcluir.execute();
			}
			con.commit();
		} catch (Exception e) {
			con.rollback();
			con.setAutoCommit(true);
			throw e;
		} finally {
			con.setAutoCommit(true);
		}
	}

	/**
	 * Responsável por realizar uma consulta de <code>Feriado</code> através do
	 * valor do atributo <code>nome</code> da classe <code>Cidade</code> Faz uso
	 * da operação <code>montarDadosConsulta</code> que realiza o trabalho de
	 * prerarar o List resultante.
	 * 
	 * @return List Contendo vários objetos da classe <code>FeriadoVO</code>
	 *         resultantes da consulta.
	 * @exception Execption
	 *                Caso haja problemas de conexão ou restrição de acesso.
	 */
	public List consultarPorNomeCidade(String valorConsulta, int nivelMontarDados) throws Exception {
		consultarCRM(getIdEntidade(), true);
		String sqlStr = "SELECT Feriado.* FROM Feriado, Cidade WHERE Feriado.cidade = Cidade.codigo and upper( Cidade.nome ) like('" + valorConsulta.toUpperCase() + "%') ORDER BY Cidade.nome";
		try (Statement stm = con.createStatement()) {
			try (ResultSet tabelaResultado = stm.executeQuery(sqlStr)) {
				return montarDadosConsulta(tabelaResultado, nivelMontarDados, con);
			}
		}
	}

	public List consultarPorNomeEstado(String valorConsulta, int nivelMontarDados) throws Exception {
		consultarCRM(getIdEntidade(), true);
		String sqlStr = "SELECT Feriado.* FROM Feriado, Estado WHERE Feriado.estado = Estado.codigo and upper( Estado.descricao ) like('" + valorConsulta.toUpperCase() + "%') ORDER BY Estado.descricao";
		try (Statement stm = con.createStatement()) {
			try (ResultSet tabelaResultado = stm.executeQuery(sqlStr)) {
				return montarDadosConsulta(tabelaResultado, nivelMontarDados, con);
			}
		}
	}

	public List consultarPorNomePais(String valorConsulta, int nivelMontarDados) throws Exception {
		consultarCRM(getIdEntidade(), true);
		String sqlStr = "SELECT Feriado.* FROM Feriado, Pais WHERE Feriado.pais = Pais.codigo and upper( Pais.nome ) like('" + valorConsulta.toUpperCase() + "%') ORDER BY Pais.nome";
		try (Statement stm = con.createStatement()) {
			try (ResultSet tabelaResultado = stm.executeQuery(sqlStr)) {
				return montarDadosConsulta(tabelaResultado, nivelMontarDados, con);
			}
		}
	}

	public List<Date> consultarPorPeriodoEmpresa(Date inicio, Date fim, EmpresaVO empresaVO) throws Exception{
		StringBuilder sql = new StringBuilder();
		sql.append("select dia from (select ");
		sql.append("  cast(cast((date_part('year', current_date) * 10000 + date_part('month', dia) * 100 + date_part('day', dia)) as character(8)) as date) dia  ");
		sql.append("   from feriado a where naorecorrente = false and nacional = true ");
		sql.append("union ");
		sql.append("select dia  ");
		sql.append("   from feriado a where naorecorrente = true and nacional = true ");
		sql.append("union ");
		sql.append("select dia from feriado a where estado = ? and nacional = false and estadual = true ");
		sql.append("union  ");
		sql.append("select dia from feriado a where estado = ? and cidade = ? and nacional = false and estadual = false ");
		sql.append(") tb where dia between ? and ? order by dia asc; ");

		List<Date> dias;
		try (PreparedStatement stm = con.prepareStatement(sql.toString())) {
			stm.setInt(1, empresaVO.getEstado().getCodigo());
			stm.setInt(2, empresaVO.getEstado().getCodigo());
			stm.setInt(3, empresaVO.getCidade().getCodigo());
			stm.setDate(4, new java.sql.Date(inicio.getTime()));
			stm.setDate(5, new java.sql.Date(fim.getTime()));


			try (ResultSet rs = stm.executeQuery()) {
				dias = new ArrayList<Date>();
				while (rs.next()) {
					dias.add(rs.getDate("dia"));
				}
			}
		}
		return dias;

	}

	public Set<Date> consultarPorPeriodoEmpresaAulaCheia(Date inicio, Date fim, EmpresaVO empresaVO) throws Exception{
		StringBuilder sql = new StringBuilder();
		HashSet<Date> dias = new HashSet<>();
		HashSet<Date> recorrentes = new HashSet<>();

		sql.append("select dia from feriado where naorecorrente = false");
		try (PreparedStatement stm = con.prepareStatement(sql.toString())) {
			try (ResultSet rs = stm.executeQuery()) {
				while (rs.next()) {
					if(Calendario.dataNoMesmoMes(rs.getDate("dia"), inicio) ||
							Calendario.dataNoMesmoMes(rs.getDate("dia"), fim))
						recorrentes.add(rs.getDate("dia"));
				}
			}
		}
		recorrentes.add(inicio);
		for(Date anoFeriado : recorrentes) {
			sql.delete(0, sql.length());
			sql.append("select codigo, dia, naorecorrente from ( select codigo, dia, naorecorrente  ");
			sql.append("   from feriado a where naorecorrente = false and nacional = true ");
			sql.append("union ");
			sql.append("select codigo, dia, naorecorrente  ");
			sql.append("   from feriado a where naorecorrente = true and nacional = true ");
			sql.append("union ");
			sql.append("select codigo, dia, naorecorrente from feriado a where estado = ? and nacional = false and estadual = true ");
			sql.append("union  ");
			sql.append("select codigo, dia, naorecorrente from feriado a where estado = ? and cidade = ? and nacional = false and estadual = false ");
			sql.append("union  ");
			sql.append("select codigo, dia, naorecorrente from feriado a where estado = ? and cidade = ? and nacional = false and estadual = false and naorecorrente = false");
			sql.append(") tb where ((dia between ? and ?) or ");
			sql.append(" (dia::date between '" +  Uteis.getDataJDBC(
												Uteis.obterPrimeiroDiaMes(anoFeriado))
												+"' and '"+
											Uteis.getDataJDBC(
												Uteis.obterUltimoDiaMes(anoFeriado))
					+"' and naorecorrente = false)) order by dia asc; ");

			try (PreparedStatement stm = con.prepareStatement(sql.toString())) {
				stm.setInt(1, empresaVO.getEstado().getCodigo());
				stm.setInt(2, empresaVO.getEstado().getCodigo());
				stm.setInt(3, empresaVO.getCidade().getCodigo());
				stm.setInt(4, empresaVO.getEstado().getCodigo());
				stm.setInt(5, empresaVO.getCidade().getCodigo());
				stm.setDate(6, Uteis.getDataJDBC(inicio));
				stm.setDate(7, Uteis.getDataJDBC(fim));


				try (ResultSet rs = stm.executeQuery()) {
					while (rs.next()) {
						if(!Calendario.maior(rs.getDate("dia"), fim) && (Calendario.dataNoMesmoMes(rs.getDate("dia"), inicio) ||
								Calendario.dataNoMesmoMes(rs.getDate("dia"), fim))) {
							dias.add(rs.getDate("dia"));
						}
					}
				}
			}
		}
		return dias;

	}

	/**
	 * Método responsavel por validar se é feriado Nacional, Estadual e Local. Também validando se é recorrente ou não. 
	 * Se não for recorrente ele valida se o feriado é do mesmo ano da Data passada por parametro.
	 * @param obj
	 * @param prmIni
	 * @return
	 * @throws Exception
	 * <AUTHOR>
	 */
	public Boolean validarFeriadoPorEmpresaParaCalculoAberturaMeta(EmpresaVO empresa, Date prmIni) throws Exception {
            List listaFeriado = consultarFeriadoPorDiaAndMes(prmIni, false, Uteis.NIVELMONTARDADOS_TODOS);
            Iterator e = listaFeriado.iterator();
            while (e.hasNext()) {
                FeriadoVO feriado = (FeriadoVO) e.next();
                if(validarFeriadoEmpresa(feriado, empresa, prmIni)){
                    return true;
                }

            }
            return false;
	}

	/**
	 * Responsável por realizar uma consulta de <code>Feriado</code> através do
	 * valor do atributo <code>Date dia</code>. Retorna os objetos com valores
	 * pertecentes ao período informado por parâmetro. Faz uso da operação
	 * <code>montarDadosConsulta</code> que realiza o trabalho de prerarar o
	 * List resultante.
	 * 
	 * @param controlarAcesso
	 *            Indica se a aplicação deverá verificar se o usuário possui
	 *            permissão para esta consulta ou não.
	 * @return List Contendo vários objetos da classe <code>FeriadoVO</code>
	 *         resultantes da consulta.
	 * @exception Exception
	 *                Caso haja problemas de conexão ou restrição de acesso.
	 */
	public Boolean consultarPorDiaAndFeriado(Date prmIni, boolean controlarAcesso, int nivelMontarDados) throws Exception {
		consultarCRM(getIdEntidade(), controlarAcesso);
		String sqlStr = "SELECT * FROM Feriado WHERE Cast(dia as date) = Cast('" + Uteis.getDataJDBC(prmIni) + "' as date) ORDER BY dia";
		try (Statement stm = con.createStatement()) {
			try (ResultSet tabelaResultado = stm.executeQuery(sqlStr)) {
				if (!tabelaResultado.next()) {
					return false;
				}
			}
		}
		return true;
	}

	/**
	 * Método responsavel por consultar o feriado por dia e por Mes retornando uma lista de Feriado caso haja.
	 * @param prmIni
	 * @param controlarAcesso
	 * @param nivelMontarDados
	 * @return
	 * @throws Exception
	 * <AUTHOR>
	 */
	public List consultarFeriadoPorDiaAndMes(Date prmIni, boolean controlarAcesso, int nivelMontarDados) throws Exception {
		consultarCRM(getIdEntidade(), controlarAcesso);
		String sqlStr = "SELECT * FROM Feriado WHERE date_part('day', dia) = date_part('day', cast('" + Uteis.getDataJDBC(prmIni) + "' as date)) "+ 
						"and date_part('month', dia) = date_part('month', cast('" + Uteis.getDataJDBC(prmIni) + "' as date))  ORDER BY dia";

		try (Statement stm = con.createStatement()) {
			try (ResultSet tabelaResultado = stm.executeQuery(sqlStr)) {
				return montarDadosConsulta(tabelaResultado, nivelMontarDados, con);
			}
		}
	}

	/**
	 * Responsável por realizar uma consulta de <code>Feriado</code> através do
	 * valor do atributo <code>Date dia</code>. Retorna os objetos com valores
	 * pertecentes ao período informado por parâmetro. Faz uso da operação
	 * <code>montarDadosConsulta</code> que realiza o trabalho de prerarar o
	 * List resultante.
	 * 
	 * @param controlarAcesso
	 *            Indica se a aplicação deverá verificar se o usuário possui
	 *            permissão para esta consulta ou não.
	 * @return List Contendo vários objetos da classe <code>FeriadoVO</code>
	 *         resultantes da consulta.
	 * @exception Exception
	 *                Caso haja problemas de conexão ou restrição de acesso.
	 */
	public List consultarPorDia(Date prmIni, Date prmFim, boolean controlarAcesso, int nivelMontarDados) throws Exception {
		consultarCRM(getIdEntidade(), controlarAcesso);
		String sqlStr = "SELECT * FROM Feriado WHERE ((dia >= '" + Uteis.getDataJDBC(prmIni) + "') and (dia <= '" + Uteis.getDataJDBC(prmFim) + "')) ORDER BY dia";
		try (Statement stm = con.createStatement()) {
			try (ResultSet tabelaResultado = stm.executeQuery(sqlStr)) {
				return (montarDadosConsulta(tabelaResultado, nivelMontarDados, con));
			}
		}
	}

	/**
	 * Responsável por realizar uma consulta de <code>Feriado</code> através do
	 * valor do atributo <code>String descricao</code>. Retorna os objetos, com
	 * início do valor do atributo idêntico ao parâmetro fornecido. Faz uso da
	 * operação <code>montarDadosConsulta</code> que realiza o trabalho de
	 * prerarar o List resultante.
	 * 
	 * @param controlarAcesso
	 *            Indica se a aplicação deverá verificar se o usuário possui
	 *            permissão para esta consulta ou não.
	 * @return List Contendo vários objetos da classe <code>FeriadoVO</code>
	 *         resultantes da consulta.
	 * @exception Exception
	 *                Caso haja problemas de conexão ou restrição de acesso.
	 */
	public List consultarPorDescricao(String valorConsulta, boolean controlarAcesso, int nivelMontarDados) throws Exception {
		consultarCRM(getIdEntidade(), controlarAcesso);
		String sqlStr = "SELECT * FROM Feriado WHERE upper( descricao ) like('" + valorConsulta.toUpperCase() + "%') ORDER BY descricao";
		try (Statement stm = con.createStatement()) {
			try (ResultSet tabelaResultado = stm.executeQuery(sqlStr)) {
				return (montarDadosConsulta(tabelaResultado, nivelMontarDados, con));
			}
		}
	}

	/**
	 * Responsável por realizar uma consulta de <code>Feriado</code> através do
	 * valor do atributo <code>Integer codigo</code>. Retorna os objetos com
	 * valores iguais ou superiores ao parâmetro fornecido. Faz uso da operação
	 * <code>montarDadosConsulta</code> que realiza o trabalho de prerarar o
	 * List resultante.
	 * 
	 * @param controlarAcesso
	 *            Indica se a aplicação deverá verificar se o usuário possui
	 *            permissão para esta consulta ou não.
	 * @return List Contendo vários objetos da classe <code>FeriadoVO</code>
	 *         resultantes da consulta.
	 * @exception Exception
	 *                Caso haja problemas de conexão ou restrição de acesso.
	 */
	public List consultarPorCodigo(Integer valorConsulta, boolean controlarAcesso, int nivelMontarDados) throws Exception {
		consultarCRM(getIdEntidade(), controlarAcesso);
		String sqlStr = "SELECT * FROM Feriado WHERE codigo = " + valorConsulta.intValue() + " ORDER BY codigo";
		try (Statement stm = con.createStatement()) {
			try (ResultSet tabelaResultado = stm.executeQuery(sqlStr)) {
				return (montarDadosConsulta(tabelaResultado, nivelMontarDados, con));
			}
		}
	}

	/**
	 * Responsável por montar os dados de vários objetos, resultantes de uma
	 * consulta ao banco de dados (<code>ResultSet</code>). Faz uso da operação
	 * <code>montarDados</code> que realiza o trabalho para um objeto por vez.
	 * 
	 * @return List Contendo vários objetos da classe <code>FeriadoVO</code>
	 *         resultantes da consulta.
	 */
	public static List montarDadosConsulta(ResultSet tabelaResultado, int nivelMontarDados,Connection con) throws Exception {
		List vetResultado = new ArrayList();
		while (tabelaResultado.next()) {
			FeriadoVO obj = new FeriadoVO();
			obj = montarDados(tabelaResultado, nivelMontarDados, con);
			vetResultado.add(obj);
		}
		tabelaResultado = null;
		return vetResultado;
	}

	/**
	 * Responsável por montar os dados resultantes de uma consulta ao banco de
	 * dados (<code>ResultSet</code>) em um objeto da classe
	 * <code>FeriadoVO</code>.
	 * 
	 * @return O objeto da classe <code>FeriadoVO</code> com os dados
	 *         devidamente montados.
	 */
	public static FeriadoVO montarDados(ResultSet dadosSQL, int nivelMontarDados, Connection con) throws Exception {
		FeriadoVO obj = new FeriadoVO();
		obj.setCodigo(new Integer(dadosSQL.getInt("codigo")));
		obj.setDescricao(dadosSQL.getString("descricao"));
		obj.setDia(dadosSQL.getDate("dia"));
		obj.setMes(dadosSQL.getString("mes"));
		obj.setNacional(new Boolean(dadosSQL.getBoolean("nacional")));
		obj.setEstadual(new Boolean(dadosSQL.getBoolean("estadual")));
		obj.getEstado().setCodigo(new Integer(dadosSQL.getInt("estado")));
		obj.getCidade().setCodigo(new Integer(dadosSQL.getInt("cidade")));
		obj.setNaoRecorrente(new Boolean(dadosSQL.getBoolean("naoRecorrente")));
		obj.getPais().setCodigo(new Integer(dadosSQL.getInt("pais")));
		obj.setNovoObj(new Boolean(false));
		if (nivelMontarDados == Uteis.NIVELMONTARDADOS_DADOSBASICOS) {
			return obj;
		}
		montarDadosCidade(obj, nivelMontarDados, con);
		montarDadosEstado(obj, nivelMontarDados, con);
		montarDadosPais(obj, nivelMontarDados, con);
		return obj;
	}

	/**
	 * Operação responsável por montar os dados de um objeto da classe
	 * <code>CidadeVO</code> relacionado ao objeto <code>FeriadoVO</code>. Faz
	 * uso da chave primária da classe <code>CidadeVO</code> para realizar a
	 * consulta.
	 * 
	 * @param obj
	 *            Objeto no qual será montado os dados consultados.
	 */
	public static void montarDadosCidade(FeriadoVO obj, int nivelMontarDados, Connection con) throws Exception {
		if (obj.getCidade().getCodigo().intValue() == 0) {
			obj.setCidade(new CidadeVO());
			return;
		}
                Cidade cidadeDAO = new Cidade(con);
		obj.setCidade(cidadeDAO.consultarPorChavePrimaria(obj.getCidade().getCodigo(), nivelMontarDados));
                cidadeDAO = null;
	}

	public static void montarDadosPais(FeriadoVO obj, int nivelMontarDados, Connection con) throws Exception {
		if (obj.getPais().getCodigo().intValue() == 0) {
			obj.setPais(new PaisVO());
			return;
		}
                Pais paisDAO = new Pais(con); 
		obj.setPais(paisDAO.consultarPorChavePrimaria(obj.getPais().getCodigo(), nivelMontarDados));
                paisDAO = null;
	}

	public static void montarDadosEstado(FeriadoVO obj, int nivelMontarDados, Connection con) throws Exception {
		if (obj.getEstado().getCodigo().intValue() == 0) {
			obj.setEstado(new EstadoVO());
			return;
		}
                Estado estadoDAO = new Estado(con);
		obj.setEstado(estadoDAO.consultarPorChavePrimaria(obj.getEstado().getCodigo(), nivelMontarDados));
                estadoDAO = null; 
	}

	/**
	 * Operação responsável por localizar um objeto da classe
	 * <code>FeriadoVO</code> através de sua chave primária.
	 * 
	 * @exception Exception
	 *                Caso haja problemas de conexão ou localização do objeto
	 *                procurado.
	 */
	public FeriadoVO consultarPorChavePrimaria(Integer codigoPrm, int nivelMontarDados) throws Exception {
		consultarCRM(getIdEntidade(), false);
		String sql = "SELECT * FROM Feriado WHERE codigo = ?";
		try (PreparedStatement sqlConsultar = con.prepareStatement(sql)) {
			sqlConsultar.setInt(1, codigoPrm.intValue());
			try (ResultSet tabelaResultado = sqlConsultar.executeQuery()) {
				if (!tabelaResultado.next()) {
					throw new ConsistirException("Dados Não Encontrados ( Feriado ).");
				}
				return (montarDados(tabelaResultado, nivelMontarDados, con));
			}
		}
	}	
	
	/* (non-Javadoc)
	 * @see negocio.interfaces.crm.FeriadoInterfaceFacade#consultarDiasFeriados(java.util.Date, java.util.Date)
	 */
	public List<Date> consultarDiasFeriados(Date inicio, Date fim, EmpresaVO empresa) throws Exception{
            List<Date> dias = new ArrayList<Date>();
            if(fim == null){
                fim = inicio;
            }
            List<PeriodoMensal>  mesesEntreDatas = Uteis.getPeriodosMensaisEntreDatas(inicio, fim);
            StringBuffer where = new StringBuffer();
            for(PeriodoMensal mes : mesesEntreDatas){
                where.append(" OR (");
                where.append(" date_part('day', dia) >= date_part('day', cast('").append(Uteis.getDataJDBC(mes.getDataInicio())).append("' as date))");
                where.append(" and date_part('day', dia) <= date_part('day', cast('").append(Uteis.getDataJDBC(mes.getDataTermino())).append("' as date))");
                where.append(" and date_part('month', dia) = date_part('month', cast('").append(Uteis.getDataJDBC(mes.getDataTermino())).append("' as date))");
                where.append(")");
            }
            if(where.length() == 0){
                return dias;
            }
            String sql = "SELECT * FROM FERIADO WHERE "+ where.substring(3).toString();// o substrig retira o primeiro or do where
            Declaracao dc = new Declaracao(sql, con);
		List listaFeriado;
		try (ResultSet rs = dc.executeQuery()) {
			listaFeriado = montarDadosConsulta(rs, Uteis.NIVELMONTARDADOS_TODOS, con);
		}
		Date dataVerificar = null;
            Iterator e = listaFeriado.iterator();
            while (e.hasNext()) {
                FeriadoVO feriado = (FeriadoVO) e.next();
                dataVerificar = Calendario.setAnoData(feriado.getDia(), Uteis.getAnoData(inicio));
                if(Calendario.menor(dataVerificar, inicio)){
                    dataVerificar = Uteis.somarCampoData(dataVerificar, Calendar.YEAR, 1);
                }
                if(validarFeriadoEmpresa(feriado, empresa, dataVerificar)){
                    dias.add(Uteis.getDataComHoraZerada(dataVerificar));
                }
            }
            return dias; 
	}

	private PreparedStatement getPS() throws SQLException {

		StringBuilder sql = new StringBuilder("SELECT\n" +
				"  f.codigo,\n" +
				"  f.descricao,\n" +
				"  f.dia,\n" +
				"  p.nome      AS pais,\n" +
				"  e.descricao AS estado,\n" +
				"  c.nome      AS cidade\n" +
				"FROM feriado f\n" +
				"  INNER JOIN pais p ON p.codigo = f.pais\n" +
				"  LEFT JOIN estado e ON e.codigo = f.estado\n" +
				"  LEFT JOIN cidade c ON c.codigo = f.cidade;");

		return con.prepareStatement(sql.toString());
	}

	public String consultarJSON() throws Exception {
		StringBuilder json = new StringBuilder();
		json.append("{\"aaData\":[");
		boolean dados = false;
		try(ResultSet rs = getPS().executeQuery()) {
			while (rs.next()) {
				dados = true;
				json.append("[\"").append(rs.getString("codigo")).append("\",");
				json.append("\"").append(Uteis.normalizarStringJSON(rs.getString("descricao"))).append("\",");
				json.append("\"").append(Uteis.getData(rs.getDate("dia"))).append("\",");
				json.append("\"").append(Uteis.normalizarStringJSON(rs.getString("pais"))).append("\",");
				json.append("\"").append(Uteis.normalizarStringJSON(rs.getString("estado"))).append("\",");
				json.append("\"").append(Uteis.normalizarStringJSON(rs.getString("cidade"))).append("\"],");
			}
		}
		if(dados) {
			json.deleteCharAt(json.toString().length() - 1);
		}
		json.append("]}");
		return json.toString();
	}
        
    public boolean validarFeriadoEmpresa(FeriadoVO feriado, EmpresaVO empresa, Date dia) throws Exception{
        if (feriado.getNacional() 
            && feriado.getPais().getCodigo().equals(empresa.getPais().getCodigo()) 
            && !feriado.getNaoRecorrente()) {
               return true;

       } else if (feriado.getNacional() 
                       && feriado.getPais().getCodigo().equals(empresa.getPais().getCodigo()) 
                       && feriado.getNaoRecorrente() 
                       && Uteis.getCompareData(feriado.getDia(), dia) == 0) {
               return true;

       } else if (feriado.getEstadual() 
                       && feriado.getEstado().getCodigo().equals(empresa.getEstado().getCodigo()) 
                       && !feriado.getNaoRecorrente()) {
               return true;

       } else if (feriado.getEstadual() 
                       && feriado.getEstado().getCodigo().equals(empresa.getEstado().getCodigo()) 
                       && feriado.getNaoRecorrente() 
                       && Uteis.getCompareData(feriado.getDia(), dia) == 0) {
               return true;

       } else if (!feriado.getNacional() 
                       && !feriado.getEstadual() 
                       && feriado.getCidade().getCodigo().equals(empresa.getCidade().getCodigo()) 
                       && !feriado.getNaoRecorrente()) {
               return true;

       } else if (!feriado.getNacional() 
                       && !feriado.getEstadual() 
                       && feriado.getCidade().getCodigo().equals(empresa.getCidade().getCodigo()) 
                       && feriado.getNaoRecorrente() && Uteis.getCompareData(feriado.getDia(), dia) == 0) {
               return true;
       }
       return false;
    }    

    public Date obterProximoDiaUtil(Date dataBase, EmpresaVO empresa) throws Exception{
        Date dataObtida = dataBase; 
        List<Date> feriados = new ArrayList<Date>();
         for(int i = 1; i < 20; i++){
            feriados = consultarDiasFeriados(dataObtida, null, empresa);
            if(Uteis.verificarDiaUtil(dataObtida, feriados)){
                return dataObtida;
            }
            dataObtida = Uteis.somarDias(dataObtida, 1);
        }
        return dataObtida;
    }
}

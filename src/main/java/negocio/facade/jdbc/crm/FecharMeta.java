package negocio.facade.jdbc.crm;

import br.com.pactosolucoes.comuns.util.Declaracao;
import br.com.pactosolucoes.comuns.util.Formatador;
import br.com.pactosolucoes.enumeradores.FasesCRMEnum;
import br.com.pactosolucoes.enumeradores.SituacaoAtualMetaEnum;
import br.com.pactosolucoes.enumeradores.TipoFaseCRM;
import br.com.pactosolucoes.totalpass.ApiTotalPass;
import negocio.comuns.arquitetura.UsuarioVO;
import negocio.comuns.basico.ClienteVO;
import negocio.comuns.basico.ColaboradorVO;
import negocio.comuns.contrato.ContratoVO;
import negocio.comuns.crm.*;
import negocio.comuns.utilitarias.Calendario;
import negocio.comuns.utilitarias.ConsistirException;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisValidacao;
import negocio.facade.jdbc.arquitetura.SuperEntidade;
import negocio.facade.jdbc.arquitetura.SuperFacadeJDBC;
import negocio.facade.jdbc.arquitetura.Usuario;
import negocio.facade.jdbc.basico.Cliente;
import negocio.facade.jdbc.crm.fecharMetaDetalhado.FecharMetaDetalhado;
import negocio.interfaces.crm.FecharMetaInterfaceFacade;
import relatorio.negocio.comuns.basico.ItemRelatorioTO;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.sql.Statement;
import java.util.*;
import java.util.logging.Level;
import java.util.logging.Logger;

import static negocio.comuns.utilitarias.Uteis.getDataJDBC;

/**
 * Classe de persistï¿½ncia que encapsula todas as operações de manipulação dos
 * dados da classe <code>FecharMetaVO</code>. Responsá½vel por implementar
 * operações como incluir, alterar, excluir e consultar pertinentes a classe
 * <code>FecharMetaVO</code>. Encapsula toda a interaï¿½ï¿½o com o banco de dados.
 *
 * @see FecharMetaVO
 * @see SuperEntidade
 */
public class FecharMeta extends SuperEntidade implements FecharMetaInterfaceFacade {

    private static final Logger LOGGER = Logger.getLogger(FecharMeta.class.getName());

    private Hashtable fecharMetaDetalhados;
    //SQL QUE CONTA OS COMPARECIDOS DE AGENDAMENTOS LIGADO A UMA META
    public static final String CONTAR_COMPARECIMENTOS = "SELECT count(codigo) as total FROM agenda WHERE codigo IN ( " +
            "SELECT codigoorigem FROM fecharmetadetalhado WHERE origem LIKE 'AGENDA' " +
            "AND fecharmeta = ?) AND datacomparecimento IS NOT NULL ";
    //SQL QUE CONTA OS REAGENDADOS DE AGENDAMENTOS LIGADO A UMA META
    public static final String CONTAR_REAGENDADOS = "SELECT count(total) as total from( " +
            "SELECT count(codigo) as total FROM historicocontato WHERE codigo IN ( " +
            "SELECT historicocontato FROM fecharmetadetalhado " +
            "WHERE origem LIKE 'AGENDA' AND fecharmeta = ? )AND tipooperacao LIKE 'RE' GROUP BY cliente) as agendado ";
    //SQL QUE CONTA OS COMPARECIDOS DE AGENDAMENTOS LIGADO A UMA META
    public static final String CONTAR_COMPARECIMENTOS_FECHADOS = "SELECT count(codigo) as total FROM agenda WHERE codigo IN ( " +
            "SELECT codigoorigem FROM fecharmetadetalhado WHERE origem LIKE 'AGENDA' " +
            "AND fecharmeta = ? AND contrato IS NOT NULL ) AND datacomparecimento IS NOT NULL ";

    public static final String CONTAR_LIGACOES = "SELECT COUNT(*) as total FROM fecharmetadetalhado fmd INNER JOIN agenda a " +
            "ON a.codigo = fmd.codigoorigem AND a.tipoagendamento LIKE 'LI' WHERE fecharmeta = ?";

    public FecharMeta() throws Exception {
        super();
        setIdEntidade("AberturaMeta");
        setFecharMetaDetalhados(new Hashtable());
    }

    public FecharMeta(Connection conexao) throws Exception {
        super(conexao);
        setIdEntidade("AberturaMeta");
        setFecharMetaDetalhados(new Hashtable());
    }

    /**
     * Operaï¿½ï¿½o responsï¿½vel por retornar um novo objeto da classe
     * <code>FecharMetaVO</code>.
     */
    public FecharMetaVO novo() throws Exception {
        incluirCRM(getIdEntidade());
        FecharMetaVO obj = new FecharMetaVO();
        return obj;
    }


    /**
     * Responsï¿½vel por consultar o cï¿½digo de um registro de fecharmeta, baseado no colaborador, na data e no identificador.
     *
     * <AUTHOR>
     * 30/05/2011
     */
    public Integer obterCodigoFecharMetaColaboradorPorDia(Integer codigoColaborador, Date data, String identificador) throws Exception {
        StringBuilder sql = new StringBuilder();
        sql.append("SELECT fm.codigo ");
        sql.append("FROM   fecharmeta fm ");
        sql.append("       INNER JOIN aberturameta a ");
        sql.append("       ON     fm.aberturameta          = a.codigo ");
        sql.append("       AND    a.colaboradorresponsavel = ? ");
        sql.append("       AND    a.dia                    = ? ");
        sql.append("       AND    fm.identificadormeta  LIKE ?");

        Declaracao dc = new Declaracao(sql.toString(), con);
        int i = 0;
        dc.setInt(++i, codigoColaborador);
        dc.setDate(++i, getDataJDBC(data));
        dc.setString(++i, identificador);
        Integer codigo;
        try (ResultSet rs = dc.executeQuery()) {
            codigo = new Integer(0);
            if (rs.next()) {
                codigo = rs.getInt("codigo");
            }
        }
        return codigo;

    }

    /**
     * Operaï¿½ï¿½o responsï¿½vel por incluir no banco de dados um objeto da classe
     * <code>FecharMetaVO</code>. Primeiramente valida os dados (
     * <code>validarDados</code>) do objeto. Verifica a conexï¿½o com o banco de
     * dados e a permissï¿½o do usuï¿½rio para realizar esta operacï¿½o na entidade.
     * Isto, atravï¿½s da operaï¿½ï¿½o <code>incluir</code> da superclasse.
     *
     * @param obj Objeto da classe <code>FecharMetaVO</code> que serï¿½ gravado no
     *            banco de dados.
     * @throws Exception Caso haja problemas de conexï¿½o, restriï¿½ï¿½o de acesso ou
     *                   validaï¿½ï¿½o de dados.
     */
    public void incluir(FecharMetaVO obj) throws Exception {
        boolean metaextra = false;
        Integer countMetaExtra = 0;
        FecharMetaVO.validarDados(obj);
        incluirCRM(getIdEntidade());
        obj.realizarUpperCaseDados();
        String sql = "INSERT INTO FecharMeta( dataRegistro, meta, metaAtingida, porcentagem, justificativa, identificadorMeta, aberturaMeta, repescagem, metacalculada, nomemeta, maladireta ) VALUES ( ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)";
        PreparedStatement sqlInserir = con.prepareStatement(sql);
        sqlInserir.setTimestamp(1, Uteis.getDataJDBCTimestamp(obj.getDataRegistro()));
        sqlInserir.setInt(2, obj.getMeta().intValue());
        if (obj.getIdentificadorMeta().equals("MF")) {
            sqlInserir.setDouble(3, obj.getMetaAtingida());
        } else {
            sqlInserir.setInt(3, obj.getMetaAtingida().intValue());
        }
        sqlInserir.setDouble(4, obj.getPorcentagem());
        sqlInserir.setString(5, obj.getJustificativa());
        sqlInserir.setString(6, obj.getIdentificadorMeta());
        if (obj.getAberturaMetaVO().getCodigo() != 0) {
            sqlInserir.setInt(7, obj.getAberturaMetaVO().getCodigo());
        } else {
            sqlInserir.setNull(7, 0);
        }
        sqlInserir.setInt(8, obj.getRepescagem().intValue());
        sqlInserir.setBoolean(9, obj.getMetaCalculada());
        sqlInserir.setString(10, obj.getNomeMeta());

        if (obj.getMalaDiretaCRMExtra().getCodigo() != 0) {
            metaextra = true;
            sqlInserir.setInt(11, obj.getMalaDiretaCRMExtra().getCodigo());
        } else {
            sqlInserir.setNull(11, 0);
        }

        if(metaextra){
            countMetaExtra = countFecharMetaExtra(obj.getMalaDiretaCRMExtra().getCodigo(),  obj.getAberturaMetaVO().getCodigo());
        }
        if(countMetaExtra == 0 && metaextra){
            IncluirFecharmeta(obj, sqlInserir);
        }
        if(!metaextra) {
            IncluirFecharmeta(obj, sqlInserir);
        }
    }

    @Override
    public void incluir(FecharMetaVO obj, FasesCRMEnum fase) throws Exception {
        FecharMetaVO meta = consultarMetaPorDiaPorColaboradorResponsavel(fase.getDescricao(),obj.getDataRegistro(), obj.getAberturaMetaVO().getColaboradorResponsavel().getCodigo(), false, Uteis.NIVELMONTARDADOS_DADOSBASICOS,obj.getAberturaMetaVO().getEmpresaVO().getCodigo());
        if((meta != null  && meta.getCodigo() == 0) || meta == null){
            incluir(obj);
        }
    }

    private void IncluirFecharmeta(FecharMetaVO obj, PreparedStatement sqlInserir) throws Exception {
        sqlInserir.execute();
        obj.setCodigo(obterValorChavePrimariaCodigo());
        FecharMetaDetalhado fecharMetaDetalhado = new FecharMetaDetalhado(con);
        fecharMetaDetalhado.incluirFecharMetaDetalhados(obj.getCodigo(), obj.getFecharMetaDetalhadoVOs());
        fecharMetaDetalhado = null;
        obj.setNovoObj(false);
    }

    public Integer countFecharMetaExtra(Integer maladireta, Integer aberturameta) throws Exception {
        String sqlStr = "select count(1) totalFechameta from fecharmeta f where f.maladireta= " + maladireta + " and f.aberturameta="+ aberturameta;
        try (Statement stm = con.createStatement()) {
            try (ResultSet totalFechameta = stm.executeQuery(sqlStr)) {
                if (totalFechameta.next()) {
                    return totalFechameta.getInt("totalFechameta");
                }
            }
        }
        return 0;
    }
    /**
     * Operaï¿½ï¿½o responsï¿½vel por alterar no BD os dados de um objeto da classe
     * <code>FecharMetaVO</code>. Sempre utiliza a chave primï¿½ria da classe como
     * atributo para localizaï¿½ï¿½o do registro a ser alterado. Primeiramente
     * valida os dados (<code>validarDados</code>) do objeto. Verifica a conexï¿½o
     * com o banco de dados e a permissï¿½o do usuï¿½rio para realizar esta operacï¿½o
     * na entidade. Isto, atravï¿½s da operaï¿½ï¿½o <code>alterar</code> da
     * superclasse.
     *
     * @param obj Objeto da classe <code>FecharMetaVO</code> que serï¿½ alterada
     *            no banco de dados.
     * @throws Exception Caso haja problemas de conexï¿½o, restriï¿½ï¿½o de acesso ou
     *                   validaï¿½ï¿½o de dados.
     */
    public void alterar(FecharMetaVO obj) throws Exception {
        FecharMetaVO.validarDados(obj);
        alterarCRM(getIdEntidade());
        obj.realizarUpperCaseDados();
        String sql = "UPDATE FecharMeta set dataRegistro=?, meta=?, metaAtingida=?, porcentagem=?, justificativa=?, identificadormeta=?,  aberturaMeta=?, repescagem=?, metacalculada = ?, nomemeta = ?, maladireta = ?  WHERE ((codigo = ?))";
        PreparedStatement sqlAlterar = con.prepareStatement(sql);
        sqlAlterar.setTimestamp(1, Uteis.getDataJDBCTimestamp(obj.getDataRegistro()));
        sqlAlterar.setInt(2, obj.getMeta().intValue());
        if (obj.getIdentificadorMeta().equals("MF")) {
            sqlAlterar.setDouble(3, obj.getMetaAtingida());
        } else {
            sqlAlterar.setInt(3, obj.getMetaAtingida().intValue());
        }
        sqlAlterar.setDouble(4, obj.getPorcentagem());
        sqlAlterar.setString(5, obj.getJustificativa());
        sqlAlterar.setString(6, obj.getIdentificadorMeta());
        if (obj.getAberturaMetaVO().getCodigo() != 0) {
            sqlAlterar.setInt(7, obj.getAberturaMetaVO().getCodigo());
        } else {
            sqlAlterar.setNull(7, 0);
        }
        sqlAlterar.setInt(8, obj.getRepescagem().intValue());
        sqlAlterar.setBoolean(9, obj.getMetaCalculada());
        sqlAlterar.setString(10, obj.getNomeMeta());

        if (obj.getMalaDiretaCRMExtra().getCodigo() != 0) {
            sqlAlterar.setInt(11, obj.getMalaDiretaCRMExtra().getCodigo());
        } else {
            sqlAlterar.setNull(11, 0);
        }

        sqlAlterar.setInt(12, obj.getCodigo());
        sqlAlterar.execute();

        FecharMetaDetalhado fecharMetaDetalhado = new FecharMetaDetalhado(con);
        fecharMetaDetalhado.incluirFecharMetaDetalhados(obj.getCodigo(), obj.getFecharMetaDetalhadoVOs());
        fecharMetaDetalhado = null;
    }

    public void alteraSemSubordinada(FecharMetaVO obj) throws Exception {
        alteraSemSubordinada(obj ,false);
    }

    /**
     * Operaï¿½ï¿½o responsï¿½vel por alterar no BD os dados de um objeto da classe
     * <code>FecharMetaVO</code>. Sempre utiliza a chave primï¿½ria da classe como
     * atributo para localizaï¿½ï¿½o do registro a ser alterado. Primeiramente
     * valida os dados (<code>validarDados</code>) do objeto. Verifica a conexï¿½o
     * com o banco de dados e a permissï¿½o do usuï¿½rio para realizar esta operacï¿½o
     * na entidade. Isto, atravï¿½s da operaï¿½ï¿½o <code>alterar</code> da
     * superclasse.
     *
     * @param obj Objeto da classe <code>FecharMetaVO</code> que serï¿½ alterada
     *            no banco de dados.
     * @throws Exception Caso haja problemas de conexï¿½o, restriï¿½ï¿½o de acesso ou
     *                   validaï¿½ï¿½o de dados.
     */
    public void alteraSemSubordinada(FecharMetaVO obj, boolean ignorarValidacao) throws Exception {
        FecharMetaVO.validarDados(obj);
        if(!ignorarValidacao) {
            alterarCRM(getIdEntidade());
        }
        obj.realizarUpperCaseDados();
        String sql = "UPDATE FecharMeta set dataRegistro=?, meta=?, metaAtingida=?, porcentagem=?, justificativa=?, identificadormeta=?,  repescagem=?, metacalculada=?, nomemeta = ?, maladireta = ? WHERE ((codigo = ?))";
        PreparedStatement sqlAlterar = con.prepareStatement(sql);
        sqlAlterar.setTimestamp(1, Uteis.getDataJDBCTimestamp(obj.getDataRegistro()));
        sqlAlterar.setInt(2, obj.getMeta().intValue());
        if (obj.getIdentificadorMeta().equals("MF")) {
            sqlAlterar.setDouble(3, obj.getMetaAtingida());
        } else {
            sqlAlterar.setInt(3, obj.getMetaAtingida().intValue());
        }
        sqlAlterar.setDouble(4, obj.getPorcentagem());
        sqlAlterar.setString(5, obj.getJustificativa());
        sqlAlterar.setString(6, obj.getIdentificadorMeta());
        sqlAlterar.setInt(7, obj.getRepescagem().intValue());
        sqlAlterar.setBoolean(8, obj.getMetaCalculada());
        sqlAlterar.setString(9, obj.getNomeMeta());

        if (obj.getMalaDiretaCRMExtra().getCodigo() != 0) {
            sqlAlterar.setInt(10, obj.getMalaDiretaCRMExtra().getCodigo());
        } else {
            sqlAlterar.setNull(10, 0);
        }

        sqlAlterar.setInt(11, obj.getCodigo());
        sqlAlterar.execute();

    }

    public List<MetaCRMTO> consultarMeta(Date dataInicioMeta, Date dataFimMeta, int nivelMontarDados, List<UsuarioVO> usuarioVOList, Integer empresa, boolean apenasCalculadas) throws Exception {
        String codigosUsuario = Uteis.retornarCodigos(usuarioVOList);
        List<MetaCRMTO> listaRetornar = new ArrayList<MetaCRMTO>();

        // CONSULTAR META CRM
        processarMeta(dataInicioMeta, dataFimMeta, nivelMontarDados, empresa, apenasCalculadas, codigosUsuario, listaRetornar);

        // CONSULTAR META CRM EXTRA
        processarMetaExtra(dataInicioMeta, dataFimMeta, nivelMontarDados, empresa, codigosUsuario, listaRetornar);

        for (MetaCRMTO metaCRMTO : listaRetornar) {
            for (TipoMetaCRMTO tipoMetaCRMTO : metaCRMTO.getListaTipoMetaVO()) {

                //Preencher o total de contatos sem bater meta
                tipoMetaCRMTO.setTotalContatoSemBaterMeta(totalContatoSemBaterMeta(tipoMetaCRMTO.getListaFecharMetaVO()));

                Integer meta = tipoMetaCRMTO.getTotalMeta();
                Integer realizado = tipoMetaCRMTO.getTotalMetaRealizada();
                Integer contatoSemBater = tipoMetaCRMTO.getTotalContatoSemBaterMeta();
                Integer realizadoMAIScontatoSemBater = realizado + contatoSemBater;

                //PREENCHER AS SITUAï¿½ï¿½ES DE CADA META
                if (realizado >= meta) {
                    tipoMetaCRMTO.setSituacaoAtualMetaEnum(SituacaoAtualMetaEnum.META_ATENDIDA);
                } else if ((realizadoMAIScontatoSemBater >= meta) && (contatoSemBater != 0)) {
                    tipoMetaCRMTO.setSituacaoAtualMetaEnum(SituacaoAtualMetaEnum.META_NAO_ATENDIDA_CONTATOREALIZADO);
                } else if (realizadoMAIScontatoSemBater < meta) {
                    tipoMetaCRMTO.setSituacaoAtualMetaEnum(SituacaoAtualMetaEnum.META_NAO_ATENDIDA);
                }
            }
        }

        return listaRetornar;
    }

    public List<MetaCRMTO> consultarMetaToServlet(Date dataInicioMeta, Date dataFimMeta, List<UsuarioVO> usuarioVOList, Integer empresa, boolean apenasCalculadas) throws Exception {
        String codigosUsuario = Uteis.retornarCodigos(usuarioVOList);
        List<MetaCRMTO> listaRetornar = new ArrayList<>();

        // CONSULTAR META CRM
        processarMetaServlet(dataInicioMeta, dataFimMeta, empresa, apenasCalculadas, codigosUsuario, listaRetornar);

        // CONSULTAR META CRM EXTRA
        processarMetaExtra(dataInicioMeta, dataFimMeta, Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA, empresa, codigosUsuario, listaRetornar);

        for (MetaCRMTO metaCRMTO : listaRetornar) {
            for (TipoMetaCRMTO tipoMetaCRMTO : metaCRMTO.getListaTipoMetaVO()) {
                Integer meta = tipoMetaCRMTO.getTotalMeta();
                Integer realizado = tipoMetaCRMTO.getTotalMetaRealizada();
                Integer contatoSemBater = tipoMetaCRMTO.getTotalContatoSemBaterMeta();
                Integer realizadoMAIScontatoSemBater = realizado + contatoSemBater;

                //PREENCHER AS SITUAï¿½ï¿½ES DE CADA META
                if (realizado >= meta) {
                    tipoMetaCRMTO.setSituacaoAtualMetaEnum(SituacaoAtualMetaEnum.META_ATENDIDA);
                } else if ((realizadoMAIScontatoSemBater >= meta) && (contatoSemBater != 0)) {
                    tipoMetaCRMTO.setSituacaoAtualMetaEnum(SituacaoAtualMetaEnum.META_NAO_ATENDIDA_CONTATOREALIZADO);
                } else if (realizadoMAIScontatoSemBater < meta) {
                    tipoMetaCRMTO.setSituacaoAtualMetaEnum(SituacaoAtualMetaEnum.META_NAO_ATENDIDA);
                }
            }
        }

        return listaRetornar;
    }

    private void processarMetaServlet(Date dataInicioMeta, Date dataFimMeta, Integer empresa, boolean apenasCalculadas, String codigosUsuario, List<MetaCRMTO> listaRetornar) throws Exception {
        StringBuilder sql = new StringBuilder();
        sql.append("select m.*, \n");
        sql.append("    (select count(*) \n");
        sql.append("    from fecharmetadetalhado fmd \n");
        sql.append("    inner join agenda ag on ag.codigo = fmd.codigoorigem \n");
        sql.append("    where fmd.origem = 'AGENDA'\n");
        sql.append("    and fmd.fecharmeta = m.codigo\n");
        sql.append("    and ag.tipoagendamento = 'LI') as ligacoes, \n");
        sql.append("    (select count(*)\n");
        sql.append("    from fecharmetadetalhado fmd\n");
        sql.append("    where fmd.fecharmeta = m.codigo\n");
        sql.append("    and fmd.obtevesucesso = false\n");
        sql.append("    and fmd.tevecontato = true) as contatoSemSucesso,\n");
        sql.append("    (select count(*)\n");
        sql.append("    from fecharmetadetalhado fmd\n");
        sql.append("    where fmd.fecharmeta = m.codigo\n");
        sql.append("    and fmd.obtevesucesso = true) as contatoComSucesso\n");
        sql.append("from fecharMeta m \n");
        sql.append("inner join aberturaMeta am on am.codigo = m.aberturaMeta \n");
        if (!UteisValidacao.emptyString(codigosUsuario)) {
            sql.append("inner join (").append(Uteis.criarTabelaIn(codigosUsuario)).append(")sqlIn on sqlIn.codigo = am.colaboradorResponsavel \n");
        }
        if (dataFimMeta != null) {
            sql.append("where am.dia >= '").append(getDataJDBC(dataInicioMeta)).append("'");
            sql.append(" and am.dia <= '").append(getDataJDBC(dataFimMeta)).append("'");
        } else {
            sql.append("where am.dia = '").append(getDataJDBC(dataInicioMeta)).append("'");
        }
        if (!UteisValidacao.emptyNumber(empresa)) {
            sql.append(" and am.empresa = ").append(empresa);
        }
        if (apenasCalculadas) {
            sql.append(" and m.metacalculada ");
        }
        sql.append(" and m.identificadorMeta not in('").append(FasesCRMEnum.CONVERSAO_INDICADOS.getSigla())
                .append("','").append(FasesCRMEnum.CONVERSAO_AGENDADOS.getSigla())
                .append("','").append(FasesCRMEnum.CONVERSAO_EX_ALUNOS.getSigla())
                .append("','").append(FasesCRMEnum.CONVERSAO_VISITANTES_ANTIGOS.getSigla())
                .append("','").append(FasesCRMEnum.CONVERSAO_DESISTENTES.getSigla())
                .append("','").append(FasesCRMEnum.CRM_EXTRA.getSigla()).append("')");

        List<FecharMetaVO> listaMeta = new ArrayList<>();
        try (PreparedStatement pst = con.prepareStatement(sql.toString())) {
            try (ResultSet dadosSQL = pst.executeQuery()) {
                while (dadosSQL.next()) {
                    FecharMetaVO obj = new FecharMetaVO();
                    obj.setCodigo(dadosSQL.getInt("codigo"));
                    obj.setDataRegistro(dadosSQL.getTimestamp("dataRegistro"));
                    obj.setMeta((double) dadosSQL.getInt("meta"));
                    obj.setRepescagem((double) dadosSQL.getInt("repescagem"));
                    obj.setMetaAtingida(dadosSQL.getDouble("metaAtingida"));
                    obj.getAberturaMetaVO().setCodigo(dadosSQL.getInt("aberturameta"));
                    obj.calcularPorcentagem();
                    obj.setJustificativa(dadosSQL.getString("justificativa"));
                    obj.setIdentificadorMeta(dadosSQL.getString("identificadorMeta"));
                    obj.setMetaCalculada(dadosSQL.getBoolean("metacalculada"));
                    obj.setNovoObj(false);
                    obj.setFase(FasesCRMEnum.getFasePorSigla(obj.getIdentificadorMeta()));
                    obj.setNomeMeta(dadosSQL.getString("nomeMeta"));
                    obj.getMalaDiretaCRMExtra().setCodigo(dadosSQL.getInt("maladireta"));
                    obj.setQtdLigacoesMeta(dadosSQL.getInt("ligacoes"));
                    obj.setQtdContatosSemSucesso(dadosSQL.getInt("contatoSemSucesso"));
                    obj.setQtdContatosComSucesso(dadosSQL.getInt("contatoComSucesso"));
                    listaMeta.add(obj);
                }
            }
        }

        for (FecharMetaVO fecharMetaVO : listaMeta) {
            FasesCRMEnum fasesCRMEnum = FasesCRMEnum.getFasePorSigla(fecharMetaVO.getIdentificadorMeta());

            MetaCRMTO metaCRMTO = new MetaCRMTO();
            metaCRMTO.setTipoFaseCRM(fasesCRMEnum.getTipoFase());
            int indiceTipoMeta = listaRetornar.indexOf(metaCRMTO);
            if (indiceTipoMeta < 0) {
                listaRetornar.add(metaCRMTO);
            } else {
                metaCRMTO = listaRetornar.get(indiceTipoMeta);
            }
            TipoMetaCRMTO tipoMetaCRMTO = new TipoMetaCRMTO();
            tipoMetaCRMTO.setFasesCRMEnum(fasesCRMEnum);
            int indiceFaseCRM = metaCRMTO.getListaTipoMetaVO().indexOf(tipoMetaCRMTO);
            if (indiceFaseCRM < 0) {
                metaCRMTO.getListaTipoMetaVO().add(tipoMetaCRMTO);
            } else {
                tipoMetaCRMTO = metaCRMTO.getListaTipoMetaVO().get(indiceFaseCRM);
            }

            int qtdMetasFase = fecharMetaVO.getMeta().intValue();

            //Manter integridade dos totalizadores.
            fecharMetaVO.setMeta((double) qtdMetasFase);
            tipoMetaCRMTO.setTotalMeta(tipoMetaCRMTO.getTotalMeta() + qtdMetasFase);

            int qtdMetaRealizada = fecharMetaVO.getQtdContatosComSucesso();
            int qtdContatoSemBaterMeta = fecharMetaVO.getQtdContatosSemSucesso();

            //Na fase de agendamentos não podem ser consideradas as ligações e afins, por isso, utiliza somente o próprio valor da meta;
            if (tipoMetaCRMTO.getFasesCRMEnum().equals(FasesCRMEnum.AGENDAMENTO)) {
                qtdMetaRealizada = fecharMetaVO.getMetaAtingida().intValue();
                qtdContatoSemBaterMeta = fecharMetaVO.getMetaAtingida().intValue();
            }

            tipoMetaCRMTO.setTotalMetaRealizada(tipoMetaCRMTO.getTotalMetaRealizada() + qtdMetaRealizada);
            tipoMetaCRMTO.setTotalContatoSemBaterMeta(tipoMetaCRMTO.getTotalContatoSemBaterMeta() + qtdContatoSemBaterMeta);
            tipoMetaCRMTO.getListaFecharMetaVO().add(fecharMetaVO);

            //Não adicionar no MetaCRMTO a fase de Indicação
            if (!fecharMetaVO.getFase().equals(FasesCRMEnum.INDICACOES)) {
                metaCRMTO.setTotalMeta(metaCRMTO.getTotalMeta() + fecharMetaVO.getMeta().intValue());
                metaCRMTO.setTotalMetaRealizada(metaCRMTO.getTotalMetaRealizada() + qtdMetaRealizada);
            }
        }
    }

    private void processarMeta(Date dataInicioMeta, Date dataFimMeta, int nivelMontarDados, Integer empresa, boolean apenasCalculadas, String codigosUsuario, List<MetaCRMTO> listaRetornar) throws Exception {
        StringBuilder sql = new StringBuilder();
        sql.append("select m.* \n");
        sql.append("from fecharMeta m \n");
        sql.append("inner join aberturaMeta am on am.codigo = m.aberturaMeta \n");
        if(!UteisValidacao.emptyString(codigosUsuario)){
            sql.append("inner join (").append(Uteis.criarTabelaIn(codigosUsuario)).append(")sqlIn on sqlIn.codigo = am.colaboradorResponsavel \n");
        }
        if (dataFimMeta != null) {
            sql.append("where am.dia >= '").append(getDataJDBC(dataInicioMeta)).append("'");
            sql.append(" and am.dia <= '").append(getDataJDBC(dataFimMeta)).append("'");
        } else {
            sql.append("where am.dia = '").append(getDataJDBC(dataInicioMeta)).append("'");
        }
        if (!UteisValidacao.emptyNumber(empresa)) {
            sql.append(" and am.empresa = ").append(empresa);
        }
        if(apenasCalculadas){
            sql.append(" and m.metacalculada ");
        }
        sql.append(" and m.identificadorMeta not in('").append(FasesCRMEnum.CONVERSAO_INDICADOS.getSigla())
                .append("','").append(FasesCRMEnum.CONVERSAO_AGENDADOS.getSigla())
                .append("','").append(FasesCRMEnum.CONVERSAO_EX_ALUNOS.getSigla())
                .append("','").append(FasesCRMEnum.CONVERSAO_VISITANTES_ANTIGOS.getSigla())
                .append("','").append(FasesCRMEnum.CONVERSAO_DESISTENTES.getSigla())
                .append("','").append(FasesCRMEnum.CRM_EXTRA.getSigla()).append("')");

        List<FecharMetaVO> listaMeta;
        try (PreparedStatement pst = con.prepareStatement(sql.toString())) {
            try (ResultSet rs = pst.executeQuery()) {

                listaMeta = montarDadosConsulta(rs, nivelMontarDados, con);
            }
        }
        FecharMetaDetalhado fecharMetaDetalhadoDAO = new FecharMetaDetalhado(con);
        HashMap<Integer, Integer> integridadeMeta = new HashMap<>();
        for (FecharMetaVO fecharMetaVO : listaMeta) {
            FasesCRMEnum fasesCRMEnum = FasesCRMEnum.getFasePorSigla(fecharMetaVO.getIdentificadorMeta());

            final Boolean metaAgendamento = fasesCRMEnum.equals(FasesCRMEnum.AGENDAMENTO);
            final Boolean metaIndicacoes = fasesCRMEnum.equals(FasesCRMEnum.INDICACOES);
            final Boolean metaAgendamentoLigacoes = fasesCRMEnum.equals(FasesCRMEnum.AGENDAMENTOS_LIGACOES);
            MetaCRMTO metaCRMTO = new MetaCRMTO();
            metaCRMTO.setTipoFaseCRM(fasesCRMEnum.getTipoFase());
            int indiceTipoMeta = listaRetornar.indexOf(metaCRMTO);
            if (indiceTipoMeta < 0) {
                listaRetornar.add(metaCRMTO);
            } else {
                metaCRMTO = listaRetornar.get(indiceTipoMeta);
            }
            TipoMetaCRMTO tipoMetaCRMTO = new TipoMetaCRMTO();
            tipoMetaCRMTO.setFasesCRMEnum(fasesCRMEnum);
            int indiceFaseCRM = metaCRMTO.getListaTipoMetaVO().indexOf(tipoMetaCRMTO);
            if (indiceFaseCRM < 0) {
                metaCRMTO.getListaTipoMetaVO().add(tipoMetaCRMTO);
            } else {
                tipoMetaCRMTO = metaCRMTO.getListaTipoMetaVO().get(indiceFaseCRM);
            }

            List<FecharMetaDetalhadoVO> metas = fecharMetaDetalhadoDAO.consultarFecharMetaDetalhados(fecharMetaVO.getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS);

            int totalizacaoMetaRealizadaAgendamentoIndicacoesLigacoes = fecharMetaDetalhadoDAO.contarTotalizacaoMetaRealizadaAgendamentoIndicacoesLigacoes(
                    null,
                    metaAgendamento,
                    metaAgendamentoLigacoes,
                    metaIndicacoes,
                    empresa, fecharMetaVO.getCodigo());

            int qtdMetasFase = metas.size();
            if (fecharMetaVO.getFase().equals(FasesCRMEnum.INDICACOES)) {
               // totalizacaoMetaRealizadaAgendamentoIndicacoesLigacoes = metas.size();
                qtdMetasFase = fecharMetaVO.getMeta().intValue();
            }

            //Manter integridade dos totalizadores.
            integridadeMeta.put(fecharMetaVO.getCodigo(), qtdMetasFase);
            fecharMetaVO.setMeta((double) qtdMetasFase);
            tipoMetaCRMTO.setTotalMeta(tipoMetaCRMTO.getTotalMeta() + qtdMetasFase);

            if (tipoMetaCRMTO.getFasesCRMEnum().equals(FasesCRMEnum.AGENDAMENTO)) {
                int cliMeta = metas.size();
                for (FecharMetaDetalhadoVO clientesMeta : metas) {
                    if (!UteisValidacao.emptyString(clientesMeta.getTipoAgendamento()) && clientesMeta.getTipoAgendamento().equals("Ligação")) {
                        cliMeta--;
                    }
                }
                integridadeMeta.put(fecharMetaVO.getCodigo(), cliMeta);
                fecharMetaVO.setMeta((double) cliMeta);
                tipoMetaCRMTO.setTotalMeta((tipoMetaCRMTO.getTotalMeta() - metas.size()) + cliMeta);
            }

            if (!fecharMetaVO.getFase().equals(FasesCRMEnum.INDICACOES)) { //Não adicionar no MetaCRMTO a fase de Indicação
                metaCRMTO.setTotalMeta(metaCRMTO.getTotalMeta() + fecharMetaVO.getMeta().intValue());
                metaCRMTO.setTotalMetaRealizada(metaCRMTO.getTotalMetaRealizada() + totalizacaoMetaRealizadaAgendamentoIndicacoesLigacoes);
            }

            tipoMetaCRMTO.setTotalMetaRealizada(tipoMetaCRMTO.getTotalMetaRealizada() + totalizacaoMetaRealizadaAgendamentoIndicacoesLigacoes);
            tipoMetaCRMTO.getListaFecharMetaVO().add(fecharMetaVO);

        }
        for (Map.Entry<Integer, Integer> entryIntegridadeMeta : integridadeMeta.entrySet()) {
            alterarSomenteCampoMetaFechamentoDia(entryIntegridadeMeta.getKey(), entryIntegridadeMeta.getValue());
        }
        fecharMetaDetalhadoDAO = null;
    }

    private int consultarRepescagemAgendamentos(FecharMetaVO fecharMetaVO) throws SQLException {
        String sql = "select count(fmd.codigo) as qtd\n" +
                "from fecharmetadetalhado fmd\n" +
                "    left join agenda ag on ag.codigo = fmd.codigoorigem and fmd.origem = 'AGENDA'\n" +
                "where fmd.fecharmeta = "+fecharMetaVO.getCodigo()+"\n" +
                "  and fmd.repescagem is true\n" +
                "  and ag.tipoagendamento <> 'LI'";

        int qtdRepescagemFecharMeta = 0;
        try (PreparedStatement ce = con.prepareStatement(sql)) {
            try (ResultSet cers = ce.executeQuery()) {
                if (cers.next()) {
                    qtdRepescagemFecharMeta = cers.getInt("qtd");
                }
            }
        }
        return qtdRepescagemFecharMeta;
    }


    private void processarMetaExtra(Date dataInicioMeta, Date dataFimMeta, int nivelMontarDados, Integer empresa, String codigosUsuario, List<MetaCRMTO> listaRetornar) throws Exception {
        StringBuilder extra = new StringBuilder();

        extra.append("select m.*,md.metaExtraIndividual \n");
        extra.append("from fecharMeta m \n");
        extra.append("inner join aberturaMeta am on am.codigo = m.aberturaMeta \n");
        extra.append("inner join maladireta md ON m.maladireta = md.codigo and md.excluida = false and coalesce(md.vigenteate::date, current_date) >= current_date\n");
        if (!UteisValidacao.emptyString(codigosUsuario)) {
            extra.append("inner join (").append(Uteis.criarTabelaIn(codigosUsuario)).append(")sqlIn on sqlIn.codigo = am.colaboradorResponsavel \n");
        }

        StringBuilder sqlWhere = new StringBuilder();

        sqlWhere.append("where m.identificadorMeta in('").append(FasesCRMEnum.CRM_EXTRA.getSigla()).append("') \n");
        if (dataFimMeta != null) {
            sqlWhere.append("and am.dia >= '").append(getDataJDBC(dataInicioMeta)).append("' \n");
            sqlWhere.append("and am.dia <= '").append(getDataJDBC(dataFimMeta)).append("' \n");
        } else {
            sqlWhere.append("and am.dia = '").append(getDataJDBC(dataInicioMeta)).append("' \n");
        }
        if (!UteisValidacao.emptyNumber(empresa)) {
            sqlWhere.append("and am.empresa = ").append(empresa).append(" \n");
        }

        if (dataFimMeta != null) {
            extra.append("inner join ( ");
            extra.append("  select ");
            extra.append("    abm.colaboradorresponsavel, ");
            extra.append("    fm.meta, ");
            extra.append("    max(fm.codigo) as codigo ");
            extra.append("  from ");
            extra.append("    fecharMeta fm ");
            extra.append("  left outer join aberturameta abM ON fm.aberturameta = abM.codigo ");
            extra.append(sqlWhere.toString().replaceAll(" am.", " abm.").replaceAll(" m.", " fm."));
            extra.append("  group by abm.colaboradorresponsavel, fm.codigo ");
            extra.append(") x on x.codigo = m.codigo and x.meta = m.meta and x.colaboradorresponsavel = am.colaboradorresponsavel ");
        }

        extra.append(sqlWhere);

        if (dataFimMeta != null) {
            extra.append("order by m.dataregistro asc");
        }

        List<FecharMetaVO> listaMetaCRMExtra;
        try (PreparedStatement ce = con.prepareStatement(extra.toString())) {
            try (ResultSet cers = ce.executeQuery()) {
                listaMetaCRMExtra = new ArrayList<>();
                while (cers.next()) {
                    FecharMetaVO obj = montarDados(cers, nivelMontarDados, con);
                    obj.getMalaDiretaCRMExtra().setMetaExtraIndividual(cers.getBoolean("metaExtraIndividual"));
                    listaMetaCRMExtra.add(obj);
                }
            }
        }

        for (FecharMetaVO fecharMetaVO : listaMetaCRMExtra) {
            FasesCRMEnum fasesCRMEnum = FasesCRMEnum.getFasePorSigla(fecharMetaVO.getIdentificadorMeta());
            if (fasesCRMEnum != null && fasesCRMEnum.equals(FasesCRMEnum.CRM_EXTRA)) {
                if (UteisValidacao.emptyNumber(fecharMetaVO.getMeta()) && UteisValidacao.emptyNumber(fecharMetaVO.getMetaAtingida())) {
                    continue;
                }
            }

            MetaCRMTO metaCRMTO = new MetaCRMTO();
            metaCRMTO.setTipoFaseCRM(fasesCRMEnum.getTipoFase());
            int indiceTipoMeta = listaRetornar.indexOf(metaCRMTO);
            if (indiceTipoMeta < 0) {
                listaRetornar.add(metaCRMTO);
            } else {
                metaCRMTO = listaRetornar.get(indiceTipoMeta);
            }

            TipoMetaCRMTO tipoMetaCRMTO = new TipoMetaCRMTO();
            tipoMetaCRMTO.setFasesCRMEnum(fasesCRMEnum);
            tipoMetaCRMTO.setNomeMeta(fecharMetaVO.getNomeMeta());

            tipoMetaCRMTO = setarNaListaTipoMetaCRMTO(metaCRMTO.getListaTipoMetaVO(), tipoMetaCRMTO);

            tipoMetaCRMTO.getListaFecharMetaVO().add(fecharMetaVO);

            tipoMetaCRMTO.setTotalMeta(tipoMetaCRMTO.getTotalMeta() + fecharMetaVO.getMeta().intValue());
            tipoMetaCRMTO.setTotalMetaRealizada(tipoMetaCRMTO.getTotalMetaRealizada() + fecharMetaVO.getMetaAtingida().intValue() + fecharMetaVO.getRepescagem().intValue());

            metaCRMTO.setTotalMeta(metaCRMTO.getTotalMeta() + fecharMetaVO.getMeta().intValue());
            metaCRMTO.setTotalMetaRealizada(metaCRMTO.getTotalMetaRealizada() + fecharMetaVO.getMetaAtingida().intValue() + fecharMetaVO.getRepescagem().intValue());
        }
    }

    private TipoMetaCRMTO setarNaListaTipoMetaCRMTO( List<TipoMetaCRMTO> l, TipoMetaCRMTO o ){

        for (TipoMetaCRMTO oAux: l) {
            if(oAux.getNomeMeta().equalsIgnoreCase(o.getNomeMeta())){
                return oAux;
            }
        }

        l.add(o);
        return o;
    }

    /**
     * Mï¿½todo responsavel por alterar apenas a justificativa
     * no momento de fazer o Fechamento do Dia.
     *
     * @param obj
     * @throws Exception
     * <AUTHOR>
     */
    public void alterarSomenteCampoJustificativaFechamentoDia(FecharMetaVO obj) throws Exception {
        try {
            obj.realizarUpperCaseDados();
            String sql = "UPDATE FecharMeta set justificativa=? WHERE ((codigo = ?))";
            PreparedStatement sqlAlterar = con.prepareStatement(sql);
            sqlAlterar.setString(1, obj.getJustificativa());
            sqlAlterar.setInt(2, obj.getCodigo().intValue());
            sqlAlterar.execute();
        } catch (Exception e) {
            throw e;
        }
    }

    public void alterarSomenteCampoMetaFechamentoDia(Integer codFecharMeta, int metaAtualizada) throws Exception {
        String sql = "UPDATE FecharMeta set meta = ? WHERE codigo = ?";
        try (PreparedStatement sqlAlterar = con.prepareStatement(sql)) {
            int i = 0;
            sqlAlterar.setInt(++i, metaAtualizada);
            sqlAlterar.setInt(++i, codFecharMeta);
            sqlAlterar.execute();
        }
    }

    /**
     * Operaï¿½ï¿½o responsï¿½vel por excluir no BD um objeto da classe
     * <code>FecharMetaVO</code>. Sempre localiza o registro a ser excluï¿½do
     * atravï¿½s da chave primï¿½ria da entidade. Primeiramente verifica a conexï¿½o
     * com o banco de dados e a permissï¿½o do usuï¿½rio para realizar esta operacï¿½o
     * na entidade. Isto, atravï¿½s da operaï¿½ï¿½o <code>excluir</code> da
     * superclasse.
     *
     * @param obj Objeto da classe <code>FecharMetaVO</code> que serï¿½ removido
     *            no banco de dados.
     * @throws Exception Caso haja problemas de conexï¿½o ou restriï¿½ï¿½o de acesso.
     */
    public void excluir(FecharMetaVO obj) throws Exception {
        try {
            con.setAutoCommit(false);
            excluirCRM(getIdEntidade());
            String sql = "DELETE FROM FecharMeta WHERE ((codigo = ?))";
            PreparedStatement sqlExcluir = con.prepareStatement(sql);
            sqlExcluir.setInt(1, obj.getCodigo().intValue());
            sqlExcluir.execute();
            getFacade().getFecharMetaDetalhado().excluirFecharMetaDetalhados(obj.getCodigo());
            con.commit();
        } catch (Exception e) {
            con.rollback();
            con.setAutoCommit(true);
            throw e;
        } finally {
            con.setAutoCommit(true);
        }
    }

    /**
     * Mï¿½todo que exclui apenas FecharMetaVO pelo codigo
     *
     * @param obj
     * @throws Exception
     */
    public void excluirSomenteMaeFecharMeta(FecharMetaVO obj) throws Exception {
        try {
            excluirCRM(getIdEntidade());
            String sql = "DELETE FROM FecharMeta WHERE ((codigo = ?))";
            PreparedStatement sqlExcluir = con.prepareStatement(sql);
            sqlExcluir.setInt(1, obj.getCodigo().intValue());
            sqlExcluir.execute();
        } catch (Exception e) {
            throw e;
        } finally {
        }
    }

    /**
     * Metodo usado para criar a entidade fecharMetaDetalhado e chamar o metodo
     * para persitir.
     *
     * @param identificadorMeta      campo para realizar a consulta da entidade mae de
     *                               fechaMetaDetalhado
     * @param colaboradorResponsavel campo para realizar a consulta da entidade mae de
     *                               fechaMetaDetalhado
     * @param passivo                Se a inclusao envolver o passivo entao passe o codigo senao
     *                               passe 0
     * @param indicado               Se a inclusao envolver o indicado entao passe o codigo senao
     *                               passe 0
     * @param cliente                Se a inclusao envolver o cliente entao passe o codigo senao
     *                               passe 0
     * @param hist                   manter o historico.
     */
    public FecharMetaDetalhadoVO executarPersistenciaFechaMetaDetalhado(Date dia, String identificadorMeta, Integer colaboradorResponsavel, Integer passivo, Integer indicado, Integer cliente, Integer empresa, HistoricoContatoVO hist, Boolean atualizaMeta) throws Exception {
        FecharMetaVO fecharMeta = consultarPorIdentificadorMetaPorDiaPorColaborador(identificadorMeta, dia, colaboradorResponsavel, false, empresa, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
        FecharMetaDetalhadoVO obj = new FecharMetaDetalhadoVO();
        if (fecharMeta.getMetaCalculada()) {
            if (atualizaMeta) {
                fecharMeta.setMetaAtingida(fecharMeta.getMetaAtingida() + 1);
                alteraSemSubordinada(fecharMeta);
            }
            obj.setFecharMeta(fecharMeta);
            if (hist != null && hist.getCodigo().intValue() != 0) {
                obj.setHistoricoContatoVO(hist);
            }
            if (passivo.intValue() != 0) {
                obj.setCodigoOrigem(passivo);
                obj.setOrigem("PASSIVO");
                obj.getPassivo().setCodigo(passivo);
                obj.setObteveSucesso(true);
            } else if (indicado.intValue() != 0) {
                obj.setCodigoOrigem(indicado);
                obj.setOrigem("INDICADO");
                obj.getIndicado().setCodigo(indicado);
                obj.setObteveSucesso(true);
            }
            FecharMetaDetalhado fecharMetaDetalhado = new FecharMetaDetalhado(getCon());
            fecharMetaDetalhado.incluir(obj);
            fecharMetaDetalhado = null;
        }
        return obj;
    }

    // Regra de negocio que atualizar a meta passivo e indicaï¿½ï¿½o

    /**
     * Metodo usado somente para caso de agendamento para criar a entidade
     * fecharMetaDetalhado e chamar o metodo para persitir.
     */
    public void executarPersistenciaFechaMetaDetalhadoParaAgendamento(Date dia, AgendaVO agenda, Integer colaboradorResponsavel, Integer passivo, Integer indicado, Integer cliente, HistoricoContatoVO hist) throws Exception {
        FecharMetaVO fecharMeta;
        try {
            String fase =  (hist.getFase().equals("LA")) ? "LA" : "AG";
            fecharMeta = consultarPorIdentificadorMetaPorDiaPorColaborador(fase, dia, colaboradorResponsavel, false, agenda.getEmpresa(), Uteis.NIVELMONTARDADOS_DADOSBASICOS);
            adicionarAgendamentoNaMeta(agenda, passivo, indicado, cliente, hist, fecharMeta);
        } catch (Exception e) {}
    }

    /*Cria os Agendados de Amanhã*/
    public void executarPersistenciaFechaMetaDetalhadoParaAgendadosAmanhã(Date dia, AgendaVO agenda, Integer colaboradorResponsavel, Integer passivo, Integer indicado, Integer cliente, HistoricoContatoVO hist) throws Exception {
        FecharMetaVO fecharMeta;
        try {
            String fase =  "LA";
            fecharMeta = consultarPorIdentificadorMetaPorDiaPorColaborador(fase, dia, colaboradorResponsavel, false, agenda.getEmpresa(), Uteis.NIVELMONTARDADOS_DADOSBASICOS);
            adicionarAgendamentoNaMeta(agenda, passivo, indicado, cliente, hist, fecharMeta);
        } catch (Exception e) {}
    }

    private void adicionarAgendamentoNaMeta(AgendaVO agenda, Integer passivo, Integer indicado,
                                            Integer cliente, HistoricoContatoVO hist, FecharMetaVO fecharMeta) throws Exception {
        if (fecharMeta.getMetaCalculada()) {
            fecharMeta.setMeta(fecharMeta.getMeta() + 1);
            fecharMeta.calcularPorcentagem();
            alteraSemSubordinada(fecharMeta);
            FecharMetaDetalhadoVO obj = new FecharMetaDetalhadoVO();
            obj.setOrigem("AGENDA");
            obj.setCodigoOrigem(agenda.getCodigo());
            obj.setFecharMeta(fecharMeta);
            if (passivo != 0) {
                obj.getPassivo().setCodigo(passivo);
            } else if (indicado != 0) {
                obj.getIndicado().setCodigo(indicado);
            } else {
                obj.getCliente().setCodigo(cliente);
            }
            getFacade().getFecharMetaDetalhado().incluir(obj);
        }
    }

    /**
     * Mï¿½todo responsavel por validar qual a fase e qual meta deve ser
     * atualizada a meta do dia ou a de repescagem Quando ï¿½ realizar um
     * historico contato do tipo agendamento.
     *
     * <AUTHOR>
     */
    public void executarValidacaoQuandoGravaAgendaAlcancaMetaAtingida(Date dia, AgendaVO agenda, String identificadorMeta, Integer codigoFechaMetaDetalhado, Boolean incluir) throws Exception {
        FecharMetaDetalhadoVO fecharMetaDetalhado = getFacade().getFecharMetaDetalhado().consultarPorChavePrimaria(codigoFechaMetaDetalhado, Uteis.NIVELMONTARDADOS_METACOLABORADORRESPONSAVEL);

        if (incluir && !fecharMetaDetalhado.getObteveSucesso() && fecharMetaDetalhado.getFecharMeta().getAberturaMetaVO().getMetaEmAberto() && (!identificadorMeta.equals("AG") && !identificadorMeta.equals("IN") && !identificadorMeta.equals("LA"))) {
             executarAtualizacaoMetaAtingida(fecharMetaDetalhado, 0, 1L, true, !identificadorMeta.equals("AL"));
             if(agenda.getTipoAgendamento().equals("VI") && Calendario.igual(agenda.getDataAgendamento(), Calendario.hoje())){
                 //evolui a meta, agora só é atingida se comparecer na visita.
                 fecharMetaDetalhado.setObteveSucesso(false);
                 fecharMetaDetalhado.setTeveContato(true);
                 getFacade().getFecharMetaDetalhado().alterar(fecharMetaDetalhado);
             }

        } else if (incluir && !fecharMetaDetalhado.getObteveSucesso() && Uteis.getCompareData(fecharMetaDetalhado.getFecharMeta().getDataRegistro(), dia) <= 0 && !fecharMetaDetalhado.getFecharMeta().getAberturaMetaVO().getMetaEmAberto() && (!identificadorMeta.equals("AG") && !identificadorMeta.equals("IN") && !identificadorMeta.equals("LA"))) {
            executarAtualizacaoMetaRepescagem(fecharMetaDetalhado, 0, 1L, true);

        } else if (!incluir && !fecharMetaDetalhado.getObteveSucesso() && agenda.getCodigo().intValue() == 0 && fecharMetaDetalhado.getFecharMeta().getAberturaMetaVO().getMetaEmAberto() && (!identificadorMeta.equals("AG") && !identificadorMeta.equals("IN") && !identificadorMeta.equals("LA"))) {
            executarAtualizacaoMetaAtingida(fecharMetaDetalhado, 0, 1L, true, !identificadorMeta.equals("AL"));

        } else if (!incluir && !fecharMetaDetalhado.getObteveSucesso() && agenda.getCodigo().intValue() == 0 && Uteis.getCompareData(fecharMetaDetalhado.getFecharMeta().getDataRegistro(), dia) <= 0 && !fecharMetaDetalhado.getFecharMeta().getAberturaMetaVO().getMetaEmAberto() && (!identificadorMeta.equals("AG") && !identificadorMeta.equals("IN") && !identificadorMeta.equals("LA"))) {
            executarAtualizacaoMetaRepescagem(fecharMetaDetalhado, 0, 1L, true);
        } else if (incluir && !fecharMetaDetalhado.getObteveSucesso() && !fecharMetaDetalhado.getFecharMeta().getAberturaMetaVO().getMetaEmAberto() && (!identificadorMeta.equals("IN") && !identificadorMeta.equals("LA"))) {
            executarAtualizacaoMetaAtingida(fecharMetaDetalhado, 0, 1L, true, !identificadorMeta.equals("AL"));
            fecharMetaDetalhado.setTeveContato(true);
            getFacade().getFecharMetaDetalhado().alterar(fecharMetaDetalhado);
        }

    }

    /**
     * Mï¿½todo responsavel por validar qual a fase e qual meta deve ser
     * atualizada a meta do dia ou a de repescagem Quando ï¿½ realizar um
     * historico contato do tipo Registro simple ou objecao.
     *
     * <AUTHOR>
     */
    //Apenas metas que batem com o Simples Registro
    public void executarValidacaoQuandoGravaHistoricoContatoAlcancaMetaAtingida(Date dia,
                                                                                String identificadorMeta,
                                                                                Integer codigoFechaMetaDetalhado,
                                                                                Boolean metaEmAberta,
                                                                                FecharMetaDetalhado fecharMetaDetalhadoDAO) throws Exception {
        FecharMetaDetalhadoVO fecharMetaDetalhado = fecharMetaDetalhadoDAO.consultarPorChavePrimaria(codigoFechaMetaDetalhado,
                Uteis.NIVELMONTARDADOS_DADOSENTIDADESPRINCIPAIS);

        ConfiguracaoSistemaCRMVO config = new ConfiguracaoSistemaCRM(con).consultarPorChavePrimaria(1, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
        if (config.isBaterMetaTodasAcoes()) {
            if (!fecharMetaDetalhado.getObteveSucesso() && metaEmAberta) {
                fecharMetaDetalhadoDAO.baterMetaPorFase(fecharMetaDetalhado.getCliente().getCodigo(), null, dia, identificadorMeta, 0, 0, 0);
            } else if (Uteis.getCompareData(fecharMetaDetalhado.getFecharMeta().getDataRegistro(), dia) <= 0 && !metaEmAberta) {
                executarAtualizacaoMetaRepescagem(fecharMetaDetalhado, 0, 1L, true);
            }
        } else {
            if (!fecharMetaDetalhado.getObteveSucesso() && metaEmAberta && (identificadorMeta.equals("AN")
                    || identificadorMeta.equals("PV")
                    || identificadorMeta.equals("RI")
                    || identificadorMeta.equals("FA")
                    || identificadorMeta.equals("SF")
                    || identificadorMeta.equals("SA")
                    || identificadorMeta.equals("LA")
                    || identificadorMeta.equals("CR")
                    || identificadorMeta.equals(FasesCRMEnum.ALUNO_GYMPASS.getSigla())
                    || identificadorMeta.equals(FasesCRMEnum.ALUNO_ULTIMO_ACESSO_GYMPASS.getSigla())
                    || identificadorMeta.equals(FasesCRMEnum.FILA_ESPERA_TURMA_CRM.getSigla())
                   // || identificadorMeta.equals(FasesCRMEnum.VISITA_RECORRENTE.getSigla())
                    || identificadorMeta.equals(FasesCRMEnum.CRM_EXTRA.getSigla()))) {
                fecharMetaDetalhadoDAO.baterMetaPorFase(fecharMetaDetalhado.getCliente().getCodigo(), null, dia, identificadorMeta, 0, 0, 0);
            } else if (Uteis.getCompareData(fecharMetaDetalhado.getFecharMeta().getDataRegistro(), dia) <= 0 && !metaEmAberta && (
                    identificadorMeta.equals("AN")
                            || identificadorMeta.equals("PV")
                            || identificadorMeta.equals("RI")
                            || identificadorMeta.equals("FA")
                            || identificadorMeta.equals("SA")
                            || identificadorMeta.equals("SF")
                            || identificadorMeta.equals("LA")
                            || identificadorMeta.equals("CR")
                            || identificadorMeta.equals(FasesCRMEnum.ALUNO_GYMPASS.getSigla())
                            || identificadorMeta.equals(FasesCRMEnum.ALUNO_ULTIMO_ACESSO_GYMPASS.getSigla())
                            || identificadorMeta.equals(FasesCRMEnum.FILA_ESPERA_TURMA_CRM.getSigla())
                           // || identificadorMeta.equals(FasesCRMEnum.VISITA_RECORRENTE.getSigla())
                            || identificadorMeta.equals(FasesCRMEnum.CRM_EXTRA.getSigla()))) {
                executarAtualizacaoMetaRepescagem(fecharMetaDetalhado, 0, 1L, true);
            } else  if (fecharMetaDetalhado.getObteveSucesso() && metaEmAberta && (identificadorMeta.equals("AN"))){
                fecharMetaDetalhadoDAO.baterMetaPorFase(fecharMetaDetalhado.getCliente().getCodigo(), null, dia, identificadorMeta, 0, 0, 0);
            }
        }
    }

    public void executarAtualizacaoRespescagemObjecao(
            Integer codigoFechaMetaDetalhado,
            FecharMetaDetalhado fecharMetaDetalhadoDAO) throws Exception {
        FecharMetaDetalhadoVO fecharMetaDetalhado = fecharMetaDetalhadoDAO.consultarPorChavePrimaria(codigoFechaMetaDetalhado,
                Uteis.NIVELMONTARDADOS_DADOSENTIDADESPRINCIPAIS);


        executarAtualizacaoMetaRepescagem(fecharMetaDetalhado, 0, 1L, true);
    }

    /**
     * Metodo responsavel por atualizar a entidade fecharMeta para os
     * identificadores que sao comuns.
     *
     * <AUTHOR>
     */
    public void executarAtualizacaoMetaAtingidaPorIndicado(FecharMetaVO fecharMetaVO, Long quantidade, Boolean somar) throws Exception {
        if (fecharMetaVO.getMetaCalculada()) {
            if (somar) {
                fecharMetaVO.setMetaAtingida(fecharMetaVO.getMetaAtingida() + quantidade);
            } else {
                fecharMetaVO.setMetaAtingida(fecharMetaVO.getMetaAtingida() - quantidade);
            }
            fecharMetaVO.calcularPorcentagem();
            alteraSemSubordinada(fecharMetaVO);
        }

    }

    /**
     * Metodo responsavel por atualizar a meta atingida, calcular a porcentagem das metas, fazer alteraï¿½ï¿½o
     * no banco do campo obtve sucesso caso ainda nï¿½o tenha obtido sucesso.
     *
     * @param fecharMetaDetalhadoVO
     * @param contrato
     * @param quantidade
     * @param somar
     * @throws Exception
     * <AUTHOR>
     */
    public void executarAtualizacaoMetaAtingida(FecharMetaDetalhadoVO fecharMetaDetalhadoVO, Integer contrato, Long quantidade, Boolean somar, Boolean alterarFecharMeta) throws Exception {
        if (!fecharMetaDetalhadoVO.getObteveSucesso()) {
            if (somar) {
                if(alterarFecharMeta){
                    fecharMetaDetalhadoVO.getFecharMeta().setMetaAtingida(fecharMetaDetalhadoVO.getFecharMeta().getMetaAtingida() + quantidade);
                }
                fecharMetaDetalhadoVO.setObteveSucesso(true);
            } else {
                if(alterarFecharMeta){
                    fecharMetaDetalhadoVO.getFecharMeta().setMetaAtingida(fecharMetaDetalhadoVO.getFecharMeta().getMetaAtingida() - quantidade);
                }
                fecharMetaDetalhadoVO.setObteveSucesso(false);
            }
            fecharMetaDetalhadoVO.getFecharMeta().calcularPorcentagem();
            alteraSemSubordinada(fecharMetaDetalhadoVO.getFecharMeta());
        }
        FecharMetaDetalhado fmdDAO = new FecharMetaDetalhado(con);
        fmdDAO.alterarSomenteCamposObteveSucessoAndContrato(fecharMetaDetalhadoVO.getObteveSucesso(), contrato, fecharMetaDetalhadoVO.getCodigo(), fecharMetaDetalhadoVO.getObservacao());
        fmdDAO = null;
    }

    /**
     * Metodo responsavel por atualizar a meta atingida, calcular a porcentagem das metas, fazer alteraï¿½ï¿½o
     * no banco do campo obtve sucesso caso ainda nï¿½o tenha obtido sucesso.
     *
     * <AUTHOR>
     */
    public void executarAtualizacaoMetaAtingidaPessoasComparecidas(FecharMetaDetalhadoVO fecharMetaDetalhadoVO, Integer contrato,
                                                                   Long quantidade, Boolean somar) throws Exception {
        if (somar) {
            if (!fecharMetaDetalhadoVO.getObteveSucesso()) {
                fecharMetaDetalhadoVO.getFecharMeta().setMetaAtingida(fecharMetaDetalhadoVO.getFecharMeta().getMetaAtingida() + quantidade);
                fecharMetaDetalhadoVO.setObteveSucesso(true);
            }
        } else {
            fecharMetaDetalhadoVO.getFecharMeta().setMetaAtingida(fecharMetaDetalhadoVO.getFecharMeta().getMetaAtingida() - quantidade);
            fecharMetaDetalhadoVO.setObteveSucesso(false);
        }
        fecharMetaDetalhadoVO.getFecharMeta().calcularPorcentagem();
        alteraSemSubordinada(fecharMetaDetalhadoVO.getFecharMeta());
        FecharMetaDetalhado fecharMetaDetalhadoDAO = new FecharMetaDetalhado(con);
        fecharMetaDetalhadoDAO.alterarSomenteCamposObteveSucessoAndContrato(fecharMetaDetalhadoVO.getObteveSucesso(),
                contrato, fecharMetaDetalhadoVO.getCodigo(), fecharMetaDetalhadoVO.getObservacao());
        fecharMetaDetalhadoDAO = null;
    }

    public void executarAtualizacaoMetaAgendadoQuandoRealiaCompraDeUmcontrato(ContratoVO contrato, AgendaVO agendaMarcadaPresenca, Integer indicado, Integer passivo, Integer cliente) throws Exception {
        FecharMetaDetalhado fmdDAO = new FecharMetaDetalhado(con);
        FecharMetaDetalhadoVO fecharMetaDetalhado =fmdDAO.consultarPorCodigoColaboradorResponsavelIdentificadorMeta(agendaMarcadaPresenca.getColaboradorResponsavel().getCodigo(), "AG", indicado, passivo, cliente, false, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
       
        if (fecharMetaDetalhado.getCodigo().intValue() != 0) {
            if (!fecharMetaDetalhado.getFecharMeta().getAberturaMetaVO().getMetaEmAberto()) {
                executarAtualizacaoMetaRepescagem(fecharMetaDetalhado, contrato.getCodigo(), 1L, true);
            } else {
                executarAtualizacaoMetaAtingida(fecharMetaDetalhado, contrato.getCodigo(), 1L, true, !agendaMarcadaPresenca.getTipoAgendamento().equals("LI"));
            }
        }
        fmdDAO = null;
    }

    public void executarAtualizacaoMetaRepescagem(FecharMetaDetalhadoVO fecharMetaDetalhadoVO, Integer contrato, Long quantidade, Boolean somar) throws Exception {
        boolean ligacao = false;
        FecharMetaDetalhado fmdDAO = new FecharMetaDetalhado(con);
        if (fecharMetaDetalhadoVO.getOrigem().equals("AGENDA")) {
            if(fecharMetaDetalhadoVO.getAgenda() != null){
                if (fecharMetaDetalhadoVO.getAgenda().getTipoAgendamento().equals("LI")) {
                    ligacao = true;
                }
            }else{
                Agenda agendaDAO = new Agenda(con);
                AgendaVO agendaVO = agendaDAO.consultarPorChavePrimaria(fecharMetaDetalhadoVO.getCodigoOrigem(), Uteis.NIVELMONTARDADOS_DADOSBASICOS);
                if (agendaVO.getTipoAgendamento().equals("LI")) {
                    ligacao = true;
                }
                agendaDAO = null;
            }
        }

        if (!fecharMetaDetalhadoVO.getObteveSucesso()) {
            if (somar) {
                fecharMetaDetalhadoVO.getFecharMeta().setRepescagem(fecharMetaDetalhadoVO.getFecharMeta().getRepescagem() + quantidade);
            } else {
                fecharMetaDetalhadoVO.getFecharMeta().setRepescagem(fecharMetaDetalhadoVO.getFecharMeta().getRepescagem() - quantidade);
            }
            if (!ligacao) {
                alteraSemSubordinada(fecharMetaDetalhadoVO.getFecharMeta());
            }
            fecharMetaDetalhadoVO.setObteveSucesso(true);

            fmdDAO.alterarSomenteCampoRepescagemFecharMetaDetalhado(somar, fecharMetaDetalhadoVO.getCodigo());
        }
        fmdDAO.alterarSomenteCamposObteveSucessoAndContrato(true, contrato, fecharMetaDetalhadoVO.getCodigo(), fecharMetaDetalhadoVO.getObservacao());
        fmdDAO = null;
    }

    public void executarAtualizacaoMetaAtingidaRegraParaAgendamento(FecharMetaDetalhadoVO fecharMetaDetalhadoVO, Long quantidade, Boolean somar) throws Exception {
        if (!fecharMetaDetalhadoVO.getObteveSucesso()) {
            if (fecharMetaDetalhadoVO.getFecharMeta().getCodigo().intValue() != 0) {
                if (somar) {
                    fecharMetaDetalhadoVO.getFecharMeta().setMetaAtingida(fecharMetaDetalhadoVO.getFecharMeta().getMetaAtingida() + quantidade);
                    fecharMetaDetalhadoVO.getFecharMeta().setMeta(fecharMetaDetalhadoVO.getFecharMeta().getMeta() + quantidade);
                } else {
                    fecharMetaDetalhadoVO.getFecharMeta().setMetaAtingida(fecharMetaDetalhadoVO.getFecharMeta().getMetaAtingida() - quantidade);
                    fecharMetaDetalhadoVO.getFecharMeta().setMeta(fecharMetaDetalhadoVO.getFecharMeta().getMeta() - quantidade);
                }

                fecharMetaDetalhadoVO.getFecharMeta().calcularPorcentagem();
                alteraSemSubordinada(fecharMetaDetalhadoVO.getFecharMeta());
            }
        }
    }

    public void executarAtualizacaoMetaRegraParaAgendamentoPorPassivo(FecharMetaDetalhadoVO fecharMetaDetalhadoVO, Long quantidade, Boolean somar) throws Exception {
        if (somar) {
            fecharMetaDetalhadoVO.getFecharMeta().setMeta(fecharMetaDetalhadoVO.getFecharMeta().getMeta() + quantidade);
        } else {
            fecharMetaDetalhadoVO.getFecharMeta().setMeta(fecharMetaDetalhadoVO.getFecharMeta().getMeta() - quantidade);
        }
        fecharMetaDetalhadoVO.getFecharMeta().calcularPorcentagem();
        alteraSemSubordinada(fecharMetaDetalhadoVO.getFecharMeta());

    }

    /**
     * Metodo responsavel por atualizar a entidade fecharMeta quando o
     * identificador ï¿½ Meta de faturamento ou Meta de Quantidade de venda pois
     * ele tem uma regra de negocio diferente dos outros fecharMeta
     *
     * @param contrato Entidade Contrato
     * @throws Exception Caso haja problemas de conexï¿½o, restriï¿½ï¿½o de acesso ou
     *                   validaï¿½ï¿½o de dados.
     * <AUTHOR>
     */
    public void executarAtualizacaoMetaFaturamentoAndVenda(ContratoVO contrato) throws Exception {
        Usuario usuarioDao = new Usuario(con);
        UsuarioVO usuario = usuarioDao.consultarPorCodigoColaborador(contrato.getConsultor().getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS);
        usuarioDao = null;
        if (usuario.getCodigo().intValue() != 0) {
            Cliente clienteDao = new Cliente(con);
            ClienteVO cliente = clienteDao.consultarPorCodigoPessoa(contrato.getPessoa().getCodigo(), Uteis.NIVELMONTARDADOS_ABERTURAMETA);
            clienteDao = null;
            executarPersistenciaFechaMetaDetalhadoFaturamentoOrVenda("MF", usuario.getCodigo(), cliente.getCodigo(), contrato, contrato.getEmpresa().getCodigo());
            executarPersistenciaFechaMetaDetalhadoFaturamentoOrVenda("MQ", usuario.getCodigo(), cliente.getCodigo(), contrato, contrato.getEmpresa().getCodigo());

        }
    }

    /**
     * Metodo responsavel por persistir a entidade FecharMetaDetalhado quando um
     * cliente realizar um contrato e ja atualizo a entidade fecharMeta porem
     * para que tudo de certo antes e realizado uma consultar se existe a meta.
     *
     * <AUTHOR>
     */
    public void executarPreencherMetaDetalhadoFaturamentoOrVenda(FecharMetaVO fecharMeta, Integer codigoColaborador, Date dia) throws Exception {
        try (ResultSet resultado = getFacade().getFecharMetaDetalhado().consultarClientesPeloCodigoConsultorAndDataLancamentoContrato(codigoColaborador, dia)) {
            while (resultado.next()) {
                FecharMetaDetalhadoVO obj = new FecharMetaDetalhadoVO();
                obj.setFecharMeta(fecharMeta);
                obj.setCodigoOrigem(resultado.getInt("cliente"));
                obj.setOrigem("CLIENTE");
                obj.getCliente().setCodigo(resultado.getInt("cliente"));
                obj.setObteveSucesso(true);
                obj.getContratoVO().setCodigo(resultado.getInt("contrato"));
                if (fecharMeta.getIdentificadorMeta().equals("MF")) {
                    fecharMeta.setMetaAtingida(Uteis.arredondarForcando2CasasDecimais(fecharMeta.getMetaAtingida() + resultado.getDouble("valor")));
                    fecharMeta.calcularPorcentagem();
                } else {
                    fecharMeta.setMetaAtingida(fecharMeta.getMetaAtingida() + 1);
                    fecharMeta.calcularPorcentagem();
                }
                fecharMeta.getFecharMetaDetalhadoVOs().add(obj);
            }
        }
    }

    /**
     * Metodo responsavel por persistir a entidade FecharMetaDetalhado quando um
     * cliente realizar um contrato e ja atualizo a entidade fecharMeta porem
     * para que tudo de certo antes e realizado uma consultar se existe a meta.
     *
     * @param identificadorMeta      Parametro para realizar a
     *                               consultarMetaPorDiaPorColaboradorResponsavel
     * @param colaboradorResponsavel Parametro para realizar a
     *                               consultarMetaPorDiaPorColaboradorResponsavel
     * @param cliente                Codigo do cliente que esta sendo validado
     * @param contrato               Entidade contrato para atualizar o valores das metas.
     * @throws Exception Caso haja problemas de conexï¿½o, restriï¿½ï¿½o de acesso ou
     *                   validaï¿½ï¿½o de dados.
     * <AUTHOR>
     */
    public void executarPersistenciaFechaMetaDetalhadoFaturamentoOrVenda(String identificadorMeta, Integer colaboradorResponsavel, Integer cliente, ContratoVO contrato, Integer empresa) throws Exception {
        FecharMetaVO fecharMeta = consultarMetaPorDiaPorColaboradorResponsavel(identificadorMeta, negocio.comuns.utilitarias.Calendario.hoje(), colaboradorResponsavel, false, Uteis.NIVELMONTARDADOS_DADOSBASICOS, empresa);
        if (fecharMeta.getCodigo().intValue() != 0 && fecharMeta.getMetaCalculada()) {
            FecharMetaDetalhadoVO obj = new FecharMetaDetalhadoVO();
            obj.setFecharMeta(fecharMeta);
            obj.setCodigoOrigem(cliente);
            obj.setOrigem("CLIENTE");
            obj.getCliente().setCodigo(cliente);
            obj.setObteveSucesso(true);
            obj.setContratoVO(contrato);
            FecharMetaDetalhado fecharMetaDetalhado = new FecharMetaDetalhado(this.con);
            fecharMetaDetalhado.incluir(obj);
            fecharMetaDetalhado = null;
            if (identificadorMeta.equals("MF")) {
                fecharMeta.setMetaAtingida(Uteis.arredondarForcando2CasasDecimais(fecharMeta.getMetaAtingida() + contrato.getValorFinal()));
                fecharMeta.calcularPorcentagem();
            } else {
                fecharMeta.setMetaAtingida(fecharMeta.getMetaAtingida() + 1);
                fecharMeta.calcularPorcentagem();

            }
            alteraSemSubordinada(fecharMeta);
        }
    }

    public void executarValidacaoAgendadoComprouContrato(ContratoVO contrato, AgendaVO agendaMarcadaPresenca) throws Exception {
        if (agendaMarcadaPresenca.getIndicado().getCodigo().intValue() != 0) {
            executarAtualizacaoMetaAgendadoQuandoRealiaCompraDeUmcontrato(contrato, agendaMarcadaPresenca, agendaMarcadaPresenca.getIndicado().getCodigo(), 0, 0);
        } else if (agendaMarcadaPresenca.getPassivo().getCodigo().intValue() != 0) {
            executarAtualizacaoMetaAgendadoQuandoRealiaCompraDeUmcontrato(contrato, agendaMarcadaPresenca, 0, agendaMarcadaPresenca.getPassivo().getCodigo(), 0);
        } else {
            executarAtualizacaoMetaAgendadoQuandoRealiaCompraDeUmcontrato(contrato, agendaMarcadaPresenca, 0, 0, agendaMarcadaPresenca.getCliente().getCodigo());
        }
    }

    public List consultarFecharMetaVenda(Integer fecharMeta, int nivelMontarDados) throws Exception {
        consultarCRM(getIdEntidade(), false);
        String fasesVenda = "";
        for (FasesCRMEnum fase : FasesCRMEnum.values()) {
            if (fase.getTipoFase().equals(TipoFaseCRM.VENDAS)) {
                fasesVenda = fasesVenda + ",'" + fase.getSigla() + "'";
            }
        }
        List objetos = new ArrayList();
        String sql = "SELECT * FROM FecharMeta WHERE aberturaMeta = ? " +
                "and identificadormeta IN (" + fasesVenda.replaceFirst(",", "") + ")";
        try (PreparedStatement sqlConsulta = con.prepareStatement(sql)) {
            sqlConsulta.setInt(1, fecharMeta);
            try (ResultSet resultado = sqlConsulta.executeQuery()) {
                while (resultado.next()) {
                    FecharMetaVO novoObj = FecharMeta.montarDados(resultado, nivelMontarDados, con);
                    objetos.add(novoObj);
                }
            }
        }

        return objetos;
    }

    public List consultarFecharMetaEstudio(Integer fecharMeta, int nivelMontarDados) throws Exception {
        consultarCRM(getIdEntidade(), false);
        String fasesEstudio = "";
        for (FasesCRMEnum fase : FasesCRMEnum.values()) {
            if (fase.getTipoFase().equals(TipoFaseCRM.ESTUDIO)) {
                fasesEstudio = fasesEstudio + ",'" + fase.getSigla() + "'";
            }
        }
        List objetos = new ArrayList();
        String sql = "SELECT * FROM FecharMeta WHERE aberturaMeta = ? " +
                "and identificadormeta IN (" + fasesEstudio.replaceFirst(",", "") + ")";
        try (PreparedStatement sqlConsulta = con.prepareStatement(sql)) {
            sqlConsulta.setInt(1, fecharMeta);
            try (ResultSet resultado = sqlConsulta.executeQuery()) {
                while (resultado.next()) {
                    FecharMetaVO novoObj = FecharMeta.montarDados(resultado, nivelMontarDados, con);
                    objetos.add(novoObj);
                }
            }
        }

        return objetos;
    }

    public List<FecharMetaVO> consultarFecharMetaRetencao(Integer fecharMeta, int nivelMontarDados) throws Exception {
        consultarCRM(getIdEntidade(), false);
        String fasesRetencao = "";
        for (FasesCRMEnum fase : FasesCRMEnum.values()) {
            if (fase.getTipoFase().equals(TipoFaseCRM.RETENCAO)) {
                fasesRetencao = fasesRetencao + ",'" + fase.getSigla() + "'";
            }
        }
        List<FecharMetaVO> objetos = new ArrayList<FecharMetaVO>();
        String sql = "SELECT * FROM FecharMeta WHERE aberturaMeta = ? " +
                "and identificadormeta IN (" + fasesRetencao.replaceFirst(",", "") + ")";
        try (PreparedStatement sqlConsulta = con.prepareStatement(sql)) {
            sqlConsulta.setInt(1, fecharMeta);
            try (ResultSet resultado = sqlConsulta.executeQuery()) {
                while (resultado.next()) {
                    FecharMetaVO novoObj = FecharMeta.montarDados(resultado, nivelMontarDados, con);
                    objetos.add(novoObj);
                }
            }
        }
        return objetos;
    }

    /**
     * Responsï¿½vel por realizar uma consulta de <code>FecharMeta</code> atravï¿½s
     * do valor do atributo <code>String codOrigem</code>. Retorna os objetos,
     * com inï¿½cio do valor do atributo idï¿½ntico ao parï¿½metro fornecido. Faz uso
     * da operaï¿½ï¿½o <code>montarDadosConsulta</code> que realiza o trabalho de
     * prerarar o List resultante.
     *
     * @param controlarAcesso Indica se a aplicaï¿½ï¿½o deverï¿½ verificar se o usuï¿½rio possui
     *                        permissï¿½o para esta consulta ou nï¿½o.
     * @return List Contendo vï¿½rios objetos da classe <code>FecharMetaVO</code>
     * resultantes da consulta.
     * @throws Exception Caso haja problemas de conexï¿½o ou restriï¿½ï¿½o de acesso.
     */
    public FecharMetaVO consultarMetaPorDiaPorColaboradorResponsavel(String tipoMeta, Date dia, Integer codigo, boolean controlarAcesso, int nivelMontarDados, Integer empresa) throws Exception {
        consultarCRM(getIdEntidade(), controlarAcesso);
        String sqlStr = "SELECT FecharMeta.* FROM FecharMeta "
                + "inner join aberturameta on aberturameta.codigo = FecharMeta.aberturameta and aberturameta.colaboradorresponsavel = " + codigo
                + " AND aberturameta.empresa =  " + empresa + " WHERE upper( identificadorMeta ) like('" + tipoMeta.toUpperCase() + "%') "
                + "and Cast (dataRegistro as Date) = Cast('" + getDataJDBC(dia) + "' as Date) ";
        try (Statement stm = con.createStatement()) {
            try (ResultSet tabelaResultado = stm.executeQuery(sqlStr)) {
                if (!tabelaResultado.next()) {
                    return new FecharMetaVO();
                }
                return (montarDados(tabelaResultado, nivelMontarDados, con));
            }
        }
    }

    // Consultar nao traz codigo da FecharMeta porque nao tem como realizar o
    // SUM.
    public FecharMetaVO consultarMetaPorDiaPorColaboradorResponsavel(String identificador, Date dia, String sql, boolean controlarAcesso, int nivelMontarDados, Integer empresa) throws Exception {
        consultarCRM(getIdEntidade(), controlarAcesso);
        String sqlStr = "";
        if (!sql.equals("")) {
            sqlStr = "SELECT  SUM (meta) as meta, SUM(metaatingida) as metaatingida, array_agg(DISTINCT fecharmeta.codigo) AS codigos , SUM(repescagem) as repescagem FROM FecharMeta "
                    + " inner join aberturameta on aberturameta.codigo = FecharMeta.aberturameta and ( " + sql + "	) AND empresa = " + empresa
                    + " WHERE upper( identificadorMeta ) like('" + identificador.toUpperCase() + "%') "
                    + " and Cast (dataRegistro as Date) = Cast('" + getDataJDBC(dia) + "' as Date)  ";
        } else {
            sqlStr = "SELECT  SUM (meta) as meta, SUM(metaatingida) as metaatingida, array_agg(DISTINCT fecharmeta.codigo) AS codigos, SUM(repescagem) as repescagem FROM FecharMeta "
                    + " inner join aberturameta on aberturameta.codigo = FecharMeta.aberturameta AND empresa = " + empresa
                    + " WHERE upper( identificadorMeta ) like('" + identificador.toUpperCase() + "%') "
                    + " and Cast (dataRegistro as Date) = Cast('" + getDataJDBC(dia) + "' as Date)  ";
        }
        FecharMetaVO fecharMeta;
        try (Statement stm = con.createStatement()) {
            try (ResultSet tabelaResultado = stm.executeQuery(sqlStr)) {
                if (!tabelaResultado.next()) {
                    throw new ConsistirException("Dados Nï¿½o Encontrados ( FecharMeta ).");
                }
                fecharMeta = montarDados(tabelaResultado, nivelMontarDados, con);
            }
        }
        String cods = obterCodigosColaboradores(sql);
        somarMetasDetalhadas(cods, fecharMeta, dia);
        return fecharMeta;
    }

    public String obterCodigosColaboradores(String sql) {
        String cods = sql.replaceAll("aberturameta.colaboradorresponsavel = ", ",");
        cods = cods.replaceAll("or", "");
        cods = cods.replaceFirst(",", "");
        return cods;
    }

    public void somarMetasDetalhadas(String colaboradores, FecharMetaVO fecharMeta, Date data) throws Exception {
        FecharMetaVO recipiente;
        String[] cods = colaboradores.split(",");
        for (String codigo : cods) {
            recipiente = new FecharMetaVO();
            //obter os totais de cada colaborador

            recipiente.setCodigo(obterCodigoFecharMetaColaboradorPorDia(Integer.valueOf(codigo.trim()), data, "AG"));
            recipiente.setContAgendaComparecimento(contarDetalhesFecharMeta(recipiente.getCodigo(), CONTAR_COMPARECIMENTOS, con));
            recipiente.setContReAgendados(contarDetalhesFecharMeta(recipiente.getCodigo(), CONTAR_REAGENDADOS, con));
            recipiente.setContFechamentos(contarDetalhesFecharMeta(recipiente.getCodigo(), CONTAR_COMPARECIMENTOS_FECHADOS, con));
            recipiente.setContLigacao(contarDetalhesFecharMeta(recipiente.getCodigo(), CONTAR_LIGACOES, con));
            //incrementar
            fecharMeta.setContAgendaComparecimento(fecharMeta.getContAgendaComparecimento() + recipiente.getContAgendaComparecimento());
            fecharMeta.setContReAgendados(fecharMeta.getContReAgendados() + recipiente.getContReAgendados());
            fecharMeta.setContFechamentos(fecharMeta.getContFechamentos() + recipiente.getContFechamentos());
            fecharMeta.setContLigacao(fecharMeta.getContLigacao() + recipiente.getContLigacao());
        }
    }

    /**
     * Responsï¿½vel por realizar uma consulta de <code>FecharMeta</code> atravï¿½s
     * do valor do atributo <code>String codOrigem</code>. Retorna os objetos,
     * com inï¿½cio do valor do atributo idï¿½ntico ao parï¿½metro fornecido. Faz uso
     * da operaï¿½ï¿½o <code>montarDadosConsulta</code> que realiza o trabalho de
     * prerarar o List resultante.
     *
     * @param controlarAcesso Indica se a aplicaï¿½ï¿½o deverï¿½ verificar se o usuï¿½rio possui
     *                        permissï¿½o para esta consulta ou nï¿½o.
     * @return List Contendo vï¿½rios objetos da classe <code>FecharMetaVO</code>
     * resultantes da consulta.
     * @throws Exception Caso haja problemas de conexï¿½o ou restriï¿½ï¿½o de acesso.
     */

    public FecharMetaVO consultarPorIdentificadorMetaPorDiaPorColaborador(String valorConsulta, Date dia, Integer colaborador, boolean controlarAcesso, Integer empresa, int nivelMontarDados) throws Exception {
        Logger.getLogger(getClass().getSimpleName()).log(Level.INFO,"#### [consultarPorIdentificadorMetaPorDiaPorColaborador] INICIANDO - valorConsulta={0}, dia={1}, colaborador={2}, empresa={3}",
                new Object[] { valorConsulta, dia, colaborador, empresa }
        );
        consultarCRM(getIdEntidade(), controlarAcesso);
        StringBuilder sqlStr = new StringBuilder();
        sqlStr.append("SELECT FecharMeta.* FROM FecharMeta \n");
        sqlStr.append("INNER JOIN aberturaMeta ON aberturaMeta.codigo = FecharMeta.aberturaMeta \n");
        sqlStr.append("AND aberturaMeta.colaboradorResponsavel =  " + colaborador + " \n");
        if (!UteisValidacao.emptyNumber(empresa)) {
            sqlStr.append(" AND aberturameta.empresa = " + empresa + " \n");
        }
        sqlStr.append(" WHERE UPPER( identificadorMeta ) LIKE ('" + valorConsulta.toUpperCase() + "%') " + "and Cast (dataRegistro as Date) = Cast('" + getDataJDBC(dia) + "' AS DATE )");
        try (Statement stm = con.createStatement()) {
            try (ResultSet tabelaResultado = stm.executeQuery(sqlStr.toString())) {
                if (!tabelaResultado.next()) {
                    Logger.getLogger(getClass().getSimpleName()).log(
                            Level.WARNING,
                            "#### [consultarPorIdentificadorMetaPorDiaPorColaborador] Nenhum resultado encontrado para valorConsulta={0}, dia={1}, colaborador={2}, empresa={3}",
                            new Object[]{ valorConsulta, dia, colaborador, empresa }
                    );
                    throw new ConsistirException("Dados Não Encontrados ( FecharMeta ).");
                }
                Logger.getLogger(getClass().getSimpleName()).log(Level.INFO, "#### [consultarPorIdentificadorMetaPorDiaPorColaborador] FecharMeta encontrado com sucesso.");
                return montarDados(tabelaResultado, nivelMontarDados, con);
            }
        } catch (Exception e) {
            Logger.getLogger(getClass().getSimpleName()).log(Level.SEVERE, "#### ERRO AO CONSULTAR POR IDENTIFICADOR DE META: {0}", e.getMessage());
            throw e;
        }
    }

    public Boolean consultarColaboradorResFase(Integer fase, List<String> tipos) throws Exception {
        if(tipos == null || tipos.isEmpty()){
            return false;
        }
        String tiposStr = "";
        for(String t : tipos){
            tiposStr += ",'" + t + "'";
        }

        tiposStr = tiposStr.substring(tiposStr.indexOf("\\,") + 2);
        ResultSet resultSet = SuperFacadeJDBC.criarConsulta("select exists(select codigo from tiposvinculosfase where fase = " +
                fase +  " and tipocolaborador IN (" + tiposStr + ")) as temfase", con);

        return resultSet.next() ? resultSet.getBoolean("temfase") : false;
    }


    public List<String> consultarTipoColaborador(Integer colaborador) throws Exception{
        StringBuilder sqlStr = new StringBuilder();
        sqlStr.append("SELECT descricao FROM tipocolaborador where colaborador = "+ colaborador);
        List<String> tipos = new ArrayList<>();
        try (Statement stmStr = con.createStatement()) {
            try (ResultSet tabelaResultado = stmStr.executeQuery(sqlStr.toString())) {
                while (tabelaResultado.next()){
                    tipos.add(tabelaResultado.getString("descricao"));
                }
            }
        }

        return tipos;
    }

    public FecharMetaVO consultarPorIdentificadorMetaPorCodigoIndicacaoPorColaborador(String valorConsulta, Integer codigoIndicacao, Integer colaborador, boolean controlarAcesso, int nivelMontarDados) throws Exception {
        consultarCRM(getIdEntidade(), controlarAcesso);
        String sqlStr = "SELECT Distinct on (FecharMeta.codigo) FecharMeta.* FROM FecharMeta "
                + "inner join aberturaMeta on aberturaMeta.codigo = FecharMeta.aberturaMeta and aberturaMeta.colaboradorResponsavel =   " + colaborador + " "
                + "inner join fecharMetaDetalhado on fecharMetaDetalhado.fecharMeta = fecharMeta.codigo "
                + "inner join indicado on indicado.codigo = fecharMetaDetalhado.indicado "
                + "inner join indicacao on indicacao.codigo = indicado.indicacao and indicacao.codigo =  " + codigoIndicacao + " "
                + "WHERE upper( identificadorMeta ) like('" + valorConsulta + "%') ";
        try (Statement stm = con.createStatement()) {
            try (ResultSet tabelaResultado = stm.executeQuery(sqlStr)) {
                if (!tabelaResultado.next()) {
                    throw new ConsistirException("Dados Não Encontrados ( FecharMeta ).");
                }
                return (montarDados(tabelaResultado, nivelMontarDados, con));
            }
        }
    }

    /**
     * Responsï¿½vel por realizar uma consulta de <code>FecharMeta</code> atravï¿½s
     * do valor do atributo <code>String codOrigem</code>. Retorna os objetos,
     * com inï¿½cio do valor do atributo idï¿½ntico ao parï¿½metro fornecido. Faz uso
     * da operaï¿½ï¿½o <code>montarDadosConsulta</code> que realiza o trabalho de
     * prerarar o List resultante.
     *
     * @param controlarAcesso Indica se a aplicaï¿½ï¿½o deverï¿½ verificar se o usuï¿½rio possui
     *                        permissï¿½o para esta consulta ou nï¿½o.
     * @return List Contendo vï¿½rios objetos da classe <code>FecharMetaVO</code>
     * resultantes da consulta.
     * @throws Exception Caso haja problemas de conexï¿½o ou restriï¿½ï¿½o de acesso.
     */
    public FecharMetaVO consultarPorCodigoFechaMeta(Integer codigo, boolean controlarAcesso, int nivelMontarDados) throws Exception {
        consultarCRM(getIdEntidade(), controlarAcesso);
        String sqlStr = "SELECT FecharMeta.* FROM FecharMeta  where codigo = " + codigo;
        try (Statement stm = con.createStatement()) {
            try (ResultSet tabelaResultado = stm.executeQuery(sqlStr)) {
                if (!tabelaResultado.next()) {
                    throw new ConsistirException("Dados Nï¿½o Encontrados ( FecharMeta ).");
                }
                return (montarDados(tabelaResultado, nivelMontarDados, con));
            }
        }
    }

    /**
     * Responsï¿½vel por realizar uma consulta de <code>FecharMeta</code> atravï¿½s
     * do valor do atributo <code>String codOrigem</code>. Retorna os objetos,
     * com inï¿½cio do valor do atributo idï¿½ntico ao parï¿½metro fornecido. Faz uso
     * da operaï¿½ï¿½o <code>montarDadosConsulta</code> que realiza o trabalho de
     * prerarar o List resultante.
     *
     * @param controlarAcesso Indica se a aplicaï¿½ï¿½o deverï¿½ verificar se o usuï¿½rio possui
     *                        permissï¿½o para esta consulta ou nï¿½o.
     * @return List Contendo vï¿½rios objetos da classe <code>FecharMetaVO</code>
     * resultantes da consulta.
     * @throws Exception Caso haja problemas de conexï¿½o ou restriï¿½ï¿½o de acesso.
     */
    public List consultarPorIdentificadorMeta(String valorConsulta, boolean controlarAcesso, int nivelMontarDados) throws Exception {
        consultarCRM(getIdEntidade(), controlarAcesso);
        String sqlStr = "SELECT * FROM FecharMeta WHERE upper( identificadorMeta ) like('" + valorConsulta.toUpperCase() + "%') ORDER BY codOrigem";
        try (Statement stm = con.createStatement()) {
            try (ResultSet tabelaResultado = stm.executeQuery(sqlStr)) {
                return (montarDadosConsulta(tabelaResultado, nivelMontarDados, con));
            }
        }
    }

    /**
     * Responsï¿½vel por realizar uma consulta de <code>FecharMeta</code> atravï¿½s
     * do valor do atributo <code>situacao</code> da classe
     * <code>Colaborador</code> Faz uso da operaï¿½ï¿½o
     * <code>montarDadosConsulta</code> que realiza o trabalho de prerarar o
     * List resultante.
     *
     * @return List Contendo vï¿½rios objetos da classe <code>FecharMetaVO</code>
     * resultantes da consulta.
     * @throws Exception Caso haja problemas de conexï¿½o ou restriï¿½ï¿½o de acesso.
     */
    public List consultarPorSituacaoColaborador(String valorConsulta, int nivelMontarDados) throws Exception {
        consultarCRM(getIdEntidade(), true);
        String sqlStr = "SELECT FecharMeta.* FROM FecharMeta, Colaborador WHERE FecharMeta.colaborador = Colaborador.codigo and upper( Colaborador.situacao ) like('" + valorConsulta.toUpperCase() + "%') ORDER BY Colaborador.situacao";
        try (Statement stm = con.createStatement()) {
            try (ResultSet tabelaResultado = stm.executeQuery(sqlStr)) {
                return montarDadosConsulta(tabelaResultado, nivelMontarDados, con);
            }
        }
    }

    /**
     * Responsï¿½vel por realizar uma consulta de <code>FecharMeta</code> atravï¿½s
     * do valor do atributo <code>Date dataRegistro</code>. Retorna os objetos
     * com valores pertecentes ao perï¿½odo informado por parï¿½metro. Faz uso da
     * operaï¿½ï¿½o <code>montarDadosConsulta</code> que realiza o trabalho de
     * prerarar o List resultante.
     *
     * @param controlarAcesso Indica se a aplicaï¿½ï¿½o deverï¿½ verificar se o usuï¿½rio possui
     *                        permissï¿½o para esta consulta ou nï¿½o.
     * @return List Contendo vï¿½rios objetos da classe <code>FecharMetaVO</code>
     * resultantes da consulta.
     * @throws Exception Caso haja problemas de conexï¿½o ou restriï¿½ï¿½o de acesso.
     */
    public List consultarPorDataRegistro(Date prmIni, Date prmFim, boolean controlarAcesso, int nivelMontarDados) throws Exception {
        consultarCRM(getIdEntidade(), controlarAcesso);
        String sqlStr = "SELECT * FROM FecharMeta WHERE ((dataRegistro >= '" + getDataJDBC(prmIni) + "') and (dataRegistro <= '" + getDataJDBC(prmFim) + "')) ORDER BY dataRegistro";
        try (Statement stm = con.createStatement()) {
            try (ResultSet tabelaResultado = stm.executeQuery(sqlStr)) {
                return (montarDadosConsulta(tabelaResultado, nivelMontarDados, con));
            }
        }
    }

    /**
     * Responsï¿½vel por realizar uma consulta de <code>FecharMeta</code> atravï¿½s
     * do valor do atributo <code>Integer codigo</code>. Retorna os objetos com
     * valores iguais ou superiores ao parï¿½metro fornecido. Faz uso da operaï¿½ï¿½o
     * <code>montarDadosConsulta</code> que realiza o trabalho de prerarar o
     * List resultante.
     *
     * @param controlarAcesso Indica se a aplicaï¿½ï¿½o deverï¿½ verificar se o usuï¿½rio possui
     *                        permissï¿½o para esta consulta ou nï¿½o.
     * @return List Contendo vï¿½rios objetos da classe <code>FecharMetaVO</code>
     * resultantes da consulta.
     * @throws Exception Caso haja problemas de conexï¿½o ou restriï¿½ï¿½o de acesso.
     */
    public List consultarPorCodigo(Integer valorConsulta, boolean controlarAcesso, int nivelMontarDados) throws Exception {
        consultarCRM(getIdEntidade(), controlarAcesso);
        String sqlStr = "SELECT * FROM FecharMeta WHERE codigo >= " + valorConsulta.intValue() + " ORDER BY codigo";
        try (Statement stm = con.createStatement()) {
            try (ResultSet tabelaResultado = stm.executeQuery(sqlStr)) {
                return (montarDadosConsulta(tabelaResultado, nivelMontarDados, con));
            }
        }
    }

    /**
     * Mï¿½todo responsavel por realizar uma consulta em FecharMeta pelo cï¿½digo da AberturaMeta
     *
     * @param valorConsulta
     * @param controlarAcesso
     * @param nivelMontarDados
     * @return lista de FecharMeta
     * @throws Exception
     * <AUTHOR>
     */
    public List consultarPorCodigoAberturaMeta(Integer valorConsulta, boolean controlarAcesso, int nivelMontarDados) throws Exception {
        consultarCRM(getIdEntidade(), controlarAcesso);
        String sqlStr = "SELECT * FROM FecharMeta WHERE aberturaMeta = " + valorConsulta.intValue() + " ORDER BY codigo";
        try (Statement stm = con.createStatement()) {
            try (ResultSet tabelaResultado = stm.executeQuery(sqlStr)) {
                return (montarDadosConsulta(tabelaResultado, nivelMontarDados, con));
            }
        }
    }

    @Override
    public List<FecharMetaVO> consultarPorPeriodoColaboradoresResponsaveis(Date ini, Date fim, List<ColaboradorVO> colaboradores, boolean metaAberta, boolean controlarAcesso, Integer empresa, int nivelMontarDados) throws Exception {
        consultarCRM(getIdEntidade(), controlarAcesso);
        StringBuilder sqlStr = new StringBuilder();
        sqlStr.append("SELECT fecharmeta.codigo, fecharmeta.identificadorMeta, fecharmeta.meta, fecharmeta.repescagem, ");
        sqlStr.append("fecharmeta.aberturameta, fecharmeta.dataRegistro, fecharmeta.justificativa, ");
        sqlStr.append("fecharmeta.metacalculada, fecharmeta.nomeMeta, fecharmeta.maladireta, ");
        sqlStr.append("CASE WHEN fecharmeta.identificadorMeta = 'IN' THEN ");
        sqlStr.append("(SELECT count(f.indicado) FROM fecharmetadetalhado f WHERE f.fecharmeta = fecharmeta.codigo ");
        sqlStr.append("AND f.obtevesucesso AND f.repescagem = false) ");
        sqlStr.append("ELSE fecharmeta.metaAtingida END as metaAtingida ");

        sqlStr.append("FROM fecharmeta ");
        sqlStr.append("INNER JOIN aberturameta ON fecharmeta.aberturameta = aberturameta.codigo ? ");
        sqlStr.append("WHERE aberturameta.dia >= '" + Uteis.getSQLData(ini) + "' AND aberturameta.dia <= '" + Uteis.getSQLData(fim) + "' AND ");
        sqlStr.append("aberturameta.metaemaberto = " + metaAberta);
        if (!UteisValidacao.emptyNumber(empresa)) {
            sqlStr.append(" AND aberturameta.empresa = " + empresa);
        }

        int posicao = sqlStr.indexOf("?");
        // filtra a consulta pelos colaboradores
        // Observar que o campo colaboradorresponsavel armazena o codigo do usuario (concepï¿½ï¿½o original otimize)
        StringBuilder strTemp = new StringBuilder();
        for (ColaboradorVO co : colaboradores)
            if (co.getColaboradorEscolhido()) {
                if (strTemp.toString().isEmpty())
                    strTemp.append("(");
                else
                    strTemp.append(",");
                strTemp.append(co.getCodigo());
            }
        if (strTemp.toString().isEmpty())
            sqlStr.replace(posicao, posicao + 1, "");
        else {
            strTemp.append(")");
            sqlStr.replace(posicao, posicao + 1, " INNER JOIN usuario ON usuario.codigo = aberturameta.colaboradorresponsavel AND usuario.colaborador IN " + strTemp.toString());
        }
        sqlStr.append(" ORDER BY identificadormeta");

        try (Statement stm = con.createStatement()) {
            try (ResultSet tabelaResultado = stm.executeQuery(sqlStr.toString())) {
                return (montarDadosConsulta(tabelaResultado, nivelMontarDados, con));
            }
        }
    }

    public double contarColaboradoresNasMetasPorPeriodoColaboradoresResponsaveis(Date ini, Date fim, List<ColaboradorVO> colaboradores, boolean metaAberta, boolean controlarAcesso, Integer empresa, int nivelMontarDados) throws Exception {
        StringBuilder sqlStr = new StringBuilder();
        sqlStr.append("SELECT count(distinct colaboradorresponsavel) FROM fecharmeta ");
        sqlStr.append("INNER JOIN aberturameta ON fecharmeta.aberturameta = aberturameta.codigo ? ");
        sqlStr.append("WHERE aberturameta.dia >= '").append(Uteis.getSQLData(ini)).append("' AND aberturameta.dia <= '").append(Uteis.getSQLData(fim)).append("' AND ");
        sqlStr.append("aberturameta.metaemaberto = ").append(metaAberta);
        if (!UteisValidacao.emptyNumber(empresa)) {
            sqlStr.append(" AND aberturameta.empresa = ").append(empresa);
        }

        int posicao = sqlStr.indexOf("?");
        // filtra a consulta pelos colaboradores
        // Observar que o campo colaboradorresponsavel armazena o codigo do usuario (concepï¿½ï¿½o original otimize)
        StringBuilder strTemp = new StringBuilder();
        for (ColaboradorVO co : colaboradores)
            if (co.getColaboradorEscolhido()) {
                if (strTemp.toString().isEmpty())
                    strTemp.append("(");
                else
                    strTemp.append(",");
                strTemp.append(co.getCodigo());
            }
        if (strTemp.toString().isEmpty())
            sqlStr.replace(posicao, posicao + 1, "");
        else {
            strTemp.append(")");
            sqlStr.replace(posicao, posicao + 1, " INNER JOIN usuario ON usuario.codigo = aberturameta.colaboradorresponsavel AND usuario.colaborador IN " + strTemp.toString());
        }

        return (double) contar(sqlStr.toString(), con);
    }


    /**
     * Responsï¿½vel por montar os dados de vï¿½rios objetos, resultantes de uma
     * consulta ao banco de dados (<code>ResultSet</code>). Faz uso da operaï¿½ï¿½o
     * <code>montarDados</code> que realiza o trabalho para um objeto por vez.
     *
     * @return List Contendo vï¿½rios objetos da classe <code>FecharMetaVO</code>
     * resultantes da consulta.
     */
    public static List montarDadosConsultaBI(ResultSet tabelaResultado, int nivelMontarDados, Connection con) throws Exception {
        List vetResultado = new ArrayList();
        while (tabelaResultado.next()) {
            FecharMetaVO obj = new FecharMetaVO();
            obj = montarDadosBI(tabelaResultado, nivelMontarDados, con);
            vetResultado.add(obj);
        }
        tabelaResultado = null;
        return vetResultado;
    }

    /**
     * Responsï¿½vel por montar os dados de vï¿½rios objetos, resultantes de uma
     * consulta ao banco de dados (<code>ResultSet</code>). Faz uso da operaï¿½ï¿½o
     * <code>montarDados</code> que realiza o trabalho para um objeto por vez.
     *
     * @return List Contendo vï¿½rios objetos da classe <code>FecharMetaVO</code>
     * resultantes da consulta.
     */
    public static List montarDadosConsulta(ResultSet tabelaResultado, int nivelMontarDados, Connection con) throws Exception {
        List vetResultado = new ArrayList();
        while (tabelaResultado.next()) {
            FecharMetaVO obj = new FecharMetaVO();
            obj = montarDados(tabelaResultado, nivelMontarDados, con);
            vetResultado.add(obj);
        }
        tabelaResultado = null;
        return vetResultado;
    }


    /**
     * Responsï¿½vel por montar os dados resultantes de uma consulta ao banco de
     * dados (<code>ResultSet</code>) em um objeto da classe
     * <code>FecharMetaVO</code>.
     *
     * @return O objeto da classe <code>FecharMetaVO</code> com os dados
     * devidamente montados.
     */
    public static FecharMetaVO montarDadosBI(ResultSet dadosSQL, int nivelMontarDados, Connection con) throws Exception {
        FecharMetaVO obj = new FecharMetaVO();

        if (nivelMontarDados == Uteis.NIVELMONTARDADOS_MINIMOS) {
            obj.setMeta(new Double(dadosSQL.getInt("meta")));
            obj.setMetaAtingida(new Double(dadosSQL.getDouble("metaAtingida")));
            obj.setRepescagem(new Double(dadosSQL.getDouble("repescagem")));
            try {
                obj.setCodigosConcat(dadosSQL.getString("codigos").replace("{", "").replace("}", ""));
            } catch (Exception ignored) {

            }
            obj.calcularPorcentagem();
            return obj;
        }
        obj.setCodigo(new Integer(dadosSQL.getInt("codigo")));
        obj.setDataRegistro(dadosSQL.getTimestamp("dataRegistro"));
        obj.setMeta((double) dadosSQL.getInt("meta"));
        obj.setRepescagem((double) dadosSQL.getInt("repescagem"));
        obj.setMetaAtingida(dadosSQL.getDouble("metaAtingida"));
        obj.getAberturaMetaVO().setCodigo(dadosSQL.getInt("aberturameta"));
        obj.calcularPorcentagem();
        obj.setJustificativa(dadosSQL.getString("justificativa"));
        obj.setIdentificadorMeta(dadosSQL.getString("identificadorMeta"));
        obj.setMetaCalculada(dadosSQL.getBoolean("metacalculada"));
        obj.setNovoObj(false);
        obj.setFase(FasesCRMEnum.getFasePorSigla(obj.getIdentificadorMeta()));
        obj.setNomeMeta(dadosSQL.getString("nomeMeta"));
        obj.getMalaDiretaCRMExtra().setCodigo(dadosSQL.getInt("maladireta"));
        if (nivelMontarDados == Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA) {
            return obj;
        }

        if (nivelMontarDados == Uteis.NIVELMONTARDADOS_DADOSBASICOS) {
            return obj;
        }

        if (nivelMontarDados == Uteis.NIVELMONTARDADOS_DADOSENTIDADESPRINCIPAIS) {
            montarDadosAberturaMeta(obj, Uteis.NIVELMONTARDADOS_TELACONSULTA, con);
            return obj;
        }
        if (nivelMontarDados == Uteis.NIVELMONTARDADOS_METACOLABORADORRESPONSAVEL) {
            montarDadosAberturaMeta(obj, nivelMontarDados, con);
            return obj;
        }
        obj.setFecharMetaDetalhadoVOs(getFacade().getFecharMetaDetalhado().consultarFecharMetaDetalhados(obj.getCodigo(), nivelMontarDados));
        return obj;
    }

    /**
     * Responsï¿½vel por montar os dados resultantes de uma consulta ao banco de
     * dados (<code>ResultSet</code>) em um objeto da classe
     * <code>FecharMetaVO</code>.
     *
     * @return O objeto da classe <code>FecharMetaVO</code> com os dados
     * devidamente montados.
     */
    public static FecharMetaVO montarDados(ResultSet dadosSQL, int nivelMontarDados, Connection con) throws Exception {
        FecharMetaVO obj = new FecharMetaVO();

        if (nivelMontarDados == Uteis.NIVELMONTARDADOS_MINIMOS) {
            // obj.setCodigo(new Integer(dadosSQL.getInt("codigo")));
            obj.setMeta(new Double(dadosSQL.getInt("meta")));
            obj.setMetaAtingida(new Double(dadosSQL.getDouble("metaAtingida")));
            obj.setRepescagem(new Double(dadosSQL.getDouble("repescagem")));
            try {
                obj.setCodigosConcat(dadosSQL.getString("codigos").replace("{", "").replace("}", ""));
            } catch (Exception ignored) {

            }
            obj.calcularPorcentagem();
            return obj;
        }
        obj.setCodigo(new Integer(dadosSQL.getInt("codigo")));
        obj.setDataRegistro(dadosSQL.getTimestamp("dataRegistro"));
        obj.setMeta((double) dadosSQL.getInt("meta"));
        obj.setRepescagem((double) dadosSQL.getInt("repescagem"));
        obj.setMetaAtingida(dadosSQL.getDouble("metaAtingida"));
        obj.getAberturaMetaVO().setCodigo(dadosSQL.getInt("aberturameta"));
        obj.calcularPorcentagem();
        obj.setJustificativa(dadosSQL.getString("justificativa"));
        obj.setIdentificadorMeta(dadosSQL.getString("identificadorMeta"));
        obj.setMetaCalculada(dadosSQL.getBoolean("metacalculada"));
        obj.setNovoObj(false);
        obj.setFase(FasesCRMEnum.getFasePorSigla(obj.getIdentificadorMeta()));
        obj.setNomeMeta(dadosSQL.getString("nomeMeta"));
        obj.getMalaDiretaCRMExtra().setCodigo(dadosSQL.getInt("maladireta"));
        if (nivelMontarDados == Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA) {
            return obj;
        }

        obj.setContAgendaComparecimento(contarDetalhesFecharMeta(obj.getCodigo(), CONTAR_COMPARECIMENTOS, con));
        obj.setContReAgendados(contarDetalhesFecharMeta(obj.getCodigo(), CONTAR_REAGENDADOS, con));
        obj.setContFechamentos(contarDetalhesFecharMeta(obj.getCodigo(), CONTAR_COMPARECIMENTOS_FECHADOS, con));
        obj.setContLigacao(contarDetalhesFecharMeta(obj.getCodigo(), CONTAR_LIGACOES, con));

        if (nivelMontarDados == Uteis.NIVELMONTARDADOS_DADOSBASICOS) {
            return obj;
        }

        if (nivelMontarDados == Uteis.NIVELMONTARDADOS_DADOSENTIDADESPRINCIPAIS) {
            montarDadosAberturaMeta(obj, Uteis.NIVELMONTARDADOS_TELACONSULTA, con);
            return obj;
        }
        if (nivelMontarDados == Uteis.NIVELMONTARDADOS_METACOLABORADORRESPONSAVEL) {
            montarDadosAberturaMeta(obj, nivelMontarDados, con);
            //obj.setTotaisOrigens(getFacade().getFecharMetaDetalhado().contarOrigem(obj.getCodigo()));
            return obj;
        }
        obj.setFecharMetaDetalhadoVOs(getFacade().getFecharMetaDetalhado().consultarFecharMetaDetalhados(obj.getCodigo(), nivelMontarDados));
        return obj;
    }

    /**
     * Operaï¿½ï¿½o responsï¿½vel por montar os dados de um objeto da classe
     * <code>ColaboradorVO</code> relacionado ao objeto
     * <code>FecharMetaVO</code>. Faz uso da chave primï¿½ria da classe
     * <code>ColaboradorVO</code> para realizar a consulta.
     *
     * @param obj Objeto no qual serï¿½ montado os dados consultados.
     */
    public static void montarDadosAberturaMeta(FecharMetaVO obj, int nivelMontarDados, Connection con) throws Exception {
        if (obj.getAberturaMetaVO().getCodigo().intValue() == 0) {
            obj.setAberturaMetaVO(new AberturaMetaVO());
            return;
        }
        AberturaMeta aberturaMetaDAO = new AberturaMeta(con);

        obj.setAberturaMetaVO(aberturaMetaDAO.consultarPorChavePrimaria(obj.getAberturaMetaVO().getCodigo(), nivelMontarDados));
    }

    /**
     * Operaï¿½ï¿½o responsï¿½vel por adicionar um objeto da
     * <code>FecharMetaDetalhadoVO</code> no Hashtable
     * <code>FecharMetaDetalhados</code>. Neste Hashtable sï¿½o mantidos todos os
     * objetos de FecharMetaDetalhado de uma determinada FecharMeta.
     *
     * @param obj Objeto a ser adicionado no Hashtable.
     */
    public void adicionarObjFecharMetaDetalhados(FecharMetaDetalhadoVO obj) throws Exception {
        getFecharMetaDetalhados().put(obj.getCliente().getCodigo() + "", obj);
        // adicionarObjSubordinadoOC
    }

    /**
     * Operaï¿½ï¿½o responsï¿½vel por remover um objeto da classe
     * <code>FecharMetaDetalhadoVO</code> do Hashtable
     * <code>FecharMetaDetalhados</code>. Neste Hashtable sï¿½o mantidos todos os
     * objetos de FecharMetaDetalhado de uma determinada FecharMeta.
     *
     * @param Cliente Atributo da classe <code>FecharMetaDetalhadoVO</code>
     *                utilizado como apelido (key) no Hashtable.
     */
    public void excluirObjFecharMetaDetalhados(Integer Cliente) throws Exception {
        getFecharMetaDetalhados().remove(Cliente + "");
        // excluirObjSubordinadoOC
    }

    /**
     * Operaï¿½ï¿½o responsï¿½vel por localizar um objeto da classe
     * <code>FecharMetaVO</code> atravï¿½s de sua chave primï¿½ria.
     *
     * @throws Exception Caso haja problemas de conexï¿½o ou localizaï¿½ï¿½o do objeto
     *                   procurado.
     */
    public FecharMetaVO consultarPorChavePrimaria(Integer codigoPrm, int nivelMontarDados) throws Exception {
        consultarCRM(getIdEntidade(), false);
        String sql = "SELECT * FROM FecharMeta WHERE codigo = ?";
        try (PreparedStatement sqlConsultar = con.prepareStatement(sql)) {
            sqlConsultar.setInt(1, codigoPrm.intValue());
            try (ResultSet tabelaResultado = sqlConsultar.executeQuery()) {
                if (!tabelaResultado.next()) {
                    throw new ConsistirException("Dados Nï¿½o Encontrados ( FecharMeta ).");
                }
                return (montarDados(tabelaResultado, nivelMontarDados, con));
            }
        }
    }

    public FecharMetaVO consultarPorCodigoFecharMetaDetalhado(Integer valorConsulta, boolean controlarAcesso, int nivelMontarDados) throws Exception {
        consultarCRM(getIdEntidade(), controlarAcesso);
        String sqlStr = "SELECT * FROM FecharMeta inner join FecharMetaDetalhado on FecharMeta.codigo = FecharMetaDetalhado.fecharMeta and FecharMetaDetalhado.codigo = " + valorConsulta.intValue() + " ";
        try (Statement stm = con.createStatement()) {
            try (ResultSet tabelaResultado = stm.executeQuery(sqlStr)) {
                if (!tabelaResultado.next()) {
                    return new FecharMetaVO();
                }
                return (montarDados(tabelaResultado, nivelMontarDados, con));
            }
        }
    }

    public void incluirFecharMetaVos(Integer aberturaMetaPrm, List objetos) throws Exception {
        Iterator e = objetos.iterator();
        while (e.hasNext()) {
            FecharMetaVO obj = (FecharMetaVO) e.next();
            obj.getAberturaMetaVO().setCodigo(aberturaMetaPrm);
            incluir(obj);
            if (obj.getIdentificadorMeta().equals(FasesCRMEnum.AGENDAMENTO.getSigla())) {
                obj.setContLigacao(contarDetalhesFecharMeta(obj.getCodigo(), FecharMeta.CONTAR_LIGACOES, con));
            }
        }
    }

    @Override
    public void incluirFecharMetaVos(Integer aberturaMetaPrm, List objetos, FasesCRMEnum fase) throws Exception {
        Iterator e = objetos.iterator();
        while (e.hasNext()) {
            FecharMetaVO obj = (FecharMetaVO) e.next();
            obj.getAberturaMetaVO().setCodigo(aberturaMetaPrm);
            incluir(obj, fase);
            if (obj.getIdentificadorMeta().equals(FasesCRMEnum.AGENDAMENTO.getSigla())) {
                obj.setContLigacao(contarDetalhesFecharMeta(obj.getCodigo(), FecharMeta.CONTAR_LIGACOES, con));
            }
        }
    }

    public void alterarFecharMetaVos(Integer aberturaMetaPrm, List objetos) throws Exception {
        Iterator e = objetos.iterator();
        while (e.hasNext()) {
            FecharMetaVO obj = (FecharMetaVO) e.next();
            obj.getAberturaMetaVO().setCodigo(aberturaMetaPrm);
            alterarSomenteCampoJustificativaFechamentoDia(obj);
        }
    }

    public void alterarFecharMetaVosVindoAberturaMeta(Integer aberturaMetaPrm, List objetos) throws Exception {
        Iterator e = objetos.iterator();
        while (e.hasNext()) {
            FecharMetaVO obj = (FecharMetaVO) e.next();
            obj.getAberturaMetaVO().setCodigo(aberturaMetaPrm);
            alterar(obj);
        }
    }

    public Hashtable getFecharMetaDetalhados() {
        if (fecharMetaDetalhados == null) {
            fecharMetaDetalhados = new Hashtable();
        }
        return (fecharMetaDetalhados);
    }

    public void setFecharMetaDetalhados(Hashtable fecharMetaDetalhados) {
        this.fecharMetaDetalhados = fecharMetaDetalhados;
    }

    // regra de negocio todos daqui pra baixo
    public void inicializarDadosParaTelaMetaDetalhando(FecharMetaVO fecharMetaVO) {
        fecharMetaVO.setListaHistoricoContatoHistorico(new ArrayList());
        fecharMetaVO.setListaHistoricoContatoMes(new ArrayList());
        fecharMetaVO.setMarcaTodosHoje(false);
        fecharMetaVO.setMarcaTodosMes(false);
        fecharMetaVO.setMarcaTodosHistorico(false);

    }

    /**
     * Mï¿½todo responsavel por receber uma lista e retorna um valor Long do
     * tamanhao da lista recebida.
     */
    public Long executarTotalizadorSelecionado(List<FecharMetaDetalhadoVO> lista, Long totalizadorSelecionado) {
        totalizadorSelecionado = 0L;
        for (FecharMetaDetalhadoVO fe : lista) {
            if (fe.getEnviarEmailSMS()) {
                totalizadorSelecionado = totalizadorSelecionado + new Long(1);
            }
        }
        return totalizadorSelecionado;
    }

    /**
     * Mï¿½todo responsavel por marcar todos BooleanSelectBox e atualizar o
     * totalizadorSelecionado da aba Hoje.
     */
    public Long executarTodosFecharMetaDetalhados(FecharMetaVO fecharMeta, Long totalizadorSelecionadoHoje) {
        for (FecharMetaDetalhadoVO fe : fecharMeta.getFecharMetaDetalhadoVOs()) {
            fe.setEnviarEmailSMS(fecharMeta.getMarcaTodosHoje());
        }
        if (fecharMeta.getMarcaTodosHoje()) {
            totalizadorSelecionadoHoje = new Long(fecharMeta.getFecharMetaDetalhadoVOs().size());
        } else {
            totalizadorSelecionadoHoje = new Long(0);
        }
        return totalizadorSelecionadoHoje;
    }

    /**
     * Mï¿½todo responsavel por marcar todos BooleanSelectBox e atualizar o
     * totalizadorSelecionado da aba Mï¿½s.
     */
    public Long executarTodosMes(FecharMetaVO fecharMeta, Long totalizadorSelecionadoMes) {
        for (FecharMetaDetalhadoVO fe : fecharMeta.getListaHistoricoContatoMes()) {
            fe.setEnviarEmailSMS(fecharMeta.getMarcaTodosMes());
        }
        if (fecharMeta.getMarcaTodosMes()) {
            totalizadorSelecionadoMes = new Long(fecharMeta.getListaHistoricoContatoMes().size());
        } else {
            totalizadorSelecionadoMes = new Long(0);
        }
        return totalizadorSelecionadoMes;
    }

    /**
     * Mï¿½todo responsavel por marcar todos BooleanSelectBox e atualizar o
     * totalizadorSelecionado da aba Historico.
     */
    public Long executarTodosHistorico(FecharMetaVO fecharMeta, Long totalizadorSelecionadoHistorico) {
        for (FecharMetaDetalhadoVO fe : fecharMeta.getListaHistoricoContatoHistorico()) {
            fe.setEnviarEmailSMS(fecharMeta.getMarcaTodosHistorico());
        }
        if (fecharMeta.getMarcaTodosHistorico()) {
            totalizadorSelecionadoHistorico = new Long(fecharMeta.getListaHistoricoContatoHistorico().size());
        } else {
            totalizadorSelecionadoHistorico = new Long(0);
        }
        return totalizadorSelecionadoHistorico;
    }

    /**
     * Mï¿½todo responsavel por percorrer a lista de FecharMetaDetalhado e para cada
     * fecharMetaDetalhado excluir a fecharMeta pelo codigo.
     *
     * @param listaFecharMetaDetalhado
     * @throws Exception
     * <AUTHOR>
     */
    public void executarExclusaoFecharMetaPassivo(List listaFecharMetaDetalhado) throws Exception {
        Iterator e = listaFecharMetaDetalhado.iterator();
        while (e.hasNext()) {
            FecharMetaDetalhadoVO obj = (FecharMetaDetalhadoVO) e.next();
            if (obj.getFecharMeta().getIdentificadorMeta().equals("AG")) {
                obj.getFecharMeta().setMeta(obj.getFecharMeta().getMeta() - 1);
            }
            if (obj.getFecharMeta().getIdentificadorMeta().equals("CP")) {
                obj.getFecharMeta().setMetaAtingida(obj.getFecharMeta().getMetaAtingida() - 1);
            }
            alteraSemSubordinada(obj.getFecharMeta());
        }

    }

    /**
     * Mï¿½todo responsavel por alterar a FecharMetaVO no momento que exclui uma Indicaï¿½ï¿½o
     *
     * @param obj
     * @throws Exception
     * <AUTHOR>
     */
    public void executarAlteracaoFecharMetaIndicado(FecharMetaVO obj, Integer quantidade) throws Exception {
        if (obj.getIdentificadorMeta().equals("IN")) {
            obj.setMetaAtingida(obj.getMetaAtingida() - quantidade);
        }
        alteraSemSubordinada(obj);
    }

    public static Integer contarDetalhesFecharMeta(Integer codigoFecharMeta, String sql, Connection con) throws Exception {
        Integer retorno = null;
        Declaracao dc = new Declaracao(sql, con);
        dc.setInt(1, codigoFecharMeta);
        try (ResultSet rs = dc.executeQuery()) {
            if (rs.next()) {
                retorno = rs.getInt("total");
            }
        }
        return retorno;
    }

    public List<FecharMetaVO> consultarFecharMeta(int aberturaMeta, int nivelMontarDados, String... fases) throws Exception {
        String fasesSiglas = "";
        for (int i = 0; i < fases.length; i++) {
            fasesSiglas += ",'" + fases[i] + "'";
        }
        fasesSiglas = fasesSiglas.replaceFirst(",", "");

        List<FecharMetaVO> objetos = new ArrayList<FecharMetaVO>();
        String sql = "SELECT * FROM FecharMeta WHERE aberturaMeta = ? ";
        if (!UteisValidacao.emptyString(fasesSiglas)) {
            sql += " and identificadormeta in (" + fasesSiglas + ")";
        }
        try (PreparedStatement sqlConsulta = con.prepareStatement(sql)) {
            sqlConsulta.setInt(1, aberturaMeta);
            try (ResultSet resultado = sqlConsulta.executeQuery()) {
                while (resultado.next()) {
                    objetos.add(montarDados(resultado, nivelMontarDados, con));
                }
            }
        }
        return objetos;
    }

    public boolean metaAbertaFechamentaDetalhado(Integer fechametaDetalhado) throws Exception {
        String sqlStr = "select am.metaemaberto from aberturameta am inner join fecharmeta fm on fm.aberturameta = am.codigo inner join fecharmetadetalhado fmd on fm.codigo = fmd.fecharmeta where fmd.codigo = " + fechametaDetalhado.intValue() + " ";
        try (Statement stm = con.createStatement()) {
            try (ResultSet tabelaResultado = stm.executeQuery(sqlStr)) {
                if (!tabelaResultado.next()) {
                    return false;
                }
                return tabelaResultado.getBoolean("metaemaberto");
            }
        }
    }

    public FecharMetaDetalhadoVO inserirFecharMetaDetalhadoIndicado(FecharMetaVO fecharMeta, Integer indicado) throws Exception {
        Logger.getLogger(getClass().getSimpleName()).log(
                Level.INFO,
                "#### [inserirFecharMetaDetalhadoIndicado] INICIANDO - fecharMeta={0}, indicado={1}",
                new Object[]{ (fecharMeta != null ? fecharMeta.getCodigo() : null), indicado }
        );

        FecharMetaDetalhadoVO obj = new FecharMetaDetalhadoVO();
        try {
            if (fecharMeta.getMetaCalculada()) {
                obj.setFecharMeta(fecharMeta);
                obj.setCodigoOrigem(indicado);
                obj.setOrigem("INDICADO");
                obj.getIndicado().setCodigo(indicado);
                obj.setObteveSucesso(false);

                FecharMetaDetalhado fecharMetaDetalhado = new FecharMetaDetalhado(con);
                fecharMetaDetalhado.incluir(obj);
                fecharMetaDetalhado = null;

                Logger.getLogger(getClass().getSimpleName()).log(Level.INFO,
                        "#### [inserirFecharMetaDetalhadoIndicado] FecharMetaDetalhado inserido - Codigo={0}",
                        obj.getCodigo()
                );
            } else {
                Logger.getLogger(getClass().getSimpleName()).log(
                        Level.INFO,
                        "#### [inserirFecharMetaDetalhadoIndicado] Meta não calculada. Nenhum registro incluído."
                );
            }
        } catch (Exception e) {
            Logger.getLogger(getClass().getSimpleName()).log(Level.SEVERE, "#### ERRO AO INSERIR FecharMetaDetalhado INDICADO: {0}", e.getMessage());
            throw e;
        }
        Logger.getLogger(getClass().getSimpleName()).log(Level.INFO, "#### [inserirFecharMetaDetalhadoIndicado] FINALIZADO OK");
        return obj;
    }

    public Integer totalContatoSemBaterMeta(List<FecharMetaVO> fecharMetaVOList) throws Exception {
        String codigosFecharMeta = Uteis.retornarCodigos(fecharMetaVOList);
        String sqlStr = "select count(*) as totalContatoSemBaterMeta from fecharmetadetalhado where obtevesucesso = false and tevecontato = true and fecharmeta in (" + codigosFecharMeta + ");";
        try (Statement stm = con.createStatement()) {
            try (ResultSet tabelaResultado = stm.executeQuery(sqlStr)) {
                if (tabelaResultado.next()) {
                    return tabelaResultado.getInt("totalContatoSemBaterMeta");
                }
            }
        }
        return 0;
    }
    public void excluirMetaExtraGeradaComFalhaUDia(Integer malaDireta, Date dia) throws Exception {

        String sql = "delete from fecharmeta  where codigo in(\n" +
                "select f.codigo  from fecharmeta f left join fecharmetadetalhado fd \n" +
                "on f.codigo  = fd.fecharmeta \n" +
                "left join aberturameta a on a.codigo  = f.aberturameta \n" +
                "where f.dataregistro =?   and f.maladireta = ? and fd.codigo  is null)";
        PreparedStatement stm = con.prepareStatement(sql);
        stm.setDate(1, getDataJDBC(dia));
        stm.setInt(2,malaDireta);
        stm.execute();
    }
    public boolean existeMetaCRMExtraGeradaUsuarioDia(Integer malaDireta, Integer usuario, Date dia, Integer empresa) throws Exception {

        StringBuffer sqlStr = new StringBuffer();
        sqlStr.append("SELECT EXISTS( \n");
        sqlStr.append("select \n");
        sqlStr.append("f.codigo \n");
        sqlStr.append("from fecharmeta f \n");
        sqlStr.append("inner join aberturameta a on a.codigo = f.aberturameta \n");
        sqlStr.append("where \n");
        sqlStr.append("a.colaboradorresponsavel = ").append(usuario).append("\n");
        sqlStr.append("and f.maladireta = ").append(malaDireta).append("\n");
        sqlStr.append("and f.dataregistro = '").append(Uteis.getData(dia)).append("' \n");
        if (empresa != null) {
            sqlStr.append("and a.empresa = ").append(empresa).append("\n");
        }
        sqlStr.append(")");

        try (Statement stm = con.createStatement()) {
            try (ResultSet tabelaResultado = stm.executeQuery(sqlStr.toString())) {
                tabelaResultado.next();
                return tabelaResultado.getBoolean(1);
            }
        }
    }


    public Double consultarTotalIndicadoBI(Date ini, Date fim, List<UsuarioVO> usuarioVOList, boolean metaAberta, boolean controlarAcesso, Integer empresa, int nivelMontarDados) throws Exception {
        consultarCRM(getIdEntidade(), controlarAcesso);
        StringBuilder sqlStr = new StringBuilder();
        sqlStr.append(" SELECT count(indicado) metaAtingida from fecharmetadetalhado f where f.fecharmeta in \n" +
                " (");
        sqlStr.append("SELECT  fecharmeta.codigo  FROM fecharmeta ");
        sqlStr.append("INNER JOIN aberturameta ON fecharmeta.aberturameta = aberturameta.codigo ? ");
        sqlStr.append("WHERE aberturameta.dia >= '" + Uteis.getSQLData(ini) + "' AND aberturameta.dia <= '" + Uteis.getSQLData(fim) + "' ");


        if (!UteisValidacao.emptyNumber(empresa)) {
            sqlStr.append(" AND aberturameta.empresa = " + empresa);
        }
        sqlStr.append(" AND fecharmeta.identificadorMeta  in('IN')  and metaatingida > 0  )");
        sqlStr.append(" AND f.obtevesucesso and f.repescagem = false ");

        int posicao = sqlStr.indexOf("?");
        StringBuilder strTemp = new StringBuilder();
        for (UsuarioVO co : usuarioVOList) {
            if (strTemp.toString().isEmpty()) {
                strTemp.append("(");
            } else {
                strTemp.append(",");
            }
            strTemp.append(co.getCodigo());
        }
        if (strTemp.toString().isEmpty())
            sqlStr.replace(posicao, posicao + 1, "");
        else {
            strTemp.append(")");
            sqlStr.replace(posicao, posicao + 1, " INNER JOIN usuario ON usuario.codigo = aberturameta.colaboradorresponsavel AND usuario.codigo IN " + strTemp.toString());
        }

        Integer totalMetaAtingidaIndicado = contar(sqlStr.toString(), con);

        return Double.parseDouble(String.valueOf(totalMetaAtingidaIndicado));
    }


    public List<FecharMetaVO> consultarPorPeriodoColaboradoresResponsaveisBI(Date ini, Date fim, List<UsuarioVO> usuarioVOList, boolean metaAberta, boolean controlarAcesso, Integer empresa, int nivelMontarDados) throws Exception {
        consultarCRM(getIdEntidade(), controlarAcesso);
        StringBuilder sqlStr = new StringBuilder();
        sqlStr.append("SELECT fecharmeta.* FROM fecharmeta ");
        sqlStr.append("INNER JOIN aberturameta ON fecharmeta.aberturameta = aberturameta.codigo ? ");
        sqlStr.append("WHERE aberturameta.dia >= '" + Uteis.getSQLData(ini) + "' AND aberturameta.dia <= '" + Uteis.getSQLData(fim) + "' ");
        if (!UteisValidacao.emptyNumber(empresa)) {
            sqlStr.append(" AND aberturameta.empresa = " + empresa);
        }

        int posicao = sqlStr.indexOf("?");
        StringBuilder strTemp = new StringBuilder();
        for (UsuarioVO co : usuarioVOList) {
            if (strTemp.toString().isEmpty()) {
                strTemp.append("(");
            } else {
                strTemp.append(",");
            }
            strTemp.append(co.getCodigo());
        }
        if (strTemp.toString().isEmpty())
            sqlStr.replace(posicao, posicao + 1, "");
        else {
            strTemp.append(")");
            sqlStr.replace(posicao, posicao + 1, " INNER JOIN usuario ON usuario.codigo = aberturameta.colaboradorresponsavel AND usuario.codigo IN " + strTemp.toString());
        }
        sqlStr.append(" AND fecharmeta.identificadorMeta not in('").append(FasesCRMEnum.CRM_EXTRA.getSigla()).append("') \n");
        sqlStr.append(" ORDER BY identificadormeta");

        List<FecharMetaVO> listaMeta;
        try (Statement stm = con.createStatement()) {
            try (ResultSet tabelaResultado = stm.executeQuery(sqlStr.toString())) {
                listaMeta = montarDadosConsultaBI(tabelaResultado, nivelMontarDados, con);
            }
        }

        // CONSULTAR META CRM EXTRA
        String codigosUsuario = Uteis.retornarCodigos(usuarioVOList);

        StringBuilder extra = new StringBuilder();
        extra.append("select m.*,md.metaExtraIndividual \n");
        extra.append("from fecharMeta m \n");
        extra.append("inner join maladireta md ON md.codigo = m.maladireta\n");


        extra.append("where 1 = 1 \n");

        extra.append(" and m.codigo in (").append(" select max(m.codigo) \n");
        extra.append(" from fecharMeta m \n");
        extra.append(" inner join aberturameta am on am.codigo = m.aberturameta and am.colaboradorResponsavel in \n");
        extra.append("(").append(codigosUsuario).append(") \n");
        extra.append(" inner join maladireta md ON md.codigo = m.maladireta ");
        extra.append(" where 1=1 ");

        if (fim != null) {
            extra.append("and am.dia >= '").append(getDataJDBC(ini)).append("' \n");
            extra.append("and am.dia <= '").append(getDataJDBC(fim)).append("' \n");
        } else {
            extra.append("and am.dia = '").append(getDataJDBC(ini)).append("' \n");
        }
        extra.append("and m.identificadorMeta in ('").append(FasesCRMEnum.CRM_EXTRA.getSigla()).append("') \n");
        extra.append(" and m.meta > 0 \n");
        extra.append(" group by nomemeta, colaboradorresponsavel, metaExtraIndividual \n");
        extra.append(" order by colaboradorresponsavel);");


        List<FecharMetaVO> listaMetaCRMExtra;
        try (PreparedStatement ce = con.prepareStatement(extra.toString())) {
            try (ResultSet cers = ce.executeQuery()) {
                listaMetaCRMExtra = new ArrayList<FecharMetaVO>();
                while (cers.next()) {
                    FecharMetaVO obj = montarDados(cers, nivelMontarDados, con);
                    obj.getMalaDiretaCRMExtra().setMetaExtraIndividual(cers.getBoolean("metaExtraIndividual"));
                    listaMetaCRMExtra.add(obj);
                }
            }
        }

        if (!UteisValidacao.emptyList(listaMetaCRMExtra)) {
            listaMeta.addAll(listaMetaCRMExtra);
        }

        List<FecharMetaVO> listaMetaOrdenada = new ArrayList<FecharMetaVO>();


        StringBuilder conv = new StringBuilder();
        conv.append(",").append(FasesCRMEnum.CONVERSAO_INDICADOS.getSigla())
                .append(",").append(FasesCRMEnum.CONVERSAO_AGENDADOS.getSigla())
                .append(",").append(FasesCRMEnum.CONVERSAO_EX_ALUNOS.getSigla())
                .append(",").append(FasesCRMEnum.CONVERSAO_VISITANTES_ANTIGOS.getSigla())
                .append(",").append(FasesCRMEnum.CONVERSAO_DESISTENTES.getSigla())
                .append(",").append(FasesCRMEnum.AGENDAMENTOS_LIGACOES.getSigla())
                .append(",").append(FasesCRMEnum.INDICACOES_SEM_CONTATO.getSigla())
                .append(",").append(FasesCRMEnum.PASSIVO.getSigla());

        ConfiguracaoSistemaCRMVO configuracaoSistemaCRMVO = getFacade().getConfiguracaoSistemaCRM().consultarPorChavePrimaria(1, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
        String ordenacaoMetasConfig = configuracaoSistemaCRMVO.getOrdenacaoMetas() + conv.toString();
        String[] ordenacaoMetas = ordenacaoMetasConfig.split(",");

        for (String meta : ordenacaoMetas) {
            for (FecharMetaVO fecharMetaVO : listaMeta) {
                if (fecharMetaVO.getFase().getSigla().equals(meta)) {
                    listaMetaOrdenada.add(fecharMetaVO);
                }
            }
        }

        listaMeta = listaMetaOrdenada;
        return listaMeta;
    }

    public double contarColaboradoresNasMetasPorPeriodoColaboradoresResponsaveisBI(Date ini, Date fim, List<UsuarioVO> usuarioVOList, boolean metaAberta, boolean controlarAcesso, Integer empresa, int nivelMontarDados) throws Exception {
        StringBuilder sqlStr = new StringBuilder();
        sqlStr.append("SELECT count(distinct colaboradorresponsavel) FROM fecharmeta ");
        sqlStr.append("INNER JOIN aberturameta ON fecharmeta.aberturameta = aberturameta.codigo ? ");
        sqlStr.append("WHERE aberturameta.dia >= '").append(Uteis.getSQLData(ini)).append("' AND aberturameta.dia <= '").append(Uteis.getSQLData(fim)).append("' AND ");
        sqlStr.append("aberturameta.metaemaberto = ").append(metaAberta);
        if (!UteisValidacao.emptyNumber(empresa)) {
            sqlStr.append(" AND aberturameta.empresa = ").append(empresa);
        }

        int posicao = sqlStr.indexOf("?");
        StringBuilder strTemp = new StringBuilder();
        for (UsuarioVO co : usuarioVOList) {
            if (strTemp.toString().isEmpty()) {
                strTemp.append("(");
            } else {
                strTemp.append(",");
            }
            strTemp.append(co.getCodigo());
        }
        if (strTemp.toString().isEmpty())
            sqlStr.replace(posicao, posicao + 1, "");
        else {
            strTemp.append(")");
            sqlStr.replace(posicao, posicao + 1, " INNER JOIN usuario ON usuario.codigo = aberturameta.colaboradorresponsavel AND usuario.codigo IN " + strTemp.toString());
        }

        return (double) contar(sqlStr.toString(), con);
    }

    public ResultSet consultarGraficoBICRMDesempenho(Date dataInicio, Date dataFim, List<UsuarioVO> usuarioVOList, boolean diario, boolean impressao) throws Exception {
        String codigosUsuario = Uteis.retornarCodigos(usuarioVOList);
        if (usuarioVOList.isEmpty()) {
            codigosUsuario = "0";
        }

        StringBuilder sql = new StringBuilder();
        sql.append("select \n");
        sql.append("am.colaboradorresponsavel  as usuariometa, \n");
        sql.append("u.nome  as usuario, \n");
        if (diario) {
            sql.append("to_char(am.dia, 'YYYYMMDD') as mesAnoOrdem, \n");
            sql.append("to_char(am.dia, 'DD/MM/YYYY') as mesAno, \n");
        } else {
            sql.append("to_char(am.dia, 'YYYYMM') as mesAnoOrdem, \n");
            sql.append("to_char(am.dia, 'MM/YYYY') as mesAno, \n");
        }
        if (impressao) {
            sql.append("to_char(am.dia, 'MM') as mes, \n");
            sql.append("to_char(am.dia, 'YYYY') as ano, \n");
        }
        sql.append("sum(fm.meta) as meta, \n");
        sql.append("sum(fm.metaatingida) as metaatingida, \n");
        sql.append("sum(fm.repescagem) as repescagem \n");
        sql.append("from fecharmeta fm \n");
        sql.append("inner join aberturameta am on am.codigo = fm.aberturameta \n");
        sql.append("inner join usuario u on u.codigo = am.colaboradorresponsavel \n");
        sql.append("where am.dia::date between '").append(Uteis.getData(dataInicio)).append("' and '").append(Uteis.getData(dataFim)).append("' \n");
        sql.append("and fm.identificadormeta not in ('CI','CV','CE','CA','CD','IN','CR') \n");
        sql.append("and am.colaboradorresponsavel in (").append(codigosUsuario).append(") \n");
        sql.append("group by usuariometa, usuario, mesAnoOrdem, mesAno \n");
        if (impressao) {
            sql.append(", mes, ano \n");
        }
        sql.append("order by mesAnoOrdem, usuario");

        PreparedStatement sqlConsultar = con.prepareStatement(sql.toString());
        return sqlConsultar.executeQuery();
    }

    public ResultSet consultarLegendaBICRMDesempenho(Date dataInicio, Date dataFim, List<UsuarioVO> usuarioVOList) throws Exception {
        String codigosUsuario = Uteis.retornarCodigos(usuarioVOList);
        if (usuarioVOList.isEmpty()) {
            codigosUsuario = "0";
        }

        StringBuilder sql = new StringBuilder();
        sql.append("select distinct(usuariometa),usuario From (select \n");
        sql.append("am.colaboradorresponsavel  as usuariometa, \n");
        sql.append("u.nome  as usuario, \n");
        sql.append("to_char(am.dia, 'YYYYMM') as mesAnoOrdem, \n");
        sql.append("to_char(am.dia, 'MM/YYYY') as mesAno, \n");
        sql.append("sum(fm.meta) as meta, \n");
        sql.append("sum(fm.metaatingida) as metaatingida, \n");
        sql.append("sum(fm.repescagem) as repescagem \n");
        sql.append("from fecharmeta fm \n");
        sql.append("inner join aberturameta am on am.codigo = fm.aberturameta \n");
        sql.append("inner join usuario u on u.codigo = am.colaboradorresponsavel \n");
        sql.append("where am.dia::date between '").append(Uteis.getData(dataInicio)).append("' and '").append(Uteis.getData(dataFim)).append("' \n");
        sql.append("and fm.identificadormeta not in ('CI','CV','CE','CA','CD','IN','CR') \n");
        sql.append("and am.metaemaberto = false \n");
        sql.append("and am.colaboradorresponsavel in (").append(codigosUsuario).append(") \n");
        sql.append("group by usuariometa, usuario, mesAnoOrdem, mesAno) as sql");

        PreparedStatement sqlConsultar = con.prepareStatement(sql.toString());
        return sqlConsultar.executeQuery();
    }

    public String consultarGraficoResultadoBICRMLegenda(Date dataInicio, Date dataFim, List<UsuarioVO> usuarioVOList) throws Exception {
        if (usuarioVOList.isEmpty()) {
            return "[]";
        }
        //MONTAR O DADOS DA LEGENDA DO GRAFICO
        StringBuilder legenda = new StringBuilder();
        legenda.append("[");

        try (ResultSet cl = consultarLegendaBICRMDesempenho(dataInicio, dataFim, usuarioVOList)) {
            while (cl.next()) {
                legenda.append("{\"balloonText\": \"[[title]] <br> <b>[[value]] %</b>\",");
                legenda.append("\"lineAlpha\": 1,");
                legenda.append("\"lineThickness\": 1,");
                legenda.append("\"bullet\": \"round\",");
                legenda.append("\"type\": \"line\",");
                legenda.append("\"valueField\": \"").append("usuario").append(cl.getInt("usuariometa")).append("\",");
                legenda.append("\"title\": \"").append(Uteis.obterPrimeiroNomeConcatenadoSobreNome(cl.getString("usuario"))).append("\"},");
            }
        }
        legenda.deleteCharAt(legenda.toString().length() - 1);
        legenda.append("]");
        return legenda.toString();
    }

    public String consultarGraficoResultadoBICRM(Date dataInicio, Date dataFim, List<UsuarioVO> usuarioVOList) throws Exception {
        Date dataInicioParametro = Uteis.somarDias(dataInicio, 0);
        Date dataFimParametro = Uteis.somarDias(dataFim, 0);
        //MONTAR O DADOS DO GRAFICO
        List<Date> listaMeses = Uteis.getMesesEntreDatas(dataInicio, dataFim);

        StringBuilder json = new StringBuilder();
        json.append("[");

        if (listaMeses != null) {
            for (Date mes : listaMeses) {
                dataInicio = Uteis.obterPrimeiroDiaMes(mes);
                if (Calendario.menor(dataInicio, dataInicioParametro)) {
                    dataInicio = dataInicioParametro;
                }
                dataFim = Uteis.obterUltimoDiaMes(mes);
                if (Calendario.maior(dataFim, dataFimParametro)) {
                    dataFim = dataFimParametro;
                }
                try (ResultSet rs = consultarGraficoBICRMDesempenho(dataInicio, dataFim, usuarioVOList, false, false)) {

                    String mesAno = Uteis.getMesNomeReferencia(mes);
                    String mesAnoOrdem = Uteis.getAnoMesReferenciaData(mes);

                    json.append("{").append("\"mesAnoOrdem\":").append("\"").append(mesAnoOrdem).append("\",");
                    json.append("\"mesAno\":").append("\"").append(mesAno).append("\",");

                    while (rs.next()) {
                        Double meta = rs.getDouble("meta");
                        Double metaatingida = rs.getDouble("metaatingida");
                        Double repescagem = rs.getDouble("repescagem");
                        Double porcentagem;
                        if (meta == 0 || (metaatingida + repescagem == 0)) {
                            porcentagem = 0.0;
                        } else {
                            Double totalRea = metaatingida + repescagem;
                            Double porc = ((totalRea / meta) * 100.0);
                            porcentagem = Uteis.arredondarForcando2CasasDecimais(porc);
                        }

                        json.append("\"usuario").append(rs.getInt("usuariometa")).append("\":").append("").append(porcentagem).append(",");
                    }
                }
                json.deleteCharAt(json.toString().length() - 1);
                json.append("},");
            }

        }
        json.deleteCharAt(json.toString().length() - 1);
        json.append("]");

        return json.toString();
    }

    public List<FecharMetaVO> consultarBIDetalhado(Date dataInicio, Date dataFim, String identificadorMeta, List<UsuarioVO> usuariosSelecionados, Integer empresa) throws Exception {
        List<FecharMetaVO> listaRetornar = new ArrayList<FecharMetaVO>();
        String codigosUsuario = Uteis.retornarCodigos(usuariosSelecionados);
        StringBuilder sql = new StringBuilder();

        sql.append("select \n");
        sql.append("am.colaboradorresponsavel, \n");
        sql.append("us.nome, \n");
        sql.append("sum(fm.meta) as meta, \n");
        sql.append("sum(fm.metaatingida) as metaatingida, \n");
        sql.append("sum(fm.repescagem) as repescagem \n");
        sql.append("from fecharmeta fm \n");
        sql.append("inner join aberturameta am on am.codigo = fm.aberturameta \n");
        sql.append("inner join usuario us on us.codigo = am.colaboradorresponsavel \n");
        sql.append("where fm.identificadormeta = '").append(identificadorMeta).append("' \n");
        sql.append("and am.empresa = ").append(empresa).append(" \n");
        sql.append("and am.colaboradorresponsavel in (").append(codigosUsuario).append(") \n");
        sql.append("and fm.dataregistro::date between '").append(getDataJDBC(dataInicio)).append("' and '").append(getDataJDBC(dataFim)).append("' \n");
        sql.append("group by 1,2 \n");

        try (PreparedStatement pst = con.prepareStatement(sql.toString())) {
            try (ResultSet dadosSQL = pst.executeQuery()) {
                while (dadosSQL.next()) {
                    FecharMetaVO fecharMetaVO = new FecharMetaVO();
                    fecharMetaVO.setUsuarioVO(new UsuarioVO());
                    fecharMetaVO.getUsuarioVO().setCodigo(dadosSQL.getInt("colaboradorresponsavel"));
                    fecharMetaVO.getUsuarioVO().setNome(dadosSQL.getString("nome"));
                    fecharMetaVO.setMeta(dadosSQL.getDouble("meta"));
                    fecharMetaVO.setMetaAtingida(dadosSQL.getDouble("metaatingida"));
                    fecharMetaVO.setRepescagem(dadosSQL.getDouble("repescagem"));
                    fecharMetaVO.calcularPorcentagem();
                    listaRetornar.add(fecharMetaVO);
                }
            }
        }

        return listaRetornar;
    }

    public boolean metaCRMExtraUtilizada(Integer malaDireta) throws Exception {
        StringBuffer sqlStr = new StringBuffer();
        sqlStr.append("select exists (select codigo from  fecharmetadetalhado  where obtevesucesso = true and fecharmeta in (select codigo from fecharmeta  where maladireta = " + malaDireta + "))");
        try (Statement stm = con.createStatement()) {
            try (ResultSet tabelaResultado = stm.executeQuery(sqlStr.toString())) {
                tabelaResultado.next();
                return tabelaResultado.getBoolean(1);
            }
        }
    }

    public List<FecharMetaVO> consultarFecharMetaPorMalaDiretaMetaExtra(Integer malaDireta, int nivelMontarDados) throws Exception {
        consultarCRM(getIdEntidade(), false);
        String sqlStr = "SELECT * FROM FecharMeta WHERE maladireta = " + malaDireta + " ;";
        try (Statement stm = con.createStatement()) {
            try (ResultSet tabelaResultado = stm.executeQuery(sqlStr)) {
                return (montarDadosConsulta(tabelaResultado, nivelMontarDados, con));
            }
        }
    }

    public List<ItemRelatorioTO> consultarImpressaoGraficoDesempenhoResultadoBICRM(Date dataInicio, Date dataFim, List<UsuarioVO> usuarioVOList) throws Exception {
        List<ItemRelatorioTO> retorno = new ArrayList<ItemRelatorioTO>();

        //MONTAR O DADOS DO GRAFICO
        List<Date> listaMeses = Uteis.getMesesEntreDatas(dataInicio, dataFim);

        if (listaMeses != null) {
            for (Date mes : listaMeses) {
                dataInicio = Uteis.obterPrimeiroDiaMes(mes);
                dataFim = Uteis.obterUltimoDiaMes(mes);
                try (ResultSet rs = consultarGraficoBICRMDesempenho(dataInicio, dataFim, usuarioVOList, false, true)) {

                    String mesAno = Uteis.getMesNomeReferencia(mes);
                    String mesAnoOrdem = Uteis.getAnoMesReferenciaData(mes);

                    while (rs.next()) {
                        ItemRelatorioTO itemRelatorioTO = new ItemRelatorioTO();

                        Integer mesRS = rs.getInt("mes");
                        Integer anoRS = rs.getInt("ano");
                        String mesAnoRS = rs.getString("mesAno");
                        Double meta = rs.getDouble("meta");
                        Double metaatingida = rs.getDouble("metaatingida");
                        Double repescagem = rs.getDouble("repescagem");
                        Double porcentagem;
                        if (meta == 0 || (metaatingida + repescagem == 0)) {
                            porcentagem = 0.0;
                        } else {
                            Double totalRea = metaatingida + repescagem;
                            Double porc = ((totalRea / meta) * 100.0);
                            porcentagem = Uteis.arredondarForcando2CasasDecimais(porc);
                        }

                        itemRelatorioTO.setPorcentagem(Formatador.formatarValorMonetarioSemMoeda(porcentagem) + "%");
                        itemRelatorioTO.setMeta(meta.intValue());
                        itemRelatorioTO.setMetaAtingida(metaatingida.intValue());
                        itemRelatorioTO.setRepescagem(repescagem.intValue());
                        itemRelatorioTO.setMes(mesRS);
                        itemRelatorioTO.setAno(anoRS);
                        itemRelatorioTO.setMesAno(mesAnoRS);

                        Integer codUsuario = rs.getInt("usuariometa");
                        UsuarioVO usuarioVO = null;
                        if (!UteisValidacao.emptyNumber(codUsuario)) {
                            usuarioVO = getFacade().getUsuario().consultarPorCodigoUsuario(codUsuario, false, Uteis.NIVELMONTARDADOS_DADOSENTIDADESPRINCIPAIS);
                        }

                        if (usuarioVO != null && !usuarioVO.getCodigo().equals(0)) {
                            itemRelatorioTO.setNome(usuarioVO.getNome());
                            itemRelatorioTO.setCodUsuario(usuarioVO.getCodigo());
                        }
                        retorno.add(itemRelatorioTO);
                    }
                }
            }
        }
        return retorno;
    }

    public Double obterMetaAlcancado(Date dataInicio, Date dataFim, int empresa) throws Exception {
        List<GrupoColaboradorParticipanteVO> grupoColaboradorParticipanteVOS = getFacade().getGrupoColaboradorParticipante().consultarPorCodigo(0, false, Uteis.NIVELMONTARDADOS_TODOS);
        List<UsuarioVO> usuarioVOS = new ArrayList<UsuarioVO>();
        for (GrupoColaboradorParticipanteVO grupoColaboradorParticipanteVO : grupoColaboradorParticipanteVOS) {
            usuarioVOS.add(grupoColaboradorParticipanteVO.getUsuarioParticipante());
        }

        Double soma = 0.0;
        List<MetaCRMTO> metaCRMTOS = getFacade().getFecharMeta().consultarMeta(dataInicio, dataFim, Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA, usuarioVOS, empresa, true);
        for (MetaCRMTO metaCRMTO : metaCRMTOS) {
            //OS INDICADORES NÃO ENTRA NO TOTAL DE METAS
            if (!metaCRMTO.getTipoFaseCRM().equals(TipoFaseCRM.INDICADORES)) {
                for (TipoMetaCRMTO tipoMetaCRMTO : metaCRMTO.getListaTipoMetaVO()) {
                    if (!tipoMetaCRMTO.getFasesCRMEnum().equals(FasesCRMEnum.AGENDAMENTOS_LIGACOES) && !tipoMetaCRMTO.getFasesCRMEnum().equals(FasesCRMEnum.INDICACOES) && !tipoMetaCRMTO.getFasesCRMEnum().getTipoFase().equals(TipoFaseCRM.CRMEXTRA)) {
                        soma += tipoMetaCRMTO.getTotalMetaRealizada();
                    }
                }
            }
        }

        return soma;
    }
    public Double obterSomaMeta(Date dataInicio, Date dataFim, int empresa) throws Exception {
        List<GrupoColaboradorParticipanteVO> grupoColaboradorParticipanteVOS = getFacade().getGrupoColaboradorParticipante().consultarPorCodigo(0, false, Uteis.NIVELMONTARDADOS_TODOS);
        List<UsuarioVO> usuarioVOS = new ArrayList<UsuarioVO>();
        for (GrupoColaboradorParticipanteVO grupoColaboradorParticipanteVO : grupoColaboradorParticipanteVOS) {
            usuarioVOS.add(grupoColaboradorParticipanteVO.getUsuarioParticipante());
        }

        Double soma = 0.0;
        List<MetaCRMTO> metaCRMTOS = getFacade().getFecharMeta().consultarMeta(dataInicio, dataFim, Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA, usuarioVOS, empresa, true);
        for (MetaCRMTO metaCRMTO : metaCRMTOS) {
            //OS INDICADORES NÃO ENTRA NO TOTAL DE METAS
            if (!metaCRMTO.getTipoFaseCRM().equals(TipoFaseCRM.INDICADORES)) {
                for (TipoMetaCRMTO tipoMetaCRMTO : metaCRMTO.getListaTipoMetaVO()) {
                    if (!tipoMetaCRMTO.getFasesCRMEnum().equals(FasesCRMEnum.AGENDAMENTOS_LIGACOES) && !tipoMetaCRMTO.getFasesCRMEnum().equals(FasesCRMEnum.INDICACOES) && !tipoMetaCRMTO.getFasesCRMEnum().getTipoFase().equals(TipoFaseCRM.CRMEXTRA)) {
                        soma += tipoMetaCRMTO.getTotalMeta();
                    }
                }
            }
        }

        return soma;
    }

    public void alterarFecharMetaMalaDireta(MalaDiretaVO malaDireta) throws Exception {
        String sql = "UPDATE Fecharmeta set nomemeta = ? WHERE maladireta = ?";
        PreparedStatement stm = con.prepareStatement(sql);
        int i = 1;
        stm.setString(i++,malaDireta.getTitulo());
        stm.setInt(i++,malaDireta.getCodigo());
        stm.execute();
    }

    public void excluirFecharMetaMalaDireta(MalaDiretaVO malaDireta) throws Exception {
        String sql = "delete from Fecharmeta WHERE maladireta = ?";
        PreparedStatement stm = con.prepareStatement(sql);
        stm.setInt(1,malaDireta.getCodigo());
        stm.execute();
    }
    
    public void adicionarLeadMetaDoDia(ConversaoLeadVO conversaoLeadVO, Date data) throws Exception{
        FecharMetaVO metaDia = consultarMetaPorDiaPorColaboradorResponsavel(FasesCRMEnum.LEADS_HOJE.getSigla(), data, conversaoLeadVO.getResponsavel().getCodigo(), false, Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA, conversaoLeadVO.getLead().getEmpresa().getCodigo());
        if(!UteisValidacao.emptyNumber(metaDia.getCodigo())){ //se houver meta aberta no dia e não ter passado do horário limite, meta é atualizada
            FecharMetaDetalhadoVO novo = new FecharMetaDetalhadoVO();
            if(!UteisValidacao.emptyNumber(conversaoLeadVO.getLead().getCliente().getCodigo())){
                novo.setCliente(conversaoLeadVO.getLead().getCliente());
            } else if (!UteisValidacao.emptyNumber(conversaoLeadVO.getLead().getPassivo().getCodigo())) {
                novo.setPassivo(conversaoLeadVO.getLead().getPassivo());
            } else {
                novo.setIndicado(conversaoLeadVO.getLead().getIndicado());
            }
            novo.setCodigoOrigem(conversaoLeadVO.getCodigo());
            novo.setOrigem("CONVERSAOLEAD");
            novo.setFecharMeta(metaDia);
            FecharMetaDetalhado fmDao = new FecharMetaDetalhado(con);
            fmDao.incluir(novo);
            metaDia.setMeta(metaDia.getMeta() + 1);
            metaDia.calcularPorcentagem();
            executarConsultaUpdate("update fecharmeta set meta = "+metaDia.getMeta()+", porcentagem = "+metaDia.getPorcentagem()+" where codigo = "+metaDia.getCodigo(), con);
        }
    }
    
    public List consultarFecharMetaLeads(Integer fecharMeta, int nivelMontarDados) throws Exception {
        consultarCRM(getIdEntidade(), false);
        String fasesVenda = "";
        for (FasesCRMEnum fase : FasesCRMEnum.values()) {
            if (fase.getTipoFase().equals(TipoFaseCRM.LEAD)) {
                fasesVenda = fasesVenda + ",'" + fase.getSigla() + "'";
            }
        }
        List objetos = new ArrayList();
        String sql = "SELECT * FROM FecharMeta WHERE aberturaMeta = ? " +
                "and identificadormeta IN (" + fasesVenda.replaceFirst(",", "") + ")";
        try (PreparedStatement sqlConsulta = con.prepareStatement(sql)) {
            sqlConsulta.setInt(1, fecharMeta);
            try (ResultSet resultado = sqlConsulta.executeQuery()) {
                while (resultado.next()) {
                    FecharMetaVO novoObj = FecharMeta.montarDados(resultado, nivelMontarDados, con);
                    objetos.add(novoObj);
                }
            }
        }

        return objetos;
    }
    
    public void removerAgendamentoNasMetas(Integer agenda) throws Exception {
        FecharMetaDetalhado fdDAO = new FecharMetaDetalhado(con);
        List<FecharMetaDetalhadoVO> listaAssociada = fdDAO.consultarFecharMetaDetalhadoPorCodigoAgenda(agenda,null,Uteis.NIVELMONTARDADOS_DADOSBASICOS);
        for(FecharMetaDetalhadoVO fmDetalhado: listaAssociada){
            FecharMetaVO fecharMeta = consultarPorChavePrimaria(fmDetalhado.getFecharMeta().getCodigo(), Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA);
            if (fecharMeta.getMetaCalculada()) {
                fecharMeta.setMeta(fecharMeta.getMeta() - 1);
                if(fmDetalhado.isObteveSucesso()){
                     fecharMeta.setMetaAtingida(fecharMeta.getMetaAtingida() - 1);
                }
                fecharMeta.calcularPorcentagem();
                alteraSemSubordinada(fecharMeta);
            }
            fdDAO.excluir(fmDetalhado);
        }
    }

    public void removerMetasQueJaTiveramAgendamento(FecharMetaDetalhadoVO excluir) throws Exception {
        FecharMetaDetalhado fdDAO = new FecharMetaDetalhado(con);
        FecharMetaVO fecharMeta = consultarPorChavePrimaria(excluir.getFecharMeta().getCodigo(), Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA);
        if (fecharMeta.getMetaCalculada()) {
            fecharMeta.setMeta(fecharMeta.getMeta() - 1);
            if(excluir.isObteveSucesso()){
                fecharMeta.setMetaAtingida(fecharMeta.getMetaAtingida() - 1);
            }
            fecharMeta.calcularPorcentagem();
            alteraSemSubordinada(fecharMeta);
        }
        fdDAO.excluir(excluir);
    }

}

package negocio.facade.jdbc.crm;

import br.com.pactosolucoes.enumeradores.FasesCRMEnum;
import br.com.pactosolucoes.enumeradores.OrigemSistemaEnum;
import br.com.pactosolucoes.integracao.amigoFit.ClienteAmigoFitJSON;
import br.com.pactosolucoes.integracao.importacao.ClienteJSON;
import controle.crm.MetaCRMControle;
import negocio.comuns.arquitetura.UsuarioVO;
import negocio.comuns.basico.ClienteVO;
import negocio.comuns.basico.ColaboradorVO;
import negocio.comuns.crm.EventoVO;
import negocio.comuns.crm.FecharMetaDetalhadoVO;
import negocio.comuns.crm.FecharMetaVO;
import negocio.comuns.crm.HistoricoContatoVO;
import negocio.comuns.crm.IndicacaoVO;
import negocio.comuns.crm.IndicadoVO;
import negocio.comuns.utilitarias.*;
import negocio.facade.jdbc.arquitetura.SuperEntidade;
import negocio.facade.jdbc.arquitetura.Usuario;
import negocio.facade.jdbc.basico.Cliente;
import negocio.facade.jdbc.basico.Colaborador;
import negocio.facade.jdbc.crm.fecharMetaDetalhado.FecharMetaDetalhado;
import negocio.interfaces.crm.IndicacaoInterfaceFacade;
import org.json.JSONArray;
import org.json.JSONObject;
import org.postgresql.util.GT;
import servicos.integracao.impl.amigoFit.IntegracaoAmigoFitServiceImpl;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.sql.Statement;
import java.text.DecimalFormat;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.logging.Level;
import java.util.logging.Logger;

/**
 * Classe de persistência que encapsula todas as operações de manipulação dos
 * dados da classe <code>IndicacaoVO</code>. Responsável por implementar
 * operações como incluir, alterar, excluir e consultar pertinentes a classe
 * <code>IndicacaoVO</code>. Encapsula toda a interação com o banco de dados.
 *
 * @see IndicacaoVO
 * @see SuperEntidade
 */
public class Indicacao extends SuperEntidade implements IndicacaoInterfaceFacade {

    public Indicacao() throws Exception {
        super();
    }

    public Indicacao(Connection con) throws Exception {
		super(con);
	}

	/**
     * Operação responsável por retornar um novo objeto da classe
     * <code>IndicacaoVO</code>.
     */
    public IndicacaoVO novo() throws Exception {
        incluirCRM(getIdEntidade());
        return new IndicacaoVO();
    }

    /**
     * Operação responsável por incluir no banco de dados um objeto da classe
     * <code>IndicacaoVO</code>. Primeiramente valida os dados (
     * <code>validarDados</code>) do objeto. Verifica a conexão com o banco de
     * dados e a permissão do usuário para realizar esta operacão na entidade.
     * Isto, através da operação <code>incluir</code> da superclasse.
     *
     * @param obj
     *            Objeto da classe <code>IndicacaoVO</code> que será gravado no
     *            banco de dados.
     * @exception Exception
     *                Caso haja problemas de conexão, restrição de acesso ou
     *                validação de dados.
     */
    public void incluir(IndicacaoVO obj) throws Exception {
        try {
            con.setAutoCommit(false);
            IndicacaoVO.validarDados(obj);
            incluirCRM(getIdEntidade());
            incluirSomenteIndicacao(obj);

            Indicado indicacaoDao = new Indicado(con);
            indicacaoDao.incluirIndicados(obj, obj.getIndicadoVOs());
            if (!obj.getColaboradorResponsavel().getAdministrador()) {
                FecharMeta fecharMetaDao = new FecharMeta(con);
                FecharMetaVO fecharMetaVO = fecharMetaDao.consultarPorIdentificadorMetaPorDiaPorColaborador("IN", obj.getDiaAbertura(), obj.getColaboradorResponsavel().getCodigo(), false, obj.getEmpresa().getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS);
                fecharMetaDao.executarAtualizacaoMetaAtingidaPorIndicado(fecharMetaVO, (long) obj.getIndicadoVOs().size(), true);
                fecharMetaDao = null;
            }
            indicacaoDao =null;

            con.commit();
        } catch (Exception e) {
            obj.setNovoObj(true);
            con.rollback();
            con.setAutoCommit(true);
            throw e;
        } finally {
            con.setAutoCommit(true);
        }
    }

    public void incluirSomenteIndicacao(IndicacaoVO obj) throws Exception{
        String sql = "INSERT INTO Indicacao( responsavelCadastro, clienteQueIndicou, colaboradorQueIndicou, colaboradorResponsavel, observacao, dia, evento, origemSistema ) VALUES ( ?, ?, ?, ?, ?, ?, ?, ? )";
        try (PreparedStatement sqlInserir = con.prepareStatement(sql)) {
            if (obj.getResponsavelCadastro().getCodigo() != 0) {
                sqlInserir.setInt(1, obj.getResponsavelCadastro().getCodigo());
            } else {
                sqlInserir.setNull(1, 0);
            }
            if (obj.getClienteQueIndicou().getCodigo() != 0) {
                sqlInserir.setInt(2, obj.getClienteQueIndicou().getCodigo());
            } else {
                sqlInserir.setNull(2, 0);
            }
            if (obj.getColaboradorQueIndicou().getCodigo() != 0) {
                sqlInserir.setInt(3, obj.getColaboradorQueIndicou().getCodigo());
            } else {
                sqlInserir.setNull(3, 0);
            }
            if (obj.getColaboradorResponsavel().getCodigo() != 0) {
                sqlInserir.setInt(4, obj.getColaboradorResponsavel().getCodigo());
            } else {
                sqlInserir.setNull(4, 0);
            }
            sqlInserir.setString(5, obj.getObservacao());
            sqlInserir.setTimestamp(6, Uteis.getDataJDBCTimestamp(obj.getDia()));
            if (obj.getEvento().getCodigo() != 0) {
                sqlInserir.setInt(7, obj.getEvento().getCodigo());
            } else {
                sqlInserir.setNull(7, 0);
            }
            sqlInserir.setInt(8, obj.getOrigemSistemaEnum().getCodigo());
            sqlInserir.execute();
        }
        obj.setCodigo(obterValorChavePrimariaCodigo());
        obj.setNovoObj(false);
    }

    /**
     * Operação responsável por alterar no BD os dados de um objeto da classe
     * <code>IndicacaoVO</code>. Sempre utiliza a chave primária da classe como
     * atributo para localização do registro a ser alterado. Primeiramente
     * valida os dados (<code>validarDados</code>) do objeto. Verifica a conexão
     * com o banco de dados e a permissão do usuário para realizar esta operacão
     * na entidade. Isto, através da operação <code>alterar</code> da
     * superclasse.
     *
     * @param obj
     *            Objeto da classe <code>IndicacaoVO</code> que será alterada no
     *            banco de dados.
     * @exception Exception
     *                Caso haja problemas de conexão, restrição de acesso ou
     *                validação de dados.
     */
    public void alterar(IndicacaoVO obj) throws Exception {
        try {
            con.setAutoCommit(false);
            IndicacaoVO.validarDados(obj);
            alterarCRM(getIdEntidade());
            String sql = "UPDATE Indicacao set responsavelCadastro=?, clienteQueIndicou=?, colaboradorQueIndicou=?,colaboradorResponsavel=?, observacao=?, dia=?, evento=?  WHERE ((codigo = ?))";
            try (PreparedStatement sqlAlterar = con.prepareStatement(sql)) {
                if (obj.getResponsavelCadastro().getCodigo() != 0) {
                    sqlAlterar.setInt(1, obj.getResponsavelCadastro().getCodigo());
                } else {
                    sqlAlterar.setNull(1, 0);
                }
                if (obj.getClienteQueIndicou().getCodigo() != 0) {
                    sqlAlterar.setInt(2, obj.getClienteQueIndicou().getCodigo());
                } else {
                    sqlAlterar.setNull(2, 0);
                }
                if (obj.getColaboradorQueIndicou().getCodigo() != 0) {
                    sqlAlterar.setInt(3, obj.getColaboradorQueIndicou().getCodigo());
                } else {
                    sqlAlterar.setNull(3, 0);
                }
                if (obj.getColaboradorResponsavel().getCodigo() != 0) {
                    sqlAlterar.setInt(4, obj.getColaboradorResponsavel().getCodigo());
                } else {
                    sqlAlterar.setNull(4, 0);
                }
                sqlAlterar.setString(5, obj.getObservacao());
                sqlAlterar.setTimestamp(6, Uteis.getDataJDBCTimestamp(obj.getDia()));
                if (obj.getEvento().getCodigo() != 0) {
                    sqlAlterar.setInt(7, obj.getEvento().getCodigo());
                } else {
                    sqlAlterar.setNull(7, 0);
                }
                sqlAlterar.setInt(8, obj.getCodigo());
                sqlAlterar.execute();
            }
            Indicado indicacaoDao = new Indicado(con);
            indicacaoDao.alterarIndicados(obj, obj.getIndicadoVOs());
            indicacaoDao = null;
            con.commit();
        } catch (Exception e) {
            con.rollback();
            con.setAutoCommit(true);
            throw e;
        } finally {
            con.setAutoCommit(true);
        }
    }

    /**
     * Operação responsável por excluir no BD um objeto da classe
     * <code>IndicacaoVO</code>. Sempre localiza o registro a ser excluído
     * através da chave primária da entidade. Primeiramente verifica a conexão
     * com o banco de dados e a permissão do usuário para realizar esta operacão
     * na entidade. Isto, através da operação <code>excluir</code> da
     * superclasse.
     *
     * @param obj
     *            Objeto da classe <code>IndicacaoVO</code> que será removido no
     *            banco de dados.
     * @exception Exception
     *                Caso haja problemas de conexão ou restrição de acesso.
     */
    public void excluir(IndicacaoVO obj) throws Exception {
        excluirCRM(getIdEntidade());
        String sql = "DELETE FROM Indicacao WHERE ((codigo = ?))";
        try (PreparedStatement sqlExcluir = con.prepareStatement(sql)) {
            sqlExcluir.setInt(1, obj.getCodigo());
            sqlExcluir.execute();
        }
    }

    /**
     * Responsável por realizar uma consulta de <code>Indicacao</code> através
     * do valor do atributo <code>situacao</code> da classe <code>Cliente</code>
     * Faz uso da operação <code>montarDadosConsulta</code> que realiza o
     * trabalho de prerarar o List resultante.
     *
     * @return List Contendo vários objetos da classe <code>IndicacaoVO</code>
     *         resultantes da consulta.
     * @exception Exception
     *                Caso haja problemas de conexão ou restrição de acesso.
     */
    public List consultarPorSituacaoCliente(String valorConsulta, int nivelMontarDados) throws Exception {
        consultarCRM(getIdEntidade(), true);
        String sqlStr = "SELECT Indicacao.* FROM Indicacao, Cliente WHERE Indicacao.cliente = Cliente.codigo and upper( Cliente.situacao ) like('" + valorConsulta.toUpperCase() + "%') ORDER BY Cliente.situacao";
        try (Statement stm = con.createStatement()) {
            try (ResultSet tabelaResultado = stm.executeQuery(sqlStr)) {
                return montarDadosConsulta(tabelaResultado, nivelMontarDados, con);
            }
        }
    }

    /**
     * Responsável por realizar uma consulta de <code>Indicacao</code> através
     * do valor do atributo <code>String nomeIndicado</code>. Retorna os
     * objetos, com início do valor do atributo idêntico ao parâmetro fornecido.
     * Faz uso da operação <code>montarDadosConsulta</code> que realiza o
     * trabalho de prerarar o List resultante.
     *
     * @param controlarAcesso
     *            Indica se a aplicação deverá verificar se o usuário possui
     *            permissão para esta consulta ou não.
     * @return List Contendo vários objetos da classe <code>IndicacaoVO</code>
     *         resultantes da consulta.
     * @exception Exception
     *                Caso haja problemas de conexão ou restrição de acesso.
     */
    public List consultarPorNomeIndicado(String valorConsulta, boolean controlarAcesso, int nivelMontarDados) throws Exception {
        consultarCRM(getIdEntidade(), controlarAcesso);
        String sqlStr = "SELECT * FROM Indicacao WHERE upper( nomeIndicado ) like('" + valorConsulta.toUpperCase() + "%') ORDER BY nomeIndicado";
        try (Statement stm = con.createStatement()) {
            try (ResultSet tabelaResultado = stm.executeQuery(sqlStr)) {
                return (montarDadosConsulta(tabelaResultado, nivelMontarDados, con));
            }
        }
    }

    /**
     * Responsável por realizar uma consulta de <code>Indicacao</code> através
     * do valor do atributo <code>Date dia</code>. Retorna os objetos com
     * valores pertecentes ao período informado por parâmetro. Faz uso da
     * operação <code>montarDadosConsulta</code> que realiza o trabalho de
     * prerarar o List resultante.
     *
     * @param controlarAcesso
     *            Indica se a aplicação deverá verificar se o usuário possui
     *            permissão para esta consulta ou não.
     * @return List Contendo vários objetos da classe <code>IndicacaoVO</code>
     *         resultantes da consulta.
     * @exception Exception
     *                Caso haja problemas de conexão ou restrição de acesso.
     */
    public List consultarPorDia(Date prmIni, Date prmFim, boolean controlarAcesso, int nivelMontarDados) throws Exception {
        consultarCRM(getIdEntidade(), controlarAcesso);
        String sqlStr = "SELECT * FROM Indicacao WHERE ((Cast (dia as Date) >= '" + Uteis.getDataJDBC(prmIni) + "') and (Cast (dia as Date) <= '" + Uteis.getDataJDBC(prmFim) + "')) ORDER BY dia";
        try (Statement stm = con.createStatement()) {
            try (ResultSet tabelaResultado = stm.executeQuery(sqlStr)) {
                return (montarDadosConsulta(tabelaResultado, nivelMontarDados, con));
            }
        }
    }

    public List<IndicacaoVO> consultarPorPeriodoColaboradorCliente(Date prmIni, Date prmFim, Integer colaborador, Integer cliente, boolean controlarAcesso, int nivelMontarDados) throws Exception{
        consultarCRM(getIdEntidade(), controlarAcesso);

        if( null == prmIni){
            prmIni = Uteis.obterPrimeiroDiaMes(Calendario.hoje());
        }
        if(null == prmFim){
            prmFim = Calendario.hoje();
        }

        StringBuilder sb = new StringBuilder();
        sb.append("SELECT * FROM Indicacao WHERE (Cast (dia as Date) between '").append(Uteis.getDataJDBC(prmIni)).append("' and '").append(Uteis.getDataJDBC(prmFim)).append("') ");
        if(null != colaborador && colaborador > 0){
            sb.append(" and responsavelcadastro = ").append(colaborador);
        }

        if(null != cliente && cliente > 0){
            sb.append(" and clientequeindicou = ").append(cliente);
        }

        try (Statement stm = con.createStatement()) {
            try (ResultSet tabelaResultado = stm.executeQuery(sb.toString())) {
                return (montarDadosConsulta(tabelaResultado, nivelMontarDados, con));
            }
        }
    }

    /**
     * Responsável por realizar uma consulta de <code>Indicacao</code> através
     * do valor do atributo <code>Integer clienteQueIndicou</code>. Retorna os
     * objetos com valores iguais ou superiores ao parâmetro fornecido. Faz uso
     * da operação <code>montarDadosConsulta</code> que realiza o trabalho de
     * prerarar o List resultante.
     *
     * @param controlarAcesso
     *            Indica se a aplicação deverá verificar se o usuário possui
     *            permissão para esta consulta ou não.
     * @return List Contendo vários objetos da classe <code>IndicacaoVO</code>
     *         resultantes da consulta.
     * @exception Exception
     *                Caso haja problemas de conexão ou restrição de acesso.
     */
    public List consultarPorClienteQueIndicou(Integer valorConsulta, boolean controlarAcesso, int nivelMontarDados) throws Exception {
        consultarCRM(getIdEntidade(), controlarAcesso);
        String sqlStr = "SELECT * FROM Indicacao WHERE clienteQueIndicou = " + valorConsulta + " ORDER BY clienteQueIndicou";
        try (Statement stm = con.createStatement()) {
            try (ResultSet tabelaResultado = stm.executeQuery(sqlStr)) {
                return (montarDadosConsulta(tabelaResultado, nivelMontarDados, con));
            }
        }
    }

    public List consultarPorColaboradorQueIndicou(Integer valorConsulta, boolean controlarAcesso, int nivelMontarDados) throws Exception {
        consultarCRM(getIdEntidade(), controlarAcesso);
        String sqlStr = "SELECT * FROM Indicacao WHERE colaboradorQueIndicou = " + valorConsulta + " ORDER BY colaboradorQueIndicou";
        try (Statement stm = con.createStatement()) {
            try (ResultSet tabelaResultado = stm.executeQuery(sqlStr)) {
                return (montarDadosConsulta(tabelaResultado, nivelMontarDados, con));
            }
        }
    }

    /**
     * Responsável por realizar uma consulta de <code>Indicacao</code> através
     * do valor do atributo <code>situacao</code> da classe
     * <code>Colaborador</code> Faz uso da operação
     * <code>montarDadosConsulta</code> que realiza o trabalho de prerarar o
     * List resultante.
     *
     * @return List Contendo vários objetos da classe <code>IndicacaoVO</code>
     *         resultantes da consulta.
     * @exception Exception
     *                Caso haja problemas de conexão ou restrição de acesso.
     */
    public List consultarPorNomeResponsavelCadastro(String valorConsulta, int nivelMontarDados) throws Exception {
        consultarCRM(getIdEntidade(), true);
        String sqlStr = "SELECT Indicacao.* FROM Indicacao inner join usuario on usuario.codigo = Indicacao.responsavelcadastro and  upper( usuario.nome ) like('" + valorConsulta.toUpperCase() + "%') ORDER BY usuario.nome";
        try (Statement stm = con.createStatement()) {
            try (ResultSet tabelaResultado = stm.executeQuery(sqlStr)) {
                return montarDadosConsulta(tabelaResultado, nivelMontarDados, con);
            }
        }
    }

    public List consultarPorNomeColaboradorResponsavel(String valorConsulta, int nivelMontarDados) throws Exception {
        consultarCRM(getIdEntidade(), true);
        String sqlStr = "SELECT Indicacao.* FROM Indicacao inner join usuario on usuario.codigo = Indicacao.colaboradorResponsavel and  upper( usuario.nome ) like('" + valorConsulta.toUpperCase() + "%') ORDER BY usuario.nome";
        try (Statement stm = con.createStatement()) {
            try (ResultSet tabelaResultado = stm.executeQuery(sqlStr)) {
                return montarDadosConsulta(tabelaResultado, nivelMontarDados, con);
            }
        }
    }

    public List consultarPorNomeColaboradorQueIndicou(String valorConsulta, int nivelMontarDados) throws Exception {
        consultarCRM(getIdEntidade(), true);
        String sqlStr = "SELECT Indicacao.* FROM Indicacao, Colaborador , Pessoa WHERE Indicacao.colaboradorQueIndicou = Colaborador.codigo and " + "Colaborador.pessoa = Pessoa.codigo and upper( Pessoa.nome ) like('" + valorConsulta.toUpperCase() + "%') ORDER BY Pessoa.nome";
        try (Statement stm = con.createStatement()) {
            try (ResultSet tabelaResultado = stm.executeQuery(sqlStr)) {
                return montarDadosConsulta(tabelaResultado, nivelMontarDados, con);
            }
        }
    }

    public List consultarPorNomeClienteQueIndicou(String valorConsulta, int nivelMontarDados) throws Exception {
        consultarCRM(getIdEntidade(), true);
        String sqlStr = "SELECT Indicacao.* FROM Indicacao, Cliente , Pessoa WHERE Indicacao.clienteQueIndicou = Cliente.codigo and " + "Cliente.pessoa = Pessoa.codigo and upper( Pessoa.nome ) like('" + valorConsulta.toUpperCase() + "%') ORDER BY Pessoa.nome";
        try (Statement stm = con.createStatement()) {
            try (ResultSet tabelaResultado = stm.executeQuery(sqlStr)) {
                return montarDadosConsulta(tabelaResultado, nivelMontarDados, con);
            }
        }
    }

    public List consultarPorNomeIndicador(String valorConsulta, int nivelMontarDados) throws Exception {
        consultarCRM(getIdEntidade(), true);
        String sqlStr = "SELECT distinct(indicacao.codigo) , Indicacao.* FROM Indicacao left join Cliente on Indicacao.clienteQueIndicou = Cliente.codigo " + "left join Colaborador on Indicacao.colaboradorQueIndicou = Colaborador.codigo " + "inner join Pessoa on (Cliente.pessoa = Pessoa.codigo or Colaborador.pessoa = Pessoa.codigo ) and upper( Pessoa.nome ) like('" + valorConsulta.toUpperCase() + "%')";
        try (Statement stm = con.createStatement()) {
            try (ResultSet tabelaResultado = stm.executeQuery(sqlStr)) {
                return montarDadosConsulta(tabelaResultado, nivelMontarDados, con);
            }
        }
    }

    /**
     * Responsável por realizar uma consulta de <code>Indicacao</code> através
     * do valor do atributo <code>Integer codigo</code>. Retorna os objetos com
     * valores iguais ou superiores ao parâmetro fornecido. Faz uso da operação
     * <code>montarDadosConsulta</code> que realiza o trabalho de prerarar o
     * List resultante.
     *
     * @param controlarAcesso
     *            Indica se a aplicação deverá verificar se o usuário possui
     *            permissão para esta consulta ou não.
     * @return List Contendo vários objetos da classe <code>IndicacaoVO</code>
     *         resultantes da consulta.
     * @exception Exception
     *                Caso haja problemas de conexão ou restrição de acesso.
     */
    public List consultarPorCodigo(Integer valorConsulta, boolean controlarAcesso, int nivelMontarDados) throws Exception {
        consultarCRM(getIdEntidade(), controlarAcesso);
        String sqlStr = "SELECT * FROM Indicacao WHERE codigo = " + valorConsulta + " ORDER BY codigo";
        try (Statement stm = con.createStatement()) {
            try (ResultSet tabelaResultado = stm.executeQuery(sqlStr)) {
                return (montarDadosConsulta(tabelaResultado, nivelMontarDados, con));
            }
        }
    }

    public Integer consultarIndicacaoPorColaboradorResponsavel(Integer valorConsulta, Date dia) throws Exception {
        consultarCRM(getIdEntidade(), true);
        String sqlStr = "SELECT COUNT(Indicacao.codigo) FROM Indicacao, Usuario where usuario.codigo = Indicacao.colaboradorResponsavel and Indicacao.colaboradorResponsavel = " + valorConsulta.intValue() + " and  CAST( Indicacao.dia AS DATE) = CAST( '" + Uteis.getDataJDBC(dia) + "' AS DATE)";
        try (Statement stm = con.createStatement()) {
            try (ResultSet tabelaResultado = stm.executeQuery(sqlStr)) {
                tabelaResultado.next();
                return (tabelaResultado.getInt(1));
            }
        }
    }

    /**
     * Responsável por montar os dados de vários objetos, resultantes de uma
     * consulta ao banco de dados (<code>ResultSet</code>). Faz uso da operação
     * <code>montarDados</code> que realiza o trabalho para um objeto por vez.
     *
     * @return List Contendo vários objetos da classe <code>IndicacaoVO</code>
     *         resultantes da consulta.
     */
    public static List montarDadosConsulta(ResultSet tabelaResultado, int nivelMontarDados, Connection con) throws Exception {
        List<IndicacaoVO> vetResultado = new ArrayList<IndicacaoVO>();
        while (tabelaResultado.next()) {
            IndicacaoVO obj = montarDados(tabelaResultado, nivelMontarDados, con);
            vetResultado.add(obj);
        }
        tabelaResultado = null;
        return vetResultado;
    }

    private static IndicacaoVO montarDadosBasico(ResultSet dadosSQL) throws Exception {
        IndicacaoVO obj = new IndicacaoVO();
        obj.setCodigo(new Integer(dadosSQL.getInt("codigo")));
        obj.setDia(dadosSQL.getTimestamp("dia"));
        obj.getColaboradorResponsavel().setCodigo(new Integer(dadosSQL.getInt("colaboradorResponsavel")));
        obj.getEvento().setCodigo(new Integer(dadosSQL.getInt("evento")));
        obj.getResponsavelCadastro().setCodigo(new Integer(dadosSQL.getInt("responsavelCadastro")));
        obj.getClienteQueIndicou().setCodigo(new Integer(dadosSQL.getInt("clienteQueIndicou")));
        obj.getColaboradorQueIndicou().setCodigo(new Integer(dadosSQL.getInt("colaboradorQueIndicou")));
        obj.setObservacao(dadosSQL.getString("observacao"));
        obj.setOrigemSistemaEnum(OrigemSistemaEnum.getOrigemSistema(dadosSQL.getInt("origemSistema")));
        return obj;
    }


    /**
     * Responsável por montar os dados resultantes de uma consulta ao banco de
     * dados (<code>ResultSet</code>) em um objeto da classe
     * <code>IndicacaoVO</code>.
     *
     * @return O objeto da classe <code>IndicacaoVO</code> com os dados
     *         devidamente montados.
     */
    public static IndicacaoVO montarDados(ResultSet dadosSQL, int nivelMontarDados, Connection con) throws Exception {
        IndicacaoVO obj = montarDadosBasico(dadosSQL);
        if (nivelMontarDados == Uteis.NIVELMONTARDADOS_HISTORICOINDICADO) {
            if (obj.getEvento().getCodigo() != 0) {
                montarDadosEvento(obj, nivelMontarDados, con);
            }

            if (obj.getColaboradorResponsavel().getCodigo() != 0) {
                montarDadosColaboradorResponsavel(obj, nivelMontarDados, con);
            }
            obj.setNovoObj(false);
            return obj;
        }
        if (nivelMontarDados == Uteis.NIVELMONTARDADOS_TELACONSULTA) {
            if (obj.getClienteQueIndicou().getCodigo() != 0) {
                montarDadosClienteQueIndicou(obj, Uteis.NIVELMONTARDADOS_MINIMOS, con);
            }
            if (obj.getEvento().getCodigo() != 0) {
                montarDadosEvento(obj, nivelMontarDados, con);
            }

            if (obj.getColaboradorQueIndicou().getCodigo() != 0) {
                montarDadosColaboradorQueIndicou(obj, Uteis.NIVELMONTARDADOS_MINIMOS, con);
            }
            obj.setNovoObj(false);
            montarDadosResponsavelCadastro(obj, nivelMontarDados, con);
            montarDadosColaboradorResponsavel(obj, nivelMontarDados, con);
            return obj;
        }
        if (nivelMontarDados == Uteis.NIVELMONTARDADOS_HISTORICOINDICADO) {
            montarDadosColaboradorResponsavel(obj, nivelMontarDados, con);
            return obj;
        }
        if (obj.getEvento().getCodigo() != 0) {
            montarDadosEvento(obj, nivelMontarDados, con);
        }
        if (obj.getClienteQueIndicou().getCodigo() != 0) {
            montarDadosClienteQueIndicou(obj, Uteis.NIVELMONTARDADOS_MINIMOS, con);
        }

        if (obj.getColaboradorQueIndicou().getCodigo() != 0) {
            montarDadosColaboradorQueIndicou(obj, nivelMontarDados, con);
        }
        obj.setIndicadoVOs(new Indicado(con).consultarListaIndicados(obj.getCodigo(), nivelMontarDados));
        obj.setNovoObj(false);
        if (nivelMontarDados == Uteis.NIVELMONTARDADOS_DADOSBASICOS) {
            return obj;
        }
        montarDadosEvento(obj, nivelMontarDados, con);
        montarDadosResponsavelCadastro(obj, nivelMontarDados, con);
        montarDadosColaboradorResponsavel(obj, nivelMontarDados, con);
        return obj;
    }

    /**
     * Operação responsável por montar os dados de um objeto da classe
     * <code>ColaboradorVO</code> relacionado ao objeto <code>IndicacaoVO</code>
     * . Faz uso da chave primária da classe <code>ColaboradorVO</code> para
     * realizar a consulta.
     *
     * @param obj
     *            Objeto no qual será montado os dados consultados.
     */
    public static void montarDadosColaboradorResponsavel(IndicacaoVO obj, int nivelMontarDados, Connection con) throws Exception {
        if (obj.getColaboradorResponsavel().getCodigo() == 0) {
            obj.setColaboradorResponsavel(new UsuarioVO());
            return;
        }
        obj.setColaboradorResponsavel(new Usuario(con).consultarPorChavePrimaria(obj.getColaboradorResponsavel().getCodigo(), nivelMontarDados));
    }

    public static void montarDadosClienteQueIndicou(IndicacaoVO obj, int nivelMontarDados, Connection con) throws Exception {
        if (obj.getClienteQueIndicou().getCodigo() == 0) {
            obj.setClienteQueIndicou(new ClienteVO());
            return;
        }
        obj.setClienteQueIndicou(new Cliente(con).consultarPorChavePrimaria(obj.getClienteQueIndicou().getCodigo(), nivelMontarDados));

    }

    public static void montarDadosColaboradorQueIndicou(IndicacaoVO obj, int nivelMontarDados, Connection con) throws Exception {
        if (obj.getColaboradorQueIndicou().getCodigo() == 0) {
            obj.setColaboradorQueIndicou(new ColaboradorVO());
            return;
        }
        obj.setColaboradorQueIndicou(new Colaborador(con).consultarPorChavePrimaria(obj.getColaboradorQueIndicou().getCodigo(), nivelMontarDados));
    }

    public static void montarDadosEvento(IndicacaoVO obj, int nivelMontarDados, Connection con) throws Exception {
        if (obj.getEvento().getCodigo() == 0) {
            obj.setEvento(new EventoVO());
            return;
        }
        obj.setEvento(new Evento(con).consultarPorChavePrimaria(obj.getEvento().getCodigo(), nivelMontarDados));
    }

    /**
     * Operação responsável por montar os dados de um objeto da classe
     * <code>ColaboradorVO</code> relacionado ao objeto <code>IndicacaoVO</code>
     * . Faz uso da chave primária da classe <code>ColaboradorVO</code> para
     * realizar a consulta.
     *
     * @param obj
     *            Objeto no qual será montado os dados consultados.
     */
    public static void montarDadosResponsavelCadastro(IndicacaoVO obj, int nivelMontarDados, Connection con) throws Exception {
        if (obj.getResponsavelCadastro().getCodigo() == 0) {
            obj.setResponsavelCadastro(new UsuarioVO());
            return;
        }
        obj.setResponsavelCadastro(new Usuario(con).consultarPorChavePrimaria(obj.getResponsavelCadastro().getCodigo(), nivelMontarDados));
    }

    /**
     * Operação responsável por localizar um objeto da classe
     * <code>IndicacaoVO</code> através de sua chave primária.
     *
     * @exception Exception
     *                Caso haja problemas de conexão ou localização do objeto
     *                procurado.
     */
    public IndicacaoVO consultarPorChavePrimaria(Integer codigoPrm, int nivelMontarDados) throws Exception {
        consultarCRM(getIdEntidade(), false);
        String sql = "SELECT * FROM Indicacao WHERE codigo = ?";
        try (PreparedStatement sqlConsultar = con.prepareStatement(sql)) {
            sqlConsultar.setInt(1, codigoPrm);
            try (ResultSet tabelaResultado = sqlConsultar.executeQuery()) {
                if (!tabelaResultado.next()) {
                    throw new ConsistirException("Dados Não Encontrados ( Indicacao ).");
                }
                return (montarDados(tabelaResultado, nivelMontarDados, con));
            }
        }
    }

    /**
     * Método que invoca os metodos exclui os dependentes de Indicação e indicacao
     * @param obj
     * @throws Exception
     * <AUTHOR>
     */
    public void excutarExclusaoDependentesIndicacao(IndicacaoVO obj) throws Exception {
        try {
            con.setAutoCommit(false);
            executarExclusaoDependentes(obj);
            excluir(obj);
            con.commit();
        } catch (Exception e) {
            obj.setNovoObj(true);
            con.rollback();
            con.setAutoCommit(true);
            throw e;
        } finally {
            con.setAutoCommit(true);
        }

    }

    /**
     * Método responsavel por excluir FecharMetaVO e FecharMEtaDetalhadaVO quando é excluido uma Indicação
     * @param obj
     * @throws Exception
     * <AUTHOR>
     */
    public void executarExclusaoDependentes(IndicacaoVO obj) throws Exception {
        Indicado indicadoDao = new Indicado(con);
        FecharMeta fecharMetaDao = new FecharMeta(con);
        FecharMetaDetalhado fecharMetaDetalhadoDao = new FecharMetaDetalhado(con);
        HistoricoContato historicoContatoDao = new HistoricoContato(con);
        Agenda agendaDao = new Agenda(con);
        try {
            for (IndicadoVO indicado : obj.getIndicadoVOs()) {
                indicadoDao.validarSePodeExcluirIndicado(indicado.getCodigo(), obj.getColaboradorResponsavel().getCodigo());
                FecharMetaDetalhadoVO fecharMetaDetalhadoVO = fecharMetaDetalhadoDao.consultarPorCodigoIndicado(indicado.getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS);
                if (fecharMetaDetalhadoVO != null) {
                    FecharMetaVO fecharMetaVO = fecharMetaDao.
                            consultarPorChavePrimaria(fecharMetaDetalhadoVO.getFecharMeta().getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS);
                    fecharMetaVO.setMetaAtingida(fecharMetaVO.getMetaAtingida().intValue() - 1.0);
                    fecharMetaDao.alterar(fecharMetaVO);
                }

                fecharMetaDetalhadoDao.excluirPorCodigoIndicadoCodigoColaboradorResponsavelDataIdentificador(indicado.getCodigo(), obj.getCodigo(), obj.getColaboradorResponsavel().getCodigo().intValue(), "IN");
                Boolean existeHistoricoContato = historicoContatoDao.consultarHistoricoPorCodigoIndicado(indicado.getCodigo(), false, Uteis.NIVELMONTARDADOS_TODOS);
                if (existeHistoricoContato) {
                    historicoContatoDao.excluirHistoricoContatoPorCodigoIndicado(indicado.getCodigo());
                    agendaDao.excluirHistoricoContatoPorCodigoIndicado(indicado.getCodigo());
                }
            }
        }catch (Exception e) {
            throw e;
        } finally {
            fecharMetaDetalhadoDao = null;
            fecharMetaDao = null;
            indicadoDao = null;
            historicoContatoDao = null;
            agendaDao = null;
        }
    }

    private PreparedStatement getPS(Date dtInicio, Date dtFim, Integer empresa, String clausulaLike) throws Exception {
        ConfiguracaoSistemaCRM configuracaoSistemaCRMDao = new ConfiguracaoSistemaCRM(con);

        StringBuilder sql = new StringBuilder();
        sql.append("SELECT\n");
        sql.append("i.codigo,\n");
        sql.append("ind.nomeindicado,\n");
        sql.append("p.nome AS clientequeindicou,\n");
        sql.append("pc.nome AS colaboradorqueindicou,\n");
        sql.append("i.dia,\n");
        sql.append("e.descricao AS evento,\n");
        sql.append("us.nome AS responsavelcadastro,\n");
        sql.append("ind.telefone,\n");
        sql.append("ind.telefoneindicado,\n");
        sql.append("ind.email,\n");
        sql.append("(SELECT con.datalancamento \n");
        sql.append("FROM contrato con \n");
        sql.append("WHERE con.pessoa = ci.pessoa \n");
        sql.append("AND con.datalancamento::date >= i.dia::date \n");
        sql.append("AND con.datalancamento::date < i.dia::date + ");
        sql.append(configuracaoSistemaCRMDao.obterNrDiasContarResultado()).append("\n");
        sql.append("AND con.situacaocontrato IN ('MA', 'RE') \n");
        sql.append("AND NOT EXISTS (SELECT codigo FROM contratooperacao WHERE tipooperacao = 'CA' AND contrato = con.codigo) \n");
        sql.append("ORDER BY codigo ASC LIMIT 1) AS datalancamento\n");
        sql.append("FROM indicacao i\n");
        sql.append("INNER JOIN usuario us ON us.codigo = i.responsavelcadastro\n");
        sql.append("INNER JOIN indicado ind ON ind.indicacao = i.codigo\n");
        sql.append("LEFT JOIN evento e ON e.codigo = i.evento\n");
        sql.append("LEFT JOIN cliente c ON c.codigo = i.clientequeindicou\n");
        sql.append("LEFT JOIN pessoa p ON c.pessoa = p.codigo\n");
        sql.append("LEFT JOIN colaborador col ON col.codigo = i.colaboradorqueindicou\n");
        sql.append("LEFT JOIN pessoa pc ON pc.codigo = col.pessoa\n");
        sql.append("LEFT JOIN cliente ci on ci.codigo = ind.cliente\n");
        sql.append("LEFT JOIN pessoa pi on pi.codigo = ci.pessoa\n");
        sql.append("WHERE 1=1\n");

        if (!UteisValidacao.emptyString(clausulaLike)) {
            sql.append("AND ");
            sql.append("(");
            sql.append("lower(i.codigo::VARCHAR) ilike '%").append(clausulaLike).append("%' OR\n");
            sql.append("lower(ind.nomeindicado::VARCHAR) ilike '%").append(clausulaLike).append("%' OR\n");
            sql.append("lower(US.nome::VARCHAR) ilike '%").append(clausulaLike).append("%' OR\n");
            sql.append("lower(ind.telefone::VARCHAR) ilike '%").append(clausulaLike).append("%' OR\n");
            sql.append("lower(ind.telefoneindicado::VARCHAR) ilike '%").append(clausulaLike).append("%' OR\n");
            sql.append("lower(ind.email::VARCHAR) ilike '%").append(clausulaLike).append("%' OR\n");
            sql.append("lower(e.descricao::VARCHAR) ilike '%").append(clausulaLike).append("%' OR\n");
            sql.append("lower(pc.nome::VARCHAR) ilike '%").append(clausulaLike).append("%' OR\n");
            sql.append("lower(pc.nome::VARCHAR) ilike '%").append(clausulaLike).append("%' OR\n");
            sql.append("lower(p.nome::VARCHAR) ilike '%").append(clausulaLike).append("%'\n");
            sql.append(")\n");
        }


        if (dtInicio != null) {
            sql.append("AND i.dia >= '").append(Uteis.getDataJDBCTimestamp(dtInicio)).append("'\n");
        }

        if (dtFim != null) {
            sql.append("AND i.dia <= '").append(Uteis.getDataJDBC(dtFim)).append(" 23:59:59.999'\n");
        }

        if (empresa > 0) {
            sql.append("AND ind.empresa  = ").append(empresa).append("\n");
        }

        sql.append("ORDER BY 1 ASC\n");

        configuracaoSistemaCRMDao = null;

        return con.prepareStatement(sql.toString());
    }

    public String consultarJSON(Integer empresa,String sEcho,Integer offset, Integer limit, String clausulaLike, Integer colOrdenar, String dirOrdenar,Date dtInicio, Date dtFim) throws Exception {
        ConfiguracaoSistemaCRM configuracaoSistemaCRMDao = new ConfiguracaoSistemaCRM(con);
        if (!UteisValidacao.emptyString(clausulaLike)) {
            clausulaLike = clausulaLike.toLowerCase();
        }
        StringBuilder sql = new StringBuilder();
        sql.append("SELECT\n");
        sql.append("i.codigo,\n");
        sql.append("ind.nomeindicado,\n");
        sql.append("p.nome AS clientequeindicou,\n");
        sql.append("pc.nome AS colaboradorqueindicou,\n");
        sql.append("i.dia,\n");
        sql.append("(SELECT con.datalancamento \n");
        sql.append("FROM contrato con \n");
        sql.append("WHERE con.pessoa = ci.pessoa \n");
        sql.append("AND con.datalancamento::date >= i.dia::date \n");
        sql.append("AND con.datalancamento::date < i.dia::date + ");
        sql.append(configuracaoSistemaCRMDao.obterNrDiasContarResultado()).append("\n");
        sql.append("AND con.situacaocontrato IN ('MA', 'RE') \n");
        sql.append("AND NOT EXISTS (SELECT codigo FROM contratooperacao WHERE tipooperacao = 'CA' AND contrato = con.codigo) \n");
        sql.append("ORDER BY codigo ASC LIMIT 1) AS datalancamento,\n");
        sql.append("e.descricao AS evento,\n");
        sql.append("us.nome AS responsavelcadastro,\n");
        sql.append("ind.telefone,\n");
        sql.append("ind.telefoneindicado,\n");
        sql.append("ind.email\n");
        sql.append("FROM indicacao i\n");
        sql.append("INNER JOIN usuario us ON us.codigo = i.responsavelcadastro\n");
        sql.append("INNER JOIN indicado ind ON ind.indicacao = i.codigo\n");
        sql.append("LEFT JOIN evento e ON e.codigo = i.evento\n");
        sql.append("LEFT JOIN cliente c ON c.codigo = i.clientequeindicou\n");
        sql.append("LEFT JOIN pessoa p ON c.pessoa = p.codigo\n");
        sql.append("LEFT JOIN colaborador col ON col.codigo = i.colaboradorqueindicou\n");
        sql.append("LEFT JOIN pessoa pc ON pc.codigo = col.pessoa\n");
        sql.append("LEFT JOIN cliente ci on ci.codigo = ind.cliente\n");
        sql.append("LEFT JOIN pessoa pi on pi.codigo = ci.pessoa\n");
        sql.append("WHERE 1=1\n");

        if (!UteisValidacao.emptyString(clausulaLike)) {
            sql.append("AND ");
            sql.append("(");
            sql.append("lower(i.codigo::VARCHAR) ilike '%").append(clausulaLike).append("%' OR\n");
            sql.append("lower(ind.nomeindicado::VARCHAR) ilike '%").append(clausulaLike).append("%' OR\n");
            sql.append("lower(US.nome::VARCHAR) ilike '%").append(clausulaLike).append("%' OR\n");
            sql.append("lower(ind.telefone::VARCHAR) ilike '%").append(clausulaLike).append("%' OR\n");
            sql.append("lower(ind.telefoneindicado::VARCHAR) ilike '%").append(clausulaLike).append("%' OR\n");
            sql.append("lower(ind.email::VARCHAR) ilike '%").append(clausulaLike).append("%' OR\n");
            sql.append("lower(e.descricao::VARCHAR) ilike '%").append(clausulaLike).append("%' OR\n");
            sql.append("lower(pc.nome::VARCHAR) ilike '%").append(clausulaLike).append("%' OR\n");
            sql.append("lower(pc.nome::VARCHAR) ilike '%").append(clausulaLike).append("%' OR\n");
            sql.append("lower(p.nome::VARCHAR) ilike '%").append(clausulaLike).append("%'\n");
            sql.append(")\n");
        }


        if (dtInicio != null) {
            sql.append("AND i.dia >= '").append(Uteis.getDataJDBCTimestamp(dtInicio)).append("'\n");
        }

        if (dtFim != null) {
            sql.append("AND i.dia <= '").append(Uteis.getDataJDBC(dtFim)).append(" 23:59:59.999'\n");
        }

        if (empresa > 0) {
            sql.append("AND ind.empresa  = ").append(empresa).append("\n");
        }

        sql.append("ORDER BY ").append(colOrdenar + 1).append(" ").append(dirOrdenar).append("\n");
        StringBuilder sqlCount = new StringBuilder();
        sqlCount.append("SELECT COUNT(foo.codigo) FROM (");
        sqlCount.append(sql);
        sqlCount.append(") as foo");

        if (limit > 0) {
            sql.append("LIMIT ").append(limit).append("\n");
        }
        sql.append("OFFSET ").append(offset).append("\n");

        configuracaoSistemaCRMDao = null;
        StringBuilder json;
        boolean dados;
        try (PreparedStatement sqlConsultar = con.prepareStatement(sql.toString())) {
            try (ResultSet rs = sqlConsultar.executeQuery()) {

                json = new StringBuilder();
                json.append("{");
                json.append("\"iTotalRecords\":\"").append(contar(sqlCount.toString(), getCon())).append("\",");
                json.append("\"iTotalDisplayRecords\":\"").append(contar(sqlCount.toString(), getCon())).append("\",");
                json.append("\"sEcho\":\"").append(sEcho).append("\",");
                json.append("\"aaData\":[");
                dados = false;
                while (rs.next()) {
                    dados = true;
                    json.append("[\"").append(rs.getInt("codigo")).append("\",");
                    json.append("\"").append(Uteis.normalizarStringJSON(rs.getString("nomeindicado"))).append("\",");
                    json.append("\"").append(Uteis.normalizarStringJSON(rs.getString("clientequeindicou"))).append("\",");
                    json.append("\"").append(Uteis.normalizarStringJSON(rs.getString("colaboradorqueindicou"))).append("\",");
                    json.append("\"").append(Uteis.getDataComHHMM(rs.getTimestamp("dia"))).append("\",");
                    json.append("\"").append(rs.getTimestamp("datalancamento") != null ? Uteis.getDataComHHMM(rs.getTimestamp("datalancamento")) : null).append("\",");
                    json.append("\"").append(Uteis.normalizarStringJSON(rs.getString("evento"))).append("\",");
                    json.append("\"").append(Uteis.normalizarStringJSON(rs.getString("responsavelcadastro"))).append("\"],");
                }
            }
        }
        if(dados) {
            json.deleteCharAt(json.toString().length() - 1);
        }
        json.append("]}");
        return json.toString();
    }

    public Map<String, Number> countResultadosIndicacao(Integer empresa, Date dtInicio, Date dtFim) throws Exception {
        ConfiguracaoSistemaCRM configuracaoSistemaCRMDao = new ConfiguracaoSistemaCRM(con);
        StringBuilder sql = new StringBuilder();
        sql.append("SELECT\n");
        sql.append("SUM(qtdindicacao) AS qtdindicacao,\n");
        sql.append("SUM(qtdconvertido) as qtdconvertido FROM(\n");
        sql.append("SELECT\n");
        sql.append("0 AS qtdIndicacao,\n");
        sql.append("COUNT(i.codigo) AS qtdConvertido\n");
        sql.append("FROM indicacao i\n");
        sql.append("INNER JOIN indicado ind ON ind.indicacao = i.codigo\n");
        sql.append("LEFT JOIN cliente ci ON ci.codigo = ind.cliente\n");
        sql.append("LEFT JOIN pessoa pi ON pi.codigo = ci.pessoa\n");
        sql.append("LEFT JOIN contrato con ON con.pessoa = pi.codigo\n");
        sql.append("WHERE 1 = 1\n");
        sql.append("AND con.datalancamento::date >= i.dia::date\n");
        sql.append("AND con.datalancamento::date < i.dia::date + ").append(configuracaoSistemaCRMDao.obterNrDiasContarResultado()).append("\n");
        sql.append("AND con.situacaocontrato IN ('MA', 'RE')\n");
        sql.append("AND NOT EXISTS (SELECT codigo FROM contratooperacao WHERE tipooperacao = 'CA' AND contrato = con.codigo)\n");
        if (dtInicio != null) {
            sql.append("AND i.dia >= '").append(Uteis.getDataJDBCTimestamp(dtInicio)).append("'\n");
        }

        if (dtFim != null) {
            sql.append("AND i.dia <= '").append(Uteis.getDataJDBC(dtFim)).append(" 23:59:59.999'\n");
        }

        if (empresa > 0) {
            sql.append("AND ind.empresa  = ").append(empresa).append("\n");
        }
        sql.append("UNION\n");
        sql.append("SELECT\n");
        sql.append("COUNT(i.codigo) AS qtdIndicacao,\n");
        sql.append("0 AS qtdConvertido\n");
        sql.append("FROM indicacao i\n");
        sql.append("INNER JOIN indicado ind ON ind.indicacao = i.codigo\n");
        sql.append("WHERE 1 = 1\n");
        if (dtInicio != null) {
            sql.append("AND i.dia >= '").append(Uteis.getDataJDBCTimestamp(dtInicio)).append("'\n");
        }

        if (dtFim != null) {
            sql.append("AND i.dia <= '").append(Uteis.getDataJDBC(dtFim)).append(" 23:59:59.999'\n");
        }

        if (empresa > 0) {
            sql.append("AND ind.empresa  = ").append(empresa).append("\n");
        }

        sql.append(") as foo");
        configuracaoSistemaCRMDao = null;
        try (PreparedStatement ps = con.prepareStatement(sql.toString())) {
            try (ResultSet rs = ps.executeQuery()) {
                Integer qtdIndicacao = 0;
                Integer qtdConvertido = 0;
                if (rs.next()) {
                    qtdIndicacao = rs.getInt("qtdIndicacao");
                    qtdConvertido = rs.getInt("qtdConvertido");
                }

                if (!UteisValidacao.emptyNumber(qtdIndicacao)) {
                    double porcentagem = (qtdConvertido > 0) ? ((double) qtdConvertido / qtdIndicacao) * 100 : 0;

                    Map<String, Number> resultado = new HashMap<>();
                    resultado.put("totalRegistros", qtdIndicacao);
                    resultado.put("convertidos", qtdConvertido);
                    resultado.put("porcentagem", Uteis.arredondar(porcentagem, 1, 1));
                    return resultado;
                } else {
                    Map<String, Number> resultado = new HashMap<>();
                    resultado.put("totalRegistros", 0);
                    resultado.put("convertidos", 0);
                    resultado.put("porcentagem", 0);
                    return resultado;
                }

            }
        }
    }

    public IndicacaoVO consultarPorCodigoIndicado(Integer codigoIndicado, int nivelMontarDados) throws Exception {
        consultarCRM(getIdEntidade(), false);

        StringBuilder sql = new StringBuilder();
        sql.append("select * \n");
        sql.append("from indicacao ind \n");
        sql.append("inner join indicado i on i.indicacao = ind.codigo \n");
        sql.append("where i.codigo = "+ codigoIndicado +"");

        try (PreparedStatement pst = con.prepareStatement(sql.toString())) {
            try (ResultSet rs = pst.executeQuery()) {
                if (!rs.next()) {
                    throw new ConsistirException("Dados Não Encontrados ( Indicacao ).");
                }
                return montarDados(rs, nivelMontarDados, con);
            }
        }
    }

    public IndicacaoVO consultarPorCodigoClienteIndicado(Integer codigoClienteIndicado, int nivelMontarDados) throws Exception {
        StringBuilder sql = new StringBuilder();
        sql.append("select * from indicacao ind \n");
        sql.append("inner join indicado i on i.indicacao = ind.codigo \n");
        sql.append("where i.cliente = ").append(codigoClienteIndicado).append(" \n");
        sql.append("order by ind.codigo ");

        try (PreparedStatement pst = con.prepareStatement(sql.toString())) {
            try (ResultSet rs = pst.executeQuery()) {
                if (rs.next()) {
                    return montarDados(rs, nivelMontarDados, con);
                }
            }
        }
        return null;
    }

    public void incluirIndicacaoMeta(IndicacaoVO obj, FecharMetaVO fecharMeta) throws Exception {

        Logger.getLogger(getClass().getSimpleName()).log(Level.INFO,"#### [incluirIndicacaoMeta] INICIANDO - IndicacaoVO={0}, FecharMetaVO={1}",
                new Object[]{ obj, (fecharMeta != null ? fecharMeta.getCodigo() : null) }
        );
        try {
            con.setAutoCommit(false);
            IndicacaoVO.validarDados(obj);
            incluirCRM(getIdEntidade());
            String sql = "INSERT INTO Indicacao( responsavelCadastro, clienteQueIndicou, colaboradorQueIndicou, colaboradorResponsavel, observacao, dia, evento, origemSistema ) VALUES ( ?, ?, ?, ?, ?, ?, ?, ? )";
            try (PreparedStatement sqlInserir = con.prepareStatement(sql)) {
                if (obj.getResponsavelCadastro().getCodigo() != 0) {
                    sqlInserir.setInt(1, obj.getResponsavelCadastro().getCodigo());
                } else {
                    sqlInserir.setNull(1, 0);
                }
                if (obj.getClienteQueIndicou().getCodigo() != 0) {
                    sqlInserir.setInt(2, obj.getClienteQueIndicou().getCodigo());
                } else {
                    sqlInserir.setNull(2, 0);
                }
                if (obj.getColaboradorQueIndicou().getCodigo() != 0) {
                    sqlInserir.setInt(3, obj.getColaboradorQueIndicou().getCodigo());
                } else {
                    sqlInserir.setNull(3, 0);
                }
                if (obj.getColaboradorResponsavel().getCodigo() != 0) {
                    sqlInserir.setInt(4, obj.getColaboradorResponsavel().getCodigo());
                } else {
                    sqlInserir.setNull(4, 0);
                }
                sqlInserir.setString(5, obj.getObservacao());
                sqlInserir.setTimestamp(6, Uteis.getDataJDBCTimestamp(obj.getDia()));
                if (obj.getEvento().getCodigo() != 0) {
                    sqlInserir.setInt(7, obj.getEvento().getCodigo());
                } else {
                    sqlInserir.setNull(7, 0);
                }
                sqlInserir.setInt(8, obj.getOrigemSistemaEnum().getCodigo());
                sqlInserir.execute();
            }
            obj.setCodigo(obterValorChavePrimariaCodigo());
            Logger.getLogger(getClass().getSimpleName()).log(Level.INFO, "#### [incluirIndicacaoMeta] Indicação inserida com sucesso. Novo codigo={0}", obj.getCodigo()
            );
            Indicado indicadoDao = new Indicado(con);
            indicadoDao.incluirIndicadosMeta(obj, obj.getIndicadoVOs(), fecharMeta);
            indicadoDao = null;

            obj.setNovoObj(false);
            con.commit();
            Logger.getLogger(getClass().getSimpleName()).log(
                    Level.INFO,
                    "#### [incluirIndicacaoMeta] Concluído com sucesso. IndicaçãoVO={0}",
                    obj.getCodigo()
            );
        } catch (Exception e) {
            obj.setNovoObj(true);
            Logger.getLogger(getClass().getSimpleName()).log(Level.SEVERE, "#### ERRO AO INCLUIR INDICACAO META: {0}", e.getMessage());
            con.rollback();
            con.setAutoCommit(true);
            throw e;
        } finally {
            con.setAutoCommit(true);
        }
    }

    public void incluirHistoricoContatoIndicado(IndicacaoVO indicacaoVO,  IndicadoVO indicadoVO, HistoricoContatoVO hist, String agendaOuObjecao) throws Exception {
        hist.setColaboradorResponsavel(indicacaoVO.getColaboradorResponsavel());
        hist.setResponsavelCadastro(indicacaoVO.getResponsavelCadastro());
        hist.getAgendaVO().setEmpresa(indicacaoVO.getEmpresa().getCodigo());
        hist.setDia(Calendario.hoje());
        hist.setFase(FasesCRMEnum.INDICACOES.getSigla());
        hist.setTipoContato(hist.getTipoContato());
        hist.setIndicadoVO(indicadoVO);
        hist.setObservacao(indicacaoVO.getObservacao());
        HistoricoContato historicoContatoDao = new HistoricoContato(con);
        hist = historicoContatoDao.executarInsercaoHistoricoIndicado(agendaOuObjecao, hist);
        historicoContatoDao = null;
        if (agendaOuObjecao.equals("AG")) {
            Agenda agendaDao = new Agenda(con);
            agendaDao.executarRegraNegocioParaFecharMetaInclusao(hist.getDiaAbertura(), hist.getAgendaVO(), hist.getColaboradorResponsavel().getCodigo(), 0, hist.getIndicadoVO().getCodigo(), 0, hist);
            agendaDao = null;
        }
    }

    public List consultarParaImpressao(String filtro, String ordem, String campoOrdenacao, Date dtInicio, Date dtFim, Integer empresa, String clausulaLike) throws Exception {
        List lista;
        try (ResultSet rs = getPS(dtInicio, dtFim, empresa, clausulaLike).executeQuery()) {
            lista = new ArrayList();

            while (rs.next()) {
                IndicadoVO ind = new IndicadoVO();
                String geral = rs.getString("codigo") + rs.getString("nomeindicado") + rs.getString("clientequeindicou") + rs.getString("colaboradorqueindicou")
                        + Uteis.getDataComHHMM(rs.getTimestamp("dia")) + rs.getString("evento") + rs.getString("responsavelcadastro");
                if (geral.toLowerCase().contains(filtro.toLowerCase())) {
                    ind.setCodigo(rs.getInt("codigo"));
                    ind.setNomeIndicado(rs.getString("nomeindicado"));
                    ind.getIndicacaoVO().getClienteQueIndicou().getPessoa().setNome(rs.getString("clientequeindicou"));
                    ind.getIndicacaoVO().getColaboradorQueIndicou().getPessoa().setNome(rs.getString("colaboradorqueindicou"));
                    ind.setDataLancamento(rs.getTimestamp("dia"));
                    ind.getIndicacaoVO().getEvento().setDescricao(rs.getString("evento"));
                    ind.getIndicacaoVO().getResponsavelCadastro().setNome(rs.getString("responsavelcadastro"));
                    ind.setTelefone(rs.getString("telefone"));
                    ind.setTelefoneIndicado(rs.getString("telefoneindicado"));
                    ind.setEmail(rs.getString("email"));
                    lista.add(ind);
                }
            }
        }
        if (campoOrdenacao.equals("Código")) {
            Ordenacao.ordenarLista(lista, "codigo");
        } else if (campoOrdenacao.equals("Nome Indicado")) {
            Ordenacao.ordenarLista(lista, "nomeIndicado");
        } else if (campoOrdenacao.equals("Cliente que Indicou")) {
            Ordenacao.ordenarLista(lista, "nomeClienteIndicou");
        } else if (campoOrdenacao.equals("Colaborador que Indicou")) {
            Ordenacao.ordenarLista(lista, "nomeColaboradorIndicou");
        } else if (campoOrdenacao.equals("Data de Cadastro")) {
            Ordenacao.ordenarLista(lista, "dataLancamento");
        } else if (campoOrdenacao.equals("Evento")) {
            Ordenacao.ordenarLista(lista, "eventoIndicacao");
        } else if (campoOrdenacao.equals("Responsável Cadastro")) {
            Ordenacao.ordenarLista(lista, "responsavelCadastro");
        }
        if (ordem.contains("desc")) {
            Collections.reverse(lista);
        }
        return lista;
    }
}

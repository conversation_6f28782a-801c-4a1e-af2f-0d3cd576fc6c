package negocio.facade.jdbc.crm;

import br.com.pactosolucoes.bi.dto.FiltroDTO;
import br.com.pactosolucoes.comuns.json.JSONMapper;
import br.com.pactosolucoes.oamd.controle.basico.DAO;
import controle.crm.MsgBuildDTO;
import controle.crm.TemplateWhatsAppDTO;
import negocio.facade.jdbc.oamd.RedeEmpresa;
import negocio.interfaces.crm.*;
import negocio.comuns.crm.ModeloMensagemVO;
import negocio.facade.jdbc.arquitetura.*;

import java.net.URLDecoder;
import java.net.URLEncoder;
import java.sql.*;
import java.util.*;
import java.util.Date;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

import negocio.comuns.crm.MeioEnvio;
import negocio.comuns.utilitarias.*;
import negocio.modulos.integracao.usuariomovel.UsuarioMovelVO;
import negocio.oamd.RedeEmpresaVO;
import org.apache.commons.io.Charsets;
import org.apache.poi.ss.usermodel.DateUtil;
import org.joda.time.DateTime;
import org.json.JSONArray;
import org.json.JSONObject;
import servicos.http.MetodoHttpEnum;
import servicos.http.RequestHttpService;
import servicos.http.RespostaHttpDTO;
import servicos.integracao.whatsApp.WhatsAppController;
import servicos.integracao.whatsApp.dadosCliente;
import servicos.integracao.whatsApp.template;
import servicos.operacoes.midias.MidiaService;
import servicos.operacoes.midias.commons.MidiaEntidadeEnum;
import servicos.propriedades.PropsService;
import servicos.util.ExecuteRequestHttpService;

/**
 * Classe de persistência que encapsula todas as operações de manipulação dos dados da classe <code>ModeloMensagemVO</code>.
 * Responsável por implementar operações como incluir, alterar, excluir e consultar pertinentes a classe <code>ModeloMensagemVO</code>.
 * Encapsula toda a interação com o banco de dados.
 * @see ModeloMensagemVO
 * @see SuperEntidade
 */
public class ModeloMensagem extends SuperEntidade implements ModeloMensagemInterfaceFacade {

    private static Map<String,List<MsgBuildDTO>> templatesPacto = new HashMap<>();
    private static Map<String, String> mapaChaveMarketing = new HashMap<>();

    public ModeloMensagem() throws Exception {
        super();
    }

    public ModeloMensagem(Connection con) throws Exception {
		super(con);
	}

	/**
     * Operação responsável por retornar um novo objeto da classe <code>ModeloMensagemVO</code>.
     */
    public ModeloMensagemVO novo() throws Exception {
        incluirCRM(getIdEntidade());
        ModeloMensagemVO obj = new ModeloMensagemVO();
        return obj;
    }

    /**
     * Operação responsável por incluir no banco de dados um objeto da classe <code>ModeloMensagemVO</code>.
     * Primeiramente valida os dados (<code>validarDados</code>) do objeto. Verifica a conexão com o banco de dados e a permissão do usuário
     * para realizar esta operacão na entidade.
     * Isto, através da operação <code>incluir</code> da superclasse.
     * @param obj  Objeto da classe <code>ModeloMensagemVO</code> que será gravado no banco de dados.
     * @exception Exception Caso haja problemas de conexão, restrição de acesso ou validação de dados.
     */
    public void incluir(ModeloMensagemVO obj) throws Exception {
        ModeloMensagemVO.validarDados(obj);
        incluirCRM(getIdEntidade());
        obj.realizarUpperCaseDados();
        String sql = "INSERT INTO ModeloMensagem( titulo, mensagem,  meioDeEnvio, tipoMensagem, imagemModelo, " +
                "nomeimagem, ativo, urlRedirecionamento, builder, configs) VALUES ( ?, ?,?, ?, ?, ?, ?, ?, ?, ?)";
        PreparedStatement sqlInserir = con.prepareStatement(sql);
        int i = 1;
        sqlInserir.setString(i++, obj.getTitulo());
        sqlInserir.setString(i++, obj.getMensagem());
        sqlInserir.setInt(i++, obj.getMeioDeEnvioEnum().getCodigo());
        sqlInserir.setString(i++, obj.getTipoMensagem());
        if (obj.getImagemModelo() != null && !PropsService.isTrue(PropsService.fotosParaNuvem)) {
            sqlInserir.setBytes(i++, obj.getImagemModelo());
        } else {
            sqlInserir.setNull(i++, 0);
        }
        if (obj.getImagemModelo() != null) {
            sqlInserir.setString(i++, obj.getNomeImagem());
        } else {
            sqlInserir.setNull(i++, 0);
        }
        sqlInserir.setBoolean(i++, obj.isAtivo());
        sqlInserir.setString(i++, obj.getUrlRedirecionamento());
        sqlInserir.setBoolean(i++, obj.getBuilder());
        sqlInserir.setString(i++, obj.getConfigs());
        sqlInserir.execute();
        obj.setCodigo(obterValorChavePrimariaCodigo());
        obj.setNovoObj(new Boolean(false));
        updateImagemModelo(obj);
    }

    /**
     * Operação responsável por alterar no BD os dados de um objeto da classe <code>ModeloMensagemVO</code>.
     * Sempre utiliza a chave primária da classe como atributo para localização do registro a ser alterado.
     * Primeiramente valida os dados (<code>validarDados</code>) do objeto. Verifica a conexão com o banco de dados e a permissão do usuário
     * para realizar esta operacão na entidade.
     * Isto, através da operação <code>alterar</code> da superclasse.
     * @param obj    Objeto da classe <code>ModeloMensagemVO</code> que será alterada no banco de dados.
     * @exception Exception Caso haja problemas de conexão, restrição de acesso ou validação de dados.
     */
    public void alterar(ModeloMensagemVO obj) throws Exception {
        try {
            con.setAutoCommit(false);
            ModeloMensagemVO.validarDados(obj);
            alterarCRM(getIdEntidade());
            obj.realizarUpperCaseDados();
            String sql = "UPDATE ModeloMensagem set titulo=?, mensagem=?, meioDeEnvio=?, tipoMensagem=?, imagemModelo=?, nomeimagem=?, ativo = ?, urlRedirecionamento =?," +
                    " builder = ?, configs = ? " +
                    " WHERE ((codigo = ?))";
            PreparedStatement sqlAlterar = con.prepareStatement(sql);

            int i = 1;

            sqlAlterar.setString(i++, obj.getTitulo());
            sqlAlterar.setString(i++, obj.getMensagem());
            sqlAlterar.setInt(i++, obj.getMeioDeEnvioEnum().getCodigo());
            sqlAlterar.setString(i++, obj.getTipoMensagem());
            if (obj.getImagemModelo() != null && !PropsService.isTrue(PropsService.fotosParaNuvem)) {
                sqlAlterar.setBytes(i++, obj.getImagemModelo());
            } else {
                sqlAlterar.setNull(i++, 0);
            }
            sqlAlterar.setString(i++, obj.getNomeImagem());
            sqlAlterar.setBoolean(i++, obj.isAtivo());
            sqlAlterar.setString(i++, obj.getUrlRedirecionamento());
            sqlAlterar.setBoolean(i++, obj.getBuilder());
            sqlAlterar.setString(i++, obj.getConfigs());
            sqlAlterar.setInt(i++, obj.getCodigo().intValue());

            sqlAlterar.execute();
            con.commit();
            updateImagemModelo(obj);
        } catch (Exception e) {
            con.rollback();
            con.setAutoCommit(true);
            throw e;
        } finally {
            con.setAutoCommit(true);
        }
    }

    /**
     * Operação responsável por excluir no BD um objeto da classe <code>ModeloMensagemVO</code>.
     * Sempre localiza o registro a ser excluído através da chave primária da entidade.
     * Primeiramente verifica a conexão com o banco de dados e a permissão do usuário
     * para realizar esta operacão na entidade.
     * Isto, através da operação <code>excluir</code> da superclasse.
     * @param obj    Objeto da classe <code>ModeloMensagemVO</code> que será removido no banco de dados.
     * @exception Exception Caso haja problemas de conexão ou restrição de acesso.
     */
    public void excluir(ModeloMensagemVO obj) throws Exception {
        try {
            con.setAutoCommit(false);
            excluirCRM(getIdEntidade());
            String sql = "DELETE FROM ModeloMensagem WHERE ((codigo = ?))";
            PreparedStatement sqlExcluir = con.prepareStatement(sql);
            sqlExcluir.setInt(1, obj.getCodigo().intValue());
            sqlExcluir.execute();
            con.commit();
        } catch (Exception e) {
            con.rollback();
            con.setAutoCommit(true);
            throw e;
        } finally {
            con.setAutoCommit(true);
        }
    }

    /**
     * Responsável por realizar uma consulta de <code>ModeloMensagem</code> através do valor do atributo 
     * <code>String titulo</code>. Retorna os objetos, com início do valor do atributo idêntico ao parâmetro fornecido.
     * Faz uso da operação <code>montarDadosConsulta</code> que realiza o trabalho de prerarar o List resultante.
     * @param   controlarAcesso Indica se a aplicação deverá verificar se o usuário possui permissão para esta consulta ou não.
     * @return  List Contendo vários objetos da classe <code>ModeloMensagemVO</code> resultantes da consulta.
     * @exception Exception Caso haja problemas de conexão ou restrição de acesso.
     */
    public List consultarPorTitulo(String valorConsulta, boolean controlarAcesso, int nivelMontarDados) throws Exception {
        consultarCRM(getIdEntidade(), controlarAcesso);
        String sqlStr = "SELECT * FROM ModeloMensagem WHERE upper( titulo ) like('" + valorConsulta.toUpperCase() + "%') ORDER BY codigo desc";
        Statement stm = con.createStatement();
        ResultSet tabelaResultado = stm.executeQuery(sqlStr);
        return (montarDadosConsulta(tabelaResultado, nivelMontarDados, con));
    }

    /**
     * Responsável por realizar uma consulta de <code>ModeloMensagem</code> através do valor do atributo 
     * <code>Integer codigo</code>. Retorna os objetos com valores iguais ou superiores ao parâmetro fornecido.
     * Faz uso da operação <code>montarDadosConsulta</code> que realiza o trabalho de prerarar o List resultante.
     * @param   controlarAcesso Indica se a aplicação deverá verificar se o usuário possui permissão para esta consulta ou não.
     * @return  List Contendo vários objetos da classe <code>ModeloMensagemVO</code> resultantes da consulta.
     * @exception Exception Caso haja problemas de conexão ou restrição de acesso.
     */
    public List consultarPorCodigo(Integer valorConsulta, boolean controlarAcesso, int nivelMontarDados) throws Exception {
        consultarCRM(getIdEntidade(), controlarAcesso);
        String sqlStr = "SELECT * FROM ModeloMensagem WHERE ativo = true AND codigo = " + valorConsulta.intValue() + " ORDER BY codigo desc";
        Statement stm = con.createStatement();
        ResultSet tabelaResultado = stm.executeQuery(sqlStr);
        return (montarDadosConsulta(tabelaResultado, nivelMontarDados, con));
    }

    public List consultarPorCodigo(Integer valorConsulta,MeioEnvio meioEnvio, boolean controlarAcesso, int nivelMontarDados) throws Exception {
        consultarCRM(getIdEntidade(), controlarAcesso);
        String sqlStr = "SELECT * FROM ModeloMensagem WHERE ativo = true AND codigo >= " + valorConsulta.intValue() + " and meioDeEnvio = "+meioEnvio.getCodigo()+" ORDER BY codigo desc";
        Statement stm = con.createStatement();
        ResultSet tabelaResultado = stm.executeQuery(sqlStr);
        return (montarDadosConsulta(tabelaResultado, nivelMontarDados, con));
    }

     public List consultarPorTitulo(String valorConsulta,MeioEnvio meioEnvio, boolean controlarAcesso, int nivelMontarDados) throws Exception {
        consultarCRM(getIdEntidade(), controlarAcesso);
        String sqlStr = "SELECT * FROM ModeloMensagem WHERE ativo = true AND upper( titulo ) "
                + "like('" + valorConsulta.toUpperCase() + "%') "
                + "and meioDeEnvio = "+meioEnvio.getCodigo()+" ORDER BY codigo desc";
        Statement stm = con.createStatement();
        ResultSet tabelaResultado = stm.executeQuery(sqlStr);
        return (montarDadosConsulta(tabelaResultado, nivelMontarDados, con));
    }

    public List consultarPorNomeModeloMensagemComLimite(String valorConsulta, boolean controlarAcesso, int nivelMontarDados) throws Exception {
        consultar(getIdEntidade(), controlarAcesso);
        String sqlStr = "";
        sqlStr = "SELECT ModeloMensagem.* FROM modeloMensagem WHERE upper(ModeloMensagem.titulo) like('" + valorConsulta.toUpperCase() + "%') ORDER BY codigo desc limit 20";
        Statement stm = con.createStatement();
        ResultSet tabelaResultado = stm.executeQuery(sqlStr);
        return (montarDadosConsulta(tabelaResultado, nivelMontarDados, con));
    }

    public ModeloMensagemVO consultarPorNomeUsuario(String valorConsulta, boolean controlarAcesso, int nivelMontarDados) throws Exception {
        consultar(getIdEntidade(), controlarAcesso);
        String sqlStr = "SELECT * FROM modeloMensagem WHERE upper( titulo ) like('" + valorConsulta.toUpperCase() + "%') ORDER BY codigo desc ";
        Statement stm = con.createStatement();
        ResultSet tabelaResultado = stm.executeQuery(sqlStr);
        if (!tabelaResultado.next()) {
            ModeloMensagemVO obj = new ModeloMensagemVO();
            return obj;
        }
        return montarDados(tabelaResultado, nivelMontarDados, con);
    }

    /**
     * Responsável por montar os dados de vários objetos, resultantes de uma consulta ao banco de dados (<code>ResultSet</code>).
     * Faz uso da operação <code>montarDados</code> que realiza o trabalho para um objeto por vez.
     * @return  List Contendo vários objetos da classe <code>ModeloMensagemVO</code> resultantes da consulta.
     */
    public static List montarDadosConsulta(ResultSet tabelaResultado, int nivelMontarDados, Connection con) throws Exception {
        List vetResultado = new ArrayList();
        while (tabelaResultado.next()) {
            ModeloMensagemVO obj = new ModeloMensagemVO();
            obj = montarDados(tabelaResultado, nivelMontarDados, con);
            vetResultado.add(obj);
        }
        tabelaResultado = null;
        return vetResultado;
    }

    /**
     * Responsável por montar os dados resultantes de uma consulta ao banco de dados (<code>ResultSet</code>)
     * em um objeto da classe <code>ModeloMensagemVO</code>.
     * @return  O objeto da classe <code>ModeloMensagemVO</code> com os dados devidamente montados.
     */
    public static ModeloMensagemVO montarDados(ResultSet dadosSQL, int nivelMontarDados, Connection con) throws Exception {
        ModeloMensagemVO obj = new ModeloMensagemVO();
        obj.setCodigo(new Integer(dadosSQL.getInt("codigo")));
        obj.setTitulo(dadosSQL.getString("titulo"));
        obj.setMensagem(dadosSQL.getString("mensagem"));
        obj.setMeioDeEnvioEnum(MeioEnvio.getMeioEnvioPorCodigo(dadosSQL.getInt("meioDeEnvio")));
        obj.setTipoMensagem(dadosSQL.getString("tipoMensagem"));
        try {
            obj.setBuilder(dadosSQL.getBoolean("builder"));
            obj.setConfigs(dadosSQL.getString("configs"));
        }catch (Exception e){
            Uteis.logar(e, ModeloMensagem.class);
        }
        if (nivelMontarDados == Uteis.NIVELMONTARDADOS_TELACONSULTA) {
            return obj;
        }
        obj.setImagemModelo(dadosSQL.getBytes("imagemModelo"));
        String nomeImagem = dadosSQL.getString("nomeimagem") == null ? "" : dadosSQL.getString("nomeimagem");
        
        obj.setNomeImagem(nomeImagem.replaceAll(" ", ""));
        obj.setMensagem(obj.getMensagem().replace(nomeImagem, nomeImagem.replaceAll(" ", "")));
        obj.setAtivo(dadosSQL.getBoolean("ativo"));
        obj.setUrlRedirecionamento(dadosSQL.getString("urlRedirecionamento"));
        obj.setNovoObj(new Boolean(false));
        if (nivelMontarDados == Uteis.NIVELMONTARDADOS_DADOSBASICOS) {
            return obj;
        }

        return obj;
    }

    /**
     * Operação responsável por localizar um objeto da classe <code>ModeloMensagemVO</code>
     * através de sua chave primária. 
     * @exception Exception Caso haja problemas de conexão ou localização do objeto procurado.
     */
    public ModeloMensagemVO consultarPorChavePrimaria(Integer codigoPrm, int nivelMontarDados) throws Exception {
        consultarCRM(getIdEntidade(), false);
        ModeloMensagemVO eCache = (ModeloMensagemVO) obterFromCache(codigoPrm);
        if (eCache != null) {
            return eCache;
        }
        String sql = "SELECT * FROM ModeloMensagem WHERE codigo = ?";
        try (PreparedStatement sqlConsultar = con.prepareStatement(sql)) {
            sqlConsultar.setInt(1, codigoPrm);
            try (ResultSet tabelaResultado = sqlConsultar.executeQuery()) {
                if (!tabelaResultado.next()) {
                    throw new ConsistirException("Dados Não Encontrados ( ModeloMensagem ).");
                }
                eCache = (montarDados(tabelaResultado, nivelMontarDados, con));
            }
        }
        putToCache(eCache);
        return eCache;
    }

    public int obterNumeroTotalImagensModelo() throws Exception {
        StringBuffer sql = new StringBuffer();
        int result = 0;

        sql.append("select count(codigo) as total from modelomensagem where bit_length(imagemmodelo) > 0");

        PreparedStatement ps = con.prepareStatement(sql.toString());

        ResultSet rs = ps.executeQuery();
        if (rs.next()) {
            result = rs.getInt("total");
        }

        return result;
    }
    
    public boolean validaNomeArquivo(String nomeArquivo) throws Exception{
    	return existe("SELECT * FROM modelomensagem WHERE nomeimagem like '"+nomeArquivo+"'", con);
    }

    @Override
    public void updateImagemModelo(ModeloMensagemVO obj) {
        try {
            if (PropsService.isTrue(PropsService.fotosParaNuvem)
                    && obj.isFeitoUploadAlgumaFoto()
                    && !UteisValidacao.emptyNumber(obj.getCodigo())) {
                final String chave = DAO.resolveKeyFromConnection(con);
                MidiaService.getInstance().uploadObjectFromByteArray(chave, 
                                                           MidiaEntidadeEnum.FOTO_MODELO_MENSAGEM, obj.getCodigo().toString(), 
                                                           obj.getImagemModelo());
            }            
        } catch (Exception e) {
            Uteis.logar(e, ModeloMensagem.class);
        }
    }

    private ResultSet getRS() throws SQLException {

        StringBuilder sql = new StringBuilder("SELECT\n" +
                "  codigo,\n" +
                "  titulo,\n" +
                "  tipomensagem,\n" +
                "  meiodeenvio,\n" +
                "  ativo\n" +
                "FROM modelomensagem;");

        PreparedStatement sqlConsultar = con.prepareStatement(sql.toString());
        ResultSet rs = sqlConsultar.executeQuery();
        return rs;
    }

    public String consultarJSON() throws Exception {
        ResultSet rs = getRS();

        StringBuilder json = new StringBuilder();
        json.append("{\"aaData\":[");
        boolean dados = false;
        while (rs.next()) {
            dados = true;
            json.append("[\"").append(rs.getInt("codigo")).append("\",");
            json.append("\"").append(Uteis.normalizarStringJSON(rs.getString("titulo"))).append("\",");
            json.append("\"").append(Dominios.getTipoMensagem().get(((rs.getString("tipomensagem"))))).append("\",");
            json.append("\"").append(MeioEnvio.getMeioEnvioPorCodigo(rs.getInt("meiodeenvio"))).append("\",");
            if(rs.getBoolean("ativo")){
                json.append("\"").append("Ativo").append("\"],");
            } else {
                json.append("\"").append("Inativo").append("\"],");
            }

        }
        if(dados) {
            json.deleteCharAt(json.toString().length() - 1);
        }
        json.append("]}");
        return json.toString();
    }

    public ModeloMensagemVO consultarPorTipo(String tipoMensagem, int nivelMontarDados) throws Exception {
        consultarCRM(getIdEntidade(), false);
        String sql = "SELECT * FROM modelomensagem WHERE tipomensagem = ?";
        PreparedStatement sqlConsultar = con.prepareStatement(sql);
        sqlConsultar.setString(1, tipoMensagem);
        ResultSet tabelaResultado = sqlConsultar.executeQuery();
        if (!tabelaResultado.next()) {
            return null;
        }
        return (montarDados(tabelaResultado, nivelMontarDados, con));
    }

    public List<MsgBuildDTO> consultarModelosAntigos() throws Exception{
        return consultarMensagensBuilder(MeioEnvio.EMAIL, true);
    }
    public List<MsgBuildDTO> consultarMensagensBuilder(MeioEnvio meioEnvio) throws Exception{
        return consultarMensagensBuilder(meioEnvio, false);
    }

    public List<MsgBuildDTO> consultarMensagensWhatsAppBuilder(Integer empresaId, String key, MeioEnvio meioEnvio) throws Exception{
        WhatsAppController whatsAppController = new WhatsAppController(key, null,  this.con);
        return whatsAppController.consultarTemplateWhatsApp(empresaId, key, meioEnvio);
    }

    public List<MsgBuildDTO> consultarMensagensBuilder(MeioEnvio meioEnvio, boolean antigas) throws Exception{
        List<MsgBuildDTO> mensagens = new ArrayList<>();
        ResultSet rs = criarConsulta("select codigo, titulo, tipomensagem, mensagem, configs from modelomensagem " +
                "where " + (antigas ? " not builder  " : " (builder or meiodeenvio <> 1) ") +
                "and ativo and meiodeenvio = " + meioEnvio.getCodigo(), con);
        while(rs.next()){
            MsgBuildDTO msg = new MsgBuildDTO();
            msg.setCodigo(rs.getInt("codigo"));
            msg.setBody(rs.getString("configs"));
            msg.setTipo(rs.getString("tipomensagem"));
            msg.setHtml(rs.getString("mensagem"));
            String titulo = rs.getString("titulo");
            msg.setNome(URLEncoder.encode(titulo == null ? "Sem título" : titulo, "iso-8859-1"));
            mensagens.add(msg);
        }
        return mensagens;
    }

    public void desativar(Integer codigo) throws Exception{
        executarConsulta("update modelomensagem set ativo = false where codigo = " + codigo, con);
    }

    public List<MsgBuildDTO> getTemplatesPacto(String chave){
        try {
            return templates(chave);
        }catch (Exception e){
            return new ArrayList<>();
        }
    }

    public void clearTemplatesPacto(){
        templatesPacto = new HashMap<>();
        mapaChaveMarketing = new HashMap<>();
    }

    public String chaveMarketingRede(String chave) {
        String chaveMarketing = mapaChaveMarketing.get(chave);
        try {
            if (chaveMarketing == null) {
                RedeEmpresa redeEmpresaDao = new RedeEmpresa(getCon());
                RedeEmpresaVO redeEmpresaVO = redeEmpresaDao.consultarPorChaveZWOAMD(chave);
                if (!UteisValidacao.emptyString(redeEmpresaVO.getChaveFranqueadora())) {
                    chaveMarketing = redeEmpresaVO.getChaveFranqueadora();
                }
            }
        } catch (Exception e) {
            Uteis.logar(e, ModeloMensagem.class);
        }
        chaveMarketing = chaveMarketing == null ? "modelosDefault" : chaveMarketing;
        mapaChaveMarketing.put(chave, chaveMarketing);
        return chaveMarketing;
    }

    public List<MsgBuildDTO> templates(String chave) throws Exception {
        String chaveMarketing = chaveMarketingRede(chave);
        List<MsgBuildDTO> msgBuildDTOS = templatesPacto.get(chaveMarketing);
        if (msgBuildDTOS == null) {
            String result = ExecuteRequestHttpService.executeHttpRequest(PropsService.getPropertyValue("DISCOVERY_URL") + "/chaves/modelos"
                            + (chaveMarketing.equals("modelosDefault")  ? "" : ("?chave=" + chaveMarketing)), null,
                    new HashMap<>(), ExecuteRequestHttpService.METODO_GET,
                    Charsets.ISO_8859_1.name());
            String url = new JSONObject(result).getString("content");
            msgBuildDTOS = buscarMontar(url);
            templatesPacto.put(chaveMarketing, msgBuildDTOS);
        }
        return msgBuildDTOS;
    }

    public List<MsgBuildDTO> buscarMontar(String url) throws Exception{
        List<MsgBuildDTO> msgBuildDTOS = new ArrayList<>();
        String resultDados = ExecuteRequestHttpService.executeHttpRequest(url, null,
                new HashMap<>(), ExecuteRequestHttpService.METODO_GET,
                Charsets.UTF_8.name());
        JSONArray jsonArray = new JSONArray(resultDados);
        for(int i = 0; i < jsonArray.length(); i++){
            JSONObject jsonObject = jsonArray.getJSONObject(i);
            try {
                MsgBuildDTO msgBuildDTO = new MsgBuildDTO();
                msgBuildDTO.setCodigo(jsonObject.getInt("codigo"));
                msgBuildDTO.setNome(URLDecoder.decode(jsonObject.getString("nome"), "iso-8859-1"));
                msgBuildDTO.setHtml(jsonObject.getString("html"));
                msgBuildDTO.setBody(jsonObject.getString("body"));
                msgBuildDTOS.add(msgBuildDTO);
            }catch (Exception e){
                Uteis.logar(e, ModeloMensagem.class);
            }
        }
        return msgBuildDTOS;
    }

}

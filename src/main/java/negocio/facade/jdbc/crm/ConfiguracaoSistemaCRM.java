package negocio.facade.jdbc.crm;

import br.com.pactosolucoes.enumeradores.ConfiguracaoEmailEnum;
import br.com.pactosolucoes.enumeradores.FasesCRMEnum;
import negocio.comuns.arquitetura.UsuarioVO;
import negocio.comuns.basico.ConfiguracaoEmailFechamentoMetaVO;
import negocio.comuns.basico.EmpresaVO;
import negocio.comuns.crm.ConfiguracaoDiasMetasTO;
import negocio.comuns.crm.ConfiguracaoDiasPosVendaVO;
import negocio.comuns.crm.ConfiguracaoEmpresaBitrixVO;
import negocio.comuns.crm.ConfiguracaoSistemaCRMVO;
import negocio.comuns.plano.ProdutoVO;
import negocio.comuns.utilitarias.ConsistirException;
import negocio.comuns.utilitarias.DiasDaSemana;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisEmail;
import negocio.comuns.utilitarias.UteisValidacao;
import negocio.comuns.utilitarias.Calendario;
import negocio.facade.jdbc.arquitetura.SuperEntidade;
import negocio.facade.jdbc.arquitetura.SuperFacadeJDBC;
import negocio.facade.jdbc.arquitetura.Usuario;
import negocio.interfaces.crm.ConfiguracaoSistemaCRMInterfaceFacade;
import servicos.operacoes.MailingItensController;
import servicos.operacoes.MailingItensController.MailingItem;
import servicos.propriedades.PropsService;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.Statement;
import java.sql.Types;
import java.util.ArrayList;
import java.util.Date;
import java.util.Hashtable;
import java.util.List;

/**
 * Classe de persistência que encapsula todas as operações de manipulação dos
 * dados da classe
 * <code>ConfiguracaoSistemaCRMVO</code>. Responsável por implementar operações
 * como incluir, alterar, excluir e consultar pertinentes a classe
 * <code>ConfiguracaoSistemaCRMVO</code>. Encapsula toda a interação com o banco
 * de dados.
 *
 * @see ConfiguracaoSistemaCRMVO
 * @see SuperEntidade
 */
public class ConfiguracaoSistemaCRM extends SuperEntidade implements ConfiguracaoSistemaCRMInterfaceFacade {

    private Hashtable configuracaoDiasPosVendas;

    public ConfiguracaoSistemaCRM() throws Exception {
        super();
        setIdEntidade("ConfiguracaoSistemaCRM");
        setConfiguracaoDiasPosVendas(new Hashtable());
    }

    public ConfiguracaoSistemaCRM(Connection con) throws Exception {
        super(con);
        setIdEntidade("ConfiguracaoSistemaCRM");
        setConfiguracaoDiasPosVendas(new Hashtable());
    }

    /**
     * Responsável por montar os dados de vários objetos, resultantes de uma
     * consulta ao banco de dados (
     * <code>ResultSet</code>). Faz uso da operação
     * <code>montarDados</code> que realiza o trabalho para um objeto por vez.
     *
     * @return List Contendo vários objetos da
     * classe <code>ConfiguracaoSistemaCRMVO</code> resultantes da consulta.
     */
    public static List<ConfiguracaoSistemaCRMVO> montarDadosConsulta(ResultSet tabelaResultado, int nivelMontarDados, Connection con) throws Exception {
        List<ConfiguracaoSistemaCRMVO> vetResultado = new ArrayList<ConfiguracaoSistemaCRMVO>();
        while (tabelaResultado.next()) {
            ConfiguracaoSistemaCRMVO obj = montarDados(tabelaResultado, nivelMontarDados, con);
            vetResultado.add(obj);
        }
        tabelaResultado = null;
        return vetResultado;
    }

    /**
     * Responsável por montar os dados resultantes de uma consulta ao banco de
     * dados (
     * <code>ResultSet</code>) em um objeto da classe
     * <code>ConfiguracaoSistemaCRMVO</code>.
     *
     * @return O objeto da classe <code>ConfiguracaoSistemaCRMVO</code> com os
     * dados devidamente montados.
     */
    public static ConfiguracaoSistemaCRMVO montarDados(ResultSet dadosSQL, int nivelMontarDados, Connection con) throws Exception {
        ConfiguracaoSistemaCRMVO obj = new ConfiguracaoSistemaCRMVO();
        obj.setUrlJenkins(PropsService.getPropertyValue("urlJenkins"));
        obj.setUrlMailing(PropsService.getPropertyValue("urlMailing"));
        obj.setCodigo(new Integer(dadosSQL.getInt("codigo")));
        obj.setRemetentePadrao(dadosSQL.getString("remetentePadrao"));
        obj.setUsarRemetentePadraoGeral(dadosSQL.getBoolean("usarRemetentePadraoGeral"));
        obj.setEmailPadrao(dadosSQL.getString("emailPadrao"));
        obj.setMailServer(dadosSQL.getString("mailServer"));
        obj.setLogin(dadosSQL.getString("login"));
        obj.setSenha(dadosSQL.getString("senha"));
        obj.setAbertoSabado(dadosSQL.getBoolean("abertoSabado"));
        obj.setAbertoDomingo(dadosSQL.getBoolean("abertoDomingo"));
        obj.setNrFaltaPlanoMensal(dadosSQL.getInt("nrFaltaPlanoMensal"));
        obj.setNrFaltaPlanoTrimestral(dadosSQL.getInt("nrFaltaPlanoTrimestral"));
        obj.setNrFaltaPlanoAcimaSemestral(dadosSQL.getInt("nrFaltaPlanoAcimaSemestral"));
        obj.setNrRisco(dadosSQL.getInt("nrRisco"));
        obj.setNrDiasParaClientePreveRenovacao(dadosSQL.getInt("nrDiasParaClientePreveRenovacao"));
        obj.setNrDiasParaClientePreveRenovacaoMaiorUmMes(dadosSQL.getInt("nrDiasParaClientePreveRenovacaoMaiorUmMes"));
        obj.setNrDiasAnterioresAgendamento(dadosSQL.getInt("nrDiasAnterioresAgendamento"));
        obj.setNrDiasPosterioresAgendamento(dadosSQL.getInt("nrDiasPosterioresAgendamento"));
        obj.setNrDiasParaClientePrevePerda(dadosSQL.getInt("nrDiasParaClientePrevePerda"));
        obj.setConexaoSegura(dadosSQL.getBoolean("conexaoSegura"));
        obj.setDividirFase(dadosSQL.getBoolean("dividirfase"));
        obj.setBloquearTermoSpam(dadosSQL.getBoolean("bloqueartermospam"));
        obj.setIniciarTLS(dadosSQL.getBoolean("iniciartls"));
        obj.setIndicacoesMes(dadosSQL.getInt("qtdIndicacoesMes"));
        obj.setConversaoAgendadosMes(dadosSQL.getInt("qtdConversoesVendasMes"));
        obj.setQtdConversoesExAlunosMes(dadosSQL.getInt("qtdConversoesExAlunosMes"));
        obj.setNovoObj(false);
        obj.setNrDiasLimiteAgendamentoFuturo(dadosSQL.getInt("nrDiasLimiteAgendamentoFuturo"));
        obj.setNrDiasPosAgendamentoConversaoExAluno(dadosSQL.getInt("nrDiasPosAgendamentoConversaoExAluno"));
        obj.setIncluirContratosRenovados(dadosSQL.getBoolean("incluircontratosrenovados"));
        try {
            obj.setIntegracaoPacto(dadosSQL.getBoolean("integracaopacto"));
            obj.setLimiteDiarioEmails(dadosSQL.getInt("limitediarioemails"));
            obj.setLimiteMensalPacto(dadosSQL.getInt("limitemensalpacto"));
        }catch (Exception e){
            Uteis.logar(e, ConfiguracaoSistemaCRM.class);
        }
        try {
            obj.setBaterMetaTodasAcoes(dadosSQL.getBoolean("batermetatodasacoes"));
            obj.setNrDiasContarResultado(dadosSQL.getInt("nrdiascontarresultado"));
            obj.setConsiderarProfessorTreinoWeb(dadosSQL.getBoolean("considerarProfessorTreinoWeb"));
            obj.setPortaServer(dadosSQL.getString("portaserver"));
            obj.setObrigatorioSeguirOrdemMetas(dadosSQL.getBoolean("obrigatorioSeguirOrdemMetas"));
            obj.setOrdenacaoMetas(dadosSQL.getString("ordenacaoMetas"));
            obj.getRemetentePadraoMailing().setCodigo(dadosSQL.getInt("remetentepadraomailing"));
            obj.setEnviarEmailIndividualmente(dadosSQL.getBoolean("enviarEmailIndividualmente"));
            obj.setNrCreditosTreinoRenovar(dadosSQL.getInt("nrCreditosTreinoRenovar"));
            obj.setAgendamentoParaMetaConsultor(dadosSQL.getBoolean("agendamentoParaMetaConsultor"));
            obj.setAutorrenovavelEntraRenovacao(dadosSQL.getBoolean("autorrenovavelEntraRenovacao"));
            obj.setApresentarColaboradoresPorTipoColaborador(dadosSQL.getBoolean("apresentarColaboradoresPorTipoColaborador"));
            obj.setApresentarColaboradoresInativos(dadosSQL.getBoolean("apresentarColaboradoresInativos"));
            obj.setMailingFtpServer(dadosSQL.getString("mailingFtpServer"));
            obj.setMailingFtpUser(dadosSQL.getString("mailingFtpUser"));
            obj.setMailingFtpPass(dadosSQL.getString("mailingFtpPass"));
            obj.setMailingFtpPort(dadosSQL.getInt("mailingFtpPort"));
            obj.setMailingFtpType(dadosSQL.getString("mailingFtpType"));
            obj.setMailingFtpFolder(dadosSQL.getString("mailingFtpFolder"));
            obj.setUsaSMTPS(dadosSQL.getBoolean("usaSMTPS"));
            obj.setUsaConfiguracaoEmailManual(dadosSQL.getBoolean("usaConfiguracaoEmailManual"));
            obj.setTokenBitly(dadosSQL.getString("tokenBitly"));
            obj.setGerarIndicacaoParaCadastroConvidadosVendasOnline(dadosSQL.getBoolean("gerarIndicacaoParaCadastroConvidadosVendasOnline"));
            obj.setDirecionaragendamentosexperimentaisagenda(dadosSQL.getBoolean("direcionaragendamentosexperimentaisagenda"));
        } catch (Exception ignored) {
        }
        if (nivelMontarDados == Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA) {
            return obj;
        }
        obterTermos(obj, con);
        if (nivelMontarDados == Uteis.NIVELMONTARDADOS_DADOSBASICOS) {
            return obj;
        }
        obj.setConfiguracaoDiasPosVendaVOs(new ConfiguracaoDiasPosVenda(con).consultarConfiguracaoDiasPosVendas(obj.getCodigo(), nivelMontarDados));
        montarDadosConfiguracaoDiasMetas(obj, con);
        if (nivelMontarDados == Uteis.NIVELMONTARDADOS_DADOSENTIDADESUBORDINADAS) {
            return obj;
        }

        if (nivelMontarDados == Uteis.NIVELMONTARDADOS_TODOS) {
            montarDadosRemetentePadraoMailing(obj, Uteis.NIVELMONTARDADOS_MINIMOS, con);
        }

        return obj;
    }

    public static void montarDadosRemetentePadraoMailing(ConfiguracaoSistemaCRMVO obj, int nivelMontarDados, Connection con) throws Exception {
        if (obj.getRemetentePadraoMailing().getCodigo() == 0) {
            obj.setRemetentePadraoMailing(new UsuarioVO());
            return;
        }
        Usuario usuario = new Usuario(con);
        obj.setRemetentePadraoMailing(usuario.consultarPorChavePrimaria(obj.getRemetentePadraoMailing().getCodigo(), nivelMontarDados));
        usuario = null;
    }

    private static void obterTermos(ConfiguracaoSistemaCRMVO cfg, Connection con) throws Exception {
        try (ResultSet resultSet = criarConsulta("SELECT * FROM termospam", con)) {
            cfg.setTermosSpam(new ArrayList<String>());
            while (resultSet.next()) {
                cfg.getTermosSpam().add(resultSet.getString("termo"));
            }
        }
    }

    public static void montarDadosConfiguracaoDiasMetas(ConfiguracaoSistemaCRMVO crm, Connection con) throws Exception {
        try (ResultSet consulta = criarConsulta("SELECT cdm.codigo, nrdias, cdm.descricao, fase, prod.codigo as produto, prod.descricao as produtodesc FROM configuracaodiasmetas cdm LEFT JOIN produto prod ON cdm.produto = prod.codigo order by nrdias", con)) {
            while (consulta.next()) {
                int fase = consulta.getInt("fase");
                if (fase == 0) {
                    return;
                }
                if (FasesCRMEnum.EX_ALUNOS.getCodigo().equals(fase)) {
                    crm.getConfiguracaoDiasMetasExAlunos().add(new ConfiguracaoDiasMetasTO(consulta.getInt("nrdias"), consulta.getString("descricao")));
                } else if (FasesCRMEnum.VISITANTES_ANTIGOS.getCodigo().equals(fase)) {
                    crm.getConfiguracaoDiasMetasVisitantesAntigos().add(new ConfiguracaoDiasMetasTO(consulta.getInt("nrdias"), consulta.getString("descricao")));
                } else if (FasesCRMEnum.ULTIMAS_SESSOES.getCodigo().equals(fase)) {
                    crm.getConfiguracaoDiasMetasSessoesFinais().add(new ConfiguracaoDiasMetasTO(consulta.getInt("nrdias"), consulta.getString("descricao")));
                } else if (FasesCRMEnum.ALUNO_ULTIMO_ACESSO_GYMPASS.getCodigo().equals(fase)) {
                    crm.getConfiguracaoDiasMetasUltimoAcessoGymp().add(new ConfiguracaoDiasMetasTO(consulta.getInt("nrdias"), consulta.getString("descricao")));
                } else {
                    ProdutoVO produto = new ProdutoVO();
                    produto.setCodigo(consulta.getInt("produto"));
                    produto.setDescricao(consulta.getString("produtodesc"));
                    crm.getConfiguracaoDiasMetasSemAgendamento().add(new ConfiguracaoDiasMetasTO(consulta.getInt("nrdias"), consulta.getString("descricao"), produto));
                }
            }
        }
    }
    
    /**
     * Operação responsável por retornar um novo objeto da classe
     * <code>ConfiguracaoSistemaCRMVO</code>.
     */
    public ConfiguracaoSistemaCRMVO novo() throws Exception {
        incluirCRM(getIdEntidade());
        return new ConfiguracaoSistemaCRMVO();
    }

    /**
     * Operação responsável por incluir no banco de dados um objeto da classe
     * <code>ConfiguracaoSistemaCRMVO</code>. Primeiramente valida os dados (
     * <code>validarDados</code>) do objeto. Verifica a conexão com o banco de
     * dados e a permissão do usuário para realizar esta operacão na entidade.
     * Isto, através da operação
     * <code>incluir</code> da superclasse.
     *
     * @param obj Objeto da classe <code>ConfiguracaoSistemaCRMVO</code> que
     * será gravado no banco de dados.
     * @throws Exception Caso haja problemas de conexão, restrição de acesso ou
     * validação de dados.
     */
    public void incluir(ConfiguracaoSistemaCRMVO obj) throws Exception {
        try {
            con.setAutoCommit(false);
            incluirSemCommit(obj);
            con.commit();
        } catch (Exception e) {
            obj.setNovoObj(true);
            con.rollback();
            con.setAutoCommit(true);
            throw e;
        } finally {
            con.setAutoCommit(true);
        }
    }

    public void incluirSemCommit(ConfiguracaoSistemaCRMVO obj) throws Exception {
        ConfiguracaoSistemaCRMVO.validarDados(obj);
        incluirCRM(getIdEntidade());
        obj.realizarUpperCaseDados();
        String sql = "INSERT INTO ConfiguracaoSistemaCRM( remetentePadrao, usarRemetentePadraoGeral, emailPadrao, mailServer, login, senha, abertoSabado,"
                + "abertoDomingo, nrFaltaPlanoMensal, nrFaltaPlanoTrimestral, nrFaltaPlanoAcimaSemestral, nrRisco, "
                + "nrDiasParaClientePreveRenovacao, nrDiasParaClientePrevePerda, conexaoSegura, dividirfase, "
                + "nrDiasAnterioresAgendamento, nrDiasPosterioresAgendamento, nrDiasLimiteAgendamentoFuturo, "
                + "bloqueartermospam, iniciartls, qtdIndicacoesMes, qtdConversoesVendasMes, "
                + "nrDiasPosAgendamentoConversaoExAluno, qtdConversoesExAlunosMes, incluircontratosrenovados, "
                + "considerarProfessorTreinoWeb, batermetatodasacoes, nrdiascontarresultado, portaServer, "
                + "obrigatorioSeguirOrdemMetas, ordenacaoMetas, remetentepadraomailing, enviarEmailIndividualmente, "
                + "nrCreditosTreinoRenovar,agendamentoParaMetaConsultor, autorrenovavelEntraRenovacao, apresentarColaboradoresPorTipoColaborador, "
                + "apresentarColaboradoresInativos, nrDiasParaClientePreveRenovacaoMaiorUmMes, "
                + "mailingFtpServer, mailingFtpUser, mailingFtpPass, mailingFtpPort, mailingFtpType, mailingFtpFolder, usaSMTPS, usaConfiguracaoEmailManual,"
                + " tokenBitly, integracaopacto, limitediarioemails, limitemensalpacto) "
                + "VALUES ( ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, " +
                " ?, ?,?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)";
        int i = 1;
        try (PreparedStatement sqlInserir = con.prepareStatement(sql)) {
            sqlInserir.setString(i++, obj.getRemetentePadrao());
            sqlInserir.setBoolean(i++, obj.getUsarRemetentePadraoGeral());
            sqlInserir.setString(i++, obj.getEmailPadrao());
            sqlInserir.setString(i++, obj.getMailServer());
            sqlInserir.setString(i++, obj.getLogin());
            sqlInserir.setString(i++, obj.getSenha());
            sqlInserir.setBoolean(i++, obj.isAbertoSabado());
            sqlInserir.setBoolean(i++, obj.isAbertoDomingo());
            sqlInserir.setInt(i++, obj.getNrFaltaPlanoMensal());
            sqlInserir.setInt(i++, obj.getNrFaltaPlanoTrimestral());
            sqlInserir.setInt(i++, obj.getNrFaltaPlanoAcimaSemestral());
            sqlInserir.setInt(i++, obj.getNrRisco());
            sqlInserir.setInt(i++, obj.getNrDiasParaClientePreveRenovacao());
            sqlInserir.setInt(i++, obj.getNrDiasParaClientePrevePerda());
            sqlInserir.setBoolean(i++, obj.isConexaoSegura());
            sqlInserir.setBoolean(i++, obj.getDividirFase());
            sqlInserir.setInt(i++, obj.getNrDiasAnterioresAgendamento());
            sqlInserir.setInt(i++, obj.getNrDiasPosterioresAgendamento());
            sqlInserir.setInt(i++, obj.getNrDiasLimiteAgendamentoFuturo());
            sqlInserir.setBoolean(i++, obj.getBloquearTermoSpam());
            sqlInserir.setBoolean(i++, obj.isIniciarTLS());
            sqlInserir.setInt(i++, obj.getIndicacoesMes());
            sqlInserir.setInt(i++, obj.getConversaoAgendadosMes());
            sqlInserir.setInt(i++, obj.getNrDiasPosAgendamentoConversaoExAluno());
            sqlInserir.setInt(i++, obj.getQtdConversoesExAlunosMes());
            sqlInserir.setBoolean(i++, obj.isIncluirContratosRenovados());
            sqlInserir.setBoolean(i++, obj.isConsiderarProfessorTreinoWeb());
            sqlInserir.setBoolean(i++, obj.isBaterMetaTodasAcoes());
            sqlInserir.setInt(i++, obj.getNrDiasContarResultado());
            sqlInserir.setString(i++, obj.getPortaServer());
            sqlInserir.setBoolean(i++, obj.isObrigatorioSeguirOrdemMetas());
            sqlInserir.setString(i++, obj.getOrdenacaoMetas());
            if (obj.getRemetentePadraoMailing() != null && obj.getRemetentePadraoMailing().getCodigo() != 0) {
                sqlInserir.setInt(i++, obj.getRemetentePadraoMailing().getCodigo());
            } else {
                sqlInserir.setNull(i++, Types.INTEGER);
            }
            sqlInserir.setBoolean(i++, obj.isEnviarEmailIndividualmente());
            sqlInserir.setInt(i++, obj.getNrCreditosTreinoRenovar());
            sqlInserir.setBoolean(i++, obj.isAgendamentoParaMetaConsultor());
            sqlInserir.setBoolean(i++, obj.isAutorrenovavelEntraRenovacao());
            sqlInserir.setBoolean(i++, obj.isApresentarColaboradoresPorTipoColaborador());
            sqlInserir.setBoolean(i++, obj.isApresentarColaboradoresInativos());
            sqlInserir.setInt(i++, obj.getNrDiasParaClientePreveRenovacaoMaiorUmMes());
            sqlInserir.setString(i++, obj.getMailingFtpServer());
            sqlInserir.setString(i++, obj.getMailingFtpUser());
            sqlInserir.setString(i++, obj.getMailingFtpPass());
            sqlInserir.setInt(i++, obj.getMailingFtpPort());
            sqlInserir.setString(i++, obj.getMailingFtpType());
            sqlInserir.setString(i++, obj.getMailingFtpFolder());
            sqlInserir.setBoolean(i++, obj.getUsaSMTPS());
            sqlInserir.setBoolean(i++, obj.getUsaConfiguracaoEmailManual());
            sqlInserir.setString(i++, obj.getTokenBitly());
            sqlInserir.setBoolean(i++, obj.getIntegracaoPacto());
            sqlInserir.setInt(i++, obj.getLimiteDiarioEmails());
            sqlInserir.setInt(i++, obj.getLimiteMensalPacto());
            sqlInserir.execute();
        }
        obj.setCodigo(obterValorChavePrimariaCodigo());
        getFacade().getConfiguracaoDiasPosVenda().incluirConfiguracaoDiasPosVendas(obj.getCodigo(), obj.getConfiguracaoDiasPosVendaVOs());
        gravarDiasMetas(obj.getConfiguracaoDiasMetasSessoesFinais(), FasesCRMEnum.ULTIMAS_SESSOES);
        gravarDiasMetas(obj.getConfiguracaoDiasMetasSemAgendamento(), FasesCRMEnum.SEM_AGENDAMENTO);
        gravarDiasMetas(obj.getConfiguracaoDiasMetasExAlunos(), FasesCRMEnum.EX_ALUNOS);
        gravarDiasMetas(obj.getConfiguracaoDiasMetasVisitantesAntigos(), FasesCRMEnum.VISITANTES_ANTIGOS);
        gravarDiasMetas(obj.getConfiguracaoDiasMetasUltimoAcessoGymp(), FasesCRMEnum.ALUNO_ULTIMO_ACESSO_GYMPASS);
        gravarTermosSpam(obj);
        obj.setNovoObj(false);

    }

    /**
     * Operação responsável por alterar no BD os dados de um objeto da classe
     * <code>ConfiguracaoSistemaCRMVO</code>. Sempre utiliza a chave primária da
     * classe como atributo para localização do registro a ser alterado.
     * Primeiramente valida os dados (
     * <code>validarDados</code>) do objeto. Verifica a conexão com o banco de
     * dados e a permissão do usuário para realizar esta operacão na entidade.
     * Isto, através da operação
     * <code>alterar</code> da superclasse.
     *
     * @param obj Objeto da classe <code>ConfiguracaoSistemaCRMVO</code> que
     * será alterada no banco de dados.
     * @throws Exception Caso haja problemas de conexão, restrição de acesso ou
     * validação de dados.
     */
    public void alterar(ConfiguracaoSistemaCRMVO obj) throws Exception {
        try {
            con.setAutoCommit(false);
            alterarSemCommit(obj);
            con.commit();
        } catch (Exception e) {
            con.rollback();
            con.setAutoCommit(true);
            throw e;
        } finally {
            con.setAutoCommit(true);
        }
    }

    public void alterarSemCommit(ConfiguracaoSistemaCRMVO obj) throws Exception{

            ConfiguracaoSistemaCRMVO.validarDados(obj);
            alterarCRM(getIdEntidade());
            obj.realizarUpperCaseDados();
            String sql = "UPDATE ConfiguracaoSistemaCRM set remetentePadrao=?, usarRemetentePadraoGeral=?, emailPadrao=?, mailServer=?, login=?, senha=?, abertoSabado=?, "
                    + "abertoDomingo=?, nrFaltaPlanoMensal=?, nrFaltaPlanoTrimestral=?, nrFaltaPlanoAcimaSemestral=?, nrRisco=?, "
                    + "nrDiasParaClientePreveRenovacao=?, nrDiasParaClientePrevePerda=?, conexaoSegura=?, dividirfase = ?, "
                    + "nrDiasAnterioresAgendamento = ?, nrDiasPosterioresAgendamento = ?, nrDiasLimiteAgendamentoFuturo = ?,"
                    + "bloqueartermospam = ?, iniciartls = ?, qtdIndicacoesMes = ?, "
                    + "qtdConversoesVendasMes = ?, nrDiasPosAgendamentoConversaoExAluno = ?, qtdConversoesExAlunosMes = ?, "
                    + "incluircontratosrenovados = ?, considerarProfessorTreinoWeb = ?, batermetatodasacoes = ?, "
                    + "nrdiascontarresultado = ?, portaServer = ?, obrigatorioSeguirOrdemMetas = ?, ordenacaoMetas = ?, "
                    + "remetentepadraomailing = ?, enviarEmailIndividualmente = ?, nrCreditosTreinoRenovar = ?, agendamentoParaMetaConsultor = ?, autorrenovavelEntraRenovacao = ?, apresentarColaboradoresPorTipoColaborador = ?,"
                    + "apresentarColaboradoresInativos = ?, nrDiasParaClientePreveRenovacaoMaiorUmMes = ?, "
                    + "mailingFtpServer = ?, mailingFtpUser = ?, mailingFtpPass = ?, mailingFtpPort = ?, mailingFtpType = ?, mailingFtpFolder = ?, usaSMTPS = ?, usaConfiguracaoEmailManual = ?, tokenBitly = ?, "
                    + "integracaopacto = ?, limitediarioemails = ?, limitemensalpacto = ?, gerarindicacaoparacadastroconvidadosvendasonline = ?, direcionaragendamentosexperimentaisagenda = ? "
                    + "WHERE codigo = ?";
            int i = 1;
        try (PreparedStatement sqlAlterar = con.prepareStatement(sql)) {
            sqlAlterar.setString(i++, obj.getRemetentePadrao());
            sqlAlterar.setBoolean(i++, obj.getUsarRemetentePadraoGeral());
            sqlAlterar.setString(i++, obj.getEmailPadrao());
            sqlAlterar.setString(i++, obj.getMailServer());
            sqlAlterar.setString(i++, obj.getLogin());
            sqlAlterar.setString(i++, obj.getSenha());
            sqlAlterar.setBoolean(i++, obj.isAbertoSabado());
            sqlAlterar.setBoolean(i++, obj.isAbertoDomingo());
            sqlAlterar.setInt(i++, obj.getNrFaltaPlanoMensal());
            sqlAlterar.setInt(i++, obj.getNrFaltaPlanoTrimestral());
            sqlAlterar.setInt(i++, obj.getNrFaltaPlanoAcimaSemestral());
            sqlAlterar.setInt(i++, obj.getNrRisco());
            sqlAlterar.setInt(i++, obj.getNrDiasParaClientePreveRenovacao());
            sqlAlterar.setInt(i++, obj.getNrDiasParaClientePrevePerda());
            sqlAlterar.setBoolean(i++, obj.isConexaoSegura());
            sqlAlterar.setBoolean(i++, obj.getDividirFase());
            sqlAlterar.setInt(i++, obj.getNrDiasAnterioresAgendamento());
            sqlAlterar.setInt(i++, obj.getNrDiasPosterioresAgendamento());
            sqlAlterar.setInt(i++, obj.getNrDiasLimiteAgendamentoFuturo());
            sqlAlterar.setBoolean(i++, obj.getBloquearTermoSpam());
            sqlAlterar.setBoolean(i++, obj.isIniciarTLS());
            sqlAlterar.setInt(i++, obj.getIndicacoesMes());
            sqlAlterar.setInt(i++, obj.getConversaoAgendadosMes());
            sqlAlterar.setInt(i++, obj.getNrDiasPosAgendamentoConversaoExAluno());
            sqlAlterar.setInt(i++, obj.getQtdConversoesExAlunosMes());
            sqlAlterar.setBoolean(i++, obj.isIncluirContratosRenovados());
            sqlAlterar.setBoolean(i++, obj.isConsiderarProfessorTreinoWeb());
            sqlAlterar.setBoolean(i++, obj.isBaterMetaTodasAcoes());
            sqlAlterar.setInt(i++, obj.getNrDiasContarResultado());
            sqlAlterar.setString(i++, obj.getPortaServer());
            sqlAlterar.setBoolean(i++, obj.isObrigatorioSeguirOrdemMetas());
            sqlAlterar.setString(i++, obj.getOrdenacaoMetas());
            if (obj.getRemetentePadraoMailing() != null && obj.getRemetentePadraoMailing().getCodigo() != 0) {
                sqlAlterar.setInt(i++, obj.getRemetentePadraoMailing().getCodigo());
            } else {
                sqlAlterar.setNull(i++, Types.INTEGER);
            }
            sqlAlterar.setBoolean(i++, obj.isEnviarEmailIndividualmente());
            sqlAlterar.setInt(i++, obj.getNrCreditosTreinoRenovar());
            sqlAlterar.setBoolean(i++, obj.isAgendamentoParaMetaConsultor());
            sqlAlterar.setBoolean(i++, obj.isAutorrenovavelEntraRenovacao());
            sqlAlterar.setBoolean(i++, obj.isApresentarColaboradoresPorTipoColaborador());
            sqlAlterar.setBoolean(i++, obj.isApresentarColaboradoresInativos());
            sqlAlterar.setInt(i++, obj.getNrDiasParaClientePreveRenovacaoMaiorUmMes());
            sqlAlterar.setString(i++, obj.getMailingFtpServer());
            sqlAlterar.setString(i++, obj.getMailingFtpUser());
            sqlAlterar.setString(i++, obj.getMailingFtpPass());
            sqlAlterar.setInt(i++, obj.getMailingFtpPort());
            sqlAlterar.setString(i++, obj.getMailingFtpType());
            sqlAlterar.setString(i++, obj.getMailingFtpFolder());
            sqlAlterar.setBoolean(i++, obj.getUsaSMTPS());
            sqlAlterar.setBoolean(i++, obj.getUsaConfiguracaoEmailManual());
            sqlAlterar.setString(i++, obj.getTokenBitly());

            sqlAlterar.setBoolean(i++, obj.getIntegracaoPacto());
            sqlAlterar.setInt(i++, obj.getLimiteDiarioEmails());
            sqlAlterar.setInt(i++, obj.getLimiteMensalPacto());
            sqlAlterar.setBoolean(i++, obj.getGerarIndicacaoParaCadastroConvidadosVendasOnline());
            sqlAlterar.setBoolean(i++, obj.isDirecionaragendamentosexperimentaisagenda());

            sqlAlterar.setInt(i++, obj.getCodigo());
            sqlAlterar.execute();
        }
        getFacade().getConfiguracaoDiasPosVenda().alterarConfiguracaoDiasPosVendas(obj.getCodigo(), obj.getConfiguracaoDiasPosVendaVOs());
            gravarDiasMetas(obj.getConfiguracaoDiasMetasSessoesFinais(), FasesCRMEnum.ULTIMAS_SESSOES);
            gravarDiasMetas(obj.getConfiguracaoDiasMetasSemAgendamento(), FasesCRMEnum.SEM_AGENDAMENTO);
            gravarDiasMetas(obj.getConfiguracaoDiasMetasExAlunos(), FasesCRMEnum.EX_ALUNOS);
            gravarDiasMetas(obj.getConfiguracaoDiasMetasVisitantesAntigos(), FasesCRMEnum.VISITANTES_ANTIGOS);
            gravarDiasMetas(obj.getConfiguracaoDiasMetasUltimoAcessoGymp(), FasesCRMEnum.ALUNO_ULTIMO_ACESSO_GYMPASS);
            gravarTermosSpam(obj);
        }

    /**
     * Operação responsável por excluir no BD um objeto da classe
     * <code>ConfiguracaoSistemaCRMVO</code>. Sempre localiza o registro a ser
     * excluído através da chave primária da entidade. Primeiramente verifica a
     * conexão com o banco de dados e a permissão do usuário para realizar esta
     * operacão na entidade. Isto, através da operação
     * <code>excluir</code> da superclasse.
     *
     * @param obj Objeto da classe <code>ConfiguracaoSistemaCRMVO</code> que
     * será removido no banco de dados.
     * @throws Exception Caso haja problemas de conexão ou restrição de acesso.
     */
    public void excluir(ConfiguracaoSistemaCRMVO obj) throws Exception {
        try {
            con.setAutoCommit(false);
            excluirCRM(getIdEntidade());
            String sql = "DELETE FROM ConfiguracaoSistemaCRM WHERE ((codigo = ?))";
            try (PreparedStatement sqlExcluir = con.prepareStatement(sql)) {
                sqlExcluir.setInt(1, obj.getCodigo());
                sqlExcluir.execute();
            }
            getFacade().getConfiguracaoDiasPosVenda().excluirConfiguracaoDiasPosVendas(obj.getCodigo());
            executarConsulta("DELETE FROM termosspam;", con);
            con.commit();
        } catch (Exception e) {
            con.rollback();
            con.setAutoCommit(true);
            throw e;
        } finally {
            con.setAutoCommit(true);
        }
    }

    /**
     * Responsável por realizar uma consulta de
     * <code>ConfiguracaoSistemaCRM</code> através do valor do atributo
     * <code>Integer codigo</code>. Retorna os objetos com valores iguais ou
     * superiores ao parâmetro fornecido. Faz uso da operação
     * <code>montarDadosConsulta</code> que realiza o trabalho de prerarar o
     * List resultante.
     *
     * @param controlarAcesso Indica se a aplicação deverá verificar se o
     * usuário possui permissão para esta consulta ou não.
     * @return List Contendo vários objetos da
     * classe <code>ConfiguracaoSistemaCRMVO</code> resultantes da consulta.
     * @throws Exception Caso haja problemas de conexão ou restrição de acesso.
     */
    public List consultarPorCodigo(Integer valorConsulta, boolean controlarAcesso, int nivelMontarDados) throws Exception {
        consultarCRM(getIdEntidade(), controlarAcesso);
        String sqlStr = "SELECT * FROM ConfiguracaoSistemaCRM WHERE codigo >= " + valorConsulta + " ORDER BY codigo";
        try (Statement stm = con.createStatement()) {
            try (ResultSet tabelaResultado = stm.executeQuery(sqlStr)) {
                return (montarDadosConsulta(tabelaResultado, nivelMontarDados, con));
            }
        }
    }

    public boolean consultarSeExisteConfiguracaoSistemaCRM() throws Exception {
        String sql = "select exists (select codigo from configuracaosistemacrm limit 1 ) as existe";
        try (PreparedStatement stm = con.prepareStatement(sql)) {
            try (ResultSet tabelaResultado = stm.executeQuery()) {
                tabelaResultado.next();
                return tabelaResultado.getBoolean("existe");
            }
        }
    }

    public void incluirConfiguracoesEmail( ConfiguracaoSistemaCRMVO obj) throws Exception {
        PreparedStatement update = con.prepareStatement("update ConfiguracaoSistemaCRM set mailserver = ?, portaserver = ?, iniciartls = ?, conexaosegura = ?, usasmtps = ?, usaConfiguracaoEmailManual = ?, " +
                "emailpadrao = ?, login = ?, remetentepadrao = ?, usarRemetentePadraoGeral = ?, senha = ? where codigo = 1");

        int i = 0;
        update.setString(++i, obj.getMailServer());
        update.setString(++i, obj.getPortaServer());
        update.setBoolean(++i, obj.isIniciarTLS());
        update.setBoolean(++i, obj.isConexaoSegura());
        update.setBoolean(++i, obj.getUsaSMTPS());
        update.setBoolean(++i, obj.getUsaConfiguracaoEmailManual());
        update.setString(++i, obj.getEmailPadrao());
        update.setString(++i, obj.getLogin());
        update.setString(++i, obj.getRemetentePadrao());
        update.setBoolean(++i, obj.getUsarRemetentePadraoGeral());
        update.setString(++i, obj.getSenha());

        update.execute();
        obj.setCodigo(obterValorChavePrimariaCodigo());
        obj.setNovoObj(false);
    }

    public ConfiguracaoSistemaCRMVO consultarConfiguracaoSistemaCRM(int nivelMontarDados) throws Exception {
        consultarCRM(getIdEntidade(), false);
        String sql = "SELECT * FROM ConfiguracaoSistemaCRM ";
        try (PreparedStatement sqlConsultar = con.prepareStatement(sql)) {
            try (ResultSet tabelaResultado = sqlConsultar.executeQuery()) {
                if (!tabelaResultado.next()) {
                    throw new ConsistirException(
                            "Dados Não Encontrados ( ConfiguracaoSistemaCRM ).");
                }
                return (montarDados(tabelaResultado, nivelMontarDados, con));
            }
        }
    }

    private void gravarTermosSpam(ConfiguracaoSistemaCRMVO cfg) throws Exception {
        executarConsulta("DELETE FROM termospam;", con);
        for (String termo : cfg.getTermosSpam()) {
            executarConsulta("INSERT INTO termospam VALUES ('" + termo + "')", con);
        }
    }

    /**
     * Operação responsável por adicionar um objeto da
     * <code>ConfiguracaoDiasPosVendaVO</code> no Hashtable
     * <code>ConfiguracaoDiasPosVendas</code>. Neste Hashtable são mantidos
     * todos os objetos de ConfiguracaoDiasPosVenda de uma determinada
     * ConfiguracaoSistemaCRM.
     *
     * @param obj Objeto a ser adicionado no Hashtable.
     */
    public void adicionarObjConfiguracaoDiasPosVendas(ConfiguracaoDiasPosVendaVO obj) throws Exception {
        getConfiguracaoDiasPosVendas().put(obj.getNrDia() + "", obj);
        //adicionarObjSubordinadoOC
    }

    /**
     * Operação responsável por remover um objeto da classe
     * <code>ConfiguracaoDiasPosVendaVO</code> do Hashtable
     * <code>ConfiguracaoDiasPosVendas</code>. Neste Hashtable são mantidos
     * todos os objetos de ConfiguracaoDiasPosVenda de uma determinada
     * ConfiguracaoSistemaCRM.
     *
     * @param NrDia Atributo da classe <code>ConfiguracaoDiasPosVendaVO</code>
     * utilizado como apelido (key) no Hashtable.
     */
    public void excluirObjConfiguracaoDiasPosVendas(Integer NrDia) throws Exception {
        getConfiguracaoDiasPosVendas().remove(NrDia + "");
        //excluirObjSubordinadoOC
    }

    /**
     * Operação responsável por localizar um objeto da classe
     * <code>ConfiguracaoSistemaCRMVO</code> através de sua chave primária.
     *
     * @throws Exception Caso haja problemas de conexão ou localização do objeto
     * procurado.
     */
    public ConfiguracaoSistemaCRMVO consultarPorChavePrimaria(Integer codigoPrm, int nivelMontarDados) throws Exception {
        consultarCRM(getIdEntidade(), false);
        String sql = "SELECT * FROM ConfiguracaoSistemaCRM WHERE codigo = ?";
        try (PreparedStatement sqlConsultar = con.prepareStatement(sql)) {
            sqlConsultar.setInt(1, codigoPrm);
            try (ResultSet tabelaResultado = sqlConsultar.executeQuery()) {
                if (!tabelaResultado.next()) {
                    throw new ConsistirException("Dados Não Encontrados ( ConfiguracaoSistemaCRM ).");
                }
                return (montarDados(tabelaResultado, nivelMontarDados, con));
            }
        }
    }

    public List<ConfiguracaoSistemaCRMVO> consultarTodas(int nivelMontarDados) throws Exception{
        try (Statement st = con.createStatement()) {
            try (ResultSet rs = st.executeQuery("select * from ConfiguracaoSistemaCRM ")) {
                return montarDadosConsulta(rs, nivelMontarDados, con);
            }
        }
    }


    public Hashtable getConfiguracaoDiasPosVendas() {
        if (configuracaoDiasPosVendas == null) {
            configuracaoDiasPosVendas = new Hashtable();
        }
        return (configuracaoDiasPosVendas);
    }

    public void setConfiguracaoDiasPosVendas(Hashtable configuracaoDiasPosVendas) {
        this.configuracaoDiasPosVendas = configuracaoDiasPosVendas;
    }

    public String getRemetentePadraoConfiguracaoSistemaCRM() throws Exception {
        String sql = "SELECT remetentepadrao FROM ConfiguracaoSistemaCRM where usarremetentepadraogeral = true";
        try (PreparedStatement sqlConsultar = con.prepareStatement(sql)) {
            try (ResultSet tabelaResultado = sqlConsultar.executeQuery()) {
                if (tabelaResultado.next()) {
                    return tabelaResultado.getString("remetentepadrao");
                }
            }
        }
        return null;
    }

    /**
     * Responsável por consultar os emails de destino para enviar o fechamento
     * de meta do colaborador
     *
     * <AUTHOR> 14/10/2011
     */
    public List<ConfiguracaoEmailFechamentoMetaVO> consultarEmailsEnviarFechamento(EmpresaVO empresaVO) throws Exception {
        StringBuilder sql = new StringBuilder("SELECT conf.codigo, conf.email, conf.empresa, emp.nome "
                + "FROM configuracaoemailfechamentometa conf\n"
                + "LEFT JOIN empresa emp ON conf.empresa = emp.codigo\n");
        if (empresaVO != null) {
            sql.append("WHERE conf.empresa = ").append(empresaVO.getCodigo()).append("\n");
        }
        List<ConfiguracaoEmailFechamentoMetaVO> emails;
        try (ResultSet consulta = ConfiguracaoSistemaCRM.criarConsulta(sql.toString(), con)) {
            emails = new ArrayList<ConfiguracaoEmailFechamentoMetaVO>();
            while (consulta.next()) {
                ConfiguracaoEmailFechamentoMetaVO email = new ConfiguracaoEmailFechamentoMetaVO();
                email.setCodigo(consulta.getInt("codigo"));
                email.setEmail(consulta.getString("email"));
                email.getEmpresa().setCodigo(consulta.getInt("empresa"));
                email.getEmpresa().setNome(consulta.getString("nome"));
                emails.add(email);
            }
        }
        return emails;
    }

    /**
     * Responsável por salvar os emails de destino para enviar o fechamento de
     * meta do colaborador
     *
     * <AUTHOR> 14/10/2011
     */
    public void salvarEmailsFechamento(List<ConfiguracaoEmailFechamentoMetaVO> emails) throws Exception {
        ConfiguracaoSistemaCRM.executarConsulta("DELETE FROM configuracaoemailfechamentometa", con);
        for (ConfiguracaoEmailFechamentoMetaVO email : emails) {
            String sql = "INSERT INTO configuracaoemailfechamentometa (email, empresa) VALUES ('" + email.getEmail() + "', " + email.getEmpresa().getCodigo() + ");";
            ConfiguracaoSistemaCRM.executarConsulta(sql, con);
        }
    }

    /**
     * verifica se a academia foi aberta no dia passado como parametro
     *
     * <AUTHOR> 05/12/2011
     */
    public Boolean verificarDiaAcademiaAberta(Date dia, ConfiguracaoSistemaCRMVO configCRM, EmpresaVO empresa) throws Exception {
        Boolean aberto = Boolean.TRUE;
        if (Uteis.getDiaDaSemana(dia, DiasDaSemana.SABADO) && !configCRM.getAbertoSabado()) {
            aberto = Boolean.FALSE;
        }
        if (Uteis.getDiaDaSemana(dia, DiasDaSemana.DOMINGO) && !configCRM.getAbertoDomingo()) {
            aberto = Boolean.FALSE;
        }
        Feriado feriadoDAO = new Feriado(con);
        if (feriadoDAO.validarFeriadoPorEmpresaParaCalculoAberturaMeta(empresa, dia)) {
            aberto = Boolean.FALSE;
        }
        feriadoDAO = null;
        return aberto;

    }

    /**
     * Joao Alcides 03/01/2012
     *
     * @param dataContrato
     * @param anterior
     * @param nrDias
     * @param empresa
     * @throws Exception
     */
    public Date obterDataCalculadaDiasUteis(Date dataContrato, boolean anterior, int nrDias, EmpresaVO empresa) throws Exception {
        int nrDiasCalcular = 0;
        Date dataCalcular = dataContrato;
        while (nrDiasCalcular < nrDias) {
            if (anterior) {
                dataCalcular = Uteis.obterDataAnterior(dataCalcular, 1);
            } else {
                dataCalcular = Uteis.obterDataFutura2(dataCalcular, 1);
            }

            if (verificarDiaAcademiaAberta(dataCalcular, consultarConfiguracaoSistemaCRM(Uteis.NIVELMONTARDADOS_DADOSBASICOS), empresa)) {
                nrDiasCalcular++;
            }
        }
        return dataCalcular;
    }

    public void gravarDiasMetas(List<ConfiguracaoDiasMetasTO> dias, FasesCRMEnum fase) throws Exception {
        executarConsulta("DELETE FROM configuracaodiasmetas where fase = " + fase.getCodigo(), con);
        String sql = "INSERT INTO configuracaodiasmetas (nrdias, descricao, fase, produto) VALUES (?, ?, ?, ?);";
        for (ConfiguracaoDiasMetasTO cdsa : dias) {
            try (PreparedStatement stm = con.prepareStatement(sql)) {
                stm.setInt(1, cdsa.getNrDia());
                stm.setString(2, cdsa.getDescricao());
                stm.setInt(3, fase.getCodigo());
                if (cdsa.getProduto() != null && cdsa.getProduto().getCodigo() > 0) {
                    stm.setInt(4, cdsa.getProduto().getCodigo());
                } else {
                    stm.setNull(4, 0);
                }
                stm.execute();
            }
        }
    }

    public List<ConfiguracaoDiasMetasTO> consultarConfiguracaoDiasMetasPorFase(FasesCRMEnum fase) throws Exception {
        List<ConfiguracaoDiasMetasTO> dias;
        try (ResultSet consulta = criarConsulta("SELECT cdm.codigo, nrdias, cdm.descricao, fase, prod.codigo as produto, prod.descricao as produtodesc FROM configuracaodiasmetas cdm LEFT JOIN produto prod ON cdm.produto = prod.codigo WHERE fase = " + fase.getCodigo(), con)) {
            dias = new ArrayList<ConfiguracaoDiasMetasTO>();
            while (consulta.next()) {
                if (UteisValidacao.emptyNumber(consulta.getInt("produto"))) {
                    dias.add(new ConfiguracaoDiasMetasTO(consulta.getInt("nrdias"), consulta.getString("descricao")));
                } else {
                    ProdutoVO produto = new ProdutoVO();
                    produto.setCodigo(consulta.getInt("produto"));
                    produto.setDescricao(consulta.getString("produtodesc"));
                    dias.add(new ConfiguracaoDiasMetasTO(consulta.getInt("nrdias"), consulta.getString("descricao"), produto));
                }
            }
        }
        return dias;
    }

    public void alterarLimiteDiarioLimite(Integer limite) throws Exception{
        SuperFacadeJDBC.executarConsulta("update configuracaosistemacrm set limitediarioemails = " + limite, con);
    }

    public void alterarLimiteMensal(Integer limite) throws Exception{
        SuperFacadeJDBC.executarConsulta("update configuracaosistemacrm set limitemensalpacto = " + limite, con);
    }

    public Integer obterLimiteDiario() throws Exception{
        ResultSet rs = SuperFacadeJDBC.criarConsulta("select limitediarioemails from configuracaosistemacrm ", con);
        return rs.next() ? rs.getInt("limitediarioemails") : 0;
    }

    public ConfiguracaoSistemaCRMVO obterConfiguracaoEmailAutomatico(String emailDestino, String nomeEmpresaLogada,
                                                                     ConfiguracaoSistemaCRMVO configuracaoSistemaCRMBase) throws Exception {
        try {
            if (configuracaoSistemaCRMBase == null) {
                throw new Exception("configuracaoSistemaCRMBase não informada");
            }

            if (configuracaoSistemaCRMBase.getIntegracaoPacto() &&
                    configuracaoSistemaCRMBase.getIntegracaoPacto() != null) {
                String msgEnviar = "Este é um teste de email pelo ZillyonWeb.";
                String assunto = "Teste Configuração de Email | " + nomeEmpresaLogada.trim() + " | " + Uteis.getDataComHora(Calendario.hoje());
                UteisEmail uteis = new UteisEmail();
                uteis.novo(assunto, configuracaoSistemaCRMBase);
                uteis.enviarEmailComSendy(new String[]{emailDestino}, msgEnviar, assunto, configuracaoSistemaCRMBase.getRemetentePadrao(), configuracaoSistemaCRMBase.preparaEnvioSendy(),0,0, null, null);
                return configuracaoSistemaCRMBase;
            } else {

                //lista de configurações que serão testadas
                List<ConfiguracaoSistemaCRMVO> listaConfiguracoesTentar = new ArrayList<>();


                //adicionar a configuração do jeito que está para realizar a tentativa de configuracao
                ConfiguracaoSistemaCRMVO configBaseFake = (ConfiguracaoSistemaCRMVO) configuracaoSistemaCRMBase.getClone(true);
                listaConfiguracoesTentar.add(configBaseFake);

                //adicionar tbm com alteraçao de TLS e conexão segura
                adicionarConfiguracao(listaConfiguracoesTentar, configBaseFake, "587");
                adicionarConfiguracao(listaConfiguracoesTentar, configBaseFake, "465");

                if (!configBaseFake.getPortaServer().equalsIgnoreCase("465") &&
                        !configBaseFake.getPortaServer().equalsIgnoreCase("587")) {
                    adicionarConfiguracao(listaConfiguracoesTentar, configBaseFake, configBaseFake.getPortaServer());
                }

                String emailRemetente = configuracaoSistemaCRMBase.getLogin();
                if (configuracaoSistemaCRMBase.getLogin().contains("@")) {
                    try {
                        emailRemetente = configuracaoSistemaCRMBase.getLogin().substring(configuracaoSistemaCRMBase.getLogin().indexOf("@") + 1);
                    } catch (Exception ignored) {
                    }
                } else if (configuracaoSistemaCRMBase.getLogin().contains("=")) {
                    try {
                        emailRemetente = configuracaoSistemaCRMBase.getLogin().substring(configuracaoSistemaCRMBase.getLogin().indexOf("=") + 1);
                    } catch (Exception ignored) {
                    }
                }
                List<ConfiguracaoSistemaCRMVO> lstTentativa = new ArrayList<>();
                if (configuracaoSistemaCRMBase.getUsaConfiguracaoEmailManual()) {
                    ConfiguracaoSistemaCRMVO configFake = (ConfiguracaoSistemaCRMVO) configuracaoSistemaCRMBase.getClone(true);
                    lstTentativa.add(configFake);
                } else {
                    for (ConfiguracaoEmailEnum configEnum : ConfiguracaoEmailEnum.values()) {
                        ConfiguracaoSistemaCRMVO configFake = (ConfiguracaoSistemaCRMVO) configuracaoSistemaCRMBase.getClone(true);
                        configFake.setMailServer(configEnum.getSmtpPadrao().replaceAll("EMAIL_REMETENTE", emailRemetente));
                        if (configuracaoSistemaCRMBase.getMailServer().equals(configEnum.getSmtpPadrao()) ||
                                configuracaoSistemaCRMBase.getMailServer().equals(configEnum.getSmtpPadrao().replaceAll("EMAIL_REMETENTE", emailRemetente))) {
                            lstTentativa.add(configFake);
                        }
                    }
                }

                String retorno = "";
                for (ConfiguracaoSistemaCRMVO configFake : lstTentativa) {
                    retorno = realizarEnvioEmail(emailDestino, nomeEmpresaLogada, configFake);
                    if (retorno.equalsIgnoreCase("ok")) {
                        return configFake;
                    }
                }
                throw new Exception(retorno);
            }
        } catch (Exception ex) {
            ex.printStackTrace();
            throw ex;
        }
    }

    public boolean isGerarIndicacaoParaCadastroConvidadosVendasOnline() throws Exception {
        String sql = "SELECT gerarindicacaoparacadastroconvidadosvendasonline FROM ConfiguracaoSistemaCRM";
        try (PreparedStatement sqlConsultar = con.prepareStatement(sql)) {
            try (ResultSet tabelaResultado = sqlConsultar.executeQuery()) {
                if (tabelaResultado.next()) {
                    return tabelaResultado.getBoolean("gerarindicacaoparacadastroconvidadosvendasonline");
                }
            }
        }
        return false;
    }

    private void adicionarConfiguracao(List<ConfiguracaoSistemaCRMVO> listaConfiguracoesTentar,
                                       ConfiguracaoSistemaCRMVO configBaseFake,
                                       String porta) throws IllegalAccessException, InstantiationException {
        ConfiguracaoSistemaCRMVO configFake = (ConfiguracaoSistemaCRMVO) configBaseFake.getClone(true);
        configFake.setIniciarTLS(false);
        configFake.setConexaoSegura(true);
        configFake.setPortaServer(porta);
        listaConfiguracoesTentar.add(configFake);
        configFake.setIniciarTLS(true);
        configFake.setConexaoSegura(false);
        configFake.setPortaServer(porta);
        listaConfiguracoesTentar.add(configFake);
    }

    private String realizarEnvioEmail(String emailDestino, String nomeEmpresaLogada, ConfiguracaoSistemaCRMVO configuracaoSistemaCRMVO) {
        try {
            String msgEnviar = "Este é um teste de email pelo ZillyonWeb.";
            String assunto = "Teste Configuração de Email | " + nomeEmpresaLogada.trim() + " | " + Uteis.getDataComHora(Calendario.hoje());
            if (!configuracaoSistemaCRMVO.isConfiguracaoEmailValida()) {
                throw new Exception("Não foi possível enviar o e-mail. Verifique as configurações de e-mail no CRM!");
            }
            UteisEmail uteisEmail = new UteisEmail();
            uteisEmail.novo(assunto, configuracaoSistemaCRMVO);
            uteisEmail.timeout_read_write_ms = 10000;
            uteisEmail.enviarEmail(emailDestino, null, msgEnviar, nomeEmpresaLogada, configuracaoSistemaCRMVO.getIntegracaoPacto(), configuracaoSistemaCRMVO.preparaEnvioSendy());
            return "ok";
        } catch (Exception ex) {
            ex.printStackTrace();
            return ex.getMessage();
        }
    }


    public List<ConfiguracaoEmpresaBitrixVO> consultarConfiguracaoEmpresaBitrix24(String chave) throws Exception {
        List<ConfiguracaoEmpresaBitrixVO> config;
        try (ResultSet consulta = criarConsulta("SELECT * FROM configuracaoempresabitrix24 where empresa = '" + chave +"'", con)) {
            config = new ArrayList<ConfiguracaoEmpresaBitrixVO>();
            while (consulta.next()) {
                ConfiguracaoEmpresaBitrixVO item = new ConfiguracaoEmpresaBitrixVO();
                item.setAcaoobjecao(consulta.getInt("acaoobjecao"));
                item.setEmpresa(consulta.getString("empresa"));
                item.setResponsavelPadrao(consulta.getInt("responsavelpadrao"));
                item.setAcao(consulta.getString("acao"));
                item.setHabilitada(consulta.getBoolean("habilitada"));
                item.setUrl(consulta.getString("url"));
                config.add(item);
            }
        }
        return config;
    }

    public Integer obterNrDiasContarResultado() throws Exception{
        ResultSet rs = SuperFacadeJDBC.criarConsulta("select nrDiasContarResultado from configuracaosistemacrm ", con);
        return rs.next() ? rs.getInt("nrDiasContarResultado") : 20;
    }

}

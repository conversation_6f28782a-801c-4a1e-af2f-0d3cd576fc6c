package negocio.facade.jdbc.crm;

import br.com.pactosolucoes.enumeradores.FasesCRMEnum;
import negocio.comuns.crm.TextoPadraoVO;
import negocio.comuns.utilitarias.ConsistirException;
import negocio.comuns.utilitarias.Dominios;
import negocio.comuns.utilitarias.Uteis;
import negocio.facade.jdbc.arquitetura.SuperEntidade;
import negocio.interfaces.crm.TextoPadraoInterfaceFacade;

import javax.faces.model.SelectItem;
import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.sql.Statement;
import java.util.ArrayList;
import java.util.List;

/**
 * Created by glauco on 15/01/14.
 */
public class TextoPadrao extends SuperEntidade implements TextoPadraoInterfaceFacade {

    public TextoPadrao() throws Exception {
    }

    public TextoPadrao(Connection conexao) throws Exception {
        super(conexao);
    }

    public static TextoPadraoVO montarDados(ResultSet dadosSQL, int nivelMontarDados, Connection con) throws Exception {
        TextoPadraoVO obj = new TextoPadraoVO();
        obj.setCodigo(new Integer(dadosSQL.getInt("codigo")));
        obj.setDescricao(dadosSQL.getString("descricao"));
        obj.setFaseCRM(FasesCRMEnum.getFase(dadosSQL.getInt("fasecrm")));
        obj.getEmpresa().setCodigo(dadosSQL.getInt("empresa"));
        obj.setMensagemPadrao(dadosSQL.getString("mensagempadrao"));
        obj.setLinkDocs(dadosSQL.getString("linkdocs"));
        obj.setTipoContato(dadosSQL.getString("tipocontato"));
        obj.setNovoObj(false);
        if (nivelMontarDados == Uteis.NIVELMONTARDADOS_DADOSBASICOS) {
            return obj;
        }
        return obj;
    }

    public static List<TextoPadraoVO> montarDadosConsulta(ResultSet resultSet, int nivelMontarDados, Connection con) throws Exception{
        List<TextoPadraoVO> list = new ArrayList<TextoPadraoVO>();
        while(resultSet.next()){
            list.add(montarDados(resultSet, nivelMontarDados, con));
        }
        return list;
    }

    private ResultSet getRS(Integer empresa) throws SQLException {
        String sql = "SELECT codigo, fasecrm, descricao, tipocontato FROM textopadrao WHERE empresa = " + empresa + " ORDER BY descricao";
        PreparedStatement sqlConsultar = con.prepareStatement(sql);
        return sqlConsultar.executeQuery();
    }

    @Override
    public String consultarJSON(Integer empresa) throws Exception {
        ResultSet rs = getRS(empresa);

        StringBuilder json = new StringBuilder();
        json.append("{\"aaData\":[");
        boolean dados = false;
        while (rs.next()) {
            dados = true;
            json.append("[\"").append(rs.getString("codigo")).append("\",");
            json.append("\"").append(FasesCRMEnum.getFase(rs.getInt("fasecrm")).getDescricao()).append("\",");
            json.append("\"").append(Dominios.getTipoContatoHistoricoContato().get(rs.getString("tipocontato"))).append("\",");
            json.append("\"").append(Uteis.normalizarStringJSON(rs.getString("descricao"))).append("\"],");
        }
        if (dados) {
            json.deleteCharAt(json.toString().length() - 1);
        }
        json.append("]}");
        return json.toString();
    }

    @Override
    public void incluir(TextoPadraoVO textoPadraoVO) throws Exception {
        try {
            String sql = "INSERT INTO textopadrao(descricao, fasecrm, empresa, mensagempadrao, linkdocs, tipocontato) VALUES ( ?, ?, ?, ? , ?, ?)";
            PreparedStatement sqlInserir = con.prepareStatement(sql);
            int i = 0;
            sqlInserir.setString(++i, textoPadraoVO.getDescricao());
            sqlInserir.setDouble(++i, textoPadraoVO.getFaseCRM().getCodigo());
            sqlInserir.setInt(++i, textoPadraoVO.getEmpresa().getCodigo());
            sqlInserir.setString(++i, textoPadraoVO.getMensagemPadrao());
            sqlInserir.setString(++i, textoPadraoVO.getLinkDocs());
            sqlInserir.setString(++i, textoPadraoVO.getTipoContato());
            sqlInserir.execute();
            textoPadraoVO.setCodigo(obterValorChavePrimariaCodigo());
            textoPadraoVO.setNovoObj(false);
        } catch (Exception e) {
            textoPadraoVO.setNovoObj(true);
            throw e;
        }
    }

    @Override
    public void alterar(TextoPadraoVO textoPadraoVO) throws Exception {
        try {
            con.setAutoCommit(false);
            String sql = "UPDATE textopadrao SET descricao=?, fasecrm=?, empresa=?, mensagempadrao=?, linkdocs=?,  tipocontato=? WHERE codigo = ?";
            PreparedStatement sqlAlterar = con.prepareStatement(sql);
            int i = 0;
            sqlAlterar.setString(++i, textoPadraoVO.getDescricao());
            sqlAlterar.setDouble(++i, textoPadraoVO.getFaseCRM().getCodigo());
            sqlAlterar.setInt(++i, textoPadraoVO.getEmpresa().getCodigo());
            sqlAlterar.setString(++i, textoPadraoVO.getMensagemPadrao());
            sqlAlterar.setString(++i, textoPadraoVO.getLinkDocs());
            sqlAlterar.setString(++i, textoPadraoVO.getTipoContato());
            sqlAlterar.setInt(++i, textoPadraoVO.getCodigo());
            sqlAlterar.execute();
            con.commit();
        } catch (Exception e) {
            con.rollback();
            throw e;
        } finally {
            con.setAutoCommit(true);
        }
    }

    public void excluir(TextoPadraoVO textoPadraoVO) throws Exception {
        try {
            con.setAutoCommit(false);
            String sql = "DELETE FROM textopadrao WHERE codigo = ?";
            PreparedStatement sqlExcluir = con.prepareStatement(sql);
            sqlExcluir.setInt(1, textoPadraoVO.getCodigo());
            sqlExcluir.execute();
            con.commit();
        } catch (Exception e) {
            con.rollback();
            throw e;
        } finally {
            con.setAutoCommit(true);
        }
    }

    public List<TextoPadraoVO> consultarTodos(int nivelMontarDados) throws Exception{
        Statement st = con.createStatement();
        ResultSet rs = st.executeQuery("select * from textopadrao");
        return montarDadosConsulta(rs,nivelMontarDados, con);
    }

    public TextoPadraoVO consultarPorChavePrimaria(Integer codigoConsulta, int nivelMontarDados) throws Exception {
        String sql = "SELECT * FROM textopadrao WHERE codigo = ?";
        PreparedStatement sqlConsultar = con.prepareStatement(sql);
        sqlConsultar.setInt(1, codigoConsulta);
        ResultSet tabelaResultado = sqlConsultar.executeQuery();
        if (!tabelaResultado.next()) {
            throw new ConsistirException("Dados Não Encontrados(Texto Padrão).");
        }
        return (montarDados(tabelaResultado, nivelMontarDados, this.con));
    }

    public TextoPadraoVO consultarPorFase(Integer faseCRM, Integer codEmpresa, int nivelMontarDados) throws Exception {
        String sql = "SELECT * FROM textopadrao WHERE fasecrm = ? AND empresa = ?";
        PreparedStatement sqlConsultar = con.prepareStatement(sql);
        sqlConsultar.setInt(1, faseCRM);
        sqlConsultar.setInt(2, codEmpresa);
        ResultSet tabelaResultado = sqlConsultar.executeQuery();
        if (!tabelaResultado.next()) {
            return null;
        }
        return (montarDados(tabelaResultado, nivelMontarDados, this.con));
    }

    public List<TextoPadraoVO> consultarPorFaseETipoContato(Integer faseCRM, String tipoContato, Integer codEmpresa, int nivelMontarDados) throws Exception {
        String sql = "SELECT * FROM textopadrao WHERE fasecrm = ? AND empresa = ? AND tipocontato = ?";
        PreparedStatement sqlConsultar = con.prepareStatement(sql);
        sqlConsultar.setInt(1, faseCRM);
        sqlConsultar.setInt(2, codEmpresa);
        sqlConsultar.setString(3, tipoContato);
        ResultSet tabelaResultado = sqlConsultar.executeQuery();
        return montarDadosConsulta(tabelaResultado, nivelMontarDados, this.con);
    }


    public List<SelectItem> consultarPorFaseVariosParaMontarCombo(Integer faseCRM, Integer codEmpresa, int nivelMontarDados) throws Exception {
        List<SelectItem> vetResultado = new ArrayList<SelectItem>(0);
        try {
            String sql = "SELECT codigo, descricao FROM textopadrao WHERE fasecrm = ? AND empresa = ?";
            PreparedStatement sqlConsultar = con.prepareStatement(sql);
            sqlConsultar.setInt(1, faseCRM);
            sqlConsultar.setInt(2, codEmpresa);
            ResultSet tabelaResultado = sqlConsultar.executeQuery();
            while (tabelaResultado.next()) {
                if (tabelaResultado.isFirst()){
                    vetResultado.add(new SelectItem(0,""));
                }
                vetResultado.add(new SelectItem(tabelaResultado.getInt("codigo"),tabelaResultado.getString("descricao") ));
            }
        }catch (Exception e){
           throw e;
        }
        return vetResultado;
    }

    public List<SelectItem> consultarPorFase(Integer faseCRM, Integer codEmpresa) throws Exception {
        List<SelectItem> vetResultado = new ArrayList<SelectItem>();
        try {
            String sql = "SELECT codigo, descricao FROM textopadrao WHERE fasecrm = ? AND empresa = ?";
            PreparedStatement sqlConsultar = con.prepareStatement(sql);
            sqlConsultar.setInt(1, faseCRM);
            sqlConsultar.setInt(2, codEmpresa);
            ResultSet tabelaResultado = sqlConsultar.executeQuery();
            while (tabelaResultado.next()) {
                vetResultado.add(new SelectItem(tabelaResultado.getInt("codigo"),tabelaResultado.getString("descricao") ));
            }
        }catch (Exception e){
            throw e;
        }
        return vetResultado;
    }
}

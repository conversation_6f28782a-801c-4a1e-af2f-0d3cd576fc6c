package negocio.facade.jdbc.crm.fecharMetaDetalhado;

import br.com.pactosolucoes.comuns.util.Declaracao;
import br.com.pactosolucoes.enumeradores.FasesCRMEnum;
import br.com.pactosolucoes.enumeradores.TipoFaseCRM;
import br.com.pactosolucoes.estrutura.paginacao.ConfPaginacao;
import controle.arquitetura.security.LoginControle;
import negocio.comuns.acesso.AcessoClienteVO;
import negocio.comuns.arquitetura.RiscoVO;
import negocio.comuns.arquitetura.UsuarioVO;
import negocio.comuns.basico.ClienteSituacaoVO;
import negocio.comuns.basico.ClienteVO;
import negocio.comuns.basico.EmpresaVO;
import negocio.comuns.basico.TelefoneVO;
import negocio.comuns.contrato.ContratoVO;
import negocio.comuns.conviteaulaexperimental.ConviteAulaExperimentalVO;
import negocio.comuns.crm.AberturaMetaVO;
import negocio.comuns.crm.AgendaVO;
import negocio.comuns.crm.ConfiguracaoDiasMetasTO;
import negocio.comuns.crm.ConfiguracaoDiasPosVendaVO;
import negocio.comuns.crm.ConfiguracaoSistemaCRMVO;
import negocio.comuns.crm.ConversaoLeadVO;
import negocio.comuns.crm.FasesCRMSQL;
import negocio.comuns.crm.FecharMetaDetalhadoVO;
import negocio.comuns.crm.FecharMetaVO;
import negocio.comuns.crm.HistoricoContatoVO;
import negocio.comuns.crm.IndicadoVO;
import negocio.comuns.crm.MalaDiretaEnviadaVO;
import negocio.comuns.crm.MalaDiretaVO;
import negocio.comuns.crm.ModeloMensagemVO;
import negocio.comuns.crm.PassivoVO;
import negocio.comuns.crm.TipoMetaCRMTO;
import negocio.comuns.utilitarias.Calendario;
import negocio.comuns.utilitarias.ConsistirException;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisValidacao;
import negocio.comuns.utilitarias.UtilReflection;
import negocio.facade.jdbc.acesso.AcessoCliente;
import negocio.facade.jdbc.arquitetura.SuperEntidade;
import negocio.facade.jdbc.arquitetura.consulta.ConsultaUtil;
import negocio.facade.jdbc.basico.Cliente;
import negocio.facade.jdbc.basico.Email;
import negocio.facade.jdbc.basico.Telefone;
import negocio.facade.jdbc.contrato.Contrato;
import negocio.facade.jdbc.crm.Agenda;
import negocio.facade.jdbc.crm.ConfiguracaoDiasPosVenda;
import negocio.facade.jdbc.crm.ConsultasFasesCRM;
import negocio.facade.jdbc.crm.ConversaoLead;
import negocio.facade.jdbc.crm.FecharMeta;
import negocio.facade.jdbc.crm.HistoricoContato;
import negocio.facade.jdbc.crm.Indicado;
import negocio.facade.jdbc.crm.Passivo;
import negocio.interfaces.crm.FecharMetaDetalhadoInterfaceFacade;
import relatorio.controle.sad.ProbabilidadeEvasao;
import relatorio.negocio.comuns.basico.ProbabilidadeEvasaoVO;
import servicos.integracao.enumerador.TipoLeadEnum;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.sql.Statement;
import java.sql.Types;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * Classe de persistência que encapsula todas as operações de manipulação dos
 * dados da classe
 * <code>FecharMetaDetalhadoVO</code>. Responsável por implementar operações
 como incluir, alterar, excluirComLog e consultar pertinentes a classe
 <code>FecharMetaDetalhadoVO</code>. Encapsula toda a interação com o banco de
 * dados.
 *
 * @see FecharMetaDetalhadoVO
 * @see SuperEntidade
 * @see FecharMeta
 */
public class FecharMetaDetalhado extends SuperEntidade implements FecharMetaDetalhadoInterfaceFacade {

    public static final String DETALHAR_COMPARECIMENTOS = "SELECT fm.* FROM fecharmetadetalhado fm "
            + "INNER JOIN agenda a ON a.codigo = fm.codigoorigem "
            + "AND origem LIKE 'AGENDA' AND a.datacomparecimento IS NOT NULL "
            + "WHERE  fecharmeta = ?";
    public static final String DETALHAR_REAGENDADOS = "SELECT fm.* FROM fecharmetadetalhado fm "
            + "INNER JOIN agenda a ON a.codigo = fm.codigoorigem AND fm.origem LIKE 'AGENDA' "
            + "INNER JOIN historicocontato h ON h.agenda = a.codigo AND h.tipooperacao LIKE 'RE' "
            + "WHERE fm.fecharmeta = ? GROUP BY fm.codigo, fm.fecharmeta, fm.obtevesucesso, fm.cliente, fm.passivo,"
            + "fm.indicado, fm.historicocontato, fm.configuracaodiasposvenda,fm.contrato, fm.acessocliente,"
            + "fm.codigoorigem, fm.pesorisco, fm.origem";
    public static final String DETALHAR_FECHAMENTOS = "SELECT fm.* FROM fecharmetadetalhado fm "
            + "INNER JOIN agenda a ON a.codigo = fm.codigoorigem "
            + "AND origem LIKE 'AGENDA' AND a.datacomparecimento IS NOT NULL "
            + "WHERE  fecharmeta = ? AND contrato IS NOT NULL ";

    public FecharMetaDetalhado() throws Exception {
        super();
        setIdEntidade("AberturaMeta");
    }

    public FecharMetaDetalhado(Connection con) throws Exception {
        super(con);
        setIdEntidade("AberturaMeta");
    }

    public FecharMetaDetalhadoVO novo() throws Exception {
        incluirCRM(getIdEntidade());
        return new FecharMetaDetalhadoVO();
    }

    public void incluir(FecharMetaDetalhadoVO obj) throws Exception {
        incluirCRM(getIdEntidade());
        incluirSemCommit(obj);
    }

    public void incluirSemCommit(FecharMetaDetalhadoVO obj) throws Exception {
        FecharMetaDetalhadoVO.validarDados(obj);
        obj.realizarUpperCaseDados();
        String sql = "INSERT INTO FecharMetaDetalhado(fecharMeta, cliente, obteveSucesso, passivo, indicado, "
                + "configuracaoDiasPosVenda, historicoContato, contrato, origem, codigoOrigem, acessoCliente, "
                + "pesoRisco, sessoesfinais, vendaavulsa, diassemagendamento, conviteAulaExperimental, descconfiguracaodiasmetas, observacaoFilaEsperaTurmaCrm) "
                + "VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ? ,?, ?, ?, ?, ?, ?)";
        try (PreparedStatement sqlInserir = con.prepareStatement(sql)) {
            int i = 1;
            if (obj.getFecharMeta().getCodigo() != 0) {
                sqlInserir.setInt(i++, obj.getFecharMeta().getCodigo());
            } else {
                sqlInserir.setNull(i++, 0);
            }
            if (obj.getCliente().getCodigo() != 0) {
                sqlInserir.setInt(i++, obj.getCliente().getCodigo());
            } else {
                sqlInserir.setNull(i++, 0);
            }
            sqlInserir.setBoolean(i++, obj.isObteveSucesso());
            if (obj.getPassivo().getCodigo() != 0) {
                sqlInserir.setInt(i++, obj.getPassivo().getCodigo());
            } else {
                sqlInserir.setNull(i++, 0);
            }
            if (obj.getIndicado().getCodigo() != 0) {
                sqlInserir.setInt(i++, obj.getIndicado().getCodigo());
            } else {
                sqlInserir.setNull(i++, 0);
            }
            if (obj.getConfiguracaoDiasPosVendaVO().getCodigo() != 0) {
                sqlInserir.setInt(i++, obj.getConfiguracaoDiasPosVendaVO().getCodigo());
            } else {
                sqlInserir.setNull(i++, 0);
            }
            if (obj.getHistoricoContatoVO().getCodigo() != 0) {
                sqlInserir.setInt(i++, obj.getHistoricoContatoVO().getCodigo());
            } else {
                sqlInserir.setNull(i++, 0);
            }
            if (obj.getContratoVO().getCodigo() != 0) {
                sqlInserir.setInt(i++, obj.getContratoVO().getCodigo());
            } else {
                sqlInserir.setNull(i++, 0);
            }
            sqlInserir.setString(i++, obj.getOrigem());
            sqlInserir.setInt(i++, obj.getCodigoOrigem());
            if (obj.getAcessoClienteVO().getCodigo() != 0) {
                sqlInserir.setInt(i++, obj.getAcessoClienteVO().getCodigo());
            } else {
                sqlInserir.setNull(i++, 0);
            }
            sqlInserir.setInt(i++, obj.getPesoRisco());
            sqlInserir.setInt(i++, obj.getSessoesFinais());
            sqlInserir.setInt(i++, obj.getVendaAvulsa());
            sqlInserir.setInt(i++, obj.getDiasSemAgendamento());
            if (UtilReflection.objetoMaiorQueZero(obj, "getConviteAulaExperimentalVO().getCodigo()")) {
                sqlInserir.setInt(i++, obj.getConviteAulaExperimentalVO().getCodigo());
            } else {
                sqlInserir.setNull(i++, Types.NULL);
            }

            if (null != obj.getDescconfiguracaodiasmetas() && !obj.getDescconfiguracaodiasmetas().isEmpty()) {
                sqlInserir.setString(i++, obj.getDescconfiguracaodiasmetas());
            } else {
                sqlInserir.setString(i++, "");
            }
            sqlInserir.setString(i++, obj.getObservacaoFilaEsperaTurmaCrm());

            sqlInserir.execute();
            obj.setCodigo(obterValorChavePrimariaCodigo());
            obj.setNovoObj(false);
        } catch (Exception ex) {
            Uteis.logar(ex, this.getClass());
            ex.printStackTrace();
            throw ex;
        }
    }

    public void alterar(FecharMetaDetalhadoVO obj) throws Exception {
        FecharMetaDetalhadoVO.validarDados(obj);
        alterarCRM(getIdEntidade());
        obj.realizarUpperCaseDados();
        String sql = "UPDATE FecharMetaDetalhado set fecharMeta=?, cliente=?, obteveSucesso=?, passivo=?, indicado=?, ConfiguracaoDiasPosVenda=?, historicoContato=?, contrato=?, origem=?, codigoOrigem=?, acessoCliente=?, pesoRisco=?, observacaoFilaEsperaTurmaCrm=? WHERE ((codigo = ?))";
        try (PreparedStatement sqlAlterar = con.prepareStatement(sql)) {
            if (obj.getFecharMeta().getCodigo() != 0) {
                sqlAlterar.setInt(1, obj.getFecharMeta().getCodigo());
            } else {
                sqlAlterar.setNull(1, 0);
            }
            if (obj.getCliente().getCodigo() != 0) {
                sqlAlterar.setInt(2, obj.getCliente().getCodigo());
            } else {
                sqlAlterar.setNull(2, 0);
            }
            sqlAlterar.setBoolean(3, obj.isObteveSucesso());
            if (obj.getPassivo().getCodigo() != 0) {
                sqlAlterar.setInt(4, obj.getPassivo().getCodigo());
            } else {
                sqlAlterar.setNull(4, 0);
            }
            if (obj.getIndicado().getCodigo() != 0) {
                sqlAlterar.setInt(5, obj.getIndicado().getCodigo());
            } else {
                sqlAlterar.setNull(5, 0);
            }
            if (obj.getConfiguracaoDiasPosVendaVO().getCodigo() != 0) {
                sqlAlterar.setInt(6, obj.getConfiguracaoDiasPosVendaVO().getCodigo());
            } else {
                sqlAlterar.setNull(6, 0);
            }
            if (obj.getHistoricoContatoVO().getCodigo() != 0) {
                sqlAlterar.setInt(7, obj.getHistoricoContatoVO().getCodigo());
            } else {
                sqlAlterar.setNull(7, 0);
            }
            if (obj.getContratoVO().getCodigo() != 0) {
                sqlAlterar.setInt(8, obj.getContratoVO().getCodigo());
            } else {
                sqlAlterar.setNull(8, 0);
            }
            sqlAlterar.setString(9, obj.getOrigem());
            sqlAlterar.setInt(10, obj.getCodigoOrigem());
            if (obj.getAcessoClienteVO().getCodigo() != 0) {
                sqlAlterar.setInt(11, obj.getAcessoClienteVO().getCodigo());
            } else {
                sqlAlterar.setNull(11, 0);
            }
            sqlAlterar.setInt(12, obj.getPesoRisco());
            sqlAlterar.setString(13, obj.getObservacaoFilaEsperaTurmaCrm());
            sqlAlterar.setInt(14, obj.getCodigo());
            sqlAlterar.execute();
        }
    }

    /* (non-Javadoc)
     * @see negocio.facade.jdbc.crm.FecharMetaDetalhadoInterfaceFacade#alterarSomenteCamposHistoricoContato(java.lang.Integer, java.lang.Integer)
     */
    public void alterarSomenteCamposHistoricoContato(Integer codHistoricoContato, Integer codigoFecharMetaDetalhado) throws Exception {
        alterarCRM(getIdEntidade());
        String sql = "UPDATE FecharMetaDetalhado set historicoContato=?, teveContato = true WHERE ((codigo = ?))";
        try (PreparedStatement sqlAlterar = con.prepareStatement(sql)) {
            sqlAlterar.setInt(1, codHistoricoContato);
            sqlAlterar.setInt(2, codigoFecharMetaDetalhado);
            sqlAlterar.execute();
        }
    }


    /* (non-Javadoc)
   * @see negocio.facade.jdbc.crm.FecharMetaDetalhadoInterfaceFacade#alterarSomenteCamposHistoricoContato(java.lang.Integer, java.lang.Integer)
   */
    public void alterarSomenteCamposHistoricoContatoObteveSucesso(Integer codHistoricoContato, Integer codigoFecharMetaDetalhado) throws Exception {
        alterarCRM(getIdEntidade());
        String sql = "UPDATE FecharMetaDetalhado set historicoContato=?, teveContato = true WHERE ((codigo = ?)) and obtevesucesso = 't'";
        try (PreparedStatement sqlAlterar = con.prepareStatement(sql)) {
            sqlAlterar.setInt(1, codHistoricoContato);
            sqlAlterar.setInt(2, codigoFecharMetaDetalhado);
            sqlAlterar.execute();
        }
    }

    /* (non-Javadoc)
     * @see negocio.facade.jdbc.crm.FecharMetaDetalhadoInterfaceFacade#alterarSomenteCamposObteveSucessoAndContrato(java.lang.Boolean, java.lang.Integer, java.lang.Integer)
     */
    public void alterarSomenteCamposObteveSucessoAndContrato(Boolean obteveSucesso, Integer contrato, Integer codigoFecharMetaDetalhado, String observacao) throws Exception {
        alterarCRM(getIdEntidade());
        String sql = "UPDATE FecharMetaDetalhado set obtevesucesso=?, contrato=?, observacao=?, teveContato = false WHERE ((codigo = ?))";
        if (obteveSucesso){
            sql = "UPDATE FecharMetaDetalhado set obtevesucesso=?, contrato=?, observacao=?, teveContato = true WHERE ((codigo = ?))";
        }
        try (PreparedStatement sqlAlterar = con.prepareStatement(sql)) {
            sqlAlterar.setBoolean(1, obteveSucesso);
            if (contrato != 0) {
                sqlAlterar.setInt(2, contrato);
            } else {
                sqlAlterar.setNull(2, 0);
            }
            sqlAlterar.setString(3, observacao);
            sqlAlterar.setInt(4, codigoFecharMetaDetalhado);
            sqlAlterar.execute();
        }
    }

    /* (non-Javadoc)
     * @see negocio.facade.jdbc.crm.FecharMetaDetalhadoInterfaceFacade#excluirComLog(negocio.comuns.crm.FecharMetaDetalhadoVO)
     */
    public void excluir(FecharMetaDetalhadoVO obj, Boolean ignorarPermissao) throws Exception {
        if(!ignorarPermissao) {
            excluirCRM(getIdEntidade());
        }
        String sql = "DELETE FROM FecharMetaDetalhado WHERE ((codigo = ?))";
        try (PreparedStatement sqlExcluir = con.prepareStatement(sql)) {
            sqlExcluir.setInt(1, obj.getCodigo());
            sqlExcluir.execute();
        }
    }
    public void excluir(FecharMetaDetalhadoVO obj) throws Exception {
        excluir(obj,false);
    }

    /**
     * Método responsavel por excluirComLog uma MetaDetalhada pelo codigo Indicado, de
 acordo com o colaboradorResponsavel responsavel e pelo Identificador da
 Meta.
     *
     * <AUTHOR>
     */
    public Boolean consultarSeAberturaMetaEstaAbertaPorCodigoIndicadoCodigoColaboradorResponsavelIdentificador(Integer indicado, Integer codigoColaborador, String identificador) throws Exception {
        excluirCRM(getIdEntidade());
        String str = "Select * FROM FecharMetaDetalhado "
                + "inner join FecharMeta on FecharMeta.codigo = FecharMetaDetalhado.fecharMeta and " + "upper( identificadorMeta ) like('" + identificador.toUpperCase() + "%') "
                + "inner join aberturaMeta on aberturaMeta.codigo = FecharMeta.aberturaMeta and aberturaMeta.colaboradorResponsavel =  " + codigoColaborador + " and aberturameta.metaemaberto = true "
                + "WHERE ((indicado = " + indicado + "))";

        try (PreparedStatement sqlConsultar = con.prepareStatement(str)) {
            try (ResultSet tabelaResultado = sqlConsultar.executeQuery()) {
                return tabelaResultado.next();
            }
        }
    }

    /**
     * Método responsavel por excluirComLog uma MetaDetalhada pelo codigo Indicado, de
 acordo com o colaboradorResponsavel responsavel e pelo Identificador da
 Meta.
     *
     * <AUTHOR>
     */
    public void excluirPorCodigoIndicadoCodigoColaboradorResponsavelDataIdentificador(Integer indicado, Integer codigoIndicacao, Integer codigoColaborador, String identificador) throws Exception {
        excluirCRM(getIdEntidade());
        String str = "DELETE FROM FecharMetaDetalhado WHERE ((indicado = " + indicado + ")) and "
                + " FecharMeta =  (SELECT Distinct  (FecharMeta.codigo)  FROM FecharMeta "
                + "inner join aberturaMeta on aberturaMeta.codigo = FecharMeta.aberturaMeta and aberturaMeta.colaboradorResponsavel =   " + codigoColaborador + " "
                + "inner join fecharMetaDetalhado on fecharMetaDetalhado.fecharMeta = fecharMeta.codigo "
                + "inner join indicado on indicado.codigo = fecharMetaDetalhado.indicado "
                + "inner join indicacao on indicacao.codigo = indicado.indicacao and indicacao.codigo =  " + codigoIndicacao + " "
                + "WHERE upper( identificadorMeta ) like('" + identificador + "%') )";
        try (PreparedStatement sql = con.prepareStatement(str)) {
            sql.execute();
        }
    }

    /**
     * Método responsavel por excluirComLog uma MetaDetalhada pelo codigo Indicado, de
 acordo com o colaboradorResponsavel responsavel e pelo Identificador da
 Meta.
     *
     * <AUTHOR>
     */
    public void excluirPorCodigoIndicadoCodigoColaboradorResponsavelDataIdentificador(Integer indicado, Integer codigoColaborador, Date data, String identificador) throws Exception {
        excluirCRM(getIdEntidade());
        String str = "DELETE FROM FecharMetaDetalhado WHERE ((indicado = " + indicado + ")) and "
                + " FecharMeta =  (SELECT FecharMeta.codigo FROM FecharMeta inner join aberturaMeta on aberturaMeta.codigo = FecharMeta.aberturaMeta and aberturaMeta.colaboradorResponsavel =  " + codigoColaborador + " " + "WHERE upper( identificadorMeta ) like('" + identificador.toUpperCase() + "%') " + " and Cast (dataRegistro as Date) = Cast('" + Uteis.getDataJDBC(data) + "' as Date))";
        try (PreparedStatement sql = con.prepareStatement(str)) {
            sql.execute();
        }
    }

    /* (non-Javadoc)
     * @see negocio.facade.jdbc.crm.FecharMetaDetalhadoInterfaceFacade#consultarPorCodigoIndicadoCodigoColaboradorResponsavelDataIdentificador(java.lang.Integer, java.lang.Integer, java.util.Date, java.lang.String, int)
     */
    public FecharMetaDetalhadoVO consultarPorCodigoIndicadoCodigoColaboradorResponsavelDataIdentificador(Integer indicado, Integer codigoColaborador, Date data, String identificador, int nivelMontarDados) throws Exception {
        consultarCRM(getIdEntidade(), false);
        String str = "SELECT * FROM FecharMetaDetalhado WHERE ((indicado = " + indicado + ")) and "
                + " FecharMeta =  (SELECT FecharMeta.codigo FROM FecharMeta inner join aberturaMeta on aberturaMeta.codigo = FecharMeta.aberturaMeta and aberturaMeta.colaboradorResponsavel =  " + codigoColaborador + " " + "WHERE upper( identificadorMeta ) like('" + identificador.toUpperCase() + "%') " + "and Cast (dataRegistro as Date) = Cast('" + Uteis.getDataJDBC(data) + "' as Date))";
        try (PreparedStatement sql = con.prepareStatement(str)) {
            try (ResultSet tabelaResultado = sql.executeQuery()) {
                if (!tabelaResultado.next()) {
                    throw new ConsistirException("Dados Não Encontrados ( FecharMetaDetalhado ).");
                }
                return (montarDados(tabelaResultado, nivelMontarDados, con));
            }
        }
    }
    /* (non-Javadoc)
     * @see negocio.facade.jdbc.crm.FecharMetaDetalhadoInterfaceFacade#excluirPorCodigoPassivoCodigoColaboradorResponsavelDataIdentificador(java.lang.Integer, java.lang.Integer, java.util.Date, java.lang.String)
     */

    public void excluirPorCodigoPassivoCodigoColaboradorResponsavelDataIdentificador(Integer passivo, Integer codigoColaborador, Date data, String identificador) throws Exception {
        excluirCRM(getIdEntidade());
        String str = "DELETE FROM FecharMetaDetalhado WHERE ((Passivo = " + passivo + ")) and "
                + " FecharMeta =  (SELECT FecharMeta.codigo FROM FecharMeta inner join aberturaMeta on aberturaMeta.codigo = FecharMeta.aberturaMeta and aberturaMeta.colaboradorResponsavel =  " + codigoColaborador + " " + "WHERE upper( identificadorMeta ) like('" + identificador.toUpperCase() + "%') " + "and Cast (dataRegistro as Date) = Cast('" + Uteis.getDataJDBC(data) + "' as Date))";
        try (PreparedStatement sql = con.prepareStatement(str)) {
            sql.execute();
        }
    }

    /**
     * Método que exclui do Banco a MetaDetalhada pelo Codigo Passivo
     *
     * <AUTHOR>
     */
    public void excluirFecharMetaDetalhadoPorCodigoPassivo(Integer codPassivo) throws Exception {
        excluirCRM(getIdEntidade());
        String str = "DELETE FROM FecharMetaDetalhado WHERE ((Passivo = " + codPassivo + ")) ";
        try (PreparedStatement sql = con.prepareStatement(str)) {
            sql.execute();
        }
    }

    /**
     * Método responsavel por excluirComLog uma MetaDetalhada pelo codigo Indicado
     *
     * @param codIndicado
     * @throws Exception
     * <AUTHOR>
     */
    public void excluirFecharMetaDetalhadoPorCodigoIndicado(Integer codIndicado) throws Exception {
        excluirCRM(getIdEntidade());
        String str = "DELETE FROM FecharMetaDetalhado WHERE ((indicado = " + codIndicado + ")) ";
        try (PreparedStatement sql = con.prepareStatement(str)) {
            sql.execute();
        }
    }
    /* (non-Javadoc)
     * @see negocio.facade.jdbc.crm.FecharMetaDetalhadoInterfaceFacade#excluirPorCodigoClienteCodigoColaboradorResponsavelDataIdentificador(java.lang.Integer, java.lang.Integer, java.util.Date, java.lang.String)
     */

    public void excluirPorCodigoClienteCodigoColaboradorResponsavelDataIdentificador(Integer cliente, Integer codigoColaborador, Date data, String identificador) throws Exception {
        excluirCRM(getIdEntidade());
        String str = "DELETE FROM FecharMetaDetalhado WHERE ((cliente = " + cliente + ")) and "
                + " FecharMeta =  (SELECT FecharMeta.codigo FROM FecharMeta inner join aberturaMeta on aberturaMeta.codigo = FecharMeta.aberturaMeta and aberturaMeta.colaboradorResponsavel =  " + codigoColaborador + " " + "WHERE upper( identificadorMeta ) like('" + identificador.toUpperCase() + "%') " + "and Cast (dataRegistro as Date) = Cast('" + Uteis.getDataJDBC(data) + "' as Date))";
        try (PreparedStatement sql = con.prepareStatement(str)) {
            sql.execute();
        }
    }

    /* (non-Javadoc)
     * @see negocio.facade.jdbc.crm.FecharMetaDetalhadoInterfaceFacade#consultarPorSituacaoCliente(java.lang.String, int)
     */
    public List consultarPorSituacaoCliente(String valorConsulta, int nivelMontarDados) throws Exception {
        consultarCRM(getIdEntidade(), true);
        String sqlStr = "SELECT FecharMetaDetalhado.* FROM FecharMetaDetalhado, Cliente WHERE FecharMetaDetalhado.cliente = Cliente.codigo and upper( Cliente.situacao ) like('" + valorConsulta.toUpperCase() + "%') ORDER BY Cliente.situacao";
        try (Statement stm = con.createStatement()) {
            try (ResultSet tabelaResultado = stm.executeQuery(sqlStr)) {
                return montarDadosConsulta(tabelaResultado, nivelMontarDados, con);
            }
        }
    }

    /* (non-Javadoc)
     * @see negocio.facade.jdbc.crm.FecharMetaDetalhadoInterfaceFacade#consultarPorCodigoFecharMeta(java.lang.Integer, int)
     */
    public List consultarPorCodigoFecharMeta(Integer valorConsulta, int nivelMontarDados) throws Exception {
        consultarCRM(getIdEntidade(), true);
        String sqlStr = "SELECT * FROM fecharmetadetalhado fmd " +
                "INNER JOIN fecharmeta fm on fm.codigo = fmd.fecharmeta " +
                "WHERE fmd.fecharMeta = " + valorConsulta.intValue() +
                " AND fm.meta <> 0 " +
                "ORDER BY fmd.fecharMeta";
        try (Statement stm = con.createStatement()) {
            try (ResultSet tabelaResultado = stm.executeQuery(sqlStr)) {
                return montarDadosConsulta(tabelaResultado, nivelMontarDados, con);
            }
        }
    }

    /* (non-Javadoc)
     * @see negocio.facade.jdbc.crm.FecharMetaDetalhadoInterfaceFacade#consultarPorCodigo(java.lang.Integer, boolean, int)
     */
    public FecharMetaDetalhadoVO consultarPorCodigo(Integer valorConsulta, boolean controlarAcesso, int nivelMontarDados) throws Exception {
        consultarCRM(getIdEntidade(), controlarAcesso);
        String sqlStr = "SELECT * FROM FecharMetaDetalhado WHERE codigo = " + valorConsulta;
        try (Statement stm = con.createStatement()) {
            try (ResultSet tabelaResultado = stm.executeQuery(sqlStr)) {
                if (tabelaResultado.next()) {
                    return (montarDados(tabelaResultado, nivelMontarDados, con));
                }
                return null;
            }
        }
    }

    public List<FecharMetaDetalhadoVO> consultarPorContrato(int contrato, boolean controlarAcesso, int nivelMontarDados) throws Exception {
        consultarCRM(getIdEntidade(), controlarAcesso);
        String sqlStr = "SELECT * FROM FecharMetaDetalhado WHERE (origem = 'CONTRATO' and codigoorigem = " + contrato + ") or contrato = " + contrato;
        try (Statement stm = con.createStatement()) {
            try (ResultSet tabelaResultado = stm.executeQuery(sqlStr)) {
                return (montarDadosConsulta(tabelaResultado, nivelMontarDados, con));
            }
        }
    }

    public List<FecharMetaDetalhadoVO> consultarPorCliente(int cliente, boolean controlarAcesso, int nivelMontarDados) throws Exception {
        consultarCRM(getIdEntidade(), controlarAcesso);
        String sqlStr = "SELECT * FROM FecharMetaDetalhado WHERE cliente = " + cliente;
        try (Statement stm = con.createStatement()) {
            try (ResultSet tabelaResultado = stm.executeQuery(sqlStr)) {
                return (montarDadosConsulta(tabelaResultado, nivelMontarDados, con));
            }
        }
    }

    public List<FecharMetaDetalhadoVO> consultarPorClienteAberturaMeta(int cliente, AberturaMetaVO aberturaMetaVO, int nivelMontarDados) throws Exception {
        String sqlStr = "SELECT * FROM fecharmetadetalhado fmd\n" +
                "INNER JOIN fecharmeta fm ON fm.codigo = fmd.fecharmeta\n" +
                "INNER JOIN aberturameta am ON am.codigo = fm.aberturameta \n" +
                "WHERE am.codigo = " + aberturaMetaVO.getCodigo() + " and cliente = " + cliente;
        try (Statement stm = con.createStatement()) {
            try (ResultSet tabelaResultado = stm.executeQuery(sqlStr)) {
                return (montarDadosConsulta(tabelaResultado, nivelMontarDados, con));
            }
        }
    }

    public Integer consultarPorCodigoClienteComBaseEmHistoriContato(Integer codigoCliente, boolean controlarAcesso) throws Exception {
        consultarCRM(getIdEntidade(), controlarAcesso);
        String sqlStr = "SELECT max (FecharMetaDetalhado.codigo) FROM FecharMetaDetalhado WHERE historicoContato is null and cliente = " + codigoCliente + ";";
        try (Statement stm = con.createStatement()) {
            try (ResultSet tabelaResultado = stm.executeQuery(sqlStr)) {
                if (!tabelaResultado.next()) {
                    return 0;
                }
                return tabelaResultado.getInt(1);
            }
        }
    }

    public Integer consultarPorCodigoPassivoComBaseEmHistoriContato(Integer codigoPassivo, boolean controlarAcesso) throws Exception {
        consultarCRM(getIdEntidade(), controlarAcesso);
        String sqlStr = "SELECT FecharMetaDetalhado.codigo FROM FecharMetaDetalhado "
                + "WHERE historicoContato in (select MAX(codigo) from historicocontato where passivo = " + codigoPassivo + ") "
                + "ORDER BY codigo";
        try (Statement stm = con.createStatement()) {
            try (ResultSet tabelaResultado = stm.executeQuery(sqlStr)) {
                if (!tabelaResultado.next()) {
                    return (new Integer(0));
                }
                return (new Integer(tabelaResultado.getInt(1)));
            }
        }
    }

    public Integer consultarPorCodigoIndicadoComBaseEmHistoriContato(Integer codigoIndicado, boolean controlarAcesso) throws Exception {
        consultarCRM(getIdEntidade(), controlarAcesso);
        String sqlStr = "SELECT FecharMetaDetalhado.codigo FROM FecharMetaDetalhado "
                + "WHERE historicoContato in (select MAX(codigo) from historicocontato where indicado = " + codigoIndicado + ") "
                + "ORDER BY codigo";
        try (Statement stm = con.createStatement()) {
            try (ResultSet tabelaResultado = stm.executeQuery(sqlStr)) {
                if (!tabelaResultado.next()) {
                    return (new Integer(0));
                }
                return (new Integer(tabelaResultado.getInt(1)));
            }
        }
    }

    public ResultSet consultarClientesPeloCodigoConsultorAndDataLancamentoContrato(Integer codigoColaborador, Date dataLancamento) throws Exception {
        consultarCRM(getIdEntidade(), false);
        String sqlStr = "SELECT contrato.codigo as contrato, contrato.valorFinal as valor, cliente.codigo as cliente FROM contrato "
                + "inner join cliente on cliente.pessoa = contrato.pessoa "
                + "where cast(contrato.datalancamento as date) = '" + Uteis.getDataJDBC(dataLancamento) + "'  and consultor = " + codigoColaborador;
        Statement stm = con.createStatement();
        ResultSet tabelaResultado = stm.executeQuery(sqlStr);
        return tabelaResultado;
    }

    /**
     * Responsável por montar os dados de vários objetos, resultantes de uma
     * consulta ao banco de dados (
     * <code>ResultSet</code>). Faz uso da operação
     * <code>montarDados</code> que realiza o trabalho para um objeto por vez.
     *
     * @return List Contendo vários objetos da classe
     * <code>FecharMetaDetalhadoVO</code> resultantes da consulta.
     */
    public static List<FecharMetaDetalhadoVO> montarDadosConsulta(ResultSet tabelaResultado, int nivelMontarDados, Connection con) throws Exception {
        List<FecharMetaDetalhadoVO> vetResultado = new ArrayList<FecharMetaDetalhadoVO>();
        while (tabelaResultado.next()) {
            vetResultado.add(montarDados(tabelaResultado, nivelMontarDados, con));
        }
        tabelaResultado = null;
        return vetResultado;
    }

    /**
     * Responsável por montar os dados resultantes de uma consulta ao banco de
     * dados (
     * <code>ResultSet</code>) em um objeto da classe
     * <code>FecharMetaDetalhadoVO</code>.
     *
     * @return O objeto da classe <code>FecharMetaDetalhadoVO</code> com os
     * dados devidamente montados.
     */
    public static FecharMetaDetalhadoVO montarDados(ResultSet dadosSQL, int nivelMontarDados, Connection con) throws Exception {
        FecharMetaDetalhadoVO obj = new FecharMetaDetalhadoVO();
        obj.setCodigo(new Integer(dadosSQL.getInt("codigo")));
        obj.getFecharMeta().setCodigo(new Integer(dadosSQL.getInt("fecharMeta")));
        obj.getCliente().setCodigo(new Integer(dadosSQL.getInt("cliente")));
        obj.setObteveSucesso(dadosSQL.getBoolean("obteveSucesso"));
        obj.getPassivo().setCodigo(new Integer(dadosSQL.getInt("passivo")));
        obj.getIndicado().setCodigo(new Integer(dadosSQL.getInt("indicado")));
        obj.getHistoricoContatoVO().setCodigo(new Integer(dadosSQL.getInt("historicoContato")));
        obj.getConfiguracaoDiasPosVendaVO().setCodigo(new Integer(dadosSQL.getInt("configuracaoDiasPosVenda")));
        obj.getContratoVO().setCodigo(new Integer(dadosSQL.getInt("contrato")));
        obj.getAcessoClienteVO().setCodigo(new Integer(dadosSQL.getInt("acessoCliente")));
        obj.setCodigoOrigem(dadosSQL.getInt("codigoOrigem"));
        obj.setOrigem((dadosSQL.getString("origem")));
        obj.setDiasSemAgendamento(dadosSQL.getInt("diassemagendamento"));
        obj.setConviteAulaExperimentalVO(new ConviteAulaExperimentalVO());
        obj.getConviteAulaExperimentalVO().setCodigo(dadosSQL.getInt("conviteAulaExperimental"));

        obj.setDataUltimoContato(new HistoricoContato(con).obterDataUltimoContato(obj, false));
        obj.setPesoRisco(dadosSQL.getInt("pesoRisco"));
        obj.setObservacao(dadosSQL.getString("observacao"));
        obj.setRepescagem(dadosSQL.getBoolean("repescagem"));
        obj.setTeveContato(dadosSQL.getBoolean("teveContato"));
        obj.setNovoObj(false);
        try {
            obj.setDescconfiguracaodiasmetas(dadosSQL.getString("descconfiguracaodiasmetas"));
        }catch (Exception ignored){}
        try {
            obj.setObservacaoFilaEsperaTurmaCrm(dadosSQL.getString("observacaoFilaEsperaTurmaCrm"));
        }catch (Exception ignored){}

        if (nivelMontarDados == Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA) {
            return obj;
        }
        FecharMeta fecharMetaDAO = new FecharMeta(con);


        //NOVO CRM - CRIAR NIVELMONTARDADOS
        if (nivelMontarDados == 1000) {
            montarDadosIndicado(obj, Uteis.NIVELMONTARDADOS_TELACONSULTA, con); //OK
            montarDadosPassivo(obj, Uteis.NIVELMONTARDADOS_DADOSBASICOS, con);
            montarDadosCliente(obj, Uteis.NIVELMONTARDADOS_HISTORICOCLIENTE, con);
            montarDadosFecharMeta(obj, Uteis.NIVELMONTARDADOS_METACOLABORADORRESPONSAVEL, fecharMetaDAO);
            if (obj.getOrigem().equals("AGENDA")) {
                montarDadosAgenda(obj, Uteis.NIVELMONTARDADOS_DADOSBASICOS, con);
            }
            if (obj.getOrigem().equals("CONVERSAOLEAD")) {
                montarDadosLead(obj, Uteis.NIVELMONTARDADOS_DADOSBASICOS,con);
            }
            return obj;
        }

        if (nivelMontarDados == Uteis.NIVELMONTARDADOS_DADOSBASICOS) {
            montarDadosFecharMeta(obj, Uteis.NIVELMONTARDADOS_METACOLABORADORRESPONSAVEL, fecharMetaDAO);
            if (obj.getOrigem().equals("AGENDA")) {
                montarDadosAgenda(obj, Uteis.NIVELMONTARDADOS_INDICERENOVACAO, con);
            }
            return obj;
        }
        if (nivelMontarDados == Uteis.NIVELMONTARDADOS_DADOSENTIDADESPRINCIPAIS) {
            montarDadosFecharMeta(obj, nivelMontarDados, fecharMetaDAO);

            return obj;
        }

        if (nivelMontarDados == Uteis.NIVELMONTARDADOS_HISTORICOCLIENTE) {
            montarDadosCliente(obj, Uteis.NIVELMONTARDADOS_HISTORICOCLIENTE, con);
            montarDadosHistoricoContato(obj, Uteis.NIVELMONTARDADOS_DADOSBASICOS, con);
            try {
                obj.getHistoricoContatoVO().setDiasUltAcesso(dadosSQL.getDate("dthrentrada"));
            } catch (Exception ignored) {

            }

            montarDadosFecharMeta(obj, Uteis.NIVELMONTARDADOS_METACOLABORADORRESPONSAVEL, fecharMetaDAO);
            return obj;
        }

        if (nivelMontarDados == Uteis.NIVELMONTARDADOS_METAPASSIVODETALHADA) {
            montarDadosHistoricoContato(obj, nivelMontarDados, con);
            montarDadosPassivo(obj, nivelMontarDados, con);
            montarDadosFecharMeta(obj, Uteis.NIVELMONTARDADOS_METACOLABORADORRESPONSAVEL, fecharMetaDAO);
            return obj;
        }

        if (nivelMontarDados == Uteis.NIVELMONTARDADOS_METAINDICACAODETALHADA) {
            montarDadosHistoricoContato(obj, Uteis.NIVELMONTARDADOS_METAPASSIVODETALHADA, con);
            montarDadosIndicado(obj, Uteis.NIVELMONTARDADOS_HISTORICOINDICADO, con);
            montarDadosFecharMeta(obj, Uteis.NIVELMONTARDADOS_METACOLABORADORRESPONSAVEL, fecharMetaDAO);
            return obj;
        }

        if (nivelMontarDados == Uteis.NIVELMONTARDADOS_HISTORICOPASSIVO) {
            montarDadosHistoricoContato(obj, nivelMontarDados, con);
            montarDadosPassivo(obj, nivelMontarDados, con);
            montarDadosFecharMeta(obj, Uteis.NIVELMONTARDADOS_METACOLABORADORRESPONSAVEL, fecharMetaDAO);
            return obj;
        }

        if (nivelMontarDados == Uteis.NIVELMONTARDADOS_HISTORICOINDICADO) {
            montarDadosHistoricoContato(obj, nivelMontarDados, con);
            montarDadosIndicado(obj, Uteis.NIVELMONTARDADOS_HISTORICOINDICADO, con);
            montarDadosFecharMeta(obj, Uteis.NIVELMONTARDADOS_METACOLABORADORRESPONSAVEL, fecharMetaDAO);
            return obj;
        }

        if (nivelMontarDados == Uteis.NIVELMONTARDADOS_METAAGENDAMENTODETALHADA) {
            montarDadosIndicado(obj, Uteis.NIVELMONTARDADOS_TELACONSULTA, con);
            montarDadosPassivo(obj, Uteis.NIVELMONTARDADOS_DADOSBASICOS, con);
            montarDadosCliente(obj, Uteis.NIVELMONTARDADOS_HISTORICOCLIENTE, con);
            montarDadosHistoricoContato(obj, Uteis.NIVELMONTARDADOS_DADOSBASICOS, con);
            montarDadosFecharMeta(obj, Uteis.NIVELMONTARDADOS_METACOLABORADORRESPONSAVEL, fecharMetaDAO);
            if (obj.getOrigem().equals("AGENDA")) {
                montarDadosAgenda(obj, Uteis.NIVELMONTARDADOS_INDICERENOVACAO, con);
            }
            if (obj.getOrigem().equals("CONVERSAOLEAD")) {
                montarDadosLead(obj, Uteis.NIVELMONTARDADOS_DADOSBASICOS, con);
            }
            return obj;
        }

        if (nivelMontarDados == Uteis.NIVELMONTARDADOS_INDICERENOVACAO) {
            montarDadosIndicado(obj, nivelMontarDados, con);
            montarDadosPassivo(obj, nivelMontarDados, con);
            montarDadosCliente(obj, Uteis.NIVELMONTARDADOS_HISTORICOCLIENTE, con);
            montarDadosContrato(obj, nivelMontarDados, con);
            montarDadosFecharMeta(obj, Uteis.NIVELMONTARDADOS_METACOLABORADORRESPONSAVEL, fecharMetaDAO);
            montarDadosConfiguracaoDiasPosVendaVO(obj, nivelMontarDados, con);
            montarDadosHistoricoContato(obj, Uteis.NIVELMONTARDADOS_DADOSBASICOS, con);
            if (obj.getOrigem().equals("AGENDA")) {
                montarDadosAgenda(obj, Uteis.NIVELMONTARDADOS_INDICERENOVACAO, con);
            }
            if (obj.getOrigem().equals("CONVERSAOLEAD")) {
                montarDadosLead(obj, Uteis.NIVELMONTARDADOS_DADOSBASICOS,con);
            }

            return obj;
        }
        montarDadosHistoricoContato(obj, nivelMontarDados, con);
        montarDadosIndicado(obj, nivelMontarDados, con);
        montarDadosPassivo(obj, nivelMontarDados, con);
        montarDadosCliente(obj, nivelMontarDados, con);
        montarDadosConfiguracaoDiasPosVendaVO(obj, nivelMontarDados, con);
        montarDadosContrato(obj, nivelMontarDados, con);
        montarDadosFecharMeta(obj, Uteis.NIVELMONTARDADOS_METACOLABORADORRESPONSAVEL, fecharMetaDAO);
        return obj;
    }

    public static void montarDadosFecharMeta(FecharMetaDetalhadoVO obj, int nivelMontarDados, FecharMeta fecharMetaDAO) throws Exception {
        if (obj.getFecharMeta().getCodigo().intValue() == 0) {
            obj.setFecharMeta(new FecharMetaVO());
            return;
        }

        obj.setFecharMeta(fecharMetaDAO.consultarPorChavePrimaria(obj.getFecharMeta().getCodigo().intValue(), nivelMontarDados));
    }

    public static void montarDadosAgenda(FecharMetaDetalhadoVO obj, int nivelMontarDados, Connection con) throws Exception {
        if (obj.getCodigoOrigem().intValue() == 0) {
            obj.setAgenda(new AgendaVO());
            return;
        }
        Agenda agendaDAO = new Agenda(con);
        AgendaVO agenda = agendaDAO.consultarValidandoMeta(obj, nivelMontarDados);
        agendaDAO = null;
        obj.setAgenda(agenda);
    }

    /**
     * Operação responsável por montar os dados de um objeto da classe
     * <code>ClienteVO</code> relacionado ao objeto
     * <code>FecharMetaDetalhadoVO</code>. Faz uso da chave primária da classe
     * <code>ClienteVO</code> para realizar a consulta.
     *
     * @param obj Objeto no qual será montado os dados consultados.
     */
    public static void montarDadosHistoricoContato(FecharMetaDetalhadoVO obj, int nivelMontarDados, Connection con) throws Exception {
        if (obj.getHistoricoContatoVO().getCodigo().intValue() == 0) {
            obj.setHistoricoContatoVO(new HistoricoContatoVO());
            return;
        }
        HistoricoContato historicoContatoDAO = new HistoricoContato(con);
        obj.setHistoricoContatoVO(historicoContatoDAO.consultarPorChavePrimaria(obj.getHistoricoContatoVO().getCodigo(), nivelMontarDados));
    }

    /**
     * Operação responsável por montar os dados de um objeto da classe
     * <code>ClienteVO</code> relacionado ao objeto
     * <code>FecharMetaDetalhadoVO</code>. Faz uso da chave primária da classe
     * <code>ClienteVO</code> para realizar a consulta.
     *
     * @param obj Objeto no qual será montado os dados consultados.
     */
    public static void montarDadosContrato(FecharMetaDetalhadoVO obj, int nivelMontarDados, Connection con) throws Exception {
        if (obj.getContratoVO().getCodigo().intValue() == 0) {
            obj.setContratoVO(new ContratoVO());
            return;
        }
        Contrato contratoDAO = new Contrato(con);
        obj.setContratoVO(contratoDAO.consultarPorChavePrimaria(obj.getContratoVO().getCodigo(), nivelMontarDados));
    }

    /**
     * Operação responsável por montar os dados de um objeto da classe
     * <code>ClienteVO</code> relacionado ao objeto
     * <code>FecharMetaDetalhadoVO</code>. Faz uso da chave primária da classe
     * <code>ClienteVO</code> para realizar a consulta.
     *
     * @param obj Objeto no qual será montado os dados consultados.
     */
    public static void montarDadosAcessoCliente(FecharMetaDetalhadoVO obj, int nivelMontarDados, Connection con) throws Exception {
        if (obj.getAcessoClienteVO().getCodigo().intValue() == 0) {
            obj.setAcessoClienteVO(new AcessoClienteVO());
            return;
        }
        obj.setAcessoClienteVO(new AcessoCliente(con).consultarPorChavePrimaria(obj.getAcessoClienteVO().getCodigo(), nivelMontarDados));
    }

    public static void montarDadosConfiguracaoDiasPosVendaVO(FecharMetaDetalhadoVO obj, int nivelMontarDados, Connection con) throws Exception {
        if (obj.getConfiguracaoDiasPosVendaVO().getCodigo().intValue() == 0) {
            obj.setConfiguracaoDiasPosVendaVO(new ConfiguracaoDiasPosVendaVO());
            return;
        }
        obj.setConfiguracaoDiasPosVendaVO(new ConfiguracaoDiasPosVenda(con).consultarPorChavePrimaria(obj.getConfiguracaoDiasPosVendaVO().getCodigo(), nivelMontarDados));
    }

    /**
     * Operação responsável por montar os dados de um objeto da classe
     * <code>ClienteVO</code> relacionado ao objeto
     * <code>FecharMetaDetalhadoVO</code>. Faz uso da chave primária da classe
     * <code>ClienteVO</code> para realizar a consulta.
     *
     * @param obj Objeto no qual será montado os dados consultados.
     */
    public static void montarDadosCliente(FecharMetaDetalhadoVO obj, int nivelMontarDados, Connection con) throws Exception {
        if (obj.getCliente().getCodigo().intValue() == 0) {
            obj.setCliente(new ClienteVO());
            return;
        }
        Cliente clienteDAO = new Cliente(con);
        Email emailDAO = new Email(con);
        Telefone telefoneDAO = new Telefone(con);
        obj.setCliente(clienteDAO.consultarPorChavePrimaria(obj.getCliente().getCodigo(), nivelMontarDados));
        obj.getCliente().getPessoa().setEmailVOs(emailDAO.consultarEmails(obj.getCliente().getPessoa().getCodigo(), true, Uteis.NIVELMONTARDADOS_DADOSBASICOS));
        obj.getCliente().getPessoa().setTelefoneVOs(telefoneDAO.consultarTelefones(obj.getCliente().getPessoa().getCodigo(), true, Uteis.NIVELMONTARDADOS_DADOSBASICOS));
    }

    public static void montarDadosPassivo(FecharMetaDetalhadoVO obj, int nivelMontarDados, Connection con) throws Exception {
        if (obj.getPassivo().getCodigo().intValue() == 0) {
            obj.setPassivo(new PassivoVO());
            return;
        }
        obj.setPassivo(new Passivo(con).consultarPorChavePrimaria(obj.getPassivo().getCodigo(), nivelMontarDados));
    }

    public static void montarDadosIndicado(FecharMetaDetalhadoVO obj, int nivelMontarDados, Connection con) throws Exception {
        if (obj.getIndicado().getCodigo().intValue() == 0) {
            obj.setIndicado(new IndicadoVO());
            return;
        }
        obj.setIndicado(new Indicado(con).consultarPorChavePrimaria(obj.getIndicado().getCodigo(), nivelMontarDados));
    }

    /* (non-Javadoc)
     * @see negocio.facade.jdbc.crm.FecharMetaDetalhadoInterfaceFacade#excluirFecharMetaDetalhados(java.lang.Integer)
     */
    public void excluirFecharMetaDetalhados(Integer fecharMeta) throws Exception {
        excluirCRM(getIdEntidade());
        String sql = "DELETE FROM FecharMetaDetalhado WHERE (fecharMeta = ?)";
        try (PreparedStatement sqlExcluir = con.prepareStatement(sql)) {
            sqlExcluir.setInt(1, fecharMeta.intValue());
            sqlExcluir.execute();
        }
    }

//	/**
//	 * Operação responsável por alterar todos os objetos da
//	 * <code>FecharMetaDetalhadoVO</code> contidos em um Hashtable no BD. Faz
//	 * uso da operação <code>excluirFecharMetaDetalhados</code> e
//	 * <code>incluirFecharMetaDetalhados</code> disponíveis na classe
//	 * <code>FecharMetaDetalhado</code>.
//	 * 
//	 */
//	public void alterarFecharMetaDetalhados(Integer fecharMeta, List objetos) throws Exception {
//		excluirFecharMetaDetalhados(fecharMeta);
//		incluirFecharMetaDetalhados(fecharMeta, objetos);
//	}

    /* (non-Javadoc)
     * @see negocio.facade.jdbc.crm.FecharMetaDetalhadoInterfaceFacade#incluirFecharMetaDetalhados(java.lang.Integer, java.util.List)
     */
    public void incluirFecharMetaDetalhados(Integer fecharMetaPrm, List objetos) throws Exception {
        for (Object objeto : objetos) {
            FecharMetaDetalhadoVO obj = (FecharMetaDetalhadoVO) objeto;
            obj.getFecharMeta().setCodigo(fecharMetaPrm);
            incluir(obj);
        }
    }

    /* (non-Javadoc)
     * @see negocio.facade.jdbc.crm.FecharMetaDetalhadoInterfaceFacade#consultarFecharMetaDetalhados(java.lang.Integer, int)
     */
    public List<FecharMetaDetalhadoVO> consultarFecharMetaDetalhados(Integer fecharMeta, int nivelMontarDados) throws Exception {
        consultarCRM(getIdEntidade(), false);
        List<FecharMetaDetalhadoVO> objetos = new ArrayList<FecharMetaDetalhadoVO>();
        String sql = "SELECT * FROM FecharMetaDetalhado WHERE fecharMeta = ?";
        try (PreparedStatement sqlConsulta = con.prepareStatement(sql)) {
            sqlConsulta.setInt(1, fecharMeta);
            try (ResultSet resultado = sqlConsulta.executeQuery()) {
                while (resultado.next()) {
                    FecharMetaDetalhadoVO novoObj = FecharMetaDetalhado.montarDados(resultado, nivelMontarDados, con);
                    objetos.add(novoObj);
                }
            }
        }
        return objetos;
    }

    /* (non-Javadoc)
     * @see negocio.facade.jdbc.crm.FecharMetaDetalhadoInterfaceFacade#consultarPorChavePrimaria(java.lang.Integer, int)
     */
    public FecharMetaDetalhadoVO consultarPorChavePrimaria(Integer codigoPrm, int nivelMontarDados) throws Exception {
        consultarCRM(getIdEntidade(), false);
        String sql = "SELECT * FROM FecharMetaDetalhado WHERE codigo = ?";
        try (PreparedStatement sqlConsultar = con.prepareStatement(sql)) {
            sqlConsultar.setInt(1, codigoPrm.intValue());
            try (ResultSet tabelaResultado = sqlConsultar.executeQuery()) {
                if (!tabelaResultado.next()) {
                    throw new ConsistirException("Dados Não Encontrados ( FecharMetaDetalhado ).");
                }
                return (montarDados(tabelaResultado, nivelMontarDados, con));
            }
        }
    }

    /* (non-Javadoc)
     * @see negocio.facade.jdbc.crm.FecharMetaDetalhadoInterfaceFacade#consultarPorCodigoPassivo(java.lang.Integer, int)
     */
    public FecharMetaDetalhadoVO consultarPorCodigoPassivo(Integer codigoPassivo, int nivelMontarDados) throws Exception {
        consultarCRM(getIdEntidade(), false);
        String sql = "SELECT * FROM FecharMetaDetalhado "
                + "inner join fecharmeta on fecharmeta.codigo = fecharmetadetalhado.fecharmeta "
                + "where identificadormeta = 'AG' and passivo = ? ";
        try (PreparedStatement sqlConsultar = con.prepareStatement(sql)) {
            sqlConsultar.setInt(1, codigoPassivo.intValue());
            try (ResultSet tabelaResultado = sqlConsultar.executeQuery()) {
                if (!tabelaResultado.next()) {
                    return null;
                }
                return (montarDados(tabelaResultado, nivelMontarDados, con));
            }
        }
    }

    public FecharMetaDetalhadoVO consultarPorCodigoIndicado(Integer codigoIndicado, int nivelMontarDados) throws Exception {
        consultarCRM(getIdEntidade(), false);
        String sql = "SELECT * FROM FecharMetaDetalhado "
                + "inner join fecharmeta on fecharmeta.codigo = fecharmetadetalhado.fecharmeta "
                + "where identificadormeta = 'IN' and indicado = ? ";
        try (PreparedStatement sqlConsultar = con.prepareStatement(sql)) {
            sqlConsultar.setInt(1, codigoIndicado.intValue());
            try (ResultSet tabelaResultado = sqlConsultar.executeQuery()) {
                if (!tabelaResultado.next()) {
                    return null;
                }
                return (montarDados(tabelaResultado, nivelMontarDados, con));
            }
        }
    }

    /* (non-Javadoc)
     * @see negocio.facade.jdbc.crm.FecharMetaDetalhadoInterfaceFacade#inicializarSqlFecharMetaDetalhadosPassivoPorMes(java.lang.String, negocio.comuns.crm.AberturaMetaVO, int)
     */
    public String inicializarSqlFecharMetaDetalhadosPassivoPorMes(String identificador, AberturaMetaVO obj, int nivelMontarDados) throws Exception {
        consultarCRM(getIdEntidade(), false);
        String sqlP = getFacade().getAberturaMeta().obterColaboradoresMarcados(obj.getGrupoColaboradorListaVenda());
        String sql = "";
        if (sqlP.equals("")) {
            return sql = "select * from fecharMetaDetalhado "
                    + "inner join passivo on passivo.codigo = fecharMetaDetalhado.passivo "
                    + "inner join fecharMeta on fecharMeta.codigo = fecharMetaDetalhado.fecharMeta and fecharMeta.codigo in "
                    + "(SELECT  FecharMeta.codigo FROM FecharMeta "
                    + "inner join aberturameta on aberturameta.codigo = FecharMeta.aberturameta and aberturaMeta.colaboradorResponsavel = " + obj.getColaboradorResponsavel().getCodigo().intValue() + " "
                    + "WHERE upper( identificadorMeta ) like('" + identificador.toUpperCase() + "%') "
                    + "and date_part('month', dataRegistro) = date_part('month', '" + Uteis.getDataJDBC(Calendario.hoje()) + "'::date) "
                    + "and date_part('year', dataRegistro) = date_part('year', '" + Uteis.getDataJDBC(Calendario.hoje()) + "'::date))";

        } else {
            return sql = "select * from fecharMetaDetalhado "
                    + "inner join passivo on passivo.codigo = fecharMetaDetalhado.passivo "
                    + "inner join fecharMeta on fecharMeta.codigo = fecharMetaDetalhado.fecharMeta and fecharMeta.codigo in "
                    + "( " + "SELECT  FecharMeta.codigo FROM FecharMeta "
                    + "inner join aberturameta on aberturameta.codigo = FecharMeta.aberturameta and (" + sqlP + ")"
                    + "WHERE upper( identificadorMeta ) like('" + identificador.toUpperCase() + "%') "
                    + "and date_part('month', dataRegistro) = date_part('month', '" + Uteis.getDataJDBC(Calendario.hoje()) + "'::date) "
                    + "and date_part('year', dataRegistro) = date_part('year', '" + Uteis.getDataJDBC(Calendario.hoje()) + "'::date))";
        }

    }

    /* (non-Javadoc)
     * @see negocio.facade.jdbc.crm.FecharMetaDetalhadoInterfaceFacade#inicializarSqlFecharMetaDetalhadosAgendadosPorMes(java.lang.String, negocio.comuns.crm.AberturaMetaVO, int)
     */
    public String inicializarSqlFecharMetaDetalhadosAgendadosPorMes(String identificador, AberturaMetaVO obj, int nivelMontarDados) throws Exception {
        consultarCRM(getIdEntidade(), false);
        String sqlP = getFacade().getAberturaMeta().obterColaboradoresMarcados(obj.getGrupoColaboradorListaVenda());
        String sql = "";
        if (sqlP.equals("")) {
            return sql = "select * from fecharMetaDetalhado "
                    + "left join passivo on passivo.codigo = fecharMetaDetalhado.passivo "
                    + "left join indicado on indicado.codigo = fecharMetaDetalhado.indicado "
                    + "left join cliente on cliente.codigo = fecharMetaDetalhado.cliente "
                    + "inner join fecharMeta on fecharMeta.codigo = fecharMetaDetalhado.fecharMeta and fecharMeta.codigo in "
                    + "(SELECT  FecharMeta.codigo FROM FecharMeta "
                    + "inner join aberturameta on aberturameta.codigo = FecharMeta.aberturameta "
                    + "and aberturaMeta.colaboradorResponsavel = " + obj.getColaboradorResponsavel().getCodigo().intValue() + " "
                    + "WHERE upper( identificadorMeta ) like('" + identificador.toUpperCase() + "%') "
                    + "and date_part('month', dataRegistro) = date_part('month', '" + Uteis.getDataJDBC(Calendario.hoje()) + "'::date) "
                    + "and date_part('year', dataRegistro) = date_part('year', '" + Uteis.getDataJDBC(Calendario.hoje()) + "'::date))";

        } else {
            return sql = "select * from fecharMetaDetalhado "
                    + "left join passivo on passivo.codigo = fecharMetaDetalhado.passivo "
                    + "left join indicado on indicado.codigo = fecharMetaDetalhado.indicado "
                    + "left join cliente on cliente.codigo = fecharMetaDetalhado.cliente "
                    + "inner join fecharMeta on fecharMeta.codigo = fecharMetaDetalhado.fecharMeta and fecharMeta.codigo in "
                    + "( " + "SELECT  FecharMeta.codigo FROM FecharMeta "
                    + "inner join aberturameta on aberturameta.codigo = FecharMeta.aberturameta and (" + sqlP + ")"
                    + "WHERE upper( identificadorMeta ) like('" + identificador.toUpperCase() + "%') "
                    + "and date_part('month', dataRegistro) = date_part('month', '" + Uteis.getDataJDBC(Calendario.hoje()) + "'::date) "
                    + "and date_part('year', dataRegistro) = date_part('year', '" + Uteis.getDataJDBC(Calendario.hoje()) + "'::date))";
        }
    }

    /* (non-Javadoc)
     * @see negocio.facade.jdbc.crm.FecharMetaDetalhadoInterfaceFacade#inicializarSqlFecharMetaDetalhadosVinteQuatroHorasPorMes(java.lang.String, negocio.comuns.crm.AberturaMetaVO, int)
     */
    public String inicializarSqlFecharMetaDetalhadosVinteQuatroHorasPorMes(String identificador, AberturaMetaVO obj, int nivelMontarDados) throws Exception {
        consultarCRM(getIdEntidade(), false);
        String sqlP = getFacade().getAberturaMeta().obterColaboradoresMarcados(obj.getGrupoColaboradorListaVenda());
        String sql = "";
        if (sqlP.equals("")) {
            return sql = "select distinct fecharMetaDetalhado.* from fecharMetaDetalhado "
                    + "inner join fecharMeta on fecharMeta.codigo = fecharMetaDetalhado.fecharMeta and fecharMeta.codigo in "
                    + "(SELECT  FecharMeta.codigo FROM FecharMeta "
                    + "inner join aberturameta on aberturameta.codigo = FecharMeta.aberturameta and aberturaMeta.colaboradorResponsavel = " + obj.getColaboradorResponsavel().getCodigo().intValue() + " "
                    + "WHERE upper( identificadorMeta ) like('" + identificador.toUpperCase() + "%') "
                    + "and date_part('month', dataRegistro) = date_part('month', '" + Uteis.getDataJDBC(Calendario.hoje()) + "'::date) "
                    + "and date_part('year', dataRegistro) = date_part('year', '" + Uteis.getDataJDBC(Calendario.hoje()) + "'::date)) "
                    + "inner join cliente on cliente.codigo = fecharMetaDetalhado.cliente "
                    + "and (cliente.situacao = 'VI' or cliente.situacao = 'IN')";

        } else {
            return sql = "select distinct fecharMetaDetalhado.* from fecharMetaDetalhado "
                    + "inner join fecharMeta on fecharMeta.codigo = fecharMetaDetalhado.fecharMeta and fecharMeta.codigo in "
                    + "(SELECT  FecharMeta.codigo FROM FecharMeta "
                    + "inner join aberturameta on aberturameta.codigo = FecharMeta.aberturameta and (" + sqlP + ")"
                    + "WHERE upper( identificadorMeta ) like('" + identificador.toUpperCase() + "%') "
                    + "and date_part('month', dataRegistro) = date_part('month', '" + Uteis.getDataJDBC(Calendario.hoje()) + "'::date) "
                    + "and date_part('year', dataRegistro) = date_part('year', '" + Uteis.getDataJDBC(Calendario.hoje()) + "'::date)) "
                    + "inner join cliente on cliente.codigo = fecharMetaDetalhado.cliente "
                    + "and (cliente.situacao = 'VI' or cliente.situacao = 'IN')";
        }
    }

    /* (non-Javadoc)
     * @see negocio.facade.jdbc.crm.FecharMetaDetalhadoInterfaceFacade#inicializarSqlFecharMetaDetalhadosIndicadoPorMes(java.lang.String, negocio.comuns.crm.AberturaMetaVO, int)
     */
    public String inicializarSqlFecharMetaDetalhadosIndicadoPorMes(String identificador, AberturaMetaVO obj, int nivelMontarDados) throws Exception {
        consultarCRM(getIdEntidade(), false);
        String sqlP = getFacade().getAberturaMeta().obterColaboradoresMarcados(obj.getGrupoColaboradorListaVenda());
        String sql = "";
        if (sqlP.equals("")) {
            return sql = "select * from fecharMetaDetalhado "
                    + "inner join indicado on indicado.codigo = fecharMetaDetalhado.indicado "
                    + "inner join fecharMeta on fecharMeta.codigo = fecharMetaDetalhado.fecharMeta and fecharMeta.codigo in "
                    + "(SELECT  FecharMeta.codigo FROM FecharMeta "
                    + "inner join aberturameta on aberturameta.codigo = FecharMeta.aberturameta and aberturaMeta.colaboradorResponsavel = " + obj.getColaboradorResponsavel().getCodigo().intValue() + " "
                    + "WHERE upper( identificadorMeta ) like('" + identificador.toUpperCase() + "%') "
                    + "and date_part('month', dataRegistro) = date_part('month', '" + Uteis.getDataJDBC(Calendario.hoje()) + "'::date) "
                    + "and date_part('year', dataRegistro) = date_part('year', '" + Uteis.getDataJDBC(Calendario.hoje()) + "'::date))";

        } else {
            return sql = "select * from fecharMetaDetalhado "
                    + "inner join indicado on indicado.codigo = fecharMetaDetalhado.indicado "
                    + "inner join fecharMeta on fecharMeta.codigo = fecharMetaDetalhado.fecharMeta and fecharMeta.codigo in "
                    + "( " + "SELECT  FecharMeta.codigo FROM FecharMeta "
                    + "inner join aberturameta on aberturameta.codigo = FecharMeta.aberturameta and (" + sqlP + ")"
                    + "WHERE upper( identificadorMeta ) like('" + identificador.toUpperCase() + "%') "
                    + "and date_part('month', dataRegistro) = date_part('month', '" + Uteis.getDataJDBC(Calendario.hoje()) + "'::date) "
                    + "and date_part('year', dataRegistro) = date_part('year', '" + Uteis.getDataJDBC(Calendario.hoje()) + "'::date))";
        }
    }

    /* (non-Javadoc)
     * @see negocio.facade.jdbc.crm.FecharMetaDetalhadoInterfaceFacade#inicializarSqlPassivoHistorico(java.lang.String, java.lang.String, java.util.Date, java.util.Date, negocio.comuns.crm.AberturaMetaVO, int, java.lang.Boolean)
     */
    public String inicializarSqlPassivoHistorico(String identificador, String nome, Date dataInicio, Date dataTermino, AberturaMetaVO obj, int nivelMontarDados, Boolean isVenda) throws Exception {
        consultarCRM(getIdEntidade(), false);
        String sqlP = "";
        if (isVenda) {
            sqlP = getFacade().getAberturaMeta().obterColaboradoresMarcados(obj.getGrupoColaboradorListaVenda());
        } else {
            sqlP = getFacade().getAberturaMeta().obterColaboradoresMarcados(obj.getGrupoColaboradorListaRetencao());
        }
        String sql = "";
        if (!nome.equals("")) {
            if (sqlP.equals("")) {
                return sql = "select distinct on (fecharMetaDetalhado.codigo) fecharMetaDetalhado.* from fecharMetaDetalhado "
                        + "inner join passivo on passivo.codigo = fecharMetaDetalhado.passivo and upper (passivo.nome) like('" + nome.toUpperCase() + "%') "
                        + "inner join fecharMeta on fecharMeta.codigo = fecharMetaDetalhado.fecharMeta and fecharMeta.codigo in "
                        + "( SELECT  FecharMeta.codigo FROM FecharMeta "
                        + "inner join aberturameta on aberturameta.codigo = FecharMeta.aberturameta and (aberturaMeta.colaboradorResponsavel = " + obj.getColaboradorResponsavel().getCodigo().intValue() + ") "
                        + "WHERE upper( identificadorMeta ) like('" + identificador.toUpperCase() + "%') "
                        + "and dataRegistro >= '" + Uteis.getDataJDBC(dataInicio) + " 00:00:00' and dataRegistro <= '" + Uteis.getDataJDBC(dataTermino) + " 23:59:59')";

            } else {

                return sql = "select distinct on (fecharMetaDetalhado.codigo) fecharMetaDetalhado.* from fecharMetaDetalhado "
                        + "inner join passivo on passivo.codigo = fecharMetaDetalhado.passivo and upper (passivo.nome) like('" + nome.toUpperCase() + "%') "
                        + "inner join fecharMeta on fecharMeta.codigo = fecharMetaDetalhado.fecharMeta and fecharMeta.codigo in "
                        + "( SELECT  FecharMeta.codigo FROM FecharMeta "
                        + "inner join aberturameta on aberturameta.codigo = FecharMeta.aberturameta and (" + sqlP + ")"
                        + "WHERE upper( identificadorMeta ) like('" + identificador.toUpperCase() + "%') "
                        + "and dataRegistro >= '" + Uteis.getDataJDBC(dataInicio) + " 00:00:00' and dataRegistro <= '" + Uteis.getDataJDBC(dataTermino) + " 23:59:59')";
            }
        } else {
            if (sqlP.equals("")) {
                return sql = "select distinct on (fecharMetaDetalhado.codigo) fecharMetaDetalhado.* from fecharMetaDetalhado "
                        + "inner join fecharMeta on fecharMeta.codigo = fecharMetaDetalhado.fecharMeta and fecharMeta.codigo in "
                        + "( SELECT  FecharMeta.codigo FROM FecharMeta  WHERE upper( identificadorMeta ) like('" + identificador.toUpperCase() + "%') "
                        + "and dataRegistro >= '" + Uteis.getDataJDBC(dataInicio) + " 00:00:00' and dataRegistro <= '" + Uteis.getDataJDBC(dataTermino) + " 23:59:59') "
                        + "INNER JOIN aberturameta a ON  a.codigo = fecharMeta.aberturameta AND a.colaboradorresponsavel = " + obj.getColaboradorResponsavel().getCodigo().intValue();

            } else {
                return sql = "select distinct on (fecharMetaDetalhado.codigo) fecharMetaDetalhado.* from fecharMetaDetalhado "
                        + "inner join fecharMeta on fecharMeta.codigo = fecharMetaDetalhado.fecharMeta and fecharMeta.codigo in "
                        + "( SELECT  FecharMeta.codigo FROM FecharMeta  "
                        + "inner join aberturameta on aberturameta.codigo = FecharMeta.aberturameta and (" + sqlP + ")"
                        + "WHERE upper( identificadorMeta ) like('" + identificador.toUpperCase() + "%') "
                        + "and dataRegistro >= '" + Uteis.getDataJDBC(dataInicio) + " 00:00:00' and dataRegistro <= '" + Uteis.getDataJDBC(dataTermino) + " 23:59:59') ";

            }

        }
    }

    /* (non-Javadoc)
     * @see negocio.facade.jdbc.crm.FecharMetaDetalhadoInterfaceFacade#inicializarSqlAgendadosHistorico(java.lang.String, java.lang.String, java.util.Date, java.util.Date, negocio.comuns.crm.AberturaMetaVO, int)
     */
    public String inicializarSqlAgendadosHistorico(String identificador, String nome, Date dataInicio, Date dataTermino, AberturaMetaVO obj, int nivelMontarDados) throws Exception {
        consultarCRM(getIdEntidade(), false);
        String sqlP = getFacade().getAberturaMeta().obterColaboradoresMarcados(obj.getGrupoColaboradorListaVenda());
        String sql = "";
        if (!nome.equals("")) {
            if (sqlP.equals("")) {
                return sql = "select distinct on (fecharMetaDetalhado.codigo) fecharMetaDetalhado.* from fecharMetaDetalhado "
                        + "inner join passivo on passivo.codigo = fecharMetaDetalhado.passivo and upper (passivo.nome) like('" + nome.toUpperCase() + "%') "
                        + "inner join fecharMeta on fecharMeta.codigo = fecharMetaDetalhado.fecharMeta and fecharMeta.codigo in "
                        + "( SELECT  FecharMeta.codigo FROM FecharMeta "
                        + "inner join aberturameta on aberturameta.codigo = FecharMeta.aberturameta and (aberturaMeta.colaboradorResponsavel = " + obj.getColaboradorResponsavel().getCodigo().intValue() + ") "
                        + "WHERE upper( identificadorMeta ) like('" + identificador.toUpperCase() + "%') "
                        + "and dataRegistro >= '" + Uteis.getDataJDBC(dataInicio) + " 00:00:00' and dataRegistro <= '" + Uteis.getDataJDBC(dataTermino) + " 23:59:59') "
                        + "UNION ALL  "
                        + "select distinct on (fecharMetaDetalhado.codigo) fecharMetaDetalhado.* from fecharMetaDetalhado "
                        + "inner join indicado on indicado.codigo = fecharMetaDetalhado.indicado and upper (indicado.nomeIndicado) like('" + nome.toUpperCase() + "%') "
                        + "inner join fecharMeta on fecharMeta.codigo = fecharMetaDetalhado.fecharMeta and fecharMeta.codigo in "
                        + "( SELECT  FecharMeta.codigo FROM FecharMeta WHERE upper( identificadorMeta ) like('" + identificador.toUpperCase() + "%') "
                        + "and dataRegistro >= '" + Uteis.getDataJDBC(dataInicio) + " 00:00:00' and dataRegistro <= '" + Uteis.getDataJDBC(dataTermino) + " 23:59:59') "
                        + "UNION ALL "
                        + "select distinct on (fecharMetaDetalhado.codigo) fecharMetaDetalhado.* from fecharMetaDetalhado "
                        + "inner join cliente on cliente.codigo = fecharMetaDetalhado.cliente "
                        + "inner join pessoa on pessoa.codigo = cliente.pessoa and upper (pessoa.nome) like('" + nome.toUpperCase() + "%') "
                        + "inner join fecharMeta on fecharMeta.codigo = fecharMetaDetalhado.fecharMeta and fecharMeta.codigo in "
                        + "( SELECT  FecharMeta.codigo FROM FecharMeta WHERE upper( identificadorMeta ) like('" + identificador.toUpperCase() + "%') "
                        + "and dataRegistro >= '" + Uteis.getDataJDBC(dataInicio) + " 00:00:00' and dataRegistro <= '" + Uteis.getDataJDBC(dataTermino) + " 23:59:59')";

            } else {

                return sql = "select distinct on (fecharMetaDetalhado.codigo) fecharMetaDetalhado.* from fecharMetaDetalhado "
                        + "inner join passivo on passivo.codigo = fecharMetaDetalhado.passivo and upper (passivo.nome) like('" + nome.toUpperCase() + "%') "
                        + "inner join fecharMeta on fecharMeta.codigo = fecharMetaDetalhado.fecharMeta and fecharMeta.codigo in "
                        + "( SELECT  FecharMeta.codigo FROM FecharMeta WHERE upper( identificadorMeta ) like('" + identificador.toUpperCase() + "%') "
                        + "and dataRegistro >= '" + Uteis.getDataJDBC(dataInicio) + " 00:00:00' and dataRegistro <= '" + Uteis.getDataJDBC(dataTermino) + " 23:59:59') "
                        + "UNION ALL  "
                        + "select distinct on (fecharMetaDetalhado.codigo) fecharMetaDetalhado.* from fecharMetaDetalhado "
                        + "inner join indicado on indicado.codigo = fecharMetaDetalhado.indicado and upper (indicado.nomeIndicado) like('" + nome.toUpperCase() + "%') "
                        + "inner join fecharMeta on fecharMeta.codigo = fecharMetaDetalhado.fecharMeta and fecharMeta.codigo in "
                        + "( SELECT  FecharMeta.codigo FROM FecharMeta WHERE upper( identificadorMeta ) like('" + identificador.toUpperCase() + "%') "
                        + "and dataRegistro >= '" + Uteis.getDataJDBC(dataInicio) + " 00:00:00' and dataRegistro <= '" + Uteis.getDataJDBC(dataTermino) + " 23:59:59') "
                        + "UNION ALL "
                        + "select distinct on (fecharMetaDetalhado.codigo) fecharMetaDetalhado.* from fecharMetaDetalhado "
                        + "inner join cliente on cliente.codigo = fecharMetaDetalhado.cliente "
                        + "inner join pessoa on pessoa.codigo = cliente.pessoa and upper (pessoa.nome) like('" + nome.toUpperCase() + "%') "
                        + "inner join fecharMeta on fecharMeta.codigo = fecharMetaDetalhado.fecharMeta and fecharMeta.codigo in "
                        + "( SELECT  FecharMeta.codigo FROM FecharMeta "
                        + "inner join aberturameta on aberturameta.codigo = FecharMeta.aberturameta and (" + sqlP + ") "
                        + "WHERE upper( identificadorMeta ) like('" + identificador.toUpperCase() + "%') "
                        + "and dataRegistro >= '" + Uteis.getDataJDBC(dataInicio) + " 00:00:00' and dataRegistro <= '" + Uteis.getDataJDBC(dataTermino) + " 23:59:59')";
            }
        } else {
            if (sqlP.equals("")) {
                return sql = "select distinct on (fecharMetaDetalhado.codigo) fecharMetaDetalhado.* from fecharMetaDetalhado "
                        + "inner join fecharMeta on fecharMeta.codigo = fecharMetaDetalhado.fecharMeta and fecharMeta.codigo in "
                        + "( SELECT  FecharMeta.codigo FROM FecharMeta  WHERE upper( identificadorMeta ) like('" + identificador.toUpperCase() + "%') "
                        + "and dataRegistro >= '" + Uteis.getDataJDBC(dataInicio) + " 00:00:00' and dataRegistro <= '" + Uteis.getDataJDBC(dataTermino) + " 23:59:59')"
                        + "INNER JOIN aberturameta a ON  a.codigo = fecharMeta.aberturameta AND a.colaboradorresponsavel = " + obj.getColaboradorResponsavel().getCodigo().intValue();

            } else {
                return sql = "select distinct on (fecharMetaDetalhado.codigo) fecharMetaDetalhado.* from fecharMetaDetalhado "
                        + "inner join fecharMeta on fecharMeta.codigo = fecharMetaDetalhado.fecharMeta and fecharMeta.codigo in "
                        + "( SELECT  FecharMeta.codigo FROM FecharMeta  "
                        + "inner join aberturameta on aberturameta.codigo = FecharMeta.aberturameta and (" + sqlP + ")"
                        + "WHERE upper( identificadorMeta ) like('" + identificador.toUpperCase() + "%') "
                        + "and dataRegistro >= '" + Uteis.getDataJDBC(dataInicio) + " 00:00:00' and dataRegistro <= '" + Uteis.getDataJDBC(dataTermino) + " 23:59:59')";
            }

        }
    }

    /* (non-Javadoc)
     * @see negocio.facade.jdbc.crm.FecharMetaDetalhadoInterfaceFacade#inicializarSqlIndicadoHistorico(java.lang.String, java.lang.String, java.util.Date, java.util.Date, negocio.comuns.crm.AberturaMetaVO, int)
     */
    public String inicializarSqlIndicadoHistorico(String identificador, String nome, Date dataInicio, Date dataTermino, AberturaMetaVO obj, int nivelMontarDados) throws Exception {
        consultarCRM(getIdEntidade(), false);
        String sqlP = getFacade().getAberturaMeta().obterColaboradoresMarcados(obj.getGrupoColaboradorListaVenda());
        String sql = "";
        if (!nome.equals("")) {
            if (sqlP.equals("")) {
                return sql = "select distinct on (fecharMetaDetalhado.codigo) fecharMetaDetalhado.* from fecharMetaDetalhado "
                        + "inner join indicado on indicado.codigo = fecharMetaDetalhado.indicado and upper (indicado.nomeIndicado) like('" + nome.toUpperCase() + "%') "
                        + "inner join fecharMeta on fecharMeta.codigo = fecharMetaDetalhado.fecharMeta and fecharMeta.codigo in "
                        + "( SELECT  FecharMeta.codigo FROM FecharMeta "
                        + "inner join aberturameta on aberturameta.codigo = FecharMeta.aberturameta and (aberturaMeta.colaboradorResponsavel = " + obj.getColaboradorResponsavel().getCodigo().intValue() + ") "
                        + "WHERE upper( identificadorMeta ) like('" + identificador.toUpperCase() + "%') "
                        + "and dataRegistro >= '" + Uteis.getDataJDBC(dataInicio) + " 00:00:00' and dataRegistro <= '" + Uteis.getDataJDBC(dataTermino) + " 23:59:59')";
            } else {
                return sql = "select distinct on (fecharMetaDetalhado.codigo) fecharMetaDetalhado.* from fecharMetaDetalhado "
                        + "inner join indicado on indicado.codigo = fecharMetaDetalhado.indicado and upper (indicado.nomeIndicado) like('" + nome.toUpperCase() + "%') "
                        + "inner join fecharMeta on fecharMeta.codigo = fecharMetaDetalhado.fecharMeta and fecharMeta.codigo in "
                        + "( SELECT  FecharMeta.codigo FROM FecharMeta "
                        + "inner join aberturameta on aberturameta.codigo = FecharMeta.aberturameta and (" + sqlP + ")"
                        + "WHERE upper( identificadorMeta ) like('" + identificador.toUpperCase() + "%') "
                        + "and dataRegistro >= '" + Uteis.getDataJDBC(dataInicio) + " 00:00:00' and dataRegistro <= '" + Uteis.getDataJDBC(dataTermino) + " 23:59:59')";
            }
        } else {
            if (sqlP.equals("")) {
                return sql = "select distinct on (fecharMetaDetalhado.codigo) fecharMetaDetalhado.* from fecharMetaDetalhado "
                        + "inner join fecharMeta on fecharMeta.codigo = fecharMetaDetalhado.fecharMeta and fecharMeta.codigo in "
                        + "( SELECT  FecharMeta.codigo FROM FecharMeta  WHERE upper( identificadorMeta ) like('" + identificador.toUpperCase() + "%') "
                        + "and dataRegistro >= '" + Uteis.getDataJDBC(dataInicio) + " 00:00:00' and dataRegistro <= '" + Uteis.getDataJDBC(dataTermino) + " 23:59:59')"
                        + "INNER JOIN aberturameta a ON  a.codigo = fecharMeta.aberturameta AND a.colaboradorresponsavel = " + obj.getColaboradorResponsavel().getCodigo().intValue();
            } else {
                return sql = "select distinct on (fecharMetaDetalhado.codigo) fecharMetaDetalhado.* from fecharMetaDetalhado "
                        + "inner join fecharMeta on fecharMeta.codigo = fecharMetaDetalhado.fecharMeta and fecharMeta.codigo in "
                        + "( SELECT  FecharMeta.codigo FROM FecharMeta  "
                        + "inner join aberturameta on aberturameta.codigo = FecharMeta.aberturameta and (" + sqlP + ")"
                        + "WHERE upper( identificadorMeta ) like('" + identificador.toUpperCase() + "%') "
                        + "and dataRegistro >= '" + Uteis.getDataJDBC(dataInicio) + " 00:00:00' and dataRegistro <= '" + Uteis.getDataJDBC(dataTermino) + " 23:59:59')";
            }
        }
    }

    /* (non-Javadoc)
     * @see negocio.facade.jdbc.crm.FecharMetaDetalhadoInterfaceFacade#consultarFecharMetaDetalhadosHistorico(java.lang.String, java.lang.String, java.util.Date, java.util.Date, negocio.comuns.crm.AberturaMetaVO, int, java.lang.Boolean)
     */
    public List consultarFecharMetaDetalhadosHistorico(String identificador, String nome, Date dataInicio, Date dataTermino, AberturaMetaVO obj, int nivelMontarDados, Boolean isVenda) throws Exception {
        consultarCRM(getIdEntidade(), false);
        String sqlP = "";
        if (isVenda) {
            sqlP = getFacade().getAberturaMeta().obterColaboradoresMarcados(obj.getGrupoColaboradorListaVenda());
        } else {
            sqlP = getFacade().getAberturaMeta().obterColaboradoresMarcados(obj.getGrupoColaboradorListaRetencao());
        }
        String sql = "";
        if (identificador.equals("CP")) {
            sql = inicializarSqlPassivoHistorico(identificador, nome, dataInicio, dataTermino, obj, nivelMontarDados, isVenda);
        } else if (identificador.equals("IN")) {
            sql = inicializarSqlIndicadoHistorico(identificador, nome, dataInicio, dataTermino, obj, nivelMontarDados);
        } else if (identificador.equals("AG")) {
            sql = inicializarSqlAgendadosHistorico(identificador, nome, dataInicio, dataTermino, obj, nivelMontarDados);
        } else {
            if (!nome.equals("")) {
                if (sqlP.equals("")) {
                    sql = "select distinct on (fecharMetaDetalhado.codigo) fecharMetaDetalhado.* from fecharMetaDetalhado "
                            + "inner join cliente on cliente.codigo = fecharMetaDetalhado.cliente "
                            + "inner join pessoa on pessoa.codigo = cliente.pessoa and upper (pessoa.nome) like('" + nome.toUpperCase() + "%') "
                            + "inner join fecharMeta on fecharMeta.codigo = fecharMetaDetalhado.fecharMeta and fecharMeta.codigo in "
                            + "( SELECT  FecharMeta.codigo FROM FecharMeta "
                            + "inner join aberturameta on aberturameta.codigo = FecharMeta.aberturameta and (aberturaMeta.colaboradorResponsavel = " + obj.getColaboradorResponsavel().getCodigo().intValue() + ") "
                            + "WHERE upper( identificadorMeta ) like('" + identificador.toUpperCase() + "%') "
                            + "and dataRegistro >= '" + Uteis.getDataJDBC(dataInicio) + " 00:00:00' and dataRegistro <= '" + Uteis.getDataJDBC(dataTermino) + " 23:59:59') "
                            + "AND cliente.empresa = " + obj.getEmpresaVO().getCodigo();

                } else {
                    sql = "select distinct on (fecharMetaDetalhado.codigo) fecharMetaDetalhado.* from fecharMetaDetalhado "
                            + "inner join cliente on cliente.codigo = fecharMetaDetalhado.cliente "
                            + "inner join pessoa on pessoa.codigo = cliente.pessoa and upper (pessoa.nome) like('" + nome.toUpperCase() + "%') "
                            + "inner join fecharMeta on fecharMeta.codigo = fecharMetaDetalhado.fecharMeta and fecharMeta.codigo in "
                            + "( SELECT  FecharMeta.codigo FROM FecharMeta "
                            + "inner join aberturameta on aberturameta.codigo = FecharMeta.aberturameta and (" + sqlP + ") "
                            + "WHERE upper( identificadorMeta ) like('" + identificador.toUpperCase() + "%') "
                            + "and dataRegistro >= '" + Uteis.getDataJDBC(dataInicio) + " 00:00:00' and dataRegistro <= '" + Uteis.getDataJDBC(dataTermino) + " 23:59:59') "
                            + "AND cliente.empresa = " + obj.getEmpresaVO().getCodigo();
                }
            } else {
                if (sqlP.equals("")) {
                    sql = "select distinct on (fecharMetaDetalhado.codigo) fecharMetaDetalhado.* from fecharMetaDetalhado "
                            + "inner join fecharMeta on fecharMeta.codigo = fecharMetaDetalhado.fecharMeta and fecharMeta.codigo in "
                            + "( SELECT  FecharMeta.codigo FROM FecharMeta  WHERE upper( identificadorMeta ) like('" + identificador.toUpperCase() + "%') "
                            + "and dataRegistro >= '" + Uteis.getDataJDBC(dataInicio) + " 00:00:00' and dataRegistro <= '" + Uteis.getDataJDBC(dataTermino) + " 23:59:59') "
                            + " INNER JOIN aberturameta a ON  a.codigo = fecharMeta.aberturameta AND a.empresa = " + obj.getEmpresaVO().getCodigo()
                            + " and (a.colaboradorResponsavel = " + obj.getColaboradorResponsavel().getCodigo().intValue() + ")";

                } else {
                    sql = "select distinct on (fecharMetaDetalhado.codigo) fecharMetaDetalhado.* from fecharMetaDetalhado "
                            + "inner join fecharMeta on fecharMeta.codigo = fecharMetaDetalhado.fecharMeta and fecharMeta.codigo in "
                            + "( SELECT  FecharMeta.codigo FROM FecharMeta  "
                            + "inner join aberturameta on aberturameta.codigo = FecharMeta.aberturameta and (" + sqlP + ")"
                            + "WHERE upper( identificadorMeta ) like('" + identificador.toUpperCase() + "%') "
                            + "and dataRegistro >= '" + Uteis.getDataJDBC(dataInicio) + " 00:00:00' and dataRegistro <= '" + Uteis.getDataJDBC(dataTermino) + " 23:59:59') "
                            + "INNER JOIN aberturameta a ON  a.codigo = fecharMeta.aberturameta AND a.colaboradorresponsavel = " + obj.getColaboradorResponsavel().getCodigo().intValue()
                            + " AND a.empresa = " + obj.getEmpresaVO().getCodigo();
                }
            }
        }
        try (Statement sqlConsulta = con.createStatement()) {
            try (ResultSet resultado = sqlConsulta.executeQuery(sql)) {
                return (montarDadosConsulta(resultado, nivelMontarDados, con));
            }
        }
    }

    //METODOS QUE CONSULTAM O FECHARMETADETALHADA (INDICADOR DE VENDAS E INDICADOR DE RETENÇÃO)
    /* (non-Javadoc)
     * @see negocio.facade.jdbc.crm.FecharMetaDetalhadoInterfaceFacade#consultarFecharMetaDetalhados(java.util.Date, java.lang.String, java.lang.String, int)
     */
    public List consultarFecharMetaDetalhados(Date dia, Integer codResponsavel, String sqlColaborador, String identificador, int nivelMontarDados, Integer empresa) throws Exception {
        consultarCRM(getIdEntidade(), false);
        String sql = "";
        if (sqlColaborador.equals("")) {
            sql = "select FecharMetaDetalhado.*, ac.dthrentrada from fecharMetaDetalhado "
                    + "inner join fecharMeta on fecharMeta.codigo = fecharMetaDetalhado.fecharMeta and fecharMeta.codigo in " + "( "
                    + "SELECT  FecharMeta.codigo FROM FecharMeta "
                    + "inner join aberturameta on aberturameta.codigo = FecharMeta.aberturameta and aberturameta.colaboradorResponsavel = "
                    + codResponsavel + " AND aberturameta.empresa = " + empresa + " WHERE upper( identificadorMeta ) like('" + identificador.toUpperCase() + "%') "
                    + "and Cast (dataRegistro as Date) = Cast('" + Uteis.getDataJDBC(dia) + "' as Date))"
                    + "left join cliente cli on  cliente = cli.codigo\n"
                    + "left join acessocliente ac on cli.uacodigo = ac.codigo;";

        } else {
            sql = "select FecharMetaDetalhado.*, ac.dthrentrada from fecharMetaDetalhado "
                    + "inner join fecharMeta on fecharMeta.codigo = fecharMetaDetalhado.fecharMeta and fecharMeta.codigo in "
                    + "( " + "SELECT  FecharMeta.codigo FROM FecharMeta " + "inner join aberturameta on aberturameta.codigo = FecharMeta.aberturameta and ("
                    + sqlColaborador + ") AND aberturameta.empresa = " + empresa + " WHERE upper( identificadorMeta ) like('" + identificador.toUpperCase() + "%') "
                    + "and Cast (dataRegistro as Date) = Cast('" + Uteis.getDataJDBC(dia) + "' as Date))"
                    + "left join cliente cli on  cliente = cli.codigo\n"
                    + "left join acessocliente ac on cli.uacodigo = ac.codigo;";
        }
        try (Statement sqlConsulta = con.createStatement()) {
            try (ResultSet resultado = sqlConsulta.executeQuery(sql)) {
                return (montarDadosConsulta(resultado, nivelMontarDados, con));
            }
        }
    }

    // Regras de negocio para AberturaMeta

    /* (non-Javadoc)
     * @see negocio.facade.jdbc.crm.FecharMetaDetalhadoInterfaceFacade#consultarFecharMetaDetalhado(negocio.comuns.crm.AberturaMetaVO, java.lang.String, java.lang.Boolean)
     */
    public List consultarFecharMetaDetalhado(AberturaMetaVO obj, String identificador, Boolean isVenda, Integer empresa) throws Exception {
        if (UteisValidacao.emptyNumber(obj.getEmpresaVO().getCodigo())) {
            obj.getEmpresaVO().setCodigo(empresa);
        }
        String sqlP = "";
        if (isVenda) {
            sqlP = getFacade().getAberturaMeta().obterColaboradoresMarcados(obj.getGrupoColaboradorListaVenda());
        } else {
            sqlP = getFacade().getAberturaMeta().obterColaboradoresMarcados(obj.getGrupoColaboradorListaRetencao());
        }
        if (identificador.equals("CP")) {
            if (!sqlP.equals("")) {
                return consultarFecharMetaDetalhados(obj.getDia(), 0, sqlP, "CP", Uteis.NIVELMONTARDADOS_METAPASSIVODETALHADA, obj.getEmpresaVO().getCodigo());
            } else {
                return consultarFecharMetaDetalhados(obj.getDia(), obj.getColaboradorResponsavel().getCodigo(), "", "CP", Uteis.NIVELMONTARDADOS_METAPASSIVODETALHADA, obj.getEmpresaVO().getCodigo());
            }
        }
        if (identificador.equals("IN")) {
            if (!sqlP.equals("")) {
                return consultarFecharMetaDetalhados(obj.getDia(), 0, sqlP, "IN", Uteis.NIVELMONTARDADOS_METAINDICACAODETALHADA, obj.getEmpresaVO().getCodigo());
            } else {
                return consultarFecharMetaDetalhados(obj.getDia(), obj.getColaboradorResponsavel().getCodigo(), "", "IN", Uteis.NIVELMONTARDADOS_METAINDICACAODETALHADA, obj.getEmpresaVO().getCodigo());
            }
        }
        if (identificador.equals(FasesCRMEnum.AGENDAMENTO.getSigla())
                || identificador.equals(FasesCRMEnum.CONVERSAO_AGENDADOS.getSigla())
                || identificador.equals(FasesCRMEnum.LIGACAO_AGENDADOS_AMANHA.getSigla())) {
            if (!sqlP.equals("")) {
                return consultarFecharMetaDetalhados(obj.getDia(), 0, sqlP, identificador, Uteis.NIVELMONTARDADOS_METAAGENDAMENTODETALHADA, obj.getEmpresaVO().getCodigo());
            } else {
                return consultarFecharMetaDetalhados(obj.getDia(), obj.getColaboradorResponsavel().getCodigo(), "", identificador, Uteis.NIVELMONTARDADOS_METAAGENDAMENTODETALHADA, obj.getEmpresaVO().getCodigo());
            }
        }
        if (identificador.equals("PV") || identificador.equals("MF")) {
            if (!sqlP.equals("")) {
                return consultarFecharMetaDetalhados(obj.getDia(), 0, sqlP, identificador, Uteis.NIVELMONTARDADOS_INDICERENOVACAO, obj.getEmpresaVO().getCodigo());
            } else {
                return consultarFecharMetaDetalhados(obj.getDia(), obj.getColaboradorResponsavel().getCodigo(), "", identificador, Uteis.NIVELMONTARDADOS_INDICERENOVACAO, obj.getEmpresaVO().getCodigo());
            }
        }
        if (!sqlP.equals("")) {
            return consultarFecharMetaDetalhados(obj.getDia(), 0, sqlP, identificador, Uteis.NIVELMONTARDADOS_HISTORICOCLIENTE, obj.getEmpresaVO().getCodigo());
        } else {
            return consultarFecharMetaDetalhados(obj.getDia(), obj.getColaboradorResponsavel().getCodigo(), "", identificador, Uteis.NIVELMONTARDADOS_HISTORICOCLIENTE, obj.getEmpresaVO().getCodigo());
        }
    }

    // ************* Métodos que montam fecharMetaDetalhado **************

    /* (non-Javadoc)
     * @see negocio.facade.jdbc.crm.FecharMetaDetalhadoInterfaceFacade#consultarClientesAniversariantesBuscandoCodigo(negocio.comuns.crm.FecharMetaVO, java.util.Date, java.lang.Integer, int)
     */
    public void consultarClientesAniversariantesBuscandoCodigo(FecharMetaVO obj, Date data, Integer codColaborador, int nivelMontarDados, Integer empresa, String tipoVinculos, String codigoClientesObjecoes) throws Exception {
        consultarCRM(getIdEntidade(), false);
        StringBuilder sql = new StringBuilder();
        sql.append("SELECT distinct(cliente.codigo) as cliente , 'CLIENTE' as origem,  cliente.codigo as codigoOrigem  from pessoa inner join cliente on cliente.pessoa = pessoa.codigo  \n");
        sql.append("inner join vinculo on vinculo.cliente = cliente.codigo and vinculo.colaborador = " + codColaborador.intValue() + " \n");
        if (!tipoVinculos.equals("TODOS")) {
            sql.append(" and vinculo.tipovinculo in (").append(tipoVinculos).append(") ");
        }
        sql.append("WHERE  \n");
        sql.append(" (date_part('month', dataNasc) = date_part('month', timestamp '" + Uteis.getDataJDBC(data) + "') \n");
        sql.append("AND date_part('day', dataNasc)  = date_part('day', timestamp '" + Uteis.getDataJDBC(data) + "')) \n");
        sql.append("AND cliente.empresa = " + empresa);
        sql.append(" AND cliente.codigo not in (").append(codigoClientesObjecoes).append(")");
        try (Statement sqlConsultar = con.createStatement()) {
            try (ResultSet tabelaResultado = sqlConsultar.executeQuery(sql.toString())) {
                montarFecharMetaDetalhado(tabelaResultado, obj, 0, nivelMontarDados);
            }
        }
    }

    /* (non-Javadoc)
     * @see negocio.facade.jdbc.crm.FecharMetaDetalhadoInterfaceFacade#consultarClientesVinteQuatroHorasBuscandoCodigo(negocio.comuns.crm.FecharMetaVO, java.util.Date, java.lang.Integer, int)
     */
    public ResultSet consultarClientesVinteQuatroHorasBuscandoCodigo(FecharMetaVO obj, Date dia, Integer codColaborador, int nivelMontarDados, Integer empresa, String tipoVinculos, String codigoClientesObjecoes) throws Exception {
        consultarCRM(getIdEntidade(), false);
        StringBuilder sql = new StringBuilder();
        sql.append("SELECT\n");
        sql.append("  DISTINCT (sdw.codigocliente) AS cliente,\n");
        sql.append("           'CLIENTE'           AS origem,\n");
        sql.append("           sdw.codigocliente   AS codigoOrigem\n");
        sql.append("FROM situacaoclientesinteticodw sdw\n");
        sql.append("  INNER JOIN pessoa pes\n");
        sql.append("    ON pes.codigo = sdw.codigopessoa AND sdw.empresacliente = ").append(empresa).append("\n");
        sql.append(" LEFT JOIN periodoacessocliente pr ON pr.pessoa = sdw.codigopessoa \n");
        sql.append("  INNER JOIN vinculo v\n");
        sql.append("    ON v.cliente = sdw.codigocliente \n");
        if (!tipoVinculos.equals("TODOS")){
            sql.append(" AND v.tipovinculo in (").append(tipoVinculos).append(") \n");
        }
        sql.append("       AND v.colaborador = ").append(codColaborador).append("\n");
        sql.append("       AND Cast(pes.datacadastro AS DATE) = Cast((SELECT\n");
        sql.append("                                                    cast('").append(Uteis.getDataJDBC(dia)).append("' AS DATE) - INTERVAL '1 DAY' AS DATA) AS DATE)\n");
        sql.append("WHERE (sdw.situacao = 'VI' OR sdw.situacao = 'IN') AND not exists (select contrato.codigo from contrato  where contrato.pessoa = pes.codigo and contrato.vigenciade >= '").append(Uteis.getDataJDBC(dia)).append("') ");

        //Remover cliente gym pass
        sql.append("AND (coalesce(pr.tokengympass, '') = '')");

        sql.append(" AND sdw.codigocliente not in (").append(codigoClientesObjecoes).append(")");

        Statement sqlConsultar = con.createStatement();
        ResultSet tabelaResultado = sqlConsultar.executeQuery(sql.toString());
        montarFecharMetaDetalhado(tabelaResultado, obj, 0, nivelMontarDados);
        return tabelaResultado;
    }

    public ResultSet consultarClientesGymPassBuscandoCodigo(FecharMetaVO obj, Date dia, Integer codColaborador, int nivelMontarDados, Integer empresa, String tipoVinculos, String codigoClientesObjecoes) throws Exception {
        consultarCRM(getIdEntidade(), false);
        StringBuilder sql = new StringBuilder();
        sql.append("SELECT\n");
        sql.append("DISTINCT (sdw.codigocliente) AS cliente, \n");
        sql.append("'CLIENTE'           AS origem, \n");
        sql.append("sdw.codigocliente   AS codigoOrigem \n");
        sql.append("FROM situacaoclientesinteticodw sdw \n");
        sql.append("INNER JOIN pessoa pes ON pes.codigo = sdw.codigopessoa AND sdw.empresacliente = ").append(empresa).append("\n");
        sql.append("INNER JOIN periodoacessocliente pr ON pr.pessoa = sdw.codigopessoa \n");
        sql.append("INNER JOIN vinculo v ON v.cliente = sdw.codigocliente ").append(" AND v.colaborador = ").append(codColaborador).append("\n");
        if (!tipoVinculos.equals("TODOS")){
            sql.append(" AND v.tipovinculo in (").append(tipoVinculos).append(") \n");
        }
        sql.append("WHERE 1 = 1 \n");
        sql.append("AND coalesce(pr.tokengympass, '') <> ''");
        sql.append("AND pr.tipoacesso = 'PL' \n");
        sql.append("AND pr.datainicioacesso::date = '").append(Uteis.getDataJDBC(Uteis.somarDias(dia, -1))).append("' \n");
        sql.append("AND sdw.codigocliente not in (").append(codigoClientesObjecoes).append(")");
        Statement sqlConsultar = con.createStatement();
        ResultSet tabelaResultado = sqlConsultar.executeQuery(sql.toString());
        montarFecharMetaDetalhado(tabelaResultado, obj, 0, nivelMontarDados);
        return tabelaResultado;
    }

    /* (non-Javadoc)
     * @see negocio.facade.jdbc.crm.FecharMetaDetalhadoInterfaceFacade#consultarClientesAgendados(negocio.comuns.crm.FecharMetaVO, java.util.Date, java.lang.Integer, int)
     */
    public ResultSet consultarClientesAgendados(FecharMetaVO obj, Date dia, Integer codColaborador, int nivelMontarDados, Integer empresa,
                                                String tipoVinculos, String codigoClientesObjecoes) throws Exception {
        consultarCRM(getIdEntidade(), false);
        StringBuilder sql = new StringBuilder();
        sql.append("SELECT distinct(agenda.cliente), agenda.passivo, agenda.indicado , 'AGENDA' as origem,  ");
        sql.append("agenda.codigo as codigoOrigem, agenda.datacomparecimento from agenda WHERE (select cast(dataAgendamento as Date)) ");
        sql.append(" = (select cast ('").append(Uteis.getDataJDBC(dia)).append("' as date)) ");
        sql.append(" and coalesce(colaboradorResponsavel, responsavelcadastro) = ").append(codColaborador.intValue());
        sql.append(" AND empresa = ").append(empresa);

        Statement sqlConsultar = con.createStatement();
        ResultSet tabelaResultado = sqlConsultar.executeQuery(sql.toString());
        montarFecharMetaDetalhado(tabelaResultado, obj, 0, nivelMontarDados);
        return tabelaResultado;

    }

    /* (non-Javadoc)
     * @see negocio.facade.jdbc.crm.FecharMetaDetalhadoInterfaceFacade#consultarClientesAgendados(negocio.comuns.crm.FecharMetaVO, java.util.Date, java.lang.Integer, int)
     */
    public ResultSet consultarVisitantesRecorrentes(FecharMetaVO obj, Date dia, Integer codColaborador, int nivelMontarDados, Integer empresa,
                                                String tipoVinculos, String codigoClientesObjecoes) throws Exception {
        consultarCRM(getIdEntidade(), false);
        StringBuilder sql = new StringBuilder();
        sql.append("select distinct(c.codigo) as cliente , 'CLIENTE' as origem,  c.codigo as codigoOrigem ");
        sql.append("from cliente c inner join historicocontato h \n" +
                " on c.codigo = h.cliente");
        sql.append(" inner join vinculo on vinculo.cliente = c.codigo and vinculo.colaborador = " + codColaborador.intValue() + " \n");
        sql.append(" where h.fase = 'VR'");
        sql.append(" AND h.dia::date >= '").append(Uteis.getDataJDBC(Uteis.somarDias(dia, -7))).append("' \n");
        sql.append(" AND c.situacao = 'VI'");
        sql.append(" AND c.codigo not in (" + codigoClientesObjecoes + ") ");
        Statement sqlConsultar = con.createStatement();
        ResultSet tabelaResultado = sqlConsultar.executeQuery(sql.toString());
        montarFecharMetaDetalhado(tabelaResultado, obj, 0, nivelMontarDados);
        return tabelaResultado;

    }

    public ResultSet consultarClientesConversaoAgendados(FecharMetaVO obj, Date inicio, Date fim, Integer codColaborador, int nivelMontarDados, Integer empresa) throws Exception {
        consultarCRM(getIdEntidade(), false);
        StringBuilder strSQL = new StringBuilder();

        strSQL.append(" SELECT distinct(a.cliente), a.passivo, a.indicado , 'AGENDA' as origem,");
        strSQL.append(" a.codigo as codigoOrigem FROM agenda a ");
        strSQL.append(" INNER JOIN cliente cli ON a.cliente = cli.codigo ");
        strSQL.append(" WHERE colaboradorresponsavel = " + codColaborador);
        strSQL.append(" AND dataagendamento BETWEEN '" + Uteis.getDataJDBC(inicio) + " 00:00:00' AND '" + Uteis.getDataJDBC(fim) + " 23:59:59' ");
        strSQL.append(" AND tipoagendamento IN ('VI','AE')  AND a.empresa = " + empresa);
        strSQL.append(" AND (SELECT COUNT(*) FROM contrato WHERE pessoa = cli.pessoa  ");
        strSQL.append(" AND datalancamento BETWEEN a.dataagendamento AND '" + Uteis.getDataJDBC(fim) + " 23:59:59') = 0 ");
        strSQL.append(" AND (SELECT COUNT(*) FROM fecharmetadetalhado fmd ");
        strSQL.append(" INNER JOIN fecharmeta f ON f.codigo = fmd.fecharmeta  ");
        strSQL.append(" WHERE f.dataregistro BETWEEN '" + Uteis.getDataJDBC(inicio) + " 00:00:00' AND '" + Uteis.getDataJDBC(fim) + " 23:59:59' ");
        strSQL.append(" AND f.identificadormeta LIKE '" + FasesCRMEnum.CONVERSAO_AGENDADOS.getSigla() + "' AND fmd.cliente = cli.codigo) = 0 ");

        Statement sqlConsultar = con.createStatement();
        ResultSet tabelaResultado = sqlConsultar.executeQuery(strSQL.toString());
        montarFecharMetaDetalhado(tabelaResultado, obj, 0, nivelMontarDados);
        return tabelaResultado;

    }

    /* (non-Javadoc)
     * @see negocio.facade.jdbc.crm.FecharMetaDetalhadoInterfaceFacade#consultarClientesRenovacao(negocio.comuns.crm.FecharMetaVO, java.lang.Integer, java.lang.Integer, java.util.Date, int)
     */
    public ResultSet consultarClientesRenovacao(FecharMetaVO obj, Integer codColaborador, 
                                                Integer diasPrevistoUmMes, Integer diasPrevistoMaiorUmMes, Date dia, int nivelMontarDados, Integer empresa,
            String tipoVinculos, String codigoClientesObjecoes, Integer saldoLimiteRenovar, Set<Integer> skipped, boolean entraContratoAutoRRenovavel) throws Exception {
        consultarCRM(getIdEntidade(), false);
        StringBuilder strSQL = new StringBuilder();
        strSQL.append("SELECT Distinct (vinculo.cliente), 'CONTRATO' as origem, contrato.codigo as codigoOrigem from contrato \n");
        strSQL.append("inner join pessoa on  pessoa.codigo = contrato.pessoa \n");
        strSQL.append("inner join cliente on cliente.pessoa = pessoa.codigo  \n");
        strSQL.append("inner join vinculo on vinculo.cliente = cliente.codigo and vinculo.colaborador = '");
        strSQL.append(codColaborador.intValue()).append("'  \n");
        if (!tipoVinculos.equals("TODOS")) {
            strSQL.append(" and vinculo.tipovinculo in (").append(tipoVinculos).append(") \n");
        }

        strSQL.append(" LEFT JOIN contratoDuracao cd ON cd.contrato = contrato.codigo\n");

        if(!UteisValidacao.emptyNumber(saldoLimiteRenovar)){
            strSQL.append(" LEFT JOIN contratoDuracaoCreditoTreino cdc on cdc.contratoDuracao = cd.codigo\n");
        }
        strSQL.append(" left join contratooperacao op on op.contrato = contrato.codigo and op.tipooperacao = 'CA' \n");

        if(!entraContratoAutoRRenovavel) {
            strSQL.append(" inner join plano on contrato.plano = plano.codigo");
            strSQL.append(" left join planorecorrencia plr on plr.plano = plano.codigo \n");
            strSQL.append(" left join contratorecorrencia cr  on cr.contrato = contrato.codigo \n");
        }

        strSQL.append(" where 1 = 1 ");
        strSQL.append(" and (\n");
        strSQL.append("     ((cd.numeromeses = 1 and (Cast (dataPrevistaRenovar as date) = (select cast('");
        strSQL.append(Uteis.getDataJDBC(dia)).append("' as date) + interval '").append(diasPrevistoUmMes).append(" days' as date)))");
        strSQL.append(" OR  (cd.numeromeses > 1 and (Cast (dataPrevistaRenovar as date) = (select cast('");
        strSQL.append(Uteis.getDataJDBC(dia)).append("' as date) + interval '").append(diasPrevistoMaiorUmMes).append(" days' as date))))");
        if(!UteisValidacao.emptyNumber(saldoLimiteRenovar)){
            strSQL.append(" OR (cdc.quantidadeCreditoDisponivel <= ").append(saldoLimiteRenovar).append(" AND contrato.situacao = 'AT' and contrato.vigenciade <= '").append(Uteis.getDataJDBC(dia)).append("') ");
        }
        strSQL.append(") \n");
        strSQL.append("AND op.codigo is null AND cliente.empresa = ");
        strSQL.append(empresa).append(" AND datarenovarrealizada IS NULL \n");
        strSQL.append(" AND cliente.codigo not in (").append(codigoClientesObjecoes).append(")");

        if(!entraContratoAutoRRenovavel) {
            strSQL.append(" AND ((cr.contrato is null and not (plano.renovavelautomaticamente and contrato.renovavelautomaticamente))  or (cr.contrato is  not null and not (plr.renovavelautomaticamente and cr.renovavelautomaticamente)))");
        }

        Statement sqlConsultar = con.createStatement();
        ResultSet tabelaResultado = sqlConsultar.executeQuery(strSQL.toString());
        montarFecharMetaDetalhado(tabelaResultado, obj, 0, nivelMontarDados, skipped);
        return tabelaResultado;
    }

    /* (non-Javadoc)
     * @see negocio.facade.jdbc.crm.FecharMetaDetalhadoInterfaceFacade#consultarPerdasClientes(negocio.comuns.crm.FecharMetaVO, java.util.Date, java.lang.Integer, java.lang.Integer, int)
     */
    public ResultSet consultarPerdasClientes(FecharMetaVO obj, Date dia, Integer nrDiasAposVencimentoContrato, Integer codColaborador, int nivelMontarDados, Integer empresa, String tipoVinculos, String codigoClientesObjecoes) throws Exception {
        consultarCRM(getIdEntidade(), false);
        StringBuilder sql = new StringBuilder();
        sql.append(" SELECT distinct(codigocliente) as cliente,'CONTRATO' as origem, codigocontrato as codigoOrigem from situacaoclientesinteticodw ");
        sql.append(" inner join contrato on contrato.codigo = situacaoclientesinteticodw.codigocontrato ");
        sql.append(" inner join vinculo on vinculo.cliente = situacaoclientesinteticodw.codigocliente ");
        if (!tipoVinculos.equals("TODOS")) {
            sql.append(" and vinculo.tipovinculo in (").append(tipoVinculos).append(") ");
        }
        sql.append(" INNER JOIN cliente ON situacaoclientesinteticodw.codigocliente = cliente.codigo ");
        sql.append(" and vinculo.colaborador = " + codColaborador.intValue() + " where cast (( situacaoclientesinteticodw.datavigenciaateajustada + ");
        //soma mais um, pois deve começar a contar no dia posterior ao último dia do contrato
        sql.append(" interval '" + (nrDiasAposVencimentoContrato + 1) + " days' ) as date) = cast ('" + Uteis.getDataJDBC(dia) + "' as date)  and  ( ");
        sql.append(" contrato.contratoresponsavelrenovacaomatricula + contrato.contratoresponsavelrematriculamatricula) = 0 ");
        sql.append(" AND cliente.empresa = " + empresa + " AND situacaoclientesinteticodw.situacaocontrato NOT LIKE 'CA'");
        sql.append(" AND situacaoclientesinteticodw.situacaocontrato NOT LIKE 'AT'");
        sql.append(" AND situacaoclientesinteticodw.codigocliente not in (").append(codigoClientesObjecoes).append(")");
        Statement sqlConsultar = con.createStatement();
        ResultSet tabelaResultado = sqlConsultar.executeQuery(sql.toString());
        montarFecharMetaDetalhado(tabelaResultado, obj, 0, nivelMontarDados);
        return tabelaResultado;

    }

    public ResultSet consultarVencidos(FecharMetaVO obj, Integer codColaborador, int nivelMontarDados, Integer empresa, String tipoVinculos, String codigoClientesObjecoes) throws Exception {
        consultarCRM(getIdEntidade(), false);
        StringBuilder strSQL = new StringBuilder();
        strSQL.append("SELECT distinct(ss.codigocliente) as cliente,'CONTRATO' as origem, ss.codigocontrato as codigoOrigem FROM situacaoclientesinteticodw ss \n");
        strSQL.append("INNER JOIN vinculo v ON ss.codigocliente = v.cliente \n");
        if (!tipoVinculos.equals("TODOS")) {
            strSQL.append(" and v.tipovinculo in (").append(tipoVinculos).append(") ");
        }
        strSQL.append("INNER JOIN cliente c ON ss.codigocliente = c.codigo \n");
        strSQL.append("WHERE ss.situacaocontrato LIKE 'VE' \n");
        strSQL.append("AND v.colaborador = " + codColaborador + " \n");
        strSQL.append("AND ss.codigocliente not in (").append(codigoClientesObjecoes).append(")");
        strSQL.append("AND ( \n");
        strSQL.append(" SELECT COUNT(fm.codigo) FROM fecharmeta fm  \n");
        strSQL.append("INNER JOIN fecharmetadetalhado fmd on fm.codigo = fmd.fecharmeta \n");
        strSQL.append("WHERE fm.identificadormeta  = 'VE' and fmd.cliente = ss.codigocliente \n");
        strSQL.append("AND fmd.obtevesucesso = 't' and fm.dataregistro >= ss.datavigenciaateajustada) = 0 \n");
        strSQL.append("AND ( \n");
        strSQL.append("SELECT COUNT(a.codigo) FROM agenda a \n");
        strSQL.append("WHERE a.dataagendamento >= '").append(Uteis.getDataJDBC(obj.getDataRegistro())).append("' \n");
        strSQL.append("AND a.cliente = ss.codigocliente) = 0 \n");
        strSQL.append("AND c.empresa = " + empresa);

        Statement sqlConsultar = con.createStatement();
        ResultSet tabelaResultado = sqlConsultar.executeQuery(strSQL.toString());
        montarFecharMetaDetalhado(tabelaResultado, obj, 0, nivelMontarDados);
        return tabelaResultado;

    }
    /* (non-Javadoc)
     * @see negocio.facade.jdbc.crm.FecharMetaDetalhadoInterfaceFacade#consultarRiscoClientes(negocio.comuns.crm.FecharMetaVO, java.util.Date, java.lang.Integer, java.lang.Integer, int)
     */

    public ResultSet consultarRiscoClientes(FecharMetaVO obj, Integer codColaborador, Integer nrRisco, Date meta, int nivelMontarDados, Integer empresa, String tipoVinculos, String codigoClientesObjecoes) throws Exception {
        consultarCRM(getIdEntidade(), false);
        Statement sqlConsultar = con.createStatement();
        ResultSet tabelaResultado = sqlConsultar.executeQuery(FasesCRMSQL.sqlGrupoRisco(meta, nrRisco, codColaborador, empresa, false, tipoVinculos, codigoClientesObjecoes));
        montarFecharMetaDetalhado(tabelaResultado, obj, 0, nivelMontarDados);
        return tabelaResultado;
    }

    public ResultSet consultarCalculoMetaQtdeFaltasos(FecharMetaVO obj, Date dia, Integer codColaborador, Integer nrFalta, Integer nrFaltaConf, Integer nrDuracaoPlano, int nivelMontarDados, Integer empresa, String tipoVinculos, String codigoClientesObjecoes, Integer codCliente) throws Exception {
        consultarCRM(getIdEntidade(), false);
        Date dataInicioFaltas = Uteis.obterDataAnterior(dia, nrFalta);
        String sql = obterSqlCalculoMetaFaltosos(dia, codColaborador, nrFaltaConf, nrDuracaoPlano, empresa, tipoVinculos, codigoClientesObjecoes, dataInicioFaltas, codCliente);
        Statement sqlConsultar = con.createStatement();
        ResultSet tabelaResultado = sqlConsultar.executeQuery(sql);
        montarFecharMetaDetalhado(tabelaResultado, obj, 0, nivelMontarDados);
        return tabelaResultado;
    }

    public String obterSqlCalculoMetaFaltosos(Date dia, Integer codColaborador, Integer nrFaltaConf, Integer nrDuracaoPlano, Integer empresa, String tipoVinculos, String codigoClientesObjecoes, Date dataInicioFaltas, Integer codCliente) throws Exception {
        StringBuilder sql = new StringBuilder();
        sql.append("select * from ");
        sql.append(" (select distinct(codigocliente) as cliente, 'CLIENTE' as origem, codigocliente as codigoOrigem , ");
        sql.append(" ((('").append(Uteis.getDataJDBC(dia)).append("' :: DATE) - ((COALESCE(ac.dthrentrada, con.vigenciaDe) :: DATE)+1)) % ").append(nrFaltaConf).append(") as nrfalta, scsdw.datavigenciade, con.situacaocontrato as contratosituacao, ");
        sql.append(" (('").append(Uteis.getDataJDBC(dia)).append("' :: DATE) - ((COALESCE(ac.dthrentrada, con.vigenciaDe) :: DATE)+1))  as nrRealdefalta ");
        sql.append(" from situacaoclientesinteticodw scsdw");
        sql.append(" inner join contrato con on con.codigo = scsdw.codigocontrato ");
        sql.append(" inner join vinculo on vinculo.cliente = codigocliente and vinculo.colaborador = ").append(codColaborador);
        if (!tipoVinculos.equals("TODOS")) {
            sql.append(" and vinculo.tipovinculo in (").append(tipoVinculos).append(") ");
        }
        sql.append(" inner join contratoduracao on contratoduracao.contrato = scsdw.codigocontrato ");
        if (nrDuracaoPlano == 1) {
                sql.append(" and (contratoduracao.numeromeses = 1 or contratoduracao.numeromeses = 2) ");
        } else if (nrDuracaoPlano == 3) {
                sql.append(" and (contratoduracao.numeromeses = 3 or contratoduracao.numeromeses = 4 or contratoduracao.numeromeses = 5) ");
        } else {
                sql.append(" and contratoduracao.numeromeses >= 6 ");
        }
        sql.append("INNER JOIN cliente ON scsdw.codigocliente = cliente.codigo ");
        sql.append("INNER JOIN acessocliente ac ON cliente.uacodigo = ac.codigo ");
        sql.append(" Where ");
        sql.append(" ('").append(Uteis.getDataJDBC(dia)).append("' :: DATE - COALESCE(ac.dthrentrada, con.vigenciaDe) :: DATE) > 0 ");
        sql.append(" and con.situacao='AT' ");
        sql.append(" and cliente.codigo not in (").append(codigoClientesObjecoes).append(")");
        sql.append(" and ((scsdw.situacao = 'AT' and scsdw.situacaocontrato <> 'AE') ");
        sql.append(" and ");
        sql.append("(scsdw.situacao = 'AT' and scsdw.situacaocontrato <> 'CR')) ");
        sql.append(" AND cliente.empresa = ").append(empresa);
        if(!UteisValidacao.emptyNumber(codCliente)) {
            sql.append(" AND cliente.codigo = ").append(codCliente);
        }
        sql.append(" )  as tabela  where (tabela.nrfalta = 0) and (tabela.nrRealdefalta > 0) and (tabela.datavigenciade <='").append(Uteis.getDataJDBC(dataInicioFaltas)).append("' or (tabela.contratosituacao = 'RN') ");
        sql.append(") AND   (SELECT COUNT(*) FROM contratooperacao co INNER JOIN contrato c ON c.codigo = co.contrato ");
        sql.append("INNER JOIN cliente cli ON cli.pessoa = c.pessoa WHERE co.tipooperacao  ");
        sql.append("IN ('TR', 'CR', 'AT', 'BC') AND cli.codigo = tabela.cliente ");
        sql.append("AND ( datainicioefetivacaooperacao BETWEEN '").append(Uteis.getDataJDBC(dataInicioFaltas)).append("' AND '").append(Uteis.getDataJDBC(dia)).append("' ");
        sql.append("OR datafimefetivacaooperacao BETWEEN '").append(Uteis.getDataJDBC(dataInicioFaltas)).append("' AND '").append(Uteis.getDataJDBC(dia)).append("' ");
        sql.append("OR ('").append(Uteis.getDataJDBC(dataInicioFaltas)).append("' <= datainicioefetivacaooperacao AND '").append(Uteis.getDataJDBC(dia)).append("' >= datafimefetivacaooperacao))) <= 0");
        return sql.toString();
    }

    @Override
    public void consultarUltimoAcessoGympass(Date data, FecharMetaVO obj, ConfiguracaoDiasMetasTO tempoGympass, Integer codColaborador, Integer empresa, String tipoVinculos, String consultarUltimoAcessoGympass) throws Exception {
        try (Statement sqlConsultar = con.createStatement()) {
            try (ResultSet dadosSQL = sqlConsultar.executeQuery(FasesCRMSQL.sqlUltimoAcessoGympass(data, codColaborador, tempoGympass, empresa, false, tipoVinculos, consultarUltimoAcessoGympass))) {
                while (dadosSQL.next()) {
                    FecharMetaDetalhadoVO detalhado = new FecharMetaDetalhadoVO();
                    detalhado.setCodigoOrigem(dadosSQL.getInt("cliente"));
                    detalhado.setOrigem("CLIENTE");
                    detalhado.getCliente().setCodigo(dadosSQL.getInt("cliente"));
                    detalhado.setDiasSemAgendamento(tempoGympass.getNrDia());
                    detalhado.setDescconfiguracaodiasmetas(tempoGympass.getDescricao());
                    obj.getFecharMetaDetalhadoVOs().add(detalhado);
                }
            }
        }
    }

    @Override
    public void consultarFilaEsperaTurmaCRM(Date data, FecharMetaVO obj, Integer codColaborador, Integer empresa, String tipoVinculos, String clientesObjecoes) throws Exception {
        HashMap<Integer, Integer> controleOcupacao = new HashMap<>();
        try (Statement sqlConsultar = con.createStatement()) {
            try (ResultSet dadosSQL = sqlConsultar.executeQuery(FasesCRMSQL.sqlFilaEsperaTurmaCRM(data, codColaborador, empresa, false, tipoVinculos, clientesObjecoes))) {
                while (dadosSQL.next()) {

                    if (validarClienteReceptivoPossuiVinculoUsuarioMeta(codColaborador, dadosSQL)) continue;

                    if (!controleOcupacao.containsKey(dadosSQL.getInt("codigoTurma"))) {
                        controleOcupacao.put(dadosSQL.getInt("codigoTurma"), dadosSQL.getInt("ocupacao"));
                    }

                    int nrMaxAlunos = dadosSQL.getInt("nrmaximoaluno");
                    if (controleOcupacao.get(dadosSQL.getInt("codigoTurma")) < nrMaxAlunos) {
                        FecharMetaDetalhadoVO detalhado = new FecharMetaDetalhadoVO();
                        if (!UteisValidacao.emptyNumber(dadosSQL.getInt("codigoClienteOrigem"))) {
                            detalhado.setCodigoOrigem(dadosSQL.getInt("fila"));
                            detalhado.setOrigem("CLIENTE");
                            detalhado.getCliente().setCodigo(dadosSQL.getInt("codigoClienteOrigem"));
                        } else {
                            detalhado.setCodigoOrigem(dadosSQL.getInt("fila"));
                            detalhado.setOrigem("PASSIVO");
                            detalhado.getPassivo().setCodigo(dadosSQL.getInt("codigoPassivoOrigem"));
                        }
                        detalhado.setObservacaoFilaEsperaTurmaCrm("Vaga disponível na turma: " + dadosSQL.getString("identificadorturma") +
                                " - " + dadosSQL.getString("horainicial") + " às " + dadosSQL.getString("horafinal"));
                        obj.getFecharMetaDetalhadoVOs().add(detalhado);

                        controleOcupacao.put(dadosSQL.getInt("codigoTurma"), controleOcupacao.get(dadosSQL.getInt("codigoTurma")) + 1);
                    }
                }
            }
        }
    }

    private boolean validarClienteReceptivoPossuiVinculoUsuarioMeta(Integer codColaborador, ResultSet dadosSQL) throws SQLException {
        if (!UteisValidacao.emptyNumber(dadosSQL.getInt("codigoClienteOrigem"))) {
            try (Statement sqlVerificar = con.createStatement()) {
                try (ResultSet dadosSQL2 = sqlVerificar.executeQuery("select * from vinculo where cliente = " + dadosSQL.getInt("codigoClienteOrigem") + " and colaborador = " + codColaborador)) {
                    if (!dadosSQL2.next()) {
                        return true;
                    }
                }
            }
        } else if (!UteisValidacao.emptyNumber(dadosSQL.getInt("codigoPassivoOrigem"))) {
            try (Statement sqlVerificar = con.createStatement()) {
                try (ResultSet dadosSQL3 = sqlVerificar.executeQuery("select codigo from colaborador where pessoa = (select pessoa from colaborador where codigo = (select colaborador from usuario where codigo = (select responsavelcadastro from passivo where codigo = " + dadosSQL.getInt("codigoPassivoOrigem") + ")))")) {
                    boolean possuiCOlab = false;
                    while (dadosSQL3.next()) {
                        if (dadosSQL3.getInt("codigo") == codColaborador) {
                            possuiCOlab = true;
                            break;
                        }
                    }
                    if (!possuiCOlab) {
                        return true;
                    }
                }
            }
        }
        return false;
    }

    /* (non-Javadoc)
     * @see negocio.facade.jdbc.crm.FecharMetaDetalhadoInterfaceFacade#consultarClientesPosVendaBuscandoCodigoPassandoNrDeDias(java.lang.Integer, java.util.Date, negocio.comuns.crm.FecharMetaVO, java.lang.Integer, java.lang.Integer, int)
     */
    public ResultSet consultarClientesPosVendaBuscandoCodigoPassandoNrDeDias(Integer nrDias, Date dia, FecharMetaVO obj, Integer codigoConfiguracaoDiasPosVenda, Integer codColaborador, int nivelMontarDados, Integer empresa, boolean incluirContratosRenovados, String tipoVinculos, String codigoClientesObjecoes) throws Exception {
        consultarCRM(getIdEntidade(), false);
        Statement sqlConsultar = con.createStatement();
        ResultSet tabelaResultado = sqlConsultar.executeQuery(FasesCRMSQL.sqlPosVenda(nrDias, codColaborador, dia, empresa, incluirContratosRenovados, false, tipoVinculos, codigoClientesObjecoes));
        montarFecharMetaDetalhado(tabelaResultado, obj, codigoConfiguracaoDiasPosVenda, nivelMontarDados);
        return tabelaResultado;

    }

    /* (non-Javadoc)
     * @see negocio.facade.jdbc.crm.FecharMetaDetalhadoInterfaceFacade#consultarPorCodigoColaboradorResponsavelIdentificadorMeta(java.lang.Integer, java.lang.String, java.lang.Integer, java.lang.Integer, java.lang.Integer, boolean, int)
     */
    public FecharMetaDetalhadoVO consultarPorCodigoColaboradorResponsavelIdentificadorMeta(Integer valorConsulta, String identificadorMeta, Integer indicado, Integer passivo, Integer cliente, boolean controlarAcesso, int nivelMontarDados) throws Exception {
        consultarCRM(getIdEntidade(), controlarAcesso);
        String sqlStr = "select fecharmetadetalhado.* from fecharmetadetalhado "
                + "inner join fecharMeta on fecharmetadetalhado.fecharMeta = fecharMeta.codigo and fecharMeta.identificadorMeta = '" + identificadorMeta.toUpperCase() + "' "
                + "inner join aberturaMeta on fecharMeta.aberturaMeta = aberturaMeta.codigo and aberturaMeta.colaboradorResponsavel = " + valorConsulta;
        if (indicado.intValue() != 0) {
            sqlStr += " where indicado = " + indicado;
        } else if (passivo.intValue() != 0) {
            sqlStr += " where passivo = " + passivo;
        } else {
            sqlStr += " where cliente = " + cliente;
        }
        sqlStr += " and fecharmeta.dataregistro::DATE = '" + Uteis.getDataJDBC(Calendario.hoje()) +"';";
        try (Statement stm = con.createStatement()) {
            try (ResultSet tabelaResultado = stm.executeQuery(sqlStr)) {
                if (!tabelaResultado.next()) {
                    return new FecharMetaDetalhadoVO();
                }
                return montarDados(tabelaResultado, nivelMontarDados, con);
            }
        }
    }

    /* (non-Javadoc)
     * @see negocio.facade.jdbc.crm.FecharMetaDetalhadoInterfaceFacade#montarFecharMetaDetalhado(java.sql.ResultSet, negocio.comuns.crm.FecharMetaVO, java.lang.Integer, int)
     */
    public void montarFecharMetaDetalhado(ResultSet dadosSQL, FecharMetaVO obj, Integer configuracaoDiasPosVenda, int nivelMontarDados) throws SQLException {
        montarFecharMetaDetalhado(dadosSQL, obj, configuracaoDiasPosVenda, nivelMontarDados, null);
    }

    public void montarFecharMetaDetalhado(ResultSet dadosSQL, FecharMetaVO obj, Integer configuracaoDiasPosVenda, int nivelMontarDados, Set<Integer> skipped) throws SQLException {
        while (dadosSQL.next()) {
            FecharMetaDetalhadoVO detalhado = new FecharMetaDetalhadoVO();
            if (obj.getIdentificadorMeta().equals("RI")) {
                detalhado.setPesoRisco(new Integer(dadosSQL.getInt("peso")));
            }


            detalhado.setCodigoOrigem(new Integer(dadosSQL.getInt("codigoOrigem")));
            detalhado.setOrigem(dadosSQL.getString("origem"));
            if (nivelMontarDados == Uteis.NIVELMONTARDADOS_FECHARMETADETALHADOANIVERSARIANTE) {
                if (dadosSQL.getInt("cliente") != 0) {
                    detalhado.getCliente().setCodigo(new Integer(dadosSQL.getInt("cliente")));
                    if (configuracaoDiasPosVenda.intValue() != 0) {
                        detalhado.getConfiguracaoDiasPosVendaVO().setCodigo(configuracaoDiasPosVenda);
                    }
                }
            }
            if (nivelMontarDados == Uteis.NIVELMONTARDADOS_FECHARMETADETALHADOPASSIVO) {
                if (dadosSQL.getInt("passivo") != 0) {
                    detalhado.getPassivo().setCodigo(new Integer(dadosSQL.getInt("passivo")));
                }
            }
            if (nivelMontarDados == Uteis.NIVELMONTARDADOS_FECHARMETADETALHADOINDICACAO) {
                if (dadosSQL.getInt("indicado") != 0) {
                    detalhado.getIndicado().setCodigo(new Integer(dadosSQL.getInt("indicado")));
                }
            }
            if (nivelMontarDados == Uteis.NIVELMONTARDADOS_DADOSBASICOS) {
                if (dadosSQL.getInt("cliente") != 0) {
                    detalhado.getCliente().setCodigo(new Integer(dadosSQL.getInt("cliente")));
                    if (configuracaoDiasPosVenda.intValue() != 0) {
                        detalhado.getConfiguracaoDiasPosVendaVO().setCodigo(configuracaoDiasPosVenda);
                    }
                } else  if (dadosSQL.getInt("indicado") != 0) {
                    detalhado.getIndicado().setCodigo(new Integer(dadosSQL.getInt("indicado")));
                } else if (dadosSQL.getInt("passivo") != 0) {
                    detalhado.getPassivo().setCodigo(new Integer(dadosSQL.getInt("passivo")));
                }
            }

            if (obj.getIdentificadorMeta().equals(FasesCRMEnum.AGENDAMENTO.getSigla())) {
                if (dadosSQL.getDate("datacomparecimento") != null) {
                    detalhado.setObteveSucesso(true);
                    detalhado.setFecharMeta(obj);
                    obj.setMetaAtingida(obj.getMetaAtingida() + 1);
                }
            }
            if (skipped != null && !skipped.add(detalhado.getCodigoOrigem())) {
                continue;
            }
            if (!obj.getIdentificadorMeta().equals(FasesCRMEnum.FALTOSOS.getSigla()) || !obj.getFecharMetaDetalhadoVOs().contains(detalhado)) {
                obj.getFecharMetaDetalhadoVOs().add(detalhado);
            }
        }
    }

    public void processarMetasBatidasAnteriormente(List<FecharMetaVO> fecharMetaVos) throws Exception {
        for (FecharMetaVO obj : fecharMetaVos) {
            if (obj.getIdentificadorMeta().equals(FasesCRMEnum.DESISTENTES.getSigla())) {
                for (FecharMetaDetalhadoVO objDetalhado : obj.getFecharMetaDetalhadoVOs()) {
                    if (existeAgendamentoFuturo(objDetalhado.getCliente().getCodigo(), obj.getDataRegistro())) {
                        baterMetaPorFase(objDetalhado.getCliente().getCodigo(), null, obj.getDataRegistro(), FasesCRMEnum.DESISTENTES.getSigla(), 0, 0, 0);
                        HistoricoContatoVO hist = getFacade().getHistoricoContato().consultarPrimeiroAgendamentoAposDataPorCliente(objDetalhado.getCliente().getCodigo(), obj.getDataRegistro(), Uteis.NIVELMONTARDADOS_DADOSBASICOS);
                        alterarSomenteCamposHistoricoContato(hist.getCodigo(), objDetalhado.getCodigo());
                    }
                }
            }
            if (obj.getIdentificadorMeta().equals(FasesCRMEnum.CONVERSAO_AGENDADOS.getSigla())) {
                for (FecharMetaDetalhadoVO objDetalhado : obj.getFecharMetaDetalhadoVOs()) {
                    if (existeContratoLancadoPeriodoAgendamento(objDetalhado.getCliente())) {
                        baterMetaPorFase(objDetalhado.getCliente().getCodigo(), null, obj.getDataRegistro(), FasesCRMEnum.CONVERSAO_AGENDADOS.getSigla(), 0, 0, 0);
                    }
                }
            }
        }
    }

    @Override
    public void processarMetaBatidaAnteriormente(FecharMetaDetalhadoVO fecharMetaDetalhadoVO, FasesCRMEnum fase) throws Exception {
        if (existeAgendamentoFuturo(fecharMetaDetalhadoVO.getCliente().getCodigo(), fecharMetaDetalhadoVO.getFecharMeta().getDataRegistro())) {
            baterMetaPorFase(fecharMetaDetalhadoVO.getCliente().getCodigo(), null, fecharMetaDetalhadoVO.getFecharMeta().getDataRegistro(), fase.getSigla(), 0, 0, 0);
            alterarSomenteCamposHistoricoContato(fecharMetaDetalhadoVO.getHistoricoContatoVO().getCodigo(), fecharMetaDetalhadoVO.getCodigo());
        }
    }

    private boolean existeAgendamentoFuturo(Integer cliente, Date data) throws Exception {
        String sql = "select codigo from agenda where cliente = " + cliente + " and dataagendamento > '" + Uteis.getDataJDBC(data) + "'";
        return existe(sql, this.con);
    }

    private boolean existeContratoLancadoPeriodoAgendamento(ClienteVO cliente) throws Exception {
        ConfiguracaoSistemaCRMVO configCRM = getFacade().getConfiguracaoSistemaCRM().consultarPorChavePrimaria(1, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
        Date dataMinima = Uteis.somarDias(Calendario.hoje(), (-configCRM.getNrDiasAnterioresAgendamento()));
        if (cliente.getPessoa() == null || cliente.getPessoa().getCodigo() == 0) {
            cliente = getFacade().getCliente().consultarPorChavePrimaria(cliente.getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS);
        }
        return getFacade().getContrato().consultarPorClientePeriodoDataLancamento(cliente, dataMinima, Calendario.hoje(), Uteis.NIVELMONTARDADOS_MINIMOS).size() > 0;

    }

    /* (non-Javadoc)
     * @see negocio.facade.jdbc.crm.FecharMetaDetalhadoInterfaceFacade#inicializarDadosEnviarEmailColetivo(negocio.comuns.crm.FecharMetaVO, negocio.comuns.crm.MalaDiretaVOVO, negocio.comuns.arquitetura.UsuarioVO)
     */
    public void inicializarDadosEnviarEmailColetivo(FecharMetaVO obj, MalaDiretaVO malaDiretaVO, UsuarioVO usuarioLogado) throws Exception {
        malaDiretaVO.setNovoObj(true);
        malaDiretaVO.setTitulo("");
        malaDiretaVO.setMensagem("");
        malaDiretaVO.setModeloMensagem(new ModeloMensagemVO());
        malaDiretaVO.setRemetente(usuarioLogado);
        malaDiretaVO.setmalaDiretaEnviadaVOs(new ArrayList<MalaDiretaEnviadaVO>());
        malaDiretaVO.setFaseEnvio(obj.getIdentificadorMeta());

        MalaDiretaEnviadaVO malaDireta = null;
        if (obj.getAbaSelecionada().equals("HJ")) {
            for (FecharMetaDetalhadoVO metaDetalhado : obj.getFecharMetaDetalhadoVOs()) {
                if (metaDetalhado.getEnviarEmailSMS() && metaDetalhado.getPassivo().getCodigo() != 0) {
                    if (UteisValidacao.emptyString(metaDetalhado.getPassivo().getEmail())) {
                        throw new ConsistirException("Passivo(a) " + metaDetalhado.getPassivo().getNome() + " não possui e-mail. Cadastre um e-mail ou desmarque-o.");
                    }
                    malaDireta = new MalaDiretaEnviadaVO();
                    malaDireta.setPassivoVO(metaDetalhado.getPassivo());
                    malaDiretaVO.setRemetente(usuarioLogado);
                    malaDiretaVO.getMalaDiretaEnviadaVOs().add(malaDireta);
                    malaDiretaVO.setTotalPessoaMalaDireta((malaDiretaVO.getMalaDiretaEnviadaVOs().size()));
                } else if (metaDetalhado.getEnviarEmailSMS() && metaDetalhado.getIndicado().getCodigo() != 0) {
                    if (UteisValidacao.emptyString(metaDetalhado.getIndicado().getEmail())) {
                        throw new ConsistirException("Indicado(a) " + metaDetalhado.getIndicado().getNomeIndicado() + " não possui e-mail. Cadastre um e-mail ou desmarque-o.");
                    }
                    malaDireta = new MalaDiretaEnviadaVO();
                    malaDireta.setIndicadoVO(metaDetalhado.getIndicado());
                    malaDiretaVO.setRemetente(usuarioLogado);
                    malaDiretaVO.getMalaDiretaEnviadaVOs().add(malaDireta);
                    malaDiretaVO.setTotalPessoaMalaDireta(malaDiretaVO.getMalaDiretaEnviadaVOs().size());
                } else if (metaDetalhado.getEnviarEmailSMS() && metaDetalhado.getCliente().getCodigo() != 0) {
                    if (metaDetalhado.getCliente().getPessoa().getEmailVOs().isEmpty()) {
                        throw new ConsistirException("Aluno(a) " + metaDetalhado.getCliente().getPessoa().getNome() + " não possui e-mail. Cadastre um e-mail ou desmarque-o.");
                    }
                    malaDireta = new MalaDiretaEnviadaVO();
                    metaDetalhado.getCliente().setPessoa(getFacade().getPessoa().consultarPorChavePrimaria(metaDetalhado.getCliente().getPessoa().getCodigo().intValue(), Uteis.NIVELMONTARDADOS_DADOSBASICOS));
                    malaDireta.getClienteVO().setPessoa(metaDetalhado.getCliente().getPessoa());
                    malaDireta.getClienteVO().setCodigo(metaDetalhado.getCliente().getCodigo());
                    malaDiretaVO.setRemetente(usuarioLogado);
                    malaDiretaVO.getMalaDiretaEnviadaVOs().add(malaDireta);
                    malaDiretaVO.setTotalPessoaMalaDireta(malaDiretaVO.getMalaDiretaEnviadaVOs().size());
                }
            }
            if (malaDiretaVO.getMalaDiretaEnviadaVOs().isEmpty()) {
                throw new ConsistirException("Não existe nenhuma pessoa selecionada para o envio de E-mail !");
            }
        }

        if (obj.getAbaSelecionada().equals("ME")) {
            for (FecharMetaDetalhadoVO metaDetalhado : obj.getListaHistoricoContatoMes()) {
                if (metaDetalhado.getEnviarEmailSMS() && metaDetalhado.getPassivo().getCodigo() != 0) {
                    malaDireta = new MalaDiretaEnviadaVO();
                    malaDireta.setPassivoVO(metaDetalhado.getPassivo());
                    malaDiretaVO.setRemetente(usuarioLogado);
                    malaDiretaVO.getMalaDiretaEnviadaVOs().add(malaDireta);
                    malaDiretaVO.setTotalPessoaMalaDireta(malaDiretaVO.getMalaDiretaEnviadaVOs().size());
                } else if (metaDetalhado.getEnviarEmailSMS() && metaDetalhado.getIndicado().getCodigo() != 0) {
                    malaDireta = new MalaDiretaEnviadaVO();
                    malaDireta.setIndicadoVO(metaDetalhado.getIndicado());
                    malaDiretaVO.setRemetente(usuarioLogado);
                    malaDiretaVO.getMalaDiretaEnviadaVOs().add(malaDireta);
                    malaDiretaVO.setTotalPessoaMalaDireta(malaDiretaVO.getMalaDiretaEnviadaVOs().size());
                } else if (metaDetalhado.getEnviarEmailSMS() && metaDetalhado.getCliente().getCodigo() != 0) {
                    if (metaDetalhado.getCliente().getPessoa().getEmailVOs().isEmpty()) {
                        throw new ConsistirException("Aluno(a) " + metaDetalhado.getCliente().getPessoa().getNome() + " não possui e-mail. Cadastre um e-mail ou desmarque-o.");
                    }
                    malaDireta = new MalaDiretaEnviadaVO();
                    metaDetalhado.getCliente().setPessoa(getFacade().getPessoa().consultarPorChavePrimaria(metaDetalhado.getCliente().getPessoa().getCodigo().intValue(), Uteis.NIVELMONTARDADOS_DADOSBASICOS));
                    malaDireta.getClienteVO().setPessoa(metaDetalhado.getCliente().getPessoa());
                    malaDireta.getClienteVO().setCodigo(metaDetalhado.getCliente().getCodigo());
                    malaDiretaVO.setRemetente(usuarioLogado);
                    malaDiretaVO.getMalaDiretaEnviadaVOs().add(malaDireta);
                    malaDiretaVO.setTotalPessoaMalaDireta(malaDiretaVO.getMalaDiretaEnviadaVOs().size());
                }
            }
            if (malaDiretaVO.getMalaDiretaEnviadaVOs().isEmpty()) {
                throw new ConsistirException("Não existe nenhuma pessoa selecionada para o envio de E-mail !");
            }
        }
        if (obj.getAbaSelecionada().equals("HI")) {
            for (FecharMetaDetalhadoVO metaDetalhado : obj.getListaHistoricoContatoHistorico()) {
                if (metaDetalhado.getEnviarEmailSMS() && metaDetalhado.getPassivo().getCodigo() != 0) {
                    malaDireta = new MalaDiretaEnviadaVO();
                    malaDireta.setPassivoVO(metaDetalhado.getPassivo());
                    malaDiretaVO.setRemetente(usuarioLogado);
                    malaDiretaVO.getMalaDiretaEnviadaVOs().add(malaDireta);
                    malaDiretaVO.setTotalPessoaMalaDireta(malaDiretaVO.getMalaDiretaEnviadaVOs().size());
                } else if (metaDetalhado.getEnviarEmailSMS() && metaDetalhado.getIndicado().getCodigo() != 0) {
                    malaDireta = new MalaDiretaEnviadaVO();
                    malaDireta.setIndicadoVO(metaDetalhado.getIndicado());
                    malaDiretaVO.setRemetente(usuarioLogado);
                    malaDiretaVO.getMalaDiretaEnviadaVOs().add(malaDireta);
                    malaDiretaVO.setTotalPessoaMalaDireta(malaDiretaVO.getMalaDiretaEnviadaVOs().size());
                } else if (metaDetalhado.getEnviarEmailSMS() && metaDetalhado.getCliente().getCodigo() != 0) {
                    if (metaDetalhado.getCliente().getPessoa().getEmailVOs().isEmpty()) {
                        throw new ConsistirException("Aluno(a) " + metaDetalhado.getCliente().getPessoa().getNome() + " não possui e-mail. Cadastre um e-mail ou desmarque-o.");
                    }
                    malaDireta = new MalaDiretaEnviadaVO();
                    metaDetalhado.getCliente().setPessoa(getFacade().getPessoa().consultarPorChavePrimaria(metaDetalhado.getCliente().getPessoa().getCodigo().intValue(), Uteis.NIVELMONTARDADOS_DADOSBASICOS));
                    malaDireta.getClienteVO().setPessoa(metaDetalhado.getCliente().getPessoa());
                    malaDireta.getClienteVO().setCodigo(metaDetalhado.getCliente().getCodigo());
                    malaDiretaVO.setRemetente(usuarioLogado);
                    malaDiretaVO.getMalaDiretaEnviadaVOs().add(malaDireta);
                    malaDiretaVO.setTotalPessoaMalaDireta(malaDiretaVO.getMalaDiretaEnviadaVOs().size());
                }
            }
            if (malaDiretaVO.getMalaDiretaEnviadaVOs().isEmpty()) {
                throw new ConsistirException("Não existe nenhuma pessoa selecionada para o envio de E-mail !");
            }
        }
    }

    public void consultarClientesLigacaoAgendados(FecharMetaVO obj, Date dia, Integer codColaborador, int nivelMontarDados, Integer empresa, String tipoVinculos, String codigoClientesObjecoes) throws Exception {
        consultarCRM(getIdEntidade(), false);
        String sql = String.format(ConsultasFasesCRM.ligacaoAgendadosAmanha,
                new Object[]{"agenda.cliente, agenda.passivo, agenda.indicado , 'AGENDA' as origem,  "
                        + "agenda.codigo as codigoOrigem, agenda.datacomparecimento "});

        sql = sql + " AND (agenda.cliente not in (" + codigoClientesObjecoes + ") or (agenda.cliente is null and (agenda.passivo is not null or agenda.indicado is not null)))";

        try (PreparedStatement stm = con.prepareStatement(sql)) {
            int i = 0;
            stm.setDate(++i, Uteis.getDataJDBC(Uteis.somarCampoData(dia, Calendar.DAY_OF_MONTH, 1)));
            stm.setInt(++i, codColaborador);
            stm.setInt(++i, empresa);

            try (ResultSet tabelaResultado = stm.executeQuery()) {
                montarFecharMetaDetalhado(tabelaResultado, obj, 0, nivelMontarDados);
            }
        }
    }

    public void inicializarDadosEnviarSMSColetivo(FecharMetaVO obj, MalaDiretaVO malaDiretaVO, UsuarioVO usuarioLogado) throws Exception {
        malaDiretaVO.setNovoObj(true);
        malaDiretaVO.setTitulo("");
        malaDiretaVO.setMensagem("");
        malaDiretaVO.setModeloMensagem(new ModeloMensagemVO());
        malaDiretaVO.setRemetente(usuarioLogado);
        malaDiretaVO.setmalaDiretaEnviadaVOs(new ArrayList<MalaDiretaEnviadaVO>());
        malaDiretaVO.setFaseEnvio(obj.getIdentificadorMeta());

        MalaDiretaEnviadaVO malaDireta = null;
        if (obj.getAbaSelecionada().equals("HJ")) {
            for (FecharMetaDetalhadoVO metaDetalhado : obj.getFecharMetaDetalhadoVOs()) {
                if (metaDetalhado.getEnviarEmailSMS() && metaDetalhado.getPassivo().getCodigo() != 0) {
                    /*if (metaDetalhado.getPassivo().getTelefones().isEmpty()) {
                     throw new ConsistirException("Passivo(a) " + metaDetalhado.getPassivo().getNome() + " não possui telefone celular. Cadastre um telefone ou desmarque-o.");
                     }*/
                    malaDireta = new MalaDiretaEnviadaVO();
                    malaDireta.setPassivoVO(metaDetalhado.getPassivo());
                    malaDiretaVO.setRemetente(usuarioLogado);
                    malaDiretaVO.getMalaDiretaEnviadaVOs().add(malaDireta);
                    malaDiretaVO.setTotalPessoaMalaDireta(malaDiretaVO.getMalaDiretaEnviadaVOs().size());
                } else if (metaDetalhado.getEnviarEmailSMS() && metaDetalhado.getIndicado().getCodigo() != 0) {
                    /*if (metaDetalhado.getIndicado().getTelefones().isEmpty()) {
                     throw new ConsistirException("Indicado(a) " + metaDetalhado.getIndicado().getNomeIndicado() + " não possui telefone celular. Cadastre um telefone ou desmarque-o.");
                     }*/
                    malaDireta = new MalaDiretaEnviadaVO();
                    malaDireta.setIndicadoVO(metaDetalhado.getIndicado());
                    malaDiretaVO.setRemetente(usuarioLogado);
                    malaDiretaVO.getMalaDiretaEnviadaVOs().add(malaDireta);
                    malaDiretaVO.setTotalPessoaMalaDireta(malaDiretaVO.getMalaDiretaEnviadaVOs().size());
                } else if (metaDetalhado.getEnviarEmailSMS() && metaDetalhado.getCliente().getCodigo() != 0) {
                    boolean temCelular = false;
                    for (TelefoneVO telefoneVO : metaDetalhado.getCliente().getPessoa().getTelefoneVOs()) {
                        if (telefoneVO.getTipoTelefone().equals("CE")) {
                            temCelular = true;
                        }
                    }
                    if (!temCelular) {
                        throw new ConsistirException("Aluno(a) " + metaDetalhado.getCliente().getPessoa().getNome() + " não possui telefone celular. Cadastre um telefone ou desmarque-o.");
                    }
                    malaDireta = new MalaDiretaEnviadaVO();
                    metaDetalhado.getCliente().setPessoa(getFacade().getPessoa().consultarPorChavePrimaria(metaDetalhado.getCliente().getPessoa().getCodigo().intValue(), Uteis.NIVELMONTARDADOS_DADOSBASICOS));
                    malaDireta.getClienteVO().setPessoa(metaDetalhado.getCliente().getPessoa());
                    malaDireta.getClienteVO().setCodigo(metaDetalhado.getCliente().getCodigo());
                    malaDiretaVO.setRemetente(usuarioLogado);
                    malaDiretaVO.getMalaDiretaEnviadaVOs().add(malaDireta);
                    malaDiretaVO.setTotalPessoaMalaDireta(malaDiretaVO.getMalaDiretaEnviadaVOs().size());
                }
            }
            if (malaDiretaVO.getMalaDiretaEnviadaVOs().isEmpty()) {
                throw new ConsistirException("Não existe nenhuma pessoa selecionada para o envio de SMS !");
            }
        }
        if (obj.getAbaSelecionada().equals("ME")) {
            for (FecharMetaDetalhadoVO metaDetalhado : obj.getListaHistoricoContatoMes()) {
                if (metaDetalhado.getEnviarEmailSMS() && metaDetalhado.getPassivo().getCodigo() != 0) {
                    /*if (metaDetalhado.getPassivo().getTelefones().isEmpty()) {
                     throw new ConsistirException("Passivo(a) " + metaDetalhado.getPassivo().getNome() + " não possui telefone celular. Cadastre um telefone ou desmarque-o.");
                     }*/
                    malaDireta = new MalaDiretaEnviadaVO();
                    malaDireta.setPassivoVO(metaDetalhado.getPassivo());
                    malaDiretaVO.setRemetente(usuarioLogado);
                    malaDiretaVO.getMalaDiretaEnviadaVOs().add(malaDireta);
                    malaDiretaVO.setTotalPessoaMalaDireta(malaDiretaVO.getMalaDiretaEnviadaVOs().size());
                } else if (metaDetalhado.getEnviarEmailSMS() && metaDetalhado.getIndicado().getCodigo() != 0) {
                    /*if (metaDetalhado.getIndicado().getTelefones().isEmpty()) {
                     throw new ConsistirException("Indicado(a) " + metaDetalhado.getIndicado().getNomeIndicado() + " não possui telefone celular. Cadastre um telefone ou desmarque-o.");
                     }*/
                    malaDireta = new MalaDiretaEnviadaVO();
                    malaDireta.setIndicadoVO(metaDetalhado.getIndicado());
                    malaDiretaVO.setRemetente(usuarioLogado);
                    malaDiretaVO.getMalaDiretaEnviadaVOs().add(malaDireta);
                    malaDiretaVO.setTotalPessoaMalaDireta(malaDiretaVO.getMalaDiretaEnviadaVOs().size());
                } else if (metaDetalhado.getEnviarEmailSMS() && metaDetalhado.getCliente().getCodigo() != 0) {
                    if (metaDetalhado.getCliente().getPessoa().getTelefones().isEmpty()) {
                        throw new ConsistirException("Aluno(a) " + metaDetalhado.getCliente().getPessoa().getNome() + " não possui telefone celular. Cadastre um telefone ou desmarque-o.");
                    }
                    malaDireta = new MalaDiretaEnviadaVO();
                    metaDetalhado.getCliente().setPessoa(getFacade().getPessoa().consultarPorChavePrimaria(metaDetalhado.getCliente().getPessoa().getCodigo().intValue(), Uteis.NIVELMONTARDADOS_DADOSBASICOS));
                    malaDireta.getClienteVO().setPessoa(metaDetalhado.getCliente().getPessoa());
                    malaDireta.getClienteVO().setCodigo(metaDetalhado.getCliente().getCodigo());
                    malaDiretaVO.setRemetente(usuarioLogado);
                    malaDiretaVO.getMalaDiretaEnviadaVOs().add(malaDireta);
                    malaDiretaVO.setTotalPessoaMalaDireta(malaDiretaVO.getMalaDiretaEnviadaVOs().size());
                }
            }
            if (malaDiretaVO.getMalaDiretaEnviadaVOs().isEmpty()) {
                throw new ConsistirException("Não existe nenhuma pessoa selecionada para o envio de SMS !");
            }
        }
        if (obj.getAbaSelecionada().equals("HI")) {
            for (FecharMetaDetalhadoVO metaDetalhado : obj.getListaHistoricoContatoHistorico()) {
                if (metaDetalhado.getEnviarEmailSMS() && metaDetalhado.getPassivo().getCodigo() != 0) {
                    /* if (metaDetalhado.getPassivo().getTelefones().isEmpty()) {
                     throw new ConsistirException("Passivo(a) " + metaDetalhado.getPassivo().getNome() + " não possui telefone celular. Cadastre um telefone ou desmarque-o.");
                     }*/
                    malaDireta = new MalaDiretaEnviadaVO();
                    malaDireta.setPassivoVO(metaDetalhado.getPassivo());
                    malaDiretaVO.setRemetente(usuarioLogado);
                    malaDiretaVO.getMalaDiretaEnviadaVOs().add(malaDireta);
                    malaDiretaVO.setTotalPessoaMalaDireta(malaDiretaVO.getMalaDiretaEnviadaVOs().size());
                } else if (metaDetalhado.getEnviarEmailSMS() && metaDetalhado.getIndicado().getCodigo() != 0) {
                    /*if (metaDetalhado.getIndicado().getTelefones().isEmpty()) {
                     throw new ConsistirException("Indicado(a) " + metaDetalhado.getIndicado().getNomeIndicado() + " não possui telefone celular. Cadastre um telefone ou desmarque-o.");
                     }*/
                    malaDireta = new MalaDiretaEnviadaVO();
                    malaDireta.setIndicadoVO(metaDetalhado.getIndicado());
                    malaDiretaVO.setRemetente(usuarioLogado);
                    malaDiretaVO.getMalaDiretaEnviadaVOs().add(malaDireta);
                    malaDiretaVO.setTotalPessoaMalaDireta(malaDiretaVO.getMalaDiretaEnviadaVOs().size());
                } else if (metaDetalhado.getEnviarEmailSMS() && metaDetalhado.getCliente().getCodigo() != 0) {
                    /*if (metaDetalhado.getCliente().getPessoa().getTelefones().isEmpty()) {
                     throw new ConsistirException("Aluno(a) " + metaDetalhado.getCliente().getPessoa().getNome() + " não possui telefone celular. Cadastre um telefone ou desmarque-o.");
                     }*/
                    malaDireta = new MalaDiretaEnviadaVO();
                    metaDetalhado.getCliente().setPessoa(getFacade().getPessoa().consultarPorChavePrimaria(metaDetalhado.getCliente().getPessoa().getCodigo().intValue(), Uteis.NIVELMONTARDADOS_DADOSBASICOS));
                    malaDireta.getClienteVO().setPessoa(metaDetalhado.getCliente().getPessoa());
                    malaDireta.getClienteVO().setCodigo(metaDetalhado.getCliente().getCodigo());
                    malaDiretaVO.setRemetente(usuarioLogado);
                    malaDiretaVO.getMalaDiretaEnviadaVOs().add(malaDireta);
                    malaDiretaVO.setTotalPessoaMalaDireta(malaDiretaVO.getMalaDiretaEnviadaVOs().size());
                }
            }
            if (malaDiretaVO.getMalaDiretaEnviadaVOs().isEmpty()) {
                throw new ConsistirException("Não existe nenhuma pessoa selecionada para o envio de SMS !");
            }
        }
    }

    /**
     * Método responsavel por consultar um fecharMetaDetalhado pelo codigo do
     * Passivo
     *
     * <AUTHOR>
     */
    public List consultarFecharMetaDetalhadoPorCodigoPassivo(Integer codPassivo, int nivelMontarDados) throws Exception {
        String sql = "SELECT FecharMetaDetalhado.* FROM FecharMetaDetalhado "
                + "inner join fecharMeta on fecharMeta.codigo = FecharMetaDetalhado.fecharMeta "
                + "WHERE FecharMetaDetalhado.passivo = " + codPassivo.intValue() + "";
        try (Statement stm = con.createStatement()) {
            try (ResultSet tabelaResultado = stm.executeQuery(sql)) {
                return montarDadosConsulta(tabelaResultado, nivelMontarDados, con);
            }
        }

    }

    /**
     * Método responsavel por consultar um fecharMetaDetalhado pelo codigo da
     * Agenda
     *
     * <AUTHOR>
     */
    public FecharMetaDetalhadoVO consultarFecharMetaDetalhadoPorCodigoAgenda(Integer codAgenda, int nivelMontarDados) throws Exception {
        String sql = "SELECT FecharMetaDetalhado.* FROM FecharMetaDetalhado "
                + "inner join fecharMeta on fecharMeta.codigo = FecharMetaDetalhado.fecharMeta AND fecharMeta.identificadormeta LIKE 'AG' "
                + "WHERE FecharMetaDetalhado.codigoOrigem = " + codAgenda.intValue() + " and FecharMetaDetalhado.origem = 'AGENDA' ";
        try (PreparedStatement sqlConsultar = con.prepareStatement(sql)) {
            try (ResultSet tabelaResultado = sqlConsultar.executeQuery()) {
                if (!tabelaResultado.next()) {
                    return new FecharMetaDetalhadoVO();
                }
                return montarDados(tabelaResultado, nivelMontarDados, con);
            }
        }

    }

    /**
     * Método responsavel por consultar a MetaDetalhada pelo codigo do Passivo e
 excluirComLog a MetaDetalhada encontrada.
     *
     * <AUTHOR>
     */
    public List executarExclusaoFecharMetaDetalhadoPorCodigoPassivo(Integer codPassivo) throws Exception {
        List listaFecharMetaDetalhado = new ArrayList();
        listaFecharMetaDetalhado = consultarFecharMetaDetalhadoPorCodigoPassivo(codPassivo, Uteis.NIVELMONTARDADOS_HISTORICOCLIENTE);
        Iterator e = listaFecharMetaDetalhado.iterator();
        while (e.hasNext()) {
            FecharMetaDetalhadoVO obj = (FecharMetaDetalhadoVO) e.next();
            if (obj.getFecharMeta().getAberturaMetaVO().getMetaEmAberto()) {
                excluirFecharMetaDetalhadoPorCodigoPassivo(codPassivo);
            } else {
                throw new Exception("A Abertura de Meta já foi fechada !");
            }
        }
        return listaFecharMetaDetalhado;
    }

    /**
     * Responsável por obter os numeros totais de origem de cada meta detalhada
     *
     * <AUTHOR> 23/05/2011
     */
    public Map<String, Integer> contarOrigem(Integer valorConsulta) throws Exception {
        Map<String, Integer> result = new HashMap<String, Integer>();
        String sql = "SELECT COUNT(codigo) as total, origem FROM fecharmetadetalhado WHERE fecharmeta = ? GROUP BY (origem)";
        Declaracao dc = new Declaracao(sql, con);
        dc.setInt(1, valorConsulta);
        try (ResultSet rs = dc.executeQuery()) {
            while (rs.next()) {
                result.put(rs.getString("origem"), rs.getInt("total"));
            }
        }
        return result;
    }

    /**
     * Esta consulta irá retornar os comparecimentos de clientes agendados, de
     * uma meta
     *
     * <AUTHOR> 26/05/2011
     */
    public List<FecharMetaDetalhadoVO> consultarComparecimentos(Integer codigoFecharMeta, Integer nivelMontarDados) throws Exception {
        return consultarDetalhadosFiltrados(codigoFecharMeta, nivelMontarDados, DETALHAR_COMPARECIMENTOS);
    }

    /**
     * Esta consulta irá retornar os comparecimentos de clientes agendados, de
     * uma meta
     *
     * <AUTHOR> 26/05/2011
     */
    public List<FecharMetaDetalhadoVO> consultarReagendados(Integer codigoFecharMeta, Integer nivelMontarDados) throws Exception {
        return consultarDetalhadosFiltrados(codigoFecharMeta, nivelMontarDados, DETALHAR_REAGENDADOS);
    }

    /**
     * Esta consulta irá retornar os comparecimentos de clientes agendados que
     * fecharam contrato, de uma meta
     *
     * <AUTHOR> 26/05/2011
     */
    public List<FecharMetaDetalhadoVO> consultarFechamentos(Integer codigoFecharMeta, Integer nivelMontarDados) throws Exception {
        return consultarDetalhadosFiltrados(codigoFecharMeta, nivelMontarDados, DETALHAR_FECHAMENTOS);
    }

    public List<FecharMetaDetalhadoVO> consultarLigacoes(Integer codigoFecharMeta, Integer nivelMontarDados) throws Exception {
        String sqlDetalharLigacoes = "SELECT\n"
                + "  fmd.* AS total\n"
                + "FROM fecharmetadetalhado fmd INNER JOIN agenda a\n"
                + "    ON a.codigo = fmd.codigoorigem AND a.tipoagendamento LIKE 'LI'\n"
                + "WHERE fecharmeta = ?";
        return consultarDetalhadosFiltrados(codigoFecharMeta, nivelMontarDados, sqlDetalharLigacoes);
    }

    public List<FecharMetaDetalhadoVO> consultarLigacoes(String codigosFecharMeta, Integer nivelMontarDados) throws Exception {
        String sqlDetalharLigacoes = "SELECT\n"
                + "  fmd.* AS total\n"
                + "FROM fecharmetadetalhado fmd INNER JOIN agenda a\n"
                + "    ON a.codigo = fmd.codigoorigem AND a.tipoagendamento LIKE 'LI'\n"
                + "WHERE fecharmeta in (" + codigosFecharMeta + ")";
        return consultarDetalhadosFiltrados("", nivelMontarDados, sqlDetalharLigacoes);
    }

    /**
     * Método genérico responsável por executar as consultas sql passadas como
     * parametro e retornar os resultados montados em objetos do tipo
     * FecharMetaDetalhadoVO.
     *
     * <AUTHOR> 26/05/2011
     */
    private List<FecharMetaDetalhadoVO> consultarDetalhadosFiltrados(Object codigoFecharMeta, Integer nivelMontarDados, String sql) throws Exception {
        Declaracao dc = new Declaracao(sql, con);
        if (!codigoFecharMeta.toString().equals("")) {
            dc.setInt(1, (Integer) codigoFecharMeta);
        }
        try (ResultSet rs = dc.executeQuery()) {
            return montarDadosConsulta(rs, nivelMontarDados, con);
        }

    }

    public void setarAtributoEspecificoFecharMetaDetalhados(List<FecharMetaDetalhadoVO> lista, String identificador) throws Exception {
        if (identificador.equals("FA")) {
            for (FecharMetaDetalhadoVO meta : lista) {
                getFacade().getHistoricoContato().consultarTotalLigacaoEDiasUltAcesso(meta.getHistoricoContatoVO());
            }
        } else if (identificador.equals("PE") || identificador.equals("RE")) {
            for (FecharMetaDetalhadoVO meta : lista) {
                meta.getHistoricoContatoVO().setVencimentoContrato(getFacade().getHistoricoContato().obterVencimentoUltimoContrato(meta.getCliente().getCodigo()));
            }
        } else if (identificador.equals("VE")) {
            for (FecharMetaDetalhadoVO meta : lista) {
                meta.getHistoricoContatoVO().setVencimentoContrato(getFacade().getHistoricoContato().obterVencimentoUltimoContrato(meta.getCliente().getCodigo()));
                try {
                    RiscoVO riscoVO = getFacade().getRisco().consultarPorCliente(meta.getCliente().getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS);
                    meta.setPesoRisco(riscoVO.getPeso());
                } catch (Exception e) {
                    meta.setPesoRisco(0);
                }
                getFacade().getHistoricoContato().consultarTotalLigacaoEDiasUltAcesso(meta.getHistoricoContatoVO());
            }
        }
    }

    /**
     * <AUTHOR> 30/08/2011
     */
    public List<Map<String, Object>> consultarPorClienteDia(Integer cliente, Date dia, Integer nivelMontarDados) throws Exception {
        StringBuilder sql = new StringBuilder();

        sql.append("SELECT fd.codigo, am.dia,fd.obtevesucesso, am.metaemaberto, f.identificadormeta \n");
        sql.append("FROM fecharmetadetalhado fd, fecharmeta f, aberturameta am, cliente c \n");
        sql.append("WHERE fd.fecharmeta = f.codigo \n");
        sql.append("AND f.aberturameta = am.codigo \n");
        sql.append("AND ( fd.origem LIKE 'CLIENTE' OR fd.origem LIKE 'CONTRATO' ) \n");
        sql.append("AND am.dia = '").append(Uteis.getDataJDBC(dia)).append("' \n");
        sql.append("AND fd.cliente = c.codigo \n");
        sql.append("AND c.pessoa =  ").append(cliente);

        List<Map<String, Object>> lista = new ArrayList<Map<String, Object>>();
        try (ResultSet consulta = criarConsulta(sql.toString(), con)) {
            while (consulta.next()) {
                Map<String, Object> mapa = new HashMap<String, Object>();
                mapa.put("codigoFecharMetaDetalhada", consulta.getObject("codigo"));
                mapa.put("dia", consulta.getObject("dia"));
                mapa.put("metaemaberto", consulta.getObject("metaemaberto"));
                mapa.put("identificadormeta", consulta.getObject("identificadormeta"));
                mapa.put("obtevesucesso", consulta.getObject("obtevesucesso"));
                lista.add(mapa);
            }
        }
        return lista;
    }

    /**
     * Joao Alcides 13/02/2012
     *
     * @param codigoCliente
     * @param dia
     * @throws Exception
     */
    public void baterMetaPorFase(Integer codigoCliente, Integer pessoa, Date dia, String fase, Integer hist, Integer passivo, Integer indicado) throws Exception {
        if(UteisValidacao.emptyNumber(passivo) && (UteisValidacao.emptyNumber(indicado))){
            if (UteisValidacao.emptyNumber(codigoCliente)) {
                ResultSet setCliente = FecharMetaDetalhado.criarConsulta("SELECT codigo FROM cliente WHERE pessoa = " + pessoa, con);
                if (setCliente.next()) {
                    codigoCliente = setCliente.getInt("codigo");
                }
            }
        }
        StringBuffer sql = new StringBuffer();
        sql.append("SELECT fd.codigo, fd.fecharmeta, f.metaatingida, f.meta,f.identificadormeta,ag.tipoagendamento,fd.origem,fd.codigoorigem FROM fecharmetadetalhado fd \n");
        sql.append("INNER JOIN fecharmeta f ON f.codigo = fd.fecharmeta \n");
        sql.append("LEFT JOIN agenda ag on ag.codigo = fd.codigoorigem and origem = 'AGENDA' WHERE \n");
        if(!UteisValidacao.emptyNumber(passivo)){
            sql.append(" fd.passivo = " + passivo + " AND \n");
        } else  if(!UteisValidacao.emptyNumber(indicado)){
            sql.append(" fd.indicado = " + indicado + " AND \n");
        } else {
            sql.append(" fd.cliente = " + codigoCliente + " AND \n");
        }
        if (fase.equals("%")) {
            sql.append("f.identificadormeta <> 'AG' \n");
        } else if(fase.equals("AG")){
            sql.append("f.identificadormeta = 'AG' \n");
        } else {
           sql.append("f.identificadormeta LIKE '" + fase + "' \n");
        }
        sql.append("AND f.dataregistro::date = '" + Uteis.getDataJDBC(dia) + "' \n");
        if(!fase.equals("AN")){
            sql.append(" AND (fd.obtevesucesso = FALSE OR fd.obtevesucesso IS NULL) ");
        }
        try (ResultSet resultSet = FecharMetaDetalhado.criarConsulta(sql.toString(), con)) {
            List<Integer> fecharMetaProcessado = new ArrayList<Integer>();
            while (resultSet.next()) {
                if (!resultSet.getString("identificadormeta").equals("AG") || (resultSet.getString("identificadormeta").equals("AG") && (resultSet.getString("tipoagendamento").equals("LI")) || resultSet.getString("tipoagendamento").equals("VI"))) {
                    Integer codigo = resultSet.getInt("codigo");
                    Integer fecharMeta = resultSet.getInt("fecharmeta");
                    String origem = resultSet.getString("origem");
                    Integer codigoorigem = resultSet.getInt("codigoorigem");
                    Double meta = resultSet.getDouble("meta");
                    Double metaAtingida = resultSet.getDouble("metaatingida") + 1;
                    if (fecharMetaProcessado.contains(fecharMeta)) {//aluno duas vezes na meta, acontece por exemplo no pós-venda, em casos de dias próximos, que caiam no fim de semana
                        try (ResultSet rsMetaAtingida = FecharMetaDetalhado.criarConsulta("select metaatingida from fecharmeta where codigo = " + fecharMeta, con)) {
                            rsMetaAtingida.next();
                            metaAtingida = rsMetaAtingida.getDouble("metaatingida") + 1;
                        }
                    }
                    Double porcentagem = 0.0;
                    if (meta != 0.0) {
                        porcentagem = (metaAtingida / meta) * 100.0;
                    }

                    FecharMetaDetalhado.executarConsulta("UPDATE fecharmetadetalhado SET teveContato = true, obtevesucesso = TRUE " + (UteisValidacao.emptyNumber(hist) ? "" : " , historicocontato = " + hist) + " WHERE codigo = " + codigo, con);
                    FecharMetaDetalhado.executarConsulta("UPDATE fecharmeta SET metaatingida = " + metaAtingida + ", porcentagem = " + porcentagem
                            + " WHERE codigo = " + fecharMeta, con);
                    fecharMetaProcessado.add(fecharMeta);

                    if (origem.equals("CONVERSAOLEAD")) {
                        ConversaoLead conversaoleadDAO = new ConversaoLead(con);
                        conversaoleadDAO.atualizarLeadMetaBatida(codigoorigem, hist);
                        conversaoleadDAO = null;
                    }
                }
            }
        }
    }

    /**
     * Anderson Xavier 27/07/2023
     *
     * @param codigoCliente
     * @param dia
     * @throws Exception
     */
    public void baterMetaPorFaseConversao(Integer codigoCliente, Integer pessoa, Date dia, String fase, Integer hist, Integer passivo, Integer indicado) throws Exception {
        if(UteisValidacao.emptyNumber(passivo) && (UteisValidacao.emptyNumber(indicado))){
            if (UteisValidacao.emptyNumber(codigoCliente)) {
                ResultSet setCliente = FecharMetaDetalhado.criarConsulta("SELECT codigo FROM cliente WHERE pessoa = " + pessoa, con);
                if (setCliente.next()) {
                    codigoCliente = setCliente.getInt("codigo");
                }
            }
        }
        StringBuffer sql = new StringBuffer();
        sql.append("SELECT fd.codigo, fd.fecharmeta, f.metaatingida, f.meta,f.identificadormeta,ag.tipoagendamento,fd.origem,fd.codigoorigem FROM fecharmetadetalhado fd \n");
        sql.append("INNER JOIN fecharmeta f ON f.codigo = fd.fecharmeta \n");
        sql.append("LEFT JOIN agenda ag on ag.codigo = fd.codigoorigem and origem = 'AGENDA' WHERE \n");
        if(!UteisValidacao.emptyNumber(passivo)){
            sql.append(" fd.passivo = " + passivo + " AND \n");
        } else  if(!UteisValidacao.emptyNumber(indicado)){
            sql.append(" fd.indicado = " + indicado + " AND \n");
        } else {
            sql.append(" fd.cliente = " + codigoCliente + " AND \n");
        }
        if (fase.equals("%")) {
            sql.append("f.identificadormeta <> 'AG' \n");
        } else {
            sql.append("f.identificadormeta LIKE '" + fase + "' \n");
        }
        sql.append("AND f.dataregistro::date = '" + Uteis.getDataJDBC(dia) + "' \n"
                + "AND NOT fd.obtevesucesso");
        try (ResultSet resultSet = FecharMetaDetalhado.criarConsulta(sql.toString(), con)) {
            List<Integer> fecharMetaProcessado = new ArrayList<Integer>();
            while (resultSet.next()) {
                if ((resultSet.getString("identificadormeta") != null && resultSet.getString("tipoagendamento") != null) && (!resultSet.getString("identificadormeta").equals("AG") || (resultSet.getString("identificadormeta").equals("AG") && resultSet.getString("tipoagendamento").equals("LI") || resultSet.getString("identificadormeta").equals("AG") && resultSet.getString("tipoagendamento").equals("VI") || resultSet.getString("identificadormeta").equals("AG") && resultSet.getString("tipoagendamento").equals("AE")))) {
                    Integer codigo = resultSet.getInt("codigo");
                    Integer fecharMeta = resultSet.getInt("fecharmeta");
                    String origem = resultSet.getString("origem");
                    Integer codigoorigem = resultSet.getInt("codigoorigem");
                    Double meta = resultSet.getDouble("meta");
                    Double metaAtingida = resultSet.getDouble("metaatingida") + 1;
                    if (fecharMetaProcessado.contains(fecharMeta)) {//aluno duas vezes na meta, acontece por exemplo no pós-venda, em casos de dias próximos, que caiam no fim de semana
                        try (ResultSet rsMetaAtingida = FecharMetaDetalhado.criarConsulta("select metaatingida from fecharmeta where codigo = " + fecharMeta, con)) {
                            rsMetaAtingida.next();
                            metaAtingida = rsMetaAtingida.getDouble("metaatingida") + 1;
                        }
                    }
                    Double porcentagem = 0.0;
                    if (meta != 0.0) {
                        porcentagem = (metaAtingida / meta) * 100.0;
                    }

                    FecharMetaDetalhado.executarConsulta("UPDATE fecharmetadetalhado SET teveContato = true, obtevesucesso = TRUE " + (UteisValidacao.emptyNumber(hist) ? "" : " , historicocontato = " + hist) + " WHERE codigo = " + codigo, con);
                    if (!(resultSet.getString("identificadormeta").equals("AG") && resultSet.getString("tipoagendamento").equals("LI"))) {
                        FecharMetaDetalhado.executarConsulta("UPDATE fecharmeta SET metaatingida = " + metaAtingida + ", porcentagem = " + porcentagem
                                + " WHERE codigo = " + fecharMeta, con);
                        fecharMetaProcessado.add(fecharMeta);
                    }
                }
            }
        }
    }
    public void alterarSomenteCamposHistoricoContato(Integer codigoCliente, Integer pessoa, Date dia, String fase, Integer hist) throws Exception {
        if (UteisValidacao.emptyNumber(codigoCliente)) {
            try (ResultSet setCliente = FecharMetaDetalhado.criarConsulta("SELECT codigo FROM cliente WHERE pessoa = " + pessoa, con)) {
                if (setCliente.next()) {
                    codigoCliente = setCliente.getInt("codigo");
                }
            }
        }
        String sql = "SELECT fd.codigo, fd.fecharmeta, f.metaatingida, f.meta FROM fecharmetadetalhado fd \n"
                + "INNER JOIN fecharmeta f ON f.codigo = fd.fecharmeta \n"
                + "WHERE fd.cliente = " + codigoCliente + " AND f.identificadormeta LIKE '" + fase + "' \n"
                + "AND f.dataregistro::date = '" + Uteis.getDataJDBC(dia) + "' \n"
                + "AND fd.obtevesucesso";
        try (ResultSet resultSet = FecharMetaDetalhado.criarConsulta(sql, con)) {
            while (resultSet.next()) {
                Integer codigo = resultSet.getInt("codigo");
                alterarSomenteCamposHistoricoContato(hist, codigo);
            }
        }
    }

    /**
     * Joao Alcides 04/04/2012
     *
     * @param inicio
     * @param fim
     * @return
     * @throws Exception
     */
    public FecharMetaDetalhadoVO consultarConversaoAgendados(Integer pessoa, Date inicio, Date fim) throws Exception {
        StringBuilder sql = new StringBuilder();
        sql.append(" SELECT * FROM fecharmetadetalhado fmd");
        sql.append(" INNER JOIN fecharmeta f ON f.codigo = fmd.fecharmeta");
        sql.append(" INNER JOIN cliente c ON c.codigo = fmd.cliente ");
        sql.append(" WHERE f.dataregistro BETWEEN '" + Uteis.getDataJDBC(inicio) + "' AND '" + Uteis.getDataJDBC(fim) + "' ");
        sql.append(" AND f.identificadormeta LIKE '" + FasesCRMEnum.CONVERSAO_AGENDADOS.getSigla() + "' AND c.pessoa = " + pessoa);

        try (ResultSet consulta = criarConsulta(sql.toString(), con)) {
            if (consulta.next()) {
                return montarDados(consulta, Uteis.NIVELMONTARDADOS_TODOS, con);
            } else {
                return new FecharMetaDetalhadoVO();
            }
        }
    }

    public void consultarClientesSessoesFinais(FecharMetaVO obj, int sessoesFinais, Integer codColaborador, Integer empresa, String codigoClientesObjecoes) throws Exception {
        try (Statement sqlConsultar = con.createStatement()) {
            try (ResultSet dadosSQL = sqlConsultar.executeQuery(FasesCRMSQL.sqlSessoesFinais(codColaborador, sessoesFinais, empresa, false, codigoClientesObjecoes))) {
                while (dadosSQL.next()) {
                    FecharMetaDetalhadoVO detalhado = new FecharMetaDetalhadoVO();
                    detalhado.setCodigoOrigem(dadosSQL.getInt("cliente"));
                    detalhado.setOrigem("CLIENTE");
                    detalhado.getCliente().setCodigo(dadosSQL.getInt("cliente"));
                    detalhado.setSessoesFinais(sessoesFinais);
                    detalhado.setVendaAvulsa(dadosSQL.getInt("vendaavulsa"));
                    obj.getFecharMetaDetalhadoVOs().add(detalhado);
                }
            }
        }
    }

    public void consultarClientesSemAgendamento(Date data, FecharMetaVO obj, ConfiguracaoDiasMetasTO semAgendamento, Integer codColaborador, Integer empresa, String codigoClientesObjecoes) throws Exception {
        try (Statement sqlConsultar = con.createStatement()) {
            try (ResultSet dadosSQL = sqlConsultar.executeQuery(FasesCRMSQL.sqlSemAgendamento(data, codColaborador, semAgendamento, empresa, false, codigoClientesObjecoes))) {
                while (dadosSQL.next()) {
                    FecharMetaDetalhadoVO detalhado = new FecharMetaDetalhadoVO();
                    detalhado.setCodigoOrigem(dadosSQL.getInt("cliente"));
                    detalhado.setOrigem("CLIENTE");
                    detalhado.getCliente().setCodigo(dadosSQL.getInt("cliente"));
                    detalhado.setDiasSemAgendamento(semAgendamento.getNrDia());
                    obj.getFecharMetaDetalhadoVOs().add(detalhado);
                }
            }
        }
    }

    public void consultarClientesExAlunos(Date data, FecharMetaVO obj, ConfiguracaoDiasMetasTO tempoExAluno, Integer codColaborador, Integer empresa, String tipoVinculos, String codigoClientesObjecoes) throws Exception {
        try (Statement sqlConsultar = con.createStatement()) {
            try (ResultSet dadosSQL = sqlConsultar.executeQuery(FasesCRMSQL.sqlExAlunos(data, codColaborador, tempoExAluno, empresa, false, tipoVinculos, codigoClientesObjecoes))) {
                while (dadosSQL.next()) {
                    FecharMetaDetalhadoVO detalhado = new FecharMetaDetalhadoVO();
                    detalhado.setCodigoOrigem(dadosSQL.getInt("cliente"));
                    detalhado.setOrigem("CLIENTE");
                    detalhado.getCliente().setCodigo(dadosSQL.getInt("cliente"));
                    detalhado.setDiasSemAgendamento(tempoExAluno.getNrDia());
                    detalhado.setDescconfiguracaodiasmetas(tempoExAluno.getDescricao());
                    obj.getFecharMetaDetalhadoVOs().add(detalhado);
                }
            }
        }
    }

    public void consultarVisitantesAntigos(Date data, FecharMetaVO obj, ConfiguracaoDiasMetasTO tempoExAluno, Integer codColaborador, Integer empresa, String tipoVinculos, String codigoPessoasObjecoes) throws Exception {
        try (Statement sqlConsultar = con.createStatement()) {
            try (ResultSet dadosSQL = sqlConsultar.executeQuery(FasesCRMSQL.sqlVisitantesAntigos(data, codColaborador, tempoExAluno, empresa, false, tipoVinculos, codigoPessoasObjecoes))) {
                List<FecharMetaDetalhadoVO> listaAtual = obj.getFecharMetaDetalhadoVOs();
                while (dadosSQL.next()) {
                    FecharMetaDetalhadoVO detalhado = new FecharMetaDetalhadoVO();
                    detalhado.setCodigoOrigem(dadosSQL.getInt("cliente"));
                    detalhado.setOrigem("CLIENTE");
                    detalhado.getCliente().setCodigo(dadosSQL.getInt("cliente"));
                    detalhado.setDiasSemAgendamento(tempoExAluno.getNrDia());
                    detalhado.setDescconfiguracaodiasmetas(tempoExAluno.getDescricao());

                    boolean existe = false;
                    for (FecharMetaDetalhadoVO detalhadoVO : listaAtual) {
                        if (detalhadoVO.getCliente().getCodigo().equals(detalhado.getCliente().getCodigo())) {
                            existe = true;
                            break;
                        }
                    }
                    if (!existe) {
                        obj.getFecharMetaDetalhadoVOs().add(detalhado);
                    }
                }
            }
        }
    }

    public FecharMetaDetalhadoVO consultarPorFaseIntervaloDataClienteColaborador(Date inicio, Date fim, String identificadorFase, Integer codigoCliente) throws Exception {
        StringBuilder sql = new StringBuilder();
        sql.append("SELECT\n");
        sql.append("  fmd.*\n");
        sql.append("FROM fecharmetadetalhado fmd\n");
        sql.append("  INNER JOIN fecharmeta fm\n");
        sql.append("    ON fmd.fecharmeta = fm.codigo\n");
        sql.append("  INNER JOIN aberturameta am\n");
        sql.append("    ON fm.aberturameta = am.codigo\n");
        if (FasesCRMEnum.INDICACOES.getSigla().equals(identificadorFase)) {
            sql.append("  INNER JOIN indicado ind\n");
            sql.append("    ON fmd.indicado = ind.codigo\n");
        }
        sql.append("WHERE 1 = 1\n");
        sql.append("      AND am.dia :: DATE >= '").append(Uteis.getDataJDBC(inicio)).append("' \n");
        sql.append("      AND am.dia :: DATE <= '").append(Uteis.getDataJDBC(fim)).append("'\n");
        if (FasesCRMEnum.INDICACOES.getSigla().equals(identificadorFase)) {
            sql.append("      AND ind.cliente = ").append(codigoCliente).append("\n");
        } else {
            sql.append("      AND fmd.cliente = ").append(codigoCliente).append("\n");
        }
        sql.append("      AND fm.identificadormeta = '").append(identificadorFase).append("'");

        try (ResultSet consulta = criarConsulta(sql.toString(), con)) {
            if (consulta.next()) {
                return montarDados(consulta, Uteis.NIVELMONTARDADOS_TODOS, con);
            } else {
                return null;
            }
        }
    }

    @Override
    public Double resultadoCRM(Date inicio, Date fim, String fases, Integer empresa) throws Exception {
        StringBuilder sql = new StringBuilder();
        sql.append(" SELECT sum(meta) as meta, sum(metaatingida) as metaatingida FROM fecharmeta fm \n");
        sql.append(" INNER JOIN aberturameta am ON fm.aberturameta = am.codigo \n");
        sql.append(" WHERE am.dia BETWEEN '").append(Uteis.getDataJDBC(inicio)).append(" 00:00:00'");
        sql.append(" AND '").append(Uteis.getDataJDBC(fim)).append(" 23:59:59' \n");
        sql.append("AND fm.identificadormeta IN (").append(fases).append(") \n");
        sql.append("AND meta > 0 and am.empresa = ").append(empresa);

        try (ResultSet rs = criarConsulta(sql.toString(), con)) {
            rs.next();
            if (rs.getDouble("meta") == 0) {
                return null;
            } else {
                return (rs.getDouble("metaatingida") * 100) / rs.getDouble("meta");
            }
        }

    }

    @Override
    public List<FecharMetaDetalhadoVO> consultarMetaDetalhadaPaginado(List<FecharMetaVO> listaFecharMetaVO, boolean metaAgendados,
                                                                      boolean metaAgendadosLigacoes, boolean metaIndicadores,
                                                                      Integer empresa, ConfPaginacao confPaginacao) throws Exception {
        final StringBuilder SQL =
                montarSQLconsultarMetaDetalhada(false)
                        .append(montarClausulaWhereMetaDetalhada(listaFecharMetaVO, metaAgendados, metaIndicadores, metaAgendadosLigacoes, empresa, null))
                        .append(ordenarPorHorarioNome());

        final ResultSet tabelaResultado = consultarPaginado(confPaginacao, SQL.toString());
        try {
            return montarFechamentosMetaDetalhados(tabelaResultado);
        } finally {
            ConsultaUtil.fecharResultSetQuietly(tabelaResultado);
        }
    }

    private String ordenarPorHorarioNome() {
        return " ORDER BY ag.hora, ag.minuto, coalesce(pes.nome, pas.nome)";
    }

    private ResultSet consultarPaginado(final ConfPaginacao confPaginacao, final String sql) throws Exception {
        confPaginacao.configurarNavegacao();
        confPaginacao.iniciarPaginacao(this);
        confPaginacao.addPaginacao(new StringBuffer(sql));
        return confPaginacao.consultaPaginada();
    }

    public List<FecharMetaDetalhadoVO> consultarMetaDetalhada(List<FecharMetaVO> listaFecharMetaVO, boolean metaAgendados,
                                                              boolean metaAgendadosLigacoes, boolean metaIndicadores,
                                                              Integer empresa) throws Exception {
        final StringBuilder SQL =
                montarSQLconsultarMetaDetalhada(false)
                .append(montarClausulaWhereMetaDetalhada(listaFecharMetaVO, metaAgendados, metaIndicadores, metaAgendadosLigacoes, empresa, null));

        final PreparedStatement pst = con.prepareStatement(SQL.toString());
        final ResultSet dadosSQL = pst.executeQuery();

        try {
            return montarFechamentosMetaDetalhados(dadosSQL);
        } finally {
            ConsultaUtil.fecharResultSetQuietly(dadosSQL);
            ConsultaUtil.fecharStatementQuietly(pst);
        }
    }

    private List<FecharMetaDetalhadoVO> montarFechamentosMetaDetalhados(ResultSet dadosSQL) throws Exception {
        final HistoricoContato historicoContato = new HistoricoContato(con);
        final List<FecharMetaDetalhadoVO> fechamentoMetasDetalhado = new ArrayList<FecharMetaDetalhadoVO>();

        while (dadosSQL.next()) {
            FecharMetaDetalhadoVO fecharMetaDetalhadoVO = new FecharMetaDetalhadoVO();

            fecharMetaDetalhadoVO.setClienteCreditoTreino(dadosSQL.getBoolean("validarsaldocreditotreino"));
            fecharMetaDetalhadoVO.setSaldoCreditoTreino(dadosSQL.getInt("saldocreditotreino"));
            fecharMetaDetalhadoVO.setTelefones(dadosSQL.getString("telefones"));
            fecharMetaDetalhadoVO.setEmails(dadosSQL.getString("emails"));

            fecharMetaDetalhadoVO.setCodigo(dadosSQL.getInt("codigodetalhado"));
            fecharMetaDetalhadoVO.setObteveSucesso(dadosSQL.getBoolean("obtevesucesso"));
            fecharMetaDetalhadoVO.setTeveContato(dadosSQL.getBoolean("teveContato"));
            fecharMetaDetalhadoVO.setRepescagem(dadosSQL.getBoolean("repescagem"));
            fecharMetaDetalhadoVO.getFecharMeta().setCodigo(dadosSQL.getInt("fecharmeta"));
            fecharMetaDetalhadoVO.getFecharMeta().setDataRegistro(dadosSQL.getTimestamp("datameta"));
            fecharMetaDetalhadoVO.getFecharMeta().setIdentificadorMeta(dadosSQL.getString("identificadormeta"));
            fecharMetaDetalhadoVO.getFecharMeta().getAberturaMetaVO().setMetaEmAberto(dadosSQL.getBoolean("metaaberta"));
            fecharMetaDetalhadoVO.getFecharMeta().getAberturaMetaVO().getColaboradorResponsavel().setCodigo(dadosSQL.getInt("colaboradorresponsavel"));
            fecharMetaDetalhadoVO.getConfiguracaoDiasPosVendaVO().setCodigo(dadosSQL.getInt("configuracaodiasposvenda"));
            fecharMetaDetalhadoVO.setCodigoOrigem(dadosSQL.getInt("codigoorigem"));
            fecharMetaDetalhadoVO.setOrigem(dadosSQL.getString("origem"));
            fecharMetaDetalhadoVO.setDiasSemAgendamento(dadosSQL.getInt("diassemagendamento"));
            fecharMetaDetalhadoVO.setDescconfiguracaodiasmetas(dadosSQL.getString("descconfiguracaodiasmetas"));
            fecharMetaDetalhadoVO.getCliente().setCodigo(dadosSQL.getInt("cliente"));
            fecharMetaDetalhadoVO.getCliente().getPessoa().setCodigo(dadosSQL.getInt("codigopessoacliente"));
            fecharMetaDetalhadoVO.getCliente().getPessoa().setNome(dadosSQL.getString("nomecliente"));
            fecharMetaDetalhadoVO.getCliente().getPessoa().setDataNasc(dadosSQL.getTimestamp("datanasc"));

            if (!UteisValidacao.emptyNumber(fecharMetaDetalhadoVO.getCliente().getCodigo())) {
                ProbabilidadeEvasao probabilidadeEvasao = new ProbabilidadeEvasao();
                LoginControle loginControle = (LoginControle) context().getExternalContext().getSessionMap().get("LoginControle");
                List<ProbabilidadeEvasaoVO> listaProbEvasao = probabilidadeEvasao.consultarAlunos(0, null, fecharMetaDetalhadoVO.getCliente().getCodigo(), loginControle.getEmpresa().getCodigo(), new ArrayList<>());
                if(listaProbEvasao.size()>0){
                    fecharMetaDetalhadoVO.setTaxaEvasao(String.valueOf(listaProbEvasao.get(0).getChancesair30dias()));
                } else {
                    fecharMetaDetalhadoVO.setTaxaEvasao("Não identificada");
                }
            }
            try {
                fecharMetaDetalhadoVO.getCliente().getPessoa().setFoto(dadosSQL.getBytes("foto"));
            } catch(Exception e) {
            }
            try {
                fecharMetaDetalhadoVO.setObservacaoFilaEsperaTurmaCrm(dadosSQL.getString("observacaoFilaEsperaTurmaCrm"));
            } catch(Exception e) {
            }

            fecharMetaDetalhadoVO.getCliente().getPessoa().setFotoKey(dadosSQL.getString("fotokey"));
            fecharMetaDetalhadoVO.getCliente().getPessoa().setEstadoCivil(dadosSQL.getString("estadocivil"));
            fecharMetaDetalhadoVO.getCliente().setMatricula(dadosSQL.getString("matricula"));
            fecharMetaDetalhadoVO.getCliente().setSituacao(dadosSQL.getString("situacao"));
            fecharMetaDetalhadoVO.getCliente().setSituacaoContrato(dadosSQL.getString("situacaocontrato"));

            ClienteSituacaoVO clienteSituacaoVO = new ClienteSituacaoVO();
            clienteSituacaoVO.getClienteVO().setCodigo(dadosSQL.getInt("cliente"));
            clienteSituacaoVO.setSituacao(dadosSQL.getString("situacao"));
            clienteSituacaoVO.setSubordinadaSituacao(dadosSQL.getString("situacaocontrato"));
            fecharMetaDetalhadoVO.getCliente().getClienteSituacaoVOs().add(clienteSituacaoVO);

            fecharMetaDetalhadoVO.getPassivo().setCodigo(dadosSQL.getInt("passivo"));
            fecharMetaDetalhadoVO.getPassivo().setNome(dadosSQL.getString("nomepassivo"));
            fecharMetaDetalhadoVO.getConversaoLeadVO().setIdentificador(dadosSQL.getString("ldidentificador"));
            fecharMetaDetalhadoVO.getConversaoLeadVO().setCodigo(dadosSQL.getInt("ldcodigo"));
            fecharMetaDetalhadoVO.getConversaoLeadVO().setDataLancamento(dadosSQL.getTimestamp("lddatalancamento"));
            fecharMetaDetalhadoVO.getConversaoLeadVO().setProps(dadosSQL.getString("props"));
            fecharMetaDetalhadoVO.getConversaoLeadVO().getLead().setTipo(TipoLeadEnum.getPorCodigo(dadosSQL.getInt("tipolead")));
            fecharMetaDetalhadoVO.getIndicado().setCodigo(dadosSQL.getInt("indicado"));
            fecharMetaDetalhadoVO.getIndicado().setNomeIndicado(dadosSQL.getString("nomeindicado"));
            fecharMetaDetalhadoVO.getAgenda().setCodigo(dadosSQL.getInt("codigoagenda"));
            fecharMetaDetalhadoVO.getAgenda().setDataAgendamento(dadosSQL.getTimestamp("dataagendamento"));
            fecharMetaDetalhadoVO.getAgenda().setTipoAgendamento(dadosSQL.getString("tipoagendamento"));
            fecharMetaDetalhadoVO.getAgenda().setHora(dadosSQL.getString("hora"));
            fecharMetaDetalhadoVO.getAgenda().setMinuto(dadosSQL.getString("minuto"));

            fecharMetaDetalhadoVO.setDataUltimoContato(historicoContato.obterDataUltimoContato(fecharMetaDetalhadoVO, false));

            fechamentoMetasDetalhado.add(fecharMetaDetalhadoVO);
        }

        return fechamentoMetasDetalhado;
    }

    @Override
    public int contarTotalizacaoMetaRealizadaAgendamentoIndicacoesLigacoes(List<FecharMetaVO> listaFecharMetaVO,
                                                                           Boolean metaAgendamento, boolean metaAgendamentoLigacoes,
                                                                           Boolean metaIndicacoes, Integer codigoEmpresa, Integer codFecharMeta) throws SQLException {
        final StringBuilder SQL =
                montarSQLconsultarMetaDetalhada(true)
                        .append(montarClausulaWhereMetaDetalhada(listaFecharMetaVO, metaAgendamento, metaIndicacoes, metaAgendamentoLigacoes, codigoEmpresa, codFecharMeta));

        if (metaAgendamentoLigacoes) {
            SQL.append(" and fmd.tevecontato = true");
        } else {
            SQL.append(" and fmd.obtevesucesso = true");
        }

        return getTotal(SQL);
    }

    @Override
    public int contarTotalizacaoMetaNaoRealizadaAgendamentoIndicacoes(List<FecharMetaVO> listaFecharMetaVO, Integer codigoEmpresa) throws SQLException {

        final StringBuilder SQL =
                montarSQLconsultarMetaDetalhada(true)
                        .append(montarClausulaWhereMetaDetalhada(listaFecharMetaVO, false, false, false, codigoEmpresa, null))
                        .append(" AND fmd.obtevesucesso = false")
                        .append(" AND fmd.tevecontato = true");

        return getTotal(SQL);
    }

    private int getTotal(StringBuilder SQL) throws SQLException {
        PreparedStatement pst = null;
        ResultSet tabelaResultado = null;

        try {
            pst = con.prepareStatement(SQL.toString());
            tabelaResultado = pst.executeQuery();

            return tabelaResultado != null && tabelaResultado.next() ? tabelaResultado.getInt("total") : 0;
        } finally {
            ConsultaUtil.fecharResultSetQuietly(tabelaResultado);
            ConsultaUtil.fecharStatementQuietly(pst);
        }
    }

    private StringBuilder montarSQLconsultarMetaDetalhada(boolean contar) {
        final StringBuilder SQL_SELECT = new StringBuilder("select ");
        if (contar) {
            SQL_SELECT.append("COUNT(1) as total ");
        } else {
            SQL_SELECT.append("fmd.codigo as codigodetalhado, ")
                    .append("fmd.obtevesucesso, ")
                    .append("fm.codigo as fecharmeta, ")
                    .append("fm.dataregistro as datameta, ")
                    .append("fm.identificadormeta as identificadormeta, ")
                    .append("am.metaemaberto as metaaberta, ")
                    .append("am.colaboradorresponsavel, ")
                    .append("fmd.configuracaodiasposvenda, ")
                    .append("fmd.teveContato, ")
                    .append("fmd.repescagem, ")
                    .append("fmd.codigoorigem, ")
                    .append("fmd.origem, ")
                    .append("fmd.diassemagendamento, ")
                    .append("fmd.descconfiguracaodiasmetas, ")
                    .append("fmd.cliente, ")
                    .append("fmd.observacaoFilaEsperaTurmaCrm, ")
                    .append("ld.identificador as ldidentificador, ")
                    .append("ld.codigo as ldcodigo, ")
                    .append("ld.datalancamento as lddatalancamento, ")
                    .append("ld.props as props,\n")
                    .append("l.tipo as tipolead, \n")
                    .append("pes.codigo as codigopessoacliente, ")
                    .append("cli.matricula as matricula, ")
                    .append("pes.nome as nomecliente, ")
                    .append("pes.datanasc, ")
                    .append("pes.foto, ")
                    .append("pes.fotokey, ")
                    .append("pes.estadocivil, ")
                    .append("fmd.passivo, ")
                    .append("pas.nome as nomepassivo, ")
                    .append("fmd.indicado, ")
                    .append("ind.nomeindicado as nomeindicado, ")
                    .append("ag.codigo as codigoagenda, ")
                    .append("ag.dataagendamento, ")
                    .append("ag.tipoagendamento, ")
                    .append("ag.hora, ")
                    .append("ag.minuto, ")
                    .append("scs.situacao, ")
                    .append("scs.situacaocontrato, ")
                    .append("scs.saldocreditotreino, ")
                    .append("scs.validarsaldocreditotreino, ")
                    .append("(select string_agg(numero, ', ') telefones from telefone where pessoa = pes.codigo and tipotelefone in ('CE','RE')), ")
                    .append("(select string_agg(email, ', ') emails from email where pessoa = pes.codigo and emailcorrespondencia = true and bloqueadobounce IS FALSE ) ");
        }

        return SQL_SELECT.append("FROM fecharmetadetalhado fmd ")
                .append("inner join fecharmeta fm on fm.codigo = fmd.fecharmeta ")
                .append("inner join aberturameta am on am.codigo = fm.aberturameta ")
                .append("left join cliente cli on cli.codigo = fmd.cliente ")
                .append("left join pessoa pes on pes.codigo = cli.pessoa ")
                .append("left join passivo pas on pas.codigo = fmd.passivo ")
                .append("left join indicado ind on ind.codigo = fmd.indicado ")
                .append("left join agenda ag on ag.codigo = fmd.codigoorigem and fmd.origem = 'AGENDA' ")
                .append("left join conversaolead ld on ld.codigo = fmd.codigoorigem and fmd.origem = 'CONVERSAOLEAD' ")
                .append("left join lead l on ld.lead = l.codigo\n")
                .append("left join situacaoclientesinteticodw scs on scs.codigocliente = fmd.cliente ");
    }

    private String montarClausulaWhereMetaDetalhada(List<FecharMetaVO> listaFecharMetaVO, boolean metaAgendados, boolean metaIndicadores, boolean metaAgendadosLigacoes, Integer empresa, Integer codFecharMeta) {
        final StringBuilder SQL = new StringBuilder();
        if(listaFecharMetaVO == null && !UteisValidacao.emptyNumber(codFecharMeta)){
            SQL.append("where fmd.fecharmeta = "+codFecharMeta);
        }else{
            SQL.append("where fmd.fecharmeta in (").append(Uteis.retornarCodigos(listaFecharMetaVO)).append(")");
        }

        if (metaAgendados) {
            SQL.append(" and ag.tipoagendamento <> 'LI'");
        }

        if (metaAgendadosLigacoes) {
            SQL.append(" and ag.tipoagendamento = 'LI'");
        }

        if (empresa != null) {
            SQL.append(" and am.empresa = ").append(empresa);
        }

        return SQL.toString();
    }


    public boolean isAgendadoLigacao(Integer codigoFecharMetaDetalhado) throws Exception {
        String sql = "select fd.codigo from fecharmetadetalhado fd INNER JOIN agenda ag on ag.codigo = fd.codigoorigem and origem = 'AGENDA'  where fd.codigo = " + codigoFecharMetaDetalhado + " and ag.tipoagendamento = 'LI'";
        return existe(sql, this.con);
    }

    private ResultSet sqlConsultarIndicadores(List<FecharMetaVO> listaFecharMetaVO, boolean agendamentosLigacoes, boolean indicacoesSemContato, boolean count, boolean biIndicadorLigacaoPendente) throws Exception {
        final StringBuilder sql =
                montarSQLIndicadores(listaFecharMetaVO, agendamentosLigacoes, indicacoesSemContato, count)
                        .append(montarClausulaWhereIndicadores(agendamentosLigacoes, indicacoesSemContato, biIndicadorLigacaoPendente));
        return criarConsulta(sql.toString(), con);
    }

    private StringBuilder montarSQLIndicadores(List<FecharMetaVO> listaFecharMetaVO, boolean agendamentosLigacoes, boolean indicacoesSemContato, boolean count) {
        String codigos = Uteis.retornarCodigos(listaFecharMetaVO);
        StringBuilder sql = new StringBuilder();

        if (count) {
            sql.append("select count(d.*) as total \n");
        } else {
            sql.append("select d.* \n");
        }

        sql.append("from fecharmetadetalhado d \n");

        if (agendamentosLigacoes) {
            sql.append("left join agenda a on a.codigo = d.codigoorigem and d.origem = 'AGENDA' \n");
        }


        if (indicacoesSemContato) {
            sql.append("inner join fecharmeta fm on fm.codigo = d.fecharmeta and fm.identificadormeta = 'IN' \n");
        }

        sql.append("inner join (").append(Uteis.criarTabelaIn(codigos)).append(")sqlIn on sqlIn.codigo = d.fecharMeta");

        return sql;
    }

    private String montarClausulaWhereIndicadores(boolean agendamentosLigacoes, boolean indicacoesSemContato,
                                                boolean biIndicadorLigacaoPendente) {
        StringBuilder sql = new StringBuilder();
        if (agendamentosLigacoes) {
            sql.append("\n where a.tipoagendamento = 'LI'");
        }

        if (indicacoesSemContato) {
            sql.append("\n where d.obtevesucesso = false");
        }

        if (biIndicadorLigacaoPendente) {
            sql.append("\n and d.tevecontato = false");
        }
        return sql.toString();
    }

    public Integer contarIndicadores(List<FecharMetaVO> listaFecharMetaVO, boolean agendamentosLigacoes, boolean indicacoesSemContato, boolean biIndicadorLigacaoPendente) throws Exception {
        try (ResultSet rs = sqlConsultarIndicadores(listaFecharMetaVO, agendamentosLigacoes, indicacoesSemContato, true, biIndicadorLigacaoPendente)) {
            if (rs.next()) {
                return rs.getInt("total");
            }
        }
        return 0;
    }

    public List<FecharMetaDetalhadoVO> consultarIndicadores(List<FecharMetaVO> listaFecharMetaVO, boolean agendamentosLigacoes, boolean indicacoesSemContato, boolean biIndicadorLigacaoPendente, int nivelMontarDados) throws Exception {
        try (ResultSet rs = sqlConsultarIndicadores(listaFecharMetaVO, agendamentosLigacoes, indicacoesSemContato, false, biIndicadorLigacaoPendente)) {
            return montarDadosConsulta(rs, nivelMontarDados, con);
        }
    }

    public int contarTotalizacaoMetaNaoRealizadaIndicadores(List<FecharMetaVO> listaFecharMetaVO) throws Exception {
        final String SQL = montarSQLIndicadores(
                listaFecharMetaVO,
                false,
                true,
                true)
                .append(montarClausulaWhereIndicadores(false, true, false))
                .append(" AND d.teveContato = true")
                .toString();

        try (ResultSet tabelaResultado = criarConsulta(SQL, con)) {
            return tabelaResultado != null && tabelaResultado.next() ? tabelaResultado.getInt("total") : 0;
        }
    }

    public void alterarSomenteCampoObteveSucesso(boolean obteveSucesso, Integer codigoFecharMetaDetalhado) throws Exception {
        String sql = "UPDATE FecharMetaDetalhado SET obteveSucesso = ?  WHERE codigo = ?";
        if (obteveSucesso) {
            sql = "UPDATE FecharMetaDetalhado SET teveContato = true, obteveSucesso = ?  WHERE codigo = ?";
        }
        try (PreparedStatement sqlAlterar = con.prepareStatement(sql)) {
            sqlAlterar.setBoolean(1, obteveSucesso);
            sqlAlterar.setInt(2, codigoFecharMetaDetalhado);
            sqlAlterar.execute();
        }
    }

    public void alterarSomenteCampoRepescagemFecharMetaDetalhado(Boolean repescagem, Integer codigoFecharMetaDetalhado) throws Exception {
        alterarCRM(getIdEntidade());
        String sql = "UPDATE FecharMetaDetalhado SET repescagem = ? WHERE codigo = ?";
        try (PreparedStatement sqlAlterar = con.prepareStatement(sql)) {
            sqlAlterar.setBoolean(1, repescagem);
            sqlAlterar.setInt(2, codigoFecharMetaDetalhado);
            sqlAlterar.execute();
        }
    }


    public String inicializarDadosEnviarSMSColetivoNovoCRM(TipoMetaCRMTO tipoMetaCRMTO, MalaDiretaVO malaDiretaVO, UsuarioVO usuarioLogado) throws Exception {
        String retorno = "OK";

        malaDiretaVO.setNovoObj(true);
        malaDiretaVO.setTitulo("");
        malaDiretaVO.setMensagem("");
        malaDiretaVO.setModeloMensagem(new ModeloMensagemVO());
        malaDiretaVO.setRemetente(usuarioLogado);
        malaDiretaVO.setmalaDiretaEnviadaVOs(new ArrayList<MalaDiretaEnviadaVO>());
        malaDiretaVO.setFaseEnvio(tipoMetaCRMTO.getFasesCRMEnum().getSigla());
        malaDiretaVO.setCodAberturaMeta(tipoMetaCRMTO.getListaFecharMetaVO().get(0).getAberturaMetaVO().getCodigo());

        StringBuilder pessoasSemCelular = new StringBuilder();

        MalaDiretaEnviadaVO malaDireta;
        if (!UteisValidacao.emptyList(tipoMetaCRMTO.getListaMetaDetalhada())) {
            for (FecharMetaDetalhadoVO metaDetalhado : tipoMetaCRMTO.getListaMetaDetalhada()) {
                if (metaDetalhado.getEnviarEmailSMS() && metaDetalhado.getPassivo().getCodigo() != 0) {
                    //PASSIVO ---- RECEPTIVO
                    metaDetalhado.setPassivo(getFacade().getPassivo().consultarPorChavePrimaria(metaDetalhado.getPassivo().getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS));
                    if (Uteis.validarTelefoneCelular(metaDetalhado.getPassivo().getTelefoneCelular())) {
                        malaDireta = new MalaDiretaEnviadaVO();
                        malaDireta.setPassivoVO(metaDetalhado.getPassivo());
                        malaDiretaVO.setRemetente(usuarioLogado);
                        malaDiretaVO.getMalaDiretaEnviadaVOs().add(malaDireta);
                        malaDiretaVO.setTotalPessoaMalaDireta(malaDiretaVO.getMalaDiretaEnviadaVOs().size());
                    }
                } else if (metaDetalhado.getEnviarEmailSMS() && metaDetalhado.getIndicado().getCodigo() != 0) {
                    //INDICADO
                    metaDetalhado.setIndicado(getFacade().getIndicado().consultarPorChavePrimaria(metaDetalhado.getIndicado().getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS));
                    if (Uteis.validarTelefoneCelular(metaDetalhado.getIndicado().getTelefoneIndicado())) {
                        malaDireta = new MalaDiretaEnviadaVO();
                        malaDireta.setIndicadoVO(metaDetalhado.getIndicado());
                        malaDiretaVO.setRemetente(usuarioLogado);
                        malaDiretaVO.getMalaDiretaEnviadaVOs().add(malaDireta);
                        malaDiretaVO.setTotalPessoaMalaDireta(malaDiretaVO.getMalaDiretaEnviadaVOs().size());
                    }
                } else if (metaDetalhado.getEnviarEmailSMS() && metaDetalhado.getCliente().getCodigo() != 0) {
                    metaDetalhado.getCliente().getPessoa().setTelefoneVOs(getFacade().getTelefone().consultarTelefones(metaDetalhado.getCliente().getPessoa().getCodigo(), false, Uteis.NIVELMONTARDADOS_TODOS));
                    boolean temCelular = false;
                    for (TelefoneVO telefoneVO : metaDetalhado.getCliente().getPessoa().getTelefoneVOs()) {
                        if (telefoneVO.getTipoTelefone().equals("CE") && Uteis.validarTelefoneCelular(telefoneVO.getNumero())) {
                            temCelular = true;
                        }
                    }
                    if (temCelular) {
                        malaDireta = new MalaDiretaEnviadaVO();
                        metaDetalhado.getCliente().setPessoa(getFacade().getPessoa().consultarPorChavePrimaria(metaDetalhado.getCliente().getPessoa().getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS));
                        malaDireta.getClienteVO().setPessoa(metaDetalhado.getCliente().getPessoa());
                        malaDireta.getClienteVO().setCodigo(metaDetalhado.getCliente().getCodigo());
                        malaDiretaVO.setRemetente(usuarioLogado);
                        malaDiretaVO.getMalaDiretaEnviadaVOs().add(malaDireta);
                        malaDiretaVO.setTotalPessoaMalaDireta(malaDiretaVO.getMalaDiretaEnviadaVOs().size());
                    } else {
                        pessoasSemCelular.append(metaDetalhado.getNomePessoaRel()).append("<br>");
                    }
                }
            }
            if (!UteisValidacao.emptyString(pessoasSemCelular.toString())) {
                retorno = ("As seguintes pessoas não foram adicionadas pois não tem celular cadastrado: <br>" + pessoasSemCelular.toString());
                retorno = retorno.substring(0, retorno.length() - 4);
                return  retorno;
            }

            if (UteisValidacao.emptyList(malaDiretaVO.getMalaDiretaEnviadaVOs())) {
                retorno = ("Não existe nenhuma pessoa selecionada para o envio de SMS !");
                return  retorno;
            }
        } else {
            retorno = ("Não existe nenhuma pessoa selecionada para o envio de SMS !");
        }
        return retorno;
    }

    public String inicializarDadosEnviarEmailColetivoNovoCRM(TipoMetaCRMTO tipoMetaCRMTO, MalaDiretaVO malaDiretaVO, UsuarioVO usuarioLogado) throws Exception {
        String retorno = "OK";
        malaDiretaVO.setNovoObj(true);
        malaDiretaVO.setTitulo("");
        malaDiretaVO.setMensagem("");
        malaDiretaVO.setModeloMensagem(new ModeloMensagemVO());
        malaDiretaVO.setRemetente(usuarioLogado);
        malaDiretaVO.setmalaDiretaEnviadaVOs(new ArrayList<MalaDiretaEnviadaVO>());
        malaDiretaVO.setFaseEnvio(tipoMetaCRMTO.getFasesCRMEnum().getSigla());
        malaDiretaVO.setCodAberturaMeta(tipoMetaCRMTO.getListaFecharMetaVO().get(0).getAberturaMetaVO().getCodigo());

        StringBuilder pessoasSemEmail = new StringBuilder();

        MalaDiretaEnviadaVO malaDireta;
        if (!UteisValidacao.emptyList(tipoMetaCRMTO.getListaMetaDetalhada())) {
            for (FecharMetaDetalhadoVO metaDetalhado : tipoMetaCRMTO.getListaMetaDetalhada()) {
                if (metaDetalhado.getEnviarEmailSMS() && metaDetalhado.getPassivo().getCodigo() != 0) {
                    //PASSIVO ---- RECEPTIVO
                    metaDetalhado.setPassivo(getFacade().getPassivo().consultarPorChavePrimaria(metaDetalhado.getPassivo().getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS));
                    if (!UteisValidacao.emptyString(metaDetalhado.getPassivo().getEmail())) {
                        malaDireta = new MalaDiretaEnviadaVO();
                        malaDireta.setPassivoVO(metaDetalhado.getPassivo());
                        malaDiretaVO.setRemetente(usuarioLogado);
                        malaDiretaVO.getMalaDiretaEnviadaVOs().add(malaDireta);
                        malaDiretaVO.setTotalPessoaMalaDireta((malaDiretaVO.getMalaDiretaEnviadaVOs().size()));
                    } else {
                        pessoasSemEmail.append(metaDetalhado.getNomePessoaRel()).append("<br>");
                    }
                } else if (metaDetalhado.getEnviarEmailSMS() && metaDetalhado.getIndicado().getCodigo() != 0) {
                    //INDICADO
                    metaDetalhado.setIndicado(getFacade().getIndicado().consultarPorChavePrimaria(metaDetalhado.getIndicado().getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS));
                    if (!UteisValidacao.emptyString(metaDetalhado.getIndicado().getEmail())) {
                        malaDireta = new MalaDiretaEnviadaVO();
                        malaDireta.setIndicadoVO(metaDetalhado.getIndicado());
                        malaDiretaVO.setRemetente(usuarioLogado);
                        malaDiretaVO.getMalaDiretaEnviadaVOs().add(malaDireta);
                        malaDiretaVO.setTotalPessoaMalaDireta(malaDiretaVO.getMalaDiretaEnviadaVOs().size());
                    } else {
                        pessoasSemEmail.append(metaDetalhado.getNomePessoaRel()).append("<br>");
                    }
                } else if (metaDetalhado.getEnviarEmailSMS() && metaDetalhado.getCliente().getCodigo() != 0) {
                    //CLIENTE
                    metaDetalhado.getCliente().getPessoa().setEmailVOs(getFacade().getEmail().consultarEmails(metaDetalhado.getCliente().getPessoa().getCodigo(), false, Uteis.NIVELMONTARDADOS_TODOS));
                    if (!UteisValidacao.emptyList(metaDetalhado.getCliente().getPessoa().getEmailVOs())) {
                        malaDireta = new MalaDiretaEnviadaVO();
                        metaDetalhado.getCliente().setPessoa(getFacade().getPessoa().consultarPorChavePrimaria(metaDetalhado.getCliente().getPessoa().getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS));
                        malaDireta.getClienteVO().setPessoa(metaDetalhado.getCliente().getPessoa());
                        malaDireta.getClienteVO().setCodigo(metaDetalhado.getCliente().getCodigo());
                        malaDiretaVO.setRemetente(usuarioLogado);
                        malaDiretaVO.getMalaDiretaEnviadaVOs().add(malaDireta);
                        malaDiretaVO.setTotalPessoaMalaDireta(malaDiretaVO.getMalaDiretaEnviadaVOs().size());
                    } else {
                        pessoasSemEmail.append(metaDetalhado.getNomePessoaRel()).append("<br>");
                    }
                }
            }

            if (!UteisValidacao.emptyString(pessoasSemEmail.toString())) {
                retorno = ("As seguintes pessoas não foram adicionadas pois não tem email cadastrado: <br>" + pessoasSemEmail.toString());
                retorno = retorno.substring(0, retorno.length() - 4);
                return retorno;
            }

            if (UteisValidacao.emptyList(malaDiretaVO.getMalaDiretaEnviadaVOs())) {
                retorno = ("Não existe nenhuma pessoa selecionada para o envio de E-mail !");
                return retorno;
            }

        } else {
            retorno = ("Não existe nenhuma pessoa selecionada para o envio de E-mail !");
        }
        return retorno;
    }


    public List<FecharMetaDetalhadoVO> consultarMetaDetalhadaTodasFases(List<FecharMetaVO> listaFecharMetaVO, String filtro) throws Exception {
        String codigos = Uteis.retornarCodigos(listaFecharMetaVO);
        StringBuilder sql = new StringBuilder();

        sql.append("select\n");
        sql.append("fmd.codigo as codigodetalhado,\n");
        sql.append("fmd.obtevesucesso,\n");
        sql.append("fm.codigo as fecharmeta,\n");
        sql.append("fm.dataregistro as datameta,\n");
        sql.append("fm.identificadormeta as identificadormeta,\n");
        sql.append("am.metaemaberto as metaaberta,\n");
        sql.append("fmd.configuracaodiasposvenda,\n");
        sql.append("fmd.codigoorigem,\n");
        sql.append("fmd.origem,\n");
        sql.append("fmd.cliente,\n");
        sql.append("pes.codigo as codigopessoacliente,\n");
        sql.append("cli.matricula as matricula,\n");
        sql.append("pes.nome as nomecliente,\n");
        sql.append("pes.datanasc,\n");
        sql.append("pes.foto,\n");
        sql.append("pes.fotokey,\n");
        sql.append("pes.estadocivil,\n");
        sql.append("fmd.passivo,\n");
        sql.append("pas.nome as nomepassivo,\n");
        sql.append("fmd.indicado,\n");
        sql.append("ind.nomeindicado as nomeindicado,\n");
        sql.append("ag.codigo as codigoagenda,\n");
        sql.append("ag.dataagendamento,\n");
        sql.append("ag.tipoagendamento,\n");
        sql.append("ag.hora,\n");
        sql.append("ag.minuto,\n");
        sql.append("scs.situacao,\n");
        sql.append("scs.situacaocontrato\n");
        sql.append("from fecharmetadetalhado fmd\n");
        sql.append("inner join fecharmeta fm on fm.codigo = fmd.fecharmeta\n");
        sql.append("inner join aberturameta am on am.codigo = fm.aberturameta\n");
        sql.append("left join cliente cli on cli.codigo = fmd.cliente\n");
        sql.append("left join pessoa pes on pes.codigo = cli.pessoa\n");
        sql.append("left join passivo pas on pas.codigo = fmd.passivo\n");
        sql.append("left join indicado ind on ind.codigo = fmd.indicado\n");
        sql.append("left join agenda ag on ag.codigo = fmd.codigoorigem and fmd.origem = 'AGENDA'\n");
        sql.append("left join situacaoclientesinteticodw scs on scs.codigocliente = fmd.cliente\n");
        sql.append("where fmd.fecharmeta in (").append(codigos).append(")\n");

        sql.append("AND fm.identificadorMeta not in('").append(FasesCRMEnum.CONVERSAO_INDICADOS.getSigla())
                .append("','").append(FasesCRMEnum.CONVERSAO_AGENDADOS.getSigla())
                .append("','").append(FasesCRMEnum.CONVERSAO_EX_ALUNOS.getSigla())
                .append("','").append(FasesCRMEnum.CONVERSAO_VISITANTES_ANTIGOS.getSigla())
                .append("','").append(FasesCRMEnum.CONVERSAO_DESISTENTES.getSigla()).append("')\n");


        sql.append("AND (pes.nome ilike '%").append(filtro).append("%'\n");
        sql.append("OR pas.nome ilike '%").append(filtro).append("%'\n");
        sql.append("OR ind.nomeindicado ilike '%").append(filtro).append("%'\n");
        sql.append("OR cli.matricula ilike '%").append(filtro).append("%')\n");

        sql.append("ORDER BY fm.dataregistro desc");


        List<FecharMetaDetalhadoVO> listaRetornar;
        try (PreparedStatement pst = con.prepareStatement(sql.toString())) {
            try (ResultSet dadosSQL = pst.executeQuery()) {

                listaRetornar = new ArrayList<FecharMetaDetalhadoVO>();
                while (dadosSQL.next()) {
                    FecharMetaDetalhadoVO fecharMetaDetalhadoVO = new FecharMetaDetalhadoVO();

                    fecharMetaDetalhadoVO.setCodigo(dadosSQL.getInt("codigodetalhado"));
                    fecharMetaDetalhadoVO.setObteveSucesso(dadosSQL.getBoolean("obtevesucesso"));
                    fecharMetaDetalhadoVO.getFecharMeta().setCodigo(dadosSQL.getInt("fecharmeta"));
                    fecharMetaDetalhadoVO.getFecharMeta().setDataRegistro(dadosSQL.getTimestamp("datameta"));
                    fecharMetaDetalhadoVO.getFecharMeta().setIdentificadorMeta(dadosSQL.getString("identificadormeta"));
                    fecharMetaDetalhadoVO.getFecharMeta().getAberturaMetaVO().setMetaEmAberto(dadosSQL.getBoolean("metaaberta"));
                    fecharMetaDetalhadoVO.getConfiguracaoDiasPosVendaVO().setCodigo(dadosSQL.getInt("configuracaodiasposvenda"));
                    fecharMetaDetalhadoVO.setCodigoOrigem(dadosSQL.getInt("codigoorigem"));
                    fecharMetaDetalhadoVO.setOrigem(dadosSQL.getString("origem"));
                    fecharMetaDetalhadoVO.getCliente().setCodigo(dadosSQL.getInt("cliente"));
                    fecharMetaDetalhadoVO.getCliente().getPessoa().setCodigo(dadosSQL.getInt("codigopessoacliente"));
                    fecharMetaDetalhadoVO.getCliente().getPessoa().setNome(dadosSQL.getString("nomecliente"));
                    fecharMetaDetalhadoVO.getCliente().getPessoa().setDataNasc(dadosSQL.getTimestamp("datanasc"));
                    try {
                        fecharMetaDetalhadoVO.getCliente().getPessoa().setFoto(dadosSQL.getBytes("foto"));
                    } catch (Exception ex) {
                    }
                    fecharMetaDetalhadoVO.getCliente().getPessoa().setFotoKey(dadosSQL.getString("fotokey"));
                    fecharMetaDetalhadoVO.getCliente().getPessoa().setEstadoCivil(dadosSQL.getString("estadocivil"));
                    fecharMetaDetalhadoVO.getCliente().setMatricula(dadosSQL.getString("matricula"));
                    fecharMetaDetalhadoVO.getCliente().setSituacao(dadosSQL.getString("situacao"));
                    fecharMetaDetalhadoVO.getCliente().setSituacaoContrato(dadosSQL.getString("situacaocontrato"));

                    ClienteSituacaoVO clienteSituacaoVO = new ClienteSituacaoVO();
                    clienteSituacaoVO.getClienteVO().setCodigo(dadosSQL.getInt("cliente"));
                    clienteSituacaoVO.setSituacao(dadosSQL.getString("situacao"));
                    clienteSituacaoVO.setSubordinadaSituacao(dadosSQL.getString("situacaocontrato"));
                    fecharMetaDetalhadoVO.getCliente().getClienteSituacaoVOs().add(clienteSituacaoVO);

                    fecharMetaDetalhadoVO.getPassivo().setCodigo(dadosSQL.getInt("passivo"));
                    fecharMetaDetalhadoVO.getPassivo().setNome(dadosSQL.getString("nomepassivo"));
                    fecharMetaDetalhadoVO.getIndicado().setCodigo(dadosSQL.getInt("indicado"));
                    fecharMetaDetalhadoVO.getIndicado().setNomeIndicado(dadosSQL.getString("nomeindicado"));
                    fecharMetaDetalhadoVO.getAgenda().setCodigo(dadosSQL.getInt("codigoagenda"));
                    fecharMetaDetalhadoVO.getAgenda().setDataAgendamento(dadosSQL.getTimestamp("dataagendamento"));
                    fecharMetaDetalhadoVO.getAgenda().setTipoAgendamento(dadosSQL.getString("tipoagendamento"));
                    fecharMetaDetalhadoVO.getAgenda().setHora(dadosSQL.getString("hora"));
                    fecharMetaDetalhadoVO.getAgenda().setMinuto(dadosSQL.getString("minuto"));

                    listaRetornar.add(fecharMetaDetalhadoVO);
                }
            }
        }

        return listaRetornar;
    }

    public void alterarSomenteTeveContato(Integer codigoFecharMetaDetalhado) throws Exception {
        alterarCRM(getIdEntidade());
        String sql = "UPDATE FecharMetaDetalhado set teveContato = true WHERE codigo = ?";
        try (PreparedStatement sqlAlterar = con.prepareStatement(sql)) {
            sqlAlterar.setInt(1, codigoFecharMetaDetalhado);
            sqlAlterar.execute();
        }
    }

    public boolean verificarDetalhadoMetaBatidaCRMExtra(Integer malaDireta, Integer codCliente) throws Exception {

        StringBuffer sqlStr = new StringBuffer();
        sqlStr.append("SELECT EXISTS(select \n");
        sqlStr.append("fmd.codigo, \n");
        sqlStr.append("fm.codigo as fecharmeta, \n");
        sqlStr.append("fmd.cliente, \n");
        sqlStr.append("fmd.obtevesucesso \n");
        sqlStr.append("from fecharmetadetalhado fmd \n");
        sqlStr.append("inner join fecharmeta fm on fm.codigo = fmd.fecharmeta \n");
        sqlStr.append("where fm.maladireta = ").append(malaDireta).append(" \n");
        sqlStr.append("and fmd.cliente = ").append(codCliente).append(" \n");
        sqlStr.append("and fmd.obtevesucesso = true)");

        try (Statement stm = con.createStatement()) {
            try (ResultSet tabelaResultado = stm.executeQuery(sqlStr.toString())) {
                tabelaResultado.next();
                return tabelaResultado.getBoolean(1);
            }
        }
    }

    public List<FecharMetaDetalhadoVO> consultarListaBICRM(EmpresaVO empresa, Date dataInicio, Date dataFinal, List<UsuarioVO> usuarioVOList, String identificadorMeta, boolean somenteObteveSucesso, boolean somenteRepescagem, boolean total, boolean metaAtingidaBIResultado, List<FecharMetaVO> fecharMetaCRMExtra) throws Exception {
        String codigosUsuarios = Uteis.retornarCodigos(usuarioVOList);
        boolean conversao = false;

        //SÓ IRÁ BUSCAR PELO CODIGO CASO SEJA CRM-EXTRA CASO CONTRÁRIO SETAR COMO 0 PARA NÃO SER UTILIZADO
        if (!identificadorMeta.equals(FasesCRMEnum.CRM_EXTRA.getSigla())){
            fecharMetaCRMExtra = new ArrayList<FecharMetaVO>();
        }

        if (!metaAtingidaBIResultado) {
            if (identificadorMeta.equals(FasesCRMEnum.CONVERSAO_INDICADOS.getSigla())) {
                identificadorMeta = FasesCRMEnum.INDICACOES.getSigla();
                somenteObteveSucesso = true;
                conversao = true;
            } else if (identificadorMeta.equals(FasesCRMEnum.CONVERSAO_AGENDADOS.getSigla())) {
                identificadorMeta = FasesCRMEnum.AGENDAMENTO.getSigla();
                somenteObteveSucesso = true;
                conversao = true;
            } else if (identificadorMeta.equals(FasesCRMEnum.CONVERSAO_EX_ALUNOS.getSigla())) {
                identificadorMeta = FasesCRMEnum.EX_ALUNOS.getSigla();
                somenteObteveSucesso = true;
                conversao = true;
            } else if (identificadorMeta.equals(FasesCRMEnum.CONVERSAO_VISITANTES_ANTIGOS.getSigla())) {
                identificadorMeta = FasesCRMEnum.VISITANTES_ANTIGOS.getSigla();
                somenteObteveSucesso = true;
                conversao = true;
            } else if (identificadorMeta.equals(FasesCRMEnum.CONVERSAO_DESISTENTES.getSigla())) {
                identificadorMeta = FasesCRMEnum.DESISTENTES.getSigla();
                somenteObteveSucesso = true;
                conversao = true;
            }
        }

        if (total) {
            Integer tipoFase = 0;
            for (FasesCRMEnum fasesCRMEnum : FasesCRMEnum.values()) {
                if (identificadorMeta.equals(fasesCRMEnum.getSigla())) {
                    tipoFase = fasesCRMEnum.getTipoFase().getCodigo();
                    break;
                }
            }

            if (tipoFase.equals(TipoFaseCRM.VENDAS.getCodigo())) {
                identificadorMeta = "('AG','LA','HO','RE','PE','VA','EX')";
            } else if (tipoFase.equals(TipoFaseCRM.RETENCAO.getCodigo())) {
                identificadorMeta = "('RI','VE','PV','FA','AN')";
            } else if (tipoFase.equals(TipoFaseCRM.CRMEXTRA.getCodigo())) {
                identificadorMeta = "('CR')";
            } else if (tipoFase.equals(TipoFaseCRM.ESTUDIO.getCodigo())) {
                identificadorMeta = "('SF','SA')";
            }
        } else {
            identificadorMeta = "('" + identificadorMeta + "')";
        }

        StringBuilder sql = new StringBuilder();
        sql.append("select \n");
        sql.append("fmd.codigo as codigodetalhado, \n");
        sql.append("fmd.obtevesucesso, \n");
        sql.append("fm.codigo as fecharmeta, \n");
        sql.append("fm.dataregistro as datameta, \n");
        sql.append("fm.identificadormeta as identificadormeta, \n");
        sql.append("am.metaemaberto as metaaberta, \n");
        sql.append("fmd.configuracaodiasposvenda, \n");
        sql.append("fmd.codigoorigem,\n");
        sql.append("fmd.origem,\n");
        sql.append("fmd.teveContato, \n");
        sql.append("fmd.repescagem, \n");
        sql.append("fmd.cliente, \n");
        sql.append("pes.codigo as codigopessoacliente, \n");
        sql.append("cli.matricula as matricula, \n");
        sql.append("pes.nome as nomecliente, \n");
        sql.append("pes.datanasc, \n");
        sql.append("pes.estadocivil, \n");
        sql.append("fmd.passivo, \n");
        sql.append("pas.nome as nomepassivo, \n");
        sql.append("fmd.indicado, \n");
        sql.append("ind.nomeindicado as nomeindicado, \n");
        sql.append("ag.codigo as codigoagenda,\n");
        sql.append("ag.dataagendamento,\n");
        sql.append("ag.tipoagendamento,\n");
        sql.append("ag.hora,\n");
        sql.append("ag.minuto,\n");
        sql.append("scs.situacao, \n");
        sql.append("scs.situacaocontrato \n");
        sql.append("from fecharmetadetalhado  fmd \n");
        sql.append("inner join fecharmeta fm on fm.codigo = fmd.fecharmeta \n");
        sql.append("inner join aberturameta am on am.codigo = fm.aberturameta \n");
        sql.append("left join cliente cli on cli.codigo = fmd.cliente \n");
        sql.append("left join pessoa pes on pes.codigo = cli.pessoa \n");
        sql.append("left join passivo pas on pas.codigo = fmd.passivo \n");
        sql.append("left join indicado ind on ind.codigo = fmd.indicado \n");
        sql.append("left join situacaoclientesinteticodw scs on scs.codigocliente = fmd.cliente \n");
        sql.append("left join agenda ag on ag.codigo = fmd.codigoorigem and fmd.origem = 'AGENDA' \n");
        sql.append("where am.dia::date between '").append(Uteis.getData(dataInicio)).append("' and '").append(Uteis.getData(dataFinal)).append("' \n");
        sql.append("and am.colaboradorresponsavel in (").append(codigosUsuarios).append(") \n");
        sql.append("and fm.identificadormeta in ").append(identificadorMeta).append(" \n");
        if (somenteObteveSucesso) {
            sql.append("and fmd.obtevesucesso = true \n");
        }
        if (somenteRepescagem) {
            sql.append("and fmd.repescagem = true \n");
        }

        if (somenteObteveSucesso && !somenteRepescagem && !conversao) {
            sql.append("and fmd.repescagem = false \n");
        }

        if (identificadorMeta.contains(FasesCRMEnum.AGENDAMENTO.getSigla())) {
            sql.append("and (ag.tipoagendamento = 'AE' or ag.tipoagendamento = 'VI' or ag.tipoagendamento is null) \n");
        }
        
        String codigosFecharMetaCRMExtra = Uteis.retornarCodigos(fecharMetaCRMExtra);
        if (!UteisValidacao.emptyList(fecharMetaCRMExtra)) {
            sql.append("and fm.codigo in (").append(codigosFecharMetaCRMExtra).append(") \n");
        }
        if (empresa != null && empresa.getCodigo() != null) {
            sql.append(" AND am.empresa = " + empresa.getCodigo());
        }

        List<FecharMetaDetalhadoVO> listaRetornar;
        try (PreparedStatement pst = con.prepareStatement(sql.toString())) {
            try (ResultSet dadosSQL = pst.executeQuery()) {

                listaRetornar = new ArrayList<FecharMetaDetalhadoVO>();
                while (dadosSQL.next()) {
                    FecharMetaDetalhadoVO fecharMetaDetalhadoVO = new FecharMetaDetalhadoVO();

                    fecharMetaDetalhadoVO.setCodigo(dadosSQL.getInt("codigodetalhado"));
                    fecharMetaDetalhadoVO.setObteveSucesso(dadosSQL.getBoolean("obtevesucesso"));
                    fecharMetaDetalhadoVO.setTeveContato(dadosSQL.getBoolean("teveContato"));
                    fecharMetaDetalhadoVO.setRepescagem(dadosSQL.getBoolean("repescagem"));
                    fecharMetaDetalhadoVO.getFecharMeta().setCodigo(dadosSQL.getInt("fecharmeta"));
                    fecharMetaDetalhadoVO.getFecharMeta().setDataRegistro(dadosSQL.getTimestamp("datameta"));
                    fecharMetaDetalhadoVO.getFecharMeta().setIdentificadorMeta(dadosSQL.getString("identificadormeta"));
                    fecharMetaDetalhadoVO.getFecharMeta().getAberturaMetaVO().setMetaEmAberto(dadosSQL.getBoolean("metaaberta"));
                    fecharMetaDetalhadoVO.getConfiguracaoDiasPosVendaVO().setCodigo(dadosSQL.getInt("configuracaodiasposvenda"));
                    fecharMetaDetalhadoVO.setCodigoOrigem(dadosSQL.getInt("codigoorigem"));
                    fecharMetaDetalhadoVO.setOrigem(dadosSQL.getString("origem"));
                    fecharMetaDetalhadoVO.getCliente().setCodigo(dadosSQL.getInt("cliente"));
                    fecharMetaDetalhadoVO.getCliente().getPessoa().setCodigo(dadosSQL.getInt("codigopessoacliente"));
                    fecharMetaDetalhadoVO.getCliente().getPessoa().setNome(dadosSQL.getString("nomecliente"));
                    fecharMetaDetalhadoVO.getCliente().getPessoa().setDataNasc(dadosSQL.getTimestamp("datanasc"));
                    fecharMetaDetalhadoVO.getCliente().getPessoa().setEstadoCivil(dadosSQL.getString("estadocivil"));
                    fecharMetaDetalhadoVO.getCliente().setMatricula(dadosSQL.getString("matricula"));
                    fecharMetaDetalhadoVO.getCliente().setSituacao(dadosSQL.getString("situacao"));
                    fecharMetaDetalhadoVO.getCliente().setSituacaoContrato(dadosSQL.getString("situacaocontrato"));

                    ClienteSituacaoVO clienteSituacaoVO = new ClienteSituacaoVO();
                    clienteSituacaoVO.getClienteVO().setCodigo(dadosSQL.getInt("cliente"));
                    clienteSituacaoVO.setSituacao(dadosSQL.getString("situacao"));
                    clienteSituacaoVO.setSubordinadaSituacao(dadosSQL.getString("situacaocontrato"));
                    fecharMetaDetalhadoVO.getCliente().getClienteSituacaoVOs().add(clienteSituacaoVO);

                    fecharMetaDetalhadoVO.getPassivo().setCodigo(dadosSQL.getInt("passivo"));
                    fecharMetaDetalhadoVO.getPassivo().setNome(dadosSQL.getString("nomepassivo"));
                    fecharMetaDetalhadoVO.getIndicado().setCodigo(dadosSQL.getInt("indicado"));
                    fecharMetaDetalhadoVO.getIndicado().setNomeIndicado(dadosSQL.getString("nomeindicado"));
                    fecharMetaDetalhadoVO.getAgenda().setCodigo(dadosSQL.getInt("codigoagenda"));
                    fecharMetaDetalhadoVO.getAgenda().setDataAgendamento(dadosSQL.getTimestamp("dataagendamento"));
                    fecharMetaDetalhadoVO.getAgenda().setTipoAgendamento(dadosSQL.getString("tipoagendamento"));
                    fecharMetaDetalhadoVO.getAgenda().setHora(dadosSQL.getString("hora"));
                    fecharMetaDetalhadoVO.getAgenda().setMinuto(dadosSQL.getString("minuto"));

                    fecharMetaDetalhadoVO.setDataUltimoContato(new HistoricoContato(con).obterDataUltimoContato(fecharMetaDetalhadoVO, false));

                    listaRetornar.add(fecharMetaDetalhadoVO);
                }
            }
        }

        return listaRetornar;
    }
    
    public boolean existeMetaConfiguracaoDiasPosVenda(Integer codigoConfiguracao) throws Exception{
        return SuperEntidade.existe("select codigo from fecharmetadetalhado  where configuracaodiasposvenda  = "+codigoConfiguracao+" limit 1", con);
    }

    public void excluirPorIndicado(Integer codigoIndicado)throws Exception{
        String sql = "delete from fecharMetaDetalhado where indicado = ?";
        try (PreparedStatement pst = con.prepareStatement(sql)) {
            pst.setInt(1, codigoIndicado);
            pst.execute();
        }
    }

    public void deletarAlunoFilaEsperaTurmaCrm(Integer codigoFila)throws Exception{
        String sql = "delete from filaEsperaTurmaCrm where codigo = ?";
        try (PreparedStatement pst = con.prepareStatement(sql)) {
            pst.setInt(1, codigoFila);
            pst.execute();
        }
    }

    public void excluirFecharMetaDetalhadoDoAgendamentoAulaExperimental(AgendaVO agendaVO, Integer codigoConvite)throws Exception{
        String sql = "delete from fecharMetaDetalhado where origem = ? AND codigoOrigem = ?  and conviteAulaExperimental = ?";
        try (PreparedStatement pst = con.prepareStatement(sql)) {
            pst.setString(1, "AGENDA");
            pst.setInt(2, agendaVO.getCodigo());
            pst.setInt(3, codigoConvite);
            pst.execute();
        }
    }

    public boolean consultarSituacaoObteveSucesso(Integer codigoFecharMetaDetalhado) throws Exception {
        String sql = "select obtevesucesso from fecharmetadetalhado  where codigo = " + codigoFecharMetaDetalhado +";";
        try (Statement stm = con.createStatement()) {
            try (ResultSet tabelaResultado = stm.executeQuery(sql)) {
                if (!tabelaResultado.next()) {
                    return false;
                }
                return tabelaResultado.getBoolean("obtevesucesso");
            }
        }
    }

    @Override
    public List<FecharMetaDetalhadoVO> consultarListaBICRM(EmpresaVO empresa, Date dataInicio, Date dataFinal, List<UsuarioVO> usuarioVOList, String identificadorMeta, boolean somenteObteveSucesso, boolean somenteRepescagem, boolean total, boolean metaAtingidaBIResultado, List<FecharMetaVO> fecharMetaCRMExtra, boolean metaIndividual, String codigosMetaExtraIndividual, boolean mostrarTodosClientesDaMeta, Integer quantidadeConsultorSelecionado, String codigosMetasExtraSomada, boolean metaExtra) throws Exception {
        String codigosUsuarios = Uteis.retornarCodigos(usuarioVOList);
        boolean conversao = false;

        //SÓ IRÁ BUSCAR PELO CODIGO CASO SEJA CRM-EXTRA CASO CONTRÁRIO SETAR COMO 0 PARA NÃO SER UTILIZADO
        if (!identificadorMeta.equals(FasesCRMEnum.CRM_EXTRA.getSigla())){
            fecharMetaCRMExtra = new ArrayList<FecharMetaVO>();
        }

        if (!metaAtingidaBIResultado) {
            if (identificadorMeta.equals(FasesCRMEnum.CONVERSAO_INDICADOS.getSigla())) {
                identificadorMeta = FasesCRMEnum.INDICACOES.getSigla();
                somenteObteveSucesso = true;
                conversao = true;
            } else if (identificadorMeta.equals(FasesCRMEnum.CONVERSAO_AGENDADOS.getSigla())) {
                identificadorMeta = FasesCRMEnum.AGENDAMENTO.getSigla();
                somenteObteveSucesso = true;
                conversao = true;
            } else if (identificadorMeta.equals(FasesCRMEnum.CONVERSAO_EX_ALUNOS.getSigla())) {
                identificadorMeta = FasesCRMEnum.EX_ALUNOS.getSigla();
                somenteObteveSucesso = true;
                conversao = true;
            } else if (identificadorMeta.equals(FasesCRMEnum.CONVERSAO_VISITANTES_ANTIGOS.getSigla())) {
                identificadorMeta = FasesCRMEnum.VISITANTES_ANTIGOS.getSigla();
                somenteObteveSucesso = true;
                conversao = true;
            } else if (identificadorMeta.equals(FasesCRMEnum.CONVERSAO_DESISTENTES.getSigla())) {
                identificadorMeta = FasesCRMEnum.DESISTENTES.getSigla();
                somenteObteveSucesso = true;
                conversao = true;
            }
        }

        if (total) {
            Integer tipoFase = 0;
            for (FasesCRMEnum fasesCRMEnum : FasesCRMEnum.values()) {
                if (identificadorMeta.equals(fasesCRMEnum.getSigla())) {
                    tipoFase = fasesCRMEnum.getTipoFase().getCodigo();
                    break;
                }
            }

            if (tipoFase.equals(TipoFaseCRM.VENDAS.getCodigo())) {
                identificadorMeta = "('AG','LA','HO','RE','PE','VA','EX')";
            } else if (tipoFase.equals(TipoFaseCRM.RETENCAO.getCodigo())) {
                identificadorMeta = "('RI','VE','PV','FA','AN')";
            } else if (tipoFase.equals(TipoFaseCRM.CRMEXTRA.getCodigo())) {
                identificadorMeta = "('CR')";
            } else if (tipoFase.equals(TipoFaseCRM.ESTUDIO.getCodigo())) {
                identificadorMeta = "('SF','SA')";
            }
        } else {
            identificadorMeta = "('" + identificadorMeta + "')";
        }

        StringBuilder sql = new StringBuilder();
        sql.append("select \n");
        sql.append("fmd.codigo as codigodetalhado, \n");
        sql.append("fmd.obtevesucesso, \n");
        sql.append("fm.codigo as fecharmeta, \n");
        sql.append("fm.dataregistro as datameta, \n");
        sql.append("fm.identificadormeta as identificadormeta, \n");
        sql.append("am.metaemaberto as metaaberta, \n");
        sql.append("fmd.configuracaodiasposvenda, \n");
        sql.append("fmd.codigoorigem,\n");
        sql.append("fmd.origem,\n");
        sql.append("fmd.teveContato, \n");
        sql.append("fmd.repescagem, \n");
        sql.append("fmd.cliente, \n");
        sql.append("pes.codigo as codigopessoacliente, \n");
        sql.append("cli.matricula as matricula, \n");
        sql.append("pes.nome as nomecliente, \n");
        sql.append("pes.datanasc, \n");
        sql.append("pes.estadocivil, \n");
        sql.append("fmd.passivo, \n");
        sql.append("pas.nome as nomepassivo, \n");
        sql.append("fmd.indicado, \n");
        sql.append("ind.nomeindicado as nomeindicado, \n");
        sql.append("ag.codigo as codigoagenda,\n");
        sql.append("ag.dataagendamento,\n");
        sql.append("ag.tipoagendamento,\n");
        sql.append("ag.hora,\n");
        sql.append("ag.minuto,\n");
        sql.append("scs.situacao, \n");
        sql.append("scs.situacaocontrato, \n");
        sql.append("colabpes.nome as nomecolaborador \n");
        sql.append("from fecharmetadetalhado  fmd \n");
        sql.append("inner join fecharmeta fm on fm.codigo = fmd.fecharmeta \n");
        sql.append("inner join aberturameta am on am.codigo = fm.aberturameta \n");
        sql.append("left join usuario u on u.codigo = am.colaboradorresponsavel \n");
        sql.append("left join colaborador colab on colab.codigo = u.colaborador \n");
        sql.append("left join pessoa colabpes on colabpes.codigo = colab.pessoa \n");
        if (metaExtra && usuarioVOList.size() > 1 && mostrarTodosClientesDaMeta == false) {
            sql.append("inner join maladireta mal on fm.maladireta = mal.codigo \n");
        }
        sql.append("left join cliente cli on cli.codigo = fmd.cliente \n");
        sql.append("left join pessoa pes on pes.codigo = cli.pessoa \n");
        sql.append("left join passivo pas on pas.codigo = fmd.passivo \n");
        sql.append("left join indicado ind on ind.codigo = fmd.indicado \n");
        sql.append("left join situacaoclientesinteticodw scs on scs.codigocliente = fmd.cliente \n");
        sql.append("left join agenda ag on ag.codigo = fmd.codigoorigem and fmd.origem = 'AGENDA' \n");
        sql.append("where am.dia::date between '").append(Uteis.getData(dataInicio)).append("' and '").append(Uteis.getData(dataFinal)).append("' \n");
        sql.append("and am.colaboradorresponsavel in (").append(codigosUsuarios).append(") \n");
        sql.append("and fm.identificadormeta in ").append(identificadorMeta).append(" \n");
        if (somenteObteveSucesso) {
            sql.append("and fmd.obtevesucesso = true \n");
        }
        if (somenteRepescagem) {
            sql.append("and fmd.repescagem = true \n");
        }

        if (somenteObteveSucesso && !somenteRepescagem && !conversao) {
            sql.append("and fmd.repescagem = false \n");
        }

        if (identificadorMeta.contains(FasesCRMEnum.AGENDAMENTO.getSigla())) {
            sql.append("and (ag.tipoagendamento = 'AE' or ag.tipoagendamento = 'VI' or ag.tipoagendamento is null) \n");
        }

        if(metaExtra && quantidadeConsultorSelecionado > 1 && mostrarTodosClientesDaMeta == false) {
            sql.append(" and mal.metaextraindividual ");
            if(metaIndividual) {
                sql.append(" = true ");
            }else {
                sql.append(" = false ");
            }
            sql.append(" \n");
        }

        if (quantidadeConsultorSelecionado == 1) {
            String codigosFecharMetaCRMExtra = Uteis.retornarCodigos(fecharMetaCRMExtra);
            if (!UteisValidacao.emptyList(fecharMetaCRMExtra)) {
                sql.append("and fm.codigo in (").append(codigosFecharMetaCRMExtra).append(") \n");
            }
        }else if(quantidadeConsultorSelecionado > 1 && metaIndividual == false && mostrarTodosClientesDaMeta){
            String codigosFecharMetaCRMExtra = Uteis.retornarCodigos(fecharMetaCRMExtra);
            if (!UteisValidacao.emptyList(fecharMetaCRMExtra)) {
                sql.append("and fm.codigo in (").append(codigosFecharMetaCRMExtra).append(") \n");
            }
        }else if(quantidadeConsultorSelecionado > 1 && metaIndividual && mostrarTodosClientesDaMeta){
            if (!UteisValidacao.emptyString(codigosMetasExtraSomada)) {
                sql.append("and fm.codigo in (").append(codigosMetasExtraSomada).append(") \n");
            }else {
                String codigosFecharMetaCRMExtra = Uteis.retornarCodigos(fecharMetaCRMExtra);
                sql.append("and fm.codigo in (").append(codigosFecharMetaCRMExtra).append(") \n");
            }
        }else if(quantidadeConsultorSelecionado > 1 && metaIndividual && mostrarTodosClientesDaMeta == false){
            if (!UteisValidacao.emptyString(codigosMetasExtraSomada)) {
                sql.append("and fm.codigo in (").append(codigosMetasExtraSomada).append(") \n");
            }else if(!UteisValidacao.emptyList(fecharMetaCRMExtra)){
                String codigosFecharMetaCRMExtra  = Uteis.retornarCodigos(fecharMetaCRMExtra);
                sql.append("and fm.codigo in (").append(codigosFecharMetaCRMExtra).append(") \n");
            }
        } else if(quantidadeConsultorSelecionado > 1 && metaIndividual == false){
            String codigosFecharMetaCRMExtra = "";
            if (!codigosMetasExtraSomada.isEmpty()) {
                codigosFecharMetaCRMExtra = codigosMetasExtraSomada;
            }else{
               codigosFecharMetaCRMExtra  = Uteis.retornarCodigos(fecharMetaCRMExtra);
            }
            if (!UteisValidacao.emptyList(fecharMetaCRMExtra)) {
                sql.append("and fm.codigo in (").append(codigosFecharMetaCRMExtra).append(") \n");
            }
        }

        if(!fecharMetaCRMExtra.isEmpty()){
            sql.append(definirFecharMeta(fecharMetaCRMExtra));
        }

        if (empresa != null && empresa.getCodigo() != null) {
            sql.append(" AND am.empresa = " + empresa.getCodigo());
        }

        List<FecharMetaDetalhadoVO> listaRetornar;
        try (PreparedStatement pst = con.prepareStatement(sql.toString())) {
            try (ResultSet dadosSQL = pst.executeQuery()) {

                listaRetornar = new ArrayList<FecharMetaDetalhadoVO>();
                while (dadosSQL.next()) {
                    FecharMetaDetalhadoVO fecharMetaDetalhadoVO = new FecharMetaDetalhadoVO();

                    fecharMetaDetalhadoVO.setCodigo(dadosSQL.getInt("codigodetalhado"));
                    fecharMetaDetalhadoVO.setObteveSucesso(dadosSQL.getBoolean("obtevesucesso"));
                    fecharMetaDetalhadoVO.setTeveContato(dadosSQL.getBoolean("teveContato"));
                    fecharMetaDetalhadoVO.setRepescagem(dadosSQL.getBoolean("repescagem"));
                    fecharMetaDetalhadoVO.getFecharMeta().setCodigo(dadosSQL.getInt("fecharmeta"));
                    fecharMetaDetalhadoVO.getFecharMeta().setDataRegistro(dadosSQL.getTimestamp("datameta"));
                    fecharMetaDetalhadoVO.getFecharMeta().setIdentificadorMeta(dadosSQL.getString("identificadormeta"));
                    fecharMetaDetalhadoVO.getFecharMeta().getAberturaMetaVO().setMetaEmAberto(dadosSQL.getBoolean("metaaberta"));
                    fecharMetaDetalhadoVO.getFecharMeta().getAberturaMetaVO().getColaboradorResponsavel().setNome(dadosSQL.getString("nomecolaborador"));
                    fecharMetaDetalhadoVO.getConfiguracaoDiasPosVendaVO().setCodigo(dadosSQL.getInt("configuracaodiasposvenda"));
                    fecharMetaDetalhadoVO.setCodigoOrigem(dadosSQL.getInt("codigoorigem"));
                    fecharMetaDetalhadoVO.setOrigem(dadosSQL.getString("origem"));
                    fecharMetaDetalhadoVO.getCliente().setCodigo(dadosSQL.getInt("cliente"));
                    fecharMetaDetalhadoVO.getCliente().getPessoa().setCodigo(dadosSQL.getInt("codigopessoacliente"));
                    fecharMetaDetalhadoVO.getCliente().getPessoa().setNome(dadosSQL.getString("nomecliente"));
                    fecharMetaDetalhadoVO.getCliente().getPessoa().setDataNasc(dadosSQL.getTimestamp("datanasc"));
                    fecharMetaDetalhadoVO.getCliente().getPessoa().setEstadoCivil(dadosSQL.getString("estadocivil"));
                    fecharMetaDetalhadoVO.getCliente().setMatricula(dadosSQL.getString("matricula"));
                    fecharMetaDetalhadoVO.getCliente().setSituacao(dadosSQL.getString("situacao"));
                    fecharMetaDetalhadoVO.getCliente().setSituacaoContrato(dadosSQL.getString("situacaocontrato"));

                    ClienteSituacaoVO clienteSituacaoVO = new ClienteSituacaoVO();
                    clienteSituacaoVO.getClienteVO().setCodigo(dadosSQL.getInt("cliente"));
                    clienteSituacaoVO.setSituacao(dadosSQL.getString("situacao"));
                    clienteSituacaoVO.setSubordinadaSituacao(dadosSQL.getString("situacaocontrato"));
                    fecharMetaDetalhadoVO.getCliente().getClienteSituacaoVOs().add(clienteSituacaoVO);

                    fecharMetaDetalhadoVO.getPassivo().setCodigo(dadosSQL.getInt("passivo"));
                    fecharMetaDetalhadoVO.getPassivo().setNome(dadosSQL.getString("nomepassivo"));
                    fecharMetaDetalhadoVO.getIndicado().setCodigo(dadosSQL.getInt("indicado"));
                    fecharMetaDetalhadoVO.getIndicado().setNomeIndicado(dadosSQL.getString("nomeindicado"));
                    fecharMetaDetalhadoVO.getAgenda().setCodigo(dadosSQL.getInt("codigoagenda"));
                    fecharMetaDetalhadoVO.getAgenda().setDataAgendamento(dadosSQL.getTimestamp("dataagendamento"));
                    fecharMetaDetalhadoVO.getAgenda().setTipoAgendamento(dadosSQL.getString("tipoagendamento"));
                    fecharMetaDetalhadoVO.getAgenda().setHora(dadosSQL.getString("hora"));
                    fecharMetaDetalhadoVO.getAgenda().setMinuto(dadosSQL.getString("minuto"));

                    fecharMetaDetalhadoVO.setDataUltimoContato(new HistoricoContato(con).obterDataUltimoContato(fecharMetaDetalhadoVO, false));

                    listaRetornar.add(fecharMetaDetalhadoVO);
                }
            }
        }

        return listaRetornar;
    }

    @Override
    public List<FecharMetaDetalhadoVO> consultarListaBICRMEspecifico(Date dataInicio, Date dataFinal, List<UsuarioVO> usuarioVOList, String identificadorMeta, boolean somenteObteveSucesso, boolean somenteRepescagem, boolean total, boolean metaAtingidaBIResultado, List<FecharMetaVO> fecharMetaCRMExtra, String codigosMetas,boolean mostrarTodosClientesDaMeta,Integer quantidadeConsultorSelecionado) throws Exception {
        String codigosUsuarios = Uteis.retornarCodigos(usuarioVOList);
        boolean conversao = false;

        //SÓ IRÁ BUSCAR PELO CODIGO CASO SEJA CRM-EXTRA CASO CONTRÁRIO SETAR COMO 0 PARA NÃO SER UTILIZADO
        if (!identificadorMeta.equals(FasesCRMEnum.CRM_EXTRA.getSigla())){
            fecharMetaCRMExtra = new ArrayList<FecharMetaVO>();
        }

        if (!metaAtingidaBIResultado) {
            if (identificadorMeta.equals(FasesCRMEnum.CONVERSAO_INDICADOS.getSigla())) {
                identificadorMeta = FasesCRMEnum.INDICACOES.getSigla();
                somenteObteveSucesso = true;
                conversao = true;
            } else if (identificadorMeta.equals(FasesCRMEnum.CONVERSAO_AGENDADOS.getSigla())) {
                identificadorMeta = FasesCRMEnum.AGENDAMENTO.getSigla();
                somenteObteveSucesso = true;
                conversao = true;
            } else if (identificadorMeta.equals(FasesCRMEnum.CONVERSAO_EX_ALUNOS.getSigla())) {
                identificadorMeta = FasesCRMEnum.EX_ALUNOS.getSigla();
                somenteObteveSucesso = true;
                conversao = true;
            } else if (identificadorMeta.equals(FasesCRMEnum.CONVERSAO_VISITANTES_ANTIGOS.getSigla())) {
                identificadorMeta = FasesCRMEnum.VISITANTES_ANTIGOS.getSigla();
                somenteObteveSucesso = true;
                conversao = true;
            } else if (identificadorMeta.equals(FasesCRMEnum.CONVERSAO_DESISTENTES.getSigla())) {
                identificadorMeta = FasesCRMEnum.DESISTENTES.getSigla();
                somenteObteveSucesso = true;
                conversao = true;
            }
        }

        if (total) {
            Integer tipoFase = 0;
            for (FasesCRMEnum fasesCRMEnum : FasesCRMEnum.values()) {
                if (identificadorMeta.equals(fasesCRMEnum.getSigla())) {
                    tipoFase = fasesCRMEnum.getTipoFase().getCodigo();
                    break;
                }
            }

            if (tipoFase.equals(TipoFaseCRM.VENDAS.getCodigo())) {
                identificadorMeta = "('AG','LA','HO','RE','PE','VA','EX')";
            } else if (tipoFase.equals(TipoFaseCRM.RETENCAO.getCodigo())) {
                identificadorMeta = "('RI','VE','PV','FA','AN')";
            } else if (tipoFase.equals(TipoFaseCRM.CRMEXTRA.getCodigo())) {
                identificadorMeta = "('CR')";
            } else if (tipoFase.equals(TipoFaseCRM.ESTUDIO.getCodigo())) {
                identificadorMeta = "('SF','SA')";
            }
        } else {
            identificadorMeta = "('" + identificadorMeta + "')";
        }

        StringBuilder sql = new StringBuilder();
        sql.append("select \n");
        sql.append("fmd.codigo as codigodetalhado, \n");
        sql.append("fmd.obtevesucesso, \n");
        sql.append("fm.codigo as fecharmeta, \n");
        sql.append("fm.dataregistro as datameta, \n");
        sql.append("fm.identificadormeta as identificadormeta, \n");
        sql.append("am.metaemaberto as metaaberta, \n");
        sql.append("fmd.configuracaodiasposvenda, \n");
        sql.append("fmd.codigoorigem,\n");
        sql.append("fmd.origem,\n");
        sql.append("fmd.teveContato, \n");
        sql.append("fmd.repescagem, \n");
        sql.append("fmd.cliente, \n");
        sql.append("pes.codigo as codigopessoacliente, \n");
        sql.append("cli.matricula as matricula, \n");
        sql.append("pes.nome as nomecliente, \n");
        sql.append("pes.datanasc, \n");
        sql.append("pes.estadocivil, \n");
        sql.append("fmd.passivo, \n");
        sql.append("pas.nome as nomepassivo, \n");
        sql.append("fmd.indicado, \n");
        sql.append("ind.nomeindicado as nomeindicado, \n");
        sql.append("ag.codigo as codigoagenda,\n");
        sql.append("ag.dataagendamento,\n");
        sql.append("ag.tipoagendamento,\n");
        sql.append("ag.hora,\n");
        sql.append("ag.minuto,\n");
        sql.append("scs.situacao, \n");
        sql.append("scs.situacaocontrato \n");
        sql.append("from fecharmetadetalhado  fmd \n");
        sql.append("inner join fecharmeta fm on fm.codigo = fmd.fecharmeta \n");
        sql.append("inner join aberturameta am on am.codigo = fm.aberturameta \n");
        sql.append("left join cliente cli on cli.codigo = fmd.cliente \n");
        sql.append("left join pessoa pes on pes.codigo = cli.pessoa \n");
        sql.append("left join passivo pas on pas.codigo = fmd.passivo \n");
        sql.append("left join indicado ind on ind.codigo = fmd.indicado \n");
        sql.append("left join situacaoclientesinteticodw scs on scs.codigocliente = fmd.cliente \n");
        sql.append("left join agenda ag on ag.codigo = fmd.codigoorigem and fmd.origem = 'AGENDA' \n");
        sql.append("where am.dia::date between '").append(Uteis.getData(dataInicio)).append("' and '").append(Uteis.getData(dataFinal)).append("' \n");
        sql.append("and am.colaboradorresponsavel in (").append(codigosUsuarios).append(") \n");
        sql.append("and fm.identificadormeta in ").append(identificadorMeta).append(" \n");
        if (somenteObteveSucesso) {
            sql.append("and fmd.obtevesucesso = true \n");
        }
        if (somenteRepescagem) {
            sql.append("and fmd.repescagem = true \n");
        }

        if (somenteObteveSucesso && !somenteRepescagem && !conversao) {
            sql.append("and fmd.repescagem = false \n");
        }

        if (identificadorMeta.contains(FasesCRMEnum.AGENDAMENTO.getSigla())) {
            sql.append("and (ag.tipoagendamento = 'AE' or ag.tipoagendamento = 'VI' or ag.tipoagendamento is null) \n");
        }
        
        if (quantidadeConsultorSelecionado == 1) {
            String codigosFecharMetaCRMExtra = Uteis.retornarCodigos(fecharMetaCRMExtra);
            if (!UteisValidacao.emptyList(fecharMetaCRMExtra)) {
                sql.append("and fm.codigo in (").append(codigosFecharMetaCRMExtra).append(") \n");
            }
        }else if(quantidadeConsultorSelecionado > 1){
            if (!UteisValidacao.emptyString(codigosMetas)) {
                sql.append("and fm.codigo in (").append(codigosMetas).append(") \n");
            }
        }

        if(!fecharMetaCRMExtra.isEmpty()){
            sql.append(definirFecharMeta(fecharMetaCRMExtra));
        }

        List<FecharMetaDetalhadoVO> listaRetornar;
        try (PreparedStatement pst = con.prepareStatement(sql.toString())) {
            try (ResultSet dadosSQL = pst.executeQuery()) {

                listaRetornar = new ArrayList<FecharMetaDetalhadoVO>();
                while (dadosSQL.next()) {
                    FecharMetaDetalhadoVO fecharMetaDetalhadoVO = new FecharMetaDetalhadoVO();

                    fecharMetaDetalhadoVO.setCodigo(dadosSQL.getInt("codigodetalhado"));
                    fecharMetaDetalhadoVO.setObteveSucesso(dadosSQL.getBoolean("obtevesucesso"));
                    fecharMetaDetalhadoVO.setTeveContato(dadosSQL.getBoolean("teveContato"));
                    fecharMetaDetalhadoVO.setRepescagem(dadosSQL.getBoolean("repescagem"));
                    fecharMetaDetalhadoVO.getFecharMeta().setCodigo(dadosSQL.getInt("fecharmeta"));
                    fecharMetaDetalhadoVO.getFecharMeta().setDataRegistro(dadosSQL.getTimestamp("datameta"));
                    fecharMetaDetalhadoVO.getFecharMeta().setIdentificadorMeta(dadosSQL.getString("identificadormeta"));
                    fecharMetaDetalhadoVO.getFecharMeta().getAberturaMetaVO().setMetaEmAberto(dadosSQL.getBoolean("metaaberta"));
                    fecharMetaDetalhadoVO.getConfiguracaoDiasPosVendaVO().setCodigo(dadosSQL.getInt("configuracaodiasposvenda"));
                    fecharMetaDetalhadoVO.setCodigoOrigem(dadosSQL.getInt("codigoorigem"));
                    fecharMetaDetalhadoVO.setOrigem(dadosSQL.getString("origem"));
                    fecharMetaDetalhadoVO.getCliente().setCodigo(dadosSQL.getInt("cliente"));
                    fecharMetaDetalhadoVO.getCliente().getPessoa().setCodigo(dadosSQL.getInt("codigopessoacliente"));
                    fecharMetaDetalhadoVO.getCliente().getPessoa().setNome(dadosSQL.getString("nomecliente"));
                    fecharMetaDetalhadoVO.getCliente().getPessoa().setDataNasc(dadosSQL.getTimestamp("datanasc"));
                    fecharMetaDetalhadoVO.getCliente().getPessoa().setEstadoCivil(dadosSQL.getString("estadocivil"));
                    fecharMetaDetalhadoVO.getCliente().setMatricula(dadosSQL.getString("matricula"));
                    fecharMetaDetalhadoVO.getCliente().setSituacao(dadosSQL.getString("situacao"));
                    fecharMetaDetalhadoVO.getCliente().setSituacaoContrato(dadosSQL.getString("situacaocontrato"));

                    ClienteSituacaoVO clienteSituacaoVO = new ClienteSituacaoVO();
                    clienteSituacaoVO.getClienteVO().setCodigo(dadosSQL.getInt("cliente"));
                    clienteSituacaoVO.setSituacao(dadosSQL.getString("situacao"));
                    clienteSituacaoVO.setSubordinadaSituacao(dadosSQL.getString("situacaocontrato"));
                    fecharMetaDetalhadoVO.getCliente().getClienteSituacaoVOs().add(clienteSituacaoVO);

                    fecharMetaDetalhadoVO.getPassivo().setCodigo(dadosSQL.getInt("passivo"));
                    fecharMetaDetalhadoVO.getPassivo().setNome(dadosSQL.getString("nomepassivo"));
                    fecharMetaDetalhadoVO.getIndicado().setCodigo(dadosSQL.getInt("indicado"));
                    fecharMetaDetalhadoVO.getIndicado().setNomeIndicado(dadosSQL.getString("nomeindicado"));
                    fecharMetaDetalhadoVO.getAgenda().setCodigo(dadosSQL.getInt("codigoagenda"));
                    fecharMetaDetalhadoVO.getAgenda().setDataAgendamento(dadosSQL.getTimestamp("dataagendamento"));
                    fecharMetaDetalhadoVO.getAgenda().setTipoAgendamento(dadosSQL.getString("tipoagendamento"));
                    fecharMetaDetalhadoVO.getAgenda().setHora(dadosSQL.getString("hora"));
                    fecharMetaDetalhadoVO.getAgenda().setMinuto(dadosSQL.getString("minuto"));

                    fecharMetaDetalhadoVO.setDataUltimoContato(new HistoricoContato(con).obterDataUltimoContato(fecharMetaDetalhadoVO, false));

                    listaRetornar.add(fecharMetaDetalhadoVO);
                }
            }
        }

        return listaRetornar;
    }

    public String definirFecharMeta(List<FecharMetaVO> fecharMetaCRMExtra){
        StringBuilder sql = new StringBuilder(" AND fm.codigo in (");

        for(FecharMetaVO fecharMeta : fecharMetaCRMExtra){
            sql.append(fecharMeta.getCodigo() + ",");
        }
        sql.deleteCharAt(sql.lastIndexOf(","));
        sql.append(")" );
        return sql.toString();
    }

    public void excluirFecharMetaDetalhadoMalaDireta(MalaDiretaVO malaDireta) throws Exception {
        String sql = "delete from fecharmetadetalhado where fecharmeta in (select codigo from Fecharmeta WHERE maladireta = ?)";
        try (PreparedStatement stm = con.prepareStatement(sql)) {
            stm.setInt(1, malaDireta.getCodigo());
            stm.execute();
        }
    }
     
    public ResultSet consultarLeadsHoje(FecharMetaVO obj, Integer codColaborador, int nivelMontarDados, Integer empresa) throws Exception {
        consultarCRM(getIdEntidade(), false);
        StringBuilder sql = new StringBuilder();
        sql.append("SELECT lead.cliente, lead.passivo, lead.indicado , 'CONVERSAOLEAD' as origem,  ");
        sql.append("cl.codigo as codigoOrigem from conversaolead cl inner join lead on lead.codigo = cl.lead  WHERE ");
        sql.append(" cl.responsavel = ").append(codColaborador);
        sql.append(" AND lead.empresa = ").append(empresa);
        sql.append(" AND cl.tratada = 'f' AND not exists(select codigo from fecharmetadetalhado where codigoorigem = cl.codigo and origem = 'CONVERSAOLEAD')");

        Statement sqlConsultar = con.createStatement();
        ResultSet tabelaResultado = sqlConsultar.executeQuery(sql.toString());
        montarFecharMetaDetalhado(tabelaResultado, obj, 0, nivelMontarDados);
        return tabelaResultado;

    }
    
    private static void montarDadosLead(FecharMetaDetalhadoVO obj, int nivelMontarDados, Connection con) throws Exception {
        if (obj.getCodigoOrigem().intValue() == 0) {
            obj.setConversaoLeadVO(new ConversaoLeadVO());
            return;
        }
        ConversaoLead conversaoLeadDAO = new ConversaoLead(con);
        obj.setConversaoLeadVO(conversaoLeadDAO.consultarPorChavePrimaria(obj.getCodigoOrigem(), Uteis.NIVELMONTARDADOS_DADOSBASICOS));
        conversaoLeadDAO = null;
    }
    
    public ResultSet consultarLeadsAcumulado(FecharMetaVO obj, Integer codColaborador, int nivelMontarDados, Integer empresa) throws Exception {
        consultarCRM(getIdEntidade(), false);
        StringBuilder sql = new StringBuilder();
         sql.append("SELECT lead.cliente, lead.passivo, lead.indicado , 'CONVERSAOLEAD' as origem,  ");
        sql.append("cl.codigo as codigoOrigem from conversaolead cl inner join lead on lead.codigo = cl.lead  WHERE ");
        sql.append(" cl.responsavel = ").append(codColaborador);
        sql.append(" AND lead.empresa = ").append(empresa);
        sql.append(" AND cl.datalancamento >= '").append(Uteis.getDataJDBC(Calendario.ontem())).append("'");
        sql.append(" AND cl.datalancamento < '").append(Uteis.getDataJDBC(Calendario.hoje())).append("'");
        sql.append(" AND cl.tratada = 'f' AND exists(select codigo from fecharmetadetalhado where codigoorigem = cl.codigo and origem = 'CONVERSAOLEAD')");

        Statement sqlConsultar = con.createStatement();
        ResultSet tabelaResultado = sqlConsultar.executeQuery(sql.toString());
        montarFecharMetaDetalhado(tabelaResultado, obj, 0, nivelMontarDados);
        return tabelaResultado;

    }

    public FecharMetaVO pegarMetaAulaExperimental(Integer codigoCliente, Date dataAula) throws Exception {
        StringBuilder sql = new StringBuilder();
        sql.append(" SELECT FM.codigo AS codigo, ");
        sql.append("        FM.meta AS meta ");
        sql.append(" FROM fecharmetadetalhado FMD ");
        sql.append(" INNER JOIN fecharmeta	FM on FM.codigo = FMD.fecharmeta ");
        sql.append(" INNER JOIN cliente 	C  on C.codigo = FMD.cliente ");
        sql.append(" INNER JOIN agenda  	A  on A.codigo = FMD.codigoorigem ");
        sql.append(" WHERE C.codigo = ").append(codigoCliente);
        sql.append(" AND   FMD.origem = 'AGENDA'");
        sql.append(" AND   FM.dataregistro::date = '").append(Uteis.getDataJDBCTimestamp(dataAula)).append("'::date ");
        sql.append(" AND   A.tipoagendamento <> 'AG' ");

        FecharMetaVO fm;
        try (Statement s = con.createStatement()) {
            try (ResultSet rs = s.executeQuery(sql.toString())) {
                if (!rs.next()) {
                    return null;
                }

                fm = new FecharMetaVO();
                fm.setCodigo(new Integer(rs.getInt("codigo")));
                fm.setMeta(rs.getDouble("meta"));
            }
        }

        return fm;
    }

    public void decrementarMeta(FecharMetaVO fecharMeta) throws Exception {
        StringBuilder sql = new StringBuilder();
        // Normalização do valor
        Double meta = Math.max(fecharMeta.getMeta() - 1, 0);

        sql.append(" UPDATE fecharmeta ");
        sql.append("    SET meta = ").append(meta);
        sql.append(" WHERE codigo = ").append(fecharMeta.getCodigo());

        try (PreparedStatement stm = con.prepareStatement(sql.toString())) {
            stm.execute();
        }
    }
    
    public List<FecharMetaDetalhadoVO> consultarFecharMetaDetalhadoPorCodigoAgenda(Integer codAgenda, String identificadorMeta, int nivelMontarDados) throws Exception {
        String sql = "SELECT FecharMetaDetalhado.* FROM FecharMetaDetalhado ";
        if(!UteisValidacao.emptyString(identificadorMeta)){
           sql += " inner join fecharMeta on fecharMeta.codigo = FecharMetaDetalhado.fecharMeta  AND fecharMeta.identificadormeta LIKE '"+identificadorMeta+"' ";
        }
        sql += " WHERE FecharMetaDetalhado.codigoOrigem = " + codAgenda.intValue() + " and FecharMetaDetalhado.origem = 'AGENDA' ";
        try (PreparedStatement sqlConsultar = con.prepareStatement(sql)) {
            try (ResultSet rs = sqlConsultar.executeQuery()) {
                return montarDadosConsulta(rs, nivelMontarDados, con);
            }
        }

    }

}

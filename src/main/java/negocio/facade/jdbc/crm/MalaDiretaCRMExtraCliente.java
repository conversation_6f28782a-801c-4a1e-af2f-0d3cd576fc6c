/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package negocio.facade.jdbc.crm;

import controle.crm.MetaExtraImportadoDTO;
import negocio.comuns.arquitetura.UsuarioVO;
import negocio.comuns.basico.AmostraClienteTO;
import negocio.comuns.basico.TelefoneVO;
import negocio.comuns.crm.MalaDiretaCRMExtraClienteVO;
import negocio.comuns.crm.MalaDiretaVO;
import negocio.comuns.crm.PassivoVO;
import negocio.comuns.utilitarias.Calendario;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisValidacao;
import negocio.facade.jdbc.arquitetura.SuperEntidade;
import negocio.facade.jdbc.arquitetura.SuperFacadeJDBC;
import negocio.interfaces.crm.MalaDiretaCRMExtraClienteInterfaceFacade;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.util.ArrayList;
import java.util.List;

/**
 * Created by <PERSON><PERSON> on 02/12/2015
 */
public class MalaDiretaCRMExtraCliente extends SuperEntidade implements MalaDiretaCRMExtraClienteInterfaceFacade {

    public MalaDiretaCRMExtraCliente() throws Exception {
        super();
    }

    public MalaDiretaCRMExtraCliente(Connection conexao) throws Exception {
        super(conexao);
    }

    public static List<MalaDiretaCRMExtraClienteVO> montarDadosConsulta(ResultSet tabelaResultado, int nivelMontarDados) throws Exception {
        List<MalaDiretaCRMExtraClienteVO> vetResultado = new ArrayList<MalaDiretaCRMExtraClienteVO>();
        while (tabelaResultado.next()) {
            MalaDiretaCRMExtraClienteVO obj = montarDados(tabelaResultado, nivelMontarDados);
            vetResultado.add(obj);
        }
        return vetResultado;
    }

    public static MalaDiretaCRMExtraClienteVO montarDadosBasico(ResultSet dadosSQL) throws Exception {
        MalaDiretaCRMExtraClienteVO obj = new MalaDiretaCRMExtraClienteVO();
        obj.setNovoObj(false);
        obj.setCodigo(dadosSQL.getInt("codigo"));
        obj.getMalaDiretaVO().setCodigo(dadosSQL.getInt("maladireta"));
        obj.getClienteVO().setCodigo(dadosSQL.getInt("cliente"));
        try {
            obj.getPassivoVO().setCodigo(dadosSQL.getInt("passivo"));
        }catch (Exception e){
            Uteis.logar(e, MalaDiretaCRMExtraCliente.class);
        }
        return obj;
    }

    public static MalaDiretaCRMExtraClienteVO montarDados(ResultSet dadosSQL, int nivelMontarDados) throws Exception {
        MalaDiretaCRMExtraClienteVO obj = montarDadosBasico(dadosSQL);
        return obj;
    }

    public void incluir(MalaDiretaCRMExtraClienteVO obj) throws Exception {
        String sql = "INSERT INTO maladiretacrmextracliente(maladireta, cliente, passivo) VALUES (?, ?, ?)";
        PreparedStatement sqlInserir = con.prepareStatement(sql);
        if (obj.getMalaDiretaVO().getCodigo() != 0) {
            sqlInserir.setInt(1, obj.getMalaDiretaVO().getCodigo());
        } else {
            sqlInserir.setNull(1, 0);
        }

        if (obj.getClienteVO().getCodigo() != 0) {
            sqlInserir.setInt(2, obj.getClienteVO().getCodigo());
        } else {
            sqlInserir.setNull(2, 0);
        }

        if (obj.getPassivoVO().getCodigo() != 0) {
            sqlInserir.setInt(3, obj.getPassivoVO().getCodigo());
        } else {
            sqlInserir.setNull(3, 0);
        }
        sqlInserir.execute();
        obj.setCodigo(obterValorChavePrimariaCodigo());
        obj.setNovoObj(false);
    }

    public void alterar(MalaDiretaCRMExtraClienteVO obj) throws Exception {
        String sql = "UPDATE maladiretacrmextracliente set maladireta = ?, cliente = ? WHERE codigo = ?";
        PreparedStatement sqlAlterar = con.prepareStatement(sql);
        if (obj.getMalaDiretaVO().getCodigo() != 0) {
            sqlAlterar.setInt(1, obj.getMalaDiretaVO().getCodigo());
        } else {
            sqlAlterar.setNull(1, 0);
        }

        if (obj.getClienteVO().getCodigo() != 0) {
            sqlAlterar.setInt(2, obj.getClienteVO().getCodigo());
        } else {
            sqlAlterar.setNull(2, 0);
        }

        sqlAlterar.setInt(3, obj.getCodigo());
        sqlAlterar.execute();
    }

    public void alterarMalaDireta(List<MalaDiretaCRMExtraClienteVO> obj, MalaDiretaVO malaDiretaVO) throws Exception {
        for(MalaDiretaCRMExtraClienteVO malaDiretaCRMExtraClienteVO : obj) {
            malaDiretaCRMExtraClienteVO.setMalaDiretaVO(malaDiretaVO);
            String sql = "UPDATE maladiretacrmextracliente set maladireta = ? WHERE codigo = ?";
            PreparedStatement sqlAlterar = con.prepareStatement(sql);
            if (malaDiretaCRMExtraClienteVO.getMalaDiretaVO().getCodigo() != 0) {
                sqlAlterar.setInt(1, malaDiretaVO.getCodigo());
            } else {
                sqlAlterar.setNull(1, 0);
            }

            sqlAlterar.setInt(2, malaDiretaCRMExtraClienteVO.getCodigo());
            sqlAlterar.execute();
        }
    }

    public void excluir(MalaDiretaCRMExtraClienteVO obj) throws Exception {
        excluir(getIdEntidade());
        String sql = "DELETE FROM maladiretacrmextracliente WHERE codigo = ?";
        PreparedStatement sqlExcluir = con.prepareStatement(sql);
        sqlExcluir.setInt(1, obj.getCodigo());
        sqlExcluir.execute();
    }

    public void excluirPorMalaDireta(MalaDiretaVO obj) throws Exception {
        String sql = "DELETE FROM maladiretacrmextracliente WHERE maladireta = ?";
        PreparedStatement sqlExcluir = con.prepareStatement(sql);
        sqlExcluir.setInt(1, obj.getCodigo());
        sqlExcluir.execute();
    }

    public List<MalaDiretaCRMExtraClienteVO> incluirListaImportados(UsuarioVO usuario, Integer empresa, MalaDiretaVO malaDiretaVO, List<MetaExtraImportadoDTO> listaImportados) throws Exception{
        if (!malaDiretaVO.isNovoObj()) {
            excluirPorMalaDireta(malaDiretaVO);
        }

        List<MalaDiretaCRMExtraClienteVO> malaDiretaCRMExtraClienteVOS = new ArrayList<>();

        for (MetaExtraImportadoDTO meta : listaImportados) {
            MalaDiretaCRMExtraClienteVO crmExtraClienteVO = new MalaDiretaCRMExtraClienteVO();

            boolean adicionarPassivo = true;

            if (!UteisValidacao.emptyString(meta.getMatricula())) {
                try {
                    ResultSet rs = SuperFacadeJDBC.criarConsulta("select codigo from cliente" +
                            " where codigomatricula = " + Integer.valueOf(meta.getMatricula()) + " and empresa = " + empresa, con);
                    if(rs.next()){
                        crmExtraClienteVO.getClienteVO().setCodigo(rs.getInt("codigo"));
                        adicionarPassivo = false;
                    }
                }catch (Exception e){
                    Uteis.logar(e, MalaDiretaCRMExtraCliente.class);
                }
            }

            Passivo passivoDao = new Passivo(this.con);
            if (adicionarPassivo) {
                PassivoVO passivoVO = new PassivoVO();
                passivoVO.setNome(meta.getNome());
                passivoVO.setEmail(meta.getEmail());
                passivoVO.setTelefoneCelular(TelefoneVO.formatTelefone(meta.getTelefone(),"(##)#########"));
                passivoVO.setObservacao(meta.getCampoLivre());
                passivoVO.setDia(Calendario.hoje());
                passivoVO.setLead(false);
                passivoVO.setResponsavelCadastro(usuario);
                passivoVO.getEmpresaVO().setCodigo(empresa);
                passivoVO.setMetaExtra(true);
                passivoDao.incluir(passivoVO, false);
                crmExtraClienteVO.setPassivoVO(passivoVO);
            }

            incluir(crmExtraClienteVO);
            malaDiretaCRMExtraClienteVOS.add(crmExtraClienteVO);
        }
        return malaDiretaCRMExtraClienteVOS;
    }

    public void incluirListaCliente(MalaDiretaVO malaDiretaVO, List<AmostraClienteTO> amostraClienteTOList) throws Exception {
        if (!malaDiretaVO.isNovoObj()) {
            excluirPorMalaDireta(malaDiretaVO);
        }
        for (AmostraClienteTO cliente : amostraClienteTOList) {
            MalaDiretaCRMExtraClienteVO crmExtraClienteVO = new MalaDiretaCRMExtraClienteVO();
            crmExtraClienteVO.setMalaDiretaVO(malaDiretaVO);
            crmExtraClienteVO.getClienteVO().setCodigo(cliente.getCodigoCliente());
            incluir(crmExtraClienteVO);
        }
    }

    public List<MalaDiretaCRMExtraClienteVO> consultarPorMalaDireta(Integer codMalaDireta, int nivelMontarDados, Integer empresa) throws Exception {
        List<MalaDiretaCRMExtraClienteVO> objetos = new ArrayList<MalaDiretaCRMExtraClienteVO>();
        StringBuilder sql = new StringBuilder();
        sql.append(" SELECT mlcrm.*,sin.colaboradores FROM maladiretacrmextracliente mlcrm\n");
        sql.append(" INNER JOIN situacaoclientesinteticodw sin ON sin.codigocliente = mlcrm.cliente\n");
        sql.append(" INNER JOIN cliente cli on cli.codigo = mlcrm.cliente\n");
        sql.append(" WHERE maladireta = ?");
        if (empresa != null) {
            sql.append(" AND   cli.empresa = ?");
        }
        sql.append(" LIMIT 3000");
        PreparedStatement sqlConsulta = con.prepareStatement(sql.toString());
        sqlConsulta.setInt(1, codMalaDireta);
        if (empresa != null) {
            sqlConsulta.setInt(2, empresa);
        }
        ResultSet resultado = sqlConsulta.executeQuery();
        while (resultado.next()) {
            MalaDiretaCRMExtraClienteVO novoObj = montarDados(resultado, nivelMontarDados);
            novoObj.setVinculos(resultado.getString("colaboradores"));
            objetos.add(novoObj);
        }

        StringBuilder sql2 = new StringBuilder();
        sql2.append(" SELECT mlcrm.* FROM maladiretacrmextracliente mlcrm\n");
        sql2.append(" INNER JOIN passivo pa on mlcrm.passivo = pa.codigo\n");
        sql2.append(" WHERE maladireta = ?");
        PreparedStatement sqlConsulta2 = con.prepareStatement(sql2.toString());
        sqlConsulta2.setInt(1, codMalaDireta);
        ResultSet resultado2 = sqlConsulta2.executeQuery();
        while (resultado2.next()) {
            MalaDiretaCRMExtraClienteVO novoObj2 = montarDados(resultado2, nivelMontarDados);
            objetos.add(novoObj2);
        }

        return objetos;
    }
    public List<MalaDiretaCRMExtraClienteVO> consultarPorMalaDiretaVinculoColaborador(Integer codMalaDireta,Integer codigoColaborador,String tipoVinculo, int nivelMontarDados, Integer empresa)
            throws Exception {
        List<MalaDiretaCRMExtraClienteVO> objetos = new ArrayList<MalaDiretaCRMExtraClienteVO>();
        StringBuilder sql = new StringBuilder();
        sql.append(" SELECT mlcrm.* FROM maladiretacrmextracliente mlcrm\n");
        sql.append(" INNER JOIN Vinculo vin ON vin.cliente = mlcrm.cliente \n");
        sql.append(" INNER JOIN cliente cli on cli.codigo = mlcrm.cliente\n");
        sql.append(" WHERE maladireta = ? AND vin.tipoVinculo = ? and vin.colaborador = ?");
        if (empresa != null) {
            sql.append(" AND   cli.empresa = ? ");
        }
        PreparedStatement sqlConsulta = con.prepareStatement(sql.toString());
        sqlConsulta.setInt(1, codMalaDireta);
        sqlConsulta.setString(2, tipoVinculo);
        sqlConsulta.setInt(3, codigoColaborador);
        if (empresa != null) {
            sqlConsulta.setInt(4, empresa);
        }
        ResultSet resultado = sqlConsulta.executeQuery();
        while (resultado.next()) {
            MalaDiretaCRMExtraClienteVO novoObj = montarDados(resultado, nivelMontarDados);
            objetos.add(novoObj);
        }
        return objetos;
    }

}

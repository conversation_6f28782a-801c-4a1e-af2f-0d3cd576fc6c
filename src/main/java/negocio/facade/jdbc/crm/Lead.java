/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package negocio.facade.jdbc.crm;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.Statement;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

import negocio.comuns.arquitetura.UsuarioVO;
import negocio.comuns.basico.EmpresaVO;
import negocio.comuns.crm.LeadVO;
import negocio.comuns.utilitarias.Calendario;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisValidacao;
import negocio.facade.jdbc.arquitetura.SuperEntidade;
import negocio.facade.jdbc.basico.Cliente;
import negocio.facade.jdbc.basico.Empresa;
import negocio.interfaces.crm.LeadInterfaceFacade;
import servicos.integracao.enumerador.TipoLeadEnum;

/**
 *
 * <AUTHOR>
 */
public class Lead extends SuperEntidade implements LeadInterfaceFacade {

    
    public Lead() throws Exception {
        super();
    }

    public Lead(Connection con) throws Exception {
        super(con);
    }

    @Override
    public void incluir(LeadVO obj) throws Exception {
        String sql = "INSERT INTO lead(passivo, cliente, urlrd, idLead, uuid, email, empresa, indicado, tipo, dataregistro, dados) VALUES ( ?, ?, ?, ?, ?, ?,?,?,?,?,?);";
        PreparedStatement sqlInserir = con.prepareStatement(sql);
        int i = 1;
        if (!UteisValidacao.emptyNumber(obj.getPassivo().getCodigo())) {
            sqlInserir.setInt(i++, obj.getPassivo().getCodigo());
        } else {
            sqlInserir.setNull(i++, 0);
        }
        if (!UteisValidacao.emptyNumber(obj.getCliente().getCodigo())) {
            sqlInserir.setInt(i++, obj.getCliente().getCodigo());
        } else {
            sqlInserir.setNull(i++, 0);
        }
        sqlInserir.setString(i++, obj.getUrlRD());
        sqlInserir.setLong(i++, obj.getIdLead());
        sqlInserir.setString(i++, obj.getUuid());
        sqlInserir.setString(i++, obj.getEmail());
        sqlInserir.setInt(i++, obj.getEmpresa().getCodigo());
        if (!UteisValidacao.emptyNumber(obj.getIndicado().getCodigo())) {
            sqlInserir.setInt(i++, obj.getIndicado().getCodigo());
        } else {
            sqlInserir.setNull(i++, 0);
        }
        if (obj.getTipo() != null) {
            sqlInserir.setInt(i++, obj.getTipo().getId());
        } else {
            sqlInserir.setNull(i++, 0);
        }
        sqlInserir.setTimestamp(i++, Uteis.getDataJDBCTimestamp(Calendario.hoje()));
        sqlInserir.setString(i++, obj.getDados());
        sqlInserir.execute();
        obj.setCodigo(obterValorChavePrimariaCodigo());
    }

    @Override
    public void alterar(LeadVO obj) throws Exception {
        String sql = "UPDATE lead SET  passivo=?, cliente=?, urlrd=?, idLead=?, uuid=?, email=?, empresa=?, indicado=?, tipo=? WHERE ((codigo = ?))";
        PreparedStatement sqlAlterar = con.prepareStatement(sql);
        int i = 1;
         if (!UteisValidacao.emptyNumber(obj.getPassivo().getCodigo())) {
            sqlAlterar.setInt(i++, obj.getPassivo().getCodigo());
        } else {
            sqlAlterar.setNull(i++, 0);
        }
        if (!UteisValidacao.emptyNumber(obj.getCliente().getCodigo())) {
            sqlAlterar.setInt(i++, obj.getCliente().getCodigo());
        } else {
            sqlAlterar.setNull(i++, 0);
        }
        sqlAlterar.setString(i++, obj.getUrlRD());
        sqlAlterar.setLong(i++, obj.getIdLead());
        sqlAlterar.setString(i++, obj.getUuid());
        sqlAlterar.setString(i++, obj.getEmail());
        sqlAlterar.setInt(i++, obj.getEmpresa().getCodigo());
        if (!UteisValidacao.emptyNumber(obj.getIndicado().getCodigo())) {
            sqlAlterar.setInt(i++, obj.getIndicado().getCodigo());
        } else {
            sqlAlterar.setNull(i++, 0);
        }
        if (obj.getTipo() != null) {
            sqlAlterar.setInt(i++, obj.getTipo().getId());
        } else {
            sqlAlterar.setNull(i++, 0);
        }
        sqlAlterar.setInt(i++, obj.getCodigo());
        sqlAlterar.execute();
    }

    @Override
    public void excluir(LeadVO obj) throws Exception {
        throw new UnsupportedOperationException("Not supported yet."); //To change body of generated methods, choose Tools | Templates.
    }

    @Override
    public LeadVO consultarPorChavePrimaria(Integer codigo, int nivelMontarDados) throws Exception {
        String sql = "SELECT * FROM lead WHERE codigo = ?";
        PreparedStatement sqlConsultar = con.prepareStatement(sql);
        sqlConsultar.setInt(1, codigo);
        ResultSet tabelaResultado = sqlConsultar.executeQuery();
        if (!tabelaResultado.next()) {
            return new LeadVO();
        }
        return (montarDados(tabelaResultado, nivelMontarDados, this.con));
    }

    @Override
    public List<LeadVO> consultarPorNome(String valorConsulta, boolean controlarAcesso, EmpresaVO empresa, int nivelMontarDados) throws Exception {
        throw new UnsupportedOperationException("Not supported yet."); //To change body of generated methods, choose Tools | Templates.
    }

    @Override
    public LeadVO consultarPorPassivo(Integer codigoPassivo, int nivelMontarDados) throws Exception {
        StringBuilder sql = new StringBuilder();
        sql.append("SELECT l.* FROM lead l WHERE passivo = ").append(codigoPassivo);
        Statement stm = con.createStatement();
        ResultSet tabelaResultado = stm.executeQuery(sql.toString());
         if (!tabelaResultado.next()) {
            return new LeadVO();
        }
        return (montarDados(tabelaResultado, nivelMontarDados, this.con));
    }
    
    @Override
    public LeadVO consultarPorCliente(Integer codigocliente, int nivelMontarDados) throws Exception {
        StringBuilder sql = new StringBuilder();
        sql.append("SELECT l.* FROM lead l WHERE cliente = ").append(codigocliente);
        Statement stm = con.createStatement();
        ResultSet tabelaResultado = stm.executeQuery(sql.toString());
        if (!tabelaResultado.next()) {
            return new LeadVO();
        }
        return (montarDados(tabelaResultado, nivelMontarDados, this.con));
    }

    public static LeadVO montarDados(ResultSet dadosSQL, int nivelMontarDados, Connection con) throws Exception {
        LeadVO obj = new LeadVO();
        obj.setCodigo(new Integer(dadosSQL.getInt("codigo")));
        obj.getPassivo().setCodigo(dadosSQL.getInt("passivo"));
        obj.getCliente().setCodigo(dadosSQL.getInt("cliente"));
        obj.getIndicado().setCodigo(dadosSQL.getInt("indicado"));
        obj.setUrlRD(dadosSQL.getString("urlrd"));
        obj.setIdLead(dadosSQL.getLong("idlead"));
        obj.setUuid(dadosSQL.getString("uuid"));
        obj.setEmail(dadosSQL.getString("email"));
        obj.getEmpresa().setCodigo(dadosSQL.getInt("empresa"));
        obj.setTipo(TipoLeadEnum.getPorCodigo(dadosSQL.getInt("tipo")));
        obj.setDados(dadosSQL.getString("dados"));
        obj.setNovoObj(false);
        if (nivelMontarDados == Uteis.NIVELMONTARDADOS_DADOSBASICOS) {
            return obj;
        }
        if(nivelMontarDados == Uteis.NIVELMONTARDADOS_DADOSENTIDADESPRINCIPAIS){
            montarDadosCliente(obj, con);
            montarDadosPassivo(obj, con);
            montarDadosIndicado(obj, con);
            return obj;
        }
        montarDadosPassivo(obj, con);
        montarDadosCliente(obj, con);
        montarDadosEmpresa(obj, con);
        montarDadosIndicado(obj, con);
        return obj;
    }
    
    public static List<LeadVO> montarDadosConsulta(ResultSet tabelaResultado, int nivelMontarDados, Connection con) throws Exception {
        List<LeadVO> vetResultado = new ArrayList<LeadVO>();
        while (tabelaResultado.next()) {
            LeadVO obj = new LeadVO();
            obj = montarDados(tabelaResultado, nivelMontarDados, con);
            vetResultado.add(obj);
        }
        tabelaResultado = null;
        return vetResultado;
    }
    
    public static void montarDadosPassivo(LeadVO obj, Connection con) throws Exception {
        if(!UteisValidacao.emptyNumber(obj.getPassivo().getCodigo())){
            Passivo passivoDAO = new Passivo(con);
            obj.setPassivo(passivoDAO.consultarPorChavePrimaria(obj.getPassivo().getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS));
            passivoDAO = null;
        }
    }
    
    public static void montarDadosCliente(LeadVO obj, Connection con) throws Exception {
        if(!UteisValidacao.emptyNumber(obj.getCliente().getCodigo())){
            Cliente clienteDAO = new Cliente(con);
            obj.setCliente(clienteDAO.consultarPorChavePrimaria(obj.getCliente().getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS));
            clienteDAO = null;
        }
    }
    
    public static void montarDadosIndicado(LeadVO obj, Connection con) throws Exception {
        if(!UteisValidacao.emptyNumber(obj.getIndicado().getCodigo())){
            Indicado indicadoDAO = new Indicado(con);
            obj.setIndicado(indicadoDAO.consultarPorChavePrimaria(obj.getIndicado().getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS));
        }
    }
    
    public static void montarDadosEmpresa(LeadVO obj, Connection con) throws Exception {
        Empresa empDAO = new Empresa(con);
        obj.setEmpresa(empDAO.consultarPorChavePrimaria(obj.getEmpresa().getCodigo(), Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA));
        empDAO = null;
    }
    
    public LeadVO consultarPorCodigoRD(final String valorConsulta, int nivelMontarDados) throws Exception {
        String sql = "SELECT * FROM lead WHERE uuid = '" + valorConsulta+"'";
        Statement stm = con.createStatement();
        ResultSet tabelaResultado = stm.executeQuery(sql.toString());
        if (!tabelaResultado.next()) {
            return new LeadVO();
        }
        return (montarDados(tabelaResultado, nivelMontarDados, con));
    }

    public List<LeadVO> consultarLeadsConvertidosPorPeriodoResponsavelCadastro(List<UsuarioVO> listaUsuarioVO, Date dataInicioMeta, Date dataFimMeta, Integer empresa, int nivelMontarDados, boolean conversao) throws Exception {
        ResultSet rs = sqlLeadPorPeriodoResponsavelCadastro(listaUsuarioVO, dataInicioMeta, dataFimMeta, empresa, false, conversao);
        return montarDadosConsulta(rs, nivelMontarDados, con);
    }

    public Integer contarLeadPorPeriodoResponsavelCadastro(List<UsuarioVO> listaUsuarioVO, Date dataInicioMeta, Date dataFimMeta, Integer empresa, boolean contar, boolean conversao) throws Exception {
        ResultSet rs = sqlLeadPorPeriodoResponsavelCadastro(listaUsuarioVO, dataInicioMeta, dataFimMeta, empresa, contar, conversao);
        if (rs.next()) {
            return rs.getInt("total");
        }
        return 0;
    }

    private ResultSet sqlLeadPorPeriodoResponsavelCadastro(List<UsuarioVO> listaUsuarioVO, Date dataInicioMeta, Date dataFimMeta, Integer empresa, boolean contar, boolean conversao) throws Exception {
        String codigosUsuario = Uteis.retornarCodigos(listaUsuarioVO);
        StringBuilder sql = new StringBuilder();

        if(contar) {
            sql.append("SELECT count(*) as total FROM lead l ");
        }else {
            sql.append("SELECT l.* FROM lead l ");
        }
        sql.append("left join passivo p on p.codigo = l.passivo ");
        sql.append("left join indicado i on i.codigo = l.indicado ");
        sql.append("left join indicacao ii on ii.codigo = i.indicacao ");
        if(conversao){
            sql.append("inner join situacaoclientesinteticodw scs on ((scs.codigocliente = l.cliente) or (scs.codigocliente = i.cliente)) ");
            sql.append("inner join pessoa pes on pes.codigo  = scs.codigopessoa ");
        }

        sql.append(" WHERE ((p.dia >= '").append(Uteis.getDataJDBC(dataInicioMeta)).append(" 00:00:00'");
        if (dataFimMeta != null) {
            sql.append(" AND p.dia <= '").append(Uteis.getDataJDBC(dataFimMeta)).append(" 23:59:59')");
            sql.append(" OR ");
            sql.append(" (i.datalancamento >= '").append(Uteis.getDataJDBC(dataInicioMeta)).append(" 00:00:00'");
            sql.append(" AND i.datalancamento <= '").append(Uteis.getDataJDBC(dataFimMeta)).append(" 23:59:59'))");
        } else {
            sql.append(" AND p.dia <= '").append(Uteis.getDataJDBC(dataInicioMeta)).append(" 23:59:59')");
            sql.append(" OR ");
            sql.append(" (i.datalancamento >= '").append(Uteis.getDataJDBC(dataInicioMeta)).append(" 00:00:00'");
            sql.append(" AND i.datalancamento <= '").append(Uteis.getDataJDBC(dataInicioMeta)).append(" 23:59:59'))");
        }

        if(!UteisValidacao.emptyString(codigosUsuario)){
            sql.append(" AND (p.responsavelcadastro in (" + codigosUsuario + ")");
            sql.append(" OR ");
            sql.append("(ii.responsavelcadastro in (" + codigosUsuario + ")))");
        }

        if (!UteisValidacao.emptyNumber(empresa)) {
            sql.append(" AND l.empresa =  " + empresa);
        }
        sql.append(" and (p.lead = true or i.lead = true)");
        sql.append(" and (p.metaextra is null or p.metaextra = 'f') ");
        if(conversao){
            sql.append(" and (l.cliente is not null or i.cliente is not null)");
            sql.append(" AND EXISTS (SELECT codigo FROM contrato c WHERE c.pessoa = pes.codigo) ");
        }
        if(!contar){
            sql.append(" ORDER BY p.dia, i.datalancamento desc");
        }

        return criarConsulta(sql.toString(), con);
    }


    public LeadVO consultarPorCodigo(final Integer codigo, int nivelMontarDados) throws Exception {
        String sql = "SELECT * FROM lead WHERE codigo = " + codigo;
        Statement stm = con.createStatement();
        ResultSet tabelaResultado = stm.executeQuery(sql.toString());
        if (!tabelaResultado.next()) {
            return new LeadVO();
        }
        return (montarDados(tabelaResultado, nivelMontarDados, con));
    }
    
    public void executarAlteracaoClientePassivo(Integer codigoPassivo, Integer cliente) throws Exception {
        String sql = "UPDATE lead set cliente=? WHERE passivo = ?";
        PreparedStatement sqlAlterar = con.prepareStatement(sql);
        sqlAlterar.setInt(1, cliente);
        sqlAlterar.setInt(2, codigoPassivo);
        sqlAlterar.execute();
    }
    
     @Override
    public LeadVO consultarPorIndicado(Integer codigoIndicado, int nivelMontarDados) throws Exception {
        StringBuilder sql = new StringBuilder();
        sql.append("SELECT l.* FROM lead l WHERE indicado = ").append(codigoIndicado);
        Statement stm = con.createStatement();
        ResultSet tabelaResultado = stm.executeQuery(sql.toString());
         if (!tabelaResultado.next()) {
            return new LeadVO();
        }
        return (montarDados(tabelaResultado, nivelMontarDados, this.con));
    }
    
    public void executarAlteracaoClienteIndicado(Integer codigoIndicado, Integer cliente) throws Exception {
        String sql = "UPDATE lead set cliente=? WHERE indicado = ?";
        PreparedStatement sqlAlterar = con.prepareStatement(sql);
        sqlAlterar.setInt(1, cliente);
        sqlAlterar.setInt(2, codigoIndicado);
        sqlAlterar.execute();
    }
    
}

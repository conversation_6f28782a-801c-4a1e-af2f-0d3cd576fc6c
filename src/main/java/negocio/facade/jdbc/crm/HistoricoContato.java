package negocio.facade.jdbc.crm;

import br.com.pactosolucoes.comuns.util.Declaracao;
import br.com.pactosolucoes.enumeradores.FasesCRMEnum;
import br.com.pactosolucoes.enumeradores.TipoAgendamentoEnum;
import br.com.pactosolucoes.enumeradores.TipoEventoEnum;
import br.com.pactosolucoes.estrutura.paginacao.ConfPaginacao;
import br.com.pactosolucoes.estrutura.paginacao.PreparedStatementPersonalizado;
import br.com.pactosolucoes.oamd.controle.basico.DAO;
import controle.arquitetura.SuperControle;
import negocio.comuns.arquitetura.UsuarioVO;
import negocio.comuns.basico.*;
import negocio.comuns.conviteaulaexperimental.ConviteAulaExperimentalVO;
import negocio.comuns.crm.*;
import negocio.comuns.utilitarias.Calendario;
import negocio.comuns.utilitarias.ConsistirException;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisEmail;
import negocio.comuns.utilitarias.UteisValidacao;
import negocio.comuns.utilitarias.UtilReflection;
import negocio.facade.jdbc.arquitetura.SuperEntidade;
import negocio.facade.jdbc.arquitetura.Usuario;
import negocio.facade.jdbc.arquitetura.ZillyonWebFacade;
import negocio.facade.jdbc.basico.Cliente;
import negocio.facade.jdbc.crm.fecharMetaDetalhado.FecharMetaDetalhado;
import negocio.interfaces.crm.HistoricoContatoInterfaceFacade;
import org.apache.commons.lang.StringUtils;
import relatorio.negocio.comuns.sad.SituacaoClienteSinteticoEnum;
import servicos.operacoes.MailingItensController;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.sql.Statement;
import java.sql.Types;
import java.util.*;

/**
 * Classe de persistência que encapsula todas as operações de manipulação dos
 * dados da classe
 * <code>HistoricoContatoVO</code>. Responsável por implementar operações como
 * incluir, alterar, excluir e consultar pertinentes a classe
 * <code>HistoricoContatoVO</code>. Encapsula toda a interação com o banco de
 * dados.
 *
 * @see HistoricoContatoVO
 * @see SuperEntidade
 */
public class HistoricoContato extends SuperEntidade implements HistoricoContatoInterfaceFacade {

    public HistoricoContato() throws Exception {
        super();
    }

    public HistoricoContato(Connection conexao) throws Exception {
        super(conexao);
    }

    /**
     * Operação responsável por retornar um novo objeto da classe
     * <code>HistoricoContatoVO</code>.
     */
    @Override
    public HistoricoContatoVO novo() throws Exception {
        incluirCRM(getIdEntidade());
        return new HistoricoContatoVO();
    }

    /**
     * Operação responsável por incluir no banco de dados um objeto da classe
     * <code>HistoricoContatoVO</code>. Primeiramente valida os dados (
     * <code>validarDados</code>) do objeto. Verifica a conexão com o banco de
     * dados e a permissão do usuário para realizar esta operacão na entidade.
     * Isto, através da operação
     * <code>incluir</code> da superclasse.
     *
     * @param obj Objeto da classe <code>HistoricoContatoVO</code> que será
     *            gravado no banco de dados.
     * @throws Exception Caso haja problemas de conexão, restrição de acesso
     *                   ou validação de dados.
     */
    @Override
    public void incluir(HistoricoContatoVO obj) throws Exception {
        try {
            con.setAutoCommit(false);
            incluirSemCommit(obj);
            con.commit();
        } catch (Exception e) {
            obj.setNovoObj(true);
            con.rollback();
            con.setAutoCommit(true);
            throw e;
        } finally {
            con.setAutoCommit(true);
        }
    }

    /**
     * Operação responsável por incluir no banco de dados um objeto da classe
     * <code>HistoricoContatoVO</code>. Primeiramente valida os dados (
     * <code>validarDados</code>) do objeto. Verifica a conexão com o banco de
     * dados e a permissão do usuário para realizar esta operacão na entidade.
     * Isto, através da operação
     * <code>incluir</code> da superclasse.
     *
     * @param obj Objeto da classe <code>HistoricoContatoVO</code> que será
     *            gravado no banco de dados.
     * @throws Exception Caso haja problemas de conexão, restrição de acesso
     *                   ou validação de dados.
     */
    @Override
    public void incluirSemCommit(HistoricoContatoVO obj) throws Exception {
        try {
            incluirSemCommitSemAtualizarSintetico(obj);
            if (obj.getClienteVO().getCodigo() > 0) {
                ZillyonWebFacade zwFacade = new ZillyonWebFacade(con);
                zwFacade.atualizarSintetico(obj.getClienteVO(), Calendario.hoje(), SituacaoClienteSinteticoEnum.GRUPO_CONTATO, true);
            }

            if (obj.getCodigoFecharMetaDetalhado() != 0){
                ZillyonWebFacade zwFacade = new ZillyonWebFacade(con);
                zwFacade.getFecharMetaDetalhado().alterarSomenteTeveContato(obj.getCodigoFecharMetaDetalhado());
            }
        } catch (Exception e) {
            obj.setNovoObj(true);
            throw e;
        }
    }

    public void incluirSemCommitSemAtualizarSintetico(HistoricoContatoVO obj) throws Exception{
        obj.realizarUpperCaseDados();
        String sql = "INSERT INTO HistoricoContato( dia, cliente, passivo, indicado, observacao, maladireta, "
                + "tipoOperacao, responsavelCadastro, agenda, objecao, fase, resultado, tipoContato, "
                + "grausatisfacao,contatoavulso,codigoNotificacao, resposta, opcoes, conviteAulaExperimental, "
                + "dataProximoEnvio, wagienvi, origem, origemcodigo, fluxogymbot, tipogymbotenum)"
                + " VALUES ( ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ? , ?, ?, ?, ?, ?,?,?,?,?,?)";
        try (PreparedStatement sqlInserir = con.prepareStatement(sql)) {
            sqlInserir.setTimestamp(1, Uteis.getDataJDBCTimestamp(obj.getDia()));
            if (obj.getClienteVO().getCodigo().intValue() != 0) {
                sqlInserir.setInt(2, obj.getClienteVO().getCodigo().intValue());
            } else {
                sqlInserir.setNull(2, 0);
            }
            if (obj.getPassivoVO().getCodigo().intValue() != 0) {
                sqlInserir.setInt(3, obj.getPassivoVO().getCodigo().intValue());
            } else {
                sqlInserir.setNull(3, 0);
            }
            if (obj.getIndicadoVO().getCodigo().intValue() != 0) {
                sqlInserir.setInt(4, obj.getIndicadoVO().getCodigo().intValue());
            } else {
                sqlInserir.setNull(4, 0);
            }
            sqlInserir.setString(5, obj.getObservacao());
            if (obj.getMalaDiretaVO().getCodigo().intValue() != 0) {
                sqlInserir.setInt(6, obj.getMalaDiretaVO().getCodigo().intValue());
            } else {
                sqlInserir.setNull(6, 0);
            }
            sqlInserir.setString(7, obj.getTipoOperacao());
            if (obj.getResponsavelCadastro().getCodigo().intValue() != 0) {
                sqlInserir.setInt(8, obj.getResponsavelCadastro().getCodigo().intValue());
            } else {
                sqlInserir.setNull(8, 0);
            }
            if (obj.getAgendaVO().getCodigo().intValue() != 0) {
                sqlInserir.setInt(9, obj.getAgendaVO().getCodigo().intValue());
            } else {
                sqlInserir.setNull(9, 0);
            }
            if (obj.getObjecaoVO().getCodigo().intValue() != 0) {
                sqlInserir.setInt(10, obj.getObjecaoVO().getCodigo().intValue());
            } else {
                sqlInserir.setNull(10, 0);
            }
            sqlInserir.setString(11, obj.getFase());
            sqlInserir.setString(12, obj.getResultado());
            sqlInserir.setString(13, obj.getTipoContato());
            sqlInserir.setString(14, obj.getGrauSatisfacao());
            sqlInserir.setBoolean(15, obj.getContatoAvulso());
            resolveIntegerNull(sqlInserir, 16, obj.getCodigoNotificacao());
            sqlInserir.setString(17, obj.getResposta());
            sqlInserir.setString(18, obj.getOpcoes());
            if (UtilReflection.objetoMaiorQueZero(obj, "getConviteAulaExperimentalVO().getCodigo()")) {
                sqlInserir.setInt(19, obj.getConviteAulaExperimentalVO().getCodigo());
            } else {
                sqlInserir.setNull(19, Types.NULL);
            }
            if (obj.getDataProximoEnvio() == null) {
                sqlInserir.setNull(20, Types.NULL);
            } else {
                sqlInserir.setDate(20, Uteis.getDataJDBC(obj.getDataProximoEnvio()));
            }
            sqlInserir.setBoolean(21, obj.isWagienvi());
            sqlInserir.setString(22, obj.getOrigem());
            resolveIntegerNull(sqlInserir, 23, obj.getOrigemCodigo());
            if (UteisValidacao.emptyNumber(obj.getFluxoGymBot())) {
                sqlInserir.setInt(24, Types.NULL);
            } else {
                sqlInserir.setInt(24, obj.getFluxoGymBot());
            }
            if (obj.getTipoGymBotEnum() == null || UteisValidacao.emptyNumber(obj.getTipoGymBotEnum().getCodigo())) {
                sqlInserir.setInt(25, Types.NULL);
            } else {
                sqlInserir.setInt(25,obj.getTipoGymBotEnum().getCodigo());
            }
            sqlInserir.execute();
        }
        obj.setCodigo(obterValorChavePrimariaCodigo());
        obj.setNovoObj(false);
    }

    public void validarDadosHistorioAgendamentoFuturo(HistoricoContatoVO obj, AgendaVO agenda, Integer empresa) throws Exception {
        if ((Uteis.getCompareData(agenda.getDataAgendamento(), Calendario.hoje()) > 0)
                && getFacade().getAberturaMeta().consultarAberturaPorCodigoUsuario(obj.getColaboradorResponsavel().getCodigo(), empresa, agenda.getDataAgendamento())) {
            throw new ConsistirException("Não foi possivel realizar o agendamento para o dia informado: " + agenda.getDataAgendamento_Apresentar() + ", pois o colaborador responsável ja realizou uma abertura meta para esse dia !");
        }
    }

    /**
     * Método responsavel por incluir primeiramente a agenda, apos inclusão da
     * agenda inclui o Historico Contato. Chama metodos que atualiza as metas e
     * metas atingidas.
     */
    @Override

    public void incluirHistoricoContatoAgenda(HistoricoContatoVO obj, AgendaVO agenda, String tipoResultado, Integer empresa) throws Exception {
        incluirHistoricoContatoAgenda(obj, agenda, tipoResultado, empresa, null);
    }

    public void incluirHistoricoContatoAgenda(HistoricoContatoVO obj, AgendaVO agenda, String tipoResultado, Integer empresa, UsuarioVO usuarioVO) throws Exception {
        try {
            FecharMetaDetalhado fecharMetaDetalhadoDAO = new FecharMetaDetalhado(con);
            con.setAutoCommit(false);
            HistoricoContatoVO.validarDados(obj);
            incluirCRM(getIdEntidade());
            if (tipoResultado.equals("AG") || (tipoResultado.equals("LA") )) {
                validarDadosHistorioAgendamentoFuturo(obj, agenda, empresa);
                obj.setObjecaoVO(new ObjecaoVO());

                if(usuarioVO != null && usuarioVO.getNome().trim() != "" && !usuarioVO.getColaboradorVO().getCodAcesso().equals("")) {
                    obj.setObservacao(obj.getObservacao() +  System.lineSeparator() +   System.lineSeparator() + "Usuário informado no agendamento: " + usuarioVO.getNome() );
                    getFacade().getAgenda().preencherDadosAgenda(agenda, obj, obj.getTipoOperacao(), true, usuarioVO);
                } else {
                    getFacade().getAgenda().preencherDadosAgenda(agenda, obj, obj.getTipoOperacao(), true);
                }
            } else if (!tipoResultado.equals("OB") || !obj.getFase().equals("LA")) { //objeção para agendados de amanhã é batida em outro lugar
                obj.setAgendaVO(new AgendaVO());
                obj.setResultado(obj.qualResultado(false, tipoResultado));
                if (!obj.getContatoAvulso() && obj.getCodigoFecharMetaDetalhado() != 0) {
                    getFacade().getFecharMeta().executarValidacaoQuandoGravaHistoricoContatoAlcancaMetaAtingida(obj.getDiaAbertura(),
                            obj.getFase(), obj.getCodigoFecharMetaDetalhado(), obj.getAberturaMetaEstaEmAberta(), fecharMetaDetalhadoDAO);
                }
            }
            obj.setResultado(obj.qualResultado(false, tipoResultado));

            incluirSemCommit(obj);
            if(tipoResultado.equals("AG") || tipoResultado.equals("LA") ){
                // valido de novo se é agendamento para executar uma regra de negocio depois da gravação de historico.
                    // pois quando a cliente ou passivo e indicado realizar um agendamento entao é gerado um registro na tabela fecha meta
                // se o registro for para hoje
                getFacade().getAgenda().executarRegraNegocioParaFecharMetaInclusao(obj.getDia(), agenda, obj.getColaboradorResponsavel().getCodigo(), obj.getPassivoVO().getCodigo(), obj.getIndicadoVO().getCodigo(), obj.getClienteVO().getCodigo(), obj);
            }
            if (!obj.getContatoAvulso()) {
                if (tipoResultado.equals("AG")) {
                    if (obj.getCodigoFecharMetaDetalhado() != 0) {
                        getFacade().getFecharMeta().executarValidacaoQuandoGravaAgendaAlcancaMetaAtingida(obj.getDiaAbertura(), agenda, obj.getFase(), obj.getCodigoFecharMetaDetalhado(), true);
                    }
                }
                if (obj.getCodigoFecharMetaDetalhado() != 0
                        && (!obj.getFase().equals("AG") || (obj.getFase().equals("AG") &&
                        (!(Uteis.getCompareData(agenda.getDataAgendamento(), obj.getReagendamentoVO().getDataAgendamento()) == 0) || getFacade().getFecharMetaDetalhado().isAgendadoLigacao(obj.getCodigoFecharMetaDetalhado()))))) { // se for um reagendamento para o mesmo dia, o sistema não grava o contrato para não dar problema nos reagendados da meta
                    getFacade().getFecharMetaDetalhado().alterarSomenteCamposHistoricoContato(obj.getClienteVO().getCodigo(), null, obj.getDiaAbertura(), obj.getFase(), obj.getCodigo());
                }
                if (tipoResultado.equals("OB") && !obj.getAberturaMetaEstaEmAberta() && !obj.getFase().equals("AG") && obj.getCodigoFecharMetaDetalhado() != 0) {// bate meta de repescagem quando e objeçao
                    getFacade().getFecharMeta().executarAtualizacaoRespescagemObjecao(obj.getCodigoFecharMetaDetalhado(), fecharMetaDetalhadoDAO);
                }
            }
            con.commit();
        } catch (Exception e) {
            obj.setNovoObj(true);
            con.rollback();
            con.setAutoCommit(true);
            throw e;
        } finally {
            con.setAutoCommit(true);
        }
    }

    /**
     * Método responsável por gravar o historico Contato e Agenda
     *
     * @throws Exception
     */
    @Override

    public void gravarHistoricoContato(String tipoResultado, HistoricoContatoVO obj, AgendaVO agenda, MalaDiretaVO malaDiretaVO, Integer empresa) throws Exception{
        obj.getEmpresa().setCodigo(empresa);

        ConfiguracaoSistemaCRMVO config = getFacade().getConfiguracaoSistemaCRM().consultarPorChavePrimaria(1, Uteis.NIVELMONTARDADOS_DADOSBASICOS);

        if (!obj.getTipoContato().equals("EM")) {
            if (obj.isNovoObj()) {
                if (tipoResultado.equals("OB") && obj.getObjecaoVO().getTipoGrupo().equals("OD")) {
                    incluirObjecaoDefinitiva(obj);
                }
                incluirHistoricoContatoAgenda(obj, agenda, tipoResultado, empresa);
                if (!obj.getContatoAvulso()) {
                    if (config.isBaterMetaTodasAcoes()) {
                        if(obj.getFase().equals(FasesCRMEnum.AGENDAMENTO.getSigla())){
                            getFacade().getFecharMetaDetalhado().baterMetaPorFase(obj.getClienteVO().getCodigo(), null, obj.getDia(), "AG", obj.getCodigo(),obj.getPassivoVO().getCodigo(), obj.getIndicadoVO().getCodigo());
                        }else{
                            getFacade().getFecharMetaDetalhado().baterMetaPorFase(obj.getClienteVO().getCodigo(), null, obj.getDia(), "%", obj.getCodigo(),obj.getPassivoVO().getCodigo(), obj.getIndicadoVO().getCodigo());
                        }
                        if (obj.getFase().equals(FasesCRMEnum.AGENDAMENTO.getSigla()) && tipoResultado.equals("OB")) {
                            marcarFecharDetalhadoAtingido(obj);
                        }
                    } else {
                        if (tipoResultado.equals("OB") || tipoResultado.equals("AG")) {
                            getFacade().getFecharMetaDetalhado().baterMetaPorFase(obj.getClienteVO().getCodigo(), null, obj.getDia(), FasesCRMEnum.VENCIDOS.getSigla(), obj.getCodigo(),0 ,0);
                            getFacade().getFecharMetaDetalhado().baterMetaPorFase(obj.getClienteVO().getCodigo(), null, obj.getDia(), FasesCRMEnum.EX_ALUNOS.getSigla(), obj.getCodigo(),0 ,0);
                            getFacade().getFecharMetaDetalhado().baterMetaPorFase(obj.getClienteVO().getCodigo(), null, obj.getDia(), FasesCRMEnum.VINTE_QUATRO_HORAS.getSigla(), obj.getCodigo(),0 ,0);
                            getFacade().getFecharMetaDetalhado().baterMetaPorFase(obj.getClienteVO().getCodigo(), null, obj.getDia(), FasesCRMEnum.VISITANTES_ANTIGOS.getSigla(), obj.getCodigo(),0 ,0);
                            getFacade().getFecharMetaDetalhado().baterMetaPorFase(obj.getClienteVO().getCodigo(), null, obj.getDia(), FasesCRMEnum.RENOVACAO.getSigla(), obj.getCodigo(),0 ,0);
                            getFacade().getFecharMetaDetalhado().baterMetaPorFase(obj.getClienteVO().getCodigo(), null, obj.getDia(), FasesCRMEnum.POS_VENDA.getSigla(), obj.getCodigo(),0 ,0);
                            getFacade().getFecharMetaDetalhado().baterMetaPorFase(obj.getClienteVO().getCodigo(), null, obj.getDia(), FasesCRMEnum.LEADS_HOJE.getSigla(), obj.getCodigo(), obj.getPassivoVO().getCodigo() , obj.getIndicadoVO().getCodigo());
                            getFacade().getFecharMetaDetalhado().baterMetaPorFase(obj.getClienteVO().getCodigo(), null, obj.getDia(), FasesCRMEnum.LEADS_ACUMULADAS.getSigla(), obj.getCodigo(), obj.getPassivoVO().getCodigo() ,obj.getIndicadoVO().getCodigo());

                            if (obj.getFase().equals(FasesCRMEnum.AGENDAMENTO.getSigla() ) || obj.getFase().equals(FasesCRMEnum.INDICACOES.getSigla())) {
                                marcarFecharDetalhadoAtingido(obj);
                            }
                        }
                        if (tipoResultado.equals("OB")) {
                            getFacade().getFecharMetaDetalhado().baterMetaPorFase(obj.getClienteVO().getCodigo(), null, obj.getDia(), FasesCRMEnum.DESISTENTES.getSigla(), obj.getCodigo(),0 ,0);
                        }
                        getFacade().getFecharMetaDetalhado().baterMetaPorFase(obj.getClienteVO().getCodigo(), null, obj.getDia(), FasesCRMEnum.LIGACAO_AGENDADOS_AMANHA.getSigla(), obj.getCodigo(),obj.getPassivoVO().getCodigo() ,obj.getIndicadoVO().getCodigo());
                        getFacade().getFecharMetaDetalhado().baterMetaPorFase(obj.getClienteVO().getCodigo(), null, obj.getDia(), FasesCRMEnum.ULTIMAS_SESSOES.getSigla(), obj.getCodigo(),0 ,0);
                        getFacade().getFecharMetaDetalhado().baterMetaPorFase(obj.getClienteVO().getCodigo(), null, obj.getDia(), FasesCRMEnum.SEM_AGENDAMENTO.getSigla(), obj.getCodigo(),0 ,0);
                    }
                }
            } else {
                getFacade().getHistoricoContato().alterarHistoricoContatoAgenda(obj, agenda, tipoResultado, empresa);
            }
        } else {
            String email = "";
            if (!UteisValidacao.emptyNumber(obj.getClienteVO().getPessoa().getCodigo())) {
                email = obj.getClienteVO().getPessoa().getEmail();
            } else if (!UteisValidacao.emptyNumber(obj.getPassivoVO().getCodigo())) {
                email = obj.getPassivoVO().getEmail();
            } else if (!UteisValidacao.emptyNumber(obj.getIndicadoVO().getCodigo())) {
                email = obj.getIndicadoVO().getEmail();
            }

            if (!email.isEmpty() && UteisEmail.getValidEmail(email)) {
                getFacade().getHistoricoContato().gravarEmail(malaDiretaVO, obj);
            } else {
                throw new ConsistirException("O e-mail do do destinatario: " + obj.getClienteVO().getPessoa().getNome() + " é invalido.");
            }
        }
    }

    public void gravarHistoricoContatoComColaborador(String tipoResultado, HistoricoContatoVO obj, AgendaVO agenda, MalaDiretaVO malaDiretaVO, Integer empresa, UsuarioVO usuarioVO) throws Exception {
        obj.getEmpresa().setCodigo(empresa);

        ConfiguracaoSistemaCRMVO config = getFacade().getConfiguracaoSistemaCRM().consultarPorChavePrimaria(1, Uteis.NIVELMONTARDADOS_DADOSBASICOS);

        if (!obj.getTipoContato().equals("EM")) {
            if (obj.isNovoObj()) {
                if (tipoResultado.equals("OB") && obj.getObjecaoVO().getTipoGrupo().equals("OD")) {
                    incluirObjecaoDefinitiva(obj);
                }
                if (!obj.getContatoAvulso()) {
                    if (config.isBaterMetaTodasAcoes()) {
                        getFacade().getFecharMetaDetalhado().baterMetaPorFase(obj.getClienteVO().getCodigo(), null, obj.getDia(), "%", obj.getCodigo(),obj.getPassivoVO().getCodigo(), obj.getIndicadoVO().getCodigo());
                        if (obj.getFase().equals(FasesCRMEnum.AGENDAMENTO.getSigla()) && tipoResultado.equals("OB")) {
                            marcarFecharDetalhadoAtingido(obj);
                        }
                    } else {
                        if (tipoResultado.equals("OB") || tipoResultado.equals("AG")) {
                            getFacade().getFecharMetaDetalhado().baterMetaPorFase(obj.getClienteVO().getCodigo(), null, obj.getDia(), FasesCRMEnum.VENCIDOS.getSigla(), obj.getCodigo(),0 ,0);
                            getFacade().getFecharMetaDetalhado().baterMetaPorFase(obj.getClienteVO().getCodigo(), null, obj.getDia(), FasesCRMEnum.EX_ALUNOS.getSigla(), obj.getCodigo(),0 ,0);
                            getFacade().getFecharMetaDetalhado().baterMetaPorFase(obj.getClienteVO().getCodigo(), null, obj.getDia(), FasesCRMEnum.VINTE_QUATRO_HORAS.getSigla(), obj.getCodigo(),0 ,0);
                            getFacade().getFecharMetaDetalhado().baterMetaPorFase(obj.getClienteVO().getCodigo(), null, obj.getDia(), FasesCRMEnum.VISITANTES_ANTIGOS.getSigla(), obj.getCodigo(),0 ,0);
                            getFacade().getFecharMetaDetalhado().baterMetaPorFase(obj.getClienteVO().getCodigo(), null, obj.getDia(), FasesCRMEnum.RENOVACAO.getSigla(), obj.getCodigo(),0 ,0);
                            getFacade().getFecharMetaDetalhado().baterMetaPorFase(obj.getClienteVO().getCodigo(), null, obj.getDia(), FasesCRMEnum.POS_VENDA.getSigla(), obj.getCodigo(),0 ,0);
                            getFacade().getFecharMetaDetalhado().baterMetaPorFase(obj.getClienteVO().getCodigo(), null, obj.getDia(), FasesCRMEnum.LEADS_HOJE.getSigla(), obj.getCodigo(), obj.getPassivoVO().getCodigo() , obj.getIndicadoVO().getCodigo());
                            getFacade().getFecharMetaDetalhado().baterMetaPorFase(obj.getClienteVO().getCodigo(), null, obj.getDia(), FasesCRMEnum.LEADS_ACUMULADAS.getSigla(), obj.getCodigo(), obj.getPassivoVO().getCodigo() ,obj.getIndicadoVO().getCodigo());
                            getFacade().getFecharMetaDetalhado().baterMetaPorFase(obj.getClienteVO().getCodigo(), null, obj.getDia(), FasesCRMEnum.GRUPO_RISCO.getSigla(), obj.getCodigo(),0 ,0);
                            getFacade().getFecharMetaDetalhado().baterMetaPorFase(obj.getClienteVO().getCodigo(), null, obj.getDia(), FasesCRMEnum.FALTOSOS.getSigla(), obj.getCodigo(),0 ,0);
                            getFacade().getFecharMetaDetalhado().baterMetaPorFase(obj.getClienteVO().getCodigo(), null, obj.getDia(), FasesCRMEnum.AGENDAMENTOS_LIGACOES.getSigla(), obj.getCodigo(),0 ,0);
                            if (obj.getFase().equals(FasesCRMEnum.AGENDAMENTO.getSigla()) || obj.getFase().equals(FasesCRMEnum.INDICACOES.getSigla()) ) {
                                marcarFecharDetalhadoAtingido(obj);
                            }
                        }
                        if (tipoResultado.equals("OB")) {
                            getFacade().getFecharMetaDetalhado().baterMetaPorFase(obj.getClienteVO().getCodigo(), null, obj.getDia(), FasesCRMEnum.DESISTENTES.getSigla(), obj.getCodigo(),0 ,0);
                        }
                        getFacade().getFecharMetaDetalhado().baterMetaPorFase(obj.getClienteVO().getCodigo(), null, obj.getDia(), FasesCRMEnum.LIGACAO_AGENDADOS_AMANHA.getSigla(), obj.getCodigo(),0 ,0);
                        getFacade().getFecharMetaDetalhado().baterMetaPorFase(obj.getClienteVO().getCodigo(), null, obj.getDia(), FasesCRMEnum.ULTIMAS_SESSOES.getSigla(), obj.getCodigo(),0 ,0);
                        getFacade().getFecharMetaDetalhado().baterMetaPorFase(obj.getClienteVO().getCodigo(), null, obj.getDia(), FasesCRMEnum.SEM_AGENDAMENTO.getSigla(), obj.getCodigo(),0 ,0);
                    }
                }
                incluirHistoricoContatoAgenda(obj, agenda, tipoResultado, empresa, usuarioVO);
            } else {
                getFacade().getHistoricoContato().alterarHistoricoContatoAgenda(obj, agenda, tipoResultado, empresa);
            }
        } else {
            String email = "";
            if (!UteisValidacao.emptyNumber(obj.getClienteVO().getPessoa().getCodigo())) {
                email = obj.getClienteVO().getPessoa().getEmail();
            } else if (!UteisValidacao.emptyNumber(obj.getPassivoVO().getCodigo())) {
                email = obj.getPassivoVO().getEmail();
            } else if (!UteisValidacao.emptyNumber(obj.getIndicadoVO().getCodigo())) {
                email = obj.getIndicadoVO().getEmail();
            }

            if (!email.isEmpty() && UteisEmail.getValidEmail(email)) {
                getFacade().getHistoricoContato().gravarEmail(malaDiretaVO, obj);
            } else {
                throw new ConsistirException("O e-mail do do destinatario: " + obj.getClienteVO().getPessoa().getNome() + " é invalido.");
            }
        }

    }

    /**
     * Método responsavel por gravar o email caso o tipo de Contato seja e-mail.
     *
     * @throws Exception
     */
    @Override
    public void gravarEmail(MalaDiretaVO mensagem, HistoricoContatoVO hist) throws Exception {
        try {
            mensagem.setTipoAgendamento(TipoAgendamentoEnum.AGENDAMENTO_INSTANTANEO);
            con.setAutoCommit(false);
            if (mensagem.isNovoObj()) {
                if (!mensagem.getRemetente().getNome().equals("")) {
                    mensagem.setRemetente(getFacade().getUsuario().consultarPorNomeUsuario(mensagem.getRemetente().getNome(), false, Uteis.NIVELMONTARDADOS_TELACONSULTA));
                }
                mensagem.setConfiguracaoSistemaCRMVO(SuperControle.getConfiguracaoSMTPNoReply());
                UteisEmail email = new UteisEmail();
                email.novo(mensagem.getTitulo(), mensagem.getConfiguracaoSistemaCRMVO());
                email.setRemetente(mensagem.getRemetente());
                MalaDiretaEnviadaVO not = new MalaDiretaEnviadaVO();
                if (hist.getClienteVO().getCodigo() != 0) {
                    not.setClienteVO(hist.getClienteVO());
                } else if (hist.getPassivoVO().getCodigo() != 0) {
                    not.setPassivoVO(hist.getPassivoVO());
                } else {
                    not.setIndicadoVO(hist.getIndicadoVO());
                }
                mensagem.getMalaDiretaEnviadaVOs().add(not);

                MalaDireta malaDireta = new MalaDireta(con);
                malaDireta.agendarEnvioEmailSemCommit(mensagem, false, Calendario.hoje(), hist.getEmpresa());

            }
            con.commit();
        } catch (javax.mail.SendFailedException emailInvalido) {
            con.rollback();
            con.setAutoCommit(true);
            String msg;
            if (emailInvalido.toString().contains("Invalid Addresses")) {
                msg = "E-mail com domínio inválido : "
                        + emailInvalido.toString().substring(emailInvalido.toString().indexOf("<") + 1, emailInvalido.toString().indexOf(">"));
            } else {
                msg = emailInvalido.toString();
            }
            throw new Exception(msg);
        } catch (Exception e) {
            con.rollback();
            con.setAutoCommit(true);
            throw e;
        } finally {
            con.setAutoCommit(true);
        }
    }

    public void preencherCamposHistoricoContatoQuandoEnviarMailing(MalaDiretaVO mensagemVO, HistoricoContatoVO histVO) {
        if (mensagemVO.getMeioDeEnvioEnum() == MeioEnvio.SMS) {
            histVO.setResultado("Envio SMS");
        } else if (mensagemVO.getMeioDeEnvioEnum() == MeioEnvio.EMAIL) {
            histVO.setResultado("Envio E-mail");
        } else if (mensagemVO.getMeioDeEnvioEnum() == MeioEnvio.APP) {
            histVO.setResultado("Envio APP");
        }
        histVO.setObservacao("<p> Título: " + mensagemVO.getTitulo() + " </p> "
                + "<p>Data de envio: " + mensagemVO.getDataEnvio_Apresentar() + " </p> "
                + "<p>Remente: " + mensagemVO.getRemetente().getNome() + " </p> "
                + "<p>------------------------------------------------------ " + "</p> "
                + "<p>Mensagem:" + mensagemVO.getMensagem() + "</p> ");
        histVO.getMalaDiretaVO().setCodigo(mensagemVO.getCodigo());
        if(mensagemVO.getCfgEvento() != null && mensagemVO.getCfgEvento().getEvento() != null &&
                mensagemVO.getCfgEvento().getEvento().equals(TipoEventoEnum.PARCELAS_VENCIDAS)){
            Integer dias = mensagemVO.getEmpresa().getQtdDiasVencimentoBoleto();
            if(dias == null){
                dias = SuperControle.quantidadePadraoDiasEnviarEmailParcelasVencidas;
            }
            dias++;
            histVO.setDataProximoEnvio(Uteis.somarDias(Calendario.hoje(), dias));
        }
    }

    public Integer executarGravacaoVindoGymbot(MalaDiretaVO mensagemVO, Integer passivo, Integer cliente, String observacao, Integer codigoFluxoGymBot) throws Exception {
        HistoricoContatoVO hist = new HistoricoContatoVO();

        hist.setObservacao(observacao);
        hist.setDia(negocio.comuns.utilitarias.Calendario.hoje());
        if (mensagemVO.getMeioDeEnvio().equals(MeioEnvio.GYMBOT.getCodigo())){
            hist.setTipoContato("BT");
            hist.setResultado("Envio GymBot");
            hist.setFluxoGymBot(codigoFluxoGymBot);
            hist.setTipoGymBotEnum(TipoGymBotEnum.GYMBOT_LITE);
        }
        if (mensagemVO.getMeioDeEnvio().equals(MeioEnvio.GYMBOT_PRO.getCodigo())){
            hist.setTipoContato("GP");
            hist.setResultado("Envio GymBot Pro");
            hist.setFluxoGymBot(codigoFluxoGymBot);
            hist.setTipoGymBotEnum(TipoGymBotEnum.GYMBOT_PRO);
        }
        hist.setResponsavelCadastro(mensagemVO.getRemetente());
        if (cliente != 0) {
            hist.getClienteVO().setCodigo(cliente);
        }
        hist.setWagienvi(false);
        hist.setContatoAvulso(mensagemVO.isContatoAvulso());
        hist.setFase(mensagemVO.getFaseEnvio());
        HistoricoContatoVO.validarDados(hist);
        incluirSemCommit(hist);
        return hist.getCodigo();
    }

    @Override
    public Integer executarGravacaoVindoEmail(MalaDiretaVO mensagemVO, Integer passivo, Integer cliente, Integer indicado, Integer codigoNotificacao, boolean wagienvi) throws Exception {
        HistoricoContatoVO hist = new HistoricoContatoVO();
        preencherCamposHistoricoContatoQuandoEnviarMailing(mensagemVO, hist);
        hist.setDia(negocio.comuns.utilitarias.Calendario.hoje());
        if (codigoNotificacao != null && codigoNotificacao > 0) {
            hist.setTipoContato("AP");
            hist.setCodigoNotificacao(codigoNotificacao);
        } else if (mensagemVO.getMeioDeEnvio().equals(MeioEnvio.EMAIL.getCodigo())) {
            hist.setTipoContato("EM");
        } else if (mensagemVO.getMeioDeEnvio().equals(MeioEnvio.SMS.getCodigo())){
            hist.setTipoContato("CS");
        } else if (mensagemVO.getMeioDeEnvio().equals(MeioEnvio.WHATSAPP.getCodigo())){
            hist.setTipoContato("WH");
            hist.setResultado("Envio WhatsApp");
        } else if (mensagemVO.getMeioDeEnvio().equals(MeioEnvio.GYMBOT.getCodigo())){
            hist.setTipoContato("BT");
            hist.setResultado("Envio GymBot");
            hist.setTipoGymBotEnum(TipoGymBotEnum.GYMBOT_LITE);
            hist.setFluxoGymBot(mensagemVO.getCodigoFluxoGymBotConversa());
        } else if (mensagemVO.getMeioDeEnvio().equals(MeioEnvio.GYMBOT_PRO.getCodigo())){
            hist.setTipoContato("GP");
            hist.setResultado("Envio GymBot Pro");
            hist.setTipoGymBotEnum(TipoGymBotEnum.GYMBOT_PRO);
            hist.setFluxoGymBot(mensagemVO.getConfigGymbotPro().getCodigo());
        }else{
            //Expurga - se tentar gravar o historico para app com codigo notificacao null ou 0
            if( mensagemVO.getMeioDeEnvio().equals(MeioEnvio.APP.getCodigo())
                    && codigoNotificacao == null || (codigoNotificacao == 0 &&   mensagemVO.getMeioDeEnvio().equals(MeioEnvio.APP.getCodigo())))
                    return 0;
        }
        hist.setResponsavelCadastro(mensagemVO.getRemetente());
        if (passivo != 0) {
            hist.getPassivoVO().setCodigo(passivo);
        } else if (indicado != 0) {
            hist.getIndicadoVO().setCodigo(indicado);
        } else if (cliente != 0) {
            hist.getClienteVO().setCodigo(cliente);
        }
        hist.setWagienvi(wagienvi);
        hist.setContatoAvulso(mensagemVO.isContatoAvulso());
        hist.setFase(mensagemVO.getFaseEnvio());
        HistoricoContatoVO.validarDados(hist);
        incluirSemCommit(hist);
        return hist.getCodigo();
    }

    /**
     * Operação responsável por alterar no BD os dados de um objeto da classe
     * <code>HistoricoContatoVO</code>. Sempre utiliza a chave primária da
     * classe como atributo para localização do registro a ser alterado.
     * Primeiramente valida os dados (
     * <code>validarDados</code>) do objeto. Verifica a conexão com o banco de
     * dados e a permissão do usuário para realizar esta operacão na entidade.
     * Isto, através da operação
     * <code>alterar</code> da superclasse.
     *
     * @param obj Objeto da classe <code>HistoricoContatoVO</code> que será
     *            alterada no banco de dados.
     * @throws Exception Caso haja problemas de conexão, restrição de acesso
     *                   ou validação de dados.
     */
    @Override
    public void alterar(HistoricoContatoVO obj) throws Exception {
        try {
            con.setAutoCommit(false);
            alterarSemCommit(obj);
            con.commit();
        } catch (Exception e) {
            con.rollback();
            con.setAutoCommit(true);
            throw e;
        } finally {
            con.setAutoCommit(true);
        }
    }

    @Override
    public void alterarSemCommit(HistoricoContatoVO obj) throws Exception {
        try {
            HistoricoContatoVO.validarDados(obj);
            alterarCRM(getIdEntidade());
            obj.realizarUpperCaseDados();
            String sql = "UPDATE HistoricoContato set dia=?, cliente=?, "
                    + "passivo=?, indicado=?, observacao=?, maladireta=?, "
                    + "tipoOperacao=?, responsavelCadastro=?, agenda=?, objecao=?, "
                    + "fase=?, resultado=?, tipoContato=?, "
                    + "grausatisfacao=?,contatoavulso = ?, "
                    + "codigoNotificacao = ?, opcoes = ?, resposta = ? WHERE ((codigo = ?))";
            try (PreparedStatement sqlAlterar = con.prepareStatement(sql)) {
                sqlAlterar.setTimestamp(1, Uteis.getDataJDBCTimestamp(obj.getDia()));
                if (obj.getClienteVO().getCodigo().intValue() != 0) {
                    sqlAlterar.setInt(2, obj.getClienteVO().getCodigo().intValue());
                } else {
                    sqlAlterar.setNull(2, 0);
                }
                if (obj.getPassivoVO().getCodigo().intValue() != 0) {
                    sqlAlterar.setInt(3, obj.getPassivoVO().getCodigo().intValue());
                } else {
                    sqlAlterar.setNull(3, 0);
                }
                if (obj.getIndicadoVO().getCodigo().intValue() != 0) {
                    sqlAlterar.setInt(4, obj.getIndicadoVO().getCodigo().intValue());
                } else {
                    sqlAlterar.setNull(4, 0);
                }
                sqlAlterar.setString(5, obj.getObservacao());
                if (obj.getMalaDiretaVO().getCodigo().intValue() != 0) {
                    sqlAlterar.setInt(6, obj.getMalaDiretaVO().getCodigo().intValue());
                } else {
                    sqlAlterar.setNull(6, 0);
                }
                sqlAlterar.setString(7, obj.getTipoOperacao());
                if (obj.getResponsavelCadastro().getCodigo().intValue() != 0) {
                    sqlAlterar.setInt(8, obj.getResponsavelCadastro().getCodigo().intValue());
                } else {
                    sqlAlterar.setNull(8, 0);
                }
                if (obj.getAgendaVO().getCodigo().intValue() != 0) {
                    sqlAlterar.setInt(9, obj.getAgendaVO().getCodigo().intValue());
                } else {
                    sqlAlterar.setNull(9, 0);
                }
                if (obj.getObjecaoVO().getCodigo().intValue() != 0) {
                    sqlAlterar.setInt(10, obj.getObjecaoVO().getCodigo().intValue());
                } else {
                    sqlAlterar.setNull(10, 0);
                }
                sqlAlterar.setString(11, obj.getFase());
                sqlAlterar.setString(12, obj.getResultado());
                sqlAlterar.setString(13, obj.getTipoContato());
                sqlAlterar.setString(14, obj.getGrauSatisfacao());
                sqlAlterar.setBoolean(15, obj.getContatoAvulso());
                if (!UteisValidacao.emptyNumber(obj.getCodigoNotificacao())) {
                    sqlAlterar.setInt(16, obj.getCodigoNotificacao());
                } else {
                    sqlAlterar.setNull(16, 0);
                }
                sqlAlterar.setString(17, obj.getOpcoes());
                sqlAlterar.setString(18, obj.getResposta());
                sqlAlterar.setInt(19, obj.getCodigo().intValue());
                sqlAlterar.execute();
            }
            getFacade().getZWFacade().atualizarSintetico(obj.getClienteVO(),
                    Calendario.hoje(), SituacaoClienteSinteticoEnum.GRUPO_CONTATO, true);
        } catch (Exception e) {
            throw e;
        }
    }

    /**
     * Método que define quais alterações serão feitas, se é Objeção, Agenda ou
     * Simples registro.
     */
    @Override
    public void alterarHistoricoContatoAgenda(HistoricoContatoVO obj, AgendaVO agenda, String botaoApertado, Integer empresa) throws Exception {
        try {
            con.setAutoCommit(false);
            HistoricoContatoVO.validarDados(obj);
            alterarCRM(getIdEntidade());
            if (botaoApertado.equals("OB")) {
                alterarObjecaoHistoricoContato(obj);
            }
            if (botaoApertado.equals("AG")) {
                validarDadosHistorioAgendamentoFuturo(obj, agenda, empresa);
                alterarAgendaHistoricoContato(obj, agenda);
            }
            if (botaoApertado.equals("SR")) {
                obj.setAgendaVO(agenda);
                alterarSimplesRegistroHistoricoContato(obj);
            }
            con.commit();
        } catch (Exception e) {
            obj.setNovoObj(true);
            con.rollback();
            con.setAutoCommit(true);
            throw e;
        } finally {
            con.setAutoCommit(true);
        }
    }

    /**
     * Método responsavel por alterar uma agenda setando zero na Objeção.
     *
     * @param obj
     * @param agenda
     * @throws Exception
     */
    public void alterarAgendaHistoricoContato(HistoricoContatoVO obj, AgendaVO agenda) throws Exception {
        try {
            if (obj.getObjecaoVO().getCodigo().intValue() != 0) {
                obj.getObjecaoVO().setCodigo(0);
            }
            if (!obj.getContatoAvulso()) {
                getFacade().getAgenda().executarRegraNegocioParaFecharMetaAlteracao(obj.getDiaAbertura(), agenda, obj.getColaboradorResponsavel().getCodigo(), obj.getPassivoVO().getCodigo(), obj.getIndicadoVO().getCodigo(), obj.getClienteVO().getCodigo(), obj);
                if (obj.getCodigoFecharMetaDetalhado().intValue() != 0) {
                    getFacade().getFecharMeta().executarValidacaoQuandoGravaAgendaAlcancaMetaAtingida(obj.getDiaAbertura(), agenda, obj.getFase(), obj.getCodigoFecharMetaDetalhado(), false);
                }
            }
            getFacade().getAgenda().preencherDadosAgenda(agenda, obj, obj.getTipoOperacao(), false);
            alterarSemCommit(obj);
        } catch (Exception e) {
            throw e;
        }

    }

    /**
     * Método responsavel por alterar a Objeção e excluir um agendamento caso
     * haja algum.
     *
     * @param obj
     * @throws Exception
     */
    public void alterarObjecaoHistoricoContato(HistoricoContatoVO obj) throws Exception {
        try {
            int codigoAgendaTemporario = 0;
            if (!obj.getContatoAvulso()) {
                getFacade().getObjecao().executarRegraNegocioParaFecharMetaAlteracao(obj.getDiaAbertura(), obj.getColaboradorResponsavel().getCodigo(), obj.getPassivoVO().getCodigo(), obj.getIndicadoVO().getCodigo(), obj.getClienteVO().getCodigo(), obj);
            }
            if (obj.getAgendaVO().getCodigo().intValue() != 0) {
                codigoAgendaTemporario = obj.getAgendaVO().getCodigo().intValue();
                obj.getAgendaVO().setCodigo(0);
            }
            alterarSemCommit(obj);
            getFacade().getAgenda().excluirSemCommit(codigoAgendaTemporario);

        } catch (Exception e) {
            obj.setNovoObj(true);
            throw e;
        }
    }

    /**
     * Método responsavel por alterar quando o cliente clicar no botao simples
     * registro. Ele seta zero para a Objeção e seta zero para a Agenda e
     * excluir uma agenda caso tenha sido feita alguma gravação.
     *
     * @param obj
     * @throws Exception
     */
    public void alterarSimplesRegistroHistoricoContato(HistoricoContatoVO obj) throws Exception {
        try {
            if (!obj.getContatoAvulso()) {
                getFacade().getObjecao().executarRegraNegocioParaFecharMetaAlteracao(obj.getDiaAbertura(), obj.getColaboradorResponsavel().getCodigo(), obj.getPassivoVO().getCodigo(), obj.getIndicadoVO().getCodigo(), obj.getClienteVO().getCodigo(), obj);
            }
            if (obj.getObjecaoVO().getCodigo().intValue() != 0) {
                obj.getObjecaoVO().setCodigo(0);
            }
            int codigoAgendaTemporario = 0;
            if (obj.getAgendaVO().getCodigo().intValue() != 0) {
                codigoAgendaTemporario = obj.getAgendaVO().getCodigo().intValue();
                obj.getAgendaVO().setCodigo(0);
            }
            obj.setResultado("Simples Registro");
            alterarSemCommit(obj);
            getFacade().getAgenda().excluirSemCommit(codigoAgendaTemporario);
        } catch (Exception e) {
            obj.setNovoObj(true);
            throw e;
        }
    }

    @Override
    public HistoricoContatoVO executarInsercaoHistoricoPorPassivoOrClienteOrIndicado(String fase, String tipoContato, String agendaOuObjecao, Integer passivo, Integer indicado, Integer cliente, HistoricoContatoVO hist, UsuarioVO colaboradoreResponsavel, UsuarioVO responsavelCadastro, Integer empresa) throws Exception {
        hist.setResponsavelCadastro(responsavelCadastro);
        hist.setDia(negocio.comuns.utilitarias.Calendario.hoje());
        hist.setFase(fase);
        hist.setTipoContato(tipoContato);
        if (passivo.intValue() != 0) {
            hist.getPassivoVO().setCodigo(passivo);
            if (agendaOuObjecao.equals("AG")) {
                hist.setObjecaoVO(new ObjecaoVO());
                hist.setResultado(hist.getAgendaVO().qualResultadoAgendamento());
                getFacade().getAgenda().preencherDadosAgenda(hist.getAgendaVO(), hist, hist.getTipoOperacao(), hist.getAgendaVO().isNovoObj());
            } else {
                hist.setResultado(hist.getObjecaoVO().qualResultadoObjecao());
            }
        }
        incluirSemCommit(hist);
        return hist;
    }

    @Override
    public HistoricoContatoVO executarAlteracaoHistoricoPorPorPassivoOrClienteOrIndicado(String agendaOuObjecao, String fase, String tipoOperacao, Integer passivo, Integer indicado, Integer cliente, HistoricoContatoVO hist, UsuarioVO colaboradoreResponsavel, UsuarioVO responsavelCadastro, Integer empresa) throws Exception {
        if (passivo.intValue() != 0) {
            if (agendaOuObjecao.equals("AG")) {
                validarDadosHistorioAgendamentoFuturo(hist, hist.getAgendaVO(), empresa);
                hist.setObjecaoVO(new ObjecaoVO());
                hist.setResultado(hist.getAgendaVO().qualResultadoAgendamento());
                getFacade().getAgenda().preencherDadosAgenda(hist.getAgendaVO(), hist, hist.getTipoOperacao(), hist.getAgendaVO().isNovoObj());
                getFacade().getHistoricoContato().alterarSemCommit(hist);
            } else {
                hist.setResultado(hist.getObjecaoVO().qualResultadoObjecao());
                int codigoAgendamento = 0;
                if (hist.getAgendaVO().getCodigo().intValue() != 0) {
                    codigoAgendamento = hist.getAgendaVO().getCodigo().intValue();
                    hist.getAgendaVO().setCodigo(0);
                }
                getFacade().getHistoricoContato().alterarSemCommit(hist);
                if (codigoAgendamento != 0) {
                    getFacade().getAgenda().excluirSemCommit(codigoAgendamento);
                }
            }
        } else {
        }

        return hist;
    }

    /**
     * Operação responsável por excluir no BD um objeto da classe
     * <code>HistoricoContatoVO</code>. Sempre localiza o registro a ser
     * excluído através da chave primária da entidade. Primeiramente verifica a
     * conexão com o banco de dados e a permissão do usuário para realizar esta
     * operacão na entidade. Isto, através da operação
     * <code>excluir</code> da superclasse.
     *
     * @param obj Objeto da classe <code>HistoricoContatoVO</code> que será
     *            removido no banco de dados.
     * @throws Exception Caso haja problemas de conexão ou restrição de
     *                   acesso.
     */
    @Override
    public void excluir(HistoricoContatoVO obj) throws Exception {
        try {
            con.setAutoCommit(false);
            excluirCRM(getIdEntidade());
            String sql = "DELETE FROM HistoricoContato WHERE ((codigo = ?))";
            try (PreparedStatement sqlExcluir = con.prepareStatement(sql)) {
                sqlExcluir.setInt(1, obj.getCodigo().intValue());
                sqlExcluir.execute();
            }
            con.commit();
        } catch (Exception e) {
            con.rollback();
            con.setAutoCommit(true);
            throw e;
        } finally {
            con.setAutoCommit(true);
        }
    }

    /**
     * Método responsavel por deletar HistoriContato pelo código Passivo
     *
     * @param codPassivo
     * @throws Exception
     * <AUTHOR>
     */
    @Override
    public void excluirHistoricoContatoPorCodigoPassivo(Integer codPassivo) throws Exception {
        try {
            excluirCRM(getIdEntidade());
            String sql = "DELETE FROM HistoricoContato WHERE ((passivo = ?))";
            try (PreparedStatement sqlExcluir = con.prepareStatement(sql)) {
                sqlExcluir.setInt(1, codPassivo.intValue());
                sqlExcluir.execute();
            }
        } catch (Exception e) {
            throw e;
        }
    }



    /**
     * Método responsavel por deletar HistoriContato pelo código Indicado
     *
     * @param codIndicado
     * @throws Exception
     * <AUTHOR>
     */
    @Override
    public void excluirHistoricoContatoPorCodigoIndicado(Integer codIndicado) throws Exception {
        try {
            excluirCRM(getIdEntidade());
            String sql = "DELETE FROM HistoricoContato WHERE ((indicado = ?))";
            try (PreparedStatement sqlExcluir = con.prepareStatement(sql)) {
                sqlExcluir.setInt(1, codIndicado.intValue());
                sqlExcluir.execute();
            }
        } catch (Exception e) {
            throw e;
        }
    }

    @Override
    public HistoricoContatoVO consultarHistoricoContatoPorCodigoPassivo(Integer codPassivo, boolean controlarAcesso, int nivelMontarDados) throws Exception {
        consultarCRM(getIdEntidade(), controlarAcesso);
        String sqlStr = "SELECT * FROM HistoricoContato where passivo = " + codPassivo.intValue() + " ORDER BY codigo ";
        try (PreparedStatement sqlConsultar = con.prepareStatement(sqlStr)) {
            try (ResultSet tabelaResultado = sqlConsultar.executeQuery()) {
                if (!tabelaResultado.next()) {
                    return new HistoricoContatoVO();
                }
                return (montarDados(tabelaResultado, nivelMontarDados, con));
            }
        }
    }

    @Override
    public String consultarFasePassivo(int passivo, boolean faseAtual) throws SQLException {
        String sqlStr = "select fase from historicoContato as hc  where passivo = " + passivo + " order by codigo ";
        if (faseAtual) {
            sqlStr += " desc ";
        }
        sqlStr += "limit 1 ";
        try (PreparedStatement sqlConsultar = con.prepareStatement(sqlStr)) {
            try (ResultSet tabelaResultado = sqlConsultar.executeQuery()) {
                tabelaResultado.next();
                return tabelaResultado.getString("fase");
            }
        }
    }

    @Override
    public String consultarFaseIndicado(int indicado, boolean faseAtual) throws SQLException {
        String sqlStr = "select fase from historicoContato as hc  where indicado = " + indicado + " order by codigo ";
        if (faseAtual) {
            sqlStr += " desc ";
        }
        sqlStr += "limit 1 ";
        try (PreparedStatement sqlConsultar = con.prepareStatement(sqlStr)) {
            try (ResultSet tabelaResultado = sqlConsultar.executeQuery()) {
                tabelaResultado.next();
                return tabelaResultado.getString("fase");
            }
        }

    }

    @Override
    public String consultarFaseCliente(int cliente, boolean faseAtual) throws SQLException {
        String sqlStr = "select fase from historicoContato as hc  where fase is not null and fase <> '' and fase <> ' ' and cliente = " + cliente + " order by codigo ";
        if (faseAtual) {
            sqlStr += " desc ";
        }
        sqlStr += "limit 1 ";

        try (PreparedStatement sqlConsultar = con.prepareStatement(sqlStr)) {
            try (ResultSet tabelaResultado = sqlConsultar.executeQuery()) {
                if (!tabelaResultado.next()) {
                    return "";
                }
                return tabelaResultado.getString("fase");
            }
        }
    }

    @Override
    public HistoricoContatoVO consultarHistoricoContatoPorCodigoIndicado(Integer codIndicado, boolean controlarAcesso, int nivelMontarDados) throws Exception {
        consultarCRM(getIdEntidade(), controlarAcesso);
        String sqlStr = "SELECT * FROM HistoricoContato where indicado = " + codIndicado.intValue() + " ORDER BY codigo";
        try (PreparedStatement sqlConsultar = con.prepareStatement(sqlStr)) {
            try (ResultSet tabelaResultado = sqlConsultar.executeQuery()) {
                if (!tabelaResultado.next()) {
                    return new HistoricoContatoVO();
                }
                return (montarDados(tabelaResultado, nivelMontarDados, con));
            }
        }
    }

    @Override
    public List consultarHistoricoContatoPassivo(Integer codPassivo, boolean controlarAcesso, int nivelMontarDados) throws Exception {
        consultarCRM(getIdEntidade(), controlarAcesso);
        String sqlStr = "SELECT * FROM HistoricoContato where passivo = " + codPassivo.intValue() + " ORDER BY dia desc";
        try (Statement stm = con.createStatement()) {
            try (ResultSet tabelaResultado = stm.executeQuery(sqlStr)) {
                return (montarDadosConsulta(tabelaResultado, nivelMontarDados, con));
            }
        }
    }

    @Override
    public List consultarHistoricoContatoIndicado(Integer codIndicado, boolean controlarAcesso, int nivelMontarDados) throws Exception {
        consultarCRM(getIdEntidade(), controlarAcesso);
        String sqlStr = "SELECT * FROM HistoricoContato where indicado = " + codIndicado.intValue() + " ORDER BY dia desc";
        try (Statement stm = con.createStatement()) {
            try (ResultSet tabelaResultado = stm.executeQuery(sqlStr)) {
                return (montarDadosConsulta(tabelaResultado, nivelMontarDados, con));
            }
        }
    }

    @Override
    public List consultarHistoricoContatoCliente(Integer codCliente, boolean controlarAcesso, int nivelMontarDados) throws Exception {
        consultarCRM(getIdEntidade(), controlarAcesso);
        String sqlStr = "SELECT * FROM HistoricoContato where cliente = " + codCliente.intValue() + " ORDER BY dia desc";
        try (Statement stm = con.createStatement()) {
            try (ResultSet tabelaResultado = stm.executeQuery(sqlStr)) {
                return (montarDadosConsulta(tabelaResultado, nivelMontarDados, con));
            }
        }
    }

    @Override
    public Integer countHistoricoContatoClient(Integer codCliente, boolean controlarAcesso, int nivelMontarDados) throws Exception {
        consultarCRM(getIdEntidade(), controlarAcesso);
        String sqlStr = "SELECT COUNT(*) FROM HistoricoContato where cliente = " + codCliente.intValue();
        Integer totalLinhas;
        try (Statement stm = con.createStatement()) {
            totalLinhas = 0;
            try (ResultSet tabelaResultado = stm.executeQuery(sqlStr)) {
                if (tabelaResultado.next())
                    totalLinhas = tabelaResultado.getInt(1);
            }
        }
        return totalLinhas;
    }

    @Override
    public List<HistoricoContatoVO> consultarHistoricoContatoCliente(Integer codCliente, boolean controlarAcesso, int nivelMontarDados,Integer limit,Integer offset ) throws Exception {
        consultarCRM(getIdEntidade(), controlarAcesso);
        try (Statement stm = con.createStatement()) {
            String sqlStr = "SELECT * FROM HistoricoContato where cliente = " + codCliente.intValue() + " ORDER BY dia desc  Limit " + limit + " OFFSET " + offset;
            try (ResultSet tabelaResultado = stm.executeQuery(sqlStr)) {
                return (montarDadosConsulta(tabelaResultado, nivelMontarDados, con));
            }
        }
    }

    public String consultarHistoricoContatoClienteObjecao(boolean cliente, boolean passivo, boolean indicado, String tipoGrupoObjecao) throws Exception {
        try {
            StringBuilder sqlStr = new StringBuilder();
            if (cliente) {
                sqlStr.append("SELECT array_to_string(foo, ',') FROM (SELECT ARRAY(SELECT codigo :: TEXT FROM cliente where objecao is not null and objecao > 0) foo) AS a");
            } else if (passivo) {
                sqlStr.append("SELECT array_to_string(foo, ',') FROM (SELECT ARRAY(SELECT codigo :: TEXT FROM passivo where objecao is not null and objecao > 0) foo) AS a");
            } else {
                sqlStr.append("SELECT array_to_string(foo, ',') FROM (SELECT ARRAY(SELECT codigo :: TEXT FROM indicado where objecao is not null and objecao > 0) foo) AS a");
            }
            try (Statement stm = con.createStatement()) {
                try (ResultSet tabelaResultado = stm.executeQuery(sqlStr.toString())) {
                    if (tabelaResultado.next()) {
                        if ((tabelaResultado.getString(1)).equals("")) {
                            return "9999999";
                        }
                        return tabelaResultado.getString(1);
                    }
                }
            }
        } catch (Exception e) {
            throw e;
        }
        return "9999999";
    }

    public String consultarHistoricoContatoPessoaObjecao() throws Exception {
        try {
            String sql = "SELECT array_to_string(foo, ',') FROM (SELECT ARRAY(SELECT pessoa :: TEXT FROM cliente where objecao is not null and objecao > 0) foo) AS a";
            try (Statement stm = con.createStatement()) {
                try (ResultSet tabelaResultado = stm.executeQuery(sql)) {
                    if (tabelaResultado.next()) {
                        if ((tabelaResultado.getString(1)).equals("")) {
                            return "9999999";
                        }
                        return tabelaResultado.getString(1);
                    }
                }
            }
        } catch (Exception e) {
            throw e;
        }
        return "9999999";
    }

    /**
     * Método responsavel por consultar um HistoricoContato pelo codigo do
     * Indicado
     *
     * @param codIndicado
     * @param controlarAcesso
     * @param nivelMontarDados
     * @return
     * @throws Exception
     * <AUTHOR>
     */
    @Override
    public Boolean consultarHistoricoPorCodigoIndicado(Integer codIndicado, boolean controlarAcesso, int nivelMontarDados) throws Exception {
        consultarCRM(getIdEntidade(), controlarAcesso);
        String sqlStr = "SELECT * FROM HistoricoContato where indicado = " + codIndicado.intValue() + " ORDER BY codigo";
        try (Statement stm = con.createStatement()) {
            try (ResultSet tabelaResultado = stm.executeQuery(sqlStr)) {
                if (tabelaResultado.next()) {
                    return true;
                }
            }
        }
        return false;
    }

    /**
     * Operação responsável por localizar um objeto da classe
     * <code>HistoricoContatoVO</code> através de sua chave primária.
     *
     * @throws Exception Caso haja problemas de conexão ou localização do
     *                   objeto procurado.
     */
    @Override
    public HistoricoContatoVO consultarPorChavePrimaria(Integer codigoPrm, int nivelMontarDados) throws Exception {
        consultarCRM(getIdEntidade(), false);
        String sql = "SELECT * FROM HistoricoContato WHERE codigo = ?";
        try (PreparedStatement sqlConsultar = con.prepareStatement(sql)) {
            sqlConsultar.setInt(1, codigoPrm.intValue());
            try (ResultSet tabelaResultado = sqlConsultar.executeQuery()) {
                if (!tabelaResultado.next()) {
                    throw new ConsistirException("Dados Não Encontrados ( HistoricoContato ).");
                }
                return (montarDados(tabelaResultado, nivelMontarDados, con));
            }
        }
    }

    /**
     * Responsável por montar os dados de vários objetos, resultantes de uma
     * consulta ao banco de dados (
     * <code>ResultSet</code>). Faz uso da operação
     * <code>montarDados</code> que realiza o trabalho para um objeto por vez.
     *
     * @return List Contendo vários objetos da classe
     * <code>HistoricoContatoVO</code> resultantes da consulta.
     */
    public static List montarDadosConsulta(ResultSet tabelaResultado, int nivelMontarDados, Connection con) throws Exception {
        List vetResultado = new ArrayList();
        while (tabelaResultado.next()) {
            HistoricoContatoVO obj = new HistoricoContatoVO();
            obj = montarDados(tabelaResultado, nivelMontarDados, con);
            vetResultado.add(obj);
        }

        tabelaResultado = null;
        return vetResultado;
    }

    /**
     * Responsável por realizar uma consulta de
     * <code>HistoricoContato</code> através do valor do atributo
     * <code>Integer codigo</code>. Retorna os objetos com valores iguais ou
     * superiores ao parâmetro fornecido. Faz uso da operação
     * <code>montarDadosConsulta</code> que realiza o trabalho de prerarar o
     * List resultante.
     *
     * @param controlarAcesso Indica se a aplicação deverá verificar se o
     *                        usuário possui permissão para esta consulta ou não.
     * @return List Contendo vários objetos da classe
     * <code>HistoricoContatoVO</code> resultantes da consulta.
     * @throws Exception Caso haja problemas de conexão ou restrição de
     *                   acesso.
     */
    @Override
    public List consultarPorCodigo(Integer valorConsulta, boolean controlarAcesso, int nivelMontarDados) throws Exception {
        consultarCRM(getIdEntidade(), controlarAcesso);
        String sqlStr = "SELECT * FROM HistoricoContato WHERE codigo >= " + valorConsulta.intValue() + " ORDER BY codigo";
        try (Statement stm = con.createStatement()) {
            try (ResultSet tabelaResultado = stm.executeQuery(sqlStr)) {
                return (montarDadosConsulta(tabelaResultado, nivelMontarDados, con));
            }
        }
    }

    /**
     * Responsável por montar os dados resultantes de uma consulta ao banco de
     * dados (
     * <code>ResultSet</code>) em um objeto da classe
     * <code>HistoricoContatoVO</code>.
     *
     * @return O objeto da classe <code>HistoricoContatoVO</code> com os dados
     * devidamente montados.
     */
    public static HistoricoContatoVO montarDados(ResultSet dadosSQL, int nivelMontarDados, Connection con) throws Exception {
        HistoricoContatoVO obj = new HistoricoContatoVO();
        if (nivelMontarDados == Uteis.NIVELMONTARDADOS_TELACONSULTA) {
            obj.setCodigo(new Integer(dadosSQL.getInt("codigo")));
            obj.setDia(dadosSQL.getTimestamp("dia"));
            obj.getClienteVO().setCodigo(new Integer(dadosSQL.getInt("cliente")));
            obj.getPassivoVO().setCodigo(new Integer(dadosSQL.getInt("passivo")));
            obj.getIndicadoVO().setCodigo(new Integer(dadosSQL.getInt("indicado")));
            obj.setTipoOperacao(dadosSQL.getString("tipoOperacao"));
            obj.setTipoContato(dadosSQL.getString("tipoContato"));
            obj.getResponsavelCadastro().setCodigo(new Integer(dadosSQL.getInt("responsavelCadastro")));
            obj.setFase(dadosSQL.getString("fase"));
            obj.setFaseAtual(dadosSQL.getString("faseAtual"));
            obj.setFaseInicio(dadosSQL.getString("faseInicio"));
            obj.setResultado(dadosSQL.getString("resultado"));
            obj.setGrauSatisfacao(dadosSQL.getString("grausatisfacao"));
            obj.setContatoAvulso(dadosSQL.getBoolean("contatoavulso"));
            try {
                obj.setConviteAulaExperimentalVO(new ConviteAulaExperimentalVO());
                obj.getConviteAulaExperimentalVO().setCodigo(dadosSQL.getInt("conviteAulaExperimental"));
            }catch (Exception ignored){}
            try {
                obj.setResposta(dadosSQL.getString("resposta"));
                obj.setOpcoes(dadosSQL.getString("opcoes"));
                obj.setCodigoNotificacao(dadosSQL.getInt("codigoNotificacao"));
            } catch (Exception e) {
            }
            obj.setNovoObj(false);
            montarDadosResponsavelCadastro(obj, nivelMontarDados, con);
            montarDadosPassivo(obj, nivelMontarDados, con);
            montarDadosIndicado(obj, nivelMontarDados, con);
            montarDadosCliente(obj, nivelMontarDados, con);
            return obj;
        }
        if (nivelMontarDados == Uteis.NIVELMONTARDADOS_DADOSENTIDADESPRINCIPAIS) {
            obj.setCodigo(new Integer(dadosSQL.getInt("codigo")));
            obj.setDia(dadosSQL.getTimestamp("dia"));
            obj.setClienteVO(new ClienteVO());
            obj.getClienteVO().setCodigo(new Integer(dadosSQL.getInt("codigoCliente")));
            obj.getClienteVO().setMatricula(dadosSQL.getString("matricula"));
            obj.getClienteVO().setPessoa(new PessoaVO());
            obj.getClienteVO().getPessoa().setCodigo(new Integer(dadosSQL.getInt("codigoPessoa")));
            obj.getClienteVO().getPessoa().setNome(dadosSQL.getString("nomePessoa"));
            obj.getPassivoVO().setCodigo(new Integer(dadosSQL.getInt("codigoPassivo")));
            obj.getPassivoVO().setNome(dadosSQL.getString("nomePassivo"));
            obj.getIndicadoVO().setCodigo(new Integer(dadosSQL.getInt("codigoIndicado")));
            obj.getIndicadoVO().setNomeIndicado(dadosSQL.getString("nomeIndicado"));
            obj.setTipoContato(dadosSQL.getString("tipoContato"));
            obj.getResponsavelCadastro().setCodigo(new Integer(dadosSQL.getInt("responsavelCadastro")));
            obj.getResponsavelCadastro().setNome(dadosSQL.getString("nomeResponsavelCadastro"));
            obj.setFase(dadosSQL.getString("fase"));
            obj.setContatoAvulso(dadosSQL.getBoolean("contatoavulso"));
            obj.setObservacao(dadosSQL.getString("observacao"));
            try {
                obj.setConviteAulaExperimentalVO(new ConviteAulaExperimentalVO());
                obj.getConviteAulaExperimentalVO().setCodigo(dadosSQL.getInt("conviteAulaExperimental"));
            }catch (Exception ignored){}
            try {
                obj.getClienteVO().getEmpresa().setCodigo(dadosSQL.getInt("empresa"));
                obj.getClienteVO().getEmpresa().setNome(dadosSQL.getString("empresanome"));
            }catch (Exception ignored){}
            try {
                obj.setResposta(dadosSQL.getString("resposta"));
                obj.setOpcoes(dadosSQL.getString("opcoes"));
                obj.setCodigoNotificacao(dadosSQL.getInt("codigoNotificacao"));
            } catch (Exception e) {
            }

            HistoricoContato historicoContatoDAO = new HistoricoContato(con);
            if (obj.getClienteVO().getCodigo() != 0) {
                obj.setFaseInicio(historicoContatoDAO.consultarFaseCliente(obj.getClienteVO().getCodigo(), false));
                obj.setFaseAtual(historicoContatoDAO.consultarFaseCliente(obj.getClienteVO().getCodigo(), true));
            } else if (obj.getPassivoVO().getCodigo() != 0) {
                obj.setFaseInicio(historicoContatoDAO.consultarFasePassivo(obj.getPassivoVO().getCodigo(), false));
                obj.setFaseAtual(historicoContatoDAO.consultarFasePassivo(obj.getPassivoVO().getCodigo(), true));
            } else if (obj.getIndicadoVO().getCodigo() != 0) {
                obj.setFaseInicio(historicoContatoDAO.consultarFaseIndicado(obj.getIndicadoVO().getCodigo(), false));
                obj.setFaseAtual(historicoContatoDAO.consultarFaseIndicado(obj.getIndicadoVO().getCodigo(), true));
            }
            obj.setResultado(dadosSQL.getString("resultado"));
            obj.setNovoObj(false);
            return obj;
        }

        obj.setCodigo(new Integer(dadosSQL.getInt("codigo")));
        obj.setDia(dadosSQL.getTimestamp("dia"));
        obj.getClienteVO().setCodigo(new Integer(dadosSQL.getInt("cliente")));
        obj.getPassivoVO().setCodigo(new Integer(dadosSQL.getInt("passivo")));
        obj.getIndicadoVO().setCodigo(new Integer(dadosSQL.getInt("indicado")));
        obj.setObservacao(dadosSQL.getString("observacao"));
        obj.getMalaDiretaVO().setCodigo(new Integer(dadosSQL.getInt("maladireta")));
        obj.setTipoOperacao(dadosSQL.getString("tipoOperacao"));
        obj.getResponsavelCadastro().setCodigo(new Integer(dadosSQL.getInt("responsavelCadastro")));
        obj.getAgendaVO().setCodigo(new Integer(dadosSQL.getInt("agenda")));
        obj.getObjecaoVO().setCodigo(new Integer(dadosSQL.getInt("objecao")));
        obj.setFase(dadosSQL.getString("fase"));
        if(UteisValidacao.emptyNumber(obj.getObjecaoVO().getCodigo())){
            obj.setResultado(dadosSQL.getString("resultado"));
        }else{
            montarDadosObjecao(obj, nivelMontarDados, con);
            obj.setResultado("Obje\u00E7\u00E3o: "+obj.getObjecaoVO().getDescricao() + " | C\u00F3d.: "+obj.getObjecaoVO().getCodigo());
        }
        obj.setTipoContato(dadosSQL.getString("tipoContato"));
        obj.setGrauSatisfacao(dadosSQL.getString("grausatisfacao"));
        obj.setContatoAvulso(dadosSQL.getBoolean("contatoavulso"));
        try {
            obj.setConviteAulaExperimentalVO(new ConviteAulaExperimentalVO());
            obj.getConviteAulaExperimentalVO().setCodigo(dadosSQL.getInt("conviteAulaExperimental"));
        }catch (Exception ignored){}
        try {
            obj.setResposta(dadosSQL.getString("resposta"));
            obj.setOpcoes(dadosSQL.getString("opcoes"));
            obj.setCodigoNotificacao(dadosSQL.getInt("codigoNotificacao"));
        } catch (Exception e) {
        }
        obj.setNovoObj(false);

        if (nivelMontarDados == Uteis.NIVELMONTARDADOS_METAPASSIVODETALHADA) {
            return obj;
        }

        montarDadosResponsavelCadastro(obj, nivelMontarDados, con);

        if (nivelMontarDados == Uteis.NIVELMONTARDADOS_DADOSBASICOS) {
            return obj;
        }
        if(!UteisValidacao.emptyNumber(obj.getMalaDiretaVO().getCodigo())){
            montarImagemMalaDireta(obj, nivelMontarDados, con);
        }
        if (nivelMontarDados == Uteis.NIVELMONTARDADOS_HISTORICOPASSIVO) {
            montarDadosPassivo(obj, nivelMontarDados, con);
            if (obj.getAgendaVO().getCodigo() != 0) {
                montarDadosAgenda(obj, Uteis.NIVELMONTARDADOS_DADOSBASICOS, con);
            }
            return obj;
        }

        if (nivelMontarDados == Uteis.NIVELMONTARDADOS_HISTORICOINDICADO) {
            montarDadosIndicado(obj, nivelMontarDados, con);
            return obj;
        }

        if (nivelMontarDados == Uteis.NIVELMONTARDADOS_HISTORICOCLIENTE) {
            montarDadosCliente(obj, nivelMontarDados, con);

            return obj;
        }

        if (nivelMontarDados == Uteis.NIVELMONTARDADOS_INDICERENOVACAO) {
            montarDadosAgenda(obj, nivelMontarDados, con);
            return obj;
        }

        montarDadosPassivo(obj, nivelMontarDados, con);
        montarDadosIndicado(obj, nivelMontarDados, con);
        montarDadosCliente(obj, nivelMontarDados, con);
        montarDadosAgenda(obj, nivelMontarDados, con);

        return obj;
    }

    @Override
    public Date obterDataUltimoContato(FecharMetaDetalhadoVO fecharDetalhado, boolean controlarAcesso) throws Exception {
        final StringBuilder sqlStr =
                new StringBuilder("SELECT dia FROM HistoricoContato WHERE ");
        if (fecharDetalhado.getPassivo().getCodigo() != 0) {
            sqlStr.append("passivo = ").append(fecharDetalhado.getPassivo().getCodigo());
        } else if (fecharDetalhado.getIndicado().getCodigo() != 0) {
            sqlStr.append("indicado = ").append(fecharDetalhado.getIndicado().getCodigo());
        } else {
            sqlStr.append("cliente = ").append(fecharDetalhado.getCliente().getCodigo());
        }
        sqlStr.append(" ORDER BY codigo DESC LIMIT 1");

        try (Statement stm = con.createStatement()) {
            try (ResultSet tabelaResultado = stm.executeQuery(sqlStr.toString())) {
                if (!tabelaResultado.next()) {
                    return null;
                }

                return tabelaResultado.getTimestamp("dia");
            }
        }
    }

    public static void montarDadosResponsavelCadastro(HistoricoContatoVO obj, int nivelMontarDados, Connection con) throws Exception {
        if (obj.getResponsavelCadastro().getCodigo().intValue() == 0) {
            obj.setResponsavelCadastro(new UsuarioVO());
            return;
        }

        obj.setResponsavelCadastro(new Usuario(con).consultarPorChavePrimaria(obj.getResponsavelCadastro().getCodigo(), nivelMontarDados));
    }

    public static void montarDadosPassivo(HistoricoContatoVO obj, int nivelMontarDados, Connection con) throws Exception {
        if (obj.getPassivoVO().getCodigo().intValue() == 0) {
            obj.setPassivoVO(new PassivoVO());
            return;
        }
        obj.setPassivoVO(new Passivo(con).consultarPorChavePrimaria(obj.getPassivoVO().getCodigo(), nivelMontarDados));
    }

    public static void montarDadosIndicado(HistoricoContatoVO obj, int nivelMontarDados, Connection con) throws Exception {
        if (obj.getIndicadoVO().getCodigo().intValue() == 0) {
            obj.setIndicadoVO(new IndicadoVO());
            return;
        }
        obj.setIndicadoVO(new Indicado(con).consultarPorChavePrimaria(obj.getIndicadoVO().getCodigo(), nivelMontarDados));
    }

    public static void montarDadosCliente(HistoricoContatoVO obj, int nivelMontarDados, Connection con) throws Exception {
        if (obj.getClienteVO().getCodigo().intValue() == 0) {
            obj.setClienteVO(new ClienteVO());
            return;
        }
        obj.setClienteVO(new Cliente(con).consultarPorChavePrimaria(obj.getClienteVO().getCodigo(), nivelMontarDados));
    }

    public static void montarDadosAgenda(HistoricoContatoVO obj, int nivelMontarDados, Connection con) throws Exception {
        if (obj.getAgendaVO().getCodigo().intValue() == 0) {
            obj.setAgendaVO(new AgendaVO());
            return;
        }
        obj.setAgendaVO(new Agenda(con).consultarPorChavePrimaria(obj.getAgendaVO().getCodigo(), nivelMontarDados));
    }

    public HistoricoContatoVO consultarHistoricoContatoPorCodigoCliente(Integer codigoCliente, int nivelMontarDados) throws Exception {
        consultarCRM(getIdEntidade(), false);
        String sql = "SELECT * FROM HistoricoContato WHERE cliente = " + codigoCliente.intValue() + "";
        try (PreparedStatement sqlConsultar = con.prepareStatement(sql)) {
            try (ResultSet tabelaResultado = sqlConsultar.executeQuery()) {
                if (!tabelaResultado.next()) {
                    return new HistoricoContatoVO();
                }

                return (montarDados(tabelaResultado, nivelMontarDados, con));
            }
        }
    }

    /**
     * Método Responsável por preencher os dados do Historico Contato com os
     * dados do Passivo, ele faz uma consulta que retorna o Total de Ligação e o
     * ultimo dia de acesso do passivo. Tambem calcula o numero de dias entre as
     * datas e calcula a idade da pessoa.
     *
     * @throws Exception
     */
    @Override
    public void preencherHistoricoContatoComDadosPassivo(HistoricoContatoVO historicoContatoVO) throws Exception {
        consultarTotalLigacaoPassivo(historicoContatoVO, historicoContatoVO.getPassivoVO().getCodigo().intValue(), Uteis.NIVELMONTARDADOS_TELACONSULTA);
        historicoContatoVO.setQtdEmail(consultarQuantEnviadoPassivo(historicoContatoVO, historicoContatoVO.getPassivoVO().getCodigo().intValue(), Uteis.NIVELMONTARDADOS_TELACONSULTA, "EM"));
        historicoContatoVO.setQtdSMS(consultarQuantEnviadoPassivo(historicoContatoVO, historicoContatoVO.getPassivoVO().getCodigo().intValue(), Uteis.NIVELMONTARDADOS_TELACONSULTA, "CS"));
        consultarQuantPessoalAgendadoPassivo(historicoContatoVO, historicoContatoVO.getPassivoVO().getCodigo(), Uteis.NIVELMONTARDADOS_TELACONSULTA);
        historicoContatoVO.setDiasCadastrado(Uteis.nrDiasEntreDatas(historicoContatoVO.getPassivoVO().getDia(), negocio.comuns.utilitarias.Calendario.hoje()));
    }

    /**
     * Método Responsável por preencher os dados do Historico Contato com os
     * dados do Indicado, ele faz uma consulta que retorna o Total de Ligação e
     * o ultimo dia de acesso do indicado. Tambem calcula o numero de dias entre
     * as datas e calcula a idade da pessoa.
     *
     * @throws Exception
     */
    @Override
    public void preencherHistoricoContatoComDadosIndicado(HistoricoContatoVO historicoContatoVO) throws Exception {
        consultarTotalLigacaoIndicado(historicoContatoVO, historicoContatoVO.getIndicadoVO().getCodigo().intValue(), Uteis.NIVELMONTARDADOS_TELACONSULTA);
        consultarQuantEmailEnviadoIndicado(historicoContatoVO, historicoContatoVO.getIndicadoVO().getCodigo().intValue(), Uteis.NIVELMONTARDADOS_TELACONSULTA);
        consultarQuantPessoalAgendadoIndicado(historicoContatoVO, historicoContatoVO.getIndicadoVO().getCodigo(), Uteis.NIVELMONTARDADOS_TELACONSULTA);
        historicoContatoVO.setDiasCadastrado(Uteis.nrDiasEntreDatas(historicoContatoVO.getIndicadoVO().getIndicacaoVO().getDia(), negocio.comuns.utilitarias.Calendario.hoje()));
    }

    /**
     * Método Responsável por preencher os dados do Historico Contato com os
     * dados do Cliente, ele faz uma consulta que retorna o Total de Ligação e o
     * ultimo dia de acesso do cliente. Tambem calcula o numero de dias entre as
     * datas e calcula a idade da pessoa.
     *
     * @throws Exception
     */
    @Override
    public void preencherHistoricoContatoComDadosCliente(HistoricoContatoVO historicoContatoVO) throws Exception {
        consultarTotalLigacaoEDiasUltAcesso(historicoContatoVO);
        historicoContatoVO.setQtdEmail(consultarQuantEnviado(historicoContatoVO, "EM"));
        historicoContatoVO.setQtdSMS(consultarQuantEnviado(historicoContatoVO, "CS"));
        consultarQuantPessoalAgendado(historicoContatoVO.getClienteVO().getCodigo(), historicoContatoVO, Uteis.NIVELMONTARDADOS_TELACONSULTA);
        historicoContatoVO.setDiasCadastrado(Uteis.nrDiasEntreDatas(historicoContatoVO.getClienteVO().getPessoa().getDataCadastro(), negocio.comuns.utilitarias.Calendario.hoje()));
        historicoContatoVO.setIdade(Uteis.calcularIdadePessoa(negocio.comuns.utilitarias.Calendario.hoje(), historicoContatoVO.getClienteVO().getPessoa().getDataNasc()));
        historicoContatoVO.setVencimentoContrato(obterVencimentoUltimoContrato(historicoContatoVO.getClienteVO().getCodigo()));
        historicoContatoVO.setQtdAPP(consultarQuantEnviado(historicoContatoVO, "AP").intValue());
    }

    /**
     * Método responsavel por inicializar os dados do cliente e fazendo as
     * validações para fase Atual do cliente.
     *
     * @param historicoContatoVO
     * @throws Exception
     */
    @Override
    public void inicializarDadosHistoricoContatoCliente(HistoricoContatoVO historicoContatoVO) throws Exception {
        preencherHistoricoContatoComDadosCliente(historicoContatoVO);
        validarSituacaoClienteFaseAtual(!historicoContatoVO.getClienteVO().getClienteSituacaoVOs().isEmpty()
                ? historicoContatoVO.getClienteVO().getClienteSituacaoVOs().get(0) : new ClienteSituacaoVO(), historicoContatoVO);
    }

    /**
     * Método responsavel por validar a Situação do cliente para setar na fase
     * do historicoContatoVO a fase respectiva, de acordo com regras passadas
     * pelo Max.
     *
     * @param obj
     * @param historicoContatoVO
     */
    public void validarSituacaoClienteFaseAtual(ClienteSituacaoVO obj, HistoricoContatoVO historicoContatoVO) {
        if (obj.getSituacao().equals("VI")) {
            historicoContatoVO.setFase("HO");
        } else if ((obj.getAtivoNormal()) || obj.getTrancado() || obj.getAtivoCarencia() || obj.getAtivoAtestado()) {
            historicoContatoVO.setFase("PV");
        } else if (obj.getAtivoAvencer() || obj.getInativoVencido()) {
            historicoContatoVO.setFase("RE");
        } else if (obj.getInativoDesistente() || obj.getInativo() || obj.getInativoCancelamento()) {
            historicoContatoVO.setFase("PE");
        }
    }

    @Override
    public HistoricoContatoVO consultarUltimoHistoricoPorCliente(Integer codigoCliente, int nivelMontarDados) throws Exception {
        consultarCRM(getIdEntidade(), false);
        String sql = "SELECT * FROM HistoricoContato WHERE cliente = " + codigoCliente + " order by codigo desc limit 1";
        try (PreparedStatement sqlConsultar = con.prepareStatement(sql)) {
            try (ResultSet tabelaResultado = sqlConsultar.executeQuery()) {
                if (!tabelaResultado.next()) {
                    return new HistoricoContatoVO();
                }
                return (montarDados(tabelaResultado, nivelMontarDados, con));
            }
        }
    }

    public HistoricoContatoVO consultarUltimoHistoricoPorClienteComDiaFase(Integer codigoCliente,  Date dia, String fase, int nivelMontarDados) throws Exception {
        consultarCRM(getIdEntidade(), false);
        String sql = "SELECT * FROM HistoricoContato hc " +
                "INNER JOIN agenda ag on hc.agenda = ag.codigo " +
                "WHERE hc.cliente = " + codigoCliente +
                " and dia :: DATE >=  '" + Uteis.getDataHoraJDBC(dia, "23:59:59") +
                "' and fase = '" + fase + "' order by hc.codigo ASC" ;
        try (PreparedStatement sqlConsultar = con.prepareStatement(sql)) {
            try (ResultSet tabelaResultado = sqlConsultar.executeQuery()) {
                if (!tabelaResultado.next()) {
                    return new HistoricoContatoVO();
                }
                return (montarDados(tabelaResultado, nivelMontarDados, con));
            }
        }
    }

    public HistoricoContatoVO consultarPrimeiroAgendamentoAposDataPorCliente(Integer codCliente, Date dia, int nivelMontarDados) throws Exception {
        String sql = "SELECT\n"
                + "  *\n"
                + "FROM HistoricoContato hc\n"
                + "  INNER JOIN agenda ag\n"
                + "    ON hc.agenda = ag.codigo\n"
                + "WHERE hc.cliente = " + codCliente + "\n"
                + "      AND dataagendamento :: DATE >= '" + Uteis.getDataJDBC(dia) + "'\n"
                + "ORDER BY hc.codigo ASC;";
        try (PreparedStatement sqlConsultar = con.prepareStatement(sql)) {
            try (ResultSet tabelaResultado = sqlConsultar.executeQuery()) {
                if (!tabelaResultado.next()) {
                    return new HistoricoContatoVO();
                }
                return (montarDados(tabelaResultado, nivelMontarDados, con));
            }
        }
    }

    public HistoricoContatoVO consultarHistoricoContatoDataPorCliente(Integer codCliente, Date dia, int nivelMontarDados) throws Exception {
        String sql = "SELECT\n"
                + "  *\n"
                + "FROM HistoricoContato hc\n"
                + "WHERE hc.cliente = " + codCliente + "\n"
                + "      AND hc.dia :: DATE >= '" + Uteis.getDataJDBC(dia) + "'\n"
                + "ORDER BY hc.codigo ASC;";
        try (PreparedStatement sqlConsultar = con.prepareStatement(sql)) {
            try (ResultSet tabelaResultado = sqlConsultar.executeQuery()) {
                if (!tabelaResultado.next()) {
                    return new HistoricoContatoVO();
                }
                return (montarDados(tabelaResultado, nivelMontarDados, con));
            }
        }
    }

    public void consultarTotalLigacaoPassivo(HistoricoContatoVO obj, Integer codPassivo, int nivelMontarDados) throws Exception {
        String sqlStr = "select count (historicoContato.codigo) as ligacao from historicoContato where historicoContato.passivo = " + codPassivo.intValue() + " and tipoContato = 'TE'";
        try (Statement stm = con.createStatement()) {
            try (ResultSet tabelaResultado = stm.executeQuery(sqlStr)) {
                if (tabelaResultado.next()) {
                    obj.setTotalLigacao(new Long(tabelaResultado.getLong("ligacao")));
                }
            }
        }

    }

    /**
     * **************************** METODOS QUE PREENCHEM O HISTORICO CONTATO
     * *****************************
     */
    @Override
    public void consultarTotalLigacaoEDiasUltAcesso(HistoricoContatoVO obj) throws Exception {
        String sqlStr = "select count (historicoContato.codigo) as ligacao ,(select max (acessoCliente.dthrentrada)  FROM acessoCliente WHERE acessoCliente.cliente = " + obj.getClienteVO().getCodigo() + ") as diasUltAcesso FROM historicoContato " + "WHERE historicoContato.cliente = " + obj.getClienteVO().getCodigo() + " and tipoContato = 'TE' ";

        try (Statement stm = con.createStatement()) {
            try (ResultSet tabelaResultado = stm.executeQuery(sqlStr)) {
                if (tabelaResultado.next()) {
                    obj.setTotalLigacao(tabelaResultado.getLong("ligacao"));
                    obj.setDiasUltAcesso(tabelaResultado.getTimestamp("diasUltAcesso"));
                }
            }
        }
    }

    // *************************Preencher o cabe?alho do historico Contato com
    // dados Indicado *********************
    public void consultarTotalLigacaoIndicado(HistoricoContatoVO obj, Integer codIndicado, int nivelMontarDados) throws Exception {
        String sqlStr = "select count (historicoContato.codigo) as ligacao from historicoContato where historicoContato.indicado = " + codIndicado.intValue() + " and tipoContato = 'TE'";
        try (Statement stm = con.createStatement()) {
            try (ResultSet tabelaResultado = stm.executeQuery(sqlStr)) {
                if (tabelaResultado.next()) {
                    obj.setTotalLigacao(new Long(tabelaResultado.getLong("ligacao")));
                }
            }
        }

    }

    public void consultarQuantEmailEnviadoIndicado(HistoricoContatoVO obj, Integer codIndicado, int nivelMontarDados) throws Exception {
        String sqlStr = "select count (historicoContato.codigo) as qtdEmail from historicoContato where historicoContato.indicado = " + codIndicado.intValue() + " and tipoContato = 'EM'";
        try (Statement stm = con.createStatement()) {
            try (ResultSet tabelaResultado = stm.executeQuery(sqlStr)) {
                if (tabelaResultado.next()) {
                    obj.setQtdEmail(new Long(tabelaResultado.getLong("qtdEmail")));
                }
            }
        }

    }

    public void consultarQuantPessoalAgendadoIndicado(HistoricoContatoVO obj, Integer codIndicado, int nivelMontarDados) throws Exception {
        String sqlStr = "select count (historicoContato.codigo) as qtdPessoal from historicoContato where historicoContato.indicado = " + codIndicado.intValue() + " and tipoContato = 'PE'";
        try (Statement stm = con.createStatement()) {
            try (ResultSet tabelaResultado = stm.executeQuery(sqlStr)) {
                if (tabelaResultado.next()) {
                    obj.setQtdPessoal(new Long(tabelaResultado.getLong("qtdPessoal")));
                }
            }
        }

    }

    public Long consultarQuantEnviado(HistoricoContatoVO obj, String tipo) throws Exception {
        String sqlStr = "select count (historicoContato.codigo) as qtd FROM historicoContato " + "WHERE historicoContato.cliente = " + obj.getClienteVO().getCodigo() + "	 and tipoContato = '" + tipo + "' ";
        Long retorno;
        try (Statement stm = con.createStatement()) {
            try (ResultSet tabelaResultado = stm.executeQuery(sqlStr)) {
                retorno = null;
                if (tabelaResultado.next()) {
                    retorno = new Long(tabelaResultado.getLong("qtd"));
                }
            }
        }
        return retorno;
    }

    public void consultarQuantPessoalAgendado(Integer valorConsulta, HistoricoContatoVO obj, int nivelMontarDados) throws Exception {
        String sqlStr = "select count (historicoContato.codigo) as qtdPessoal FROM historicoContato " + "WHERE historicoContato.cliente = " + obj.getClienteVO().getCodigo().intValue() + " and tipoContato = 'PE'  ";
        try (Statement stm = con.createStatement()) {
            try (ResultSet tabelaResultado = stm.executeQuery(sqlStr)) {
                if (tabelaResultado.next()) {
                    obj.setQtdPessoal(new Long(tabelaResultado.getLong("qtdPessoal")));
                }
            }
        }
    }

    public Long consultarQuantEnviadoPassivo(HistoricoContatoVO obj, Integer codPassivo, int nivelMontarDados, String tipo) throws Exception {
        String sqlStr = "select count (historicoContato.codigo) as qtd from historicoContato where historicoContato.passivo = " + codPassivo.intValue() + " and tipoContato = '" + tipo + "'";
        Long retorno;
        try (Statement stm = con.createStatement()) {
            try (ResultSet tabelaResultado = stm.executeQuery(sqlStr)) {
                retorno = null;
                if (tabelaResultado.next()) {
                    retorno = new Long(tabelaResultado.getLong("qtd"));
                }
            }
        }
        return retorno;

    }

    public void consultarQuantPessoalAgendadoPassivo(HistoricoContatoVO obj, Integer codPassivo, int nivelMontarDados) throws Exception {
        String sqlStr = "select count (historicoContato.codigo) as qtdPessoal from historicoContato where historicoContato.passivo = " + codPassivo.intValue() + " and tipoContato = 'PE'";
        try (Statement stm = con.createStatement()) {
            try (ResultSet tabelaResultado = stm.executeQuery(sqlStr)) {
                if (tabelaResultado.next()) {
                    obj.setQtdPessoal(new Long(tabelaResultado.getLong("qtdPessoal")));
                }
            }
        }

    }

    // REGRAS DE NEGÓCIO
    @Override
    public List consultarPorFiltrosTelaConsulta(UsuarioVO colaboradorConsultar, Date dataInicio, Date dataTermino, List<String> fases, Boolean filtroOperacaoReagendamento, Boolean filtroOperacaoConfirmacao, Boolean filtroOperacaoComparecimento, Boolean filtroOperacaoPessoal, Boolean filtroOperacaoTelefone, Boolean filtroOperacaoLigacaoSemContato, Boolean filtroOperacaoEmail, Boolean filtroOperacaoSMS, boolean controlarAcesso, int nivelMontarDados, EmpresaVO empresa) throws Exception {
        consultarCRM(getIdEntidade(), controlarAcesso);
        StringBuilder sqlStr = new StringBuilder();
        StringBuilder filtroOperacao = new StringBuilder();

        sqlStr.append(" select historico.codigo, historico.dia , historico.cliente, historico.passivo, "
                + "historico.indicado, historico.tipoOperacao, historico.tipoContato, "
                + "historico.grausatisfacao, historico.resultado, historico.responsavelcadastro, ");
        sqlStr.append(" (Case when historico.cliente <> 0 then  (select fase from historicoContato as hc "
                + " where hc.cliente = historico.cliente order by codigo limit 1 ) ");
        sqlStr.append(" else ");
        sqlStr.append(" Case when historico.passivo <> 0 then  (select fase from historicoContato as hc  "
                + "where hc.passivo = historico.passivo order by codigo limit 1 ) ");
        sqlStr.append(" else ");
        sqlStr.append(" (select fase from historicoContato as hc  where hc.indicado = historico.indicado "
                + "order by codigo limit 1) ");
        sqlStr.append(" end ");
        sqlStr.append(" end  ) as faseInicio, ");
        sqlStr.append(" historico.fase ,  historico.contatoavulso, ");
        sqlStr.append(" (Case when historico.cliente <> 0 then  (select fase from historicoContato as "
                + "hc  where hc.cliente = historico.cliente order by codigo desc limit 1 ) ");
        sqlStr.append(" else ");
        sqlStr.append(" Case when historico.passivo <> 0 then  (select fase from historicoContato as hc  "
                + "where hc.passivo = historico.passivo order by codigo desc limit 1 ) ");
        sqlStr.append(" else ");
        sqlStr.append(" (select fase from historicoContato as hc  where hc.indicado = historico.indicado order by codigo desc limit 1) ");
        sqlStr.append(" end ");
        sqlStr.append(" end  ) as faseAtual ");
        sqlStr.append(" from historicoContato as historico");
        sqlStr.append(" LEFT JOIN cliente ON cliente.codigo = historico.cliente \n");
        sqlStr.append(" LEFT JOIN passivo ON passivo.codigo = historico.passivo \n");
        sqlStr.append(" LEFT JOIN indicado ON indicado.codigo = historico.indicado \n");
        sqlStr.append("  WHERE historico.dia >= '").append(Uteis.getDataJDBC(dataInicio)).append(" 00:00:00 ' and historico.dia <='").append(Uteis.getDataJDBC(dataTermino)).append(" 23:59:59' and ");

        if (!UteisValidacao.emptyNumber(empresa.getCodigo())) {
            sqlStr.append("   (cliente.empresa = ").append(empresa.getCodigo()).append(" OR passivo.empresa = ").append(empresa.getCodigo()).append(" OR indicado.empresa = ").append(empresa.getCodigo()).append(") AND ");
        }
        if (colaboradorConsultar.getCodigo().intValue() != 0) {
            sqlStr.append(" historico.responsavelcadastro =  ").append(colaboradorConsultar.getCodigo().intValue()).append(" and ");
        }
        if(!UteisValidacao.emptyList(fases)){
            String fasesConsulta = "";
            for(String fase : fases){
                fasesConsulta += ",'"+fase+"'";
            }
            sqlStr.append(" historico.fase in (").append(fasesConsulta.replaceFirst(",", "")).append(") and ");
        }
        // filtros para tipoOperacao
//		if (filtroOperacaoComparecimento) {
//			filtroOperacao.append(" historico.tipoContato = 'CP' or ");
//		}
//		if (filtroOperacaoConfirmacao) {
//			filtroOperacao.append(" tipoOperacao = 'CO' or ");
//		}
        if (filtroOperacaoEmail) {
            filtroOperacao.append(" historico.tipoContato = 'EM' or ");
        }
        if (filtroOperacaoLigacaoSemContato) {
            filtroOperacao.append(" historico.tipoContato = 'LC' or ");
        }
        if (filtroOperacaoPessoal) {
            filtroOperacao.append(" historico.tipoContato= 'PE' or ");
        }
//		if (filtroOperacaoReagendamento) {
//			filtroOperacao.append(" historico.tipoOperacao = 'RE' or ");
//		}
        if (filtroOperacaoSMS) {
            filtroOperacao.append(" historico.tipoContato = 'CS' or ");
        }
        if (filtroOperacaoTelefone) {
            filtroOperacao.append(" historico.tipoContato = 'TE' or ");
        }
        if (!filtroOperacao.toString().equals("")) {
            sqlStr.append("( ").append(filtroOperacao.toString().substring(0, filtroOperacao.toString().length() - 3)).append(") and ");
        }
        try (Statement stm = con.createStatement()) {
            String sql = sqlStr.toString().substring(0, sqlStr.toString().length() - 4) + " order by  historico.passivo, historico.cliente, historico.indicado, historico.dia desc  ";
            try (ResultSet tabelaResultado = stm.executeQuery(sql)) {
                return (montarDadosConsulta(tabelaResultado, nivelMontarDados, con));
            }
        }
    }

    /**
     * Metodo responsavel por retornar uma consulta paginada em banco, de acordo
     * com os filtros passados
     * <p/>
     * Autor: Carla Criado em 13/01/2011
     */
    @SuppressWarnings("unchecked")
    @Override
    public List consultarPaginado(HistoricoContatoFiltroVO filtro, ConfPaginacao confPaginacao) throws Exception {
        //1- CONFIGURANDO A NAVEGACAO DA PAGINACAO
        confPaginacao.configurarNavegacao();

        //sql principal
        StringBuffer sqlStr = new StringBuffer();
        sqlStr.append("SELECT ");
        sqlStr.append("coalesce( pessoa.nome, passivo.nome,nomeindicado)  AS OrdenacaoNome,\n");
        sqlStr.append("cliente.matricula,pessoa.nome as nomePessoa,");
        sqlStr.append("cliente.empresa,");
        sqlStr.append("empresa.nome empresanome,");
        sqlStr.append("pessoa.codigo as codigoPessoa,passivo.codigo as codigoPassivo, passivo.nome as nomePassivo,"
                + " indicado.codigo as codigoIndicado,indicado.nomeindicado as nomeIndicado, historico.dia ,  "
                + "cliente.codigo as codigoCliente, historico.tipoContato, historico.resultado, "
                + "historico.responsavelCadastro, usuario.nome as nomeResponsavelCadastro, historico.codigo, ");
        sqlStr.append(" historico.fase, historico.contatoavulso, historico.observacao ");
        sqlStr.append(" FROM historicoContato as historico");

        //sql inner joins
        associacoes(filtro, sqlStr);


        //2 - CONFIGURA A INICIALIZACAO DA PAGINACAO PARA ESTA DAO.
        confPaginacao.iniciarPaginacao(this);

        //a utilização dos filtros deve ser feita utilizando confPaginacao.getSqlFiltro()
        filtros(filtro, confPaginacao.getSqlFiltro());
        if (!"".equals(confPaginacao.getSqlFiltro().toString())) {
            sqlStr.append(" WHERE ");
            sqlStr.append(confPaginacao.getSqlFiltro().toString());
        }

        sqlStr = new StringBuffer(sqlStr.toString().substring(0, sqlStr.toString().length() - 4));

        //concatena ordenações
        // ordenacao(filtro, sqlStr);
        // Adiciona a ordenação que esteja definida na dataTable
        if (!filtro.getOrdenacao().equals(""))
            sqlStr.append(" ORDER BY ").append(filtro.getOrdenacao()).append("\n");
        else
            ordenacao(filtro, sqlStr);
        //3 - ADICIONA PAGINACAO NA CONSULTA
        confPaginacao.addPaginacao(sqlStr);

        //seta os valores dos filtros
        filtrosValoresStatement(filtro, confPaginacao.getStm());

        //4 - REALIZA A CONSULTA COM PAGINACAO
        try (ResultSet tabelaResultado = confPaginacao.consultaPaginada()) {
            return (montarDadosConsulta(tabelaResultado, filtro.getNivelMontarDados(), con));
        }
    }

    /**
     * Atribuindo valores para os filtros no Statement para o metodo
     * consultarPaginado(ReciboPagamentoFiltroVO filtro, ConfPaginacao
     * confPaginacao)
     * <p/>
     * Autora: Carla Criado em 14/01/2011
     */
    private void filtrosValoresStatement(HistoricoContatoFiltroVO filtro, PreparedStatementPersonalizado stm) throws SQLException, Exception {
        int i = 0;
        //INICIO - SETANDO PARAMETROS PARA "filtros(HistoricoContatoFiltroVO filtro, StringBuffer sqlFiltro)"
        if (filtro != null) {

            if (filtro.getEmpresaVO() != null && filtro.getEmpresaVO().getCodigo() != 0) {
                stm.setInt(i++, filtro.getEmpresaVO().getCodigo());
                stm.setInt(i++, filtro.getEmpresaVO().getCodigo());
                stm.setInt(i++, filtro.getEmpresaVO().getCodigo());
            }
            if (filtro.getCodigoColaborador() > 0) {
                stm.setInt(i++, filtro.getCodigoColaborador());
            }
        }
        //FIM - SETANDO PARAMETROS PARA "filtros(HistoricoContatoFiltroVO filtro, StringBuffer sqlFiltro)"
    }

    /**
     * Definindo o tipo de ordenacao para o metodo
     * consultarPaginado(ReciboPagamentoFiltroVO filtro, ConfPaginacao
     * confPaginacao)
     * <p/>
     * Autora: Carla Criado em 14/01/2011
     */
    private void ordenacao(HistoricoContatoFiltroVO filtro, StringBuffer sql) {

        sql.append(" ORDER BY historico.dia ASC,historico.passivo, historico.cliente, historico.indicado, historico.dia desc  ");
    }

    /**
     * Definindo as condicoes da clausa WHERE para o metodo
     * consultarPaginado(ClienteFiltroVO filtro, ConfPaginacao confPaginacao)
     * <p/>
     * Autora: Carla Criado em 14/01/2011
     */
    private void filtros(HistoricoContatoFiltroVO filtro, StringBuffer sqlFiltro) throws Exception {
        //filtrando por data de inicio e termino
        if (filtro.getDataInicio() != null && filtro.getDataTermino() != null) {
            sqlFiltro.append(" historico.dia >= '").
                    append(Uteis.getDataJDBC(filtro.getDataInicio())).
                    append(" 00:00:00 ' and historico.dia <='").
                    append(Uteis.getDataJDBC(filtro.getDataTermino())).
                    append(" 23:59:59' and ");
        }
        //filtrando por data de inicio e termino da string resultado
        if (filtro.getDataInicioAgendamento() != null && filtro.getDataTerminoAgendameto() != null) {
            sqlFiltro.append(" (CASE ").
                    append(" WHEN historico.resultado ilike 'Ag.Ligação: %' THEN TO_DATE(SUBSTRING(historico.resultado FROM 'Ag.Ligação: ([0-9]{2}/[0-9]{2}/[0-9]{4})'), 'DD/MM/YYYY') ").
                    append(" WHEN historico.resultado ilike 'Ag.Visita: %' THEN TO_DATE(SUBSTRING(historico.resultado FROM 'Ag.Visita: ([0-9]{2}/[0-9]{2}/[0-9]{4})'), 'DD/MM/YYYY') ").
                    append(" WHEN historico.resultado ilike 'Ag.Aula Experimental: %' THEN TO_DATE(SUBSTRING(historico.resultado FROM 'Ag.Aula Experimental:: ([0-9]{2}/[0-9]{2}/[0-9]{4})'), 'DD/MM/YYYY') ").
                    append(" ELSE null ").
                    append("END) between '").
                    append(Uteis.getDataJDBC(filtro.getDataInicioAgendamento())).
                    append("' AND '").
                    append(Uteis.getDataJDBC(filtro.getDataTerminoAgendameto())).
                    append("' AND ");
        }
        //filtrando por empresa
        if (!UteisValidacao.emptyNumber(filtro.getEmpresaVO().getCodigo())) {
            sqlFiltro.append("   (cliente.empresa = ? OR passivo.empresa = ? OR indicado.empresa = ?) AND ");
        }
        if (filtro.getCodigoColaborador() != 0) {
            sqlFiltro.append(" historico.responsavelcadastro =  ? and ");
        }
        /*
         filtrando por fases
         A fase GA não é bem uma fase, por este motivo não é adicionada no select.
         */

        StringBuilder sqlFases = new StringBuilder();
        for (String fase : filtro.getListaFases()) {
            sqlFases.append(" historico.fase = '").append(fase).append("' or ");
        }
        if (!sqlFases.toString().equals("")) {
            sqlFiltro.append("( ").append(sqlFases.toString().substring(0, sqlFases.toString().length() - 3)).append(") and ");
        }
        //filtrando por operação
        StringBuilder sqlOperacoes = new StringBuilder();
        for (String operacao : filtro.getListaOperacoes()) {
            sqlOperacoes.append(" historico.tipoContato = '").append(operacao).append("' or ");
        }
        if (!sqlOperacoes.toString().equals("")) {
            sqlFiltro.append("( ").append(sqlOperacoes.toString().substring(0, sqlOperacoes.toString().length() - 3)).append(") and ");
        }



        Boolean todosResultadosSelecionados = filtro.isResultadoAgAula() && filtro.isResultadoAgLigacao() && filtro.isResultadoAgVisita() && filtro.isResultadoSimplesRegistro() && filtro.isResultadoObjecao() && filtro.isResultadoOutros();
        Boolean peloMenosUmResultadoSelecionado = filtro.isResultadoAgAula() || filtro.isResultadoAgLigacao() || filtro.isResultadoAgVisita() || filtro.isResultadoSimplesRegistro() || filtro.isResultadoObjecao() || filtro.isResultadoOutros();

        if(!todosResultadosSelecionados && peloMenosUmResultadoSelecionado) {

            StringBuilder sqlResultado = new StringBuilder();

            if(filtro.isResultadoObjecao()) {
                if(!filtro.getListaObjecoes().isEmpty()) {
                    sqlResultado.append("or historico.objecao in (").append(StringUtils.join(filtro.getListaObjecoes(), ",")).append(") ");
                }else{
                    sqlResultado.append("or historico.objecao > 0 ");
                }
            }

            if(filtro.isResultadoAgAula()){
                sqlResultado.append("or ( historico.objecao is null and historico.resultado like 'Ag.Aula Experimental%') ");
            }

            if(filtro.isResultadoAgLigacao()){
                sqlResultado.append("or ( historico.objecao is null and historico.resultado like 'Ag.Ligação%') ");
            }

            if(filtro.isResultadoAgVisita()){
                sqlResultado.append("or ( historico.objecao is null and historico.resultado like 'Ag.Visita%') ");
            }

            if(filtro.isResultadoSimplesRegistro()){
                sqlResultado.append("or ( historico.objecao is null and historico.resultado = 'Simples Registro') ");
            }

            if(filtro.isResultadoOutros()){
                sqlResultado.append("or not ( not historico.objecao is null or historico.resultado = 'Simples Registro' or historico.resultado like 'Ag.Visita%' or historico.resultado like 'Ag.Ligação%' or historico.resultado like 'Ag.Aula Experimental%')");
            }


            sqlFiltro.append("(");
            sqlFiltro.append(sqlResultado.toString().substring(3));
            sqlFiltro.append(") and ");
        }
    }

    /**
     * Definindo os Inner Join e Left Join para o metodo
     * consultarPaginado(HistoricoContatoFiltroVO filtro, ConfPaginacao
     * confPaginacao)
     * <p/>
     * Autora: Carla Criado em 23/04/2012
     */
    private void associacoes(HistoricoContatoFiltroVO filtro, StringBuffer sql) {
        sql.append(" LEFT JOIN cliente ON cliente.codigo = historico.cliente \n");
        sql.append(" LEFT JOIN empresa ON empresa.codigo = cliente.empresa \n");
        sql.append(" LEFT JOIN pessoa ON pessoa.codigo = cliente.pessoa  \n");
        sql.append(" LEFT JOIN passivo ON passivo.codigo = historico.passivo \n");
        sql.append(" LEFT JOIN indicado ON indicado.codigo = historico.indicado \n");
        sql.append(" LEFT JOIN usuario ON usuario.codigo = historico.responsavelCadastro \n");
        if (filtro.isGerouAgendamento() || (filtro.getDataInicioAgendamento() != null && filtro.getDataTerminoAgendameto() != null)) {
            sql.append(" INNER JOIN agenda ON agenda.codigo = historico.agenda \n");
        }
    }

    /**
     * Método responsavel por preencher os dados do passivo no momento em que
     * seleciona na tela metaPassivoDetalhadaForm.
     *
     * @param historicoContatoVO
     * @param obj
     * @param usuarioLogado
     * @param colaboradorResponsavel
     * @param malaDiretaVO
     * @param malaDiretaEnviadaVO
     * @param listatelfoneClientePotencial
     * @throws Exception
     */
    @Override
    public void inicializarDadosPassivoRealizacaoContato(HistoricoContatoVO historicoContatoVO, AgendaVO agenda, FecharMetaDetalhadoVO obj, UsuarioVO usuarioLogado, UsuarioVO colaboradorResponsavel, MalaDiretaVO malaDiretaVO, MalaDiretaEnviadaVO malaDiretaEnviadaVO, List<TelefoneVO> listatelfoneClientePotencial, Boolean reagendamento) throws Exception {
        if (reagendamento) {
            historicoContatoVO.setTipoOperacao("RE");
            agenda.setNovoObj(true);
        }
        historicoContatoVO.setPassivoVO(getFacade().getPassivo().consultarPorChavePrimaria(obj.getPassivo().getCodigo(), Uteis.NIVELMONTARDADOS_HISTORICOPASSIVO));
        historicoContatoVO.setCodigoFecharMetaDetalhado(obj.getCodigo());
        historicoContatoVO.setDiaAbertura(obj.getFecharMeta().getAberturaMetaVO().getDia());
        historicoContatoVO.setAberturaMetaEstaEmAberta(obj.getFecharMeta().getAberturaMetaVO().getMetaEmAberto());
        historicoContatoVO.setObservacao("");
        malaDiretaVO.setRemetente(usuarioLogado);
        malaDiretaEnviadaVO.setPassivoVO(historicoContatoVO.getPassivoVO());
        historicoContatoVO.setNovoObj(true);
        historicoContatoVO.setColaboradorResponsavel(colaboradorResponsavel);
        historicoContatoVO.setResponsavelCadastro(usuarioLogado);
        historicoContatoVO.setFase("CP");
        historicoContatoVO.setReagendamentoVO(obj.getAgenda());
        TelefoneVO telefone = new TelefoneVO();
        if (!obj.getPassivo().getTelefoneResidencial().equals("")) {
            telefone.setNumero(obj.getPassivo().getTelefoneResidencial());
            telefone.setTipoTelefone("RE");
            listatelfoneClientePotencial.add(telefone);
            telefone = new TelefoneVO();
        }
        if (!obj.getPassivo().getTelefoneCelular().equals("")) {
            telefone.setNumero(obj.getPassivo().getTelefoneCelular());
            telefone.setTipoTelefone("CE");
            listatelfoneClientePotencial.add(telefone);
            telefone = new TelefoneVO();
        }
        if (!obj.getPassivo().getTelefoneTrabalho().equals("")) {
            telefone.setNumero(obj.getPassivo().getTelefoneTrabalho());
            telefone.setTipoTelefone("CO");
            listatelfoneClientePotencial.add(telefone);
        }
        preencherHistoricoContatoComDadosPassivo(historicoContatoVO);

    }

    public void inicializarDadosPassivoRealizacaoContatoVindoTelaHistoricoContatoCons(HistoricoContatoVO historicoContatoVO, AgendaVO agenda, UsuarioVO usuarioLogado, UsuarioVO colaboradorResponsavel, MalaDiretaVO malaDiretaVO, MalaDiretaEnviadaVO malaDiretaEnviadaVO, List<TelefoneVO> listatelfoneClientePotencial, Boolean reagendamento, AberturaMetaVO abertura) throws Exception {
        if (reagendamento) {
            historicoContatoVO.setTipoOperacao("RE");
            agenda.setNovoObj(true);
        }
        historicoContatoVO.setPassivoVO(getFacade().getPassivo().consultarPorChavePrimaria(historicoContatoVO.getPassivoVO().getCodigo(), Uteis.NIVELMONTARDADOS_HISTORICOPASSIVO));
        historicoContatoVO.setObservacao("");
        historicoContatoVO.setDiaAbertura(abertura.getDia());
        historicoContatoVO.setAberturaMetaEstaEmAberta(abertura.getMetaEmAberto());
        historicoContatoVO.setCodigoFecharMetaDetalhado(getFacade().getFecharMetaDetalhado().consultarPorCodigoPassivoComBaseEmHistoriContato(historicoContatoVO.getPassivoVO().getCodigo(), false));
        malaDiretaVO.setRemetente(usuarioLogado);
        malaDiretaEnviadaVO.setPassivoVO(historicoContatoVO.getPassivoVO());
        historicoContatoVO.setNovoObj(true);
        historicoContatoVO.setColaboradorResponsavel(colaboradorResponsavel);
        historicoContatoVO.setResponsavelCadastro(usuarioLogado);
        historicoContatoVO.setFase("CP");
        TelefoneVO telefone = new TelefoneVO();
        if (!historicoContatoVO.getPassivoVO().getTelefoneResidencial().equals("")) {
            telefone.setNumero(historicoContatoVO.getPassivoVO().getTelefoneResidencial());
            telefone.setTipoTelefone("RE");
            listatelfoneClientePotencial.add(telefone);
            telefone = new TelefoneVO();
        }
        if (!historicoContatoVO.getPassivoVO().getTelefoneCelular().equals("")) {
            telefone.setNumero(historicoContatoVO.getPassivoVO().getTelefoneCelular());
            telefone.setTipoTelefone("CE");
            listatelfoneClientePotencial.add(telefone);
            telefone = new TelefoneVO();
        }
        if (!historicoContatoVO.getPassivoVO().getTelefoneTrabalho().equals("")) {
            telefone.setNumero(historicoContatoVO.getPassivoVO().getTelefoneTrabalho());
            telefone.setTipoTelefone("CO");
            listatelfoneClientePotencial.add(telefone);
        }
        preencherHistoricoContatoComDadosPassivo(historicoContatoVO);

    }

    /**
     * Método responsavel por preencher os dados do indicado no momento em que
     * seleciona na tela metaPassivoDetalhadaForm.
     *
     * @throws Exception
     */
    @Override
    public void inicializarDadosIndicadoRealizacaoContato(HistoricoContatoVO historicoContatoVO, AgendaVO agenda, FecharMetaDetalhadoVO obj, UsuarioVO usuarioLogado, UsuarioVO colaboradorResponsavel, MalaDiretaVO malaDiretaVO, MalaDiretaEnviadaVO malaDiretaEnviadaVO, List<TelefoneVO> listaTelefoneIndicado, Boolean reagendamento) throws Exception {
        if (reagendamento) {
            historicoContatoVO.setTipoOperacao("RE");
            agenda.setNovoObj(true);
        }
        historicoContatoVO.setCodigoFecharMetaDetalhado(obj.getCodigo());
        historicoContatoVO.setDiaAbertura(obj.getFecharMeta().getAberturaMetaVO().getDia());
        historicoContatoVO.setAberturaMetaEstaEmAberta(obj.getFecharMeta().getAberturaMetaVO().getMetaEmAberto());
        historicoContatoVO.setObservacao("");
        historicoContatoVO.setIndicadoVO(getFacade().getIndicado().consultarPorChavePrimaria(obj.getIndicado().getCodigo(), Uteis.NIVELMONTARDADOS_HISTORICOINDICADO));
        malaDiretaVO.setRemetente(usuarioLogado);
        malaDiretaEnviadaVO.setIndicadoVO(historicoContatoVO.getIndicadoVO());
        historicoContatoVO.setNovoObj(true);
        historicoContatoVO.setFase("IN");
        historicoContatoVO.setColaboradorResponsavel(colaboradorResponsavel);
        historicoContatoVO.setResponsavelCadastro(usuarioLogado);
        historicoContatoVO.setReagendamentoVO(obj.getAgenda());
        TelefoneVO telefone = new TelefoneVO();
        if (!obj.getIndicado().getTelefoneIndicado().equals("")) {
            telefone.setNumero(obj.getIndicado().getTelefoneIndicado());
            telefone.setTipoTelefone("RE");
            listaTelefoneIndicado.add(telefone);
            telefone = new TelefoneVO();
        }
        if (!obj.getIndicado().getTelefone().equals("")) {
            telefone.setNumero(obj.getIndicado().getTelefone());
            telefone.setTipoTelefone("CE");
            listaTelefoneIndicado.add(telefone);
            telefone = new TelefoneVO();
        }
        getFacade().getHistoricoContato().preencherHistoricoContatoComDadosIndicado(historicoContatoVO);
    }

    public void inicializarDadosIndicadoRealizacaoContatoVindoTelaHistoricoContatoCons(HistoricoContatoVO historicoContatoVO, AgendaVO agenda, UsuarioVO usuarioLogado, UsuarioVO colaboradorResponsavel, MalaDiretaVO malaDiretaVO, MalaDiretaEnviadaVO malaDiretaEnviadaVO, List<TelefoneVO> listaIndicado, Boolean reagendamento, AberturaMetaVO abertura) throws Exception {
        if (reagendamento) {
            historicoContatoVO.setTipoOperacao("RE");
            agenda.setNovoObj(true);
        }
        historicoContatoVO.setObservacao("");
        historicoContatoVO.setIndicadoVO(getFacade().getIndicado().consultarPorChavePrimaria(historicoContatoVO.getIndicadoVO().getCodigo(), Uteis.NIVELMONTARDADOS_HISTORICOINDICADO));
        historicoContatoVO.setDiaAbertura(abertura.getDia());
        historicoContatoVO.setAberturaMetaEstaEmAberta(abertura.getMetaEmAberto());
        historicoContatoVO.setCodigoFecharMetaDetalhado(getFacade().getFecharMetaDetalhado().consultarPorCodigoIndicadoComBaseEmHistoriContato(historicoContatoVO.getIndicadoVO().getCodigo(), false));
        malaDiretaVO.setRemetente(usuarioLogado);
        malaDiretaEnviadaVO.setIndicadoVO(historicoContatoVO.getIndicadoVO());
        historicoContatoVO.setNovoObj(true);
        historicoContatoVO.setFase("IN");
        historicoContatoVO.setColaboradorResponsavel(colaboradorResponsavel);
        historicoContatoVO.setResponsavelCadastro(usuarioLogado);
        TelefoneVO telefone = new TelefoneVO();
        if (!historicoContatoVO.getIndicadoVO().getTelefoneIndicado().equals("")) {
            telefone.setNumero(historicoContatoVO.getIndicadoVO().getTelefoneIndicado());
            telefone.setTipoTelefone("RE");
            listaIndicado.add(telefone);
            telefone = new TelefoneVO();
        }
        if (!historicoContatoVO.getIndicadoVO().getTelefone().equals("")) {
            telefone.setNumero(historicoContatoVO.getIndicadoVO().getTelefone());
            telefone.setTipoTelefone("CE");
            listaIndicado.add(telefone);
            telefone = new TelefoneVO();
        }
        getFacade().getHistoricoContato().preencherHistoricoContatoComDadosIndicado(historicoContatoVO);
    }

    /**
     * Método responsavel por preencher os dados do agendados no momento em que
     * seleciona na tela metaAgendadosDetalhadaForm.
     *
     * @throws Exception
     */
    @Override
    public HistoricoContatoVO inicializarDadosAgendadosRealizacaoContato(
            HistoricoContatoVO hist, AgendaVO agenda, FecharMetaDetalhadoVO obj,
            UsuarioVO usuarioLogado, UsuarioVO colaboradorResponsavel,
            MalaDiretaVO malaDiretaVO,
            MalaDiretaEnviadaVO malaDiretaEnviadaVO,
            List<TelefoneVO> listatelfoneClientePotencial,
            List listaTelefoneIndicado, Boolean reagendamento) throws Exception {

        hist.setCodigoFecharMetaDetalhado(obj.getCodigo().intValue());
        if (obj.getPassivo().getCodigo() != 0) {
            inicializarDadosPassivoRealizacaoContato(hist, agenda, obj, usuarioLogado, colaboradorResponsavel, malaDiretaVO, malaDiretaEnviadaVO, listatelfoneClientePotencial, reagendamento);
        } else if (obj.getIndicado().getCodigo() != 0) {
            inicializarDadosIndicadoRealizacaoContato(hist, agenda, obj, usuarioLogado, colaboradorResponsavel, malaDiretaVO, malaDiretaEnviadaVO, listaTelefoneIndicado, reagendamento);
        } else {
            if (reagendamento) {
                hist.setTipoOperacao("RE");
                agenda.setNovoObj(true);
                agenda.setTipoAgendamento(obj.getAgenda().getTipoAgendamento());
                agenda.setModalidade(obj.getAgenda().getModalidade());
            }
            hist.setClienteVO(getFacade().getCliente().consultarPorChavePrimaria(obj.getCliente().getCodigo(), Uteis.NIVELMONTARDADOS_DADOSENTIDADESPRINCIPAIS));
            hist.setCodigoFecharMetaDetalhado(obj.getCodigo());
            hist.setDiaAbertura(obj.getFecharMeta().getAberturaMetaVO().getDia());
            hist.setAberturaMetaEstaEmAberta(obj.getFecharMeta().getAberturaMetaVO().getMetaEmAberto());
            hist.setObservacao("");
            malaDiretaVO.setRemetente(usuarioLogado);
            hist.setNovoObj(true);
            hist.setColaboradorResponsavel(colaboradorResponsavel);
            hist.setResponsavelCadastro(usuarioLogado);
            preencherHistoricoContatoComDadosCliente(hist);
            hist.setReagendamentoVO(obj.getAgenda());
        }
        return hist;
    }

    @Override
    public HistoricoContatoVO inicializarDadosPessoasHistoricoContatoRealizacaoContato(HistoricoContatoVO hist, AgendaVO agenda, UsuarioVO usuarioLogado, UsuarioVO colaboradorResponsavel, MalaDiretaVO malaDiretaVO, MalaDiretaEnviadaVO malaDiretaEnviadaVO, List listatelfoneClientePotencial, List listaTelefoneIndicado, Boolean reagendamento, AberturaMetaVO abertura) throws Exception {
        if (hist.getPassivoVO().getCodigo() != 0) {
            inicializarDadosPassivoRealizacaoContatoVindoTelaHistoricoContatoCons(hist, agenda, usuarioLogado, colaboradorResponsavel, malaDiretaVO, malaDiretaEnviadaVO, listatelfoneClientePotencial, reagendamento, abertura);
        } else if (hist.getIndicadoVO().getCodigo() != 0) {
            inicializarDadosIndicadoRealizacaoContatoVindoTelaHistoricoContatoCons(hist, agenda, usuarioLogado, colaboradorResponsavel, malaDiretaVO, malaDiretaEnviadaVO, listaTelefoneIndicado, reagendamento, abertura);
        } else {
            if (reagendamento) {
                hist.setTipoOperacao("RE");
                agenda.setNovoObj(true);
            }
            hist.setClienteVO(getFacade().getCliente().consultarPorChavePrimaria(hist.getClienteVO().getCodigo(), Uteis.NIVELMONTARDADOS_DADOSENTIDADESPRINCIPAIS));
            hist.setObservacao("");
            hist.setDiaAbertura(abertura.getDia());
            hist.setAberturaMetaEstaEmAberta(abertura.getMetaEmAberto());
            hist.setCodigoFecharMetaDetalhado(getFacade().getFecharMetaDetalhado().consultarPorCodigoClienteComBaseEmHistoriContato(hist.getClienteVO().getCodigo(), false));
            malaDiretaVO.setRemetente(usuarioLogado);
            hist.setNovoObj(true);
            hist.setColaboradorResponsavel(colaboradorResponsavel);
            hist.setResponsavelCadastro(usuarioLogado);
            hist.setFase("AG");
            preencherHistoricoContatoComDadosCliente(hist);
        }
        return hist;
    }

    /**
     * Método responsavel por preencher os dados do vinteQuatroHoras no momento
     * em que seleciona na tela metaAgendadosDetalhadaForm.
     *
     * @param historicoContatoVO
     * @param obj
     * @param usuarioLogado
     * @param colaboradorResponsavel
     * @param malaDiretaVO
     * @param malaDiretaEnviadaVO
     * @param listatelfoneClientePotencial
     * @throws Exception
     */
    @Override
    public void inicializarDadosVinteQuatroHorasRealizacaoContato(HistoricoContatoVO historicoContatoVO, AgendaVO agenda, FecharMetaDetalhadoVO obj, UsuarioVO usuarioLogado, UsuarioVO colaboradorResponsavel, MalaDiretaVO malaDiretaVO, MalaDiretaEnviadaVO malaDiretaEnviadaVO, List<TelefoneVO> listatelfoneClientePotencial, List listaTelefoneIndicado, Boolean reagendamento) throws Exception {
        historicoContatoVO.setCodigoFecharMetaDetalhado(obj.getCodigo().intValue());
        historicoContatoVO.setDiaAbertura(obj.getFecharMeta().getAberturaMetaVO().getDia());
        historicoContatoVO.setAberturaMetaEstaEmAberta(obj.getFecharMeta().getAberturaMetaVO().getMetaEmAberto());
        historicoContatoVO.setClienteVO(getFacade().getCliente().consultarPorChavePrimaria(obj.getCliente().getCodigo(), Uteis.NIVELMONTARDADOS_DADOSENTIDADESPRINCIPAIS));
        historicoContatoVO.setObservacao("");
        malaDiretaVO.setRemetente(usuarioLogado);
        historicoContatoVO.setNovoObj(true);
        historicoContatoVO.setColaboradorResponsavel(colaboradorResponsavel);
        historicoContatoVO.setResponsavelCadastro(usuarioLogado);
        historicoContatoVO.setFase("HO");
        preencherHistoricoContatoComDadosCliente(historicoContatoVO);
        for (TelefoneVO tel : obj.getCliente().getPessoa().getTelefoneVOs()) {
            historicoContatoVO.getClienteVO().getPessoa().setTelefoneVOs(obj.getCliente().getPessoa().getTelefoneVOs());
        }
    }

    /**
     * Método responsavel por preencher os dados do vinteQuatroHoras no momento
     * em que seleciona na tela metaAgendadosDetalhadaForm.
     *
     * @param historicoContatoVO
     * @param obj
     * @param usuarioLogado
     * @param colaboradorResponsavel
     * @param malaDiretaVO
     * @param malaDiretaEnviadaVO
     * @param listatelfoneClientePotencial
     * @throws Exception
     */
    @Override
    public void inicializarDadosVendasRealizacaoContato(HistoricoContatoVO historicoContatoVO, AgendaVO agenda, FecharMetaDetalhadoVO obj, UsuarioVO usuarioLogado, UsuarioVO colaboradorResponsavel, MalaDiretaVO malaDiretaVO, MalaDiretaEnviadaVO malaDiretaEnviadaVO, List<TelefoneVO> listatelfoneClientePotencial, List listaTelefoneIndicado, Boolean reagendamento) throws Exception {
        historicoContatoVO.setCodigoFecharMetaDetalhado(obj.getCodigo().intValue());
        historicoContatoVO.setDiaAbertura(obj.getFecharMeta().getAberturaMetaVO().getDia());
        historicoContatoVO.setAberturaMetaEstaEmAberta(obj.getFecharMeta().getAberturaMetaVO().getMetaEmAberto());
        historicoContatoVO.setClienteVO(getFacade().getCliente().consultarPorChavePrimaria(obj.getCliente().getCodigo(), Uteis.NIVELMONTARDADOS_DADOSENTIDADESPRINCIPAIS));
        historicoContatoVO.setObservacao("");
        malaDiretaVO.setRemetente(usuarioLogado);
        historicoContatoVO.setNovoObj(true);
        historicoContatoVO.setColaboradorResponsavel(colaboradorResponsavel);
        historicoContatoVO.setResponsavelCadastro(usuarioLogado);
        historicoContatoVO.setFase("MQ");
        preencherHistoricoContatoComDadosCliente(historicoContatoVO);
        for (TelefoneVO tel : obj.getCliente().getPessoa().getTelefoneVOs()) {
            historicoContatoVO.getClienteVO().getPessoa().setTelefoneVOs(obj.getCliente().getPessoa().getTelefoneVOs());
        }
    }

    /**
     * /**
     * Método responsavel por preencher os dados do vinteQuatroHoras no momento
     * em que seleciona na tela metaAgendadosDetalhadaForm.
     *
     * @param historicoContatoVO
     * @param obj
     * @param usuarioLogado
     * @param colaboradorResponsavel
     * @param malaDiretaVO
     * @param malaDiretaEnviadaVO
     * @param listatelfoneClientePotencial
     * @throws Exception
     */
    @Override
    public void inicializarDadosFaturamentoRealizacaoContato(HistoricoContatoVO historicoContatoVO, AgendaVO agenda, FecharMetaDetalhadoVO obj, UsuarioVO usuarioLogado, UsuarioVO colaboradorResponsavel, MalaDiretaVO malaDiretaVO, MalaDiretaEnviadaVO malaDiretaEnviadaVO, List<TelefoneVO> listatelfoneClientePotencial, List listaTelefoneIndicado, Boolean reagendamento) throws Exception {
        historicoContatoVO.setCodigoFecharMetaDetalhado(obj.getCodigo().intValue());
        historicoContatoVO.setDiaAbertura(obj.getFecharMeta().getAberturaMetaVO().getDia());
        historicoContatoVO.setAberturaMetaEstaEmAberta(obj.getFecharMeta().getAberturaMetaVO().getMetaEmAberto());
        historicoContatoVO.setClienteVO(getFacade().getCliente().consultarPorChavePrimaria(obj.getCliente().getCodigo(), Uteis.NIVELMONTARDADOS_DADOSENTIDADESPRINCIPAIS));
        historicoContatoVO.setObservacao("");
        malaDiretaVO.setRemetente(usuarioLogado);
        historicoContatoVO.setNovoObj(true);
        historicoContatoVO.setColaboradorResponsavel(colaboradorResponsavel);
        historicoContatoVO.setResponsavelCadastro(usuarioLogado);
        historicoContatoVO.setFase("MF");
        preencherHistoricoContatoComDadosCliente(historicoContatoVO);
        for (TelefoneVO tel : obj.getCliente().getPessoa().getTelefoneVOs()) {
            historicoContatoVO.getClienteVO().getPessoa().setTelefoneVOs(obj.getCliente().getPessoa().getTelefoneVOs());
        }
    }

    /**
     * M?todo responsavel por preencher os dados do Faltas no momento em que
     * seleciona na tela metaAgendadosDetalhadaForm.
     *
     * @param historicoContatoVO
     * @param obj
     * @param usuarioLogado
     * @param colaboradorResponsavel
     * @param malaDiretaVO
     * @param malaDiretaEnviadaVO
     * @param listatelfoneClientePotencial
     * @throws Exception
     */
    @Override
    public void inicializarDadosFaltasRealizacaoContato(HistoricoContatoVO historicoContatoVO, AgendaVO agenda, FecharMetaDetalhadoVO obj, UsuarioVO usuarioLogado, UsuarioVO colaboradorResponsavel, MalaDiretaVO malaDiretaVO, MalaDiretaEnviadaVO malaDiretaEnviadaVO, List<TelefoneVO> listatelfoneClientePotencial, List listaTelefoneIndicado, Boolean reagendamento) throws Exception {
        historicoContatoVO.setCodigoFecharMetaDetalhado(obj.getCodigo().intValue());
        historicoContatoVO.setDiaAbertura(obj.getFecharMeta().getAberturaMetaVO().getDia());
        historicoContatoVO.setAberturaMetaEstaEmAberta(obj.getFecharMeta().getAberturaMetaVO().getMetaEmAberto());
        historicoContatoVO.setClienteVO(getFacade().getCliente().consultarPorChavePrimaria(obj.getCliente().getCodigo(), Uteis.NIVELMONTARDADOS_DADOSENTIDADESPRINCIPAIS));
        historicoContatoVO.setObservacao("");
        malaDiretaVO.setRemetente(usuarioLogado);
        historicoContatoVO.setNovoObj(true);
        historicoContatoVO.setColaboradorResponsavel(colaboradorResponsavel);
        historicoContatoVO.setResponsavelCadastro(usuarioLogado);
        historicoContatoVO.setFase("FA");
        preencherHistoricoContatoComDadosCliente(historicoContatoVO);
        for (TelefoneVO tel : obj.getCliente().getPessoa().getTelefoneVOs()) {
            historicoContatoVO.getClienteVO().getPessoa().setTelefoneVOs(obj.getCliente().getPessoa().getTelefoneVOs());
        }
    }

    /**
     * Método responsavel por preencher os dados do Perda no momento em que
     * seleciona na tela metaPerdaDetalhadaForm.
     *
     * @param historicoContatoVO
     * @param obj
     * @param usuarioLogado
     * @param colaboradorResponsavel
     * @param malaDiretaVO
     * @param malaDiretaEnviadaVO
     * @param listatelfoneClientePotencial
     * @throws Exception
     */
    @Override
    public void inicializarDadosPerdaRealizacaoContato(HistoricoContatoVO historicoContatoVO, AgendaVO agenda, FecharMetaDetalhadoVO obj, UsuarioVO usuarioLogado, UsuarioVO colaboradorResponsavel, MalaDiretaVO malaDiretaVO, MalaDiretaEnviadaVO malaDiretaEnviadaVO, List<TelefoneVO> listatelfoneClientePotencial, List listaTelefoneIndicado, Boolean reagendamento) throws Exception {
        historicoContatoVO.setCodigoFecharMetaDetalhado(obj.getCodigo().intValue());
        historicoContatoVO.setDiaAbertura(obj.getFecharMeta().getAberturaMetaVO().getDia());
        historicoContatoVO.setAberturaMetaEstaEmAberta(obj.getFecharMeta().getAberturaMetaVO().getMetaEmAberto());
        historicoContatoVO.setClienteVO(getFacade().getCliente().consultarPorChavePrimaria(obj.getCliente().getCodigo(), Uteis.NIVELMONTARDADOS_DADOSENTIDADESPRINCIPAIS));
        historicoContatoVO.setObservacao("");
        malaDiretaVO.setRemetente(usuarioLogado);
        historicoContatoVO.setNovoObj(true);
        historicoContatoVO.setColaboradorResponsavel(colaboradorResponsavel);
        historicoContatoVO.setResponsavelCadastro(usuarioLogado);
        historicoContatoVO.setFase("PE");
        preencherHistoricoContatoComDadosCliente(historicoContatoVO);
        for (TelefoneVO tel : obj.getCliente().getPessoa().getTelefoneVOs()) {
            historicoContatoVO.getClienteVO().getPessoa().setTelefoneVOs(obj.getCliente().getPessoa().getTelefoneVOs());
        }
    }

    /**
     * Método responsavel por preencher os dados do GrupoRisco no momento em que
     * seleciona na tela metaAgendadosDetalhadaForm.
     *
     * @param historicoContatoVO
     * @param obj
     * @param usuarioLogado
     * @param colaboradorResponsavel
     * @param malaDiretaVO
     * @param malaDiretaEnviadaVO
     * @param listatelfoneClientePotencial
     * @throws Exception
     */
    @Override
    public void inicializarDadosGrupoRiscoRealizacaoContato(HistoricoContatoVO historicoContatoVO, AgendaVO agenda, FecharMetaDetalhadoVO obj, UsuarioVO usuarioLogado, UsuarioVO colaboradorResponsavel, MalaDiretaVO malaDiretaVO, MalaDiretaEnviadaVO malaDiretaEnviadaVO, List<TelefoneVO> listatelfoneClientePotencial, List listaTelefoneIndicado, Boolean reagendamento) throws Exception {
        historicoContatoVO.setCodigoFecharMetaDetalhado(obj.getCodigo().intValue());
        historicoContatoVO.setDiaAbertura(obj.getFecharMeta().getAberturaMetaVO().getDia());
        historicoContatoVO.setAberturaMetaEstaEmAberta(obj.getFecharMeta().getAberturaMetaVO().getMetaEmAberto());
        historicoContatoVO.setClienteVO(getFacade().getCliente().consultarPorChavePrimaria(obj.getCliente().getCodigo(), Uteis.NIVELMONTARDADOS_DADOSENTIDADESPRINCIPAIS));
        historicoContatoVO.setObservacao("");
        malaDiretaVO.setRemetente(usuarioLogado);
        historicoContatoVO.setNovoObj(true);
        historicoContatoVO.setColaboradorResponsavel(colaboradorResponsavel);
        historicoContatoVO.setResponsavelCadastro(usuarioLogado);
        historicoContatoVO.setFase("RI");
        preencherHistoricoContatoComDadosCliente(historicoContatoVO);
        for (TelefoneVO tel : obj.getCliente().getPessoa().getTelefoneVOs()) {
            historicoContatoVO.getClienteVO().getPessoa().setTelefoneVOs(obj.getCliente().getPessoa().getTelefoneVOs());
        }
    }

    @Override
    public void inicializarDadosVencidosRealizacaoContato(HistoricoContatoVO historicoContatoVO, AgendaVO agenda, FecharMetaDetalhadoVO obj, UsuarioVO usuarioLogado, UsuarioVO colaboradorResponsavel, MalaDiretaVO malaDiretaVO, MalaDiretaEnviadaVO malaDiretaEnviadaVO, List<TelefoneVO> listatelfoneClientePotencial, List listaTelefoneIndicado, Boolean reagendamento) throws Exception {
        historicoContatoVO.setCodigoFecharMetaDetalhado(obj.getCodigo().intValue());
        historicoContatoVO.setDiaAbertura(obj.getFecharMeta().getAberturaMetaVO().getDia());
        historicoContatoVO.setAberturaMetaEstaEmAberta(obj.getFecharMeta().getAberturaMetaVO().getMetaEmAberto());
        historicoContatoVO.setClienteVO(getFacade().getCliente().consultarPorChavePrimaria(obj.getCliente().getCodigo(), Uteis.NIVELMONTARDADOS_DADOSENTIDADESPRINCIPAIS));
        historicoContatoVO.setObservacao("");
        malaDiretaVO.setRemetente(usuarioLogado);
        historicoContatoVO.setNovoObj(true);
        historicoContatoVO.setColaboradorResponsavel(colaboradorResponsavel);
        historicoContatoVO.setResponsavelCadastro(usuarioLogado);
        historicoContatoVO.setFase("VE");
        preencherHistoricoContatoComDadosCliente(historicoContatoVO);
        for (TelefoneVO tel : obj.getCliente().getPessoa().getTelefoneVOs()) {
            historicoContatoVO.getClienteVO().getPessoa().setTelefoneVOs(obj.getCliente().getPessoa().getTelefoneVOs());
        }
    }

    /**
     * Método responsavel por preencher os dados do vinteQuatroHoras no momento
     * em que seleciona na tela metaAgendadosDetalhadaForm.
     *
     * @param historicoContatoVO
     * @param obj
     * @param usuarioLogado
     * @param colaboradorResponsavel
     * @param malaDiretaVO
     * @param malaDiretaEnviadaVO
     * @param listatelfoneClientePotencial
     * @throws Exception
     */
    @Override
    public void inicializarDadosPosVendaRealizacaoContato(HistoricoContatoVO historicoContatoVO, AgendaVO agenda, FecharMetaDetalhadoVO obj, UsuarioVO usuarioLogado, UsuarioVO colaboradorResponsavel, MalaDiretaVO malaDiretaVO, MalaDiretaEnviadaVO malaDiretaEnviadaVO, List<TelefoneVO> listatelfoneClientePotencial, List listaTelefoneIndicado, Boolean reagendamento) throws Exception {
        historicoContatoVO.setCodigoFecharMetaDetalhado(obj.getCodigo().intValue());
        historicoContatoVO.setDiaAbertura(obj.getFecharMeta().getAberturaMetaVO().getDia());
        historicoContatoVO.setAberturaMetaEstaEmAberta(obj.getFecharMeta().getAberturaMetaVO().getMetaEmAberto());
        historicoContatoVO.setClienteVO(getFacade().getCliente().consultarPorChavePrimaria(obj.getCliente().getCodigo(), Uteis.NIVELMONTARDADOS_DADOSENTIDADESPRINCIPAIS));
        historicoContatoVO.setObservacao("");
        malaDiretaVO.setRemetente(usuarioLogado);
        historicoContatoVO.setNovoObj(true);
        historicoContatoVO.setColaboradorResponsavel(colaboradorResponsavel);
        historicoContatoVO.setResponsavelCadastro(usuarioLogado);
        historicoContatoVO.setFase("PV");
        preencherHistoricoContatoComDadosCliente(historicoContatoVO);
        for (TelefoneVO tel : obj.getCliente().getPessoa().getTelefoneVOs()) {
            historicoContatoVO.getClienteVO().getPessoa().setTelefoneVOs(obj.getCliente().getPessoa().getTelefoneVOs());
        }
    }

    /**
     * Método responsavel por preencher os dados do Renovacao no momento em que
     * seleciona na tela metaAgendadosDetalhadaForm.
     *
     * @param historicoContatoVO
     * @param obj
     * @param usuarioLogado
     * @param colaboradorResponsavel
     * @param malaDiretaVO
     * @param malaDiretaEnviadaVO
     * @param listatelfoneClientePotencial
     * @throws Exception
     */
    @Override
    public void inicializarDadosRenovacaoRealizacaoContato(HistoricoContatoVO historicoContatoVO, AgendaVO agenda, FecharMetaDetalhadoVO obj, UsuarioVO usuarioLogado, UsuarioVO colaboradorResponsavel, MalaDiretaVO malaDiretaVO, MalaDiretaEnviadaVO malaDiretaEnviadaVO, List<TelefoneVO> listatelfoneClientePotencial, List listaTelefoneIndicado, Boolean reagendamento) throws Exception {
        historicoContatoVO.setCodigoFecharMetaDetalhado(obj.getCodigo().intValue());
        historicoContatoVO.setDiaAbertura(obj.getFecharMeta().getAberturaMetaVO().getDia());
        historicoContatoVO.setAberturaMetaEstaEmAberta(obj.getFecharMeta().getAberturaMetaVO().getMetaEmAberto());
        historicoContatoVO.setClienteVO(getFacade().getCliente().consultarPorChavePrimaria(obj.getCliente().getCodigo(), Uteis.NIVELMONTARDADOS_DADOSENTIDADESPRINCIPAIS));
        historicoContatoVO.setObservacao("");
        malaDiretaVO.setRemetente(usuarioLogado);
        historicoContatoVO.setNovoObj(true);
        historicoContatoVO.setColaboradorResponsavel(colaboradorResponsavel);
        historicoContatoVO.setResponsavelCadastro(usuarioLogado);
        historicoContatoVO.setFase("RE");
        preencherHistoricoContatoComDadosCliente(historicoContatoVO);
        for (TelefoneVO tel : obj.getCliente().getPessoa().getTelefoneVOs()) {
            historicoContatoVO.getClienteVO().getPessoa().setTelefoneVOs(obj.getCliente().getPessoa().getTelefoneVOs());
        }

    }

    /**
     * Método responsavel por preencher os dados do agendados no momento em que
     * seleciona na tela metaAgendadosDetalhadaForm.
     *
     * @param historicoContatoVO
     * @param obj
     * @param usuarioLogado
     * @param colaboradorResponsavel
     * @param malaDiretaVO
     * @param malaDiretaEnviadaVO
     * @param listatelfoneClientePotencial
     * @throws Exception
     */
    @Override
    public void inicializarDadosAniversarianteRealizacaoContato(HistoricoContatoVO historicoContatoVO, AgendaVO agenda, FecharMetaDetalhadoVO obj, UsuarioVO usuarioLogado, UsuarioVO colaboradorResponsavel, MalaDiretaVO malaDiretaVO, MalaDiretaEnviadaVO malaDiretaEnviadaVO, List<TelefoneVO> listatelfoneClientePotencial, List listaTelefoneIndicado, Boolean reagendamento) throws Exception {
        historicoContatoVO.setCodigoFecharMetaDetalhado(obj.getCodigo().intValue());
        historicoContatoVO.setDiaAbertura(obj.getFecharMeta().getAberturaMetaVO().getDia());
        historicoContatoVO.setAberturaMetaEstaEmAberta(obj.getFecharMeta().getAberturaMetaVO().getMetaEmAberto());
        historicoContatoVO.setClienteVO(getFacade().getCliente().consultarPorChavePrimaria(obj.getCliente().getCodigo(), Uteis.NIVELMONTARDADOS_DADOSENTIDADESPRINCIPAIS));
        historicoContatoVO.setObservacao("");
        malaDiretaVO.setRemetente(usuarioLogado);
        historicoContatoVO.setNovoObj(true);
        historicoContatoVO.setColaboradorResponsavel(colaboradorResponsavel);
        historicoContatoVO.setResponsavelCadastro(usuarioLogado);
        historicoContatoVO.setFase("AN");
        preencherHistoricoContatoComDadosCliente(historicoContatoVO);
        for (TelefoneVO tel : obj.getCliente().getPessoa().getTelefoneVOs()) {
            historicoContatoVO.getClienteVO().getPessoa().setTelefoneVOs(obj.getCliente().getPessoa().getTelefoneVOs());
        }

    }

    /**
     * Responsável por obter o Vencimento do Ultimo Contrato do cliente
     *
     * <AUTHOR> 31/05/2011
     */
    @Override
    public Date obterVencimentoUltimoContrato(Integer valorConsulta) throws Exception {
        StringBuilder sql = new StringBuilder();
        sql.append("SELECT con.vigenciaateajustada ");
        sql.append("FROM   contrato con ");
        sql.append("       INNER JOIN cliente c ");
        sql.append("       ON     c.pessoa = con.pessoa ");
        sql.append("       AND    c.codigo = ?");
        sql.append("       ORDER BY con.vigenciaateajustada DESC");
        Declaracao dc = new Declaracao(sql.toString(), con);
        dc.setInt(1, valorConsulta);
        Date vencimento;
        try (ResultSet rs = dc.executeQuery()) {
            vencimento = null;
            if (rs.next()) {
                vencimento = rs.getDate("vigenciaateajustada");
            }
        }
        return vencimento;
    }

    /**
     * Responsável por consultar a quantidade de Dias do Ultimo Acesso
     *
     * <AUTHOR> 18/08/2011
     */
    @Override
    public long consultarDiasUltimoAcesso(Integer valorConsulta) throws Exception {
        long nrUltimoAcesso = 0;
        String sqlStr = "";
        //consultar ultimo acesso do cliente
        sqlStr = "SELECT MAX(acessoCliente.dthrentrada) AS ultimoacesso FROM acessoCliente WHERE acessoCliente.cliente = " + valorConsulta;
        Date ultimoAcesso;
        try (ResultSet tabelaResultado = HistoricoContato.criarConsulta(sqlStr, con)) {
            ultimoAcesso = null;
            if (tabelaResultado.next()) {
                ultimoAcesso = tabelaResultado.getDate("ultimoacesso");
            }
            //se não tem ultimo acesso obter data da matricula
            if (ultimoAcesso == null) {
                sqlStr = "SELECT MIN(contrato.datamatricula) AS matricula FROM contrato, cliente WHERE cliente.pessoa = contrato.pessoa AND cliente.codigo = " + valorConsulta;
                ResultSet tabelaResultado2 = HistoricoContato.criarConsulta(sqlStr, con);
                if (tabelaResultado2.next()) {
                    ultimoAcesso = tabelaResultado2.getDate("matricula");
                }
            }
        }
        //obter a diferença entre o ultimo acesso e data atual
        if (ultimoAcesso != null) {
            nrUltimoAcesso = Uteis.nrDiasEntreDatas(ultimoAcesso, negocio.comuns.utilitarias.Calendario.hoje());
        }
        return nrUltimoAcesso;

    }

    @Override
    public List<HistoricoContatoVO> consultarLigacoesCliente(Integer codCliente, boolean controlarAcesso, int nivelMontarDados) throws Exception {
        consultarCRM(getIdEntidade(), controlarAcesso);
        String sqlStr = "SELECT * FROM HistoricoContato where cliente = " + codCliente.intValue() + " and tipoContato = 'TE' ORDER BY dia desc";
        try (Statement stm = con.createStatement()) {
            try (ResultSet tabelaResultado = stm.executeQuery(sqlStr)) {
                return (montarDadosConsulta(tabelaResultado, nivelMontarDados, con));
            }
        }
    }

    @Override
    public String consultarGrauSatisfacao(Integer codCliente) throws Exception {
        String grau = null;
        String sqlStr = "SELECT grausatisfacao FROM historicocontato WHERE cliente = " + codCliente + " ORDER BY dia DESC LIMIT 1";
        try (Statement stm = con.createStatement()) {
            try (ResultSet tabelaResultado = stm.executeQuery(sqlStr)) {
                if (tabelaResultado.next()) {
                    grau = tabelaResultado.getString("grausatisfacao");
                }
            }
        }
        return grau == null ? "" : grau;
    }

    public void gravarHistoricoContatoMalaDiretaAgendado(MalaDiretaVO malaDireta, ClienteVO cliente, MailingItensController mailingItensController, Boolean wagienvi) throws Exception {

        if(cliente != null){
            executarGravacaoVindoEmail(malaDireta, 0, cliente.getCodigo(), 0, null, wagienvi);
        }else if (malaDireta.getMalaDiretaEnviadaVOs().isEmpty()) {
            if (mailingItensController != null && !mailingItensController.getEnviados().toString().isEmpty()) {
                try {
                    String[] clientesEnviados = mailingItensController.getEnviados().toString().split(",");
                    int i = 0;
                    while (i < clientesEnviados.length) {
                        if (!clientesEnviados[i].isEmpty()) {
                            int codigoCliente = Integer.parseInt(clientesEnviados[i]);
                            executarGravacaoVindoEmail(malaDireta, 0, codigoCliente, 0, null, wagienvi);
                        }
                        i++;
                    }
                } catch (Exception e) {
                    e.getStackTrace();
                }
            }
        } else {
            FecharMetaDetalhado fecharMetaDetalhadoDAO = new FecharMetaDetalhado(con);
            ConfiguracaoSistemaCRMVO config = new ConfiguracaoSistemaCRM(con).consultarPorChavePrimaria(1, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
            FecharMeta fecharMetaDao = new FecharMeta(con);
            Date dataAvaliar = Calendario.hoje();
            if(!UteisValidacao.emptyNumber(malaDireta.getCodAberturaMeta())){
                Date dataAberturaMeta = new AberturaMeta(con).obterDiaAberturaMeta(malaDireta.getCodAberturaMeta());
                if(dataAberturaMeta != null){
                    dataAvaliar = dataAberturaMeta;
                }
            }
            for (MalaDiretaEnviadaVO mlde : malaDireta.getMalaDiretaEnviadaVOs()) {
                List<Map<String, Object>> listaMetas = new ArrayList<Map<String, Object>>();
                if (!malaDireta.isContatoAvulso()) { //só irá consultar metas se não for um contato avulso e for  depois da execução do agendamento.
                    listaMetas.addAll(fecharMetaDetalhadoDAO.consultarPorClienteDia(
                            mlde.getClienteVO().getPessoa().getCodigo(),
                            dataAvaliar,
                            Uteis.NIVELMONTARDADOS_DADOSBASICOS));
                }
                Integer codigoHis = executarGravacaoVindoEmail(malaDireta, 0, mlde.getClienteVO().getCodigo(), 0, null, wagienvi);
                if (!listaMetas.isEmpty()) {
                    try {
                        for (Map<String, Object> map : listaMetas) {
                            String identificadorMeta = (String) map.get("identificadormeta");
                            if (!(Boolean) map.get("obtevesucesso") && (config.isBaterMetaTodasAcoes() || identificadorMeta.equals("AN")
                                    || identificadorMeta.equals("PV")
                                    || identificadorMeta.equals("RI")
                                    || identificadorMeta.equals("FA")
                                    || identificadorMeta.equals("SF")
                                    || identificadorMeta.equals("SA")
                                    || identificadorMeta.equals("LA")
                                    || identificadorMeta.equals("CR"))) {
                                fecharMetaDao.executarValidacaoQuandoGravaHistoricoContatoAlcancaMetaAtingida((Date) map.get("dia"),
                                        (String) map.get("identificadormeta"),
                                        (Integer) map.get("codigoFecharMetaDetalhada"), (Boolean) map.get("metaemaberto"),
                                        fecharMetaDetalhadoDAO);
                                fecharMetaDetalhadoDAO.alterarSomenteCamposHistoricoContatoObteveSucesso(codigoHis, (Integer) map.get("codigoFecharMetaDetalhada"));
                            }
                        }
                    } catch (Exception e) {
                        Uteis.logar(e, HistoricoContato.class);
                    }
                }
            }
        }
    }

    @Override
    public void inicializarDadosRealizacaoContato(HistoricoContatoVO historicoContatoVO, AgendaVO agenda, FecharMetaDetalhadoVO obj, UsuarioVO usuarioLogado, UsuarioVO colaboradorResponsavel, MalaDiretaVO malaDiretaVO, MalaDiretaEnviadaVO malaDiretaEnviadaVO, List<TelefoneVO> listatelfoneClientePotencial, List listaTelefoneIndicado, Boolean reagendamento) throws Exception {
        historicoContatoVO.setCodigoFecharMetaDetalhado(obj.getCodigo());
        historicoContatoVO.setDiaAbertura(obj.getFecharMeta().getAberturaMetaVO().getDia());
        historicoContatoVO.setAberturaMetaEstaEmAberta(obj.getFecharMeta().getAberturaMetaVO().getMetaEmAberto());
        historicoContatoVO.setClienteVO(getFacade().getCliente().consultarPorChavePrimaria(obj.getCliente().getCodigo(), Uteis.NIVELMONTARDADOS_DADOSENTIDADESPRINCIPAIS));
        historicoContatoVO.setObservacao("");
        malaDiretaVO.setRemetente(usuarioLogado);
        historicoContatoVO.setNovoObj(true);
        historicoContatoVO.setColaboradorResponsavel(colaboradorResponsavel);
        historicoContatoVO.setResponsavelCadastro(usuarioLogado);
        historicoContatoVO.setFase(obj.getFecharMeta().getFase().getSigla());
        preencherHistoricoContatoComDadosCliente(historicoContatoVO);
        historicoContatoVO.getClienteVO().getPessoa().setTelefoneVOs(obj.getCliente().getPessoa().getTelefoneVOs());
    }

    public void gravarResposta(Integer codigoNotf, String resposta) throws Exception {
        HistoricoContatoVO historicoContatoVO = consultarPoCodigoNotificacao(codigoNotf,Uteis.NIVELMONTARDADOS_DADOSBASICOS);
        historicoContatoVO.setCodigo(0);
        historicoContatoVO.setDia(Calendario.hoje());
        historicoContatoVO.setResposta(resposta);
        incluir(historicoContatoVO);
    }

    @Override
    public List<HistoricoContatoVO> relatorioContatoApp(String filtroNomePessoa, String filtroResposta, String filtroMensagem, Date inicio, Date fim,
                                                        UsuarioVO responsavel, Integer malaDireta, Integer empresa) throws Exception {
        List<HistoricoContatoVO> lista = new ArrayList<HistoricoContatoVO>();
        StringBuilder sql = new StringBuilder();
        sql.append("SELECT cl.matricula, pes.nome, hc.dia, hc.maladireta, ml.mensagem, hc.resposta, cl.empresa  FROM historicocontato hc \n");
        sql.append(" INNER JOIN cliente cl ON cl.codigo = hc.cliente \n");
        sql.append(" INNER JOIN pessoa pes ON pes.codigo = cl.pessoa \n");
        sql.append(" INNER JOIN maladireta ml ON ml.codigo = hc.maladireta \n");
        sql.append(" WHERE hc.tipocontato LIKE 'AP' \n");
        if (!UteisValidacao.emptyString(filtroNomePessoa)) {
            sql.append(" AND lower(pes.nome) LIKE ? \n");
        }
        if (!UteisValidacao.emptyString(filtroResposta)) {
            sql.append(" AND lower(hc.resposta) LIKE ? \n");
        }
        if (!UteisValidacao.emptyString(filtroMensagem)) {
            sql.append(" AND lower(ml.mensagem) LIKE ? \n");
        }
        if (!UteisValidacao.emptyNumber(empresa)) {
            sql.append(" AND cl.empresa = ? \n");
        }
        if (responsavel != null && !UteisValidacao.emptyNumber(responsavel.getCodigo())) {
            sql.append(" AND ml.remetente = ? \n");
        }
        if (!UteisValidacao.emptyNumber(malaDireta)) {
            sql.append(" AND ml.codigo = ? \n");
        }
        if (inicio != null) {
            sql.append(" AND hc.dia >= ? ");
        }
        if (fim != null) {
            sql.append(" AND hc.dia <= ? ");
        }
        sql.append(" ORDER BY hc.dia DESC");
        try (PreparedStatement stm = con.prepareStatement(sql.toString())) {
            int i = 1;
            if (!UteisValidacao.emptyString(filtroNomePessoa)) {
                stm.setString(i++, "%" + filtroNomePessoa.toLowerCase() + "%");
            }
            if (!UteisValidacao.emptyString(filtroResposta)) {
                stm.setString(i++, "%" + filtroResposta.toLowerCase() + "%");
            }
            if (!UteisValidacao.emptyString(filtroMensagem)) {
                stm.setString(i++, "%" + filtroMensagem.toLowerCase() + "%");
            }
            if (!UteisValidacao.emptyNumber(empresa)) {
                stm.setInt(i++, empresa);
            }
            if (responsavel != null && !UteisValidacao.emptyNumber(responsavel.getCodigo())) {
                stm.setInt(i++, responsavel.getCodigo());
            }
            if (!UteisValidacao.emptyNumber(malaDireta)) {
                stm.setInt(i++, malaDireta);
            }
            if (inicio != null) {
                stm.setTimestamp(i++, Uteis.getDataJDBCTimestamp(Calendario.getDataComHoraZerada(inicio)));
            }
            if (fim != null) {
                stm.setTimestamp(i++, Uteis.getDataJDBCTimestamp(Calendario.getDataComHora(fim, "23:59")));
            }

            try (ResultSet rs = stm.executeQuery()) {
                while (rs.next()) {
                    HistoricoContatoVO obj = new HistoricoContatoVO();
                    obj.getClienteVO().getPessoa().setNome(rs.getString("nome"));
                    obj.getClienteVO().setMatricula(rs.getString("matricula"));
                    obj.getClienteVO().getEmpresa().setCodigo(rs.getInt("empresa"));
                    obj.getMalaDiretaVO().setCodigo(rs.getInt("maladireta"));
                    obj.getMalaDiretaVO().setMensagem(rs.getString("mensagem"));
                    obj.setDia(rs.getTimestamp("dia"));
                    obj.setResposta(rs.getString("resposta"));
                    lista.add(obj);
                }
            }
        }
        return lista;
    }

    @Override
    public List<HistoricoContatoVO> consultarHistoricoContatoVO(FecharMetaDetalhadoVO fecharMetaDetalhadoVO, boolean controlarAcesso, Integer limit, Integer offset) throws Exception {
        consultarCRM(getIdEntidade(), controlarAcesso);

        StringBuilder sql = new StringBuilder();
        sql.append("SELECT \n");
        sql.append(" hc.codigo, \n");
        sql.append(" hc.maladireta, \n");
        sql.append(" hc.cliente, \n");
        sql.append(" hc.passivo, \n");
        sql.append(" hc.indicado, \n");
        sql.append(" hc.dia, \n");
        sql.append(" hc.fase, \n");
        sql.append(" hc.resultado, \n");
        sql.append(" hc.tipocontato, \n");
        sql.append(" hc.contatoavulso, \n");
        sql.append(" hc.resposta, \n");
        sql.append(" hc.observacao, \n");
        sql.append(" u.nome as responsavelCadastro, \n");
        sql.append(" hc.objecao \n");
        sql.append(" FROM historicocontato hc \n");
        sql.append(" LEFT JOIN usuario u ON u.codigo = hc.responsavelCadastro \n");
        sql.append(" WHERE \n");

        if (fecharMetaDetalhadoVO.getCliente().getCodigo() != 0) {
            sql.append(" hc.cliente = ").append(fecharMetaDetalhadoVO.getCliente().getCodigo()).append(" \n");
        } else if (fecharMetaDetalhadoVO.getPassivo().getCodigo() != 0) {
            sql.append(" hc.passivo = ").append(fecharMetaDetalhadoVO.getPassivo().getCodigo()).append(" \n");
        } else if (fecharMetaDetalhadoVO.getIndicado().getCodigo() != 0) {
            sql.append(" hc.indicado = ").append(fecharMetaDetalhadoVO.getIndicado().getCodigo()).append(" \n");
        }
        sql.append(" ORDER BY hc.dia DESC LIMIT ").append(limit).append(" OFFSET ").append(offset);

        List<HistoricoContatoVO> resultado;
        try (Statement stm = con.createStatement()) {
            try (ResultSet dadosSQL = stm.executeQuery(sql.toString())) {

                resultado = new ArrayList<HistoricoContatoVO>();
                while (dadosSQL.next()) {
                    HistoricoContatoVO obj = new HistoricoContatoVO();
                    obj.setCodigo(new Integer(dadosSQL.getInt("codigo")));
                    obj.getMalaDiretaVO().setCodigo(new Integer(dadosSQL.getInt("maladireta")));
                    obj.getClienteVO().setCodigo(new Integer(dadosSQL.getInt("cliente")));
                    obj.getPassivoVO().setCodigo(new Integer(dadosSQL.getInt("passivo")));
                    obj.getIndicadoVO().setCodigo(new Integer(dadosSQL.getInt("indicado")));
                    obj.setDia(dadosSQL.getTimestamp("dia"));
                    obj.setFase(dadosSQL.getString("fase"));
                    obj.setResultado(dadosSQL.getString("resultado"));
                    obj.setTipoContato(dadosSQL.getString("tipoContato"));
                    obj.setContatoAvulso(dadosSQL.getBoolean("contatoavulso"));
                    obj.setResposta(dadosSQL.getString("resposta"));
                    obj.setObservacao(dadosSQL.getString("observacao"));
                    obj.getResponsavelCadastro().setNome(Uteis.getNomeAbreviado(dadosSQL.getString("responsavelCadastro")));
                    obj.getObjecaoVO().setCodigo(dadosSQL.getInt("objecao"));
                    obj.setNovoObj(false);
                    resultado.add(obj);
                }
            }
        }
        return resultado;
    }

    public List<HistoricoContatoVO> consultarHistoricoContatoVO(FecharMetaDetalhadoVO fecharMetaDetalhadoVO, boolean controlarAcesso) throws Exception {
        consultarCRM(getIdEntidade(), controlarAcesso);

        StringBuilder sql = new StringBuilder();
        sql.append("SELECT \n");
        sql.append(" hc.codigo, \n");
        sql.append(" hc.maladireta, \n");
        sql.append(" hc.cliente, \n");
        sql.append(" hc.passivo, \n");
        sql.append(" hc.indicado, \n");
        sql.append(" hc.dia, \n");
        sql.append(" hc.fase, \n");
        sql.append(" hc.resultado, \n");
        sql.append(" hc.tipocontato, \n");
        sql.append(" hc.contatoavulso, \n");
        sql.append(" hc.resposta, \n");
        sql.append(" hc.observacao, \n");
        sql.append(" u.nome as responsavelCadastro, \n");
        sql.append(" hc.objecao \n");
        sql.append(" FROM historicocontato hc \n");
        sql.append(" LEFT JOIN usuario u ON u.codigo = hc.responsavelCadastro \n");
        sql.append(" WHERE \n");

        if (fecharMetaDetalhadoVO.getCliente().getCodigo() != 0) {
            sql.append(" hc.cliente = ").append(fecharMetaDetalhadoVO.getCliente().getCodigo()).append(" \n");
        } else if (fecharMetaDetalhadoVO.getPassivo().getCodigo() != 0) {
            sql.append(" hc.passivo = ").append(fecharMetaDetalhadoVO.getPassivo().getCodigo()).append(" \n");
        } else if (fecharMetaDetalhadoVO.getIndicado().getCodigo() != 0) {
            sql.append(" hc.indicado = ").append(fecharMetaDetalhadoVO.getIndicado().getCodigo()).append(" \n");
        }

        sql.append(" ORDER BY hc.dia DESC");

        List<HistoricoContatoVO> resultado;
        try (Statement stm = con.createStatement()) {
            try (ResultSet dadosSQL = stm.executeQuery(sql.toString())) {

                resultado = new ArrayList<HistoricoContatoVO>();
                while (dadosSQL.next()) {
                    HistoricoContatoVO obj = new HistoricoContatoVO();
                    obj.setCodigo(new Integer(dadosSQL.getInt("codigo")));
                    obj.getMalaDiretaVO().setCodigo(new Integer(dadosSQL.getInt("maladireta")));
                    obj.getClienteVO().setCodigo(new Integer(dadosSQL.getInt("cliente")));
                    obj.getPassivoVO().setCodigo(new Integer(dadosSQL.getInt("passivo")));
                    obj.getIndicadoVO().setCodigo(new Integer(dadosSQL.getInt("indicado")));
                    obj.setDia(dadosSQL.getTimestamp("dia"));
                    obj.setFase(dadosSQL.getString("fase"));
                    obj.setResultado(dadosSQL.getString("resultado"));
                    obj.setTipoContato(dadosSQL.getString("tipoContato"));
                    obj.setContatoAvulso(dadosSQL.getBoolean("contatoavulso"));
                    obj.setResposta(dadosSQL.getString("resposta"));
                    obj.setObservacao(dadosSQL.getString("observacao"));
                    obj.getResponsavelCadastro().setNome(Uteis.getNomeAbreviado(dadosSQL.getString("responsavelCadastro")));
                    obj.getObjecaoVO().setCodigo(dadosSQL.getInt("objecao"));
                    obj.setNovoObj(false);
                    resultado.add(obj);
                }
            }
        }
        return resultado;
    }


    public Integer countHistoricoContatoVO(FecharMetaDetalhadoVO fecharMetaDetalhadoVO, boolean controlarAcesso) throws Exception {
        consultarCRM(getIdEntidade(), controlarAcesso);
        StringBuilder sql = new StringBuilder();
        sql.append("SELECT COUNT(*) \n");
        sql.append(" FROM historicocontato hc \n");
        sql.append(" LEFT JOIN usuario u ON u.codigo = hc.responsavelCadastro \n");
        sql.append(" WHERE \n");

        if (fecharMetaDetalhadoVO.getCliente().getCodigo() != 0) {
            sql.append(" hc.cliente = ").append(fecharMetaDetalhadoVO.getCliente().getCodigo()).append(" \n");
        } else if (fecharMetaDetalhadoVO.getPassivo().getCodigo() != 0) {
            sql.append(" hc.passivo = ").append(fecharMetaDetalhadoVO.getPassivo().getCodigo()).append(" \n");
        } else if (fecharMetaDetalhadoVO.getIndicado().getCodigo() != 0) {
            sql.append(" hc.indicado = ").append(fecharMetaDetalhadoVO.getIndicado().getCodigo()).append(" \n");
        }

        Integer totalLinhas;
        try (Statement stm = con.createStatement()) {
            totalLinhas = 0;
            try (ResultSet tabelaResultado = stm.executeQuery(sql.toString())) {
                if (tabelaResultado.next())
                    totalLinhas = tabelaResultado.getInt(1);
            }
        }
        return totalLinhas;
    }

    public void preencherHistoricoContato(HistoricoContatoVO historicoContatoVO) throws Exception {
        historicoContatoVO.setDiasUltAcesso(consultarUltimoAcessoCliente(historicoContatoVO));
        historicoContatoVO.setVencimentoContrato(obterVencimentoUltimoContrato(historicoContatoVO.getClienteVO().getCodigo()));
        historicoContatoVO.setTotalLigacao(consultarQuantEnviadoGenerico(historicoContatoVO, "TE"));
        historicoContatoVO.setQtdEmail(consultarQuantEnviadoGenerico(historicoContatoVO, "EM"));
        historicoContatoVO.setQtdPessoal(consultarQuantEnviadoGenerico(historicoContatoVO, "PE"));
        historicoContatoVO.setQtdSMS(consultarQuantEnviadoGenerico(historicoContatoVO, "CS"));
        historicoContatoVO.setQtdAPP(consultarQuantEnviadoGenerico(historicoContatoVO, "AP").intValue());
    }



    public HistoricoContatoVO executarInsercaoHistoricoIndicado(String agendaOuObjecao, HistoricoContatoVO hist) throws Exception {
        if (agendaOuObjecao.equals("AG")) {
            hist.setObjecaoVO(new ObjecaoVO());
            hist.setResultado(hist.getAgendaVO().qualResultadoAgendamento());
            getFacade().getAgenda().preencherDadosAgendaIndicado(hist.getAgendaVO(), hist);
        } else {
            hist.setResultado(hist.getObjecaoVO().qualResultadoObjecao());
        }
        incluirSemCommit(hist);
        return hist;
    }

    public Boolean existeHistoricoDataExpecifica(FecharMetaDetalhadoVO fecharMetaDetalhadoVO) throws Exception {
        consultar(getIdEntidade(), false);

        Date data = fecharMetaDetalhadoVO.getFecharMeta().getDataRegistro();
        Integer cliente = fecharMetaDetalhadoVO.getCliente().getCodigo();
        Integer passivo = fecharMetaDetalhadoVO.getPassivo().getCodigo();
        Integer indicado = fecharMetaDetalhadoVO.getIndicado().getCodigo();

        StringBuilder sql = new StringBuilder();
        sql.append("SELECT exists(SELECT * \n");
        sql.append("FROM historicocontato  \n");
        sql.append("WHERE ( cliente = " + cliente + " \n");
        sql.append("OR passivo = " + passivo + " \n");
        sql.append("OR indicado = " + indicado + ") \n");
        sql.append("AND dia::DATE = '" + data + "') \n");
        sql.append("as existe");
        try (PreparedStatement pst = con.prepareStatement(sql.toString())) {
            try (ResultSet tabelaResultado = pst.executeQuery()) {
                tabelaResultado.next();
                return tabelaResultado.getBoolean("existe");
            }
        }
    }

    public Long consultarQuantEnviadoGenerico(HistoricoContatoVO obj, String tipo) throws Exception {
        Integer cliente = obj.getClienteVO().getCodigo();
        Integer passivo = obj.getPassivoVO().getCodigo();
        Integer indicado = obj.getIndicadoVO().getCodigo();

        StringBuilder sql = new StringBuilder();
        sql.append("SELECT count (historicoContato.codigo) as qtd FROM historicoContato \n");
        if (cliente != 0) {
            sql.append("WHERE cliente = " + cliente + " \n");
        } else if (passivo != 0) {
            sql.append("WHERE passivo = " + passivo + " \n");
        } else if (indicado != 0) {
            sql.append("WHERE indicado = " + indicado + " \n");
        }
        sql.append("AND tipoContato = '" + tipo + "'");
        Long retorno;
        try (PreparedStatement pst = con.prepareStatement(sql.toString())) {
            try (ResultSet tabelaResultado = pst.executeQuery()) {
                retorno = null;
                if (tabelaResultado.next()) {
                    retorno = new Long(tabelaResultado.getLong("qtd"));
                }
            }
        }
        return retorno;
    }
    public void excluirHistoricoContatoDeConvite(Integer codigoConvite, Integer codigoAgenda) throws Exception{
        String sql = "delete from historicoContato where agenda = ? and conviteAulaExperimental = ?";
        try (PreparedStatement pst = con.prepareStatement(sql)) {
            pst.setInt(1, codigoAgenda);
            pst.setInt(2, codigoConvite);
            pst.execute();
        }
    }

    public Date consultarUltimoAcessoCliente(HistoricoContatoVO obj) throws Exception {
        Integer cliente = obj.getClienteVO().getCodigo();

        StringBuilder sql = new StringBuilder();
        sql.append("SELECT max(dthrentrada) as ultimoAcesso \n");
        sql.append("FROM acessoCliente \n");
        sql.append("WHERE cliente = " + cliente);
        try (PreparedStatement pst = con.prepareStatement(sql.toString())) {
            try (ResultSet tabelaResultado = pst.executeQuery()) {
                if (!tabelaResultado.next()) {
                    return null;
                }
                return tabelaResultado.getTimestamp("ultimoAcesso");
            }
        }
    }

    public void inserirHistoricoConfirmarAgendamento(AgendaVO agendaVO, String fase, boolean excluir) {
        try {
            String observacao = "*** AGENDAMENTO CONFIRMADO ***";
            if (excluir) {
                StringBuilder sql = new StringBuilder();
                sql.append("delete from historicocontato where observacao = ? and agenda = ? ");
                try (PreparedStatement pst = con.prepareStatement(sql.toString())) {
                    pst.setString(1, observacao);
                    pst.setInt(2, agendaVO.getCodigo());
                    pst.execute();
                }

            } else {
                HistoricoContatoVO hist = new HistoricoContatoVO();
                hist.setDia(Calendario.hoje());
                hist.setClienteVO(agendaVO.getCliente());
                hist.setPassivoVO(agendaVO.getPassivo());
                hist.setIndicadoVO(agendaVO.getIndicado());
                hist.setObservacao(observacao);
                hist.setResponsavelCadastro(agendaVO.getResponsavelComparecimento());
                hist.setAgendaVO(agendaVO);
                hist.setFase(fase);
                hist.setResultado("Simples Registro");
                hist.setContatoAvulso(false);
                incluir(hist);
            }

        } catch (Exception ignored) {
        }
    }

    public List<HistoricoContatoVO> consultarTelaCliente(Integer codigoCliente, Integer limit)throws Exception{
        List<HistoricoContatoVO> lista;
        try (ResultSet rs = criarConsulta("select hc.codigo,hc.observacao, hc.dia, resultado, tipocontato, u.nome as usuario from historicocontato hc "
                + " INNER JOIN usuario u ON u.codigo = hc.responsavelcadastro "
                + " WHERE hc.cliente = " + codigoCliente
                + " ORDER BY hc.dia DESC"
                + (UteisValidacao.emptyNumber(limit) ? "" : " LIMIT " + limit), con)) {
            lista = new ArrayList<HistoricoContatoVO>();
            while (rs.next()) {
                HistoricoContatoVO historico = new HistoricoContatoVO();
                historico.setCodigo(rs.getInt("codigo"));
                historico.setDia(rs.getTimestamp("dia"));
                historico.setResultado(rs.getString("resultado"));
                historico.setTipoContato(rs.getString("tipocontato"));
                historico.setResponsavelCadastro(new UsuarioVO());
                historico.getResponsavelCadastro().setNome(rs.getString("usuario"));
                historico.setObservacao(rs.getString("observacao"));
                lista.add(historico);
            }
        }
        return lista;
    }

    private static void montarImagemMalaDireta(HistoricoContatoVO obj, int nivelMontarDados, Connection con) throws Exception {
        try (ResultSet rs = criarConsulta("select modelomensagem from maladireta where codigo  = " + obj.getMalaDiretaVO().getCodigo(), con)) {
            if (rs.next() && !UteisValidacao.emptyNumber(rs.getInt("modelomensagem"))) {
                obj.getMalaDiretaVO().setModeloMensagem(new ModeloMensagem(con).consultarPorChavePrimaria(rs.getInt("modelomensagem"), Uteis.NIVELMONTARDADOS_DADOSBASICOS));
                final String chave = DAO.resolveKeyFromConnection(con);
                obj.getMalaDiretaVO().getModeloMensagem().verificarSeExisteImagemModelo(false, chave);
            }
        }

    }
    public Integer obterTotalContatosPeriodo(int codigoEmpresa,Date inicio,Date fim) throws Exception{
        StringBuilder sql = new StringBuilder();
        sql.append("Select COUNT(*) as count from historicoContato hc\n");
        sql.append("INNER JOIN Cliente cli ON cli.codigo = hc.cliente\n");
        sql.append("WHERE  hc.dia between ? and ? and empresa  = ?");
        try (PreparedStatement ps = con.prepareStatement(sql.toString())) {
            int i = 1;
            ps.setDate(i++, Uteis.getDataJDBC(inicio));
            ps.setDate(i++, Uteis.getDataJDBC(fim));
            ps.setInt(i++, codigoEmpresa);
            try (ResultSet rs = ps.executeQuery()) {
                if (rs.next()) {
                    return rs.getInt("count");
                } else {
                    return 0;
                }
            }
        }
    }

    private void incluirObjecaoDefinitiva(HistoricoContatoVO obj) throws Exception {
        if (!UteisValidacao.emptyNumber(obj.getObjecaoVO().getCodigo())) {
            if (!UteisValidacao.emptyNumber(obj.getClienteVO().getCodigo())) {
                obj.getClienteVO().setObjecao(obj.getObjecaoVO());
                getFacade().getCliente().alterarObjecaoCliente(obj.getClienteVO(), obj.getUsuarioVO(), false);
            } else if (!UteisValidacao.emptyNumber(obj.getPassivoVO().getCodigo())) {
                getFacade().getPassivo().alterarObjecaoPassivo(obj.getObjecaoVO().getCodigo(), obj.getPassivoVO().getCodigo());
            } else if (!UteisValidacao.emptyNumber(obj.getIndicadoVO().getCodigo())) {
                getFacade().getIndicado().alterarObjecaoIndicado(obj.getObjecaoVO().getCodigo(), obj.getIndicadoVO().getCodigo());
            }
        }
    }

    public void alterarTodasObjecoes(Integer antigaObjecao, Integer novaObjecao) throws Exception {
        executarConsulta("UPDATE historicocontato SET objecao = " + novaObjecao + " WHERE objecao = " + antigaObjecao, con);
    }

    @Override
    public HistoricoContatoVO consultarPoCodigoNotificacao(Integer codigoNotificacao, int nivelMontarDados) throws Exception {
        String sqlStr = "SELECT * FROM HistoricoContato where codigoNotificacao = " + codigoNotificacao + " ORDER BY codigo";
        try (PreparedStatement sqlConsultar = con.prepareStatement(sqlStr)) {
            try (ResultSet tabelaResultado = sqlConsultar.executeQuery()) {
                if (!tabelaResultado.next()) {
                    return new HistoricoContatoVO();
                }
                return (montarDados(tabelaResultado, nivelMontarDados, con));
            }
        }
    }

    public void incluirHistoricoContatoConversaoCliente(ClienteVO clienteVO) {
        try {
            HistoricoContatoVO hist = new HistoricoContatoVO();
            hist.setClienteVO(clienteVO);

            if (clienteVO.getCodigoIndicado() != 0) {
                hist.setResultado("INDICAÇÃO-CRM");

                IndicadoVO indicadoVO = getFacade().getIndicado().consultarPorChavePrimaria(clienteVO.getCodigoIndicado(), Uteis.NIVELMONTARDADOS_HISTORICOINDICADO);
                hist.setDia(indicadoVO.getIndicacaoVO().getDia());
                hist.setResponsavelCadastro(indicadoVO.getIndicacaoVO().getResponsavelCadastro());
                hist.setObservacao(indicadoVO.getIndicacaoVO().getObservacao());
                incluirSemCommit(hist);
            } else if (clienteVO.getCodigoPassivo() != 0) {
                hist.setResultado("RECEPTIVO-CRM");

                PassivoVO passivoVO = getFacade().getPassivo().consultarPorChavePrimaria(clienteVO.getCodigoPassivo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS);
                hist.setDia(passivoVO.getDia());
                hist.setResponsavelCadastro(passivoVO.getResponsavelCadastro());
                HistoricoContatoVO historicoContatoVO = getFacade().getHistoricoContato().consultarHistoricoContatoPorCodigoPassivo(clienteVO.getCodigoPassivo(), false, Uteis.NIVELMONTARDADOS_HISTORICOPASSIVO);
                hist.setObservacao(historicoContatoVO.getObservacao());
                incluirSemCommit(hist);
            }
        } catch (Exception ignored) {
        }
    }

    public void atualizarHistoricoCadastroCliente(ClienteVO obj) throws Exception {
        if (!UteisValidacao.emptyNumber(obj.getCodigoPassivo())) {
            executarConsulta("UPDATE historicocontato SET cliente = " + obj.getCodigo() + " WHERE passivo = " + obj.getCodigoPassivo(), con);
        }
        if (!UteisValidacao.emptyNumber(obj.getCodigoIndicado())) {
            executarConsulta("UPDATE historicocontato SET cliente = " + obj.getCodigo() + " WHERE indicado = " + obj.getCodigoIndicado(), con);
        }
    }

    public static void montarDadosObjecao(HistoricoContatoVO obj, int nivelMontarDados, Connection con) throws Exception {
        if (UteisValidacao.emptyNumber(obj.getObjecaoVO().getCodigo())) {
            obj.setObjecaoVO(new ObjecaoVO());
            return;
        }
        Objecao objecaoDAO = new Objecao(con);
        obj.setObjecaoVO(objecaoDAO.consultarPorChavePrimaria(obj.getObjecaoVO().getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS));
        objecaoDAO = null;
    }

    public void marcarFecharDetalhadoAtingido(HistoricoContatoVO obj) throws Exception {
        FecharMetaDetalhadoVO fecharMetaDetalhado = getFacade().getFecharMetaDetalhado().consultarPorChavePrimaria(obj.getCodigoFecharMetaDetalhado(), Uteis.NIVELMONTARDADOS_DADOSENTIDADESPRINCIPAIS);
        boolean repescagem = !Calendario.igual(Calendario.hoje(), fecharMetaDetalhado.getFecharMeta().getDataRegistro());
        if (!fecharMetaDetalhado.isObteveSucesso()) {
            if (repescagem) {
                executarConsulta("UPDATE fecharmetadetalhado SET teveContato = true, repescagem = TRUE WHERE codigo = " + obj.getCodigoFecharMetaDetalhado(), con);
                executarConsulta("UPDATE fecharmeta SET repescagem = repescagem + 1 WHERE codigo in (select fecharmeta from fecharmetadetalhado where codigo = "+obj.getCodigoFecharMetaDetalhado()+" )", con);
            }
            executarConsulta("UPDATE fecharmetadetalhado SET teveContato = true, obtevesucesso = TRUE WHERE codigo = " + obj.getCodigoFecharMetaDetalhado(), con);
            executarConsulta("UPDATE fecharmeta SET metaatingida = metaatingida + 1 WHERE codigo in (select fecharmeta from fecharmetadetalhado where codigo = "+obj.getCodigoFecharMetaDetalhado()+" )", con);
        }
    }
}

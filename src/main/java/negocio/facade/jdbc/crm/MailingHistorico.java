package negocio.facade.jdbc.crm;

import br.com.pactosolucoes.enumeradores.StatusEnvioMailingEnum;
import br.com.pactosolucoes.estrutura.paginacao.ConfPaginacao;
import negocio.comuns.crm.MailingHistoricoVO;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisValidacao;
import negocio.facade.jdbc.arquitetura.SuperEntidade;
import negocio.interfaces.crm.MailingHistoricoInterfaceFacade;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

public class MailingHistorico extends SuperEntidade implements MailingHistoricoInterfaceFacade {

    public static final int SMS_DIA = 2000;

    public MailingHistorico() throws Exception {
        super();
    }

    public MailingHistorico(Connection con) throws Exception {
        super(con);
    }

    @Override
    public List<MailingHistoricoVO> consultar(Integer malaDireta) throws Exception {
        ResultSet dados = con.prepareStatement("SELECT * FROM mailingHistorico WHERE maladireta = " + malaDireta + " ORDER BY datafim desc").executeQuery();
        return montarDadosConsulta(dados, con);
    }

    @Override
    public void excluir(MailingHistoricoVO obj) throws Exception {
        con.prepareStatement("DELETE FROM mailingHistorico WHERE codigo = " + obj.getCodigo()).execute();
    }

    @Override
    public void incluir(MailingHistoricoVO obj) throws Exception {

        StringBuilder sql = new StringBuilder();
        Integer nrAmostraDestinatarios = 0;
        String filtro = obj.getFiltro();
        PreparedStatement stm = null;
        if(filtro != null){
            filtro = filtro.replaceAll("distinct   distinct","distinct");
            stm = con.prepareStatement(filtro);
            ResultSet rs = stm.executeQuery();
            while (rs.next()){
                nrAmostraDestinatarios++;
            }
        }

        sql.append(" INSERT INTO mailingHistorico (maladireta, datainicio, datafim, filtro, registrosafetados, pessoasafetadas, " //6
                + "log, status, clientesenviados, clientesnaoenviados, saldo, numeroamostradestinatarios)");
        sql.append(" VALUES (?,?,?,?,?,?,?,?,?,?,?,?)");
        stm = con.prepareStatement(sql.toString());

        int i = 0;
        stm.setInt(++i, obj.getMalaDireta());
        stm.setTimestamp(++i, Uteis.getDataJDBCTimestamp(obj.getDataInicio()));
        stm.setTimestamp(++i, Uteis.getDataJDBCTimestamp(obj.getDataFim()));
        stm.setString(++i, obj.getFiltro());
        if (obj.getRegistrosAfetados() != null && obj.getRegistrosAfetados() > 0) {
            stm.setInt(++i, obj.getRegistrosAfetados());
        } else {
            stm.setNull(++i, 0);
        }
        if (obj.getPessoasAfetadas() != null && obj.getPessoasAfetadas() > 0) {
            stm.setInt(++i, obj.getPessoasAfetadas());
        } else {
            stm.setNull(++i, 0);
        }
        stm.setString(++i, obj.getLog());
        stm.setInt(++i, obj.getStatus().getCodigo());
        stm.setString(++i, obj.getClientesEnviados());
        stm.setString(++i, obj.getClientesNaoEnviados());
        if (obj.getSaldo() != null) {
            stm.setInt(++i, obj.getSaldo());
        } else {
            stm.setNull(++i, 0);
        }
        stm.setInt(++i, nrAmostraDestinatarios);

        stm.execute();
    }

    public static List<MailingHistoricoVO> montarDadosConsulta(ResultSet tabelaResultado, Connection con) throws Exception {
        List<MailingHistoricoVO> vetResultado = new ArrayList<MailingHistoricoVO>();
        while (tabelaResultado.next()) {
            MailingHistoricoVO obj = montarDados(tabelaResultado, con);
            vetResultado.add(obj);
        }
        tabelaResultado = null;
        return vetResultado;
    }

    public static MailingHistoricoVO montarDados(ResultSet dados, Connection con) throws Exception {
        MailingHistoricoVO obj = new MailingHistoricoVO();
        obj.setCodigo(dados.getInt("codigo"));
        obj.setMalaDireta(dados.getInt("maladireta"));
        obj.setDataFim(dados.getTimestamp("datafim"));
        obj.setDataInicio(dados.getTimestamp("datainicio"));
        obj.setFiltro(dados.getString("filtro"));
        obj.setRegistrosAfetados(dados.getInt("registrosafetados"));
        obj.setPessoasAfetadas(dados.getInt("pessoasafetadas"));
        obj.setLog(dados.getString("log"));
        obj.setStatus(StatusEnvioMailingEnum.getStatus(dados.getInt("status")));
        obj.setClientesEnviados(dados.getString("clientesenviados"));
        obj.setClientesNaoEnviados(dados.getString("clientesnaoenviados"));
        obj.setNrEnviados(contar(dados.getString("clientesenviados")));
        obj.setNrNaoEnviados(contarNaoEnviados(dados.getString("clientesnaoenviados")));
        obj.setNrAmostraDestinatarios(dados.getInt("numeroamostradestinatarios"));
        return obj;

    }

    private static int contarNaoEnviados(String codigos) {
        if (UteisValidacao.emptyString(codigos)) {
            return 0;
        }
        int cont = 0;
        String[] pessoasEerros = codigos.split(",");
        for(String s : pessoasEerros){
            String[] codigoEerro = s.split("\\|");
            if(codigoEerro.length == 2 && UteisValidacao.somenteNumeros(codigoEerro[0]) && Integer.parseInt(codigoEerro[0]) > 0){
                cont++;
            }
        }
        return cont;
    }
    private static int contar(String codigos) {
        if (UteisValidacao.emptyString(codigos)) {
            return 0;
        }
        int cont = 0;
        for (int i = 0; i < codigos.length(); i++) {
            if (codigos.charAt(i) == ',') {
                cont++;
            }
        }
        return cont + 1;
    }


    public List<MailingHistoricoVO> consultarPaginado(Integer malaDireta, ConfPaginacao confPaginacao) throws Exception {
        //1- CONFIGURANDO A NAVEGACAO DA PAGINACAO
        confPaginacao.configurarNavegacao();

        //sql principal
        StringBuffer sqlStr = new StringBuffer().append(" SELECT * FROM mailingHistorico WHERE maladireta = " + malaDireta + " ORDER BY datafim desc");

        //sql inner joins

        //2 - CONFIGURA A INICIALIZACAO DA PAGINACAO PARA ESTA DAO.
        confPaginacao.iniciarPaginacao(this);


        //3 - ADICIONA PAGINACAO NA CONSULTA
        confPaginacao.addPaginacao(sqlStr);


        //4 - REALIZA A CONSULTA COM PAGINACAO
        ResultSet tabelaResultado = confPaginacao.consultaPaginada();

        return (montarDadosConsulta(tabelaResultado, con));
    }


    public int consultarSaldoPorEmpresa(Integer codEmpresa, Date dataConsulta) throws Exception {
        String sqlConsulta = "SELECT\n" +
                "  saldo\n" +
                "FROM mailinghistorico mh\n" +
                "  INNER JOIN maladireta md ON mh.maladireta = md.codigo\n" +
                "WHERE mh.saldo IS NOT NULL\n" +
                "      AND mh.datainicio BETWEEN '" + Uteis.getDataJDBC(dataConsulta) + " 00:00:00' AND '" + Uteis.getDataJDBC(dataConsulta) + " 23:59:59'\n" +
                "      AND md.empresa = " + codEmpresa + "\n" +
                "ORDER BY mh.codigo DESC;";

        PreparedStatement ps = getCon().prepareStatement(sqlConsulta);
        ResultSet rs = ps.executeQuery();
        if (rs.next()) {
            return rs.getInt("saldo");
        }
        return SMS_DIA;
    }
}

package negocio.facade.jdbc.crm;

public class ConsultasFasesCRM {
	
	
	public static final String ligacaoAgendadosAmanha = "select %s  from " +
                            " (select min(codigo) as codigoagenda, cliente,passivo,indicado from agenda where dataagendamento = ? "
                + " AND coalesce(colaboradorResponsavel,responsavelcadastro) = ? AND tipoagendamento NOT LIKE 'LI' AND empresa = ? group by 2,3,4) as auxiliar inner join agenda on agenda.codigo = auxiliar.codigoagenda";

}

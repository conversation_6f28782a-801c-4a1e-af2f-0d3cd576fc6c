package negocio.facade.jdbc.crm;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.Statement;
import java.util.ArrayList;
import java.util.Iterator;
import java.util.List;

import negocio.comuns.basico.ClienteVO;
import negocio.comuns.crm.IndicadoVO;
import negocio.comuns.crm.MalaDiretaEnviadaVO;
import negocio.comuns.crm.PassivoVO;
import negocio.comuns.utilitarias.ConsistirException;
import negocio.comuns.utilitarias.Uteis;
import negocio.facade.jdbc.arquitetura.SuperEntidade;
import negocio.facade.jdbc.basico.Cliente;

/**
 * Classe de persistência que encapsula todas as operações de manipulação dos dados da classe <code>MalaDiretaEnviadaVO</code>.
 * Responsável por implementar operações como incluir, alterar, excluir e consultar pertinentes a classe <code>MalaDiretaEnviadaVO</code>.
 * Encapsula toda a interação com o banco de dados.
 * @see MalaDiretaEnviadaVO
 * @see SuperEntidade
 * @see MalaDireta
*/
public class MalaDiretaEnviada extends SuperEntidade {    
	
    public MalaDiretaEnviada() throws Exception {
        super();
        setIdEntidade("MalaDireta");
    }
    
    public MalaDiretaEnviada(Connection con) throws Exception {
        super(con);
        setIdEntidade("MalaDireta");
    }
	
    /**
     * Operação responsável por retornar um novo objeto da classe <code>MalaDiretaEnviadaVO</code>.
    */
    public MalaDiretaEnviadaVO novo() throws Exception {
        incluirCRM(getIdEntidade());
        MalaDiretaEnviadaVO obj = new MalaDiretaEnviadaVO();
        return obj;
    }

    /**
     * Operação responsável por incluir no banco de dados um objeto da classe <code>MalaDiretaEnviadaVO</code>.
     * Primeiramente valida os dados (<code>validarDados</code>) do objeto. Verifica a conexão com o banco de dados e a permissão do usuário
     * para realizar esta operacão na entidade.
     * Isto, através da operação <code>incluir</code> da superclasse.
     * @param obj  Objeto da classe <code>MalaDiretaEnviadaVO</code> que será gravado no banco de dados.
     * @exception Exception Caso haja problemas de conexão, restrição de acesso ou validação de dados.
    */
    public void incluir(MalaDiretaEnviadaVO obj) throws Exception {
        MalaDiretaEnviadaVO.validarDados(obj);
        obj.realizarUpperCaseDados();
        String sql = "INSERT INTO maladiretaenviada( maladireta, cliente, passivo, indicado ) VALUES ( ?, ?, ?, ? )";
        PreparedStatement sqlInserir = con.prepareStatement(sql);
        if (obj.getMalaDiretaVO().getCodigo().intValue() != 0) {
            sqlInserir.setInt(1, obj.getMalaDiretaVO().getCodigo().intValue() );
        } else {
            sqlInserir.setNull(1, 0);
        }
        if (obj.getClienteVO().getCodigo().intValue() != 0) {
            sqlInserir.setInt(2, obj.getClienteVO().getCodigo().intValue() );
        } else {
            sqlInserir.setNull(2, 0);
        }
        if (obj.getPassivoVO().getCodigo().intValue() != 0) {
        	sqlInserir.setInt(3, obj.getPassivoVO().getCodigo().intValue() );
        } else {
        	sqlInserir.setNull(3, 0);
        }
        if (obj.getIndicadoVO().getCodigo().intValue() != 0) {
        	sqlInserir.setInt(4, obj.getIndicadoVO().getCodigo().intValue() );
        } else {
        	sqlInserir.setNull(4, 0);
        }
        sqlInserir.execute();
        obj.setCodigo(obterValorChavePrimariaCodigo());
        obj.setNovoObj(new Boolean(false));
    }

    /**
     * Operação responsável por alterar no BD os dados de um objeto da classe <code>MalaDiretaEnviadaVO</code>.
     * Sempre utiliza a chave primária da classe como atributo para localização do registro a ser alterado.
     * Primeiramente valida os dados (<code>validarDados</code>) do objeto. Verifica a conexão com o banco de dados e a permissão do usuário
     * para realizar esta operacão na entidade.
     * Isto, através da operação <code>alterar</code> da superclasse.
     * @param obj    Objeto da classe <code>MalaDiretaEnviadaVO</code> que será alterada no banco de dados.
     * @exception Execption Caso haja problemas de conexão, restrição de acesso ou validação de dados.
    */
    public void alterar(MalaDiretaEnviadaVO obj) throws Exception {
        MalaDiretaEnviadaVO.validarDados(obj);
        alterarCRM(getIdEntidade());
        obj.realizarUpperCaseDados();
        String sql = "UPDATE maladiretaenviada set maladireta=?, cliente=?, passivo=?, indicado=? WHERE ((codigo = ?))";
        PreparedStatement sqlAlterar = con.prepareStatement(sql);
        if (obj.getMalaDiretaVO().getCodigo().intValue() != 0) {
            sqlAlterar.setInt(1, obj.getMalaDiretaVO().getCodigo().intValue() );
        } else {
            sqlAlterar.setNull(1, 0);
        }
        if (obj.getClienteVO().getCodigo().intValue() != 0) {
            sqlAlterar.setInt(2, obj.getClienteVO().getCodigo().intValue() );
        } else {
            sqlAlterar.setNull(2, 0);
        }
        if (obj.getPassivoVO().getCodigo().intValue() != 0) {
        	sqlAlterar.setInt(3, obj.getPassivoVO().getCodigo().intValue() );
        } else {
        	sqlAlterar.setNull(3, 0);
        }
        if (obj.getIndicadoVO().getCodigo().intValue() != 0) {
        	sqlAlterar.setInt(4, obj.getIndicadoVO().getCodigo().intValue() );
        } else {
        	sqlAlterar.setNull(4, 0);
        }
        sqlAlterar.setInt( 5, obj.getCodigo().intValue() );
        sqlAlterar.execute();
    }

    /**
     * Operação responsável por excluir no BD um objeto da classe <code>MalaDiretaEnviadaVO</code>.
     * Sempre localiza o registro a ser excluído através da chave primária da entidade.
     * Primeiramente verifica a conexão com o banco de dados e a permissão do usuário
     * para realizar esta operacão na entidade.
     * Isto, através da operação <code>excluir</code> da superclasse.
     * @param obj    Objeto da classe <code>MalaDiretaEnviadaVO</code> que será removido no banco de dados.
     * @exception Execption Caso haja problemas de conexão ou restrição de acesso.
    */
    public void excluir(MalaDiretaEnviadaVO obj) throws Exception {
        excluirCRM(getIdEntidade());
        String sql = "DELETE FROM maladiretaenviada WHERE ((codigo = ?))";
        PreparedStatement sqlExcluir = con.prepareStatement(sql);
        sqlExcluir.setInt( 1, obj.getCodigo().intValue() );
        sqlExcluir.execute();
    }
    
    

    /**
     * Responsável por realizar uma consulta de <code>MalaDiretaEnviadaVO</code> através do valor do atributo 
     * <code>nome</code> da classe <code>Pessoa</code>
     * Faz uso da operação <code>montarDadosConsulta</code> que realiza o trabalho de prerarar o List resultante.
     * @return  List Contendo vários objetos da classe <code>MalaDiretaEnviadaVO</code> resultantes da consulta.
     * @exception Execption Caso haja problemas de conexão ou restrição de acesso.
    */
    public List consultarPorNomePessoa(String valorConsulta, int nivelMontarDados) throws Exception {
        consultarCRM(getIdEntidade(), true);
        String sqlStr = "SELECT maladiretaenviada.* FROM maladiretaenviada, Pessoa WHERE maladiretaenviada.pessoa = Pessoa.codigo and upper( Pessoa.nome ) like('" + valorConsulta.toUpperCase() + "%') ORDER BY Pessoa.nome";
        Statement stm = con.createStatement();
        ResultSet tabelaResultado = stm.executeQuery(sqlStr);
        return montarDadosConsulta(tabelaResultado, nivelMontarDados, con);
    }

    /**
     * Responsável por realizar uma consulta de <code>MalaDiretaEnviadaVO</code> através do valor do atributo 
     * <code>codigo</code> da classe <code>malaDiretaVO</code>
     * Faz uso da operação <code>montarDadosConsulta</code> que realiza o trabalho de prerarar o List resultante.
     * @return  List Contendo vários objetos da classe <code>MalaDiretaEnviadaVO</code> resultantes da consulta.
     * @exception Exception Caso haja problemas de conexão ou restrição de acesso.
    */
    public List consultarPorCodigoMalaDireta(Integer valorConsulta, int nivelMontarDados) throws Exception {
        consultarCRM(getIdEntidade(), true);
        String sqlStr = "SELECT maladiretaenviada.* FROM maladiretaenviada, maladireta WHERE maladiretaenviada.maladireta = maladireta.codigo and maladireta.codigo >= " + valorConsulta.intValue() + " ORDER BY maladireta.codigo";
        Statement stm = con.createStatement();
        ResultSet tabelaResultado = stm.executeQuery(sqlStr);
        return montarDadosConsulta(tabelaResultado, nivelMontarDados, con);
    }

    /**
     * Responsável por realizar uma consulta de <code>MalaDiretaEnviadaVO</code> através do valor do atributo 
     * <code>Integer codigo</code>. Retorna os objetos com valores iguais ou superiores ao parâmetro fornecido.
     * Faz uso da operação <code>montarDadosConsulta</code> que realiza o trabalho de prerarar o List resultante.
     * @param   controlarAcesso Indica se a aplicação deverá verificar se o usuário possui permissão para esta consulta ou não.
     * @return  List Contendo vários objetos da classe <code>MalaDiretaEnviadaVO</code> resultantes da consulta.
     * @exception Exception Caso haja problemas de conexão ou restrição de acesso.
     */
    public List consultarPorCodigo(Integer valorConsulta, boolean controlarAcesso, int nivelMontarDados) throws Exception {
        consultarCRM(getIdEntidade(), controlarAcesso);
        String sqlStr = "SELECT * FROM maladiretaenviada WHERE codigo >= " + valorConsulta.intValue() + " ORDER BY codigo";
        Statement stm = con.createStatement();
        ResultSet tabelaResultado = stm.executeQuery(sqlStr);
        return (montarDadosConsulta(tabelaResultado, nivelMontarDados, con));
    }

    /**
     * Responsável por montar os dados de vários objetos, resultantes de uma consulta ao banco de dados (<code>ResultSet</code>).
     * Faz uso da operação <code>montarDados</code> que realiza o trabalho para um objeto por vez.
     * @return  List Contendo vários objetos da classe <code>MalaDiretaEnviadaVO</code> resultantes da consulta.
    */
    public static List montarDadosConsulta(ResultSet tabelaResultado, int nivelMontarDados, Connection con) throws Exception {
        List vetResultado = new ArrayList();
        while (tabelaResultado.next()) {
            MalaDiretaEnviadaVO obj = new MalaDiretaEnviadaVO();
            obj = montarDados(tabelaResultado, nivelMontarDados, con);
            vetResultado.add(obj);
        }
        tabelaResultado = null;
        return vetResultado;
    }

    /**
     * Responsável por montar os dados resultantes de uma consulta ao banco de dados (<code>ResultSet</code>)
     * em um objeto da classe <code>MalaDiretaEnviadaVO</code>.
     * @return  O objeto da classe <code>MalaDiretaEnviadaVO</code> com os dados devidamente montados.
    */
    public static MalaDiretaEnviadaVO montarDados(ResultSet dadosSQL, int nivelMontarDados, Connection con) throws Exception {
        MalaDiretaEnviadaVO obj = new MalaDiretaEnviadaVO();
        obj.setCodigo( new Integer( dadosSQL.getInt("codigo")));
        obj.getMalaDiretaVO().setCodigo( new Integer( dadosSQL.getInt("maladireta")));
        obj.getClienteVO().setCodigo( new Integer( dadosSQL.getInt("cliente")));
        obj.getPassivoVO().setCodigo( new Integer( dadosSQL.getInt("passivo")));
        obj.getIndicadoVO().setCodigo( new Integer( dadosSQL.getInt("indicado")));
        obj.setNovoObj(new Boolean(false));
        if (nivelMontarDados == Uteis.NIVELMONTARDADOS_DADOSBASICOS) {
            return obj;
        }

        montarDadosCliente(obj, Uteis.NIVELMONTARDADOS_METAAGENDAMENTODETALHADA, con);
        montarDadosPassivo(obj, Uteis.NIVELMONTARDADOS_DADOSBASICOS, con);
        montarDadosIndicado(obj, Uteis.NIVELMONTARDADOS_DADOSBASICOS, con);
        return obj;
    }

    /**
     * Operação responsável por montar os dados de um objeto da classe <code>PessoaVO</code> relacionado ao objeto <code>MalaDiretaEnviadaVO</code>.
     * Faz uso da chave primária da classe <code>PessoaVO</code> para realizar a consulta.
     * @param obj  Objeto no qual será montado os dados consultados.
    */
    public static void montarDadosCliente(MalaDiretaEnviadaVO obj, int nivelMontarDados, Connection con) throws Exception {
        if (obj.getClienteVO().getCodigo().intValue() == 0) {
            obj.setClienteVO(new ClienteVO());
            return;
        }
        obj.setClienteVO(new Cliente(con).consultarPorChavePrimaria(obj.getClienteVO().getCodigo(), nivelMontarDados));
    }
    /**
     * Operação responsável por montar os dados de um objeto da classe <code>PessoaVO</code> relacionado ao objeto <code>MalaDiretaEnviadaVO</code>.
     * Faz uso da chave primária da classe <code>PessoaVO</code> para realizar a consulta.
     * @param obj  Objeto no qual será montado os dados consultados.
     */
    public static void montarDadosPassivo(MalaDiretaEnviadaVO obj, int nivelMontarDados, Connection con) throws Exception {
    	if (obj.getPassivoVO().getCodigo().intValue() == 0) {
    		obj.setPassivoVO(new PassivoVO());
    		return;
    	}
    	obj.setPassivoVO(new Passivo(con).consultarPorChavePrimaria(obj.getPassivoVO().getCodigo(), nivelMontarDados));
    }
    /**
     * Operação responsável por montar os dados de um objeto da classe <code>PessoaVO</code> relacionado ao objeto <code>MalaDiretaEnviadaVO</code>.
     * Faz uso da chave primária da classe <code>PessoaVO</code> para realizar a consulta.
     * @param obj  Objeto no qual será montado os dados consultados.
     */
    public static void montarDadosIndicado(MalaDiretaEnviadaVO obj, int nivelMontarDados, Connection con) throws Exception {
    	if (obj.getIndicadoVO().getCodigo().intValue() == 0) {
    		obj.setIndicadoVO(new IndicadoVO());
    		return;
    	}
    	obj.setIndicadoVO(new Indicado(con).consultarPorChavePrimaria(obj.getIndicadoVO().getCodigo(), nivelMontarDados));
    }

    /**
     * Operação responsável por excluir todos os objetos da <code>MalaDiretaEnviadaVO</code> no BD.
     * Faz uso da operação <code>excluir</code> disponível na classe <code>MalaDiretaEnviadaVO</code>.
     * @param <code>malaDiretaVO</code> campo chave para exclusão dos objetos no BD.
     * @exception Exception  Erro de conexão com o BD ou restrição de acesso a esta operação.
    */
    public void excluirMalaDiretaEnviadas( Integer codMalaDireta ) throws Exception {
        excluirCRM(getIdEntidade());
        String sql = "DELETE FROM maladiretaenviada WHERE (maladireta = ?)";
        PreparedStatement sqlExcluir = con.prepareStatement(sql);
        sqlExcluir.setInt( 1, codMalaDireta.intValue() );
        sqlExcluir.execute();
    }

    /**
     * Operação responsável por alterar todos os objetos da <code>MalaDiretaEnviadaVO</code> contidos em um Hashtable no BD.
     * Faz uso da operação <code>excluirMalaDiretaEnviadas</code> e <code>incluirMalaDiretaEnviadas</code> disponíveis na classe <code>MalaDiretaEnviadaVO</code>.
     * @param objetos  List com os objetos a serem alterados ou incluídos no BD.
     * @exception Exception  Erro de conexão com o BD ou restrição de acesso a esta operação.
    */
    public void alterarMalaDiretaEnviadas( Integer codMalaDireta, List objetos ) throws Exception {
        String str = "DELETE FROM maladiretaenviada WHERE maladireta = " + codMalaDireta;
        Iterator i = objetos.iterator();
        while (i.hasNext()) {
            MalaDiretaEnviadaVO objeto = (MalaDiretaEnviadaVO)i.next();
            str += " AND codigo <> " + objeto.getCodigo().intValue();
        }
        PreparedStatement sqlExcluir = con.prepareStatement(str);
        sqlExcluir.execute();
        Iterator e = objetos.iterator();
        while (e.hasNext()) {
            MalaDiretaEnviadaVO objeto = (MalaDiretaEnviadaVO)e.next();
            if (objeto.getCodigo().equals(0)) {
                incluir(objeto);
            } else {
                alterar(objeto);
            }
        }
    }

    /**
     * Operação responsável por incluir objetos da <code>MalaDiretaEnviadaVO</code> no BD.
     * Garantindo o relacionamento com a entidade principal <code>CRM.malaDiretaVO</code> através do atributo de vínculo.
     * @param objetos List contendo os objetos a serem gravados no BD da classe.
     * @exception Exception  Erro de conexão com o BD ou restrição de acesso a esta operação.
    */
    public void incluirMalaDiretaEnviadas( Integer codMalaDireta, List objetos ) throws Exception {
        Iterator e = objetos.iterator();
        while (e.hasNext()) {
            MalaDiretaEnviadaVO obj = (MalaDiretaEnviadaVO)e.next();
            obj.getMalaDiretaVO().setCodigo(codMalaDireta);
            incluir(obj);
        }
    }

    /**
     * Operação responsável por consultar todos os <code>MalaDiretaEnviadaVO</code> relacionados a um objeto da classe <code>CRM.malaDiretaVO</code>.
     * @param codMalaDireta  Atributo de <code>CRM.malaDiretaVO</code> a ser utilizado para localizar os objetos da classe <code>MalaDiretaEnviadaVO</code>.
     * @return List  Contendo todos os objetos da classe <code>MalaDiretaEnviadaVO</code> resultantes da consulta.
     * @exception Exception  Erro de conexão com o BD ou restrição de acesso a esta operação.
    */
    public List consultarMalaDiretaEnviadas(Integer codMalaDireta, int nivelMontarDados) throws Exception {
        consultarCRM(getIdEntidade(), false);
        List objetos = new ArrayList();
        String sql = "SELECT * FROM maladiretaenviada WHERE maladireta = ?";
        PreparedStatement sqlConsulta = con.prepareStatement(sql);
        sqlConsulta.setInt(1, codMalaDireta.intValue());
        ResultSet resultado = sqlConsulta.executeQuery();
        while (resultado.next()) {
            MalaDiretaEnviadaVO novoObj = new MalaDiretaEnviadaVO();
            novoObj = MalaDiretaEnviada.montarDados(resultado, nivelMontarDados, con);
            objetos.add(novoObj);
        }
        return objetos;
    }

    /**
     * Operação responsável por localizar um objeto da classe <code>MalaDiretaEnviadaVO</code>
     * através de sua chave primária. 
     * @exception Exception Caso haja problemas de conexão ou localização do objeto procurado.
    */
    public MalaDiretaEnviadaVO consultarPorChavePrimaria( Integer codigoPrm, int nivelMontarDados ) throws Exception {
        consultarCRM(getIdEntidade(), false);
        String sql = "SELECT * FROM maladiretaenviada WHERE codigo = ?";
        PreparedStatement sqlConsultar = con.prepareStatement(sql);
        sqlConsultar.setInt(1, codigoPrm.intValue() );
        ResultSet tabelaResultado = sqlConsultar.executeQuery();
        if (!tabelaResultado.next()) {
            throw new ConsistirException("Dados Não Encontrados ( MalaDiretaEnviada ).");
        }
        return (montarDados(tabelaResultado, nivelMontarDados, con));
    }

    /**
     * Operação responsável por obter o último valor gerado para uma chave primária.
     * É utilizada para obter o valor gerado pela SGBD para uma chave primária, 
     * a apresentação do mesmo e a implementação de possíveis relacionamentos. 
     */
    public  Integer obterValorChavePrimariaCodigo() throws Exception {
        inicializar();
        String sqlStr = "SELECT MAX(codigo) FROM maladiretaenviada";
        Statement stm = con.createStatement();
        ResultSet tabelaResultado = stm.executeQuery(sqlStr);
        tabelaResultado.next();
        return (new Integer( tabelaResultado.getInt(1)) );
    }
    
    public String executarAlteracaoTags(String texto, String nome){
    	texto = texto.replaceAll("&lt;NOME&gt;", nome);
    	return texto;
    }
}
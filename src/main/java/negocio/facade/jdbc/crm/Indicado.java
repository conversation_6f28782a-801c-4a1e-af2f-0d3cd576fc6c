package negocio.facade.jdbc.crm;

import br.com.pactosolucoes.enumeradores.OrigemSistemaEnum;
import negocio.comuns.basico.ClienteVO;
import negocio.comuns.basico.EmpresaVO;
import negocio.comuns.crm.FecharMetaDetalhadoVO;
import negocio.comuns.crm.FecharMetaVO;
import negocio.comuns.crm.IndicacaoVO;
import negocio.comuns.crm.IndicadoVO;
import negocio.comuns.utilitarias.Calendario;
import negocio.comuns.utilitarias.ConsistirException;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisValidacao;
import negocio.facade.jdbc.arquitetura.SuperEntidade;
import negocio.facade.jdbc.basico.Cliente;
import negocio.facade.jdbc.basico.Empresa;
import negocio.facade.jdbc.crm.fecharMetaDetalhado.FecharMetaDetalhado;
import negocio.interfaces.crm.FecharMetaInterfaceFacade;
import negocio.interfaces.crm.IndicadoInterfaceFacade;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.Statement;
import java.sql.Types;
import java.util.ArrayList;
import java.util.Date;
import java.util.Iterator;
import java.util.List;
import java.util.logging.Level;
import java.util.logging.Logger;

public class Indicado extends SuperEntidade implements IndicadoInterfaceFacade {

    public Indicado() throws Exception {
        super();
        setIdEntidade("Indicacao");
    }

    public Indicado(Connection con) throws Exception {
		super(con);
        setIdEntidade("Indicacao");
	}

	public IndicadoVO novo() throws Exception {
        incluirCRM(getIdEntidade());
        IndicadoVO obj = new IndicadoVO();
        return obj;
    }

    public void incluir(IndicadoVO obj) throws Exception {
        try {
            IndicadoVO.validarDados(obj);
            incluirCRM(getIdEntidade());
            obj.realizarUpperCaseDados();
            String sql = "INSERT INTO Indicado( telefoneIndicado, telefone, email, nomeIndicado, indicacao, cliente, empresa, origemSistema, dataLancamento, lead, cpf) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)";
            PreparedStatement sqlInserir = con.prepareStatement(sql);
            sqlInserir.setString(1, obj.getTelefoneIndicado());
            sqlInserir.setString(2, obj.getTelefone());
            sqlInserir.setString(3, obj.getEmail());
            sqlInserir.setString(4, obj.getNomeIndicado());
            if (obj.getIndicacaoVO().getCodigo().intValue() != 0) {
                sqlInserir.setInt(5, obj.getIndicacaoVO().getCodigo().intValue());
            } else {
                sqlInserir.setNull(5, 0);
            }
            if (obj.getClienteVO() != null && obj.getClienteVO().getCodigo() != 0) {
                sqlInserir.setInt(6, obj.getClienteVO().getCodigo());
            } else {
                sqlInserir.setNull(6, 0);
            }
            if(!UteisValidacao.emptyNumber(obj.getEmpresaVO().getCodigo())){
            	sqlInserir.setInt(7, obj.getEmpresaVO().getCodigo());
            }else{
            	sqlInserir.setNull(7, 0);
            }
            sqlInserir.setInt(8, obj.getOrigemSistemaEnum().getCodigo());
            sqlInserir.setTimestamp(9, Uteis.getDataJDBCTimestamp(Calendario.hoje()));
            sqlInserir.setBoolean(10, obj.isLead());
            sqlInserir.setString(11,obj.getCpf());
            sqlInserir.execute();
            obj.setCodigo(obterValorChavePrimariaCodigo());
            resolveNomeConsulta(obj);
            obj.setNovoObj(new Boolean(false));
            if(!obj.isLead()){
                FecharMeta fecharMeta = new FecharMeta(getCon());
                fecharMeta.executarPersistenciaFechaMetaDetalhado(obj.getIndicacaoVO().getDiaAbertura(), "IN", obj.getIndicacaoVO().getColaboradorResponsavel().getCodigo(), 0, obj.getCodigo(), 0, null, null, false);
                fecharMeta = null;
            }

        } catch (Exception e) {
            throw e;
        }


    }

    public IndicadoVO consultarPorEmail(Integer codigoEmpresa,String emailIndicado, int nivelMontarDados)throws Exception{
        String sql = "select * from indicado where (email = ?) and empresa = ? ";
        PreparedStatement pst = con.prepareStatement(sql);
        pst.setString(1, emailIndicado);
        pst.setInt(2,codigoEmpresa);
        ResultSet rs = pst.executeQuery();
        if (rs.next()) {
            return montarDados(rs,nivelMontarDados,con);
        }
        return null;

    }

    public void incluirAtualizandoFecharMetaDetalhado(IndicadoVO obj) throws Exception {
        try {
            IndicadoVO.validarDados(obj);
            incluirCRM(getIdEntidade());
            obj.realizarUpperCaseDados();
            String sql = "INSERT INTO Indicado( telefoneIndicado, telefone, email, nomeIndicado, indicacao, empresa, origemSistema, dataLancamento ) VALUES ( ?, ?, ?, ?, ?, ?, ?, ?,? )";
            PreparedStatement sqlInserir = con.prepareStatement(sql);
            sqlInserir.setString(1, obj.getTelefoneIndicado());
            sqlInserir.setString(2, obj.getTelefone());
            sqlInserir.setString(3, obj.getEmail());
            sqlInserir.setString(4, obj.getNomeIndicado());
            if (obj.getIndicacaoVO().getCodigo().intValue() != 0) {
                sqlInserir.setInt(5, obj.getIndicacaoVO().getCodigo().intValue());
            } else {
                sqlInserir.setNull(5, 0);
            }
            if(!UteisValidacao.emptyNumber(obj.getEmpresaVO().getCodigo())){
            	sqlInserir.setInt(6, obj.getEmpresaVO().getCodigo());
            }else{
            	sqlInserir.setNull(6, 0);
            }
            sqlInserir.setInt(7, obj.getOrigemSistemaEnum().getCodigo());
            sqlInserir.setTimestamp(8, Uteis.getDataJDBCTimestamp(Calendario.hoje()));
            sqlInserir.setString(9,obj.getCpf());
            sqlInserir.execute();
            obj.setCodigo(obterValorChavePrimariaCodigo());
            resolveNomeConsulta(obj);
            obj.setNovoObj(new Boolean(false));
            FecharMeta fecharMetaDao = new FecharMeta(con);
            FecharMetaVO fecharMetaVO = fecharMetaDao.consultarPorIdentificadorMetaPorCodigoIndicacaoPorColaborador("IN", obj.getIndicacaoVO().getCodigo(), obj.getIndicacaoVO().getColaboradorResponsavel().getCodigo().intValue(), false, Uteis.NIVELMONTARDADOS_DADOSENTIDADESPRINCIPAIS);
            fecharMetaDao.executarPersistenciaFechaMetaDetalhado(fecharMetaVO.getAberturaMetaVO().getDia(), "IN", obj.getIndicacaoVO().getColaboradorResponsavel().getCodigo(), 0, obj.getCodigo(), 0,null, null, false);
            fecharMetaDao.executarAtualizacaoMetaAtingidaPorIndicado(fecharMetaVO, 1L, true);
            fecharMetaDao = null;
        } catch (Exception e) {
            throw e;
        }


    }

    public void alterar(IndicadoVO obj) throws Exception {
        try {
            IndicadoVO.validarDados(obj);
            obj.realizarUpperCaseDados();
            alterarCRM(getIdEntidade());
            String sql = "UPDATE Indicado set telefoneIndicado=?, telefone=?, email=?, nomeIndicado=?, indicacao=?, empresa = ? WHERE ((codigo = ?)) returning codigo";
            PreparedStatement sqlAlterar = con.prepareStatement(sql);
            if (obj.getTelefoneIndicado() == null){
                sqlAlterar.setNull(1,Types.NULL);
            }else{
                sqlAlterar.setString(1, obj.getTelefoneIndicado());
            }
            if (obj.getTelefone() == null){
                sqlAlterar.setNull(2,Types.NULL);
            }else{
                sqlAlterar.setString(2, obj.getTelefone());
            }
            sqlAlterar.setString(3, obj.getEmail());
            sqlAlterar.setString(4, obj.getNomeIndicado());
            if (obj.getIndicacaoVO().getCodigo().intValue() != 0) {
                sqlAlterar.setInt(5, obj.getIndicacaoVO().getCodigo().intValue());
            } else {
                sqlAlterar.setNull(5, 0);
            }
            if(!UteisValidacao.emptyNumber(obj.getEmpresaVO().getCodigo())){
            	sqlAlterar.setInt(6, obj.getEmpresaVO().getCodigo());
            }else{
            	sqlAlterar.setNull(6, 0);
            }

            sqlAlterar.setInt(7, obj.getCodigo().intValue());
            resolveNomeConsulta(obj);
            if (!sqlAlterar.executeQuery().next()) {
                incluir(obj);
            }
        } catch (Exception e) {
            throw e;
        }
    }

    public void executarAlteracaoPorCadastroCliente(Integer codigo, Integer cliente) throws Exception {
        alterarCRM(getIdEntidade());
        String sql = "UPDATE Indicado set cliente=? WHERE ((codigo = ?))";
        PreparedStatement sqlAlterar = con.prepareStatement(sql);
        sqlAlterar.setInt(1, cliente);
        sqlAlterar.setInt(2, codigo);
        sqlAlterar.execute();
        Lead leadDao = new Lead(con);
        leadDao.executarAlteracaoClienteIndicado(codigo, cliente);
        leadDao = null;
    }

    public void excluir(IndicadoVO obj) throws Exception {
        try {
            excluirCRM(getIdEntidade());
            String sql = "DELETE FROM Indicado WHERE ((codigo = ?))";
            PreparedStatement sqlExcluir = con.prepareStatement(sql);
            sqlExcluir.setInt(1, obj.getCodigo().intValue());
            sqlExcluir.execute();
        } catch (Exception e) {
            throw e;
        }
    }

    public void incluirIndicados(IndicacaoVO indicacaoPrm, List objetos) throws Exception {
        Iterator e = objetos.iterator();
        while (e.hasNext()) {
            IndicadoVO obj = (IndicadoVO) e.next();
            obj.setIndicacaoVO(indicacaoPrm);
            obj.setEmpresaVO(indicacaoPrm.getEmpresa());
            incluir(obj);

        }
    }

    public List consultarPorCodigoIndicacao(Integer valorConsulta,  int nivelMontarDados) throws Exception {
        consultarCRM(getIdEntidade(), true);
        String sqlStr = "SELECT Indicado.* FROM Indicado, Indicacao WHERE Indicado.indicacao = Indicacao.codigo and Indicacao.codigo >= " + valorConsulta.intValue() + " ORDER BY Indicacao.codigo";
        Statement stm = con.createStatement();
        ResultSet tabelaResultado = stm.executeQuery(sqlStr);
        return montarDadosConsulta(tabelaResultado, nivelMontarDados, con);
    }

    public IndicadoVO consultarPorIndicacao(Integer codigoIndicacao, int nivelMontarDados) throws Exception{
        String sql = "select * from indicado where Indicacao = ?";
        PreparedStatement pst = con.prepareStatement(sql);
        pst.setInt(1, codigoIndicacao);
        ResultSet rs = pst.executeQuery();
        if (rs.next())
            return montarDados(rs,nivelMontarDados,con);
        return null;
    }


    public List consultarPorSituacaoCliente(String valorConsulta, EmpresaVO empresa,  int nivelMontarDados) throws Exception {
        consultarCRM(getIdEntidade(), true);
        StringBuilder sql = new StringBuilder();
        sql.append("SELECT Indicado.* FROM Indicado, Cliente WHERE Indicado.cliente = Cliente.codigo ");
        sql.append("and upper( Cliente.situacao ) like('" + valorConsulta.toUpperCase() + "%') ");
        if(!UteisValidacao.emptyNumber(empresa.getCodigo())){
        	sql.append(" AND indicado.empresa = "+empresa.getCodigo());
        }
        sql.append(" ORDER BY Cliente.situacao");
        Statement stm = con.createStatement();
        ResultSet tabelaResultado = stm.executeQuery(sql.toString());
        return montarDadosConsulta(tabelaResultado, nivelMontarDados, con);
    }

    public List consultarPorNomeIndicado(String valorConsulta, boolean controlarAcesso, EmpresaVO empresa,  int nivelMontarDados) throws Exception {
        consultarCRM(getIdEntidade(), controlarAcesso);
        StringBuilder sql = new StringBuilder();
        sql.append("SELECT * FROM Indicado WHERE upper( nomeIndicado ) like('" + valorConsulta.toUpperCase() + "%') ");
        if(!UteisValidacao.emptyNumber(empresa.getCodigo())){
        	sql.append(" AND indicado.empresa = "+empresa.getCodigo());
        }
        sql.append("  ORDER BY nomeIndicado");
        Statement stm = con.createStatement();
        ResultSet tabelaResultado = stm.executeQuery(sql.toString());
        return (montarDadosConsulta(tabelaResultado, nivelMontarDados, con));
    }

    public List consultarPorDia(Date prmIni, Date prmFim, boolean controlarAcesso, EmpresaVO empresa,  int nivelMontarDados) throws Exception {
        consultarCRM(getIdEntidade(), controlarAcesso);
        StringBuilder sql = new StringBuilder();
        sql.append("SELECT * FROM Indicado WHERE ((dia >= '" + Uteis.getDataJDBC(prmIni) + "') and (dia <= '" + Uteis.getDataJDBC(prmFim) + "')) ");
        if(!UteisValidacao.emptyNumber(empresa.getCodigo())){
        	sql.append(" AND indicado.empresa = "+empresa.getCodigo());
        }
        sql.append(" ORDER BY dia");
        Statement stm = con.createStatement();
        ResultSet tabelaResultado = stm.executeQuery(sql.toString());
        return (montarDadosConsulta(tabelaResultado, nivelMontarDados, con));
    }

    public List consultarPorCodigo(Integer valorConsulta, boolean controlarAcesso,  EmpresaVO empresa, int nivelMontarDados) throws Exception {
        consultarCRM(getIdEntidade(), controlarAcesso);
        StringBuilder sql = new StringBuilder();
        sql.append("SELECT * FROM Indicado WHERE codigo >= " + valorConsulta.intValue() );
        if(!UteisValidacao.emptyNumber(empresa.getCodigo())){
        	sql.append(" AND indicado.empresa = "+empresa.getCodigo());
        }
        sql.append(" ORDER BY codigo");
        Statement stm = con.createStatement();
        ResultSet tabelaResultado = stm.executeQuery(sql.toString());
        return (montarDadosConsulta(tabelaResultado, nivelMontarDados, con));
    }

    public IndicadoVO consultarPorCodigo(Integer codigo, int nivelMontarDados) throws Exception{
        String sql = "select * from Indicado where codigo = ?";
        PreparedStatement pst = con.prepareStatement(sql);
        pst.setInt(1, codigo);
        ResultSet rs = pst.executeQuery();
        if (rs.next())
            return montarDados(rs,nivelMontarDados,con);
        return null;
    }

    public IndicadoVO consultarPorCodigoCliente(Integer codigo, int nivelMontarDados) throws Exception {
        String sql = "SELECT Indicado.* FROM Indicado WHERE cliente = ?";

        PreparedStatement preparedStatement = con.prepareStatement(sql);
        preparedStatement.setInt(1, codigo);

        ResultSet rs = preparedStatement.executeQuery();
        if (rs.next())
            return montarDados(rs,nivelMontarDados,con);
        return null;
    }

    public List consultarListaIndicados(Integer indicacao, int nivelMontarDados) throws Exception {
        consultarCRM(getIdEntidade(), false);
        List objetos = new ArrayList();
        String sql = "SELECT * FROM Indicado WHERE indicacao = ?";
        PreparedStatement sqlConsulta = con.prepareStatement(sql);
        sqlConsulta.setInt(1, indicacao.intValue());
        ResultSet resultado = sqlConsulta.executeQuery();
        while (resultado.next()) {
            IndicadoVO novoObj = new IndicadoVO();
            novoObj = montarDados(resultado, nivelMontarDados, con);
            objetos.add(novoObj);
        }
        return objetos;
    }

    public static List montarDadosConsulta(ResultSet tabelaResultado, int nivelMontarDados, Connection con) throws Exception {
        List vetResultado = new ArrayList();
        while (tabelaResultado.next()) {
            IndicadoVO obj = new IndicadoVO();
            obj = montarDados(tabelaResultado, nivelMontarDados, con);
            vetResultado.add(obj);
        }
        tabelaResultado = null;
        return vetResultado;
    }

    public static List montarDadosConsultaIndicado(ResultSet tabelaResultado, int nivelMontarDados, Connection con) throws Exception {
        List vetResultado = new ArrayList();
        while (tabelaResultado.next()) {
            IndicadoVO obj = new IndicadoVO();
            obj = montarDados(tabelaResultado, nivelMontarDados, con);
            vetResultado.add(obj);
        }
        tabelaResultado = null;
        return vetResultado;
    }

    private static IndicadoVO montarDadosBasico(ResultSet dadosSQL)throws Exception {
        IndicadoVO obj = new IndicadoVO();
        obj.setCodigo(new Integer(dadosSQL.getInt("codigo")));
        obj.setNomeIndicado(dadosSQL.getString("nomeIndicado"));
        obj.setTelefoneIndicado(dadosSQL.getString("telefoneIndicado"));
        obj.setTelefone(dadosSQL.getString("telefone"));
        obj.setEmail(dadosSQL.getString("email"));
        obj.getIndicacaoVO().setCodigo(dadosSQL.getInt("indicacao"));
        obj.getEmpresaVO().setCodigo(dadosSQL.getInt("empresa"));
        obj.getClienteVO().setCodigo(new Integer(dadosSQL.getInt("cliente")));
        obj.setOrigemSistemaEnum(OrigemSistemaEnum.getOrigemSistema(dadosSQL.getInt("origemSistema")));
        obj.getObjecao().setCodigo(dadosSQL.getInt("objecao"));
        obj.setLead(dadosSQL.getBoolean("lead"));
        obj.setCpf(dadosSQL.getString("cpf"));
        obj.setNovoObj(new Boolean(false));
        return obj;

    }


    public static IndicadoVO montarDados(ResultSet dadosSQL, int nivelMontarDados, Connection con) throws Exception {
        IndicadoVO obj = montarDadosBasico(dadosSQL);
        if (nivelMontarDados == Uteis.NIVELMONTARDADOS_HISTORICOINDICADO) {
            montarDadosIndicacao(obj, nivelMontarDados, con);
            montarDadosEmpresa(obj, Uteis.NIVELMONTARDADOS_DADOSBASICOS, con);
            return obj;
        }
        if (nivelMontarDados == Uteis.NIVELMONTARDADOS_TELACONSULTA) {
            obj.setNovoObj(new Boolean(false));
            return obj;
        }
        if (nivelMontarDados == Uteis.NIVELMONTARDADOS_METAAGENDAMENTODETALHADA){
            Cliente clienteDao = new Cliente(con);
        	ClienteVO clienteVO = clienteDao.consultarPorCodigoPessoa(obj.getClienteVO().getCodigo(), Uteis.NIVELMONTARDADOS_METAAGENDAMENTODETALHADA);
        	obj.setClienteVO(clienteVO);
            clienteDao = null;
        }
        obj.getEmpresaVO().setCodigo(dadosSQL.getInt("empresa"));
        if (nivelMontarDados == Uteis.NIVELMONTARDADOS_DADOSBASICOS) {
            return obj;
        }
        if (nivelMontarDados == Uteis.NIVELMONTARDADOS_TODOS) {
            montarDadosIndicacao(obj, nivelMontarDados, con);
            montarDadosEmpresa(obj, Uteis.NIVELMONTARDADOS_DADOSBASICOS, con);
            return obj;
        }
        return obj;
    }

    public static void montarDadosIndicacao(IndicadoVO obj, int nivelMontarDados, Connection con) throws Exception {
        if (obj.getIndicacaoVO().getCodigo().intValue() == 0) {
            obj.setIndicacaoVO(new IndicacaoVO());
            return;
        }
        obj.setIndicacaoVO(new Indicacao(con).consultarPorChavePrimaria(obj.getIndicacaoVO().getCodigo(), Uteis.NIVELMONTARDADOS_HISTORICOINDICADO));
    }

    public Integer consultarPorCodigoCliente(Integer valorConsulta, boolean controlarAcesso) throws Exception {
        consultar(getIdEntidade(), controlarAcesso);
        String sqlStr = "";
        sqlStr = "SELECT Indicado.* FROM Indicado WHERE cliente =" + valorConsulta + " ";
        PreparedStatement sqlConsultar = con.prepareStatement(sqlStr);
        ResultSet tabelaResultado = sqlConsultar.executeQuery();
        if (!tabelaResultado.next()) {
            return 0;
        }
        return (new Integer(tabelaResultado.getInt(1)));
    }

    public IndicadoVO consultarPorChavePrimaria(Integer codigoPrm, int nivelMontarDados) throws Exception {
        consultarCRM(getIdEntidade(), false);
        String sql = "SELECT * FROM Indicado WHERE codigo = ?";
        PreparedStatement sqlConsultar = con.prepareStatement(sql);
        sqlConsultar.setInt(1, codigoPrm.intValue());
        ResultSet tabelaResultado = sqlConsultar.executeQuery();
        if (!tabelaResultado.next()) {
            throw new ConsistirException("Dados Não Encontrados ( Indicado ).");
        }
        return (montarDados(tabelaResultado, nivelMontarDados, con));
    }

    public void excluirIndicados(Integer grupoColaborador) throws Exception {
        excluirCRM(getIdEntidade());
        String sql = "DELETE FROM Indicado WHERE (indicacao = ?)";
        PreparedStatement sqlExcluir = con.prepareStatement(sql);
        sqlExcluir.setInt(1, grupoColaborador.intValue());
        sqlExcluir.execute();
    }

    /**
     * Operação responsável por alterar todos os objetos da <code>GrupoColaboradorParticipanteVO</code> contidos em um Hashtable no BD.
     * Faz uso da operação <code>excluirGrupoColaboradorParticipantes</code> e <code>incluirGrupoColaboradorParticipantes</code> disponíveis na classe <code>GrupoColaboradorParticipante</code>.
     * @param objetos  List com os objetos a serem alterados ou incluídos no BD.
     * @exception Exception  Erro de conexão com o BD ou restrição de acesso a esta operação.
     */
    public void alterarIndicados(IndicacaoVO indicacao, List objetos) throws Exception {
        validarFecharMeta(indicacao, objetos);
        String str = "DELETE FROM Indicado WHERE indicacao = " + indicacao.getCodigo();
        Iterator i = objetos.iterator();
        while (i.hasNext()) {
            IndicadoVO objeto = (IndicadoVO) i.next();
            str += " AND codigo <> " + objeto.getCodigo().intValue();
        }
        PreparedStatement sqlExcluir = con.prepareStatement(str);
        sqlExcluir.execute();
        Iterator e = objetos.iterator();
        while (e.hasNext()) {
            IndicadoVO objeto = (IndicadoVO) e.next();
            if (objeto.getCodigo().equals(0)) {
                objeto.setIndicacaoVO(indicacao);
                incluirAtualizandoFecharMetaDetalhado(objeto);
            } else {
                alterar(objeto);
            }
        }
    }

    public void validarFecharMeta(IndicacaoVO indicacao, List objetos) throws Exception {
        String str = "Select codigo FROM Indicado WHERE indicacao = " + indicacao.getCodigo();
        Iterator i = objetos.iterator();
        while (i.hasNext()) {
            IndicadoVO objeto = (IndicadoVO) i.next();
            str += " AND codigo <> " + objeto.getCodigo().intValue();
        }
        PreparedStatement sql = con.prepareStatement(str);
        ResultSet tabelaResultado = sql.executeQuery();
        Long diminuirMeta = 0L;
        FecharMetaDetalhado fecharMetaDetalhadoDao = new FecharMetaDetalhado(con);
        while (tabelaResultado.next()) {
            diminuirMeta = diminuirMeta + 1L;
            validarSePodeExcluirIndicado(new Integer(tabelaResultado.getInt("codigo")), indicacao.getColaboradorResponsavel().getCodigo().intValue());
            fecharMetaDetalhadoDao.excluirPorCodigoIndicadoCodigoColaboradorResponsavelDataIdentificador(new Integer(tabelaResultado.getInt("codigo")), indicacao.getCodigo(), indicacao.getColaboradorResponsavel().getCodigo().intValue(), "IN");
        }
        fecharMetaDetalhadoDao = null;
        FecharMeta fecharMetaDao = new FecharMeta(con);
        FecharMetaVO fecharMetaVO = fecharMetaDao.consultarPorIdentificadorMetaPorCodigoIndicacaoPorColaborador("IN", indicacao.getCodigo(), indicacao.getColaboradorResponsavel().getCodigo().intValue(), false, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
        if (diminuirMeta > 0) {
            fecharMetaDao.executarAtualizacaoMetaAtingidaPorIndicado(fecharMetaVO, diminuirMeta, false);
        }


    }

    public void validarSePodeExcluirIndicado(Integer codigoIndicado, Integer codigoColaborador) throws Exception {
        FecharMetaDetalhado fecharMetaDetalhadoDao = new FecharMetaDetalhado(con);
        if (!fecharMetaDetalhadoDao.consultarSeAberturaMetaEstaAbertaPorCodigoIndicadoCodigoColaboradorResponsavelIdentificador(codigoIndicado, codigoColaborador, "IN")) {
            throw new ConsistirException("Não será possivel alterar o registro de indicação, pois a abertura meta já foi fechada.");
        }
        if (consultarIndicaoECliente(codigoIndicado)) {
            throw new ConsistirException("Não será possivel remover esse indicado, pois ele já se tornou um cliente.");
        }
        fecharMetaDetalhadoDao = null;
    }

    public Boolean consultarIndicaoECliente(Integer indicado) throws Exception {
        excluirCRM(getIdEntidade());
        String str = "select * from indicado where codigo = " + indicado + " and cliente is not null";
        PreparedStatement sqlConsultar = con.prepareStatement(str);
        ResultSet tabelaResultado = sqlConsultar.executeQuery();
        if (!tabelaResultado.next()) {
            return false;
        }
        return true;
    }
    
    public static void montarDadosEmpresa(IndicadoVO obj, int nivelMontarDados, Connection con) throws Exception {
        if (obj.getEmpresaVO().getCodigo().intValue() == 0) {
            obj.setEmpresaVO(new EmpresaVO());
            return;
        }
        Empresa empresa = new Empresa(con);
        obj.setEmpresaVO(empresa.consultarPorChavePrimaria(obj.getEmpresaVO().getCodigo(), nivelMontarDados));
        empresa = null;
    }

    public void incluirIndicadosMeta(IndicacaoVO indicacaoPrm, List objetos, FecharMetaVO fecharMeta) throws Exception {
        Iterator e = objetos.iterator();
        while (e.hasNext()) {
            IndicadoVO obj = (IndicadoVO) e.next();
            obj.setIndicacaoVO(indicacaoPrm);
            obj.setEmpresaVO(indicacaoPrm.getEmpresa());
            incluirIndicadoMeta(obj, fecharMeta);
        }
    }

    public void incluirIndicadoMeta(IndicadoVO obj, FecharMetaVO fecharMeta) throws Exception {
        Logger.getLogger(getClass().getSimpleName()).log(Level.INFO, "#### [incluirIndicadoMeta] INICIANDO - IndicadoVO={0}, fecharMeta={1}",
                new Object[]{ obj, (fecharMeta != null ? fecharMeta.getCodigo() : null) }
        );
        try {
            IndicadoVO.validarDados(obj);
            incluirCRM(getIdEntidade());
            obj.realizarUpperCaseDados();
            String sql = "INSERT INTO Indicado( telefoneIndicado, telefone, email, nomeIndicado, indicacao, empresa,origemSistema, dataLancamento,cpf ) VALUES ( ?, ?, ?, ?, ?, ?, ?, ?,?)";
            PreparedStatement sqlInserir = con.prepareStatement(sql);
            sqlInserir.setString(1, obj.getTelefoneIndicado());
            sqlInserir.setString(2, obj.getTelefone());
            sqlInserir.setString(3, obj.getEmail());
            sqlInserir.setString(4, obj.getNomeIndicado());
            sqlInserir.setInt(5, obj.getIndicacaoVO().getCodigo());
            sqlInserir.setInt(6, obj.getEmpresaVO().getCodigo());
            sqlInserir.setInt(7, obj.getOrigemSistemaEnum().getCodigo());
            sqlInserir.setTimestamp(8, Uteis.getDataJDBCTimestamp(Calendario.hoje()));
            sqlInserir.setString(9,obj.getCpf());
            sqlInserir.execute();
            obj.setCodigo(obterValorChavePrimariaCodigo());
            Logger.getLogger(getClass().getSimpleName()).log(Level.INFO,
                    "#### [incluirIndicadoMeta] Indicado inserido com sucesso. Novo codigo={0}", obj.getCodigo());

            resolveNomeConsulta(obj);
            obj.setNovoObj(false);
            FecharMetaInterfaceFacade fecharMetaFacade = new FecharMeta(con);
            fecharMetaFacade.inserirFecharMetaDetalhadoIndicado(fecharMeta, obj.getCodigo());
            fecharMetaFacade = null;
            Logger.getLogger(getClass().getSimpleName()).log(Level.INFO, "#### [incluirIndicadoMeta] FINALIZADO OK");
        } catch (Exception e) {
            Logger.getLogger(getClass().getSimpleName()).log(Level.SEVERE, "#### ERRO AO INCLUIR INDICADO META: {0}", e.getMessage());
            throw e;
        }


    }

    private void resolveNomeConsulta(IndicadoVO obj) throws Exception {
        executarConsultaUpdate("UPDATE indicado SET nomeconsulta = remove_acento_upper(nomeindicado)"
                + " WHERE codigo = " + obj.getCodigo(), con);
    }

    public void alterarObjecaoIndicado(Integer objecao, Integer indicado) throws Exception {
        String sql = "UPDATE indicado SET objecao = ? WHERE codigo =  ?";
        PreparedStatement sqlAlterar = con.prepareStatement(sql);
        resolveIntegerNull(sqlAlterar, 1, objecao);
        sqlAlterar.setInt(2, indicado);
        sqlAlterar.execute();
    }

    public List consultarPorCpf(String cpf,EmpresaVO empresa, int nivelMontarDados) throws Exception {
        StringBuilder sql = new StringBuilder();
        sql.append("SELECT * FROM Indicado WHERE cpf = '" + cpf + "'");
        if (!UteisValidacao.emptyNumber(empresa.getCodigo())) {
            sql.append(" AND indicado.empresa = " + empresa.getCodigo());
        }
        Statement stm = con.createStatement();
        ResultSet tabelaResultado = stm.executeQuery(sql.toString());
        return (montarDadosConsulta(tabelaResultado, nivelMontarDados, con));
    }

    public List consultarIndicadoPorCpf(String cpf, int nivelMontarDados) throws Exception {
        StringBuilder sql = new StringBuilder();
        sql.append("SELECT * FROM Indicado WHERE cpf = '" + cpf + "'");
        Statement stm = con.createStatement();
        ResultSet tabelaResultado = stm.executeQuery(sql.toString());
        return (montarDadosConsulta(tabelaResultado, nivelMontarDados, con));
    }
}

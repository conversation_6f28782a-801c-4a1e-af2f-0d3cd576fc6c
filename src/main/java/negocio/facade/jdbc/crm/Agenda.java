package negocio.facade.jdbc.crm;

import br.com.pactosolucoes.comuns.util.Declaracao;
import br.com.pactosolucoes.enumeradores.TipoAgendaEnum;
import br.com.pactosolucoes.enumeradores.TipoTurmaEnum;
import br.com.pactosolucoes.enumeradores.*;
import br.com.pactosolucoes.turmas.servico.dto.ParamAlunoAulaCheiaTO;
import br.com.pactosolucoes.turmas.servico.impl.TurmasServiceImpl;
import br.com.pactosolucoes.turmas.servico.intf.TurmasServiceInterface;
import negocio.comuns.arquitetura.LogVO;
import negocio.comuns.arquitetura.UsuarioVO;
import negocio.comuns.basico.ClienteSituacaoVO;
import negocio.comuns.basico.ClienteVO;
import negocio.comuns.basico.ColaboradorVO;
import negocio.comuns.basico.TelefoneVO;
import negocio.comuns.basico.VinculoVO;
import negocio.comuns.contrato.ContratoVO;
import negocio.comuns.conviteaulaexperimental.ConviteAulaExperimentalVO;
import negocio.comuns.crm.*;
import negocio.comuns.plano.ModalidadeVO;
import negocio.comuns.utilitarias.Calendario;
import negocio.comuns.utilitarias.ConsistirException;
import negocio.comuns.utilitarias.Ordenacao;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisValidacao;
import negocio.comuns.utilitarias.UtilReflection;
import negocio.facade.jdbc.arquitetura.Log;
import negocio.facade.jdbc.arquitetura.SuperEntidade;
import negocio.facade.jdbc.contrato.Contrato;
import negocio.facade.jdbc.arquitetura.SuperFacadeJDBC;
import negocio.facade.jdbc.arquitetura.Usuario;
import negocio.facade.jdbc.basico.Cliente;
import negocio.facade.jdbc.crm.fecharMetaDetalhado.FecharMetaDetalhado;
import negocio.facade.jdbc.plano.Modalidade;
import negocio.facade.jdbc.utilitarias.Conexao;
import negocio.interfaces.arquitetura.UsuarioInterfaceFacade;
import negocio.interfaces.crm.AgendaInterfaceFacade;

import java.sql.*;
import java.util.*;
import java.util.Date;
import negocio.comuns.basico.ReposicaoVO;
import negocio.comuns.plano.ProdutoVO;

import java.util.Map;
import java.util.stream.Collectors;

/**
 * Classe de persistência que encapsula todas as operações de manipulação dos
 * dados da classe
 * <code>AgendaVO</code>. Responsável por implementar operações como incluir,
 * alterar, excluir e consultar pertinentes a classe
 * <code>AgendaVO</code>. Encapsula toda a interação com o banco de dados.
 *
 * @see AgendaVO
 * @see SuperEntidade
 */
public class Agenda extends SuperEntidade implements AgendaInterfaceFacade {

    private Log logDAO;
    private UsuarioInterfaceFacade usuarioDao;

    public Agenda() throws Exception {
        super();
        setIdEntidade("Agenda");
    }

    public Agenda(Connection conexao) throws Exception {
        super(conexao);
        setIdEntidade("Agenda");
        this.logDAO = new Log(conexao);
    }

    /**
     * Operação responsável por retornar um novo objeto da classe
     * <code>AgendaVO</code>.
     */
    public AgendaVO novo() throws Exception {
        incluirCRM(getIdEntidade());
        AgendaVO obj = new AgendaVO();
        return obj;
    }

    /**
     * Operação responsável por incluir no banco de dados um objeto da classe
     * <code>AgendaVO</code>. Primeiramente valida os dados (
     * <code>validarDados</code>) do objeto. Verifica a conexão com o banco de
     * dados e a permissão do usuário para realizar esta operacão na entidade.
     * Isto, através da operação
     * <code>incluir</code> da superclasse.
     *
     * @param obj Objeto da classe <code>AgendaVO</code> que será gravado no
     * banco de dados.
     * @exception Exception Caso haja problemas de conexão, restrição de acesso
     * ou validação de dados.
     */
    public void incluir(AgendaVO obj) throws Exception {
        try {
            AgendaVO.validarDados(obj);
            incluirCRM(getIdEntidade());
            incluirSemValidacao(obj);
        } catch (Exception e) {
            obj.setNovoObj(new Boolean(true));
            throw e;
        }
    }


    public void incluirSemValidacao(AgendaVO obj)throws Exception{
        obj.realizarUpperCaseDados();
        String sql = "INSERT INTO Agenda( passivo, indicado, colaboradorResponsavel, cliente, dataAgendamento, hora, minuto, tipoAgendamento, modalidade, responsavelCadastro, dataComparecimento, responsavelComparecimento, dataLancamento, empresa, conviteAulaExperimental, reposicao, gymPass, tipoProfessor, codigoProfessor, alunohorarioturma) VALUES (  ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)";
        try (PreparedStatement sqlInserir = con.prepareStatement(sql)) {
            if (obj.getPassivo().getCodigo().intValue() != 0) {
                sqlInserir.setInt(1, obj.getPassivo().getCodigo().intValue());
            } else {
                sqlInserir.setNull(1, 0);
            }
            if (obj.getIndicado().getCodigo().intValue() != 0) {
                sqlInserir.setInt(2, obj.getIndicado().getCodigo().intValue());
            } else {
                sqlInserir.setNull(2, 0);
            }
            if (obj.getColaboradorResponsavel().getCodigo().intValue() != 0) {
                sqlInserir.setInt(3, obj.getColaboradorResponsavel().getCodigo().intValue());
            } else {
                sqlInserir.setNull(3, 0);
            }
            if (obj.getCliente().getCodigo().intValue() != 0) {
                sqlInserir.setInt(4, obj.getCliente().getCodigo().intValue());
            } else {
                sqlInserir.setNull(4, 0);
            }
            sqlInserir.setDate(5, Uteis.getDataJDBC(obj.getDataAgendamento()));
            sqlInserir.setString(6, obj.getHora());
            sqlInserir.setString(7, obj.getMinuto());
            sqlInserir.setString(8, obj.getTipoAgendamento());
            if (obj.getModalidade().getCodigo().intValue() != 0) {
                sqlInserir.setInt(9, obj.getModalidade().getCodigo().intValue());
            } else {
                sqlInserir.setNull(9, 0);
            }

            if (obj.getResponsavelCadastro().getCodigo().intValue() != 0) {
                sqlInserir.setInt(10, obj.getResponsavelCadastro().getCodigo().intValue());
            }
            else{
                sqlInserir.setNull(10, 0);
            }
            sqlInserir.setTimestamp(11, Uteis.getDataJDBCTimestamp(obj.getDataComparecimento()));
            if (obj.getResponsavelComparecimento().getCodigo().intValue() != 0) {
                sqlInserir.setInt(12, obj.getResponsavelComparecimento().getCodigo().intValue());
            } else {
                sqlInserir.setNull(12, 0);
            }
            sqlInserir.setTimestamp(13, Uteis.getDataJDBCTimestamp(obj.getDataLancamento()));
            if (obj.getEmpresa() == null) {
                sqlInserir.setNull(14, 0);
            } else {
                sqlInserir.setInt(14, obj.getEmpresa());
            }
            if (UtilReflection.objetoMaiorQueZero(obj, "getConviteAulaExperimentalVO().getCodigo()")) {
                sqlInserir.setInt(15, obj.getConviteAulaExperimentalVO().getCodigo());
            } else {
                sqlInserir.setNull(15, Types.NULL);
            }
            if (UteisValidacao.emptyNumber(obj.getReposicao())) {
                sqlInserir.setNull(16, Types.NULL);
            } else {
                sqlInserir.setInt(16, obj.getReposicao());
            }
            sqlInserir.setBoolean(17, obj.isGymPass());
            sqlInserir.setString(18, obj.getTipoProfessor());

            if (obj.getCodigoProfessor() != null) {
                sqlInserir.setInt(19, obj.getCodigoProfessor());
            } else {
                sqlInserir.setNull(19, 0);
            }

            if (obj.getAlunohorarioturma() != null) {
                sqlInserir.setInt(20, obj.getAlunohorarioturma());
            } else {
                sqlInserir.setNull(20, 0);
            }

            sqlInserir.execute();
        }
        obj.setCodigo(obterValorChavePrimariaCodigo());
        obj.setNovoObj(new Boolean(false));
    }

    public void alterar(AgendaVO obj) throws Exception {
        try {
            AgendaVO.validarDados(obj);
            alterarCRM(getIdEntidade());
            obj.realizarUpperCaseDados();
            String sql = "UPDATE Agenda set passivo=?, indicado=?, colaboradorResponsavel=?, cliente=?, dataAgendamento=?, hora=?, minuto=?, tipoAgendamento=?, modalidade=?, responsavelCadastro=?, dataComparecimento=?, responsavelComparecimento=?, dataLancamento=?, empresa=?, reposicao=?, gymPass=?, tipoProfessor=?, codigoProfessor=? WHERE ((codigo = ?))";
            try (PreparedStatement sqlAlterar = con.prepareStatement(sql)) {
                if (obj.getPassivo().getCodigo().intValue() != 0) {
                    sqlAlterar.setInt(1, obj.getPassivo().getCodigo().intValue());
                } else {
                    sqlAlterar.setNull(1, 0);
                }
                if (obj.getIndicado().getCodigo().intValue() != 0) {
                    sqlAlterar.setInt(2, obj.getIndicado().getCodigo().intValue());
                } else {
                    sqlAlterar.setNull(2, 0);
                }
                if (obj.getColaboradorResponsavel().getCodigo().intValue() != 0) {
                    sqlAlterar.setInt(3, obj.getColaboradorResponsavel().getCodigo().intValue());
                } else {
                    sqlAlterar.setNull(3, 0);
                }
                if (obj.getCliente().getCodigo().intValue() != 0) {
                    sqlAlterar.setInt(4, obj.getCliente().getCodigo().intValue());
                } else {
                    sqlAlterar.setNull(4, 0);
                }
                sqlAlterar.setDate(5, Uteis.getDataJDBC(obj.getDataAgendamento()));
                sqlAlterar.setString(6, obj.getHora());
                sqlAlterar.setString(7, obj.getMinuto());
                sqlAlterar.setString(8, obj.getTipoAgendamento());
                if (obj.getModalidade().getCodigo().intValue() != 0) {
                    sqlAlterar.setInt(9, obj.getModalidade().getCodigo().intValue());
                } else {
                    sqlAlterar.setNull(9, 0);
                }

                if (obj.getResponsavelCadastro().getCodigo().intValue() != 0) {
                    sqlAlterar.setInt(10, obj.getResponsavelCadastro().getCodigo().intValue());
                } else {
                    sqlAlterar.setNull(10, 0);
                }
                sqlAlterar.setTimestamp(11, Uteis.getDataJDBCTimestamp(obj.getDataComparecimento()));
                if (obj.getResponsavelComparecimento().getCodigo().intValue() != 0) {
                    sqlAlterar.setInt(12, obj.getResponsavelComparecimento().getCodigo().intValue());
                } else {
                    sqlAlterar.setNull(12, 0);
                }
                sqlAlterar.setTimestamp(13, Uteis.getDataJDBCTimestamp(obj.getDataLancamento()));
                if (obj.getEmpresa() == null) {
                    sqlAlterar.setNull(14, 0);
                } else {
                    sqlAlterar.setInt(14, obj.getEmpresa());
                }
                if (UteisValidacao.emptyNumber(obj.getReposicao())) {
                    sqlAlterar.setNull(15, 0);
                } else {
                    sqlAlterar.setInt(15, obj.getReposicao());
                }
                sqlAlterar.setBoolean(16, obj.isGymPass());
                sqlAlterar.setString(17, obj.getTipoProfessor());

                if (obj.getCodigoProfessor() != null) {
                    sqlAlterar.setInt(18, obj.getCodigoProfessor());
                } else {
                    sqlAlterar.setNull(18, 0);
                }

                sqlAlterar.setInt(19, obj.getCodigo().intValue());
                sqlAlterar.execute();
            }

        } catch (Exception e) {
            throw e;
        }
    }

    public void alterarConfirmacaoAgendadosComparecidos(AgendaVO obj, Boolean dataComparecimentoNula, boolean controlarAcesso) throws Exception {
        try {
            AgendaVO.validarDados(obj);
            if(controlarAcesso){
                alterarCRM(getIdEntidade());
            }
            obj.realizarUpperCaseDados();
            String sql = "UPDATE Agenda set dataComparecimento=?, responsavelComparecimento=? WHERE ((codigo = ?))";
            try (PreparedStatement sqlAlterar = con.prepareStatement(sql)) {
                if (dataComparecimentoNula == true) {
                    sqlAlterar.setNull(1, 0);
                } else {
                    sqlAlterar.setTimestamp(1, Uteis.getDataJDBCTimestamp(obj.getDataComparecimento()));
                }
                if (obj.getResponsavelComparecimento().getCodigo().intValue() != 0) {
                    sqlAlterar.setInt(2, obj.getResponsavelComparecimento().getCodigo().intValue());
                } else {
                    sqlAlterar.setNull(2, 0);
                }
                sqlAlterar.setInt(3, obj.getCodigo().intValue());
                sqlAlterar.execute();
            }
        } catch (Exception e) {
            throw e;
        }

    }

    public void alterarAgendadosComparecido(AgendaVO obj, UsuarioVO usuario, boolean controlarAcesso) throws Exception {
        try {
            con.setAutoCommit(false);
            alterarAgendadosComparecidoSemCommit(obj, usuario, controlarAcesso);
            con.commit();
        } catch (Exception e) {
            con.rollback();
            con.setAutoCommit(true);
            throw e;
        } finally {
            con.setAutoCommit(true);
        }
    }
    
    public void alterarAgendadosComparecidoSemCommit(AgendaVO obj, UsuarioVO usuario, boolean controlarAcesso) throws Exception {
        try {
            obj.setIsDataComparecimentoNula(false);
            obj.setDataComparecimento(negocio.comuns.utilitarias.Calendario.hoje());
            obj.setResponsavelComparecimento(usuario);
            alterarConfirmacaoAgendadosComparecidos(obj, obj.getIsDataComparecimentoNula(), controlarAcesso);
            if(!obj.getTipoAgendamento().equals("LI")){
                consultarMetaDetalhadoAtualizacaoMetaAtingidaPorCodigoAgenda(obj, true);
            }
        } catch (Exception e) {
            obj.setDataComparecimento(null);
            obj.setResponsavelComparecimento(null);
            throw e;
        } 
    }

    /**
     * Método responsavel por consultar fecharMetaDetalhado para atualizar a
     * meta quando houver algum comparecimento pessoal.
     *
     * @param obj
     * @throws Exception
     */
    public void consultarMetaDetalhadoAtualizacaoMetaAtingidaPorCodigoAgenda(AgendaVO obj, Boolean somar) throws Exception {
        FecharMetaDetalhado fecharMetaDetalhadoDAO = new FecharMetaDetalhado(con);
        FecharMetaDetalhadoVO fecharMetaDetalhado = fecharMetaDetalhadoDAO.consultarFecharMetaDetalhadoPorCodigoAgenda(obj.getCodigo().intValue(), Uteis.NIVELMONTARDADOS_TODOS);
        fecharMetaDetalhadoDAO = null;
        if (!UteisValidacao.emptyNumber(fecharMetaDetalhado.getCodigo())) {
            FecharMeta fecharMetaDAO = new FecharMeta(con);
            fecharMetaDAO.executarAtualizacaoMetaAtingidaPessoasComparecidas(fecharMetaDetalhado, 0, 1L, somar);
            fecharMetaDAO = null;
        }
    }

    public void cancelarAgendadosComparecido(AgendaVO obj) throws Exception {
        try {
            con.setAutoCommit(false);
            obj.setIsDataComparecimentoNula(true);
            obj.setDataComparecimento(null);
            obj.setResponsavelComparecimento(new UsuarioVO());
            alterarConfirmacaoAgendadosComparecidos(obj, obj.getIsDataComparecimentoNula(), true);
            consultarMetaDetalhadoAtualizacaoMetaAtingidaPorCodigoAgenda(obj, false);
            con.commit();
        } catch (Exception e) {
            con.rollback();
            con.setAutoCommit(true);
            throw e;
        } finally {
            con.setAutoCommit(true);
        }
    }

    public void executarSomaTotalizador(AgendaVO obj) {
        obj.setTotalComparecidos(obj.getTotalComparecidos().intValue() + new Integer(1));
    }

    public void executarSubtracaoTotalizador(AgendaVO obj) {
        obj.setTotalComparecidos(obj.getTotalComparecidos().intValue() - new Integer(1));
    }

    /**
     * Operação responsável por excluir no BD um objeto da classe
     * <code>AgendaVO</code>. Sempre localiza o registro a ser excluído através
     * da chave primária da entidade. Primeiramente verifica a conexão com o
     * banco de dados e a permissão do usuário para realizar esta operacão na
     * entidade. Isto, através da operação
     * <code>excluir</code> da superclasse.
     *
     * @param obj Objeto da classe <code>AgendaVO</code> que será removido no
     * banco de dados.
     * @exception Exception Caso haja problemas de conexão ou restrição de
     * acesso.
     */
    public void excluir(AgendaVO obj) throws Exception {
        try {
            con.setAutoCommit(false);
            excluirCRM(getIdEntidade());
            String sql = "DELETE FROM Agenda WHERE ((codigo = ?))";
            try (PreparedStatement sqlExcluir = con.prepareStatement(sql)) {
                sqlExcluir.setInt(1, obj.getCodigo().intValue());
                sqlExcluir.execute();
            }
            con.commit();
        } catch (Exception e) {
            con.rollback();
            con.setAutoCommit(true);
            throw e;
        } finally {
            con.setAutoCommit(true);
        }
    }

    public void excluirSemCommit(Integer codigo) throws Exception {
        try {
            excluirCRM(getIdEntidade());

            //update para retirar a fk.
            String sqlupdate = "update historicocontato set observacao = observacao || ' (ALUNO DESMARCADO) ', agenda = null where agenda = "+ codigo;
            executarConsulta(sqlupdate ,con);

            // excluir a reposição
            FecharMeta fmDAO = new FecharMeta(con);
            fmDAO.removerAgendamentoNasMetas(codigo);
            fmDAO = null;

            String sqldelete = "delete from agenda where codigo = " + codigo;
            executarConsulta(sqldelete ,con);

        } catch (Exception e) {
            throw e;
        }
    }

    /**
     * Método responsavel por excluir Agenda pelo Codigo Indicado
     *
     * @param codPassivo
     * @throws Exception
     * <AUTHOR>
     */
    public void excluirAgendaPorCodigoPassivo(Integer codPassivo) throws Exception {
        try {
            excluirCRM(getIdEntidade());
            String sql = "DELETE FROM Agenda WHERE ((passivo = ?))";
            try (PreparedStatement sqlExcluir = con.prepareStatement(sql)) {
                sqlExcluir.setInt(1, codPassivo.intValue());
                sqlExcluir.execute();
            }
        } catch (Exception e) {
            throw e;
        }
    }

    /**
     * Método responsavel por excluir Agenda pelo Codigo Indicado
     *
     * @param codIndicado
     * @throws Exception
     * <AUTHOR>
     */
    public void excluirHistoricoContatoPorCodigoIndicado(Integer codIndicado) throws Exception {
        try {
            excluirCRM(getIdEntidade());
            String sql = "DELETE FROM Agenda WHERE ((indicado = ?))";
            try (PreparedStatement sqlExcluir = con.prepareStatement(sql)) {
                sqlExcluir.setInt(1, codIndicado.intValue());
                sqlExcluir.execute();
            }
        } catch (Exception e) {
            throw e;
        }
    }

    /**
     * Responsável por realizar uma consulta de
     * <code>Agenda</code> através do valor do atributo
     * <code>String modalidade</code>. Retorna os objetos, com início do valor
     * do atributo idêntico ao parâmetro fornecido. Faz uso da operação
     * <code>montarDadosConsulta</code> que realiza o trabalho de prerarar o
     * List resultante.
     *
     * @param controlarAcesso Indica se a aplicação deverá verificar se o
     * usuário possui permissão para esta consulta ou não.
     * @return List Contendo vários objetos da classe <code>AgendaVO</code>
     * resultantes da consulta.
     * @exception Exception Caso haja problemas de conexão ou restrição de
     * acesso.
     */
    public List consultarPorModalidade(String valorConsulta, boolean controlarAcesso, int nivelMontarDados) throws Exception {
        consultarCRM(getIdEntidade(), controlarAcesso);
        String sqlStr = "SELECT * FROM Agenda WHERE upper( modalidade ) like('" + valorConsulta.toUpperCase() + "%') ORDER BY modalidade";
        try (Statement stm = con.createStatement()) {
            try (ResultSet tabelaResultado = stm.executeQuery(sqlStr)) {
                return (montarDadosConsulta(tabelaResultado, nivelMontarDados, con));
            }
        }
    }

    public ResultSet consultarAgendadosParaHojePassivoAndIndicado(String valorConsultar, Date date, boolean controlarAcesso) throws Exception {
        consultarCRM(getIdEntidade(), controlarAcesso);
        String sqlStr = "select * from ( "
                + "(select pessoa.datanasc, passivo.codigo, 'PA' as tipoPessoa from passivo "
                + " inner join pessoa on pessoa.codigo = passivo.codigo where upper (passivo.nome) like ('" + valorConsultar.toUpperCase() + "%') limit 20) "
                + "UNION ALL "
                + "(select pessoa.datanasc, indicado.codigo, 'IN' as tipoPessoa from indicado "
                + " inner join pessoa on pessoa.codigo = indicado.codigo where upper (nomeindicado) like ('" + valorConsultar.toUpperCase() + "%') limit 20)  "
                + "UNION ALL "
                + "(select pessoa.datanasc, cliente.codigo, 'CL' as tipoPessoa from cliente "
                + " inner join pessoa on pessoa.codigo = cliente.pessoa where upper (pessoa.nome) like ('" + valorConsultar.toUpperCase() + "%') limit 20)  "
                + ")  as tabela";
        try (Statement stm = con.createStatement()) {
            try (ResultSet tabelaResultado = stm.executeQuery(sqlStr)) {
                return tabelaResultado;
            }
        }
    }

    public Integer consultarSeExistePassivoAgendado(Integer passivo, boolean controlarAcesso) throws Exception {
        consultarCRM(getIdEntidade(), controlarAcesso);
        String sqlStr = "select codigo from agenda where passivo = " + passivo + " ";
        try (Statement stm = con.createStatement()) {
            try (ResultSet tabelaResultado = stm.executeQuery(sqlStr)) {
                if (!tabelaResultado.next()) {
                    return 0;
                }
                return new Integer(tabelaResultado.getInt(1));
            }
        }
    }

    public Integer consultarSeExistePassivoAgendadoParaHoje(Integer passivo, Date date, boolean controlarAcesso) throws Exception {
        consultarCRM(getIdEntidade(), controlarAcesso);
        String sqlStr = "select codigo from agenda where passivo = " + passivo + " and Cast( dataagendamento as date) = Cast('" + Uteis.getDataJDBC(date) + "' as date)";
        try (Statement stm = con.createStatement()) {
            try (ResultSet tabelaResultado = stm.executeQuery(sqlStr)) {
                if (!tabelaResultado.next()) {
                    return 0;
                }
                return new Integer(tabelaResultado.getInt(1));
            }
        }
    }

    public Integer consultarSeExistePassivoAgendadoParaMes(Integer passivo, boolean controlarAcesso) throws Exception {
        consultarCRM(getIdEntidade(), controlarAcesso);
        String sqlStr = "select codigo from agenda where passivo = " + passivo + " and date_part ('MONTH',dataagendamento) =  date_part ('MONTH', now()) and date_part ('YEAR',dataagendamento) =  date_part ('YEAR', now())";
        try (Statement stm = con.createStatement()) {
            try (ResultSet tabelaResultado = stm.executeQuery(sqlStr)) {
                if (!tabelaResultado.next()) {
                    return 0;
                }
                return new Integer(tabelaResultado.getInt(1));
            }
        }
    }

    public Integer consultarSeExisteIndicadoAgendado(Integer indicado, boolean controlarAcesso) throws Exception {
        consultarCRM(getIdEntidade(), controlarAcesso);
        String sqlStr = "select codigo from agenda where indicado = " + indicado + " ";
        try (Statement stm = con.createStatement()) {
            try (ResultSet tabelaResultado = stm.executeQuery(sqlStr)) {
                if (!tabelaResultado.next()) {
                    return 0;
                }
                return new Integer(tabelaResultado.getInt(1));
            }
        }
    }

    public Integer consultarSeExisteIndicadoAgendadoParaHoje(Integer indicado, Date date, boolean controlarAcesso) throws Exception {
        consultarCRM(getIdEntidade(), controlarAcesso);
        String sqlStr = "select codigo from agenda where indicado = " + indicado + " and Cast( dataagendamento as date) = Cast('" + Uteis.getDataJDBC(date) + "' as date)";
        try (Statement stm = con.createStatement()) {
            try (ResultSet tabelaResultado = stm.executeQuery(sqlStr)) {
                if (!tabelaResultado.next()) {
                    return 0;
                }
                return new Integer(tabelaResultado.getInt(1));
            }
        }
    }

    public Integer consultarSeExisteIndicadoAgendadoParaMes(Integer indicado, boolean controlarAcesso) throws Exception {
        consultarCRM(getIdEntidade(), controlarAcesso);
        String sqlStr = "select codigo from agenda where indicado = " + indicado + " and date_part ('MONTH',dataagendamento) =  date_part ('MONTH', now()) and date_part ('YEAR',dataagendamento) =  date_part ('YEAR', now())";
        try (Statement stm = con.createStatement()) {
            try (ResultSet tabelaResultado = stm.executeQuery(sqlStr)) {
                if (!tabelaResultado.next()) {
                    return 0;
                }
                return new Integer(tabelaResultado.getInt(1));
            }
        }
    }

    public Integer consultarSeExisteClienteAgendadoVisitaOuAula(Integer cliente, boolean controlarAcesso) throws Exception {
        consultarCRM(getIdEntidade(), controlarAcesso);
        String sqlStr = "select codigo from agenda where tipoagendamento <> 'LI' and Cliente = " + cliente + " ";
        try (Statement stm = con.createStatement()) {
            try (ResultSet tabelaResultado = stm.executeQuery(sqlStr)) {
                if (!tabelaResultado.next()) {
                    return 0;
                }
                return new Integer(tabelaResultado.getInt(1));
            }
        }
    }

    public void verificaExisteAgendamentoDiaHoraMinuto(HistoricoContatoVO historicoContatoVO, AgendaVO agendaVO) throws Exception {
        StringBuilder sql = new StringBuilder();
        sql.append("select exists(select codigo from agenda where 1 = 1 \n");
        if (!UteisValidacao.emptyNumber(historicoContatoVO.getClienteVO().getCodigo())) {
            sql.append(" and cliente = ").append(historicoContatoVO.getClienteVO().getCodigo());
        } else if (!UteisValidacao.emptyNumber(historicoContatoVO.getPassivoVO().getCodigo())) {
            sql.append(" and passivo = ").append(historicoContatoVO.getPassivoVO().getCodigo());
        } else if (!UteisValidacao.emptyNumber(historicoContatoVO.getIndicadoVO().getCodigo())) {
            sql.append(" and indicado = ").append(historicoContatoVO.getIndicadoVO().getCodigo());
        }
        sql.append(" and dataagendamento::date = '").append(Uteis.getDataJDBC(agendaVO.getDataAgendamento())).append("' \n");
        sql.append(" and hora = '").append(agendaVO.getHora()).append("' \n");
        sql.append(" and minuto = '").append(agendaVO.getMinuto()).append("')");
        boolean existe;
        try (Statement stm = con.createStatement()) {
            try (ResultSet tabelaResultado = stm.executeQuery(sql.toString())) {
                existe = tabelaResultado.next() && tabelaResultado.getBoolean(1);
            }
        }
        if (existe) {
            throw new Exception("Já existe um agendamento para o dia " + Uteis.getData(agendaVO.getDataAgendamento()) + " para às " + agendaVO.getHora() + ":" + agendaVO.getMinuto());
        }
    }

    public Integer consultarSeExisteClienteAgendadoParaMes(Integer cliente, boolean controlarAcesso) throws Exception {
        consultarCRM(getIdEntidade(), controlarAcesso);
        String sqlStr = "select codigo from agenda where cliente = " + cliente + " and date_part ('MONTH',dataagendamento) =  date_part ('MONTH', now()) and date_part ('YEAR',dataagendamento) =  date_part ('YEAR', now())";
        try (Statement stm = con.createStatement()) {
            try (ResultSet tabelaResultado = stm.executeQuery(sqlStr)) {
                if (!tabelaResultado.next()) {
                    return 0;
                }
                return new Integer(tabelaResultado.getInt(1));
            }
        }
    }

    /**
     * Responsável por realizar uma consulta de
     * <code>Agenda</code> através do valor do atributo
     * <code>String tipoAgendamento</code>. Retorna os objetos, com início do
     * valor do atributo idêntico ao parâmetro fornecido. Faz uso da operação
     * <code>montarDadosConsulta</code> que realiza o trabalho de prerarar o
     * List resultante.
     *
     * @param controlarAcesso Indica se a aplicação deverá verificar se o
     * usuário possui permissão para esta consulta ou não.
     * @return List Contendo vários objetos da classe <code>AgendaVO</code>
     * resultantes da consulta.
     * @exception Exception Caso haja problemas de conexão ou restrição de
     * acesso.
     */
    public List consultarPorTipoAgendamento(String valorConsulta, boolean controlarAcesso, int nivelMontarDados) throws Exception {
        consultarCRM(getIdEntidade(), controlarAcesso);
        String sqlStr = "SELECT * FROM Agenda WHERE upper( tipoAgendamento ) like('" + valorConsulta.toUpperCase() + "%') ORDER BY tipoAgendamento";
        try (Statement stm = con.createStatement()) {
            try (ResultSet tabelaResultado = stm.executeQuery(sqlStr)) {
                return (montarDadosConsulta(tabelaResultado, nivelMontarDados, con));
            }
        }
    }

    /**
     * Responsável por realizar uma consulta de
     * <code>Agenda</code> através do valor do atributo
     * <code>Date dia</code>. Retorna os objetos com valores pertecentes ao
     * período informado por parâmetro. Faz uso da operação
     * <code>montarDadosConsulta</code> que realiza o trabalho de prerarar o
     * List resultante.
     *
     * @param controlarAcesso Indica se a aplicação deverá verificar se o
     * usuário possui permissão para esta consulta ou não.
     * @return List Contendo vários objetos da classe <code>AgendaVO</code>
     * resultantes da consulta.
     * @exception Exception Caso haja problemas de conexão ou restrição de
     * acesso.
     */
    public List consultarPorDia(Date prmIni, Date prmFim, boolean controlarAcesso, int nivelMontarDados) throws Exception {
        consultarCRM(getIdEntidade(), controlarAcesso);
        String sqlStr = "SELECT * FROM Agenda WHERE ((dataAgendamento >= '" + Uteis.getDataJDBC(prmIni) + "') and (dataAgendamento <= '" + Uteis.getDataJDBC(prmFim) + "')) ORDER BY dataAgendamento";
        try (Statement stm = con.createStatement()) {
            try (ResultSet tabelaResultado = stm.executeQuery(sqlStr)) {
                return (montarDadosConsulta(tabelaResultado, nivelMontarDados, con));
            }
        }
    }

    /**
     * Responsável por realizar uma consulta de
     * <code>Agenda</code> através do valor do atributo
     * <code>situacao</code> da classe
     * <code>Cliente</code> Faz uso da operação
     * <code>montarDadosConsulta</code> que realiza o trabalho de prerarar o
     * List resultante.
     *
     * @return List Contendo vários objetos da classe <code>AgendaVO</code>
     * resultantes da consulta.
     * @exception Exception Caso haja problemas de conexão ou restrição de
     * acesso.
     */
    public List consultarPorSituacaoCliente(String valorConsulta, int nivelMontarDados) throws Exception {
        consultarCRM(getIdEntidade(), true);
        String sqlStr = "SELECT Agenda.* FROM Agenda, Cliente WHERE Agenda.cliente = Cliente.codigo and upper( Cliente.situacao ) like('" + valorConsulta.toUpperCase() + "%') ORDER BY Cliente.situacao";
        try (Statement stm = con.createStatement()) {
            try (ResultSet tabelaResultado = stm.executeQuery(sqlStr)) {
                return montarDadosConsulta(tabelaResultado, nivelMontarDados, con);
            }
        }
    }

    /**
     * Responsável por realizar uma consulta de
     * <code>Agenda</code> através do valor do atributo
     * <code>situacao</code> da classe
     * <code>Colaborador</code> Faz uso da operação
     * <code>montarDadosConsulta</code> que realiza o trabalho de prerarar o
     * List resultante.
     *
     * @return List Contendo vários objetos da classe <code>AgendaVO</code>
     * resultantes da consulta.
     * @exception Exception Caso haja problemas de conexão ou restrição de
     * acesso.
     */
    public List consultarPorSituacaoColaborador(String valorConsulta, int nivelMontarDados) throws Exception {
        consultarCRM(getIdEntidade(), true);
        String sqlStr = "SELECT Agenda.* FROM Agenda, Colaborador WHERE Agenda.colaboradorResponsavel = colaborador.codigo and upper( Colaborador.situacao ) like('" + valorConsulta.toUpperCase() + "%') ORDER BY Colaborador.situacao";
        try (Statement stm = con.createStatement()) {
            try (ResultSet tabelaResultado = stm.executeQuery(sqlStr)) {
                return montarDadosConsulta(tabelaResultado, nivelMontarDados, con);
            }
        }
    }

    public List consultarPorSituacaoResponsavelCadastro(String valorConsulta, int nivelMontarDados) throws Exception {
        consultarCRM(getIdEntidade(), true);
        String sqlStr = "SELECT Agenda.* FROM Agenda, Colaborador WHERE Agenda.responsavelCadastro = colaborador.codigo and upper( Colaborador.situacao ) like('" + valorConsulta.toUpperCase() + "%') ORDER BY Colaborador.situacao";
        try (Statement stm = con.createStatement()) {
            try (ResultSet tabelaResultado = stm.executeQuery(sqlStr)) {
                return montarDadosConsulta(tabelaResultado, nivelMontarDados, con);
            }
        }
    }

    /**
     * Responsável por realizar uma consulta de
     * <code>Agenda</code> através do valor do atributo
     * <code>nomeIndicado</code> da classe
     * <code>Indicacao</code> Faz uso da operação
     * <code>montarDadosConsulta</code> que realiza o trabalho de prerarar o
     * List resultante.
     *
     * @return List Contendo vários objetos da classe <code>AgendaVO</code>
     * resultantes da consulta.
     * @exception Exception Caso haja problemas de conexão ou restrição de
     * acesso.
     */
    public List consultarPorNomeIndicado(String valorConsulta, int nivelMontarDados) throws Exception {
        consultarCRM(getIdEntidade(), true);
        String sqlStr = "SELECT Agenda.* FROM Agenda, Indicado WHERE Agenda.indicado = Indicado.codigo and upper( Indicado.nomeIndicado ) like('" + valorConsulta.toUpperCase() + "%') ORDER BY Indicado.nomeIndicado";
        try (Statement stm = con.createStatement()) {
            try (ResultSet tabelaResultado = stm.executeQuery(sqlStr)) {
                return montarDadosConsulta(tabelaResultado, nivelMontarDados, con);
            }
        }
    }

    /**
     * Responsável por realizar uma consulta de
     * <code>Agenda</code> através do valor do atributo
     * <code>nome</code> da classe
     * <code>Passivo</code> Faz uso da operação
     * <code>montarDadosConsulta</code> que realiza o trabalho de prerarar o
     * List resultante.
     *
     * @return List Contendo vários objetos da classe <code>AgendaVO</code>
     * resultantes da consulta.
     * @exception Exception Caso haja problemas de conexão ou restrição de
     * acesso.
     */
    public List consultarPorNomePassivo(String valorConsulta, int nivelMontarDados) throws Exception {
        consultarCRM(getIdEntidade(), true);
        String sqlStr = "SELECT Agenda.* FROM Agenda, Passivo WHERE Agenda.passivo = Passivo.codigo and upper( Passivo.nome ) like('" + valorConsulta.toUpperCase() + "%') ORDER BY Passivo.nome";
        try (Statement stm = con.createStatement()) {
            try (ResultSet tabelaResultado = stm.executeQuery(sqlStr)) {
                return montarDadosConsulta(tabelaResultado, nivelMontarDados, con);
            }
        }
    }

    /**
     * Método responsavel por fazer a consulta de todos agendados do dia
     *
     * @param dia
     * @param nivelMontarDados
     * @return
     * @throws Exception
     */
    public List consultarAgendadosConfirmacaoComparecimentoHoje(Date dia, Integer empresa, int nivelMontarDados) throws Exception {
        StringBuilder sqlStr = new StringBuilder();
        sqlStr.append("SELECT agenda.* FROM agenda WHERE tipoAgendamento <> 'LI' ");
        sqlStr.append("AND dataAgendamento = CAST('").append(Uteis.getDataJDBC(dia)).append("' as Date)");
        if (!UteisValidacao.emptyNumber(empresa)) {
            sqlStr.append("AND empresa = ").append(empresa);
        }
        try (Statement stm = con.createStatement()) {
            try (ResultSet tabelaResultado = stm.executeQuery(sqlStr.toString())) {
                return montarDadosConsulta(tabelaResultado, nivelMontarDados, con);
            }
        }
    }

    /**
     * Método responsavel por fazer a consulta de todos agendados do Mes
     *
     * @param nivelMontarDados nível para montar os dados
     * @throws Exception
     */
    public List consultarAgendadosConfirmacaoComparecimentoMes(Integer empresa, int nivelMontarDados) throws Exception {
        StringBuilder sqlStr = new StringBuilder();
        sqlStr.append("SELECT * FROM agenda WHERE tipoAgendamento <> 'LI' \n");
        sqlStr.append("AND date_part('month', dataAgendamento) = date_part('month', Cast ('").append(Uteis.getDataJDBC(Calendario.hoje())).append("' as Date)) \n");
        sqlStr.append("AND date_part('year', dataAgendamento) = date_part('year', Cast ('").append(Uteis.getDataJDBC(Calendario.hoje())).append("' as Date)) \n");
        if (!UteisValidacao.emptyNumber(empresa)) {
            sqlStr.append("AND empresa = ").append(empresa);
        }
        try (Statement stm = con.createStatement()) {
            try (ResultSet tabelaResultado = stm.executeQuery(sqlStr.toString())) {
                return montarDadosConsulta(tabelaResultado, nivelMontarDados, con);
            }
        }
    }

    /**
     * Método responsavel por fazer a consulta de todos agendados do Historico
     * passando duas datas ou nome.
     *
     * @param nivelMontarDados
     * @return
     * @throws Exception
     */
    public List consultarAgendadosConfirmacaoComparecimentoHistorico(Integer codEmpresa, String valorConsulta, Date dataInicio, Date dataTermino, int nivelMontarDados) throws Exception {
        String sqlStr = "select Agenda.* from agenda " + "inner join passivo on passivo.codigo = agenda.passivo and upper (passivo.nome) like('" + valorConsulta.toUpperCase() + "%') " + "WHERE tipoAgendamento <> 'LI' and agenda.empresa = " + codEmpresa + " and dataAgendamento >= '" + Uteis.getDataJDBC(dataInicio) + " 00:00:00' and dataAgendamento <= '" + Uteis.getDataJDBC(dataTermino) + " 23:59:59' " + "UNION ALL " + "select Agenda.* from agenda " + "inner join indicado on indicado.codigo = agenda.indicado and upper (indicado.nomeIndicado) like('" + valorConsulta.toUpperCase() + "%') " + "WHERE tipoAgendamento <> 'LI' and agenda.empresa = " + codEmpresa+ " and dataAgendamento >= '" + Uteis.getDataJDBC(dataInicio) + " 00:00:00' and dataAgendamento <= '" + Uteis.getDataJDBC(dataTermino) + " 23:59:59' " + "UNION ALL " + "select Agenda.* from agenda " + "inner join cliente on cliente.codigo = agenda.cliente "
                + "inner join pessoa on pessoa.codigo = cliente.pessoa and upper (pessoa.nome) like('" + valorConsulta.toUpperCase() + "%') " + "WHERE tipoAgendamento <> 'LI' and agenda.empresa= " + codEmpresa + " and dataAgendamento >= '" + Uteis.getDataJDBC(dataInicio) + " 00:00:00' and dataAgendamento <= '" + Uteis.getDataJDBC(dataTermino) + " 23:59:59' ";
        try (Statement stm = con.createStatement()) {
            try (ResultSet tabelaResultado = stm.executeQuery(sqlStr)) {
                return montarDadosConsulta(tabelaResultado, nivelMontarDados, con);
            }
        }
    }

    /**
     * Responsável por realizar uma consulta de
     * <code>Agenda</code> através do valor do atributo
     * <code>Integer codigo</code>. Retorna os objetos com valores iguais ou
     * superiores ao parâmetro fornecido. Faz uso da operação
     * <code>montarDadosConsulta</code> que realiza o trabalho de prerarar o
     * List resultante.
     *
     * @param controlarAcesso Indica se a aplicação deverá verificar se o
     * usuário possui permissão para esta consulta ou não.
     * @return List Contendo vários objetos da classe <code>AgendaVO</code>
     * resultantes da consulta.
     * @exception Exception Caso haja problemas de conexão ou restrição de
     * acesso.
     */
    public List consultarPorCodigo(Integer valorConsulta, boolean controlarAcesso, int nivelMontarDados) throws Exception {
        consultarCRM(getIdEntidade(), controlarAcesso);
        String sqlStr = "SELECT * FROM Agenda WHERE codigo >= " + valorConsulta.intValue() + " ORDER BY codigo";
        try (Statement stm = con.createStatement()) {
            try (ResultSet tabelaResultado = stm.executeQuery(sqlStr)) {
                return (montarDadosConsulta(tabelaResultado, nivelMontarDados, con));
            }
        }
    }

    public List consultarUltimoPorCodigo(Integer codigo,  int nivelMontarDados) throws Exception {
        String sqlStr = "SELECT * FROM Agenda WHERE cliente = " + codigo.intValue() + " order by dataagendamento desc\n" +
                "limit 1";
        try (Statement stm = con.createStatement()) {
            try (ResultSet tabelaResultado = stm.executeQuery(sqlStr)) {
                return (montarDadosConsulta(tabelaResultado, nivelMontarDados, con));
            }
        }
    }
    public AgendaVO consultarPorCodigoPassivo(Integer valorConsulta, Date dia, boolean controlarAcesso, int nivelMontarDados) throws Exception {
        consultarCRM(getIdEntidade(), controlarAcesso);
        String sqlStr = "select * from agenda where passivo = " + valorConsulta.intValue() + " and dataAgendamento = Cast('" + Uteis.getDataJDBC(dia) + "' as Date)";
        try (Statement stm = con.createStatement()) {
            try (ResultSet tabelaResultado = stm.executeQuery(sqlStr)) {
                if (!tabelaResultado.next()) {
                    throw new ConsistirException("Dados Não Encontrados ( Agenda ).");
                }
                return (montarDados(tabelaResultado, nivelMontarDados, con));
            }
        }
    }

    public AgendaVO consultarPorCodigoPassivo(Integer valorConsulta, boolean controlarAcesso, int nivelMontarDados) throws Exception {
        consultarCRM(getIdEntidade(), controlarAcesso);
        String sqlStr = "select * from agenda where passivo = " + valorConsulta.intValue();
        try (Statement stm = con.createStatement()) {
            try (ResultSet tabelaResultado = stm.executeQuery(sqlStr)) {
                if (!tabelaResultado.next()) {
                    throw new ConsistirException("Dados Não Encontrados ( Agenda ).");
                }
                return (montarDados(tabelaResultado, nivelMontarDados, con));
            }
        }
    }

    public AgendaVO consultarPorCodigoIndicado(Integer valorConsulta, Date dia, boolean controlarAcesso, int nivelMontarDados) throws Exception {
        consultarCRM(getIdEntidade(), controlarAcesso);
        String sqlStr = "select * from agenda where indicado = " + valorConsulta.intValue() + " and dataAgendamento = Cast('" + Uteis.getDataJDBC(dia) + "' as Date)";
        try (Statement stm = con.createStatement()) {
            try (ResultSet tabelaResultado = stm.executeQuery(sqlStr)) {
                if (!tabelaResultado.next()) {
                    throw new ConsistirException("Dados Não Encontrados ( Agenda ).");
                }
                return (montarDados(tabelaResultado, nivelMontarDados,con));
            }
        }
    }

    public AgendaVO consultarPorCodigoCliente(Integer valorConsulta, Date dia, boolean controlarAcesso, int nivelMontarDados) throws Exception {
        consultarCRM(getIdEntidade(), controlarAcesso);
        String sqlStr = "select * from agenda where cliente = " + valorConsulta.intValue() + " and dataAgendamento = Cast('" + Uteis.getDataJDBC(dia) + "' as Date)";
        try (Statement stm = con.createStatement()) {
            try (ResultSet tabelaResultado = stm.executeQuery(sqlStr)) {
                if (!tabelaResultado.next()) {
                    throw new ConsistirException("Dados Não Encontrados ( Agenda ).");
                }
                return (montarDados(tabelaResultado, nivelMontarDados, con));
            }
        }
    }

    public List<AgendaVO> consultarPorConviteAulaExperimental(TipoTurmaEnum tipoTurmaEnum,Integer codigoConviteAulaExperimental, int nivelMontarDados)throws Exception{
        StringBuilder sql = new StringBuilder();
        sql.append("select distinct ag.*,  mod.nome as nomeModalidade  \n");
        sql.append("from agenda ag \n");
        sql.append("inner join modalidade mod on mod.codigo = ag.modalidade \n");
        sql.append("inner join turma t on t.modalidade = mod.codigo \n");
        sql.append("where ag.conviteAulaExperimental = ? \n");
        if (tipoTurmaEnum != TipoTurmaEnum.TODOS){
            sql.append("and t.aulacoletiva = ").append( (tipoTurmaEnum == TipoTurmaEnum.AULA_CHEIA) ? "true": "false ");
        }
        sql.append(" order by ag.dataAgendamento");
        List<AgendaVO> lista;
        try (PreparedStatement pst = con.prepareStatement(sql.toString())) {
            pst.setInt(1, codigoConviteAulaExperimental);
            try (ResultSet rs = pst.executeQuery()) {
                lista = new ArrayList<AgendaVO>();
                while (rs.next()) {
                    AgendaVO obj = montarDadosBasico(rs);
                    obj.getModalidade().setNome(rs.getString("nomeModalidade"));
                    lista.add(obj);
                }
            }
        }
        return lista;

    }


    /**
     * Responsável por montar os dados de vários objetos, resultantes de uma
     * consulta ao banco de dados (
     * <code>ResultSet</code>). Faz uso da operação
     * <code>montarDados</code> que realiza o trabalho para um objeto por vez.
     *
     * @return List Contendo vários objetos da classe <code>AgendaVO</code>
     * resultantes da consulta.
     */
    public static List montarDadosConsulta(ResultSet tabelaResultado, int nivelMontarDados, Connection con) throws Exception {
        List vetResultado = new ArrayList();
        while (tabelaResultado.next()) {
            AgendaVO obj = new AgendaVO();
            obj = montarDados(tabelaResultado, nivelMontarDados, con);
            vetResultado.add(obj);
        }
        tabelaResultado = null;
        return vetResultado;
    }

    public static AgendaVO montarDadosBasico(ResultSet dadosSQL) throws Exception {
        AgendaVO obj = new AgendaVO();
        obj.setCodigo(new Integer(dadosSQL.getInt("codigo")));
        obj.setDataAgendamento(dadosSQL.getDate("dataAgendamento"));
        obj.getResponsavelCadastro().setCodigo(new Integer(dadosSQL.getInt("responsavelCadastro")));
        obj.getColaboradorResponsavel().setCodigo(new Integer(dadosSQL.getInt("colaboradorResponsavel")));
        obj.setDataLancamento(dadosSQL.getTimestamp("dataLancamento"));
        obj.setHora(dadosSQL.getString("hora"));
        obj.setMinuto(dadosSQL.getString("minuto"));
        obj.setTipoAgendamento(dadosSQL.getString("tipoAgendamento"));
        obj.setDataComparecimento(dadosSQL.getTimestamp("dataComparecimento"));
        obj.getModalidade().setCodigo(new Integer(dadosSQL.getInt("modalidade")));
        obj.getResponsavelComparecimento().setCodigo(new Integer(dadosSQL.getInt("responsavelComparecimento")));
        obj.getPassivo().setCodigo(new Integer(dadosSQL.getInt("passivo")));
        obj.getIndicado().setCodigo(new Integer(dadosSQL.getInt("indicado")));
        obj.getCliente().setCodigo(new Integer(dadosSQL.getInt("cliente")));
        obj.setEmpresa(dadosSQL.getInt("empresa"));
        obj.setConviteAulaExperimentalVO(new ConviteAulaExperimentalVO());
        obj.getConviteAulaExperimentalVO().setCodigo(dadosSQL.getInt("conviteAulaExperimental"));
        obj.setGymPass(dadosSQL.getBoolean("gymPass"));
        return obj;
    }

    /**
     * Responsável por montar os dados resultantes de uma consulta ao banco de
     * dados (
     * <code>ResultSet</code>) em um objeto da classe
     * <code>AgendaVO</code>.
     *
     * @return O objeto da classe <code>AgendaVO</code> com os dados devidamente
     * montados.
     */
    public static AgendaVO montarDados(ResultSet dadosSQL, int nivelMontarDados, Connection con) throws Exception {
        AgendaVO obj = montarDadosBasico(dadosSQL);
        if (nivelMontarDados == Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA) {
            return obj;
        }

        if (nivelMontarDados == Uteis.NIVELMONTARDADOS_TELACONSULTA) {
            montarDadosColaboradorResponsavel(obj, nivelMontarDados, con);
            montarDadosResponsavelCadastro(obj, nivelMontarDados, con);
            return obj;
        }
        if (nivelMontarDados == Uteis.NIVELMONTARDADOS_INDICERENOVACAO) {
            montarDadosColaboradorResponsavel(obj, nivelMontarDados, con);
            montarDadosResponsavelCadastro(obj, nivelMontarDados, con);
            montarDadosResponsavelComparecimento(obj, nivelMontarDados, con);
            montarDadosModalidade(obj, Uteis.NIVELMONTARDADOS_TELACONSULTA, con);
            return obj;
        }
        obj.setNovoObj(new Boolean(false));
        if (nivelMontarDados == Uteis.NIVELMONTARDADOS_DADOSBASICOS) {
            return obj;
        }

        montarDadosPassivo(obj, nivelMontarDados, con);
        montarDadosIndicado(obj, nivelMontarDados, con);
        montarDadosColaboradorResponsavel(obj, nivelMontarDados, con);
        montarDadosCliente(obj, nivelMontarDados, con);
        montarDadosResponsavelCadastro(obj, nivelMontarDados, con);
        montarDadosResponsavelComparecimento(obj, nivelMontarDados, con);
        montarDadosModalidade(obj, Uteis.NIVELMONTARDADOS_DADOSBASICOS, con);
        return obj;
    }

    /**
     * Operação responsável por montar os dados de um objeto da classe
     * <code>ClienteVO</code> relacionado ao objeto
     * <code>AgendaVO</code>. Faz uso da chave primária da classe
     * <code>ClienteVO</code> para realizar a consulta.
     *
     * @param obj Objeto no qual será montado os dados consultados.
     */
    public static void montarDadosResponsavelCadastro(AgendaVO obj, int nivelMontarDados, Connection con) throws Exception {
        if (obj.getResponsavelCadastro().getCodigo().intValue() == 0) {
            obj.setResponsavelCadastro(new UsuarioVO());
            return;
        }
        Usuario usuarioDAO = new Usuario(con);
        obj.setResponsavelCadastro(usuarioDAO.consultarPorChavePrimaria(obj.getResponsavelCadastro().getCodigo(), nivelMontarDados));
        usuarioDAO = null;
    }

    /*
     * Operação responsável por montar os dados de um objeto da classe
     * <code>ClienteVO</code> relacionado ao objeto
     * <code>AgendaVO</code>. Faz uso da chave primária da classe
     * <code>ClienteVO</code> para realizar a consulta.
     *
     * @param obj Objeto no qual será montado os dados consultados.
     */
    public static void montarDadosResponsavelComparecimento(AgendaVO obj, int nivelMontarDados, Connection con) throws Exception {
        if (obj.getResponsavelComparecimento().getCodigo().intValue() == 0) {
            obj.setResponsavelComparecimento(new UsuarioVO());
            return;
        }
        Usuario usuarioDAO = new Usuario(con);
        obj.setResponsavelComparecimento(usuarioDAO.consultarPorChavePrimaria(obj.getResponsavelComparecimento().getCodigo(), nivelMontarDados));
        usuarioDAO = null;
    }

    /**
     * Operação responsável por montar os dados de um objeto da classe
     * <code>ClienteVO</code> relacionado ao objeto
     * <code>AgendaVO</code>. Faz uso da chave primária da classe
     * <code>ClienteVO</code> para realizar a consulta.
     *
     * @param obj Objeto no qual será montado os dados consultados.
     */
    public static void montarDadosCliente(AgendaVO obj, int nivelMontarDados, Connection con) throws Exception {
        if (obj.getCliente().getCodigo().intValue() == 0) {
            obj.setCliente(new ClienteVO());
            return;
        }
        Cliente clienteDAO = new Cliente(con);
        obj.setCliente(clienteDAO.consultarPorChavePrimaria(obj.getCliente().getCodigo(), nivelMontarDados));
        clienteDAO = null;
    }

    /**
     * Operação responsável por montar os dados de um objeto da classe
     * <code>ColaboradorVO</code> relacionado ao objeto
     * <code>AgendaVO</code>. Faz uso da chave primária da classe
     * <code>ColaboradorVO</code> para realizar a consulta.
     *
     * @param obj Objeto no qual será montado os dados consultados.
     */
    public static void montarDadosColaboradorResponsavel(AgendaVO obj, int nivelMontarDados, Connection con) throws Exception {
        if (obj.getColaboradorResponsavel().getCodigo().intValue() == 0) {
            obj.setColaboradorResponsavel(new UsuarioVO());
            return;
        }
        Usuario usuarioDAO = new Usuario(con);
        obj.setColaboradorResponsavel(usuarioDAO.consultarPorChavePrimaria(obj.getColaboradorResponsavel().getCodigo(), nivelMontarDados));
        usuarioDAO = null;
    }

    /**
     * Operação responsável por montar os dados de um objeto da classe
     * <code>IndicacaoVO</code> relacionado ao objeto
     * <code>AgendaVO</code>. Faz uso da chave primária da classe
     * <code>IndicacaoVO</code> para realizar a consulta.
     *
     * @param obj Objeto no qual será montado os dados consultados.
     */
    public static void montarDadosIndicado(AgendaVO obj, int nivelMontarDados, Connection con) throws Exception {
        if (obj.getIndicado().getCodigo().intValue() == 0) {
            obj.setIndicado(new IndicadoVO());
            return;
        }
        Indicado indicadoDAO = new Indicado(con);
        obj.setIndicado(indicadoDAO.consultarPorChavePrimaria(obj.getIndicado().getCodigo(), nivelMontarDados));
        indicadoDAO = null;
    }

    /**
     * Operação responsável por montar os dados de um objeto da classe
     * <code>PassivoVO</code> relacionado ao objeto
     * <code>AgendaVO</code>. Faz uso da chave primária da classe
     * <code>PassivoVO</code> para realizar a consulta.
     *
     * @param obj Objeto no qual será montado os dados consultados.
     */
    public static void montarDadosPassivo(AgendaVO obj, int nivelMontarDados, Connection con) throws Exception {
        if (obj.getPassivo().getCodigo().intValue() == 0) {
            obj.setPassivo(new PassivoVO());
            return;
        }
        Passivo passivoDAO = new Passivo(con);
        obj.setPassivo(passivoDAO.consultarPorChavePrimaria(obj.getPassivo().getCodigo(), nivelMontarDados));
        passivoDAO = null;
    }

    public static void montarDadosModalidade(AgendaVO obj, int nivelMontarDados, Connection con) throws Exception {
        if (obj.getModalidade().getCodigo().intValue() == 0) {
            obj.setModalidade(new ModalidadeVO());
            return;
        }
        Modalidade modalidadeDAO = new Modalidade(con);
        obj.setModalidade(modalidadeDAO.consultarPorChavePrimaria(obj.getModalidade().getCodigo(), nivelMontarDados));
        modalidadeDAO = null;
    }

    /**
     * Operação responsável por localizar um objeto da classe
     * <code>AgendaVO</code> através de sua chave primária.
     *
     * @exception Exception Caso haja problemas de conexão ou localização do
     * objeto procurado.
     */
    public AgendaVO consultarPorChavePrimaria(Integer codigoPrm, int nivelMontarDados) throws Exception {
        consultarCRM(getIdEntidade(), false);
        String sql = "SELECT * FROM Agenda WHERE codigo = ?";
        try (PreparedStatement sqlConsultar = con.prepareStatement(sql)) {
            sqlConsultar.setInt(1, codigoPrm.intValue());
            try (ResultSet tabelaResultado = sqlConsultar.executeQuery()) {
                if (!tabelaResultado.next()) {
                    throw new ConsistirException("Dados Não Encontrados ( Agenda ).");
                }
                return (montarDados(tabelaResultado, nivelMontarDados, con));
            }
        }
    }

    public AgendaVO consultarValidandoMeta(FecharMetaDetalhadoVO obj, int nivelMontarDados) throws Exception {
        AgendaVO agenda = null;
        FecharMeta fecharMetaDAO = new FecharMeta(con);
        try {
            agenda = consultarPorChavePrimaria(obj.getCodigoOrigem(), nivelMontarDados);
        } catch (Exception ex) {
            if (ex.getMessage().equals("Dados Não Encontrados ( Agenda ).") && obj.getCodigoOrigem() != 0 && obj.getOrigem().equals("AGENDA")) {
                if (obj.getFecharMeta().getIdentificadorMeta().equals(FasesCRMEnum.AGENDAMENTO.getSigla()) ||
                        obj.getFecharMeta().getIdentificadorMeta().equals(FasesCRMEnum.LIGACAO_AGENDADOS_AMANHA.getSigla())) {
                    fecharMetaDAO.removerMetasQueJaTiveramAgendamento(obj);
                    return new AgendaVO();
                }
            }
        } finally {
            fecharMetaDAO = null;
        }
        return agenda;
    }

    // regra de negocio
    /**
     * Método responsavel por validar se o tipo de Operação é Reagendamento, se
     * for executar as regras de negocio estabelecidas por ele.
     */
    public void preencherDadosAgenda(AgendaVO agenda, HistoricoContatoVO historicoContato, String tipoOperacao, Boolean atualizarMeta) throws Exception {
        preencherDadosAgenda(agenda, historicoContato, tipoOperacao, atualizarMeta, null);
    }

    public void preencherDadosAgenda(AgendaVO agenda, HistoricoContatoVO historicoContato, String tipoOperacao, Boolean atualizarMeta, UsuarioVO usuarioVO) throws Exception {
        if (tipoOperacao.equals(FasesCRMEnum.RENOVACAO.getSigla())) {
            if (Uteis.getCompareData(agenda.getDataAgendamento(), historicoContato.getReagendamentoVO().getDataAgendamento()) == 0) {
                executarPreenchimentoDadosAgendaAlteracao(historicoContato, agenda);
                historicoContato.setAgendaVO(historicoContato.getReagendamentoVO());
                historicoContato.setResultado(historicoContato.qualResultado(true, FasesCRMEnum.AGENDAMENTO.getSigla()));
            } else {
                executarPreenchimentoDadosAgendaAndPersistir(agenda, historicoContato, tipoOperacao);
                //Realizo aqui a operação set com valor agenda para que no qualResultado utilize o valor da agendamento futuro
                historicoContato.setAgendaVO(agenda);
                historicoContato.setResultado(historicoContato.qualResultado(true, FasesCRMEnum.AGENDAMENTO.getSigla()));
                //Realizo aqui a operação set com valor reagendamento para gravar no banco o codigo da agenda
                //historicoContato.setAgendaVO(historicoContato.getReagendamentoVO());
                if (atualizarMeta && !agenda.getTipoAgendamento().equals("LI") && !historicoContato.getContatoAvulso()) {
                    if (historicoContato.getCodigoFecharMetaDetalhado().intValue() != 0) {
                        FecharMetaDetalhadoVO fecharMetaDetalhado = getFacade().getFecharMetaDetalhado().consultarPorChavePrimaria(historicoContato.getCodigoFecharMetaDetalhado(), Uteis.NIVELMONTARDADOS_DADOSBASICOS);
                        if (fecharMetaDetalhado.getFecharMeta().getAberturaMetaVO().getMetaEmAberto()) {
                            getFacade().getFecharMeta().executarAtualizacaoMetaAtingida(fecharMetaDetalhado, 0, 1L, true, true);
                        } else {
                            getFacade().getFecharMeta().executarAtualizacaoMetaRepescagem(fecharMetaDetalhado, 0, 1L, true);
                        }
                    }
                }

            }
        } else {
            if(usuarioVO!=null){
                executarPreenchimentoDadosAgendaAndPersistir(agenda, historicoContato, tipoOperacao, usuarioVO);
            }else {
                executarPreenchimentoDadosAgendaAndPersistir(agenda, historicoContato, tipoOperacao);
            }
            historicoContato.setAgendaVO(agenda);
            historicoContato.setResultado(historicoContato.qualResultado(false, "AG"));
        }
    }

    public void executarPreenchimentoDadosAgendaAlteracao(HistoricoContatoVO historicoContato, AgendaVO agenda) throws Exception {
        historicoContato.getReagendamentoVO().setHora(agenda.getHora());
        historicoContato.getReagendamentoVO().setMinuto(agenda.getMinuto());
        historicoContato.getReagendamentoVO().setEmpresa(agenda.getEmpresa());
        historicoContato.getReagendamentoVO().setTipoAgendamento(agenda.getTipoAgendamento());
        historicoContato.getReagendamentoVO().setDataAgendamento(agenda.getDataAgendamento());
        historicoContato.getReagendamentoVO().setModalidade(agenda.getModalidade());
        historicoContato.getReagendamentoVO().setResponsavelCadastro(historicoContato.getResponsavelCadastro());
        historicoContato.getReagendamentoVO().setColaboradorResponsavel(historicoContato.getColaboradorResponsavel());
        historicoContato.getReagendamentoVO().getPassivo().setCodigo(historicoContato.getPassivoVO().getCodigo());
        historicoContato.getReagendamentoVO().getIndicado().setCodigo(historicoContato.getIndicadoVO().getCodigo());
        historicoContato.getReagendamentoVO().getCliente().setCodigo(historicoContato.getClienteVO().getCodigo());
        if (!historicoContato.getReagendamentoVO().getModalidade().getNome().equals("")) {
            historicoContato.getReagendamentoVO().setModalidade(getFacade().getModalidade().consultarPorNomeModalidade(historicoContato.getReagendamentoVO().getModalidade().getNome(), false, Uteis.NIVELMONTARDADOS_TELACONSULTA));
        }
        alterar(historicoContato.getReagendamentoVO());
    }

    public UsuarioVO obterColaboradorResponsavel(AgendaVO agenda, HistoricoContatoVO historicoContatoVO) throws Exception{
        UsuarioVO responsavel = new UsuarioVO();
        //j.a.:passivo não tem com consultor responsavel
        if (historicoContatoVO.getPassivoVO().getCodigo() != 0) {
            return historicoContatoVO.getColaboradorResponsavel();
        } else if (historicoContatoVO.getClienteVO().getCodigo() != 0) {
            //j.a.:se for cliente, descobrir o usuario do consultor
            ResultSet rs = SuperFacadeJDBC.criarConsulta("SELECT pessoa FROM colaborador c\n"
                    + "INNER JOIN vinculo v on v.colaborador = c.codigo and v.tipovinculo = 'CO' and v.cliente = "
                    +historicoContatoVO.getClienteVO().getCodigo(), con);
            if(rs.next()){
                ResultSet rsUsuario = SuperFacadeJDBC.criarConsulta("SELECT u.codigo from usuario u\n"
                        + " inner join colaborador c on u.colaborador = c.codigo and c.pessoa = "
                        +rs.getInt("pessoa"), con);
                if(rsUsuario.next()){
                    responsavel.setCodigo(rsUsuario.getInt("codigo"));
                    return responsavel;
                }
            }
            return historicoContatoVO.getColaboradorResponsavel();
        } else {
            //j.a.:se for indicado, descobrir o usuario do consultor responsavel
            ResultSet rs = SuperFacadeJDBC.criarConsulta("select u.codigo from indicacao i\n"
                    + " inner join indicado id on id.indicacao = i.codigo\n"
                    + " inner join usuario u on i.colaboradorresponsavel = u.codigo"
                    + " where id.codigo = "+historicoContatoVO.getIndicadoVO().getCodigo(), con);
            if(rs.next()){
                responsavel.setCodigo(rs.getInt("codigo"));
                return responsavel;
            }else{
                return historicoContatoVO.getColaboradorResponsavel();
            }
        }
    }

    public void executarPreenchimentoDadosAgendaAndPersistir(AgendaVO agenda, HistoricoContatoVO historicoContatoVO, String tipoOperacao) throws Exception {
        executarPreenchimentoDadosAgendaAndPersistir(agenda, historicoContatoVO, tipoOperacao, null);
    }

    public void executarPreenchimentoDadosAgendaAndPersistir(AgendaVO agenda, HistoricoContatoVO historicoContatoVO, String tipoOperacao, UsuarioVO usuarioVO) throws Exception {

        ConfiguracaoSistemaCRMVO configuracaoSistemaCRM = getFacade().getConfiguracaoSistemaCRM().consultarConfiguracaoSistemaCRM(Uteis.NIVELMONTARDADOS_DADOSBASICOS);


        if(usuarioVO!=null){
            agenda.setColaboradorResponsavel(usuarioVO);
        }
        else {
            if (configuracaoSistemaCRM.isAgendamentoParaMetaConsultor()) {
                agenda.setColaboradorResponsavel(obterColaboradorResponsavel(agenda, historicoContatoVO));
            } else {
                agenda.setColaboradorResponsavel(historicoContatoVO.getColaboradorResponsavel());
            }
        }

        agenda.setResponsavelCadastro(historicoContatoVO.getResponsavelCadastro());

        if (historicoContatoVO.getPassivoVO().getCodigo() != 0) {
            agenda.getPassivo().setCodigo(historicoContatoVO.getPassivoVO().getCodigo());
        } else if (historicoContatoVO.getClienteVO().getCodigo() != 0) {
            agenda.getCliente().setCodigo(historicoContatoVO.getClienteVO().getCodigo());
        } else {
            agenda.getIndicado().setCodigo(historicoContatoVO.getIndicadoVO().getCodigo());
        }
        if (!agenda.getModalidade().getNome().equals("")) {
            agenda.setModalidade(getFacade().getModalidade().consultarPorNomeModalidade(agenda.getModalidade().getNome(), false, Uteis.NIVELMONTARDADOS_TELACONSULTA));
        }

        if (historicoContatoVO.getReagendamentoVO() != null && Calendario.maior(historicoContatoVO.getReagendamentoVO().getDataAgendamento(), Calendario.hoje())) {
            if (!agenda.getTipoAgendamento().equals("LI") || (agenda.getTipoAgendamento().equals("LI") && historicoContatoVO.getReagendamentoVO().equals("LI"))) {
                agenda.setCodigo(historicoContatoVO.getReagendamentoVO().getCodigo());
            }
        }
        if (agenda.getCodigo() == 0 || Calendario.maior(agenda.getDataAgendamento(), Calendario.hoje())) {
            incluir(agenda);
        } else {
            alterar(agenda);
        }
    }

    public void executarRegraNegocioParaFecharMetaInclusao(Date dia, AgendaVO agenda, Integer colaboradorResponsavel, Integer passivo, Integer indicado, Integer cliente, HistoricoContatoVO hist) throws Exception {
        if (dia == null) {
            dia = Calendario.hoje();
        }
        Integer responsavelValidar = UtilReflection.objetoMaiorQueZero(agenda, "getColaboradorResponsavel().getCodigo()") ? agenda.getColaboradorResponsavel().getCodigo() : colaboradorResponsavel;
        boolean executaMetasAmanha = Calendario.igual(Calendario.amanha(), agenda.getDataAgendamento());

        FecharMeta fecharMetaDao = new FecharMeta(this.con);
        if (passivo != 0 && agenda.getCodigo() != 0 && Uteis.getCompareData(agenda.getDataAgendamento(), dia) == 0) {
            fecharMetaDao.executarPersistenciaFechaMetaDetalhadoParaAgendamento(dia, agenda, responsavelValidar, passivo, 0, 0, hist);
        } else if (cliente != 0 && agenda.getCodigo() != 0 && Uteis.getCompareData(agenda.getDataAgendamento(), dia) == 0) {
            fecharMetaDao.executarPersistenciaFechaMetaDetalhadoParaAgendamento(dia, agenda, responsavelValidar, 0, 0, cliente, hist);
        } else if (indicado != 0 && agenda.getCodigo() != 0 && Uteis.getCompareData(agenda.getDataAgendamento(), dia) == 0) {
            fecharMetaDao.executarPersistenciaFechaMetaDetalhadoParaAgendamento(dia, agenda, responsavelValidar, 0, indicado, 0, hist);
        } else  if (passivo != 0 && agenda.getCodigo() != 0 && Uteis.getCompareData(agenda.getDataAgendamento(), dia) == 0) {
            fecharMetaDao.executarPersistenciaFechaMetaDetalhadoParaAgendamento(dia, agenda, responsavelValidar, passivo, 0, 0, hist);
        }else if (cliente != 0 && agenda.getCodigo() != 0 && executaMetasAmanha && !agenda.getTipoAgendamento().equals("LI")) {
            fecharMetaDao.executarPersistenciaFechaMetaDetalhadoParaAgendadosAmanhã(dia, agenda, responsavelValidar, 0, 0, cliente, hist);
        }
        fecharMetaDao = null;
    }

    public void executarRegraNegocioParaFecharMetaAlteracao(Date dia, AgendaVO agenda, Integer colaboradorResponsavel, Integer passivo, Integer indicado, Integer cliente, HistoricoContatoVO hist) throws Exception {
         Integer responsavelValidar = UtilReflection.objetoMaiorQueZero(agenda, "getColaboradorResponsavel().getCodigo()") ? agenda.getColaboradorResponsavel().getCodigo() : colaboradorResponsavel;
        if (passivo.intValue() != 0) {
            // valido se agenda nao foi gravado no banco e se é para hoje
            if (agenda.getCodigo().intValue() == 0 && Uteis.getCompareData(agenda.getDataAgendamento(), dia) == 0) {
                getFacade().getFecharMeta().executarPersistenciaFechaMetaDetalhadoParaAgendamento(dia, agenda, responsavelValidar, passivo, 0, 0, hist);
            } else if (Uteis.getCompareData(agenda.getDataAgendamento(), dia) == 0) {
                // Valido se o agendamento e diferente de ligação pois so com
                // essa
                // condição que é gerado alteração no banco em fecharMeta.
                if (hist.getCodigoFecharMetaDetalhado().intValue() != 0) {
                    if (agenda.getTipoAgendamento().equals("LI")) {

                        FecharMetaDetalhadoVO fecharMetaDetalhado = getFacade().getFecharMetaDetalhado().consultarPorChavePrimaria(hist.getCodigoFecharMetaDetalhado(), Uteis.NIVELMONTARDADOS_DADOSBASICOS);
                        getFacade().getFecharMeta().executarAtualizacaoMetaAtingida(fecharMetaDetalhado, 0, 1L, false, false);
                    } else {
                        FecharMetaDetalhadoVO fecharMetaDetalhado = getFacade().getFecharMetaDetalhado().consultarPorChavePrimaria(hist.getCodigoFecharMetaDetalhado(), Uteis.NIVELMONTARDADOS_DADOSBASICOS);
                        getFacade().getFecharMeta().executarAtualizacaoMetaAtingida(fecharMetaDetalhado, 0, 1L, true,true);
                    }
                }
            }
        } else if (cliente.intValue() != 0) {
            // valido se agenda nao foi gravado no banco e se é para hoje
            if (agenda.getCodigo().intValue() == 0 && Uteis.getCompareData(agenda.getDataAgendamento(), dia) == 0) {
                getFacade().getFecharMeta().executarPersistenciaFechaMetaDetalhadoParaAgendamento(dia, agenda, responsavelValidar, 0, 0, cliente, hist);
            } else if (Uteis.getCompareData(agenda.getDataAgendamento(), dia) == 0) {
                // Valido se o agendamento e diferente de ligação pois so com
                // essa
                // condição que é gerado alteração no banco em fecharMeta.
                if (hist.getCodigoFecharMetaDetalhado().intValue() != 0) {
                    if (agenda.getTipoAgendamento().equals("LI")) {

                        FecharMetaDetalhadoVO fecharMetaDetalhado = getFacade().getFecharMetaDetalhado().consultarPorChavePrimaria(hist.getCodigoFecharMetaDetalhado(), Uteis.NIVELMONTARDADOS_DADOSBASICOS);
                        getFacade().getFecharMeta().executarAtualizacaoMetaAtingida(fecharMetaDetalhado, 0, 1L, false,false);
                    } else {
                        FecharMetaDetalhadoVO fecharMetaDetalhado = getFacade().getFecharMetaDetalhado().consultarPorChavePrimaria(hist.getCodigoFecharMetaDetalhado(), Uteis.NIVELMONTARDADOS_DADOSBASICOS);
                        getFacade().getFecharMeta().executarAtualizacaoMetaAtingida(fecharMetaDetalhado, 0, 1L, true,true);
                    }
                }
            }

        } else if (indicado.intValue() != 0) {
            // valido se agenda nao foi gravado no banco e se é para hoje
            if (agenda.getCodigo().intValue() == 0 && Uteis.getCompareData(agenda.getDataAgendamento(), dia) == 0) {
                getFacade().getFecharMeta().executarPersistenciaFechaMetaDetalhadoParaAgendamento(dia, agenda, responsavelValidar, 0, indicado, 0, hist);
            } else if (Uteis.getCompareData(agenda.getDataAgendamento(), dia) == 0) {
                // Valido se o agendamento e diferente de ligação pois so com
                // essa
                // condição que é gerado alteração no banco em fecharMeta.
                if (hist.getCodigoFecharMetaDetalhado().intValue() != 0) {
                    if (agenda.getTipoAgendamento().equals("LI")) {
                        FecharMetaDetalhadoVO fecharMetaDetalhado = getFacade().getFecharMetaDetalhado().consultarPorChavePrimaria(hist.getCodigoFecharMetaDetalhado(), Uteis.NIVELMONTARDADOS_DADOSBASICOS);
                        getFacade().getFecharMeta().executarAtualizacaoMetaAtingida(fecharMetaDetalhado, 0, 1L, false, false);
                    } else {
                        FecharMetaDetalhadoVO fecharMetaDetalhado = getFacade().getFecharMetaDetalhado().consultarPorChavePrimaria(hist.getCodigoFecharMetaDetalhado(), Uteis.NIVELMONTARDADOS_DADOSBASICOS);
                        getFacade().getFecharMeta().executarAtualizacaoMetaAtingida(fecharMetaDetalhado, 0, 1L, true, true);
                    }
                }
            }
        }

    }

    public void executarRegraNegocioParaFecharMetaAlteracaoPorPassivo(Date dia, AgendaVO agenda, Integer colaboradorResponsavel, PassivoVO passivo, HistoricoContatoVO hist) throws Exception {
        Integer responsavelValidar = UtilReflection.objetoMaiorQueZero(agenda, "getColaboradorResponsavel().getCodigo()") ? agenda.getColaboradorResponsavel().getCodigo() : colaboradorResponsavel;

        // valido se agenda nao foi gravado no banco e se é para hoje
        if (agenda.getCodigo().intValue() == 0 && Uteis.getCompareData(agenda.getDataAgendamento(), dia) == 0) {
            getFacade().getFecharMeta().executarPersistenciaFechaMetaDetalhadoParaAgendamento(dia, agenda, responsavelValidar, passivo.getCodigo(), 0, 0, hist);
        } else if (Uteis.getCompareData(agenda.getDataAgendamento(), dia) == 0) {
            // Valido se o agendamento e diferente de ligação pois so com
            // essa
            // condição que é gerado alteração no banco em fecharMeta.				
            FecharMetaDetalhadoVO fecharMetaDetalhado = getFacade().getFecharMetaDetalhado().consultarFecharMetaDetalhadoPorCodigoAgenda(agenda.getCodigo().intValue(), Uteis.NIVELMONTARDADOS_DADOSBASICOS);
            if (fecharMetaDetalhado.getCodigo().intValue() == 0) {
                getFacade().getFecharMeta().executarPersistenciaFechaMetaDetalhadoParaAgendamento(dia, agenda, responsavelValidar, passivo.getCodigo(), 0, 0, hist);
            } else {
                if (hist.getAgendaVO().getTipoAgendamento().equals("LI") && !passivo.getTipoAgendamentoAnterior().equals("LI")) {
                    getFacade().getFecharMeta().executarAtualizacaoMetaRegraParaAgendamentoPorPassivo(fecharMetaDetalhado, 1L, false);
                } else if (!hist.getAgendaVO().getTipoAgendamento().equals("LI") && passivo.getTipoAgendamentoAnterior().equals("LI")) {
                    getFacade().getFecharMeta().executarAtualizacaoMetaRegraParaAgendamentoPorPassivo(fecharMetaDetalhado, 1L, true);
                }
            }
        } else {
            FecharMetaDetalhadoVO fecharMetaDetalhado = getFacade().getFecharMetaDetalhado().consultarFecharMetaDetalhadoPorCodigoAgenda(agenda.getCodigo().intValue(), Uteis.NIVELMONTARDADOS_DADOSBASICOS);
            if (fecharMetaDetalhado.getCodigo().intValue() != 0) {
                getFacade().getFecharMetaDetalhado().excluir(fecharMetaDetalhado);
                if (hist.getAgendaVO().getTipoAgendamento().equals("LI") && !passivo.getTipoAgendamentoAnterior().equals("LI")) {
                    getFacade().getFecharMeta().executarAtualizacaoMetaRegraParaAgendamentoPorPassivo(fecharMetaDetalhado, 1L, false);
                } else if (!hist.getAgendaVO().getTipoAgendamento().equals("LI") && passivo.getTipoAgendamentoAnterior().equals("LI")) {
                    getFacade().getFecharMeta().executarAtualizacaoMetaRegraParaAgendamentoPorPassivo(fecharMetaDetalhado, 1L, true);
                }
            }
        }
    }

    public Integer inicializarTotalComparecidos(List<AgendaVO> lista) {
        Integer cont = 0;
        for (AgendaVO agenda : lista) {
            if (agenda.getDataComparecimento() != null) {
                cont++;
            }
        }
        return cont;
    }

    public Integer inicializarTotalComparecidosMes(List<AgendaVO> lista) {
        Integer cont = 0;
        for (AgendaVO agenda : lista) {
            if (agenda.getDataComparecimento() != null) {
                cont++;
            }
        }
        return cont;
    }

    public Integer inicializarTotalComparecidosHistorico(List<AgendaVO> lista) {
        Integer cont = 0;
        for (AgendaVO agenda : lista) {
            if (agenda.getDataComparecimento() != null) {
                cont++;
            }
        }
        return cont;
    }

    /**
     * Responsável por obter agendamento para data anterior ou igual a hoje e
     * que não tenha sido marcado comparecimento do cliente
     *
     * <AUTHOR> 26/05/2011
     */
    public AgendaVO obterAgendamentoParaMarcarComparecimento(Integer codigoPessoa) throws Exception {
        AgendaVO agenda = new AgendaVO();
        StringBuilder sql = new StringBuilder();
        sql.append("SELECT a.* FROM AGENDA a ");
        sql.append(" left JOIN  ( ");
        sql.append("             select ");
        sql.append("                    c.pessoa, ");
        sql.append("                    c.codigo cliente, ");
        sql.append("                    p.codigo passivo, ");
        sql.append("                    i.codigo indicado ");
        sql.append("             from cliente c left join passivo p on  c.codigo = p.cliente ");
        sql.append("                            left join indicado i on  c.codigo = i.cliente ");
        sql.append("    ) c on a.cliente = c.cliente or a.indicado = c.indicado or a.passivo = c.passivo ");
        sql.append("    where c.pessoa = ? ");
        sql.append("    AND datacomparecimento IS NULL ");
        sql.append("    and a.dataagendamento = ? ");


        Declaracao dc = new Declaracao(sql.toString(), con);
        dc.setInt(1, codigoPessoa);
        dc.setDate(2, Uteis.getDataJDBC(Calendario.hoje()));
        ResultSet rs = dc.executeQuery();
        if (rs.next()) {
            agenda = montarDados(rs, Uteis.NIVELMONTARDADOS_DADOSBASICOS, con);
        }
        return agenda;
    }

    /**
     * Responsável por marcar um comparecimento de cliente, recebendo como
     * parametro apenas o codigo do mesmo e o usuario responsavel pelo
     * comparecimento
     *
     * <AUTHOR> 26/05/2011
     * @return retorna a agenda onde foi marcado comparecimento
     */
    public AgendaVO marcarComparecimentoPessoa(Integer codigoPessoa, UsuarioVO usuario, boolean contralarAcesso) throws Exception {
        AgendaVO agendaVO = obterAgendamentoParaMarcarComparecimento(codigoPessoa);
        if (!UteisValidacao.emptyNumber(agendaVO.getCodigo())) {
            alterarAgendadosComparecidoSemCommit(agendaVO, usuario, contralarAcesso);
            return agendaVO;
        }
        return null;
    }

    /**
     * Método responsável por verificar se o cliente possui um agendamento de
     * visita ou aula experimental no período informado.
     *
     * @return
     * @throws Exception
     */
    public AgendaVO verificaAgendamento(Date dataInicial, Date dataFinal, Integer codigoPessoa) throws Exception {
        StringBuilder sql = new StringBuilder();
        sql.append("SELECT * FROM agenda INNER JOIN cliente ON cliente.codigo = agenda.cliente ");
        sql.append("WHERE dataagendamento BETWEEN '" + Uteis.getDataJDBC(dataInicial) + "' AND '" + Uteis.getDataJDBC(dataFinal) + "' ");
        sql.append("AND tipoagendamento IN ('VI','AE') AND cliente.pessoa = " + codigoPessoa);
        ResultSet resultSet = Contrato.criarConsulta(sql.toString(), con);
        if (resultSet.next()) {
            return montarDados(resultSet, Uteis.NIVELMONTARDADOS_DADOSBASICOS, con);
        }
        return new AgendaVO();
    }

    public AgendaVO gerarAgendamentoContratoFechado(ContratoVO contrato) throws Exception {
        if (!UteisValidacao.emptyNumber(contrato.getAgendamentoOrigem().getCodigo())) {
            String sql = "UPDATE agenda SET datacomparecimento = ?, responsavelcomparecimento = ? WHERE codigo = ?";
            try (PreparedStatement stm = con.prepareStatement(sql)) {
                int i = 0;
                stm.setDate(++i, Uteis.getDataJDBC(contrato.getDataLancamento()));
                stm.setInt(++i, contrato.getResponsavelContrato().getCodigo());
                stm.setInt(++i, contrato.getAgendamentoOrigem().getCodigo());
                stm.execute();
            }
        }
        AgendaVO agenda = new AgendaVO();
        agenda.setCliente(contrato.getCliente());
        agenda.setDataAgendamento(contrato.getDataLancamento());
        agenda.setHora("00");
        agenda.setMinuto("00");
        agenda.setDataComparecimento(contrato.getDataLancamento());
        agenda.setTipoAgendamento("VI");
        agenda.setColaboradorResponsavel(contrato.getResponsavelContrato());
        agenda.setResponsavelCadastro(contrato.getResponsavelContrato());
        agenda.setResponsavelComparecimento(contrato.getResponsavelContrato());
        agenda.setEmpresa(contrato.getEmpresa().getCodigo());

        incluir(agenda);
        return agenda;
    }

    @Override
    public RelatorioAgendamentoTO consultarAgendamentos(Date inicio, Date fim, Date iniciolancamento,Date fimlancamento, String tipoAgenda,
                                                        UsuarioVO usuario, Integer empresa, Integer nrDiasContarResultado, Integer modalidade,
                                                        Boolean somenteConvertidos, Boolean somenteExecutadas,
                                                        Boolean aconteceram, Boolean acontecer, Boolean gymPass) throws Exception {
        return consultarAgendamentos(inicio, fim, iniciolancamento,fimlancamento,tipoAgenda, usuario, empresa, nrDiasContarResultado, modalidade,
                somenteConvertidos, somenteExecutadas, aconteceram, acontecer, gymPass, null, null);
    }

    @Override
    public RelatorioAgendamentoTO consultarAgendamentos(Date inicio, Date fim, Date iniciolancamento,Date fimlancamento, String tipoAgenda,
                                                        UsuarioVO usuario, Integer empresa, Integer nrDiasContarResultado, Integer modalidade,
                                                        Boolean somenteConvertidos, Boolean somenteExecutadas,
                                                        Boolean aconteceram, Boolean acontecer, Boolean gymPass, ColaboradorVO colaborador,
                                                        List<Integer> codigos) throws Exception {

        StringBuilder sql = new StringBuilder();

        sql.append("select * from ( \n");
        sql.append("select \n");
        sql.append("rep.datapresenca, \n");
        sql.append("COALESCE(ac.diaaula, a.datacomparecimento) as datapresencaaulacoletiva, \n");
        sql.append("a.codigo, \n");
        sql.append("a.dataagendamento, \n");
        sql.append("a.hora, \n");
        sql.append("a.minuto, \n");
        sql.append("a.tipoagendamento, \n");
        sql.append("a.colaboradorresponsavel, \n");
        sql.append("a.cliente, \n");
        sql.append("a.indicado, \n");
        sql.append("a.passivo, \n");
        sql.append("a.datacomparecimento, \n");
        sql.append("a.responsavelcadastro, \n");
        sql.append("pe.nome as clientenome, \n");
        sql.append("c.matricula, \n");
        sql.append("usur.nome as responsavelcadastronome, \n");
        sql.append("pe.codigo as codigopessoa, \n");
        sql.append("i.nomeindicado, \n");
        sql.append("p.nome as nomepassivo, \n");
        sql.append("p.telefoneresidencial as telefoneresidencialpassivo, \n");
        sql.append("p.telefonecelular as telefonecelularpassivo, \n");
        sql.append("p.telefonetrabalho as telefonetrabalhopassivo, \n");
        sql.append("us.nome as colaboradorresponsavelnome, \n");
        sql.append("con.codigo as contrato, \n");
        sql.append("s.codigocontrato, \n");
        sql.append("s.situacao as situacaoCliente, \n");
        sql.append("s.situacaocontrato as situacaoContrato, \n");
        sql.append("pescol.nome as consultor, \n");
        sql.append("resp.nome as responsavel, \n");
        sql.append("a.datalancamento, \n");
        sql.append("a.gympass, \n");
        sql.append("COALESCE(ht.professor,htrep.professor) as codigoProfessor, \n");
        sql.append("a.tipoprofessor, \n");
        sql.append("cliente_passivo.codigo as codigoclientepassivo, \n");
        sql.append("cliente_indicado.codigo as codigoclienteindicado, \n");
        sql.append("pesprof.nome as nomeprofessor, \n");
        sql.append("(select codigo from contrato c where pessoa = pe.codigo and con.codigo = c.codigo and con.datalancamento::date >= a.dataagendamento::date limit 1  ) as contratoConvertido,");
        sql.append("(select pes.nome from colaborador col inner join pessoa pes on col.pessoa = pes.codigo where col.codigo = a.codigoprofessor limit 1) as professorCRM \n");
        sql.append("from agenda a \n");
        sql.append("left join usuario us ON us.codigo = a.colaboradorresponsavel \n");
        sql.append("left join usuario usur ON usur.codigo = a.responsavelcadastro \n");
        sql.append("left join cliente c on c.codigo = a.cliente \n");
        sql.append("left join pessoa pe on pe.codigo = c.pessoa \n");
        sql.append("left join situacaoclientesinteticodw s on s.codigopessoa = c.pessoa \n");
        sql.append("left join contrato con ON con.codigo = s.codigocontrato \n");
        sql.append("left join passivo p on p.codigo = a.passivo \n");
        sql.append("left join cliente cliente_passivo on cliente_passivo.codigo = p.cliente \n");
        sql.append("left join indicado i on i.codigo = a.indicado \n");
        sql.append("left join cliente cliente_indicado on cliente_indicado.codigo = i.cliente \n");
        sql.append("left join colaborador col ON con.consultor = col.codigo \n");
        sql.append("left join pessoa pescol ON pescol.codigo = col.pessoa \n");
        sql.append("left join usuario resp ON con.responsavelcontrato = resp.codigo \n");
        sql.append("left join reposicao rep ON rep.codigo = a.reposicao \n");
        sql.append("left join alunohorarioturma aht on a.alunohorarioturma = aht.codigo \n");
        sql.append("left join aulaconfirmada ac on aht.horarioturma = ac.horario and aht.cliente = ac.cliente and aht.dia::date = ac.diaaula::date \n");
        sql.append("left join horarioturma htrep ON htrep.codigo = rep.horarioturma \n");
        sql.append("left join horarioturma ht ON ht.codigo = aht.horarioturma  \n");
        sql.append("left join colaborador prof ON prof.codigo = COALESCE(ht.professor,htrep.professor) \n");
        sql.append("left join pessoa pesprof ON pesprof.codigo = prof.pessoa \n");
        sql.append("where 1 = 1 \n");
        sql.append("AND NOT EXISTS  \n");
        sql.append("(SELECT 1 FROM periodoacessocliente pac WHERE pac.pessoa = pe.codigo AND pac.tipoTotalPass IS TRUE) \n");

        if (!UteisValidacao.emptyNumber(empresa)) {
            sql.append("AND a.empresa = ? \n");
        }

        if (colaborador != null && !UteisValidacao.emptyNumber(colaborador.getCodigo()) && !UteisValidacao.emptyString(colaborador.getPessoa().getNome())) {
            sql.append("AND ht.professor = ? \n");
        }

        if (somenteExecutadas) {
            sql.append("and (rep.datapresenca is not null or ac.diaaula is not null or a.datacomparecimento is not null)\n");
        }

        if (!UteisValidacao.emptyString(tipoAgenda)) {
            sql.append("AND a.tipoagendamento LIKE ? \n");
        }

        if (inicio != null) {
            sql.append("AND a.dataagendamento >= ? \n");
        }

        if (fim != null) {
            sql.append("AND a.dataagendamento <= ? \n");

        }

        if (iniciolancamento != null) {
            sql.append("AND a.datalancamento >= ? \n");

        }

        if (fimlancamento != null) {
            sql.append("AND a.datalancamento <= ? \n");

        }


        if (usuario != null && !UteisValidacao.emptyNumber(usuario.getCodigo()) && !UteisValidacao.emptyString(usuario.getNome())) {
            sql.append("AND a.responsavelcadastro = ? \n");
        }
        if( gymPass == false) {
            sql.append("and not gympass \n");
        }
        sql.append(") as sql \n");

        sql.append("where 1 = 1 \n");
        if (codigos != null && !codigos.isEmpty()) {
            String clientes = codigos.stream()
                    .map(Object::toString)
                    .collect(Collectors.joining(","));

            sql.append(" and (cliente in (").append(clientes).append(")\n");
            sql.append(" or codigoclientepassivo in (").append(clientes).append(")\n");
            sql.append(" or codigoclienteindicado in (").append(clientes).append(")\n)");
        }

        if (somenteConvertidos) {
            sql.append("and contratoConvertido is not null \n");
        }
        sql.append("ORDER BY dataagendamento, hora, codigo\n");

        RelatorioAgendamentoTO retorno = new RelatorioAgendamentoTO();

        try (PreparedStatement stm = con.prepareStatement(sql.toString())) {
            int i = 1;

            if (!UteisValidacao.emptyNumber(empresa)) {
                stm.setInt(i++, empresa);
            }

            if (colaborador != null && !UteisValidacao.emptyNumber(colaborador.getCodigo()) && !UteisValidacao.emptyString(colaborador.getPessoa().getNome())) {
                stm.setInt(i++, colaborador.getCodigo());
            }

            if (!UteisValidacao.emptyString(tipoAgenda)) {
                stm.setString(i++, tipoAgenda);
            }

            if (inicio != null) {
                stm.setTimestamp(i++, Uteis.getDataJDBCTimestamp(inicio));
            }

            if (fim != null) {
                stm.setTimestamp(i++, Uteis.getDataJDBCTimestamp(fim));
            }

            if (iniciolancamento != null) {
                stm.setTimestamp(i++, Uteis.getDataJDBCTimestamp(iniciolancamento));

            }

            if (fimlancamento != null) {
                stm.setTimestamp(i++, Uteis.getDataJDBCTimestamp(Calendario.getDataComUltimaHora(fimlancamento)));
            }


            if (usuario != null && !UteisValidacao.emptyNumber(usuario.getCodigo()) && !UteisValidacao.emptyString(usuario.getNome())) {
                stm.setInt(i++, usuario.getCodigo());
            }
            try (ResultSet rs = stm.executeQuery()) {
                montarDadosRelatorio(rs, retorno, aconteceram, acontecer);
            }
        }
        return retorno;
    }

    public void montarDadosRelatorio(ResultSet rs, RelatorioAgendamentoTO retorno, boolean aconteceu, boolean acontecer) throws Exception {
        List<AgendaVO> lista = new ArrayList<AgendaVO>();

        Map<Integer, ClienteVO> clientesComAgendamento = new HashMap<Integer, ClienteVO>();
        Map<Integer, ClienteVO> clientesQueExecutaramAgendamento = new HashMap<Integer, ClienteVO>();
        Map<Integer, ClienteVO> clientesConvertidos = new HashMap<Integer, ClienteVO>();

        while (rs.next()) {
            AgendaVO a = new AgendaVO();
            //        ag.codigo,
            a.setCodigo(rs.getInt("codigo"));
            a.setGymPass(rs.getBoolean("gympass"));
            a.setCodigoProfessor(rs.getInt("codigoprofessor"));
            a.setTipoProfessor(rs.getString("tipoprofessor"));
            a.setNomeProfessor(rs.getString("tipoprofessor"));
            a.setProfessorCrm(rs.getString("professorcrm"));

            a.setPresenca( (rs.getDate("datapresenca") != null) || (rs.getDate("datapresencaaulacoletiva") != null) );

            //        ag.dataagendamento,
            a.setDataAgendamento(rs.getDate("dataagendamento"));

            a.setDataLancamento(rs.getTimestamp("datalancamento"));
            //        ag.hora,
            a.setHora(rs.getString("hora"));
            //        ag.minuto,
            a.setMinuto(rs.getString("minuto"));
            //        ag.tipoagendamento,
            a.setTipoAgendamento(rs.getString("tipoagendamento"));
            //        ag.colaboradorresponsavel,
            a.getColaboradorResponsavel().setCodigo(rs.getInt("colaboradorresponsavel"));
            //        ag.datacomparecimento,
            a.setDataComparecimento(rs.getTimestamp("datacomparecimento"));
            //        ag.cliente,
            a.getCliente().setCodigo(rs.getInt("cliente"));
            a.getCliente().setMatricula(rs.getString("matricula"));
            //          ag.indicado,
            a.getIndicado().setCodigo(rs.getInt("indicado"));
            //        ag.passivo,
            a.getPassivo().setCodigo(rs.getInt("indicado"));
            //        ag.responsavelcadastro,
            a.getResponsavelCadastro().setCodigo(rs.getInt("responsavelcadastro"));
            a.getResponsavelCadastro().setNome(rs.getString("responsavelcadastronome"));
            //        mo.nome,
//            a.getModalidade().setNome(rs.getString("nome"));
            //        pes.nome as clientenome,
            a.getCliente().getPessoa().setNome(rs.getString("clientenome"));
            a.getCliente().getPessoa().setCodigo(rs.getInt("codigopessoa"));

            List<TelefoneVO> listTelefones = getFacade().getTelefone().consultarTelefones(rs.getInt("codigopessoa"), false, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
            a.getCliente().getPessoa().setTelefoneVOs(listTelefones);


            a.getPassivo().setNome(rs.getString("nomepassivo"));
            a.getPassivo().setTelefoneResidencial(rs.getString("telefoneresidencialpassivo"));
            a.getPassivo().setTelefoneCelular(rs.getString("telefonecelularpassivo"));
            a.getPassivo().setTelefoneTrabalho(rs.getString("telefonetrabalhopassivo"));

            a.getIndicado().setNomeIndicado(rs.getString("nomeindicado"));
            a.getColaboradorResponsavel().setNome(rs.getString("colaboradorresponsavelnome"));
            a.getContrato().setCodigo(rs.getInt("contrato"));
            a.setNomeProfessor(rs.getString("nomeprofessor"));
            ClienteVO cliente = new ClienteVO();
            cliente.setProfessor(rs.getString("nomeprofessor"));
            cliente.setCodigo(a.getCliente().getCodigo());
            if (cliente.getCodigo() <= 0) {
                cliente.setCodigo(rs.getInt("codigoclientepassivo"));
                if (cliente.getCodigo() <= 0) {
                    cliente.setCodigo(rs.getInt("codigoclienteindicado"));
                }
            }
            if (cliente.getCodigo() > 0) {
                clientesComAgendamento.put(cliente.getCodigo(), cliente);
                if (a.isPresenca()) {
                    clientesQueExecutaramAgendamento.put(cliente.getCodigo(), cliente);
                }
            }

            Integer anti = rs.getInt("contratoConvertido");
            if (anti > 0) {
                retorno.setTotalConvertidos(retorno.getTotalConvertidos() + 1);
                clientesConvertidos.put(cliente.getCodigo(), cliente);
                a.setConvertido(true);
            }

            if (rs.getInt("contrato") > 0 && a.getCodigoProfessor() > 0){
//            if (anti > 0 && a.getCodigoProfessor() > 0){
                retorno.setTotalProfAluConvertidos(retorno.getTotalProfAluConvertidos() + 1);
            }else if (a.getCodigoProfessor() > 0) {
                retorno.setTotalProfAluVisitantes(retorno.getTotalProfAluVisitantes() + 1);
            }

            a.getContrato().getResponsavelContrato().setNome(rs.getString("responsavel"));
            a.getContrato().getConsultor().getPessoa().setNome(rs.getString("consultor"));

            if (!UteisValidacao.emptyNumber(a.getCliente().getCodigo())) {
                ClienteSituacaoVO clienteSituacaoVO = new ClienteSituacaoVO();
                clienteSituacaoVO.getClienteVO().setCodigo(a.getCliente().getCodigo());
                clienteSituacaoVO.setSituacao(rs.getString("situacaoCliente"));
                clienteSituacaoVO.setSubordinadaSituacao(rs.getString("situacaoContrato"));
                a.getCliente().getClienteSituacaoVOs().add(clienteSituacaoVO);
            }

            Date inicio = Uteis.getDateTime(a.getDataAgendamento(), Integer.valueOf(rs.getString("hora")),
                    Integer.valueOf(rs.getString("minuto")),0);
            if(Calendario.menorComHora(inicio, Calendario.hoje())){
                if(acontecer && !aconteceu){
                    continue;
                }
                retorno.setTotalAconteceu(retorno.getTotalAconteceu() + 1);
            }else{
                if(!acontecer && aconteceu){
                    continue;
                }
                retorno.setTotalAcontecer(retorno.getTotalAcontecer() + 1);
            }

            if (a.getTipoAgendamento().equals(TipoAgendaEnum.LIGACAO.getId())) {
                retorno.setTotalLigacoes(retorno.getTotalLigacoes() + 1);
                if (anti > 0) {
                    retorno.setTotalConvertidosLigacoes(retorno.getTotalConvertidosLigacoes() + 1);
                }
            } else if (a.getTipoAgendamento().equals(TipoAgendaEnum.VISITA.getId())) {
                retorno.setTotalVisita(retorno.getTotalVisita() + 1);
                if (anti > 0) {
                    retorno.setTotalConvertidosVisita(retorno.getTotalConvertidosVisita() + 1);
                }
            } else if (a.getTipoAgendamento().equals(TipoAgendaEnum.AULA_EXPERIMENTAL.getId())) {
                retorno.setTotalAulaExperimental(retorno.getTotalAulaExperimental() + 1);
                if(a.getDataComparecimento() != null){
                    retorno.setTotalComparecidos(retorno.getTotalComparecidos() + 1);
                }
                if (anti > 0) {
                    retorno.setTotalConvertidosAulaExperimental(retorno.getTotalConvertidosAulaExperimental() + 1);
                }
                if(a.isPresenca()){
                    retorno.setTotalExecutadas(retorno.getTotalExecutadas() + 1);
                }

            } else if (a.getTipoAgendamento().equals(TipoAgendaEnum.AULA_DIARIA.getId())) {
                retorno.setTotalDiarias(retorno.getTotalDiarias() + 1);
                if (anti > 0) {
                    retorno.setTotalConvertidosDiarias(retorno.getTotalConvertidosDiarias() + 1);
                }
            }

        lista.add(a);
        }
        retorno.setClientesComAgendamento(new ArrayList<ClienteVO>(clientesComAgendamento.values()));
        retorno.setClientesQueExecutaramAgendamento(new ArrayList<ClienteVO>(clientesQueExecutaramAgendamento.values()));
        retorno.setClientesConvertidos(new ArrayList<ClienteVO>(clientesConvertidos.values()));
        retorno.setLista(Ordenacao.ordenarLista(lista, "nomePessoa"));

    }

    public void preencherDadosAgendaIndicado(AgendaVO agenda, HistoricoContatoVO historicoContato) throws Exception {
        executarPreenchimentoDadosAgendaIndicado(agenda, historicoContato);
        historicoContato.setAgendaVO(agenda);
    }

    public AgendaVO consultarPorConvite(Integer codigoConvite, Integer codigoModalidade, Date dataAgendamento, int nivelMontarDados)throws Exception{
        String sql = "select * from agenda where conviteAulaExperimental = ? and dataAgendamento = ? and modalidade = ?";
        try (PreparedStatement pst = con.prepareStatement(sql)) {
            pst.setInt(1, codigoConvite);
            pst.setDate(2, Uteis.getDataJDBC(dataAgendamento));
            pst.setInt(3, codigoModalidade);
            try (ResultSet rs = pst.executeQuery()) {
                if (rs.next())
                    return montarDados(rs, nivelMontarDados, con);
            }
        }
        return null;
    }

    public void excluirAgendaConviteAulaExperimental(Integer codigoConvite,Integer codigoModalidade, Date dataAgendamento)throws Exception{
        String sql = "delete from agenda where conviteAulaExperimental = ? and dataAgendamento = ? and modalidade = ?";
        try (PreparedStatement pst = con.prepareStatement(sql)) {
            pst.setInt(1, codigoConvite);
            pst.setDate(2, Uteis.getDataJDBC(dataAgendamento));
            pst.setInt(3, codigoModalidade);
            pst.execute();
        }
    }

    @Override
    public List<AgendaVO> consultarPorColaboradorResponsavel(Integer codigoColaborador, Date dataAgendamento, int nivelmontardados) throws  Exception{
        String sql = "SELECT a.* FROM agenda a INNER JOIN usuario u ON u.codigo = a.colaboradorresponsavel INNER JOIN cliente cli ON cli.codigo = a.cliente INNER JOIN pessoa p ON p.codigo = cli.pessoa WHERE u.colaborador = ? AND (((a.dataagendamento::date|| ' ' || a.hora || ':' || a.minuto)::timestamp) >= ?) ORDER BY p.nome ";
        try (PreparedStatement ps = con.prepareStatement(sql)) {
            ps.setInt(1, codigoColaborador);
            ps.setDate(2, Uteis.getDataJDBC(dataAgendamento));
            try (ResultSet rs = ps.executeQuery()) {
                return montarDadosConsulta(rs, nivelmontardados, con);
            }
        }
    }

    public Boolean clientePossuiAgendamentosColaborador(Integer codigoColaborador, Integer codigoCliente, Date dataAgendamento) throws  Exception{
        String sql = "SELECT count(1) as qtd FROM agenda a INNER JOIN usuario u ON u.codigo = a.colaboradorresponsavel  AND (((a.dataagendamento::date|| ' ' || a.hora || ':' || a.minuto)::timestamp) >= ?) AND a.cliente = ? AND u.colaborador = ?";
        Boolean possui;
        try (PreparedStatement ps = con.prepareStatement(sql)) {
            ps.setDate(1, Uteis.getDataJDBC(dataAgendamento));
            ps.setInt(2, codigoCliente);
            ps.setInt(3, codigoColaborador);
            try (ResultSet rs = ps.executeQuery()) {
                possui = rs.next();
                if (possui) {
                    int qtd = rs.getInt("qtd");
                    possui = qtd > 0;
                }
            }
        }
        return possui;
    }

    @Override
    public void alterarConsultorResponsavelAgenda(Integer codigoColaboradorResponsavelAntigo, Integer codigoColaboradorResponsavelNovo, Integer codigoCliente, Date data) throws Exception {
        String sql = "UPDATE agenda a SET colaboradorresponsavel = ? WHERE colaboradorresponsavel = ? and (((dataagendamento::date|| ' ' || hora || ':' || minuto)::timestamp) >= ?) AND cliente = ? ;";
        try (PreparedStatement ps = con.prepareStatement(sql)) {
            ps.setInt(1, codigoColaboradorResponsavelNovo);
            ps.setInt(2, codigoColaboradorResponsavelAntigo);
            ps.setDate(3, Uteis.getDataJDBC(data));
            ps.setInt(4, codigoCliente);
            ps.executeUpdate();
        }
    }

    @Override
    public List<Integer> consultarPorColaboradoresResponsaveis(List<Integer> codigosColaboradores, final Integer cliente, final Integer empresa, Date dataAgendamento, int nivelmontardadosDados) throws  Exception{
        String sql = "SELECT DISTINCT colEmpresa.codigo as colaborador FROM agenda a "
                + " INNER JOIN usuario u ON u.codigo = a.colaboradorresponsavel inner join colaborador colUsuario on u.colaborador = colUsuario.codigo   inner join colaborador colEmpresa on colUsuario.pessoa = colEmpresa.pessoa and colEmpresa.empresa = ?"
                + " WHERE a.cliente = ? AND  colEmpresa.codigo = ANY (?) AND (((a.dataagendamento::date|| ' ' || a.hora || ':' || a.minuto)::timestamp) >= ?) ";
        List<Integer> colaboradoresResponsaveis;
        try (PreparedStatement ps = con.prepareStatement(sql)) {
            ps.setInt(1, empresa);
            ps.setInt(2, cliente);
            ps.setArray(3, con.createArrayOf("int", codigosColaboradores.toArray()));
            ps.setDate(4, Uteis.getDataJDBC(dataAgendamento));
            try (ResultSet rs = ps.executeQuery()) {
                colaboradoresResponsaveis = new ArrayList<Integer>();
                while (rs.next()) {
                    colaboradoresResponsaveis.add(rs.getInt("colaborador"));
                }
            }
        }
        return colaboradoresResponsaveis;
    }

    @Override
    public List<Integer> consultarCodigoAgendaFuturasPorVinculos(List<VinculoVO> vinculos, Date dataAgendamento, Integer empresa) throws  Exception{
        StringBuilder sql = new StringBuilder();
        sql.append(" SELECT DISTINCT a.codigo");
        sql.append(" FROM agenda a");
        sql.append(" INNER JOIN usuario u ON u.codigo = a.colaboradorresponsavel");
        sql.append(" inner join colaborador cusuario on cusuario.codigo = u.colaborador ");
        sql.append(" inner join pessoa p on p.codigo = cusuario.pessoa ");
        sql.append(" inner join colaborador cempresa on cempresa.pessoa = p.codigo and cempresa.empresa = ? ");
        sql.append(" INNER JOIN vinculo v ON v.colaborador = cempresa.codigo AND a.cliente = v.cliente");
        sql.append(" WHERE (((a.dataagendamento::date|| ' ' || a.hora || ':' || a.minuto)::timestamp) >= ?)");
        sql.append(" AND v.codigo = ANY(?)");
        List<Integer> codigoAgendas;
        try (PreparedStatement ps = con.prepareStatement(sql.toString())) {
            ps.setInt(1, empresa);
            ps.setDate(2, Uteis.getDataJDBC(dataAgendamento));
            List<Integer> codigos = new ArrayList<Integer>(vinculos.size());
            for (VinculoVO v : vinculos) codigos.add(v.getCodigo());
            ps.setArray(3, con.createArrayOf("int", codigos.toArray()));
            try (ResultSet rs = ps.executeQuery()) {
                codigoAgendas = new ArrayList<Integer>();
                while (rs.next()) codigoAgendas.add(rs.getInt("codigo"));
            }
        }
        return codigoAgendas;
    }

    @Override
    public void alterarConsultorResponsavelAgenda(List<Integer> agendasTransferir, UsuarioVO usuario) throws  Exception{
        String sql ="UPDATE agenda SET colaboradorresponsavel = ? WHERE codigo = ANY(?);";
        try (PreparedStatement ps = con.prepareStatement(sql)) {
            ps.setInt(1, usuario.getCodigo());
            ps.setArray(2, con.createArrayOf("int", agendasTransferir.toArray()));
            ps.executeUpdate();
        }
    }

    @Override
    public void alterarConsultorResponsavelAgenda(Integer codigoColaboradorResponsavelAntigo, Integer codigoColaboradorResponsavelNovo, Date data) throws Exception {
        String sql = "UPDATE agenda a SET colaboradorresponsavel = ? WHERE colaboradorresponsavel = ? and (((dataagendamento::date|| ' ' || hora || ':' || minuto)::timestamp) >= ?);";
        try (PreparedStatement ps = con.prepareStatement(sql)) {
            ps.setInt(1, codigoColaboradorResponsavelNovo);
            ps.setInt(2, codigoColaboradorResponsavelAntigo);
            ps.setDate(3, Uteis.getDataJDBC(data));
            ps.executeUpdate();
        }
    }

    public void executarPreenchimentoDadosAgendaIndicado(AgendaVO agenda, HistoricoContatoVO historicoContatoVO) throws Exception {
        agenda.setColaboradorResponsavel(historicoContatoVO.getColaboradorResponsavel());
        agenda.setResponsavelCadastro(historicoContatoVO.getResponsavelCadastro());
        agenda.getIndicado().setCodigo(historicoContatoVO.getIndicadoVO().getCodigo());
        if (!agenda.getModalidade().getNome().equals("")) {
            agenda.setModalidade(getFacade().getModalidade().consultarPorNomeModalidade(agenda.getModalidade().getNome(), false, Uteis.NIVELMONTARDADOS_TELACONSULTA));
        }
        if (agenda.getCodigo() == 0) {
            incluir(agenda);
        } else {
            alterar(agenda);
        }
    }


    public void gravarAgendamentoAulaExperimental(ReposicaoVO reposicaoVO, boolean gymPass) throws Exception{
        gravarAgendamentoAulaExperimental(reposicaoVO, gymPass, TipoAgendaEnum.AULA_EXPERIMENTAL);
    }

    public void gravarAgendamentoAulaExperimental(ReposicaoVO reposicaoVO, boolean gymPass, TipoAgendaEnum tipoAgendaEnum) throws Exception{
        Cliente clienteDao = new Cliente(con);

        StringBuilder sql = new StringBuilder();
        sql.append(" select ht.horainicial, t.modalidade from reposicao r \n");
        sql.append(" inner join horarioturma ht on r.horarioturma = ht.codigo \n");
        sql.append(" inner join turma t on t.codigo = ht.turma \n");
        sql.append(" where r.codigo = ").append(reposicaoVO.getCodigo());
        ResultSet rs = criarConsulta(sql.toString(), con);
        if(rs.next()){
            String hora = rs.getString("horainicial");
            String[] split = hora.split(":");
            AgendaVO agendamento = new AgendaVO();
            agendamento.setCliente(reposicaoVO.getCliente());
            agendamento.setDataAgendamento(reposicaoVO.getDataReposicao());
            agendamento.setGymPass(gymPass);
            agendamento.setHora(split[0]);
            agendamento.setMinuto(split[1]);
            agendamento.setDataLancamento(Calendario.hoje());
            agendamento.setTipoAgendamento(tipoAgendaEnum.getId());
            agendamento.setModalidade(new ModalidadeVO());
            agendamento.getModalidade().setCodigo(rs.getInt("modalidade"));

            ResultSet rsUsu = SuperFacadeJDBC.criarConsulta("SELECT colaborador from vinculo where " +
                    "tipovinculo = 'CO' and cliente = " + reposicaoVO.getCliente().getCodigo(), con);
            if(rsUsu.next()){
                Usuario usuarioDao = new Usuario(con);
                UsuarioVO colaborador = usuarioDao.consultarPorCodigoColaborador(rsUsu.getInt("colaborador"),
                        Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA);
                agendamento.setColaboradorResponsavel(colaborador);
            }else{
                agendamento.setColaboradorResponsavel(reposicaoVO.getUsuario());
            }
            agendamento.setResponsavelCadastro(reposicaoVO.getUsuario());
            agendamento.setCliente(reposicaoVO.getCliente());
            agendamento.setEmpresa(reposicaoVO.getCliente().getEmpresa().getCodigo());
            agendamento.setReposicao(reposicaoVO.getCodigo());
            incluir(agendamento);
            HistoricoContatoVO his = new HistoricoContatoVO();
            his.setClienteVO(reposicaoVO.getCliente());
            his.setAgendaVO(agendamento);
            executarRegraNegocioParaFecharMetaInclusao(null, agendamento, null, 0,0,reposicaoVO.getCliente().getCodigo(), his);
            if(UteisValidacao.notEmptyNumber(reposicaoVO.getProdutoFreePass()) && !tipoAgendaEnum.equals(TipoAgendaEnum.AULA_DIARIA)){
                reposicaoVO.getCliente().setFreePass(new ProdutoVO());
                reposicaoVO.getCliente().getFreePass().setCodigo(reposicaoVO.getProdutoFreePass());
                clienteDao.incluirFreePassSemCommit(reposicaoVO.getCliente(), reposicaoVO.getDataReposicao(), null,null, true, null);
                ClienteVO clienteVO = clienteDao.consultarPorChavePrimaria(reposicaoVO.getCliente().getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS);
                Usuario usuarioDao = new Usuario(con);
                UsuarioVO usuarioVO = usuarioDao.consultarPorChavePrimaria(reposicaoVO.getUsuario().getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS);
                gravarLogIncluirFreePass(usuarioVO, clienteVO, reposicaoVO.getProdutoFreePass(), agendamento.getDataAgendamento());
            }
        }
    }
    
    public AgendaVO consultarPorReposicao(Integer codigoReposicao, int nivelMontarDados) throws Exception {
        String sqlStr = "SELECT * FROM Agenda WHERE reposicao =" +codigoReposicao;
        try (Statement stm = con.createStatement()) {
            try (ResultSet tabelaResultado = stm.executeQuery(sqlStr)) {
                if (!tabelaResultado.next()) {
                    throw new ConsistirException("Dados Não Encontrados ( Agenda ).");
                }
                return (montarDados(tabelaResultado, nivelMontarDados, con));
            }
        }
    }
    
    public void alterarAgendamentoPassivoIndicadoParaCliente(Integer cliente, Integer passivo, Integer indicado) throws Exception{
        String sqlStr = "SELECT * FROM Agenda WHERE ";
        if(!UteisValidacao.emptyNumber(passivo)){
            sqlStr += " passivo = " + passivo;
        } else if (!UteisValidacao.emptyNumber(indicado)){
             sqlStr += " indicado = " + indicado;
        } else {
            return;
        }       
        sqlStr += " and dataagendamento = '" + Uteis.getDataFormatoBD(Calendario.hoje()) +"'" ;
        try (Statement stm = con.createStatement()) {
            try (ResultSet tabelaResultado = stm.executeQuery(sqlStr)) {
                while (tabelaResultado.next()) {
                    SuperFacadeJDBC.executarConsultaUpdate("UPDATE agenda set cliente = " + cliente + ", passivo = null, indicado = null where codigo = " + tabelaResultado.getInt("codigo"), con);
                    SuperFacadeJDBC.executarConsultaUpdate("UPDATE fecharmetadetalhado set cliente = " + cliente + ", passivo = null, indicado = null where codigoorigem = " + tabelaResultado.getInt("codigo") + " and origem = 'AGENDA'", con);

                }
            }
        }
    }

    public void gravarAgendamentoAulaColetivaExperimental(Date dia, String hora, Integer cliente,
                                                          Integer modalidade,
                                                          Integer empresa,
                                                          Integer produtoFreePass,
                                                          Integer usuario,
                                                          Integer alunohorarioturma,
                                                          TipoAgendaEnum tipoAgendaEnum) throws Exception {
        Cliente clienteDao = new Cliente(con);
        try {
            hora = hora.contains(" ") ? hora.split(" ")[1] : hora;
        }catch (Exception e){
            Uteis.logar(e, Agenda.class);
        }
        ResultSet set = criarConsulta("select tokengympass from periodoacessocliente pa \n" +
                "inner join cliente cli on cli.pessoa = pa.pessoa \n" +
                "where (tipoacesso = 'DI' or tipoacesso = 'PL') and tokengympass <> ''\n" +
                "and '" + Calendario.getDataAplicandoFormatacao(dia, "yyyy-MM-dd") + "' between datainicioacesso and datafinalacesso\n" +
                "and cli.codigo = " + cliente, con);
        boolean gymPass = set.next();

        StringBuilder sql = new StringBuilder();

        String[] split = hora.split(":");
        AgendaVO agendamento = new AgendaVO();
        ClienteVO clienteVO = clienteDao.consultarPorChavePrimaria(cliente, Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA);
        agendamento.setCliente(clienteVO);
        agendamento.setDataAgendamento(dia);
        agendamento.setGymPass(gymPass);
        agendamento.setHora(split[0]);
        agendamento.setMinuto(split[1]);
        agendamento.setDataLancamento(Calendario.hoje());
        agendamento.setTipoAgendamento(tipoAgendaEnum.getId());
        agendamento.setModalidade(new ModalidadeVO());
        agendamento.getModalidade().setCodigo(modalidade);
        agendamento.setAlunohorarioturma(alunohorarioturma);
        Usuario usuarioDao = new Usuario(con);
        UsuarioVO usuarioVO = null;
        if(usuario > 0){
            usuarioVO  = usuarioDao.consultarPorChavePrimaria(usuario, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
        }
        ResultSet rsUsu = SuperFacadeJDBC.criarConsulta("SELECT colaborador from vinculo where " +
                "tipovinculo = 'CO' and cliente = " + cliente, con);
        if (rsUsu.next()) {
            UsuarioVO colaborador = usuarioDao.consultarPorCodigoColaborador(rsUsu.getInt("colaborador"),
                    Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA);
            agendamento.setColaboradorResponsavel(colaborador);
        } else {
            agendamento.setColaboradorResponsavel(usuarioVO);
        }
        agendamento.setResponsavelCadastro(usuarioVO);
        agendamento.setCliente(clienteVO);
        agendamento.setEmpresa(empresa);
        incluir(agendamento);
        HistoricoContatoVO his = new HistoricoContatoVO();
        his.setClienteVO(clienteVO);
        his.setAgendaVO(agendamento);
        executarRegraNegocioParaFecharMetaInclusao(null, agendamento, null, 0, 0, cliente, his);
        if (!tipoAgendaEnum.equals(TipoAgendaEnum.AULA_DIARIA)) {
            clienteVO.setFreePass(new ProdutoVO());
            clienteVO.getFreePass().setCodigo(produtoFreePass);
            clienteDao.incluirFreePassSemCommit(clienteVO, dia, null, null, true, null);
            gravarLogIncluirFreePass(usuarioVO, clienteVO, produtoFreePass, agendamento.getDataAgendamento());
        }

    }

    public void gravarLogIncluirFreePass(UsuarioVO usuarioVO, ClienteVO clienteVO, Integer codProdutoFreePass, Date dataAgendamento) throws Exception {
        try {
            ProdutoVO produtoVO = consultarProduto(codProdutoFreePass);
            LogVO obj = new LogVO();
            obj.setPessoa(clienteVO.getPessoa().getCodigo());
            obj.setNomeEntidade("FREEPASS" + "");
            obj.setChavePrimaria(clienteVO.getPessoa().getCodigo().toString());
            obj.setNomeEntidadeDescricao("FreePass");
            obj.setOperacao("INCLUSÃO " + (usuarioVO == null ? "Usuário existente somente no treino" : usuarioVO.getNome()));
            obj.setResponsavelAlteracao(usuarioVO == null ? "Usuário existente somente no treino" : usuarioVO.getNome());
            if (usuarioVO != null) {
                obj.setUserOAMD(usuarioVO.getUserOamd());
            }
            obj.setNomeCampo("FREEPASS");
            obj.setValorCampoAlterado(obj.getValorCampoAlterado()
                    + "\n\rNome do Cliente = " + clienteVO.getPessoa().getNome()
                    + "\n\rData do inicio do FreePass = " + dataAgendamento
                    + "\n\rData Final do FreePass= " + Uteis.somarDias(dataAgendamento, produtoVO.getNrDiasVigencia())
                    + "\n\rProduto = " + produtoVO.getDescricao() + "\n\r");
            this.logDAO.incluirSemCommit(obj);
        } catch (Exception ex) {
            ex.printStackTrace();
        }
    }

    public ProdutoVO consultarProduto(Integer codigo) {
        try(ResultSet rs = SuperFacadeJDBC.criarConsulta("SELECT codigo, descricao, nrdiasvigencia FROM produto WHERE codigo = " + codigo, con)) {
            if (rs.next()) {
                ProdutoVO produtoVO = new ProdutoVO();
                produtoVO.setCodigo(rs.getInt("codigo"));
                produtoVO.setDescricao(rs.getString("descricao"));
                produtoVO.setNrDiasVigencia(rs.getInt("nrdiasvigencia"));
                return produtoVO;
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        return null;
    }

}

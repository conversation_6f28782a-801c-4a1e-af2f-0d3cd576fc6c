package negocio.facade.jdbc.crm;

import br.com.pactosolucoes.enumeradores.FasesCRMEnum;
import br.com.pactosolucoes.enumeradores.OrigemSistemaEnum;
import br.com.pactosolucoes.turmas.servico.impl.TurmasServiceImpl;
import negocio.comuns.arquitetura.UsuarioVO;
import negocio.comuns.basico.ClienteVO;
import negocio.comuns.basico.EmpresaVO;
import negocio.comuns.crm.*;
import negocio.comuns.plano.HorarioTurmaVO;
import negocio.comuns.utilitarias.Calendario;
import negocio.comuns.utilitarias.ConsistirException;
import negocio.comuns.utilitarias.Ordenacao;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisValidacao;
import negocio.facade.jdbc.arquitetura.SuperEntidade;
import negocio.facade.jdbc.arquitetura.Usuario;
import negocio.facade.jdbc.basico.Cliente;
import negocio.facade.jdbc.basico.Empresa;
import negocio.interfaces.crm.PassivoInterfaceFacade;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.sql.Statement;
import java.sql.Types;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Date;
import java.util.List;

/**
 * Classe de persistência que encapsula todas as operações de manipulação dos
 * dados da classe <code>PassivoVO</code>. Responsável por implementar operações
 * como incluir, alterar, excluir e consultar pertinentes a classe
 * <code>PassivoVO</code>. Encapsula toda a interação com o banco de dados.
 *
 * @see PassivoVO
 * @see SuperEntidade
 */
public class Passivo extends SuperEntidade implements PassivoInterfaceFacade {

    public Passivo() throws Exception {
        super();
    }

    public Passivo(Connection con) throws Exception {
        super(con);
    }

    /**
     * Operação responsável por retornar um novo objeto da classe
     * <code>PassivoVO</code>.
     */
    public PassivoVO novo() throws Exception {
        incluirCRM(getIdEntidade());
        PassivoVO obj = new PassivoVO();
        return obj;
    }

    /**
     * Rotina responsável por gravar no BD os dados editados de um novo objeto
     * da classe <code>Passivo</code>. Caso o objeto seja novo (ainda não
     * gravado no BD) é acionado a operação <code>incluir()</code>. Caso
     * contrário é acionado o <code>alterar()</code>. Se houver alguma
     * inconsistência o objeto não é gravado, sendo re-apresentado para o
     * usuário juntamente com uma mensagem de erro.
     *
     * @throws Exception
     */
    public void salvar(PassivoVO obj, HistoricoContatoVO hist, String agendaOuObjecao, Integer empresa,
                       ConfiguracaoSistemaCRMVO configCRM, Integer codigoAula, TipoMetaCRMTO tipoMetaSelec, UsuarioVO usuarioLogado, List<HorarioTurmaVO> aulasAgenda) throws Exception {
        if (!obj.getEvento().getDescricao().equals("")) {
            obj.setEvento(getFacade().getEvento().consultarPorNomeEvento(obj.getEvento().getDescricao(), Integer.SIZE, Uteis.NIVELMONTARDADOS_TELACONSULTA));
        }
        if (obj.isNovoObj()) {
            incluirPassivoEDependentes(obj, hist, agendaOuObjecao, empresa, configCRM, codigoAula, tipoMetaSelec, usuarioLogado, aulasAgenda);
        } else {
            alterarPassivoEDependentes(obj, hist, agendaOuObjecao, empresa);
        }
    }

    public void alterarSomentePassivo(PassivoVO obj) throws Exception {
        try {
            con.setAutoCommit(false);
            alterar(obj);
            con.commit();
        } catch (Exception e) {
            con.rollback();
            con.setAutoCommit(true);
            throw e;
        } finally {
            con.setAutoCommit(true);
        }

    }

    public void incluirPassivoEDependentes(PassivoVO obj, HistoricoContatoVO hist, String agendaOuObjecao, Integer empresa,
                                           ConfiguracaoSistemaCRMVO configCRM, Integer codigoAula, TipoMetaCRMTO tipoMetaSelec, UsuarioVO usuarioLogado, List<HorarioTurmaVO> aulasAgenda) throws Exception {
        try {
            con.setAutoCommit(false);
            incluir(obj,false);
            incluirDependencias(obj, hist, agendaOuObjecao, empresa, configCRM, codigoAula, tipoMetaSelec, usuarioLogado, aulasAgenda);
            con.commit();
            obj.setNovoObj(false);
        } catch (Exception e) {
            hist.getAgendaVO().setCodigo(0);
            hist.getObjecaoVO().setCodigo(0);
            obj.setNovoObj(true);
            con.rollback();
            con.setAutoCommit(true);
            throw e;
        } finally {
            con.setAutoCommit(true);
        }

    }

    public void alterarPassivoEDependentes(PassivoVO obj, HistoricoContatoVO hist, String agendaOuObjecao, Integer empresa) throws Exception {
        int codigoAgenda = hist.getAgendaVO().getCodigo();
        int codigoObjecao = hist.getObjecaoVO().getCodigo();
        try {
            con.setAutoCommit(false);
            alterar(obj);
            alterarDependencias(obj, hist, agendaOuObjecao, empresa);
            con.commit();
        } catch (Exception e) {
            hist.getAgendaVO().setCodigo(codigoAgenda);
            hist.getObjecaoVO().setCodigo(codigoObjecao);
            con.rollback();
            con.setAutoCommit(true);
            throw e;
        } finally {
            con.setAutoCommit(true);
        }

    }

    /**
     * Operação responsável por incluir no banco de dados um objeto da classe
     * <code>PassivoVO</code>. Primeiramente valida os dados (
     * <code>validarDados</code>) do objeto. Verifica a conexão com o banco de
     * dados e a permissão do usuário para realizar esta operacão na entidade.
     * Isto, através da operação <code>incluir</code> da superclasse.
     *
     * @param obj Objeto da classe <code>PassivoVO</code> que será gravado no
     *            banco de dados.
     * @throws Exception Caso haja problemas de conexão, restrição de acesso ou
     *                   validação de dados.
     */
    public void incluir(PassivoVO obj, boolean validaRD) throws Exception {
        incluir(obj, false, validaRD);
    }

    public void incluir(PassivoVO obj, boolean wordPress, boolean validaRD) throws Exception {
        incluir(obj, wordPress, validaRD, false);
    }

    public void incluir(PassivoVO obj, boolean wordPress, boolean validaRD, boolean bitrix) throws Exception {

        try {
            PassivoVO.validarDados(obj, wordPress, validaRD, bitrix);
            incluirCRM(getIdEntidade());
            obj.realizarUpperCaseDados();
            String sql = "INSERT INTO Passivo( nome, telefoneResidencial, telefoneCelular, telefoneTrabalho, responsavelCadastro, dia, colaboradorResponsavel, email, evento, empresa, contrato,lead, origemSistema, metaextra, observacao) VALUES ( ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)";
            PreparedStatement sqlInserir = con.prepareStatement(sql);
            sqlInserir.setString(1, obj.getNome());
            sqlInserir.setString(2, obj.getTelefoneResidencial());
            sqlInserir.setString(3, obj.getTelefoneCelular());
            sqlInserir.setString(4, obj.getTelefoneTrabalho());
            if (obj.getResponsavelCadastro().getCodigo().intValue() != 0) {
                sqlInserir.setInt(5, obj.getResponsavelCadastro().getCodigo().intValue());
            } else {
                sqlInserir.setNull(5, 0);
            }
            sqlInserir.setTimestamp(6, Uteis.getDataJDBCTimestamp(obj.getDia()));
            if (obj.getColaboradorResponsavel().getCodigo().intValue() != 0) {
                sqlInserir.setInt(7, obj.getColaboradorResponsavel().getCodigo().intValue());
            } else {
                sqlInserir.setNull(7, 0);
            }
            sqlInserir.setString(8, obj.getEmail());
            if (obj.getEvento().getCodigo().intValue() != 0) {
                sqlInserir.setInt(9, obj.getEvento().getCodigo().intValue());
            } else {
                sqlInserir.setNull(9, 0);
            }
            if (!UteisValidacao.emptyNumber(obj.getEmpresaVO().getCodigo())) {
                sqlInserir.setInt(10, obj.getEmpresaVO().getCodigo());
            } else {
                sqlInserir.setNull(10, 0);
            }
            if (!UteisValidacao.emptyNumber(obj.getContrato())) {
                sqlInserir.setInt(11, obj.getContrato());
            } else {
                sqlInserir.setNull(11, 0);
            }
            sqlInserir.setBoolean(12, obj.isLead());
            sqlInserir.setInt(13, obj.getOrigemSistemaEnum().getCodigo());
            sqlInserir.setBoolean(14, obj.getMetaExtra());
            sqlInserir.setString(15, obj.getObservacao());

            sqlInserir.execute();
            obj.setCodigo(obterValorChavePrimariaCodigo());
            resolveNomeConsulta(obj);
        }catch (Exception e){
            Uteis.logarDebug("ERRO AO INCLUIR PASSIVO: " + e.getMessage());
        }

    }

    /**
     * Operação responsável por alterar no BD os dados de um objeto da classe
     * <code>PassivoVO</code>. Sempre utiliza a chave primária da classe como
     * atributo para localização do registro a ser alterado. Primeiramente
     * valida os dados (<code>validarDados</code>) do objeto. Verifica a conexão
     * com o banco de dados e a permissão do usuário para realizar esta operacão
     * na entidade. Isto, através da operação <code>alterar</code> da
     * superclasse.
     *
     * @param obj Objeto da classe <code>PassivoVO</code> que será alterada no
     *            banco de dados.
     * @throws Exception Caso haja problemas de conexão, restrição de acesso ou
     *                   validação de dados.
     */
    public void alterar(PassivoVO obj) throws Exception {
        alterar(obj, false);
    }

    public void alterar(PassivoVO obj, Boolean bitrix) throws Exception {
        PassivoVO.validarDados(obj,false, false, bitrix);
        alterarCRM(getIdEntidade());
        obj.realizarUpperCaseDados();
        String sql = "UPDATE Passivo set nome=?, telefoneResidencial=?, telefoneCelular=?," +
                " telefoneTrabalho=?, responsavelCadastro=?, dia=?, colaboradorResponsavel=?," +
                " email=?, evento=?, empresa = ?, contrato = ?, lead=?, origemSistema=?, observacao=? WHERE ((codigo = ?))";
        PreparedStatement sqlAlterar = con.prepareStatement(sql);
        sqlAlterar.setString(1, obj.getNome());
        if (obj.getTelefoneResidencial() != null)
            sqlAlterar.setString(2, obj.getTelefoneResidencial());
        else
            sqlAlterar.setNull(2, Types.NULL);
        if (obj.getTelefoneCelular() != null)
            sqlAlterar.setString(3, obj.getTelefoneCelular());
        else
            sqlAlterar.setNull(3, Types.NULL);
        if (obj.getTelefoneTrabalho() != null)
            sqlAlterar.setString(4, obj.getTelefoneTrabalho());
        else
            sqlAlterar.setNull(4, Types.NULL);
        if (obj.getResponsavelCadastro().getCodigo() != 0) {
            sqlAlterar.setInt(5, obj.getResponsavelCadastro().getCodigo());
        } else {
            sqlAlterar.setNull(5, 0);
        }
        sqlAlterar.setTimestamp(6, Uteis.getDataJDBCTimestamp(obj.getDia()));
        if (obj.getColaboradorResponsavel().getCodigo() != 0) {
            sqlAlterar.setInt(7, obj.getColaboradorResponsavel().getCodigo());
        } else {
            sqlAlterar.setNull(7, 0);
        }
        sqlAlterar.setString(8, obj.getEmail());
        if (obj.getEvento().getCodigo() != 0) {
            sqlAlterar.setInt(9, obj.getEvento().getCodigo());
        } else {
            sqlAlterar.setNull(9, 0);
        }
        if (!UteisValidacao.emptyNumber(obj.getEmpresaVO().getCodigo())) {
            sqlAlterar.setInt(10, obj.getEmpresaVO().getCodigo());
        } else {
            sqlAlterar.setNull(10, 0);
        }
        if (!UteisValidacao.emptyNumber(obj.getContrato())) {
            sqlAlterar.setInt(11, obj.getContrato());
        } else {
            sqlAlterar.setNull(11, 0);
        }
         sqlAlterar.setBoolean(12, obj.isLead());
         sqlAlterar.setInt(13, obj.getOrigemSistemaEnum().getCodigo());
         sqlAlterar.setString(14, obj.getObservacao());
        
        sqlAlterar.setInt(15, obj.getCodigo());
        sqlAlterar.execute();
        resolveNomeConsulta(obj);
    }

    public void executarAlteracaoPorCadastroCliente(Integer codigo, Integer cliente) throws Exception {
        alterarCRM(getIdEntidade());
        String sql = "UPDATE Passivo set cliente=? WHERE ((codigo = ?))";
        PreparedStatement sqlAlterar = con.prepareStatement(sql);
        sqlAlterar.setInt(1, cliente);
        sqlAlterar.setInt(2, codigo);
        sqlAlterar.execute();
        getFacade().getLead().executarAlteracaoClientePassivo(codigo, cliente);
    }

    /**
     * Operação responsável por excluir no BD um objeto da classe
     * <code>PassivoVO</code>. Sempre localiza o registro a ser excluído através
     * da chave primária da entidade. Primeiramente verifica a conexão com o
     * banco de dados e a permissão do usuário para realizar esta operacão na
     * entidade. Isto, através da operação <code>excluir</code> da superclasse.
     *
     * @param obj Objeto da classe <code>PassivoVO</code> que será removido no
     *            banco de dados.
     * @throws Exception Caso haja problemas de conexão ou restrição de acesso.
     */
    public void excluirPassivo(PassivoVO obj) throws Exception {
        excluirCRM(getIdEntidade());
        String sql = "DELETE FROM Passivo WHERE ((codigo = ?))";
        PreparedStatement sqlExcluir = con.prepareStatement(sql);
        sqlExcluir.setInt(1, obj.getCodigo().intValue());
        sqlExcluir.execute();
    }

    /**
     * Método responsavel por validar se Exclui o passivo. O Passivo so poderá
     * ser excluido caso ele não tenha se transformado em Cliente(Apos Contrato)
     *
     * <AUTHOR>
     */
    public void excluir(PassivoVO obj) throws Exception {
        try {
            con.setAutoCommit(false);
            obj = consultarPorChavePrimaria(obj.getCodigo().intValue(), Uteis.NIVELMONTARDADOS_DADOSBASICOS);
            if (obj.getClienteVO().getCodigo() == 0) {
                executarExclusaoDependentesPassivo(obj);
                excluirPassivo(obj);
            } else {
                throw new Exception("Não foi possível realizar esta operação, devido o passivo já ter se tornado um cliente !");
            }
            con.commit();
        } catch (Exception e) {
            con.rollback();
            throw e;
        } finally {
            con.setAutoCommit(true);
        }
    }

    /**
     * Método responsavel por excluir todos dependentes de um Passivo 1º
     * HistoricoContato - exclui o historicoContato pelo codigo Passivo 2º
     * Agenda - exclui Agenda pelo codigo Passivo 3º FecharMetaDetalhado -
     * exclui toddos FecharMetaDetalhado cujo passivo esteja gravado nela 4º
     * FecharMeta - exclui todos fecharMeta.
     *
     * @param obj
     * @throws Exception
     * <AUTHOR>
     */
    public void executarExclusaoDependentesPassivo(PassivoVO obj) throws Exception {
        //se o tipo de agendamento for de aula experimental ou visita então retirar da meta
        if (getFacade().getAgenda().consultarSeExistePassivoAgendado(obj.getCodigo(), false) != 0) {

            AgendaVO agendaVO = getFacade().getAgenda().
                    consultarPorCodigoPassivo(obj.getCodigo(), false, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
            if (agendaVO.getTipoAgendamento().equals("VI") || agendaVO.getTipoAgendamento().equals("AE")) {
                FecharMetaDetalhadoVO fecharMetaDetalhadoVO = getFacade().
                        getFecharMetaDetalhado().consultarPorCodigoPassivo(obj.getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS);
                if (fecharMetaDetalhadoVO != null) {
                    if (fecharMetaDetalhadoVO.getObteveSucesso()) {
                        FecharMetaVO fecharMetaVO = getFacade().getFecharMeta().
                                consultarPorChavePrimaria(fecharMetaDetalhadoVO.getFecharMeta().getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS);
                        fecharMetaVO.setMetaAtingida(fecharMetaVO.getMetaAtingida().intValue() - 1.0);
                        fecharMetaVO.setMeta(fecharMetaVO.getMeta().intValue() - 1.0);
                        getFacade().getFecharMeta().alterar(fecharMetaVO);
                    } else {
                        FecharMetaVO fecharMetaVO = getFacade().getFecharMeta().
                                consultarPorChavePrimaria(fecharMetaDetalhadoVO.getFecharMeta().getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS);
                        fecharMetaVO.setMeta(fecharMetaVO.getMeta().intValue() - 1.0);
                        getFacade().getFecharMeta().alterar(fecharMetaVO);
                    }
                }
            }
        }
        getFacade().getHistoricoContato().excluirHistoricoContatoPorCodigoPassivo(obj.getCodigo().intValue());
        getFacade().getAgenda().excluirAgendaPorCodigoPassivo(obj.getCodigo().intValue());
    }

    public void incluirDependencias(PassivoVO obj, HistoricoContatoVO hist, String agendaOuObjecao, Integer empresa,
                                    ConfiguracaoSistemaCRMVO configCRM, Integer codigoAula, TipoMetaCRMTO tipoMetaSelec, UsuarioVO usuarioLogado, List<HorarioTurmaVO> aulaAgenda) throws Exception {
        hist.setColaboradorResponsavel(obj.getColaboradorResponsavel());
        hist.setResponsavelCadastro(obj.getColaboradorResponsavel());
        hist.getAgendaVO().setEmpresa(empresa);
        if (agendaOuObjecao.equals("AG")) {
            hist.getPassivoVO().setCodigo(obj.getCodigo());
            getFacade().getAgenda().verificaExisteAgendamentoDiaHoraMinuto(hist, hist.getAgendaVO());
        }

        if (configCRM != null && configCRM.isDirecionaragendamentosexperimentaisagenda() &&
                !UteisValidacao.emptyNumber(codigoAula) && (tipoMetaSelec.getFasesCRMEnum().equals(FasesCRMEnum.LEADS_HOJE) ||
                tipoMetaSelec.getFasesCRMEnum().equals(FasesCRMEnum.LEADS_ACUMULADAS) ||
                tipoMetaSelec.getFasesCRMEnum().equals(FasesCRMEnum.INDICACOES_SEM_CONTATO) ||
                tipoMetaSelec.getFasesCRMEnum().equals(FasesCRMEnum.PASSIVO))) {
            String horaDeFato = aulaAgenda.stream().filter(a -> a.getCodigo().equals(codigoAula)).findFirst().get().getHoraInicial();
            if (!UteisValidacao.emptyString(horaDeFato)) {
                hist.getAgendaVO().setHoraMinuto(horaDeFato);
            }

            TurmasServiceImpl turmasService = new TurmasServiceImpl(con);
            try {
                hist.getAgendaVO().setAlunohorarioturma(
                        turmasService.inserirAulaExperimentalAgenda(codigoAula, null, hist.getPassivoVO(), null, hist.getAgendaVO().getDataAgendamento(), usuarioLogado));
            } catch (Exception ex) {
                ex.printStackTrace();
            } finally {
                turmasService = null;
            }
        }

        hist = getFacade().getHistoricoContato().executarInsercaoHistoricoPorPassivoOrClienteOrIndicado("CP", "TE", agendaOuObjecao, obj.getCodigo(), 0, 0, hist, obj.getColaboradorResponsavel(), obj.getResponsavelCadastro(), empresa);
        if (agendaOuObjecao.equals("AG") && Uteis.datasMesmoDiaMesAno(hist.getAgendaVO().getDataAgendamento(), Calendario.hoje()) ) {
            getFacade().getAgenda().executarRegraNegocioParaFecharMetaInclusao(hist.getDiaAbertura(), hist.getAgendaVO(), obj.getColaboradorResponsavel().getCodigo(), obj.getCodigo(), 0, 0, hist);
        }
    }

    public void alterarDependencias(PassivoVO obj, HistoricoContatoVO hist, String agendaOuObjecao, Integer empresa) throws Exception {
        if (agendaOuObjecao.equals("AG")) {
            getFacade().getAgenda().executarRegraNegocioParaFecharMetaAlteracaoPorPassivo(hist.getDiaAbertura(), hist.getAgendaVO(), obj.getColaboradorResponsavel().getCodigo(), obj, hist);
        } else if (agendaOuObjecao.equals("OB")) {
            getFacade().getObjecao().executarRegraNegocioParaFecharMetaAlteracao(hist.getDiaAbertura(), obj.getColaboradorResponsavel().getCodigo(), obj.getCodigo(), 0, 0, hist);

        }
        hist = getFacade().getHistoricoContato().executarAlteracaoHistoricoPorPorPassivoOrClienteOrIndicado(agendaOuObjecao, "CP", "TE", obj.getCodigo(), 0, 0, hist, obj.getColaboradorResponsavel(), obj.getResponsavelCadastro(), empresa);

    }

    /**
     * Responsável por realizar uma consulta de <code>Passivo</code> através do
     * valor do atributo <code>Date dia</code>. Retorna os objetos com valores
     * pertecentes ao período informado por parâmetro. Faz uso da operação
     * <code>montarDadosConsulta</code> que realiza o trabalho de prerarar o
     * List resultante.
     *
     * @param controlarAcesso Indica se a aplicação deverá verificar se o usuário possui
     *                        permissão para esta consulta ou não.
     * @return List Contendo vários objetos da classe <code>PassivoVO</code>
     * resultantes da consulta.
     * @throws Exception Caso haja problemas de conexão ou restrição de acesso.
     */
    public List consultarPorDia(Date prmIni, Date prmFim, boolean controlarAcesso, EmpresaVO empresa, int nivelMontarDados) throws Exception {
        consultarCRM(getIdEntidade(), controlarAcesso);
        StringBuilder sql = new StringBuilder();
        sql.append("SELECT * FROM Passivo WHERE ((Cast (dia as Date) >= '" + Uteis.getDataJDBC(prmIni) + "') and ");
        sql.append("(Cast (dia as Date) <= '" + Uteis.getDataJDBC(prmFim) + "')) ");
        if (!UteisValidacao.emptyNumber(empresa.getCodigo())) {
            sql.append(" AND passivo.empresa =  " + empresa.getCodigo());
        }
        sql.append(" ORDER BY dia");
        Statement stm = con.createStatement();
        ResultSet tabelaResultado = stm.executeQuery(sql.toString());
        return (montarDadosConsulta(tabelaResultado, nivelMontarDados, con));
    }

    public List<PassivoVO> consultarPorPeriodoResponsavel(Integer empresa, Date prmIni, Date prmFim, Integer usuario, boolean controlarAcesso, int nivelMontarDados) throws Exception{
        consultarCRM(getIdEntidade(), controlarAcesso);
        if( null == prmIni){
            prmIni = Uteis.obterPrimeiroDiaMes(Calendario.hoje());
        }
        if(null == prmFim){
            prmFim = Calendario.hoje();
        }

        StringBuilder sb = new StringBuilder();
        sb.append("SELECT * FROM Passivo WHERE (Cast (dia as Date) between '").append(Uteis.getDataJDBC(prmIni)).append("' and '").append(Uteis.getDataJDBC(prmFim)).append("') ");

        if(null != empresa && empresa > 0){
            sb.append(" and empresa = ").append(empresa);
        }

        if(null != usuario && usuario > 0){
            sb.append(" and responsavelcadastro = ").append(usuario);
        }

        Statement stm = con.createStatement();
        ResultSet tabelaResultado = stm.executeQuery(sb.toString());
        return (montarDadosConsulta(tabelaResultado, nivelMontarDados, con));
    }

    /**
     * Responsável por realizar uma consulta de <code>Passivo</code> através do
     * valor do atributo <code>situacao</code> da classe
     * <code>Colaborador</code> Faz uso da operação
     * <code>montarDadosConsulta</code> que realiza o trabalho de prerarar o
     * List resultante.
     *
     * @return List Contendo vários objetos da classe <code>PassivoVO</code>
     * resultantes da consulta.
     * @throws Exception Caso haja problemas de conexão ou restrição de acesso.
     */
    public List consultarPorSituacaoColaborador(String valorConsulta, EmpresaVO empresa, int nivelMontarDados) throws Exception {
        consultarCRM(getIdEntidade(), true);
        StringBuilder sql = new StringBuilder();
        sql.append("SELECT Passivo.* FROM Passivo, Colaborador WHERE Passivo.colaboradorResponsavel = Colaborador.codigo ");
        sql.append(" and upper( Colaborador.situacao ) like('" + valorConsulta.toUpperCase() + "%') ");
        if (!UteisValidacao.emptyNumber(empresa.getCodigo())) {
            sql.append(" AND passivo.empresa =  " + empresa.getCodigo());
        }
        sql.append(" ORDER BY Colaborador.situacao");
        Statement stm = con.createStatement();
        ResultSet tabelaResultado = stm.executeQuery(sql.toString());
        return montarDadosConsulta(tabelaResultado, nivelMontarDados,con);
    }

    public List consultarPorUsuarioResponsavelCadastro(String valorConsulta, EmpresaVO empresa, int nivelMontarDados) throws Exception {
        consultarCRM(getIdEntidade(), true);
        StringBuilder sql = new StringBuilder();
        sql.append("SELECT\n");
        sql.append("  pas.*\n");
        sql.append("FROM passivo pas\n");
        sql.append("  INNER JOIN usuario usu\n");
        sql.append("    ON pas.responsavelcadastro = usu.codigo\n");
        sql.append("WHERE upper(usu.nome) LIKE ('").append(valorConsulta.toUpperCase()).append("%')\n");
        sql.append("  AND lead is 'f'    AND pas.empresa = ").append(empresa.getCodigo()).append("\n");
        sql.append("ORDER BY usu.nome;");
        Statement stm = con.createStatement();
        ResultSet tabelaResultado = stm.executeQuery(sql.toString());
        return montarDadosConsulta(tabelaResultado, nivelMontarDados,con);
    }

    public List consultarPorNomePassivoComLimite(String valorConsulta, boolean controlarAcesso, EmpresaVO empresa, int nivelMontarDados) throws Exception {
        consultar(getIdEntidade(), controlarAcesso);
        StringBuilder sql = new StringBuilder();
        sql.append("SELECT Passivo.* FROM Passivo WHERE upper(Passivo.nome) like('" + valorConsulta.toUpperCase() + "%')");
        if (!UteisValidacao.emptyNumber(empresa.getCodigo())) {
            sql.append(" AND passivo.empresa =  " + empresa.getCodigo());
        }
        sql.append(" ORDER BY passivo.nome limit 20");
        Statement stm = con.createStatement();
        ResultSet tabelaResultado = stm.executeQuery(sql.toString());
        return (montarDadosConsulta(tabelaResultado, nivelMontarDados,con));
    }

    public PassivoVO consultarPorNomePassivo(String valorConsulta, boolean controlarAcesso, EmpresaVO empresa, int nivelMontarDados) throws Exception {
        consultar(getIdEntidade(), controlarAcesso);
        StringBuilder sql = new StringBuilder();
        sql.append("SELECT Passivo.* FROM Passivo WHERE upper(Passivo.nome) like('" + valorConsulta.toUpperCase() + "%') ");
        if (!UteisValidacao.emptyNumber(empresa.getCodigo())) {
            sql.append(" AND passivo.empresa =  " + empresa.getCodigo());
        }
        sql.append(" ORDER BY passivo.nome");
        PreparedStatement sqlConsultar = con.prepareStatement(sql.toString());
        ResultSet tabelaResultado = sqlConsultar.executeQuery();
        if (!tabelaResultado.next()) {
            return new PassivoVO();
        }
        return (montarDados(tabelaResultado, nivelMontarDados,con));
    }

    public Integer consultarPorCodigoCliente(Integer valorConsulta, boolean controlarAcesso) throws Exception {
        consultar(getIdEntidade(), controlarAcesso);
        String sqlStr = "SELECT Passivo.* FROM Passivo WHERE cliente =" + valorConsulta + " ORDER BY passivo.nome";
        PreparedStatement sqlConsultar = con.prepareStatement(sqlStr);
        ResultSet tabelaResultado = sqlConsultar.executeQuery();
        if (!tabelaResultado.next()) {
            return 0;
        }
        return (new Integer(tabelaResultado.getInt(1)));
    }

    public List consultarPorSituacaoResponsavelCadastro(String valorConsulta, EmpresaVO empresa, int nivelMontarDados) throws Exception {
        consultarCRM(getIdEntidade(), true);
        StringBuilder sql = new StringBuilder();
        sql.append("SELECT Passivo.* FROM Passivo, Colaborador WHERE Passivo.responsavelCadastro = ");
        sql.append("Colaborador.codigo and upper( Colaborador.situacao ) like('" + valorConsulta.toUpperCase() + "%') ");
        if (!UteisValidacao.emptyNumber(empresa.getCodigo())) {
            sql.append(" AND passivo.empresa =  " + empresa.getCodigo());
        }
        sql.append(" ORDER BY Colaborador.situacao");
        Statement stm = con.createStatement();
        ResultSet tabelaResultado = stm.executeQuery(sql.toString());
        return montarDadosConsulta(tabelaResultado, nivelMontarDados, con);
    }

    /**
     * Responsável por realizar uma consulta de <code>Passivo</code> através do
     * valor do atributo <code>String nome</code>. Retorna os objetos, com
     * início do valor do atributo idêntico ao parâmetro fornecido. Faz uso da
     * operação <code>montarDadosConsulta</code> que realiza o trabalho de
     * prerarar o List resultante.
     *
     * @param controlarAcesso Indica se a aplicação deverá verificar se o usuário possui
     *                        permissão para esta consulta ou não.
     * @return List Contendo vários objetos da classe <code>PassivoVO</code>
     * resultantes da consulta.
     * @throws Exception Caso haja problemas de conexão ou restrição de acesso.
     */
    public List consultarPorNome(String valorConsulta, boolean controlarAcesso, EmpresaVO empresa, int nivelMontarDados) throws Exception {
        consultarCRM(getIdEntidade(), controlarAcesso);
        StringBuilder sql = new StringBuilder();
        sql.append("SELECT * FROM Passivo WHERE upper( nome ) like('" + valorConsulta.toUpperCase() + "%') ");
        if (!UteisValidacao.emptyNumber(empresa.getCodigo())) {
            sql.append(" AND passivo.empresa =  " + empresa.getCodigo());
        }
        sql.append(" ORDER BY nome");
        Statement stm = con.createStatement();
        ResultSet tabelaResultado = stm.executeQuery(sql.toString());
        return (montarDadosConsulta(tabelaResultado, nivelMontarDados,con));
    }

    /**
     * Responsável por realizar uma consulta de <code>Passivo</code> através do
     * valor do atributo <code>Integer codigo</code>. Retorna os objetos com
     * valores iguais ou superiores ao parâmetro fornecido. Faz uso da operação
     * <code>montarDadosConsulta</code> que realiza o trabalho de prerarar o
     * List resultante.
     *
     * @param controlarAcesso Indica se a aplicação deverá verificar se o usuário possui
     *                        permissão para esta consulta ou não.
     * @return List Contendo vários objetos da classe <code>PassivoVO</code>
     * resultantes da consulta.
     * @throws Exception Caso haja problemas de conexão ou restrição de acesso.
     */
    public List consultarPorCodigo(Integer valorConsulta, boolean controlarAcesso, EmpresaVO empresa, int nivelMontarDados) throws Exception {
        consultarCRM(getIdEntidade(), controlarAcesso);
        StringBuilder sql = new StringBuilder();
        sql.append("SELECT * FROM Passivo WHERE codigo = " + valorConsulta.intValue());
        if (!UteisValidacao.emptyNumber(empresa.getCodigo())) {
            sql.append(" AND passivo.empresa =  " + empresa.getCodigo());
        }
        sql.append(" ORDER BY codigo");
        Statement stm = con.createStatement();
        ResultSet tabelaResultado = stm.executeQuery(sql.toString());
        return (montarDadosConsulta(tabelaResultado, nivelMontarDados,con));
    }

    public Integer consultarPassivoPorColaboradorResponsavel(Integer valorConsulta, Date dia, EmpresaVO empresa) throws Exception {
        consultarCRM(getIdEntidade(), true);
        StringBuilder sql = new StringBuilder();
        sql.append("SELECT COUNT(passivo.codigo) FROM Passivo, Usuario where usuario.codigo = Passivo.colaboradorResponsavel ");
        sql.append("and Passivo.colaboradorResponsavel = " + valorConsulta.intValue());
        sql.append(" and  CAST( Passivo.dia AS DATE) = CAST( '" + Uteis.getDataJDBC(dia) + "' AS DATE)");
        if (!UteisValidacao.emptyNumber(empresa.getCodigo())) {
            sql.append(" AND passivo.empresa =  " + empresa.getCodigo());
        }
        Statement stm = con.createStatement();
        ResultSet tabelaResultado = stm.executeQuery(sql.toString());
        tabelaResultado.next();
        return (new Integer(tabelaResultado.getInt(1)));
    }

    /**
     * Responsável por montar os dados de vários objetos, resultantes de uma
     * consulta ao banco de dados (<code>ResultSet</code>). Faz uso da operação
     * <code>montarDados</code> que realiza o trabalho para um objeto por vez.
     *
     * @return List Contendo vários objetos da classe <code>PassivoVO</code>
     * resultantes da consulta.
     */
    public static List montarDadosConsulta(ResultSet tabelaResultado, int nivelMontarDados, Connection con) throws Exception {
        List vetResultado = new ArrayList();
        while (tabelaResultado.next()) {
            PassivoVO obj = new PassivoVO();
            obj = montarDados(tabelaResultado, nivelMontarDados,con);
            vetResultado.add(obj);
        }
        tabelaResultado = null;
        return vetResultado;
    }

    /**
     * Responsável por montar os dados resultantes de uma consulta ao banco de
     * dados (<code>ResultSet</code>) em um objeto da classe
     * <code>PassivoVO</code>.
     *
     * @return O objeto da classe <code>PassivoVO</code> com os dados
     * devidamente montados.
     */
    public static PassivoVO montarDados(ResultSet dadosSQL, int nivelMontarDados, Connection con) throws Exception {
        PassivoVO obj = new PassivoVO();
        obj.setCodigo(new Integer(dadosSQL.getInt("codigo")));
        obj.setNome(dadosSQL.getString("nome"));
        obj.setTelefoneResidencial(dadosSQL.getString("telefoneResidencial"));
        obj.setTelefoneCelular(dadosSQL.getString("telefoneCelular"));
        obj.setTelefoneTrabalho(dadosSQL.getString("telefoneTrabalho"));
        obj.getResponsavelCadastro().setCodigo(new Integer(dadosSQL.getInt("responsavelCadastro")));
        obj.setDia(dadosSQL.getTimestamp("dia"));
        obj.setObservacao(dadosSQL.getString("observacao"));
        obj.getColaboradorResponsavel().setCodigo(new Integer(dadosSQL.getInt("colaboradorResponsavel")));
        obj.setEmail(dadosSQL.getString("email"));
        obj.getEvento().setCodigo(dadosSQL.getInt("evento"));
        obj.getClienteVO().setCodigo(new Integer(dadosSQL.getInt("cliente")));
        obj.getEmpresaVO().setCodigo(dadosSQL.getInt("empresa"));
        obj.setContrato(dadosSQL.getInt("contrato"));
        obj.getObjecao().setCodigo(dadosSQL.getInt("objecao"));
        obj.setLead(dadosSQL.getBoolean("lead"));
        obj.setOrigemSistemaEnum(OrigemSistemaEnum.getOrigemSistema(dadosSQL.getInt("origemSistema")));
        obj.setNovoObj(false);
        if (nivelMontarDados == Uteis.NIVELMONTARDADOS_DADOSBASICOS) {
            return obj;
        }
        if (nivelMontarDados == Uteis.NIVELMONTARDADOS_METAPASSIVODETALHADA) {
            montarDadosEvento(obj, Uteis.NIVELMONTARDADOS_DADOSBASICOS,con);
            montarDadosColaboradorResponsavel(obj, Uteis.NIVELMONTARDADOS_METACOLABORADORRESPONSAVEL,con);
            return obj;
        }
        if (nivelMontarDados == Uteis.NIVELMONTARDADOS_HISTORICOPASSIVO) {
            montarDadosEvento(obj, nivelMontarDados,con);
            montarDadosColaboradorResponsavel(obj, Uteis.NIVELMONTARDADOS_METACOLABORADORRESPONSAVEL,con);
            montarDadosEmpresa(obj, Uteis.NIVELMONTARDADOS_DADOSBASICOS,con);
            return obj;
        }
        if (nivelMontarDados == Uteis.NIVELMONTARDADOS_METAAGENDAMENTODETALHADA) {
            Cliente clienteDAO = new Cliente(con);
            ClienteVO clienteVO = clienteDAO.consultarPorCodigo(obj.getClienteVO().getCodigo(), false, Uteis.NIVELMONTARDADOS_METAAGENDAMENTODETALHADA);
            obj.setClienteVO(clienteVO);
            clienteDAO = null;
        }

        montarDadosEvento(obj, nivelMontarDados,con);
        montarDadosResponsavelCadastro(obj, Uteis.NIVELMONTARDADOS_INDICERENOVACAO,con);
        montarDadosColaboradorResponsavel(obj, Uteis.NIVELMONTARDADOS_INDICERENOVACAO,con);
        montarDadosEmpresa(obj, Uteis.NIVELMONTARDADOS_DADOSBASICOS,con);
        montarDadosLead(obj,Uteis.NIVELMONTARDADOS_DADOSBASICOS,con);
                
        return obj;
    }

    /**
     * Operação responsável por montar os dados de um objeto da classe
     * <code>ColaboradorVO</code> relacionado ao objeto <code>PassivoVO</code>.
     * Faz uso da chave primária da classe <code>ColaboradorVO</code> para
     * realizar a consulta.
     *
     * @param obj Objeto no qual será montado os dados consultados.
     */
    public static void montarDadosColaboradorResponsavel(PassivoVO obj, int nivelMontarDados, Connection con) throws Exception {
        if (obj.getColaboradorResponsavel().getCodigo().intValue() == 0) {
            obj.setColaboradorResponsavel(new UsuarioVO());
            return;
        }
        Usuario usuarioDAO = new Usuario(con);
        obj.setColaboradorResponsavel(usuarioDAO.consultarPorChavePrimaria(obj.getColaboradorResponsavel().getCodigo(), nivelMontarDados));
        usuarioDAO = null;
    }

    /**
     * Operação responsável por montar os dados de um objeto da classe
     * <code>ColaboradorVO</code> relacionado ao objeto <code>PassivoVO</code>.
     * Faz uso da chave primária da classe <code>ColaboradorVO</code> para
     * realizar a consulta.
     *
     * @param obj Objeto no qual será montado os dados consultados.
     */
    public static void montarDadosResponsavelCadastro(PassivoVO obj, int nivelMontarDados, Connection con) throws Exception {
        if (obj.getResponsavelCadastro().getCodigo().intValue() == 0) {
            obj.setResponsavelCadastro(new UsuarioVO());
            return;
        }
        Usuario usuarioDAO = new Usuario(con);
        obj.setResponsavelCadastro(usuarioDAO.consultarPorChavePrimaria(obj.getResponsavelCadastro().getCodigo(), nivelMontarDados));
        usuarioDAO = null;
    }

    public static void montarDadosEvento(PassivoVO obj, int nivelMontarDados, Connection con) throws Exception {
        if (obj.getEvento().getCodigo().intValue() == 0) {
            obj.setEvento(new EventoVO());
            return;
        }
        Evento eventoDAO = new Evento(con);
        obj.setEvento(eventoDAO.consultarPorChavePrimaria(obj.getEvento().getCodigo(), nivelMontarDados));
        eventoDAO = null;
    }

    /**
     * Operação responsável por localizar um objeto da classe
     * <code>PassivoVO</code> através de sua chave primária.
     *
     * @throws Exception Caso haja problemas de conexão ou localização do objeto
     *                   procurado.
     */
    public PassivoVO consultarPorChavePrimaria(Integer codigoPrm, int nivelMontarDados) throws Exception {
        consultarCRM(getIdEntidade(), false);
        String sql = "SELECT * FROM Passivo WHERE codigo = ?";
        PreparedStatement sqlConsultar = con.prepareStatement(sql);
        sqlConsultar.setInt(1, codigoPrm.intValue());
        ResultSet tabelaResultado = sqlConsultar.executeQuery();
        if (!tabelaResultado.next()) {
            throw new ConsistirException("Dados Não Encontrados ( Passivo ).");
        }
        return (montarDados(tabelaResultado, nivelMontarDados, con));
    }

    public PassivoVO consultarPorCodigo(Integer codigoPassivo, int nivelMontarDados) throws Exception {
        String sql = "select * from passivo where codigo = ?";
        PreparedStatement pst = con.prepareStatement(sql);
        pst.setInt(1, codigoPassivo);
        ResultSet rs = pst.executeQuery();
        if (rs.next()) {
            return montarDados(rs, nivelMontarDados,con);
        }
        return null;
    }


    private ResultSet getRS(Integer empresa, String sEcho, Integer offset, Integer limit, String clausulaLike, Integer colOrdenar, String dirOrdenar,
                            Date dtInicio, Date dtFim) throws SQLException {

        StringBuilder sql = new StringBuilder();
        sql.append(" SELECT\n");
        sql.append(" pas.codigo, ");
        sql.append(" pas.nome, ");
        sql.append(" usu.nome AS colaboradorRespCadastro, ");
        sql.append(" pas.dia, ");
        sql.append(" emp.nome as empresa ");
        sql.append(" FROM passivo pas ");
        sql.append(" INNER JOIN usuario usu ON usu.codigo = pas.responsavelcadastro ");
        sql.append(" INNER JOIN empresa emp ON emp.codigo = pas.empresa ");
        sql.append(" WHERE\n");
        if (empresa > 0) {
            sql.append(" emp.codigo = " + empresa + "\n");
        }
        if (!UteisValidacao.emptyString(clausulaLike)) {
            if (empresa > 0)
                sql.append(" AND ");
            sql.append("(");
            sql.append("lower(pas.codigo::VARCHAR) ~ '").append(clausulaLike).append("' OR\n");
            sql.append("lower(pas.nome::VARCHAR) ~ '").append(clausulaLike).append("' OR\n");
            sql.append("lower(colaboradorRespCadastro) ~ '").append(clausulaLike).append("' OR\n");
            sql.append("lower(empresa::VARCHAR) ~ '").append(clausulaLike).append("'\n");
            sql.append(")");
        }
        sql.append("  ORDER BY ").append(colOrdenar + 1).append(" ").append(dirOrdenar).append("\n");
        if (limit > 0) {
            sql.append(" LIMIT ").append(limit).append("\n");
        }
        sql.append(" OFFSET ").append(offset).append("\n");

        PreparedStatement sqlConsultar = con.prepareStatement(sql.toString());
        ResultSet rs = sqlConsultar.executeQuery();
        return rs;
    }

    private ResultSet getRS(Integer empresa) throws SQLException {

        StringBuilder sql = new StringBuilder();
        sql.append(" SELECT\n");
        sql.append(" pas.codigo, ");
        sql.append(" pas.nome, ");
        sql.append(" usu.nome AS colaboradorRespCadastro, ");
        sql.append(" pas.dia, ");
        sql.append(" pas.telefoneresidencial, ");
        sql.append(" pas.telefonecelular, ");
        sql.append(" pas.telefonetrabalho, ");
        sql.append(" emp.nome as empresa, ");
        sql.append(" evt.descricao as descricaoEvento, ");
        sql.append(" hist.observacao as observacao, ");
        sql.append(" c.matricula ");
        sql.append(" FROM passivo pas ");
        sql.append(" INNER JOIN usuario usu ON usu.codigo = pas.responsavelcadastro ");
        sql.append(" INNER JOIN empresa emp ON emp.codigo = pas.empresa ");
        sql.append(" LEFT JOIN evento evt ON evt.codigo = pas.evento ");
        sql.append(" LEFT JOIN cliente c ON c.codigo = pas.cliente ");
        sql.append(" LEFT JOIN historicocontato hist ON hist.passivo = pas.codigo ");
        if (empresa > 0) {
            sql.append(" WHERE emp.codigo = " + empresa + ";");
        }

        PreparedStatement sqlConsultar = con.prepareStatement(sql.toString());
        ResultSet rs = sqlConsultar.executeQuery();
        return rs;
    }

    public String consultarJSON(Integer empresa, String sEcho, Integer offset, Integer limit, String clausulaLike, Integer colOrdenar, String dirOrdenar,
                                Date dtInicio, Date dtFim, boolean apresentarConvertidos) throws Exception {

        if (!UteisValidacao.emptyString(clausulaLike)) {
            clausulaLike = clausulaLike.toLowerCase();
        }

        StringBuilder sql = new StringBuilder();
        sql.append(" SELECT\n");
        sql.append(" pas.codigo, ");
        sql.append(" pas.nome, ");
        sql.append(" pas.telefoneresidencial, ");
        sql.append(" pas.telefonecelular, ");
        sql.append(" pas.telefonetrabalho, ");
        sql.append(" usu.nome AS colaboradorRespCadastro, ");
        sql.append(" pas.dia, ");
        sql.append(" pas.lead, ");
        sql.append(" emp.nome as empresa, ");
        sql.append(" coalesce(evt.descricao, '') as descricaoEvento, ");
        sql.append(" coalesce(hist.observacao, '') as observacao, ");
        sql.append(" c.matricula ");
        sql.append(" FROM passivo pas ");
        sql.append(" INNER JOIN usuario usu ON usu.codigo = pas.responsavelcadastro ");
        sql.append(" INNER JOIN empresa emp ON emp.codigo = pas.empresa ");
        sql.append(" LEFT JOIN cliente c ON c.codigo = pas.cliente ");
        sql.append(" LEFT JOIN evento evt ON evt.codigo = pas.evento ");
        sql.append(" LEFT JOIN historicocontato hist ON hist.passivo = pas.codigo ");
        sql.append(" WHERE 1=1\n");
        if(!apresentarConvertidos){
            sql.append(" AND c.codigo is null\n");
        }
        if (empresa > 0) {
            sql.append(" AND emp.codigo = " + empresa + "\n");
        }
        if (!UteisValidacao.emptyString(clausulaLike)) {
            if (empresa > 0)
                sql.append(" AND ");
            sql.append("(");
            sql.append("lower(pas.codigo::VARCHAR) ilike '%").append(clausulaLike).append("%' OR\n");
            sql.append("lower(pas.nome::VARCHAR) ilike '%").append(clausulaLike).append("%' OR\n");
            sql.append("lower(usu.nome::VARCHAR) ilike '%").append(clausulaLike).append("%' OR\n");
            sql.append("lower(pas.telefoneresidencial::VARCHAR) ilike '%").append(clausulaLike).append("%' OR\n");
            sql.append("lower(pas.telefonecelular::VARCHAR) ilike '%").append(clausulaLike).append("%' OR\n");
            sql.append("lower(pas.telefonetrabalho::VARCHAR) ilike '%").append(clausulaLike).append("%' OR\n");
            sql.append("lower(emp.nome::VARCHAR) ilike '%").append(clausulaLike).append("%'\n");
            sql.append(")");
        }

        if (dtInicio != null) {
            sql.append("  AND pas.dia >= '").append(Uteis.getDataJDBCTimestamp(dtInicio)).append("'\n");
        }

        if (dtFim != null) {
            sql.append("  AND pas.dia <= '").append(Uteis.getDataJDBC(dtFim)).append(" 23:59:59.999'\n");
        }
        colOrdenar = colOrdenar + 1;
        sql.append("  ORDER BY ").append(colOrdenar == 11 ? "c.codigo" : colOrdenar).append(" ").append(dirOrdenar).append("\n");
        if (limit > 0) {
            sql.append(" LIMIT ").append(limit).append("\n");
        }
        sql.append(" OFFSET ").append(offset).append("\n");

        PreparedStatement sqlConsultar = con.prepareStatement(sql.toString());
        ResultSet rs = sqlConsultar.executeQuery();

        StringBuilder sqlContarFiltrados = new StringBuilder("SELECT count(pas.codigo)\n");
        sqlContarFiltrados.append("FROM passivo pas\n");
        sqlContarFiltrados.append(" INNER JOIN usuario usu ON usu.codigo = pas.responsavelcadastro ");
        sqlContarFiltrados.append(" INNER JOIN empresa emp ON emp.codigo = pas.empresa ");
        sqlContarFiltrados.append(" LEFT JOIN cliente c ON c.codigo = pas.cliente ");
        sqlContarFiltrados.append(" LEFT JOIN historicocontato hist ON hist.passivo = pas.codigo ");
        sqlContarFiltrados.append(" WHERE 1=1\n");

        if (empresa > 0) {
            sqlContarFiltrados.append(" AND emp.codigo = " + empresa + "\n");
        }
        if (!UteisValidacao.emptyString(clausulaLike)) {
            if (empresa > 0)
                sqlContarFiltrados.append(" AND ");
            sqlContarFiltrados.append("(");
            sqlContarFiltrados.append("lower(pas.codigo::VARCHAR) ilike '%").append(clausulaLike).append("%' OR\n");
            sqlContarFiltrados.append("lower(pas.telefoneresidencial::VARCHAR) ilike '%").append(clausulaLike).append("%' OR\n");
            sqlContarFiltrados.append("lower(pas.telefonecelular::VARCHAR) ilike '%").append(clausulaLike).append("%' OR\n");
            sqlContarFiltrados.append("lower(pas.telefonetrabalho::VARCHAR) ilike '%").append(clausulaLike).append("%' OR\n");
            sqlContarFiltrados.append("lower(pas.nome::VARCHAR) ilike '%").append(clausulaLike).append("%' OR\n");
            sqlContarFiltrados.append("lower(usu.nome::VARCHAR) ilike '%").append(clausulaLike).append("%' OR\n");
            sqlContarFiltrados.append("lower(emp.nome::VARCHAR) ilike '%").append(clausulaLike).append("%'\n");
            sqlContarFiltrados.append(")");
        }
        if (dtInicio != null) {
            sqlContarFiltrados.append("  AND pas.dia >= '").append(Uteis.getDataJDBCTimestamp(dtInicio)).append("'\n");
        }

        if (dtFim != null) {
            sqlContarFiltrados.append("  AND pas.dia <= '").append(Uteis.getDataJDBC(dtFim)).append(" 23:59:59.999'\n");
        }

        if(!apresentarConvertidos){
            sqlContarFiltrados.append(" AND c.codigo is null\n");
        }

        StringBuilder sqlCount = new StringBuilder("SELECT count(pas.codigo) FROM passivo pas ");
        sqlCount.append("LEFT JOIN cliente c on c.codigo = pas.cliente ");
        sqlCount.append("WHERE 1=1 ");
        if (empresa > 0) {
            sqlCount.append(" AND pas.empresa = " + empresa + "\n");
        }
        if(!apresentarConvertidos){
            sqlCount.append(" AND c.codigo is null\n");
        }
        StringBuilder json = new StringBuilder();
        json.append("{");
        json.append("\"iTotalRecords\":\"").append(contar(sqlCount.toString(), getCon())).append("\",");
        json.append("\"iTotalDisplayRecords\":\"").append(contar(sqlContarFiltrados.toString(), getCon())).append("\",");
        json.append("\"sEcho\":\"").append(sEcho).append("\",");
        json.append("\"aaData\":[");
        boolean dados = false;
        while (rs.next()) {
            dados = true;
            json.append("[\"").append(rs.getString("codigo")).append("\",");
            json.append("\"").append(Uteis.normalizarStringJSON(rs.getString("nome")+(rs.getBoolean("lead") ? " (lead)" : ""))).append("\",");
            json.append("\"").append(Uteis.normalizarStringJSON(rs.getString("telefoneresidencial"))).append("\",");
            json.append("\"").append(Uteis.normalizarStringJSON(rs.getString("telefonecelular"))).append("\",");
            json.append("\"").append(Uteis.normalizarStringJSON(rs.getString("telefonetrabalho"))).append("\",");
            json.append("\"").append(Uteis.normalizarStringJSON(rs.getString("colaboradorRespCadastro"))).append("\",");
            json.append("\"").append(Uteis.getDataComHHMM(rs.getTimestamp("dia"))).append("\",");
            json.append("\"").append(Uteis.normalizarStringJSON(rs.getString("empresa"))).append("\",");
            json.append("\"").append(Uteis.normalizarStringJSON(rs.getString("descricaoEvento") != null ? rs.getString("descricaoEvento") : "")).append("\",");
            json.append("\"").append(Uteis.normalizarStringJSON(rs.getString("observacao").length() > 30 ? rs.getString("observacao").substring(0,30) + "..." : rs.getString("observacao"))).append("\",");
            json.append("\"").append(Uteis.normalizarStringJSON(rs.getString("matricula") != null ? rs.getString("matricula") : "")).append("\"],");
        }
        if (dados) {
            json.deleteCharAt(json.toString().length() - 1);
        }
        json.append("]}");
        return json.toString();
    }

    public List consultarParaImpressao(Integer empresa, Date dataInicio, Date dataFim, String filtro, String ordem, String campoOrdenacao) throws SQLException {

        ResultSet rs = getRS(empresa);
        List lista = new ArrayList();

        while (rs.next()) {

            PassivoVO pas = new PassivoVO();
            String geral = rs.getString("codigo") +
                    rs.getString("nome") +
                    rs.getString("telefoneresidencial") +
                    rs.getString("telefonecelular") +
                    rs.getString("telefonetrabalho") +
                    rs.getString("colaboradorRespCadastro") +
                    Uteis.getData(rs.getDate("dia")) +
                    rs.getString("empresa") +
                    rs.getString("descricaoEvento") +
                    rs.getString("observacao") +
                    rs.getString("matricula");
            //Verifica se os filtros de periodos estao nulos se estiverem nulo não sera aplicado a condição de periodo,
            // mas caso não esteja sera aplicado as condicões por periodo selecionados
            if ((!UteisValidacao.emptyString(geral) && geral.toLowerCase().contains(filtro.toLowerCase())) && ((dataFim == null && dataFim == null) ||
                    (!(dataFim == null && dataFim == null) &&
                            (Calendario.entre(rs.getDate("dia"), dataInicio, dataFim) || (Calendario.igual(dataInicio, dataFim) && Calendario.igual(rs.getDate("dia"), dataInicio))
                                    || Calendario.igual(rs.getDate("dia"), dataFim) || Calendario.igual(dataInicio, rs.getDate("dia")))
                    ))) {

                pas.setCodigo(rs.getInt("codigo"));
                pas.setNome(rs.getString("nome"));
                pas.setTelefoneResidencial(rs.getString("telefoneresidencial"));
                pas.setTelefoneCelular(rs.getString("telefonecelular"));
                pas.setTelefoneTrabalho(rs.getString("telefonetrabalho"));
                pas.setColaboradorResp(rs.getString("colaboradorRespCadastro"));
                pas.setDia(rs.getDate("dia"));
                pas.setEmpresa(rs.getString("empresa"));
                pas.setDescricaoEvento(rs.getString("descricaoEvento"));
                pas.setObservacao(rs.getString("observacao"));
                pas.setClienteVO(new ClienteVO());
                pas.getClienteVO().setMatricula(rs.getString("matricula"));
                lista.add(pas);
            }
        }
        if (campoOrdenacao.equals("Código")) {
            Ordenacao.ordenarLista(lista, "codigo");
        } else if (campoOrdenacao.equals("Nome")) {
            Ordenacao.ordenarLista(lista, "nome");
        } else if (campoOrdenacao.equals("Telefone Residencial")) {
            Ordenacao.ordenarLista(lista, "telefoneResidencial");
        } else if (campoOrdenacao.equals("Telefone Celular")) {
            Ordenacao.ordenarLista(lista, "telefoneCelular");
        } else if (campoOrdenacao.equals("Telefone Comercial")) {
            Ordenacao.ordenarLista(lista, "telefoneTrabalho");
        } else if (campoOrdenacao.equals("Responsável Cadastro")) {
            Ordenacao.ordenarLista(lista, "colaboradorRespCadastro");
        } else if (campoOrdenacao.equals("Dia")) {
            Ordenacao.ordenarLista(lista, "dia");
        } else if (campoOrdenacao.equals("Empresa")) {
            Ordenacao.ordenarLista(lista, "empresa");
        } else if (campoOrdenacao.equals("Evento")) {
            Ordenacao.ordenarLista(lista, "descricaoEvento");
        } else if (campoOrdenacao.equals("Comentário")) {
            Ordenacao.ordenarLista(lista, "observacao");
        } else if (campoOrdenacao.equals("Matrícula (Convertido)")) {
            Ordenacao.ordenarLista(lista, "clienteVO.matricula");
        }

        if (ordem.contains("desc")) {
            Collections.reverse(lista);
        }
        return lista;

    }

    public static void montarDadosEmpresa(PassivoVO obj, int nivelMontarDados, Connection con) throws Exception {
        if (obj.getEmpresaVO().getCodigo().intValue() == 0) {
            obj.setEmpresaVO(new EmpresaVO());
            return;
        }
        Empresa empresaDAO = new Empresa(con);
        obj.setEmpresaVO(empresaDAO.consultarPorChavePrimaria(obj.getEmpresaVO().getCodigo(), nivelMontarDados));
        empresaDAO = null;
    }

    public Integer contarPassivoPorPeriodoResponsavelCadastro(List<UsuarioVO> listaUsuarioVO, Date dataInicioMeta, Date dataFimMeta, Integer empresa) throws Exception {
        ResultSet rs = sqlPassivoPorPeriodoResponsavelCadastro(listaUsuarioVO, dataInicioMeta, dataFimMeta, true, false, empresa);
        if (rs.next()) {
            return rs.getInt("total");
        }
        return 0;
    }

    public List consultarPassivoPorPeriodoResponsavelCadastro(List<UsuarioVO> listaUsuarioVO, Date dataInicioMeta, Date dataFimMeta, Integer empresa, int nivelMontarDados) throws Exception {
        ResultSet rs = sqlPassivoPorPeriodoResponsavelCadastro(listaUsuarioVO, dataInicioMeta, dataFimMeta, false, false, empresa);
        return montarDadosConsulta(rs, nivelMontarDados, con);
    }

    public Integer contarPassivoConvertidosPorPeriodoResponsavelCadastro(List<UsuarioVO> listaUsuarioVO, Date dataInicioMeta, Date dataFimMeta, Integer empresa) throws Exception {
        ResultSet rs = sqlPassivoPorPeriodoResponsavelCadastro(listaUsuarioVO, dataInicioMeta, dataFimMeta, true, true, empresa);
        if (rs.next()) {
            return rs.getInt("total");
        }
        return 0;
    }

    public List<PassivoVO> consultarPassivoConvertidosPorPeriodoResponsavelCadastro(List<UsuarioVO> listaUsuarioVO, Date dataInicioMeta, Date dataFimMeta, Integer empresa, int nivelMontarDados) throws Exception {
        ResultSet rs = sqlPassivoPorPeriodoResponsavelCadastro(listaUsuarioVO, dataInicioMeta, dataFimMeta, false, true, empresa);
        return montarDadosConsulta(rs, nivelMontarDados, con);
    }

    private ResultSet sqlPassivoPorPeriodoResponsavelCadastro(List<UsuarioVO> listaUsuarioVO, Date dataInicioMeta, Date dataFimMeta, boolean count, boolean convertidos, Integer empresa) throws Exception {
        String codigosUsuario = Uteis.retornarCodigos(listaUsuarioVO);
        StringBuilder sql = new StringBuilder();

        if (count) {
            sql.append("SELECT count(*) as total FROM Passivo ");
        } else {
            sql.append("SELECT * FROM Passivo ");
        }

        if (dataFimMeta != null) {
            sql.append(" WHERE dia >= '").append(Uteis.getDataJDBC(dataInicioMeta)).append(" 00:00:00'");
            sql.append(" AND dia <= '").append(Uteis.getDataJDBC(dataFimMeta)).append(" 23:59:59'");
        } else {
            sql.append(" WHERE dia >= '").append(Uteis.getDataJDBC(dataInicioMeta)).append(" 00:00:00'");
            sql.append(" AND dia <= '").append(Uteis.getDataJDBC(dataInicioMeta)).append(" 23:59:59'");
        }

        sql.append(UteisValidacao.emptyString(codigosUsuario) ? "" : " AND responsavelcadastro in (" + codigosUsuario + ")");

        if (!UteisValidacao.emptyNumber(empresa)) {
            sql.append(" AND empresa =  " + empresa);
        }

        if (convertidos) {
            sql.append(" AND cliente is not null and contrato <> 0 ");
        }
        sql.append(" and lead = 'f' ");
        sql.append(" and metaextra = 'f' ");
        if (!count) {
            sql.append(" ORDER BY dia desc");
        }

        return criarConsulta(sql.toString(), con);
    }

    private void resolveNomeConsulta(PassivoVO obj) throws Exception {
        executarConsultaUpdate("UPDATE passivo SET nomeconsulta = remove_acento_upper(nome)"
                + " WHERE codigo = " + obj.getCodigo(), con);
    }

    public void executarAlteracaoContrato(Integer passivo, Integer contrato) throws Exception {
        alterarCRM(getIdEntidade());
        String sql = "UPDATE Passivo SET contrato = ? WHERE codigo = ?";
        PreparedStatement sqlAlterar = con.prepareStatement(sql);
        sqlAlterar.setInt(1, contrato);
        sqlAlterar.setInt(2, passivo);
        sqlAlterar.execute();
    }
    
    public PassivoVO consultarPorEmail(String email,Integer empresa,  int nivelMontarDados)throws Exception{
        StringBuilder sql = new StringBuilder();
        sql.append("select * from passivo where upper(email) = '").append(email.toUpperCase()).append("'");
        if(!UteisValidacao.emptyNumber(empresa)){
            sql.append(" and empresa = ").append(empresa);
        }
        Statement st = con.createStatement();
        ResultSet rs = st.executeQuery(sql.toString());
        if (rs.next()){
            return montarDados(rs, nivelMontarDados, con);
        }
        return null;
    }

    public List<PassivoVO> consultarPorContrato(Integer contrato, boolean controlarAcesso, int nivelMontarDados) throws Exception {
        consultar(getIdEntidade(), controlarAcesso);
        StringBuilder sql = new StringBuilder();
        sql.append("select * from passivo where contrato = ").append(contrato);
        Statement stm = con.createStatement();
        ResultSet tabelaResultado = stm.executeQuery(sql.toString());
        return (montarDadosConsulta(tabelaResultado, nivelMontarDados, con));
    }

    public void alterarObjecaoPassivo(Integer objecao, Integer passivo) throws Exception {
        String sql = "UPDATE passivo SET objecao = ? WHERE codigo = ?";
        PreparedStatement sqlAlterar = con.prepareStatement(sql);
        resolveIntegerNull(sqlAlterar, 1, objecao);
        sqlAlterar.setInt(2, passivo);
        sqlAlterar.execute();
    }
    
    private static void montarDadosLead(PassivoVO obj, int NIVELMONTARDADOS_DADOSBASICOS, Connection con) throws Exception {
        if(obj.isLead()){
            Lead leadDAO = new Lead(con);
            obj.setLeadVO(leadDAO.consultarPorPassivo(obj.getCodigo(), NIVELMONTARDADOS_DADOSBASICOS));
            leadDAO = null;
        }
    }
}

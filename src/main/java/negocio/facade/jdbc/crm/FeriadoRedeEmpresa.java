package negocio.facade.jdbc.crm;

import negocio.comuns.crm.FeriadoRedeEmpresaVO;
import negocio.comuns.utilitarias.Uteis;
import negocio.facade.jdbc.arquitetura.SuperEntidade;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;

public class FeriadoRedeEmpresa extends SuperEntidade {

    public FeriadoRedeEmpresa(Connection conexao) throws Exception {
        super(conexao);
    }

    public FeriadoRedeEmpresa() throws Exception {
        super();
    }

    public void inserir(FeriadoRedeEmpresaVO feriadoRedeEmpresaVO) throws SQLException {
        String sql = "INSERT INTO FeriadoRedeEmpresa " +
                "(feriado,chave,datacadastro,feriadoReplicado, nomeUnidade, mensagemSituacao)" +
                "VALUES" +
                "(?,?,?,?,?,?)";

        try (PreparedStatement preparedStatement = con.prepareStatement(sql)) {
            int i = 1;
            preparedStatement.setInt(i++, feriadoRedeEmpresaVO.getFeriado());
            preparedStatement.setString(i++, feriadoRedeEmpresaVO.getChave());
            preparedStatement.setTimestamp(i++, Uteis.getTimestamp());
            if (feriadoRedeEmpresaVO.getFeriadoReplicado() != null) {
                preparedStatement.setInt(i++, feriadoRedeEmpresaVO.getFeriadoReplicado());
            } else {
                preparedStatement.setNull(i++, 0);
            }
            preparedStatement.setString(i++, feriadoRedeEmpresaVO.getNomeUnidade());
            preparedStatement.setString(i++, feriadoRedeEmpresaVO.getMensagemSituacao());
            preparedStatement.execute();
        }
    }

    public void alterarMensagemSituacao(Integer feriado, String chave, String mensagemSituacao) throws SQLException {
        String sql = "UPDATE FeriadoRedeEmpresa set " +
                "mensagemSituacao = ? " +
                "WHERE feriado = ? AND chave = ?";

        try (PreparedStatement preparedStatement = con.prepareStatement(sql)) {
            int i = 1;
            preparedStatement.setString(i++, mensagemSituacao);
            preparedStatement.setInt(i++, feriado);
            preparedStatement.setString(i++, chave);
            preparedStatement.execute();
        }
    }

    public void alterarDataAtualizacao(Integer feriado, String chave) throws SQLException {
        String sql = "UPDATE FeriadoRedeEmpresa set " +
                "dataatualizacao = ? " +
                "WHERE feriado = ? AND chave = ?";

        try (PreparedStatement preparedStatement = con.prepareStatement(sql)) {
            int i = 1;
            preparedStatement.setTimestamp(i++, Uteis.getTimestamp());
            preparedStatement.setInt(i++, feriado);
            preparedStatement.setString(i++, chave);
            preparedStatement.execute();
        }
    }

    public void alterarDataAtualizacao(Integer feriado, String chave, Integer novoFeriado, String mensagemSituacao) throws SQLException {
        String sql = "UPDATE FeriadoRedeEmpresa set " +
                "dataatualizacao = ?, feriadoReplicado = ?, mensagemSituacao = ? " +
                "WHERE feriado = ? AND chave = ?";

        try (PreparedStatement preparedStatement = con.prepareStatement(sql)) {
            int i = 1;
            preparedStatement.setTimestamp(i++, Uteis.getTimestamp());
            preparedStatement.setInt(i++, novoFeriado);
            preparedStatement.setString(i++, mensagemSituacao);
            preparedStatement.setInt(i++, feriado);
            preparedStatement.setString(i++, chave);
            preparedStatement.execute();
        }
    }

    public FeriadoRedeEmpresaVO consultarPorChaveFeriado(String chave, Integer feriado) throws SQLException {
        String sql = "SELECT * FROM FeriadoRedeEmpresa " +
                "WHERE chave = ? AND feriado = ?";

        try (PreparedStatement preparedStatement = con.prepareStatement(sql)) {
            int i = 1;
            preparedStatement.setString(i++, chave);
            preparedStatement.setInt(i++, feriado);
            try (ResultSet resultSet = preparedStatement.executeQuery()) {
                if (resultSet.next()) {
                    return montarDados(resultSet);
                }
            }
        }
        return null;
    }

    public FeriadoRedeEmpresaVO montarDados(ResultSet resultSet) throws SQLException {
        FeriadoRedeEmpresaVO feriadoRedeEmpresaVO = new FeriadoRedeEmpresaVO();
        feriadoRedeEmpresaVO.setChave(resultSet.getString("chave"));
        feriadoRedeEmpresaVO.setCodigo(resultSet.getInt("codigo"));
        feriadoRedeEmpresaVO.setDatacadastro(resultSet.getDate("datacadastro"));
        feriadoRedeEmpresaVO.setFeriado(resultSet.getInt("feriado"));
        feriadoRedeEmpresaVO.setDataatualizacao(resultSet.getDate("dataatualizacao"));
        feriadoRedeEmpresaVO.setFeriadoReplicado(resultSet.getInt("feriadoreplicado"));
        feriadoRedeEmpresaVO.setNomeUnidade(resultSet.getString("nomeUnidade"));
        feriadoRedeEmpresaVO.setMensagemSituacao(resultSet.getString("mensagemsituacao"));

        return feriadoRedeEmpresaVO;
    }
}

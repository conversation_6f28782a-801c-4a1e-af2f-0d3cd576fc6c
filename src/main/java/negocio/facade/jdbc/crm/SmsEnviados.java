package negocio.facade.jdbc.crm;

import negocio.comuns.crm.SmsEnviadosVO;
import negocio.comuns.utilitarias.Uteis;
import negocio.facade.jdbc.arquitetura.SuperEntidade;
import negocio.interfaces.crm.SmsEnviadosInterfaceFacade;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.util.Date;

public class SmsEnviados  extends SuperEntidade implements SmsEnviadosInterfaceFacade {

    public SmsEnviados(Connection con) throws Exception {
        super(con);
    }

    @Override
    public void incluir(SmsEnviadosVO obj) throws Exception  {

        StringBuilder sql = new StringBuilder();
        sql.append(" INSERT INTO smsenviados (pessoa, dataenvio, cliente, situacao ) ");
        sql.append(" VALUES (?,?,?,?)");
        PreparedStatement stm = con.prepareStatement(sql.toString());
        int i = 0;
        stm.setInt(++i, obj.getPessoa());
        stm.setTimestamp(++i, Uteis.getDataJDBCTimestamp(obj.getDataenvio()));
        stm.setInt(++i, obj.getCliente());
        stm.setString(++i, obj.getSituacao());
        stm.execute();
    }

    @Override
    public void excluir(Date data) throws Exception {
        con.prepareStatement("DELETE FROM smsenviados WHERE dataenvio::date <='" + Uteis.getDataJDBCTimestamp(data) +"'" ).execute();

    }

}

/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package negocio.facade.jdbc.crm;

import br.com.pactosolucoes.comuns.util.JSFUtilities;
import br.com.pactosolucoes.integracao.join.IntegracaoJoinService;
import br.com.pactosolucoes.oamd.controle.basico.DAO;
import negocio.comuns.basico.EmpresaVO;
import negocio.comuns.crm.ConfiguracaoEmpresaBitrixVO;
import negocio.comuns.crm.ConversaoLeadVO;
import negocio.comuns.crm.HistoricoContatoVO;
import negocio.comuns.crm.ObjecaoVO;
import negocio.comuns.utilitarias.Calendario;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisValidacao;
import negocio.facade.jdbc.arquitetura.SuperEntidade;
import negocio.facade.jdbc.basico.Empresa;
import negocio.interfaces.crm.ConfiguracaoSistemaCRMInterfaceFacade;
import negocio.interfaces.crm.ConversaoLeadInterfaceFacade;
import org.json.JSONObject;
import servicos.integracao.enumerador.TipoLeadEnum;
import servicos.integracao.impl.IntegracaoLeadGenericaServiceImpl;
import servicos.integracao.impl.buzzlead.IntegracaoBuzzLeadServiceImpl;
import servicos.integracao.impl.hubspot.IntegracaoHubspostLeadServiceImpl;
import servicos.integracao.impl.rd.IntegracaoRDServiceImpl;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.Statement;
import java.util.ArrayList;
import java.util.List;

/**
 *
 * <AUTHOR>
 */
public class ConversaoLead extends SuperEntidade implements ConversaoLeadInterfaceFacade {

    
    public ConversaoLead() throws Exception {
        super();
    }

    public ConversaoLead(Connection con) throws Exception {
        super(con);
    }

    @Override
    public void incluir(ConversaoLeadVO obj) throws Exception {
        String sql = "INSERT INTO conversaolead(lead, responsavel, contrato, objecao, agendamento, tratada, datacriacao, datalancamento, identificador, props) VALUES ( ?, ?, ?, ?, ?, ?, ?, ?, ?,?);";
        PreparedStatement sqlInserir = con.prepareStatement(sql);
        int i = 1;
        sqlInserir.setInt(i++, obj.getLead().getCodigo());
        sqlInserir.setInt(i++, obj.getResponsavel().getCodigo());
        if (!UteisValidacao.emptyNumber(obj.getContrato().getCodigo())) {
            sqlInserir.setInt(i++, obj.getContrato().getCodigo());
        } else {
            sqlInserir.setNull(i++, 0);
        }
        if (!UteisValidacao.emptyNumber(obj.getObjecao().getCodigo())) {
            sqlInserir.setInt(i++, obj.getObjecao().getCodigo());
        } else {
            sqlInserir.setNull(i++, 0);
        }
        if (!UteisValidacao.emptyNumber(obj.getAgendamento().getCodigo())) {
            sqlInserir.setInt(i++, obj.getAgendamento().getCodigo());
        } else {
            sqlInserir.setNull(i++, 0);
        }
        sqlInserir.setBoolean(i++, obj.isTratada());
        sqlInserir.setTimestamp(i++, Uteis.getDataJDBCTimestamp(obj.getDataCriacao()));
        sqlInserir.setTimestamp(i++, Uteis.getDataJDBCTimestamp(Calendario.hoje()));
        sqlInserir.setString(i++, obj.getIdentificador());
        sqlInserir.setString(i++, obj.getProps());
        sqlInserir.execute();
        obj.setCodigo(obterValorChavePrimariaCodigo());
    }

    @Override
    public void alterar(ConversaoLeadVO obj) throws Exception {
        String sql = "UPDATE conversaolead SET  lead=?, responsavel=?, contrato=?, objecao=?, agendamento=?, tratada=?, datacriacao=?, datalancamento=?, identificador=? WHERE ((codigo = ?))";
        PreparedStatement sqlAlterar = con.prepareStatement(sql);
        int i = 1;
        sqlAlterar.setInt(i++, obj.getLead().getCodigo());
        sqlAlterar.setInt(i++, obj.getResponsavel().getCodigo());
        if (!UteisValidacao.emptyNumber(obj.getContrato().getCodigo())) {
            sqlAlterar.setInt(i++, obj.getContrato().getCodigo());
        } else {
            sqlAlterar.setNull(i++, 0);
        }
        if (!UteisValidacao.emptyNumber(obj.getObjecao().getCodigo())) {
            sqlAlterar.setInt(i++, obj.getObjecao().getCodigo());
        } else {
            sqlAlterar.setNull(i++, 0);
        }
        if (!UteisValidacao.emptyNumber(obj.getAgendamento().getCodigo())) {
            sqlAlterar.setInt(i++, obj.getAgendamento().getCodigo());
        } else {
            sqlAlterar.setNull(i++, 0);
        }
        sqlAlterar.setBoolean(i++, obj.isTratada());
        sqlAlterar.setTimestamp(i++, Uteis.getDataJDBCTimestamp(obj.getDataCriacao()));
        sqlAlterar.setTimestamp(i++, Uteis.getDataJDBCTimestamp(obj.getDataLancamento()));
        sqlAlterar.setString(i++, obj.getIdentificador());
        sqlAlterar.setInt(i++, obj.getCodigo());
        sqlAlterar.execute();
    }

    @Override
    public void excluir(ConversaoLeadVO obj) throws Exception {
        throw new UnsupportedOperationException("Not supported yet."); //To change body of generated methods, choose Tools | Templates.
    }

    @Override
    public ConversaoLeadVO consultarPorChavePrimaria(Integer codigo, int nivelMontarDados) throws Exception {
        String sql = "SELECT * FROM conversaolead WHERE codigo = ?";
        PreparedStatement sqlConsultar = con.prepareStatement(sql);
        sqlConsultar.setInt(1, codigo);
        ResultSet tabelaResultado = sqlConsultar.executeQuery();
        if (!tabelaResultado.next()) {
            return new ConversaoLeadVO();
        }
        return (montarDados(tabelaResultado, nivelMontarDados, this.con));
    }

    @Override
    public List<ConversaoLeadVO> consultarPorNome(String valorConsulta, boolean controlarAcesso, EmpresaVO empresa, int nivelMontarDados) throws Exception {
        throw new UnsupportedOperationException("Not supported yet."); //To change body of generated methods, choose Tools | Templates.
    }

    @Override
    public List<ConversaoLeadVO> consultarPorPassivo(Integer codigoPassivo, int nivelMontarDados) throws Exception {
        StringBuilder sql = new StringBuilder();
        sql.append("SELECT cl.* FROM conversaolead cl inner join lead l  on l.codigo = cl.lead WHERE l.passivo = ").append(codigoPassivo);
        sql.append(" ORDER BY datalancamento");
        Statement stm = con.createStatement();
        ResultSet tabelaResultado = stm.executeQuery(sql.toString());
        return (montarDadosConsulta(tabelaResultado, nivelMontarDados, this.con));
    }

    @Override
    public List<ConversaoLeadVO> consultarPorResponsavel(Integer codigoResponsavel, Integer empresa, Boolean tradada, int nivelMontarDados) throws Exception {
        StringBuilder sql = new StringBuilder();
        sql.append("SELECT cl.* FROM conversaolead cl inner join lead l  on l.codigo = cl.lead  WHERE cl.responsavel = ").append(codigoResponsavel);
        sql.append(" and p.empresa = ").append(empresa);
        if (tradada != null) {
            sql.append(" AND l.tratada =  ").append(tradada.toString());
        }
        sql.append(" ORDER BY datalancamento");
        Statement stm = con.createStatement();
        ResultSet tabelaResultado = stm.executeQuery(sql.toString());
        return (montarDadosConsulta(tabelaResultado, nivelMontarDados, this.con));
    }
    
    public static ConversaoLeadVO montarDados(ResultSet dadosSQL, int nivelMontarDados, Connection con) throws Exception {
        ConversaoLeadVO obj = new ConversaoLeadVO();
        obj.setCodigo(new Integer(dadosSQL.getInt("codigo")));
        obj.getLead().setCodigo(dadosSQL.getInt("lead"));
        obj.getContrato().setCodigo(dadosSQL.getInt("contrato"));
        obj.getObjecao().setCodigo(dadosSQL.getInt("objecao"));
        obj.getAgendamento().setCodigo(dadosSQL.getInt("agendamento"));
        obj.setTratada(dadosSQL.getBoolean("tratada"));
        obj.setIdentificador(dadosSQL.getString("identificador"));
        obj.getResponsavel().setCodigo(new Integer(dadosSQL.getInt("responsavel")));
        obj.setDataLancamento(dadosSQL.getTimestamp("datalancamento"));
        obj.setDataCriacao(dadosSQL.getTimestamp("datacriacao"));
        obj.setProps(dadosSQL.getString("props"));
        obj.setNovoObj(false);
        if (nivelMontarDados == Uteis.NIVELMONTARDADOS_DADOSBASICOS) {
            return obj;
        }
        montarDadosLead(obj, con);
        montarDadosEmpresa(obj, con);
        return obj;
    }
    
    public static List<ConversaoLeadVO> montarDadosConsulta(ResultSet tabelaResultado, int nivelMontarDados, Connection con) throws Exception {
        List<ConversaoLeadVO> vetResultado = new ArrayList<ConversaoLeadVO>();
        while (tabelaResultado.next()) {
            ConversaoLeadVO obj = montarDados(tabelaResultado, nivelMontarDados, con);
            vetResultado.add(obj);
        }
        tabelaResultado = null;
        return vetResultado;
    }
    
    public static void montarDadosLead(ConversaoLeadVO obj, Connection con) throws Exception {
        Lead leadDAO = new Lead(con);
        obj.setLead(leadDAO.consultarPorChavePrimaria(obj.getLead().getCodigo(), Uteis.NIVELMONTARDADOS_TODOS));
        leadDAO = null;
    }
    
    public static void montarDadosEmpresa(ConversaoLeadVO obj, Connection con) throws Exception {
        Empresa empDAO = new Empresa(con);
        obj.getLead().setEmpresa(empDAO.consultarPorChavePrimaria(obj.getLead().getEmpresa().getCodigo(), Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA));
        empDAO = null;
    }
    
    @Override
    public ConversaoLeadVO consultarUltimaConversao(Integer codigoCliente,Integer codigoPessoa, Integer codigoEmpresa, int nivelMontarDados) throws Exception {
        StringBuilder sql = new StringBuilder();
        sql.append("SELECT cl.* FROM conversaolead cl inner join lead l on cl.lead = l.codigo left join passivo p on p.codigo = l.passivo" );
        sql.append(" left join cliente c on c.codigo = l.cliente" );
        sql.append(" left join pessoa pes on c.pessoa = pes.codigo" );
        sql.append(" WHERE ");
        if(UteisValidacao.emptyNumber(codigoCliente)){
            sql.append(" pes.codigo = ").append(codigoPessoa);
        } else {
            sql.append(" c.codigo = ").append(codigoCliente);
        }
         sql.append(" AND l.empresa = ").append(codigoEmpresa);
        sql.append(" ORDER BY datalancamento desc limit 1");
        
        Statement stm = con.createStatement();
        ResultSet tabelaResultado = stm.executeQuery(sql.toString());
        if (!tabelaResultado.next()) {
            return new ConversaoLeadVO();
        }
        return (montarDados(tabelaResultado, nivelMontarDados, this.con));
    }
    
    public void atualizarLeadMetaBatida(Integer codigoConversao, Integer codigoHistorico) throws Exception{
        ConversaoLeadVO conversaoLeadVO = consultarPorChavePrimaria(codigoConversao, Uteis.NIVELMONTARDADOS_TODOS);
        conversaoLeadVO.setTratada(true);
        if(!UteisValidacao.emptyNumber(codigoHistorico)){
            HistoricoContato histDAO = new HistoricoContato(con);
            HistoricoContatoVO historico = histDAO.consultarPorChavePrimaria(codigoHistorico, Uteis.NIVELMONTARDADOS_HISTORICOPASSIVO );
            conversaoLeadVO.setAgendamento(historico.getAgendaVO());
            conversaoLeadVO.setObjecao(historico.getObjecaoVO());
            if(UteisValidacao.emptyNumber(conversaoLeadVO.getContrato().getCodigo()) && !UteisValidacao.emptyNumber(historico.getObjecaoVO().getCodigo())){
                atualizarLeadLost(conversaoLeadVO, historico.getObjecaoVO());
            }
        }
        alterar(conversaoLeadVO);
        
    }

    private void atualizarLeadLost(ConversaoLeadVO conversaoLead, ObjecaoVO objecaoVO) {
        try{
            if (conversaoLead.getLead().getTipo().equals(TipoLeadEnum.RDSTATION) &&
                    (conversaoLead.getLead().getEmpresa().getConfiguracaoRDStation().isAcaoObjecaoLeadAtaulizarQualquer() ||
                            (conversaoLead.getLead().getEmpresa().getConfiguracaoRDStation().isAcaoObjecaoLeadApenasDefinitiva() && objecaoVO.getTipoGrupo().equals("OD")))
            ) {
                IntegracaoRDServiceImpl rdService = new IntegracaoRDServiceImpl(con);
                String chave = (String) JSFUtilities.getFromSession("key");
                rdService.alterarStatusLead(conversaoLead, false, null, objecaoVO.getDescricao() + " - " + objecaoVO.getComentario(), chave);
            }
            if (conversaoLead.getLead().getTipo().equals(TipoLeadEnum.BUZZLEAD) &&
                    (conversaoLead.getLead().getEmpresa().getConfiguracaoIntegracaoBuzzLeadVO().isAcaoObjecaoLeadAtaulizarQualquer() ||
                            (conversaoLead.getLead().getEmpresa().getConfiguracaoIntegracaoBuzzLeadVO().isAcaoObjecaoLeadApenasDefinitiva() && objecaoVO.getTipoGrupo().equals("OD")))
            ) {
                IntegracaoBuzzLeadServiceImpl buzzService = new IntegracaoBuzzLeadServiceImpl(con);
                buzzService.alterarStatusLead(conversaoLead, false, null, objecaoVO.getDescricao() + " - " + objecaoVO.getComentario());
            }
            if(conversaoLead.getLead().getTipo().equals(TipoLeadEnum.HUBSPOT) &&
                    (conversaoLead.getLead().getEmpresa().getConfiguracaoEmpresaHubspot().isAcaoObjecaoLeadAtaulizarQualquer() ||
                            (conversaoLead.getLead().getEmpresa().getConfiguracaoEmpresaHubspot().isAcaoObjecaoLeadApenasDefinitiva() && objecaoVO.getTipoGrupo().equals("OD")))
            ) {
                IntegracaoHubspostLeadServiceImpl hubspost = new IntegracaoHubspostLeadServiceImpl(this.con);
                String chave = (String) JSFUtilities.getFromSession("key");
                hubspost.alterarStatusLead(conversaoLead, false, null, "",chave);
            }

            if(conversaoLead.getLead().getTipo().equals(TipoLeadEnum.BITIRX24))
            {
                IntegracaoLeadGenericaServiceImpl bitrix = new IntegracaoLeadGenericaServiceImpl(this.con);
                String chave = (String) JSFUtilities.getFromSession("key");
                List<ConfiguracaoEmpresaBitrixVO> listbitrix = getFacade().getConfiguracaoSistemaCRM().consultarConfiguracaoEmpresaBitrix24(chave);
                conversaoLead.getLead().getEmpresa().setConfiguracaoEmpresaBitrix(listbitrix.get(0));
                ConfiguracaoEmpresaBitrixVO config = conversaoLead.getLead().getEmpresa().getConfiguracaoEmpresaBitrix();
                String jsonString  = config.getAcao();
                jsonString = jsonString.replace("'", "\"");
                JSONObject jsonObject = new JSONObject(jsonString);
                String jsonFields =  bitrix.leadFieldBitrix(config.getUrl(), "l");
                JSONObject json = Uteis.extractField(jsonFields, "Status Pacto");

                JSONObject fields = new JSONObject();
                String objecao = conversaoLead.getLead().getEmpresa().getConfiguracaoEmpresaBitrix().isAcaoObjecaoLeadApenasDefinitiva() ?  "OBJEÇÃO" : "OBJEÇÃO SIMPLES";
                fields.put(json.get("field").toString(), objecao);
                bitrix.updateStausBitrix(config.getUrl() + jsonObject.getString("updatelead") , (int) conversaoLead.getLead().getIdLead(),fields);
            }

            if ((conversaoLead.getLead().getTipo().equals(TipoLeadEnum.GENERICO) && conversaoLead.getMensagem().equals("lead_origem_join")) &&
                    (conversaoLead.getLead().getEmpresa().getConfiguracaoIntegracaoJoinVO().isAcaoObjecaoLeadAtaulizarQualquer() ||
                            (conversaoLead.getLead().getEmpresa().getConfiguracaoIntegracaoJoinVO().isAcaoObjecaoLeadApenasDefinitiva() && objecaoVO.getTipoGrupo().equals("OD")))
            ) {
                try {
                    IntegracaoJoinService joinService = new IntegracaoJoinService();
                    joinService.enviar(DAO.resolveKeyFromConnection(con), conversaoLead, 5);
                } catch (Exception e) {
                    e.printStackTrace();
                }
            }
        }catch(Exception e){
            Uteis.logar(e, this.getClass());
        }
    }

    public List<ConversaoLeadVO> consultarTodosAcao(String identificador) throws Exception {
        String sql = "select *\n" +
                "from conversaolead\n" +
                "where 1 = 1\n" +
                "  AND (contrato > 0 or objecao > 0)\n" +
                "  and identificador ilike '"+identificador+"';";

        try (PreparedStatement ps = con.prepareStatement(sql);
             ResultSet rs = ps.executeQuery()) {
                return montarDadosConsulta(rs, Uteis.NIVELMONTARDADOS_TODOS, con);
        }
    }

}

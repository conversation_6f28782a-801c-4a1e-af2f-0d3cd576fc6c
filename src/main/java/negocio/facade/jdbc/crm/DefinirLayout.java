package negocio.facade.jdbc.crm;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.Statement;
import java.util.ArrayList;
import java.util.List;

import negocio.comuns.arquitetura.UsuarioVO;
import negocio.comuns.crm.DefinirLayoutVO;
import negocio.comuns.utilitarias.ConsistirException;
import negocio.comuns.utilitarias.Uteis;
import negocio.facade.jdbc.arquitetura.SuperEntidade;
import negocio.interfaces.crm.DefinirLayoutInterfaceFacade;

/**
 * Classe de persistência que encapsula todas as operações de manipulação dos dados da classe <code>DefinirLayoutVO</code>.
 * Responsável por implementar operações como incluir, alterar, excluir e consultar pertinentes a classe <code>DefinirLayoutVO</code>.
 * Encapsula toda a interação com o banco de dados.
 * @see DefinirLayoutVO
 * @see SuperEntidade
*/
public class DefinirLayout extends SuperEntidade implements DefinirLayoutInterfaceFacade {    
	
    public DefinirLayout() throws Exception {
        super();        
    }
	
    /**
     * Operação responsável por retornar um novo objeto da classe <code>DefinirLayoutVO</code>.
    */
    public DefinirLayoutVO novo() throws Exception {
        incluirCRM(getIdEntidade());
        DefinirLayoutVO obj = new DefinirLayoutVO();
        return obj;
    }

    /**
     * Operação responsável por incluir no banco de dados um objeto da classe <code>DefinirLayoutVO</code>.
     * Primeiramente valida os dados (<code>validarDados</code>) do objeto. Verifica a conexão com o banco de dados e a permissão do usuário
     * para realizar esta operacão na entidade.
     * Isto, através da operação <code>incluir</code> da superclasse.
     * @param obj  Objeto da classe <code>DefinirLayoutVO</code> que será gravado no banco de dados.
     * @exception Exception Caso haja problemas de conexão, restrição de acesso ou validação de dados.
    */
    public void incluir(DefinirLayoutVO obj) throws Exception {
        try {
            con.setAutoCommit(false);
            DefinirLayoutVO.validarDados(obj);
            incluirCRM(getIdEntidade());
            obj.realizarUpperCaseDados();
            String sql = "INSERT INTO DefinirLayout( usuario, titulo, url, sequencia ) VALUES ( ?, ?, ?, ? )";
            PreparedStatement sqlInserir = con.prepareStatement(sql);
            if (obj.getUsuario().getCodigo().intValue() != 0) {
            	sqlInserir.setInt( 1, obj.getUsuario().getCodigo().intValue() );
            } else {
                sqlInserir.setNull(1, 0);
            }
            sqlInserir.setString( 2, obj.getTitulo() );
            sqlInserir.setString( 3, obj.getUrl() );
            sqlInserir.setInt( 4, obj.getSequencia().intValue() );
            sqlInserir.execute();
            obj.setCodigo(obterValorChavePrimariaCodigo());
            obj.setNovoObj(new Boolean(false));
            con.commit();
        } catch (Exception e) {
            obj.setNovoObj(new Boolean(true));
            con.rollback();
            con.setAutoCommit(true);
            throw e;
        } finally {
            con.setAutoCommit(true);
        }
    }

    /**
     * Operação responsável por alterar no BD os dados de um objeto da classe <code>DefinirLayoutVO</code>.
     * Sempre utiliza a chave primária da classe como atributo para localização do registro a ser alterado.
     * Primeiramente valida os dados (<code>validarDados</code>) do objeto. Verifica a conexão com o banco de dados e a permissão do usuário
     * para realizar esta operacão na entidade.
     * Isto, através da operação <code>alterar</code> da superclasse.
     * @param obj    Objeto da classe <code>DefinirLayoutVO</code> que será alterada no banco de dados.
     * @exception Execption Caso haja problemas de conexão, restrição de acesso ou validação de dados.
    */
    public void alterar(DefinirLayoutVO obj) throws Exception {
        try {
            con.setAutoCommit(false);
            DefinirLayoutVO.validarDados(obj);
            alterarCRM(getIdEntidade());
            obj.realizarUpperCaseDados();
            String sql = "UPDATE DefinirLayout set usuario=?, titulo=?, url=?, sequencia=? WHERE ((codigo = ?))";
            PreparedStatement sqlAlterar = con.prepareStatement(sql);
            if (obj.getUsuario().getCodigo().intValue() != 0) {
            	sqlAlterar.setInt( 1, obj.getUsuario().getCodigo().intValue() );
            } else {
            	sqlAlterar.setNull(1, 0);
            }
            sqlAlterar.setString( 2, obj.getTitulo() );
            sqlAlterar.setString( 3, obj.getUrl() );
            sqlAlterar.setInt( 4, obj.getSequencia().intValue() );
            sqlAlterar.setInt( 5, obj.getCodigo().intValue() );
            sqlAlterar.execute();
            con.commit();
        } catch (Exception e) {
            con.rollback();
            con.setAutoCommit(true);
            throw e;
        } finally {
            con.setAutoCommit(true);
        }
    }

    /**
     * Operação responsável por excluir no BD um objeto da classe <code>DefinirLayoutVO</code>.
     * Sempre localiza o registro a ser excluído através da chave primária da entidade.
     * Primeiramente verifica a conexão com o banco de dados e a permissão do usuário
     * para realizar esta operacão na entidade.
     * Isto, através da operação <code>excluir</code> da superclasse.
     * @param obj    Objeto da classe <code>DefinirLayoutVO</code> que será removido no banco de dados.
     * @exception Execption Caso haja problemas de conexão ou restrição de acesso.
    */
    public void excluir(DefinirLayoutVO obj) throws Exception {
        try {
            con.setAutoCommit(false);
            excluirCRM(getIdEntidade());
            String sql = "DELETE FROM DefinirLayout WHERE ((codigo = ?))";
            PreparedStatement sqlExcluir = con.prepareStatement(sql);
            sqlExcluir.setInt( 1, obj.getCodigo().intValue() );
            sqlExcluir.execute();
            con.commit();
        } catch (Exception e) {
            con.rollback();
            con.setAutoCommit(true);
            throw e;
        } finally {
            con.setAutoCommit(true);
        }
    }

    /**
     * Responsável por realizar uma consulta de <code>DefinirLayout</code> através do valor do atributo 
     * <code>Integer usuario</code>. Retorna os objetos com valores iguais ou superiores ao parâmetro fornecido.
     * Faz uso da operação <code>montarDadosConsulta</code> que realiza o trabalho de prerarar o List resultante.
     * @param   controlarAcesso Indica se a aplicação deverá verificar se o usuário possui permissão para esta consulta ou não.
     * @return  List Contendo vários objetos da classe <code>DefinirLayoutVO</code> resultantes da consulta.
     * @exception Exception Caso haja problemas de conexão ou restrição de acesso.
     */
    public List<DefinirLayoutVO> consultarPorUsuario(Integer valorConsulta, boolean controlarAcesso, int nivelMontarDados) throws Exception {
        consultarCRM(getIdEntidade(), controlarAcesso);
        String sqlStr = "SELECT * FROM DefinirLayout WHERE usuario = " + valorConsulta + " ORDER BY sequencia";
        Statement stm = con.createStatement();
        ResultSet tabelaResultado = stm.executeQuery(sqlStr);
        return (montarDadosConsulta(tabelaResultado, nivelMontarDados));
        }

    /**
     * Responsável por realizar uma consulta de <code>DefinirLayout</code> através do valor do atributo 
     * <code>Integer codigo</code>. Retorna os objetos com valores iguais ou superiores ao parâmetro fornecido.
     * Faz uso da operação <code>montarDadosConsulta</code> que realiza o trabalho de prerarar o List resultante.
     * @param   controlarAcesso Indica se a aplicação deverá verificar se o usuário possui permissão para esta consulta ou não.
     * @return  List Contendo vários objetos da classe <code>DefinirLayoutVO</code> resultantes da consulta.
     * @exception Exception Caso haja problemas de conexão ou restrição de acesso.
     */
    public List consultarPorCodigo(Integer valorConsulta, boolean controlarAcesso, int nivelMontarDados) throws Exception {
        consultarCRM(getIdEntidade(), controlarAcesso);
        String sqlStr = "SELECT * FROM DefinirLayout WHERE codigo >= " + valorConsulta.intValue() + " ORDER BY codigo";
        Statement stm = con.createStatement();
        ResultSet tabelaResultado = stm.executeQuery(sqlStr);
        return (montarDadosConsulta(tabelaResultado, nivelMontarDados));
        }

    /**
     * Responsável por montar os dados de vários objetos, resultantes de uma consulta ao banco de dados (<code>ResultSet</code>).
     * Faz uso da operação <code>montarDados</code> que realiza o trabalho para um objeto por vez.
     * @return  List Contendo vários objetos da classe <code>DefinirLayoutVO</code> resultantes da consulta.
    */
    public static List<DefinirLayoutVO> montarDadosConsulta(ResultSet tabelaResultado, int nivelMontarDados) throws Exception {
        List<DefinirLayoutVO> vetResultado = new ArrayList<DefinirLayoutVO>();
        while (tabelaResultado.next()) {
            DefinirLayoutVO obj = montarDados(tabelaResultado, nivelMontarDados);
            vetResultado.add(obj);
        }
        tabelaResultado = null;
        return vetResultado;
    }

    /**
     * Responsável por montar os dados resultantes de uma consulta ao banco de dados (<code>ResultSet</code>)
     * em um objeto da classe <code>DefinirLayoutVO</code>.
     * @return  O objeto da classe <code>DefinirLayoutVO</code> com os dados devidamente montados.
    */
    public static DefinirLayoutVO montarDados(ResultSet dadosSQL, int nivelMontarDados) throws Exception {
        DefinirLayoutVO obj = new DefinirLayoutVO();
        obj.setCodigo( new Integer( dadosSQL.getInt("codigo")));
        obj.getUsuario().setCodigo( new Integer( dadosSQL.getInt("usuario")));
        obj.setTitulo( dadosSQL.getString("titulo"));
        obj.setUrl( dadosSQL.getString("url"));
        obj.setSequencia(dadosSQL.getInt("sequencia"));
        obj.setNovoObj(false);
        if (nivelMontarDados == Uteis.NIVELMONTARDADOS_DADOSBASICOS) {
            return obj;
        }
        montarDadosUsuario(obj, nivelMontarDados);

        return obj;
    }
    
    /**
     * Operação responsável por montar os dados de um objeto da classe <code>PaisVO</code> relacionado ao objeto <code>CidadeVO</code>.
     * Faz uso da chave primária da classe <code>PaisVO</code> para realizar a consulta.
     * @param obj  Objeto no qual será montado os dados consultados.
     */
    public static void montarDadosUsuario(DefinirLayoutVO obj, int nivelMontarDados) throws Exception {
        if (obj.getUsuario().getCodigo().intValue() == 0) {
            obj.setUsuario(new UsuarioVO());
            return;
        }
        obj.setUsuario(getFacade().getUsuario().consultarPorChavePrimaria(obj.getUsuario().getCodigo(), nivelMontarDados));
    }

    /**
     * Operação responsável por localizar um objeto da classe <code>DefinirLayoutVO</code>
     * através de sua chave primária. 
     * @exception Exception Caso haja problemas de conexão ou localização do objeto procurado.
    */
    public DefinirLayoutVO consultarPorChavePrimaria( Integer codigoPrm, int nivelMontarDados ) throws Exception {
        consultarCRM(getIdEntidade(), false);
        String sql = "SELECT * FROM DefinirLayout WHERE codigo = ?";
        PreparedStatement sqlConsultar = con.prepareStatement(sql);
        sqlConsultar.setInt(1, codigoPrm.intValue() );
        ResultSet tabelaResultado = sqlConsultar.executeQuery();
        if (!tabelaResultado.next()) {
            throw new ConsistirException("Dados Não Encontrados ( DefinirLayout ).");
        }
        return (montarDados(tabelaResultado, nivelMontarDados));
    }

    /**
     * Operação responsável por obter o último valor gerado para uma chave primária.
     * É utilizada para obter o valor gerado pela SGBD para uma chave primária, 
     * a apresentação do mesmo e a implementação de possíveis relacionamentos. 
     */
    public  Integer obterValorChavePrimariaCodigo() throws Exception {
        inicializar();
        String sqlStr = "SELECT MAX(codigo) FROM DefinirLayout";
        Statement stm = con.createStatement();
        ResultSet tabelaResultado = stm.executeQuery(sqlStr);
        tabelaResultado.next();
        return (new Integer( tabelaResultado.getInt(1)) );
    }
}
package negocio.facade.jdbc.crm;

import br.com.pactosolucoes.comuns.util.Formatador;
import java.sql.Connection;
import java.sql.ResultSet;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

import negocio.comuns.basico.ClienteVO;
import negocio.comuns.crm.ClientePotencialVO;
import negocio.comuns.crm.IndicadoVO;
import negocio.comuns.crm.PassivoVO;
import negocio.comuns.utilitarias.Uteis;
import negocio.facade.jdbc.arquitetura.SuperEntidade;

public class ClientePotencial extends SuperEntidade {

    public ClientePotencial() throws Exception {
        super();
    }

    public ClientePotencial(Connection conexao) throws Exception {
        super(conexao);
    }

    public void executarValidacaoCadastroPassivoOrIndicado(ClienteVO cliente) throws Exception {
        if (cliente.getCodigoIndicado() != 0) {
            getFacade().getIndicado().executarAlteracaoPorCadastroCliente(cliente.getCodigoIndicado(), cliente.getCodigo());
            getFacade().getHistoricoContato().incluirHistoricoContatoConversaoCliente(cliente);
            getFacade().getAgenda().alterarAgendamentoPassivoIndicadoParaCliente(cliente.getCodigo(), null, cliente.getCodigoIndicado());
        } else if (cliente.getCodigoPassivo() != 0) {
            getFacade().getPassivo().executarAlteracaoPorCadastroCliente(cliente.getCodigoPassivo(), cliente.getCodigo());
            getFacade().getHistoricoContato().incluirHistoricoContatoConversaoCliente(cliente);
            getFacade().getAgenda().alterarAgendamentoPassivoIndicadoParaCliente(cliente.getCodigo(), cliente.getCodigoPassivo(), null);
        }
    }

    public List executarConsultareDeUmClientePotencial(String valorConsultar) throws Exception {
        ResultSet reslt = getFacade().getAgenda().consultarAgendadosParaHojePassivoAndIndicado(valorConsultar, negocio.comuns.utilitarias.Calendario.hoje(), false);
        return montarDadosConsulta(reslt);
    }

    /**
     * Responsável por montar os dados de vários objetos, resultantes de uma
     * consulta ao banco de dados (<code>ResultSet</code>). Faz uso da operação
     * <code>montarDados</code> que realiza o trabalho para um objeto por vez.
     *
     * @return List Contendo vários objetos da classe <code>AgendaVO</code>
     *         resultantes da consulta.
     */
    public static List montarDadosConsulta(ResultSet tabelaResultado) throws Exception {
        List vetResultado = new ArrayList();
        while (tabelaResultado.next()) {
            ClientePotencialVO obj = new ClientePotencialVO();
            obj = montarDados(tabelaResultado);
            vetResultado.add(obj);
        }
        tabelaResultado = null;
        return vetResultado;
    }

    /**
     * Responsável por montar os dados resultantes de uma consulta ao banco de
     * dados (<code>ResultSet</code>) em um objeto da classe
     * <code>AgendaVO</code>.
     *
     * @return O objeto da classe <code>AgendaVO</code> com os dados devidamente
     *         montados.
     */
    public static ClientePotencialVO montarDados(ResultSet dadosSQL) throws Exception {
        ClientePotencialVO obj = new ClientePotencialVO();

        if (dadosSQL.getString("tipopessoa").equals("PA")) {
            obj.getPassivoVO().setCodigo(new Integer(dadosSQL.getInt("codigo")));
            montarDadosPassivo(obj, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
            obj.setTelefone("Residencial:" + obj.getPassivoVO().getTelefoneResidencial() + " Trabalho:" + obj.getPassivoVO().getTelefoneTrabalho() + " Celular:" + obj.getPassivoVO().getTelefoneCelular());
        } else if (dadosSQL.getString("tipopessoa").equals("IN")) {
            obj.getIndicadoVO().setCodigo(new Integer(dadosSQL.getInt("codigo")));
            montarDadosIndicado(obj, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
            obj.setTelefone("Residencial:" + obj.getIndicadoVO().getTelefoneIndicado() + " Telefone: " + obj.getIndicadoVO().getTelefone());
        } else {
            obj.getClienteVO().setCodigo(new Integer(dadosSQL.getInt("codigo")));
            montarDadosCliente(obj, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
            obj.setTelefone(obj.getClienteVO().obterTelefoneCliente(obj.getClienteVO()));
        }
        obj.setTipoPessoa(dadosSQL.getString("tipoPessoa"));
        obj.setDataNasc(Formatador.formatarDataPadrao(dadosSQL.getDate("datanasc")));
        return obj;
    }

    /**
     * Operação responsável por montar os dados de um objeto da classe
     * <code>PassivoVO</code> relacionado ao objeto <code>AgendaVO</code>. Faz
     * uso da chave primária da classe <code>PassivoVO</code> para realizar a
     * consulta.
     *
     * @param obj
     *            Objeto no qual será montado os dados consultados.
     */
    public static void montarDadosPassivo(ClientePotencialVO obj, int nivelMontarDados) throws Exception {
        if (obj.getPassivoVO().getCodigo().intValue() == 0) {
            obj.setPassivoVO(new PassivoVO());
            return;
        }
        obj.setPassivoVO(getFacade().getPassivo().consultarPorChavePrimaria(obj.getPassivoVO().getCodigo(), nivelMontarDados));
    }

    /**
     * Operação responsável por montar os dados de um objeto da classe
     * <code>PassivoVO</code> relacionado ao objeto <code>AgendaVO</code>. Faz
     * uso da chave primária da classe <code>PassivoVO</code> para realizar a
     * consulta.
     *
     * @param obj
     *            Objeto no qual será montado os dados consultados.
     */
    public static void montarDadosCliente(ClientePotencialVO obj, int nivelMontarDados) throws Exception {
        if (obj.getClienteVO().getCodigo().intValue() == 0) {
            obj.setClienteVO(new ClienteVO());
            return;
        }
        obj.setClienteVO(getFacade().getCliente().consultarPorChavePrimaria(obj.getClienteVO().getCodigo(), nivelMontarDados));
    }

    /**
     * Operação responsável por montar os dados de um objeto da classe
     * <code>IndicacaoVO</code> relacionado ao objeto <code>AgendaVO</code>. Faz
     * uso da chave primária da classe <code>IndicacaoVO</code> para realizar a
     * consulta.
     *
     * @param obj
     *            Objeto no qual será montado os dados consultados.
     */
    public static void montarDadosIndicado(ClientePotencialVO obj, int nivelMontarDados) throws Exception {
        if (obj.getIndicadoVO().getCodigo().intValue() == 0) {
            obj.setIndicadoVO(new IndicadoVO());
            return;
        }
        obj.setIndicadoVO(getFacade().getIndicado().consultarPorChavePrimaria(obj.getIndicadoVO().getCodigo(), nivelMontarDados));
    }
}

/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package negocio.facade.jdbc.crm;

import negocio.comuns.crm.OAuth2RDStationVO;
import negocio.comuns.utilitarias.Uteis;
import negocio.facade.jdbc.arquitetura.SuperEntidade;
import negocio.interfaces.crm.OAuth2RDStationInterfaceFacade;

import java.sql.Connection;
import java.sql.ResultSet;
import java.sql.Statement;
import java.util.ArrayList;
import java.util.List;

/**
 *
 * <AUTHOR>
 */
public class OAuth2RDStation extends SuperEntidade implements OAuth2RDStationInterfaceFacade {


    public OAuth2RDStation() throws Exception {
        super();
    }

    public OAuth2RDStation(Connection con) throws Exception {
        super(con);
    }

    @Override
    public OAuth2RDStationVO consultarPorEmpresa(Integer codigoEmpresa, int nivelMontarDados) throws Exception {
        Statement stm = con.createStatement();
        ResultSet tabelaResultado = stm.executeQuery("SELECT * FROM oauth2rdstation where codigoempresa = " + codigoEmpresa);
        if (!tabelaResultado.next()) {
            return new OAuth2RDStationVO();
        }
        return (montarDados(tabelaResultado, nivelMontarDados, this.con));
    }

    public static OAuth2RDStationVO montarDados(ResultSet dadosSQL, int nivelMontarDados, Connection con) throws Exception {
        OAuth2RDStationVO obj = new OAuth2RDStationVO();
        obj.setCodigo(dadosSQL.getInt("codigo"));
        obj.setCodigoEmpresa(dadosSQL.getInt("codigoempresa"));
        obj.setCodigoAuth(dadosSQL.getString("codigoauth"));
        obj.setAcessToken(dadosSQL.getString("access_token"));
        obj.setRefreshToken(dadosSQL.getString("refresh_token"));
        obj.setDataPersistir(dadosSQL.getDate("datapersistir"));
        obj.setNovoObj(false);
        if (nivelMontarDados == Uteis.NIVELMONTARDADOS_DADOSBASICOS) {
            return obj;
        }
        return obj;
    }
    
    public static List<OAuth2RDStationVO> montarDadosConsulta(ResultSet tabelaResultado, int nivelMontarDados, Connection con) throws Exception {
        List<OAuth2RDStationVO> vetResultado = new ArrayList<OAuth2RDStationVO>();
        while (tabelaResultado.next()) {
            OAuth2RDStationVO obj = new OAuth2RDStationVO();
            obj = montarDados(tabelaResultado, nivelMontarDados, con);
            vetResultado.add(obj);
        }
        tabelaResultado = null;
        return vetResultado;
    }
    
}

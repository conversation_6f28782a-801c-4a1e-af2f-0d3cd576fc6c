package negocio.facade.jdbc.crm;

import negocio.comuns.crm.ConfiguracaoCrmIAVO;
import negocio.facade.jdbc.arquitetura.SuperEntidade;
import negocio.interfaces.crm.ConfiguracaoCrmIAInterfaceFacade;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.util.ArrayList;
import java.util.List;

public class ConfiguracaoCrmIA extends SuperEntidade implements ConfiguracaoCrmIAInterfaceFacade {

    public ConfiguracaoCrmIA() throws Exception {
        super();
    }

    public ConfiguracaoCrmIA(Connection conexao) throws Exception {
        super(conexao);
    }

    @Override
    public List<ConfiguracaoCrmIAVO> consultarTodos() throws Exception {
        String sql = "SELECT * FROM configuracaocrmia";
        try (PreparedStatement sqlConsultar = con.prepareStatement(sql)) {
            try (ResultSet resultSet = sqlConsultar.executeQuery()) {
                return montarDadosConsulta(resultSet);
            }
        }
    }

    private List<ConfiguracaoCrmIAVO> montarDadosConsulta(ResultSet tabelaResultado) throws Exception {
        List<ConfiguracaoCrmIAVO> vetResultado = new ArrayList<ConfiguracaoCrmIAVO>();
        while (tabelaResultado.next()) {
            ConfiguracaoCrmIAVO obj = montarDados(tabelaResultado);
            vetResultado.add(obj);
        }
        return vetResultado;

    }

    public ConfiguracaoCrmIAVO montarDados(ResultSet dadosSQL) throws Exception {
        ConfiguracaoCrmIAVO obj = new ConfiguracaoCrmIAVO();
        obj.setCodigo(dadosSQL.getInt("codigo"));
        obj.setHabilitarConfigIA(dadosSQL.getBoolean("habilitarconfigia"));
        obj.setPersonalidade(dadosSQL.getString("personalidade"));
        obj.setInformacoesAdicionaisAcademia(dadosSQL.getString("informacoesadicionaisacademia"));
        obj.setTokenZApi(dadosSQL.getString("tokenzapi"));
        obj.setIdInstancia(dadosSQL.getString("idinstancia"));
        obj.setPactoConversasLogin(dadosSQL.getString("pactoconversaslogin"));
        obj.setPactoConversasSenha(dadosSQL.getString("pactoconversassenha"));
        obj.setWhatsappBusiness(dadosSQL.getBoolean("whatsappbusiness"));
        obj.setHorarioPadrao(dadosSQL.getTime("horariopadrao"));
        obj.setMatriz(dadosSQL.getBoolean("matriz"));
        obj.setChaveMatriz(dadosSQL.getString("chavematriz"));
        obj.setHabilitarConfigParaRedeAcademias(dadosSQL.getBoolean("habilitarconfigpararedeacademias"));
        obj.setCodigoEmpresa(dadosSQL.getInt("codigoempresa"));
        obj.setNomeMatriz(dadosSQL.getString("nomematriz"));
        return obj;
    }

    @Override
    public ConfiguracaoCrmIAVO consultarPorEmpresa(Integer empresa) throws Exception {
        ConfiguracaoCrmIAVO configuracaoCrmIAVO = null;
        String sql = "SELECT * FROM configuracaocrmia WHERE codigoempresa = ?";
        try (PreparedStatement sqlConsultar = con.prepareStatement(sql)) {
            sqlConsultar.setInt(1, empresa);
            try (ResultSet resultDados = sqlConsultar.executeQuery()) {
                if (resultDados.next()) {
                    configuracaoCrmIAVO = montarDados(resultDados);
                }
            }
        }
        return configuracaoCrmIAVO;
    }

}

package negocio.facade.jdbc.crm;

import negocio.comuns.arquitetura.UsuarioVO;
import negocio.comuns.basico.ColaboradorVO;
import negocio.comuns.basico.TipoColaboradorVO;
import negocio.comuns.crm.GrupoColaboradorParticipanteVO;
import negocio.comuns.utilitarias.ConsistirException;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisValidacao;
import negocio.facade.jdbc.arquitetura.SuperEntidade;

import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.Statement;
import java.util.ArrayList;
import java.util.Date;
import java.util.Iterator;
import java.util.List;

/**
 * Classe de persistência que encapsula todas as operações de manipulação dos dados da classe <code>GrupoColaboradorParticipanteVO</code>.
 * Responsável por implementar operações como incluir, alterar, excluir e consultar pertinentes a classe <code>GrupoColaboradorParticipanteVO</code>.
 * Encapsula toda a interação com o banco de dados.
 * @see GrupoColaboradorParticipanteVO
 * @see SuperEntidade
 * @see GrupoColaborador
 */
public class GrupoColaboradorParticipante extends SuperEntidade {

    public GrupoColaboradorParticipante() throws Exception {
        super();
        setIdEntidade("GrupoColaborador");
    }

    /**
     * Operação responsável por retornar um novo objeto da classe <code>GrupoColaboradorParticipanteVO</code>.
     */
    public GrupoColaboradorParticipanteVO novo() throws Exception {
        incluirCRM(getIdEntidade());
        GrupoColaboradorParticipanteVO obj = new GrupoColaboradorParticipanteVO();
        return obj;
    }

    /**
     * Operação responsável por incluir no banco de dados um objeto da classe <code>GrupoColaboradorParticipanteVO</code>.
     * Primeiramente valida os dados (<code>validarDados</code>) do objeto. Verifica a conexão com o banco de dados e a permissão do usuário
     * para realizar esta operacão na entidade.
     * Isto, através da operação <code>incluir</code> da superclasse.
     * @param obj  Objeto da classe <code>GrupoColaboradorParticipanteVO</code> que será gravado no banco de dados.
     * @exception Exception Caso haja problemas de conexão, restrição de acesso ou validação de dados.
     */
    public void incluir(GrupoColaboradorParticipanteVO obj) throws Exception {
        GrupoColaboradorParticipanteVO.validarDados(obj);
        incluirCRM(getIdEntidade());
        obj.realizarUpperCaseDados();
        String sql = "INSERT INTO GrupoColaboradorParticipante( colaboradorParticipante, grupoColaborador, tipoVisao ) VALUES ( ?, ?, ? )";
        PreparedStatement sqlInserir = con.prepareStatement(sql);
        if (obj.getColaboradorParticipante().getCodigo().intValue() != 0) {
            sqlInserir.setInt(1, obj.getColaboradorParticipante().getCodigo().intValue());
        } else {
            sqlInserir.setNull(1, 0);
        }
        if (obj.getGrupoColaborador().getCodigo().intValue() != 0) {
            sqlInserir.setInt(2, obj.getGrupoColaborador().getCodigo().intValue());
        } else {
            sqlInserir.setNull(2, 0);
        }
        sqlInserir.setString(3, obj.getTipoVisao());
        sqlInserir.execute();
        obj.setCodigo(obterValorChavePrimariaCodigo());
        obj.setNovoObj(new Boolean(false));
    }

    /**
     * Operação responsável por alterar no BD os dados de um objeto da classe <code>GrupoColaboradorParticipanteVO</code>.
     * Sempre utiliza a chave primária da classe como atributo para localização do registro a ser alterado.
     * Primeiramente valida os dados (<code>validarDados</code>) do objeto. Verifica a conexão com o banco de dados e a permissão do usuário
     * para realizar esta operacão na entidade.
     * Isto, através da operação <code>alterar</code> da superclasse.
     * @param obj    Objeto da classe <code>GrupoColaboradorParticipanteVO</code> que será alterada no banco de dados.
     * @exception Exception Caso haja problemas de conexão, restrição de acesso ou validação de dados.
     */
    public void alterar(GrupoColaboradorParticipanteVO obj) throws Exception {
        GrupoColaboradorParticipanteVO.validarDados(obj);
        alterarCRM(getIdEntidade());
        obj.realizarUpperCaseDados();
        String sql = "UPDATE GrupoColaboradorParticipante set colaboradorParticipante=?, grupoColaborador=?, tipoVisao=? WHERE ((codigo = ?)) returning codigo";
        PreparedStatement sqlAlterar = con.prepareStatement(sql);
        if (obj.getColaboradorParticipante().getCodigo().intValue() != 0) {
            sqlAlterar.setInt(1, obj.getColaboradorParticipante().getCodigo().intValue());
        } else {
            sqlAlterar.setNull(1, 0);
        }
        if (obj.getGrupoColaborador().getCodigo().intValue() != 0) {
            sqlAlterar.setInt(2, obj.getGrupoColaborador().getCodigo().intValue());
        } else {
            sqlAlterar.setNull(2, 0);
        }
        sqlAlterar.setString(3, obj.getTipoVisao());
        sqlAlterar.setInt(4, obj.getCodigo().intValue());
        if (!sqlAlterar.executeQuery().next()) {
            incluir(obj);
        }
    }


    public void alteraTipoVisao(int colaborador, String tipoVisao) throws Exception{
        try {

            con.setAutoCommit(false);
            alteraTipoVisaoSemCommit(colaborador, tipoVisao);
            con.commit();

        } catch (Exception e) {
            con.rollback();
            con.setAutoCommit(true);
            throw e;
        } finally {
            con.setAutoCommit(true);
        }
    }

    public void alteraTipoVisaoSemCommit(int colaborador, String tipoVisao) throws Exception{
        String sql = "UPDATE GrupoColaboradorParticipante set tipoVisao=? WHERE colaboradorparticipante = ?";
        PreparedStatement sqlAlterar = con.prepareStatement(sql);
        sqlAlterar.setString(1, tipoVisao);
        sqlAlterar.setInt(2, colaborador);
        sqlAlterar.execute();

    }

    /**
     * Operação responsável por excluir no BD um objeto da classe <code>GrupoColaboradorParticipanteVO</code>.
     * Sempre localiza o registro a ser excluído através da chave primária da entidade.
     * Primeiramente verifica a conexão com o banco de dados e a permissão do usuário
     * para realizar esta operacão na entidade.
     * Isto, através da operação <code>excluir</code> da superclasse.
     * @param obj    Objeto da classe <code>GrupoColaboradorParticipanteVO</code> que será removido no banco de dados.
     * @exception Exception Caso haja problemas de conexão ou restrição de acesso.
     */
    public void excluir(GrupoColaboradorParticipanteVO obj) throws Exception {
        excluirCRM(getIdEntidade());
        String sql = "DELETE FROM GrupoColaboradorParticipante WHERE ((codigo = ?))";
        PreparedStatement sqlExcluir = con.prepareStatement(sql);
        sqlExcluir.setInt(1, obj.getCodigo().intValue());
        sqlExcluir.execute();
    }

    /**
     * Responsável por realizar uma consulta de <code>GrupoColaboradorParticipante</code> através do valor do atributo 
     * <code>codigo</code> da classe <code>GrupoColaborador</code>
     * Faz uso da operação <code>montarDadosConsulta</code> que realiza o trabalho de prerarar o List resultante.
     * @return  List Contendo vários objetos da classe <code>GrupoColaboradorParticipanteVO</code> resultantes da consulta.
     * @exception Exception Caso haja problemas de conexão ou restrição de acesso.
     */
    public List consultarPorCodigoGrupoColaborador(Integer valorConsulta, int nivelMontarDados) throws Exception {
        consultarCRM(getIdEntidade(), true);
        String sqlStr = "SELECT GrupoColaboradorParticipante.* FROM GrupoColaboradorParticipante, GrupoColaborador WHERE GrupoColaboradorParticipante.grupoColaborador = GrupoColaborador.codigo and GrupoColaborador.codigo >= " + valorConsulta.intValue() + " ORDER BY GrupoColaborador.codigo";
        Statement stm = con.createStatement();
        ResultSet tabelaResultado = stm.executeQuery(sqlStr);
        return montarDadosConsulta(tabelaResultado, nivelMontarDados);
    }

    /**
     * Responsável por realizar uma consulta de <code>GrupoColaboradorParticipante</code> através do valor do atributo 
     * <code>situacao</code> da classe <code>Colaborador</code>
     * Faz uso da operação <code>montarDadosConsulta</code> que realiza o trabalho de prerarar o List resultante.
     * @return  List Contendo vários objetos da classe <code>GrupoColaboradorParticipanteVO</code> resultantes da consulta.
     * @exception Exception Caso haja problemas de conexão ou restrição de acesso.
     */
    public List consultarPorSituacaoColaborador(String valorConsulta, int nivelMontarDados) throws Exception {
        consultarCRM(getIdEntidade(), true);
        String sqlStr = "SELECT GrupoColaboradorParticipante.* FROM GrupoColaboradorParticipante, Colaborador WHERE GrupoColaboradorParticipante.colaborador = Colaborador.codigo and upper( Colaborador.situacao ) like('" + valorConsulta.toUpperCase() + "%') ORDER BY Colaborador.situacao";
        Statement stm = con.createStatement();
        ResultSet tabelaResultado = stm.executeQuery(sqlStr);
        return montarDadosConsulta(tabelaResultado, nivelMontarDados);
    }

    /**
     * Responsável por realizar uma consulta de <code>GrupoColaboradorParticipante</code> através do valor do atributo 
     * <code>situacao</code> da classe <code>Colaborador</code>
     * Faz uso da operação <code>montarDadosConsulta</code> que realiza o trabalho de prerarar o List resultante.
     * @return  List Contendo vários objetos da classe <code>GrupoColaboradorParticipanteVO</code> resultantes da consulta.
     * @exception Exception Caso haja problemas de conexão ou restrição de acesso.
     */
    public List consultarPorCodigoGrupoColaboradorTipoGrupo(Integer codigo, String tipoGrupo, int nivelMontarDados) throws Exception {
        consultarCRM(getIdEntidade(), true);
        String sqlStr = "select "
                + "gcp.codigo as gcp, "
                + "gcp.grupoColaborador as grupo, "
                + "gcp.tipoVisao, "
                + "us.codigo as usuario, "
                + "co.codigo as colaborador, "
                + "pe.codigo as pessoa, "
                + "pe.nome as nome, "
                + "SUM( "
                + "(select  Count (Vinculo.codigo) as cliente  from Vinculo "
                + "where Vinculo.colaborador = co.codigo and Vinculo.tipovinculo = '" + tipoGrupo.toUpperCase() + "' "
                + "))   as totalcliente "
                + "from GrupoColaboradorParticipante as gcp "
                + "inner join Usuario as us on gcp.usuario = us.codigo "
                + "inner join Colaborador as co on us.colaborador = co.codigo "
                + "inner join Pessoa as pe on pe.codigo = co.pessoa "
                + "where " + codigo + " = gcp.grupoColaborador and gcp.tipoVisao <> 'VI' "
                + "group  by  gcp , us.codigo, co.codigo, pe.nome, pe.codigo, gcp.grupoColaborador, gcp.tipoVisao "
                + "order by gcp.codigo";
        Statement stm = con.createStatement();
        ResultSet tabelaResultado = stm.executeQuery(sqlStr);
        return montarDadosConsulta(tabelaResultado, nivelMontarDados);
    }

    /**
     * Responsável por realizar uma consulta de <code>GrupoColaboradorParticipante</code> através do valor do atributo 
     * <code>situacao</code> da classe <code>Colaborador</code>
     * Faz uso da operação <code>montarDadosConsulta</code> que realiza o trabalho de prerarar o List resultante.
     * @return  List Contendo vários objetos da classe <code>GrupoColaboradorParticipanteVO</code> resultantes da consulta.
     * @exception Exception Caso haja problemas de conexão ou restrição de acesso.
     */
    public List consultarPorCodigoGrupoColaboradorTipoGrupoOrganizadorCarteira(Integer codigo, String tipoGrupo, int nivelMontarDados) throws Exception {
        consultarCRM(getIdEntidade(), true);
        String sqlStr = "select * from ( "
                + "select gcp.codigo as gcp, gcp.grupoColaborador as grupo, co.codigo as colaborador, pe.codigo as pessoa, pe.nome as nome, gcp.tipoVisao, "
                + "SUM( (select  Count (Vinculo.codigo) as cliente  from Vinculo where Vinculo.colaborador = co.codigo and Vinculo.tipovinculo = '" + tipoGrupo.toUpperCase() + "'  )) as totalcliente "
                + "from GrupoColaboradorParticipante as gcp  "
                + "inner join Colaborador as co on gcp.colaboradorParticipante = co.codigo  "
                + "inner join Pessoa as pe on pe.codigo = co.pessoa where " + codigo + " = gcp.grupoColaborador and gcp.tipoVisao <> 'VI' "
                + "group  by  gcp , co.codigo, pe.nome, pe.codigo, gcp.grupoColaborador , gcp.tipoVisao order by gcp.codigo "
                + ") as t  "
                + "Union all "
                + "select * from ( "
                + "select gcp.codigo as gcp, gcp.grupoColaborador as grupo, co.codigo as colaborador, pe.codigo as pessoa, pe.nome as nome, gcp.tipoVisao, "
                + "SUM( (select  Count (Vinculo.codigo) as cliente  from Vinculo where Vinculo.colaborador = co.codigo  )) as totalcliente "
                + "from GrupoColaboradorParticipante as gcp "
                + "inner join Colaborador as co on gcp.colaboradorParticipante = co.codigo  "
                + "inner join Pessoa as pe on pe.codigo = co.pessoa where " + codigo + "= gcp.grupoColaborador and gcp.tipoVisao = 'VI' "
                + "group  by  gcp , co.codigo, pe.nome, pe.codigo, gcp.grupoColaborador , gcp.tipoVisao order by gcp.codigo "
                + ") as t where totalcliente > 0 ";
        Statement stm = con.createStatement();
        ResultSet tabelaResultado = stm.executeQuery(sqlStr);
        return montarDadosConsulta(tabelaResultado, nivelMontarDados);
    }

    /**
     * Responsável por realizar uma consulta de <code>GrupoColaboradorParticipante</code> através do valor do atributo 
     * <code>situacao</code> da classe <code>Colaborador</code>
     * Faz uso da operação <code>montarDadosConsulta</code> que realiza o trabalho de prerarar o List resultante.
     * @return  List Contendo vários objetos da classe <code>GrupoColaboradorParticipanteVO</code> resultantes da consulta.
     * @exception Exception Caso haja problemas de conexão ou restrição de acesso.
     */
    public List consultarPorGrupoOrganizadorCarteiraSemGrupo(Integer empresa) throws Exception {
        consultarCRM(getIdEntidade(), true);
        String sqlStr = "select Distinct on (co.codigo) "
                + "co.codigo as colaborador, "
                // + "us1.codigo as usuario, "
                + "pe.codigo as pessoa, "
                + "pe.nome as nome, "
                + "( select count(vinculo.cliente) from vinculo "
                + "where vinculo.colaborador = vi.colaborador "
                + ") as totalCliente "
                + "from  colaborador as co "
                + "inner join vinculo as vi on vi.colaborador = co.codigo "
                //  + "inner join usuario as us1 on us1.colaborador = co.codigo "
                + "inner join Pessoa as pe on pe.codigo = co.pessoa "
                + "where co.codigo not in( "
                + "select Distinct (co.codigo) from  colaborador as co "
                // + "inner join usuario as us on us.colaborador = co.codigo  "
                + "inner join grupoColaboradorParticipante as gcp on gcp.colaboradorParticipante =  co.codigo  "
                + "inner join grupoColaborador as gc on gc.codigo = gcp.grupocolaborador) ";
        if (!UteisValidacao.emptyNumber(empresa)) {
            sqlStr += " and co.empresa = " + empresa;
        }
        Statement stm = con.createStatement();
        ResultSet tabelaResultado = stm.executeQuery(sqlStr);
        List vetResultado = new ArrayList();
        while (tabelaResultado.next()) {
            GrupoColaboradorParticipanteVO obj = new GrupoColaboradorParticipanteVO();
            obj.getColaboradorParticipante().setCodigo(new Integer(tabelaResultado.getInt("colaborador")));
            obj.getColaboradorParticipante().getPessoa().setCodigo(new Integer(tabelaResultado.getInt("pessoa")));
            obj.getColaboradorParticipante().getPessoa().setNome(tabelaResultado.getString("nome"));
            obj.setTotalCliente(new Long(tabelaResultado.getInt("totalcliente")));
            obj.setTipoVisao("");
            vetResultado.add(obj);
        }
        tabelaResultado = null;
        return vetResultado;
    }
// antes de aalteracao
//    /**
//     * Responsável por realizar uma consulta de <code>GrupoColaboradorParticipante</code> através do valor do atributo 
//     * <code>situacao</code> da classe <code>Colaborador</code>
//     * Faz uso da operação <code>montarDadosConsulta</code> que realiza o trabalho de prerarar o List resultante.
//     * @return  List Contendo vários objetos da classe <code>GrupoColaboradorParticipanteVO</code> resultantes da consulta.
//     * @exception Execption Caso haja problemas de conexão ou restrição de acesso.
//     */
//    public List consultarPorCodigoGrupoColaboradorTipoGrupoDataOrganizadorCarteira(Integer codigo, String tipoGrupo, Date data, int nivelMontarDados) throws Exception {
//        consultarCRM(getIdEntidade(), true);
//        String sqlStr = "select "
//                + "gcp.codigo as gcp, "
//                + "gcp.grupoColaborador as grupo, "
//                //+ "us.codigo as usuario, "
//                + "co.codigo as colaborador, "
//                + "pe.codigo as pessoa, "
//                + "pe.nome as nome, "
//                + "gcp.tipoVisao, "
//                + "SUM( "
//                + "(select  Count (Vinculo.codigo) as cliente  from Vinculo "
//                + "inner join Cliente as cl on cl.codigo = vinculo.cliente "
//                + "inner join Pessoa as pe on pe.codigo =cl.pessoa and pe.datacadastro >= '" + Uteis.getDataJDBC(data) + " 00:00:00' "
//                + "where Vinculo.colaborador = co.codigo and Vinculo.tipovinculo = '" + tipoGrupo.toUpperCase() + "' "
//                + "))   as totalcliente "
//                + "from GrupoColaboradorParticipante as gcp  "
//                //+ "inner join Usuario as us on gcp.usuario = us.codigo "
//                + "inner join Colaborador as co on gcp.colaboradorParticipante = co.codigo "
//                + "inner join Pessoa as pe on pe.codigo = co.pessoa "
//                + "where " + codigo + " = gcp.grupoColaborador and gcp.tipoVisao <> 'VI'	"
//                //+ "group  by  gcp , us.codigo, co.codigo, pe.nome , pe.codigo ,gcp.grupoColaborador, gcp.tipoVisao  "
//                + "group  by  gcp , co.codigo, pe.nome , pe.codigo ,gcp.grupoColaborador, gcp.tipoVisao  "
//                + "order by gcp.codigo";
//        Statement stm = con.createStatement();
//        ResultSet tabelaResultado = stm.executeQuery(sqlStr);
//        return montarDadosConsulta(tabelaResultado, nivelMontarDados);
//    }

    /**
     * Responsável por realizar uma consulta de <code>GrupoColaboradorParticipante</code> através do valor do atributo 
     * <code>situacao</code> da classe <code>Colaborador</code>
     * Faz uso da operação <code>montarDadosConsulta</code> que realiza o trabalho de prerarar o List resultante.
     * @return  List Contendo vários objetos da classe <code>GrupoColaboradorParticipanteVO</code> resultantes da consulta.
     * @exception Exception Caso haja problemas de conexão ou restrição de acesso.
     */
    public List consultarPorCodigoGrupoColaboradorTipoGrupoDataOrganizadorCarteira(Integer codigo, String tipoGrupo, Date data, int nivelMontarDados, Integer empresa) throws Exception {
        consultarCRM(getIdEntidade(), true);

        //montar a sql de consulta
        StringBuilder sqlStr = new StringBuilder();

        sqlStr.append(" SELECT gcp.codigo AS gcp, gcp.grupoColaborador AS grupo, co.codigo AS colaborador,  ");
        sqlStr.append(" pe.codigo AS pessoa, pe.nome AS nome, gcp.tipoVisao, \n");
        sqlStr.append(" (SELECT COUNT(v.codigo) FROM vinculo v \n");
        sqlStr.append(" INNER JOIN cliente AS c ON c.codigo = v.cliente \n");
        sqlStr.append(" INNER JOIN situacaoclientesinteticodw AS sc  ON c.codigo = sc.codigocliente \n");
        sqlStr.append(" WHERE v.colaborador = co.codigo \n");
        if (data != null) {
            sqlStr.append(" AND (sc.situacao IN('AT') \n");
            sqlStr.append(" OR ( sc.situacao in ('IN', 'TR') and sc.datavigenciaateajustada >= '" + Uteis.getDataJDBC(data) + " 00:00:00') \n");
            sqlStr.append(" OR ( sc.situacao in ('VI') and sc.dataultimobv >= '" + Uteis.getDataJDBC(data) + " 00:00:00')) \n");
        }

        if (!UteisValidacao.emptyNumber(empresa)) {
            sqlStr.append(" AND c.empresa = " + empresa);
        }
        sqlStr.append(") AS totalcliente \n");
        sqlStr.append(" FROM GrupoColaboradorParticipante AS gcp    \n");
        sqlStr.append(" INNER JOIN Colaborador AS co ON gcp.colaboradorParticipante = co.codigo \n");
        sqlStr.append(" INNER JOIN Pessoa AS pe ON pe.codigo = co.pessoa \n");
        sqlStr.append(" WHERE gcp.grupoColaborador = " + codigo);
        sqlStr.append(" GROUP BY gcp , co.codigo, pe.nome , pe.codigo ,gcp.grupoColaborador, gcp.tipoVisao \n");
        sqlStr.append(" ORDER BY gcp.codigo \n");

        Statement stm = con.createStatement();
        ResultSet tabelaResultado = stm.executeQuery(sqlStr.toString());
        return montarDadosConsulta(tabelaResultado, nivelMontarDados);
    }

    /**
     * Responsável por realizar uma consulta de <code>GrupoColaboradorParticipante</code> através do valor do atributo 
     * <code>situacao</code> da classe <code>Colaborador</code>
     * Faz uso da operação <code>montarDadosConsulta</code> que realiza o trabalho de prerarar o List resultante.
     * @return  List Contendo vários objetos da classe <code>GrupoColaboradorParticipanteVO</code> resultantes da consulta.
     * @exception Exception Caso haja problemas de conexão ou restrição de acesso.
     */
    public List consultarPorDataOrganizadorCarteiraSemGrupo(Date data, Integer empresa) throws Exception {
        consultarCRM(getIdEntidade(), true);
        StringBuilder sql = new StringBuilder();

        sql.append("SELECT co.codigo  AS colaborador, co.empresa AS empresa, pe.codigo  AS pessoa, pe.nome AS nome, \n");
        sql.append("(SELECT COUNT(v.cliente) FROM vinculo v \n");
        sql.append("	INNER JOIN cliente AS c ON c.codigo = v.cliente \n");
        sql.append("	INNER JOIN situacaoclientesinteticodw AS sc  ON c.codigo = sc.codigocliente \n");
        sql.append("	WHERE (sc.situacao IN('AT') \n");
        sql.append("	OR ( sc.situacao in ('IN', 'TR') and sc.datavigenciaateajustada >= '" + Uteis.getDataJDBC(data) + " 00:00:00') \n");
        sql.append("	OR ( sc.situacao in ('VI') and sc.dataultimobv >= '" + Uteis.getDataJDBC(data) + " 00:00:00')) \n");
        sql.append("	AND co.codigo = v.colaborador) AS totalcliente \n");
        sql.append("FROM colaborador co \n");
        sql.append("INNER JOIN pessoa pe ON pe.codigo = co.pessoa \n");
        sql.append("INNER JOIN vinculo ON vinculo.colaborador = co.codigo \n");
        sql.append("WHERE co.codigo NOT IN (SELECT colaboradorparticipante FROM grupocolaboradorparticipante) \n");
        if (!UteisValidacao.emptyNumber(empresa)) {
            sql.append("AND co.empresa = " + empresa + " \n");
        }
        sql.append("GROUP BY co.codigo, co.empresa, pe.codigo, pe.nome \n");


        Statement stm = con.createStatement();
        ResultSet tabelaResultado = stm.executeQuery(sql.toString());
        List vetResultado = new ArrayList();
        while (tabelaResultado.next()) {
            GrupoColaboradorParticipanteVO obj = new GrupoColaboradorParticipanteVO();
            obj.getColaboradorParticipante().setCodigo(new Integer(tabelaResultado.getInt("colaborador")));
            obj.getColaboradorParticipante().getPessoa().setCodigo(new Integer(tabelaResultado.getInt("pessoa")));
            obj.getColaboradorParticipante().getPessoa().setNome(tabelaResultado.getString("nome"));
            obj.setTotalCliente(new Long(tabelaResultado.getInt("totalcliente")));
            vetResultado.add(obj);
        }
        tabelaResultado = null;
        return vetResultado;
    }

    /**
     * Responsável por realizar uma consulta de <code>GrupoColaboradorParticipante</code> através do valor do atributo 
     * <code>Integer codigo</code>. Retorna os objetos com valores iguais ou superiores ao parâmetro fornecido.
     * Faz uso da operação <code>montarDadosConsulta</code> que realiza o trabalho de prerarar o List resultante.
     * @param   controlarAcesso Indica se a aplicação deverá verificar se o usuário possui permissão para esta consulta ou não.
     * @return  List Contendo vários objetos da classe <code>GrupoColaboradorParticipanteVO</code> resultantes da consulta.
     * @exception Exception Caso haja problemas de conexão ou restrição de acesso.
     */
    public List consultarPorCodigo(Integer valorConsulta, boolean controlarAcesso, int nivelMontarDados) throws Exception {
        consultarCRM(getIdEntidade(), controlarAcesso);
        String sqlStr = "SELECT * FROM GrupoColaboradorParticipante WHERE codigo >= " + valorConsulta.intValue() + " ORDER BY codigo";
        Statement stm = con.createStatement();
        ResultSet tabelaResultado = stm.executeQuery(sqlStr);
        return (montarDadosConsulta(tabelaResultado, nivelMontarDados));
    }

    /**
     * Responsável por montar os dados de vários objetos, resultantes de uma consulta ao banco de dados (<code>ResultSet</code>).
     * Faz uso da operação <code>montarDados</code> que realiza o trabalho para um objeto por vez.
     * @return  List Contendo vários objetos da classe <code>GrupoColaboradorParticipanteVO</code> resultantes da consulta.
     */
    public static List<GrupoColaboradorParticipanteVO> montarDadosConsulta(ResultSet tabelaResultado, int nivelMontarDados) throws Exception {
        List<GrupoColaboradorParticipanteVO> vetResultado = new ArrayList<GrupoColaboradorParticipanteVO>();
        while (tabelaResultado.next()) {
            GrupoColaboradorParticipanteVO obj = new GrupoColaboradorParticipanteVO();
            obj = montarDados(tabelaResultado, nivelMontarDados);
            vetResultado.add(obj);
        }
        tabelaResultado = null;
        return vetResultado;
    }

    public static List<GrupoColaboradorParticipanteVO> montarDadosConsultaColaborador(String tipoGrupo, ResultSet tabelaResultado, int nivelMontarDados) throws Exception {
        List<GrupoColaboradorParticipanteVO> vetResultado = new ArrayList<GrupoColaboradorParticipanteVO>();
        while (tabelaResultado.next()) {
            GrupoColaboradorParticipanteVO obj = new GrupoColaboradorParticipanteVO();
            obj = montarDadosColaboradorParticipante(tipoGrupo, tabelaResultado, nivelMontarDados);
            vetResultado.add(obj);
        }
        tabelaResultado = null;
        return vetResultado;
    }

    public static GrupoColaboradorParticipanteVO montarDadosColaboradorParticipante(String tipoGrupo, ResultSet dadosSQL, int nivelMontarDados) throws Exception {
        GrupoColaboradorParticipanteVO obj = new GrupoColaboradorParticipanteVO();
        obj.getColaboradorParticipante().setCodigo(new Integer(dadosSQL.getInt("codigo")));
        obj.setNovoObj(new Boolean(false));
        montarDadosColaboradorParticipante(obj, nivelMontarDados);
        if (obj.getColaboradorParticipante().getCodigo().intValue() != 0) {
            obj.getColaboradorParticipante().getListaTipoColaboradorVOs();
            Iterator i = obj.getColaboradorParticipante().getListaTipoColaboradorVOs().iterator();
            while (i.hasNext()) {
                TipoColaboradorVO tipo = (TipoColaboradorVO) i.next();
                if (tipo.getDescricao().equals(tipoGrupo)) {
                    obj.getColaboradorParticipante().setTipoColaborador(tipoGrupo);
                    obj.setTipoVisao("AC");
                    obj.setNaoPermitirAlterarTipoVisao(false);
                }
            }
            if (obj.getTipoVisao().equals("")) {
                obj.setTipoVisao("VI");
                obj.setNaoPermitirAlterarTipoVisao(true);
            }
        } else {
            obj.setTipoVisao("VI");
            obj.setNaoPermitirAlterarTipoVisao(true);
        }
        obj.getColaboradorParticipante().setColaboradorEscolhidoIndiceConversao(false);
        return obj;
    }

    /**
     * Responsável por montar os dados resultantes de uma consulta ao banco de dados (<code>ResultSet</code>)
     * em um objeto da classe <code>GrupoColaboradorParticipanteVO</code>.
     * @return  O objeto da classe <code>GrupoColaboradorParticipanteVO</code> com os dados devidamente montados.
     */
    public static GrupoColaboradorParticipanteVO montarDados(ResultSet dadosSQL, int nivelMontarDados) throws Exception {
        GrupoColaboradorParticipanteVO obj = new GrupoColaboradorParticipanteVO();
        if (nivelMontarDados == Uteis.NIVELMONTARDADOS_ORGANIZADORCARTEIRA) {
            obj.setCodigo(new Integer(dadosSQL.getInt("gcp")));
            obj.getGrupoColaborador().setCodigo(new Integer(dadosSQL.getInt("grupo")));
            obj.getColaboradorParticipante().setCodigo(new Integer(dadosSQL.getInt("colaborador")));
            obj.getColaboradorParticipante().getPessoa().setCodigo(new Integer(dadosSQL.getInt("pessoa")));
            obj.getColaboradorParticipante().getPessoa().setNome(dadosSQL.getString("nome"));
            obj.setTotalCliente(new Long(dadosSQL.getInt("totalcliente")));
            obj.setTipoVisao(dadosSQL.getString("tipoVisao"));
            return obj;

        }
        if (nivelMontarDados == Uteis.NIVELMONTARDADOS_RESULTADOS_BI) {
            obj.setCodigo(dadosSQL.getInt("codigo"));
            obj.getGrupoColaborador().setCodigo(dadosSQL.getInt("grupocolaborador"));
            obj.getColaboradorParticipante().setCodigo(dadosSQL.getInt("colaboradorparticipante"));
            obj.setTipoVisao(dadosSQL.getString("tipoVisao"));
            obj.setColaboradorParticipante(getFacade().getColaborador().consultarPorChavePrimaria(
                    obj.getColaboradorParticipante().getCodigo(), Uteis.NIVELMONTARDADOS_MINIMOS));
            return obj;

        }
        if (nivelMontarDados == Uteis.NIVELMONTARDADOS_ABERTURAMETA) {
            obj.setCodigo(new Integer(dadosSQL.getInt("codigo")));
            //obj.getUsuarioParticipante().setCodigo(new Integer(dadosSQL.getInt("usuario")));
            obj.getColaboradorParticipante().setCodigo(new Integer(dadosSQL.getInt("colaboradorParticipante")));
            obj.getGrupoColaborador().setCodigo(new Integer(dadosSQL.getInt("grupoColaborador")));
            obj.setTipoVisao(dadosSQL.getString("tipoVisao"));
            obj.setNovoObj(new Boolean(false));
            obj.setGrupoColaboradorParticipanteEscolhido(new Boolean(false));
            montarDadosUsuarioColaboradorParticipante(obj, Uteis.NIVELMONTARDADOS_TODOS);
            return obj;
        }
        obj.setCodigo(new Integer(dadosSQL.getInt("codigo")));
        obj.getColaboradorParticipante().setCodigo(new Integer(dadosSQL.getInt("colaboradorParticipante")));
        obj.getGrupoColaborador().setCodigo(new Integer(dadosSQL.getInt("grupoColaborador")));
        obj.setTipoVisao(dadosSQL.getString("tipoVisao"));
        obj.setNovoObj(new Boolean(false));
        obj.setGrupoColaboradorParticipanteEscolhido(new Boolean(false));
        if (nivelMontarDados == Uteis.NIVELMONTARDADOS_DADOSBASICOS) {
            obj.setColaboradorParticipante(getFacade().getColaborador().consultarPorChavePrimaria(obj.getColaboradorParticipante().getCodigo(), Uteis.NIVELMONTARDADOS_MINIMOS));
            return obj;
        }
        if (nivelMontarDados == Uteis.NIVELMONTARDADOS_CAIXAPARAOPERADOR) {
            montarDadosColaboradorParticipante(obj, Uteis.NIVELMONTARDADOS_CAIXAPARAOPERADOR);
            return obj;
        }
        montarDadosColaboradorParticipante(obj, Uteis.NIVELMONTARDADOS_DADOSENTIDADESPRINCIPAIS);
        montarDadosUsuarioColaboradorParticipante(obj, Uteis.NIVELMONTARDADOS_TODOS);
        return obj;
    }

    /**
     * Operação responsável por montar os dados de um objeto da classe <code>ColaboradorVO</code> relacionado ao objeto <code>GrupoColaboradorParticipanteVO</code>.
     * Faz uso da chave primária da classe <code>ColaboradorVO</code> para realizar a consulta.
     * @param obj  Objeto no qual será montado os dados consultados.
     */
    public static void montarDadosUsuarioColaboradorParticipante(GrupoColaboradorParticipanteVO obj, int nivelMontarDados) throws Exception {
        if (obj.getColaboradorParticipante().getCodigo().intValue() == 0) {
            obj.setUsuarioParticipante(new UsuarioVO());
        }
        obj.setUsuarioParticipante(getFacade().getUsuario().consultarPorColaboradorEmpresa(obj.getColaboradorParticipante().getCodigo(), obj.getColaboradorParticipante().getEmpresa().getCodigo(), nivelMontarDados));
    }

    /**
     * Operação responsável por montar os dados de um objeto da classe <code>ColaboradorVO</code> relacionado ao objeto <code>GrupoColaboradorParticipanteVO</code>.
     * Faz uso da chave primária da classe <code>ColaboradorVO</code> para realizar a consulta.
     * @param obj  Objeto no qual será montado os dados consultados.
     */
    public static void montarDadosColaboradorParticipante(GrupoColaboradorParticipanteVO obj, int nivelMontarDados) throws Exception {
        if (obj.getColaboradorParticipante().getCodigo().intValue() == 0) {
            obj.setColaboradorParticipante(new ColaboradorVO());
        }
        obj.setColaboradorParticipante(getFacade().getColaborador().consultarPorChavePrimaria(obj.getColaboradorParticipante().getCodigo(), nivelMontarDados));
    }

    /**
     * Operação responsável por excluir todos os objetos da <code>GrupoColaboradorParticipanteVO</code> no BD.
     * Faz uso da operação <code>excluir</code> disponível na classe <code>GrupoColaboradorParticipante</code>.
     * @param grupoColaborador campo chave para exclusão dos objetos no BD.
     * @exception Exception  Erro de conexão com o BD ou restrição de acesso a esta operação.
     */
    public void excluirGrupoColaboradorParticipantes(Integer grupoColaborador) throws Exception {
        excluirCRM(getIdEntidade());
        String sql = "DELETE FROM GrupoColaboradorParticipante WHERE (grupoColaborador = ?)";
        PreparedStatement sqlExcluir = con.prepareStatement(sql);
        sqlExcluir.setInt(1, grupoColaborador.intValue());
        sqlExcluir.execute();
    }

    /**
     * Operação responsável por alterar todos os objetos da <code>GrupoColaboradorParticipanteVO</code> contidos em um Hashtable no BD.
     * Faz uso da operação <code>excluirGrupoColaboradorParticipantes</code> e <code>incluirGrupoColaboradorParticipantes</code> disponíveis na classe <code>GrupoColaboradorParticipante</code>.
     * @param objetos  List com os objetos a serem alterados ou incluídos no BD.
     * @exception Exception  Erro de conexão com o BD ou restrição de acesso a esta operação.
     */
    public void alterarGrupoColaboradorParticipantes(Integer grupoColaborador, List objetos) throws Exception {
        String str = "DELETE FROM GrupoColaboradorParticipante WHERE grupoColaborador = " + grupoColaborador;
        Iterator i = objetos.iterator();
        while (i.hasNext()) {
            GrupoColaboradorParticipanteVO objeto = (GrupoColaboradorParticipanteVO) i.next();
            str += " AND codigo <> " + objeto.getCodigo().intValue();
        }
        PreparedStatement sqlExcluir = con.prepareStatement(str);
        sqlExcluir.execute();
        Iterator e = objetos.iterator();
        while (e.hasNext()) {
            GrupoColaboradorParticipanteVO objeto = (GrupoColaboradorParticipanteVO) e.next();
            if (objeto.getCodigo().equals(0)) {
                incluir(objeto);
            } else {
                alterar(objeto);
            }
        }
    }

    /**
     * Operação responsável por incluir objetos da <code>GrupoColaboradorParticipanteVO</code> no BD.
     * Garantindo o relacionamento com a entidade principal <code>CRM.GrupoColaborador</code> através do atributo de vínculo.
     * @param objetos List contendo os objetos a serem gravados no BD da classe.
     * @exception Exception  Erro de conexão com o BD ou restrição de acesso a esta operação.
     */
    public void incluirGrupoColaboradorParticipantes(Integer grupoColaboradorPrm, List objetos) throws Exception {
        Iterator e = objetos.iterator();
        while (e.hasNext()) {
            GrupoColaboradorParticipanteVO obj = (GrupoColaboradorParticipanteVO) e.next();
            obj.getGrupoColaborador().setCodigo(grupoColaboradorPrm);
            incluir(obj);
        }
    }

    /**
     * Operação responsável por consultar todos os <code>GrupoColaboradorParticipanteVO</code> relacionados a um objeto da classe <code>CRM.GrupoColaborador</code>.
     * @param grupoColaborador  Atributo de <code>CRM.GrupoColaborador</code> a ser utilizado para localizar os objetos da classe <code>GrupoColaboradorParticipanteVO</code>.
     * @return List  Contendo todos os objetos da classe <code>GrupoColaboradorParticipanteVO</code> resultantes da consulta.
     * @exception Exception  Erro de conexão com o BD ou restrição de acesso a esta operação.
     */
    public List consultarGrupoColaboradorParticipantes(Integer grupoColaborador, int nivelMontarDados) throws Exception {
        consultarCRM(getIdEntidade(), false);
        List objetos = new ArrayList();
        String sql = "SELECT * FROM GrupoColaboradorParticipante WHERE grupoColaborador = ?";
        PreparedStatement sqlConsulta = con.prepareStatement(sql);
        sqlConsulta.setInt(1, grupoColaborador.intValue());
        ResultSet resultado = sqlConsulta.executeQuery();
        while (resultado.next()) {
            GrupoColaboradorParticipanteVO novoObj = new GrupoColaboradorParticipanteVO();
            novoObj = montarDados(resultado, nivelMontarDados);
            objetos.add(novoObj);
        }
        return objetos;
    }

    /**
     * Operação responsável por consultar todos os <code>GrupoColaboradorParticipanteVO</code> relacionados a um objeto da classe <code>CRM.GrupoColaborador</code>.
     * @param grupoColaborador  Atributo de <code>CRM.GrupoColaborador</code> a ser utilizado para localizar os objetos da classe <code>GrupoColaboradorParticipanteVO</code>.
     * @return List  Contendo todos os objetos da classe <code>GrupoColaboradorParticipanteVO</code> resultantes da consulta.
     * @exception Exception  Erro de conexão com o BD ou restrição de acesso a esta operação.
     */
    public List consultarGrupoColaboradorParticipantesPorCodigoETipoVisaoDiferente(Integer grupoColaborador, String tipoVisao, int nivelMontarDados) throws Exception {
        consultarCRM(getIdEntidade(), false);
        List objetos = new ArrayList();
        String sql = "SELECT * FROM GrupoColaboradorParticipante WHERE grupoColaborador = ? and tipoVisao <> ?";
        PreparedStatement sqlConsulta = con.prepareStatement(sql);
        sqlConsulta.setInt(1, grupoColaborador.intValue());
        sqlConsulta.setString(2, tipoVisao);
        ResultSet resultado = sqlConsulta.executeQuery();
        while (resultado.next()) {
            GrupoColaboradorParticipanteVO novoObj = new GrupoColaboradorParticipanteVO();
            novoObj = GrupoColaboradorParticipante.montarDados(resultado, nivelMontarDados);
            objetos.add(novoObj);
        }
        return objetos;
    }

    /**
     * Operação responsável por consultar todos os <code>GrupoColaboradorParticipanteVO</code> relacionados a um objeto da classe <code>CRM.GrupoColaborador</code>.
     * @param grupoColaborador  Atributo de <code>CRM.GrupoColaborador</code> a ser utilizado para localizar os objetos da classe <code>GrupoColaboradorParticipanteVO</code>.
     * @return List  Contendo todos os objetos da classe <code>GrupoColaboradorParticipanteVO</code> resultantes da consulta.
     * @exception Exception  Erro de conexão com o BD ou restrição de acesso a esta operação.
     */
    public List consultarGrupoColaboradorParticipantesPorCodigoETipoVisaoDiferenteMontandoUsuario(Integer grupoColaborador, String tipoVisao, int nivelMontarDados) throws Exception {
        consultarCRM(getIdEntidade(), false);
        List objetos = new ArrayList();
        String sql = "SELECT GrupoColaboradorParticipante.*, usuario.codigo as usuario FROM GrupoColaboradorParticipante "
                + "inner join usuario on usuario.colaborador = GrupoColaboradorParticipante.colaboradorParticipante "
                + "WHERE grupoColaborador = ? and tipoVisao <> ?";
        PreparedStatement sqlConsulta = con.prepareStatement(sql);
        sqlConsulta.setInt(1, grupoColaborador.intValue());
        sqlConsulta.setString(2, tipoVisao);
        ResultSet resultado = sqlConsulta.executeQuery();
        while (resultado.next()) {
            GrupoColaboradorParticipanteVO novoObj = new GrupoColaboradorParticipanteVO();
            novoObj = GrupoColaboradorParticipante.montarDados(resultado, nivelMontarDados);
            objetos.add(novoObj);
        }
        return objetos;
    }

    /**
     * Operação responsável por localizar um objeto da classe <code>GrupoColaboradorParticipanteVO</code>
     * através de sua chave primária. 
     * @exception Exception Caso haja problemas de conexão ou localização do objeto procurado.
     */
    public GrupoColaboradorParticipanteVO consultarPorChavePrimaria(Integer codigoPrm, int nivelMontarDados) throws Exception {
        consultarCRM(getIdEntidade(), false);
        String sql = "SELECT * FROM GrupoColaboradorParticipante WHERE codigo = ?";
        PreparedStatement sqlConsultar = con.prepareStatement(sql);
        sqlConsultar.setInt(1, codigoPrm.intValue());
        ResultSet tabelaResultado = sqlConsultar.executeQuery();
        if (!tabelaResultado.next()) {
            throw new ConsistirException("Dados Não Encontrados ( GrupoColaboradorParticipante ).");
        }
        return (montarDados(tabelaResultado, nivelMontarDados));
    }

    public List consultarPorNomePessoaColaboradorSomenteAtivo(String valorConsulta, String tipoGrupo, Integer empresa, Boolean controlarAcesso, int nivelMontarDados, String sql) throws Exception {
        consultar(getIdEntidade(), true);
        String sqlStr = "";
        String sqlEmpresa = empresa != null && empresa != 0 ? "and colaborador.empresa =" + empresa.intValue() :" ";
        if (sql.equals("")) {
            sqlStr = "select colaborador.codigo from colaborador "
                    + "inner join pessoa on pessoa.codigo = colaborador.pessoa  "
                    + "inner join tipoColaborador on tipoColaborador.colaborador = colaborador.codigo "
                    + "and tipocolaborador.descricao = '" + tipoGrupo.toUpperCase() + "' "
                    + "where upper( Pessoa.nome ) like ('" + valorConsulta.toUpperCase() + "%')  "
                    +sqlEmpresa
                    + " and colaborador.codigo not in (select codigo from colaborador "
                    + "where "
                    +"colaborador.situacao <> 'AT' order by colaborador) order by codigo";
        } else {
           sqlStr = "select colaborador.codigo from colaborador "
                    + "inner join pessoa on pessoa.codigo = colaborador.pessoa  "
                    + "inner join tipoColaborador on tipoColaborador.colaborador = colaborador.codigo "
                    + "and tipocolaborador.descricao = '" + tipoGrupo.toUpperCase() + "'"
                    + "where upper( Pessoa.nome ) like ('%" + valorConsulta.toUpperCase() + "%')"
                    +sqlEmpresa
                    + " and colaborador.codigo not in (select codigo from colaborador "
                    + "where colaborador.situacao <> 'AT' order by colaborador) and " + sql;
        }
        Statement stm = con.createStatement();
        ResultSet tabelaResultado = stm.executeQuery(sqlStr);
        return montarDadosConsultaColaborador(tipoGrupo, tabelaResultado, nivelMontarDados);
    }

    public List consultarPorTipoColaboradorSomenteAtivo(String valorConsulta, String tipoGrupo, Integer empresa, Boolean controlarAcesso, int nivelMontarDados, String sql) throws Exception {
        consultar(getIdEntidade(), true);
        String sqlStr = "";
        String sqlEmpresa = empresa != null && empresa != 0 ? "and colaborador.empresa =" + empresa.intValue() :" ";
        if (sql.equals("")) {
            sqlStr = "select distinct ( Colaborador.codigo) from Colaborador "
                    + "inner join tipoColaborador on tipoColaborador.colaborador = colaborador.codigo "
                    + "and upper (tipocolaborador.descricao) like('" + valorConsulta.toUpperCase() + "%') "
                    +sqlEmpresa
                    + "where colaborador.codigo not in "
                    + "(select codigo from colaborador where colaborador.situacao <> 'AT' order by colaborador ) "
                    + "order by colaborador.codigo";
        } else {
            sqlStr = "select distinct (Colaborador.codigo)  from Colaborador "
                    + "inner join tipoColaborador on tipoColaborador.colaborador = colaborador.codigo "
                    + "and upper (tipocolaborador.descricao) like('" + valorConsulta.toUpperCase() + "%') "
                    + sqlEmpresa
                    + "where colaborador.codigo not in "
                    + "(select codigo from colaborador where colaborador.situacao <> 'AT' order by colaborador ) "
                    + "and " + sql + "  order by colaborador.codigo";
        }
        Statement stm = con.createStatement();
        ResultSet tabelaResultado = stm.executeQuery(sqlStr);
        return montarDadosConsultaColaborador(tipoGrupo, tabelaResultado, nivelMontarDados);
    }

    public List consultarPorCodigoColaboradorSomenteAtivo(Integer valorConsulta, String tipoGrupo, Integer empresa, boolean controlarAcesso, int nivelMontarDados, String sql) throws Exception {
        consultar(getIdEntidade(), controlarAcesso);
        String sqlStr = "";
        String sqlEmpresa = empresa != null && empresa != 0 ? " and colaborador.empresa =" + empresa.intValue() :" ";
        if (sql.equals("")) {
            sqlStr = "SELECT Colaborador.* FROM Colaborador "
                    + "left join tipoColaborador on tipoColaborador.colaborador = colaborador.codigo "
                    + "and tipocolaborador.descricao = '" + tipoGrupo.toUpperCase() + "' "
                    + "WHERE Colaborador.codigo = " + valorConsulta.intValue()
                    + " and tipocolaborador.descricao = '" + tipoGrupo.toUpperCase() + "' "
                    + sqlEmpresa
                    + " and colaborador.codigo not in "
                    + "(select codigo from colaborador where colaborador.situacao <> 'AT' order by colaborador ) "
                    + "ORDER BY Colaborador.codigo";
        } else {
            sqlStr = "SELECT Colaborador.* FROM Colaborador "
                    + "left join tipoColaborador on tipoColaborador.colaborador = colaborador.codigo "
                    + "and tipocolaborador.descricao = '" + tipoGrupo.toUpperCase() + "' "
                    + "WHERE "
                    + "tipocolaborador.descricao = '" + tipoGrupo.toUpperCase() + "' and "
                    +"Colaborador.codigo = " + valorConsulta.intValue()
                    + sqlEmpresa
                    + " and " + sql + " and colaborador.codigo not in "
                    + "(select codigo from colaborador where colaborador.situacao <> 'AT' order by colaborador ) "
                    + "ORDER BY Colaborador.codigo";
        }
        Statement stm = con.createStatement();
        ResultSet tabelaResultado = stm.executeQuery(sqlStr);
        return (montarDadosConsultaColaborador(tipoGrupo, tabelaResultado, nivelMontarDados));
    }

    public List<GrupoColaboradorParticipanteVO> consultarPorColaborador(Integer codigoColaborador, int nivelMontarDados) throws Exception {
        String sql = "SELECT * FROM grupocolaboradorparticipante WHERE colaboradorparticipante = ?";
        PreparedStatement sqlConsultar = con.prepareStatement(sql);
        sqlConsultar.setInt(1, codigoColaborador);
        ResultSet tabelaResultado = sqlConsultar.executeQuery();
        return montarDadosConsulta(tabelaResultado, nivelMontarDados);
    }



    public Boolean verificarColaboradorSubstitutoPossuiMesmoGrupoColaboradorDoColaborador(int colaboradorSubstituto, int colaborador) throws Exception {
        String sql = "select * from grupocolaboradorparticipante  where colaboradorparticipante = ? and grupocolaborador in (select grupocolaborador from grupocolaboradorparticipante where colaboradorparticipante = ?)";
        PreparedStatement sqlConsultar = con.prepareStatement(sql);
        sqlConsultar.setInt(1, colaboradorSubstituto);
        sqlConsultar.setInt(2, colaborador);
        ResultSet tabelaResultado = sqlConsultar.executeQuery();
        if (tabelaResultado.next()){
            return true;
        }
        return false;
    }

    public List<GrupoColaboradorParticipanteVO> consultarUsuariosSemGrupo(Integer codEmpresa, int nivelMontarDados) throws Exception {
        StringBuilder sql = new StringBuilder();
        sql.append("SELECT DISTINCT col2.codigo AS colaborador \n");
        sql.append("FROM usuario usu1 \n");
        sql.append("LEFT JOIN usuarioperfilacesso usu2 on usu1.codigo = usu2.usuario \n");
        sql.append("LEFT JOIN colaborador col1 on usu1.colaborador = col1.codigo \n");
        sql.append("LEFT JOIN colaborador col2 on col1.pessoa = col2.pessoa and col2.empresa = usu2.empresa \n");
        sql.append("LEFT JOIN grupocolaboradorparticipante gcp ON gcp.colaboradorparticipante = col2.codigo \n");
        sql.append("WHERE gcp.codigo IS NULL \n");
        sql.append("AND col2.empresa = ? \n");
        sql.append("AND col2.situacao = 'AT' \n");
        PreparedStatement sqlConsultar = con.prepareStatement(sql.toString());
        int i = 0;
        sqlConsultar.setInt(++i, codEmpresa);
        List<GrupoColaboradorParticipanteVO> vetResultado = new ArrayList<GrupoColaboradorParticipanteVO>();

        ResultSet tabelaResultado = sqlConsultar.executeQuery();
        GrupoColaboradorParticipanteVO obj;
        while (tabelaResultado.next()) {
            obj = new GrupoColaboradorParticipanteVO();
            obj.getColaboradorParticipante().setCodigo(tabelaResultado.getInt("colaborador"));
            montarDadosColaboradorParticipante(obj, Uteis.NIVELMONTARDADOS_MINIMOS);
            montarDadosUsuarioColaboradorParticipante(obj, Uteis.NIVELMONTARDADOS_MINIMOS);
            vetResultado.add(obj);
        }
        tabelaResultado = null;
        return vetResultado;

    }

}

package negocio.facade.jdbc.crm;

import negocio.comuns.acesso.enumerador.MeioIdentificacaoEnum;
import negocio.comuns.acesso.enumerador.SituacaoAcessoEnum;
import negocio.comuns.crm.EventoVO;
import negocio.comuns.financeiro.BIEventoDTO;
import negocio.comuns.utilitarias.Calendario;
import negocio.comuns.utilitarias.Calendario;
import negocio.comuns.utilitarias.ConsistirException;
import negocio.comuns.utilitarias.Dominios;
import negocio.comuns.utilitarias.Uteis;
import negocio.facade.jdbc.arquitetura.SuperEntidade;
import negocio.interfaces.crm.EventoInterfaceFacade;
import org.json.JSONArray;
import org.json.JSONObject;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.sql.Statement;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * Classe de persistência que encapsula todas as operações de manipulação dos dados da classe <code>EventoVO</code>.
 * Responsável por implementar operações como incluir, alterar, excluir e consultar pertinentes a classe <code>EventoVO</code>.
 * Encapsula toda a interação com o banco de dados.
 * @see EventoVO
 * @see SuperEntidade
 */
public class Evento extends SuperEntidade implements EventoInterfaceFacade {    

    public Evento() throws Exception {
        super();        
    }

    public Evento(Connection con) throws Exception {
		super(con);
                
	}

	/**
     * Operação responsável por retornar um novo objeto da classe <code>EventoVO</code>.
     */
    public EventoVO novo() throws Exception {
        incluirCRM(getIdEntidade());
        EventoVO obj = new EventoVO();
        return obj;
    }

    /**
     * Operação responsável por incluir no banco de dados um objeto da classe <code>EventoVO</code>.
     * Primeiramente valida os dados (<code>validarDados</code>) do objeto. Verifica a conexão com o banco de dados e a permissão do usuário
     * para realizar esta operacão na entidade.
     * Isto, através da operação <code>incluir</code> da superclasse.
     * @param obj  Objeto da classe <code>EventoVO</code> que será gravado no banco de dados.
     * @exception Exception Caso haja problemas de conexão, restrição de acesso ou validação de dados.
     */
    public void incluir(EventoVO obj) throws Exception {
        try {
            con.setAutoCommit(false);
            EventoVO.validarDados(obj);
            incluirCRM(getIdEntidade());
            obj.realizarUpperCaseDados();
            String sql = "INSERT INTO Evento( descricao, dataLancamento,vigenciaInicial,vigenciaFinal,observacao ) VALUES ( ?, ?,?,?,? )";
            PreparedStatement sqlInserir = con.prepareStatement(sql);
            sqlInserir.setString(1, obj.getDescricao());
            sqlInserir.setTimestamp(2,Uteis.getDataJDBCTimestamp(Calendario.hoje()));
            resolveDateNull(sqlInserir, 3,obj.getVigenciaInicial());
            resolveDateNull(sqlInserir, 4,obj.getVigenciaFinal());
            resolveStringNull(sqlInserir, 5,obj.getObservacao());

            sqlInserir.execute();
            obj.setCodigo(obterValorChavePrimariaCodigo());
            obj.setNovoObj(new Boolean(false));
            con.commit();
        } catch (Exception e) {
            obj.setNovoObj(new Boolean(true));
            con.rollback();
            con.setAutoCommit(true);
            throw e;
        } finally {
            con.setAutoCommit(true);
        }
    }

    /**
     * Operação responsável por alterar no BD os dados de um objeto da classe <code>EventoVO</code>.
     * Sempre utiliza a chave primária da classe como atributo para localização do registro a ser alterado.
     * Primeiramente valida os dados (<code>validarDados</code>) do objeto. Verifica a conexão com o banco de dados e a permissão do usuário
     * para realizar esta operacão na entidade.
     * Isto, através da operação <code>alterar</code> da superclasse.
     * @param obj    Objeto da classe <code>EventoVO</code> que será alterada no banco de dados.
     * @exception Exception Caso haja problemas de conexão, restrição de acesso ou validação de dados.
     */
    public void alterar(EventoVO obj) throws Exception {
        try {
            con.setAutoCommit(false);
            EventoVO.validarDados(obj);
            alterarCRM(getIdEntidade());
            obj.realizarUpperCaseDados();
            String sql = "UPDATE Evento set descricao=?, vigenciaInicial=?, vigenciaFinal=?, observacao=? WHERE ((codigo = ?))";
            PreparedStatement sqlAlterar = con.prepareStatement(sql);
            sqlAlterar.setString(1, obj.getDescricao());
            resolveDateNull(sqlAlterar, 2,obj.getVigenciaInicial());
            resolveDateNull(sqlAlterar, 3,obj.getVigenciaFinal());
            resolveStringNull(sqlAlterar, 4,obj.getObservacao());
            sqlAlterar.setInt(5, obj.getCodigo().intValue());
            sqlAlterar.execute();
            con.commit();
        } catch (Exception e) {
            con.rollback();
            con.setAutoCommit(true);
            throw e;
        } finally {
            con.setAutoCommit(true);
        }
    }

    /**
     * Operação responsável por excluir no BD um objeto da classe <code>EventoVO</code>.
     * Sempre localiza o registro a ser excluído através da chave primária da entidade.
     * Primeiramente verifica a conexão com o banco de dados e a permissão do usuário
     * para realizar esta operacão na entidade.
     * Isto, através da operação <code>excluir</code> da superclasse.
     * @param obj    Objeto da classe <code>EventoVO</code> que será removido no banco de dados.
     * @exception Exception Caso haja problemas de conexão ou restrição de acesso.
     */
    public void excluir(EventoVO obj) throws Exception {
        try {
            con.setAutoCommit(false);
            excluirCRM(getIdEntidade());
            String sql = "DELETE FROM Evento WHERE ((codigo = ?))";
            PreparedStatement sqlExcluir = con.prepareStatement(sql);
            sqlExcluir.setInt(1, obj.getCodigo().intValue());
            sqlExcluir.execute();
            con.commit();
        } catch (Exception e) {
            con.rollback();
            con.setAutoCommit(true);
            throw e;
        } finally {
            con.setAutoCommit(true);
        }
    }

    /**
     * Responsável por realizar uma consulta de <code>Evento</code> através do valor do atributo 
     * <code>Integer codigo</code>. Retorna os objetos com valores iguais ou superiores ao parâmetro fornecido.
     * Faz uso da operação <code>montarDadosConsulta</code> que realiza o trabalho de prerarar o List resultante.
     * @param   controlarAcesso Indica se a aplicação deverá verificar se o usuário possui permissão para esta consulta ou não.
     * @return  List Contendo vários objetos da classe <code>EventoVO</code> resultantes da consulta.
     * @exception Exception Caso haja problemas de conexão ou restrição de acesso.
     */
    public List consultarPorCodigo(Integer valorConsulta, boolean controlarAcesso, int nivelMontarDados) throws Exception {
        consultarCRM(getIdEntidade(), controlarAcesso);
        String sqlStr = "SELECT * FROM Evento WHERE codigo = " + valorConsulta.intValue() + " ORDER BY codigo";
        try (Statement stm = con.createStatement()) {
            try (ResultSet tabelaResultado = stm.executeQuery(sqlStr)) {
                return (montarDadosConsulta(tabelaResultado, nivelMontarDados));
            }
        }
    }

    public List<EventoVO> consultarPorDescricao(String valorConsulta, boolean controlarAcesso, int nivelMontarDados) throws Exception {
        consultarCRM(getIdEntidade(), controlarAcesso);
        String sqlStr = "SELECT * FROM Evento WHERE upper(Evento.descricao) like ('" + valorConsulta.toUpperCase() + "%') ORDER BY Evento.descricao";
        try (Statement stm = con.createStatement()) {
            try (ResultSet tabelaResultado = stm.executeQuery(sqlStr)) {
                return (montarDadosConsulta(tabelaResultado, nivelMontarDados));
            }
        }
    }

    /**
     * Responsável por montar os dados de vários objetos, resultantes de uma consulta ao banco de dados (<code>ResultSet</code>).
     * Faz uso da operação <code>montarDados</code> que realiza o trabalho para um objeto por vez.
     * @return  List Contendo vários objetos da classe <code>EventoVO</code> resultantes da consulta.
     */
    public static List<EventoVO> montarDadosConsulta(ResultSet tabelaResultado, int nivelMontarDados) throws Exception {
        List<EventoVO> vetResultado = new ArrayList<EventoVO>();
        while (tabelaResultado.next()) {
            EventoVO obj = montarDados(tabelaResultado, nivelMontarDados);
            vetResultado.add(obj);
        }
        tabelaResultado = null;
        return vetResultado;
    }

    /**
     * Responsável por montar os dados resultantes de uma consulta ao banco de dados (<code>ResultSet</code>)
     * em um objeto da classe <code>EventoVO</code>.
     * @return  O objeto da classe <code>EventoVO</code> com os dados devidamente montados.
     */
    public static EventoVO montarDados(ResultSet dadosSQL, int nivelMontarDados) throws Exception {
        EventoVO obj = new EventoVO();
        obj.setCodigo(new Integer(dadosSQL.getInt("codigo")));
        obj.setDescricao(dadosSQL.getString("descricao"));
        obj.setStatus(dadosSQL.getString("status"));
        obj.setDataLancamento(dadosSQL.getTimestamp("dataLancamento"));
        obj.setVigenciaInicial(dadosSQL.getDate("vigenciaInicial"));
        obj.setVigenciaFinal(dadosSQL.getDate("vigenciaFinal"));
        obj.setObservacao(dadosSQL.getString("observacao"));
        obj.setNovoObj(new Boolean(false));

        if (nivelMontarDados == Uteis.NIVELMONTARDADOS_DADOSBASICOS) {
            return obj;
        }

        return obj;
    }

    /**
     * Operação responsável por localizar um objeto da classe <code>EventoVO</code>
     * através de sua chave primária. 
     * @exception Exception Caso haja problemas de conexão ou localização do objeto procurado.
     */
    public EventoVO consultarPorChavePrimaria(Integer codigoPrm, int nivelMontarDados) throws Exception {
        consultarCRM(getIdEntidade(), false);
        String sql = "SELECT * FROM Evento WHERE codigo = ?";
        try (PreparedStatement sqlConsultar = con.prepareStatement(sql)) {
            sqlConsultar.setInt(1, codigoPrm.intValue());
            try (ResultSet tabelaResultado = sqlConsultar.executeQuery()) {
                if (!tabelaResultado.next()) {
                    throw new ConsistirException("Dados Não Encontrados ( Evento ).");
                }
                return (montarDados(tabelaResultado, nivelMontarDados));
            }
        }
    }

    public EventoVO consultarPorCodigo(Integer codigoPrm, int nivelMontarDados) throws Exception {
        consultarCRM(getIdEntidade(), false);
        String sql = "SELECT * FROM Evento WHERE codigo = ?";
        try (PreparedStatement sqlConsultar = con.prepareStatement(sql)) {
            sqlConsultar.setInt(1, codigoPrm.intValue());
            try (ResultSet tabelaResultado = sqlConsultar.executeQuery()) {
                if (!tabelaResultado.next()) {
                    return new EventoVO();
                }
                return (montarDados(tabelaResultado, nivelMontarDados));
            }
        }
    }

    public List consultarPorNomeEventoComLimite(String valorConsulta, boolean controlarAcesso, int nivelMontarDados) throws Exception {
        consultar(getIdEntidade(), controlarAcesso);
        StringBuilder sqlStr = new StringBuilder();
        sqlStr.append("SELECT Evento.*  \n");
        sqlStr.append("FROM Evento \n");
        sqlStr.append(" where '").append(new SimpleDateFormat("yyyy-MM-dd").format(Calendario.hoje())).append("' between vigenciaInicial and VigenciaFinal \n");
        sqlStr.append(" AND upper(Evento.descricao) like('").append(valorConsulta.toUpperCase()).append("%') ORDER BY evento.descricao limit 50");
        try (Statement stm = con.createStatement()) {
            try (ResultSet tabelaResultado = stm.executeQuery(sqlStr.toString())) {
                return (montarDadosConsulta(tabelaResultado, nivelMontarDados));
            }
        }
    }
    
    public List consultarTodosEventosComLimite(boolean controlarAcesso, int nivelMontarDados) throws Exception {
    	consultar(getIdEntidade(), controlarAcesso);
        StringBuilder sqlStr = new StringBuilder();
    	sqlStr.append("SELECT Evento.* \n");
    	sqlStr.append("FROM Evento \n");
        sqlStr.append(" where '").append(new SimpleDateFormat("yyyy-MM-dd").format(Calendario.hoje())).append("' between vigenciaInicial and VigenciaFinal \n");
    	sqlStr.append(" ORDER BY evento.descricao limit 50");
        try (Statement stm = con.createStatement()) {
            try (ResultSet tabelaResultado = stm.executeQuery(sqlStr.toString())) {
                return (montarDadosConsulta(tabelaResultado, nivelMontarDados));
            }
        }
    }
    

    public EventoVO consultarPorNomeEvento(String valorConsulta, Integer empresa, int nivelMontarDados) throws Exception {
        consultar(getIdEntidade(), true);
        String sqlStr = "";
        sqlStr = "SELECT Evento.* FROM Evento WHERE  upper( Evento.descricao ) like('" + valorConsulta.toUpperCase() + "%') ORDER BY Evento.descricao";
        try (PreparedStatement sqlConsultar = con.prepareStatement(sqlStr)) {
            try (ResultSet tabelaResultado = sqlConsultar.executeQuery()) {
                if (!tabelaResultado.next()) {
                    return new EventoVO();
                }
                return (montarDados(tabelaResultado, nivelMontarDados));
            }
        }
    }

    public List<EventoVO> consultarPorNomeEvento(String valorConsulta, int nivelMontarDados) throws Exception {
        return consultarPorNomeSituacaoEvento(valorConsulta, null, nivelMontarDados);
    }

    public List<EventoVO> consultarPorNomeSituacaoEvento(String valorConsulta, Boolean situacao, int nivelMontarDados) throws Exception {
        consultar("Cliente", true);
        StringBuilder sql = new StringBuilder();
        sql.append("SELECT Evento.*\n")
                .append("FROM Evento\n")
                .append("WHERE upper( Evento.descricao ) like('").append(valorConsulta.toUpperCase()).append("%')\n");

        if (situacao != null) {
            if (situacao){
                // consultar somente os eventos vigentes.
                sql.append(" and '").append(new SimpleDateFormat("yyyy-MM-dd").format(Calendario.hoje())).append("' between vigenciaInicial and VigenciaFinal ");
            }

        }
        sql.append("ORDER BY Evento.descricao;");

        try (PreparedStatement sqlConsultar = con.prepareStatement(sql.toString())) {
            try (ResultSet tabelaResultado = sqlConsultar.executeQuery()) {
                return (montarDadosConsulta(tabelaResultado, nivelMontarDados));
            }
        }
    }

    private PreparedStatement getPS() throws SQLException {
        StringBuilder sql = new StringBuilder("SELECT * FROM evento;");
        return con.prepareStatement(sql.toString());
    }

    public String consultarJSON() throws Exception {
        JSONObject aaData = new JSONObject();
        JSONArray valores = new JSONArray();
        try (ResultSet rs = getPS().executeQuery()) {
            while (rs.next()) {
                JSONArray itemArray = new JSONArray();
                itemArray.put(rs.getInt("codigo"));
                itemArray.put(rs.getString("descricao"));
                itemArray.put(rs.getDate("vigenciaInicial"));
                itemArray.put(rs.getDate("vigenciaFinal"));
                valores.put(itemArray);
            }
        }
        aaData.put("aaData", valores);
        return aaData.toString();
    }

    public List<EventoVO> consultarTodosEventosAtivos(boolean controlarAcesso, int nivelMontarDados) throws Exception {
        consultar(getIdEntidade(), controlarAcesso);
        StringBuilder sqlStr = new StringBuilder();
        sqlStr.append("SELECT Evento.* \n");
        sqlStr.append(" FROM Evento \n");
        sqlStr.append(" WHERE '").append(new SimpleDateFormat("yyyy-MM-dd").format(Calendario.hoje())).append("' between vigenciaInicial and VigenciaFinal ");
        sqlStr.append(" ORDER BY evento.descricao");
        try (Statement stm = con.createStatement()) {
            try (ResultSet tabelaResultado = stm.executeQuery(sqlStr.toString())) {
                return (montarDadosConsulta(tabelaResultado, nivelMontarDados));
            }
        }
    }

    public List<EventoVO> consultarPorNomeEventoAtivo(String valorConsulta, int nivelMontarDados) throws Exception {
        consultar(getIdEntidade(), true);
        StringBuilder sqlStr = new StringBuilder();
        sqlStr.append("SELECT Evento.* \n");
        sqlStr.append("FROM Evento \n");
        sqlStr.append(" WHERE '").append(new SimpleDateFormat("yyyy-MM-dd").format(Calendario.hoje())).append("' between vigenciaInicial and VigenciaFinal \n");
        sqlStr.append(" AND upper( Evento.descricao ) like('").append(valorConsulta.toUpperCase()).append("%') ORDER BY Evento.descricao");

        try (PreparedStatement sqlConsultar = con.prepareStatement(sqlStr.toString())) {
            try (ResultSet tabelaResultado = sqlConsultar.executeQuery()) {
                return (montarDadosConsulta(tabelaResultado, nivelMontarDados));
            }
        }
    }

    public List<EventoVO> consultarEventosVigente()throws Exception{
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
        List<EventoVO> lista = new ArrayList<>();
        StringBuilder sql = new StringBuilder();
        sql.append("select * \n");
        sql.append("from evento \n");
        sql.append("where '").append(sdf.format(Calendario.hoje())).append("' between vigenciaInicial and VigenciaFinal ");
        try (ResultSet rs = con.createStatement().executeQuery(sql.toString())){
            while (rs.next()){
                lista.add(montarDados(rs,Uteis.NIVELMONTARDADOS_DADOSBASICOS));
            }
            return lista;
        }
    }

    public List<EventoVO>consultar(Date dataBaseConsulta)throws Exception{
        if (dataBaseConsulta == null){
            dataBaseConsulta = Calendario.hoje();
        }
        List<EventoVO> lista = new ArrayList<>();
        StringBuilder sql = new StringBuilder();
        sql.append("select * \n");
        sql.append("from evento  \n");
        sql.append("where '").append(new SimpleDateFormat("yyyy-MM-dd").format(dataBaseConsulta)).append("' between vigenciaInicial and vigenciaFinal \n");
        sql.append(" order by codigo desc");
        ResultSet rs = con.createStatement().executeQuery(sql.toString());
        while (rs.next()){
            lista.add(montarDados(rs,Uteis.NIVELMONTARDADOS_DADOSBASICOS));
        }
        return lista;
    }
}

package negocio.facade.jdbc.crm;

import negocio.comuns.arquitetura.UsuarioVO;
import negocio.comuns.basico.ConfiguracaoEmpresaHubspotVO;
import negocio.facade.jdbc.arquitetura.SuperEntidade;
import negocio.interfaces.crm.ConfiguracaoEmpresaHubspotInterfaceFacade;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;

public class ConfiguracaoEmpresaHubspot extends SuperEntidade implements ConfiguracaoEmpresaHubspotInterfaceFacade {

    public ConfiguracaoEmpresaHubspot(Connection con) throws Exception {
        super(con);
        setIdEntidade("ConfiguracaoEmpresaHubspot");
    }

    @Override
    public void incluir(ConfiguracaoEmpresaHubspotVO obj) throws Exception {

        String sql = "INSERT INTO configuracaoempresahubspot (empresa, empresausahub, clientsecret, horaexpiracao, " +
                "clientId,url_instalacao, url_redirect, appid, token) VALUES (?, ?, ?, ?,?,?,?,?,?);";
        try (PreparedStatement stm = con.prepareStatement(sql)) {
            stm.setInt(1, obj.getEmpresa());
            stm.setBoolean(2, obj.isEmpresausahub());
            stm.setString(3, obj.getClientsecret());
            stm.setString(4, obj.getHoraexpiracao());
            stm.setString(5, obj.getClientId());
            stm.setString(6, obj.getUrl_instalacao());
            stm.setString(7, obj.getUrl_redirect());
            stm.setString(8, obj.getAppId());
            stm.setString(9, obj.getToken());
            stm.execute();
        }
    }

    @Override
    public void alterar(ConfiguracaoEmpresaHubspotVO obj) throws Exception {
        String sql = "UPDATE  configuracaoempresahubspot SET empresa=?, empresausahub=?, clientsecret=?, " +
                "horaexpiracao=?, clientId=?, url_instalacao=?, url_redirect=?, appid=?, token=? where codigo=?";
        try (PreparedStatement stm = con.prepareStatement(sql)) {
            stm.setInt(1, obj.getEmpresa());
            stm.setBoolean(2, obj.isEmpresausahub());
            stm.setString(3, obj.getClientsecret());
            stm.setString(4, obj.getHoraexpiracao());
            stm.setString(5, obj.getClientId());
            stm.setString(6, obj.getUrl_instalacao());
            stm.setString(7, obj.getUrl_redirect());
            stm.setString(8, obj.getAppId());
            stm.setString(9, obj.getToken());
            stm.setInt(10, obj.getCodigo());
            stm.execute();
        }
    }

    public ConfiguracaoEmpresaHubspotVO consultarPorEmpresa(Integer empresa) throws Exception {
        String sql = "SELECT * FROM configuracaoempresahubspot WHERE empresa = ?";
        try (PreparedStatement ps = con.prepareStatement(sql)) {
            ps.setInt(1, empresa);
            try (ResultSet rs = ps.executeQuery()) {
                if (rs.next()) {
                    return montarDadosBasico(rs);
                } else {
                    return new ConfiguracaoEmpresaHubspotVO();
                }
            }
        }
    }

    private ConfiguracaoEmpresaHubspotVO montarDadosBasico(ResultSet rs) throws Exception {
        ConfiguracaoEmpresaHubspotVO obj = new ConfiguracaoEmpresaHubspotVO();
        obj.setNovoObj(false);
        obj.setCodigo(rs.getInt("codigo"));
        obj.setEmpresausahub(rs.getBoolean("empresausahub"));
        obj.setClientId(rs.getString("clientId"));
        obj.setUrl_instalacao(rs.getString("url_instalacao"));
        obj.setUrl_redirect(rs.getString("url_redirect"));
        obj.setHoraexpiracao(rs.getString("horaexpiracao"));
        obj.setEmpresa(rs.getInt("empresa"));
        obj.setClientsecret(rs.getString("clientsecret"));
        obj.setHoraLimite(rs.getString("horalimite"));
        obj.setAcaoObjecao(rs.getInt("acaoobjecao"));
        obj.setAppId(rs.getString("appid"));
        obj.setToken(rs.getString("token"));
        obj.setResponsavelPadrao(new UsuarioVO());
        obj.getResponsavelPadrao().setCodigo(rs.getInt("responsavelpadrao"));
        return obj;
    }
}

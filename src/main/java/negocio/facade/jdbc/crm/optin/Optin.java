package negocio.facade.jdbc.crm.optin;

import br.com.pactosolucoes.comuns.util.FileUtilities;
import controle.arquitetura.SuperControle;
import controle.arquitetura.security.AlgoritmoCriptoEnum;
import negocio.comuns.basico.ClienteVO;
import negocio.comuns.basico.EmpresaVO;
import negocio.comuns.crm.optin.OptinVO;
import negocio.comuns.utilitarias.Calendario;
import negocio.comuns.utilitarias.Criptografia;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisValidacao;
import negocio.comuns.utilitarias.queue.MsgTO;
import negocio.facade.jdbc.arquitetura.SuperEntidade;
import negocio.interfaces.crm.optin.OptinInterfaceFacade;
import org.json.JSONArray;
import servicos.operacoes.midias.MidiaService;
import servicos.operacoes.midias.commons.MidiaEntidadeEnum;
import servicos.propriedades.PropsService;

import javax.servlet.http.HttpSession;
import java.io.File;
import java.sql.*;
import java.util.Objects;

public class Optin extends SuperEntidade implements OptinInterfaceFacade {

    public Optin() throws Exception {
    }

    public Optin(HttpSession session) throws Exception {
        super(session);
    }

    public Optin(Connection conexao) throws Exception {
        super(conexao);
    }

    /**
     * Operação responsável por retornar um novo objeto da classe <code>OptinVO</code>.
     */
    @Override
    public OptinVO novo() throws Exception {
        incluir(getIdEntidade());
        return new OptinVO();
    }

    @Override
    public void incluir(OptinVO obj) throws Exception {
        try {
            OptinVO.validarDados(obj);
            Integer codigoDoRegistro = existeRegistro(obj);

            if (codigoDoRegistro == null) {
                String sql = "INSERT INTO optin (cliente, empresa, email, bloqueadobounce, dataregistro) VALUES(?,?,?,?,?);";

                try (PreparedStatement pStmt = con.prepareStatement(sql)) {

                    resolveIntegerNull(pStmt, 1, obj.getCliente().getCodigo());
                    pStmt.setInt(2, obj.getEmpresa().getCodigo());
                    pStmt.setString(3, obj.getEmail());
                    pStmt.setBoolean(4, obj.getBloqueadoBounce());
                    pStmt.setTimestamp(5, new Timestamp(System.currentTimeMillis()));
                    pStmt.executeUpdate();
                }
            }
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    public void alterar(OptinVO obj, Boolean sesc,String email) throws Exception{
        try {
            OptinVO.validarDados(obj);
            Integer codigoDoRegistro = existeRegistro(obj);

            if (codigoDoRegistro != null){
                String sql = "UPDATE optin SET empresa = ?, email = ?, ipCliente = ?, bloqueadobounce = ?,  origemenvio = ?, dataexclusao= ? WHERE codigo = ?";

                try (PreparedStatement pStmt = con.prepareStatement(sql)){

                    pStmt.setInt(1, obj.getEmpresa().getCodigo());
                    pStmt.setString(2, email);
                    pStmt.setString(3, obj.getIpCliente());
                    pStmt.setBoolean(4, obj.getBloqueadoBounce() );
                    resolveIntegerNull(pStmt,5, obj.getOrigemEnvio() == null?  null : obj.getOrigemEnvio().getCodigo());
                    pStmt.setTimestamp(6, obj.getBloqueadoBounce() ? new Timestamp(System.currentTimeMillis()) : null);
                    pStmt.setInt(7, codigoDoRegistro);
                    pStmt.executeUpdate();
                    atualizarBounce(obj);
                }
            }
            else {
                    obj.setEmail(email);
                    incluir(obj);
            }
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    @Override
    public void alterar(OptinVO obj) throws Exception {
        try {
            OptinVO.validarDados(obj);
            Integer codigoDoRegistro = existeRegistro(obj);

            if (codigoDoRegistro != null){
                String dataAtualizar = obj.getBloqueadoBounce() ? "dataexclusao" : "datainscricao";
                String sql = "UPDATE optin SET empresa = ?, email = ?, ipCliente = ?, bloqueadobounce = ?,  origemenvio = ?, "+dataAtualizar+" = ? WHERE codigo = ?";

                try (PreparedStatement pStmt = con.prepareStatement(sql)){

                        pStmt.setInt(1, obj.getEmpresa().getCodigo());
                        pStmt.setString(2, obj.getEmail());
                        pStmt.setString(3, obj.getIpCliente());
                        pStmt.setBoolean(4, false);
                        resolveIntegerNull(pStmt,5, obj.getOrigemEnvio().getCodigo());
                        pStmt.setTimestamp(6, new Timestamp(System.currentTimeMillis()));
                        pStmt.setInt(7, codigoDoRegistro);

                        pStmt.executeUpdate();
                        atualizarBounce(obj);
                }
            }
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    @Override
    public void alterarSemCommit(OptinVO obj) throws Exception {

    }

    @Override
    public void excluir(OptinVO obj) throws Exception {

    }

    @Override
    public JSONArray consultarTodas() throws Exception {
        return null;
    }

    @Override
    public OptinVO consultarPorChavePrimaria(Integer codigo, int nivelMontarDados) throws Exception {
        return null;
    }

    /**
     *  Verifica se já existe um registro do objeto no BD
     *  método utilizado para validar a persistência de dados
     */
    public Integer existeRegistro(OptinVO obj) throws Exception {

        int idDoRegistro;
        String sql = "SELECT * FROM optin o WHERE o.empresa = ? and o.email = ?";

        if (!Objects.equals(obj.getEmail(), "<EMAIL>")){
            try (PreparedStatement pStmt = con.prepareStatement(sql)) {
                    pStmt.setInt(1, obj.getEmpresa().getCodigo());
                    pStmt.setString(2, obj.getEmail());
                    ResultSet tabelaResultado = pStmt.executeQuery();
                    if (tabelaResultado.next()){
                        idDoRegistro = tabelaResultado.getInt("codigo");
                        return idDoRegistro;
                    }
            }
        }
        return null;
    }

    public void alterarDataEnvio(OptinVO obj) throws Exception {
        try {
            Integer codigoDoRegistro = existeRegistro(obj);

            if (codigoDoRegistro != null){
                String sql = "UPDATE optin SET ultimoenvio = ?  WHERE codigo = ?";

                try (PreparedStatement pStmt = con.prepareStatement(sql)){

                    pStmt.setTimestamp(1, Uteis.getDataJDBCTimestamp(Calendario.hoje()));
                    pStmt.setInt(2, codigoDoRegistro);

                    pStmt.executeUpdate();
                }
            }
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    /**
     *  Atualizará na tabela Email a coluna bloqueadobounce
     *  caso ache nela algum email equivalente ao passado por parâmetro
     */
    public void atualizarBounce(OptinVO obj) throws Exception {
        String sql = "select * from email e where e.email = ?";
        String sql2 = "UPDATE email SET bloqueadobounce = ? WHERE codigo = ? ";

        try (PreparedStatement pStmt = con.prepareStatement(sql)) {
            pStmt.setString(1, obj.getEmail());
            ResultSet tabelaResultado = pStmt.executeQuery();
            if (tabelaResultado.next()){
                try (PreparedStatement pStmt2 = con.prepareStatement(sql2)){
                    pStmt2.setBoolean(1, obj.getBloqueadoBounce());
                    pStmt2.setInt(2, tabelaResultado.getInt("codigo"));
                    pStmt2.executeUpdate();
                }
            }
        }
    }

    @Override
    public boolean precisaRealizarEnvio(OptinVO obj) {
        int idDoRegistro;
        String sql = "SELECT * FROM optin o WHERE o.empresa = ? and o.email = ?";

        if (!Objects.equals(obj.getEmail(), "<EMAIL>")){
            try (PreparedStatement pStmt = con.prepareStatement(sql)) {
                pStmt.setInt(1, obj.getEmpresa().getCodigo());
                pStmt.setString(2, obj.getEmail());
                ResultSet tabelaResultado = pStmt.executeQuery();
                if (tabelaResultado.next()){
                    if (tabelaResultado.getTimestamp("datainscricao") != null
                            || tabelaResultado.getTimestamp("dataexclusao") != null
                            || (tabelaResultado.getTimestamp("ultimoenvio") != null  && Calendario.maiorOuIgual(tabelaResultado.getDate("ultimoenvio"), Calendario.hoje()))) {
                        return false;
                    }
                }
            } catch (SQLException e) {
                throw new RuntimeException(e);
            }
        }
        return true;
    }

    public void enviarEmailOptin(EmpresaVO empresaVO, ClienteVO clienteVO, String chave, boolean sistemaSesc) throws Exception {
        if (sistemaSesc) {
            return;
        }

        if (UteisValidacao.validaEmail(clienteVO.getPessoa().getEmail())) {
            OptinVO optinVO = new OptinVO();
            optinVO.setCliente(clienteVO);
            optinVO.setEmpresa(empresaVO);
            optinVO.setEmail(clienteVO.getPessoa().getEmail());
            optinVO.setBloqueadoBounce(true);
            incluir(optinVO);
            if (precisaRealizarEnvio(optinVO)) {
                enviarEmailOptin(empresaVO, clienteVO, chave, optinVO);
            }
        } else {
            throw new Exception("Não foi possível enviar pois o cliente não possui um email válido.");
        }
    }

    private void enviarEmailOptin(EmpresaVO empresaVO, ClienteVO clienteVO, String chave, OptinVO optinVO) throws Exception {
        String[] emails = new String[]{clienteVO.getPessoa().getEmail()};
        String mensagem = gerarHTMLEmailOptin(clienteVO, empresaVO, chave);
        MsgTO msg = new MsgTO(new StringBuffer(mensagem),
                "Confirmação de recebimento de comunicações e marketing",
                empresaVO.getNome(),
                true, SuperControle.getConfiguracaoSMTPRobo(), false,
                emails
        );
        UteisValidacao.enfileirarEmail(msg);
        alterarDataEnvio(optinVO);
    }

    private String gerarHTMLEmailOptin (ClienteVO clienteVO, EmpresaVO empresaVO, String chave) throws Exception {
        String urlVendas = PropsService.getPropertyValue(PropsService.urlVendasOnline);
        String urlBaseOptIn = PropsService.getPropertyValue(PropsService.urlBaseOptIn);

        File arq = new File(getClass().getResource("/br/com/pactosolucoes/comuns/util/resources/emailOptin.txt").toURI());
        StringBuilder texto = FileUtilities.readContentFile(arq.getAbsolutePath(), "UTF-8");

        Integer codCliente = clienteVO.getCodigo();
        String clienteEmail = clienteVO.getPessoa().getEmail();
        String empresaNome = empresaVO.getNome().toUpperCase();
        String empresaUnidade = String.valueOf(empresaVO.getCodigo());
        String empresaUrlLogo = getLogoEmpresa(chave, empresaVO);
        String empresaEndereco = toTitledCase(empresaVO.getEndereco().replaceAll("\\s{2,}", " ").trim()); //a expressão regular substitui tudo que houver mais de 2 espaços em branco por somente um espaço em branco e o trim remove espaço em branco antes e depois da string
        String empresaNumero = empresaVO.getNumero();
        String empresaSetor = toTitledCase(empresaVO.getSetor());
        String empresaCidade = toTitledCase(empresaVO.getCidade().getNome());
        String empresaTelefone = empresaVO.getTelComercial1();
        String empresaEmail = empresaVO.getEmail().toLowerCase();
        String nomeAluno = clienteVO.getPessoa().getNome();
        String urlConfirmacao = ((!UteisValidacao.emptyString(urlBaseOptIn) && !urlBaseOptIn.contains("@")) ? urlBaseOptIn : urlVendas) + "/email-optin?key=";

        if (clienteVO.getCodigo() == 0) {
            codCliente = obterValorChavePrimariaCodigo(chave);
        }

        String parametros1 = chave + "&" + empresaUnidade + "&" + empresaNome;
        parametros1 = Criptografia.encrypt(parametros1, SuperControle.Crypt_KEY, AlgoritmoCriptoEnum.ALGORITMO_AES);
        String parametros2 = "&" + empresaEmail + "&" + codCliente + "&" + clienteEmail;
        parametros2 = Criptografia.encrypt(parametros2, SuperControle.Crypt_KEY, AlgoritmoCriptoEnum.ALGORITMO_AES);

        String t  =  texto.toString()
                .replaceAll("#ZW_URL#",Uteis.getUrlAplicacao())
                .replaceAll("#ACADEMIA_NOME#", Uteis.trocarAcentuacaoPorAcentuacaoHTML(empresaNome))
                .replaceAll("#ACADEMIA_URL_LOGO#", empresaUrlLogo)
                .replaceAll("#ACADEMIA_ENDERECO#", Uteis.trocarAcentuacaoPorAcentuacaoHTML(empresaEndereco))
                .replaceAll("#ACADEMIA_NUMERO#", Uteis.trocarAcentuacaoPorAcentuacaoHTML(empresaNumero))
                .replaceAll("#ACADEMIA_SETOR#", Uteis.trocarAcentuacaoPorAcentuacaoHTML(empresaSetor))
                .replaceAll("#ACADEMIA_CIDADE#", Uteis.trocarAcentuacaoPorAcentuacaoHTML(empresaCidade))
                .replaceAll("#ACADEMIA_TELEFONE#", empresaTelefone)
                .replaceAll("#ACADEMIA_EMAIL#", Uteis.trocarAcentuacaoPorAcentuacaoHTML(empresaEmail))
                .replaceAll("#NOME_ALUNO#", Uteis.trocarAcentuacaoPorAcentuacaoHTML(nomeAluno))
                .replaceAll("#LINK_EMAIL_OPTIN#", urlConfirmacao + parametros1 + "+" + parametros2);
        return t;
    }


    public void renviarEmailOptin(EmpresaVO empresaVO, ClienteVO clienteVO, String chave) throws Exception {
        if (UteisValidacao.validaEmail(clienteVO.getPessoa().getEmail())) {
            OptinVO optinVO = new OptinVO();
            optinVO.setCliente(clienteVO);
            optinVO.setEmpresa(empresaVO);
            optinVO.setEmail(clienteVO.getPessoa().getEmail());
            optinVO.setBloqueadoBounce(true);
            alterarDataEnvio(optinVO);

            enviarEmailOptin(empresaVO, clienteVO, chave, optinVO);

        } else {
            throw new Exception("Não foi possível enviar pois o cliente não possui um email válido.");
        }
    }

    private String getLogoEmpresa(String chave, EmpresaVO empresaVO) {
        try {
            String genKey = MidiaService.getInstance().genKey(chave, MidiaEntidadeEnum.FOTO_EMPRESA_EMAIL, empresaVO.getCodigo().toString());
            return Uteis.getPaintFotoDaNuvem(genKey);
        } catch (Exception ignored) {
            return "";
        }
    }

    private String toTitledCase(String word){
        String[] words = word.split("\\s");
        StringBuilder sb = new StringBuilder();

        if (!word.isEmpty()) {
            for (String s : words) {
                sb.append(s.substring(0, 1).toUpperCase()).append(s.substring(1).toLowerCase());
                sb.append(" ");
            }
        }
        return sb.toString();
    }
}

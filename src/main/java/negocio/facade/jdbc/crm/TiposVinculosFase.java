package negocio.facade.jdbc.crm;

import java.sql.Connection;
import java.sql.ResultSet;
import java.util.ArrayList;
import java.util.List;

import controle.arquitetura.ModuloAberto;
import controle.arquitetura.security.LoginControle;
import negocio.comuns.crm.TiposVinculosFaseVO;
import negocio.facade.jdbc.arquitetura.SuperEntidade;
import negocio.interfaces.crm.TiposVinculosFaseInterfaceFacade;
import br.com.pactosolucoes.enumeradores.FasesCRMEnum;
import br.com.pactosolucoes.enumeradores.TipoColaboradorEnum;

public class TiposVinculosFase extends SuperEntidade implements TiposVinculosFaseInterfaceFacade{
	
	public TiposVinculosFase() throws Exception {
		super();
	}
	public TiposVinculosFase(Connection con) throws Exception {
		super(con);
	}

	
	/**
	 * <AUTHOR>
	 * 08/11/2011
	 */
	public void incluir(TiposVinculosFaseVO obj) throws Exception {
		try{//deletar os tipos da fase
			String sql = "DELETE FROM tiposvinculosfase WHERE fase = " + obj.getFase().getCodigo();
			TiposVinculosFase.executarConsulta(sql, con);
			con.setAutoCommit(false);
			if(!obj.getTiposColaborador().isEmpty()){
				//incluir os tipos da fase
				for(TipoColaboradorEnum tipo : obj.getTiposColaborador()){
					String sqlIncluir = "INSERT INTO tiposvinculosfase(fase, tipocolaborador) VALUES (" + obj.getFase().getCodigo() + ",'"+tipo.getSigla()+"')";
					TiposVinculosFase.executarConsulta(sqlIncluir, con);
				}
			}
		}catch(Exception e){
			con.rollback();
			throw e;
		}finally{
			con.setAutoCommit(true);
		}
	}

	/**
	 * <AUTHOR>
	 * 08/11/2011
	 */
	public TiposVinculosFaseVO consultarPorFase(Integer fase) throws Exception {
		try (ResultSet rs = TiposVinculosFase.criarConsulta("SELECT tipocolaborador FROM tiposvinculosfase WHERE fase = " + fase, con)) {
			return montarDadosFase(FasesCRMEnum.getFase(fase), rs);
		}
	}

	/**
	 * <AUTHOR>
	 * 08/11/2011
	 */
	public List<TiposVinculosFaseVO> consultarTodos() throws Exception {
		List<TiposVinculosFaseVO> fases = new ArrayList<TiposVinculosFaseVO>();
		boolean estudio = false;
		LoginControle loginControle = (LoginControle) context().getExternalContext().getSessionMap().get("LoginControle");
		List<ModuloAberto> modulosHabilitados = loginControle.getModulosHabilitados();
		for (ModuloAberto modulo : modulosHabilitados){
			if(modulo.getSigla().equals("EST")){
				estudio = true;
			}
		}
		for(FasesCRMEnum fase : FasesCRMEnum.values()){
			if(!fase.getSigla().equals("CI")
					&& !fase.getSigla().equals("CV")
					&& !fase.getSigla().equals("CE")
					&& !fase.getSigla().equals("CA")
					&& !fase.getSigla().equals("CD")
					&& !fase.getSigla().equals("CT")
					&& !fase.getSigla().equals("CL")) {
				if (!estudio) {
					if (!fase.getSigla().equals("SF") && !fase.getSigla().equals("SA")){
						fases.add(consultarPorFase(fase.getCodigo()));
					}
				} else {
					fases.add(consultarPorFase(fase.getCodigo()));
				}
			}
		}
		return fases;
	}

	/**
	 * <AUTHOR>
	 * 08/11/2011
	 */
	public void excluir(TiposVinculosFaseVO obj) throws Exception {
		TiposVinculosFase.executarConsulta("DELETE FROM tiposvinculosfase WHERE fase = "+obj.getFase().getCodigo(), con);
	}

	/**
	 * <AUTHOR>
	 * 08/11/2011
	 */
	public void excluirTodos() throws Exception {
		TiposVinculosFase.executarConsulta("DELETE FROM tiposvinculosfase", con);
	}
	
	/**
	 * <AUTHOR>
	 * 08/11/2011
	 */
	public static TiposVinculosFaseVO montarDadosFase(FasesCRMEnum fase, ResultSet rs) throws Exception{
		TiposVinculosFaseVO vinculosFase = new TiposVinculosFaseVO();
		vinculosFase.setFase(fase);
		while(rs.next()){
			vinculosFase.getTiposColaborador().add(TipoColaboradorEnum.getTipo(rs.getString("tipocolaborador")));
		}
		return vinculosFase;
	}
	@Override
	public void incluir(List<TiposVinculosFaseVO> objs) throws Exception {
		for(TiposVinculosFaseVO tipo : objs){
			incluir(tipo);
		}
	}
	@Override
	public boolean verificarAberturaColaboradorFase(String tipoColaborador, FasesCRMEnum fase) throws Exception {
		String sql = "SELECT * FROM tiposvinculosfase WHERE fase = "+fase.getCodigo()+" AND tipocolaborador LIKE '"+tipoColaborador+"'";
		try (ResultSet rs = TiposVinculosFase.criarConsulta(sql, con)) {
			return rs.next();
		}
	}
	
	/**
	 * <AUTHOR>
	 * 09/11/2011
	 */
	public List<String> consultarTiposColaborador(Integer codigoColaborador) throws Exception{
		List<String> tipos;
		try (ResultSet consulta = TiposVinculosFase.criarConsulta("SELECT descricao FROM tipocolaborador WHERE colaborador = " + codigoColaborador, con)) {
			tipos = new ArrayList<String>();
			while (consulta.next()) {
				tipos.add(consulta.getString("descricao"));
			}
		}
		return tipos;
	}
	
	/**
     * <AUTHOR>
     * 09/11/2011
     */
    public boolean verificarUsarDivisao() throws Exception{
		Boolean dividirfase;
		try (ResultSet consulta = ConfiguracaoSistemaCRM.criarConsulta("SELECT dividirfase FROM configuracaosistemacrm", con)) {
			dividirfase = Boolean.FALSE;
			if (consulta.next()) {
				dividirfase = consulta.getBoolean("dividirfase");
			}
		}
		return dividirfase;
    }

}

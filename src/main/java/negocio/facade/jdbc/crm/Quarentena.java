package negocio.facade.jdbc.crm;

import negocio.comuns.crm.QuarentenaVO;
import negocio.comuns.utilitarias.Calendario;
import negocio.comuns.utilitarias.Uteis;
import negocio.facade.jdbc.arquitetura.SuperEntidade;
import negocio.facade.jdbc.arquitetura.SuperFacadeJDBC;
import negocio.facade.jdbc.arquitetura.Usuario;
import negocio.facade.jdbc.basico.Empresa;
import negocio.interfaces.crm.QuarentenaInterfaceFacade;

import java.util.ArrayList;
import java.util.Date;
import java.sql.*;
import java.util.List;

public class Quarentena extends SuperEntidade implements QuarentenaInterfaceFacade {

    static final String INSERT_INTO = "INSERT INTO Quarentena( inicioquarentena, fimquarentena, ativa, empresa, usuarioIniciou, usuarioEncerrou, motivo)" +
            " VALUES ( ?, ?, ?, ?, ?, ?, ?)";
    static final String ALTER_TABLE = "UPDATE Quarentena set fimquarentena=?, ativa=?, usuarioEncerrou=? WHERE ((codigo = ?))";

    public Quarentena() throws Exception {
        super();
    }

    public Quarentena(Connection conexao) throws Exception {
        super(conexao);
    }

    public void iniciar(QuarentenaVO obj) throws Exception {
        String sql = INSERT_INTO;
        PreparedStatement sqlInserir = con.prepareStatement(sql);
        sqlInserir.setTimestamp(1, Uteis.getDataHoraJDBC(Calendario.hoje(), Uteis.getHoraAtual()));
        sqlInserir.setNull(2, 0);
        sqlInserir.setBoolean(3, true);
        sqlInserir.setInt(4, obj.getEmpresa().getCodigo());
        sqlInserir.setInt(5, obj.getUsuarioIniciou().getCodigo());
        sqlInserir.setNull(6, 0);
        sqlInserir.setString(7, obj.getMotivo());

        sqlInserir.execute();
        obj.setCodigo(obterValorChavePrimariaCodigo());
        obj.setNovoObj(false);
    }

    public void iniciarRetroativa(QuarentenaVO obj) throws Exception {
        String sql = INSERT_INTO;
        PreparedStatement sqlInserir = con.prepareStatement(sql);
        sqlInserir.setTimestamp(1, Uteis.getDataJDBCTimestamp(obj.getInicioQuarentena()));
        sqlInserir.setTimestamp(2, Uteis.getDataJDBCTimestamp(obj.getFimQuarentena()));
        sqlInserir.setBoolean(3, false);
        sqlInserir.setInt(4, obj.getEmpresa().getCodigo());
        sqlInserir.setInt(5, obj.getUsuarioIniciou().getCodigo());
        sqlInserir.setInt(6, obj.getUsuarioIniciou().getCodigo());
        sqlInserir.setString(7, obj.getMotivo());

        sqlInserir.execute();
        obj.setCodigo(obterValorChavePrimariaCodigo());
        obj.setNovoObj(false);
    }

    public void encerrar(QuarentenaVO obj) throws Exception {
        String sql = ALTER_TABLE;
        PreparedStatement sqlAlterar = con.prepareStatement(sql);
        sqlAlterar.setTimestamp(1, Uteis.getDataHoraJDBC(Calendario.hoje(), Uteis.getHoraAtual()));
        sqlAlterar.setBoolean(2, false);
        sqlAlterar.setInt(3, obj.getUsuarioEncerrou().getCodigo());

        sqlAlterar.setInt(4, obj.getCodigo());
        sqlAlterar.execute();
    }

    public QuarentenaVO consultarPorCodigo(int codigo, int nivelMontarDados) throws Exception {
        String sqlStr = "SELECT * FROM Quarentena WHERE codigo = " + codigo;
        try (Statement stm = con.createStatement()) {
            try (ResultSet tabelaResultado = stm.executeQuery(sqlStr)) {
                if (!tabelaResultado.next()) {
                    return new QuarentenaVO();
                }
                return (montarDados(tabelaResultado, nivelMontarDados, con));
            }
        }
    }

    public void alterarDadosGeradosNaParalisacao(Date dataInicio, Date dataFim) throws Exception {
        try {
            SuperFacadeJDBC.executarConsulta("delete from fecharmetadetalhado where codigo in (select fd.codigo from (select codigo from aberturameta a2 where dia between '" + dataInicio + "' and '" + dataFim + "') ab\n" +
                    "inner join fecharmeta fm on ab.codigo = fm.aberturameta\n" +
                    "inner join fecharmetadetalhado fd on fm.codigo = fd.fecharmeta \n" +
                    "where fm.identificadormeta in ('FA','RI'))", con);

            SuperFacadeJDBC.executarConsulta("update fecharmeta set meta = 0 where codigo in (select fm.codigo from (select codigo from aberturameta a2 where dia between  '" + dataInicio + "' and '" + dataFim + "') ab\n" +
                    "inner join fecharmeta fm on ab.codigo = fm.aberturameta\n" +
                    "where fm.identificadormeta in ('FA','RI'))", con);
        } catch (Exception e) {
            Uteis.logar(e, this.getClass());
        }
    }

    public QuarentenaVO obterAtiva(int codEmpresa) throws Exception {
        String sqlStr = "SELECT * FROM Quarentena WHERE ativa = true and empresa = " + codEmpresa;
        try (Statement stm = con.createStatement()) {
            try (ResultSet tabelaResultado = stm.executeQuery(sqlStr)) {
                if (!tabelaResultado.next()) {
                    return new QuarentenaVO();
                }
                return (montarDados(tabelaResultado, Uteis.NIVELMONTARDADOS_DADOSBASICOS, con));
            }
        }
    }

    public QuarentenaVO obterUltimaQuarentenaEncerrada(int codEmpresa) throws Exception {
        String sqlStr = "SELECT * FROM Quarentena WHERE ativa = false and fimquarentena is not null and empresa = " + codEmpresa + " order by codigo desc";
        try (Statement stm = con.createStatement()) {
            try (ResultSet tabelaResultado = stm.executeQuery(sqlStr)) {
                if (!tabelaResultado.next()) {
                    return new QuarentenaVO();
                }
                return (montarDados(tabelaResultado, Uteis.NIVELMONTARDADOS_DADOSBASICOS, con));
            }
        }
    }

    public List<QuarentenaVO> obterTodas(int codEmpresa) throws Exception {
        List<QuarentenaVO> quarentenaVOS = new ArrayList<>();
        String sqlStr = "SELECT * FROM quarentena where empresa = "+codEmpresa;
        try (Statement stm = con.createStatement()) {
            try (ResultSet tabelaResultado = stm.executeQuery(sqlStr)) {
                while (tabelaResultado.next()) {
                    quarentenaVOS.add(montarDados(tabelaResultado, Uteis.NIVELMONTARDADOS_DADOSBASICOS, con));
                }
            }
        }
        return quarentenaVOS;
    }

    private QuarentenaVO montarDados(ResultSet dadosSQL, int nivelMontarDados, Connection con) throws Exception {
        QuarentenaVO obj = new QuarentenaVO();
        if (nivelMontarDados == Uteis.NIVELMONTARDADOS_DADOSBASICOS) {
            obj.setCodigo(dadosSQL.getInt("codigo"));
            obj.setInicioQuarentena(Calendario.getInstance(dadosSQL.getTimestamp("inicioQuarentena")).getTime());
            obj.setFimQuarentena(dadosSQL.getDate("fimQuarentena"));
            obj.setAtiva(dadosSQL.getBoolean("ativa"));
            Empresa empresaDAO = new Empresa(con);
            obj.setEmpresa(empresaDAO.consultarPorCodigo(dadosSQL.getInt("empresa"), nivelMontarDados));
            empresaDAO = null;
            Usuario usuaroDAO = new Usuario(con);
            obj.setUsuarioIniciou(usuaroDAO.consultarPorChavePrimaria(dadosSQL.getInt("usuarioIniciou"), nivelMontarDados));
            try {
                obj.setUsuarioEncerrou(usuaroDAO.consultarPorChavePrimaria(dadosSQL.getInt("usuarioEncerrou"), nivelMontarDados));
            } catch (Exception ignore) {
            } finally {
                usuaroDAO = null;
            }
            obj.setMotivo(dadosSQL.getString("motivo"));
            return obj;
        }
        return obj;
    }

}

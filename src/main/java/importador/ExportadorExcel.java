package importador;

import br.com.pactosolucoes.comuns.util.FileUtilities;
import negocio.comuns.utilitarias.Calendario;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisValidacao;
import negocio.comuns.utilitarias.UtilReflection;
import org.apache.poi.hssf.usermodel.HSSFSheet;
import org.apache.poi.hssf.usermodel.HSSFWorkbook;
import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.CellStyle;
import org.apache.poi.ss.usermodel.FillPatternType;
import org.apache.poi.ss.usermodel.HorizontalAlignment;
import org.apache.poi.ss.usermodel.IndexedColors;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.VerticalAlignment;
import org.json.JSONArray;
import org.json.JSONObject;
import servicos.propriedades.PropsService;

import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

public class ExportadorExcel {

    public File gerarArquivoExcel(String nomeArquivo, JSONArray array, String key) throws IOException {
        return gerarArquivoExcelGeral(nomeArquivo, array, key, null);
    }

    public File gerarArquivoExcelGeral(String nomeArquivo, JSONArray array, String key, String caminhoExportar) throws IOException {

        Uteis.logar(null, "Gerar arquivo excel");

        // Criar arquivo excel
        HSSFWorkbook hssfWorkbook = new HSSFWorkbook();

        int rownum = 0;
        int cellnum = 0;
        Row row;

        // Gerar planilha
//        String sheetName = Calendario.getData(logCobrancaPacto.getDataCobranca(), "yyyyMMdd HHmmss");
        HSSFSheet sheet = hssfWorkbook.createSheet("LISTA");

        // Definindo alguns padroes de layout
        sheet.setDefaultColumnWidth(20);
        sheet.setDefaultRowHeight((short) 300);

        // Configurando Cabecalho
        CellStyle estiloCabecalho = hssfWorkbook.createCellStyle();

        row = sheet.createRow(rownum++);

        JSONObject jsonBase = new JSONObject();
        for (int e = 0; e < array.length(); e++) {
            JSONObject json1 = array.getJSONObject(e);
            if (JSONObject.getNames(jsonBase) == null ||
                    JSONObject.getNames(json1).length > JSONObject.getNames(jsonBase).length) {
                jsonBase = json1;
            }
        }

        String[] colunas = JSONObject.getNames(jsonBase);
        for (String coluna : colunas) {
            cabecalho(estiloCabecalho, row, cellnum++, coluna);
        }

        for (int e = 0; e < array.length(); e++) {
            JSONObject json = array.getJSONObject(e);

            row = sheet.createRow(rownum++);
            cellnum = 0;

            for (String keys : colunas) {
                criarCelula(cellnum++, row, json.opt(keys));
            }
        }

        //salvar arquivo
//        String diretorioBase = PropsService.getPropertyValue(PropsService.diretorioArquivos) + File.separator + "exporta_excel";
        String diretorioBase = "";
        if (UteisValidacao.emptyString(caminhoExportar)) {
            diretorioBase = Uteis.obterCaminhoWeb() + File.separator + "relatorio" + File.separator;
            File pDestino = new File(diretorioBase);
            if (!pDestino.exists()) {
                FileUtilities.forceDirectory(diretorioBase);
            }

            if (UteisValidacao.emptyString(nomeArquivo)) {
                nomeArquivo = "ExportaExcel";
            }
        } else {
            diretorioBase = caminhoExportar;
        }


        File file = new File(diretorioBase + File.separator + nomeArquivo+ "-" + key + "-" + Calendario.hoje().getTime() + ".xls");

        FileOutputStream out = new FileOutputStream(file);
        hssfWorkbook.write(out);
        out.close();
        hssfWorkbook.close();
        return file;
    }

    /**
     *
     */
    public File gerarArquivoExcelPorAtributos(String nomeArquivo, String atributos, List listaItens) throws IOException {

        Uteis.logar(null, "Gerar arquivo excel");


        //Separar atributos
        String[] atributosSeparados = atributos.split(",");

        List<String> listaCampos = new ArrayList<>();
        List<String> listaNomesColuna = new ArrayList<>();

        for(String atributo : atributosSeparados) {
            String[] campoNome = atributo.split("=");
            if(campoNome.length == 2) {
                listaCampos.add(campoNome[0]);
                listaNomesColuna.add(campoNome[1]);
            }
        }


        // Criar arquivo excel
        HSSFWorkbook hssfWorkbook = new HSSFWorkbook();

        int rownum = 0;
        int cellnum = 0;
        Row row;

        HSSFSheet sheet = hssfWorkbook.createSheet("LISTA");

        // Definindo alguns padroes de layout
        sheet.setDefaultColumnWidth(20);
        sheet.setDefaultRowHeight((short) 300);

        // Configurando Cabecalho
        CellStyle estiloCabecalho = hssfWorkbook.createCellStyle();

        row = sheet.createRow(rownum++);


        for (String coluna : listaNomesColuna) {
            cabecalho(estiloCabecalho, row, cellnum++, coluna);
        }

        for (Object item : listaItens) {
            row = sheet.createRow(rownum++);
            cellnum = 0;

            for (String campo : listaCampos) {
                Object obj;
                try {
                    obj = getField(item, campo);
                } catch (Exception e) {
                    obj = "";
                }
                criarCelula(cellnum++, row, obj);
            }
        }

        String diretorioBase;
        try {
            diretorioBase = Uteis.obterCaminhoWeb() + File.separator + "relatorio" + File.separator;
            File pDestino = new File(diretorioBase);
            if (!pDestino.exists()) {
                FileUtilities.forceDirectory(diretorioBase);
            }
        } catch (Exception e) {
            File f = new File(PropsService.getPropertyValue(PropsService.diretorioArquivos));
            if(!f.exists()) {
                f.getParentFile().mkdirs();
            }
            diretorioBase = f.getAbsolutePath();
        }

        if (UteisValidacao.emptyString(nomeArquivo)) {
            nomeArquivo = "ExportaExcel";
        }

        File file = new File(diretorioBase + File.separator + nomeArquivo + "-" + Calendario.hoje().getTime() + ".xls");

        FileOutputStream out = new FileOutputStream(file);
        hssfWorkbook.write(out);
        out.close();
        hssfWorkbook.close();
        return file;
    }

    public File gerarArquivoExcel(String nomeArquivo, JSONArray array, String key, String colunas) throws IOException {

        Uteis.logar(null, "Gerar arquivo excel");

        HSSFWorkbook hssfWorkbook = new HSSFWorkbook();

        int rownum = 0;
        int cellnum = 0;
        Row row;

        HSSFSheet sheet = hssfWorkbook.createSheet("LISTA");

        sheet.setDefaultColumnWidth(20);
        sheet.setDefaultRowHeight((short) 300);

        CellStyle estiloCabecalho = hssfWorkbook.createCellStyle();

        row = sheet.createRow(rownum++);

        JSONObject jsonBase = new JSONObject();
        for (int e = 0; e < array.length(); e++) {
            JSONObject json1 = array.getJSONObject(e);
            if (JSONObject.getNames(jsonBase) == null ||
                    JSONObject.getNames(json1).length > JSONObject.getNames(jsonBase).length) {
                jsonBase = json1;
            }
        }

        for (String coluna : colunas.split(",")) {
            cabecalho(estiloCabecalho, row, cellnum++, coluna);
        }

        for (int e = 0; e < array.length(); e++) {
            JSONObject json = array.getJSONObject(e);

            row = sheet.createRow(rownum++);
            cellnum = 0;

            for (String keys : colunas.split(",")) {
                criarCelula(cellnum++, row, json.opt(keys));
            }
        }

        String diretorioBase = Uteis.obterCaminhoWeb() + File.separator + "relatorio" + File.separator;
        File pDestino = new File(diretorioBase);
        if (!pDestino.exists()) {
            FileUtilities.forceDirectory(diretorioBase);
        }

        if (UteisValidacao.emptyString(nomeArquivo)) {
            nomeArquivo = "ExportaExcel";
        }

        File file = new File(diretorioBase + File.separator + nomeArquivo+ "-" + key + "-" + Calendario.hoje().getTime() + ".xls");

        FileOutputStream out = new FileOutputStream(file);
        hssfWorkbook.write(out);
        out.close();
        hssfWorkbook.close();
        return file;
    }

    private void criarCelula(int numeroCelula, Row row, Object valor) {
        Cell cell = row.createCell(numeroCelula);
        setValorCelula(cell, valor);
    }

    private void setValorCelula(Cell cell, Object valor) {
        if (valor instanceof String) {
            cell.setCellValue((String) valor);
        } else if (valor instanceof Number) {
            cell.setCellValue(((Number) valor).doubleValue());
        } else if (valor instanceof Date) {
            cell.setCellValue(Uteis.getData((Date) valor, "br"));
        } else if (valor instanceof Boolean) {
            cell.setCellValue((Boolean) valor);
        } else {
            cell.setCellValue(valor == null ? "" : valor.toString());
        }
    }

    private Cell cabecalho(CellStyle estilo, Row row, int cellnum, String textoCelula) {
        estilo.setFillForegroundColor(IndexedColors.GREY_25_PERCENT.getIndex());
        estilo.setFillPattern(FillPatternType.SOLID_FOREGROUND);
        estilo.setAlignment(HorizontalAlignment.CENTER);
        estilo.setVerticalAlignment(VerticalAlignment.CENTER);

        Cell cell = row.createCell(cellnum++);
        cell.setCellStyle(estilo);
        cell.setCellValue(textoCelula);
        return cell;
    }

    private Object getField(Object entidade, String fieldMain) throws Exception {
        String fieldSub = null;
        if(fieldMain.indexOf(".") > 0) {
            fieldSub = fieldMain.substring(fieldMain.indexOf(".") + 1);
            fieldMain = fieldMain.substring(0, fieldMain.indexOf("."));
        }


        String methodMain = "get" + Uteis.firstLetterUpper(fieldMain.trim());
        Object obj = UtilReflection.invoke(entidade, methodMain);

        if (fieldSub != null) {
            obj = getField(obj, fieldSub);
        }

        return obj;
    }
}

package importador.enumerador;

import javax.faces.model.SelectItem;
import java.util.ArrayList;
import java.util.List;

/**
 * Created by <PERSON><PERSON> on 20/12/2019.
 */
public enum ImportacaoParcelasSituacaoEnum {

    TODAS_EM_ABERTO(1, "TODAS AS PARCELAS EM ABERTO"),
    TODAS_PAGAS(2, "TODAS PARCELAS PAGAS"),
    FUTURAS_EM_ABERTO(3, "SOMENTE PARCELAS FUTURAS EM ABERTO"),
    ;

    private Integer codigo;
    private String descricao;

    ImportacaoParcelasSituacaoEnum(Integer codigo, String descricao) {
        this.codigo = codigo;
        this.descricao = descricao;
    }

    public static ImportacaoParcelasSituacaoEnum obterPorCodigo(Integer codigo) {
        for (ImportacaoParcelasSituacaoEnum f : ImportacaoParcelasSituacaoEnum.values()) {
            if (f.getCodigo().equals(codigo)) {
                return f;
            }
        }
        return null;
    }

    public static List<SelectItem> obterListaSelectItem(boolean objeto, boolean adicionarVazio) {
        List<SelectItem> lista = new ArrayList<>();
        for (ImportacaoParcelasSituacaoEnum masc : ImportacaoParcelasSituacaoEnum.values()) {
            if (objeto) {
                lista.add(new SelectItem(masc, masc.getDescricao()));
            } else {
                lista.add(new SelectItem(masc.getCodigo(), masc.getDescricao()));
            }
        }

        if (adicionarVazio) {
            lista.add(new SelectItem(0, ""));
        }
        return lista;
    }

    public Integer getCodigo() {
        return codigo;
    }

    public String getDescricao() {
        return descricao;
    }
}

package importador;

import br.com.pactosolucoes.oamd.controle.basico.DAO;
import controle.arquitetura.threads.ThreadImportacao;
import importador.enumerador.ImportacaoParcelasSituacaoEnum;
import importador.json.ClienteImportacaoJSON;
import importador.json.ContratoImportacaoJSON;
import importador.json.FamiliarImportacaoJSON;
import importador.json.FornecedorImportacaoJSON;
import importador.json.PagamentoImportacaoJSON;
import importador.json.ParcelaPagamentoJSON;
import importador.json.TurmaImportacaoJSON;
import negocio.comuns.arquitetura.UsuarioVO;
import negocio.comuns.basico.*;
import negocio.comuns.basico.enumerador.MascaraDataEnum;
import negocio.comuns.basico.enumerador.TipoImportacaoEnum;
import negocio.comuns.utilitarias.Uteis;
import negocio.facade.jdbc.arquitetura.Usuario;
import negocio.facade.jdbc.basico.Cliente;
import negocio.facade.jdbc.basico.Empresa;
import negocio.facade.jdbc.basico.Familiar;
import negocio.facade.jdbc.basico.Parentesco;
import negocio.facade.jdbc.utilitarias.Conexao;
import org.apache.poi.xssf.usermodel.XSSFRow;

import java.sql.Connection;
import java.util.ArrayList;
import java.util.List;

public class ImportacaoStart {

    private static boolean executarTurmas = true;
    private static boolean executarClientes = true;
    private static boolean executarContratos = true;
    private static boolean executarPagamentosParcelas = true;
    private static boolean executarFamiliares = true;
    private static boolean executarFornecedor = true;

    public static void main(String[] args) throws Exception {
        String chave = "sesipara";
        Integer codigoEmpresa = 1;
        Integer codigoModalidade = 1;
        Integer codigoPlano = 1;
        Integer codigoHorario = 1;

        String caminhoPlanilhaCadastroTurmas = "C:\\pacto\\backups\\sesi_pa_importacao\\20241220\\turmas.xlsx";
        String caminhoPlanilhaClientes = "C:\\pacto\\backups\\sesi_pa_importacao\\20241220\\clientes.xlsx";
        String caminhoPlanilhaContratos = "C:\\pacto\\backups\\sesi_pa_importacao\\20241220\\contratos.xlsx";
        String caminhoPlanilhaVinculosFamiliares = "C:\\pacto\\backups\\sesi_pa_importacao\\20241220\\familiares.xlsx";
        String caminhoPlanilhaPagamentos = "C:\\pacto\\backups\\sesi_pa_importacao\\20241220\\pagamentos.xlsx";
        String caminhoFornecedores = "C:\\pacto\\backups\\sesi_pa_importacao\\20241220\\fornecedores.xlsx";

        DAO dao = new DAO();
        Connection con = dao.obterConexaoEspecifica(chave);
        Conexao.guardarConexaoForJ2SE(con);
        dao = null;

        Empresa empresaDAO = new Empresa(con);
        EmpresaVO empresaVO = empresaDAO.consultarPorCodigo(codigoEmpresa, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
        empresaDAO = null;

        Usuario usuarioDAO = new Usuario(con);
        UsuarioVO usuarioVO = usuarioDAO.consultarPorUsername("pactobr", Uteis.NIVELMONTARDADOS_DADOSBASICOS);
        usuarioDAO = null;

        ConfigExcelImportacaoTO configExcelImportacaoTO = new ConfigExcelImportacaoTO();
        configExcelImportacaoTO.setEmpresaVO(empresaVO);
        configExcelImportacaoTO.setMascaraDataEnum(MascaraDataEnum.DATA_DDMMYYYY_1.getCodigo());
        configExcelImportacaoTO.setModalidade(codigoModalidade);
        configExcelImportacaoTO.setHorario(codigoHorario);
        configExcelImportacaoTO.setPlano(codigoPlano);
        configExcelImportacaoTO.setChave(chave);
        configExcelImportacaoTO.setDiasCarencia(30);
        configExcelImportacaoTO.setImportacaoParcelasSituacaoEnum(ImportacaoParcelasSituacaoEnum.TODAS_EM_ABERTO);
        configExcelImportacaoTO.setUsuarioResponsavelImportacao(usuarioVO);

        UteisImportacaoExcel uteisImportacaoExcel = new UteisImportacaoExcel();
        if (executarTurmas) {
            System.out.println("Executando importação de Turmas...");
            List<XSSFRow> linhasTurmas = LeitorExcel2010.lerLinhas(caminhoPlanilhaCadastroTurmas);
            uteisImportacaoExcel.obterListaTurmaJSONExcel(linhasTurmas, empresaVO.getCodigo());
            processarTurmas(chave, usuarioVO, uteisImportacaoExcel.getListaTurma(), configExcelImportacaoTO, con);
        }

        if (executarClientes) {
            System.out.println("Executando importação de Clientes...");
            List<XSSFRow> linhasClientes = LeitorExcel2010.lerLinhas(caminhoPlanilhaClientes);
            uteisImportacaoExcel.obterListaClienteJSONExcel(linhasClientes, configExcelImportacaoTO);
            processarClientes(chave, usuarioVO, uteisImportacaoExcel.getListaClientes(), configExcelImportacaoTO, con);
        }

        if (executarContratos) {
            System.out.println("Executando importação de Contratos...");
            List<XSSFRow> linhasContratos = LeitorExcel2010.lerLinhas(caminhoPlanilhaContratos);
            uteisImportacaoExcel.obterListaContratoJSONExcel(linhasContratos, configExcelImportacaoTO);
            processarContratos(chave, usuarioVO, uteisImportacaoExcel.getListaContratos(), configExcelImportacaoTO, con);
        }

        if (executarPagamentosParcelas) {
            System.out.println("Executando importação de Pagamentos parcelas...");
            List<XSSFRow> linhas = LeitorExcel2010.lerLinhas(caminhoPlanilhaPagamentos);
            uteisImportacaoExcel.obterListaParcelasPagamentosJSONExcel(linhas, configExcelImportacaoTO);
            processarPagamentosParcelas(chave, usuarioVO, uteisImportacaoExcel.getListaParcelasPagamentos(), configExcelImportacaoTO, con);
        }

        if (executarFamiliares) {
            System.out.println("Executando importação de Familiares...");
            List<XSSFRow> linhasFamiliares = LeitorExcel2010.lerLinhas(caminhoPlanilhaVinculosFamiliares);
            uteisImportacaoExcel.obterListaVinculosFamiliaresJSONExcel(linhasFamiliares, configExcelImportacaoTO);
            processarVinculoFamiliaresContratoImportacao(chave, usuarioVO, uteisImportacaoExcel.getListaFamiliares(), configExcelImportacaoTO, con);
        }

        if (executarFornecedor) {
            System.out.println("Executando importação de Fornecedores...");
            List<XSSFRow> linhasFornecedores = LeitorExcel2010.lerLinhas(caminhoFornecedores);
            List<FornecedorImportacaoJSON> listaFornecedores = uteisImportacaoExcel.obterListaFornecedorExcel(linhasFornecedores, configExcelImportacaoTO);
            processarFornecedores(chave, usuarioVO, listaFornecedores, configExcelImportacaoTO, con);
        }

        System.out.println("Processos finalizados!");

    }

    private static void processarContratos(String chave, UsuarioVO usuarioVO, List<ContratoImportacaoJSON> listaContratos, ConfigExcelImportacaoTO configExcelImportacaoTO, Connection con) throws Exception {
        ImportacaoConfigTO configTO = new ImportacaoConfigTO();
        configTO.setChave(chave);
        configTO.setListaEmails(new ArrayList<>());
        configTO.setUsuarioResponsavelImportacao(usuarioVO.getCodigo());
        configTO.setTipoImportacaoEnum(TipoImportacaoEnum.CONTRATO);

        ThreadImportacao thread = new ThreadImportacao(configTO, configExcelImportacaoTO, con, null, listaContratos, null, null, null, null, null, null, null, null, null, null, null);
        thread.setDaemon(true);
        thread.start();
        thread.join();
    }

    private static void processarPagamentosParcelas(String chave, UsuarioVO usuarioVO, List<ParcelaPagamentoJSON> lista, ConfigExcelImportacaoTO configExcelImportacaoTO, Connection con) throws Exception {
        ImportacaoConfigTO configTO = new ImportacaoConfigTO();
        configTO.setChave(chave);
        configTO.setListaEmails(new ArrayList<>());
        configTO.setUsuarioResponsavelImportacao(usuarioVO.getCodigo());
        configTO.setTipoImportacaoEnum(TipoImportacaoEnum.PARCELAS_PAGAMENTOS);

        ThreadImportacao thread = new ThreadImportacao(configTO, configExcelImportacaoTO, con, null, null, null, null, null, null, null, null, lista, null, null, null, null);
        thread.setDaemon(true);
        thread.start();
        thread.join();
    }

    private static void processarClientes(String chave, UsuarioVO usuarioVO, List<ClienteImportacaoJSON> listaClientes, ConfigExcelImportacaoTO configExcelImportacaoTO, Connection con) throws Exception {
        ImportacaoConfigTO configTO = new ImportacaoConfigTO();
        configTO.setChave(chave);
        configTO.setListaEmails(new ArrayList<>());
        configTO.setUsuarioResponsavelImportacao(usuarioVO.getCodigo());
        configTO.setTipoImportacaoEnum(TipoImportacaoEnum.CLIENTE);

        ThreadImportacao thread = new ThreadImportacao(configTO, configExcelImportacaoTO, con, listaClientes, null, null, null, null, null, null, null, null, null, null, null, null);
        thread.setDaemon(true);
        thread.start();
        thread.join();
    }

    private static void processarTurmas(String chave,
                                        UsuarioVO usuarioVO,
                                        List<TurmaImportacaoJSON> listaTurmaJson,
                                        ConfigExcelImportacaoTO configExcelImportacaoTO,
                                        Connection con) throws Exception {
        ImportacaoConfigTO configTO = new ImportacaoConfigTO();
        configTO.setChave(chave);
        configTO.setListaEmails(new ArrayList<>());
        configTO.setUsuarioResponsavelImportacao(usuarioVO.getCodigo());
        configTO.setTipoImportacaoEnum(TipoImportacaoEnum.TURMAS);

        ThreadImportacao thread = new ThreadImportacao(configTO, configExcelImportacaoTO, con, null, null, null, null, null, null, null, listaTurmaJson, null, null, null, null, null);
        thread.setDaemon(true);
        thread.start();
        thread.join();
    }

    private static void processarVinculoFamiliaresContratoImportacao(String chave, UsuarioVO usuarioVO, List<FamiliarImportacaoJSON> listaFamiliarImportacaoJson, ConfigExcelImportacaoTO configExcelImportacaoTO,
                                                                     Connection con) throws Exception {
        System.out.println("==============[ INICIANDO PROCESSO: processarVinculoFamiliaresContratoImportacao ]==============");

        Familiar familiarDao = new Familiar(con);
        Cliente clienteDao = new Cliente(con);
        Parentesco parentescoDao = new Parentesco(con);
        Integer qtdVinculosFamiliaresCriados = 0;
        for(FamiliarImportacaoJSON familiarImportacaoJSON : listaFamiliarImportacaoJson) {
            FamiliarVO familiarVO = new FamiliarVO();
            ClienteVO clienteAluno = clienteDao.consultarPorCodigoMatriculaExterna(
                    Integer.valueOf(familiarImportacaoJSON.getMatriculaExternaAluno()), Uteis.NIVELMONTARDADOS_DADOSBASICOS
            );
            if(clienteAluno == null || clienteAluno.getCodigo().intValue() == 0) {
                throw new Exception("Nao foi encontrado aluno com matricula externa "
                        + familiarImportacaoJSON.getMatriculaExternaAluno());
            }
            ClienteVO clienteFamiliar = clienteDao.consultarPorCodigoMatriculaExterna(
                    Integer.valueOf(familiarImportacaoJSON.getMatriculaExternaFamiliar()), Uteis.NIVELMONTARDADOS_DADOSBASICOS);
            if(clienteFamiliar == null || clienteAluno.getCodigo().intValue() == 0) {
                throw new Exception("Nao foi encontrado familiar com matricula externa "
                        + familiarImportacaoJSON.getMatriculaExternaFamiliar());
            }
            familiarVO.setNovoObj(true);
            familiarVO.setNome(clienteFamiliar.getPessoa().getNome());
            familiarVO.setCliente(clienteAluno.getCodigo());
            familiarVO.setFamiliar(clienteFamiliar.getCodigo());
            familiarVO.setCodAcesso(clienteAluno.getCodAcesso());
            ParentescoVO parentescoVO = parentescoDao.obterParentescoCriandoSeNaoExiste(familiarImportacaoJSON.getParentesco());
            familiarVO.setParentesco(parentescoVO);
            familiarDao.incluir(familiarVO, true);
            System.out.println(qtdVinculosFamiliaresCriados + 1 + "/" + listaFamiliarImportacaoJson.size() + " - Criado vínculo familiar para CLIENTE: " + clienteAluno.getPessoa().getNome() + " (Cod.: " + clienteAluno.getCodigo() + ") FAMILIAR: " + clienteFamiliar.getPessoa().getNome() + " (Cod.: " + clienteFamiliar.getCodigo() + ") PARENTESCO: " + parentescoVO.getDescricao());
            qtdVinculosFamiliaresCriados++;

        }
        System.out.println("processarVinculoFamiliaresContratoImportacao finalizado: " + qtdVinculosFamiliaresCriados + " familiares importados com êxito");
    }

    private static void processarFornecedores(String chave,
                                        UsuarioVO usuarioVO,
                                        List<FornecedorImportacaoJSON> listaJson,
                                        ConfigExcelImportacaoTO configExcelImportacaoTO,
                                        Connection con) throws Exception {
        ImportacaoConfigTO configTO = new ImportacaoConfigTO();
        configTO.setChave(chave);
        configTO.setListaEmails(new ArrayList<>());
        configTO.setUsuarioResponsavelImportacao(usuarioVO.getCodigo());
        configTO.setTipoImportacaoEnum(TipoImportacaoEnum.FORNECEDOR);

        ThreadImportacao thread = new ThreadImportacao(configTO, configExcelImportacaoTO, con, null, null, null, null, listaJson, null, null, null, null, null, null, null, null);
        thread.setDaemon(true);
        thread.start();
        thread.join();
    }
}

package importador.json;

import br.com.pactosolucoes.comuns.json.SuperJSON;
import negocio.comuns.utilitarias.Uteis;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * Created by <PERSON><PERSON> on 14/01/2020.
 */
public class FornecedorImportacaoJSON extends SuperJSON {

    private Integer idExterno;
    private String nome, cpf_cnpj, nomeContato, grauInstrucao, nacionalidade;
    private String naturalidade, cidade, uf, observacao;
    private String incricaoEstadual, incricaoMunicipal;
    private List<TelefoneImportacaoJSON> telefones;
    private List<EmailImportacaoJSON> emails;
    private List<EnderecoImportacaoJSON> enderecos;
    private Integer empresa;
    private Integer codigo;
    private boolean sucesso = false;
    private String msgRetorno;
    private Date dataDoCadastro, dataDeValidade;
    private String razaoSocial, sindicato, codFpas, cnae;
    private Integer numeroTotalDeFuncionarios, grauDeRiscoNr4, grauDeRiscoInss, porteDaEmpresa;

    public String getNome() {
        if (nome == null) {
            nome = "";
        }
        return nome.toUpperCase();
    }

    public void setNome(String nome) {
        this.nome = nome;
    }

    public String getCpf_cnpj_Formatado() {
        return Uteis.formatarCpfCnpj(getCpf_cnpj(), false);
    }

    public String getCpf_cnpj_SomenteNumeros() {
        return Uteis.formatarCpfCnpj(getCpf_cnpj(), true);
    }

    public String getGrauInstrucao() {
        if (grauInstrucao == null) {
            grauInstrucao = "";
        }
        return grauInstrucao;
    }

    public void setGrauInstrucao(String grauInstrucao) {
        this.grauInstrucao = grauInstrucao;
    }

    public String getNacionalidade() {
        if (nacionalidade == null) {
            nacionalidade = "";
        }
        return nacionalidade;
    }

    public Date getDataDoCadastro() {
        return dataDoCadastro;
    }

    public void setDataDoCadastro(Date dataDoCadastro) {
        this.dataDoCadastro = dataDoCadastro;
    }

    public Date getDataDeValidade() {
        return dataDeValidade;
    }

    public void setDataDeValidade(Date dataDeValidade) {
        this.dataDeValidade = dataDeValidade;
    }

    public String getRazaoSocial() {
        return razaoSocial;
    }

    public void setRazaoSocial(String razaoSocial) {
        this.razaoSocial = razaoSocial;
    }

    public Integer getGrauDeRiscoNr4() {
        return grauDeRiscoNr4;
    }

    public void setGrauDeRiscoNr4(Integer grauDeRiscoNr4) {
        this.grauDeRiscoNr4 = grauDeRiscoNr4;
    }

    public Integer getGrauDeRiscoInss() {
        return grauDeRiscoInss;
    }

    public void setGrauDeRiscoInss(Integer grauDeRiscoInss) {
        this.grauDeRiscoInss = grauDeRiscoInss;
    }

    public String getSindicato() {
        return sindicato;
    }

    public void setSindicato(String sindicato) {
        this.sindicato = sindicato;
    }

    public Integer getPorteDaEmpresa() {
        return porteDaEmpresa;
    }

    public void setPorteDaEmpresa(Integer porteDaEmpresa) {
        this.porteDaEmpresa = porteDaEmpresa;
    }

    public String getCnae() {
        return cnae;
    }

    public void setCnae(String cnae) {
        this.cnae = cnae;
    }

    public Integer getNumeroTotalDeFuncionarios() {
        return numeroTotalDeFuncionarios;
    }

    public String getCodFpas() {
        return codFpas;
    }

    public void setCodFpas(String codFpas) {
        this.codFpas = codFpas;
    }

    public void setNumeroTotalDeFuncionarios(Integer numeroTotalDeFuncionarios) {
        this.numeroTotalDeFuncionarios = numeroTotalDeFuncionarios;
    }

    public void setNacionalidade(String nacionalidade) {
        this.nacionalidade = nacionalidade;
    }

    public String getNaturalidade() {
        if (naturalidade == null) {
            naturalidade = "";
        }
        return naturalidade;
    }

    public void setNaturalidade(String naturalidade) {
        this.naturalidade = naturalidade;
    }

    public String getCidade() {
        if (cidade == null) {
            cidade = "";
        }
        return cidade;
    }

    public void setCidade(String cidade) {
        this.cidade = cidade;
    }

    public String getUf() {
        if (uf == null) {
            uf = "";
        }
        return uf;
    }

    public void setUf(String uf) {
        this.uf = uf;
    }

    public Integer getIdExterno() {
        return idExterno;
    }

    public void setIdExterno(Integer idExterno) {
        this.idExterno = idExterno;
    }

    public String getObservacao() {
        if (observacao == null) {
            observacao = "";
        }
        return observacao;
    }

    public void setObservacao(String observacao) {
        this.observacao = observacao;
    }

    public List<TelefoneImportacaoJSON> getTelefones() {
        if (telefones == null) {
            telefones = new ArrayList<>();
        }
        return telefones;
    }

    public void setTelefones(List<TelefoneImportacaoJSON> telefones) {
        this.telefones = telefones;
    }

    public List<EmailImportacaoJSON> getEmails() {
        if (emails == null) {
            emails = new ArrayList<>();
        }
        return emails;
    }

    public void setEmails(List<EmailImportacaoJSON> emails) {
        this.emails = emails;
    }

    public List<EnderecoImportacaoJSON> getEnderecos() {
        if (enderecos == null) {
            enderecos = new ArrayList<>();
        }
        return enderecos;
    }

    public void setEnderecos(List<EnderecoImportacaoJSON> enderecos) {
        this.enderecos = enderecos;
    }

    public boolean isSucesso() {
        return sucesso;
    }

    public void setSucesso(boolean sucesso) {
        this.sucesso = sucesso;
    }

    public String getMsgRetorno() {
        if (msgRetorno == null) {
            msgRetorno = "";
        }
        return msgRetorno;
    }

    public void setMsgRetorno(String msgRetorno) {
        this.msgRetorno = msgRetorno;
    }

    public Integer getCodigo() {
        if (codigo == null) {
            codigo = 0;
        }
        return codigo;
    }

    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public Integer getEmpresa() {
        if (empresa == null) {
            empresa = 0;
        }
        return empresa;
    }

    public void setEmpresa(Integer empresa) {
        this.empresa = empresa;
    }

    public String getCpf_cnpj() {
        if (cpf_cnpj == null) {
            cpf_cnpj = "";
        }
        return cpf_cnpj;
    }

    public void setCpf_cnpj(String cpf_cnpj) {
        this.cpf_cnpj = cpf_cnpj;
    }

    public String getNomeContato() {
        if (nomeContato == null) {
            nomeContato = "";
        }
        return nomeContato;
    }

    public void setNomeContato(String nomeContato) {
        this.nomeContato = nomeContato;
    }

    public String getIncricaoEstadual() {
        return incricaoEstadual;
    }

    public void setIncricaoEstadual(String incricaoEstadual) {
        this.incricaoEstadual = incricaoEstadual;
    }

    public String getIncricaoMunicipal() {
        return incricaoMunicipal;
    }

    public void setIncricaoMunicipal(String incricaoMunicipal) {
        this.incricaoMunicipal = incricaoMunicipal;
    }

}

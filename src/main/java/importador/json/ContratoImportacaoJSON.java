package importador.json;

import br.com.pactosolucoes.ce.comuns.enumerador.DiaSemana;
import br.com.pactosolucoes.comuns.json.SuperJSON;
import importador.enumerador.ImportacaoParcelasSituacaoEnum;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * Created by <PERSON><PERSON> on 20/12/2019.
 */
public class ContratoImportacaoJSON extends SuperJSON {

    private Integer idExterno;
    private Long idExternoCliente;
    private Long idExternoTurma;
    private String horarioTurma;
    private List<DiaSemana> diasTurma;
    private Date dataLancamento;
    private Date dataInicio;
    private Date dataFim;
    private Double valorMensal;
    private Double valorTotal;
    private Double valorMatricula;
    private Double valorAnuidade;
    private Integer consultor;
    private Integer plano;
    private String descricaoPlano;
    private Integer diasCarencia;
    private Integer diasCancelamentoAutomatico;
    private Integer empresa;
    private Integer usuario;
    private Integer modalidade;
    private Integer duracao;
    private Integer horario;
    private String observacao;
    private boolean recorrencia = false;
    private boolean contratoEmAberto = false;
    private Integer nrParcelas;
    private Integer importacaoParcelasSituacao;

    private Integer codigo;
    private boolean sucesso = false;
    private String msgRetorno;
    private List<ParcelaImportacaoJSON> parcelas;
    private String nome;


    public Date getDataLancamento() {
        return dataLancamento;
    }

    public void setDataLancamento(Date dataLancamento) {
        this.dataLancamento = dataLancamento;
    }

    public Date getDataInicio() {
        return dataInicio;
    }

    public void setDataInicio(Date dataInicio) {
        this.dataInicio = dataInicio;
    }

    public Date getDataFim() {
        return dataFim;
    }

    public void setDataFim(Date dataFim) {
        this.dataFim = dataFim;
    }

    public Double getValorTotal() {
        return valorTotal;
    }

    public void setValorTotal(Double valorTotal) {
        this.valorTotal = valorTotal;
    }

    public Integer getPlano() {
        return plano;
    }

    public void setPlano(Integer plano) {
        this.plano = plano;
    }

    public Integer getUsuario() {
        return usuario;
    }

    public void setUsuario(Integer usuario) {
        this.usuario = usuario;
    }

    public Integer getModalidade() {
        return modalidade;
    }

    public void setModalidade(Integer modalidade) {
        this.modalidade = modalidade;
    }

    public Integer getDuracao() {
        return duracao;
    }

    public void setDuracao(Integer duracao) {
        this.duracao = duracao;
    }

    public boolean isRecorrencia() {
        return recorrencia;
    }

    public void setRecorrencia(boolean recorrencia) {
        this.recorrencia = recorrencia;
    }

    public String getDescricaoPlano() {
        return descricaoPlano;
    }

    public void setDescricaoPlano(String descricaoPlano) {
        this.descricaoPlano = descricaoPlano;
    }

    public boolean isSucesso() {
        return sucesso;
    }

    public void setSucesso(boolean sucesso) {
        this.sucesso = sucesso;
    }

    public String getMsgRetorno() {
        if (msgRetorno == null) {
            msgRetorno = "";
        }
        return msgRetorno;
    }

    public void setMsgRetorno(String msgRetorno) {
        this.msgRetorno = msgRetorno;
    }

    public Integer getCodigo() {
        if (codigo == null) {
            codigo = 0;
        }
        return codigo;
    }

    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public Integer getConsultor() {
        if (consultor == null) {
            consultor = 0;
        }
        return consultor;
    }

    public void setConsultor(Integer consultor) {
        this.consultor = consultor;
    }


    public Integer getDiasCarencia() {
        if (diasCarencia == null) {
            diasCarencia = 0;
        }
        return diasCarencia;
    }

    public void setDiasCarencia(Integer diasCarencia) {
        this.diasCarencia = diasCarencia;
    }

    public Integer getHorario() {
        if (horario == null) {
            horario = 0;
        }
        return horario;
    }

    public void setHorario(Integer horario) {
        this.horario = horario;
    }

    public Integer getNrParcelas() {
        if (nrParcelas == null) {
            nrParcelas = 1;
        }
        return nrParcelas;
    }

    public void setNrParcelas(Integer nrParcelas) {
        this.nrParcelas = nrParcelas;
    }

    public Integer getEmpresa() {
        return empresa;
    }

    public void setEmpresa(Integer empresa) {
        this.empresa = empresa;
    }

    public String getObservacao() {
        if (observacao == null) {
            observacao = "";
        }
        return observacao;
    }

    public void setObservacao(String observacao) {
        this.observacao = observacao;
    }

    public Double getValorMatricula() {
        if (valorMatricula == null) {
            valorMatricula = 0.0;
        }
        return valorMatricula;
    }

    public void setValorMatricula(Double valorMatricula) {
        this.valorMatricula = valorMatricula;
    }

    public List<ParcelaImportacaoJSON> getParcelas() {
        if (parcelas == null) {
            parcelas = new ArrayList<>();
        }
        return parcelas;
    }

    public void setParcelas(List<ParcelaImportacaoJSON> parcelas) {
        this.parcelas = parcelas;
    }

    public Double getValorMensal() {
        if (valorMensal == null) {
            valorMensal = 0.0;
        }
        return valorMensal;
    }

    public void setValorMensal(Double valorMensal) {
        this.valorMensal = valorMensal;
    }

    public Double getValorAnuidade() {
        if (valorAnuidade == null) {
            valorAnuidade = 0.0;
        }
        return valorAnuidade;
    }

    public void setValorAnuidade(Double valorAnuidade) {
        this.valorAnuidade = valorAnuidade;
    }

    public Integer getDiasCancelamentoAutomatico() {
        if (diasCancelamentoAutomatico == null) {
            diasCancelamentoAutomatico = 0;
        }
        return diasCancelamentoAutomatico;
    }

    public void setDiasCancelamentoAutomatico(Integer diasCancelamentoAutomatico) {
        this.diasCancelamentoAutomatico = diasCancelamentoAutomatico;
    }

    public boolean isContratoEmAberto() {
        return contratoEmAberto;
    }

    public void setContratoEmAberto(boolean contratoEmAberto) {
        this.contratoEmAberto = contratoEmAberto;
    }

    public Integer getIdExterno() {
        return idExterno;
    }

    public void setIdExterno(Integer idExterno) {
        this.idExterno = idExterno;
    }

    public Long getIdExternoCliente() {
        return idExternoCliente;
    }

    public void setIdExternoCliente(Long idExternoCliente) {
        this.idExternoCliente = idExternoCliente;
    }

    public String getNome() {
        if (nome == null) {
            nome = "";
        }
        return nome;
    }

    public void setNome(String nome) {
        this.nome = nome;
    }

    public Integer getImportacaoParcelasSituacao() {
        if (importacaoParcelasSituacao == null) {
            importacaoParcelasSituacao = ImportacaoParcelasSituacaoEnum.TODAS_PAGAS.getCodigo();
        }
        return importacaoParcelasSituacao;
    }

    public void setImportacaoParcelasSituacao(Integer importacaoParcelasSituacao) {
        this.importacaoParcelasSituacao = importacaoParcelasSituacao;
    }

    public Long getIdExternoTurma() {
        return idExternoTurma;
    }

    public void setIdExternoTurma(Long idExternoTurma) {
        this.idExternoTurma = idExternoTurma;
    }

    public String getHorarioTurma() {
        return horarioTurma;
    }

    public void setHorarioTurma(String horarioTurma) {
        this.horarioTurma = horarioTurma;
    }

    public List<DiaSemana> getDiasTurma() {
        if (diasTurma == null) {
            diasTurma = new ArrayList<>();
        }
        return diasTurma;
    }

    public void setDiasTurma(List<DiaSemana> diasTurma) {
        this.diasTurma = diasTurma;
    }
}

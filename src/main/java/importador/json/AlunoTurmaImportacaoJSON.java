package importador.json;

import br.com.pactosolucoes.comuns.json.SuperJSON;
import negocio.comuns.utilitarias.UteisValidacao;

import java.util.ArrayList;
import java.util.List;

/**
 * Created by <PERSON><PERSON> on 20/12/2019.
 */
public class AlunoTurmaImportacaoJSON extends SuperJSON {

    private Long codigoDeContrato;
    private Long codigoDaModalidade;

    private List<Integer> horarios;

    private boolean sucesso = false;
    private String msgRetorno;
    private Integer empresa;
    private Integer idExterno;

    public boolean isSucesso() {
        return sucesso;
    }

    public void setSucesso(boolean sucesso) {
        this.sucesso = sucesso;
    }

    public String getMsgRetorno() {
        if (msgRetorno == null) {
            msgRetorno = "";
        }
        return msgRetorno;
    }

    public void setMsgRetorno(String msgRetorno) {
        this.msgRetorno = msgRetorno;
    }


    public void setCodigoDeContrato(Long codigoDeContrato) {
        this.codigoDeContrato = codigoDeContrato;
    }

    public Long getCodigoDaModalidade() {
        return codigoDaModalidade;
    }

    public void setCodigoDaModalidade(Long codigoDaModalidade) {
        this.codigoDaModalidade = codigoDaModalidade;
    }

    public Long getCodigoDeContrato() {
        return codigoDeContrato;
    }

    public List<Integer> getHorarios() {
        if (horarios == null) {
            horarios = new ArrayList<>();
        }
        return horarios;
    }

    public void setHorarios(List<Integer> horarios) {
        this.horarios = horarios;
    }

    public Integer getEmpresa() {
        return empresa;
    }

    public void setEmpresa(Integer empresa) {
        this.empresa = empresa;
    }

    public Integer getIdExterno() {
        return idExterno;
    }

    public void setIdExterno(Integer idExterno) {
        this.idExterno = idExterno;
    }

    public void validarDados() throws Exception {
        if (UteisValidacao.emptyNumber(codigoDeContrato) && UteisValidacao.emptyNumber(idExterno)) {
            throw new Exception("Código\\Idexterno do contrato não informado.");
        }
        if (UteisValidacao.emptyNumber(codigoDaModalidade)) {
            throw new Exception("Código da modalidade não informado.");
        }
        if (UteisValidacao.emptyList(horarios)) {
            throw new Exception("Horários não informados.");
        }
        if (UteisValidacao.emptyNumber(empresa)) {
            throw new Exception("Empresa não informada.");
        }
    }
}

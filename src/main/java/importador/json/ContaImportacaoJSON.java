package importador.json;

import br.com.pactosolucoes.comuns.json.SuperJSON;
import negocio.comuns.utilitarias.Uteis;

import java.util.Date;

/**
 * Created by <PERSON><PERSON> on 14/02/2020.
 */
public class ContaImportacaoJSON extends SuperJSON {

    private Integer idExterno;
    private String descricao;
    private String pessoa;
    private String cpf_cnpj;
    private Double valor;
    private Date dataCompetencia;
    private Date dataVencimento;
    private Date dataLancamento;
    private Date dataQuitacao;
    private String observacao;
    private String tipoConta; //E ou S
    private String centroCusto;
    private String planoConta;
    private Integer empresa;
    private Integer codigo;
    private boolean sucesso = false;
    private String msgRetorno;

    public Integer getIdExterno() {
        if (idExterno == null) {
            idExterno = 0;
        }
        return idExterno;
    }

    public void setIdExterno(Integer idExterno) {
        this.idExterno = idExterno;
    }

    public String getDescricao() {
        if (descricao == null) {
            descricao = "";
        }
        return descricao;
    }

    public void setDescricao(String descricao) {
        this.descricao = descricao;
    }

    public Double getValor() {
        if (valor == null) {
            valor = 0.0;
        }
        return valor;
    }

    public void setValor(Double valor) {
        this.valor = valor;
    }

    public String getObservacao() {
        if (observacao == null) {
            observacao = "";
        }
        return observacao;
    }

    public void setObservacao(String observacao) {
        this.observacao = observacao;
    }

    public boolean isSucesso() {
        return sucesso;
    }

    public void setSucesso(boolean sucesso) {
        this.sucesso = sucesso;
    }

    public String getMsgRetorno() {
        if (msgRetorno == null) {
            msgRetorno = "";
        }
        return msgRetorno;
    }

    public void setMsgRetorno(String msgRetorno) {
        this.msgRetorno = msgRetorno;
    }

    public Integer getCodigo() {
        if (codigo == null) {
            codigo = 0;
        }
        return codigo;
    }

    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public Date getDataCompetencia() {
        return dataCompetencia;
    }

    public void setDataCompetencia(Date dataCompetencia) {
        this.dataCompetencia = dataCompetencia;
    }

    public Date getDataVencimento() {
        return dataVencimento;
    }

    public void setDataVencimento(Date dataVencimento) {
        this.dataVencimento = dataVencimento;
    }

    public Date getDataLancamento() {
        return dataLancamento;
    }

    public void setDataLancamento(Date dataLancamento) {
        this.dataLancamento = dataLancamento;
    }

    public Date getDataQuitacao() {
        return dataQuitacao;
    }

    public void setDataQuitacao(Date dataQuitacao) {
        this.dataQuitacao = dataQuitacao;
    }

    public String getTipoConta() {
        if (tipoConta == null) {
            tipoConta = "";
        }
        return tipoConta;
    }

    public void setTipoConta(String tipoConta) {
        this.tipoConta = tipoConta;
    }

    public String getCentroCusto() {
        if (centroCusto == null) {
            centroCusto = "";
        }
        return centroCusto;
    }

    public void setCentroCusto(String centroCusto) {
        this.centroCusto = centroCusto;
    }

    public String getPlanoConta() {
        if (planoConta == null) {
            planoConta = "";
        }
        return planoConta;
    }

    public void setPlanoConta(String planoConta) {
        this.planoConta = planoConta;
    }

    public Integer getEmpresa() {
        return empresa;
    }

    public void setEmpresa(Integer empresa) {
        this.empresa = empresa;
    }

    public String getPessoa() {
        if (pessoa == null) {
            pessoa = "";
        }
        return pessoa;
    }

    public void setPessoa(String pessoa) {
        this.pessoa = pessoa;
    }

    public String getCpf_cnpj() {
        if (cpf_cnpj == null) {
            cpf_cnpj = "";
        }
        return cpf_cnpj;
    }

    public void setCpf_cnpj(String cpf_cnpj) {
        this.cpf_cnpj = cpf_cnpj;
    }

    public String getCpf_CNPJ_Formatado() {
        return Uteis.formatarCpfCnpj(getCpf_cnpj(), false);
    }

    public String getCpf_CNPJ_SomenteNumeros() {
        return Uteis.formatarCpfCnpj(getCpf_cnpj(), true);
    }
}

package importador.json;

public class AtividadeFichaJSON {

    private Integer codigoFicha;
    private Integer codigoAtividade;
    private String nomeAtividade;
    private Integer sequenciaOrdem;
    private String repeticoes;
    private Integer carga;
    private Integer cadencia;
    private String duracao;
    private String velocidade;
    private String distancia;
    private Integer descancoIntervalo;
    private Integer qtdSeries;
    private String obsComplemento;

    public AtividadeFichaJSON() {
    }

    public Integer getCodigoFicha() {
        return codigoFicha;
    }

    public void setCodigoFicha(Integer codigoFicha) {
        this.codigoFicha = codigoFicha;
    }

    public Integer getCodigoAtividade() {
        return codigoAtividade;
    }

    public void setCodigoAtividade(Integer codigoAtividade) {
        this.codigoAtividade = codigoAtividade;
    }

    public String getNomeAtividade() {
        return nomeAtividade;
    }

    public void setNomeAtividade(String nomeAtividade) {
        this.nomeAtividade = nomeAtividade;
    }

    public Integer getSequenciaOrdem() {
        return sequenciaOrdem;
    }

    public void setSequenciaOrdem(Integer sequenciaOrdem) {
        this.sequenciaOrdem = sequenciaOrdem;
    }

    public String getRepeticoes() {
        return repeticoes;
    }

    public void setRepeticoes(String repeticoes) {
        this.repeticoes = repeticoes;
    }

    public Integer getCarga() {
        return carga;
    }

    public void setCarga(Integer carga) {
        this.carga = carga;
    }

    public Integer getCadencia() {
        return cadencia;
    }

    public void setCadencia(Integer cadencia) {
        this.cadencia = cadencia;
    }

    public String getDuracao() {
        return duracao;
    }

    public void setDuracao(String duracao) {
        this.duracao = duracao;
    }

    public String getVelocidade() {
        return velocidade;
    }

    public void setVelocidade(String velocidade) {
        this.velocidade = velocidade;
    }

    public String getDistancia() {
        return distancia;
    }

    public void setDistancia(String distancia) {
        this.distancia = distancia;
    }

    public Integer getDescancoIntervalo() {
        return descancoIntervalo;
    }

    public void setDescancoIntervalo(Integer descancoIntervalo) {
        this.descancoIntervalo = descancoIntervalo;
    }

    public Integer getQtdSeries() {
        return qtdSeries;
    }

    public void setQtdSeries(Integer qtdSeries) {
        this.qtdSeries = qtdSeries;
    }

    public String getObsComplemento() {
        return obsComplemento;
    }

    public void setObsComplemento(String obsComplemento) {
        this.obsComplemento = obsComplemento;
    }
}

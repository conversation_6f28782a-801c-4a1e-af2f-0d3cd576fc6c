package importador.json;

import br.com.pactosolucoes.comuns.json.SuperJSON;
import negocio.comuns.utilitarias.Uteis;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * Created by <PERSON><PERSON> on 20/12/2019.
 */
public class ClienteImportacaoJSON extends SuperJSON {

    private Long idExterno;
    private Long matriculaExterna;
    private String nome, cpf, rg, rgOrgao, sexo, estadoCivil, necessidadeEspecial, profissao, grauInstrucao, nacionalidade;
    private String naturalidade, cidade, uf, nomePai, cpfPai, nomeMae, cpfMae, observacao, categoria, empresaFornecedor;
    private Date dataNascimento, dataCadastro;
    private List<TelefoneImportacaoJSON> telefones;
    private List<EmailImportacaoJSON> emails;
    private List<EnderecoImportacaoJSON> enderecos;
    private List<VinculoImportacaoJSON> vinculos;
    private Integer usuario;
    private Integer empresa;
    private Integer codigo;
    private String familiares;
    private boolean sucesso = false;
    private String msgRetorno;

    public String getNome() {
        if (nome == null) {
            nome = "";
        }
        return nome.toUpperCase();
    }

    public void setNome(String nome) {
        this.nome = nome;
    }

    public String getCpf_Formatado() {
        return Uteis.formatarCpfCnpj(getCpf(), false);
    }

    public String getCpf_SomenteNumeros() {
        return Uteis.formatarCpfCnpj(getCpf(), true);
    }

    public String getCpf() {
        if (cpf == null) {
            cpf = "";
        }
        return cpf;
    }

    public void setCpf(String cpf) {
        this.cpf = cpf;
    }

    public String getRgOrgao() {
        if (rgOrgao == null) {
            rgOrgao = "";
        }
        return rgOrgao;
    }

    public void setRgOrgao(String rgOrgao) {
        this.rgOrgao = rgOrgao;
    }

    public String getSexo() {
        if (sexo == null) {
            sexo = "";
        }
        return sexo;
    }

    public void setSexo(String sexo) {
        this.sexo = sexo;
    }

    public String getEstadoCivil() {
        if (estadoCivil == null) {
            estadoCivil = "";
        }
        return estadoCivil;
    }

    public void setEstadoCivil(String estadoCivil) {
        this.estadoCivil = estadoCivil;
    }

    public String getProfissao() {
        if (profissao == null) {
            profissao = "";
        }
        return profissao;
    }

    public void setProfissao(String profissao) {
        this.profissao = profissao;
    }

    public String getGrauInstrucao() {
        if (grauInstrucao == null) {
            grauInstrucao = "";
        }
        return grauInstrucao;
    }

    public void setGrauInstrucao(String grauInstrucao) {
        this.grauInstrucao = grauInstrucao;
    }

    public String getNacionalidade() {
        if (nacionalidade == null) {
            nacionalidade = "";
        }
        return nacionalidade;
    }

    public void setNacionalidade(String nacionalidade) {
        this.nacionalidade = nacionalidade;
    }

    public String getNaturalidade() {
        if (naturalidade == null) {
            naturalidade = "";
        }
        return naturalidade;
    }

    public void setNaturalidade(String naturalidade) {
        this.naturalidade = naturalidade;
    }

    public String getCidade() {
        if (cidade == null) {
            cidade = "";
        }
        return cidade;
    }

    public void setCidade(String cidade) {
        this.cidade = cidade;
    }

    public String getUf() {
        if (uf == null) {
            uf = "";
        }
        return uf;
    }

    public void setUf(String uf) {
        this.uf = uf;
    }

    public String getRg() {
        if (rg == null) {
            rg = "";
        }
        return rg;
    }

    public void setRg(String rg) {
        this.rg = rg;
    }

    public String getCategoria() {
        if (categoria == null) {
            categoria = "";
        }
        return categoria;
    }

    public void setCategoria(String categoria) {
        this.categoria = categoria;
    }

    public Long getIdExterno() {
        return idExterno;
    }

    public void setIdExterno(Long idExterno) {
        this.idExterno = idExterno;
    }

    public String getObservacao() {
        if (observacao == null) {
            observacao = "";
        }
        return observacao;
    }

    public void setObservacao(String observacao) {
        this.observacao = observacao;
    }

    public List<TelefoneImportacaoJSON> getTelefones() {
        if (telefones == null) {
            telefones = new ArrayList<>();
        }
        return telefones;
    }

    public void setTelefones(List<TelefoneImportacaoJSON> telefones) {
        this.telefones = telefones;
    }

    public List<EmailImportacaoJSON> getEmails() {
        if (emails == null) {
            emails = new ArrayList<>();
        }
        return emails;
    }

    public void setEmails(List<EmailImportacaoJSON> emails) {
        this.emails = emails;
    }

    public List<EnderecoImportacaoJSON> getEnderecos() {
        if (enderecos == null) {
            enderecos = new ArrayList<>();
        }
        return enderecos;
    }

    public void setEnderecos(List<EnderecoImportacaoJSON> enderecos) {
        this.enderecos = enderecos;
    }

    public List<VinculoImportacaoJSON> getVinculos() {
        if (vinculos == null) {
            vinculos = new ArrayList<>();
        }
        return vinculos;
    }

    public void setVinculos(List<VinculoImportacaoJSON> vinculos) {
        this.vinculos = vinculos;
    }

    public Long getMatriculaExterna() {
        return matriculaExterna;
    }

    public void setMatriculaExterna(Long matriculaExterna) {
        this.matriculaExterna = matriculaExterna;
    }

    public Date getDataNascimento() {
        return dataNascimento;
    }

    public void setDataNascimento(Date dataNascimento) {
        this.dataNascimento = dataNascimento;
    }

    public Date getDataCadastro() {
        return dataCadastro;
    }

    public void setDataCadastro(Date dataCadastro) {
        this.dataCadastro = dataCadastro;
    }

    public String getNomePai() {
        if (nomePai == null) {
            nomePai = "";
        }
        return nomePai;
    }

    public void setNomePai(String nomePai) {
        this.nomePai = nomePai;
    }

    public String getCpfPai() {
        return cpfPai;
    }

    public void setCpfPai(String cpfPai) {
        this.cpfPai = cpfPai;
    }

    public String getNomeMae() {
        if (nomeMae == null) {
            nomeMae = "";
        }
        return nomeMae;
    }

    public void setNomeMae(String nomeMae) {
        this.nomeMae = nomeMae;
    }

    public String getCpfMae() {
        return cpfMae;
    }

    public void setCpfMae(String cpfMae) {
        this.cpfMae = cpfMae;
    }

    public boolean isSucesso() {
        return sucesso;
    }

    public void setSucesso(boolean sucesso) {
        this.sucesso = sucesso;
    }

    public String getMsgRetorno() {
        if (msgRetorno == null) {
            msgRetorno = "";
        }
        return msgRetorno;
    }

    public void setMsgRetorno(String msgRetorno) {
        this.msgRetorno = msgRetorno;
    }

    public Integer getCodigo() {
        if (codigo == null) {
            codigo = 0;
        }
        return codigo;
    }

    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public Integer getEmpresa() {
        if (empresa == null) {
            empresa = 0;
        }
        return empresa;
    }

    public void setEmpresa(Integer empresa) {
        this.empresa = empresa;
    }

    public Integer getUsuario() {
        if (usuario == null) {
            usuario = 0;
        }
        return usuario;
    }

    public void setUsuario(Integer usuario) {
        this.usuario = usuario;
    }

    public String getNecessidadeEspecial() {
        return necessidadeEspecial;
    }

    public void setNecessidadeEspecial(String necessidadeEspecial) {
        this.necessidadeEspecial = necessidadeEspecial;
    }

    public String getEmpresaFornecedor() {
        return empresaFornecedor;
    }

    public void setEmpresaFornecedor(String empresaFornecedor) {
        this.empresaFornecedor = empresaFornecedor;
    }

    public String getFamiliares() {
        return familiares;
    }

    public void setFamiliares(String familiares) {
        this.familiares = familiares;
    }
}

package importador.json;

import br.com.pactosolucoes.ce.comuns.enumerador.DiaSemana;
import br.com.pactosolucoes.comuns.json.SuperJSON;
import negocio.comuns.utilitarias.UteisValidacao;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

public class TurmaImportacaoJSON extends SuperJSON {

    private Integer idExterno;
    private String turma;
    private String modalidade;
    private Date vigenciaDe;
    private Date vigenciaAte;
    private Integer idadeMinima;
    private Integer idadeMinimaMeses;
    private Integer idadeMaxima;
    private Integer idadeMaximaMeses;
    private List<DiaSemana> diasSemana;
    private String horaInicio;
    private String horaFim;
    private Integer capacidade;
    private Integer codigoProfessor;
    private String ambiente;
    private String nivel;
    private Integer tolerancia;
    private Integer codigoEmpresa;

    public Integer getIdExterno() {
        return idExterno;
    }

    public void setIdExterno(Integer idExterno) {
        this.idExterno = idExterno;
    }

    public String getTurma() {
        return turma;
    }

    public void setTurma(String turma) {
        this.turma = turma;
    }

    public String getModalidade() {
        return modalidade;
    }

    public void setModalidade(String modalidade) {
        this.modalidade = modalidade;
    }

    public Date getVigenciaDe() {
        return vigenciaDe;
    }

    public void setVigenciaDe(Date vigenciaDe) {
        this.vigenciaDe = vigenciaDe;
    }

    public Date getVigenciaAte() {
        return vigenciaAte;
    }

    public void setVigenciaAte(Date vigenciaAte) {
        this.vigenciaAte = vigenciaAte;
    }

    public Integer getIdadeMinima() {
        return idadeMinima;
    }

    public void setIdadeMinima(Integer idadeMinima) {
        this.idadeMinima = idadeMinima;
    }

    public Integer getIdadeMinimaMeses() {
        return idadeMinimaMeses;
    }

    public void setIdadeMinimaMeses(Integer idadeMinimaMeses) {
        this.idadeMinimaMeses = idadeMinimaMeses;
    }

    public Integer getIdadeMaxima() {
        return idadeMaxima;
    }

    public void setIdadeMaxima(Integer idadeMaxima) {
        this.idadeMaxima = idadeMaxima;
    }

    public Integer getIdadeMaximaMeses() {
        return idadeMaximaMeses;
    }

    public void setIdadeMaximaMeses(Integer idadeMaximaMeses) {
        this.idadeMaximaMeses = idadeMaximaMeses;
    }

    public List<DiaSemana> getDiasSemana() {
        if (diasSemana == null) {
            diasSemana = new ArrayList<>();
        }
        return diasSemana;
    }

    public void setDiasSemana(List<DiaSemana> diasSemana) {
        this.diasSemana = diasSemana;
    }

    public String getHoraInicio() {
        return horaInicio;
    }

    public void setHoraInicio(String horaInicio) {
        this.horaInicio = horaInicio;
    }

    public String getHoraFim() {
        return horaFim;
    }

    public void setHoraFim(String horaFim) {
        this.horaFim = horaFim;
    }

    public Integer getCapacidade() {
        return capacidade;
    }

    public void setCapacidade(Integer capacidade) {
        this.capacidade = capacidade;
    }

    public Integer getCodigoProfessor() {
        return codigoProfessor;
    }

    public void setCodigoProfessor(Integer codigoProfessor) {
        this.codigoProfessor = codigoProfessor;
    }

    public String getAmbiente() {
        return ambiente;
    }

    public void setAmbiente(String ambiente) {
        this.ambiente = ambiente;
    }

    public String getNivel() {
        return nivel;
    }

    public void setNivel(String nivel) {
        this.nivel = nivel;
    }

    public Integer getTolerancia() {
        return tolerancia;
    }

    public void setTolerancia(Integer tolerancia) {
        this.tolerancia = tolerancia;
    }

    public Integer getCodigoEmpresa() {
        return codigoEmpresa;
    }

    public void setCodigoEmpresa(Integer codigoEmpresa) {
        this.codigoEmpresa = codigoEmpresa;
    }

    public void validarDados() throws Exception {
        if (UteisValidacao.emptyNumber(idExterno)) {
            throw new Exception("O campo idExterno é obrigatório");
        }
        if (UteisValidacao.emptyString(turma)) {
            throw new Exception("O campo turma é obrigatório");
        }
        if (UteisValidacao.emptyString(modalidade)) {
            throw new Exception("O campo modalidade é obrigatório");
        }
        if (vigenciaDe == null) {
            throw new Exception("O campo vigenciaDe é obrigatório");
        }
        if (vigenciaAte == null) {
            throw new Exception("O campo vigenciaAte é obrigatório");
        }
        if (UteisValidacao.emptyList(diasSemana)) {
            throw new Exception("O campo diasSemana é obrigatório");
        }
        if (UteisValidacao.emptyString(horaInicio)) {
            throw new Exception("O campo horaInicio é obrigatório");
        }
        if (UteisValidacao.emptyString(horaFim)) {
            throw new Exception("O campo horaFim é obrigatório");
        }
        if (UteisValidacao.emptyNumber(capacidade)) {
            throw new Exception("O campo capacidade é obrigatório");
        }
        if (UteisValidacao.emptyNumber(codigoProfessor)) {
            throw new Exception("O campo codigoProfessor é obrigatório");
        }
        if (UteisValidacao.emptyString(ambiente)) {
            throw new Exception("O campo ambiente é obrigatório");
        }
    }
}

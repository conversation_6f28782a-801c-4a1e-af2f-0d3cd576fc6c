package importador.json;

import br.com.pactosolucoes.comuns.json.SuperJSON;

public class FamiliarImportacaoJSON extends SuperJSON {
    private String matriculaExternaAluno;
    private String matriculaExternaFamiliar;
    private String parentesco;

    public String getMatriculaExternaAluno() {
        return matriculaExternaAluno;
    }

    public void setMatriculaExternaAluno(String matriculaExternaAluno) {
        this.matriculaExternaAluno = matriculaExternaAluno;
    }

    public String getMatriculaExternaFamiliar() {
        return matriculaExternaFamiliar;
    }

    public void setMatriculaExternaFamiliar(String matriculaExternaFamiliar) {
        this.matriculaExternaFamiliar = matriculaExternaFamiliar;
    }

    public String getParentesco() {
        return parentesco;
    }

    public void setParentesco(String parentesco) {
        this.parentesco = parentesco;
    }
}

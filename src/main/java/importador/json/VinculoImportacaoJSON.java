package importador.json;

import br.com.pactosolucoes.comuns.json.SuperJSON;

/**
 * Created by <PERSON><PERSON> on 20/12/2019.
 */
public class VinculoImportacaoJSON extends SuperJSON {

    private String tipoVinculo;
    private Integer colaborador;
    private String nomeColaborador;

    public String getTipoVinculo() {
        return tipoVinculo;
    }

    public void setTipoVinculo(String tipoVinculo) {
        this.tipoVinculo = tipoVinculo;
    }

    public Integer getColaborador() {
        return colaborador;
    }

    public void setColaborador(Integer colaborador) {
        this.colaborador = colaborador;
    }

    public String getNomeColaborador() {
        return nomeColaborador;
    }

    public void setNomeColaborador(String nomeColaborador) {
        this.nomeColaborador = nomeColaborador;
    }
}

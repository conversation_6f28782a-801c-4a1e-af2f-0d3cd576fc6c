package importador.json;

import br.com.pactosolucoes.enumeradores.SituacaoParcelaEnum;

import java.util.Date;
import br.com.pactosolucoes.comuns.json.SuperJSON;

public class ParcelaPagamentoJSON extends SuperJSON {

    private Long matriculaExterna;
    private Long idExternoContrato;
    private Long idExternoParcela;
    private Integer numeroParcela;
    private Double valorParcela;
    private String situacaoParcela;
    private Integer codigoSituacaoParcela;
    private Date dtVencimento;
    private Date dtPagamento;
    private String formaPagamento;
    private Integer codigoFormaPagamento;
    private Integer qtdParcelasCartao;
    private Long numeroNsu;
    private boolean sucesso = false;
    private String msgRetorno;

    public Long getMatriculaExterna() {
        return matriculaExterna;
    }

    public void setMatriculaExterna(Long matriculaExterna) {
        this.matriculaExterna = matriculaExterna;
    }

    public Long getIdExternoContrato() {
        return idExternoContrato;
    }

    public void setIdExternoContrato(Long idExternoContrato) {
        this.idExternoContrato = idExternoContrato;
    }

    public Long getIdExternoParcela() {
        return idExternoParcela;
    }

    public void setIdExternoParcela(Long idExternoParcela) {
        this.idExternoParcela = idExternoParcela;
    }

    public Integer getNumeroParcela() {
        return numeroParcela;
    }

    public void setNumeroParcela(Integer numeroParcela) {
        this.numeroParcela = numeroParcela;
    }

    public Double getValorParcela() {
        return valorParcela;
    }

    public void setValorParcela(Double valorParcela) {
        this.valorParcela = valorParcela;
    }

    public Integer getCodigoSituacaoParcela() {
        return codigoSituacaoParcela;
    }

    public void setCodigoSituacaoParcela(Integer codigoSituacaoParcela) {
        this.codigoSituacaoParcela = codigoSituacaoParcela;
    }

    public Date getDtVencimento() {
        return dtVencimento;
    }

    public void setDtVencimento(Date dtVencimento) {
        this.dtVencimento = dtVencimento;
    }

    public Date getDtPagamento() {
        return dtPagamento;
    }

    public void setDtPagamento(Date dtPagamento) {
        this.dtPagamento = dtPagamento;
    }

    public String getFormaPagamento() {
        return formaPagamento;
    }

    public void setFormaPagamento(String formaPagamento) {
        this.formaPagamento = formaPagamento;
    }

    public Integer getCodigoFormaPagamento() {
        return codigoFormaPagamento;
    }

    public void setCodigoFormaPagamento(Integer codigoFormaPagamento) {
        this.codigoFormaPagamento = codigoFormaPagamento;
    }

    public Integer getQtdParcelasCartao() {
        return qtdParcelasCartao;
    }

    public void setQtdParcelasCartao(Integer qtdParcelasCartao) {
        this.qtdParcelasCartao = qtdParcelasCartao;
    }

    public Long getNumeroNsu() {
        return numeroNsu;
    }

    public void setNumeroNsu(Long numeroNsu) {
        this.numeroNsu = numeroNsu;
    }

    public String getSituacaoParcela() {
        return situacaoParcela;
    }

    public void setSituacaoParcela(String situacaoParcela) {
        this.situacaoParcela = situacaoParcela;
    }

    public boolean isSucesso() {
        return sucesso;
    }

    public void setSucesso(boolean sucesso) {
        this.sucesso = sucesso;
    }

    public String getMsgRetorno() {
        return msgRetorno;
    }

    public void setMsgRetorno(String msgRetorno) {
        this.msgRetorno = msgRetorno;
    }
}

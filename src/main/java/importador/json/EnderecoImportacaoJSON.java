package importador.json;

import br.com.pactosolucoes.comuns.json.SuperJSON;
import br.com.pactosolucoes.enumeradores.TipoEnderecoEnum;

/**
 * Created by <PERSON><PERSON> on 20/12/2019.
 */
public class EnderecoImportacaoJSON extends SuperJSON {

    private String tipo;
    private String cep;
    private String bairro;
    private String numero;
    private String complemento;
    private String endereco;
    private boolean correspondencia = true;

    public String getTipo() {
        if (tipo == null) {
            tipo = TipoEnderecoEnum.RESIDENCIAL.getCodigo();
        }
        return tipo;
    }

    public void setTipo(String tipo) {
        this.tipo = tipo;
    }

    public String getCep() {
        if (cep == null) {
            cep = "";
        }
        return cep;
    }

    public void setCep(String cep) {
        this.cep = cep;
    }

    public String getBairro() {
        if (bairro == null) {
            bairro = "";
        }
        return bairro;
    }

    public void setBairro(String bairro) {
        this.bairro = bairro;
    }

    public String getNumero() {
        if (numero == null) {
            numero = "";
        }
        return numero;
    }

    public void setNumero(String numero) {
        this.numero = numero;
    }

    public String getComplemento() {
        if (complemento == null) {
            complemento = "";
        }
        return complemento;
    }

    public void setComplemento(String complemento) {
        this.complemento = complemento;
    }

    public String getEndereco() {
        if (endereco == null) {
            endereco = "";
        }
        return endereco;
    }

    public void setEndereco(String endereco) {
        this.endereco = endereco;
    }

    public boolean isCorrespondencia() {
        return correspondencia;
    }

    public void setCorrespondencia(boolean correspondencia) {
        this.correspondencia = correspondencia;
    }
}

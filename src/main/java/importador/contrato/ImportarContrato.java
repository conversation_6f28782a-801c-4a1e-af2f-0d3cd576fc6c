package importador.contrato;


import br.com.pactosolucoes.ce.comuns.enumerador.DiaSemana;
import importador.ImportacaoCache;
import importador.ImportacaoConfigTO;
import importador.alunosTurma.ImportarAlunosTurma;
import importador.enumerador.ImportacaoParcelasSituacaoEnum;
import importador.json.ContratoImportacaoJSON;
import negocio.comuns.basico.ClienteVO;
import negocio.comuns.basico.EmpresaVO;
import negocio.comuns.contrato.*;
import negocio.comuns.financeiro.*;
import negocio.comuns.plano.*;
import negocio.comuns.plano.enumerador.TipoProduto;
import negocio.comuns.utilitarias.Calendario;
import negocio.comuns.utilitarias.Ordenacao;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisValidacao;
import negocio.facade.jdbc.arquitetura.Log;
import negocio.facade.jdbc.arquitetura.SuperFacadeJDBC;
import negocio.facade.jdbc.arquitetura.ZillyonWebFacade;
import negocio.facade.jdbc.basico.Cliente;
import negocio.facade.jdbc.basico.Empresa;
import negocio.facade.jdbc.contrato.*;
import negocio.facade.jdbc.financeiro.*;
import negocio.facade.jdbc.plano.*;
import negocio.facade.jdbc.utilitarias.Conexao;
import org.json.JSONArray;
import relatorio.negocio.comuns.sad.SituacaoClienteSinteticoEnum;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

public class ImportarContrato {

    private Connection con;
    private ImportacaoConfigTO configTO;

    public ImportarContrato(ImportacaoConfigTO configTO, Connection con) {
        this.con = con;
        this.configTO = configTO;
    }

    public ContratoVO importarContrato(ContratoImportacaoJSON json, ImportacaoCache cache) throws Exception {
        try {
            con.setAutoCommit(false);

            Uteis.logar(true, null, "INICIA IMPORTAÇÃO | CONTRATO... " + json.getIdExterno());


            ResultSet rsCliente = SuperFacadeJDBC.criarConsulta(
                    "SELECT codigo FROM cliente WHERE coalesce(idExterno, matriculaexterna) = " + json.getIdExternoCliente(), con);

            ClienteVO clienteVO;

            if (rsCliente.next()) {
                Cliente clienteDAO = new Cliente(con);
                clienteVO = clienteDAO.consultarPorChavePrimaria(rsCliente.getInt("codigo"), Uteis.NIVELMONTARDADOS_MINIMOS);
                clienteDAO = null;
            } else {
                throw new Exception("Cliente de id ou matricula externa " + json.getIdExternoCliente() + " não foi encontrado para importação do contrato");
            }

            json.setNome(clienteVO.getPessoa().getNome());

            ContratoVO contratoVO = montarContratoVO(json, clienteVO, cache);

            ResultSet rsExiste = SuperFacadeJDBC.criarConsulta("SELECT codigo FROM contrato WHERE idExterno = " + contratoVO.getIdExterno()
                    + " and empresa = " + json.getEmpresa(), con);
            if (rsExiste.next()) {
                throw new Exception("Já existe um CONTRATO importado com o idExterno " + contratoVO.getIdExterno() + " na empresa " + contratoVO.getEmpresa());
            }

            incluirSemCommit(contratoVO);


            if (contratoVO != null && !UteisValidacao.emptyNumber(contratoVO.getCodigo())) {
                gerarHistoricoContrato(contratoVO);

                //gerar e incluir produtos
                List<MovProdutoVO> produtosContrato = gerarMovProdutos(contratoVO, json.getValorMatricula());
                //gerar e incluir parcelas
                List<MovParcelaVO> parcelasContrato = gerarMovParcelas(contratoVO, contratoVO.getValorBaseCalculo(), json.getNrParcelas());
                //gerar relacionamento
                gerarProdutosParcelasContrato(produtosContrato, parcelasContrato);

                //gerar parcela de matricula
//            parcelasContrato.add(gerarParcelaMatricula(contratoVO, json));
                //gerar parcela de anuidade
                MovParcelaVO parcelaAnuidade = gerarParcelaAnuidade(contratoVO, json);
                if (parcelaAnuidade != null) {
                    parcelasContrato.add(parcelaAnuidade);
                }

                ImportacaoParcelasSituacaoEnum importacaoParcelasSituacaoEnum = ImportacaoParcelasSituacaoEnum.obterPorCodigo(json.getImportacaoParcelasSituacao());
                if (importacaoParcelasSituacaoEnum == null) {
                    throw new Exception("ImportacaoContratoParcelasEnum não encontrado");
                }

                if(contratoVO.getValorFinal() != null && contratoVO.getValorFinal() > 0.0) {
                    gerarPagamentoParcelas(contratoVO, parcelasContrato, importacaoParcelasSituacaoEnum);
                }

                Log logDao = new Log(con);
                logDao.incluirLogItemImportacao("CONTRATO", contratoVO.getCodigo(), cache.getUsuarioVOImportacao(), contratoVO.getPessoa().getCodigo());
                logDao = null;

                Contrato contratoDAO = new Contrato(con);
                contratoDAO.gerarHistoricoTemporalUmContrato(contratoVO.getCodigo());
                contratoDAO = null;

                ZillyonWebFacade zwFacadeDAO = new ZillyonWebFacade(con);
                zwFacadeDAO.atualizarSintetico(clienteVO, Calendario.hoje(), SituacaoClienteSinteticoEnum.GRUPO_TODOS, false);
                zwFacadeDAO = null;

                json.setSucesso(true);
                json.setCodigo(contratoVO.getCodigo());
                json.setMsgRetorno("Contrato importado com sucesso.");
            } else {
                throw new Exception("Contrato não importado.");
            }
            if(con.getAutoCommit()) {
                con.setAutoCommit(false);
            }
            con.commit();
            return contratoVO;
        } catch (Exception ex) {
            json.setSucesso(false);
            json.setCodigo(null);
            json.setMsgRetorno("ERRO Contrato ID Ext. " + json.getIdExterno() + " - " + ex.getMessage());
            ex.printStackTrace();
            con.rollback();
            throw ex;
        } finally {
            con.setAutoCommit(true);
        }
    }

    public void gerarHistoricoContrato(ContratoVO contratoVO) throws Exception {

        HistoricoContrato historicoContratoDAO = new HistoricoContrato(con);
        HistoricoContratoVO historicoContratoVO = new HistoricoContratoVO();
        historicoContratoVO.setContrato(contratoVO.getCodigo());

        if (!UteisValidacao.emptyNumber(contratoVO.getContratoBaseadoRenovacao())) {
            historicoContratoVO.setDescricao("RENOVADO");
            historicoContratoVO.setTipoHistorico("RN");
        } else {
            historicoContratoVO.setDescricao("MATRICULADO");
            historicoContratoVO.setTipoHistorico("MA");
        }
        historicoContratoVO.setDataInicioTemporal(contratoVO.getVigenciaDe());
        historicoContratoVO.setResponsavelRegistro(contratoVO.getResponsavelContrato());
        historicoContratoVO.setDataRegistro(contratoVO.getDataLancamento());
        historicoContratoVO.setDataInicioSituacao(contratoVO.getVigenciaDe());
        historicoContratoVO.setDataFinalSituacao(contratoVO.getVigenciaAteAjustada());

        historicoContratoDAO.incluirSemCommit(historicoContratoVO, false);
    }

    public void importarVinculoTurma(String[] horariosExtraidosExcel, ContratoVO contratoVO, ContratoImportacaoJSON json, Connection con) throws Exception {
        if(json.getIdExternoTurma() != null) {
            ImportarAlunosTurma importarAlunosTurma = new ImportarAlunosTurma(con, configTO);
            HorarioTurma horarioTurmaDao = new HorarioTurma(con);
            Turma turmaDao = new Turma(con);

            TurmaVO turmaEncontradaPeloIdExt = turmaDao.consultarPorIdexterno(json.getIdExternoTurma().toString(), Uteis.NIVELMONTARDADOS_TODOS);

            if(turmaEncontradaPeloIdExt == null) {
                throw new Exception("Não foi encontrado uma turma com ID Externo " + json.getIdExternoTurma());
            }

            String[] diasSemanaValidados = json.getDiasTurma().stream().map(DiaSemana::getCodigo).toArray(String[]::new);

            ArrayList<String> horariosTurmaNaoEncontradosParaDiaSemana = new ArrayList<>();

            JSONArray codigosHorariosTurma = new JSONArray();

            for (String diaSemana : diasSemanaValidados) {
                HorarioTurmaVO horarioTurmaVo = horarioTurmaDao.consultarPorHorarioDiaSemanaCodTurma(
                        horariosExtraidosExcel[0], // Hora inicial
                        horariosExtraidosExcel[1], // Hora final
                        diaSemana,
                        turmaEncontradaPeloIdExt.getCodigo(),
                        Uteis.NIVELMONTARDADOS_DADOSBASICOS
                );

                if (horarioTurmaVo == null) {
                    horariosTurmaNaoEncontradosParaDiaSemana.add(diaSemana);
                } else {
                    codigosHorariosTurma.put(horarioTurmaVo.getCodigo());
                }
            }

            if (!horariosTurmaNaoEncontradosParaDiaSemana.isEmpty()) {
                throw new Exception("Não foi encontrado horarioTurma no sistema para os seguintes dias da semana: "
                        + horariosTurmaNaoEncontradosParaDiaSemana + " e horário: " + json.getHorarioTurma());
            }

            importarAlunosTurma.alterContratoIncluindoTurma(
                    contratoVO.getCodigo(),
                    turmaEncontradaPeloIdExt.getModalidade().getCodigo(),
                    codigosHorariosTurma,
                    true,
                    true
                    );

        }


    }





    private MovParcelaVO gerarParcelaMatricula(ContratoVO contratoVO, ContratoImportacaoJSON json) throws Exception {
        Produto produtoDAO = new Produto(con);
        ProdutoVO produtoMatricula = produtoDAO.criarOuConsultarExisteProdutoPorTipo("", TipoProduto.MATRICULA.getCodigo(), 0.0);
        produtoDAO = null;

        String descricaoParcela = "PARCELA MATRICULA";
        return gerarParcela(contratoVO, produtoMatricula, json.getValorMatricula(), descricaoParcela);
    }

    private MovParcelaVO gerarParcelaAnuidade(ContratoVO contratoVO, ContratoImportacaoJSON json) throws Exception {
        if (!UteisValidacao.emptyNumber(json.getValorAnuidade())) {
            Produto produtoDAO = new Produto(con);
            ProdutoVO produtoAnuidade = produtoDAO.criarOuConsultarExisteProdutoPorTipo("", TipoProduto.TAXA_DE_ANUIDADE_PLANO_RECORRENCIA.getCodigo(), 0.0);
            produtoDAO = null;
            String descricaoParcela = "ANUIDADE PLANO RECORRENTE - " + Uteis.getAnoData(contratoVO.getVigenciaDe());
            return gerarParcela(contratoVO, produtoAnuidade, json.getValorAnuidade(), descricaoParcela);
        }
        return null;
    }

    private void gerarProdutosParcelasContrato(List<MovProdutoVO> produtosContrato, List<MovParcelaVO> parcelasContrato) throws Exception {
        Ordenacao.ordenarLista(produtosContrato, "codigo");
        Ordenacao.ordenarLista(parcelasContrato, "codigo");

        for (MovParcelaVO movParcelaVO : parcelasContrato) {

            Double valorParcela = Uteis.arredondarForcando2CasasDecimais(movParcelaVO.getValorParcela());
            Double valorFalta = valorParcela;

            for (MovProdutoVO movProdutoVO : produtosContrato) {
                if (UteisValidacao.emptyNumber(valorFalta)) {
                    continue;
                }

                Double valorProduto = Uteis.arredondarForcando2CasasDecimais(movProdutoVO.getTotalFinal());
                Double valorPagoMovProdutoParcela = Uteis.arredondarForcando2CasasDecimais(movProdutoVO.getValorPagoMovProdutoParcela());

                if (!UteisValidacao.emptyNumber(valorProduto) && valorPagoMovProdutoParcela.equals(valorProduto)) {
                    continue;
                }

                Double valorPago;
                if (valorProduto <= valorFalta) {
                    valorPago = valorProduto;
                } else {
                    valorPago = (valorProduto - valorFalta);
                }
                valorPago = Uteis.arredondarForcando2CasasDecimais(valorPago);

                gerarIncluirMovProdutoParcela(movParcelaVO, movProdutoVO, valorPago);
                valorFalta = Uteis.arredondarForcando2CasasDecimais(valorFalta - valorPago);
                movProdutoVO.setValorPagoMovProdutoParcela(movProdutoVO.getValorPagoMovProdutoParcela() + valorPago);
            }
        }
    }

    private ContratoVO montarContratoVO(ContratoImportacaoJSON json, ClienteVO clienteVO, ImportacaoCache cache) throws Exception {
        ContratoVO contratoVO = new ContratoVO();

        if(json.getIdExterno() != null) {
            contratoVO.setIdExterno(json.getIdExterno());
        } else {
            ResultSet rsUltimoIdexternoContrato = SuperFacadeJDBC.criarConsulta("select max(idexterno) as idexterno from contrato", con);
            if(rsUltimoIdexternoContrato.next()) {
                contratoVO.setIdExterno(rsUltimoIdexternoContrato.getInt("idexterno") + 1);
            }
        }

        contratoVO.setPessoa(clienteVO.getPessoa());
        contratoVO.setObservacao(json.getObservacao());
        contratoVO.setRegimeRecorrencia(json.isRecorrencia());

        //empresa
        EmpresaVO empresaVO = cache.obterEmpresaVO(json.getEmpresa());
        contratoVO.setEmpresa(empresaVO);

        Double valorTotal = 0.0;

        // Valor total
        if(json.getValorTotal() != null) {
            valorTotal = json.getValorTotal();
        }

        // Datas
        if (json.getDataInicio() == null) {
            throw new Exception("Data início do contrato não informada.");
        }
        if (json.getDataFim() == null) {
            throw new Exception("Data fim do contrato não informada.");
        }
        Date inicioContrato = json.getDataInicio();
        Date finalContrato = json.getDataFim();
        Date lancamentoContrato = json.getDataLancamento();

        SimpleDateFormat sdf = new SimpleDateFormat("dd/MM/yyyy");

        List<String> datasInvalidas = new ArrayList<>();

        if (inicioContrato != null && inicioContrato.compareTo(finalContrato) > 0) {
            datasInvalidas.add("data de início do contrato (" + sdf.format(inicioContrato) + ")");
        }

        if (lancamentoContrato != null && lancamentoContrato.compareTo(finalContrato) > 0) {
            datasInvalidas.add("data de lançamento do contrato (" + sdf.format(lancamentoContrato) + ")");
        }

        if (!datasInvalidas.isEmpty()) {
            String mensagem = String.join(" e a ", datasInvalidas) +
                    (datasInvalidas.size() > 1 ? " não podem ser posteriores " : " não pode ser posterior ") +
                    "à data de término do contrato (" + sdf.format(finalContrato) + ").";
            throw new Exception(mensagem);
        }

        contratoVO.setVigenciaDe(inicioContrato);
        contratoVO.setVigenciaAte(finalContrato);
        contratoVO.setVigenciaAteAjustada(finalContrato);
        contratoVO.setDataLancamento(lancamentoContrato);
        contratoVO.setDataMatricula(inicioContrato);
        contratoVO.setDataPrevistaRematricula(finalContrato);
        contratoVO.setDataPrevistaRenovar(finalContrato);

        // Duracao
        ContratoDuracaoVO contratoDuracaoVO = new ContratoDuracaoVO();
        contratoDuracaoVO.setCarencia(json.getDiasCarencia());

        Integer numeroMeses = json.getDuracao();
        if (UteisValidacao.emptyNumber(numeroMeses)) {
            Long dias = Uteis.nrDiasEntreDatas(json.getDataInicio(), json.getDataFim());
            if (dias < 30) {
                numeroMeses = 1;
            } else {
                numeroMeses = (new Long(dias / 30).intValue());
            }
        }
        contratoDuracaoVO.setNumeroMeses(numeroMeses);
        contratoVO.setContratoDuracao(contratoDuracaoVO);


        // Situacao do contrato
        if (Calendario.menor(contratoVO.getVigenciaAteAjustada(), Calendario.hoje())) {
            contratoVO.setSituacao("IN");
        } else {
            contratoVO.setSituacao("AT");
        }

        // Plano
        contratoVO.setPlano(cache.obterPlanoVO(json.getPlano()));


        contratoVO.setValorFinal(valorTotal);
        contratoVO.setValorBaseCalculo(valorTotal - json.getValorMatricula() - json.getValorAnuidade());

        // Responsável e consultor
        Integer usuario = json.getUsuario();
        if (UteisValidacao.emptyNumber(usuario)) {
            usuario = cache.getUsuarioVOImportacao().getCodigo();
        }
        contratoVO.setResponsavelContrato(cache.obterUsuarioVO(usuario));

        Integer consultor = json.getConsultor();
        if (UteisValidacao.emptyNumber(consultor)) {
            consultor = cache.obterColaboradorPactoBREmpresa(empresaVO.getCodigo()).getCodigo();
        }
        contratoVO.setConsultor(cache.obterColaboradorVO(consultor));

        //contrato modalidade
        //(regra: se possui turma, vai usar a modalidade da turma; só usar modalidade configurada caso o id externo da turma não tenha sido informado )
        Integer codigoModalidade;
        if(json.getIdExternoTurma() != null) {
            Turma turmaDao = new Turma(con);
            TurmaVO turmaEncontrada = turmaDao.consultarPorIdexterno(json.getIdExternoTurma().toString(), Uteis.NIVELMONTARDADOS_TODOS);
            if(turmaEncontrada == null) {
                throw new Exception(" Turma com ID Externo " + json.getIdExternoTurma() + " não encontrada" );
            }
            if(turmaEncontrada.getModalidade().getCodigo().intValue() != 0) {
                codigoModalidade = turmaEncontrada.getModalidade().getCodigo();
            } else {
                codigoModalidade = json.getModalidade();
                Uteis.logar("Importação de contrato com ID Externo "
                        + json.getIdExterno() + ": não foi encontrado modalidade para turma com id externo "
                        + json.getIdExternoTurma());
            }

        } else {
            codigoModalidade = json.getModalidade();
        }

        ContratoModalidadeVO contMod = new ContratoModalidadeVO();
        ModalidadeVO modalidadeVO = cache.obterModalidadeVO(codigoModalidade);
        modalidadeVO.setModalidadeEscolhida(true);
        Double valor = (contratoVO.getValorFinal() - 0.0) / contratoVO.getContratoDuracao().getNumeroMeses();

        //modalidade
        contMod.setModalidade(modalidadeVO);
        contMod.setNrVezesSemana(7);
        contMod.setValorModalidade(valor);
        contMod.setValorFinalModalidade(valor);

        //vezes semana
        PlanoModalidadeVezesSemanaVO planoVezesSemanaVO = new PlanoModalidadeVezesSemanaVO();
        planoVezesSemanaVO.setNrVezes(1);
        planoVezesSemanaVO.setVezeSemanaEscolhida(true);
        contMod.setPlanoVezesSemanaVO(planoVezesSemanaVO);

        //utilizar turma
        //modalidade escolhida
        contMod.setModalidade(modalidadeVO);
        contMod.getModalidade().setUtilizarTurma(false);
        contMod.getModalidade().setModalidadeEscolhida(true);
        List<ContratoModalidadeVO> contratoModalidadeVOs = new ArrayList<>();
        contratoModalidadeVOs.add(contMod);
        contratoVO.setContratoModalidadeVOs(contratoModalidadeVOs);

        //plano duracao
        PlanoDuracao planoDuracaoDAO = new PlanoDuracao(con);
        PlanoDuracaoVO planoDuracaoVO = planoDuracaoDAO.consultarPorNumeroMesesPlano(contratoVO.getContratoDuracao().getNumeroMeses(), json.getPlano(), Uteis.NIVELMONTARDADOS_DADOSBASICOS);
        planoDuracaoDAO = null;

        if (planoDuracaoVO == null || UteisValidacao.emptyNumber(planoDuracaoVO.getCodigo())) {
            planoDuracaoVO = new PlanoDuracaoVO();
            planoDuracaoVO.setCodigo(1);
            contratoVO.setPlanoDuracao(planoDuracaoVO);
            contratoVO.getPlanoDuracao().setDuracaoEscolhida(true);
            contratoVO.getPlanoDuracao().setNrMaximoParcelasCondPagamento(1);
            contratoVO.getPlanoDuracao().setNumeroMeses(contratoVO.getContratoDuracao().getNumeroMeses());
            contratoVO.getPlanoDuracao().setValorDesejado(0.0);
            contratoVO.getPlanoDuracao().setCarencia(json.getDiasCarencia());
            contratoVO.getPlanoDuracao().setTipoValor("PD");
            contratoVO.getPlanoDuracao().setTipoOperacao("AC");
        } else {
            planoDuracaoVO.setCarencia(json.getDiasCarencia());
            contratoVO.setPlanoDuracao(planoDuracaoVO);
        }

        //horario contrato
        ContratoHorarioVO contratoHorarioVO = new ContratoHorarioVO();

        HorarioVO horarioVO = null;
        if(json.getIdExternoTurma() != null) {
            Horario horario = new Horario(con);
            horarioVO = horario.consultarPorDescricao("HORARIO DA TURMA", Uteis.NIVELMONTARDADOS_DADOSBASICOS);

        } else {
            horarioVO = cache.obterHorarioVO(json.getHorario());
        }


        contratoHorarioVO.setHorario(horarioVO);
        contratoVO.setContratoHorario(contratoHorarioVO);
        //horario contrato
        PlanoHorarioVO planoHorarioVO = new PlanoHorarioVO();
        planoHorarioVO.setCodigo(1);
        planoHorarioVO.setHorario(horarioVO);
        contratoVO.setPlanoHorario(planoHorarioVO);


        //condição de pagamento
        PlanoCondicaoPagamento planoCondDAO = new PlanoCondicaoPagamento(con);
        List listaPlanoCond = planoCondDAO.consultaPlanoCondicaoPagamentosNumeroParcelas(contratoVO.getPlanoDuracao().getCodigo(), contratoVO.getPlanoDuracao().getNumeroMeses(), Uteis.NIVELMONTARDADOS_DADOSBASICOS);
        planoCondDAO = null;
        PlanoCondicaoPagamentoVO planoCondicaoPagamentoVO = new PlanoCondicaoPagamentoVO();
        if (!UteisValidacao.emptyList(listaPlanoCond)) {
            planoCondicaoPagamentoVO = (PlanoCondicaoPagamentoVO) listaPlanoCond.get(0);
        }
        contratoVO.setPlanoCondicaoPagamento(planoCondicaoPagamentoVO);

        //plano texto padrão
        ContratoTextoPadraoVO contratoTextoPadraoVO = new ContratoTextoPadraoVO();
        contratoTextoPadraoVO.setPlanoTextoPadrao(contratoVO.getPlano().getPlanoTextoPadrao());
        contratoVO.setContratoTextoPadrao(contratoTextoPadraoVO);

        // histórico de contrato
//        gerarHistoricoContrato(contratoVO);

        // periodoacessocliente
        contratoVO.setPeriodoAcessoClienteVOs(new ArrayList());
        contratoVO.getPeriodoAcessoClienteVOs().add(montarPeriodoAcesso(contratoVO));

        if (contratoVO.isRegimeRecorrencia()) {
            Double valorMensal = json.getValorMensal();
            if (UteisValidacao.emptyNumber(valorMensal)) {
                valorMensal = Uteis.arredondarForcando2CasasDecimais(contratoVO.getValorBaseCalculo() / contratoVO.getContratoDuracao().getNumeroMeses());
            }
            ContratoRecorrenciaVO contratoRecorrenciaVO = new ContratoRecorrenciaVO();
            contratoRecorrenciaVO.setContrato(contratoVO);
            contratoRecorrenciaVO.setDiaVencimentoAnuidade(Uteis.getDiaMesData(Calendario.hoje()));
            contratoRecorrenciaVO.setMesVencimentoAnuidade(Uteis.getMesData(Calendario.hoje()));
            contratoRecorrenciaVO.setDiaVencimentoCartao(Uteis.getDiaMesData(Calendario.hoje()));
            contratoRecorrenciaVO.setDiasCancelamentoAutomatico(json.getDiasCancelamentoAutomatico());
            contratoRecorrenciaVO.setFidelidade(contratoVO.getContratoDuracao().getNumeroMeses());
            contratoRecorrenciaVO.setPessoa(contratoVO.getPessoa());
            contratoRecorrenciaVO.setRenovavelAutomaticamente(true);
            contratoRecorrenciaVO.setValorMensal(valorMensal);
            contratoRecorrenciaVO.setValorAnuidade(json.getValorAnuidade());
            contratoRecorrenciaVO.setAnuidadeNaParcela(false);
            contratoRecorrenciaVO.setCancelamentoProporcional(false);
            contratoVO.setContratoRecorrenciaVO(contratoRecorrenciaVO);
        }

        return contratoVO;
    }

    public void incluirSemCommit(final ContratoVO obj) throws Exception {
        obj.realizarUpperCaseDados();
        String sql = "INSERT INTO Contrato( empresa, pessoa, plano, situacao, "
                + "estendeCoberturaFamiliares, vigenciaDe, vigenciaAte, valorBaseCalculo, valorFinal, responsavelLiberacaoCondicaoPagamento, convenioDesconto, "
                + "observacao, responsavelContrato, pagarComBoleto, dividirProdutosNasParcelas, desconto, tipoDesconto, valorDescontoEspecifico, "
                + "dataLancamento, situacaoContrato, dataMatricula, dataPrevistaRenovar, situacaoRenovacao, "
                + "contratoBaseadoRenovacao, dataPrevistaRematricula, situacaoRematricula, "
                + "contratoBaseadoRematricula, vigenciaAteAjustada, contratoResponsavelRenovacaoMatricula, "
                + "contratoResponsavelRematriculaMatricula, somaProduto,nomeModalidades, valorDescontoPorcentagem, "
                + "bolsa, naoPermitirRenovacaoRematriculaDeContratoAnteriores, consultor, diaVencimentoProrata, datarematricularealizada, "
                + "importacao, regimerecorrencia, idExterno) "
                + "VALUES ( ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, true, ?, ?)";
        int i = 0;
        PreparedStatement sqlInserir = con.prepareStatement(sql);
        sqlInserir.setInt(++i, obj.getEmpresa().getCodigo());
        sqlInserir.setInt(++i, obj.getPessoa().getCodigo());
        sqlInserir.setInt(++i, obj.getPlano().getCodigo());
        sqlInserir.setString(++i, obj.getSituacao());
        sqlInserir.setBoolean(++i, obj.getEstendeCoberturaFamiliares());
        sqlInserir.setDate(++i, Uteis.getDataJDBC(obj.getVigenciaDe()));
        sqlInserir.setDate(++i, Uteis.getDataJDBC(obj.getVigenciaAte()));
        sqlInserir.setDouble(++i, obj.getValorBaseCalculo());
        sqlInserir.setDouble(++i, obj.getValorFinal());
        if (obj.getResponsavelLiberacaoCondicaoPagamento().getCodigo() != 0) {
            sqlInserir.setInt(++i, obj.getResponsavelLiberacaoCondicaoPagamento().getCodigo());
        } else {
            sqlInserir.setNull(++i, 0);
        }
        if (obj.getConvenioDesconto().getCodigo() != 0) {
            sqlInserir.setInt(++i, obj.getConvenioDesconto().getCodigo());
        } else {
            sqlInserir.setNull(++i, 0);
        }
        sqlInserir.setString(++i, obj.getObservacao());
        if (obj.getResponsavelContrato().getCodigo() != 0) {
            sqlInserir.setInt(++i, obj.getResponsavelContrato().getCodigo());
        } else {
            sqlInserir.setNull(++i, 0);
        }
        sqlInserir.setBoolean(++i, obj.getPagarComBoleto());
        sqlInserir.setBoolean(++i, obj.getDividirProdutosNasParcelas());
        if (obj.getDesconto().getCodigo() != 0) {
            sqlInserir.setInt(++i, obj.getDesconto().getCodigo());
        } else {
            sqlInserir.setNull(++i, 0);
        }
        sqlInserir.setString(++i, obj.getTipoDesconto());
        sqlInserir.setDouble(++i, obj.getValorDescontoEspecifico());
        sqlInserir.setTimestamp(++i, Uteis.getDataJDBCTimestamp(obj.getDataLancamento()));
        sqlInserir.setString(++i, obj.getSituacaoContrato());
        sqlInserir.setDate(++i, Uteis.getDataJDBC(obj.getDataMatricula()));
        sqlInserir.setDate(++i, Uteis.getDataJDBC(obj.getDataPrevistaRenovar()));
        sqlInserir.setString(++i, obj.getSituacaoRenovacao());
        sqlInserir.setInt(++i, obj.getContratoBaseadoRenovacao());
        sqlInserir.setDate(++i, Uteis.getDataJDBC(obj.getDataPrevistaRematricula()));
        sqlInserir.setString(++i, obj.getSituacaoRematricula());
        sqlInserir.setInt(++i, obj.getContratoBaseadoRematricula());
        sqlInserir.setDate(++i, Uteis.getDataJDBC(obj.getVigenciaAteAjustada()));
        sqlInserir.setInt(++i, obj.getContratoResponsavelRenovacaoMatricula());
        sqlInserir.setInt(++i, obj.getContratoResponsavelRematriculaMatricula());
        sqlInserir.setDouble(++i, obj.getSomaProduto());
        obj.obterNomeModalidadeCliente();
        sqlInserir.setString(++i, obj.getNomeModalidades());
        sqlInserir.setDouble(++i, obj.getValorDescontoPorcentagem());
        sqlInserir.setBoolean(++i, obj.getBolsa());
        sqlInserir.setBoolean(++i, obj.getNaoPermitirRenovacaoRematriculaDeContratoAnteriores());
        if (obj.getConsultor().getCodigo() != 0) {
            sqlInserir.setInt(++i, obj.getConsultor().getCodigo());
        } else {
            sqlInserir.setNull(++i, 0);
        }
        sqlInserir.setInt(++i, obj.getDiaVencimentoProrata());
        sqlInserir.setDate(++i, Uteis.getDataJDBC(obj.getDataRematriculaRealizada()));
        sqlInserir.setBoolean(++i, obj.isRegimeRecorrencia());
        sqlInserir.setInt(++i, obj.getIdExterno());
        sqlInserir.execute();
        obj.setCodigo(Conexao.obterUltimoCodigoGeradoTabela(con, "contrato"));

        obj.setNovoObj(false);
        obj.inicializarDadosContrato();

        ContratoDuracao contratoDuracaoDAO = new ContratoDuracao(con);
        contratoDuracaoDAO.incluir(obj.getContratoDuracao());
        contratoDuracaoDAO = null;

        if (obj.getContratoHorario().getHorario().getCodigo() > 0) {
            ContratoHorario contratoHorarioDAO = new ContratoHorario(con);
            contratoHorarioDAO.incluir(obj.getContratoHorario());
            contratoHorarioDAO = null;
        }

        ContratoCondicaoPagamento contratoCondicaoPagamentoDAO = new ContratoCondicaoPagamento(con);
        contratoCondicaoPagamentoDAO.incluir(obj.getContratoCondicaoPagamento());
        contratoCondicaoPagamentoDAO = null;

        ContratoComposicao contratoComposicaoDAO = new ContratoComposicao(con);
        contratoComposicaoDAO.incluirContratoComposicaos(obj.getCodigo(), obj.getContratoComposicaoVOs());
        contratoComposicaoDAO = null;

        ContratoTextoPadrao contratoTextoPadraoDAO = new ContratoTextoPadrao(con);
        contratoTextoPadraoDAO.incluir(obj.getContratoTextoPadrao());
        contratoTextoPadraoDAO = null;

        ContratoModalidade contratoModalidadeDAO = new ContratoModalidade(con);
        contratoModalidadeDAO.incluirContratoModalidades(obj.getCodigo(), obj.getContratoModalidadeVOs());
        contratoModalidadeDAO = null;

        ContratoPlanoProdutoSugerido contratoPlanoProdutoSugeridoDAO = new ContratoPlanoProdutoSugerido(con);
        contratoPlanoProdutoSugeridoDAO.incluirContratoPlanoProdutoSugerido(obj.getCodigo(), obj.getContratoPlanoProdutoSugeridoVOs());
        contratoPlanoProdutoSugeridoDAO = null;

        HistoricoContrato historicoContratoDAO = new HistoricoContrato(con);
        historicoContratoDAO.incluirHistoricoContratos(obj.getCodigo(), obj.getHistoricoContratoVOs());
        historicoContratoDAO = null;

        PeriodoAcessoCliente periodoAcessoClienteDAO = new PeriodoAcessoCliente(con);
        periodoAcessoClienteDAO.incluirPeriodoAcessoClienteContrato(obj, obj.getPeriodoAcessoClienteVOs());
        periodoAcessoClienteDAO = null;

        if (obj.isRegimeRecorrencia()) {
            ContratoRecorrencia contratoRecorrenciaDAO = new ContratoRecorrencia(con);
            contratoRecorrenciaDAO.incluir(obj.getContratoRecorrenciaVO());
            contratoRecorrenciaDAO = null;
        }

    }

    private void gerarPagamentoParcelas(ContratoVO contratoVO, List<MovParcelaVO> listaParcelasContrato, ImportacaoParcelasSituacaoEnum importacaoParcelasSituacaoEnum) throws Exception {

        if (importacaoParcelasSituacaoEnum.equals(ImportacaoParcelasSituacaoEnum.TODAS_EM_ABERTO)) {
            return;
        }

        FormaPagamento formaPagamentoDAO = new FormaPagamento(con);
        FormaPagamentoVO formaPagamentoVO = formaPagamentoDAO.consultarAVista();
        formaPagamentoDAO = null;

        for (MovParcelaVO movParcelaVO : listaParcelasContrato) {

            if (importacaoParcelasSituacaoEnum.equals(ImportacaoParcelasSituacaoEnum.TODAS_PAGAS) ||
                    (importacaoParcelasSituacaoEnum.equals(ImportacaoParcelasSituacaoEnum.FUTURAS_EM_ABERTO) &&
                            Calendario.menor(movParcelaVO.getDataVencimento(), Calendario.hoje()))) {

                List<MovPagamentoVO> listaPagamento = new ArrayList<>();
                MovPagamentoVO movPagamento = new MovPagamentoVO();
                movPagamento.setPessoa(contratoVO.getPessoa());
                movPagamento.setDataLancamento(contratoVO.getDataLancamento());
                movPagamento.setDataPagamento(contratoVO.getDataLancamento());
                movPagamento.setDataQuitacao(contratoVO.getDataLancamento());
                movPagamento.setResponsavelPagamento(contratoVO.getResponsavelContrato());
                movPagamento.setValor(movParcelaVO.getValorParcela());
                movPagamento.setValorTotal(movParcelaVO.getValorParcela());
                movPagamento.setEmpresa(contratoVO.getEmpresa());
                movPagamento.setFormaPagamento(formaPagamentoVO);
                movPagamento.getFormaPagamento().setTipoFormaPagamento("AV");
                movPagamento.setNomePagador(contratoVO.getPessoa().getNome());
                movPagamento.setMovPagamentoEscolhida(true);
                listaPagamento.add(movPagamento);

                List<MovParcelaVO> listaParcelas = new ArrayList<>();
                listaParcelas.add(movParcelaVO);

                MovPagamento movPagamentoDAO = new MovPagamento(con);
                ReciboPagamentoVO reciboVO = movPagamentoDAO.incluirListaPagamento(
                        listaPagamento, listaParcelas, null, contratoVO,
                        false, 0.0, false, null, null);
                movPagamentoDAO = null;
            }
        }
    }

    private PeriodoAcessoClienteVO montarPeriodoAcesso(ContratoVO contratoVO) {
        PeriodoAcessoClienteVO periodoAcesso = new PeriodoAcessoClienteVO();
        periodoAcesso.setPessoa(contratoVO.getPessoa().getCodigo());
        periodoAcesso.setContrato(contratoVO.getCodigo());
        periodoAcesso.setDataInicioAcesso(contratoVO.getVigenciaDe());
        periodoAcesso.setDataFinalAcesso(contratoVO.getVigenciaAteAjustada());
        periodoAcesso.setTipoAcesso("CA");
        return periodoAcesso;
    }

    private List<MovParcelaVO> gerarMovParcelas(ContratoVO contratoVO, Double valorTotalParcelas, Integer qtdParcelas) throws Exception {
        List<MovParcelaVO> lista = new ArrayList<>();

        if (qtdParcelas == null || qtdParcelas == 0) {
            qtdParcelas = Uteis.getMesesEntreDatas(contratoVO.getVigenciaDe(), contratoVO.getVigenciaAte()).size();
        }

        Double valorParcela = Uteis.arredondarForcando2CasasDecimais(valorTotalParcelas / qtdParcelas);
        Date dataVencimento = contratoVO.getVigenciaDe();

        int i = 0;
        while (i < qtdParcelas) {
            MovParcelaVO movParcelaVO = montarMovParcelaVO(++i, dataVencimento, valorParcela, contratoVO);
            lista.add(movParcelaVO);
            dataVencimento = Calendario.somarMeses(dataVencimento, 1);
        }

        MovParcela movParcelaDAO = new MovParcela(con);
        movParcelaDAO.incluirListaMovParcelas(lista);
        movParcelaDAO = null;

        return lista;
    }

    private MovParcelaVO montarMovParcelaVO(Integer nrParcela, Date dataVencimento, Double valorParcela, ContratoVO contratoVO) {
        MovParcelaVO movParcelaVO = new MovParcelaVO();
        movParcelaVO.setContrato(contratoVO);
        movParcelaVO.setDataRegistro(contratoVO.getDataLancamento());
        movParcelaVO.setDataVencimento(dataVencimento);
        movParcelaVO.setDescricao("PARCELA " + nrParcela);
        movParcelaVO.setResponsavel(contratoVO.getResponsavelContrato());
        movParcelaVO.setSituacao(UteisValidacao.emptyNumber(valorParcela) ? "PG" : "EA");
        movParcelaVO.setValorParcela(valorParcela);
        movParcelaVO.setPercentualJuro(0.0);
        movParcelaVO.setPercentualMulta(0.0);
        movParcelaVO.setPessoa(contratoVO.getPessoa());
        movParcelaVO.setEmpresa(contratoVO.getEmpresa());
        movParcelaVO.setRegimeRecorrencia(contratoVO.getRegimeRecorrencia());
        return movParcelaVO;
    }

    private List<MovProdutoVO> gerarMovProdutos(ContratoVO contratoVO, Double valorMatricula) throws Exception {
        List<MovProdutoVO> lista = new ArrayList<MovProdutoVO>();
        Integer duracao = contratoVO.getContratoDuracao().getNumeroMeses();
        Date dataInicio = contratoVO.getVigenciaDe();
        Date dataFim = Uteis.somarCampoData(dataInicio, Calendar.MONTH, 1);
        lista.add(montarMatricula(contratoVO, dataInicio, valorMatricula));

        Integer codProdutoMensalidade = contratoVO.getPlano().getProdutoPadraoGerarParcelasContrato().getCodigo();
        Double valorMensalidadeContrato = (contratoVO.getValorBaseCalculo());

        if (duracao > 1) {
            Double valorMensalidade = Uteis.arredondarForcando2CasasDecimais(valorMensalidadeContrato / duracao);
            lista.add(montarMensalidade(contratoVO, dataInicio, dataFim, codProdutoMensalidade, valorMensalidade));
            for (int i = 1; i < duracao; i++) {
                dataInicio = dataFim;
                dataFim = Uteis.somarCampoData(dataInicio, Calendar.MONTH, 1);
                lista.add(montarMensalidade(contratoVO, dataInicio, dataFim, codProdutoMensalidade, valorMensalidade));
            }
        } else {
            lista.add(montarMensalidade(contratoVO, dataInicio, contratoVO.getVigenciaAte(), codProdutoMensalidade, valorMensalidadeContrato));
        }

        MovProduto movProdutoDAO = new MovProduto(con);
        movProdutoDAO.incluirListaMovProdutos(lista);
        movProdutoDAO = null;
        return lista;
    }

    private MovProdutoVO montarMatricula(ContratoVO contratoVO, Date inicio, Double valor) throws Exception {
        Produto produtoDAO = new Produto(con);
        ProdutoVO matricula = produtoDAO.criarOuConsultarExisteProdutoPorTipo("", "MA", 0.0);
        produtoDAO = null;

        Integer ano = Uteis.getAnoData(inicio);
        String mes = Uteis.getMesReferencia(inicio);

        MovProdutoVO movProduto = new MovProdutoVO();
        movProduto.setSituacao(UteisValidacao.emptyNumber(valor) ? "PG" : "EA");
        movProduto.setAnoReferencia(ano);
        movProduto.setMesReferencia(mes + "/" + ano);
        movProduto.setResponsavelLancamento(contratoVO.getResponsavelContrato());
        movProduto.setDataLancamento(contratoVO.getDataLancamento());
        movProduto.setTotalFinal(valor);
        movProduto.setValorDesconto(0.0);
        movProduto.setPrecoUnitario(valor);
        movProduto.setQuantidade(1);
        movProduto.setDescricao("MATRICULA");
        movProduto.setEmpresa(contratoVO.getEmpresa());
        movProduto.setPessoa(contratoVO.getPessoa());
        movProduto.setContrato(contratoVO);
        movProduto.setProduto(matricula);
        movProduto.setApresentarMovProduto(true);
        movProduto.setQuitado(true);
        return movProduto;
    }

    private MovParcelaVO gerarParcela(ContratoVO contratoVO, ProdutoVO produtoVO, Double valor, String descricaoParcela) throws Exception {

        valor = Uteis.arredondarForcando2CasasDecimais(valor);

        Integer ano = Uteis.getAnoData(contratoVO.getVigenciaDe());
        String mes = Uteis.getMesReferencia(contratoVO.getVigenciaDe());

        MovProdutoVO movProduto = new MovProdutoVO();
        movProduto.setSituacao(UteisValidacao.emptyNumber(valor) ? "PG" : "EA");
        movProduto.setAnoReferencia(ano);
        movProduto.setMesReferencia(mes + "/" + ano);
        movProduto.setResponsavelLancamento(contratoVO.getResponsavelContrato());
        movProduto.setDataLancamento(contratoVO.getDataLancamento());
        movProduto.setTotalFinal(valor);
        movProduto.setValorDesconto(0.0);
        movProduto.setPrecoUnitario(valor);
        movProduto.setQuantidade(1);
        movProduto.setDescricao(produtoVO.getDescricao());
        movProduto.setEmpresa(contratoVO.getEmpresa());
        movProduto.setPessoa(contratoVO.getPessoa());
        movProduto.setContrato(contratoVO);
        movProduto.setProduto(produtoVO);
        movProduto.setApresentarMovProduto(true);
        movProduto.setQuitado(true);
        MovProduto movProdutoDAO = new MovProduto(con);
        movProdutoDAO.incluirSemCommit(movProduto);
        movProdutoDAO = null;

        MovParcelaVO movParc = new MovParcelaVO();
        movParc.setValorBaseCalculo(valor);
        movParc.setValorParcela(valor);
        movParc.setDescricao(descricaoParcela);
        movParc.setDataRegistro(contratoVO.getDataLancamento());
        movParc.setDataVencimento(contratoVO.getVigenciaDe());
        movParc.setDataCobranca(contratoVO.getVigenciaDe());
        movParc.setResponsavel(contratoVO.getResponsavelContrato());
        movParc.setSituacao(UteisValidacao.emptyNumber(movParc.getValorParcela()) ? "PG" : "EA");
        movParc.setContrato(contratoVO);
        movParc.setEmpresa(contratoVO.getEmpresa());
        movParc.setPessoa(contratoVO.getPessoa());
        MovParcela movParcelaDAO = new MovParcela(con);
        movParcelaDAO.incluirSemCommit(movParc);
        movParcelaDAO = null;

        gerarIncluirMovProdutoParcela(movParc, movProduto, valor);
        return movParc;
    }

    private void gerarIncluirMovProdutoParcela(MovParcelaVO movParcela, MovProdutoVO movProduto, Double valorPago) throws Exception {
        MovProdutoParcelaVO movProdutoParcela = new MovProdutoParcelaVO();
        movProdutoParcela.setMovProduto(movProduto.getCodigo());
        movProdutoParcela.setMovProdutoVO(movProduto);
        movProdutoParcela.setValorPago(valorPago);
        movProdutoParcela.setMovParcelaVO(movParcela);
        movProdutoParcela.setMovParcela(movParcela.getCodigo());
        MovProdutoParcela movProdutoParcelaDAO = new MovProdutoParcela(con);
        movProdutoParcelaDAO.incluir(movProdutoParcela);
        movProdutoParcelaDAO = null;
    }

    private MovProdutoVO montarMensalidade(ContratoVO contratoVO, Date inicio, Date fim,
                                           Integer codProduto, Double valor) {

        Integer ano = Uteis.getAnoData(inicio);
        String mes = Uteis.getMesReferencia(inicio);

        MovProdutoVO movProduto = new MovProdutoVO();
        movProduto.setSituacao(UteisValidacao.emptyNumber(valor) ? "PG" : "EA");
        movProduto.setDataFinalVigencia(fim);
        movProduto.setDataInicioVigencia(inicio);
        movProduto.setAnoReferencia(ano);
        movProduto.setMesReferencia(mes + "/" + ano);
        movProduto.setResponsavelLancamento(contratoVO.getResponsavelContrato());
        movProduto.setDataLancamento(contratoVO.getDataLancamento());
        movProduto.setTotalFinal(valor);
        movProduto.setValorDesconto(0.0);
        movProduto.setPrecoUnitario(valor);
        movProduto.setQuantidade(1);
        movProduto.setDescricao(contratoVO.getPlano().getDescricao() + " - " + mes + "/" + ano);
        movProduto.setEmpresa(contratoVO.getEmpresa());
        movProduto.setPessoa(contratoVO.getPessoa());
        movProduto.setContrato(contratoVO);
        ProdutoVO produto = new ProdutoVO();
        produto.setCodigo(codProduto);
        movProduto.setProduto(produto);
        movProduto.setApresentarMovProduto(true);
        movProduto.setQuitado(true);
        return movProduto;
    }

    public ImportacaoConfigTO getConfigTO() {
        return configTO;
    }

    public void setConfigTO(ImportacaoConfigTO configTO) {
        this.configTO = configTO;
    }

    public Connection getCon() {
        return con;
    }

    public void setCon(Connection con) {
        this.con = con;
    }
}

package importador.membersevo.threads;

import br.com.pactosolucoes.comuns.json.JSONMapper;
import importador.ImportacaoCache;
import importador.ImportacaoConfigTO;
import importador.membersevo.ImportadorMembersEvo;
import negocio.comuns.acesso.IntegracaoMemberVO;
import negocio.comuns.acesso.integracao.member.MemberDataJson;
import negocio.comuns.acesso.integracao.member.MembersApiViewTO;
import negocio.comuns.acesso.integracao.member.financeiro.ReceivablesViewTO;
import negocio.comuns.acesso.integracao.member.financeiro.SalesApiViewTO;
import negocio.comuns.basico.ClienteVO;
import negocio.comuns.basico.ProcessoImportacaoItemVO;
import negocio.comuns.basico.ProcessoImportacaoLogVO;
import negocio.comuns.basico.ProcessoImportacaoVO;
import negocio.comuns.basico.enumerador.TipoLogEnum;
import negocio.comuns.utilitarias.Calendario;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisValidacao;
import negocio.facade.jdbc.acesso.Member;
import negocio.facade.jdbc.arquitetura.SuperFacadeJDBC;
import negocio.facade.jdbc.arquitetura.ZillyonWebFacade;
import negocio.facade.jdbc.basico.ProcessoImportacao;
import negocio.facade.jdbc.basico.ProcessoImportacaoItem;
import negocio.facade.jdbc.basico.ProcessoImportacaoLog;
import negocio.facade.jdbc.utilitarias.Conexao;
import org.json.JSONObject;
import relatorio.negocio.comuns.sad.SituacaoClienteSinteticoEnum;

import java.sql.Connection;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

public class ThreadImportacaoMembers extends Thread {

    private boolean consultaApiTerminou = false;
    private Connection con;
    private IntegracaoMemberVO integracaoMemberVO;
    private ImportacaoCache importacaoCache;
    private ImportacaoConfigTO importacaoConfigTO;
    private ProcessoImportacao processoImportacaoDAO;
    private ProcessoImportacaoLog processoLogDAO;
    private ProcessoImportacaoItem processoItemDAO;
    private Member memberDAO;
    private ZillyonWebFacade zillyonWebFacadeDAO;

    private List<Integer> idsMembers;
    private List<Integer> idsMembersProcessados = new ArrayList<>();

    public ThreadImportacaoMembers(Connection con, IntegracaoMemberVO integracaoMemberVO, ImportacaoConfigTO importacaoConfigTO, List<Integer> idsMembers) throws Exception {
        this.con = Conexao.getInstance().obterNovaConexaoBaseadaOutra(con);
        Conexao.guardarConexaoForJ2SE(this.con);
        this.importacaoConfigTO = importacaoConfigTO;
        this.integracaoMemberVO = integracaoMemberVO;
        this.idsMembers = idsMembers;
        this.importacaoCache = new ImportacaoCache(this.con, this.importacaoConfigTO.getUsuarioResponsavelImportacao());
        this.importacaoCache.carregarContratosImportados();
        this.processoImportacaoDAO = new ProcessoImportacao(con);
        this.processoItemDAO = new ProcessoImportacaoItem(con);
        this.processoLogDAO = new ProcessoImportacaoLog(con);
        this.memberDAO = new Member(con);
        this.zillyonWebFacadeDAO = new ZillyonWebFacade(con);
    }


    @Override
    public void run() {
        try {
            int count = 0;
            int sucesso = 0;
            int falha = 0;

            ProcessoImportacaoVO processoVO = processoImportacaoDAO.iniciarProcessoImportacaoVO(importacaoConfigTO, importacaoConfigTO.getDataInicioProcesso(), idsMembers.size());
            processoVO.atualizarJSONStatus(count, sucesso, falha, idsMembers.size());
            processoImportacaoDAO.atualizarStatus(processoVO);
            Date dataInicioProcesso = importacaoConfigTO.isSincronizarMembersImportados() ? processoVO.getDataFim() : null;

            ImportadorMembersEvo importadorMembersEvo = new ImportadorMembersEvo(con, this.integracaoMemberVO, this.importacaoCache);
            while (count < idsMembers.size()) {


                List<MemberDataJson> membersDataJsonApiResults = memberDAO.obterMembersImportar(integracaoMemberVO.getCodigo(), idsMembers, dataInicioProcesso);
                membersDataJsonApiResults = membersDataJsonApiResults.stream()
                        .filter(memberDataJson -> !idsMembersProcessados.contains(memberDataJson.getIdMember()))
                        .collect(Collectors.toList());


                if (UteisValidacao.emptyList(membersDataJsonApiResults)) {
                    if (isConsultaApiTerminou()) {
                        Uteis.logarDebug(String.format("Finalizando Thread %s...", Thread.currentThread().getName()));
                        break;
                    } else {
                        Uteis.logarDebug(String.format("Thread %s aguardando dados para processar...", Thread.currentThread().getName()));
                        Thread.currentThread().sleep(1000);
                        continue;
                    }
                }

                for (MemberDataJson memberDataJson : membersDataJsonApiResults) {
                    long milis = System.currentTimeMillis();

                    Uteis.logarDebug(String.format("%d\\%d - Member id: %d processando...", count + 1, idsMembers.size(), memberDataJson.getIdMember()));

                    MembersApiViewTO member = JSONMapper.getObject(memberDataJson.getMemberJson(), MembersApiViewTO.class);
                    List<MembersApiViewTO> memberDependentes = JSONMapper.getList(memberDataJson.getMembersDependentesJson(), MembersApiViewTO.class);
                    List<SalesApiViewTO> sales = JSONMapper.getList(memberDataJson.getSalesJson(), SalesApiViewTO.class);
                    List<ReceivablesViewTO> receivables = JSONMapper.getList(memberDataJson.getReceivablesJson(), ReceivablesViewTO.class);

                    try {
                        idsMembersProcessados.add(memberDataJson.getIdMember());

                        ClienteVO clienteVO = importadorMembersEvo.importarCliente(member);
                        importadorMembersEvo.importarCadastroDependentes(memberDependentes);

                        importadorMembersEvo.importarVendasContratosProdutosServicos(clienteVO, sales, member);

                        if (importacaoConfigTO.isImportarReceivablesSemVendas()) {
                            importadorMembersEvo.importarRecebimentosSemVendas(receivables, clienteVO);
                        }

                        if (clienteVO != null && !UteisValidacao.emptyNumber(clienteVO.getCodigo())) {
                            boolean isTitular = SuperFacadeJDBC.existe("select codigo from cliente where titularplanocompartilhado = " + clienteVO.getCodigo(), con);
                            clienteVO.setDeveAtualizarDependentesSintetico(isTitular);
                            zillyonWebFacadeDAO.atualizarSintetico(clienteVO, Calendario.hoje(), SituacaoClienteSinteticoEnum.GRUPO_TODOS, false);
                        }

                        memberDAO.setarSincronizado(memberDataJson.getCodigoMember(), true);

                        processoLogDAO.incluir(new ProcessoImportacaoLogVO(processoVO, TipoLogEnum.SUCESSO, String.format("Matricula: %d - Processada com sucesso", member.getIdMember())));
                        sucesso++;
                    } catch (Exception ex) {
                        falha++;
                        handleError(ex, memberDataJson.getIdMember(), processoVO);
                    } finally {
                        processoVO.atualizarJSONStatus(++count, sucesso, falha, idsMembers.size());
                        processoImportacaoDAO.atualizarStatus(processoVO);
                        processoItemDAO.incluir(new ProcessoImportacaoItemVO(processoVO, new JSONObject().toString()));
                    }
                    String msg = String.format(">>>>> Processar Member id: %d levou: %d ms | Qtd Contratos: %d | Qtd pagamentos %d",
                            member.getIdMember(), System.currentTimeMillis() - milis, member.getMemberships().size(), receivables.size());
                    Uteis.logarDebug(msg);
                }
            }
            this.processoImportacaoDAO.finalizarProcessoImportacaoVO(processoVO, importacaoConfigTO);
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    private void handleError(Exception ex, Integer idMember, ProcessoImportacaoVO processoImportacaoVO) {
        ex.printStackTrace();
        String erro = UteisValidacao.emptyString(ex.getMessage()) ? ex.toString() : ex.getMessage();
        String msgErro = String.format("Matricula: %d - Falha ao processar: %s", idMember, erro);
        try {
            processoLogDAO.incluir(new ProcessoImportacaoLogVO(processoImportacaoVO, TipoLogEnum.ERRO, msgErro));
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    public boolean isConsultaApiTerminou() {
        return consultaApiTerminou;
    }

    public void setConsultaApiTerminou(boolean consultaApiTerminou) {
        this.consultaApiTerminou = consultaApiTerminou;
    }
}

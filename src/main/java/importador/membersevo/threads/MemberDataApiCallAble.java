package importador.membersevo.threads;

import br.com.pactosolucoes.comuns.json.JSONMapper;
import importador.UteisImportacao;
import negocio.comuns.acesso.IntegracaoMemberVO;
import negocio.comuns.acesso.integracao.member.MemberDataJson;
import negocio.comuns.acesso.integracao.member.MemberMemberShipCancellationApiViewTO;
import negocio.comuns.acesso.integracao.member.MembersApiViewTO;
import negocio.comuns.acesso.integracao.member.enums.MemberShipStatusEnum;
import negocio.comuns.utilitarias.Calendario;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisValidacao;
import org.apache.http.HttpEntity;
import org.apache.http.auth.AuthScope;
import org.apache.http.auth.UsernamePasswordCredentials;
import org.apache.http.client.CredentialsProvider;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpGet;
import org.apache.http.impl.client.BasicCredentialsProvider;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClientBuilder;
import org.apache.http.util.EntityUtils;
import org.json.JSONArray;
import org.json.JSONObject;

import java.util.*;
import java.util.concurrent.Callable;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.concurrent.atomic.AtomicLong;
import java.util.concurrent.locks.ReentrantLock;

public class MemberDataApiCallAble implements Callable<MemberDataJson> {

    private static final String URL_BASE_EVO_INTEGRACAO = "https://evo-integracao.w12app.com.br/api"; // 600 requisições por minuto
    private static final String URL_BASE_EVO_INTEGRACAO_API = "https://evo-integracao-api.w12app.com.br/api"; // 20 requisições por minuto
    private final String ENDPOINT = "members";
    private final String ENDPOINT_SALES = "sales";
    private final String ENDPOINT_RECEIVABLES = "receivables";
    private final String ENDPOINT_MEMBER_MEMBER_SHIP = "membermembership";
    private Integer apiCallLimiteQuotaPerMinute = 600;
    private AtomicInteger countRequestsApi;
    private AtomicLong milisStartCount;

    private IntegracaoMemberVO integracaoMemberVO;
    private Integer idMember;
    private MemberDataJson memberDataApiResults = new MemberDataJson();
    private ReentrantLock lock;
    List<String> userAgents = Arrays.asList(
            "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
            "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********",
            "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:136.0) Gecko/20100101 Firefox/136.0",
            "Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
            "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
    );

    public MemberDataApiCallAble(IntegracaoMemberVO integracaoMemberVO, MembersApiViewTO member, AtomicInteger countRequestsApi, AtomicLong milisStartCount, ReentrantLock lock) {
        this.integracaoMemberVO = integracaoMemberVO;
        this.idMember = member.getIdMember();
        this.countRequestsApi = countRequestsApi;
        this.milisStartCount = milisStartCount;
        this.memberDataApiResults.setMemberJson(member.getDataJson());
        this.memberDataApiResults.setIdMember(member.getIdMember());
        this.lock = lock;
    }

    @Override
    public MemberDataJson call() {
        try {
            if (memberDataApiResults.getMemberJson() == null
                    || memberDataApiResults.getMemberJson().optInt("idMember") == 0
                    || Uteis.removerMascara(memberDataApiResults.getMemberJson().optString("document")).length() != 11
            ) {
                memberDataApiResults.setMemberJson(getMember(idMember));
            }
            if (memberDataApiResults.getMemberJson() == null || memberDataApiResults.getMemberJson().optInt("idMember") == 0) {
                throw new Exception("Erro requisição get member");
            }
            memberDataApiResults.setIdsSales(obterIdsSales());
            memberDataApiResults.setSalesJson(obterSalesPorIds());
            memberDataApiResults.setReceivablesJson(obterReceivables());

            JSONArray memberShips = memberDataApiResults.getMemberJson().optJSONArray("memberships");
            if (memberShips != null) {
                for (int i = 0; i < memberShips.length(); i++) {
                    JSONObject memberShipJson = memberShips.getJSONObject(i);
                    Date cancelDate = UteisImportacao.getDateFromLocalDateTime(memberShipJson.optString("cancelDate"));
                    if (cancelDate != null) {
                        JSONObject memberShipCancellationJson = getMemberShipCancellation(idMember, memberShipJson.optInt("idMembership"), cancelDate);
                        memberShipJson.put("memberShipCancellation", memberShipCancellationJson);
                    }
                    if (memberShipJson.optString("membershipStatus").toLowerCase().equals(MemberShipStatusEnum.TRANSFER.getName().toLowerCase())) {
                        JSONObject memberShipCancellationJson = memberShipJson.optJSONObject("memberShipCancellation");
                        if (memberShipCancellationJson != null) {
                            MemberMemberShipCancellationApiViewTO cancellation = JSONMapper.getObject(memberShipCancellationJson, MemberMemberShipCancellationApiViewTO.class);
                            if (cancellation.getMembershipTrasnferData() != null
                                    && !UteisValidacao.emptyNumber(cancellation.getMembershipTrasnferData().getIdMemberTransfer())) {
                                memberDataApiResults.getMembersDependentesJson().put(getMember(cancellation.getMembershipTrasnferData().getIdMemberTransfer()));
                            }
                        }
                    }
                }
            }
        } catch (Exception ex) {
            ex.printStackTrace();
            String msg = String.format("Falha ao tentar obter dados do member: %d - %s", idMember, ex.getMessage());
            Uteis.logarDebug(msg);
            this.memberDataApiResults.setMsgErro(msg);
        }
        return memberDataApiResults;
    }

    private JSONObject getMember(Integer idMember) throws Exception {
        final String url = String.format("%s/v1/%s/%d", getUrlBaseApi(), ENDPOINT, idMember);
        String result = executeRequest(url);
        if (UteisValidacao.emptyString(result)) {
            return null;
        }
        JSONObject jsonObject = new JSONObject(result);
        return jsonObject;
    }

    public List<Integer> obterIdsSales() throws Exception {
        String url = String.format("%s/v1/%s", getUrlBaseApi(), ENDPOINT_SALES);
        url += "?take=%d&skip=%d&showReceivables=false&onlyMembership=false&atLeastMonthly=false&showOnlyActiveMemberships=false";
        url += "&idMember=" + idMember;

        int take = 50;
        int skip = -50;

        List<Integer> ids = new ArrayList<>();

        while (true) {
            skip = skip + take;
            final String formattedUrl = String.format(url, take, skip);

            String result = executeRequest(formattedUrl);
            JSONArray jsonArray = obterJsonArray(result);
            if (jsonArray == null || jsonArray.length() == 0) {
                break;
            }

            for (int i = 0; i < jsonArray.length(); i++) {
                JSONObject jsonObject = jsonArray.getJSONObject(i);
                if (jsonObject != null && jsonObject.optInt("idSale") > 0
                        && !ids.contains(jsonObject.optInt("idSale"))) {
                    ids.add(jsonObject.getInt("idSale"));
                }
            }
            if (jsonArray.length() < take) {
                break;
            }
        }
        return ids;
    }

    public JSONArray obterSalesPorIds() throws Exception {
        JSONArray sales = new JSONArray();
        for (Integer id : memberDataApiResults.getIdsSales()) {
            String url = String.format("%s/v1/%s/%d", getUrlBaseApi(), ENDPOINT_SALES, id);
            String result = executeRequest(url);
            if (result != null) {
                sales.put(new JSONObject(result));
            }
        }
        return sales;
    }

    public JSONArray obterReceivables() throws Exception {
        String url = String.format("%s/v1/%s", getUrlBaseApi(), ENDPOINT_RECEIVABLES);
        url += "?take=%d&skip=%d";
        url += "&memberId=" + idMember;
        int skip = -50;
        int take = 50;

        JSONArray receivables = new JSONArray();
        while (true) {
            skip = skip + take;
            final String formattedUrl = String.format(url, take, skip);

            String result = executeRequest(formattedUrl);
            JSONArray jsonArray = obterJsonArray(result);
            if (jsonArray == null || jsonArray.length() == 0) {
                break;
            }
            for (int i = 0; i < jsonArray.length(); i++) {
                JSONObject jsonObject = jsonArray.getJSONObject(i);
                if (jsonObject == null) {
                    continue;
                }
                receivables.put(jsonObject);
            }
            if (jsonArray.length() < take) {
                break;
            }
        }
        return receivables;
    }

    private JSONArray obterJsonArray(String result) {
        try {
            return result != null ? new JSONArray(result) : null;
        } catch (Exception e) {
            Uteis.logarDebug(String.format("Falha ao tentar obter JSONArray: ERRO %s - %s ", e.getMessage()));
            return null;
        }
    }

    public String executeRequest(String url) throws Exception {
        int maxTentativas = 5;
        int countTentativas = 0;
        long tempoSegundosEntreTentativas = 5;

        List<String> falhas = new ArrayList<>();

        while (countTentativas <= maxTentativas) {
            countTentativas++;
            try {
                verifyLimiteQuotaApi();
                if (integracaoMemberVO.tokensDisponiveisNaoBloqueados().isEmpty()) {
                    throw new Exception("Nenhum token disponivel");
                }
                UsernamePasswordCredentials upc = integracaoMemberVO.getCredentials();
                String urserAgent = userAgents.get(new Random().nextInt(userAgents.size()));

                Uteis.logarDebug(String.format("Thread: %d %s - Executando requisicao: GET %s token: %s useragent: %s", Thread.currentThread().getId(), Thread.currentThread().getName(),
                        url,
                        upc.getPassword().substring(0,4),
                        urserAgent));

                HttpGet request = new HttpGet(url);
                CredentialsProvider provider = new BasicCredentialsProvider();
                provider.setCredentials(AuthScope.ANY, upc);

                request.addHeader("User-Agent", urserAgent);

                try (CloseableHttpClient httpClient = HttpClientBuilder.create()
                        .setDefaultCredentialsProvider(provider)
                        .build();
                     CloseableHttpResponse response = httpClient.execute(request)) {
                    HttpEntity entity = response.getEntity();
                    if (entity != null && response.getStatusLine().getStatusCode() == 200) {
                        return EntityUtils.toString(entity);
                    }
                    String txtResponse = entity != null ? EntityUtils.toString(entity).toLowerCase() : "";
                    if (txtResponse.contains("requests per minute has been reached")) {
                        countRequestsApi.set(apiCallLimiteQuotaPerMinute);
                    } else if (txtResponse.contains("key temporarily blocked due to too many attempts")) {
                        JSONObject jsonResponse = new JSONObject();
                        Date availableAgainAt = jsonResponse.optString("availableAgainAt").trim().isEmpty() ? null : Calendario.getDate("MM/dd/yyyy HH:mm:sss", jsonResponse.optString("availableAgainAt").split("\\\\u")[0].trim());
                        availableAgainAt = availableAgainAt == null ? Calendario.somarMinutos(Calendario.hoje(), 32) : Calendario.somarMinutos(availableAgainAt, 2);
                        integracaoMemberVO.getMapaTokenDateBlocked().put(upc.getPassword(), availableAgainAt);
                    }
                    throw new Exception(String.format("Falha ao executar requisitacao! status: %s response: %s", response.getStatusLine().getStatusCode(), txtResponse));
                }
            } catch (Exception e) {
                Uteis.logarDebug(String.format("Thread: %d %s - tentativa %d\\%d - %s \n%s", Thread.currentThread().getId(), Thread.currentThread().getName(), countTentativas, maxTentativas, url, e.getMessage()));

                if (integracaoMemberVO.tokensDisponiveisNaoBloqueados().isEmpty() && integracaoMemberVO.getMapaTokenDateBlocked().size() > 0) {
                    Date dataDesbloqueio = integracaoMemberVO.obterProximaDataHoraPrevistaDesbloqueio();
                    if (Calendario.maiorComHora(dataDesbloqueio, Calendario.hoje())) {
                        tempoSegundosEntreTentativas = Calendario.diferencaEmMinutos(Calendario.hoje(), dataDesbloqueio);
                        Uteis.logarDebug(String.format("Token(s) Bloqueado(s)! aguardar %d minuto(s) ate o desbloqueio, previsão: %s", tempoSegundosEntreTentativas, Calendario.getDataAplicandoFormatacao(dataDesbloqueio, "dd/MM/yyyy")));
                    }
                } else {
                    Thread.sleep(tempoSegundosEntreTentativas * 1000);
                }
                if (countTentativas == maxTentativas) {
                    throw new Exception(String.format("Falha ao executar requisitacao! url: %s, falhas: %s ", url, falhas));
                }
            }
        }
        return null;
    }

    private void verifyLimiteQuotaApi() {
        lock.lock();
        try {
            if (milisStartCount.get() == 0l) {
                milisStartCount.set(System.currentTimeMillis());
            } else if((System.currentTimeMillis() - milisStartCount.get()) >= 60000) {
                Uteis.logarDebug(String.format("Tread: %d %s - Reniciando contador resquisições por minuto...", Thread.currentThread().getId(), Thread.currentThread().getName()));
                countRequestsApi.set(0);
                milisStartCount.set(System.currentTimeMillis());
            }
            if (countRequestsApi.incrementAndGet() > apiCallLimiteQuotaPerMinute) {
                long tempoAguardar = 60000 - (System.currentTimeMillis() - milisStartCount.get());
                if (tempoAguardar > 0) {
                    Uteis.logarDebug(String.format("Thread: %d %s - Aguardando tempo restante limite Quota API: %d segundos", Thread.currentThread().getId(), Thread.currentThread().getName(), tempoAguardar / 1000));
                    Thread.sleep(tempoAguardar);
                }
                countRequestsApi.set(0);
                milisStartCount.set(System.currentTimeMillis());
            }
        } catch (InterruptedException e) {
            Uteis.logarDebug(String.format("Thread: %d %s - Erro ao tentar aguardar tempo restante limite Quota API", Thread.currentThread().getId(), Thread.currentThread().getName()));
        } finally {
            lock.unlock();
        }
    }
    private JSONObject getMemberShipCancellation(Integer idMember, Integer idMemberShip, Date cancelDate) throws Exception {
        String url = String.format("%s/v2/%s", getUrlBaseApi(), ENDPOINT_MEMBER_MEMBER_SHIP);
        url += "?take=50&skip=0";
        url += "&showTransfers=true";
        url += "&idMembership=" + idMemberShip;
        url += "&idMember=" + idMember;
        url += "&cancelDateStart=" + Calendario.getDataAplicandoFormatacao(cancelDate, "yyyy-MM-dd");
        url += "&cancelDateEnd=" + Calendario.getDataAplicandoFormatacao(cancelDate, "yyyy-MM-dd");

        String result = executeRequest(url);
        JSONArray jsonArray = obterJsonArray(result);
        if (jsonArray == null || jsonArray.length() == 0) {
            return null;
        }

        for(int i = 0; i < jsonArray.length(); i++){
            JSONObject jsonObject = jsonArray.getJSONObject(i);
            if(jsonObject == null){
                continue;
            }
            return jsonObject;
        }
        return null;
    }

    public Integer getIdMember() {
        return idMember;
    }

    public void setIdMember(Integer idMember) {
        this.idMember = idMember;
    }

    public String getUrlBaseApi() {
        return URL_BASE_EVO_INTEGRACAO;
    }
}

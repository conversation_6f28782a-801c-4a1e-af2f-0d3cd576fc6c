package importador;

import negocio.comuns.acesso.IntegracaoMemberVO;
import negocio.comuns.acesso.integracao.member.enums.TipoOperacaoIntegracaoMembersEnum;
import negocio.comuns.basico.enumerador.TipoImportacaoEnum;
import negocio.comuns.utilitarias.Calendario;
import org.json.JSONArray;
import org.json.JSONObject;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * Created by <PERSON><PERSON> on 20/12/2019.
 */
public class ImportacaoConfigTO {

    private TipoImportacaoEnum tipoImportacaoEnum;
    private String chave;
    private Integer usuarioResponsavelImportacao;
    private List<String> listaEmails;
    private IntegracaoMemberVO integracaoMemberVO;
    private String idsMembers;
    private TipoOperacaoIntegracaoMembersEnum tipoOperacaoIntegracaoMembersEnum;
    private Date dataInicioProcesso = Calendario.hoje();
    private boolean importarProspects = true;
    private boolean importarVendas = true;
    private boolean importarReceivablesSemVendas = true;
    private boolean sincronizarMembersImportados = true;

    public List<String> getListaEmails() {
        if (listaEmails == null) {
            listaEmails = new ArrayList<>();
        }
        return listaEmails;
    }

    public void setListaEmails(List<String> listaEmails) {
        this.listaEmails = listaEmails;
    }

    public Integer getUsuarioResponsavelImportacao() {
        if (usuarioResponsavelImportacao == null) {
            usuarioResponsavelImportacao = 0;
        }
        return usuarioResponsavelImportacao;
    }

    public void setUsuarioResponsavelImportacao(Integer usuarioResponsavelImportacao) {
        this.usuarioResponsavelImportacao = usuarioResponsavelImportacao;
    }

    public String getChave() {
        if (chave == null) {
            chave = "";
        }
        return chave;
    }

    public void setChave(String chave) {
        this.chave = chave;
    }

    public TipoImportacaoEnum getTipoImportacaoEnum() {
        return tipoImportacaoEnum;
    }

    public void setTipoImportacaoEnum(TipoImportacaoEnum tipoImportacaoEnum) {
        this.tipoImportacaoEnum = tipoImportacaoEnum;
    }

    public IntegracaoMemberVO getIntegracaoMemberVO() {
        return integracaoMemberVO;
    }

    public void setIntegracaoMemberVO(IntegracaoMemberVO integracaoMemberVO) {
        this.integracaoMemberVO = integracaoMemberVO;
    }

    public String getIdsMembers() {
        return idsMembers;
    }

    public void setIdsMembers(String idsMembers) {
        this.idsMembers = idsMembers;
    }

    public TipoOperacaoIntegracaoMembersEnum getTipoOperacaoIntegracaoMembersEnum() {
        return tipoOperacaoIntegracaoMembersEnum;
    }

    public void setTipoOperacaoIntegracaoMembersEnum(TipoOperacaoIntegracaoMembersEnum tipoOperacaoIntegracaoMembersEnum) {
        this.tipoOperacaoIntegracaoMembersEnum = tipoOperacaoIntegracaoMembersEnum;
    }

    public String obterJSON(){
        try {
            JSONObject json = new JSONObject();
            json.put("chave", getChave());
            json.put("usuarioResponsavelImportacao", getUsuarioResponsavelImportacao());
            json.put("tipoImportacaoEnum", getTipoImportacaoEnum() == null ? "" : getTipoImportacaoEnum().name());

            JSONArray array = new JSONArray();
            for (String email : getListaEmails()) {
                array.put(email);
            }
            json.put("emails", array);
            return json.toString();
        } catch (Exception ex){
            ex.printStackTrace();
            return "";
        }
    }

    public Date getDataInicioProcesso() {
        return dataInicioProcesso;
    }

    public boolean isImportarProspects() {
        return importarProspects;
    }

    public void setImportarProspects(boolean importarProspects) {
        this.importarProspects = importarProspects;
    }

    public boolean isImportarVendas() {
        return importarVendas;
    }

    public void setImportarVendas(boolean importarVendas) {
        this.importarVendas = importarVendas;
    }

    public boolean isImportarReceivablesSemVendas() {
        return importarReceivablesSemVendas;
    }

    public void setImportarReceivablesSemVendas(boolean importarReceivablesSemVendas) {
        this.importarReceivablesSemVendas = importarReceivablesSemVendas;
    }

    public void setDataInicioProcesso(Date dataInicioProcesso) {
        this.dataInicioProcesso = dataInicioProcesso;
    }

    public boolean isSincronizarMembersImportados() {
        return sincronizarMembersImportados;
    }

    public void setSincronizarMembersImportados(boolean sincronizarMembersImportados) {
        this.sincronizarMembersImportados = sincronizarMembersImportados;
    }
}

package importador;

import br.com.pactosolucoes.enumeradores.TipoOperacaoLancamento;
import importador.financeiro.ImportarFornecedor;
import negocio.comuns.arquitetura.UsuarioVO;
import negocio.comuns.basico.EmpresaVO;
import negocio.comuns.basico.FornecedorVO;
import negocio.comuns.basico.enumerador.TimeZoneEnum;
import negocio.comuns.financeiro.CaixaVO;
import negocio.comuns.financeiro.CentroCustoTO;
import negocio.comuns.financeiro.ContaVO;
import negocio.comuns.financeiro.MovContaRateioVO;
import negocio.comuns.financeiro.MovContaVO;
import negocio.comuns.financeiro.PlanoContaTO;
import negocio.comuns.financeiro.enumerador.TipoES;
import negocio.comuns.utilitarias.Calendario;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisValidacao;
import negocio.facade.jdbc.arquitetura.SuperFacadeJDBC;
import negocio.facade.jdbc.arquitetura.Usuario;
import negocio.facade.jdbc.financeiro.Caixa;
import negocio.facade.jdbc.financeiro.MovConta;
import org.apache.poi.xssf.usermodel.XSSFRow;

import java.io.File;
import java.io.FileInputStream;
import java.sql.Connection;
import java.sql.DriverManager;
import java.sql.ResultSet;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * Created by Joao Alcides on 01/11/2016.
 */
public class ImportadorFinanceiroEvo {

    public static Integer EMPRESA = 1;
    public static Integer contaPadrao = 1;

    public static Map<String, Integer> mapaContas(){
        Map<String, Integer> contas = new HashMap<String, Integer>();
        //ADICIONE AQUI A CORRESPONDENCIA ENTRE CONTAS
        //"NOME DA CONTA NO INFO SKY", codigo da conta no ZW
        //contas.put("13003075-9", 10);
        //contas.put("FOM", 6);
        // contas.put("02065-9", 5);
        return contas;
    }

    public static Map<String, Integer> mapaCC(){
        Map<String, Integer> contas = new HashMap<String, Integer>();
        //ADICIONE AQUI A CORRESPONDENCIA ENTRE CENTRO DE CUSTOS
        //"CODIGO DO CENTRO DE CUSTOS NO INFO SKY", codigo do centro de custos no ZW
        //contas.put("1", 1);
        //contas.put("2", 11);
        return contas;
    }

    public static List<String> listFiles(String caminho ){
        File diretorio = new File(caminho);
        List<String> lista = Arrays.asList(diretorio.list());
        return lista;
    }

    public static void importarFinanceiroArquivoSimples(String arquivoContasPagar, String arquivoFornecedores, Connection con, Integer empresa) throws Exception{
        try {
            SuperFacadeJDBC.executarConsulta("ALTER TABLE movconta ADD COLUMN importado BOOLEAN DEFAULT FALSE;", con);
        }catch(Exception e){
        }
        try {
            SuperFacadeJDBC.executarConsulta("ALTER TABLE planoconta add column nomeconsulta text;", con);
        }catch(Exception e){
        }
        SuperFacadeJDBC.executarConsulta("update planoconta set nomeconsulta = remove_acento_upper(nome);", con);
        File fileContasPagar = new File(arquivoContasPagar);
        List<XSSFRow> hssfRows = LeitorExcel2010.lerLinhas(new FileInputStream(fileContasPagar));
        File arquivoFileFornecedores = new File(arquivoFornecedores);
        List<XSSFRow> linhasFornecedores = LeitorExcel2010.lerLinhas(new FileInputStream(arquivoFileFornecedores));
        System.out.println(arquivoContasPagar);

        String fornecedorPadrao = "Fornecedor Padrão Importação";
        Map<String, FornecedorVO> mapaFornecedores = ImportarFornecedor.obterFornecedoresExterno(linhasFornecedores, empresa, con);
        FornecedorVO fornecedorPadraoObj = ImportarFornecedor.consultarFornecedor(fornecedorPadrao, empresa, con);
        mapaFornecedores.put("0", fornecedorPadraoObj);

        Usuario userDao = new Usuario(con);
        Caixa caixaDao = new Caixa(con);
        MovConta movContaDao = new MovConta(con);
        List<UsuarioVO> list = userDao.consultarPorUsername("PACTOBR", false, Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA);
        Map<String, Integer> mapaContas = mapaContas();
        Map<String, Integer> mapaCC = mapaCC();

        if(!list.isEmpty()){
            CaixaVO caixaVO =  new CaixaVO();
            caixaVO.setUsuarioVo(list.get(0));
            caixaVO.setDataAbertura(Calendario.hoje());
            caixaVO.setDataFechamento(Calendario.hoje());
            caixaVO.setEmpresaVo(new EmpresaVO());
            caixaVO.getEmpresaVo().setCodigo(empresa);
            caixaDao.incluir(caixaVO);

            int total = hssfRows.size();
            int atual = 0;

            for(XSSFRow linha : hssfRows){
                System.out.println((++atual) + "/" + hssfRows.size() + " - Importando conta - Planilha: \"" + fileContasPagar.getName() + "\"");

                String isCancelado = LeitorExcel2010.obterString(linha, 11);
                if ("TRUE".equalsIgnoreCase(isCancelado)) {
                    continue;
                }

                String descricao = LeitorExcel2010.obterString(linha, 5);
                if ("".equals(descricao)) {
                    descricao = "SEM DESCRIÇÃO - IMPORTAÇÃO";
                }

                MovContaVO movContaVO = new MovContaVO();
                movContaVO.setEmpresaVO(new EmpresaVO());
                movContaVO.getEmpresaVO().setCodigo(empresa);
                Date dataVencimento = LeitorExcel2010.obterDataEspecifico(linha, 4);
                if (dataVencimento == null) {
                    String dataStr = LeitorExcel2010.obterString(linha, 4);
                    if (UteisValidacao.emptyString(dataStr) || dataStr.toUpperCase().equals("NULL")) {
                        throw new Exception("Data de vencimento não informada!");
                    }
                    dataVencimento = Calendario.getDate("yyyy-MM-dd", dataStr);
                }
                Date dataLancamento = LeitorExcel2010.obterDataEspecifico(linha, 10);
                if (dataLancamento == null) {
                    String dataStr = LeitorExcel2010.obterString(linha, 4);
                    if (!UteisValidacao.emptyString(dataStr) || dataStr.toUpperCase().equals("NULL")) {
                        dataLancamento = Calendario.getDate("yyyy-MM-dd", dataStr);
                    } else {
                        dataLancamento = dataVencimento;
                    }
                }
                movContaVO.setDataCompetencia(dataVencimento);
                movContaVO.setDataLancamento(dataLancamento);
                movContaVO.setDataVencimento(dataVencimento);

                if(!(LeitorExcel2010.obterDataEspecifico(linha, 13) == null)){
                    Date baixa = LeitorExcel2010.obterDataEspecifico(linha, 13);
                    if(Calendario.maiorOuIgual(Calendario.hoje(), baixa)){
                        movContaVO.setDataQuitacao(baixa);
                    }
                }

                String fornecedorId = LeitorExcel2010.obterStringDoNumero(linha, 36);
                FornecedorVO fornecedor = mapaFornecedores.get(fornecedorId);
                if(fornecedor == null){
                    fornecedor = fornecedorPadraoObj;
                }
                movContaVO.setPessoaVO(fornecedor.getPessoa());

                Double valor = LeitorExcel2010.obterNumero(linha, 6).doubleValue();
                movContaVO.setValor(valor < 0.0 ? valor * -1 : valor);

                if (valor.equals(0.0)) {
                    System.out.println(String.format("Conta com valor zerado não importada: %s", descricao));
                    continue;
                }

                movContaVO.setContaVO(new ContaVO());
                Integer conta = mapaContas.get(LeitorExcel2010.obterString(linha, 0));
                if(conta == null){
                    conta = contaPadrao;
                }
                movContaVO.getContaVO().setCodigo(conta);
                movContaVO.setDescricao(descricao);

                MovContaRateioVO movContaRateioVO = new MovContaRateioVO();
                movContaRateioVO.setDescricao(movContaVO.getDescricao());
                movContaRateioVO.setValor(movContaVO.getValor());
                movContaRateioVO.setTipoES(valor < 0.0 ? TipoES.ENTRADA : TipoES.SAIDA);

                String cc = LeitorExcel2010.obterStringDoNumero(linha, 3);
                Integer codigoCC = mapaCC.get(cc);
                if(!UteisValidacao.emptyNumber(codigoCC)){
                    movContaRateioVO.setCentroCustoVO(new CentroCustoTO());
                    movContaRateioVO.getCentroCustoVO().setCodigo(codigoCC);
                }

                Integer codigoPC = 0;
                if(!UteisValidacao.emptyNumber(codigoPC)){
                    movContaRateioVO.setPlanoContaVO(new PlanoContaTO());
                    movContaRateioVO.getPlanoContaVO().setCodigo(codigoPC);
                }

                StringBuilder obs = new StringBuilder();
                String observacao = LeitorExcel2010.obterString(linha, 8);
                if (!UteisValidacao.emptyString(observacao)) {
                    obs.append(observacao).append("; ");
                }
                String observacao2 = LeitorExcel2010.obterString(linha, 28);
                if (!UteisValidacao.emptyString(observacao)) {
                    obs.append("CHEQUE: ").append(observacao2);
                }
                movContaVO.setObservacoes(obs.toString());

                movContaVO.setMovContaRateios(new ArrayList<MovContaRateioVO>());
                movContaVO.getMovContaRateios().add(movContaRateioVO);
                movContaVO.setUsuarioVO(caixaVO.getUsuarioVo());
                movContaVO.setTipoOperacaoLancamento(valor < 0.0 ? TipoOperacaoLancamento.RECEBIMENTO : TipoOperacaoLancamento.PAGAMENTO);

                try {
                    movContaVO.setIdImportacao(fileContasPagar.getName() + "|LINHA_" + atual);
                } catch (Exception ex) {
                    System.out.println(descricao + " | Erro gerar idImportacao ignorado...");
                }

                if (existeImportada(movContaVO, con)) {
                    System.out.println("\nConta já importada!");
                    continue;
                }

                movContaDao.incluirSemCommit(movContaVO, 0, false, null);

                SuperFacadeJDBC.executarConsulta("UPDATE movconta SET idimportacao = '" + movContaVO.getIdImportacaoSalvar() + "', importado = TRUE, " +
                        "dataimportacao = '" + Calendario.getDataAplicandoFormatacao(Calendario.hoje(TimeZoneEnum.Brazil_GTM_3.getId()), "yyyy-MM-dd HH:mm:ss") + "' WHERE codigo = " + movContaVO.getCodigo(), con);
            }
        }
    }

    private static boolean existeImportada(MovContaVO movContaVO, Connection con) throws Exception {
        String sql = "SELECT EXISTS(select codigo from movconta where idimportacao ilike '" + movContaVO.getIdImportacaoSalvar() + "') as existe";
        ResultSet rs = SuperFacadeJDBC.criarConsulta(sql, con);
        if (rs.next()) {
            return rs.getBoolean("existe");
        }
        return false;
    }


    public static void main(String[] args){
        try {
            Connection con = DriverManager.getConnection("******************************************", "postgres", "pactodb");
            importarFinanceiroArquivoSimples("C:\\pacto\\backups\\contas_evo_teste\\contasapagar.xlsx",
                    "C:\\pacto\\backups\\contas_evo_teste\\fornecedores.xlsx", con, EMPRESA);
        }catch (Exception e){
            e.printStackTrace();
        }
    }
}

package importador;

import java.io.FileInputStream;
import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

import negocio.comuns.utilitarias.Uteis;

import org.apache.poi.hssf.usermodel.HSSFCell;
import org.apache.poi.hssf.usermodel.HSSFRow;
import org.apache.poi.hssf.usermodel.HSSFSheet;
import org.apache.poi.hssf.usermodel.HSSFWorkbook;

public class LeitorExcel {

	
	
	public static List<HSSFRow> lerLinhas(String caminho) throws Exception {
		return lerLinhas(caminho, 0);
	}

	public static List<HSSFRow> lerLinhas(String caminho, int idxSheet) throws Exception {
		List<HSSFRow> linhas = new ArrayList<HSSFRow>();
		// WorkBook, o arquivo planilha em si

		HSSFWorkbook wb;

		// Worksheet vai usar, aquelas abinhas que tem lá embaixo no Excel
		HSSFSheet sheet;
		// passa qual WorkBook vai ser usada, de acordo com o caminho
		wb = new HSSFWorkbook(new FileInputStream(caminho));
		// Passa qual WorkSheet vai ser usada. Nesse caso a primera (0)
		sheet = wb.getSheetAt(idxSheet);
		// Cria a linha de acordo com o parametro
		boolean continua = true;
		int i = 1;
		while (continua) {
			HSSFRow row = sheet.getRow(i);
			try{
				retirarNullExcell(row, 1);
				linhas.add(row);
				i++;
			}catch(Exception e) {
				continua = false;
				System.out.println("A leitura do arquivo Excel terminou na linha "+ (i + 1) + " do arquivo.");
			}
		}
		return linhas;
	}
	
	public static Number obterNumero(HSSFRow row, int coluna){
		HSSFCell cell = row.getCell((short) coluna);
		Number n = 0;
		try{
			n = cell.getNumericCellValue();	
		}catch (Exception e) {
//			e.printStackTrace();
		}
		return n;
		
	}
	public static String obterStringDoNumero(HSSFRow row, int coluna){
		HSSFCell cell = row.getCell((short) coluna);
		Number n = 0;
		try{
			n = cell.getNumericCellValue();	
		}catch (Exception e) {
//			e.printStackTrace();
		}
		return n == null ? "" : ""+n.intValue();
		
	}
	public static Date obterData(HSSFRow row, int coluna){
		HSSFCell cell = row.getCell((short) coluna);
		try{
			String value = cell.getStringCellValue();
			DateFormat formatter = new SimpleDateFormat("yyyy-MM-dd HH:mm");
			value = value.substring(0, 16);
			formatter.setLenient(false);	
			Date date = (Date) formatter.parse(value);
			return date;
		}catch (Exception e) {
			return null;
		}
		
	}

	public static Date obterDataNoFormatoData(HSSFRow row, int coluna){
		HSSFCell cell = row.getCell((short) coluna);
		try{
			return cell.getDateCellValue();
		}catch (Exception e) {
			return null;
		}

	}

	
	public static Date obterDataEspecifico(HSSFRow row, int coluna){
		HSSFCell cell = row.getCell((short) coluna);
		try{
			Date value = cell.getDateCellValue();
			return value;
		}catch (Exception e) {
			return null;
		}
		
	}
	
	public static String obterString(HSSFRow row, int coluna){
		HSSFCell cell = row.getCell((short) coluna);
		try{
			return cell.getStringCellValue();
		}catch (Exception e) {
			return "";
		}
		
	}
	@SuppressWarnings("deprecation")
	public static String retirarNullExcell(HSSFRow row, int coluna){
		HSSFCell cell = row.getCell((short) coluna);
		String valor = null;
		try{
			if(cell != null){
				 valor = cell.getStringCellValue();	
			}
			
		} catch (IllegalStateException e) {
			try{
				Date data = cell.getDateCellValue();
				valor = Uteis.getData(data);	
			} catch (IllegalStateException ex) {
				valor = String.valueOf(cell.getNumericCellValue());	
			}
			
		} catch (Exception e) {
//			e.printStackTrace();
		}
		
		if(valor == null || valor.equals("null") || valor.equals("")){
			return null;
		}else{
			return valor;
		}
			
		
	}
	
}

package importador.produtos;


import importador.ImportacaoCache;
import importador.ImportacaoConfigTO;
import importador.json.ProdutoImportacaoJSON;
import negocio.comuns.basico.ConfigExcelImportacaoTO;
import negocio.comuns.plano.CategoriaProdutoVO;
import negocio.comuns.plano.ProdutoVO;
import negocio.comuns.plano.enumerador.TipoProduto;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisValidacao;
import negocio.facade.jdbc.arquitetura.Log;
import negocio.facade.jdbc.arquitetura.SuperFacadeJDBC;
import negocio.facade.jdbc.plano.Produto;

import java.sql.Connection;

public class ImportarProduto {

    private Connection con;
    private ImportacaoConfigTO configTO;

    public ImportarProduto(ImportacaoConfigTO configTO, Connection con) {
        this.con = con;
        this.configTO = configTO;
    }

    public ProdutoVO importarProduto(ProdutoImportacaoJSON json, ConfigExcelImportacaoTO configExcelImportacaoTO, ImportacaoCache cache) throws Exception {
        try {
            con.setAutoCommit(false);


            Uteis.logar(true, null, "INICIA IMPORTAÇÃO | PRODUTO... " + json.getIdExterno() + " | " + json.getDescricao().toUpperCase());

            if (UteisValidacao.emptyNumber(json.getIdExterno())) {
                throw new Exception("Necessário que o PRODUTO tenha um IdExterno");
            }

            boolean existeIdExterno = SuperFacadeJDBC.existe("select codigo from produto where id_externo = '" + json.getIdExterno() + "'", con);
            if (existeIdExterno) {
                throw new Exception("Já existe um produto cadastrado com o Id_Externo \"" + json.getIdExterno() + "\"");
            }

            if (configExcelImportacaoTO.isValidarProdutoMesmoNome()) {
                Produto produtoDAO = new Produto(con);
                ProdutoVO produtoExiste = produtoDAO.consultarPorDescricao(json.getDescricao(), Uteis.NIVELMONTARDADOS_DADOSBASICOS);
                produtoDAO = null;
                if (produtoExiste != null && !UteisValidacao.emptyNumber(produtoExiste.getCodigo())) {
                    throw new Exception("Já existe um produto cadastrado com a descrição \"" + json.getDescricao() + "\"");
                }
            }

            ProdutoVO produtoVO = new ProdutoVO();
            produtoVO.setDescricao(json.getDescricao());
            produtoVO.setId_externo(json.getIdExterno());
            produtoVO.setValorFinal(json.getValor());
            produtoVO.setDesativado(!json.isAtivo());
            produtoVO.setCodigoBarras(json.getCodigoBarras());

            if (!UteisValidacao.emptyNumber(json.getVigenciaNrDias())) {
                produtoVO.setTipoVigencia("ID");
                produtoVO.setNrDiasVigencia(json.getVigenciaNrDias());
            }

            if (json.getVigenciaInicio() != null && json.getVigenciaFinal() != null) {
                produtoVO.setTipoVigencia("PF");
                produtoVO.setNrDiasVigencia(null);
                produtoVO.setDataInicioVigencia(json.getVigenciaInicio());
                produtoVO.setDataFinalVigencia(json.getVigenciaFinal());
            }
            produtoVO.setBloqueiaPelaVigencia(json.isBloquearAposVigencia());
            produtoVO.setObservacao(json.getObservacao());


            CategoriaProdutoVO categoriaVO = new CategoriaProdutoVO();
            if (!UteisValidacao.emptyString(json.getCategoria().trim())) {
                categoriaVO = cache.obterCategoriaProdutoVO_Descricao(json.getCategoria().trim());
            }

            if (categoriaVO == null || UteisValidacao.emptyNumber(categoriaVO.getCodigo())) {
                throw new Exception("Categoria não informada.");
            }
            produtoVO.setCategoriaProduto(categoriaVO);


            TipoProduto tipo = cache.obterTipoProduto(json.getTipo());
            if (tipo == null) {
                throw new Exception("Tipo de Produto não encontrado.");
            }

            produtoVO.setTipoProduto(tipo.getCodigo());

            Produto produtoDAO = new Produto(con);
            produtoDAO.incluir(produtoVO);
            produtoDAO = null;

            if (UteisValidacao.emptyNumber(produtoVO.getCodigo())) {
                throw new Exception("Produto não importado.");
            }

            Log logDao = new Log(con);
            logDao.incluirLogItemImportacao("PRODUTO", produtoVO.getCodigo(), cache.getUsuarioVOImportacao(), 0);
            logDao = null;

            if (!UteisValidacao.emptyNumber(produtoVO.getCodigo())) {
                json.setSucesso(true);
                json.setCodigo(produtoVO.getCodigo());
                json.setMsgRetorno("Produto importado com sucesso.");
            } else {
                throw new Exception("Produto não importado.");
            }

            con.commit();
            return produtoVO;
        } catch (Exception ex) {
            json.setSucesso(false);
            json.setCodigo(null);
            json.setMsgRetorno(ex.getMessage());
            ex.printStackTrace();
            con.rollback();
            throw ex;
        } finally {
            con.setAutoCommit(true);
        }
    }

    private void incluirLogInclusao(ProdutoVO produtoVO, ImportacaoCache cache) throws Exception {
        Log logDAO = new Log(con);
        produtoVO.setObjetoVOAntesAlteracao(new ProdutoVO());
        produtoVO.setNovoObj(true);
        logDAO.registrarLogObjetoVO(produtoVO, cache.getUsuarioVOImportacao(), produtoVO.getCodigo().toString(), "PRODUTO", 0, true);
        logDAO = null;
    }

    public Connection getCon() {
        return con;
    }

    public void setCon(Connection con) {
        this.con = con;
    }

    public ImportacaoConfigTO getConfigTO() {
        return configTO;
    }

    public void setConfigTO(ImportacaoConfigTO configTO) {
        this.configTO = configTO;
    }
}

package importador;

import negocio.comuns.arquitetura.PerfilAcessoVO;
import negocio.comuns.arquitetura.UsuarioVO;
import negocio.comuns.basico.*;
import negocio.comuns.contrato.JustificativaOperacaoVO;
import negocio.comuns.financeiro.AdquirenteVO;
import negocio.comuns.financeiro.BancoVO;
import negocio.comuns.financeiro.CentroCustoTO;
import negocio.comuns.financeiro.FormaPagamentoVO;
import negocio.comuns.financeiro.OperadoraCartaoVO;
import negocio.comuns.financeiro.PlanoContaTO;
import negocio.comuns.financeiro.enumerador.TipoFormaPagto;
import negocio.comuns.plano.CategoriaProdutoVO;
import negocio.comuns.plano.HorarioVO;
import negocio.comuns.plano.ModalidadeVO;
import negocio.comuns.plano.PlanoCondicaoPagamentoVO;
import negocio.comuns.plano.PlanoDuracaoVO;
import negocio.comuns.plano.PlanoVO;
import negocio.comuns.plano.ProdutoVO;
import negocio.comuns.plano.enumerador.TipoProduto;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisValidacao;
import negocio.facade.jdbc.arquitetura.PerfilAcesso;
import negocio.facade.jdbc.arquitetura.SuperFacadeJDBC;
import negocio.facade.jdbc.arquitetura.Usuario;
import negocio.facade.jdbc.basico.*;
import negocio.facade.jdbc.financeiro.Adquirente;
import negocio.facade.jdbc.financeiro.Banco;
import negocio.facade.jdbc.financeiro.CentroCusto;
import negocio.facade.jdbc.financeiro.FormaPagamento;
import negocio.facade.jdbc.financeiro.OperadoraCartao;
import negocio.facade.jdbc.financeiro.PlanoConta;
import negocio.facade.jdbc.plano.CategoriaProduto;
import negocio.facade.jdbc.plano.Horario;
import negocio.facade.jdbc.plano.Modalidade;
import negocio.facade.jdbc.plano.Plano;
import negocio.facade.jdbc.plano.PlanoCondicaoPagamento;
import negocio.facade.jdbc.plano.PlanoDuracao;
import negocio.facade.jdbc.plano.Produto;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * Created by Luiz Felipe on 20/12/2019.
 */
public class ImportacaoCache {

    private Connection con;

    private Map<Integer, PlanoVO> mapaPlanoVO;
    private Map<String, PlanoDuracaoVO> mapaPlanoDuracaoVO;
    private Map<String, List<PlanoCondicaoPagamentoVO>> mapaPlanoCondicoesPagamento;
    private Map<Integer, ModalidadeVO> mapaModalidadeVO;
    private Map<Integer, UsuarioVO> mapaUsuarioVO;
    private Map<Integer, EmpresaVO> mapaEmpresaVO;
    private Map<Integer, ColaboradorVO> mapaColaboradorVO;
    private Map<String, EstadoVO> mapaEstadoVO;
    private Map<Integer, HorarioVO> mapaHorarioVO;
    private Map<Integer, CidadeVO> mapaCidadeVO;
    private Map<Integer, CategoriaProdutoVO> mapaCategoriaProdutoVO;
    private List<PerfilAcessoVO> listaPerfilAcessoVO;
    private List<PlanoContaTO> listaPlanoContaTO;
    private List<CentroCustoTO> listaCentroCustoTO;
    private ConfiguracaoSistemaVO configuracaoSistemaVO;
    private UsuarioVO usuarioVOImportacao;
    private Map<Integer, ColaboradorVO> mapaEmpresaColaboradorPactoBR; //mapa com o codigo da empresa e o colaborador PACTOBR daquela empresa.
    private Map<Integer, FormaPagamentoVO> mapaFormaPagamento;
    private Map<String, AdquirenteVO> mapaAdquirente;
    private Map<String, OperadoraCartaoVO> mapaOperadoraCartaoCredito;
    private Map<String, OperadoraCartaoVO> mapaOperadoraCartaoDebito;
    private Map<String, BancoVO> mapaBanco;
    private Map<String, PlanoVO> mapaPlanoCorrespondencia;
    private Map<Integer, PlanoVO> mapaPlanoIdExterno;
    private Map<String, ProdutoVO> mapaProdutos;
    private Map<String, ParentescoVO> mapaParentesco;
    private Map<String, JustificativaOperacaoVO> mapaJustificativaOperacao;
    private Map<Integer, Integer> contratosImportados;

    public ImportacaoCache(Connection con, Integer usuarioImportacao) throws Exception {
        this.con = con;
        setUsuarioVOImportacao(obterUsuarioVO(usuarioImportacao));
        carregarPlanoCorrespondencia();
    }

    private void carregarPlanoCorrespondencia() throws Exception {
        Plano planoDAO = new Plano(con);
        PreparedStatement stm = con.prepareStatement("SELECT codigo, correspondencia_zd FROM plano \n " +
                " WHERE correspondencia_zd is not null \n " +
                " AND planopersonal is false \n" +
                " AND vendacreditotreino is false \n");

        ResultSet rs = stm.executeQuery();
        while(rs.next()){
            try{
                PlanoVO consultarPorChavePrimaria = planoDAO.consultarPorChavePrimaria(rs.getInt("codigo"), false,
                        Uteis.NIVELMONTARDADOS_TODOS);
                String correspondencias = rs.getString("correspondencia_zd");
                String[] cods = correspondencias.split(";");
                for(String cod : cods){
                    getMapaPlanoCorrespondencia().put(cod, consultarPorChavePrimaria);
                }
            }catch (Exception e) {
                e.printStackTrace();
            }
        }
    }

    private Map<Integer, PlanoVO> getMapaPlanoVO() {
        if (mapaPlanoVO == null) {
            mapaPlanoVO = new HashMap<>();
        }
        return mapaPlanoVO;
    }

    public void setMapaPlanoVO(Map<Integer, PlanoVO> mapaPlanoVO) {
        this.mapaPlanoVO = mapaPlanoVO;
    }

    public Map<Integer, PlanoVO> getMapaPlanoIdExterno() {
        if (mapaPlanoIdExterno == null) {
            mapaPlanoIdExterno = new HashMap<>();
        }
        return mapaPlanoIdExterno;
    }

    private Map<Integer, UsuarioVO> getMapaUsuarioVO() {
        if (mapaUsuarioVO == null) {
            mapaUsuarioVO = new HashMap<>();
        }
        return mapaUsuarioVO;
    }

    public void setMapaUsuarioVO(Map<Integer, UsuarioVO> mapaUsuarioVO) {
        this.mapaUsuarioVO = mapaUsuarioVO;
    }

    private Map<Integer, EmpresaVO> getMapaEmpresaVO() {
        if (mapaEmpresaVO == null) {
            mapaEmpresaVO = new HashMap<>();
        }
        return mapaEmpresaVO;
    }

    public void setMapaEmpresaVO(Map<Integer, EmpresaVO> mapaEmpresaVO) {
        this.mapaEmpresaVO = mapaEmpresaVO;
    }

    private Map<Integer, ColaboradorVO> getMapaColaboradorVO() {
        if (mapaColaboradorVO == null) {
            mapaColaboradorVO = new HashMap<>();
        }
        return mapaColaboradorVO;
    }

    public void setMapaColaboradorVO(Map<Integer, ColaboradorVO> mapaColaboradorVO) {
        this.mapaColaboradorVO = mapaColaboradorVO;
    }

    private Map<Integer, ModalidadeVO> getMapaModalidadeVO() {
        if (mapaModalidadeVO == null) {
            mapaModalidadeVO = new HashMap<>();
        }
        return mapaModalidadeVO;
    }

    public void setMapaModalidadeVO(Map<Integer, ModalidadeVO> mapaModalidadeVO) {
        this.mapaModalidadeVO = mapaModalidadeVO;
    }


    private Map<String, EstadoVO> getMapaEstadoVO() {
        if (mapaEstadoVO == null) {
            mapaEstadoVO = new HashMap<>();
        }
        return mapaEstadoVO;
    }

    public void setMapaEstadoVO(Map<String, EstadoVO> mapaEstadoVO) {
        this.mapaEstadoVO = mapaEstadoVO;
    }

    public EmpresaVO obterEmpresaVO(Integer empresa) throws Exception {
        EmpresaVO empresaVO = getMapaEmpresaVO().get(empresa);
        if (empresaVO == null) {
            Empresa dao = new Empresa(con);
            empresaVO = dao.consultarPorChavePrimaria(empresa, Uteis.NIVELMONTARDADOS_TODOS);
            dao = null;
        }

        if (empresaVO != null && !UteisValidacao.emptyNumber(empresaVO.getCodigo())) {
            getMapaEmpresaVO().put(empresaVO.getCodigo(), empresaVO);
            return empresaVO;
        } else {
            throw new Exception("Não foi encontrado EMPRESA com código " + empresa);
        }
    }

    public PlanoVO obterPlanoVO(Integer plano) throws Exception {
        PlanoVO planoVO = getMapaPlanoVO().get(plano);
        if (planoVO == null) {
            Plano dao = new Plano(con);
            planoVO = dao.consultarPorChavePrimaria(plano, Uteis.NIVELMONTARDADOS_TODOS);
            dao = null;
        } else {
            return planoVO;
        }

        if (planoVO != null && !UteisValidacao.emptyNumber(planoVO.getCodigo())) {
            getMapaPlanoVO().put(planoVO.getCodigo(), planoVO);
            return planoVO;
        } else {
            throw new Exception("Não foi encontrado PLANO com código " + plano);
        }
    }

    public PlanoVO obterPlanoPorIdExterno(Integer idExterno, Integer codigoEmpresa) throws Exception {
        if (UteisValidacao.emptyNumber(idExterno)) {
            return null;
        }
        PlanoVO planoVO = getMapaPlanoIdExterno().get(idExterno);
        if (planoVO == null) {
            Plano dao = new Plano(con);
            planoVO = dao.findByIdExterno(idExterno.toString(), codigoEmpresa, Uteis.NIVELMONTARDADOS_TODOS);
            getMapaPlanoIdExterno().put(planoVO.getCodigo(), planoVO);
            dao = null;
        }
        return planoVO;
    }

    public Map<String, ParentescoVO> getMapaParentesco() {
        if (mapaParentesco == null) {
            mapaParentesco = new HashMap<>();
        }
        return mapaParentesco;
    }

    public Map<String, JustificativaOperacaoVO> getMapaJustificativaOperacao() {
        if (mapaJustificativaOperacao == null) {
            mapaJustificativaOperacao = new HashMap<>();
        }
        return mapaJustificativaOperacao;
    }

    public JustificativaOperacaoVO obterJustificativaOperacao(Integer codigoEmpresa, String descricao, String tipoOperacao) throws Exception {
        JustificativaOperacaoVO justificativaOperacaoVO = getMapaJustificativaOperacao().get(descricao);
        if (justificativaOperacaoVO == null) {
            justificativaOperacaoVO = UteisImportacao.obterJustificativaOperacao(codigoEmpresa, descricao, tipoOperacao, con);
        } else {
            return justificativaOperacaoVO;
        }

        if (justificativaOperacaoVO != null && !UteisValidacao.emptyNumber(justificativaOperacaoVO.getCodigo())) {
            getMapaJustificativaOperacao().put(descricao, justificativaOperacaoVO);
            return justificativaOperacaoVO;
        } else {
            throw new Exception("Falha ao obter justificativa operação: " + descricao);
        }
    }

    public ParentescoVO obterParentesco(String descricao) throws Exception {
        ParentescoVO parentescoVO = getMapaParentesco().get(descricao);
        if (parentescoVO == null) {
            Parentesco parentescoDAO = new Parentesco(con);
            parentescoVO = parentescoDAO.obterParentescoCriandoSeNaoExiste(descricao);
            parentescoDAO = null;
        } else {
            return parentescoVO;
        }

        if (parentescoVO != null && !UteisValidacao.emptyNumber(parentescoVO.getCodigo())) {
            getMapaParentesco().put(descricao, parentescoVO);
            return parentescoVO;
        } else {
            throw new Exception("Falha ao obter parentesco: " + descricao);
        }
    }

    public UsuarioVO obterUsuarioVO(Integer usuario) throws Exception {
        UsuarioVO usuarioVO = getMapaUsuarioVO().get(usuario);
        if (usuarioVO == null) {
            Usuario dao = new Usuario(con);
            usuarioVO = dao.consultarPorChavePrimaria(usuario, Uteis.NIVELMONTARDADOS_TODOS);
            dao = null;
        } else {
            return usuarioVO;
        }

        if (usuarioVO != null && !UteisValidacao.emptyNumber(usuarioVO.getCodigo())) {
            getMapaUsuarioVO().put(usuarioVO.getCodigo(), usuarioVO);
            return usuarioVO;
        } else {
            throw new Exception("Não foi encontrado USUÁRIO com código " + usuario);
        }
    }

    public ColaboradorVO obterColaboradorVO(Integer colaborador) throws Exception {
        ColaboradorVO colaboradorVO = getMapaColaboradorVO().get(colaborador);
        if (colaboradorVO == null) {
            Colaborador dao = new Colaborador(con);
            colaboradorVO = dao.consultarPorChavePrimaria(colaborador, Uteis.NIVELMONTARDADOS_TODOS);
            dao = null;
        } else {
            return colaboradorVO;
        }

        if (colaboradorVO != null && !UteisValidacao.emptyNumber(colaboradorVO.getCodigo())) {
            getMapaColaboradorVO().put(colaboradorVO.getCodigo(), colaboradorVO);
            return colaboradorVO;
        } else {
            throw new Exception("Não foi encontrado COLABORADOR com código " + colaborador);
        }
    }

    public ModalidadeVO obterModalidadeVO(Integer modalidade) throws Exception {
        ModalidadeVO modalidadeVO = getMapaModalidadeVO().get(modalidade);
        if (modalidadeVO == null) {
            Modalidade dao = new Modalidade(con);
            modalidadeVO = dao.consultarPorChavePrimaria(modalidade, Uteis.NIVELMONTARDADOS_TODOS);
            dao = null;
        } else {
            return modalidadeVO;
        }

        if (modalidadeVO != null && !UteisValidacao.emptyNumber(modalidadeVO.getCodigo())) {
            getMapaModalidadeVO().put(modalidadeVO.getCodigo(), modalidadeVO);
            return modalidadeVO;
        } else {
            throw new Exception("Não foi encontrado MODALIDADE com código " + modalidade);
        }
    }

    public EstadoVO obterEstadoVO(String uf) throws Exception {
        EstadoVO estadoVO = getMapaEstadoVO().get(uf);
        if (estadoVO == null) {
            Estado dao = new Estado(con);
            estadoVO = dao.consultarPorSiglaUf(uf, false, Uteis.NIVELMONTARDADOS_TODOS);
            dao = null;
        } else {
            return estadoVO;
        }

        if (estadoVO != null && !UteisValidacao.emptyNumber(estadoVO.getCodigo())) {
            getMapaEstadoVO().put(estadoVO.getSigla(), estadoVO);
            return estadoVO;
        } else {
            throw new Exception("Não foi encontrado ESTADO com a sigla " + uf);
        }
    }

    public HorarioVO obterHorarioVO(Integer horario) throws Exception {
        HorarioVO horarioVO = getMapaHorarioVO().get(horario);
        if (horarioVO == null) {
            Horario dao = new Horario(con);
            horarioVO = dao.consultarPorChavePrimaria(horario, Uteis.NIVELMONTARDADOS_TODOS);
            dao = null;
        } else {
            return horarioVO;
        }

        if (horarioVO != null && !UteisValidacao.emptyNumber(horarioVO.getCodigo())) {
            getMapaHorarioVO().put(horarioVO.getCodigo(), horarioVO);
            return horarioVO;
        } else {
            throw new Exception("Não foi encontrado HORARIO com código " + horario);
        }
    }

    public Map<Integer, HorarioVO> getMapaHorarioVO() {
        if (mapaHorarioVO == null) {
            mapaHorarioVO = new HashMap<>();
        }
        return mapaHorarioVO;
    }

    public void setMapaHorarioVO(Map<Integer, HorarioVO> mapaHorarioVO) {
        this.mapaHorarioVO = mapaHorarioVO;
    }

    public Map<Integer, CidadeVO> getMapaCidadeVO() {
        if (mapaCidadeVO == null) {
            mapaCidadeVO = new HashMap<>();
        }
        return mapaCidadeVO;
    }

    public void setMapaCidadeVO(Map<Integer, CidadeVO> mapaCidadeVO) {
        this.mapaCidadeVO = mapaCidadeVO;
    }

    public CidadeVO obterCidadeVO(Integer cidade) throws Exception {
        CidadeVO cidadeVO = getMapaCidadeVO().get(cidade);
        if (cidadeVO == null) {
            Cidade dao = new Cidade(con);
            cidadeVO = dao.consultarPorChavePrimaria(cidade, Uteis.NIVELMONTARDADOS_TODOS);
            dao = null;
        } else {
            return cidadeVO;
        }

        if (cidadeVO != null && !UteisValidacao.emptyNumber(cidadeVO.getCodigo())) {
            getMapaCidadeVO().put(cidadeVO.getCodigo(), cidadeVO);
            return cidadeVO;
        } else {
            throw new Exception("Não foi encontrado CIDADE com código " + cidade);
        }
    }

    public CategoriaProdutoVO obterCategoriaProdutoVO_Descricao(String descricaoCategoriaProduto) throws Exception {
        CategoriaProduto dao = new CategoriaProduto(con);
        CategoriaProdutoVO categoriaProdutoVO = dao.criarCategoriaOuConsultarSeExistePorDescricao(descricaoCategoriaProduto);
        dao = null;

        if (categoriaProdutoVO != null && !UteisValidacao.emptyNumber(categoriaProdutoVO.getCodigo())) {
            getMapaCategoriaProdutoVO().put(categoriaProdutoVO.getCodigo(), categoriaProdutoVO);
        }
        return categoriaProdutoVO;
    }

    public CategoriaProdutoVO obterCategoriaProdutoVO_Codigo(Integer categoriaProduto) throws Exception {
        CategoriaProdutoVO categoriaProdutoVO = getMapaCategoriaProdutoVO().get(categoriaProduto);
        if (categoriaProdutoVO == null) {
            CategoriaProduto dao = new CategoriaProduto(con);
            categoriaProdutoVO = dao.consultarPorChavePrimaria(categoriaProduto, Uteis.NIVELMONTARDADOS_TODOS);
            dao = null;
        } else {
            return categoriaProdutoVO;
        }

        if (categoriaProdutoVO != null && !UteisValidacao.emptyNumber(categoriaProdutoVO.getCodigo())) {
            getMapaCategoriaProdutoVO().put(categoriaProdutoVO.getCodigo(), categoriaProdutoVO);
            return categoriaProdutoVO;
        } else {
            throw new Exception("Não foi encontrado CATEGORIA PRODUTO com código " + categoriaProduto);
        }
    }

    public Map<Integer, CategoriaProdutoVO> getMapaCategoriaProdutoVO() {
        if (mapaCategoriaProdutoVO == null) {
            mapaCategoriaProdutoVO = new HashMap<>();
        }
        return mapaCategoriaProdutoVO;
    }

    public void setMapaCategoriaProdutoVO(Map<Integer, CategoriaProdutoVO> mapaCategoriaProdutoVO) {
        this.mapaCategoriaProdutoVO = mapaCategoriaProdutoVO;
    }

    public List<PerfilAcessoVO> getListaPerfilAcessoVO() {
        return listaPerfilAcessoVO;
    }

    public void setListaPerfilAcessoVO(List<PerfilAcessoVO> listaPerfilAcessoVO) {
        this.listaPerfilAcessoVO = listaPerfilAcessoVO;
    }

    public List<PerfilAcessoVO> obterListaPerfilAcesso() throws Exception {
        if (UteisValidacao.emptyList(getListaPerfilAcessoVO())) {
            PerfilAcesso dao = new PerfilAcesso(con);
            setListaPerfilAcessoVO(dao.consultarPorNome("", false, Uteis.NIVELMONTARDADOS_DADOSBASICOS));
            dao = null;
        }
        return getListaPerfilAcessoVO();
    }

    public List<PlanoContaTO> getListaPlanoContaTO() {
        if (listaPlanoContaTO == null) {
            listaPlanoContaTO = new ArrayList<>();
        }
        return listaPlanoContaTO;
    }

    public void setListaPlanoContaTO(List<PlanoContaTO> listaPlanoContaTO) {
        this.listaPlanoContaTO = listaPlanoContaTO;
    }

    public List<CentroCustoTO> getListaCentroCustoTO() {
        if (listaCentroCustoTO == null) {
            listaCentroCustoTO = new ArrayList<>();
        }
        return listaCentroCustoTO;
    }

    public void setListaCentroCustoTO(List<CentroCustoTO> listaCentroCustoTO) {
        this.listaCentroCustoTO = listaCentroCustoTO;
    }

    private void povoarPlanoConta() throws Exception {
        if (UteisValidacao.emptyList(getListaPlanoContaTO())) {
            PlanoConta dao = new PlanoConta(con);
            setListaPlanoContaTO(dao.consultarTodos());
            dao = null;
        }
    }

    public PlanoContaTO obterPlanoContaTODescricao(String planoConta) throws Exception {
        povoarPlanoConta();
        for (PlanoContaTO plano : getListaPlanoContaTO()) {
            if (plano.getDescricao().toUpperCase().contains(planoConta.toUpperCase())) {
                return plano;
            }
        }
        return new PlanoContaTO();
    }

    public PlanoContaTO obterPlanoContaTOCodigo(Integer planoConta) throws Exception {
        povoarPlanoConta();
        for (PlanoContaTO plano : getListaPlanoContaTO()) {
            if (plano.getCodigo().equals(planoConta)) {
                return plano;
            }
        }
        return new PlanoContaTO();
    }

    private void povoarCentroCusto() throws Exception {
        if (UteisValidacao.emptyList(getListaCentroCustoTO())) {
            CentroCusto dao = new CentroCusto(con);
            setListaCentroCustoTO(dao.consultarTodos());
            dao = null;
        }
    }

    public CentroCustoTO obterCentroCustoTODescricao(String centroCusto) throws Exception {
        povoarCentroCusto();
        for (CentroCustoTO centro : getListaCentroCustoTO()) {
            if (centro.getDescricao().toUpperCase().contains(centroCusto.toUpperCase())) {
                return centro;
            }
        }
        return new CentroCustoTO();
    }

    public CentroCustoTO obterCentroCustoTOCodigo(Integer centroCusto) throws Exception {
        povoarCentroCusto();
        for (CentroCustoTO centro : getListaCentroCustoTO()) {
            if (centro.getCodigo().equals(centroCusto)) {
                return centro;
            }
        }
        return new CentroCustoTO();
    }

    public ConfiguracaoSistemaVO getConfiguracaoSistemaVO() throws Exception {
        if (configuracaoSistemaVO == null || UteisValidacao.emptyNumber(configuracaoSistemaVO.getCodigo())) {
            ConfiguracaoSistema configuracaoSistemaDAO = new ConfiguracaoSistema(con);
            configuracaoSistemaVO = configuracaoSistemaDAO.consultarConfigs(Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA);
            configuracaoSistemaDAO = null;
        }
        return configuracaoSistemaVO;
    }

    public Connection getCon() {
        return con;
    }

    public void setCon(Connection con) {
        this.con = con;
    }

    public TipoProduto obterTipoProduto(String descricaoTipo) {
        TipoProduto tipoProduto = null;

        //buscar pela descricao
        if (descricaoTipo != null && !UteisValidacao.emptyString(descricaoTipo.trim())) {
            tipoProduto = TipoProduto.retornaPorDescricaoImportacao(descricaoTipo.trim());
        }

        //buscar pela descricao
        if (tipoProduto == null) {
            tipoProduto = TipoProduto.getTipoProdutoCodigo(descricaoTipo);
        }
        return tipoProduto;
    }

    public UsuarioVO getUsuarioVOImportacao() {
        if (usuarioVOImportacao == null) {
            usuarioVOImportacao = new UsuarioVO();
        }
        return usuarioVOImportacao;
    }

    public void setUsuarioVOImportacao(UsuarioVO usuarioVOImportacao) {
        this.usuarioVOImportacao = usuarioVOImportacao;
    }

    public ColaboradorVO obterColaboradorPactoBREmpresa(Integer empresa) throws Exception {
        ColaboradorVO colaboradorVO = getMapaEmpresaColaboradorPactoBR().get(empresa);
        if (colaboradorVO == null) {
            Colaborador dao = new Colaborador(con);
            colaboradorVO = dao.consultarPorNomeColaborador("PACTO - M", empresa, Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA);
            dao = null;
        } else {
            return colaboradorVO;
        }

        if (colaboradorVO != null && !UteisValidacao.emptyNumber(colaboradorVO.getCodigo())) {
            getMapaEmpresaColaboradorPactoBR().put(empresa, colaboradorVO);
            return colaboradorVO;
        } else {
            throw new Exception("Não foi encontrado COLABORADOR PACTOBR na empresa com código " + empresa);
        }
    }

    public Map<Integer, ColaboradorVO> getMapaEmpresaColaboradorPactoBR() {
        if (mapaEmpresaColaboradorPactoBR == null) {
            mapaEmpresaColaboradorPactoBR = new HashMap<>();
        }
        return mapaEmpresaColaboradorPactoBR;
    }

    public void setMapaEmpresaColaboradorPactoBR(Map<Integer, ColaboradorVO> mapaEmpresaColaboradorPactoBR) {
        this.mapaEmpresaColaboradorPactoBR = mapaEmpresaColaboradorPactoBR;
    }

    public Map<Integer, FormaPagamentoVO> getMapaFormaPagamento() {
        if (mapaFormaPagamento == null) {
            mapaFormaPagamento = new HashMap<>();
        }
        return mapaFormaPagamento;
    }

    public void setMapaFormaPagamento(Map<Integer, FormaPagamentoVO> mapaFormaPagamento) {
        this.mapaFormaPagamento = mapaFormaPagamento;
    }

    public FormaPagamentoVO obterFormaPagamento(TipoFormaPagto tipoFormaPagto) throws Exception {
        FormaPagamentoVO formaPagamentoVO = getMapaFormaPagamento().get(tipoFormaPagto.getCodigo());
        if (formaPagamentoVO == null) {
            FormaPagamento formaPagamentoDAO = new FormaPagamento(con);
            formaPagamentoVO = formaPagamentoDAO.obterFormaPagamentoRapido(tipoFormaPagto);
            if (formaPagamentoVO == null || UteisValidacao.emptyNumber(formaPagamentoVO.getCodigo())) {
                formaPagamentoVO.setDescricao(String.format("%s (IMPORTACAO)", tipoFormaPagto.getDescricao()));
                formaPagamentoVO.setTipoFormaPagamento(tipoFormaPagto.getSigla());
                formaPagamentoVO.setAtivo(true);
                formaPagamentoDAO.incluir(formaPagamentoVO);
            }
            getMapaFormaPagamento().put(tipoFormaPagto.getCodigo(), formaPagamentoVO);
            formaPagamentoDAO = null;
        }
        return formaPagamentoVO;
    }

    public Map<String, AdquirenteVO> getMapaAdquirente() {
        if (mapaAdquirente == null) {
            mapaAdquirente = new HashMap<>();
        }
        return mapaAdquirente;
    }

    public void setMapaAdquirente(Map<String, AdquirenteVO> mapaAdquirente) {
        this.mapaAdquirente = mapaAdquirente;
    }

    public AdquirenteVO obterAdquirente(String descricao) throws Exception {
        if (UteisValidacao.emptyString(descricao)) {
            return null;
        }
        descricao = descricao.trim().toUpperCase();
        AdquirenteVO adquirenteVO = getMapaAdquirente().get(descricao);
        if (adquirenteVO == null) {
            Adquirente adquirenteDAO = new Adquirente(con);
            adquirenteVO = adquirenteDAO.consultarOuCriaSeNaoExistir(descricao);
            getMapaAdquirente().put(descricao, adquirenteVO);
        }
        return adquirenteVO;
    }

    public Map<String, OperadoraCartaoVO> getMapaOperadoraCartaoCredito() {
        if (mapaOperadoraCartaoCredito == null) {
            mapaOperadoraCartaoCredito = new HashMap<>();
        }
        return mapaOperadoraCartaoCredito;
    }

    public void setMapaOperadoraCartaoCredito(Map<String, OperadoraCartaoVO> mapaOperadoraCartaoCredito) {
        this.mapaOperadoraCartaoCredito = mapaOperadoraCartaoCredito;
    }

    public OperadoraCartaoVO obterOperadora(String descricao, boolean credito) throws Exception {
        if (UteisValidacao.emptyString(descricao)) {
            return null;
        }
        Map<String, OperadoraCartaoVO> mapOperadoraCartao = credito ? getMapaOperadoraCartaoCredito() : getMapaOperadoraCartaoDebito();
        descricao = descricao.trim().toUpperCase();
        OperadoraCartaoVO operadoraCartaoVO = mapOperadoraCartao.get(descricao);
        if (operadoraCartaoVO == null) {
            OperadoraCartao operadoraCartaoDAO = new OperadoraCartao(con);
            operadoraCartaoVO = operadoraCartaoDAO.consultarOuCriar(descricao, 999, credito, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
            mapOperadoraCartao.put(descricao, operadoraCartaoVO);
        }
        return operadoraCartaoVO;
    }

    public Map<String, OperadoraCartaoVO> getMapaOperadoraCartaoDebito() {
        if (mapaOperadoraCartaoDebito == null) {
            mapaOperadoraCartaoDebito = new HashMap<>();
        }
        return mapaOperadoraCartaoDebito;
    }

    public void setMapaOperadoraCartaoDebito(Map<String, OperadoraCartaoVO> mapaOperadoraCartaoDebito) {
        this.mapaOperadoraCartaoDebito = mapaOperadoraCartaoDebito;
    }

    public Map<String, BancoVO> getMapaBanco() {
        if (mapaBanco == null) {
            mapaBanco = new HashMap<>();
        }
        return mapaBanco;
    }

    public Map<String, PlanoVO> getMapaPlanoCorrespondencia() {
        if (mapaPlanoCorrespondencia == null) {
            mapaPlanoCorrespondencia = new HashMap<>();
        }
        return mapaPlanoCorrespondencia;
    }

    public Map<String, ProdutoVO> getMapaProdutos() {
        if (mapaProdutos == null) {
            mapaProdutos = new HashMap<>();
        }
        return mapaProdutos;
    }

    public BancoVO obterBanco(String nome) throws Exception {
        if(UteisValidacao.emptyString(nome)) {
            return null;
        }
        nome = nome.trim().toUpperCase();
        Banco bancoDAO = new Banco(con);
        BancoVO bancoVO = getMapaBanco().get(nome);
        if (bancoVO == null) {
            bancoVO = new BancoVO(null, null, nome);
            bancoVO = bancoDAO.criarOuConsultarSeExistePorNome(bancoVO);
            getMapaBanco().put(nome, bancoVO);
        }
        return bancoVO;
    }

    public ProdutoVO obterProdutoVendaAvulsa(String descricao, TipoProduto tipoProduto) throws Exception {
        if (UteisValidacao.emptyString(descricao) || tipoProduto == null) {
            return null;
        }
        String key = tipoProduto.getCodigo() + "_" +descricao;
        descricao = descricao.length() > 50 ? descricao.substring(0, 50) : descricao;
        ProdutoVO produtoVO = getMapaProdutos().get(key);
        if (produtoVO == null) {
            Produto produtoDAO = new Produto(con);
            produtoVO = produtoDAO.criarOuConsultarExisteProdutoPorTipo(descricao, tipoProduto.getCodigo(), 0.0);
            getMapaProdutos().put(key, produtoVO);
        }
        return produtoVO;
    }

    public void carregarContratosImportados() throws Exception {
        ResultSet rs = SuperFacadeJDBC.criarConsulta("select array_to_string(array(" +
                "select codigo || '-' || coalesce(id_externo,idexterno) from contrato \n" +
                "where coalesce(id_externo,idexterno) > 0),',') ", con);
        if (rs.next()) {
            if (UteisValidacao.emptyString(rs.getString(1))) {
                return;
            }
            String[] contratos = rs.getString(1).split(",");
            for (String contrato : contratos) {
                String[] split = contrato.split("-");
                getContratosImportados().put(Integer.parseInt(split[1]), Integer.parseInt(split[0]));
            }
        }
    }

    public Map<Integer, Integer> getContratosImportados() {
        if (contratosImportados == null) {
            contratosImportados = new HashMap<>();
        }
        return contratosImportados;
    }

    public PlanoDuracaoVO obterPlanoDuracao(Integer nrMeses, Integer codigoPlano) throws Exception {
        String key = nrMeses + "_" + codigoPlano;
        if (getMapaPlanoDuracaoVO().containsKey(key)) {
            return getMapaPlanoDuracaoVO().get(key);
        }

        PlanoDuracao planoDuracaoDAO = new PlanoDuracao(con);
        PlanoDuracaoVO planoDuracaoVO =  planoDuracaoDAO.consultarPorNumeroMesesPlano(nrMeses, codigoPlano, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
        planoDuracaoDAO = null;

        getMapaPlanoDuracaoVO().put(key, planoDuracaoVO);

        return planoDuracaoVO;
    }

    public Map<String, PlanoDuracaoVO> getMapaPlanoDuracaoVO() {
        if (mapaPlanoDuracaoVO == null) {
            mapaPlanoDuracaoVO = new HashMap<>();
        }
        return mapaPlanoDuracaoVO;
    }

    public List<PlanoCondicaoPagamentoVO> obterPlanoCondicaoDePagamento(Integer codigoPlanoDuracao, Integer numeroMeses, int nivelmontardadosDadosbasicos) throws Exception {
        String key = codigoPlanoDuracao + "_" + numeroMeses;
        if (getMapaPlanoCondicoesPagamento().containsKey(key)) {
            return getMapaPlanoCondicoesPagamento().get(key);
        }
        PlanoCondicaoPagamento planoCondicaoPagamentoDAO = new PlanoCondicaoPagamento(con);
        List listaPlanoCond = planoCondicaoPagamentoDAO.consultaPlanoCondicaoPagamentosNumeroParcelas(codigoPlanoDuracao, numeroMeses, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
        getMapaPlanoCondicoesPagamento().put(key, listaPlanoCond);
        return listaPlanoCond;
    }

    public Map<String, List<PlanoCondicaoPagamentoVO>> getMapaPlanoCondicoesPagamento() {
        if (mapaPlanoCondicoesPagamento == null) {
            mapaPlanoCondicoesPagamento = new HashMap<>();
        }
        return mapaPlanoCondicoesPagamento;
    }

}

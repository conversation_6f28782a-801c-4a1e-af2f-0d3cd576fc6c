package importador.outros;

import importador.LeitorExcel2010;
import negocio.comuns.basico.EmpresaVO;
import negocio.comuns.basico.FornecedorVO;
import negocio.comuns.basico.PessoaVO;
import negocio.comuns.financeiro.PlanoContaTO;
import negocio.comuns.financeiro.enumerador.TipoES;
import negocio.comuns.financeiro.enumerador.TipoEquivalenciaDRE;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisValidacao;
import negocio.facade.jdbc.arquitetura.SuperFacadeJDBC;
import negocio.facade.jdbc.basico.Fornecedor;
import negocio.facade.jdbc.basico.Pessoa;
import negocio.facade.jdbc.financeiro.PlanoConta;
import negocio.facade.jdbc.plano.Plano;
import org.apache.poi.xssf.usermodel.XSSFRow;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class ImportarFinanceiroUteis {

    public static FornecedorVO fornecedor(String nome, Integer empresa, String cnpj, Connection con) throws Exception {
        Pessoa pessoaDao = new Pessoa(con);
        Fornecedor fornecedorDao = new Fornecedor(con);

        FornecedorVO fornecedor = new FornecedorVO();
        fornecedor.setPessoa(new PessoaVO());
        fornecedor.getPessoa().setNome(nome);
        pessoaDao.incluir(fornecedor.getPessoa());

        fornecedor.setEmpresaVO(new EmpresaVO());
        fornecedor.getEmpresaVO().setCodigo(empresa);
        if (!UteisValidacao.emptyString(cnpj) &&
                !UteisValidacao.emptyString(cnpj.trim())) {
            fornecedor.setCnpj(Uteis.formatarCpfCnpj(cnpj, true));
        }
        fornecedorDao.incluir(fornecedor);

        SuperFacadeJDBC.executarConsulta("UPDATE fornecedor SET importado = TRUE WHERE codigo = " + fornecedor.getCodigo(), con);
        return fornecedor;
    }

    public static FornecedorVO consultarFornecedor(String nome, Integer empresa, String cnpj, Connection con) throws Exception {
        StringBuilder sql = new StringBuilder();
        sql.append(" SELECT * FROM fornecedor f ");
        sql.append(" INNER JOIN pessoa p ON f.pessoa = p.codigo ");
        sql.append(" where p.nome ilike ? ");
        PreparedStatement stm = con.prepareStatement(sql.toString());
        stm.setString(1, nome.trim());
        ResultSet rs = stm.executeQuery();
        if (rs.next()) {
            Fornecedor fornecedorDao = new Fornecedor(con);
            return fornecedorDao.montarDados(rs, Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA);
        } else {
            return fornecedor(nome, empresa, cnpj, con);
        }
    }

    public static Map<String, FornecedorVO> obterFornecedores(List<XSSFRow> linhas, Integer empresa, Connection con) throws Exception {
        return obterFornecedores(linhas, empresa, con, 4);
    }

    public static Map<String, FornecedorVO> obterFornecedores(List<XSSFRow> linhas, Integer empresa, Connection con, int coluna) throws Exception {
        Map<String, FornecedorVO> mapaFornecedor = new HashMap<String, FornecedorVO>();
        for (XSSFRow linha : linhas) {
            String fornecedor = LeitorExcel2010.obterString(linha, coluna);
            FornecedorVO fornecedorVO = mapaFornecedor.get(fornecedor);
            if (fornecedorVO == null) {
                mapaFornecedor.put(fornecedor, consultarFornecedor(fornecedor.replaceAll("  ", " "), empresa, null, con));
            }
        }
        return mapaFornecedor;
    }

    public static Map<String, FornecedorVO> obterFornecedoresExterno(List<XSSFRow> linhasFornecedores, Integer empresa, Connection con) throws Exception {
        Map<String, FornecedorVO> mapaFornecedor = new HashMap<String, FornecedorVO>();

        try {
            SuperFacadeJDBC.executarConsulta("ALTER TABLE fornecedor ADD COLUMN importado BOOLEAN DEFAULT FALSE;", con);
        } catch (Exception e) {
        }

        for (XSSFRow linha : linhasFornecedores) {
            String fornecedorId = LeitorExcel2010.obterStringDoNumero(linha, 0);
            String fornecedorNome = LeitorExcel2010.obterString(linha, 2);
            mapaFornecedor.put(fornecedorId, consultarFornecedor(fornecedorNome, empresa, null, con));
        }
        return mapaFornecedor;
    }

    public static PlanoContaTO consultarPlanoConta(String codigoPlano, String nome,
                                                   TipoES tipoES, TipoEquivalenciaDRE tipoEquivalenciaDRE,
                                                   Connection con, boolean controlarTransacao) throws Exception {
        StringBuilder sql = new StringBuilder();
        sql.append(" SELECT * FROM planoconta where codigoplanocontas = ? ");
        PreparedStatement stm = con.prepareStatement(sql.toString());
        stm.setString(1, codigoPlano.trim());
        ResultSet rs = stm.executeQuery();
        if (rs.next()) {
            PlanoConta planoContaDAO = new PlanoConta(con);
            return planoContaDAO.montarDados(rs);
        } else {
            return planoConta(codigoPlano, nome, tipoES, tipoEquivalenciaDRE, con, controlarTransacao);
        }
    }

    public static PlanoContaTO planoConta(String codigoPlano, String nome,
                                          TipoES tipoES, TipoEquivalenciaDRE tipoEquivalenciaDRE,
                                          Connection con, boolean controlarTransacao) throws Exception {
        PlanoConta planoContaDAO;
        try {
            planoContaDAO = new PlanoConta(con);
            PlanoContaTO obj = new PlanoContaTO();
            obj.setCodigoPlano(codigoPlano);
            obj.setDescricao(nome);
            obj.setTipoPadrao(tipoES);
            obj.setEquivalenciaDRE(tipoEquivalenciaDRE);
            if (controlarTransacao) {
                planoContaDAO.incluir(obj);
            } else {
                planoContaDAO.incluirSemCommit(obj);
            }

            SuperFacadeJDBC.executarConsulta("UPDATE planoconta SET importado = TRUE WHERE codigo = " + obj.getCodigo(), con);
            return obj;
        } finally {
            planoContaDAO = null;
        }
    }
}

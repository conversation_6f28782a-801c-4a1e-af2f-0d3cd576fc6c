package importador.outros;


import br.com.pactosolucoes.enumeradores.TipoHorarioCreditoTreinoEnum;
import br.com.pactosolucoes.enumeradores.TipoOperacaoCreditoTreinoEnum;
import importador.LeitorExcel2010;
import negocio.comuns.arquitetura.UsuarioVO;
import negocio.comuns.contrato.ContratoDuracaoCreditoTreinoVO;
import negocio.comuns.contrato.ContratoVO;
import negocio.comuns.contrato.ControleCreditoTreinoVO;
import negocio.comuns.utilitarias.Calendario;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisValidacao;
import negocio.facade.jdbc.arquitetura.SuperFacadeJDBC;
import negocio.facade.jdbc.contrato.Contrato;
import negocio.facade.jdbc.contrato.ContratoDuracaoCreditoTreino;
import negocio.facade.jdbc.contrato.ControleCreditoTreino;
import negocio.facade.jdbc.utilitarias.Conexao;
import org.apache.poi.xssf.usermodel.XSSFRow;

import java.io.FileInputStream;
import java.sql.Connection;
import java.sql.DriverManager;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.util.Date;
import java.util.List;

public class ProcessoInserirCreditosContratosImportados {

    private static int columnIdCliente = 0;
    private static int columnIdClienteContrato = 2;
    private static int columnQtdCreditos = 7;
    private static int columnQtdCreditosContratada = 8;

    public static void main(String[] args) throws Exception {

        Connection con = DriverManager.getConnection("***********************************************************************************", "postgres", "pactodb");
        Conexao.guardarConexaoForJ2SE(con);
        Integer codigoEmpresa = 1;
        Integer codigoPlanoPadraoCredito = 11;
        TipoHorarioCreditoTreinoEnum tipoHorarioCreditoTreinoEnum = TipoHorarioCreditoTreinoEnum.LIVRE_OBRIGATORIO_MARCAR_AULA;
        String caminhoPlanilhaCreditos = "C:\\pacto\\backups\\Sereia SP\\alunos_plano_credito_evo.xlsx";

        inserirCreditosContatosImportadosEvo(codigoEmpresa, caminhoPlanilhaCreditos, tipoHorarioCreditoTreinoEnum, codigoPlanoPadraoCredito, con);

    }

    private static void inserirCreditosContatosImportadosEvo(Integer codigoEmpresa, String caminhoPlanilhaCreditos, TipoHorarioCreditoTreinoEnum tipoHorarioCreditoTreinoEnum, Integer codigoPlanoPadraoCredito, Connection con) throws Exception {
        Contrato contratoDAO = new Contrato(con);
        ControleCreditoTreino controleCreditoTreinoDAO = new ControleCreditoTreino(con);

        List<XSSFRow> hssfRows = LeitorExcel2010.lerLinhas(new FileInputStream(caminhoPlanilhaCreditos), 0);

        int count = 0;
        int countSucesso = 0;
        int countFalha = 0;

        for (XSSFRow linha : hssfRows) {
            String idCliente = LeitorExcel2010.obterString(linha, columnIdCliente);
            String idClienteContrato = LeitorExcel2010.obterString(linha, columnIdClienteContrato);
            String qtdCreditos = LeitorExcel2010.obterString(linha, columnQtdCreditos);
            String qtdCreditosContratada =  LeitorExcel2010.obterString(linha, columnQtdCreditosContratada);

            try {
                con.setAutoCommit(false);
                Uteis.logarDebug("Processando linha: " + (++count) + "\\" + hssfRows.size() + " - ID_CLIENTE: " + idCliente + " ID_CLIENTE_CONTRATO: " + idClienteContrato + " QTD_CREDITOS: " + qtdCreditos);

                if (isDadosValidos(idCliente, idClienteContrato, qtdCreditos, qtdCreditosContratada)) {
                    ResultSet rs = SuperFacadeJDBC.criarConsulta("select c.codigo, c.vigenciaateajustada, c.vendacreditotreino, cd.codigo as contratoduracao, c.plano from contrato c \n" +
                            " inner join contratoduracao cd on cd.contrato = c.codigo \n" +
                            "where c.empresa = " + codigoEmpresa + "\n" +
                            "and c.id_externo = " + idClienteContrato, con);
                    if (rs.next()) {
                        Integer codigoContrato = rs.getInt("codigo");
                        if (Calendario.menor(rs.getDate("vigenciaateajustada"), Calendario.hoje())) {
                            Uteis.logarDebug("\tNão ajustado, Contrato Inativo!");
                            continue;
                        }

                        ResultSet rsExisteCredito = SuperFacadeJDBC.criarConsulta("select cdct.codigo from contratoduracaocreditotreino cdct where cdct.contratoduracao = " + rs.getInt("contratoduracao"), con);
                        if (rsExisteCredito.next()) {
                            Uteis.logarDebug("\tContrato já possui credito!");
                            continue;
                        }

                        SuperFacadeJDBC.executarUpdate("update contrato set vendacreditotreino = true where codigo = " + codigoContrato, con);
                        if (!codigoPlanoPadraoCredito.equals(rs.getInt("plano"))) {
                            SuperFacadeJDBC.executarUpdate("update contrato set plano = " + codigoPlanoPadraoCredito + " where codigo = " + codigoContrato, con);
                            SuperFacadeJDBC.executarUpdate("update movproduto mpro set descricao = pla.descricao || ' - ' || substring(mpro.descricao, position('/20' in mpro.descricao) - 2) from contrato con, plano pla, produto pro\n" +
                                    "where con.codigo = mpro.contrato\n" +
                                    "and pla.codigo = con.plano\n" +
                                    "and pro.codigo = mpro.produto\n" +
                                    "and pro.tipoproduto = 'PM'\n" +
                                    "and coalesce(con.id_externo, 0) <> 0\n" +
                                    "and mpro.descricao not ilike '%' || pla.descricao || '%' \n" +
                                    "and mpro.contrato = " + codigoContrato, con);
                        }

                        ContratoVO contratoVO = contratoDAO.consultarPorCodigo(codigoContrato, Uteis.NIVELMONTARDADOS_TODOS);
                        Date dataLancamentoCredito = Calendario.hoje();

                        ContratoDuracaoCreditoTreinoVO contratoDuracaoCreditoTreinoVO = new ContratoDuracaoCreditoTreinoVO();
                        contratoDuracaoCreditoTreinoVO.setContratoDuracaoVO(contratoVO.getContratoDuracao());
                        contratoDuracaoCreditoTreinoVO.getContratoDuracaoVO().setContratoVO(contratoVO);
                        contratoDuracaoCreditoTreinoVO.setQuantidadeCreditoCompra(Integer.parseInt(qtdCreditosContratada));
                        contratoDuracaoCreditoTreinoVO.setQuantidadeCreditoDisponivel(Integer.parseInt(qtdCreditos));
                        contratoDuracaoCreditoTreinoVO.setQuantidadeCreditoMensal(Integer.parseInt(qtdCreditosContratada));
                        contratoDuracaoCreditoTreinoVO.setCreditoTreinoNaoCumulativo(true);
                        contratoDuracaoCreditoTreinoVO.setTipoHorarioCreditoTreinoEnum(tipoHorarioCreditoTreinoEnum);
                        contratoDuracaoCreditoTreinoVO.setNumeroVezesSemana(7);
                        contratoDuracaoCreditoTreinoVO.setValorUnitario(0);
                        contratoDuracaoCreditoTreinoVO.setDataUltimoCreditoMensal(dataLancamentoCredito);

                        String sqlInsertContratoDuracaoCreditoTreino = "insert into ContratoDuracaoCreditoTreino(contratoDuracao, tipoHorario, numeroVezesSemana, quantidadeCreditoCompra, valorUnitario, creditoTreinoNaoCumulativo, quantidadeCreditoMensal, dataUltimoCreditoMensal, quantidadecreditodisponivel) values (?,?,?,?,?,?,?,?,?)";
                        PreparedStatement pst = con.prepareStatement(sqlInsertContratoDuracaoCreditoTreino);
                        pst.setInt(1, contratoDuracaoCreditoTreinoVO.getContratoDuracaoVO().getCodigo());
                        pst.setInt(2, contratoDuracaoCreditoTreinoVO.getTipoHorarioCreditoTreinoEnum().getCodigo());
                        pst.setInt(3, contratoDuracaoCreditoTreinoVO.getNumeroVezesSemana());
                        pst.setInt(4, contratoDuracaoCreditoTreinoVO.getQuantidadeCreditoCompra());
                        pst.setDouble(5, contratoDuracaoCreditoTreinoVO.getValorUnitario());
                        pst.setBoolean(6, contratoDuracaoCreditoTreinoVO.isCreditoTreinoNaoCumulativo());
                        pst.setInt(7, contratoDuracaoCreditoTreinoVO.getQuantidadeCreditoMensal());
                        pst.setTimestamp(8, Uteis.getDataJDBCTimestamp(contratoDuracaoCreditoTreinoVO.getDataUltimoCreditoMensal()));
                        pst.setInt(9, contratoDuracaoCreditoTreinoVO.getQuantidadeCreditoDisponivel());
                        pst.execute();

                        ControleCreditoTreinoVO controleCreditoTreinoVO = new ControleCreditoTreinoVO();
                        controleCreditoTreinoVO.setContratoVO(contratoVO);
                        controleCreditoTreinoVO.setTipoOperacaoCreditoTreinoEnum(TipoOperacaoCreditoTreinoEnum.COMPRA);
                        controleCreditoTreinoVO.setQuantidade(contratoDuracaoCreditoTreinoVO.getQuantidadeCreditoCompra());
                        controleCreditoTreinoVO.setDataOperacao(dataLancamentoCredito);
                        controleCreditoTreinoVO.setDatalancamento(dataLancamentoCredito);
                        controleCreditoTreinoVO.setUsuarioVO(new UsuarioVO());
                        controleCreditoTreinoVO.getUsuarioVO().setCodigo(1);

                        controleCreditoTreinoDAO.incluirSemCommit(controleCreditoTreinoVO, contratoVO.getCliente().getCodigo(), null, dataLancamentoCredito);

                        int qtdUtilizado = contratoDuracaoCreditoTreinoVO.getQuantidadeCreditoCompra() - contratoDuracaoCreditoTreinoVO.getQuantidadeCreditoDisponivel();
                        if (qtdUtilizado > 0) {
                            controleCreditoTreinoVO = new ControleCreditoTreinoVO();
                            controleCreditoTreinoVO.setContratoVO(contratoVO);
                            controleCreditoTreinoVO.setTipoOperacaoCreditoTreinoEnum(TipoOperacaoCreditoTreinoEnum.AJUSTE_MANUAL);
                            controleCreditoTreinoVO.setQuantidade(qtdUtilizado * -1);
                            controleCreditoTreinoVO.setObservacao("IMPORTAÇÃO");
                            controleCreditoTreinoVO.setDataOperacao(dataLancamentoCredito);
                            controleCreditoTreinoVO.setDatalancamento(dataLancamentoCredito);
                            controleCreditoTreinoVO.setUsuarioVO(new UsuarioVO());
                            controleCreditoTreinoVO.getUsuarioVO().setCodigo(1);
                        }

                        controleCreditoTreinoDAO.incluirSemCommit(controleCreditoTreinoVO, contratoVO.getCliente().getCodigo(), null, dataLancamentoCredito);

                    } else {
                        Uteis.logar("Contrato id_externo: " + idClienteContrato + " não encontrado!");
                    }
                }
                con.commit();
                countSucesso++;
            } catch (Exception e) {
                countFalha++;
                con.rollback();
                e.printStackTrace();
            } finally {
                con.setAutoCommit(true);
            }
        }
        Uteis.logarDebug("TOTAL SUCESSO: " + countSucesso);
        Uteis.logarDebug("TOTAL FALHA: " + countFalha);
    }

    private static boolean isDadosValidos(String idCliente, String idClienteContrato, String quatidadeCreditos, String qtdCreditosContratada) {
        if ((UteisValidacao.emptyString(idCliente) || !idCliente.matches("\\d+"))
        || (UteisValidacao.emptyString(idClienteContrato) || !idClienteContrato.matches("\\d+"))
        || (UteisValidacao.emptyString(quatidadeCreditos) || !quatidadeCreditos.matches("\\d+"))
        || (UteisValidacao.emptyString(qtdCreditosContratada) || !qtdCreditosContratada.matches("\\d+"))
        ) {
            return false;
        }
        return true;
    }
}

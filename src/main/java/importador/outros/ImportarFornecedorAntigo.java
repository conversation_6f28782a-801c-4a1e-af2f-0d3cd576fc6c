package importador.outros;

import importador.LeitorExcel2010;
import negocio.comuns.basico.EmpresaVO;
import negocio.comuns.basico.FornecedorVO;
import negocio.comuns.basico.PessoaVO;
import negocio.comuns.utilitarias.Uteis;
import negocio.facade.jdbc.arquitetura.SuperFacadeJDBC;
import negocio.facade.jdbc.basico.Fornecedor;
import negocio.facade.jdbc.basico.Pessoa;
import org.apache.poi.xssf.usermodel.XSSFRow;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.logging.Level;
import java.util.logging.Logger;

/**
 * Created by <PERSON><PERSON> Alcides on 01/11/2016.
 */
public class ImportarFornecedorAntigo {

    public static FornecedorVO fornecedor(String nome, Integer empresa, Connection con) throws Exception{
        Pessoa pessoaDao = new Pessoa(con);
        Fornecedor fornecedorDao = new Fornecedor(con);

        FornecedorVO fornecedor = new FornecedorVO();
        fornecedor.setPessoa(new PessoaVO());
        fornecedor.getPessoa().setNome(nome);
        pessoaDao.incluir(fornecedor.getPessoa());

        fornecedor.setEmpresaVO(new EmpresaVO());
        fornecedor.getEmpresaVO().setCodigo(empresa);
        fornecedorDao.incluir(fornecedor);

        SuperFacadeJDBC.executarConsulta("UPDATE fornecedor SET importado = TRUE WHERE codigo = "+fornecedor.getCodigo(), con);
        System.out.println("Gravei fornecedor "+nome);
        return fornecedor;
    }

    public static FornecedorVO consultarFornecedor(String nome, Integer empresa, Connection con) throws Exception{
        StringBuilder sql = new StringBuilder();
        sql.append(" SELECT * FROM fornecedor f ");
        sql.append(" INNER JOIN pessoa p ON f.pessoa = p.codigo ");
        sql.append(" where p.nome ilike ? ");
        PreparedStatement stm = con.prepareStatement(sql.toString());
        stm.setString(1 , nome.trim());
        ResultSet rs = stm.executeQuery();
        if(rs.next()){
            Fornecedor fornecedorDao = new Fornecedor(con);
            System.out.println("Achei fornecedor "+nome.trim());
            return fornecedorDao.montarDados(rs, Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA);
        }else{
            return fornecedor(nome,empresa, con);
        }

    }

    public static Map<String, FornecedorVO> obterFornecedores(List<XSSFRow> linhas, Integer empresa, Connection con) throws Exception{
        return obterFornecedores(linhas, empresa, con, 4);
    }
    public static Map<String, FornecedorVO> obterFornecedores(List<XSSFRow> linhas, Integer empresa, Connection con, int coluna) throws Exception{
        Map<String, FornecedorVO> mapaFornecedor = new HashMap<String, FornecedorVO>();
        try {
            SuperFacadeJDBC.executarConsulta("ALTER TABLE fornecedor ADD COLUMN importado BOOLEAN DEFAULT FALSE;", con);
        }catch(Exception e){
        }
        for(XSSFRow linha : linhas){
            String fornecedor = LeitorExcel2010.obterString(linha, coluna);
            FornecedorVO fornecedorVO = mapaFornecedor.get(fornecedor);
            if(fornecedorVO == null){
                mapaFornecedor.put(fornecedor, consultarFornecedor(fornecedor.replaceAll("  ", " "),empresa, con));
            }
        }
        return mapaFornecedor;
    }
    
    public static Map<String, FornecedorVO> obterFornecedoresExterno(List<XSSFRow> linhasFornecedores, Integer empresa, Connection con) throws Exception {
        Map<String, FornecedorVO> mapaFornecedor = new HashMap<String, FornecedorVO>();
        
        try {
            SuperFacadeJDBC.executarConsulta("ALTER TABLE fornecedor ADD COLUMN importado BOOLEAN DEFAULT FALSE;", con);
        }catch(Exception e){
        }
        
        for(XSSFRow linha : linhasFornecedores){
            String fornecedorId = LeitorExcel2010.obterStringDoNumero(linha, 0);
            String fornecedorNome = LeitorExcel2010.obterString(linha, 2);
            mapaFornecedor.put(fornecedorId, consultarFornecedor(fornecedorNome, empresa, con));
        }
        return mapaFornecedor;
    }
}

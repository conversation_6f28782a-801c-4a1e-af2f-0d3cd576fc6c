package importador.outros;

import br.com.pactosolucoes.oamd.controle.basico.DAO;
import negocio.comuns.utilitarias.Uteis;
import negocio.facade.jdbc.arquitetura.SuperFacadeJDBC;

import java.io.IOException;
import java.sql.Connection;
import java.sql.DriverManager;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.HashMap;
import java.util.Map;

public class ProcessosReguaDeCobranca {

    private static Map<String, StringBuilder> mapaLogGravar;
    private static String nomeBanco = "";
    private static String logGeral = "GERAL";

    public static void main(String[] args) throws IOException, SQLException {
        try {
            Connection conOAMD = DriverManager.getConnection("*************************************", "postgres", "pactodb");
            processarTodosOsBancos(conOAMD);
//            Connection con = DriverManager.getConnection("**************************************************************", "postgres", "pactodb");
//            preencherPactoPayComunicacaoAntigos(con);
        } catch (Exception ex) {
            ex.printStackTrace();
        }
    }

    public static void processarTodosOsBancos(Connection conOAMD) throws Exception {
        PreparedStatement st = conOAMD.prepareStatement("SELECT chave from empresa");
        ResultSet rs = st.executeQuery();
        while (rs.next()) {
            String chave = rs.getString("chave");
            try (Connection con = new DAO().obterConexaoEspecifica(chave)) {
                preencherPactoPayComunicacaoAntigos(con);
            }
        }
    }

    public static void preencherPactoPayComunicacaoAntigos(Connection con) throws Exception {
        SuperFacadeJDBC.executarUpdateExecutarProcessos("alter table pactopaycomunicacao add column ajuste_manual boolean;", con);

        StringBuilder sql = new StringBuilder();
        sql.append("--ajustar pactopaycomunicacao antigas  \n");
        sql.append("--marcar itens ajustados \n");
        sql.append("--alter table pactopaycomunicacao add column ajuste_manual boolean; \n");
        sql.append(" \n");
        sql.append("select  \n");
        sql.append("--base.*, \n");
        sql.append("--base.data_registro::date - ppc.dataregistro::date as diferenca_tempo, \n");
        sql.append("--base.data_registro as dataregistro_operacao, \n");
        sql.append("--ppc.dataregistro as dataregistro_pactopaycomunicacao_proxima, \n");
        sql.append("('update pactopaycomunicacao set ajuste_manual = true, '|| base.tabela ||' = ' || base.codigo || ' where ajuste_manual is null and codigo = ' || base.pactocomunicacao_proxima ) as sql_ajustar \n");
        sql.append("from ( \n");
        sql.append("select  \n");
        sql.append("'transacao' as tabela, \n");
        sql.append("t.codigo as codigo, \n");
        sql.append("t.dataprocessamento as data_registro, \n");
        sql.append("t.origem as origem, \n");
        sql.append("t.empresa as empresa, \n");
        sql.append("(select max(codigo) from pactopaycomunicacao where transacao = t.codigo) as pactocomunicacao_codigo, \n");
        sql.append("(select max(codigo) from pactopaycomunicacao where transacao is null and pessoa = t.pessoapagador and dataregistro < t.dataprocessamento) as pactocomunicacao_proxima \n");
        sql.append("from transacao t  \n");
        sql.append(" \n");
        sql.append("union  \n");
        sql.append(" \n");
        sql.append("select  \n");
        sql.append("'pix' as tabela, \n");
        sql.append("t.codigo as codigo, \n");
        sql.append("t.\"data\" as data_registro, \n");
        sql.append("t.origem as origem, \n");
        sql.append("t.empresa as empresa, \n");
        sql.append("(select max(codigo) from pactopaycomunicacao where pix = t.codigo) as pactocomunicacao_codigo, \n");
        sql.append("(select max(codigo) from pactopaycomunicacao where pix is null and pessoa = t.pessoa and dataregistro < t.\"data\") as pactocomunicacao_proxima \n");
        sql.append("from pix t  \n");
        sql.append(" \n");
        sql.append("union  \n");
        sql.append(" \n");
        sql.append("select  \n");
        sql.append("'boleto' as tabela, \n");
        sql.append("t.codigo as codigo, \n");
        sql.append("t.dataregistro as data_registro, \n");
        sql.append("t.origem as origem, \n");
        sql.append("t.empresa as empresa, \n");
        sql.append("(select max(codigo) from pactopaycomunicacao where boleto = t.codigo) as pactocomunicacao_codigo, \n");
        sql.append("(select max(codigo) from pactopaycomunicacao where boleto is null and pessoa = t.pessoa and dataregistro < t.dataregistro) as pactocomunicacao_proxima \n");
        sql.append("from boleto t  \n");
        sql.append(" \n");
        sql.append("union  \n");
        sql.append(" \n");
        sql.append("select  \n");
        sql.append("'autorizacaocobrancacliente' as tabela, \n");
        sql.append("t.codigo as codigo, \n");
        sql.append("t.dataregistro as data_registro, \n");
        sql.append("t.origem as origem, \n");
        sql.append("0 as empresa, \n");
        sql.append("(select max(codigo) from pactopaycomunicacao where autorizacaocobrancacliente = t.codigo) as pactocomunicacao_codigo, \n");
        sql.append("(select max(codigo) from pactopaycomunicacao where autorizacaocobrancacliente is null and pessoa = cl.pessoa and dataregistro < t.dataregistro) as pactocomunicacao_proxima \n");
        sql.append("from autorizacaocobrancacliente t  \n");
        sql.append("inner join cliente cl on cl.codigo = t.cliente \n");
        sql.append(" \n");
        sql.append(") as base \n");
        sql.append("inner join pactopaycomunicacao ppc on ppc.codigo = base.pactocomunicacao_proxima \n");
        sql.append("where base.origem in (select codigo from origemcobranca where reguacobranca and codigo not in (22))  \n");
        sql.append("and base.pactocomunicacao_codigo is null \n");
        sql.append("and base.pactocomunicacao_proxima is not null \n");
        //somente os que tem diferença de dias de até 5 dias entre a comunicação e a operação que foi feita
        sql.append("and (base.data_registro::date - ppc.dataregistro::date) <= 5 \n");
//        sql.append("limit 500 \n");
        Integer total = SuperFacadeJDBC.contar("select count(*) as qtd from ( " + sql + " ) as sql", con);
        ResultSet rs = SuperFacadeJDBC.criarConsulta(sql.toString(), con);
        int i = 0;
        while (rs.next()) {
            try {
                Uteis.logarDebug(++i + "/" + total + " - PreencherPactoPayComunicacaoAntigos");
                SuperFacadeJDBC.executarUpdate(rs.getString(1), con);
            } catch (Exception ex) {
                ex.printStackTrace();
            }
        }
    }

    private static void adicionarLog(String keyMapa, String msg) {
        String s = "[DEBUG] " + Uteis.getDataAtualAplicandoFormatacao("dd/MM/yyyy hh:mm:ss") + " --> " + msg;
        System.out.println(s);
        StringBuilder logGravar = getLogGravar(keyMapa).append(s).append("\n");
        mapaLogGravar.put(keyMapa, logGravar);
    }

    private static StringBuilder getLogGravar(String keyMapa) {
        if (mapaLogGravar == null) {
            mapaLogGravar = new HashMap<>();
        }
        StringBuilder logGravar = mapaLogGravar.get(keyMapa);
        if (logGravar == null) {
            logGravar = new StringBuilder();
        }
        return logGravar;
    }
}




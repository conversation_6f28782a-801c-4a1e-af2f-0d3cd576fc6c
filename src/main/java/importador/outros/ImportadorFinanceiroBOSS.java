package importador.outros;

import br.com.pactosolucoes.enumeradores.TipoOperacaoLancamento;
import importador.LeitorExcel2010;
import importador.outros.ImportarFornecedorAntigo;
import negocio.comuns.arquitetura.UsuarioVO;
import negocio.comuns.basico.EmpresaVO;
import negocio.comuns.basico.FornecedorVO;
import negocio.comuns.financeiro.CaixaVO;
import negocio.comuns.financeiro.ContaVO;
import negocio.comuns.financeiro.MovContaRateioVO;
import negocio.comuns.financeiro.MovContaVO;
import negocio.comuns.financeiro.PlanoContaTO;
import negocio.comuns.financeiro.enumerador.TipoES;
import negocio.comuns.utilitarias.Calendario;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisValidacao;
import negocio.facade.jdbc.arquitetura.SuperFacadeJDBC;
import negocio.facade.jdbc.arquitetura.Usuario;
import negocio.facade.jdbc.financeiro.Caixa;
import negocio.facade.jdbc.financeiro.MovConta;
import org.apache.poi.xssf.usermodel.XSSFRow;

import java.io.File;
import java.io.FileInputStream;
import java.sql.Connection;
import java.sql.DriverManager;
import java.sql.ResultSet;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * Created by Joao Alcides on 01/11/2016.
 */
public class ImportadorFinanceiroBOSS {

    public static Integer EMPRESA = 1;
    public static Integer contaPadrao = 1;

    public static Map<String, Integer> mapaContas(){
        Map<String, Integer> contas = new HashMap<String, Integer>();
        //ADICIONE AQUI A CORRESPONDENCIA ENTRE CONTAS
        //"NOME DA CONTA NO INFO SKY", codigo da conta no ZW
        contas.put("13003075-9", 10);
        contas.put("FOM", 6);
        contas.put("02065-9", 5);
        return contas;
    }

    public static Map<String, Integer> mapaCC(){
        Map<String, Integer> contas = new HashMap<String, Integer>();
        //ADICIONE AQUI A CORRESPONDENCIA ENTRE CENTRO DE CUSTOS
        //"CODIGO DO CENTRO DE CUSTOS NO INFO SKY", codigo do centro de custos no ZW
        contas.put("1", 1);
        contas.put("2", 11);
        return contas;
    }

    public static List<String> listFiles(String caminho ){
        File diretorio = new File(caminho);
        List<String> lista = Arrays.asList(diretorio.list());
        return lista;
    }

    public static Map<TipoES, Map<String, Integer>> mapaColunas(){
        Map<TipoES, Map<String, Integer>> mapa = new HashMap<TipoES, Map<String, Integer>>();
        Map<String, Integer> saida = new HashMap<String, Integer>();
        saida.put("fornecedor", 8);
        saida.put("lancamento", 11);
        saida.put("vencimento", 11);
        saida.put("quitacao", 11);
        saida.put("competencia", 11);
        saida.put("valor", 13);
        saida.put("conta", 10);
        saida.put("descricao", 12);
        saida.put("plano", 10);
        saida.put("cod", 7);
        saida.put("tip", 6);
        mapa.put(TipoES.SAIDA, saida);

        Map<String, Integer> entrada = new HashMap<String, Integer>();
        entrada.put("fornecedor", 1);
        entrada.put("lancamento", 8);
        entrada.put("vencimento", 8);
        entrada.put("quitacao", 6);
        entrada.put("competencia", 7);
        entrada.put("valor", 15);
        entrada.put("conta", 4);
        entrada.put("descricao", 2);
        entrada.put("plano", 4);
        entrada.put("cod", 7);
        entrada.put("tip", 4);
        mapa.put(TipoES.ENTRADA, entrada);
        return mapa;
    }

    public static void importarFinanceiroArquivoSimples(String arquivo, Connection con, Integer empresa, TipoES tipoES) throws Exception{
        try {
            SuperFacadeJDBC.executarConsulta("ALTER TABLE movconta ADD COLUMN importado BOOLEAN DEFAULT FALSE;", con);
        }catch(Exception e){
        }
        try {
            SuperFacadeJDBC.executarConsulta("ALTER TABLE planoconta add column nomeconsulta text;", con);
        }catch(Exception e){
        }
        Map<TipoES, Map<String, Integer>> mc = mapaColunas();
        SuperFacadeJDBC.executarConsulta("update planoconta set nomeconsulta = remove_acento_upper(nome);", con);
        List<XSSFRow> hssfRows = LeitorExcel2010.lerLinhas(new FileInputStream(arquivo));
        System.out.println(arquivo);
        Map<String, FornecedorVO> mapaFornecedores = ImportarFornecedorAntigo.obterFornecedores(hssfRows, empresa, con,mc.get(tipoES).get("fornecedor"));
        Usuario userDao = new Usuario(con);
        Caixa caixaDao = new Caixa(con);
        MovConta movContaDao = new MovConta(con);
        List<UsuarioVO> list = userDao.consultarPorUsername("PACTOBR", false, Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA);
        Map<String, Integer> mapaContas = mapaContas();
        Map<String, Integer> mapaCC = mapaCC();

        if(!list.isEmpty()){
            CaixaVO caixaVO =  new CaixaVO();
            caixaVO.setUsuarioVo(list.get(0));
            caixaVO.setDataAbertura(Calendario.hoje());
            caixaVO.setDataFechamento(Calendario.hoje());
            caixaVO.setEmpresaVo(new EmpresaVO());
            caixaVO.getEmpresaVo().setCodigo(empresa);
            caixaDao.incluir(caixaVO);
            int i = 0;
            for(XSSFRow linha : hssfRows){
                System.out.println(++i+" - importando lancamento");
                MovContaVO movContaVO = new MovContaVO();
                movContaVO.setEmpresaVO(new EmpresaVO());

                movContaVO.getEmpresaVO().setCodigo(empresa);
                movContaVO.setDataCompetencia(LeitorExcel2010.obterDataEspecifico(linha, mc.get(tipoES).get("competencia")));
                movContaVO.setDataLancamento(LeitorExcel2010.obterDataEspecifico(linha, mc.get(tipoES).get("lancamento")));
                movContaVO.setDataVencimento(LeitorExcel2010.obterDataEspecifico(linha, mc.get(tipoES).get("vencimento")));
                try {
                    if(!UteisValidacao.emptyString(LeitorExcel2010.obterDataEspecifico(linha, mc.get(tipoES).get("quitacao")).toString())){
                        Date baixa = LeitorExcel2010.obterDataEspecifico(linha, mc.get(tipoES).get("quitacao"));
                        if(Calendario.maiorOuIgual(Calendario.hoje(), baixa)){
                            movContaVO.setDataQuitacao(baixa);
                        }
                    }
                }catch (Exception e){
                    e.printStackTrace();
                }

                movContaVO.setPessoaVO(mapaFornecedores.get(LeitorExcel2010.obterString(linha, mc.get(tipoES).get("fornecedor"))).getPessoa());

                Double valor =LeitorExcel2010.obterNumero(linha, mc.get(tipoES).get("valor")).doubleValue();
                if(UteisValidacao.emptyNumber(valor)){
                    valor = Double.valueOf(LeitorExcel2010.obterString(linha, mc.get(tipoES).get("valor")).replace(".","").replace(",", "."));
                }
                movContaVO.setValor(valor < 0.0 ? valor * -1 : valor);

                movContaVO.setContaVO(new ContaVO());
                Integer conta = mapaContas.get(LeitorExcel2010.obterString(linha, mc.get(tipoES).get("conta")));
                if(conta == null) {
                    conta = contaPadrao;
                }
                movContaVO.getContaVO().setCodigo(conta);
                if(tipoES.equals(TipoES.SAIDA)){
                    movContaVO.setDescricao(LeitorExcel2010.obterString(linha, mc.get(tipoES).get("descricao")));
                    movContaVO.setTipoOperacaoLancamento(TipoOperacaoLancamento.PAGAMENTO);
                }else{
                    movContaVO.setTipoOperacaoLancamento(TipoOperacaoLancamento.RECEBIMENTO);
                    movContaVO.setAutorizacaoCartao(LeitorExcel2010.obterString(linha, 22));
                    movContaVO.setDescricao("PARCELA "
                            + LeitorExcel2010.obterString(linha, mc.get(tipoES).get("descricao"))
                            + "/"+LeitorExcel2010.obterString(linha, 3));
                }

                MovContaRateioVO movContaRateioVO = new MovContaRateioVO();
                movContaRateioVO.setDescricao(movContaVO.getDescricao());
                movContaRateioVO.setValor(movContaVO.getValor());
                movContaRateioVO.setTipoES(tipoES);



                String plano = LeitorExcel2010.obterString(linha, mc.get(tipoES).get("plano"));

                Integer codigoPC = planoContasNome(con, plano);
                if(UteisValidacao.emptyNumber(codigoPC)){
                    codigoPC = planoContas(con, plano);
                }
                if(!UteisValidacao.emptyNumber(codigoPC)){
                    movContaRateioVO.setPlanoContaVO(new PlanoContaTO());
                    movContaRateioVO.getPlanoContaVO().setCodigo(codigoPC);
                }

                String obs = "";
                obs += "COD: " + LeitorExcel2010.obterString(linha, mc.get(tipoES).get("cod"));
                obs += "\nTIP: " + LeitorExcel2010.obterString(linha, mc.get(tipoES).get("tip"));
                obs += "\n\n\nARQ: " + arquivo;
                movContaVO.setObservacoes(obs);

                movContaVO.setMovContaRateios(new ArrayList<MovContaRateioVO>());
                movContaVO.getMovContaRateios().add(movContaRateioVO);
                movContaVO.setUsuarioVO(caixaVO.getUsuarioVo());


                movContaDao.incluirSemCommit(movContaVO, caixaVO.getCodigo(), false, null);
                SuperFacadeJDBC.executarConsulta("UPDATE movconta SET importado = TRUE WHERE codigo = "+movContaVO.getCodigo(), con);
            }
        }
    }

    public static Integer planoContas(Connection con, String nomepc) throws Exception{
        ResultSet resultSet = SuperFacadeJDBC.criarConsulta("SELECT codigo FROM planoconta where nomeconsulta like remove_acento_upper('" +
                nomepc.replaceAll(" ", "%")+"%')", con);
        if(resultSet.next()){
            return resultSet.getInt("codigo");
        }
        return null;
    }


    public static Integer planoContasNome(Connection con, String nomepc) throws Exception{
        ResultSet resultSet = SuperFacadeJDBC.criarConsulta("SELECT codigo FROM planoconta where nome ilike '"+nomepc+"'", con);
        if(resultSet.next()){
            return resultSet.getInt("codigo");
        }
        return null;
    }

    public static void main(String[] args){
        try {
            Connection con = DriverManager.getConnection("**********************************************************", "postgres", "pactodb");
           // importarFinanceiroArquivoSimples("C:\\Importacao\\BD_clientes\\UNIC\\CONTAS_A_RECEBER.xlsx", con, 1, TipoES.ENTRADA);
            importarFinanceiroArquivoSimples("C:\\Importacao\\BD_clientes\\UNIC\\CONTAS_A_PAGAR.xlsx", con, 1, TipoES.SAIDA);
        }catch (Exception e){
            e.printStackTrace();
        }
    }
}

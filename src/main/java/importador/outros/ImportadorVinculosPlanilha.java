package importador.outros;

import br.com.pactosolucoes.enumeradores.TipoColaboradorEnum;
import importador.LeitorExcel2010;
import negocio.comuns.arquitetura.UsuarioVO;
import negocio.comuns.basico.ClienteVO;
import negocio.comuns.basico.ColaboradorVO;
import negocio.comuns.basico.VinculoVO;
import negocio.comuns.utilitarias.Calendario;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisValidacao;
import negocio.facade.jdbc.arquitetura.SuperFacadeJDBC;
import negocio.facade.jdbc.arquitetura.Usuario;
import negocio.facade.jdbc.basico.Cliente;
import negocio.facade.jdbc.basico.Colaborador;
import negocio.facade.jdbc.basico.Vinculo;
import negocio.facade.jdbc.utilitarias.Conexao;
import org.apache.poi.xssf.usermodel.XSSFRow;

import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;
import java.sql.Connection;
import java.sql.DriverManager;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class ImportadorVinculosPlanilha {

    private static StringBuilder logGravar;
    private static String nomeBanco = "";
    // Indices colunas planilha
    private static int empresa, qtdProfessorImportado, qtdConsultorImportado, idClienteIndex, nomeClienteIndex, idExternoConsultorIndex, nomeConsultorIndex, idExternoProfessorIndex, nomeProfessorIndex;
    private static boolean importarConsultor, importarProfessor = true;
    private static boolean idClienteCodigoPacto, idConsultorCodigoPacto, idProfessorCodigoPacto = false;
    private static TipoColaboradorEnum tipoColaboradorProfessor;
    private static Map<String, ColaboradorVO> mapaColaborador = new HashMap<>();

    public static void main(String[] args) throws IOException {
        Date d1 = Calendario.hoje();
        try {
            Connection con = DriverManager.getConnection("******************************************", "postgres", "pactodb");
            Conexao.guardarConexaoForJ2SE(con);

            nomeBanco = con.getCatalog();

            //Ex: "C:\\Processos\\arquivo\\sereia.xlsx"
            String arquivo = "C:\\Processos\\arquivo\\sereia.xlsx";

            //Empresa
            empresa = 1;

            //importar consultor
            importarConsultor = true;
            //importar professor
            importarProfessor = true;
            tipoColaboradorProfessor = TipoColaboradorEnum.PROFESSOR;

            //TRUE = Significa que o id informado na planilha é o codigo do colaborador no Sistema Pacto
            //FALSE = Sistema vai buscar o colaborador por buscando pelo idExterno ou pelo nome
            idClienteCodigoPacto = false;
            idConsultorCodigoPacto = false;
            idProfessorCodigoPacto = false;

            // Colunas planilha - (Necessário conferir os indices nas planilhas)
            idClienteIndex = 0;
            nomeClienteIndex = 1;
            idExternoProfessorIndex = 2;
            nomeProfessorIndex = 3;
            idExternoConsultorIndex = 4;
            nomeConsultorIndex = 5;

            adicionarLog("Parâmetros: ");
            adicionarLog("nomeBanco: " + nomeBanco);
            adicionarLog("Arquivo: " + arquivo);
            adicionarLog("Empresa: " + empresa);
            adicionarLog("importarConsultor: " + importarConsultor);
            adicionarLog("importarProfessor: " + importarProfessor);
            importar(arquivo, con);
        } catch (Exception ex) {
            ex.printStackTrace();
            adicionarLog(ex.getMessage());
        } finally {
            Date d2 = Calendario.hoje();
            adicionarLog("=============================================");
            adicionarLog("Tempo Total: " + Calendario.diferencaEmSegundos(d1, d2) + " segundos");
            adicionarLog("=============================================");
            Uteis.salvarArquivo(ImportadorVinculosPlanilha.class.getSimpleName() + "_" + nomeBanco + "-" + Calendario.getData("yyyyMMddHHmmss") + ".txt", getLogGravar().toString(), "C:\\Processos\\" + File.separator);
        }
    }

    private static void importar(String arquivo, Connection con) throws Exception {
        Usuario usuarioDAO;
        try {
            usuarioDAO = new Usuario(con);

            UsuarioVO usuarioAdminVO = usuarioDAO.consultarPorUsername("admin", Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA);

            File arquivoFile = new File(arquivo);
            List<XSSFRow> hssfRows = LeitorExcel2010.lerLinhas(new FileInputStream(arquivo));
            int atual = 0;
            Integer erros = 0;
            for (XSSFRow linha : hssfRows) {
                String nomeCliente = "";
                try {
                    adicionarLog((++atual) + "/" + hssfRows.size() + " - Importando Vinculo - Planilha: \"" + arquivoFile.getName() + "\"");

                    String idCliente = LeitorExcel2010.obterString(linha, idClienteIndex);
                    nomeCliente = LeitorExcel2010.obterString(linha, nomeClienteIndex);
                    ClienteVO clienteVO = consultarCliente(idCliente, nomeCliente, con);

                    if (importarConsultor) {
                        try {
                            String idConsultor = LeitorExcel2010.obterString(linha, idExternoConsultorIndex);
                            String nomeConsultor = LeitorExcel2010.obterString(linha, nomeConsultorIndex);
                            importarVinculo(clienteVO, usuarioAdminVO, idConsultor, nomeConsultor, TipoColaboradorEnum.CONSULTOR, con);
                            ++qtdConsultorImportado;
                        } catch (Exception ex) {
                            adicionarLog("ERRO | IMPORTAR CONSULTOR | ARQUIVO " + arquivoFile.getName() + " | LINHA " + atual + " | " + nomeCliente + " | " + ex.getMessage());
                        }
                    }

                    if (importarProfessor) {
                        try {
                            String idProfessor = LeitorExcel2010.obterString(linha, idExternoProfessorIndex);
                            String nomeProfessor = LeitorExcel2010.obterString(linha, nomeProfessorIndex);
                            importarVinculo(clienteVO, usuarioAdminVO, idProfessor, nomeProfessor, tipoColaboradorProfessor, con);
                            ++qtdProfessorImportado;
                        } catch (Exception ex) {
                            adicionarLog("ERRO | IMPORTAR PROFESSOR | ARQUIVO " + arquivoFile.getName() + " | LINHA " + atual + " | " + nomeCliente + " | " + ex.getMessage());
                        }
                    }
                } catch (Exception ex) {
//                    ex.printStackTrace();
                    erros++;
                    adicionarLog("ERRO | ARQUIVO " + arquivoFile.getName() + " | LINHA " + atual + " | " + nomeCliente + " | " + ex.getMessage());
                }
            }

            adicionarLog("=============================================");
            adicionarLog("================= RESULTADO =================");
            adicionarLog("=============================================");
            adicionarLog("Erros buscar cliente: " + erros);
            adicionarLog("Vinculo Consultor Importado: " + qtdConsultorImportado);
            adicionarLog("Vinculo Professor Importado: " + qtdProfessorImportado);
            adicionarLog("=============================================");
            adicionarLog("Banco: " + nomeBanco);
            adicionarLog("Arquivo: " + arquivo);
            adicionarLog("Linhas: " + hssfRows.size());
            adicionarLog("=============================================");
            adicionarLog("=============================================");
        } finally {
            usuarioDAO = null;
        }
    }

    private static void importarVinculo(ClienteVO clienteVO, UsuarioVO usuarioVO,
                                        String idColaborador, String nomeColaborador,
                                        TipoColaboradorEnum tipoColaboradorEnum, Connection con) throws Exception {
        ColaboradorVO colaboradorVO = consultarColaborador(idColaborador.trim(), nomeColaborador.trim(), tipoColaboradorEnum, con);
        if (colaboradorVO == null || UteisValidacao.emptyNumber(colaboradorVO.getCodigo())) {
            throw new Exception("Não encontrei colaborador com idColaborador: " + idColaborador.trim() + " | nomeColaborador: " + nomeColaborador.trim());
        }
        excluirVinculos(clienteVO, tipoColaboradorEnum, con);
        criarVinculo(clienteVO, colaboradorVO, usuarioVO, tipoColaboradorEnum, con);
    }

    private static void criarVinculo(ClienteVO clienteVO, ColaboradorVO colaboradorVO, UsuarioVO usuarioVO,
                                     TipoColaboradorEnum tipoColaboradorEnum, Connection con) throws Exception {
        Vinculo vinculoDAO;
        try {
            vinculoDAO = new Vinculo(con);
            VinculoVO obj = new VinculoVO();
            obj.setCliente(clienteVO);
            obj.setColaborador(colaboradorVO);
            obj.setTipoVinculo(tipoColaboradorEnum.getSigla());
            vinculoDAO.incluir(obj, Calendario.hoje(), "IMPORTACAO", false, usuarioVO, null);
            if (UteisValidacao.emptyNumber(obj.getCodigo())) {
                throw new Exception("Não foi incluido Vinculo");
            }
        } finally {
            vinculoDAO = null;
        }
    }

    private static void excluirVinculos(ClienteVO clienteVO, TipoColaboradorEnum tipoColaboradorEnum, Connection con) throws Exception {
        SuperFacadeJDBC.executarConsulta("DELETE FROM vinculo WHERE tipovinculo = '" + tipoColaboradorEnum.getSigla() + "' AND cliente = " + clienteVO.getCodigo(), con);
        SuperFacadeJDBC.executarConsulta("DELETE FROM historicovinculo WHERE tipocolaborador = '" + tipoColaboradorEnum.getSigla() + "' AND cliente = " + clienteVO.getCodigo(), con);
    }

    private static ClienteVO consultarCliente(String idCliente, String nomeCliente, Connection con) throws Exception {
        Cliente clienteDAO;
        try {
            clienteDAO = new Cliente(con);

            if (idClienteCodigoPacto) {
                ClienteVO clienteVO = clienteDAO.consultarPorChavePrimaria(Integer.parseInt(idCliente), Uteis.NIVELMONTARDADOS_AUTORIZACAO_COBRANCA);
                if (clienteVO == null || UteisValidacao.emptyNumber(clienteVO.getCodigo())) {
                    throw new Exception("Cliente não encontrado com o código: " + idCliente);
                }
                return clienteVO;
            }

            if (!UteisValidacao.emptyString(idCliente)) {
                ClienteVO clienteVO = clienteDAO.consultarPorCodigoMatriculaExterna(Integer.parseInt(idCliente), empresa, Uteis.NIVELMONTARDADOS_AUTORIZACAO_COBRANCA);
                if (clienteVO != null && !UteisValidacao.emptyNumber(clienteVO.getCodigo())) {
                    return clienteVO;
                }
            }

            if (UteisValidacao.emptyString(nomeCliente)) {
                throw new Exception("Nome do cliente não informado: " + nomeCliente);
            }

            List<ClienteVO> lista = clienteDAO.consultarPorNomePessoa(nomeCliente, empresa, Uteis.NIVELMONTARDADOS_AUTORIZACAO_COBRANCA, null);
            if (lista.isEmpty()) {
                throw new Exception("Nenhum cliente encontrado com o nome: " + nomeCliente);
            } else if (lista.size() > 1) {
                throw new Exception("Foi encontrado " + lista.size() + " clientes com o nome: " + nomeCliente);
            } else {
                return lista.get(0);
            }
        } finally {
            clienteDAO = null;
        }
    }

    private static ColaboradorVO consultarColaborador(String idColaborador, String nomeColaborador,
                                                      TipoColaboradorEnum tipoColaboradorEnum, Connection con) throws Exception {
        Colaborador colaboradorDAO;
        try {
            colaboradorDAO = new Colaborador(con);

            if (UteisValidacao.emptyString(idColaborador) &&
                    UteisValidacao.emptyString(nomeColaborador)) {
                throw new Exception("Não vou importar " + tipoColaboradorEnum.getDescricao().toUpperCase() + " não tem idColaborador: " + idColaborador + " | nem nomeColaborador: " + nomeColaborador);
            }

            boolean buscarPorCodigo = false;
            if (tipoColaboradorEnum.equals(TipoColaboradorEnum.CONSULTOR)) {
                buscarPorCodigo = idConsultorCodigoPacto;
            }
            if (tipoColaboradorEnum.equals(tipoColaboradorProfessor)) {
                buscarPorCodigo = idProfessorCodigoPacto;
            }
            if (buscarPorCodigo) {
                String idMapa = ("ID_" + empresa + "_" + idColaborador);
                ColaboradorVO colaboradorVO = mapaColaborador.get(idMapa);
                if (colaboradorVO != null) {
                    return colaboradorVO;
                }
                colaboradorVO = colaboradorDAO.consultarPorChavePrimaria(Integer.parseInt(idColaborador), Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA);
                if (colaboradorVO == null || UteisValidacao.emptyNumber(colaboradorVO.getCodigo())) {
                    throw new Exception("Colaborador não encontrado com o código: " + idColaborador);
                }
                mapaColaborador.put(idMapa, colaboradorVO);
                return colaboradorVO;
            }

            if (!UteisValidacao.emptyString(idColaborador)) {
                String idMapa = ("ID_" + empresa + "_" + idColaborador);
                ColaboradorVO colaboradorVO = mapaColaborador.get(idMapa);
                if (colaboradorVO != null) {
                    return colaboradorVO;
                }
                colaboradorVO = colaboradorDAO.consultarPorIdExterno(idColaborador, empresa, Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA);
                if (colaboradorVO != null && !UteisValidacao.emptyNumber(colaboradorVO.getCodigo())) {
                    mapaColaborador.put(idMapa, colaboradorVO);
                    return colaboradorVO;
                }
            }

            if (UteisValidacao.emptyString(nomeColaborador)) {
                throw new Exception("Nome do colaborador não informado: " + nomeColaborador);
            }

            String idMapaNome = ("NOME_" + empresa + "_" + nomeColaborador);
            ColaboradorVO colaboradorVO = mapaColaborador.get(idMapaNome);
            if (colaboradorVO != null) {
                return colaboradorVO;
            }

            List<ColaboradorVO> lista = colaboradorDAO.consultarPorNomePessoa(nomeColaborador, empresa, Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA);
            if (lista.isEmpty()) {
                throw new Exception("Nenhum colaborador encontrado com o nome: " + nomeColaborador);
            } else if (lista.size() > 1) {
                throw new Exception("Foi encontrado " + lista.size() + " colaboradores com o nome: " + nomeColaborador);
            } else {
                mapaColaborador.put(idMapaNome, colaboradorVO);
                return lista.get(0);
            }
        } finally {
            colaboradorDAO = null;
        }
    }

    private static void adicionarLog(String msg) {
        String s = "[DEBUG] " + Uteis.getDataAtualAplicandoFormatacao("dd/MM/yyyy hh:mm:ss") + " --> " + msg;
        System.out.println(s);
        getLogGravar().append(s).append("\n");
    }

    private static StringBuilder getLogGravar() {
        if (logGravar == null) {
            logGravar = new StringBuilder();
        }
        return logGravar;
    }
}




/*
 * To change this template, choose Too<PERSON> | Templates
 * and open the template in the editor.
 */
package importador.outros;

import br.com.pactosolucoes.atualizadb.processo.CorrigirImportacao;
import br.com.pactosolucoes.comuns.util.Formatador;
import java.io.File;
import java.io.FileWriter;
import java.text.DateFormat;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.SimpleTimeZone;
import java.util.TimeZone;
import negocio.comuns.utilitarias.Calendario;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisValidacao;
import org.jdom.Attribute;
import org.jdom.Document;
import org.jdom.Element;
import org.jdom.output.Format;
import org.jdom.output.XMLOutputter;
import test.simulacao.LeitorXML;

/**
 *
 * <AUTHOR>
 */
public class ArquivoXML {

    public static void fazerArquivoSoComDiarias() {
        try {
            LeitorXML leitorXML = new LeitorXML();
            List<Element> listaOriginal = leitorXML.lerXML("D:\\Pagamentos.xml");
            List<Element> listaNova = new ArrayList<Element>();
            for (Element e : listaOriginal) {
                Date fim = formataData(CorrigirImportacao.retirarNulo(e,
                        "finalcontratoajustado"));
                long nrDiasEntreDatas = Uteis.nrDiasEntreDatas(Calendario.hoje(), fim);
                String tipo = CorrigirImportacao.retirarNulo(e, "Tipo_Plano");
//                if (tipo.equals("D") && nrDiasEntreDatas > -60) {
                if (tipo.equals("D")) {
                    listaNova.add(e);
                }
            }
            gravarXML(listaNova, "D:\\PagamentosDiarias.xml");
        } catch (Exception e) {
            e.printStackTrace();
        }

    }
    public static void fazerArquivoSoComCartaoDebito() {
        try {
            LeitorXML leitorXML = new LeitorXML();
            List<Element> listaOriginal = leitorXML.lerXML("D:\\Formas.xml");
            List<Element> listaNova = new ArrayList<Element>();
            for (Element e : listaOriginal) {
                String forma = CorrigirImportacao.retirarNulo(e,"FormaPgExt");
                if (forma.equals("Cartao Debito")) {
                    listaNova.add(e);
                }
            }
            gravarXML(listaNova, "D:\\FormasCartaoDebito.xml");
        } catch (Exception e) {
            e.printStackTrace();
        }

    }
    public static void fazerArquivoSoComAutorizacao() {
        try {
            LeitorXML leitorXML = new LeitorXML();
            List<Element> listaOriginal = leitorXML.lerXML("D:\\FormasPagamentoSudoeste3.xml");
            List<Element> listaNova = new ArrayList<Element>();
            for (Element e : listaOriginal) {
                String autorizacao = CorrigirImportacao.retirarNulo(e,"NomeOuAuto");
                if (!UteisValidacao.emptyString(autorizacao)) {
                    listaNova.add(e);
                }
            }
            gravarXML(listaNova, "D:\\FormasSudoeste3.xml");
        } catch (Exception e) {
            e.printStackTrace();
        }

    }

    public static void main(String... args) {
//        fazerArquivoSoComDiarias();
//        fazerArquivoSoComCartaoDebito();
        fazerArquivoSoComAutorizacao();
    }

    public static void gravarXML(List<Element> linhas, String path) throws Exception {
        Element rowDataPacket = new Element("DATAPACKET");
        Element rowData = new Element("ROWDATA");
        Document arquivo = new Document(rowDataPacket);
        rowDataPacket.addContent(rowData);
        //percorrer a lista de mapas
        for (Element row : linhas) {
            Element newrow = new Element("ROW");
            for (Object obj : row.getAttributes()) {
                Attribute atr = (Attribute) obj;
                newrow.setAttribute(atr.getName(), atr.getValue());
            }
            rowData.addContent(newrow);
        }
        XMLOutputter xout = new XMLOutputter();
        //xout.output(arquivo, System.out);
        Format formatXML = Format.getPrettyFormat();
        formatXML.setEncoding("ISO-8859-1");
        xout.setFormat(formatXML);
        FileWriter xml = new FileWriter(new File(path));
        xout.output(arquivo, xml);
    }

    public static Date formataData(String data) throws Exception {
        TimeZone tzone = new SimpleTimeZone(-3 * 60 * 60 * 1000, "Brazil");
        TimeZone.setDefault(tzone);
        Date datamenor = new Date();
        Date datamaior = new Date();
        String dataSistema = "01/01/1900";
        String dataMaiorSistema = "01/01/2100";
        DateFormat df = new SimpleDateFormat("dd/MM/yyyy");
        try {
            datamenor = df.parse(dataSistema);
            datamaior = df.parse(dataMaiorSistema);
        } catch (ParseException e) {
            e.printStackTrace();
        }
        // verificar se data é nula
        if (data == null) {
            return null;
        } else {
            try {
                // converter data
                DateFormat formatter = new SimpleDateFormat("yyyyMMdd HH:mm");
                data = data.replaceAll("T", " ");
                if (data.length() > 14) {
                    data = data.substring(0, 14);
                }
                formatter.setLenient(false);
                Date date = (Date) formatter.parse(data);
                //verificar se a data é válida
                if (date.before(datamenor) || date.after(datamaior)) {
                    return null;
                }
                // retornar data
                if (data.length() > 8) {
                    date = Uteis.getDateTime(date, Integer.parseInt(data.substring(9, 11)), Integer.parseInt(data.substring(12, 14)), 0);
                }
                return date;
            } catch (ParseException e) {
                try {
                    // converter data
                    DateFormat formatter = new SimpleDateFormat("dd/MM/yyyy");
                    formatter.setLenient(false);
                    Date date = (Date) formatter.parse(data);
                    //verificar se a data é válida
                    if (date.before(datamenor) || date.after(datamaior)) {
                        return null;
                    }
                    // retornar data
                    return date;
                    // caso existam erros na formatação, retornar null
                } catch (ParseException ex) {
                    try {
                        // converter data
                        DateFormat formatter = new SimpleDateFormat("yyyyMMdd");
                        formatter.setLenient(false);
                        Date date = (Date) formatter.parse(data);
                        //verificar se a data é válida
                        if (date.before(datamenor) || date.after(datamaior)) {
                            return null;
                        }
                        // retornar data
                        return date;
                    } catch (ParseException exc) {
                        return null;
                    }
                }
            }

        }
    }
}

package importador.outros;

import java.io.BufferedReader;
import java.io.FileReader;
import java.io.IOException;
import java.math.BigDecimal;
import java.sql.Connection;
import java.sql.DriverManager;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.text.NumberFormat;
import java.text.ParseException;
import java.util.ArrayList;
import java.util.List;
import java.util.Locale;

public class RelatorioPagamentoEngenharia {

    private Connection conZW;


    private String urlCSVAlunos = "";
    private List<AlunoEVO> alunosAvaliar = new ArrayList<>();

    public static void main(String[] args) {
        try {
            RelatorioPagamentoEngenharia processo = new RelatorioPagamentoEngenharia();
            processo.inicializar();
            processo.processarArquivo();

            processo.consultarInformacoes();
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    private void consultarInformacoes() throws SQLException {
        if (alunosAvaliar.isEmpty()) {
            return;
        }


        final String sqlConsultar = "select situacao, descricao from movparcela \n" +
                "where pessoa IN (select pessoa.codigo \n" +
                "               from pessoa \n" +
                "               inner join cliente cli on pessoa.codigo = cli.pessoa \n" +
                "               where cfp = '%s')\n" +
                "and datavencimento = '%s';";

        for (AlunoEVO aluno : alunosAvaliar) {
            String sqlFormatted = String.format(sqlConsultar, aluno.getCpf(), aluno.getDataVencimento());
            try (PreparedStatement stm = conZW.prepareStatement(sqlFormatted)) {
                try (ResultSet rs = stm.executeQuery()) {
                    if (rs.next()) {
                        String resSituacao = rs.getString("situacao");
                        aluno.setPagamentoRecebido(resSituacao.equals("PG"));
                        aluno.setDescricaoParcela(rs.getString("descricao"));
                    }
                }
            }
        }

        for (AlunoEVO aluno : alunosAvaliar) {
            if (aluno.getPagamentoRecebido()) {
                System.out.println(aluno);
            }
        }

    }


    private void inicializar() throws SQLException {
        this.urlCSVAlunos = "C:/opt/Ativo_SPC_MATRIZ.csv";
        conZW = DriverManager.getConnection("****************************************************************************", "postgres", "pactodb");
    }

    private void processarArquivo() throws IOException, ParseException {
        alunosAvaliar = new ArrayList<>();

        NumberFormat nf = NumberFormat.getInstance(new Locale("pt", "BR"));


        try (BufferedReader reader = new BufferedReader(new FileReader(urlCSVAlunos))) {
            String line;
            int lineCount = 0;
            while ((line = reader.readLine()) != null) {
                lineCount++;
                if (lineCount == 1) {
                    continue;
                }
                String[] values = line.split(";");

                String cpf = values[0];
                String nomeConsumidor = values[1];
                String dataVencimento = values[3];
                BigDecimal valorDebito = new BigDecimal(nf.parse(values[4]).toString());
                String dataInclusao = values[5];

                alunosAvaliar.add(new AlunoEVO(cpf, nomeConsumidor, dataVencimento, valorDebito, dataInclusao));
            }
        }
    }
}

class AlunoEVO {

    private String cpf;
    private String nomeConsumidor;
    private String dataVencimento;
    private BigDecimal valorDebito;
    private String dataInclusao;
    private boolean pagamentoRecebido;
    private String descricaoParcela;

    public AlunoEVO(String cpf, String nomeConsumidor, String dataVencimento, BigDecimal valorDebito, String dataInclusao) {
        this.cpf = cpf;
        this.nomeConsumidor = nomeConsumidor;
        this.dataVencimento = dataVencimento;
        this.valorDebito = valorDebito;
        this.dataInclusao = dataInclusao;
    }

    public String getCpf() {
        return cpf;
    }

    public void setCpf(String cpf) {
        this.cpf = cpf;
    }

    public String getNomeConsumidor() {
        return nomeConsumidor;
    }

    public void setNomeConsumidor(String nomeConsumidor) {
        this.nomeConsumidor = nomeConsumidor;
    }

    public String getDataVencimento() {
        return dataVencimento;
    }

    public void setDataVencimento(String dataVencimento) {
        this.dataVencimento = dataVencimento;
    }

    public BigDecimal getValorDebito() {
        return valorDebito;
    }

    public void setValorDebito(BigDecimal valorDebito) {
        this.valorDebito = valorDebito;
    }

    public String getDataInclusao() {
        return dataInclusao;
    }

    public void setDataInclusao(String dataInclusao) {
        this.dataInclusao = dataInclusao;
    }

    public boolean getPagamentoRecebido() {
        return pagamentoRecebido;
    }

    public void setPagamentoRecebido(boolean pagamentoRecebido) {
        this.pagamentoRecebido = pagamentoRecebido;
    }

    public String getDescricaoParcela() {
        if (descricaoParcela == null) {
            descricaoParcela = "";
        }
        return descricaoParcela;
    }

    public void setDescricaoParcela(String descricaoParcela) {
        this.descricaoParcela = descricaoParcela;
    }

    @Override
    public String toString() {
        return cpf + ";" + nomeConsumidor + ";" + getDescricaoParcela();
    }
}


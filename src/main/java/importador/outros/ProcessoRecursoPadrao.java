package importador.outros;

import br.com.pactosolucoes.enumeradores.TipoInfoMigracaoEnum;
import br.com.pactosolucoes.oamd.controle.basico.DAO;
import negocio.comuns.arquitetura.UsuarioVO;
import negocio.comuns.utilitarias.Calendario;
import negocio.comuns.utilitarias.Uteis;
import negocio.facade.jdbc.arquitetura.SuperFacadeJDBC;
import negocio.facade.jdbc.arquitetura.Usuario;
import org.json.JSONObject;
import servicos.propriedades.PropsService;
import servicos.util.ExecuteRequestHttpService;

import java.io.File;
import java.io.IOException;
import java.sql.Connection;
import java.sql.DriverManager;
import java.sql.ResultSet;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;

public class ProcessoRecursoPadrao {

    private static Map<String, StringBuilder> mapaLogGravar;
    private static String nomeBanco = "";
    private static String logGeral = "GERAL";

    public static void main(String[] args) throws IOException {
        Date d1 = Calendario.hoje();
        try {
            Connection con = DriverManager.getConnection("******************************************************", "postgres", "pactodb");
            nomeBanco = con.getCatalog();

            String chave = DAO.resolveKeyFromConnection(con);
//            String chave = "sesi";

            processarTelaAluno(chave, con);
        } catch (Exception ex) {
            ex.printStackTrace();
            adicionarLog(logGeral, ex.getMessage());
        } finally {
            Date d2 = Calendario.hoje();
            adicionarLog(logGeral, "=============================================");
            adicionarLog(logGeral, "Banco: " + nomeBanco);
            adicionarLog(logGeral, "Tempo Total: " + Calendario.diferencaEmSegundos(d1, d2) + " segundos");
            adicionarLog(logGeral, "=============================================");
            Uteis.salvarArquivo(ProcessoRecursoPadrao.class.getSimpleName() + "_" + nomeBanco + "-" + Calendario.getData("yyyyMMddHHmmss") + ".txt",
                    getLogGravar(logGeral).toString(), "C:\\Processos\\" + File.separator);
        }
    }

    public static String processarTelaAluno(String chave, Connection con) throws Exception {
        Usuario usuarioDAO;
        Date d1 = Calendario.hoje();
        Integer total = 0;
        try {
            usuarioDAO = new Usuario(con);

            boolean configTreino = obterConfigTreino(chave);
            if (!configTreino) {
                return "Configuração do treino DESATIVAR_TELA_ALUNO_TREINO não está marcada";
            }

            UsuarioVO usuarioVO = usuarioDAO.getUsuarioRecorrencia();

            StringBuilder sql = new StringBuilder();
            sql.append("select \n");
            sql.append("distinct \n");
            sql.append("u.codigo as usuario \n");
            sql.append("from usuario u \n");
            sql.append("inner join colaborador c1 on c1.codigo = u.colaborador \n");
            sql.append("where not exists(select codigo from infomigracao where tipoinfo = ").append(TipoInfoMigracaoEnum.TELA_ALUNO.getId()).append(" and usuario = u.codigo) \n");
            sql.append("and exists(select t.codigo \n");
            sql.append("		from tipocolaborador t \n");
            sql.append("		inner join colaborador c2 on c2.codigo = t.colaborador \n");
            sql.append("		where t.descricao = 'TW' \n");
            sql.append("		and c2.situacao = 'AT' \n");
            sql.append("		and c1.pessoa = c2.pessoa) \n");
            sql.append("order by 1 ");

            total = SuperFacadeJDBC.contar("SELECT COUNT(*) FROM (" + sql + ") as qtd", con);
            ResultSet rs = SuperFacadeJDBC.criarConsulta(sql.toString(), con);
            while (rs.next()) {
                try {
                    usuarioDAO.gravarRecurso(TipoInfoMigracaoEnum.TELA_ALUNO, rs.getInt("usuario"), "true", null, usuarioVO);
                } catch (Exception ex) {
                    ex.printStackTrace();
                }
            }
        } catch (Exception ex) {
            ex.printStackTrace();
            throw ex;
        } finally {
            usuarioDAO = null;
        }
        Date d2 = Calendario.hoje();
        return "Total: " + total + " - Tempo Total: " + Calendario.diferencaEmSegundos(d1, d2) + " segundos";
    }

    private static boolean obterConfigTreino(String chave) throws IOException {
//        String urlTreino = "http://host.docker.internal:8201/TreinoWeb";
        String urlTreino = PropsService.getPropertyValue(chave, PropsService.urlTreino);
        String dados = ExecuteRequestHttpService.executeHttpRequestGETEncode(urlTreino + "/prest/config/"
                + chave + "/getconfigs/DESATIVAR_TELA_ALUNO_TREINO", new HashMap<>(), "UTF-8");
        JSONObject json = new JSONObject(dados).getJSONObject("return").getJSONArray("configuracoes").getJSONObject(0);
        return json.getBoolean("valor");
    }

    private static void adicionarLog(String keyMapa, String msg) {
        String s = "[DEBUG] " + Uteis.getDataAtualAplicandoFormatacao("dd/MM/yyyy hh:mm:ss") + " --> " + msg;
        System.out.println(s);
        StringBuilder logGravar = getLogGravar(keyMapa).append(s).append("\n");
        mapaLogGravar.put(keyMapa, logGravar);
    }

    private static StringBuilder getLogGravar(String keyMapa) {
        if (mapaLogGravar == null) {
            mapaLogGravar = new HashMap<>();
        }
        StringBuilder logGravar = mapaLogGravar.get(keyMapa);
        if (logGravar == null) {
            logGravar = new StringBuilder();
        }
        return logGravar;
    }
}

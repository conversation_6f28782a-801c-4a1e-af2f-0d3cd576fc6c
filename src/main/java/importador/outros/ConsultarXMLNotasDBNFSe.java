package importador.outros;

import br.com.pactosolucoes.comuns.util.Formatador;
import br.com.pactosolucoes.oamd.controle.basico.DAO;
import negocio.comuns.nfe.NotaFiscalDeServicoVO;
import negocio.comuns.utilitarias.Calendario;
import negocio.comuns.utilitarias.Uteis;
import negocio.facade.jdbc.arquitetura.SuperFacadeJDBC;
import negocio.facade.jdbc.nfe.NotaFiscalDeServico;
import org.apache.poi.hssf.usermodel.HSSFSheet;
import org.apache.poi.hssf.usermodel.HSSFWorkbook;
import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.CellStyle;
import org.apache.poi.ss.usermodel.CellType;
import org.apache.poi.ss.usermodel.FillPatternType;
import org.apache.poi.ss.usermodel.HorizontalAlignment;
import org.apache.poi.ss.usermodel.IndexedColors;
import org.apache.poi.ss.usermodel.Row;

import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.sql.Connection;
import java.sql.ResultSet;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class ConsultarXMLNotasDBNFSe {

    private static Map<String, StringBuilder> mapaLogGravar;
    private static String nomeBanco = "";
    private static String keyLogGeral = "GERAL";
    private static List<String> colunasCriar;
    private static HSSFSheet sheet;

    public static void main(String... args) throws IOException {
        Date d1 = Calendario.hoje();
        String nomeArquivo = (ConsultarXMLNotasDBNFSe.class.getSimpleName() + "_" + nomeBanco + "-" + Calendario.getData("yyyyMMddHHmmss"));
        try {
            DAO dao = new DAO();
            Connection con = dao.obterConexaoEspecifica("");
            NotaFiscalDeServico notaFiscalDeServico = new NotaFiscalDeServico(con);

            List<String> nomes = new ArrayList<>();
            nomes.add("Beatriz Guerrieri Saboya ");
            nomes.add("Hannah Peixoto Schechtman ");
            nomes.add("Marcela Teixeira Batista ");
            nomes.add("Danielle Pina Carvalho ");
            nomes.add("Thamara Bastos ");
            nomes.add("Douglas Vitoriano Locatelli ");
            nomes.add("Gabriel Pereira e Borges ");
            nomes.add("Ana Carolina de Andrade Lima ");
            nomes.add("Ana Paula Coalho ");
            nomes.add("Debora Caetano Oliveira ");
            nomes.add("Noah Meneses de Souza ");
            nomes.add("Daniel Rodrigues Nogueira Lemos ");
            nomes.add("Isabela Cesario Baldini ");
            nomes.add("Isabela Dantas da Rocha ");
            nomes.add("Dilze Teixeira ");
            nomes.add("Romildo Olgo Peixoto Junior ");
            nomes.add("Vera Lilian C. Costa Ravagnani ");
            nomes.add("Alceu Massatake Hayashi ");
            nomes.add("Erica Nishiyama ");
            nomes.add("Heloisa Marcia de Meneses Bailao ");
            nomes.add("Luiza Abreu ");
            nomes.add("Arthur Vilanova Oliveira ");

            int rowNumber = 0;
            HSSFWorkbook hssfWorkbook = criarTabela();

            for (String nome : nomes) {
                StringBuilder sql = new StringBuilder();
                sql.append("select \n");
                sql.append("r.id_rps  \n");
                sql.append("from rps r  \n");
                sql.append("inner join lote l on l.Id_Lote = r.Id_Lote \n");
                sql.append("where l.Id_Empresa = 6110 \n");
                sql.append("and r.RazaoSocialCons like '%").append(nome.trim()).append("%' \n");
                sql.append("order by r.DataEmissao \n");
                ResultSet rs = SuperFacadeJDBC.criarConsulta(sql.toString(), con);
                while (rs.next()) {
                    NotaFiscalDeServicoVO obj = notaFiscalDeServico.consultarPorChavePrimaria(rs.getInt("id_rps"), Uteis.NIVELMONTARDADOS_DADOSBASICOS);
                    String link = notaFiscalDeServico.obtenhaLinkXML(obj.getIdRPS(), false, false, false, false, false);
                    String dataEmissao = Calendario.getDataAplicandoFormatacao(obj.getDataEmissao(), "dd/MM/yyyy HH:mm:ss");
                    String valor = Formatador.formatarValorMonetario(obj.getValorTotal());
                    String dados = (obj.getRazaoSocialCons() + " | Dt. Emissão: " + dataEmissao + " | Valor: " + valor + " | Número Nota: " + obj.getNumeroNota() + " | Chave de Acesso: " + obj.getCodigoVerificacao() + " | Link XML: " + link);

                    int cellNumber = -1;
                    Row row = hssfWorkbook.getSheetAt(0).createRow(++rowNumber);
                    criarCelula(++cellNumber, row, obj.getRazaoSocialCons());
                    criarCelula(++cellNumber, row, dataEmissao);
                    criarCelula(++cellNumber, row, valor);
                    criarCelula(++cellNumber, row, obj.getNumeroNota());
                    criarCelula(++cellNumber, row, obj.getCodigoVerificacao());
                    criarCelula(++cellNumber, row, link);
                    adicionarLog(false, keyLogGeral, dados);
                }
            }

            try {
                File file = new File("C:\\Processos\\" + File.separator + nomeArquivo + ".xls");
                FileOutputStream out = new FileOutputStream(file);
                hssfWorkbook.write(out);
                out.close();
                hssfWorkbook.close();
            } catch (Exception ignored) {

            }
        } catch (Exception ex) {
            ex.printStackTrace();
            adicionarLog(true, keyLogGeral, ex.getMessage());
        } finally {
            Date d2 = Calendario.hoje();
            adicionarLog(true, keyLogGeral, "=============================================");
            adicionarLog(true, keyLogGeral, "Tempo Total: " + Calendario.diferencaEmSegundos(d1, d2) + " segundos");
            adicionarLog(true, keyLogGeral, "=============================================");
            Uteis.salvarArquivo(nomeArquivo + ".txt",
                    getLogGravar(keyLogGeral).toString(), "C:\\Processos\\" + File.separator);
        }
    }

    private static void adicionarLog(boolean adicionarData, String keyMapa, String msg) {
        String s = "[DEBUG] " + Uteis.getDataAtualAplicandoFormatacao("dd/MM/yyyy hh:mm:ss") + " --> " + msg;
        if (!adicionarData) {
            s = msg;
        }
        System.out.println(s);
        StringBuilder logGravar = getLogGravar(keyMapa).append(s).append("\n");
        mapaLogGravar.put(keyMapa, logGravar);
    }

    private static StringBuilder getLogGravar(String keyMapa) {
        if (mapaLogGravar == null) {
            mapaLogGravar = new HashMap<>();
        }
        StringBuilder logGravar = mapaLogGravar.get(keyMapa);
        if (logGravar == null) {
            logGravar = new StringBuilder();
        }
        return logGravar;
    }

    private static HSSFWorkbook criarTabela() {
        HSSFWorkbook hssfWorkbook = new HSSFWorkbook();
        sheet = hssfWorkbook.createSheet("Notas");

        int cellnum = 0;
        Row row;

        // Definindo alguns padroes de layout
        sheet.setDefaultColumnWidth(20);
        sheet.setDefaultRowHeight((short) 300);

        row = sheet.createRow(0);
        colunasCriar = new ArrayList<>();
        colunasCriar.add("Nome");
        colunasCriar.add("Data Emissão");
        colunasCriar.add("Valor");
        colunasCriar.add("Numero Nota");
        colunasCriar.add("Chave de Acesso");
        colunasCriar.add("Link XML");

        for (String coluna : colunasCriar) {
            cabecalho(hssfWorkbook, row, cellnum++, coluna);
        }

        return hssfWorkbook;
    }

    private static void criarCelula(int numeroCelula, Row row, Object valor) {
        Cell cell = row.createCell(numeroCelula);
        setValorCelula(cell, valor);
    }

    private static void criarCelula(int numeroCelula, Row row, Object valor, CellStyle cellStyle) {
        Cell cell = row.createCell(numeroCelula);
        setValorCelula(cell, valor);
        cell.setCellStyle(cellStyle);
    }

    private static void setValorCelula(Cell cell, Object valor) {
        if (valor instanceof String) {
            cell.setCellType(CellType.STRING);
            cell.setCellValue((String) valor);
        } else if (valor instanceof Integer) {
            cell.setCellType(CellType.NUMERIC);
            cell.setCellValue((Integer) valor);
        } else if (valor instanceof Double) {
            cell.setCellType(CellType.NUMERIC);
            cell.setCellValue(Uteis.arredondarForcando2CasasDecimais((Double) valor));
        } else if (valor instanceof Date) {
            cell.setCellValue((Date) valor);
        } else if (valor instanceof Boolean) {
            cell.setCellType(CellType.BOOLEAN);
            cell.setCellValue((Boolean) valor);
        } else {
            cell.setCellValue(valor == null ? "" : valor.toString());
        }
    }

    private static void cabecalho(HSSFWorkbook hssfWorkbook, Row row, int cellnum, String textoCelula) {
        CellStyle cellStyle = hssfWorkbook.createCellStyle();
        cellStyle.setFillForegroundColor(IndexedColors.GREY_25_PERCENT.getIndex());
        cellStyle.setFillPattern(FillPatternType.SOLID_FOREGROUND);
        cellStyle.setAlignment(HorizontalAlignment.CENTER);

        Cell cell = row.createCell(cellnum);
        cell.setCellStyle(cellStyle);
        cell.setCellValue(textoCelula);
    }
}




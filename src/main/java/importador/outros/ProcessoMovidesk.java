package importador.outros;

import negocio.comuns.utilitarias.Uteis;
import org.apache.http.HttpStatus;
import org.json.JSONArray;
import org.json.JSONObject;
import servicos.http.MetodoHttpEnum;
import servicos.http.RequestHttpService;
import servicos.http.RespostaHttpDTO;

import java.sql.Connection;
import java.sql.DriverManager;
import java.sql.PreparedStatement;
import java.sql.SQLException;
import java.util.HashMap;
import java.util.Map;

public class ProcessoMovidesk {

    public static void main(String[] args) {
        try {
            Connection con = DriverManager.getConnection("*****************************************", "postgres", "pactodb");
            processar(con);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    private static void processar(Connection con) throws InterruptedException {
        boolean processar = true;
        Integer skip = 161000; //161000
        Map<Integer, Integer> mapaSkip = new HashMap<>();
        while (processar) {
            try {
                Uteis.logarDebug("Iniciando consulta | Skip " + skip);

                String url = "https://api.movidesk.com/public/v1/tickets";
                Map<String, String> header = new HashMap<>();
                header.put("Content-Type", "application/json;");

                Map<String, String> params = new HashMap<>();
                params.put("token", "0a8af88b-d27b-49dc-a2e8-ece5c2724847");
                params.put("$select", "id,protocol,type,subject,category,urgency,status,baseStatus,justification,origin,createdDate,originEmailAccount,owner,ownerTeam,createdBy,serviceFull,serviceFirstLevelId,serviceFirstLevel,serviceSecondLevel,serviceThirdLevel,contactForm,tags,cc,resolvedIn,reopenedIn,closedIn,lastActionDate,actionCount,lastUpdate,lifetimeWorkingTime,stoppedTime,stoppedTimeWorkingTime,resolvedInFirstCall,chatWidget,chatGroup,chatTalkTime,chatWaitingTime,sequence,slaAgreement,slaAgreementRule,slaSolutionTime,slaResponseTime,slaSolutionChangedByUser,slaSolutionChangedBy,slaSolutionDate,slaSolutionDateIsPaused,slaResponseDate,slaRealResponseDate,jiraIssueKey,redmineIssueId,clients,actions,parentTickets,childrenTickets,ownerHistories,statusHistories,satisfactionSurveyResponses,customFieldValues,assets");
                params.put("$expand", "clients($expand=organization),actions,ownerHistories,satisfactionSurveyResponses,owner,createdBy,actions($expand=createdBy)");
                params.put("$orderby", "id desc");
                if (skip != 0) {
                    params.put("$skip", skip.toString());
                }

                RequestHttpService service = new RequestHttpService();
                RespostaHttpDTO respostaHttpDTO = service.executeRequest(url, header, params, null, MetodoHttpEnum.GET);
                service = null;

                if (!respostaHttpDTO.getHttpStatus().equals(HttpStatus.SC_OK)) {
                    Uteis.logarDebug("Status | " + respostaHttpDTO.getHttpStatus() + " | " + respostaHttpDTO.getResponse());

                    Integer tentativas = mapaSkip.get(skip);
                    if (tentativas == null) {
                        tentativas = 1;
                    } else {
                        tentativas += 1;
                    }
                    mapaSkip.put(skip, tentativas);

                    if (tentativas >= 5) {
                        Uteis.logarDebug("Vou pular skip | " + skip + " | Tentativas " + tentativas);
                        skip += 1000;
                    }

                    continue;
                }
                JSONArray array = new JSONArray(respostaHttpDTO.getResponse());

                Uteis.logarDebug("Total registros | " + array.length());

                if (array.length() == 0) {
                    //acabou
                    processar = false;
                    continue;
                }

                for (int i = 0; i < array.length(); i++) {
                    try {
                        JSONObject json = array.getJSONObject(i);
                        inserirBanco(json, con);
                    } catch (Exception ex) {
                        ex.printStackTrace();
                    }
                }
                skip += 1000;
                Uteis.logarDebug("Fim consulta | Skip " + skip);
            } catch (Exception ex) {
                ex.printStackTrace();
                Uteis.logarDebug("Erro | " + ex.getMessage());
            } finally {
                Thread.sleep(60000);
            }
        }
    }

    private static void inserirBanco(JSONObject json, Connection con) throws SQLException {
        String insert = "insert into tickets(id,dados) values (?,?);";
        PreparedStatement pst = con.prepareStatement(insert);
        pst.setInt(1, json.optInt("id"));
        pst.setString(2, json.toString());
        pst.execute();
    }
}

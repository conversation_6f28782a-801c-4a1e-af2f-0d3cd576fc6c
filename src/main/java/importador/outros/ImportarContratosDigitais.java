package importador.outros;


import negocio.comuns.contrato.ContratoAssinaturaDigitalVO;
import negocio.comuns.utilitarias.Uteis;
import negocio.facade.jdbc.contrato.ContratoAssinaturaDigital;
import java.io.BufferedInputStream;
import java.io.ByteArrayOutputStream;
import java.io.FileOutputStream;
import java.io.InputStream;
import java.net.URL;
import java.sql.Connection;
import java.sql.DriverManager;
import java.util.Date;
import java.util.List;

/***
 * <AUTHOR>
 * @since 27/09/2019
 */
public class ImportarContratosDigitais {
    public static void main (String[] args){
        try {
            String path = "C:\\opt\\ZW_ARQ\\relatoriogeralcliente\\fotos\\";
            Connection connection = obterConexao("localhost", "5432", "bdzillyon4livese");
            importarContratosDigitais(path, connection);
        } catch (Exception e) {
            Uteis.logar(e, ImportarContratosDigitais.class);

        }
    }
    public static void importarContratosDigitais(String path,Connection connection) throws Exception{
        System.out.println("-----INICIO IMPORTACAO DAS ASSINATURAS DIGITAIS-----");
        ContratoAssinaturaDigital contratoAssinaturaDigital = new ContratoAssinaturaDigital(connection);
        if(connection == null){
            throw new Exception("Não foi possivel conectar no banco");
        }
        //Boleano assinados somente se o contrato tiver assinatura preenchida
        List<ContratoAssinaturaDigitalVO> contratos = contratoAssinaturaDigital.consultarTodosContratosDigitais(Uteis.NIVELMONTARDADOS_CONSULTA_DADOS_FOTOS_IMPORTADOR, true);
        int i = 1;
        for (ContratoAssinaturaDigitalVO contrato : contratos) {
            String uri = Uteis.getPaintFotoDaNuvem(contrato.getAssinatura());
            uri = uri + (uri.contains("?") ? "&time=" : "?time=") + new Date().getTime();

            URL url = new URL(uri);
            InputStream in = new BufferedInputStream(url.openStream());
            ByteArrayOutputStream out = new ByteArrayOutputStream();
            byte[] buf = new byte[1024];
            int n = 0;
            while (-1 != (n = in.read(buf))) {
                out.write(buf, 0, n);
            }
            out.close();
            in.close();
            byte[] response = out.toByteArray();
            FileOutputStream fos = new FileOutputStream(path+"Numero Contrato"+contrato.getContrato().getCodigo()+".png");
            System.out.println("Assinatura digital: "+path+contrato.getContrato().getCodigo()+".png - Baixada");
            fos.write(response);
            fos.close();
            i++;
        }
        System.out.println("-----TERMINO DA IMPORTACAO DAS ASSINATURAS DIGITAIS TOTAL DE :"+ i +"  IMAGENS BAIXADAS-----");
        /*File arquivos = new File("C:\\opt\\ZW_ARQ\\relatoriogeralcliente\\fotos\\");
        File[] fotosOrigem = arquivos.listFiles();
        for (File file : fotosOrigem) {
            for (ContratoAssinaturaDigitalVO contrato : contratos) {

                if(file.getName().equals(contrato.getContrato().getCodigo()+".png") ){
                    System.out.println("ESMEAQUI: "+contrato.getContrato().getCodigo() + " COD.ASSI: "+contrato.getCodigo() +" NOME ARQ: "+file.getName());
                }else{
                    System.out.println("ESNAOMEAQUI: "+contrato.getContrato().getCodigo()+ " COD.ASSI: "+contrato.getCodigo()+" NOME ARQ: "+file.getName());
                    break;
                }
            }

        }*/

    }
    public static Connection obterConexao(String hostBD, String porta, String nomeBD) throws Exception {
        String userBD = "postgres";
        String passwordBD = "pactodb";
        String url = "jdbc:postgresql://" + hostBD + ":" + porta + "/" + nomeBD;
        String driver = "org.postgresql.Driver";
        Class.forName(driver);
        return DriverManager.getConnection(url, userBD, passwordBD);
    }
}

package importador.outros;

import br.com.pactosolucoes.enumeradores.TipoColaboradorEnum;
import importador.LeitorExcel;
import negocio.comuns.arquitetura.UsuarioVO;
import negocio.comuns.basico.ClienteVO;
import negocio.comuns.basico.ColaboradorVO;
import negocio.comuns.basico.VinculoVO;
import negocio.comuns.utilitarias.Uteis;
import negocio.facade.jdbc.arquitetura.FacadeManager;
import negocio.facade.jdbc.utilitarias.Conexao;
import org.apache.poi.hssf.usermodel.HSSFRow;

import java.sql.Connection;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * Created by glauco on 08/10/2018.
 */
public class ImportadorVinculosUnic {

    public static Integer COLUNA_MATRICULA = 0;
    public static Integer COLUNA_PROFESSOR = 4;
    public static Integer COLUNA_CONSULTOR = 5;

    public static void main(String[] args) {
        try {
            Connection connection = new Conexao("*********************************************************************", "postgres", "pactodb").getConexao();
            Conexao.guardarConexaoForJ2SE("unic", connection);

            String pathExcel = "/opt/ORGANIZAÇÃO DE CARTEIRAS.xls";
            int indexSheet = 1;
            int codEmpresa = 1;
            TipoColaboradorEnum tipoColaboradorImportar = TipoColaboradorEnum.PROFESSOR;

            if (tipoColaboradorImportar.equals(TipoColaboradorEnum.CONSULTOR)) {
                importarVinculos(pathExcel, indexSheet, connection, codEmpresa, tipoColaboradorImportar);
            } else if (tipoColaboradorImportar.equals(TipoColaboradorEnum.PROFESSOR)) {
                importarVinculosProfessor(pathExcel, indexSheet, connection, codEmpresa, tipoColaboradorImportar);
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    private static void importarVinculosProfessor(String pathExcel, int indexSheet, Connection connection, int codEmpresa, TipoColaboradorEnum tipoColaboradorImportar) throws SQLException {
        try {
            System.out.println("INICIO IMPORTACAO - PROFESSORES");
            connection.setAutoCommit(false);

            UsuarioVO usuarioAdmin = obterUsuarioAdmin();

            Map<String, ColaboradorVO> mapaColaboradores = new HashMap<String, ColaboradorVO>();
            Map<ColaboradorVO, List<VinculoVO>> mapaVinculosAntigos = new HashMap<ColaboradorVO, List<VinculoVO>>();


            List<HSSFRow> linhasPlano = LeitorExcel.lerLinhas(pathExcel, indexSheet);
            int i = 0;
            for (HSSFRow linha : linhasPlano) {
                System.out.println("Lendo " + ++i + " de " + linhasPlano.size());
                String matricula = LeitorExcel.obterString(linha, COLUNA_MATRICULA);
                String nomeColaborador = LeitorExcel.obterString(linha, COLUNA_PROFESSOR);

                ColaboradorVO colaboradorVO = mapaColaboradores.get(nomeColaborador);
                colaboradorVO = inicializarColaboradorMapa(codEmpresa, tipoColaboradorImportar, mapaColaboradores, nomeColaborador, colaboradorVO);

                ClienteVO clienteVO = FacadeManager.getFacade().getCliente().consultarPorCodigoMatricula(Integer.parseInt(matricula), codEmpresa, Uteis.NIVELMONTARDADOS_CONSULTA_WS);

                List<VinculoVO> vinculosOrigem = mapaVinculosAntigos.get(colaboradorVO);
                if (vinculosOrigem == null) {
                    vinculosOrigem = new ArrayList<VinculoVO>();
                    mapaVinculosAntigos.put(colaboradorVO, vinculosOrigem);
                }

                if (colaboradorVO == null) {
                    List<VinculoVO> vinculos = FacadeManager.getFacade().getVinculo().consultarPorClienteTipoVinculo(clienteVO.getCodigo(), tipoColaboradorImportar.getSigla(), Uteis.NIVELMONTARDADOS_MINIMOS);
                    vinculosOrigem.addAll(vinculos);
                } else {
                    VinculoVO vinculoProfessor = new VinculoVO();
                    vinculoProfessor.setCliente(clienteVO);
                    vinculoProfessor.setColaborador(colaboradorVO);
                    vinculoProfessor.setTipoVinculo(TipoColaboradorEnum.PROFESSOR.getSigla());

                    vinculosOrigem.add(vinculoProfessor);
                }
            }

            for (ColaboradorVO colaboradorVO : mapaVinculosAntigos.keySet()) {
                List<VinculoVO> vinculosOrigem = mapaVinculosAntigos.get(colaboradorVO);
                if (colaboradorVO != null) {
                    FacadeManager.getFacade().getVinculo().incluirVinculos(vinculosOrigem, "PROCESSO - IMPORTADOR VINCULOS", usuarioAdmin);
                } else {
                    if (vinculosOrigem.size() > 0) {
                        FacadeManager.getFacade().getVinculo().removerVinculos(vinculosOrigem, "PROCESSO - IMPORTADOR VINCULOS", usuarioAdmin, true);
                    }
                }
            }

            connection.commit();
            System.out.println("IMPORTACAO REALIZADA COM SUCESSO");
        } catch (Exception e) {
            connection.rollback();
            System.out.println("ERRO NA IMPORTACAO.");
            e.printStackTrace();
        } finally {
            connection.setAutoCommit(true);
            connection.close();
        }
    }

    private static ColaboradorVO inicializarColaboradorMapa(int codEmpresa, TipoColaboradorEnum tipoColaboradorImportar, Map<String, ColaboradorVO> mapaColaboradores, String nomeColaborador, ColaboradorVO colaboradorVO) throws Exception {
        if (colaboradorVO == null) {
            List<ColaboradorVO> colaboradores = FacadeManager.getFacade().getColaborador().consultarPorNomeTipoColaborador(nomeColaborador, codEmpresa, false, true, Uteis.NIVELMONTARDADOS_CONSULTA_WS, tipoColaboradorImportar);
            if (colaboradores.size() == 1) {
                colaboradorVO = colaboradores.get(0);
                mapaColaboradores.put(nomeColaborador, colaboradorVO);
            }
        }
        return colaboradorVO;
    }

    private static UsuarioVO obterUsuarioAdmin() {
        UsuarioVO usuarioAdmin = new UsuarioVO();
        usuarioAdmin.setCodigo(1);
        return usuarioAdmin;
    }

    private static void importarVinculos(final String pathExcel_plano, int indexSheet, Connection connection, final int empresa, final TipoColaboradorEnum tipoColaboradorImportar) throws Exception {
        try {
            System.out.println("INICIO IMPORTACAO");
            connection.setAutoCommit(false);

            UsuarioVO usuarioAdmin = obterUsuarioAdmin();

            Map<String, ColaboradorVO> mapaColaboradores = new HashMap<String, ColaboradorVO>();
            Map<ColaboradorVO, List<VinculoVO>> mapaVinculosAntigos = new HashMap<ColaboradorVO, List<VinculoVO>>();

            List<HSSFRow> linhasPlano = LeitorExcel.lerLinhas(pathExcel_plano, indexSheet);
            int i = 0;
            for (HSSFRow linha : linhasPlano) {
                System.out.println("Lendo " + ++i + " de " + linhasPlano.size());
                String matricula = LeitorExcel.obterString(linha, COLUNA_MATRICULA);
                String nomeColaborador = LeitorExcel.obterString(linha, COLUNA_CONSULTOR);

                ColaboradorVO colaboradorVO = mapaColaboradores.get(nomeColaborador);
                colaboradorVO = inicializarColaboradorMapa(empresa, tipoColaboradorImportar, mapaColaboradores, nomeColaborador, colaboradorVO);

                ClienteVO clienteVO = FacadeManager.getFacade().getCliente().consultarPorCodigoMatricula(Integer.parseInt(matricula), empresa, Uteis.NIVELMONTARDADOS_CONSULTA_WS);

                List<VinculoVO> vinculos = FacadeManager.getFacade().getVinculo().consultarPorClienteTipoVinculo(clienteVO.getCodigo(), tipoColaboradorImportar.getSigla(), Uteis.NIVELMONTARDADOS_MINIMOS);
                List<VinculoVO> vinculosOrigem = mapaVinculosAntigos.get(colaboradorVO);
                if (vinculosOrigem == null) {
                    vinculosOrigem = new ArrayList<VinculoVO>();
                    mapaVinculosAntigos.put(colaboradorVO, vinculosOrigem);
                }
                vinculosOrigem.addAll(vinculos);
                i++;
            }

            for (ColaboradorVO colaboradorVO : mapaVinculosAntigos.keySet()) {
                if (colaboradorVO != null) {
                    List<VinculoVO> vinculosOrigem = mapaVinculosAntigos.get(colaboradorVO);
                    FacadeManager.getFacade().getVinculo().transferirVinculos(vinculosOrigem, colaboradorVO, "PROCESSO - IMPORTADOR VINCULOS", usuarioAdmin, new ArrayList<Integer>());
                }
            }

            connection.commit();
            System.out.println("IMPORTACAO REALIZADA COM SUCESSO");
        } catch (Exception e) {
            connection.rollback();
            System.out.println("ERRO NA IMPORTACAO.");
            e.printStackTrace();
        } finally {
            connection.setAutoCommit(true);
            connection.close();
        }
    }
}

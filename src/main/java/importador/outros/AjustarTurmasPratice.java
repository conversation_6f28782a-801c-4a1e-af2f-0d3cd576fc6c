/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package importador.outros;

import importador.LeitorExcel;
import static importador.outros.ImportadorContratoPratique.obterConexao;
import java.sql.Connection;
import java.sql.ResultSet;
import java.util.List;

import negocio.comuns.utilitarias.Calendario;
import negocio.comuns.utilitarias.Uteis;
import negocio.facade.jdbc.arquitetura.SuperFacadeJDBC;
import negocio.facade.jdbc.utilitarias.Conexao;
import org.apache.poi.hssf.usermodel.HSSFRow;

/**
 *
 * <AUTHOR>
 */
public class AjustarTurmasPratice {
    
    public static void main(String[] args){
        try{
            String pathExcel_turma = "C:\\pacto\\xml\\PATINHO.xls";


            Connection connection = obterConexao("localhost","5432","BDTestePratique" );
            ajustarMatriculas(pathExcel_turma, connection);
        }catch (Exception e){
            e.printStackTrace();
        }
    }
    
    private static void ajustarMatriculas(String pathExcel_turma, Connection con)throws Exception{
        Conexao.guardarConexaoForJ2SE("pratique", con);
        Integer matriculaExterna = 0;
        Integer vezesPorSemana = 0;
        Integer modalidade = 0;
        Integer turma = 0;
        String diasSemana = "";
        String horaInicial = "";
        Integer toleranciaOcupacao = 15; //verifiquei na configuracao da empresa
        try{
            System.out.println("INICIO IMPORTACAO");
            con.setAutoCommit(false);

            List<HSSFRow> linhasTurma = LeitorExcel.lerLinhas(pathExcel_turma);
            int i = 0;
            for (HSSFRow linha : linhasTurma) {
                matriculaExterna = (Integer) LeitorExcel.obterNumero(linha, 0).intValue();
                modalidade = (Integer) LeitorExcel.obterNumero(linha, 9).intValue();
                vezesPorSemana = Integer.parseInt(LeitorExcel.obterString(linha, 6).trim().replace("X", ""));
                
                ResultSet consulta = SuperFacadeJDBC.criarConsulta("select c.codigo as contrato,c.vigenciaateajustada,cm.codigo as contratomodalidade, c.pessoa,c.pessoa,cmt.codigo as contratoModalidadeturma  from contrato c "
                        + " inner join contratomodalidade cm on cm.contrato = c.codigo inner  join contratomodalidadeturma cmt on cmt.contratomodalidade = cm.codigo  where c.pessoa in(\n" +
                "select pessoa from cliente  where matriculaexterna = "+matriculaExterna+"   and situacao = 'AT'\n" +
                ") and c.situacao = 'AT'  and cm.modalidade = "+modalidade+" and cm.vezessemana  = "+ vezesPorSemana, con);
                if(consulta.next()){
                    turma = (Integer) LeitorExcel.obterNumero(linha, 8).intValue();
                    horaInicial = Uteis.gethoraHHMMFormatado(LeitorExcel.obterDataNoFormatoData(linha, 4));
                    SuperFacadeJDBC.executarConsultaUpdate("update contratomodalidadeturma  set turma = "+turma+"  where codigo = " + consulta.getInt("contratoModalidadeturma"), con);
                    SuperFacadeJDBC.executarConsultaUpdate("delete from contratomodalidadehorarioturma  where contratomodalidadeturma  = " + consulta.getInt("contratoModalidadeturma"), con);
                    SuperFacadeJDBC.executarConsultaUpdate("update matriculaalunohorarioturma set datafim = '"+Uteis.getDataFormatoBD(Uteis.somarDias(Calendario.hoje(), -1))+"' where contrato = " + consulta.getInt("contrato"), con);
                    if(vezesPorSemana == 3){
                        diasSemana  = "('SG','QA','SX')";
                    } else if (vezesPorSemana == 2){
                        diasSemana  = "('TR','QI')";
                    } else if (vezesPorSemana == 1){
                        diasSemana  = "('SB')";
                    } else {
                        throw new Exception("Não foi informado a quantidade de vezes para o aluno  de matricula externa"+matriculaExterna);
                    }
                    ResultSet consultaTurmas = SuperFacadeJDBC.criarConsulta("select codigo as horarioturma from horarioturma  where turma = "+turma+" and horainicial = '"+horaInicial+"' and diasemana in "+diasSemana, con);
                    while(consultaTurmas.next()){
                        SuperFacadeJDBC.executarConsultaUpdate("INSERT INTO contratomodalidadehorarioturma(horarioturma, contratomodalidadeturma) VALUES ("+consultaTurmas.getInt("horarioturma")+" , "+consulta.getInt("contratoModalidadeturma")+");",con);
                        SuperFacadeJDBC.executarConsultaUpdate("INSERT INTO matriculaalunohorarioturma(empresa, pessoa, contrato, datainicio, datafim, horarioturma) VALUES (1, "+consulta.getInt("pessoa")+", "+consulta.getInt("contrato")+", '"+Uteis.getDataFormatoBD(Calendario.hoje())+"', '"+Uteis.getDataFormatoBD(Uteis.somarDias(consulta.getDate("vigenciaateajustada"),toleranciaOcupacao))+"', "+consultaTurmas.getInt("horarioturma")+");",con);                        
                    }
                    System.out.println("contrato "+consulta.getInt("contrato")+" foi ajustado");
                }
                
                
            }
            con.commit();
            System.out.println("IMPORTACAO REALIZADA COM SUCESSO");

        }catch (Exception e){
            con.rollback();
            System.out.println("ERRO NA IMPORTACAO.");
            e.printStackTrace();
        } finally {
            con.setAutoCommit(true);
            con.close();
        }
    }
    
}

package importador.outros;

import edu.emory.mathcs.backport.java.util.Arrays;
import importador.LeitorExcel2010;
import negocio.comuns.financeiro.PlanoContaTO;
import negocio.comuns.financeiro.enumerador.TipoES;
import negocio.comuns.financeiro.enumerador.TipoEquivalenciaDRE;
import negocio.comuns.utilitarias.Calendario;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisValidacao;
import negocio.facade.jdbc.arquitetura.SuperFacadeJDBC;
import negocio.facade.jdbc.financeiro.PlanoConta;
import negocio.facade.jdbc.utilitarias.Conexao;
import org.apache.poi.xssf.usermodel.XSSFRow;

import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;
import java.sql.Connection;
import java.sql.DriverManager;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

public class ImportadorPlanoContasEngenharia {

    private static StringBuilder logGravar;
    private static String nomeBanco = "";
    private static List<String> falhas = new ArrayList<>();
    // Indices colunas planilha

    public static void main(String[] args) throws Exception {
        Connection con = DriverManager.getConnection("**************************************", "postgres", "pactodb");
        String arquivoPlanoContasNovo = "C:\\Pacto\\engenharia\\novo_PLANO_DE_CONTAS.xlsx";
        String arquivoDePara = "C:\\Pacto\\engenharia\\pacto_de_para.xlsx";
        importarPlanoConta(con, arquivoPlanoContasNovo, arquivoDePara);

//        String arquivoDeParaInserirPlanoConta = "C:\\pacto\\backups\\Engenharia do Corpo\\planoconta\\pacto_de_para_inserir_plano_conta.xlsx";
//        Integer redeEmpresaId = 0;
//        String[] chaves = new String[]{
//                "engenhariadocorpoaracaju",
//        };
//        inserirPlanoContaRedeEmpresa(redeEmpresaId, chaves, arquivoDeParaInserirPlanoConta);
    }

    private static void importarPlanoConta(Connection con, String arquivoPlanoContasNovo, String arquivoDePara) throws SQLException, IOException {
        try {
            nomeBanco = con.getCatalog();

            adicionarLog("Parâmetros: ");
            adicionarLog("nomeBanco: " + nomeBanco);
            adicionarLog("arquivoPlanoContasNovo: " + arquivoPlanoContasNovo);
            adicionarLog("arquivoDePara: " + arquivoDePara);

            String dataHora = Calendario.getDataAplicandoFormatacao(Calendario.hoje(), "yyyyMMddHHmmss");
            String tabelaBackup = ("planoContas" + dataHora);
            String colunaBackup = ("planoContas" + dataHora);

            adicionarLog("tabelaBackup: " + tabelaBackup);
            adicionarLog("colunaBackup: " + colunaBackup);

            con.setAutoCommit(false);

            excluirPlanoAtual(tabelaBackup, colunaBackup, con);
            atualizarBanco(con);
            importarPlanoContas(arquivoPlanoContasNovo, TipoES.ENTRADA, con);
            importarPlanoContas(arquivoPlanoContasNovo, TipoES.SAIDA, con);
            atualizarBanco(con);
            atualizarContasAntigas(tabelaBackup, colunaBackup, arquivoDePara, con);

            con.commit();
        } catch (Exception ex) {
            ex.printStackTrace();
            adicionarLog(ex.getMessage());
            if (con != null) {
                con.rollback();
            }
        } finally {
            if (con != null) {
                con.setAutoCommit(true);
            }
            Uteis.salvarArquivo(ImportadorPlanoContasEngenharia.class.getSimpleName() + "_" + nomeBanco + "-" + Calendario.getData("yyyyMMddHHmmss") + ".txt", getLogGravar().toString(), "C:\\Processos\\" + File.separator);
        }
    }

    private static void excluirPlanoAtual(String tabelaBackup, String colunaBackup, Connection con) throws Exception {
        SuperFacadeJDBC.executarConsulta("create table " + tabelaBackup + " as table planoconta;", con);
        SuperFacadeJDBC.executarConsulta("alter table fornecedor add column " + colunaBackup + " integer;", con);
        SuperFacadeJDBC.executarConsulta("update fornecedor set " + colunaBackup + " = planoconta;", con);
        SuperFacadeJDBC.executarConsulta("update fornecedor set planoconta = null;", con);
        SuperFacadeJDBC.executarConsulta("alter table movcontarateio add column " + colunaBackup + " integer;", con);
        SuperFacadeJDBC.executarConsulta("update movcontarateio set " + colunaBackup + " = planoconta;", con);
        SuperFacadeJDBC.executarConsulta("update movcontarateio set planoconta = null;", con);

        SuperFacadeJDBC.executarConsulta("alter table configuracaofinanceiro add column " + colunaBackup + "_boleto integer;", con);
        SuperFacadeJDBC.executarConsulta("alter table configuracaofinanceiro add column " + colunaBackup + "_taxa integer;", con);
        SuperFacadeJDBC.executarConsulta("alter table configuracaofinanceiro add column " + colunaBackup + "_devolucao integer;", con);
        SuperFacadeJDBC.executarConsulta("update configuracaofinanceiro set " + colunaBackup + "_boleto = planocontastaxaboleto;", con);
        SuperFacadeJDBC.executarConsulta("update configuracaofinanceiro set " + colunaBackup + "_taxa = planocontastaxa;", con);
        SuperFacadeJDBC.executarConsulta("update configuracaofinanceiro set " + colunaBackup + "_devolucao = planocontasdevolucao;", con);
        SuperFacadeJDBC.executarConsulta("update configuracaofinanceiro set planocontastaxaboleto = null, planocontastaxa = null, planocontasdevolucao = null;", con);

        SuperFacadeJDBC.executarConsulta("alter table rateiointegracao add column " + colunaBackup + " integer;", con);
        SuperFacadeJDBC.executarConsulta("update rateiointegracao set " + colunaBackup + " = planoconta;", con);
        SuperFacadeJDBC.executarConsulta("update rateiointegracao set planoconta = null;", con);

        SuperFacadeJDBC.executarConsulta("delete from planoconta;", con);
    }

    private static void atualizarBanco(Connection con) {
        Set<String> atualiza = new HashSet<>();
        atualiza.add("DO $$ \n" +
                "    BEGIN\n" +
                "        BEGIN\n" +
                "            ALTER TABLE planoconta ADD COLUMN nomeconsulta text;\n" +
                "        EXCEPTION\n" +
                "            WHEN duplicate_column THEN RAISE NOTICE 'Coluna nomeconsulta já existe';\n" +
                "        END;\n" +
                "    END;\n" +
                "$$");
        atualiza.add("DO $$ \n" +
                "    BEGIN\n" +
                "        BEGIN\n" +
                "            ALTER TABLE planoconta ADD COLUMN importado BOOLEAN DEFAULT FALSE;\n" +
                "        EXCEPTION\n" +
                "            WHEN duplicate_column THEN RAISE NOTICE 'Coluna importado já existe';\n" +
                "        END;\n" +
                "    END;\n" +
                "$$");
        atualiza.add("UPDATE planoconta SET nomeconsulta = remove_acento_upper(nome);");
        for (String update : atualiza) {
            try {
                SuperFacadeJDBC.executarConsulta(update, con);
            } catch (Exception ignored) {
            }
        }
    }

    private static PlanoContaTO consultarPlanoDeContas(String codigoPlano, String nome, TipoES tipoES,
                                                       TipoEquivalenciaDRE tipoEquivalenciaDRE,
                                                       Connection con) throws Exception {
//        if (!codigoNome.trim().contains(" ")) {
//            throw new Exception("Plano De Conta incorreto: " + codigoNome);
//        }
//        String[] cod = codigoNome.split(" ");
//        String codigoPlano = cod[0].trim();
//        if (!UteisValidacao.somenteNumerosEPontos(codigoPlano)) {
//            throw new Exception("Plano De Conta incorreto: " + codigoNome);
//        }
        codigoPlano = ajustarCodigoPlano(codigoPlano);
//        String nome = codigoNome.replaceFirst(cod[0], "").trim();
        return ImportarFinanceiroUteis.consultarPlanoConta(codigoPlano, nome, tipoES, tipoEquivalenciaDRE, con, false);
    }

    private static String ajustarCodigoPlano(String codigo) {
        String ret = "";
        for (String cod : codigo.split("\\.")) {
            String novo = new String(cod);
            int add = (3 - cod.length());
            int atu = 0;
            while (atu < add) {
                novo = ("0" + novo);
                atu++;
            }
            ret += ("." + novo);
        }
        return ret.replaceFirst("\\.", "");
    }

    private static void importarPlanoContas(String arquivo, TipoES tipoES, Connection con) {
        try {
            List<XSSFRow> hssfRows = LeitorExcel2010.lerLinhas(new FileInputStream(arquivo), 0);
            for (XSSFRow linha : hssfRows) {
                try {
                    String codigo = "";
                    String descricao = "";
                    TipoEquivalenciaDRE tipoEquivalenciaDRE = null;
                    if (tipoES.equals(TipoES.ENTRADA)) {
                        tipoEquivalenciaDRE = TipoEquivalenciaDRE.RECEITA_BRUTA;
                        codigo = LeitorExcel2010.obterString(linha, 0);
                        descricao = LeitorExcel2010.obterString(linha, 1);
                    } else if (tipoES.equals(TipoES.SAIDA)) {
                        tipoEquivalenciaDRE = TipoEquivalenciaDRE.DESPESAS_OPERACIONAIS;
                        codigo = LeitorExcel2010.obterString(linha, 3);
                        descricao = LeitorExcel2010.obterString(linha, 4);
                    }

                    if (UteisValidacao.emptyString(codigo) ||
                            UteisValidacao.emptyString(descricao)) {
//                        adicionarLog("Vazio codigo | " + codigo);
//                        adicionarLog("Vazio descricao | " + descricao);
                        continue;
                    }

                    if (codigo.length() > 2 && codigo.substring(codigo.length() - 2, codigo.length()).equals(".0")) {
                        String novoCodigo = codigo.substring(0, codigo.length() - 2);
                        adicionarLog("Alterou codigo | " + codigo + " | Novo " + novoCodigo);
                        codigo = novoCodigo;
                    }

                    consultarPlanoDeContas(codigo.trim(), descricao.trim(), tipoES, tipoEquivalenciaDRE, con);
                } catch (Exception ex) {
                    ex.printStackTrace();
                }
            }
        } catch (Exception ex) {
            ex.printStackTrace();
        }
    }

    private static void adicionarLog(String msg) {
        String s = "[DEBUG] " + Uteis.getDataAtualAplicandoFormatacao("dd/MM/yyyy hh:mm:ss") + " --> " + msg;
        System.out.println(s);
        getLogGravar().append(s).append("\n");
    }

    private static StringBuilder getLogGravar() {
        if (logGravar == null) {
            logGravar = new StringBuilder();
        }
        return logGravar;
    }

    private static void atualizarContasAntigas(String tabelaBackup, String colunaBackup,
                                               String arquivoDePara, Connection con) throws Exception {
        PlanoConta planoContaDAO;
        try {
            planoContaDAO = new PlanoConta(con);

//            List<PlanoContaTO> listaPlano = planoContaDAO.consultarTodos();

            List<XSSFRow> hssfRows = LeitorExcel2010.lerLinhas(new FileInputStream(arquivoDePara), 0);
            Integer sucesso = 0;
            Integer erro = 0;
            Integer vazio = 0;
            for (XSSFRow linha : hssfRows) {
                try {

                    String nomePlanoAnterior = LeitorExcel2010.obterString(linha, 0);
                    String nomePlanoNovo = LeitorExcel2010.obterString(linha, 1);
                    String tipo = LeitorExcel2010.obterString(linha, 2).trim();
                    TipoES tipoES = TipoES.getPorSigla(tipo);

                    if (UteisValidacao.emptyString(nomePlanoNovo) ||
                            UteisValidacao.emptyString(nomePlanoAnterior)) {
//                        adicionarLog("Vazio nomePlanoAnterior | " + nomePlanoAnterior);
//                        adicionarLog("Vazio nomePlanoNovo | " + nomePlanoNovo);
                        ++vazio;
                        continue;
                    }

                    adicionarLog("Buscar plano de contas equivalente para | " + nomePlanoAnterior + " | Novo | " + nomePlanoNovo);

                    Integer codigoAnterior = obterCodigoPlanoAnterior(tabelaBackup, nomePlanoAnterior, tipoES, con);
                    if (UteisValidacao.emptyNumber(codigoAnterior)) {
                        throw new Exception("Não foi encontrado codigoAnterior | " + nomePlanoAnterior);
                    }
                    Integer codigoNovo = obterCodigoPlanoNovo(nomePlanoNovo, tipoES, con);
                    if (UteisValidacao.emptyNumber(codigoNovo)) {
                        throw new Exception("Não foi encontrado codigoNovo | " + nomePlanoNovo);
                    }

                    adicionarLog("Alterar plano de contas | Plano Anterior | " + nomePlanoAnterior + " | Plano Novo | " + nomePlanoNovo);
                    adicionarLog("Alterar plano de contas | Código Anterior | " + codigoAnterior + " | Codigo Novo | " + codigoNovo);

                    SuperFacadeJDBC.executarConsulta("update fornecedor set planoconta = "+codigoNovo+" where " + colunaBackup + " = " + codigoAnterior, con);
                    SuperFacadeJDBC.executarConsulta("update movcontarateio set planoconta = " + codigoNovo + " where " + colunaBackup + " = " + codigoAnterior, con);
                    SuperFacadeJDBC.executarConsulta("update configuracaofinanceiro set planocontastaxaboleto = " + codigoNovo + " where " + colunaBackup + "_boleto = " + codigoAnterior, con);
                    SuperFacadeJDBC.executarConsulta("update configuracaofinanceiro set planocontastaxa = " + codigoNovo + " where " + colunaBackup + "_taxa = " + codigoAnterior, con);
                    SuperFacadeJDBC.executarConsulta("update configuracaofinanceiro set planocontasdevolucao = " + codigoNovo + " where " + colunaBackup + "_devolucao = " + codigoAnterior, con);
                    SuperFacadeJDBC.executarConsulta("update rateiointegracao set planoconta = " + codigoNovo + " where " + colunaBackup + " = " + codigoAnterior, con);
                    ++sucesso;
                } catch (Exception ex) {
                    adicionarLog("ERRO: " + ex.getMessage());
                    ++erro;
                }
            }

            adicionarLog("FIM MIGRAR PLANO DE CONTAS");
            adicionarLog("Total: " + hssfRows.size());
            adicionarLog("Sucesso: " + sucesso);
            adicionarLog("Erro: " + erro);
            adicionarLog("Vazio: " + vazio);
        } finally {
            planoContaDAO = null;
        }
    }

    private static Integer obterCodigoPlanoAnterior(String tabelaBackup, String nomePlanoAnterior, TipoES tipoES, Connection con) throws Exception {
        String sqlBuscaAnterior = "select * from " + tabelaBackup + " where upper(trim(nome)) = upper(trim('" + nomePlanoAnterior + "')) and tipoes = " + tipoES.getCodigo();
        Integer total = SuperFacadeJDBC.contar("select count(*) as qtd from ( " + sqlBuscaAnterior + " ) as sql", con);
        if (UteisValidacao.emptyNumber(total)) {
            throw new Exception("Não foi encontrado plano anterior para | " + nomePlanoAnterior);
        }
        if (total > 1) {
            throw new Exception("Foi encontrado " + total + " para o plano anterior | " + nomePlanoAnterior);
        }
        ResultSet rs = SuperFacadeJDBC.criarConsulta(sqlBuscaAnterior, con);
        if (rs.next()) {
            return rs.getInt("codigo");
        }
        return null;
    }

    private static Integer obterCodigoPlanoNovo(String nomePlanoNovo, TipoES tipoES, Connection con) throws Exception {
        String sqlBuscaAnterior = "select * from planoconta where upper(trim(nome)) = upper(trim('" + nomePlanoNovo + "')) and tipoes = " + tipoES.getCodigo();
        Integer total = SuperFacadeJDBC.contar("select count(*) as qtd from ( " + sqlBuscaAnterior + " ) as sql", con);
        if (UteisValidacao.emptyNumber(total)) {
            throw new Exception("Não foi encontrado plano novo para | " + nomePlanoNovo);
        }
        if (total > 1) {
            throw new Exception("Foi encontrado " + total + " para o plano novo | " + nomePlanoNovo);
        }
        ResultSet rs = SuperFacadeJDBC.criarConsulta(sqlBuscaAnterior, con);
        if (rs.next()) {
            return rs.getInt("codigo");
        }
        return null;
    }

    private static void inserirPlanoContaRedeEmpresa(Integer redeEmpresaId, String[] chaves, String arquivoDeParaInserirPlanoConta) throws Exception {
        Connection conOAMD = DriverManager.getConnection("*************************************", "postgres", "pactodb");
        falhas = new ArrayList<>();
        int sucesso = 0;

        if (UteisValidacao.emptyNumber(redeEmpresaId) && chaves.length == 0) {
            throw new Exception("A rede empresa id ou as chaves das empresa devem ser informadas!");
        }

        String sqlEmpresas = "select distinct e.* from empresa e \n" +
                " left join empresafinanceiro ef on ef.chavezw = e.chave \n" +
                "where e.ativa is true \n ";

        if (!UteisValidacao.emptyNumber(redeEmpresaId)) {
            sqlEmpresas += " and ef.redeempresa_id = " + redeEmpresaId + " \n";
        }

        if (chaves != null && chaves.length > 0) {
            List<String> keys = Arrays.asList(chaves);
            sqlEmpresas += "and e.chave in (" + keys.stream().map(c -> "'" + c + "'").collect(Collectors.joining(",")) + ")  \n";
        }

        ResultSet rs = SuperFacadeJDBC.criarConsulta(sqlEmpresas, conOAMD);

        int count = 0;
        int total = SuperFacadeJDBC.contar("select count(sql.*) from (" + sqlEmpresas + ") as sql", conOAMD);

        if (total == 0) {
            throw new Exception("Nenhuma empresa encontrada para o redeEmpresaId + " + redeEmpresaId);
        }

        List<Map<String, Object>> planosContas = obterListaPlanosContasArquivo(arquivoDeParaInserirPlanoConta);

        while(rs.next()) {
            String host = rs.getString("hostBD");
            String porta = rs.getString("porta");
            String nomeBD = rs.getString("nomeBD");
            String user = rs.getString("userBD");
            String passWord = rs.getString("passwordBD");
            String chave = rs.getString("chave");

            try (Connection con = DriverManager.getConnection("jdbc:postgresql://" + host + ":" + porta + "/" + nomeBD, user, passWord)) {
                Conexao.guardarConexaoForJ2SE(con);
                try {
                    adicionarLog(String.format("%d\\%d [%s] - Processando unidade: %s", ++count, total, nomeBD, rs.getString("identificadorempresa")));
                    inserirPlanoConta(con, chave, planosContas);
                } catch (Exception e) {
                    falhas.add(String.format("%s;%s;%s", nomeBD, chave, e.getMessage()));
                    e.printStackTrace();
                }
            }
        }
        adicionarLog("TOTAL DE FALHAS: " + falhas.size());

        if (falhas.size() > 0) {
            String arquivoFalha = arquivoDeParaInserirPlanoConta.substring(0, arquivoDeParaInserirPlanoConta.lastIndexOf(File.separator));
            String logFalhas = "nomeBD;chave;falha\n";
            for (String f : falhas) {
                logFalhas += f + "\n";
            }
            Uteis.salvarArquivo("log-falhas-incluid-plano-conta-" + Calendario.getData("yyyyMMddHHmmss") + ".csv", logFalhas, arquivoFalha + File.separator);
        }

    }

    private static List<Map<String, Object>> obterListaPlanosContasArquivo(String arquivoDeParaInserirPlanoConta) throws Exception {
        List<Map<String, Object>> planosContas = new ArrayList<>();

        Integer columnCodigoPlanoContasPai = 0; // 001 ou 001.002 etc...
        Integer columnNomePlanoContaPai = 1; // Nome do plano pai
        Integer columnCodigoPlanoContaAdicionar = 2; // 001.002
        Integer columnNomePlanoContaAdicionar = 3;
        Integer columnTipoES = 4; // ENTRADA OU SAIDA

        List<XSSFRow> hssfRows = LeitorExcel2010.lerLinhas(new FileInputStream(arquivoDeParaInserirPlanoConta), 0);
        for (XSSFRow linha : hssfRows) {

            String nomePlanoContaAdicionar = LeitorExcel2010.obterString(linha, columnNomePlanoContaAdicionar);
            String codigoPlanoContaAdicionar = LeitorExcel2010.obterString(linha, columnCodigoPlanoContaAdicionar);
            TipoES tipoES = TipoES.getByName(LeitorExcel2010.obterString(linha, columnTipoES));
            TipoEquivalenciaDRE tipoEquivalenciaDRE = tipoES.equals(TipoES.ENTRADA) ? TipoEquivalenciaDRE.RECEITA_BRUTA : TipoEquivalenciaDRE.DESPESAS_OPERACIONAIS;
            String codigoPlanoContasPai = LeitorExcel2010.obterString(linha, columnCodigoPlanoContasPai);
            String nomePlanoContaPai = LeitorExcel2010.obterString(linha, columnNomePlanoContaPai);

            if (UteisValidacao.emptyString(codigoPlanoContasPai)) {
                throw new Exception("Código do plano de contas pai não informado na linha: " + linha.getRowNum());
            }
            if (codigoPlanoContasPai.length() < 3) {
                throw new Exception(String.format("Código do plano de contas pai inválido: %s na linha: %d de ter 3 caracteres, EX: 001", codigoPlanoContaAdicionar, linha.getRowNum()));
            }
            if (UteisValidacao.emptyString(nomePlanoContaPai)) {
                throw new Exception("Nome do plano de contas pai não informado na linha: " + linha.getRowNum());
            }
            if (UteisValidacao.emptyString(nomePlanoContaAdicionar)) {
                throw new Exception("Nome do plano de contas não informado na linha: " + linha.getRowNum());
            }
            if (!UteisValidacao.emptyString(codigoPlanoContaAdicionar) && (codigoPlanoContaAdicionar.length() <= 3 || !codigoPlanoContaAdicionar.contains(codigoPlanoContasPai))) {
                throw new Exception(String.format("Código do plano de contas inválido: %s na linha: %d - deve estar no padrão: codigoplanopai.codigoplano Ex: 001.001", codigoPlanoContaAdicionar, linha.getRowNum()));
            }
            if (tipoES == null) {
                throw new Exception("Tipo de plano de contas não informado ou invalido na linha: " + linha.getRowNum());
            }

            Map<String, Object> mapaPlanosContas = new HashMap<>();
            mapaPlanosContas.put("nomePlanoContaAdicionar", nomePlanoContaAdicionar);
            mapaPlanosContas.put("codigoPlanoContaAdicionar", codigoPlanoContaAdicionar);
            mapaPlanosContas.put("tipoES", tipoES);
            mapaPlanosContas.put("tipoEquivalenciaDRE", tipoEquivalenciaDRE);
            mapaPlanosContas.put("codigoPlanoContasPai", codigoPlanoContasPai);
            mapaPlanosContas.put("nomePlanoContaPai", nomePlanoContaPai);

            planosContas.add(mapaPlanosContas);
        }
        return planosContas;
    }

    private static void inserirPlanoConta(Connection con, String chave, List<Map<String, Object>> planosContas) throws Exception {
        PlanoConta planoContaDAO = new PlanoConta(con);

        int atual = 0;
        int total = planosContas.size();

        for (Map<String, Object> mapaPlanoConta : planosContas) {
            try {
                con.setAutoCommit(false);

                String codigoPlanoContaAdicionar = (String) mapaPlanoConta.get("codigoPlanoContaAdicionar");
                String nomePlanoContaAdicionar = (String) mapaPlanoConta.get("nomePlanoContaAdicionar");
                TipoES tipoES = (TipoES) mapaPlanoConta.get("tipoES");
                TipoEquivalenciaDRE tipoEquivalenciaDRE = (TipoEquivalenciaDRE) mapaPlanoConta.get("tipoEquivalenciaDRE");
                String codigoPlanoContasPai = (String) mapaPlanoConta.get("codigoPlanoContasPai");
                String nomePlanoContaPai = (String) mapaPlanoConta.get("nomePlanoContaPai");

                adicionarLog(String.format("\t%d\\%d - Processando plano conta: %s", ++atual, total, nomePlanoContaAdicionar));

                PlanoContaTO planoContaPaiTO = planoContaDAO.consultarPorCodigoPlanoTipo(codigoPlanoContasPai, tipoES);
                if (UteisValidacao.emptyNumber(planoContaPaiTO.getCodigo())) {
                    throw new Exception(String.format("\t\tPlano de contas pai: %s - %s não encontrado", codigoPlanoContasPai, nomePlanoContaPai));
                }

                if (!planoContaPaiTO.getDescricao().toLowerCase().equals(nomePlanoContaPai.toLowerCase())) {
                    throw new Exception(String.format("\t\tO plano de contas pai: %s - %s, está com nome diferente nessa unidade: %s - %s",
                            codigoPlanoContasPai, nomePlanoContaPai, planoContaPaiTO.getCodigoPlano(), planoContaPaiTO.getDescricao()));
                }

                String sqlPlanoFilho = "select pc.codigo, pc.codigoplanocontas, pc.nome from planoconta pc\n" +
                        "where upper(trim(pc.nome)) = upper(trim('" + nomePlanoContaAdicionar + "'))\n" +
                        "and pc.codigoplanocontas like '" + codigoPlanoContasPai + ".%'\n";
                ResultSet rsPlanoFilho = SuperFacadeJDBC.criarConsulta(sqlPlanoFilho, con);
                if (rsPlanoFilho.next()) {
                    continue;
                }

                if (!UteisValidacao.emptyString(codigoPlanoContaAdicionar)) {
                    PlanoContaTO planoContaTO = planoContaDAO.consultarPorCodigoPlanoTipo(codigoPlanoContaAdicionar, tipoES);
                    if (planoContaTO != null && !UteisValidacao.emptyNumber(planoContaTO.getCodigo())) {
                        if (planoContaTO.getDescricao().toLowerCase().equals(nomePlanoContaAdicionar.toLowerCase())) {
                            continue;
                        } else {
                            String codigoProximoFilho = planoContaDAO.obterCodigoProximoFilho(codigoPlanoContasPai);
                            adicionarLog(String.format("\t\tAlterando codigo do plano de contas: %s - %s para %s", codigoPlanoContaAdicionar, nomePlanoContaAdicionar, codigoProximoFilho));
                            planoContaTO.setCodigoPlano(codigoProximoFilho);
                            planoContaDAO.alterar(planoContaTO);
                        }
                    }
                } else {
                    codigoPlanoContaAdicionar = planoContaDAO.obterCodigoProximoFilho(codigoPlanoContasPai);
                }

                PlanoContaTO planoContaTOInserir = new PlanoContaTO();
                planoContaTOInserir.setCodigoPlano(codigoPlanoContaAdicionar);
                planoContaTOInserir.setDescricao(nomePlanoContaAdicionar);
                planoContaTOInserir.setTipoPadrao(tipoES);
                planoContaTOInserir.setEquivalenciaDRE(tipoEquivalenciaDRE);
                planoContaTOInserir.setPlanoPai(planoContaPaiTO);
                planoContaDAO.incluirSemCommit(planoContaTOInserir);

                con.commit();
            } catch (Exception e) {
                con.rollback();
                adicionarLog(e.getMessage());
                falhas.add(String.format("%s;%s;%s", con.getCatalog(), chave, e.getMessage()));
            } finally {
                con.setAutoCommit(true);
            }
        }
    }
}




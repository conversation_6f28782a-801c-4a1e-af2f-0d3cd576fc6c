package importador.outros;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * Created by alcides on 30/08/2016.
 */
public class PessoaImp extends SuperZW{

    public Integer codigo;
    public Integer idExterno;
    public Date dataCadastro = new Date();
    public String nome = "";
    public Date dataNasc;
    public String nomePai = "";
    public String nomeMae = "";
    public String cfp = "";
    public String rg = "";
    public String rgOrgao = "";
    public String rgUf = "";
    public String estadoCivil = "";
    public String nacionalidade = "";
    public String naturalidade = "";
    public String sexo = "";
    public String webPage = "";
    public transient byte foto[];
    public List telefoneVOs = new ArrayList();
    public List emailVOs = new ArrayList();
    public List eventos = new ArrayList();

    @Override
    public String toString() {
        return "Pessoa{" +
                "codigo=" + codigo +
                ", nome='" + nome + '\'' +
                '}';
    }

    public static void main(String[] args) {
        String cpf = "024.280.541 - 80";
        cpf = cpf.replaceAll("\\s+", "");
        System.out.println(cpf);
    }

    public void setCpf(final String cpf) {
        if (cpf != null && !cpf.equals("___.___.___ - __") && !cpf.equals("? FORNECIDO")) {
            String cpf_tmp = cpf.replaceAll("\\s+", "");
            if (cpf_tmp.length() <= 14) {
                this.cfp = cpf_tmp;
            }
        }
    }

    public void setRg(final String rg) {
        if (rg != null && rg.matches("[0-9]*")) {
            this.rg = rg;
        }
    }

    public void setNacionalidade(final String nacionalidade) {
        if (nacionalidade != null && !nacionalidade.equals(" FORNECIDO") && nacionalidade.length() <= 20) {
            this.nacionalidade = nacionalidade;
        }
    }

    public void setNaturalidade(final String naturalidade) {
        if (naturalidade != null && !naturalidade.equals(" FORNECIDO") && naturalidade.length() <= 20) {
            this.naturalidade = naturalidade;
        }
    }

    public void setNomePai(final String nomePai) {
        if (nomePai != null && nomePai.length() > 3) {
            this.nomePai = nomePai;
        }
    }

    public void setNomeMae(final String nomeMae) {
        if (nomeMae != null && nomeMae.length() > 3) {
            this.nomeMae = nomeMae;
        }
    }

    public static Map<String, Integer> obterTodos(Connection con) throws Exception {
        Map<String, Integer> mapaMatriculaCodigo = new HashMap<String, Integer>();
        String sql = "SELECT p.codigo, c.matriculaexterna FROM cliente c\n" +
                "INNER JOIN pessoa p ON p.codigo = c.pessoa";
        PreparedStatement ps = con.prepareStatement(sql);
        ResultSet rs = ps.executeQuery();
        while (rs.next()) {
            mapaMatriculaCodigo.put(rs.getString("matriculaexterna"), rs.getInt("codigo"));
        }
        return mapaMatriculaCodigo;
    }

    public PreparedStatement psIncluir(Connection con) throws Exception {
        String sql = "INSERT INTO pessoa(idexterno, datacadastro, nome, datanasc, nomepai, nomemae, cfp, rg, nacionalidade, naturalidade, sexo, foto) \n" +
                "VALUES(?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?);";
        int i = 0;
        PreparedStatement ps = con.prepareStatement(sql);
        ps.setInt(++i, this.idExterno);
        ps.setDate(++i, getDataJDBC(this.dataCadastro));
        ps.setString(++i, this.nome);
        ps.setDate(++i, getDataJDBC(this.dataNasc));
        ps.setString(++i, this.nomePai);
        ps.setString(++i, this.nomeMae);
        ps.setString(++i, this.cfp);
        ps.setString(++i, this.rg);
        ps.setString(++i, this.nacionalidade);
        ps.setString(++i, this.naturalidade);
        ps.setString(++i, this.sexo);
//        ps.setBytes(++i, this.foto);
        ps.setNull(++i, 0);
        return ps;
    }
}

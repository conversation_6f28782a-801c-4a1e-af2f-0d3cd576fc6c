package importador.outros;

import br.com.pactosolucoes.oamd.controle.basico.DAO;
import negocio.comuns.arquitetura.UsuarioVO;
import negocio.comuns.basico.ClienteVO;
import negocio.comuns.contrato.ContratoVO;
import negocio.comuns.financeiro.EstornoReciboVO;
import negocio.comuns.financeiro.ReciboPagamentoVO;
import negocio.comuns.financeiro.TransacaoVO;
import negocio.comuns.utilitarias.Calendario;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisValidacao;
import negocio.facade.jdbc.arquitetura.SuperFacadeJDBC;
import negocio.facade.jdbc.arquitetura.Usuario;
import negocio.facade.jdbc.arquitetura.ZillyonWebFacade;
import negocio.facade.jdbc.basico.Cliente;
import negocio.facade.jdbc.contrato.Contrato;
import negocio.facade.jdbc.financeiro.MovPagamento;
import negocio.facade.jdbc.financeiro.MovParcela;
import negocio.facade.jdbc.financeiro.ReciboPagamento;
import negocio.facade.jdbc.financeiro.Transacao;
import negocio.facade.jdbc.utilitarias.Conexao;
import relatorio.negocio.comuns.sad.SituacaoClienteSinteticoEnum;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.List;

public class ProcessoAtualizarCessaoContratoEngenharia {

    private final String key;
    private final boolean reabrirContratos;

    private Connection conZW;

    private ReciboPagamento reciboPagamentoDAO;
    private MovPagamento movPagamentoDAO;
    private MovParcela movParcelaDAO;
    private Cliente clienteDAO;
    private Contrato contratoDAO;
    private ZillyonWebFacade zwFacadeDAO;
    private Transacao transacaoDAO;
    private Usuario usuarioDAO;


    public ProcessoAtualizarCessaoContratoEngenharia(final String key, boolean reabrirContratos) {
        this.key = key;
        this.reabrirContratos = reabrirContratos;
    }

    public static void main(String[] args) {
        try {
            String keyParam = "bento";
            boolean reabrirContrato = false;
            if (args.length > 0) {
                keyParam = args[0];
                if (args.length > 1) {
                    reabrirContrato = Boolean.parseBoolean(args[1]);
                }
            }

            ProcessoAtualizarCessaoContratoEngenharia processo = new ProcessoAtualizarCessaoContratoEngenharia(keyParam, reabrirContrato);
            processo.inicializar();

            if (processo.reabrirContratos) {
                processo.reabrirContratosTransferidosCancelados();
//                processo.reabrirSomenteUmContrato(xxx);
            }

            List<ContratoParaCessao> contratosParaCessao = processo.consultarCessoesDireitoUso();
//            List<ContratoParaCessao> contratosParaCessao = processo.consultarCessoesDireitoUsoApenasUmContrato();
            processo.processarCessaoContrato(contratosParaCessao);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    private void processarCessaoContrato(List<ContratoParaCessao> contratosParaCessao) throws Exception {
        List<String> listSucesso = new ArrayList<>();
        List<String> listSemSucesso = new ArrayList<>();
        List<String> listDependentesPlanoDobro = new ArrayList<>();
        int qtdErros = 0;

        UsuarioVO usuarioAdmin = usuarioDAO.consultarPorChavePrimaria(1, Uteis.NIVELMONTARDADOS_MINIMOS);

        int qtdTotal = contratosParaCessao.size();
        for (ContratoParaCessao contratoParaCessao : contratosParaCessao) {
            try {
                ContratoVO contratoVOParaCeder = contratoDAO.consultarPorChavePrimaria(contratoParaCessao.getCodigoContrato(), Uteis.NIVELMONTARDADOS_DADOSBASICOS);
                ClienteVO clienteTitular = clienteDAO.consultarPorCodigoPessoa(contratoVOParaCeder.getPessoa().getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS);

                if (!UteisValidacao.emptyNumber(contratoVOParaCeder.getPessoaOriginal().getCodigo())) {
                    String msg = "Cessão do contrato %s realizada com sucesso.";
                    String msgFormatada = String.format(msg, contratoParaCessao.getCodigoContrato());
                    listSucesso.add(msgFormatada);
                    continue;
                }

                ClienteVO clienteDependente;
                if (contratoParaCessao.getMatriculaCederContrato() != null) {
                    clienteDependente = clienteDAO.consultarPorCodigoMatriculaExterna(Integer.parseInt(contratoParaCessao.getMatriculaCederContrato()), Uteis.NIVELMONTARDADOS_DADOSBASICOS);
                } else {
                    clienteDependente = clienteDAO.consultarPorNomeCliente(contratoParaCessao.getNomeCederContrato(), false, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
                }

                if (UteisValidacao.emptyNumber(clienteDependente.getCodigo())) {
                    String msg = "Não vincular cessão do contrato %s. Dependente não encontrado. Observação importada: %s";
                    String msgFormatada = String.format(msg, contratoParaCessao.getCodigoContrato(), contratoParaCessao.getObservacaoImportada());
                    listSemSucesso.add(msgFormatada);
                    continue;
                }

                if (!UteisValidacao.emptyNumber(clienteDependente.getTitularPlanoCompartilhado())) {
                    String msg = "Não vincular cessão do contrato %s. Dependente plano em dobro: %s %s";
                    String msgFormatada = String.format(msg, contratoParaCessao.getCodigoContrato(), clienteDependente.getMatricula(), clienteDependente.getNome_Apresentar());
                    listDependentesPlanoDobro.add(msgFormatada);
                    continue;
                }

                List<ContratoVO> contratosDependente = contratoDAO.consultarContratosVigentesPorPessoa(clienteDependente.getPessoa().getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS);
                ContratoVO contratoDependente = contratoDAO.consultarContratoVigentePorPessoa(clienteDependente.getPessoa().getCodigo(), false, false, Uteis.NIVELMONTARDADOS_DADOSBASICOS);

                if (contratosDependente.size() > 1) {
                    String msg = "Não vincular cessão do contrato %s. Dependente possui mais de 1 contrato ativo: %s %s";
                    String msgFormatada = String.format(msg, contratoParaCessao.getCodigoContrato(), clienteDependente.getMatricula(), clienteDependente.getNome_Apresentar());
                    listSemSucesso.add(msgFormatada);
                    continue;
                }

                if (contratoDependente.getValorFinal() > 0.0) {
                    String msg = "Não vincular cessão do contrato %s. Valor do contrato %d > R$ 0): %s %s";
                    String msgFormatada = String.format(msg, contratoParaCessao.getCodigoContrato(), contratoDependente.getCodigo(), clienteDependente.getMatricula(), clienteDependente.getNome_Apresentar());
                    listSemSucesso.add(msgFormatada);
                    continue;
                }

                List<TransacaoVO> transacoesContrato = transacaoDAO.consultarPorPessoa(clienteDependente.getPessoa().getCodigo());
                if (!transacoesContrato.isEmpty()) {
                    String msg = "Não vincular cessão do contrato %s. Dependente possui transações: %s %s - Titular: %s";
                    String msgFormatada = String.format(msg, contratoParaCessao.getCodigoContrato(), clienteDependente.getMatricula(), clienteDependente.getNome_Apresentar());
                    listSemSucesso.add(msgFormatada);
                    continue;
                }

                if (!UteisValidacao.emptyNumber(contratoDependente.getCodigo())) {
                    estornarContrato(contratoDependente, usuarioAdmin, conZW);
                }

                zwFacadeDAO.transferirDireitoDeUso(contratoVOParaCeder, clienteDependente, usuarioAdmin, false);

                zwFacadeDAO.atualizarSintetico(clienteDependente, Calendario.hoje(), SituacaoClienteSinteticoEnum.GRUPO_TODOS, false);

                zwFacadeDAO.atualizarSintetico(clienteTitular, Calendario.hoje(), SituacaoClienteSinteticoEnum.GRUPO_TODOS, false);

                String msg = "Cessão do contrato %s realizada com sucesso. Titular %s %s - Dependente %s %s";
                String msgFormatada = String.format(msg,
                        contratoParaCessao.getCodigoContrato(),
                        clienteTitular.getMatricula(), clienteTitular.getNome_Apresentar(),
                        clienteDependente.getMatricula(), clienteDependente.getNome_Apresentar());
                listSucesso.add(msgFormatada);
            } catch (Exception ex) {
                qtdErros++;
                ex.printStackTrace();
            }
        }


        Uteis.logarDebug("### SUCESSO ###");
        listSucesso.forEach(Uteis::logarDebug);

        Uteis.logarDebug("### DEPENDENTES ###");
        listDependentesPlanoDobro.forEach(Uteis::logarDebug);

        Uteis.logarDebug("### NÃO VINCULADOS ###");
        listSemSucesso.forEach(Uteis::logarDebug);


        Uteis.logarDebug(String.format("\n" +
                "Total de dependentes analisados: %d\n" +
                "Vinculados com sucesso: %d\n" +
                "Vinculados ignorados: %d\n" +
                "Clientes que já são dependentes: %d\n" +
                "Erros: %d", qtdTotal, listSucesso.size(), listSemSucesso.size(), listDependentesPlanoDobro.size(), qtdErros));
    }

    private void estornarContrato(ContratoVO contratoVO, UsuarioVO usuarioAdmin, Connection conZW) throws Exception {
        ClienteVO cliente;
        contratoVO.setUsuarioVO(usuarioAdmin);

        List<ReciboPagamentoVO> listaReciboPagamento = reciboPagamentoDAO.consularReciboPagamParcelaPorCodigoContrato(contratoVO.getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS);
        if (!listaReciboPagamento.isEmpty()) {
            contratoVO.setMovParcelaVOs(new ArrayList<>());
            contratoVO.setListaEstornoRecibo(new ArrayList<>());
            for (ReciboPagamentoVO recibo : listaReciboPagamento) {
                EstornoReciboVO estornoRecibo = new EstornoReciboVO();
                estornoRecibo.setReciboPagamentoVO(recibo);
                estornoRecibo.setListaMovPagamento(movPagamentoDAO.consultarPorCodigoRecibo(recibo.getCodigo(), false, Uteis.NIVELMONTARDADOS_CAIXAPARAOPERADOR));
                estornoRecibo.setListaMovParcela(movParcelaDAO.consultarPorCodigoRecibo(recibo.getCodigo(), false, Uteis.NIVELMONTARDADOS_MINIMOS));

                //transações de cartão de crédito
                contratoVO.montarListaTransacoes(estornoRecibo.getListaMovParcela(), conZW);
                contratoVO.getListaEstornoRecibo().add(estornoRecibo);
            }

        } else {
            contratoVO.setMovParcelaVOs(movParcelaDAO.consultarPorContrato(contratoVO.getCodigo(), Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA));

            //transações de cartão de crédito
            contratoVO.montarListaTransacoes(contratoVO.getMovParcelaVOs(), conZW);
        }

        contratoVO.setPrecisaEstornarTransacoes(false);

        contratoVO.montarListaItensRemessa(movParcelaDAO.consultarPorContrato(contratoVO.getCodigo(), Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA), conZW);


        cliente = clienteDAO.consultarPorCodigoPessoa(contratoVO.getPessoa().getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS);
        try {
            contratoDAO.estornoContrato(contratoVO, cliente, null, null);
            zwFacadeDAO.atualizarSintetico(cliente, Calendario.hoje(), SituacaoClienteSinteticoEnum.GRUPO_TODOS, false);
            Uteis.logar(null, "Estornado com sucesso o contrato -> " + contratoVO.getCodigo() + " do cliente -> " + cliente.getCodigo());
        } catch (Exception e) {
            Uteis.logar(null, "Erro ao estornar contrato -> " + contratoVO.getCodigo() + " do cliente -> " + cliente.getCodigo());
            Uteis.logar(null, "Exceção -> " + e.getMessage());
            throw e;
        }

    }

    private void inicializar() throws Exception {
        DAO dao = new DAO();
        conZW = dao.obterConexaoEspecifica(key);
        Conexao.guardarConexaoForJ2SE(conZW);

        reciboPagamentoDAO = new ReciboPagamento(conZW);
        movPagamentoDAO = new MovPagamento(conZW);
        movParcelaDAO = new MovParcela(conZW);
        clienteDAO = new Cliente(conZW);
        contratoDAO = new Contrato(conZW);
        zwFacadeDAO = new ZillyonWebFacade(conZW);
        transacaoDAO = new Transacao(conZW);
        usuarioDAO = new Usuario(conZW);
    }

    public List<ContratoParaCessao> consultarCessoesDireitoUso() throws SQLException {
        String sql = "select importacao_observacao,\n" +
                "   c.codigo,\n" +
                "   c.pessoa,\n" +
                "   c.pessoaoriginal\n" +
                "from contrato c\n" +
                "inner join plano pl on c.plano = pl.codigo\n" +
                "where importacao_observacao like 'Transferido para%'\n" +
                "   and (pl.descricao not like '%DOBRO%' or (pl.descricao like '%DOBRO%' and c.valorfinal > 0))\n" +
                "   and vigenciaateajustada > now()\n" +
                "   and pessoaoriginal is null;";

        List<ContratoParaCessao> listaContratos = new ArrayList<>();
        try (PreparedStatement ps = conZW.prepareStatement(sql)) {
            try (ResultSet rs = ps.executeQuery()) {
                while (rs.next()) {
                    ContratoParaCessao contratoCeder = new ContratoParaCessao();

                    String observacoes = rs.getString("importacao_observacao");

                    String replaceObs = observacoes.replace("Transferido para: ", "");
                    String[] partObs = replaceObs.split(" - ");
                    String matriculaExternaClienteCessao = null;
                    String nomeClienteCessao = null;
                    if (partObs.length > 1) {
                        matriculaExternaClienteCessao = partObs[0].replace(" ", "");
                    } else {
                        nomeClienteCessao = partObs[0];
                    }

                    contratoCeder.setCodigoContrato(rs.getInt("codigo"));
                    contratoCeder.setMatriculaCederContrato(matriculaExternaClienteCessao);
                    contratoCeder.setNomeCederContrato(nomeClienteCessao);
                    contratoCeder.setObservacaoImportada(observacoes);

                    listaContratos.add(contratoCeder);
                }
            }
        }
        return listaContratos;
    }

    public List<ContratoParaCessao> consultarCessoesDireitoUsoApenasUmContrato() {
        List<ContratoParaCessao> listaContratos = new ArrayList<>();
        ContratoParaCessao contratoCeder = new ContratoParaCessao();
        contratoCeder.setCodigoContrato(40718);
        contratoCeder.setMatriculaCederContrato("429373");
        contratoCeder.setNomeCederContrato("JULIA ANTONIAZZI");
        contratoCeder.setObservacaoImportada("Transferido para: 429373 - JULIA ANTONIAZZI");
        listaContratos.add(contratoCeder);
        return listaContratos;
    }

    private void reabrirSomenteUmContrato(Integer codContrato)  throws Exception {
        String sqlContratosCedidosCancelados = "select * from contrato c\n" +
                "WHERE codigo = " + codContrato + ";";

        acaoReabrirContrato(sqlContratosCedidosCancelados);
    }

    private void reabrirContratosTransferidosCancelados() throws Exception {
        String sqlContratosCedidosCancelados = "select * from contrato c\n" +
                "   left join contratooperacao co on c.codigo = co.contrato and co.tipooperacao = 'CA' and co.dataoperacao > '2023-05-01'\n" +
                "where importacao_dt_fim_original > now()\n" +
                "and situacao = 'CA'\n" +
                "and importacao_observacao like 'Transferido para%'\n" +
                "and co.codigo is null;";

        acaoReabrirContrato(sqlContratosCedidosCancelados);
    }

    private void acaoReabrirContrato(String sqlContratosCedidosCancelados) throws Exception {
        List<ContratoVO> contratosCedidos;
        try (PreparedStatement ps = conZW.prepareStatement(sqlContratosCedidosCancelados)) {
            try (ResultSet rs = ps.executeQuery()) {
                contratosCedidos = Contrato.montarDadosConsulta(rs, Uteis.NIVELMONTARDADOS_DADOSBASICOS, conZW);
            }
        }

        Uteis.logarDebug(String.format("Serão reabertos %d contratos.", contratosCedidos.size()));

        String updateContrato = "UPDATE contrato\n" +
                "SET vigenciaateajustada = vigenciaate, situacao = 'AT'\n" +
                "WHERE codigo = %d";

        String updatePeriodoAcesso = "UPDATE periodoacessocliente p\n" +
                "SET datafinalacesso = '%s', pessoa = %d\n" +
                "WHERE contrato = %d";

        String deleteHistoricoCancelado = "DELETE FROM historicocontrato h\n" +
                "WHERE contrato = %d and tipohistorico = 'CA';";

        String updateHistoricoMatricula = "UPDATE historicocontrato h\n" +
                "SET datafinalsituacao = '%s'\n" +
                "where contrato = %d and tipohistorico = 'MA';";

        String deleteContratoOperacao = "DELETE FROM contratooperacao c\n" +
                "WHERE contrato = %d AND tipooperacao = 'CA';";

        String infoContratoReaberto = "Contrato %d reaberto;";

        for (ContratoVO contratoCedido : contratosCedidos) {
            SuperFacadeJDBC.executarConsultaUpdate(String.format(updateContrato,
                    contratoCedido.getCodigo()), conZW);

            SuperFacadeJDBC.executarConsultaUpdate(String.format(updatePeriodoAcesso,
                    Uteis.getDataJDBC(contratoCedido.getVigenciaAte()),
                    contratoCedido.getPessoa().getCodigo(),
                    contratoCedido.getCodigo()), conZW);

            SuperFacadeJDBC.executarConsultaUpdate(String.format(deleteHistoricoCancelado,
                    contratoCedido.getCodigo()), conZW);

            SuperFacadeJDBC.executarConsultaUpdate(String.format(updateHistoricoMatricula,
                    Uteis.getDataJDBC(contratoCedido.getVigenciaAte()),
                    contratoCedido.getCodigo()), conZW);

            SuperFacadeJDBC.executarConsultaUpdate(String.format(deleteContratoOperacao,
                    contratoCedido.getCodigo()), conZW);

            Uteis.logarDebug(String.format(infoContratoReaberto,
                    contratoCedido.getCodigo()));

            ClienteVO clienteVO = new ClienteVO();
            clienteVO.setPessoa(contratoCedido.getPessoa());
            zwFacadeDAO.atualizarSintetico(clienteVO, Calendario.hoje(), SituacaoClienteSinteticoEnum.GRUPO_TODOS, false);
        }
    }

}


package importador.outros;

import br.com.pactosolucoes.integracao.aragorn.uteis.ImportarTabelaAuxiliarImportacaoCartao;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisValidacao;
import negocio.facade.jdbc.arquitetura.SuperFacadeJDBC;
import org.json.JSONObject;

import java.sql.Connection;
import java.sql.DriverManager;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.ResultSetMetaData;

public class ProcessoPreencherTabelaAuxiliarImportacaoCartaoEVO {

    public static void main(String[] args) {
        try {
            Connection conZW = DriverManager.getConnection("************************************************************************************", "postgres", "pactodb");
            Connection conSQL = getConSQLServer("EdCriciuma");
            processar(conSQL, conZW);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    public static Connection getConSQLServer(String bd) {
        try {
            String driver = "net.sourceforge.jtds.jdbc.Driver";
            String conexao = "jdbc:jtds:sqlserver:";
            Class.forName(driver).newInstance();
            return DriverManager.getConnection(conexao + "//localhost/" + bd, "sa", "pactodb");
        } catch (Exception e) {
            e.printStackTrace();
            return null;
        }
    }

    private static void processar(Connection conSQL, Connection conZW) throws Exception {
//        select ID_CARTAO_CLIENTE, ID_CLIENTE, NM_IGUAL_CARTAO, NUMERO_CARTAO, MES_VENCIMENTO, ANO_VENCIMENTO, FL_ATIVO, CPF_TITULAR, CNPJ_TITULAR, * from CLIENTES_CARTOES
//        select * from CLIENTES_CARTOES

        //criar a tabela de auxiliar para evitar erro no sql caso utilize
        ImportarTabelaAuxiliarImportacaoCartao.criarTabela(conZW);

        StringBuilder sql = new StringBuilder();
        sql.append("select \n");
        sql.append("cl.NOME as nome_cliente_evo, \n");
        sql.append("cl.SNOME as snome_cliente_evo, \n");
        sql.append("cl.CPF as cpf_cliente_evo, \n");
        sql.append("cl.ID_CARTAO_PRINCIPAL, \n");
        sql.append("cc.* \n");
        sql.append("from CLIENTES_CARTOES cc \n");
        sql.append("inner join CLIENTES cl on cl.ID_CLIENTE = cc.ID_CLIENTE \n");

        Integer total = SuperFacadeJDBC.contar("select count(*) as qtd from ( " + sql + " ) as sql", conSQL);
        Uteis.logarDebug("Total Registros: " + total);

        sql.append("order by ID_CARTAO_CLIENTE \n");

        Integer atual = 0;
        try (PreparedStatement ps = conSQL.prepareStatement(sql.toString())) {
            try (ResultSet rs = ps.executeQuery()) {
                final ResultSetMetaData meta = rs.getMetaData();
                final int columnCount = meta.getColumnCount();
                while (rs.next()) {
                    String id_cartao = "";
                    try {
                        Uteis.logarDebug("Processando " + ++atual + "/" + total);
                        id_cartao = rs.getString("ID_CARTAO_CLIENTE");
                        String matricula = rs.getString("ID_CLIENTE");
                        String nome = rs.getString("nome_cliente_evo");
                        String sobreNome = rs.getString("snome_cliente_evo");
                        String nomeCompleto = (nome.trim() + (sobreNome != null ? (" " + sobreNome.trim()) : "")).trim();
                        String cartao = rs.getString("NUMERO_CARTAO");
                        String ultimos = cartao.length() >= 4 ? cartao.substring(cartao.length() - 4, cartao.length()) : "";
                        String titular = rs.getString("NM_IGUAL_CARTAO");
                        String cpf = rs.getString("CPF_TITULAR");
                        if (cpf == null || UteisValidacao.emptyString(cpf.trim())) {
                            cpf = rs.getString("cpf_cliente_evo");
                        }

                        JSONObject dados = new JSONObject();
                        for (int column = 1; column <= columnCount; ++column) {
                            try {
                                final String nomeColuna = meta.getColumnName(column);
                                final Object value = rs.getObject(column);
                                dados.put(nomeColuna.toLowerCase(), value);
                            } catch (Exception ex) {
                                ex.printStackTrace();
                            }
                        }

                        dados.put("isCartaoPrincipal", false);
                        Integer idCartaoCliente = rs.getInt("ID_CARTAO_CLIENTE");
                        Integer idCartaoPrincipal = rs.getInt("ID_CARTAO_PRINCIPAL");
                        if (!UteisValidacao.emptyNumber(idCartaoPrincipal)
                                && idCartaoPrincipal.equals(idCartaoCliente)) {
                            dados.put("isCartaoPrincipal", true);
                        }

                        String mesAnoVencimento = String.format("%02d/20%d",
                                rs.getInt("MES_VENCIMENTO"),
                                rs.getInt("ANO_VENCIMENTO"));
                        dados.put("MES_ANO_VENCIMENTO", mesAnoVencimento);

                        ImportarTabelaAuxiliarImportacaoCartao.incluir(matricula, nomeCompleto, cartao, ultimos, titular, id_cartao, "", dados.toString(), cpf, conZW);
                    } catch (Exception ex) {
                        ex.printStackTrace();
                        Uteis.logarDebug("Erro | " + id_cartao + " | " + ex.getMessage());
                    }
                }
            }
        }
    }
}

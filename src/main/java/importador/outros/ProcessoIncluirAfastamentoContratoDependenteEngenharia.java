package importador.outros;

import importador.UteisImportacao;
import negocio.comuns.arquitetura.UsuarioVO;
import negocio.comuns.basico.ContratoDependenteVO;
import negocio.comuns.contrato.AfastamentoContratoDependenteVO;
import negocio.comuns.contrato.JustificativaOperacaoVO;
import negocio.comuns.utilitarias.Calendario;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisValidacao;
import negocio.facade.jdbc.arquitetura.SuperFacadeJDBC;
import negocio.facade.jdbc.arquitetura.Usuario;
import negocio.facade.jdbc.basico.ContratoDependente;
import negocio.facade.jdbc.contrato.AfastamentoContratoDependente;
import org.json.JSONObject;

import java.io.File;
import java.sql.*;
import java.util.Date;

public class ProcessoIncluirAfastamentoContratoDependenteEngenharia {

    private static StringBuilder logGravar;
    private static String caminhoLog = "C:\\PactoJ\\INCLUIR_AFASTAMENTO_DEPENDENTES_ENGENHARIA\\Logs";
    private static String nomeBD;

    public static void main(String[] args) throws Exception {
        logGravar = new StringBuilder();

        try {
            Uteis.debug = true;

            Connection conEscava = DriverManager.getConnection("***************************************************", "postgres", "pactodb");
            Connection conZW = DriverManager.getConnection("***********************************************************************", "postgres", "pactodb");
            Integer codEmpresaZw = 1;
            String matriculasDependentes = "";

            nomeBD = conZW.getCatalog();

            processarAfastamentos(codEmpresaZw, matriculasDependentes, conZW, conEscava);

        } catch (Exception ex) {
            adicionarLog(ex.getMessage());
        } finally {
            File pathFileLogs = new File(caminhoLog);
            if (!pathFileLogs.exists()) {
                pathFileLogs.mkdirs();
            }

            Uteis.salvarArquivo("incluir-afastamento-dependentes" + nomeBD + "-" + Calendario.hoje().getTime() + ".txt", logGravar.toString(), caminhoLog + File.separator);
        }
    }

    private static void processarAfastamentos(Integer codEmpresa, String matriculaDependentes, Connection conZW, Connection conEscava) throws Exception {
        criarColunaIdExterno(conZW);

        ContratoDependente contratoDependenteDAO = new ContratoDependente(conZW);
        AfastamentoContratoDependente afastamentoDAO = new AfastamentoContratoDependente(conZW);
        Usuario usuarioDAO = new Usuario(conZW);
        UsuarioVO usuarioVOPactoBR = usuarioDAO.consultarPorUsername("PACTOBR", Uteis.NIVELMONTARDADOS_DADOSBASICOS);
        JustificativaOperacaoVO justificativaFerias = UteisImportacao.obterJustificativaOperacao(codEmpresa, "IMPORTAÇÃO AFASTAMENTO - FÉRIAS", "CR", conZW);
        JustificativaOperacaoVO justificativaAtestado = UteisImportacao.obterJustificativaOperacao(codEmpresa, "IMPORTAÇÃO AFASTAMENTO - ATESTADO", "AT", conZW);


        String sql = "select \n" +
                "\tcs.idsuspensao, \n" +
                "\tcs.idcliente,\n" +
                "\tcs.dados,\n" +
                "\t(con.contrato->>'inicio')::date as inicio,\n" +
                "\t(con.contrato->>'fim')::date as fim,\n" +
                "from contratossuspensoes cs\n" +
                "\tinner join contratos con on con.idclientecontrato = cs.idclientecontrato \n" +
                "\tinner join contratosinformacoes ci on ci.idclientecontrato = con.idclientecontrato\n" +
                "where ci.dados->>'observacoes' ilike '%Transfer_ncia de:%' \n" +
                "and lower(con.contrato->>'status') in ('ativo', 'active') \n";
        if (!UteisValidacao.emptyString(matriculaDependentes)) {
            sql += " and cs.idcliente in (" + matriculaDependentes + ") \n";
        }

        Integer total = SuperFacadeJDBC.contar("select count(sql.*) from (" + sql + ") as sql", conEscava);
        int count = 0;
        Integer sucesso = 0;
        Integer falha = 0;

        ResultSet rs = SuperFacadeJDBC.criarConsulta(sql.toString(), conEscava);
        while (rs.next()) {

            try {
                conZW.setAutoCommit(false);

                Integer idSuspensao = rs.getInt("idsuspensao");
                JSONObject jsonSuspensao = new JSONObject(rs.getString("dados"));
                Integer idClienteDependente = rs.getInt("idCliente");
                String motivo = jsonSuspensao.getString("motivo");
                String observacoes = rs.getString("observacoes");
                Integer idClienteTitular = Integer.parseInt(observacoes.split(" de: ")[1].split(" - ")[0].trim());
                Date dataInicioContrato = rs.getDate("inicio");
                String dtInicioContrato = Calendario.getDataAplicandoFormatacao(dataInicioContrato, "yyyy-MM-dd");
                Date dataFimContratoEvo = rs.getDate("fim");
                String dtFimContrato = Calendario.getDataAplicandoFormatacao(dataFimContratoEvo, "yyyy-MM-dd");

                adicionarLog(++count + "\\" + total + " - Processando suspensoes do idCliente: " + idClienteDependente);

                if (afastamentoJaFoiImportado(idSuspensao, conZW)) {
                    adicionarLog("\t Afastamento já foi importado (idSuspensao: " + idSuspensao + " motivo: " + motivo + ")");
                    sucesso++;
                    continue;
                }

                // Tentar encontrar o contrato dependente no adm
                String sqlContratoDependente = "select \n" +
                        "\tcd.codigo as contratodependente,\n" +
                        "\tcd.datainicio,\n" +
                        "\tcd.datafinalajustada\n" +
                        "from contratodependente cd\n" +
                        "\tinner join cliente cli_d on cli_d.codigo = cd.cliente \n" +
                        "\tinner join contrato ct on ct.codigo = cd.contrato \n" +
                        "\tinner join cliente cli_t on cli_t.codigo = cd.titular \n" +
                        "where 1 = 1\n" +
                        "and coalesce(ct.id_externo,0) <> 0\n" +
                        "and cli_d.matriculaexterna = " + idClienteDependente + "\n" +
                        "and cli_t.matriculaexterna = " + idClienteTitular + "\n" +
                        "and cd.datainicio BETWEEN '" + dtInicioContrato + "' and '" + dtFimContrato + "'";

                ResultSet rsContratoDependente = SuperFacadeJDBC.criarConsulta(sqlContratoDependente, conZW);
                Date dataFinalAjustadaPacto;
                Integer codigoContratoDependente;
                if (rsContratoDependente.next()) {
                    dataFinalAjustadaPacto = rsContratoDependente.getDate("datafinalajustada");
                    codigoContratoDependente = rsContratoDependente.getInt("contratodependente");
                    adicionarLog("\t Incluindo afastamento (idSuspensao: " + idSuspensao + " motivo: " + motivo + ")");

                    incluirAfastamento(conZW, jsonSuspensao, codigoContratoDependente, usuarioVOPactoBR, justificativaAtestado, justificativaFerias);
                } else {
                    throw new Exception("Contrato dependente não encontrado no Adm ou já possui afastamento");
                }

                // Atualizar datas contrato dependencia
                if (dataFimContratoEvo.after(dataFinalAjustadaPacto)) {
                    ContratoDependenteVO contratoDependenteVO = contratoDependenteDAO.findByCodigo(codigoContratoDependente).get();
                    contratoDependenteVO.setDataInicio(dataInicioContrato);
                    contratoDependenteVO.setDataFinal(dataFimContratoEvo);
                    contratoDependenteVO.setDataFinalAjustada(dataFimContratoEvo);
                    contratoDependenteDAO.alterar(contratoDependenteVO);
                }

                conZW.commit();
                sucesso++;
            } catch (Exception e) {
                falha++;
                adicionarLog("\tERRO: " + e.getMessage());
                conZW.rollback();
            }
        }

        adicionarLog("TOTAL SUCESSO: " + sucesso);
        adicionarLog("TOTAL FALHA: " + falha);
    }

    public static void incluirAfastamento(Connection con, JSONObject jsonSuspensao, Integer codigoContratoDependente, UsuarioVO usuarioVO, JustificativaOperacaoVO justificativaAtestado, JustificativaOperacaoVO justificativaFerias) throws Exception {
        AfastamentoContratoDependente afastamentoContratoDependenteDAO = new AfastamentoContratoDependente(con);
        AfastamentoContratoDependenteVO afastamentoVO = new AfastamentoContratoDependenteVO();
        afastamentoVO.setContratoDependenteVO(new ContratoDependenteVO());
        afastamentoVO.getContratoDependenteVO().setCodigo(codigoContratoDependente);
        afastamentoVO.setUsuarioVO(usuarioVO);
        afastamentoVO.setDataRegistro(Calendario.getDate("yyyy-MM-dd", jsonSuspensao.getString("inicioSuspensao").split("T")[0]));
        afastamentoVO.setDataInicio(Calendario.getDate("yyyy-MM-dd", jsonSuspensao.getString("inicioSuspensao").split("T")[0]));
        afastamentoVO.setDataTermino(Calendario.getDate("yyyy-MM-dd", jsonSuspensao.getString("fimSuspensao").split("T")[0]));
        afastamentoVO.setNrDiasSomar(jsonSuspensao.getInt("quantidadeDias"));
        afastamentoVO.setObservacao(jsonSuspensao.getString("motivo"));
        if (isAtestado(jsonSuspensao.getString("motivo"))) {
            afastamentoVO.setTipoAfastamento("AT");
            afastamentoVO.setJustificativa(justificativaAtestado.getCodigo());
        } else {
            afastamentoVO.setTipoAfastamento("CR");
            afastamentoVO.setJustificativa(justificativaFerias.getCodigo());
        }
        afastamentoContratoDependenteDAO.incluirSemCommit(afastamentoVO);
        preencherIdExternoAfastamento(afastamentoVO.getCodigo(), jsonSuspensao.getInt("idSuspensao"), con);
    }

    private static boolean afastamentoJaFoiImportado(Integer idExterno, Connection conZW) throws Exception {
        ResultSet rs = SuperFacadeJDBC.criarConsulta("select * from afastamentocontratodependente where idexterno = " + idExterno + ";", conZW);
        return rs.next();
    }

    public static void criarColunaIdExterno(Connection conZW) {
        try {
            SuperFacadeJDBC.executarConsulta("alter table afastamentocontratodependente add column idexterno integer", conZW);
        } catch (Exception ignore) {}
    }

    public static void preencherIdExternoAfastamento(Integer codigo, Integer idExterno, Connection conZW) throws SQLException {
        PreparedStatement pstm = conZW.prepareStatement("update afastamentocontratodependente set idexterno = ? where codigo = ?");
        pstm.setInt(1, idExterno);
        pstm.setInt(2, codigo);
        pstm.execute();
    }

    private static boolean isAtestado(String motivo) {
        motivo = motivo.toLowerCase();
        return motivo.contains("atestado") || motivo.contains("cirurgia")
                || motivo.contains("médico") || motivo.contains("medico")
                || motivo.contains("saúde") || motivo.contains("saude")
                || motivo.contains("doença") || motivo.contains("doença")
                || motivo.contains("lesão")  || motivo.contains("lesao")
                || motivo.contains("lesionou") || motivo.contains("machucou");
    }

    private static void adicionarLog(String msg) {
        String s = "[DEBUG] " + Uteis.getDataAplicandoFormatacao(Calendario.hoje(), "dd/MM/yyyy HH:mm:ss") + " --> " + msg;
        System.out.println(s);
        getLogGravar().append(s).append("\n");
    }

    private static StringBuilder getLogGravar() {
        if (logGravar == null) {
            logGravar = new StringBuilder();
        }
        return logGravar;
    }
}

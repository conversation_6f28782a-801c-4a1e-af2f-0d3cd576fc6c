package importador.outros;

import br.com.pactosolucoes.comuns.json.JSONMapper;
import br.com.pactosolucoes.encrypt.gpg.PgpEncryption;
import br.com.pactosolucoes.integracao.aragorn.ImportacaoCartaoAPIDTO;
import br.com.pactosolucoes.oamd.controle.basico.DAO;
import br.com.pactosolucoes.pactopay.PactoPaySuperService;
import negocio.comuns.arquitetura.LogVO;
import negocio.comuns.arquitetura.UsuarioVO;
import negocio.comuns.basico.EmpresaVO;
import negocio.comuns.financeiro.ConvenioCobrancaVO;
import negocio.comuns.financeiro.TransacaoVO;
import negocio.comuns.financeiro.enumerador.SituacaoTransacaoEnum;
import negocio.comuns.financeiro.enumerador.TipoTransacaoEnum;
import negocio.comuns.utilitarias.Calendario;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisValidacao;
import negocio.facade.jdbc.arquitetura.Log;
import negocio.facade.jdbc.arquitetura.SuperFacadeJDBC;
import negocio.facade.jdbc.arquitetura.Usuario;
import negocio.facade.jdbc.basico.Empresa;
import negocio.facade.jdbc.financeiro.ConvenioCobranca;
import negocio.facade.jdbc.financeiro.Transacao;
import org.bouncycastle.jce.provider.BouncyCastleProvider;
import org.json.JSONObject;
import servicos.impl.cieloecommerce.CieloeCommerceService;
import servicos.impl.redepay.ERedeService;
import servicos.pix.PixDto;
import servicos.pix.PixItau;
import servicos.propriedades.PropsService;

import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.nio.charset.StandardCharsets;
import java.security.Security;
import java.sql.Connection;
import java.sql.DriverManager;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.ResultSetMetaData;
import java.sql.SQLException;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;

public class TestesLuizFelipe {

    private static Map<String, StringBuilder> mapaLogGravar;
    private static String nomeBanco = "";
    private static String logGeral = "GERAL";

    public static void main(String[] args) throws IOException, SQLException {
        try {
            testePixItau();

//            try (Connection con = DriverManager.getConnection("*********************************************************", "postgres", "pactodb")) {
////                processoAjustarClienteUtilizarResponsavelPagamento(false, true, con);
//
//                PactoPaySuperService service = new PactoPaySuperService(con);
//                EmpresaVO empresaVO = new EmpresaVO();
//                empresaVO.setCodigo(1);
//                service.diaHorarioPermiteEnviarAgora(empresaVO);
//            }

//            alterarDadosEmpresaOAMD();
//            alterarDadosOAMDPaths();
//            gerarLinkDownloadComprovante();
        } catch (Exception ex) {
            ex.printStackTrace();
        }
    }

    private static void testePixItau() throws Exception {

//        try (Connection con = DriverManager.getConnection("*********************************************************", "postgres", "pactodb")) {
        try (Connection con = new DAO().obterConexaoEspecifica("athos")) {
            ConvenioCobranca convenioCobrancaDAO = new ConvenioCobranca(con);
            ConvenioCobrancaVO convenioCobrancaVO = convenioCobrancaDAO.consultarPorChavePrimaria(4, Uteis.NIVELMONTARDADOS_TODOS);


            String pixDOTG = "{\"calendario\":{\"expiracao\":283720},\"txid\":\"a25f043b350f47419d8cc3f767d61368\",\"devedor\":{\"cpf\":\"22219882055\",\"nome\":\"LUIZ FELIPE ALMEIDA DE DEUS\"},\"valor\":{\"original\":\"1.50\"},\"chave\":\"878c5a17-c022-4094-b4bd-cbb3664196ae\",\"solicitacaoPagador\":\"PAGAMENTO PARA ATHOS ACADEMIA\",\"deleted\":false,\"pixCopiaECola\":\"\"}";
            PixDto pixDto = JSONMapper.getObject(new JSONObject(pixDOTG), PixDto.class);

            PixItau pixItau = new PixItau(con, convenioCobrancaVO);
//            pixItau.criarCobrancaPix(convenioCobrancaVO, pixDto);
//            pixItau.criarCobrancaBoleto(convenioCobrancaVO, pixDto);

        }
    }


    public static void testeDescriptoCard() throws Exception {
        String body = "{\"data\": \"hQGMAzqYTT1q6tnjAQv7BWPMXGJ1CpEL1kBTwCGl9gGnRUWEYMM9EC+IXV1Sc7KC kDqm3CJ1ftO/YbLMu23F9+1mg2BfWJ/twe86ysnCg33DuHr6DSvP3iJ7K7SO0S77 vqRmuOu7GyCYJiwTLtUW+Gqfm+zUCm/AT6DLGSzXlpZZdW7mqskFtzx8mzpx3JFO fSwbAeC3U0ALs7HCffAhHICJWyzAp9e4/KqQFAdWSafDCy5Y1DKdoUUKknGZwEiP +3cXsu+eEfcR7pHoN2Ol13cfWDDoV4g4WIq/g/09zDJHwWyBJXuoBZDW8gviwccR ERNTZnTn4FsN6R72Z69rhGCSNbgDKx+4XnLgfvdD+tBUf2694ia894Iojge0vFFs FH4A+4pzl4ye+L2ttAfLBxmPDaR/Wc4UODRokeVumx10kkP0JYSSpb9R1TyNdRVF W1fyYIfrjc3sShCCpfnqNuBPkfH9t6MbaG4QCyuNf3Oz3bVVw4o+pLzzc2LprRdV UNUkcqfU35thBwitqFt/0sBWAZ0OG/wcWAncYwPwXrD0XLIoWEiyq/ey0pH9N6Fi UUmeAesNYJlnrB83hFQi1IOVFk4+kkFnT3GfWxPNR1IiSAWDRK+nzbt3DCxki5gE 7ZTCudiqq9a0O21ZD2CihEs2mkLF90UCzg/0uWUVDOcKnWd/itMy0KOUMHztEpSc lgDdN2+xuIDQxqp92UF2IrMJYgzeAhAyo7gyBz2fogmqT1F81CMyNYuUBIM+hfc1 fxosUP7DG+TStF1/sKFEuWcNfgPNRiMDXRzrrqY2ZzxWkEoRJoJxv5MlesJHpnx8 q1zpICZRpFAc8jm3jK0+nzJGa3S3thcczNkqvZg4S5gkK3GvnGYcla0bNGcdficE RvHLMxGamoI= =1B94 \"}";
        JSONObject jsonBody = new JSONObject(body);
        String dadosCripto = jsonBody.getString("data").replace(" ", "\n");

        String arquivoChaveDescripto = PropsService.getPropertyValue(PropsService.chaveCriptoImportCc);
        String chaveCripto = Uteis.readLineByLineJava8(arquivoChaveDescripto);
        InputStream chave = new ByteArrayInputStream(chaveCripto.getBytes());

        byte[] decrypt;
        try {
            Security.addProvider(new BouncyCastleProvider());
            decrypt = PgpEncryption.decrypt(dadosCripto.getBytes(), chave, "", false);
        } catch (NoClassDefFoundError ncdf) {
            ncdf.printStackTrace();
            throw new Exception(ncdf.getMessage());
        }

        StringBuilder dadosDecrypt = new StringBuilder(new String(decrypt, StandardCharsets.UTF_8));
        ImportacaoCartaoAPIDTO cartaoAPIDTO = JSONMapper.getObject(new JSONObject(dadosDecrypt.toString()), ImportacaoCartaoAPIDTO.class);
        System.out.println("Feito! " + cartaoAPIDTO.toString());
    }

    public static void alterarDadosEmpresaOAMD() throws SQLException {
        Connection con = DriverManager.getConnection("*************************************", "postgres", "pactodb");
        String valorAnterior = "***********";
        String valorNovo = "host.docker.internal";

        String tabela = "empresa";
        String chave = "fly";

        try (PreparedStatement ps = con.prepareStatement("SELECT * FROM " + tabela + " where chave = '" + chave + "'")) {
            try (ResultSet rs = ps.executeQuery()) {
                final ResultSetMetaData meta = rs.getMetaData();
                final int columnCount = meta.getColumnCount();
                if (rs.next()) {
                    for (int column = 1; column <= columnCount; ++column) {
                        try {
                            final String nomeColuna = meta.getColumnName(column);
                            final Object value = rs.getObject(column);
                            if (value instanceof String) {
                                if (((String) value).contains(valorAnterior)) {
                                    String sql = "update " + tabela + " set " + nomeColuna + " =  replace(" + nomeColuna + ", '" + valorAnterior + "', '" + valorNovo + "') where chave = '" + chave + "';";
                                    System.out.println(sql);
                                }
                            }
                        } catch (Exception ex) {
                            ex.printStackTrace();
                        }
                    }
                }
            }
        }
    }

    public static void alterarDadosOAMDPaths() throws SQLException {
        Connection con = DriverManager.getConnection("*************************************", "postgres", "pactodb");
        String valorAnterior = "***********";
        String valorNovo = "host.docker.internal";

        String tabela = "paths";

        try (PreparedStatement ps = con.prepareStatement("SELECT * FROM " + tabela)) {
            try (ResultSet rs = ps.executeQuery()) {
                final ResultSetMetaData meta = rs.getMetaData();
                final int columnCount = meta.getColumnCount();
                if (rs.next()) {
                    for (int column = 1; column <= columnCount; ++column) {
                        try {
                            final String nomeColuna = meta.getColumnName(column);
                            final Object value = rs.getObject(column);
                            if (value instanceof String) {
                                if (((String) value).contains(valorAnterior)) {
                                    String sql = "update " + tabela + " set " + nomeColuna + " =  replace(" + nomeColuna + ", '" + valorAnterior + "', '" + valorNovo + "');";
                                    System.out.println(sql);
                                }
                            }
                        } catch (Exception ex) {
                            ex.printStackTrace();
                        }
                    }
                }
            }
        }
    }

    public static void gerarLinkDownloadComprovante() {
        String chave = "25c62fcea787555d25b39dafb1682bee";
        String listaTransacoes = "30806,30805,30799,30798,30804,30797,30812,30803,30800,30802,30811";
        gerarLink(chave, listaTransacoes);
        chave = "d03ca11eaafa6855cb1a754fe6e93268";
        listaTransacoes = "88630,88663,88634,88668,88664,88615,88638,88639,88620,88672,88679,88678,88665,88623,88635,88661,88618,88676,88628,88633,88669,88611,88652,88646,88684,88631,88660,88640,88617,88626,88667,88667,88677,88642,88643,88675";
        gerarLink(chave, listaTransacoes);
    }

    public static void gerarLink(String chave, String listaTransacoes) {
        adicionarLog(logGeral, "=============================================");
        adicionarLog(logGeral, "Chave: " + chave);
        adicionarLog(logGeral, "=============================================");
        for (String transacao : listaTransacoes.split(",")) {
            TransacaoVO transacaoVO = new TransacaoVO();
            transacaoVO.setCodigo(Integer.valueOf(transacao));
            transacaoVO.setSituacao(SituacaoTransacaoEnum.CANCELADA);
            String link = Transacao.gerarURLComprovanteCancelamentoTransacao(transacaoVO, chave);
            adicionarLog(logGeral, link);
        }
        adicionarLog(logGeral, "=============================================");
    }

    public static void consultarTransacao() {
        Connection con = null;
        Transacao transacaoDAO;
        try {
            con = DriverManager.getConnection("***********************************************************", "postgres", "pactodb");
            transacaoDAO = new Transacao(con);

//            TransacaoVO transacaoVO = transacaoDAO.consultarPorChavePrimaria(486945);
            TransacaoVO transacaoVO = transacaoDAO.consultarPorChavePrimaria(485978);
            TransacaoVO transacaoAnterior = (TransacaoVO) transacaoVO.getClone(true);

            if (transacaoVO.getTipo().equals(TipoTransacaoEnum.E_REDE)) {
                ERedeService service = new ERedeService(con, transacaoVO.getEmpresaVO().getCodigo(), transacaoVO.getConvenioCobrancaVO().getCodigo());
                service.consultarPeloCodigoReferencia(transacaoVO);
                service = null;
            } else if (transacaoVO.getTipo().equals(TipoTransacaoEnum.CIELO_ONLINE)) {
                CieloeCommerceService service = new CieloeCommerceService(con, transacaoVO.getEmpresaVO().getCodigo(), transacaoVO.getConvenioCobrancaVO().getCodigo());
                service.consultarSituacaoCobrancaTransacao(transacaoVO);
                service = null;
                if (!transacaoAnterior.getSituacao().equals(transacaoVO.getSituacao())) {
                    transacaoDAO.alterar(transacaoVO);
                }
            }

            System.out.println("####################################################");

        } catch (Exception ex) {
            ex.printStackTrace();
        } finally {
            transacaoDAO = null;
        }
    }

    private static void adicionarLog(String keyMapa, String msg) {
        String s = "[DEBUG] " + Uteis.getDataAtualAplicandoFormatacao("dd/MM/yyyy hh:mm:ss") + " --> " + msg;
        System.out.println(s);
        StringBuilder logGravar = getLogGravar(keyMapa).append(s).append("\n");
        mapaLogGravar.put(keyMapa, logGravar);
    }

    private static StringBuilder getLogGravar(String keyMapa) {
        if (mapaLogGravar == null) {
            mapaLogGravar = new HashMap<>();
        }
        StringBuilder logGravar = mapaLogGravar.get(keyMapa);
        if (logGravar == null) {
            logGravar = new StringBuilder();
        }
        return logGravar;
    }

    private static void processoAjustarClienteUtilizarResponsavelPagamento(boolean marcarParaMenorComResponsavel, boolean desmarcarSemResponsavel, Connection con) throws Exception {
        StringBuilder sql = new StringBuilder();
        sql.append("select  \n");
        sql.append("cl.matricula, \n");
        sql.append("cl.codigo as cliente, \n");
        sql.append("p.codigo as pessoa, \n");
        sql.append("p.nome, \n");
        sql.append("p.tipopessoa, \n");
        sql.append("cl.utilizarresponsavelpagamento, \n");
        sql.append("p.datanasc, \n");
        sql.append("(DATE_PART('YEAR', current_date) - DATE_PART('YEAR', p.datanasc)) as idade, \n");
        sql.append("cl.pessoaresponsavel, \n");
        sql.append("p.cfp as cpf, \n");
        sql.append("replace(coalesce(p.cpfresponsavelempresa, ''), ' ', '') as cpfresponsavelempresa, \n");
        sql.append("replace(coalesce(p.nomemae, ''), ' ', '') as nomemae, \n");
        sql.append("replace(coalesce(p.cpfmae, ''), ' ', '') as cpfmae, \n");
        sql.append("replace(coalesce(p.nomepai, ''), ' ', '') as nomepai, \n");
        sql.append("replace(coalesce(p.cpfpai, ''), ' ', '') as cpfpai \n");
        sql.append("from cliente cl  \n");
        sql.append("inner join pessoa p on p.codigo = cl.pessoa    \n");
        sql.append("order by cl.codigo \n");

        UsuarioVO usuarioVO = new Usuario(con).getUsuarioRecorrencia();

        Integer total = SuperFacadeJDBC.contar("SELECT COUNT(*) FROM (" + sql + ") as qtd", con);
        Integer totalMarcado = 0;
        Integer totalDesmarcado = 0;
        Integer atual = 0;

        ResultSet rs = SuperFacadeJDBC.criarConsulta(sql.toString(), con);
        while (rs.next()) {
            StringBuilder msg = new StringBuilder();
            try {
                con.setAutoCommit(false);

                Integer cliente = rs.getInt("cliente");
                Integer pessoa = rs.getInt("pessoa");
                String matricula = rs.getString("matricula");
                String nomemae = rs.getString("nomemae");
                String cpfmae = rs.getString("cpfmae");
                String nomepai = rs.getString("nomepai");
                String cpfpai = rs.getString("cpfpai");
                Integer pessoaresponsavel = rs.getInt("pessoaresponsavel");
                String cpf = rs.getString("cpf");
                boolean utilizarresponsavelpagamento = rs.getBoolean("utilizarresponsavelpagamento");

                Date dataNasc = rs.getDate("datanasc");
                boolean menorIdade = (dataNasc == null || Uteis.calcularIdadePessoa(Calendario.hoje(), dataNasc) < 18);

                //se for menor de idade e responsável então vou marcar
                if (marcarParaMenorComResponsavel &&
                        menorIdade && cpf.length() <= 5 &&
                        !utilizarresponsavelpagamento &&
                        (!UteisValidacao.emptyNumber(pessoaresponsavel) ||
                                (!UteisValidacao.emptyString(nomemae.trim()) && !UteisValidacao.emptyString(cpfmae.trim())) ||
                                (!UteisValidacao.emptyString(nomepai.trim()) && !UteisValidacao.emptyString(cpfpai.trim()))
                        )) {
                    ++totalMarcado;
                    msg.append("AJUSTAR Matricula ").append(matricula).append(" | Pessoa ").append(pessoa).append(" | Cliente ").append(cliente).append(" | Vou marcar TRUE");
                    gerarLog(pessoa, cliente, utilizarresponsavelpagamento, true, usuarioVO, con);
                    SuperFacadeJDBC.executarUpdate("update cliente set utilizarresponsavelpagamento = true where codigo = " + cliente + ";", con);

                } else if (desmarcarSemResponsavel &&
                        utilizarresponsavelpagamento &&
                        (UteisValidacao.emptyNumber(pessoaresponsavel) &&
                                UteisValidacao.emptyString(nomemae.trim()) && UteisValidacao.emptyString(cpfmae.trim()) &&
                                UteisValidacao.emptyString(nomepai.trim()) && UteisValidacao.emptyString(cpfpai.trim())
                        )) {

                    //está marcado porem não tem reponsável
                    ++totalDesmarcado;
                    msg.append("AJUSTAR Matricula ").append(matricula).append(" | Pessoa ").append(pessoa).append(" | Cliente ").append(cliente).append(" | Vou marcar FALSE");
                    gerarLog(pessoa, cliente, utilizarresponsavelpagamento, false, usuarioVO, con);
                    SuperFacadeJDBC.executarUpdate("update cliente set utilizarresponsavelpagamento = false where codigo = " + cliente + ";", con);
                }

                con.commit();
            } catch (Exception ex) {
                ex.printStackTrace();
                con.rollback();
            } finally {
                con.setAutoCommit(true);
                Uteis.logarDebug(++atual + "/" + total + " - " + msg);
            }
        }

        Uteis.logarDebug(total + " | Clientes verificados");
        Uteis.logarDebug(totalMarcado + " | Marcado");
        Uteis.logarDebug(totalDesmarcado + " | Desmarcado");
    }

    private static void gerarLog(Integer pessoa, Integer cliente,
                                 boolean valorAnterior, boolean novoValor,
                                 UsuarioVO usuarioVO, Connection con) throws Exception {
        Log logDAO;
        try {
            logDAO = new Log(con);

            LogVO obj = new LogVO();
            obj.setChavePrimaria(cliente.toString());
            obj.setNomeEntidade("CLIENTE");
            obj.setNomeEntidadeDescricao("CLIENTE");
            obj.setOperacao("UTILIZAR RESPONSAVEL PAGAMENTO - AJUSTE");
            obj.setPessoa(pessoa);
            obj.setDataAlteracao(Calendario.hoje());
            try {
                obj.setUsuarioVO(usuarioVO);
                obj.setResponsavelAlteracao(usuarioVO.getNome());
                obj.setUserOAMD(usuarioVO.getUserOamd());
            } catch (Exception ex) {
                obj.setResponsavelAlteracao("");
            }

            obj.setNomeCampo("UTILIZAR_RESPONSAVEL_PAGAMENTO");

            obj.setValorCampoAnterior(valorAnterior ? "SIM" : "NÃO");
            obj.setValorCampoAlterado(novoValor ? "SIM" : "NÃO");
            logDAO.incluirSemCommit(obj);
        } finally {
            logDAO = null;
        }
    }
}




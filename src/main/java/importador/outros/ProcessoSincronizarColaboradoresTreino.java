package importador.outros;

import br.com.pactosolucoes.oamd.controle.basico.DAO;
import negocio.comuns.basico.ColaboradorVO;
import negocio.comuns.utilitarias.Calendario;
import negocio.comuns.utilitarias.Uteis;
import negocio.facade.jdbc.arquitetura.SuperFacadeJDBC;
import negocio.facade.jdbc.basico.Colaborador;
import org.apache.http.HttpStatus;
import org.json.JSONArray;
import org.json.JSONObject;
import servicos.discovery.ClientDiscoveryDataDTO;
import servicos.discovery.DiscoveryMsService;
import servicos.http.MetodoHttpEnum;
import servicos.http.RequestHttpService;
import servicos.http.RespostaHttpDTO;
import servicos.integracao.TreinoWSConsumer;

import java.io.File;
import java.io.IOException;
import java.sql.Connection;
import java.sql.ResultSet;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;

public class ProcessoSincronizarColaboradoresTreino {

    private static Map<String, StringBuilder> mapaLogGravar;
    private static String nomeBanco = "";
    private static String logGeral = "GERAL";

    public static void main(String[] args) throws IOException {
        Date d1 = Calendario.hoje();
        try {
            String key = "hype";
            processar(key);
        } catch (Exception ex) {
            ex.printStackTrace();
            adicionarLog(logGeral, ex.getMessage());
        } finally {
            Date d2 = Calendario.hoje();
            adicionarLog(logGeral, "=============================================");
            adicionarLog(logGeral, "Banco: " + nomeBanco);
            adicionarLog(logGeral, "Tempo Total: " + Calendario.diferencaEmSegundos(d1, d2) + " segundos");
            adicionarLog(logGeral, "=============================================");
//            Uteis.salvarArquivo(ProcessoSincronizarColaboradoresTreino.class.getSimpleName() + "_" + nomeBanco + "-" + Calendario.getData("yyyyMMddHHmmss") + ".txt",
//                    getLogGravar(logGeral).toString(), "C:\\Processos\\" + File.separator);
        }
    }

    public static String processar(String key) throws Exception {
        Colaborador colaboradorDAO;
        Date d1 = Calendario.hoje();
        Integer total = 0;
        Integer totalProf = 0;
        Integer sincronizado = 0;
        try (Connection con = new DAO().obterConexaoEspecifica(key.trim())) {
            nomeBanco = con.getCatalog();

            colaboradorDAO = new Colaborador(con);

            JSONArray arrayProf = obterProfessoresTreino(key);
            totalProf = arrayProf.length();

            StringBuilder sql = new StringBuilder();
            sql.append("select \n");
            sql.append("c.codigo as colaborador, \n");
            sql.append("c.situacao, \n");
            sql.append("p.nome, \n");
            sql.append("c.empresa, \n");
            sql.append("exists(select codigo from tipocolaborador where colaborador = c.codigo and descricao = 'TW') as professorTW \n");
            sql.append("from colaborador c \n");
            sql.append("inner join pessoa p on p.codigo = c.pessoa \n");
            sql.append("order by 1 ");

            total = SuperFacadeJDBC.contar("SELECT COUNT(*) FROM (" + sql + ") as qtd", con);
            ResultSet rs = SuperFacadeJDBC.criarConsulta(sql.toString(), con);
            while (rs.next()) {
                try {
                    Integer colaboradorZW = rs.getInt("colaborador");
                    String nome = rs.getString("nome");
                    String situacao = rs.getString("situacao");
                    boolean ativoZW = situacao.equalsIgnoreCase("AT");
                    Integer empresaZW = rs.getInt("empresa");
                    boolean professorZW = rs.getBoolean("professorTW");

                    boolean sincronizar = false;
                    for (int e = 0; e < arrayProf.length(); e++) {
                        JSONObject objProf = arrayProf.getJSONObject(e);

                        Integer colaboradorTW = objProf.getInt("codigoColaborador");
                        Integer empresaTW = objProf.getInt("empresaZW");
                        Boolean professorTW = objProf.getBoolean("professorTW");
                        Boolean ativoTW = objProf.getBoolean("ativo");

                        if (colaboradorTW.equals(colaboradorZW)) {
                            if (ativoTW != ativoZW) {
                                adicionarLog(logGeral, "Sincronizar colaborador: " + colaboradorZW + " | " + nome + " | ativoTW: " + ativoTW + " | ativoZW: " + ativoZW);
                                sincronizar = true;
                                break;
                            }
                            if (professorTW != professorZW) {
                                adicionarLog(logGeral, "Sincronizar colaborador: " + colaboradorZW + " | " + nome + " | professorTW: " + professorTW + " | professorZW: " + professorZW);
                                sincronizar = true;
                                break;
                            }
                        }
                    }

                    if (sincronizar) {
                        ColaboradorVO colaboradorVO = colaboradorDAO.consultarPorChavePrimaria(colaboradorZW, Uteis.NIVELMONTARDADOS_TODOS);
                        TreinoWSConsumer.sincronizarProfessor(key, colaboradorVO.toProfessorSintetico(), colaboradorVO.getEmpresa().getCodigo());
                        ++sincronizado;
                    }
                } catch (Exception ex) {
                    ex.printStackTrace();
                }
            }
        } catch (Exception ex) {
            ex.printStackTrace();
            throw ex;
        } finally {
            colaboradorDAO = null;
        }
        Date d2 = Calendario.hoje();
        adicionarLog(logGeral, "Chave: " + key);
        adicionarLog(logGeral, "Banco: " + nomeBanco);
        adicionarLog(logGeral, "Total: " + total);
        adicionarLog(logGeral, "Total Prof: " + totalProf);
        adicionarLog(logGeral, "Sincronizado: " + sincronizado);
        adicionarLog(logGeral, "Tempo Total: " + Calendario.diferencaEmSegundos(d1, d2) + " segundos");

        JSONObject jsonRet = new JSONObject();
        jsonRet.put("chave", key);
        jsonRet.put("banco", nomeBanco);
        jsonRet.put("total", total);
        jsonRet.put("total_prof", totalProf);
        jsonRet.put("total_sincronizado", sincronizado);
        jsonRet.put("tempo", Calendario.diferencaEmSegundos(d1, d2) + " segundos");
        return jsonRet.toString();
    }

    private static JSONArray obterProfessoresTreino(String key) throws Exception {
        ClientDiscoveryDataDTO clientDiscoveryDataDTO = DiscoveryMsService.urlsChave(key);

        Map<String, String> params = new HashMap<>();
        params.put("codEmpresa", "0");
        params.put("somenteAtivos", "false");
        params.put("professorDTO", "true");

        String url = (clientDiscoveryDataDTO.getServiceUrls().getTreinoApiUrl() + "/professor/" + key + "/todos");

        RequestHttpService httpService = new RequestHttpService();
        RespostaHttpDTO respostaHttpDTO = httpService.executeRequest(url, null, params, null, MetodoHttpEnum.POST);
        if (!respostaHttpDTO.getHttpStatus().equals(HttpStatus.SC_OK)) {
            throw new Exception(new JSONObject(respostaHttpDTO).toString());
        }
        JSONObject json = new JSONObject(respostaHttpDTO.getResponse());
        return json.getJSONArray("return");
    }

    private static void adicionarLog(String keyMapa, String msg) {
        String s = "[DEBUG] " + Uteis.getDataAtualAplicandoFormatacao("dd/MM/yyyy hh:mm:ss") + " --> " + msg;
        System.out.println(s);
        StringBuilder logGravar = getLogGravar(keyMapa).append(s).append("\n");
        mapaLogGravar.put(keyMapa, logGravar);
    }

    private static StringBuilder getLogGravar(String keyMapa) {
        if (mapaLogGravar == null) {
            mapaLogGravar = new HashMap<>();
        }
        StringBuilder logGravar = mapaLogGravar.get(keyMapa);
        if (logGravar == null) {
            logGravar = new StringBuilder();
        }
        return logGravar;
    }
}

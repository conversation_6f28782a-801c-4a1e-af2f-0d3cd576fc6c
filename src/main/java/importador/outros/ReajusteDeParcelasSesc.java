package importador.outros;

import br.com.pactosolucoes.atualizadb.processo.ExecutarProcessos;
import negocio.comuns.arquitetura.UsuarioVO;
import negocio.comuns.plano.PlanoVO;
import negocio.comuns.plano.enumerador.TipoReajuste;
import negocio.comuns.utilitarias.Calendario;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisValidacao;
import negocio.facade.jdbc.arquitetura.SuperFacadeJDBC;
import negocio.facade.jdbc.arquitetura.Usuario;
import negocio.facade.jdbc.plano.Plano;
import negocio.facade.jdbc.plano.PlanoProdutoSugerido;
import negocio.facade.jdbc.utilitarias.Conexao;
import org.json.JSONArray;
import org.json.JSONObject;

import java.io.File;
import java.io.IOException;
import java.sql.Connection;
import java.sql.DriverManager;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class ReajusteDeParcelasSesc {

    private static Map<String, StringBuilder> mapaLogGravar;
    private static String nomeBanco = "";
    private static String keyLogGeral = "GERAL";
    private static Integer qtdContratosGeral = 0;
    private static Integer qtdParcelasGeral = 0;

    public static void main(String[] args) throws IOException {
        Date d1 = Calendario.hoje();
        try {
            Connection con = DriverManager.getConnection("************************************************", "postgres", "pactodb");
            Conexao.guardarConexaoForJ2SE(con);

            nomeBanco = con.getCatalog();

            adicionarLog(keyLogGeral, "Parâmetros: ");
            adicionarLog(keyLogGeral, "nomeBanco: " + nomeBanco);

            Date inicioVencimentoParcelas = Calendario.getDate("dd/MM/yyyy", "01/01/2025");
            Date dataLancamentoNovaParcela = Calendario.getDate("dd/MM/yyyy", "01/01/2025");
            Date dataLancamentoParcelaCancelada = Calendario.getDate("dd/MM/yyyy", "01/01/2025");
            Date anoReferencia = Calendario.getDate("dd/MM/yyyy", "01/12/2024");

            List<ReajusteSescTO> lista = new ArrayList<>();
            // Ponta Porã
            lista.add(new ReajusteSescTO(364, 2.45, inicioVencimentoParcelas, dataLancamentoNovaParcela, dataLancamentoParcelaCancelada));
            lista.add(new ReajusteSescTO(365, 6.0, inicioVencimentoParcelas, dataLancamentoNovaParcela, dataLancamentoParcelaCancelada));
            lista.add(new ReajusteSescTO(366, 7.0, inicioVencimentoParcelas, dataLancamentoNovaParcela, dataLancamentoParcelaCancelada));
            lista.add(new ReajusteSescTO(363, 5.0, inicioVencimentoParcelas, dataLancamentoNovaParcela, dataLancamentoParcelaCancelada));
            lista.add(new ReajusteSescTO(367, 2.45, inicioVencimentoParcelas, dataLancamentoNovaParcela, dataLancamentoParcelaCancelada));
            lista.add(new ReajusteSescTO(368, 7.0, inicioVencimentoParcelas, dataLancamentoNovaParcela, dataLancamentoParcelaCancelada));
            lista.add(new ReajusteSescTO(369, 6.0, inicioVencimentoParcelas, dataLancamentoNovaParcela, dataLancamentoParcelaCancelada));
            lista.add(new ReajusteSescTO(370, 5.0, inicioVencimentoParcelas, dataLancamentoNovaParcela, dataLancamentoParcelaCancelada));
            lista.add(new ReajusteSescTO(165, 2.45, inicioVencimentoParcelas, dataLancamentoNovaParcela, dataLancamentoParcelaCancelada));
            lista.add(new ReajusteSescTO(166, 6.0, inicioVencimentoParcelas, dataLancamentoNovaParcela, dataLancamentoParcelaCancelada));
            lista.add(new ReajusteSescTO(167, 7.0, inicioVencimentoParcelas, dataLancamentoNovaParcela, dataLancamentoParcelaCancelada));
            lista.add(new ReajusteSescTO(168, 5.0, inicioVencimentoParcelas, dataLancamentoNovaParcela, dataLancamentoParcelaCancelada));
            lista.add(new ReajusteSescTO(277, 2.45, inicioVencimentoParcelas, dataLancamentoNovaParcela, dataLancamentoParcelaCancelada));
            lista.add(new ReajusteSescTO(274, 7.0, inicioVencimentoParcelas, dataLancamentoNovaParcela, dataLancamentoParcelaCancelada));
            lista.add(new ReajusteSescTO(275, 6.0, inicioVencimentoParcelas, dataLancamentoNovaParcela, dataLancamentoParcelaCancelada));
            lista.add(new ReajusteSescTO(276, 5.0, inicioVencimentoParcelas, dataLancamentoNovaParcela, dataLancamentoParcelaCancelada));

            // Aquidauana
            lista.add(new ReajusteSescTO(355, 2.45, inicioVencimentoParcelas, dataLancamentoNovaParcela, dataLancamentoParcelaCancelada));
            lista.add(new ReajusteSescTO(356, 6.0, inicioVencimentoParcelas, dataLancamentoNovaParcela, dataLancamentoParcelaCancelada));
            lista.add(new ReajusteSescTO(357, 7.0, inicioVencimentoParcelas, dataLancamentoNovaParcela, dataLancamentoParcelaCancelada));
            lista.add(new ReajusteSescTO(359, 5.0, inicioVencimentoParcelas, dataLancamentoNovaParcela, dataLancamentoParcelaCancelada));
            lista.add(new ReajusteSescTO(360, 2.45, inicioVencimentoParcelas, dataLancamentoNovaParcela, dataLancamentoParcelaCancelada));
            lista.add(new ReajusteSescTO(358, 6.0, inicioVencimentoParcelas, dataLancamentoNovaParcela, dataLancamentoParcelaCancelada));
            lista.add(new ReajusteSescTO(361, 7.0, inicioVencimentoParcelas, dataLancamentoNovaParcela, dataLancamentoParcelaCancelada));
            lista.add(new ReajusteSescTO(362, 5.0, inicioVencimentoParcelas, dataLancamentoNovaParcela, dataLancamentoParcelaCancelada));
            lista.add(new ReajusteSescTO(153, 2.45, inicioVencimentoParcelas, dataLancamentoNovaParcela, dataLancamentoParcelaCancelada));
            lista.add(new ReajusteSescTO(154, 6.0, inicioVencimentoParcelas, dataLancamentoNovaParcela, dataLancamentoParcelaCancelada));
            lista.add(new ReajusteSescTO(181, 7.0, inicioVencimentoParcelas, dataLancamentoNovaParcela, dataLancamentoParcelaCancelada));
            lista.add(new ReajusteSescTO(156, 5.0, inicioVencimentoParcelas, dataLancamentoNovaParcela, dataLancamentoParcelaCancelada));
            lista.add(new ReajusteSescTO(270, 2.45, inicioVencimentoParcelas, dataLancamentoNovaParcela, dataLancamentoParcelaCancelada));
            lista.add(new ReajusteSescTO(271, 6.0, inicioVencimentoParcelas, dataLancamentoNovaParcela, dataLancamentoParcelaCancelada));
            lista.add(new ReajusteSescTO(285, 7.0, inicioVencimentoParcelas, dataLancamentoNovaParcela, dataLancamentoParcelaCancelada));
            lista.add(new ReajusteSescTO(273, 5.0, inicioVencimentoParcelas, dataLancamentoNovaParcela, dataLancamentoParcelaCancelada));

            // Camilo Boni
            lista.add(new ReajusteSescTO(310, 4.0, inicioVencimentoParcelas, dataLancamentoNovaParcela, dataLancamentoParcelaCancelada));
            lista.add(new ReajusteSescTO(308, 11.0, inicioVencimentoParcelas, dataLancamentoNovaParcela, dataLancamentoParcelaCancelada));
            lista.add(new ReajusteSescTO(309, 10.0, inicioVencimentoParcelas, dataLancamentoNovaParcela, dataLancamentoParcelaCancelada));
            lista.add(new ReajusteSescTO(307, 8.0, inicioVencimentoParcelas, dataLancamentoNovaParcela, dataLancamentoParcelaCancelada));
            lista.add(new ReajusteSescTO(248, 6.0, inicioVencimentoParcelas, dataLancamentoNovaParcela, dataLancamentoParcelaCancelada));
            lista.add(new ReajusteSescTO(246, 6.0, inicioVencimentoParcelas, dataLancamentoNovaParcela, dataLancamentoParcelaCancelada));
            lista.add(new ReajusteSescTO(244, 5.0, inicioVencimentoParcelas, dataLancamentoNovaParcela, dataLancamentoParcelaCancelada));
            lista.add(new ReajusteSescTO(249, 8.0, inicioVencimentoParcelas, dataLancamentoNovaParcela, dataLancamentoParcelaCancelada));
            lista.add(new ReajusteSescTO(247, 7.0, inicioVencimentoParcelas, dataLancamentoNovaParcela, dataLancamentoParcelaCancelada));
            lista.add(new ReajusteSescTO(245, 6.0, inicioVencimentoParcelas, dataLancamentoNovaParcela, dataLancamentoParcelaCancelada));
            lista.add(new ReajusteSescTO(254, 6.0, inicioVencimentoParcelas, dataLancamentoNovaParcela, dataLancamentoParcelaCancelada));
            lista.add(new ReajusteSescTO(252, 6.0, inicioVencimentoParcelas, dataLancamentoNovaParcela, dataLancamentoParcelaCancelada));
            lista.add(new ReajusteSescTO(250, 5.0, inicioVencimentoParcelas, dataLancamentoNovaParcela, dataLancamentoParcelaCancelada));
            lista.add(new ReajusteSescTO(293, 3.0, inicioVencimentoParcelas, dataLancamentoNovaParcela, dataLancamentoParcelaCancelada));
            lista.add(new ReajusteSescTO(291, 8.0, inicioVencimentoParcelas, dataLancamentoNovaParcela, dataLancamentoParcelaCancelada));
            lista.add(new ReajusteSescTO(292, 8.0, inicioVencimentoParcelas, dataLancamentoNovaParcela, dataLancamentoParcelaCancelada));
            lista.add(new ReajusteSescTO(290, 6.0, inicioVencimentoParcelas, dataLancamentoNovaParcela, dataLancamentoParcelaCancelada));
            lista.add(new ReajusteSescTO(242, 6.0, inicioVencimentoParcelas, dataLancamentoNovaParcela, dataLancamentoParcelaCancelada));
            lista.add(new ReajusteSescTO(241, 6.0, inicioVencimentoParcelas, dataLancamentoNovaParcela, dataLancamentoParcelaCancelada));
            lista.add(new ReajusteSescTO(238, 5.0, inicioVencimentoParcelas, dataLancamentoNovaParcela, dataLancamentoParcelaCancelada));
            lista.add(new ReajusteSescTO(243, 8.0, inicioVencimentoParcelas, dataLancamentoNovaParcela, dataLancamentoParcelaCancelada));
            lista.add(new ReajusteSescTO(240, 7.0, inicioVencimentoParcelas, dataLancamentoNovaParcela, dataLancamentoParcelaCancelada));
            lista.add(new ReajusteSescTO(239, 6.0, inicioVencimentoParcelas, dataLancamentoNovaParcela, dataLancamentoParcelaCancelada));
            lista.add(new ReajusteSescTO(207, 3.0, inicioVencimentoParcelas, dataLancamentoNovaParcela, dataLancamentoParcelaCancelada));
            lista.add(new ReajusteSescTO(213, 9.0, inicioVencimentoParcelas, dataLancamentoNovaParcela, dataLancamentoParcelaCancelada));
            lista.add(new ReajusteSescTO(212, 9.0, inicioVencimentoParcelas, dataLancamentoNovaParcela, dataLancamentoParcelaCancelada));
            lista.add(new ReajusteSescTO(209, 7.0, inicioVencimentoParcelas, dataLancamentoNovaParcela, dataLancamentoParcelaCancelada));
            lista.add(new ReajusteSescTO(208, 4.5, inicioVencimentoParcelas, dataLancamentoNovaParcela, dataLancamentoParcelaCancelada));
            lista.add(new ReajusteSescTO(214, 12.0, inicioVencimentoParcelas, dataLancamentoNovaParcela, dataLancamentoParcelaCancelada));
            lista.add(new ReajusteSescTO(211, 11.0, inicioVencimentoParcelas, dataLancamentoNovaParcela, dataLancamentoParcelaCancelada));
            lista.add(new ReajusteSescTO(210, 9.0, inicioVencimentoParcelas, dataLancamentoNovaParcela, dataLancamentoParcelaCancelada));
            lista.add(new ReajusteSescTO(219, 8.0, inicioVencimentoParcelas, dataLancamentoNovaParcela, dataLancamentoParcelaCancelada));
            lista.add(new ReajusteSescTO(217, 8.0, inicioVencimentoParcelas, dataLancamentoNovaParcela, dataLancamentoParcelaCancelada));
            lista.add(new ReajusteSescTO(215, 6.0, inicioVencimentoParcelas, dataLancamentoNovaParcela, dataLancamentoParcelaCancelada));
            lista.add(new ReajusteSescTO(220, 10.0, inicioVencimentoParcelas, dataLancamentoNovaParcela, dataLancamentoParcelaCancelada));
            lista.add(new ReajusteSescTO(218, 9.0, inicioVencimentoParcelas, dataLancamentoNovaParcela, dataLancamentoParcelaCancelada));
            lista.add(new ReajusteSescTO(216, 8.0, inicioVencimentoParcelas, dataLancamentoNovaParcela, dataLancamentoParcelaCancelada));
            lista.add(new ReajusteSescTO(157, 2.45, inicioVencimentoParcelas, dataLancamentoNovaParcela, dataLancamentoParcelaCancelada));
            lista.add(new ReajusteSescTO(158, 6.0, inicioVencimentoParcelas, dataLancamentoNovaParcela, dataLancamentoParcelaCancelada));
            lista.add(new ReajusteSescTO(159, 7.0, inicioVencimentoParcelas, dataLancamentoNovaParcela, dataLancamentoParcelaCancelada));
            lista.add(new ReajusteSescTO(160, 5.0, inicioVencimentoParcelas, dataLancamentoNovaParcela, dataLancamentoParcelaCancelada));
            lista.add(new ReajusteSescTO(262, 6.0, inicioVencimentoParcelas, dataLancamentoNovaParcela, dataLancamentoParcelaCancelada));
            lista.add(new ReajusteSescTO(260, 17.0, inicioVencimentoParcelas, dataLancamentoNovaParcela, dataLancamentoParcelaCancelada));
            lista.add(new ReajusteSescTO(258, 15.0, inicioVencimentoParcelas, dataLancamentoNovaParcela, dataLancamentoParcelaCancelada));
            lista.add(new ReajusteSescTO(256, 13.0, inicioVencimentoParcelas, dataLancamentoNovaParcela, dataLancamentoParcelaCancelada));
            lista.add(new ReajusteSescTO(267, 4.5, inicioVencimentoParcelas, dataLancamentoNovaParcela, dataLancamentoParcelaCancelada));
            lista.add(new ReajusteSescTO(264, 12.0, inicioVencimentoParcelas, dataLancamentoNovaParcela, dataLancamentoParcelaCancelada));
            lista.add(new ReajusteSescTO(266, 12.0, inicioVencimentoParcelas, dataLancamentoNovaParcela, dataLancamentoParcelaCancelada));
            lista.add(new ReajusteSescTO(265, 10.0, inicioVencimentoParcelas, dataLancamentoNovaParcela, dataLancamentoParcelaCancelada));
            lista.add(new ReajusteSescTO(202, 2.45, inicioVencimentoParcelas, dataLancamentoNovaParcela, dataLancamentoParcelaCancelada));
            lista.add(new ReajusteSescTO(205, 7.0, inicioVencimentoParcelas, dataLancamentoNovaParcela, dataLancamentoParcelaCancelada));
            lista.add(new ReajusteSescTO(204, 6.0, inicioVencimentoParcelas, dataLancamentoNovaParcela, dataLancamentoParcelaCancelada));
            lista.add(new ReajusteSescTO(203, 5.0, inicioVencimentoParcelas, dataLancamentoNovaParcela, dataLancamentoParcelaCancelada));

            // Dourados
            lista.add(new ReajusteSescTO(321, 2.0, inicioVencimentoParcelas, dataLancamentoNovaParcela, dataLancamentoParcelaCancelada));
            lista.add(new ReajusteSescTO(371, 2.45, inicioVencimentoParcelas, dataLancamentoNovaParcela, dataLancamentoParcelaCancelada));
            lista.add(new ReajusteSescTO(376, 2.45, inicioVencimentoParcelas, dataLancamentoNovaParcela, dataLancamentoParcelaCancelada));
            lista.add(new ReajusteSescTO(164, 2.45, inicioVencimentoParcelas, dataLancamentoNovaParcela, dataLancamentoParcelaCancelada));
            lista.add(new ReajusteSescTO(279, 2.45, inicioVencimentoParcelas, dataLancamentoNovaParcela, dataLancamentoParcelaCancelada));
            lista.add(new ReajusteSescTO(415, 4.0, inicioVencimentoParcelas, dataLancamentoNovaParcela, dataLancamentoParcelaCancelada));
            lista.add(new ReajusteSescTO(411, 4.0, inicioVencimentoParcelas, dataLancamentoNovaParcela, dataLancamentoParcelaCancelada));
            lista.add(new ReajusteSescTO(372, 5.0, inicioVencimentoParcelas, dataLancamentoNovaParcela, dataLancamentoParcelaCancelada));
            lista.add(new ReajusteSescTO(318, 5.0, inicioVencimentoParcelas, dataLancamentoNovaParcela, dataLancamentoParcelaCancelada));
            lista.add(new ReajusteSescTO(378, 5.0, inicioVencimentoParcelas, dataLancamentoNovaParcela, dataLancamentoParcelaCancelada));
            lista.add(new ReajusteSescTO(163, 5.0, inicioVencimentoParcelas, dataLancamentoNovaParcela, dataLancamentoParcelaCancelada));
            lista.add(new ReajusteSescTO(280, 5.0, inicioVencimentoParcelas, dataLancamentoNovaParcela, dataLancamentoParcelaCancelada));
            lista.add(new ReajusteSescTO(374, 6.0, inicioVencimentoParcelas, dataLancamentoNovaParcela, dataLancamentoParcelaCancelada));
            lista.add(new ReajusteSescTO(319, 6.0, inicioVencimentoParcelas, dataLancamentoNovaParcela, dataLancamentoParcelaCancelada));
            lista.add(new ReajusteSescTO(320, 6.0, inicioVencimentoParcelas, dataLancamentoNovaParcela, dataLancamentoParcelaCancelada));
            lista.add(new ReajusteSescTO(326, 6.0, inicioVencimentoParcelas, dataLancamentoNovaParcela, dataLancamentoParcelaCancelada));
            lista.add(new ReajusteSescTO(377, 6.0, inicioVencimentoParcelas, dataLancamentoNovaParcela, dataLancamentoParcelaCancelada));
            lista.add(new ReajusteSescTO(429, 6.0, inicioVencimentoParcelas, dataLancamentoNovaParcela, dataLancamentoParcelaCancelada));
            lista.add(new ReajusteSescTO(161, 6.0, inicioVencimentoParcelas, dataLancamentoNovaParcela, dataLancamentoParcelaCancelada));
            lista.add(new ReajusteSescTO(281, 6.0, inicioVencimentoParcelas, dataLancamentoNovaParcela, dataLancamentoParcelaCancelada));
            lista.add(new ReajusteSescTO(373, 7.0, inicioVencimentoParcelas, dataLancamentoNovaParcela, dataLancamentoParcelaCancelada));
            lista.add(new ReajusteSescTO(375, 7.0, inicioVencimentoParcelas, dataLancamentoNovaParcela, dataLancamentoParcelaCancelada));
            lista.add(new ReajusteSescTO(162, 7.0, inicioVencimentoParcelas, dataLancamentoNovaParcela, dataLancamentoParcelaCancelada));
            lista.add(new ReajusteSescTO(278, 7.0, inicioVencimentoParcelas, dataLancamentoNovaParcela, dataLancamentoParcelaCancelada));
            lista.add(new ReajusteSescTO(412, 8.0, inicioVencimentoParcelas, dataLancamentoNovaParcela, dataLancamentoParcelaCancelada));
            lista.add(new ReajusteSescTO(316, 8.0, inicioVencimentoParcelas, dataLancamentoNovaParcela, dataLancamentoParcelaCancelada));
            lista.add(new ReajusteSescTO(413, 10.0, inicioVencimentoParcelas, dataLancamentoNovaParcela, dataLancamentoParcelaCancelada));
            lista.add(new ReajusteSescTO(410, 10.0, inicioVencimentoParcelas, dataLancamentoNovaParcela, dataLancamentoParcelaCancelada));
            lista.add(new ReajusteSescTO(414, 11.0, inicioVencimentoParcelas, dataLancamentoNovaParcela, dataLancamentoParcelaCancelada));
            lista.add(new ReajusteSescTO(315, 11.0, inicioVencimentoParcelas, dataLancamentoNovaParcela, dataLancamentoParcelaCancelada));
            lista.add(new ReajusteSescTO(408, 13.0, inicioVencimentoParcelas, dataLancamentoNovaParcela, dataLancamentoParcelaCancelada));
            lista.add(new ReajusteSescTO(317, 15.0, inicioVencimentoParcelas, dataLancamentoNovaParcela, dataLancamentoParcelaCancelada));
            lista.add(new ReajusteSescTO(407, 17.0, inicioVencimentoParcelas, dataLancamentoNovaParcela, dataLancamentoParcelaCancelada));

            processar(lista, anoReferencia, con);

//            reverterAjuste(con);
        } catch (Exception ex) {
            ex.printStackTrace();
            adicionarLog(keyLogGeral, ex.getMessage());
        } finally {
            Date d2 = Calendario.hoje();
            adicionarLog(keyLogGeral, "=============================================");
            adicionarLog(keyLogGeral, "Banco: " + nomeBanco);
            adicionarLog(keyLogGeral, "Contratos: " + qtdContratosGeral);
            adicionarLog(keyLogGeral, "Parcelas: " + qtdParcelasGeral);
            adicionarLog(keyLogGeral, "Tempo Total: " + Calendario.diferencaEmSegundos(d1, d2) + " segundos");
            adicionarLog(keyLogGeral, "=============================================");
            Uteis.salvarArquivo(ReajusteDeParcelasSesc.class.getSimpleName() + "_" + nomeBanco + "-" + Calendario.getData("yyyyMMddHHmmss") + ".txt",
                    getLogGravar(keyLogGeral).toString(), "C:\\Processos\\" + File.separator);
        }
    }

    private static void processar(List<ReajusteSescTO> lista, Date anoReferencia, Connection con) throws Exception {
        Usuario usuarioDAO;
        Plano planoDAO;
        PlanoProdutoSugerido planoProdutoSugeridoDAO;
        try {
            usuarioDAO = new Usuario(con);
            planoDAO = new Plano(con);
            planoProdutoSugeridoDAO = new PlanoProdutoSugerido(con);

            UsuarioVO usuarioVO = usuarioDAO.consultarPorUsername("pactobr", Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA);

            int atual = 0;
            for (ReajusteSescTO reajusteSescTO : lista) {
                adicionarLog(reajusteSescTO.getPlano().toString(), "=============================================");
                adicionarLog(reajusteSescTO.getPlano().toString(), "================= Processando: " + ++atual + "/" + lista.size());
                adicionarLog(reajusteSescTO.getPlano().toString(), "=============================================");
                adicionarLog(reajusteSescTO.getPlano().toString(), "================= Plano: " + reajusteSescTO.getPlano());
                adicionarLog(reajusteSescTO.getPlano().toString(), "================= ValorAcrescentar: " + reajusteSescTO.getValorAcrescentar());
                adicionarLog(reajusteSescTO.getPlano().toString(), "================= InicioVencimentoParcelas: " + Uteis.getData(reajusteSescTO.getInicioVencimentoParcelas(), "dd/MM/yyyy"));
                adicionarLog(reajusteSescTO.getPlano().toString(), "================= DataLancamentoNovaParcela: " + Uteis.getData(reajusteSescTO.getDataLancamentoNovaParcela(), "dd/MM/yyyy"));
                adicionarLog(reajusteSescTO.getPlano().toString(), "================= DataLancamentoParcelaCancelada: " + Uteis.getData(reajusteSescTO.getDataLancamentoParcelaCancelada(), "dd/MM/yyyy"));
                adicionarLog(reajusteSescTO.getPlano().toString(), "================= Ano Referencia: " + (anoReferencia != null ? Calendario.getAno(anoReferencia) : "null"));
                Date d1 = Calendario.hoje();
                PlanoVO planoVO;
                StringBuilder resultado = new StringBuilder();
                Integer qtdContratos = 0;
                Integer qtdParcelas = 0;
                try {
                    planoVO = planoDAO.consultarPorChavePrimaria(reajusteSescTO.getPlano(), Uteis.NIVELMONTARDADOS_TODOS);
                    adicionarLog(reajusteSescTO.getPlano().toString(), "================= Plano Descrição: " + planoVO.getDescricao());
                    planoVO.setPlanoProdutoSugeridoVOs(planoProdutoSugeridoDAO.consultarPlanoProdutoSugeridos(planoVO.getCodigo(), false, Uteis.NIVELMONTARDADOS_TODOS));

                    TipoReajuste tipoReajuste = TipoReajuste.VALOR;
                    boolean simular = false;
                    boolean gerarNovaParcela = true;
                    String tiposContratos = "'MA','RE','RN'";
                    Date dataLancamentoContratoInicio = null;
                    Date dataLancamentoContratoFim = null;
                    String tipoAlteracaoSelecionado = PlanoVO.TIPO_ALTERACAO_DETALHADO;

                    resultado = planoDAO.alterarValoresContratos(tipoReajuste, reajusteSescTO.getValorAcrescentar(),
                            planoVO.getCodigo(), reajusteSescTO.getInicioVencimentoParcelas(), usuarioVO, simular, tiposContratos,
                            dataLancamentoContratoInicio, dataLancamentoContratoFim, 0.0, 0.0,
                            reajusteSescTO.getContrato(), null, tipoAlteracaoSelecionado, gerarNovaParcela, reajusteSescTO.getDataLancamentoNovaParcela(),
                            reajusteSescTO.getDataLancamentoParcelaCancelada(), anoReferencia);
                    if (UteisValidacao.emptyNumber(resultado.length())) {
                        resultado.append("Nenhum reajuste foi possível de ser aplicado. Nenhum contrato compatível com os parâmetros informados.");
                    }

                    try {
                        if (!resultado.toString().contains("Nenhum reajuste foi possível")) {
                            Integer qtdContrato1 = Integer.valueOf(resultado.toString().split("Contratos: ")[1].split("<br/>")[0]);
                            Integer qtdParcela1 = Integer.valueOf(resultado.toString().split("Parcelas atuais: ")[1].split(" = ")[0]);
                            qtdContratos = qtdContrato1;
                            qtdParcelas = qtdParcela1;

                            qtdContratosGeral += qtdContratos;
                            qtdParcelasGeral += qtdParcelas;
                        }
                    } catch (Exception ex) {
                        ex.printStackTrace();
                    }
                } catch (Exception ex) {
                    ex.printStackTrace();
                    resultado = new StringBuilder("ERRO: " + ex.getMessage());
                } finally {
                    Date d2 = Calendario.hoje();
                    adicionarLog(reajusteSescTO.getPlano().toString(), "=============================================");
                    adicionarLog(reajusteSescTO.getPlano().toString(), "================= RESULTADO =================");
                    adicionarLog(reajusteSescTO.getPlano().toString(), "=============================================");
                    adicionarLog(reajusteSescTO.getPlano().toString(), "================= Contratos: " + qtdContratos);
                    adicionarLog(reajusteSescTO.getPlano().toString(), "================= Parcelas: " + qtdParcelas);
                    adicionarLog(reajusteSescTO.getPlano().toString(), "=============================================");
                    adicionarLog(reajusteSescTO.getPlano().toString(), resultado.toString());
                    adicionarLog(reajusteSescTO.getPlano().toString(), "=============================================");
                    adicionarLog(reajusteSescTO.getPlano().toString(), "================= Tempo Total: " + Calendario.diferencaEmSegundos(d1, d2) + " segundos");
                    adicionarLog(reajusteSescTO.getPlano().toString(), "=============================================");
                    String nomeArquivo = (ReajusteDeParcelasSesc.class.getSimpleName() + "_" + nomeBanco + "-" +
                            Calendario.getData("yyyyMMddHHmmss") +
                            "_PLANO_" + reajusteSescTO.getPlano() + ".txt");
                    Uteis.salvarArquivo(nomeArquivo, getLogGravar(reajusteSescTO.getPlano().toString()).toString(), "C:\\Processos\\" + File.separator);
                }
            }
        } finally {
            usuarioDAO = null;
        }
    }

    private static void adicionarLog(String keyMapa, String msg) {
        String s = "[DEBUG] " + Uteis.getDataAtualAplicandoFormatacao("dd/MM/yyyy hh:mm:ss") + " --> " + msg;
        System.out.println(s);
        StringBuilder logGravar = getLogGravar(keyMapa).append(s).append("\n");
        mapaLogGravar.put(keyMapa, logGravar);
    }

    private static StringBuilder getLogGravar(String keyMapa) {
        if (mapaLogGravar == null) {
            mapaLogGravar = new HashMap<>();
        }
        StringBuilder logGravar = mapaLogGravar.get(keyMapa);
        if (logGravar == null) {
            logGravar = new StringBuilder();
        }
        return logGravar;
    }

    private static void reverterAjuste(Connection con) throws Exception {
        ResultSet rs = SuperFacadeJDBC.criarConsulta("select * from loggenerico order by codigo desc", con);
        while (rs.next()) {
            Integer contrato_codigo = 0;
            Integer loggenerico_codigo = 0;
            try {
                con.setAutoCommit(false);

                loggenerico_codigo = rs.getInt("codigo");
                JSONObject jsonInfo = new JSONObject(rs.getString("info"));
                contrato_codigo = jsonInfo.getInt("contrato_codigo");

//                if (!contrato_codigo.equals(62839)) {
//                    continue;
//                }

                if (jsonInfo.optBoolean("operacao_revertida")) {
                    throw new Exception("Contrato " + contrato_codigo + " | Operação já revertida");
                }

                ResultSet rsContrato = SuperFacadeJDBC.criarConsulta("select codigo,datalancamento from contrato where datalancamento::date >= '01-01-2024' and codigo = " + contrato_codigo, con);
                Date dataLancamentoContrato = null;
                if (rsContrato.next()) {
                    dataLancamentoContrato = rsContrato.getTimestamp("datalancamento");
                } else {
                    continue;
                }

                adicionarLog(keyLogGeral, "Ajustar Contrato | " + contrato_codigo + " | Data Lançamento | " + Uteis.getDataComHora(dataLancamentoContrato));

                //Excluir parcela criada e produto
                JSONArray movprodutos = jsonInfo.getJSONArray("movprodutos");
                for (int i = 0; i < movprodutos.length(); i++) {
                    JSONObject jsonProd = movprodutos.getJSONObject(i);

                    JSONObject jsonParcela = jsonProd.getJSONObject("movparcela");
                    Integer movparcela_criada_codigo = jsonParcela.getInt("movparcela_criada_codigo");

                    //parcela original voltar para em aberto
                    Integer movparcela_codigo = jsonParcela.getInt("movparcela_codigo");
                    String movparcela_dataregistro_anterior = jsonParcela.getString("movparcela_dataregistro_anterior");
                    Date movparcela_dataregistro_anteriorDate = Calendario.getDate("yyyyMMddHHmmss", movparcela_dataregistro_anterior);
                    Integer movproduto_criado_reajuste_codigo = jsonProd.getInt("movproduto_criado_reajuste_codigo");


                    boolean parcelaEmAberto = SuperFacadeJDBC.existe("select codigo from movparcela where situacao = 'EA' and codigo = " + movparcela_criada_codigo, con);
                    if (!parcelaEmAberto) {
                        throw new Exception("Parcela " + movparcela_criada_codigo + " | Está paga");
                    }

                    SuperFacadeJDBC.executarConsulta("update movprodutoparcela set movparcela = " + movparcela_codigo + " where movparcela = " + movparcela_criada_codigo, con);
                    SuperFacadeJDBC.executarConsulta("delete from movparcela where codigo = " + movparcela_criada_codigo, con);
                    SuperFacadeJDBC.executarConsulta("delete from movproduto where codigo = " + movproduto_criado_reajuste_codigo, con);
                    SuperFacadeJDBC.executarConsulta("delete from movprodutoparcela where movproduto = " + movproduto_criado_reajuste_codigo, con);
                    SuperFacadeJDBC.executarConsulta("update movparcela set situacao = 'EA', dataregistro = '"+Uteis.getDataFormatoBD(movparcela_dataregistro_anteriorDate)+"' where codigo = " + movparcela_codigo, con);
                    ++qtdParcelasGeral;
                }

                //ajustar valores contrato
                Double contrato_valorfinal_anterior = jsonInfo.getDouble("contrato_valorfinal_anterior");
                Double contrato_valorbasecalculo_anterior = jsonInfo.getDouble("contrato_valorbasecalculo_anterior");
                StringBuilder updateContrato = new StringBuilder();
                updateContrato.append("update contrato set valorfinal = ").append(Double.toString(contrato_valorfinal_anterior)).
                        append(", valorbasecalculo = ").append(Double.toString(contrato_valorbasecalculo_anterior)).
                        append(" where codigo = ").append(contrato_codigo);
                SuperFacadeJDBC.executarConsulta(updateContrato.toString(), con);

                jsonInfo.put("operacao_revertida", true);
                jsonInfo.put("operacao_revertida_data", Calendario.getDataAplicandoFormatacao(Calendario.hoje(), "yyyyMMddHHmmss"));

                String sql = "UPDATE loggenerico SET info = ? WHERE codigo = ?";
                PreparedStatement ps = con.prepareStatement(sql);
                int i = 0;
                ps.setString(++i, jsonInfo.toString());
                ps.setInt(++i, loggenerico_codigo);
                ps.execute();

                ++qtdContratosGeral;
                con.commit();
            } catch (Exception ex) {
                ex.printStackTrace();
                con.rollback();
                adicionarLog(keyLogGeral, "ERRO | Contrato | " + contrato_codigo + " | " + ex.getMessage());
            } finally {
                con.setAutoCommit(true);
            }
        }
    }
}




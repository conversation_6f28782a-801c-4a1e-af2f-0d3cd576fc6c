package importador.outros;


import br.com.pactosolucoes.enumeradores.SituacaoClienteEnum;
import importador.LeitorExcel2010;
import negocio.comuns.basico.ColaboradorVO;
import negocio.comuns.basico.EmpresaVO;
import negocio.comuns.basico.TipoColaboradorVO;
import negocio.comuns.utilitarias.Calendario;
import negocio.comuns.utilitarias.Uteis;
import negocio.facade.jdbc.basico.Colaborador;
import negocio.facade.jdbc.basico.TipoColaborador;
import org.apache.poi.xssf.usermodel.XSSFRow;

import java.sql.Connection;
import java.sql.DriverManager;
import java.util.List;

public class ImportarColaboradorAntigo {

    public static void main(String[] args) {
        try {
            // String pathExcel = "/opt/LEO SM.xlsx";
            String pathExcel = "C:/Users/<USER>/Documents/Colaborado_Engenho_Pacto.xlsx";
            Connection connection = obterConexao("localhost", "5432", "bdzillyonineexz");

            importarColaboradores(pathExcel, connection);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    public static void importarColaboradores(String pathExcel, Connection con) throws Exception {
        System.out.println("-----INICIO IMPORTACAO DE COLABORADORES-----");

        Colaborador colaborador = new Colaborador(con);
        TipoColaborador tipoColaborador = new TipoColaborador(con);

        List<XSSFRow> xssfRows = LeitorExcel2010.lerLinhas(pathExcel);
        ColaboradorVO colaboradorVOS = new ColaboradorVO();
        EmpresaVO empresaVO = new EmpresaVO();
        TipoColaboradorVO tipoColaboradorVO = new TipoColaboradorVO();
        int iLinha = 0;
        try {
            for (XSSFRow linha : xssfRows) {
                iLinha++;
                colaboradorVOS.getPessoa().setNome(String.valueOf(LeitorExcel2010.obterString(linha, 1)).trim());
                colaboradorVOS.getPessoa().setDataNasc(Uteis.getDate(LeitorExcel2010.obterString(linha, 2)));

                for (SituacaoClienteEnum situacaoClienteEnum : SituacaoClienteEnum.values()) {
                    if (situacaoClienteEnum.getDescricao().equalsIgnoreCase(String.valueOf(LeitorExcel2010.obterString(linha, 3)).trim())) {
                        colaboradorVOS.setSituacao(String.valueOf(LeitorExcel2010.obterString(linha, 3)).trim());
                    } else {
                        colaboradorVOS.setSituacao(SituacaoClienteEnum.INATIVO.getCodigo());
                    }
                }

                if (String.valueOf(LeitorExcel2010.obterString(linha, 4)).trim().equals("")) {
                    colaboradorVOS.setTipoColaborador("CO");
                } else {
                    colaboradorVOS.setTipoColaborador(String.valueOf(LeitorExcel2010.obterString(linha, 4)).trim());
                }

                empresaVO.setCodigo(Integer.valueOf(LeitorExcel2010.obterStringDoNumero(linha, 5)));
                colaboradorVOS.setEmpresa(empresaVO);
                colaboradorVOS.getPessoa().setDataCadastro(Calendario.hoje());
                colaboradorVOS.getPessoa().setNomeFoto("fotoPadrao.jpg");

                colaborador.incluir(colaboradorVOS);
                tipoColaboradorVO.setColaborador(colaboradorVOS.getCodigo());
                tipoColaboradorVO.setDescricao(colaboradorVOS.getTipoColaborador());
                tipoColaborador.incluir(tipoColaboradorVO);
                con.commit();

                System.out.println("**Linha: " + iLinha + " foi importada. **");
               // break;
            }
        } catch (Exception e) {
            con.rollback();
            System.out.println("******ERRO NA IMPORTACAO. (erro na linha " + iLinha + " da planilha excel.******");
            e.printStackTrace();
        } finally {
            con.setAutoCommit(true);
            con.close();
        }
        System.out.println("-----IMPORTACAO REALIZADA COM SUCESSO-----");
    }

    public static Connection obterConexao(String hostBD, String porta, String nomeBD) throws Exception {
        String userBD = "postgres";
        String passwordBD = "pactodb";
        String url = "jdbc:postgresql://" + hostBD + ":" + porta + "/" + nomeBD;
        String driver = "org.postgresql.Driver";
        Class.forName(driver);
        return DriverManager.getConnection(url, userBD, passwordBD);
    }
}




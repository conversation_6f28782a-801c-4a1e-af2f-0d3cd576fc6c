package importador.outros;

import controle.contrato.ContratoControle;
import importador.LeitorExcel;
import negocio.comuns.arquitetura.UsuarioVO;
import negocio.comuns.basico.ClienteVO;
import negocio.comuns.basico.ColaboradorVO;
import negocio.comuns.basico.EmpresaVO;
import negocio.comuns.contrato.ContratoModalidadeHorarioTurmaVO;
import negocio.comuns.contrato.ContratoModalidadeTurmaVO;
import negocio.comuns.contrato.ContratoModalidadeVO;
import negocio.comuns.plano.AmbienteVO;
import negocio.comuns.plano.CondicaoPagamentoVO;
import negocio.comuns.plano.HorarioTurmaVO;
import negocio.comuns.plano.HorarioVO;
import negocio.comuns.plano.ModalidadeEmpresaVO;
import negocio.comuns.plano.ModalidadeVO;
import negocio.comuns.plano.NivelTurmaVO;
import negocio.comuns.plano.PlanoCondicaoPagamentoVO;
import negocio.comuns.plano.PlanoDuracaoVO;
import negocio.comuns.plano.PlanoHorarioVO;
import negocio.comuns.plano.PlanoModalidadeVO;
import negocio.comuns.plano.PlanoModalidadeVezesSemanaVO;
import negocio.comuns.plano.PlanoProdutoSugeridoVO;
import negocio.comuns.plano.PlanoVO;
import negocio.comuns.plano.TurmaVO;
import negocio.comuns.utilitarias.Calendario;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UtilReflection;
import negocio.facade.jdbc.arquitetura.FacadeManager;
import negocio.facade.jdbc.utilitarias.Conexao;
import org.apache.poi.hssf.usermodel.HSSFRow;

import java.sql.Connection;
import java.sql.DriverManager;
import java.sql.ResultSet;
import java.sql.Statement;
import java.text.DecimalFormat;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

/**
 * Created by ulisses on 19/12/2017.
 */
public class ImportadorContratoPratique {

    public static Integer TURMA_COLUNA_MATRICULA = 0;
    public static Integer TURMA_COLUNA_NOME_ALUNO = 1;
    public static Integer TURMA_COLUNA_NOME_MODALIDADE = 2;
    public static Integer TURMA_COLUNA_NOME_TURMA = 3;
    public static Integer TURMA_COLUNA_HORA_INICIAL = 4;
    public static Integer TURMA_COLUNA_HORA_FINAL = 5;
    public static Integer TURMA_COLUNA_VEZES_SEMANA = 6;
    public static Integer TURMA_COLUNA_PROFESSOR = 7;

    public static Integer PLANO_COLUNA_MATRICULA = 0;
    public static Integer PLANO_DATA_FIM_CONTRATO = 2;
    public static Integer PLANO_MESES_DURACAO = 5;

    public static void main(String[] args){
        try{
            String pathExcel_plano = "C:\\temp\\migracao\\ALUNOSGINASTICALOCALIZADA.xls";
            String pathExcel_turma = "C:\\temp\\migracao\\TURMAGINASTICALOCALIZADA.xls";


            Connection connection = obterConexao("localhost","5432","pratique2" );
            importarContratos(pathExcel_plano,pathExcel_turma, connection);
        }catch (Exception e){
            e.printStackTrace();
        }
    }

    private static void importarContratos(String pathExcel_plano, String pathExcel_turma, Connection connection)throws Exception{
        Conexao.guardarConexaoForJ2SE("pratique", connection);
        try{
            System.out.println("INICIO IMPORTACAO");
            connection.setAutoCommit(false);
            List<AmbienteVO> listaAmbiente = FacadeManager.getFacade().getAmbiente().consultarPorDescricao("PISCINA",false,Uteis.NIVELMONTARDADOS_DADOSBASICOS);
            if ((listaAmbiente == null) || (listaAmbiente.size() <= 0)){
                throw new Exception("Não foi encontrado nenhum ambiente com o nome de PISCINA. Cadastre o ambiente e depois repita a importação.");
            }
            EmpresaVO empresaVO = FacadeManager.getFacade().getEmpresa().consultarPorCodigo(1,Uteis.NIVELMONTARDADOS_DADOSBASICOS);
            if (!UtilReflection.objetoMaiorQueZero(empresaVO, "getCodigo()")){
                throw new Exception("Não foi encontrado nenhuma empresa cadastrada. Cadastre uma empresa e depois repita a importação.");
            }
            ColaboradorVO professor = FacadeManager.getFacade().getColaborador().consultarPorNomeColaborador("PACTO", empresaVO.getCodigo(),Uteis.NIVELMONTARDADOS_DADOSBASICOS);
            if (!UtilReflection.objetoMaiorQueZero(professor, "getCodigo()")){
                throw new Exception("Não foi encontrado nenhum professor cadastrado com o nome PACTO. Cadastre um novo professor com o nome PACTO e depois repita a importação.");
            }
            HorarioVO horarioTurma = null;
            HorarioVO horarioLivre = null;
            List<HorarioVO> listaHorario = FacadeManager.getFacade().getHorario().consultarPorDescricao("",false,Uteis.NIVELMONTARDADOS_DADOSBASICOS);
            if ((listaHorario == null)  || (listaHorario.size() <= 0)){
                throw new Exception("Não foi encontrado nenhum horário cadastrado. Cadastre dois horário(HORARIO DA TURMA e LIVRE) depois repita a importação.");
            }
            for (HorarioVO horarioVO: listaHorario){
                if (horarioVO.getDescricao().contains("TURMA")){
                    horarioTurma = horarioVO;
                }else if (horarioVO.getDescricao().contains("LIVRE")){
                    horarioLivre = horarioTurma;
                }
            }
            if (!UtilReflection.objetoMaiorQueZero(horarioTurma, "getCodigo()")){
                throw new Exception("Não foi encontrado nenhum horário cadastrado com o nome HORARIO DA TURMA. Cadastre um novo horário com o nome HORARIO DA TURMA e depois repita a importação.");
            }
            if (!UtilReflection.objetoMaiorQueZero(horarioLivre, "getCodigo()")){
                throw new Exception("Não foi encontrado nenhum horário cadastrado com o nome LIVRE. Cadastre um novo horário com o nome LIVRE e depois repita a importação.");
            }

            List<HSSFRow> linhasPlano = LeitorExcel.lerLinhas(pathExcel_plano);
            List<HSSFRow> linhasTurma = LeitorExcel.lerLinhas(pathExcel_turma);
            List<DadosImportarTO> listaDadosImportar = new ArrayList<DadosImportarTO>();
            int i = 0;
            for (HSSFRow linha : linhasTurma) {
                lerDadosTurma(linha, listaDadosImportar, pathExcel_turma, i);
                i++;
            }
            i = 0;
            for (HSSFRow linha : linhasPlano) {
                lerDadosPlano(linha, listaDadosImportar, pathExcel_plano, i);
                i++;
            }
            /*for (DadosImportarTO dadosImportarTO: listaDadosImportar){
                System.out.println(dadosImportarTO.toString());
            }*/
            consultarGravarModalidade(listaDadosImportar, empresaVO);
            consultarGravarTurma(listaDadosImportar,empresaVO, listaAmbiente.get(0), professor);

            PlanoVO planoVO = FacadeManager.getFacade().getPlano().consultarPorDescricao("PLANO IMPORTACAO", Uteis.NIVELMONTARDADOS_DADOSBASICOS);
            gravarPlanoModalidade(planoVO, listaDadosImportar);

            connection.commit();
            connection.setAutoCommit(true);
            connection.setAutoCommit(false);

            StringBuilder nomes = new StringBuilder();
            // Validar se todos os clientes estão cadastrados.
            for(DadosImportarTO dadosImportarTO: listaDadosImportar) {
                dadosImportarTO.setClienteVO(FacadeManager.getFacade().getCliente().consultarPorCodigoMatriculaExterna(dadosImportarTO.getMatriculaExterna(), Uteis.NIVELMONTARDADOS_DADOSBASICOS));
                if (!UtilReflection.objetoMaiorQueZero(dadosImportarTO, "getClienteVO().getCodigo()")) {
                    if (nomes.length() <= 0){
                        nomes.append(dadosImportarTO.getNomeAluno());
                    }else{
                        nomes.append(",\n ").append(dadosImportarTO.getNomeAluno());
                    }
                }
            }
            if (nomes.length() > 0){
                throw new Exception("Os clientes abaixo não foram encontrados através da matriculaExterna. \n" + nomes.toString() + "\n Cadastre os clientes manualmente ou Retire-os da planilha e repita a operação." );
            }


            incluirContratos(listaDadosImportar,empresaVO, planoVO, horarioLivre, horarioTurma);
            connection.commit();
            System.out.println("IMPORTACAO REALIZADA COM SUCESSO");

        }catch (Exception e){
            connection.rollback();
            System.out.println("ERRO NA IMPORTACAO.");
            e.printStackTrace();
        } finally {
            connection.setAutoCommit(true);
            connection.close();
        }
    }

    private static void gravarPlanoModalidade(PlanoVO planoVO, List<DadosImportarTO> listaDadosImportar)throws Exception{
        if (!UtilReflection.objetoMaiorQueZero(planoVO, "getCodigo()")){
            throw new Exception("Não foi encontrado nenhum plano cadastrado com o nome PLANO IMPORTACAO. Cadastre um novo plano com o nome PLANO IMPORTACAO e depois repita a importação.");
        }
        Set<String> listaModalidadeVzSemana = new HashSet<String>();
        for (DadosImportarTO dadosImportarTO: listaDadosImportar){
            String modalidadeVzSemana = dadosImportarTO.getModalidadeVO().getCodigo() + ";" + dadosImportarTO.getNumeroVzSemana();
            listaModalidadeVzSemana.add(modalidadeVzSemana);
        }
        Set<Integer> listaCodigo = new HashSet<Integer>();
        for (String modVzSemana: listaModalidadeVzSemana){
            Integer codigoModalidade = Integer.parseInt(modVzSemana.split(";")[0]);
            Integer vezesSemana = Integer.parseInt(modVzSemana.split(";")[1]);

            PlanoModalidadeVO planoModalidadeVO = FacadeManager.getFacade().getPlanoModalidade().consultar(planoVO.getCodigo(), codigoModalidade,Uteis.NIVELMONTARDADOS_DADOSBASICOS);
            if (!UtilReflection.objetoMaiorQueZero(planoModalidadeVO, "getCodigo()")){
                planoModalidadeVO = new PlanoModalidadeVO();
                planoModalidadeVO.setValidarDados(false);
                planoModalidadeVO.setModalidade(new ModalidadeVO());
                planoModalidadeVO.getModalidade().setCodigo(codigoModalidade);
                planoModalidadeVO.setPlano(planoVO.getCodigo());
                planoModalidadeVO.setPlanoModalidadeVezesSemanaVOs(new ArrayList());
                FacadeManager.getFacade().getPlanoModalidade().incluir(planoModalidadeVO);
            }
            listaCodigo.add(planoModalidadeVO.getCodigo());

            PlanoModalidadeVezesSemanaVO planoModalidadeVezesSemanaVO  = FacadeManager.getFacade().getPlanoModalidadeVezesSemana().consultar(planoModalidadeVO.getCodigo(), vezesSemana,Uteis.NIVELMONTARDADOS_DADOSBASICOS);
            if (planoModalidadeVezesSemanaVO == null){
                planoModalidadeVezesSemanaVO = new PlanoModalidadeVezesSemanaVO();
                planoModalidadeVezesSemanaVO.setValidarDados(false);
                planoModalidadeVezesSemanaVO.setPlanoModalidade(planoModalidadeVO.getCodigo());
                planoModalidadeVezesSemanaVO.setNrVezes(vezesSemana);
                planoModalidadeVezesSemanaVO.setPercentualDesconto(0.0);
                planoModalidadeVezesSemanaVO.setValorEspecifico(0.0);
                planoModalidadeVezesSemanaVO.setTipoValor("VE");
                planoModalidadeVezesSemanaVO.setTipoOperacao("RE");
                planoModalidadeVezesSemanaVO.setReferencia(false);
                FacadeManager.getFacade().getPlanoModalidadeVezesSemana().incluir(planoModalidadeVezesSemanaVO);
            }
        }

        for (Integer codigo: listaCodigo){
            PlanoModalidadeVO planoModalidadeVO = FacadeManager.getFacade().getPlanoModalidade().consultarPorChavePrimaria(codigo,Uteis.NIVELMONTARDADOS_TODOS);
            StringBuilder descVzSemana = new StringBuilder();
            for (Object obj: planoModalidadeVO.getPlanoModalidadeVezesSemanaVOs()){
                PlanoModalidadeVezesSemanaVO planoModalidadeVezesSemanaVO = (PlanoModalidadeVezesSemanaVO)obj;
                String vP;
                if (planoModalidadeVezesSemanaVO.getNrVezes() == 1){
                    vP = " Vez";
                }else{
                    vP = " Vezes";
                }
                DecimalFormat decimalFormat = new DecimalFormat("R$ #,##0.00");
                if (descVzSemana.length() <= 0){
                    descVzSemana.append(planoModalidadeVezesSemanaVO.getNrVezes()).append(vP).append(" - " + decimalFormat.format(0));
                }else{
                    descVzSemana.append(", ").append(planoModalidadeVezesSemanaVO.getNrVezes()).append(vP).append(" - " + decimalFormat.format(0));
                }
            }
            Statement st = FacadeManager.getFacade().getPlanoModalidade().getCon().createStatement();
            st.execute("update planoModalidade set listaVezesSemana = '" +descVzSemana.toString() + "' where codigo ="+  codigo);
        }


    }

    private static boolean jaImportouContrato(DadosImportarTO dadosImportarTO, PlanoVO planoVO)throws Exception{
        StringBuilder sql = new StringBuilder();
        sql.append("select cm.* \n");
        sql.append("from contratoModalidade cm \n");
        sql.append("inner join contrato c on c.codigo = cm.contrato \n");
        sql.append("where c.pessoa = ").append(dadosImportarTO.getClienteVO().getPessoa().getCodigo());
        sql.append(" and cm.vezesSemana = ").append(dadosImportarTO.getNumeroVzSemana());
        sql.append(" and c.plano = ").append(planoVO.getCodigo());
        sql.append(" and cm.modalidade = ").append(dadosImportarTO.getModalidadeVO().getCodigo());
        Statement st = FacadeManager.getFacade().getContrato().getCon().createStatement();
        ResultSet rs = st.executeQuery(sql.toString());
        return rs.next();
    }


    private static void montarDadosMatriculaHorarioTurma(ContratoControle contratoControle, DadosImportarTO dadosImportarTO)throws Exception{
        for (ContratoModalidadeVO contratoModalidadeVO : contratoControle.getContratoVO().getContratoModalidadeVOs()){
            contratoModalidadeVO.getModalidade().setModalidadeEscolhida(false);
        }
        ContratoModalidadeTurmaVO cmt = new ContratoModalidadeTurmaVO();
        for (HorarioTurmaVO horarioTurmaVO: dadosImportarTO.getListaHorarioTurma()){
            TurmaVO turmaVO = dadosImportarTO.getTurmaVO();
            turmaVO.setTurmaEscolhida(true);
            ContratoModalidadeHorarioTurmaVO cmht = new ContratoModalidadeHorarioTurmaVO();
            cmht.setHorarioTurma(horarioTurmaVO);
            cmht.getHorarioTurma().setHorarioTurmaEscolhida(true);
            cmt.getContratoModalidadeHorarioTurmaVOs().add(cmht);
            cmt.setTurma(turmaVO);
            for (ContratoModalidadeVO contratoModalidadeVO : contratoControle.getContratoVO().getContratoModalidadeVOs()){
                if ((contratoModalidadeVO.getModalidade().getCodigo().equals(dadosImportarTO.getModalidadeVO().getCodigo())) ){
                        //(contratoModalidadeVO.getModalidade().getNrVezes().intValue() == dadosImportarTO.getNumeroVzSemana().intValue())){
                    cmt.setContratoModalidade(contratoModalidadeVO.getCodigo());
                    if (contratoModalidadeVO.getContratoModalidadeTurmaVOs().size() == 0){
                        PlanoModalidadeVezesSemanaVO planoVezesSemanaVO = new PlanoModalidadeVezesSemanaVO();
                        planoVezesSemanaVO.setNrVezes(dadosImportarTO.getNumeroVzSemana());
                        planoVezesSemanaVO.setVezeSemanaEscolhida(true);
                        contratoModalidadeVO.setPlanoVezesSemanaVO(planoVezesSemanaVO );
                        contratoModalidadeVO.setNrVezesSemana(dadosImportarTO.getNumeroVzSemana());

                        contratoModalidadeVO.getContratoModalidadeTurmaVOs().add(cmt);
                    }
                    contratoModalidadeVO.getModalidade().setUtilizarTurma(true);
                    contratoModalidadeVO.getModalidade().setModalidadeEscolhida(true);
                    break;
                }
            }
        }

    }

    private static void montarDadosMatriculaHorarioLivre(ContratoControle contratoControle, DadosImportarTO dadosImportarTO)throws Exception{
        for (ContratoModalidadeVO contratoModalidadeVO : contratoControle.getContratoVO().getContratoModalidadeVOs()){
            contratoModalidadeVO.getModalidade().setModalidadeEscolhida(false);
        }
        for (ContratoModalidadeVO contratoModalidadeVO : contratoControle.getContratoVO().getContratoModalidadeVOs()){
            if ((contratoModalidadeVO.getModalidade().getCodigo().equals(dadosImportarTO.getModalidadeVO().getCodigo()))) {
                contratoModalidadeVO.setContratoModalidadeTurmaVOs(new ArrayList());
                PlanoModalidadeVezesSemanaVO planoVezesSemanaVO = new PlanoModalidadeVezesSemanaVO();
                planoVezesSemanaVO.setNrVezes(dadosImportarTO.getNumeroVzSemana());
                planoVezesSemanaVO.setVezeSemanaEscolhida(true);
                contratoModalidadeVO.setPlanoVezesSemanaVO(planoVezesSemanaVO );
                contratoModalidadeVO.setNrVezesSemana(dadosImportarTO.getNumeroVzSemana());
                contratoModalidadeVO.getModalidade().setUtilizarTurma(false);
                contratoModalidadeVO.getModalidade().setModalidadeEscolhida(true);
                break;
            }
        }
    }


    private static void incluirContratos(List<DadosImportarTO> listaDadosImportar, EmpresaVO empresaVO, PlanoVO planoVO, HorarioVO horarioLivre, HorarioVO horarioTurma)throws Exception{
        int total = 0;
        for(DadosImportarTO dadosImportarTO: listaDadosImportar){
            total++;
            dadosImportarTO.setClienteVO(FacadeManager.getFacade().getCliente().consultarPorCodigoMatriculaExterna(dadosImportarTO.getMatriculaExterna(),Uteis.NIVELMONTARDADOS_DADOSBASICOS));
            if (!UtilReflection.objetoMaiorQueZero(dadosImportarTO, "getClienteVO().getCodigo()")){
                throw  new Exception("Não foi encontrado nenhum cliente com a matriculaExterna " + dadosImportarTO.getMatriculaExterna());
            }
            if ((dadosImportarTO.getQtdeMesesDuracaoContrato() == null) || (dadosImportarTO.getQtdeMesesDuracaoContrato() <= 0)){
                throw  new Exception("Não foi encontrado a duração do plano para o cliente " + dadosImportarTO.getNomeAluno());
            }
            if (jaImportouContrato(dadosImportarTO,planoVO)){
                continue;
            }
            ContratoControle contratoControle = FacadeManager.getFacade().getContrato().obterContratoControle(planoVO.getCodigo(), dadosImportarTO.getClienteVO().getCodigo());
            contratoControle.getContratoVO().setImportacao(true);

            // ZERAR OS VALORES DOS PRODUTOS DOS PLANOS
            for (Object obj: contratoControle.getContratoVO().getPlano().getPlanoProdutoSugeridoVOs()) {
                PlanoProdutoSugeridoVO planoProdutoSugeridoVO = (PlanoProdutoSugeridoVO) obj;
                planoProdutoSugeridoVO.setValorProduto(0.0);
                planoProdutoSugeridoVO.getProduto().setValorFinal(0.0);
            }

            if (dadosImportarTO.getNumeroVzSemana() < 6){
                // HORARIO TURMA
                montarDadosMatriculaHorarioTurma(contratoControle,dadosImportarTO);
            }else{
                // HORARIO LIVRE
                montarDadosMatriculaHorarioLivre(contratoControle, dadosImportarTO);
            }

            // INFORMAR A DURAÇÃO DO CONTRATO
            PlanoDuracaoVO planoDuracaoVO = FacadeManager.getFacade().getPlanoDuracao().consultarPorNumeroMesesPlano(dadosImportarTO.getQtdeMesesDuracaoContrato(),planoVO.getCodigo(),Uteis.NIVELMONTARDADOS_TODOS);
            contratoControle.getContratoVO().setPlanoDuracao(planoDuracaoVO);

            // INFORMAR O HORÁRIO
            List<PlanoHorarioVO> listaPlanoHorario = FacadeManager.getFacade().getPlanoHorario().consultarPlanoHorarios(planoVO.getCodigo(), Uteis.NIVELMONTARDADOS_TODOS);
            PlanoHorarioVO planoHorarioSel = null;
            for (PlanoHorarioVO planoHorarioVO: listaPlanoHorario){
                if ((dadosImportarTO.getNumeroVzSemana() == 6) && (planoHorarioVO.getHorario().getDescricao().contains("LIVRE"))){
                    planoHorarioSel = planoHorarioVO;
                    break;
                }else if ((dadosImportarTO.getNumeroVzSemana() < 6) && (planoHorarioVO.getHorario().getDescricao().contains("TURMA"))){
                    planoHorarioSel = planoHorarioVO;
                    break;
                }
            }
            contratoControle.getContratoVO().setPlanoHorario(planoHorarioSel);

            // INFORMAR A CONDIÇÃO DE PAGAMENTO
            CondicaoPagamentoVO condicaoPagamentoVO = FacadeManager.getFacade().getCondicaoPagamento().consultarPorDescricao("A VISTA",Uteis.NIVELMONTARDADOS_DADOSBASICOS);
            PlanoCondicaoPagamentoVO planoCondicaoPagamentoVO =   FacadeManager.getFacade().getPlanoCondicaoPagamento().consultarPorPlanoDuracaoCondicao(planoDuracaoVO.getCodigo(),condicaoPagamentoVO.getCodigo(),Uteis.NIVELMONTARDADOS_TODOS);
            planoCondicaoPagamentoVO.getCondicaoPagamento().setCondicaoPagamentoEscolhida(true);
            contratoControle.selecionarCondicaoPagamento(planoCondicaoPagamentoVO);
            contratoControle.getContratoVO().setPlanoCondicaoPagamento(planoCondicaoPagamentoVO);


            contratoControle.getContratoVO().setVigenciaDe(dadosImportarTO.getDataInicioContrato());
            contratoControle.getContratoVO().setVigenciaAte(dadosImportarTO.getDataFimContrato());
            contratoControle.getContratoVO().setDataLancamento(Calendario.hoje());
            contratoControle.getContratoVO().setVigenciaAteAjustada(dadosImportarTO.getDataFimContrato());
            contratoControle.getContratoVO().setDataMatricula(dadosImportarTO.getDataInicioContrato());
            contratoControle.setDataInicioContrato(dadosImportarTO.getDataInicioContrato());

            String fecharNegociacao = contratoControle.fecharNegociacao();
            if (fecharNegociacao.contains("erro")) {
                throw new Exception(contratoControle.getMensagemDetalhada());
            }
            UsuarioVO usuarioVO = FacadeManager.getFacade().getUsuario().consultarPorChavePrimaria(1, Uteis.NIVELMONTARDADOS_MINIMOS);
            String gravarContrato = contratoControle.gravar(usuarioVO);
            if (gravarContrato.contains("erro")) {
                throw new Exception(contratoControle.getMensagemDetalhada());
            }
            System.out.println("CONTRATO INCLUIDO COM SUCESSO PARA O CLIENTE: " + dadosImportarTO.getNomeAluno());
        }
        System.out.println("\n\n TOTAL GERAL DE CONTRATOS INCLUIDOS: " + total);
    }




    private static void consultarGravarModalidade(List<DadosImportarTO> listaDadosImportar, EmpresaVO empresaVO)throws Exception{

        for (DadosImportarTO dadosImportarTO: listaDadosImportar){
            //String nomeModalidade = dadosImportarTO.getNomeModalidade() + " " + dadosImportarTO.getNrVezesSemanaTurma();
             String nomeModalidade = dadosImportarTO.getNomeModalidade();

             ModalidadeVO modalidadeVO = FacadeManager.getFacade().getModalidade().consultarPorNomeModalidade(nomeModalidade,false, Uteis.NIVELMONTARDADOS_TODOS);
             if (!UtilReflection.objetoMaiorQueZero(modalidadeVO, "getCodigo()")){
                 modalidadeVO = new ModalidadeVO();
                 modalidadeVO.setNome(nomeModalidade);
                 if (dadosImportarTO.getNrVezesSemanaTurma().toUpperCase().equals("6X")){
                     // HORÁRIO LIVVRE
                     modalidadeVO.setUtilizarTurma(false);
                 }else {
                     modalidadeVO.setUtilizarTurma(true);
                 }
                 modalidadeVO.setValidarDados(false);
                 modalidadeVO.setValorMensal(0.0);
                 modalidadeVO.setModalidadeDefault(false);
                 modalidadeVO.setUtilizarProduto(false);
                 modalidadeVO.setUsaTreino(false);
                 modalidadeVO.setModalidadeEmpresaVOs(new ArrayList());
                 modalidadeVO.setValorMensal(0.0);
                 ModalidadeEmpresaVO modalidadeEmpresaVO = new ModalidadeEmpresaVO();
                 modalidadeEmpresaVO.setEmpresa(empresaVO);
                 modalidadeVO.setModalidadeEmpresaVOs(new ArrayList());
                 modalidadeVO.getModalidadeEmpresaVOs().add(modalidadeEmpresaVO);
                 modalidadeVO.setNrVezes(Integer.parseInt(dadosImportarTO.getNrVezesSemanaTurma().substring(0, 1)));
                 modalidadeVO.setCrossfit(false);
                 modalidadeVO.setAtivo(true);
                 FacadeManager.getFacade().getModalidade().incluirSemCommit(modalidadeVO);
             }
            dadosImportarTO.setModalidadeVO(modalidadeVO);
        }

    }

    private static void consultarGravarTurma(List<DadosImportarTO> listaDadosImportar, EmpresaVO empresaVO, AmbienteVO ambienteVO, ColaboradorVO professor)throws Exception{

        for (DadosImportarTO dadosImportarTO: listaDadosImportar){
            if (dadosImportarTO.getNumeroVzSemana() < 6){
                TurmaVO turmaVO = FacadeManager.getFacade().getTurma().consultar(dadosImportarTO.getNomeTurma(), dadosImportarTO.getModalidadeVO().getCodigo(), empresaVO.getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS);
                if (turmaVO == null){
                    turmaVO = new TurmaVO();
                    turmaVO.setEmpresa(empresaVO);
                    turmaVO.setDescricao(dadosImportarTO.getNomeTurma());
                    turmaVO.setIdentificador(dadosImportarTO.getNomeTurma().substring(0,4));
                    turmaVO.setModalidade(dadosImportarTO.getModalidadeVO());
                    turmaVO.setDataInicialVigencia(Calendario.getDate("dd/MM/yyyy", "01/01/2016"));
                    turmaVO.setDataFinalVigencia(Calendario.getDate("dd/MM/yyyy", "25/12/2025"));
                    turmaVO.setIdadeMinima(0);
                    turmaVO.setIdadeMaxima(100);
                    turmaVO.setBloquearMatriculasAcimaLimite(true);
                    turmaVO.setBloquearReposicaoAcimaLimite(false);
                    turmaVO.setPermitirDesmarcarReposicoes(true);
                    turmaVO.setMinutosAntecedenciaDesmarcarAula(0);
                    turmaVO.setTipoAntecedenciaMarcarAula(1);
                    FacadeManager.getFacade().getTurma().incluir(turmaVO);
                }
                dadosImportarTO.setTurmaVO(turmaVO);
                consultarGravarHorarioTurma(dadosImportarTO, ambienteVO, professor);
            }
        }
    }

    private static void consultarGravarHorarioTurma(DadosImportarTO dadosImportarTO, AmbienteVO ambienteVO, ColaboradorVO professor)throws Exception{
        if (dadosImportarTO.getNrVezesSemanaTurma().toUpperCase().equals("2X")){
            // CONSULTAR HORÁRIO DE TERÇA-FERIA
            HorarioTurmaVO horarioTurmaTerca = FacadeManager.getFacade().getHorarioTurma().consultar(dadosImportarTO.getHoraInicioTurma(),dadosImportarTO.getHoraFimTurma(),"TR",dadosImportarTO.getTurmaVO().getCodigo(),ambienteVO.getCodigo(),1,professor.getCodigo(),Uteis.NIVELMONTARDADOS_DADOSBASICOS);
            if (!UtilReflection.objetoMaiorQueZero(horarioTurmaTerca, "getCodigo()")){
                horarioTurmaTerca = incluirHorarioTurma("TR",dadosImportarTO,professor,ambienteVO);
            }
            // CONSULTAR HORÁRIO DE QUINTA-FERIA
            HorarioTurmaVO horarioTurmaQuinta = FacadeManager.getFacade().getHorarioTurma().consultar(dadosImportarTO.getHoraInicioTurma(),dadosImportarTO.getHoraFimTurma(),"QI",dadosImportarTO.getTurmaVO().getCodigo(),ambienteVO.getCodigo(),1,professor.getCodigo(),Uteis.NIVELMONTARDADOS_DADOSBASICOS);
            if (!UtilReflection.objetoMaiorQueZero(horarioTurmaQuinta, "getCodigo()")){
                horarioTurmaQuinta = incluirHorarioTurma("QI",dadosImportarTO,professor,ambienteVO);
            }
            dadosImportarTO.setListaHorarioTurma(new ArrayList<HorarioTurmaVO>());
            dadosImportarTO.getListaHorarioTurma().add(horarioTurmaTerca);
            dadosImportarTO.getListaHorarioTurma().add(horarioTurmaQuinta);
        }else if (dadosImportarTO.getNrVezesSemanaTurma().toUpperCase().equals("3X")){
            // CONSULTAR HORÁRIO DE SEGUNDA-FERIA
            HorarioTurmaVO horarioTurmaSegunda = FacadeManager.getFacade().getHorarioTurma().consultar(dadosImportarTO.getHoraInicioTurma(),dadosImportarTO.getHoraFimTurma(),"SG",dadosImportarTO.getTurmaVO().getCodigo(),ambienteVO.getCodigo(),1,professor.getCodigo(),Uteis.NIVELMONTARDADOS_DADOSBASICOS);
            if (!UtilReflection.objetoMaiorQueZero(horarioTurmaSegunda, "getCodigo()")){
                horarioTurmaSegunda = incluirHorarioTurma("SG",dadosImportarTO,professor,ambienteVO);
            }
            // CONSULTAR HORÁRIO DE QUARTA-FERIA
            HorarioTurmaVO horarioTurmaQuarta = FacadeManager.getFacade().getHorarioTurma().consultar(dadosImportarTO.getHoraInicioTurma(),dadosImportarTO.getHoraFimTurma(),"QA",dadosImportarTO.getTurmaVO().getCodigo(),ambienteVO.getCodigo(),1,professor.getCodigo(),Uteis.NIVELMONTARDADOS_DADOSBASICOS);
            if (!UtilReflection.objetoMaiorQueZero(horarioTurmaQuarta, "getCodigo()")){
                horarioTurmaQuarta = incluirHorarioTurma("QA",dadosImportarTO,professor,ambienteVO);
            }
            // CONSULTAR HORÁRIO DE SEXTA-FERIA
            HorarioTurmaVO horarioTurmaSexta = FacadeManager.getFacade().getHorarioTurma().consultar(dadosImportarTO.getHoraInicioTurma(),dadosImportarTO.getHoraFimTurma(),"SX",dadosImportarTO.getTurmaVO().getCodigo(),ambienteVO.getCodigo(),1,professor.getCodigo(),Uteis.NIVELMONTARDADOS_DADOSBASICOS);
            if (!UtilReflection.objetoMaiorQueZero(horarioTurmaSexta, "getCodigo()")){
                horarioTurmaSexta = incluirHorarioTurma("SX",dadosImportarTO,professor,ambienteVO);
            }
            dadosImportarTO.setListaHorarioTurma(new ArrayList<HorarioTurmaVO>());
            dadosImportarTO.getListaHorarioTurma().add(horarioTurmaSegunda);
            dadosImportarTO.getListaHorarioTurma().add(horarioTurmaQuarta);
            dadosImportarTO.getListaHorarioTurma().add(horarioTurmaSexta);
        }else if (dadosImportarTO.getNrVezesSemanaTurma().toUpperCase().equals("1X")){
            // CONSULTAR HORÁRIO DE SEGUNDA-FERIA
            HorarioTurmaVO horarioTurmaSabado = FacadeManager.getFacade().getHorarioTurma().consultar(dadosImportarTO.getHoraInicioTurma(),dadosImportarTO.getHoraFimTurma(),"SB",dadosImportarTO.getTurmaVO().getCodigo(),ambienteVO.getCodigo(),1,professor.getCodigo(),Uteis.NIVELMONTARDADOS_DADOSBASICOS);
            if (!UtilReflection.objetoMaiorQueZero(horarioTurmaSabado, "getCodigo()")){
                horarioTurmaSabado = incluirHorarioTurma("SB",dadosImportarTO,professor,ambienteVO);
            }
            dadosImportarTO.setListaHorarioTurma(new ArrayList<HorarioTurmaVO>());
            dadosImportarTO.getListaHorarioTurma().add(horarioTurmaSabado);
        }


    }

    private static HorarioTurmaVO incluirHorarioTurma(String diaSemana, DadosImportarTO dadosImportarTO, ColaboradorVO professor, AmbienteVO ambienteVO)throws Exception{
        HorarioTurmaVO horarioTurmaVO = new HorarioTurmaVO();
        horarioTurmaVO = new HorarioTurmaVO();
        horarioTurmaVO.setTurma(dadosImportarTO.getTurmaVO().getCodigo());
        horarioTurmaVO.setNivelTurma(new NivelTurmaVO());
        horarioTurmaVO.getNivelTurma().setCodigo(1);
        horarioTurmaVO.setAmbiente(ambienteVO);
        horarioTurmaVO.setDiaSemana(diaSemana);
        horarioTurmaVO.montarDadosDiaSemanaNumero(horarioTurmaVO);
        horarioTurmaVO.setNrMaximoAluno(100);
        horarioTurmaVO.setIdentificadorTurma(dadosImportarTO.getTurmaVO().getIdentificador());
        horarioTurmaVO.setSituacao("AT");
        horarioTurmaVO.setProfessor(professor);
        horarioTurmaVO.setHoraInicial(dadosImportarTO.getHoraInicioTurma());
        horarioTurmaVO.setHoraFinal(dadosImportarTO.getHoraFimTurma());
        horarioTurmaVO.setToleranciaEntradaMinutos(30);
        FacadeManager.getFacade().getHorarioTurma().incluir(horarioTurmaVO);
        return horarioTurmaVO;
    }

    private static void lerDadosPlano(HSSFRow linha, List<DadosImportarTO> listaDadosImportar,String pathExcel_plano, int indiceLinha)throws Exception{
        Integer matriculaExterna = (Integer) LeitorExcel.obterNumero(linha, PLANO_COLUNA_MATRICULA).intValue();
        if ((matriculaExterna == null) || (matriculaExterna <=0)){
            throw new Exception("Erro no arquivo: " +pathExcel_plano + " na linha: " + indiceLinha + ". Não foi informado a matrícula do aluno" );
        }
        for (DadosImportarTO dadosImportarTO: listaDadosImportar){
            if (dadosImportarTO.getMatriculaExterna().intValue() == matriculaExterna.intValue()){
                dadosImportarTO.setDataFimContrato(LeitorExcel.obterDataNoFormatoData(linha, PLANO_DATA_FIM_CONTRATO));
                dadosImportarTO.setQtdeMesesDuracaoContrato(LeitorExcel.obterNumero(linha, PLANO_MESES_DURACAO).intValue());
                Calendar dataIni = Calendar.getInstance();
                dataIni.setTime(dadosImportarTO.getDataFimContrato());
                dataIni.add(Calendar.MONTH, (dadosImportarTO.getQtdeMesesDuracaoContrato()*-1));
                dataIni.add(Calendar.DAY_OF_MONTH, 1);
                dadosImportarTO.setDataInicioContrato(dataIni.getTime());
            }
        }

        /*DadosImportarTO dadosImportarTO = new ImportadorContratoPratique().new DadosImportarTO();
        dadosImportarTO.setMatriculaExterna(matriculaExterna);
        int index = listaDadosImportar.indexOf(dadosImportarTO);
        if (index >=0){
            dadosImportarTO = listaDadosImportar.get(index);
        }else{
            listaDadosImportar.add(dadosImportarTO);
        }
        dadosImportarTO.setNomeAluno(LeitorExcel.obterString(linha, 1));
        dadosImportarTO.setDataFimContrato(LeitorExcel.obterDataNoFormatoData(linha, 2));

        dadosImportarTO.setNomeModalidade(LeitorExcel.obterString(linha, 3));
        dadosImportarTO.setQtdeMesesDuracaoContrato(LeitorExcel.obterNumero(linha, 5).intValue());
        Calendar dataIni = Calendar.getInstance();
        dataIni.setTime(dadosImportarTO.getDataFimContrato());
        dataIni.add(Calendar.MONTH, (dadosImportarTO.getQtdeMesesDuracaoContrato()*-1));
        dadosImportarTO.setDataInicioContrato(dataIni.getTime());*/

    }

    private static void lerDadosTurma(HSSFRow linha, List<DadosImportarTO> listaDadosImportar,String pathExcel_Turma, int indiceLinha)throws Exception{
        Integer matriculaExterna = (Integer) LeitorExcel.obterNumero(linha, 0).intValue();
        if ((matriculaExterna == null) || (matriculaExterna <=0)){
            throw new Exception("Erro no arquivo: " +pathExcel_Turma + " na linha: " + indiceLinha + ". Não foi informado a matrícula do aluno" );
        }
        DadosImportarTO dadosImportarTO = new ImportadorContratoPratique().new DadosImportarTO();
        dadosImportarTO.setMatriculaExterna(matriculaExterna);
        /*int index = listaDadosImportar.indexOf(dadosImportarTO);
        if (index >=0){
            dadosImportarTO = listaDadosImportar.get(index);
        }else{
            listaDadosImportar.add(dadosImportarTO);
        }*/
        listaDadosImportar.add(dadosImportarTO);

        dadosImportarTO.setNomeAluno(LeitorExcel.obterString(linha, TURMA_COLUNA_NOME_ALUNO));
        dadosImportarTO.setNomeModalidade(LeitorExcel.obterString(linha, TURMA_COLUNA_NOME_MODALIDADE));
        dadosImportarTO.setNomeTurma(LeitorExcel.obterString(linha, TURMA_COLUNA_NOME_TURMA));
        dadosImportarTO.setHoraInicioTurma((new SimpleDateFormat("HH:mm")).format(LeitorExcel.obterDataNoFormatoData(linha, TURMA_COLUNA_HORA_INICIAL)));
        dadosImportarTO.setHoraFimTurma((new SimpleDateFormat("HH:mm")).format(LeitorExcel.obterDataNoFormatoData(linha, TURMA_COLUNA_HORA_FINAL)));
        dadosImportarTO.setNrVezesSemanaTurma(LeitorExcel.obterString(linha, TURMA_COLUNA_VEZES_SEMANA));
        dadosImportarTO.setNomeProfessor(LeitorExcel.obterString(linha, TURMA_COLUNA_PROFESSOR));
    }

    public class DadosImportarTO{
        private Integer matriculaExterna;
        private String nomeAluno;
        private Date dataInicioContrato;
        private Date dataFimContrato;
        private String nomeModalidade;
        private ClienteVO clienteVO;
        private double valorContrato = 0.01;
        private Integer qtdeMesesDuracaoContrato;
        private String nomeTurma;
        private String horaInicioTurma;
        private String horaFimTurma;
        private String nrVezesSemanaTurma;
        private String nomeProfessor;
        private ModalidadeVO modalidadeVO;
        private TurmaVO turmaVO;
        private List<HorarioTurmaVO>listaHorarioTurma;


        public Integer getMatriculaExterna() {
            return matriculaExterna;
        }

        public void setMatriculaExterna(Integer matriculaExterna) {
            this.matriculaExterna = matriculaExterna;
        }

        public String getNomeAluno() {
            return nomeAluno;
        }

        public void setNomeAluno(String nomeAluno) {
            this.nomeAluno = nomeAluno;
        }

        public Date getDataInicioContrato() {
            return dataInicioContrato;
        }

        public void setDataInicioContrato(Date dataInicioContrato) {
            this.dataInicioContrato = dataInicioContrato;
        }

        public Date getDataFimContrato() {
            return dataFimContrato;
        }

        public void setDataFimContrato(Date dataFimContrato) {
            this.dataFimContrato = dataFimContrato;
        }

        public String getNomeModalidade() {
            return nomeModalidade;
        }

        public void setNomeModalidade(String nomeModalidade) {
            this.nomeModalidade = nomeModalidade;
        }

        public ClienteVO getClienteVO() {
            return clienteVO;
        }

        public void setClienteVO(ClienteVO clienteVO) {
            this.clienteVO = clienteVO;
        }

        public double getValorContrato() {
            return valorContrato;
        }

        public void setValorContrato(double valorContrato) {
            this.valorContrato = valorContrato;
        }

        public Integer getQtdeMesesDuracaoContrato() {
            return qtdeMesesDuracaoContrato;
        }

        public void setQtdeMesesDuracaoContrato(Integer qtdeMesesDuracaoContrato) {
            this.qtdeMesesDuracaoContrato = qtdeMesesDuracaoContrato;
        }

        public String getNomeTurma() {
            return nomeTurma;
        }

        public void setNomeTurma(String nomeTurma) {
            this.nomeTurma = nomeTurma;
        }

        public ModalidadeVO getModalidadeVO() {
            return modalidadeVO;
        }

        public void setModalidadeVO(ModalidadeVO modalidadeVO) {
            this.modalidadeVO = modalidadeVO;
        }

        public TurmaVO getTurmaVO() {
            return turmaVO;
        }

        public void setTurmaVO(TurmaVO turmaVO) {
            this.turmaVO = turmaVO;
        }

        public String getHoraInicioTurma() {
            return horaInicioTurma;
        }

        public void setHoraInicioTurma(String horaInicioTurma) {
            this.horaInicioTurma = horaInicioTurma;
        }

        public String getHoraFimTurma() {
            return horaFimTurma;
        }

        public void setHoraFimTurma(String horaFimTurma) {
            this.horaFimTurma = horaFimTurma;
        }

        public String getNrVezesSemanaTurma() {
            return nrVezesSemanaTurma;
        }

        public void setNrVezesSemanaTurma(String nrVezesSemanaTurma) {
            this.nrVezesSemanaTurma = nrVezesSemanaTurma;
        }

        public String getNomeProfessor() {
            return nomeProfessor;
        }

        public void setNomeProfessor(String nomeProfessor) {
            this.nomeProfessor = nomeProfessor;
        }

        public List<HorarioTurmaVO> getListaHorarioTurma() {
            return listaHorarioTurma;
        }

        public void setListaHorarioTurma(List<HorarioTurmaVO> listaHorarioTurma) {
            this.listaHorarioTurma = listaHorarioTurma;
        }
        public Integer getNumeroVzSemana(){
            return Integer.parseInt(this.nrVezesSemanaTurma.substring(0,1));
        }


        public String toString(){
            SimpleDateFormat sdf = new SimpleDateFormat("dd/MM/yyyy");
            StringBuilder msg = new StringBuilder();
            msg.append(this.matriculaExterna).append(" - ");
            msg.append(this.nomeAluno).append(" - ");
            msg.append(sdf.format(this.dataInicioContrato)).append(" - ");
            msg.append(sdf.format(this.dataFimContrato)).append(" - ");
            msg.append("Modalidade: " + this.getNomeModalidade()).append(" - ");
            msg.append("Turma: " + this.getNomeTurma()).append(" - ");
            msg.append(this.getHoraInicioTurma()).append(" - ");
            msg.append(this.getHoraFimTurma()).append(" - ");
            msg.append(this.getNrVezesSemanaTurma()).append(" - ");
            msg.append(this.getNomeProfessor());
            return msg.toString();
        }

        @Override
        public boolean equals(Object obj){
            if ((obj == null) || (!(obj instanceof DadosImportarTO))){
                return false;
            }
            if (this.matriculaExterna == null){
                return false;
            }
            return ((DadosImportarTO)obj).getMatriculaExterna().equals(this.matriculaExterna);
        }
    }

    public static Connection obterConexao(String hostBD, String porta, String nomeBD)throws Exception{
        String userBD = "postgres";
        String passwordBD = "pactodb";
        String url = "jdbc:postgresql://" + hostBD + ":" + porta + "/" + nomeBD;
        String driver = "org.postgresql.Driver";
        Class.forName(driver);
        return DriverManager.getConnection(url, userBD, passwordBD);
    }

}

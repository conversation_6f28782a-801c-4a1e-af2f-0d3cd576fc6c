package importador.outros;

import importador.LeitorExcel;
import importador.LeitorExcel2010;
import negocio.comuns.plano.CategoriaProdutoVO;
import negocio.comuns.plano.ProdutoVO;
import negocio.comuns.plano.enumerador.TipoProduto;
import negocio.facade.jdbc.plano.Produto;
import org.apache.poi.hssf.usermodel.HSSFRow;
import org.apache.poi.xssf.usermodel.XSSFRow;

import java.sql.Connection;
import java.sql.DriverManager;
import java.util.List;

public class ImportarProdutosAntigoExcel {

    public static void main(String[] args) {
        try {
            String pathExcel = "C:/Users/<USER>/Documents/produtos.xls";
            Connection connection = obterConexao("localhost", "5432", "bdzillyonineexz");

            importarProdutos(pathExcel, connection);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    public static void importarProdutos(String pathExcel, Connection con) throws Exception {
        System.out.println("-----INICIO IMPORTACAO DE PRODUTOS-----");

        Produto produto = new Produto(con);

        List<HSSFRow> hssfRows = LeitorExcel.lerLinhas(pathExcel);
        ProdutoVO produtoVO = new ProdutoVO();
        CategoriaProdutoVO categoriaProdutoVO = new CategoriaProdutoVO();
        int iLinha = 0;
        try {
            for (HSSFRow linha : hssfRows) {
                iLinha++;

                if(String.valueOf(LeitorExcel.obterString(linha,2)).trim().equals("")){
                    produtoVO.setTipoProduto(TipoProduto.retornaPorDescricao("PRODUTO_ESTOQUE"));
                }else{
                    produtoVO.setTipoProduto(TipoProduto.retornaPorDescricao(String.valueOf(LeitorExcel.obterString(linha,2))));
                }
                if(String.valueOf(LeitorExcel.obterString(linha,3)).trim().equals("")){
                    produtoVO.setDescricao("PRODUTO");
                }
                if(String.valueOf(LeitorExcel.obterString(linha,3)).length() <= 50) {
                    produtoVO.setDescricao(String.valueOf(LeitorExcel.obterString(linha, 3)));
                }else{
                    System.out.println("Linha " + iLinha + " não importada pois o nome ultrapassa 50 caracteres");
                    continue;
                }

                produtoVO.setValorFinal(LeitorExcel.obterNumero(linha,4).doubleValue());
                categoriaProdutoVO.setCodigo(10);
                categoriaProdutoVO.setDescricao("LOJA");
                categoriaProdutoVO.setBloquearAcessoSeProdutoAberto(false);

                produtoVO.setCategoriaProduto(categoriaProdutoVO);

                produto.incluir(produtoVO,true);
                con.commit();

                System.out.println("**Linha: " + iLinha + " foi importada. **");
               // break;
            }
        } catch (Exception e) {
            con.rollback();
            System.out.println("******ERRO NA IMPORTACAO. (erro na linha " + iLinha + " da planilha excel.******");
            e.printStackTrace();
        } finally {
            con.setAutoCommit(true);
            con.close();
        }
        System.out.println("-----IMPORTACAO REALIZADA COM SUCESSO-----");
    }

    public static Connection obterConexao(String hostBD, String porta, String nomeBD) throws Exception {
        String userBD = "postgres";
        String passwordBD = "pactodb";
        String url = "jdbc:postgresql://" + hostBD + ":" + porta + "/" + nomeBD;
        String driver = "org.postgresql.Driver";
        Class.forName(driver);
        return DriverManager.getConnection(url, userBD, passwordBD);
    }
}




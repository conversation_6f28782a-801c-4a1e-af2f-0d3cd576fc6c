package importador.outros;

import negocio.comuns.acesso.AutorizacaoAcessoGrupoEmpresarialVO;
import negocio.comuns.acesso.IntegracaoAcessoGrupoEmpresarialVO;
import negocio.comuns.utilitarias.Uteis;
import negocio.facade.jdbc.acesso.AutorizacaoAcessoGrupoEmpresarial;

import java.sql.Connection;
import java.sql.DriverManager;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;

public class ProcessoCorrigirCodAcessoIntegracaoAcessoEngenharia {

    private static HashMap<String, String> mapChaveNomeBD = new HashMap<>();

    public static void main(String[] args) {
        try {
            prepararMapaChaves();

            String urlFranqueadora = "********************************************************************";
            try (Connection conFranqueadora = DriverManager.getConnection(urlFranqueadora, "postgres", "pactodb")) {
                int offset = 0;
                int processados = 0;
                int atualizadosTotal = 0;

                ProcessoCorrigirCodAcessoIntegracaoAcessoEngenharia processo = new ProcessoCorrigirCodAcessoIntegracaoAcessoEngenharia();
                List<AutorizacaoAcessoGrupoEmpresarialVO> listaAutorizacoesErradas = processo.consultarAutorizacoes(conFranqueadora, offset);
                while (!listaAutorizacoesErradas.isEmpty()) {
                    int atualizados = 0;
                    for (AutorizacaoAcessoGrupoEmpresarialVO autorizacao : listaAutorizacoesErradas) {
                        IntegracaoAcessoGrupoEmpresarialVO integracao = autorizacao.getIntegracao();

                        String urlBanco = "********************************/" + mapChaveNomeBD.get(integracao.getChave());
                        try (Connection con = DriverManager.getConnection(urlBanco, "postgres", "pactodb")) {
                            boolean encontrou = false;
                            String sql = "select codacesso from cliente cli inner join pessoa pes on cli.pessoa = pes.codigo where cfp =  '" + autorizacao.getCpf() + "'; ";
                            try (PreparedStatement ps = con.prepareStatement(sql)) {
                                try (ResultSet rs = ps.executeQuery()) {
                                    if (rs.next()) {
                                        atualizados++;
                                        String codAcesso = rs.getString("codacesso");
                                        atualizarCodAcesso("NU" + codAcesso, autorizacao.getCodigo(), conFranqueadora);
                                        encontrou = true;
                                    }
                                }
                            }
                            if (!encontrou) {
                                sql = "select codacesso from cliente cli where pessoa =  '" + autorizacao.getCodigoPessoa() + "'; ";
                                try (PreparedStatement ps = con.prepareStatement(sql)) {
                                    try (ResultSet rs = ps.executeQuery()) {
                                        if (rs.next()) {
                                            atualizados++;
                                            String codAcesso = rs.getString("codacesso");
                                            atualizarCodAcesso("NU" + codAcesso, autorizacao.getCodigo(), conFranqueadora);
                                            encontrou = true;
                                        }
                                    }
                                }
                            }
                        }
                    }

                    offset += (listaAutorizacoesErradas.size() - atualizados);
                    processados += listaAutorizacoesErradas.size();
                    atualizadosTotal += atualizados;
                    System.out.println("Progresso: " + atualizadosTotal + "/" + processados);
                    listaAutorizacoesErradas = processo.consultarAutorizacoes(conFranqueadora, offset);
                }

            }

        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    private static void atualizarCodAcesso(String codAcesso, Integer cod, Connection conFranqueadora) throws SQLException {
        String update = "UPDATE autorizacaoacessogrupoempresarial SET codAcesso = '" + codAcesso + "' WHERE codigo = " + cod;
        try (PreparedStatement ps = conFranqueadora.prepareStatement(update)) {
            ps.execute();
        }
    }

    private static void prepararMapaChaves() throws SQLException {
        String urlOamd = "********************************/OAMD";
        try (Connection con = DriverManager.getConnection(urlOamd, "postgres", "pactodb")) {
            String sqlEmpresa = "SELECT chave, \"nomeBD\" FROM empresa";

            mapChaveNomeBD = new HashMap<>();
            try (PreparedStatement ps = con.prepareStatement(sqlEmpresa)) {
                try (ResultSet rs = ps.executeQuery()) {
                    while (rs.next()) {
                        String chave = rs.getString("chave");
                        String nomeBD = rs.getString("nomeBD");
                        mapChaveNomeBD.put(chave, nomeBD);
                    }
                }
            }
        }
    }

    private List<AutorizacaoAcessoGrupoEmpresarialVO> consultarAutorizacoes(Connection conFranqueadora, int offset) throws Exception {
        List<AutorizacaoAcessoGrupoEmpresarialVO> autorizacoesFalhas = new ArrayList<>();

        String sql = "select * from autorizacaoacessogrupoempresarial a where codacesso  = 'NU' and tipopessoa = 'CL' order by CODIGO desc LIMIT 100 offset " + offset + ";";
        try (PreparedStatement ps = conFranqueadora.prepareStatement(sql)) {
            try (ResultSet rs = ps.executeQuery()) {
                while (rs.next()) {
                    AutorizacaoAcessoGrupoEmpresarialVO aut = AutorizacaoAcessoGrupoEmpresarial.montarDados(rs, Uteis.NIVELMONTARDADOS_DADOSBASICOS, conFranqueadora);
                    autorizacoesFalhas.add(aut);
                }
            }
        }
        return autorizacoesFalhas;
    }


}


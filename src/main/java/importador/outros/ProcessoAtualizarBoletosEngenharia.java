package importador.outros;

import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisValidacao;
import negocio.facade.jdbc.arquitetura.SuperFacadeJDBC;

import java.sql.Connection;
import java.sql.DriverManager;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;

public class ProcessoAtualizarBoletosEngenharia {

    public static void main(String[] args) {
        try {
            Connection conZW = DriverManager.getConnection("*********************************************************************", "postgres", "pactodb");
            Connection conSQL = getConSQLServer("EVO_TR287847_F1_160123");
            ajustarBoletos(conSQL, conZW);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    public static Connection getConSQLServer(String bd) {
        try {
            String driver = "net.sourceforge.jtds.jdbc.Driver";
            String conexao = "jdbc:jtds:sqlserver:";
            Class.forName(driver).newInstance();
            return DriverManager.getConnection(conexao + "//localhost/" + bd, "sa", "pactodb");
        } catch (Exception e) {
            e.printStackTrace();
            return null;
        }
    }

    private static void ajustarBoletos(Connection conSQL, Connection conZW) throws Exception {
        preencherNumeroInterno(conZW);
        preencherDataRegistro(conSQL, conZW);
        preencherIdExterno(conSQL, conZW);
        preencherLinhaDigitavel(conSQL, conZW);
//        alterarSituacaoBoletosSemIdExternoELink(conZW);
    }

    private static void preencherNumeroInterno(Connection conZW) throws SQLException {
       SuperFacadeJDBC.executarUpdate("update boleto set numerointerno = idimportacao where situacao <> 2 and idimportacao not ilike '%null%' and coalesce(idimportacao,'') <> '' and coalesce(numerointerno  ,'') = '';", conZW);
    }

    private static void alterarSituacaoBoletosSemIdExternoELink(Connection conZW) throws SQLException {
        SuperFacadeJDBC.executarUpdate("update boleto set situacao = 2 where situacao <> 2 and idimportacao not ilike '%null%' and coalesce(idimportacao,'') <> '' and coalesce(linkboleto ,'') = '' and coalesce(idexterno ,'') = '';", conZW);
    }

    private static void preencherDataRegistro(Connection conSQL, Connection conZW) throws Exception {
        String sql1 = "select array_to_string(array(" +
                "select idimportacao from boleto where  idimportacao not ilike '%null%' and coalesce(idimportacao,'') <> '' and dataregistro is null" +
                "), ',', '')";
        PreparedStatement sqlConsulta = conZW.prepareStatement(sql1);
        ResultSet rs = sqlConsulta.executeQuery();
        int atual = 0;
        if (rs.next()) {
            String codigos = rs.getString(1);

            if (UteisValidacao.emptyString(codigos)) {
                Uteis.logarDebug("preencherDataRegistro | Sem registros para ajustar");
                return;
            }

            StringBuilder sql2 = new StringBuilder();
            sql2.append("select \n");
            sql2.append("DT_CADASTRO, \n");
            sql2.append("ID_EXTERNO, \n");
            sql2.append("ID_BOLETO \n");
            sql2.append("from RECEBIMENTOS_BOLETOS \n");
            sql2.append("where ID_BOLETO in (").append(codigos).append(") ");

            Integer total = SuperFacadeJDBC.contar("select count(*) as qtd from ( " + sql2 + " ) as sql", conSQL);
            if (UteisValidacao.emptyNumber(total)) {
                Uteis.logarDebug("preencherDataRegistro | Total " + total);
                return;
            }

            PreparedStatement ps2 = conSQL.prepareStatement(sql2.toString());
            ResultSet rs1 = ps2.executeQuery();
            while (rs1.next()) {

                String ID_BOLETO = rs1.getString("ID_BOLETO");

                String update = "update boleto set dataregistro = ? where idimportacao = ?;";
                PreparedStatement pstup = conZW.prepareStatement(update);
                int i = 0;
                pstup.setTimestamp(++i, Uteis.getDataJDBCTimestamp(rs1.getTimestamp("DT_CADASTRO")));
                pstup.setString(++i, ID_BOLETO);
                Uteis.logarDebug("preencherDataRegistro | " + atual++ + "/" + total + " - " + pstup);
                pstup.execute();
            }
        }
    }

    private static void preencherIdExterno(Connection conSQL, Connection conZW) throws Exception {
        String sql1 = "select array_to_string(array(" +
                "select idimportacao from boleto where situacao <> 2 and idimportacao not ilike '%null%' and coalesce(idimportacao,'') <> '' and coalesce(idexterno ,'') = ''" +
                "), ',', '')";
        PreparedStatement sqlConsulta = conZW.prepareStatement(sql1);
        ResultSet rs = sqlConsulta.executeQuery();
        int atual = 0;
        if (rs.next()) {
            String codigos = rs.getString(1);

            if (UteisValidacao.emptyString(codigos)) {
                Uteis.logarDebug("preencherIdExterno | Sem registros para ajustar");
                return;
            }

            StringBuilder sql2 = new StringBuilder();
            sql2.append("select \n");
            sql2.append("DT_CADASTRO, \n");
            sql2.append("ID_EXTERNO, \n");
            sql2.append("ID_BOLETO \n");
            sql2.append("from RECEBIMENTOS_BOLETOS \n");
            sql2.append("where coalesce(ID_EXTERNO,'') <> '' ");
            sql2.append("and ID_BOLETO in (").append(codigos).append(") ");

            Integer total = SuperFacadeJDBC.contar("select count(*) as qtd from ( " + sql2 + " ) as sql", conSQL);
            if (UteisValidacao.emptyNumber(total)) {
                Uteis.logarDebug("preencherIdExterno | Total " + total);
                return;
            }

            PreparedStatement ps2 = conSQL.prepareStatement(sql2.toString());
            ResultSet rs1 = ps2.executeQuery();
            while (rs1.next()) {

                String ID_EXTERNO = rs1.getString("ID_EXTERNO");
                String ID_BOLETO = rs1.getString("ID_BOLETO");

                String update = "update boleto set idexterno = ? where idimportacao = ?;";
                PreparedStatement pstup = conZW.prepareStatement(update);
                int i = 0;
                pstup.setString(++i, ID_EXTERNO);
                pstup.setString(++i, ID_BOLETO);
                Uteis.logarDebug("preencherIdExterno | " + atual++ + "/" + total + " - " + pstup);
                pstup.execute();
            }
        }
    }

    private static void preencherLinhaDigitavel(Connection conSQL, Connection conZW) throws Exception {
        String sql1 = "select array_to_string(array(" +
                "select idimportacao from boleto where situacao <> 2 and idimportacao not ilike '%null%' and coalesce(idimportacao,'') <> '' and coalesce(linhadigitavel ,'') = ''" +
                "), ',', '')";
        PreparedStatement sqlConsulta = conZW.prepareStatement(sql1);
        ResultSet rs = sqlConsulta.executeQuery();
        int atual = 0;
        if (rs.next()) {
            String codigos = rs.getString(1);

            if (UteisValidacao.emptyString(codigos)) {
                Uteis.logarDebug("preencherLinhaDigitavel | Sem registros para ajustar");
                return;
            }

            StringBuilder sql2 = new StringBuilder();
            sql2.append("select \n");
            sql2.append("ID_BOLETO, \n");
            sql2.append("CODIGO_BARRAS \n");
            sql2.append("from RECEBIMENTOS_BOLETOS \n");
            sql2.append("where coalesce(CODIGO_BARRAS,'') <> '' ");
            sql2.append("and ID_BOLETO in (").append(codigos).append(") ");

            Integer total = SuperFacadeJDBC.contar("select count(*) as qtd from ( " + sql2 + " ) as sql", conSQL);
            if (UteisValidacao.emptyNumber(total)) {
                Uteis.logarDebug("preencherLinhaDigitavel | Total " + total);
                return;
            }

            PreparedStatement ps2 = conSQL.prepareStatement(sql2.toString());
            ResultSet rs1 = ps2.executeQuery();
            while (rs1.next()) {

                String CODIGO_BARRAS = rs1.getString("CODIGO_BARRAS");
                String ID_BOLETO = rs1.getString("ID_BOLETO");

                String update = "update boleto set linhadigitavel = ? where idimportacao = ?;";
                PreparedStatement pstup = conZW.prepareStatement(update);
                int i = 0;
                pstup.setString(++i, CODIGO_BARRAS);
                pstup.setString(++i, ID_BOLETO);
                Uteis.logarDebug("preencherLinhaDigitavel | " + atual++ + "/" + total + " - " + pstup);
                pstup.execute();
            }
        }
    }

//    private static void preencherIdExterno(Connection conSQL, Connection conZW) throws SQLException {
//        String sql1 = "select array_to_string(array(select idimportacao from boleto where idimportacao not ilike '%null%'and coalesce(idimportacao,'') <> '' and coalesce(idexterno ,'') = ''), ',', '')";
//        PreparedStatement sqlConsulta = conZW.prepareStatement(sql1);
//        ResultSet rs = sqlConsulta.executeQuery();
//        int atual = 0;
//        if (rs.next()) {
//            String codigos = rs.getString(1);
//
//            StringBuilder sql2 = new StringBuilder();
//            sql2.append("select  \n");
//            sql2.append("CONCAT('update boleto set idexterno = ''',ID_EXTERNO,''' where idimportacao = ''',ID_BOLETO,''';') as teste, \n");
//            sql2.append("ID_EXTERNO, \n");
//            sql2.append("ID_BOLETO \n");
//            sql2.append("from RECEBIMENTOS_BOLETOS \n");
//            sql2.append("where coalesce(ID_EXTERNO,'') <> '' \n");
//            sql2.append("and ID_BOLETO in (").append(codigos).append(")");
//            PreparedStatement ps2 = conSQL.prepareStatement(sql2.toString());
//            ResultSet rs1 = ps2.executeQuery();
//            while (rs1.next()) {
//                String update = rs1.getString("teste");
//                Uteis.logarDebug(atual++ + " - " + update);
//                String ID_EXTERNO = rs1.getString("ID_EXTERNO");
//                if (!UteisValidacao.emptyString(ID_EXTERNO)) {
//                    SuperFacadeJDBC.executarUpdate(update, conZW);
//                }
//            }
//        }
//    }
}

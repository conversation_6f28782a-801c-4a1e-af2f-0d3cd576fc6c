package importador.outros;

import negocio.comuns.basico.ClienteVO;
import negocio.comuns.utilitarias.Calendario;
import negocio.comuns.utilitarias.SinteticoException;
import negocio.comuns.utilitarias.Uteis;
import negocio.facade.jdbc.arquitetura.SuperFacadeJDBC;
import negocio.facade.jdbc.arquitetura.ZillyonWebFacade;
import negocio.facade.jdbc.basico.Cliente;
import relatorio.negocio.comuns.sad.SituacaoClienteSinteticoEnum;

import java.io.IOException;
import java.sql.Connection;
import java.sql.DriverManager;
import java.sql.ResultSet;

public class ProcessarSinteticoTodosClientes {

    private static StringBuilder logGravar;
    private static String nomeBanco = "";

    public static void main(String[] args) throws IOException {
        try {
            Connection con = DriverManager.getConnection("********************************************************", "postgres", "pactodb");
            nomeBanco = con.getCatalog();

            Cliente clienteDAO = new Cliente(con);
            ZillyonWebFacade zwFacade = new ZillyonWebFacade(con);

            String sql = "select codigo from cliente where titularplanocompartilhado is not null";
            ResultSet rs = SuperFacadeJDBC.criarConsulta(sql, con);
            Integer total = SuperFacadeJDBC.contar("select count(*) as qtd from ( " + sql + " ) as sql", con);
            adicionarLog("TODOS OS CLIENTES | Total --> " + total);
            int atual = 0;
            while (rs.next()) {
                Integer cliente = rs.getInt("codigo");
                adicionarLog(++atual + "/" + total + " -- Cliente " + cliente);
                try {
                    ClienteVO clienteVO = clienteDAO.consultarPorChavePrimaria(cliente, Uteis.NIVELMONTARDADOS_TODOS);
                    zwFacade.atualizarSintetico(clienteVO, Calendario.hoje(), SituacaoClienteSinteticoEnum.GRUPO_TODOS, false);
                } catch (SinteticoException se) {
                    adicionarLog("Problema ao processar sintético do cliente: " + cliente);
                }
            }
            clienteDAO = null;
            zwFacade = null;
        } catch (Exception ex) {
            ex.printStackTrace();
            adicionarLog(ex.getMessage());
        } finally {
//            Uteis.salvarArquivo(ProcessarSinteticoTodosClientes.class.getSimpleName() + "_" + nomeBanco + "-" + Calendario.getData("yyyyMMddHHmmss") + ".txt", getLogGravar().toString(), "C:\\Processos\\" + File.separator);
        }
    }

    private static void adicionarLog(String msg) {
        String s = "[DEBUG] " + Calendario.hoje() + " --> " + msg;
        System.out.println(s);
        getLogGravar().append(s).append("\n");
    }

    private static StringBuilder getLogGravar() {
        if (logGravar == null) {
            logGravar = new StringBuilder();
        }
        return logGravar;
    }
}




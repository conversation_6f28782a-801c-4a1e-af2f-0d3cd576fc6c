package importador.outros;

import br.com.pactosolucoes.enumeradores.SituacaoContratoEnum;
import negocio.comuns.utilitarias.Calendario;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisValidacao;
import negocio.facade.jdbc.arquitetura.SuperFacadeJDBC;
import org.apache.poi.hssf.usermodel.HSSFSheet;
import org.apache.poi.hssf.usermodel.HSSFWorkbook;
import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.CellStyle;
import org.apache.poi.ss.usermodel.CellType;
import org.apache.poi.ss.usermodel.FillPatternType;
import org.apache.poi.ss.usermodel.HorizontalAlignment;
import org.apache.poi.ss.usermodel.IndexedColors;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.VerticalAlignment;
import servicos.propriedades.PropsService;

import java.io.File;
import java.io.FileOutputStream;
import java.sql.Connection;
import java.sql.DriverManager;
import java.sql.ResultSet;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class GerarRelatorioCuponsDescontoEngenharia {

    private static String caminhoArquivo;
    private static String[] colunas = new String[]{"chave","empresa","nomeportadorcupom","datapremioportadorcupom","contrato","contratoestornado","tipoContrato","nomeplano","valorOriginalContrato","valordesconto","valorfinalcontrato","numerocupom","cupomnomefixo","campanha_id","descricaocampanha"};

    public static void main(String[] args) throws Exception {

        Connection conOAMD = DriverManager.getConnection("*****************************************************", "postgres", "pactodb");
        Integer redeEmpresaId = 467; // 467 Engenharia
        caminhoArquivo = "C:\\pacto\\relatorios";

        gerarRelatorioCuponsDesconto(conOAMD, redeEmpresaId);

    }

    public static void gerarRelatorioCuponsDesconto(Connection conOAMD, Integer redeEmpresaId) throws Exception {
        String sql = "select \n" +
                "\thcup.chavePortadorCupom as chave,\n" +
                "\t(select e.nomefantasia from empresafinanceiro e where e.chaveZW = hcup.chavePortadorCupom and e.empresazw = hcup.empresaportadorcupom limit 1) as empresa,\n" +
                "\thcup.nomeportadorcupom,\n" +
                "\thcup.datapremioportadorcupom,\n" +
                "\thcup.contrato,\n" +
                "\thcup.contratoestornado,\n" +
                "\thcup.numerocupom,\n" +
                "\thcup.cupomnomefixo,\n" +
                "\tcamp.id as campanha_id,\n" +
                "\tcamp.descricaocampanha \n" +
                "from HistoricoUtilizacaoCupomDesconto hcup \n" +
                "\tinner join campanhaCupomDesconto camp on camp.id = hcup.campanhaCupomDesconto_id \n" +
                "where 1 = 1\n" +
                "and ((hcup.dataPremioPortadorCupom is not null) or (hcup.dataPremioAluno is not null))\n" +
                "and camp.redeEmpresa_id = " + redeEmpresaId + "\n";

        int total = SuperFacadeJDBC.contar("select count(sql.*) from (" + sql + ") as sql \n", conOAMD);
        int count = 0;

        ResultSet rs = SuperFacadeJDBC.criarConsulta(sql, conOAMD);

        Map<String, Connection> mapConnections = new HashMap<>();
        List<Map<String, Object>> lista = new ArrayList<>();
        while (rs.next()) {
            Uteis.logarDebug((++count) + "\\" + total + " - Processando relatorio...");

            String chave = rs.getString("chave");
            Integer codigoContrato = rs.getInt("contrato");
            Boolean contratoEstornado = rs.getBoolean("contratoestornado");
            String tipoContrato = "-";
            String plano = "-";
            Double valorOriginalContrato = 0.0;
            Double valorDesconto = 0.0;
            Double valorFinalContrato = 0.0;
            if (!UteisValidacao.emptyString(chave) && !UteisValidacao.emptyNumber(codigoContrato) && !contratoEstornado) {
                ResultSet rsEmpresa = SuperFacadeJDBC.criarConsulta("select * from empresa where chave = '" + chave + "'", conOAMD);
                if (rsEmpresa.next()) {
                    String host = rsEmpresa.getString("hostBD");
                    String porta = rsEmpresa.getString("porta");
                    String nomeBD = rsEmpresa.getString("nomeBD");
                    String user = rsEmpresa.getString("userBD");
                    String passWord = rsEmpresa.getString("passwordBD");
                    try {
                        Connection conAdm = null;
                        if (mapConnections.containsKey(nomeBD)) {
                            conAdm = mapConnections.get(nomeBD);
                        } else {
                            conAdm = DriverManager.getConnection("jdbc:postgresql://" + host + ":" + porta + "/" + nomeBD, user, passWord);
                            mapConnections.put(nomeBD, conAdm);
                        }
                        ResultSet rsContrato = SuperFacadeJDBC.criarConsulta("select \n" +
                                " con.codigo, con.situacaocontrato, pla.codigo as codigoplano, pla.descricao as nomeplano,\n" +
                                " trunc((select sum(mpro.precounitario) from movproduto mpro inner join produto pro on pro.codigo = mpro.produto where mpro.contrato  = con.codigo and pro.tipoproduto in ('PM','MA','RE','RN'))::numeric,2) as valorOriginalContrato, \n" +
                                " trunc(con.valorfinal::numeric,2) as valorfinal, \n" +
                                " trunc((select sum(mpro.valordesconto) from movproduto mpro where mpro.contrato  = con.codigo)::numeric,2) as valorDesconto \n" +
                                "from contrato con \n" +
                                "inner join plano pla on pla.codigo = con.plano \n" +
                                "where con.codigo = " + codigoContrato, conAdm);
                        if (rsContrato.next()) {
                            tipoContrato = SituacaoContratoEnum.obterPorCodigo(rsContrato.getString("situacaocontrato")).name();
                            plano = rsContrato.getInt("codigoplano") + " - " + rsContrato.getString("nomeplano");
                            valorOriginalContrato = rsContrato.getDouble("valorOriginalContrato");
                            valorDesconto = rsContrato.getDouble("valorDesconto");
                            valorFinalContrato = rsContrato.getDouble("valorfinal");
                        }
                    } catch (Exception e) {
                        e.printStackTrace();
                    }
                }
            }
            Map<String, Object> item = new HashMap<>();
            int i = 0;
            item.put(colunas[i++], rs.getString("chave"));
            item.put(colunas[i++], rs.getString("empresa"));
            item.put(colunas[i++], rs.getString("nomeportadorcupom"));
            item.put(colunas[i++], Calendario.getDataAplicandoFormatacao(rs.getDate("datapremioportadorcupom"), "dd/MM/yyyy HH:mm:mm"));
            item.put(colunas[i++], rs.getInt("contrato"));
            item.put(colunas[i++], (rs.getBoolean("contratoestornado") ? "SIM" : "NÃO"));
            item.put(colunas[i++], tipoContrato);
            item.put(colunas[i++], plano);
            item.put(colunas[i++], valorOriginalContrato);
            item.put(colunas[i++], valorDesconto);
            item.put(colunas[i++], valorFinalContrato);
            item.put(colunas[i++], rs.getString("numerocupom"));
            item.put(colunas[i++], (rs.getBoolean("cupomnomefixo") ? "SIM" : "NÃO"));
            item.put(colunas[i++], rs.getInt("campanha_id"));
            item.put(colunas[i++], rs.getString("descricaocampanha"));
            lista.add(item);
        }
        Uteis.logarDebug("Gerando Planilha...");
        gerarArquivoExcel(lista);
    }

    private static void gerarArquivoExcel(List<Map<String, Object>> lista) {
        try {

            if (UteisValidacao.emptyList(lista)) {
                throw new Exception("Lista vazia");
            }

            // Criar arquivo excel
            HSSFWorkbook hssfWorkbook = new HSSFWorkbook();

            //lista
            gerarSheetGeral(hssfWorkbook, lista);

            //salvar arquivo
            caminhoArquivo = UteisValidacao.emptyString(caminhoArquivo) ? PropsService.getPropertyValue(PropsService.diretorioArquivos) : caminhoArquivo;
            File file = new File(caminhoArquivo + File.separator + "RelatorioCuponsDesconto_" + Calendario.hoje().getTime() + ".xls");

            FileOutputStream out = new FileOutputStream(file);
            hssfWorkbook.write(out);
            out.close();
            hssfWorkbook.close();

            Uteis.logarDebug("#####################################");
            Uteis.logarDebug("#####################################");
            Uteis.logarDebug("########## ARQUIVO EXCEL ############");
            Uteis.logarDebug(file.getAbsolutePath());
            Uteis.logarDebug("#####################################");
            Uteis.logarDebug("#####################################");
            Uteis.logarDebug("#####################################");

        } catch (Exception ex) {
            ex.printStackTrace();
            Uteis.logarDebug("#####################################");
            Uteis.logarDebug("########## ERRO GERAR EXCEL #########");
            Uteis.logarDebug(ex.getMessage());
            Uteis.logarDebug("#####################################");
            Uteis.logarDebug("#####################################");
        }
    }

    private static void gerarSheetGeral(HSSFWorkbook hssfWorkbook, List<Map<String, Object>> lista) {

        HSSFSheet sheet = hssfWorkbook.createSheet("RelatorioCuponsDesconto");

        int rownum = 0;
        int cellnum = 0;
        Row row;

        // Definindo alguns padroes de layout
        sheet.setDefaultColumnWidth(20);
        sheet.setDefaultRowHeight((short) 300);

        row = sheet.createRow(rownum++);
        for (String coluna : colunas) {
            cabecalho(hssfWorkbook, row, cellnum++, coluna);
        }

        for (Map<String, Object> item: lista) {

            row = sheet.createRow(rownum++);
            cellnum = 0;
            for (int i = 0; i < colunas.length; i++) {
                criarCelula(hssfWorkbook, cellnum++, row, item.get(colunas[i]));
            }
        }
    }

    private static void setValorCelula(Cell cell, Object valor) {
        if (valor instanceof String) {
            cell.setCellType(CellType.STRING);
            cell.setCellValue((String) valor);
        } else if (valor instanceof Integer) {
            cell.setCellType(CellType.NUMERIC);
            cell.setCellValue((Integer) valor);
        } else if (valor instanceof Double) {
            cell.setCellType(CellType.NUMERIC);
            cell.setCellValue((Double) valor);
        } else if (valor instanceof Date) {
            cell.setCellValue((Date) valor);
        } else if (valor instanceof Boolean) {
            cell.setCellType(CellType.BOOLEAN);
            cell.setCellValue((Boolean) valor);
        } else {
            cell.setCellValue(valor == null ? "" : valor.toString());
        }
    }

    private static Cell cabecalho(HSSFWorkbook hssfWorkbook, Row row, int cellnum, String textoCelula) {
        CellStyle cellStyle = hssfWorkbook.createCellStyle();
        cellStyle.setFillForegroundColor(IndexedColors.GREY_25_PERCENT.getIndex());
        cellStyle.setFillPattern(FillPatternType.SOLID_FOREGROUND);
        cellStyle.setAlignment(HorizontalAlignment.CENTER);
        cellStyle.setVerticalAlignment(VerticalAlignment.CENTER);

        Cell cell = row.createCell(cellnum++);
        cell.setCellStyle(cellStyle);
        cell.setCellValue(textoCelula);
        return cell;
    }

    private static void criarCelula(HSSFWorkbook hssfWorkbook, int numeroCelula, Row row, Object valor) {
        Cell cell = row.createCell(numeroCelula);
        setValorCelula(cell, valor);
    }
}

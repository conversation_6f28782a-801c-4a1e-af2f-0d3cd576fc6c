package importador.outros;

import br.com.pactosolucoes.comuns.util.Formatador;
import br.com.pactosolucoes.enumeradores.LogGenericoTipoEnum;
import negocio.comuns.arquitetura.UsuarioVO;
import negocio.comuns.financeiro.MovParcelaVO;
import negocio.comuns.financeiro.MovProdutoParcelaVO;
import negocio.comuns.financeiro.PagamentoMovParcelaVO;
import negocio.comuns.financeiro.ReciboPagamentoVO;
import negocio.comuns.utilitarias.Calendario;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisValidacao;
import negocio.facade.jdbc.arquitetura.Log;
import negocio.facade.jdbc.arquitetura.SuperFacadeJDBC;
import negocio.facade.jdbc.arquitetura.Usuario;
import negocio.facade.jdbc.financeiro.MovPagamento;
import negocio.facade.jdbc.financeiro.MovParcela;
import negocio.facade.jdbc.financeiro.MovProdutoParcela;
import negocio.facade.jdbc.financeiro.PagamentoMovParcela;
import negocio.facade.jdbc.financeiro.ProdutosPagosServico;
import negocio.facade.jdbc.financeiro.ReciboPagamento;
import org.json.JSONArray;
import org.json.JSONObject;

import java.io.File;
import java.io.IOException;
import java.sql.Connection;
import java.sql.DriverManager;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;

public class ProcessoAlterarParcelasRecibo {

    private static Map<String, StringBuilder> mapaLogGravar;
    private static String nomeBanco = "";
    private static String logGeral = "GERAL";

    public static void main(String[] args) throws IOException {
        Date d1 = Calendario.hoje();
        try {
            Connection con = DriverManager.getConnection("**************************************************************", "postgres", "pactodb");
            nomeBanco = con.getCatalog();

            Integer codigoRecibo = 69848;
            List<Integer> novasParcelas = new ArrayList<>();
            novasParcelas.add(92631);

            UsuarioVO usuarioVO = new Usuario(con).getUsuarioRecorrencia();

            processar(codigoRecibo, novasParcelas, usuarioVO, true, con);
        } catch (Exception ex) {
            ex.printStackTrace();
            adicionarLog(logGeral, ex.getMessage());
        } finally {
            Date d2 = Calendario.hoje();
            adicionarLog(logGeral, "=============================================");
            adicionarLog(logGeral, "Banco: " + nomeBanco);
            adicionarLog(logGeral, "Tempo Total: " + Calendario.diferencaEmSegundos(d1, d2) + " segundos");
            adicionarLog(logGeral, "=============================================");
            Uteis.salvarArquivo(ProcessoAlterarParcelasRecibo.class.getSimpleName() + "_" + nomeBanco + "-" + Calendario.getData("yyyyMMddHHmmss") + ".txt",
                    getLogGravar(logGeral).toString(), "C:\\Processos\\" + File.separator);
        }
    }

    public static void processar(Integer codigoRecibo, List<Integer> listaParcelas,
                                 UsuarioVO usuarioVO, boolean simular, Connection con) throws Exception {
        Log logDAO;
        MovParcela movParcelaDAO;
        MovPagamento movPagamentoDAO;
        ReciboPagamento reciboPagamentoDAO;
        MovProdutoParcela movProdutoParcelaDAO;
        PagamentoMovParcela pagamentoMovParcelaDAO;
        try {
            con.setAutoCommit(false);

            logDAO = new Log(con);
            movParcelaDAO = new MovParcela(con);
            reciboPagamentoDAO = new ReciboPagamento(con);
            movPagamentoDAO = new MovPagamento(con);
            movProdutoParcelaDAO = new MovProdutoParcela(con);
            pagamentoMovParcelaDAO = new PagamentoMovParcela(con);

            StringBuilder parcelas = new StringBuilder();
            for (Integer par : listaParcelas) {
                parcelas.append(",").append(par);
            }
            parcelas = new StringBuilder(parcelas.toString().replaceFirst(",", ""));

            adicionarLog(logGeral, "=============================================");
            adicionarLog(logGeral, "================ Parâmetros =================");
            adicionarLog(logGeral, "=============================================");
            adicionarLog(logGeral, "Banco de dados: " + nomeBanco);
            adicionarLog(logGeral, "Recibo: " + codigoRecibo);
            adicionarLog(logGeral, "Parcelas: " + parcelas);
            if (usuarioVO != null) {
                adicionarLog(logGeral, "Usuario - Codigo: " + usuarioVO.getCodigo());
                adicionarLog(logGeral, "Usuario - Nome: " + usuarioVO.getNome());
                adicionarLog(logGeral, "Usuario - Username: " + usuarioVO.getUsername());
            }
            adicionarLog(logGeral, "=============================================");

            JSONObject log = new JSONObject();
            if (usuarioVO != null) {
                log.put("usuario_codigo", usuarioVO.getCodigo());
                log.put("usuario_nome", usuarioVO.getNome());
                log.put("usuario_username", usuarioVO.getUsername());
            }
            log.put("recibopagamento", codigoRecibo);
            log.put("dataatual", Calendario.getDataAplicandoFormatacao(Calendario.hoje(), "yyyyMMddHHmmss"));

            JSONArray parcelas_anterior = new JSONArray();
            JSONArray produtos_anterior = new JSONArray();
            JSONArray movProdutoParcela_anterior = new JSONArray();
            JSONArray pagamentoMovParcela_anterior = new JSONArray();
            JSONArray parcelas_nova = new JSONArray();
            JSONArray produtos_nova = new JSONArray();
            JSONArray movProdutoParcela_nova = new JSONArray();
            JSONArray pagamentoMovParcela_nova = new JSONArray();


            ReciboPagamentoVO reciboPagamentoVO = reciboPagamentoDAO.consultarPorChavePrimaria(codigoRecibo, Uteis.NIVELMONTARDADOS_TODOS);
            reciboPagamentoVO.setPagamentosDesteRecibo(movPagamentoDAO.consultarPorCodigoRecibo(reciboPagamentoVO.getCodigo(), false, Uteis.NIVELMONTARDADOS_TODOS));
            List<MovParcelaVO> parcelaVOS = movParcelaDAO.consultar("select * from movparcela where codigo in (" + parcelas + ")", Uteis.NIVELMONTARDADOS_DADOSBASICOS);

            Double valorParcelas = 0.0;
            Set<Integer> contrato = new HashSet<>();
            List<MovProdutoParcelaVO> movProdutoParcelaVOSNovos = new ArrayList<>();
            for (MovParcelaVO movParcelaVO : parcelaVOS) {
                if (!movParcelaVO.getSituacao().equals("EA")) {
                    throw new Exception("Parcela " + movParcelaVO.getCodigo() + " - " + movParcelaVO.getDescricao() + " - Não está em aberto");
                }
                if (movParcelaDAO.parcelaEstaBloqueadaPorCobranca(movParcelaVO)) {
                    throw new Exception("Parcela " + movParcelaVO.getCodigo() + " - " + movParcelaVO.getDescricao() + " - Está em alguma cobrança pendente");
                }
                valorParcelas += Uteis.arredondarForcando2CasasDecimais(movParcelaVO.getValorParcela());
                movProdutoParcelaVOSNovos.addAll(movProdutoParcelaDAO.consultarPorCodigoMovParcela(movParcelaVO.getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS));

                if (!UteisValidacao.emptyNumber(movParcelaVO.getContrato().getCodigo())) {
                    contrato.add(movParcelaVO.getContrato().getCodigo());
                }
            }

            Double valorRecibo = Uteis.arredondarForcando2CasasDecimais(reciboPagamentoVO.getValorTotal());
            if (!valorRecibo.equals(valorParcelas)) {
                throw new Exception("A soma das parcelas " + Formatador.formatarValorMonetario(valorParcelas) + " - Não condiz com o valor do recibo " + Formatador.formatarValorMonetario(reciboPagamentoVO.getValorTotal()));
            }

            if (reciboPagamentoVO.getPagamentosDesteRecibo().size() > 1) {
                throw new Exception("Recibo tem mais que 1 pagamento vinculado");
            }

            List<MovProdutoParcelaVO> movProdutoParcelaVOSAnterior = movProdutoParcelaDAO.consultarPorReciboPagamneto(codigoRecibo, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
            List<PagamentoMovParcelaVO> pagamentoMovParcelaVOSAnterior = pagamentoMovParcelaDAO.consultarPorReciboPagamneto(codigoRecibo, Uteis.NIVELMONTARDADOS_MOVPAGAMENTOSINTETICO);


            for (MovProdutoParcelaVO obj : movProdutoParcelaVOSAnterior) {
                //colocar produtos em aberto
                SuperFacadeJDBC.executarUpdate("update movproduto set situacao = 'EA' where codigo = " + obj.getMovProduto(), con);
                //colocar parcelas em aberto
                SuperFacadeJDBC.executarUpdate("update movparcela set situacao = 'EA' where codigo = " + obj.getMovParcela(), con);
                //remover o recibo
                SuperFacadeJDBC.executarUpdate("update movProdutoParcela set recibopagamento = null where codigo = " + obj.getCodigo(), con);

                JSONObject objLog = new JSONObject();
                objLog.put("movparcelaoriginalmultajuros", obj.getMovParcelaOriginalMultaJuros().getCodigo());
                objLog.put("recibopagamento", obj.getReciboPagamento().getCodigo());
                objLog.put("valorpago", obj.getValorPago());
                objLog.put("movparcela", obj.getMovParcela());
                objLog.put("movproduto", obj.getMovProduto());
                movProdutoParcela_anterior.put(objLog);
                produtos_anterior.put(obj.getMovProduto());
                parcelas_anterior.put(obj.getMovParcela());
            }

            for (PagamentoMovParcelaVO obj : pagamentoMovParcelaVOSAnterior) {
                SuperFacadeJDBC.executarUpdate("delete from pagamentoMovParcela where codigo = " + obj.getCodigo(), con);

                JSONObject objLog = new JSONObject();
                objLog.put("recibopagamento", obj.getReciboPagamento().getCodigo());
                objLog.put("valorpago", obj.getValorPago());
                objLog.put("movparcela", obj.getMovParcela().getCodigo());
                objLog.put("movpagamento", obj.getMovPagamento());
                pagamentoMovParcela_anterior.put(objLog);
            }


            //novos
            for (MovProdutoParcelaVO obj : movProdutoParcelaVOSNovos) {
                if (obj.getReciboPagamento() != null &&
                        !UteisValidacao.emptyNumber(obj.getReciboPagamento().getCodigo())) {
                    continue;
                }
                //colocar produtos em aberto
                SuperFacadeJDBC.executarUpdate("update movproduto set situacao = 'PG' where codigo = " + obj.getMovProduto(), con);
                //colocar parcelas em aberto
                SuperFacadeJDBC.executarUpdate("update movparcela set situacao = 'PG' where codigo = " + obj.getMovParcela(), con);
                //remover o recibo
                SuperFacadeJDBC.executarUpdate("update movProdutoParcela set recibopagamento = " + codigoRecibo + " where codigo = " + obj.getCodigo(), con);

                JSONObject objLog = new JSONObject();
                objLog.put("movparcelaoriginalmultajuros", obj.getMovParcelaOriginalMultaJuros().getCodigo());
                objLog.put("recibopagamento", codigoRecibo);
                objLog.put("valorpago", obj.getValorPago());
                objLog.put("movparcela", obj.getMovParcela());
                objLog.put("movproduto", obj.getMovProduto());
                movProdutoParcela_nova.put(objLog);
                produtos_nova.put(obj.getMovProduto());
                parcelas_nova.put(obj.getMovParcela());
            }

            for (MovParcelaVO obj : parcelaVOS) {
                PagamentoMovParcelaVO pagamentoMovParcelaVO = new PagamentoMovParcelaVO();
                pagamentoMovParcelaVO.setMovParcela(obj);
                pagamentoMovParcelaVO.setReciboPagamento(reciboPagamentoVO);
                pagamentoMovParcelaVO.setMovPagamento(reciboPagamentoVO.getPagamentosDesteRecibo().get(0).getCodigo());
                pagamentoMovParcelaVO.setValorPago(obj.getValorParcela());
                pagamentoMovParcelaDAO.incluir(pagamentoMovParcelaVO);

                JSONObject objLog = new JSONObject();
                objLog.put("recibopagamento", pagamentoMovParcelaVO.getReciboPagamento().getCodigo());
                objLog.put("valorpago", pagamentoMovParcelaVO.getValorPago());
                objLog.put("movparcela", pagamentoMovParcelaVO.getMovParcela().getCodigo());
                objLog.put("movpagamento", pagamentoMovParcelaVO.getMovPagamento());
                objLog.put("codigo", pagamentoMovParcelaVO.getCodigo());
                pagamentoMovParcela_nova.put(objLog);
            }

            if (contrato.size() == 1) {
                SuperFacadeJDBC.executarUpdate("update recibopagamento set contrato = " + contrato.iterator().next() + " where codigo = " + codigoRecibo, con);
            } else {
                SuperFacadeJDBC.executarUpdate("update recibopagamento set contrato = null where codigo = " + codigoRecibo, con);
            }

            ProdutosPagosServico.setarProdutosPagos(con, codigoRecibo);

            log.put("parcelas_anterior", parcelas_anterior);
            log.put("produtos_anterior", produtos_anterior);
            log.put("movprodutoparcela_anterior", movProdutoParcela_anterior);
            log.put("pagamentomovparcela_anterior", pagamentoMovParcela_anterior);
            log.put("parcelas_nova", parcelas_nova);
            log.put("produtos_nova", produtos_nova);
            log.put("movprodutoparcela_nova", movProdutoParcela_nova);
            log.put("pagamentomovparcela_nova", pagamentoMovParcela_nova);

            logDAO.incluirLogGenerico(LogGenericoTipoEnum.ALTERAR_MOVPARCELA_RECIBO, usuarioVO, log.toString());

            if (simular) {
                con.rollback();
            } else {
                con.commit();
            }
        } catch (Exception ex) {
            con.rollback();
            ex.printStackTrace();
            throw ex;
        } finally {
            con.setAutoCommit(true);
            logDAO = null;
            movParcelaDAO = null;
            reciboPagamentoDAO = null;
            movProdutoParcelaDAO = null;
            pagamentoMovParcelaDAO = null;
        }
    }

    private static void adicionarLog(String keyMapa, String msg) {
        String s = "[DEBUG] " + Uteis.getDataAtualAplicandoFormatacao("dd/MM/yyyy hh:mm:ss") + " --> " + msg;
        System.out.println(s);
        StringBuilder logGravar = getLogGravar(keyMapa).append(s).append("\n");
        mapaLogGravar.put(keyMapa, logGravar);
    }

    private static StringBuilder getLogGravar(String keyMapa) {
        if (mapaLogGravar == null) {
            mapaLogGravar = new HashMap<>();
        }
        StringBuilder logGravar = mapaLogGravar.get(keyMapa);
        if (logGravar == null) {
            logGravar = new StringBuilder();
        }
        return logGravar;
    }
}

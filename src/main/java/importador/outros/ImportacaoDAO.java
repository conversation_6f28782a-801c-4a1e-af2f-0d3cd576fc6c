package importador.outros;

import java.sql.Connection;
import java.sql.ResultSet;
import java.sql.SQLException;

import negocio.comuns.basico.ClienteVO;
import negocio.comuns.plano.ProdutoVO;
import negocio.comuns.utilitarias.Uteis;
import negocio.facade.jdbc.arquitetura.SuperEntidade;
import negocio.facade.jdbc.basico.Cliente;
import negocio.facade.jdbc.plano.Produto;
import negocio.facade.jdbc.utilitarias.Conexao;

public class ImportacaoDAO extends SuperEntidade {

	public ImportacaoDAO() throws Exception {
		super();
	}
	public ImportacaoDAO(Connection con) throws Exception {
		super(con);
	}
	
	public ClienteVO consultarClientePorIdExterno(Integer idExterno) throws Exception{
		String sql = "SELECT c.* FROM cliente c, pessoa p WHERE p.codigo = c.pessoa AND p.idexterno = "+idExterno;
		ResultSet query = criarConsulta(sql, con);
		if(query.next()){
			return Cliente.montarDados(query, Uteis.NIVELMONTARDADOS_DADOSENTIDADESUBORDINADAS, con);
		}else
			return new ClienteVO();
	}
	
	
	public ProdutoVO consultarProdutoPorNome(String nome, Double valor) throws Exception{
		String sql = "SELECT * FROM produto WHERE descricao ILIKE '"+nome+"' ";
		ResultSet query = criarConsulta(sql, con);
		if(query.next()){
			return Produto.montarDados(query, Uteis.NIVELMONTARDADOS_DADOSENTIDADESUBORDINADAS, con);
		}else{
			StringBuilder insert = new StringBuilder();
			insert.append("INSERT into produto (tipoproduto, valorfinal, descricao) ");
			insert.append("VALUES ('SS', "+valor+", '"+nome+"' ) ");
			executarConsulta(insert.toString(), con);
			ProdutoVO produto = new ProdutoVO();
			produto.setDescricao(nome);
			produto.setValorFinal(valor);
			produto.setTipoProduto("SS");
			produto.setCodigo(getFacade().getProduto().obterValorChavePrimariaCodigo());
			return produto;
		}
	}
	public Integer adicionarBancoOperadora(String tipo, String nome, int codigo) throws Exception{
		if(tipo.equals("OP")){
			executarConsulta("INSERT INTO operadoracartao (descricao, codigooperadora, credito, qtdemaxparcelas) " +
					"VALUES ('"+nome+"', "+codigo+", true, 12)", con);
			return Conexao.obterUltimoCodigoGeradoTabela(con, "operadoracartao");
		}else{
			executarConsulta("INSERT INTO banco (nome, codigobanco) " +
					"VALUES ('"+nome+"', "+codigo+")", con);
			return Conexao.obterUltimoCodigoGeradoTabela(con, "banco");
		}
	}
	

}

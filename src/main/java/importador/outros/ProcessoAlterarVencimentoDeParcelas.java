package importador.outros;

import br.com.pactosolucoes.atualizadb.processo.ExecutarProcessos;
import br.com.pactosolucoes.enumeradores.LogGenericoTipoEnum;
import br.com.pactosolucoes.enumeradores.OrigemSistemaEnum;
import negocio.comuns.utilitarias.Calendario;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisValidacao;
import negocio.facade.jdbc.arquitetura.Log;
import negocio.facade.jdbc.arquitetura.SuperFacadeJDBC;
import negocio.facade.jdbc.utilitarias.Conexao;
import org.json.JSONArray;
import org.json.JSONObject;

import java.io.File;
import java.io.IOException;
import java.sql.Connection;
import java.sql.DriverManager;
import java.sql.ResultSet;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;

public class ProcessoAlterarVencimentoDeParcelas {

    private static Map<String, StringBuilder> mapaLogGravar;
    private static String nomeBanco = "";
    // Indices colunas planilha

    public static void main(String[] args) throws IOException {
        Date d1 = Calendario.hoje();
        try {
            Connection con = DriverManager.getConnection("*******************************************", "postgres", "pactodb");
            Conexao.guardarConexaoForJ2SE(con);

            ExecutarProcessos executarProcessos = new ExecutarProcessos(con);
            executarProcessos.migracaoVersao2054();

            nomeBanco = con.getCatalog();

            boolean simular = true;
            Integer codigoPlano = 262;
            Integer mesesAdicionar = 1;
            Date dataLancamentoContrato = Calendario.getDate("dd/MM/yyyy", "26/12/2023");
            Date dataInicioContrato = Calendario.getDate("dd/MM/yyyy", "02/01/2024");
            OrigemSistemaEnum origemSistemaEnum = OrigemSistemaEnum.ZW;
            String contratosIgnorar = "58925,58946,58950,58962";

            processar(codigoPlano, mesesAdicionar, dataLancamentoContrato, dataInicioContrato, origemSistemaEnum, contratosIgnorar, simular, con);
        } catch (Exception ex) {
            ex.printStackTrace();
            adicionarLog("GERAL", ex.getMessage());
        } finally {
            Date d2 = Calendario.hoje();
            adicionarLog("GERAL", "=============================================");
            adicionarLog("GERAL", "Banco: " + nomeBanco);
            adicionarLog("GERAL", "Tempo Total: " + Calendario.diferencaEmSegundos(d1, d2) + " segundos");
            adicionarLog("GERAL", "=============================================");
            Uteis.salvarArquivo(ProcessoAlterarVencimentoDeParcelas.class.getSimpleName() + "_" + nomeBanco + "-" + Calendario.getData("yyyyMMddHHmmss") + ".txt",
                    getLogGravar("GERAL").toString(), "C:\\Processos\\" + File.separator);
        }
    }

    private static void processar(Integer codigoPlano, Integer mesesAdicionar, Date dataLancamentoContrato,
                                  Date dataInicioContrato, OrigemSistemaEnum origemSistemaEnum, String contratosIgnorar,
                                  boolean simular, Connection con) throws Exception {
        Log logDAO;
        try {
            logDAO = new Log(con);

            adicionarLog("GERAL", "=============================================");
            adicionarLog("GERAL", "================ Parâmetros =================");
            adicionarLog("GERAL", "=============================================");
            adicionarLog("GERAL", "Banco de dados: " + nomeBanco);
            adicionarLog("GERAL", "Plano: " + codigoPlano);
            adicionarLog("GERAL", "Meses adicionar as parcelas em aberto: " + mesesAdicionar + (mesesAdicionar > 1 ? " meses" : " mês"));
            adicionarLog("GERAL", "Contrato com data lançamento: " + Calendario.getDataAplicandoFormatacao(dataLancamentoContrato, "dd/MM/yyyy"));
            adicionarLog("GERAL", "Contrato com data de início: " + Calendario.getDataAplicandoFormatacao(dataInicioContrato, "dd/MM/yyyy"));
            adicionarLog("GERAL", "Contrato com origem: " + origemSistemaEnum.getDescricao());
            adicionarLog("GERAL", "Contratos ignorar: " + contratosIgnorar);
            adicionarLog("GERAL", "Simular: " + (simular ? "SIM" : "NÃO"));
            adicionarLog("GERAL", "=============================================");

            StringBuilder sql = new StringBuilder();
            sql.append("select \n");
            sql.append("cl.matricula, \n");
            sql.append("p.nome, \n");
            sql.append("c.codigo as contrato, \n");
            sql.append("c.pessoa, \n");
            sql.append("c.origemcontrato, \n");
            sql.append("c.origemsistema \n");
            sql.append("from contrato c \n");
            sql.append("inner join pessoa p on p.codigo = c.pessoa \n");
            sql.append("inner join cliente cl on cl.pessoa = c.pessoa  \n");
            sql.append("where c.plano = ").append(codigoPlano).append(" \n");
            sql.append("and c.datalancamento::date = '").append(Uteis.getDataFormatoBD(dataLancamentoContrato)).append("'  \n");
            sql.append("and c.vigenciade::date = '").append(Uteis.getDataFormatoBD(dataInicioContrato)).append("' \n");
            sql.append("and c.origemsistema = ").append(origemSistemaEnum.getCodigo()).append(" \n");
            if (!UteisValidacao.emptyString(contratosIgnorar)) {
                sql.append("and c.codigo not in (").append(contratosIgnorar).append(") \n");
            }
            sql.append("order by c.codigo \n");

            Integer total = SuperFacadeJDBC.contar("SELECT COUNT(*) FROM (" + sql + ") as qtd", con);
            adicionarLog("GERAL", "Total de contratos: " + total);

            ResultSet rs = SuperFacadeJDBC.criarConsulta(sql.toString(), con);

            int atual = 0;
            while (rs.next()) {
                try {
                    con.setAutoCommit(false);

                    String matricula = rs.getString("matricula");
                    String nome = rs.getString("nome");
                    Integer contrato = rs.getInt("contrato");
                    Integer pessoa = rs.getInt("pessoa");

                    JSONObject log = new JSONObject();
                    log.put("matricula", matricula);
                    log.put("nome", nome);
                    log.put("contrato", contrato);
                    log.put("pessoa", pessoa);

                    adicionarLog("GERAL", "=============================================");
                    adicionarLog("GERAL", "Processando: " + ++atual + "/" + total);
                    adicionarLog("GERAL", "Mat.: " + matricula + " | Nome: " + nome + " | Contrato: " + contrato);

                    JSONArray parcelas = ajustarParcelas(contrato, mesesAdicionar, simular, con);
                    log.put("parcelas", parcelas);

                    if (!simular) {
                        logDAO.incluirLogGenerico(LogGenericoTipoEnum.AJUSTE_VENCIMENTO_PARCELAS, null, log.toString());
                    }
                    con.commit();
                } catch (Exception ex) {
                    con.rollback();
                    ex.printStackTrace();
                } finally {
                    con.setAutoCommit(true);
                }
            }
        } finally {
            logDAO = null;
        }
    }

    private static JSONArray ajustarParcelas(Integer contrato, Integer mesesAdicionar,
                                             boolean simular, Connection con) throws Exception {
        JSONArray logParcelas = new JSONArray();
        StringBuilder sql = new StringBuilder();
        sql.append("select \n");
        sql.append("mp.codigo, \n");
        sql.append("mp.descricao, \n");
        sql.append("mp.datavencimento, \n");
        sql.append("(mp.datavencimento + interval '").append(mesesAdicionar).append(" month') as nova_data, \n");
        sql.append("mp.valorparcela \n");
        sql.append("from movparcela mp \n");
        sql.append("where mp.situacao = 'EA' \n");
        sql.append("and mp.contrato = ").append(contrato).append(" \n");
        sql.append("order by mp.datavencimento \n");

        ResultSet rs = SuperFacadeJDBC.criarConsulta(sql.toString(), con);
        while (rs.next()) {
            Integer parcela = rs.getInt("codigo");
            String descricao = rs.getString("descricao");
            Date dataVencimentoAnterior = rs.getTimestamp("datavencimento");
            Date dataVencimentoNova = rs.getTimestamp("nova_data");

            JSONObject log = new JSONObject();
            log.put("codigo", parcela);
            log.put("dataVencimentoAnterior", Calendario.getDataAplicandoFormatacao(dataVencimentoAnterior, "dd/MM/yyyy"));
            log.put("dataVencimentoNova", Calendario.getDataAplicandoFormatacao(dataVencimentoNova, "dd/MM/yyyy"));

            adicionarLog("GERAL", "Parcela: " + parcela + " | Descricao: " + descricao +
                    " | Vencimento Anterior: " + Calendario.getDataAplicandoFormatacao(dataVencimentoAnterior, "dd/MM/yyyy") +
                    " | Novo Vencimento: " + Calendario.getDataAplicandoFormatacao(dataVencimentoNova, "dd/MM/yyyy"));

            String update = "update movparcela set datavencimento = '" + Uteis.getDataFormatoBD(dataVencimentoNova) +
                    "', datacobranca = '" + Uteis.getDataFormatoBD(dataVencimentoNova) + "' where codigo = " + parcela;

            if (!simular) {
                SuperFacadeJDBC.executarUpdate(update, con);
            }

            logParcelas.put(log);
        }
        return logParcelas;
    }

    private static void adicionarLog(String keyMapa, String msg) {
        String s = "[DEBUG] " + Uteis.getDataAtualAplicandoFormatacao("dd/MM/yyyy hh:mm:ss") + " --> " + msg;
        System.out.println(s);
        StringBuilder logGravar = getLogGravar(keyMapa).append(s).append("\n");
        mapaLogGravar.put(keyMapa, logGravar);
    }

    private static StringBuilder getLogGravar(String keyMapa) {
        if (mapaLogGravar == null) {
            mapaLogGravar = new HashMap<>();
        }
        StringBuilder logGravar = mapaLogGravar.get(keyMapa);
        if (logGravar == null) {
            logGravar = new StringBuilder();
        }
        return logGravar;
    }
}

package importador.outros;

import br.com.pactosolucoes.integracao.pactopay.dto.EnderecoDTO;
import br.com.pactosolucoes.oamd.controle.basico.DAO;
import negocio.comuns.basico.EnderecoVO;
import negocio.comuns.basico.PessoaVO;
import negocio.comuns.basico.TelefoneVO;
import negocio.comuns.financeiro.MovParcelaVO;
import negocio.comuns.utilitarias.Uteis;
import negocio.facade.jdbc.basico.Pessoa;
import negocio.facade.jdbc.financeiro.MovParcela;
import negocio.facade.jdbc.utilitarias.Conexao;
import servicos.impl.microsservice.integracoes.MovParcelaCDLTO;
import servicos.impl.microsservice.integracoes.TelefoneCDLTO;

import java.io.BufferedReader;
import java.io.FileReader;
import java.io.IOException;
import java.math.BigDecimal;
import java.sql.Connection;
import java.text.DecimalFormat;
import java.text.NumberFormat;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Locale;

public class ProcessoAtualizarClienteSPCEngenharia {

    private final String key;
    private final String pathToCsvSPC;
    private final List<RegistroParcelaSPC> parcelasAvaliar;
    private Connection conZW;
    private MovParcela movParcelaDAO;
    private Pessoa pessoaDAO;

    public ProcessoAtualizarClienteSPCEngenharia(final String key, final String pathToCsvSPC) {
        this.key = key;
        this.pathToCsvSPC = pathToCsvSPC;
        parcelasAvaliar = new ArrayList<>();
    }

    public static void main(String[] args) {
        try {
            String keyParam = "1c81b0d9bb3b592b05ee1558e154137c";
            String pathToCsvSPC = "C:\\opt\\SPC-Importação\\ATIVOS SPC DESVIO RIZZO_2.csv";
            if (args.length > 0) {
                keyParam = args[0];
                if (args.length > 1) {
                    pathToCsvSPC = args[1];
                }
            }

            ProcessoAtualizarClienteSPCEngenharia processo = new ProcessoAtualizarClienteSPCEngenharia(keyParam, pathToCsvSPC);
            processo.inicializar();
            processo.processarLista();

        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    private void processarLista() {
        for (RegistroParcelaSPC registro : parcelasAvaliar) {
            try {
                List<MovParcelaVO> parcelasNegativar = movParcelaDAO.findByCPFAndVencimentoAndValor(
                        registro.getCpf(), registro.getDataVencimento(), registro.getValorDebito(),
                        Uteis.NIVELMONTARDADOS_DADOSBASICOS
                );

                MovParcelaVO parcelaNegativar;
                if (parcelasNegativar.size() == 1) {
                    parcelaNegativar = parcelasNegativar.get(0);
                } else if (parcelasNegativar.isEmpty()) {
                    System.out.println(
                            String.format("Parcela não encontrada. CPF: %s Vencimento: %s Valor: %s",
                                    registro.getCpf(), registro.getDataVencimento(), registro.getValorDebito())
                    );
                    continue;
                } else {
                    System.out.println(
                            String.format("Mais de 1 Parcela encontrada. CPF: %s Vencimento: %s Valor: %s",
                                    registro.getCpf(), registro.getDataVencimento(), registro.getValorDebito())
                    );
                    continue;
                }

                if (parcelaNegativar.isIncluidaSPC()) {
                    continue;
                }

                PessoaVO pessoaNegativada = parcelaNegativar.getPessoa();
                pessoaNegativada = pessoaDAO.consultarPorCodigo(pessoaNegativada.getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS);

                MovParcelaCDLTO movParcelaCDLTO = new MovParcelaCDLTO();
                movParcelaCDLTO.setDataRegistro(parcelaNegativar.getDataRegistro().getTime());
                movParcelaCDLTO.setDataVencimento(registro.getDataVencimento().getTime());
                movParcelaCDLTO.setCodigoContrato(registro.getContrato());
                movParcelaCDLTO.setValorParcela(registro.getValorDebito().doubleValue());
                movParcelaCDLTO.getPessoa().setNome(parcelaNegativar.getPessoa_Apresentar());
                movParcelaCDLTO.getPessoa().setCpf(parcelaNegativar.getPessoa().getCfp());

                if (!pessoaNegativada.getEnderecoVOs().isEmpty()) {
                    EnderecoVO endereco = pessoaNegativada.getEnderecoVOs().get(0);
                    EnderecoDTO enderecoDTO = new EnderecoDTO();
                    enderecoDTO.setEndereco(endereco.getEndereco());
                    enderecoDTO.setCep(endereco.getCep());
                    enderecoDTO.setNumero(endereco.getNumero());
                    enderecoDTO.setComplemento(endereco.getComplemento());
                    enderecoDTO.setBairro(endereco.getBairro());
                    enderecoDTO.setCidade(pessoaNegativada.getCidade_Apresentar());
                    enderecoDTO.setUf(pessoaNegativada.getEstadoVO().getSigla());
                    enderecoDTO.setPais(pessoaNegativada.getPais().getNome());
                    movParcelaCDLTO.getPessoa().setEndereco(enderecoDTO.toEnderecoCDLTO());
                }

                movParcelaCDLTO.getPessoa().setNumeroRg(parcelaNegativar.getPessoa().getRg());
                movParcelaCDLTO.getPessoa().setUfRg(parcelaNegativar.getPessoa().getRgUf());

                pessoaNegativada.getEmailVOs()
                        .stream()
                        .findFirst()
                        .ifPresent(email -> movParcelaCDLTO.getPessoa().setEmail(email.getEmail()));

                if (!pessoaNegativada.getTelefoneVOs().isEmpty()) {
                    TelefoneVO telefoneVO = pessoaNegativada.getTelefoneVOs().stream().findFirst().orElse(null);
                    if (telefoneVO != null) {
                        String telefone = telefoneVO.getNumero();
                        TelefoneCDLTO telefoneCDLTO = new TelefoneCDLTO();
                        telefone = telefone
                                .replace("(", "")
                                .replace(")", "");

                        String dddTelefone = telefone.substring(0, 2);
                        telefoneCDLTO.setDdd(dddTelefone);

                        String numeroTelefone = telefone.substring(2);
                        telefoneCDLTO.setNumero(numeroTelefone);

                        movParcelaCDLTO.getPessoa().setTelefone(telefoneCDLTO);
                    }
                }

                if (pessoaNegativada.getDataNasc() != null) {
                    movParcelaCDLTO.getPessoa().setDataNascimento(pessoaNegativada.getDataNasc().getTime());
                }

                movParcelaDAO.alterarSituacaoSPC(parcelaNegativar.getCodigo(), true, "Negativação importada");
                movParcelaDAO.salvarJSONEnvio(parcelaNegativar.getCodigo(), movParcelaCDLTO.toString());
                movParcelaDAO.alterarSituacaoSPCCliente(parcelaNegativar.getPessoa().getCodigo());
            } catch (Exception ex) {
                ex.printStackTrace();
            }
        }
    }

    private void inicializar() throws Exception {
        DAO dao = new DAO();
        conZW = dao.obterConexaoEspecifica(key);
        Conexao.guardarConexaoForJ2SE(conZW);

        movParcelaDAO = new MovParcela(conZW);
        pessoaDAO = new Pessoa(conZW);

        // Leitura do arquivo CSV
        try (BufferedReader br = new BufferedReader(new FileReader(pathToCsvSPC))) {
            String linha;
            boolean primeiraLinha = true;

            while ((linha = br.readLine()) != null) {
                if (primeiraLinha) {
                    primeiraLinha = false;
                    continue;
                }
                String[] colunas = linha.split(";");
                parcelasAvaliar.add(new RegistroParcelaSPC(colunas));
            }
        } catch (IOException e) {
            e.printStackTrace();
        }
    }

}


class RegistroParcelaSPC {

    private SimpleDateFormat sdf = new SimpleDateFormat("dd/MM/yyyy");
    private SimpleDateFormat sdfComHora = new SimpleDateFormat("dd/MM/yyyy HH:mm:ss");
    private String cpf;
    private String consumidor;
    private String contrato;
    private Date dataVencimento;
    private BigDecimal valorDebito;
    private Date dataInclusao;

    public RegistroParcelaSPC(String[] colunas) {
        this.cpf = colunas[0];
        this.consumidor = colunas[1];
        String cpfSemMascara = Uteis.removerMascara(cpf);
        this.contrato = colunas[2];
        if (cpfSemMascara.contains(contrato)) {
            contrato = cpfSemMascara;
        }
        String dataVencimentoStr = colunas[3];
        try {
            this.dataVencimento = sdf.parse(dataVencimentoStr);
        } catch (Exception ex) {
            ex.printStackTrace();
        }

        DecimalFormat decimalFormat = (DecimalFormat) NumberFormat.getInstance(Locale.getDefault());
        decimalFormat.setParseBigDecimal(true);

        try {
            this.valorDebito = (BigDecimal) decimalFormat.parse(colunas[4]);
        } catch (ParseException e) {
            e.printStackTrace();
            return;
        }

        String horaInclusaoStr = colunas[5] + " " + colunas[6];
        try {
            this.dataInclusao = sdfComHora.parse(horaInclusaoStr);
        } catch (Exception ex) {
            ex.printStackTrace();
        }
    }

    public String getCpf() {
        return cpf;
    }

    public void setCpf(String cpf) {
        this.cpf = cpf;
    }

    public String getConsumidor() {
        return consumidor;
    }

    public void setConsumidor(String consumidor) {
        this.consumidor = consumidor;
    }

    public String getContrato() {
        return contrato;
    }

    public void setContrato(String contrato) {
        this.contrato = contrato;
    }

    public Date getDataVencimento() {
        return dataVencimento;
    }

    public void setDataVencimento(Date dataVencimento) {
        this.dataVencimento = dataVencimento;
    }

    public BigDecimal getValorDebito() {
        return valorDebito;
    }

    public void setValorDebito(BigDecimal valorDebito) {
        this.valorDebito = valorDebito;
    }

    public Date getDataInclusao() {
        return dataInclusao;
    }

    public void setDataInclusao(Date dataInclusao) {
        this.dataInclusao = dataInclusao;
    }
}

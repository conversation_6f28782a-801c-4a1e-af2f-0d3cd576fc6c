package importador.outros;

import negocio.comuns.acesso.AutorizacaoAcessoGrupoEmpresarialVO;
import negocio.comuns.acesso.IntegracaoAcessoGrupoEmpresarialVO;
import negocio.comuns.utilitarias.UteisValidacao;
import org.json.JSONObject;
import servicos.impl.microsservice.acessosistema.AcessoSistemaMSException;
import servicos.util.ExecuteRequestHttpService;

import java.sql.Connection;
import java.sql.DriverManager;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class ProcessoAtualizarCodigosAutorizacaoAcessoEngenharia {

    private static HashMap<String, String> mapChaveNomeBD = new HashMap<>();
    private Connection connection;

    public ProcessoAtualizarCodigosAutorizacaoAcessoEngenharia(Connection connection) {
        this.connection = connection;
    }

    public static void main(String[] args) {
        try {
            prepararMapaChaves();

            String urlFranqueadora = "********************************/bdzillyonvalidacaoengenhariadocorpo";
            try (Connection conFranqueadora = DriverManager.getConnection(urlFranqueadora, "postgres", "pactodb")) {
                ProcessoAtualizarCodigosAutorizacaoAcessoEngenharia processo = new ProcessoAtualizarCodigosAutorizacaoAcessoEngenharia(conFranqueadora);
                List<IntegracaoAcessoGrupoEmpresarialVO> integracoes = processo.consultarIntegracoes();

                for (IntegracaoAcessoGrupoEmpresarialVO integracao : integracoes) {
                    if (integracao.getChave().equals("628519d73cf98a91c81fa2492e0a9189")) {
                        System.out.println("Não avaliar integração " + integracao.getDescricao());
                        continue;
                    }
                    Map<String, AutorizacaoAcessoGrupoEmpresarialVO> autorizacoesPorIntegracao = processo.consultarAutorizacoes(integracao);
                    System.out.println("Encontradas " + autorizacoesPorIntegracao.size() + " autorizações da integração: " + integracao.getDescricao());

                    Map<String, AutorizacaoAcessoGrupoEmpresarialVO> clientesOriginais = processo.consultarClientes(integracao);
                    System.out.println("Encontrados " + clientesOriginais.size() + " clientes na origem da integração: " + integracao.getDescricao());

                    int qtdNaoEncontrada = 0;
                    int qtdAtualizarDados = 0;
                    int qtdAtualizarBiometrias = 0;
                    int qtdOK = 0;

                    for (Map.Entry<String, AutorizacaoAcessoGrupoEmpresarialVO> autorizacaoCadastrada : autorizacoesPorIntegracao.entrySet()) {
                        String cpf = autorizacaoCadastrada.getKey();
                        AutorizacaoAcessoGrupoEmpresarialVO autorizacaoGateway = autorizacaoCadastrada.getValue();
                        AutorizacaoAcessoGrupoEmpresarialVO autorizacaoOrigem = clientesOriginais.get(cpf);

                        if (autorizacaoOrigem == null) {
                            qtdNaoEncontrada++;
                            continue;
                        }

                        if (necessitaAtualizarDados(autorizacaoGateway, autorizacaoOrigem)) {
                            qtdAtualizarDados++;
                            processo.atualizarDados(autorizacaoGateway, autorizacaoOrigem);
                        }

                        if (necessitaAtualizarBiometrias(autorizacaoGateway, autorizacaoOrigem)) {
                            qtdAtualizarBiometrias++;
                            processo.atualizarBiometrias(autorizacaoGateway, autorizacaoOrigem);
                            continue;
                        }

                        qtdOK++;
                    }

                    System.out.println("Não encontrados: " + qtdNaoEncontrada);
                    System.out.println("Necessitou atualizar dados: " + qtdAtualizarDados);
                    System.out.println("Necessitou atualizar biometrias: " + qtdAtualizarBiometrias);
                    System.out.println("Fim... " + integracao.getDescricao());
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    private void atualizarBiometrias(AutorizacaoAcessoGrupoEmpresarialVO autorizacaoGateway, AutorizacaoAcessoGrupoEmpresarialVO autorizacaoOrigem) throws Exception {
        String update = "UPDATE autorizacaoacessogrupoempresarial SET assinaturabiometriadigital = ? WHERE codigo = ?;";
        try (PreparedStatement ps = connection.prepareStatement(update)) {
            int i = 0;
            ps.setString(++i, autorizacaoOrigem.getAssinaturaBiometriaDigital());
            ps.setInt(++i, autorizacaoGateway.getCodigo());
            ps.execute();
        }

        String retorno = post("https://zw301.pactosolucoes.com.br/acesso-sistema-ms/autorizacoesAcesso/" + autorizacaoGateway.getCodigoAutorizacao() + "/force-sync?url=https://bio.ath.cx&tipo=biometria");
        System.out.println(retorno);
    }

    private static String post(String url) throws Exception {
        Map<String, String> headers = new HashMap<>();
        headers.put("Authorization", "Bearer e82cdafdf149e399e18ae3eb096bd873");
        String response;
        try {
            response = ExecuteRequestHttpService.post(url, null, headers);
        } catch (Exception e) {
            response = e.getMessage();
        }
        return response;
    }

    private static boolean necessitaAtualizarDados(AutorizacaoAcessoGrupoEmpresarialVO autorizacaoGateway, AutorizacaoAcessoGrupoEmpresarialVO autorizacaoOrigem) {
        return !autorizacaoGateway.getNomePessoa().equals(autorizacaoOrigem.getNomePessoa()) ||
                !autorizacaoGateway.getCodigoPessoa().equals(autorizacaoOrigem.getCodigoPessoa()) ||
                !autorizacaoGateway.getCodAcesso().equals(autorizacaoOrigem.getCodAcesso()) ||
                !autorizacaoGateway.getCodigoMatricula().equals(autorizacaoOrigem.getCodigoMatricula()) ||
                !autorizacaoGateway.getCodigoGenerico().equals(autorizacaoOrigem.getCodigoGenerico());
    }

    private static boolean necessitaAtualizarBiometrias(AutorizacaoAcessoGrupoEmpresarialVO autorizacaoGateway, AutorizacaoAcessoGrupoEmpresarialVO autorizacaoOrigem) {
        return !UteisValidacao.emptyString(autorizacaoOrigem.getAssinaturaBiometriaDigital())
                && !UteisValidacao.emptyString(autorizacaoGateway.getAssinaturaBiometriaDigital())
                && !autorizacaoGateway.getAssinaturaBiometriaDigital().equals(autorizacaoOrigem.getAssinaturaBiometriaDigital());
    }

    private static void prepararMapaChaves() throws SQLException {
        String urlOamd = "********************************/OAMD";
        try (Connection con = DriverManager.getConnection(urlOamd, "postgres", "pactodb")) {
            String sqlEmpresa = "SELECT chave, \"nomeBD\" FROM empresa";

            mapChaveNomeBD = new HashMap<>();
            try (PreparedStatement ps = con.prepareStatement(sqlEmpresa)) {
                try (ResultSet rs = ps.executeQuery()) {
                    while (rs.next()) {
                        String chave = rs.getString("chave");
                        String nomeBD = rs.getString("nomeBD");
                        mapChaveNomeBD.put(chave, nomeBD);
                    }
                }
            }
        }
    }

    private void atualizarDados(AutorizacaoAcessoGrupoEmpresarialVO autorizacaoGateway, AutorizacaoAcessoGrupoEmpresarialVO autorizacaoOrigem) throws SQLException {
        String update = "UPDATE autorizacaoacessogrupoempresarial SET nomePessoa = ?, codigoPessoa = ?, codAcesso = ?, codacessoalternativo = ?, codigomatricula = ?, codigoGenerico = ?  WHERE codigo = ?";
        try (PreparedStatement ps = connection.prepareStatement(update)) {
            int i = 0;
            ps.setString(++i, autorizacaoOrigem.getNomePessoa());
            ps.setInt(++i, autorizacaoOrigem.getCodigoPessoa());
            ps.setString(++i, autorizacaoOrigem.getCodAcesso());
            ps.setString(++i, autorizacaoGateway.getCodAcesso());
            ps.setInt(++i, autorizacaoOrigem.getCodigoMatricula());
            ps.setInt(++i, autorizacaoOrigem.getCodigoGenerico());
            ps.setInt(++i, autorizacaoGateway.getCodigo());
            ps.execute();
        }
    }

    private List<IntegracaoAcessoGrupoEmpresarialVO> consultarIntegracoes() throws Exception {
        List<IntegracaoAcessoGrupoEmpresarialVO> integracoesAcesso = new ArrayList<>();

        String sql = "select codigo, descricao, chave from integracaoacessogrupoempresarial i order by 1";
        try (PreparedStatement ps = connection.prepareStatement(sql)) {
            try (ResultSet rs = ps.executeQuery()) {
                while (rs.next()) {
                    IntegracaoAcessoGrupoEmpresarialVO integracao = new IntegracaoAcessoGrupoEmpresarialVO();
                    integracao.setCodigo(rs.getInt("codigo"));
                    integracao.setDescricao(rs.getString("descricao"));
                    integracao.setChave(rs.getString("chave"));
                    integracoesAcesso.add(integracao);
                }
            }
        }
        return integracoesAcesso;
    }

    private Map<String, AutorizacaoAcessoGrupoEmpresarialVO> consultarAutorizacoes(IntegracaoAcessoGrupoEmpresarialVO integracao) throws Exception {
        Map<String, AutorizacaoAcessoGrupoEmpresarialVO> autorizacoes = new HashMap<>();

        String sql = "select\n" +
                "    codigo,\n" +
                "    nomepessoa,\n" +
                "    codigopessoa,\n" +
                "    codacesso,\n" +
                "    codigomatricula,\n" +
                "    codigogenerico,\n" +
                "    assinaturabiometriadigital,\n" +
                "    cpf,\n" +
                "    codigoautorizacao\n" +
                "from\n" +
                "    autorizacaoacessogrupoempresarial a\n" +
                "where\n" +
                "    integracaoacessogrupoempresarial = " + integracao.getCodigo() + "\n" +
                "    and tipopessoa = 'CL';";
        try (PreparedStatement ps = connection.prepareStatement(sql)) {
            try (ResultSet rs = ps.executeQuery()) {
                while (rs.next()) {
                    AutorizacaoAcessoGrupoEmpresarialVO aut = new AutorizacaoAcessoGrupoEmpresarialVO();
                    aut.setCodigo(rs.getInt("codigo"));
                    aut.setNomePessoa(rs.getString("nomepessoa"));
                    aut.setCodigoPessoa(rs.getInt("codigopessoa"));
                    aut.setCodAcesso(rs.getString("codacesso"));
                    aut.setCodigoMatricula(rs.getInt("codigomatricula"));
                    aut.setCodigoGenerico(rs.getInt("codigogenerico"));
                    aut.setAssinaturaBiometriaDigital(rs.getString("assinaturabiometriadigital"));
                    aut.setCpf(rs.getString("cpf"));
                    aut.setCodigoAutorizacao(rs.getString("codigoautorizacao"));
                    autorizacoes.put(aut.getCpf(), aut);
                }
            }
        }
        return autorizacoes;
    }

    private Map<String, AutorizacaoAcessoGrupoEmpresarialVO> consultarClientes(IntegracaoAcessoGrupoEmpresarialVO integracao) throws Exception {
        Map<String, AutorizacaoAcessoGrupoEmpresarialVO> clientes = new HashMap<>();

        String urlBanco = "********************************/" + mapChaveNomeBD.get(integracao.getChave());
        try (Connection con = DriverManager.getConnection(urlBanco, "postgres", "pactodb")) {

            String sql = "select\n" +
                    "    p.nome,\n" +
                    "    p.codigo as codPessoa,\n" +
                    "    cli.codacesso,\n" +
                    "    cli.codigomatricula,\n" +
                    "    cli.codigo as codCliente,\n" +
                    "    p.assinaturadigitalbiometria,\n" +
                    "    p.cfp as cpf\n" +
                    "from\n" +
                    "    cliente cli\n" +
                    "inner join pessoa p on\n" +
                    "    cli.pessoa = p.codigo\n" +
                    "where\n" +
                    "    coalesce(p.cfp, '') <> ''\n" +
                    "    and sincronizadoredeempresa is not null";
            try (PreparedStatement ps = con.prepareStatement(sql)) {
                try (ResultSet rs = ps.executeQuery()) {
                    while (rs.next()) {
                        AutorizacaoAcessoGrupoEmpresarialVO cliente = new AutorizacaoAcessoGrupoEmpresarialVO();
                        cliente.setNomePessoa(rs.getString("nome"));
                        cliente.setCodigoPessoa(rs.getInt("codPessoa"));
                        cliente.setCodAcesso("NU" + rs.getString("codacesso"));
                        cliente.setCodigoMatricula(rs.getInt("codigomatricula"));
                        cliente.setCodigoGenerico(rs.getInt("codCliente"));
                        cliente.setAssinaturaBiometriaDigital(rs.getString("assinaturadigitalbiometria"));
                        cliente.setCpf(rs.getString("cpf"));
                        clientes.put(cliente.getCpf(), cliente);
                    }
                }
            }
            return clientes;
        }
    }

}


package importador.outros;

import java.util.Date;

public class ReajusteSescTO {

    private Integer plano;
    private Integer contrato;
    private Double valorAcrescentar;
    private Date inicioVencimentoParcelas;
    private Date dataLancamentoNovaParcela;
    private Date dataLancamentoParcelaCancelada;

    public ReajusteSescTO(){

    }
    public ReajusteSescTO(Integer plano, Double valorAcrescentar,
                          Date inicioVencimentoParcelas, Date dataLancamentoNovaParcela,
                          Date dataLancamentoParcelaCancelada) {
        this.plano = plano;
        this.valorAcrescentar = valorAcrescentar;
        this.inicioVencimentoParcelas = inicioVencimentoParcelas;
        this.dataLancamentoNovaParcela = dataLancamentoNovaParcela;
        this.dataLancamentoParcelaCancelada = dataLancamentoParcelaCancelada;
    }

    public Integer getPlano() {
        return plano;
    }

    public void setPlano(Integer plano) {
        this.plano = plano;
    }

    public Double getValorAcrescentar() {
        return valorAcrescentar;
    }

    public void setValorAcrescentar(Double valorAcrescentar) {
        this.valorAcrescentar = valorAcrescentar;
    }

    public Date getInicioVencimentoParcelas() {
        return inicioVencimentoParcelas;
    }

    public void setInicioVencimentoParcelas(Date inicioVencimentoParcelas) {
        this.inicioVencimentoParcelas = inicioVencimentoParcelas;
    }

    public Date getDataLancamentoNovaParcela() {
        return dataLancamentoNovaParcela;
    }

    public void setDataLancamentoNovaParcela(Date dataLancamentoNovaParcela) {
        this.dataLancamentoNovaParcela = dataLancamentoNovaParcela;
    }

    public Integer getContrato() {
        return contrato;
    }

    public void setContrato(Integer contrato) {
        this.contrato = contrato;
    }

    public Date getDataLancamentoParcelaCancelada() {
        return dataLancamentoParcelaCancelada;
    }

    public void setDataLancamentoParcelaCancelada(Date dataLancamentoParcelaCancelada) {
        this.dataLancamentoParcelaCancelada = dataLancamentoParcelaCancelada;
    }
}

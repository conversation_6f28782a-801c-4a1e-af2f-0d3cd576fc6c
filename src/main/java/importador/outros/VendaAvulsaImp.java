package importador.outros;

import java.io.Console;
import java.sql.Connection;
import java.sql.DriverManager;
import java.sql.ResultSet;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

import importador.LeitorExcel;
import negocio.comuns.arquitetura.UsuarioVO;
import negocio.comuns.basico.ClienteVO;
import negocio.comuns.financeiro.ItemVendaAvulsaVO;
import negocio.comuns.financeiro.VendaAvulsaVO;
import negocio.comuns.utilitarias.Calendario;
import negocio.comuns.utilitarias.UteisValidacao;
import negocio.facade.jdbc.financeiro.VendaAvulsa;

import org.apache.poi.hssf.usermodel.HSSFRow;

import br.com.pactosolucoes.estudio.dao.AgendaEstudio;

public class VendaAvulsaImp{
	ImportacaoDAO importacaoDAO;
	VendaAvulsa vendaAvulsa;
	AgendaEstudio agenda;
	List<VendaAvulsaVO> vendas = new ArrayList<VendaAvulsaVO>();
	Connection con;
	public VendaAvulsaImp(Connection con) throws Exception{
		this.con = con;
		importacaoDAO = new ImportacaoDAO(con);
		vendaAvulsa = new VendaAvulsa(con);
		agenda = new AgendaEstudio(con); 
	}
	public void importarVendas(){
		try {
			Console console = System.console();
			UsuarioVO responsavel = new UsuarioVO();
			responsavel.setCodigo(2);
			List<HSSFRow> linhas = LeitorExcel.lerLinhas("C:\\Dhyana.xls");
			int i = 0;
			for( HSSFRow linha : linhas){
				try{
					VendaAvulsaVO vendaAvulsaVO = montarVenda(linha, responsavel);
					
					vendaAvulsa.incluir(vendaAvulsaVO, true,null, null,null);
					
					for(Object obj : vendaAvulsaVO.getItemVendaAvulsaVOs()){
						ItemVendaAvulsaVO item = (ItemVendaAvulsaVO) obj;
						agenda.salvarAgendaAgendar(item, 
								vendaAvulsaVO.getCliente().getCodigo(), 
								vendaAvulsaVO.getEmpresa().getCodigo(),null,null,false);
					}
					console.printf("\n"+i++);	
				}catch (Exception e) {
					e.printStackTrace();
					}
				
			}
		} catch (Exception e) {
			// TODO Auto-generated catch block
			e.printStackTrace();
		}
	}
	
	private VendaAvulsaVO montarVenda(HSSFRow row, UsuarioVO responsavel) throws Exception{
		VendaAvulsaVO venda = new VendaAvulsaVO();
		venda.setTipoComprador("CI");
		Integer id_pessoa = LeitorExcel.obterNumero(row,5).intValue();
		ClienteVO clienteVO = importacaoDAO.consultarClientePorIdExterno(id_pessoa);
		if(UteisValidacao.emptyNumber(clienteVO.getCodigo())){
			throw new Exception("Cliente não encontrado.");
		}
		venda.setCliente(clienteVO);
		Date data = LeitorExcel.obterData(row, 6);
		venda.setDataRegistro(data == null ? Calendario.hoje() : data);
		
		venda.setNomeComprador(clienteVO.getPessoa().getNome());
		venda.setEmpresa(clienteVO.getEmpresa());
		
		venda.setResponsavel(responsavel);
		
		Double desconto = LeitorExcel.obterNumero(row,4).doubleValue();
		Integer qtdRestante = LeitorExcel.obterNumero(row,2).intValue();
		Integer qtd = LeitorExcel.obterNumero(row,1).intValue();
		
		Double descontoPorUni = desconto / qtd;
		
		Double valorUnitario = LeitorExcel.obterNumero(row,0).doubleValue();
					
		ItemVendaAvulsaVO item = new ItemVendaAvulsaVO();
	    item.setProduto(importacaoDAO.consultarProdutoPorNome(LeitorExcel.obterString(row, 7), valorUnitario));
		item.setQuantidade(qtdRestante);
		item.setValorParcial((valorUnitario-descontoPorUni)*qtdRestante);
		if(desconto > 0){
			item.setDescontoManual(true);
			item.setResponsavelAutorizacaoDesconto(responsavel);
			item.setValorDescontoManual(descontoPorUni*qtdRestante);
		}
		
		venda.getItemVendaAvulsaVOs().add(item);
		return venda;
	}
	
	
	public static void main(String[] args) {
		VendaAvulsaImp imp;
		try {
			Connection con1 = DriverManager.getConnection(args[0], "postgres", "pactodb");
			imp = new VendaAvulsaImp(con1);
		    imp.processarAgenda();
		} catch (Exception e) {
			// TODO Auto-generated catch block
			e.printStackTrace();
		}
		
	}
	
	public void processarAgenda() throws Exception{
		Console console = System.console();
		ResultSet cont = con.prepareStatement("SELECT COUNT(codigo) FROM VENDAAVULSA").executeQuery();
		cont.next(); int total = cont.getInt(1);
		ResultSet criarConsulta = con.prepareStatement("SELECT codigo, cliente, empresa FROM VENDAAVULSA").executeQuery();
		int proc = 1;
		while(criarConsulta.next()){
			console.printf("\n"+proc+++"/"+total);
			Integer codigo = criarConsulta.getInt(1);
			StringBuilder sql = new StringBuilder();
			sql.append("select i.produto, i.quantidade "); 
			sql.append("from itemvendaavulsa i inner join produto p on p.codigo = i.produto ");
			sql.append("where p.tipoproduto like 'SS' and i.vendaavulsa = "+codigo);
			ResultSet resultSet = con.prepareStatement(sql.toString()).executeQuery();
			while(resultSet.next()){
				ResultSet contar = con.prepareStatement("select count(*) from sch_estudio.agenda_agendar where id_vendaavulsa = "+codigo+" and id_produto = "+resultSet.getInt("produto")).executeQuery();
				contar.next();
				int nr = contar.getInt(1);
				for(int i = 0; i < (resultSet.getInt("quantidade")-nr); i++){
					con.prepareStatement("INSERT INTO sch_estudio.agenda_agendar" 
							+"( id_vendaavulsa, id_produto, id_cliente, id_empresa ) VALUES ("+codigo+", "
							+resultSet.getInt("produto")+", "+criarConsulta.getInt(2)+", "+criarConsulta.getInt(3)+") ").execute();
				}
				
			}
			
		}
		
		
	}
}

package importador.outros;

import importador.LeitorExcel2010;
import negocio.comuns.financeiro.PlanoContaTO;
import negocio.comuns.financeiro.enumerador.TipoES;
import negocio.comuns.financeiro.enumerador.TipoEquivalenciaDRE;
import negocio.comuns.utilitarias.Calendario;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisValidacao;
import negocio.facade.jdbc.arquitetura.SuperFacadeJDBC;
import org.apache.poi.xssf.usermodel.XSSFRow;

import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;
import java.sql.Connection;
import java.sql.DriverManager;
import java.sql.ResultSet;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

public class ImportadorFinanceiroPatrimonialEngenharia {

    private static StringBuilder logGravar;
    private static String nomeBanco = "";

    public static void main(String[] args) throws IOException {
        try {
//            Connection con = DriverManager.getConnection("*********************************************************************___", "postgres", "pactodb");

            Connection con = DriverManager.getConnection("********************************************************", "postgres", "pactodb");
            nomeBanco = con.getCatalog();

            String arquivo = "C:\\Engenharia_Backup\\patrimonial\\Conta Patrimonial Engenharia.xlsx";

            adicionarLog("Parâmetros: ");
            adicionarLog("Arquivo: " + arquivo);
            importarPlanoContas(arquivo, con);
        } catch (Exception ex) {
            ex.printStackTrace();
            adicionarLog(ex.getMessage());
        } finally {
            Uteis.salvarArquivo(ImportadorFinanceiroPatrimonialEngenharia.class.getSimpleName() + "_" + nomeBanco + "-" + Calendario.getData("yyyyMMddHHmmss") + ".txt", getLogGravar().toString(), "C:\\Processos\\" + File.separator);
        }
    }

    private static void atualizarBanco(Connection con) {
        Set<String> atualiza = new HashSet<>();
        atualiza.add("ALTER TABLE movconta ADD COLUMN importado BOOLEAN DEFAULT FALSE;");
        atualiza.add("ALTER TABLE movconta ADD COLUMN idimportacao character varying;");
        atualiza.add("ALTER TABLE movconta ADD COLUMN dataimportacao timestamp without time zone;");
        atualiza.add("ALTER TABLE planoconta ADD COLUMN nomeconsulta text;");
        atualiza.add("ALTER TABLE planoconta ADD COLUMN importado BOOLEAN DEFAULT FALSE;");
        atualiza.add("ALTER TABLE fornecedor ADD COLUMN importado BOOLEAN DEFAULT FALSE;");
        atualiza.add("ALTER TABLE pessoa ALTER COLUMN nome type character varying(120);");
        atualiza.add("ALTER TABLE pessoa ALTER COLUMN nomeconsulta type character varying(120);");
        atualiza.add("UPDATE planoconta SET nomeconsulta = remove_acento_upper(nome);");
        for (String update : atualiza) {
            try {
                SuperFacadeJDBC.executarConsulta(update, con);
            } catch (Exception ignored) {
            }
        }
    }

    private static PlanoContaTO consultarPlanoDeContas(String codigoNome, TipoES tipoES,
                                                       TipoEquivalenciaDRE tipoEquivalenciaDRE,
                                                       Connection con, boolean exception) throws Exception {
        try {
            if (!codigoNome.trim().contains(" ")) {
                throw new Exception("Plano De Conta incorreto: " + codigoNome);
            }
            String[] cod = codigoNome.split(" ");
            String codigoPlano = cod[0].trim();
            if (!UteisValidacao.somenteNumerosEPontos(codigoPlano)) {
                throw new Exception("Plano De Conta incorreto: " + codigoNome);
            }
            codigoPlano = ajustarCodigoPlano(codigoPlano);
            String nome = codigoNome.replaceFirst(cod[0], "").trim();
            return ImportarFinanceiroUteis.consultarPlanoConta(codigoPlano, nome, tipoES, tipoEquivalenciaDRE, con, true);
        } catch (Exception ex) {
            if (exception) {
                throw ex;
            } else {
                return null;
            }
        }
    }

    private static String ajustarCodigoPlano(String codigo) {
        String ret = "";
        for (String cod : codigo.split("\\.")) {
            String novo = new String(cod);
            int add = (3 - cod.length());
            int atu = 0;
            while (atu < add) {
                novo = ("0" + novo);
                atu++;
            }
            ret += ("." + novo);
        }
        return ret.replaceFirst("\\.", "");
    }

    private static void importarPlanoContas(String arquivo, Connection con) throws Exception {
        atualizarBanco(con);

        Integer sucesso = 0;
        Integer falha = 0;
        Integer atual = 0;
        File arquivoFile = new File(arquivo);
        List<XSSFRow> hssfRows = LeitorExcel2010.lerLinhas(new FileInputStream(arquivo));

        for (XSSFRow linha : hssfRows) {
            try {
                adicionarLog("Importando... " + atual++ + "/" + hssfRows.size());


                TipoES tipoES = TipoES.SAIDA;

                String colunaPaiPrincipal = LeitorExcel2010.obterString(linha, 2);

                String codPaiPrincipal = obterCodigo(colunaPaiPrincipal, null, 1, con);
                String nomePaiPrincipal = (codPaiPrincipal + " " + colunaPaiPrincipal);
                consultarPlanoDeContas(nomePaiPrincipal, tipoES, null, con, true);


                String coluna2 = LeitorExcel2010.obterString(linha, 1);
                String codigoColuna2 = obterCodigo(coluna2, codPaiPrincipal + ".", 2, con);
                String nomeColuna2 = (codigoColuna2 + " " + coluna2);
                consultarPlanoDeContas(nomeColuna2, tipoES, null, con, false);


                String coluna3 = LeitorExcel2010.obterString(linha, 0);
                if (!UteisValidacao.emptyString(coluna3)) {
                    String codigoColuna3 = obterCodigo(coluna3, codigoColuna2 + ".", 3, con);
                    String nomeColuna3 = (codigoColuna3 + " " + coluna3);
                    consultarPlanoDeContas(nomeColuna3, tipoES, null, con, false);
                }

                sucesso++;
            } catch (Exception ex) {
                ex.printStackTrace();
                falha++;
                adicionarLog("ERRO | ARQUIVO " + arquivoFile.getName() + " | LINHA " + atual + " | " + ex.getMessage());
            }
        }

        adicionarLog("#######################");
        adicionarLog("#######################");
        adicionarLog("Sucesso: " + sucesso);
        adicionarLog("Falha: " + falha);
        adicionarLog("#######################");
        adicionarLog("#######################");
        adicionarLog("Arquivo: " + arquivo);
        adicionarLog("Linhas: " + hssfRows.size());
        adicionarLog("#######################");
        adicionarLog("#######################");

    }

    private static void adicionarLog(String msg) {
        String s = "[DEBUG] " + Calendario.hoje() + " --> " + msg;
        System.out.println(s);
        getLogGravar().append(s).append("\n");
    }

    private static StringBuilder getLogGravar() {
        if (logGravar == null) {
            logGravar = new StringBuilder();
        }
        return logGravar;
    }

//    private static String obterCodigoPrincipal(String nome, Connection con) throws Exception {
//        ResultSet rs = SuperFacadeJDBC.criarConsulta("select * from planoconta where nome ilike '" + nome + "' ", con);
//        if (rs.next()) {
//            return rs.getString("codigoplanocontas");
//        }
//        return obterProximo("", con);
//    }

    private static String obterCodigo(String nome, String codigoPai, Integer nivel, Connection con) throws Exception {
        StringBuilder sql = new StringBuilder();
        sql.append("select \n");
        sql.append("* \n");
        sql.append("from planoconta \n");
        sql.append("where nome ilike '").append(nome).append("' \n");
        if (!UteisValidacao.emptyString(codigoPai)) {
            sql.append(" and codigoplanocontas ilike '").append(codigoPai).append("%' \n");
        }
        ResultSet rs = SuperFacadeJDBC.criarConsulta(sql.toString(), con);
        if (rs.next()) {
            return rs.getString("codigoplanocontas");
        }
        return obterProximo(nivel, codigoPai, con);
    }

    private static String obterProximo(Integer nivel, String codigoBase, Connection con) throws Exception {
        StringBuilder sql = new StringBuilder();
        sql.append("select \n");
        sql.append("max(split_part(split_part(codigoplanocontas, '.', ").append(nivel).append("), '.',1)::integer) + 1 as proximo \n");
        sql.append("from planoconta \n");
        if (!UteisValidacao.emptyString(codigoBase)) {
            sql.append(" where codigoplanocontas ilike '").append(codigoBase).append("%' \n");
        }
        ResultSet rs = SuperFacadeJDBC.criarConsulta(sql.toString(), con);
        if (rs.next()) {
            Integer codigo = rs.getInt("proximo");
            if (UteisValidacao.emptyNumber(codigo)) {
                codigo = 1;
            }
            if (!UteisValidacao.emptyString(codigoBase)) {
                return (codigoBase + ajustarCodigoPlano(codigo.toString()));
            }
            return ajustarCodigoPlano(codigo.toString());
        }
        throw new Exception("Erro obter codigo plano");
    }
}




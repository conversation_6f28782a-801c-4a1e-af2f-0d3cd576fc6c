package importador.outros;

import java.util.Calendar;
import java.util.Date;
import java.util.GregorianCalendar;

/**
 * Created by alcides on 30/08/2016.
 */
public class SuperZW {

    public Integer codigo;

    public boolean validarStringNaoNulaOuZero(String textoValidar) {
        return textoValidar != null && !textoValidar.equals("NULL") && !textoValidar.trim().isEmpty() && !textoValidar.equals("0");
    }

    public java.sql.Date getDataJDBC(java.util.Date dataConverter) throws Exception {
        if (dataConverter == null) {
            return null;
        }
        return new java.sql.Date(dataConverter.getTime());
    }

    public java.sql.Timestamp getTimestampJDBC(java.util.Date dataConverter) throws Exception {
        if (dataConverter == null) {
            return null;
        }
        return new java.sql.Timestamp(dataConverter.getTime());
    }

    public static Date somarDias(Date data, int dias) {
        Calendar cal = new GregorianCalendar();
        cal.setTime(data);

        cal.add(Calendar.DAY_OF_MONTH, dias);

        return cal.getTime();
    }
}


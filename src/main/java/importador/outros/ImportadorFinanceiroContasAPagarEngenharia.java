package importador.outros;

import br.com.pactosolucoes.comuns.util.FileUtilities;
import br.com.pactosolucoes.comuns.util.Formatador;
import br.com.pactosolucoes.enumeradores.TipoOperacaoLancamento;
import importador.LeitorExcel2010;
import negocio.comuns.arquitetura.UsuarioVO;
import negocio.comuns.basico.EmpresaVO;
import negocio.comuns.basico.FornecedorVO;
import negocio.comuns.basico.enumerador.TimeZoneEnum;
import negocio.comuns.financeiro.ContaVO;
import negocio.comuns.financeiro.MovContaRateioVO;
import negocio.comuns.financeiro.MovContaVO;
import negocio.comuns.financeiro.PlanoContaTO;
import negocio.comuns.financeiro.TipoContaVO;
import negocio.comuns.financeiro.TipoDocumentoVO;
import negocio.comuns.financeiro.enumerador.TipoES;
import negocio.comuns.financeiro.enumerador.TipoEquivalenciaDRE;
import negocio.comuns.utilitarias.Calendario;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisValidacao;
import negocio.facade.jdbc.arquitetura.SuperFacadeJDBC;
import negocio.facade.jdbc.arquitetura.Usuario;
import negocio.facade.jdbc.basico.Empresa;
import negocio.facade.jdbc.financeiro.Conta;
import negocio.facade.jdbc.financeiro.MovConta;
import negocio.facade.jdbc.financeiro.TipoConta;
import negocio.facade.jdbc.financeiro.TipoDocumento;
import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.xssf.usermodel.XSSFRow;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;

import java.io.File;
import java.io.FileInputStream;
import java.io.FileOutputStream;
import java.io.IOException;
import java.sql.Connection;
import java.sql.DriverManager;
import java.sql.ResultSet;
import java.sql.Statement;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

public class ImportadorFinanceiroContasAPagarEngenharia {

    private static StringBuilder logGravar;
    private static String nomeBanco = "";
    // Indices colunas planilha
    private static int fornecedorIndex, descricaoIndex, dtLancamentoIndex, dtVencimentoIndex, dtCompetenciaIndex, dtQuitacaoIndex, valorIndex, valorBaixaIndex, notaIndex, statusContaIndex, planoDeContaIndex, centroDeCustoIndex, contaIndex, tipoContaPadrao, fornecedorCNPJIndex, revisarIndex;
    private static boolean importarContaPlanoContas, importarConta = true;
    private static List<ContaVO> contas = new ArrayList<>();

    public static void main(String[] args) throws IOException {
        try {
            Connection con = DriverManager.getConnection("*************************************************************************", "postgres", "pactodb");
            nomeBanco = con.getCatalog();

            Integer empresa = 1;
            // caminho - Informar um diretorio que contém as planilhas ".xlsx" a serem importadas,
            // ou o caminho completo de alguma planilha. Ex: "C:\\contas_importar" ou "C:\\contas_importar\\contas_pagar.xlsx"
            String caminho = "C:\\Pacto\\Assis.xlsx";
            boolean contaQuitada = true;


            TipoOperacaoLancamento tipoOperacaoLancamento =TipoOperacaoLancamento.PAGAMENTO;
            TipoES tipoES = TipoES.SAIDA;
            TipoEquivalenciaDRE tipoEquivalenciaDRE = TipoEquivalenciaDRE.CUSTOS_ESPECIFICOS;

            //importar conta plano de contas
            importarContaPlanoContas = true;

            //importar para para determinada conta
            importarConta = true;
            //codigo do tipo de conta padrão para importar a conta
            tipoContaPadrao = 2;

            // Colunas planilha - (Necessário conferir os indices nas planilhas)
            fornecedorIndex = 0;
            descricaoIndex = 1;
            centroDeCustoIndex = 2;
            dtLancamentoIndex = 3;
            dtCompetenciaIndex = 4;
            dtVencimentoIndex = 5;
            dtQuitacaoIndex = 6;
            valorIndex = 7;
            valorBaixaIndex = 8;
            notaIndex = 9;
            statusContaIndex = 10;
            planoDeContaIndex = 13;
            fornecedorCNPJIndex = 14;
            contaIndex = 15;
            revisarIndex = 16;

            if (!caminho.contains(".xls")) {
                List<Map<String, File>> arquivos = FileUtilities.readListFilesDirectory(caminho);
                for (Map<String, File> map : arquivos) {
                    for (String arquivo : map.keySet()) {
                        File file = map.get(arquivo);
                        adicionarLog("Parâmetros: ");
                        adicionarLog("Empresa: " + empresa);
                        adicionarLog("Arquivo: " + file.getAbsolutePath());
                        if (validarDadosArquivo(caminho)) {
                            importarContas(arquivo, empresa, contaQuitada, tipoES, tipoEquivalenciaDRE, tipoOperacaoLancamento, con);
                        }
                    }
                }

            } else {
                adicionarLog("Parâmetros: ");
                adicionarLog("Empresa: " + empresa);
                adicionarLog("Arquivo: " + caminho);
                if (validarDadosArquivo(caminho)) {
                    importarContas(caminho, empresa, contaQuitada, tipoES, tipoEquivalenciaDRE, tipoOperacaoLancamento, con);
                }
            }
        } catch (Exception ex) {
            ex.printStackTrace();
            adicionarLog(ex.getMessage());
        } finally {
            Uteis.salvarArquivo(ImportadorFinanceiroContasAPagarEngenharia.class.getSimpleName() + "_" + nomeBanco + "-" + Calendario.getData("yyyyMMddHHmmss") + ".txt", getLogGravar().toString(), "C:\\Processos\\" + File.separator);
        }
    }

    private static boolean validarDadosArquivo(String caminhoDoArquivoPlanosContasExcluir) {
        try (FileInputStream arquivo = new FileInputStream(caminhoDoArquivoPlanosContasExcluir); Workbook workbook = new XSSFWorkbook(arquivo)) {
            Sheet planilha = workbook.getSheetAt(0);
            int count = 0;
            int linhasRevisar = 0;
            for (Row linha : planilha) {
                adicionarLog(" - Validando dados da linha: " + (++count) + "\\" + planilha.getLastRowNum());
                if (count == 1) {
                    Cell cellResults = linha.getCell(revisarIndex);
                    if (cellResults == null) {
                        cellResults = linha.createCell(revisarIndex);
                        cellResults.setCellValue("Revisar");
                    }
                    continue;
                }
                List<String> results = new ArrayList<>();
                try {
                    results.add(validarCampoString("Fornecedor", LeitorExcel2010.obterString((XSSFRow) linha, fornecedorIndex), 120));
                    results.add(validarCampoString("Descrição", LeitorExcel2010.obterString((XSSFRow) linha, descricaoIndex), 200));
                    results.add(validarCampoData("Data Lançamento", LeitorExcel2010.obterString((XSSFRow) linha, dtLancamentoIndex), "dd/MM/yyyy", "^(0[1-9]|[12][0-9]|3[01])/(0[1-9]|1[0-2])/\\d{4}$"));
                    String test1DtCompetencia = validarCampoData("Data Competência", LeitorExcel2010.obterString((XSSFRow) linha, dtCompetenciaIndex), "MM/yyyy", "^(0[1-9]|1[0-2])/\\d{4}$");
                    String test2DtCompetencia = validarCampoData("Data Competência", LeitorExcel2010.obterString((XSSFRow) linha, dtCompetenciaIndex), "MM/yyyy", "^(0[1-9]|[12][0-9]|3[01])/(0[1-9]|1[0-2])/\\d{4}$");
                    if (!test1DtCompetencia.equals("ok") && !test2DtCompetencia.equals("ok")) {
                        results.add(test1DtCompetencia);
                    }results.add(validarCampoData("Data Vencimento", LeitorExcel2010.obterString((XSSFRow) linha, dtVencimentoIndex), "dd/MM/yyyy", "^(0[1-9]|[12][0-9]|3[01])/(0[1-9]|1[0-2])/\\d{4}$"));
                    String status = LeitorExcel2010.obterString((XSSFRow) linha, statusContaIndex).toLowerCase();
                    results.add(validarCampoString("Status", status, 100));
                    if (!status.equals("paga") && !status.equals("em aberto") && !status.equals("vencida")) {
                        results.add(String.format("Status inválido: %s. Dever ser paga, em aberto ou vencida", status));
                    }
                    results.add(validarCampoString("Status", status, 100));

                    if (status.toLowerCase().equals("paga")) {
                        results.add(validarCampoData("Data Pagamento\\Quitação", LeitorExcel2010.obterString((XSSFRow) linha, dtQuitacaoIndex), "dd/MM/yyyy", "^(0[1-9]|[12][0-9]|3[01])/(0[1-9]|1[0-2])/\\d{4}$"));
                    }
                    try {
                        LeitorExcel2010.obterNumero((XSSFRow) linha, valorIndex);
                    } catch (Exception ex) {
                        results.add(String.format("Falha ao converter valor da conta: (%s)", ex.getMessage()));
                    }
                    String planoConta = LeitorExcel2010.obterString((XSSFRow) linha, planoDeContaIndex);
                    results.add(validarCampoString("Plano Conta", planoConta, 100));
                    String codigoPlanoConta = planoConta.split(" ")[0];
                    if (planoConta.split(" ").length <= 1 || (codigoPlanoConta.length() > 3 && !codigoPlanoConta.contains("."))) {
                        results.add(String.format("Plano de contas no formato incorreto \"%s\" deve estar no padrão: \"001.001 Nome Item)\"", planoConta));
                    }
                } catch (Exception ex) {
                    results.add("Falha ao tentar validar dados: " + ex.getMessage());
                }
                Cell cellResults = linha.getCell(revisarIndex);
                if (cellResults == null) {
                    cellResults = linha.createCell(revisarIndex);
                }
                String result = results.stream().filter(r -> !r.equals("ok")).collect(Collectors.joining("; "));

                if (!UteisValidacao.emptyString(result)) {
                    adicionarLog("\t" + result);
                    cellResults.setCellValue(result);
                    linhasRevisar++;
                }
            }
            if (linhasRevisar > 0) {
                adicionarLog("Total de linhas revisar: " + linhasRevisar);
                String arquivoRevisar = caminhoDoArquivoPlanosContasExcluir.replace(".xls", "_REVISAR_" + Calendario.getData("yyyyMMdd_HHmmss") + ".xls");
                adicionarLog("Gravando resultados na planilha: " + arquivoRevisar);
                try { Thread.sleep(2000);} catch (Exception e) {}
                // Salve as alterações no arquivo
                try {
                    FileOutputStream outputStream = new FileOutputStream(arquivoRevisar);
                    workbook.write(outputStream);
                } catch (Exception ex) {}
            } else {
                adicionarLog("Dados OK");
                return true;
            }
        } catch (IOException e) {
            e.printStackTrace();
        }
        return false;
    }


    private static String validarCampoString(String nomeCampo, String value, int tamanhoMaximo) {
        if (UteisValidacao.emptyString(value)) {
            return String.format("Campo %s vazio", nomeCampo);
        }
        if (value.length() > tamanhoMaximo) {
            return String.format("Campo %s utrapassou limite de %d caracteres", nomeCampo, tamanhoMaximo);
        }
        return "ok";
    }
    public static String validarCampoData(String nomeCampo, String dataStr, String padrao, String regexPadraoExperado) {
        if (UteisValidacao.emptyString(dataStr)) {
            return String.format("Campo %s vazio", nomeCampo);
        }
        if (!dataStr.matches(regexPadraoExperado)) {
            return String.format("O campo: %s \"%s\" deve estar no padrão: %s", nomeCampo, dataStr, padrao);
        }
        try {
            Uteis.getDate(dataStr, padrao);
        } catch (Exception ex) {
            return String.format("Falha ao tentar converter data do campo: %s \"%s\" - erro: %s ", nomeCampo, dataStr, ex.getMessage());
        }
        return "ok";
    }

    private static void atualizarBanco(Connection con) {
        Set<String> atualiza = new HashSet<>();
        atualiza.add("ALTER TABLE movconta ADD COLUMN importado BOOLEAN DEFAULT FALSE;");
        atualiza.add("ALTER TABLE movconta ADD COLUMN idimportacao character varying;");
        atualiza.add("ALTER TABLE movconta ADD COLUMN dataimportacao timestamp without time zone;");
        atualiza.add("ALTER TABLE planoconta ADD COLUMN nomeconsulta text;");
        atualiza.add("ALTER TABLE planoconta ADD COLUMN importado BOOLEAN DEFAULT FALSE;");
        atualiza.add("ALTER TABLE fornecedor ADD COLUMN importado BOOLEAN DEFAULT FALSE;");
        atualiza.add("ALTER TABLE pessoa ALTER COLUMN nome type character varying(120);");
        atualiza.add("ALTER TABLE pessoa ALTER COLUMN nomeconsulta type character varying(120);");
        atualiza.add("UPDATE planoconta SET nomeconsulta = remove_acento_upper(nome);");
        for (String update : atualiza) {
            try {
                SuperFacadeJDBC.executarConsulta(update, con);
            } catch (Exception ignored) {
            }
        }
    }

    private static void importarContas(String arquivo, Integer empresa, boolean quitada,
                                       TipoES tipoES, TipoEquivalenciaDRE tipoEquivalenciaDRE, TipoOperacaoLancamento tipoOperacaoLancamento,
                                       Connection con) throws Exception {
        Empresa empresaDAO;
        Usuario usuarioDAO;
        MovConta movContaDAO;
        TipoDocumento tipoDocumentoDAO;
        Conta contaDAO;
        TipoConta tipoContaDAO;
        try {
            atualizarBanco(con);

            empresaDAO = new Empresa(con);
            usuarioDAO = new Usuario(con);
            movContaDAO = new MovConta(con);
            tipoDocumentoDAO = new TipoDocumento(con);
            contaDAO = new Conta(con);
            tipoContaDAO = new TipoConta(con);

            UsuarioVO usuarioPactobrVO = usuarioDAO.consultarPorUsername("PACTOBR", Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA);
            EmpresaVO empresaVO = empresaDAO.consultarPorChavePrimaria(empresa, Uteis.NIVELMONTARDADOS_GESTAOREMESSA);
            List<TipoDocumentoVO> listaTipoDocumento = tipoDocumentoDAO.consultarTodas(Uteis.NIVELMONTARDADOS_DADOSBASICOS);
            TipoDocumentoVO tipoDocumentoVONotaFiscal = obterTipoDocumento("nota fiscal", listaTipoDocumento);
            TipoContaVO tipoContaVO = null;
            if (importarConta) {
                contas = contaDAO.consultarContasSimples(empresaVO.getCodigo(), true);
                tipoContaVO = tipoContaDAO.consultarPorChavePrimaria(tipoContaPadrao, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
            }

//            if (importarContaPlanoContas) {
//                importarPlanoContas(arquivo, tipoES, tipoEquivalenciaDRE, con);
//            }

            File arquivoFile = new File(arquivo);
            List<XSSFRow> hssfRows = LeitorExcel2010.lerLinhas(new FileInputStream(arquivo));
            int atual = 0;
            Integer sucesso = 0;
            Integer falha = 0;
            Integer ignoradoDescricao = 0;
            for (XSSFRow linha : hssfRows) {
                String descricao = "";
                try {
                    adicionarLog((++atual) + "/" + hssfRows.size() + " - Importando conta - Planilha: \"" + arquivoFile.getName() + "\"");

                    descricao = LeitorExcel2010.obterString(linha, descricaoIndex);

                    if (UteisValidacao.emptyString(descricao)) {
                        adicionarLog("Descrição não informada");
                        ignoradoDescricao++;
                        continue;
                    }

                    String nota = LeitorExcel2010.obterString(linha, notaIndex);
                    String status = LeitorExcel2010.obterString(linha, statusContaIndex);
                    String centroCusto = LeitorExcel2010.obterString(linha, centroDeCustoIndex);

                    FornecedorVO fornecedorVO = ImportarFinanceiroUteis.consultarFornecedor(LeitorExcel2010.obterString(linha, fornecedorIndex), empresa, LeitorExcel2010.obterString(linha, fornecedorCNPJIndex), con);

                    MovContaVO movContaVO = new MovContaVO();
                    movContaVO.setEmpresaVO(empresaVO);
                    movContaVO.setPessoaVO(fornecedorVO.getPessoa());
                    movContaVO.setDescricao(descricao);
                    movContaVO.setUsarHoraAtual(false);

                    String dataLancamento = LeitorExcel2010.obterString(linha, dtLancamentoIndex);
                    String dataCompetencia = LeitorExcel2010.obterString(linha, dtCompetenciaIndex);
                    String dataVencimento = LeitorExcel2010.obterString(linha, dtVencimentoIndex);

                    if (UteisValidacao.emptyString(dataLancamento)) {
                        adicionarLog("Lançamento | " + descricao + " | dataLancamento não informada será usado a dataVencimento");
                        dataLancamento = dataVencimento;
                    }
                    if (UteisValidacao.emptyString(dataCompetencia)) {
                        adicionarLog("Lançamento | " + descricao + " | dataCompetencia não informada será usado a dataVencimento");
                        dataCompetencia = dataVencimento;
                    }

                    if (UteisValidacao.emptyString(dataLancamento)) {
                        throw new Exception("dataLancamento não informada");
                    }
                    if (UteisValidacao.emptyString(dataCompetencia)) {
                        throw new Exception("dataCompetencia não informada");
                    }
                    if (UteisValidacao.emptyString(dataVencimento)) {
                        throw new Exception("dataVencimento não informada");
                    }

                    movContaVO.setDataLancamento(Uteis.getDate(dataLancamento));
                    movContaVO.setDataCompetencia(Uteis.getDate(dataCompetencia, "MM/yyyy"));
                    movContaVO.setDataVencimento(Uteis.getDate(dataVencimento));

                    if (!UteisValidacao.emptyString(nota)) {
                        movContaVO.setNumeroDocumento(nota);
                    }

                    Double valor = LeitorExcel2010.obterNumero(linha, valorIndex).doubleValue();
                    valor = (valor < 0.0 ? valor * -1 : valor);
                    movContaVO.setValor(valor);
                    movContaVO.setValorOriginalAlterado(valor);

                    Double valorBaixa = 0.0;

                    if (status.equalsIgnoreCase("paga")) {
                        valorBaixa = LeitorExcel2010.obterNumero(linha, valorBaixaIndex).doubleValue();
                        valorBaixa = (valorBaixa < 0.0 ? valorBaixa * -1 : valorBaixa);

                        Date dataBaixa = Uteis.getDate(LeitorExcel2010.obterString(linha, dtQuitacaoIndex));
                        movContaVO.setDataQuitacao(dataBaixa);
                        if (Uteis.arredondarForcando2CasasDecimais(valorBaixa) >
                                Uteis.arredondarForcando2CasasDecimais(valor)) {
                            adicionarLog(descricao + " | Valor Baixa Superior ao valor do lançamento");
                        }
                        if (UteisValidacao.emptyNumber(movContaVO.getValor())) {
                            adicionarLog(descricao + " | Usar valor baixa");
                            movContaVO.setValor(valorBaixa);
                        }
                        movContaVO.setValorPago(valorBaixa);
                    }

                    //importar conta
                    String conta = "";
                    if (importarConta) {
                        conta = LeitorExcel2010.obterString(linha, contaIndex);
                        ContaVO contaVO = obterContaVO(conta, empresaVO, tipoContaVO, contaDAO);
                        if (contaVO != null) {
                            movContaVO.setContaVO(contaVO);
                        }
                    }

                    MovContaRateioVO movContaRateioVO = new MovContaRateioVO();
                    movContaRateioVO.setDescricao(movContaVO.getDescricao());
                    movContaRateioVO.setValor(movContaVO.getValor());
                    movContaRateioVO.setTipoES(tipoES);


                    if(movContaVO.getDataQuitacao() != null){
                        movContaRateioVO.getFormaPagamentoVO().setCodigo(11); //A Vista
                    }

                    String planoContas = "";
                    if (importarContaPlanoContas) {
                        planoContas = LeitorExcel2010.obterString(linha, planoDeContaIndex);
                        PlanoContaTO planoContaTO1 = consultarPlanoDeContas(planoContas, tipoES, tipoEquivalenciaDRE, con, false);
                        if (planoContaTO1 == null || UteisValidacao.emptyNumber(planoContaTO1.getCodigo())) {
                            planoContaTO1 = new PlanoContaTO();
                            adicionarLog("\t Conta: \"" + descricao + "\" | Plano de contas não identificado: \"" + planoContas + "\"");
                        }
                        consultarPlanoDeContas(LeitorExcel2010.obterString(linha, 12), tipoES, tipoEquivalenciaDRE, con, false);
                        consultarPlanoDeContas(LeitorExcel2010.obterString(linha, 13), tipoES, tipoEquivalenciaDRE, con, false);
                        consultarPlanoDeContas(LeitorExcel2010.obterString(linha, 14), tipoES, tipoEquivalenciaDRE, con, false);
                        consultarPlanoDeContas(LeitorExcel2010.obterString(linha, 15), tipoES, tipoEquivalenciaDRE, con, false);
                        movContaRateioVO.setPlanoContaVO(planoContaTO1);
                    }

                    if (!UteisValidacao.emptyString(nota)) {
                        movContaRateioVO.setTipoDocumentoVO(tipoDocumentoVONotaFiscal);
                    }

                    try {
                        movContaVO.setIdImportacao((quitada ? "QUITADA|" : "NAO_QUITADA|") + arquivoFile.getName() + "|LINHA_" + atual);
                    } catch (Exception ex) {
                        adicionarLog(descricao + " | Erro gerar idImportacao ignorado...");
                    }

                    String obs = ("VALOR: " + Formatador.formatarValorMonetario(valor));
                    obs += (UteisValidacao.emptyNumber(valorBaixa) ? "" : ("\nVALOR BAIXA: " + Formatador.formatarValorMonetario(valorBaixa)));
                    obs += (UteisValidacao.emptyString(planoContas) ? "" : ("\nPLANO CONTA: " + planoContas));
                    obs += ("\nCENTRO CUSTO: " + centroCusto);
                    obs += (UteisValidacao.emptyString(nota) ? "" : ("\nNOTA: " + nota));
                    obs += (UteisValidacao.emptyString(conta) ? "" : ("\nCONTA: " + conta));
                    obs += "\n\nARQ: " + arquivoFile.getName();
                    movContaVO.setObservacoes(obs);

                    movContaVO.setMovContaRateios(new ArrayList<>());
                    movContaVO.getMovContaRateios().add(movContaRateioVO);
                    movContaVO.setUsuarioVO(usuarioPactobrVO);
                    movContaVO.setTipoOperacaoLancamento(tipoOperacaoLancamento);

                    if (existeImportada(movContaVO, con)) {
                        throw new Exception("Conta já importada!");
                    }

                    movContaDAO.incluirSemCommit(movContaVO, 0, false, null);
                    SuperFacadeJDBC.executarConsulta("UPDATE movconta SET idimportacao = '" + movContaVO.getIdImportacaoSalvar() + "', importado = TRUE, " +
                            "dataimportacao = '" + Calendario.getDataAplicandoFormatacao(Calendario.hoje(TimeZoneEnum.Brazil_GTM_3.getId()), "yyyy-MM-dd HH:mm:ss") + "' WHERE codigo = " + movContaVO.getCodigo(), con);
                    sucesso++;
                } catch (Exception ex) {
                    ex.printStackTrace();
                    falha++;
                    adicionarLog("ERRO | ARQUIVO " + arquivoFile.getName() + " | LINHA " + atual + " | " + descricao + " | " + ex.getMessage());
                }
            }

            adicionarLog("#######################");
            adicionarLog("#######################");
            adicionarLog("Sucesso: " + sucesso);
            adicionarLog("Falha: " + falha);
            adicionarLog("#######################");
            adicionarLog("Ignorado Descrição vazia: " + ignoradoDescricao);
            adicionarLog("#######################");
            adicionarLog("Arquivo: " + arquivo);
            adicionarLog("Linhas: " + hssfRows.size());
            adicionarLog("#######################");
            adicionarLog("#######################");
        } finally {
            empresaDAO = null;
            usuarioDAO = null;
            movContaDAO = null;
            tipoDocumentoDAO = null;
            contaDAO = null;
            tipoContaDAO = null;
        }
    }

    private static PlanoContaTO consultarPlanoDeContas(String codigoNome, TipoES tipoES,
                                                       TipoEquivalenciaDRE tipoEquivalenciaDRE,
                                                       Connection con, boolean exception) throws Exception {
        try {
            if (!codigoNome.trim().contains(" ")) {
                throw new Exception("Plano De Conta incorreto: " + codigoNome);
            }
            String[] cod = codigoNome.split(" ");
            String codigoPlano = cod[0].trim();
            if (!UteisValidacao.somenteNumerosEPontos(codigoPlano)) {
                throw new Exception("Plano De Conta incorreto: " + codigoNome);
            }
            codigoPlano = ajustarCodigoPlano(codigoPlano);
            String nome = codigoNome.replaceFirst(cod[0], "").trim();
            return ImportarFinanceiroUteis.consultarPlanoConta(codigoPlano, nome, tipoES, tipoEquivalenciaDRE, con, true);
        } catch (Exception ex) {
            if (exception) {
                throw ex;
            } else {
                return null;
            }
        }
    }

    private static String ajustarCodigoPlano(String codigo) {
        String ret = "";
        for (String cod : codigo.split("\\.")) {
            String novo = new String(cod);
            int add = (3 - cod.length());
            int atu = 0;
            while (atu < add) {
                novo = ("0" + novo);
                atu++;
            }
            ret += ("." + novo);
        }
        return ret.replaceFirst("\\.", "");
    }

    private static TipoDocumentoVO obterTipoDocumento(String descricao, List<TipoDocumentoVO> lista) {
        for (TipoDocumentoVO obj : lista) {
            if (obj.getDescricao().equalsIgnoreCase(descricao)) {
                return obj;
            }
        }
        return null;
    }

    private static void importarPlanoContas(String arquivo, TipoES tipoES,
                                            TipoEquivalenciaDRE tipoEquivalenciaDRE,
                                            Connection con) {
        try {
            List<XSSFRow> hssfRows = LeitorExcel2010.lerLinhas(new FileInputStream(arquivo), 0);
            for (XSSFRow linha : hssfRows) {
                int coluna = 0;
                while (coluna < 5) {
                    try {
                        consultarPlanoDeContas(LeitorExcel2010.obterString(linha, coluna++), tipoES, tipoEquivalenciaDRE, con, false);
                    } catch (Exception ex) {
                        ex.printStackTrace();
                    }
                }
            }
        } catch (Exception ex) {
            ex.printStackTrace();
        }
    }

    private static boolean existeImportada(MovContaVO movContaVO, Connection con) throws Exception {
        String sql = "SELECT EXISTS(select codigo from movconta where idimportacao ilike '" + movContaVO.getIdImportacaoSalvar() + "') as existe";
        ResultSet rs = SuperFacadeJDBC.criarConsulta(sql, con);
        if (rs.next()) {
            return rs.getBoolean("existe");
        }
        return false;
    }

    private static void preencherIdImportacao(Connection con) throws Exception {
        String sql = "select * from movconta where importado and coalesce(idimportacao,'') = ''";
        Statement st = con.createStatement();
        ResultSet rs = st.executeQuery(sql);
        MovConta movContaDAO = new MovConta(con);
        List<MovContaVO> lista = movContaDAO.montarDadosConsulta(rs, Uteis.NIVELMONTARDADOS_LOTE_AVULSO, con);
        movContaDAO = null;
        int atual = 0;
        for (MovContaVO movContaVO : lista) {
            SuperFacadeJDBC.executarConsulta("UPDATE movconta SET idimportacao = '" + movContaVO.getIdImportacaoSalvar() + "' WHERE codigo = " + movContaVO.getCodigo(), con);
            System.out.println(atual++ + "/" + lista.size() + " - MovConta: " + movContaVO.getCodigo() + " | IdImportacao | " + movContaVO.getIdImportacaoSalvar());
        }
    }

    private static void adicionarLog(String msg) {
        String s = "[DEBUG] " + Uteis.getDataAtualAplicandoFormatacao("dd/MM/yyyy hh:mm:ss") + " --> " + msg;
        System.out.println(s);
        getLogGravar().append(s).append("\n");
    }

    private static StringBuilder getLogGravar() {
        if (logGravar == null) {
            logGravar = new StringBuilder();
        }
        return logGravar;
    }

    private static ContaVO obterContaVO(String conta, EmpresaVO empresaVO,
                                        TipoContaVO tipoContaVO, Conta contaDAO) throws Exception {
        if (!UteisValidacao.emptyString(conta) &&
                !UteisValidacao.emptyString(conta.trim())) {
            //exatamente igual
            for (ContaVO contaVO : contas) {
                if (contaVO.getDescricao().trim().equalsIgnoreCase(conta.trim())) {
                    return contaVO;
                }
            }

            //inicia igual
            for (ContaVO contaVO : contas) {
                if (contaVO.getDescricao().trim().toUpperCase().startsWith(conta.trim().toUpperCase())) {
                    return contaVO;
                }
            }

            //não encontrou vou cadastrar uma nova
            ContaVO contaVO = new ContaVO();
            contaVO.setDescricao(conta.toUpperCase());
            contaVO.setEmpresa(empresaVO);
            contaVO.setTipoConta(tipoContaVO);
            contaDAO.incluir(contaVO);
            contas.add(contaVO);
            return contaVO;
        }
        return null;
    }
}




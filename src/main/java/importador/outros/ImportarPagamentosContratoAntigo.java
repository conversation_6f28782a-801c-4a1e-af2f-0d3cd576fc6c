package importador.outros;

import br.com.pactosolucoes.integracao.importacao.PagamentoJSON;
import importador.json.ClienteImportacaoJSON;
import negocio.comuns.basico.ClienteVO;
import negocio.comuns.basico.EmpresaVO;
import negocio.comuns.contrato.ContratoVO;
import negocio.comuns.financeiro.MovPagamentoVO;
import negocio.comuns.financeiro.MovParcelaVO;
import negocio.comuns.financeiro.ReciboPagamentoVO;
import negocio.comuns.utilitarias.Uteis;
import negocio.facade.jdbc.arquitetura.SuperFacadeJDBC;
import negocio.facade.jdbc.basico.webservice.IntegracaoCadastros;
import negocio.facade.jdbc.basico.webservice.IntegracaoImportacao;
import negocio.facade.jdbc.contrato.Contrato;
import negocio.facade.jdbc.financeiro.MovPagamento;
import negocio.facade.jdbc.financeiro.MovParcela;
import negocio.facade.jdbc.financeiro.ReciboPagamento;
import org.json.JSONObject;

import java.sql.Connection;
import java.sql.ResultSet;
import java.util.*;

public class ImportarPagamentosContratoAntigo extends IntegracaoCadastros {

    public ImportarPagamentosContratoAntigo(Connection con) throws Exception {
        super(con);
    }

    public JSONObject persistirPagamentosImportacao(ClienteImportacaoJSON clienteImportacaoJSON, ClienteVO clienteVO, EmpresaVO empresaVO) throws Exception {
        List<MovPagamentoVO> listaPagamento = new ArrayList<MovPagamentoVO>();
        List<MovParcelaVO> parcelasPagar = new ArrayList<MovParcelaVO>();
        IntegracaoImportacao integracaoImportacao = new IntegracaoImportacao(con);
        ReciboPagamento reciboDAO = new ReciboPagamento(con);
        MovPagamento movpagamentoDAO = new MovPagamento(con);
        JSONObject objRetorno = new JSONObject();
        try {
            ResultSet rsExistente = SuperFacadeJDBC.criarConsulta("select recibopagamento from movpagamento where pessoa = " + clienteVO.getPessoa().getCodigo() + " and empresa = " + empresaVO.getCodigo(), con);
            if (rsExistente.next()) {
                objRetorno.put("status", "warning");
                objRetorno.put("codigoRegistroZW", rsExistente.getInt("recibopagamento"));
                objRetorno.put("mensagem", "Já foi importado pagamento para o contrato do cliente: " + clienteVO.getPessoa().getNome());
                return objRetorno;
            }

//            List<PagamentoJSON> listaPagamentoJSON = beanImportacao.getLstPagamentoJson();
//            integracaoImportacao.validarDadosBasicosPagamentos(listaPagamentoJSON);

            ContratoVO contratoVO = new ContratoVO();
            ResultSet rsContrato = SuperFacadeJDBC.criarConsulta("select * from contrato where pessoa = " + clienteVO.getPessoa().getCodigo() + " and empresa = " + empresaVO.getCodigo(), con);
            if (rsContrato.next()) {
                contratoVO = Contrato.montarDados(rsContrato, Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA, con);
                System.out.println(contratoVO);
            } else {
                objRetorno.put("status", "erro");
                objRetorno.put("codigoRegistroZW", 0);
                objRetorno.put("mensagem", "Não foi encontrado contrato");
                return objRetorno;
            }

            Map<Date, List<PagamentoJSON>> mapRecibos = new HashMap<Date, List<PagamentoJSON>>();
            mapRecibos.put(Uteis.getDataComHoraZerada(contratoVO.getDataLancamento()), new ArrayList<PagamentoJSON>());

            ResultSet rsParcela = SuperFacadeJDBC.criarConsulta("select * from movparcela where situacao = 'EA' and contrato = " + contratoVO.getCodigo(), con);
            while (rsParcela.next()) {
                MovParcelaVO movParcelaVO = MovParcela.montarDados(rsParcela, Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA, con);
                ReciboPagamentoVO recibo = new ReciboPagamentoVO();
                recibo.setContrato(contratoVO);
                recibo.setData(contratoVO.getDataLancamento());
                recibo.setNomePessoaPagador(clienteVO.getPessoa().getNome());
                recibo.setPessoaPagador(contratoVO.getPessoa());
                recibo.setResponsavelLancamento(contratoVO.getResponsavelContrato());
                recibo.setValorTotal(movParcelaVO.getValorParcela());
                recibo.setEmpresa(contratoVO.getEmpresa());

                movpagamentoDAO.inicializarDadosReciboPagamento(listaPagamento, recibo, contratoVO);
                reciboDAO.incluir(recibo);

                MovPagamentoVO movPagamento = new MovPagamentoVO();
                movPagamento.setPessoa(contratoVO.getPessoa());
                movPagamento.setDataLancamento(contratoVO.getDataLancamento());
                movPagamento.setDataPagamento(contratoVO.getDataLancamento());
                movPagamento.setDataQuitacao(contratoVO.getDataLancamento());
                movPagamento.setResponsavelPagamento(contratoVO.getResponsavelContrato());
                movPagamento.setValor(movParcelaVO.getValorParcela());
                movPagamento.setValorTotal(movParcelaVO.getValorParcela());
                movPagamento.setEmpresa(empresaVO);
                //movPagamento.setCredito(Boolean.TRUE);
                movPagamento.getFormaPagamento().setCodigo(3);
                movPagamento.getFormaPagamento().setTipoFormaPagamento("AV");
                movPagamento.setNomePagador(clienteVO.getPessoa().getNome());
                movPagamento.setMovPagamentoEscolhida(true);
                movPagamento.setReciboPagamento(recibo);
                listaPagamento.add(movPagamento);
                parcelasPagar.add(movParcelaVO);

                movpagamentoDAO.incluirListaPagamento(listaPagamento, parcelasPagar, null,
                        contratoVO, false, 0.0, false, recibo);
                listaPagamento.clear();
                parcelasPagar.clear();
            }

            movpagamentoDAO = null;
            reciboDAO = null;
            //objRetorno.put("status", "success");
            return objRetorno;
        } catch (Exception e) {
            throw e;
        }
    }

}

package importador.outros;

import br.com.pactosolucoes.enumeradores.TipoOperacaoLancamento;
import importador.LeitorExcel2010;
import importador.outros.ImportarFornecedorAntigo;
import negocio.comuns.arquitetura.UsuarioVO;
import negocio.comuns.basico.EmpresaVO;
import negocio.comuns.basico.FornecedorVO;
import negocio.comuns.financeiro.CaixaVO;
import negocio.comuns.financeiro.CentroCustoTO;
import negocio.comuns.financeiro.ContaVO;
import negocio.comuns.financeiro.MovContaRateioVO;
import negocio.comuns.financeiro.MovContaVO;
import negocio.comuns.financeiro.PlanoContaTO;
import negocio.comuns.financeiro.enumerador.TipoES;
import negocio.comuns.utilitarias.Calendario;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisValidacao;
import negocio.facade.jdbc.arquitetura.SuperFacadeJDBC;
import negocio.facade.jdbc.arquitetura.Usuario;
import negocio.facade.jdbc.financeiro.Caixa;
import negocio.facade.jdbc.financeiro.MovConta;
import org.apache.poi.xssf.usermodel.XSSFRow;

import java.io.File;
import java.io.FileInputStream;
import java.sql.Connection;
import java.sql.DriverManager;
import java.sql.ResultSet;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * Created by Joao Alcides on 01/11/2016.
 */
public class ImportadorFinanceiro {
    
    public static Integer EMPRESA = 1;
    public static Integer contaPadrao = 1;

    public static Map<String, Integer> mapaContas(){
        Map<String, Integer> contas = new HashMap<String, Integer>();
        //ADICIONE AQUI A CORRESPONDENCIA ENTRE CONTAS
        //"NOME DA CONTA NO INFO SKY", codigo da conta no ZW
        contas.put("13003075-9", 10);
        contas.put("FOM", 6);
        contas.put("02065-9", 5);
        return contas;
    }

    public static Map<String, Integer> mapaCC(){
        Map<String, Integer> contas = new HashMap<String, Integer>();
        //ADICIONE AQUI A CORRESPONDENCIA ENTRE CENTRO DE CUSTOS
        //"CODIGO DO CENTRO DE CUSTOS NO INFO SKY", codigo do centro de custos no ZW
        contas.put("1", 1);
        contas.put("2", 11);
        return contas;
    }

    public static List<String> listFiles(String caminho ){
        File diretorio = new File(caminho);
        List<String> lista = Arrays.asList(diretorio.list());
        return lista;
    }

    public static void importarFinanceiro(String caminho, Connection con, Integer empresa) throws Exception{
        List<String> arquivos = listFiles(caminho);
        for(String arquivo : arquivos){
            if(!arquivo.startsWith("~")){
                importarFinanceiroArquivoSimples("C:\\PactoJ\\Monday\\"+arquivo, con, empresa);
            }
        }
        for(String arquivo : arquivos){
            if(!arquivo.startsWith("~")){
                System.out.println(arquivo + " importado com sucesso!");
            }

        }
    }

    public static void importarFinanceiroArquivoSimples(String arquivo, Connection con, Integer empresa) throws Exception{
        try {
            SuperFacadeJDBC.executarConsulta("ALTER TABLE movconta ADD COLUMN importado BOOLEAN DEFAULT FALSE;", con);
        }catch(Exception e){
        }
        try {
            SuperFacadeJDBC.executarConsulta("ALTER TABLE planoconta add column nomeconsulta text;", con);
        }catch(Exception e){
        }
        SuperFacadeJDBC.executarConsulta("update planoconta set nomeconsulta = remove_acento_upper(nome);", con);
        List<XSSFRow> hssfRows = LeitorExcel2010.lerLinhas(new FileInputStream(arquivo));
        System.out.println(arquivo);
        Map<String, FornecedorVO> mapaFornecedores = ImportarFornecedorAntigo.obterFornecedores(hssfRows, empresa, con);
        Usuario userDao = new Usuario(con);
        Caixa caixaDao = new Caixa(con);
        MovConta movContaDao = new MovConta(con);
        List<UsuarioVO> list = userDao.consultarPorUsername("PACTOBR", false, Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA);
        Map<String, Integer> mapaContas = mapaContas();
        Map<String, Integer> mapaCC = mapaCC();

        if(!list.isEmpty()){
            CaixaVO caixaVO =  new CaixaVO();
            caixaVO.setUsuarioVo(list.get(0));
            caixaVO.setDataAbertura(Calendario.hoje());
            caixaVO.setDataFechamento(Calendario.hoje());
            caixaVO.setEmpresaVo(new EmpresaVO());
            caixaVO.getEmpresaVo().setCodigo(empresa);
            caixaDao.incluir(caixaVO);
            for(XSSFRow linha : hssfRows){
                MovContaVO movContaVO = new MovContaVO();
                movContaVO.setEmpresaVO(new EmpresaVO());
                movContaVO.getEmpresaVO().setCodigo(empresa);
                movContaVO.setDataCompetencia(Uteis.getDate(LeitorExcel2010.obterString(linha, 2)));
                movContaVO.setDataLancamento(Uteis.getDate(LeitorExcel2010.obterString(linha, 1)));
                movContaVO.setDataVencimento(Uteis.getDate(LeitorExcel2010.obterString(linha, 2)));
                if(!UteisValidacao.emptyString(LeitorExcel2010.obterString(linha, 3).trim())){
                    Date baixa = Uteis.getDate(LeitorExcel2010.obterString(linha, 3));
                    if(Calendario.maiorOuIgual(Calendario.hoje(), baixa)){
                        movContaVO.setDataQuitacao(baixa);
                    }
                }
                movContaVO.setPessoaVO(mapaFornecedores.get(LeitorExcel2010.obterString(linha, 4)).getPessoa());

                Double valor =LeitorExcel2010.obterNumero(linha, 5).doubleValue();
                movContaVO.setValor(valor < 0.0 ? valor * -1 : valor);

                movContaVO.setContaVO(new ContaVO());
                Integer conta = mapaContas.get(LeitorExcel2010.obterString(linha, 9));
                if(conta == null){
                    conta = contaPadrao;
                }
                movContaVO.getContaVO().setCodigo(conta);
                movContaVO.setDescricao(LeitorExcel2010.obterString(linha, 6));

                MovContaRateioVO movContaRateioVO = new MovContaRateioVO();
                movContaRateioVO.setDescricao(movContaVO.getDescricao());
                movContaRateioVO.setValor(movContaVO.getValor());
                movContaRateioVO.setTipoES(valor < 0.0 ? TipoES.ENTRADA : TipoES.SAIDA);

                String cc = LeitorExcel2010.obterStringDoNumero(linha, 12);
                Integer codigoCC = mapaCC.get(cc);
                if(!UteisValidacao.emptyNumber(codigoCC)){
                    movContaRateioVO.setCentroCustoVO(new CentroCustoTO());
                    movContaRateioVO.getCentroCustoVO().setCodigo(codigoCC);
                }

                String plano = LeitorExcel2010.obterString(linha, 11);

                Integer codigoPC = planoContasNome(con, plano);
                if(UteisValidacao.emptyNumber(codigoPC)){
                    codigoPC = planoContas(con, plano);
                }
                if(!UteisValidacao.emptyNumber(codigoPC)){
                    movContaRateioVO.setPlanoContaVO(new PlanoContaTO());
                    movContaRateioVO.getPlanoContaVO().setCodigo(codigoPC);
                }

                String obs = (UteisValidacao.emptyNumber(codigoPC) ? ("PC: "+LeitorExcel2010.obterString(linha, 11)) : "")+"\n";
                obs += "RECONC: " + LeitorExcel2010.obterString(linha, 7);
                obs += "\nCHEQUE: " + LeitorExcel2010.obterString(linha, 8);
                obs += "\nDOCUMENTO: " + LeitorExcel2010.obterString(linha, 13);
                obs += "\n\n\nARQ: " + arquivo;
                movContaVO.setObservacoes(obs);

                movContaVO.setMovContaRateios(new ArrayList<MovContaRateioVO>());
                movContaVO.getMovContaRateios().add(movContaRateioVO);
                movContaVO.setUsuarioVO(caixaVO.getUsuarioVo());
                movContaVO.setTipoOperacaoLancamento(valor < 0.0 ? TipoOperacaoLancamento.RECEBIMENTO : TipoOperacaoLancamento.PAGAMENTO);

                movContaDao.incluirSemCommit(movContaVO, caixaVO.getCodigo(), false, null);
                SuperFacadeJDBC.executarConsulta("UPDATE movconta SET importado = TRUE WHERE codigo = "+movContaVO.getCodigo(), con);
            }
        }
    }

    public static void main(String[] args){
        try {
            Connection con = DriverManager.getConnection("***************************************", "postgres", "pactodb");
            importarFinanceiro("C:\\PactoJ\\Monday", con, 1);
//            importarFinanceiroArquivoSimples("C:\\PactoJ\\Monday\\Mai16.xlsx", con, EMPRESA);
        }catch (Exception e){
            e.printStackTrace();
        }
    }

    

    public static Integer planoContas(Connection con, String nomepc) throws Exception{
        ResultSet resultSet = SuperFacadeJDBC.criarConsulta("SELECT codigo FROM planoconta where nomeconsulta like remove_acento_upper('" +
                nomepc.replaceAll(" ", "%")+"%')", con);
        if(resultSet.next()){
            return resultSet.getInt("codigo");
        }
        return null;
    }


    public static Integer planoContasNome(Connection con, String nomepc) throws Exception{
        ResultSet resultSet = SuperFacadeJDBC.criarConsulta("SELECT codigo FROM planoconta where nome ilike '"+nomepc+"'", con);
        if(resultSet.next()){
            return resultSet.getInt("codigo");
        }
        return null;
    }
}




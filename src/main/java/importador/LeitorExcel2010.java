package importador;

import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisValidacao;
import org.apache.poi.ss.usermodel.CellType;
import org.apache.poi.xssf.usermodel.XSSFCell;
import org.apache.poi.xssf.usermodel.XSSFRow;
import org.apache.poi.xssf.usermodel.XSSFSheet;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;

import java.io.FileInputStream;
import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * Created by Joao Alcides on 01/11/2016.
 */
public class LeitorExcel2010 {


    public static List<XSSFRow> lerLinhas(FileInputStream file) throws Exception {
        return lerLinhas(file, 0);
    }

    public static List<XSSFRow> lerLinhas(FileInputStream file, int indexSheet) throws Exception {

        // WorkBook, o arquivo planilha em si

        XSSFWorkbook wb;

        // Worksheet vai usar, aquelas abinhas que tem lá embaixo no Excel

        XSSFSheet sheet;
        // passa qual WorkBook vai ser usada, de acordo com o caminho
        wb = new XSSFWorkbook(file);
        List<XSSFRow> linhas = new ArrayList<XSSFRow>();
        // Passa qual WorkSheet vai ser usada. Nesse caso a primera (0)
        sheet = wb.getSheetAt(indexSheet);
        // Cria a linha de acordo com o parametro
        boolean continua = true;
        int i = 1;
        while (continua) {
            XSSFRow row = sheet.getRow(i);
            try {
                retirarNullExcell(row, 1);
                linhas.add(row);
                i++;
            } catch (Exception e) {
                continua = false;
//                System.out.println("A leitura do arquivo Excel terminou na linha " + (i + 1) + " do arquivo.");
            }
        }
        return linhas;
    }

    public static XSSFRow obterLinha(Integer linha, FileInputStream file, int indexSheet) throws Exception {

        // WorkBook, o arquivo planilha em si

        XSSFWorkbook wb;

        // Worksheet vai usar, aquelas abinhas que tem lá embaixo no Excel

        XSSFSheet sheet;
        // passa qual WorkBook vai ser usada, de acordo com o caminho
        wb = new XSSFWorkbook(file);
        List<XSSFRow> linhas = new ArrayList<XSSFRow>();
        // Passa qual WorkSheet vai ser usada. Nesse caso a primera (0)
        sheet = wb.getSheetAt(indexSheet);
//         Cria a linha de acordo com o parametro
        return sheet.getRow(linha);
    }

    public static List<XSSFRow> lerLinhas(String caminho) throws Exception {
        return lerLinhas(caminho, 0);
    }

    public static List<XSSFRow> lerLinhas(String caminho, int indexSheet) throws Exception {
        return lerLinhas(new FileInputStream(caminho), indexSheet);
    }

    public static Number obterNumero(XSSFRow row, int coluna) {
        XSSFCell cell = row.getCell((short) coluna);
        Number n = 0;
        try {
            n = cell.getNumericCellValue();
        } catch (Exception e) {
//			e.printStackTrace();
        }
        return n;
    }

    public static Double obterDouble(XSSFRow row, int coluna) {
        XSSFCell cell = row.getCell((short) coluna);
        try {
            if (cell.getCellTypeEnum().equals(CellType.NUMERIC)) {
                return cell.getNumericCellValue();
            } else if (cell.getCellTypeEnum().equals(CellType.STRING)) {
                String valor = cell.getStringCellValue();
                valor = removerPontosDesnecessariosDoubleString(valor);
                return new Double(valor);
            }
            return null;
        } catch (Exception e) {
            return null;
        }
    }

    private static String removerPontosDesnecessariosDoubleString(String valor) {
        valor = valor.replaceAll("R", "").replaceAll("\\$", "").replaceAll(" ", "");
        if (valor.contains(",")) {
            valor = valor.replaceAll(",", ".");
        }
        Integer qtdPontos = Uteis.contarSequenciaCaracterString(valor, ".");
        if (qtdPontos > 1) {
            int i = 1;
            while (i < qtdPontos) {
                valor = valor.replaceFirst("\\.", "");
                ++i;
            }
        }
        return valor;
    }

    public static String obterStringDoNumero(XSSFRow row, int coluna) {
        XSSFCell cell = row.getCell((short) coluna);
        Number n = 0;
        try {
            n = cell.getNumericCellValue();
        } catch (Exception e) {
//			e.printStackTrace();
        }
        return n == null ? "" : "" + n.intValue();

    }

    public static Date obterData(XSSFRow row, int coluna) {
        XSSFCell cell = row.getCell((short) coluna);
        try {
            String value = cell.getStringCellValue();
            DateFormat formatter = new SimpleDateFormat("yyyy-MM-dd HH:mm");
            value = value.substring(0, 16);
            formatter.setLenient(false);
            Date date = (Date) formatter.parse(value);
            return date;
        } catch (Exception e) {
            return null;
        }

    }

    public static Date obterDataEspecifico(XSSFRow row, int coluna) {
        return obterData(row, coluna, "dd/MM/yyyy HH:mm");
    }

    public static Date obterData(XSSFRow row, int coluna, String mascara) {
        XSSFCell cell = row.getCell((short) coluna);
        try {
            Date value = cell.getDateCellValue();
            return value;
        } catch (Exception e) {
            try {
                String v = cell.getStringCellValue();
                return Uteis.getDate(v, mascara);
            } catch (Exception ex) {
                return null;
            }
        }
    }

    public static String obterString(XSSFRow row, int coluna) {
        XSSFCell cell = row.getCell((short) coluna);
        try {
            if (!cell.getCellTypeEnum().equals(CellType.STRING)) {
                cell.setCellType(CellType.STRING);
            }
            return cell.getStringCellValue();
        } catch (Exception e) {
            return "";
        }
    }

    public static String obterStringCorrigindoNotacoesCientificas(XSSFRow row, int coluna) {
        //metodo criado para resolver problemas em planilhas com colunas definidas como 'geral' ou 'numero' no Excel
        //em alguns casos, numeros grandes como CPF ou RG sao lidos como notacao cientifica pelo leitor
        //mesmo apos conversao para string no metodo obterString, a notacao cientifica pode permanecer
        //este metodo corrige essa situacao (ver ticket GC-1356 - importacao drako)

        XSSFCell cell = row.getCell((short) coluna);
        try {
            if (cell == null) {
                return "";
            }

            // Verifica se a celula eh do tipo NUMERIC e converte para texto
            if (cell.getCellTypeEnum().equals(CellType.NUMERIC)) {
                // Remove a notacao cientifica e converte para string
                return String.valueOf((long) cell.getNumericCellValue()).trim();
            }

            // Força conversao para STRING
            if (!cell.getCellTypeEnum().equals(CellType.STRING)) {
                cell.setCellType(CellType.STRING);
            }

            return cell.getStringCellValue().trim();
        } catch (Exception e) {
            return "";
        }
    }


    public static Boolean obterBooleanImportacao(XSSFRow row, int coluna, boolean retornoVazio) {
        XSSFCell cell = row.getCell((short) coluna);
        try {
            if (cell.getCellTypeEnum().equals(CellType.BOOLEAN)) {
                return cell.getBooleanCellValue();
            }

            if (!cell.getCellTypeEnum().equals(CellType.STRING)) {
                cell.setCellType(CellType.STRING);
            }

            String valor = cell.getStringCellValue().toUpperCase();
            if (UteisValidacao.emptyString(valor)) {
                return retornoVazio;
            }

            if (valor.equalsIgnoreCase("SIM") || valor.equalsIgnoreCase("TRUE") ||
                    valor.equalsIgnoreCase("ATIVO") || valor.equalsIgnoreCase("S")) {
                return true;
            } else if (valor.equalsIgnoreCase("NÃO") || valor.equalsIgnoreCase("NAO") ||
                    valor.equalsIgnoreCase("N") || valor.equalsIgnoreCase("FALSE") ||
                    valor.equalsIgnoreCase("INATIVO")) {
                return false;
            }
            return null;
        } catch (Exception e) {
            return null;
        }
    }

    @SuppressWarnings("deprecation")
    public static String retirarNullExcell(XSSFRow row, int coluna) {
        XSSFCell cell = row.getCell((short) coluna);
        String valor = null;
        try {
            if (cell != null) {
                valor = cell.getStringCellValue();
            }

        } catch (IllegalStateException e) {
            try {
                Date data = cell.getDateCellValue();
                valor = Uteis.getData(data);
            } catch (IllegalStateException ex) {
                valor = String.valueOf(cell.getNumericCellValue());
            }

        } catch (Exception e) {
//			e.printStackTrace();
        }

        if (valor == null || valor.equals("null") || valor.equals("")) {
            return null;
        } else {
            return valor;
        }
    }

    public static Date obterDataNoFormatoData(XSSFRow row, int coluna) {
        XSSFCell cell = row.getCell((short) coluna);
        try {
            return cell.getDateCellValue();
        } catch (Exception e) {
            return null;
        }

    }
}

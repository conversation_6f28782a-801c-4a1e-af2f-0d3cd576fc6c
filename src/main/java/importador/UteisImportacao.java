package importador;

import br.com.pactosolucoes.comuns.util.Formatador;
import br.com.pactosolucoes.controle.json.sesice.TiposNecessidadesEspeciaisSesiCeEnum;
import br.com.pactosolucoes.enumeradores.TipoColaboradorEnum;
import br.com.pactosolucoes.enumeradores.TipoTelefoneEnum;
import importador.json.ClienteImportacaoJSON;
import importador.json.EmailImportacaoJSON;
import importador.json.EnderecoImportacaoJSON;
import importador.json.TelefoneImportacaoJSON;
import importador.json.TipoColaboradorImportacaoJSON;
import importador.json.VinculoImportacaoJSON;
import negocio.comuns.basico.*;
import negocio.comuns.contrato.JustificativaOperacaoVO;
import negocio.comuns.plano.AmbienteVO;
import negocio.comuns.plano.ModalidadeEmpresaVO;
import negocio.comuns.plano.ModalidadeVO;
import negocio.comuns.plano.NivelTurmaVO;
import negocio.comuns.utilitarias.Calendario;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisEmail;
import negocio.comuns.utilitarias.UteisValidacao;
import negocio.facade.jdbc.arquitetura.SuperFacadeJDBC;
import negocio.facade.jdbc.basico.*;
import negocio.facade.jdbc.basico.webservice.IntegracaoImportacao;
import negocio.facade.jdbc.contrato.JustificativaOperacao;
import negocio.facade.jdbc.plano.Ambiente;
import negocio.facade.jdbc.plano.Modalidade;
import negocio.facade.jdbc.plano.NivelTurma;

import java.sql.Connection;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.logging.Level;
import java.util.logging.Logger;

public class UteisImportacao {

    public static Date getDateFromLocalDateTime(String strDate) {
        if (UteisValidacao.emptyString(strDate)) {
            return null;
        }
        LocalDateTime localDateTime = LocalDateTime.parse(strDate);
        return Date.from(localDateTime.atZone(ZoneId.systemDefault()).toInstant());
    }

    public static ModalidadeVO obterModalidadeImportacao(Integer codigoEmpresa, Connection con) throws Exception {
        return obterModalidade(codigoEmpresa, "IMPORTAÇÃO", false, con);
    }

    public static ModalidadeVO obterModalidade(Integer codigoEmpresa, String nomeModalidade, boolean utilizaTurma, Connection con) throws Exception {
        Modalidade modalidadeDAO = new Modalidade(con);

        nomeModalidade = UteisValidacao.emptyString(nomeModalidade) ? "IMPORTAÇÃO" : nomeModalidade;

        ModalidadeVO modalidadeVO = modalidadeDAO.consultarPorNomeModalidade(nomeModalidade, false, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
        if (modalidadeVO == null || UteisValidacao.emptyNumber(modalidadeVO.getCodigo())) {
            modalidadeVO = new ModalidadeVO();
            modalidadeVO.setNome(nomeModalidade);
            modalidadeVO.setUtilizarTurma(utilizaTurma);
            modalidadeVO.setValidarDados(false);
            modalidadeVO.setValorMensal(0.0);
            modalidadeVO.setModalidadeDefault(false);
            modalidadeVO.setUtilizarProduto(false);
            modalidadeVO.setUsaTreino(false);
            modalidadeVO.setModalidadeEmpresaVOs(new ArrayList());
            ModalidadeEmpresaVO modalidadeEmpresaVO = new ModalidadeEmpresaVO();
            modalidadeEmpresaVO.getEmpresa().setCodigo(codigoEmpresa);
            modalidadeVO.setModalidadeEmpresaVOs(new ArrayList());
            modalidadeVO.getModalidadeEmpresaVOs().add(modalidadeEmpresaVO);
            modalidadeVO.setNrVezes(7);
            modalidadeVO.setCrossfit(false);
            modalidadeVO.setAtivo(true);
            modalidadeDAO.incluirSemCommit(modalidadeVO);
        } else {
            modalidadeVO.setAtivo(true);
            modalidadeVO.setUtilizarTurma(utilizaTurma);
            SuperFacadeJDBC.executarUpdate("update modalidade set ativo = true, utilizarturma = " + utilizaTurma + " where codigo = " + modalidadeVO.getCodigo(), con);
        }

        return modalidadeVO;
    }

    public CidadeVO obterCidade(String nomeCidade, EstadoVO estadoVO, Connection con) throws Exception {
        if (UteisValidacao.emptyString(nomeCidade) || estadoVO == null || UteisValidacao.emptyNumber(estadoVO.getCodigo())) {
            return new CidadeVO();
        }

        Cidade cidadeDAO = new Cidade(con);
        CidadeVO cidadeVO = cidadeDAO.consultarCidadePorNomeEstadoPais(nomeCidade, estadoVO.getCodigo(), estadoVO.getPais(), Uteis.NIVELMONTARDADOS_DADOSBASICOS);
        if (cidadeVO == null || UteisValidacao.emptyNumber(cidadeVO.getCodigo())) {
            cidadeVO = new CidadeVO();
            cidadeVO.setEstado(estadoVO);
            cidadeVO.getPais().setCodigo(estadoVO.getPais());
            cidadeVO.setNome(nomeCidade);
            cidadeDAO.incluir(cidadeVO);
        }
        cidadeDAO = null;
        return cidadeVO;
    }

    public ProfissaoVO obterProfissao(String profissao, Connection con) {
        ProfissaoVO profissaoVO = new ProfissaoVO();
        try {
            profissao = profissao.trim().toUpperCase();
            if (UteisValidacao.emptyString(profissao)) {
                return profissaoVO;
            }

            try {
                profissaoVO.setCodigo(Integer.valueOf(profissao));
            } catch (NumberFormatException e) {
                Profissao profissaoDAO = new Profissao(con);
                profissaoVO = profissaoDAO.criarOuConsultarProfissaoPorDescricao(profissao, Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA);
                profissaoDAO = null;
            }
        } catch (Exception e) {
            Uteis.logar("Erro ao preencher Profissão: " + e.getMessage());
        }
        return profissaoVO;
    }

    public static CategoriaVO obterCategoria(String categoria, Connection con) {
        CategoriaVO categoriaVO = new CategoriaVO();
        try {
            categoria = categoria.trim().toUpperCase();
            if (UteisValidacao.emptyString(categoria)) {
                return categoriaVO;
            }
            try {
                categoriaVO.setCodigo(Integer.valueOf(categoria));
            } catch (NumberFormatException e) {
                Categoria categoriaDAO = new Categoria(con);
                categoriaVO = categoriaDAO.criarOuConsultarCategoriaPorNome(categoria, Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA);
                categoriaDAO = null;
            }
        } catch (Exception e) {
            Uteis.logar("Erro ao preencher categoria: " + e.getMessage());
        }
        return categoriaVO;
    }

    public static String obterNecessidadesEspeciais(String colunaNecessidadeEspecialExcel, Connection con) {
        if (UteisValidacao.emptyString(colunaNecessidadeEspecialExcel)) {
            return null;
        }

        TiposNecessidadesEspeciaisSesiCeEnum necessidadeEspecial = TiposNecessidadesEspeciaisSesiCeEnum.obterConsultarPorSigla(colunaNecessidadeEspecialExcel.trim().toUpperCase());

        return (necessidadeEspecial != null) ? necessidadeEspecial.getSigla() : null;
    }

    public static FornecedorVO obterEmpresaFornecedor(String colunaEmpresaFornecedorExcel, Connection con) {
        try {
            if (UteisValidacao.emptyString(colunaEmpresaFornecedorExcel)) {
                return null;
            }

            int codigoFornecedor = Integer.parseInt(colunaEmpresaFornecedorExcel);
            Fornecedor fornecedorDAO = new Fornecedor(con);
            return fornecedorDAO.obter(codigoFornecedor);

        } catch (Exception e) {
            return null;
        }
    }

    public GrauInstrucaoVO obterGrauInstrucao(String grauInstrucao, Connection con) {
        GrauInstrucaoVO grauInstrucaoVO = new GrauInstrucaoVO();
        try {
            grauInstrucao = grauInstrucao.trim().toUpperCase();
            if (UteisValidacao.emptyString(grauInstrucao)) {
                return grauInstrucaoVO;
            }

            GrauInstrucao grauInstrucaoDAO = new GrauInstrucao(con);
            List<GrauInstrucaoVO> list = grauInstrucaoDAO.consultarPorDescricao(grauInstrucao, false, Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA);
            if (UteisValidacao.emptyList(list)) {
                grauInstrucaoVO.setDescricao(grauInstrucao);
                grauInstrucaoDAO.incluir(grauInstrucaoVO, true);
            } else {
                grauInstrucaoVO = list.get(0);
            }
            grauInstrucaoDAO = null;
        } catch (Exception ex) {
            Logger.getLogger(IntegracaoImportacao.class.getName()).log(Level.SEVERE, null, ex);
        }
        return grauInstrucaoVO;
    }

    public static AmbienteVO obterAmbiente(String ambiente, Connection con) throws Exception {
        if (UteisValidacao.emptyString(ambiente)) {
            return null;
        }
        Ambiente ambienteDAO = new Ambiente(con);
        List<AmbienteVO> ambienteVOS = ambienteDAO.consultarPorDescricao(ambiente, false, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
        if (!UteisValidacao.emptyList(ambienteVOS)) {
            return ambienteVOS.get(0);
        } else {
            AmbienteVO ambienteVO = new AmbienteVO();
            ambienteVO.setDescricao(ambiente);
            ambienteVO.setCapacidade(99);
            ambienteVO.setTipoModulo("TU");
            ambienteVO.setSituacaoAmbiente(1);
            ambienteDAO.incluir(ambienteVO);
            return ambienteVO;
        }

    }

    public static NivelTurmaVO obterNivel(String nivel, Connection con) throws Exception {
        if (UteisValidacao.emptyString(nivel)) {
            return null;
        }
        NivelTurma nivelDAO = new NivelTurma(con);
        List<NivelTurmaVO> nivelTurmaVOS = nivelDAO.consultarPorDescricao(nivel, false, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
        if (!UteisValidacao.emptyList(nivelTurmaVOS)) {
            return nivelTurmaVOS.get(0);
        } else {
            NivelTurmaVO nivelTurmaVO = new NivelTurmaVO();
            nivelTurmaVO.setDescricao(nivel);
            nivelDAO.incluir(nivelTurmaVO);
            return nivelTurmaVO;
        }
    }

    public Date obterDataNascimento(Date dataNascimento) {
        if (dataNascimento != null && Calendario.maior(dataNascimento, Calendario.hoje())) {
            dataNascimento = null;
        }
        return dataNascimento;
    }

    public List<VinculoVO> obterVinculosVO(ClienteImportacaoJSON clienteJSON, ImportacaoCache cache, Connection con) {
        List<VinculoVO> lista = new ArrayList<>();
        for (VinculoImportacaoJSON vinculoJSON : clienteJSON.getVinculos()) {
            try {

                TipoColaboradorEnum tipoVinculo = TipoColaboradorEnum.getTipo(vinculoJSON.getTipoVinculo());
                if (tipoVinculo == null) {
                    continue;
                }

                ColaboradorVO colaboradorVO = new ColaboradorVO();
                Colaborador colaboradorDAO = new Colaborador(con);
                if (!UteisValidacao.emptyNumber(vinculoJSON.getColaborador())) {
                    colaboradorVO = cache.obterColaboradorVO(vinculoJSON.getColaborador());
                } else if (!UteisValidacao.emptyString(vinculoJSON.getNomeColaborador())) {
                    List<ColaboradorVO> listaCol = colaboradorDAO.consultarPorNomeTipoVinculoPossivel(vinculoJSON.getNomeColaborador(), tipoVinculo, clienteJSON.getEmpresa(), Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA);
                    if (!UteisValidacao.emptyList(listaCol)) {
                        colaboradorVO = listaCol.get(0);
                    }
                }
                colaboradorDAO = null;

                if (colaboradorVO != null && !UteisValidacao.emptyNumber(colaboradorVO.getCodigo())) {
                    VinculoVO vinculoVO = new VinculoVO();
                    vinculoVO.setColaborador(colaboradorVO);
                    vinculoVO.setTipoVinculo(tipoVinculo.getSigla());
                    lista.add(vinculoVO);
                }
            } catch (Exception ex) {
                ex.printStackTrace();
            }
        }
        return lista;
    }

    public List<TipoColaboradorVO> obterTipoColaboradorVO(List<TipoColaboradorImportacaoJSON> lista) {
        List<TipoColaboradorVO> retorno = new ArrayList<>();
        for (TipoColaboradorImportacaoJSON json : lista) {
            try {
                TipoColaboradorEnum tipoEnum = TipoColaboradorEnum.getTipo(json.getSigla());
                if (tipoEnum == null) {
                    continue;
                }

                TipoColaboradorVO tipoVO = new TipoColaboradorVO();
                tipoVO.setDescricao(tipoEnum.getSigla());
                retorno.add(tipoVO);
            } catch (Exception ex) {
                ex.printStackTrace();
            }
        }
        return retorno;
    }

    public List<EnderecoVO> obterListaEnderecoVO(List<EnderecoImportacaoJSON> listaEnderecoJSON) {
        if (UteisValidacao.emptyList(listaEnderecoJSON)) {
            return new ArrayList<>();
        }
        List<EnderecoVO> lista = new ArrayList<>();
        for (EnderecoImportacaoJSON enderecoJSON : listaEnderecoJSON) {
            try {
                if (!UteisValidacao.emptyString(enderecoJSON.getEndereco())) {
                    EnderecoVO endVO = new EnderecoVO();
                    endVO.setCep(obterCEPMascarado(enderecoJSON.getCep()));
                    endVO.setTipoEndereco(enderecoJSON.getTipo());
                    endVO.setBairro(enderecoJSON.getBairro());
                    endVO.setNumero(enderecoJSON.getNumero());
                    endVO.setComplemento(enderecoJSON.getComplemento());
                    endVO.setEndereco(enderecoJSON.getEndereco());
                    endVO.setEnderecoCorrespondencia(enderecoJSON.isCorrespondencia());
                    lista.add(endVO);
                }
            } catch (Exception ex) {
                ex.printStackTrace();
            }
        }
        return lista;
    }

    public String obterCEPMascarado(String cep) {
        if (UteisValidacao.emptyString(cep)) {
            return "";
        }

        String cepSemMascara = Uteis.tirarCaracteres(cep, true);
        if (UteisValidacao.emptyString(cepSemMascara)) {
            return "";
        }

        if (cepSemMascara.length() > 8) {
            cepSemMascara = cepSemMascara.substring(0, 8);
        }

        if (cepSemMascara.length() == 8) {
            return Formatador.formatarString("##.###-###", cepSemMascara);
        } else {
            return cepSemMascara;
        }
    }

    public List<TelefoneVO> obterListaTelefonesVO(List<TelefoneImportacaoJSON> listaTelefoneJSON, String padraoDDD) {
        if (UteisValidacao.emptyList(listaTelefoneJSON)) {
            return new ArrayList<>();
        }
        List<TelefoneVO> lista = new ArrayList<>();
        for (TelefoneImportacaoJSON telefoneJSON : listaTelefoneJSON) {
            try {
                TipoTelefoneEnum tipoTelefoneEnum = TipoTelefoneEnum.obterPorSigla(telefoneJSON.getTipoTelefone());
                TelefoneVO tel = obterTelefone(telefoneJSON.getNumero(), tipoTelefoneEnum, padraoDDD);
                if (tel == null) {
                    continue;
                }
                lista.add(tel);
            } catch (Exception ex) {
                ex.printStackTrace();
            }
        }
        return lista;
    }

    public TelefoneVO obterTelefone(String numero, TipoTelefoneEnum tipoTelefoneEnum, String padraoDDD) {
        if (tipoTelefoneEnum == null || UteisValidacao.emptyString(numero)) {
            return null;
        }

        String numeroSemMascara = Uteis.tirarCaracteres(numero, true);

        if (UteisValidacao.emptyString(numeroSemMascara)) {
            return null;
        }

        Integer qtdSemDDD = 0;
        if (tipoTelefoneEnum.equals(TipoTelefoneEnum.CELULAR)) {
            qtdSemDDD = 9;
        } else if (tipoTelefoneEnum.equals(TipoTelefoneEnum.RESIDENCIAL)) {
            qtdSemDDD = 8;
        }

        if (numeroSemMascara.length() <= qtdSemDDD && !UteisValidacao.emptyString(padraoDDD)) {
            numeroSemMascara = padraoDDD + numeroSemMascara;
        }

        TelefoneVO telefoneVO = new TelefoneVO();
        telefoneVO.setTipoTelefone(tipoTelefoneEnum.getCodigo());
        telefoneVO.setNumero(Formatador.formataTelefoneZW(numeroSemMascara));
        telefoneVO.setValidarDados(false);
        return telefoneVO;
    }

    public List<EmailVO> obterListaEmailVO(List<EmailImportacaoJSON> listaEmailJSON) {
        if (UteisValidacao.emptyList(listaEmailJSON)) {
            return new ArrayList<>();
        }
        List<EmailVO> lista = new ArrayList<>();
        for (EmailImportacaoJSON emailJSON : listaEmailJSON) {
            try {
                EmailVO emailVO = new EmailVO();
                if (UteisValidacao.emptyString(emailJSON.getEmail()) || !UteisEmail.getValidEmail(emailJSON.getEmail())) {
                    continue;
                }
                emailVO.setEmail(emailJSON.getEmail());
                emailVO.setEmailCorrespondencia(true);
                lista.add(emailVO);
            } catch (Exception ex) {
                ex.printStackTrace();
            }
        }
        return lista;
    }

    public static JustificativaOperacaoVO obterJustificativaOperacao(Integer codigoEmpresa, String descricao, String tipoOperacao, Connection con) throws Exception {
        JustificativaOperacao justificativaDAO = new JustificativaOperacao(con);
        JustificativaOperacaoVO justificativaVO = new JustificativaOperacaoVO();
        justificativaVO.setDescricao(descricao);
        justificativaVO.setEmpresa(new EmpresaVO());
        justificativaVO.getEmpresa().setCodigo(codigoEmpresa);
        justificativaVO.setTipoOperacao(tipoOperacao);
        return justificativaDAO.criarOuConsultarSeExistePorNome(justificativaVO);
    }
}

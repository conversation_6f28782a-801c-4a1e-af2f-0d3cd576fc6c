package importador.financeiro;


import importador.ImportacaoCache;
import importador.ImportacaoConfigTO;
import importador.LeitorExcel2010;
import importador.UteisImportacao;
import importador.json.FornecedorImportacaoJSON;
import negocio.comuns.basico.*;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisValidacao;
import negocio.facade.jdbc.arquitetura.Log;
import negocio.facade.jdbc.arquitetura.SuperFacadeJDBC;
import negocio.facade.jdbc.basico.Fornecedor;
import negocio.facade.jdbc.basico.Pessoa;
import org.apache.poi.xssf.usermodel.XSSFRow;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class ImportarFornecedor {

    private Connection con;
    private ImportacaoConfigTO configTO;

    public ImportarFornecedor(ImportacaoConfigTO configTO, Connection con) {
        this.con = con;
        this.configTO = configTO;
    }

    public FornecedorVO importarFornecedor(FornecedorImportacaoJSON json, ConfigExcelImportacaoTO configExcelImportacaoTO, ImportacaoCache cache) throws Exception {
        try {
            con.setAutoCommit(false);

            Uteis.logar(true, null, "INICIA IMPORTAÇÃO | FORNECEDOR... " + json.getIdExterno() + " | " + json.getNome().toUpperCase());

            if (UteisValidacao.emptyNumber(json.getIdExterno())) {
                throw new Exception("Necessário que o fornecedor tenha um IdExterno");
            }

            ResultSet rsExiste = SuperFacadeJDBC.criarConsulta("SELECT codigo FROM fornecedor WHERE idExterno = '" + json.getIdExterno()
                    + "' and empresa = " + json.getEmpresa(), con);
            if (rsExiste.next()) {
                throw new Exception("Já existe um FORNECEDOR importado com o idExterno " + json.getIdExterno() + " na empresa " + json.getEmpresa());
            }

            UteisImportacao uteisImportacao = new UteisImportacao();

            if (configExcelImportacaoTO.isValidarCpfCnpjJaCadastrado() && !json.getCpf_cnpj().equals("")) {
                ResultSet rs = SuperFacadeJDBC.criarConsulta("select nome, f.cnpj from fornecedor f join pessoa on pessoa.codigo = f.pessoa where (f.cnpj = '" + json.getCpf_cnpj_SomenteNumeros() + "' or f.cnpj = '" + json.getCpf_cnpj_Formatado() + "') ", con);
                if (rs.next()) {
                    String nome = rs.getString("nome");
                    throw new Exception("O CNPJ (" + json.getCpf_cnpj_Formatado() + ") já está cadastrado para o fornecedor \"" + nome + "\".");
                }
            }

            EmpresaVO empresaVO = cache.obterEmpresaVO(json.getEmpresa());

            EstadoVO estadoVO = new EstadoVO();
            CidadeVO cidadeVO = new CidadeVO();
            PaisVO paisVO = new PaisVO();

            if (!UteisValidacao.emptyString(json.getUf())) {
                estadoVO = cache.obterEstadoVO(json.getUf());
                if (estadoVO != null && !UteisValidacao.emptyNumber(estadoVO.getCodigo())) {
                    if (!UteisValidacao.emptyNumber(estadoVO.getPais())) {
                        paisVO.setCodigo(estadoVO.getPais());
                    }
                    cidadeVO = uteisImportacao.obterCidade(json.getCidade(), estadoVO, con);
                }
            }

            if (cidadeVO != null && !UteisValidacao.emptyNumber(cidadeVO.getCodigo())) {
                estadoVO = cidadeVO.getEstado();
                paisVO = cidadeVO.getPais();
            }

            PessoaVO pessoaVO = new PessoaVO();
            pessoaVO.setNome(json.getNome());
            pessoaVO.setPais(paisVO);
            pessoaVO.setEstadoVO(estadoVO);
            pessoaVO.setCidade(cidadeVO);
            pessoaVO.setTipoPessoa("1");
            pessoaVO.setEnderecoVOs(uteisImportacao.obterListaEnderecoVO(json.getEnderecos()));
            pessoaVO.setTelefoneVOs(uteisImportacao.obterListaTelefonesVO(json.getTelefones(), configExcelImportacaoTO.getPadraoDDD()));
            pessoaVO.setEmailVOs(uteisImportacao.obterListaEmailVO(json.getEmails()));

            //incluir pessoa
            Pessoa pessoaDAO = new Pessoa(con);
            pessoaDAO.incluirPessoaImportacao(pessoaVO);
            pessoaDAO = null;

            FornecedorVO fornecedorVO = new FornecedorVO();
            fornecedorVO.setNomeFantasia(json.getNome());
            fornecedorVO.setCnpj(json.getCpf_cnpj_SomenteNumeros());
            fornecedorVO.setContato(json.getNomeContato());
            fornecedorVO.setObservacao(json.getObservacao());
            fornecedorVO.setEmpresaVO(empresaVO);
            fornecedorVO.setPessoa(pessoaVO);

            fornecedorVO.setInscricaoEstadual(json.getIncricaoEstadual());
            fornecedorVO.setInscricaoMunicipal(json.getIncricaoMunicipal());
            fornecedorVO.setDataValidade(json.getDataDeValidade());
            fornecedorVO.setDataCadastro(json.getDataDoCadastro());

            fornecedorVO.setRazaoSocial(json.getRazaoSocial());
            fornecedorVO.setCnae(json.getCnae());
            fornecedorVO.setCodigoFpas(json.getCodFpas());
            fornecedorVO.setGrauRiscoNr4(json.getGrauDeRiscoNr4());
            fornecedorVO.setGrauRiscoInss(json.getGrauDeRiscoInss());
            fornecedorVO.setSindicato(json.getSindicato());
            fornecedorVO.setNrTotalFuncionarios(json.getNumeroTotalDeFuncionarios());
            fornecedorVO.setPorteEmpresa(json.getPorteDaEmpresa());
            Fornecedor fornecedorDAO = new Fornecedor(con);
            fornecedorDAO.incluir(fornecedorVO);
            fornecedorDAO = null;

            if (UteisValidacao.emptyNumber(fornecedorVO.getCodigo())) {
                throw new Exception("Erro ao criar fornecedor.");
            }

            SuperFacadeJDBC.executarUpdate("update fornecedor set idExterno = '" + json.getIdExterno() + "' where codigo = " + fornecedorVO.getCodigo(), con);

            Log logDao = new Log(con);
            logDao.incluirLogItemImportacao("FORNECEDOR", fornecedorVO.getCodigo(), cache.getUsuarioVOImportacao(), pessoaVO.getCodigo());
            logDao = null;

            if (!UteisValidacao.emptyNumber(fornecedorVO.getCodigo())) {
                json.setSucesso(true);
                json.setCodigo(fornecedorVO.getCodigo());
                json.setMsgRetorno("Fornecedor importado com sucesso.");
            } else {
                throw new Exception("Fornecedor não importado.");
            }

            con.commit();
            return fornecedorVO;
        } catch (Exception ex) {
            json.setSucesso(false);
            json.setCodigo(null);
            json.setMsgRetorno(ex.getMessage());
            ex.printStackTrace();
            con.rollback();
            throw ex;
        } finally {
            con.setAutoCommit(true);
        }
    }

    public Connection getCon() {
        return con;
    }

    public void setCon(Connection con) {
        this.con = con;
    }

    public ImportacaoConfigTO getConfigTO() {
        return configTO;
    }

    public void setConfigTO(ImportacaoConfigTO configTO) {
        this.configTO = configTO;
    }

    public static FornecedorVO fornecedor(String nome, Integer empresa, Connection con) throws Exception{
        Pessoa pessoaDao = new Pessoa(con);
        Fornecedor fornecedorDao = new Fornecedor(con);

        FornecedorVO fornecedor = new FornecedorVO();
        fornecedor.setPessoa(new PessoaVO());
        if(nome.length() > 80) {
            nome = nome.substring(0,80);
        }
        fornecedor.getPessoa().setNome(nome);
        pessoaDao.incluir(fornecedor.getPessoa());

        fornecedor.setEmpresaVO(new EmpresaVO());
        fornecedor.getEmpresaVO().setCodigo(empresa);
        fornecedorDao.incluir(fornecedor);

        SuperFacadeJDBC.executarConsulta("UPDATE fornecedor SET importado = TRUE WHERE codigo = "+fornecedor.getCodigo(), con);
        System.out.println("Gravei fornecedor "+nome);
        return fornecedor;
    }

    public static Map<String, FornecedorVO> obterFornecedoresExterno(List<XSSFRow> linhasFornecedores, Integer empresa, Connection con) throws Exception {
        Map<String, FornecedorVO> mapaFornecedor = new HashMap<>();

        try {
            SuperFacadeJDBC.executarConsulta("ALTER TABLE fornecedor ADD COLUMN importado BOOLEAN DEFAULT FALSE;", con);
        }catch(Exception e){
        }

        for(XSSFRow linha : linhasFornecedores){
            String fornecedorId = LeitorExcel2010.obterStringDoNumero(linha, 0);
            String fornecedorNome = LeitorExcel2010.obterString(linha, 2);
            mapaFornecedor.put(fornecedorId, consultarFornecedor(fornecedorNome, empresa, con));
        }
        return mapaFornecedor;
    }

    public static FornecedorVO consultarFornecedor(String nome, Integer empresa, Connection con) throws Exception{
        StringBuilder sql = new StringBuilder();
        sql.append(" SELECT * FROM fornecedor f ");
        sql.append(" INNER JOIN pessoa p ON f.pessoa = p.codigo ");
        sql.append(" where p.nome ilike ? ");
        PreparedStatement stm = con.prepareStatement(sql.toString());
        stm.setString(1 , nome.trim());
        ResultSet rs = stm.executeQuery();
        if(rs.next()){
            Fornecedor fornecedorDao = new Fornecedor(con);
            System.out.println("Achei fornecedor "+nome.trim());
            return fornecedorDao.montarDados(rs, Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA);
        }else{
            return fornecedor(nome,empresa, con);
        }
    }
}

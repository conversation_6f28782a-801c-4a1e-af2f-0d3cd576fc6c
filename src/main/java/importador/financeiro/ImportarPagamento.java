package importador.financeiro;

import negocio.comuns.contrato.MovProdutoVO;
import negocio.comuns.financeiro.CartaoCreditoVO;
import negocio.comuns.financeiro.MovPagamentoVO;
import negocio.comuns.financeiro.PagamentoMovParcelaVO;
import negocio.comuns.financeiro.ReciboPagamentoVO;
import negocio.comuns.utilitarias.Uteis;
import negocio.facade.jdbc.basico.ReciboClienteConsultor;
import negocio.facade.jdbc.contrato.MovProduto;
import negocio.facade.jdbc.financeiro.CartaoCredito;
import negocio.facade.jdbc.financeiro.Cheque;
import negocio.facade.jdbc.financeiro.MovPagamento;
import negocio.facade.jdbc.financeiro.MovParcela;
import negocio.facade.jdbc.financeiro.ReciboPagamento;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.util.List;

public class ImportarPagamento {

    private Connection con;
    private MovParcela movParcelaDAO;
    private MovProduto movProdutoDAO;
    private MovPagamento movPagamentoDAO;
    private ReciboPagamento reciboPagamentoDAO;
    private ReciboClienteConsultor reciboClienteConsultorDAO;
    private Cheque chequeDAO;
    private CartaoCredito cartaoCreditoDAO;

    public ImportarPagamento(Connection con) throws Exception {
        this.con = con;
        this.movParcelaDAO = new MovParcela(con);
        this.movProdutoDAO = new MovProduto(con);
        this.movPagamentoDAO = new MovPagamento(con);
        this.reciboPagamentoDAO = new ReciboPagamento(con);
        this.reciboClienteConsultorDAO = new ReciboClienteConsultor(con);
        this.chequeDAO = new Cheque(con);
        this.cartaoCreditoDAO = new CartaoCredito(con);
    }

    public void incluirPagamento(MovPagamentoVO movPagamentoVO, Integer codigoContrato) throws Exception {
        ReciboPagamentoVO reciboPagamentoVO = gerarRecibo(codigoContrato, movPagamentoVO);
        movPagamentoVO.setReciboPagamento(reciboPagamentoVO);

        movPagamentoVO.setMovPagamentoEscolhida(true);
        incluirMovPagamento(movPagamentoVO);

        movPagamentoDAO.setarProdutosPagos(reciboPagamentoVO.getCodigo());
        reciboClienteConsultorDAO.incluirComRecibo(reciboPagamentoVO);
    }

    private ReciboPagamentoVO gerarRecibo(Integer codigoContrato, MovPagamentoVO movPagamentoVO) throws Exception {
        ReciboPagamentoVO recibo = new ReciboPagamentoVO();
        recibo.getContrato().setCodigo(codigoContrato);
        recibo.setData(movPagamentoVO.getDataLancamento());
        recibo.setNomePessoaPagador(movPagamentoVO.getPessoa().getNome());
        recibo.setPessoaPagador(movPagamentoVO.getPessoa());
        recibo.setResponsavelLancamento(movPagamentoVO.getResponsavelPagamento());
        recibo.setValorTotal(movPagamentoVO.getValorTotal());
        recibo.setEmpresa(movPagamentoVO.getEmpresa());
        reciboPagamentoDAO.incluir(recibo);
        return recibo;
    }

    public void incluirMovPagamento(MovPagamentoVO obj) throws Exception {
        try {
            obj.realizarUpperCaseDados();
            String sql = "INSERT INTO MovPagamento( pessoa, dataPagamento, dataLancamento, "
                    + "valor, formaPagamento, nomePagador, operadoraCartao, nrParcelaCartaoCredito, "
                    + "movPagamentoEscolhida, responsavelPagamento, reciboPagamento, "
                    + "dataquitacao, convenioCobranca, empresa, valortotal, autorizacaocartao, adquirente, nsu) "
                    + "VALUES ( ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)";
            PreparedStatement sqlInserir = con.prepareStatement(sql);

            int i = 0;
            if (obj.getPessoa().getCodigo().intValue() != 0) {
                sqlInserir.setInt(++i, obj.getPessoa().getCodigo().intValue());
            } else {
                sqlInserir.setNull(++i, 0);
            }

            sqlInserir.setTimestamp(++i, Uteis.getDataJDBCTimestamp(obj.getDataPagamento()));
            sqlInserir.setTimestamp(++i, Uteis.getDataJDBCTimestamp(obj.getDataLancamento()));
            sqlInserir.setDouble(++i, obj.getValor());
            if (obj.getFormaPagamento().getCodigo().intValue() != 0) {
                sqlInserir.setInt(++i, obj.getFormaPagamento().getCodigo().intValue());
            } else {
                sqlInserir.setNull(++i, 0);
            }

            sqlInserir.setString(++i, obj.getNomePagador());
            if (obj.getOperadoraCartaoVO().getCodigo().intValue() != 0) {
                sqlInserir.setInt(++i, obj.getOperadoraCartaoVO().getCodigo());
            } else {
                sqlInserir.setNull(++i, 0);
            }
            sqlInserir.setInt(++i, obj.getNrParcelaCartaoCredito());
            sqlInserir.setBoolean(++i, obj.getMovPagamentoEscolhida());
            if (obj.getResponsavelPagamento().getCodigo().intValue() != 0) {
                sqlInserir.setInt(++i, obj.getResponsavelPagamento().getCodigo().intValue());
            } else {
                sqlInserir.setNull(++i, 0);
            }

            if (obj.getReciboPagamento().getCodigo().intValue() != 0) {
                sqlInserir.setInt(++i, obj.getReciboPagamento().getCodigo().intValue());
            } else {
                sqlInserir.setNull(++i, 0);
            }
            sqlInserir.setDate(++i, Uteis.getDataJDBC(obj.getDataQuitacao()));

            if (obj.getConvenio().getCodigo().intValue() != 0) {
                sqlInserir.setInt(++i, obj.getConvenio().getCodigo().intValue());
            } else {
                sqlInserir.setNull(++i, 0);
            }
            sqlInserir.setInt(++i, obj.getEmpresa().getCodigo());
            sqlInserir.setDouble(++i, obj.getValorTotal());
            sqlInserir.setString(++i, obj.getAutorizacaoCartao());
            if (obj.getAdquirenteVO().getCodigo().intValue() != 0) {
                sqlInserir.setInt(++i, obj.getAdquirenteVO().getCodigo());
            } else {
                sqlInserir.setNull(++i, 0);
            }
            sqlInserir.setString(++i, obj.getNsu());
            sqlInserir.execute();
            obj.setCodigo(movPagamentoDAO.obterValorChavePrimariaCodigo());
            obj.setNovoObj(new Boolean(false));

            if (obj.getFormaPagamento().getTipoFormaPagamento().equals("CH")) {
                chequeDAO.incluirPagamentoCheques(obj.getCodigo(), obj.getChequeVOs());
            }
            if (obj.getFormaPagamento().getTipoFormaPagamento().equals("CA")) {
                for (int j = 0; j < obj.getCartaoCreditoVOs().size(); j++) {
                    CartaoCreditoVO cartao = obj.getCartaoCreditoVOs().get(j);
                    cartao.setMovpagamento(obj);
                }
                cartaoCreditoDAO.incluirCartaoCreditos(obj.getCodigo(), obj.getCartaoCreditoVOs());
            }

            for (PagamentoMovParcelaVO pmp : obj.getPagamentoMovParcelaVOs()) {
                pmp.setMovPagamento(obj.getCodigo());
                pmp.setReciboPagamento(obj.getReciboPagamento());
                incluirPagamentoMovParcela(pmp);
            }
        } catch (Exception e) {
            obj.setNovoObj(new Boolean(true));
            throw e;
        }
    }

    private void incluirPagamentoMovParcela(PagamentoMovParcelaVO pmp) throws Exception {
        String sql = "INSERT INTO PagamentoMovParcela( movPagamento, movParcela, valorPago, reciboPagamento ) VALUES ( ?, ?, ?, ? )";
        try (PreparedStatement sqlInserir = con.prepareStatement(sql)) {
            if (pmp.getMovPagamento() != 0) {
                sqlInserir.setInt(1, pmp.getMovPagamento());
            } else {
                sqlInserir.setNull(1, 0);
            }
            if (pmp.getMovParcela().getCodigo() != 0) {
                sqlInserir.setInt(2, pmp.getMovParcela().getCodigo());
            } else {
                sqlInserir.setNull(2, 0);
            }
            sqlInserir.setDouble(3, pmp.getValorPago());
            if (pmp.getReciboPagamento().getCodigo() != 0) {
                sqlInserir.setInt(4, pmp.getReciboPagamento().getCodigo());
            } else {
                sqlInserir.setNull(4, 0);
            }
            sqlInserir.execute();

            if (pmp.getMovParcela().getSituacao().equals("PG")) {
                movParcelaDAO.alterarSomenteSituacaoSemCommit(pmp.getMovParcela());
                List<MovProdutoVO> movProdutoParcelaVOS = movProdutoDAO.consultarPorCodigoParcela(pmp.getMovParcela().getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS);
                movProdutoParcelaVOS.forEach(mpp -> {
                    try {
                        movProdutoDAO.alterarSomenteSituacaoSemCommit(mpp.getCodigo(), "PG");
                    } catch (Exception e) {
                        throw new RuntimeException(e);
                    }
                });
            }
        }
    }
}

package importador.alunosTurma;


import br.com.pactosolucoes.ce.comuns.enumerador.DiaSemana;
import importador.ImportacaoCache;
import importador.ImportacaoConfigTO;
import importador.UteisImportacao;
import importador.json.TurmaImportacaoJSON;
import negocio.comuns.basico.ColaboradorVO;
import negocio.comuns.plano.AmbienteVO;
import negocio.comuns.plano.HorarioTurmaVO;
import negocio.comuns.plano.ModalidadeVO;
import negocio.comuns.plano.NivelTurmaVO;
import negocio.comuns.plano.TurmaVO;
import negocio.comuns.utilitarias.ConsistirException;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisValidacao;
import negocio.facade.jdbc.arquitetura.Usuario;
import negocio.facade.jdbc.basico.Colaborador;
import negocio.facade.jdbc.plano.HorarioTurma;
import negocio.facade.jdbc.plano.Modalidade;
import negocio.facade.jdbc.plano.Turma;
import negocio.facade.jdbc.utilitarias.Conexao;

import java.sql.Connection;

public class ImportarTurma {

    private Connection con;
    private Turma turmaDAO;
    private HorarioTurma horarioTurmaDAO;
    private Usuario usuarioDAO;
    private Colaborador colaboradorDAO;
    ImportacaoCache importacaoCache;

    public ImportarTurma(Connection con, ImportacaoConfigTO configTO) throws Exception {
        Conexao.guardarConexaoForJ2SE(con);
        this.con = con;
        this.turmaDAO = new Turma(con);
        this.horarioTurmaDAO = new HorarioTurma(con);
        this.usuarioDAO = new Usuario(con);
        this.colaboradorDAO = new Colaborador(con);
        if (UteisValidacao.emptyNumber(configTO.getUsuarioResponsavelImportacao())) {
            configTO.setUsuarioResponsavelImportacao(usuarioDAO.consultarPorUsername("pactobr", Uteis.NIVELMONTARDADOS_DADOSBASICOS).getCodigo());
        }
        this.importacaoCache = new ImportacaoCache(con, configTO.getUsuarioResponsavelImportacao());
    }

    public void importarTurma(TurmaImportacaoJSON turmaImportacaoJSON) throws Exception {
        TurmaVO turmaVO = turmaDAO.consultarPorIdexterno(turmaImportacaoJSON.getIdExterno().toString(), Uteis.NIVELMONTARDADOS_TODOS);
        if (turmaVO != null && !UteisValidacao.emptyNumber(turmaVO.getCodigo())) {
            return;
        }

        turmaVO = new TurmaVO();
        turmaVO.getEmpresa().setCodigo(turmaImportacaoJSON.getCodigoEmpresa());
        turmaVO.setIdExterno(turmaImportacaoJSON.getIdExterno().toString());
        turmaVO.setDescricao(turmaImportacaoJSON.getTurma());
        turmaVO.setIdentificador(turmaVO.getDescricao());

        ModalidadeVO modalidadeVO = UteisImportacao.obterModalidade(turmaVO.getEmpresa().getCodigo(), turmaImportacaoJSON.getModalidade(), true, con);

        turmaVO.setModalidade(modalidadeVO);
        turmaVO.setDataInicialVigencia(turmaImportacaoJSON.getVigenciaDe());
        turmaVO.setDataFinalVigencia(turmaImportacaoJSON.getVigenciaAte());
        turmaVO.setIdadeMinima(turmaImportacaoJSON.getIdadeMinima());
        turmaVO.setIdadeMinimaMeses(turmaImportacaoJSON.getIdadeMinimaMeses());
        turmaVO.setIdadeMaxima(turmaImportacaoJSON.getIdadeMaxima());
        turmaVO.setIdadeMaximaMeses(turmaImportacaoJSON.getIdadeMaximaMeses());
        turmaVO.setProfessor(turmaImportacaoJSON.getCodigoProfessor());
        turmaVO.setCapacidade(turmaImportacaoJSON.getCapacidade());

        if (UteisValidacao.emptyNumber(turmaVO.getIdadeMaxima())) {
            turmaVO.setIdadeMaxima(100);
        }

        turmaDAO.incluir(turmaVO);

        HorarioTurmaVO horarioTurmaVO = new HorarioTurmaVO();
        horarioTurmaVO.setIdentificadorTurma(turmaImportacaoJSON.getTurma());
        horarioTurmaVO.setTurma(turmaVO.getCodigo());
        horarioTurmaVO.setToleranciaEntradaMinutos(turmaImportacaoJSON.getTolerancia());
        horarioTurmaVO.setHoraInicial(turmaImportacaoJSON.getHoraInicio());
        horarioTurmaVO.setHoraFinal(turmaImportacaoJSON.getHoraFim());
        horarioTurmaVO.setNrMaximoAluno(turmaImportacaoJSON.getCapacidade());

        AmbienteVO ambienteVO = UteisImportacao.obterAmbiente(turmaImportacaoJSON.getAmbiente(), con);
        if (ambienteVO == null) {
            throw new ConsistirException("Falha ao obter ambiente! " + turmaImportacaoJSON.getAmbiente());
        }
        horarioTurmaVO.setAmbiente(ambienteVO);

        ColaboradorVO colaboradorVO =  colaboradorDAO.consultarPorCodigo(turmaImportacaoJSON.getCodigoProfessor().intValue(), turmaImportacaoJSON.getCodigoEmpresa(), Uteis.NIVELMONTARDADOS_TODOS);
        if (colaboradorVO == null) {
            colaboradorVO = importacaoCache.obterColaboradorPactoBREmpresa(turmaImportacaoJSON.getCodigoEmpresa());
        }

        horarioTurmaVO.setProfessor(colaboradorVO);

        NivelTurmaVO nivelTurmaVO = UteisImportacao.obterNivel(turmaImportacaoJSON.getNivel(), con);
        horarioTurmaVO.setNivelTurma(nivelTurmaVO);

        for (DiaSemana dia: turmaImportacaoJSON.getDiasSemana()) {
            horarioTurmaVO.setDiaSemanaNumero(dia.getNumeral());
            horarioTurmaVO.setDiaSemana(dia.getCodigo());
            horarioTurmaDAO.incluir(horarioTurmaVO);
        }
    }
}

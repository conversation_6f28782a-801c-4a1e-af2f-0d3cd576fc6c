package annotations.arquitetura;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

@Retention(RetentionPolicy.RUNTIME)

//Qual elemento sofrerá anotação, no nosso caso FIELD.
//Outros valores seriam: TYPE (class e interface), METHOD, PARAMETER, CONSTRUCTOR, LOCAL_VARIABLE e PACKAGE.
@Target(ElementType.FIELD)
public @interface PassWord {
}

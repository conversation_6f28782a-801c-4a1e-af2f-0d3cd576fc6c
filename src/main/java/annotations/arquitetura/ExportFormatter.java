package annotations.arquitetura;

import servicos.bi.exportador.formatadores.FormatadorEnum;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * Created by GlaucoT on 22/02/2017
 */
@Retention(RetentionPolicy.RUNTIME)
@Target({ElementType.FIELD, ElementType.METHOD})
public @interface ExportFormatter {
    FormatadorEnum formato();
}

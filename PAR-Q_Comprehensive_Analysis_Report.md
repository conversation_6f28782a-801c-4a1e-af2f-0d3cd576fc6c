## Relatório de Análise Abrangente do PAR-Q (Questionário de Prontidão para Atividade Física)

### Sumário Executivo

Este relatório fornece uma análise abrangente da funcionalidade do PAR-Q (Questionário de Prontidão para Atividade Física) no código-fonte do projeto de treinamento. O sistema PAR-Q é um componente crucial para avaliar a prontidão do cliente para atividade física e garantir a conformidade legal com as regulamentações de saúde e segurança.

#### Principais Conclusões:

- **Fluxo de Trabalho Digital Completo**: Processo PAR-Q digital de ponta a ponta, desde o preenchimento do questionário até a assinatura digital.
- **Integração com o Sistema de Treinamento**: Integração perfeita com o sistema de treinamento externo para o processamento do PAR-Q.
- **Conformidade Legal**: Suporte integrado para as leis estaduais brasileiras (RJ e GO) sobre prontidão para atividade física.
- **Suporte Multicanal**: Funcionalidade PAR-Q disponível por meio da interface web e integração com vendas online.
- **Modelo de Dados Abrangente**: Esquema de banco de dados bem estruturado que suporta relações complexas de questionários.

-----

### 1\. Visão Geral da Arquitetura

O sistema PAR-Q segue um padrão de arquitetura em camadas com clara separação de responsabilidades:

#### 1.1 Camadas do Sistema

- **Camada de Frontend**: Interface baseada na web com recursos de assinatura digital.
- **Camada de API**: Endpoints RESTful e serviços web para operações do PAR-Q.
- **Camada de Lógica de Negócio**: Serviços centrais para gerenciamento e validação de questionários.
- **Camada de Modelo de Dados**: Objetos de valor que representam as entidades do PAR-Q.
- **Camada de Banco de Dados**: Armazenamento persistente com modelo de dados relacional.
- **Camada de Integração Externa**: Integração com o sistema de treinamento e com terceiros.

#### 1.2 Componentes Principais

- **Canvas de Assinatura Digital**: Captura de assinatura baseada em HTML5.
- **Integração com o Sistema de Treinamento**: Chamadas de API externas para o processamento do PAR-Q.
- **Módulo de Conformidade Legal**: Suporte para regulamentações específicas de cada estado.
- **Motor de Fluxo de Trabalho**: Gerenciamento completo do ciclo de vida do PAR-Q.

-----

### 2\. Modelos e Entidades de Dados

#### 2.1 Modelos de Dados Principais

##### QuestionarioClienteVO

- **Finalidade**: Representa um questionário PAR-Q preenchido para um cliente.
- **Propriedades Principais**:
  - `codigo`: Chave primária de identificação.
  - `cliente`: Referência a ClienteVO.
  - `questionario`: Referência a QuestionarioVO.
  - `consultor`: Referência a ColaboradorVO (consultor).
  - `data`: Data de preenchimento do questionário.
  - `observacao`: Observações adicionais.
  - `evento`: Tipo de evento.
  - `tipoBV`: Enumeração do tipo de BV.
  - `origemSistema`: Enumeração da origem do sistema.

##### PerguntaClienteVO

- **Finalidade**: Representa perguntas individuais em questionários PAR-Q.
- **Propriedades Principais**:
  - `codigo`: Chave primária de identificação.
  - `descricao`: Texto/descrição da pergunta.
  - `tipoPergunta`: Tipo de pergunta.
  - `multipla`: Booleano para perguntas de múltipla escolha.
  - `simples`: Booleano para perguntas simples.
  - `textual`: Booleano para perguntas baseadas em texto.
  - `respostaPergClienteVOs`: Coleção de respostas possíveis.

##### RespostaPergClienteVO

- **Finalidade**: Representa possíveis respostas a perguntas do PAR-Q.
- **Propriedades Principais**:
  - `codigo`: Chave primária de identificação.
  - `perguntaCliente`: Referência à pergunta.
  - `marcado`: Indicador de seleção.
  - `descricaoRespota`: Descrição da resposta.
  - `respostaOpcao`: Opção de resposta booleana.

##### QuestionarioPerguntaClienteVO

- **Finalidade**: Entidade de junção que conecta questionários a perguntas.
- **Propriedades Principais**:
  - `codigo`: Chave primária de identificação.
  - `questionarioCliente`: Referência ao questionário.
  - `perguntaCliente`: Referência à pergunta.

#### 2.2 Integração com o Cliente

- **ClienteVO.parqPositivo**: Sinalizador booleano que indica se o cliente tem respostas PAR-Q positivas.
- **EmpresaVO.diasParaVencimentoParq**: Configuração para os dias de vencimento do PAR-Q.

-----

### 3\. Esquema do Banco de Dados

#### 3.1 Tabelas Principais

##### Tabela QuestionarioCliente

```sql
CREATE TABLE QuestionarioCliente (
    codigo SERIAL PRIMARY KEY,
    questionario INTEGER REFERENCES Questionario(codigo),
    cliente INTEGER REFERENCES Cliente(codigo),
    consultor INTEGER REFERENCES Colaborador(codigo),
    data TIMESTAMP,
    observacao TEXT,
    evento VARCHAR(255),
    tipobv VARCHAR(10),
    ultimaAtualizacao TIMESTAMP,
    origemSistema INTEGER
);
```

##### Tabela PerguntaCliente

```sql
CREATE TABLE PerguntaCliente (
    codigo SERIAL PRIMARY KEY,
    descricao TEXT NOT NULL,
    tipoPergunta VARCHAR(50),
    multipla BOOLEAN DEFAULT FALSE,
    simples BOOLEAN DEFAULT FALSE,
    textual BOOLEAN DEFAULT FALSE
);
```

##### Tabela RespostaPergCliente

```sql
CREATE TABLE RespostaPergCliente (
    codigo SERIAL PRIMARY KEY,
    perguntaCliente INTEGER REFERENCES PerguntaCliente(codigo),
    marcado INTEGER,
    descricaoRespota TEXT,
    respostaOpcao BOOLEAN
);
```

##### Tabela QuestionarioPerguntaCliente

```sql
CREATE TABLE QuestionarioPerguntaCliente (
    codigo SERIAL PRIMARY KEY,
    questionarioCliente INTEGER REFERENCES QuestionarioCliente(codigo),
    perguntaCliente INTEGER REFERENCES PerguntaCliente(codigo)
);
```

#### 3.2 Relacionamentos

- **Um-para-Muitos**: QuestionarioCliente → QuestionarioPerguntaCliente
- **Um-para-Muitos**: PerguntaCliente → QuestionarioPerguntaCliente
- **Um-para-Muitos**: PerguntaCliente → RespostaPergCliente
- **Muitos-para-Um**: QuestionarioCliente → Cliente
- **Muitos-para-Um**: QuestionarioCliente → Questionario

-----

### 4\. Endpoints e Serviços de API

#### 4.1 Endpoints REST (ContratoAssinaturaDigitalServletControle)

##### Operações Principais do PAR-Q

- **consultarClientesParQ**: Recuperar clientes que precisam preencher o PAR-Q.
- **consultarPerguntasParQ**: Obter perguntas do PAR-Q para uma resposta específica.
- **salvarAssinaturaParQ**: Salvar novas respostas do PAR-Q com assinatura digital.
- **salvarEdicaoAssinaturaParQ**: Atualizar respostas existentes do PAR-Q.
- **visualizarRespostasParQ**: Visualizar respostas do PAR-Q preenchidas.
- **removerAssinaturaParQ**: Remover assinatura do PAR-Q (com permissões).
- **verificaConfigSescEAtualizaMensagemParq**: Verificar a configuração do SESC para mensagens do PAR-Q.

#### 4.2 Serviços Web (IntegracaoCadastrosWS)

- **responderQuestionario**: Preencher questionário via serviço web.
- **consultarParcelasVencidas**: Operações financeiras relacionadas.

#### 4.3 Integração com o Sistema de Treinamento

- **URL Base**: `{urlTreino}/prest/avaliacao/{key}/`
- **Endpoints Chave**:
  - `obterPerguntasParQ`: Obter perguntas do PAR-Q.
  - `salvarRespostasParQ`: Salvar respostas do PAR-Q.
  - `salvarEdicaoRespostasParQ`: Editar respostas do PAR-Q.
  - `removerAssinaturaParQ`: Remover assinatura do PAR-Q.
  - `is-parq-aluno-assinado`: Verificar se o aluno assinou o PAR-Q.
  - `responderParq`: Enviar respostas do PAR-Q.

-----

### 5\. Componentes de Frontend

#### 5.1 Interface do Usuário (contratos.html)

- **Itens de Menu do PAR-Q**: Elementos de navegação para a funcionalidade do PAR-Q.
- **Canvas de Assinatura Digital**: Canvas HTML5 para a captura de assinatura.
- **Elementos de Formulário**: Exibição de perguntas e coleta de respostas.
- **Exibição de Texto Legal**: Requisitos legais específicos de cada estado (leis RJ/GO).
- **Caixas de Diálogo (Modais)**: Confirmação e tratamento de erros.

#### 5.2 Controlador JavaScript (assinaturas\_v56.js)

- **Funções Principais**:
  - `assinarParQ()`: Iniciar o processo de assinatura do PAR-Q.
  - `selecionarPessoaParQ()`: Selecionar cliente para o preenchimento do PAR-Q.
  - `montarHtmlParQ()`: Construir a interface do PAR-Q.
  - `salvarRespostasParQAluno()`: Salvar respostas do PAR-Q.
  - `visualizarRespostasParQ()`: Exibir respostas preenchidas.
  - `filtrarClientesParQ()`: Filtrar clientes por status do PAR-Q.

#### 5.3 Integração de Assinatura Digital

- **Gerenciamento de Canvas**: `construirCanvasParQ()`, `ajustarCanvasAssinaturaParQ()`.
- **Validação de Assinatura**: Garante que a assinatura seja fornecida antes do envio.
- **Processamento de Imagem**: Converte a assinatura para base64 para armazenamento.

-----

### 6\. Lógica de Negócio e Fluxos de Trabalho

#### 6.1 Fluxo de Trabalho de Preenchimento do PAR-Q

1.  **Seleção do Cliente**: O usuário seleciona o cliente que precisa do PAR-Q.
2.  **Recuperação das Perguntas**: O sistema busca as perguntas do PAR-Q no sistema de treinamento.
3.  **Coleta de Respostas**: O cliente responde a todas as perguntas obrigatórias.
4.  **Validação**: O sistema valida se todas as perguntas foram respondidas.
5.  **Reconhecimento Legal**: O cliente reconhece os termos de responsabilidade.
6.  **Assinatura Digital**: O cliente fornece a assinatura digital.
7.  **Envio**: As respostas e a assinatura são enviadas para o sistema de treinamento.
8.  **Atualização de Status**: O status do PAR-Q do cliente é atualizado no sistema local.

#### 6.2 Regras de Validação

- **Respostas Completas**: Todas as perguntas devem ser respondidas.
- **Assinatura Digital**: Necessária para o envio do formulário.
- **Reconhecimento Legal**: Os termos de responsabilidade devem ser aceitos.
- **Tratamento de Respostas Positivas**: Processamento especial para clientes com respostas positivas.

#### 6.3 Gerenciamento de Estado

- **Não Assinado**: Clientes que precisam preencher o PAR-Q.
- **PAR-Q Positivo**: Clientes com respostas positivas que exigem liberação médica.
- **Assinado**: Clientes com PAR-Q preenchido.
- **Vencido**: Clientes com PAR-Q vencido (com base na configuração da empresa).

-----

### 7\. Pontos de Integração

#### 7.1 Integração com o Sistema de Treinamento

- **Integração Principal**: O sistema de treinamento externo lida com o processamento do PAR-Q.
- **Comunicação por API**: Chamadas de API RESTful para todas as operações do PAR-Q.
- **Sincronização de Dados**: O sistema local mantém sinalizadores de status do PAR-Q.
- **Tratamento de Erros**: Degradação elegante quando o sistema de treinamento não está disponível.

#### 7.2 Integração com Vendas Online (VendasOnlineService)

- **Envio do PAR-Q**: Envio automático do PAR-Q durante vendas online.
- **Processamento de Respostas**: Lida com respostas do PAR-Q do fluxo de vendas online.
- **Registro de Erros**: Registro de erros abrangente para problemas de integração.

#### 7.3 Integração com o CadSis

- **Objetos de Transferência de Dados (DTOs)**: DTOs especializados para integração com sistemas externos.
- **Mapeamento de Perguntas**: `IntegracaoBVPerguntaCliente` para dados de perguntas.
- **Mapeamento de Respostas**: `IntegracaoBVRespostaPerguntaCliente` para dados de respostas.

#### 7.4 Integração com o Controle de Acesso

- **Bloqueio por PAR-Q**: O sistema pode bloquear o acesso se o PAR-Q não estiver preenchido.
- **Validação de Permissões**: Acesso baseado em funções para operações do PAR-Q.
- **Registro de Auditoria**: Trilha de auditoria completa para operações do PAR-Q.

-----

### 8\. Recursos de Conformidade Legal

#### 8.1 Suporte a Leis Estaduais Brasileiras

- **Rio de Janeiro (RJ)**: Lei Nº 6765 de 05 de Maio de 2014.
- **Goiás (GO)**: Lei N 20.630, de 08 de Novembro de 2019.
- **Texto Legal Dinâmico**: Texto legal configurável com base na localização.

#### 8.2 Requisitos de Conformidade

- **PAR-Q Obrigatório**: Necessário para toda participação em atividade física.
- **Liberação Médica**: Obrigatória para respostas positivas no PAR-Q.
- **Assinaturas Digitais**: Captura de assinatura digital legalmente vinculante.
- **Retenção de Registros**: Armazenamento permanente de respostas e assinaturas do PAR-Q.

-----

### 9\. Configuração e Personalização

#### 9.1 Configuração em Nível de Empresa

- **Vencimento do PAR-Q**: `diasParaVencimentoParq` em EmpresaVO.
- **Mensagens Legais**: Configuração de mensagens específicas do SESC.
- **Conjuntos de Perguntas**: Modelos de questionário configuráveis.

#### 9.2 Configuração do Sistema

- **URL do Sistema de Treinamento**: Endpoint de sistema externo configurável.
- **Texto Legal**: Texto de requisito legal específico de cada estado.
- **Regras de Validação**: Parâmetros de validação configuráveis.

-----

### 10\. Recomendações e Observações

#### 10.1 Pontos Fortes

- **Abrangência Completa**: Gerenciamento do ciclo de vida completo do PAR-Q.
- **Conformidade Legal**: Suporte integrado para requisitos regulatórios.
- **Arquitetura de Integração**: Integração bem projetada com sistemas externos.
- **Experiência do Usuário**: Interface web intuitiva com assinaturas digitais.

#### 10.2 Áreas de Melhoria

- **Tratamento de Erros**: Tratamento de erros aprimorado para falhas no sistema de treinamento.
- **Desempenho**: Otimização potencial para processamento de PAR-Q em larga escala.
- **Suporte Móvel**: Considerar uma interface PAR-Q otimizada para dispositivos móveis.
- **Relatórios**: Recursos de relatórios aprimorados para análise do PAR-Q.

#### 10.3 Dívida Técnica

- **Documentação do Código**: Algumas áreas poderiam se beneficiar de documentação adicional.
- **Cobertura de Testes**: Estratégia de teste abrangente para fluxos de trabalho do PAR-Q.
- **Versionamento de API**: Considerar uma estratégia de versionamento para a API do sistema de treinamento.

-----

### 11\. Conclusão

O sistema PAR-Q representa uma solução abrangente e bem arquitetada para o gerenciamento de questionários de prontidão para atividade física. O sistema aborda com sucesso os requisitos de conformidade legal, ao mesmo tempo em que oferece uma experiência digital amigável ao usuário. A integração com sistemas de treinamento externos demonstra uma abordagem madura para a arquitetura do sistema e o gerenciamento de dados.

A implementação mostra uma forte aderência às melhores práticas de engenharia de software com clara separação de responsabilidades, modelagem de dados abrangente e padrões de integração robustos. O sistema está bem posicionado para apoiar as operações de fitness e bem-estar da organização, mantendo a conformidade com as regulamentações aplicáveis.

-----

*Relatório Gerado: 08/08/2025* *Escopo da Análise: Apenas Componentes PAR-Q do Projeto de Treinamento* *Versão do Código-fonte: Análise do Estado Atual*